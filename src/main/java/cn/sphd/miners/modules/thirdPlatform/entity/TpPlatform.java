package cn.sphd.miners.modules.thirdPlatform.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table( name ="t_tp_platform" )
public class TpPlatform extends BaseEntity {

    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;//`id` INTEGER NOT NULL AUTO_INCREMENT COMMENT 'ID',
    @Column(length = 50)
    private String name;//`name` VARCHAR(50) DEFAULT NULL COMMENT '名称',
    @Column(name = "tg_code")
    private Byte tgCode;//enum TgCode //`tg_code` TINYINT DEFAULT NULL COMMENT '第三方大厂(1-weChat,2-byteDance)',
    @Column(name = "app_id", length = 34)
    private String appId;//`app_id` VARCHAR(34) CHARACTER SET latin1 COLLATE 'latin1_bin' DEFAULT NULL COMMENT '第三方平台appId',
    @Column(name = "app_secret", length = 64)
    private String appSecret;//`app_secret` VARCHAR(64) CHARACTER SET latin1 COLLATE 'latin1_bin' DEFAULT NULL COMMENT '第三方平台appSecret',
    @Column(name = "verif_token", length = 128)
    private String verifToken;//`verif_token` VARCHAR(128) CHARACTER SET latin1 COLLATE 'latin1_bin' DEFAULT NULL COMMENT '消息校验Token',
    @Column(name = "ed_key", length = 64)
    private String edKey;//`ed_key` VARCHAR(64) CHARACTER SET latin1 COLLATE 'latin1_bin' DEFAULT NULL COMMENT '消息加解密Key',
    @Column
    private Boolean enabled;//`enabled` BOOLEAN DEFAULT NULL COMMENT '有效：true-受wonderss管理，更新accesstoken，false/null-不受wonderss管理，不获取accesstoken',
    @Column
    private String memo;//`memo` VARCHAR(255) DEFAULT NULL COMMENT '备注',
    @Column
    private Integer creator;//`creator` INTEGER DEFAULT NULL COMMENT '创建人userId',
    @Column(name = "create_name", length = 100)
    private String createName;//`create_name` VARCHAR(100) DEFAULT NULL COMMENT '创建人',
    @CreationTimestamp
    @Column(name = "create_time")
    private Date createTime;//`create_time` DATETIME(3) DEFAULT NULL COMMENT '创建时间',
    @Column
    private Integer updator;//`updator` INTEGER DEFAULT NULL COMMENT '修改人userId',
    @Column(name = "update_name", length = 100)
    private String updateName;//`update_name` VARCHAR(100) DEFAULT NULL COMMENT '修改人',
    @UpdateTimestamp
    @Column(name = "update_time")
    private Date updateTime;//`update_time` DATETIME(3) DEFAULT NULL COMMENT '修改时间',

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Byte getTgCode() {
        return tgCode;
    }

    public void setTgCode(Byte tgCode) {
        this.tgCode = tgCode;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getVerifToken() {
        return verifToken;
    }

    public void setVerifToken(String verifToken) {
        this.verifToken = verifToken;
    }

    public String getEdKey() {
        return edKey;
    }

    public void setEdKey(String edKey) {
        this.edKey = edKey;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
