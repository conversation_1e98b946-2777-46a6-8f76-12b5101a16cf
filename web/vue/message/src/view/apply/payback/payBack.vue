<template>
  <div id="payList">
    <TY_NavTop title="回款录入" :isBackHome="isBackHome" isSearch="true" @toggleList="screen"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="tipPane">如下{{backList.length}}笔回款尚未处理完成。</div>
      <div class="ui-cells">
        <a class="ui-cell" v-for="(item, index) in backList" :key="index" v-on:click="applyTo(item.id)">
          <div class="backApplyLists">
            <p class="company" v-text="item.customerName"></p>
            <p class="listCon">本次回款 {{item.method | incomeType}} {{item.amount}} 元</p>
            <p class="listCon">{{item.createName}} {{item.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</p>
          </div>
        </a>
      </div>
    </div>
  </div>
</template>

<script>
import { incomeType, formatDate } from '../../../js/common'
export default {
  name: 'returnInput',
  filters: {
    incomeType,
    formatDate
  },
  data () {
    return {
      loading: true,
      backList: [],
      listenersUid: []
    }
  },
  computed: {
  },
  created () {
    let _this = this
    _this.loading = true
    _this.listenersUid = [
      _this.sphdSocket.subscribe('getReceivableList', function (data) {
        let resData = JSON.parse(data)
        _this.backList = resData.list
        _this.loading = false
      }),
      _this.sphdSocket.subscribe('getReceivableList', function (data) {
        console.log('新增录入：' + data)
        let resData = JSON.parse(data)
        let update = resData.personnelReimburse
        if (resData.operate > 0) {
          _this.backList.push(update)
        } else if (resData.operate < 0) {
          _this.backList.forEach(function (item, index) {
            let deletId = update.id
            if (deletId === item.id) {
              _this.backList.splice(index, 1)
            }
          })
        }
      }, function () {
        console.log('Socket check Error:')
      }, 'user')
    ]
    _this.sphdSocket.send('getReceivableList', {
      'oid': this.sphdSocket.user.oid,
      'session': this.sphdSocket.sessionid,
      'userId': this.sphdSocket.user.userID
    })
  },
  destroyed: function () {
    let _this = this
    _this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    applyTo: function (id) {
      this.$router.push({
        path: `/payApplyDetail/${id}`
      })
    },
    screen: function () {
      let type = 1
      this.$router.push({
        path: `/payApplyScreen/${type}`
      })
    }
  }
}
</script>
