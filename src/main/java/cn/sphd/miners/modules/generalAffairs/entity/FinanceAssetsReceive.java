package cn.sphd.miners.modules.generalAffairs.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by Administrator on 2017/7/11.
 * 固定资产领用记录
 */
@Entity
@Table(name="t_finance_assets_receive")
public class FinanceAssetsReceive implements Serializable {


    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="receive_date"   , nullable=true , unique=false)
    private Date receiveDate;

    @Column(name="receive_dept"   , nullable=true , unique=false)
    private Integer receiveDept;

    @Column(name="receive_dept_name"  , length=100 , nullable=true , unique=false)
    private String receiveDeptName;

    @Column(name="receiver"   , nullable=true , unique=false)
    private Integer receiver;

    @Column(name="receiver_name"  , length=100 , nullable=true , unique=false)
    private String receiverName;

    @Column(name="return_date"   , nullable=true , unique=false)
    private Date returnDate;

    @Column(name="returner"   , nullable=true , unique=false)
    private Integer returner;

    @Column(name="returner_name"  , length=100 , nullable=true , unique=false)
    private String returnerName;

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;


    @ManyToOne(fetch= FetchType.LAZY )
    @JoinColumn(name="assets", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private FinanceFixedAssets assets;

    @Column(name="assets"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer assets_;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getReceiveDate() {
        return receiveDate;
    }

    public void setReceiveDate(Date receiveDate) {
        this.receiveDate = receiveDate;
    }

    public Integer getReceiveDept() {
        return receiveDept;
    }

    public void setReceiveDept(Integer receiveDept) {
        this.receiveDept = receiveDept;
    }

    public String getReceiveDeptName() {
        return receiveDeptName;
    }

    public void setReceiveDeptName(String receiveDeptName) {
        this.receiveDeptName = receiveDeptName;
    }

    public Integer getReceiver() {
        return receiver;
    }

    public void setReceiver(Integer receiver) {
        this.receiver = receiver;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public Date getReturnDate() {
        return returnDate;
    }

    public void setReturnDate(Date returnDate) {
        this.returnDate = returnDate;
    }

    public Integer getReturner() {
        return returner;
    }

    public void setReturner(Integer returner) {
        this.returner = returner;
    }

    public String getReturnerName() {
        return returnerName;
    }

    public void setReturnerName(String returnerName) {
        this.returnerName = returnerName;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public FinanceFixedAssets getAssets() {
        return assets;
    }

    public void setAssets(FinanceFixedAssets assets) {
        this.assets = assets;
    }

    public Integer getAssets_() {
        return assets_;
    }

    public void setAssets_(Integer assets_) {
        this.assets_ = assets_;
    }
}
