<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.accountant.mapper.SubjectCategoryMapper" >


    <select id = "getSingle" resultType = "subjectCategory" parameterType="subjectCategory" >
      select
      id, code, name, parent, level, orders, enabled, memo
      from t_accountant_subject_category
      where id = #{id}
    </select>

    <select id = "listPage" resultType = "subjectCategory" parameterType="hashmap" >
      select
      id, code, name, parent, level, orders, enabled, memo
      from t_accountant_subject_category
    </select>
    <delete id = "delete" parameterType = "subjectCategory" >
      delete from t_accountant_subject_category
      where id = #{id}
    </delete>

    <insert id = "insert" useGeneratedKeys = "true" keyProperty = "id" parameterType = "subjectCategory" >
      insert into t_accountant_subject_category (id, code, name,
        parent, level, orders,
        enabled, memo)
      values (#{id}, #{code}, #{name},
        #{parent}, #{level}, #{orders},
        #{enabled}, #{memo})
    </insert>

  <update id = "update" parameterType = "subjectCategory" >
    update t_accountant_subject_category
    <set >
      <if test="code != null" >
        code = #{code},
      </if>
      <if test="name != null" >
        name = #{name},
      </if>
      <if test="parent != null" >
        parent = #{parent},
      </if>
      <if test="level != null" >
        level = #{level},
      </if>
      <if test="orders != null" >
        orders = #{orders},
      </if>
      <if test="enabled != null" >
        enabled = #{enabled},
      </if>
      <if test="memo != null" >
        memo = #{memo},
      </if>
    </set>
    where id = #{id}
  </update>

  <select id="subjectCategoryByList" resultType="subjectCategory">
    select
      id, code, name, parent, level, orders, enabled, memo
      from t_accountant_subject_category
      where parent = #{parent}
  </select>


</mapper>