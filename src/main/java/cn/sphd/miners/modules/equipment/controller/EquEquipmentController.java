package cn.sphd.miners.modules.equipment.controller;

import java.util.Date;
import java.util.List;

import cn.sphd.miners.modules.equipment.entity.TEquEquipment;
import cn.sphd.miners.modules.equipment.service.ITEquEquipmentService;
import cn.sphd.miners.modules.mci.utils.AjaxResult;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import static cn.sphd.miners.modules.mci.utils.AjaxResult.toAjax;

/**
 * 装备管理_装备器具
 * 20230209 1.247装备器具新增
 * Controller
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Controller
@RequestMapping("/equipment")
public class EquEquipmentController {
    @Autowired
    private ITEquEquipmentService tEquEquipmentService;

    /**
     * 查询装备管理_装备器具
     * 20230209 1.247装备器具新增
     * 列表
     */
    @PostMapping("/list")
    @ResponseBody
    public List list(TEquEquipment tEquEquipment,User user) {
        tEquEquipment.setOrg(Long.valueOf(user.getOid()));
        List<TEquEquipment> list = tEquEquipmentService.selectTEquEquipmentList(tEquEquipment);
        return list;
    }



    /**
     * 新增保存装备管理_装备器具
     * 20230209 1.247装备器具新增
     */

    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(TEquEquipment tEquEquipment, User user) {
        tEquEquipment.setOrg(Long.valueOf(user.getOid()));
        tEquEquipment.setCreateDate(new Date());
        tEquEquipment.setCreator(Long.valueOf(user.getUserID()));
        tEquEquipment.setCreateName(user.getUserName());
        tEquEquipment.setName(tEquEquipment.getFullName());
        return toAjax(tEquEquipmentService.insertTEquEquipment(tEquEquipment));
    }
    /**
     * 修改保存装备管理_装备器具
     * 20230209 1.247装备器具新增
     */

    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(TEquEquipment tEquEquipment, User user) {
        return toAjax(tEquEquipmentService.updateTEquEquipment(tEquEquipment,user));
    }

    /**
     * 删除装备管理_装备器具
     * 20230209 1.247装备器具新增
     */

    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(tEquEquipmentService.deleteTEquEquipmentByIds(ids));
    }


}
