<template>
  <div id="applyTrialQueryPer">
    <TY_NavTop title="试用申请"></TY_NavTop>
    <div class="container">
      <div class="ui-message">
        <div class="ui-tip">
          <div class="queryCondition">
            <div>您要查询：</div>
            <div class="queryContent">{{queryData.beginDate | formatDay('YYYY年MM月DD日')}}-{{queryData.endDate | formatDay('YYYY年MM月DD日')}}期间</div>
            <div class="queryContent">
              <span v-if="queryData.approvalStatus === 2">被批准</span><span v-if="queryData.approvalStatus === 3">被驳回</span>的试用申请
            </div>
          </div>
        </div>
        <div class="sendContainer overTimeQuery">
          <div>
            符合查询条件的数据共如下{{queryData.trialQueryList.length}}条
          </div>
        </div>
      </div>
      <div>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in queryData.trialQueryList" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.name}}</div>
                <div style="margin-left: 20px">{{item.createName}} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
            </div>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
  .queryCondition{
    background: #fffaeb;
    font-size: 12px;
    padding: 8px;
    margin-bottom: 5px;
  }
</style>

<script>
export default {
  name: 'applyTrialQueryPer',
  data () {
    return {
      loading: true,
      paramQuery: {},
      queryData: {
        beginDate: 1560408078000,
        endDate: 1560408078000,
        approvalStatus: 2,
        number: 0,
        trialQueryList: []
      }
    }
  },
  created: function () {
    let paramQuery = this.$route.params.data
    if (!paramQuery) {
      paramQuery = JSON.parse(localStorage.getItem('query'))
    }
    this.paramQuery = paramQuery
    let that = this
    this.$http.post('../../../special/queryTryApplies.do', paramQuery, {
      emulateJSON: true
    }).then((response) => {
      this.loading = false
      let data = response.body
      if (data) {
        let data = response.data.data
        console.log(response.data)
        that.queryData.trialQueryList = data.customerOrganizations
        that.queryData.beginDate = data.beginDate
        that.queryData.endDate = data.endDate
        that.queryData.approvalStatus = data.approveStatus
      } else {
        that.$kiko_message('加载失败')
      }
    }).catch(function () {
      // this.jumpError('系统错误，请重试！')
    })
  },
  methods: {
    jump: function (id) {
      parent.location.href = '../../../special/toTryApplyInfo.do?type=11&id=' + id
    }
  }
}
</script>
