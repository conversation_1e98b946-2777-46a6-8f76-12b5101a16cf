package cn.sphd.miners.modules.material.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.modules.material.dao.SrmSupplierContactHistoryDao;
import cn.sphd.miners.modules.material.entity.SrmSupplierContact;
import cn.sphd.miners.modules.material.entity.SrmSupplierContactHistory;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
@Repository
public class SrmSupplierContactHistoryDaoImpl extends BaseDao<SrmSupplierContactHistory, Serializable> implements SrmSupplierContactHistoryDao {
    @Override
    public int insertAndResultId(SrmSupplierContact srmSupplierContact) {
        SrmSupplierContactHistory srmSupplierContactHistory=new SrmSupplierContactHistory();
        BeanUtils.copyProperties(srmSupplierContact,srmSupplierContactHistory);
        srmSupplierContactHistory.setId(null);
        srmSupplierContactHistory.setSupplierContact(srmSupplierContact.getId());
        save(srmSupplierContactHistory);
        getSession().flush();
        return srmSupplierContactHistory.getId();
    }
}
