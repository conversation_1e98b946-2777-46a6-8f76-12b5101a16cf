package cn.sphd.miners.modules.system.service.impl;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.service.BaseServiceImpl;
import cn.sphd.miners.common.utils.*;
import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.auth.service.AuthService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.generalAffairs.dao.UserContactDao;
import cn.sphd.miners.modules.generalAffairs.dto.AddressBookDto;
import cn.sphd.miners.modules.generalAffairs.dto.AttendanceUserDaysDto;
import cn.sphd.miners.modules.generalAffairs.entity.*;
import cn.sphd.miners.modules.generalAffairs.service.PersonalService;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceOldService;
import cn.sphd.miners.modules.personal.entity.*;
import cn.sphd.miners.modules.personal.service.PersonnelContractService;
import cn.sphd.miners.modules.personal.service.PersonnelReimburseService;
import cn.sphd.miners.modules.personal.service.UserMessageService;
import cn.sphd.miners.modules.sms.service.SendMessageService;
import cn.sphd.miners.modules.sms.service.SmsService;
import cn.sphd.miners.modules.sms.service.TemplateService;
import cn.sphd.miners.modules.socket.entity.MobileDevice;
import cn.sphd.miners.modules.socket.service.MqMobileDeviceService;
import cn.sphd.miners.modules.system.dao.*;
import cn.sphd.miners.modules.system.dto.UserDto;
import cn.sphd.miners.modules.system.dto.UserHonePageDto;
import cn.sphd.miners.modules.system.dto.UserLoginDto;
import cn.sphd.miners.modules.system.entity.*;
import cn.sphd.miners.modules.system.service.*;
import com.alibaba.fastjson.JSON;
import io.netty.util.internal.StringUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.query.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by 赵应 on 2016-06-20.
 */
@Service("userService")
@Transactional(readOnly = false)
public class UserServiceImpl extends BaseServiceImpl implements UserService {
    @Autowired
    UserDao userDao;
    @Autowired
    OrgDao orgDao;
    @Autowired
    UserContactDao userContactDao;
    @Autowired
    UserIdcardDao userIdcardDao;

    @Autowired
    UserLogDao userLogDao;

    @Autowired
    UserPopedomDao userPopedomDao;

    @Autowired
    PopedomDao popedomDao;
    @Autowired
    UserRoleDao userRoleDao;
    @Autowired
    ApprovalService approvalService;
    @Autowired
    UserMessageService userMessageService;
    @Autowired
    OffDutyService offDutyService;
    @Autowired
    PersonnelReimburseService personnelReimburseService;
    @Autowired
    PersonalService personalService;
    @Autowired
    UserPopedomService userPopedomService;
    @Autowired
    OrgPopedomService orgPopedomService;
    @Autowired
    WorkAttendanceOldService workAttendanceOldService;
    @Autowired
    MqMobileDeviceService mqMobileDeviceService;
    @Autowired
    SendMessageService sendMessageService;
    @Autowired
    UserHistoryService userHistoryService;
    @Autowired
    AuthService authService;
    @Autowired
    UserService userService;
    @Autowired
    UserLogService userLogService;
    @Autowired
    PopedomTaskService popedomTaskService;
    @Autowired
    OrgService orgService;
    @Autowired
    RolePrincipalHistoryService rolePrincipalHistoryService;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    SmsService smsService;
    @Autowired
    PersonnelContractService personnelContractService;
    @Autowired
    PopedomService popedomService;

    @Override
    public User getUserByLogon(Integer userID, String mobile, Integer oid) {
        User user;
        if(userID!=null && userID>=0) {
            user = userDao.get(userID);
        } else {
            String hql = "from User where isDuty!='2' and mobile=:mobile and oid=:oid and roleCode in ('staff','super','smallSuper','agentAccounting','agentSmallAccounting','see')";//2020/9/17 1.125多重身份 lixu改 and roleCode in ('staff','super')
            HashMap<String, Object> params = new HashMap<>();
            params.put("mobile", mobile);
            params.put("oid", oid);
            user = (User) userDao.getByHQLWithNamedParams(hql, params);
        }
        //wyu：user.logonState Not usered.
//        if(user!=null){
//            userDao.incAttr(user,"logonState");
//        }
        return user;
    }

    @Override
    public User getUserByOidAndPhone(Integer oid,String logonName) {
        String hql=" from User where oid=:oid and mobile=:mobile and isDuty in (:isDutys) and roleCode in (:roleCodes)";  //='staff' or roleCode='super' or roleCode='browse')
        Map<String,Object> params=new HashMap<>();
        params.put("mobile",logonName);
        params.put("oid",oid);
        params.put("isDutys",IsDutys.isDutyListAppCanLogin.getIsDutyList());
        params.put("roleCodes",RoleCodes.personOrgBrowse.getCodeList());
        User user= (User) userDao.getByHQLWithNamedParams(hql,params);
        return user;
    }

    // 临时处理 2024/10/31 12：11 需求确认，禁止离职手机在本机构入职，等复职功能做完后再一起处理。
    @Override
    public User getUserByOidAndPhone1(Integer oid,String logonName) {
        String hql=" from User where oid=:oid and mobile=:mobile and isDuty in (:isDutys) and roleCode in (:roleCodes)";  //='staff' or roleCode='super' or roleCode='browse')
        Map<String,Object> params=new HashMap<>();
        params.put("mobile",logonName);
        params.put("oid",oid);
//        params.put("isDutys",IsDutys.isDutyListAppCanLogin.getIsDutyList());
        params.put("isDutys",new ArrayList<String>(Arrays.asList("1","2","4")));
        params.put("roleCodes",RoleCodes.personOrgBrowse.getCodeList());
        User user= (User) userDao.getByHQLWithNamedParams(hql,params);
        return user;
    }

    @Override
    public User getMobileMasterUserByOidAndAcc(Integer oid, AuthAcc acc) {
        return getMasterUserByOidAndAcc(oid, acc, IsDutys.isDutyListAppCanLogin.getIsDutyList()); //User.getIsDutyListMobileCanLogin()
    }

    @Override
    public User getMasterUserByOidAndAcc(Integer oid, AuthAcc acc) {
        return getMasterUserByOidAndAcc(oid,acc,IsDutys.isDutyListCanLogin.getIsDutyList());  //User.getIsDutyListCanLogin()
    }
    public User getMasterUserByOidAndAcc(Integer oid, AuthAcc acc,List<String> isDuty) {
        String hql="from User where oid=:oid and accId=:accId and isDuty in (:isDuty) and roleCode in (:roleCode)";
        Map<String,Object> params=new HashMap<>();
        params.put("isDuty", isDuty);
        params.put("roleCode", RoleCodes.personCodes.getCodeList()); //User.personCodes()
        params.put("accId",acc.getId());
        params.put("oid",oid);
        return (User) userDao.getByHQLWithNamedParams(hql,params);
    }

    @Override
    @CacheEvict(value = "orgWithUserTree", allEntries=true)
    @CachePut(value = "getUserByRoleCode", condition = "#u.roleCode!=null&&#u.roleCode!=\"staff\"&&#u.roleCode!=\"browse\"", key="#u.oid+#u.roleCode")
    public User addUser(User u, Organization o) {
        AuthAcc authAcc= authService.newEnabledAccInOrg(u.getMobile(),u.getUserName(), o);// 生成登陆账号
        u.setAccId(authAcc.getId());
        u.setMsgCount(0);
        u.setForumCount(0);
        userDao.save(u);
        if (RoleTmpl.getStaffCodes().contains(u.getRoleCode())&&u.getIsDuty().equalsIgnoreCase("1")) {
            workAttendanceOldService.changeUserDepartment(u,u.getDepartment(), WorkAttendanceOldService.ChangeDeptReasons.Enroll);
        }
        userHistoryService.addUserHistoryByUser(u,"0",false);//保存原始信息历史记录
        return u;
    }

    @Override
    @Caching(evict={
            @CacheEvict(value = "orgWithUserTree", allEntries=true),
            @CacheEvict(value = "getUserByRoleCode", condition = "#u.roleCode!=null&&!\"staff\".equals(#u.roleCode)&&!\"browse\".equals(#u.roleCode)", key="#u.oid+#u.roleCode"),
            @CacheEvict(value = "getUserByRoleCode", condition = "(\"1\".equals(#u.isDuty)||\"3\".equals(#u.isDuty))&&\"staff\".equals(#u.roleCode)", key="#u.oid+\"accounting\""),
            @CacheEvict(value = "getUserByRoleCode", condition = "(\"1\".equals(#u.isDuty)||\"3\".equals(#u.isDuty))&&\"staff\".equals(#u.roleCode)", key="#u.oid+\"smallAccounting\""),
    })
    @CachePut(value = "changePasswordTime", key = "#u.mobile", condition = "#u.passwordTime!=null")
    public Date updateUser(User u) {
        userDao.update(u);
        return u.getPasswordTime();
    }

//    @Override
//    public void updateUserDevice(User u) {
//        String hql = "update User set deviceType=:deviceType,tokenId=:tokenId where mobile=:mobile";
//        HashMap<String,Object> params = new HashMap<>();
//        params.put("deviceType",u.getDeviceType());
//        params.put("tokenId",u.getTokenId());
//        params.put("mobile",u.getMobile());
//        userDao.queryHQLWithNamedParams(hql,params);
//    }


    @Override
    public List<User> getFinanceAndManageUserByOrgId(Integer oid) {
//        String condition = " and o.organization.id="+oid+" and o.isDuty=1 and o.roleCode in('general','finance','sale','accounting','agentAccounting')";
//        Map<String,String> orderBy=new HashMap<String,String>();
//        orderBy.put("userID","asc");
//        return userDao.findCollectionByConditionNoPage(condition,null,orderBy);

        String hql="from User where oid=:oid and isDuty=1 and roleCode in (:roleCode)";
        Map<String,Object> params=new HashMap<>();
        params.put("roleCode", RoleCodes.manageRoles.getCodeList()); //User.personCodes()
        params.put("oid",oid);
        List<User> userList=userDao.getListByHQLWithNamedParams(hql+" order by userID asc",params);
        return userList;

    }

    @Override
    public List<User> getUserListByOrgId(Integer oid) {
        return this.getUserListByOrgId(oid, "");
    }

    @Override
    public List<User> getUserListByOrgId(Integer oid, String order) {
        String condition = "from User where oid=:oid "+order;
        Map<String,Object> params=new HashMap<>();
        params.put("oid",oid);
        List<User> users=userDao.getListByHQLWithNamedParams(condition,params);
        return users;
    }


    @Override
    public User getUserByID(Integer userID) {
        User user=userDao.get(userID);
        if (user!=null&&0==user.getUserType()){  // 机构状态正常 的user
            return user;
        }
        return null;
    }

    @Override
    public User getLockedUser(Integer userID) {
        User user=userDao.get(userID);
        if (user!=null&&0!=user.getUserType()){  // 机构状态不正常 的user
            return user;
        }
        return null;
    }

    @Override
    public void deleteUser(User user) {
        userLogService.deleteUserLogsByUserId(user.getUserID());// 删除了日志级联，数据库有外键，需要先删日志表
        userDao.delete(user);
    }

    //根据学校获取所有人
    @Override
    public List<User> getAllUsersByOrganization(Integer orgId,User user) {
        return getAllUsersByOrgDepart(orgId, user, null);
    }

    @Override
    public List<User> getAllUsersByOrgDepart(Integer orgId, User user, String departName) {
        StringBuffer hql = new StringBuffer("from User");
        List<String> where = new ArrayList<>();
        Map<String, Object> params = new HashMap<>();
        where.add("oid =:orgId");
        params.put("orgId", orgId);
        if(user!=null) {
            if (StringUtils.isNotBlank(user.getUserName())){
                where.add("userName like :userName");
                params.put("userName", "%" + user.getUserName() + "%");
            }
            if (StringUtils.isNumeric(user.getDepartment())) {
                if ("0".equals(user.getDepartment())) {
                    where.add("department is null");
                } else {
                    where.add("department=:department");
                    params.put("department", user.getDepartment());
                }
            }
            if (StringUtils.isNumeric(user.getPostID())) {
                where.add("postID like :postID");
                params.put("postID", "%" + user.getPostID() + "%");
            }
        }
        if(StringUtils.isNotBlank(departName)) {
            where.add("department in (:department)");
            List<Organization> departments = orgService.getDepartmentsByName(orgId, departName);
            params.put("department", departments.stream().map(Organization::getId).collect(Collectors.toList()));
        }
        if(!where.isEmpty()) {
            hql.append(" where ").append(StringUtils.join(where, " and "));
        }
        List<User> users = userDao.getListByHQLWithNamedParams(hql.toString() ,params);
        return users;
    }

    @Override
    public List<User> getUserByOid(Integer oid,String userName) {
//        String condition = " and o.organization.id="+oid+" and o.roleCode in('staff','agent') ";
//        if (userName!=null&&!"".equals(userName)){
//            condition+=" and o.userName like'%"+userName+"%'";
//        }
//        return userDao.findCollectionByConditionNoPage(condition,null,null);

        List<User> userList= getUserByCurrentOidAndName(oid,userName,null);
        return userList;
    }

    @Override
    public List<User> getUserByCurrentOidAndName(Integer oid,String userName,String isDuty) {
//        String condition = " and o.organization.id="+oid+" and o.roleCode in('staff','agent')"; //not in('super','browse','smallSuper')
//        if (userName!=null&&!"".equals(userName)){
//            condition+=" and o.userName like'%"+userName+"%'";
//        }
//        if (!"".equals(isDuty) && isDuty!=null){
//            condition+=" and o.isDuty = "+isDuty;
//        }
//        return userDao.findCollectionByConditionNoPage(condition,null,null);

        String hql=" from User where oid=:oid and roleCode in(:roleCodes)";
        Map<String,Object> map=new HashMap<>();
        map.put("oid",oid);
        map.put("roleCodes",RoleCodes.personStaff.getCodeList());
        if (StringUtils.isNotEmpty(userName)){
            hql+=" and userName like:userName";
            map.put("userName","%"+userName+"%");
        }
        if (StringUtils.isNotEmpty(isDuty)){
            hql+=" and isDuty=:isDuty";
            map.put("isDuty",isDuty);
        }
        List<User> userList=userDao.getListByHQLWithNamedParams(hql,map);
        return userList;
    }

    @Override
    public List<User> getUserByCurrentOidAndNameLocking(Integer oid,String userName) {
//        String condition = " and o.organization.id="+oid+" and o.roleCode in('staff','agent') and o.isDuty in('1','9')"; //not in('super','browse','smallSuper')
//        if (userName!=null&&!"".equals(userName)){
//            condition+=" and o.userName like'%"+userName+"%'";
//        }
//        userDao.findCollectionByConditionNoPage(condition,null,null);

        List<User> userList= getUserByCurrentOidAndName(oid,userName,"1");
        return userList;
    }

    @Override
    public List<User> getUserListByPostId(Long postId) {
        String hql="from User where isDuty in(:isDutys) and postID=:postId";
        Map<String,Object> map=new HashMap<>();
        map.put("postId",postId.toString());
        map.put("isDutys",IsDutys.isDutyListWebCanLogin.getIsDutyList());
        List<User> users=userDao.getListByHQLWithNamedParams(hql,map);
        return users;
    }

    @Override
    public List<User> getUserListByOidPhone(Integer oid, String phone) {
//        String condition = " and o.mobile ='"+phone+"' and o.leader is not null and roleCode in ('staff','super')";
//        if (oid!=null){
//            condition+=" and o.oid="+oid;
//        }
//        List<User> users=userDao.findCollectionByConditionNoPage(condition,null,null);

        String hql="from User where leader is not null and oid=:oid and mobile=:mobile and roleCode in (:roleCodes) ";
        Map<String,Object> map=new HashMap<>();
        map.put("oid",oid);
        map.put("mobile",phone);
        map.put("roleCodes",RoleCodes.personOrg.getCodeList());
        List<User> users=userDao.getListByHQLWithNamedParams(hql,map);
        return users;
    }


    @Override
    public List<User> getUsersByOrg(Integer oid,String department,String isDuty) {
        HashMap<String, Object> params = new HashMap<>();
        StringBuffer hql = new StringBuffer("from User");
        StringBuffer where = new StringBuffer();
        if (oid!=null) {
            where.append(" and oid=:oid");
            params.put("oid",oid);
        }
        if (!MyStrings.nulltoempty(department).isEmpty()){
            where.append(" and department =: department");
            params.put("department",department);
        }
        if (!MyStrings.nulltoempty(isDuty).isEmpty()){
            where.append(" and isDuty=:isDuty");
            params.put("isDuty",isDuty);
        }
        if (where.length() > 0) {
            hql.append(" where ").append(where.substring(4));
        }
        List<User> users = userDao.getListByHQLWithNamedParams(hql.toString(),params);
        return users;
    }

    @Override
    public List<User> getUsersByLeader(Integer oid, String leader) {
//        String hql=" from User where oid="+oid;
//        if (leader!=null){
//            hql+=" and leader='"+leader+"'";
//        }
//        List<User> users=userDao.getListByHQL(hql);
        String hql=" from User where oid=:oid ";
        if (leader!=null){
            hql+=" and leader=:leader";
        }
        Map<String,Object> map=new HashMap<>();
        map.put("oid",oid);
        map.put("leader",leader);
        List<User> users=userDao.getListByHQLWithNamedParams(hql,map);
        return users;
    }



    protected static List<String> menuMids=null;
    protected void setMenuMids(){
        if(menuMids==null||menuMids.size()<=0){
            String hql="select mid from Popedom where url is not null and length(trim(url))>0";
            menuMids=popedomDao.getListByHQLWithNamedParams(hql,null);
        }
    }

    @Override
    @Cacheable(value = "userPopedoms", key = "#u.userID")
    public String getPopedomStringByUser(User u) {
        return getPopedomStringByUser(u, false);
    }

    @Override
    @CachePut(value = "userPopedoms", key = "#u.userID")
    @CacheEvict(value = "userPopedomTasks", key = "#u.userID")
    public String getPopedomStringByUser(User u, boolean reload){
        setMenuMids();
        HashMap<String, Boolean> orgSearch = this.getOrgPopedomHashMap(u.getOid());
        HashMap<String, Boolean> userSearch = userPopedomDao.getUserPopedomMapByUser(this.getUserByID(u.getUserID()));
        if (menuMids.size() > 0 && orgSearch.size() > 0 && userSearch.size() > 0) {
            StringBuffer sb = new StringBuffer("'");
            for (String mid : menuMids) {
                if (orgSearch.containsKey(mid) && userSearch.containsKey(mid)) {
                    sb.append(mid).append("','");
                }
            }
            if (sb.length() > 1) {
                return sb.substring(0, sb.length() - 2);
            }
        }
        return "";
    }

    public HashMap getOrgPopedomHashMap(Integer oid){
        HashMap<String,Boolean> result=new HashMap<>();
        if(oid == null) {
            return result;
        }
//        Long now,time = System.currentTimeMillis();
        Organization organization=orgDao.get(oid);
        if(organization == null) {
            return result;
        }
        boolean b=orgPopedomService.getOrgPopedomByMid(oid,"sb");//是否有会计模块权限
//        now = System.currentTimeMillis();
//        System.out.println("UserServiceImpl 之 getOrgPopedomHashMap 查询机构耗时："+ TimeUtils.toMilliTimeString(now-time));
        List<OrgPopedom> list=new ArrayList<>();
        if (b) {
            if (!"4".equals(organization.getAccountState())&&"N".equals(organization.getRebuildLabel())) {
//                time = System.currentTimeMillis();
                list=orgPopedomService.getOrgPopedomListNotInCode(oid, "accountState");// 未建账
//                now = System.currentTimeMillis();
//                System.out.println("UserServiceImpl 之 getOrgPopedomListNotInCode accountState 耗时："+ TimeUtils.toMilliTimeString(now-time));
            } else if ("4".equals(organization.getAccountState())&&"N".equals(organization.getRebuildLabel())){
//                time = System.currentTimeMillis();
                list=orgPopedomService.getOrgPopedomListByOid(oid); //建账后
//                now = System.currentTimeMillis();
//                System.out.println("UserServiceImpl 之 建账后 getOrgPopedomListByOid 耗时："+ TimeUtils.toMilliTimeString(now-time));
            } else if ("Y".equals(organization.getRebuildLabel())) {
//                time = System.currentTimeMillis();
                list=orgPopedomService.getOrgPopedomListNotInCode(oid, "rebuildLabel");//重新建账点击前
//                now = System.currentTimeMillis();
//                System.out.println("UserServiceImpl 之 getOrgPopedomListNotInCode rebuildLabel 耗时："+ TimeUtils.toMilliTimeString(now-time));
            }
        }else {
//            time = System.currentTimeMillis();
            list=orgPopedomService.getOrgPopedomListByOid(oid);
//            now = System.currentTimeMillis();
//            System.out.println("UserServiceImpl 之 getOrgPopedomListByOid 耗时："+ TimeUtils.toMilliTimeString(now-time));
        }
        for(OrgPopedom p : list){
            result.put(p.getMid(), true);
        }
        return result;
    }

    @Override
    @Caching(evict={@CacheEvict(value = "userPopedoms", key = "#uid"),@CacheEvict(value = "userPopedomTasks", key = "#uid")})
    public void clearPopedomsCacheByUserId(Integer uid) {
        //wyu: 空函数体
    }

    @Override
    public List<User> getManages(Integer oid) {
//        String hql = " from User o where o.isDuty=1 and o.roleCode in('general','finance','sale','accounting') and o.oid = "+oid;
//        return userDao.getListByHQL(hql);

        String hql = " from User where isDuty=1 and oid =:oid and roleCode in(:roleCodes) ";
        Map<String,Object> map=new HashMap<>();
        map.put("oid",oid);
        map.put("roleCodes",RoleCodes.manageRoles.getCodeList());
        List<User> users=userDao.getListByHQLWithNamedParams(hql,map);
        return users;
    }



    //根据条件筛选职工
    @Override
    public List<User> screenUsers(String edus, String gender, String departments, String posts, String marry, String birthday1, String birthday2, String onDutyDate1, String onDutyDate2,String isDuty,String oids) {
        String condition = " and o.roleCode!='browse' and o.oid in("+oids+")";// and o.roleCode!='super' 1.96 版要求 职工档案展示董事长
        //学历
        if (StringUtils.isNotBlank(edus)){
            condition+=" and (";
            List<String> edu1 = Arrays.asList(edus.split(","));
            for (int i=0;i<edu1.size();i++){
                condition+="o.degree = '"+edu1.get(i)+"'";
                if (i<edu1.size()-1){
                    condition+=" or ";
                }
                if (i==edu1.size()-1){
                    condition+=")";
                }
            }
        }
        // gender 性别 1-男 0-女
        if (!"".equals(gender) && gender!=null){
            condition+=" and (";
            List<String> genders = Arrays.asList(gender.split(","));
            for (int i=0; i<genders.size(); i++){
                condition+=" o.gender = '"+genders.get(i)+"'";
                if (i<genders.size()-1){
                    condition+=" or";
                }
                if (i==genders.size()-1){
                    condition+=")";
                }
            }
        }
        //部门
        if (StringUtils.isNotBlank(departments)){
            condition+=" and (";
            List<String> departments1 = Arrays.asList(departments.split(","));
            for (int i=0; i<departments1.size(); i++){
                condition+=" o.departName = '"+departments1.get(i)+"'";
                if (i<departments1.size()-1){
                    condition+=" or";
                }
                if (i==departments1.size()-1){
                    condition+=")";
                }
            }
        }
        //职位
        if ( StringUtils.isNotBlank(posts)){
            condition+=" and (";
            List<String> posts1 = Arrays.asList(posts.split(","));
            for (int i=0; i<posts1.size(); i++){
                condition+=" o.postName = '"+posts1.get(i)+"'";
                if (i<posts1.size()-1){
                    condition+=" or";
                }
                if (i==posts1.size()-1){
                    condition+=")";
                }
            }
        }
        //婚姻状况 0-已婚。1-未婚
        if (!"".equals(marry) && marry!=null){
            condition+=" and (";
            List<String> marrys = Arrays.asList(marry.split(","));
            for (int i=0; i<marrys.size(); i++){
                condition+=" o.marry = '"+marrys.get(i)+"'";
                if (i<marrys.size()-1){
                    condition+=" or";
                }
                if (i==marrys.size()-1){
                    condition+=")";
                }
            }
        }
        //出生日期
        if (!"".equals(birthday1) && birthday1!=null){
            condition+=" and o.birthday >='"+birthday1+" 00:00:00'";
        }
        if (!"".equals(birthday2) && birthday2!=null){
            condition+=" and o.birthday <='"+birthday2+" 00:00:00'";
        }
        //入职日期
        if (!"".equals(onDutyDate1) && onDutyDate1!=null){
            condition+=" and o.onDutyDate >='"+onDutyDate1+" 00:00:00'";

        }
        if (!"".equals(onDutyDate2) && onDutyDate2!=null){
            condition+=" and o.onDutyDate <='"+onDutyDate2+" 00:00:00'";

        }

        //isDuty 是否在职  1表示在职，2表示离职
        if (!"".equals(isDuty) && isDuty!=null){
            if ("1".equals(isDuty)){
                condition+=" and o.isDuty in('1','9')";
            }else {
                condition+= " and o.isDuty = " + isDuty;
            }
        }
        List<User> users = userDao.findCollectionByConditionNoPage(condition,null,null);
        return users;
    }


    @Override
    public List<User> employeeList(String[] edus, String[] gender, String[] departments, String[] posts, String[] marry, String birthday1, String birthday2, String onDutyDate1, String onDutyDate2, String isDuty, Integer[] oids,PageInfo pageInfo) {
        String hql = " from User where roleCode in(:roleCodes) and oid in(:oids)";// and o.roleCode!='super' 1.96 版要求 职工档案展示董事长
        Map<String,Object> map=new HashMap<>();
        map.put("roleCodes",RoleCodes.personOrg.getCodeList());
        map.put("oids",oids);

        //学历
        if (edus!=null&&edus.length>0){
            hql+=" and edu in(:edus)";
            map.put("edus",edus);
        }
        // gender 性别 1-男 0-女
        if (gender!=null&&gender.length>0){
           hql+=" and gender in(:gender)";
           map.put("gender",gender);
        }
        //部门
        if (departments!=null&&departments.length>0){
            hql+=" and department in(:departments)";
            map.put("departments",departments);
        }
        //职位
        if (posts!=null&&posts.length>0){
            hql+=" and postID in(:posts)";
            map.put("posts",posts);
        }
        //婚姻状况 0-已婚。1-未婚
        if (marry!=null&&marry.length>0){
            hql+=" and marry in(:marry)";
            map.put("marry",marry);
        }
        //出生日期
        if (StringUtils.isNotEmpty(birthday1)){
            hql+=" and birthday >=:birthday1";
            map.put("birthday1", NewDateUtils.dateFromString(birthday1+"-01","yyyy-MM-dd"));
        }
        if (StringUtils.isNotEmpty(birthday2)){
            hql+=" and birthday<=:birthday2";
            map.put("birthday2",NewDateUtils.dateFromString(birthday2+"-31","yyyy-MM-dd"));
        }
        //入职日期
        if (StringUtils.isNotEmpty(onDutyDate1)){
            hql+=" and onDutyDate >=:onDutyDate1";
            map.put("onDutyDate1",NewDateUtils.dateFromString(onDutyDate1,"yyyy-MM-dd"));
        }
        if (StringUtils.isNotEmpty(onDutyDate2)){
            hql+=" and onDutyDate <=:onDutyDate2";
            map.put("onDutyDate2",NewDateUtils.dateFromString(onDutyDate2,"yyyy-MM-dd"));
        }

        //isDuty 是否在职  1表示在职，2表示离职
        if (isDuty!=null){
            hql+=" and isDuty=:isDuty";
            map.put("isDuty",isDuty);
        }
        List<User> users = userDao.getListByHQLWithNamedParams(hql,map,pageInfo);

        List<Integer> userIdList=new ArrayList<>();
        List<Integer> oidList=new ArrayList<>();
        for (User u:users){
            userIdList.add(u.getUserID());
            oidList.add(u.getOid());
        }
        Map<Integer,String> orgMap=new HashMap<>();
        List<Organization> organizationList=orgService.getOrgByOids(oidList);
        for (Organization organization:organizationList){
            orgMap.put(organization.getId(),organization.getName());
        }
        Map<Integer,Date> dateMap=userLogService.getUserLastLoginDate(userIdList);

        //1.322 劳动合同 李旭 加 2024/12/19
        List<PersonnelContract> personnelContractList=personnelContractService.getPersonnelContractsByUserIds(userIdList, Byte.valueOf(isDuty));
        Map<Integer,PersonnelContract> personnelContractMap=new HashMap<>();
        for (PersonnelContract personnelContract:personnelContractList){
            personnelContractMap.put(personnelContract.getUser(),personnelContract);
        }
        for (User u:users){
            u.setLastLoginDate(dateMap.get(u.getUserID()));
            u.setOrgName(orgMap.get(u.getOid()));
            u.setPersonnelContract(personnelContractMap.get(u.getUserID()));
        }
        return users;
    }

    @Override
    public Integer getSmallManageByMCode(Integer oid, String manageCode) {
//        String mid=popedomDao.getMidByCode("AuthLeaf");
//        String hql="from UserPopedom where oid="+oid+" and user.isDuty='1' and user.managerCode='"+manageCode+"' and user.roleCode!='"+manageCode+"' and mid='"+mid+"'";
//        UserPopedom userPopedom=userPopedomDao.getByHQL(hql);
//        if (userPopedom!=null) {
//            return userPopedom.getUserId();
//        }else {
//            return 0;
//        }

        String mid=popedomDao.getMidByCode("AuthLeaf");
        String hql="from UserPopedom where oid=:oid and user.isDuty='1' and user.managerCode=:manageCode and user.roleCode!=:manageCode and mid=:mid";
        Map<String,Object> map=new HashMap<>();
        map.put("oid",oid);
        map.put("manageCode",manageCode);
        map.put("mid",mid);
        UserPopedom userPopedom= (UserPopedom) userPopedomDao.getByHQLWithNamedParams(hql,map);
        if (userPopedom!=null) {
            return userPopedom.getUserId();
        }else {
            return 0;
        }
    }

    @Override
    public List<User> getUserListByCodeOid(Integer oid, String code, String isDuty) {
        Map<String,Object> map=new HashMap<>();

        StringBuffer condition =new StringBuffer(" from User where oid =:oid ");
        map.put("oid",oid);
        if (code!=null&&!"".equals(code)){
            condition.append(" and managerCode=:code  and managerCode!=roleCode"); //身份不再作为权限设置选项
            map.put("code",code);
        }else {
            condition.append(" and roleCode not in(:roleCodes)");
            map.put("roleCodes",new String[]{"super","browse","accounting","agentAccounting"});
        }
        if (!"".equals(isDuty) && isDuty!=null){
            condition.append(" and isDuty =:isDuty");
            map.put("isDuty",isDuty);
        }
    //    condition.append(" order by CONVERT(userName USING gbk)");
        return userDao.getListByHQLWithNamedParams(condition.toString(),map);
    }

    @Override
    public List<User> getUserListByCodeOidLocking(Integer oid, String code) {
        Map<String,Object> map=new HashMap<>();

        StringBuffer condition =new StringBuffer(" from User where oid =:oid and isDuty in('1','9')");
        map.put("oid",oid);
        if (code!=null&&!"".equals(code)){
            condition.append(" and managerCode=:code and managerCode!=roleCode"); //身份不再作为权限设置选项
            map.put("code",code);
        }else {
            condition.append(" and roleCode not in(:roleCodes)");
            map.put("roleCodes",new String[]{"super","browse","accounting","agentAccounting"});
        }
        //    condition.append(" order by CONVERT(userName USING gbk)");
        return userDao.getListByHQLWithNamedParams(condition.toString(),map);
    }

    @Override
    @Cacheable(value = "getUserByRoleCode",condition = "#roleCode!=null&&#roleCode!=\"staff\"&&#roleCode!=\"browse\"&&#roleCode!=\"agentAccounting\"&&#roleCode!=\"initialBeforeGeneral\"&&#roleCode!=\"temporary\"", key="#oid+#roleCode")
    public User getUserByRoleCode(Integer oid, String roleCode) {
        String hql="from User where oid=:oid and roleCode=:roleCode and isDuty in (:isDuties)";
        HashMap<String,Object> params = new HashMap<>();
        params.put("oid",oid);
        params.put("isDuties",IsDutys.isDutyListWebCanLogin.getIsDutyList()); //new String[]{"1","3","9"}) // 董事长 就算 锁定了 也要找到，不然 很多用董事长的地方 会报错
        params.put("roleCode",roleCode);
        return (User) userDao.getByHQLWithNamedParams(hql,params);
    }

    @Override
    @Cacheable(value = "getUserByRoleCode",condition = "#roleCode!=null&&#roleCode!=\"staff\"&&#roleCode!=\"browse\"&&#roleCode!=\"agentAccounting\"&&#roleCode!=\"initialBeforeGeneral\"&&#roleCode!=\"temporary\"", key="#oid+#roleCode")
    public User getUserByRoleCode(Integer oid,String userName,String roleCode) {
        String hql="from User where oid=:oid and roleCode=:roleCode and isDuty in (:isDuties)";
        HashMap<String,Object> params = new HashMap<>();
        params.put("oid",oid);
        params.put("isDuties",IsDutys.isDutyListWebCanLogin.getIsDutyList());  //new String[]{"1","3","9"} // 董事长 就算 锁定了 也要找到，不然 很多用董事长的地方 会报错
        params.put("roleCode",roleCode);
        return (User) userDao.getByHQLWithNamedParams(hql,params);
    }

    @Override
    public boolean isSuperGeneralGeneralSmallManager(User user) {
        return isSuper(user)||isGeneral(user)||isGeneralSmallManager(user);
    }

    @Override
    public boolean isSuper(User user) {
        if (user.getRoleCode()!=null) {
            return RoleTmpl.isSuper(user.getRoleCode());
        }else {
            return false;
        }
    }

    @Override
    public boolean isSuper(UserLoginDto user) {
        if (user.getRoleCode()!=null) {
            return RoleTmpl.isSuper(user.getRoleCode());
        }else {
            return false;
        }
    }

    @Override
    public boolean isGeneral(User user) {
        return RoleTmpl.isGeneral(user.getRoleCode());
    }

    @Override
    public boolean isGeneralSmallManager(User user) {
        String roleCode = user.getRoleCode();
        if(RoleTmpl.isHighManager(roleCode) //高管
                || user.getManagerCode()==null //还没设置所属高管
                || !RoleTmpl.isGeneral(user.getManagerCode()) //非总务下属
                || !RoleTmpl.isStaff(roleCode)){ //超管或者超级浏览者
            return false;
        }
        return popedomDao.isSmallManager(user); //是小高管
    }

    @Override
    public List<User> getUserByMobileAndIsDuties(Integer oid, String mobile, List<String> isDuties) {
        StringBuffer hql = new StringBuffer("from User u");
        List<String> where = new ArrayList<>();
        Map<String, Object> params = new HashMap<>();
        if (oid != null) {
            where.add("u.oid=:oid");
            params.put("oid", oid);
        }
        if (StringUtils.isNotEmpty(mobile)) {
            where.add("u.mobile=:mobile");
            params.put("mobile", mobile);
        }
        if (isDuties!=null && isDuties.size()>0) {
            where.add("u.isDuty in (:isDuty)");
            params.put("isDuty", isDuties);
        }
        if(where.size()>0) {
            hql.append(" where ").append(StringUtils.join(where, " and "));
            return userDao.getListByHQLWithNamedParams(hql.toString(),params);
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public List<User> getUserByMobileAndIsDuty(Integer oid, String phone, String isDuty) {
        List<String> isDuties = null;
        if(StringUtils.isNotEmpty(isDuty)) {
            isDuties = Arrays.asList(isDuty);
        }
        return getUserByMobileAndIsDuties(oid, phone, isDuties);
//        String condition = " and o.mobile ='"+phone+"'";
//        if (oid!=null){
//            condition+=" and o.oid="+oid;
//        }
//        if (!"".equals(isDuty) && isDuty!=null){
//            condition+=" and o.isDuty="+isDuty;
//        }
//        List<User> users=userDao.findCollectionByConditionNoPage(condition,null,null);
//        return users;
    }

    @Override
    public List<User> getUserListByCoreCode(Integer oid, String coreCode) {
        String hql=" from UserRole where user.isDuty='1' and user.oid="+oid+" and role.code='"+coreCode+"'";
        List<UserRole> userRoles=userRoleDao.getListByHQL(hql);
        List<User> userList=new ArrayList<>();
        for (UserRole u:userRoles){
            User user=u.getUser();
            user.setRoleCreateUserName(u.getCreateName());
            user.setRoleCreateDate(u.getCreateTime());
            userList.add(user);
        }
        return userList;
    }

    @Override
    public List<User> getUserListByCoreCodeLocking(Integer oid, String coreCode) {
        String hql=" from UserRole where user.isDuty in('1','9') and user.oid="+oid+" and role.code='"+coreCode+"'";
        List<UserRole> userRoles=userRoleDao.getListByHQL(hql);
        List<User> userList=new ArrayList<>();
        for (UserRole u:userRoles){
            User user=u.getUser();
            user.setRoleCreateUserName(u.getCreateName());
            user.setRoleCreateDate(u.getCreateTime());
            userList.add(user);
        }
        return userList;
    }

    @Override
    public User getUserByCoreCode(Integer oid, String coreCode) {
        String hql=" from UserRole where user.isDuty in('1','9') and user.oid="+oid+" and role.code='"+coreCode+"'";
        UserRole userRole=userRoleDao.getByHQL(hql);
        if (userRole!=null) {
            return userRole.getUser();
        }else {
            return null;
        }
    }

    @Override
    public List<User> getUserByOidAndPostId(Integer oid, Integer postId) {
        String hql = " from User where isDuty='1' and oid = "+oid;
        if (postId!=null){
            hql+=" and postID = "+postId;
        }
        return userDao.getListByHQL(hql);
    }


    @Override
    public List<User> getUserListByUserNameDepartName(Integer oid, String userName, String departName) {
//        String condition = " and o.organization.id="+oid+" and o.isDuty in('1','9') and o.roleCode!='super' and o.roleCode!='browse'";
//        if (userName!=null&&!"".equals(userName)){
//            condition+=" and o.userName like'%"+userName+"%'";
//        }
//        if (!"".equals(departName) && departName!=null){
//            condition+=" and o.departName like'%"+departName+"%'";
//        }
//        return userDao.findCollectionByConditionNoPage(condition,null,null);

        String hql=" from User where oid=:oid and isDuty='1' and roleCode not in(:roleCodes)";
        Map<String,Object> map =new HashMap<>();
        map.put("roleCodes",RoleCodes.superAndBrowse.getCodeList());
        if (StringUtils.isNotEmpty(userName)){
            hql+=" and userName like:userName";
            map.put("userName","%"+userName+"%");
        }
        if (StringUtils.isNotEmpty(departName)){
            hql+=" and departName like:departName";
            map.put("departName","%"+departName+"%");
        }
        return userDao.getListByHQLWithNamedParams(hql,map);

    }

    @Override
    public List<UserDto> getUserDtoByUserNameDepartName(Integer oid, String userName, String departName) {
        Map<String,Object> map = new HashMap<>();
        map.put("oid",oid);
//        String hql="select new cn.sphd.miners.modules.system.dto.UserDto(userID, userName, departName, postName) from User where roleCode!='super' and roleCode!='browse' and oid=:oid";
        String hql="select new cn.sphd.miners.modules.system.dto.UserDto(userID, userName, departName, postName) from User where roleCode in('super','staff','agent') and oid=:oid and isDuty in('1','9')";

        if (userName!=null&&!"".equals(userName)){
            hql+=" and userName like:userName";
            map.put("userName","%"+userName+"%");
        }
        if (departName!=null&&!"".equals(departName)){
            hql+=" and departName like:departName";
            map.put("departName","%"+departName+"%");
        }

        List<UserDto> list = popedomDao.getListByHQLWithNamedParams(hql, map);

        for (UserDto u:list){
            HashMap<String,String> approvalMap=new HashMap<>();
            approvalMap.put("outTimeApproval",this.isApproval(oid,u.getUserID(),"overTimeApply"));
            approvalMap.put("leaveApproval",this.isApproval(oid,u.getUserID(),"leaveApply"));
            approvalMap.put("reimburseApproval",this.isApproval(oid,u.getUserID(),"reimburseApply"));
            approvalMap.put("approvalApproval",this.isApproval(oid,u.getUserID(),"itemApply"));
//            approvalMap.put("projectApproval",this.isApproval(oid,u.getUserID(),"新项目立项"));
//            approvalMap.put("projectDevelopment",this.isApproval(oid,u.getUserID(),"新项目开发"));
            u.setApprovalMap(approvalMap);//审批放到人的审批容器中
        }
        return list;
    }

    @Override
    public String isApproval(Integer oid, Integer userId, String item) {
        ApprovalItem approvalItem=approvalService.getApprovalItemByOidAndCode(oid, item);
        if (approvalItem.getLevel()!=null) {//审批项 审批级别不为空
            for (ApprovalFlow a : approvalItem.getApprovalFlowHashSet()) {
                if (a.getToUserId().equals(userId)) {//审批人是当前人
                    if (approvalItem.getLevel().equals(a.getLevel())) {//审批项级别与审批流程定义级别相同
                        return "最终审批者";
                    }else {
                        return a.getLevel()+"级审批者";
                    }
                }
            }
        }
        return "";
    }

    @Override
    public List<User> getNotDirectLowerGrade(Integer oid,Integer... userId) {
        String hql="from User where oid="+oid+" and roleCode in('staff','agent','smallSuper') and isDuty in ('1','9')";
        for (Integer u:userId){
            String rankUrl="/"+u+"/";
            hql+=" and userID!="+u+" and rankUrl not like'%"+rankUrl+"%'";
        }
        List<User> users=userDao.getListByHQL(hql);
        return users;

//        String hql="from User where oid=:oid and roleCode in(:roleCodes) and isDuty ='1' ";
//        for (Integer u:userId){
//            String rankUrl="/"+u+"/";
//            hql+=" and userID!=:u and rankUrl not like :rankUrl ";
//        }

    }
    @Override
    public List<User> getNotDirectLowerGrade(Integer oid,String userName,Integer... userId) {
        String hql="from User where oid="+oid+" and roleCode in('staff','agent') and isDuty in ('1','9')";
        for (Integer u:userId){
            String rankUrl="";
            rankUrl+="/"+u+"/";
            hql+=" and userID!="+u+" and rankUrl not like'%"+rankUrl+"%'";
        }
        List<User> users=userDao.getListByHQL(hql);
        return users;
    }

    @Override
    public List<User> getDirectLowerGrade(String rankUrl,Integer oid) {
//        User user=userDao.get(userId);
//        String rankUrl="";
//        if (user.getRankUrl()!=null){
//            rankUrl+=user.getRankUrl();
//        }
//        String hql="from User where oid="+oid+" and isDuty in ('1','3') and rankUrl like'%"+rankUrl+"%'";
        String hql=" from User where oid=:oid and isDuty in ('1','3','9') and rankUrl like:rankUrl";
        Map<String,Object> map = new HashMap<>();
        map.put("oid",oid);
        map.put("rankUrl","%"+rankUrl+"%");
        List<User> users=userDao.getListByHQLWithNamedParams(hql,map);
        return users;
    }

    @Override
    public List<UserDto> getStaffAndLocking(Integer oid, String userName) {
        Map<String,Object> map = new HashMap<>();
        String hql="select new cn.sphd.miners.modules.system.dto.UserDto(userID, userName,departName,postName,department) from User where department=null and roleCode='staff' and oid=:oid  and isDuty in(1,9)";
        map.put("oid",oid);
        if (!"".equals(userName) && userName!=null){
            hql+=" and userName like:userName";
            map.put("userName","%"+userName+"%");
        }
        List<UserDto> list = popedomDao.getListByHQLWithNamedParams(hql, map);
        return list;
    }


    @Override
    public List<UserDto> getUserDtoByOidAndDeptIds(Integer oid,Integer userId, List<String> deptIds, String userName) {
        return getUserDtoByOidAndDeptIds(oid,userId, deptIds, userName, null);
    }

    @Override
    public List<UserDto> getUserDtoByOidAndDeptIds(Integer oid,Integer userId, List<String> deptIds, String userName, List<Integer> removeUids) {
        Map<String,Object> map = new HashMap<>();
        StringBuffer hql=new StringBuffer("select new cn.sphd.miners.modules.system.dto.UserDto(userID, userName,departName,postName,department) from User where oid=:oid and isDuty=1 and roleCode='staff'");//isDuty=1在职
        map.put("oid",oid);
        boolean nullDept=false;
        if(deptIds.contains("0")) {
            deptIds.remove("0");
            nullDept=true;
        }
        if (deptIds!=null&&deptIds.size()>0){
            if(nullDept){
                hql.append(" and (department is null or ");
            } else {
                hql.append(" and ");
            }
            hql.append("department in (:departments)");
            map.put("departments",deptIds);
            if(nullDept) {
                hql.append(")");
            }
        } else if (nullDept) {
            hql.append(" and department is null");
        }
        if (!MyStrings.nulltoempty(userName).isEmpty()){
            hql.append(" and userName like:userName");
            map.put("userName","%"+userName+"%");
        }
        if(userId!=null){  //取此人员的下属以及间接下属
            hql.append(" and (userID=:userId or rankUrl like :userID)");
            map.put("userId", userId); //取职工自己
            map.put("userID", "%/" + userId + "/%");  //取本职工的直接以及间接下级
        }
        if (removeUids!=null && !removeUids.isEmpty()) {
            hql.append(" and userID not in (:removeUids)");
            map.put("removeUids", removeUids);
        }
        List<UserDto> list = popedomDao.getListByHQLWithNamedParams(hql.toString(), map);
        return list;
    }

    @Override
    public List<UserDto> getUserDtoByOidAndDeptIdsLocking(Integer oid,Integer userId, List<String> deptIds, String userName, List<Integer> removeUids) {
        Map<String,Object> map = new HashMap<>();
        StringBuffer hql=new StringBuffer("select new cn.sphd.miners.modules.system.dto.UserDto(userID, userName,departName,postName,department) from User where oid=:oid and isDuty in('1','9') and roleCode='staff'");//isDuty=1在职
        map.put("oid",oid);
        boolean nullDept=false;
        if(deptIds.contains("0")) {
            deptIds.remove("0");
            nullDept=true;
        }
        if (deptIds!=null&&deptIds.size()>0){
            if(nullDept){
                hql.append(" and (department is null or ");
            } else {
                hql.append(" and ");
            }
            hql.append("department in (:departments)");
            map.put("departments",deptIds);
            if(nullDept) {
                hql.append(")");
            }
        } else if (nullDept) {
            hql.append(" and department is null");
        }
        if (!MyStrings.nulltoempty(userName).isEmpty()){
            hql.append(" and userName like:userName");
            map.put("userName","%"+userName+"%");
        }
        if(userId!=null){  //取此人员的下属以及间接下属
            hql.append(" and (userID=:userId or rankUrl like :userID)");
            map.put("userId", userId); //取职工自己
            map.put("userID", "%/" + userId + "/%");  //取本职工的直接以及间接下级
        }
        if (removeUids!=null && !removeUids.isEmpty()) {
            hql.append(" and userID not in (:removeUids)");
            map.put("removeUids", removeUids);
        }
        List<UserDto> list = popedomDao.getListByHQLWithNamedParams(hql.toString(), map);
        return list;
    }


    @Override
    public List<UserDto> getUserDtoByOidAndNoDeptIds(Integer oid,Integer userId, List<String> deptIds, String userName) {
        return getUserDtoByOidAndNoDeptIds(oid,userId, deptIds, userName, null);
    }

    @Override
    public List<UserDto> getUserDtoByOidAndNoDeptIds(Integer oid,Integer userId, List<String> deptIds, String userName, List<Integer> removeUids) {
        Map<String,Object> map = new HashMap<>();
        StringBuffer hql=new StringBuffer("select new cn.sphd.miners.modules.system.dto.UserDto(userID, userName,departName,postName,department) from User where oid=:oid and isDuty=1 and roleCode='staff'");//isDuty=1在职
        map.put("oid",oid);
        boolean nullDept=false;
        if(deptIds.contains("0")) {
            deptIds.remove("0");
            nullDept=true;
        }
        if (deptIds!=null&&deptIds.size()>0){
            if(!nullDept){
                hql.append(" and (department is null or ");
            } else {
                hql.append(" and ");
            }
            hql.append("department not in (:departments)");
            map.put("departments",deptIds);
            if(!nullDept) {
                hql.append(")");
            }
        } else if (nullDept) {
            hql.append(" and department is not null");
        }
        if (!MyStrings.nulltoempty(userName).isEmpty()){
            hql.append(" and userName like:userName");
            map.put("userName","%"+userName+"%");
        }
        if(userId!=null){  //取此人员的下属以及间接下属
            hql.append(" and (userID=:userId or rankUrl like :userID)");
            map.put("userId", userId); //取职工自己
            map.put("userID", "%/" + userId + "/%");  //取本职工的直接以及间接下级
        }
        if (removeUids!=null && !removeUids.isEmpty()) {
            hql.append(" and userID not in (:removeUids)");
            map.put("removeUids", removeUids);
        }
        List<UserDto> list = popedomDao.getListByHQLWithNamedParams(hql.toString(), map);
        return list;
    }

    @Override
    public List<UserDto> getUserDtoByOidAndNoDeptIdsLocking(Integer oid,Integer userId, List<String> deptIds, String userName, List<Integer> removeUids) {
        Map<String,Object> map = new HashMap<>();
        StringBuffer hql=new StringBuffer("select new cn.sphd.miners.modules.system.dto.UserDto(userID, userName,departName,postName,department) from User where oid=:oid and isDuty in('1','9') and roleCode='staff'");//isDuty=1在职
        map.put("oid",oid);
        boolean nullDept=false;
        if(deptIds.contains("0")) {
            deptIds.remove("0");
            nullDept=true;
        }
        if (deptIds!=null&&deptIds.size()>0){
            if(!nullDept){
                hql.append(" and (department is null or ");
            } else {
                hql.append(" and ");
            }
            hql.append("department not in (:departments)");
            map.put("departments",deptIds);
            if(!nullDept) {
                hql.append(")");
            }
        } else if (nullDept) {
            hql.append(" and department is not null");
        }
        if (!MyStrings.nulltoempty(userName).isEmpty()){
            hql.append(" and userName like:userName");
            map.put("userName","%"+userName+"%");
        }
        if(userId!=null){  //取此人员的下属以及间接下属
            hql.append(" and (userID=:userId or rankUrl like :userID)");
            map.put("userId", userId); //取职工自己
            map.put("userID", "%/" + userId + "/%");  //取本职工的直接以及间接下级
        }
        if (removeUids!=null && !removeUids.isEmpty()) {
            hql.append(" and userID not in (:removeUids)");
            map.put("removeUids", removeUids);
        }
        List<UserDto> list = popedomDao.getListByHQLWithNamedParams(hql.toString(), map);
        return list;
    }


    //查找某职工以及某职工直属下级和间接下级的职工id    type 1-包含本职工 2-只有直接下级和间接下级
    @Override
    public Map<String,Object> getUserIds(Integer loginUserId,Integer type){
        Map<String,Object> map = new HashMap<>();
        List<Integer> userIds1 = new ArrayList<>();
        String userIds = null;
        if (loginUserId!=null){
            String hql = "select userID from User where isDuty='1' and roleCode='staff'";
            if (type==2){
                hql+=" and rankUrl like :userIds";
                map.put("userIds","%/"+loginUserId+"/%");
            }else {
                hql+=" and (userID=:userID or rankUrl like :userIds)";
                map.put("userID",loginUserId);
                map.put("userIds","%/"+loginUserId+"/%");
            }
            userIds1 = userDao.getListByHQLWithNamedParams(hql,map);
            for (Integer userId:userIds1) {
                if (userIds==null){
                    userIds = userId.toString();
                }else {
                    userIds = userIds+","+userId;
                }
            }
        }
        Map<String,Object> map1 = new HashMap<>();
        map1.put("userIdsList",userIds1);  //返回list格式
        map1.put("userIdsString",userIds);  //返回string类型
        return map1;
    }

    @Override     //查找某职工以及某职工直属下级和间接下级的职工id    type 1-包含本职工 2-只有直接下级和间接下级 3-查询直属下级
    public List<User> getUserDtos(Integer loginUserId,Integer type){
        Map<String,Object> map = new HashMap<>();
        List<User> users = new ArrayList<>();
        if (loginUserId!=null){
            String hql = "from User where isDuty='1' and roleCode='staff'";
            if (type==2){
                hql+=" and rankUrl like :userIds";
                map.put("userIds","%/"+loginUserId+"/%");
            }else if (type==1){
                hql+=" and (userID=:userID or rankUrl like :userIds)";
                map.put("userID",loginUserId);
                map.put("userIds","%/"+loginUserId+"/%");
            }else {
                hql+=" and leader = :leader";
                map.put("leader",loginUserId.toString());
            }
            users = userDao.getListByHQLWithNamedParams(hql,map);
        }
        return users;
    }

    @Override
    public UserDto getUserDtoByUserId(Integer userId) {
        Map<String,Object> map = new HashMap<>();
        String hql="select new cn.sphd.miners.modules.system.dto.UserDto(userID,userName,departName,postName,department) from User where userID=:userId";
        map.put("userId",userId);
        UserDto userDto = (UserDto) userDao.getByHQLWithNamedParams(hql,map);
        return userDto;
    }

    @Override
    public UserHonePageDto getUserHonePageDtoByUserId(Integer userId) {
        Map<String,Object> map = new HashMap<>();
        String hql="select new cn.sphd.miners.modules.system.dto.UserHonePageDto(userID,userName,departName,mobile,isDuty,gender,imgPath,roleCode) from User where userID=:userId";
        map.put("userId",userId);
        UserHonePageDto userHonePageDto = (UserHonePageDto) userDao.getByHQLWithNamedParams(hql,map);
        return userHonePageDto;
    }

    public List<UserHonePageDto> getManageListByOid(Integer oid) {
//        String hql = "select new cn.sphd.miners.modules.system.dto.UserHonePageDto(userID,userName,departName,mobile,isDuty) from User where oid=:oid and "+User.getWhereIsDuty()+" and roleCode "+RoleTmpl.getManageWhere();
        String hql = "select new cn.sphd.miners.modules.system.dto.UserHonePageDto(userID,userName,departName,mobile,isDuty) from User where oid=:oid and isDuty in(:isDutys) and roleCode in(:roleCodes)";
        Map<String,Object> params=new HashMap<>();
        params.put("oid",oid);
        params.put("isDutys",IsDutys.isDutyListWebCanLogin.getIsDutyList());
        params.put("roleCodes",RoleCodes.manageRoles.getCodeList());
//        hql+=" group by userID";
        ArrayList<UserHonePageDto> users = (ArrayList<UserHonePageDto>) userDao.getListByHQLWithNamedParams(hql,params);
        return users;
    }

    @Override
    public UserHonePageDto getUserHonePageDtoByOidRoleCode(Integer oid, String roleCode) {
//        String hql="select new cn.sphd.miners.modules.system.dto.UserHonePageDto(userID,userName,departName,mobile,isDuty) from User where oid=:oid and "+User.getWhereIsDuty()+" and roleCode=:roleCode";
        String hql="select new cn.sphd.miners.modules.system.dto.UserHonePageDto(userID,userName,departName,mobile,isDuty) from User where oid=:oid and isDuty in(:isDutys) and roleCode=:roleCode";
        Map<String,Object> params=new HashMap<>();
        params.put("oid",oid);
        params.put("isDutys",IsDutys.isDutyListWebCanLogin.getIsDutyList());
        params.put("roleCode",roleCode);
        UserHonePageDto userHonePageDto= (UserHonePageDto) userDao.getByHQLWithNamedParams(hql,params);
        return userHonePageDto;
    }

    @Override
    @Caching(evict = {
        @CacheEvict(value = "orgWithUserTree", allEntries=true),
        @CacheEvict(value = "getUserByRoleCode", key="#o.id+\"smallSuper\""),
        @CacheEvict(value = "getUserByRoleCode", key="#o.id+\"general\""),
        @CacheEvict(value = "getUserByRoleCode", key="#o.id+\"finance\""),
        @CacheEvict(value = "getUserByRoleCode", key="#o.id+\"sale\"")
    })
    public User outManage(Organization o, Integer manageId, String phone, String userName) {
        User user = userDao.get(manageId);
        //把老总务个人信息复制出来新增
        User oldUser = new User();
//        oldUser.setLogonName(user.getLogonName());
        oldUser.setMobile(user.getMobile());
//        oldUser.setLogonPwd(user.getLogonPwd());
        oldUser.setOrganization(o);
        oldUser.setOid(o.getId());
        oldUser.setCreateTime(user.getCreateTime());
        oldUser.setIsDuty("2");//离职
        oldUser.setLeader(user.getLeader());
        oldUser.setLeaderName(user.getLeaderName());
        oldUser.setLogonState(0L);
        oldUser.setOid(o.getId());
        oldUser.setLoginStatus(0);
        oldUser.setUserName(user.getUserName());
        oldUser.setRoleID(user.getRoleID());
        oldUser.setBirthday(user.getBirthday());
        oldUser.setEdu(user.getEdu());
        oldUser.setGender(user.getGender());
        oldUser.setOnDutyDate(user.getOnDutyDate());
        oldUser.setOffDutyDate(new Date());
        oldUser.setPostName(user.getPostName());
        oldUser.setDepartName(user.getDepartName());
//        oldUser.setUserType(user.getUserType());
        oldUser.setOrdinaryEmployees(user.getOrdinaryEmployees());
//        oldUser.setTokenId(user.getTokenId());
        oldUser.setSuperId(user.getSuperId());
        oldUser.setCreateName(user.getCreateName());
        oldUser.setCreator(user.getCreator());
        oldUser.setRealName(user.getRealName());
        oldUser.setStatus(user.getStatus());
        oldUser.setHandleTime(new Date());
        oldUser.setDefault(false);
        this.addUser(oldUser, o);

        user.setHandleTime(new Date());
//        user.setLogonPwd(this.getLogonPwdByPhone(phone));
//        user.setLogonName(phone);
        AuthAcc authAcc= authService.newEnabledAccInOrg(phone,userName, o);// 生成登陆账号
        user.setAccId(authAcc.getId());
        user.setMobile(phone);

        user.setUserName(userName);
        user.setIsDuty("1");//在职
        user.setOnDutyDate(new Date());

        //需要清空的
        user.setBirthday(null);
        user.setEdu(null);
        user.setGender(null);
        user.setRealName(null);
        user.setStatus(null);//1-禁用
        user.setMarry(null);//已婚未婚
        user.setNation(null);//民族
        user.setHomeAddress(null);//家庭住址
        user.setEmail(null);
        user.setQq(null);
        user.setPoliticalStatus(null);
        user.setNativePlace(null);
        user.setIdCard(null);
        userDao.update(user);

        List<User> users=this.getUsersByLeader(user.getOid(),user.getUserID().toString());
        for (User u:users){
            u.setLeaderName(user.getUserName());
            userDao.update(u);
        }

        //将老总务的请假申请记录带走
        List<PersonnelLeave> personnelLeaveList = offDutyService.getLeaveListByUser(user.getUserID());
        for (PersonnelLeave p : personnelLeaveList) {
            p.setUser(oldUser);
            userMessageService.updatePersonnelLeave(p);
        }

        //将老总务的加班申请记录带走
        List<PersonnelOvertime> personnelOvertimeList = offDutyService.getOverTimeListByUser(user.getUserID());
        for (PersonnelOvertime p : personnelOvertimeList) {
            p.setUser(oldUser);
            userMessageService.updatePersonnelOvertime(p);
        }

        //将老总务的报销申请记录带走
        List<PersonnelReimburse> personnelReimburseList = offDutyService.getReimburseListByUser(user.getUserID());
        for (PersonnelReimburse p : personnelReimburseList) {
            p.setUser(oldUser);
            personnelReimburseService.updatePersonnelReimburse(p);
        }

        //将老总务发起和接收到的申请结果消息带走
        List<UserMessage> userMessageList = offDutyService.getMessageListByUser(user.getUserID(),null);
        for (UserMessage u : userMessageList) {
            u.setUser(oldUser);
            if (u.getReceiveUserId().equals(user.getUserID())) {
                //接收消息人id与总务之前id相同，接到消息的人是他自己，把收消息的记录也一并带走
                u.setReceiveUserId(oldUser.getUserID());//收到消息的人
            }
            userMessageService.updateUserMassage(u);
        }

        //将老高管的工作经历带走
        for (PersonnelOccupation po:user.getPersonnelOccupations()){
            po.setUser(oldUser);
            personalService.updatePersonnelOccupation(po);
        }

        //将老高管的教育背景带走
        for (PersonalEducation pe:user.getPersonalEducations()){
            pe.setUser(oldUser);
            personalService.updatePersonalEducation(pe);
        }

        //将老高管的薪资情况带走
        for (PersonnelSalaryLog ps:user.getPersonnelSalaryLogUser()){
            ps.setUser(oldUser);
            personalService.updatePersonnelSalaryLog(ps);
        }

        //将老高管的奖惩情况带走
        for (PersonalRewardPunishment pr:user.getPersonalRewardPunishments()){
            pr.setUser(oldUser);
            personalService.updatePersonalRewardPunishment(pr);
        }

        //将老高管的评价情况带走
        for (PersonalAssessment pa:user.getPersonalAssessments()){
            pa.setUser(oldUser);
            personalService.updatePersonalAssessment(pa);
        }
        return user;
    }

    @Override
    @CacheEvict(value = "orgWithUserTree", allEntries=true)
    @Caching(put = {
        @CachePut(value = "getUserByRoleCode", condition = "\"1\".equals(#agentType)", key="#o.id+\"agentAccounting\""),
        @CachePut(value = "getUserByRoleCode", condition = "\"0\".equals(#agentType)", key="#o.id+\"accounting\"")
    })
    public User addAccountingUser(User loginUser,Organization o, String phone, String agentType, String userName,HttpServletRequest request,AuthInfoDto authInfo) {
        User u = new User();
//        u.setLogonName(phone);
        u.setMobile(phone);
//        u.setLogonPwd(this.getLogonPwdByPhone(phone));//获取该有的登录密码
        u.setOrganization(o);
        u.setCreateTime(new Date());
        u.setCreateName(loginUser.getUserName());
        u.setCreator(loginUser.getCreator());
        u.setLeader(String.valueOf(loginUser.getUserID()));
        u.setLeaderName(loginUser.getUserName());
        u.setLogonState(0L);
        u.setOid(o.getId());
        u.setLoginStatus(0);
        u.setUserName(userName);
        u.setHandleTime(new Date());

        u.setOrdinaryEmployees(0);//非普通员工
        u.setAgentType(agentType);//会计代理状态 1-代理，0-不代理

//        u.setUserType(this.getUserTypeByCode("accounting"));

        if ("1".equals(agentType)) {
            u.setIsDuty("3");//可登录的代理人员
            u.setManagerCode("agentAccounting");//高管code
            u.setRoleCode("agentAccounting");//自己权限code
            u.setRoleName("会计事务的负责人(代理)");
            u.setSuperId(o.getSuperId());
            this.addUser(u, o);

        }else {
            u.setSuperId(o.getSuperId());
            u.setIsDuty("1");//正式职工
            u.setManagerCode("accounting");//高管code
            u.setRoleCode("accounting");//自己权限code
            u.setRoleName("会计事务的负责人");
            String rankUrl ="/" + loginUser.getUserID();//更改后的从属关系
            u.setRankUrl(rankUrl);
            this.addUser(u, o);
//            if (o.getInitState().equals("1")) { //机构已正式启用
//                userPopedomService.saveUserPopedomByCode("accounting", u);//给会计赋权限
//            }
            //新增薪资宝用户
//            salaryAction.addSalaryUser(u);
        }

        if (o.getInitState().equals("1")) { //机构已正式启用
            User finance=getUserByRoleCode(o.getId(),"finance");  // 财务高管   1.307初始化 会计部分   会计初始化完成 标志为 财务初始化完成，  完成前只有 初始化-会计模块
            UserPopedom userPopedom=userPopedomService.getUserPopedomByUserIdMid(finance.getUserID(),"vh");
            if (userPopedom==null) {  //财务 初始化完成
                userPopedomService.saveUserPopedomByCode(u.getRoleCode(), u);//给会计赋 对应code权限
            }else {   //财务初始化 未完成  只给 初始化-会计菜单
                userPopedomService.saveUserPopedomByCode("initialBeforeAccounting", u);//给会计赋 对应code权限
            }
        }

//        String cont="我公司将使用软件系统进行管理，请点击下载手机端，安装后使用您"+phone+"手机号登录，并按页面提示操作。用电脑登录"+System.getProperty("BaseUrl")+"也可进行同样操作。\n" +
//                "有疑问可给我"+loginUser.getMobile()+"回电话。";
//        sendMessageService.sendMessage(u.getUserID(),o.getId(),phone,cont);//发短信

        // 短信重构 2024/5/23 李旭更改
        String phoneNo=phone;
        String url = GetLocalIPUtils.getRootPath(request);
        String roleName="会计事务的负责人";
        Map<String, String> params = new HashMap<String, String>(4) {{
            put("phoneNo", phoneNo);
            put("url", url);
            put("roleName", roleName);
            put("mobile", loginUser.getMobile());
        }};
        smsService.sendMessage(SmsService.SendType.role, phoneNo, authInfo, TemplateService.SmsContentTemplate.addManage, params, null);

        return u;
    }

    @Override
    @CacheEvict(value = "orgWithUserTree", allEntries=true)
    @CachePut(value = "getUserByRoleCode", key="#o.id+#manageName")
    public User addManage(User loginUser, Organization o, String phone, String manageName, String userName, HttpServletRequest request, AuthInfoDto authInfo) {
        User u = new User();
//        u.setLogonPwd(this.getLogonPwdByPhone(phone));//获取该有的登录密码
//        u.setLogonName(phone);
        u.setMobile(phone);
        u.setOrganization(o);
        u.setCreateTime(new Date());
        u.setCreateName(loginUser.getUserName());
        u.setCreator(loginUser.getUserID());
        u.setIsDuty("1");

        u.setLeader(loginUser.getUserID().toString());
        String rankUrl ="/" + loginUser.getUserID();//更改后的从属关系
        u.setRankUrl(rankUrl);

        User superUser=this.getUserByRoleCode(o.getId(),"smallSuper");
        if(superUser==null){
            superUser=this.getUserByRoleCode(o.getId(),"super");
        }

        u.setLeaderName(superUser.getUserName());
        u.setLogonState(0L);
        u.setOid(o.getId());
        u.setLoginStatus(0);
        u.setUserName(userName);
        u.setHandleTime(new Date());
        u.setManagerCode(manageName);//高管code
        u.setRoleCode(manageName);//自己权限code
        String xx="XX";
        String roleCode=manageName;
        if (manageName.equals("general")) {
            u.setRoleName("总务权限的分配者");
            xx="总务";
        }
        if (manageName.equals("finance")) {
            u.setRoleName("财务权限的分配者");
            roleCode="initialBeforeFinance" ;//1.307初始化财务 ，需求说财务初始化完成前， 只有一个菜单。  lixu 2024/10/23
            xx="财务";
        }
        if (manageName.equals("sale")) {
            u.setRoleName("销售权限的分配者");
            roleCode="initialBeforeSale" ;//1.343初始化销售 ，需求说销售初始化完成前， 只有一个菜单。  lixu 2025/7/15
            xx="销售";
        }
//        if (manageName.equals("accounting")) {
//            u.setRoleName("会计");
//            u.setAgentType("0");//代理状态 0-非代理 1-代理
//        }
//        if (manageName.equals("agentAccounting")) {
//            u.setRoleName("代理会计");
//            u.setAgentType("1");//代理状态 0-非代理 1-代理
//            u.setIsDuty("3");
//        }
        u.setOrdinaryEmployees(0);//非普通员工
//        String cont="我公司将使用软件系统进行管理，请点击下载手机端，安装后使用您"+phone+"手机号登录，并按页面提示操作。用电脑登录"+System.getProperty("BaseUrl")+"也可进行同样操作。\n" +
//                "有疑问可给我"+loginUser.getMobile()+"回电话。";

//        String cont="我公司将使用Wonderss企业管理系统，"+xx+"的初始化需由您进行。请使用您"+phone+"手机号登录电脑端网址"+System.getProperty("BaseUrl")+"，之后按页面提示操作。\n" +
//                "有疑问可拨打我手机"+loginUser.getMobile()+"。";
//        sendMessageService.sendMessage(u.getUserID(),superUser.getOid(),phone,cont);//发短信

        // 短信重构 2024/5/23 李旭更改
        String phoneNo=phone;
        String url = GetLocalIPUtils.getRootPath(request);
        String roleName=xx;
        Map<String, String> params = new HashMap<String, String>(4) {{
            put("phoneNo", phoneNo);
            put("url", url);
            put("roleName", roleName);
            put("mobile", loginUser.getMobile());
        }};
        smsService.sendMessage(SmsService.SendType.role, phoneNo, authInfo, TemplateService.SmsContentTemplate.addManage, params, null);

        if (!u.getRoleCode().equals("browse")){
            u.setSuperId(o.getSuperId());//除了浏览者都发工资
        }
        u.setSuperId(o.getSuperId());
        this.addUser(u, o);
        if (o.getInitState().equals("1")) { //机构已正式启用
            userPopedomService.saveUserPopedomByCode(roleCode, u);//给高管赋权限（所有高管都 赋予权限）
        }else {
            //没正式启用 只有总务新增 并附上 初始化前总务权限
            if ("general".equals(u.getRoleCode())){
                userPopedomService.saveUserPopedomByCode("initialBeforeGeneral", u);//给初始化前总务赋权限
            }
        }

//        //新增薪资宝用户
//        salaryAction.addSalaryUser(u);
        return u;
    }

    @Override
    @CacheEvict(value = "orgWithUserTree", allEntries=true)
    @CachePut(value = "getUserByRoleCode", key="#oid+\"smallSuper\"")
    public User addSmallSuper(User loginUser, Integer oid, Integer passiveUserId,Map<String,Object> map,HttpServletRequest request,AuthInfoDto authInfo) {
        User passiveUser = userService.getUserByID(passiveUserId);//被选为高管的人

        if (loginUser.getRoleCode().equals("super")) {

            Organization o = orgService.getByOid(oid,true,false);
            User superUser=this.getUserByRoleCode(oid,"super");

            passiveUser.setManagerCode("smallSuper");
            passiveUser.setRoleCode("smallSuper");
            passiveUser.setRoleName("总经理");

            rolePrincipalHistoryService.saveRolePrincipalHistoryBy(passiveUser, passiveUser, "smallSuper");//保存高管身份变更历史

            o.setExistenceSmallSuper("1");//机构设置了小超管
            orgService.updateOrg(o);

//                approvalProcessService.updateApprovalProcessListByToUser(user.getUserID(), u.getUserID());//替换审批过程
            approvalService.updateApprovalFlowByUserId(superUser.getUserID(), passiveUser.getUserID(),passiveUser.getUserName(),"总经理");//替换审批设置
//                userService.updateUserListByLeaderId(user.getUserID(),u.getUserID(),u.getUserName());//更换直接上级名字

            if (1==passiveUser.getOrdinaryEmployees()){
                passiveUser.setOrdinaryEmployees(0);// 非普通员工
                userPopedomService.saveUserPopedomByCode("seniorStaff",passiveUser);
            }
            passiveUser.setLeader(superUser.getUserID().toString());
            passiveUser.setLeaderName(superUser.getUserName());
            passiveUser.setRankUrl("/"+superUser.getUserID()+"/");
            userService.updateUser(passiveUser);

            List<User> userList=userService.getDirectLowerGrade("/"+passiveUserId+"/",oid);//直系下级关系
            for (User ul:userList){
                String url=ul.getRankUrl();
                String middleUrl= StringUtils.substringAfter(url, "/"+passiveUserId);//从之后截取
                String newUrl="/"+superUser.getUserID()+middleUrl;
                ul.setRankUrl(newUrl);//新的从属关系
                userService.updateUser(ul);
            }

            if ("0".equals(o.getInitState())) {
                userPopedomService.saveUserPopedomByCode("initialBeforeSmallSuper", passiveUser);//机构初始化前 总经理权限
            }else {
                userPopedomService.saveUserPopedomByCode("smallSuper",passiveUser);//给总经理赋权限
            }
            userService.getPopedomStringByUser(passiveUser,true);//变更权限时用。
            popedomTaskService.updateUserBadgeNumber(passiveUser);//重新计算角标

//            String cont="我公司将使用电脑端网址为"+System.getProperty("BaseUrl")+"的管理系统。请使用"+passiveUser.getMobile()+"手机号登录系统，之后按页面提示操作。有疑问可给我"+superUser.getMobile()+"回电话。";
//            String cont="我公司将使用Wonderss企业管理系统，您是全权负责人。请使用您"+passiveUser.getMobile()+"手机号登录电脑端网址"+System.getProperty("BaseUrl")+"，之后按页面提示操作。有疑问可拨打我手机"+superUser.getMobile()+"。";
//            sendMessageService.sendMessage(passiveUser.getUserID(),oid,passiveUser.getMobile(),cont);//发短信

            // 短信重构 2024/5/23 李旭更改
            String phoneNo=passiveUser.getMobile();
            String url = GetLocalIPUtils.getRootPath(request);
            Map<String, String> params = new HashMap<String, String>(3) {{
                put("phoneNo", phoneNo);
                put("url", url);
                put("mobile", loginUser.getMobile());
            }};
            smsService.sendMessage(SmsService.SendType.role, phoneNo, authInfo, TemplateService.SmsContentTemplate.addSmallSuper, params, null);

            String messageCont="您已在系统中被指定为"+passiveUser.getRoleName()+"，请及时处理与之有关的各项工作！";
            userSuspendMsgService.saveUserSuspendMsg(1, messageCont, messageCont,"操作时间  "+superUser.getUserName()+" " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), passiveUserId, "", passiveUserId);//推送我的消息

//                List<User> userList= userService.getUsersByMasterUserID(passiveUserId);// 包含员工本身 的身份列表
//                clusterMessageSendingOperations.convertAndSendToUser(passiveUserId.toString(),"/changeRolePrincipals",null,null,u.getOid(),u.getOrganization().getName(),JSON.toJSONString(userList));
            map.put("status", 1);//成功
            map.put("smallSuper", passiveUser);//小超管信息
        } else {
            map.put("status", 2);//你不是超管
        }
        return passiveUser;
    }


    @Override
    @CacheEvict(value = "orgWithUserTree", allEntries=true)
    @CachePut(value = "getUserByRoleCode", key="#oid+\"smallSuper\"")
    public User updateOrgSmallSuper(User loginUser,Integer oid,Integer oldUserId, Integer newUserId,HttpServletRequest request,AuthInfoDto authInfo) {
        User oldUser=userService.getUserByID(oldUserId);
        User passiveUser=userService.getUserByID(newUserId);

        Organization o = orgService.getByOid(oid,true,false);
        User superUser=this.getUserByRoleCode(oid,"super");

        passiveUser.setRoleCode("smallSuper");
        passiveUser.setRoleName("总经理");

        oldUser.setManagerCode("general");
        oldUser.setRoleCode("staff");
        oldUser.setRoleName(null);
        userService.updateUser(oldUser);

        List<RolePrincipalHistory> rolePrincipalHistories=rolePrincipalHistoryService.getRolePrincipalHistoriesByOidRoleCode(oid,"smallSuper");
        if (rolePrincipalHistories.size()>0){
            RolePrincipalHistory rolePrincipalHistory=rolePrincipalHistories.get(0);
            rolePrincipalHistory.setUpdateName(loginUser.getUserName());
            rolePrincipalHistory.setUpdator(loginUser.getUserID());
            rolePrincipalHistory.setUpdateDate(new Date());
            rolePrincipalHistoryService.updateRolePrincipalHistory(rolePrincipalHistory);
        }

        rolePrincipalHistoryService.saveRolePrincipalHistoryBy(passiveUser, passiveUser, "smallSuper");//保存总经理变更记录

        approvalService.updateApprovalFlowByUserId(oldUserId, passiveUser.getUserID(),passiveUser.getUserName(),null);//替换审批设置

        userPopedomService.deleteUserDefaultPopedomByRoleCode(oldUserId,"smallSuper"); // 删除原总经理 总经理权限

        if (1==passiveUser.getOrdinaryEmployees()){
            passiveUser.setOrdinaryEmployees(0);// 非普通员工
            userPopedomService.saveUserPopedomByCode("seniorStaff",passiveUser);
        }
        passiveUser.setLeader(superUser.getUserID().toString());
        passiveUser.setLeaderName(superUser.getUserName());
        passiveUser.setRankUrl("/"+superUser.getUserID()+"/");
        userService.updateUser(passiveUser);

        List<User> userList=userService.getDirectLowerGrade("/"+newUserId+"/",oid);//直系下级关系
        for (User ul:userList){
            String url=ul.getRankUrl();
            String middleUrl= StringUtils.substringAfter(url, "/"+newUserId);//从之后截取
            String newUrl="/"+superUser.getUserID()+middleUrl;
            ul.setRankUrl(newUrl);//新的从属关系
            userService.updateUser(ul);
        }

        if ("0".equals(o.getInitState())) {
            userPopedomService.saveUserPopedomByCode("initialBeforeSmallSuper", passiveUser);//机构初始化前 总经理权限
        }else {
            userPopedomService.saveUserPopedomByCode("smallSuper",passiveUser);//给总经理赋权限
        }
        userService.getPopedomStringByUser(passiveUser,true);//变更权限时用。
        popedomTaskService.updateUserBadgeNumber(passiveUser);//重新计算角标
        userService.getPopedomStringByUser(oldUser,true);//变更权限时用。
        popedomTaskService.updateUserBadgeNumber(oldUser);//重新计算角标

//        String cont="我公司将使用Wonderss企业管理系统，您是全权负责人。请使用您"+passiveUser.getMobile()+"手机号登录电脑端网址"+System.getProperty("BaseUrl")+"，之后按页面提示操作。有疑问可拨打我手机"+superUser.getMobile()+"。";
//        sendMessageService.sendMessage(passiveUser.getUserID(),oid,passiveUser.getMobile(),cont);//发短信
        // 短信重构 2024/5/23 李旭更改
        String phoneNo=passiveUser.getMobile();
        String url = GetLocalIPUtils.getRootPath(request);
        Map<String, String> params = new HashMap<String, String>(3) {{
            put("phoneNo", phoneNo);
            put("url", url);
            put("mobile", loginUser.getMobile());
        }};
        smsService.sendMessage(SmsService.SendType.role, phoneNo, authInfo, TemplateService.SmsContentTemplate.addSmallSuper, params, null);

        return  passiveUser;
    }

    @Override
    @CacheEvict(value = "orgWithUserTree", allEntries=true)
    @CachePut(value = "getUserByRoleCode", key="#oid+\"smallSuper\"")
    public void noSmallSuper(User loginUser, Integer oldUserId, Integer oid) {
        User oldUser = userService.getUserByID(oldUserId); // 原总经理  要变成普通员工
        oldUser.setRoleCode("staff");
        oldUser.setRoleName(null);
        userService.updateUser(oldUser);//更新废弃总经理

        Organization organization = orgService.getByOid(oid,true,false);
        organization.setExistenceSmallSuper("0");//不设置小超管
        orgService.updateOrg(organization);

        User superUser=this.getUserByRoleCode(oid,"super");

        approvalService.updateApprovalFlowByUserId(oldUserId, superUser.getUserID(), superUser.getUserName(),"超管");//替换审批设置

        List<RolePrincipalHistory> rolePrincipalHistories=rolePrincipalHistoryService.getRolePrincipalHistoriesByOidRoleCode(oid,"smallSuper");
        if (rolePrincipalHistories.size()>0){
            RolePrincipalHistory rolePrincipalHistory=rolePrincipalHistories.get(0);
            rolePrincipalHistory.setUpdateName(loginUser.getUserName());
            rolePrincipalHistory.setUpdator(loginUser.getUserID());
            rolePrincipalHistory.setUpdateDate(new Date());
            rolePrincipalHistoryService.updateRolePrincipalHistory(rolePrincipalHistory);
        }

        userPopedomService.deleteUserDefaultPopedomByRoleCode(oldUserId, "smallSuper");  // 删除总经理权限
        userService.getPopedomStringByUser(oldUser,true);//变更权限时用。
        popedomTaskService.updateUserBadgeNumber(oldUser);//重新计算角标



//            List<User> users= userService.getUsersByMasterUserID(smallSuper.getMasterUserID());// 包含员工本身 的身份列表
//            clusterMessageSendingOperations.convertAndSendToUser(smallSuper.getMasterUserID().toString(),"/changeRolePrincipals",null,null,superSuper.getOid(),superSuper.getOrganization().getName(), JSON.toJSONString(users));

    }

    @Override
    public User addTemporaryAdmin(User loginUser,Organization organization, String userName, String mobile,HttpServletRequest request,AuthInfoDto authInfo) {
        User user1=new User();
        user1.setUserName(userName);
        user1.setMobile(mobile);
        user1.setOrdinaryEmployees(0);//是否为普通员工 1-普通员工
        loginUser = userService.getUserByID(loginUser.getUserID());// 当前操作人只会是董事长
        String rankUrl="/"+loginUser.getUserID();
        user1.setRankUrl(rankUrl);//从属关系
        user1.setLeader(loginUser.getUserID().toString());
        user1.setLeaderName(loginUser.getUserName());//从属于某位领导
        user1.setCreateTime(new Date());//创建时间
        user1.setCreator(loginUser.getUserID());//创建者
        user1.setCreateName(loginUser.getUserName());//创建者
        user1.setStatus("2");////状态：1-禁用 其它-启用
        user1.setIsDuty("1");//是否在职  1表示在职，2表示离职
        user1.setOrganization(organization);
        user1.setOid(organization.getId());
        user1.setDefault(false);
        user1.setManagerCode("general");  //临时总务高管 的职工导入权限
        user1.setRoleCode("temporary");  //临时管理员权限   导入完成后 要改成 staff  并删除 目前权限

        user1.setSuperId(organization.getSuperId());
        userService.addUser(user1, organization);

        userPopedomService.saveUserPopedomByCode("temporary", user1);//给临时管理员附上权限


//        String sms="我公司将使用电脑端网址为"+System.getProperty("BaseUrl")+"的管理系统。请使用你"+user1.getMobile()+"手机号登录系统，之后将职工名单导入，并按页面提示操作。\n" +
//                "有疑问可给我"+loginUser.getMobile()+"回电话。";
//
//        sendMessageService.sendMessage(user1.getUserID(),organization.getId(),user1.getMobile(),sms);

        // 短信重构 2024/5/22 李旭更改
        String phoneNo=mobile;
        String url = GetLocalIPUtils.getRootPath(request);
        String meMobile=loginUser.getMobile();
        Map<String, String> params = new HashMap<String, String>(3) {{
            put("phoneNo", phoneNo);
            put("url", url);
            put("mobile", meMobile);
        }};
        smsService.sendMessage(SmsService.SendType.role, phoneNo, authInfo, TemplateService.SmsContentTemplate.addTemporary, params, null);
        return  user1;
    }

    @Override
    public List<User> getMobileUserListByAcc(AuthAcc acc) {
        String hql = "from User where accId=:accId and userType=0 and isDuty in(:isDuty) order by masterUserID";
        Map<String,Object> params=new HashMap<>();
        params.put("accId",acc.getId());
        params.put("isDuty",IsDutys.isDutyListAppCanLogin.getIsDutyList());
        return userDao.getListByHQLWithNamedParams(hql,params);
    }

    @Override
    public void updateUserByAccMobileChange(AuthAcc acc) {
        List<User> userList=getMobileUserListByAcc(acc);
        String updateName = null;
        for (User u:userList) {
            if (u.getMasterUserID()!=null) {
                updateName = u.getUserName();
                userHistoryService.addUserHistoryByUser(u, "3", false);//保存历史记录
            }
            u.setMobile(acc.getMobile());
            u.setVersionNo(u.getVersionNo() == null ? 1 : u.getVersionNo() + 1);
            u.setUpdateName(updateName);
            u.setUpdateDate(new Date(System.currentTimeMillis()));
            userService.updateUser(u);//wyu：更新缓存需要用接口调。
            if (u.getMasterUserID()!=null) { //是主 员工 才产生修改记录  身份数据不记录 人员信息修改历史
                userHistoryService.addUserHistoryByUser(u, "3", true);//保存历史记录
            }
        }
    }

    @Override
    public List<User> getUserListByOidRoleCode(Integer oid, String roleCode,String userName,PageInfo pageInfo) {

        List<User> users = new ArrayList<>();
        int i = 1;
        while (i>=0){
            Map<String,Object> params=new HashMap<>();
            String hql=" from User where oid=:oid and roleCode=:roleCode";
            if (i==1){
                hql+=" and isDuty in ('1','9')";
            }else {
                hql+=" and isDuty='2'";
            }
            params.put("roleCode",roleCode);
            params.put("oid",oid);
            if (!"".equals(userName) && userName!=null){
//                hql+=" and userName like:userName";  //模糊查询的
//                params.put("userName","%"+userName+"%");
                hql+=" and userName=:userName";   //直接查询就可以，无需模糊查询
                params.put("userName",userName);
            }
            hql+=" order by  convert(userName, 'gbk') asc";
            List<User> users1=userDao.getListByHQLWithNamedParams(hql,params,pageInfo);
            if (users1.size()>0){
                users.addAll(users1);
            }
            i--;
        }
//        String hql=" from User where isDuty in ('1','9') and oid=:oid and roleCode=:roleCode";
//        Map<String,Object> params=new HashMap<>();
//        params.put("roleCode",roleCode);
//        params.put("oid",oid);
//        if (!"".equals(userName) && userName!=null){
//            hql+=" and userName like:userName";
//            params.put("userName","%"+userName+"%");
//        }
//        hql+=" order by  convert(userName, 'gbk') asc";
//        List<User> users=userDao.getListByHQLWithNamedParams(hql,params,pageInfo);

        return users;
    }

    @Override
    public void deleteStaffUserListByOid(Integer oid) {
        String hql=" delete from User where roleCode='staff' and oid=:oid";
        Map<String,Object> params=new HashMap<>();
        params.put("oid",oid);
        userDao.queryHQLWithNamedParams(hql, params);
    }

    @Override
    public List<User> getAllUserByDepartment(Integer id) {
        return userDao.getListByHQL(" from User o where o.department=?0",id+"");
    }

    @Override
    public void updateUserListByLeaderId(Integer oldId, Integer newId, String newUserName) {
        String hql="update User set leader=:newUserId,leaderName=:newUserName where leader=:oldUserId and roleCode!='smallSuper'";
        Map<String,Object> map = new HashMap<>();
        map.put("newUserId",newId.toString());
        map.put("oldUserId",oldId.toString());
        map.put("newUserName",newUserName);
        userDao.queryHQLWithNamedParams(hql,map);
    }

    @Override
    public List<UserLoginDto> getUserList(Long accId, PageInfo pageInfo) {
        return getUserList(accId, User.personCodes(), User.getIsDutyListWebCanLogin(), Organization.getAllSearchCode(), pageInfo);
    }

    /**
     * <AUTHOR>
     * @description 查询账号可登录机构
     * @date Create at 2023/9/14 13:24
     * @param accId 账号id
     * @param roleCodes roleCodes集合，=null:不设条件，空集合:字段 is null
     * @param isDuties isDuty集合，=null:不设条件，空集合:字段 is null
     * @param orgCodes org.code集合，=null:不设条件，空集合:字段 is null
     * @param pageInfo 分页，=null全部输出。
     * @return
     */
    @Override
    public List<UserLoginDto> getUserList(Long accId, List<String> roleCodes, List<String> isDuties, List<String> orgCodes, PageInfo pageInfo) {
        StringBuffer hql = new StringBuffer("select new cn.sphd.miners.modules.system.dto.UserLoginDto(u.userID,u.oid,o.name,u.mobile,u.isDefault,u.leader,u.roleCode,u.msgCount,u.forumCount,o.code,o.state) from User u join u.organization o where u.accId=:accId and u.userType=0 and u.leader is not null");
        Map<String,Object> params=new HashMap<String,Object>();
        params.put("accId",accId);
        if(roleCodes!=null) {
            if(roleCodes.isEmpty()) {
                hql.append(" and u.roleCode is null");
            } else {
                hql.append(" and u.roleCode in(:roleCode)");
                params.put("roleCode", roleCodes);
            }
        }
        if(isDuties!=null) {
            if(isDuties.isEmpty()) {
                hql.append(" and u.isDuty is null");
            } else {
                hql.append(" and u.isDuty in(:isDuty)");
                params.put("isDuty", isDuties);
            }
        }
        if(orgCodes!=null) {
            if(orgCodes.isEmpty()) {
                hql.append(" and o.code is null");
            } else {
                hql.append(" and o.code in(:orgCode)");
                params.put("orgCode", orgCodes);
            }
        }
        hql.append(" order by u.isDefault desc,u.oid");
        return userDao.getListByHQLWithNamedParams(hql.toString(),params,pageInfo);
    }

    @Override
    public Long getUserCount(Long accId, List<String> isDuties) {
        String hql = "select count(*) from User where accId=:accId and leader is not null and roleCode in(:personCodes) and isDuty in(:isDuty)";
        Map<String,Object> params=new HashMap<String,Object>();
        params.put("accId",accId);
        params.put("personCodes",RoleCodes.personCodes.getCodeList()); //User.personCodes()
        params.put("isDuty",isDuties);
        return (Long) userDao.getByHQLWithNamedParams(hql,params);
    }

    @Override
    public List<UserHonePageDto> getUserUserHonePageDtoListByOid(Integer oid, String isDuty) {
        String hql = "select new cn.sphd.miners.modules.system.dto.UserHonePageDto(submitState,userID,userName,departName,mobile,isDuty,gender) from User where roleCode not like '%super%' and oid=:oid ";
        Map<String,Object> params=new HashMap<>();
        params.put("oid",oid);
        if (isDuty!=null&&!isDuty.equals("")){
            hql+=" and isDuty=:isDuty";
            params.put("isDuty",isDuty);
        }
        ArrayList<UserHonePageDto> users = (ArrayList<UserHonePageDto>) userPopedomDao.getListByHQLWithNamedParams(hql,params);
        return users;
    }

    /**
     * 已经不需要调用接口方法更新用户角标数量，popedomTaskService.updateUserBadgeNumberWithCode方法会自动更新
     * <AUTHOR>
     * @since 2019/7/16 10:52
     */
    @Override
    public User changeMsgCount(User user, Integer value) {
//        if(value!=null && value!=0) {
//            mqMobileDeviceService.changeMsgCount(user.getMobile(),value);
//            user = userDao.incAttr(user,"msgCount", value);
//        }
        return user;
    }

    @Override
    public User updateMsgCount(Integer u, Integer value) {
        User user = getUserByID(u);
        if(value!=null) {
            Integer change = user.getMsgCount()!=null?value-user.getMsgCount():value;//求差
            if(!change.equals(0)) {
                mqMobileDeviceService.changeMsgCount(user.getMobile(), change);
            }
            user.setMsgCount(value);
            userDao.update(user);
        }
        return user;
    }

    @Override
    public Integer getMsgCountByMobile(String mobile) {
        String hql = "select coalesce(sum(msgCount),0) from User where mobile=:mobile and isDuty in(:isDutys)"; //+User.getIsDutyCanLogin();
        HashMap<String, Object> params = new HashMap<>();
        params.put("mobile", mobile);
        params.put("isDutys", IsDutys.isDutyListAllCanLogin.getIsDutyList());
        return ((Long) userDao.getByHQLWithNamedParams(hql, params)).intValue();
    }

    /**
     * 查询各种人员的总人数，返回数字
     * @param oid  机构id
     * @param isDuty   //是否在职  1表示在职，2表示离职 ,3可登录的代理人员 4-外部浏览者（手机端超管分配）
     * @param ordinaryEmployees   是否为普通员工 1-普通员工
     * @return
     */
    public Integer getNumberByKinds(Integer oid,String isDuty,Integer ordinaryEmployees){
        Map<String,Object> params=new HashMap<>();
        String hql="select count(id) from User where oid=:oid and roleCode='staff'";
        if (!MyStrings.nulltoempty(isDuty).isEmpty()){
            hql+=" and isDuty=:isDuty";
            params.put("isDuty",isDuty);
        }
        if (ordinaryEmployees!=null){
            if (1==ordinaryEmployees){
                hql+=" and ordinaryEmployees=1"; //是否为普通员工 1-普通员工
            }else if (2==ordinaryEmployees){
                hql+=" and ordinaryEmployees!=1";  //是否为普通员工 1-普通员工
            }
        }
        params.put("oid",oid);
        Long result = (Long) userDao.getByHQLWithNamedParams(hql,params);
        return result.intValue();
    }


    public Integer getNumberByKindsLocking(Integer oid,Integer ordinaryEmployees){
        Map<String,Object> params=new HashMap<>();
        String hql="select count(id) from User where oid=:oid and roleCode='staff' and isDuty in('1','9')";
        if (ordinaryEmployees!=null){
            if (1==ordinaryEmployees){
                hql+=" and ordinaryEmployees=1"; //是否为普通员工 1-普通员工
            }else if (2==ordinaryEmployees){
                hql+=" and ordinaryEmployees!=1";  //是否为普通员工 1-普通员工
            }
        }
        params.put("oid",oid);
        Long result = (Long) userDao.getByHQLWithNamedParams(hql,params);
        return result.intValue();
    }

    /**
     *按条件查找(除部门)
     * @param oid  机构id
     * @param ordinaryEmployees  是否为普通员工 0-非普通员工 1-普通员工
     * @param department 部门id，（此部门id为一级部门id，若查找无部门人员，则此值为0）
     * @param queryType 查询条件 1-按部门(单独的一个方法) 2-按性别 3-按学历 4-按入职时间 5-按出生日期 6-按婚姻状态
     * @return
     */
    @Override
    public List<Map<String, Object>> getNumByKind(Integer oid, Integer ordinaryEmployees, String department,Integer queryType) {
        List<Map<String, Object>> listMap = new ArrayList<>();
        if (queryType!=null) {
            if (2 == queryType || 6 == queryType) {   //2==queryType(gender 性别 1-男 0-女 2-未知)   6==queryType(marry 0-已婚,1-未婚 ,2-未知)
                String kind = "gender";
                String unknownNum = "2";
                if (6 == queryType) {  //6==queryType(marry 0-已婚,1-未婚 ,2-未知)
                    kind = "marry";
                }
                listMap = this.getNumByGender(oid, ordinaryEmployees, department, kind, unknownNum, listMap);
            } else if (5 == queryType) {  //5-按出生日期
                listMap = this.getNumByBirthday(oid, ordinaryEmployees, department);
            } else if (3 == queryType) {  //按学历  1-研究生，2-本科，3-大专，4-中专或高中，5-其他， 6-未知
                listMap = this.getNumByDegree(oid, ordinaryEmployees, department, listMap);
            } else if (4 == queryType) {    //4-按入职时间年份
                listMap = this.getNumByOnDutyDate(oid, ordinaryEmployees, department, listMap);
            }
        }
        return listMap;
    }

    /**
     * 按性别展示查询   gender 性别 1-男 0-女 2-未知
     * @param oid  机构id
     * @param ordinaryEmployees  是否为普通员工 0-非普通员工 1-普通员工
     * @param department 部门id，（此部门id为一级部门id，若查找无部门人员，则此值为0）
     * @return
     */
    public List<Map<String, Object>> getNumByGender(Integer oid, Integer ordinaryEmployees, String department,String kind,String unknownNum, List<Map<String, Object>> listMap) {
        Map<String,Object> params = new HashMap<>();
        StringBuffer hql = new StringBuffer("select case when "+kind+" is not null and "+kind+"!='' then "+kind+" else "+unknownNum+" end,count(userID) from User where isDuty='1' and roleCode='staff'");
        if (oid != null) {
            hql.append(" and oid=:oid");
            params.put("oid", oid);
        }
        if (ordinaryEmployees != null) {
            hql.append(" and ordinaryEmployees=:ordinaryEmployees");
            params.put("ordinaryEmployees", ordinaryEmployees);
        }
        if (!MyStrings.nulltoempty(department).isEmpty()) {
            if (!"0".equals(department)) {  //此部门id为一级部门id
                hql.append(" and department in (select cast(id as string) from Organization where orgCode like :department and orgType=2)");
                params.put("department", "%/" + department + "/%");
            } else {
                hql.append(" and department is null");
            }
        }
        hql.append(" group by case when "+kind+" is not null and "+kind+"!='' then "+kind+" else "+unknownNum+" end order by "+kind+" desc");

        List<Object[]> num = userDao.getListByHQLWithNamedParams(hql.toString(), params);
        for (Object[] ob : num) {
            Map<String, Object> map = new HashMap<>();
            map.put("kind", ob[0]);
            map.put("num", ob[1]);
            listMap.add(map);
        }
        return listMap;
    }

    /**
     * 按出生日期查询展示  排序需按照 60/70/80/90/00/10
     * @param oid  机构id
     * @param ordinaryEmployees  是否为普通员工 0-非普通员工 1-普通员工
     * @param department 部门id，（此部门id为一级部门id，若查找无部门人员，则此值为0）
     * @return
     */
    public List<Map<String, Object>> getNumByBirthday(Integer oid, Integer ordinaryEmployees, String department) {
        Map<String,Object> params = new HashMap<>();
        List<Map<String, Object>> listMap = new ArrayList<>();
        String kind = "substring(cast(date_format(birthday,'%Y') as string),3,1)";  //截取生日年份的第三个字符
        StringBuffer hql = new StringBuffer("select case when "+kind+" is not null then "+kind+" else null end,count(userID) from User where isDuty='1' and roleCode not in('super','browse')");
        if (oid != null) {
            hql.append(" and oid=:oid");
            params.put("oid", oid);
        }
        if (ordinaryEmployees != null) {
            hql.append(" and ordinaryEmployees=:ordinaryEmployees");
            params.put("ordinaryEmployees", ordinaryEmployees);
        }
        if (!MyStrings.nulltoempty(department).isEmpty()) {
            if (!"0".equals(department)) {  //此部门id为一级部门id
                hql.append(" and department in (select cast(id as string) from Organization where orgCode like :department and orgType=2)");
                params.put("department", "%/" + department + "/%");
            } else {
                hql.append(" and department is null");
            }
        }
            hql.append(" group by "+kind+" order by birthday");  //因出生日期排序需按照 60/70/80/90/00/10

        List<Object[]> num = userDao.getListByHQLWithNamedParams(hql.toString(), params);

        //为了排序啊，呜呜

        if (num.size()>1) {
            Object[] first = num.get(0);  //单独将出生日期为空的摘出来，放到排序的最后
            if (first[0]==null) {
                num.remove(0);  //将第一的移除
                num.add(first);  //放到最后一个
            }
        }//结束

        for (Object[] ob : num) {
            Map<String, Object> map = new HashMap<>();
             //出生日期返回的是几0后(如：80后，60后，90后，00后)
            if (ob[0]==null){  //未知的人员
                map.put("kind", "0");
                map.put("num", ob[1]);
            }else {
                map.put("kind", ob[0]+"0");
                map.put("num", ob[1]);
            }
            listMap.add(map);
        }
        return listMap;
    }

    /**
     * 按学历查询展示  1-研究生，2-本科，3-大专，4-中专或高中，5-其他， 6-未知
     * @param oid  机构id
     * @param ordinaryEmployees  是否为普通员工 0-非普通员工 1-普通员工
     * @param department 部门id，（此部门id为一级部门id，若查找无部门人员，则此值为0）
     * @return
     */
    public List<Map<String, Object>> getNumByDegree(Integer oid, Integer ordinaryEmployees, String department,List<Map<String, Object>> listMap) {
        Map<String,Object> params = new HashMap<>();
        StringBuffer hql = new StringBuffer("select case when degree is not null then degree else '6' end,count(userID) from User where isDuty='1' and roleCode not in('super','browse')");
        if (oid != null) {
            hql.append(" and oid=:oid");
            params.put("oid", oid);
        }
        if (ordinaryEmployees != null) {
            hql.append(" and ordinaryEmployees=:ordinaryEmployees");
            params.put("ordinaryEmployees", ordinaryEmployees);
        }
        if (!MyStrings.nulltoempty(department).isEmpty()) {
            if (!"0".equals(department)) {  //此部门id为一级部门id
                hql.append(" and department in (select cast(id as string) from Organization where orgCode like :department and orgType=2)");
                params.put("department", "%/" + department + "/%");
            } else {
                hql.append(" and department is null");
            }
        }
        hql.append(" group by degree order by degree");
        List<Object[]> num = userDao.getListByHQLWithNamedParams(hql.toString(), params);

        //为了排序啊，呜呜
        if (num.size()>1) {
            Object[] first = num.get(0);  //单独将学历为空的摘出来，放到排序的最后
            Integer keyValue = (Integer) first[0];
            if (6==keyValue) {
                num.remove(0);  //将第一的移除
                num.add(first);  //放到最后一个
            }
        }//结束

        for (Object[] ob : num) {
            Map<String, Object> map = new HashMap<>();
            map.put("kind", ob[0]);
            map.put("num", ob[1]);
            listMap.add(map);
        }
        return listMap;
    }

    /**
     * 按入职时间展示查询  0-未知
     * @param oid  机构id
     * @param ordinaryEmployees  是否为普通员工 0-非普通员工 1-普通员工
     * @param department 部门id，（此部门id为一级部门id，若查找无部门人员，则此值为0）
     * @return
     */
    public List<Map<String, Object>> getNumByOnDutyDate(Integer oid, Integer ordinaryEmployees, String department,List<Map<String, Object>> listMap) {
        Map<String,Object> params = new HashMap<>();
        String kind = "date_format(onDutyDate,'%Y')";
        StringBuffer hql = new StringBuffer("select case when " + kind + " is not null then " + kind + " else '0' end,count(userID) from User where isDuty='1' and roleCode not in('super','browse')");
        if (oid != null) {
            hql.append(" and oid=:oid");
            params.put("oid", oid);
        }
        if (ordinaryEmployees != null) {
            hql.append(" and ordinaryEmployees=:ordinaryEmployees");
            params.put("ordinaryEmployees", ordinaryEmployees);
        }
        if (!MyStrings.nulltoempty(department).isEmpty()) {
            if (!"0".equals(department)) {  //此部门id为一级部门id
                hql.append(" and department in (select cast(id as string) from Organization where orgCode like :department and orgType=2)");
                params.put("department", "%/" + department + "/%");
            } else {
                hql.append(" and department is null");
            }
        }
        hql.append(" group by " + kind + " order by " + kind + "");

        List<Object[]> num = userDao.getListByHQLWithNamedParams(hql.toString(), params);

        //为了排序啊，呜呜
        if (num.size()>1) {
            Object[] first = num.get(0);  //单独将入职时间为空的摘出来，放到排序的最后
            if ("0".equals(first[0])) {
                num.remove(0);  //将第一的移除
                num.add(first);  //放到最后一个
            }
        }//结束

        for (Object[] ob : num) {
            Map<String, Object> map = new HashMap<>();
            map.put("kind", ob[0]);
            map.put("num", ob[1]);
            listMap.add(map);
        }
        return listMap;
    }

    /**
     *按部门查找
     * @param department  一级部门id,0-查找无部门人员(固定)
     * @param ordinaryEmployees 是否为普通员工 0-非普通员工 1-普通员工
     * @return
     */
    @Override
    public Integer getNumByDepartment(String department, Integer ordinaryEmployees,Integer oid) {
        Map<String,Object> params = new HashMap<>();
        StringBuffer hql = new StringBuffer("select count(userID) from User where isDuty='1' and roleCode='staff'");
        if (oid!=null){
            hql.append(" and oid=:oid");
            params.put("oid", oid);
        }
        if (ordinaryEmployees != null) {
            hql.append(" and ordinaryEmployees=:ordinaryEmployees");
            params.put("ordinaryEmployees", ordinaryEmployees);
        }
        if (!MyStrings.nulltoempty(department).isEmpty()) {
            if (!"0".equals(department)) {  //此部门id为一级部门id
                hql.append(" and department in (select cast(id as string) from Organization where orgCode like :department and orgType=2)");
                params.put("department", "%/" + department + "/%");
            } else {
                hql.append(" and department is null");
            }
        }
        Long num = (Long) userDao.getByHQLWithNamedParams(hql.toString(),params);
        return num==null?0:num.intValue();
    }

    /**
     *
     * @param oid  机构id
     * @param ordinaryEmployees  是否为普通员工 0-非普通员工 1-普通员工
     * @param department 部门id
     * @param gender 性别 1-男 0-女 2-未知
     * @param marry 0-已婚。1-未婚  2-未知
     * @param onDutyDate  入职时间
     * @param degree  学历（使用此字段） 1-研究生，2-本科，3-大专，4-中专或高中，5-其他  6-未知
     * @param birthday  出生日期 0-未知 (1960,1970,1980,1990,2000,2010后等,转换截取第三个字段)
     * @return
     */
    @Override
    public List<User> getUsersByKind(Integer oid,Integer ordinaryEmployees,String department, String gender, String marry, String onDutyDate,Integer degree,String birthday,PageInfo pageInfo) {
        Map<String,Object> params = new HashMap<>();
        StringBuffer hql = new StringBuffer("from User where isDuty='1' and roleCode ='staff'");
        if (oid!=null){
            hql.append(" and oid=:oid");
            params.put("oid",oid);
        }
        if (ordinaryEmployees!=null){
            hql.append(" and ordinaryEmployees=:ordinaryEmployees");
            params.put("ordinaryEmployees",ordinaryEmployees);
        }
        if (!MyStrings.nulltoempty(department).isEmpty()){  //部门
            if (!"0".equals(department)) {   //在此传过来的部门id均是一级部门的id
                hql.append(" and department in (select cast(id as string) from Organization where orgCode like :department and orgType=2)");
                params.put("department", "%/"+department+"/%");
            }else {
                hql.append(" and department is null");  //无部门
            }
        }
        if (!MyStrings.nulltoempty(gender).isEmpty()){  //性别
            if (!"2".equals(gender)){
                hql.append(" and gender=:gender");
                params.put("gender",gender);
            }else {
                hql.append(" and (gender is null or gender='')");
            }
        }
        if (!MyStrings.nulltoempty(marry).isEmpty()){  //婚姻状态
            if (!"2".equals(marry)){
                hql.append(" and marry=:marry");
                params.put("marry",marry);
            }else {
                hql.append(" and (marry is null or marry='')");
            }
        }
        if (!MyStrings.nulltoempty(onDutyDate).isEmpty()){  //入职时间
            if (!"0".equals(onDutyDate)) {
                hql.append(" and date_format(onDutyDate,'%Y')=date_format(:onDutyDate,'%Y')");
                params.put("onDutyDate", onDutyDate + "-01-01");
            }else {
                hql.append(" and onDutyDate is null");
            }
        }
        if (degree!=null){   //学历
            if (degree!=6){
                hql.append(" and degree=:degree");
                params.put("degree",degree);
            }else {
                hql.append(" and degree is null");
            }
        }
        if (!MyStrings.nulltoempty(birthday).isEmpty()){  //出生日期
            if ("0".equals(birthday)){  //查询未知人员
                hql.append(" and birthday is null");
            }else {
                hql.append(" and substring(cast(date_format(birthday,'%Y') as string),3,1)=:birthday");
                params.put("birthday",birthday.substring(0,1));
            }
        }
        return userDao.getListByHQLWithNamedParams(hql.toString(),params,pageInfo);
    }

    @Override
    public List<User> searchUsers(Integer oid, String search, PageInfo pageInfo) {
        Map<String,Object> params = new HashMap<>();
//        String hql = " from User where oid=:oid and (userName like :search) and isDuty='1' and roleCode not in('super','browse')";
        String hql = " from User where oid=:oid and (userName like :search) and isDuty='1' and roleCode not in(:roleCodes)";
        params.put("oid",oid);
        params.put("roleCodes",RoleCodes.superAndBrowse.getCodeList());
        params.put("search","%"+search+"%");
        return userDao.getListByHQLWithNamedParams(hql,params,pageInfo);
    }

    @Override
    public List<User> getUsersByOidAndRoleCode(Integer org, String s) {
        String hql = " from User o where o.oid = "+org+" and o.roleCode in "+s;
        return userDao.getListByHQL(hql);
    }

    @Override
    public List<UserDto> getCoreSettingUserList(Integer oid, String coreCode) {
//        String hql = "select new cn.sphd.miners.modules.system.dto.UserDto(u.userID, case when u.userName is not null and length(trim(u.userName))>0 then u.userName else u.logonName end) from User u where u.oid = :oid  and u.isDuty='1' and u.roleCode!='browse'and u.roleCode!='super' and u.userID NOT in (select ur.userID from UserRole ur where ur.role.code=:code)";
        String hql = "select new cn.sphd.miners.modules.system.dto.UserDto(u.userID, u.userName,u.departName,u.postName,u.department,u.mobile) from User u where u.oid = :oid  and u.isDuty in('1','9') and u.roleCode='staff' and u.userID NOT in (select ur.userID from UserRole ur where ur.role.code=:code)";

        //and (u.department is null or length(trim(u.department))=0)
        Map<String,Object> params = new HashMap<>();
        params.put("oid",oid);
        params.put("code",coreCode);
        List<UserDto> userDtoList=userDao.getListByHQLWithNamedParams(hql,params);
        return userDtoList;
    }

    @Override
    public Long getPopedomsCacheUserCount(List roleCodes) {
        StringBuffer hql = new StringBuffer("select count(*) from User where oid is not null and isDuty in(:isDuty)");
        Map<String,Object> params = new HashMap<>();
        params.put("isDuty", Arrays.asList("1","3"));
        if(roleCodes!=null && !roleCodes.isEmpty()) {
            params.put("roleCode", roleCodes);
            hql.append(" and roleCode not in(:roleCode)");
        }
        return (Long) userDao.getByHQLWithNamedParams(hql.toString(), params);
    }

    @Override
    public List<User> getPopedomsCacheUsers(Integer start, Integer limit, List roleCodes) {
        System.out.println(start.toString() + " | " + limit.toString());
        StringBuffer hql = new StringBuffer("from User where oid is not null and isDuty in(:isDuty)");
        Map<String,Object> params = new HashMap<>();
        params.put("isDuty", Arrays.asList("1","3","9"));
        if(roleCodes!=null && !roleCodes.isEmpty()) {
            params.put("roleCode", roleCodes);
            hql.append(" and roleCode not in(:roleCode)");
        }
        Query query = userDao.getSession().createQuery(hql.toString());
        userDao.setParams(query, params);
        query.setFirstResult(start);
        query.setMaxResults(limit);
        List<User> userList = query.list();
        return userList;
    }

    @Override
    public User updateUserBasicInfo(User loginUser,User user, JSONObject userInfoJson, String contacts) {
//        JSONObject userInfoJson=JSONObject.fromObject(userInfo);
//        user=userDao.get(userInfoJson.getInt("passiveUserId"));
        user.setGender(userInfoJson.getString("gender"));//性别  1-男 0-女
        user.setNation(userInfoJson.getString("nation"));//民族
        if (!StringUtil.isNullOrEmpty(userInfoJson.getString("birthday"))) {
            try {
                user.setBirthday(new SimpleDateFormat("yyyy/MM/dd").parse(userInfoJson.getString("birthday")));//出生日期
            } catch (ParseException e) {
                System.out.println("出生日期传值异常：" + userInfoJson.getString("birthday"));
                e.printStackTrace();
            }
        }else {
            user.setBirthday(null);
        }
        user.setBirthplace(userInfoJson.getString("birthplace"));//出生地
        user.setMarry(userInfoJson.getString("marry"));//0已婚。1未婚
        user.setPoliticalStatus(userInfoJson.getString("politicalStatus"));//政治面貌
        user.setDegree(userInfoJson.getInt("degree"));  //最高学历
        user.setFirstLanguage(userInfoJson.getString("firstLanguage"));//第一外语
        user.setFirstForeignLevel(userInfoJson.getString("firstForeignLevel"));//第一外语证书
        user.setSecondLanguage(userInfoJson.getString("secondLanguage"));//第二外语
        user.setSecondForeignLevel(userInfoJson.getString("secondForeignLevel"));//第二外语证书
        user.setComputerLevel(userInfoJson.getString("computerLevel"));//计算机证书
        user.setOtherSkills(userInfoJson.getString("otherSkills"));//其他技能描述
        user.setUpdator(loginUser.getUserID());
        user.setUpdateDate(new Date());
        user.setUpdateName(loginUser.getUserName());
        if ("1".equals(user.getSubmitState()))
            user.setVersionNo(user.getVersionNo() == null ? 1 : user.getVersionNo() + 1);
//        else
//            user.setVersionNo(0);

        this.updateUser(user);

        String hql = "delete UserContact where userId=:userId";
        Map<String, Object> map = new HashMap<>();
        map.put("userId", user.getUserID());
        userContactDao.queryHQLWithNamedParams(hql, map);

        if (!StringUtil.isNullOrEmpty(contacts)) {
            JSONArray contactsJsonArray = JSONArray.fromObject(contacts);
            for (Object o:contactsJsonArray){
                JSONObject contactJson=JSONObject.fromObject(o);
                UserContact userContact=new UserContact();
                userContact.setUserId(user.getUserID());
                userContact.setOrg(user.getOid());
                userContact.setCreateDate(new Date());
                userContact.setCreator(user.getUserID());
                userContact.setCreateName(user.getUserName());
                userContact.setUpdateDate(new Date());
                userContact.setUpdator(user.getUserID());
                userContact.setUpdateName(user.getUserName());
                userContact.setEnabled(true);//状态：0-不启用,1-启用
                userContact.setVersionNo(user.getVersionNo());
                userContact.setType(contactJson.getString("type"));//类型:1-mobile,2-qq,3-email,4-weixin,5-weibo,9-其他自定义标签
                userContact.setOpen(contactJson.getBoolean("isOpen"));//是否公开:true-公开,false-保密
                userContact.setCode(contactJson.getString("code"));
                if ("9".equals(userContact.getType()))
                    userContact.setName(contactJson.getString("name"));
                userContactDao.save(userContact);
            }
        }
        return user;
    }

    @Override
    public void addUserIdcard(User user,User loginUser, String idCard, String address) {
        String hql=" from UserIdcard where userId=:userId order by createDate desc";
        Map<String,Object> map = new HashMap<>();
        map.put("userId",user.getUserID());
        UserIdcard oldUserIdcard= (UserIdcard) userIdcardDao.getByHQLWithNamedParams(hql,map);

        UserIdcard userIdcard=new UserIdcard();
        userIdcard.setUserId(user.getUserID());
        userIdcard.setOrg(user.getOid());
        userIdcard.setCardOldValue(user.getIdCard());//身份证号原值
        userIdcard.setAddressOldValue(user.getAddress());//身份证地址原值
        userIdcard.setCardNewValue(idCard);//身份证号新值
        userIdcard.setAddressNewValue(address);//身份证地址新值
        userIdcard.setCreateDate(new Date());
        userIdcard.setCreateName(loginUser.getUserName());
        userIdcard.setCreator(loginUser.getUserID());
        userIdcard.setUpdateDate(new Date());
        userIdcard.setUpdator(loginUser.getUserID());
        userIdcard.setUpdateName(loginUser.getUserName());
        if (oldUserIdcard!=null) {
            userIdcard.setPreviousId(oldUserIdcard.getId());
            userIdcard.setVersionNo(oldUserIdcard.getVersionNo() != null ? oldUserIdcard.getVersionNo() + 1 : 1);
        }else {
            userIdcard.setVersionNo(0);
        }
        userIdcardDao.save(userIdcard);//生成身份证信息历史记录
    }

    @Override
    public User getUserByApproval(Integer oid) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from User where oid=:oid  and isDuty='1'";
        map.put("oid",oid);
        User user = (User) userDao.getByHQLWithNamedParams(hql+" and managerCode = 'smallSuper'",map);
        if (user==null){
            user = (User) userDao.getByHQLWithNamedParams(hql+" and managerCode = 'super'",map);
        }
        return user;
    }

    @Override
    public List<User> getUserNoGeneralByOid(Integer oid,List<Integer> noUserIds,PageInfo pageInfo) {
        String hql=" from User where oid=:oid and isDuty in('1','9') and roleCode in('agent','staff','super')";
        Map<String,Object> map=new HashMap<>();
        map.put("oid",oid);
        if (noUserIds.size()>0){
            hql+=" and userID not in(:noUserIds)";
            map.put("noUserIds",noUserIds);
        }
        List<User> userList=userDao.getListByHQLWithNamedParams(hql,map,pageInfo);
        return userList;
    }

    @Override
    public List<UserIdcard> getUserIdcardHistories(Integer userId) {
        String hql=" from UserIdcard where userId=:userId order by createDate asc";
        Map<String,Object> map=new HashMap<>();
        map.put("userId",userId);
        List<UserIdcard> userIdcardList=userIdcardDao.getListByHQLWithNamedParams(hql,map);
        return userIdcardList;
    }

    @Override
    public UserIdcard getUserIdCardById(Integer id) {
        return userIdcardDao.get(id);
    }

    @Override
    public List<User> getUsersByUserIds(List<Integer> userIds) {
        List<User> userList=new ArrayList<>();
        if (userIds.size()>0) {
            String hql = " from User where userID in(:userIds)";
            Map<String, Object> map = new HashMap<>();
            map.put("userIds", userIds);
            userList = userDao.getListByHQLWithNamedParams(hql, map);
        }
        return userList;
    }

    @Override
    public List<User> getUsersByMasterUserID(Integer masterUserID) {
//        String hql=" from User where isDuty in ('1','4') and (masterUserID=:masterUserID or userID=:masterUserID)";
        String hql=" from User where isDuty in (:isDutys) and (masterUserID=:masterUserID or userID=:masterUserID)";
        Map<String, Object> map = new HashMap<>();
        map.put("isDutys",IsDutys.isDutyListAppCanLogin.getIsDutyList());
        map.put("masterUserID", masterUserID);
        List<User> userList=userDao.getListByHQLWithNamedParams(hql,map);
        return userList;
    }

    @Override
    public List<UserLoginDto> getUserMsgCountSum(Long accId) {
        String hql="select new cn.sphd.miners.modules.system.dto.UserLoginDto(mobile,oid,cast(sum(msgCount) as integer)) from User where isDuty='1' and accId=:accId group by oid";
        Map<String, Object> map = new HashMap<>();
//        map.put("oid", oid);
        map.put("accId",accId);
        List<UserLoginDto> list= userDao.getListByHQLWithNamedParams(hql,map);
        return list;
    }

    @Override
    public void updateUserNameByOidMobile(Integer oid, String mobile,String newUserName) {
        String hql="update User set userName=:newUserName where roleCode!='agent' and oid=:oid and mobile=:mobile"; //2021/7/26 lixu 更新时 被代办人姓名 不一起改
        Map<String,Object> map = new HashMap<>();
        map.put("oid",oid);
        map.put("mobile",mobile);
        map.put("newUserName",newUserName);
        userDao.queryHQLWithNamedParams(hql,map);
    }

    //某部门以及其下子部门中的所有职工
    @Override
    public List<User> getUsersByOrgAndChild(Integer deptId) {
        Map<String, Object> params = new HashMap<>();
        String hql = "select cast(id as string) from Organization where orgType=2 and enabled=true and orgCode like :pass";
        params.put("pass","%/"+deptId+"/%");
        List<String> deptIds = (List<String>) userDao.getListByHQLWithNamedParams(hql, params);

        List<User> users = new ArrayList<>();
        if (deptIds.size()!=0){
            Map<String, Object> map = new HashMap<>();
//            String hql1 = "from User where roleCode in('staff','super') and department in (:department) and isDuty in ('1','9')";
            String hql1 = "from User where roleCode in(:roleCodes) and department in (:department) and isDuty in ('1','9')";
            map.put("roleCodes",RoleCodes.personOrg.getCodeList());
            map.put("department",deptIds);
//            if (!MyStrings.nulltoempty(isDuty).isEmpty()){
//                hql1+=" and isDuty in :isDuty";
//                map.put("isDuty",isDuty);
//            }
           users = userDao.getListByHQLWithNamedParams(hql1,map);
        }
        return users;
    }


    @Override
    public List<UserHonePageDto> getStaffsOrSuper(Integer oid, String type, List<Integer> list) {
//        String hql=" select new cn.sphd.miners.modules.system.dto.UserHonePageDto(userID,userName,departName,mobile,isDuty,gender,imgPath,roleCode) from User where roleCode in ('staff','super','smallSuper') and isDuty in('1','9') and oid=:oid ";
        String hql=" select new cn.sphd.miners.modules.system.dto.UserHonePageDto(userID,userName,departName,mobile,isDuty,gender,imgPath,roleCode) from User where roleCode in (:roleCodes) and isDuty in('1','9') and oid=:oid ";
        Map<String, Object> param = new HashMap<>();
        param.put("oid",oid);
        param.put("roleCodes",RoleCodes.personOrg.getCodeList());
        if ("2".equals(type)) {
            hql += " and userID not in (:list)";
            param.put("list", list);
        }
        List<UserHonePageDto> userList=userDao.getListByHQLWithNamedParams(hql,param);
        return userList;
    }

    @Override
    public List<UserHonePageDto> getStaffsOrSuperLocking(Integer oid, String type, List<Integer> list) {
//        String hql=" select new cn.sphd.miners.modules.system.dto.UserHonePageDto(userID,userName,departName,mobile,isDuty,gender,imgPath,roleCode) from User where roleCode in ('staff','super','see','smallSuper') and isDuty in('1','9','5') and oid=:oid ";
        String hql=" select new cn.sphd.miners.modules.system.dto.UserHonePageDto(userID,userName,departName,mobile,isDuty,gender,imgPath,roleCode) from User where roleCode in (:roleCodes) and isDuty in('1','9','5') and oid=:oid ";
        Map<String, Object> param = new HashMap<>();
        param.put("oid",oid);
        param.put("roleCodes",RoleCodes.personAndAgent.getCodeList());
        if ("1".equals(type)) {
            hql += " and userID in (:list)";
            param.put("list", list);
        }else if ("2".equals(type)) {
            hql += " and userID not in (:list)";
            param.put("list", list);
        }
        List<UserHonePageDto> userList=userDao.getListByHQLWithNamedParams(hql,param);
        return userList;
    }


    @Override
    public Integer getUserCountsByOid(Integer oid) {
        String hql="select count(1) from User where roleCode='staff' and oid=:oid";
        Map<String, Object> map = new HashMap<>();
        map.put("oid", oid);
        Long number= (Long) userDao.getByHQLWithNamedParams(hql,map);
        return number.intValue();
    }

    @Override
    public List<User> getPresentUsers(Integer oid) {
//        String hql = "from User where roleCode in('staff','super','smallSuper','temporary') and isDuty in('1','9') and oid=:oid";
        String hql = "from User where roleCode in(:roleCodes) and isDuty in('1','9') and oid=:oid";
        Map<String,Object> map=new HashMap<String,Object>();
        map.put("oid",oid);
        map.put("roleCodes",RoleCodes.personOrgTemporary.getCodeList());
        List<User> list = (List)userDao.getListByHQLWithNamedParams(hql,map);
        return list;
    }

    @Override
    public User getManageUserByMasterUserID(Integer masterUserID,String roleCode) {
        String hql=" from User where roleCode=:roleCode and isDuty='1' and masterUserID=:masterUserID";
        Map<String,Object> map=new HashMap<String,Object>();
        map.put("masterUserID",masterUserID);
        map.put("roleCode",roleCode);
        User manage= (User) userDao.getByHQLWithNamedParams(hql,map);
        return manage;
    }

    @Override
    public List<User> getRoleAndUsersByOid(Integer oid) {
        String hql=" from User where isDuty in('1','9') and oid=:oid";
        Map<String,Object> map=new HashMap<String,Object>();
        map.put("oid",oid);
        List<User> userList= userDao.getListByHQLWithNamedParams(hql,map);
        return userList;
    }

    @Override
    public User getUserByOidAndUserName(Integer oid, String userName) {
        String hql = "from User where oid=:oid and userName=:userName";
        Map<String, Object> params = new HashMap<String, Object>(2);
        params.put("oid", oid);
        params.put("userName", userName);
        List<User> users = userDao.getListByHQLWithNamedParams(hql, params);
        User result = null;
        for (User u : users) {
            if (result == null
                //isDuty优先级排序：1表示在职，2表示离职 ,3可登录的代理人员 4-外部浏览者（手机端超管分配）  0-导入过程未全部完成的人员，不算正式员工，只在导入后续处理列表中
                || Integer.valueOf(u.getIsDuty())>0 && (Integer.valueOf(result.getIsDuty())==0 || Integer.valueOf(result.getIsDuty())>Integer.valueOf(u.getIsDuty()))
                //roleCode优先级排序：人优先于身份
                || User.personCodes().contains(u.getRoleCode()) && !User.personCodes().contains(result.getRoleCode())
            ) {
                result = u;
            }
        }
        return result;
    }

    @Override
    public Integer disableUsersByAcc(AuthAcc acc) {
        //根据账号id ，锁定机构user
        String hql = "from User where accId=:accId and roleCode in (:roleCode)";
        Map<String,Object> params=new HashMap<>();
        params.put("accId",acc.getId());
        params.put("roleCode", RoleCodes.personCodes.getCodeList());  //User.personCodes()); // 只锁定人，不锁定高管身份
        List<User> userList= userDao.getListByHQLWithNamedParams(hql,params);
        for (User u: userList){
            u.setIsDuty("9");// 9- 锁定（1.159私人领地奠基，超89天锁定）
            updateUser(u);
        }
        return userList.size();
    }

    @Override
    public List<AddressBookDto> getAddressBookDtoList(Integer oid,String userName) {
        Map<String,Object> map = new HashMap<>();
//        String hql="select new cn.sphd.miners.modules.system.dto.UserHonePageDto(userID,userName,departName,mobile,isDuty,gender,imgPath,roleCode) from User where roleCode in('staff','agent') and oid=:oid";
        String hql="select new cn.sphd.miners.modules.system.dto.UserHonePageDto(userID,userName,departName,mobile,isDuty,gender,imgPath,roleCode) from User where roleCode in(:roleCodes) and oid=:oid";
        map.put("roleCodes",RoleCodes.personStaff.getCodeList());
        map.put("oid",oid);
        if (StringUtils.isNotEmpty(userName)){
            hql+=" and userName like:userName";
            map.put("userName","%"+userName+"%");
        }
        List<AddressBookDto> addressBookDtoList = (List<AddressBookDto>) userDao.getListByHQLWithNamedParams(hql,map);
        return addressBookDtoList;
    }

    @Override
    public List<User> getUserList(Long accId) {
        String hql = " from User u  where u.accId=:accId and u.organization.state=0  and u.leader is not null and u.roleCode in(:personCodes) and u.isDuty in(:getIsDutyListCanLogin) order by u.isDefault desc,u.oid asc";
        Map<String,Object> map=new HashMap<String,Object>();
        map.put("accId",accId);
        map.put("personCodes", RoleCodes.personCodes.getCodeList()); //User.personCodes());
        map.put("getIsDutyListCanLogin", IsDutys.isDutyListCanLogin.getIsDutyList());//User.getIsDutyListCanLogin());
        return userDao.getListByHQLWithNamedParams(hql,map);
    }

    @Override
    public List<AttendanceUserDaysDto> getUserDays(Integer oid, Integer loginUserId, Integer deptId, Integer onlyUser, Date beginDate, Date endDate, PageInfo pageInfo) {
        if(beginDate==null || endDate==null) {
            throw new IllegalArgumentException("时间段不能为空！");
        }
        Map<String,Object> params = new HashMap<>();
        List<String> where = new ArrayList<>();
        StringBuffer hql = new StringBuffer("select new cn.sphd.miners.modules.generalAffairs.dto.AttendanceUserDaysDto(userID,userName,mobile,leader,onDutyDate,offDutyDate) from User");
        where.add("roleCode in ('staff','agent','super')");
        where.add("(isDuty in ('1','2','9'))");
        where.add("(onDutyDate is null or onDutyDate <= :endDate)");
        where.add("(offDutyDate is null or offDutyDate >= :beginDate)");
        params.put("endDate",endDate);
        params.put("beginDate",beginDate);
        if (oid!=null){
            where.add("oid=:oid");
            params.put("oid",oid);
        }
        if (deptId!=null){
            if (deptId!=0) {
                where.add("department=:department");
                params.put("department", deptId.toString());
            } else {//deptId==0查无部门人员
                where.add("department is null");
            }
        }
        if (onlyUser != null) {  //仅查询此人员
            where.add("userID=:userId");
            params.put("userId", onlyUser); //职工自己
        } else if (loginUserId != null) {  //取此人员的下属以及间接下属
            where.add("(userID=:userId or rankUrl like :rankUrl)");
            params.put("userId", loginUserId); //职工自己
            params.put("rankUrl", "%/" + loginUserId + "/%");  //取本职工自身、直接以及间接下级
        }
        hql.append(" where ").append(StringUtils.join(where, " and ")).append(" order by convert(userName, 'gbk')");
        List<AttendanceUserDaysDto> attendanceUserDaysDtos = userDao.getListByHQLWithNamedParams(hql.toString(),params,pageInfo);
        AtomicInteger index = new AtomicInteger();//添加序号
        attendanceUserDaysDtos.stream().forEach(item -> {item.setRownum(index.incrementAndGet());});
        return attendanceUserDaysDtos;
    }

    @Override
    public Integer activationUser(User user, Integer passiveUserId) {
        User staffUser=userDao.get(passiveUserId);
        Integer state=0;
        if (user!=null&&staffUser!=null&&staffUser.getOid().equals(user.getOid())){
            User repeatUser=getUserByOidAndPhone(staffUser.getOid(),staffUser.getMobile());
            if (repeatUser==null||repeatUser.getUserID().equals(passiveUserId)) { // 不与机构内其他现用手机号 重复，则可以激活
//                AuthAcc authAcc = authService.newEnabledAccInOrg(staffUser.getMobile(), staffUser.getUserName(), staffUser.getOrganization());
                AuthAcc authAcc=authService.getLockedAcc(staffUser.getAccId());
//                staffUser.setAccId(authAcc.getId());
//                staffUser.setIsDuty("1");
//                staffUser.setUpdator(user.getUserID());
//                staffUser.setUpdateName(user.getUserName());
//                staffUser.setUpdateDate(new Date());
//                updateUser(staffUser);

                // 1.289账号验证与安全问题  2024/6/21改 lixu
                if(authService.enableAcc(authAcc)) {
                    authService.removeActiveCode(authAcc);
                    state = 1;
                }
            }else {
                state=2;//激活失败，与机构内其他职工 现用手机号重复
            }
        }
        return state;
    }

    //获取在职/离职的职工
    @Override
    public List<UserDto> getUserListByOidIsduty(Integer oid, List<Integer> orgIntegerList,String isDuty,String period) {
        Map<String,Object> map=new HashMap<>();

        StringBuffer condition =new StringBuffer(" select new cn.sphd.miners.modules.system.dto.UserDto(userID,userName,departName,postName,department,mobile) from User where roleCode='staff'");
        if (oid!=null){
            condition.append(" and oid=:oid");
            map.put("oid",oid);
        }
        if (orgIntegerList!=null&&orgIntegerList.size()>0){
            condition.append(" and oid in (:orgIntegerList)");
            map.put("orgIntegerList",orgIntegerList);
        }
        if (!"".equals(isDuty) && isDuty!=null){
            if ("1".equals(isDuty)){
                condition.append(" and isDuty in('1','9')");
            }else {    //离职的要判断下时间
                condition.append(" and isDuty =:isDuty");
                map.put("isDuty", isDuty);
                if (StringUtils.isNotEmpty(period)){  //当前月以及之前离职的人员
                    //指定日期那月的最后1毫秒
                    Date begin = NewDateUtils.getLastTimeOfMonth(NewDateUtils.dateFromString(period+"01","yyyy-MM-dd"));
                    condition.append(" and offDutyDate<=:begin");
                    map.put("begin", begin);
                }
            }
        }
        //    condition.append(" order by CONVERT(userName USING gbk)");
        return userDao.getListByHQLWithNamedParams(condition.toString(),map);
    }


    @Override
    public Boolean joinTeam(String userName, String phone,Integer oid,String submitState) {

        User userByLogonName = userService.getUserByOidAndPhone(oid,phone);
        if(userByLogonName==null) {
            Organization organization = orgService.getByOid(oid,true,false);
            if (organization==null){
                return  false; // 机构已被注销 返回失败
            }
            //基本信息
            User user1 = new User();

            user1.setOnDutyDate(new Date());//入职时间
            user1.setUserName(userName);//用户姓名
            user1.setRealName(userName);//真实姓名
            List<User> userList = userService.getUserByMobileAndIsDuty(oid, phone, "9");  // 本机构相同手机号锁定的数据
            for (User u : userList) {
                u.setIsDuty("2");
                userService.updateUser(u); // 锁定的手机号 被重新录入机构，需要把原锁定的员工 变为离职。 1.117 奠基优化 20210823 lixu 改
            }
            user1.setMobile(phone);//手机
            user1.setOrdinaryEmployees(1);//是否为普通员工 1-普通员工
            user1.setSubmitState(submitState); //0-团队成员隐藏手机号，1-团队成员展示手机号   需求刚捋出来的， 和用户不勾选那个选项一样

            User pUser = getUserByRoleCode(oid, "super");
            if (pUser==null){
                return  false; // 董事长已离职 返回失败
            }
            Integer pid = pUser.getUserID();
            if (pUser != null) {
                user1.setLeader(String.valueOf(pid));//从属于某位领导
                String rankUrl;
                if (!StringUtil.isNullOrEmpty(pUser.getRankUrl())) {
                    rankUrl = pUser.getRankUrl() + pid;
                } else {
                    rankUrl = "/" + pid;
                }
                user1.setRankUrl(rankUrl);//从属关系
                user1.setLeaderName(pUser.getUserName());//从属于某位领导
            }

            user1.setCreateTime(new Date());//创建时间
//        user1.setCreator(user.getUserID());//创建者
//        user1.setCreateName(user.getUserName());//创建者
            user1.setStatus("2");////状态：1-禁用 其它-启用
            user1.setIsDuty("1");//是否在职  1表示在职，2表示离职
            user1.setRemark("直播助手加入团队");//备注
            user1.setOrganization(organization);
            user1.setOid(organization.getId());
            user1.setDefault(false);
//            user1.setManager(user.getManager());  //高管id
//            User manage=userService.getUserByID(user.getManager());
            user1.setManagerCode("general");  //高管权限
            user1.setRoleCode("staff");  //此员工的权限
            user1.setSuperId(organization.getSuperId());
            userService.addUser(user1, organization);

//        if (newUser.getImgPath()!=null&&!newUser.getImgPath().isEmpty()) {
//            user1.setImgPath(newUser.getImgPath());
//            uploadService.addFileUsing(new UserUsing(user1.getUserID()), user1.getImgPath(), user1.getUserName()+"(办理入职)", user1, "职工头像");
//        }
//        userService.updateUser(user1);


            userPopedomService.saveUserPopedomByCode("staff", user1);//给普通员工附上权限
            if (1 != user1.getOrdinaryEmployees()) {  // 非普通员工   2020/11/3 我的团队版  lixu加
                userPopedomService.saveUserPopedomByCode("seniorStaff", user1); //添加非普通员工权限
            }
            userService.getPopedomStringByUser(user1, true);//变更权限时用。
            popedomTaskService.updateUserBadgeNumber(user1);//重新计算角标
            return true;
        }

        return false;
    }


    @Override
    public Boolean memberOutTeam(User opUser, Integer outUserId) {
        return outOrBackTeam(opUser,outUserId,"2");//移出团队
    }

    private Boolean outOrBackTeam(User opUser,Integer userId,String isDuty){
        if(opUser!=null&&userId!=null) {
            User outUser = userService.getUserByID(userId);
            outUser.setIsDuty(isDuty);//2表示离职
            outUser.setOffDutyDate(new Date());//离职时间
            outUser.setUpdateDate(new Date());
            outUser.setUpdateName(opUser.getUserName());
            outUser.setUpdator(opUser.getUserID());
            userService.updateUser(outUser);
            return true;
        }
        return false;
    }

    @Override
    public Boolean memberBackTeam(User opUser, Integer outUserId) {
        return outOrBackTeam(opUser,outUserId,"1"); //重新移入团队
    }

    @Override
    public String getStateByAccUser(Long accId,String type, Integer oid) {
        if (accId!=null) {
            List<User> userList = getUserList(accId);
            for (User user : userList) {
                if ("4".equals(user.getOrganization().getOrgType())) {
                    if (user.getRoleCode().equals("super") && "1".equals(type)) {
                        return "1"; // 主播账号已有
                    } else if ("2".equals(type) && user.getOid().equals(oid)) {
                        return "2"; // 要加入的机构已有
                    }
                }
            }
            return "3";
        }
        return "0"; //没有这个账号
    }


    @Override
    public Integer getBrowseCountsByOid(Integer oid) {
        String hql="select count(1) from User where roleCode='browse' and oid=:oid";
        Map<String, Object> map = new HashMap<>();
        map.put("oid", oid);
        Long number= (Long) userDao.getByHQLWithNamedParams(hql,map);
        return number.intValue();
    }

    @Override
    public User getUserByOrgSonOrgMobile(Integer oid, String mobile) {
        Organization organization=orgDao.get(oid);
        if (organization.getOrgType()!=1){
            oid=organization.getPid();
        }
        List<Integer> oids= orgService.getOrgSonOrgIds(oid);// 总机构 子机构oid列表
        String hql=" from User where oid in (:oid) and mobile=:mobile and isDuty in (:isDuty) and roleCode in (:roleCode)";
        Map<String,Object> params=new HashMap<>();
        params.put("mobile",mobile);
        params.put("oid",oids);
        params.put("isDuty", IsDutys.isDutyListCanLogin.getIsDutyList()); //User.getIsDutyListCanLogin());
        params.put("roleCode", RoleCodes.personCodes.getCodeList()); //User.personCodes());

        User user= (User) userDao.getByHQLWithNamedParams(hql,params);
        return user;
    }


    @Override
    public List<User> getUserListByOrgSonOrg(Integer oid) {
        Organization organization=orgDao.get(oid);
        if (organization.getOrgType()!=1){
            oid=organization.getPid();
        }
        List<Integer> oids= orgService.getOrgSonOrgIds(oid);// 总机构 子机构oid列表
        String hql=" from User where oid in (:oid) and isDuty in (:isDuty) and roleCode in (:roleCode)";
        Map<String,Object> params=new HashMap<>();
        params.put("oid",oids);
//        params.put("isDuty", IsDutys.isDutyListCanLogin.getIsDutyList()); //User.getIsDutyListCanLogin());
        // 临时处理 2024/10/31 12：11 需求确认，禁止离职手机在本机构入职，等复职功能做完后再一起处理。
        params.put("isDuty",new ArrayList<String>(Arrays.asList("1","2","3","4","5")));
        params.put("roleCode", RoleCodes.personCodes.getCodeList()); //User.personCodes());
        List<User> userList= userDao.getListByHQLWithNamedParams(hql,params);
        return userList;
    }

    @Override
    public void followSuperToGlpt(User u,String phone) {
        if ("super".equals(u.getRoleCode())) {
            //改的是董事长的手机号 去管理平台同步
            String url = System.getProperty("miners.glptApiRoot") + "/tenant/followUpdateSuperMobile.do";
            Map<String, String> map = new HashMap<String, String>();
            map.put("mOrgId", u.getOid().toString());
            map.put("mobile", phone);
//            MinersHttpClientUtils.postJson(url, map);
            HttpClientUtils client = new HttpClientUtils(url);
            client.jsonResponseToT(client.doPost(null, url, null,null, map),JsonResult.class);
        }
    }

    @Override
    public List<User> getSuperUsersByAccId(Long accId) {
        String hql="from User where accId=:accId and roleCode='super'";
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("accId",accId);
        List<User> superUserList=userDao.getListByHQLWithNamedParams(hql,map);
        return superUserList;
    }

    @Override
    public List<User> getSpanOrgUsersByOid(Integer oid,PageInfo pageInfo) {
        Organization organization=orgDao.get(oid);
        if (organization.getOrgType()!=1){
            oid=organization.getPid();
        }
        List<Integer> oids= orgService.getOrgSonOrgIds(oid);// 总机构 子机构oid列表
        String hql=" from User where oid in (:oid) and isDuty in (:isDuty) and roleCode in (:roleCode)";
        Map<String,Object> params=new HashMap<>();
        params.put("oid",oids);
        params.put("isDuty", IsDutys.isDutyListCanLogin.getIsDutyList()); //User.getIsDutyListCanLogin());
        params.put("roleCode",RoleCodes.personStaff.getCodeList()); //Arrays.asList("staff","agent")); // 普通员工 和 被代理人员
        List<User> userList= userDao.getListByHQLWithNamedParams(hql,params,pageInfo);
        return  userList;
    }

    @Override
    public List<User> getSeeUserByAccId(Long accId,String isDuty) {
        String hql=" from User where accId=:accId and roleCode=:roleCode";
        Map<String,Object> params=new HashMap<>();
        params.put("accId",accId);
        if (!StringUtils.isEmpty(isDuty)){
            hql+=" and isDuty=:isDuty";
            params.put("isDuty",isDuty); //5-有效的可见人员  6- 无效的可见人员
        }
        params.put("roleCode","see"); //  可见人员
        List<User> userList= userDao.getListByHQLWithNamedParams(hql,params);
        return userList;
    }

    @Override
    public User getSeeUserByOid(Integer oid,String mobile) {
        String hql=" from User where oid=:oid and roleCode=:roleCode and mobile=:mobile";
        Map<String,Object> params=new HashMap<>();
        params.put("oid",oid);
        params.put("roleCode","see"); //  可见人员
        params.put("mobile",mobile); //  可见人员
        User seeUser= (User) userDao.getByHQLWithNamedParams(hql,params);
        return seeUser;
    }

    @Override
    public List<User> getSeeUsersByAccId(Long accId) {
        String hql=" from User where accId=:accId and roleCode=:roleCode and isDuty='6'";
        Map<String,Object> params=new HashMap<>();
        params.put("accId",accId);
        params.put("roleCode","see"); //  可见人员
        List<User> seeUsers=  userDao.getListByHQLWithNamedParams(hql,params);
        return seeUsers;
    }

    @Override
    public List<User> getOrgUsersAndSee(Integer oid) {
        String hql=" from User where oid=:oid and isDuty in (:isDuty) and roleCode in (:roleCode)";
        Map<String,Object> params=new HashMap<>();
        params.put("oid",oid);
        params.put("isDuty",User.getIsDutyListSpanOrg());
        params.put("roleCode",RoleCodes.personAndAgent.getCodeList());//Arrays.asList("staff","agent","super","see","smallSuper")); // 普通员工 和 被代理人员
        List<User> userList= userDao.getListByHQLWithNamedParams(hql,params);
        return userList;
    }

    @Override
    public List<Integer> getUserIdsByRoleCode(Integer oid, String roleCode) {
        String hql="select userID from User where roleCode=:roleCode and oid=:oid";
        Map<String,Object> params=new HashMap<>();
        params.put("oid",oid);
        params.put("roleCode",roleCode);
        List<Integer> userIds=userDao.getListByHQLWithNamedParams(hql,params);
        return userIds;
    }

    @Override
    public List<Integer> getUserIdsByIsDuty(List<String> isDutyList) {
        String hql="select userID from User where isDuty in(:isDutyList)";
        Map<String,Object> params=new HashMap<>();
        params.put("isDutyList",isDutyList);
        List<Integer> userIds=userDao.getListByHQLWithNamedParams(hql,params);
        return userIds;
    }

    //    @Override
//    public void updateSuperRankUrl(String rankUrl) {
//        String hql="update User set rankUrl=:rankUrl where roleCode='super' ";
//        Map<String,Object> map = new HashMap<>();
//        map.put("rankUrl",rankUrl);
//        userDao.queryHQLWithNamedParams(hql,map);
//    }
//

    @Override
    public void updateUsersRankUrl(PrintWriter writer) {
        List<User> userList = userDao.getListByHQLWithNamedParams("from User",null);
        userList.stream().forEach(item -> item.setRankUrl("empty/"));//把所有人设为"empty"
        Map<Integer, User> settedMap = userList.stream().filter(item->"super".equals(item.getRoleCode())).collect(Collectors.toMap(User::getUserID, Function.identity(), (key1, key2) -> key1));
        settedMap.values().stream().forEach(item -> item.setRankUrl("/"));//把超管设为"/"
        writer.println("修改超管……");
        writer.flush();
        long count, lastCount = 0;
        int loop = 0, layer = 0;
        while ((count = userList.stream().filter(user -> "empty/".equals(user.getRankUrl())).count())>0 && count!=lastCount && loop<3) {
            if(count == 0 && count==lastCount) {
                loop++;
            }
            writer.println("正在修改第"+(++layer)+"层");
            writer.flush();
            lastCount = count;
            userList.stream().filter(item ->settedMap.get(Integer.valueOf(item.getUserID())) == null && settedMap.containsKey(Integer.valueOf(item.getLeader()))).forEach(item-> {
                User leader=settedMap.get(Integer.valueOf(item.getLeader()));
                item.setRankUrl(leader.getRankUrl()+leader.getUserID()+"/");
                settedMap.put(item.getUserID(), item);
            });
        }
    }


    @Override
    public JsonResult checkLogin(User user, Organization organization, HttpServletRequest request) {
        Map<Integer,String> map=new HashMap<>();
        //1.118暂停服务 lixu
        if ("1".equals(user.getOrganization().getState())){
            map.put(1,"贵公司已被暂停服务，请告知公司管理人员！");
            return new JsonResult(0,null,"1","贵公司已被暂停服务，请告知公司管理人员！！");
        }

        //1.139中枢管控2  lixu 2020/12/17
        if("0".equals(organization.getInitState())&&"staff".equals(user.getRoleCode())){ //未正式 启用前， 以全权负责人身份 直接进入机构
            user=userService.getManageUserByMasterUserID(user.getUserID(),"general");
            if (user==null){
                return new JsonResult(0,null,"2","当前账户没有分配权限");
            } else {
                return new JsonResult(1,user);
            }
        }
        /////wyu:使用当前登录名查询当前登录名所具有的权限，将权限组织成字符串的形式（格式：aa,ab,ac,ad,ae）
        String popedom=userService.getPopedomStringByUser(user);
        if(popedom.isEmpty()) {
            return new JsonResult(0,null,"3","当前账户没有分配权限");
        }
        if("liveHelper".equalsIgnoreCase(organization.getCode())
                && !GetLocalIPUtils.isCmwPrj(request)) {
            return new JsonResult(0,null,"4","请从字幕精灵小程序或者字幕精灵PC端登录");
        }
        return new JsonResult(1,null);
    }


    @Override
    public void userSubscribeDefault(HttpServletRequest request, HttpServletResponse response, String token, AuthAcc acc, String tokenId, String deviceType, String brand, String model, String deviceUuid, String intent, List<User> users) {
        String hostUrl = GetLocalIPUtils.getRootPath(request);
        MobileDevice device = mqMobileDeviceService.loginDevice(acc, tokenId, MqMobileDeviceService.DeviceType.getByIndex(deviceType), brand, model, deviceUuid, intent, hostUrl);
        if(!AuthAcc.realAcc(acc)) {
            logger.error("call mqMobileDeviceService.loginDevice error userSubscribeDefault\n" + JSON.toJSONString(acc) + "\n" + JSON.toJSONString(device));
        }
        String clientIp = GetLocalIPUtils.getLocalIp(request);
        mqMobileDeviceService.subscribeDefault(device, acc, users, hostUrl, clientIp, MqMobileDeviceService.Level.mobileAcc);
//        result.put("deviceId", device.getId());
        acc.setDeviceId(device.getId());
        System.out.println("mobile deviceId innerEntry : " + acc.getDeviceId());
        authService.updateTokenAcc(response,token,acc);
    }


    @Override
    public List<User> getSoonFrozenUserList(Integer oid,PageInfo pageInfo) {
        String hql=" select u from User u left join AuthAcc a on u.accId=a.id where u.isDuty='1' and u.oid=:oid and u.roleCode in(:roleCodes) and :begin<=a.lastVerification and a.lastVerification<=:end and a.state=1";
        Date begin=NewDateUtils.changeDay(new Date(),-89);    //不超过89
        Date end=NewDateUtils.changeDay(new Date(),-75);   //超过75;
        Map<String,Object> map=new HashMap<>();
        map.put("oid",oid);
        map.put("roleCodes",RoleCodes.personOrg.getCodeList());
        map.put("begin",begin);
        map.put("end",end);
        List<User> userList=userDao.getListByHQLWithNamedParams(hql,map,pageInfo);
        List<Long> accIdList=new ArrayList<>();
        List<Integer> oidList=new ArrayList<>();

        for (User u:userList){
            accIdList.add(u.getAccId());
            oidList.add(u.getOid());
        }
        String accHql=" from AuthAcc where id in(:accIdList)";
        map.clear();
        map.put("accIdList",accIdList);
        List<AuthAcc> accList= userDao.getListByHQLWithNamedParams(accHql,map);
        Map<Long,Date> dateMap=new HashMap<>();
        for (AuthAcc authAcc:accList){
            dateMap.put(authAcc.getId(),authAcc.getLastVerification());
        }

        Map<Integer,String> orgMap=new HashMap<>();
        List<Organization> organizationList=orgService.getOrgByOids(oidList);
        for (Organization organization:organizationList){
            orgMap.put(organization.getId(),organization.getName());
        }

        for (User u:userList){
            u.setLastVerification(dateMap.get(u.getAccId()));
            if (u.getLastVerification()!=null) {
                u.setDays(NewDateUtils.getDaies(begin.getTime() - u.getLastVerification().getTime()));
            }
            u.setOrgName(orgMap.get(u.getOid()));
        }
        return userList;
    }


    @Override
    public List<User> getFrozenUserList(Integer oid, PageInfo pageInfo) {
        String hql="select u from User u left join AuthAcc a on u.accId=a.id where u.isDuty='1' and u.oid=:oid and u.roleCode in(:roleCodes) and a.state=0"; //a.lastVerification <:begin and
        Date begin=NewDateUtils.changeDay(new Date(),-89);    //超过
        Map<String,Object> map=new HashMap<>();
        map.put("oid",oid);
        map.put("roleCodes",RoleCodes.personOrg.getCodeList());
//        map.put("begin",begin);
        List<User> userList=userDao.getListByHQLWithNamedParams(hql,map,pageInfo);

        List<Integer> oidList=new ArrayList<>();

        for (User u:userList){
            oidList.add(u.getOid());
        }

        Map<Integer,String> orgMap=new HashMap<>();
        List<Organization> organizationList=orgService.getOrgByOids(oidList);
        for (Organization organization:organizationList){
            orgMap.put(organization.getId(),organization.getName());
        }

        for (User u:userList){
            u.setOrgName(orgMap.get(u.getOid()));
        }
        return userList;
    }

    @Override
    public JsonResult mergeAccUsers(AuthAcc source, AuthAcc destination, String newMobile) {
        String hql = "from User where accId=:accId";
        Map<String, Object> params = new HashMap<>(1);
        params.put("accId", source.getId());
        List<User> sourceUsers = userDao.getListByHQLWithNamedParams(hql, params);
        params.put("accId", destination.getId());
        List<User> destinationUsers = userDao.getListByHQLWithNamedParams(hql, params);
        List<User> intersectionDestUsers;
        if(sourceUsers.isEmpty()) {
            return new JsonResult(2, "无需合并");
        } else if(ObjectUtils.isNotEmpty(intersectionDestUsers = checkIntersection(sourceUsers, destinationUsers))){
            User intersectionDestUser = intersectionDestUsers.get(0);
            Organization intersectionOrg = intersectionDestUser.getOrganization();
            return new JsonResult(new MyException("-1", "您录入的手机号与"+intersectionOrg.getName()+"公司"+intersectionDestUser.getUserName()+"手机号相同。请确认！"));
        } else {
            return mergeUsers(sourceUsers, source.getMobile(), destination, newMobile);
        }
    }

    @Override
    public List<User> checkIntersection(Collection<User> source, Collection<User> destination) {
        if(ObjectUtils.isEmpty(source) || ObjectUtils.isEmpty(destination)) {
            return new ArrayList<>(0);
        }
        List<Integer> sourceOrgIds = source.stream().filter(u -> u.getMasterUserID()==null || u.getUserID().intValue() == u.getMasterUserID().intValue()).map(User::getOid).collect(Collectors.toList());
        return destination.stream().filter(u -> (u.getMasterUserID()==null || u.getUserID().intValue() == u.getMasterUserID().intValue()) && sourceOrgIds.contains(u.getOid())).collect(Collectors.toList());
    }

    @Override
    public JsonResult mergeUsers(Collection<User> sourceUsers, String oldMobile, AuthAcc destination, String newMobile) {
        for (User u : sourceUsers) {
            u.setMobile(destination.getMobile());
            u.setVersionNo(u.getVersionNo() == null ? 1 : u.getVersionNo() + 1);
            u.setAccId(destination.getId());
            userService.updateUser(u);
            if (StringUtils.isNotBlank(oldMobile) && !oldMobile.equalsIgnoreCase(newMobile) //修改手机号
                    && (u.getMasterUserID()==null || u.getUserID().intValue() == u.getMasterUserID().intValue())) { //是主 员工 才产生修改记录  身份数据不记录 人员信息修改历史
                userHistoryService.addUserHistoryByUser(u, "3", true);//保存历史记录
                userService.followSuperToGlpt(u, destination.getMobile());
            }
        }
        if (StringUtils.isNotEmpty(oldMobile)) {
            mqMobileDeviceService.forceLogoutDevices(oldMobile, "该账户的机构列表发生变动，请重新登陆。", null); // 强制下线 旧手机号
        }
        mqMobileDeviceService.forceLogoutDevices(newMobile, "该账户的机构列表发生变动，请重新登陆。", null); // 强制下线 新手机号
        return new JsonResult(1, "操作成功!");
    }

    @Override
    @Caching(evict={@CacheEvict(value = "userPopedoms", allEntries=true),@CacheEvict(value = "userPopedomTasks", allEntries=true)})
    public void clearPopedomsAndTasks() {

    }

    @Override
    public void sendH5UnLockedActiveCode(HttpServletRequest request,AuthInfoDto authInfo,User loginUser, String phone) {
        AuthAcc authAcc=authService.getLockedByMobile(phone);
        if (authAcc!=null) {
//            String url = GetLocalIPUtils.getRootPath(request);  h5激活连接 改成下面短链接
            WonderssLoginShortPathUtils wonderssLoginShortPathUtils = new WonderssLoginShortPathUtils();
            String url = wonderssLoginShortPathUtils.getLoginShortUrl(request);
            Map<String, String> params = new HashMap<String, String>(3) {{
                put("activeCode", authService.getOrCreateActiveCode(authAcc).getRight());
                put("url", url);
                put("mobile", loginUser.getMobile());
            }};
            smsService.sendMessage(SmsService.SendType.role, phone, authInfo, TemplateService.SmsContentTemplate.h5Unlock, params, null);
        }
    }

    @Override
    public List<String> getUserNamesByOid(Integer oid) {
        String hql = "select userName from User where isDuty in(:isDutys) and roleCode in(:roleCodes) and oid=:oid";
        Map<String, Object> params = new HashMap<>();
        params.put("oid", oid);
        params.put("roleCodes",RoleCodes.personOrg.getCodeList());
        params.put("isDutys",new ArrayList<String>(Arrays.asList("1","2")));
        List<String> userNames = userDao.getListByHQLWithNamedParams(hql, params);
        return userNames;
    }

    @Override
    public JsonResult forLeaving(Integer userId, String transferReason,User loginUser) {
        if (userId != null) {
            User user = userService.getUserByID(userId);
            if (user.getManagerCode() != null) {
                String cannotDimission = popedomService.cannotDimission(user);
                if (cannotDimission != null) {
                    return new JsonResult(new MyException("-1",cannotDimission + ",所以不能进行离职"));
//                    model.addAttribute("error", cannotDimission + ",所以不能进行离职");
//                    return "error";
//                    return "/generalAffairs/updateError";
                }
            }
            if (RoleTmpl.getStaffCodes().contains(user.getRoleCode())) {
                workAttendanceOldService.changeUserDepartment(user, null, WorkAttendanceOldService.ChangeDeptReasons.Resign);
            }
            if (!MyStrings.nulltoempty(user.getDepartment()).isEmpty()){
                Organization organization = orgService.getOrgByOid(Integer.parseInt(user.getDepartment()), OrgService.OrgType.department);
                organization.setUserNum(organization.getUserNum()==null?0:organization.getUserNum()-1);
                orgService.updateOrg(organization);
            }
//            if (!MyStrings.nulltoempty(user.getPostID()).isEmpty()){
//                Organization organization = orgService.getOrgByOid(Integer.parseInt(user.getPostID()), OrgService.OrgType.post);
//                organization.setUserNum(organization.getUserNum()==null?0:organization.getUserNum()-1);
//                orgService.updateOrg(organization);
//            }

            user.setIsDuty("2");//2表示离职
            user.setOffDutyDate(new Date());//离职时间
            user.setTransferReason(transferReason);//离职原因
            user.setUpdator(loginUser.getUserID());
            user.setUpdateName(loginUser.getUserName());
            userService.updateUser(user);
//            userService.updateSalaryIsUse(user);
            personnelContractService.terminatePerCon(user.getUserID());
            return new JsonResult(1,"操作成功");

        }
        return new JsonResult(new MyException("-2","参数错误"));
    }

    @Override
    public Map<String, Object> getAppHomePageAndWorks(User loginUser) {
        Map<String,Object> map=new HashMap<>();
        String popedom=getPopedomStringByUser(loginUser);
        HashMap<String, String> search = popedomService.getAllPopedomSearch(1,3);
        List<Popedom> popedoms = popedomService.findFullPopedomListByPopedom(popedom, search, loginUser.getOid(),1,3);

        List<Map<String, Object>> list = new ArrayList<>();
        List<UserPopedom> userPopedomList=new ArrayList<>();

        for (Popedom p:popedoms){
            String mid=p.getMid();
            if (p.getCode().contains("homePage")) {
                String pid = "";
                String button = ""; //控制手机端有没有按钮 0- 没有，1-有
                if (p.getCode().equalsIgnoreCase("homePageOne")) {
                    pid = "0";
                } else {
                    pid = p.getPid();
                }
                if (search.containsValue(mid)) {
                    button = "0";
                } else {
                    button = "1";
                }
                Map<String, Object> homeMap = new HashMap<>();
                homeMap.put("mid", mid);
                homeMap.put("name", p.getName());
                homeMap.put("pid", pid);
                homeMap.put("userId", loginUser.getUserID());
                homeMap.put("button", button);
                list.add(homeMap);
            }else {
                UserPopedom userPopedom=new UserPopedom();
                userPopedom.setUserId(loginUser.getUserID());
                userPopedom.setMid(mid);
                userPopedom.setOid(loginUser.getOid());
                userPopedom.setPid(p.getPid());
                userPopedom.setName(p.getName());
                userPopedomList.add(userPopedom);
            }
        }
        map.put("popedomList",userPopedomList);//工作页 权限
        map.put("homePageList",list);//主页权限
        return map;
    }
}