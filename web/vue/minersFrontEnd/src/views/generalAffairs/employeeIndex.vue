<!-- 职工档案 -->
<template>
  <div class="employeeIndex">
    <!--  主页面  -->
    <div v-if="mainPage === 0">
      <div>
        <div class="ty-left">
          <el-popover placement="bottom-start" :width="560" v-model:visible="searchVisible" trigger="manual">
            <div @click.stop>
              <table class="searchCon">
                <tbody>
                <tr v-if="matchType">
                  <td class="trS">
                    <div style="min-height:44px;">
                      <div class="ttlS">所属的机构</div>
                      <div class="checkAll ty-linkBtn" @click="checkAllBtn">全选</div>
                    </div>
                  </td>
                  <td>
                    <el-checkbox-group v-model="search.orgs">
                      <el-checkbox :label="org.id" v-for="org in sonOrgs">{{ org.name }}</el-checkbox>
                    </el-checkbox-group>
                  </td>
                </tr>
                <tr>
                  <td class="trS"><div class="ttlS">最高学历</div></td>
                  <td>
                    <el-checkbox-group v-model="search.education">
                      <el-checkbox label="1">研究生</el-checkbox>
                      <el-checkbox label="2">本科</el-checkbox>
                      <el-checkbox label="3">大专</el-checkbox>
                      <el-checkbox label="4" >中专或高中</el-checkbox>
                      <el-checkbox label="5">其他</el-checkbox>
                    </el-checkbox-group>
                  </td>
                </tr>
                <tr>
                  <td class="trS"><div class="ttlS">性别</div></td>
                  <td>
                    <el-checkbox-group v-model="search.gender">
                      <el-checkbox label="1">男</el-checkbox>
                      <el-checkbox label="0">女</el-checkbox>
                    </el-checkbox-group>
                  </td>
                </tr>
                <tr>
                  <td class="trS"><div class="ttlS">部门</div></td>
                  <td>
                    <el-checkbox-group v-model="search.department">
                      <el-checkbox :label="dep.id" v-for="dep in departments">{{ dep.name }}</el-checkbox>
                    </el-checkbox-group>
                  </td>
                </tr>
                <tr>
                  <td class="trS"><div class="ttlS">职位</div></td>
                  <td>
                    <el-checkbox-group v-model="search.post">
                      <el-checkbox :label="pos.id" v-for="pos in posts">{{ pos.name }}</el-checkbox>
                    </el-checkbox-group>
                  </td>
                </tr>
                <tr>
                  <td class="trS"><div class="ttlS">婚否</div></td>
                  <td>
                    <el-checkbox-group v-model="search.marry">
                      <el-checkbox label="1">未婚</el-checkbox>
                      <el-checkbox label="0">已婚</el-checkbox>
                    </el-checkbox-group>
                  </td>
                </tr>
                <tr>
                  <td class="trS"><div class="ttlS">出生年月</div></td>
                  <td></td>
                </tr>
                <tr>
                  <td class="trS"></td>
                  <td>
                    <div class="timePicker">
                      <el-date-picker style="width:100%;" v-model="search.birthday1" :teleported="false" type="month" value-format="YYYY-MM" >
                      </el-date-picker>
                    </div>
                    <span style="position: relative; top:-10px;">至</span>
                    <div class="timePicker">
                      <el-date-picker style="width:100%;" v-model="search.birthday2" :teleported="false" type="month" value-format="YYYY-MM" >
                      </el-date-picker>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td class="trS"><div class="ttlS">入职时间</div></td>
                  <td>
                    <div class="timePicker">
                      <el-date-picker style="width:100%;" v-model="search.onDutyDate1" :teleported="false" value-format="YYYY-MM-DD" >
                      </el-date-picker>
                    </div>
                    <span style="position: relative; top:-10px;">至</span>
                    <div class="timePicker">
                      <el-date-picker style="width:100%;" v-model="search.onDutyDate2" :teleported="false" value-format="YYYY-MM-DD" >
                      </el-date-picker>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td colspan="2">
                    <div style="text-align: right">
                      <button class="ty-btn ty-btn-gray ty-btn-big ty-circle-3" style="margin-right:20px; " @click="clearqueryEmployeeBtn">清空</button>
                      <button class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 " @click="queryEmployeeBtn">查询</button>
                    </div>
                  </td>
                </tr>
                </tbody>
              </table>

            </div>
            <template #reference>
              <span v-if="tabIndex===0" class="ty-btn ty-btn-green ty-btn-big ty-circle-5" @click="searchBtn">筛选</span>
            </template>
          </el-popover>
        </div>
        <div class="btnStyle">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 marRight20" @click="leadingStart">批量导入</span>
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 marRight20" @click="joinStaff">办理入职</span>
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 marRight20" @click="goFrozen">异常账户</span>
          <span class="ty-btn ty-btn-green ty-btn-big ty-circle-5" style="display: none">导出</span>
        </div>
      </div>
      <div style="margin-top: 20px"></div>
      <tySecondTab class="marBtm22" :namelist="namelist" :changeTabFun="changeTab" ></tySecondTab>
      <div >
        <div v-if="tabIndex===0">
          <!--    在职列表      -->
          <table class="ty-table ty-table-control">
            <tbody>
              <tr>
                <td>姓名</td>
                <td>性别</td>
                <td>手机号</td>
                <td v-if="matchType">所属的机构</td>
                <td>部门</td>
                <td>职位</td>
                <td>直接上级</td>
                <td>最高学历</td>
                <td>最后登陆时间</td>
                <td>劳动合同</td>
                <td>操作</td>
              </tr>
              <tr v-for="(item, index) in tabIndex0Data.list" :key="index">
                <td v-html="item.userName"></td>
                <td>{{ item.submitState == '1'? chargeSex(item.gender) : '' }} </td>
                <td v-html="item.mobile"></td>
                <td v-if="matchType">{{ item.orgName || '' }}</td>
                <td>{{ item.departName || '' }}</td>
                <td>{{ item.postName || '' }}</td>
                <td>{{ item.roleCode == 'super' ? '' : item.leaderName }}</td>
                <td>{{ item.submitState == '1'? chargeDegree(item.degree) : '' }} </td>
                <td>{{ (new Date(item.lastLoginDate).format('yyyy-MM-dd hh:mm:ss')) || '从未登陆' }} </td>
                <td width="260px" class="ty-td-control">
                  <div class="inlinePos" v-if="!item.personnelContract">未知</div>
                  <div class="inlinePos" :class="{'redMark': new Date(todayDate) > new Date(Number(item.personnelContract.validEnd) + 86399000)}" v-else>
                    {{ new Date(item.personnelContract.validStart).format('yyyy-MM-dd')}}至{{new Date(item.personnelContract.validEnd).format('yyyy-MM-dd')}}
                  </div>
                  <span class="ty-color-blue ty-right" @click="employeeContractSee(item)">查看</span>
                </td>
                <td>
                  <div v-if="tabIndex0Data.params.isDuty === 1">
                    <div v-if="item.roleCode === 'staff'">
                      <span class="ty-color-blue" @click="employeeInforView(item)">个人信息</span>
                      <span class="ty-color-blue" @click="handleUpdate(item)">档案管理</span>
                      <span class="ty-color-gray">指纹</span>
                      <span class="ty-color-red" @click="forLeaveBtn(item)">办理离职</span>
                    </div>
                    <div v-else-if="item.roleCode === 'agent'">
                      <span class="ty-color-gray">个人信息</span>
                      <span class="ty-color-gray">档案管理</span>
                      <span class="ty-color-gray">指纹</span>
                    </div>
                    <div v-else>
                      <span class="ty-color-blue" @click="employeeInforView(item)">个人信息</span>
                      <span class="ty-color-blue" @click="handleUpdate(item)">档案管理</span>
                      <span class="ty-color-gray">指纹</span>
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
          <TyPage v-if="tabIndex0Data.pageShow"
                  :curPage="tabIndex0Data.pageInfo.currentPageNo" :pageSize="tabIndex0Data.pageInfo.pageSize"
                  :allPage="tabIndex0Data.pageInfo.totalPage" :pageClickFun="tabIndex0PageClick"></TyPage>
        </div>
        <div v-if="tabIndex===1">
          <!--    离职列表      -->
          <div class="gapBt">本列表下的职工中无法再进入本机构！</div>
          <table class="ty-table ty-table-control">
            <tbody>
              <tr>
                <td>姓名</td>
                <td>性别</td>
                <td>手机号</td>
                <td v-if="matchType">所属的机构</td>
                <td>部门</td>
                <td>职位</td>
                <td>直接上级</td>
                <td>最高学历</td>
                <td>进入离职列表的操作</td>
                <td>劳动合同</td>
                <td>操作</td>
              </tr>
              <tr v-for="(item, index) in tabIndex0Data.list" :key="index">
                <td v-html="item.userName"></td>
                <td>{{ item.submitState == '1'? chargeSex(String(item.gender)) : '' }} </td>
                <td v-html="item.mobile"></td>
                <td v-if="matchType">{{ item.orgName || '' }}</td>
                <td>{{ item.departName || '' }}</td>
                <td>{{ item.postName || '' }}</td>
                <td>{{ item.roleCode == 'super' ? '' : item.leaderName }}</td>
                <td>{{ item.submitState == '1'? chargeDegree(item.degree) : '' }} </td>
                <td>
                  {{item.updateName}} {{(new Date(item.updateDate).format('yyyy-MM-dd hh:mm:ss'))}}
                </td>
                <td width="260px" class="ty-td-control">
                  <div class="inlinePos" v-if="!item.personnelContract">到期日:未知</div>
                  <div class="inlinePos" v-else>
                    到期日:{{ $filter.format(item.personnelContract.validEnd,'day')}}
                  </div>
                  <span class="ty-color-blue ty-right seeLv" @click="employeeLeaveContractSee(item)">查看</span>
                </td>
                <td>
                  <div>
                    <span class="ty-color-gray">复职</span>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>

          <TyPage v-if="tabIndex0Data.pageShow"
                  :curPage="tabIndex0Data.pageInfo.currentPageNo" :pageSize="tabIndex0Data.pageInfo.pageSize"
                  :allPage="tabIndex0Data.pageInfo.totalPage" :pageClickFun="tabIndex0PageClick"></TyPage>

        </div>
      </div>
    </div>
    <div v-if="mainPage === 1">
      <!--  importNoSave-->
      <div style="margin-bottom:20px; ">
        <span class="ty-btn ty-btn-yellow ty-circle-3 ty-btn-big btn" @click="clearNoSave" style="margin-right: 250px;">放 弃</span>
        <span class="ty-btn ty-btn-blue ty-circle-3 ty-btn-big btn" @click="stepNext">下一步</span>
      </div>
      <p>您共导入职工<span class="initAll">{{ importNoSaveData.initAll }}</span>条，
        其中以下<span class="initWrong">{{ importNoSaveData.initWrong }}</span>条存在问题，<span class="ty-color-red"> 无法保存至系统</span>。</p><br>
      <p>姓名或手机号未录入、手机号错误或与系统中已有号码相同等，均算作问题。</p><br>
      <table class="ty-table ty-table-control" style="width: 800px; ">
        <thead class="ty-head-yellow">
        <tr>
          <td>姓名</td>
          <td>手机号</td>
          <td>操作</td>
        </tr>
        </thead>
        <tbody>
        <tr v-for="(falseStaff, falseIndex) in importNoSaveData.falseList" :key="falseIndex">
          <td>{{ falseStaff.userName || '' }}</td>
          <td>{{ falseStaff.mobile || '' }}</td>
          <td>
            <span class="ty-color-blue" @click="initUpdate(falseStaff)">修改</span>
            <span class="ty-color-red" @click="initDel(falseStaff, falseIndex)">删除</span>
          </td>
        </tr>
        </tbody>
      </table>

    </div>
    <div v-if="mainPage === 2">
      <!--  importing-->
      <div>
        <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-3 btn" data-type="cancelSave" style="margin-right: 250px;" @click="cancelSave">放 弃</span>
        <span v-if="importingData.buttonState === 1" class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 btn" @click="confirmOrgTip">保 存</span>
        <span v-else class="ty-btn ty-btn-gray ty-btn-big ty-circle-3 btn" >保 存</span>
      </div>
      <div style="line-height:25px; font-size: 15px;margin-top: 30px;">
        <p>您共导入职工<span class="importSum" v-html="importingData.importSum"></span>条，可保存至系统的共<span class="saveSum" v-html="importingData.saveSum"></span>条。</p>
        <p>1、为每位职工选定“工作特点”后，才可保存至系统。</p>
        <p>2、Wonderss内请假、加班、报销等均与“直接上级”有关，故建议按实际情况给予选定。</p>
        <p class="exportStep">找到<span class="ty-color-red">最高领导的直接下属</span>后，将其“直接上级”选择为最高领导，之后逐级选择的操作方式较易理解。</p><br>

      </div>

      <table class="ty-table ty-table-control">
        <thead class="ty-head-yellow">
        <tr>
          <td>姓名</td>
          <td>手机号</td>
          <td>是否有下属</td>
          <td>直接上级</td>
          <td>工作特点</td>
          <td>操作</td>
        </tr>
        </thead>
        <tbody>
        <tr v-for=" (staff, staffIndex) in importingData.staffUserList" :key="staffIndex">
          <td>{{ staff.userName }}</td>
          <td>{{ staff.mobile }}</td>
          <td>
            <!--是否有下属-->
            <el-select v-model.number="staff.type" @change="selectThis0(staff)" placeholder="请选择">
              <el-option label="无" value="1"></el-option>
              <el-option label="有" value="0"></el-option>
            </el-select>
          </td>
          <td>
            <!-- 直接上级-->
            <el-select v-model="staff.leader" @change="selectThis2($event, staff)" filterable placeholder="请选择">
              <el-option v-for="(leader, lIndex) in staff.userList"
                         :key="lIndex" :label="leader.showName" :value="leader.id"></el-option>
            </el-select>
          </td>
          <td>
            <!-- 工作特点-->
            <el-select v-model="staff.managerCodeStr" @change="selectThis3(staff)" filterable placeholder="请选择">
              <el-option v-for="(manager, managerIndex) in importingData.manageList" :key="managerIndex"
                         :label="manager.showVal" :value="manager.value"></el-option>
            </el-select>
          </td>
          <td>
            <span class="ty-color-blue" @click="updateInport(staff)">修改</span>
            <span class="ty-color-red" @click="delteInport(staff, staffIndex)">删除</span>
          </td>
        </tr>
        </tbody>
      </table>

    </div>
    <div v-if="mainPage === 3">
      <div>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 btn" @click="gomain0">返回上一页</span>
      </div>
      <tySecondTab class="marBtm22" :namelist="namelist2" :changeTabFun="changeTab2" ></tySecondTab>
      <div v-if="tabIndex2===0">
        <p class="marTB20">以下为超过75天未登陆且未超过89天的职工，请职工尽快登陆，否则账号将无法登陆系统</p>
        <table class="ty-table ty-table-control">
          <tbody>
            <tr>
              <td>姓名</td>
              <td>性别</td>
              <td>手机号</td>
              <td v-if="matchType">所属机构</td>
              <td>部门</td>
              <td>职位</td>
              <td>直接上级</td>
              <td>最高学历</td>
              <td>冻结剩余天数</td>
            </tr>
            <tr v-for="(item, index) in tabIndex2Data.list" :key="index">
              <td v-html="item.userName"></td>
              <td>{{ item.submitState == '1'? chargeSex(String(item.gender)) : '' }} </td>
              <td v-html="item.mobile"></td>
              <td v-if="matchType">{{ item.orgName || '' }}</td>
              <td>{{ item.departName || '' }}</td>
              <td>{{ item.postName || '' }}</td>
              <td>{{ item.roleCode == 'super' ? '' : item.leaderName }}</td>
              <td>{{ item.submitState == '1'? chargeDegree(item.degree) : '' }} </td>
              <td>{{ Math.abs(item.days) }} 天</td>
            </tr>
          </tbody>
        </table>
        <TyPage v-if="tabIndex2Data.pageShow"
                :curPage="tabIndex2Data.pageInfo.currentPageNo" :pageSize="tabIndex2Data.pageInfo.pageSize"
                :allPage="tabIndex2Data.pageInfo.totalPage" :pageClickFun="tabIndex2PageClick"></TyPage>
      </div>
      <div v-if="tabIndex2===1">
        <p class="marTB20">以下为超过89天的职工，请职工尽快解锁账号</p>
        <table class="ty-table ty-table-control">
          <tbody>
            <tr>
              <td>姓名</td>
              <td>性别</td>
              <td>手机号</td>
              <td v-if="matchType">所属机构</td>
              <td>部门</td>
              <td>职位</td>
              <td>直接上级</td>
              <td>最高学历</td>
              <td>操作</td>
            </tr>
            <tr v-for="(item, index) in tabIndex2Data.list" :key="index">
              <td v-html="item.userName"></td>
              <td>{{ item.submitState == '1'? chargeSex(String(item.gender)) : '' }} </td>
              <td v-html="item.mobile"></td>
              <td v-if="matchType">{{ item.orgName || '' }}</td>
              <td>{{ item.departName || '' }}</td>
              <td>{{ item.postName || '' }}</td>
              <td>{{ item.roleCode == 'super' ? '' : item.leaderName }}</td>
              <td>{{ item.submitState == '1'? chargeDegree(item.degree) : '' }} </td>
              <td><span class="ty-color-blue" @click="activeUser(item)">帮他激活</span> </td>
            </tr>
          </tbody>
        </table>
        <TyPage v-if="tabIndex2Data.pageShow"
                :curPage="tabIndex2Data.pageInfo.currentPageNo" :pageSize="tabIndex2Data.pageInfo.pageSize"
                :allPage="tabIndex2Data.pageInfo.totalPage" :pageClickFun="tabIndex2PageClick"></TyPage>
      </div>

    </div>
    <div>

      <!--  办理入职-->
      <TyDialog v-show="joinStaffData.visible" width="1100" :dialogTitle="joinStaffData.ttl" color="blue" :dialogHide="hideJoinStaff">
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="hideJoinStaff">取消</el-button>
          <el-button class="bounce-ok" :disabled="joinStaffOkAble" @click="joinStaffOk">确定</el-button>
        </template>
        <template #dialogBody>
          <div class="ty-clear" style="height: 20px;"></div>
          <el-form class="joinStaffForm" ref="joinStaffRef" :rules="editStaffRules" :model="joinStaffData" label-position="top" label-width="84px" size="medium">
            <div class="flexBox">
              <div class="flexBox">
                <el-form-item label="姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名" prop="userName">
                  <el-input v-model="joinStaffData.userName" placeholder=""></el-input>
                </el-form-item>
                <el-form-item label="手机号" prop="mobile">
                  <el-input v-model="joinStaffData.mobile" :disabled="joinStaffData.oldInfo.roleName==='董事长'"></el-input>
                </el-form-item>
              </div>
              <div class="flexBox">
                <el-form-item label="入职时间">
                  <el-date-picker v-model="joinStaffData.date1" :disabled="joinStaffData.oldInfo.roleName==='董事长'" format="YYYY/MM/DD" value-format="YYYY/MM/DD" type="date">
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="部&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;门">
                  <el-popover placement="bottom-start" :width="336" v-model:visible="joinStaffData.departNameVisible" trigger="manual">
                    <div class="departCon">
                      <div v-for="(depart, index) in joinStaffData.allDepartmentList" :key="index" >
                        <div v-if="depart.depars.length > 0">{{ depart.level == 1 ? '一级部门' : '子级部门' }}</div>
                        <span v-for="(departItem, index) in depart.depars" :key="index" >
                      <span v-html="departItem.name" class="departItem" :class="chargeActive(departItem, depart.level) ? 'active':''"
                            @click="getSonDepar(departItem, depart.level)"></span>
                    </span>
                      </div>
                      <div>
                        <span class="closeCon" @click="closeConBtn">[收起]</span>
                      </div>

                    </div>
                    <template #reference>
                      <el-input v-model="joinStaffData.departName" placeholder="请选择部门" @click="getDeparts"></el-input>
                    </template>
                  </el-popover>
                </el-form-item>
              </div>
            </div>
            <div class="flexBox">
              <div class="flexBox">
                <el-form-item label="职&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;位">
                  <el-select v-model="joinStaffData.postID" placeholder="---- 请选择 ----">
                    <el-option
                        v-for="item in joinStaffData.postOptions"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="是否有下属">
                  <el-select v-model="joinStaffData.ordinaryEmployees" :disabled="joinStaffData.managerCodeEditable">
                    <el-option label="无" value="1"></el-option>
                    <el-option label="有" value="0"></el-option>
                  </el-select>
                </el-form-item>
              </div>
              <div class="flexBox">
                <el-form-item label="工作特点">
                  <el-select v-model="joinStaffData.managerCode" :disabled="joinStaffData.managerCodeEditable">
                    <el-option
                        v-for="item in joinStaffData.managerCodeOptions"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="直接上级">
                  <el-select  :disabled="joinStaffData.managerCodeEditable" v-model="joinStaffData.pid" >
                    <el-option
                        v-for="item in joinStaffData.leaderOptions"
                        :key="item.userID"
                        :label="item.userName"
                        :value="item.userID"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
            </div>
            <el-divider />
            <div class="gapBro cttPos">
              请录入这位职工劳动合同方面的信息！
            </div>
            <div class="flexBox">
              <div class="flexBox">
                <el-form-item label="合同编号" prop="sn">
                  <el-input v-model="joinStaffData.sn" placeholder="请录入"/>
                </el-form-item>
                <el-form-item label="签署日期" prop="signTime">
                  <el-date-picker
                      v-model="joinStaffData.signTime"
                      placeholder="请录入"
                      type="date"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      style="width: 100%"/>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="合同的有效期" prop="validTime" class="midSize">
                  <el-date-picker
                      v-model="joinStaffData.validTime"
                      type="daterange"
                      range-separator="到"
                      start-placeholder="请选择"
                      end-placeholder="请选择"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      :unlink-panels="true"
                      :disabled-date="join_disabledDate"
                  />
                </el-form-item>
              </div>
            </div>
            <div class="flexBox">
              <div class="join_imgUpload">
                <el-form-item class="midSize multiList" label="合同的扫描件或照片(共可上传9张)" prop="imgList">
                  <template #label>
                    合同的扫描件或照片(共可上传9张)
                    <div style="float: right">
                      <el-button type="primary" link @click="$refs.join_uploadImgBtn.click()">上传</el-button>
                    </div>
                  </template>
                  <div class="fileBox">
                    <span class="grayTxt" v-if="joinStaffData.imgList.length === 0">请上传</span>
                    <el-upload
                        v-model:file-list="joinStaffData.imgList"
                        :headers="imgUpload.uploadHeaders"
                        :action="imgUpload.uploadAction"
                        :accept="imgUpload.uploadLyc"
                        :data="imgUpload.postData"
                        :limit="9"
                        :multiple="true"
                        :on-success="join_imgSuccess"
                        :on-exceed="editContract_imgHandleExceed"
                        :before-upload="joinUploadImg"
                        list-type="text"
                        v-show="joinStaffData.imgList.length > 0"
                    >
                      <span style="display: none" ref="join_uploadImgBtn">上传</span>
                      <template #file="{ file,index }">
                        <span @click="join_uploadImgPreview(file)">
                          {{index + 1}}
                        </span>
                        <el-icon class="gapSm" @click="join_imgRemove(index)"><Remove /></el-icon>
                      </template>
                    </el-upload>
                  </div>
                </el-form-item>
              </div>
              <div class="flexBox">
                <el-form-item class="join_imgUpload" label="合同的可编辑版" prop="fileList">
                  <template #label>
                    合同的可编辑版
                    <div style="float: right">
                      <el-button type="primary" link @click="$refs.join_uploadFileBtn.click()">上传</el-button>
                    </div>
                  </template>
                  <div class="fileBox">
                    <span class="grayTxt" v-if="joinStaffData.fileList.length === 0">请上传</span>
                    <el-upload
                        class="upload_contract-file"
                        v-model:file-list="joinStaffData.fileList"
                        :headers="fileUpload.uploadHeaders"
                        :action="fileUpload.uploadAction"
                        :accept="fileUpload.uploadLyc"
                        :data="fileUpload.postData"
                        ref="join_upload"
                        :limit="1"
                        :on-exceed="join_fileHandleExceed"
                        :on-success="editContract_fileSuccess"
                        :before-upload="joinUploadImg"
                        v-show="joinStaffData.fileList.length > 0"
                    >
                      <span style="display: none" ref="join_uploadFileBtn">上传</span>
                    </el-upload>
                  </div>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                  <el-input v-model="joinStaffData.remark" placeholder="请录入"/>
                </el-form-item>
              </div>
            </div>
          </el-form>
        </template>
      </TyDialog>
        <!--   档案管理   -->
      <TyDialog v-show="editStaffData.visible" width="784" dialogTitle="档案管理" color="blue" :dialogHide="hideEditStaff">
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="hideEditStaff">取消</el-button>
          <el-button class="bounce-ok" @click="joinStaffOk">确定</el-button>
        </template>
        <template #dialogBody>
          <div class="ty-right">
            <span class="ty-btn ty-btn-big ty-btn-blue" @click="handleRecord">修改记录</span>
          </div>
          <div>
            <div style="line-height: 30px">
              <span class="ty-color-red">您可对以下各项提交修改申请，或直接修改。</span>
            </div>
            <div style="height:10px; " class="ty-clear" ></div>
          </div>
          <div class="ty-clear" style="height: 20px;"></div>
          <el-form :inline="true" class="editStaffForm" :rules="editStaffRules" :model="joinStaffData" label-position="left" label-width="84px" size="medium">
            <div>
              <el-form-item label="姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名" prop="userName">
                <el-input v-model="joinStaffData.userName" placeholde0r=""></el-input>
              </el-form-item>
              <el-form-item label="手机号" prop="mobile">
                <el-input @change="checkPhone" v-model="joinStaffData.mobile" :disabled="joinStaffData.oldInfo.roleName==='董事长'"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="入职时间">
                <el-date-picker v-model="joinStaffData.date1" :disabled="joinStaffData.oldInfo.roleName==='董事长'" format="YYYY/MM/DD" value-format="YYYY/MM/DD" type="date">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="部&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;门">
                <el-popover placement="bottom-start" :width="336" v-model:visible="joinStaffData.departNameVisible" trigger="manual">
                  <div class="departCon">
                    <div v-for="(depart, index) in joinStaffData.allDepartmentList" :key="index" >
                      <div v-if="depart.depars.length > 0">{{ depart.level == 1 ? '一级部门' : '子级部门' }}</div>
                      <span v-for="(departItem, index) in depart.depars" :key="index" >
                      <span v-html="departItem.name" class="departItem" :class="chargeActive(departItem, depart.level) ? 'active':''"
                            @click="getSonDepar(departItem, depart.level)"></span>
                    </span>
                    </div>
                    <div>
                      <span class="closeCon" @click="closeConBtn">[收起]</span>
                    </div>

                  </div>
                  <template #reference>
                    <el-input v-model="joinStaffData.departName" placeholder="请选择部门" @click="getDeparts"></el-input>
                  </template>
                </el-popover>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="职&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;位">
                <el-select v-model="joinStaffData.postID" placeholder="---- 请选择 ----">
                  <el-option
                      v-for="item in joinStaffData.postOptions"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="是否有下属">
                <el-select v-model="joinStaffData.ordinaryEmployees" :disabled="joinStaffData.managerCodeEditable">
                  <el-option label="无" value="1"></el-option>
                  <el-option label="有" value="0"></el-option>
                </el-select>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="工作特点">
                <el-select v-model="joinStaffData.managerCode" :disabled="joinStaffData.managerCodeEditable">
                  <el-option
                      v-for="item in joinStaffData.managerCodeOptions"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="直接上级">
                <el-select  :disabled="joinStaffData.managerCodeEditable" v-model="joinStaffData.pid" >
                  <el-option
                      v-for="item in joinStaffData.leaderOptions"
                      :key="item.userID"
                      :label="item.userName"
                      :value="item.userID"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="紧急联系人">
                <el-input v-model="joinStaffData.emergencyName" disabled></el-input>
              </el-form-item>

              <el-form-item class="mergePhone" label="&nbsp;">
                <span>紧急联系人的联系方式</span>
                <el-input v-model="joinStaffData.emergencyContact" disabled></el-input>
              </el-form-item>
            </div>
          </el-form>
        </template>
      </TyDialog>

      <TyDialog v-if="preViewVisible" width="800" dialogTitle="个人信息" color="green" :dialogHide="hidePreView">
        <template #dialogFooter>
        </template>
        <template #dialogBody>
          <div>
            <div class="marBtm22">
              <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" @click="hidePreView">退出预览</span>
              <span class="ty-btn ty-btn-big ty-circle-3 ty-right" :class="checkState == '0' ? 'ty-btn-gray ':'ty-btn-blue ' " @click="updateUserBtn">修改</span>
            </div>
            <div class="preViewCC">
              <div id="baseInfoDetails">
                <div class="userBaseInfo">
                  <div class="userImg">
                    <img :src="userInfo.user.imgPath" alt="" onerror="">
                  </div>
                  <div class="userCon">
                    <h4 class="userName">{{ userInfo.user.userName }}</h4>
                    <p class="baseDetail">
                      <span>{{ formatGender(userInfo.user.gender) }} / </span>
                      <span>{{ userInfo.user.nation || '-'}} / </span>
                      <span>{{ new Date(userInfo.user.birthday).format('yyyy年MM月dd日生') }} / </span>
                      <span>{{ userInfo.user.birthplace || '-' }} / </span>
                      <span>{{ userInfo.user.politicalStatus || '-'  }} / </span>
                      <span>{{ formatMarry(userInfo.user.marry  || '-' ) }} / </span>
                      <span>{{ formatDegree(userInfo.user.degree || '-' ) }} </span>
                    </p>
                    <div class="contectList clear">
                    <span class="contI">
                      <span class="tctImg fa fa-mobile"></span>
                      <span class="mobile">{{ userInfo.user.mobile }}</span>
                    </span>
                      <span class="contI">
                      <span class="tctImg fa fa-qq"></span>
                        <span class="qq">{{ userInfo.user.qq || '' }}</span>
                    </span>
                      <span class="contI">
                      <span class="tctImg fa fa-envelope"></span>
                      <span class="envelope">{{ userInfo.user.email || '' }}</span>
                    </span>
                    </div>
                  </div>
                </div>
                <div class="otherInfo">
                  <h5 class="ttlH5">个人技能</h5>
                  <div class="charact">
                    <div class="mmTtl">外语：</div>
                    <div class="mmCon">
                      <p>
                        <span>第一外语语种：{{ userInfo.user.firstLanguage || ''  }} {{ userInfo.user.firstForeignLevel || ''  }}</span>
                      </p>
                      <p>
                        <span>第二外语语种：{{ userInfo.user.secondLanguage || ''  }} {{ userInfo.user.secondForeignLevel  || '' }}</span>
                      </p>
                    </div>
                  </div>
                  <div class="charact">
                    <div class="mmTtl">计算机：</div>
                    <div class="mmCon">
                      <p>{{ userInfo.user.computerLevel }}</p>
                    </div>
                  </div>
                  <div class="charact">
                    <div class="mmTtl">其它技能描述：</div>
                    <div class="mmCon">
                      <p>{{ userInfo.user.otherSkills }}</p>
                    </div>
                  </div>
                </div>
              </div>
              <div class="otherInfo">
                <h5 class="ttlH5">教育经历</h5>
                <div id="eduHashMap">
                  <div v-for=" (education, eduIndex) in userInfo.personalEducations" :key="eduIndex">
                    <div class="areaBood"  v-if="!education.deleted">
                      <div class="charact">
                        <div class="mmTtl">{{ new Date(education.beginTime).format('yyyy/MM') }} - {{ new Date(education.endTime).format('yyyy/MM') }}</div>
                        <div class="timeSlot">
                      <span>{{ education.collegeName || '--' }} |
                        {{ education.departmentName || '--' }} |
                        {{ education.major || '--' }} |
                        {{ education.degreeDesc || '--' }} </span>
                        </div>
                      </div>
                      <div class="charact">
                        <div class="mmTtl">专业描述：</div>
                        <div class="mmCon"><p>{{ education.majorDesc }}</p></div>
                      </div>
                      <div class="charact">
                        <div class="mmTtl">补充说明：</div>
                        <div class="mmCon"><p>{{education.memo }}</p></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="otherInfo">
                <h5 class="ttlH5">工作经历</h5>
                <div id="workHashMap">
                  <div class="areaBood" v-for=" (occupation, occIndex) in userInfo.personnelOccupations" :key="occIndex">
                    <div class="charact">
                      <div class="mmTtl">{{ new Date(occupation.beginTime).format('yyyy/MM') }} - {{ new Date(occupation.endTime).format('yyyy/MM') }}</div>
                      <div class="timeSlot">
                      <span>
                        {{ occupation.corpName || '--' }} |
                        {{ occupation.corpNature || '--' }} |
                        {{ occupation.corpDepartment || '--' }} |
                        {{ occupation.post || '--' }} </span>
                      </div>
                    </div>
                    <div class="charact">
                      <div class="mmTtl">工作描述：</div>
                      <div class="mmCon"><p>{{ occupation.jobDesc }}</p></div>
                    </div>
                    <div class="charact">
                      <div class="mmTtl">补充说明：</div>
                      <div class="mmCon"><p>{{ occupation.memo }}</p></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </TyDialog>

      <!-- 手机号 确定修改确认 -->
      <TyDialog v-if="confirmEditMobileData.visible" width="400" dialogTitle="！提示" color="red" :dialogHide="hideConfirmEditMobile">
        <template #dialogFooter>
          <span class="ty-btn ty-btn-big ty-btn-yellow" @click="hideConfirmEditMobile">取消</span>
          <span class="ty-btn ty-btn-big ty-btn-blue" @click="confirmEditMobileOK">确定</span>
        </template>
        <template #dialogBody>
          <div>
            <div class="ty-center">
              <p>您此次的修改将导致该手机号无法登录系统。</p>
              <p>确定修改吗？</p>
            </div>
          </div>
        </template>
      </TyDialog>

      <!-- 查重提示 -->
      <TyDialog v-if="checkTip2Data.visible" width="400" dialogTitle="修改职工信息" color="blue" :dialogHide="hideCheckTip2">
        <template #dialogFooter>
          <div v-if="checkTip2Data.status=='23'">
            <span class="ty-btn ty-btn-big ty-btn-yellow" @click="hideCheckTip2">取消</span>
            <span class="ty-btn ty-btn-big ty-btn-blue" @click="checkTip2OK">确定</span>
          </div>
          <div v-if="checkTip2Data.status=='0'">
            <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-blue" @click="hideCheckTip2">我知道了</span>
          </div>
        </template>
        <template #dialogBody>
          <div v-html="checkTip2Data.checkTip_ms">
          </div>
        </template>
      </TyDialog>

      <!-- 批量导入上次未完成 -->
      <TyDialog v-if="importNotCompletedVisible" width="400" dialogTitle="！提示" color="red" :dialogHide="hideImportNotCompleted">
        <template #dialogFooter>
          <span class="ty-btn ty-btn-big ty-btn-yellow" @click="hideImportNotCompleted">取消</span>
          <span class="ty-btn ty-btn-big ty-btn-blue" @click="importNotCompletedOK">确定</span>
        </template>
        <template #dialogBody>
          <div>
            <div class="narrowBody unfinishedForm">
              <p>上次的批量导入尚未完成。</p>
              <div style="margin-top: 10px; ">
                <el-radio-group v-model="importNotCompletedContrl">
                  <el-radio :label="1">继续上次的操作</el-radio>
                  <el-radio :label="0">放弃上次的操作，重新批量导入</el-radio>
                </el-radio-group>
              </div>

            </div>
          </div>
        </template>
      </TyDialog>

      <!-- 批量导入 -->
      <TyDialog v-if="leadingVisible" width="600" dialogTitle="批量导入" color="blue" :dialogHide="leadingHide">
        <template #dialogFooter>
        </template>
        <template #dialogBody>
          <div>
            <div class="exportStep">
              <div class="stepItem">
                <p>第一步：下载空白的“职工名单”。</p>
                <div class="flexRow">
                  <span>职工名单</span>
                  <a :href="employee_blank_sheet" download="职工名单.xls" class="ty-btn ty-btn-blue">下 载</a>
                </div>
              </div>
              <div class="stepItem">
                第二步：在空白的“职工名单”中填写内容，并存至电脑。
              </div>
              <div class="stepItem">
                <p>第三步：点击“浏览”后，选择所保存的文件并上传。</p>
                <div class="flexRow">
                  <div class="upload_sect viewBtn">
                    <uploadFile ref="uploadFile"
                                module="职工导入"
                                committed.async="false"
                                :showSelect="false"
                                :showFileList="false"
                                :autoUpload="true"
                                ext='.xls,.xlsx'
                                extMsg='只能上传图片格式文件'
                                :successFun="handleSuccess"
                    >
                      <template #btnArea>
                        <span class="uploadify-button">{{ uploadAreaData.uploadBtn1_txt }}</span>
                      </template>
                    </uploadFile>
                  </div>
                  <div class="fileFullName">{{ uploadAreaData.originalFilename || '尚未选择文件' }}</div>
                </div>
              </div>
              <div class="stepItem">
                <p>第四步：点击“导入”。</p>
                <div class="flexRow">
                  <span class="ty-btn ty-btn-yellow ty-btn-middle" @click="leadingHide">取 消</span>
                  <span class="ty-btn ty-btn-blue ty-btn-middle" @click="sysUseImportOk">导 入</span>
                </div>
              </div>
              <div class="importIntro stepItem">
                <div style="text-align:left;color:red;"><span>导入说明：</span></div>
                <div style="text-align:left; font-size: 14px; line-height: 25px; ">
                  <span>1、请勿增加、删除或修改所下载“职工档案”空白表的“列”，否则上传会失败。</span> <br/>
                  <span>2、在电脑上保存“职工名单”时，可为该文件重新命名，但“浏览”时如选错文件则上传会失败。</span>
                </div>
              </div>
            </div>

          </div>
        </template>
      </TyDialog>

      <!-- 导入失败 -->
      <TyDialog v-if="importFalseVisible" width="400" dialogTitle="！提示" color="red" :dialogHide="hideImportFalse">
        <template #dialogFooter>
        </template>
        <template #dialogBody>
          <div>
            <div class="narrowBody">
              <h4>导入失败！</h4>
              <div>
                <div>原因可能为：</div>
                <div>1、修改了所下载表格中的“列”。</div>
                <div>2、选错了文件。</div>
                <div>3、文件太大，或里面含有图片等。</div>
              </div>
            </div>
          </div>
        </template>
      </TyDialog>

      <!-- 放弃后，本次批量导入 -->
      <TyDialog v-if="clearUserData.visible" width="400" dialogTitle="！提示" color="red" :dialogHide="hideClearUse">
        <template #dialogFooter>
          <span class="ty-btn ty-btn-big ty-btn-yellow" @click="hideClearUse">取消</span>
          <span class="ty-btn ty-btn-big ty-btn-blue" @click="clearUser">确定</span>
        </template>
        <template #dialogBody>
          <div>
            <div class="ty-center">
              <p>放弃后，本次批量导入的数据将消失不见。</p>
              <p>确定放弃吗？</p>
            </div>
          </div>
        </template>
      </TyDialog>

      <!-- 进入下一步- nextStep -->
      <TyDialog v-if="nextStepData.visible" width="400" dialogTitle="！提示" color="red" :dialogHide="hideNextStep">
        <template #dialogFooter>
          <span class="ty-btn ty-btn-big ty-btn-yellow" @click="hideNextStep">取消</span>
          <span class="ty-btn ty-btn-big ty-btn-blue" @click="nextStepOK">确定</span>
        </template>
        <template #dialogBody>
          <div class="tipCenter">
            <div class="ty-center">
              <div class="safeCondition" v-if="nextStepData.noSaveSum > 0">
                <p>还有<span id="noSaveMbSum" v-html="nextStepData.noSaveSum"></span>个手机号无法保存至系统。</p>
                <p>进入下一步，这些号码将被舍弃。</p>
              </div>
              <p>确定进入下一步吗？</p>
            </div>
          </div>
        </template>
      </TyDialog>

      <!-- 保存 lastSave -->
      <TyDialog v-if="lastSaveData.visible" width="400" dialogTitle="！提示" color="red" :dialogHide="hideLastSave">
        <template #dialogFooter>
          <span class="ty-btn ty-btn-big ty-btn-yellow" @click="hideLastSave">取消</span>
          <span class="ty-btn ty-btn-big ty-btn-blue" @click="lastSaveSure">确定</span>
        </template>
        <template #dialogBody>
          <div class="tipCenter">
            <div class="ty-center">
              <p>您共导入职工<span id="saveSum" v-html="lastSaveData.saveSum"></span>条，
                可保存至系统的共<span id="saveAble" v-html="lastSaveData.saveAble"></span>条。</p>
              <p>确定保存吗？</p>
            </div>
          </div>
        </template>
      </TyDialog>

      <!-- 修改职工信息 updateUser  -->
      <TyDialog v-if="updateUserData.visible" width="400" dialogTitle="修改职工信息" color="blue" :dialogHide="hideUpdateUser">
        <template #dialogFooter>
          <span class="ty-btn ty-btn-big ty-btn-yellow" @click="hideUpdateUser">取消</span>
          <span class="ty-btn ty-btn-big ty-btn-blue" @click="updateUserOK">确定</span>
        </template>
        <template #dialogBody>
          <div style="padding-right: 70px; padding-top: 20px;">
            <el-form ref="updateFrm" :model="updateUserData" :rules="updateUserDataRules" label-width="100px" >
              <el-form-item label="姓名" prop="name">
                <el-input v-model="updateUserData.name" clearable></el-input>
              </el-form-item>
              <el-form-item label="手机号" prop="phone">
                <el-input v-model="updateUserData.phone" clearable></el-input>
              </el-form-item>
            </el-form>
          </div>
        </template>
      </TyDialog>

      <!-- 我知道了 - 提示 iknowTip  -->
      <TyDialog v-if="iknowTipVisible" width="400" dialogTitle="修改职工信息" color="red" :dialogHide="hideIknowTip">
        <template #dialogFooter>
          <span class="ty-btn ty-btn-big ty-btn-blue" @click="hideIknowTip">我知道了</span>
        </template>
        <template #dialogBody>
          <div class="tipCenter">
            <div class="iknowWord" style="text-align: center" v-html="iknowWord"></div>
          </div>
        </template>
      </TyDialog>

      <!-- 查重提示 checkTip  -->
      <TyDialog v-if="checkTipData.visible" width="400" dialogTitle="修改职工信息" color="red" :dialogHide="hideCheckTip">
        <template #dialogFooter>
          <div class="notAllow" v-if="checkTipData.status === 0 || checkTipData.status === 4">
            <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-blue" @click="hideCheckTip">我知道了</span>
          </div>
          <div class="canAllow" v-if="checkTipData.status === 2|| checkTipData.status === 3">
            <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-yellow" @click="hideCheckTip">取  消</span>
            <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-blue" id="tjSure" @click="updateImportUserSure">确定</span>
          </div>
        </template>
        <template #dialogBody>
          <div class="tipCenter">
            <div style="text-align: center" v-html="checkTipData.checkTip_ms"></div>
          </div>
        </template>
      </TyDialog>

      <!--  员工基本信息修改页面  -->
      <TyDialog v-if="employeeUpdateData.visible" width="560" :dialogTitle="employeeUpdateData.ttl" color="blue" :dialogHide="hideEmployeeUpdate">
        <template #dialogFooter>
        </template>
        <template #dialogBody>
          <div style="margin-bottom: 20px;">
          </div>
          <div style="max-height: 300px; overflow-y: auto; ">
            <table class="ty-table ty-table-control ty-table-txtLeft">
              <tbody>
                <tr>
                  <td style="text-align: center;width: 50%;">资料名称</td><td style="text-align: center">操作</td>
                </tr>
                <tr>
                  <td>基本信息</td>
                  <td>
                    <span class="ty-color-blue" @click="editBase">编辑</span>
                    <span class="ty-color-blue" @click="editBaseLog">修改记录</span>
                  </td>
                </tr>
                <tr>
                  <td>教育经历</td>
                  <td>
                    <span class="ty-color-blue" @click="addEdu">新增</span>
                  </td>
                </tr>
                <tr v-for="(edu, eduIndex) in employeeUpdateData.eduList">
                  <td class="marL40">{{ edu.collegeName }}
                    <span v-if="edu.deleted" class="ty-color-red">(已删除)</span></td>
                  <td>
                    <span v-if="!edu.deleted" class="ty-color-blue" @click="editEdu(edu)">修改</span>
                    <span class="ty-color-blue" @click="editEduLog(edu)">修改记录</span>
                    <span v-if="!edu.deleted" class="ty-color-blue" @click="delEdu(edu)">删除</span>
                  </td>
                </tr>
                <tr>
                  <td>工作经历</td>
                  <td>
                    <span class="ty-color-blue" @click="addJob">新增</span>
                  </td>
                </tr>
                <tr v-for="(job, jobIndex) in employeeUpdateData.jobList">
                  <td class="marL40">{{ job.corpName }}
                    <span v-if="job.deleted" class="ty-color-red">(已删除)</span>
                  </td>
                  <td>
                    <span v-if="!job.deleted" class="ty-color-blue" @click="editJob(job)">修改</span>
                    <span class="ty-color-blue" @click="editJobLog(job)">修改记录</span>
                    <span v-if="!job.deleted" class="ty-color-blue" @click="delJob(job)">删除</span>
                  </td>
                </tr>
                <tr>
                  <td>身份证</td>
                  <td>
                    <span class="ty-color-blue" @click="editIDCard">编辑</span>
                    <span class="ty-color-blue" @click="editIDCardLog">修改记录</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>

      <!-- 基本信息 编辑  employeeUpdate -->
      <TyDialog v-if="employeeUpdateData.editBaseVisible" width="800" dialogTitle="基本信息" color="green" :dialogHide="hideBaseEdit">
        <template #dialogFooter>
          <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-gray" @click="hideBaseEdit">取消</span>
          <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-blue marL30" @click="updateEmployeeBaseSure">确定</span>
        </template>
        <template #dialogBody>
          <div>
          </div>
          <div class="baseTab" >
            <table>
              <tbody>
              <tr>
                <td>姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名</td>
                <td>{{ baseData.userName }}</td>
                <td style="width:80px;"></td>
                <td>性&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;别 </td>
                <td>
                  <el-radio-group v-model="baseData.gender">
                    <el-radio label="1">男</el-radio>
                    <el-radio label="0">女</el-radio>
                  </el-radio-group>
                </td>
              </tr>
              <tr>
                <td>民&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;族</td><td><el-input v-model="baseData.nation" placeholder=""></el-input></td>
                <td> </td>
                <td>出生日期</td><td> <el-date-picker v-model="baseData.birthday" type="date" placeholder="选择日期">
              </el-date-picker></td>
              </tr>
              <tr>
                <td>出  生  地</td><td><el-input v-model="baseData.birthplace" placeholder=""></el-input></td>
                <td> </td>
                <td>婚姻状况</td><td><el-select v-model.number="baseData.marry" placeholder="请选择">
                <el-option label="未婚" value="1"></el-option>
                <el-option label="已婚" value="0"></el-option>
              </el-select></td>
              </tr>
              <tr>
                <td>政治面貌 </td><td><el-input v-model="baseData.politicalStatus" placeholder=""></el-input></td>
                <td> </td>
                <td>最高学历</td><td><el-select v-model.number="baseData.degree" placeholder="请选择">
                <el-option label="研究生" value="1"></el-option>
                <el-option label="本科" value="2"></el-option>
                <el-option label="大专" value="3"></el-option>
                <el-option label="中专或高中" value="4"></el-option>
                <el-option label="其他" value="5"></el-option>
              </el-select></td>
              </tr>
              <tr>
                <td>第一外语语种</td><td><el-input v-model="baseData.firstLanguage" placeholder=""></el-input></td>
                <td> </td>
                <td>水平或证书</td><td><el-input v-model="baseData.firstForeignLevel" placeholder=""></el-input></td>
              </tr>
              <tr>
                <td>第二外语语种</td><td><el-input v-model="baseData.firstLanguage" placeholder=""></el-input></td>
                <td> </td>
                <td>水平或证书</td><td><el-input v-model="baseData.firstForeignLevel" placeholder=""></el-input></td>
              </tr>
              <tr>
                <td>计算机水平或证书</td>
                <td colspan="4"><el-input v-model="baseData.computerLevel" placeholder=""></el-input></td>
              </tr>
              <tr>
                <td>其他技能描述</td>
                <td colspan="4"><el-input v-model="baseData.otherSkills" placeholder=""></el-input></td>
              </tr>
              <tr>
                <td>手机号码</td>
                <td colspan="4"> {{ baseData.mobile }}
                  <span class="ty-linkBtn" @click="addMoreContact">添加更多联系方式</span>
                  <el-select v-model="uContactMethod" placeholder="请选择" v-if="uContactMethodVisible" @change="addMoreChange">
                    <el-option label="手机" value="1"></el-option>
                    <el-option label="QQ" value="2"></el-option>
                    <el-option label="Email" value="3"></el-option>
                    <el-option label="微信" value="4"></el-option>
                    <el-option label="微博" value="5"></el-option>
                    <el-option label="自定义" value="9"></el-option>
                  </el-select>
                </td>
              </tr>
              <tr>
                <td colspan="5">
                  <div class="concatItem" v-for="(contact, cIndex) in uContactData" :key="cIndex">
                    <p>{{ ttl }}</p>
                    <el-form-item  :label="contact.ttl">
                      <el-input v-model="contact.value">
                        <template #append>
                          <span class="ty-linkBtn-red">删除</span>
                        </template>
                      </el-input>
                    </el-form-item>
                  </div>

                  <div class="clr"></div>

                </td>
              </tr>
              </tbody>


            </table>
          </div>
        </template>
      </TyDialog>

      <!-- 联系人自定义弹窗 useDefinedLabel  -->
      <TyDialog v-if="uContactMethod9Visible" width="400" dialogTitle="自定义标签" color="green" :dialogHide="hideUContactMethod9Visible">
        <template #dialogFooter>
          <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-yellow" @click="hideUContactMethod9Visible">取  消</span>
          <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-blue" @click="addNewLable">确定</span>
        </template>
        <template #dialogBody>
          <div class="tipCenter">
            <p>自定义标签</p>
            <el-input placeholder="请录入联系方式的标签" clearable v-model="uContactMethod9Name"></el-input>
          </div>
        </template>
      </TyDialog>

      <!-- 基本信息修改记录 baseInfoRecord  -->
      <TyDialog v-if="baseLog.visible" width="600" :dialogTitle="baseLog.ttl" color="blue" :dialogHide="hideLog">
        <template #dialogFooter>
        </template>
        <template #dialogBody>
          <div v-if="baseLog.type == 'base'" >
            <p v-if="baseLog.number == 0">{{ baseLog.ttl == '基本信息修改记录' ? '基本信息尚未修改过。' : '当前数据尚未修改过。' }}</p>
            <div v-else>
              <div class="clear" >
                <p class="ty-left">当前数据为第<span class="baseUpdateNum">{{ baseLog.number }}</span>次修改后的结果。</p>
                <p class="ty-right">修改者：<span class="baseUpdateInfo">{{ baseLog.updateName }} {{ new Date(baseLog.updateTime).format('yyyy/MM/dd hh:mm:ss')  }}</span></p>
                <div class="ty-clear"></div>
              </div>
              <table class="ty-table" style="margin-top: 20px;">
                <tbody>
                <tr>
                  <td>记录</td><td>操作</td><td>创建者/修改者</td>
                </tr>
                <tr v-for="(logItem, logItemIndex) in baseLog.list">
                  <td>{{ logItem.dataState }}</td>
                  <td class="ty-td-control">
                    <span v-if="baseLog.ttl == '基本信息修改记录'" class="ty-color-blue" @click="recordDetails(logItem)">查看</span>
                    <span v-if="baseLog.ttl == '档案管理修改记录'" class="ty-color-blue" @click="recordDetails(logItem)">查看</span>
                  </td>
                  <td>{{ logItem.updateName }} {{ new Date(logItem.updateTime).format('yyyy/MM/dd hh:mm:ss') }}</td>
                </tr>
                </tbody>
              </table>
              <TyPage v-if="baseLog.pageShow"
                      :curPage="baseLog.pageInfo.currentPageNo" :pageSize="baseLog.pageInfo.pageSize"
                      :allPage="baseLog.pageInfo.totalPage" :pageClickFun="baseLogPageClick"></TyPage>
            </div>
          </div>

          <div v-if="baseLog.type == 'edu'" >
            <p v-if="baseLog.number == 0">教育经历尚未修改过</p>
            <div v-else>
              <div class="clear" >
                <p style="line-height: 30px;" v-if="baseLog.info.deleted">毕业院校: {{ baseLog.info.collegeName }}</p>
                <p v-if="!baseLog.info.deleted" class="ty-left">当前数据为第<span class="baseUpdateNum">{{ baseLog.number }}</span>次修改后的结果。</p>
                <p v-else><span class="ty-color-orange">该条数据已被删除。</span></p>
                <p class="ty-right">修改者：<span class="baseUpdateInfo">{{ baseLog.info.updateName }} {{ new Date(baseLog.info.updateDate).format('yyyy/MM/dd hh:mm:ss')  }}</span></p>
                <div class="ty-clear"></div>
              </div>
              <table class="ty-table" style="margin-top: 20px;">
                <tbody>
                <tr>
                  <td>记录</td><td>操作</td><td>创建者/修改者</td>
                </tr>
                <tr v-for="(logItem, logItemIndex) in baseLog.list">
                  <td>
                    <span v-if="logItem.dataState == '删除'" class="ty-color-orange">{{ logItem.dataState }}</span>
                    <span v-else>{{ logItem.dataState }}</span>
                  </td>
                  <td class="ty-td-control">
                    <span v-if="logItem.dataState == '删除'"> - -</span>
                    <span v-else class="ty-color-blue" @click="recordEduDetails(logItem)">查看</span>
                  </td>
                  <td>{{ logItem.updateName }} {{ new Date(logItem.updateDate).format('yyyy/MM/dd hh:mm:ss') }}</td>
                </tr>
                </tbody>
              </table>
              <TyPage v-if="baseLog.pageShow"
                      :curPage="baseLog.pageInfo.currentPageNo" :pageSize="baseLog.pageInfo.pageSize"
                      :allPage="baseLog.pageInfo.totalPage" :pageClickFun="baseLogPageClick"></TyPage>
            </div>
          </div>

          <div v-if="baseLog.type == 'job'">
            <p v-if="baseLog.number == 0">工作经历尚未修改过。</p>
            <div v-else>
              <div class="clear" >
                <p style="line-height: 30px;" v-if="baseLog.info.deleted">公司名称: {{ baseLog.info.corpName }}</p>
                <p v-if="!baseLog.info.deleted" class="ty-left">当前数据为第<span class="baseUpdateNum">{{ baseLog.number }}</span>次修改后的结果。</p>
                <p v-else><span class="ty-color-orange">该条数据已被删除。</span></p>
                <p class="ty-right">修改者：<span class="baseUpdateInfo">{{ baseLog.info.updateName }} {{ new Date(baseLog.info.updateDate).format('yyyy/MM/dd hh:mm:ss')  }}</span></p>
                <div class="ty-clear"></div>
              </div>
              <table class="ty-table" style="margin-top: 20px;">
                <tbody>
                <tr>
                  <td>记录</td><td>操作</td><td>创建者/修改者</td>
                </tr>
                <tr v-for="(logItem, logItemIndex) in baseLog.list">
                  <td>
                    <span v-if="logItem.dataState == '删除'" class="ty-color-orange">{{ logItem.dataState }}</span>
                    <span v-else>{{ logItem.dataState }}</span>
                  </td>
                  <td class="ty-td-control">
                    <span v-if="logItem.dataState == '删除'"> - -</span>
                    <span v-else class="ty-color-blue" @click="recordJobDetails(logItem)">查看</span>
                  </td>
                  <td>{{ logItem.updateName }} {{ new Date(logItem.updateDate).format('yyyy/MM/dd hh:mm:ss') }}</td>
                </tr>
                </tbody>
              </table>
              <TyPage v-if="baseLog.pageShow"
                      :curPage="baseLog.pageInfo.currentPageNo" :pageSize="baseLog.pageInfo.pageSize"
                      :allPage="baseLog.pageInfo.totalPage" :pageClickFun="baseLogPageClick"></TyPage>
            </div>
          </div>

          <div v-if="baseLog.type == 'IDCard'" >
            <p v-if="baseLog.number == 0">身份证尚未修改过。</p>
            <div v-else>
              <div class="clear" >
                <p class="ty-left">当前数据为第<span class="baseUpdateNum">{{ baseLog.number }}</span>次修改后的结果。</p>
                <p class="ty-right">修改者：<span class="baseUpdateInfo">{{ baseLog.updateName }} {{ new Date(baseLog.updateTime).format('yyyy/MM/dd hh:mm:ss')  }}</span></p>
                <div class="ty-clear"></div>
              </div>
              <table class="ty-table" style="margin-top: 20px;">
                <tbody>
                  <tr>
                    <td>记录</td><td>操作</td><td>创建者/修改者</td>
                  </tr>
                  <tr v-for="(logItem, logItemIndex) in baseLog.list">
                    <td>{{ logItem.dataState }}</td>
                    <td class="ty-td-control"><span class="ty-color-blue" @click="recordIDCardDetails(logItem)">查看</span></td>
                    <td>{{ logItem.updateName }} {{ new Date(logItem.updateTime).format('yyyy/MM/dd hh:mm:ss') }}</td>
                  </tr>
                </tbody>

              </table>

            </div>
          </div>


        </template>
      </TyDialog>

      <!--  个人基本信息查看 seeBaseInfor-->
      <TyDialog v-if="baseLogDetails.visible" width="800" dialogTitle="基本信息" color="blue" :dialogHide="hideBaseLogDetails">
        <template #dialogFooter>
        </template>
        <template #dialogBody>
          <div>
          </div>
          <div class="baseTab" >
            <table width="660px" v-if="baseLogDetails.ttl === '基本信息'">
              <tbody>
              <tr>
                <td width="132px">姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名</td>
                <td width="132px">{{ baseLogDetails.user.userName }}</td>
                <td style="width:80px;"></td>
                <td width="80px">性&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;别 </td>
                <td width="132px">
                  <div class="inputDiv">{{ formatGender(baseLogDetails.user.gender) }}</div>
                </td>
              </tr>
              <tr>
                <td>民&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;族</td><td>
                <div class="inputDiv">{{ baseLogDetails.user.nation}}</div></td>
                <td> </td>
                <td>出生日期</td><td><div class="inputDiv">{{ new Date(baseLogDetails.user.birthday).format('yyyy年MM月dd日生')  }}</div></td>
              </tr>
              <tr>
                <td>出  生  地</td><td><div class="inputDiv">{{ baseLogDetails.user.birthplace}}</div></td>
                <td> </td>
                <td>婚姻状况</td><td><div class="inputDiv">{{ formatMarry(baseLogDetails.user.marry)  }}</div></td>
              </tr>
              <tr>
                <td>政治面貌 </td><td><div class="inputDiv">{{ baseLogDetails.user.politicalStatus}}</div></td>
                <td> </td>
                <td>最高学历</td><td><div class="inputDiv">{{ formatDegree(baseLogDetails.user.degree) }}</div></td>
              </tr>
              <tr>
                <td>第一外语语种</td><td><div class="inputDiv">{{ baseLogDetails.user.firstLanguage}}</div></td>
                <td> </td>
                <td>水平或证书</td><td><div class="inputDiv">{{ baseLogDetails.user.firstForeignLevel}}</div></td>
              </tr>
              <tr>
                <td>第二外语语种</td><td><div class="inputDiv">{{ baseLogDetails.user.secondLanguage}}</div></td>
                <td> </td>
                <td>水平或证书</td><td><div class="inputDiv">{{ baseLogDetails.user.secondForeignLevel}}</div></td>
              </tr>
              <tr>
                <td>计算机水平或证书</td>
                <td colspan="4"><div class="inputDiv">{{ baseLogDetails.user.computerLevel}}</div></td>
              </tr>
              <tr>
                <td>其他技能描述</td>
                <td colspan="4"><div class="inputDiv">{{ baseLogDetails.user.otherSkills}}</div></td>
              </tr>
              <tr>
                <td>手机号码</td>
                <td colspan="4"> {{ baseLogDetails.user.mobile }}
                </td>
              </tr>
              <tr>
                <td colspan="5">
                  <div class="concatItem2" v-for="(contact, cIndex) in baseLogDetails.userContacts" :key="cIndex">
                    <div>
                      <span class="contactTtl">{{ contact.ttl }}</span>
                      <span class="contactCon">{{ contact.value}}</span>
                    </div>
                  </div>

                  <div class="clr"></div>

                </td>
              </tr>
              </tbody>
            </table>

            <table width="660px" v-if="baseLogDetails.ttl === '档案管理'" >
              <tbody>
              <tr>
                <td width="60px">姓&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;名</td>
                <td width="132px"><div class="inputDiv">{{ baseLogDetails.user.userName }}</div></td>
                <td style="width:80px;"></td>
                <td width="80px">手&nbsp;&nbsp;机&nbsp;&nbsp;号 </td>
                <td width="132px">
                  <div class="inputDiv">{{ (baseLogDetails.user.mobile) }}</div>
                </td>
              </tr>
              <tr>
                <td>入职时间</td><td>
                <div class="inputDiv">{{ new Date(baseLogDetails.user.onDutyDate ).format('yyyy/MM/dd') }}</div></td>
                <td> </td>
                <td>部&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;门</td><td><div class="inputDiv">{{  baseLogDetails.user.departName }}</div></td>
              </tr>
              <tr>
                <td>职&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;位</td><td>
                <div class="inputDiv">{{ baseLogDetails.user.postName  }}</div></td>
                <td> </td>
                <td>是否有下属</td><td><div class="inputDiv">{{  baseLogDetails.user.ordinaryEmployees == 1 ? '无' : '有' }}</div></td>
              </tr>
              <tr>
                <td>工作特点</td><td>
                <div class="inputDiv">{{ changeWorkFuc(baseLogDetails.user.managerCode) }}</div></td>
                <td> </td>
                <td>直接上级</td><td><div class="inputDiv">{{  baseLogDetails.user.leaderName }}</div></td>
              </tr>
              <tr>
                <td>紧急联系人</td><td>
                <div class="inputDiv">{{ changeWorkFuc(baseLogDetails.user.emergencyName) }}</div></td>
                <td> </td>
                <td style="font-size:14px; line-height:15px;  ">紧急联系人的<br>联系方式</td><td><div class="inputDiv">{{  baseLogDetails.user.emergencyContact }}</div></td>
              </tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>

      <!--  教育经历 addEduInfo -->
      <TyDialog v-show="editEduData.visible" width="800" dialogTitle="教育经历" color="blue" :dialogHide="hideEditEdu">
        <template #dialogFooter>
          <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-gray" @click="hideEditEdu">取消</span>
          <span class="ty-btn ty-circle-5 ty-btn-big  marL30" :class="editEduOKStatus ?'ty-btn-blue': 'ty-btn-gray'" @click="editEduOK">确定</span>
        </template>
        <template #dialogBody>
          <el-form ref="editEduFrm" :model="editEduData">
            <table width="660px" class="editEduFrm">
              <tbody>
                <tr>
                  <td width="132px" class="ty-txt-right">学习时间</td>
                  <td>
                    <el-date-picker v-model="editEduData.beginTime" type="month" format="YYYY/MM" value-format="YYYY/MM" placeholder="">
                    </el-date-picker>
                  </td>
                  <td style="width:80px;">—— ——</td>
                  <td>
                    <el-date-picker v-model="editEduData.endTime" type="month" format="YYYY/MM" value-format="YYYY/MM" placeholder="">
                    </el-date-picker></td>
                </tr>
                <tr>
                  <td class="ty-txt-right">毕业院校</td>
                  <td colspan="3"><el-input v-model="editEduData.collegeName" placeholder=""></el-input></td>
                </tr>
                <tr>
                  <td class="ty-txt-right">院       系</td>
                  <td><el-input v-model="editEduData.departmentName" placeholder=""></el-input></td>
                  <td class="ty-txt-right">专       业</td>
                  <td><el-input v-model="editEduData.major" placeholder=""></el-input></td>
                </tr>
                <tr>
                  <td class="ty-txt-right">学历/学位</td>
                  <td><el-input v-model="editEduData.degreeDesc" placeholder=""></el-input></td>
                  <td></td>
                  <td></td>
                </tr>
                <tr>
                  <td class="ty-txt-right">专业描述</td>
                  <td colspan="3"><el-input type="textarea" :rows="2" v-model="editEduData.majorDesc" placeholder=""></el-input></td>
                </tr>
                <tr>
                  <td class="ty-txt-right">补充说明</td>
                  <td colspan="3"><el-input type="textarea" :rows="2" v-model="editEduData.memo" placeholder=""></el-input></td>
                </tr>
              </tbody>
            </table>
          </el-form>
        </template>
      </TyDialog>

      <!--  教育经历信息查看 seeEduInfor-->
      <TyDialog v-if="eduLogDetails.visible" width="800" dialogTitle="教育经历" color="blue" :dialogHide="hideEduLogDetails">
        <template #dialogFooter>
        </template>
        <template #dialogBody>
          <div>
          </div>
          <div class="eduTab" >
            <table width="660px" class="ty-table-txtRight ty-table-txtLeft">
              <tbody>
                <tr>
                  <td width="132px">学习时间</td>
                  <td><div class="inputDiv">{{ eduLogDetails.info.beginTime}}</div></td>
                  <td style="width:80px; text-align: center; ">——</td>
                  <td><div class="inputDiv">{{ eduLogDetails.info.endTime}}</div></td>
                </tr>
                <tr>
                  <td>毕业院校</td>
                  <td colspan="3"><div class="inputDiv">{{ eduLogDetails.info.collegeName}}</div></td>
                </tr>
                <tr>
                  <td>院系</td>
                  <td><div class="inputDiv">{{ eduLogDetails.info.departmentName}}</div></td>
                  <td>专业</td>
                  <td><div class="inputDiv">{{ eduLogDetails.info.major}}</div></td>
                </tr>
                <tr>
                  <td>学历/学位</td>
                  <td><div class="inputDiv">{{ eduLogDetails.info.degreeDesc}}</div></td>
                  <td></td>
                  <td></td>
                </tr>
                <tr>
                  <td>专业描述</td>
                  <td colspan="3">
                    <div class="inputDiv inputArea">{{ eduLogDetails.info.majorDesc}}</div>
                  </td>
                </tr>
                <tr>
                  <td>补充说明</td>
                  <td colspan="3">
                    <div class="inputDiv inputArea">{{ eduLogDetails.info.memo}}</div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>

      <!-- 删除 教育经历 或 工作经验 deleteDetail  -->
      <TyDialog v-if="delData.visible" width="450" dialogTitle="！提示" color="red" :dialogHide="hideDel">
        <template #dialogFooter>
          <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-gray" @click="hideDel">取  消</span>
          <span v-if="delData.type==='edu'" class="ty-btn ty-circle-5 ty-btn-big ty-btn-red marL30" @click="delEduOK">确定</span>
          <span v-if="delData.type==='job'" class="ty-btn ty-circle-5 ty-btn-big ty-btn-red marL30" @click="delWorkOK">确定</span>
        </template>
        <template #dialogBody>
          <div class="tipCenter" v-if="delData.type=='edu'">
            删除后，您所预览的个人资料中将不再含有本条数据。 <br>
            如需要，您可在修改记录中查看。<br>
            确定删除本条教育经历吗？
          </div>
          <div class="tipCenter" v-if="delData.type=='job'">
            删除后，您所预览的个人资料中将不再含有本条数据。<br>
            如需要，您可在修改记录中查看。<br>
            确定删除本条工作经历吗？
          </div>
        </template>
      </TyDialog>

      <!--  工作经历 addWorkInfo -->
      <TyDialog v-show="editWorkData.visible" width="800" dialogTitle="工作经历" color="blue" :dialogHide="hideEditWork">
        <template #dialogFooter>
          <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-gray" @click="hideEditWork">取消</span>
          <span class="ty-btn ty-circle-5 ty-btn-big  marL30" :class="editWorkOKStatus ?'ty-btn-blue': 'ty-btn-gray'" @click="editJobOK">确定</span>
        </template>
        <template #dialogBody>
          <el-form>
            <table width="660px" class="editEduFrm">
              <tbody>
                <tr>
                  <td width="132px" class="ty-txt-right">服务时间</td>
                  <td>
                    <el-date-picker v-model="editWorkData.beginTime" type="month" format="YYYY/MM" value-format="YYYY/MM" placeholder="">
                    </el-date-picker>
                  </td>
                  <td style="width:80px;">—— ——</td>
                  <td>
                    <el-date-picker v-model="editWorkData.endTime" type="month" format="YYYY/MM" value-format="YYYY/MM" placeholder="">
                    </el-date-picker></td>
                </tr>
                <tr>
                  <td class="ty-txt-right">公司名称</td>
                  <td colspan="3"><el-input v-model="editWorkData.corpName" placeholder=""></el-input></td>
                </tr>
                <tr>
                  <td class="ty-txt-right">公司规模</td>
                  <td><el-input v-model="editWorkData.corpSize" placeholder=""></el-input></td>
                  <td class="ty-txt-right">公司性质</td>
                  <td><el-input v-model="editWorkData.corpNature" placeholder=""></el-input></td>
                </tr>
                <tr>
                  <td class="ty-txt-right">所在部门</td>
                  <td><el-input v-model="editWorkData.corpDepartment" placeholder=""></el-input></td>
                  <td class="ty-txt-right">职&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;位</td>
                  <td><el-input v-model="editWorkData.post" placeholder=""></el-input></td>
                </tr>
                <tr>
                  <td class="ty-txt-right">工作描述</td>
                  <td colspan="3"><el-input type="textarea" :rows="2" v-model="editWorkData.jobDesc" placeholder=""></el-input></td>
                </tr>
                <tr>
                  <td class="ty-txt-right">补充说明</td>
                  <td colspan="3"><el-input type="textarea" :rows="2" v-model="editWorkData.memo" placeholder=""></el-input></td>
                </tr>
              </tbody>
            </table>
          </el-form>
        </template>
      </TyDialog>

      <!--  工作经历信息查看 seeWorkInfor-->
      <TyDialog v-if="jobLogDetails.visible" width="800" dialogTitle="工作经历" color="blue" :dialogHide="hideJobLogDetails">
        <template #dialogFooter>
        </template>
        <template #dialogBody>
          <div>
          </div>
          <div class="eduTab" >
            <table width="660px" class="ty-table-txtRight ty-table-txtLeft">
              <tbody>
                <tr>
                  <td width="132px">服务时间</td>
                  <td><div class="inputDiv">{{ jobLogDetails.info.beginTime}}</div></td>
                  <td style="width:80px; text-align: center; ">——</td>
                  <td><div class="inputDiv">{{ jobLogDetails.info.endTime}}</div></td>
                </tr>
                <tr>
                  <td>公司名称</td>
                  <td colspan="3"><div class="inputDiv">{{ jobLogDetails.info.corpName}}</div></td>
                </tr>
                <tr>
                  <td>公司规模</td>
                  <td><div class="inputDiv">{{ jobLogDetails.info.corpSize}}</div></td>
                  <td>公司性质</td>
                  <td><div class="inputDiv">{{ jobLogDetails.info.corpNature}}</div></td>
                </tr>
                <tr>
                  <td>所在部门</td>
                  <td><div class="inputDiv">{{ jobLogDetails.info.corpDepartment}}</div></td>
                  <td>职       位</td>
                  <td><div class="inputDiv">{{ jobLogDetails.info.post}}</div></td>
                </tr>
                <tr>
                  <td>工作描述</td>
                  <td colspan="3">
                    <div class="inputDiv inputArea">{{ jobLogDetails.info.jobDesc}}</div>
                  </td>
                </tr>
                <tr>
                  <td>补充说明</td>
                  <td colspan="3">
                    <div class="inputDiv inputArea">{{ jobLogDetails.info.memo}}</div>
                  </td>
                </tr>
              </tbody>

            </table>
          </div>
        </template>
      </TyDialog>

      <!--  身份证编辑 identityCardEdit -->
      <TyDialog v-if="IDCardData.visible" width="660" dialogTitle="身份证" color="blue" :dialogHide="hideIDCard">
        <template #dialogFooter>
          <span class="ty-btn ty-circle-5 ty-btn-big ty-btn-gray" @click="hideIDCard">取  消</span>
          <span class="ty-btn ty-circle-5 ty-btn-big marL30" :class="editIDCardStatus ? 'ty-btn-blue' : 'ty-btn-gray' " @click="editIDCardOK">确定</span>
        </template>
        <template #dialogBody>
          <p style="margin-bottom: 40px; font-size: 14px;"><span class="ty-color-red">！！重要提示</span>  职工身份证号与住址，全公司仅您自己可见，其他人都见不到，故请注意保密！</p>
          <el-form-item label="身份证号">
            <el-input v-model="IDCardData.user.idCard"></el-input>
          </el-form-item>
          <el-form-item label="身份证上的住址">
            <el-input v-model="IDCardData.user.address"></el-input>
          </el-form-item>

        </template>
      </TyDialog>

      <!--  身份证编记录  查看  identityCardSee -->
      <TyDialog v-if="IDCardLogData.visible" width="660" dialogTitle="身份证" color="blue" :dialogHide="hideIDCardInfo">
        <template #dialogFooter>
        </template>
        <template #dialogBody>
          <p style="margin-bottom: 40px; font-size: 14px;"><span class="ty-color-red">！！重要提示</span>  职工身份证号与住址，全公司仅您自己可见，其他人都见不到，故请注意保密！</p>
          <table>
            <tbody>
              <tr>
                <td style="width:130px; text-align: right; padding-right: 10px;">身份证号</td>
                <td>
                  <span v-if="this.IDCardLogData.user.versionNo == '0'" >{{ this.IDCardLogData.user.cardNewValue }}</span>
                  <span v-else :class="this.IDCardLogData.user.cardOldValue === this.IDCardLogData.user.cardNewValue ? '' : 'ty-color-red' ">{{ this.IDCardLogData.user.cardNewValue }}</span>
                </td>
              </tr>
              <tr>
                <td style="width:130px; text-align: right; padding-right: 10px; ">身份证上的住址</td>
                <td>
                  <span v-if="this.IDCardLogData.user.versionNo == '0'" >{{ this.IDCardLogData.user.addressNewValue }}</span>
                  <span v-else :class="this.IDCardLogData.user.addressOldValue === this.IDCardLogData.user.addressNewValue ? '' : 'ty-color-red' ">{{ this.IDCardLogData.user.addressNewValue }}</span>
                </td>
              </tr>
            </tbody>
          </table>
        </template>
      </TyDialog>

      <!--  办理离职 ForLeaving  -->
      <TyDialog v-if="forLeaveData.visible" width="660" dialogTitle="办理离职" color="red" :dialogHide="hideForLeave">
        <template #dialogFooter>
          <span class="ty-btn ty-circle-5 ty-btn-big bounce-cancel" @click="hideForLeave">取消</span>
          <span class="ty-btn ty-circle-5 ty-btn-big bounce-ok" @click="forLeaveOk">确定</span>
        </template>
        <template #dialogBody>
          <p style="line-height: 40px; margin-top: 10px;">请输入离职原因:</p>
          <el-form-item label="">
            <el-input type="textarea" v-model="forLeaveData.reason"></el-input>
          </el-form-item>
        </template>
      </TyDialog>

      <!--  删除职工 delUser  -->
      <TyDialog v-if="updateUserData.delVisible" width="660" dialogTitle="！提示" color="red" :dialogHide="hideDelVisible">
        <template #dialogFooter>
          <span class="ty-btn ty-circle-5 ty-btn-big bounce-cancel" @click="hideDelVisible">取消</span>
          <span class="ty-btn ty-circle-5 ty-btn-big bounce-ok" @click="delUserOK">确定</span>
        </template>
        <template #dialogBody>
          <div style="margin-top: 30px;" class="ty-center">确定删除所导入的这个职工吗？</div>
        </template>
      </TyDialog>

      <!--  帮他激活 提示  -->
      <TyDialog v-if="activeUserData.visible" width="660" dialogTitle="！！重要提示" color="green" :dialogHide="hideactiveUserlVisible">
        <template #dialogFooter>
          <span class="ty-btn ty-circle-5 ty-btn-big " @click="hideactiveUserlVisible">取消</span>
          <span class="ty-btn ty-circle-5 ty-btn-big marL30" :class="activeUserData.radio1 == 1? 'bounce-ok':'ty-btn-gray'" @click="activeUserSendBtn">确定</span>
        </template>
        <template #dialogBody>
          <div class=" tipStyle" style="padding-left: 100px;">
            该职工是否还能收到手机号{{ activeUserData.editUser.mobile }}的短信？<br>
            如能，可继续“帮他激活”，如不能，那么此方式无法进行 <br>。您需要去更换他的账号。<br>

            <el-radio v-model="activeUserData.radio1" label="1">知道了，现在就向{{ activeUserData.editUser.mobile }}发送短信！</el-radio>

          </div>
        </template>
      </TyDialog>
      <!--  帮他激活 录入短信验证码  -->
      <TyDialog v-if="activeUserData.visible2" width="660" dialogTitle="录入短信验证码" color="blue" :dialogHide="hideactiveUserlVisible2">
        <template #dialogFooter>
          <span class="ty-btn ty-circle-5 ty-btn-big bounce-cancel" @click="hideactiveUserlVisible2">取消</span>
          <span class="ty-btn ty-circle-5 ty-btn-big " :class="activeUserData.code.length > 0 ? 'bounce-ok':'bounce-cancel'" @click="activeUserChargeCode">确定</span>
        </template>
        <template #dialogBody>
          <div class="ty-center tipStyle">
            请在下框中录入所收到的短信验证码！<br>
            <el-input style="width: 200px;" v-model="activeUserData.code" placeholder="请录入！"></el-input>

          </div>
        </template>
      </TyDialog>

      <TyDialog
          v-if="seeExpireContract"
          dialogTitle="查看合同"
          width="700"
          :dialogHide="seeExpireContractClose"
      >
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="seeExpireContractClose()">关闭</el-button>
        </template>
        <template #dialogBody>
          <div class="seeContractCon" style="width: 600px; margin: 0 auto">
            <el-row class="gapBro">
              <el-col :span="24">
                这位职工的合同已于{{ $filter.format(employeeInfo.personnelContract.validEnd,'day') }}到期！
              </el-col>
            </el-row>
            <el-descriptions
                class="seeExpire"
                :column="1"
            >
              <el-descriptions-item label="劳动合同" width="200"><el-icon @click="manage_seeContractDetail(employeeInfo,'see')"><ArrowRight /></el-icon></el-descriptions-item>
              <el-descriptions-item label="续约记录"><el-icon @click="manage_renewHis(employeeInfo)"><ArrowRight /></el-icon></el-descriptions-item>
              <el-divider />
              <el-descriptions-item label="与他续约"><el-icon @click="manage_renew(3)"><ArrowRight /></el-icon>
              </el-descriptions-item>
              <el-descriptions-item label="合同终止，不再续约"><el-icon @click="manage_stop()"><ArrowRight /></el-icon>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </template>
      </TyDialog>
      <TyDialog
          v-show="dialog_visible_manage_changeHis"
          dialogTitle="本版本合同的修改记录"
          width="700"
          :dialogHide="manage_changeHisClose"
      >
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="manage_changeHisClose">关闭</el-button>
        </template>
        <template #dialogBody>
        <div class="mainCon" style="width: 600px; margin: 0 auto">
          <div>
            <div class="row">
              当前数据为本版本合同第{{seeContractChangeHis.list.length - 1}}次修改后的结果。
              <div class="rightBtn">
                修改时间：{{$filter.format(seeContractChangeHis.list.at(-1)?.updateDate || seeContractChangeHis.list.at(-1)?.createDate)}}
              </div>
            </div>
            <el-table :data="seeContractChangeHis.list" border style="width: 100%; margin-top: 16px">
              <el-table-column label="记录">
                <template #default="scope">
                  {{scope.$index === 0?'本版本合同的原始信息':'第' +scope.$index+ '次修改后'}}
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="scope">
                  <el-button type="primary" link @click="manage_seeContractDetail(scope.row, 'change')"><b>查看</b></el-button>
                </template>
              </el-table-column>
              <el-table-column label="创建者/修改者" width="250">
                <template #default="scope">
                  {{scope.row.updateName || scope.row.createName}}
                  {{$filter.format(scope.row.updateDate || scope.row.createDate)}}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        </template>
      </TyDialog>
      <TyDialog
          v-show="dialog_visible_manage_renewHis"
          dialogTitle="本合同的续约记录"
          width="700"
          :dialogHide="manage_renewHisHide"
      >
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="manage_renewHisHide">关闭</el-button>
        </template>
        <template #dialogBody>
          <div class="mainCon" style="width: 600px; margin: 0 auto">
            <el-table :data="seeContractRenewHis.list" border style="width: 100%; margin-top: 16px">
              <el-table-column label="版本" width="200">
                <template #default="scope">
                  {{scope.$index === 0?'第1版（原始版本）':'第' + (scope.$index + 1) + '版（第' + scope.$index + '次续约后）'}}
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="scope">
                  <el-button type="primary" link @click="manage_seeContractDetail(scope.row, 'renew')"><b>查看</b></el-button>
                </template>
              </el-table-column>
              <el-table-column label="创建（时间为各版本合同的续约时间）" width="300">
                <template #default="scope">
                  {{scope.row.createName}}
                  {{scope.$index === 0?$filter.format(scope.row.createDate):$filter.format(scope.row.updateDate)}}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </template>
      </TyDialog>
      <TyDialog
          v-show="dialog_visible_seeContract"
          dialogTitle="查看合同"
          width="700"
          :dialogHide="seeContractClose"
      >
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="seeContractClose">关闭</el-button>
        </template>
        <template #dialogBody>
          <div class="exContractCon" style="width: 600px; margin: 0 auto">
            <el-row class="gapB" v-if="seeContractDetail.info.origin === 'see'">
              <el-col :span="5">
                <el-link type="primary" @click="manage_renew(1)">与他续约</el-link>
              </el-col>
              <el-col :span="14">
                <el-link type="danger" @click="manage_stop">合同终止</el-link>
              </el-col>
              <el-col :span="5" class="txtRight">
                <el-link type="primary" @click="manage_change">合同修改</el-link>
              </el-col>
            </el-row>
            <el-row v-if="seeContractDetail.info.origin === 'leave'">
              {{seeContractDetail.info.userName}}的劳动合同已被终止，操作 {{seeContractDetail.info.updateName}} {{$filter.format(seeContractDetail.info.updateDate)}}
            </el-row>
            <div class="gapBro">本版本合同的创建 {{seeContractDetail.info.createName}} {{$filter.format(seeContractDetail.info.createDate)}}</div>
            <el-descriptions
                class="margin-top"
                :column="1"
                border
            >
              <el-descriptions-item label="合同编号" width="200">{{seeContractDetail.info.sn}}</el-descriptions-item>
              <el-descriptions-item label="签署日期">{{seeContractDetail.info.signTime}}</el-descriptions-item>
              <el-descriptions-item label="合同的有效期">
                {{seeContractDetail.info.validStart}} 至 {{seeContractDetail.info.validEnd}}
              </el-descriptions-item>
              <el-descriptions-item label="合同的扫描件或照片">
                <el-image
                    v-for="(item, index) in seeContractDetail.info.imgList"
                    :key= index
                    style="width: 40px; height: 40px"
                    :src="item.url"
                    :zoom-rate="1.2"
                    :max-scale="7"
                    :min-scale="0.2"
                    :preview-src-list="seeContractDetail.info.previewList"
                    :initial-index="index"
                    fit="cover"
                    :preview-teleported="true"
                />
              </el-descriptions-item>
              <el-descriptions-item label="合同的可编辑版">
                <el-button type="primary" link @click="preview_File(seeContractDetail.info.filePath)" v-if="seeContractDetail.info.filePath"><b>查看</b></el-button>
              </el-descriptions-item>
              <el-descriptions-item label="备注">{{seeContractDetail.info.remark}}</el-descriptions-item>
              <el-descriptions-item label="本版本合同的修改记录" v-show="seeContractDetail.info.origin === 'renew' || seeContractDetail.info.origin === 'see' || seeContractDetail.info.origin === 'leave'">
                <el-button type="primary" link @click="manage_changeHis(seeContractDetail.info)"><b>查看</b></el-button>
              </el-descriptions-item>
              <el-descriptions-item label="该职工劳动合同的续约记录" v-show="seeContractDetail.info.origin === 'see' || seeContractDetail.info.origin === 'leave'">
                <el-button type="primary" link @click="manage_renewHis(employeeInfo)"><b>查看</b></el-button>
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </template>
      </TyDialog>
      <!--新增合同、修改合同、续约合同-->
      <TyDialog
          v-show="dialog_visible_editContract"
          :dialogTitle="editContractTitle"
          width="600"
          :dialogHide="editContractClose"
          id="editContract"
      >
        <template #dialogFooter>
          <span class="ty-btn ty-circle-5 ty-btn-big bounce-cancel" @click="editContractClose">取消</span>
          <span class="ty-btn ty-circle-5 ty-btn-big bounce-ok" @click="editContract_submit">确定</span>
        </template>
        <template #dialogBody>
          <div class="mainCon" style="width: 500px; margin: 0 auto">
            <el-row :gutter="20">
              <el-col :span="24">
              <span v-if="form_editContract.editType === 1">
                系统中没有这位职工的劳动合同。请录入相关信息！
              </span>
                <div v-if="form_editContract.editType === 2">
                  <p class="gapB">本版本合同的创建 {{ employeeInfo.personnelContract.createName	}} {{ $filter.format(employeeInfo.personnelContract.createDate) }}。</p>
                  <p>如需要，可对合同的本版本进行修改。</p>
                </div>
                <span v-if="form_editContract.editType === 3">
               请录入续约后的合同信息。
              </span>
                <el-divider />
              </el-col>
            </el-row>
            <el-form
                :rules="editContractRules"
                label-position="top"
                ref="editContractForm"
                class="editContract"
                :model="form_editContract"
                label-width="110"
            >
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="合同编号" prop="sn">
                    <el-input v-model="form_editContract.sn" placeholder="请录入"/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="签署日期" prop="signTime">
                    <el-date-picker
                        v-model="form_editContract.signTime"
                        placeholder="请录入"
                        type="date"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        style="width: 100%"/>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="合同的有效期" prop="validTime">
                    <el-date-picker
                        v-model="form_editContract.validTime"
                        type="daterange"
                        range-separator="到"
                        start-placeholder="请选择"
                        end-placeholder="请选择"
                        format="YYYY-MM-DD"
                        value-format="YYYY-MM-DD"
                        :unlink-panels="true"
                        :disabled-date="disabledDate"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="合同的扫描件或照片(共可上传9张)" prop="imgList" class="widthLimit">
                <template #label>
                  合同的扫描件或照片(共可上传9张)
                  <div style="float: right">
                    <el-button type="primary" link @click="$refs.uploadImgBtn.click()">上传</el-button>
                  </div>
                </template>
                <div class="fileBox">
                  <el-upload
                      v-model:file-list="form_editContract.imgList"
                      :headers="imgUpload.uploadHeaders"
                      :action="imgUpload.uploadAction"
                      :accept="imgUpload.uploadLyc"
                      :data="imgUpload.postData"
                      :limit="9"
                      :multiple="true"
                      :on-success="editContract_imgSuccess"
                      :on-preview="editContract_imgPreview"
                      :on-remove="editContract_imgRemove"
                      :on-exceed="editContract_imgHandleExceed"
                      list-type="picture-card"
                  >
                    <span style="display: none" ref="uploadImgBtn">上传</span>
                  </el-upload>
                </div>
              </el-form-item>
              <el-form-item label="合同的可编辑版" prop="fileList" class="widthLimit">
                <template #label>
                  合同的可编辑版
                  <div style="float: right">
                    <el-button type="primary" link @click="$refs.uploadFileBtn.click()">上传</el-button>
                  </div>
                </template>
                <div class="fileBox">
                  <el-upload
                      class="upload_contract-file"
                      v-model:file-list="form_editContract.fileList"
                      :headers="fileUpload.uploadHeaders"
                      :action="fileUpload.uploadAction"
                      :accept="fileUpload.uploadLyc"
                      :data="fileUpload.postData"
                      ref="upload"
                      :limit="1"
                      :on-exceed="editContract_fileHandleExceed"
                      :on-success="editContract_fileSuccess"
                  >
                    <span style="display: none" ref="uploadFileBtn">上传</span>
                  </el-upload>
                </div>
              </el-form-item>
              <el-form-item label="备注" prop="remark">
                <el-input v-model="form_editContract.remark" placeholder="请录入"/>
              </el-form-item>
            </el-form>
          </div>
        </template>
      </TyDialog>
    </div>
  </div>
</template>
<script>
import { beforeRouteLeave, initNav } from "@/utils/routeChange"
import {
  getScreenList,
  getDepartMentList,
  getPostList,
  getSelect,
  checkSonDepartment,
  addUser,
  unfinishedImportUser,
  newImportUser,
  saveImportUser,
  giveUpImportUser,
  completeImportUser,
  updateFalseUserEnter,
  selectOptionalLeader,
  updateImportUser,
  allImportUserEnter,
  updateUserBasicInfo,
  getUserBasicInfoHistory,
  getUserHistory,
  addPersonalEducation,
  updatePersonalEducation,
  getPersonalEducationHistory,
  personalEducationHistories,
  markDeleteEducation,
  addPersonnelOccupation,
  updatePersonnelOccupation,
  personnelOccupationHistories,
  getPersonnelOccupationHistory,
  markDeleteOccupation,
  updateUserIdCard,
  getUserIdCardHistories,
  getUserIdCard,
  getUserArchives,
  updateEssentialInformation,
  forLeaving,
  getAllManageAndPosts,
  getOrgSonOrgs,
  deleteImportUser,
  updateImportUserMobile,
  soonFrozenUsers,
  frozenUsers,
  helpActivationVerificationCode,
  checkHelpActivationVerification,
  activationUser,
  updateUserMobile,

} from "@/api/generalAffairs.js"
import { mobileCheckRepeat } from "@/api/api.js"
import { userInfoPreview } from "@/api/userInfo.js"
import { chargeRole, handleNull } from "@/utils/utils"
import { validateMobile } from "@/utils/formTest.js"
import tySecondTab from "@/components/tySecondTab.vue"
import imgError from '@/assets/commonImg/avatar_default_small.png'
import employee_blank_sheet from '@/assets/templateFiles/employee_blank_sheet.xls'
import auth from '@/sys/auth'
import uploadFile from "@/components/uploadFile.vue"
import * as empApi from "@/api/generalAffairs";
import {previewFile} from "@/utils/downloadFile";

export default {
  data() {
    return {
      pageName:'employeeIndex',
      namelist: ['在职列表','离职列表'],
      namelist2: ['即将冻结的账号','已冻结的账号'],
      tabIndex: 0 ,
      tabIndex2: 0 ,
      matchType: false ,
      dangAnManageData:{
        visible:false,
        editUser:null,
      },
      tabIndex0Data:{
        params:{ },
        pageShow: true,
        pageInfo:{ 'currentPageNo': 3, 'pageSize': 10 , 'totalPage': 15  },
        list: [],
      },
      tabIndex2Data:{
        params:{ },
        pageShow: false,
        pageInfo:{ 'currentPageNo': 3, 'pageSize': 10 , 'totalPage': 15  },
        list: [],
      },
      searchVisible: false,
      departments:[],
      sonOrgs:[],
      posts:[],
      search:{
        orgs: [],
        education:[],
        gender:[],
        department:[],
        post:[],
        marry:[],
        birthday1: '',
        birthday2: '',
        onDutyDate1: '',
        onDutyDate2: '',
      },
      editObj: null ,
      delVisible: null ,
      confirmEditMobileData:{
        visible: false,
      },
      checkTip2Data:{
        visible: false,
        checkTip_ms: '',
        type: '',
        status: '',
      },
      joinStaffData:{
        ttl:'',
        visible: false,
        oldInfo:'',
        type:'', // 'add', 'update'
        departNameVisible: false,
        userName:'',
        mobile:'',
        date1:'',
        departItem:'',
        departName:'',
        postID:'',
        postName:'',
        ordinaryEmployees:'',
        managerCode:'',
        pid:'',
        postOptions:[],
        leaderOptions:[],
        managerCodeOptions:[],
        allDepartmentList:[],
        selectedDepars:[],
        imgList: [],
        fileList: [],
        sn: '',
        signTime: '',
        remark: '',
        validTime: [], // 自定义的，接收开始时间和结束时间的组合字段
      },
      editStaffData:{
        visible: false
      },
      joinStaffRules:{
        userName: [ { required: true, message: '请输入姓名', trigger: 'blur' }],
        mobile: [ { required: true, message: '请输入手机号', trigger: 'blur' }],
        sn: [
          { required: true, message: '请输入合同编号', trigger: 'blur' }
        ],
        validTime: [
          { type: 'array', required: true, message: '请选择合同的有效期', trigger: 'change' }
        ]
      },
      editStaffRules:{
        userName: [ { required: true, message: '请输入姓名', trigger: 'blur' }],
        mobile: [ { required: true, message: '请输入手机号', trigger: 'blur' }],
      },
      preViewVisible: false,
      editUserObj:null,
      userInfo:null,
      checkState:null,
      importNotCompletedVisible:false,
      importNotCompletedContrl:-1,
      leadingVisible:false,
      employee_blank_sheet: employee_blank_sheet,
      uploadAreaData:{
        uploadBtn1_txt: '浏览',
        filePath: '',
        originalFilename: '',
      },
      importFalseVisible: false,
      mainPage: 0,
      importingData: {
        importSum:0,
        saveSum:0,
        buttonState:0, // 确定按钮状态1- 变亮 0- 置灰
      },
      importNoSaveData:{
        initAll:0,
        initWrong:0,
        falseList:[],
        userList:[]

      },
      clearUserData:{
        visible: false,
        type: 0,
      },
      nextStepData:{
        visible: false,
        noSaveSum:0
      },
      lastSaveData:{
        visible: false,
        saveSum: 0,
        saveAble: 0,

      },
      updateUserData:{
        delVisible: false,
        visible: false,
        name:'',
        phone:'',
        oldPhone:'',
        type: -1,
        editStaff: null ,
      },
      updateUserDataRules:{
        name:[{ required: true, message: '请输入姓名', trigger: 'blur' }] ,
        phone:[
            { required: true, message: '请输入手机号', trigger: 'blur' },
            { validator: validateMobile.bind(this), trigger: 'blur' },
            ] ,
      },
      iknowTipVisible:false,
      iknowWord:'',
      checkTipData:{
        visible: false,
        checkTip_ms: '',
        status: '',
        name: '',

      },
      employeeUpdateData:{
        visible: false,
        editBaseVisible: false,
        ttl: false,
        eduList: [],
        jobList: [],
        user:null,
        userContacts:[],
      },
      baseData: {
      },
      uContactData: null,
      uContactMethodVisible:false,
      uContactMethod9Visible:false,
      uContactMethod9Name:'',
      uContactMethodList: [],
      uContactMethod: '',
      baseLog:{
        visible: false,
        pageShow: false,
        list:[],
        number:0,
        pageInfo:null,
        info:null,
        params:null,
        type:'', // 'base' -基本信息 'edu'-教育
      },
      baseLogDetails:{
        ttl: '',
        visible: false,
        user:null,
        userContacts:[],
      },
      jobLogDetails:{
        visible: false,
        info:null
      },
      eduLogDetails:{
        visible: false,
        info:null
      },
      editEduData:{
        editEduObj: null,
        visible: false,
        type: '',
        beginTime:'',
        endTime:'',
        collegeName:'',
        departmentName:'',
        major:'',
        degreeDesc:'',
        majorDesc:'',
        memo:'',
      },
      delData:{
        visible:false,
        editObj:null,
        type:null
      },
      editWorkData:{
        visible: false,
        type: '',
        editObj: '',
        beginTime: '',
        endTime: '',
        corpName: '',
        corpSize: '',
        corpNature: '',
        corpDepartment: '',
        post: '',
        majorDesc: '',
        memo: ''
      },
      IDCardData:{
        visible: false,
        user:null,
      },
      IDCardLogData:{
        visible: false,
        user:null,
      },
      forLeaveData:{
        visible:false,
        editObj:null,
        reason:''
      },
      activeUserData:{
        editUser:null,
        visible:false,
        visible2:false,
        code:'',
        radio1:0,
        checked1:0,
        fiveCount:0,
        codeFourCount:0,
        sendCodeTime:{},
      },
      todayDate: '',
      dialog_visible_editContract: false,
      dialog_visible_seeContract: false,
      dialog_visible_manage_changeHis: false,
      dialog_visible_manage_renewHis: false,
      form_editContract: { // 合同
        editType: 1, // 1：新增 2：修改 3：续约
        cusInfo: {},
        sn: '',
        signTime: '',
        validTime: [], // 自定义的，接收开始时间和结束时间的组合字段
        validTimeMin: '',
        validStart: '',
        validEnd: '',
        imgList: [],
        removeImgList: [],
        fileList: [],
        remark: '',
        type: 1 // 1普通续约 2停用续约 3过期续约 (只有续约传）
      },
      editContractRules: {
        sn: [
          { required: true, message: '请输入合同编号', trigger: 'blur' }
        ],
        validTime: [
          { type: 'array', required: true, message: '请选择合同的有效期', trigger: 'change' }
        ]
      },
      rootPath: { fileUrl :'',  ow365url :'',  uploadUrl :'',  webRoot :'' },
      imgUpload: {
        uploadHeaders:{ 'Token': auth.getToken() },
        uploadAction: auth.webRoot + '/uploads/uploadfyByFile.do',
        uploadLyc: '.png,.jpeg,.jpg,.gif',
        postData: {
          module: '职工档案', userId: auth.getUserID()
        },
      },
      fileUpload: {
        uploadHeaders:{ 'Token': auth.getToken() },
        uploadAction: auth.webRoot + '/uploads/uploadfyByFile.do',
        uploadLyc: '.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt',
        postData: {
          module: '职工档案', userId: auth.getUserID()
        },
      },
      seeContractDetail: {
        info: {
          fileList: []
        }
      },
      seeContractChangeHis: {
        info: {},
        list: []
      },
      seeContractRenewHis: {
        info: {},
        list: []
      },
      employeeInfo: {},
      joinBtnState: false,
      seeExpireContract: false,
      //joinStaffOkAble: false
    }
  },
  computed:{
    contractMust () {
      let able = false
      if (this.joinStaffData.sn !== '' || (this.joinStaffData.validTime && this.joinStaffData.validTime.length > 0) || this.joinStaffData.imgList.length > 0 || this.joinStaffData.remark !== ''
          || this.joinStaffData.signTime !== '' || this.joinStaffData.fileList.length > 0) {
        able = true
      }
      return able
    },
    editContractTitle() {
      let arr = ['查看', '修改合同', '续约合同']
      return arr[this.form_editContract.editType - 1]
    },
    editIDCardStatus(){
      let status = false
      let old = this.employeeUpdateData.user
      let sameNum = 0
      if(old.idCard === this.IDCardData.user.idCard){ sameNum++;  }
      if(old.address === this.IDCardData.user.address){ sameNum++;  }
      if(sameNum === 2){}else{ status = true }
      return status
    },
    editWorkOKStatus(){
      let status = false
      if(this.editWorkData.type === 'add'){
        let nullNum = 0
        if(this.editWorkData.beginTime.length === 0){ nullNum++;  }
        if(this.editWorkData.endTime.length === 0){ nullNum++;  }
        if(this.editWorkData.corpName.length === 0){ nullNum++;  }
        if(this.editWorkData.corpSize.length === 0){ nullNum++;  }
        if(this.editWorkData.corpNature.length === 0){ nullNum++;  }
        if(this.editWorkData.corpDepartment.length === 0){ nullNum++;  }
        if(this.editWorkData.post.length === 0){ nullNum++;  }
        if(this.editWorkData.jobDesc.length === 0){ nullNum++;  }
        if(this.editWorkData.memo.length === 0){ nullNum++;  }
        if(nullNum === 9){
        }else{
          status = true
        }
      }
      else if(this.editWorkData.type === 'update'){
        let sameNum = 0
        if(this.editWorkData.beginTime === this.editWorkData.editObj.beginTime){ sameNum++;  }
        if(this.editWorkData.endTime === this.editWorkData.editObj.endTime){ sameNum++;  }
        if(this.editWorkData.corpName === this.editWorkData.editObj.corpName){ sameNum++;  }
        if(this.editWorkData.corpSize === this.editWorkData.editObj.corpSize){ sameNum++;  }
        if(this.editWorkData.corpNature === this.editWorkData.editObj.corpNature){ sameNum++;  }
        if(this.editWorkData.corpDepartment === this.editWorkData.editObj.corpDepartment){ sameNum++;  }
        if(this.editWorkData.post === this.editWorkData.editObj.post){ sameNum++;  }
        if(this.editWorkData.jobDesc === this.editWorkData.editObj.jobDesc){ sameNum++;  }
        if(this.editWorkData.memo === this.editWorkData.editObj.memo){ sameNum++;  }
        console.log('sameNum=', sameNum)

        if(sameNum === 9){
        }else{
          status = true
        }
      }
      return status
    },
    editEduOKStatus(){
      let status = false
      if(this.editEduData.type === 'add'){
        let nullNum = 0
        if(this.editEduData.beginTime.length === 0){ nullNum++;  }
        if(this.editEduData.endTime.length === 0){ nullNum++;  }
        if(this.editEduData.collegeName.length === 0){ nullNum++;  }
        if(this.editEduData.departmentName.length === 0){ nullNum++;  }
        if(this.editEduData.major.length === 0){ nullNum++;  }
        if(this.editEduData.degreeDesc.length === 0){ nullNum++;  }
        if(this.editEduData.majorDesc.length === 0){ nullNum++;  }
        if(this.editEduData.memo.length === 0){ nullNum++;  }
        if(nullNum === 8){
        }else{
          status = true
        }
      }else if(this.editEduData.type === 'update'){
        let sameNum = 0
        console.log('editEduData=', this.editEduData)
        console.log('editEduData.editEduObj=', this.editEduData.editEduObj)
        if(this.editEduData.beginTime === this.editEduData.editEduObj.beginTime){ sameNum++;  }
        if(this.editEduData.endTime === this.editEduData.editEduObj.endTime){ sameNum++;  }
        if(this.editEduData.collegeName === this.editEduData.editEduObj.collegeName){ sameNum++;  }
        if(this.editEduData.departmentName === this.editEduData.editEduObj.departmentName){ sameNum++;  }
        if(this.editEduData.major === this.editEduData.editEduObj.major){ sameNum++;  }
        if(this.editEduData.degreeDesc === this.editEduData.editEduObj.degreeDesc){ sameNum++;  }
        if(this.editEduData.majorDesc === this.editEduData.editEduObj.majorDesc){ sameNum++;  }
        if(this.editEduData.memo === this.editEduData.editEduObj.memo){ sameNum++;  }

        if(sameNum === 8){
        }else{
          status = true
        }
      }
      console.log('status=', status)
      return status
    },
    joinStaffOkAble(){
      return !this.joinBtnState
    }
  },
  beforeRouteLeave(to, from, next) {
      beforeRouteLeave(to, from, next,this)
  },
  components: {
    tySecondTab,
    uploadFile,
  },
  created(){
    initNav({mid: 'kb', name: '职工档案', pageName: this.pageName}, this.mountedFun(), this)
  },
  destroyed () {
  },
  methods: {
    checkTip2OK(){
      if(this.checkTip2Data.type == 'update'){
        this.checkTip2Data.visible = false
        this.confirmEditMobileOK()

      }else if(this.checkTip2Data.type == 'add'){

      }

    },
    hideCheckTip2(){
      this.checkTip2Data.visible = false
    },
    confirmEditMobileOK(){
      let params = {
        "userId": this.joinStaffData.oldInfo.userID,
        "phone": this.joinStaffData.mobile,
      }
      updateUserMobile(params).then(res1=>{
        let res = res1.data
        if(res){
          this.hideConfirmEditMobile()
          this.getScreenListFun(this.tabIndex0Data.params)
        }else{
          this.$message.error("修改失败，请重新修改！")
          this.joinStaffData.mobile = this.joinStaffData.oldInfo.mobile
        }
      }).catch(err=>{
        console.log('err=',err)
      })
    },
    hideConfirmEditMobile(){
      this.confirmEditMobileData.visible = false
    },
    checkPhone(){
      let userID = this.joinStaffData.oldInfo.userID
      let params = {
        mobile: this.joinStaffData.mobile,
        passiveUserId: userID
      }
      if(this.joinStaffData.oldInfo.mobile != params.mobile){
        mobileCheckRepeat(params).then(res1=>{
          let data = res1.data
          var status = data["state"]; // state 1-查重通过  0-与在职人员相同  2- 与离职人员相同 3- 与历史记录相同
          if (status === 1) {
            this.confirmEditMobileData.visible = true
          } else if (status === 2 || status === 3) {
            this.checkTip2Data.visible = true
            this.checkTip2Data.checkTip_ms = data.cont
            this.checkTip2Data.status = '23'
          }else if(status === 0) {
            this.checkTip2Data.visible = true
            this.checkTip2Data.checkTip_ms = data.cont
            this.checkTip2Data.status = '0'
          }

        }).catch(err=>{
          console.log('err=', err)
        })
      }
    },
    activeUserSendBtn(){
      if(this.activeUserData.radio1 == 1){
        this.activeUserData.visible = false

        console.log('sendCodeTime=', this.activeUserData.sendCodeTime)
        if(this.activeUserData.sendCodeTime.time){
          let curTime = (new Date()).getTime()
          let cha = curTime - this.activeUserData.sendCodeTime.time
          // if(cha < 1*2000){ // 校验是不是在2秒之内
          if(cha < 10*60000){ // 校验是不是在10分钟之内
            this.$message.error('短信已发送，请勿重复发送！')
            this.activeUserData.visible2 = true
            return false
          }else if(this.activeUserData.fiveCount>=4){ // 校验 发送了几次
            this.$message.error('已发短信多次，今日不可继续操作了。请明日继续吧。')
            return false
          }
        }

        let params = { phone: this.activeUserData.editUser.mobile  }
        helpActivationVerificationCode(params).then(res1=>{
          let res = res1.data
          let data = res.data
          let success = res.success

          let user = this.activeUserData.editUser
          let sendCodeTimeInfo = {}
          let sendCodeTimeList = JSON.parse(localStorage.getItem('sendCodeTimeList') || '[]')
          let same = false
          sendCodeTimeList.forEach(sendItem=>{
            if(sendItem.user.userID == user.userID && sendItem.user.oid == user.oid){
              sendItem.time = (new Date()).getTime()
              sendItem.fiveCount++
              sendCodeTimeInfo = sendItem
              same = true
            }
          })
          if(!same){ // 没找到相同的
            sendCodeTimeInfo = {
              user: user ,
              time:(new Date()).getTime(),
              fiveCount: 1,
              codeFourCount : 0
            }
            sendCodeTimeList.push(sendCodeTimeInfo)
          }
          this.activeUserData.sendCodeTime = sendCodeTimeInfo
          console.log('存的sendCodeTimeList ', sendCodeTimeList)
          localStorage.setItem('sendCodeTimeList', JSON.stringify(sendCodeTimeList))


          if(success === 1){
            this.$message.success(data)
            if(res.errorCode === '5'){
              this.activeUserData.visible2 = true
              this.activeUserData.code = ''
            }else{

            }
          }else if(success === 0){
            this.$message.error(res.errorMsg)
          }
          if(success === 1 || res.errorCode === '5'){
            this.activeUserData.visible2 = true
            this.activeUserData.code = ''
          }

        }).catch(err=>{
          console.log('err=', err)
        })
      }
    },
    activeUserChargeCode(){
      if(this.activeUserData.code.length > 0){
        console.log('codeFourCount=', this.activeUserData.codeFourCount)
        if(this.activeUserData.codeFourCount >=4){
          this.$message.error('已连续操作失败四次，今日不可继续操作了。请明日继续吧。')
          this.activeUserData.visible2 = false
          return false
        }
        let params = {
          phone: this.activeUserData.editUser.mobile,
          code: this.activeUserData.code
        }
        checkHelpActivationVerification(params).then(res1=>{
          let res = res1.data
          let success = res.success
          let data = res.data
          if(success === 1){
            this.activeUserData.visible2 = false
            this.$message.success('激活成功！这个账号能继续登录Wonderss了！')

            let params = { passiveUserId: this.activeUserData.editUser.userID }
            this.activationUserFun(params, 2)

          }else if(success === 0){
            let user = this.activeUserData.editUser
            let sendCodeTimeList = JSON.parse(localStorage.getItem('sendCodeTimeList') || '[]')
            sendCodeTimeList.forEach(sendItem=>{
              if(sendItem.user.userID == user.userID && sendItem.user.oid == user.oid){
                let codeFourDayTime = sendItem.codeFourDayTime
                if(codeFourDayTime){
                  let otime = new Date(codeFourDayTime).format('yyyy-MM-dd')
                  let ctime = (new Date()).format('yyyy-MM-dd')
                  if(otime == ctime){// 同一天
                    sendItem.codeFourCount = ++this.activeUserData.codeFourCount
                    sendItem.codeFourDayTime = (new Date()).getTime()
                  }else{ // 不是同一天
                    sendItem.codeFourCount = 1
                    sendItem.codeFourDayTime = (new Date()).getTime()
                  }
                }else{ // 之前没有
                  sendItem.codeFourCount = 1
                  sendItem.codeFourDayTime = (new Date()).getTime()
                }
              }
            })
            localStorage.setItem('sendCodeTimeList', JSON.stringify(sendCodeTimeList))

            if(this.activeUserData.codeFourCount >= 4){
              this.$message.error('已连续操作失败四次，今日不可继续操作了。请明日继续吧。')
            }else {
              this.$message.error('操作失败！请确认后，重新录入短信验证码！')
            }

          }
        }).catch(err=>{
          console.log('err=', err)
        })
      }
    },
    activationUserFun(params,type){
      activationUser(params).then(res1=>{
        let res = res1.data
        var data = res.data
        var success = res.success
        if (success === 1) {
          this.$message.success('操作成功')
          if(type === 2){ // 已冻结的
            let data = this.tabIndex2Data.params
            this.frozenUsersFun(data)
          }else if(type === 1){ // 档案管理

          }

        } else {
          this.$message.error(res.errorMsg)
        }

      }).catch(err=>{
        console.log('err=', err)
      })
    },
    hideactiveUserlVisible(){
      this.activeUserData.visible = false
    },
    hideactiveUserlVisible2(){
      this.activeUserData.visible2 = false
    },
    activeUser(userItem){
      this.activeUserData.editUser = userItem
      this.activeUserData.visible = true
      this.activeUserData.radio1 = ''

      this.activeUserData.sendCodeTime = {}
      this.activeUserData.fiveCount = 0
      this.activeUserData.codeFourCount = 0

      let sendCodeTimeList = JSON.parse(localStorage.getItem('sendCodeTimeList') || '[]')
      sendCodeTimeList.forEach(sendItem=>{
        if(sendItem.user.userID == userItem.userID && sendItem.user.oid == userItem.oid){
          this.activeUserData.sendCodeTime = sendItem
          this.activeUserData.fiveCount = sendItem.fiveCount
          let codeFourDayTime = sendItem.codeFourDayTime
          if(codeFourDayTime){
            let otime = new Date(codeFourDayTime).format('yyyy-MM-dd')
            let ctime = (new Date()).format('yyyy-MM-dd')
            if(otime == ctime){ // 同一天
              this.activeUserData.codeFourCount = sendItem.codeFourCount || 0
            } else{ // 不是同一天
              this.activeUserData.codeFourCount = 0
            }
          }else{ // 之前没有
            this.activeUserData.codeFourCount = 0
          }
        }
      })


    },
    gomain0(){
      this.mainPage = 0
      this.getDutyList()
    },
    goFrozen(){
      this.soonFrozenUsersFun({ pageSize: 20, currentPageNo:1 })
      this.mainPage = 3
    },
    frozenUsersFun(params){
      frozenUsers(params).then(res1=>{
        let res = res1.data.data
        let userList =res.userList
        let pageInfo =res.pageInfo
        this.tabIndex2Data.list = userList
        this.tabIndex2Data.pageShow = true
        this.tabIndex2Data.pageInfo = pageInfo
        console.log('his.tabIndex2Data=', this.tabIndex2Data)

      }).catch(err=>{
        console.log('err=', err)
      })
    },
    soonFrozenUsersFun(params){
      this.tabIndex2Data.params = params
      soonFrozenUsers(params).then(res1=>{
        let res = res1.data.data
        let userList =res.userList
        let pageInfo =res.pageInfo
        this.tabIndex2Data.list = userList
        this.tabIndex2Data.pageShow = true
        this.tabIndex2Data.pageInfo = pageInfo
        console.log('his.tabIndex2Data=', this.tabIndex2Data)


      }).catch(err=>{
        console.log('err=', err)
      })
    },
    checkAllBtn(){
      let list=[]
      this.sonOrgs.forEach(org=>{
        list.push(org.id)
      })
      this.search.orgs = list
    },
    forLeaveOk(){
      let params = {
        userId: this.forLeaveData.editObj.userID,
        memo: this.forLeaveData.editObj.reason
      }
      forLeaving(params).then(res1=>{
        let res = res1.data
        let success = res.success
        if(success === 1){
          this.$message.success(res.data)
          this.getDutyList()
        }else if(success == 0){
          this.$message.error(res.errorMsg)
        }
        this.forLeaveData.visible = false


      }).catch(err=>{
        console.log('err=', err)
      })
    // ./general/forLeaving.do
    },
    forLeaveBtn(userItem){
      this.forLeaveData.visible = true
      this.forLeaveData.editObj = userItem
      this.forLeaveData.reason = ''

    },
    hideForLeave(){
      this.forLeaveData.visible = false
    },
    handleRecord(){
      let params = {
        passiveUserId: this.joinStaffData.oldInfo.userID ,
        pageSize: 10,
        currentPageNo: 1
      }
      this.baseLog.ttl = '档案管理修改记录'
      this.baseLog.visible = true
      this.getUserBasicInfoHistoryFun(params)
    },
    handleUpdate(userItem){
      var userType = chargeRole("超管")
      if(userType){
        this.$message.error('您没有此权限！')
        return false
      }
      this.editStaffData.visible = true
      this.joinStaffData.type = 'update'
      this.joinStaffData.ttl = '档案管理'
      this.joinStaffData.selectedDepars = []
      this.joinStaffData.departNameVisible = false
      let params = { userId: userItem.userID }
      let params2 = { passiveUserId: userItem.userID }
      this.getSelectFun(params, params2)

    },
    getUserArchivesFun(params){
      getUserArchives(params).then(res1=>{
        let res = res1.data
        let data = res.data
        data.onDutyDate = new Date(data.onDutyDate ).format('yyyy/MM/dd')
        data.ordinaryEmployees = String(data.ordinaryEmployees)
        this.joinStaffData.oldInfo = data
        this.joinStaffData.userName = data.userName
        this.joinStaffData.mobile = data.mobile
        this.joinStaffData.pid = Number(data.leader) || ''
        this.joinStaffData.leaderName = data.leaderName
        this.joinStaffData.postID = Number(data.postID) || ''
        this.joinStaffData.ordinaryEmployees = data.ordinaryEmployees
        this.joinStaffData.managerCode = data.managerCode
        this.joinStaffData.managerCodeEditable = false
        if(data.roleCode === 'super' || data.roleCode === 'smallSuper'){
          this.joinStaffData.managerCode = '---'
          this.joinStaffData.managerCodeEditable = true
        }
        this.joinStaffData.departName = data.departName
        this.joinStaffData.department = data.department || ''
        this.joinStaffData.emergencyName = data.emergencyName
        this.joinStaffData.emergencyContact = data.emergencyContact
        this.joinStaffData.date1 = data.onDutyDate
        // 测试用的
        // this.joinStaffData.oldInfo.activationButton = true

      }).catch(err=>{
        console.log('err=', err)

      })
    },
    addMoreContact(){
      this.uContactMethodVisible = true

    },
    addMoreChange(){
      this.uContactMethodVisible = false
      if(this.uContactMethod == 9){
        this.uContactMethod9Visible = true
      }else {
        this.uContactData.push({
          isOpen: true,
          type:this.uContactMethod,
          ttl: this.formatMethod(this.uContactMethod),
          value: ''
        })
      }

    },
    formatMethod(num, name){
      let str = ''
      switch (Number(num)){
        case 1: str = '其他手机'; break
        case 2: str = 'QQ'; break
        case 3: str = 'Email'; break
        case 4: str = '微信'; break
        case 5: str = '微博'; break
        case 9: str = name; break
        default:
          str = ''
      }
      return str

    },
    editBase(){
      this.employeeUpdateData.editBaseVisible = true
      this.baseData = JSON.parse(JSON.stringify(this.employeeUpdateData.user ))
      let contactList = []
      this.employeeUpdateData.userContacts.forEach(conta => {
        contactList.push({
          isOpen: conta.open,
          type: conta.type,
          ttl: this.formatMethod(conta.type, conta.name),
          value: conta.code
        })
      })
      this.uContactData = contactList

    },
    updateEmployeeBaseSure(){
      var userInfo = {
        'passiveUserId': this.baseData.userID ,
        'gender': this.baseData.gender,
        'nation': this.baseData.nation,
        'birthday': this.baseData.birthday,
        'birthplace': this.baseData.birthplace,
        'marry': this.baseData.marry,
        'politicalStatus': this.baseData.politicalStatus,
        'firstLanguage': this.baseData.firstLanguage,
        'firstForeignLevel': this.baseData.firstForeignLevel,
        'secondLanguage': this.baseData.secondLanguage,
        'secondForeignLevel': this.baseData.secondForeignLevel,
        'computerLevel': this.baseData.computerLevel,
        'otherSkills': this.baseData.otherSkills,
      }
      if(this.baseData.degree){
        userInfo.degree = this.baseData.degree
      }else{
        userInfo.degree = 0
      }
      let contacts = []
      this.uContactData.forEach(contact=>{
        contacts.push({
          'type': contact.type,
          'name': contact.ttl,
          'code': contact.value,
          'isOpen': contact.isOpen
        })
      })
      userInfo = JSON.stringify(userInfo);
      contacts = JSON.stringify(contacts);
      var params = {
        'userInfo': userInfo,
        'contacts': contacts
      };
      updateUserBasicInfo(params).then(res1 => {
        let res = res1.data
        var success = res['success'];
        if (success == 1) {
          this.$message.success('保存成功！')
          // 刷新主页面
          this.refreashPage()
          this.userInfoPreviewFun(this.baseData.userID)
          this.employeeUpdateData.editBaseVisible = false

        }else {
          this.$message.error('提交失败！')

        }

      }).catch(err => {
        console.log('err=', err)
      })

    },
    refreashPage(){
      let cur = this.tabIndex0Data.pageInfo.currentPageNo
      let data = this.tabIndex0Data.params
      data.currentPageNo = cur
      this.tabIndex0Data.params = data
      this.getScreenListFun(data)
    },
    addNewLable(){
      if(this.uContactMethod9Name.length === 0){
        this.$message.error('请输入标签名称！')
        return false
      }
      this.uContactMethod9Visible = false
      this.uContactData.push({
        isOpen: true,
        type:this.uContactMethod,
        ttl: this.uContactMethod9Name,
        value: ''
      })
    },
    hideUContactMethod9Visible(){
      this.uContactMethod9Visible = false
    },
    hideBaseEdit(){
      this.employeeUpdateData.editBaseVisible = false
    },
    editBaseLog(){
      let params = {
        passiveUserId: this.employeeUpdateData.user.userID ,
        pageSize: 10,
        currentPageNo: 1
      }
      this.baseLog.ttl = '基本信息修改记录'
      this.baseLog.visible = true
      this.getUserBasicInfoHistoryFun(params)
    },
    editEduLog(edu){
      let params = {
        id: edu.id ,
        pageSize: 10,
        currentPageNo: 1
      }
      this.baseLog.ttl = '教育经历修改记录'
      this.baseLog.visible = true
      this.personalEducationHistoriesFun(params)
    },
    personalEducationHistoriesFun(params){
      this.baseLog.pageShow = false
      personalEducationHistories(params).then(res1=>{
        let res = res1.data
        let success = res['success'];
        let data = res.data
        if(success == 1){
          var record = data['personnelEducationHistoryList'];
          var info = data['personalEducation'];
          var pageInfo = data['pageInfo'];
          this.baseLog.type = 'edu'
          this.baseLog.number = data.number
          this.baseLog.list = record
          this.baseLog.pageInfo = pageInfo
          this.baseLog.updateName = info.updateName
          this.baseLog.updateTime = info.updateDate
          this.baseLog.pageShow = true
          this.baseLog.info = info

        }else{
          this.$message.error('获取失败！')
          this.baseLog.visible = false
        }


      }).catch(err=>{
        console.log('err=', err)
      })

    },
    recordJobDetails(logItem){
      getPersonnelOccupationHistory({ id: logItem.id }).then(res1=>{
        let res = res1.data
        let data = res.data
        data.beginTime = new Date(data.beginTime).format('yyyy/MM')
        data.endTime = new Date(data.endTime).format('yyyy/MM')
        this.jobLogDetails.visible = true
        this.jobLogDetails.info = data
      }).catch(err=>{
        console.log('err=', err)
      })
    },
    hideJobLogDetails(){
      this.jobLogDetails.visible = false
    },
    hideIDCardInfo(){
      this.IDCardLogData.visible = false
    },
    recordIDCardDetails(logItem){
      getUserIdCard({ id: logItem.id }).then(res1 => {
        let res = res1.data
        let success = res['success'];
        if (success == 1){
          this.IDCardLogData.visible = true
          this.IDCardLogData.user = res.data



        }else{
          this.$message.error('获取失败！')
        }
      }).catch(err=>{
        console.log()
      })

    },
    recordDetails(logItem){
      getUserHistory({ id: logItem.id }).then(res1 => {
        let res = res1.data
        let success = res['success'];
        if (success == 1){
          let user =  res['data']['user'];
          this.baseLogDetails.user = user ;
          var userContacts = res['data']['userContacts'];
          this.baseLogDetails.visible = true
          if(this.baseLog.ttl == '基本信息修改记录'){
            this.baseLogDetails.ttl = '基本信息'
          }else if(this.baseLog.ttl == '档案管理修改记录'){
            this.baseLogDetails.ttl = '档案管理'
          }
          let contactList = []
          userContacts.forEach(conta =>{
            contactList.push({
              isOpen: conta.open,
              type: conta.type,
              ttl: this.formatMethod(conta.type, conta.name),
              value: conta.code
            })
          })
          this.baseLogDetails.userContacts = contactList

        }else{
          this.$message.error('获取失败！')
        }
      }).catch(err => {
        console.log('err=', err)
      })
    },
    hideEduLogDetails(){
      this.eduLogDetails.visible = false
    },
    recordEduDetails(logItem){
      getPersonalEducationHistory({ id: logItem.id }).then(res1 =>{
        let res = res1.data
        this.eduLogDetails.visible = true
        let data = res.data
        data.beginTime = new Date(data.beginTime).format('yyyy/MM')
        data.endTime = new Date(data.endTime).format('yyyy/MM')
        this.eduLogDetails.info = data

      }).catch(err => {
        console.log('err=', err)
      })
    },
    hideBaseLogDetails(){
      this.baseLogDetails.visible = false
    },
    hideLog(){
      this.baseLog.visible = false
    },
    hideEditEdu(){
      this.editEduData.visible = false
    },
    editEduOK(){
      let nullNum = 0
      if(this.editEduData.beginTime.length === 0){ nullNum++;  }
      if(this.editEduData.endTime.length === 0){ nullNum++;  }
      if(this.editEduData.collegeName.length === 0){ nullNum++;  }
      if(this.editEduData.departmentName.length === 0){ nullNum++;  }
      if(this.editEduData.major.length === 0){ nullNum++;  }
      if(this.editEduData.degreeDesc.length === 0){ nullNum++;  }
      if(this.editEduData.majorDesc.length === 0){ nullNum++;  }
      if(this.editEduData.memo.length === 0){ nullNum++;  }
      if(nullNum === 8){
        this.$message.error('请至少输入一项内容！')
        return false
      }
      console.log('editEduOK, editEduData=', this.editEduData)
      let userId = this.editUserObj.userID
      let params = { passiveUserId: userId }
      params.beginTime = this.editEduData.beginTime
      params.endTime = this.editEduData.endTime
      params.collegeName = this.editEduData.collegeName
      params.departmentName = this.editEduData.departmentName
      params.major = this.editEduData.major
      params.degreeDesc = this.editEduData.degreeDesc
      params.majorDesc = this.editEduData.majorDesc
      params.memo = this.editEduData.memo

      if(this.editEduData.type === 'add'){
        addPersonalEducation(params).then(res1 => {
          let res = res1.data
          var success = res['success'];
          if (success == 1) {
            this.editEduData.visible = false
            this.userInfoPreviewFun(userId)

          } else{
            layer.msg('提交失败！');
          }

        }).catch(err => {
          console.log('err=', err)
        })
      }
      else if(this.editEduData.type === 'update'){
        params.id = this.editEduData.editEduObj.id
        updatePersonalEducation(params).then(res1=> {
          let res = res1.data
          var success = res['success'];
          if (success == '1' || success == 1) {
            this.editEduData.visible = false
            this.userInfoPreviewFun(userId)
          } else{
            this.$message.error('提交失败！')
          }

        }).catch(err=> {
          console.log('err=', err)
        })
      }

    },
    baseLogPageClick(pageItem){
      let data = this.baseLog.params
      data.pageNumber = pageItem.page
      switch (this.baseLog.type){
        case "base":
          this.getUserBasicInfoHistoryFun(data)
          break;
        case "edu":
          this.personalEducationHistoriesFun(data)
            break;
        case "job":
          this.personnelOccupationHistoriesFun(data)
          break
        case "IDCard":
          this.getUserIdCardHistoriesFun(data)
          break
      }
    },
    getUserBasicInfoHistoryFun(params){
      this.baseLog.params = params
      getUserBasicInfoHistory(params).then(res1 => {
        let res = res1.data.data
        console.log('res =', res)
        this.baseLog.type = 'base'
        this.baseLog.number = res.number
        this.baseLog.list = res.userRoleHistoryList
        this.baseLog.pageInfo = res.pageInfo
        this.baseLog.updateName = res.updateName
        this.baseLog.updateTime = res.updateTime
        this.baseLog.pageShow = true

      }).catch(err => {
        console.log('err=', err)
      })
    },
    addEdu(){
      this.editEduData.visible = true
      this.editEduData.type = 'add'
      this.editEduData.beginTime = ''
      this.editEduData.endTime = ''
      this.editEduData.collegeName = ''
      this.editEduData.departmentName = ''
      this.editEduData.major = ''
      this.editEduData.degreeDesc = ''
      this.editEduData.majorDesc = ''
      this.editEduData.memo = ''
    },
    editEdu(edu){
      edu.beginTime = new Date(edu.beginTime).format('yyyy/MM')
      edu.endTime = new Date(edu.endTime).format('yyyy/MM')
      console.log('editEdu-edu=', edu)
      this.editEduData.visible = true
      this.editEduData.type = 'update'
      this.editEduData.editEduObj = edu
      this.editEduData.beginTime = edu.beginTime
      this.editEduData.endTime = edu.endTime
      this.editEduData.collegeName = edu.collegeName
      this.editEduData.departmentName = edu.departmentName
      this.editEduData.major = edu.major
      this.editEduData.degreeDesc = edu.degreeDesc
      this.editEduData.majorDesc = edu.majorDesc
      this.editEduData.memo = edu.memo


    },
    delEdu(edu){
      this.delData.visible = true
      this.delData.editObj = edu
      this.delData.type = 'edu'
    },
    delEduOK(){
      markDeleteEducation({ id: this.delData.editObj.id  }).then(res1=>{
        let res = res1.data
        var success = res['success'];
        if (success == 1) {
          this.delData.visible = false
          this.userInfoPreviewFun(this.editUserObj.userID)
        } else{
          this.$message.error('删除失败！')
        }
      }).catch(err=>{
        console.log('err=', err)
      })
    },
    delWorkOK(){
      markDeleteOccupation({ id: this.delData.editObj.id  }).then(res1=>{
        let res = res1.data
        var success = res['success'];
        if (success == 1) {
          this.delData.visible = false
          this.userInfoPreviewFun(this.editUserObj.userID)
        } else{
          this.$message.error('删除失败！')
        }
      }).catch(err=>{
        console.log('err=', err)
      })

    },
    hideDel(){
      this.delData.visible = false
    },
    hideEditWork(){
      this.editWorkData.visible = false
    },
    addJob(){
      this.editWorkData.visible = true
      this.editWorkData.type = 'add'
      this.editWorkData.beginTime = ''
      this.editWorkData.endTime = ''
      this.editWorkData.corpName = ''
      this.editWorkData.corpSize = ''
      this.editWorkData.corpNature = ''
      this.editWorkData.corpDepartment = ''
      this.editWorkData.post = ''
      this.editWorkData.jobDesc = ''
      this.editWorkData.memo = ''
    },
    editJob(job){
      console.log('job=', job)
      this.editWorkData.visible = true
      this.editWorkData.type = 'update'
      job.beginTime = new Date(job.beginTime).format('yyyy/MM')
      job.endTime = new Date(job.endTime).format('yyyy/MM')
      this.editWorkData.editObj = job
      this.editWorkData.beginTime = job.beginTime
      this.editWorkData.endTime = job.endTime
      this.editWorkData.corpName = job.corpName
      this.editWorkData.corpSize = job.corpSize
      this.editWorkData.corpNature = job.corpNature
      this.editWorkData.corpDepartment = job.corpDepartment
      this.editWorkData.post = job.post
      this.editWorkData.jobDesc = job.jobDesc
      this.editWorkData.memo = job.memo

    },
    editJobOK(){
      let userId = this.editUserObj.userID
      let params = { passiveUserId: userId }
      let nullNum = 0
      if(this.editWorkData.beginTime.length === 0){ nullNum++;  }
      if(this.editWorkData.endTime.length === 0){ nullNum++;  }
      if(this.editWorkData.corpName.length === 0){ nullNum++;  }
      if(this.editWorkData.corpSize.length === 0){ nullNum++;  }
      if(this.editWorkData.corpNature.length === 0){ nullNum++;  }
      if(this.editWorkData.corpDepartment.length === 0){ nullNum++;  }
      if(this.editWorkData.post.length === 0){ nullNum++;  }
      if(this.editWorkData.jobDesc.length === 0){ nullNum++;  }
      if(this.editWorkData.memo.length === 0){ nullNum++;  }

      if(nullNum === 9){
        this.$message.error('请至少输入一项内容！')
        return false
      }
      params.beginTime = this.editWorkData.beginTime
      params.endTime = this.editWorkData.endTime
      params.corpName = this.editWorkData.corpName
      params.corpSize = this.editWorkData.corpSize
      params.corpNature = this.editWorkData.corpNature
      params.corpDepartment = this.editWorkData.corpDepartment
      params.post = this.editWorkData.post
      params.jobDesc = this.editWorkData.jobDesc
      params.memo = this.editWorkData.memo

      if(this.editWorkData.type === 'add') {
        addPersonnelOccupation(params).then(res1 => {
          let res = res1.data
          var success = res['success'];
          if ( success == 1) {
            this.editWorkData.visible = false
            this.userInfoPreviewFun(userId)
          } else{
            this.$message.error('获取失败！')
          }
        }).catch(err => {
          console.log('err=', err)
        })

      }else if(this.editWorkData.type === 'update'){
        params.id = this.editWorkData.editObj.id
        updatePersonnelOccupation(params).then(res1 => {
          let res = res1.data
          var success = res['success'];
          if ( success == 1) {
            this.editWorkData.visible = false
            this.userInfoPreviewFun(userId)
          } else{
            this.$message.error('获取失败！')
          }
        }).catch(err => {
          console.log('err=', err)
        })

      }
    },
    editJobLog(job){
      let params = {
        id: job.id ,
        pageSize: 10,
        currentPageNo: 1
      }
      this.baseLog.ttl = '工作经历修改记录'
      this.baseLog.visible = true
      this.baseLog.type = 'job'
      this.personnelOccupationHistoriesFun(params)
    },
    personnelOccupationHistoriesFun(params){
      personnelOccupationHistories(params).then(res1=> {
        let res = res1.data
        let data = res.data
        var record = data['personnelOccupationHistoryList'];
        var info = data['personnelOccupation'];
        var pageInfo = data['pageInfo'];
        this.baseLog.type = 'job'
        this.baseLog.number = data.number
        this.baseLog.list = record
        this.baseLog.pageInfo = pageInfo
        this.baseLog.updateName = info.updateName
        this.baseLog.updateTime = info.updateDate
        this.baseLog.pageShow = true
        this.baseLog.info = info

      }).catch(err=>{
        console.log('err=', err)
      })
    },
    delJob(job){
      this.delData.visible = true
      this.delData.editObj = job
      this.delData.type = 'job'
    },
    editIDCard(){
      this.IDCardData.visible = true
      this.IDCardData.user = JSON.parse(JSON.stringify(this.employeeUpdateData.user ))
    },
    editIDCardLog(){
      let params = {
        passiveUserId: this.employeeUpdateData.user.userID ,
      }
      this.baseLog.ttl = '身份证修改记录'
      this.baseLog.visible = true
      this.getUserIdCardHistoriesFun(params)

    },
    getUserIdCardHistoriesFun(params){
      this.baseLog.params = params
      getUserIdCardHistories(params).then(res1=>{
        let res = res1.data.data
        console.log('res =', res)
        this.baseLog.type = 'IDCard'
        this.baseLog.number = res.number
        this.baseLog.list = res.userIdcardHistoryList
        this.baseLog.updateName = res.updateName
        this.baseLog.updateTime = res.updateDate

      }).catch(err=>{
        console.log('err=', err)
      })
    },
    editIDCardOK(){
      let userId = this.employeeUpdateData.user.userID
      let params = {
        passiveUserId: userId,
        idCard: this.IDCardData.user.idCard ,
        address: this.IDCardData.user.address ,
      }
      updateUserIdCard(params).then(res1=>{
        let res = res1.data
        var success = res['success'];
        if (success == 1) {
          this.IDCardData.visible = false
          this.userInfoPreviewFun(userId)
        } else{
          this.$message.error('提交失败！')
        }
      }).catch(err=>{
        console.log('err=', err)
      })

    },
    hideIDCard(){
      this.IDCardData.visible = false
    },
    hideEmployeeUpdate(){
      this.employeeUpdateData.visible = false
    },
    updateUserBtn(){
      // 修改用户信息
      this.employeeUpdateData.ttl = this.userInfo.user.userName + '信息'
      this.employeeUpdateData.visible = true
    },
    hidePreView(){
      this.preViewVisible = false

    },
    formatGender(sex){
      let str = ''
      if(sex){
        switch (Number(sex)){
          case 1: str = '男'; break;
          case 0: str = '女'; break;
          default: str = '';
        }
      }

      return str;
    },
    formatMarry(marry){
      let str = ''
      switch (Number(marry)){
        case 1: str = '未婚'; break;
        case 0: str = '已婚'; break;
        default: str = '';
      }
      return str;
    },
    formatDegree(degree){
      let str = ''
      switch (Number(degree)){
        case 1: str = '研究生'; break;
        case 2: str = '本科'; break;
        case 3: str = '大专'; break;
        case 4: str = '中专或高中'; break;
        case 5: str = '其他'; break;
        default: str = '';
      }
      return str;
    },
    userInfoPreviewFun(userID){
      userInfoPreview({'passiveUserId': userID}).then(res1 => {
        const res = res1.data
        const success =  res['success'];
        if(success == '1'){
          var userInfo = res.data.user;
          this.checkState = userInfo.submitState;
          this.preViewVisible = true
          this.userInfo = res.data
          // 下面集中进行 空 格式化
          this.userInfo.user.nation = handleNull(this.userInfo.user.nation )
          this.userInfo.user.birthday = handleNull(this.userInfo.user.birthday )
          this.userInfo.user.birthplace = handleNull(this.userInfo.user.birthplace )
          this.userInfo.user.politicalStatus = handleNull(this.userInfo.user.politicalStatus )
          this.userInfo.user.marry = handleNull(this.userInfo.user.marry )
          this.userInfo.user.degree = handleNull(this.userInfo.user.degree || '' )
          this.userInfo.user.mobile = handleNull(this.userInfo.user.mobile )
          this.userInfo.user.qq = handleNull(this.userInfo.user.qq )
          this.userInfo.user.email = handleNull(this.userInfo.user.email )
          this.userInfo.user.firstLanguage = handleNull(this.userInfo.user.firstLanguage )
          this.userInfo.user.firstForeignLevel = handleNull(this.userInfo.user.firstForeignLevel )
          this.userInfo.user.secondLanguage = handleNull(this.userInfo.user.secondLanguage )
          this.userInfo.user.secondForeignLevel = handleNull(this.userInfo.user.secondForeignLevel )
          this.userInfo.user.computerLevel = handleNull(this.userInfo.user.computerLevel )
          this.userInfo.user.otherSkills = handleNull(this.userInfo.user.otherSkills )

          let webRoot = auth.webRoot
          let imgPath = this.userInfo.user.imgPath
          if(imgPath){
            this.userInfo.user.imgPath = webRoot + imgPath
          }else{
            this.userInfo.user.imgPath = imgError
          }

          this.employeeUpdateData.eduList = this.userInfo.personalEducations
          this.employeeUpdateData.jobList = this.userInfo.personnelOccupations
          this.employeeUpdateData.user = this.userInfo.user
          this.employeeUpdateData.userContacts = this.userInfo.userContacts


        }else{
          this.$message.error('获取失败！')
        }
      }).catch(err=> {
        console.log('err=', err)
      })
    },
    employeeInforView(user){
      this.editUserObj = user
      let userID = user.userID
     this.userInfoPreviewFun(userID)


    },
    hideJoinStaff(){
      this.joinStaffData.visible = false
    },
    hideEditStaff(){
      this.editStaffData.visible = false
    },
    hideImportNotCompleted(){
      this.importNotCompletedVisible = false
    },
    joinStaffOk(){
      if(this.joinStaffData.type === 'update'){
        var params = {
          userID: this.joinStaffData.oldInfo.userID,
          userName: this.joinStaffData.userName,
          mobile: this.joinStaffData.mobile ,
          date1:  this.joinStaffData.date1 ,
          department: this.joinStaffData.department ,
          postID: this.joinStaffData.postID,
          postName: this.joinStaffData.oldInfo.postName  ,
          ordinaryEmployees: this.joinStaffData.ordinaryEmployees,
          managerCode: this.joinStaffData.managerCode,
          leader: this.joinStaffData.pid,
        }
        if(params.managerCode === '---'){
          params.managerCode = this.joinStaffData.oldInfo.managerCode
        }
        if(this.joinStaffData.selectedDepars.length > 0){
          let seleD = this.joinStaffData.selectedDepars[this.joinStaffData.selectedDepars.length -1]
          params.department = seleD.departItem.id
        }
        if(params.postID === ''){
          params.postName = ''
        }else {
          this.joinStaffData.postOptions.forEach(post => {
            if(post.id == this.joinStaffData.postID){
              this.joinStaffData.postName = post.name
            }
          })
        }
        updateEssentialInformation(params).then(res1=>{
          let res = res1.data
          if (res == '1') {
            this.getDutyList()
            this.editStaffData.visible = false
          } else if (res == 3){
            this.$message.error('姓名超出或者手机号已存在！')
          } else {
            this.$message.error('提交失败！')
          }
        }).catch(err=>{
          console.log('err=', err)
        })

      }else if(this.joinStaffData.type === 'add') {
        this.$refs['joinStaffRef'].validate((valid) => {
          if (valid) {
            let that = this
            let params = {"mobile": this.joinStaffData.mobile}
            mobileCheckRepeat(params).then(res1 => {
              let res = res1.data
              let data = res.data
              let status = data["state"]; // state 1-查重通过  0-与在职人员相同  2- 与离职人员相同 3- 与历史记录相同
              if (status === 1) {
                this.newStuff()
              } else if (status === 2 || status === 3) {
                this.$confirm(
                    data.cont,
                    '！提示',
                    {
                      distinguishCancelAndClose: true,
                      confirmButtonText: '确定',
                      cancelButtonText: '取消',
                    }
                ).then(() => {
                  that.newStuff();
                })
                    .catch(() => {
                      that.hideJoinStaff()
                    })


              } else if (status === 0) {
                // 您录入的手机号与公司老刘手机号相同。请确认
                this.$message.error(data.cont)

              }


            }).catch(err => {
              console.log('err=', err)
            })
          }
        })
      }
    },
    newStuff(){
      let data = {
        userName: this.joinStaffData.userName,
        mobile: this.joinStaffData.mobile,
        date1: this.joinStaffData.date1,
        postID: this.joinStaffData.postID,
        ordinaryEmployees: this.joinStaffData.ordinaryEmployees,
        managerCode: this.joinStaffData.managerCode,
        pid: this.joinStaffData.pid,

      }
      if (this.contractMust) {
        if (this.joinStaffData.sn === '') {
          this.$message({
            type: 'error',
            message: '请输入合同编号！'
          })
          return false
        }
        if (!this.joinStaffData.validTime || this.joinStaffData.validTime.length === 0 ) {
          this.$message({
            type: 'error',
            message: '请选择合同的有效期'
          })
          return false
        }
        data.sn = this.joinStaffData.sn
        data.contractSignTime= this.joinStaffData.signTime
        data.remark= this.joinStaffData.remark
        data.contractStartTime= this.joinStaffData.validTime?.[0]
        data.contractEndTime= this.joinStaffData.validTime?.[1]
        let imgs = this.joinStaffData.imgList.map(item => {
          let newItem = {
            uplaodPath: item.normalPath,
            type: 1,
            title: item.name,
            operation: item.operation
          }
          if (item.id) newItem.id = item.id
          return newItem
        })
        data.contractBaseImages = JSON.stringify(imgs)
         if (this.joinStaffData.fileList.length > 0) {
          data.filePath = this.joinStaffData.fileList[0].normalPath
          data.fileName = this.joinStaffData.fileList[0].name
        }
      }

      if(data.postID === ''){
        data.postName = ''
      }else {
        this.joinStaffData.postOptions.forEach(post => {
          if(post.id == this.joinStaffData.postID){
            this.joinStaffData.postName = post.name
          }
        })
      }
      if(this.joinStaffData.selectedDepars.length > 0){
        let seleD = this.joinStaffData.selectedDepars[this.joinStaffData.selectedDepars.length -1]
        data.department = seleD.departItem.id
      }
      addUser(data).then(res1=> {
        let success = res1.data
        if (success == '1') {
          this.$message.success('操作成功')
           this.getDutyList()
          this.joinStaffData.visible = false
        }else if(success == 3){
          this.$message.error('姓名超出或者手机号已存在！')
        } else {
          this.$message.error('提交失败！')
        }
      }).catch(err=>{
        console.log('err=', err)
      })

    },
    getDeparts(){
      console.log('调了调了')
      this.joinStaffData.departNameVisible = true
    },
    getSonDepar(departItem, level){
      let newSele = []
      this.joinStaffData.selectedDepars.forEach(selectDpar => {
        if(selectDpar.level < level){
          newSele.push(selectDpar)
        }
      })
      this.joinStaffData.selectedDepars = newSele
      this.joinStaffData.selectedDepars.push({ level:level, departItem: departItem })

      let newAllDepar = []
      this.joinStaffData.allDepartmentList.forEach(depart => {
        if(depart.level <= level){
          newAllDepar.push(depart)
        }
      })
      this.joinStaffData.allDepartmentList = newAllDepar

      this.setDepartName()

      let data = { id:departItem.id  }
      checkSonDepartment(data).then(res1 => {
        const res = res1.data
        this.joinStaffData.allDepartmentList.push({
          level: level+1 , depars: res
        })
      }).catch(err => {
        console.log('err=', err)
      })
    },
    chargeActive(departItem, level){
      let same = false
      this.joinStaffData.selectedDepars.forEach(seleDepar => {
        if(seleDepar.level === level){
          if(seleDepar.departItem.id === departItem.id){
            same = true
          }
        }
      })
      return same
    },
    closeConBtn(){
      this.joinStaffData.departNameVisible = false
      this.setDepartName()
    },
    setDepartName(){
      let departNameArr = []
      this.joinStaffData.selectedDepars.forEach(seleDepar => {
        departNameArr.push(seleDepar.departItem.name)
      })
      let departNameStr = departNameArr.join('-')
      this.joinStaffData.departName = departNameStr
    },
    joinStaff(){
      this.joinBtnState = true
      this.joinStaffData.visible = true
      this.joinStaffData.type = 'add'
      this.joinStaffData.ttl = '办理入职'

      this.joinStaffData.postID = ''
      this.joinStaffData.ordinaryEmployees = '1'
      this.joinStaffData.managerCode = 'general'
      this.joinStaffData.selectedDepars = []
      this.joinStaffData.departName = ''
      this.joinStaffData.date1 = new Date().format('yyyy/MM/dd')

      // this.$refs['joinStaffRef'].resetFields()

      this.joinStaffData.userName = ''
      this.joinStaffData.mobile = ''
      this.joinStaffData.pid = ''
      this.joinStaffData.leaderName = ''
      this.joinStaffData.emergencyName = ''
      this.joinStaffData.emergencyContact = ''
      this.joinStaffData.oldInfo = { }
      this.joinStaffData.sn = ''
      this.joinStaffData.signTime = ''
      this.joinStaffData.validTime = []
      this.joinStaffData.validStart = ''
      this.joinStaffData.validEnd = ''
      this.joinStaffData.remark = ''


      this.joinStaffData.imgList = []
      this.joinStaffData.fileList = []

      this.getSelectFun()

    },
    getSelectFun(data, params2){
      getSelect(data).then(res1 => {
        let res = res1.data
        let departmentList = res.departmentList // 部门列表
        let manageList = res.manageList // 所属高管列表
        let postList = res.postList // 职位列表
        let userList = res.userList // // 直接上级
        this.joinStaffData.postOptions = postList
        this.joinStaffData.leaderOptions = userList
        let level1 = { level:1, depars:[] }
        departmentList.forEach(depar => {
          if(depar.level == 1){
            level1.depars.push(depar)
          }
        })
        this.joinStaffData.allDepartmentList = [level1]
        if(userList && userList.length > 0){
          if(data){

          }else{
            this.joinStaffData.pid = userList[0]['userID']
          }
        }
        // managerCode
        this.joinStaffData.managerCodeOptions = []
        manageList.forEach(mItem => {
          this.joinStaffData.managerCodeOptions.push({
            code: mItem.managerCode,
            name: this.changeWorkFuc( mItem.managerCode)
          })
        })
        this.joinStaffData.managerCode = this.joinStaffData.managerCodeOptions[0]['code']

        if(params2){
          this.getUserArchivesFun(params2)
        }

      }).catch(err => {
        console.log('err=', err)
      })
    },
    changeWorkFuc(code){
      var charact = '';
      switch (code) {
        case "general":
          charact = "不含销售工作与财会工作";
          break;
        case "finance":
          charact = "包含财务工作";
          break;
        case "sale":
          charact = "含有销售工作";
          break;
        case "accounting":
          charact = "包含会计工作";
          break;
      }
      return charact;
    },
    searchBtn(){
      console.log('调了调了')
      this.searchVisible = true

    },
    clearqueryEmployeeBtn(){
      this.search = {
        orgs: [],
        education:[],
        gender:[],
        department:[],
        post:[],
        marry:[],
        onDutyDate1:'',
        onDutyDate2:'',
        birthday1:'',
        birthday2:'',
      }
    },

    queryEmployeeBtn(){
      // 查询
      this.searchVisible = false
      console.log('search=', this.search)

      let params = {
        oids: this.search.orgs.join(','),
        isDuty: 1,
        edus: this.search.education.join(','),
        gender:this.search.gender.join(','),
        departments: this.search.department.join(','),
        posts: this.search.post.join(','),
        marry: this.search.marry.join(','),
        birthday1:this.search.birthday1,
        birthday2:this.search.birthday2,
        onDutyDate1: this.search.onDutyDate1,
        onDutyDate2: this.search.onDutyDate2,
        pageSize: 20 ,
        currentPageNo: 1
      }
      this.tabIndex0Data.params = params
      this.getScreenListFun(params)
    },
    tabIndex0PageClick(pageItem){
      console.log('tabIndex0PageClick')
      let data = this.tabIndex0Data.params
      data.currentPageNo = pageItem.page
      this.tabIndex0Data.params = data
      this.getScreenListFun(data)
    },
    tabIndex2PageClick(pageItem){
      console.log('tabIndex0PageClick')
      let data = this.tabIndex2Data.params
      data.currentPageNo = pageItem.page
      this.tabIndex2Data.params = data
      if(this.tabIndex2 === 1){ // 已冻结的账号
        this.frozenUsersFun(data)
      }else{ // 即将冻结的账号
        this.soonFrozenUsersFun(data)
      }
    },
    chargeDegree(degreeCode) {
      var degree = '';
      switch (degreeCode){
        case 1:
          degree =  "研究生";
          break;
        case 2:
          degree =  "本科";
          break;
        case 3:
          degree =  "大专";
          break;
        case 4:
          degree =  "中专或高中";
          break;
        case 5:
          degree =  "其它";
          break;
      }
      return degree
    },
    chargeSex(sex) {
      console.log('性别=', sex)
      switch(sex){
        case "":
          return ''
        case "1":
          return "男";
        case "0":
          return "女";
        default:
          return '';
      }
    },
    mountedFun(){
      //获取在职列表
      this.getDutyList()
      this.getAllManageAndPostsFun()
      this.getPosts()
      this.getOrgSonOrgsFun()
      this.getAuthTimeFun()
      let rootPath = localStorage.getItem('rootPath')
      this.rootPath = JSON.parse(rootPath)
    },
    getOrgSonOrgsFun(){
      let useInfo = auth.getOrg();
      getOrgSonOrgs().then(res1=>{
        let res = res1.data
        let list = res.data
        if (list && list.length > 0) {
          if (list.length > 1 || list.length === 1 && list[0].id !== useInfo.id) {
            this.sonOrgs = list
          }
        }
        if (useInfo.orgType === 1 && list.length > 1){//orgType 是4 就是子机构， 是1 就是总机构
          this.matchType = true;
        } else {
          this.matchType = false;
        }
        console.log('子机构 this.sonOrgs =',this.sonOrgs )
        console.log('子机构 matchType =',this.matchType )


      }).catch(err=>{
        console.log('err=', err)
      })
    },
    getAllManageAndPostsFun(){
      getAllManageAndPosts({ orgType:2 }).then(res1=>{
        let res = res1.data
        this.departments = res.organizations || []

      }).catch(err=>{
        console.log('err=',err)
      })
    },
    getDepartments(){
      let data = {
        orgId: 4239,
        orgType: 2 ,
        userId: 8931
      }
      getDepartMentList(data).then(res1 => {
        let res = res1.data
        this.departments = res.organizations

      }).catch(err => {
        console.log('err=', err)
      })
    },
    getPosts(){
      getPostList().then(res1 => {
        let res = res1.data
        this.posts = res.data

      }).catch(err => {
        console.log('err=', err)
      })
    },
    getDutyList(){
      let params = {
        isDuty: 1,
        pageSize: 20 ,
        currentPageNo: 1
      }
      this.tabIndex0Data.params = params
      this.getScreenListFun(params)
    },
    getScreenListFun(data){
      getScreenList(data).then(res1 => {
        let data = res1.data.data
        let users = data["userList"] || [];
        let pageInfo = data["pageInfo"];
        let totalPage = pageInfo["totalPage"];//总页数
        let cur = data["pageNumber"];//当前页
        if(data.isDuty == 1){
          // 将之前冻结的账号记录的sendCodeTime信息删掉
          let sendCodeTimeList = JSON.parse(localStorage.getItem('sendCodeTimeList') || '[]')
          let oid = this.activeUserData.editUser.oid
          let newSendCodeTimeList = []
          sendCodeTimeList.forEach(timeInfo=>{
            if(timeInfo.user.oid == oid){
              users.forEach(u=>{
                if(u.userID == timeInfo.user.userID){
                  // 相同的 就不要了，因为已经解冻了
                }else{
                  newSendCodeTimeList.push(timeInfo)
                }
              })
            }else{
              newSendCodeTimeList.push(timeInfo)
            }
          })
          localStorage.setItem('sendCodeTimeList', JSON.stringify(newSendCodeTimeList))
        }

        this.tabIndex0Data.list = users
        this.tabIndex0Data.pageInfo = { 'currentPageNo': cur, 'pageSize': 20 , 'totalPage': totalPage }

      }).catch(err=> {
        console.log('err=', err)
      })
    },
    changeTab(tabIndex) {
      if(this.tabIndex != tabIndex) {
        this.tabIndex = tabIndex
        if(tabIndex === 0){ // 在职列表
          this.getDutyList()

        } else { // 离职列表
          let params = {
            isDuty: 2,
            // edus: '', gender:'', departments:'', posts:'',
            // marry:'', birthday1:'', birthday2:'', onDutyDate1:'', onDutyDate2:'',
            pageSize: 20 , currentPageNo: 1
          }
          this.tabIndex0Data.params = params
          this.getScreenListFun(params)
        }
      }
    },
    changeTab2(tabIndex) {
      if(this.tabIndex2 != tabIndex) {
        this.tabIndex2 = tabIndex
        if(tabIndex === 0){ // 即将冻结的账号
          this.soonFrozenUsersFun({ pageSize: 20, currentPageNo:1 })

        } else { // 已冻结的账号
          let params = {
            pageSize: 20 , currentPageNo: 1
          }
          this.frozenUsersFun(params)
        }
      }
    },
    getList(){
      let params = { 'currentPageNo': 1 , 'pageSize': 20 , 'totalPage': 15  }
      this.getDemoListFun(params)
    },
    leadingStart(){
      // 批量导入
      var userType = chargeRole("超管")
      if(userType){
        this.$message.error('您没有此权限！')
        return false
      }
      unfinishedImportUser().then(res => {
        let data = res.data
        var userList = data.userImportList;
        if (userList.length > 0) {
          this.getImportUserList(data);
          this.importNotCompletedVisible = true

        } else {
          this.leadingVisible = true
          this.uploadAreaData = {  uploadBtn1_txt: '浏览', filePath: '', originalFilename: '' }
        }

      }).catch(err => {
        console.log('err=', err)
      })
    },
    importNotCompletedOK(){
      if(this.importNotCompletedContrl === 1){
        this.mainPage = 2
        this.importNotCompletedVisible = false
      }else{ //放弃
        this.turnCancel(1)
      }
    },
    leadingHide(){
      this.leadingVisible = false
    },
    handleSuccess(file, files, ofile, totalFiles){
      this.uploadAreaData.filePath = file.filename
      this.uploadAreaData.originalFilename = file.originalFilename
    },
    sysUseImportOk(){
      if(this.uploadAreaData.filePath){
        let params = {
          filePath: this.uploadAreaData.filePath
        }
        newImportUser(params).then(res => {
          let data = res.data
          let status = data.status
          this.leadingVisible = false
          if(status === 1){
            let userList = data.trueUserList;
            let falseList = data.falseUserList;
            if (falseList && falseList.length > 0) {
              this.mainPage = 1
              this.importNoSaveData.initAll = data.importSum
              this.importNoSaveData.initWrong = data.falseImportSum
              this.importNoSaveData.falseList = falseList
              this.importNoSaveData.userList = userList

            }else{
              let importList = [];
              for (var b=0; b<userList.length;b++) {
                var item = {
                  "userName": userList[b].userName,
                  "mobile": userList[b].mobile
                }
                importList.push(item);
              }
              var json = {
                usersList: JSON.stringify(importList),
                importSum: data.importSum
              }
              this.saveImportList(json);
            }

          }else {
            this.importFalseVisible = true
          }


        }).catch(err => {
          console.log('err=', err)
        })

      }else{
        this.$message.error('您需选择一个文件后才能“导入”！')
      }
    },
    clearNoSave(){
      this.clearUserData.type = 1
      this.clearUserData.visible = true
    },
    stepNext(){
      let list = []
      this.importNoSaveData.falseList.forEach(staf => {
        list.push({ "userName": staf.userName ,"mobile": staf.mobile })
      })
      this.nextStepData.visible = true
      let param = {
        "usersList": JSON.stringify(list),
        "importSum": list.length
      }
      allImportUserEnter(param).then(res => {
        let data = res.data
        let noSaveSum = data.falseImportSum;
        this.nextStepData.noSaveSum = noSaveSum
      }).catch(err => {
        console.log('err=', err)
      })
    },
    hideUpdateUser(){
      this.updateUserData.visible = false
    },
    updateInport(staff){
      this.updateUserData.visible = true
      this.updateUserData.name = staff.userName
      this.updateUserData.phone = staff.mobile
      this.updateUserData.oldPhone = staff.mobile
      this.updateUserData.type = 2
      this.updateUserData.editStaff = staff
    },
    delteInport(staff, staffIndex){
      staff.delIndex = staffIndex
      this.updateUserData.editStaff = staff
      this.updateUserData.delVisible = true
      this.updateUserData.type = 2
    },
    initUpdate(falseStaff){
      this.updateUserData.visible = true
      this.updateUserData.name = falseStaff.userName
      this.updateUserData.phone = falseStaff.mobile
      this.updateUserData.oldPhone = falseStaff.mobile
      this.updateUserData.type = 1
      this.updateUserData.editStaff = falseStaff
    },
    initDel(falseStaff, falseIndex){
      falseStaff.delIndex = falseIndex
      this.updateUserData.editStaff = falseStaff
      this.updateUserData.delVisible = true
      this.updateUserData.type = 1
    },
    delUserOK(){
      this.updateUserData.delVisible = false
      if(this.updateUserData.type === 1){
        this.importNoSaveData.falseList.splice(this.updateUserData.editStaff.delIndex,1)
        this.importNoSaveData.initWrong = this.importNoSaveData.falseList.length

      }else if(this.updateUserData.type === 2){
        let editStaff = this.updateUserData.editStaff
        console.log('editStaff=', editStaff)
        let params = { id: editStaff.id  }
        console.log('params=', params)
        deleteImportUser(params).then(res1=>{
          let res = res1.data
          var status = res["status"];
          if (status == 1) {
            unfinishedImportUser().then(res => {
              let data = res.data
              var userList = data.userImportList;
              if (userList.length > 0) {
                this.getImportUserList(data);

              } else {
                this.leadingVisible = true
                this.uploadAreaData = {  uploadBtn1_txt: '浏览', filePath: '', originalFilename: '' }
              }

            }).catch(err => {
              console.log('err=', err)
            })
          } else {
            this.$message.error('删除失败')
          }


        }).catch(err=>{
          console.log('err=', err)
        })

      }

    },
    hideDelVisible(){
      this.updateUserData.delVisible = false
    },
    updateUserOK(){
      this.$refs['updateFrm'].validate((valid) => {
        if (valid) {
          let call = this.updateUserData.phone
          let type = this.updateUserData.type
          let old = this.updateUserData.oldPhone
          let same = 0 , name = '';
          if(type === 1){
            this.importNoSaveData.falseList.forEach(info => {
              if (call == info.mobile && old != info.mobile){
                same++;
                name = info.userName;
              }
            })

          }else if(type === 2){
            this.importingData.staffUserList.forEach(info2 => {
              let otherMb = info2.userName
              if (call == otherMb && old != otherMb){
                same++;
                name = otherMb
              }
            })
          }
          if(same >0) {
            this.iknowWord = '<p>您录入的手机号与本次导入的'+ name +'手机号相同。</p><p>请确认！</p>';
            this.iknowTipVisible = true
          }else {
            this.updateUserData.visible = false
            this.checkTipData.name = "updateImportStuff"
            this.mobileCheckImport(call, name);
          }

        } else {
          console.log('error submit!!')
          this.$message.error('请填写合法的内容！')
          return false
        }
      })

    },
    updateImportUserSure(){
      let type = this.updateUserData.type
      if(type == 1){
        this.updateUserData.editStaff.userName = this.updateUserData.name
        this.updateUserData.editStaff.mobile = this.updateUserData.phone
        this.updateUserData.visible = false
        this.checkTipData.visible = false
      }else if(type == 2){
        let params = {
          id:this.updateUserData.editStaff.id,
          mobile: this.updateUserData.phone,
          userName: this.updateUserData.name
        }
        updateImportUserMobile(params).then(res1=>{
          let res = res1.data
          var status = res["status"];
          this.updateUserData.visible = false
          if (status == 1) {
            this.updateUserData.editStaff.userName = this.updateUserData.name
            this.updateUserData.editStaff.mobile = this.updateUserData.phone
          } else if (status == -1) {
            this.$message.error("和本次导入的其他手机号重复！")
          } else {
            this.$message.error("修改失败！")
          }

        }).catch(err=>{

        })
      }
    },
    hideCheckTip(){
      this.checkTipData.visible = false
    },
    mobileCheckImport(phone, name){
      var param = {
        "mobile": phone,
        "userName": name
      };
      updateFalseUserEnter(param).then(res => {
        let data = res.data
        var status = data["status"];
        this.checkTipData.status = status

        // state 1-查重通过  0-与在职人员相同  2- 与离职人员相同 3- 与历史记录相同 4-与冻结一致
        if (status === 1) {
          this.updateImportUserSure();
        } else if (status === 2) {
          this.checkTipData.visible = true
          this.checkTipData.checkTip_ms = '<p>您录入的手机号与公司已离职者'+ data.name +'手机号相同。</p>' ;

        } else if (status === 3) {
          this.checkTipData.visible = true
          this.checkTipData.checkTip_ms = '<p>您录入的手机号与公司'+ data.name +'曾用过的手机号相同。</p>' ;

        } else if(status === 0 || status === 4) {
          this.checkTipData.visible = true
          this.checkTipData.checkTip_ms =
              '<p>您录入的手机号与公司'+ data.name +'手机号相同。</p>'+
              '<p>请确认！</p>';
        }

      }).catch(err => {
        console.log('err=', err)
      })

    },
    hideIknowTip(){
      this.iknowTipVisible = false
    },

    hideNextStep(){
      this.nextStepData.visible = false
    },
    nextStepOK(){
      this.nextStepData.visible = false
      let list = []
      this.importNoSaveData.userList.forEach(u0 => {
        list.push({ userName:u0.userName, mobile: u0.mobile })
      })
      this.importNoSaveData.falseList.forEach(u1 => {
        list.push({ userName:u1.userName, mobile: u1.mobile })
      })
      var json = {
        usersList: JSON.stringify(list),
        importSum: list.length
      }
      this.saveImportList(json);

    },
    hideImportFalse(){
      this.importFalseVisible = false
    },
    saveImportList(userData){
      saveImportUser(userData).then(res => {
        this.importFalseVisible = false
        let data = res.data
        this.getImportUserList(data);
        this.mainPage = 2

      }).catch(err=>{
        console.log('err=', err)
      })
    },
    getImportUserList(data){
      var staffUserList = data["userImportList"];
      var manageList = data["manageList"];
      var buttonState = data["buttonState"]; // 确定按钮状态1- 变亮 0- 置灰

      manageList.forEach(manager => {
        manager.value = manager.userID + '-' + manager.roleCode
        manager.showVal = this.changeWorkFuc(manager.roleCode)
      })

      staffUserList.forEach( staff => {
        staff.type = staff.ordinaryEmployees == 1 ? 1 : 0
        staff.oldType =  staff.type
        staff.leaderList = []
        staff.leader = Number(staff.leader)
        staff.oldLeader = staff.leader
        staff.managerCodeStr =  staff.managerCode
        staff.oldManagerCodeStr =  staff.managerCodeStr
        staff.showName = staff.userName + ' - - ' + staff.mobile
        staff.userList = [{ id: staff.leader, showName: staff.leaderName }]
        manageList.forEach(manager => {
          if(staff.managerCode == manager.roleCode){
            staff.managerCodeStr = manager.value
          }
        })
        this.getLeaders(staff)
      })
      this.importingData = {
        importSum:data.importSum,
        saveSum: data.tureImportSum,
        staffUserList: staffUserList,
        manageList: manageList,
        buttonState: buttonState   // 确定按钮状态1- 变亮 0- 置灰
      }
    },
    confirmOrgTip(){
      this.lastSaveData.visible = true
      this.lastSaveData.saveSum =  this.importingData.importSum
      this.lastSaveData.saveAble =  this.importingData.saveSum

    },
    lastSaveSure(){
      completeImportUser().then(res => {
        let data = res.data
        var state = data.status;
        if (state == 1) {
          this.lastSaveData.visible = false
          this.mountedFun()
          this.mainPage = 0
        } else {
          this.$message.error("保存失败！")
        }

      }).catch(err => {
        console.log('err=', err)
      })

    },
    hideLastSave(){
      this.lastSaveData.visible = false
    },
    cancelSave(){
      this.clearUserData.visible = true
      this.clearUserData.type = 2

    },
    hideClearUse(){
      this.clearUserData.visible = false
    },
    clearUser(){
      let type = this.clearUserData.type
      this.clearUserData.visible = false
      if (type === 1) {
        this.mainPage = 0
        this.defineGetList(1,20,1);
      } else {
        this.turnCancel(2)
      }
    },
    turnCancel(type){
      giveUpImportUser().then(res => {
        let data = res.data
        let state = data.status;
        if(state == '1'){
          if (type == '1') {
            this.leadingVisible = true
            this.uploadAreaData = {  uploadBtn1_txt: '浏览', filePath: '', originalFilename: '' }
          }else{
            this.mainPage = 0
            this.defineGetList(1,20,1);
          }
        }

      }).catch(err => {
        console.log('err=', err)
      })

    },
    defineGetList(pageNumber,quantum,isDuty){
      let oldParams = this.tabIndex0Data.params
      let params = {
        isDuty: isDuty, edus: oldParams.edus , gender:oldParams.gender, departments:oldParams.departments, posts:oldParams.posts,
        marry:oldParams.marry, birthday1:oldParams.birthday1, birthday2:oldParams.birthday2, onDutyDate1:oldParams.onDutyDate1, onDutyDate2:oldParams.onDutyDate2,
        pageSize:quantum , currentPageNo: pageNumber
      }
      this.tabIndex0Data.params = params
      this.getScreenListFun(params)

    },
    selectThis0(staff){
      console.log('staff.type=', staff.type)
      let data = {
        ordinaryEmployees: staff.type,
        userId: staff.id
      }
      updateImportUser(data).then(res1 =>{
        let res = res1.data
        console.log('res=', res)
        var status = res["status"];
        var buttonState = res["buttonState"]; // 确定按钮状态1- 变亮 0- 置灰
        this.importingData.buttonState = buttonState

        if (status == 1) { // 设置没问题
          this.importingData.staffUserList.forEach(staff => {
            this.getLeaders(staff)
          })

        } else if (status == 2) {
          this.$message.error('下级有员工，不能变成普通员工,设置失败！')
          staff.type = staff.oldType
        } else {
          this.$message.error('设置失败！')
          staff.type = staff.oldType
        }

      }).catch(err => {
        console.log('err=', err)
      })
    },
    selectThis2(event, staff){
      let data = {
        userId: staff.id ,
        leaderSorce:'',
        leaderId:event,
      }
      let userList =  staff.userList
      userList.forEach(uu => {
        if(uu.id == event){
          data.leaderSorce = uu.status
        }
      })
      updateImportUser(data).then(res1 =>{
        let res = res1.data
        console.log('res=', res)
        var status = res["status"];
        var buttonState = res["buttonState"]; // 确定按钮状态1- 变亮 0- 置灰
        this.importingData.buttonState = buttonState

        if (status == 1) { // 设置没问题

        } else if (status == 2) {
          this.$message.error('下级有员工，不能变成普通员工,设置失败！')
          staff.leader = staff.oldLeader
        } else {
          this.$message.error('设置失败！')
          staff.leader = staff.oldLeader
        }

      }).catch(err => {
        console.log('err=', err)
      })
    },

    selectThis3(staff){
      let data = {
        manageId: '',
        manageCode: '',
        userId: staff.id
      }
      let val = staff.managerCodeStr
      var arr = val.split('-');
      if (arr[0] != "null" && arr[0] != "") data["manageId"] = arr[0];
      data["manageCode"] = arr[1];
      updateImportUser(data).then(res1 =>{
        let res = res1.data
        console.log('res=', res)
        var status = res["status"];
        var buttonState = res["buttonState"]; // 确定按钮状态1- 变亮 0- 置灰
        this.importingData.buttonState = buttonState

        if (status == 1) { // 设置没问题

        } else if (status == 2) {
          this.$message.error('下级有员工，不能变成普通员工,设置失败！')
          staff.managerCodeStr = staff.oldManagerCodeStr
        } else {
          this.$message.error('设置失败！')
          staff.managerCodeStr = staff.oldManagerCodeStr
        }

      }).catch(err => {
        console.log('err=', err)
      })



    },
    getLeaders(staff){
      if(staff.type === 0){  // 是否直接普通员工

      } else{  // 选择直接上级的下拉框

      }
      let param = {"id": staff.id };
      selectOptionalLeader(param).then(res1 => {
        console.log('res1===', res1)
        let res = res1.data
        let userList = []
        res.forEach(u => {
          u.showName = u.userName + ' - - ' + u.mobile
          if(staff.leader == u.id){
            console.log('找到了')
          }
          userList.push(u)
        })
        staff.userList = userList
        console.log('staff.userList =', userList)

      }).catch(err=> {
        console.log('err=', err)
      })
    },
    getAuthTimeFun(){
      auth.getHostTime('/favicon.ico', hosttime => { // 获取服务器当前时间
        console.log('哈哈哈:', new Date(hosttime));
        this.todayDate = (new Date(hosttime).format("yyyy-MM-dd"))
      });
    },
    employeeContractSee(info){
      this.employeeInfo = info
      if (!info.personnelContract) {
        this.editContract(info)
      } else if (new Date(Number(info.personnelContract.validEnd) + 86399000) < new Date(this.todayDate)) {
        this.seeExpireContract = true
      } else {
        this.dialog_visible_seeContract = true
        this.manage_seeContractDetail(info,'see')
      }
    },
    employeeLeaveContractSee(info){
      this.employeeInfo = info
      if (!info.personnelContract) {
        this.$message({
          type: 'error',
          message: '对不起，系统没有这位职工劳动合同的信息！'
        })
      } else {
        this.dialog_visible_seeContract = true
        this.manage_seeContractDetail(info,'leave')
      }
    },
    editContract(info) {
      this.form_editContract.cusInfo = info
      this.initContract(1)
    },
    initContract(editType, contractInfo) {
      //this.joinBtnState = true
      this.dialog_visible_editContract = true
      this.$nextTick(() => {
        this.$refs['editContractForm'].resetFields()
        console.log('editContractForm', this.form_editContract)
        this.form_editContract.editType = editType
        this.form_editContract.validTimeMin = ''
        this.form_editContract.removeImgList = []

        if (editType > 1) {
          // 赋默认值
          empApi.getPersonnelContractMessage(contractInfo.id)
              .then(res => {
                let data = res.data.data
                let contractBase = data.perCon
                console.log(data)
                const newEditContract = this.form_editContract
                let imgs = data.listImage.map(item => {
                  return {
                    url: auth.webRoot + '/upload/' + item.uplaodPath,
                    normalPath: item.uplaodPath,
                    name: item.title,
                    operation: 4,
                    id: item.id
                  }
                })
                this.form_editContract = {
                  ...newEditContract,
                  id: contractBase.id,
                  editType: editType, // 1：新增 2：修改 3：续约
                  sn: contractBase.sn,
                  signTime: this.$filter.format(contractBase.signTime, 'day'),
                  validTime: [this.$filter.format(contractBase.validStart, 'day'), this.$filter.format(contractBase.validEnd, 'day')], // 自定义的，接收开始时间和结束时间的组合字段
                  validTimeMin: '',
                  imgList: imgs,
                  fileList: [],
                  remark: contractBase.remark
                }
                if (editType === 3) {
                  this.form_editContract.validTime = []
                  this.form_editContract.validTimeMin = this.$filter.format(contractBase.validEnd, 'day')
                }
                console.log('validTimeMin', this.form_editContract.validTimeMin)
                if (contractBase.filePath) {
                  this.form_editContract.fileList.push({
                    url: auth.webRoot + '/upload/' + contractBase.filePath,
                    normalPath: contractBase.filePath,
                    name: contractBase.fileName
                  })
                }
                console.log('this.form_editContract', this.form_editContract)
              })
        }
      })
    },
    join_disabledDate(time) {
      return time.getTime() < new Date(this.joinStaffData.validTimeMin).getTime()
    },
    disabledDate(time) {
      return time.getTime() < new Date(this.form_editContract.validTimeMin).getTime()
    },
    editContract_imgSuccess(response, file, fileList) {
      // 这里的response是服务器返回的数据
      // file是上传成功的文件对象
      // fileList是上传的文件列表
      let fileItem = {
        url: auth.webRoot + '/upload/' + response.filename,
        normalPath:  response.filename,
        name: response.originalFilename,
        operation: 1
      }
      this.form_editContract.imgList = this.form_editContract.imgList.map(f => (f.uid === file.uid ? fileItem : f));
      console.log('文件上传成功', this.form_editContract.imgList);
      // 在这里执行你需要的操作
      // if (this.form_editContract.imgList[this.form_editContract.imgList.length-1].normalPath) {
      //   this.joinBtnState = true
      // }
    },
    join_imgSuccess(response, file, fileList) {
      let fileItem = {
        url: auth.webRoot + '/upload/' + response.filename,
        normalPath:  response.filename,
        name: response.originalFilename,
        operation: 1,
      }
      this.joinStaffData.imgList = this.joinStaffData.imgList.map(f => (f.uid === file.uid ? fileItem : f))
      if (this.joinStaffData.imgList[this.joinStaffData.imgList.length-1].normalPath) {
        this.joinBtnState = true
      }
    },
    joinUploadImg(){
      this.joinBtnState = false
    },
    editContract_fileSuccess(response, file, fileList) {
      // 这里的response是服务器返回的数据
      // file是上传成功的文件对象
      // fileList是上传的文件列表
      let fileItem = {
        url: auth.webRoot + '/upload/' + response.filename,
        normalPath:  response.filename,
        name: response.originalFilename
      }
      fileList[fileList.length - 1] = fileItem
      console.log('文件上传成功', fileList);
      // 在这里执行你需要的操作
      this.joinBtnState = true
    },
    editContract_imgPreview(file) {
      this.dialog_visible_imgShow = true
      this.imgShow = file.url
    },
    join_uploadImgPreview(file) {
      this.dialog_visible_imgShow = true
      this.imgShow = file.url
    },
    editContract_imgHandleExceed() {
      this.$message({
        type: 'error',
        message: '最多只能上传9张！'
      })
    },
    editContract_fileHandleExceed(files, fileList) {
      this.$refs.upload.clearFiles()
      this.$refs.upload.handleStart(files[0])
      this.$refs.upload.submit()
    },
    join_fileHandleExceed(files, fileList) {
      this.$refs.join_upload.clearFiles()
      this.$refs.join_upload.handleStart(files[0])
      this.$refs.join_upload.submit()
    },
    editContract_imgRemove(file) {
      console.log('file', file)
      if (file.id) {
        file.operation = 2
        this.form_editContract.removeImgList.push(file)
      }
    },
    join_imgRemove(icon) {
      this.joinStaffData.imgList.splice(icon, 1)
    },
    editContract_submit() {
      this.$refs['editContractForm'].validate((valid) => {
        if (valid) {
          const editType = this.form_editContract.editType
          const contractInfo = this.form_editContract
          let imgs = contractInfo.imgList.map(item => {
            let newItem = {
              uplaodPath: item.normalPath,
              type: 1,
              title: item.name,
              operation: item.operation
            }
            if (item.id) newItem.id = item.id
            if (editType === 3) newItem.operation = 1
            return newItem
          })
          if (editType === 2) {
            // 修改时需要传删除得图片 operation 1 用于表示新增 4 表示没动 2表示删除
            let removeImgs = contractInfo.removeImgList.map(item => {
              return {
                uplaodPath: item.normalPath,
                type: 1,
                title: item.name,
                operation: 2,
                id: item.id
              }
            })
            imgs = [...imgs, ...removeImgs]
          }
          let data = {
            user: contractInfo.cusInfo.userID,
            username: contractInfo.cusInfo.userName,
            sn: contractInfo.sn,
            contractSignTime: contractInfo.signTime,
            remark: contractInfo.remark,
            contractBaseImages: JSON.stringify(imgs),
            contractStartTime: contractInfo.validTime?.[0],
            contractEndTime: contractInfo.validTime?.[1]
          }
          if (contractInfo.editType === 3) {
            data.type = contractInfo.type //1普通续约 2停用续约 3过期续约 (只有续约传）
          }
          if (contractInfo.fileList.length > 0) {
            data.filePath = contractInfo.fileList[0].normalPath
            data.fileName = contractInfo.fileList[0].name
          }
          if (editType > 1) {
            data.id = contractInfo.id
          }
          let promise = editType === 1?empApi.addEmpContract(data):(editType === 2?empApi.changeEmpContract(data): empApi.renewEmpContract(data))
          promise.then(res => {
            let state = res.data.data.state
            if (state === 1) {
              this.dialog_visible_editContract = false
              this.$message({
                type: 'success',
                message: '操作成功！'
              })
              this.getDutyList()
              if (editType === 2) {
                this.manage_seeContractDetail(this.employeeInfo,'see')
              }
            } else if (state === 2) {
              this.$message({
                type: 'error',
                message: '已续约不可修改！'
              })
            } else if (state === 3) {
              this.$message({
                type: 'error',
                message: '修改的日期不能在上一个合同结束日之前！'
              })
            } else {
              this.$message({
                type: 'error',
                message: '操作失败！'
              })
            }
          })
        } else {
          this.$message({
            type: 'error',
            message: '验证失败，请检查'
          })
        }
      })
    },
    manage_changeHis(item) {
      // 本版本合同的修改记录
      this.dialog_visible_seeContract = false
      this.dialog_visible_manage_changeHis = true
      empApi.getEmpContractChangeHis(item.id)
          .then(res => {
            let list = res.data.data.list
            this.seeContractChangeHis.list = list
          })
    },
    manage_renewHis(item) {
      // 本合同的续约记录
      this.seeExpireContract = false
      this.dialog_visible_seeContract = false
      this.dialog_visible_manage_renewHis = true
      empApi.getEmpContractRenewHis(item.userID)
          .then(res => {
            let list = res.data.data.list
            this.seeContractRenewHis.list = list
          })
    },
    manage_seeContractDetail(item, origin) {
      this.seeExpireContract = false
      let isChange = this.dialog_visible_manage_changeHis // 修改记录还是续约记录
      let promise = origin === 'see' || origin === 'leave'? empApi.getPersonnelContractMessage(item.personnelContract.id) : isChange?empApi.getEmpContractChangeHisDetail(item.id):empApi.getPersonnelContractMessage(item.id)
      promise.then(res => {
        let data = res.data.data
        let contractBase = isChange?data.contracthistory:data.perCon
        let listImage = isChange?data.listHisImage:data.listImage
        console.log(data)
        listImage.map(item => {
          item.url = auth.webRoot + '/upload/' + item.uplaodPath
          item.normalPath = item.uplaodPath
          item.name = item.title
        })
        let previewList = listImage.map(item => {
          return auth.webRoot + '/upload/' + item.uplaodPath
        })
        this.seeContractDetail.info = {
          userName: contractBase.username,
          origin,
          id: contractBase.id,
          filePath: contractBase.filePath,
          sn: contractBase.sn,
          signTime: this.$filter.format(contractBase.signTime, 'day'),
          validStart: this.$filter.format(contractBase.validStart, 'day'),
          validEnd: this.$filter.format(contractBase.validEnd, 'day'),
          imgList: listImage,
          previewList: previewList,
          fileList: [],
          remark: contractBase.remark,
          createName: contractBase.createName,
          createDate: contractBase.createDate
        }
        if (contractBase.filePath) {
          this.seeContractDetail.info.fileList.push({
            url: auth.webRoot + '/upload/' + contractBase.filePath,
            normalPath: contractBase.filePath,
            name: contractBase.fileName
          })
        }
        this.dialog_visible_seeContract = true
      })

    },
    manage_change() {
      // 修改合同信息
      this.dialog_visible_editContract = true
      this.form_editContract.cusInfo = {userID: this.employeeInfo.userID, userName: this.employeeInfo.userName}
      this.initContract(2, this.seeContractDetail.info)
    },
    manage_renew(type) {
      // 续约
      this.seeExpireContract = false
      this.dialog_visible_manage_renew = true
      this.form_editContract.cusInfo = {userID: this.employeeInfo.userID, userName: this.employeeInfo.userName}
      this.form_editContract.type = type
      this.initContract(3, this.employeeInfo.personnelContract)
    },
    manage_stop() {
      // 暂停履约/合同终止
      this.seeExpireContract = false
      this.$messageBox.confirm(
          `确定终止与这位职工的劳动合同吗？<br>点击“确定”后，这位职工将进入“离职列表”！`,
          '！！提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
      ).then(() => {
            empApi.stopPersonnelContract(this.employeeInfo.userID)
                .then(res => {
                  let state = res.data.data.state
                  if (state === 1) {
                    this.$message({
                      type: 'success',
                      message: '操作成功！'
                    })
                    this.getDutyList()
                    this.dialog_visible_seeContract = false
                  } else if (state === 0) {
                    this.$message({
                      type: 'error',
                      message: '请勿重复操作！'
                    })
                  } else {
                    this.$message({
                      type: 'error',
                      message: '操作失败！'
                    })
                  }
                })
          })
    },
    preview_File(path) {
      previewFile(this.rootPath, path??this.seeContractDetail.info.fileList[0].normalPath)
    },
    editContractClose(){
      this.dialog_visible_editContract = false
    },
    seeExpireContractClose(){
      this.seeExpireContract = false
    },
    manage_changeHisClose(){
      this.dialog_visible_manage_changeHis = false
    },
    manage_renewHisHide(){
      this.dialog_visible_manage_renewHis = false
    },
    seeContractClose(){
      this.dialog_visible_seeContract = false
    }
  }
}

</script>

<style lang="scss" scoped>
@use "@/style/contrct.scss";
.searchCon{
  background: #fff;
  padding: 0 10px;
  margin:10px 0 ;
  .timePicker{ width:150px!important; overflow: hidden; display: inline-block;}
  .timePicker:nth-child(1){ margin-right:10px; }
  .timePicker:nth-child(3){ margin-left:10px; }
  .trS{
    position: relative; width:100px;
    .checkAll{ position: absolute; left: 40px; top:26px; font-size: 14px; }
    .ttlS{ font-size: 12px; font-weight: bold; text-align: right;
      line-height: 35px; position: absolute; top:0; width: 80px; }
  }
}
.departCon{
  background: #eee; padding:10px;
  &>div:not(:first-child){
    margin-top: 10px;
  }
  .departItem {
    display: inline-block;
    padding: 1px 5px;
    border: 1px solid #fefefe;
    box-shadow: 0 0 2px #ccc;
    border-radius: 3px;
    margin: 3px 5px;
  }
  .departItem.active{ background:$tyBounce-color-blue; color: #fff;  }
}

.employeeIndex{
  padding: 50px 30px;
  min-height: 450px;

  .mergePhone{
    width: 224px; float: right; position: relative ;
    span{ position: absolute; left: -145px; z-index:1; font-size:14px; color: #666;   }
  }
  .marRight20{ margin-right: 20px; }
  .marBtm22{ margin-bottom: 20px; }
  .marL40{ padding-left: 40px; }
  .marL30{ margin-left: 30px; }
  .marTB20{ margin:20px 0; }
  .tipStyle{
    margin-top: 30px; line-height: 30px; font-size: 14px;
  }

  .tipCenter{
    line-height: 25px; padding: 20px; font-size: 14px; font-weight: 500; text-align: center;
  }

  .joinStaffForm{
    .midSize{width: 470px;}
    .el-form-item:not(.midSize){ width: 210px; }
    .el-form-item:nth-child(1){ margin-left:20px;  }
    .el-form-item:nth-child(2){ margin-left:50px;  }
    .el-select .el-input{ width: 160px; }
    .cttPos{margin-left: 30px;font-size: 14px;}
  }
  .editStaffForm{
    .el-form-item{ width: 266px; }
    .el-form-item:nth-child(1){ margin-left:20px;  }
    .el-form-item:nth-child(2){ margin-left:50px;  }
    .el-select .el-input{ width: 220px; }
  }

  .preViewCC{
    font-size:14px ; line-height: 25px; max-height: 400px; overflow-y: auto;
    .userBaseInfo { padding: 10px 30px; margin-bottom: 20px; background: #595959; overflow: hidden; }
    .otherInfo { border-bottom: 1px solid #ccc; padding-top:10px;  }
    .userImg { float: left; width: 100px; }
    .userCon { float: left; color: #eee; margin-left: 20px;  }
    .userImg img { width: 100%; border-radius: 50%; }
    .contectList { padding-bottom: 10px;   }
    .contectList .contI{
      min-width:116px; padding:10px; float: left;
      .fa { font-size: 25px; color: #eee; position:relative;  }
      .fa-qq{ top:5px;  }
      .fa-envelope{ top:5px;  }
      .fa-mobile{ font-size: 40px; }
      .qq, .envelope, .mobile {
        position:relative; display: inline-block; padding: 0 0 0 10px;
      }
      .mobile{  top:-10px;   }
      .qq{  top:2px;   }
      .envelope{  top:2px;   }
    }
    .ttlH5 { color: #00a278; font-size: 14px; }
    .charact { margin-bottom: 6px; margin-left: 110px; overflow: hidden; }
    .mmTtl { float: left; width: 20%; min-width: 100px; }
    .mmCon, .timeSlot { float: left; width: 70%; }
    .areaBood{ padding: 10px 0;  }
    .userName{ font-size: 18px;  }
  }

  .btnStyle{
    text-align: right;
  }

  .exportStep {
    padding-left: 40px; line-height: 30px;
    .stepItem {  margin-top: 20px;  }
    .flexRow{  margin-left: 50px;  display: flex;  justify-content:space-between;  width: 356px;  }
    .fileFullName{  width: 278px; line-height:32px;background: #fff;text-align: center;}
    .ty-btn-middle{padding: 0 22px;  height: 26px;  line-height: 26px;}
    .uploadify-button{
      padding: 3px 22px;
      min-width: 48px;
      text-align: center;
      margin: 0;
      border-radius: 0;
      height: 26px;
      line-height: 26px;
      background-color: #5d9cec;
      color: #fff;
      border: none;
      display: inline-block;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      text-decoration: none;

    }
  }
  .narrowBody{margin: 0 auto;width: 80%;}
  .baseTab{ line-height: 40px; max-height: 500px; overflow-y: auto; margin-left: 40px; font-size:15px;  }
  .eduTab{
    line-height: 40px;
    td{ padding: 0 10px; }
  }
  .concatItem{
    width:300px; float: left;
    .ty-linkBtn-red{ padding:0; }
    &:nth-child(even){ margin-left: 80px; }
  }
  .inputDiv{ line-height: 30px; border: 1px solid #ccc; text-align: left;
    width: 100%; border-radius: 2px; padding: 0 10px; height: 30px; }
  .inputArea{
    height: 68px; line-height: 19px; overflow-y: auto; padding: 5px; margin-bottom: 10px;
  }
  .concatItem2 {
    width:45%; float: left;
    .contactTtl{ display: inline-block; width:80px; text-align: right;  }
    .contactCon{
      line-height: 26px;border: 1px solid #ccc;text-align: left;width: 150px;border-radius:2px;padding: 0 10px;display: inline-block;margin-left:10px;
    }

  }
  .editEduFrm{
    margin-left: 20px; line-height: 30px; font-size: 15px;
    td{ padding-right: 10px; padding-top: 10px; }

  }
  .ty-hr{
    margin: 16px 0;
    width: 100%;
    height: 1px;
    background: #eee;
  }
  .gapTp{margin-top: 20px;}
  .inlinePos{
    float: left;
    text-align: center;
    width: 200px;
    line-height: 26px;
  }
  .redMark{color: red;}
  .exContractCon{
    :deep(.el-link__inner){
      font-weight: bold;
    }
  }
  .gapBro{
    margin: 16px 0;
  }
  .gapB{
    margin-bottom: 26px;
  }
  .gapBt{
    margin-bottom: 10px;
  }
  .txtRight{
    text-align: right;
  }
  .flexBox{
    display: flex;
    justify-content: space-between;
  }
  .gapSm{top: 2px;}
  .grayTxt{color:#b1b4ba;}
  .join_imgUpload {
    :deep(.el-upload){
      display: none
    }
    :deep(.el-form-item__content){
      line-height: inherit;
    }
    :deep(.el-upload-list__item){
      margin-bottom: 0;
    }
    :deep(.el-upload-list){
      margin: 0;
    }
    .multiList :deep(.el-upload-list li){
      float: left;
      margin-right: 10px;
      width: auto;
    }
  }
  .seeLv{
    margin-top: 4px;
  }
  .seeExpire{
    :deep(.el-descriptions__content) {
      float: right;
    }
  }
  .join_imgUpload :deep(.el-form-item__label),.widthLimit :deep(.el-form-item__label){
    width: 100%;
    display: block;
  }
  :deep(.el-upload){
    display: none;
  }
  .bounce-blue .bonceFoot .bounce-ok:disabled{
    background: #babbbd;
  }
}

</style>
