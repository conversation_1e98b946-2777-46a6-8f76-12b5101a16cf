package cn.sphd.miners.modules.dailyIndex.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by HIAPAD on 2022/6/9.
 */
@Entity
@Table( name ="t_daily_org")
public class DailyOrg extends BaseEntity {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "org" )
    private Integer org; //机构ID

    @Column(name = "year_month_day")
    private Integer yearMonthDay;//`年月日

    @Column(name = "user_num")
    private Integer userNum=0;//在册职工数

    @Column(name = "viewer_num")
    private Integer viewerNum=0;//浏览者数

    @Column(name = "user_login_count")
    private Integer userLoginCount=0;//'当天登录用户次数'

    @Column(name = "user_count")
    private Integer userCount=0;//当天登录用户个数

    @Column(name = "user_duration")
    private Integer userDuration=0;//当天登录用户时长(秒)，计算时按需求换算成小时

    @Column(name = "viewer_login_count")
    private Integer viewerLoginCount=0;//当天浏览者登录次数

    @Column(name = "viewer_count")
    private Integer viewerCount=0;//当天登录浏览者个数

    @Column(name = "viewer_duration")
    private Integer viewerDuration=0;//当天登录浏览者时长(秒)，计算时按需求换算成小时

    @Column(name = "respondents_count")
    private Integer respondentsCount=0;//调查+招聘人数

    @Column(name = "respondents_distribution")
    private Integer respondentsDistribution=0;//调查+招聘分布

    @Column(name = "activity_index")
    private Float activityIndex;//活跃指数

    @Column(name = "create_time")
    @CreationTimestamp
    private Date createTime;//创建时间

    @Column(name = "update_time")
    @UpdateTimestamp
    private Date updateTime;//修改时间

    public DailyOrg(){

    }

    public DailyOrg(Integer org,Integer yearMonthDay,Integer userNum,Integer viewerNum,Integer userLoginCount,Integer userCount,Integer userDuration,Integer viewerLoginCount,Integer viewerCount,Integer viewerDuration,Integer respondentsCount){
        this.org=org;
        this.yearMonthDay=yearMonthDay;
        this.userNum=userNum;
        this.viewerNum=viewerNum;
        this.userLoginCount=userLoginCount;
        this.userCount=userCount;
        this.userDuration=userDuration;
        this.viewerLoginCount=viewerLoginCount;
        this.viewerCount=viewerCount;
        this.viewerDuration=viewerDuration;
        this.respondentsCount=respondentsCount;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getYearMonthDay() {
        return yearMonthDay;
    }

    public void setYearMonthDay(Integer yearMonthDay) {
        this.yearMonthDay = yearMonthDay;
    }

    public Integer getUserNum() {
        return userNum;
    }

    public void setUserNum(Integer userNum) {
        this.userNum = userNum;
    }

    public Integer getViewerNum() {
        return viewerNum;
    }

    public void setViewerNum(Integer viewerNum) {
        this.viewerNum = viewerNum;
    }

    public Integer getUserLoginCount() {
        return userLoginCount;
    }

    public void setUserLoginCount(Integer userLoginCount) {
        this.userLoginCount = userLoginCount;
    }

    public Integer getUserCount() {
        return userCount;
    }

    public void setUserCount(Integer userCount) {
        this.userCount = userCount;
    }

    public Integer getUserDuration() {
        return userDuration;
    }

    public void setUserDuration(Integer userDuration) {
        this.userDuration = userDuration;
    }

    public Integer getViewerLoginCount() {
        return viewerLoginCount;
    }

    public void setViewerLoginCount(Integer viewerLoginCount) {
        this.viewerLoginCount = viewerLoginCount;
    }

    public Integer getViewerCount() {
        return viewerCount;
    }

    public void setViewerCount(Integer viewerCount) {
        this.viewerCount = viewerCount;
    }

    public Integer getViewerDuration() {
        return viewerDuration;
    }

    public void setViewerDuration(Integer viewerDuration) {
        this.viewerDuration = viewerDuration;
    }

    public Integer getRespondentsCount() {
        return respondentsCount;
    }

    public void setRespondentsCount(Integer respondentsCount) {
        this.respondentsCount = respondentsCount;
    }

    public Integer getRespondentsDistribution() {
        return respondentsDistribution;
    }

    public void setRespondentsDistribution(Integer respondentsDistribution) {
        this.respondentsDistribution = respondentsDistribution;
    }

    public Float getActivityIndex() {
        return activityIndex;
    }

    public void setActivityIndex(Float activityIndex) {
        this.activityIndex = activityIndex;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
