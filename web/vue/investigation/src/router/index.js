import Vue from 'vue'
import Router from 'vue-router'
import base from '../view/base'
import home from '../view/home'
import answer from '../view/answer'
import error from '../view/error'
import success from '../view/success'

Vue.use(Router)

export default new Router({
  routes: [
    {
      path: '/',
      name: 'base',
      component: base
    }, {
      path: '/item/:item',
      name: 'base',
      component: base
    }, {
      path: '/error',
      name: 'error',
      component: error
    }, {
      path: '/success',
      name: 'success',
      component: success
    }, {
      path: '/item/:item',
      name: 'home',
      component: home
    },{
      path: '/item/:item',
      name: 'answer',
      component: answer
    }, {
      path: '/:item',
      name: 'base',
      component: base
    }
  ]
})
