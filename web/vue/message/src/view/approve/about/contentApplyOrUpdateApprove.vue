<template>
  <div id="contentApplyOrUpdateApprove">
    <TY_NavTop title="内容发布/换版审批" :isBackHome="isBackHome" isSearch="true" @toggleList="screen"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="datePane">今天是{{date_today}}  {{weekDay}}</div>
      <div class="ui-cells">
        <a class="ui-cell" v-on:click="approveTo(item.id)" v-for="(item, index) in listApprove" :key="index">
          <div class="fileLists">
            <div class="fileType file_txt"></div>
            <div class="fileInfo">
              <div class="fileName" v-text="item.name" :title="item.name"></div>
              <div class="fileDetail">{{item.updateName || item.createName}} {{(item.updateTime || item.createTime) |formatDay('YYYY-MM-DD HH:mm:ss') }}</div>
            </div>
            <div class="fileFormat">
              <span class="fileVersion">G{{item.versionNo}}</span>
              <span class="fileNo">编号：{{item.fileSn}}</span>
            </div>
          </div>
        </a>
      </div>
    </div>
  </div>
</template>

<script>
  import * as moment from 'moment'
  import 'moment/locale/zh-cn'
  export default {
    name: 'contentApplyOrUpdateApprove',
    data () {
      return {
        loading: true,
        isBackHome: true,
        weekDay: '',
        date_today: moment(new Date()).format('YYYY/MM/DD'),
        listApprove: [],
        listenersUid: []
      }
    },
    created: function () {
      let today = new Date()
      let week = today.getDay()
      let weekArr = ['日', '一', '二', '三', '四', '五', '六']
      this.weekDay = '周' + weekArr[week]
      let _this = this
      _this.$http.post('../../../about/aboutLastApplyFile.do').then((response) => {
        let reqData = response.body.data
        _this.listApprove = reqData.listAboutFileByApply
        _this.loading = false
      }).catch(function () {
        _this.loading = false
        _this.$message({
          type: 'error',
          message: '系统错误，请重试！'
        })
      })
      this.listenersUid = [
        _this.sphdSocket.subscribe('contentReleaseVersionApproval', function (data) { // 终审时更新数据
          console.log('内容终审订阅:' + data)
          let reqData = JSON.parse(data)
          let update = reqData.operate
          let newDate = {}
          if (update > 0) { // 增加一条
            console.log('增加一条')
            newDate = reqData.aboutFileHis
            _this.listApprove.push(newDate)
          } else if (update < 0) { // 减少一条
            console.log('减少一条')
            let deletId = reqData.aboutFileHis.id
            _this.listApprove.forEach(function (item, index) {
              if (deletId === item.id) {
                _this.listApprove.splice(index, 1)
              }
            })
          }
        }, null, 'custom', this.sphdSocket.org.id + 'rbgeneral')
      ]
    },
    destroyed: function () {
      let _this = this
      this.listenersUid.forEach(function (item) {
        _this.sphdSocket.unsubscribe(item)
      })
    },
    methods: {
      approveTo: function (id) {
        this.$router.push({
          path: `/contentDetail/${id}/approveLast`
        })
      },
      screen: function () {
        this.$router.push({
          path: '/contentQuery/approveLast'
        })
      }
    }
  }
</script>
