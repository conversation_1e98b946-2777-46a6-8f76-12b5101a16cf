package cn.sphd.miners.modules.purchaseInvoice.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.finance.entity.FinancePayment;
import cn.sphd.miners.modules.finance.service.FinancePaymentService;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.purchaseInvoice.entity.PoPaymentApplication;
import cn.sphd.miners.modules.purchaseInvoice.service.PurchaseInvoiceService;
import cn.sphd.miners.modules.sales.model.PoOrders;
import cn.sphd.miners.modules.sales.model.PoOrdersPrepayment;
import cn.sphd.miners.modules.sales.service.PoService;
import cn.sphd.miners.modules.system.entity.User;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 1.169采购的票款处理
 * 李娅星  2021/6/16
 * 1.229采购之预付款--lyx/********
 */
@Controller
@RequestMapping("/purchaseInvoice")
public class PurchaseInvoiceController {
    @Autowired
    PurchaseInvoiceService purchaseInvoiceService;
    @Autowired
    PoService poService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    FinancePaymentService financePaymentService;

    /**
     * <AUTHOR>
     * @Description 票款处理的票据录入
     * @Date 2021/6/17
     * @param type(类型：1-仅提交票据,不提交付款申请;2-录入票据,并提交付款申请;3-仅提交付款申请,不录入票据) applicationReason(申请原因) applicationDesc(申请不符合录入的原因)
     * @return 
     **/
    @ResponseBody
    @RequestMapping("/purchaseInvoiceApply.do")
    public JsonResult purchaseInvoiceApply(User user,String type,Double amountTotal,Double billAmountTotal,Integer orders,String applicationReason,String applicationDesc,String purchaseInvoice){
        Map<String,Object> map = new HashMap<>();
        if (!StringUtils.isEmpty(type)) {
            map = purchaseInvoiceService.purchaseInvoiceApply(user, type,amountTotal,billAmountTotal,orders,applicationReason, applicationDesc,purchaseInvoice);
        }else {
            map.put("content","操作失败");
        }
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 申请人-采购部门的票据审核列表
     * @Date 2021/6/18
     * @param
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getInvoiceApply.do")
    public JsonResult getInvoiceApply(User user){
        Map<String,Object> map = new HashMap<>();
        List<Map<String,Object>> invoiceApplyHandle = purchaseInvoiceService.getPurchaseInvoicesByState(user.getUserID(),user.getOid(),"5","4",null,null,null); //待处理
        map.put("invoiceApplyHandle",invoiceApplyHandle);
        return new JsonResult(1,map);
    }


    /**
     * <AUTHOR>
     * @Description 申请人-采购部门的付款列表
     * @Date 2021/6/18
     * @param
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getPaymentApply.do")
    public JsonResult getPaymentApply(User user){
        Map<String,Object> map = new HashMap<>();
        List<Map<String,Object>> paymentApplyHandle = purchaseInvoiceService.getPurchaseInvoicesByState(user.getUserID(),user.getOid(),"5","3",null,null,null); //待处理
        map.put("paymentApplyHandle",paymentApplyHandle);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 出纳-采购部门的票据审核列表
     * @Date 2021/6/18
     * @param
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getFinanceInvoiceApply.do")
    public JsonResult getFinanceInvoiceApply(User user){
        Map<String,Object> map = new HashMap<>();
        List<Map<String,Object>> paymentOnlineHandle = purchaseInvoiceService.getPurchaseInvoices(user.getOid(),null,null,"1","2",null,null,null,null,null,45);  //待在线审核
        List<Map<String,Object>> paymentOnlineOK = purchaseInvoiceService.getPurchaseInvoices(user.getOid(),null,null,"1","3",null,null,null,null,null,46);  //在线审核ok
        List<Map<String,Object>> paymentOfflineHandle = purchaseInvoiceService.getPurchaseInvoices(user.getOid(),null,null,"1","5",null,null,null,null,null,47);  //待线下审核
        map.put("paymentOnlineHandle",paymentOnlineHandle);
        map.put("paymentOnlineOK",paymentOnlineOK);
        map.put("paymentOfflineHandle",paymentOfflineHandle);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 出纳-采购部门的付款列表
     * @Date 2021/6/18
     * @param
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getFinancePaymentApply.do")
    public JsonResult getFinancePaymentApply(User user){
        Map<String,Object> map = new HashMap<>();
        List<Map<String,Object>> payApprovalFinanceHandle = purchaseInvoiceService.getPurchaseInvoices(user.getOid(),null,null,"1",null,null,null,null,63,"1",49);  //待付款审批
        List<Map<String,Object>> payableFinanceHandle = purchaseInvoiceService.getPurchaseInvoices(user.getOid(),null,null,"1",null,null,null,null,64,"1",50);  //可付款
        List<Map<String,Object>> reviewedFinanceHandle = purchaseInvoiceService.getPurchaseInvoices(user.getOid(),null,null,"1",null,null,null,null,65,"1",51);  //待复核
        List<Map<String,Object>> payFinanceHandle = purchaseInvoiceService.getPurchaseInvoices(user.getOid(),null,null,"1",null,null,null,null,66,"1",52);  //待付款
        map.put("payApprovalFinanceHandle",payApprovalFinanceHandle);
        map.put("payableFinanceHandle",payableFinanceHandle);
        map.put("reviewedFinanceHandle",reviewedFinanceHandle);
        map.put("payFinanceHandle",payFinanceHandle);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 采购审批者的采购部门的票据审核列表
     * @Date 2021/6/21
     * @param
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getPurchaseInvoiceList.do")
    public JsonResult getPurchaseInvoiceList(User user){
        Map<String,Object> map = new HashMap<>();
        List<Map<String,Object>> purchaseInvoiceHandle = purchaseInvoiceService.getPurchaseInvoices(user.getOid(),user.getUserID(),null,"1","3",null,"1",null,null,null,46); //待处理
//        List<Map<String,Object>> purchaseInvoiceApproved = purchaseInvoiceService.getPurchaseInvoices(user.getOid(),user.getUserID(),null,"2","5",null,"1",46); //已批准
        List<Map<String,Object>> purchaseInvoiceApproved = purchaseInvoiceService.getPurchaseInvoices(user.getOid(),user.getUserID(),null,"2",null,null,"5",null,null,null,46); //已批准
        map.put("purchaseInvoiceHandle",purchaseInvoiceHandle);
        map.put("purchaseInvoiceApproved",purchaseInvoiceApproved);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 采购审批者的采购部门的付款列表
     * @Date 2021/6/17
     * @param
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getPurchasePaymentList.do")
    public JsonResult getPurchasePaymentList(User user){
        Map<String,Object> map = new HashMap<>();
        List<Map<String,Object>> purchasePaymentHandle = purchaseInvoiceService.getPurchaseInvoices(user.getOid(),user.getUserID(),null,"1",null,"2","1",null,null,null,48); //待处理
        List<Map<String,Object>> purchasePaymentApproved = purchaseInvoiceService.getPurchaseInvoices(user.getOid(),user.getUserID(),null,"2",null,"3","4",null,null,null,48); //已批准
        map.put("purchasePaymentHandle",purchasePaymentHandle);
        map.put("purchasePaymentApproved",purchasePaymentApproved);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 出纳的待在线审核审批
     * @Date 2021/6/21
     * @param approvalProcessId-审批流程id    approveStatus（2-批准 3-驳回）  reason-驳回理由（其他的时候录入的）  approveSelect-驳回理由(数字以逗号分隔)
     * @return
     **/
    @ResponseBody
    @RequestMapping("/paymentOnlineApproval.do")
    public JsonResult paymentOnlineApproval(User user,Integer approvalProcessId,String approveStatus,String reason,String approveSelect){
        Map<String,Object> map = new HashMap<>();
        if (approvalProcessId!=null&&!StringUtils.isEmpty(approveStatus)){
            map = purchaseInvoiceService.paymentOnlineApproval(user,approvalProcessId,approveStatus,reason,approveSelect);
        }else {
            map.put("content","传值有误");
        }
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 采购审批者-采购部门的票据审核的审批(即出纳的在线审核ok)
     * @Date 2021/6/21
     * @param  approvalProcessId 审批流程id    approveStatus（2-批准 3-驳回）  reason：录入的驳回理由
     * @return
     **/
    @ResponseBody
    @RequestMapping("/purchaseOnlineApproval.do")
    public JsonResult purchaseOnlineApproval(User user,Integer approvalProcessId,String approveStatus,String reason){
        Map<String,Object> map = new HashMap<>();
        if (approvalProcessId!=null&&!StringUtils.isEmpty(approveStatus)){
            map = purchaseInvoiceService.purchaseOnlineApproval(user,approvalProcessId,approveStatus,reason);
        }else {
            map.put("content","传值有误");
        }
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 出纳的待线下审核审批
     * @Date 2021/6/22
     * @param  approvalProcessId 审批流程id    approveStatus（2-批准[一致。线下审核通过。] 3-驳回[不一致，驳回报销申请。]）
     * @return
     **/
    @ResponseBody
    @RequestMapping("/purchaseOfflineApproval.do")
    public JsonResult purchaseOfflineApproval(User user,Integer approvalProcessId,String approveStatus){
        Map<String,Object> map = new HashMap<>();
        if (approvalProcessId!=null&&!StringUtils.isEmpty(approveStatus)){
            map = purchaseInvoiceService.purchaseOfflineApproval(user,approvalProcessId,approveStatus);
        }else {
            map.put("content","传值有误");
        }
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 采购审批者-采购部门的付款(仅提交付款申请)的审批
     * @Date 2021/6/22
     * @param  approvalProcessId 审批流程id    approveStatus（2-批准 3-驳回）
     * @return
     **/
    @ResponseBody
    @RequestMapping("/purchasePaymentApproval.do")
    public JsonResult purchasePaymentApproval(User user,Integer approvalProcessId,String approveStatus,String reason,String approveSelect){
        Map<String,Object> map = new HashMap<>();
        if (approvalProcessId!=null&&!StringUtils.isEmpty(approveStatus)){
            map = purchaseInvoiceService.purchasePaymentApproval(user,approvalProcessId,approveStatus,reason,approveSelect);
        }else {
            map.put("content","传值有误");
        }
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 付款审批者-采购部门的常规付款列表
     * @Date 2021/6/22
     * @param
     * @return
     **/
    @ResponseBody
    @RequestMapping("/paymentApprovalList.do")
    public JsonResult paymentApprovalList(User user){
        Map<String,Object> map = new HashMap<>();
        List<Map<String,Object>> paymentApprovalHandle = purchaseInvoiceService.getPurchaseInvoices(user.getOid(),user.getUserID(),null,"1",null,null,"4",null,null,null,49); //待处理
        List<Map<String,Object>> paymentApprovalApproved = purchaseInvoiceService.getPurchaseInvoices(user.getOid(),user.getUserID(),null,"2",null,null,"4",null,null,null,49); //已批准
        map.put("paymentApprovalHandle",paymentApprovalHandle);
        map.put("paymentApprovalApproved",paymentApprovalApproved);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 付款审批者-采购部门的常规付款审批
     * @Date 2021/6/22
     * @param  approvalProcessId 审批流程id    没有驳回，只有同意付款
     * @return
     **/
    @ResponseBody
    @RequestMapping("/paymentApprovalApproval.do")
    public JsonResult paymentApprovalApproval(User user,Integer approvalProcessId){
        Map<String,Object> map = new HashMap<>();
        if (approvalProcessId!=null){
            map = purchaseInvoiceService.paymentApprovalApproval(user,approvalProcessId);
        }else {
            map.put("content","传值有误");
        }
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 出纳-采购部门的付款-可付款审批(录入付款方式)
     * @Date 2021/6/23
     * @param  approvalProcessId 审批流程id    没有驳回，只有同意付款
     * @param  method(1-现金,2-现金支票,3-转帐支票(外部),4-承兑汇票,5-银行转帐 6-转帐支票(内部)) planDate(计划时间,拟付款日期)  factDate(实际时间,接收单位收到日期) accountId(账户id) summary(摘要)
     * @param  invoiceId(票据id) expireDate(支票到期日) receiveDate(接收日期) receiver(接收经手人) operator(支付经手人) oppositeCorp(收款单位) operatorName(接收经手人（收款单位经手人)）
     * @param  factAmount(实际付款金额)
     * @return
     **/
    @ResponseBody
    @RequestMapping("/payableFinanceApproval.do")
    public JsonResult payableFinanceApproval(User user,Integer approvalProcessId,String method,String planDate,String factDate,Integer accountId,
               String summary,Integer invoiceId,String expireDate,String receiveDate,String receiver,String operator,String oppositeCorp,String operatorName,
               String factAmount){
        Map<String,Object> map = new HashMap<>();
        if (approvalProcessId!=null&&!StringUtils.isEmpty(method)){
            Date planDate1 = null;
            if (StringUtils.isNotEmpty(planDate)){
                planDate1 = NewDateUtils.dateFromString(planDate,"yyyy-MM-dd");
            }
            Date factDate1 = null;
            if (StringUtils.isNotEmpty(factDate)){
                factDate1 = NewDateUtils.dateFromString(factDate,"yyyy-MM-dd");
            }
            map = purchaseInvoiceService.payableFinanceApproval(user,approvalProcessId,method, planDate1,factDate1,accountId,summary,invoiceId,expireDate,receiveDate,receiver,operator,oppositeCorp,operatorName,factAmount);
        }else {
            map.put("content","传值有误");
        }
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 复核审批者-采购部门的报销列表
     * @Date 2021/6/23
     * @param
     * @return
     **/
    @ResponseBody
    @RequestMapping("/reviewedApprovalList.do")
    public JsonResult reviewedApprovalList(User user){
        Map<String,Object> map = new HashMap<>();
        List<Map<String,Object>> reviewedApprovalHandle = purchaseInvoiceService.getPurchaseInvoices(user.getOid(),user.getUserID(),null,"1",null,null,"4",null,65,"1",51); //待处理
        List<Map<String,Object>> reviewedApprovalApproved = purchaseInvoiceService.getPurchaseInvoices(user.getOid(),user.getUserID(),null,"2",null,null,"4",1,65,"1",51); //已批准
        map.put("reviewedApprovalHandle",reviewedApprovalHandle);
        map.put("reviewedApprovalApproved",reviewedApprovalApproved);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 复核审批者-采购部门的报销/预付款审批
     * @Date 2021/6/23
     * @param  approvalProcessId 审批流程id    没有驳回
     * @return
     **/
    @ResponseBody
    @RequestMapping("/reviewedApprovalApproval.do")
    public JsonResult reviewedApprovalApproval(User user,Integer approvalProcessId){
        Map<String,Object> map = new HashMap<>();
        if (approvalProcessId!=null){
            map = purchaseInvoiceService.reviewedApprovalApproval(user,approvalProcessId);
        }else {
            map.put("content","传值有误");
        }
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 出纳-待付款审批
     * @Date 2021/6/23
     * @param factDate(实际付款日期,收款单位接收日期) summary（摘要） operatorName（收款单位经手人，接收经手人）
     * @return
     **/
    @ResponseBody
    @RequestMapping("/payFinanceApproval.do")
    public JsonResult payFinanceApproval(User user,Integer approvalProcessId,String factDate, String summary,String operatorName){
        Map<String,Object> map = new HashMap<>();
        if (approvalProcessId!=null){
            Date factDate1 = null;
            if (StringUtils.isNotEmpty(factDate)){
                factDate1 = NewDateUtils.dateFromString(factDate,"yyyy-MM-dd");
            }
            map = purchaseInvoiceService.payFinanceApproval(user,approvalProcessId,factDate1,summary,operatorName);
        }else {
            map.put("content","传值有误");
        }
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 获取详情
     * @Date 2021/6/24
     * @param applicationId 申请的报销id  financePaymentId-支付id(在出纳-可付款操作之后查看详情使用)
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getPurchaseInvoiceDetail.do")
    public JsonResult getPurchaseInvoiceDetail(Integer applicationId,Integer financePaymentId){
        Map<String,Object> map = new HashMap<>();
        if (applicationId!=null||financePaymentId!=null){
            map = purchaseInvoiceService.getPurchaseInvoiceDetail(applicationId,financePaymentId);
        }
        return new JsonResult(1,map);
    }


    /**
     * <AUTHOR>
     * @Description 按发票种类/费用类别查询发票详情
     * @Date 2021/8/6
     * @param applicationId（申请的报销id）  invoiceCategory（发票种类1-专用票,2-普通票,3-收据,4-其他发票）  feeCategory（费用类别 在此默认为“采购材料”）
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getInvoiceItemDetail.do")
    public JsonResult getInvoiceItemDetail(Integer applicationId,String invoiceCategory,String feeCategory){
        Map<String,Object> map = new HashMap<>();
        map = purchaseInvoiceService.getInvoiceItemDetail(applicationId,invoiceCategory,feeCategory);
        return new JsonResult(1,map);
    }


    /**
     * <AUTHOR>
     * @Description 获取原定的付款方式
     * @Date 2021/6/24
     * @param businessTypePHprocurement-采购票据 prepayment-采购的预付款【查询付款方式的信息】
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getFinancePayment.do")
    public JsonResult getFinancePayment(Integer financePaymentId,String businessTypePH){
        Map<String,Object> map = new HashMap<>();
        if (financePaymentId!=null){
            map = purchaseInvoiceService.getFinancePayment(financePaymentId,businessTypePH);
        }
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 修改付款方式
     * @Date 2021/6/24
     * @param approvalProcessId 审批流程id    没有驳回，只有同意付款    type 1-待复核修改的 2-待付款修改的
     * @param  method(1-现金,2-现金支票,3-转帐支票(外部),4-承兑汇票,5-银行转帐 6-转帐支票(内部)) planDate(计划时间)  factDate(实际时间) accountId(账户id) summary(摘要)
     * @param  invoiceId(票据id) expireDate(支票到期日) receiveDate(接收日期) receiver(接收经手人) operator(支付经手人) oppositeCorp(收款单位)
     * @param  factAmount(实际付款金额)
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getUpdateFinancePayment.do")
    public JsonResult getUpdateFinancePayment(User user,Integer type,Integer approvalProcessId,String method,String planDate,String factDate,Integer accountId,
                 String summary,Integer invoiceId,String expireDate,String receiveDate,String receiver,String operator,String oppositeCorp,String factAmount){
        Map<String,Object> map = new HashMap<>();
        Date planDate1=null;
        Date factDate1=null;
        if (StringUtils.isNotEmpty(planDate)){
            planDate1 = NewDateUtils.dateFromString(planDate,"yyyy-MM-dd");
        }
        if (StringUtils.isNotEmpty(factDate)){
            factDate1 = NewDateUtils.dateFromString(factDate,"yyyy-MM-dd");
        }
        map = purchaseInvoiceService.getUpdateFinancePayment(user,type,approvalProcessId,method,planDate1,factDate1,accountId,summary,invoiceId,expireDate,receiveDate,receiver,operator,oppositeCorp,factAmount);
        return new JsonResult(1,map);
    }
    
    
    /**
     * <AUTHOR>
     * @Description 撤回
     * @Date 2021/6/29
     * @param
     * @return 
     **/
    @ResponseBody
    @RequestMapping("/cancelPurchaseInvoice.do")
    public JsonResult cancelPurchaseInvoice(User user,Integer applicationId){
        Map<String,Object> map = new HashMap<>();
        if (applicationId!=null){
            map = purchaseInvoiceService.cancelPurchaseInvoice(user,applicationId);
        }else {
            map.put("content","传值有误");
        }
        return new JsonResult(1,map);
    }

    // /account/getAccountKinds.do（获取账户的）  /data/getChequeDetailByAccountId.do（某账户的所有空白支票列表）   /data/chooseCheque.do（获取承兑或转账支票的，还有获取账户的）


    //--------------------------------------------以下是1.229采购之预付款部分内容，上面也有--lyx/********--------------------------//

    /**
     * <AUTHOR>
     * @Description 申请人-采购预付款的申请列表
     * @Date 2022/8/31
     * @param
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getAdvancePaymentApply.do")
    public JsonResult getAdvancePaymentApply(User user){
        Map<String,Object> map = new HashMap<>();
        List<Map<String, Object>> advancePaymentApplys = purchaseInvoiceService.getAdvancePayments(user.getOid(),null,user.getUserID(),null,null,"1",null);
        map.put("advancePaymentApplyHandle",advancePaymentApplys);
        return new JsonResult(1,map);
    }


    /**
     * <AUTHOR>
     * @Description 付款审批人-付款审批-采购的预付款列表
     * @Date 2022/9/1
     * @param
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getAdvancePaymentPay.do")
    public JsonResult getPayAdvancePayment(User user){
        Map<String,Object> map = new HashMap<>();
        //待处理
        List<Map<String, Object>> advancePaymentPayHandle = purchaseInvoiceService.getAdvancePayments(user.getOid(),user.getUserID(),null,63,"1","1",null);
        //已批准
        List<Map<String, Object>> advancePaymentPayApproved = purchaseInvoiceService.getAdvancePayments(user.getOid(),user.getUserID(),null,63,"2","1",null);
        map.put("advancePaymentPayHandle",advancePaymentPayHandle);
        map.put("advancePaymentPayApproved",advancePaymentPayApproved);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 预付款的获取详情
     * @Date 2022/9/2
     * @param orderId 订单id   financePaymentId-支付id(在出纳-可付款操作之后查看详情使用)
     * @return 
     **/
    @ResponseBody
    @RequestMapping("/advancePaymentDetail.do")
    public JsonResult advancePaymentDetail(Integer orderPrepaymentId,Integer financePaymentId){
        Map<String,Object> map = purchaseInvoiceService.advancePaymentDetail(orderPrepaymentId,financePaymentId);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 付款审批人-付款审批-采购的预付款-待付款审批的审批
     * @Date 2022/9/2
     * @param approvalProcessId审批流程   只有批准，没有驳回
     * @return 
     **/
    @ResponseBody
    @RequestMapping("/advancePaymentPayApproved.do")
    public JsonResult advancePaymentPayApproved(User user,Integer approvalProcessId){
        Map<String,Object> map = new HashMap<>();
        if (approvalProcessId!=null){
            map = purchaseInvoiceService.advancePaymentPayApproved(user,approvalProcessId);
        }else {
            map.put("state",0);
            map.put("content","失败，approvalProcessId未传值");
        }
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 常规借款的--付出去的采购预付款
     * @Date 2022/9/7
     * @param yearMonth-年月(格式 yyyyMM)  prepaymentStatus:预付款审批状态0-未申请,1-申请提交,2-通过审核(通过了出纳审批才进入常规借款),3-否决审核,4-撤回
     * @return
     **/
    @ResponseBody
    @RequestMapping("/advancePaymentPayLoan.do")
    public JsonResult advancePaymentPayLoan(User user,String prepaymentStatus,String yearMonth,PageInfo pageInfo){
        Map<String,Object> map = new HashMap<>();
        List<Map<String,Object>> poOrders = poService.getOrderByPrepaymentStatus(user,prepaymentStatus,null,yearMonth,pageInfo);
        map.put("poOrders",poOrders);
        return new JsonResult(1,map,pageInfo);
    }

    /**
     * <AUTHOR>
     * @Description 常规借款的--时间线查看--即审批流程
     * @Date 2022/9/7
     * @param  orderPrepaymentId 预付款id   financePaymentId付款方式id   applicationId：票据id
     * @return
     **/
    @ResponseBody
    @RequestMapping("/orderTimeLine.do")
    public JsonResult orderTimeLine(Integer financePaymentId){
        Map<String,Object> map = new HashMap<>();
        FinancePayment financePayment = financePaymentService.getPaymentByBusiness(financePaymentId,null,null);
//        String businessType = "prepayment";  //默认预付款的
        if (financePayment!=null&&financePayment.getBusinessType().equals("prepayment")){
            Integer orderPrepaymentId = financePayment.getBusiness();
            PoOrdersPrepayment poOrdersPrepayment = purchaseInvoiceService.getPrepaymentById(orderPrepaymentId.longValue());
            List<ApprovalProcess> approvalProcesses = approvalProcessService.getApprovalProcessAll(null,poOrdersPrepayment.getOrders(),null,null,23);  //所在订单的审批流程
            List<ApprovalProcess> approvalProcesses1 = approvalProcessService.getApprovalProcesses(null,orderPrepaymentId,financePaymentId,1,63,64,65,66,67);  //出纳审批的审批流程
            approvalProcesses.addAll(approvalProcesses1);
            map.put("approvalProcesses",approvalProcesses);
        }else if (financePayment!=null&&financePayment.getBusinessType().equals("procurement")){
            Integer applicationId = financePayment.getBusiness();  //票款处理
            PoPaymentApplication poPaymentApplication = purchaseInvoiceService.getApplicationById(applicationId);
            List<ApprovalProcess> approvalProcesses = approvalProcessService.getApprovalProcesses(null,poPaymentApplication.getId(),financePaymentId,1,45,46,47,48,49,50,51,52,53);
            map.put("approvalProcesses",approvalProcesses);
        }
        map.put("financePayment",financePayment);  //付款信息
        return new JsonResult(1,map);
    }

    //---------------------------------1.231差额处理(1)-----------------------------------------------
    /**
     * <AUTHOR>
     * @Description 已付款/待复核查看列表
     * @Date 2022/11/22
     * @param  applicationId票据id   paymentId：预付款id   status 1-待复核 9-已付款  businessType：业务类型 procurement-票据处理 prepayment-预付款 overpayment-多收来的款
     * @return
     **/
    @ResponseBody
    @RequestMapping("/payDetail.do")
    public JsonResult payDetail(Integer applicationId,Integer orderPrepaymentId,String status){
        Map<String,Object> map = new HashMap<>();
        if (applicationId!=null||orderPrepaymentId!=null){
            map = purchaseInvoiceService.payDetail(applicationId,orderPrepaymentId,status,"");  //票据处理
            return new JsonResult(1,map);
        }else {
            return new JsonResult(new MyException("400", "参数传值错误！"));
        }
    }

    /**
    * 常规借款-报销时多付出去的款
    *@auther 李娅星
    *@date 2022/12/27
     * yearMonth 年月(yyyyMM)  finishType(1-已完结的 其他的均为未完成)
    */
    @ResponseBody
    @RequestMapping("/getMostDebitLoan.do")
    public JsonResult getMostDebitLoan(User user, String yearMonth, Integer finishType, PageInfo pageInfo){
        if (StringUtils.isEmpty(yearMonth)&&finishType!=null&&1==finishType){
            yearMonth = NewDateUtils.getYearMonth(new Date()).toString();
        }
        Map<String,Object> map = purchaseInvoiceService.getMostDebitLoan(user,yearMonth,finishType,pageInfo);
        return new JsonResult(1,map,pageInfo);
    }

    @ResponseBody
    @RequestMapping("/test.do")
    public JsonResult test(User user) {
//        List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcessByGroup(null,null,"113","2",50,51);
        Map<String,Object> map = new HashMap<>();
        Integer re = approvalProcessService.getApprovalProcessCounts(user.getUserID(), 48);;
        List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcessCounts1(user.getOid(),user.getUserID(),null,48);
        map.put("re",re);
        map.put("approvalProcessList",approvalProcessList);
//        map.put("approvalProcesses2",approvalProcessList);
        return new JsonResult(1,map);
    }
}
