<template>
	<!-- 注销工作室 -->
  <div class="cancellation">
    <Pageheader title="账  单" :showBack="true"/>
    <div>
      <div class="live-panel live-tr">
          最近一次缴费金额与缴费时间 <br>
          XX.XX元，XXXX年XX月XX日 XX:XX:XX
      </div>
      <div class="live-panel live-tr">
        该次缴费应服务的时间区间 <br>
        XXXX年XX月XX日——XXXX年XX月XX日
      </div>
      <div class="live-panel live-tr">
        该次缴费实际服务的时间区间 <br>
        XXXX年XX月XX日——XXXX年XX月XX日
      </div>
      <div class="live-panel live-tr">
        该次缴费实际服务时间区间发生的费用 <br>
        XX.XX元（本系统使用费用为每日9元）
      </div>
      <div class="live-panel live-tr">
        应退还金额 <br>
        XX.XX元
      </div>
      <div class="live-panel live-tr">
        ！您需在XXXX年XX月XX日XX:XX:XX前确定是否注销。<br>
        如“暂不注销，返回”，或过期不操作，则本次注销操作失效，且当日不可再次注销。本账号将继续计费。<br>
        如“确定注销”，您将无法再访问系统，以及将无法再见到您的数据。 <br>

        <div class="pad">
          <span @click="goPage('homeMy')" class="cancel live-button">暂不注销，返回</span>
          <span @click="cancellationFun()" class="primary live-button">确定注销</span>
        </div>
      </div>
    </div>

  </div>
</template>
<script setup>
import { useRouter } from "vue-router";
import { cancellationTeam, organizationList } from "@/api/api.js"
import { ElMessage } from 'element-plus'

const router = useRouter();
const goPage = (url) => {
  console.log('my to url=', url)
  router.push({name: url});
}
const cancellationFun = () => {
  cancellationTeam().then( res => {
    console.log('cancellationTeam res=', res)
    if(res.data == 1){
      localStorage.setItem('isChangeToken','0')
      goPage('mine')
    }else{
      ElMessage.error('操作失败！')
    }

  }).catch( err => {
    console.log('cancellationTeam res=', err)
  })
}

</script>


<script>
  //   import { cancellationTeam, organizationList } from "../../../api/api.js"
	// export default {
	// 	data() {
	// 		return {
  //               list:[]
	// 		}
	// 	},
	// 	methods: {
  //     cancellationOk() {
	// 			cancellationTeam().then((res) => {
	// 				console.log('cancellationOk', res)
  //                   let sta = res.data.data
	// 				// uni.hideLoading()
  //         //           if(sta){
	// 				// 	setTimeout(() => {
	// 				// 		uni.showToast({
	// 				// 			title: '您已成功注销该工作室！应退还金额将原路返回，请注意查收！',
	// 				// 			duration: 3000,
	// 				// 			icon:'none',
	// 				// 			success:function(){
	// 				// 				setTimeout(() => {
	// 				// 					organizationList().then((res) => {
	// 				// 						console.log('organizationList', res)
	// 				// 						let list = (res.data.organizationList) || []
	// 				// 						console.log('机构列表', list)
	// 				// 						uni.setStorageSync('officeList', list);
	// 				// 						uni.redirectTo({
	// 				// 							url: '/pages/redirectpage/redirectpage?path=/pages/my/privateSpace/mine/mine',
	// 				// 						});
	// 				// 					})
	// 				// 				}, 3000);
	// 				//
	// 				//
	// 				// 			}
	// 				// 		});
	// 				//
	// 				// 	}, 50);
  //         //
  //         //
  //         //   }else{
  //         //       uni.showToast({
  //         //           title: '操作失败！',
  //         //           duration: 2000,
  //         //           icon:none
  //         //       });
  //         //   }
  //
  //
	// 			}).catch((err) => {
	// 				console.log('cancellationOk err', res)
  //
	// 			})
	//
	// 		},
  //           cancellationNo(url) {
	// 			console.log(url);
	// 			// uni.navigateBack()
	// 		},
	// 	}
	// }
</script>

<style lang="scss"> 
.pad{
  padding-bottom: 30px;
}
</style>
