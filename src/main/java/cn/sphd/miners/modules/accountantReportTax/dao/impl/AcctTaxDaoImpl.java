package cn.sphd.miners.modules.accountantReportTax.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctReportDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTaxDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTaxPeriodDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTaxReportDao;
import cn.sphd.miners.modules.accountantReportTax.dto.AcctTaxDetailDto;
import cn.sphd.miners.modules.accountantReportTax.dto.AcctTaxDto;
import cn.sphd.miners.modules.accountantReportTax.entity.*;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Repository
public class AcctTaxDaoImpl extends BaseDao<AcctTaxEntity, Serializable> implements AcctTaxDao {

    @Autowired
    AcctTaxDao taxDao;
    @Autowired
    AcctReportDao reportDao;
    @Autowired
    AcctTaxPeriodDao taxPeriodDao;
    @Autowired
    AcctTaxReportDao taxReportDao;

    @Override
    public void setDisenabled(User user,Integer reportId) {
        String hql = "from AcctTaxReportEntity where report=:report and tax != null";
        HashMap<String, Object> param = new HashMap<>();
        param.put("report", reportId);
        List<AcctTaxReportEntity> list = taxReportDao.getListByHQLWithNamedParams(hql,param);
        for (AcctTaxReportEntity taxReportEntity : list) {
            Integer taxId = taxReportEntity.getTax();
            AcctTaxEntity taxEntity = taxDao.get(taxId);
            taxEntity.setEnabled(0);
            taxEntity.setEnabledTime(new Date());
            taxEntity.setUpdator(user.getUpdator());
            taxEntity.setUpdateName(user.getUpdateName());
            taxEntity.setUpdateDate(user.getUpdateDate());
        }
    }

    @Override
    public void updateHisTaxName(int taxId, String name) {
        String hql = "update AcctTaxHistoryEntity set name=:name where tax=:tax";
        HashMap<String, Object> param = new HashMap<>();
        param.put("name", name);
        param.put("tax", taxId);
        taxDao.queryHQLWithNamedParams(hql,param);
    }

    @Override
    public AcctTaxDetailDto getTaxDetailDto(User user, int taxId,int reportId) {
        AcctTaxDetailDto result = new AcctTaxDetailDto();
        AcctReportEntity reportEntity = reportDao.get(reportId);
        AcctTaxEntity taxEntity = taxDao.get(taxId);
//        String getPeriodsHql = "select period from AcctTaxPeriodEntity period,AcctReportEntity report where " +
//                "period.org=:org and period.report=:report";
        String getPeriodsHql = "from AcctTaxPeriodEntity period where " +
                "period.org=:org and period.report=:report";
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", user.getOid());
        param.put("report", reportEntity.getId());
        List<AcctTaxPeriodEntity> periodList = taxPeriodDao.getListByHQLWithNamedParams(getPeriodsHql,param);
        reportEntity.setPeriodList(periodList);//获取该报表对应的period集合
        result.setTaxEntity(taxEntity);
        result.setReportEntity(reportEntity);
        return result;
    }

    @Override
    public boolean checkTaxName(String name, Integer org) {
        String hql = "from AcctTaxEntity where org=:org and name=:name";
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", org);
        param.put("name", name);
        List<AcctTaxEntity> list = taxDao.getListByHQLWithNamedParams(hql,param);
        if (list.size() == 0) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public boolean isExsitSystemTax(Integer org) {
        String hql = "from AcctTaxEntity where org=:org and isSystem=:isSystem";
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", org);
        param.put("isSystem", 1);
        List<AcctTaxEntity> list = taxDao.getListByHQLWithNamedParams(hql,param);
        if (list.size() == 0) {
            return false;
        } else {
            return true;
        }
    }

    @Override
    public List<AcctTaxEntity> getListByOrg(Integer org) {
        String hql = "from AcctTaxEntity where org=:org and enabled=:enabled";
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", org);
        param.put("enabled", 1);
        List<AcctTaxEntity> list = taxDao.getListByHQLWithNamedParams(hql,param);
        return list;
    }

    /**
     * 返回税种ID
     * type;  类型:1-工资,2-所得税,3-社保,4-公积金
     *
     * */
    @Override
    public Integer getTaxId(byte type, Integer org) {
        String name = "";
        switch(type) {
            case 2:
                name = "个人所得税";
                break;
            case 3:
                name = "社保";
                break;
            case 4:
                name = "公积金";
                break;
        }

        String hql = "from AcctTaxEntity where org=:org and enabled=:enabled and name=:name";
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", org);
        param.put("enabled", 1);
        param.put("name", name);
        List<AcctTaxEntity> list = taxDao.getListByHQLWithNamedParams(hql,param);
        if (list.size() > 0) {
            return list.get(0).getId();
        }
        return null;
    }
}
