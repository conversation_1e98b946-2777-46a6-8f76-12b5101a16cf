package cn.sphd.miners.modules.accountant.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.ibatis.type.Alias;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * Created by root on 17-5-15.
 */
@Alias("settle")
public class TAccountantSettle {
    private Integer id;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    private Integer org;

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    private String period;

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    private String type;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    private String beginDate;

    public String getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(String beginDate) {
        this.beginDate = beginDate;
    }

    private String endDate;

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    private String settleDate;

    public String getSettleDate() {
        return settleDate;
    }

    public void setSettleDate(String settleDate) {
        this.settleDate = settleDate;
    }

    private String memo;

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    private Integer creator;

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    private String createName;

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    private String createDate;

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    private Integer updator;

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    private String updateName;

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    private String updateDate;

    public String getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }

    private Integer approveItem;

    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    private String approveStatus;

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    private Integer approveLevel;

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    private Integer auditor;

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    private String auditorName;

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    private String auditDate;

    public String getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(String auditDate) {
        this.auditDate = auditDate;
    }

    private String operation;

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    private String applyMemo;

    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    private String approveMemo;

    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    private Integer messageId;

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    private String state;
    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    private String needTrial;
    public String getNeedTrial() {
        return needTrial;
    }

    public void setNeedTrial(String needTrial) {
        this.needTrial = needTrial;
    }


    public boolean isSettled() {
        return settled;
    }

    public void setSettled(boolean settled) {
        this.settled = settled;
    }

    private boolean settled;//是否结账

    private String taxState;//报税状态,，0-未报（默认）,已报

    private Date taxDate;//报税日期
    private String superState;//报送超管状态,，0-不需要（默认）,1-需要

    public String getTaxState() {
        return taxState;
    }

    public void setTaxState(String taxState) {
        this.taxState = taxState;
    }

    public Date getTaxDate() {
        return taxDate;
    }

    public void setTaxDate(Date taxDate) {
        this.taxDate = taxDate;
    }

    public String getSuperState() {
        return superState;
    }

    public void setSuperState(String superState) {
        this.superState = superState;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        TAccountantSettle settle = (TAccountantSettle) o;

        if (id != null ? !id.equals(settle.id) : settle.id != null) return false;
        if (org != null ? !org.equals(settle.org) : settle.org != null) return false;
        if (period != null ? !period.equals(settle.period) : settle.period != null) return false;
        if (type != null ? !type.equals(settle.type) : settle.type != null) return false;
        if (beginDate != null ? !beginDate.equals(settle.beginDate) : settle.beginDate != null) return false;
        if (endDate != null ? !endDate.equals(settle.endDate) : settle.endDate != null) return false;
        if (settleDate != null ? !settleDate.equals(settle.settleDate) : settle.settleDate != null) return false;
        if (memo != null ? !memo.equals(settle.memo) : settle.memo != null) return false;
        if (creator != null ? !creator.equals(settle.creator) : settle.creator != null) return false;
        if (createName != null ? !createName.equals(settle.createName) : settle.createName != null) return false;
        if (createDate != null ? !createDate.equals(settle.createDate) : settle.createDate != null) return false;
        if (updator != null ? !updator.equals(settle.updator) : settle.updator != null) return false;
        if (updateName != null ? !updateName.equals(settle.updateName) : settle.updateName != null) return false;
        if (updateDate != null ? !updateDate.equals(settle.updateDate) : settle.updateDate != null) return false;
        if (approveItem != null ? !approveItem.equals(settle.approveItem) : settle.approveItem != null) return false;
        if (approveStatus != null ? !approveStatus.equals(settle.approveStatus) : settle.approveStatus != null)
            return false;
        if (approveLevel != null ? !approveLevel.equals(settle.approveLevel) : settle.approveLevel != null)
            return false;
        if (auditor != null ? !auditor.equals(settle.auditor) : settle.auditor != null) return false;
        if (auditorName != null ? !auditorName.equals(settle.auditorName) : settle.auditorName != null) return false;
        if (auditDate != null ? !auditDate.equals(settle.auditDate) : settle.auditDate != null) return false;
        if (operation != null ? !operation.equals(settle.operation) : settle.operation != null) return false;
        if (applyMemo != null ? !applyMemo.equals(settle.applyMemo) : settle.applyMemo != null) return false;
        if (approveMemo != null ? !approveMemo.equals(settle.approveMemo) : settle.approveMemo != null) return false;
        if (messageId != null ? !messageId.equals(settle.messageId) : settle.messageId != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (org != null ? org.hashCode() : 0);
        result = 31 * result + (period != null ? period.hashCode() : 0);
        result = 31 * result + (type != null ? type.hashCode() : 0);
        result = 31 * result + (beginDate != null ? beginDate.hashCode() : 0);
        result = 31 * result + (endDate != null ? endDate.hashCode() : 0);
        result = 31 * result + (settleDate != null ? settleDate.hashCode() : 0);
        result = 31 * result + (memo != null ? memo.hashCode() : 0);
        result = 31 * result + (creator != null ? creator.hashCode() : 0);
        result = 31 * result + (createName != null ? createName.hashCode() : 0);
        result = 31 * result + (createDate != null ? createDate.hashCode() : 0);
        result = 31 * result + (updator != null ? updator.hashCode() : 0);
        result = 31 * result + (updateName != null ? updateName.hashCode() : 0);
        result = 31 * result + (updateDate != null ? updateDate.hashCode() : 0);
        result = 31 * result + (approveItem != null ? approveItem.hashCode() : 0);
        result = 31 * result + (approveStatus != null ? approveStatus.hashCode() : 0);
        result = 31 * result + (approveLevel != null ? approveLevel.hashCode() : 0);
        result = 31 * result + (auditor != null ? auditor.hashCode() : 0);
        result = 31 * result + (auditorName != null ? auditorName.hashCode() : 0);
        result = 31 * result + (auditDate != null ? auditDate.hashCode() : 0);
        result = 31 * result + (operation != null ? operation.hashCode() : 0);
        result = 31 * result + (applyMemo != null ? applyMemo.hashCode() : 0);
        result = 31 * result + (approveMemo != null ? approveMemo.hashCode() : 0);
        result = 31 * result + (messageId != null ? messageId.hashCode() : 0);
        return result;
    }
}
