package cn.sphd.miners.modules.accountant.entity;

import org.apache.ibatis.type.Alias;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created by 刘洪涛 on 17-3-23.
 */
@Alias("voucherDto")
public class TAccountantVoucherDto implements Serializable {
    private static final long serialVersionUID = 1L;

    public TAccountantVoucherDto() { }

    private Integer id;
    private Integer org;
    private Integer accountMonth;//凭证月份
    private Date billDate;//票据日期
    private Integer voucher;
    private String belongPeroid;
    private String type;
    private String checkNo;
    private Byte isSettled;
    private Byte isAccount;
    private String reason;
    private Integer billDetail;
    private String summary;
    private String operatorName;
    private String operatorFinance;
    private String createName;
    @DateTimeFormat(pattern="yyyy-MM")
    private String createDate;
    private Integer auditor;
    private String auditorName;//审核人
    private Date auditDate;//审核时间
    private String approveStatus;
    private String source;
    private Integer billPeriod;
    private Integer detailId;//对应财务帐务明细id
    private String kind;//财务类型:1-detail 2-bill
    private Date addtime;
    private String acountDate;
    private List<TAccountantVoucherSubject> borrowSubject;
    private List<TAccountantVoucherSubject> loanSubject;
    private String financeCreateName;//财务票据的创建人名称
    private Date financeCreateDate;//财务票据的创建人时间

    public String getAcountDate() {
        return acountDate;
    }

    public void setAcountDate(String acountDate) {
        this.acountDate = acountDate;
    }

    public String getFinanceCreateName() {
        return financeCreateName;
    }

    public void setFinanceCreateName(String financeCreateName) {
        this.financeCreateName = financeCreateName;
    }

    public Date getFinanceCreateDate() {
        return financeCreateDate;
    }

    public void setFinanceCreateDate(Date financeCreateDate) {
        this.financeCreateDate = financeCreateDate;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }
    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }
    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }
    public List<TAccountantVoucherSubject> getBorrowSubject() {
        return borrowSubject;
    }

    public void setBorrowSubject(List<TAccountantVoucherSubject> borrowSubject) {
        this.borrowSubject = borrowSubject;
    }

    public List<TAccountantVoucherSubject> getLoanSubject() {
        return loanSubject;
    }

    public void setLoanSubject(List<TAccountantVoucherSubject> loanSubject) {
        this.loanSubject = loanSubject;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getBillDate() {
        return billDate;
    }

    public void setBillDate(Date billDate) {
        this.billDate = billDate;
    }

    public void setAccountMonth(Integer accountMonth) {
        this.accountMonth = accountMonth;
    }

    public Integer getAccountMonth() {
        return accountMonth;
    }

    public Integer getVoucher() {
        return voucher;
    }

    public void setVoucher(Integer voucher) {
        this.voucher = voucher;
    }

    public String getBelongPeroid() {
        return belongPeroid;
    }

    public void setBelongPeroid(String belongPeroid) {
        this.belongPeroid = belongPeroid;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCheckNo() {
        return checkNo;
    }

    public void setCheckNo(String checkNo) {
        this.checkNo = checkNo;
    }

    public Byte getIsSettled() {
        return isSettled;
    }

    public void setIsSettled(Byte isSettled) {
        this.isSettled = isSettled;
    }

    public Byte getIsAccount() {
        return isAccount;
    }

    public void setIsAccount(Byte isAccount) {
        this.isAccount = isAccount;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Integer getBillDetail() {
        return billDetail;
    }

    public void setBillDetail(Integer billDetail) {
        this.billDetail = billDetail;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getOperatorFinance() {
        return operatorFinance;
    }

    public void setOperatorFinance(String operatorFinance) {
        this.operatorFinance = operatorFinance;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Integer getBillPeriod() {
        return billPeriod;
    }

    public void setBillPeriod(Integer billPeriod) {
        this.billPeriod = billPeriod;
    }

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    public String getKind() {
        return kind;
    }

    public void setKind(String kind) {
        this.kind = kind;
    }

    public Date getAddtime() {
        return addtime;
    }

    public void setAddtime(Date addtime) {
        this.addtime = addtime;
    }

}
