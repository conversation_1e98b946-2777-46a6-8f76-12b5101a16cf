package cn.sphd.miners.modules.accountant.entity;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by khmsoft on 2017/5/20.
 */
public class recordedData implements Serializable {

    //资产负债表中各项的期末余额
    private BigDecimal r2c3;
    private BigDecimal r3c3;
    private BigDecimal r4c3;
    private BigDecimal r5c3;
    private BigDecimal r6c3;
    private BigDecimal r7c3;
    private BigDecimal r8c3;
    private BigDecimal r9c3;
    private BigDecimal r10c3;
    private BigDecimal r11c3;
    private BigDecimal r12c3;
    private BigDecimal r13c3;
    private BigDecimal r14c3;
    private BigDecimal r15c3;
    private BigDecimal r16c3;
    private BigDecimal r18c3;
    private BigDecimal r19c3;
    private BigDecimal r20c3;
    private BigDecimal r21c3;
    private BigDecimal r22c3;
    private BigDecimal r23c3;
    private BigDecimal r24c3;
    private BigDecimal r25c3;
    private BigDecimal r26c3;
    private BigDecimal r27c3;
    private BigDecimal r28c3;
    private BigDecimal r29c3;
    private BigDecimal r30c3;
    private BigDecimal r31c3;
    private BigDecimal r32c3;

    private BigDecimal r1c2;
    private BigDecimal r2c2;
    private BigDecimal r3c2;
    private BigDecimal r4c2;
    private BigDecimal r5c2;
    private BigDecimal r6c2;
    private BigDecimal r7c2;
    private BigDecimal r8c2;
    private BigDecimal r9c2;
    private BigDecimal r10c2;
    private BigDecimal r11c2;
    private BigDecimal r12c2;
    private BigDecimal r13c2;
    private BigDecimal r14c2;
    private BigDecimal r15c2;
    private BigDecimal r16c2;
    private BigDecimal r17c2;
    private BigDecimal r18c2;
    private BigDecimal r19c2;
    private BigDecimal r20c2;
    private BigDecimal r21c2;
    private BigDecimal r22c2;
    private BigDecimal r23c2;
    private BigDecimal r24c2;
    private BigDecimal r25c2;
    private BigDecimal r26c2;
    private BigDecimal r27c2;
    private BigDecimal r28c2;
    private BigDecimal r29c2;
    private BigDecimal r30c2;
    private BigDecimal r31c2;
    private BigDecimal r32c2;

    private BigDecimal r2c7;
    private BigDecimal r3c7;
    private BigDecimal r4c7;
    private BigDecimal r5c7;
    private BigDecimal r6c7;
    private BigDecimal r7c7;
    private BigDecimal r8c7;
    private BigDecimal r9c7;
    private BigDecimal r10c7;
    private BigDecimal r11c7;
    private BigDecimal r12c7;
    private BigDecimal r13c7;
    private BigDecimal r14c7;
    private BigDecimal r15c7;
    private BigDecimal r16c7;
    private BigDecimal r17c7;
    private BigDecimal r18c7;
    private BigDecimal r19c7;
    private BigDecimal r27c7;
    private BigDecimal r28c7;
    private BigDecimal r29c7;
    private BigDecimal r30c7;
    private BigDecimal r31c7;
    private BigDecimal r32c7;

    //资产负债表的各项年初余额
    private BigDecimal r2c3BeginningBalance;
    private BigDecimal r3c3BeginningBalance;
    private BigDecimal r4c3BeginningBalance;
    private BigDecimal r5c3BeginningBalance;
    private BigDecimal r6c3BeginningBalance;
    private BigDecimal r7c3BeginningBalance;
    private BigDecimal r8c3BeginningBalance;
    private BigDecimal r9c3BeginningBalance;
    private BigDecimal r10c3BeginningBalance;
    private BigDecimal r11c3BeginningBalance;
    private BigDecimal r12c3BeginningBalance;
    private BigDecimal r13c3BeginningBalance;
    private BigDecimal r14c3BeginningBalance;
    private BigDecimal r15c3BeginningBalance;
    private BigDecimal r16c3BeginningBalance;
    private BigDecimal r18c3BeginningBalance;
    private BigDecimal r19c3BeginningBalance;
    private BigDecimal r20c3BeginningBalance;
    private BigDecimal r21c3BeginningBalance;
    private BigDecimal r22c3BeginningBalance;
    private BigDecimal r23c3BeginningBalance;
    private BigDecimal r24c3BeginningBalance;
    private BigDecimal r25c3BeginningBalance;
    private BigDecimal r26c3BeginningBalance;
    private BigDecimal r27c3BeginningBalance;
    private BigDecimal r28c3BeginningBalance;
    private BigDecimal r29c3BeginningBalance;
    private BigDecimal r30c3BeginningBalance;
    private BigDecimal r31c3BeginningBalance;
    private BigDecimal r32c3BeginningBalance;

    private BigDecimal r1c2BeginningBalance;
    private BigDecimal r2c2BeginningBalance;
    private BigDecimal r3c2BeginningBalance;
    private BigDecimal r4c2BeginningBalance;
    private BigDecimal r5c2BeginningBalance;
    private BigDecimal r6c2BeginningBalance;
    private BigDecimal r7c2BeginningBalance;
    private BigDecimal r8c2BeginningBalance;
    private BigDecimal r9c2BeginningBalance;
    private BigDecimal r10c2BeginningBalance;
    private BigDecimal r11c2BeginningBalance;
    private BigDecimal r12c2BeginningBalance;
    private BigDecimal r13c2BeginningBalance;
    private BigDecimal r14c2BeginningBalance;
    private BigDecimal r15c2BeginningBalance;
    private BigDecimal r16c2BeginningBalance;
    private BigDecimal r17c2BeginningBalance;
    private BigDecimal r18c2BeginningBalance;
    private BigDecimal r19c2BeginningBalance;
    private BigDecimal r20c2BeginningBalance;
    private BigDecimal r21c2BeginningBalance;
    private BigDecimal r22c2BeginningBalance;
    private BigDecimal r23c2BeginningBalance;
    private BigDecimal r24c2BeginningBalance;
    private BigDecimal r25c2BeginningBalance;
    private BigDecimal r26c2BeginningBalance;
    private BigDecimal r27c2BeginningBalance;
    private BigDecimal r28c2BeginningBalance;
    private BigDecimal r29c2BeginningBalance;
    private BigDecimal r30c2BeginningBalance;
    private BigDecimal r31c2BeginningBalance;
    private BigDecimal r32c2BeginningBalance;

    private BigDecimal r2c7BeginningBalance;
    private BigDecimal r3c7BeginningBalance;
    private BigDecimal r4c7BeginningBalance;
    private BigDecimal r5c7BeginningBalance;
    private BigDecimal r6c7BeginningBalance;
    private BigDecimal r7c7BeginningBalance;
    private BigDecimal r8c7BeginningBalance;
    private BigDecimal r9c7BeginningBalance;
    private BigDecimal r10c7BeginningBalance;
    private BigDecimal r11c7BeginningBalance;
    private BigDecimal r12c7BeginningBalance;
    private BigDecimal r13c7BeginningBalance;
    private BigDecimal r14c7BeginningBalance;
    private BigDecimal r15c7BeginningBalance;
    private BigDecimal r16c7BeginningBalance;
    private BigDecimal r17c7BeginningBalance;
    private BigDecimal r18c7BeginningBalance;
    private BigDecimal r19c7BeginningBalance;
    private BigDecimal r27c7BeginningBalance;
    private BigDecimal r28c7BeginningBalance;
    private BigDecimal r29c7BeginningBalance;
    private BigDecimal r30c7BeginningBalance;
    private BigDecimal r31c7BeginningBalance;
    private BigDecimal r32c7BeginningBalance;

    public BigDecimal getR2c3BeginningBalance() {
        return r2c3BeginningBalance;
    }

    public void setR2c3BeginningBalance(BigDecimal r2c3BeginningBalance) {
        this.r2c3BeginningBalance = r2c3BeginningBalance;
    }

    public BigDecimal getR3c3BeginningBalance() {
        return r3c3BeginningBalance;
    }

    public void setR3c3BeginningBalance(BigDecimal r3c3BeginningBalance) {
        this.r3c3BeginningBalance = r3c3BeginningBalance;
    }

    public BigDecimal getR4c3BeginningBalance() {
        return r4c3BeginningBalance;
    }

    public void setR4c3BeginningBalance(BigDecimal r4c3BeginningBalance) {
        this.r4c3BeginningBalance = r4c3BeginningBalance;
    }

    public BigDecimal getR5c3BeginningBalance() {
        return r5c3BeginningBalance;
    }

    public void setR5c3BeginningBalance(BigDecimal r5c3BeginningBalance) {
        this.r5c3BeginningBalance = r5c3BeginningBalance;
    }

    public BigDecimal getR6c3BeginningBalance() {
        return r6c3BeginningBalance;
    }

    public void setR6c3BeginningBalance(BigDecimal r6c3BeginningBalance) {
        this.r6c3BeginningBalance = r6c3BeginningBalance;
    }

    public BigDecimal getR7c3BeginningBalance() {
        return r7c3BeginningBalance;
    }

    public void setR7c3BeginningBalance(BigDecimal r7c3BeginningBalance) {
        this.r7c3BeginningBalance = r7c3BeginningBalance;
    }

    public BigDecimal getR8c3BeginningBalance() {
        return r8c3BeginningBalance;
    }

    public void setR8c3BeginningBalance(BigDecimal r8c3BeginningBalance) {
        this.r8c3BeginningBalance = r8c3BeginningBalance;
    }

    public BigDecimal getR9c3BeginningBalance() {
        return r9c3BeginningBalance;
    }

    public void setR9c3BeginningBalance(BigDecimal r9c3BeginningBalance) {
        this.r9c3BeginningBalance = r9c3BeginningBalance;
    }

    public BigDecimal getR10c3BeginningBalance() {
        return r10c3BeginningBalance;
    }

    public void setR10c3BeginningBalance(BigDecimal r10c3BeginningBalance) {
        this.r10c3BeginningBalance = r10c3BeginningBalance;
    }

    public BigDecimal getR11c3BeginningBalance() {
        return r11c3BeginningBalance;
    }

    public void setR11c3BeginningBalance(BigDecimal r11c3BeginningBalance) {
        this.r11c3BeginningBalance = r11c3BeginningBalance;
    }

    public BigDecimal getR12c3BeginningBalance() {
        return r12c3BeginningBalance;
    }

    public void setR12c3BeginningBalance(BigDecimal r12c3BeginningBalance) {
        this.r12c3BeginningBalance = r12c3BeginningBalance;
    }

    public BigDecimal getR13c3BeginningBalance() {
        return r13c3BeginningBalance;
    }

    public void setR13c3BeginningBalance(BigDecimal r13c3BeginningBalance) {
        this.r13c3BeginningBalance = r13c3BeginningBalance;
    }

    public BigDecimal getR14c3BeginningBalance() {
        return r14c3BeginningBalance;
    }

    public void setR14c3BeginningBalance(BigDecimal r14c3BeginningBalance) {
        this.r14c3BeginningBalance = r14c3BeginningBalance;
    }

    public BigDecimal getR15c3BeginningBalance() {
        return r15c3BeginningBalance;
    }

    public void setR15c3BeginningBalance(BigDecimal r15c3BeginningBalance) {
        this.r15c3BeginningBalance = r15c3BeginningBalance;
    }

    public BigDecimal getR16c3BeginningBalance() {
        return r16c3BeginningBalance;
    }

    public void setR16c3BeginningBalance(BigDecimal r16c3BeginningBalance) {
        this.r16c3BeginningBalance = r16c3BeginningBalance;
    }

    public BigDecimal getR18c3BeginningBalance() {
        return r18c3BeginningBalance;
    }

    public void setR18c3BeginningBalance(BigDecimal r18c3BeginningBalance) {
        this.r18c3BeginningBalance = r18c3BeginningBalance;
    }

    public BigDecimal getR19c3BeginningBalance() {
        return r19c3BeginningBalance;
    }

    public void setR19c3BeginningBalance(BigDecimal r19c3BeginningBalance) {
        this.r19c3BeginningBalance = r19c3BeginningBalance;
    }

    public BigDecimal getR20c3BeginningBalance() {
        return r20c3BeginningBalance;
    }

    public void setR20c3BeginningBalance(BigDecimal r20c3BeginningBalance) {
        this.r20c3BeginningBalance = r20c3BeginningBalance;
    }

    public BigDecimal getR21c3BeginningBalance() {
        return r21c3BeginningBalance;
    }

    public void setR21c3BeginningBalance(BigDecimal r21c3BeginningBalance) {
        this.r21c3BeginningBalance = r21c3BeginningBalance;
    }

    public BigDecimal getR22c3BeginningBalance() {
        return r22c3BeginningBalance;
    }

    public void setR22c3BeginningBalance(BigDecimal r22c3BeginningBalance) {
        this.r22c3BeginningBalance = r22c3BeginningBalance;
    }

    public BigDecimal getR23c3BeginningBalance() {
        return r23c3BeginningBalance;
    }

    public void setR23c3BeginningBalance(BigDecimal r23c3BeginningBalance) {
        this.r23c3BeginningBalance = r23c3BeginningBalance;
    }

    public BigDecimal getR24c3BeginningBalance() {
        return r24c3BeginningBalance;
    }

    public void setR24c3BeginningBalance(BigDecimal r24c3BeginningBalance) {
        this.r24c3BeginningBalance = r24c3BeginningBalance;
    }

    public BigDecimal getR25c3BeginningBalance() {
        return r25c3BeginningBalance;
    }

    public void setR25c3BeginningBalance(BigDecimal r25c3BeginningBalance) {
        this.r25c3BeginningBalance = r25c3BeginningBalance;
    }

    public BigDecimal getR26c3BeginningBalance() {
        return r26c3BeginningBalance;
    }

    public void setR26c3BeginningBalance(BigDecimal r26c3BeginningBalance) {
        this.r26c3BeginningBalance = r26c3BeginningBalance;
    }

    public BigDecimal getR27c3BeginningBalance() {
        return r27c3BeginningBalance;
    }

    public void setR27c3BeginningBalance(BigDecimal r27c3BeginningBalance) {
        this.r27c3BeginningBalance = r27c3BeginningBalance;
    }

    public BigDecimal getR28c3BeginningBalance() {
        return r28c3BeginningBalance;
    }

    public void setR28c3BeginningBalance(BigDecimal r28c3BeginningBalance) {
        this.r28c3BeginningBalance = r28c3BeginningBalance;
    }

    public BigDecimal getR29c3BeginningBalance() {
        return r29c3BeginningBalance;
    }

    public void setR29c3BeginningBalance(BigDecimal r29c3BeginningBalance) {
        this.r29c3BeginningBalance = r29c3BeginningBalance;
    }

    public BigDecimal getR30c3BeginningBalance() {
        return r30c3BeginningBalance;
    }

    public void setR30c3BeginningBalance(BigDecimal r30c3BeginningBalance) {
        this.r30c3BeginningBalance = r30c3BeginningBalance;
    }

    public BigDecimal getR31c3BeginningBalance() {
        return r31c3BeginningBalance;
    }

    public void setR31c3BeginningBalance(BigDecimal r31c3BeginningBalance) {
        this.r31c3BeginningBalance = r31c3BeginningBalance;
    }

    public BigDecimal getR32c3BeginningBalance() {
        return r32c3BeginningBalance;
    }

    public void setR32c3BeginningBalance(BigDecimal r32c3BeginningBalance) {
        this.r32c3BeginningBalance = r32c3BeginningBalance;
    }

    public BigDecimal getR1c2BeginningBalance() {
        return r1c2BeginningBalance;
    }

    public void setR1c2BeginningBalance(BigDecimal r1c2BeginningBalance) {
        this.r1c2BeginningBalance = r1c2BeginningBalance;
    }

    public BigDecimal getR2c2BeginningBalance() {
        return r2c2BeginningBalance;
    }

    public void setR2c2BeginningBalance(BigDecimal r2c2BeginningBalance) {
        this.r2c2BeginningBalance = r2c2BeginningBalance;
    }

    public BigDecimal getR3c2BeginningBalance() {
        return r3c2BeginningBalance;
    }

    public void setR3c2BeginningBalance(BigDecimal r3c2BeginningBalance) {
        this.r3c2BeginningBalance = r3c2BeginningBalance;
    }

    public BigDecimal getR4c2BeginningBalance() {
        return r4c2BeginningBalance;
    }

    public void setR4c2BeginningBalance(BigDecimal r4c2BeginningBalance) {
        this.r4c2BeginningBalance = r4c2BeginningBalance;
    }

    public BigDecimal getR5c2BeginningBalance() {
        return r5c2BeginningBalance;
    }

    public void setR5c2BeginningBalance(BigDecimal r5c2BeginningBalance) {
        this.r5c2BeginningBalance = r5c2BeginningBalance;
    }

    public BigDecimal getR6c2BeginningBalance() {
        return r6c2BeginningBalance;
    }

    public void setR6c2BeginningBalance(BigDecimal r6c2BeginningBalance) {
        this.r6c2BeginningBalance = r6c2BeginningBalance;
    }

    public BigDecimal getR7c2BeginningBalance() {
        return r7c2BeginningBalance;
    }

    public void setR7c2BeginningBalance(BigDecimal r7c2BeginningBalance) {
        this.r7c2BeginningBalance = r7c2BeginningBalance;
    }

    public BigDecimal getR8c2BeginningBalance() {
        return r8c2BeginningBalance;
    }

    public void setR8c2BeginningBalance(BigDecimal r8c2BeginningBalance) {
        this.r8c2BeginningBalance = r8c2BeginningBalance;
    }

    public BigDecimal getR9c2BeginningBalance() {
        return r9c2BeginningBalance;
    }

    public void setR9c2BeginningBalance(BigDecimal r9c2BeginningBalance) {
        this.r9c2BeginningBalance = r9c2BeginningBalance;
    }

    public BigDecimal getR10c2BeginningBalance() {
        return r10c2BeginningBalance;
    }

    public void setR10c2BeginningBalance(BigDecimal r10c2BeginningBalance) {
        this.r10c2BeginningBalance = r10c2BeginningBalance;
    }

    public BigDecimal getR11c2BeginningBalance() {
        return r11c2BeginningBalance;
    }

    public void setR11c2BeginningBalance(BigDecimal r11c2BeginningBalance) {
        this.r11c2BeginningBalance = r11c2BeginningBalance;
    }

    public BigDecimal getR12c2BeginningBalance() {
        return r12c2BeginningBalance;
    }

    public void setR12c2BeginningBalance(BigDecimal r12c2BeginningBalance) {
        this.r12c2BeginningBalance = r12c2BeginningBalance;
    }

    public BigDecimal getR13c2BeginningBalance() {
        return r13c2BeginningBalance;
    }

    public void setR13c2BeginningBalance(BigDecimal r13c2BeginningBalance) {
        this.r13c2BeginningBalance = r13c2BeginningBalance;
    }

    public BigDecimal getR14c2BeginningBalance() {
        return r14c2BeginningBalance;
    }

    public void setR14c2BeginningBalance(BigDecimal r14c2BeginningBalance) {
        this.r14c2BeginningBalance = r14c2BeginningBalance;
    }

    public BigDecimal getR15c2BeginningBalance() {
        return r15c2BeginningBalance;
    }

    public void setR15c2BeginningBalance(BigDecimal r15c2BeginningBalance) {
        this.r15c2BeginningBalance = r15c2BeginningBalance;
    }

    public BigDecimal getR16c2BeginningBalance() {
        return r16c2BeginningBalance;
    }

    public void setR16c2BeginningBalance(BigDecimal r16c2BeginningBalance) {
        this.r16c2BeginningBalance = r16c2BeginningBalance;
    }

    public BigDecimal getR17c2BeginningBalance() {
        return r17c2BeginningBalance;
    }

    public void setR17c2BeginningBalance(BigDecimal r17c2BeginningBalance) {
        this.r17c2BeginningBalance = r17c2BeginningBalance;
    }

    public BigDecimal getR18c2BeginningBalance() {
        return r18c2BeginningBalance;
    }

    public void setR18c2BeginningBalance(BigDecimal r18c2BeginningBalance) {
        this.r18c2BeginningBalance = r18c2BeginningBalance;
    }

    public BigDecimal getR19c2BeginningBalance() {
        return r19c2BeginningBalance;
    }

    public void setR19c2BeginningBalance(BigDecimal r19c2BeginningBalance) {
        this.r19c2BeginningBalance = r19c2BeginningBalance;
    }

    public BigDecimal getR20c2BeginningBalance() {
        return r20c2BeginningBalance;
    }

    public void setR20c2BeginningBalance(BigDecimal r20c2BeginningBalance) {
        this.r20c2BeginningBalance = r20c2BeginningBalance;
    }

    public BigDecimal getR21c2BeginningBalance() {
        return r21c2BeginningBalance;
    }

    public void setR21c2BeginningBalance(BigDecimal r21c2BeginningBalance) {
        this.r21c2BeginningBalance = r21c2BeginningBalance;
    }

    public BigDecimal getR22c2BeginningBalance() {
        return r22c2BeginningBalance;
    }

    public void setR22c2BeginningBalance(BigDecimal r22c2BeginningBalance) {
        this.r22c2BeginningBalance = r22c2BeginningBalance;
    }

    public BigDecimal getR23c2BeginningBalance() {
        return r23c2BeginningBalance;
    }

    public void setR23c2BeginningBalance(BigDecimal r23c2BeginningBalance) {
        this.r23c2BeginningBalance = r23c2BeginningBalance;
    }

    public BigDecimal getR24c2BeginningBalance() {
        return r24c2BeginningBalance;
    }

    public void setR24c2BeginningBalance(BigDecimal r24c2BeginningBalance) {
        this.r24c2BeginningBalance = r24c2BeginningBalance;
    }

    public BigDecimal getR25c2BeginningBalance() {
        return r25c2BeginningBalance;
    }

    public void setR25c2BeginningBalance(BigDecimal r25c2BeginningBalance) {
        this.r25c2BeginningBalance = r25c2BeginningBalance;
    }

    public BigDecimal getR26c2BeginningBalance() {
        return r26c2BeginningBalance;
    }

    public void setR26c2BeginningBalance(BigDecimal r26c2BeginningBalance) {
        this.r26c2BeginningBalance = r26c2BeginningBalance;
    }

    public BigDecimal getR27c2BeginningBalance() {
        return r27c2BeginningBalance;
    }

    public void setR27c2BeginningBalance(BigDecimal r27c2BeginningBalance) {
        this.r27c2BeginningBalance = r27c2BeginningBalance;
    }

    public BigDecimal getR28c2BeginningBalance() {
        return r28c2BeginningBalance;
    }

    public void setR28c2BeginningBalance(BigDecimal r28c2BeginningBalance) {
        this.r28c2BeginningBalance = r28c2BeginningBalance;
    }

    public BigDecimal getR29c2BeginningBalance() {
        return r29c2BeginningBalance;
    }

    public void setR29c2BeginningBalance(BigDecimal r29c2BeginningBalance) {
        this.r29c2BeginningBalance = r29c2BeginningBalance;
    }

    public BigDecimal getR30c2BeginningBalance() {
        return r30c2BeginningBalance;
    }

    public void setR30c2BeginningBalance(BigDecimal r30c2BeginningBalance) {
        this.r30c2BeginningBalance = r30c2BeginningBalance;
    }

    public BigDecimal getR31c2BeginningBalance() {
        return r31c2BeginningBalance;
    }

    public void setR31c2BeginningBalance(BigDecimal r31c2BeginningBalance) {
        this.r31c2BeginningBalance = r31c2BeginningBalance;
    }

    public BigDecimal getR32c2BeginningBalance() {
        return r32c2BeginningBalance;
    }

    public void setR32c2BeginningBalance(BigDecimal r32c2BeginningBalance) {
        this.r32c2BeginningBalance = r32c2BeginningBalance;
    }

    public BigDecimal getR2c7BeginningBalance() {
        return r2c7BeginningBalance;
    }

    public void setR2c7BeginningBalance(BigDecimal r2c7BeginningBalance) {
        this.r2c7BeginningBalance = r2c7BeginningBalance;
    }

    public BigDecimal getR3c7BeginningBalance() {
        return r3c7BeginningBalance;
    }

    public void setR3c7BeginningBalance(BigDecimal r3c7BeginningBalance) {
        this.r3c7BeginningBalance = r3c7BeginningBalance;
    }

    public BigDecimal getR4c7BeginningBalance() {
        return r4c7BeginningBalance;
    }

    public void setR4c7BeginningBalance(BigDecimal r4c7BeginningBalance) {
        this.r4c7BeginningBalance = r4c7BeginningBalance;
    }

    public BigDecimal getR5c7BeginningBalance() {
        return r5c7BeginningBalance;
    }

    public void setR5c7BeginningBalance(BigDecimal r5c7BeginningBalance) {
        this.r5c7BeginningBalance = r5c7BeginningBalance;
    }

    public BigDecimal getR6c7BeginningBalance() {
        return r6c7BeginningBalance;
    }

    public void setR6c7BeginningBalance(BigDecimal r6c7BeginningBalance) {
        this.r6c7BeginningBalance = r6c7BeginningBalance;
    }

    public BigDecimal getR7c7BeginningBalance() {
        return r7c7BeginningBalance;
    }

    public void setR7c7BeginningBalance(BigDecimal r7c7BeginningBalance) {
        this.r7c7BeginningBalance = r7c7BeginningBalance;
    }

    public BigDecimal getR8c7BeginningBalance() {
        return r8c7BeginningBalance;
    }

    public void setR8c7BeginningBalance(BigDecimal r8c7BeginningBalance) {
        this.r8c7BeginningBalance = r8c7BeginningBalance;
    }

    public BigDecimal getR9c7BeginningBalance() {
        return r9c7BeginningBalance;
    }

    public void setR9c7BeginningBalance(BigDecimal r9c7BeginningBalance) {
        this.r9c7BeginningBalance = r9c7BeginningBalance;
    }

    public BigDecimal getR10c7BeginningBalance() {
        return r10c7BeginningBalance;
    }

    public void setR10c7BeginningBalance(BigDecimal r10c7BeginningBalance) {
        this.r10c7BeginningBalance = r10c7BeginningBalance;
    }

    public BigDecimal getR11c7BeginningBalance() {
        return r11c7BeginningBalance;
    }

    public void setR11c7BeginningBalance(BigDecimal r11c7BeginningBalance) {
        this.r11c7BeginningBalance = r11c7BeginningBalance;
    }

    public BigDecimal getR12c7BeginningBalance() {
        return r12c7BeginningBalance;
    }

    public void setR12c7BeginningBalance(BigDecimal r12c7BeginningBalance) {
        this.r12c7BeginningBalance = r12c7BeginningBalance;
    }

    public BigDecimal getR13c7BeginningBalance() {
        return r13c7BeginningBalance;
    }

    public void setR13c7BeginningBalance(BigDecimal r13c7BeginningBalance) {
        this.r13c7BeginningBalance = r13c7BeginningBalance;
    }

    public BigDecimal getR14c7BeginningBalance() {
        return r14c7BeginningBalance;
    }

    public void setR14c7BeginningBalance(BigDecimal r14c7BeginningBalance) {
        this.r14c7BeginningBalance = r14c7BeginningBalance;
    }

    public BigDecimal getR15c7BeginningBalance() {
        return r15c7BeginningBalance;
    }

    public void setR15c7BeginningBalance(BigDecimal r15c7BeginningBalance) {
        this.r15c7BeginningBalance = r15c7BeginningBalance;
    }

    public BigDecimal getR16c7BeginningBalance() {
        return r16c7BeginningBalance;
    }

    public void setR16c7BeginningBalance(BigDecimal r16c7BeginningBalance) {
        this.r16c7BeginningBalance = r16c7BeginningBalance;
    }

    public BigDecimal getR17c7BeginningBalance() {
        return r17c7BeginningBalance;
    }

    public void setR17c7BeginningBalance(BigDecimal r17c7BeginningBalance) {
        this.r17c7BeginningBalance = r17c7BeginningBalance;
    }

    public BigDecimal getR18c7BeginningBalance() {
        return r18c7BeginningBalance;
    }

    public void setR18c7BeginningBalance(BigDecimal r18c7BeginningBalance) {
        this.r18c7BeginningBalance = r18c7BeginningBalance;
    }

    public BigDecimal getR19c7BeginningBalance() {
        return r19c7BeginningBalance;
    }

    public void setR19c7BeginningBalance(BigDecimal r19c7BeginningBalance) {
        this.r19c7BeginningBalance = r19c7BeginningBalance;
    }

    public BigDecimal getR27c7BeginningBalance() {
        return r27c7BeginningBalance;
    }

    public void setR27c7BeginningBalance(BigDecimal r27c7BeginningBalance) {
        this.r27c7BeginningBalance = r27c7BeginningBalance;
    }

    public BigDecimal getR28c7BeginningBalance() {
        return r28c7BeginningBalance;
    }

    public void setR28c7BeginningBalance(BigDecimal r28c7BeginningBalance) {
        this.r28c7BeginningBalance = r28c7BeginningBalance;
    }

    public BigDecimal getR29c7BeginningBalance() {
        return r29c7BeginningBalance;
    }

    public void setR29c7BeginningBalance(BigDecimal r29c7BeginningBalance) {
        this.r29c7BeginningBalance = r29c7BeginningBalance;
    }

    public BigDecimal getR30c7BeginningBalance() {
        return r30c7BeginningBalance;
    }

    public void setR30c7BeginningBalance(BigDecimal r30c7BeginningBalance) {
        this.r30c7BeginningBalance = r30c7BeginningBalance;
    }

    public BigDecimal getR31c7BeginningBalance() {
        return r31c7BeginningBalance;
    }

    public void setR31c7BeginningBalance(BigDecimal r31c7BeginningBalance) {
        this.r31c7BeginningBalance = r31c7BeginningBalance;
    }

    public BigDecimal getR32c7BeginningBalance() {
        return r32c7BeginningBalance;
    }

    public void setR32c7BeginningBalance(BigDecimal r32c7BeginningBalance) {
        this.r32c7BeginningBalance = r32c7BeginningBalance;
    }

    public BigDecimal getR2c3() {
        return r2c3;
    }

    public void setR2c3(BigDecimal r2c3) {
        this.r2c3 = r2c3;
    }

    public BigDecimal getR3c3() {
        return r3c3;
    }

    public void setR3c3(BigDecimal r3c3) {
        this.r3c3 = r3c3;
    }

    public BigDecimal getR4c3() {
        return r4c3;
    }

    public void setR4c3(BigDecimal r4c3) {
        this.r4c3 = r4c3;
    }

    public BigDecimal getR5c3() {
        return r5c3;
    }

    public void setR5c3(BigDecimal r5c3) {
        this.r5c3 = r5c3;
    }

    public BigDecimal getR6c3() {
        return r6c3;
    }

    public void setR6c3(BigDecimal r6c3) {
        this.r6c3 = r6c3;
    }

    public BigDecimal getR7c3() {
        return r7c3;
    }

    public void setR7c3(BigDecimal r7c3) {
        this.r7c3 = r7c3;
    }

    public BigDecimal getR8c3() {
        return r8c3;
    }

    public void setR8c3(BigDecimal r8c3) {
        this.r8c3 = r8c3;
    }

    public BigDecimal getR9c3() {
        return r9c3;
    }

    public void setR9c3(BigDecimal r9c3) {
        this.r9c3 = r9c3;
    }

    public BigDecimal getR10c3() {
        return r10c3;
    }

    public void setR10c3(BigDecimal r10c3) {
        this.r10c3 = r10c3;
    }

    public BigDecimal getR11c3() {
        return r11c3;
    }

    public void setR11c3(BigDecimal r11c3) {
        this.r11c3 = r11c3;
    }

    public BigDecimal getR12c3() {
        return r12c3;
    }

    public void setR12c3(BigDecimal r12c3) {
        this.r12c3 = r12c3;
    }

    public BigDecimal getR13c3() {
        return r13c3;
    }

    public void setR13c3(BigDecimal r13c3) {
        this.r13c3 = r13c3;
    }

    public BigDecimal getR14c3() {
        return r14c3;
    }

    public void setR14c3(BigDecimal r14c3) {
        this.r14c3 = r14c3;
    }

    public BigDecimal getR15c3() {
        return r15c3;
    }

    public void setR15c3(BigDecimal r15c3) {
        this.r15c3 = r15c3;
    }

    public BigDecimal getR16c3() {
        return r16c3;
    }

    public void setR16c3(BigDecimal r16c3) {
        this.r16c3 = r16c3;
    }

    public BigDecimal getR18c3() {
        return r18c3;
    }

    public void setR18c3(BigDecimal r18c3) {
        this.r18c3 = r18c3;
    }

    public BigDecimal getR19c3() {
        return r19c3;
    }

    public void setR19c3(BigDecimal r19c3) {
        this.r19c3 = r19c3;
    }

    public BigDecimal getR20c3() {
        return r20c3;
    }

    public void setR20c3(BigDecimal r20c3) {
        this.r20c3 = r20c3;
    }

    public BigDecimal getR21c3() {
        return r21c3;
    }

    public void setR21c3(BigDecimal r21c3) {
        this.r21c3 = r21c3;
    }

    public BigDecimal getR22c3() {
        return r22c3;
    }

    public void setR22c3(BigDecimal r22c3) {
        this.r22c3 = r22c3;
    }

    public BigDecimal getR23c3() {
        return r23c3;
    }

    public void setR23c3(BigDecimal r23c3) {
        this.r23c3 = r23c3;
    }

    public BigDecimal getR24c3() {
        return r24c3;
    }

    public void setR24c3(BigDecimal r24c3) {
        this.r24c3 = r24c3;
    }

    public BigDecimal getR25c3() {
        return r25c3;
    }

    public void setR25c3(BigDecimal r25c3) {
        this.r25c3 = r25c3;
    }

    public BigDecimal getR26c3() {
        return r26c3;
    }

    public void setR26c3(BigDecimal r26c3) {
        this.r26c3 = r26c3;
    }

    public BigDecimal getR27c3() {
        return r27c3;
    }

    public void setR27c3(BigDecimal r27c3) {
        this.r27c3 = r27c3;
    }

    public BigDecimal getR28c3() {
        return r28c3;
    }

    public void setR28c3(BigDecimal r28c3) {
        this.r28c3 = r28c3;
    }

    public BigDecimal getR29c3() {
        return r29c3;
    }

    public void setR29c3(BigDecimal r29c3) {
        this.r29c3 = r29c3;
    }

    public BigDecimal getR30c3() {
        return r30c3;
    }

    public void setR30c3(BigDecimal r30c3) {
        this.r30c3 = r30c3;
    }

    public BigDecimal getR31c3() {
        return r31c3;
    }

    public void setR31c3(BigDecimal r31c3) {
        this.r31c3 = r31c3;
    }

    public BigDecimal getR32c3() {
        return r32c3;
    }

    public void setR32c3(BigDecimal r32c3) {
        this.r32c3 = r32c3;
    }

    public BigDecimal getR2c7() {
        return r2c7;
    }

    public void setR2c7(BigDecimal r2c7) {
        this.r2c7 = r2c7;
    }

    public BigDecimal getR3c7() {
        return r3c7;
    }

    public void setR3c7(BigDecimal r3c7) {
        this.r3c7 = r3c7;
    }

    public BigDecimal getR4c7() {
        return r4c7;
    }

    public void setR4c7(BigDecimal r4c7) {
        this.r4c7 = r4c7;
    }

    public BigDecimal getR5c7() {
        return r5c7;
    }

    public void setR5c7(BigDecimal r5c7) {
        this.r5c7 = r5c7;
    }

    public BigDecimal getR6c7() {
        return r6c7;
    }

    public void setR6c7(BigDecimal r6c7) {
        this.r6c7 = r6c7;
    }

    public BigDecimal getR7c7() {
        return r7c7;
    }

    public void setR7c7(BigDecimal r7c7) {
        this.r7c7 = r7c7;
    }

    public BigDecimal getR8c7() {
        return r8c7;
    }

    public void setR8c7(BigDecimal r8c7) {
        this.r8c7 = r8c7;
    }

    public BigDecimal getR9c7() {
        return r9c7;
    }

    public void setR9c7(BigDecimal r9c7) {
        this.r9c7 = r9c7;
    }

    public BigDecimal getR10c7() {
        return r10c7;
    }

    public void setR10c7(BigDecimal r10c7) {
        this.r10c7 = r10c7;
    }

    public BigDecimal getR11c7() {
        return r11c7;
    }

    public void setR11c7(BigDecimal r11c7) {
        this.r11c7 = r11c7;
    }

    public BigDecimal getR12c7() {
        return r12c7;
    }

    public void setR12c7(BigDecimal r12c7) {
        this.r12c7 = r12c7;
    }

    public BigDecimal getR13c7() {
        return r13c7;
    }

    public void setR13c7(BigDecimal r13c7) {
        this.r13c7 = r13c7;
    }

    public BigDecimal getR14c7() {
        return r14c7;
    }

    public void setR14c7(BigDecimal r14c7) {
        this.r14c7 = r14c7;
    }

    public BigDecimal getR15c7() {
        return r15c7;
    }

    public void setR15c7(BigDecimal r15c7) {
        this.r15c7 = r15c7;
    }

    public BigDecimal getR16c7() {
        return r16c7;
    }

    public void setR16c7(BigDecimal r16c7) {
        this.r16c7 = r16c7;
    }

    public BigDecimal getR17c7() {
        return r17c7;
    }

    public void setR17c7(BigDecimal r17c7) {
        this.r17c7 = r17c7;
    }

    public BigDecimal getR18c7() {
        return r18c7;
    }

    public void setR18c7(BigDecimal r18c7) {
        this.r18c7 = r18c7;
    }

    public BigDecimal getR19c7() {
        return r19c7;
    }

    public void setR19c7(BigDecimal r19c7) {
        this.r19c7 = r19c7;
    }

    public BigDecimal getR27c7() {
        return r27c7;
    }

    public void setR27c7(BigDecimal r27c7) {
        this.r27c7 = r27c7;
    }

    public BigDecimal getR28c7() {
        return r28c7;
    }

    public void setR28c7(BigDecimal r28c7) {
        this.r28c7 = r28c7;
    }

    public BigDecimal getR29c7() {
        return r29c7;
    }

    public void setR29c7(BigDecimal r29c7) {
        this.r29c7 = r29c7;
    }

    public BigDecimal getR30c7() {
        return r30c7;
    }

    public void setR30c7(BigDecimal r30c7) {
        this.r30c7 = r30c7;
    }

    public BigDecimal getR31c7() {
        return r31c7;
    }

    public void setR31c7(BigDecimal r31c7) {
        this.r31c7 = r31c7;
    }

    public BigDecimal getR32c7() {
        return r32c7;
    }

    public void setR32c7(BigDecimal r32c7) {
        this.r32c7 = r32c7;
    }

    public BigDecimal getR1c2() {
        return r1c2;
    }

    public void setR1c2(BigDecimal r1c2) {
        this.r1c2 = r1c2;
    }

    public BigDecimal getR2c2() {
        return r2c2;
    }

    public void setR2c2(BigDecimal r2c2) {
        this.r2c2 = r2c2;
    }

    public BigDecimal getR3c2() {
        return r3c2;
    }

    public void setR3c2(BigDecimal r3c2) {
        this.r3c2 = r3c2;
    }

    public BigDecimal getR4c2() {
        return r4c2;
    }

    public void setR4c2(BigDecimal r4c2) {
        this.r4c2 = r4c2;
    }

    public BigDecimal getR5c2() {
        return r5c2;
    }

    public void setR5c2(BigDecimal r5c2) {
        this.r5c2 = r5c2;
    }

    public BigDecimal getR6c2() {
        return r6c2;
    }

    public void setR6c2(BigDecimal r6c2) {
        this.r6c2 = r6c2;
    }

    public BigDecimal getR7c2() {
        return r7c2;
    }

    public void setR7c2(BigDecimal r7c2) {
        this.r7c2 = r7c2;
    }

    public BigDecimal getR8c2() {
        return r8c2;
    }

    public void setR8c2(BigDecimal r8c2) {
        this.r8c2 = r8c2;
    }

    public BigDecimal getR9c2() {
        return r9c2;
    }

    public void setR9c2(BigDecimal r9c2) {
        this.r9c2 = r9c2;
    }

    public BigDecimal getR10c2() {
        return r10c2;
    }

    public void setR10c2(BigDecimal r10c2) {
        this.r10c2 = r10c2;
    }

    public BigDecimal getR11c2() {
        return r11c2;
    }

    public void setR11c2(BigDecimal r11c2) {
        this.r11c2 = r11c2;
    }

    public BigDecimal getR12c2() {
        return r12c2;
    }

    public void setR12c2(BigDecimal r12c2) {
        this.r12c2 = r12c2;
    }

    public BigDecimal getR13c2() {
        return r13c2;
    }

    public void setR13c2(BigDecimal r13c2) {
        this.r13c2 = r13c2;
    }

    public BigDecimal getR14c2() {
        return r14c2;
    }

    public void setR14c2(BigDecimal r14c2) {
        this.r14c2 = r14c2;
    }

    public BigDecimal getR15c2() {
        return r15c2;
    }

    public void setR15c2(BigDecimal r15c2) {
        this.r15c2 = r15c2;
    }

    public BigDecimal getR16c2() {
        return r16c2;
    }

    public void setR16c2(BigDecimal r16c2) {
        this.r16c2 = r16c2;
    }

    public BigDecimal getR17c2() {
        return r17c2;
    }

    public void setR17c2(BigDecimal r17c2) {
        this.r17c2 = r17c2;
    }

    public BigDecimal getR18c2() {
        return r18c2;
    }

    public void setR18c2(BigDecimal r18c2) {
        this.r18c2 = r18c2;
    }

    public BigDecimal getR19c2() {
        return r19c2;
    }

    public void setR19c2(BigDecimal r19c2) {
        this.r19c2 = r19c2;
    }

    public BigDecimal getR20c2() {
        return r20c2;
    }

    public void setR20c2(BigDecimal r20c2) {
        this.r20c2 = r20c2;
    }

    public BigDecimal getR21c2() {
        return r21c2;
    }

    public void setR21c2(BigDecimal r21c2) {
        this.r21c2 = r21c2;
    }

    public BigDecimal getR22c2() {
        return r22c2;
    }

    public void setR22c2(BigDecimal r22c2) {
        this.r22c2 = r22c2;
    }

    public BigDecimal getR23c2() {
        return r23c2;
    }

    public void setR23c2(BigDecimal r23c2) {
        this.r23c2 = r23c2;
    }

    public BigDecimal getR24c2() {
        return r24c2;
    }

    public void setR24c2(BigDecimal r24c2) {
        this.r24c2 = r24c2;
    }

    public BigDecimal getR25c2() {
        return r25c2;
    }

    public void setR25c2(BigDecimal r25c2) {
        this.r25c2 = r25c2;
    }

    public BigDecimal getR26c2() {
        return r26c2;
    }

    public void setR26c2(BigDecimal r26c2) {
        this.r26c2 = r26c2;
    }

    public BigDecimal getR27c2() {
        return r27c2;
    }

    public void setR27c2(BigDecimal r27c2) {
        this.r27c2 = r27c2;
    }

    public BigDecimal getR28c2() {
        return r28c2;
    }

    public void setR28c2(BigDecimal r28c2) {
        this.r28c2 = r28c2;
    }

    public BigDecimal getR29c2() {
        return r29c2;
    }

    public void setR29c2(BigDecimal r29c2) {
        this.r29c2 = r29c2;
    }

    public BigDecimal getR30c2() {
        return r30c2;
    }

    public void setR30c2(BigDecimal r30c2) {
        this.r30c2 = r30c2;
    }

    public BigDecimal getR31c2() {
        return r31c2;
    }

    public void setR31c2(BigDecimal r31c2) {
        this.r31c2 = r31c2;
    }

    public BigDecimal getR32c2() {
        return r32c2;
    }

    public void setR32c2(BigDecimal r32c2) {
        this.r32c2 = r32c2;
    }
}
