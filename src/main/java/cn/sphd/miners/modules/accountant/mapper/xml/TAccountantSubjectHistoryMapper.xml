<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.accountant.mapper.TAccountantSubjectHistoryMapper">

    <select id="getSingle" resultType = "subjectHistory" parameterType="subjectHistory" >
        select
        id, subject_id AS subjectId, subject, org, parent, name, category, level, orders, state, max_child_subjects AS maxChildSubjects,
        initial_amount AS initialAmount, beginning_balance AS  beginningBalance, previous_balance AS previousBalance, carried_forward_date AS  carriedForwardDate, balance,
        quantity, summary, credit, debit, credit_accumulative AS  creditAccumulative, debit_accumulative AS debitAccumulative, beginning_direction AS beginningDirection,
        previous_direction AS previousDirection, balance_direction AS balanceDirection, measure_unit AS measureUnit, memo, creator, create_name AS createName,
        create_date AS createDate, updator, update_name AS updateName, update_date AS updateDate, approve_Item AS  approveItem, approve_status AS approveStatus, approve_level AS approveLevel,
        auditor, auditor_name AS auditorName, audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId
        ,end_direction AS  endDirection,quantity_assisting_accounting AS  quantityAssistingAccounting
        from t_accountant_subject_history
        where id = #{id}
    </select>

    <select id = "listPage" resultType = "subjectHistory" parameterType = "hashmap" >
        select
        id, subject_id AS subjectId, subject, org, parent, name, category, level, orders, state, max_child_subjects AS maxChildSubjects,
        initial_amount AS initialAmount, beginning_balance AS  beginningBalance, previous_balance AS previousBalance, carried_forward_date AS  carriedForwardDate, balance,
        quantity, summary, credit, debit, credit_accumulative AS  creditAccumulative, debit_accumulative AS debitAccumulative, beginning_direction AS beginningDirection,
        previous_direction AS previousDirection, balance_direction AS balanceDirection, measure_unit AS measureUnit, memo, creator, create_name AS createName,
        create_date AS createDate, updator, update_name AS updateName, update_date AS updateDate, approve_Item AS  approveItem, approve_status AS approveStatus, approve_level AS approveLevel,
        auditor, auditor_name AS auditorName, audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId
        ,end_direction AS  endDirection,quantity_assisting_accounting AS  quantityAssistingAccounting
        from t_accountant_subject_history
    </select>

    <select id="getOneSubjecthistory" resultType="subjectHistory">
        SELECT
	a.id,
	a.name,
	a.subject,
	a.max_child_subjects AS maxChildSubjects,
	a.balance_direction AS balanceDirection,
	a.level,
	a.category,
	a.state,
	a.parent,
	a.measure_unit AS measureUnit,
	a.previous_balance AS previousBalance,
	s.name AS categoryname,
	a.org,
	a.subject_id AS subjectId,
	a.end_direction AS endDirection,
	a.balance,
	a.credit_accumulative AS creditAccumulative,
	a.debit_accumulative AS debitAccumulative,
	a.previous_direction AS previousDirection,
	a.beginning_balance AS beginningBalance,
	a.beginning_direction AS beginningDirection
	,a.quantity_assisting_accounting AS  quantityAssistingAccounting,
	a.quantity

        FROM t_accountant_subject_history a, t_accountant_subject_category s
        WHERE a.category = s.id AND a.org = #{org} AND a.subject = #{subject}
    </select>
    <select id="getAllHistorySubject" resultType="subjectHistory">
        select
        id, subject_id AS subjectId, subject, org, parent, name, category, level, orders, state, max_child_subjects AS maxChildSubjects,
        initial_amount AS initialAmount, beginning_balance AS  beginningBalance, previous_balance AS previousBalance, carried_forward_date AS  carriedForwardDate, balance,
        quantity, summary, credit, debit, credit_accumulative AS  creditAccumulative, debit_accumulative AS debitAccumulative, beginning_direction AS beginningDirection,
        previous_direction AS previousDirection, balance_direction AS balanceDirection, measure_unit AS measureUnit, memo, creator, create_name AS createName,
        create_date AS createDate, updator, update_name AS updateName, update_date AS updateDate, approve_Item AS  approveItem, approve_status AS approveStatus, approve_level AS approveLevel,
        auditor, auditor_name AS auditorName, audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId
        ,end_direction AS  endDirection,quantity_assisting_accounting AS  quantityAssistingAccounting
        from t_accountant_subject_history
        where org = #{org}
    </select>
    <select id="getSubjectHistoryBySubject"
            resultType="subjectHistory">

                select
                 credit_accumulative AS  creditAccumulative, debit_accumulative AS debitAccumulative,
        id, subject_id AS subjectId, subject, org, parent, name, category, level, orders, state, max_child_subjects AS maxChildSubjects,
        initial_amount AS initialAmount, beginning_balance AS  beginningBalance, previous_balance AS previousBalance, carried_forward_date AS  carriedForwardDate, balance,
        quantity, summary, beginning_direction AS beginningDirection,
        previous_direction AS previousDirection, balance_direction AS balanceDirection, measure_unit AS measureUnit, memo, creator, create_name AS createName,
        create_date AS createDate, updator, update_name AS updateName, update_date AS updateDate, approve_Item AS  approveItem, approve_status AS approveStatus, approve_level AS approveLevel,
        auditor, auditor_name AS auditorName, audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId
        ,end_direction AS  endDirection,quantity_assisting_accounting AS  quantityAssistingAccounting
        from t_accountant_subject_history
        where org = #{org} and subject=#{subject}

    </select>
    <select id="childSubject" resultType="subjectHistory">

        SELECT
                 credit_accumulative AS  creditAccumulative, debit_accumulative AS debitAccumulative,
        id, subject_id AS subjectId, subject, org, parent, name, category, level, orders, state, max_child_subjects AS maxChildSubjects,
        initial_amount AS initialAmount, beginning_balance AS  beginningBalance, previous_balance AS previousBalance, carried_forward_date AS  carriedForwardDate, balance,
        quantity, summary, beginning_direction AS beginningDirection,
        previous_direction AS previousDirection, balance_direction AS balanceDirection, measure_unit AS measureUnit, memo, creator, create_name AS createName,
        create_date AS createDate, updator, update_name AS updateName, update_date AS updateDate, approve_Item AS  approveItem, approve_status AS approveStatus, approve_level AS approveLevel,
        auditor, auditor_name AS auditorName, audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId
        ,end_direction AS  endDirection
        FROM
        t_accountant_subject_history
        WHERE
        parent = #{subject} AND org = #{org}

    </select>


    <select id="listAllSubject"
            resultType="subjectHistory">

         select  id,subject_id AS subjectId, subject, org, parent, name, category, level, orders, state, max_child_subjects AS maxChildSubjects,
        initial_amount AS  initialAmount, beginning_balance AS beginningBalance,beginning_direction AS beginningDirection,
         previous_balance AS  previousBalance,previous_direction AS previousDirection, carried_forward_date AS carriedForwardDate, balance,
        summary, credit, debit, credit_accumulative AS creditAccumulative, debit_accumulative AS debitAccumulative, balance_direction AS balanceDirection,
        measure_unit AS measureUnit, memo, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName, update_date AS updateDate,
        approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName, audit_date AS auditDate, operation,
        apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId,end_direction AS  endDirection,quantity_assisting_accounting AS  quantityAssistingAccounting
        FROM
        t_accountant_subject_history
        WHERE
        org = #{org} order by subject


    </select>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" >
        insert into t_accountant_subject_history (subject_id, subject,
        org, parent, name,
        category, level, orders,
        state, max_child_subjects, initial_amount,
        beginning_balance, previous_balance, carried_forward_date,
        balance, quantity, summary,
        credit, debit, credit_accumulative,
        debit_accumulative, beginning_direction, previous_direction,
        balance_direction, measure_unit, memo,
        creator, create_name, create_date,
        updator, update_name, update_date,
        approve_Item, approve_status, approve_level,
        auditor, auditor_name, audit_date,
        operation, apply_memo, approve_memo,
        message_id,end_direction,quantity_assisting_accounting)
        values ( #{subjectId}, #{subject},
        #{org}, #{parent}, #{name},
        #{category}, #{level}, #{orders},
        #{state}, #{maxChildSubjects}, #{initialAmount},
        #{beginningBalance}, #{previousBalance}, #{carriedForwardDate},
        #{balance}, #{quantity}, #{summary},
        #{credit}, #{debit}, #{creditAccumulative},
        #{debitAccumulative}, #{beginningDirection}, #{previousDirection},
        #{balanceDirection}, #{measureUnit}, #{memo},
        #{creator}, #{createName}, #{createDate},
        #{updator}, #{updateName}, #{updateDate},
        #{approveItem}, #{approveStatus}, #{approveLevel},
        #{auditor}, #{auditorName}, #{auditDate},
        #{operation}, #{applyMemo}, #{approveMemo},
        #{messageId},#{endDirection},#{quantityAssistingAccounting})
    </insert>

    <insert id="insertOrgSubject"  >
        insert into t_accountant_subject_history (subject_id, subject,
        org, name, category, level, state, max_child_subjects,
        previous_balance, balance, credit, debit, previous_direction,
        balance_direction, creator,end_direction, parent,
        credit_accumulative,debit_accumulative,beginning_balance, beginning_direction,quantity_assisting_accounting,quantity,relevance_type, relevance_item,useable
        ,initial_quantity,beginning_quantity,previous_quantity,credit_accumulative_quantity,debit_accumulative_quantity)
        SELECT
        id, subject, org, name, category, level, state, max_child_subjects,
        previous_balance, balance, credit, debit, previous_direction,
        balance_direction, creator,end_direction,parent,credit_accumulative,
        debit_accumulative, beginning_balance, beginning_direction,quantity_assisting_accounting,quantity,relevance_type, relevance_item,useable
        ,initial_quantity,beginning_quantity,previous_quantity,credit_accumulative_quantity,debit_accumulative_quantity
        FROM t_accountant_subject
        WHERE org = #{org}
    </insert>

    <update id="updateOrgSubject">

        UPDATE `t_accountant_subject_history` SET balance=0,credit=0,debit=0 WHERE org=#{org}

    </update>

    <update id="update" parameterType="subjectHistory" >
        update t_accountant_subject_history
        <set >
            <if test="subjectId != null" >
                subject_id = #{subjectId},
            </if>
            <if test="subject != null" >
                subject = #{subject},
            </if>
            <if test="org != null" >
                org = #{org},
            </if>
            <if test="parent != null" >
                parent = #{parent},
            </if>
            <if test="name != null" >
                name = #{name},
            </if>
            <if test="category != null" >
                category = #{category},
            </if>
            <if test="level != null" >
                level = #{level},
            </if>
            <if test="orders != null" >
                orders = #{orders},
            </if>
            <if test="state != null" >
                state = #{state},
            </if>
            <if test="maxChildSubjects != null" >
                max_child_subjects = #{maxChildSubjects},
            </if>
            <if test="initialAmount != null" >
                initial_amount = #{initialAmount},
            </if>
            <if test="beginningBalance != null" >
                beginning_balance = #{beginningBalance},
            </if>
            <if test="previousBalance != null" >
                previous_balance = #{previousBalance},
            </if>
            <if test="carriedForwardDate != null" >
                carried_forward_date = #{carriedForwardDate},
            </if>
            <if test="balance != null" >
                balance = #{balance},
            </if>
            <if test="quantity != null" >
                quantity = #{quantity},
            </if>
            <if test="summary != null" >
                summary = #{summary},
            </if>
            <if test="credit != null" >
                credit = #{credit},
            </if>
            <if test="debit != null" >
                debit = #{debit},
            </if>
            <if test="creditAccumulative != null" >
                credit_accumulative = #{creditAccumulative},
            </if>
            <if test="debitAccumulative != null" >
                debit_accumulative = #{debitAccumulative},
            </if>
            <if test="beginningDirection != null" >
                beginning_direction = #{beginningDirection},
            </if>
            <if test="previousDirection != null" >
                previous_direction = #{previousDirection},
            </if>
            <if test="balanceDirection != null" >
                balance_direction = #{balanceDirection},
            </if>

            <if test="endDirection != null" >
                end_direction = #{endDirection},
            </if>

            <if test="measureUnit != null" >
                measure_unit = #{measureUnit},
            </if>
            <if test="memo != null" >
                memo = #{memo},
            </if>
            <if test="creator != null" >
                creator = #{creator},
            </if>
            <if test="createName != null" >
                create_name = #{createName},
            </if>
            <if test="createDate != null" >
                create_date = #{createDate},
            </if>
            <if test="updator != null" >
                updator = #{updator},
            </if>
            <if test="updateName != null" >
                update_name = #{updateName},
            </if>
            <if test="updateDate != null" >
                update_date = #{updateDate},
            </if>
            <if test="approveItem != null" >
                approve_Item = #{approveItem},
            </if>
            <if test="approveStatus != null" >
                approve_status = #{approveStatus},
            </if>
            <if test="approveLevel != null" >
                approve_level = #{approveLevel},
            </if>
            <if test="auditor != null" >
                auditor = #{auditor},
            </if>
            <if test="auditorName != null" >
                auditor_name = #{auditorName},
            </if>
            <if test="auditDate != null" >
                audit_date = #{auditDate},
            </if>
            <if test="operation != null" >
                operation = #{operation},
            </if>
            <if test="applyMemo != null" >
                apply_memo = #{applyMemo},
            </if>
            <if test="approveMemo != null" >
                approve_memo = #{approveMemo},
            </if>
            <if test="messageId != null" >
                message_id = #{messageId},
            </if>
            <if test="quantityAssistingAccounting != null" >
                quantity_assisting_accounting = #{quantityAssistingAccounting},
            </if>
        </set>
        where id = #{id}
    </update>
    <update id="swapBalance">

        UPDATE  `t_accountant_subject_history` SET previous_balance=balance,previous_direction=end_direction WHERE org=#{org}

    </update>
    <update id="updateBeginning">

        update t_accountant_subject_history set beginning_direction=end_direction,beginning_balance=balance
    </update>

    <delete id="delete" parameterType="subjectHistory" >
        delete from t_accountant_subject_history
        where id = #{id}
    </delete>

    <delete id="delSubjectHistory">
        delete from t_accountant_subject_history where org = #{org}
    </delete>


</mapper>