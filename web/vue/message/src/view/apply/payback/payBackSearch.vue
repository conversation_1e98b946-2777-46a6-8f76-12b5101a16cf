<template>
  <div id="applyFileQuery">
    <TY_NavTop :title="ssTitle"></TY_NavTop>
    <div class="container">
      <div class="ui-message">
        <div class="sendContainer">
          <div class="tip">您可查询到一年内的回款记录，请确定查询条件。</div>
          <el-form :model="queryForm" ref="queryForm" label-width="70px" size="mini">
            <el-form-item label="录入时间">
              <el-select v-model="queryForm.inputVal" placeholder="—— ——" @change="clearOther('acceptTime')">
                <el-option
                  v-for="item in inputTime"
                  :key="item"
                  :label="item.inputLabel"
                  :value="item.inputVal">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="受理时间">
              <el-select v-model="queryForm.acceptVal" placeholder="—— ——" @change="clearOther('inputTime')">
                <el-option
                  v-for="item in inputTime"
                  :key="item"
                  :label="item.inputLabel"
                  :value="item.inputVal">
                </el-option>
              </el-select>
            </el-form-item>
            <p class="hrGap"></p> <!--1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐'-->
            <el-form-item label="收入方式">
              <el-select v-model="income" placeholder="全部">
                <el-option label="全部" value=""></el-option>
                <el-option label="现金" value="1"></el-option>
                <el-option label="银行转账" value="5"></el-option>
                <el-option label="转账支票" value="3"></el-option>
                <el-option label="承兑汇票" value="4"></el-option>
              </el-select>
            </el-form-item>
            <p class="hrGap"></p>
            <el-form-item label="录入者" v-if="ssType === '2'">
              <el-select v-model="inputerVal" placeholder="全部">
                <el-option label="全部" value=""></el-option>
                <el-option
                  v-for="item in inputer"
                  :key="item.userID"
                  :label="item.userName"
                  :value="item.userID">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div class="handle_button">
            <input type="submit" class="fc-btn ui-btn_info" @click="screenCancel" value="取消">
            <input type="submit" :class="{'fc-btn ui-btn_info': !btnControl, 'fd-btn ui-btn_disabled': btnControl}" value="确定" @click="screenEnd()" :disabled = 'btnControl'>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { formatDate, getMonthList } from '../../../js/common'
export default {
  name: 'payApplyScreen',
  filters: {
    formatDate
  },
  data () {
    return {
      isDis: true,
      income: '',
      ssType: '',
      ssTitle: '',
      inputer: '',
      inputerVal: '',
      queryForm: {
        inputVal: '',
        acceptVal: ''
      },
      inputTime: [],
      loading: false,
      endParams: {}
    }
  },
  computed: {
    btnControl () {
      return this.queryForm.inputVal === '' && this.queryForm.acceptVal === ''
    }
  },
  created () {
    let type = this.$route.params.type
    if (type === undefined) {
      type = JSON.parse(localStorage.getItem('collectParams')).type
    }
    this.ssType = type
    this.inputTime = getMonthList(12)
    this.queryForm.inputVal = formatDate(new Date(), 'yyyy-MM')
    if (type === '1') {
      this.ssTitle = '回款录入'
    } else if (type === '2') {
      this.ssTitle = '回款入账'
    }
    if (type === '2') {
      // 获取录入者
      this.$http.post('../../../salesBack/getEntryUser.do', {}, {
        emulateJSON: true
      }).then((response) => {
        this.inputer = response.body.list
      }).catch(function () {
        this.$message({
          type: 'error',
          message: '系统错误，请重试！'
        })
      })
    }
  },
  methods: {
    clearOther: function (type) {
      if (type === 'inputTime') {
        this.queryForm.inputVal = ''
      } else if (type === 'acceptTime') {
        this.queryForm.acceptVal = ''
      }
    },
    screenEnd: function () {
      this.endParams.type = this.ssType
      this.endParams.method = this.income
      this.endParams.approvalDate = this.queryForm.acceptVal
      this.endParams.applyDate = this.queryForm.inputVal
      if (this.ssType === '2') {
        this.endParams.applyUserName = ''
        this.endParams.applyUserId = this.inputerVal
        for (var a in this.inputer) {
          if (this.inputerVal === this.inputer[a].userID) {
            this.endParams.applyUserName = this.inputer[a].userName
          }
        }
      }
      let param = JSON.stringify(this.endParams)
      localStorage.setItem('collectParams', param)
      this.$router.push({
        path: '/paySearchList'
      })
    },
    screenCancel: function () {
      let floatingUrl = window.localStorage.getItem('floating')
      if (floatingUrl) {
        floatingUrl = floatingUrl.split('-')
        if (floatingUrl.length > 0) {
          floatingUrl.pop()
          if (floatingUrl.length > 0) {
            this.$router.push({
              path: floatingUrl[floatingUrl.length - 1]
            })
          } else {
            this.$router.push({
              path: '/'
            })
          }
          floatingUrl.pop()
          window.localStorage.setItem('floating', floatingUrl.join('-'))
        } else {
          this.$router.push({
            path: '/'
          })
        }
      }
    }
  }
}
</script>
