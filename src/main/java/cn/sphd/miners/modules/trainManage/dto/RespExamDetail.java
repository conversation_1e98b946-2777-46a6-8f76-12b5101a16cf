package cn.sphd.miners.modules.trainManage.dto;

import cn.sphd.miners.modules.trainManage.entity.TrainingExam;

import java.util.List;

/**
 * @ClassName RespExamDetail
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/3/2 16:44
 * @Version 1.0
 */
public class RespExamDetail {
    private Integer status;
    private TrainingExam trainingExam;
    private List<RespExamBank> respExamBankList;
    private List<RespExamUser> respExamUserList;
    private List<RespExamQuestion> respExamQuestionList;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<RespExamQuestion> getRespExamQuestionList() {
        return respExamQuestionList;
    }

    public void setRespExamQuestionList(List<RespExamQuestion> respExamQuestionList) {
        this.respExamQuestionList = respExamQuestionList;
    }

    public TrainingExam getTrainingExam() {
        return trainingExam;
    }

    public void setTrainingExam(TrainingExam trainingExam) {
        this.trainingExam = trainingExam;
    }

    public List<RespExamBank> getRespExamBankList() {
        return respExamBankList;
    }

    public void setRespExamBankList(List<RespExamBank> respExamBankList) {
        this.respExamBankList = respExamBankList;
    }

    public List<RespExamUser> getRespExamUserList() {
        return respExamUserList;
    }

    public void setRespExamUserList(List<RespExamUser> respExamUserList) {
        this.respExamUserList = respExamUserList;
    }
}
