<template>
  <div id="dailyAffairs">
    <TY_NavTop title="待处理的提醒"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="ui-message">
        <div class="sendContainer">
          <div class="scheduleDetail">
            <span class="title">{{scheduleDetail.title}}</span>
            <div class="tip">系统应于{{scheduleDetail.activeStartDate | formatDate('yyyy年MM月dd hh:mm')}}给予提醒 正在提醒中</div>
            <div>描述： {{scheduleDetail.description}}</div>
            <div class="ui-row">
              <div class="item-header">详细描述</div>
              <el-form :label-position="left" :model="handleSchedule" :rules="rules" label-width="154px" ref="handleSchedule">
                <el-radio-group v-model="handleSchedule.type">
                  <el-radio :disabled="scheduleDetail.state !== 2" label="1">本次活动已完成</el-radio>
                  <el-radio :disabled="scheduleDetail.state !== 2" label="2">不再提醒</el-radio>
                  <el-radio :disabled="scheduleDetail.state !== 2" label="3">再次提醒</el-radio>
                </el-radio-group>
                <el-form-item label="请选择再次提醒的时间" prop="remindDate" style="margin-top: 8px" v-if="handleSchedule.type === '3'">
                  <el-select placeholder="请选择" v-model="handleSchedule.remindDate" size="small">
                    <el-option
                      v-for="item in options.delay"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
                <button class="ui-btn" :disabled="scheduleDetail.state !== 2 && scheduleDetail.state !== 4" @click="openDialog()" style="margin-top: 8px">自定义提醒时间</button>
              </el-form>
            </div>
            <div class="handle_button">
              <input type="submit" value="确定" class="ui-btn ui-btn_info" :disabled="scheduleDetail.state !== 2" @click="handle()" />
            </div>
          </div>
        </div>
      </div>
      <el-dialog
        title="自定义提醒时间"
        :visible.sync="dialogVisible"
        width="98%"
        :before-close="handleClose"
        :modal-append-to-body="false"
      >
        <el-form :label-position="left" :model="schedule" :rules="rules" label-width="110px" ref="schedule">
          <el-input v-model="schedule.title" size="small"></el-input>
          <el-form-item label="需要设置提醒吗" prop="notify">
            <el-radio-group v-model="notify" disabled>
              <el-radio label="true">需要</el-radio>
              <el-radio label="false">不需要</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="重复周期" prop="freqType">
            <el-select placeholder="请选择" v-model="freqType" size="small" style="width: 100%" disabled>
              <el-option
                v-for="item in options.freqType"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="从何时开始提醒">
            <el-date-picker type="date" placeholder="请选择日期" value-format="yyyy-MM-dd" v-model="schedule.date" size="small" style="width: 130px"></el-date-picker>
            <el-select placeholder="请选择时间" v-model="schedule.time" size="small" style="width: 84px">
              <el-option
                v-for="item in options.timeLine"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <div class="tips" v-show="schedule.date">
            <div>系统将于{{schedule.date}} {{schedule.time}}提醒</div>
          </div>
          <el-form-item label="详细描述" prop="description">
            <el-input v-model="schedule.description" type="textarea" :autosize="{ minRows: 2, maxRows: 4}" placeholder="请输入内容" size="small"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <div class="handle_button">
            <input type="submit" class="ui-btn" value=" 取消" @click="dialogVisible = false">
            <input type="submit" class="ui-btn ui-btn_info" value="确定" @click="delay()">
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<style>
  .title{
    font-size: 16px;
    font-weight: bold;
    color: #aaa;
  }
  .tips{
    color: #5d9cec;
  }
</style>
<script>
import { formatDate, gohistory } from '../../js/common'
export default {
  name: 'scheduleDetail',
  filters: {
    formatDate,
    gohistory
  },
  data () {
    return {
      options: {
        delay: [{
          value: 'fiveMinutes',
          label: '5分钟后'
        }, {
          value: 'tenMinutes',
          label: '10分钟后'
        }, {
          value: 'fifteenMinutes',
          label: '15分钟后'
        }, {
          value: 'halfHour',
          label: '30分钟后'
        }, {
          value: 'oneHour',
          label: '1小时后'
        }, {
          value: 'twoHours',
          label: '2小时后'
        }, {
          value: 'fourHours',
          label: '4小时后'
        }, {
          value: 'eightHours',
          label: '8小时后'
        }],
        freqType: [{
          value: 1,
          label: '不重复'
        }],
        specialInterval: [{
          value: 1,
          label: '1天'
        }, {
          value: 2,
          label: '2天'
        }, {
          value: 3,
          label: '3天'
        }],
        timeLine: []
      },
      socket: window.parent.sphdSocket,
      id: 0,
      messageParam: 0,
      dialogVisible: false,
      loading: false,
      scheduleDetail: {},
      handleSchedule: {},
      schedule: {
        date: '',
        time: ''
      },
      notify: 'true',
      freqType: 1
    }
  },
  created () {
    // 状态:1-已完成、2-提醒中、3-不再提醒、4-提醒已过期、5-尚未开始提醒
    this.messageParam = {
      'userId': this.sphdSocket.user.userID, 'messageId': this.$route.query.messageId
    }
    let id = this.$route.params.schedule
    this.id = id
    let _this = this
    let params = {
      id: id,
      msgId: this.$route.params.id,
      jobId: this.$route.params.jobId
    }
    _this.$http.post('../../../schedule/scheduleInfo.do', params, {
      emulateJSON: true
    }).then((response) => {
      let data = response.body.data
      let memoSchedule = data['memoSchedule']
      if (data) {
        // 赋值导航列表（根据pid=0获取一级导航）
        _this.scheduleDetail = memoSchedule
      } else {
        _this.$message({
          type: 'error',
          message: '获取详情失败！'
        })
      }
    }).catch(function () {
      _this.$message({
        type: 'error',
        message: '系统错误，请重试！'
      })
    })
  },
  methods: {
    jump: function () {
      gohistory(this)
    },
    openDialog: function () {
      this.options.timeLine = this.getData('0:00', 0)
      this.dialogVisible = true
      let scheduleDetail = this.scheduleDetail
      let day = formatDate(scheduleDetail.activeStartDate, 'yyyy-MM-dd hh:mm:ss')
      day = day.split(' ')
      scheduleDetail.date = day[0]
      scheduleDetail.time = day[1].substring(0, 5)
      scheduleDetail.specialInterval = ''
      this.schedule = {
        title: scheduleDetail.title,
        date: scheduleDetail.date,
        time: scheduleDetail.time,
        description: scheduleDetail.description,
        notify: true,
        freqType: 1
      }
    },
    getData: function (start, delay) {
      let data = []
      let a = start.split(':')[0]
      let b = start.split(':')[1]
      for (let i = a; i < 24; i++) {
        if (i < 10) {
          i = '0' + i
        }
        data.push({ 'value': i + ':' + '00', 'label': i + ':' + '00' })
        data.push({ 'value': i + ':' + '30', 'label': i + ':' + '30' })
      }
      if (b === '30') {
        data.shift()
      }
      if (delay > 0) {
        for (let j = 0; j < delay; j++) {
          data.shift()
        }
      }
      return data
    },
    handle: function () {
      let schedule = this.handleSchedule
      schedule.id = this.id
      schedule.msgId = this.$route.params.id
      let _this = this
      if (!schedule.type) {
        _this.$kiko_message('请选择操作')
        return false
      }
      if (schedule.type === '3' && !schedule.remindDate) {
        _this.$kiko_message('请选择再次提醒时间')
        return false
      }
      this.$http.post('../../../schedule/handleSchedule.do', schedule, {
        emulateJSON: true
      }).then((response) => {
        let data = response.body
        let status = data.status
        if (status === 1) {
          // 赋值导航列表（根据pid=0获取一级导航）
          _this.$kiko_message('操作成功')
          // _this.sphdSocket.send('updateUserSuspendMsg', _this.messageParam)
          console.log('messageParam' + JSON.stringify(_this.messageParam))
          _this.jump()
        } else if (status === 2) {
          _this.$kiko_message('时间已过时，日程不能提交')
        } else {
          _this.$kiko_message('操作失败')
        }
      }).catch(function () {
        _this.$message({
          type: 'error',
          message: '系统错误，请重试！'
        })
      })
    },
    clear: function () {
      if (!this.schedule.special) {
        this.schedule.specialInterval = ''
      }
    },
    delay: function () {
      let schedule = this.schedule
      if (schedule.date === '' || schedule.date === null || schedule.time === '') {
        this.$kiko_message('请选择从何时开始提醒')
      } else {
        let param = {
          id: this.id,
          msgId: this.$route.params.id,
          title: '', // 标题
          notify: true, // 是否需要通知
          freqType: '', // 次日程的频率 1 = 仅一次,4 = 每日,8 = 每周,16 =每月,32 =每年
          special: false, // 是否为特殊时间(如月末倒数),true-是,false-否）
          startDate: '', // 可以开始执行提醒的日期
          specialInterval: '', // 倒数第某天
          description: '' // 详细描述
        }
        for (let key in param) {
          if (schedule[key]) {
            param[key] = schedule[key]
          }
        }
        param.startDate = schedule.date + ' ' + schedule.time + ':00'
        console.log(param)
        let _this = this
        this.sphdSocket.send('updateUserSuspendMsg', this.messageParam)
        this.$http.post('../../../schedule/copySchedule.do', param, {
          emulateJSON: true
        }).then((response) => {
          let data = response.body
          let status = data.status
          if (status === 1) {
            // 赋值导航列表（根据pid=0获取一级导航）
            _this.$kiko_message('操作成功')
            _this.jump()
          } else {
            _this.$kiko_message('操作失败')
          }
        }).catch(function () {
          _this.$message({
            type: 'error',
            message: '系统错误，请重试！'
          })
        })
      }
    }
  }
}
</script>
