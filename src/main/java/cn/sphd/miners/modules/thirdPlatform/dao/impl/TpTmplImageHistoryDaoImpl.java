package cn.sphd.miners.modules.thirdPlatform.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.thirdPlatform.dao.TpTmplImageHistoryDao;
import cn.sphd.miners.modules.thirdPlatform.entity.TpTmplImageHistory;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class TpTmplImageHistoryDaoImpl extends BaseDao<TpTmplImageHistory, Serializable> implements TpTmplImageHistoryDao {
}
