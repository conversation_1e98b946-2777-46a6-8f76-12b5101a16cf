package cn.sphd.miners.modules.accountant.service.impl;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.accountant.entity.*;
import cn.sphd.miners.modules.accountant.mapper.*;
import cn.sphd.miners.modules.accountant.service.SubjectSelectService;
import cn.sphd.miners.modules.accountant.service.SubjectSettingService;
import cn.sphd.miners.modules.accountant.zEnum.CommonEnum;
import cn.sphd.miners.modules.finance.service.DataService;
import cn.sphd.miners.modules.material.service.MaterielService;
import cn.sphd.miners.modules.personal.service.UserMessageService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSON;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by 刘洪涛 on 17-1-4.
 */
@Service
@Transactional(readOnly=false)
public class SubjectSelectServiceImplement implements SubjectSelectService {

//    @Autowired
//    JdbcTemplate jdbcTemplate;
//    @Autowired
//    UserTestMapper userTestDao;
    @Autowired
    SubjectSelectMapper subjectSelectMapper;
    @Autowired
    VoucherMapper voucherMapper;
    @Autowired
    VoucherHistoryMapper voucherHistoryMapper;
    @Autowired
    VoucherSubjectMapper voucherSubjectMapper;
    @Autowired
    SubjectHistoryMapper subjectHistoryMapper;
    @Autowired
    DetailMapper detailMapper;
    @Autowired
    DetailHistoryMapper detailHistoryMapper;
    @Autowired
    SettleMapper settleMapper;
    @Autowired
    TAccountSubjectMapper tAccountSubjectMapper;
    @Autowired
    SubjectPeroidMapper subjectPeroidMapper;
    @Autowired
    TAccountantCashMapper cashMapper;
    @Autowired
    TAccountantSubjectCashMapper subjectCashMapper;
    @Autowired
    TAccountantSubjectCashHistoryMapper subjectCashHistoryMapper;
    @Autowired
    MaterielService materielService;
    @Autowired
    SubjectSettingService settingService;
    @Autowired
    UserService userService;
    @Autowired
    UserMessageService messageService;
    @Autowired
    TaccountantSettingHistoryEntityMapper settingHistoryEntityMapper;
    @Autowired
    VoucherUpdate voucherUpdate;
    @Autowired
    DataService dataService;
    @Autowired
    SubjectSelectService subjectSelectService;


    @Override
    public JSONObject queryFinanceData(int oid) {
        String sqlGetFinanceDataBill = "SELECT * FROM `t_finance_account_bill` WHERE org=" + oid +
                " AND (type='1' OR (type='2' AND (bill_cat='普通票' or bill_cat='增值票')))";

//        List listBill = jdbcTemplate.queryForList(sqlGetFinanceDataBill);
        /*
        * 应该还得判断票据类型，但是目前数据库里没有这个字段.
        * type-3收入 type-4支出
        * method-1现金  method-5银行转账
        * receive_account_date 如果是转账的话这个值是到帐时间
        * detail表的票据类型存放在bill表里
        * */
        String sqlGetFinanceDataDetail = "SELECT * FROM `t_finance_account_detail` WHERE org=" + oid + " AND " +
                "(type='3' OR (type='4'  AND  (bill_id in (SELECT id FROM t_finance_account_bill WHERE bill_cat='普通票' or bill_cat='增值票'))));";

//        List listDetail = jdbcTemplate.queryForList(sqlGetFinanceDataDetail);

        String sqlGetFinanceDataReimburse = "SELECT * FROM `t_personnel_reimburse` WHERE id in " +
                "(SELECT reimburse FROM `t_finance_account_detail` WHERE org=" + oid + ");";

//        List listReimburse = jdbcTemplate.queryForList(sqlGetFinanceDataReimburse);
        JSONObject result = new JSONObject();
//        result.put("bill",listBill);
//        result.put("detail",listDetail);
//        result.put("reimburse",listReimburse);
        return result;
    }

    /*科目选择的时候触发*/
    @Override
    public JsonResult insertVoucher(User user, SubjectChooseParams params) {
        int SUCCESS = 1;
        int ERROR = 0;
        JsonResult result = new JsonResult();
        Map<String,Object> map = new HashMap<String,Object>();
        int oid = user.getOid();
        params.setOid(oid);
        int belong_peroid = 2;//默认为非本月，如果上月已结账该值更新为1
        String subjectBorrow = params.getSubjectBorrow();
        params.setUser(user);
        boolean isSelected = true;//默认上月没有结账，false表示已经结账
        String prompt = "";//如果当前有已经记账但未结账的数据的话需要给用户提示
        //先判断一下当前是否存在已经记账的数据
        TAccountantSettle settle = settleMapper.judgeCharge(oid);
        if (settle != null) {
            String state = settle.getState();
            String period = settle.getPeriod();
            if ("2".equals(state)) {
                prompt = settle.getPeriod() + "月未结账，请您在本系统中“结账”！！";
                result.setSuccess(ERROR);
                map.put("prompt",prompt);//给用户的提示
                map.put("settleType",1);//表示有记账的数据
                result.setData(map);
                return result;
            }
            else if ("3".equals(state)) {
                Date date = NewDateUtils.changeMonth(new Date(),-1);
                String str = NewDateUtils.dateToString(date,"yyyy-MM");
                if (period.equals(str)) {
                    belong_peroid = 1;//上月已结账，需要结账的月份是本月
                }
            }
        }

        params.setBelong_peroid(belong_peroid);
        //判断上月是否已经结账
        TAccountantSettle settled = judgeSettled(belong_peroid,oid);
        if (settled != null) {
            isSelected = settled.isSettled();//false-已结账，true-未结账
        }

        String subjectLoan = params.getSubjectLoan();
        double price = params.getPrice();
        boolean isEqual = voucherUpdate.checkPrice(subjectBorrow,subjectLoan,price);//借方总金额是否等于贷方总金额/true-相等，false-不相等
        //金额录入的时候如果借方不等于贷方同时不是不与下账，则直接返回失败
        //因为不与下账的时候不用录入金额
        if (!isEqual && params.getApprove_status() != CommonEnum.VoucherApproveEnum.NOT_TO_PAY_PENDING.getIndex())
        {
            map.put("res",0);
            map.put("isSelected",isSelected);
            map.put("settleType",0);
            result.setSuccess(ERROR);
            result.setData(map);
            return result;
        }

        //上月没结账同时金额相等，或选了不与下账都会进行科目选择
        if(isSelected || "".equals(subjectBorrow))
        {
            params.setSource(0);//凭证来自于手动科目选择的财务数据
            JSONObject state = voucherUpdate.insertVoucher(params);//新增凭证
            Integer detailId = params.getDetailId();//财务明细ID
            Integer billDetail = params.getBill_detail();//票据明细ID
            int voucher = state.optInt("id");
            int approveStatus = params.getApprove_status();
            boolean isAccount = approveStatus != CommonEnum.VoucherApproveEnum.NOT_TO_PAY_APPROVE.getIndex();//true-不是不与下账，false-选择了不与下账
            Integer res = dataService.updateVoucher(detailId,billDetail,voucher,isAccount);//更新财务数据
            if (res.compareTo(0) == 0) {//财务数据更新失败
                map.put("res",0);
                map.put("isSelected",true);
                map.put("settleType",0);
                result.setSuccess(ERROR);
            }
            else {//财务数据更新成功
                result.setSuccess(SUCCESS);
                map.put("res",1);
                map.put("isSelected",true);
                map.put("settleType",0);
            }

            result.setData(map);
            return result;

        }
        else
        {
            map.put("res",0);
            map.put("isSelected",false);
            map.put("settleType",0);
        }

        result.setSuccess(ERROR);
        result.setData(map);
        return result;

    }

    /*
    * belong_peroid     1-本月 2-非本月
    * 返回是null或false都表示已结账
    * */
    @Override
    public TAccountantSettle judgeSettled(int belong_peroid, int oid) {
        TAccountantSettle isSelected = voucherUpdate.judgeSettled(belong_peroid,oid);
        return isSelected;
    }

    /*
     * 点击科目设置页中的启用科目名称关联功能时触发
     * 在t_accountant_setting表里增加一条数据，说明已和物料、商品等模块建立联系
     * 科目会随着商品等的新建而新建
     * res   1   关联成功   0   关联失败
     * state Y-开启关联，N-关闭关联
     * */
    @Override
    public int setRelation(User user,String state) {
        int oid = user.getOid();
        TAccountantSetting link = new TAccountantSetting();
        link.setCreator(user.getUserID());
        link.setOrg(oid);
        link.setValue_(state);
        int res = subjectSelectMapper.setRelation(link);
        //生成关联记录
        TaccountantSettingHistoryEntity settingHistoryEntity = new TaccountantSettingHistoryEntity();
        settingHistoryEntity.setOrg(oid);
        settingHistoryEntity.setKey_("kmgl");
        settingHistoryEntity.setValue_(state);
        settingHistoryEntity.setCreator(user.getUserID());
        settingHistoryEntity.setCreateName(user.getUserName());
        settingHistoryEntity.setCreateDate(new Date());
        settingHistoryEntityMapper.insert(settingHistoryEntity);
        if ("Y".equals(state)) {
            //需要把物料模块的老数据转换成会计科目
            TaccountantSettingHistoryEntity lastShutTime = settingHistoryEntityMapper.getLastShutTime(oid);//获取最后一次关闭时间
            if (lastShutTime != null) {
                String time = NewDateUtils.dateToString(lastShutTime.getCreateDate(),"yyyy-MM-dd HH:mm:ss");
                materielService.getAllPdBaseAndMtBase(oid,user,time,settingService,userService,messageService);
            }
            else {
                materielService.getAllPdBaseAndMtBase(oid,user,null,settingService,userService,messageService);
            }
        }

        return res;
    }

    @Override
    public int getRelation(User user) {
        TAccountantSetting link = new TAccountantSetting();
        link.setOrg(user.getOid());
        link = subjectSelectMapper.getRelation(link);
        if (link != null) {
            String value = link.getValue_();
            if ("Y".equals(value))
                return 1;
            else
                return 0;
        }
        else
            return 0;
    }


    /*
    * 数据不仅来自凭证表还来自凭证历史表
    * 但是已批准的只来自凭证表
    * */
    @Override
    public JSONArray queryApprove(int oid, int approve_status) {

        JSONArray resultArray = new JSONArray();
        TAccountantVoucher param = new TAccountantVoucher();
        param.setApproveStatus("" + approve_status);
        param.setOrg(oid);
        List<TAccountantVoucher> listVouchers = voucherMapper.getVouchersFormOriginal(param);

        String vouchers = JSON.toJSONString(listVouchers);
        JSONArray arrayVouchers = new JSONArray(vouchers);
        resultArray = getVouchers(arrayVouchers,resultArray,approve_status);
        //排除已批准的数据
        boolean flag = approve_status != CommonEnum.VoucherApproveEnum.NORMAL_APPROVE.getIndex() &&
                       approve_status != CommonEnum.VoucherApproveEnum.MODIFY_APPROVE.getIndex() &&
                       approve_status != CommonEnum.VoucherApproveEnum.NOT_TO_PAY_APPROVE.getIndex();
        if(flag)
        {
            TAccountantVoucherHistory paramHistory = new TAccountantVoucherHistory();
            paramHistory.setApproveStatus("" + approve_status);
            paramHistory.setOrg(oid);
            List<TAccountantVoucherHistory> listHistoryVouchers = voucherHistoryMapper.getVouchersFormHistory(paramHistory);
            String vouchersHistory = JSON.toJSONString(listHistoryVouchers);
            JSONArray arrayVouchersHistory = new JSONArray(vouchersHistory);
            resultArray = getVouchers(arrayVouchersHistory,resultArray,approve_status);
        }

        return resultArray;
    }

    private JSONArray getVouchers(JSONArray arrayVouchers, JSONArray resultArray, int approve_status) {
        JSONObject item = null;
        for (int i=0;i<arrayVouchers.length();i++)
        {
            JSONObject itemVoucher = arrayVouchers.getJSONObject(i);
            int id = itemVoucher.getInt("id");
            int voucher_id = itemVoucher.optInt("voucherId");
            int bill_detail = itemVoucher.optInt("billDetail");
            int detailId = itemVoucher.optInt("detailId");
            int bill_quantity = itemVoucher.optInt("billQuantity");
            int bill_period = itemVoucher.optInt("billPeriod");
            Object billDate = itemVoucher.opt("billDate");
            int belong_peroid = itemVoucher.optInt("belongPeroid");
            boolean isSettled = itemVoucher.optBoolean("isSettled");
            String create_date = itemVoucher.optString("createDate");
            String summary = itemVoucher.optString("summary");
            String reason = itemVoucher.optString("reason");
            String purpose = itemVoucher.optString("purpose");
            String type = itemVoucher.optString("type");
            String pricetype = itemVoucher.optString("pricetype");
            String source = itemVoucher.optString("source");
            String create_name = itemVoucher.optString("createName");
            String addtime = itemVoucher.optString("addtime");
            String memo = itemVoucher.optString("memo");
            String voucherOperator = itemVoucher.optString("operatorName");
            String kind = itemVoucher.optString("kind");
            String subjectNames = itemVoucher.optString("subjectNames");
            int org = itemVoucher.optInt("org");
            item = new JSONObject();
            item.put("id",id);
            item.put("voucher_id",voucher_id);
            item.put("bill_quantity",bill_quantity);
            item.put("bill_period",bill_period);
            item.put("billDate",billDate);
            item.put("belong_peroid",belong_peroid);
            item.put("bill_detail",bill_detail);
            item.put("detailId",detailId);
            item.put("create_date",create_date);
            item.put("summary",summary);
            item.put("reason",reason);
            item.put("purpose",purpose);
            item.put("type",type);
            item.put("pricetype",pricetype);
            item.put("create_name",create_name);
            item.put("addtime",addtime);
            item.put("memo",memo);
            item.put("subjectNames",subjectNames);
            item.put("kind",kind);
            item.put("voucherOperator",voucherOperator);
            item.put("is_settled",isSettled);
            //判断该凭证是不是直接从正常待选择中点击的不予下帐，如果是的话需要单独取出显示金额
            //approve_status >= 5 是指5、6、9所有和不予下帐有关的值,这些状态可能没有选择科目直接选择的不予下账
            boolean flag = voucher_id == 0 &&
                    (
                            approve_status == CommonEnum.VoucherApproveEnum.NOT_TO_PAY_PENDING.getIndex() ||
                            approve_status == CommonEnum.VoucherApproveEnum.NOT_TO_PAY_FAIL.getIndex() ||
                            approve_status == CommonEnum.VoucherApproveEnum.NOT_TO_PAY_APPROVE.getIndex()
                    );

            if(flag)
            {
                //如果一个凭证没有在历史表里没有对应凭证，则说明该凭证没有进行过科目选择，直接从科目表里取得金额
                TAccountantVoucherSubject param = new TAccountantVoucherSubject();
                param.setVoucherId(id);
                param.setDirection("1");
                List<TAccountantVoucherSubject> subjects = voucherSubjectMapper.getSubjectByDirection(param);
                BigDecimal sum = new BigDecimal(0);
                for (TAccountantVoucherSubject tvs : subjects)
                {
                    sum = sum.add(tvs.getAmount());
                }
                item.put("amount",sum);
            }
            else
            {
                //有科目选择的需要将科目代码，科目名称，发生金额都取出
                item = voucherUpdate.getSubject(item,org,id,voucher_id);
            }

            //取出现金流量表的项目和金额，这也是凭证的一部分,这些数据也有可能来自现金流量暂存历史表
            if(voucher_id > 0)
            {
                TAccountantVoucherSubjectHistory hisparam = new TAccountantVoucherSubjectHistory();
                hisparam.setVoucherId(id);
                List<TAccountantVoucherSubjectHistory> listVoucherSubject = subjectHistoryMapper.getSubjectByVoucher(hisparam);
                JSONArray cashHistory = new JSONArray();
                if(listVoucherSubject != null) {
                    for (TAccountantVoucherSubjectHistory sub : listVoucherSubject) {

                        TAccountantSubjectDetailHistory paramDet = new TAccountantSubjectDetailHistory();
                        paramDet.setSubjectId(sub.getId());
                        TAccountantSubjectDetailHistory subDetail = detailHistoryMapper.getSubjectDetail(paramDet);
                        TAccountantSubjectCashHistory cashParam = new TAccountantSubjectCashHistory();
                        cashParam.setDetailHistoryId(subDetail.getId());
                        TAccountantSubjectCashHistory subjectCash = subjectCashHistoryMapper.getSingleByDetailID(cashParam);
                        if(subjectCash != null)
                        {
                            JSONObject obj = new JSONObject();
                            obj.put("cash",subjectCash.getCredit());
                            obj.put("cashitem",subjectCash.getCashFlowCode());
                            obj.put("cashitemName",subjectCash.getCodeName());
                            cashHistory.put(obj);
                        }
                    }
                    item.put("cashjson",cashHistory);
                }
            }
            else
            {
                TAccountantVoucherSubject param = new TAccountantVoucherSubject();
                param.setVoucherId(id);
                List<TAccountantVoucherSubject> listVoucherSubject = voucherSubjectMapper.getSubjectByVoucher(param);
                JSONArray cashOriginal = new JSONArray();
                if(listVoucherSubject != null) {
                    for (TAccountantVoucherSubject sub : listVoucherSubject) {

                        TAccountantSubjectDetail paramDet = new TAccountantSubjectDetail();
                        paramDet.setSubjectId(sub.getId());
                        TAccountantSubjectDetail subDetail = detailMapper.getSubjectDetail(paramDet);
                        TAccountantSubjectCash cashParam = new TAccountantSubjectCash();
                        cashParam.setDetailId(subDetail.getId());
                        TAccountantSubjectCash subjectCash = subjectCashMapper.getSingleByDetailID(cashParam);
                        if(subjectCash != null)
                        {
                            JSONObject obj = new JSONObject();
                            obj.put("cash",subjectCash.getCredit());
                            obj.put("cashitem",subjectCash.getCashFlowCode());
                            obj.put("cashitemName",subjectCash.getCodeName());
                            cashOriginal.put(obj);
                        }
                    }
                    item.put("cashjson",cashOriginal);
                }
            }

            resultArray.put(item);
        }
        return resultArray;
    }


    /*
    * 凭证审批接口，批准或驳回
    * 每次作出批准都会连带触发多条数据
    * 分两种情况：
    * 1、凭证历史表里没有暂存数据，直接修改凭证状态
    * 2、凭证历史表里有一条暂存记录，当审批时应该和凭证表中的数据联动修改，同时交换位置
    * */
    @Override
    public int setApprove(int id,int approve_status,int voucher_id,String cashjson,User user) {
            //审核人的id和姓名
            int updator = user.getUserID();
            String update_name = user.getUserName();
            int oid = user.getOid();
            if(voucher_id > 0)
            {
                //财务修改的数据被审批通过包括不予下帐，则和凭证表的数据做交换
                if(approve_status == CommonEnum.VoucherApproveEnum.MODIFY_APPROVE.getIndex() || approve_status == CommonEnum.VoucherApproveEnum.NOT_TO_PAY_APPROVE.getIndex())
                {
                    TAccountantVoucher paramVoucherID = new TAccountantVoucher();
                    paramVoucherID.setId(voucher_id);
                    TAccountantVoucher voucher = voucherMapper.getSingle(paramVoucherID);
                    int isChecked = voucher.getIsSettled();

                    //如果原凭证已经结帐则不能选择不予下帐
                    if(isChecked > 0 && approve_status == CommonEnum.VoucherApproveEnum.NOT_TO_PAY_APPROVE.getIndex())
                        return 0;
                    //如果已经结帐，先冲洪,即，产生一条新的凭证把老凭证冲掉，凭证日期是下月最后一天
                    if(isChecked > 0)
                    {
                        int redid = voucherUpdate.redInvoice(voucher_id,oid,approve_status,cashjson);
//                        voucherUpdate.afterApprove(1,redid,oid,cashjson,voucherSubjectMapper,detailMapper,tAccountSubjectMapper,
//                                                    subjectPeroidMapper,cashMapper,subjectCashMapper,voucherMapper);
                    }

                    //数据交换
                    int res = voucherUpdate.swap(id,approve_status,voucher_id,updator,update_name,isChecked);
                    //把修改后的新凭证的现金明细写到明细表中
//                    voucherUpdate.afterApprove(0,res,oid,cashjson,voucherSubjectMapper,detailMapper,tAccountSubjectMapper,
//                                                subjectPeroidMapper,cashMapper,subjectCashMapper,voucherMapper);
                    //如果没有结帐的话需要把已经累加到中间表的现金数减去，正常审批累加一次，修改审批通过累加一次就加多了
//                    if(isChecked == 0)
//                    {
//                        voucherUpdate.subtractCash(voucher_id,subjectCashMapper,voucherSubjectMapper,detailMapper,cashMapper);
//                    }
                    return res;
                }
                else
                {
                    TAccountantVoucherHistory param = new TAccountantVoucherHistory();
                    param.setApproveStatus(approve_status + "");
                    param.setVoucherId(voucher_id);
                    int res = voucherHistoryMapper.updateVouchersHistoryApproveStatus(param);
                    return res;
                }
            }
            else
            {
                /*更新凭证某凭证的审批状态*/
                TAccountantVoucher paramVoucher = new TAccountantVoucher();
                paramVoucher.setApproveStatus(approve_status + "");
                paramVoucher.setUpdator(updator);
                paramVoucher.setUpdateName(update_name);
                paramVoucher.setId(id);
                int res = voucherMapper.update(paramVoucher);


                /*如果凭证审批通过，需要做五件事
                * 1、从科目表得到余额、借方金额，贷方金额
                * 2、将科目详情写进科目周期表
                * 3、将科目详情数据写回科目表

                * 5、更新结帐表的need_trial=1
                * */
                boolean flag = (approve_status == CommonEnum.VoucherApproveEnum.NORMAL_APPROVE.getIndex());
                if(flag)
                {
//                    voucherUpdate.afterApprove(0,id,oid,cashjson,voucherSubjectMapper,detailMapper,tAccountSubjectMapper,
//                                                subjectPeroidMapper,cashMapper,subjectCashMapper,voucherMapper);

                    voucherUpdate.updateNeedTrialField(oid,"1");
                }

                return res;
            }

    }

    /*
    * 这个方法是当审批后的凭证再次修改的时候触发
    * 现将修改后的数据存放到历史表里
    * 历史表的暂存数据将出现在修改待审批里
    * 如果修改后的数据审批通过则将与凭证表中的数据做交换
    * 否则将一直存在于历史表里
    *
    * */
    @Override
    public int insertVoucherHistory(UpdateVoucherParams params) {

        int voucher_id = params.getVoucher_id();
        int id = params.getId();
        int approve_status = params.getApprove_status();
        int belong_peroid = params.getBelong_peroid();
        String reason = params.getReason();
        String subjectBorrow = params.getSubjectBorrow();
        String subjectLoan = params.getSubjectLoan();
        String cashjson = params.getCashjson();
//        int creditQuantity = params.getCreditQuantity();
//        int debitQuantity = params.getDebitQuantity();
//        BigDecimal unitPrice = params.getUnitPrice();
        double price = params.getPrice();
//        boolean isEqual = new VoucherUpdate().checkPrice(subjectBorrow,subjectLoan,price);
//        if (!isEqual)
//        {
//            return 0;
//        }

        //修改的是历史表的数据,id 即为历史表的id
        if(voucher_id > 0)
        {
            /*
            * 如果凭证没有结帐则直接修改
            * 如果已经结帐则不能选择不予下帐
            * 如果该凭证曾经结过张也不能选择不予下帐，也就是说跟这个凭证（id）有关的所有凭证，包括修改的，结帐的，冲洪的，
            * 只要发现其中有一个凭证结过帐则不允许选择不予下帐
            * */
            TAccountantVoucherHistory paramID = new TAccountantVoucherHistory();
            paramID.setId(id);
            TAccountantVoucherHistory voucherHistory = voucherHistoryMapper.getSingle(paramID);
            int check = voucherHistory.getIsSettled();

            /*如果选择的是历史表中的凭证，需要找出原表中的voucher，通过这个字段找出所有相关的凭证，如果这些凭证中有已经结过帐的则不允许选择不予下帐*/
            int originID = voucherHistory.getVoucher();
            List<TAccountantVoucher> list = voucherMapper.getVouchersByVoucherField(originID);

            //如果改凭证已经结帐或者曾经结过帐都不能选择不予下帐
            boolean judgeCondition = (check > 0 && approve_status == CommonEnum.VoucherApproveEnum.NOT_TO_PAY_PENDING.getIndex())
                    ||
                    ((list !=null && list.size() > 1) && approve_status == CommonEnum.VoucherApproveEnum.NOT_TO_PAY_PENDING.getIndex());

            //如果已经结账则不能选择不予下账
            if(judgeCondition)
                return -3;
            else
            {
                String voucherDate = voucherUpdate.getVoucherDate(belong_peroid);

                //如果没有结账或选择的提交，则直接修改历史表的数据
                int org = voucherHistory.getOrg();
                int userID = voucherHistory.getId();
                String userName = voucherHistory.getOperatorName();
                String summary = voucherHistory.getSummary();

                TAccountantVoucherHistory param = new TAccountantVoucherHistory();
                param.setApproveStatus(approve_status + "");
                param.setBelongPeroid(belong_peroid + "");
                param.setReason(reason);
                param.setCreateDate(voucherDate);
                param.setId(id);
                int res = voucherHistoryMapper.update(param);

                TAccountantVoucherSubjectHistory paramSubject = new TAccountantVoucherSubjectHistory();
                TAccountantSubjectDetailHistory detailParam = new TAccountantSubjectDetailHistory();
                TAccountantSubjectCashHistory cashParam = new TAccountantSubjectCashHistory();
                paramSubject.setVoucherId(id);
                List<TAccountantVoucherSubjectHistory> subjects = subjectHistoryMapper.getSubjectByVoucher(paramSubject);
                //删除老的凭证科目
                voucherUpdate.deleteHistorySubjectAndDetail(subjects,detailParam,cashParam);
                //新建科目和详情，现金明细
                voucherUpdate.newHistorySubjectAndDetail(voucherDate,subjectBorrow,subjectLoan,org,id,userID,userName,summary,cashjson);

                return res;
            }
        }
        else
        {

            //得到公司id，目的是计算最近一次未结帐的凭证日期
            TAccountantVoucher paramID = new TAccountantVoucher();
            paramID.setId(id);
            TAccountantVoucher voucher = voucherMapper.getSingle(paramID);
            int oid = voucher.getOrg();
            int checkSettled = voucher.getIsSettled();

             /*找出voucher，通过这个字段找出所有相关的凭证，如果这些凭证中有已经结果张的则不允许选择不予下帐*/
            int originID = voucher.getVoucher();

            List<TAccountantVoucher> list = voucherMapper.getVouchersByVoucherField(originID);
            //如果改凭证已经结帐则不许选择不予下帐；
            //如果改凭证曾经结过帐（voucher值相等的凭证数大于1）也不许选择不予下帐
            boolean judgeCondition = (checkSettled > 0 && approve_status == CommonEnum.VoucherApproveEnum.NOT_TO_PAY_PENDING.getIndex()) ||
                    ((list !=null && list.size() > 1) && approve_status == CommonEnum.VoucherApproveEnum.NOT_TO_PAY_PENDING.getIndex());

            /*
            * 修改的是凭证表的数据
            * 如果要修改的凭证在历史表里存在且还未审批那么此次修改不被允许
            * 如果要修改的凭证在历史表里已存在且已经审批，即，已驳回（3，6，8），那么在修改的时候要先把历史表里的已驳回的数据删除掉
            * */

            String statusNow = voucher.getApproveStatus();

            TAccountantVoucherHistory checkParam = new TAccountantVoucherHistory();
            checkParam.setVoucherId(id);
            TAccountantVoucherHistory check = voucherHistoryMapper.getVoucherHistoryByVoucherID(checkParam);
            if(check != null)
            {
                String status = check.getApproveStatus();
                //等于1 或 4 或 5
                boolean flag = status.equals(CommonEnum.VoucherApproveEnum.NORMAL_PENDING.getIndex() + "") ||
                               status.equals(CommonEnum.VoucherApproveEnum.NOT_TO_PAY_PENDING.getIndex() + "") ||
                               status.equals(CommonEnum.VoucherApproveEnum.MODIFY_PENDING.getIndex() + "");
                if(flag)
                {
                    //上次修改还未审批,则返回失败
                    return 0;
                }
                if(judgeCondition)
                    return -3;
                //之前修改的那条数据已经被驳回，同时要修改的这个原凭证也是被驳回的状态，那么要把历史表里被驳回的那条删除掉，然后直接在原屏增基础上直接修改
                boolean flag2 = (status.equals(CommonEnum.VoucherApproveEnum.NORMAL_FAIL.getIndex() + "") ||
                                status.equals(CommonEnum.VoucherApproveEnum.MODIFY_FAIL.getIndex() + "") ||
                                status.equals(CommonEnum.VoucherApproveEnum.NOT_TO_PAY_FAIL.getIndex() + "")) &&
                                (statusNow.equals(CommonEnum.VoucherApproveEnum.NORMAL_FAIL.getIndex() + "") ||
                                 statusNow.equals(CommonEnum.VoucherApproveEnum.MODIFY_FAIL.getIndex() + "") ||
                                 statusNow.equals(CommonEnum.VoucherApproveEnum.NOT_TO_PAY_FAIL.getIndex() + ""));

                if(flag2)
                {
                    //如果凭证表的数据再次修改，得先把历史表的数据删除掉
                    deleteVoucher(id,voucher_id);
                    //删除后直接修改
                    updateVoucher(params);
                    return 1;

                }
                boolean flag3 = statusNow.equals(CommonEnum.VoucherApproveEnum.NORMAL_APPROVE.getIndex() + "") ||
                                statusNow.equals(CommonEnum.VoucherApproveEnum.MODIFY_APPROVE.getIndex() + "") ||
                                statusNow.equals(CommonEnum.VoucherApproveEnum.NOT_TO_PAY_APPROVE.getIndex() + "");
                if(flag3)
                {
                    deleteVoucher(id,voucher_id);
                    //多次修改
                    newHistoryVoucher(params,oid);
                    return 1;
                }
            }

            else
            {
                /*
                * 修改的是凭证表的数据，同时历史表里没有相应的修改数据
                * 先判断是否已结帐，如果已结帐在选择不予下帐则不被允许
                * 否则在历史表里新增数据
                * */

                String status = voucher.getApproveStatus();

                //要修改的凭证已经结账，选择的是不予下账，同时改凭证又是之前已经审批通过的，则返回失败
                if(judgeCondition)
                    return -3;
                //如果该凭证已经被驳回，则直接修改
                boolean flag = status.equals(CommonEnum.VoucherApproveEnum.NORMAL_FAIL.getIndex() + "") ||
                               status.equals(CommonEnum.VoucherApproveEnum.NOT_TO_PAY_FAIL.getIndex() + "") ||
                               status.equals(CommonEnum.VoucherApproveEnum.MODIFY_FAIL.getIndex() + "");

                if(flag)
                {
                    updateVoucher(params);
                }
                else {
                    //如果修改的是已审核通过的凭证，需要在历史表里新增
                    newHistoryVoucher(params,oid);
                }
                return 1;
            }
        }

        return 0;
    }

    private void deleteVoucher(int id,int voucher_id)
    {

        TAccountantVoucherHistory paramVoucherID = new TAccountantVoucherHistory();
        paramVoucherID.setVoucherId(id);
        TAccountantVoucherHistory voucherHistory = voucherHistoryMapper.getVoucherHistoryByVoucherID(paramVoucherID);
        int voucherID = voucherHistory.getId();

        TAccountantVoucherSubjectHistory paramSubject = new TAccountantVoucherSubjectHistory();
        TAccountantSubjectCashHistory cashParam = new TAccountantSubjectCashHistory();
        paramSubject.setVoucherId(id);
        TAccountantSubjectDetailHistory detailParam = new TAccountantSubjectDetailHistory();
        List<TAccountantVoucherSubjectHistory> subjects = subjectHistoryMapper.getSubjectByVoucher(paramSubject);
        voucherUpdate.deleteHistorySubjectAndDetail(subjects,detailParam,cashParam);
        voucherHistoryMapper.delete(voucherHistory);

    }

    private void updateVoucher(UpdateVoucherParams params)
    {
        int belong_peroid = params.getBelong_peroid();
        int approve_status = params.getApprove_status();
        String reason = params.getReason();
        int id = params.getId();


        String voucherDate = voucherUpdate.getVoucherDate(belong_peroid);

        TAccountantVoucher param = new TAccountantVoucher();
        param.setApproveStatus(approve_status + "");
        param.setBelongPeroid(belong_peroid + "");
        param.setReason(reason);
        param.setCreateDate(voucherDate);
        param.setId(id);
        voucherMapper.update(param);

        TAccountantVoucher voucher = voucherMapper.getSingle(param);

        int org = voucher.getOrg();
        int userID = voucher.getId();
        String userName = voucher.getOperatorName();
        String summary = voucher.getSummary();

        TAccountantVoucherSubject paramSubject = new TAccountantVoucherSubject();
        TAccountantSubjectDetail detailParam = new TAccountantSubjectDetail();
        TAccountantSubjectCash cashParam = new TAccountantSubjectCash();
        paramSubject.setVoucherId(id);
        List<TAccountantVoucherSubject> subjects = voucherSubjectMapper.getSubjectByVoucher(paramSubject);
        //删除老的凭证科目
        voucherUpdate.deleteSubjectAndDetail(subjects,detailParam,cashParam);
        //新建科目和详情，现金明细
        String subjectBorrow = params.getSubjectBorrow();
        String subjectLoan = params.getSubjectLoan();
        String cashjson = params.getCashjson();
        voucherUpdate.newSubjectAndDetail(voucherDate,subjectBorrow,subjectLoan,org,id,userID,userName,summary,cashjson);

        //如果要修改成审批通过，同时belong_peroid是2，需要更新结帐表的need_trial=1，只要上月的凭证数据发生变化就需要修改need_trial=1
        boolean flag = approve_status == CommonEnum.VoucherApproveEnum.NORMAL_APPROVE.getIndex() ||
                approve_status == CommonEnum.VoucherApproveEnum.MODIFY_APPROVE.getIndex();
        voucherUpdate.updateNeedTrialField(org,"1");


    }

    private void newHistoryVoucher(UpdateVoucherParams params, int org)
    {
        SimpleDateFormat simpleFormat =  new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String now = simpleFormat.format(new Date());
        int id = params.getId();
        int belong_peroid = params.getBelong_peroid();
        String subjectBorrow = params.getSubjectBorrow();
        String subjectLoan = params.getSubjectLoan();
        int approve_status = params.getApprove_status();
        String reason = params.getReason();
        String cashjson = params.getCashjson();
//        double creditQuantity = params.getCreditQuantity();
//        double debitQuantity = params.getDebitQuantity();
//        double unitPrice = params.getUnitPrice();

        //修改已通过的凭证或修改被驳回的凭证后回到修改待审批
        String voucherDate = voucherUpdate.getVoucherDate(belong_peroid);
        //将修改后的凭证存到历史表里

        TAccountantVoucher paramVoucher = new TAccountantVoucher();
        paramVoucher.setId(id);
        TAccountantVoucher voucher = voucherMapper.getSingle(paramVoucher);
        String creatorName = voucher.getCreateName();
        int creator = voucher.getCreator();
        String summary = voucher.getSummary();
        TAccountantVoucherHistory paramVoucherHistory = new TAccountantVoucherHistory();
        paramVoucherHistory.setOrg(voucher.getOrg());
        paramVoucherHistory.setVoucher(voucher.getVoucher());
        paramVoucherHistory.setBelongPeroid(belong_peroid + "");
        paramVoucherHistory.setBookDate(voucher.getBookDate());
        paramVoucherHistory.setMode(voucher.getMode());
        paramVoucherHistory.setType(voucher.getType());
        paramVoucherHistory.setCategory(voucher.getCategory());
        paramVoucherHistory.setSn(voucher.getSn());
        paramVoucherHistory.setSettlement(voucher.getSettlement());
        paramVoucherHistory.setCheckNo(voucher.getCheckNo());
        paramVoucherHistory.setIsSettled((byte) 0);
        paramVoucherHistory.setIsAccount(voucher.getIsAccount());
        paramVoucherHistory.setReason(reason);
        paramVoucherHistory.setBillDetail(voucher.getBillDetail());
        paramVoucherHistory.setDetailId(voucher.getDetailId());
        paramVoucherHistory.setSummary(voucher.getSummary());
        paramVoucherHistory.setOperator(voucher.getOperator());
        paramVoucherHistory.setOperatorName(voucher.getOperatorName());
        paramVoucherHistory.setOperatorFinance(voucher.getOperatorFinance());
        paramVoucherHistory.setMemo(voucher.getMemo());
        paramVoucherHistory.setCreator(voucher.getCreator());
        paramVoucherHistory.setCreateName(voucher.getCreateName());
        paramVoucherHistory.setCreateDate(voucherDate);
        paramVoucherHistory.setApproveStatus(approve_status + "");
        paramVoucherHistory.setSource(voucher.getSource());
        paramVoucherHistory.setVoucherId(id);
        paramVoucherHistory.setPricetype(voucher.getPricetype());
        paramVoucherHistory.setPurpose(voucher.getPurpose());
        paramVoucherHistory.setBillQuantity(voucher.getBillQuantity());
        paramVoucherHistory.setBillPeriod(voucher.getBillPeriod());
        if (voucher.getBillDate() != null) {
            paramVoucherHistory.setBillDate(voucher.getBillDate());
        }
        paramVoucherHistory.setKind(voucher.getKind());
        paramVoucherHistory.setAddtime(now);
        paramVoucherHistory.setAuditor(voucher.getAuditor());
        paramVoucherHistory.setAuditorName(voucher.getAuditorName());
        paramVoucherHistory.setAuditDate(voucher.getAuditDate());
        voucherHistoryMapper.insert(paramVoucherHistory);
        int res = paramVoucherHistory.getId();

        //将对应的科目存放在历史表里，此处存放的是借方
        List<Integer> borids = new ArrayList<Integer>();
        JSONArray borrowArray = new JSONArray(subjectBorrow);
        if(borrowArray != null)
        {
            TAccountantSubjectDetailHistory detHisParamB = new TAccountantSubjectDetailHistory();
            TAccountantVoucherSubjectHistory subHistoryParam = new TAccountantVoucherSubjectHistory();
            for (int i=0;i<borrowArray.length();i++)
            {
                JSONObject bor = borrowArray.getJSONObject(i);
                String subBor = bor.optString("subject");
                String subjectNames = bor.optString("subjectNames");
                double subPrice = bor.optDouble("price",0);
                int pid = bor.optInt("productID");
                String lastSub = subBor;
                BigDecimal credit = BigDecimal.valueOf(subPrice);
                subHistoryParam.setOrg(org);
                subHistoryParam.setVoucherId(res);
                subHistoryParam.setSubject(subBor);
                subHistoryParam.setSubjectNames(subjectNames);
                subHistoryParam.setDirection("1");
                subHistoryParam.setAmount(credit);
                subHistoryParam.setCreator(creator);
                subHistoryParam.setCreateName(creatorName);
                subHistoryParam.setCreateDate(now);
                subHistoryParam.setApproveStatus("1");
                subHistoryParam.setReimburseBillItem(pid);
                subjectHistoryMapper.insert(subHistoryParam);
                int resB = subHistoryParam.getId();

                double creditQuantity = bor.optDouble("creditQuantity",0);
                double debitQuantity = bor.optDouble("debitQuantity",0);
                double unitPrice = bor.optDouble("unitPrice",0);
                detHisParamB.setSubjectId(resB);
                detHisParamB.setSubject(subBor);
                detHisParamB.setType("1");
                detHisParamB.setSummary(summary);
                detHisParamB.setCredit(credit);
                detHisParamB.setCreator(creator);
                detHisParamB.setCreateName(creatorName);
                detHisParamB.setCreateDate(now);
                detHisParamB.setCreditQuantity(creditQuantity);
                detHisParamB.setDebitQuantity(debitQuantity);
                detHisParamB.setUnitPrice(unitPrice);

                if(subBor.contains("-"))
                {
                    lastSub = subBor.substring(subBor.lastIndexOf("-") + 1);
                }

                //得到科目的余额方向
                TAccountantSubjectEntity s = new TAccountantSubjectEntity();
                s.setSubject(lastSub);
                s.setOrg(org);
                TAccountantSubjectEntity list = tAccountSubjectMapper.listByFirstSubject(s);
                String borDirec = null;
                if(list != null)
                {
                    borDirec = list.getBalanceDirection();
                }

                //如果改科目是借方科目，则科目余额为借方减贷方,否则（即，在借方科目选择的地方选择了贷方科目）
                if("1".equals(borDirec))
                {
                    detHisParamB.setBalance(credit);
                    detHisParamB.setBalanceDirection("1");
                }
                else
                {
                    detHisParamB.setBalance(credit.negate());
                    detHisParamB.setBalanceDirection("2");
                }
                detailHistoryMapper.insert(detHisParamB);
                int detailBorrowID = detHisParamB.getId();
                if (lastSub.startsWith("1001") || lastSub.startsWith("1002"))
                {
                    borids.add(detailBorrowID);
                }
            }
        }

        //将对应的科目存放在历史表里，此处存放的是贷方
        List<Integer> loanids = new ArrayList<Integer>();
        JSONArray loanArray = new JSONArray(subjectLoan);
        if(loanArray != null)
        {
            TAccountantVoucherSubjectHistory loanHistoryParam = new TAccountantVoucherSubjectHistory();
            TAccountantSubjectDetailHistory detHisParamL = new TAccountantSubjectDetailHistory();
            for (int i=0;i<loanArray.length();i++)
            {
                JSONObject loan = loanArray.getJSONObject(i);
                String loanSub = loan.optString("subject");
                String subjectNames = loan.optString("subjectNames");
                double loanPrice = loan.optDouble("price",0);
                String lastSub = loanSub;
                BigDecimal debit = BigDecimal.valueOf(loanPrice);
                loanHistoryParam.setOrg(org);
                loanHistoryParam.setVoucherId(res);
                loanHistoryParam.setSubject(loanSub);
                loanHistoryParam.setSubjectNames(subjectNames);
                loanHistoryParam.setDirection("2");
                loanHistoryParam.setAmount(debit);
                loanHistoryParam.setCreator(creator);
                loanHistoryParam.setCreateName(creatorName);
                loanHistoryParam.setCreateDate(now);
                loanHistoryParam.setApproveStatus("1");
                subjectHistoryMapper.insert(loanHistoryParam);
                int resL = loanHistoryParam.getId();

                if(loanSub.contains("-"))
                {
                    lastSub = loanSub.substring(loanSub.lastIndexOf("-") + 1);
                }

                TAccountantSubjectEntity l = new TAccountantSubjectEntity();
                l.setSubject(lastSub);
                l.setOrg(org);
                TAccountantSubjectEntity listL = tAccountSubjectMapper.listByFirstSubject(l);
                String loanDirec = null;
                if(listL != null)
                {
                    loanDirec = listL.getBalanceDirection();
                }
                double creditQuantity = loan.optDouble("creditQuantity",0);
                double debitQuantity = loan.optDouble("debitQuantity",0);
                double unitPrice = loan.optDouble("unitPrice",0);
                detHisParamL.setSubjectId(resL);
                detHisParamL.setSubject(loanSub);
                detHisParamL.setType("1");
                detHisParamL.setSummary(summary);
                detHisParamL.setDebit(debit);
                detHisParamL.setCreator(creator);
                detHisParamL.setCreateName(creatorName);
                detHisParamL.setCreateDate(now);
                detHisParamL.setCreditQuantity(creditQuantity);
                detHisParamL.setDebitQuantity(debitQuantity);
                detHisParamL.setUnitPrice(unitPrice);
                if("2".equals(loanDirec))
                {
                    detHisParamL.setBalance(debit);
                    detHisParamL.setBalanceDirection("2");
                }
                else
                {
                    detHisParamL.setBalance(debit.negate());
                    detHisParamL.setBalanceDirection("1");
                }
                detailHistoryMapper.insert(detHisParamL);
                int detailLoanID = detHisParamL.getId();
                if (lastSub.startsWith("1001") || lastSub.startsWith("1002"))
                {
                    loanids.add(detailLoanID);
                }
            }
        }

        if (cashjson != null && !"".equals(cashjson))
        {
            /*新凭证生成的时候需要在现金明细表里生成状态为1的明细，即审批状态为待审批*/
            voucherUpdate.insertHistoryCashDetail(res,cashjson,borids,loanids);
        }
    }



}
