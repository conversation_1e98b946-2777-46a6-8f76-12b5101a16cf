<template>
  <div class="nameagent" v-show="nameditio === 0">
    <div>
      <div class="main rolor">
        <el-row>
          <el-col :span="8">
            <div class="grid-content bg-purple plence" id="describe">
              装备器具现有<span class="number">{{listn1.length}}</span>种：具体如下
            </div> </el-col>
          <el-col :span="8"><div class="grid-content bg-purple-light"></div> </el-col>
          <el-col :span="8"><div class="grid-content bg-purple-light">
            <el-row>
              <el-col :span="4" class="plpen"><div class="grid-content bg-purple">
                <span class="catch">查找</span>
              </div> </el-col>
              <el-col :span="16"><div class="grid-content bg-purple-light">
                <el-row>
                  <el-col :span="23"><div class="grid-content bg-purple">
                    <div>
                      <el-row>
                        <el-col :span="20"><div class="grid-content bg-purple">
                          <el-input placeholder="请输入名称中的关键字" v-model="input3" class="input-with-select">
                            <!--                        <template #append>-->
                            <!--                          <el-button @click="" id="seachlook" type="primary" class="ty-btn-blue">确定</el-button>-->
                            <!--                        </template>-->
                          </el-input>
                        </div> </el-col>
                        <el-col :span="4"><div class="grid-content bg-purple-light">
                          <el-button @click="getsecher()" id="seachlook" type="primary">确定</el-button>
                        </div> </el-col>
                      </el-row>
                    </div>
                  </div> </el-col>
                  <el-col :span="1"><div class="grid-content bg-purple-light"></div> </el-col>
                </el-row>
              </div> </el-col>
              <el-col :span="4"><div class="grid-content bg-purple">
                <el-button @click="addname()" id="addnewna" type="primary">新增名称</el-button>
              </div> </el-col>
            </el-row>
          </div> </el-col>
        </el-row>
        <div class="equipmentdata upolence">
          <table class="ty-table ty-table-control">
            <thead>
            <tr>
              <td>装备器具名称</td>
              <td>型号</td>
              <td>来源</td>
              <td>单位</td>
              <td>数量</td>
              <td>创建</td>
              <td>操作</td>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(item,index) in tableData" :key="index">
              <td>{{ item.fullName }}</td>
              <td>{{ item.modelCount }}</td>
              <td>{{ item.supplierCount }}</td>
              <td>{{ item.units }}</td>
              <td class="ty-td-control">
                <span class="ty-color-blue" @click="quantitymess(item,1)">{{ item.quantity }}</span>
              </td>
              <td>{{ item.createDate }}</td>
              <td class="ty-td-control">
                <span class="ty-color-blue" @click="upeqname(item,1)">修改</span>
                <span class="ty-color-blue" @click="upnamerecord(item,1)">名称修改记录</span>
              </td>
            </tr>
            </tbody>
          </table>
          <!--          <el-table :data="tableData" border class="ty-td-control" style="width: 100%;">-->
          <!--            <el-table-column fixed prop="fullName" label="装备器具名称" width="170" align="center"></el-table-column>-->
          <!--            <el-table-column prop="modelCount" label="型号" width="170" align="center"></el-table-column>-->
          <!--            <el-table-column prop="supplierCount" label="来源" width="170" align="center"></el-table-column>-->
          <!--            <el-table-column prop="units" label="单位" width="170" align="center"></el-table-column>-->
          <!--            <el-table-column prop="quantity" label="数量" width="170" align="center">-->
          <!--              <template #default="scope">-->
          <!--                <div class="ty-td-control">-->
          <!--                  <span class="ty-color-blue" @click="quantitymess(scope.row,1)">{{ scope.row.quantity }}</span>-->
          <!--                </div>-->
          <!--&lt;!&ndash;                <el-button class="impont-hover" type="typcolor" @click="quantitymess(scope.row,1)">{{ scope.row.quantity }}</el-button>&ndash;&gt;-->
          <!--              </template>-->
          <!--            </el-table-column>-->
          <!--            <el-table-column prop="createDate" label="创建" width="" align="center"></el-table-column>-->
          <!--            <el-table-column label="操作" width="200" align="center">-->
          <!--              <template #default="scope">-->
          <!--                <div class="ty-td-control">-->
          <!--                  <span class="ty-color-blue" @click="upeqname(scope.row,1)">修改</span>-->
          <!--                  <span class="ty-color-blue" @click="upnamerecord(scope.row,1)">名称修改记录</span>-->
          <!--                </div>-->
          <!--&lt;!&ndash;                <el-button class="impont-hover" @click="upeqname(scope.row,1)" type="typcolor" size="small">修改</el-button>&ndash;&gt;-->
          <!--&lt;!&ndash;                <el-button class="impont-hover" @click="upnamerecord(scope.row,1)" type="typcolor"&ndash;&gt;-->
          <!--&lt;!&ndash;                           size="small">名称修改记录</el-button>&ndash;&gt;-->
          <!--              </template>-->
          <!--            </el-table-column>-->
          <!--          </el-table>-->
          <!--          <table class="ty-table ty-table-control">-->
          <!--              <tr>-->
          <!--              <td>装备器具名称</td>-->
          <!--              <td>型号</td>-->
          <!--              <td>来源</td>-->
          <!--              <td>单位</td>-->
          <!--              <td>数量</td>-->
          <!--              <td>创建</td>-->
          <!--              <td>操作</td>-->
          <!--            </tr>-->
          <!--              <tr v-for="(itemn1,indexn1) in listn1" :key="indexn1">-->
          <!--                <td>{{ itemn1.fullName }}</td>-->
          <!--                <td>-->
          <!--                  <span class="chox2">{{ itemn1.modelCount }}</span>-->
          <!--                </td>-->
          <!--                <td>-->
          <!--                  <span class="choc2">{{ itemn1.supplierCount }}</span>-->
          <!--                </td>-->
          <!--                <td>{{ itemn1.units }}</td>-->
          <!--                <td>-->
          <!--                  <span class="tunch2 ty-color-blue funBtn" @click="">{{ itemn1.quantity }}</span>-->
          <!--                </td>-->
          <!--                <td>{{ itemn1.createDate }}</td>-->
          <!--                <td>-->
          <!--                  <span class="ty-color-blue fuBtn upeqname" @click="upeqname()">修改名称</span>-->
          <!--                  <span class="ty-color-blue funBtn" @click="">名称修个记录</span>-->
          <!--                  <span class="hd">{{ JSON.stringify(itemn1) }}</span>-->
          <!--                </td>-->
          <!--              </tr>-->
          <!--          </table>-->
        </div>
      </div>
      <TyPage v-if="pageShow"
              :curPage="pageInfo.currentPageNo" :pageSize="pageInfo.pageSize"
              :allPage="pageInfo.totalPage" :pageClickFun="pageClick"></TyPage>
    </div>
    <TyDialog v-if="delVisiblen1" width="500" :dialogTitle="dialogTitle" color="blue" :dialogHide="hideFun1">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun1">取消</el-button>
        <el-button class="bounce-ok" @click="tipOk1(1,1)" type="primary" v-show="addorupe === 1" id="addsubmit">确定</el-button>
        <el-button class="bounce-ok" @click="tipOk1(2,1)" type="primary" v-show="addorupe === 2" id="upmodify">确定</el-button>
      </template>
      <template #dialogBody>
        <div>
          <el-row class="plencen">
            <el-col :span="24"><div class="grid-content bg-purple-dark">
              <span>装备器具的名称</span>
            </div></el-col>
          </el-row>
          <el-input
              type="text"
              placeholder="请输入内容"
              v-model="text"
              maxlength="8"
              show-word-limit
              ref="unitern1"
          >
          </el-input>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="delVisiblen2" width="500" dialogTitle="装备器具名称的修改记录" color="blue" :dialogHide="hideFun2">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="hideFun2" type="primary">关闭</el-button>
      </template>
      <template #dialogBody>
        <div>
          <el-table :data="tableData2" border>
            <el-table-column prop="msgmade" label="事件" width="140" align="center"> </el-table-column>
            <el-table-column label="操作" width="210" align="center">
              <template #default="scope">
                {{scope.row.createName}}&nbsp;{{scope.row.createDate}}
              </template>
            </el-table-column>
            <el-table-column prop="fullName" label="操作后的名称" align="center"> </el-table-column>
          </el-table>
        </div>
      </template>
    </TyDialog>
  </div>
  <div class="quantitydetails" v-show="nameditio === 1">
    <div class="plencew">
      <el-row>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="gobcken(1)">返回</span>
      </el-row>
      <el-row class="upolence">
        <el-col :span="8"><div class="grid-content bg-purple upolence2" v-for="(itemt,indext) in listt" :key="indext">
          <span>{{ itemt.name }}</span>共<span>{{ itemt.numb }}</span>台（套），按型号展示如下：
        </div> </el-col>
        <el-col :span="6"><div class="grid-content bg-purple-light"></div> </el-col>
        <el-col :span="10"><div class="grid-content bg-purple-light">
          <el-row>
            <el-col :span="8"><div class="grid-content bg-purple"></div> </el-col>
            <el-col :span="8"><div class="grid-content bg-purple-light"></div> </el-col>
            <el-col :span="8"><div class="grid-content bg-purple">
              <el-row>
                <el-col :span="12"><div class="grid-content bg-purple"></div> </el-col>
                <el-col :span="12"><div class="grid-content bg-purple-light">
                  <el-button type="text" @click="getlookall()">直接查看全部</el-button>
                </div> </el-col>
              </el-row>
            </div> </el-col>
          </el-row>
        </div> </el-col>
      </el-row>
      <div class="tatle upolence">
        <table class="ty-table ty-table-control">
          <thead>
          <tr>
            <td>型号</td>
            <td>数量（台/套）</td>
            <td>来源</td>
            <td>操作</td>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item,index) in tableData3" :key="index">
            <td>{{ item.modelName }}</td>
            <td>{{ item.count }}</td>
            <td>{{ item.supplierCount }}</td>
            <td class="ty-td-control">
              <span class="ty-color-blue" @click="lookmore(item)">查看</span>
            </td>
          </tr>
          </tbody>
        </table>
        <!--        <el-table :data="tableData3" border class="ty-td-control" style="width: 100%;">-->
        <!--          <el-table-column fixed prop="modelName" label="型号" align="center" width="200"></el-table-column>-->
        <!--          <el-table-column prop="count" label="数量（台/套）"  align="center" width="200"></el-table-column>-->
        <!--          <el-table-column prop="supplierCount" label="来源"  align="center"></el-table-column>-->
        <!--          <el-table-column fixed="right" label="操作"  align="center" width="200">-->
        <!--            <template #default="scope">-->
        <!--              <div class="ty-td-control">-->
        <!--                <span class="ty-color-blue" @click="lookmore(scope.row)">查看</span>-->
        <!--              </div>-->
        <!--&lt;!&ndash;              <el-button class="impont-hover" @click="lookmore(scope.row)" type="typcolor" size="small">查看</el-button>&ndash;&gt;-->
        <!--            </template>-->
        <!--          </el-table-column>-->
        <!--        </el-table>-->
      </div>
    </div>
  </div>
  <div class="screenboder" v-show="nameditio === 2">
    <div class="plencew">
      <el-row>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="gobcken(2)">返回</span>
      </el-row>
      <el-row class="upolence">
        <el-col :span="8"><div class="grid-content bg-purple upolence2" v-for="(item4,index4) in listr4" :key="index4">
          <span>{{ item4.modelName }}</span>的<span>{{ item4.name }}</span>共<sapn>{{ item4.longr }}</sapn>台（套），具体如下：
        </div> </el-col>
        <el-col :span="8"><div class="grid-content bg-purple-light"></div> </el-col>
        <el-col :span="8"><div class="grid-content bg-purple">
          <el-row>
            <el-col :span="4"><div class="grid-content bg-purple"></div> </el-col>
            <el-col :span="4" class="plpen"><div class="grid-content bg-purple-light">
              <span class="screen">筛选</span>
            </div> </el-col>
            <el-col :span="16"><div class="grid-content bg-purple">
              <el-select v-model="value" placeholder="请选择" @change="chonseotherq">
                <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                >
                </el-option>
              </el-select>
            </div> </el-col>
          </el-row>
        </div> </el-col>
      </el-row>
      <div class="table4 upolence">
        <table class="ty-table ty-table-control">
          <thead>
          <tr>
            <td>系统赋予的编码</td>
            <td>装备器具编号</td>
            <td>来源</td>
            <td>所属类别</td>
            <td>创建</td>
            <td>参与加工的产品</td>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item,index) in tableData4" :key="index">
            <td>{{ item.lpadId }}</td>
            <td>{{ item.modelCode }}</td>
            <td>{{ item.supplierName }}</td>
            <td>{{ item.path }}</td>
            <td>{{ item.createName }}&nbsp;{{ item.createDate }}</td>
            <td class="ty-td-control">
              <span class="ty-color-blue" @click="lookallmore(item,1)">{{ item.productCount }}</span>
            </td>
          </tr>
          </tbody>
        </table>
        <!--        <el-table :data="tableData4" border class="ty-td-control" style="width: 100%;">-->
        <!--          <el-table-column fixed prop="lpadId" label="系统赋予的编码" width="130" align="center"></el-table-column>-->
        <!--          <el-table-column prop="modelCode" label="装备器具编号" width="130" align="center"></el-table-column>-->
        <!--          <el-table-column prop="supplierName" label="来源" width="" align="center"></el-table-column>-->
        <!--          <el-table-column prop="path" label="所属类别" width="130" align="center"></el-table-column>-->
        <!--          <el-table-column label="创建" width="220" align="center">-->
        <!--            <template #default="scope">-->
        <!--              {{ scope.row.createName }}&nbsp;{{ scope.row.createDate }}-->
        <!--            </template>-->
        <!--          </el-table-column>-->
        <!--          <el-table-column prop="productCount" label="参与加工的产品" width="160" align="center">-->
        <!--            <template #default="scope">-->
        <!--              <div class="ty-td-control">-->
        <!--                <span class="ty-color-blue" @click="lookallmore(scope.row,1)">{{ scope.row.productCount }}</span>-->
        <!--              </div>-->
        <!--&lt;!&ndash;              <el-button class="impont-hover" type="typcolor" @click="lookallmore(scope.row,1)">{{ scope.row.productCount }}</el-button>&ndash;&gt;-->
        <!--            </template>-->
        <!--          </el-table-column>-->
        <!--        </el-table>-->
      </div>
    </div>
  </div>
  <div class="drawingnumber" v-show="nameditio === 3">
    <div class="plencew">
      <el-row>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5"  @click="gobcken(3)">返回</span>
      </el-row>
      <el-row class="upolence">
        <el-col :span="24"><div class="grid-content bg-purple-dark">
          <!--          <span>装备信息：系统赋予的编码/装备器具编号/名称/型号</span>-->
          <span>装备信息：{{ setbox.lpadId }}/{{ setbox.modelCode }}/{{ setbox.equipmentName }}/{{ setbox.modelName }}</span>
          <br />
          <span>本装备参与加工的产品：</span>
        </div></el-col>
      </el-row>
      <div class="table5 upolence">
        <table class="ty-table ty-table-control">
          <thead>
          <tr>
            <td>图号（代号）</td>
            <td>名称</td>
            <td>规格</td>
            <td>型号</td>
            <td>计量单位</td>
            <td>本装备用于本产品的操作时间</td>
            <td>操作</td>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item,index) in tableData5" :key="index"></tr>
          </tbody>
        </table>
        <!--        <el-table :data="tableData5" border class="ty-td-control" style="width: 100%;">-->
        <!--          <el-table-column fixed prop="" label="图号（代号）" width="150" align="center"></el-table-column>-->
        <!--          <el-table-column prop="" label="名称" width="150" align="center"></el-table-column>-->
        <!--          <el-table-column prop="" label="规格" width="150" align="center"></el-table-column>-->
        <!--          <el-table-column prop="" label="型号" width="150" align="center"></el-table-column>-->
        <!--          <el-table-column prop="" label="计量单位" width="150" align="center"></el-table-column>-->
        <!--          <el-table-column prop="" label="本装备用于本产品的操作时间" width="" align="center"></el-table-column>-->
        <!--          <el-table-column prop="" label="操作" width="170" align="center"></el-table-column>-->
        <!--        </el-table>-->
      </div>
    </div>
  </div>
  <div class="categorycode" v-show="nameditio === 4">
    <div class="plencew">
      <el-row>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="gobcken(4)">返回</span>
      </el-row>
      <el-row class="upolence">
        <el-col :span="24"><div class="grid-content bg-purple-dark" v-for="(item5,index5) in listr5" :key="index5">
          <span>{{ item5.modelName }}</span>共<span>{{ item5.longr }}</span>台（套），具体如下:
        </div></el-col>
      </el-row>
      <div class="table6 upolence">
        <table class="ty-table ty-table-control">
          <thead>
          <tr>
            <td>系统赋予的编码</td>
            <td>装备器具编号</td>
            <td>型号</td>
            <td>来源</td>
            <td>所属类别</td>
            <td>创建</td>
            <td>参与加工的产品</td>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item,index) in tableData6" :key="index">
            <td>{{ item.lpadId }}</td>
            <td>{{ item.modelCode }}</td>
            <td>{{ item.modelName }}</td>
            <td>{{ item.supplierName }}</td>
            <td>{{ item.path }}</td>
            <td>{{ item.createName }}&nbsp;{{ item.createDate }}</td>
            <td class="ty-td-control">
              <span class="ty-color-blue" @click="lookallmore(item,2)">{{ item.productCount }}</span>
            </td>
          </tr>
          </tbody>
        </table>
        <!--        <el-table :data="tableData6" border class="ty-td-control" style="width: 100%;">-->
        <!--          <el-table-column fixed prop="lpadId" label="系统赋予的编码" width="130" align="center"></el-table-column>-->
        <!--          <el-table-column prop="modelCode" label="装备器具编号" width="130" align="center"></el-table-column>-->
        <!--          <el-table-column prop="modelName" label="型号" width="130" align="center"></el-table-column>-->
        <!--          <el-table-column prop="supplierName" label="来源" width="" align="center"></el-table-column>-->
        <!--          <el-table-column prop="path" label="所属类别" width="130" align="center"></el-table-column>-->
        <!--          <el-table-column label="创建" width="220" align="center">-->
        <!--            <template #default="scope">-->
        <!--              {{ scope.row.createName }}&nbsp;{{ scope.row.createDate }}-->
        <!--            </template>-->
        <!--          </el-table-column>-->
        <!--          <el-table-column prop="productCount" label="参与加工的产品" width="130" align="center">-->
        <!--            <template #default="scope">-->
        <!--              <div class="ty-td-control">-->
        <!--                <span class="ty-color-blue" @click="lookallmore(scope.row,2)">{{ scope.row.productCount }}</span>-->
        <!--              </div>-->
        <!--&lt;!&ndash;              <el-button class="impont-hover" type="typcolor" @click="lookallmore(scope.row,2)">{{ scope.row.productCount }}</el-button>&ndash;&gt;-->
        <!--            </template>-->
        <!--          </el-table-column>-->
        <!--        </el-table>-->
      </div>
    </div>
  </div>
  <div class="choseagent" v-show="nameditio === 5">
    <div class="plencew">
      <el-row>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="gobcken(5)">返回</span>
      </el-row>
      <el-row class="upolence">
        <el-col :span="24"><div class="grid-content bg-purple-dark" v-for="(itemk5,indexk5) in listk5" :key="indexk5">
          装备器具现有<span>{{ itemk5.kd }}</span>种：具体如下
        </div></el-col>
      </el-row>
      <div class="equipmentdata upolence">
        <table class="ty-table ty-table-control">
          <thead>
          <tr>
            <td>装备器具名称</td>
            <td>型号</td>
            <td>来源</td>
            <td>单位</td>
            <td>数量</td>
            <td>创建</td>
            <td>操作</td>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item,index) in tableData7" :key="index">
            <td>{{ item.fullName }}</td>
            <td>{{ item.modelCount }}</td>
            <td>{{ item.supplierCount }}</td>
            <td>{{ item.units }}</td>
            <td class="ty-td-control">
              <span class="ty-color-blue" @click="quantitymess(item,2)">{{ item.quantity }}</span>
            </td>
            <td>{{ item.createDate }}</td>
            <td class="ty-td-control">
              <span class="ty-color-blue" @click="upeqname(item,2)">修改</span>
              <span class="ty-color-blue" @click="upnamerecord(item,2)">名称修改记录</span>
            </td>
          </tr>
          </tbody>
        </table>
        <!--        <el-table :data="tableData7" border style="width: 100%" class="ty-td-control">-->
        <!--          <el-table-column fixed prop="fullName" label="装备器具名称" width="140" align="center"></el-table-column>-->
        <!--          <el-table-column prop="modelCount" label="型号" width="140" align="center"></el-table-column>-->
        <!--          <el-table-column prop="supplierCount" label="来源" width="" align="center"></el-table-column>-->
        <!--          <el-table-column prop="units" label="单位" width="140" align="center"></el-table-column>-->
        <!--          <el-table-column prop="quantity" label="数量" width="140" align="center">-->
        <!--            <template #default="scope">-->
        <!--              <div class="ty-td-control">-->
        <!--                <span class="ty-color-blue" @click="quantitymess(scope.row,2)">{{ scope.row.quantity }}</span>-->
        <!--              </div>-->
        <!--&lt;!&ndash;              <el-button class="impont-hover" type="typcolor" @click="quantitymess(scope.row,2)">{{ scope.row.quantity }}</el-button>&ndash;&gt;-->
        <!--            </template>-->
        <!--          </el-table-column>-->
        <!--          <el-table-column prop="createDate" label="创建" width="315" align="center"></el-table-column>-->
        <!--          <el-table-column fixed="right" label="操作" width="200" align="center">-->
        <!--            <template #default="scope">-->
        <!--              <div class="ty-td-control">-->
        <!--                <span class="ty-color-blue" @click="upeqname(scope.row,2)">修改</span>-->
        <!--                <span class="ty-color-blue" @click="upnamerecord(scope.row,2)">名称修改记录</span>-->
        <!--              </div>-->
        <!--&lt;!&ndash;              <el-button class="impont-hover" @click="upeqname(scope.row,2)" type="typcolor" size="small">修改</el-button>&ndash;&gt;-->
        <!--&lt;!&ndash;              <el-button class="impont-hover" @click="upnamerecord(scope.row,2)" type="typcolor"&ndash;&gt;-->
        <!--&lt;!&ndash;                         size="small">名称修改记录</el-button>&ndash;&gt;-->
        <!--            </template>-->
        <!--          </el-table-column>-->
        <!--        </el-table>-->
      </div>
    </div>
    <TyDialog v-if="delVisiblen3" width="500" :dialogTitle="dialogTitle" color="blue" :dialogHide="hideFun3">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun3">取消</el-button>
        <el-button class="bounce-ok" @click="tipOk1(1,2)" type="primary" v-show="addorupe === 1" id="addsubmit">确定</el-button>
        <el-button class="bounce-ok" @click="tipOk1(2,2)" type="primary" v-show="addorupe === 2" id="upmodify">确定</el-button>
      </template>
      <template #dialogBody>
        <div>
          <el-row class="plencen">
            <el-col :span="24"><div class="grid-content bg-purple-dark">
              <span>装备器具的名称</span>
            </div></el-col>
          </el-row>
          <el-input
              type="text"
              placeholder="请输入内容"
              v-model="text"
              maxlength="8"
              show-word-limit
              ref="unitern1"
          >
          </el-input>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="delVisiblen4" width="500" dialogTitle="装备器具名称的修改记录" color="blue" :dialogHide="hideFun4">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun4" type="primary">关闭</el-button>
      </template>
      <template #dialogBody>
        <div>
          <el-table :data="tableData2" border>
            <el-table-column prop="msgmade" label="事件" width="140" align="center"> </el-table-column>
            <el-table-column label="操作" width="210" align="center">
              <template #default="scope">
                {{scope.row.createName}}&nbsp;{{scope.row.createDate}}
              </template>
            </el-table-column>
            <el-table-column prop="fullName" label="操作后的名称" align="center"> </el-table-column>
          </el-table>
        </div>
      </template>
    </TyDialog>
  </div>
</template>

<script>
import { defineComponent, ref } from 'vue';
import { beforeRouteLeave,initNav } from "@/utils/routeChange"
//import TyDialog from "../../components/TyDialog";
import equipNames from "@/mixins/equipManage/equipNames";

export default defineComponent ({
  //components: {TyDialog},
  mixins: [ equipNames],
  setup() {
    return {
      input3: ref(''),
      select: ref(''),
      text: ref(''),
      textarea: ref(''),
    }
  },
  data() {
    return {
      pageName: "nameManageMent",//pageName是需要写在data-return下的，写在外面会获取不到
    }
  },
  beforeRouteLeave(to, from, next) {
    beforeRouteLeave(to, from, next,this)
  },
  created(){
    initNav({mid: 'qr',name: '名称管理',pageName: this.pageName}, this.initCreateda, this)
  },
  methods: {}
})
</script>
<style scoped lang="scss">
@use "@/style/equiptCommon.scss";
.quantitydetails {
  .plencew {
    margin: 48px 107px 20px;
  }
}
.screenboder {
  .plencew {
    margin: 48px 107px 20px;
  }
}
.drawingnumber{
  .plencew{
    margin: 48px 107px 20px;
  }
}
.categorycode{
  .plencew{
    margin: 48px 107px 20px;
  }
}
.choseagent{
  .plencew{
    margin: 48px 107px 20px;
  }
}
.nameagent{
  .rolor{
    margin: 57px 96px 0 96px;
  }
}
</style>
