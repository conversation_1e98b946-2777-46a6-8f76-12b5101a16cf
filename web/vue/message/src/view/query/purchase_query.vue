<template>
  <div id="purchaseQuery" class="query purchaseQuery">
    <TY_NavTop title="查询"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div style="padding: 0 4px;">
        <div class="tip tip-success">您可更换查询条件，以得到其他查询结果。</div>
      </div>
      <div class="ui-message">
        <div class="sendContainer">
          <el-form :model="queryForm" ref="queryForm" label-width="70px">
            <div>
              <h4 class="item-header">供应商</h4>
              <div class="el-form-item">
                <span :class="queryForm.creator === ''?'btn_all btn_active':'btn_all'" v-on:click="chooseApplier(0)">全部</span>
                <span class="chooseApplier" v-on:click="chooseApplier(1)">{{queryForm.applyName}} <i class="el-icon-arrow-right"></i></span>
              </div>
            </div>
            <div>
              <h4 class="item-header">时间选项</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.dateType" size="small" @change="init('dateType')">
                  <el-radio label="1" v-if="type === 1 || type === 2 || type === 3">入库申请的提出时间（报检时间）</el-radio>
                  <el-radio label="2" v-if="type === 1 || type === 2 ">检验时间</el-radio>
                  <el-radio label="3" v-if="type === 4 || type === 5">让步申请的提出时间</el-radio>
                  <el-radio label="4" v-if="type === 4 || type === 5">让步审批的时间</el-radio>
                  <el-radio label="5" v-if="type === 1 || type === 3">库管员的入库处理时间</el-radio>
                </el-radio-group>
              </div>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.dateSection" size="small">
                  <el-radio label="1" border>近七日</el-radio>
                  <el-radio label="2" border>本月</el-radio>
                  <el-radio label="3" border>自定义</el-radio>
                </el-radio-group>
              </div>
              <div class="el-form-item" v-if="queryForm.dateSection === '3'">
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.beginDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker> -
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.endDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker>
              </div>
            </div>
            <div v-if="queryForm.dateType === '2'">
              <h4 class="item-header">检验结果</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.resultType" size="small"  @change="init('queryForm.resultType')">
                  <el-radio label="1" border>展示订单清单</el-radio>
                  <el-radio label="2" border>展示物料清单</el-radio>
                </el-radio-group>
              </div>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.state" size="small">
                  <el-radio label="" v-if="queryForm.resultType === '1'">全部订单</el-radio>
                  <el-radio label="" v-if="queryForm.resultType === '2'">全部物料</el-radio>
                  <el-radio label="3" v-if="queryForm.resultType === '1'">物料全部合格的订单</el-radio>
                  <el-radio label="3" v-if="queryForm.resultType === '2'">所有合格物料</el-radio>
                  <el-radio label="4" v-if="queryForm.resultType === '1'">含有不合格物料的订单</el-radio>
                  <el-radio label="4" v-if="queryForm.resultType === '2'">所有不合格物料</el-radio>
                </el-radio-group>
              </div>
            </div>
            <div v-if="queryForm.dateType === '5'">
              <h4 class="item-header">入库的结果</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.state" size="small">
                  <el-radio label="">全部</el-radio>
                  <el-radio label="1">数量无误的物料</el-radio>
                  <el-radio label="0">数量有问题的物料</el-radio>
                </el-radio-group>
              </div>
            </div>
            <div v-if="queryForm.dateType === '3' || queryForm.dateType === '4'">
              <div class="el-form-item">
                <h4 class="item-header">让步审批的结果</h4>
                <el-radio-group v-model="queryForm.state" size="small">
                  <el-radio label="">全部</el-radio>
                  <el-radio label="6">让步审批通过的物料</el-radio>
                  <el-radio label="7">让步审批未通过的物料</el-radio>
                </el-radio-group>
              </div>
            </div>
            <div class="handle_button" style="margin-top: 20px">
              <input type="submit" class="ui-btn ui-btn_info" value="确定" @click="submit" v-loading.fullscreen.lock="loading">
            </div>
          </el-form>
        </div>
      </div>
    </div>
    <TY_AddressBook :rolelist="roleList" title="选择供应商" v-bind:show.sync="bookVisible" @select_role="setRole"></TY_AddressBook>
  </div>
</template>

<style>
  .reimburseQuery .el-radio-group .el-radio {
    display: inline-block;
    margin: 0 5px 5px 0;
  }
</style>

<script>
export default {
  name: 'purchaseQuery',
  data () {
    return {
      loading: true,
      queryForm: {
        dateType: '',
        dateSection: '1',
        beginDate: '',
        endDate: '',
        resultType: '1',
        approveStatus: '',
        creator: '',
        applyName: '选择',
        state: '',
        quantityType: ''
      },
      roleList: [],
      bookVisible: false,
      type: 0
    }
  },
  created: function () {
    let that = this
    let type = this.$route.params.type
    if (type) {
      switch (Number(type)) {
        case 1: // 外购物料申请
          this.queryForm.dateType = '1'
          break
        case 2: // 外购物料检验
          this.queryForm.dateType = '2'
          break
        case 3:
          this.queryForm.dateType = '1'
          break
        case 4:
        case 5:
          this.queryForm.dateType = '3'
          this.queryForm.resultType = '2'
          break
      }
      this.type = Number(type)
    }
    this.$http.post('../../../inStock/supplier', { oid: this.sphdSocket.user.oid }, {
      emulateJSON: true
    }).then((response) => {
      that.loading = false
      let data = response.body
      console.log(response.body)
      if (data) {
        let list = data.data
        for (let i = 0; i < list.length; i++) {
          that.roleList.push({
            userID: list[i].id,
            userName: list[i].name
          })
        }
        console.log(that.roleList)
      } else {
        console.log('加载失败！')
      }
    })
  },
  methods: {
    submit: function () {
      // type 1-近七日，2-本月，3-自定义
      // 自定义时传 beginDate 开始时间，endDate 结束时间
      // approveStatus 2- 批准 3- 驳回
      // feeCat 费用类别id
      // feeCatName 费用类别名称
      // billCat 票据种类 id
      // billCatName 票据种类名称
      console.log(JSON.stringify(this.queryForm))
      let queryParam = {
        oid: this.sphdSocket.user.oid, // 机构id
        supplierId: this.queryForm.creator, // 供应商id
        supplierName: this.queryForm.applyName, // 供应商id
        beginDate: this.queryForm.beginDate, // 开始时间
        endDate: this.queryForm.endDate, // 结束时间
        dateType: this.queryForm.dateType, // 时间类型 1-查询入库申请提出时间的申请入库列表 2-查询检验时间 3-查询让步申请的提出时间 4-查询让步审批时间 5-库管员的入库处理时间
        dateSection: this.queryForm.dateSection, // 时间周期 1-查询近七日 2-查询本月
        resultType: this.queryForm.resultType, // 展示形式 1-申请清单 2 - 物料清单
        state: this.queryForm.state,
        quantityType: this.queryForm.quantityType // 1-数量一致,0-数量有误
      }
      localStorage.setItem('query', JSON.stringify(queryParam))
      this.$router.push({
        path: '/purchaseQueryPer',
        name: 'purchaseQueryPer',
        params: {
          data: queryParam
        }
      })
    },
    chooseApplier: function (type) {
      if (type === 0) {
        this.queryForm.creator = ''
        this.queryForm.applyName = '选择'
      } else {
        this.bookVisible = true
      }
    },
    setRole: function (data) {
      this.queryForm.creator = data.userID
      this.queryForm.applyName = data.userName
    },
    init: function (type) {
      if (this.type === 1) {
        this.queryForm.resultType = '1'
        this.queryForm.dateSection = '1'
        this.queryForm.beginDate = ''
        this.queryForm.endDate = ''
      }
      this.queryForm.state = ''
    }
  }
}
</script>
