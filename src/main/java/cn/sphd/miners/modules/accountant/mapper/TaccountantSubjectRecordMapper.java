package cn.sphd.miners.modules.accountant.mapper;

import cn.sphd.miners.modules.accountant.entity.TaccountantSubjectRecord;
import cn.sphd.miners.modules.accountant.entity.TaccountantSubjectRecordDto;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface TaccountantSubjectRecordMapper extends BaseMapper<TaccountantSubjectRecord> {

    List<TaccountantSubjectRecord> getSubjectRecords(TaccountantSubjectRecord subjectRecord);

    TaccountantSubjectRecordDto getSubjectModifyInfo(TaccountantSubjectRecordDto subjectRecordDto);

    TaccountantSubjectRecordDto getParentInfo(TaccountantSubjectRecordDto subjectRecordDto);

    void copySubjectToRecordTable(TaccountantSubjectRecordDto recordDtoParam);
}