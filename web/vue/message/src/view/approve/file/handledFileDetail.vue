<template>
  <div id="handleFile">
    <TY_NavTop title="文件审批"></TY_NavTop>
    <div class="container" v-loading="loading">
      <div class="place" v-if="isAbolishFile">
        <div class="placeContainer">
          <p>这是一条<span class="color-red">文件废止的申请</span></p>
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          <p class="fileTtl" v-text="fileDetails.details.name"></p>
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          <div class="line-p">
            <div class="tt-cell">文件编号</div>
            <div class="con-cell" v-text="fileDetails.details.fileSn"></div>
          </div>
          <div class="line-p">
            <div class="tt-cell">保存位置</div>
            <div class="con-cell" v-html="filePosition"></div>
          </div>
          <div class="line-p">
            <div class="tt-cell">文件类型</div>
            <div class="con-cell" v-text="fileDetails.details.version"></div>
          </div>
          <div class="line-p">
            <div class="tt-cell">文件大小</div>
            <div class="con-cell">{{bytesToSize}}</div>
          </div>
          <div class="line-p" v-if="fileDetails.details.changeNum">
            <div class="tt-cell">版本号</div>
            <div class="con-cell">G{{fileDetails.details.changeNum}}</div>
          </div>
          <div class="line-p" v-if="!fileDetails.details.changeNum">
            <div class="tt-cell">说明</div>
            <div class="con-cell" v-text="fileDetails.details.content" :title="fileDetails.details.content"></div>
          </div>
          <div class="line-p" v-if="fileDetails.details.changeNum">
            <div class="tt-cell">换版原因</div>
            <div class="con-cell" v-text="fileDetails.details.content" :title="fileDetails.details.content"></div>
          </div>
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          <div class="line-btn-groups" v-if="isAbolishFile">
            <div class="line-p">
              <div class="tt-cell">将废止的文件</div>
              <div class="con-cell">
                <span class="fc-btn-see" @click="showDialog(2)">查看</span>
              </div>
            </div>
          </div>
          <div class="line-btn-groups" v-else>
            <div class="line-p">
              <div class="tt-cell">待审批的文件</div>
              <div class="con-cell">
                <span class="fc-btn-see" @click="showDialog(0)">查看</span>
              </div>
            </div>
            <div class="line-p">
              <div class="tt-cell">当前版本</div>
              <div class="con-cell">
                <span class="fc-btn-see" @click="showDialog(1)">查看</span>
              </div>
            </div>
            <div class="line-p">
              <div class="tt-cell">历史版本</div>
              <div class="con-cell">
                <span class="fc-btn-see">查看</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          <div class="record">
            <span>审批记录</span>
            <div class="script">
              <div v-for="(item ,index) in approveRecord" :key="index">
                <div v-if="index === 0">
                  <span class="sm-ttl">申请人：</span>
                  <span class="sm-con" v-text="item.userName"></span>
                  <span>{{item.createDate | formatDate('yyyy-MM-dd hh:mm:ss')}}</span>
                </div>
                <div v-if="index != 0">
                  <span class="sm-ttl">审批人：</span>
                  <span class="sm-con" v-text="item.userName"></span>
                  <span>{{item.createDate | formatDate('yyyy-MM-dd hh:mm:ss')}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="pendingDialog"
      width="70%">
      <el-form class="preview">
        <el-radio-group v-model="browser">
          <el-radio :disabled="seeOnlineDisabled" :label="1">在线预览</el-radio>
          <el-radio :label="2">下载</el-radio>
        </el-radio-group>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button handle-center">
          <a class="fc-btn-normal" @click="pendingDialog = false">取消</a>
          <span class="fc-btn ui-btn_info" ref="operate" :path="fileHandleDetail.path" :download="fileHandleDetail.name + '.' + fileHandleDetail.version" @click="oprationFile($event)">确定</span>
        </div>
      </span>
    </el-dialog>
    <el-dialog
      title="!提示"
      :visible.sync="msgDialog"
      width="80%">
      <span class="st-center">该文件详情已发生变动！</span>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button handle-center">
          <input type="submit" class="fc-btn ui-btn_info" value="确定" @click="refreshFile">
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { formatDate, canSeeOnline } from '@/js/common'
export default {
  name: 'approveHandleFile',
  filters: {
    formatDate
  },
  data () {
    return {
      loading: true,
      pendingDialog: false,
      msgDialog: false,
      browser: 1,
      fileDetails: {
        categoryName: '',
        details: {},
        filePath: '',
        fileVersion: ''
      },
      approveRecord: [],
      listenersUid: [],
      fileHandleDetail: {},
      dialogTitle: '',
      seeOnlineDisabled: true,
      isAbolishFile: false,
      titleName: '换版'
    }
  },
  computed: {
    bytesToSize () {
      let sizeStr = ''
      let fileSize = this.fileDetails.details.size
      sizeStr = fileSize < 102400 ? parseFloat(fileSize / 1024).toFixed(2) + 'KB' : parseFloat(fileSize / 1048576).toFixed(2) + 'MB'
      return sizeStr
    },
    filePosition () {
      let pos = this.fileDetails.categoryName
      let posArr = pos.split('/')
      let posStr = ''
      for (let a = 0; a < posArr.length; a++) {
        if (a === posArr.length - 1) {
          posStr += '<span class="font-orange">' + posArr[a] + '</span>'
        } else {
          posStr += posArr[a] + '/'
        }
      }
      return posStr
    }
  },
  created: function () {
    let fileId = this.$route.params.pendId
    var _this = this
    this.sphdSocket.send('onePublishFileMessage', { 'session': this.sphdSocket.sessionid, 'hisId': fileId, 'userID': this.sphdSocket.user.userID })
    this.listenersUid = [
      this.sphdSocket.subscribe('onePublishFileMessage', function (data) {
        console.log('onePublishFileMessage', data)
        let fileData = JSON.parse(data)
        _this.fileDetails.categoryName = fileData.categoryName
        _this.fileDetails.details = fileData.resHis

        let teminateState = fileData.resHis.teminateState
        let filePath = ''
        if (teminateState === '1') {
          filePath = fileData.resHis.path
          _this.titleName = '废止'
        } else {
          if (fileData.resHis.changeNum > 0) {
            filePath = fileData.validFilePath
            _this.titleName = '换版'
          } else {
            _this.titleName = '发布'
          }
        }
        let pathArr = filePath.split('.')
        let version = pathArr[pathArr.length - 1]
        _this.filePath = filePath
        _this.fileVersion = version
        _this.isAbolishFile = teminateState === '1'

        if (fileData.listAp.length > 0) {
          _this.approveRecord = fileData.listAp
        }
        _this.loading = false
      }, null, 'session', fileId),
      this.sphdSocket.subscribe('fileUpdateState', function () {
        _this.msgDialog = true
      }, function () { console.log('Socket check Error:') }, 'custom', fileId)
    ]
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    oprationFile: function (event) {
      let el = event.currentTarget
      window.parent.previewOrDownload(el, this.browser)
    },
    showDialog: function (type) {
      let fileHandleDetail = {}
      if (type === 0) {
        this.dialogTitle = '待审批的文件'
        fileHandleDetail = {
          path: this.fileDetails.details.path,
          name: this.fileDetails.details.name,
          version: this.fileDetails.details.version
        }
      } else if (type === 1) {
        this.dialogTitle = '当前的有效文件'
        fileHandleDetail = {
          path: this.fileDetails.filePath,
          name: this.fileDetails.details.name,
          version: this.fileDetails.fileVersion
        }
      } else if (type === 2) {
        this.dialogTitle = '将废止的文件'
        fileHandleDetail = {
          path: this.fileDetails.details.path,
          name: this.fileDetails.details.name,
          version: this.fileDetails.details.version
        }
      }
      console.log(fileHandleDetail)
      console.log(fileHandleDetail.version)
      this.seeOnlineDisabled = !canSeeOnline(fileHandleDetail.version)
      this.fileHandleDetail = fileHandleDetail
      this.pendingDialog = true
      if (this.seeOnlineDisabled) {
        this.browser = 2
      } else {
        this.browser = 1
      }
    },
    refreshFile () {
      this.$router.push({
        path: `/fileApproval/approvedFileIssue`
      })
    }
  }
}
</script>
