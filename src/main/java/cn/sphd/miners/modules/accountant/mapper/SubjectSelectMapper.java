package cn.sphd.miners.modules.accountant.mapper;

import cn.sphd.miners.modules.accountant.entity.TAccountantSetting;
import cn.sphd.miners.modules.accountant.entity.TaccountantEstablishEntity;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by 刘洪涛 on 17-3-23.
 */
@Component
public interface SubjectSelectMapper {
    int setRelation(TAccountantSetting link);

    TAccountantSetting getRelation(TAccountantSetting link);

    int addInitializationSetting(Integer org);

    TAccountantSetting checkOrgSettingData(Integer org);

}

