package cn.sphd.miners.modules.material.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.material.dao.SrmSupplierContactSocialHistoryDao;
import cn.sphd.miners.modules.material.entity.SrmSupplierContactSocial;
import cn.sphd.miners.modules.material.entity.SrmSupplierContactSocialHistory;
import cn.sphd.miners.modules.sales.entity.CustomerContactSocialHistory;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class SrmSupplierContactSocialHistoryDaoImpl extends BaseDao<SrmSupplierContactSocialHistory, Serializable> implements SrmSupplierContactSocialHistoryDao {
    @Override
    public void insert(SrmSupplierContactSocial contactSocial, Integer cchId) {
        SrmSupplierContactSocialHistory ccsh=new SrmSupplierContactSocialHistory();
        ccsh.setSupplier(contactSocial.getSupplier());
        ccsh.setSupplierContactHistory(cchId);
        ccsh.setCode(contactSocial.getCode());
        ccsh.setType(contactSocial.getType());
        ccsh.setName(contactSocial.getName());
        ccsh.setCreator(contactSocial.getCreator());
        ccsh.setCreateDate(contactSocial.getCreateDate());
        ccsh.setCreateName(contactSocial.getCreateName());
        ccsh.setSupplier(contactSocial.getSupplier());
        ccsh.setSupplierContact(contactSocial.getSupplierContact());
        ccsh.setOrg(contactSocial.getOrg());
        ccsh.setEnabled(contactSocial.getEnabled());
        ccsh.setEnabledTime(contactSocial.getEnabledTime());
        ccsh.setOperation(contactSocial.getOperation());
        ccsh.setContactSocial(contactSocial.getId());
        save(ccsh);
    }
}
