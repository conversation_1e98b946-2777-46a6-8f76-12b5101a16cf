package cn.sphd.miners.modules.schedule.task;

import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.schedule.service.MemoJobService;
import cn.sphd.miners.modules.schedule.service.MemoScheduleService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.service.DlmService;
import cn.sphd.miners.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Created by Administrator on 2019/1/29.
 */
public class ScheduleTask {

    @Autowired
    MemoScheduleService memoScheduleService;
    @Autowired
    MemoJobService memoJobService;
    @Autowired
    UserService userService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    DlmService dlmService;

    public void scheduleSettleDay(){
        //wyu:获取分布式锁
        String lockKey, methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        if ((lockKey=dlmService.getLock(methodName)) != null) {
            System.out.println("机构用户日程日任务开始：" + NewDateUtils.dateToString(System.currentTimeMillis(),"yyyy/MM/dd-HH:mm:ss:SSS"));
//            List<User> userList = userService.getAllUser();
//            for (User u : userList) {
//                if ("1".equals(u.getIsDuty())) {
            memoScheduleService.scheduleSettleDay(methodName);
//                }
//            }
            System.out.println("机构用户日程日任务结束："  + NewDateUtils.dateToString(System.currentTimeMillis(),"yyyy/MM/dd-HH:mm:ss:SSS"));
            dlmService.releaseLock(methodName, lockKey);
        }
    }
}
