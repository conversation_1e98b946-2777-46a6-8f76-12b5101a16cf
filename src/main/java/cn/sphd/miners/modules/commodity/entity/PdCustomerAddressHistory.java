package cn.sphd.miners.modules.commodity.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity(name = "PdCustomerAddressHistory")
@Table(name = "t_sl_customer_address_history")
public class PdCustomerAddressHistory implements Serializable {
    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;


    @Column(name="customer"   , nullable=true , unique=false)
    private Integer customer;

    @Column(name="customer_address"   , nullable=true , unique=false)
    private Integer customerAddress;

    @Column(name="type"   ,length=1 ,  nullable=true , unique=false)
    private String type;

    @Column(name="contact"  , length=100 , nullable=true , unique=false)
    private String contact;

    @Column(name="mobile"  , length=100 , nullable=true , unique=false)
    private String mobile;

    @Column(name="telephone"  , length=100 , nullable=true , unique=false)
    private String telephone;

    @Column(name="fax"  , length=100 , nullable=true , unique=false)
    private String fax;

    @Column(name="email"  , length=100 , nullable=true , unique=false)
    private String email;

    @Column(name="address"  , length=100 , nullable=true , unique=false)
    private String address;

    @Column(name="postcode"  , length=6 , nullable=true , unique=false)
    private String postcode;

    @Column(name="is_default"   , nullable=true , unique=false)
    private Boolean isDefault;

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;

    @Column(name="keywords"  , length=100 , nullable=true , unique=false)
    private String keywords;

    @Column(name="enabled"  , length=100 , nullable=true , unique=false)
    private String enabled;

    @Column(name="enabled_time"  ,  nullable=true , unique=false)
    private Date enabledTime;

    @Column(name="version_number"  ,  nullable=true , unique=false)
    private Integer versionNumber;

    @Column(name="operation"  , length=1, nullable=true , unique=false)
    private String operation;

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private java.util.Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private java.util.Date updateDate;

    @Column(name="customer_contact"  )
    private String customerContact;

    @Column(name="region_code"   , nullable=true , unique=false)
    private String regionCode;

    @Column(name="requirements"   , nullable=true , unique=false)
    private String requirements;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCustomer() {
        return customer;
    }

    public void setCustomer(Integer customer) {
        this.customer = customer;
    }

    public Integer getCustomerAddress() {
        return customerAddress;
    }

    public void setCustomerAddress(Integer customerAddress) {
        this.customerAddress = customerAddress;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    public Boolean getDefault() {
        return isDefault;
    }

    public void setDefault(Boolean aDefault) {
        isDefault = aDefault;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    public Integer getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(Integer versionNumber) {
        this.versionNumber = versionNumber;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getCustomerContact() {
        return customerContact;
    }

    public void setCustomerContact(String customerContact) {
        this.customerContact = customerContact;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }

    public String getRequirements() {
        return requirements;
    }

    public void setRequirements(String requirements) {
        this.requirements = requirements;
    }
}
