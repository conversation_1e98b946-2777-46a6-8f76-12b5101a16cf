package cn.sphd.miners.modules.schedule.controller;

import cn.sphd.miners.common.initializer.AuthPassport;
import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.schedule.entity.*;
import cn.sphd.miners.modules.schedule.service.*;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2020/7/31 0031.
 */
@Controller
@RequestMapping("/scheduleShare")
public class MemoScheduleShareController {

    @Autowired
    MemoScheduleService memoScheduleService;
    @Autowired
    MemoScheduleShareService memoScheduleShareService;
    @Autowired
    UserService userService;
    @Autowired
    MemoScheduleHistoryService  memoScheduleHistoryService;


    /**
     * <AUTHOR>
     * @Date 2020/7/31 11:06
     * 获取日程可分享的人员
     */
    @ResponseBody
    @RequestMapping("/getShareableUsers.do")
    public JsonResult  getShareableUsers(User user,Integer scheduleId){
//        if (userId==null) {
//            User user = (User) session.getAttribute("user");
//            userId=user.getUserID();
//        }
        user=userService.getUserByID(user.getUserID());
        List<User> userList=userService.getOrgUsersAndSee(user.getOid()); //去除身份,全部人员,加跨机构可见人员
        List<Integer> remUserIds=memoScheduleShareService.getRecipientIdsByScheduleId(scheduleId);
        List<User> remUsers=new ArrayList<>();
        remUsers.add(user);//把操作人自己 去掉
        for (User user1:userList){
            if (remUserIds.contains(user1.getUserID())){
                remUsers.add(user1);
            }
        }
        userList.removeAll(remUsers); // 去除不用分享的人员
        return new JsonResult(1,userList);
    }

    /**
     * <AUTHOR>
     * @Date 2020/7/31 15:06
     * 对日程/备忘多选分享接口（并给被分享人发系统消息）
     * userId 登录人id
     * scheduleId 日程id
     * shareableUserIds 被分享人 多人 100,101,102
     */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/shareScheduleToUsers.do")
    public JsonResult shareScheduleToUsers(User user, Integer scheduleId, Integer ... shareableUserIds){
        Integer state=0;
        if (scheduleId!=null&&shareableUserIds!=null) {
//            if (userId==null) {
//                User user = (User) session.getAttribute("user");
//                userId=user.getUserID();
//            }
//            user = userService.getUserByID(user.getUserID());
            MemoSchedule memoSchedule = memoScheduleService.getMemoScheduleById(scheduleId);
            if (user==null){
                user=userService.getUserByID(memoSchedule.getUser());
            }
            memoScheduleShareService.addMemoScheduleShareByMemoSchedule(memoSchedule, user, shareableUserIds);

            state=1; //成功
        }
        return new JsonResult(1,state);

    }

    /**
     * <AUTHOR>
     * @Date 2020/7/31 15:40
     * 更多下分享管理接口（带分页 每页20条）
     */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/scheduleShareManage.do")
    public JsonResult  scheduleShareManage(User user,Integer scheduleId,PageInfo pageInfo){
//        if (userId==null) {
//            User user = (User) session.getAttribute("user");
//            userId=user.getUserID();
//        }
//        user = userService.getUserByID(user.getUserID());
        List<MemoScheduleShare> memoScheduleShares=memoScheduleShareService.getMemoScheduleSharesByScheduleId(scheduleId,pageInfo);
        List<Integer> recipientIds=new ArrayList<>();
        for (MemoScheduleShare memoScheduleShare:memoScheduleShares){
            for (MemoScheduleShareRecipients mr:memoScheduleShare.getMemoScheduleShareRecipientsHashSet()){
                recipientIds.add(mr.getRecipient());
            }
        }
        if (recipientIds.size()>0) {
            List<User> userList = userService.getUsersByUserIds(recipientIds);
            HashMap<Integer,String> userMap=new HashMap<>();
            for (User u:userList){
                userMap.put(u.getUserID(),u.getUserName());
            }
            for (MemoScheduleShare m : memoScheduleShares) {
                for (MemoScheduleShareRecipients mr : m.getMemoScheduleShareRecipientsHashSet()) {
                    mr.setRecipientUserName(userMap.get(mr.getRecipient()));
                }
            }
        }
        Map<String,Object> map=new HashMap<>();
        map.put("memoScheduleShares",memoScheduleShares);
        map.put("pageInfo",pageInfo);
        return new JsonResult(1,map) ;
    }


    /**
     * <AUTHOR>
     * @Date 2020/7/31 17:40
     * 发出的分享列表（带分页 每页20条）
     */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/shareMemoScheduleShares.do")
    public JsonResult shareMemoScheduleShares(User user,AuthAcc acc,Integer passiveUserId,PageInfo pageInfo){
//        if (userId==null) {
//            User user = (User) session.getAttribute("user");
//            userId=user.getUserID();
//        }
        if (user!=null){
            passiveUserId=user.getUserID();// 登录user有值，在机构中，只能查自己
        }
        List<MemoScheduleShare> memoScheduleShareList=memoScheduleShareService.getMemoScheduleSharesByCreator(passiveUserId,pageInfo);
        List<Integer> recipientIds=new ArrayList<>();
        for (MemoScheduleShare memoScheduleShare:memoScheduleShareList){
            for (MemoScheduleShareRecipients mr:memoScheduleShare.getMemoScheduleShareRecipientsHashSet()){
                recipientIds.add(mr.getRecipient());
            }
        }
        if (recipientIds.size()>0) {
            List<User> userList = userService.getUsersByUserIds(recipientIds);
            HashMap<Integer,String> userMap=new HashMap<>();
            for (User u:userList){
                userMap.put(u.getUserID(),u.getUserName());
            }
            for (MemoScheduleShare m : memoScheduleShareList) {
                for (MemoScheduleShareRecipients mr : m.getMemoScheduleShareRecipientsHashSet()) {
                    mr.setRecipientUserName(userMap.get(mr.getRecipient()));
                }
            }
        }
        Map<String,Object> map=new HashMap<>();
        map.put("memoScheduleShares",memoScheduleShareList);
        map.put("pageInfo",pageInfo);
        return new JsonResult(1,map) ;
    }

    /**
     * <AUTHOR>
     * @Date 2020/7/31 17:40
     * 接受的分享列表接口（带分页 每页20条）
     */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/receiveMemoScheduleShares.do")
    public  JsonResult receiveMemoScheduleShares(User user,AuthAcc acc,Integer passiveUserId,PageInfo pageInfo){
//        if (userId==null) {
//            User user = (User) session.getAttribute("user");
//            userId=user.getUserID();
//        }
        if (user!=null){
            passiveUserId=user.getUserID();// 登录user有值，在机构中，只能查自己
        }
        List<MemoScheduleShare> memoScheduleShareList=memoScheduleShareService.getMemoScheduleSharesByRecipient(passiveUserId,pageInfo);
        Map<String,Object> map=new HashMap<>();
        map.put("memoScheduleShares",memoScheduleShareList);
        map.put("pageInfo",pageInfo);
        return new JsonResult(1,map) ;

    }

    /**
     * <AUTHOR>
     * @Date 2020/8/1 15:10
     * 查看某条分享详情接口
     */
    @ResponseBody
    @RequestMapping("/getMemoScheduleShareInfo.do")
    public  JsonResult getMemoScheduleShareInfo(Integer id){
        MemoScheduleShare memoScheduleShare=memoScheduleShareService.getMemoScheduleShareById(id);
        return  new JsonResult(1,memoScheduleShare);
    }


    /**
     * <AUTHOR>
     * @Date 2025/7/23
     * 分享记录 查看当时分享时的内容  1.353P备忘优化
     */
    @ResponseBody
    @RequestMapping("/getOldMemoScheduleShareInfo.do")
    public  JsonResult getOldMemoScheduleShareInfo(Integer id){
        MemoScheduleShare memoScheduleShare=memoScheduleShareService.getMemoScheduleShareById(id);
        MemoScheduleHistory m= memoScheduleHistoryService.getMemoScheduleHistoryById(memoScheduleShare.getPreviousId());
        List<MemoScheduleImageHistory> memoScheduleImageHistoryList=memoScheduleHistoryService.getMemoScheduleImageHistories(memoScheduleShare.getPreviousId());
        Map<String,Object> map=new HashMap<>();
        map.put("memoSchedule",m);//日程详情
        map.put("scheduleImageList",memoScheduleImageHistoryList);// 附件
        return  new JsonResult(1,map);
    }
}
