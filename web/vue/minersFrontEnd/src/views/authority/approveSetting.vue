<template>
  <div class="authority">
    <TyDialog
        v-if="dialog_visible_set_overTimeApply_see"
        width="500"
        dialogTitle="查看加班审批的审批设置"
        color="blue"
        :dialogHide="hideFun"
    >
      <div class="mainCon" style="width: 400px; margin: 0 auto">
        <section class="overTimeSetting">
          <el-row>
            <el-col :span="24" class="text-right">
              <el-button link type="primary" @click="wonderssOverTimeTips()"><b>Wonderss中的加班功能</b></el-button>
            </el-col>
          </el-row>
          <el-row style="margin-bottom: 16px">加班设置的现状如下，如需要，可修改。	</el-row>
          <el-row>
            <el-col :span="20">1 职工要加班，需至少提前 {{setData.rule.upperLimit}} 分钟提出申请 </el-col>
            <el-col :span="4" class="text-right"><el-button link type="primary" @click="change_overTime_advanceTime()">修改</el-button> </el-col>
          </el-row>
          <el-row>
            <el-col :span="20">2 职工端“补报加班”的功能处于 {{setData.supplementary.enabled?'开启':'关闭'}} 状态 </el-col>
            <el-col :span="4" class="text-right"><el-button link type="primary" @click="change_overTime_repayState()">修改</el-button> </el-col>
          </el-row>
          <el-row>
            <el-col :span="20">3 职工加班后，需 {{setData.submitRule.upperLimit || '--'}} 天内提交实际加班的数据</el-col>
            <el-col :span="4" class="text-right"><el-button link type="primary" @click="changeFactApplyDurationRule()">修改</el-button> </el-col>
          </el-row>
          <el-row>
            <el-col :span="20">4 加班的审批 </el-col>
            <el-col :span="4" class="text-right"><el-button link type="primary" @click="overTimeEdit()">修改</el-button> </el-col>
          </el-row>
          <el-row>
            <el-col :span="23" :offset="1" v-if="setData.approvalItem.status === 1">
              <div v-for="(item, index) in setData.approvalFlows">
                <p>不高于{{item.amountCeiling}}小时的加班，需{{numToCN(index + 1)}}级审批</p>
                <p>{{numToCN(index + 1)}}级审批者 {{item.userName}} {{item.mobile || ''}}</p>
              </div>
            </el-col>
            <el-col :span="23" :offset="1" v-else>无需审批</el-col>
          </el-row>
          <el-text size="small" class="applyTip" type="primary">
            <el-col>注：</el-col>
            <el-col :offset="1">加班跨天时，申请者需分次提交申请。</el-col>
            <el-col :offset="1">申请者每次最多可提出24小时的加班申请。</el-col>
          </el-text>
        </section>
      </div>
      <template #dialogFooter>
        <el-button @click="dialog_visible_set_overTimeApply_see = false">关闭</el-button>
      </template>
    </TyDialog>
    <TyDialog
        v-if="dialog_visible_set_overTimeApply_advanceTime"
        width="400"
        dialogTitle="加班申请的提出时间"
        color="blue"
        :dialogHide="hideFun"
    >
      <div class="mainCon" style="width: 300px; margin: 0 auto">
        <el-form-item label="职工要加班，需至少提前多久提出申请？" label-position="top">
          <el-select
              v-model="form_overTimeApply_advanceTime"
          >
            <el-option label="30分钟" value="30" />
            <el-option label="60分钟" value="60" />
            <el-option label="90分钟" value="90" />
            <el-option label="120分钟" value="120" />
          </el-select>
        </el-form-item>
      </div>
      <template #dialogFooter>
        <el-button @click="dialog_visible_set_overTimeApply_advanceTime = false">取消</el-button>
        <el-button type="primary" @click="change_overTime_advanceTime_submit()">确定</el-button>
      </template>
    </TyDialog>
    <TyDialog
        v-if="dialog_visible_set_overTimeApply_changeFactApplyDurationRule"
        width="400"
        dialogTitle="提交实际加班数据的规则"
        color="blue"
        :dialogHide="hideFun"
    >
      <div class="mainCon" style="width: 300px; margin: 0 auto">
        <el-form-item label="职工要加班，需至少提前多久提出申请？" label-position="top">
          <el-select
              v-model="form_overTimeApply_advanceTime"
          >
            <el-option label="30分钟" value="30" />
            <el-option label="60分钟" value="60" />
            <el-option label="90分钟" value="90" />
            <el-option label="120分钟" value="120" />
          </el-select>
        </el-form-item>
      </div>
      <template #dialogFooter>
        <el-button @click="dialog_visible_set_overTimeApply_advanceTime = false">取消</el-button>
        <el-button type="primary" @click="change_overTime_advanceTime_submit()">确定</el-button>
      </template>
    </TyDialog>
    <TyDialog
        v-if="dialog_visible_set_leaveApply_see"
        width="500"
        dialogTitle="查看请假审批的审批设置"
        color="blue"
        :dialogHide="hideFun"
    >
      <div class="mainCon" style="width: 400px; margin: 0 auto">
        <section class="overTimeSetting">
          <el-row>
            <el-col :span="24" class="text-right">
              <el-button link type="primary" @click="wonderssOverTimeTips()"><b>Wonderss中的请假功能</b></el-button>
            </el-col>
          </el-row>
          <el-row style="margin-bottom: 16px">请假设置的现状如下，如需要，可修改。	</el-row>
          <el-row>
            <el-col :span="20">1 职工要请假，需至少提前 {{setData.rule.upperLimit}} 分钟提出申请 </el-col>
            <el-col :span="4" class="text-right"><el-button link type="primary" @click="change_overTime_advanceTime(2)">修改</el-button> </el-col>
          </el-row>
          <el-row>
            <el-col :span="20">2 职工端“事后补假”的功能处于 {{setData.supplementary.enabled?'开启':'关闭'}} 状态 </el-col>
            <el-col :span="4" class="text-right"><el-button link type="primary" @click="changeRepayOverTimeState()">修改</el-button> </el-col>
          </el-row>
          <el-row>
            <el-col :span="20">3 请假的审批 </el-col>
            <el-col :span="4" class="text-right"><el-button link type="primary" @click="overTimeEdit()">修改</el-button> </el-col>
          </el-row>
          <el-row>
            <el-col :span="23" :offset="1">
              <div v-for="(item, index) in setData.approvalFlows">
                <p v-if="item.amountCeiling > 0 && item.amountCeiling < 24">当日不高于{{item.amountCeiling}}小时的请假，需{{numToCN(index + 1)}}级审批</p>
                <p v-if="item.amountCeiling >= 24 && item.amountCeiling < 3600">不高于{{item.amountCeiling / 24}}天的请假，需{{numToCN(index + 1)}}级审批</p>
                <p v-else>请假需{{numToCN(index + 1)}}级审批</p>
                <p>{{numToCN(index + 1)}}级审批者 {{item.userName}} {{item.mobile || ''}}</p>
              </div>
            </el-col>
<!--            <el-text size="small" class="applyTip" type="primary" v-if="setData.approvalFlows[0].amountCeiling > 0">-->
<!--              <el-col>职工无法提交超过{{setData.approvalFlows[setData.approvalFlows.length - 1].amountCeiling > 0 && setData.approvalFlows[setData.approvalFlows.length - 1].amountCeiling < 24?setData.approvalFlows[setData.approvalFlows.length - 1].amountCeiling: setData.approvalFlows[setData.approvalFlows.length - 1].amountCeiling/24}}的请假申请。</el-col>-->
<!--            </el-text>-->
          </el-row>
        </section>
      </div>
      <template #dialogFooter>
        <el-button @click="dialog_visible_set_leaveApply_see = false">关闭</el-button>
      </template>
    </TyDialog>
    <TyDialog
        v-if="dialog_visible_set_overTimeApply_change"
        width="700"
        dialogTitle="修改加班审批的审批设置"
        color="blue"
        :dialogHide="hideFun"
    >

      <template #dialogBody>
        <div class="mainCon" style="width: 600px; margin: 0 auto">
          <el-text>
            <p>说明：</p>
            <p>1  设置多级审批时，可选择“直接上级”作为审批者，但一旦选择特定职工作为审批者，则之后的各级审批者将无法再选择“直接上级”。</p>
            <p>2  审批者最高可审批不超过24小时的加班。</p>
          </el-text>

          <div id="flowList">
            <div class="apItem">
              <p>
                <span class="levelName">审批者</span>
                <span class="chargeName">董事长</span>
              </p>
              <p>
                该审批者有权批准不高于<select class="chargeHours"><option value="24">24</option></select>小时的加班
              </p>
            </div>
          </div>
          <div id="nextStep">
            <p @click="toggleFa2($(this), 1)"><span><i class="fa fa-circle-o"></i> 设置下一级</span></p>
            <p @click="toggleFa2($(this), 2)"><span><i class="fa fa-circle-o"></i> 不设置下一级，已设置完毕。</span></p>
          </div>
          <div id="startDate" >
            本修改将对 <input type="text" readonly>之后提交的加班申请生效
          </div>
        </div>
      </template>
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="dialog_visible_set_overTimeApply_change = false">取消</el-button>
        <el-button class="bounce-ok" @click="permissionAssignment_submit">确定</el-button>
      </template>
    </TyDialog>
    <TyDialog
        v-if="dialog_visible_set_overTimeApply_wonderssOverTimeTips"
        width="500"
        dialogTitle="Wonderss中的加班功能"
        color="blue"
        :dialogHide="hideFun"
    >

      <template #dialogBody>
        <div class="mainCon" style="width: 400px; margin: 0 auto">
          <pre style="border: none;background-color:inherit">
Wonderss系统中关于加班的功能如下：

1 职工加班需提前申请，且需经审批通过。
&nbsp;&nbsp;&nbsp;* 申请者每次最多可提出24小时的加班申请。
&nbsp;&nbsp;&nbsp;* 跨天的加班，申请者需分次提交申请。
&nbsp;&nbsp;&nbsp;* 加班需提前多久申请，系统带有默认值，可修改。
&nbsp;&nbsp;&nbsp;* 系统默认加班申请需经最高领导的审批，可修改。

2 职工未提前申请的加班，总务确认无误后可修改其考勤。
&nbsp;&nbsp;&nbsp;* 系统带有默认为关闭状态的“补报加班”功能，该状态可修改。

3 加班后，职工还需填报实际加班的情况，并需经审批。
&nbsp;&nbsp;&nbsp;* 实际加班情况的审批流程，与加班申请的审批流程相同。
&nbsp;&nbsp;&nbsp;* 实际加班的数据超过规定时间未提交的，将由系统驳回。

4 实际加班情况审批通过后，加班数据即进入考勤模块。
          </pre>
        </div>
      </template>
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="dialog_visible_set_overTimeApply_wonderssOverTimeTips = false">关闭</el-button>
      </template>
    </TyDialog>
    <div class="ty-container" id="home">
      <div class="page">
        <el-table :data="approvalItemList" border style="width: 100%; margin-top: 16px">
          <el-table-column prop="name" label="审批事项" width="250" />
          <el-table-column label="当前状态" prop="name">
            <template #default="scope">
              <el-tag
                  :type="scope.row.status === 1?'primary':'info'"
                  effect="dark"
                  :disable-transitions="true"
              >
                {{ filter_Tag_btnState(scope.row).tagName }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="当前审批者 ">
            <el-table-column label="一级审批者">
              <template #default="scope">
                {{scope.row.status === 1?(scope.row.firstUserName || ''): ''}}
              </template>
            </el-table-column>
            <el-table-column label="二级审批者">
              <template #default="scope">
                {{scope.row.status === 1?(scope.row.secondUserName || ''): ''}}
              </template>
            </el-table-column>
            <el-table-column label="最终审批者">
              <template #default="scope">
                {{scope.row.status === 1?(scope.row.finalUserName || ''): ''}}
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <div v-if="filter_Tag_btnState(scope.row).btnState === 'see'">
                <el-button type="primary" link @click="approveSee(scope.row)"><b>查看</b></el-button>
                <el-button type="primary" link @click="approveRecord(scope.row)"><b>修改记录</b></el-button>
              </div>
              <div v-if="filter_Tag_btnState(scope.row).btnState === 'change'">
                <el-button type="primary" link @click="approveChange(scope.row)"><b>修改</b></el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.ty-container{
  margin-top:35px; padding:10px;
}
.page{
  padding: 8px 0;
  max-width: 1200px;
  position: relative;
  margin: 0 70px;
}
.ty-hr{
  margin: 16px 0;
  width: 100%;
  height: 1px;
  background: #eee;
}
.row{
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .rightBtn{
    flex: auto;
    text-align: right;
  }
  &.head{
    margin-bottom: 16px;
  }
}
.text-center{
  text-align: center;
}
.kj-table thead td{
  text-align: center;
}
.kj-table td .text {
  padding: 0 10px;
  color: #666;
}
.kj-table{
  width: 100%;
  border-collapse: collapse;
  td{
    border: 1px solid #ebeef5;
    font-size: 14px;
    text-align: left;
    height: 40px;
    word-wrap: break-word;
    word-spacing: normal;
    word-break: break-all;
    color: #101010;
    padding: 0;
    label{
      margin: 0 8px;
      white-space: normal;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
}
.tyDialog{
  font-size: 13px;
}
.text-right{
  text-align: right;
}
.el-row{
  margin-bottom: 8px;
}
</style>
<script>
import {beforeRouteLeave, initNav} from "@/utils/routeChange"
import auth from '@/sys/auth'
import * as api from "@/api/authority"
export default {
  data() {
    return {
      pageName: 'approveSetting',
      pageShow: false,
      pageInfo:{ 'currentPageNo': 0, 'pageSize': 20 , 'totalPage': 0  },
      active: 'first',
      checkList: [],
      checkList2: [],
      userList: [],
      approvalItemList: [],
      menuData2: [],
      setData: {
        itemInfo: {},
        rule: {},
        approvalItem: {},
        submitRule: {},
        approvalFlows: [],
        supplementary: {}
      },
      form_overTimeApply_advanceTime: '30',
      dialog_visible_set_overTimeApply_see: false,
      dialog_visible_set_overTimeApply_advanceTime: false,
      dialog_visible_set_overTimeApply_change: false,
      dialog_visible_set_overTimeApply_wonderssOverTimeTips: false,
      dialog_visible_set_overTimeApply_changeFactApplyDurationRule: true,
      dialog_visible_set_leaveApply_see: false,
      dialog_visible_set_leaveApply_change: false,
      dialog_visible_set_leave: false,
    }
  },
  components: {},
  computed: {
    option_shelves() {
      return this.shelvesList.map(item => {
        return {
          label: item.shelfCode + '/' + item.shelfName,
          value: item.id,
          disabled: item.amount === item.usedAmount
        }
      })
    },
  },
  beforeRouteLeave(to, from, next) {
    beforeRouteLeave(to, from, next, this)
  },
  mounted() {
    initNav({mid: 'ec', name: '审批设置', pageName: this.pageName}, this.created, this)
  },
  methods: {
    created () {
      this.getApproveSettingList()
    },
    getApproveSettingList() {
      let oid = auth.getUserID()
      api.getApproveSettingList(oid)
          .then(res => {
            let list = res.data.data.approvalItemPC
            this.approvalItemList = list
          })
    },
    approveSee(item) {
      this.dialog_visible_set_overTimeApply_see = true
      let code = item.code
      let promise = code === 'itemApply'?api.getApproveDetail(item.id):api.getApproveItemDetail(item.id)
      promise.then(res => {
        console.log(res)
        let data = code === 'itemApply'?res.data.data: res.data.data
        switch (code) {
          case 'overTimeApply':
          case 'leaveApply':
            let approvalFlows = data.approvalFlows || []
            let supplementary = data.supplementary // 加班/请假补报详情
            let rule = data.rule // 加班/请假限制（提前量）详情
            let submitRule = data.submitRule // 提交实际加班时限规则详情
            this.setData = data
            this.setData.itemInfo = item
            break
        }
      })

    },
    filter_Tag_btnState(item) {
      let code = item.code
      let status = item.status
      let level = item.level || 0
      let tagName = ''
      let btnState = 'see'
      switch (code) {
        case 'overTimeApply':
          // 加班
        case 'leaveApply':
          // 请假
        case 'reimburseApply':
          // 报销
        case 'paymentApproval':
          // 付款
        case 'purchaseApprovalSettings':
          // 采购审批设置
          tagName = status === 1?`需${this.numToCN(level)}级审批`: '无需审批'
          break
        case 'workAttendanceApply':
          // 修改考勤
        case 'archivesApply':
          // 修改职工档案
        case 'postApply':
          // 修改岗位设置
          tagName = status === 1?`需${this.numToCN(level)}级审批`: '无需审批'
          btnState = 'none'
          break
        case 'ordersReview':
          // 来自客户订单的评审
          tagName = status === 1?'需要评审':'无需评审'
          break
        case 'materialInCheck':
          // 采购来材料的入库检验
          tagName = status === 1?'需要检验':'无需检验'
          break
        case 'productInCheck':
          // 货物入成品库前的检验
          tagName = status === 1?'需要检验':'无需检验'
          break
        case 'commodityProduct':
          // 商品与产品的关联
          tagName = status === 1?'手动关联':'自动关联'
          break
        case 'stockModeChange':
          // 仓库的模式
          tagName = status === 1?'智能仓库':'非智能仓库'
          break
        case 'itemApply':
          // 修改审批设置
          tagName = status === 1?`需${this.numToCN(level)}级审批`: '无需审批'
          btnState = 'change'
          break
      }
      return {
        tagName: tagName,
        btnState: btnState
      }
    },
    wonderssOverTimeTips() {
      this.dialog_visible_set_overTimeApply_wonderssOverTimeTips = true
    },
    change_overTime_advanceTime() {
      this.dialog_visible_set_overTimeApply_advanceTime = true
    },
    change_overTime_advanceTime_submit() {
      let itemId = this.setData.itemInfo.id
      let advanceTime = this.form_overTimeApply_advanceTime
      api.updateAdvanceTimeRule(itemId, advanceTime)
      .then(res => {
        let data = res.data.data
        let status = data.status
        if (status === 1) {
          this.$message({
            type: 'success',
            message: '修改成功！'
          })
          this.dialog_visible_set_overTimeApply_advanceTime = false
        } else if (status === 0) {
          this.$message({
            type: 'error',
            message: '修改失败！'
          })
        } else if (status === 2) {
          this.$message({
            type: 'error',
            message: '值未改变！'
          })
        }
      })
    },
    change_overTime_repayState() {
      let enabled = this.setData.supplementary.enabled
      console.log('enabled', enabled)
      const openStr = '<p>未提前申请的加班如确需计入考勤，可以去修改考勤。<br>' +
          '开启“补报加班”功能，可能带来管理方面的新问题。<br>' +
          '不开启该功能，需要时去修改考勤，同样可达到目的。</p>' +
          '<p class="text-center">确定开启该功能吗？</p>'
      const closeStr = '<p class="text-center">确定关闭该功能吗？</p>'
      this.$messageBox.confirm(
          enabled?closeStr:openStr,
          '！！提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
      ).then(() => {
        api.updateSupplementary(this.setData.supplementary.id, enabled?0:1)
            .then(res => {
              let status = res.data.data.status
              if (status === 1) {
                this.$message({
                  type: 'success',
                  message: '修改成功！'
                })
              } else if (status === 0) {
                this.$message({
                  type: 'error',
                  message: '修改失败！'
                })
              } else if (status === 2) {
                this.$message({
                  type: 'error',
                  message: '值未改变！'
                })
              }
            })
      })
    },
    change_overTime_factApplyDurationRule() {
      this.dialog_visible_set_overTimeApply_changeFactApplyDurationRule = true
    },
    hideFun(dialog) {
      console.log('dialog', dialog)
      // this[dialog] = false
    },
    confirm () {
      this.$messageBox.confirm(
          '确定提交吗',
          '！！提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
      ).then(() => {
            let data = {
              id: this.stepList[this.page - 1].id
            }
            let that = this
            api.saveStep(data)
                .then(res => {
                  let success = res.data.success
                  if (success === 1) {
                    that.$router.go(0)
                  } else {
                    this.$message({
                      type: 'error',
                      message: '保存步骤失败！'
                    })
                  }
                })
          })
    },
    getUserStr(users) {
      let newUsers = users.map(item => item.userName)
      return newUsers.join('、')
    },
    numToCN(num) {
      console.log(num)
      let arr = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九', '二']
      return arr[num]
    }
  }
}
</script>
