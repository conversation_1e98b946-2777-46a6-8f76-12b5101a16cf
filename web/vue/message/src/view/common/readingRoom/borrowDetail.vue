<template>
  <div id="borrowDetail">
    <TY_NavTop title="文件借阅申请"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <!--除了文管待处理详情和文管查询详情都如下-->
      <div v-if="params.mark !== '3' && params.mark !== '43'">
        <a class="ui-cell" style="margin-bottom: 8px">
          <div class="ui-cell__bd">
            <div class="ui-cell_title">
              <span>{{fileDetails.name}}</span>
            </div>
          </div>
          <div class="ui-cell__ft" v-if="borrowRecord.isValid !== 1 && borrowRecord.approveStatus === '2' && params.mark !== '42' && !isTrash" @click="showDialog()"><span class="color-blue">查看</span></div>
        </a>
        <div v-if="borrowRecord.isValid !== 1 && borrowRecord.approveStatus === '2' && params.mark !== '42'">
          <div class="panel">
            <div class="ui-alert">
              <div>
                <b class="color-red">重要提示！</b>
                <div>源文件随时可能更新，但上面的文件不会跟随更新！</div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="borrowRecord.isValid === 1 && borrowRecord.approveStatus === '2' && params.mark !== '42'">
          <div class="panel">
            <div class="ui-alert">借阅已过期，如还需要查看，请重新申请借阅！</div>
          </div>
        </div>
        <div class="panel">
          <div class="placeContainer">
            <div class="item">
              <div class="item_title">文件编号</div>
              <div class="item_content" v-text="fileDetails.fileSn"></div>
            </div>
            <div class="item">
              <div class="item_title">保存位置</div>
              <div class="item_mid"><a class="color-blue aLink" @click="seeNewSavePlace">查看新保存位置</a></div>
              <div class="item_content" v-html="filePosition"></div>
            </div>
            <div class="item">
              <div class="item_title">文件类型</div>
              <div class="item_content" v-text="fileDetails.version"></div>
            </div>
            <div class="item">
              <div class="item_title">文件大小</div>
              <div class="item_content">{{bytesToSize}}</div>
            </div>
            <div class="item" v-if="fileDetails.changeNum">
              <div class="item_title">版本号</div>
              <div class="item_content">G{{fileDetails.changeNum}}</div>
            </div>
            <div class="item">
              <div class="item_title">说明</div>
              <div class="con-reason" v-text="fileDetails.content" :title="fileDetails.content"></div>
            </div>
            <div class="item" v-if="fileDetails.changeNum">
              <div class="item_title">换版原因</div>
              <div class="con-reason" v-text="fileDetails.reason" :title="fileDetails.reason"></div>
            </div>
          </div>
        </div>
        <div class="panel">
          <div class="item">
            <div class="item_title">审批记录</div>
          </div>
          <div class="item">
            <div class="rightSide" >
              <div v-if="borrowProcess.length > 0">
                <span class="process_desName">申请人</span>
                <span class="process_name">{{borrowProcess[0].userName}}</span>
                <span>{{borrowProcess[0].createDate | formatDate('yyyy-MM-dd hh:mm:ss')}}</span>
              </div>
              <div v-for="(item, index) in borrowProcess" :key="index">
                <div v-if="item.approveStatus === '2'">
                  <div v-if="item.toMid === null">
                    <span class="process_desName">审批人</span>
                    <span class="process_name">{{item.toUserName}}</span>
                    <span>{{item.handleTime | formatDate('yyyy-MM-dd hh:mm:ss')}}</span>
                  </div>
                  <div v-else>
                    <span class="process_desName">文&nbsp;&nbsp;管</span>
                    <span class="process_name">{{item.toUserName}}</span>
                    <span>{{item.handleTime | formatDate('yyyy-MM-dd hh:mm:ss')}}</span>
                  </div>
                </div>
                <div v-if="item.approveStatus === '3'">
                  <div class="color-red" v-if="item.toMid === null">
                    <span class="process_desName">审批人</span>
                    <span class="process_name">{{item.toUserName}}</span>
                    <span>{{item.handleTime | formatDate('yyyy-MM-dd hh:mm:ss')}}</span>
                  </div>
                  <div class="color-red" v-else>
                    <span class="process_desName">文&nbsp;&nbsp;管</span>
                    <span class="process_name">{{item.toUserName}}</span>
                    <span>{{item.handleTime | formatDate('yyyy-MM-dd hh:mm:ss')}}</span>
                  </div>
                  <div class="color-red">
                    <span>驳回理由</span>
                    <span>{{item.approveMemo}}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="panel" v-if="params.mark === '21'">
          <el-form :rules="rules" :model="selectRule" class="handleFile rightSide">
            <el-form-item prop="radio">
              <el-radio-group v-model="selectRule.radio">
                <el-radio :label="1" :disabled="usNot">本人无异议，选择下一个审批人</el-radio>
                <el-form-item v-show="selectRule.radio == '1'" class="approverNext" size="mini">
                  <el-select border v-model="selectRule.nextApprover" placeholder="选择下一位审批人" @change="nextAp" :disabled="usNot">
                    <el-option
                      v-for="item in approverSelect"
                      :key="item.userID"
                      :label="item.userName"
                      :value="item.userID">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-radio :label="2">本人无异议，请文管继续审批</el-radio>
                <br/>
                <el-radio :label="3">驳回该阅览申请</el-radio>
                <el-input :disabled="usNot" v-show="selectRule.radio == '3'" type="textarea" v-model="opposeForm.approveMemo"></el-input>
              </el-radio-group>
            </el-form-item>
          </el-form>
          <div class="handle_button">
            <input type="submit" class="ui-btn ui-btn_info" value="确定" @click="toHandle()"/>
          </div>
        </div>
      </div>

      <!--文管审批的页面和其他页面差别过大，重写一份页面-->
      <div v-else>
        <div class="panel">
          <div class="item">
            <div class="item_title">审批记录</div>
          </div>
          <div class="item">
            <div class="rightSide">
              <div v-if="borrowProcess.length > 0">
                <span class="process_desName">申请人</span>
                <span class="process_name">{{borrowProcess[0].userName}}</span>
                <span>{{borrowProcess[0].createDate | formatDate('yyyy-MM-dd hh:mm:ss')}}</span>
              </div>
              <div v-for="(item, index) in borrowProcess" :key="index">
                <div v-if="item.approveStatus === '2'">
                  <div v-if="item.toMid === null">
                    <span class="process_desName">审批人</span>
                    <span class="process_name">{{item.toUserName}}</span>
                    <span>{{item.handleTime | formatDate('yyyy-MM-dd hh:mm:ss')}}</span>
                  </div>
                  <div v-else>
                    <span class="process_desName">文&nbsp;&nbsp;管</span>
                    <span class="process_name">{{item.toUserName}}</span>
                    <span>{{item.handleTime | formatDate('yyyy-MM-dd hh:mm:ss')}}</span>
                  </div>
                </div>
                <div v-if="item.approveStatus === '3'">
                  <div v-if="item.toMid === null">
                    <span class="process_desName">审批人</span>
                    <span class="process_name">{{item.toUserName}}</span>
                    <span>{{item.handleTime | formatDate('yyyy-MM-dd hh:mm:ss')}}</span>
                  </div>
                  <div class="color-red" v-else>
                    <span class="process_desName">文&nbsp;&nbsp;管</span>
                    <span class="process_name">{{item.toUserName}}</span>
                    <span>{{item.handleTime | formatDate('yyyy-MM-dd hh:mm:ss')}}</span>
                  </div>
                  <div class="color-red">
                    <span>驳回理由</span>
                    <span>{{item.approveMemo}}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="panel" v-if="params.mark === '3'">
          是否允许该职工阅览该文件？请谨慎确认！
          <div class="handle_button">
            <input type="submit" class="ui-btn ui-btn_error" value="驳回" @click="approve(3)"/>
            <input type="submit" class="ui-btn ui-btn_info" value="批准" @click="approve(2)"/>
          </div>
        </div>
        <div class="panel">
          <div class="item">
            <div class="item_title">文件名称</div>
            <div class="item_content" v-text="fileDetails.name" :title="fileDetails.name"></div>
          </div>
          <div class="item">
            <div class="item_title">文件编号</div>
            <div class="item_content" v-text="fileDetails.fileSn"></div>
          </div>
          <div class="item">
            <div class="item_title">保存位置 </div>
            <div class="item_mid"><a class="color-blue aLink" @click="seeNewSavePlace">查看新保存位置</a></div>
            <div class="item_content" v-html="filePosition"></div>
          </div>
        </div>
        <a class="ui-cell" @click="showDialog()">
          <div class="ui-cell__bd">
            <div class="ui-cell_title">
              <span>申请借阅的文件</span>
            </div>
          </div>
          <div class="ui-cell__ft"><span class="color-blue">查看</span></div>
        </a>
      </div>
    </div>
    <el-dialog
      title="当前的有效文件"
      :visible.sync="pendingDialog"
      width="70%">
      <el-form class="preview">
        <el-form-item>
          <el-radio-group v-model="browser">
            <el-radio :disabled="seeOnlineDisabled" :label="1">在线预览</el-radio>
            <el-radio :label="2">下载</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button handle-center">
          <a class="fc-btn-normal" @click="pendingDialog = false">取消</a>
          <span class="fc-btn ui-btn_info" ref="operate" :path="fileDetails.path" :download="fileDetails.name + '.' + fileDetails.version" @click="oprationFile($event)">确定</span>
        </div>
      </span>
    </el-dialog>
    <el-dialog
      title="查看新保存位置"
      :visible.sync="dialogVisible"
      width="70%">
      <div v-if="allCategoryName === ''">
        <span>本文件的保存位置没变。</span>
      </div>
      <div v-else>
        <span>文件新的保存位置</span>
        <p class="color-blue">{{allCategoryName}}</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button">
          <a class="fc-btn-normal" @click="dialogVisible = false">确定</a>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<style>
  .panel{
    font-size: 13px;
    padding: 6px 10px;
    background-color: #fafafb;
    border-radius: 3px;
    margin-bottom: 4px;
  }
  .item{
    display: flex;
  }
  .item .item_title{
    width: 70px;
    flex: none;
    color: #666;
    line-height:32px;
    padding: 0 8px;
  }
  .item .item_content{
    text-align: right;
    flex: auto;
    line-height:32px;
  }
  .item .item_mid{
    line-height:32px;
  }
  .rightSide{
    padding-left: 36px;
  }
  .rightSide .process_name{
    display: inline-block;
    width: 70px;
    margin-left: 4px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: top;
  }
  .rightSide .process_desName{
    display: inline-block;
    width: 55px;
    vertical-align: top;
  }
  .btn-group{
    flex: auto;
  }
  .aLink{
    color: #4797f1;
    line-height: 32px;
    cursor: pointer;
  }
  .aLink:hover{
    text-decoration: underline;
  }
  .title{
    font-size: 14px;
    color: #666;
  }
</style>

<script>
import { formatDate, canSeeOnline, gohistory } from '../../../js/common'
export default {
  name: 'approveFileDetail',
  filters: {
    formatDate
  },
  data () {
    return {
      params: {}, // （id: 借阅id, mark: 判断来源 11、12：申请者待审批、近期可预览的文件，21、22：中间审批人待审批、近期可预览的文件，3文管待审批列表 ）
      loading: true,
      pendingDialog: false,
      dialogVisible: false,
      labelPosition: 'top',
      browser: 1,
      approverSelect: [],
      opposeForm: { // 驳回表单
        approveMemo: ''
      },
      selectedAp: {},
      lastApprover: {},
      fileDetails: {
        categoryName: '',
        details: {},
        filePath: '',
        fileVersion: ''
      },
      borrowRecord: {},
      borrowProcess: [],
      isTrash: true,
      selectRule: {
        radio: '',
        nextApprover: ''
      },
      rules: {
        radio: [
          { required: true, message: '请选择下一下一个审批人', trigger: 'change' }
        ]
      },
      listenersUid: [],
      seeOnlineDisabled: true,
      allCategoryName: ''
    }
  },
  computed: {
    bytesToSize () {
      let sizeStr = ''
      let fileSize = this.fileDetails.size
      sizeStr = fileSize < 102400 ? parseFloat(fileSize / 1024).toFixed(2) + 'KB' : parseFloat(fileSize / 1048576).toFixed(2) + 'MB'
      return sizeStr
    },
    btnControl () {
      return (!this.selectRule.radio) || (this.selectRule.radio === 1 && !this.selectRule.nextApprover)
    },
    filePosition () {
      let pos = this.fileDetails.categoryName
      let posArr = pos.split('/')
      let posStr = ''
      for (let a = 0; a < posArr.length; a++) {
        if (a === posArr.length - 1) {
          posStr += '<span class="font-orange">' + posArr[a] + '</span>'
        } else {
          posStr += posArr[a] + '/'
        }
      }
      return posStr
    }
  },
  created: function () {
    // 赋值地址栏传值（id: 借阅id， mark: 来源标记）
    this.params = this.$route.params
    console.log('borrow' + JSON.stringify(this.params))
    // 获取借阅文件详情
    this.getReadFileMessage()
    if (this.params.mark === '21') {
      // 来源为中间审批人时获取其他审批人列表
      this.getNextApprover()
    }
  },
  methods: {
    getReadFileMessage () {
      let that = this
      this.$http.post('../../../read/getReadFileMessage.do', {
        borrowId: this.params.id
      }, {
        emulateJSON: true
      }).then((response) => {
        let res = response.body.data
        let fileDetails = res.resHis // 文件信息列表
        let borrowProcess = res.listAp // 借阅审批流程
        let borrowRecord = res.readingRoom // 借阅记录列表
        that.fileDetails = fileDetails
        that.borrowProcess = borrowProcess
        that.borrowRecord = borrowRecord
        that.isTrash = res.isTrash
        that.lastApprover = borrowProcess[borrowProcess.length - 1].id
        that.loading = false
      }).catch(() => {
        that.$kiko_message('系统错误，请重试！')
      })
    },
    getNextApprover () {
      let that = this
      let params = {
        borrowId: this.params.id,
        userID: this.sphdSocket.user.userID
      }
      this.$http.post('../../../res/getAllOidUserByResource.do', params, {
        emulateJSON: true
      }).then((response) => {
        let data = response.body
        that.approverSelect = data.data.list
        that.loading = false
      }).catch(function () {
        that.$kiko_message('系统错误，请重试！')
      })
    },
    nextAp (vId) {
      this.selectedAp = this.approverSelect.find((item) => {
        return item.userID === vId // 筛选出匹配数据
      })
    },
    oprationFile: function (event) {
      let el = event.currentTarget
      window.parent.previewOrDownload(el, this.browser)
    },
    showDialog: function () {
      if (this.fileDetails.enabled === false) {
        this.$kiko_message('该文件已被禁用，您已无法继续查看！')
        return false
      }
      this.seeOnlineDisabled = !canSeeOnline(this.fileDetails.version)
      this.pendingDialog = true
      if (this.seeOnlineDisabled) {
        this.browser = 2
      } else {
        this.browser = 1
      }
    },
    seeNewSavePlace: function () {
      this.loading = true
      let params = {
        fileId: this.fileDetails.file
      }
      let that = this
      this.$http.post('../../../read/getReadFilePlace.do', params, {
        emulateJSON: true
      }).then((response) => {
        that.dialogVisible = true
        that.loading = false
        let data = response.body.data
        let allCategoryName = data.allCategoryName
        if (allCategoryName) {
          this.allCategoryName = allCategoryName
        }
      }).catch(function () {
        that.$kiko_message('系统错误，请重试！')
      })
    },
    toHandle () {
      // 中间审批人审批
      let fileData = {
        userID: this.sphdSocket.user.userID,
        borrowId: this.params.id,
        processId: this.lastApprover,
        type: 0, // 1代表继续选择下级审批人，2代表选择下级审批人是总务后小总务，3代表进行最终审批
        approveStatus: 0 // 2-批准 3-驳回
      }
      if (this.selectRule.radio === 1) {
        fileData.type = 1
        fileData.approveStatus = 2
        fileData.toUser = this.selectedAp.userID
        fileData.toUserName = this.selectedAp.userName
      } else if (this.selectRule.radio === 2) {
        fileData.type = 2
        fileData.approveStatus = 2
      } else if (this.selectRule.radio === 3) {
        fileData.type = 1
        fileData.approveStatus = 3
        fileData.approveMemo = this.opposeForm.approveMemo
      }
      this.loading = true
      this.handleBorrowingFile(fileData)
    },
    approve: function (approveStatus) {
      // 文管审批
      let fileData = {
        userID: this.sphdSocket.user.userID,
        borrowId: this.params.id,
        processId: this.lastApprover,
        approveStatus: approveStatus,
        type: 3 // 1代表继续选择下级审批人，2代表选择下级审批人是总务后小总务，3代表进行最终审批
      }
      if (fileData.type === 3) {
        fileData.approveMemo = this.opposeForm.approveMemo
      }
      let that = this
      if (approveStatus === 2) {
        this.$confirm('您确定批准吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          that.loading = true
          that.handleBorrowingFile(fileData)
        })
      } else {
        this.$prompt('请输入驳回理由', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPlaceholder: '最多可录入50字'
        }).then(({ value }) => {
          that.loading = true
          fileData.approveMemo = value || ''
          if (fileData.approveMemo.length < 50) {
            that.handleBorrowingFile(fileData)
          } else {
            that.loading = false
            that.$kiko_message('最多可录入50字')
          }
        })
      }
    },
    handleBorrowingFile: function (fileData) {
      let that = this
      this.$http.post('../../../read/handleBorrowingFile.do', fileData, {
        emulateJSON: true
      }).then((response) => {
        that.loading = false
        let data = response.body.data
        let status = data.status
        if (status === '1') {
          that.$kiko_message('操作成功')
          gohistory(that)
        } else {
          that.$kiko_message('借阅文件已被审批不用重复操作')
        }
      }).catch(function () {
        that.$kiko_message('系统错误，请重试！')
      })
    }
  }
}
</script>
