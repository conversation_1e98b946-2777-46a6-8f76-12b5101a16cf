
td,th{ 
	/*text-align:center!important; */
	vertical-align:middle !important;
	overflow:hidden;white-space:normal; word-break:break-all;text-overflow:ellipsis;
	
 }

.clr{ clear:both;  }
.Con_continer{
	padding-top: 15px;
}
.Con_con{
	padding:15px 10px;
	border: 1px solid #e4e4e4;
}
.Con_bor{
	padding: 15px 15px 15px 20px;
}
.set_title{
	padding: 15px 0;
}
.set_title>span{
	display: inline-block;
	width: 60px;
	text-align: center;
	position: relative;
	top: -15px;
}
.set_title>div{
	display: inline-block;
}
.title_bar{
	float: left;
	border: 1px solid #c9c9c9;
	border-right: none;
	width: 120px;
	text-align: center;
	cursor: pointer;
}
.title_bar:last-child{
	border-right: 1px solid #c9c9c9;
}
.title_bar>span{
	display: inline-block;
	padding: 8px 2px;
	height: 40px;
	/*border-bottom: 4px solid;*/
}
.bar_active{
	border-bottom: 4px solid #32c5d2;
}
.bar_normal{
	border-bottom: 4px solid #fff;
}
.bg{
	background-color: #f1f1f1;
}
.bg_col{
	background-color: #e4e4e4;
}
.aboutbtn{
	display:inline-block;
	cursor:pointer;
	margin-right: 5px;
	padding:5px 25px;
	font-weight:400;
}
.azury,.azury:hover{
	color:#fff;
	background:#3598dc;
	text-decoration: none;
}
.azury:hover{
	color: #fff;
	background-color: #217ebd;
}
.aboutbtn_add{
	display:inline-block;
	cursor:pointer;
	margin-right: 5px;
	padding:8px 35px;
	font-weight:400;
}
.azury_add,.azury_add:hover{
	color:#fff;
	background:#3598dc;
	text-decoration: none;
}
.azury_add:hover{
	color: #fff;
	background-color: #217ebd;
}
.aboutbtn_search{
	display:inline-block;
	cursor:pointer;
	margin-right: 5px;
	padding:8px 9px;
	font-weight:400;
}
.azury_search,.azury_search:hover{
	color:#fff;
	background:#999;
	text-decoration: none;
}
.azury_search:hover{
	color: #fff;
	background-color: #767676;
}
.aboutbtn_out{
	display:inline-block;
	cursor:pointer;
	margin-right: 5px;
	padding:8px 38px;
	font-weight:400;
}
.azury_out,.azury_out:hover{
	color:#fff;
	background:#999;
	text-decoration: none;
}
.azury_out:hover{
	color: #fff;
	background-color: #767676;
}
.aboutbtn_print{
	display:inline-block;
	cursor:pointer;
	margin-right: 5px;
	padding:8px 38px;
	font-weight:400;
	border: 1px solid #e4e4e4;
	float: right;
}
.azury_print,.azury_print:hover{
	color:#34495e;
	text-decoration: none;
}
.aboutbtn1{
	display:inline-block;
	cursor:pointer;
	margin-right: 5px;
	padding:5px 25px;
	font-weight:400;
}
.azury1,.azury1:hover{
	color:#fff;
	background:#868686;
	text-decoration: none;
}
.azury1:hover{
	color: #fff;
	background-color: #777;
}
/*.resourceAddBtn{ position: relative; top:-4px; }*/
.setTbMoney{
	text-align: left!important;
}
.set_tbmoney{
	text-indent:2em;
}
.setAddUp>span{
	display: inline-block;
	cursor: pointer;
}
.set_addup>span{
	display: inline-block;
	/*width: 40%;*/
	/*margin-left: 10px;*/
	cursor: pointer;
}
.btn_hd,.update_hd{
	display: none;
}
/*各种弹框的宽度设置*/
.bonceContainer{
	width:40%;
}
.bonceHead>span{
	font-weight: 400;
	font-size: 16px;

}
.subject_Input>input , .subject_Input>select{
	width: 50%!important;
	height: 34px!important;
	margin-left: 25px;
	text-indent:2em;

}
.subject_Input>span{
	display: inline-block;
	width: 26%;
	text-align: right;
	vertical-align: middle;
}
.choose_Input>input{
	width: 62%!important;
	height: 34px!important;
	margin-left: 25px;


}
/*.hr, p{*/
	/*margin: 10px 0!important;*/
/*}*/
.choose_Input>span{
	display: inline-block;
	width: 22%;
	text-align: right;
	color: #6b6b6b;
}
.choose_point>span:nth-child(1){
	position: relative;
	/*top: -12px;*/
}
.choose_Input>span:nth-child(2){
	display: inline-block;
	width: 62%!important;
	height: 34px!important;
	margin-left: 25px;
	border: 1px solid #ccc;
}
.subject_bg>input{
	background-color:#e4e4e4;
}
.subject_bor>input{
	background-color: #fff!important;
}
.choose_conbar{
	padding: 10px 0;
	width:100%;
    font-size:0;
}
.conbar_active{
	background-color: #f2f2f2!important;
}
.conbar_normal{
	border:none!important;
}
.titlebar_active{
	background-color: #f2f2f2!important;
	border: 1px solid #ddd!important;
	border-bottom: none!important;

}
.titlebar_normal{
	border:none!important;

}
.choose_container{
	padding: 20px;
	background-color: #f2f2f2;
	border: 1px solid #ddd;
	border-top: none;
}
.container_deal{
	background-color: #fff;
}
.nav-pills, .nav-tabs{
	margin-bottom: 0!important;
}
.conbar_border, .conbar_border > tbody > tr > td, .conbar_border > tbody > tr > th, .conbar_border > tfoot > tr > td,
 .conbar_border > tfoot > tr > th, .conbar_border > thead > tr > td, .conbar_border > thead > tr > th{
 	border: 1px solid #ddd!important;
 }
 .normal{
 	border: none;
 }
.con_c{
	color: #999;
}
.num{
	color: #34495e;
}
.choose_Input>select{
	border: 1px solid #ccc;
	height: 34px;
	width: 62%;
	margin-left: 25px;
}
.choose_con{
	font-size: 13px;
}
.chooseBtn{
	text-align: right;
	width: 90%;
}
.aboutbtn_submit{
	display:inline-block;
	cursor:pointer;
	margin-right: 5px;
	padding:5px 15px;
	font-weight:400;
}
.azury_submit,.azury_submit:hover{
	color:#fff;
	background:#e7505a;
	text-decoration: none;
}
.azury_submit:hover{
	color: #fff;
	background-color: #e12330;
}
.aboutbtn_reject{ display:inline-block; padding:7px 25px; cursor:pointer; font-size:12px;   }
.azury_reject,.azury_reject:hover{
	color:#fff;
	background:#32C5D2;
	text-decoration: none;
}
.azury_reject:hover{
	color: #fff;
	background-color:#3ea49b;

}
.table_con{
	margin-top: 30px;
	font-size: 14px;

}
.t_left{
	text-align: right !important;
	font-weight: 700;
}
.t_right{
	text-align: left !important;
	font-weight: 400;
}
.choice{
	cursor: pointer!important;
}
.Manager_search{
	text-align: right;
	padding: 3px 20px 25px 20px;
}
.Manager_search>span{
	display: inline-block;
	width: 70px;
	text-align: center;
}
.Manager_search>select{
	border: 1px solid #e4e4e4;
	height: 35px;
	background-color: #999;
}
.aboutbtn_out{
	margin:0 15px;
}
.entering_addcon{
	padding: 20px 20px 15px 0;
	text-align: right;
}
.enter_bor{
	border: 1px solid #ddd;
	padding: 10px 0;
	border-bottom: none;
}
.enter_borlast{
	border: 1px solid #ddd;
	padding: 10px 0;
}
.enter_contwo{
	margin-top: 25px;
}
.enter_last{
	border: 1px solid #ddd;
	padding: 10px 0;
}
.enter_last>input{
	height: 60px!important;
}
.pic{
	margin: 30px 10px 30px 0;
	padding:9px 30px 14px 20px;
	background: #fff
	url(icon/warn.png) no-repeat center center;
	background-size: 40px;

}
.back{
	margin: 30px 0;
	padding:9px 30px 14px 20px;
	background: #fff
	url(icon/back.png) no-repeat center center;
	background-size: 22px;
	cursor: pointer;

}
.book{
	background: #fff
	url(icon/book.png) no-repeat 98% center;
	background-size: 24px;
	cursor: pointer;

}
.authority_con{
	margin-top: 15px;
}
.btn_con{
	text-align: right;
	padding: 20px 55px 5px 0;
	float:right ;
}
.authority_container{
	margin:15px ;
	padding: 20px 25px;
	border: 1px solid #e4e4e4;
}
.authority_table>thead,.authority_table>tbody{
	border: 1px solid #e4e4e4;
}
.back_set{
	display: inline-block;
	font-weight: 400;
	font-size: 18px;
}
.subject_set>span{
	display: inline-block;
	border: 1px solid #e4e4e4;
	width: 100%;
	/*height: 24px;*/
	text-align: center;
	background-color: #fff;
}
.set_bg>span{
	background-color: #999;
}
.authority_hd,.waitPend_hd,.approved_hd,.reject_hd,.waitpend_container,.hadapproved_container,.hadreject_container{
	display:none;
}
/*.manage_conon>div:nth-child(1){*/
	/*border-bottom: 1px solid #eef1f5;*/
	/*padding: 0 0 8px 0;*/
/*}*/
/*.manage_conon>div:nth-child(2){*/
	/*padding: 8px 0 0 0;*/
/*}*/
.updata_conon>div:nth-child(1){
	border-bottom: 1px solid #ddd;
	padding: 0 0 8px 0;
}
.updata_conon>div:nth-child(2){
	padding: 8px 0 0 0;
}
.updata_conon>div:nth-child(3){
	padding: 8px 0 0 0;
	color: #34495e;
}
 ul.secondNav{ height:50px; position: relative; margin: 0; padding:0;   }
 ul.secondNav>li{ display: inline-block; list-style: none;border: 1px solid #E4E4E4;border-bottom:none; vertical-align:bottom;         }
 ul.secondNav>li>a{ display: inline-block; text-align: center; width:130px;height:40px;line-height:40px; text-decoration:none; color:#666;        }
 ul.secondNav>li.active>a{ background: #E4E4E4; color:#333; height:50px; line-height:50px;border-bottom:none;        }

#choose_addAccount , #pend_addAccount , #updata_addAccount{ width:600px; }
.authNav{ height:40px;padding-right:10px; }
.bigBtn{ padding:5px 25px;       }

#addStaff{ width:600px;margin-top: 250px; }
.inputTtl{ display: inline-block; text-align: right; width: 150px; padding-right: 15px;   }
.inputCon{ display:inline-block; width: 300px; padding:2px 10px; line-height: 35px; height:35px;     }
.tip{ text-align: center; color: red;  }

.hd{
	display:none;
}

.con_scan{display: inline-block;background: #fff;text-align: left!important;line-height: 34px;
	width: 50% !important;  height: 34px !important;  margin-left: 25px;  text-indent: 2em;}

/*fdsa*/
/*settleAccount、billManage*/
.search{ float:right;  }
.search>input{ margin-left: 10px; height:30px; padding:0 5px; border: 1px solid #ccc; border-right:none;
	border-radius:4px 0 0 4px; width:320px;  }
.search>span{ display:inline-block;height:30px; float:right; border: 1px solid #ccc; cursor: default;
	background: #0b94ea; color:#fff; line-height:25px; padding:0 10px; border-radius:0 4px 4px 0; }
.back-btn {  margin-bottom: 20px;  }
.specialTab .ty-table thead td {  border-color: #d7d7d7;  background: #e8eeeb;  text-align: center;  }
.specialTab .ty-table td {  padding: 0 5px;}
.ty-table td.memoLimit{overflow: hidden;white-space: nowrap;text-overflow: ellipsis;}
