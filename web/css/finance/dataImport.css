.hd{ display: none;      }
.clr{ clear: both;       }
.btn{ border:1px solid #ccc;    }
.conTTL{  border-bottom: 1px solid #ccc; font-size: 18px; font-weight:700; padding: 25px 20px;   }
.maincontainer{ padding-top:50px;  margin:0 auto;  }
.itemTr{ padding:5px 0 ; position:relative;    }
.itemTrtd1{ text-align:right; padding-right:20px; float: left; width:30%; line-height:40px; }
.itemTrtd2{ padding-left:30%;
	position: relative;    }
.itemTrtd2>span ,.itemTrtd2>input[type="text"] ,.itemTrtd2>select { 
	width:60%; min-width:200px; border:1px solid #ccc; display:inline-block; height:35px; margin:5px 0; 
	vertical-align:middle;padding:5px;         }
.dataContainer{ padding-bottom:300px;      }
.contn{ margin: -40px 0 0 -20px;    }
/*dataMark Style*/
 .dataMark{ width: 100%; height:35px; background:rgba(0,0,0,0.2);position: absolute;z-index:1; top:10px; }
/*radioBtn style*/
span.radioBtn{ padding:0 4px; display:inline-block; width:20px; min-width:20px; height:20px;
	border-radius:10px!important; top:0px; background:#fff; position:relative; border:1px solid #ccc;    }
span.radioBtn>.val{ display:none;    }
span.valShow{ width:150px; min-width:150px; border:none; padding:0 5px; line-height:35px;    }
span.radioBtn>.radioShow{ display:inline-block; background:#fff; border-radius:5px!important; width:10px; height:10px;position: relative;
	top: -1px; }
span.radioActive>.radioShow{  background:#9d9d9d; }
/*acceptSelect*/
/*.acceptSelect{ border:1px solid #ccc; margin:0 auto;     }*/
.acceptSelect{ border:1px solid #ccc;   }
.isGenre{ display: none;  }
.center{ text-align: center;  }
#mtTip{  width:500px;  }

/* pageContainer */
/*.pageContainer{ box-shadow: 0 0 2px #333;position: absolute; z-index: 100; left:50%; width:70%; margin-left:-35%;      }*/
.pageContainer{background: rgba(100,100,100,0.7);width: 100%; z-index:9999; min-height:100%;position: absolute;box-shadow: 0 0 2px #333;}
.bonceCon td , .bonceCon th {  text-align: center;      }
#innerTip{ padding:20px; color:red; text-align: center;	font-size:16px; }
.litpageContainer {width:70%;]}
.ty-optionCon{
	display: none;
    width: 42%;
	height: 150px;
	position: absolute;
	z-index: 10;
	background-color: #fff;
	overflow-y: auto;
}
.ty-optionCon .ty-option{
	width: 100%;
	height: 30px;
	background-color: #f6f6f6;
	color: #666;
	padding-left: 8px;
	line-height: 30px;
}
.ty-optionCon .ty-option:hover{
	background-color: #48cfad;
	color: #fff;
}
.ty-optionCon .ty-optionActive{
	background-color: #A7F1DE;
	color: #fff;
}
#expend_oppcompany + .ty-optionCon{
	box-shadow: 0 3px 5px #cff0e7;
	background-color: #fefefe;
}

span.linkBtn:hover{
	text-decoration: underline;
	color: #5995e1;
}
span.linkBtn{
	cursor: pointer;
	color: #5d9cec;
	padding:3px 10px;
	border:none;
	width: auto;
	text-align: center;
	line-height: 30px;
}
#chooseC{   }
#chooseC li{ padding-left: 50px; }
#chooseC li:hover{ background: rgba(10, 158, 242, 0.05) }
#oppositeCorpDiv ul{
}
#oppositeCorpDiv ul li i{
	margin-right: 15px;
	color: #5d9cec;
}
#oppositeCorpDiv ul li{
	list-style: none;
	line-height: 30px;
}
.stakeholderCategoryContsiner{
	position: relative;
}
#stakeholderCategory option:hover{
	background: #eee;
}
#stakeholderCategory option{
	border-bottom: 1px solid #ddd;
	padding: 0 20px;
	cursor: pointer;
}
#stakeholderCategory{
	position: absolute;
	top:30px;
	background: #fff;
	z-index: 1;
	border: 1px solid #ddd;
	border-bottom: none;
	width: 100%;
	line-height: 28px;
	left: -1px;
}
#stakeholderCategoryText{ display: inline-block; width: 100%; height: 100%; }
.e3>span{
	width: 100px;
	display: inline-block;
	text-align: right;
	margin-right: 10px;
}

.bluetip{
	text-align: center; color: #0b94ea; font-size: 0.8em;
}

.ex3{
	text-align: center;
}
.cp_img_box{ position: relative }
.cp_img_box .fileType span.fa{ display: none; }
.cp_img_box:hover .fileType span.fa{
	display: block;
}
.cp_img_box .fa-times{ position: absolute; right: 6px; top:0px; color: #d64835; }
select{ background: #fff; }
.shuifee{ text-align: center; }
#rateEntryFrm{ width: 600px; }
#rateEntryFrm .rateTtl{ width: 100px; display: inline-block; text-align: right; margin-right: 10px;}
#rateEntryFrm .tip{ text-align: center; font-size: 0.8em; margin-top: -10px; }
#rateEntryFrm .cc>div{ margin-bottom: 10px; }
#rateEntryFrm .sm.form-control{ width: 190px; }
#rateEntryFrm .bg.form-control{ width: 400px; }
#ratePic{
	padding-left:100px;
}

