package cn.sphd.miners.modules.improvement.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.improvement.dao.CiBaseDao;
import cn.sphd.miners.modules.improvement.entity.TCiBase;
import cn.sphd.miners.modules.improvement.service.CiBaseService;
import cn.sphd.miners.modules.personal.dao.ApprovalProcessDao;
import cn.sphd.miners.modules.personal.dao.UserMessageDao;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.entity.UserMessage;
import cn.sphd.miners.modules.personal.service.UserMessageService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserRoleService;
import cn.sphd.miners.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * Created by 朱思旭 on 2018/5/21.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class CiBaseServiceImpl implements CiBaseService {

    @Autowired
    CiBaseDao ciBaseDao;
    @Autowired
    ApprovalProcessDao approvalProcessDao;
    @Autowired
    UserMessageService userMessageService;
    @Autowired
    UserService userService;
    @Autowired
    UserMessageDao userMessageDao;
    @Autowired
    UserRoleService userRoleService;

    @Override
    public void addImprovement(User user, TCiBase tCiBase) {
        if("3".equals(tCiBase.getState())){
            tCiBase.setRegisterApprover(user.getUserID());
            tCiBase.setRegisterApproveTime(new Date());
        }
        tCiBase.setOrg(user.getOid());
        tCiBase.setCreator(user.getUserID());
        tCiBase.setCreateName(user.getUserName());
        tCiBase.setCreateTime(new Date());
        ciBaseDao.save(tCiBase);
        // 新增一条状态是1的处理流程（立案待审批）
        addApprovalProcessApproval(tCiBase.getId(), tCiBase.getCreateTime(), user.getUserName(), "1", null);
        // 新增一条状态是3的处理流程（立案批准）
        if("3".equals(tCiBase.getState())){
            addApprovalProcessApproval(tCiBase.getId(), tCiBase.getCreateTime(), user.getUserName(), "3", null);
        } else {
            //这里少了一个获取核心人物的方法
            String code=userRoleService.getUserRoleCodeByType(7);
            User userCentre=userService.getUserByCoreCode(user.getOid(),code);
            boolean state = addImprovementMessage(1, user, userCentre, tCiBase.getId());
        }
    }

    @Override
    public List<TCiBase> getCiBase(User user, TCiBase tCiBase, Integer role, PageInfo pageInfo) {
        String hql = "from CiBase where state = :state AND org = :org";
        HashMap<String,Object> paramBase = new HashMap<>();
        paramBase.put("state", tCiBase.getState());
        paramBase.put("org", user.getOid());
        //若身份不是核心人物，就是按创建人去查询case
        if(!(role.equals(1))){
            paramBase.put("creator", user.getUserID());
            hql = hql + "  AND creator = :creator";
        }
        List<TCiBase> listBase = null;
        if("6".equals(tCiBase.getState())){
            paramBase.put("settleApproveBegin", tCiBase.getSettleApproveBegin());
            paramBase.put("settleApproveEnd", tCiBase.getSettleApproveEnd());
            hql = hql + " and settleApproveTime between :settleApproveBegin and :settleApproveEnd order by settleTimeFact desc";
            listBase = ciBaseDao.getListByHQLWithNamedParams(hql, paramBase, pageInfo);
        }else {
            hql = hql + " order by settleApproveTime desc";
            listBase = ciBaseDao.getListByHQLWithNamedParams(hql, paramBase);
        }
        for(TCiBase c : listBase){
            if(c.getSettler() != null){
                User userSettle = userService.getUserByID(c.getSettler());
                c.setSettleName(userSettle.getUserName());
            }
        }
        return listBase;
    }

    @Override
    public void updateCiBase(User user, TCiBase tCiBase) {
        String hql = "from CiBase where id = "+ tCiBase.getId();
        TCiBase ciBase = (TCiBase) ciBaseDao.getByHQLWithNamedParams(hql, null);
        ciBase.setState(tCiBase.getState());
        boolean state = false;
        User approveUser = null;
        switch (tCiBase.getState()){
            case "2":
                ciBase.setRegisterApprover(user.getUserID());
                ciBase.setRegisterApproveTime(new Date());
                ciBase.setRegisterRejectReason(tCiBase.getRegisterRejectReason());
                // 新增一条状态是2的处理流程（立案驳回）
                addApprovalProcessApproval(ciBase.getId(), ciBase.getRegisterApproveTime(), user.getUserName(), "2", tCiBase.getRegisterRejectReason());
                approveUser = userService.getUserByID(ciBase.getCreator());
                state = addImprovementMessage(3, user, approveUser, tCiBase.getId());
                updateMessageSate(tCiBase.getId(), user.getUserID(), "1");
                break;
            case "3":
                ciBase.setRegisterApprover(user.getUserID());
                ciBase.setRegisterApproveTime(new Date());
                // 新增一条状态是3的处理流程（立案批准）
                addApprovalProcessApproval(ciBase.getId(), ciBase.getRegisterApproveTime(), user.getUserName(), "3", null);
                approveUser = userService.getUserByID(ciBase.getCreator());
                state = addImprovementMessage(2, user, approveUser, tCiBase.getId());
                updateMessageSate(tCiBase.getId(), user.getUserID(), "1");
                break;
            case "4":
//                ciBase.setSettleTimeFact(Tools.str2Date(tCiBase.getFactTime()));
                ciBase.setSettleTimeFact(NewDateUtils.dateFromString(tCiBase.getFactTime(),"yyyy-MM-dd HH:mm:ss"));
                ciBase.setSettleOpinion(tCiBase.getSettleOpinion());
                ciBase.setSettler(user.getUserID());
                ciBase.setSettleTime(new Date());
                // 新增一条状态是4的处理流程（结案待审批）
                addApprovalProcessApproval(ciBase.getId(), ciBase.getSettleTime(), user.getUserName(), "4", null);
                //少一个获取核心人物的方法
                String code=userRoleService.getUserRoleCodeByType(7);
                approveUser=userService.getUserByCoreCode(user.getOid(),code);
                state = addImprovementMessage(4, user, approveUser, tCiBase.getId());
                break;
            case "5":
                ciBase.setSettleRejectReason(tCiBase.getSettleRejectReason());
                ciBase.setSettleApprover(user.getUserID());
                ciBase.setSettleApproveTime(new Date());
                addApprovalProcessApproval(ciBase.getId(), ciBase.getSettleApproveTime(), user.getUserName(), "5", tCiBase.getSettleRejectReason());
                approveUser = userService.getUserByID(ciBase.getCreator());
                state = addImprovementMessage(6, user, approveUser, tCiBase.getId());
                updateMessageSate(tCiBase.getId(), user.getUserID(), "4");
                break;
            case "6":
                ciBase.setSettleApprover(user.getUserID());
                ciBase.setSettleApproveTime(new Date());
                addApprovalProcessApproval(ciBase.getId(), ciBase.getSettleApproveTime(), user.getUserName(), "6", null);
                approveUser = userService.getUserByID(ciBase.getCreator());
                state = addImprovementMessage(5, user, approveUser, tCiBase.getId());
                updateMessageSate(tCiBase.getId(), user.getUserID(), "4");
                break;
        }
        ciBaseDao.saveOrUpdate(ciBase);
    }

    @Override
    public void centreSettleCase(User user, TCiBase tCiBase) {
        String hql = "from CiBase where id = "+ tCiBase.getId();
        TCiBase ciBase = (TCiBase) ciBaseDao.getByHQLWithNamedParams(hql, null);
//        ciBase.setSettleTimeFact(Tools.str2Date(tCiBase.getFactTime()));
        ciBase.setSettleTimeFact(NewDateUtils.dateFromString(tCiBase.getFactTime(),"yyyy-MM-dd HH:mm:ss"));
        ciBase.setSettleOpinion(tCiBase.getSettleOpinion());
        ciBase.setSettler(user.getUserID());
        ciBase.setSettleTime(new Date());
        ciBase.setSettleApprover(user.getUserID());
        ciBase.setSettleApproveTime(new Date());
        ciBase.setState("6");
        ciBaseDao.saveOrUpdate(ciBase);
        addApprovalProcessApproval(ciBase.getId(), ciBase.getSettleTime(), user.getUserName(), "4", null);
        addApprovalProcessApproval(ciBase.getId(), ciBase.getSettleApproveTime(), user.getUserName(), "6", null);
    }

    @Override
    public HashMap getCiBaseMessage(Integer id, Integer role, Integer userId) {
        String hql = "from CiBase where id = "+ id;
        TCiBase ciBase = (TCiBase) ciBaseDao.getByHQLWithNamedParams(hql, null);
        if (!(role.equals(1))) {
            updateMessageSate(id, userId, ciBase.getState());
        }
        List<ApprovalProcess> listApprovalProcess = new ArrayList<>();
        if(("6".equals(ciBase.getState())) || ("5".equals(ciBase.getState()))){
            //获取结案批准或驳回的审批流程
            ApprovalProcess setApprove = getApprovalProcess(ciBase.getId(), ciBase.getSettleApproveTime(), ciBase.getState());
            listApprovalProcess.add(setApprove);
            //获取结案申请的审批流程
            ApprovalProcess setApply = getApprovalProcess(ciBase.getId(),ciBase.getSettleTime(), "4");
            listApprovalProcess.add(setApply);
            //获取立案批准的审批流程
            ApprovalProcess regApprove = getApprovalProcess(ciBase.getId(),ciBase.getRegisterApproveTime(), "3");
            listApprovalProcess.add(regApprove);
        }else if("4".equals(ciBase.getState())){
            //获取结案申请的审批流程
            ApprovalProcess setApply = getApprovalProcess(ciBase.getId(),ciBase.getSettleTime(), ciBase.getState());
            listApprovalProcess.add(setApply);
            //获取立案批准的审批流程
            ApprovalProcess regApprove = getApprovalProcess(ciBase.getId(),ciBase.getRegisterApproveTime(), "3");
            listApprovalProcess.add(regApprove);
        }else if (("3".equals(ciBase.getState())) || ("2".equals(ciBase.getState()))){
            //获取立案批准或驳回的审批流程
            ApprovalProcess regApprove = getApprovalProcess(ciBase.getId(),ciBase.getRegisterApproveTime(), ciBase.getState());
            listApprovalProcess.add(regApprove);
        }
        //获取立案申请的审批流程
        ApprovalProcess regCreat = getApprovalProcess(ciBase.getId(),ciBase.getCreateTime(), "1");
        listApprovalProcess.add(regCreat);
        HashMap<String, Object> map = new HashMap<>();
        map.put("ciBase", ciBase);
        map.put("approvalProcessList", listApprovalProcess);
        return map;
    }

    private ApprovalProcess getApprovalProcess(Integer id, Date createDate, String approveStatus){
        String hql = "from ApprovalProcess where business = :business AND businessType = :businessType AND createDate = :createDate AND approveStatus = :approveStatus";
        HashMap<String,Object> processParams = new HashMap<>();
        processParams.put("business",id);
        processParams.put("businessType",11);
        processParams.put("createDate", createDate);
        processParams.put("approveStatus", approveStatus);
        ApprovalProcess approvalProcess = (ApprovalProcess) approvalProcessDao.getByHQLWithNamedParams(hql, processParams);
        return approvalProcess;
    }

    @Override
    public HashMap upCreator(Integer id, Integer userID, String userName) {
        String hql = "from CiBase where id = "+ id;
        TCiBase ciBase = (TCiBase) ciBaseDao.getByHQLWithNamedParams(hql, null);
        Integer state = 1;
        if(userID.equals(ciBase.getCreator())){
            state = 0;
        }else {
            String hqlDelMes = "delete from UserMessage  where messageId = :messageId and messageType = :messageType and handleId = :handleId";
            HashMap<String, Object> mesParam = new HashMap<>();
            mesParam.put("messageId", ciBase.getId());
            mesParam.put("messageType", "13");
            mesParam.put("handleId", ciBase.getCreator().toString());
            ciBaseDao.queryHQLWithNamedParams(hqlDelMes, mesParam);

            ciBase.setCreator(userID);
            ciBase.setCreateName(userName);
            ciBaseDao.saveOrUpdate(ciBase);
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        map.put("ciBase", ciBase);
        return map;
    }

    private void addApprovalProcessApproval(Integer id, Date time, String userName, String state, String reason){
        ApprovalProcess ap = new ApprovalProcess();
        if (reason != null) {
            ap.setReason(reason);
        }
        ap.setApproveStatus(state);
        ap.setBusiness(id);
        ap.setBusinessType(11);
        ap.setToUserName(userName);
        ap.setCreateDate(time);
        approvalProcessDao.save(ap);
    }

    //新增消息
    private boolean addImprovementMessage(Integer type, User creatUser, User approveUser, Integer id){
        UserMessage userMessage=new UserMessage();
        userMessage.setUser(creatUser);
        userMessage.setHandleId(approveUser.getUserID().toString());
        userMessage.setHandleName(approveUser.getUserName());
        userMessage.setMessageId(id);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
        String date = sdf.format(new Date());
        if(type == 1){
            userMessage.setEventType("持续改进");
            userMessage.setIllustrate( date +"有新的持续改进项目");
            userMessage.setApprovalStatus(1);
        }else if(type == 2){
            userMessage.setEventType("持续改进");
            userMessage.setIllustrate(date +"持续改进立案申请已立案");
            userMessage.setApprovalStatus(3);
        }else if(type == 3){
            userMessage.setEventType("持续改进");
            userMessage.setIllustrate(date +"持续改进立案申请被驳回");
            userMessage.setApprovalStatus(2);
        }else if(type == 4){
            userMessage.setEventType("持续改进");
            userMessage.setIllustrate(date +"有新的持续改进结案申请");
            userMessage.setApprovalStatus(4);
        }else if(type == 5){
            userMessage.setEventType("持续改进");
            userMessage.setIllustrate(date +"持续改进结案申请已结案");
            userMessage.setApprovalStatus(6);
        }else if(type == 6){
            userMessage.setEventType("持续改进");
            userMessage.setIllustrate(date +"持续改进结案申请被驳回");
            userMessage.setApprovalStatus(5);
        }
        userMessage.setMessageType("13");
        userMessage.setState(1);
        userMessage.setCreateDate(new Date());
        userMessage.setReceiveUserId(approveUser.getUserID()); //接收消息人
        userMessageService.addUserMassage(userMessage);
        return true;
    }

    //修改消息状态
    void updateMessageSate(Integer messageId, Integer userId, String approvalStatus){
        String hql = "from UserMessage o where o.messageType = 13 and o.messageId = '"+messageId+"' and o.handleId ='" + userId+"' and o.approvalStatus ="+ approvalStatus;
        UserMessage message = userMessageDao.getByHQL(hql);
        if(!(message == null || message.getState() == 2)){
            message.setState(2);
            userMessageDao.saveOrUpdate(message);
        }
    }

}
