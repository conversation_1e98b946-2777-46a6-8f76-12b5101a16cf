<template>
  <div id="formulaEditSearchList" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="配方修改"></TY_NavTop>
    <div class="container">
      <div class="ui-message">
        <div class="ttl11">
          <p>您要查询：</p>
          <p>{{ beginTimeShow | formatDay('YYYY年MM月DD日')}} - {{ endTimeShow | formatDay('YYYY年MM月DD日')}}期间</p>
          <p v-if="userType==1">{{ paramQuery.approvalStatus | formatApplyStatus }}的配方修改申请</p>
          <p v-if="userType==2">您经手且{{ paramQuery.approvalStatus | formatApprovalStatus }}的方修改申请</p>
          <p></p>
        </div>
        <div class="ttl1">符合查询条件的数据共如下{{list.length}}条。</div>
      </div>
      <div>
        <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in list" v-bind:key="index">
          <div class="Item">
            <div>{{item.description}}</div>
            <div>{{item.userName}} {{item.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
          </div>
          <div class="ui-cell__ft"></div>
        </a>
      </div>
    </div>
  </div>
</template>
<style lang="less">
  #formulaEditSearchList{
    .Item{
      width:100%;
      div:nth-child(2){ text-align:right;  }
    }
    .ttl1{ background:#DEEBF6; padding:10px;     }
    .ttl11{ background: #edf3c6; padding:10px;     }
    .container>div>a{  background: #fff; }
    .container>div>a:nth-child(odd){ background:#f3f3f3;  }
  }
</style>
<script>
export default {
  name: 'formulaEditSearchList',
  data () {
    return {
      beginTimeShow: '',
      loading: true,
      endTimeShow: '',
      list: [],
      num: true,
      paramQuery: {},
      queryData: {
        data: [],
        beginDate: '',
        endDate: ''
      }
    }
  },
  created: function () {
    let paramQuery = JSON.parse(localStorage.getItem('query'))
    this.paramQuery = paramQuery
    let that = this
    let url = '../../../formula/selectFormulaEditList.do'
    let data = {
      state: paramQuery.approvalStatus,
      startTime: paramQuery.beginDate,
      endTime: paramQuery.endDate,
      type: paramQuery.userType,
      date: paramQuery.type
    }
    console.log('url：', url)
    console.log('打印传参：')
    console.log(data)
    that.axios.post(url, data).then((response) => {
      that.loading = false
      console.log('返回值')
      console.log(response)
      let data = response.data
      that.beginTimeShow = data.startTime
      that.endTimeShow = data.endTime
      if (data) {
        that.list = data.list || []
      } else {
        that.$kiko_message('加载失败')
      }
    }).catch(function (res) {
      console.log(res)
    })
  },
  filters: {
    formatApplyStatus: function (ApprovalStatus) {
      let str = ''
      switch (Number(ApprovalStatus)) {
        case 2:
          str = '审批通过'
          break
        case 3:
          str = '被驳回'
          break
        default:
          str = '全部'
      }
      return str
    },
    formatApprovalStatus: function (ApprovalStatus) {
      let str = ''
      switch (Number(ApprovalStatus)) {
        case 2:
          str = '审批通过'
          break
        case 3:
          str = '审批驳回'
          break
        default:
          str = '全部'
      }
      return str
    }
  },
  methods: {
    jump: function (id) {
      this.$router.push({
        path: '/formulaEditApplyDetail/' + id
      })
    }
  }
}
</script>
