package cn.sphd.miners.modules.generalAffairs.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.generalAffairs.service.PersonalService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.entity.UserLog;
import cn.sphd.miners.modules.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.Collator;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by Administrator on 2016/8/12.
 */
@Controller
@RequestMapping("/loginrecord")
public class LoginRecordController {

    @Autowired
    UserService userService;
    @Autowired
    PersonalService personalService;
    @Autowired
    OrgService orgService;
    @Autowired
    UserLogService userLogService;
    @Autowired
    RoleService roleService;
    @Autowired
    ApprovalService approvalService;
    @Autowired
    UserPopedomService userPopedomService;
    @Autowired
    PopedomService popedomService;

    //我的登录记录跳转页面
    @RequestMapping("/toMyLoginLog.do")
    public String toMyLoginLog() {
        return "home/myLoginLog";
    }

    /**
     * @Author:林成
     * @Date:2017/9/8 16:32 //wyu：20180410 目前这个是有效的
     * @description:登录记录1.22版--主列表分页
     * sort：0-按时间操作 1-按姓名操作
     */
    @RequestMapping("/loginRecords.do")
    public void loginRecords(User user, Integer userId, String beginTime, String endTime, Integer sort, Integer currPage, Integer flag, Integer pageSize, HttpServletResponse response) throws IOException, ParseException {

        userLogService.loginRecords(user.getOid(),null,userId,beginTime,endTime,sort,currPage,flag,pageSize,response);
    }

    /**
     * @Author:林成
     * @Date:2017/9/8 16:32
     * @description:登录记录1.22版--按姓名/按时间查看-查看分页
     * @Date:2021/3/2 1.143桌面之考勤与登录记录的项目进行修改
     * Integer sort：0-按时间操作 1-按姓名操作
     */
    @RequestMapping("/loginRecordPersonal.do")
    public void loginRecordPersonal(User user,String loginYear, String mdate, String edate, Integer sort, Integer sign, Integer userID,Integer currPage, Integer flag, Integer pageSize, HttpServletResponse response) throws IOException, ParseException {

        userLogService.loginRecordPersonal(user.getOid(),null,loginYear,mdate,edate,sort,sign,userID,currPage,flag,pageSize,response);
    }

    /**
     * <AUTHOR>
     * @date 2017/10/20 16:06
     * list里套map
     * 根据时间倒序
     */
    public static void sort(List<Map<String, Object>> list, final String args) {
        if (!"".equals(args) && args != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Collections.sort(list, new Comparator<Map<String, Object>>() {
                public int compare(Map<String, Object> mapOne, Map<String, Object> mapTwo) {
                    int flag = 0;
                    Date f1 = null;
                    Date f2 = null;
                    try {
                        f1 = sdf.parse(mapOne.get(args).toString());
                        f2 = sdf.parse(mapTwo.get(args).toString());

                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                    Double first = Double.valueOf(f1.getTime());
                    Double second = Double.valueOf(f2.getTime());

                    if ((first - second) > 0) {
                        flag = -1;
                    } else if ((first - second) < 0) {
                        flag = 1;
                    } else {
                        flag = 0;
                    }
                    return flag;
                }

                ;
            });
        }
    }

    /**
     * Created by Administrator on 2017/9/11.
     */
    public static class ComparatorUser1 implements Comparator {
        @Override
        public int compare(Object o1, Object o2) {//-1代表前者小，0代表两者相等，1代表前者大
            UserLog info1=(UserLog)o1;
            UserLog info2=(UserLog)o2;
            if(null==info1.getUserName()&& null!=info2.getUserName()){
                return 1;//
            }else if(null!=info1.getUserName()&& null==info2.getUserName()){
                return -1;
            }else if(null==info1.getUserName()&& null==info2.getUserName()){
                return 0;
            }
            else {
                return Collator.getInstance(Locale.CHINA).compare(info1.getUserName(), info2.getUserName());
            }
        }
    }

    @ResponseBody
    @RequestMapping("test.do")
    public JsonResult test(){
        Map<String,Object> map = new HashMap<>();
        String mdate = "2022-01-12";
//        mdate = mdate.substring(0,7);
        Date date = NewDateUtils.dateFromString(mdate,"yyyy-MM-dd");
        map.put("date",date);
        return new JsonResult(1,map);
    }
}