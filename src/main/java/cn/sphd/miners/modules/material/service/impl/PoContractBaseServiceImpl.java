package cn.sphd.miners.modules.material.service.impl;

import cn.sphd.miners.modules.accountant.util.DateUtil;
import cn.sphd.miners.modules.material.dao.*;
import cn.sphd.miners.modules.material.entity.*;
import cn.sphd.miners.modules.material.service.PoContractBaseService;
import cn.sphd.miners.modules.sales.entity.*;
import cn.sphd.miners.modules.sales.service.impl.ContractBaseUsing;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.uploads.service.UploadService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.hibernate.criterion.CriteriaSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.*;

@Service
public class PoContractBaseServiceImpl implements PoContractBaseService {

    @Autowired
    private PoContractBaseDao poContractBaseDao;
    @Autowired
    private PoContractBaseHistoryDao poContractBaseHistoryDao;
    @Autowired
    private UploadService uploadService;
    @Autowired
    private PoContractImageDao poContractImageDao;
    @Autowired
    private PoContractImageHistoryDao poContractImageHistoryDao;
    @Autowired
    private PoContractMaterialDao poContractMaterialDao;
    @Autowired
    private PoContractMaterialHistoryDao poContractMaterialHistoryDao;
    @Override
    public String addContractBaseList(String contractBaseList, Integer supplierId, User user) {
        if (contractBaseList != null && !"".equals(contractBaseList)) {//添加合同信息
            JSONArray cbs = JSONArray.fromObject(contractBaseList);
            if (cbs.size() > 0) {

                for (int i = 0; i < cbs.size(); i++) {
                    JSONObject a = cbs.getJSONObject(i);
                    addContractBase(a,supplierId,null,user);
                }
            }
        }
        return "success";
    }

    @Override
    public List<Map<String, Object>> getContractBaseListBySupplier(Integer supplierId) {
        HashMap<String, Object> params = new HashMap<>();
        params.put("supplier",  supplierId);
        String hql="select new Map(sb.id as id,sb.sn as sn,sb.enabled AS enabled,sb.validStart as validStart,sb.validEnd as validEnd,sb.signTime as signTime," +
                "(select count(0) from PoContractMaterial as scc where scc.contract= sb.id) as num ) from PoContractBase sb where sb.supplier=:supplier and  sb.enabled=1 and sb.isExpired=0 ";

        List<Map<String, Object>> list= (List<Map<String, Object>>) poContractBaseDao.getListByHQLWithNamedParams(hql,params);
        if(list==null){
            list=new ArrayList<>();
        }
        return list;
    }

    @Override
    @Transactional
    public String addContractBase(String contractBase, Integer supplierId, User user) {
        JSONObject a = JSONObject.fromObject(contractBase);


        try {
            addContractBase(a,supplierId,null,user);
        }catch (Exception e){
            return e.getMessage();
        }
        return "success";
    }

    @Override
    @Transactional
    public String updateContractBase(String contractBase, User user) {
        JSONObject a = JSONObject.fromObject(contractBase);
        //合同id
        Integer id = a.optInt("id");
        //合同编号
        String sn = a.optString("sn");
        //签署时间
        String signTime = a.optString("signTime");
        //有效起始时间
        String validStart = a.optString("validStart");
        //有效截止时间
        String validEnd = a.optString("validEnd");
        //合同的可编辑版
        String filePath = a.optString("filePath");
        //合同的可编辑版名称
        String fileName = a.optString("fileName");
        //备注
        String memo = a.optString("memo");
        //合同的照片
        String contractBaseImages = a.optString("contractBaseImages");

        //材料列表
        String mtList = a.optString("mtList");

        PoContractBase poContractBase = poContractBaseDao.get(id);
        //删除合同文档
        if(poContractBase.getFilePath()!=null&&!"".equals(poContractBase.getFilePath())){
            //删除合同文档
            SrmSupplierUsing sb=new SrmSupplierUsing(poContractBase.getId(),PoContractBase.class);
            uploadService.delFileUsing(sb,poContractBase.getFilePath(),user);
        }
        poContractBase.setSn(sn);
        poContractBase.setUpdator(user.getUserID());
        poContractBase.setUpdateDate(new Date());
        poContractBase.setUpdateName(user.getUserName());
        poContractBase.setSignTime(DateUtil.fomatDate(signTime));
        poContractBase.setValidStart(DateUtil.fomatDate(validStart));
        poContractBase.setValidEnd(DateUtil.fomatDate(validEnd));
        poContractBase.setFilePath(filePath);
        poContractBase.setMemo(memo);
        poContractBase.setOperation("3");

        poContractBaseDao.update(poContractBase);
        PoContractBaseHistory his=poContractBaseHistoryDao.insert(poContractBase);

        if(filePath!=null&&!"".equals(filePath)){
            //添加图片库
            SrmSupplierUsing sb=new SrmSupplierUsing(poContractBase.getId(),PoContractBase.class);
            uploadService.addFileUsing(sb,poContractBase.getFilePath(),fileName,user,"供应商");
            SrmSupplierUsing sbh=new SrmSupplierUsing(his.getId(),PoContractBaseHistory.class);
            uploadService.addFileUsing(sbh,his.getFilePath(),fileName,user,"供应商");
        }



        //删除合同图片
        List<PoContractImage> imageList=poContractImageDao.getListByHQL("from PoContractImage where contract="+id);
        for (PoContractImage ci:imageList) {
            SrmSupplierUsing cbu=new SrmSupplierUsing(ci.getId(),PoContractImage.class);
            uploadService.delFileUsing(cbu,ci.getUplaodPath(),user);

            List<PoContractImageHistory> hlist=poContractImageHistoryDao.getListByHQL("from PoContractImageHistory where contractImage="+ci.getId());
            for (PoContractImageHistory sih:hlist) {
                SrmSupplierUsing cbhu=new SrmSupplierUsing(sih.getId(),PoContractImageHistory.class);
                uploadService.delFileUsing(cbhu,sih.getUplaodPath(),user);
                poContractImageHistoryDao.delete(sih);
            }
            poContractImageDao.deleteById(ci.getId());
        }

        //添加合同图片
        if (contractBaseImages != null && !"".equals(contractBaseImages)) {
            JSONArray ims = JSONArray.fromObject(contractBaseImages);
            for (int j = 0; j < ims.size(); j++) {
                JSONObject im = ims.getJSONObject(j);
                String normal = im.optString("filePath");
                Integer order= im.optInt("order");
                String type=im.optString("type");
                String title=im.optString("title");
                PoContractImage poContractImage=new PoContractImage();
                poContractImage.setOrg(user.getOid() );
                poContractImage.setCreator(user.getUserID());
                poContractImage.setCreateDate(new Date());
                poContractImage.setCreateName(user.getUserName());
                poContractImage.setUplaodPath(normal);
                poContractImage.setOrders(order);
                poContractImage.setType(type);
                poContractImage.setTitle(title);
                poContractImage.setContract(poContractBase.getId());
                poContractImage.setOperation("1");
                poContractImage.setVersionNo(0);
                poContractImageDao.save(poContractImage);
                PoContractImageHistory sih=poContractImageHistoryDao.insert(poContractImage,his.getId());

                //添加图片库
                SrmSupplierUsing siu=new SrmSupplierUsing(poContractImage.getId(),PoContractImage.class);
                uploadService.addFileUsing(siu,poContractImage.getUplaodPath(),title,user,"供应商");
                SrmSupplierUsing sihu=new SrmSupplierUsing(sih.getId(),PoContractImageHistory.class);
                uploadService.addFileUsing(sihu,sih.getUplaodPath(),title,user,"供应商");

            }
        }

        //删除关联材料
        List<PoContractMaterial> pros=poContractMaterialDao.getListByHQL("from PoContractMaterial where contract="+id);
        for (PoContractMaterial p:pros) {
            poContractMaterialHistoryDao.deleteByCommodity(p.getId());
            poContractMaterialDao.delete(p);
        }

        //关联商品
        if (mtList != null && !"".equals(mtList)) {
            JSONArray proList = JSONArray.fromObject(mtList);
            for (int j = 0; j < proList.size(); j++) {
                JSONObject pro = proList.getJSONObject(j);
                Integer order= pro.optInt("order");
                Integer mtId=pro.optInt("mtId");
                PoContractMaterial scc=new PoContractMaterial();
                scc.setOrg(user.getOid() );
                scc.setCreator(user.getUserID());
                scc.setCreateDate(new Date());
                scc.setCreateName(user.getUserName());
                scc.setOrders(order);
                scc.setContract(poContractBase.getId());
                scc.setMaterial(mtId);
                scc.setOperation("1");
                scc.setVersionNo(0);
                poContractMaterialDao.save(scc);
                poContractMaterialHistoryDao.insert(scc,his.getId(),null);

            }
        }

        return "success";

    }

    @Override
    @Transactional
    public String renewalContractBase(String contractBase, User user) {
        JSONObject a = JSONObject.fromObject(contractBase);
        //合同id
        Integer id = a.optInt("id");

        PoContractBase sl = poContractBaseDao.get(id);

        sl.setIsExpired(0);
        poContractBaseDao.update(sl);

        try {
            updateContractBase(contractBase,user);
        } catch (Exception e) {

            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return "商品不存在或已被删除!";
        }

        return "success";
    }

    @Override
    public PoContractBase getContractBase(Integer id) {
        PoContractBase poContractBase=poContractBaseDao.get(id);
        //获取文件列表
        String hql1="select new Map(i.orders as orders,i.id as id,i.uplaodPath as  filePath,i.title as title ) from PoContractImage i where i.contract="+id;
        List<Map<String,Object>> list1=(List<Map<String, Object>>) poContractImageDao.getListByHQLWithNamedParams(hql1,new HashMap<>());

        //获取材料列表
        String hql2="select new Map(p.id as id,p.name as name,p.code as code,p.model as model,p.specifications as specifications,u.name  as unit) from MtBase p   LEFT JOIN MtUnit u on u.id=p.unitId \n" +
                "LEFT JOIN PoContractMaterial cc on p.id=cc.material\n" +
                "where cc.contract="+id;
        List<Map<String,Object>> list2=poContractMaterialDao.getListByHQLWithNamedParams(hql2,new HashMap<>());

        //获取暂停恢复记录列表
        String hql3="select new Map(enabled as enabled,enabledTime as  enabledTime,updateName as  updateName ) from PoContractBaseHistory \n" +
                " where contract="+id+" and operation='4'";
        List<Map<String,Object>> list3=poContractBaseHistoryDao.getListByHQLWithNamedParams(hql3,new HashMap<>());

        //暂停/恢复记录
        poContractBase.setContractBaseImages(list1);
        poContractBase.setMtList(list2);
        poContractBase.setEnabledList(list3);
        return poContractBase;
    }

    @Override
    public List<Map<String, Object>> getSuspendContractBaseList(Integer supplierId) {
        String hql="select new Map(id as id,sn as sn,enabled as enabled,enabledTime as  enabledTime)  from PoContractBase where supplier=" + supplierId+" and enabled=0 and isExpired=0";

        List<Map<String, Object>> list= (List<Map<String, Object>>) poContractBaseDao.getListByHQLWithNamedParams(hql,new HashMap<>());
        if(list==null){
            list=new ArrayList<>();
        }
        return list;
    }

    @Override
    public List<Map<String, Object>> getContractBaseEndList(Integer supplierId) {
        String sql="select sb.id as id,sb.sn as sn,sb.enabled as enabled,sb.valid_end validEnd,\n" +
                "( select count(0) from t_po_contract_base  b where b.trace_path like  concat(sb.id,',%') or b.trace_path like  concat('%,',sb.id,',%') or  b.trace_path like concat('%,',sb.id)  or b.trace_path = sb.id ) num\n" +
                " from t_po_contract_base sb where  sb.supplier="+supplierId+" and sb.is_expired=1 \n" +
                "HAVING num =0";

        List<Map<String, Object>> list= (List<Map<String, Object>>) poContractBaseDao.findMapByConditionNoPage(sql,new Object[]{}).get("data");
        if(list==null){
            list=new ArrayList<>();
        }
        return list;
    }

    @Override
    @Transactional(readOnly = false)
    public String suspendOrStartContractBase(Integer id, Integer enabled, User user) {
        PoContractBase poContractBase=poContractBaseDao.get(id);
        //停用
        if(enabled==0){
            poContractBase.setEnabled(enabled);
            poContractBase.setEnabledTime(new Date());
            poContractBase.setOperation("4");
            poContractBase.setUpdateName(user.getUserName());
            poContractBase.setUpdator(user.getUserID());
            poContractBase.setUpdateDate(new Date());
            poContractBase.setVersionNo(poContractBase.getVersionNo()+1);
        }else{
            //启用
            String sql="select cc.* from t_po_contract_material cc \n" +
                    "LEFT JOIN t_po_contract_base cb on cb.id=cc.contract\n" +
                    "where cc.material in (select c.material from t_po_contract_material c where c.contract="+id+") \n" +
                    "and cc.id is not null and cb.enabled=1 and cb.is_expired='0' and cb.supplier="+poContractBase.getSupplier();
            List<Map<String,Object>> list=poContractMaterialDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();
            if(list.size()>0){
                return "该合同下已有材料被其他合同使用，不能启用!";
            }
            poContractBase.setEnabled(enabled);
            poContractBase.setEnabledTime(new Date());
            poContractBase.setOperation("4");
            poContractBase.setVersionNo(poContractBase.getVersionNo()+1);
            poContractBase.setUpdateName(user.getUserName());
            poContractBase.setUpdator(user.getUserID());
            poContractBase.setUpdateDate(new Date());
        }
        poContractBaseDao.update(poContractBase);

        poContractBaseHistoryDao.insert(poContractBase);
        return "success";
    }

    @Override
    public List<Map<String, Object>> getNoContractMaterialList(Integer org,Integer supplierId) {
        String sql="select  p.id,p.name as name,p.code as code,p.model,p.specifications,u.name as unit from t_mt_base p LEFT JOIN t_mt_unit u on u.id=p.unit_id  \n" +
                "where p.enabled ='1' and p.org = "+org+" and p.id not in (select cc.material from t_po_contract_material cc LEFT JOIN t_po_contract_base cb on cb.id=cc.contract where  cb.enabled=1 and cb.is_expired=0 ";
        if(supplierId!=null){
           sql=sql+" and cb.supplier="+supplierId+")";
        }else{
            sql=sql+")";
        }

        sql=sql+ " and p.id in (select material from t_mt_supplier_material where supplier="+supplierId+")";
        List<Map<String, Object>> list= (List<Map<String, Object>>) poContractBaseDao.findMapByConditionNoPage(sql,new Object[]{}).get("data");
        if(list==null){
            list=new ArrayList<>();
        }
        return list;
    }

    @Override
    public List<Map<String, Object>> getMtListByContract(Integer id) {
        String hql = "select new Map(o.id as id,o.name as name,o.invoiceCategory as invoiceCategory,o.outerSn as outerSn,o.specifications as specifications,o.model as model," +
                "o.unit as unit,o.unitPrice as unitPrice,o.unitPriceNotax as unitPriceNotax,o.unitPriceInvoice as unitPriceInvoice,o.unitPriceNoinvoice as unitPriceNoinvoice," +
                "o.unitPriceReference as unitPriceReference)from PdMerchandise o where o.id in (select cc.commodity from SlContractCommodity cc where cc.contract="+id+")";

        HashMap<String, Object> params = new HashMap<>();

        List<Map<String,Object>> list=poContractBaseDao.getListByHQLWithNamedParams(hql,params);
        for (Map<String,Object> pp:list) {
            //获取文件列表
            String sql="select i.orders,i.id,i.uplaod_path uplaodPath,i.title,i.type from t_pd_commodity_media i\n" +
                    "where i.commodity="+pp.get("id");
            List<Map<String,Object>> fileList=poContractBaseDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();
            pp.put("fileList",fileList);
        }
        return list;
    }


    /**
     *superBase上一个合同
     */

    void addContractBase(JSONObject a, Integer supplierId, PoContractBase superBase, User user) {


        //合同编号
        String sn = a.optString("sn");
        //签署时间
        String signTime = a.optString("signTime");
        //有效起始时间
        String validStart = a.optString("validStart");
        //有效截止时间
        String validEnd = a.optString("validEnd");
        //合同的可编辑版
        String filePath = a.optString("filePath");
        //合同模板文件名称
        String fileName = a.optString("fileName");
        //备注
        String memo = a.optString("memo");
        //合同的照片
        String contractBaseImages = a.optString("contractBaseImages");

        //材料列表
        String mtList = a.optString("mtList");

        PoContractBase poContractBase = new PoContractBase();
        poContractBase.setSn(sn);
        poContractBase.setOrg(user.getOid());
        poContractBase.setSupplier(supplierId);
        poContractBase.setCreator(user.getUserID());
        poContractBase.setCreateDate(new Date());
        poContractBase.setCreateName(user.getUserName());
        poContractBase.setSignTime(DateUtil.fomatDate(signTime));
        poContractBase.setValidStart(DateUtil.fomatDate(validStart));
        poContractBase.setValidEnd(DateUtil.fomatDate(validEnd));
        poContractBase.setFilePath(filePath);
        poContractBase.setFileName(fileName);
        poContractBase.setEnabled(1);
        poContractBase.setEnabledTime(new Date());
        poContractBase.setIsExpired(0);
        poContractBase.setMemo(memo);
        poContractBase.setOperation("1");
        poContractBase.setVersionNo(1);

        if(superBase!=null){
            poContractBase.setLevel(superBase.getLevel()+1);
            if(superBase.getTracePath()==null||"".equals(superBase.getTracePath())){
                poContractBase.setTracePath(superBase.getId()+"");
            }else{
                poContractBase.setTracePath(superBase.getTracePath()+","+superBase.getId()+"");
            }
        }else{
            poContractBase.setLevel(0);
        }

        poContractBaseDao.save(poContractBase);
        poContractBaseDao.getSession().flush();

        PoContractBaseHistory his=poContractBaseHistoryDao.insert(poContractBase);

        if(filePath!=null&&!"".equals(filePath)){
            //添加图片库
            SrmSupplierUsing sb=new SrmSupplierUsing(poContractBase.getId(),PoContractBase.class);
            uploadService.addFileUsing(sb,poContractBase.getFilePath(),fileName,user,"供应商");
            SrmSupplierUsing sbh=new SrmSupplierUsing(his.getId(),PoContractBaseHistory.class);
            uploadService.addFileUsing(sbh,his.getFilePath(),fileName,user,"供应商");
        }


        //添加合同图片
        if (contractBaseImages != null && !"".equals(contractBaseImages)) {
            JSONArray ims = JSONArray.fromObject(contractBaseImages);
            for (int j = 0; j < ims.size(); j++) {
                JSONObject im = ims.getJSONObject(j);
                String normal = im.optString("filePath");
                Integer order= im.optInt("order");
                String type=im.optString("type");
                String title=im.optString("title");
                PoContractImage poContractImage=new PoContractImage();
                poContractImage.setOrg(user.getOid() );
                poContractImage.setCreator(user.getUserID());
                poContractImage.setCreateDate(new Date());
                poContractImage.setCreateName(user.getUserName());
                poContractImage.setUplaodPath(normal);
                poContractImage.setOrders(order);
                poContractImage.setType(type);
                poContractImage.setTitle(title);
                poContractImage.setContract(poContractBase.getId());
                poContractImage.setOperation("1");
                poContractImage.setVersionNo(0);
                poContractImageDao.save(poContractImage);
                poContractImageDao.getSession().flush();
                PoContractImageHistory sih=poContractImageHistoryDao.insert(poContractImage,his.getId());
                //添加图片库
                SrmSupplierUsing siu=new SrmSupplierUsing(poContractImage.getId(),PoContractImage.class);
                uploadService.addFileUsing(siu,poContractImage.getUplaodPath(),title,user,"供应商");
                SrmSupplierUsing sihu=new SrmSupplierUsing(sih.getId(),PoContractImageHistory.class);
                uploadService.addFileUsing(sihu,sih.getUplaodPath(),title,user,"供应商");

            }
        }

        //关联材料
        if (mtList != null && !"".equals(mtList)) {

            //查询未被选择的商品
            List<Map<String,Object>> proL=getNoContractCommodityMtList(supplierId,user.getOid());

            JSONArray proList = JSONArray.fromObject(mtList);
            for (int j = 0; j < proList.size(); j++) {
                JSONObject pro = proList.getJSONObject(j);
                Integer order= pro.optInt("order");
                Integer mtId=pro.optInt("mtId");

                int flag=0;
                String proName="";
                for (Map<String,Object> m:proL) {
                    Integer pid= (Integer) m.get("id");
                    if(mtId.toString().equals(pid.toString())){
                        proName= (String) m.get("name");
                        flag++;
                        break;
                    }
                }
                if(flag==0){
                    throw new RuntimeException("材料"+proName+"不存在或已被选择!") ;
                }
                PoContractMaterial scc=new PoContractMaterial();
                scc.setOrg(user.getOid() );
                scc.setCreator(user.getUserID());
                scc.setCreateDate(new Date());
                scc.setCreateName(user.getUserName());
                scc.setOrders(order);
                scc.setContract(poContractBase.getId());
                scc.setMaterial(mtId);
                scc.setOperation("1");
                scc.setVersionNo(0);
                poContractMaterialDao.save(scc);
                poContractMaterialDao.getSession().flush();

                poContractMaterialHistoryDao.insert(scc,his.getId(),null);

            }
        }
    }

    private List<Map<String, Object>> getNoContractCommodityMtList(Integer supplierId,Integer oid) {
        String sql="select new Map(m.id as id,m.name as name,m.model as model,m.specifications as specifications,m.unit as unit) from MtBase m\n" +
                "left join PoContractMaterial p on p.material = m.id where m.enabled ='1' and m.org="+oid+" and m.id not in (select cc.material from PoContractMaterial cc LEFT JOIN PoContractBase cb on cb.id=cc.contract where cb.supplier="+supplierId+" and cb.enabled=1 )";
        List<Map<String, Object>> list= (List<Map<String, Object>>) poContractBaseDao.getListByHQLWithNamedParams(sql,null);
        if(list==null){
            list=new ArrayList<>();
        }
        return list;
    }

    @Override
    @Transactional
    public String removeContract(Integer id, User user) {
        PoContractBase contractBase = poContractBaseDao.get(id);
        if (contractBase != null) {
            List<PoContractBaseHistory> poContractBaseHistories = poContractBaseHistoryDao.getListByHQL(" from PoContractBaseHistory o where o.contract=?0",id);
            poContractBaseHistoryDao.deleteAll(poContractBaseHistories);
        }
        poContractBaseDao.deleteById(id);
        return "success";
    }
}
