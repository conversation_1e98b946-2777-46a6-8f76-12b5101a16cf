<template>
  <div id="contentUpdate">
    <TY_NavTop title="内容换版申请" :isBackHome="isBackHome" isSearch="true" @toggleList="screen"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="datePane">今天是{{date_today}}  {{weekDay}}</div>
      <div class="ui-cells">
        <a class="ui-cell" v-on:click="applyTo(item.id)" v-for="(item, index) in listApprove" :key="index">
          <div class="fileLists">
            <div class="fileType file_txt"></div>
            <div class="fileInfo">
              <div class="fileName" v-text="item.name" :title="item.name"></div>
              <div class="fileDetail">{{item.updateName || item.createName}} {{(item.updateTime || item.createTime) |formatDay('YYYY-MM-DD HH:mm:ss') }}</div>
            </div>
            <div class="fileFormat">
              <span class="fileVersion">G{{item.changeNum}}</span>
              <span class="fileNo">编号：{{item.fileSn}}</span>
            </div>
          </div>
        </a>
      </div>
    </div>
  </div>
</template>

<script>
  import * as moment from 'moment'
  import 'moment/locale/zh-cn'
  export default {
    name: 'contentUpdate',
    data () {
      return {
        loading: true,
        isBackHome: true,
        weekDay: '',
        date_today: moment(new Date()).format('YYYY/MM/DD'),
        listApprove: [],
        listenersUid: []
      }
    },
    created: function () {
      let today = new Date()
      let week = today.getDay()
      let weekArr = ['日', '一', '二', '三', '四', '五', '六']
      this.weekDay = '周' + weekArr[week]
      let that = this
      this.axios.post('../../../about/aboutChangeVersionFile.do')
        .then(function (response) {
          that.loading = false
          console.log(response)
          that.listApprove = response.data.data.listApply
        })
        .catch(function (error) {
          console.log(error)
        })
      this.listenersUid = [
        that.sphdSocket.subscribe('contentVersionApply', function (data) { // 终审时更新数据
          console.log('内容换版申请订阅:' + data)
          let reqData = JSON.parse(data)
          let update = reqData.operate
          let newDate = {}
          if (update > 0) { // 增加一条
            console.log('增加一条')
            newDate = reqData.aboutFileHis
            that.listApprove.push(newDate)
          } else if (update < 0) { // 减少一条
            console.log('减少一条')
            let deletId = reqData.aboutFileHis.id
            that.listApprove.forEach(function (item, index) {
              if (deletId === item.id) {
                that.listApprove.splice(index, 1)
              }
            })
          }
        }, null, 'user')
      ]
    },
    destroyed: function () {
      let _this = this
      this.listenersUid.forEach(function (item) {
        _this.sphdSocket.unsubscribe(item)
      })
    },
    methods: {
      applyTo: function (id) {
        this.$router.push({
          path: `/contentDetail/${id}/update`
        })
      },
      screen: function () {
        this.$router.push({
          path: '/contentQuery/update'
        })
      }
    }
  }
</script>
