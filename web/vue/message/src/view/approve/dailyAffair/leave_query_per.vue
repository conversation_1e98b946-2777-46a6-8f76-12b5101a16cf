<template>
  <div id="overTimeApply">
    <TY_NavTop title="查询"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div style="padding: 0 4px;">
        <div class="tip tip-success">从 {{getParams.beginDate}} 至 {{getParams.endDate}}</div>
      </div>
      <div class="ui-message">
        <div class="sendContainer leaveQuery">
          <div v-if="approveStatus === 2">您经手且最终被批准的<span v-if="getParams.applyUserId !== ''">{{getParams.applyUserName}}的</span>请假共{{count}}次</div>
          <div v-if="approveStatus === 3">您经手的被驳回的<span v-if="getParams.applyUserId !== ''">{{getParams.applyUserName}}的</span>请假申请共{{count}}次</div>
        </div>
        <div class="sendContainer leaveQuery">
          <el-row>
            <el-col :span="12" class="tabItem" v-for="item in leaveDetail" v-bind:key="item.id" @click.native="tabType(item)">
              <el-col :span="12"><div class="queryItem">{{item.name}}</div></el-col>
              <el-col :span="12"><div class="queryItem">{{item.count}}</div></el-col>
            </el-col>
          </el-row>
        </div>
      </div>
      <div v-if="duration < 31">
        <div class="tab-title">
          <div>{{name}}清单</div>
        </div>
        <div class="ui-cells" style="height: 240px; padding-top: 0;">
          <div v-for="items1 in leaveDetail" v-bind:key="items1.id" v-show="items1.id === id || id === 0">
            <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in items1.personnelLeaves" v-bind:key="index">
              <div class="ui-cell__hd">
                <span class="leave_type">{{item.createName}}</span>
              </div>
              <div class="ui-cell__bd">
                <div class="ui-cell_con_s">
                  <div>{{item.leaveTypeName}}</div>
                  <div>开始时间：{{item.beginTime | formatDay('YYYY-MM-DD HH:mm')}}</div>
                  <div>结束时间：{{item.endTime | formatDay('YYYY-MM-DD HH:mm')}}</div>
                </div>
              </div>
              <div class="ui-ti" v-show="item.actualState === '1'">提</div>
              <div class="ui-ti" v-show="item.kind === 2">补</div>
              <div class="ui-cell__ft"></div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'applyOverTimeQueryPer',
  data () {
    return {
      id: 0,
      name: '请假',
      approveStatus: 0,
      activeKey: '1',
      leaveDetail: [],
      loading: false,
      getParams: {}
    }
  },
  computed: {
    count () {
      let count = 0
      if (this.leaveDetail) {
        this.leaveDetail.forEach(function (item) {
          count += item.count
        })
      }
      return count
    },
    duration: function () {
      let beginDate = new Date(this.getParams.beginDate) // 开始时间
      let endDate = new Date(this.getParams.endDate) // 结束时间
      let du = endDate - beginDate // 相差的毫秒数
      console.log(Math.floor(du / (24 * 3600 * 1000)))
      return Math.floor(du / (24 * 3600 * 1000)) // 计算出天数
    }
  },
  created: function () {
    this.loading = false
    let paramQuery = this.$route.params.data
    if (!paramQuery) {
      paramQuery = JSON.parse(localStorage.getItem('query'))
    }
    this.getParams = paramQuery
    this.approveStatus = paramQuery.approveStatus
    let _this = this
    this.$http.post('../../../leaveAndOutTime/approvalLeaveQuery.do', paramQuery, {
      emulateJSON: true
    }).then((response) => {
      this.loading = false
      let leaveDetail = response.body
      if (leaveDetail) {
        _this.leaveDetail = leaveDetail.data
      } else {
        console.log('查询失败！')
      }
    }).catch(function () {
      // this.jumpError('系统错误，请重试！')
    })
  },
  methods: {
    tabType: function (item) {
      this.id = item.id
      this.name = item.name
      console.log(item.name)
    },
    jump: function (id) {
      this.$router.push({
        path: `/approvalLeaveDetail/${id}`
      })
    }
  }
}
</script>
