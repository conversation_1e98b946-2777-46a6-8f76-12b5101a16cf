<template>
  <div id="childFolder">
    <TY_NavTop :title="title"></TY_NavTop>
    <div class="container" v-loading="loading">
      <div class="list">
        <div class="ty-fileItem active" v-for="(item ,index) in childFold" :key="index" @click="childFolder(item)">
          <div class="ty-fileRow">
            <div class="icon_folder"></div>
            <div class="ty-fileName" :title="item.name">{{item.name}}</div>
            <div class="ui-cell__ft" v-if="item.children > 0"></div>
          </div>
          <div class="ty-fileRow ty-fileHandle">
            <div class="ty-fileDetail">
              {{item.updateName || item.createName }}
              {{item.createDate || item.updateDate | formatDay}}
            </div>
          </div>
        </div>
        <div class="ty-fileItem" v-for="(item ,index) in dataList" :key="index">
          <div class="ty-fileRow">
            <div :class="'fileType file_'+item.version"></div>
            <div class="ty-fileInfo">
              <div class="ty-fileRow">
                <div class="ty-fileName" :title="item.name">{{item.name}}</div>
              </div>
              <div class="ty-fileRow">
                <div class="ty-fileNoVersion" :title="item.fileSn">G{{item.changeNum}} {{item.fileSn}}</div>
              </div>
            </div>
          </div>
          <div class="ty-fileRow ty-fileHandle">
            <div class="ty-fileDetail">
              {{item.updateName || item.createName }}
              {{item.createDate || item.updateDate | formatDay}}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.topTip{
  padding: 8px 15px;
}
.btn-group{
  padding: 4px 0;
  .link-blue{
    margin-left: 16px;
  }
}
.container {
  height: calc(100% - 48px);
}
.list{
  height: calc(100% - 78px);
}
.ty-fileItem{
  background-color: #fff;
  cursor: default;
  position: relative;
  font-size: 13px;
  padding: 8px 12px;
  .ty-fileInfo{ flex: auto }
  .ty-fileName{ width:200px; word-wrap:break-word;  overflow:hidden; text-overflow:ellipsis; white-space: nowrap; flex: none }
  .ty-fileNoVersion{  width:100px; white-space:nowrap; overflow:hidden; text-overflow:ellipsis; font-size:12px; color:#999; flex: auto}
  .ty-fileDetail{  color: #aaa;  font-size: 12px;  font-weight: 300; margin-right: 16px; text-align: right}
  .ty-fileHandle{
    margin-top: 4px;
    .link-blue{
      flex: none;
    }
    .ty-fileDetail{
      flex: auto;
    }
  }
  &.active{
    cursor: pointer;
    &:hover{
      background: #f3f3f3;
    }
  }
}
.ty-fileRow { display: flex }
.item_title{
  width: 120px;
  flex: none;
  color: #999;
}
.item_content{
  flex: auto;
}
</style>

<script>

export default {
  name: 'childFolder',
  data () {
    return {
      dataList: [],
      childFold: [],
      loading: true,
      title: ''
    }
  },
  created () {
    this.getData()
    this.title = this.$route.query.title
  },
  methods: {
    childFolder: function (item) {
      console.log("跳转了", item.children)
      this.$router.push({
        path: `/childFolder/${item.id}`,
        query: {
          title: this.title
        }
      })
    },
    getData: function () {
      let categoryId = this.$route.params.id
      let that = this
      this.axios.post(this.auth.webRoot + '/res/getFolderAndChildFolderManager.do', {
        categoryId: categoryId,
        type: 1
      })
        .then(function (response) {
          that.loading = false
          console.log(response)
          let data = response.data.data
          let childFold= data.childFolder
          let list = data.list
          that.childFold = childFold
          that.dataList = list
        })
        .catch(function (error) {
          console.log(error)
        })
    }
  },
  watch: {
    '$route'(val, from) {
      this.getData()
    }
  }
}
</script>
