<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sphd.miners.modules.equipment.mapper.TEquModelMapper">
    <resultMap type="TEquModel" id="TEquModelResult">
        <result property="id" column="id"/>
        <result property="org" column="org"/>
        <result property="equipment" column="equipment"/>
        <result property="modelName" column="model_name"/>
        <result property="modelCode" column="model_code"/>
        <result property="unitId" column="unit_id"/>
        <!--        <result property="category"    column="category"    />-->
        <result property="supplier" column="supplier"/>
        <result property="lifeSpan" column="life_span"/>
        <result property="originalValue" column="original_value"/>
        <result property="receiveDate" column="receive_date"/>
        <result property="conditions" column="conditions"/>
        <result property="enabled" column="enabled"/>
        <result property="enabledTime" column="enabled_time"/>
        <result property="productCount" column="product_count"/>
        <result property="memo" column="memo"/>
        <result property="creator" column="creator"/>
        <result property="createName" column="create_name"/>
        <result property="createDate" column="create_date"/>
        <result property="updator" column="updator"/>
        <result property="updateName" column="update_name"/>
        <result property="updateDate" column="update_date"/>
        <result property="operation" column="operation"/>
        <result property="previousId" column="previous_id"/>
        <result property="versionNo" column="version_no"/>
        <result property="quantity" column="quantity"/>
        <result property="pointCount" column="point_count"/>
        <result property="pointState" column="point_state"/>
    </resultMap>

    <sql id="selectTEquModelVo">
        select id,
               org,
               equipment,
               model_name,
               model_code,
               unit_id,
               supplier,
               life_span,
               original_value,
               point_count,
               receive_date,
               conditions,
               enabled,
               enabled_time,
               product_count,
               point_state,
               memo,
               creator,
               create_name,
               create_date,
               updator,
               update_name,
               update_date,
               operation,
               previous_id,
               version_no,
               quantity
        from t_equ_model
    </sql>

    <select id="selectTEquModelList" parameterType="TEquModel" resultMap="TEquModelResult">
        <include refid="selectTEquModelVo"/>
        <where>
            <if test="org != null">
                and org = #{org}
            </if>
            <if test="equipment != null">
                and equipment = #{equipment}
            </if>
            <if test="modelName != null  and modelName != ''">
                and model_name like concat('%', #{modelName}, '%')
            </if>
            <if test="modelCode != null  and modelCode != ''">
                and model_code = #{modelCode}
            </if>
            <if test="unitId != null">
                and unit_id = #{unitId}
            </if>
            <!--            <if test="category != null "> and category = #{category}</if>-->
            <if test="supplier != null">
                and supplier = #{supplier}
            </if>
            <if test="lifeSpan != null">
                and life_span = #{lifeSpan}
            </if>
            <if test="originalValue != null">
                and original_value = #{originalValue}
            </if>
            <if test="receiveDate != null">
                and receive_date = #{receiveDate}
            </if>
            <if test="conditions != null">
                and conditions = #{conditions}
            </if>
            <if test="enabled != null">
                and enabled = #{enabled}
            </if>
            <if test="enabledTime != null">
                and enabled_time = #{enabledTime}
            </if>
            <if test="productCount != null">
                and product_count = #{productCount}
            </if>
            <if test="memo != null  and memo != ''">
                and memo = #{memo}
            </if>
            <if test="creator != null">
                and creator = #{creator}
            </if>
            <if test="createName != null  and createName != ''">
                and create_name like concat('%', #{createName}, '%')
            </if>
            <if test="createDate != null">
                and create_date = #{createDate}
            </if>
            <if test="updator != null">
                and updator = #{updator}
            </if>
            <if test="updateName != null  and updateName != ''">
                and update_name like concat('%', #{updateName}, '%')
            </if>
            <if test="updateDate != null">
                and update_date = #{updateDate}
            </if>
            <if test="operation != null">
                and operation = #{operation}
            </if>
            <if test="previousId != null">
                and previous_id = #{previousId}
            </if>
            <if test="versionNo != null">
                and version_no = #{versionNo}
            </if>
            <if test="quantity != null">
                and quantity = #{quantity}
            </if>
            <if test="category != null">
                and category = #{category}
            </if>

        </where>
    </select>
    <select id="selectEquModelDetailList" parameterType="TEquModel" resultType="java.util.HashMap">
        select GROUP_CONCAT(efa.id)                             as emCount,
               efa.id,
               em.id                                            as mid,
               em.point_count                                   as point_count,
               em.point_state                                   as point_state,
               LPAD(efa.id, 8, 0)                               as lpadId,
               em.supplier,
               es.id                                            as supplierId,
               ee.id                                            as equipmentId,
               ifnull(ee.full_name, '--')                       as equipmentName,
               ifnull(em.product_count, 0)                      as productCount,
               em.model_name                                    as modelName,
               efa.code                                         as modelCode,
               es.full_name                                     as supplierName,
               em.create_name                                   as
                                                                   createName,
               DATE_FORMAT(em.create_date, '%Y-%m-%d %H:%i:%s') AS createDate,
               ifnull(GROUP_CONCAT(ec.path), '--')              as path,
               mu.name                                          as units,
               em.quantity,
               mu.name                                          as unitName,
               mu.id                                            as unit_id,
               ec.id                                            as category,
               ec.name                                          as categoryName
        from t_equ_model em
                 left join t_srm_supplier es on em.supplier = es.id
                 left join t_equ_fixed_assets efa on efa.model = em.id
                 left join t_equ_category ec on efa.category =
                                                ec.id
                 left join t_equ_equipment ee on em.equipment = ee.id
                 left join t_mt_unit mu on em.unit_id = mu.id
        <where>
            <if test="org != null">
                and ee.org = #{org}
            </if>
            <if test="equipment != null">
                and em.equipment = #{equipment}
            </if>
            <if test="modelName != null">
                and model_name = #{modelName}
            </if>
            <if test="modelCode != null  and modelCode != ''">
                and model_code = #{modelCode}
            </if>

            <if test="supplier != null">
                and supplier = #{supplier}
            </if>
            <if test="ids != null">
                and em.id in (${ids})
            </if>
            <if test="init != null">
                AND efa.category IN (SELECT id
                                     FROM t_equ_category ec
                                     WHERE ec.`id` IN (SELECT id
                                                       FROM t_equ_category ec
                                                       WHERE ec.org = #{org}
                                                         AND SUBSTRING_INDEX(ec.path, '/', 1) IN ('一类装备', '五类装备'))
                                       AND ec.org = #{org})
            </if>
            <if test="category != null and category == -1">
                and ifnull(efa.category, -1) = -1 group by em.id
            </if>


            <if test="category != null and category == -2">
                and ec.path
                        = '不属于装备器具(所以无法分类)的固定资产或低值易耗品' group by efa.id
            </if>
            <if test="category != null and category == 0">
                and efa.category is not null and ec.path not IN
                ('不属于装备器具(所以无法分类)的固定资产或低值易耗品') group by em.id
            </if>
            <if test="category != null and category != -1 and category != 0 and category != -2">
                and efa.category = #{category} group by efa.id
            </if>
            <if test="category > 0 and init != null">
                and ec.id = #{category}
                group by em.id
            </if>
            <if test="category == null">
                group by efa.id
            </if>
        </where>

        ORDER BY em.id DESC
    </select>

    <select id="selectEquModelDetailListNoGroup" parameterType="TEquModel" resultType="java.util.HashMap">
        select em.id                                            as emId,
               efa.id,
               LPAD(efa.id, 8, 0)                               as lpadId,
               em.supplier,
               ifnull(em.point_count, 0)                        as point_count,
               ifnull(em.point_state, 0)                        as point_state,
               es.id                                            as supplierId,
               es.`name`                                        as supplierName,
               ee.id                                            as equipmentId,
               ifnull(ee.full_name, '--')                       as equipmentName,
               ifnull(em.product_count, 0)                      as productCount,
               em.model_name                                    as modelName,
               efa.code                                         as modelCode,
               es.name                                          as supplierName,
               em.create_name                                   as
                                                                   createName,
               DATE_FORMAT(em.create_date, '%Y-%m-%d %H:%i:%s') AS createDate,
               ifnull(ec.path, '--')                            as path,
               ee.units,
               ee.units                                         as
                                                                   unitName,
               ec.descendants,
               ec.id                                            as categoryId,
               efa.receive_date,
               efa.conditions,
               efa.life_span,
               efa.original_value,
               efa.memo,
               em.unit_id,
               efa.category,
               em.quantity
        from t_equ_model em
                 left join t_equ_fixed_assets efa on efa.model = em.id
                 left join t_srm_supplier es on em.supplier = es.id
                 left join t_equ_category ec on efa.category =
                                                ec.id
                 left join t_equ_equipment ee on em.equipment = ee.id

        <where>
            <if test="org != null">
                and ee.org = #{org}
            </if>
            <if test="equipment != null">
                and em.equipment = #{equipment}
            </if>
            <if test="modelName != null">
                and em.model_name = #{modelName}
            </if>
            <if test="unitId != null">
                and em.unit_id = #{unitId}
            </if>
            <if test="modelCode != null  and modelCode != ''">
                and efa.code = #{modelCode}
            </if>

            <if test="supplier != null">
                and em.supplier = #{supplier}
            </if>
            <if test="ids != null and ids != ''">
                and efa.id in (${ids})
            </if>

            <if test="category != null and category == -1">
                and ifnull(efa.category, -1) = -1
            </if>
            <if test="category != null and category == 0">
                and efa.category is not null
            </if>
            <if test="category != null and category != -1 and category != 0">
                and efa.category = #{category}
            </if>
        </where>
        group by efa.id
        ORDER BY em.id DESC
    </select>


    <select id="selectTEquModelById" parameterType="Long" resultMap="TEquModelResult">
        <include refid="selectTEquModelVo"/>
        where id = #{id}
    </select>
    <select id="selectTEquModelGroupList" parameterType="TEquModel" resultType="java.util.HashMap">
        select em.model_name as modelName, count(em.id) as count, count(ee.supplier_count) as supplierCount
        from t_equ_model em
                 left join t_equ_equipment ee on em.equipment = ee.id
        where em.equipment = #{equipment}
        group by em.model_name
    </select>
    <select id="getModelDetail" parameterType="long" resultType="java.util.Map">
        SELECT em.id,
               ifnull(em.point_count, 0)                        as point_count,
               ifnull(em.point_state, 0)                        as point_state,
               es.id                                            AS supplierId,
               mu.id                                            AS unitId,
               mu.`name`                                        AS unitName,
               ec.`name`                                        AS categoryName,
               ec.id                                            AS categoryId,
               efa.life_span                                    AS lifeSpan,
               efa.original_value                               AS originalValue,
               efa.receive_date                                 AS receiveDate,
               efa.conditions,
               em.memo,
               ee.full_name                                     AS equipmentName,
               ee.id                                            AS equipmentId,
               em.model_name                                    AS modelName,
               em.model_code                                    AS modelCode,
               es.NAME                                          AS supplierName,
               em.create_name                                   AS createName,
               DATE_FORMAT(em.create_date, '%Y-%m-%d %H:%i:%s') AS createDate,
               ifnull(ec.path, '--')                            AS path,
               concat(ifnull(descendants, ''), ec.id)           AS catIDs,
               em.quantity
        FROM t_equ_model em
                 LEFT JOIN t_srm_supplier es ON em.supplier = es.id
                 LEFT JOIN t_equ_fixed_assets efa ON efa.model = em.id
                 LEFT JOIN t_equ_category ec ON efa.category = ec.id
                 LEFT JOIN t_equ_equipment ee ON em.equipment = ee.id
                 LEFT JOIN t_mt_unit mu ON em.unit_id = mu.id
        WHERE efa.id = #{id}
    </select>

    <insert id="insertTEquModel" parameterType="TEquModel" useGeneratedKeys="true" keyProperty="id">
        insert into t_equ_model
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="org != null">
                org,
            </if>
            <if test="equipment != null">
                equipment,
            </if>
            <if test="pointCount != null">
                point_count,
            </if>
            <if test="pointState != null">
                point_state,
            </if>
            <if test="modelName != null">
                model_name,
            </if>
            <if test="modelCode != null">
                model_code,
            </if>
            <if test="unitId != null">
                unit_id,
            </if>
            <if test="category != null">
                category,
            </if>
            <if test="supplier != null">
                supplier,
            </if>
            <if test="lifeSpan != null">
                life_span,
            </if>
            <if test="originalValue != null">
                original_value,
            </if>
            <if test="receiveDate != null">
                receive_date,
            </if>
            <if test="conditions != null">
                conditions,
            </if>
            <if test="enabled != null">
                enabled,
            </if>
            <if test="enabledTime != null">
                enabled_time,
            </if>
            <if test="productCount != null">
                product_count,
            </if>
            <if test="memo != null">
                memo,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="createName != null">
                create_name,
            </if>
            <if test="createDate != null">
                create_date,
            </if>
            <if test="updator != null">
                updator,
            </if>
            <if test="updateName != null">
                update_name,
            </if>
            <if test="updateDate != null">
                update_date,
            </if>
            <if test="operation != null">
                operation,
            </if>
            <if test="previousId != null">
                previous_id,
            </if>
            <if test="versionNo != null">
                version_no,
            </if>
            <if test="quantity != null">
                quantity,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="org != null">
                #{org},
            </if>
            <if test="equipment != null">
                #{equipment},
            </if>
            <if test="pointCount != null">
                #{pointCount},
            </if>
            <if test="pointState != null">
                #{pointState},
            </if>
            <if test="modelName != null">
                #{modelName},
            </if>
            <if test="modelCode != null">
                #{modelCode},
            </if>
            <if test="unitId != null">
                #{unitId},
            </if>
            <if test="category != null">
                #{category},
            </if>
            <if test="supplier != null">
                #{supplier},
            </if>
            <if test="lifeSpan != null">
                #{lifeSpan},
            </if>
            <if test="originalValue != null">
                #{originalValue},
            </if>
            <if test="receiveDate != null">
                #{receiveDate},
            </if>
            <if test="conditions != null">
                #{conditions},
            </if>
            <if test="enabled != null">
                #{enabled},
            </if>
            <if test="enabledTime != null">
                #{enabledTime},
            </if>
            <if test="productCount != null">
                #{productCount},
            </if>
            <if test="memo != null">
                #{memo},
            </if>
            <if test="creator != null">
                #{creator},
            </if>
            <if test="createName != null">
                #{createName},
            </if>
            <if test="createDate != null">
                #{createDate},
            </if>
            <if test="updator != null">
                #{updator},
            </if>
            <if test="updateName != null">
                #{updateName},
            </if>
            <if test="updateDate != null">
                #{updateDate},
            </if>
            <if test="operation != null">
                #{operation},
            </if>
            <if test="previousId != null">
                #{previousId},
            </if>
            <if test="versionNo != null">
                #{versionNo},
            </if>
            <if test="quantity != null">
                #{quantity},
            </if>
        </trim>
    </insert>

    <update id="updateTEquModel" parameterType="TEquModel">
        update t_equ_model
        <trim prefix="SET" suffixOverrides=",">
            <if test="org != null">
                org = #{org},
            </if>
            <if test="equipment != null">
                equipment = #{equipment},
            </if>
            <if test="pointCount != null">
                point_count = #{pointCount},
            </if>
            <if test="pointState != null">
                point_state = #{pointState},
            </if>
            <if test="modelName != null">
                model_name = #{modelName},
            </if>
            <if test="modelCode != null">
                model_code = #{modelCode},
            </if>
            <if test="unitId != null">
                unit_id = #{unitId},
            </if>
            <if test="category != null">
                category = #{category},
            </if>
            <if test="supplier != null">
                supplier = #{supplier},
            </if>
            <if test="lifeSpan != null">
                life_span = #{lifeSpan},
            </if>
            <if test="originalValue != null">
                original_value = #{originalValue},
            </if>
            <if test="receiveDate != null">
                receive_date = #{receiveDate},
            </if>
            <if test="conditions != null">
                conditions = #{conditions},
            </if>
            <if test="enabled != null">
                enabled = #{enabled},
            </if>
            <if test="enabledTime != null">
                enabled_time = #{enabledTime},
            </if>
            <if test="productCount != null">
                product_count = #{productCount},
            </if>
            <if test="memo != null">
                memo = #{memo},
            </if>
            <if test="creator != null">
                creator = #{creator},
            </if>
            <if test="createName != null">
                create_name = #{createName},
            </if>
            <if test="createDate != null">
                create_date = #{createDate},
            </if>
            <if test="updator != null">
                updator = #{updator},
            </if>
            <if test="updateName != null">
                update_name = #{updateName},
            </if>
            <if test="updateDate != null">
                update_date = #{updateDate},
            </if>
            <if test="operation != null">
                operation = #{operation},
            </if>
            <if test="previousId != null">
                previous_id = #{previousId},
            </if>
            <if test="versionNo != null">
                version_no = #{versionNo},
            </if>
            <if test="quantity != null">
                quantity = #{quantity},
            </if>
            <if test="setNull != null">
                point_count = null
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTEquModelById" parameterType="Long">
        delete
        from t_equ_model
        where id = #{id}
    </delete>

    <delete id="deleteTEquModelByIds" parameterType="String">
        delete
        from t_equ_model where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectTEquModelListByCategory" parameterType="TEquModel" resultMap="TEquModelResult">
        <include refid="selectTEquModelVo"/>
        <where>
            <if test="org != null">
                and org = #{org}
            </if>
            <if test="equipment != null">
                and equipment = #{equipment}
            </if>
            <if test="modelName != null  and modelName != ''">
                and model_name like concat('%', #{modelName}, '%')
            </if>
            <if test="modelCode != null  and modelCode != ''">
                and model_code = #{modelCode}
            </if>
            <if test="unitId != null">
                and unit_id = #{unitId}
            </if>
            <if test="category != null">
                and category = #{category}
            </if>
            <if test="supplier != null">
                and supplier = #{supplier}
            </if>
            <if test="lifeSpan != null">
                and life_span = #{lifeSpan}
            </if>
            <if test="originalValue != null">
                and original_value = #{originalValue}
            </if>
            <if test="receiveDate != null">
                and receive_date = #{receiveDate}
            </if>
            <if test="conditions != null">
                and conditions = #{conditions}
            </if>
            <if test="enabled != null">
                and enabled = #{enabled}
            </if>
            <if test="enabledTime != null">
                and enabled_time = #{enabledTime}
            </if>
            <if test="productCount != null">
                and product_count = #{productCount}
            </if>
            <if test="memo != null  and memo != ''">
                and memo = #{memo}
            </if>
            <if test="creator != null">
                and creator = #{creator}
            </if>
            <if test="createName != null  and createName != ''">
                and create_name like concat('%', #{createName}, '%')
            </if>
            <if test="createDate != null">
                and create_date = #{createDate}
            </if>
            <if test="updator != null">
                and updator = #{updator}
            </if>
            <if test="updateName != null  and updateName != ''">
                and update_name like concat('%', #{updateName}, '%')
            </if>
            <if test="updateDate != null">
                and update_date = #{updateDate}
            </if>
            <if test="operation != null">
                and operation = #{operation}
            </if>
            <if test="previousId != null">
                and previous_id = #{previousId}
            </if>
            <if test="versionNo != null">
                and version_no = #{versionNo}
            </if>
            <if test="quantity != null">
                and quantity = #{quantity}
            </if>
        </where>
    </select>
</mapper>