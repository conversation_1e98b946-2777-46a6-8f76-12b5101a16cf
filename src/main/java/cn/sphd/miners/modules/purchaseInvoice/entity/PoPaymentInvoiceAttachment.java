package cn.sphd.miners.modules.purchaseInvoice.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购_票款处理发票附件表
 * 2021/6/16  lyx
 * 1.169采购的票款处理
 */
@Entity
@Table(name = "t_po_payment_invoice_attachment")
public class PoPaymentInvoiceAttachment implements Serializable {
    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="org"  , nullable=true, unique=false)
    private Integer org;   //机构ID

    @Column(name="invoice"  , nullable=true , unique=false)
    private Integer invoice;   //发票ID

    @Column(name="title"  , length=100 , nullable=true , unique=false)
    private String title;   //标题

    @Column(name="description"  , length=255 , nullable=true , unique=false)
    private String description ;   //描述

    @Column(name="path"  , length=255 , nullable=true , unique=false)
    private String path;  //存放路径

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo ;   //备注

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;   //操作:1-增,2-删,3-改

    @Column(name = "previous_id")
    private Integer previousId;   //修改前记录ID

    @Column(name = "version_no")
    private Integer versionNo;   //版本号,每次修改+1


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getInvoice() {
        return invoice;
    }

    public void setInvoice(Integer invoice) {
        this.invoice = invoice;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
}
