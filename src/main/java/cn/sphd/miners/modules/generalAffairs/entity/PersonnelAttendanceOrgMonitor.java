package cn.sphd.miners.modules.generalAffairs.entity;

import cn.sphd.miners.common.utils.NewDateUtils;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by Wyu on 2018/10/26.
 * 机构考勤执行表
 */
@Entity
@Table(name = "t_personnel_attendance_org_monitor")
public class PersonnelAttendanceOrgMonitor implements Serializable {
    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="org")
    private Integer org;//机构id

    @Column(name="scan_date")
    private Date scanDate;//最后执行默认考勤的时间（工作日或者假）//20250429 1.342考勤打卡 新定义：上次考勤扫描时间

    @Column(name="scan_times")
    private Byte scanTimes;//是否已扫描过，0未扫，1已扫//20250424 1.342考勤打卡 新定义：0 未建考勤日表 1 新建考勤日表，未考勤扫描 2 扫描中，未完成 3 考勤未启用

    @Column(name="start_date")
    private Date startDate;//开始扫描的工作日(下一个工作日)

    @Column(name="exception_date", nullable = false)
    private Date exceptionDate = NewDateUtils.getMinDate();//最新例外设置（非工作日）

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Date getScanDate() {
        return scanDate;
    }

    public void setScanDate(Date scanDate) {
        this.scanDate = scanDate;
    }

    public Byte getScanTimes() {
        return scanTimes;
    }

    public void setScanTimes(Byte scanTimes) {
        this.scanTimes = scanTimes;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getExceptionDate() {
        return exceptionDate;
    }

    public void setExceptionDate(Date exceptionDate) {
        this.exceptionDate = exceptionDate;
    }

    public PersonnelAttendanceOrgMonitor() {
    }

    public PersonnelAttendanceOrgMonitor(Integer org, Date scanDate, Byte scanTimes, Date startDate, Date exceptionDate) {
        this.org = org;
        this.scanDate = scanDate;
        this.scanTimes = scanTimes;
        this.startDate = startDate;
        this.exceptionDate = exceptionDate;
    }
}
