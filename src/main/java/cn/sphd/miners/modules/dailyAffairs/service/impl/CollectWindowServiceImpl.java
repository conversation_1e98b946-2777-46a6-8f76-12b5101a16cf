package cn.sphd.miners.modules.dailyAffairs.service.impl;

import cn.sphd.miners.modules.dailyAffairs.service.CollectWindowService;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.finance.dao.*;
import cn.sphd.miners.modules.finance.entity.*;
import cn.sphd.miners.modules.finance.service.AccountService;
import cn.sphd.miners.modules.sales.dao.SlCollectApplicationDao;
import cn.sphd.miners.modules.sales.entity.SlCollectApplication;
import cn.sphd.miners.modules.system.dao.OrgDao;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.entity.UserPopedom;
import cn.sphd.miners.modules.system.service.UserPopedomService;
import cn.sphd.miners.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by Administrator on 2019/5/16.
 */
@Service("collectWindowService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class CollectWindowServiceImpl implements CollectWindowService {

    @Autowired
    SlCollectApplicationDao slCollectApplicationDao;
    @Autowired
    AccountService accountService;
    @Autowired
    UserDao userDao;
    @Autowired
    FinanceAccountBillDao financeAccountBillDao;
    @Autowired
    AccountDetailDao accountDetailDao;
    @Autowired
    AccountPeroidDao accountPeroidDao;
    @Autowired
    FinanceAccountDao financeAccountDao;
    @Autowired
    OrgDao orgDao;
    @Autowired
    FinanceReturnDao financeReturnDao;

    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    SWMessageService swMessageService;
    @Autowired
    UserPopedomService userPopedomService;
    @Autowired
    UserService userService;

    /**
     * 回款入账列表查询
     * @param oid  机构id
     * @return
     */
    @Override
    public List<Map<String,Object>> collectPaymentList(Integer oid) {
        Map<String,Object> params = new HashMap<>();
        String hql = "select a.id,c.fullName,a.amount,a.method,a.createName,a.createDate from SlCollectApplication a,SlCustomer c where a.org=:org and a.recordType=0 and a.state=1 and a.customer=c.id";
        params.put("org",oid);
        List<Object[]> listObjects = slCollectApplicationDao.getListByHQLWithNamedParams(hql,params);
        List<Map<String,Object>> listMap = new ArrayList<>();
        for (Object[] listObject:listObjects) {
            Map<String,Object> map = new HashMap<>();
            map.put("collectId",listObject[0]);
            map.put("name",listObject[1]);
            map.put("amount",listObject[2]);
            map.put("method",listObject[3]);
            map.put("createName",listObject[4]);
            map.put("createDate",listObject[5]);
            listMap.add(map);
        }
        return listMap;
    }

    /**
     * 查看回款申请详情
     * @param collectId  回款申请id
     * @return
     */
    @Override
    public SlCollectApplication getSlCollectDetail(Integer collectId) {
        return slCollectApplicationDao.get(collectId);
    }

    /**
     *
     * @param collectId  回款申请id
     * @param financeAccountId  账户id(银行id)
     * @param receiveDate  到账日期
     * @param type 1-确定 2-取消
     * @param userId 登录人id
     * @param customerName  客户id
     * @param map
     * @return
     */
    @Override
    public Map<String, Object> collectPayment(Integer collectId, Integer financeAccountId, Date receiveDate, Integer type,Integer userId,String customerName, Map<String, Object> map) {
        if (collectId!=null && type!=null){
            SlCollectApplication slCollectApplication = slCollectApplicationDao.get(collectId);

            if (type==1){  //确定
                String method = slCollectApplication.getMethod(); //1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
                User user = userDao.get(userId);
                BigDecimal money = slCollectApplication.getAmount();
                Integer oid = slCollectApplication.getOrg();
                Organization organization = orgDao.get(oid);

                if ("1".equals(method)){  //1-现金
                    //单纯现金
                    FinanceAccount financeAccount = accountService.getFinanceAccountByOidAndType(oid, 1);//备用金
                    if (0 == financeAccount.getAccountStatus()) {
                        map.put("status","2");//此账户已被冻结，不允许操作
                        return map;
                    }

                    AccountPeriod accountPeriodMonth = accountService.getAccountPeriodByMonth(financeAccount.getId(),new Date());//找到月结
                    AccountPeriod accountPeriodDay = accountService.getAccountPeriodByDay(financeAccount.getId(),new Date());//找到日结

                    AccountDetail accountDetail = new AccountDetail();
                    accountDetail.setCredit(money);
                    accountDetail.setCreateDate(new Date());
                    accountDetail.setCreator(user.getUserID());
                    accountDetail.setCreateName(user.getUserName());
                    accountDetail.setOrg(organization);
//                    accountDetail.setGenre("2");  //类别 1-贷款，2-借款，3-投资款，4-废品，5-其他
                    accountDetail.setAuditDate(new Date());
                    accountDetail.setMethod(method);// 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
                    accountDetail.setMemo(slCollectApplication.getMemo());
                    accountDetail.setBillAmount(money);  //票面金额
                    accountDetail.setFid(financeAccount.getId().toString());
                    accountDetail.setAccountId(financeAccount);//账户外键
                    accountDetail.setAuditorName(user.getUserName());
                    accountDetail.setType("3");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
                    if (financeAccount.getAccount()!=null){
                        accountDetail.setAccountBank(financeAccount.getBankName() + financeAccount.getAccount());
                    }else {
                        accountDetail.setAccountBank(financeAccount.getBankName());
                    }
                    accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
                    accountDetail.setModityStatus("2");  //数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次、包括借款的收入与支出) 3-关闭账户后不可修改
                    accountDetail.setSource("3");  //数据来源:1-手工录入(数据录入、以及报销等),2-业务自动生成(暂时只包括借款) 3-销售回款
                    accountDetail.setBusinessDate(new Date());  //业务发生日期
                    accountDetail.setBusiness(slCollectApplication.getId());  //业务号
//                    accountDetail.setBusinessType("0101");  //业务类型:前两位大业务,后两位子业务    0101-常规借款借入,0102-常规借款还款
                    accountDetail.setSummary("来自"+customerName+"的回款");  //摘要    需求要求添加上的(********)
                    accountDetail.setBillDate(new Date());

                    BigDecimal credit = accountPeriodMonth.getCredit().add(accountDetail.getCredit());
                    BigDecimal balance = accountPeriodMonth.getBalance().add(accountDetail.getCredit());
                    financeAccount.setCredit(financeAccount.getCredit().add(accountDetail.getCredit()));//计入账户收入
                    financeAccount.setBalance(financeAccount.getBalance().add(accountDetail.getCredit()));//从余额加上收入
                    accountDetail.setBalance(financeAccount.getBalance());
                    accountPeriodMonth.setCredit(credit);
                    accountPeriodMonth.setBalance(balance);

                    credit = accountPeriodDay.getCredit() == null ? new BigDecimal(0).add(accountDetail.getCredit()) : accountPeriodDay.getCredit().add(accountDetail.getCredit());
                    balance = accountPeriodDay.getBalance().add(accountDetail.getCredit());
                    accountPeriodDay.setCredit(credit);
                    accountPeriodDay.setBalance(balance);

                    accountPeroidDao.update(accountPeriodMonth);//月结
                    accountPeroidDao.update(accountPeriodDay);//日结
                    financeAccountDao.update(financeAccount);//账户
                    accountDetailDao.save(accountDetail);//明细

                    slCollectApplication.setFinanceAccountDetail(accountDetail.getId());
                    slCollectApplication.setReceiveBank(financeAccount.getId().toString());  //收款银行

                }else if ("3".equals(method) || "4".equals(method)){  //3-转帐支票,4-承兑汇票

                    FinanceReturn fr = new FinanceReturn();
                    fr.setReturnNo(slCollectApplication.getReturnNo());//支票号码
                    if ("3".equals(method)) {
                        fr.setType(1);//1-转账支票 2-承兑汇票
                    } else {
                        fr.setType(2);//1-转账支票 2-承兑汇票
                    }
//                    fr.setCategory("2");//1-贷款,2-借款,3-投资款,4-废品,5-其他
                    fr.setAmount(money);//金额
                    fr.setBillAmount(money);//票面金额
                    fr.setOperatorName(user.getUserName());//经手人
                    fr.setOriginalCorp(slCollectApplication.getOriginalCorp());//出具票据单位
                    fr.setBankName(slCollectApplication.getBankName());//出具票据银行
                    fr.setExpireDate(slCollectApplication.getExpireDate());//发票到期日期
                    fr.setReceiveDate(slCollectApplication.getReceiveDate());//收到票据日期
                    fr.setMemo(slCollectApplication.getMemo());//备注
                    fr.setState("1");//1-有效,2-存入银行,3-作废
                    fr.setCreateDate(new Date());
                    fr.setCreateName(user.getUserName());
                    fr.setCreator(user.getUserID());
                    fr.setOrg(organization);
                    fr.setPayer(customerName);  //付款单位
                    fr.setSummary("来自"+customerName+"的回款");  //摘要    需求要求添加上的(********)
                    financeReturnDao.save(fr);

                    //支票明细
                    FinanceAccountBill f = new FinanceAccountBill();
                    f.setType("1");//1-收入，2-支出
                    f.setAmount(money);//金额
                    f.setBillAmount(money);  //票面金额
                    f.setBillNo(slCollectApplication.getReturnNo());//支票号码
                    f.setFinanceReturn(fr);//回款票据外键
                    f.setOrg(organization);//机构
                    f.setOperatorName(user.getUserName());//经手人
                    f.setMemo(slCollectApplication.getMemo());//备注
                    f.setAccountantStatus("1");
                    f.setBusiness(slCollectApplication.getId());
                    f.setState("0");   //状态：0-未入账，1-重新入账，2-已入账
                    f.setSource("3");  //数据来源:1-手工录入(数据录入、以及报销等),2-业务自动生成(暂时只包括借款) 3-销售回款
//                    f.setBusinessType("0101"); //业务类型:前两位大业务,后两位子业务    0101-常规借款借入,0102-常规借款还款
                    f.setModityStatus("1");
                    f.setSummary("来自"+customerName+"的回款");   //摘要    需求要求添加上的(********)
                    f.setBillDate(new Date());
                    f.setCreateDate(new Date());
                    f.setCreateName(user.getUserName());
                    f.setCreator(userId);
                    financeAccountBillDao.save(f);

                    slCollectApplication.setFinanceAccountBill(f.getId());

                }else if ("5".equals(method)){  //5-银行转账

                    FinanceAccount financeAccount = financeAccountDao.get(financeAccountId);
                    if (0 == financeAccount.getAccountStatus()) {
                        map.put("status","2"); //此账户已被冻结，不允许操作
                        return map;
                    }

                    AccountPeriod accountPeriodMonth = accountService.getAccountPeriodByMonth(financeAccount.getId(),new Date());//找到月结
                    AccountPeriod accountPeriodDay = accountService.getAccountPeriodByDay(financeAccount.getId(),new Date());//找到日结

                    AccountDetail accountDetail = new AccountDetail();
                    accountDetail.setCredit(money);
                    accountDetail.setCreateDate(new Date());
                    accountDetail.setCreator(user.getUserID());
                    accountDetail.setCreateName(user.getUserName());
                    accountDetail.setOrg(organization);
                    accountDetail.setAuditorName(user.getUserName());
                    accountDetail.setAuditor(user.getUserID());
                    accountDetail.setAuditDate(new Date());
                    accountDetail.setAuditDate(new Date());
//                    accountDetail.setGenre("2");  //类别  2-借款
                    accountDetail.setMethod(method);
                    accountDetail.setMemo(slCollectApplication.getMemo());
//        accountDetail.setOppositeCorp(oppositeCorp);//收款单位
                    accountDetail.setFid(financeAccount.getId().toString());
                    accountDetail.setAccountId(financeAccount);//账户外键
                    accountDetail.setType("3");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
                    accountDetail.setReceiveAccountDate(receiveDate);//到账日期
                    accountDetail.setBillAmount(money);  //票面金额
                    accountDetail.setAccountBank(financeAccount.getBankName() + financeAccount.getAccount());
                    accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
                    accountDetail.setModityStatus("1");  //数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次) 3-关闭账户后不可修改
                    accountDetail.setSource("3");  //数据来源:1-手工录入(数据录入、以及报销等),2-业务自动生成(暂时只包括借款) 3-销售回款
                    accountDetail.setBusinessDate(new Date());  //业务发生日期
                    accountDetail.setBusiness(slCollectApplication.getId());  //业务号
//                    accountDetail.setBusinessType("0101"); //业务类型:前两位大业务,后两位子业务    0101-常规借款借入,0102-常规借款还款
                    accountDetail.setSummary("来自"+customerName+"的回款");  //摘要    需求要求添加上的(********)
                    accountDetail.setBillDate(new Date());

                    BigDecimal credit = accountPeriodMonth.getCredit().add(accountDetail.getCredit());
                    BigDecimal balance = accountPeriodMonth.getBalance().add(accountDetail.getCredit());
                    financeAccount.setCredit(financeAccount.getCredit().add(accountDetail.getCredit()));//计入账户收入
                    financeAccount.setBalance(financeAccount.getBalance().add(accountDetail.getCredit()));//从余额加上收入
                    accountDetail.setBalance(financeAccount.getBalance());
                    accountPeriodMonth.setCredit(credit);
                    accountPeriodMonth.setBalance(balance);

                    credit = accountPeriodDay.getCredit() == null ? new BigDecimal(0).add(accountDetail.getCredit()) : accountPeriodDay.getCredit().add(accountDetail.getCredit());
                    balance = accountPeriodDay.getBalance().add(accountDetail.getCredit());
                    accountPeriodDay.setCredit(credit);
                    accountPeriodDay.setBalance(balance);

                    accountPeroidDao.update(accountPeriodMonth);//月结
                    accountPeroidDao.update(accountPeriodDay);//日结
                    financeAccountDao.update(financeAccount);//账户
                    accountDetailDao.save(accountDetail);//明细

                    slCollectApplication.setFinanceAccountDetail(accountDetail.getId());
                    slCollectApplication.setReceiveBank(financeAccountId.toString()); //收款银行id
                    if (receiveDate!=null) {
                        slCollectApplication.setExpireDate(receiveDate); // 到期日期(银行转账的到账日期)
                    }
                    slCollectApplication.setBankName(financeAccount.getAccount()==null?financeAccount.getBankName():financeAccount.getBankName()+" "+financeAccount.getAccount());  //出具支票银行(银行转账的收款银行名称)
                }
                slCollectApplication.setPassEntry(true);
                slCollectApplication.setCustomerName(customerName);
                slCollectApplication.setFinanceTime(new Date());
                slCollectApplication.setFinancer(userId);
                slCollectApplication.setFinancerName(user.getUserName());
                slCollectApplication.setFinancerTime(new Date());
                slCollectApplication.setState("3");  //0-暂存,1-录入,2-提交,3-财务审批通过,4-财务审批否决,5-撤销,6-销售回款受理
                slCollectApplication.setApprovalDate(new Date());  //受理时间
                slCollectApplication.setUpdator(userId);
                slCollectApplication.setUpdateName(user.getUserName());
                slCollectApplication.setUpdateDate(new Date());
                slCollectApplicationDao.update(slCollectApplication);

                //给销售--回款处置发送
                this.collectRejectSend(1,1,slCollectApplication,slCollectApplication.getSaler(), "/collectDisposalList","returnHandle");

                if (!"1".equals(slCollectApplication.getRecordType())) {  //财务录入的回款不用发送消息

                    //查找有回款入账的人员(财务录入的不进回款入账)
                    List<UserPopedom> userPopedomList = userPopedomService.getUserPopedomByMid(user.getOid(), "gb");
                    for (UserPopedom u : userPopedomList) {
                        if ("finance".equals(u.getUser().getManagerCode())) {
                            //给财务--回款入账发送
                            this.collectRejectSend(-1, -1, slCollectApplication, u.getUserId(), "/collectPaymentList","returnInAccount");
                        }
                    }
                    User financeUser = userService.getUserByRoleCode(user.getOid(), "finance");  //财务高管
                    if (financeUser!=null){
                        //给财务--回款入账发送
                        this.collectRejectSend(-1,-1,slCollectApplication, financeUser.getUserID(), "/collectPaymentList","returnInAccount");
                    }

                    // 回款经财务确认后 给申请人发消息提醒
                    userSuspendMsgService.saveUserSuspendMsg(1, "经确认，财务已收到来自于" + customerName + "的" + money + "元回款!", "确认时间 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), slCollectApplication.getCreator(), "payBackMsg",collectId);  //给前端要查看详情的链接
                    //给申请人，回款录入发送
                    this.collectRejectSend(0,-1,slCollectApplication, slCollectApplication.getCreator(), "/getReceivableList","returnInput");
                }
            }
            map.put("state",1);  // 成功
        }else {
            map.put("state",0);  // 失败
        }
        return map;
    }

    //报销 撤销 长连接推送   pass 通道  superscript 角标
    @Override
    public void collectRejectSend(int loginNum,int operate, SlCollectApplication slCollectApplication,Integer toUserId,String pass,String code){
        System.out.println("报销撤销推送开始:"+new Date());
        System.out.println("报销id："+slCollectApplication.getId()+" userId: "+toUserId);
        HashMap<String,Object> hashMap=new HashMap<>();
        hashMap.put("personnelReimburse", slCollectApplication);
        User user = userDao.get(toUserId);  //推送人
        swMessageService.rejectSend(loginNum,operate,hashMap,toUserId.toString(),pass,null,null,user,code);
        System.out.println("报销撤销推送结束:"+new Date());
    }

    /**
     *
     * @param createDate 录入日期
     * @param approvalDate  受理时间
     * @param method  1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
     * @param userIdLogin  登录人id
     * @param userId 录入者id
     * @param map
     * @return
     */
    @Override
    public Map<String, Object> getCollectChecks(Date createDate, Date approvalDate, String method, Integer userIdLogin, Integer userId, Map<String, Object> map) {
        Map<String, Object> params = new HashMap<>();
        String hql = "select count(id),sum(amount) from SlCollectApplication where state='3'";
        String hqll = "select a.id,c.name,a.amount,a.method,a.createName,a.createDate from SlCollectApplication a,SlCustomer c where a.state='3' and a.customer=c.id";
        if (createDate!=null){
            hql+=" and date_format(createDate,'%Y-%m')=date_format(:createDate,'%Y-%m')";
            hqll+=" and date_format(a.createDate,'%Y-%m')=date_format(:createDate,'%Y-%m')";
            params.put("createDate",createDate);
        }
        if (approvalDate!=null){
            hql+=" and date_format(approvalDate,'%Y-%m')=date_format(:approvalDate,'%Y-%m')";
            hqll+=" and date_format(a.approvalDate,'%Y-%m')=date_format(:approvalDate,'%Y-%m')";
            params.put("approvalDate",approvalDate);
        }
        if (userIdLogin!=null){
            hql+=" and financer=:financer";
            hqll+=" and a.financer=:financer";
            params.put("financer",userIdLogin);
        }
        if (userId!=null){
            hql+=" and creator=:creator";
            hqll+=" and a.creator=:creator";
            params.put("creator",userId);
        }

        Object[] objects = (Object[]) slCollectApplicationDao.getByHQLWithNamedParams(hql,params);
        map.put("num",objects[0]); //总次数
        map.put("totalAmount",objects[1]);  //总金额

        List<Object[]> listObjects = slCollectApplicationDao.getListByHQLWithNamedParams(hqll,params);
        List<Map<String,Object>> listMap = new ArrayList<>();
        for (Object[] listObject:listObjects) {
            Map<String,Object> map1 = new HashMap<>();
            map1.put("collectId",listObject[0]); // id
            map1.put("name",listObject[1]);  //客户名称
            map1.put("amount",listObject[2]);  //金额
            map1.put("method",listObject[3]); // 支出方式
            map1.put("createName",listObject[4]); //申请人名称
            map1.put("createDate",listObject[5]); //申请时间
            listMap.add(map1);
        }
        map.put("listMap",listMap);
        return map;
    }

    @Override
    public Integer collectPaymentNumber(Integer oid) {
        Map<String,Object> params = new HashMap<>();
        String hql = "select count(a.id) from SlCollectApplication a,SlCustomer c where a.org=:org and a.recordType=0 and a.state=1 and a.customer=c.id";
        params.put("org",oid);
        Long number = (Long) slCollectApplicationDao.getByHQLWithNamedParams(hql,params);
        return  number.intValue();
    }

    /**
     * 查询回款确已收到且已录入系统(已入账的)
     * @param oid 机构ID
     * @param beginDate 开始时间
     * @param endDate 结束时间
     * @param customer 客户ID
     * 以下的参数暂不是查询条件
//     * @param method  回款方式 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
//     * @param amount  金额
//     * @param returnNo  支票号
//     * @param originalCorp  原始出具票据单位
//     * @param bankName  出具支票银行【银行转账的收款银行名称】
//     * @param expireDate  到期日期【银行转账的到账日期】
     * @return
     */
    @Override
    public List<SlCollectApplication> getCollectFinance(Integer oid,Date beginDate,Date endDate,Integer customer) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from SlCollectApplication where org=:oid and passEntry=1 and customer=:customer and state in (3,6)";
        map.put("oid",oid);
        map.put("customer",customer);
//        map.put("amount",amount);
//        map.put("method",method);
//        if ("3".equals(method) || "4".equals(method)){  // 3-转账支票（回款金额、支票号、支票到期日、出具支票单位、出具支票银行）
//            if (!MyStrings.nulltoempty(returnNo).isEmpty()){    //4-承兑汇票（回款金额、汇票号、汇票到期日、原始出具汇票单位、出具汇票银行）
//                hql+=" and returnNo=:returnNo";
//                map.put("returnNo",returnNo);
//            }
//            if (expireDate!=null){
//                hql+=" and expireDate=:expireDate";
//                map.put("expireDate",expireDate);
//            }
//            if (!MyStrings.nulltoempty(originalCorp).isEmpty()){
//                hql+=" and originalCorp=:originalCorp";
//                map.put("originalCorp",originalCorp);
//            }
//            if (!MyStrings.nulltoempty(bankName).isEmpty()){
//                hql+=" and bankName=:bankName";
//                map.put("bankName",bankName);
//            }
//        }
        if (beginDate!=null && endDate!=null){
            hql+=" and createDate between :beginDate and :endDate";
            map.put("beginDate",beginDate);
            map.put("endDate",endDate);
        }
        hql+=" order by createDate desc";
        List<SlCollectApplication> slCollectApplications = slCollectApplicationDao.getListByHQLWithNamedParams(hql,map);
        return slCollectApplications;
    }

    /**
     *
     * @param collectId 回款申请id
     * @param userId 登录人id
     * @param collectPayId  回款已入账的id
     * @return
     */
    @Override
    public Map<String,Object> getDetermine(Integer collectId, Integer userId, Integer collectPayId) {
        Map<String,Object> map = new HashMap<>();
        SlCollectApplication slCollectApplication = slCollectApplicationDao.get(collectId);
        SlCollectApplication slCollectApplicationPay = slCollectApplicationDao.get(collectPayId);
        User user = userDao.get(userId);
        if ("1".equals(slCollectApplication.getState())) {
            if ("3".equals(slCollectApplication.getMethod()) || "4".equals(slCollectApplication.getMethod())) {
                // 3-转账支票（回款金额、支票号、支票到期日、出具支票单位、出具支票银行）   4-承兑汇票（回款金额、汇票号、汇票到期日、原始出具汇票位、出具汇票银行）
                slCollectApplication.setFinanceAccountBill(slCollectApplicationPay.getFinanceAccountBill());
            } else if ("1".equals(slCollectApplication.getMethod())) {  //现金
                slCollectApplication.setFinanceAccountDetail(slCollectApplicationPay.getFinanceAccountDetail());
                slCollectApplication.setReceiveBank(slCollectApplicationPay.getId().toString());  //收款银行
            } else if ("5".equals(slCollectApplication.getMethod())) {  //银行转账
                slCollectApplication.setReceiveBank(slCollectApplicationPay.getReceiveBank()); //收款银行id
                slCollectApplication.setExpireDate(slCollectApplicationPay.getExpireDate()); // 到期日期(银行转账的到账日期)
                slCollectApplication.setBankName(slCollectApplicationPay.getBankName());
            }
            slCollectApplication.setCustomerName(slCollectApplicationPay.getCustomerName());
            slCollectApplication.setFinanceTime(slCollectApplicationPay.getFinanceTime());
            slCollectApplication.setFinancer(slCollectApplicationPay.getFinancer());
            slCollectApplication.setFinancerName(slCollectApplicationPay.getFinancerName());
            slCollectApplication.setFinancerTime(slCollectApplicationPay.getFinancerTime());
            slCollectApplication.setState("6");  //0-暂存,1-录入,2-提交,3-财务审批通过,4-财务审批否决,5-撤销,6-销售回款受理
            slCollectApplication.setApprovalDate(new Date());  //受理时间
            slCollectApplication.setUpdator(userId);
            slCollectApplication.setUpdateName(user.getUserName());
            slCollectApplication.setUpdateDate(new Date());
            slCollectApplicationDao.update(slCollectApplication);

                if (!"1".equals(slCollectApplication.getRecordType())) {  //财务录入的回款不用发送消息

                    //查找有回款入账的财务人员(财务录入的不进回款入账)
                    List<UserPopedom> userPopedomList = userPopedomService.getUserPopedomByMid(user.getOid(), "gb");
                    for (UserPopedom u : userPopedomList) {
                        if ("finance".equals(u.getUser().getManagerCode())) {
                            //给财务--回款入账发送
                        this.collectRejectSend(-1, -1, slCollectApplication, u.getUserId(), "/collectPaymentList", "returnInAccount");
                    }
                }

                // 回款经财务确认后 给申请人发消息提醒
                userSuspendMsgService.saveUserSuspendMsg(1, "经确认，您所录入的回款已入账完毕", "确认时间 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), slCollectApplication.getCreator(), "payBackMsg", collectId);  //给前端要查看详情的链接
                //给申请人，回款录入发送
                this.collectRejectSend(0, -1, slCollectApplication, slCollectApplication.getCreator(), "/getReceivableList", "returnInput");
            }
            map.put("status",1);
            map.put("content","操作成功");
        }else {
            map.put("status",2);
            map.put("content","已进行处理，不可重复操作");
        }
        return map;
    }

    @Override
    public Integer getBadgeNumber(User user, String code) {
        Integer result = null;
        switch (code){
            case "returnInAccount":
                if ("finance".equals(user.getManagerCode())) {
                    result = this.collectPaymentNumber(user.getOid());
                }else {
                    result = 0;
                }
                break;
        }
        return result;
    }
}
