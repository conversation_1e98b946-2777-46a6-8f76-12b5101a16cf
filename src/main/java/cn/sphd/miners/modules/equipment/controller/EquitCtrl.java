package cn.sphd.miners.modules.equipment.controller;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

@Controller
@RequestMapping("/equitment")
public class EquitCtrl {

    @RequestMapping("/equitmentIndex.do")
    public String equitmentIndex(){
        return "/equipment/equipmentList";
    }

    @RequestMapping("/equitmentName.do")
    public String equipmentName(){
        return "/equipment/nameManageMent";
    }

    @RequestMapping("/equitmentCategory.do")
    public String equipmentCategory(){
        return "/equipment/categoryManageMent";
    }

    @RequestMapping("/equitmentSupplier.do")
    public String equipmentSupplier(){
        return "/equipment/equipMentSupplier";
    }

    @RequestMapping("/equitmentAddRe.do")
    public String equitmentAddRe(){
        return "/equipment/suppleMentaryRecording";
    }

    @RequestMapping("/equipmentInitSet.do")
    public String equipmentInitSet(){
        return "vue/minersFrontEnd/src/views/machineEquipment/initialSettings";
    }
}
