package cn.sphd.miners.modules.complaint.service.impl;

import cn.sphd.miners.modules.complaint.entity.ComplaintAttachment;
import cn.sphd.miners.modules.complaint.service.ComplaintService;
import cn.sphd.miners.modules.uploads.service.FileUsingCallback;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContext;

/**
 * Created by 朱思旭 on 2021/3/26.
 */
public class SupplmentUsing implements FileUsingCallback {

    private static final long serialVersionUID = 1L;
    Integer id;

    @Override
    public boolean checkUsing(String filename) {
        if(StringUtils.isNotEmpty(filename)) {
            ServletContext sc = ContextLoader.getCurrentWebApplicationContext().getServletContext();
            ApplicationContext ac = WebApplicationContextUtils.getRequiredWebApplicationContext(sc);
            ComplaintService service = ac.getBean(ComplaintService.class, "complaintService");
            ComplaintAttachment attachment;
            if ((attachment = service.getSingelByComAtt(id)) != null
                    && service.getSingleBySupplement(attachment.getSupplement())!=null
                    && service.getSingleByCom(attachment.getComplaint())!=null
                    ) {
                return filename.equals(attachment.getPath());
            }
        }
        return false;
    }

    @Override
    @JsonIgnore @JSONField(serialize = false)
    public String getKey() {
        /* 由于本回调类是单实体使用，只需要使用id；
         * 被UploadFileUsing.callbackKey保存数据库用于删除文件引用。 */
        return id.toString();
    }

    public SupplmentUsing(){

    }

    public SupplmentUsing(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }
}
