package cn.sphd.miners.modules.accountant.entity;

import javax.persistence.Basic;
import javax.persistence.Id;
import java.sql.Date;

/**
 * Created by root on 17-1-11.
 */
public class TAccountantVoucherEntity {
    private int id;

    @Id
    @javax.persistence.Column(name = "id", nullable = false)
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    private Integer org;

    @Basic
    @javax.persistence.Column(name = "org", nullable = true)
    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    private String voucher;

    @Basic
    @javax.persistence.Column(name = "voucher", nullable = true, length = 20)
    public String getVoucher() {
        return voucher;
    }

    public void setVoucher(String voucher) {
        this.voucher = voucher;
    }

    private String belongPeroid;

    @Basic
    @javax.persistence.Column(name = "belong_peroid", nullable = true, length = 1)
    public String getBelongPeroid() {
        return belongPeroid;
    }

    public void setBelongPeroid(String belongPeroid) {
        this.belongPeroid = belongPeroid;
    }

    private Date bookDate;

    @Basic
    @javax.persistence.Column(name = "book_date", nullable = true)
    public Date getBookDate() {
        return bookDate;
    }

    public void setBookDate(Date bookDate) {
        this.bookDate = bookDate;
    }

    private String type;

    @Basic
    @javax.persistence.Column(name = "type", nullable = true, length = 1)
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    private String category;

    @Basic
    @javax.persistence.Column(name = "category", nullable = true, length = 1)
    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    private Integer sn;

    @Basic
    @javax.persistence.Column(name = "sn", nullable = true)
    public Integer getSn() {
        return sn;
    }

    public void setSn(Integer sn) {
        this.sn = sn;
    }

    private String settlement;

    @Basic
    @javax.persistence.Column(name = "settlement", nullable = true, length = 100)
    public String getSettlement() {
        return settlement;
    }

    public void setSettlement(String settlement) {
        this.settlement = settlement;
    }

    private String checkNo;

    @Basic
    @javax.persistence.Column(name = "check_no", nullable = true, length = 50)
    public String getCheckNo() {
        return checkNo;
    }

    public void setCheckNo(String checkNo) {
        this.checkNo = checkNo;
    }

    private Byte isAccount;

    @Basic
    @javax.persistence.Column(name = "is_account", nullable = true)
    public Byte getIsAccount() {
        return isAccount;
    }

    public void setIsAccount(Byte isAccount) {
        this.isAccount = isAccount;
    }

    private String reason;

    @Basic
    @javax.persistence.Column(name = "reason", nullable = true, length = 100)
    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    private Integer billDetail;

    @Basic
    @javax.persistence.Column(name = "bill_detail", nullable = true)
    public Integer getBillDetail() {
        return billDetail;
    }

    public void setBillDetail(Integer billDetail) {
        this.billDetail = billDetail;
    }

    private String summary;

    @Basic
    @javax.persistence.Column(name = "summary", nullable = true, length = 255)
    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    private Integer operator;

    @Basic
    @javax.persistence.Column(name = "operator", nullable = true)
    public Integer getOperator() {
        return operator;
    }

    public void setOperator(Integer operator) {
        this.operator = operator;
    }

    private String operatorName;

    @Basic
    @javax.persistence.Column(name = "operator_name", nullable = true, length = 100)
    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    private String memo;

    @Basic
    @javax.persistence.Column(name = "memo", nullable = true, length = 255)
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    private Integer creator;

    @Basic
    @javax.persistence.Column(name = "creator", nullable = true)
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    private String createName;

    @Basic
    @javax.persistence.Column(name = "create_name", nullable = true, length = 100)
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    private String createDate;

    @Basic
    @javax.persistence.Column(name = "create_date", nullable = true)
    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    private Integer updator;

    @Basic
    @javax.persistence.Column(name = "updator", nullable = true)
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    private String updateName;

    @Basic
    @javax.persistence.Column(name = "update_name", nullable = true, length = 100)
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    private String updateDate;

    @Basic
    @javax.persistence.Column(name = "update_date", nullable = true)
    public String getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }

    private Integer approveItem;

    @Basic
    @javax.persistence.Column(name = "approve_Item", nullable = true)
    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    private String approveStatus;

    @Basic
    @javax.persistence.Column(name = "approve_status", nullable = true, length = 1)
    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    private Integer approveLevel;

    @Basic
    @javax.persistence.Column(name = "approve_level", nullable = true)
    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    private Integer auditor;

    @Basic
    @javax.persistence.Column(name = "auditor", nullable = true)
    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    private String auditorName;

    @Basic
    @javax.persistence.Column(name = "auditor_name", nullable = true, length = 100)
    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    private String auditDate;

    @Basic
    @javax.persistence.Column(name = "audit_date", nullable = true)
    public String getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(String auditDate) {
        this.auditDate = auditDate;
    }

    private String operation;

    @Basic
    @javax.persistence.Column(name = "operation", nullable = true, length = 1)
    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    private String applyMemo;

    @Basic
    @javax.persistence.Column(name = "apply_memo", nullable = true, length = 255)
    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    private String approveMemo;

    @Basic
    @javax.persistence.Column(name = "approve_memo", nullable = true, length = 255)
    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    private Integer messageId;

    @Basic
    @javax.persistence.Column(name = "message_id", nullable = true)
    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        TAccountantVoucherEntity that = (TAccountantVoucherEntity) o;

        if (id != that.id) return false;
        if (org != null ? !org.equals(that.org) : that.org != null) return false;
        if (voucher != null ? !voucher.equals(that.voucher) : that.voucher != null) return false;
        if (belongPeroid != null ? !belongPeroid.equals(that.belongPeroid) : that.belongPeroid != null) return false;
        if (bookDate != null ? !bookDate.equals(that.bookDate) : that.bookDate != null) return false;
        if (type != null ? !type.equals(that.type) : that.type != null) return false;
        if (category != null ? !category.equals(that.category) : that.category != null) return false;
        if (sn != null ? !sn.equals(that.sn) : that.sn != null) return false;
        if (settlement != null ? !settlement.equals(that.settlement) : that.settlement != null) return false;
        if (checkNo != null ? !checkNo.equals(that.checkNo) : that.checkNo != null) return false;
        if (isAccount != null ? !isAccount.equals(that.isAccount) : that.isAccount != null) return false;
        if (reason != null ? !reason.equals(that.reason) : that.reason != null) return false;
        if (billDetail != null ? !billDetail.equals(that.billDetail) : that.billDetail != null) return false;
        if (summary != null ? !summary.equals(that.summary) : that.summary != null) return false;
        if (operator != null ? !operator.equals(that.operator) : that.operator != null) return false;
        if (operatorName != null ? !operatorName.equals(that.operatorName) : that.operatorName != null) return false;
        if (memo != null ? !memo.equals(that.memo) : that.memo != null) return false;
        if (creator != null ? !creator.equals(that.creator) : that.creator != null) return false;
        if (createName != null ? !createName.equals(that.createName) : that.createName != null) return false;
        if (createDate != null ? !createDate.equals(that.createDate) : that.createDate != null) return false;
        if (updator != null ? !updator.equals(that.updator) : that.updator != null) return false;
        if (updateName != null ? !updateName.equals(that.updateName) : that.updateName != null) return false;
        if (updateDate != null ? !updateDate.equals(that.updateDate) : that.updateDate != null) return false;
        if (approveItem != null ? !approveItem.equals(that.approveItem) : that.approveItem != null) return false;
        if (approveStatus != null ? !approveStatus.equals(that.approveStatus) : that.approveStatus != null)
            return false;
        if (approveLevel != null ? !approveLevel.equals(that.approveLevel) : that.approveLevel != null) return false;
        if (auditor != null ? !auditor.equals(that.auditor) : that.auditor != null) return false;
        if (auditorName != null ? !auditorName.equals(that.auditorName) : that.auditorName != null) return false;
        if (auditDate != null ? !auditDate.equals(that.auditDate) : that.auditDate != null) return false;
        if (operation != null ? !operation.equals(that.operation) : that.operation != null) return false;
        if (applyMemo != null ? !applyMemo.equals(that.applyMemo) : that.applyMemo != null) return false;
        if (approveMemo != null ? !approveMemo.equals(that.approveMemo) : that.approveMemo != null) return false;
        if (messageId != null ? !messageId.equals(that.messageId) : that.messageId != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = id;
        result = 31 * result + (org != null ? org.hashCode() : 0);
        result = 31 * result + (voucher != null ? voucher.hashCode() : 0);
        result = 31 * result + (belongPeroid != null ? belongPeroid.hashCode() : 0);
        result = 31 * result + (bookDate != null ? bookDate.hashCode() : 0);
        result = 31 * result + (type != null ? type.hashCode() : 0);
        result = 31 * result + (category != null ? category.hashCode() : 0);
        result = 31 * result + (sn != null ? sn.hashCode() : 0);
        result = 31 * result + (settlement != null ? settlement.hashCode() : 0);
        result = 31 * result + (checkNo != null ? checkNo.hashCode() : 0);
        result = 31 * result + (isAccount != null ? isAccount.hashCode() : 0);
        result = 31 * result + (reason != null ? reason.hashCode() : 0);
        result = 31 * result + (billDetail != null ? billDetail.hashCode() : 0);
        result = 31 * result + (summary != null ? summary.hashCode() : 0);
        result = 31 * result + (operator != null ? operator.hashCode() : 0);
        result = 31 * result + (operatorName != null ? operatorName.hashCode() : 0);
        result = 31 * result + (memo != null ? memo.hashCode() : 0);
        result = 31 * result + (creator != null ? creator.hashCode() : 0);
        result = 31 * result + (createName != null ? createName.hashCode() : 0);
        result = 31 * result + (createDate != null ? createDate.hashCode() : 0);
        result = 31 * result + (updator != null ? updator.hashCode() : 0);
        result = 31 * result + (updateName != null ? updateName.hashCode() : 0);
        result = 31 * result + (updateDate != null ? updateDate.hashCode() : 0);
        result = 31 * result + (approveItem != null ? approveItem.hashCode() : 0);
        result = 31 * result + (approveStatus != null ? approveStatus.hashCode() : 0);
        result = 31 * result + (approveLevel != null ? approveLevel.hashCode() : 0);
        result = 31 * result + (auditor != null ? auditor.hashCode() : 0);
        result = 31 * result + (auditorName != null ? auditorName.hashCode() : 0);
        result = 31 * result + (auditDate != null ? auditDate.hashCode() : 0);
        result = 31 * result + (operation != null ? operation.hashCode() : 0);
        result = 31 * result + (applyMemo != null ? applyMemo.hashCode() : 0);
        result = 31 * result + (approveMemo != null ? approveMemo.hashCode() : 0);
        result = 31 * result + (messageId != null ? messageId.hashCode() : 0);
        return result;
    }
}
