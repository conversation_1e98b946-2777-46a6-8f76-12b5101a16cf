<template>
  <div class="currentSettings">
    <div class="ty-container" id="infoCon">
      <el-tabs v-model="activeName">
        <el-tab-pane label="已分配权限" name="first">
          <!--   审批权限       -->
          <div class="tblContainer">
            <div class="ty-table" id="assigned" v-if="indexData.assigned.length !== 0">
              <ul class="ty-head">
                <li style="width: 10%">模块</li>
                <li style="width: 18%">子模块</li>
                <li style="width: 14.4%">二级子模块</li>
                <li style="width: 36%">功能描述</li>
                <li style="width: 21.6%">已拥有此权限者</li>
              </ul>
              <div class="ty-body">
                <ul v-for="(item, index1) in indexData.assigned" :key="index1">
                  <li style="width: 10%">
                    <span> {{item.name}} </span>
                  </li>
                  <li style="width: 89.8%"  level="1">
                    <div v-if="item.subPopdoms.length > 0">
                      <ul v-for="(nextItem, index2) in item.subPopdoms" :key="index2">
                        <li style="width: 20%">
                          <span> {{nextItem.name}} </span>
                        </li>
                        <li style="width: 79.8%"  level="2"  v-if="nextItem.subPopdoms.length > 0">
                          <div>
                            <ul v-for="(threeItem, index3) in nextItem.subPopdoms" :key="index3">
                              <li style="width: 20%">
                                <span> {{threeItem.name}} </span>
                              </li>
                              <li style="width: 49.8%">
                                <span> {{threeItem.desc}} </span>
                              </li>
                              <li style="width: 30%">
                                <span>
                                   <a class="tooltip-show" v-for="(userItem, index4) in threeItem.users" :key="index4" :title="'部门：'+chargeNull(userItem.departName) + ' ； 职位：'+chargeNull(userItem.postName)">
                              {{index4 > 0 ? '、':''}}{{userItem.userName}}
                            </a>
                                </span>
                              </li>
                            </ul>
                          </div>
                        </li>
                        <li style="width: 16%" v-if="nextItem.subPopdoms.length === 0"></li>
                        <li style="width: 39.8%" v-if="nextItem.subPopdoms.length === 0">
                          <span> {{nextItem.desc}} </span>
                        </li>
                        <li style="width: 24%" v-if="nextItem.subPopdoms.length === 0">
                          <span v-if='item.name === "个人中心" || item.name === "关于"'>{{ (nextItem.name === "请求处理" ? '全部非普通职工': '全部职工') }}</span>
                          <span v-else>
                            <a class="tooltip-show" v-for="(userItem, index4) in nextItem.users" :key="index4" :title="'部门：'+chargeNull(userItem.departName) + ' ； 职位：'+chargeNull(userItem.postName)">
                              {{index4 > 0 ? '、':''}}{{userItem.userName}}
                            </a>
                          </span>
                        </li>
                      </ul>
                    </div>
                    <div v-else></div>

                  </li>
                </ul>
              </div>
            </div>
            <div class="importantTip" v-else>
              <h4>重要提示!!</h4>
              <p>您把公司最高销售、财务与总务管理人员手机号码录入到高管管理中，才能更好地使用本系统。</p>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="未分配权限" name="second" v-if="indexData.assigned.length !== 0 && indexData.unAssigned !== undefined">
          <div class="tblContainer" id="unassigned">
            <div class="ty-table">
              <ul class="ty-head">
                <li style="width: 10%">模块</li>
                <li style="width: 18%">子模块</li>
                <li style="width: 72%">功能描述</li>
              </ul>
              <div class="ty-body">
                <ul v-for="(item, index1) in indexData.unAssigned" :key="index1">
                  <li style="width: 10%">
                    <span> {{item.name}} </span>
                  </li>
                  <li style="width: 89.8%"  level="1">
                    <div v-if="item.subPopdoms.length > 0">
                      <ul v-for="(nextItem, index2) in item.subPopdoms" :key="index2">
                        <li style="width: 20%">
                          <span> {{nextItem.name}} </span>
                        </li>
                        <li style="width: 79.8%"  level="2"  v-if="nextItem.subPopdoms.length > 0">
                          <div>
                            <ul v-for="(threeItem, index3) in nextItem.subPopdoms" :key="index3">
                              <li style="width: 20%">
                                <span> {{threeItem.name}} </span>
                              </li>
                              <li style="width: 79.8%">
                                <span> {{threeItem.desc}} </span>
                              </li>
                            </ul>
                          </div>
                        </li>
                        <li style="width: 79.8%" v-if="nextItem.subPopdoms.length === 0">
                          <span> {{nextItem.desc}} </span>
                        </li>
                      </ul>
                    </div>
                    <div v-else></div>

                  </li>
                </ul>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
import {beforeRouteLeave, initNav} from "@/utils/routeChange";
import * as curApi from "@/api/authority";

export default {
  data() {
    return {
      pageName: 'currentSettings',
      indexData: {
        assigned: [],
        unassigned: []
      },
      activeName: 'first'
    }
  },
  beforeRouteLeave(to, from, next) {
    beforeRouteLeave(to, from, next, this)
  },
  mounted() {
    initNav({mid: 'ed', name: '当前权限', pageName: this.pageName}, this.created, this)
  },
  methods: {
    created() {
      this.getCurrentSetting()
    },
    getCurrentSetting() {
      curApi.getCurrentSettingList()
          .then(res => {
            this.indexData = res.data["data"]
            console.log('=----------:')
            console.log(this.indexData.assigned)
          })
    },
    chargeNull(value) {
      return value === null?'--': value;
    }
  }
}
</script>

<style scoped lang="scss">
.currentSettings{
  font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
  font-size: 14px;
  font-weight: normal;
  line-height: 1.42857;
  color: #34495e;
  padding: 20px;
  margin-top: 10px;
  td > div:last-child{
    border: none;
  }
  .ty-table td {
    padding: 0;
  }
  .importantTip{
    width: 638px;
    height: 128px;
    border:2px dashed #0099FF;
    padding:20px 40px;
    text-align: center;
    background-color: #fff;
    margin-bottom: 8px;
  }
  .ty-table .ty-head li{
    border:none;
  }
  .ty-table .ty-body{
    background: #fff;
  }
  .ty-table ul{
    width: 100%;
    overflow: hidden;
  }
  .ty-table ul li{
    float: left;
    border:1px solid #efefef;
    min-height: 40px;
    line-height:40px;
    text-align: center;
    margin-right: -1px;
    height:100%;
    overflow:hidden;
    padding-bottom:9999px;
    margin-bottom:-9999px
  }
  :deep(.el-tabs__active-bar) {
    background-color: #48cfad;
  }
  :deep(.el-tabs__item.is-active) {
    color: #48cfad;
  }
  :deep(.el-tabs__item:hover) {
    color: #48cfad;
  }
  .tooltip-show{
    color: #337ab7;
    text-decoration: none;
  }
}
</style>