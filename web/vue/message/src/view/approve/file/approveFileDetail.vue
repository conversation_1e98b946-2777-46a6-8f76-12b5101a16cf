<template>
  <div id="untreateFile">
    <TY_NavTop title="文件审批"></TY_NavTop>
    <div class="container" v-loading="loading">
      <div class="place" v-if="isAbolishFile">
        <div class="placeContainer">
          <p>这是一条<span class="color-red">文件废止的申请</span>，请谨慎操作！</p>
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          <p class="fileTtl" v-text="fileDetails.details.name"></p>
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          <div class="line-p">
            <div class="tt-cell">文件编号</div>
            <div class="con-cell">{{fileDetails.details.fileSn}}</div>
          </div>
          <div class="line-p">
            <div class="tt-cell">保存位置</div>
            <div class="con-cell" v-html="filePosition"></div>
          </div>
          <div class="line-p">
            <div class="tt-cell">文件类型</div>
            <div class="con-cell">{{fileDetails.details.version}}</div>
          </div>
          <div class="line-p">
            <div class="tt-cell">文件大小</div>
            <div class="con-cell">{{fileDetails.details.size | formatByte}}</div>
          </div>
          <div class="line-p" v-if="fileDetails.details.changeNum">
            <div class="tt-cell">版本号</div>
            <div class="con-cell">G{{fileDetails.details.changeNum}}</div>
          </div>
          <div class="line-p" v-if="!fileDetails.details.changeNum">
            <div class="tt-cell">说明</div>
            <div class="con-cell" v-text="fileDetails.details.content" :title="fileDetails.details.content"></div>
          </div>
          <div class="line-p" v-if="fileDetails.details.changeNum">
            <div class="tt-cell">换版原因</div>
            <div class="con-cell" v-text="fileDetails.details.content" :title="fileDetails.details.content"></div>
          </div>
        </div>

      </div>
      <div class="place">
        <div class="placeContainer">
          <div class="line-btn-groups" v-if="isAbolishFile">
            <div class="line-p">
              <div class="tt-cell">将废止的文件</div>
              <div class="con-cell">
                <span class="fc-btn-see" @click="showDialog(2)">查看</span>
              </div>
            </div>
          </div>
          <div class="line-btn-groups" v-else>
            <div class="line-p">
              <div class="tt-cell">待审批的文件</div>
              <div class="con-cell">
                <span class="fc-btn-see" @click="showDialog(0)">查看</span>
              </div>
            </div>
            <div class="line-p" v-if="fileDetails.details.changeNum">
              <div class="tt-cell">当前版本</div>
              <div class="con-cell">
                <span class="fc-btn-see" @click="showDialog(1)">查看</span>
              </div>
            </div>
            <div class="line-p" v-if="fileDetails.details.changeNum">
              <div class="tt-cell">历史版本</div>
              <div class="con-cell">
                <span class="fc-btn-see">查看</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          <div class="record">
            <div class="record_title">审批记录</div>
            <div class="script">
              <div v-for="(item ,index) in approveRecord" :key="index">
                <div v-if="index === 0">
                  <span class="sm-ttl">申请人：</span>
                  <span class="sm-con" v-text="item.userName"></span>
                  <span>{{item.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                </div>
                <div v-if="index != 0">
                  <span class="sm-ttl">审批人：</span>
                  <span class="sm-con" v-text="item.userName"></span>
                  <span>{{item.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                </div>
              </div>
            </div>
            <div class="script">
              <el-form :rules="rules" :model="selectRule" class="handleFile">
                <el-form-item prop="radio">
                  <el-radio-group v-model="selectRule.radio">
                    <el-radio :label="1">本人无异议，选择下一个审批人</el-radio>
                    <el-form-item v-show="selectRule.radio === 1" class="approverNext" size="mini">
                      <el-select border v-model="selectRule.nextApprover" placeholder="选择下一位审批人" @change="nextAp">
                        <el-option
                          v-for="item in approverSelect"
                          :key="item.userID"
                          :label="item.userName"
                          :value="item.userID">
                        </el-option>
                      </el-select>
                    </el-form-item>
                    <el-radio :label="2" v-if="isAbolishFile">本人无异议，文件可正式作废</el-radio>
                    <el-radio :label="2" v-else>本人无异议，文件可正式{{titleName}}</el-radio>
                    <br/>
                    <el-radio :label="3">驳回该申请</el-radio>
                    <el-input v-show="selectRule.radio === 3" type="textarea" v-model="opposeForm.reason" placeholder="在此不输入内容，也可点击确定。"></el-input>
                  </el-radio-group>
                </el-form-item>
              </el-form>
            </div>
          </div>
          <div class="handle_button">
            <button class="ui-btn ui-btn_info" :disabled="btnControl" @click="toHandle()">确定</button>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="pendingDialog"
      width="70%">
      <el-form class="preview">
        <el-form-item>
          <el-radio-group v-model="browser">
            <el-radio :disabled="seeOnlineDisabled" :label="1">在线预览</el-radio>
            <el-radio :label="2">下载</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button handle-center">
          <a class="fc-btn-normal" @click="pendingDialog = false">取消</a>
          <span class="fc-btn ui-btn_info" ref="operate" :path="fileHandleDetail.path" :download="fileHandleDetail.name + '.' + fileHandleDetail.version" @click="oprationFile($event)">确定</span>
        </div>
      </span>
    </el-dialog>
    <el-dialog
      title=" ! 提示"
      :visible.sync="msgDialog"
      width="80%">
      <span class="st-center" v-if="fileDetails.details.changeNum">申请者已终止该文件的换版！</span>
      <span class="st-center" v-else>申请者已终止该文件的发布！</span>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button handle-center">
          <input type="submit" class="fc-btn ui-btn_info" value="确定" @click="refreshFile">
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { canSeeOnline } from '@/js/common'
export default {
  name: 'approveFileDetail',
  data () {
    return {
      loading: true,
      pendingDialog: false,
      browser: 1,
      approverSelect: [],
      opposeForm: {
        reason: ''
      },
      selectedAp: {},
      lastApprover: {},
      fileDetails: {
        categoryName: '',
        details: {},
        filePath: '',
        fileVersion: ''
      },
      approveRecord: [],
      selectRule: {
        radio: '',
        nextApprover: ''
      },
      rules: {
        radio: [
          { required: true, message: '请选择下一下一个审批人', trigger: 'change' }
        ]
      },
      listenersUid: [],
      fileHandleDetail: {},
      dialogTitle: '',
      seeOnlineDisabled: true,
      isAbolishFile: false,
      titleName: '换版'
    }
  },
  computed: {
    btnControl () {
      return !this.selectRule.radio || (this.selectRule.radio === 1 && !this.selectRule.nextApprover)
    },
    filePosition () {
      let pos = this.fileDetails.categoryName
      let posArr = pos.split('/')
      let posStr = ''
      for (let a = 0; a < posArr.length; a++) {
        if (a === posArr.length - 1) {
          posStr += '<span class="font-orange">' + posArr[a] + '</span>'
        } else {
          posStr += posArr[a] + '/'
        }
      }
      return posStr
    }
  },
  created: function () {
    let fileId = this.$route.params.pendId
    var _this = this
    this.sphdSocket.send('onePublishFileMessage', { 'session': this.sphdSocket.sessionid, 'hisId': fileId, 'userID': this.sphdSocket.user.userID })
    this.listenersUid = [
      this.sphdSocket.subscribe('onePublishFileMessage', function (data) {
        let fileData = JSON.parse(data)
        console.log("onePublishFileMessage", fileData)
        _this.fileDetails.categoryName = fileData.categoryName
        _this.fileDetails.details = fileData.resHis

        let teminateState = fileData.resHis.teminateState
        let filePath = ''
        if (teminateState === '1') {
          filePath = fileData.resHis.path
          _this.titleName = '废止'
        } else {
          if (fileData.resHis.changeNum > 0) {
            filePath = fileData.validFilePath
            _this.titleName = '换版'
          } else {
            _this.titleName = '发布'
          }
        }
        let pathArr = filePath.split('.')
        let version = pathArr[pathArr.length - 1]
        _this.fileDetails.filePath = filePath
        _this.fileDetails.fileVersion = version
        _this.isAbolishFile = teminateState === '1'

        if (fileData.listAp.length > 0) {
          _this.approveRecord = fileData.listAp
          let len = fileData.listAp.length - 1
          _this.lastApprover = fileData.listAp[len]
        }
        _this.loading = false
      }, null, 'session', fileId),
      this.sphdSocket.subscribe('fileUpdateState', function (data) {
        setTimeout(function () {
          _this.loading = false
          let val = JSON.parse(data)
          if (val === 2) {
            let tips = '申请者已终止该文件的' + this.titleName
            _this.$alert(tips, '！提示', {
              confirmButtonText: '确定',
              callback: () => {
                _this.refreshFile()
              }
            });
            _this.usNot = true
          } else {
            _this.$message({
              type: 'success',
              message: '操作成功！'
            })
            _this.refreshFile()
          }
        }, 3000)
      }, function () { console.log('Socket check Error:') }, 'custom', fileId)
    ]
    let prams = {
      'hisId': fileId,
      'userID': this.sphdSocket.user.userID
    }
    this.$http.post('../../../res/getAllOidUserByResource.do', prams, {
      emulateJSON: true
    }).then((response) => {
      let data = response.body
      _this.approverSelect = data['data']['list']
    }).catch(function () {
      _this.$message({
        type: 'error',
        message: '系统错误，请重试！'
      })
    })
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    toHandle () {
      let fileData = {
        'session': this.sphdSocket.sessionid,
        'userID': this.sphdSocket.user.userID,
        'hisId': this.$route.params.pendId,
        'processId': this.lastApprover.id,
        'level': this.lastApprover.level,
        'reason': '',
        'module': '文件与资料',
        'type': '1' // 1代表继续选择下级审批人，2代表选择下级审批人是总务后小总务，3代表进行最终审批
      }
      if (this.selectRule.radio === 1) {
        fileData.auditor = this.selectedAp.userID
        fileData.auditName = this.selectedAp.userName
        fileData.approveStatus = '2'
        fileData.type = '1' // 1代表继续选择下级审批人，2代表选择下级审批人是总务后小总务，3代表进行最终审批
      } else if (this.selectRule.radio === 2) {
        fileData.type = '2'
        fileData.approveStatus = '2'
        if (fileData.auditor || fileData.auditName) {
          delete fileData.auditor
          delete fileData.auditName
        }
      } else if (this.selectRule.radio === 3) {
        fileData.reason = this.opposeForm.reason
        fileData.approveStatus = '3'
        if (fileData.auditor || fileData.auditName) {
          delete fileData.auditor
          delete fileData.auditName
        }
      }
      this.sphdSocket.send('handleFile', fileData)
      this.loading = true
    },
    nextAp (vId) {
      this.selectedAp = this.approverSelect.find((item) => {
        return item.userID === vId // 筛选出匹配数据
      })
    },
    screen: function () {
      this.$router.push({
        path: '/applyFileQuery',
        name: 'applyFileQuery',
        params: {
          event: 3
        }
      })
    },
    oprationFile: function (event) {
      let el = event.currentTarget
      window.parent.previewOrDownload(el, this.browser)
    },
    showDialog: function (type) {
      let fileHandleDetail = {}
      if (type === 0) {
        this.dialogTitle = '待审批的文件'
        fileHandleDetail = {
          path: this.fileDetails.details.path,
          name: this.fileDetails.details.name,
          version: this.fileDetails.details.version
        }
      } else if (type === 1){
        this.dialogTitle = '当前的有效文件'
        fileHandleDetail = {
          path: this.fileDetails.filePath,
          name: this.fileDetails.details.name,
          version: this.fileDetails.fileVersion
        }
      } else if (type === 2) {
        this.dialogTitle = '将废止的文件'
        fileHandleDetail = {
          path: this.fileDetails.details.path,
          name: this.fileDetails.details.name,
          version: this.fileDetails.details.version
        }
      }
      this.seeOnlineDisabled = !canSeeOnline(fileHandleDetail.version)
      this.fileHandleDetail = fileHandleDetail
      this.pendingDialog = true
      if (this.seeOnlineDisabled) {
        this.browser = 2
      } else {
        this.browser = 1
      }
    },
    refreshFile: function () {
      this.$router.go(-1)
    }
  }
}
</script>
