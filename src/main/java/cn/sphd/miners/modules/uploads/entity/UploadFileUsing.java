package cn.sphd.miners.modules.uploads.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import org.hibernate.annotations.CreationTimestamp;
import javax.persistence.*;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2021-01-23 
 */

@Entity
@Table ( name ="t_upload_file_using",
		uniqueConstraints = {@UniqueConstraint(columnNames = {"callback_class","callback_key"})},
		indexes = {@Index(columnList = "filename")})
public class UploadFileUsing extends BaseEntity {
	private static final long serialVersionUID = 1L;

	@Id
	@Column
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

   	@Column
	private Integer org;
   	@Column(name = "acc_id")
	private Long accId;

	@Column
	private String filename;

   	@Column(length = 50)
	private String module;

	@Column(name = "display_name")
	private String displayName;

	@Column(name = "callback_class")
	private String callbackClass;

   	@Column(name = "callback_json")
	private String callbackJson;

	@Column(name = "callback_key")
	private String callbackKey;

	@CreationTimestamp
	@Column(name = "create_date")
	private Date createDate;

	@Column(name = "user_id")
	private Integer userId;

	@Column(name = "user_name",length = 100)
	private String userName;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getOrg() {
		return org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Long getAccId() {
		return accId;
	}

	public void setAccId(Long accId) {
		this.accId = accId;
	}

	public String getFilename() {
		return filename;
	}

	public void setFilename(String filename) {
		this.filename = filename;
	}

	public String getModule() {
		return module;
	}

	public void setModule(String module) {
		this.module = module;
	}

	public String getDisplayName() {
		return displayName;
	}

	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}

	public String getCallbackClass() {
		return callbackClass;
	}

	public void setCallbackClass(String callbackClass) {
		this.callbackClass = callbackClass;
	}

	public String getCallbackJson() {
		return callbackJson;
	}

	public void setCallbackJson(String callbackJson) {
		this.callbackJson = callbackJson;
	}

	public String getCallbackKey() {
		return callbackKey;
	}

	public void setCallbackKey(String callbackKey) {
		this.callbackKey = callbackKey;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}
}