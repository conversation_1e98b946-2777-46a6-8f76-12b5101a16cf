import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

const originalPush = Router.prototype.push
Router.prototype.push = function push (location) {
  return originalPush.call(this, location).catch(err => err)
}


let routes = [];

// auto import route.js
(modules => modules.keys().forEach((key) => {
  routes = routes.concat(modules(key).default);
}))(require.context('@/router/routes', false, /\.routes\.js/));


let router = new Router({
  routes: routes
})
router.beforeEach((to, from, next) => {
  if (to.matched.length === 0) { // 如果未匹配到路由
    next({ path: '/seeMore' })
    // from.name ? next({ name:from.name }) : next('/')
  } else {
    next()
  }
})
export default router
