package cn.sphd.miners.modules.accountant.controller;

import cn.sphd.miners.common.utils.WriteJSONUtil;
import cn.sphd.miners.modules.accountant.service.AccountantReportsService;
import cn.sphd.miners.modules.system.entity.User;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;

/**
 * Created by root on 17-5-17.
 */
@Controller
@RequestMapping("/accountant")
public class AccountantReportsController {

    private static final String SUCCESS = "1";
    private static final String ERROR = "0";

    @Autowired
    AccountantReportsService accReportsService;

    /*获得科目余额表的数据
    * peroid 要查询的年月，如：201705
    * */
    @ResponseBody
    @RequestMapping("/getSubjectBalanceSheet.do")
    public void getSujectBalanceSheet(User user, String period, HttpServletResponse response)
    {
        JSONObject result = accReportsService.getSubBalanSheetByPeroid(user,period);
        WriteJSONUtil.writeJSON(result.toString(), response);

    }



}
