<template>
  <div id="purchasePaymentMethod2">
    <TY_NavTop :title="ttl" :isButton="true" @toggleSubmit="selectInvoiceOk"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="tip2">
        <span>已选择开户行：{{ selectInvoice.selectItem.isPublic== 1 ? '对公户': '非对公户' }} {{ selectInvoice.selectItem.bankName }} {{ selectInvoice.selectItem.account }}</span>
        <p>请选择该开户行可供选择的内部转账支票</p>
      </div>
      <div class="list" v-if="list.length > 0">
        <ul class="ul6" >
          <li v-for="(item, index) in list" v-bind:key="index">
            <div><el-radio v-model="financeInvoiceItem"  :label="item">{{''}}</el-radio></div>
            <div>
              <span>支票号</span>
              <span class="marL50"> {{ item.chequeNo }}</span>
            </div>
          </li>
        </ul>
      </div>
      <div class="list" v-if="list.length === 0">
        <div style="font-size: 16px; color:#aaa; text-align: center; line-height: 100px;">该银行没有转账支票选择！</div>
      </div>
    </div>
  </div>
</template>
<style lang="less">
#purchasePaymentMethod2{
  .tip2{ background: #fff; line-height: 40px; padding:0 10px; }
  .linkBtn{ color:#5d9cec; cursor:default; margin-right:20px; }
  .linkBtn:hover{ text-decoration:underline;  }
  ul>li:nth-child(odd){ background: #fff; }
  ul>li:nth-child(even){ background: #ddd; }
  ul>li{ display: flex; margin-left:-40px; padding: 7px 0 0 0; }
  ul>li>div:nth-child(1){ flex:1; }
  ul>li>div:nth-child(1) label{ position: relative; top: 6px;  }
  ul.ul6>li>div:nth-child(1) label{ position: relative; top:-4px; left: 10px; }
  ul>li>div:nth-child(2){ flex:6; }
  ul>li>div:nth-child(2) .marL50{ display: inline-block; margin-left:14px;  }
  ul>li>div:nth-child(3){ flex:1; line-height: 50px }
}
</style>
<script>
export default {
  name: 'purchasePaymentMethod2',
  data () {
    return {
      financeInvoiceItem: {},
      selectInvoice: {},
      tip: '',
      ttl: '',
      loading: true,
      list: []
    }
  },
  created () {
    let that = this
    let parampun = Number(this.$route.params.pund)
    that.pund = parampun
    that.selectInvoice = this.$store.getters.getPurchaseInvoice
    if (parampun === 3) {
      that.ttl = '多收来的款'
    } else {
      that.ttl = '采购部门的付款'
    }
    that.getInvoiceList()
  },
  destroyed: function () {
  },
  methods: {
    getInvoiceList: function () {
      let that = this
      this.axios.post('../../../data/getChequeDetailByAccountId.do', { 'accountId': that.selectInvoice.selectItem.id })
        .then(function (response) {
          that.loading = false
          let res = response.data.data
          that.list = res.financeChequeDetails || []
        }).catch(function (error) {
          console.log(error)
        })
    },
    selectInvoiceOk: function () {
      console.log('确定')
      let that = this
      if (that.financeInvoiceItem.id) {
        that.selectInvoice.financeInvoiceItem = that.financeInvoiceItem
        this.$store.dispatch('setPurchaseInvoice', that.selectInvoice)
        if (that.pund === 3 || that.pund === '3') {
          this.$router.push({
            path: `/overreceivedMoneyApprove1/${that.selectInvoice.id}/${that.selectInvoice.updatePay}/1`
          })
        } else if (that.pund !== 1) {
          this.$router.push({
            path: `/purchasePaymentFinanceFu/${that.selectInvoice.id}/${that.selectInvoice.updatePay}/1/0/2`
          })
        } else {
          this.$router.push({
            path: `/purchasePaymentFinanceFu/${that.selectInvoice.id}/${that.selectInvoice.updatePay}/1/1/2`
          })
        }
      } else {
        that.$kiko_message('请先选择支票')
      }
    }
  }
}
</script>
