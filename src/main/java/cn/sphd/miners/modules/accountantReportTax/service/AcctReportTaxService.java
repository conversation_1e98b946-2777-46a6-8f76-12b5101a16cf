package cn.sphd.miners.modules.accountantReportTax.service;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctReportEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxDetailEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxDetailHistoryEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxEntity;
import cn.sphd.miners.modules.system.entity.User;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

public interface AcctReportTaxService {

    JsonResult addReport(User user, AcctReportEntity reportEntity);

    JsonResult addTax(User user, AcctTaxEntity taxEntity);

    JsonResult getReportList(User user);

    JsonResult setReportDisenabled(User user, Integer reportId);

    JsonResult setReportTax(Integer detailId,int reportId, int reportHisId,String taxArray, String taxDate,User user);

    JsonResult getEditRecord(User user, int reportHistory,int reportId, String period);

    JsonResult getReportTaxRecord(User user, Integer reportId,Integer taxId, String year,int type);

    JsonResult updateReportName(User user, int reportId, String name,String type,String periodArray);//修改报表名称

    JsonResult getReportByDate(User user, int year, int month);

    JsonResult reportUpdateRecord(User user, int reportId);

    JsonResult updateTax(User user, int taxId, String name, String type, Integer curReportId,Integer newReportId);

    JsonResult setTaxDisenabled(User user, Integer taxId);

    JsonResult taxUpdateRecord(User user, int taxId);

    JsonResult getTaxDetial(User user, int taxId,int reportId);

    JsonResult addTrifles(User user, String name, String frequency,String periodArray);

    JsonResult getTriflesList(User user);

    JsonResult setTriflesDisenabled(User user, Integer triflesId);

    JsonResult updateTrifles(User user, int triflesId, String name, String type, String periodArray);

    JsonResult triflesUpdateRecord(User user, int triflesId);

    JsonResult getTriflesByDate(User user, int year, int month);

    JsonResult handleTrifles(Integer detailId,int triflesId,int triflesHisId,String latestDeclare,User user);

    JsonResult getTriflesReportRecord(User user, Integer triflesId, String year);

    JsonResult getTriflesEditRecord(User user, int triflesId, String period);

    JsonResult getReportDetial(User user, int reportId);

    JsonResult delTestData(User user);

    JsonResult getTriflesDetial(User user, int triflesId);

    //初始化系统自带的税种
    void initialSystemTax(Integer org,Integer userId,String userName);

    /**
     * PC端录入税款后需要给税种的实际金额赋值
     * org
     * taxId            税种ID
     * report           报表ID
     * factAmount       实际金额
     * period           报表需申报的月份，格式yyyy-MM  PC端选择的某条报税记录中的起日期
     * belongsBegin     税款所属时期
     * belongsEnd       税款所属时期
     * payTime          缴款时间
     * accountDetail    财务明细账ID
     * updator          修改人ID
     * updateName       修改人名称
     * updateDate       修改日期
     * */
    Integer setTaxFactAmount(AcctTaxDetailEntity detailEntity);

    /**
     * id   setTaxFactAmount 这个方法返回的ID
     * */
    Map<String,Object> getById(Integer id);

    JsonResult insertTax(User user, AcctTaxEntity taxEntity);

    JsonResult taxList(User user,Integer type);

    JsonResult delTax(User user, Integer id);

    JsonResult getApplyTaxList(User user, Integer taxId, Date startDate, Date endDate);

    JsonResult getTaxedList(User user, String period);

    JsonResult getPayTaxList(User user, Integer detailId);

    /**
     * 返回税种ID
     * type;  类型:1-工资,2-所得税,3-社保,4-公积金
     *
     * */
    Integer getTaxId(byte type,Integer org);

    /**
     * 释放某条申报记录
     * 税款修改后如果审批驳回的话需要释放之前选的申报记录
     * id   要释放锁定状态的申报记录ID
     * */
    int releasePeriodById(Integer id);
}
