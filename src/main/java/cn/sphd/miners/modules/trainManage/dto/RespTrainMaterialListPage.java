package cn.sphd.miners.modules.trainManage.dto;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.trainManage.entity.TrainingMaterial;
import cn.sphd.miners.modules.trainManage.entity.TrainingQuestionBank;
import cn.sphd.miners.modules.trainManage.entity.TrainingQuestionBankAttachment;

import java.util.List;

/**
 * @ClassName RespTrainMaterialListPage
 * @Description 素材列表
 * <AUTHOR>
 * @Date 2021/2/18 8:38
 * @Version 1.0
 */
public class RespTrainMaterialListPage {
    private Integer status;//是否可操作
    private TrainingQuestionBank trainingQuestionBank;//题库信息
    private PageInfo pageInfo;
    private List<TrainingMaterial> trainingMaterialList;//素材列表
    private List<TrainingQuestionBankAttachment> trainingQuestionBankAttachmentList;//附件列表

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<TrainingQuestionBankAttachment> getTrainingQuestionBankAttachmentList() {
        return trainingQuestionBankAttachmentList;
    }

    public void setTrainingQuestionBankAttachmentList(List<TrainingQuestionBankAttachment> trainingQuestionBankAttachmentList) {
        this.trainingQuestionBankAttachmentList = trainingQuestionBankAttachmentList;
    }

    public TrainingQuestionBank getTrainingQuestionBank() {
        return trainingQuestionBank;
    }

    public void setTrainingQuestionBank(TrainingQuestionBank trainingQuestionBank) {
        this.trainingQuestionBank = trainingQuestionBank;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<TrainingMaterial> getTrainingMaterialList() {
        return trainingMaterialList;
    }

    public void setTrainingMaterialList(List<TrainingMaterial> trainingMaterialList) {
        this.trainingMaterialList = trainingMaterialList;
    }
}
