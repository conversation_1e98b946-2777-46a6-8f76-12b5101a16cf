package cn.sphd.miners.modules.schedule.service;

import cn.sphd.miners.modules.schedule.entity.MemoFavorite;
import cn.sphd.miners.modules.schedule.entity.MemoFavoriteSchedule;
import cn.sphd.miners.modules.schedule.entity.MemoSchedule;

import java.util.List;

public interface MemoFavoriteService {

    void  saveMemoFavorite(MemoFavorite memoFavorite);

    MemoFavorite getMemoFavoriteById(Integer id);

    void updateMemoFavorite(MemoFavorite memoFavorite);

    void deleteMemoFavorite(MemoFavorite memoFavorite);

    List<MemoFavorite> getFirstFavorites(Integer userId);

    List<MemoFavorite> getFavoritesByPid(Integer pid);

    List<Integer> getFavoriteIdsByPath(String path);

    Integer getCountsByFavoriteIds(List<Integer> favoriteIds);

    List<MemoSchedule> getMemoSchedulesByFavoriteId(Integer userId,Integer favoriteId);

    MemoFavoriteSchedule getMemoFavoriteScheduleByScheduleId(Integer userId,Integer oldFavoriteId,Integer scheduleId);

    void  updateMemoFavoriteSchedule(MemoFavoriteSchedule memoFavoriteSchedule);

    void  saveMemoFavoriteSchedule(MemoFavoriteSchedule memoFavoriteSchedule);

    void deleteMemoFavoriteSchedule(MemoFavoriteSchedule memoFavoriteSchedule);

    void deleteAllMemoFavoriteSchedule(List<MemoFavoriteSchedule> memoFavoriteScheduleList);

    List<MemoFavoriteSchedule> getMemoFavoriteSchedulesByScheduleId(Integer scheduleId);

}
