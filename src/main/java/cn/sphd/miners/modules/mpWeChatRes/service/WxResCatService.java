package cn.sphd.miners.modules.mpWeChatRes.service;

import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.mpWeChatRes.entity.WxResourceCategory;
import cn.sphd.miners.modules.mpWeChatRes.entity.WxResourceCategoryHistory;

import java.util.HashMap;
import java.util.List;

/**
 * Created by 朱思旭 on 2021/12/21.
 */
public interface WxResCatService {

    //获取文件夹实体
    WxResourceCategory getWxResCatSingle(Integer id);

    //获取小程序初始目录
    HashMap<String, Object> mimiProgramInitialDirectory(User user);

    //微信小程序获取首页信息
    HashMap<String, Object> getWxHomePageMes();

    //获取本级目录和子级目录的详情
    HashMap<String, Object> minidirectionAndChild(Integer categoryId);

    HashMap<String, Object>getWxParentAndChild(Integer category);

    //循环得到某目录的全部父级文件夹名字或路径（如“图书/历史图书/中国历史”这种字样或 “1,2“）
    String wxAllParentCatNameOrPath(Integer categoryId, String categoryName, String path, String type);

    //新增目录
    HashMap<String, Object> addWxDirectory(User user, Integer parent, String categoryName);

    //修改目录名称
    HashMap<String, Object> upWxCategoryName(User user, Integer category, Integer parent, String categoryName);

    //目录修改记录
    List<WxResourceCategoryHistory> listWxCategoryHistory(Integer category);

    //删除目录
    Integer delWxCategory(Integer category);

    //获取垃圾目录
    WxResourceCategory wxTrashCategory(Integer org);

    //初始化App和文件夹
    List<WxResourceCategory> addInitialDirectory(Integer org, String appId, String appname, String appsecret, String token, Boolean canpay, String module);
}
