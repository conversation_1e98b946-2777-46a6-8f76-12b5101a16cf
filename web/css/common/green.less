@white   : #fafafb;
@grayF2  : #f1f1f2;
@grayE8  : #e8e8e8;
@grayE0  : #e0e0e0;
@grayE   : #eeeeee;
@grayC   : #cccccc;
@grayA   : #aaaaaa;
@gray6   : #666666;
@gray3   : #333333;
@gray9   : #999999;
@gray10  : #101010;
@green   : #00ce9b;
@cyan    : #a0d468;
@blue    : #4797f1;
@red     : #ed5565;
@orange  : #f58410;
@darkBlue: #3f5266;
@color-disabled : #dff0ff;
@color-bg : #f1f3f5;

@button-radius : 0px;
@header-w:160px;

@input-radius : 2px;
@input-min-width : 182px;
@inputNum-min-width:48px;
@inputNum-h:30px;
@inputNum-border-color:#e3e3e3;

@color-table:#676a74;
@color-table-border:#e9e9e9;
@color-table-h:40px;
@color-thead-td-bg:@grayE;
@color-thead-border:@grayE0;
@color-thead:@gray3;
@table-thead-border-w:1px;

@process-item-padding-top-bottom : 9px ;
@process-item-lineHeight : 30px ;
@process-dot-w : 8px;

@fileItem-w : 640px;
@fileItem-h : 60px;
@color-fileItem-hover : #f4f5f9;
@color-fileItem-shadow : #0075ff;
@fileItem-fileType-bg-doc  :url("../../../css/content/img/doc.png    ") no-repeat;
@fileItem-fileType-bg-xls  :url("../../../css/content/img/xls.png    ") no-repeat;
@fileItem-fileType-bg-ppt  :url("../../../css/content/img/ppt.png    ") no-repeat;
@fileItem-fileType-bg-rar  :url("../../../css/content/img/rar.png    ") no-repeat;
@fileItem-fileType-bg-pdf  :url("../../../css/content/img/pdf.png    ") no-repeat;
@fileItem-fileType-bg-jpg  :url("../../../css/content/img/jpg.png    ") no-repeat;
@fileItem-fileType-bg-other:url("../../../css/content/img/other.png  ") no-repeat;
@fileItem-fileInfo-h:40px;
@filePreview-w:280px;
@filePreview-padding:20px;
@filePreview-bg:#f4f5f9;



@fileTree-min-w:160px;
@fileTree-min-h:500px;
@color-fileTree-bg:#f4f5f9;
@fileTree-item-h:30px;
@fileTree-item-active-bg:#ebecf0;
@fileTree-item-level-marginLeft :20px;


body{
  font: 14px/1.5 "Helvetica Neue",Helvetica,Arial,"Microsoft Yahei","Hiragino Sans GB","Heiti SC","WenQuanYi Micro Hei",sans-serif;
  margin: 0;
  padding: 0;
}
ul,li{
  margin: 0;
  padding:0;
  list-style: none;
}
a:hover{
  text-decoration:none;
}
.page-content-wrapper{
  .page-content{
    padding: 0;
    background-color:@color-bg;
  }
}
.page-header.navbar{
  .page-logo{
    background-color:#2B3643;
  }
  .top-menu{
    .navbar-nav{
      li.navbar-navItem{
        height: 46px;
        padding: 0 16px;
        color: #b4bcc8;
        line-height:46px;
        &:hover{
          background-color: #3f4f62;
          cursor: pointer;
        }
      }
    }
  }
}
.navbar-nav-blog{
  .navbar-navItem-title{
    font-size: 20px;
    line-height:46px;
  }
}
.navbar-nav-msg{
  i{
    line-height:46px;
    font-size:20px;
  }
}

.clr{ clear:both;   }



.ty-container, .ty-header {
  font-size:14px;
  color: @gray10;
  padding:0 10px 0;
}
.ty-container{
  background:@color-bg;
}

.ty-header{
  background:@white;
  padding:1px 10px 8px ;
  zoom:1;
  h3{
    font-size:18px;
  }
  p{
    line-height:30px;
    .nav_{
      font-size: 14px;  }
    .navTxt{
      color:@green;
      font-weight:normal;
      font-size:14px;
      a{
        color:@green;
        &:hover{
          color:@green;
          text-decoration:underline;
          cursor:pointer;
        }
      }
    }
  }
  &:after{
    display:block;
    clear:both;
    content:"";
    visibility:hidden;
    height:0
  }
}
.ty-firstTab{
  line-height:40px;
  font-size:14px;
  height:40px;
  margin-bottom: -8px;
  li{
    display:inline-block;
    padding:0 30px;
    cursor:pointer;
    background-color:@white;
    margin-right: -4px  ;
    &:hover{
      background-color: @color-bg;
      -webkit-transition: all .3s ;
      -moz-transition: all .3s ;
      -ms-transition: all .3s ;
      -o-transition: all .3s ;
      transition: all .3s ;
    }
    &.ty-active{
      background-color:@color-bg;
      color:@green;
    }
  }
}
.ty-secondTab{
  border-bottom:1px solid @grayC;
  font-size:14px;
  line-height:50px;
  height:50px;
  li{
    display:inline-block;
    height:50px;
    padding:0 20px;
    cursor:pointer;
    color: #757679;
    &:hover{
    }
    &.ty-active{
      border-bottom:2px solid @green;
      color:@green;
      background-color:@color-bg;
    }
  }
}

.ty-panel{ background:@white;   }
.ty-procon{ padding:0 10px;    }
.ty-mainData{  margin:8px 0;     }
/*   button   */
.ty-btn{
  display:inline-block;
  background-color:@white;
  color:#24292e;
  padding:2px 10px;
  cursor: pointer;
  outline: none;
  border: 1px solid #dcdfe6;
  line-height:1.5;
  &:hover{
    background-color: @grayE8;
  }
  &:disabled{
    cursor: not-allowed;
    background-color: #ccc !important;
    color:#666 !important;
    border-color: #ccc !important;
  }
}
.ty-btn-big{
  padding:4px 25px;
}
.ty-btn-gray{
  background-color: @grayE8;
  color:@gray6;
  border-color: @gray6;
}
.ty-btn-green{
  background-color:@green;
  color:@white;
  border-color: @green;
  &:hover{
    background-color: darken(@green,5%);
    border-color: darken(@green,5%);
  }
  &.active{
    background-color: darken(@green,5%);
    border-color: darken(@green,5%);
  }
}
.ty-btn-red{
  background-color:@red;
  color:@white;
  border-color:@red;
  &:hover{
    background-color: darken(@red,5%);
    border-color:darken(@red,5%);
  }
  &.active{
    background-color: darken(@red,5%);
    border-color:darken(@red,5%);
  }
}
.ty-btn-blue{
  background-color:@blue;
  border-color:@blue;
  color:@white;
  &:hover{
    background-color: darken(@blue,5%);
    border-color: darken(@blue,5%);
  }
  &.active{
    background-color: darken(@blue,5%);
    border-color: darken(@blue,5%);
  }
}
.ty-btn-orange{
  background-color:@orange;
  border-color:@orange;
  color:@white;
  &:hover{
    background-color: darken(@orange,5%);
    border-color: darken(@orange,5%);
  }
  &.active{
    background-color: darken(@orange,5%);
    border-color: darken(@orange,5%);
  }
}

.ty-circle-5,.ty-circle-3{
  border-radius: @button-radius;
}

/*  按钮组  */
.ty-btn-group{
  position: relative;
  display: inline-block;
  vertical-align: top;
  .ty-btn{
    position: relative;
    float: left;
    margin-left: -1px;
    &:last-child:not(:first-child) {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
    &:first-child:not(:last-child):not(.dropdown-toggle){
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
    &:not(:first-child):not(:last-child):not(.dropdown-toggle){
      border-radius: 0;
    }
  }
  .ty-btn-active-green{  background-color:@green; color:@white; border-color: @green  }
  .ty-btn-active-blue {  background-color:@blue ; color:@white; border-color: @blue  }
  .ty-btn-active-red  {  background-color:@red  ; color:@white; border-color: @red  }
  .ty-inputNum{
    float: left;
    background-color: @white;
    font-size:12px;
    min-width:@inputNum-min-width;
    color: @gray6;
    text-align: center ;
    border:1px solid @inputNum-border-color ;
    margin-left: -1px;
    margin-right: -1px;
    &:focus{
      background-color: @white;
    }
    /*  组合数字加减号  */
    .ty-inputReduce,.ty-inputAdd{
      float: left;
      height:@inputNum-h;
      line-height:@inputNum-h;
      background-color: @white;
      padding:0 10px;
      font-size:12px;
      color: @gray6;
      border:1px solid @inputNum-border-color;
      &:hover{
        background-color: @green;
        color: @white;
        border:1px solid @green;
      }

    }
  }
}

/*input*/
.ty-inputText,.ty-inputSelect{
  background-color: @white;
  padding: 0 10px;
  font-size: 12px;
  border-radius: @input-radius;
  color: @gray6;
  min-width:@input-min-width;
  height: 36px;
  border:1px solid #dee8f0;
  &:disabled{
    background-color: @color-disabled;
    color: @gray6;
  }
  &:focus{
    border:1px solid @blue;
  }
}
.ty-search , .ty-searchInput,.ty-searchBtn{
  display:inline-block;
  height:32px;border:none;
}
.ty-search{
  position:relative;
  vertical-align: top;
  .ty-search-ttl{
    padding:0 15px 0 0 ;
  }
  .ty-searchInput{
    background-color:@color-bg;
    padding:0 10px;
    font-size:12px;
    border-radius:@input-radius;
    min-width:@input-min-width;
    &:focus{
      border:1px solid @grayE0;
    }
  }
  .ty-searchBtn{
    width: 32px;
    background: @grayE0 url("theme/icon/search.png") no-repeat center center;
    background-size:20px 20px;
    border-radius:0 @input-radius @input-radius 0;
    position:absolute;
    top:0;
    right:0;
    cursor: pointer;
    &:hover{
      background-color: darken(@grayE0,5%);
    }
  }
}

/*  table  */
.ty-table{
  width:100%;
  color: #676a74;
  .ty-inputText{
    border: 1px solid #e3e3e3 ;
    border-radius: 0;
    &:focus{
      border: 1px solid #48cfad
    }
  }
  td{
    border:1px solid @color-table-border;
    text-align:center;
    padding:0 15px;
    height:@color-table-h;
  }
  thead{
    td{
      border: @table-thead-border-w solid @color-thead-border;
      background-color: @color-thead-td-bg;
      color: @color-thead;
      font-size: 14px;
      //font-weight:bold;
    }
  }
  tbody{
    tr{
      td{
        background-color:@white;
      }
    }
  }
}

/* table button controller */
.ty-color-gray     { color:@grayA;    }
.ty-color-green    { color:@green;    }
.ty-color-blue     { color:@blue;     }
.ty-color-red      { color:@red;      }
.ty-color-cyan     { color:@cyan;     }
.ty-color-orange   { color:@orange;   }
.ty-color-darkBlue { color:@darkBlue; }
.colorC            { color:@grayC;    }
.color6            { color:@gray6;    }
.color3            { color:@gray3;    }

.ty-table-control,.ty-td-control{
  tbody{
    td{
      span{
        font-size:12px;
        padding:5px 10px 5px;
        font-weight: bold;
        cursor: pointer;
        border-radius: @button-radius;
        &.ty-color-green    { &:hover { background-color : @green    ;  color : @white } }
        &.ty-color-blue     { &:hover { background-color : @blue     ;  color : @white } }
        &.ty-color-red      { &:hover { background-color : @red      ;  color : @white } }
        &.ty-color-cyan     { &:hover { background-color : @cyan     ;  color : @white } }
        &.ty-color-orange   { &:hover { background-color : @orange   ;  color : @white } }
        &.ty-color-gray     { &:hover { background-color : @grayA    ;  color : @white } }
        &.ty-color-darkBlue { &:hover { background-color : @darkBlue ;  color : @white } }
      }
    }
  }
}

.ty-table-none{
  tbody{
    tr{
      td{
        border:none;
        background-color: transparent;
      }
    }
  }
}
.ty-table-hove{
  tbody{
    tr{
      &:hover{
        td{
          background-color:#e3f1ed;
          border:1px solid #b7dbd1;
        }
      }
    }
  }
}
/*  text-align  */
.text-right  { text-align:right;  }
.text-left   { text-align:left;   }
.text-center { text-align:center; }
.ty-left     { float: left;       }
.ty-right    { float: right;      }
.clr         { clear:both;        }

/*  process  */
.ty-process{
  position:relative;
  margin:20px 0 0 0 ;
  .ty-process-ttl{
    position:absolute;
    top:-12px;
    left: 15px;
  }
  .ty-process-container{
    margin-left:35px;
    border-left:1px solid #d7d7d7;
    margin-top:8px;
    min-height:100px;
    padding-top:15px;
    .ty-process-item{
      padding:@process-item-padding-top-bottom 15px;
      position:relative;
      line-height:@process-item-lineHeight;
      font-size:12px;
      p{
        margin:0;
        .dot,.dot-no,.dot-wait{
          width:@process-dot-w;
          height:@process-dot-w;
          border-radius:@process-dot-w;
          position:absolute;
          left:-@process-dot-w/2;
          top:@process-item-padding-top-bottom + @process-item-lineHeight / 2 - @process-dot-w ;
        }
        .dot      {  background-color: @green;  }
        .dot-no   {  background-color: @red;    }
        .dot-wait {  background-color: @grayC;  }
      }
      article{
        word-wrap:break-word;
      }
    }
    .ty-process-wait{  color: @grayC;  }
  }
}
/*   bounce    */

.bounce{
  position:absolute;
  display:none;
  background: rgba(100,100,100,0.7);
  width: 100%;
  z-index:9999;
  min-height:100%;
}
.bonceContainer{
  display:none;
  margin:200px auto 100px;
  background:@color-bg;
  width:400px;
  border-radius:5px;
  .bonceHead{
    border-bottom:1px solid @grayE8;
    height:40px;
    font-size:16px;
    line-height:40px;
    padding:0 15px;
    background-color:@white;
    border-radius:5px 5px 0 0;
    .bounce_close{
      width:16px;
      height:16px;
      background: url(theme/icon/close.png) no-repeat 0 -32px;
      display:inline-block;
      float:right;
      margin:10px 0;
      border-radius:11px;
      &:hover{
        cursor:pointer;
        background-color:@grayE8 ;
      }
    }
  }
  &.bounce-blue  .bonceHead{ border-bottom:1px solid @blue  ; color:@blue  ; .bounce_close{background-position:0 -32px  ; }}
  &.bounce-red   .bonceHead{ border-bottom:1px solid @red   ; color:@red   ; .bounce_close{background-position:0 -16px  ; }}
  &.bounce-orange.bonceHead{ border-bottom:1px solid @orange; color:@orange; .bounce_close{background-position:0 0      ; }}
  &.bounce-green .bonceHead{ border-bottom:1px solid @green ; color:@green ; .bounce_close{background-position:0 -48px  ; }}
  .bonceCon{
    padding:10px 15px;
    input,select,textarea{
      padding:2px 6px;
    }
    input,select{
      border:1px solid @grayE8;
      background-color: @white;
      display:inline-block;
      height:30px;
      line-height:30px;
      &:focus{
        border:1px solid @grayC;
      }
    }
    textarea{
      border:1px solid @grayE8;
      background-color: @white;
      display:inline-block;
      line-height:28px;
      width: 182px;
      &:focus{
        border:1px solid @grayC;
      }
    }
  }
  .bonceFoot{
    background-color: @color-bg;
    text-align:right;
    padding:10px 15px 20px;
    border-radius:0 0 5px 5px;
  }
}


/*   Alert    */

.ty-alert{
  padding: 8px 16px;
  margin: 0;
  box-sizing: border-box;
  border-radius: 2px;
  position: relative;
  background-color: #fff;
  overflow: hidden;
  opacity: 1;
  display: flex;
  align-items: center;
  transition: opacity .2s;
  margin-bottom: 8px;
}
.ty-alert-success{
  background-color: #f0f9eb;
  color:#67c23a;
}

.ty-alert-info{
  background-color: #f4f4f5;
  color:#909399;
}

.ty-alert-warning{
  background-color: #fdf6ec;
  color:#e6a23c;
}

.ty-alert-error{
  background-color: #fef0f0;
  color:#f56c6c;
}

/*  竖型目录树  */
//.ty-colFileTree{
//  float: left;
//  min-width: @fileTree-min-w;
//  min-height: @fileTree-min-h;
//  background-color: @color-fileTree-bg;
//  color: @gray3;
//  .ty-treeItem{
//    height: @fileTree-item-h;
//    line-height:@fileTree-item-h;
//    vertical-align: middle;
//    padding:0 5px;
//    cursor: default;
//    &.ty-treeItemActive{
//      background-color: @fileTree-item-active-bg;
//    }
//    i{
//      vertical-align: top;
//      color: #333;
//      margin: 8px 0 0 3px;
//      width: 15px;
//      height: 15px;
//      text-align: center;
//      &.fa-folder,&.fa-file{
//        color: @blue;
//        margin-right: 5px
//      }
//    }
//  }
//  .level2,.level3,.level4,.level5{
//    margin-left: @fileTree-item-level-marginLeft;
//  }
//  .ty-fa{
//    width: 15px;
//    height: 15px;
//    display: inline-block
//  }
//}
//.ty-fileContent{
//  float: left;
//  width: 680px;
//  padding:0 20px;
//  .ty-panelHeader{
//    height: 50px ;
//    border-bottom:1px solid @green;
//    margin-bottom: 15px;
//    h3{
//      margin: 0;
//      font-size: 16px;
//      font-weight: 500;
//      color: @green;
//      line-height: 50px;
//    }
//  }
//}
//
//.ty-fileItem{
//  width: @fileItem-w;
//  height: @fileItem-h;
//  background-color: @white;
//  padding:12px 16px;
//  margin-bottom:8px;
//  cursor: pointer;
//  &:hover{
//    background-color: @color-fileItem-hover;
//  }
//  &.ty-fileItemActive{
//    box-shadow: 0 0 3px @color-fileItem-shadow;
//  }
//  .ty-fileType{
//    float: left;
//    width: 30px;
//    height: 40px;
//    margin-right:20px;
//    &.ty-fileDoc  { background: @fileItem-fileType-bg-doc  ; background-size:30px }
//    &.ty-fileXls  { background: @fileItem-fileType-bg-xls  ; background-size:30px }
//    &.ty-filePpt  { background: @fileItem-fileType-bg-ppt  ; background-size:30px }
//    &.ty-fileRar  { background: @fileItem-fileType-bg-rar  ; background-size:30px }
//    &.ty-filePdf  { background: @fileItem-fileType-bg-pdf  ; background-size:30px }
//    &.ty-fileJpg  { background: @fileItem-fileType-bg-jpg  ; background-size:30px }
//    &.ty-fileOther{ background: @fileItem-fileType-bg-other; background-size:30px }
//  }
//  .ty-fileInfo{
//    height: @fileItem-fileInfo-h;
//    .ty-fileDetail{
//      color: @grayA;
//      font-size: 13px;
//      font-weight: 300;
//      margin-top:2px;
//    }
//  }
//  .ty-fileHandle{
//    span,a,a:visited{
//      display: block;
//      font-size:12px;
//      padding: 2px 10px;
//      border-radius:@button-radius;
//      font-weight:bold;
//      cursor:pointer
//    }
//  }
//}
//.ty-filePreview{
//  float: left;
//  width: @filePreview-w;
//  padding: @filePreview-padding;
//  background-color: @filePreview-bg;
//  color: @gray6;
//  .ty-json{
//    width: 100%;
//    overflow: hidden;
//    margin: 10px;
//    .ty-key{
//      float: left;  width: 40%;
//    }
//    .ty-val{
//      float: left;  width: 60%;
//    }
//  }
//  hr{
//    border-top: 1px solid @gray9;  margin: 20px 0;
//  }
//}

/*  隐藏  */
.hd{  display: none}
.vh{  visibility:hidden; }

/*select*/
.ty-select{ display:inline-block; position:relative; cursor:default;      }
.ty-opTTL{  min-width:70px; height:30px; width: 100%;       }
.ty-opTTL>option:first-child{ border:none; background-color:#eee; border-radius:5px 0 0 5px; height:30px; line-height:25px; padding:0 0 0  15px; width:100%; display:block; text-align:left ;     }
.ty-down{ display:inline-block; width:25px; height:30px; position:absolute; left:100%;top:0; background:#48cfad url(icon/ty-down.png)  no-repeat center center !important; border-radius:0 5px 5px 0;       }
.ty-up{ display:inline-block; width:25px; height:30px; position:absolute; left:100%;top:0; background:#48cfad url(icon/ty-down.png)  no-repeat center center !important;  border-radius:5px 0 0 5px;
  transform:rotate(180deg);
  -ms-transform:rotate(7deg); 	/* IE 9 */
  -moz-transform:rotate(180deg); 	/* Firefox */
  -webkit-transform:rotate(180deg); /* Safari 和 Chrome */
  -o-transform:rotate(180deg); 	      }
/*.ty-down{ height:4px;width:8px; background:; display:inline-block;  margin:13px 8px ;         }*/
.ty-opItems{ position:absolute; z-index:9999; background:#fff; top:30px; border:1px solid #ddd; border-top:none;  display:none; width:100%; max-height:200px; overflow-y: auto; line-height:30px;           }
.ty-opItem{ padding-left: 10px;  height:25px; overflow:hidden;display:block; background-color:#fff;            }
.ty-opItem:hover, .ty-select .ty-opItems .ty-active{ background-color:#48cfad; color:#fff;    }
.ty-select-bg .ty-opTTL>option:first-child{  background-color:#f0f8ff;   }

.ty-form-checkbox,.ty-form-checkbox *,.ty-form-radio,.ty-form-radio{
  display: inline-block;
  vertical-align: baseline;
}
.ty-form-checkbox{
  height: auto !important;
  line-height: normal !important;
  border: none !important;
  margin-right: 0;
  padding-right: 0;
  background: 0 0;
  i{
    position: absolute;
    right: 0;
    width: 30px;
    color: #fff;
    font-size: 20px;
    text-align: center;
  }
  &[skin="green"]{
    i{
      position: relative;
      top: 0;
      width: 16px;
      height: 16px;
      line-height: 16px;
      border: 1px solid #d2d2d2;
      font-size: 12px;
      border-radius: 2px;
      cursor: pointer;
      vertical-align: middle;
      background-color: #fff;
      -webkit-transition: .1s linear;
      transition: .1s linear;
    }
  }
}
.ty-form-checked{
  &[skin="green"]{
    i{
      border-color: #48cfad;
      background-color: #48cfad;
      color: #fff;
    }
    &.ty-form-disabled{
      i{
        border-color: #d2d2d2;
        background-color: #d2d2d2;
        color: #fff;
        cursor:not-allowed
      }
    }
  }
}
.ty-form-disabled {
  &[skin="green"] {
    i {
      border-color: #d2d2d2;
      background-color: #d2d2d2;
      color: #d2d2d2;
      cursor: not-allowed
    }
  }
}
.ty-form-checkbox {
  span{
    padding: 0 10px;
    height: 100%;
    font-size: 14px;
    background-color: #d2d2d2;
    color: #fff;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  &[skin="green"] {
    span {
      float: right;
      padding-right: 15px;
      line-height: 18px;
      background: 0 0;
      color: #666;
    }
  }
}
.ty-checkbox-disabled{
  &[skin="green"] {
    color: #aaa;
    &:hover {
      color: #d2d2d2 !important;
      cursor: not-allowed !important;
    }
  }
}

/*dropdown Container 下拉框   */
.ty-dropdown{
  position:relative;
  display:inline-block;
}
.ty-dropdownBtn{
  display:inline-block;
  height:35px;
}
.ty-dropdownCon{
  display:none;
  position:absolute;
  top: 45px;
  background:#f0f8ff;
  box-shadow: 3px 3px 3px #ccc;
  border:1px solid #48cfad;
}
/* arrow 向上小箭头 */
.ty-dropdown .ty-dropdownCon .ty-trigl-1 , .ty-tip .ty-tipcon .ty-trigl-1
{  padding:0 ; margin:0 ; width:0;height:0; border-width:0 10px 10px; border-style: solid ; border-color:transparent transparent #48cfad !important;
  position:relative;top: -10px; left: 35px;}
.ty-dropdown .ty-dropdownCon .ty-trigl-1 span , .ty-tip .ty-tipcon .ty-trigl-1 span
{ padding:0 ; margin:0 ; display:block; width:0; height:0; border-width: 0 8px 8px; border-style:solid; border-color:transparent transparent #f0f8ff !important;
  position:absolute; top: 3px; left: -7px; }
.ty-tip{ position:fixed; z-index:99999; display:none;  }
.ty-tip .ty-tipcon{ position:absolute; top: 45px; background:#f0f8ff; box-shadow: 3px 3px 3px #ccc; border:1px solid #48cfad; width:350px;   }

.page-sidebar .page-sidebar-menu > li.active.open > a, .page-sidebar .page-sidebar-menu > li.active > a, .page-sidebar-closed.page-sidebar-fixed .page-sidebar:hover .page-sidebar-menu > li.active.open > a, .page-sidebar-closed.page-sidebar-fixed .page-sidebar:hover .page-sidebar-menu > li.active > a {
  background: lighten(@blue,8%);
  border-top-color: transparent;
  color: #fff;
}
.page-sidebar .page-sidebar-menu > li.active.open > a:hover, .page-sidebar .page-sidebar-menu > li.active > a:hover, .page-sidebar-closed.page-sidebar-fixed .page-sidebar:hover .page-sidebar-menu > li.active.open > a:hover, .page-sidebar-closed.page-sidebar-fixed .page-sidebar:hover .page-sidebar-menu > li.active > a:hover {
  background: @blue;
}
.page-sidebar {
  padding-top:20px;
}