package cn.sphd.miners.modules.personal.entity;

import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;

/**
 * Created by Administrator on 2017/11/29.
 * 人事 家庭成员
 */
@Entity
@Table(name = "t_personnel_folks")
public class PersonnelFolks implements Serializable{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "user" ,insertable = true)
    private Integer userId;//用户id

    @Column(name = "name",length = 100)
    private String name;//成员姓名

    @Column(name = "age")
    private Integer age;//年龄

    @Column(name = "gender",length = 1)
    private String gender;//性别 1-男   0-女

    @Column(name = "relation",length = 100)
    private String relation;// 与本人关系

    @Column(name = "memo",length = 255)
    private String memo;//说明

    @Column(name="offer")
    private Integer offer;//招聘id

    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.LAZY)
    @JoinColumn(name="user", referencedColumnName = "userID" , nullable=true , unique=false , insertable=false, updatable=false)
    private User user;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getRelation() {
        return relation;
    }

    public void setRelation(String relation) {
        this.relation = relation;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Integer getOffer() {
        return offer;
    }

    public void setOffer(Integer offer) {
        this.offer = offer;
    }
}
