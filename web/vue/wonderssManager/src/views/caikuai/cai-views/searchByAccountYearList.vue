<!-- 搜索 年 月列表  -->
<template>
  <div class="head">
    <div style="width: 100%">
      <PageHeader title="财会" :showBack="true" :showClose="true" @mycloseFun="closeFun"></PageHeader>
    </div>
  </div>
  <p class="margin-10-15">{{ todayTime }}</p>
  <div class="hr_gray"></div>
  <div v-if="fid && fid!='null'">
    <p class="margin-10-15">以下展示为{{ bankName }}的数据</p>
    <div class="hr_gray"></div>
  </div>
  <div class="current-balance-1 margin-10-15">
    <div>
      <p class="ty-color-orange">{{ bankName }}</p>
      <p>本期余额</p>
    </div>
    <span>{{ allBalance }}</span>
  </div>
  <div class="hr_gray"></div>
  <div class="flex">
    <div class="balance1" v-for="(item) in bala_list" :key="item.id">
      <div>
        <img :src="item.sr" alt="">
      </div>
      <div>
        <span :class="item.style">{{ item.da }} <br> {{ item.money }}</span>
      </div>
    </div>
  </div>
  <div class="hr_gray"></div>
  <div>
    <ul class="val_list">
      <li v-for="(item, index) in accountList" :key="index" class="ta-list" >
        <div @click="goDay(item)">
          <span v-html="item.name"></span>
          <div class="current-balance-1">
            <div class="ty-color-green">
              <p>收入</p>
              <p>{{ item.income }}</p>
            </div>
          </div>
          <div class="current-balance-1 ty-color-red">
            <div>
              <p>支出</p>
              <p>{{ item.expend }}</p>
            </div>
          </div>
          <span>
          <el-icon><arrow-right /></el-icon>
        </span>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import finance from '@/mixins/finance.js'
export default {
  name: "currentAccountDate",
  mixins: [finance],
  data() {
    return {
      accID:'',
      fid:'',
      bankName:'',
      todayTime:'今天是XXXX年X月XX日 星期X',
      allBalance:0,
      bala_list: [
      ],
      accountList: []
    }
  },
  mounted() {
    this.getAccountList()
  },
  methods: {
    getAccountList(){
      let org = this.auth.getOrg()
      let info = localStorage.getItem('searchByAccountYearList')
      info = JSON.parse(info)
      let beginDate = info.beginDate
      let endDate = info.endDate
      let fid = info.fid
      this.fid = fid
      if(beginDate && endDate){
        this.todayTime = beginDate + ' ~ ' + endDate
        let params = { oid:org.id , beginDate:beginDate, endDate:endDate }
        if(fid){
          params.fid = fid
        }

        this.financeApi.getAccountMonthOrDayApp(params).then(res1=>{
          let res = res1.data.data
          console.log('getAccountMonthOrDayApp res=', res)
          this.bankName = res.bankName
          this.allBalance = this.formatCurrency(res.allBalance )
          this.bala_list = [
            {id: 0, da: "上期余额", money: this.formatCurrency(res.allPreviousBalance ) , sr: this.balance1 },
            {id: 1, da: "本期收入", money: this.formatCurrency(res.allCredit ) , sr:  this.balance2 ,style: "ty-color-green"},
            {id: 2, da: "本期支出", money: this.formatCurrency(res.allDebit) , sr: this.balance3 ,style: "ty-color-red"},
          ]

          this.accountList = []
          let accountPeriodList = res.accountPeriodList || []
          accountPeriodList.forEach(item=>{
            let iihk = {
              name:  '自' + (new Date(item.beginDate).format('yyyy-MM-dd')) + '<br/>至' + (new Date(item.endDate).format('yyyy-MM-dd'))  ,
              income: this.formatCurrency(item.credit) ,
              expend: this.formatCurrency(item.debit) ,
              ...item
            }
            this.accountList.push(iihk)
          })

        }).catch(err=>{
          console.log('err=', err)
          this.$message.error('链接失败，请重试！')
        })
      }else{
        this.$message.error('没有具体日期哦！')
      }

    },
    goDay(item){
      if(item.name){
        let info = {
          beginDate: new Date(item.beginDate).format('yyyy-MM-dd') ,
          endDate: new Date(item.endDate).format('yyyy-MM-dd'),
          fid : ''
        }
        localStorage.setItem('searchYearDur2', JSON.stringify(info))
        let path = `/searchYearDur2`
        this.$router.push({ path:path })
      }else{
        this.$message.error('没有具体日期哦！')
      }
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../../style/account.scss";
@import "../../../style/common.scss";
.centerSp{ text-align: center; }
</style>