package cn.sphd.miners.modules.investigate.utils;

import cn.sphd.miners.modules.investigate.dto.*;
import cn.sphd.miners.modules.investigate.entity.InvestigateQuestionKey;
import cn.sphd.miners.modules.investigate.entity.InvestigateSubject;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Date;

public class ExportExcel {
    public static void getValue(RespShowQuestion respShowQuestion, InvestigateSubject investigateSubject, HttpServletResponse response){
        int width= respShowQuestion.getRespInvestigatePublishShowList().size()+1;
        if(width<=1)
            width=2;
        try{
            //1.创建工作簿
            HSSFWorkbook workbook = new HSSFWorkbook();
            //1.1创建合并单元格对象
            CellRangeAddress callRangeAddress = new CellRangeAddress(0,0,0,width-1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress1 = new CellRangeAddress(1,1,0,width-1);//起始行,结束行,起始列,结束列
            //班组与时间start
            CellRangeAddress callRangeAddress20 = new CellRangeAddress(2,2,0,width-2);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress21 = new CellRangeAddress(2,2,width-2,width-1);//起始行,结束行,起始列,结束列
            //班组与时间end

            //标题
            CellRangeAddress callRangeAddress31 = new CellRangeAddress(3,3,0,0);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress32 = new CellRangeAddress(3,3,1,1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress33 = new CellRangeAddress(3,3,2,2);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress34 = new CellRangeAddress(3,3,3,3);//起始行,结束行,起始列,结束列

            //页眉
            HSSFCellStyle headStyle = createCellStyle(workbook,(short)13,true,true);
            //标题
            HSSFCellStyle erStyle = createCellStyle(workbook,(short)11,true,true);
            //班组和时间
            HSSFCellStyle sanStyle = createCellStyle(workbook,(short)10,false,false);
            //标题样式
            HSSFCellStyle colStyle = createCellStyle(workbook,(short)10,true,true);
            //内容样式
            HSSFCellStyle cellStyle = createCellStyle(workbook,(short)10,false,true);
            //2.创建工作表
            HSSFSheet sheet = workbook.createSheet("回收的问卷");
            //2.1加载合并单元格对象
            sheet.addMergedRegion(callRangeAddress);
            sheet.addMergedRegion(callRangeAddress1);
            if(width-1>1)
                sheet.addMergedRegion(callRangeAddress20);

            //设置默认列宽
            sheet.setDefaultColumnWidth(23);
            //3.创建行
            //3.1创建头标题行;并且设置头标题
            HSSFRow row = sheet.createRow(0);
            HSSFCell cell = row.createCell(0);
            //加载单元格样式
            cell.setCellStyle(headStyle);
            cell.setCellValue(investigateSubject.getHeader());

            HSSFRow rower = sheet.createRow(1);
            HSSFCell celler = rower.createCell(0);
            //加载单元格样式
            celler.setCellStyle(erStyle);
            celler.setCellValue(investigateSubject.getTitle());

            HSSFRow rowsan = sheet.createRow(2);
            HSSFCell cellsan = rowsan.createCell(0);

            HSSFCell cellsan2 = rowsan.createCell(width-1);
            //加载单元格样式
            cellsan.setCellStyle(sanStyle);
            cellsan.setCellValue("问卷已回收"+respShowQuestion.getRespInvestigateObjectList().size()+"份，具体如下：");
            cellsan2.setCellStyle(sanStyle);
            SimpleDateFormat ft = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
            cellsan2.setCellValue("导出时间:"+ft.format(new Date()));

            //3.2创建列标题;并且设置列标题
            HSSFRow row2 = sheet.createRow(3);
            HSSFCell cell22 = row2.createCell(0);
            //加载单元格样式
            cell22.setCellStyle(colStyle);
            cell22.setCellValue("提交时间");
            for( int i=0;i<respShowQuestion.getRespInvestigatePublishShowList().size();i++)
            {
                HSSFCell cell21 = row2.createCell(i+1);
                //加载单元格样式
                cell21.setCellStyle(colStyle);
                cell21.setCellValue(respShowQuestion.getRespInvestigatePublishShowList().get(i).getContent());
            }
            int sheetSize=65535;
            double sheetNum=Math.ceil((respShowQuestion.getRespInvestigateObjectList().size()+4)/new Integer(sheetSize).doubleValue());
            //4.操作单元格;将用户列表写入excel
            if(respShowQuestion.getRespInvestigateObjectList() != null)
            {
                for(int j=0;j<respShowQuestion.getRespInvestigateObjectList().size();j++) {
                    if (j >= 65531)
                        break;
                    int x=0;
                    //创建数据行,前面有两行,头标题行和列标题行
                    HSSFRow row3 = sheet.createRow(j + 4);
                    HSSFCell cell0 = row3.createCell(x);
                    cell0.setCellStyle(cellStyle);
                    cell0.setCellValue(ft.format(respShowQuestion.getRespInvestigateObjectList().get(j).getCreateDate()));

                    //根据问题对应答案
                    for (int q=0;q< respShowQuestion.getRespInvestigatePublishShowList().size();q++)
                    {
                        for(RespInvestigateAnswer investigateAnswer:respShowQuestion.getRespInvestigatePublishShowList().get(q).getInvestigateAnswerList())
                        {
                            if(investigateAnswer.getQuestion().intValue()==respShowQuestion.getRespInvestigatePublishShowList().get(q).getQuestion().intValue())
                            {
                                if(investigateAnswer.getObjective().intValue()==respShowQuestion.getRespInvestigateObjectList().get(j).getId().intValue()) {
                                    HSSFCell cell1 = row3.createCell(q + 1);
                                    cell1.setCellStyle(cellStyle);
                                    cell1.setCellValue(investigateAnswer.getContent());
                                }
                            }
                        }

                    }
                }
//                if(sheetNum>1) {
//                    for (int i = 1; i < sheetNum; i++)
//                    {
//                        HSSFSheet sheet1=workbook.createSheet("回收的问卷"+(i));
//                        int x=0;
//                        for(int j=65535*i-4;j<respShowQuestion.getRespInvestigateObjectList().size();j++)
//                        {
//
//                            if(j>=65535*(i+1)-4)
//                                break;
//                            //创建数据行,前面有两行,头标题行和列标题行
//                            HSSFRow row3 = sheet1.createRow(x);
//                            x++;
//                            HSSFCell cell0 = row3.createCell(0);
//                            cell0.setCellStyle(cellStyle);
//                            cell0.setCellValue(ft.format(respShowQuestion.getRespInvestigateObjectList().get(j).getCreateDate()));
//
//                            HSSFCell cell1 = row3.createCell(1);
//                            cell1.setCellStyle(cellStyle);
//                            cell1.setCellValue(respShowQuestion.getRespInvestigateObjectList().get(j).getShowValue1());
//
//                            HSSFCell cell2 = row3.createCell(2);
//                            cell2.setCellStyle(cellStyle);
//                            cell2.setCellValue(respShowQuestion.getRespInvestigateObjectList().get(j).getShowValue2());
//
//                            HSSFCell cell3 = row3.createCell(3);
//                            cell3.setCellStyle(cellStyle);
//                            cell3.setCellValue(respShowQuestion.getRespInvestigateObjectList().get(j).getShowValue3());
//                        }
//                    }
//                }
            }
            //5.输出
            //设置默认文件名为当前时间：年月日时分秒
            String fileName=new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()).toString();

            //设置response头信息
            response.reset();
            response.setContentType("application/vnd.ms-excel");        //改成输出excel文件
            response.setHeader("Content-disposition","attachment; filename="+fileName+".xls" );
            OutputStream out=response.getOutputStream();
            workbook.write(out);
            out.close();
        }catch(Exception e)
        {
            e.printStackTrace();
        }
    }

    /**
     *
     * @param workbook
     * @param fontsize
     * @return 单元格样式
     */
    private static HSSFCellStyle createCellStyle(HSSFWorkbook workbook, short fontsize,boolean flag,boolean flag1) {
        // TODO Auto-generated method stub
        HSSFCellStyle style = workbook.createCellStyle();
        //是否水平居中
        if(flag1){
//            style.setAlignment(HSSFCellStyle.ALIGN_CENTER);//水平居中
            style.setAlignment(HorizontalAlignment.CENTER);//水平居中
        }

//        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);//垂直居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);//垂直居中
        //创建字体
        HSSFFont font = workbook.createFont();
        //是否加粗字体
        if(flag){
//            font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
            font.setBold(true);
        }
        font.setFontHeightInPoints(fontsize);
        //加载字体
        style.setFont(font);
        return style;
    }

    public static void getValue1(RespInvestigateQuestionStatistics respInvestigateQuestionStatistics, InvestigateSubject investigateSubject, HttpServletResponse response){
        int width= 3;
        try{
            //1.创建工作簿
            HSSFWorkbook workbook = new HSSFWorkbook();
            //1.1创建合并单元格对象
            CellRangeAddress callRangeAddress = new CellRangeAddress(0,0,0,width-1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress1 = new CellRangeAddress(1,1,0,width-1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress2 = new CellRangeAddress(2,2,0,width-1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress3 = new CellRangeAddress(3,3,0,width-1);//起始行,结束行,起始列,结束列
            //班组与时间start
            CellRangeAddress callRangeAddress20 = new CellRangeAddress(4,4,0,width-2);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress21 = new CellRangeAddress(4,4,width-2,width-1);//起始行,结束行,起始列,结束列
            //班组与时间end

            //标题

            //页眉
            HSSFCellStyle headStyle = createCellStyle(workbook,(short)13,true,true);
            //标题
            HSSFCellStyle erStyle = createCellStyle(workbook,(short)11,true,false);
            //班组和时间
            HSSFCellStyle sanStyle = createCellStyle(workbook,(short)10,false,false);
            //标题样式
            HSSFCellStyle colStyle = createCellStyle(workbook,(short)10,true,true);
            //内容样式
            HSSFCellStyle cellStyle = createCellStyle(workbook,(short)10,false,false);
            //2.创建工作表
            HSSFSheet sheet = workbook.createSheet("调查分析");
            //2.1加载合并单元格对象
            sheet.addMergedRegion(callRangeAddress);
            sheet.addMergedRegion(callRangeAddress1);
            sheet.addMergedRegion(callRangeAddress2);
            sheet.addMergedRegion(callRangeAddress3);
            if(width-1>1)
                sheet.addMergedRegion(callRangeAddress20);

            //设置默认列宽
            sheet.setDefaultColumnWidth(23);
            //3.创建行
            //3.1创建头标题行;并且设置头标题
            HSSFRow row = sheet.createRow(0);
            HSSFCell cell = row.createCell(0);
            //加载单元格样式
            cell.setCellStyle(headStyle);
            cell.setCellValue(investigateSubject.getHeader());

            HSSFRow rower = sheet.createRow(1);
            HSSFCell celler = rower.createCell(0);
            //加载单元格样式
            celler.setCellStyle(headStyle);
            celler.setCellValue(investigateSubject.getTitle());

            HSSFRow rowsan = sheet.createRow(2);
            HSSFCell cellsan = rowsan.createCell(0);
            SimpleDateFormat ft = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
            //加载单元格样式
            cellsan.setCellStyle(erStyle);
            cellsan.setCellValue("本次调查共有"+respInvestigateQuestionStatistics.getSum()+"道题，截至"+ft.format(new Date())+"，共回收"+respInvestigateQuestionStatistics.getObjectSum()+"份");

            HSSFRow rowsan1 = sheet.createRow(3);
            HSSFCell cellsan1 = rowsan1.createCell(0);
            cellsan1.setCellStyle(erStyle);
            cellsan1.setCellValue("常规问答题与系统中特殊型式的问答题共"+respInvestigateQuestionStatistics.getEssayQuestionCount()+"道，情况如下");

            HSSFRow rowsan2 = sheet.createRow(4);
            HSSFCell cellsan2 = rowsan2.createCell(width-1);
            cellsan2.setCellStyle(sanStyle);
            cellsan2.setCellValue("导出时间:"+ft.format(new Date()));

            //3.2创建列标题;并且设置列标题

//            cell22.setCellValue("提交时间");
            int sheetSize=65535;
            double sheetNum=Math.ceil((respInvestigateQuestionStatistics.getRespInvestigateQuestionList().size()*2+5)/new Integer(sheetSize).doubleValue());
            //4.操作单元格;将用户列表写入excel
            if(respInvestigateQuestionStatistics.getRespInvestigateQuestionList() != null)
            {
                for(int j=0;j<respInvestigateQuestionStatistics.getRespInvestigateQuestionList().size();j++)
                {
                    if(j*2>=65531)
                        break;
                    CellRangeAddress callRangeAddress33 = new CellRangeAddress(j*2+5,j*2+5,0,width-1);
                    CellRangeAddress callRangeAddress43= new CellRangeAddress(j*2+6,j*2+6,0,width-1);
                    sheet.addMergedRegion(callRangeAddress33);
                    sheet.addMergedRegion(callRangeAddress43);
                    //创建数据行,前面有两行,头标题行和列标题行
                    HSSFRow row3 = sheet.createRow(j*2+5);
                    HSSFCell cell0 = row3.createCell(0);
                    cell0.setCellStyle(cellStyle);
                    cell0.setCellValue(respInvestigateQuestionStatistics.getRespInvestigateQuestionList().get(j).getOrders()+"."+respInvestigateQuestionStatistics.getRespInvestigateQuestionList().get(j).getContent());

                    HSSFRow row4 = sheet.createRow(j*2+6);
                    HSSFCell cell04 = row4.createCell(0);
                    cell04.setCellStyle(cellStyle);
                    cell04.setCellValue("回收的问卷中，"+respInvestigateQuestionStatistics.getRespInvestigateQuestionList().get(j).getAnswerSum()+"份回答了本题");

                }
                if(sheetNum>1) {
                    for (int i = 1; i < sheetNum; i++)
                    {
                        HSSFSheet sheet1=workbook.createSheet("回收的问卷"+(i));
                        int x=0;
                        for(int j=(65535*i-5)/2;j<respInvestigateQuestionStatistics.getRespInvestigateQuestionList().size();j++)
                        {

                            if(j>=(65535*i-5)/2)
                                break;
                            //创建数据行,前面有两行,头标题行和列标题行
                            HSSFRow row3 = sheet1.createRow(x);
                            x++;
                        }
                    }
                }
            }
            //5.输出
            //设置默认文件名为当前时间：年月日时分秒
            String fileName=new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()).toString();

            //设置response头信息
            response.reset();
            response.setContentType("application/vnd.ms-excel");        //改成输出excel文件
            response.setHeader("Content-disposition","attachment; filename="+fileName+".xls" );
            OutputStream out=response.getOutputStream();
            workbook.write(out);
            out.close();
        }catch(Exception e)
        {
            e.printStackTrace();
        }
    }
    //单选题
    public static void getValue2(RespInvestigateQuestionStatistics respInvestigateQuestionStatistics, InvestigateSubject investigateSubject, HttpServletResponse response){
        int width= 3;
        try{
            //1.创建工作簿
            HSSFWorkbook workbook = new HSSFWorkbook();
            //1.1创建合并单元格对象
            CellRangeAddress callRangeAddress = new CellRangeAddress(0,0,0,width-1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress1 = new CellRangeAddress(1,1,0,width-1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress2 = new CellRangeAddress(2,2,0,width-1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress3 = new CellRangeAddress(3,3,0,width-1);//起始行,结束行,起始列,结束列
            //班组与时间start
            CellRangeAddress callRangeAddress20 = new CellRangeAddress(4,4,0,width-2);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress21 = new CellRangeAddress(4,4,width-2,width-1);//起始行,结束行,起始列,结束列
            //班组与时间end

            //页眉
            HSSFCellStyle headStyle = createCellStyle(workbook,(short)13,true,true);
            //标题
            HSSFCellStyle erStyle = createCellStyle(workbook,(short)11,true,false);
            //班组和时间
            HSSFCellStyle sanStyle = createCellStyle(workbook,(short)10,false,false);
            //标题样式
            HSSFCellStyle colStyle = createCellStyle(workbook,(short)10,true,true);
            //内容样式
            HSSFCellStyle cellStyle = createCellStyle(workbook,(short)10,false,false);
            //2.创建工作表
            HSSFSheet sheet = workbook.createSheet("调查分析");
            //2.1加载合并单元格对象
            sheet.addMergedRegion(callRangeAddress);
            sheet.addMergedRegion(callRangeAddress1);
            sheet.addMergedRegion(callRangeAddress2);
            sheet.addMergedRegion(callRangeAddress3);
            if(width-1>1)
                sheet.addMergedRegion(callRangeAddress20);

            //设置默认列宽
            sheet.setDefaultColumnWidth(23);
            //3.创建行
            //3.1创建头标题行;并且设置头标题
            HSSFRow row = sheet.createRow(0);
            HSSFCell cell = row.createCell(0);
            //加载单元格样式
            cell.setCellStyle(headStyle);
            cell.setCellValue(investigateSubject.getHeader());

            HSSFRow rower = sheet.createRow(1);
            HSSFCell celler = rower.createCell(0);
            //加载单元格样式
            celler.setCellStyle(headStyle);
            celler.setCellValue(investigateSubject.getTitle());

            HSSFRow rowsan = sheet.createRow(2);
            HSSFCell cellsan = rowsan.createCell(0);
            SimpleDateFormat ft = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
            //加载单元格样式
            cellsan.setCellStyle(erStyle);
            cellsan.setCellValue("本次调查共有"+respInvestigateQuestionStatistics.getSum()+"道题，截至"+ft.format(new Date())+"，共回收"+respInvestigateQuestionStatistics.getObjectSum()+"份");

            HSSFRow rowsan1 = sheet.createRow(3);
            HSSFCell cellsan1 = rowsan1.createCell(0);
            cellsan1.setCellStyle(erStyle);
            cellsan1.setCellValue("常规单选题与正误判断题共"+respInvestigateQuestionStatistics.getSingleChoiceCount()+"道，情况如下");

            HSSFRow rowsan2 = sheet.createRow(4);
            HSSFCell cellsan2 = rowsan2.createCell(width-1);
            cellsan2.setCellStyle(sanStyle);
            cellsan2.setCellValue("导出时间:"+ft.format(new Date()));

            int x=5;
            //4.操作单元格;将用户列表写入excel
            if(respInvestigateQuestionStatistics.getRespInvestigateQuestionList() != null)
            {
                for(int j=0;j<respInvestigateQuestionStatistics.getRespInvestigateQuestionList().size();j++)
                {
                    if(j>=65531)
                        break;
                    CellRangeAddress callRangeAddress33 = new CellRangeAddress(x,x,0,width-1);
                    CellRangeAddress callRangeAddress43= new CellRangeAddress(x+1,x+1,0,width-1);
                    sheet.addMergedRegion(callRangeAddress33);
                    sheet.addMergedRegion(callRangeAddress43);
                    HSSFRow row32 = sheet.createRow(x);
                    HSSFCell cell32 = row32.createCell(0);
                    cell32.setCellStyle(cellStyle);
                    cell32.setCellValue(respInvestigateQuestionStatistics.getRespInvestigateQuestionList().get(j).getOrders()+"."+respInvestigateQuestionStatistics.getRespInvestigateQuestionList().get(j).getContent());
                    x++;
                    HSSFRow row42 = sheet.createRow(x);
                    HSSFCell cell42 = row42.createCell(0);
                    cell42.setCellStyle(cellStyle);
                    cell42.setCellValue("回收的问卷中，"+respInvestigateQuestionStatistics.getRespInvestigateQuestionList().get(j).getAnswerSum()+"份回答了本题，情况如下");
                    x++;

                    HSSFRow row2 = sheet.createRow(x);
                    HSSFCell cell22 = row2.createCell(0);
                    cell22.setCellStyle(cellStyle);
                    cell22.setCellValue("选项");

                    HSSFCell cell21 = row2.createCell(1);
                    cell21.setCellStyle(cellStyle);
                    cell21.setCellValue("数量");

                    HSSFCell cell23 = row2.createCell(2);
                    cell23.setCellStyle(cellStyle);
                    cell23.setCellValue("占比");
                    x++;
                    for(int n=0;n<respInvestigateQuestionStatistics.getRespInvestigateQuestionList().get(j).getRespInvestigateQuestionKeyList().size();n++)
                    {
                        HSSFRow row27 = sheet.createRow(x);
                        HSSFCell cell227 = row27.createCell(0);
                        cell227.setCellStyle(cellStyle);
                        cell227.setCellValue("("+respInvestigateQuestionStatistics.getRespInvestigateQuestionList().get(j).getRespInvestigateQuestionKeyList().get(n).getOrders()+")"+respInvestigateQuestionStatistics.getRespInvestigateQuestionList().get(j).getRespInvestigateQuestionKeyList().get(n).getContent());

                        HSSFCell cell217 = row27.createCell(1);
                        cell217.setCellStyle(cellStyle);
                        cell217.setCellValue(respInvestigateQuestionStatistics.getRespInvestigateQuestionList().get(j).getRespInvestigateQuestionKeyList().get(n).getSum());

                        HSSFCell cell237 = row27.createCell(2);
                        cell237.setCellStyle(cellStyle);
                        double number= Double.parseDouble(respInvestigateQuestionStatistics.getRespInvestigateQuestionList().get(j).getRespInvestigateQuestionKeyList().get(n).getPercentage());
                        number=number*100;
                        BigDecimal number1=new BigDecimal(Double.toString(number));
                        number1=number1.setScale(2, RoundingMode.HALF_UP);
                        cell237.setCellValue(number1.doubleValue()+"%");
                        x++;
                    }
                }
            }
            //5.输出
            //设置默认文件名为当前时间：年月日时分秒
            String fileName=new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()).toString();

            //设置response头信息
            response.reset();
            response.setContentType("application/vnd.ms-excel");        //改成输出excel文件
            response.setHeader("Content-disposition","attachment; filename="+fileName+".xls" );
            OutputStream out=response.getOutputStream();
            workbook.write(out);
            out.close();
        }catch(Exception e)
        {
            e.printStackTrace();
        }
    }
    //多选题
    public static void getValue3(RespInvestigateQuestionStatistics respInvestigateQuestionStatistics, InvestigateSubject investigateSubject, HttpServletResponse response){
        int width= 3;
        try{
            //1.创建工作簿
            HSSFWorkbook workbook = new HSSFWorkbook();
            //1.1创建合并单元格对象
            CellRangeAddress callRangeAddress = new CellRangeAddress(0,0,0,width-1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress1 = new CellRangeAddress(1,1,0,width-1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress2 = new CellRangeAddress(2,2,0,width-1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress3 = new CellRangeAddress(3,3,0,width-1);//起始行,结束行,起始列,结束列
            //班组与时间start
            CellRangeAddress callRangeAddress20 = new CellRangeAddress(4,4,0,width-2);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress21 = new CellRangeAddress(4,4,width-2,width-1);//起始行,结束行,起始列,结束列
            //班组与时间end

            //标题

            //页眉
            HSSFCellStyle headStyle = createCellStyle(workbook,(short)13,true,true);
            //标题
            HSSFCellStyle erStyle = createCellStyle(workbook,(short)11,true,false);
            //班组和时间
            HSSFCellStyle sanStyle = createCellStyle(workbook,(short)10,false,false);
            //标题样式
            HSSFCellStyle colStyle = createCellStyle(workbook,(short)10,true,true);
            //内容样式
            HSSFCellStyle cellStyle = createCellStyle(workbook,(short)10,false,false);
            //2.创建工作表
            HSSFSheet sheet = workbook.createSheet("调查分析");
            //2.1加载合并单元格对象
            sheet.addMergedRegion(callRangeAddress);
            sheet.addMergedRegion(callRangeAddress1);
            sheet.addMergedRegion(callRangeAddress2);
            sheet.addMergedRegion(callRangeAddress3);
            if(width-1>1)
                sheet.addMergedRegion(callRangeAddress20);

            //设置默认列宽
            sheet.setDefaultColumnWidth(23);
            //3.创建行
            //3.1创建头标题行;并且设置头标题
            HSSFRow row = sheet.createRow(0);
            HSSFCell cell = row.createCell(0);
            //加载单元格样式
            cell.setCellStyle(headStyle);
            cell.setCellValue(investigateSubject.getHeader());

            HSSFRow rower = sheet.createRow(1);
            HSSFCell celler = rower.createCell(0);
            //加载单元格样式
            celler.setCellStyle(headStyle);
            celler.setCellValue(investigateSubject.getTitle());

            HSSFRow rowsan = sheet.createRow(2);
            HSSFCell cellsan = rowsan.createCell(0);
            SimpleDateFormat ft = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
            //加载单元格样式
            cellsan.setCellStyle(erStyle);
            cellsan.setCellValue("本次调查共有"+respInvestigateQuestionStatistics.getSum()+"道题，截至"+ft.format(new Date())+"，共回收"+respInvestigateQuestionStatistics.getObjectSum()+"份");

            HSSFRow rowsan1 = sheet.createRow(3);
            HSSFCell cellsan1 = rowsan1.createCell(0);
            cellsan1.setCellStyle(erStyle);
            cellsan1.setCellValue("常规多选题共"+respInvestigateQuestionStatistics.getMultipleChoiceCount()+"道，各题各个选项被选择的情况统计如下");

            HSSFRow rowsan2 = sheet.createRow(4);
            HSSFCell cellsan2 = rowsan2.createCell(width-1);
            cellsan2.setCellStyle(sanStyle);
            cellsan2.setCellValue("导出时间:"+ft.format(new Date()));

            int x=5;
            //4.操作单元格;将用户列表写入excel
            if(respInvestigateQuestionStatistics.getRespInvestigateQuestionList() != null)
            {
                for(int j=0;j<respInvestigateQuestionStatistics.getRespInvestigateQuestionList().size();j++)
                {
                    if(j>=65531)
                        break;
                    CellRangeAddress callRangeAddress33 = new CellRangeAddress(x,x,0,width-1);
                    CellRangeAddress callRangeAddress43= new CellRangeAddress(x+1,x+1,0,width-1);
                    sheet.addMergedRegion(callRangeAddress33);
                    sheet.addMergedRegion(callRangeAddress43);
                    HSSFRow row32 = sheet.createRow(x);
                    HSSFCell cell32 = row32.createCell(0);
                    cell32.setCellStyle(cellStyle);
                    cell32.setCellValue(respInvestigateQuestionStatistics.getRespInvestigateQuestionList().get(j).getOrders()+"."+respInvestigateQuestionStatistics.getRespInvestigateQuestionList().get(j).getContent());
                    x++;
                    HSSFRow row42 = sheet.createRow(x);
                    HSSFCell cell42 = row42.createCell(0);
                    cell42.setCellStyle(cellStyle);
                    cell42.setCellValue("回收的问卷中，"+respInvestigateQuestionStatistics.getRespInvestigateQuestionList().get(j).getAnswerSum()+"份回答了本题，情况如下");
                    x++;

                    HSSFRow row2 = sheet.createRow(x);
                    HSSFCell cell22 = row2.createCell(0);
                    cell22.setCellStyle(cellStyle);
                    cell22.setCellValue("选项");

                    HSSFCell cell21 = row2.createCell(1);
                    cell21.setCellStyle(cellStyle);
                    cell21.setCellValue("数量");

                    HSSFCell cell23 = row2.createCell(2);
                    cell23.setCellStyle(cellStyle);
                    cell23.setCellValue("占比");
                    x++;
                    for(int n=0;n<respInvestigateQuestionStatistics.getRespInvestigateQuestionList().get(j).getRespInvestigateQuestionKeyList().size();n++)
                    {
                        HSSFRow row27 = sheet.createRow(x);
                        HSSFCell cell227 = row27.createCell(0);
                        cell227.setCellStyle(cellStyle);
                        cell227.setCellValue("("+respInvestigateQuestionStatistics.getRespInvestigateQuestionList().get(j).getRespInvestigateQuestionKeyList().get(n).getOrders()+")"+respInvestigateQuestionStatistics.getRespInvestigateQuestionList().get(j).getRespInvestigateQuestionKeyList().get(n).getContent());

                        HSSFCell cell217 = row27.createCell(1);
                        cell217.setCellStyle(cellStyle);
                        cell217.setCellValue(respInvestigateQuestionStatistics.getRespInvestigateQuestionList().get(j).getRespInvestigateQuestionKeyList().get(n).getSum());

                        HSSFCell cell237 = row27.createCell(2);
                        cell237.setCellStyle(cellStyle);
                        double number= Double.parseDouble(respInvestigateQuestionStatistics.getRespInvestigateQuestionList().get(j).getRespInvestigateQuestionKeyList().get(n).getPercentage());
                        number=number*100;
                        BigDecimal number1=new BigDecimal(Double.toString(number));
                        number1=number1.setScale(2, RoundingMode.HALF_UP);
                        cell237.setCellValue(number1+"%");
                        x++;
                    }
                }
            }
            //5.输出
            //设置默认文件名为当前时间：年月日时分秒
            String fileName=new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()).toString();

            //设置response头信息
            response.reset();
            response.setContentType("application/vnd.ms-excel");        //改成输出excel文件
            response.setHeader("Content-disposition","attachment; filename="+fileName+".xls" );
            OutputStream out=response.getOutputStream();
            workbook.write(out);
            out.close();
        }catch(Exception e)
        {
            e.printStackTrace();
        }
    }
    //常规问答题
    public static void getValue4(RespInvestigateQuestion respInvestigateQuestion, RespInvestigateQuestionStatistics respInvestigateQuestionStatistics,InvestigateSubject investigateSubject,HttpServletResponse response){
        int width= 3;
        try{
            //1.创建工作簿
            HSSFWorkbook workbook = new HSSFWorkbook();
            //1.1创建合并单元格对象
            CellRangeAddress callRangeAddress = new CellRangeAddress(0,0,0,width-1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress1 = new CellRangeAddress(1,1,0,width-1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress3 = new CellRangeAddress(3,3,0,width-1);//起始行,结束行,起始列,结束列
            //班组与时间start
            CellRangeAddress callRangeAddress20 = new CellRangeAddress(2,2,0,width-2);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress21 = new CellRangeAddress(4,4,0,width-1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress22 = new CellRangeAddress(5,5,0,width-1);//起始行,结束行,起始列,结束列
            //班组与时间end

            //标题

            //页眉
            HSSFCellStyle headStyle = createCellStyle(workbook,(short)13,true,true);
            //标题
            HSSFCellStyle erStyle = createCellStyle(workbook,(short)11,true,false);
            //班组和时间
            HSSFCellStyle sanStyle = createCellStyle(workbook,(short)10,false,false);
            //标题样式
            HSSFCellStyle colStyle = createCellStyle(workbook,(short)10,true,true);
            //内容样式
            HSSFCellStyle cellStyle = createCellStyle(workbook,(short)10,false,false);
            //2.创建工作表
            HSSFSheet sheet = workbook.createSheet("调查分析");
            //2.1加载合并单元格对象
            sheet.addMergedRegion(callRangeAddress);
            sheet.addMergedRegion(callRangeAddress1);
            sheet.addMergedRegion(callRangeAddress3);
            sheet.addMergedRegion(callRangeAddress21);
            sheet.addMergedRegion(callRangeAddress22);


            if(width-1>1)
                sheet.addMergedRegion(callRangeAddress20);

            //设置默认列宽
            sheet.setDefaultColumnWidth(23);
            //3.创建行
            //3.1创建头标题行;并且设置头标题
            HSSFRow row = sheet.createRow(0);
            HSSFCell cell = row.createCell(0);
            //加载单元格样式
            cell.setCellStyle(headStyle);
            cell.setCellValue(investigateSubject.getHeader());

            HSSFRow rower = sheet.createRow(1);
            HSSFCell celler = rower.createCell(0);
            //加载单元格样式
            celler.setCellStyle(headStyle);
            celler.setCellValue(investigateSubject.getTitle());
            SimpleDateFormat ft = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
            HSSFRow rowsan2 = sheet.createRow(2);
            HSSFCell cellsan2 = rowsan2.createCell(width-1);
            cellsan2.setCellStyle(sanStyle);
            cellsan2.setCellValue("导出时间:"+ft.format(new Date()));


            HSSFRow rowsan = sheet.createRow(3);
            HSSFCell cellsan = rowsan.createCell(0);
            cellsan.setCellStyle(erStyle);
            cellsan.setCellValue("本次调查共有"+respInvestigateQuestionStatistics.getSum()+"道题,截至"+ft.format(new Date())+"，共回收"+respInvestigateQuestionStatistics.getObjectSum()+"份");

            HSSFRow rowsan13 = sheet.createRow(4);
            HSSFCell cellsan13 = rowsan13.createCell(0);
            cellsan13.setCellStyle(cellStyle);
            cellsan13.setCellValue(respInvestigateQuestion.getOrders()+"."+respInvestigateQuestion.getContent());

            HSSFRow rowsan133 = sheet.createRow(5);
            HSSFCell cellsan133 = rowsan133.createCell(0);
            cellsan133.setCellStyle(erStyle);
            cellsan133.setCellValue("回收的问卷中，"+respInvestigateQuestion.getInvestigateAnswerList().size()+"份回答了本题，具体如下：");

            CellRangeAddress callRangeAddress33 = new CellRangeAddress(6,6,1,width-1);
            sheet.addMergedRegion(callRangeAddress33);


            HSSFRow row32 = sheet.createRow(6);
            HSSFCell cell32 = row32.createCell(0);
            cell32.setCellStyle(colStyle);
            cell32.setCellValue("提交时间");


            HSSFCell cellsan22 = row32.createCell(1);
            cellsan22.setCellStyle(colStyle);
            cellsan22.setCellValue("回答内容");

            int x=7;
            //4.操作单元格;将用户列表写入excel
            if(respInvestigateQuestion.getInvestigateAnswerList() != null)
            {
                for(int j=0;j<respInvestigateQuestion.getInvestigateAnswerList().size();j++)
                {
                    if(j>=65531)
                        break;
                    HSSFRow row323 = sheet.createRow(x);
                    HSSFCell cell323 = row323.createCell(0);
                    cell323.setCellStyle(cellStyle);
                    cell323.setCellValue(ft.format(respInvestigateQuestion.getInvestigateAnswerList().get(j).getCreateDate()));


                    HSSFCell cell42 = row323.createCell(1);
                    cell42.setCellStyle(cellStyle);
                    cell42.setCellValue(respInvestigateQuestion.getInvestigateAnswerList().get(j).getContent());
                    CellRangeAddress callRangeAddress333 = new CellRangeAddress(x,x,1,width-1);
                    sheet.addMergedRegion(callRangeAddress333);
                    x++;
                }
            }
            //5.输出
            //设置默认文件名为当前时间：年月日时分秒
            String fileName=new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()).toString();

            //设置response头信息
            response.reset();
            response.setContentType("application/vnd.ms-excel");        //改成输出excel文件
            response.setHeader("Content-disposition","attachment; filename="+fileName+".xls" );
            OutputStream out=response.getOutputStream();
            workbook.write(out);
            out.close();
        }catch(Exception e)
        {
            e.printStackTrace();
        }
    }
    //地址题
    public static void getValue5(RespInvestigateQuestion respInvestigateQuestion, RespInvestigateQuestionStatistics respInvestigateQuestionStatistics,InvestigateSubject investigateSubject,HttpServletResponse response){
        int width= 5;
        try{
            //1.创建工作簿
            HSSFWorkbook workbook = new HSSFWorkbook();
            //1.1创建合并单元格对象
            CellRangeAddress callRangeAddress = new CellRangeAddress(0,0,0,width-1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress1 = new CellRangeAddress(1,1,0,width-1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress3 = new CellRangeAddress(3,3,0,width-1);//起始行,结束行,起始列,结束列
            //班组与时间start
            CellRangeAddress callRangeAddress20 = new CellRangeAddress(2,2,0,width-2);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress21 = new CellRangeAddress(4,4,0,width-1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress22 = new CellRangeAddress(5,5,0,width-1);//起始行,结束行,起始列,结束列
            //班组与时间end

            //标题

            //页眉
            HSSFCellStyle headStyle = createCellStyle(workbook,(short)13,true,true);
            //标题
            HSSFCellStyle erStyle = createCellStyle(workbook,(short)11,true,false);
            //班组和时间
            HSSFCellStyle sanStyle = createCellStyle(workbook,(short)10,false,false);
            //标题样式
            HSSFCellStyle colStyle = createCellStyle(workbook,(short)10,true,true);
            //内容样式
            HSSFCellStyle cellStyle = createCellStyle(workbook,(short)10,false,false);
            //2.创建工作表
            HSSFSheet sheet = workbook.createSheet("调查分析");
            //2.1加载合并单元格对象
            sheet.addMergedRegion(callRangeAddress);
            sheet.addMergedRegion(callRangeAddress1);
            sheet.addMergedRegion(callRangeAddress3);
            sheet.addMergedRegion(callRangeAddress21);
            sheet.addMergedRegion(callRangeAddress22);


            if(width-1>1)
                sheet.addMergedRegion(callRangeAddress20);

            //设置默认列宽
            sheet.setDefaultColumnWidth(23);
            //3.创建行
            //3.1创建头标题行;并且设置头标题
            HSSFRow row = sheet.createRow(0);
            HSSFCell cell = row.createCell(0);
            //加载单元格样式
            cell.setCellStyle(headStyle);
            cell.setCellValue(investigateSubject.getHeader());

            HSSFRow rower = sheet.createRow(1);
            HSSFCell celler = rower.createCell(0);
            //加载单元格样式
            celler.setCellStyle(headStyle);
            celler.setCellValue(investigateSubject.getTitle());
            SimpleDateFormat ft = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
            HSSFRow rowsan2 = sheet.createRow(2);
            HSSFCell cellsan2 = rowsan2.createCell(width-1);
            cellsan2.setCellStyle(sanStyle);
            cellsan2.setCellValue("导出时间:"+ft.format(new Date()));


            HSSFRow rowsan = sheet.createRow(3);
            HSSFCell cellsan = rowsan.createCell(0);
            cellsan.setCellStyle(erStyle);
            cellsan.setCellValue("本次调查共有"+respInvestigateQuestionStatistics.getSum()+"道题,截至"+ft.format(new Date())+"，共回收"+respInvestigateQuestionStatistics.getObjectSum()+"份");

            HSSFRow rowsan13 = sheet.createRow(4);
            HSSFCell cellsan13 = rowsan13.createCell(0);
            cellsan13.setCellStyle(cellStyle);
            cellsan13.setCellValue(respInvestigateQuestion.getOrders()+"."+respInvestigateQuestion.getContent());

            HSSFRow rowsan133 = sheet.createRow(5);
            HSSFCell cellsan133 = rowsan133.createCell(0);
            cellsan133.setCellStyle(erStyle);
            cellsan133.setCellValue("回收的问卷中，"+respInvestigateQuestion.getInvestigateAnswerList().size()+"份回答了本题，具体如下：");


            HSSFRow row32 = sheet.createRow(6);
            HSSFCell cell32 = row32.createCell(0);
            cell32.setCellStyle(colStyle);
            cell32.setCellValue("提交时间");


            HSSFCell cellsan22 = row32.createCell(1);
            cellsan22.setCellStyle(colStyle);
            cellsan22.setCellValue("省");
            HSSFCell cellsan23 = row32.createCell(2);
            cellsan23.setCellStyle(colStyle);
            cellsan23.setCellValue("市");
            HSSFCell cellsan24 = row32.createCell(3);
            cellsan24.setCellStyle(colStyle);
            cellsan24.setCellValue("地区");
            HSSFCell cellsan25 = row32.createCell(4);
            cellsan25.setCellStyle(colStyle);
            cellsan25.setCellValue("具体地址");

            int x=7;
            //4.操作单元格;将用户列表写入excel
            if(respInvestigateQuestion.getInvestigateAnswerList() != null)
            {
                for(int j=0;j<respInvestigateQuestion.getInvestigateAnswerList().size();j++)
                {
                    if(j>=65531)
                        break;
                    HSSFRow row323 = sheet.createRow(x);
                    HSSFCell cell323 = row323.createCell(0);
                    cell323.setCellStyle(cellStyle);
                    cell323.setCellValue(ft.format(respInvestigateQuestion.getInvestigateAnswerList().get(j).getCreateDate()));

                    String content=respInvestigateQuestion.getInvestigateAnswerList().get(j).getContent();
                    String province="";//省
                    String city="";//市
                    String area="";//区域
                    String addr="";//详细地址
                    String[] strArr = content.split("\\--");
                    if(strArr.length>0) {
                        String[] strArr1 = strArr[0].split("\\/");
                        if(strArr1.length>0)
                            province = strArr1[0];//省
                        if(strArr1.length>1)
                            city = strArr1[1];//市
                        if(strArr1.length>2)
                            area = strArr1[2];//区域
                        if(strArr.length>1)
                            addr = strArr[strArr.length - 1];//详细地址
                    }
                    HSSFCell cell42 = row323.createCell(1);
                    cell42.setCellStyle(cellStyle);
                    cell42.setCellValue(province);
                    HSSFCell cell421 = row323.createCell(2);
                    cell421.setCellStyle(cellStyle);
                    cell421.setCellValue(city);
                    HSSFCell cell422 = row323.createCell(3);
                    cell422.setCellStyle(cellStyle);
                    cell422.setCellValue(area);
                    HSSFCell cell423 = row323.createCell(4);
                    cell423.setCellStyle(cellStyle);
                    cell423.setCellValue(addr);
                    x++;
                }
            }
            //5.输出
            //设置默认文件名为当前时间：年月日时分秒
            String fileName=new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()).toString();

            //设置response头信息
            response.reset();
            response.setContentType("application/vnd.ms-excel");        //改成输出excel文件
            response.setHeader("Content-disposition","attachment; filename="+fileName+".xls" );
            OutputStream out=response.getOutputStream();
            workbook.write(out);
            out.close();
        }catch(Exception e)
        {
            e.printStackTrace();
        }
    }
    //日期
    public static void getValue6(RespInvestigateQuestion respInvestigateQuestion, RespInvestigateQuestionStatistics respInvestigateQuestionStatistics,InvestigateSubject investigateSubject,HttpServletResponse response){
        int width= 4;
        try{
            //1.创建工作簿
            HSSFWorkbook workbook = new HSSFWorkbook();
            //1.1创建合并单元格对象
            CellRangeAddress callRangeAddress = new CellRangeAddress(0,0,0,width-1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress1 = new CellRangeAddress(1,1,0,width-1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress3 = new CellRangeAddress(3,3,0,width-1);//起始行,结束行,起始列,结束列
            //班组与时间start
            CellRangeAddress callRangeAddress20 = new CellRangeAddress(2,2,0,width-2);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress21 = new CellRangeAddress(4,4,0,width-1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress22 = new CellRangeAddress(5,5,0,width-1);//起始行,结束行,起始列,结束列
            //班组与时间end

            //标题

            //页眉
            HSSFCellStyle headStyle = createCellStyle(workbook,(short)13,true,true);
            //标题
            HSSFCellStyle erStyle = createCellStyle(workbook,(short)11,true,false);
            //班组和时间
            HSSFCellStyle sanStyle = createCellStyle(workbook,(short)10,false,false);
            //标题样式
            HSSFCellStyle colStyle = createCellStyle(workbook,(short)10,true,true);
            //内容样式
            HSSFCellStyle cellStyle = createCellStyle(workbook,(short)10,false,false);
            HSSFCellStyle cell1Style = createCellStyle(workbook,(short)10,false,true);
            //2.创建工作表
            HSSFSheet sheet = workbook.createSheet("调查分析");
            //2.1加载合并单元格对象
            sheet.addMergedRegion(callRangeAddress);
            sheet.addMergedRegion(callRangeAddress1);
            sheet.addMergedRegion(callRangeAddress3);
            sheet.addMergedRegion(callRangeAddress21);
            sheet.addMergedRegion(callRangeAddress22);


            if(width-1>1)
                sheet.addMergedRegion(callRangeAddress20);

            //设置默认列宽
            sheet.setDefaultColumnWidth(23);
            //3.创建行
            //3.1创建头标题行;并且设置头标题
            HSSFRow row = sheet.createRow(0);
            HSSFCell cell = row.createCell(0);
            //加载单元格样式
            cell.setCellStyle(headStyle);
            cell.setCellValue(investigateSubject.getHeader());

            HSSFRow rower = sheet.createRow(1);
            HSSFCell celler = rower.createCell(0);
            //加载单元格样式
            celler.setCellStyle(headStyle);
            celler.setCellValue(investigateSubject.getTitle());
            SimpleDateFormat ft = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
            HSSFRow rowsan2 = sheet.createRow(2);
            HSSFCell cellsan2 = rowsan2.createCell(width-1);
            cellsan2.setCellStyle(sanStyle);
            cellsan2.setCellValue("导出时间:"+ft.format(new Date()));


            HSSFRow rowsan = sheet.createRow(3);
            HSSFCell cellsan = rowsan.createCell(0);
            cellsan.setCellStyle(erStyle);
            cellsan.setCellValue("本次调查共有"+respInvestigateQuestionStatistics.getSum()+"道题,截至"+ft.format(new Date())+"，共回收"+respInvestigateQuestionStatistics.getObjectSum()+"份");

            HSSFRow rowsan13 = sheet.createRow(4);
            HSSFCell cellsan13 = rowsan13.createCell(0);
            cellsan13.setCellStyle(cellStyle);
            cellsan13.setCellValue(respInvestigateQuestion.getOrders()+"."+respInvestigateQuestion.getContent());

            HSSFRow rowsan133 = sheet.createRow(5);
            HSSFCell cellsan133 = rowsan133.createCell(0);
            cellsan133.setCellStyle(erStyle);
            cellsan133.setCellValue("回收的问卷中，"+respInvestigateQuestion.getInvestigateAnswerList().size()+"份回答了本题，具体如下：");


            HSSFRow row32 = sheet.createRow(6);
            HSSFCell cell32 = row32.createCell(0);
            cell32.setCellStyle(colStyle);
            cell32.setCellValue("提交时间");


            HSSFCell cellsan22 = row32.createCell(1);
            cellsan22.setCellStyle(colStyle);
            cellsan22.setCellValue("年");
            HSSFCell cellsan23 = row32.createCell(2);
            cellsan23.setCellStyle(colStyle);
            cellsan23.setCellValue("月");
            HSSFCell cellsan24 = row32.createCell(3);
            cellsan24.setCellStyle(colStyle);
            cellsan24.setCellValue("日");

            int x=7;
            //4.操作单元格;将用户列表写入excel
            if(respInvestigateQuestion.getInvestigateAnswerList() != null)
            {
                for(int j=0;j<respInvestigateQuestion.getInvestigateAnswerList().size();j++)
                {
                    if(j>=65531)
                        break;
                    HSSFRow row323 = sheet.createRow(x);
                    HSSFCell cell323 = row323.createCell(0);
                    cell323.setCellStyle(cellStyle);
                    cell323.setCellValue(ft.format(respInvestigateQuestion.getInvestigateAnswerList().get(j).getCreateDate()));

                    String content=respInvestigateQuestion.getInvestigateAnswerList().get(j).getContent();
                    String year="";//年
                    String month="";//月
                    String day="";//日
                    String[] strArr = content.split("年");
                    if(strArr.length>0)
                    {
                        year=strArr[0];
                        if(strArr.length>1){
                            String[] strArr1= strArr[1].split("月");
                            if(strArr1.length>0)
                            {
                                month=strArr1[0];
                                if(strArr1.length>1){
                                    String[] strArr2= strArr1[1].split("日");
                                    if(strArr2.length>0)
                                    {
                                        day=strArr2[0];
                                    }
                                }
                            }
                        }
                    }


                    HSSFCell cell42 = row323.createCell(1);
                    cell42.setCellStyle(cell1Style);
                    cell42.setCellValue(year);
                    HSSFCell cell421 = row323.createCell(2);
                    cell421.setCellStyle(cell1Style);
                    cell421.setCellValue(month);
                    HSSFCell cell422 = row323.createCell(3);
                    cell422.setCellStyle(cell1Style);
                    cell422.setCellValue(day);

                    x++;
                }
            }
            //5.输出
            //设置默认文件名为当前时间：年月日时分秒
            String fileName=new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()).toString();

            //设置response头信息
            response.reset();
            response.setContentType("application/vnd.ms-excel");        //改成输出excel文件
            response.setHeader("Content-disposition","attachment; filename="+fileName+".xls" );
            OutputStream out=response.getOutputStream();
            workbook.write(out);
            out.close();
        }catch(Exception e)
        {
            e.printStackTrace();
        }
    }
    //日期
    public static void getValue7(RespMultipleChoiceQuestion respMultipleChoiceQuestion, InvestigateSubject investigateSubject, HttpServletResponse response){
        int width= 3;
        try{
            //1.创建工作簿
            HSSFWorkbook workbook = new HSSFWorkbook();
            //1.1创建合并单元格对象
            CellRangeAddress callRangeAddress = new CellRangeAddress(0,0,0,width-1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress1 = new CellRangeAddress(1,1,0,width-1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress3 = new CellRangeAddress(3,3,0,width-1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress31 = new CellRangeAddress(4,4,0,width-1);//起始行,结束行,起始列,结束列
            //班组与时间start
            CellRangeAddress callRangeAddress20 = new CellRangeAddress(2,2,0,width-2);//起始行,结束行,起始列,结束列
            //班组与时间end

            //标题

            //页眉
            HSSFCellStyle headStyle = createCellStyle(workbook,(short)13,true,true);
            //标题
            HSSFCellStyle erStyle = createCellStyle(workbook,(short)11,true,false);
            //班组和时间
            HSSFCellStyle sanStyle = createCellStyle(workbook,(short)10,false,false);
            //标题样式
            HSSFCellStyle colStyle = createCellStyle(workbook,(short)10,true,true);
            //内容样式
            HSSFCellStyle cellStyle = createCellStyle(workbook,(short)10,false,false);
            HSSFCellStyle cell1Style = createCellStyle(workbook,(short)10,false,true);
            //2.创建工作表
            HSSFSheet sheet = workbook.createSheet("调查分析");
            //2.1加载合并单元格对象
            sheet.addMergedRegion(callRangeAddress);
            sheet.addMergedRegion(callRangeAddress1);
            sheet.addMergedRegion(callRangeAddress3);
            sheet.addMergedRegion(callRangeAddress31);


            if(width-1>1)
                sheet.addMergedRegion(callRangeAddress20);

            //设置默认列宽
            sheet.setDefaultColumnWidth(23);
            //3.创建行
            //3.1创建头标题行;并且设置头标题
            HSSFRow row = sheet.createRow(0);
            HSSFCell cell = row.createCell(0);
            //加载单元格样式
            cell.setCellStyle(headStyle);
            cell.setCellValue(investigateSubject.getHeader());

            HSSFRow rower = sheet.createRow(1);
            HSSFCell celler = rower.createCell(0);
            //加载单元格样式
            celler.setCellStyle(headStyle);
            celler.setCellValue(investigateSubject.getTitle());
            SimpleDateFormat ft = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
            HSSFRow rowsan2 = sheet.createRow(2);
            HSSFCell cellsan2 = rowsan2.createCell(width-1);
            cellsan2.setCellStyle(sanStyle);
            cellsan2.setCellValue("导出时间:"+ft.format(new Date()));

            HSSFRow rowsan13 = sheet.createRow(3);
            HSSFCell cellsan13 = rowsan13.createCell(0);
            cellsan13.setCellStyle(cellStyle);
            cellsan13.setCellValue(respMultipleChoiceQuestion.getRespInvestigateQuestion().getOrders()+"."+respMultipleChoiceQuestion.getRespInvestigateQuestion().getContent());

            HSSFRow rowsan131 = sheet.createRow(4);
            HSSFCell cellsan131 = rowsan131.createCell(0);
            cellsan131.setCellStyle(colStyle);
            cellsan131.setCellValue("选项");

            int x=5;
            for(int i=0;i<respMultipleChoiceQuestion.getRespInvestigateQuestion().getInvestigateQuestionKeyList().size();i++)
            {
                CellRangeAddress callRangeAddress311 = new CellRangeAddress(x,x,0,width-1);//起始行,结束行,起始列,结束列
                sheet.addMergedRegion(callRangeAddress311);
                HSSFRow rowsan1311 = sheet.createRow(x);
                HSSFCell cellsan1311 = rowsan1311.createCell(0);
                cellsan1311.setCellStyle(cellStyle);
                cellsan1311.setCellValue("("+respMultipleChoiceQuestion.getRespInvestigateQuestion().getInvestigateQuestionKeyList().get(i).getOrders()+")."+respMultipleChoiceQuestion.getRespInvestigateQuestion().getInvestigateQuestionKeyList().get(i).getContent());
                x++;
            }
            HSSFRow rowsan133 = sheet.createRow(x);
            HSSFCell cellsan133 = rowsan133.createCell(0);
            cellsan133.setCellStyle(erStyle);
            cellsan133.setCellValue("问卷共回收"+respMultipleChoiceQuestion.getObjectSum()+"份，其中"+respMultipleChoiceQuestion.getSum()+"份回答了本题，回答者按选择结果统计如下:");
            x++;

            HSSFRow row32 = sheet.createRow(x);
            HSSFCell cell32 = row32.createCell(0);
            cell32.setCellStyle(colStyle);
            cell32.setCellValue("选择结果");
            HSSFCell cellsan22 = row32.createCell(1);
            cellsan22.setCellStyle(colStyle);
            cellsan22.setCellValue("选择该项的数量");
            HSSFCell cellsan23 = row32.createCell(2);
            cellsan23.setCellStyle(colStyle);
            cellsan23.setCellValue("占比");
            x++;
            //4.操作单元格;将用户列表写入excel
            if(respMultipleChoiceQuestion.getRespAnswerCountList() != null)
            {
                for(int j=0;j<respMultipleChoiceQuestion.getRespAnswerCountList().size();j++)
                {
                    if(j>=65531)
                        break;
                    String oldString=respMultipleChoiceQuestion.getRespAnswerCountList().get(j).getAnswer();
                    String newString="";
                    String[] strArr = oldString.split(",");
                    for(int n=0;n<strArr.length;n++)
                    {
                        for(int i=0;i<respMultipleChoiceQuestion.getRespInvestigateQuestion().getInvestigateQuestionKeyList().size();i++)
                        {
                            InvestigateQuestionKey key= respMultipleChoiceQuestion.getRespInvestigateQuestion().getInvestigateQuestionKeyList().get(i);
                            if(key.getId()==Integer.valueOf(strArr[n]).intValue())
                            {
                                if("".equals(newString)){
                                    newString=newString+key.getOrders();
                                }else{
                                    newString=newString+","+key.getOrders();
                                }
                                break;
                            }
                        }
                    }
                    HSSFRow row323 = sheet.createRow(x);
                    HSSFCell cell323 = row323.createCell(0);
                    cell323.setCellStyle(cellStyle);
                    cell323.setCellValue(newString);
                    HSSFCell cell42 = row323.createCell(1);
                    cell42.setCellStyle(cell1Style);
                    cell42.setCellValue(respMultipleChoiceQuestion.getRespAnswerCountList().get(j).getSum());
                    HSSFCell cell421 = row323.createCell(2);
                    cell421.setCellStyle(cell1Style);
                    double number= Double.parseDouble(respMultipleChoiceQuestion.getRespAnswerCountList().get(j).getPercentage());
                    number=number*100;
                    BigDecimal number1=new BigDecimal(Double.toString(number));
                    number1=number1.setScale(2, RoundingMode.HALF_UP);
                    cell421.setCellValue(number1+"%");
                    x++;
                }
            }
            //5.输出
            //设置默认文件名为当前时间：年月日时分秒
            String fileName=new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()).toString();

            //设置response头信息
            response.reset();
            response.setContentType("application/vnd.ms-excel");        //改成输出excel文件
            response.setHeader("Content-disposition","attachment; filename="+fileName+".xls" );
            OutputStream out=response.getOutputStream();
            workbook.write(out);
            out.close();
        }catch(Exception e)
        {
            e.printStackTrace();
        }
    }
    //问答题导出
    public static void getValue8(RespShowQuestion respShowQuestion,String content, InvestigateSubject investigateSubject, HttpServletResponse response){
            int width=0;
            if (respShowQuestion.getRespInvestigatePublishShowList()!=null)
                 width= respShowQuestion.getRespInvestigatePublishShowList().size()+1;
            else if(respShowQuestion.getInvestigatePublishShowList()!=null)
                 width= respShowQuestion.getInvestigatePublishShowList().size()+1;
            if(width<=1)
                width=2;
            try{
                //1.创建工作簿
                HSSFWorkbook workbook = new HSSFWorkbook();
                //1.1创建合并单元格对象
                CellRangeAddress callRangeAddress = new CellRangeAddress(0,0,0,width-1);//起始行,结束行,起始列,结束列
                CellRangeAddress callRangeAddress1 = new CellRangeAddress(1,1,0,width-1);//起始行,结束行,起始列,结束列
                //班组与时间start
                CellRangeAddress callRangeAddress20 = new CellRangeAddress(2,2,0,width-2);//起始行,结束行,起始列,结束列
                CellRangeAddress callRangeAddress21 = new CellRangeAddress(2,2,width-2,width-1);//起始行,结束行,起始列,结束列
                //班组与时间end

                //标题
                CellRangeAddress callRangeAddress31 = new CellRangeAddress(3,3,0,width-1);//起始行,结束行,起始列,结束列
                CellRangeAddress callRangeAddress32 = new CellRangeAddress(4,4,0,width-1);//起始行,结束行,起始列,结束列
                CellRangeAddress callRangeAddress33 = new CellRangeAddress(5,5,0,width-1);//起始行,结束行,起始列,结束列

                CellRangeAddress callRangeAddress34 = new CellRangeAddress(3,3,3,3);//起始行,结束行,起始列,结束列

                //页眉
                HSSFCellStyle headStyle = createCellStyle(workbook,(short)13,true,true);
                //标题
                HSSFCellStyle erStyle = createCellStyle(workbook,(short)11,true,true);
                //班组和时间
                HSSFCellStyle sanStyle = createCellStyle(workbook,(short)10,false,false);
                //标题样式
                HSSFCellStyle colStyle = createCellStyle(workbook,(short)10,true,true);
                //内容样式
                HSSFCellStyle cellStyle = createCellStyle(workbook,(short)10,false,true);
                //2.创建工作表
                HSSFSheet sheet = workbook.createSheet("回收的问卷");
                //2.1加载合并单元格对象
                sheet.addMergedRegion(callRangeAddress);
                sheet.addMergedRegion(callRangeAddress1);
                sheet.addMergedRegion(callRangeAddress31);
                sheet.addMergedRegion(callRangeAddress32);
                sheet.addMergedRegion(callRangeAddress33);
                if(width-1>1)
                    sheet.addMergedRegion(callRangeAddress20);

                //设置默认列宽
                sheet.setDefaultColumnWidth(23);
                //3.创建行
                //3.1创建头标题行;并且设置头标题
                HSSFRow row = sheet.createRow(0);
                HSSFCell cell = row.createCell(0);
                //加载单元格样式
                cell.setCellStyle(headStyle);
                cell.setCellValue(investigateSubject.getHeader());

                HSSFRow rower = sheet.createRow(1);
                HSSFCell celler = rower.createCell(0);
                //加载单元格样式
                celler.setCellStyle(erStyle);
                celler.setCellValue(investigateSubject.getTitle());

                HSSFRow rowsan = sheet.createRow(2);
                HSSFCell cellsan = rowsan.createCell(0);

                HSSFCell cellsan2 = rowsan.createCell(width-1);
                //加载单元格样式
//                cellsan.setCellStyle(sanStyle);
//                cellsan.setCellValue("问卷已回收"+respShowQuestion.getRespInvestigateObjectList().size()+"份，具体如下：");
                cellsan2.setCellStyle(sanStyle);
                SimpleDateFormat ft = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
                cellsan2.setCellValue("导出时间:"+ft.format(new Date()));

                HSSFRow rowsan3 = sheet.createRow(3);
                HSSFCell cellsan3 = rowsan3.createCell(0);
                cellsan3.setCellStyle(sanStyle);
                cellsan3.setCellValue("本次调查共有"+investigateSubject.getQuestionsNum()+"道题，截至"+ft.format(new Date())+"，共回收"+respShowQuestion
                        .getObjSum()+"份");

                HSSFRow rowsan4 = sheet.createRow(4);
                HSSFCell cellsan4 = rowsan4.createCell(0);
                cellsan4.setCellStyle(sanStyle);
                cellsan4.setCellValue(respShowQuestion.getInvestigateQuestion().getOrders()+"."+respShowQuestion.getInvestigateQuestion().getContent());

                HSSFRow rowsan5 = sheet.createRow(5);
                HSSFCell cellsan5 = rowsan5.createCell(0);
                cellsan5.setCellStyle(sanStyle);
                if(content!=null&&!"".equals(content))
                {
                    //回收的XXX份问卷中，回答了本题的有N份，选择了XXXXXXXX的有P份。
                    cellsan5.setCellValue("回收的"+respShowQuestion.getObjSum()+"份问卷中，回答了本题的有"+respShowQuestion.getContentSum()+"份，选择了"+content+"的有"+respShowQuestion.getAnswerSum()+"份");
                }else{
                    cellsan5.setCellValue(respShowQuestion.getObjSum()+"份问卷中，"+respShowQuestion.getAnswerSum()+"份回答了本题，具体如下：");
                }

                //3.2创建列标题;并且设置列标题
                HSSFRow row2 = sheet.createRow(6);
                HSSFCell cell22 = row2.createCell(0);
                //加载单元格样式
                cell22.setCellStyle(colStyle);
                cell22.setCellValue("提交时间");
                for( int i=0;i<respShowQuestion.getInvestigatePublishShowList().size();i++)
                {
                    HSSFCell cell21 = row2.createCell(i+1);
                    //加载单元格样式
                    cell21.setCellStyle(colStyle);
                    cell21.setCellValue(respShowQuestion.getInvestigatePublishShowList().get(i).getContent());
                }
                int sheetSize=65535;
                double sheetNum=Math.ceil((respShowQuestion.getRespInvestigateObjectList().size()+4)/new Integer(sheetSize).doubleValue());
                //4.操作单元格;将用户列表写入excel
                if(respShowQuestion.getRespInvestigateObjectList() != null)
                {
                    for(int j=0;j<respShowQuestion.getRespInvestigateObjectList().size();j++) {
                        if (j >= 65531)
                            break;
                        int x=0;
                        //创建数据行,前面有两行,头标题行和列标题行
                        HSSFRow row3 = sheet.createRow(j + 7);
                        HSSFCell cell0 = row3.createCell(x);
                        cell0.setCellStyle(cellStyle);
                        cell0.setCellValue(ft.format(respShowQuestion.getRespInvestigateObjectList().get(j).getCreateDate()));

                        //根据问题对应答案
                        for (int q=0;q< respShowQuestion.getInvestigatePublishShowList().size();q++)
                        {
                            for(RespInvestigateAnswer investigateAnswer:respShowQuestion.getRespInvestigateObjectList().get(j).getRespInvestigateAnswerList())
                            {
                                if(investigateAnswer.getQuestion().intValue()==respShowQuestion.getInvestigatePublishShowList().get(q).getQuestion().intValue())
                                {
                                        HSSFCell cell1 = row3.createCell(q + 1);
                                        cell1.setCellStyle(cellStyle);
                                        cell1.setCellValue(investigateAnswer.getContent());
                                }
                            }
                        }
                    }
                }
                //5.输出
                //设置默认文件名为当前时间：年月日时分秒
                String fileName=new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()).toString();

                //设置response头信息
                response.reset();
                response.setContentType("application/vnd.ms-excel");        //改成输出excel文件
                response.setHeader("Content-disposition","attachment; filename="+fileName+".xls" );
                OutputStream out=response.getOutputStream();
                workbook.write(out);
                out.close();
            }catch(Exception e)
            {
                e.printStackTrace();
            }
        }
    //地址统计导出
    public static void getValue9(RespShowQuestion respShowQuestion, InvestigateSubject investigateSubject, HttpServletResponse response) {
        int width = 3;
        if("1".equals(respShowQuestion.getInvestigateQuestion().getSpecialTab())){
            width=2;
        }else if("2".equals(respShowQuestion.getInvestigateQuestion().getSpecialTab())){
            width=2;
        }else if("3".equals(respShowQuestion.getInvestigateQuestion().getSpecialTab())||"4".equals(respShowQuestion.getInvestigateQuestion().getSpecialTab())) {
            width=3;
        }
            try {
            //1.创建工作簿
            HSSFWorkbook workbook = new HSSFWorkbook();
            //1.1创建合并单元格对象
            CellRangeAddress callRangeAddress = new CellRangeAddress(0, 0, 0, width - 1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress1 = new CellRangeAddress(1, 1, 0, width - 1);//起始行,结束行,起始列,结束列
            //班组与时间start
            CellRangeAddress callRangeAddress20 = new CellRangeAddress(2, 2, 0, width - 2);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress21 = new CellRangeAddress(2, 2, width - 2, width - 1);//起始行,结束行,起始列,结束列
            //班组与时间end

            //标题
            CellRangeAddress callRangeAddress31 = new CellRangeAddress(3, 3, 0, width - 1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress32 = new CellRangeAddress(4, 4, 0, width - 1);//起始行,结束行,起始列,结束列
            CellRangeAddress callRangeAddress33 = new CellRangeAddress(5, 5, 0, width - 1);//起始行,结束行,起始列,结束列

            CellRangeAddress callRangeAddress34 = new CellRangeAddress(3, 3, 3, 3);//起始行,结束行,起始列,结束列

            //页眉
            HSSFCellStyle headStyle = createCellStyle(workbook, (short) 13, true, true);
            //标题
            HSSFCellStyle erStyle = createCellStyle(workbook, (short) 11, true, true);
            //班组和时间
            HSSFCellStyle sanStyle = createCellStyle(workbook, (short) 10, false, false);
            //标题样式
            HSSFCellStyle colStyle = createCellStyle(workbook, (short) 10, true, true);
            //内容样式
            HSSFCellStyle cellStyle = createCellStyle(workbook, (short) 10, false, true);
            //2.创建工作表
            HSSFSheet sheet = workbook.createSheet("回收的问卷");
            //2.1加载合并单元格对象
            sheet.addMergedRegion(callRangeAddress);
            sheet.addMergedRegion(callRangeAddress1);
            sheet.addMergedRegion(callRangeAddress31);
            sheet.addMergedRegion(callRangeAddress32);
            sheet.addMergedRegion(callRangeAddress33);
            if (width - 1 > 1)
                sheet.addMergedRegion(callRangeAddress20);

            //设置默认列宽
            sheet.setDefaultColumnWidth(23);
            //3.创建行
            //3.1创建头标题行;并且设置头标题
            HSSFRow row = sheet.createRow(0);
            HSSFCell cell = row.createCell(0);
            //加载单元格样式
            cell.setCellStyle(headStyle);
            cell.setCellValue(investigateSubject.getHeader());

            HSSFRow rower = sheet.createRow(1);
            HSSFCell celler = rower.createCell(0);
            //加载单元格样式
            celler.setCellStyle(erStyle);
            celler.setCellValue(investigateSubject.getTitle());

            HSSFRow rowsan = sheet.createRow(2);
            HSSFCell cellsan = rowsan.createCell(0);

            HSSFCell cellsan2 = rowsan.createCell(width - 1);
            //加载单元格样式
//                cellsan.setCellStyle(sanStyle);
//                cellsan.setCellValue("问卷已回收"+respShowQuestion.getRespInvestigateObjectList().size()+"份，具体如下：");
            cellsan2.setCellStyle(sanStyle);
            SimpleDateFormat ft = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss");
            cellsan2.setCellValue("导出时间:" + ft.format(new Date()));

            HSSFRow rowsan3 = sheet.createRow(3);
            HSSFCell cellsan3 = rowsan3.createCell(0);
            cellsan3.setCellStyle(sanStyle);
            cellsan3.setCellValue("本次调查共有" + investigateSubject.getQuestionsNum() + "道题，截至" + ft.format(new Date()) + "，共回收" + respShowQuestion
                    .getObjSum() + "份");

            HSSFRow rowsan4 = sheet.createRow(4);
            HSSFCell cellsan4 = rowsan4.createCell(0);
            cellsan4.setCellStyle(sanStyle);
            cellsan4.setCellValue(respShowQuestion.getInvestigateQuestion().getOrders() + "." + respShowQuestion.getInvestigateQuestion().getContent());

            HSSFRow rowsan5 = sheet.createRow(5);
            HSSFCell cellsan5 = rowsan5.createCell(0);
            cellsan5.setCellStyle(sanStyle);
            cellsan5.setCellValue(respShowQuestion.getObjSum() + "份问卷中，" + respShowQuestion.getAnswerSum() + "份回答了本题，具体如下：");

            //3.2创建列标题;并且设置列标题
            HSSFRow row2 = sheet.createRow(6);

            int areaStart=7;
            int areaStart1=7;
            int areaStart2=7;
            //地址
            if("3".equals(respShowQuestion.getInvestigateQuestion().getSpecialModel()))
            {
                if("1".equals(respShowQuestion.getInvestigateQuestion().getSpecialTab())){
                    HSSFCell cell21 = row2.createCell(0);
                    //加载单元格样式
                    cell21.setCellStyle(colStyle);
                    cell21.setCellValue("省");
                    for(int year=0;year<respShowQuestion.getInvestigateAreaList().size();year++)
                    {
                        RespInvestigateArea respInvestigateAreaYear=respShowQuestion.getInvestigateAreaList().get(year);
                        HSSFRow row311 = sheet.createRow(areaStart);
                        HSSFCell cell311 = row311.createCell(0);
                        cell311.setCellStyle(cellStyle);
                        cell311.setCellValue(respInvestigateAreaYear.getName() + "  " + respInvestigateAreaYear.getSum()+ "位");
                        areaStart++;
                    }
                }else if("2".equals(respShowQuestion.getInvestigateQuestion().getSpecialTab())){
                    HSSFCell cell21 = row2.createCell(0);
                    //加载单元格样式
                    cell21.setCellStyle(colStyle);
                    cell21.setCellValue("省");
                    HSSFCell cell2111 = row2.createCell(1);
                    //加载单元格样式
                    cell2111.setCellStyle(colStyle);
                    cell2111.setCellValue("市");

                    for(int year=0;year<respShowQuestion.getInvestigateAreaList().size();year++)
                    {
                        RespInvestigateArea respInvestigateAreaYear=respShowQuestion.getInvestigateAreaList().get(year);
                        if(respInvestigateAreaYear.getInvestigateAreaList()!=null)
                        {
                            for (int month = 0; month < respInvestigateAreaYear.getInvestigateAreaList().size(); month++) {
                                RespInvestigateArea respInvestigateAreaMonth = respInvestigateAreaYear.getInvestigateAreaList().get(month);
                                    HSSFRow row311 = sheet.createRow(areaStart1);
                                    HSSFCell cell311 = row311.createCell(1);
                                    cell311.setCellStyle(cellStyle);
                                    cell311.setCellValue(respInvestigateAreaMonth.getName() + "  " + respInvestigateAreaMonth.getSum()+ "位");
                                    areaStart1++;
                            }
                            if(respInvestigateAreaYear.getInvestigateAreaList().size()>1)
                            {
                                CellRangeAddress callRangeAddress333 = new CellRangeAddress(areaStart, areaStart+respInvestigateAreaYear.getInvestigateAreaList().size()-1, 0, 0);//起始行,结束行,起始列,结束列
                                sheet.addMergedRegion(callRangeAddress333);
                                respInvestigateAreaYear.setRow(areaStart);
                                HSSFRow row333 = sheet.getRow(areaStart);
                                HSSFCell cell333 = row333.getCell(0);
                                if (cell333 == null) { // 如果单元格不存在，则创建新单元格
                                    cell333 = row333.createCell(0);
                                }
                                cell333.setCellStyle(cellStyle);
                                cell333.setCellValue(respInvestigateAreaYear.getName() + "  " + respInvestigateAreaYear.getSum()+ "位");
                                areaStart=areaStart+respInvestigateAreaYear.getInvestigateAreaList().size();
                            }else{
                                respInvestigateAreaYear.setRow(areaStart);
                                HSSFRow row333 = sheet.getRow(areaStart);
                                HSSFCell cell333 = row333.getCell(0);
                                if (cell333 == null) { // 如果单元格不存在，则创建新单元格
                                    cell333 = row333.createCell(0);
                                }
                                cell333.setCellStyle(cellStyle);
                                cell333.setCellValue(respInvestigateAreaYear.getName() + "  " + respInvestigateAreaYear.getSum()+ "位");
                                areaStart++;
                            }
                        }
                    }

                }else if("3".equals(respShowQuestion.getInvestigateQuestion().getSpecialTab())||"4".equals(respShowQuestion.getInvestigateQuestion().getSpecialTab())){
                    HSSFCell cell21 = row2.createCell(0);
                    //加载单元格样式
                    cell21.setCellStyle(colStyle);
                    cell21.setCellValue("省");
                    HSSFCell cell2111 = row2.createCell(1);
                    //加载单元格样式
                    cell2111.setCellStyle(colStyle);
                    cell2111.setCellValue("市");
                    HSSFCell cell21111 = row2.createCell(2);
                    //加载单元格样式
                    cell21111.setCellStyle(colStyle);
                    cell21111.setCellValue("地区");
                    for(int year=0;year<respShowQuestion.getInvestigateAreaList().size();year++) {
                        int x=0;
                        RespInvestigateArea respInvestigateAreaYear = respShowQuestion.getInvestigateAreaList().get(year);
                        if (respInvestigateAreaYear.getInvestigateAreaList() != null) {
                            for (int month = 0; month < respInvestigateAreaYear.getInvestigateAreaList().size(); month++)
                            {
                                RespInvestigateArea respInvestigateAreaMonth = respInvestigateAreaYear.getInvestigateAreaList().get(month);
                                if (respInvestigateAreaMonth.getInvestigateAreaList() != null)
                                {
                                    x=x+ respInvestigateAreaMonth.getInvestigateAreaList().size();
                                    for (int day = 0; day < respInvestigateAreaMonth.getInvestigateAreaList().size(); day++) {
                                        RespInvestigateArea respInvestigateAreaDay = respInvestigateAreaMonth.getInvestigateAreaList().get(day);
                                        HSSFRow row31 = sheet.createRow(areaStart2);
                                        HSSFCell cell31 = row31.createCell(2);
                                        cell31.setCellStyle(cellStyle);
                                        cell31.setCellValue(respInvestigateAreaDay.getName() + "  " + respInvestigateAreaDay.getSum()+ "位");
                                        areaStart2++;
                                    }
                                }
                                if(respInvestigateAreaMonth.getInvestigateAreaList().size()>1)
                                {
                                    CellRangeAddress callRangeAddress333 = new CellRangeAddress(areaStart1, areaStart1+respInvestigateAreaMonth.getInvestigateAreaList().size()-1, 1, 1);//起始行,结束行,起始列,结束列
                                    sheet.addMergedRegion(callRangeAddress333);
                                    respInvestigateAreaMonth.setRow(areaStart1);
                                    HSSFRow row333 = sheet.getRow(areaStart1);
                                    HSSFCell cell333 = row333.getCell(1);
                                    if (cell333 == null) { // 如果单元格不存在，则创建新单元格
                                        cell333 = row333.createCell(1);
                                    }
                                    cell333.setCellStyle(cellStyle);
                                    cell333.setCellValue(respInvestigateAreaMonth.getName() + "  " + respInvestigateAreaMonth.getSum()+ "位");
                                    areaStart1=areaStart1+respInvestigateAreaMonth.getInvestigateAreaList().size();
                                }else{
                                    respInvestigateAreaMonth.setRow(areaStart1);
                                    HSSFRow row333 = sheet.getRow(areaStart1);
                                    HSSFCell cell333 = row333.getCell(1);
                                    if (cell333 == null) { // 如果单元格不存在，则创建新单元格
                                        cell333 = row333.createCell(1);
                                    }
                                    cell333.setCellStyle(cellStyle);
                                    cell333.setCellValue(respInvestigateAreaMonth.getName() + "  " + respInvestigateAreaMonth.getSum()+ "位");
                                    areaStart1++;
                                }
                            }
                        }
                        if(x>1){
                            CellRangeAddress callRangeAddress333 = new CellRangeAddress(areaStart, areaStart+x-1, 0, 0);//起始行,结束行,起始列,结束列
                            sheet.addMergedRegion(callRangeAddress333);
                            respInvestigateAreaYear.setRow(areaStart);
                            HSSFRow row333 = sheet.getRow(areaStart);
                            HSSFCell cell333 = row333.getCell(0);
                            if (cell333 == null) { // 如果单元格不存在，则创建新单元格
                                cell333 = row333.createCell(0);
                            }
                            cell333.setCellStyle(cellStyle);
                            cell333.setCellValue(respInvestigateAreaYear.getName() + "  " + respInvestigateAreaYear.getSum()+ "位");
                            areaStart=areaStart+x;
                        }else {
                            respInvestigateAreaYear.setRow(areaStart);
                            HSSFRow row333 = sheet.getRow(areaStart);
                            HSSFCell cell333 = row333.getCell(0);
                            if (cell333 == null) { // 如果单元格不存在，则创建新单元格
                                cell333 = row333.createCell(0);
                            }
                            cell333.setCellStyle(cellStyle);
                            cell333.setCellValue(respInvestigateAreaYear.getName() + "  " + respInvestigateAreaYear.getSum()+ "位");
                            areaStart++;
                        }
                    }
                }
            }
            //日期
            else if("4".equals(respShowQuestion.getInvestigateQuestion().getSpecialModel())){
                if("1".equals(respShowQuestion.getInvestigateQuestion().getSpecialTab())){
                    HSSFCell cell21 = row2.createCell(0);
                    //加载单元格样式
                    cell21.setCellStyle(colStyle);
                    cell21.setCellValue("年");
                    for(int year=0;year<respShowQuestion.getInvestigateAreaList().size();year++)
                    {
                        RespInvestigateArea respInvestigateAreaYear=respShowQuestion.getInvestigateAreaList().get(year);
                        HSSFRow row311 = sheet.createRow(areaStart);
                        HSSFCell cell311 = row311.createCell(0);
                        cell311.setCellStyle(cellStyle);
                        cell311.setCellValue(respInvestigateAreaYear.getName() + "  " + respInvestigateAreaYear.getSum()+ "位");
                        areaStart++;
                    }
                }else if("2".equals(respShowQuestion.getInvestigateQuestion().getSpecialTab())){
                    HSSFCell cell21 = row2.createCell(0);
                    //加载单元格样式
                    cell21.setCellStyle(colStyle);
                    cell21.setCellValue("年");
                    HSSFCell cell2111 = row2.createCell(1);
                    //加载单元格样式
                    cell2111.setCellStyle(colStyle);
                    cell2111.setCellValue("月");

                    for(int year=0;year<respShowQuestion.getInvestigateAreaList().size();year++)
                    {
                        RespInvestigateArea respInvestigateAreaYear=respShowQuestion.getInvestigateAreaList().get(year);
                        if(respInvestigateAreaYear.getInvestigateAreaList()!=null)
                        {
                            for (int month = 0; month < respInvestigateAreaYear.getInvestigateAreaList().size(); month++) {
                                RespInvestigateArea respInvestigateAreaMonth = respInvestigateAreaYear.getInvestigateAreaList().get(month);
                                HSSFRow row311 = sheet.createRow(areaStart1);
                                HSSFCell cell311 = row311.createCell(1);
                                cell311.setCellStyle(cellStyle);
                                cell311.setCellValue(respInvestigateAreaMonth.getName() + "  " + respInvestigateAreaMonth.getSum()+ "位");
                                areaStart1++;
                            }
                            if(respInvestigateAreaYear.getInvestigateAreaList().size()>1)
                            {
                                CellRangeAddress callRangeAddress333 = new CellRangeAddress(areaStart, areaStart+respInvestigateAreaYear.getInvestigateAreaList().size()-1, 0, 0);//起始行,结束行,起始列,结束列
                                sheet.addMergedRegion(callRangeAddress333);
                                respInvestigateAreaYear.setRow(areaStart);
                                HSSFRow row333 = sheet.getRow(areaStart);
                                HSSFCell cell333 = row333.getCell(0);
                                if (cell333 == null) { // 如果单元格不存在，则创建新单元格
                                    cell333 = row333.createCell(0);
                                }
                                cell333.setCellStyle(cellStyle);
                                cell333.setCellValue(respInvestigateAreaYear.getName() + "  " + respInvestigateAreaYear.getSum()+ "位");
                                areaStart=areaStart+respInvestigateAreaYear.getInvestigateAreaList().size();
                            }else{
                                respInvestigateAreaYear.setRow(areaStart);
                                HSSFRow row333 = sheet.getRow(areaStart);
                                HSSFCell cell333 = row333.getCell(0);
                                if (cell333 == null) { // 如果单元格不存在，则创建新单元格
                                    cell333 = row333.createCell(0);
                                }
                                cell333.setCellStyle(cellStyle);
                                cell333.setCellValue(respInvestigateAreaYear.getName() + "  " + respInvestigateAreaYear.getSum()+ "位");
                                areaStart++;
                            }
                        }
                    }
                }else if("3".equals(respShowQuestion.getInvestigateQuestion().getSpecialTab())){
                    HSSFCell cell21 = row2.createCell(0);
                    //加载单元格样式
                    cell21.setCellStyle(colStyle);
                    cell21.setCellValue("年");
                    HSSFCell cell2111 = row2.createCell(1);
                    //加载单元格样式
                    cell2111.setCellStyle(colStyle);
                    cell2111.setCellValue("月");
                    HSSFCell cell21111 = row2.createCell(2);
                    //加载单元格样式
                    cell21111.setCellStyle(colStyle);
                    cell21111.setCellValue("日");

                    for(int year=0;year<respShowQuestion.getInvestigateAreaList().size();year++) {
                        int x=0;
                        RespInvestigateArea respInvestigateAreaYear = respShowQuestion.getInvestigateAreaList().get(year);
                        if (respInvestigateAreaYear.getInvestigateAreaList() != null) {
                            for (int month = 0; month < respInvestigateAreaYear.getInvestigateAreaList().size(); month++)
                            {
                                RespInvestigateArea respInvestigateAreaMonth = respInvestigateAreaYear.getInvestigateAreaList().get(month);
                                if (respInvestigateAreaMonth.getInvestigateAreaList() != null)
                                {
                                    x=x+ respInvestigateAreaMonth.getInvestigateAreaList().size();
                                    for (int day = 0; day < respInvestigateAreaMonth.getInvestigateAreaList().size(); day++) {
                                        RespInvestigateArea respInvestigateAreaDay = respInvestigateAreaMonth.getInvestigateAreaList().get(day);
                                        HSSFRow row31 = sheet.createRow(areaStart2);
                                        HSSFCell cell31 = row31.createCell(2);
                                        cell31.setCellStyle(cellStyle);
                                        cell31.setCellValue(respInvestigateAreaDay.getName() + "  " + respInvestigateAreaDay.getSum()+ "位");
                                        areaStart2++;
                                    }
                                }
                                if(respInvestigateAreaMonth.getInvestigateAreaList().size()>1)
                                {
                                    CellRangeAddress callRangeAddress333 = new CellRangeAddress(areaStart1, areaStart1+respInvestigateAreaMonth.getInvestigateAreaList().size()-1, 1, 1);//起始行,结束行,起始列,结束列
                                    sheet.addMergedRegion(callRangeAddress333);
                                    respInvestigateAreaMonth.setRow(areaStart1);
                                    HSSFRow row333 = sheet.getRow(areaStart1);
                                    HSSFCell cell333 = row333.getCell(1);
                                    if (cell333 == null) { // 如果单元格不存在，则创建新单元格
                                        cell333 = row333.createCell(1);
                                    }
                                    cell333.setCellStyle(cellStyle);
                                    cell333.setCellValue(respInvestigateAreaMonth.getName() + "  " + respInvestigateAreaMonth.getSum()+ "位");
                                    areaStart1=areaStart1+respInvestigateAreaMonth.getInvestigateAreaList().size();
                                }else{
                                    respInvestigateAreaMonth.setRow(areaStart1);
                                    HSSFRow row333 = sheet.getRow(areaStart1);
                                    HSSFCell cell333 = row333.getCell(1);
                                    if (cell333 == null) { // 如果单元格不存在，则创建新单元格
                                        cell333 = row333.createCell(1);
                                    }
                                    cell333.setCellStyle(cellStyle);
                                    cell333.setCellValue(respInvestigateAreaMonth.getName() + "  " + respInvestigateAreaMonth.getSum()+ "位");
                                    areaStart1++;
                                }
                            }
                        }
                        if(x>1){
                            CellRangeAddress callRangeAddress333 = new CellRangeAddress(areaStart, areaStart+x-1, 0, 0);//起始行,结束行,起始列,结束列
                            sheet.addMergedRegion(callRangeAddress333);
                            respInvestigateAreaYear.setRow(areaStart);
                            HSSFRow row333 = sheet.getRow(areaStart);
                            HSSFCell cell333 = row333.getCell(0);
                            if (cell333 == null) { // 如果单元格不存在，则创建新单元格
                                cell333 = row333.createCell(0);
                            }
                            cell333.setCellStyle(cellStyle);
                            cell333.setCellValue(respInvestigateAreaYear.getName() + "  " + respInvestigateAreaYear.getSum()+ "位");
                            areaStart=areaStart+x;
                        }else {
                            respInvestigateAreaYear.setRow(areaStart);
                            HSSFRow row333 = sheet.getRow(areaStart);
                            HSSFCell cell333 = row333.getCell(0);
                            if (cell333 == null) { // 如果单元格不存在，则创建新单元格
                                cell333 = row333.createCell(0);
                            }
                            cell333.setCellStyle(cellStyle);
                            cell333.setCellValue(respInvestigateAreaYear.getName() + "  " + respInvestigateAreaYear.getSum()+ "位");
                            areaStart++;
                        }
                    }
                }
            }

            //5.输出
            //设置默认文件名为当前时间：年月日时分秒
            String fileName = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()).toString();

            //设置response头信息
            response.reset();
            response.setContentType("application/vnd.ms-excel");        //改成输出excel文件
            response.setHeader("Content-disposition", "attachment; filename=" + fileName + ".xls");
            OutputStream out = response.getOutputStream();
            workbook.write(out);
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
