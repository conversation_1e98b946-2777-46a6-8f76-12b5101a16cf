package cn.sphd.miners.modules.generalAffairs.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

/**
 * t_iot_terminal_history 物联网_设备历史表  名字改为t_personnel_attendance_iot_terminal，太长不加history
 */
@Entity
@Table(name = "t_personnel_attendance_iot_terminal")
public class PersonnelAttendanceIotTerminal extends BaseEntity {
    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;//`id` INTEGER NOT NULL AUTO_INCREMENT COMMENT 'ID',
    @Column(name = "iot_terminal")
    private Integer iotTerminal;//系统设备ID
    @Column(name = "device_uuid", length = 36)
    private String deviceUuid;//`device_uuid` VARCHAR(36) CHARACTER SET latin1 COLLATE latin1_general_ci DEFAULT NULL COMMENT '唯一标识',
    @Column(name = "terminal_type")
    private Byte terminalType;//`terminal_type` TINYINT DEFAULT NULL COMMENT '设备类型，枚举:1-移动考勤宝',
    @Column(name = "login_type")
    private Byte loginType;//`login_type` TINYINT DEFAULT NULL COMMENT '登录类型:0-不可登录,1-手机端模拟登录,2-设备token登录',
//    @Column(name = "terminal_token", length = 128)
//    private String terminalToken;//`terminal_token` VARCHAR(128) CHARACTER SET latin1 COLLATE latin1_bin DEFAULT NULL COMMENT '设备token',
//    @Column(name = "org_id")
//    private Integer orgId;//`org_id` INTEGER DEFAULT NULL COMMENT '模拟登录机构Id',
//    @Column(name = "acc_id")
//    private Long accId;//`acc_id` BIGINT DEFAULT NULL COMMENT '模拟登录账号id',
//    @Column(length = 20)
//    private String mobile;//`mobile` VARCHAR(20) CHARACTER SET latin1 COLLATE latin1_bin DEFAULT NULL COMMENT '模拟登录手机号',
//    @Column(name = "user_id")
//    private Integer userId;//`user_id` INTEGER DEFAULT NULL COMMENT '模拟登录用户id',
//    @Column(name = "member_id")
//    private Long memberId;//`member_id` BIGINT DEFAULT NULL COMMENT '模拟登录第三方平台用户id',
//    @Column(name = "tg_code")
//    private Byte tgCode;//`tg_code` TINYINT DEFAULT NULL COMMENT '模拟登录第三方大厂(1-weChat,2-byteDance)',
//    @Column(name = "app_id", length = 34)
//    private String appId;//`app_id` VARCHAR(34) CHARACTER SET latin1 COLLATE latin1_bin DEFAULT NULL COMMENT '模拟登录appId',
//    @Column(name = "union_id", length = 128)
//    private String unionId;//`union_id` VARCHAR(128) CHARACTER SET latin1 COLLATE latin1_bin DEFAULT NULL COMMENT '模拟登录用户union_id',
//    @Column(name = "open_id", length = 128)
//    private String openId;//`open_id` VARCHAR(128) CHARACTER SET latin1 COLLATE latin1_bin DEFAULT NULL COMMENT '模拟登录用户openid',
    @Column(length = 20)
    private String sn;//`sn` VARCHAR(20) CHARACTER SET latin1 COLLATE latin1_general_ci DEFAULT NULL COMMENT '编号',
    @Column(length = 50)
    private String brand;//`brand` VARCHAR(50) DEFAULT NULL COMMENT '品牌',
    @Column(length = 50)
    private String model;//`model` VARCHAR(50) DEFAULT NULL COMMENT '型号',
    @Column
    private Boolean enabled;//`enabled` BOOLEAN DEFAULT NULL COMMENT '是否有效,true-有效,false-无效',
    @Column(name = "enabled_time")
    private Date enabledTime;//`enabled_time` DATETIME(3) DEFAULT NULL COMMENT '启停用时间',

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator; //创建人id

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName; //创建人

    @Column(name = "create_time")
    @CreationTimestamp
    private Date createTime;//`create_time` DATETIME(3) DEFAULT NULL COMMENT '创建时间',

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator; //修改人id'

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName; //'修改人

    @Column(name = "update_time")
    @UpdateTimestamp
    private Date updateTime;//`update_time` DATETIME(3) DEFAULT NULL COMMENT '修改时间',

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private Byte operation;  //操作:0-正常,1-增,2-删,3-改,4-启用,5-停用

    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId;  //修改前记录ID

    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo; //版本号,每次修改+1

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getIotTerminal() {
        return iotTerminal;
    }

    public void setIotTerminal(Integer iotTerminal) {
        this.iotTerminal = iotTerminal;
    }

    public String getDeviceUuid() {
        return deviceUuid;
    }

    public void setDeviceUuid(String deviceUuid) {
        this.deviceUuid = deviceUuid;
    }

    public Byte getTerminalType() {
        return terminalType;
    }

    public void setTerminalType(Byte terminalType) {
        this.terminalType = terminalType;
    }

    public Byte getLoginType() {
        return loginType;
    }

    public void setLoginType(Byte loginType) {
        this.loginType = loginType;
    }

//    public String getTerminalToken() {
//        return terminalToken;
//    }
//
//    public void setTerminalToken(String terminalToken) {
//        this.terminalToken = terminalToken;
//    }
//
//    public Integer getOrgId() {
//        return orgId;
//    }
//
//    public void setOrgId(Integer orgId) {
//        this.orgId = orgId;
//    }
//
//    public Long getAccId() {
//        return accId;
//    }
//
//    public void setAccId(Long accId) {
//        this.accId = accId;
//    }
//
//    public String getMobile() {
//        return mobile;
//    }
//
//    public void setMobile(String mobile) {
//        this.mobile = mobile;
//    }
//
//    public Integer getUserId() {
//        return userId;
//    }
//
//    public void setUserId(Integer userId) {
//        this.userId = userId;
//    }
//
//    public Long getMemberId() {
//        return memberId;
//    }
//
//    public void setMemberId(Long memberId) {
//        this.memberId = memberId;
//    }
//
//    public Byte getTgCode() {
//        return tgCode;
//    }
//
//    public void setTgCode(Byte tgCode) {
//        this.tgCode = tgCode;
//    }
//
//    public String getAppId() {
//        return appId;
//    }
//
//    public void setAppId(String appId) {
//        this.appId = appId;
//    }
//
//    public String getUnionId() {
//        return unionId;
//    }
//
//    public void setUnionId(String unionId) {
//        this.unionId = unionId;
//    }
//
//    public String getOpenId() {
//        return openId;
//    }
//
//    public void setOpenId(String openId) {
//        this.openId = openId;
//    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Byte getOperation() {
        return operation;
    }

    public void setOperation(Byte operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
}