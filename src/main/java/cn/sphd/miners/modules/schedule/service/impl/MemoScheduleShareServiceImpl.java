package cn.sphd.miners.modules.schedule.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.schedule.dao.MemoScheduleDao;
import cn.sphd.miners.modules.schedule.dao.MemoScheduleImageDao;
import cn.sphd.miners.modules.schedule.dao.MemoScheduleShareDao;
import cn.sphd.miners.modules.schedule.dao.MemoScheduleShareRecipientsDao;
import cn.sphd.miners.modules.schedule.entity.*;
import cn.sphd.miners.modules.schedule.service.MemoScheduleHistoryService;
import cn.sphd.miners.modules.schedule.service.MemoScheduleShareService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.uploads.service.UploadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.BeanUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2020/7/31.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class MemoScheduleShareServiceImpl implements MemoScheduleShareService{


    @Autowired
    MemoScheduleShareDao memoScheduleShareDao;
    @Autowired
    MemoScheduleShareRecipientsDao memoScheduleShareRecipientsDao;
    @Autowired
    MemoScheduleDao memoScheduleDao;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    MemoScheduleImageDao memoScheduleImageDao;
    @Autowired
    UploadService uploadService;
    @Autowired
    MemoScheduleHistoryService memoScheduleHistoryService;

    @Override
    public List<Integer> getRecipientIdsByScheduleId(Integer scheduleId) {
        String hql="select recipient from MemoScheduleShareRecipients where scheduleShare.scheduleId=:scheduleId";
        Map<String,Object> map=new HashMap<String,Object>();
        map.put("scheduleId",scheduleId);
        List<Integer> recipientIds=memoScheduleShareRecipientsDao.getListByHQLWithNamedParams(hql,map);
        return recipientIds;
    }

    @Override
    public void addMemoScheduleShareByMemoSchedule(MemoSchedule memoSchedule, User user, Integer... shareableUserIds) {
        MemoScheduleShare memoScheduleShare=new MemoScheduleShare();
        BeanUtils.copyProperties(memoSchedule, memoScheduleShare); //拷贝相同字段内容
        memoScheduleShare.setCreateTime(new Date());
        memoScheduleShare.setUpdateTime(new Date());
        memoScheduleShare.setRecipients(shareableUserIds.length); //分享人数
        memoScheduleShare.setScheduleId(memoSchedule.getId());//日程id
        memoScheduleShare.setCreator(user.getUserID());
        memoScheduleShare.setUpdator(user.getUserID());

        MemoScheduleHistory memoScheduleHistory = memoScheduleHistoryService.saveMemoScheduleHistory(memoSchedule,user,false,"6");//把分享时的 内容生成历史记录 1353P备忘优化 lixu 2025/7/23
        memoScheduleShare.setPreviousId(memoScheduleHistory.getId());

        memoScheduleShareDao.save(memoScheduleShare);

        Integer orders=1;
        for (Integer userId:shareableUserIds){
            MemoScheduleShareRecipients memoScheduleShareRecipients=new MemoScheduleShareRecipients();
            memoScheduleShareRecipients.setCreateTime(memoScheduleShare.getCreateTime());
            memoScheduleShareRecipients.setCreator(memoScheduleShare.getCreator());
            memoScheduleShareRecipients.setCreateName(memoScheduleShare.getCreateName());
            memoScheduleShareRecipients.setOrders(orders);
            memoScheduleShareRecipients.setRecipient(userId);//接受人id
            memoScheduleShareRecipients.setReceiveTime(memoScheduleShare.getCreateTime());//接收时间
            memoScheduleShareRecipients.setScheduleShare(memoScheduleShare);
            memoScheduleShareRecipientsDao.save(memoScheduleShareRecipients);
            orders++;//排序加1

            MemoSchedule shareScedule=new MemoSchedule();// 新增 分享来的备忘
            shareScedule.setNotify(false);//不提醒
            shareScedule.setUser(userId);// 日程归宿人
            shareScedule.setTitle(memoSchedule.getTitle());
            shareScedule.setPlace(memoSchedule.getPlace());
            shareScedule.setDescription(memoSchedule.getDescription());
            shareScedule.setSource("4");   //来源：1-新增,2-修改原任务而来,3-设置新的提醒时间复制而来 4- 分享而来
            shareScedule.setCreator(user.getUserID()); //创建人是主动分享人
            shareScedule.setCreateName(user.getUserName());//创建人是主动分享人
            shareScedule.setShareSource(memoSchedule.getId());//来源:0-原生,其它-分享ID
            shareScedule.setCreateDate(new Date());
            shareScedule.setUpdateDate(new Date());
            shareScedule.setUpdateName(user.getUserName());
            memoScheduleDao.save(shareScedule);

            Integer imageOrders=1;   //分享附件
            for (MemoScheduleImage msi:memoSchedule.getMemoScheduleImageHashSet()){
                MemoScheduleImage memoScheduleImage=new MemoScheduleImage();
                memoScheduleImage.setCreateDate(shareScedule.getCreateDate());
                memoScheduleImage.setCreator(shareScedule.getCreator());
                memoScheduleImage.setCreateName(shareScedule.getCreateName());
                memoScheduleImage.setNormal(msi.getNormal());//正常图片路径
                memoScheduleImage.setOrders(imageOrders);
                memoScheduleImage.setSchedule(shareScedule);
                memoScheduleImageDao.save(memoScheduleImage);
                imageOrders++;

                MemoUsing callback = new MemoUsing(memoScheduleImage.getId(), memoScheduleImage.getClass());
                String name=memoSchedule.getTitle()+"(分享)";
                if (memoSchedule.getMemoScheduleImageHashSet().size()>1){ //多个文件
                    name=name+imageOrders;
                }
                uploadService.addFileUsing(callback, memoScheduleImage.getNormal(), name, user, "备忘与日程");
            }

            userSuspendMsgService.saveUserSuspendMsg(1,user.getUserName()+"向您分享了一条备忘事项","操作时间 "+new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),userId,null,0);
        }

    }

    @Override
    public List<MemoScheduleShare> getMemoScheduleSharesByScheduleId(Integer scheduleId, PageInfo pageInfo) {
        String hql="from MemoScheduleShare where scheduleId=:scheduleId order by createTime desc";
        Map<String,Object> map=new HashMap<String,Object>();
        map.put("scheduleId",scheduleId);
        List<MemoScheduleShare> memoScheduleShares=memoScheduleShareDao.getListByHQLWithNamedParams(hql,map,pageInfo);
        return memoScheduleShares;
    }

    @Override
    public List<MemoScheduleShare> getMemoScheduleSharesByCreator(Integer creator,PageInfo pageInfo) {
        String hql="from MemoScheduleShare where creator=:creator order by createTime desc";
        Map<String,Object> map=new HashMap<String,Object>();
        map.put("creator",creator);
        List<MemoScheduleShare> memoScheduleShares=memoScheduleShareDao.getListByHQLWithNamedParams(hql,map,pageInfo);
        return memoScheduleShares;
    }

    @Override
    public List<MemoScheduleShare> getMemoScheduleSharesByRecipient(Integer recipient, PageInfo pageInfo) {
        String hql="from MemoScheduleShare where id in(select scheduleShareId from MemoScheduleShareRecipients where recipient=:recipient) order by createTime desc";
        Map<String,Object> map=new HashMap<String,Object>();
        map.put("recipient",recipient);
        List<MemoScheduleShare> memoScheduleShares=memoScheduleShareDao.getListByHQLWithNamedParams(hql,map,pageInfo);
        return memoScheduleShares;
    }

    @Override
    public MemoScheduleShare getMemoScheduleShareById(Integer id) {
        return memoScheduleShareDao.get(id);
    }

    @Override
    public void deleteAllMemoScheduleShare(List<MemoScheduleShare> memoScheduleShareList) {
        memoScheduleShareDao.deleteAll(memoScheduleShareList);
    }
}
