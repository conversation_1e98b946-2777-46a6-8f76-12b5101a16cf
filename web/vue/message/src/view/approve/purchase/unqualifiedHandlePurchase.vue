<template>
  <div id="unqualifiedHandlePurchase">
    <TY_NavTop title="不合格品处理" isSearch="true" @toggleList="search" :isBackHome=false></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待处理" name="1" closable='false' :msgCount="waitHandleCount">
        <div class="tip">以下物料被检验不合格，请及时处理！</div>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id, 1)" v-for="(item, index) in waitHandle" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.code}}</div>
                <div>{{item.name}}</div>
                <div>申请入库的数量  {{item.quantity_plan}}{{item.unit}}  到货日期 {{item.arrive_date | formatDay('YYYY-MM-DD')}}</div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="待让步审批" name="2" closable='false'>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id, 2)" v-for="(item, index) in waitCompromiseApprove" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.code}}</div>
                <div>{{item.name}}</div>
                <div>申请入库的数量  {{item.quantity_plan}}{{item.unit}}  到货日期 {{item.arrive_date | formatDay('YYYY-MM-DD')}}</div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="待入库" name="3" closable='false'>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id, 3)" v-for="(item, index) in waitStorage" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.code}}</div>
                <div>{{item.name}}</div>
                <div>申请入库的数量  {{item.quantity_plan}}{{item.unit}}  到货日期 {{item.arrive_date | formatDay('YYYY-MM-DD')}}</div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="数量异议待处理" name="4" closable='false'>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id, 4)" v-for="(item, index) in handleNum" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.code}}</div>
                <div>{{item.name}}</div>
                <div>申请入库的数量  {{item.quantity_plan}}{{item.unit}}  到货日期 {{item.arrive_date | formatDay('YYYY-MM-DD')}}</div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>

<style>
  #unqualifiedHandlePurchase .tabs-tab{
    width: 25%;
  }
  #unqualifiedHandlePurchase .ui-cell{
    font-size: 12px;
  }
</style>

<script>
export default {
  name: 'unqualifiedHandlePurchase',
  data () {
    return {
      tabValue: '1',
      isBackHome: false,
      listenersUid: [],
      waitHandle: [],
      waitCompromiseApprove: [],
      waitStorage: [],
      handleNum: []
    }
  },
  computed: {
    waitHandleCount: function () {
      return this.waitHandle.length
    }
  },
  created: function () {
    this.tabValue = this.$route.params.tabValue || '1'
    let that = this
    this.axios.post('../../../inStock/mtList', {
      oid: that.sphdSocket.user.oid,
      state: '(4)',
      users: 'vb'
    }).then(function (response) {
      let list = response.data.data
      that.waitHandle = list || []
    })
    this.axios.post('../../../inStock/mtList', {
      oid: that.sphdSocket.user.oid,
      state: '(5)',
      users: 'vb'
    }).then(function (response) {
      let list = response.data.data
      that.waitCompromiseApprove = list || []
    })
    this.axios.post('../../../inStock/mtList', {
      oid: that.sphdSocket.user.oid,
      state: '(6)',
      users: 'vb'
    }).then(function (response) {
      let list = response.data.data
      that.waitStorage = list || []
    })
    this.axios.post('../../../inStock/mtList', {
      oid: that.sphdSocket.user.oid,
      state: '(9)',
      users: 'vb',
      approvalType: 2
    }).then(function (response) {
      let list = response.data.data
      that.handleNum = list || []
    })
  },
  methods: {
    jump: function (id, tab) {
      this.$store.dispatch('setMaterialParam', {
        user: 'zbu',
        tab: tab
      })
      this.$router.push({
        path: `/materialDetail/${id}`
      })
    },
    search: function () {
      this.$router.push({
        path: `/purchaseQuery/4`
      })
    }
  }
}
</script>
