<template>
  <div id="payList">
    <TY_NavTop :title="ssTtl" :isBackHome="isBackHome"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="tipPane" v-if="ssType === '1' && (ssMethod === '1' || ssMethod === '5' || ssMethod === '')">{{ssDate}}共收到回款{{ssCount}}次，金额共{{ssAmount}}元</div>
      <div class="tipPane" v-if="ssType === '1' && (ssMethod === '3' || ssMethod === '4')">{{ssDate}}共收到{{ssMethod | incomeType}}{{ssCount}}次，金额共{{ssAmount}}元</div>
      <div class="tipPane" v-if="ssType === '2' && (ssMethod === '3' || ssMethod === '4') && ssApplyer === ''">{{ssDate}}共收到{{ssMethod | incomeType}}{{ssCount}}次，金额共{{ssAmount}}元</div>
      <div class="tipPane" v-if="ssType === '2' && (ssMethod === '3' || ssMethod === '4') && ssApplyer !== ''">{{ssDate}}共收到{{ssApplyer}}经手的{{ssMethod | incomeType}}{{ssCount}}次，金额共{{ssAmount}}元</div>
      <div class="tipPane" v-if="ssType === '2' && (ssMethod === '1' || ssMethod === '5' || ssMethod === '') && ssApplyer === ''">{{ssDate}}共收到回款{{ssCount}}次，金额共{{ssAmount}}元</div>
      <div class="tipPane" v-if="ssType === '2' && (ssMethod === '1' || ssMethod === '5' || ssMethod === '') && ssApplyer !== ''">{{ssDate}}共收到{{ssApplyer}}经手的回款{{ssCount}}次，金额共{{ssAmount}}元</div>
      <div class="ui-cells">
        <a class="ui-cell searchPane" v-on:click="applyTo(item.id)" v-for="(item, index) in screenList" :key="index">
          <div class="backApplyLists">
            <p class="company">{{item.customerName}}</p>
            <p class="listCon"> 本次回款 {{ item.method | incomeType}} {{item.amount}}元</p>
            <p class="listCon">{{item.createName}} {{item.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</p>
          </div>
          <div class="revokeImg" v-if="item.state === '5'">
            <img :src="revokeImg" />
          </div>
        </a>
      </div>
    </div>
  </div>
</template>
<script>
import revokePng from '../../../images/revoke.png'
import { incomeType, formatDate } from '../../../js/common'
export default {
  name: 'paySearchList',
  filters: {
    incomeType,
    formatDate
  },
  data () {
    return {
      ssTtl: '',
      ssType: '',
      ssDate: '',
      ssCount: '',
      ssApplyer: '',
      ssAmount: '',
      ssMethod: '',
      loading: true,
      revokeImg: revokePng,
      screenList: []
    }
  },
  created () {
    let json = localStorage.getItem('collectParams')
    json = JSON.parse(json)
    let resJson = json
    let type = json.type
    let ssArr = []
    if (type === '1') {
      this.ssTtl = '回款录入'
    } else if (type === '2') {
      this.ssTtl = '回款入账'
      this.ssApplyer = json.applyUserName
      delete resJson.applyUserName
    }
    this.ssDate = json.applyDate ? json.applyDate : json.approvalDate
    ssArr = this.ssDate.split('-')
    this.ssDate = ssArr[0] + '年' + ssArr[1] + '月'
    this.ssType = json.type
    this.ssMethod = json.method
    this.$http.post('../../../salesBack/collectQueries.do', resJson, {
      emulateJSON: true
    }).then((response) => {
      let result = response.body.list
      let allAmount = 0
      this.screenList = result
      this.ssCount = this.screenList.length
      for (let a in result) {
        if (result[a].state !== '5') {
          allAmount = allAmount + Number(result[a].amount)
        }
      }
      this.ssAmount = allAmount.toFixed(2)
      this.loading = false
    }).catch(function () {
      this.$message({
        type: 'error',
        message: '系统错误，请重试！'
      })
    })
  },
  methods: {
    applyTo: function (id) {
      this.$router.push({
        path: `/paySearchDetail/${id}`
      })
    }
  }
}
</script>
