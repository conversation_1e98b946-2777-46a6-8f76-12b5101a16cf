package cn.sphd.miners.modules.equipment.mapper;

import cn.sphd.miners.modules.equipment.entity.TEquControlImage;

import java.util.List;

/**
 * Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-04-13
 */
public interface TEquControlImageMapper 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    public TEquControlImage selectTEquControlImageById(Long id);

    /**
     * 查询列表
     * 
     * @param tEquControlImage 
     * @return 集合
     */
    public List<TEquControlImage> selectTEquControlImageList(TEquControlImage tEquControlImage);

    /**
     * 新增
     * 
     * @param tEquControlImage 
     * @return 结果
     */
    public int insertTEquControlImage(TEquControlImage tEquControlImage);

    /**
     * 修改
     * 
     * @param tEquControlImage 
     * @return 结果
     */
    public int updateTEquControlImage(TEquControlImage tEquControlImage);

    /**
     * 删除
     * 
     * @param id 主键
     * @return 结果
     */
    public int deleteTEquControlImageById(Long id);

    /**
     * 批量删除
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTEquControlImageByIds(String[] ids);
}
