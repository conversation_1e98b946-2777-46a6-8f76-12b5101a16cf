package cn.sphd.miners.modules.accountant.mapper;

import cn.sphd.miners.modules.accountant.entity.SubjectBalanceSheet;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by root on 17-5-18.
 */
@Component
public interface SubjectBalanceSheetMapper {


    List<SubjectBalanceSheet> getSubBalanSheetByPeroid(SubjectBalanceSheet subBalanSheet);

    List<SubjectBalanceSheet> getChildrenBalanceSheet(SubjectBalanceSheet childParam);

    /*得到所有一级科目*/
    List<SubjectBalanceSheet> getTopSubject(SubjectBalanceSheet subBalanSheet);

    SubjectBalanceSheet getAccumulative(SubjectBalanceSheet balanceSheetParam);
}
