<template>
  <div class="head">
    <PageHeader title="财会" :showBack="true" :runMyBackFun="true" @myBackFun="backShouYe"></PageHeader>

  </div>

  <p style="margin: 10px 15px;">{{ todayTime }} </p>
  <ul>
    <li class="cai-liGray padding-10-15"><p>资金</p></li>
    <li class="padding-10-30 ta-list2">
      <div>
        <div>
          <span>现金/备用金与银行账户</span>
        </div>
        <div>
          <span>{{ balanceAccount }}</span>
        </div>
        <div @click="dis">
        <span style="display: flex; align-items: center">
              <el-icon><arrow-right/></el-icon>
        </span>
        </div>
      </div>
      <div class="btn_l" :style="{display:disValue}" disabled="true">
        <div class="span" @click="currentDateRange(0)">本日</div>
        <div class="span" @click="currentDateRange(1)">本月</div>
        <div class="span" @click="currentDateRange(2)">本年</div>
        <div class="span" @click="currentDateRange(3)">自定义</div>
      </div>
    </li>
    <li class="padding-10-30 ta-list for_list" v-for="item in list1" :key="item.id">
      <div>
        <span>{{ item.name }}</span>
      </div>
      <span>{{ item.money }}</span>
      <span style="width: 20%;text-align: end;" @click="cmo()" v-if="item.name != '合计'">
        <el-icon><arrow-right/></el-icon>
      </span>

    </li>
  </ul>
  <ul>
    <li class="cai-liGray padding-10-15"><p>借贷等</p></li>
    <li class="padding-10-30 ta-list" v-for="item in list2" :key="item.id">
      <span>{{ item.name }}</span>
      <span>{{ item.money }}</span>
      <span style="width: 20%;
  text-align: end;" @click="loans(item.id)">
             <el-icon><arrow-right/></el-icon>
        </span>
    </li>
  </ul>
  <ul>
    <li class="cai-liGray padding-10-15"><p>其他数据</p></li>
    <li class="padding-10-30 ta-list" v-for="item in list3" :key="item.id">
      <span>{{ item.name }}</span>
      <span>{{ item.money }}</span>
      <span @click="invoice(item.id)" style="width: 20%; text-align: end;" >
              <el-icon><arrow-right/></el-icon>
        </span>
    </li>
  </ul>
</template>

<script>
import finance from '@/mixins/finance.js'
export default {
  name: "caikuai-default",
  mixins: [finance],
  data() {
    return {
      disValue: "none",
      todayTime:'今天是XXXX年X月XX日 星期X',
      balanceAccount: 0,
      list1: [
        {id: 1, name: "手中的转账支票及承兑汇票", money: "XXXXXX.XX",},
        {id: 2, name: "合计", money: "XXXXXX.XX",},
      ],
      list2: [
        {id: 0, name: "贷款",},
        {id: 1, name: "借款",},
        {id: 2, name: "未完结的款项",},
      ],
      list3: [
        {id: 0, name: "报税记录",},
        {id: 1, name: "申领来的发票",},
        {id: 2, name: "银行的账户信息",},
        {id: 3, name: "申领来的银行支票",},
      ],
      headTitle: "财会"
    }
  },
  mounted() {
    this.todayTime = '今天是'+ new Date().format('yyyy年M月d日') + ' ' + this.getWeekday()
    this.getAccounts()
  },
  methods: {
    backShouYe(){
      this.$router.push({ path:'/shouye' })
    },
    getAccounts(){
      let org = this.auth.getOrg()
      this.financeApi.getBalance({ oid:org.id }).then(res1=>{
        console.log('getBalance res1=', res1)
        let res = res1.data.data
        this.balanceAccount = this.formatCurrency(res.balanceAccount)
        let balanceReturn = this.formatCurrency(res.balanceReturn)
        let balanceTotal = this.formatCurrency(res.balanceTotal)
        this.list1[0].money = balanceReturn
        this.list1[1].money = balanceTotal

      }).catch(err=>{
        console.log('err=',err)
        this.$message.error('链接失败，请重试！')
      })
    },
    dis() {
      this.disValue = this.disValue == 'block' ? 'none' : 'block'
    },
    currentDateRange(index) {
      if (index == 0) {
        this.$router.push("ViewByTime")
      } else if (index == 1) {
        this.$router.push("/ViewByTimeB")
      } else if (index == 2) {
        this.$router.push("/ViewByTimeYear")
      } else if (index == 3) {
        this.$router.push("timePicker")
      }

    },
    invoice(index) {
      // this.$message.info('暂未开发，敬请期待！')
      // return false
      if (index == 1) {
        this.$router.push("/invoiceHome")
      } else if (index == 2) {
        // this.$router.push("/accountList")
      }
    },
    loans(index) {
      // this.$message.info('暂未开发，敬请期待！')
      // return false
      if (index === 0) {
        this.$router.push("/loansHome")
      } else if (index === 1) {
        this.$router.push("/borrowMoneyHome")
      } else if (index === 2) {
        this.$router.push("/uncompletedFundHome")
      }
    },
    cmo(){
      this.$router.push("/cmoHome")
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../style/account.scss";

.head {
  color: #ffffff;
  background-color: $tyColorGreen;
  font-weight: 100;
  font-size: 18px;
  //justify-content: center;

  > p {
    font-size: 16px;
    color: #fff;
  }
}

ul {
  span {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .ta-list2 {
    align-items: end;
    display: flex;
    flex-direction: column;


    div {
      display: flex;
    }

    > div:first-child {
      width: 100%;
      display: flex;
      justify-content: space-between;

      div:nth-child(1) {
        width: 50%;
        justify-content: space-between;
        align-items: center;
        //span:first-child {
        //  //width: 30%;
        //  width: 40px;
        //  //display: flex;
        //  justify-content: center;
        //}
      }

      div:nth-child(2) {
        width: 40%;
        display: flex;
        justify-content: center;

        span {
          text-align: center;
          line-height: 25px;
        }
      }

      div:nth-child(3) {
        width: 10%;
        display: flex;
        justify-content: end;
      }

    }

    .btn_l {
      display: none;
      cursor: pointer;
      line-height: 20px;
      height: 30px;
      text-align: right;

      div.span {
        display: inline-block;
        padding: 5px 16px;
        font-size: 16px;
        color:$tyColorBlue;
        text-align: center;
        height: 100%;
        line-height: 30px;
      }
    }
  }

  .for_list {
    div:first-child {
      width: 72%;
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        width: 100%;
      }
    }

    span:nth-child(2) {
      width: 40%;
      display: flex;
      justify-content: center;
      //color: #488fea;
    }

    span:nth-child(3) {
      display: flex;
      width: 10%;
      justify-content: end;
    }


  }

  img {
    width: 20px;
    height: 20px;
    padding-top: 2px;
  }

  .margin-10-15 {
    margin: 10px 15px;
  }
}
</style>