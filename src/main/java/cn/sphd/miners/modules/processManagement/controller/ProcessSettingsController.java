package cn.sphd.miners.modules.processManagement.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.processManagement.dto.SonProcessesDto;
import cn.sphd.miners.modules.processManagement.entity.*;
import cn.sphd.miners.modules.processManagement.service.ProcessSettingsService;
import cn.sphd.miners.modules.system.entity.User;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2023/5/9
 * 1.251工序1
 */
@Controller
@RequestMapping("/processSettings")
public class ProcessSettingsController {

    @Autowired
    ProcessSettingsService processSettingsService;

    /**
    *@Description 跳转工序设置
    *@auther 李娅星
    *@date 2023/5/10
    *@param
    */
    @RequestMapping("/toProcessSettings.do")
    public String toProcessSettings(){
        return "/processmanagement/processSettings";
    }
    
    /**
    *@Description 工序设置列表统计
    *@auther 李娅星,孙文
    *@date 2023/5/11
    *@param
    */
    @ResponseBody
    @RequestMapping("/getProcessSettingsList.do")
    public JsonResult getProcessSettingsList(User user){
        Map<String,Object> map = new HashMap<>();
        List<PpmProcessStat> ppmProcessStatList = processSettingsService.getProcessSettingsList(user);
        map.put("ppmProcessStatList",ppmProcessStatList);
        return new JsonResult(1,map);
    }

    /**
    *@Description 去管理获取列表
    *@auther 李娅星
    *@date 2023/5/11
    *@param type(类型：0-全部 1-产品 2-零组件)
    */
    @ResponseBody
    @RequestMapping("/getToManageList.do")
    public JsonResult getToManageList(User user, Byte type, PageInfo pageInfo){
        Map<String,Object> map = processSettingsService.getToManageList(user,type,pageInfo);
        return new JsonResult(1,map);
    }

    /**
     *@Description 获取选项管理列表
     *@auther 李娅星
     *@date 2023/5/11
     *@param
     */
    @ResponseBody
    @RequestMapping("/getOptionManagements.do")
    public JsonResult getOptionManagements(User user){
        Map<String,Object> map = new HashMap<>();
        Integer processNum = processSettingsService.getProcessNum(user.getOid(),true);  //工序
        Integer sonProcessNum = processSettingsService.getActivityNum(user.getOid(),true,ProcessSettingsService.ActivityType.sonProcess.getIndex()); //子工序
        Integer operateNum = processSettingsService.getActivityNum(user.getOid(),true,ProcessSettingsService.ActivityType.operate.getIndex()); //操作
        map.put("processNum",processNum);
        map.put("sonProcessNum",sonProcessNum);
        map.put("operateNum",operateNum);
        return new JsonResult(1,map);
    }

    /**
    *@Description 创建工序选项
    *@auther 李娅星
    *@date 2023/5/11
    *@param code-代号   name-名称   category-类别id
    */
    @ResponseBody
    @RequestMapping("/createProcess.do")
    public JsonResult createProcess(User user, PpmProcess ppmProcess, PpmActivityCyclicity ppmActivityCyclicity){
        Map<String,Object> map = new HashMap<>();
        if (StringUtils.isNotEmpty(ppmProcess.getCode())&&StringUtils.isNotEmpty(ppmProcess.getName())){
            map = processSettingsService.createProcess(user,ppmProcess, ppmActivityCyclicity);
            return new JsonResult(1,map);
        }else {
            return new JsonResult(new MyException("400", "操作失败，参数错误！"));
        }
    }

    /**
    *@Description 创建子工序/操作选项
    *@auther 李娅星
    *@date 2023/5/12
    *@param type(类型:2-子工序,3-操作,4-套餐) timing(时间点:1-准备工作,2-正式操作,3-收尾工作) isCyclicity(是否周期性1-是,0-否(默认))
    *@param pattern(周期型式1-一定时间内需进行一次或数次,2-一定次数的操作后需进行一次或数次,3-一个或几个班次后需进行一次或数次)   frequency(时长/频次)
    *@param timeUnit(时间单位1-秒,2-分钟,3-小时,4-天) executeTimes(执行次数)
    */
    @ResponseBody
    @RequestMapping("/createActivity.do")
    public JsonResult createActivity(User user, PpmActivity ppmActivity, PpmActivityCyclicity ppmActivityCyclicity){
        Map<String,Object> map = new HashMap<>();
        if (StringUtils.isNotEmpty(ppmActivity.getCode())&&StringUtils.isNotEmpty(ppmActivity.getName())){
            map = processSettingsService.createActivity(user,ppmActivity,ppmActivityCyclicity);
            return new JsonResult(1,map);
        }else {
            return new JsonResult(new MyException("400", "操作失败，参数错误！"));
        }
    }

    /**
    *@Description 新增类别
    *@auther 李娅星
    *@date 2023/5/11
    *@param type(类型1-工序,2-子工序,3-操作,4-套餐)
    */
    @ResponseBody
    @RequestMapping("/createCategory.do")
    public JsonResult createCategory(User user,String name,Integer type){
        Map<String,Object> map = new HashMap<>();
        if (StringUtils.isNotEmpty(name)&&type!=null){
            map = processSettingsService.createCategory(user,name,type);
            return new JsonResult(1,map);
        }else {
            return new JsonResult(new MyException("400", "操作失败，参数错误！"));
        }
    }

    /**
    *@Description 类别管理列表
    *@auther 李娅星
    *@date 2023/5/12
    *@param type(类型1-工序,2-子工序,3-操作,4-套餐)  enabled(1-在用的，0-停用的)
    */
    @ResponseBody
    @RequestMapping("/getCategoryList.do")
    public JsonResult getCategoryList(User user,Byte type,Integer enabled){
        Map<String,Object> map = new HashMap<>();
        if (type!=null&&enabled!=null){
            List<PpmActivityCategory> ppmActivityCategories = processSettingsService.getCategoryList(user.getOid(),type,enabled);
            map.put("ppmActivityCategories",ppmActivityCategories);
            return new JsonResult(1,map);
        }else {
            return new JsonResult(new MyException("400", "操作失败，参数错误！"));
        }
    }

    /**
    *@Description 类别的停用/复用
    *@auther 李娅星
    *@date 2023/5/12
    *@param
    */
    @ResponseBody
    @RequestMapping("/updateCategoryEnabled.do")
    public JsonResult updateCategoryEnabled(User user,Integer categoryId,Integer enabled){
        Map<String,Object> map = new HashMap<>();
        if (categoryId!=null&&enabled!=null){
            map = processSettingsService.updateCategoryEnabled(user,categoryId,enabled);
            return new JsonResult(1,map);
        }else {
            return new JsonResult(new MyException("400", "操作失败，参数错误！"));
        }
    }
    
    /**
    *@Description 获取设置列表
    *@auther 李娅星
    *@date 2023/5/15
    *@param product(产品ID)
    */
    @ResponseBody
    @RequestMapping("/getSettingsList.do")
    public JsonResult getSettingsList(User user,Integer product){
        if (product!=null) {
            Map<String, Object> map = processSettingsService.getSettingsList(user,product);
            return new JsonResult(1, map);
        }else {
            return new JsonResult(new MyException("400", "操作失败，参数错误！"));
        }
    }

    /**
    *@Description 获取工序列表(去掉了已经选择的工序)
    *@auther 李娅星
    *@date 2023/5/15
    *@param product：产品id    categoryId：类别id
    */
    @ResponseBody
    @RequestMapping("/getProcessList.do")
    public JsonResult getProcessList(User user,Integer product,Integer categoryId){
        Map<String,Object> map = new HashMap<>();
        //List<Long> processIds = processSettingsService.getProcessIds(user.getOid(),product);  //某产品下已有的工序id
        List<PpmProcess> ppmProcessList  = processSettingsService.getProcessList(user.getOid(),true,categoryId,null);
        map.put("ppmProcessList",ppmProcessList);
        return new JsonResult(1, map);
    }

    /**
     *@Description 获取子工序列表
     *@auther 李娅星
     *@date 2023/5/15
     *@param
     */
    @ResponseBody
    @RequestMapping("/getSonProcessList.do")
    public JsonResult getSonProcessList(User user,Integer categoryId){
        Map<String,Object> map = new HashMap<>();
        List<PpmActivity> ppmActivityList  = processSettingsService.getActivityList(user.getOid(),true,categoryId,ProcessSettingsService.ActivityType.sonProcess.getIndex(),null,null);
        map.put("ppmActivityList",ppmActivityList);
        return new JsonResult(1, map);
    }

    /**
     *@Description 获取操作列表
     *@auther 李娅星
     *@date 2023/5/15
     *@param
     */
    @ResponseBody
    @RequestMapping("/getOperateList.do")
    public JsonResult getOperateList(User user,Integer categoryId){
        Map<String,Object> map = new HashMap<>();
        List<Map<String,Object>> operateActivityList  = processSettingsService.getActivityByCategoryList(user.getOid(),true,categoryId,ProcessSettingsService.ActivityType.operate.getIndex());  //准备工作
        map.put("operateActivityList",operateActivityList);  //准备工作
        return new JsonResult(1, map);
    }

    /**
     *@Description 进行设置操作(选择一道“工序”的操作)【产品中选择工序后，子工序和操作必须二选一；若选择子工序，则子工序下也必须有操作】
     *@auther 李娅星
     *@date 2023/5/16
     *@param product(产品id)
     */
    @ResponseBody
    @RequestMapping("/chooseProcess.do")
    public JsonResult chooseProcess(User user,String processSJson,Integer product){
        Map<String,Object> map = new HashMap<>();
        if (StringUtils.isNotEmpty(processSJson)&&product!=null){
            map = processSettingsService.chooseProcess(user,product,processSJson);
            return new JsonResult(1, map);
        }else {
            map.put("content","操作失败！一个产品至少需有一道工序，每道工序下至少需有一项操作！");
            return new JsonResult(1, map);
        }
    }

    /**
    *@Description 获取某产品下的工序列表
    *@auther 李娅星
    *@date 2023/5/16
    *@param
    */
    @ResponseBody
    @RequestMapping("/getProductProcessList.do")
    public JsonResult getProductProcessList(User user,Integer product){
        Map<String,Object> map = new HashMap<>();
        List<PpmProcess> ppmProcessList = processSettingsService.getProcessByProductList(user.getOid(),product);
        map.put("ppmProcessList",ppmProcessList);
        return new JsonResult(1, map);
    }

    /**
     *@Description 获取某产品下工序的子工序或操作列表/获取某子工序下的操作列表
     *@auther 李娅星
     *@date 2023/5/24
     *@param parent父活动id(获取某子工序下的操作列表时，此值传子工序id)
     */
    @ResponseBody
    @RequestMapping("/getSonProcessByproductList.do")
    public JsonResult getSonProcessByproductList(User user,Integer product,Long processId,Long parent){
        Map<String,Object> map = new HashMap<>();
        List<SonProcessesDto>  sonProcessList= processSettingsService.getActivityByProductAndProcessId(user.getOid(),product,processId,parent);  //子工序或者操作
        map.put("sonProcessList",sonProcessList);
        return new JsonResult(1, map);
    }

    /**
    *@Description 工序排序
    *@auther 李娅星
    *@date 2023/5/24
    *@param processId：修改的工序id processIdAfter修改的工序要排到此工序id后面 orderStatus：1-排到最前面 0-排到某个工序后面
    */
    @ResponseBody
    @RequestMapping("/updateProcessOrders.do")
    public JsonResult updateProcessOrders(User user,Integer product,Long processId,Long processIdAfter,Integer orderStatus){
        Map<String,Object> map = new HashMap<>();
        if (product!=null&&processId!=null){
            map = processSettingsService.updateProcessOrders(user,product,processId,processIdAfter,orderStatus);
            return new JsonResult(1, map);
        }else {
            return new JsonResult(new MyException("400", "操作失败，参数错误！"));
        }
    }

    /**
     *@Description 工序下的子工序或操作排序/子工序下的操作排序
     *@auther 李娅星
     *@date 2023/5/24
     *@param sonProcessId：修改的子工序或操作id sonProcessIdAfter修改的工序要排到此工序id后面 orderStatus：1-排到最前面 0-排到某个子工序/操作后面
     *@param parent：子工序下的操作排序时的子工序id
     */
    @ResponseBody
    @RequestMapping("/updateSonProcessOrders.do")
    public JsonResult updateSonProcessOrders(User user,Integer product,Long processId,Long parent,Long sonProcessId,Long sonProcessIdAfter,Integer orderStatus){
        Map<String,Object> map = new HashMap<>();
        if (product!=null&&processId!=null){
            map = processSettingsService.updateSonProcessOrders(user,product,processId,parent,sonProcessId,sonProcessIdAfter,orderStatus);
            return new JsonResult(1, map);
        }else {
            return new JsonResult(new MyException("400", "操作失败，参数错误！"));
        }
    }

    /**
    *@Description 工序删除
    *@auther 李娅星
    *@date 2023/5/24
    *@param
    */
    @ResponseBody
    @RequestMapping("/deleteProcess.do")
    public JsonResult deleteProcess(Integer product,Long processId){
        Map<String,Object> map = new HashMap<>();
        if (product!=null&&processId!=null) {
            map = processSettingsService.deleteProcess(product,processId);
            return new JsonResult(1, map);
        }else {
            return new JsonResult(new MyException("400", "操作失败，参数错误！"));
        }
    }

    /**
     *@Description 子工序/操作/以及子工序下的操作进行删除
     *@auther 李娅星
     *@date 2023/5/24
     *@param activityId：要删除的子工序或者操作或者子工序下的某操作
     */
    @ResponseBody
    @RequestMapping("/deleteSonProcess.do")
    public JsonResult deleteSonProcess(Integer product,Long processId,Long parent,Long activityId){
        Map<String,Object> map = new HashMap<>();
        if (product!=null&&processId!=null&&activityId!=null) {
            map = processSettingsService.deleteSonProcess(product,processId,parent,activityId);
            return new JsonResult(1, map);
        }else {
            return new JsonResult(new MyException("400", "操作失败，参数错误！"));
        }
    }

    /**
     *@Description 工序设置列表修改是否带有子工序的状态
     *@auther zsx
     *@date 2024/10/15
     *@param
     */
    @ResponseBody
    @RequestMapping("/upProcessSettingSonState.do")
    public JsonResult upProcessSettingSonState(User user){
        Map<String,Object> map = new HashMap<>();
        List<PpmProcessStat> ppmProcessStatList = processSettingsService.updateProcessSettingsSonProcess(user);
        map.put("ppmProcessStatList",ppmProcessStatList);
        return new JsonResult(1,map);
    }


    /**
     *@Description 测试
     *@auther 李娅星
     *@date 2023/5/24
     *@param
     */
    @ResponseBody
    @RequestMapping("/test.do")
    public JsonResult test(User user){
        Map<String,Object> map = new HashMap<>();
        List<Map<String, Object>> mapList = processSettingsService.getPdBaseList(user,"2");
        return new JsonResult(1, mapList);

    }
}
