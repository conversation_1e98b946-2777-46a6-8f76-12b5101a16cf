import { fileURLToPath, URL } from 'url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
// import vueJsx from '@vitejs/plugin-vue-jsx'
import Components from "unplugin-vue-components/vite";
//
//
// const AutoImport = require('unplugin-auto-import/webpack');
// const Components = require('unplugin-vue-components/webpack');

// 引入插件
// import AutoImport from 'unplugin-auto-import/vite';
const { ElementPlusResolver } = require('unplugin-vue-components/resolvers');

// https://vitejs.dev/config/
export default ({command, mode}) => {
  const envConfig = loadEnv(mode, './', ['VITE_', 'VUE_'])
  console.log('envConfig',envConfig)
  let config = {
    plugins: [
        vue(),
      // vueJsx(),
      // AutoImport({
      //   resolvers: [ElementPlusResolver()],
      //   imports: ['vue', 'vue-router']
      // }),
      Components({
        resolvers: [ElementPlusResolver()],
      }),
    ],
    resolve: {
      dedupe: [
        'vue'
      ],
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    define: {
      'process.env': envConfig
    },
    base: './',
    publicPath: './',
    build: {
      chunkSizeWarningLimit: 65536,
      target: "es2022",
      brotliSize: false,
      // rollupOptions: {
      //   external: [
      //     "element-plus"
      //   ]
      // }
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: "@import 'src/style/common.scss';"
        }
      }
    }
  }
  return defineConfig(config)
}
