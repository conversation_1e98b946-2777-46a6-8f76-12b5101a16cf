<template>
  <div>
    <p class="h35f14 f4 margin_top_16">
      <span>按发票类别展示如下：</span>
    </p>
    <ul v-if="list.specialInvoice&&list.ordinaryInvoice&&list.otherInvoice">
      <li class="ty-fff" @click="rou(1)">
        <p>增值税专用发票</p>
        <p>
          <span>{{ list.specialInvoice.num }}张</span>
          <span>{{ list.specialInvoice.totalAmount ? list.specialInvoice.totalAmount : 0 }}元</span>
        </p>
        <div>
          <el-icon style="font-size: 20px">
            <arrow-right/>
          </el-icon>
        </div>
      </li>
      <li class="f4" @click="rou(2)">
        <p>增值税普通发票</p>
        <p>
          <span>{{ list.ordinaryInvoice.num }}张</span>
          <span>{{ list.ordinaryInvoice.totalAmount ?　list.ordinaryInvoice.totalAmount
          :
          0 }}元</span>
        </p>
        <div>
          <el-icon style="font-size: 20px">
            <arrow-right/>
          </el-icon>
        </div>
      </li>
      <li class="ty-fff" @click="rou(3)">
        <p>其他发票</p>
        <p>
          <span>{{ list.otherInvoice.num }}张</span>
          <span>{{ list.otherInvoice.totalAmount }}元</span>
        </p>
        <div>
          <el-icon style="font-size: 20px">
            <arrow-right/>
          </el-icon>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import {getInvoiceStatistics} from '@/api/financeBill'

export default {
  name: "category",
  data() {
    return {
      list: {},
    }
  },
  methods: {
    rou(index) {
      if (this.invoiceTypeList.typeMonth == 1) {
        const beginDate = this.list.beginTime
        const endDate = this.list.endTime
        let invoiceType = {type: index, beginDate, endDate}
        localStorage.setItem('invoiceType', JSON.stringify(invoiceType))
        this.$router.push('/invoiceMonthly')
      } else if (this.invoiceTypeList.typeMonth == 2) {
        const beginDate = this.list.beginTime
        const endDate = this.list.endTime
        let invoiceDetailsMonthly = {
          type: index,
          beginDate,
          endDate,
        }
        localStorage.setItem('invoiceDetailsMonthly', JSON.stringify(invoiceDetailsMonthly))
        this.$router.push("/invoiceDetailsList")
      }
    },
    getList(invoiceTypeList) {
      const params = {
        beginDate: invoiceTypeList.beginDate,
        typeMonth: invoiceTypeList.typeMonth,
        dateType: invoiceTypeList.dateType
      }
      getInvoiceStatistics(params).then(res1 => {
        this.list = res1.data.data
      }).catch(error => {
        console.error('Error:', error);
      });
    }
  },
  mounted() {
    let invoiceTypeList = localStorage.getItem('invoiceTypeList');
    this.invoiceTypeList = JSON.parse(invoiceTypeList);
    console.log(this.invoiceTypeList, 'invoiceTypeListinvoiceTypeListinvoiceTypeList')
    this.getList(this.invoiceTypeList)
  },
}
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";

ul {
  > li {
    display: flex;
    line-height: 22px;
    padding: 8px 15px;
    justify-content: space-between;

    > :nth-child(1) {
      width: 30%;
    }

    > :nth-child(2) {
      width: 50%;
      text-align: left;
      display: flex;
      justify-content: space-between;

      > span {
        width: 48%;
        text-align: end;
        word-break: break-all;
        white-space: normal;
      }
    }

    > div:last-child {
      width: 10%;
      display: flex;
      justify-content: end;
    }
  }
}

.h35f14 {
  padding: 2px 15px;
}
</style>