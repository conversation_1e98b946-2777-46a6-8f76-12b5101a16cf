package cn.sphd.miners.modules.accountantReportTax.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTriflesEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTriflesPeriodEntity;

import java.io.Serializable;
import java.util.List;

public interface AcctTriflesPeriodDao extends IBaseDao<AcctTriflesPeriodEntity,Serializable> {
    List<AcctTriflesPeriodEntity> getTriflesPeriodList(Integer id);
}
