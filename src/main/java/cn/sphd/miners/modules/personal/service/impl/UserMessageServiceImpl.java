package cn.sphd.miners.modules.personal.service.impl;


import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.DateUtils;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.personal.dao.*;
import cn.sphd.miners.modules.personal.entity.*;
import cn.sphd.miners.modules.personal.service.UserMessageService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.dao.CodeCategoryDao;
import cn.sphd.miners.modules.system.dao.CodeDao;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2015/10/12.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class UserMessageServiceImpl implements UserMessageService {
    @Autowired
    UserMessageDao userMessageDao;
    @Autowired
    PersonnelOvertimeDao personnelOvertimeDao;
    @Autowired
    PersonnelLeaveDao personnelLeaveDao;
    @Autowired
    PersonnelReimburseDao personnelReimburseDao;
    @Autowired
    CodeDao codeDao;
    @Autowired
    CodeCategoryDao codeCategoryDao;
    @Autowired
    UserDao userDao;
    @Autowired
    PersonnelLeaveItemDao personnelLeaveItemDao;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;


    @Override
    public List<UserMessage> findUserMessageByUserId(Integer uid, Integer state) {
        String hql=" and o.messageType=1 and o.user="+uid;
        if (state!=null&&state!=0){
            hql+=" and o.approvalStatus="+state;
        }else {
            hql+=" and o.approvalStatus=1";
        }
        Map<String,String> orderBy = new HashMap<String, String>();
        orderBy.put("createDate","desc");
        return userMessageDao.findCollectionByConditionNoPage(hql,null,orderBy);
    }

    @Override
    public List<UserMessage> getUserMessageBySenderId(Integer sid, Integer state) {
        String hql=" and o.messageType=1 and o.sender="+sid;
        if (state!=null&&state!=0){
            hql+=" and o.approvalStatus="+state;
        }else {
            hql+=" and o.approvalStatus=1";
        }
        Map<String,String> orderBy = new HashMap<String, String>();
        orderBy.put("createDate","desc");
        return userMessageDao.findCollectionByConditionNoPage(hql,null,orderBy);
    }

    @Override
    public List<UserMessage> getUserMessageByAccountId(Integer accountId, Integer state) {
        String hql=" and o.messageType=1 and o.accountId="+accountId;
        if (state!=null){
            hql+=" and o.approvalStatus="+state;
        }
        Map<String,String> orderBy = new HashMap<>();
        orderBy.put("createDate","desc");
        return userMessageDao.findCollectionByConditionNoPageByOne(hql,null,orderBy);
    }

    //wyu：目前只有会计使用此函数，本次修改增加了消息数变更的推送，********。
    @Override
    public void addUserMassage(UserMessage userMessage) {
        String messageType="";
        if (userMessage.getMessageType().equals("1")){
            messageType="财务数据修改";
        }else if (userMessage.getMessageType().equals("2")||userMessage.getMessageType().equals("9")){
            messageType="加班";
        }else if (userMessage.getMessageType().equals("3")||userMessage.getMessageType().equals("10")){
            messageType="请假";
        }else if (userMessage.getMessageType().equals("4")){
            messageType="报销";
        }else if (userMessage.getMessageType().equals("5")){
            messageType="入库";
        }else if (userMessage.getMessageType().equals("12")){
            messageType="会计报税";
        }else if (userMessage.getMessageType().equals("14")){
            messageType="项目管理";
        }
        String cont="";
        if (messageType.equals("会计报税")) {
            cont = userMessage.getIllustrate();
        }
        else
        {
            //=1 为申请结果  否则是需要处理的申请
            if (userMessage.getIsNull()!=null&&"1".equals(userMessage.getIsNull())) {
                cont = userMessage.getUser().getUserName()+"在" + userMessage.getUser().getOrganization().getName() + "机构" + "的" + messageType + "申请结果";
            }else {
                cont = "来自" + userMessage.getUser().getOrganization().getName() + "机构员工" + userMessage.getUser().getUserName() + "的" + messageType + "申请";
            }
        }
        userMessageDao.save(userMessage);
        User receiveUser = userDao.get(userMessage.getReceiveUserId());
        Long userMessageNumber = getMessageCountByUidAndState(receiveUser.getUserID(), 1);
        clusterMessageSendingOperations.convertAndSendToUser(receiveUser.getUserID().toString(), "/userMessageNumber",messageType, cont, receiveUser.getOid(), receiveUser.getOrganization().getName(), userMessageNumber);


//        String token=this.getUser(userMessage.getReceiveUserId()).getTokenId();//接收消息人的设备
//        Integer messageSize=this.getUserMessageSize(1,userMessage.getReceiveUserId());//接收消息人的 未处理消息总数
//        String deviceType=this.getUser(userMessage.getReceiveUserId()).getDeviceType();
//        Xinge.postXinge(deviceType,messageType,cont,token,messageSize);

//        //ios  2200265520L  f62de34cfae06eb577d06033c67ef60c61185cbd35c33ad49d0ab55185f6e149
//        if ("1".equals(this.getUser(userMessage.getReceiveUserId()).getDeviceType())){
////            System.out.println(XingeApp.pushTokenIos(2200265520L,"9e6a2f2e7aa8f606131f0caffd4737a7",cont,token,XingeApp.IOSENV_DEV));
//            System.out.println(Xinge.pushTokenIosAndActivity(2200265520L,"9e6a2f2e7aa8f606131f0caffd4737a7",cont, token,messageSize));
//        }
//
//        //安卓  2100218021  fd6fe0451ea051f34965a938bbcfdbf900f6a753
//        if ("2".equals(userMessage.getUser().getDeviceType())) {
////            System.out.println(XingeApp.pushTokenAndroid(2100218021, "11c7b6b4882b3d1076a84d9f0fbaf63a",cont, "没毛病", token));
//            System.out.println(Xinge.pushTokenAndroidAndActivity(2100218021, "11c7b6b4882b3d1076a84d9f0fbaf63a", cont, "没毛病", token,"com.sphd.kij.systemmanger.activity.request.dealwithrequest.DealWithRequestActivity",messageSize));
//
//        }
////        Xinge.pushTokenAndroidAndActivity(2100218021, "-1",userMessage.getUser().getUserName()+"的"+messageType+"申请", "能行吗", "fd6fe0451ea051f34965a938bbcfdbf900f6a753","com.sphd.kij.systemmanger.activity.request.dealwithrequest.DealWithRequestActivity");
//


    }


    @Override
    public Long getMessageCountByUidAndState(Integer uid, Integer status) {
        HashMap<String,Object> params = new HashMap<>();
        StringBuffer hql=new StringBuffer("select count(*) from UserMessage");
        String link=" where ";
        if (uid!=null){
            params.put("receiveUserId",uid);
            hql.append(link).append("receiveUserId=:receiveUserId");
            link=" and ";
        }
        if (status!=null){
            params.put("state",status);
            hql.append(link).append("state=:state");
        }
        return (Long)userMessageDao.getByHQLWithNamedParams(hql.toString(),params);
    }

    @Override
    public UserMessage getUserMessageById(Integer id) {
        return userMessageDao.get(id);
    }

    @Override
    public List<UserMessage> getManyMessageByMtStock(Integer mtStockId) {
        String hql="from UserMessage o where o.mtStockId="+mtStockId+" and o.messageType=5 order by o.createDate desc";
        return userMessageDao.getListByHQL(hql);
    }

    @Override
    public void deleteMessage(UserMessage userMessage) {
        userMessageDao.delete(userMessage);
    }

    @Override
    public void updateUserMassage(UserMessage userMessage) {
        userMessageDao.update(userMessage);
    }


    @Override
    public void updatePersonnelOvertime(PersonnelOvertime personnelOvertime) {
        personnelOvertimeDao.update(personnelOvertime);
    }

    @Override
    public void updatePersonnelLeave(PersonnelLeave personnelLeave) {
        personnelLeaveDao.update(personnelLeave);
    }

    @Override
    public PersonnelLeave getPersonnelLeaveById(Integer id) {
        return personnelLeaveDao.get(id);
    }

    @Override
    public PersonnelReimburse getPersonnelReimburseById(Integer id) {
        return personnelReimburseDao.get(id);
    }


    @Override
    public UserMessage getUserMessageByMessageId(Integer messageId,String messageType) {
        String hql = " from UserMessage o where o.isNull = null and o.messageId = "+messageId;
        if (!"".equals(messageType) && messageType!=null){
            hql+=" and o.messageType='"+messageType+"'";
        }
        UserMessage userMessage = userMessageDao.getByHQL(hql);
        return userMessage;
    }

    @Override
    public UserMessage getUserMessageByMessageIdAndUserId(Integer messageId, String messageType, Integer UserId) {
        String hql = " from UserMessage o where o.isNull = null and o.messageId = "+messageId;
        if (!"".equals(messageType) && messageType!=null){
            hql+=" and o.messageType='"+messageType+"'";
        }
        hql += " and o.handleId = '"+ UserId +"'";
        UserMessage userMessage = userMessageDao.getByHQL(hql);
        return userMessage;
    }

    @Override
    public List<UserMessage> getUserMessageListByMessageId(Integer messageId, String messageType) {
        String hql = " from UserMessage o where o.messageType='"+messageType+"' and o.isNull = null and o.messageId = "+messageId;
        List<UserMessage> userMessages = userMessageDao.getListByHQL(hql);
        return userMessages;
    }

    @Override
    public List<UserMessage> getUserMessageListDelete(Integer messageId, String messageType) {
        String hql = " from UserMessage o where 1=1";
        if (messageId!=null){
            hql+=" and o.messageId = "+messageId;
        }
        if (messageType!=null){
            hql+=" and o.messageType = "+messageType;
        }
        List<UserMessage> userMessages = userMessageDao.getListByHQL(hql);
        return userMessages;
    }


    @Override
    public void updatePersonnelLeaveItem(PersonnelLeaveItem personnelLeaveItem) {
        personnelLeaveItemDao.update(personnelLeaveItem);
    }

    @Override
    public PersonnelLeaveItem getPersonnelLeaveItemById(Integer id) {
        return personnelLeaveItemDao.get(id);
    }

    @Override
    public List<PersonnelLeaveItem> getPersonnelLeaveItemByLeaveId(Integer leaveId) {
        Map<String,String> orderBy = new HashMap<String, String>();
        String hql = "";
        if(leaveId!=null){
            hql+=" and o.leaveId = "+leaveId;
        }
        orderBy.put("id","desc");
        return personnelLeaveItemDao.findCollectionByConditionNoPage(hql,null,orderBy);
    }


    /**
     * <AUTHOR>
     * @Date 2018/07/04 10:10
     * 根据消息类型、messageId、handleId、approvalStatus来获取某条消息
     */
    @Override
    public UserMessage getMesByUserAndType(Integer messageId, String messageType, Integer UserId, String approvalStatus, Integer state) {
        String hql = " from UserMessage o where o.messageId = "+messageId;
        if (!"".equals(messageType) && messageType!=null){
            hql+=" and o.messageType='"+messageType+"'";
        }
        if (!"".equals(approvalStatus) && approvalStatus!=null){
            hql+=" and o.approvalStatus='"+approvalStatus+"'";
        }
        hql += " and o.handleId = '"+ UserId +"' and o.state = " +state;
        UserMessage userMessage = userMessageDao.getByHQL(hql);
        return userMessage;
    }

    @Override
    public void updateUserMessagesByReceiveUserId(Integer oldUserId, Integer newUserId) {
        String hql="update UserMessage set receiveUserId=:newUserId where receiveUserId=:oldUserId";
        Map<String,Object> map = new HashMap<>();
        map.put("newUserId",newUserId);
        map.put("oldUserId",oldUserId);
        userMessageDao.queryHQLWithNamedParams(hql,map);
    }


    @Override   //applicantId 申请人id
    public void approvalUserMessage(UserMessage userMessage1,User user,Integer approvalStatus,String eventType, String messageType,ApprovalProcess approvalProcess,Integer personnelReimburId,String reason) {
        //   批准/驳回后此审批者查看的消息改为已读状态
        if (userMessage1!=null) {
            if (approvalStatus==1){
                userMessage1.setApprovalStatus(2);
            }else {
                userMessage1.setApprovalStatus(3);
                userMessage1.setHandleReply(reason);
            }
            userMessage1.setState(2);
            userMessage1.setHandleId(user.getUserID().toString());
            userMessage1.setHandleName(user.getUserName());
            userMessage1.setHandleTime(new Date());
            userMessageDao.update(userMessage1);
        }

        //   批准/驳回后给申请者的消息
        UserMessage userMessage = new UserMessage();
        if (userMessage1.getUser()!=null){
            userMessage.setUser(userMessage1.getUser());  //申请人
        }

        if (approvalStatus==1){   // 消息中的审批状态  审批批准
            userMessage.setApprovalStatus(2);
        }else {   //审批驳回
            userMessage.setApprovalStatus(3);
        }
        userMessage.setHandleId(approvalProcess.getToUser().toString());
        userMessage.setMessageId(approvalProcess.getId());
        userMessage.setEventType(eventType);
        userMessage.setPersonnelReimburId(personnelReimburId);  //加班、请假、报销等的id
        userMessage.setIllustrate(userMessage1.getIllustrate());
        userMessage.setMessageType(messageType);//消息类型  1-财务  2-计划加班  3-计划请假   4-报销 5-入库 6-出库，7-投诉，8-讨论 9-申报加班 10-提前结束请假
        userMessage.setState(1);
        userMessage.setReceiveUserId(userMessage1.getUser().getUserID());  //接收消息人(申请人id)
        userMessage.setIsNull("1");  //为空时在请求和申请列表展示，不为空只是消息通知，不在前两个列表展示。
        userMessage.setCreateDate(new Date());
        userMessageDao.save(userMessage);
    }

    @Override
    public List<UserMessage> findUserMessageByUserIdAndStateAndMessageState(Integer oldId, Integer state, Integer messageType) {
        String hql = " from UserMessage o where 1=1";

        if (oldId!=null){
            hql+=" and o.receiveUserId = "+oldId;
        }
        if (state!=null)
            hql+=" and o.state = "+state;
        if (messageType!=null)
            hql+=" and o.messageType = "+messageType;
        Date thisMonthEnd = NewDateUtils.getLastTimeOfMonth(new Date());
        Date lastMonthStart = DateUtils.getLastMonthFirstDay();
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        hql +=" and o.createDate between '"+sd.format(lastMonthStart)+"' and '"+sd.format(thisMonthEnd)+"'";
        List<UserMessage> userMessages = userMessageDao.getListByHQL(hql);
        return userMessages;
    }

    @Override
    public List<UserMessage> getUserMessageListHou() {
        String hql="from UserMessage";
        PageInfo pageInfo=new PageInfo(1,500);
        List<UserMessage> userMessageList= userMessageDao.getListByHQLWithNamedParams(hql,null,pageInfo);
        return userMessageList;
    }
}
