package cn.sphd.miners.modules.generalAffairs.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceOldService;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgService;
import cn.sphd.miners.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.*;

/**
 * Created by Administrator on 2017/11/28.
 * 岗位管理[待此版本完成后，需将此controller内的所有相关的接口在generalController中删除]
 */
@Controller
@RequestMapping("/post")
public class PostController {

    @Autowired
    OrgService orgService;
    @Autowired
    UserService userService;
    @Autowired
    WorkAttendanceOldService workAttendanceOldService;

    /**
     *<AUTHOR>
     *@date 2017/11/29 14:07
     *跳转岗位管理的页面
     */
    @RequestMapping("/postManagement.do")
    public String postManagement(){
        return "/generalAffairs/postManagement";
    }

    /**
     *<AUTHOR>
     *@date 2017/11/28 17:58     修改 2020-09-16
     *岗位管理(获取第一级的岗位或者部门----获取某部门详情及子部门)
     * orgType 1-公司，2-部门，3—岗位
     * orgId(如果点击岗位管理，则此值为机构id；如果是查看某一个部门信息及子部门，则此值为部门id)
     * userId 登陆人id
     */
    @ResponseBody
    @RequestMapping("/toPostManagement.do")
    public JsonResult toPostManagement(Integer orgType, Integer orgId, User user) throws InvocationTargetException, IllegalAccessException {
        Map<String,Object> map = orgService.getPostDepartments(orgId,orgType,user.getUserID());
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2017/11/29 11:27
     *同级部门提交
     * orgId 传值，则为同级部门添加；若传空，则为创建第一级部门
     */
    @ResponseBody
    @RequestMapping("/sameLevelSubmit.do")
    public void sameLevelSubmit(Integer orgId,String orgName,Integer userId,HttpServletResponse rsponse,User user) throws IOException, InvocationTargetException, IllegalAccessException {
        Map<String,Object> map = new HashMap<>();
        if (orgName!=null && !"".equals(orgName)){
            Organization organization1 = new Organization();
            List<Organization> organizations = new ArrayList<>();
            if (orgId!=null){
                Organization organization = orgService.getOrgByOid(orgId, OrgService.OrgType.department);
                organization1.setPid(organization.getPid());
                if (organization.getLevel()!=null){
                    organization1.setLevel(organization.getLevel());
                    if (organization.getLevel()>1){
                        organization1.setFirstOrg(organization.getFirstOrg());
                    }
                }else {
                    organization1.setLevel(1);
                }
                organizations = orgService.getDepartmentByPidAndName(organization.getPid(),orgName,2);
            }else {
                orgId = user.getOid();
                organization1.setPid(orgId);
                organization1.setLevel(1);
                organizations = orgService.getDepartmentByPidAndName(orgId,orgName,2);
            }
            organization1.setName(orgName);
            organization1.setEstablishDate(new Date());
            organization1.setOrgType(2);

            if (organizations.size()>0){
                map.put("status",2);  //同级中有相同的部门名称，请进行更换
            }else {
                organization1.setAdefault(false);  //是否是销售部
                organization1.setEnableTime(NewDateUtils.today(new Date()));
                organization1.setOperation("1");
                organization1.setUserNum(0);
                organization1.setChildNum(0);
                organization1.setDisabledChildNum(0);
                organization1.setCreateDate(new Date());
                organization1.setCreateName(user.getUserName());
                organization1.setCreator(userId);
                orgService.saveOrg(organization1);
                orgService.getDeptToHistory(organization1,orgName,user,"1",new Date());
                map.put("status",1);  //添加成功
            }
        }else {
            map.put("status",0);  //添加失败
        }
        ObjectToJson.objectToJson1(map,new String[]{},rsponse);
    }

    /**
     *<AUTHOR>
     *@date 2017/11/29 14:43
     *添加子级部门时，获取上级的部门
     */
    @ResponseBody
    @RequestMapping("/addChildren.do")
    public void addChildren(Integer pid,HttpServletResponse rsponse) throws IOException {
        Map<String,Object> map = new HashMap<>();
        List<Organization> organizations = orgService.getAllDepartmentById(pid);
        map.put("organizations",organizations);
        ObjectToJson.objectToJson1(map,new String[]{"roles","user","area","aId","financeAccounts","accountDetails","accountPeriods",
                "mtCategoryOrg","financeReturnHashSet","financeChequeHashSet","orgPopedomHashSet","defaulOrg","mainPhone","areaName",
                "teacherNum","regNum","year","flag","allStudent","debit","number","money","allNumber","allMoney","newDate",
                "credit","previousBalance","balance","allDebit","allCredit","allPreviousBalance","allBalance","isDefault"},rsponse);
    }

    /**
     *<AUTHOR>
     *@date 2017/11/29 15:17
     *子级部门提交（添加子级部门）
     */
    @ResponseBody
    @RequestMapping("/childrenSubmit.do")
    public void childrenSubmit(String orgChildName,Integer orgId,User user,HttpServletResponse response) throws IOException, InvocationTargetException, IllegalAccessException {
        Map<String,Object> map = new HashMap<>();
        if (orgId!=null && !"".equals(orgChildName)){
            Organization organization = orgService.getOrgByOid(orgId, OrgService.OrgType.department);
//            User user = userService.getUserByID(userId);
            List<Organization> organizations = orgService.checkDepartmentByPId(orgId,2);  //判断子级部门新增不可超过八级的，包括停用的部门（已确定过2020-11-16）
            if (organizations.size()>=8){
                map.put("status",2);  //已经第八级了,不能再增加了
            }else {
                Organization organization1 = new Organization();
                organization1.setPid(organization.getId());
                organization1.setName(orgChildName);
                organization1.setOrgType(2);
                organization1.setEstablishDate(new Date());
                organization1.setEnableTime(NewDateUtils.today(new Date()));
                organization1.setOperation("1");
                organization1.setUserNum(0);
                organization1.setChildNum(0);
                organization1.setDisabledChildNum(0);
                organization1.setAdefault(false);  //是否是销售部
                organization1.setCreateDate(new Date());
                organization1.setCreateName(user.getUserName());
                organization1.setCreator(user.getUserID());
                if (organization.getLevel()!=null){
                    organization1.setLevel(organization.getLevel() + 1);
                    if (organization.getLevel()==1){
                        organization1.setFirstOrg(organization.getId());
                    }else {
                        organization1.setFirstOrg(organization.getFirstOrg());
                    }
                }else {
                    organization1.setLevel(1);
                }
                List<Organization> organizations1 = orgService.getDepartmentByPidAndName(orgId,orgChildName,2);
                if (organizations1.size()>0){
                    map.put("status",3);  //同级中有相同的部门名称，请进行更换
                }else {
                    organization.setChildNum(organization.getChildNum()==null?1:organization.getChildNum()+1);
                    orgService.updateOrg(organization);

                    orgService.saveOrg(organization1);
                    orgService.getDeptToHistory(organization1,orgChildName,user,"1",new Date());
                    map.put("status",1);  //添加成功
                }
            }
        }else {
            map.put("status",0);  //添加失败
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    /**
     *<AUTHOR>
     *@date 2017/12/1 17:25
     *编辑界面提交(编辑部门)
     */
    @ResponseBody
    @RequestMapping("/editSubmit.do")
    public void editSubmit(Integer orgId,String orgName,User user,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        if (orgId!=null){
            Organization organization = orgService.getOrgByOid(orgId, OrgService.OrgType.department);
            organization.setName(orgName);
            organization.setUpdateDate(new Date());
            organization.setUpdator(user.getUserID());
            organization.setUpdateName(user.getUserName());

            List<Organization> organizations = orgService.getDepartmentByPidAndName(organization.getPid(),orgName,2);
            if (organizations.size()>1){
                map.put("status",2);  //同级中有相同的部门名称，请进行更换
            }else {
                orgService.updateOrg(organization);

                Integer oid= user.getOid();
                List<User> users=userService.getUserByOid(oid,null);
                for (User u:users){
                    if (u.getDepartment()!=null && !"".equals(u.getDepartment())) {
                        if (Integer.decode(u.getDepartment()).equals(organization.getId())) {
                            u.setDepartName(organization.getName());//变更该部门下所有员工的部门
                            userService.updateUser(u);
                        }
                    }
                }
                map.put("status",1);  //修改成功
            }
        }else {
            map.put("status",0);  //修改失败
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    /**
     *<AUTHOR>
     *@date 2017/12/4 9:19
     * @date 2020/09/16
     *删除部门
     */
    @ResponseBody
    @RequestMapping("/deleteDepartment.do")
    public JsonResult deleteDepartment(Integer orgId) {
        Map<String,Object> map = new HashMap<>();
        if (orgId!=null){
            map = orgService.deleteOrganization(orgId);

        }else {
            map.put("status",0);  //删除失败
            map.put("content","操作失败");
        }
        return new JsonResult(1,map);
//        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    /**
     *<AUTHOR>
     *@date 2017/12/4 11:29
     *查询某部门或某职位
     * orgType 1-公司，2-部门，3—岗位
     */
//    @ResponseBody
//    @RequestMapping("/getDepartment.do")
//    public void getDepartment(Integer orgId,HttpServletResponse response) throws IOException {
//        Map<String,Object> map = new HashMap<>();
//        Organization organization=orgService.getOrgByOid(orgId);
//        map.put("organization",organization);
//        ObjectToJson.objectToJson1(map,new String[]{"roles","user","area","aId","financeAccounts","accountDetails","accountPeriods",
//                "mtCategoryOrg","financeReturnHashSet","financeChequeHashSet","orgPopedomHashSet","defaulOrg","mainPhone","areaName",
//                "teacherNum","regNum","year","flag","allStudent","debit","number","money","allNumber","allMoney","newDate",
//                "credit","previousBalance","balance","allDebit","allCredit","allPreviousBalance","allBalance","isDefault"},response);
//    }

    /**
     * <AUTHOR>
     * @Description   查看某部门中的职工
     * @Date 2020/9/16
     */
    @ResponseBody
    @RequestMapping("/getUsers.do")
    public JsonResult getUsers(Integer deptId){
        List<User> users=userService.getUsersByOrgAndChild(deptId);
        Map<String,Object> map = new HashMap<>();
        map.put("users",users);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description  部门进行停用或启用的操作
     * @Date 2020/9/16
     * enabled 1-启用 0-停用
     */
    @ResponseBody
    @RequestMapping("/updateEnabled.do")
    public JsonResult updateEnabled(Integer deptId,User user,Integer enabled){
        Map<String,Object> map = new HashMap<>();
        map = orgService.updateEnabled(deptId,user.getUserID(),enabled);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description  查看已停用的部门
     * @Date 2020/9/16
     */
    @ResponseBody
    @RequestMapping("/getDeptByEnable.do")
    public JsonResult getDeptByEnable(User user){
        Map<String,Object> map = new HashMap<>();
        List<Organization> organizations = orgService.getAllDepartments(user.getOid(),0);
        map.put("organizations",organizations);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 改错字操作/部门改名
     * @Date 2020/9/17
     * type 1-改错字操作 2-部门改名
     */
    @ResponseBody
    @RequestMapping("/updateNameWord.do")
    public JsonResult updateNameWord(Integer deptId,String departmentName,User user,Date enableTime,Integer type) throws InvocationTargetException, IllegalAccessException {
        Map<String,Object> map = new HashMap<>();
        map = orgService.updateNameWord(deptId,departmentName,user.getUserID(),enableTime,type);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 修改历史
     * @Date 2020/9/18
     */
    @ResponseBody
    @RequestMapping("/getRecords.do")
    public JsonResult getRecords(Integer deptId) {
        Map<String,Object> map = new HashMap<>();
        map = orgService.getRecords(deptId);
        return new JsonResult(1,map);
    }


    /**
     *<AUTHOR>
     *@date 2020/10/22 17:05
     *返回相关的提示
     * deptId 部门id   type 1-改错字  2-停用部门  3-删除部门
    */
    @ResponseBody
    @RequestMapping("/getReturnTips.do")
    public JsonResult getReturnTips(User user,Integer deptId,Integer type){
        Map<String,Object> map = new HashMap<>();
        map = orgService.getReturnTips(user.getOid(),deptId,type);
        return new JsonResult(1,map);
    }
}
