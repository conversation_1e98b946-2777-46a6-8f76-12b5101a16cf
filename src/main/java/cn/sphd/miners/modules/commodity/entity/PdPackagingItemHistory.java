package cn.sphd.miners.modules.commodity.entity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;


@Entity
@Table(name = "t_pd_packaging_item_history")
public class PdPackagingItemHistory {
    private Integer id;
    private Integer org;
    private Integer packagingItem;
    private Integer packagingStruct;
    private Integer orders;
    private Integer material;
    private Integer enabled;
    private Date enabledTime;
    private Integer isPrincipal;
    private Integer usageUnitId;
    private String usageUnit;
    private BigDecimal ratedAmout;
    private BigDecimal stockWeight;
    private Integer stockWeightUnitId;
    private String stockWeightUnit;
    private BigDecimal usageWeight;
    private Integer usageWeightUnitId;
    private String usageWeightUnit;
    private String memo;
    private Integer creator;
    private String createName;
    private Date createDate;
    private Integer updator;
    private String updateName;
    private Date updateDate;
    private Integer operation;
    private Integer previousId;
    private Integer versionNo;

    private String name;  //商品名称

    private String code;   //条码

    private String model;
    private String specifications;
    private String unit;
    private Integer unitId;
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Basic
    @Column(name = "org")
    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    @Basic
    @Column(name = "packaging_item")
    public Integer getPackagingItem() {
        return packagingItem;
    }

    public void setPackagingItem(Integer packagingItem) {
        this.packagingItem = packagingItem;
    }

    @Basic
    @Column(name = "packaging_struct")
    public Integer getPackagingStruct() {
        return packagingStruct;
    }

    public void setPackagingStruct(Integer packagingStruct) {
        this.packagingStruct = packagingStruct;
    }

    @Basic
    @Column(name = "orders")
    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    @Basic
    @Column(name = "material")
    public Integer getMaterial() {
        return material;
    }

    public void setMaterial(Integer material) {
        this.material = material;
    }

    @Basic
    @Column(name = "enabled")
    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    @Basic
    @Column(name = "enabled_time")
    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    @Basic
    @Column(name = "is_principal")
    public Integer getIsPrincipal() {
        return isPrincipal;
    }

    public void setIsPrincipal(Integer isPrincipal) {
        this.isPrincipal = isPrincipal;
    }

    @Basic
    @Column(name = "usage_unit_id")
    public Integer getUsageUnitId() {
        return usageUnitId;
    }

    public void setUsageUnitId(Integer usageUnitId) {
        this.usageUnitId = usageUnitId;
    }

    @Basic
    @Column(name = "usage_unit")
    public String getUsageUnit() {
        return usageUnit;
    }

    public void setUsageUnit(String usageUnit) {
        this.usageUnit = usageUnit;
    }

    @Basic
    @Column(name = "rated_amout")
    public BigDecimal getRatedAmout() {
        return ratedAmout;
    }

    public void setRatedAmout(BigDecimal ratedAmout) {
        this.ratedAmout = ratedAmout;
    }

    @Basic
    @Column(name = "stock_weight")
    public BigDecimal getStockWeight() {
        return stockWeight;
    }

    public void setStockWeight(BigDecimal stockWeight) {
        this.stockWeight = stockWeight;
    }

    @Basic
    @Column(name = "stock_weight_unit_id")
    public Integer getStockWeightUnitId() {
        return stockWeightUnitId;
    }

    public void setStockWeightUnitId(Integer stockWeightUnitId) {
        this.stockWeightUnitId = stockWeightUnitId;
    }

    @Basic
    @Column(name = "stock_weight_unit")
    public String getStockWeightUnit() {
        return stockWeightUnit;
    }

    public void setStockWeightUnit(String stockWeightUnit) {
        this.stockWeightUnit = stockWeightUnit;
    }

    @Basic
    @Column(name = "usage_weight")
    public BigDecimal getUsageWeight() {
        return usageWeight;
    }

    public void setUsageWeight(BigDecimal usageWeight) {
        this.usageWeight = usageWeight;
    }

    @Basic
    @Column(name = "usage_weight_unit_id")
    public Integer getUsageWeightUnitId() {
        return usageWeightUnitId;
    }

    public void setUsageWeightUnitId(Integer usageWeightUnitId) {
        this.usageWeightUnitId = usageWeightUnitId;
    }

    @Basic
    @Column(name = "usage_weight_unit")
    public String getUsageWeightUnit() {
        return usageWeightUnit;
    }

    public void setUsageWeightUnit(String usageWeightUnit) {
        this.usageWeightUnit = usageWeightUnit;
    }

    @Basic
    @Column(name = "memo")
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Basic
    @Column(name = "creator")
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    @Basic
    @Column(name = "create_name")
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Basic
    @Column(name = "create_date")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "updator")
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    @Basic
    @Column(name = "update_name")
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Basic
    @Column(name = "update_date")
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Basic
    @Column(name = "operation")
    public Integer getOperation() {
        return operation;
    }

    public void setOperation(Integer operation) {
        this.operation = operation;
    }

    @Basic
    @Column(name = "previous_id")
    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    @Basic
    @Column(name = "version_no")
    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
    @Transient
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    @Transient
    public String getCode() {
        return code;
    }

    @Transient
    public String getModel() {
        return model;
    }
    @Transient
    public void setModel(String model) {
        this.model = model;
    }

    @Transient
    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public void setCode(String code) {
        this.code = code;
    }
    @Transient
    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }
    @Transient
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }
}
