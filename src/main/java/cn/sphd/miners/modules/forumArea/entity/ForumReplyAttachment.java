package cn.sphd.miners.modules.forumArea.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import cn.sphd.miners.modules.resourceAuthority.entity.ResEntity;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2018-02-1
 * @description
 **/
@Entity
@Table(name = "t_forum_reply_attachment")
public class ForumReplyAttachment  extends BaseEntity {
    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    @Column(name = "reply")
    private Integer reply;
    @Column(name = "title")
    private String title;
    @Column(name = "type")
    private String type;
    @Column(name = "description")
    private String description;
    @Column(name = "path")
    private String path;
    @Column(name = "orders")
    private Integer orders;
    @Column(name = "memo")
    private String memo;
    @Column(name = "updator")
    private Integer updator;
    @Column(name = "update_name")
    private String updateName;
    @Column(name = "update_time")
    private Date updateTime;
    @Column(name = "size")
    private Integer size;
    @Column(name = "org")
    private Integer org;
    @Column(name = "place")
    private String place;
    @Column(name = "location")
    private String location;
    /**
     * 内部文件资源ID
     */
    @Column(name = "resource_id", nullable=true , unique=false )
    private Integer resourceId;
    @Column(name = "resource_version")
    private Integer resourceVersion;
    @Column(name = "resource_history")
    private Integer resourceHistory;
    @Column(name = "creator")
    private Integer creator;
    @Column(name = "create_name")
    private String createName;
    @Column(name = "create_time")
    private Date createTime;

    @Transient
    private String imgPath;

    @Transient
    private String gender;		//性别  1-男

    @Transient
    private String module;

    @Transient
    private String resourceImportName;

    @Transient
    private Date resourceImportDate;

    @Transient
    private Integer importVerson;

    @Transient
    private ResEntity resEntity;

    @Transient
    private Integer replyId;

    @Transient
    private Byte isUploaded;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getReply() {
        return reply;
    }

    public void setReply(Integer reply) {
        this.reply = reply;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public String getPlace() {
        return place;
    }

    public void setPlace(String place) {
        this.place = place;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getImgPath() {
        return imgPath;
    }

    public void setImgPath(String imgPath) {
        this.imgPath = imgPath;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public Integer getResourceId() {
        return resourceId;
    }

    public void setResourceId(Integer resourceId) {
        this.resourceId = resourceId;
    }


    public Integer getResourceVersion() {
        return resourceVersion;
    }

    public void setResourceVersion(Integer resourceVersion) {
        this.resourceVersion = resourceVersion;
    }

    public Integer getResourceHistory() {
        return resourceHistory;
    }

    public void setResourceHistory(Integer resourceHistory) {
        this.resourceHistory = resourceHistory;
    }

    public ResEntity getResEntity() {
        return resEntity;
    }

    public void setResEntity(ResEntity resEntity) {
        this.resEntity = resEntity;
    }

    public String getResourceImportName() {
        return resourceImportName;
    }

    public void setResourceImportName(String resourceImportName) {
        this.resourceImportName = resourceImportName;
    }

    public Date getResourceImportDate() {
        return resourceImportDate;
    }

    public void setResourceImportDate(Date resourceImportDate) {
        this.resourceImportDate = resourceImportDate;
    }

    public Integer getImportVerson() {
        return importVerson;
    }

    public void setImportVerson(Integer importVerson) {
        this.importVerson = importVerson;
    }

    public Integer getReplyId() {
        return replyId;
    }

    public void setReplyId(Integer replyId) {
        this.replyId = replyId;
    }

    public Byte getIsUploaded() {
        return isUploaded;
    }

    public void setIsUploaded(Byte isUploaded) {
        this.isUploaded = isUploaded;
    }
}
