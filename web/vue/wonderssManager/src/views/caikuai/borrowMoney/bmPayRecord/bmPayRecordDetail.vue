<template>
  <div class="box">
    <div class="head">
      <PageHeader :title="tit" :showBack=true :showClose=true
                  @mycloseFun="handleCustomClose"
      ></PageHeader>
    </div>
    <div class="sticky ty-fff">
      <p class="h35f14  padding-0-15 spaceBetween">
        <span>{{ ordRepayment.createName }}<span class="padding-0-10">{{ new
        Date(ordRepayment.createDate).format('yyyy-MM-dd  hh:mm:ss') }}</span>录入</span>
        <span style="color:#337ab7;" @click="rou()">修改记录</span>
      </p>
    </div>
    <div class="padding-0-15 ty-fff margin_top_16">
      <p>
        <span>收款金额</span>
        <span>{{ this.formatCurrency(ordRepayment.repaymentAmount) }}元</span>
      </p>
      <p>
        <span>收款日期</span>
        <span>{{ new
        Date(ordRepayment.repaymentTime).format('yyyy-MM-dd')}}</span>
      </p>
      <p>
        <span>支付方式</span>
        <span>{{ this.chargePayMethod(ordRepayment.repaymentMethod) }}</span>
      </p>
      <p v-if="ordRepayment.repaymentMethod==='1'">
        <span>收款经手人</span>
        <span>{{ ordRepayment.receiveOperatorName }}</span>
      </p>
      <div v-if="ordRepayment.repaymentMethod==='5'">
        <p>
          <span>到账日期</span>
          <span>{{ new
          Date(ordRepayment.arriveDate).format('yyyy-MM-dd')}}</span>
        </p>
        <p>
          <span>收款银行</span>
          <span>{{ ordRepayment.receiveBank }}</span>
        </p>
      </div>
      <div v-if="ordRepayment.repaymentMethod==4||ordRepayment.repaymentMethod==3">
        <p>
          <span>{{ordRepayment.repaymentMethod == 4 ? '汇' : ordRepayment.repaymentMethod == 3 ? '支' : 'err'}}票号</span>
          <span>{{ ordRepayment.billNo }}</span>
        </p>
        <p>
          <span>票据金额</span>
          <span>{{ this.formatCurrency(ordRepayment.repaymentAmount) }}元</span>
        </p>
        <p>
          <span>收到{{ordRepayment.repaymentMethod == 4 ? '汇' : ordRepayment.repaymentMethod == 3 ? '支' : 'err'}}票的日期</span>
          <span>{{ new
          Date(ordRepayment.billReceiveDate).format('yyyy-MM-dd') }}</span>
        </p>
        <p>
          <span>{{ordRepayment.repaymentMethod == 4 ? '汇' : ordRepayment.repaymentMethod == 3 ? '支' : 'err'}}票到期日</span>
          <span>{{ new
          Date(ordRepayment.billEndDate).format('yyyy-MM-dd') }}</span>
        </p>
        <p>
          <span>出具{{ordRepayment.repaymentMethod == 4 ? '汇' : ordRepayment.repaymentMethod == 3 ? '支' : 'err'}}票银行</span>
          <span style="width: 70%;text-align: end" class="hH">{{ ordRepayment.billBank }}</span>
        </p>
        <p>
          <span>{{ordRepayment.repaymentMethod == 4 ? '原始出具汇票' : ordRepayment.repaymentMethod == 3 ? '出具支票' : 'err'}}单位</span>
          <span style="width: 60%;text-align: end">{{ ordRepayment.billSource }}</span>
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import finance from '@/mixins/finance.js'

export default {
  name: "bmPayRecordDetail",
  mixins: [finance],
  data() {
    return {
      tit: "财会",
      ordRepayment: {}

    }
  },
  computed: {
    id() {
      return this.$route.query.id
    },
  },
  mounted() {
    this.getOrdRepaymentDetail()
  },
  methods: {
    rou() {
      this.$router.push({path: '/bmPayChangeRecordList', query: {id:this.id}})
    },
    handleCustomClose() {
      this.$router.push("/caikuai")
    },
    getOrdRepaymentDetail() {
      let params = {id: this.id}
      this.financeApi.ordRepaymentDetail(params).then(res1 => {
        this.ordRepayment = res1.data.data
        console.log('res1.data.data1111', res1.data.data.id)
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";

.box {
  > div:nth-child(3) {
    line-height: 30px;

    > p {
      display: flex;
      justify-content: space-between;
      padding: 2px 0;
    }

    > div {
      > p {
        display: flex;
        justify-content: space-between;
        padding: 2px 0;
      }
    }
  }
}
</style>