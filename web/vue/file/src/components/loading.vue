<template>
  <div class="loading">
    <van-loading size="24px" color="#fff" vertical>加载中...</van-loading>
  </div>
</template>

<script>
export default {
  name: "loading",
};
</script>

<style scoped lang="scss">
$topbar-height: 55px;
.loading {
  position: fixed;
  top: 20%;
  z-index: 999;
  width: 110px;
  height: 80px;
  background: rgba(50, 50, 50, 0.5);
  text-align: center;
  left: calc(50% - #{$topbar-height});
  padding-top: 25px;
  border-radius: 10px;
  font-size: 20px;
}
</style>
