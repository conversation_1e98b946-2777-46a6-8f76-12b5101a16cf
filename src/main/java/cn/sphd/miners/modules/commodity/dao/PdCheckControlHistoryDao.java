package cn.sphd.miners.modules.commodity.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.commodity.entity.PdCheckControl;
import cn.sphd.miners.modules.commodity.entity.PdCheckControlHistory;

import java.io.Serializable;

public interface PdCheckControlHistoryDao extends IBaseDao<PdCheckControlHistory, Serializable> {

    void insertHistory(PdCheckControl pdCheckControl);
}
