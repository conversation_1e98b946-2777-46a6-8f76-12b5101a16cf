<!--财会首页点击本日按钮后的页面-->
<template>
  <div class="head">
    <div style="width: 100%">
      <PageHeader title="财会" :showBack="true" :showClose="true" @mycloseFun="closeFun">
        <span class="bennn" @click="goSearchPage">选择完成</span>
      </PageHeader>
    </div>
    <div>
      <div class="tip">您选择好开始时间后，自动将结束时间设置为当月最后一天，您也可修改</div>
      <div>
        <div class="timeBtn">
          <el-input v-model="beginTime" placeholder="开始时间选择" readonly @click="showDrawer(1)"></el-input>
<!--          <el-date-picker v-model="beginTime" type="date" @change="setEndTime" placeholder="开始时间选择"></el-date-picker>-->
        </div>
        <div class="timeBtn">
          <el-input v-model="endTime" placeholder="结束时间选择" readonly @click="showDrawer(2)"></el-input>

          <!--          <el-date-picker v-model="endTime" type="date" placeholder="结束时间选择"></el-date-picker>-->
        </div>

      </div>


      <el-drawer title="选择时间" v-model="drawer" direction="btt" destroy-on-close height="500px">
        <div>
          <el-calendar v-model="selectTime" ref="calendar" >
            <template #header="{date}">
              <div>
                <div class="clear:both">{{ date }}</div> <br>
                <el-button-group>
                  <el-button size="mini" @click="selectDate('prev-year')">上一年</el-button>
                  <el-button size="mini" @click="selectDate('prev-month')">上一月</el-button>
                  <el-button size="mini" @click="selectDate('today')">今天</el-button>
                  <el-button size="mini" @click="selectDate('next-month')">下一月</el-button>
                  <el-button size="mini" @click="selectDate('next-year')">下一年</el-button>
                </el-button-group>
              </div>
            </template>
          </el-calendar>
          <div style="text-align: right">
            <span class="ty-btn ty-btn-green ty-btn-big ty-circle-3" style="margin-right: 30px;" @click="selectOK">确定</span>
          </div>
        </div>
      </el-drawer>

    </div>
  </div>

</template>

<script>
import finance from '@/mixins/finance.js'
export default {
  name: "today",
  mixins: [finance],
  data() {
    return {
      beginTime:'',
      endTime:'',
      drawer:false,
      selectTime:'',
      timeType:''
    }
  },
  components:{ },
  mounted() { },
  methods: {
    showDrawer(type){
      this.timeType = type
      this.drawer=true
      if(type === 1){
        // this.selectTime = new Date(this.beginTime)
        this.$refs.calendar.selectDate(new Date(this.beginTime))
      }else{
        this.$refs.calendar.selectDate(new Date(this.endTime))
        // this.selectTime = new Date(this.endTime)
      }
    },
    selectOK(){
      console.log('选择的时间', this.selectTime)
      if(this.selectTime > (new Date())){ //判断合法时间
        this.$message.error('请选择已发生的时间！')
        return false
      }
      if(this.timeType === 2){
        if(this.selectTime < (new Date(this.beginTime ))){
          this.$message.error('结束日期应在开始日期之后！')
          return false
        }
      }
      if(this.timeType === 1){
        this.beginTime = new Date(this.selectTime).format('yyyy-MM-dd')
        this.setEndTime()
      } else{
        this.endTime = new Date(this.selectTime).format('yyyy-MM-dd')
      }
      this.drawer= false
    },
    selectDate(value) {
      this.$refs.calendar.selectDate(value)
    },
    setEndTime(){
      let y = new Date(this.beginTime).format('yyyy')
      let m = new Date(this.beginTime).format('MM')
      let cury = new Date().format('yyyy')
      let curm = new Date().format('MM')
      if(cury === y && curm === m){
        this.endTime = new Date().format('yyyy-MM-dd')
      }else {
        let d = 30
        if(m === '02'){
          var year = y
          if(year % 4 ==0 && year % 100 !=0 || year % 400 ==0){
            d = 29
          }else{
            d = 28
          }
        }else{
          let inDay31 = ['01','03','05','07','08','10','12'].indexOf(m)
          if(inDay31 > -1){
            d = 31
          }
        }
        this.endTime = `${y}-${m}-${d}`
        console.log('设置的时间=', this.endTime)
      }
    },
    goSearchPage(){
      if(this.beginTime === ''){
        this.$message.info('请先输入开始时间')
      }
      else{
        let bYear = new Date(this.beginTime).format('yyyy')
        let bMonth = new Date(this.beginTime).format('MM')
        let bDay = new Date(this.beginTime).format('dd')
        let eYear = new Date(this.endTime).format('yyyy')
        let eMonth = new Date(this.endTime).format('MM')
        let eDay = new Date(this.endTime).format('dd')

        if(bYear === eYear){ // 同一年
          if(bMonth === eMonth){ // 同月份
            if(bDay === eDay){ // 同一天
              //  日流水明细
              localStorage.setItem('currentAccountDate_beginDate', new Date(this.beginTime).format('yyyy-MM-dd') )
              let path = `/currentAccountDateDur/null`
              this.$router.push({ path:path })
            }else{ // 不同天
              //  日列表
              localStorage.setItem('searchMonthDur3_beginDate', new Date(this.beginTime).format('yyyy-MM-dd') )
              localStorage.setItem('searchMonthDur3_endDate', new Date(this.endTime).format('yyyy-MM-dd') )
              let path = `/viewByTimeDayList`
              this.$router.push({ path:path })
            }
          }else{ // 不同月份
            // 月列表
            localStorage.setItem('searchYearDur_monthBegin', new Date(this.beginTime).format('yyyy-MM-dd')  )
            localStorage.setItem('searchYearDur_monthEnd', new Date(this.endTime).format('yyyy-MM-dd')  )
            let path = `/viewByTimemonthList`
            this.$router.push({ path:path })
          }
        }
        else{ // 不同年
          localStorage.setItem('searchYearList_Begin', new Date(this.beginTime).format('yyyy-MM-dd')  )
          localStorage.setItem('searchYearList_End', new Date(this.endTime).format('yyyy-MM-dd')  )
          let path = `/viewByTimeYearList`
          this.$router.push({ path:path })
        }
      }

    }
  },
}
</script>

<style scoped lang="scss">
@import "../../style/account.scss";
.tip{
  margin: 200px auto 100px;
  width: 70%; color: $tyColorGreen ; line-height: 40px; font-size: 18px;
}
.timeBtn{
    width: 70%; margin: 40px auto;
}
.bennn{
  position: relative; left: -30px; font-size: 16px;
}

</style>
<style>
@import "../../style/account.scss";
.el-input__wrapper{
  height: 40px; width: 93%;
}
.el-drawer.btt{ height: 550px!important;  }
.el-calendar__header{    }
.el-calendar-table .el-calendar-day{ height:40px }
.el-button{ padding-left:10px; padding-right:10px;  }
.el-drawer__header{ margin-bottom: 0; }
.el-drawer__body{ padding: 5px;  }
</style>