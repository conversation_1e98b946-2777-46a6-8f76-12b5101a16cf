package cn.sphd.miners.modules.investigate.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2021-08-01 
 */

@Entity ( name ="InvestigatePublish" )
@Table ( name ="t_investigate_publish" )
public class InvestigatePublish implements Serializable {

	private static final long serialVersionUID =  6429820538020946115L;

	/**
	 * ID
	 */
	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * 机构ID
	 */
   	@Column(name = "org" )
	private Integer org;

	/**
	 * 问卷ID
	 */
   	@Column(name = "subject" )
	private Integer subject;

	/**
	 * 调查类型:自定义
	 */
   	@Column(name = "category" )
	private String category;

	/**
	 * 开始时间
	 */
   	@Column(name = "begin_time" )
	private Date beginTime;

	/**
	 * 截止时间
	 */
   	@Column(name = "end_time" )
	private Date endTime;

	/**
	 * 封面图片存储路径
	 */
	@Column(name = "cover_image" )
	private String coverImage;

	/**
	 * 允许修改时间
	 */
	@Column(name = "modify_limit" )
	private Integer modifyLimit;

	/**
	 * 名称
	 */
   	@Column(name = "name" )
	private String name;

	/**
	 * 代码
	 */
   	@Column(name = "code" )
	private String code;

	/**
	 * 发布时间
	 */
   	@Column(name = "publish_time" )
	private Date publishTime;

	/**
	 * 二维码生成时间
	 */
   	@Column(name = "QR_generae_time" )
	private Date qrGeneraeTime;

	/**
	 * 二维码存放路径
	 */
   	@Column(name = "QR_path" )
	private String qrPath;

	/**
	 * 二维码有效截止时间
	 */
   	@Column(name = "QR_deadline" )
	private Date qrDeadline;

	/**
	 * 是否启用
	 */
   	@Column(name = "enabled" )
	private Integer enabled;

	/**
	 * 启/停用时间
	 */
   	@Column(name = "enabled_time" )
	private Date enabledTime;

	/**
	 * 回收份数
	 */
   	@Column(name = "feedback_num" )
	private Integer feedbackNum;

	/**
	 * 排序
	 */
   	@Column(name = "orders" )
	private Integer orders;

	/**
	 * 备注
	 */
   	@Column(name = "memo" )
	private String memo;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_date" )
	private Date createDate;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_date" )
	private Date updateDate;

	/**
	 * 操作:1-增,2-删,3-改,4-改二维码
	 */
   	@Column(name = "operation" )
	private String operation;

	/**
	 * 修改前记录ID
	 */
   	@Column(name = "previous_id" )
	private Integer previousId;

	/**
	 * 版本号,每次修改+1
	 */
   	@Column(name = "version_no" )
	private Integer versionNo;

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOrg() {
		return this.org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getSubject() {
		return this.subject;
	}

	public void setSubject(Integer subject) {
		this.subject = subject;
	}

	public String getCategory() {
		return this.category;
	}

	public void setCategory(String category) {
		this.category = category;
	}

	public Date getBeginTime() {
		return this.beginTime;
	}

	public void setBeginTime(Date beginTime) {
		this.beginTime = beginTime;
	}

	public Date getEndTime() {
		return this.endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public String getCoverImage() {
		return coverImage;
	}

	public void setCoverImage(String coverImage) {
		this.coverImage = coverImage;
	}

	public Integer getModifyLimit() {
		return modifyLimit;
	}

	public void setModifyLimit(Integer modifyLimit) {
		this.modifyLimit = modifyLimit;
	}

	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCode() {
		return this.code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public Date getPublishTime() {
		return this.publishTime;
	}

	public void setPublishTime(Date publishTime) {
		this.publishTime = publishTime;
	}

	public Date getQrGeneraeTime() {
		return this.qrGeneraeTime;
	}

	public void setQrGeneraeTime(Date qrGeneraeTime) {
		this.qrGeneraeTime = qrGeneraeTime;
	}

	public String getQrPath() {
		return this.qrPath;
	}

	public void setQrPath(String qrPath) {
		this.qrPath = qrPath;
	}

	public Date getQrDeadline() {
		return this.qrDeadline;
	}

	public void setQrDeadline(Date qrDeadline) {
		this.qrDeadline = qrDeadline;
	}

	public Integer getEnabled() {
		return this.enabled;
	}

	public void setEnabled(Integer enabled) {
		this.enabled = enabled;
	}

	public Date getEnabledTime() {
		return this.enabledTime;
	}

	public void setEnabledTime(Date enabledTime) {
		this.enabledTime = enabledTime;
	}

	public Integer getFeedbackNum() {
		return this.feedbackNum;
	}

	public void setFeedbackNum(Integer feedbackNum) {
		this.feedbackNum = feedbackNum;
	}

	public Integer getOrders() {
		return this.orders;
	}

	public void setOrders(Integer orders) {
		this.orders = orders;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getCreator() {
		return this.creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return this.createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getUpdator() {
		return this.updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return this.updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return this.updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public String getOperation() {
		return this.operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public Integer getPreviousId() {
		return this.previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return this.versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}

}
