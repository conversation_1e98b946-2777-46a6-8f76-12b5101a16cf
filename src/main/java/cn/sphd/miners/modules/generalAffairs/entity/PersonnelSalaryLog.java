package cn.sphd.miners.modules.generalAffairs.entity;

import cn.sphd.miners.modules.system.entity.User;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 薪资表
 * Created by Administrator on 2015/12/14.
 */
@Entity
@Table(name="t_personnel_salary_log")
public class PersonnelSalaryLog implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;  //薪资id

    @Column(name="salary"   , nullable=true , unique=false)
    private BigDecimal salary;//月薪
    @Column(name="adjust_date"   , nullable=true , unique=false)
    private Date adjustDate;//调整日期
    @Column(name="admust_resaon"  , length=255 , nullable=true , unique=false)
    private String admustResaon;//调整原因
    @Column(name="operate_time"   , nullable=true , unique=false)
    private Date operateTime;//调整时间
    @Column(name="operator"   , nullable=true , unique=false)
    private Integer operator;//调整者
    @Column(name="operator_name"  , nullable=true , unique=false)
    private String operatorName;  //调整人名称
    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;//备注

    //���û�����һ��ϵ
    @ManyToOne(fetch= FetchType.LAZY )
    @JoinColumn(name="user", referencedColumnName = "userID" , nullable=true , unique=false , insertable=true, updatable=true)
    private User user;

    @Column(name="user"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer user_;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public BigDecimal getSalary() {
        return salary;
    }

    public void setSalary(BigDecimal salary) {
        this.salary = salary;
    }

    public Date getAdjustDate() {
        return adjustDate;
    }

    public void setAdjustDate(Date adjustDate) {
        this.adjustDate = adjustDate;
    }

    public String getAdmustResaon() {
        return admustResaon;
    }

    public void setAdmustResaon(String admustResaon) {
        this.admustResaon = admustResaon;
    }

    public Date getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Date operateTime) {
        this.operateTime = operateTime;
    }

    public Integer getOperator() {
        return operator;
    }

    public void setOperator(Integer operator) {
        this.operator = operator;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Integer getUser_() {
        return user_;
    }

    public void setUser_(Integer user_) {
        this.user_ = user_;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

}
