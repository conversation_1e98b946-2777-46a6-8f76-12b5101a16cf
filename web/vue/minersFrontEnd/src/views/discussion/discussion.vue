<template>
  <div class="discussion">
   <h1>讨论区</h1>
  </div>
</template>
<script>
import { beforeRouteLeave } from "@/utils/routeChange"
import auth from '@/sys/auth'

export default {
  data() {
    return {

    }
  },
  beforeRouteLeave(to, from, next) {
    beforeRouteLeave(to, from, next,this)
  },
  components: {
  },
  mounted() {
  },
  methods: {
    hideU2(){
      this.userInfoBasicVisible = false
    },

  }
}


</script>

<style lang="scss" scoped>
.discussion{


}

</style>
