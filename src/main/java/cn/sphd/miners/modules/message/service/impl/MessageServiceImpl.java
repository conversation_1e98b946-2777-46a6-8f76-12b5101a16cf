package cn.sphd.miners.modules.message.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.service.BaseServiceImpl;
import cn.sphd.miners.modules.message.dao.MessageDao;
import cn.sphd.miners.modules.message.service.MessageService;
import cn.sphd.miners.modules.personal.entity.UserMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2017/3/27.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class MessageServiceImpl extends BaseServiceImpl implements MessageService{
    @Autowired
    MessageDao messageDao;

    @Override
    public List<UserMessage> getMessageByUidAndState(Integer status, Integer uid) {
        String condition = "";
        if (status!=null){
            condition+= " and o.state = "+status;
        }
        if (uid!=null){
            condition+=  "and o.receiveUserId = "+uid;
        }
        Map<String,String> orderBy = new HashMap<>();
        orderBy.put("createDate","desc");
        List<UserMessage> userMessages = messageDao.findCollectionByConditionNoPage(condition,null,orderBy);
        return userMessages;
    }
    @Override
    public List<UserMessage> getMessageByUidAndState(Integer status, Integer uid, PageInfo pageInfo) {
        HashMap<String,Object> params = new HashMap<>();
        StringBuffer hql=new StringBuffer("from UserMessage");
        String link=" where ";
        if (status!=null){
            params.put("state",status);
            hql.append(link).append("state=:state");
        }
        if (uid!=null){
            link=" and ";
            params.put("receiveUserId",uid);
            hql.append(link).append("receiveUserId=:receiveUserId");
        }
        link=" order by id desc";
        hql.append(link);
        return messageDao.getListByHQLWithNamedParams(hql.toString(),params,pageInfo);
    }


    @Override
    public List<UserMessage> getAllMessage(Integer status, Integer uid, String beginDate, String endDate) {
        String condition = " and o.approvalStatus = " + status + " and o.user = "+uid;
        if ("".equals(endDate)||endDate==null){
            condition+=" and month(o.createDate) = month('"+beginDate+"')";
        }else {
            condition+= " and o.createDate between '"+beginDate+"' and '"+endDate+"'";
        }
        List<UserMessage> userMessages = messageDao.findCollectionByConditionNoPage(condition,null,null);
        return userMessages;
    }

    @Override
    public List<UserMessage> getMessageByPersonnelReimburseId(Integer pid, Integer status,Integer uid,Integer messageType) {
        String hql = " from UserMessage o where 1=1";
        if (uid!=null){
            hql+=" and o.receiveUserId = "+uid;
        }
        if (status!=null){
            hql+=" and o.state = "+status;

        }
        if (pid!=null){
            hql+=" and o.personnelReimburId!=null and o.personnelReimburId = "+pid;
        }

        if(messageType!=null){
            hql+=" and o.messageType='"+messageType+"'";
        }
        List<UserMessage> userMessages = messageDao.getListByHQL(hql);
        return userMessages;
    }

    @Override
    public List<UserMessage> getMessageByMessageId(Integer messageId) {
        String hql = " from UserMessage o where o.messageId = "+messageId;
        return messageDao.getListByHQL(hql);
    }

    @Override
    public void updateMessagesByUidMessageType( Integer uid, String messageType) {
        String hql=" update UserMessage set state=2 where state=1 and messageType=:messageType and receiveUserId=:uid";
        Map<String,Object> map = new HashMap<>();
        map.put("messageType",messageType);
        map.put("uid",uid);
        messageDao.queryHQLWithNamedParams(hql,map);
    }
}
