package cn.sphd.miners.modules.equipment.service.impl;

import java.util.List;

import cn.sphd.miners.modules.equipment.entity.TEquFixedAssets;
import cn.sphd.miners.modules.equipment.entity.TEquFixedAssetsHistory;
import cn.sphd.miners.modules.equipment.entity.TEquModelHistory;
import cn.sphd.miners.modules.equipment.mapper.*;
import cn.sphd.miners.modules.equipment.service.ITEquModelHistoryService;
import cn.sphd.miners.modules.material.dao.MtUnitDao;
import cn.sphd.miners.modules.material.dao.SrmSupplierDao;
import cn.sphd.miners.modules.material.entity.MtUnit;
import cn.sphd.miners.modules.material.entity.SrmSupplier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 装备管理_型号历史
 * 20230209 1.247装备器具新增
 * Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Service
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class TEquModelHistoryServiceImpl implements ITEquModelHistoryService {
    @Autowired
    private TEquModelHistoryMapper tEquModelHistoryMapper;
    @Autowired
    private TEquEquipmentMapper equEquipmentMapper;

    @Autowired
    private TEquCategoryMapper categoryMapper;
    @Autowired
    private SrmSupplierDao srmSupplierDao;
    @Autowired
    private MtUnitDao unitDao;
    @Autowired
    TEquSupplierMapper supplierMapper;

    @Autowired
    TEquFixedAssetsMapper fixedAssetsMapper;

    @Autowired
    TEquFixedAssetsHistoryMapper fixedAssetsHistoryMapper;

    /**
     * 查询装备管理_型号历史
     * 20230209 1.247装备器具新增
     *
     * @param id 装备管理_型号历史
     *           20230209 1.247装备器具新增
     *           主键
     * @return 装备管理_型号历史
     * 20230209 1.247装备器具新增
     */
    @Override
    public TEquModelHistory selectTEquModelHistoryById(Long id) {
        return tEquModelHistoryMapper.selectTEquModelHistoryById(id);
    }

    /**
     * 查询装备管理_型号历史
     * 20230209 1.247装备器具新增
     * 列表
     *
     * @param tEquModelHistory 装备管理_型号历史
     *                         20230209 1.247装备器具新增
     * @return 装备管理_型号历史
     * 20230209 1.247装备器具新增
     */
    @Override
    public List<TEquModelHistory> selectTEquModelHistoryList(TEquModelHistory tEquModelHistory) {
        return tEquModelHistoryMapper.selectTEquModelHistoryList(tEquModelHistory);
    }

    /**
     * 新增装备管理_型号历史
     * 20230209 1.247装备器具新增
     *
     * @param tEquModelHistory 装备管理_型号历史
     *                         20230209 1.247装备器具新增
     * @return 结果
     */
    @Override
    public int insertTEquModelHistory(TEquModelHistory tEquModelHistory) {
        return tEquModelHistoryMapper.insertTEquModelHistory(tEquModelHistory);
    }

    /**
     * 修改装备管理_型号历史
     * 20230209 1.247装备器具新增
     *
     * @param tEquModelHistory 装备管理_型号历史
     *                         20230209 1.247装备器具新增
     * @return 结果
     */
    @Override
    public int updateTEquModelHistory(TEquModelHistory tEquModelHistory) {
        return tEquModelHistoryMapper.updateTEquModelHistory(tEquModelHistory);
    }

    /**
     * 批量删除装备管理_型号历史
     * 20230209 1.247装备器具新增
     *
     * @param ids 需要删除的装备管理_型号历史
     *            20230209 1.247装备器具新增
     *            主键
     * @return 结果
     */
    @Override
    public int deleteTEquModelHistoryByIds(String... ids) {
        return tEquModelHistoryMapper.deleteTEquModelHistoryByIds(ids);
    }

    /**
     * 删除装备管理_型号历史
     * 20230209 1.247装备器具新增
     * 信息
     *
     * @param id 装备管理_型号历史
     *           20230209 1.247装备器具新增
     *           主键
     * @return 结果
     */
    @Override
    public int deleteTEquModelHistoryById(Long id) {
        return tEquModelHistoryMapper.deleteTEquModelHistoryById(id);
    }

    @Override
    public List histories(Long id, Long operation) {
        if(operation==null) operation=7L;
        TEquFixedAssets fixedAssets = fixedAssetsMapper.selectTEquFixedAssetsById(id);

        //查询公共信息的修改记录
        if(operation==6){
            TEquModelHistory h = new TEquModelHistory();
            h.setModel(fixedAssets.getModel());
            h.setOperation(operation);
            List<TEquModelHistory> list = tEquModelHistoryMapper.selectTEquModelHistoryList(h);
            for (TEquModelHistory tEquModelHistory : list) {
                if (tEquModelHistory.getEquipment() != null) {
                    tEquModelHistory.setEquipmentName(equEquipmentMapper.selectTEquEquipmentById(tEquModelHistory.getEquipment()).getFullName());
                }
                if (tEquModelHistory.getCategory() != null) {
                    tEquModelHistory.setCategoryName(categoryMapper.selectTEquCategoryById(tEquModelHistory.getCategory()).getName());
                }
                if (tEquModelHistory.getUnitId() != null){
                    MtUnit mtUnit = unitDao.get(tEquModelHistory.getUnitId().intValue());
                    if (mtUnit != null) {
                        tEquModelHistory.setUnitName(mtUnit.getName());
                    }
                }
                if (tEquModelHistory.getSupplier() != null) {
                    SrmSupplier srmSupplier = srmSupplierDao.get(tEquModelHistory.getSupplier().intValue());
                    if (srmSupplier != null)
                        tEquModelHistory.setSupplierName(srmSupplier.getFullName());

                }
            }

            return list;
        }else if (operation==7){
            TEquFixedAssetsHistory h = new TEquFixedAssetsHistory();
            h.setFixedAssets(id);
            List<TEquFixedAssetsHistory> fixedAssetsHistories = fixedAssetsHistoryMapper.selectTEquFixedAssetsHistoryList(h);

            //数据库设计有差异 ，暂时设置一下
            for (TEquFixedAssetsHistory fixedAssetsHistory : fixedAssetsHistories) {
                fixedAssetsHistory.setModelCode(fixedAssets.getCode());

                if (fixedAssetsHistory.getCategory() != null) {
                    fixedAssetsHistory.setCategoryName(categoryMapper.selectTEquCategoryById(Long.valueOf(fixedAssetsHistory.getCategory())).getName());
                }
            }
            return fixedAssetsHistories;
        }
       return null;
    }
}
