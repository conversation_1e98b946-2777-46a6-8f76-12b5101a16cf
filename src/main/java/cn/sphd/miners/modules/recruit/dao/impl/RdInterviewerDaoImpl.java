package cn.sphd.miners.modules.recruit.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.recruit.dao.RdInterviewerDao;
import cn.sphd.miners.modules.recruit.entity.RdInterviewer;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class RdInterviewerDaoImpl extends BaseDao<RdInterviewer, Serializable> implements RdInterviewerDao {
}
