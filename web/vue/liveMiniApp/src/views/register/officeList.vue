<template>
	<div class="uni-container">
		<Pageheader title="主播工作室列表" :showBack="false"/>

    <div class="live-panel live-tr" v-for="(item , index) in list" :key="index">
      <div>
        <div @click="goOffice(item)">
          <el-icon>
            <video-camera color="#53B5A8" class="houseSty" />
          </el-icon>
          {{item.name}}
          <el-icon class="live-right relaRight" >
            <arrow-right-bold color="#53B5A8" class="houseSty8" />
          </el-icon>
        </div>
      </div>
    </div>
	</div>
</template>

<script setup>
  import { ElMessage } from 'element-plus'
  import { ref, onMounted } from 'vue'
  import { useRouter } from "vue-router";
  import auth from '@/sys/auth'
  import JSONBig from 'json-bigint'


  const router = useRouter();
	import { organizationList, getTerritoryAuthInfo, sureLogin, getRootPathFile  } from '@/api/api.js'
  const list = ref([])
  const goOffice = (officeItem) => {
    console.log('officeItem', officeItem)
    let jsonDataStr = localStorage.getItem('jsonData')
    let localJsonData = ''
    if(jsonDataStr && jsonDataStr.length > 5){
      localJsonData = JSONBig.parse(jsonDataStr)
    }else{
      localJsonData = {}
    }
    if(officeItem.code === "liveHelper"){
      // 用户登陆
      console.log('officeItem', officeItem)
      sureLogin({"oid": officeItem.id}).then((res)=>{

        localJsonData.officeItem = officeItem
        localStorage.setItem('jsonData', JSONBig.stringify(localJsonData))
        console.log('进机构存的数据', localJsonData)
        localStorage.setItem('isChangeToken', '1')
        let userInfo = auth.getUser()
        console.log('userInfo = ', userInfo )
        if(userInfo.roleCode == 'staff'){
          router.replace({name: 'homeManage'});
        }else{
          router.replace({name: 'homeData'});
        }
      })
    }else{
      ElMessage.error('抱歉，抖音短暂不支持该类机构的登陆！');
    }

  }
  const getOfficeList = () => {
    organizationList().then(res => {
      // ElMessage.success('获取用户信息成功');
      let orgs = res.organizationList
      list.value = orgs
      let liveOffice = []
      orgs.forEach( off => {
        if(off.code === "liveHelper"){
          liveOffice.push(off)
        }
      })
      let jsonDataStr = localStorage.getItem('jsonData')
      let localJsonData = ''
      if(jsonDataStr.length > 5){
        localJsonData = JSONBig.parse(jsonDataStr)
      }else{
        localJsonData = {}
      }
      localJsonData.officeList = liveOffice
      localStorage.setItem('jsonData', JSONBig.stringify(localJsonData))
      if(liveOffice.length === 0){
        localStorage.setItem('isChangeToken', '0')
        router.replace({name: 'mine'});
      }
    }).catch( err => {
      ElMessage.error('获取用户信息失败，请稍后重试！');
    })
  }

  onMounted(()=>{
    getFileRoot()
    getOfficeList()
  })
  const getFileRoot = (type) => {
    getRootPathFile().then((res) =>{
      // fileUrl:"https://hxz-t.frp.btransmission.commonDatanData/upload/"
      // ow365url:"https://ow365.cn/?i=13867&ssl=1&furl=https://hxz-t.frp.btransmission.com/upload/"
      // uploadUrl:"https://hxz-t.frp.btransmission.com/upload/"
      // webRoot:"https://hxz-t.frp.btransmission.com"
      localStorage.setItem('uploadUrl',  res.uploadUrl)
      localStorage.setItem('webRoot',  res.webRoot)

    })
  }

</script>

<style lang="scss" scoped>
.houseSty{
  position: relative;
  top:4px;
}
.houseSty8{
  position: relative;
  top:8px;
}
.live-panel{
  margin-top: 10px;
}
</style>
