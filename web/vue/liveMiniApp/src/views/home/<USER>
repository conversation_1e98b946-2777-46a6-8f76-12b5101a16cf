<template>
  <div class="main">
<!--    <Pageheader :title="offName" :showBack="false" /> -->
    <router-view />

    <component :is="TabBar" :activeIndex0="activeIndex" :isShowData="isShowData" :mainTab="1"></component>

  </div>
</template>

<script setup>
  import TabBar from '../../components/tabBar.vue'
  import auth from '../../sys/auth'
  import { ref } from 'vue'
  import JSONBig from 'json-bigint'

  let jsonData = JSONBig.parse(localStorage.getItem('jsonData'))
  let user = auth.getUser()
  let isShowData = user.roleCode === "super" ? true : false
  let activeIndex = isShowData ? 0 : 1



</script>

<style lang="scss">

</style>
