

// 已经重构过的页面
export function pageMenu() {
  let pageMenus = [
      { 'mid': 'wodezhuomian' , 'pageName': 'desktop', 'name':'我的桌面' },
      { 'mid': 'mf' , 'pageName': 'systemSet', 'name':'系统设置' },
      { 'mid': 'mi' , 'pageName': 'loginSet', 'name':'登陆设置' },

  ]
  return pageMenus
}

// 全部菜单列表
export function singleMenu() {
  let singleMenus =
          [
        {
          "name":"公司总览",
          "mid":"ba",
          "pid":"0",
          "disabled":false,
          "url":"",
          "users":null,
        },
        {
          "name":"实物资产",
          "mid":"bc",
          "checked":false,
          "pid":"ba",
          "disabled":false,
          "url":"../",
          "users":null,
        },
        {
          "name":"财会",
          "mid":"bb",
          "checked":false,
          "pid":"ba",
          "disabled":false,
          "url":"../",
          "users":null
        },
        {
          "name":"车务",
          "mid":"bca",
          "checked":false,
          "pid":"ba",
          "disabled":false,
          "url":"../",
          "users":null,
        },
        {
          "name":"文件与资料",
          "mid":"bd",
          "checked":false,
          "pid":"ba",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"职工",
          "mid":"be",
          "checked":false,
          "pid":"ba",
          "disabled":false,
          "url":"",
          "users":null,

        },
        {
          "name":"登录记录",
          "mid":"bea",
          "checked":false,
          "pid":"be",
          "disabled":false,
          "url":"../general/userLog.do",
          "users":null,

        },
        {
          "name":"职工档案",
          "mid":"beb",
          "checked":false,
          "pid":"be",
          "disabled":false,
          "url":"../",
          "users":null,
        },
        {
          "name":"考勤",
          "mid":"bec",
          "checked":false,
          "pid":"be",
          "disabled":false,
          "url":"../workAttendance/companyOverviewAttendance.do",
          "users":null,

        },
        {
          "name":"请假动态",
          "mid":"bed",
          "checked":false,
          "pid":"be",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"加班动态",
          "mid":"bee",
          "checked":false,
          "pid":"be",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"工资",
          "mid":"bef",
          "checked":false,
          "pid":"be",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"工作记录",
          "mid":"beg",
          "checked":false,
          "pid":"be",
          "disabled":false,
          "url":"../mywork/toBrowsePage.do",
          "users":null,

        },
        {
          "name":"客户",
          "mid":"bf",
          "checked":false,
          "pid":"ba",
          "disabled":false,
          "url":"../",
          "users":null,
        },
        {
          "name":"客户信息",
          "mid":"bfa",
          "checked":false,
          "pid":"bf",
          "disabled":false,
          "url":"../sales/overviewSlCustomerPage.do",
          "users":null,
        },
        {
          "name":"订购信息",
          "mid":"bfb",
          "checked":false,
          "pid":"bf",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"账务信息",
          "mid":"bfc",
          "checked":false,
          "pid":"bf",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"订单",
          "mid":"bh",
          "checked":false,
          "pid":"ba",
          "disabled":false,
          "url":"../sales/overviewOrderPage.do",
          "users":null,

        },
        {
          "name":"权限",
          "mid":"bi",
          "checked":false,
          "pid":"ba",
          "disabled":false,
          "url":"../popedom/toAuthority.do",
          "users":null,

        },
        {
          "name":"审批权限",
          "mid":"bia",
          "checked":false,
          "pid":"bi",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"投诉",
          "mid":"bj",
          "checked":false,
          "pid":"ba",
          "disabled":false,
          "url":"../complaint/companyOverviewComplaint.do",
          "users":null,
        },
        {
          "name":"商品",
          "mid":"bk",
          "checked":false,
          "pid":"ba",
          "disabled":false,
          "url":"../sales/overviewProductPage.do",
          "users":null,
        },
        {
          "name":"销售统计",
          "mid":"bl",
          "checked":false,
          "pid":"ba",
          "disabled":false,
          "url":"../",
          "users":null,
        },
        {
          "name":"仓库",
          "mid":"bm",
          "checked":false,
          "pid":"ba",
          "disabled":false,
          "url":"",
          "users":null,
        },
        {
          "name":"原辅材料库",
          "mid":"bma",
          "checked":false,
          "pid":"bm",
          "disabled":false,
          "url":"../mt/ovRawMt",
          "users":null,
        },
        {
          "name":"成品库",
          "mid":"bmb",
          "checked":false,
          "pid":"bm",
          "disabled":false,
          "url":"../finishedOverview/home.do",
          "users":null,
        },
        {
          "name":"手机端工作",
          "mid":"ca",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":null,
          "users":null,
        },
        {
          "name":"浏览管理",
          "mid":"cb",
          "checked":false,
          "pid":"ca",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"车务管理",
          "mid":"cc",
          "checked":false,
          "pid":"ca",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"车务支出",
          "mid":"cd",
          "checked":false,
          "pid":"ca",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"会议管理",
          "mid":"ce",
          "checked":false,
          "pid":"ca",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"资源管理",
          "mid":"cg",
          "checked":false,
          "pid":"ca",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"加油卡",
          "mid":"ch",
          "checked":false,
          "pid":"ca",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"领料",
          "mid":"ci",
          "checked":false,
          "pid":"ca",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"报税事务",
          "mid":"cj",
          "checked":false,
          "pid":"ca",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"会计杂项",
          "mid":"ck",
          "checked":false,
          "pid":"ca",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"收款单位",
          "mid":"cl",
          "checked":false,
          "pid":"ca",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"考核成绩公示板",
          "mid":"cm",
          "checked":false,
          "pid":"ca",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"公共信息管理",
          "mid":"cn",
          "checked":false,
          "pid":"ca",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"公共信息",
          "mid":"co",
          "checked":false,
          "pid":"ca",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"参展商",
          "mid":"cp",
          "checked":false,
          "pid":"ca",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"展会宝",
          "mid":"cq",
          "checked":false,
          "pid":"ca",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"成品库库位设置",
          "mid":"cr",
          "checked":false,
          "pid":"ca",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"半成品库库位设置",
          "mid":"cs",
          "checked":false,
          "pid":"ca",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"原辅材料库库位设置",
          "mid":"ct",
          "checked":false,
          "pid":"ca",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"办公用品存放位置设置",
          "mid":"cu",
          "checked":false,
          "pid":"ca",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"考勤宝",
          "mid":"cv",
          "checked":false,
          "pid":"ca",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"考勤打卡",
          "mid":"cw",
          "checked":false,
          "pid":"ca",
          "disabled":false,
          "url":"../",
          "users":null,

        },
        {
          "name":"权限管理",
          "mid":"ea",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"",
          "users":null,
        },
        {
          "name":"权限设置",
          "mid":"eb",
          "checked":false,
          "pid":"ea",
          "disabled":false,
          "url":"../popedom/toRoutinePowerSetting.do",
          "users":null,
        },
        {
          "name":"审批设置",
          "mid":"ec",
          "checked":false,
          "pid":"ea",
          "disabled":false,
          "url":"../popedom/toApprovalPowerSetting.do",
          "users":null,
        },
        {
          "name":"当前权限",
          "mid":"ed",
          "checked":false,
          "pid":"ea",
          "disabled":false,
          "url":"../popedom/toCurrentRoutinePower.do",
          "users":null,
        },
        {
          "name":"审批查看",
          "mid":"ee",
          "checked":false,
          "pid":"ea",
          "disabled":false,
          "url":"../popedom/toPowerSetting.do",
          "users":null,

        },
        {
          "name":"职工权限",
          "mid":"ef",
          "checked":false,
          "pid":"ea",
          "disabled":false,
          "url":"../popedom/toWorkerAuthority.do",
          "users":null,

        },
        {
          "name":"修改记录",
          "mid":"eg",
          "checked":false,
          "pid":"ea",
          "disabled":false,
          "url":"../popedom/toUpdateLog.do",
          "users":null,

        },
        {
          "name":"总务管理",
          "mid":"ka",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":null,
          "users":null,
        },
        {
          "name":"职工档案",
          "mid":"kb",
          "checked":false,
          "pid":"ka",
          "disabled":false,
          "url":"../general/employeeIndex.do",
          "users":null,
        },
        {
          "name":"跨机构管理",
          "mid":"kc",
          "checked":false,
          "pid":"ka",
          "disabled":false,
          "url":"../sonOrg/toInteragencyManagement.do",
          "users":null,
        },
        {
          "name":"岗位管理",
          "mid":"kd",
          "checked":false,
          "pid":"ka",
          "disabled":false,
          "url":"../general/postManagement.do",
          "users":null,
        },
        {
          "name":"固定资产",
          "mid":"kf",
          "checked":false,
          "pid":"ka",
          "disabled":false,
          "url":"../capitalAsserts/capitalAsserts.do",
          "users":null,
        },
        {
          "name":"企业信息",
          "mid":"kg",
          "checked":false,
          "pid":"ka",
          "disabled":false,
          "url":"../business/toPage.do",
          "users":null,
        },
        {
          "name":"分工设置",
          "mid":"ki",
          "checked":false,
          "pid":"ka",
          "disabled":false,
          "url":"../coreSetting/toDivisionSetting.do",
          "users":null,

        },
        {
          "name":"考勤管理",
          "mid":"kj",
          "checked":false,
          "pid":"ka",
          "disabled":false,
          "url":"../workAttendance/attendance.do",
          "users":null,

        },
        {
          "name":"修改记录",
          "mid":"kk",
          "checked":false,
          "pid":"ka",
          "disabled":false,
          "url":"../workAttendance/attendanceLog.do",
          "users":null,

        },
        {
          "name":"招聘管理",
          "mid":"kl",
          "checked":false,
          "pid":"ka",
          "disabled":false,
          "url":"../general/goRecruitManagement.do",
          "users":null,

        },
        {
          "name":"财务管理",
          "mid":"la",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":null,
          "users":null,
        },
        {
          "name":"数据查看",
          "mid":"lb",
          "checked":false,
          "pid":"la",
          "disabled":false,
          "url":"../sys/toAdminIndex.do",
          "users":null,

        },
        {
          "name":"账户管理",
          "mid":"ld",
          "checked":false,
          "pid":"la",
          "disabled":false,
          "url":"../finance/financeManage.do",
          "users":null,
        },
        {
          "name":"工资管理",
          "mid":"le",
          "checked":false,
          "pid":"la",
          "disabled":false,
          "url":"../salary/goSalaryIndex.do",
          "users":null,
        },
        {
          "name":"支票管理",
          "mid":"lm",
          "checked":false,
          "pid":"la",
          "disabled":false,
          "url":"../financeJump/checkManage.do",
          "users":null,

        },
        {
          "name":"数据录入",
          "mid":"ln",
          "checked":false,
          "pid":"la",
          "disabled":false,
          "url":"../financeJump/dataImport.do",
          "users":null,
        },
        {
          "name":"回款票据",
          "mid":"lo",
          "checked":false,
          "pid":"la",
          "disabled":false,
          "url":"../financeJump/replyBill.do",
          "users":null,
        },
        {
          "name":"报销受理",
          "mid":"lp",
          "checked":false,
          "pid":"la",
          "disabled":false,
          "url":"../financeJump/expenseReceive.do",
          "users":null,
        },
        {
          "name":"发票管理",
          "mid":"lr",
          "checked":false,
          "pid":"la",
          "disabled":false,
          "url":"../invoice/toPage.do",
          "users":null,
        },
        {
          "name":"发票设置",
          "mid":"ls",
          "checked":false,
          "pid":"la",
          "disabled":false,
          "url":"../invoiceSetting/toInvoiceSettingPage.do",
          "users":null,
        },
        {
          "name":"销售管理",
          "mid":"qa",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":null,
          "users":null,
        },
        {
          "name":"通用型商品",
          "mid":"qh",
          "checked":false,
          "pid":"qa",
          "disabled":false,
          "url":"../commodity/commonGoods.do",
          "users":null,
        },
        {
          "name":"专属商品",
          "mid":"qe",
          "checked":false,
          "pid":"qa",
          "disabled":false,
          "url":"../commodity/basic.do",
          "users":null,
        },
        {
          "name":"客户管理",
          "mid":"qb",
          "checked":false,
          "pid":"qa",
          "disabled":false,
          "url":"../sales/customerManage.do",
          "users":null,
        },
        {
          "name":"订单管理",
          "mid":"qc",
          "checked":false,
          "pid":"qa",
          "disabled":false,
          "url":"../sales/orderManage.do",
          "users":null,
        },
        {
          "name":"订单评审",
          "mid":"qd",
          "checked":false,
          "pid":"qa",
          "disabled":false,
          "url":"../sales/orderScan.do",
          "users":null,
        },
        {
          "name":"销售分工",
          "mid":"qf",
          "checked":false,
          "pid":"qa",
          "disabled":false,
          "url":"../sales/salesDivision.do",
          "users":null,
        },
        {
          "name":"发票送达管理",
          "mid":"qg",
          "checked":false,
          "pid":"qa",
          "disabled":false,
          "url":"../sale/invoiceSend.do",
          "users":null,
        },
        {
          "name":"服务项目",
          "mid":"qi",
          "checked":false,
          "pid":"qa",
          "disabled":false,
          "url":"../saleService/index.do",
          "users":null,
        },
        {
          "name":"服务套餐",
          "mid":"qj",
          "checked":false,
          "pid":"qa",
          "disabled":false,
          "url":"../service/servicePackagePage.do",
          "users":null,
        },
        {
          "name":"服务合同",
          "mid":"qk",
          "checked":false,
          "pid":"qa",
          "disabled":false,
          "url":"../saleContract/index.do",
          "users":null,
        },
        {
          "name":"生产管理",
          "mid":"ua",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"",
          "users":null,
        },
        {
          "name":"订单评审",
          "mid":"ub",
          "checked":false,
          "pid":"ua",
          "disabled":false,
          "url":"../sr/toReview.do",
          "users":null,
        },
        {
          "name":"订单查看",
          "mid":"uc",
          "checked":false,
          "pid":"ua",
          "disabled":false,
          "url":"../sr/toScan.do",
          "users":null,
        },
        {
          "name":"入库申请",
          "mid":"ud",
          "checked":false,
          "pid":"ua",
          "disabled":false,
          "url":"../mtStock/mtStockIndex.do",
          "users":null,
        },
        {
          "name":"让步申请",
          "mid":"ue",
          "checked":false,
          "pid":"ua",
          "disabled":false,
          "url":"../mtStock/mtStockGiveInOne.do",
          "users":null,
        },
        {
          "name":"商品管理",
          "mid":"pa",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":null,
          "users":null,
        },
        {
          "name":"工序信息",
          "mid":"pd",
          "checked":false,
          "pid":"pa",
          "disabled":false,
          "url":"../commodity/process.do",
          "users":null,
        },
        {
          "name":"包装信息",
          "mid":"pe",
          "checked":false,
          "pid":"pa",
          "disabled":false,
          "url":"../pack/packIndex.do",
          "users":null,
        },
        {
          "name":"工序管理",
          "mid":"pl",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":null,
          "users":null,
        },
        {
          "name":"工序设置",
          "mid":"pm",
          "checked":false,
          "pid":"pl",
          "disabled":false,
          "url":"../processSettings/toProcessSettings.do",
          "users":null,
        },
        {
          "name":"工序查看",
          "mid":"pn",
          "checked":false,
          "pid":"pl",
          "disabled":false,
          "url":"../processView/toProcessView.do",
          "users":null,
        },
        {
          "name":"物料管理",
          "mid":"oa",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":null,
          "users":null,
        },
        {
          "name":"物料信息",
          "mid":"ob",
          "checked":false,
          "pid":"oa",
          "disabled":false,
          "url":"../material/materialManage.do",
          "users":null,
        },
        {
          "name":"供应商名录",
          "mid":"oc",
          "checked":false,
          "pid":"oa",
          "disabled":false,
          "url":"../material/supplierDirectory.do",
          "users":null,
        },
        {
          "name":"配方管理",
          "mid":"ui",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"",
          "users":null,
        },
        {
          "name":"配方",
          "mid":"uj",
          "checked":false,
          "pid":"ui",
          "disabled":false,
          "url":"../formula/formula.do",
          "users":null,
        },
        {
          "name":"材料",
          "mid":"uk",
          "checked":false,
          "pid":"ui",
          "disabled":false,
          "url":"../formula/materialsInFormula.do",
          "users":null,
        },
        {
          "name":"启用管理",
          "mid":"ia",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"../startManage/toStartManages.do",
          "users":null,
        },
        {
          "name":"直播助手",
          "mid":"ib",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"../",
          "users":null,
        },
        {
          "name":"项目管理",
          "mid":"ta",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"",
          "users":null,
        },
        {
          "name":"综合管理",
          "mid":"tb",
          "checked":false,
          "pid":"ta",
          "disabled":false,
          "url":"../project/toProjectBaseManage.do",
          "users":null,
        },
        {
          "name":"立案",
          "mid":"tc",
          "checked":false,
          "pid":"ta",
          "disabled":false,
          "url":"../project/toProjectBaseIndex.do",
          "users":null,
        },
        {
          "name":"投诉管理",
          "mid":"fb",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"../coreSetting/toIntegrateManage.do",
          "users":null,
        },
        {
          "name":"投诉录入",
          "mid":"fc",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"../coreSetting/toComplaintFiling.do",
          "users":null,
        },
        {
          "name":"投诉处理",
          "mid":"fd",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"../coreSetting/toComplaintHandling.do",
          "users":null,
        },
        {
          "name":"持续改进",
          "mid":"ha",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":null,
          "users":null,
        },
        {
          "name":"综合管理",
          "mid":"hb",
          "checked":false,
          "pid":"ha",
          "disabled":false,
          "url":"../improvement/integrateManage.do",
          "users":null,
        },
        {
          "name":"立案",
          "mid":"hc",
          "checked":false,
          "pid":"ha",
          "disabled":false,
          "url":"../improvement/improvementFiling.do",
          "users":null,
        },
        {
          "name":"备忘与日程",
          "mid":"ja",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"",
          "users":null,
        },
        {
          "name":"事件管理",
          "mid":"jc",
          "checked":false,
          "pid":"ja",
          "disabled":false,
          "url":"../schedule/toScheduleManage.do",
          "users":null,
        },
        {
          "name":"工作记录点评",
          "mid":"km",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"../mywork/toWorkReviewPage.do",
          "users":null,
        },
        {
          "name":"职工导入",
          "mid":"ko",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"../org/toUserImport.do",
          "users":null,
        },
        {
          "name":"企业需求调查",
          "mid":"ku",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":null,
          "users":null,
        },

        {
          "name":"二维码",
          "mid":"kv",
          "checked":false,
          "pid":"ku",
          "disabled":false,
          "url":"../survey/goQRcode.do",
          "users":null,
        },
        {
          "name":"信息调查",
          "mid":"kw",
          "checked":false,
          "pid":"ku",
          "disabled":false,
          "url":"../survey/goSurveyAnswerList.do",
          "users":null,
        },
        {
          "name":"调查管理",
          "mid":"kx",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":null,
          "users":null,
        },
        {
          "name":"问卷库",
          "mid":"ky",
          "checked":false,
          "pid":"kx",
          "disabled":false,
          "url":"../investigationManage/goQuestionBank.do",
          "users":null,
        },
        {
          "name":"调查管理",
          "mid":"kz",
          "checked":false,
          "pid":"kx",
          "disabled":false,
          "url":"../investigationManage/goInvestigation.do",
          "users":null,
        },
        {
          "name":"常规借款",
          "mid":"lt",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"../loan/ordinaryLoanList.do",
          "users":null,
        },
        {
          "name":"高管管理",
          "mid":"mb",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"../sys/toManagesList.do",
          "users":null,
        },
        {
          "name":"中枢管控",
          "mid":"mc",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"../sys/toManagesList.do",
          "users":null,
        },
        {
          "name":"冻结账号",
          "mid":"md",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"../userLock/frozenAccount.do",
          "users":null,
        },
        {
          "name":"购销统筹",
          "mid":"ql",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"../sales/salesPlan.do",
          "users":null,
        },
        {
          "name":"供应商",
          "mid":"qm",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":null,
          "users":null,
        },
        {
          "name":"常规信息",
          "mid":"qn",
          "checked":false,
          "pid":"qm",
          "disabled":false,
          "url":"../supplier/index.do",
          "users":null,
        },
        {
          "name":"MCI",
          "mid":"qo",
          "checked":false,
          "pid":"qm",
          "disabled":false,
          "url":"../eis/index.do",
          "users":null,
        },
        {
          "name":"装备器具",
          "mid":"qp",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":null,
          "users":null,
        },
        {
          "name":"装备清单",
          "mid":"qq",
          "checked":false,
          "pid":"qp",
          "disabled":false,
          "url":"../equitment/equitmentIndex.do",
          "users":null,
        },
        {
          "name":"名称管理",
          "mid":"qr",
          "checked":false,
          "pid":"qp",
          "disabled":false,
          "url":"../equitment/equitmentName.do",
          "users":null,
        },
        {
          "name":"类别管理",
          "mid":"qs",
          "checked":false,
          "pid":"qp",
          "disabled":false,
          "url":"../equitment/equitmentCategory.do",
          "users":null,
        },
        {
          "name":"供应商",
          "mid":"qt",
          "checked":false,
          "pid":"qp",
          "disabled":false,
          "url":"../equitment/equitmentSupplier.do",
          "users":null,
        },
        {
          "name":"补录",
          "mid":"qu",
          "checked":false,
          "pid":"qp",
          "disabled":false,
          "url":"../equitment/equitmentAddRe.do",
          "users":null,
        },
        {
          "name":"文件与资料",
          "mid":"ra",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":null,
          "users":null,
        },
        {
          "name":"文件夹管理",
          "mid":"rb",
          "checked":false,
          "pid":"ra",
          "disabled":false,
          "url":"../res/gongGao.do",
          "users":null,
        },
        {
          "name":"文件管理",
          "mid":"rc",
          "checked":false,
          "pid":"ra",
          "disabled":false,
          "url":"../res/wenJian.do",
          "users":null,
        },
        {
          "name":"表格管理",
          "mid":"rd",
          "checked":false,
          "pid":"ra",
          "disabled":false,
          "url":"../res/resFormManage.do",
          "users":null,
        },
        {
          "name":"会计管理",
          "mid":"sa",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"",
          "users":null,
        },
        {
          "name":"科目管理",
          "mid":"sb",
          "checked":false,
          "pid":"sa",
          "disabled":false,
          "url":"../accountant/subjectSet.do",
          "users":null,
        },
        {
          "name":"建账管理",
          "mid":"sc",
          "checked":false,
          "pid":"sa",
          "disabled":false,
          "url":"../accountant/buildMange.do",
          "users":null,
        },
        {
          "name":"凭证管理",
          "mid":"sd",
          "checked":false,
          "pid":"sa",
          "disabled":false,
          "url":"../accountant/voucherManage.do",
          "users":null,
        },
        {
          "name":"会计录入",
          "mid":"sg",
          "checked":false,
          "pid":"sa",
          "disabled":false,
          "url":"../accountant/accountantImport.do",
          "users":null,
        },
        {
          "name":"结账管理",
          "mid":"sh",
          "checked":false,
          "pid":"sa",
          "disabled":false,
          "url":"../accountant/settleAccounts.do",
          "users":null,
        },
        {
          "name":"质量管理",
          "mid":"va",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":null,
          "users":null,
        },
        {
          "name":"入库检验",
          "mid":"vb",
          "checked":false,
          "pid":"va",
          "disabled":false,
          "url":"../mtStock/MtStockQuality.do",
          "users":null,
        },
        {
          "name":"技术管理",
          "mid":"wa",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":null,
          "users":null,
        },
        {
          "name":"产品档案",
          "mid":"pc",
          "checked":false,
          "pid":"wa",
          "disabled":false,
          "url":"../commodity/composition.do",
          "users":null,
        },
        {
          "name":"让步受理",
          "mid":"wb",
          "checked":false,
          "pid":"wa",
          "disabled":false,
          "url":"../mtStock/mtStockGiveInTwo.do",
          "users":null,
        },
        {
          "name":"关联管理",
          "mid":"wc",
          "checked":false,
          "pid":"wa",
          "disabled":false,
          "url":"../commodity/relatedCommodities.do",
          "users":null,
        },
        {
          "name":"构成处理",
          "mid":"wd",
          "checked":false,
          "pid":"wa",
          "disabled":false,
          "url":"../mtStock/constituteManage.do",
          "users":null,
        },
        {
          "name":"材料",
          "mid":"we",
          "checked":false,
          "pid":"wa",
          "disabled":false,
          "url":"../mtStock/material.do",
          "users":null,
        },
        {
          "name":"零组件档案",
          "mid":"wf",
          "checked":false,
          "pid":"wa",
          "disabled":false,
          "url":"../parts/partsIndex.do",
          "users":null,
        },
        {
          "name":"仓库管理",
          "mid":"xa",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":null,
          "users":null,
        },
        {
          "name":"成品库",
          "mid":"xb",
          "checked":false,
          "pid":"xa",
          "disabled":false,
          "url":"../mtStock/MtStockApproval.do",
          "users":null,
        },
        {
          "name":"库存查询",
          "mid":"xd",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":null,
          "users":null,
        },
        {
          "name":"原辅材料查询",
          "mid":"xe",
          "checked":false,
          "pid":"xd",
          "disabled":false,
          "url":"../mt/rawMt",
          "users":null,
        },
        {
          "name":"成品查询",
          "mid":"xf",
          "checked":false,
          "pid":"xd",
          "disabled":false,
          "url":"../finishedOverview/queryHome.do",
          "users":null,
        },
        {
          "name":"原辅材料库",
          "mid":"xg",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"",
          "users":null,
        },
        {
          "name":"仓库管理",
          "mid":"xh",
          "checked":false,
          "pid":"xg",
          "disabled":false,
          "url":"/mt/mtStore.do",
          "users":null,
        },
        {
          "name":"盘点管理",
          "mid":"xi",
          "checked":false,
          "pid":"xg",
          "disabled":false,
          "url":"/mt/inventoryMent",
          "users":null,
        },
        {
          "name":"手动入库",
          "mid":"xj",
          "checked":false,
          "pid":"xg",
          "disabled":false,
          "url":"/mt/manualWareh",
          "users":null,
        },
        {
          "name":"手动领料",
          "mid":"xk",
          "checked":false,
          "pid":"xg",
          "disabled":false,
          "url":"/mt/manualResition",
          "users":null,
        },
        {
          "name":"出入库记录",
          "mid":"xl",
          "checked":false,
          "pid":"xg",
          "disabled":false,
          "url":"/mt/entryExitRecords",
          "users":null,
        },
        {
          "name":"物流管理",
          "mid":"ya",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":null,
          "users":null,
        },
        {
          "name":"发货管理",
          "mid":"yb",
          "checked":false,
          "pid":"ya",
          "disabled":false,
          "url":"../inOutStock/shippingManagement.do",
          "users":null,
        },
        {
          "name":"签收管理",
          "mid":"yc",
          "checked":false,
          "pid":"ya",
          "disabled":false,
          "url":"../inOutStock/receiptManagement.do",
          "users":null,
        },
        {
          "name":"采购",
          "mid":"za",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"",
          "users":null,
        },
        {
          "name":"入库",
          "mid":"zb",
          "checked":false,
          "pid":"za",
          "disabled":false,
          "url":"../inStock/inStockIndex.do",
          "users":null,
        },
        {
          "name":"材料录入",
          "mid":"zc",
          "checked":false,
          "pid":"za",
          "disabled":false,
          "url":"../mt/in.do",
          "users":null,
        },
        {
          "name":"材料管理",
          "mid":"zd",
          "checked":false,
          "pid":"za",
          "disabled":false,
          "url":"../mt/manage.do",
          "users":null,
        },
        {
          "name":"订单管理",
          "mid":"ze",
          "checked":false,
          "pid":"za",
          "disabled":false,
          "url":"../po/index.do",
          "users":null,
        },
        {
          "name":"收货地点",
          "mid":"zf",
          "checked":false,
          "pid":"za",
          "disabled":false,
          "url":"../dac/index.do",
          "users":null,
        },
        {
          "name":"企业云盘",
          "mid":"zaa",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"http://seafilepro.btransmission.com",
          "users":null,
        },
        {
          "name":"个人中心",
          "mid":"ma",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":null,
          "users":null,
        },
        {
          "name":"系统设置",
          "mid":"mf",
          "checked":false,
          "pid":"ma",
          "disabled":false,
          "url":"../user/systemSet.do",
          "users":null,
        },
        {
          "name":"登录设置",
          "mid":"mi",
          "checked":false,
          "pid":"ma",
          "disabled":false,
          "url":"../loginSettings/toLoginSettings.do",
          "users":null,
        },
        {
          "name":"参考资料",
          "mid":"na",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":null,
          "users":null,
        },
        {
          "name":"类别设置",
          "mid":"nb",
          "checked":false,
          "pid":"na",
          "disabled":false,
          "url":"../reference/goResourceCatory.do",
          "users":null,
        },
        {
          "name":"文件管理",
          "mid":"nc",
          "checked":false,
          "pid":"na",
          "disabled":false,
          "url":"../reference/goResourceWenjian.do",
          "users":null,
        },
        {
          "name":"阅览室",
          "mid":"rh",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"",
          "users":null,
        },
        {
          "name":"文件与资料",
          "mid":"ri",
          "checked":false,
          "pid":"rh",
          "disabled":false,
          "url":"../read/readingRoom.do",
          "users":null,
        },
        {
          "name":"讨论组",
          "mid":"rj",
          "checked":false,
          "pid":"rh",
          "disabled":false,
          "url":"../read/archivedForum.do",
          "users":null,
        },
        {
          "name":"小程序",
          "mid":"ro",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"",
          "users":null,
        },
        {
          "name":"目录管理",
          "mid":"rp",
          "checked":false,
          "pid":"ro",
          "disabled":false,
          "url":"../mpRes/wxResDirection.do",
          "users":null,
        },
        {
          "name":"文章管理",
          "mid":"rq",
          "checked":false,
          "pid":"ro",
          "disabled":false,
          "url":"../mpRes/wxResContentManage.do",
          "users":null,
        },
        {
          "name":"日常事务",
          "mid":"db",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"../daily/dailyAffairs.do",
          "users":null,
        },
        {
          "name":"回款录入",
          "mid":"gb",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"../salesBack/cashBackInput.do",
          "users":null,
        },
        {
          "name":"潜在客户",
          "mid":"gc",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"../sale/potentialCustomer.do",
          "users":null,
        },
        {
          "name":"访谈记录",
          "mid":"gd",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"../sales/interviewManag.do",
          "users":null,
        },
        {
          "name":"计量单位",
          "mid":"dc",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"../unit/unitManage.do",
          "users":null,
        },
        {
          "name":"领料分工",
          "mid":"ge",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"../picking/index.do",
          "users":null,
        },
        {
          "name":"我的团队",
          "mid":"dd",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"",
          "users":null,
        },
        {
          "name":"团队考勤",
          "mid":"de",
          "checked":false,
          "pid":"dd",
          "disabled":false,
          "url":"../workAttendanceTeam/attendancePage.do",
          "users":null,
        },
        {
          "name":"登录记录",
          "mid":"df",
          "checked":false,
          "pid":"dd",
          "disabled":false,
          "url":"../loginTeam/getLoginPage.do",
          "users":null,
        },
        {
          "name":"请假动态",
          "mid":"dg",
          "checked":false,
          "pid":"dd",
          "disabled":false,
          "url":"../",
          "users":null,
        },
        {
          "name":"加班动态",
          "mid":"dh",
          "checked":false,
          "pid":"dd",
          "disabled":false,
          "url":"../",
          "users":null,
        },
        {
          "name":"工作记录",
          "mid":"di",
          "checked":false,
          "pid":"dd",
          "disabled":false,
          "url":"../mywork/toMyTeamPage.do",
          "users":null,
        },
        {
          "name":"跨机构访问",
          "mid":"dj",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":"../sonOrg/toInteragencyVisit.do",
          "users":null,
        },
        {
          "name":"培训管理",
          "mid":"nf",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":null,
          "users":null,
        },
        {
          "name":"试题库",
          "mid":"ng",
          "checked":false,
          "pid":"nf",
          "disabled":false,
          "url":"../trainManage/goQuestionBank.do",
          "users":null,
        },
        {
          "name":"考核管理",
          "mid":"nh",
          "checked":false,
          "pid":"nf",
          "disabled":false,
          "url":"../trainManage/goAssessManage.do",
          "users":null,
        },
        {
          "name":"公共信息",
          "mid":"ni",
          "checked":false,
          "pid":"0",
          "disabled":false,
          "url":null,
          "users":null,
        },
        {
          "name":"目录",
          "mid":"nj",
          "checked":false,
          "pid":"ni",
          "disabled":false,
          "url":"../pi/publicDirection.do",
          "users":null,
        },
        {
          "name":"信息",
          "mid":"nk",
          "checked":false,
          "pid":"ni",
          "disabled":false,
          "url":"../pi/publicInformation.do",
          "users":null,
        },
      ]
  return singleMenus
}


