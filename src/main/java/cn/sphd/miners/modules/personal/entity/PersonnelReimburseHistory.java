package cn.sphd.miners.modules.personal.entity;

import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.annotation.JSONField;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by Administrator on 2016/12/7.
 */
@Entity
@Table(name="t_personnel_reimburse_history")
public class PersonnelReimburseHistory implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="title"  , length=100 , nullable=true , unique=false)
    private String title;//标题

    @Column(name="fee_cat"  ,  nullable=true , unique=false)
    private Integer feeCat;//费用类别 (字典表id)

    @Column(name="bill_cat"  , nullable=true , unique=false)
    private Integer billCat;//票据类型 (字典表id)

    @Column(name="fee_cat_name" ,length=100 ,  nullable=true , unique=false)
    private String feeCatName;//费用类别名称

    @Column(name="bill_cat_name"  ,length=100 , nullable=true , unique=false)
    private String billCatName;//票据类型名称

    @Column(name="summary"  , length=255 , nullable=true , unique=false)
    private String summary;//摘要

    @Column(name="purpose"  , length=255 , nullable=true , unique=false)
    private String purpose;//用途

    @Column(name="bill_quantity"   , nullable=true , unique=false)
    private Integer billQuantity;//票据数量

    @Column(name="bill_date"  , length=100 , nullable=true , unique=false)
    private String billDate;//发票所属月份,1-本月票据,2-非本月票据

    @Column(name="amount"   , nullable=true , unique=false)
    private BigDecimal amount;//合计金额

    @Column(name="bill_amount"   , nullable=true , unique=false)
    private BigDecimal billAmount;  //票面金额

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;//备注

    @Column(name="pay_method"  , length=1 , nullable=true , unique=false)
    private String payMethod;//支出方式,0-未两讫,1-现金,2-现金支票,3-银行转帐

    @Column(name="pay_date"   , nullable=true , unique=false)
    @Temporal(TemporalType.DATE)
    private Date payDate;//支出日期

    @Column(name="account"  , length=255 , nullable=true , unique=false)
    private String account;//银行帐号

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;//创建人id'

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;//创建人

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;//创建时间

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;//修改人id'

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;//修改人

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;//修改时间

    @Column(name="approve_Item"   , nullable=true , unique=false)
    private Integer approveItem;//申批项目

    @Column(name="approve_status"  , length=1 , nullable=true , unique=false)
    private String approveStatus;//审批状态：0-未申请 ，1-申请提交，2-通过审核，3-否决审核

    @Column(name="approve_level"   , nullable=true , unique=false)
    private Integer approveLevel;//审批次级

    @Column(name="auditor"   , nullable=true , unique=false)
    private Integer auditor;//审批者ID'

    @Column(name="auditor_name"  , length=100 , nullable=true , unique=false)
    private String auditorName;//审批者

    @Column(name="audit_date"   , nullable=true , unique=false)
    private Date auditDate;//审批日期

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;//操作：1-增，2—删，3-—改

    @Column(name="apply_memo"  , length=255 , nullable=true , unique=false)
    private String applyMemo;//申请备注

    @Column(name="approve_memo"  , length=255 , nullable=true , unique=false)
    private String approveMemo;//审批备注

    @Column(name="message_id"   , nullable=true , unique=false)
    private Integer messageId;//对应消息表的id'

    @Column(name="reimburse_id"   , nullable=true , unique=false)
    private Integer reimburseId;//对应原始的报销id

    @Column(name="type"  , length=1 , nullable=true , unique=false)
    private String type;   //1-初始金额冲减,2-转帐交易,3-收入,4-支出(1,2类型不允许提交修改申请),转帐为二条记录,5-内部非支出性转账

    @Column(name="sub_type"  , length=1 , nullable=true , unique=false)
    private String subType;   //帐务子类:支出时,1-本人的公务支出（财务费用除外、需入库物品的报销除外）,2-其他同事的公务支出（财务费用除外，需入库物品的报销除外）,3-需入库物品的报销,4-税款,5-汇划费,6-其他财务费用的支出,7-以上类别以外的支出

    @Column(name = "fact_user")
    private Integer factUser;//实际用户(同事userid)',

//    @ManyToOne(fetch= FetchType.LAZY )
//    @JoinColumn(name="user", referencedColumnName = "userId" , nullable=true , unique=false , insertable=true, updatable=true)
//    private User user;//
//
//    @Column(name="user"  , nullable=true , unique=false, insertable=false, updatable=false)
//    private Integer user_;//

    //员工报销附件表
    @JSONField(serialize = false)
    @OneToMany(targetEntity=PersonnelReimbursetAttachmentHistory.class, fetch= FetchType.LAZY, mappedBy="reimbursetH", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<PersonnelReimbursetAttachmentHistory> personnelReimbursetAttachmentHistoryHashSet = new HashSet<PersonnelReimbursetAttachmentHistory>();


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getFeeCat() {
        return feeCat;
    }

    public void setFeeCat(Integer feeCat) {
        this.feeCat = feeCat;
    }

    public Integer getBillCat() {
        return billCat;
    }

    public void setBillCat(Integer billCat) {
        this.billCat = billCat;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public Integer getBillQuantity() {
        return billQuantity;
    }

    public void setBillQuantity(Integer billQuantity) {
        this.billQuantity = billQuantity;
    }

    public String getBillDate() {
        return billDate;
    }

    public void setBillDate(String billDate) {
        this.billDate = billDate;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public Date getPayDate() {
        return payDate;
    }

    public void setPayDate(Date payDate) {
        this.payDate = payDate;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

//    public User getUser() {
//        return user;
//    }
//
//    public void setUser(User user) {
//        this.user = user;
//    }
//
//    public Integer getUser_() {
//        return user_;
//    }
//
//    public void setUser_(Integer user_) {
//        this.user_ = user_;
//    }


    public Set<PersonnelReimbursetAttachmentHistory> getPersonnelReimbursetAttachmentHistoryHashSet() {
        return personnelReimbursetAttachmentHistoryHashSet;
    }

    public void setPersonnelReimbursetAttachmentHistoryHashSet(Set<PersonnelReimbursetAttachmentHistory> personnelReimbursetAttachmentHistoryHashSet) {
        this.personnelReimbursetAttachmentHistoryHashSet = personnelReimbursetAttachmentHistoryHashSet;
    }

    public String getFeeCatName() {
        return feeCatName;
    }

    public void setFeeCatName(String feeCatName) {
        this.feeCatName = feeCatName;
    }

    public String getBillCatName() {
        return billCatName;
    }

    public void setBillCatName(String billCatName) {
        this.billCatName = billCatName;
    }

    public Integer getReimburseId() {
        return reimburseId;
    }

    public void setReimburseId(Integer reimburseId) {
        this.reimburseId = reimburseId;
    }

    public BigDecimal getBillAmount() {
        return billAmount;
    }

    public void setBillAmount(BigDecimal billAmount) {
        this.billAmount = billAmount;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSubType() {
        return subType;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public Integer getFactUser() {
        return factUser;
    }

    public void setFactUser(Integer factUser) {
        this.factUser = factUser;
    }
}
