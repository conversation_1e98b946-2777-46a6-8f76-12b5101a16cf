<template>
  <div id="equipmentList">
    <div class="mainCon mainCon0" v-if="mainNum === 'page0'">
      <div class="rowGap">
        <el-row>
          <el-col :span="12">
            待处理的数据共{{equipInfo.noCategory || 0}}条
            <el-link type="primary" class="gapLt" @click="equipUnHandle()">去处理</el-link>
          </el-col>
          <el-col :span="12" class="txtRight">
            经处理，已确认不属于装备器具的数据共{{equipInfo.noc || 0}}条
            <el-link type="info" class="gapLt">去查看</el-link>
          </el-col>
        </el-row>
      </div>
      <div class="hrLine"></div>
      <el-row class="rowGap">
        <el-col :span="6">以下数据共{{equipInfo.yetCategory || 0}}条</el-col>
        <el-col :span="18">
          <div class="filter">
            <span class="gapRt">筛选</span>
            <input type="text" v-model="filterCat" readonly class="form-control funBtn" @click="filterCatBtn('filterCatBtn')"/>
            <i class="fa fa-angle-down"></i>
          </div>
        </el-col>
      </el-row>
      <div>
        <table class="ty-table ty-table-control">
          <thead>
          <tr>
            <td>装备器具名称/型号</td>
            <td>来源</td>
            <td>单位</td>
            <td>数量</td>
            <td>所属类别</td>
            <td>参与加工的产品</td>
            <td>操作</td>
          </tr>
          </thead>
          <tbody id="allList">
          <tr  v-for="(item, index) in equipInfo.data" :key="index">
            <td>{{ item.equipmentName || '' }}/{{ item.modelName }}</td>
            <td>{{ item.supplierName || '未知' }}</td>
            <td>{{ item.units || '' }}</td>
            <td>{{ item.length }}</td>
            <td>{{ item.path }}</td>
            <td class="ty-td-control"><span class="ty-color-blue">{{item.productCount || '0' }} 种</span></td>
            <td>
              <span class="ty-color-blue" @click="manageBtn(item)">管理</span>
            </td>
          </tr>
          </tbody>

        </table>
        <TyPage
            :curPage="equipInfo.pageInfo.currentPageNo" :pageSize="equipInfo.pageInfo.pageSize"
            :allPage="equipInfo.pageInfo.totalPage" :pageClickFun="pageInfoControl"></TyPage>
      </div>
    </div>
    <div class="mainCon mainCon1" v-if="mainNum === 'page1'">
      <div class="allWidth">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainNum = 'page0'">返 回</span>
        <div class="ty-right alltip">
          <i class="fa fa-circle-o" :class="{'fa-dot-circle-o': sflag}" @click="toogeCircle" id="sflag"></i>
          待处理数据共{{equipNoCategory.noCategory || 0}}条，本次操作确认了{{readySetNum}}条。本次操作已完成！
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 gapLt" @click="selectCatSure()">确  定</span>
        </div>
      </div>
      <div>
        <div class="clear gapB">
          <div class="ty-left">
            <div>以下为待处理的数据，请确认哪些属于装备器具，并对属于装备器具的选定类别。</div>
            <div>需分类的数据较多时，可“批量处理”。</div>
          </div>
          <span class="ty-right linkBtn" @click="batchClass">批量处理</span>
        </div>
        <table class="ty-table ty-table-control">
          <thead>
          <tr>
            <td>系统赋予的编码</td>
            <td>装备器具编号/名称/型号</td>
            <td>来源</td>
            <td colspan="2">所属类别</td>
          </tr>
          </thead>
          <tbody id="tbContent">
          <tr v-for="(item, index) in equipNoCategory.data" :class="{'isMore' : item.size > 1 , 'isOne':item.size === 1 }" :key="index">
            <td>{{ item.lpadId }}</td>
            <td>{{ (item.modelCode || '') }}/{{ item.equipmentName || '' }}/{{ item.modelName }}</td>
            <td>{{ item.supplierName || '未知' }}</td>
            <td>{{ item.path === '--' ? '尚未选择':item.path  }}</td>
            <td class="grayBg" data-align="1" @click="getSameEqList(item, index)"><i class="fa fa-sort-down"></i>
            </td>
          </tr>
          </tbody>
        </table>
        <TyPage
            :curPage="equipNoCategory.pageInfo.currentPageNo" :pageSize="equipNoCategory.pageInfo.pageSize"
            :allPage="equipNoCategory.pageInfo.totalPage" :pageClickFun="pageInfoUnHandle"></TyPage>
      </div>
    </div>
    <div class="mainCon mainCon2" v-if="mainNum === 'page2'">
      <div class="allWidth">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainNum = 'page0'">返回主页</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 gapNr" @click="mainNum = 'page1'">返回上一页</span>
        <div class="ty-right">
          <i class="fa" :class="{'fa-circle-o': !dotState, 'fa-circle': dotState}" @click="toogeCircle2" id="toogeCircle"></i>
          待处理数据共<span id="weiNum">{{equipNoCategory.noCategory || 0}}</span>条，本次操作确认了<span id="selectNum">{{batchSelectNum}}</span>条。本次操作已完成！
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 gapLt" @click="saveBatchCat">确定</span>
        </div>
      </div>
      <div class="pa40">
        <p>点击类别右侧的“<span class="fa fa-angle-right"></span>”（进入符号）后，可在随后所见的页上选择属于该类别的装备器具。</p>
        <p>带有子类别的类别右侧无“<span class="fa fa-angle-right"></span>”，但左侧带有“<span class="fa fa-angle-double-down"></span>”（展开符号）。点击该符号后，将展示该类别下的子类别。</p>
      </div>
      <div id="batchClassCon">
        <el-tree :props="categoryProps" :load="loadBatch" lazy>
          <template #default="{ node, data }">
            <div>
              <span class="fa fa-angle-right angleBtn" @click="selectEqBtn(data)" v-if="data.childrens === 0"></span>
              <div class="catItemName funBtn">
                {{ data['name'] }}：{{ data['content'] }} （已选：<span class="selectNum">0</span>项）---- 子级类别 有 {{ data.childrens}}个
              </div>
            </div>
          </template>
        </el-tree>
      </div>
    </div>
    <div class="mainCon mainCon3" v-if="mainNum === 'page3'">
      <div>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainNum='page0'">返回主页</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 gapNr" @click="mainNum='page2'">返回上一页</span>
        <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="selectEQOK" data-num="2">确 定</span>
      </div>
      <div style="padding:20px 0;">
        <p>
          请选择属于<span id="catPath">{{selectedCatItem.name}}</span>的装备器具。
          <span class="ty-right selectCount">已选：<span id="cSelectNum">{{checkedNum}}</span>项</span>
        </p>
        <table class="ty-table" id="selectTab">
          <tbody>
          <tr>
            <td>系统赋予的编码</td>
            <td>固定资产编号</td>
            <td>固定资产名称</td>
            <td>型号</td>
            <td>创建</td>
          </tr>
          <tr v-for="(item, index) in batchEqData.list" :class="{'isMore' : item.size > 1 , 'isOne':item.size === 1 }" :key="index">
            <td>
              <div class="tdRela">
                <i class="fa" :class="{'fa-square-o': !item.checked, 'fa-check-square-o': item.checked}" @click="checkBtn(item, index)"></i>
                {{ item.lpadId || '' }}
              </div>
            </td>
            <td>{{ item.modelCode || '' }}</td>
            <td>{{ item.equipmentName || '' }}</td>
            <td>{{ item.modelName }}</td>
            <td>{{ item.createName }}{{ new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')}}</td>
          </tr>
          </tbody>
        </table>
      </div>

    </div>
    <div class="mainCon mainCon4" v-if="mainNum === 'page4'">
      <div class="gapPage">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="filterCatBack()">返回</span>
      </div>
      <div style="padding:20px 0;">
        <p class="filterTxt">系统内的装备器具中，<span class="ty-color-blue">{{filterCat }}</span> 共 {{ filterEqListData.data.length }}台（套），具体如下：</p>
        <table class="ty-table ty-table-control">
          <thead>
          <tr>
            <td>装备器具名称/型号</td>
            <td>来源</td>
            <td>单位</td>
            <td>数量</td>
            <td>所属类别</td>
            <td>参与加工的产品</td>
            <td>操作</td>
          </tr>
          </thead>
          <tbody id="tbContent2">
          <tr v-for="(item, index) in filterEqListData.data" :key="index">
            <td>{{ item.equipmentName || '' }}/{{ item.modelName }}</td>
            <td>{{ item.supplierName || '未知' }}</td>
            <td>{{ item.units || '' }}</td>
            <td>{{ item.length }}</td>
            <td>{{ item.path }}</td>
            <td class="ty-td-control"><span class="ty-color-blue">{{ item.productCount || '0' }}种</span></td>
            <td>
              <span class="ty-color-blue" @click="manageBtn(item)">管理</span>
            </td>
          </tr>
          </tbody>
        </table>
        <TyPage
            :curPage="filterEqListData.pageInfo.currentPageNo" :pageSize="filterEqListData.pageInfo.pageSize"
            :allPage="filterEqListData.pageInfo.totalPage" :pageClickFun="pageInfoFilter"></TyPage>

      </div>

    </div>
    <div class="mainCon mainCon5" v-if="mainNum === 'page5'">
      <div>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainNum='page0'">返回主页</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 gapNr" @click="mainNum='page2'" id="subPage">返回上一页</span>
        <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="sameEqComplete" data-num="1">确 定</span>
        <div class="ty-right setTip"><i class="fa" :class="{'fa-circle-o': !finishState, 'fa-circle': finishState}" @click="toogeCircle3"></i>  设置完成！</div>
      </div>
      <div style="padding:40px 0;">
        <p><span class="sameTip">以下{{eqGroupList.length}}台设备的名称均为{{equipmentInfo.equipmentName}}，型号均为{{equipmentInfo.modelName}}，来源均为{{(equipmentInfo.supplierName == 0 ? '整机系自行装配（零部件可能为外购、外加工或自制）' : (equipmentInfo.supplierName || '未知'))}}。</span>为防遗漏，特一并列出，请为这些设备设置类别。</p>
        <div class="clear">
          <div class="ty-left">
            <div class="ty-color-blue">注1 设置时，可统一设置，也可分别设置。</div>
            <div class="ty-color-blue">注2 设置规则为，如选择了一二三四类中的某个类别，则不可以在选择一二三四类中的其他类别。</div>
          </div>
          <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="initUniform()">统一设置</span>
        </div>
        <table class="ty-table" id="selectCatPath">
          <tbody>
          <tr>
            <td>系统赋予的编码</td>
            <td>装备器具编号</td>
            <td>创建</td>
            <td colspan="2">所属类别</td>
          </tr>
          <tr v-for="(item, index) in eqGroupList" :key="index">
            <td>{{ item.lpadId }}</td>
            <td>{{ item.modelCode || '' }}</td>
            <td>{{ item.createName }} {{ new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss') }}</td>
            <td>{{ item.path === '--' ? '尚未选择':item.path }}</td>
            <td class="grayBg funBtn" @click="selectPathBtn(index)">
              <i class="fa fa-sort-down"></i>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
    <TyDialog v-if="eqScanLog" width="1000" dialogTitle="装备器具管理" color="blue" :dialogHide="hideEqScanLog">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="hideEqScanLog">关 闭</el-button>
      </template>
      <template #dialogBody>
        <div class="singleSect" v-if="eqScanList.length === 1">
          <span>基本信息</span>
        </div>
        <table class="ty-table ty-table-control">
          <tbody>
          <tr>
            <td>
              <div>装备器具名称</div>
            </td>
            <td>
              <div>型号</div>
            </td>
            <td>
              <div>供应商/加工方</div>
            </td>
            <td>
              <div>单位</div>
            </td>
            <td>
              <div>数量</div>
            </td>
            <td>
              <div>操作</div>
            </td>
          </tr>
          <tr>
            <td>
              <div class="name">{{(eqScanDetail.equipmentName || '')}}</div>
            </td>
            <td>
              <div class="model">{{eqScanDetail.modelName || ''}}</div>
            </td>
            <td>
              <div class="sup">{{eqScanDetail.supplierName || '整机系自行装配（零部件可能为外购、外加工或自制）'}}</div>
            </td>
            <td>
              <div class="unit">{{eqScanDetail.unitName || ''}}</div>
            </td>
            <td>
              <div class="eqCount">{{eqScanDetail.length}}</div>
            </td>
            <td>
              <span class="ty-color-blue" @click="updateBtn()">修改</span>
              <span class="ty-color-blue" @click="editLog(eqScanDetail,1)">修改记录</span>
            </td>
          </tr>
          </tbody>
        </table>
        <div class="moreBd" v-if="eqScanList.length > 1"></div>
        <div class="singleSect gapTp" v-if="eqScanList.length === 1">
          <span>其他信息</span>
        </div>
        <div class="eqDetails">
          <div v-for="(item, index) in eqScanList" :key="index">
            <div>
              <span>创建： {{item.createName}} {{new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')}}</span>
              <span>系统赋予的编码：{{item.lpadId}}</span>
              <span>装备器具的编号：{{item.modelCode}}</span>
              <span class="ty-right cateCon" title="${item.path}">类别：{{item.path}}</span>
            </div>
            <table class="ty-table ty-table-control">
              <tbody>
              <tr>
                <td>
                  <div>到厂日期</div>
                </td>
                <td>
                  <div>到厂时的新旧情况</div>
                </td>
                <td>
                  <div>原值</div>
                </td>
                <td>
                  <div>预期的使用寿命</div>
                </td>
                <td>
                  <div>备注</div>
                </td>
                <td>
                  <div>操作</div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="factDate">{{new Date(item.receive_date).format('yyyyMM')}}</div>
                </td>
                <td>
                  <div class="newOrOld"> {{item.conditions == 1 ? '新' : '' }}{{item.conditions == 2 ? '旧' : '' }}</div>
                </td>
                <td>
                  <div class="oral">{{(item.original_value ? item.original_value + '元' : '')}}</div>
                </td>
                <td>
                  <div class="expireYear">{{item.life_span || ''}}</div>
                </td>
                <td>
                  <div class="memo" :title="(item.memo || '')">{{item.memo || ""}}</div>
                </td>
                <td>
                  <span class="ty-color-blue" @click="updateEqBtn(item, index)">修改</span>
                  <span class="ty-color-blue" @click="editLog(item, 2)">修改记录</span>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="msgEditLog" width="1200" :dialogTitle="msgEditTtl" color="blue" :dialogHide="hideMsgEditLog">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideMsgEditLog">取消</el-button>
        <el-button class="bounce-ok" @click="editMsgOk()">确定</el-button>
      </template>
      <template #dialogBody>
        <el-form ref="baseFormRef"
                 :model="baseForm"
                 :rules="baseFormRules">
          <div class="ty-color-blue publicInfor" v-if="editSource === 1 && eqScanDetail.length > 1">注：本处的修改对于页面上的{{ eqScanDetail.length }}台装备共同生效！</div>
          <div class="ty-color-blue morePartTip" v-if="editSource === 2 && eqScanDetail.length > 1">注：如修改装备器具的名称、型号或供应商/加工方，说明本台装备与其他{{ eqScanDetail.length-1 }}台装备不是相同的设备，修改后本台设备将不再展示于这些设备的列表中！</div>
          <table class="ty-table infoEdit">
            <tbody>
            <tr>
              <td>
                <div>装备器具名称<span class="ty-color-red">*</span><span class="linkBtn ty-right" @click="addName">新增</span></div>
              </td>
              <td width="150">
                <div>型号</div>
              </td>
              <td>
                <div>供应商/加工方<span class="linkBtn ty-right" @click="addSup">新增</span></div>
              </td>
              <td class="singlePart" v-if="editSource === 1 || editSource === 2 && baseForm.length === 1">
                <div>单位<span class="linkBtn ty-right" @click="addUnit">新增</span></div>
              </td>
              <td class="publicInfor" v-if="editSource === 1 && baseForm.length > 1">
                <div>数量</div>
              </td>
            </tr>
            <tr>
              <td>
                <el-form-item prop="equipment">
                  <el-select v-model="baseForm.equipment" placeholder="请选择">
                    <el-option v-for="item in eqNameList" :key="item.id" :value="item.id" :label="item['fullName']"/>
                  </el-select>
                </el-form-item>
              </td>
              <td>
                <el-form-item prop="modelName">
                  <el-input v-model="baseForm.modelName" placeholder="请录入" />
                </el-form-item>
              </td>
              <td>
                <el-form-item prop="supplier">
                  <el-select v-model="baseForm.supplier" placeholder="请选择">
                    <el-option v-for="item in supplierList" :key="item.id" :value="item.id" :label="item['fullName']"/>
                  </el-select>
                </el-form-item>
              </td>
              <td class="singlePart" v-if="editSource === 1 || editSource === 2 && baseForm.length === 1">
                <el-form-item prop="unitId">
                  <el-select v-model="baseForm.unitId" placeholder="请选择">
                    <el-option v-for="item in unitList" :key="item.id" :value="item.id" :label="item['name']"/>
                  </el-select>
                </el-form-item>
              </td>
              <td class="publicInfor" v-if="editSource === 1 && baseForm.length > 1">
                <el-form-item>
                  <el-input :value="baseForm.length" readonly @click="amountChangeTipLog = true" />
                </el-form-item>
              </td>
            </tr>
            </tbody>
          </table>
          <div class="equipEdit" v-if="editSource === 2">
            <div class="ty-hr"></div>
            <table class="ty-table">
              <tbody>
              <tr>
                <td width="15%"><div>装备器具编号</div></td>
                <td width="15%"><div>原值</div></td>
                <td width="15%"><div>预期的使用寿命</div></td>
                <td width="10%"><div>到厂日期</div></td>
                <td width="15%"><div>到厂时的新旧情况</div></td>
                <td width="10%"><div>类别</div></td>
                <td width="20%"><div>其他需要备注的内容</div></td>
              </tr>
              <tr>
                <td>
                  <el-form-item prop="modelCode">
                    <el-input v-model="baseForm.modelCode" placeholder="请录入" />
                  </el-form-item>
                </td>
                <td>
                  <div class="limitPos">
                    <el-form-item>
                      <el-input v-model="baseForm.originalValue" placeholder="请录入" @input="getLimitVal('originalValue')">
                        <template #append>元</template>
                      </el-input>
                    </el-form-item>
                  </div>
                </td>
                <td>
                  <div class="limitPos">
                    <el-form-item>
                      <el-input v-model="baseForm.lifeSpan" placeholder="请录入" @input="limitOnlyInteger('lifeSpan')">
                        <template #append>年</template>
                      </el-input>
                    </el-form-item>
                  </div>
                </td>
                <td>
                  <el-form-item>
                    <el-date-picker
                        v-model="baseForm.rDate"
                        type="month"
                        value-format="YYYYMM"
                        placeholder="请选择"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-select v-model="baseForm.conditions" class="form-control" id="conditions">
                    <el-option :value=1 label="新" />
                    <el-option :value=2 label="旧" />
                  </el-select>
                </td>
                <td>
                  <el-form-item>
                    <el-input v-model="baseForm.path" placeholder="请录入" @click="filterCatBtn('msgEditSelectCatBtn')"/>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item>
                    <el-input v-model="baseForm.memo" placeholder="可录入不超过50字"  maxlength="50" show-word-limit />
                  </el-form-item>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </el-form>
      </template>
    </TyDialog>
    <TyDialog v-if="amountChangeTipLog" width="500" dialogTitle="！提示" color="blue" :dialogHide="hideAmountChangeTipLog">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="hideAmountChangeTipLog">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center">
          <div>增加装备的数量，可通过”补录“的方式！</div>
          <div>减少装备的数量，请进行”停用“的操作！</div>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="editRecordLog" width="700" dialogTitle="修改记录" color="blue" :dialogHide="hideEditRecordLog">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="hideEditRecordLog">关 闭</el-button>
      </template>
      <template #dialogBody>
        <p v-if="editRecordList.length <= 0"><span>当前资料尚未经修改。</span><span class="ty-right"> 创建人：{{ eqScanDetail.createName }} {{ new Date(eqScanDetail.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span></p>
        <div v-if="editRecordList.length > 0">
          <p>
            <span>当前资料为第{{ editRecordList.length-1 }}次修改后的结果。</span><span class="ty-right"> 修改人：{{ editRecordList[editRecordList.length-1].createName }} {{ new Date(editRecordList[editRecordList.length-1].createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
          </p>
          <table class="ty-table" id="editLogTab">
            <tbody>
            <tr>
              <td>资料状态</td>
              <td>操作</td>
              <td>创建人/修改人</td>
            </tr>
            <tr v-for="(item, index) in editRecordList" :key="index">
              <td>{{ (index === 0 ? '原始信息': '第' + index +'次修改后') }}</td>
              <td class="ty-td-control">
                <span class="ty-color-blue" @click="logScan(item)">查看</span>
              </td>
              <td>{{ item.createName }} {{ new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss') }}</td>
            </tr>
            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="addNameLog" width="600" dialogTitle="新增名称" color="blue" :dialogHide="hideAddNameLog">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideAddNameLog">取消</el-button>
        <el-button class="bounce-ok" @click="addNameOk()">确定</el-button>
      </template>
      <template #dialogBody>
        <div>
          <el-form-item label="装备器具的名称" prop="eqName">
            <el-input v-model="eqName" placeholder="请录入" />
          </el-form-item>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="addSupLog" width="500" dialogTitle="增加供应商/加工方的选项" color="blue" :dialogHide="hideAddSupLog">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideAddSupLog">取消</el-button>
        <el-button class="bounce-ok" @click="addSupOk()">确定</el-button>
      </template>
      <template #dialogBody>
        <el-form
            ref="supplierRef"
            :model="supplierForm"
            :rules="supplierForRules"
            label-width="120px"
        >
          <el-form-item label="供应商/加工方名称" prop="fullName" placeholder="请录入">
            <el-input v-model="supplierForm.fullName" />
          </el-form-item>
          <el-form-item label="供应商/加工方简称" prop="name" placeholder="请录入">
            <el-input v-model="supplierForm.name" />
          </el-form-item>
          <el-form-item label="供应商/加工方代号" prop="codeName" placeholder="请录入">
            <el-input v-model="supplierForm.codeName" />
          </el-form-item>
          <span class="ty-color-blue">
                    注：对于供应商代号，系统仅要求不能重复，具体如何编号，请与采购部门沟通。如无规则请随意录入。
          </span>
        </el-form>
      </template>
    </TyDialog>
    <!--  新增计量单位   -->
    <TyDialog v-if="addUnitLog" width="500" dialogTitle="新增计量单位" color="blue" :dialogHide="hideAddUnitLog">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideAddUnitLog">取消</el-button>
        <el-button class="bounce-ok" @click="addUnitOkBtn(11)">确定</el-button>
      </template>
      <template #dialogBody>
        <el-form ref="unitForm" :model="unitForm" label-width="80px" label-position="top">
          <el-form-item label="计量单位">
            <el-input type="text" v-model="unitForm.name"  placeholder="请录入计量单位的名称"></el-input>
          </el-form-item>
        </el-form>
      </template>
    </TyDialog>
    <TyDialog v-if="uniformSettingsLog" width="600" dialogTitle="统一设置" color="blue" :dialogHide="hideUniformSettingsLog">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideUniformSettingsLog">取消</el-button>
        <el-button class="bounce-ok" @click="uniformSettingsSure()">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="catBody">
          <div>要统一设置为哪个类别？请选择！</div>
          <div class="ty-color-blue">注：“确定”后，各行均将加载为统一设置的类别，无论之前是否已单选！</div>
          <div class="uniformCatName">
            <el-input v-model="uniformCatName.id" type="hidden" />
            <input id="uniformCatName" v-model="uniformCatName.name" type="text" @click="filterCatBtn('uniformSet')" /><span class="downBtn"><i class="fa fa-sort-down"></i></span>
          </div>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="selectCatLog" width="600" dialogTitle="请选择类别" color="blue" :dialogHide="hideSelectCatLog">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideSelectCatLog">取消</el-button>
        <el-button class="bounce-ok" @click="selectCatOk()">确定</el-button>
      </template>
      <template #dialogBody>
        <div id="catContainer">
          <el-tree
              ref="catTree"
              :props="props"
              :load="loadNode"
              lazy
          >
          </el-tree>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="recordScanLog" width="1000" dialogTitle="装备器具信息查看" color="blue" :dialogHide="hideRecordScanLog">
      <template #dialogFooter>
      </template>
      <template #dialogBody>
        <table class="ty-table">
          <tr>
            <td>
              <div>装备器具名称</div>
            </td>
            <td>
              <div>型号</div>
            </td>
            <td>
              <div>供应商/加工方</div>
            </td>
            <td>
              <div>单位</div>
            </td>
            <td class="baseLogScan" v-if="recordNum === 1">
              <div>数量</div>
            </td>
          </tr>
          <tr>
            <td>{{recordScan.equipmentName}}</td>
            <td>{{recordScan.modelName}}</td>
            <td>{{recordScan.unitName}}</td>
            <td>{{recordScan.supplierName || '整机系自行装配（零部件可能为外购、外加工或自制）'}}</td>
            <td class="baseLogScan" v-if="recordNum === 1">
              <div class="amountLog">{{recordScan.quantity || 1}}</div>
            </td>
          </tr>
        </table>
        <table class="ty-table subLogScan" v-if="recordNum === 2">
          <tr>
            <td>
              <div>装备器具编号</div>
            </td>
            <td>
              <div>原值</div>
            </td>
            <td>
              <div>预期的使用寿命</div>
            </td>
            <td>
              <div>到厂日期</div>
            </td>
            <td>
              <div>到厂时的新旧情况</div>
            </td>
            <td>
              <div>类别</div>
            </td>
            <td>
              <div>备注</div>
            </td>
          </tr>
          <tr>
            <td>{{recordScan.modelCode}}</td>
            <td>{{recordScan.originalValue ? recordScan.originalValue +` 元` : ''}} </td>
            <td>{{recordScan.lifeSpan}} </td>
            <td>{{new Date(recordScan.receiveDate).format('yyyyMM')}} </td>
            <td>{{recordScan.conditions == 1 ? '新' : ''}}{{recordScan.conditions == 2 ? '旧' : ''}} </td>
            <td>{{recordScan.categoryName}} </td>
            <td>{{recordScan.memo}} </td>
          </tr>
        </table>
      </template>
    </TyDialog>
  </div>
</template>

<script lang="js">
import {initNav} from "@/utils/routeChange";
import eqListMixin from "@/mixins/equipManage/eqListMixin";
import addMixin from "@/mixins/equipManage/eqAddRecord";

export default {
  mixins: [eqListMixin,addMixin],
  data() {
    return {
      pageName: 'equipmentList'
    }
  },
  mounted() {
    initNav({mid: 'qq', name: '装备清单', pageName: this.pageName}, this.getCreatData, this)
  },
  methods: {}
}
</script>
<style scoped lang="scss">
@use "@/style/equiptCommon.scss";
#equipmentList {
  padding: 0 100px;
  font-size: 14px;
}
</style>