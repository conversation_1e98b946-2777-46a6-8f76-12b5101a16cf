package cn.sphd.miners.modules.dmmSchedule.service;

import cn.sphd.miners.modules.dmmSchedule.entity.DmmFavorite;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmFavoriteSchedule;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmSchedule;

import java.util.List;

public interface DmmFavoriteService {

    void  saveDmmFavorite(DmmFavorite dmmFavorite);

    DmmFavorite getDmmFavoriteById(Integer id);

    void updateDmmFavorite(DmmFavorite dmmFavorite);

    void deleteDmmFavorite(DmmFavorite dmmFavorite);

    List<DmmFavorite> getFirstFavorites(Long accId);

    List<DmmFavorite> getFavoritesByPid(Integer pid);

    List<Integer> getFavoriteIdsByPath(String path);

    Integer getCountsByFavoriteIds(List<Integer> favoriteIds);

    List<DmmSchedule> getDmmSchedulesByFavoriteId(Long accId, Integer favoriteId);

    DmmFavoriteSchedule getDmmFavoriteScheduleByScheduleId(Long accId, Integer oldFavoriteId, Integer scheduleId);

    void  updateDmmFavoriteSchedule(DmmFavoriteSchedule dmmFavoriteSchedule);

    void  saveDmmFavoriteSchedule(DmmFavoriteSchedule dmmFavoriteSchedule);

    void deleteDmmFavoriteSchedule(DmmFavoriteSchedule dmmFavoriteSchedule);

    void deleteAllDmmFavoriteSchedule(List<DmmFavoriteSchedule> dmmFavoriteScheduleList);

    List<DmmFavoriteSchedule> getDmmFavoriteSchedulesByScheduleId(Integer scheduleId);

}
