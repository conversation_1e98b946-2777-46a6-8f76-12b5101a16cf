<template>
  <div id="potentialApply" class="potential">
    <TY_NavTop title="潜在客户申请"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="panel-content def-gap">
        <div>{{subTitle}}</div>
      </div>
      <div class="panel-content sect-con">
        <el-row>
          <el-col :span="6">姓名</el-col>
          <el-col :span="18"><el-input size="mini" placeholder="请输入内容" v-model="details.name"></el-input></el-col>
        </el-row>
        <el-row>
          <el-col :span="6">职位</el-col>
          <el-col :span="18"><el-input size="mini" placeholder="请输入内容" v-model="details.post"></el-input></el-col>
        </el-row>
        <el-row>
          <el-col :span="6">手机</el-col>
          <el-col :span="18"><el-input size="mini" placeholder="请输入内容" v-model="details.mobile"></el-input></el-col>
        </el-row>
        <div v-for="(it, i) in contactTypeList" :key="i">
          <el-row class="socialList" v-for="(social, index) in details.socialList[it.eName]" :key="index">
            <el-col :span="6" v-if="social.type === '9' || social.type === 9">{{social.name}}</el-col>
            <el-col :span="6" v-else>{{it.name}}</el-col>
            <el-col :span="12">{{social.code}}</el-col>
            <el-col :span="6"><el-button type="danger" size="mini" @click="delContact(it.eName, index)">删除</el-button></el-col>
          </el-row>
        </div>
      </div>
      <div class="panel-content gap">
        <el-collapse v-model="activeNames" accordion>
          <el-collapse-item title="添加更多联系方式" name="1" class="primary">
            <el-row>
              <el-col :span="6">
                <el-select v-model="contactType" placeholder="请选择" size="mini">
                  <el-option value="1" label="手机"></el-option>
                  <el-option value="2" label="QQ"></el-option>
                  <el-option value="3" label="邮箱"></el-option>
                  <el-option value="4" label="微信"></el-option>
                  <el-option value="5" label="微博"></el-option>
                  <el-option value="9" label="自定义"></el-option>
                </el-select>
              </el-col>
              <el-col :span="18" v-if="contactType !== ''" class="definedArea">
                <el-input placeholder="请输入内容" v-model="contactValue" class="input-with-select" size="mini">
                  <el-input class="definedInput" v-if="contactType === '9'" v-model="definedTitle" slot="prepend" placeholder="请录入自定义标签" size="mini"></el-input>
                </el-input>
              </el-col>
            </el-row>
            <el-button type="primary" size="small" @click="addOtherContact" :disabled="contactType === '' && contactValue === ''">添加</el-button>
          </el-collapse-item>
        </el-collapse>
      </div>
      <div class="panel-content">
        <el-row>
          <el-col :span="24">名片照片</el-col>
        </el-row>
        <el-row>
          <el-col :span="20" class="handle-center editUploadFile">
            <el-upload
              class="avatar-uploader"
              :action="webRoot+'/uploads/uploadfyByFile.do'"
              :data = "uploadData"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
            >
              <img v-if="details.visitCard"
                   :src="fileUrl + details.visitCard"
                   class="avatar row-bg" type="flex" justify="space-between"
              >
              <i class="el-icon-plus avatar-uploader-icon" v-else></i>
            </el-upload>
          </el-col>
          <el-col :span="4" v-if="details.visitCard">
            <el-button type="text" @click="handleRemove">删除</el-button>
          </el-col>
        </el-row>
      </div>
      <div class="handle_button handle-center">
        <el-button type="primary" :disabled="editControl" @click="contactUpdateSure" size="medium">提交</el-button>
      </div>
    </div>
  </div>
</template>
<script>
import { gohistory, handleNull } from '../../../js/common'
export default {
  name: 'contactDetailUpdate',
  filters: {
    gohistory,
    handleNull
  },
  data () {
    return {
      loading: true,
      subTitle: '',
      details: {
        name: '',
        post: '',
        mobile: '',
        socialList: {
          mobile: [],
          qq: [],
          email: [],
          weixin: [],
          weibo: [],
          defined: []
        },
        visitCard: ''
      },
      orgDetails: {
        name: '',
        post: '',
        mobile: '',
        socialList: [],
        visitCard: ''
      },
      activeNames: '',
      contactTypeList: [
        {
          type: 1,
          name: '手机',
          eName: 'mobile'
        }, {
          type: 2,
          name: 'QQ',
          eName: 'qq'
        }, {
          type: 3,
          name: '邮箱',
          eName: 'email'
        }, {
          type: 4,
          name: '微信',
          eName: 'weixin'
        }, {
          type: 5,
          name: '微博',
          eName: 'weibo'
        }, {
          type: 9,
          name: '自定义',
          eName: 'defined'
        }
      ],
      contactType: '',
      contactValue: '',
      definedTitle: '',
      fileUrl: window.parent.$.fileUrl,
      webRoot: window.parent.$.webRoot,
      uploadData: {
        module: '潜在客户',
        userId: this.sphdSocket.user.userID
      }
    }
  },
  computed: {
    editControl () {
      let state = 0
      for (let item in this.details) {
        if (item === 'socialList') {
          let len = this.details[item]['mobile'].length + this.details[item]['qq'].length + this.details[item]['email'].length +
            this.details[item]['weixin'].length + this.details[item]['weibo'].length + this.details[item]['defined'].length
          if (len !== this.orgDetails[item].length) {
            state++
          } else {
            for (let a in this.contactTypeList) {
              let arr = this.details[item][this.contactTypeList[a].eName]
              for (let t in arr) {
                let black = this.orgDetails[item].find(value => value.code === arr[t].code)
                if (black === undefined || (black && black.name !== arr[t].name)) {
                  state++
                }
              }
            }
          }
        } else {
          if (this.details[item] !== this.orgDetails[item]) {
            state++
          }
        }
      }
      if ((this.details.name !== '' || this.details.post !== '' || this.details.mobile !== '') && state > 0) {
        return false
      } else {
        return true
      }
    }
  },
  created () {
    let _this = this
    if (_this.$route.params.type === 'new') {
      _this.subTitle = '新增客户联系人'
      _this.loading = false
    } else {
      _this.subTitle = '修改客户联系人'
      this.axios.post('../../../sale/gettPotentialContactsSocial.do', {
        customerId: _this.$route.params.detailId,
        contactId: _this.$route.params.contactId
      }).then(function (response) {
        _this.loading = false
        let details = response.data.data
        let list = details.socialList
        let infoStr = JSON.stringify(details)
        const orgData = JSON.parse(infoStr)
        for (let key in _this.details) {
          if (key === 'visitCard') {
            if (details.visitCard !== null && details.visitCard !== 'null' && details.visitCard !== '') {
              _this.details.visitCard = details.visitCard
            } else {
              orgData.visitCard = ''
            }
          } else if (key === 'socialList') {
            for (let i in list) {
              let type = list[i].type
              let item = _this.contactTypeList.find(item => Number(item.type) === Number(type))
              if (item) {
                _this.details.socialList[item.eName].push(list[i])
              }
            }
          } else {
            _this.details[key] = details[key]
          }
        }
        _this.orgDetails = orgData
      }).catch(function (error) {
        console.log(error)
      })
    }
  },
  methods: {
    addOtherContact: function () {
      let type = this.contactType
      let value = this.contactValue
      let item = this.contactTypeList.find(item => Number(item.type) === Number(type))
      if (type === '9') {
        let definedLable = this.definedTitle
        if (item) {
          this.details.socialList[item.eName].push({
            code: value,
            type: item.type,
            name: definedLable
          })
        }
      } else {
        if (item) {
          this.details.socialList[item.eName].push({
            code: value,
            type: item.type,
            name: item.name
          })
        }
      }
      this.contactType = ''
      this.contactValue = ''
      this.activeNames = ''
    },
    delContact: function (eName, index) {
      this.details.socialList[eName].splice(index, 1)
    },
    handleAvatarSuccess (file) {
      // 删除之前的照片
      let option = {type: 'fileId', fileId: this.details.visitCardUid}
      window.parent.cancelFileDel(option, true)
      // 赋值记录新的照片
      this.details.visitCard = file.filename
      this.details.visitCardUid = file.fileUid
      let fileDelArr = JSON.parse(window.localStorage.getItem('fileDelArr') || '[]')
      fileDelArr.push({'type':'fileId', 'fileId': file.fileUid})
      window.localStorage.setItem('fileDelArr', JSON.stringify(fileDelArr))
    },
    handleRemove () {
      let option = {type: 'fileId', fileId: this.details.visitCardUid}
      window.parent.cancelFileDel(option, true)
      this.details.visitCard = ''
      this.details.visitCardUid = ''
    },
    contactUpdateSure: function () {
      let _this = this
      _this.loading = true
      let social = []
      let socialList = _this.details.socialList
      for (var a in socialList.mobile) {
        social.push(socialList.mobile[a])
      }
      for (var b in socialList.qq) {
        social.push(socialList.qq[b])
      }
      for (var c in socialList.email) {
        social.push(socialList.email[c])
      }
      for (var d in socialList.weixin) {
        social.push(socialList.weixin[d])
      }
      for (var e in socialList.weibo) {
        social.push(socialList.weibo[e])
      }
      for (var f in socialList.defined) {
        social.push(socialList.defined[f])
      }
      social = JSON.stringify(social)
      let json = {
        name: _this.details.name,
        post: _this.details.post,
        mobile: _this.details.mobile,
        socialList: social,
        visitCard: _this.details.visitCard
      }
      let url = ''
      if (_this.$route.params.type === 'new') {
        url = '../../../sales/addCustomerContact.do'
        json.customer = _this.$route.params.detailId
      } else {
        json.contactId = _this.$route.params.contactId
        url = '../../../sales/updateContactsSocialAndCard.do'
      }
      _this.axios.post(url, json).then(function () {
        _this.loading = false
        let option = {type: 'fileId', fileId: _this.details.visitCardUid}
        window.parent.cancelFileDel(option)
        gohistory(_this)
      }).catch(function (error) {
        console.log(error)
      })
    }
  },
  destroyed: function () {
    window.parent.cancelFileDel({type: 'fileId', fileId: this.details.visitCardUid}, true)
  }
}
</script>
