<template>
  <div id='player' style='width:100%;height:100%'>
    <video id='video' :class="{'video_show_cue':isShowCue}" controls controlslist='nodownload' preload style='width:100%;height:100%;' crossorigin='anonymous' disablePictureInPicture playsinline webkit-playsinline x5-video-player-type='h5' x5-video-orientation='landscape|portrait' ></video>
    <div id='display' @click='playVideo'></div>
  </div>
</template>

<style lang="scss">
body{
  display: flex;
  justify-content: center;
}
#app{
  display: flex;
  justify-content: center;
}
</style>
<style id='videoCue' scoped>
#display{
  position:absolute;
  display:block;
  writing-mode: vertical-rl;
  text-align :center;
  color:yellow;
  font-size:24px;
  font-family:Comic Sans MS;
  text-shadow: 0.1em 0.1em 0.15em #333;
  z-index:100; /* set z-index to be sure div is on top */
}
video::cue{
  background-color:rgba(0,0,0,0) !important;
  color:transparent;
  font-size:24px;
  line-height:100px;
}
video::-webkit-media-text-track-display-backdrop {
  background-color:rgba(0,0,0,0) !important;
}
video::-webkit-media-text-track-display{
  background-color:rgba(0,0,0,0) !important;
}
video::-webkit-media-text-track-container {
  background-color:rgba(0,0,0,0) !important;
}
video.video_show_cue::cue {
  color:yellow;
}
/*video::-webkit-media-controls-fullscreen-button {*/
/*  display:none !important;*/
/*}*/
</style>

<script>
import Hls from 'hls.js'
import auth from "@/sys/auth"
import { getDeviceType, getHtmlFontSize, fullScreenElement } from "@/utils/browerUtils";
export default {
  data() {
    return {
      info: {},
      isLoad: true,
      contentList: {},
      isShowCue: false
    };
  },
  mounted() {
    getDeviceType()
    this.showVod()
  },
  methods: {
    showVod() {
      let that = this
      let video = document.getElementById('video')
      let player = document.getElementById('player')
      let disp = document.getElementById('display')
      this.resize(disp, player, video)
      let videoInfoEvent = 'loadedmetadata'
      let kind
      if (getDeviceType() == 'IOS') {
        kind = 'metadata'
        video.controlsList = 'nofullscreen nodownload noremote footbar'
      } else {
        kind = 'subtitles'
        video.controlsList = 'nodownload noremote footbar'
      }
      console.log('Hello world1', Hls.isSupported())
      let url = 'https://vod.btransmission.com/vod/test1.mp4/index.m3u8'
      let trackUrl = auth.webRoot + '/lyric/testVtt.do'
      console.log('url', url)
      console.log('trackUrl', trackUrl)
      if (Hls.isSupported()) {
        let track = document.createElement('track')
        track.src = trackUrl
        track.srclang = 'zh-cn'
        track.label = '简体中文'
        track.kind = kind
        track.default = 'default'
        let hls = new Hls()
        hls.loadSource(url)
        hls.attachMedia(video)
        video.appendChild(track)
        console.log('Hello world2', track.src)
        hls.on(Hls.Events.MANIFEST_PARSED, function () {
          console.log('Hello world3')
          that.play(video, track, videoInfoEvent)
          console.log('Hello world4')
        })
        console.log('Hello world5')
      } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
        console.log('Hello world6')
        let track = document.createElement('track')
        track.src = trackUrl
        track.srclang = 'zh-cn'
        track.label = '简体中文'
        track.kind = kind
        track.default = 'default'
        video.appendChild(track)
        video.src = url
        videoInfoEvent = 'canplay'
        video.addEventListener('canplay', function () {
          console.log('Hello world7')
          that.play(video, track, videoInfoEvent)
        })
        console.log('Hello world8')
      }
    },
    playVideo(){
      let video = document.getElementById('video')
      video.play()
    },
    resize(disp, player, video) {
      let parent = player.parentElement
      // player.style.width = video.videoWidth + 'px'  // make enclosure div width == video width
      // disp.style.top = (video.style.top + (Math.min(video.videoHeight, parent.clientHeight) / 5)) + 'px' // set the text to appear at 5% from the top of the video
      disp.style.top = 0  // set the text to appear at 5% from the top of the video
      disp.style.height = video.videoHeight>0 ? Math.min(video.videoWidth, parent.clientHeight)*2/3 + 'px' : parent.clientHeight*2/3 + 'px'
      console.log('video.videoHeight>0 ? Math.min(video.videoWidth, parent.clientHeight)*2/3 + \'px\' : parent.clientHeight*2/3 + \'px\'', video.videoWidth, parent.clientHeight, disp.style.height)
      let spanSize = getHtmlFontSize(video.parentNode)
      disp.style.left = (parent.scrollLeft + spanSize) / 2 + 'px';  // set the text to appear relative to the left edge of the video
      disp.style.width = (parent.clientWidth - spanSize) + 'px'; // set text box to the width of the video
      console.log('parent.scrollLeft + parent.clientWidth - video.videoWidth + spanSize', typeof parent.scrollLeft, typeof parent.clientWidth, typeof video.videoWidth, typeof spanSize, typeof disp.style.left)
      console.log('parent.scrollLeft + parent.clientWidth - video.videoWidth + spanSize', parent.scrollLeft, parent.clientWidth, video.videoWidth, spanSize, disp.style.left)
      console.log('parent.clientWidth parent.clientHeight', parent.clientWidth, parent.clientHeight)
      console.log('video.videoHeight video.videoWidth', video.videoHeight, video.videoWidth)
      console.log('disp.style.left disp.style.width', disp.style.left, disp.style.width)
    },
    play(video, track, videoInfoEvent) {
      let player = document.getElementById('player')
      let disp = document.getElementById('display')
      this.resize(disp, player, video)
      console.log('Hello world9')
      video.play()
      console.log('Hello world10', typeof track !== 'undefined')
      if(typeof track !== 'undefined') {
        let that = this
        console.log('Hello world11', document, document.addEventListener)
        // document.addEventListener('DOMContentLoaded', function () {  // don't run this until all DOM content is loaded
          console.log('Hello world11.1')
          //  get objects associated with the video, track, and div elements
          console.log('Hello world12', videoInfoEvent)

          video.addEventListener(videoInfoEvent, function () {//'loadedmetadata'
            that.resize(disp, player, video)
          })

          console.log('Hello world13', 'cuechange')
          let precuechang = true
          track.addEventListener('cuechange', function () {
            let myTrack = this.track             // track element is 'this'
            let myCues = myTrack.activeCues      // activeCues is an array of current cues.
            if (myCues.length > 0) {
              if (fullScreenElement() == undefined && video.videoWidth > 0 && precuechang) {
                that.isShowCue = false
                precuechang = false
                that.resize(disp, player, video)
              }
              disp.innerText = myCues[0].text   // write the text
              console.log(disp.innerText)
            }
          })
          console.log('Hello world15', 'resize')
          window.addEventListener('resize', function () {
            console.log('Hello world16', fullScreenElement() == undefined && video.videoWidth > 0)
            if (fullScreenElement() == undefined && video.videoWidth > 0) {
              that.isShowCue = false
              that.resize(disp, player, video)
            }
            console.log('Hello world16.1', document.fullscreenElement !== null)
            if(document.fullscreenElement !== null) {
              that.isShowCue = true
            }
          });
          console.log('Hello world17', 'webkitbeginfullscreen')
          video.addEventListener('webkitbeginfullscreen', function () {
            console.log('webkitbeginfullscreen')
            console.log('Hello world18', that.isShowCue)
            that.isShowCue = true
            // document.getElementById('videoCue').innerHTML = document.getElementById('videoCue').innerHTML.replace(/(^[\s\S]*\scolor:)[^;]+(;[\s\S]*$)/g, '$1yellow$2')
            video.play()
          })
          console.log('Hello world19', 'webkitendfullscreen')
          video.addEventListener('webkitendfullscreen', function () {
            console.log('webkitendfullscreen')
            console.log('Hello world20', that.isShowCue)
            that.isShowCue = false
            // document.getElementById('videoCue').innerHTML = document.getElementById('videoCue').innerHTML.replace(/(^[\s\S]*\scolor:)[^;]+(;[\s\S]*$)/g, '$1transparent$2')
            video.play()
          })
          let arr = ["fullscreenchange"]
          console.log('Hello world21', arr)
          arr.forEach((item, index) => {
            console.log('Hello world21', 'fullscreenchange')
            console.log('full screen add event : ', item)
            document.addEventListener(item, function () {
              console.log('full screen change event : ', item)
              console.log('Hello world22', document.fullscreenElement || document.webkitFullscreenElement !== null)
              if (document.fullscreenElement || document.webkitFullscreenElement !== null) {
                console.log('Hello world23', that.isShowCue)
                that.isShowCue = true
                // document.getElementById('videoCue').innerHTML = document.getElementById('videoCue').innerHTML.replace(/(^[\s\S]*\scolor:)[^;]+(;[\s\S]*$)/g, '$1yellow$2')
              } else {
                console.log('Hello world24', that.isShowCue)
                that.isShowCue = false
                // document.getElementById('videoCue').innerHTML = document.getElementById('videoCue').innerHTML.replace(/(^[\s\S]*\scolor:)[^;]+(;[\s\S]*$)/g, '$1transparent$2')
              }
            })
          })
          console.log('Hello world11.9')
        // })
        console.log('Hello world99')
      }
    }
  }
}
</script>
