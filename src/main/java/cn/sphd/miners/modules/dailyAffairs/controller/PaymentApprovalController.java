package cn.sphd.miners.modules.dailyAffairs.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.dailyAffairs.service.PaymentApprovalService;
import cn.sphd.miners.modules.personal.entity.PersonnelReimburse;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.personal.service.PersonnelReimburseService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2019/12/27.
 * 1.87 付款审批
 * 1.103付款复核 2020/6/17改来的（2020/6/9）
 */
@Controller
@RequestMapping("/paymentApproval")
public class PaymentApprovalController {

    @Autowired
    PaymentApprovalService paymentApprovalService;
    @Autowired
    PersonnelReimburseService personnelReimburseService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    ApprovalProcessService approvalProcessService;

    /**
     *<AUTHOR>
     *@date 2019/12/27 10:12
     *审批人付款审批列表
    */
    @ResponseBody
    @RequestMapping("/getPayApply.do")
    @MessageMapping("/getPayApply")
    public JsonResult getPayApply(String sessionid, User user){
        Map<String,Object> map = new HashMap<String,Object>();
        List<PersonnelReimburse> paymentHandle = paymentApprovalService.getPaymentHandle(user.getUserID(),null,"1","1",17,null,null);  //付款审批待处理
        List<PersonnelReimburse> paymentApproval = paymentApprovalService.getPaymentHandle(user.getUserID(),null,"1","2",17,null,null);  //付款审批已批准
        map.put("paymentHandle",paymentHandle);
        map.put("paymentApproval",paymentApproval);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/paymentHandle",null,null,null,null,paymentHandle);  //返回结果的订阅
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/paymentApproval",null,null,null,null,paymentApproval);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2019/12/27 16:32
     *付款审批的审批 （没有驳回）
    */
    @ResponseBody
    @RequestMapping("/paymentHandleApproval.do")
    @MessageMapping("/paymentHandleApproval")
    private JsonResult paymentHandleApproval(String json,String sessionid,User user,Integer approvalProcessId){
        JSONObject jsonObject = JSON.parseObject(json);
        if (approvalProcessId==null) {
            approvalProcessId = jsonObject.getInteger("approvalProcessId");  //审批过程id
        }
        Map<String,Object> map = new HashMap<>();
        if (approvalProcessId!=null && user!=null){
            map = paymentApprovalService.paymentHandleApproval(user.getUserID(),approvalProcessId);
        }else {
            map.put("status",0);  //失败
            map.put("content","操作失败");  //失败
        }
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/paymentHandleApproval",null,null,null,null,map);  //返回结果的订阅
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2019/12/28 10:06
     *个人报销付款审批的查询
     * type 1-近七日 2-本月 3-自定义
     * applyUserId-申请人id  userId-登录人id  beginDate-开始时间  endDate-结束时间
    */
    @ResponseBody
    @RequestMapping("/getPayment.do")
    public JsonResult getPayment(Integer type, Integer applyUserId, User user, String beginDate,String endDate) throws ParseException {
        Map<String,Object> map = new HashMap<>();
        Date beginTime = new Date();
        Date endTime = new Date();
        if (1==type){  //1-近七日
            beginTime = NewDateUtils.changeDay(new Date(),-6);
            endTime = new Date();
        }else if (2==type){  //2-本月
            beginTime = NewDateUtils.changeMonth(new Date(),0);
            endTime = new Date();
        }else if (3==type){
            beginTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(beginDate);
            endTime = NewDateUtils.getLastTimeOfDay(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(endDate));
        }
        List<PersonnelReimburse> payments = paymentApprovalService.getPaymentHandle(user.getUserID(),applyUserId,"2","2",17,beginTime,endTime);
        map.put("payments",payments);
        map.put("beginTime",beginTime);
        map.put("endTime",endTime);
        return new JsonResult(1,map);
    }


    //----------------------以下为1.103付款复核----------------
    /**
     * 可付款的审批
     * @return
     */
    @ResponseBody
    @RequestMapping("/payableApproval.do")
    @MessageMapping("/payableApproval")
    public JsonResult payableApproval(String json, String sessionid,User user,Integer approvalProcessId,String method,String planDate,String factDate,Integer accountId,Integer reimburseId,String summary){
        Map<String,Object> map=new HashMap<>();
        JSONObject jsonObject = JSONObject.parseObject(json);
        Date planDate1 = null;
        Date factDate1 = null;
        if (StringUtils.isNotEmpty(planDate)){
            planDate1 = NewDateUtils.dateFromString(planDate,"yyyy-MM-dd");
        }
        if (StringUtils.isNotEmpty(factDate)){
            factDate1 = NewDateUtils.dateFromString(factDate,"yyyy-MM-dd");
        }
        if (approvalProcessId==null) {
            approvalProcessId = jsonObject.getInteger("approvalProcessId");  //审批过程id
            method = jsonObject.getString("method");  //支付方式:1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
            planDate1 = jsonObject.getDate("planDate");   //计划时间
            factDate1 = jsonObject.getDate("factDate");   //实际时间
            accountId = jsonObject.getInteger("accountId");   //账户id
            reimburseId = jsonObject.getInteger("reimburseId");   //报销id
            summary = jsonObject.getString("summary");   //摘要
        }

        map = paymentApprovalService.payableApproval(user.getUserID(),approvalProcessId,method,planDate1,factDate1,accountId,reimburseId,summary,map);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/paymentReviewMessage",null,null,null,null, map);  //订阅的通道
        return new JsonResult(1,map);
    }

    /**
     * 审批人付款复核-待复核/待付款列表
     * @return
     */
    @ResponseBody
    @RequestMapping("/paymentReviewApprover.do")
    @MessageMapping("/paymentReviewApprover")
    public JsonResult paymentReviewApprover(String sessionid,User user){
        Map<String,Object> map = new HashMap<String,Object>();
        List<PersonnelReimburse> paymentReviewHandle = paymentApprovalService.paymentReviewApprover(user.getUserID(),null,"1","1",null,null,20,22);  //付款复核待处理
//        List<PersonnelReimburse> paymentReviewApproval = paymentApprovalService.paymentReviewApprover(user.getUserID(),null,"1","2",null,null,20,22);  //付款复核已批准
        List<PersonnelReimburse> paymentReviewApproval = personnelReimburseService.cashier(null,user.getOid(),"4");  //付款复核已批准(与财务待付款一样)
        map.put("paymentReviewHandle",paymentReviewHandle);
        map.put("paymentReviewApproval",paymentReviewApproval);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/paymentReviewHandle",null,null,null,null,paymentReviewHandle);  //返回结果的订阅
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/paymentReviewApproval",null,null,null,null,paymentReviewApproval);
        return new JsonResult(1,map);
    }


    /**
    * <AUTHOR>
    * @Description  待复核审批（没有驳回）
    * @Date 2020/6/17
    */
    @ResponseBody
    @RequestMapping("/reviewApproval.do")
    @MessageMapping("/reviewApproval")
    public JsonResult reviewApproval(String json,String sessionid,User user,Integer approvalProcessId){
        Map<String,Object> map = new HashMap<String,Object>();
        JSONObject jsonObject = JSON.parseObject(json);
        if (approvalProcessId==null) {
            approvalProcessId = jsonObject.getInteger("approvalProcessId");  //审批过程id
        }
        map = paymentApprovalService.reviewApproval(user.getUserID(),approvalProcessId);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/paymentReviewMessage",null,null,null,null, map);  //订阅的通道
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Description 查看原定的付款方式
    * @Date 2020/6/17
    */
    @ResponseBody
    @RequestMapping("/getPaymentMethod.do")
    @MessageMapping("/getPaymentMethod")
    public JsonResult getPaymentMethod(Integer reimburseId){
        Map<String,Object> map = new HashMap<String,Object>();
        if (reimburseId!=null){
            map = paymentApprovalService.getPaymentMethod(reimburseId);
        }
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Description  修改付款方式
    * @Date 2020/6/18
    */
    @ResponseBody
    @RequestMapping("/updatePaymentMethod.do")
    @MessageMapping("/updatePaymentMethod")
    public JsonResult updatePaymentMethod(String json,String sessionid,User user,Integer approvalProcessId,String method,String planDate,String factDate,Integer accountId,Integer type,String summary){
        Map<String,Object> map = new HashMap<String,Object>();
        JSONObject jsonObject = JSONObject.parseObject(json);
        Date planDate1 = null;
        Date factDate1 = null;
        if (StringUtils.isNotEmpty(planDate)){
            planDate1 = NewDateUtils.dateFromString(planDate,"yyyy-MM-dd");
        }
        if (StringUtils.isNotEmpty(factDate)){
            factDate1 = NewDateUtils.dateFromString(factDate,"yyyy-MM-dd");
        }
        if (approvalProcessId==null) {
            approvalProcessId = jsonObject.getInteger("approvalProcessId");  //审批过程id
            method = jsonObject.getString("method");  //支付方式:1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
            planDate1 = jsonObject.getDate("planDate");   //计划时间
            factDate1 = jsonObject.getDate("factDate");   //实际时间
            accountId = jsonObject.getInteger("accountId");   //账户id
            type = jsonObject.getInteger("type");   //1-待复核的修改  2-待付款的修改
            summary = jsonObject.getString("summary");  //摘要
        }

        map = paymentApprovalService.updatePaymentMethod(user.getUserID(),approvalProcessId,method,planDate1,factDate1,accountId,type,summary);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/paymentReviewMessage",null,null,null,null, map);  //订阅的通道
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Description  付款复核审批的查询
    * @Date 2020/6/18
    * type 1-近七日 2-本月 3-自定义
    * applyUserId-申请人id  userId-登录人id  beginDate-开始时间  endDate-结束时间
    */
    @ResponseBody
    @RequestMapping("/getPaymentReview.do")
    public JsonResult getPaymentReview(Integer type, Integer applyUserId, User user, String beginDate,String endDate) throws ParseException {
        Map<String,Object> map = new HashMap<>();
        Date beginTime = new Date();
        Date endTime = new Date();
        if (1==type){  //1-近七日
            beginTime = NewDateUtils.changeDay(new Date(),-6);
            endTime = new Date();
        }else if (2==type){  //2-本月
            beginTime = NewDateUtils.changeMonth(new Date(),0);
            endTime = new Date();
        }else if (3==type){
            beginTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(beginDate);
            endTime = NewDateUtils.getLastTimeOfDay(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(endDate));
        }
        List<PersonnelReimburse> paymentReviews = paymentApprovalService.paymentReviewApprover(user.getUserID(),applyUserId,"2","2",beginTime,endTime,20);
        map.put("paymentReviews",paymentReviews);
        map.put("beginTime",beginTime);
        map.put("endTime",endTime);
        return new JsonResult(1,map);
    }

    @ResponseBody
    @RequestMapping("/test.do")
    public Integer test(){
//        this.getPersonnelReimburseNum(user.getUserID(), "1", user.getOid(), null);  //报销审批人

        Integer num = approvalProcessService.getApprovalProcessCountsMid(3069,"at",25,27);
        return num;
    }
}
