package cn.sphd.miners.modules.accountant.service;


import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.accountant.entity.*;
import cn.sphd.miners.modules.car.util.Result;
import cn.sphd.miners.modules.system.entity.User;

import java.math.BigDecimal;
import java.util.List;

public interface Accountant1_209_Service {
    List<TaccountantSubjectJournal> getSubjectJournal(User user, Integer establish, PageInfo pageInfo);

    List<TaccountantSubjectRecord> getSubjectRecords(User user, TaccountantSubjectRecord subjectRecord);

    TaccountantSubjectRecordDto getSubjectModifyInfo(TaccountantSubjectRecordDto subjectRecordDto);

    List<TaccountantSubjectChange> getChangeSubjects(TaccountantSubjectChange subjectChange);

    VoucherHomeData getVoucherHomeData(User user);

    List<TAccountantVoucherDto> getVoucherByMonth(User user, String month,String type);

    SettleHomeData getSettleHomeData(User user);

    JsonResult test(User user);

    List<TAccountantSettle> getSettleListByYear(User user, Integer year);

    List<TAccountantVoucherDto> searchVoucher(User user, String settleMonth,String type,String con);

    public String getSettleMonth(Integer org);
}
