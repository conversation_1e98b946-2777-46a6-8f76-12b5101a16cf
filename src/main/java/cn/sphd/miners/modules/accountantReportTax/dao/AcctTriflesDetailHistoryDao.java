package cn.sphd.miners.modules.accountantReportTax.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTriflesDetailEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTriflesDetailHistoryEntity;

import java.io.Serializable;
import java.util.List;

public interface AcctTriflesDetailHistoryDao extends IBaseDao<AcctTriflesDetailHistoryEntity,Serializable> {
    List<AcctTriflesDetailHistoryEntity> getTriflesEditRecord(AcctTriflesDetailHistoryEntity param);
}
