*{ font-family:Microsoft Yahei ;   }
td,th{ 
	text-align:center!important; 
 }

a {
	color: #34495e;
}
.clr{ clear:both;  }
.hd, .val, .select { display: none; }
.contn input,.contn select ,.bounce input , .bounce select { background:#fff; }
.contn input[disabled], .contn select[disabled] ,.bounce input[disabled], .bounce select[disabled]   { background:#eee;  }
.bounce .btn { border:1px solid #ccc; }
td>input[type="text"].hd{  display: none;  }
.edit , .delet{ width:16px; height:16px; display:inline-block; border:1px solid #fff;   }
.edit{ color:#48cfad;  }
.delet{ color:#ed5565 ;   }
.edit:hover{ border:1px solid #48cfad; }
.delet:hover{ border:1px solid #ed5565; }
.pic{
	padding: 9px 6px 6px 30px; 
	background: #fff url(img/goback.png) no-repeat center center;
	display: inline-block;
	background-size: 30px;
}
div.left_container>div>form, div.left_container>div>form>ul, div.left_container>div>form>ul>li{ margin: 0;  }

/*右面头部*/
div.contn{ margin:-10px 0 0 -20px; }

.doubleCon{ width: 370px;  }
.threeCon{  }
.leMar{ padding-left: 50px; }
.doubleCon>p>span , .threeCon>p>span{ width:150px; text-align:right; display:inline-block;padding-right:20px;    }
.doubleCon>p>input , .threeCon>p>input{ border-radius:4px; border-radius: 4px;
	border: 1px solid #ccc;
	height: 30px;
	padding: 5px;    }
.doubleCon>p>input[readonly], .threeCon>p>input[readonly] ,select[disabled]{ border-radius:4px; background: #eaeaea;   }
.threeCon span.radioCon{ width:auto; text-align: left; padding-left:15px;     }
 .hang4 , .hang8 , .hang9 , .hang11 , .hang_5, .hang_7{ display: none;  }
#addSupply i.ccc{ color:#ccc; }
#addSupply .threeCon>p { padding:5px 20px 5px 0;  }
#addSupply .threeCon>p:hover { background:#eaeaea;   }
#addSupply .threeCon>p.leMar { padding-left:50px;  }
#addPurchase .fa, #addSupply .fa{ color:#48cfad ;    }
.litT{ display: inline-block; width: 100px; text-align: right;  }

div.Con>p{ 
	font-weight: 400;
	font-size: 24px;
	padding: 0 15px;
	margin-top: 25px;
}
.page-content{  min-height:1000px;  }

/*左面*/
.Btop{ 	padding: 10px 0 10px 15px;background: #bdd7ee;}
.between{ position: relative; float:left; border:1px solid #e3e3e3; border-top-color:#f0f8ff; border-bottom:3px solid #fff;
	height:100%; width:10px; top:-2px;     background-color: #f0f8ff;    }
.between2{   border-left:1px solid #e3e3e3; border-right:1px solid #e3e3e3;width: 109%;  position:absolute; bottom:-6px;
	height:10px; left: -1px;     background-color: #f0f8ff;    }
.Btop>span{ font-weight: bold; }
.faceul{ list-style-type: none; }
.faceul1:hover{ background:#eaeaea;  }
.faceul1{ height:35px; line-height:35px;      }
.faceul1>a{  color: #0069b4; text-decoration: none;	display: block; padding:0 0 0 26px; }
.bottomTree .faceul1>a{ font-size: 12px; }
.back-btn,.back-btn:hover{ font-weight: 700; font-size: 14px; color: black; }
.back-b{ padding:50px 0 20px 30px; }
.add-b>a{ 	color: black; }
.add-b>a:hover{ text-decoration: none; }
.add-b{ padding:10px 15px; margin-bottom: 15px; }

div.add-b:hover{ background-color: #e3e3e3!important; }
.faceul1>a>span{ display:inline-block; vertical-align: middle;  }
.faceul1>a>span:nth-child(1){ width:100px; overflow:hidden; height: 35px;text-overflow:ellipsis; white-space: nowrap;  }

.faceul1>a>span:nth-child(2){ width:10%; overflow:hidden; text-align:center;   }
.faceul1>a>span:nth-child(3){ width:25%; min-width:50px; text-align:right;}

.mar{ display:inline-block; margin-right:10px;  }


/*右面*/
.bigContainer{ position:relative;margin-left:15px; border:1px solid #e3e3e3;  }
.left_container{ float: left;width:235px;   }
.right_container{ padding-left: 255px;   }
.Right-label, .Left-label {
	padding: 0;
}
 .Left-label{
	 min-height:86px;
   }
/* .left-bottom{
	 position: absolute;
	 bottom: 0;
 }*/
 .Right-label{
 	padding: 10px 15px 179px 15px;
 	min-width: 700px;
 }

.conon{	
	padding: 10px 0;
	display: inline-block;
	float: left;
	min-width: 300px;
}
.conon>a,.concon>a:hover{
	color: #909090;
	font-family: "方正兰亭超细黑简体";
	font-weight: 700;
}
.conon>p,.conon>p.a:hover{
	display: inline-block;
	padding: 0 5px;
	font-weight: 700;
}
.conon>a:nth-child(2),.conon>a:nth-child(2):hover{
	color:black;
	font-weight: lighter;
	text-decoration: none;
}
.dq>a{
	color: black;
}
.bj>a,.bj>a:hover{
	color: black;
	text-decoration: none;

}


/*供应商名录*/
.TCh{
	padding: 0 10px;
}
.Border{
	border: 1px solid #e3e3e3;
	padding: 10px 20px;

}
.Con-bt>p{
	padding: 30px 10px;
	font-weight: 400;
	font-size: 24px;
}
.T1{
	margin-top: 50px;
}
.see>a,.see>a:hover{
	color: black;
	text-decoration: none;
}
/*供应商名录查看*/
.Back{
	padding: 20px 0;
}
.Img>img{
	width: 30px;
}
.Back>a,.Back>a:hover{
	text-decoration: none;
	color: #60a4e8;
	font-weight: 700;
	font-size: 16px;
}
.jiacu{
	font-weight: bold;
	vertical-align: middle;
}
.aboutbtn{ display:inline-block; padding:5px 20px; cursor:pointer; font-size:14px;   }
.aboutbtn1{ display:inline-block; padding:5px 16px; cursor:pointer; font-size:12px;   }
.aboutbtn2{ display:inline-block; padding:7px 25px; cursor:pointer; font-size:12px;   }
.aboutbtn3{ display:inline-block; padding:4px 13px; cursor:pointer; font-size:12px;   }
.aboutbtn:after{ display:block; content: " ";  }
.bigBtn{ padding:7px 30px;  font-weight:bolder; font-size:16px;  }
.azury,.azury:hover,.azury:active{ 
	color:#fff;  
	text-decoration: none;
	font-size: 16px;
}
.azury1,.azury1:hover,.azury1:active{ 
	color:#fff;  
	text-decoration: none;
	font-size: 12px;
}
.azury2,.azury2:hover,.azury2:active{ 
	color:black;  
	text-decoration: none;
	font-size: 12px;
}
.azury3,.azury3:hover,.azury3:active{ 
	color:#333;  
	text-decoration: none;
	font-size: 16px;
}
.azury{ 
	background:#32C5D2;

}
.azury1{ 
	background:#32C5D2;

}
.azury2{ 
	background:#e0e4eb;

}
.azury3{ 
	background:#e0e4eb;
	margin-left: 10px;

}
.Btn{
	text-align:left!important;
	padding: 10px 60px !important;
}
td.tz{
	text-align:left!important;
	padding: 10px 15px;
}/*
.container_nav{
	text-align: right;
}*/
.conon1{

	display: inline-block;

}
.resourceAddBtn{ position: relative; top:8px;   }
.conon>span{
	padding: 0 20px;
}

.shu1>p{
	text-align: left;
	margin-left: 62px;
	color: #999999;
	font-size: 16px;
	font-weight: 400;
}
/*新增物料*/
.Con-bt1{ display: none;padding-left:20px;   }

.Con-bt1>p{
	/*padding: 0 15px ;*/
	font-weight: 400;
	font-size: 24px;
}
.TCh1{
	border:1px solid #e3e3e3;
	padding: 0 10px 40px 10px;
}
.TCh1-container{
	border-bottom: 1px solid #e3e3e3;
	padding: 20px 10px;
	font-size: 16px;
	color: #4276a4;
	font-weight: bold;
}
.TCh1-content{
	padding: 30px 0 0 0;	
	margin-left: 15%;
}
.TCh1-content_sele{ 
	padding: 30px 0 0 0;	
	margin-left: 15%;
 }
.biaoq{
	display: inline-block;
	width: 100px;
	text-align: right;
	font-size: 15px;
	font-weight: 400;
	vertical-align: top;
}
.TCh1-content_sele input{
	height: 25px;
	width: 250px;
	margin-left: 30px;
}
.TCh1-content1 input.hd{ display: none!important; }
.TCh1-content1 input {
	height: 35px;
	width: 250px;
	border:1px solid #ccc;
	margin-left: 30px;
}
.TCh1-content_sele select{
	height: 36px;
	width: 265px;
	border:1px solid #ccc;
}
.T{
	padding: 5PX 15px;

}
.T>div{
	padding: 10px 0;
}
.T>div>a{
	color: #4276a4;
	font-size: 14px;
	font-weight: 400;
}
.bg{
	background-color: #f1f1f1;
}
.bg1{
	background-color: #e3e3e3;
}
.tz{
	text-align: left;
	color: #666666;
}


.TCh2{
	padding: 5px 0 22px 0;
	border:1px solid #e3e3e3;
	border-top: 1px solid white;
}
/*编辑*/
.TCh1-contentt{
/*	padding: 30px 590px 0 0;*/
	text-align: right;
}
.TCh1-content2>span{
	font-size: 15px;
	font-weight: 400;
	margin-right: 30px;

}
.TCh1-content2>input{
	height: 25px;
	width: 40%;

}
.TCh1-content2>select{
	height: 36px;
	width: 20%;
}

td>input[type="text"].hd{  margin:0; height:30px; border:1px solid #ccc; padding:0;  }
.opinionCon>table tr>td{ padding:3px; height:40px; vertical-align:middle;}

.noup{
	color: red;
}
.biaoInfo{ 
	display: inline-block; 
	min-width: 300px;  
}
.biaoInfo1{
	min-width: 300px;
	margin-left:80px;
}
.biaoInfo1>select{ display:block; margin:10px 0 10px 50px; }
.TCh1-content1{
	width: 400px; 
	padding:10px 0;
}
.biaoInfo1>div{ padding-left:50px; }
.mt_ttl{ float:left; display: block; font-size: 16px; text-align:right; width:95px;     }
#mtKindContainer select{ display:block;margin-top:10px; }
.controlBtn{ 
	text-align: center;
	min-width: 700px;
	width:60%;
}

.TCh1-content_select{ width:60%; min-width: 400px;  }
.span_lab{
	text-align: left;
}

.supply_input_container{ margin-left:100px; width:200px;  }
.select>div{ line-height:30px; cursor: pointer;  }
.select>div:hover{ background: #eee; color:#333;  }
.supply_input_container>input ,.supply_input_container>div{ display:block; width:100%; border:1px solid #ccc;  }
.supply_input_container>input{  height:30px; line-height:30px;   }
.supply_input_container>div{  background: #fff;  position: absolute; width:200px; z-index: 9999;    }
.addSup_item>div:nth-child(1){ float:left ; width:50%;    }
.addSup_item>div:nth-child(2){  margin-left:50%;  }
.addSup_item>div{ line-height:35px; padding:5px 0 ;    }
.addSup_item>div>span{ display:inline-block; width:95px; text-align:right;   }
.addSup_item>div input[type="text"]{ border:1px solid #ccc; height:35px; width:200px; padding:0 3px;   }
.addsupTip{ padding-left: 100px;  }



.editSup_0,.editSup_1{float: left; margin-left: 20px;  }
.editsupcodn{ position:relative; top:-21px; }

input:not[type="radio"],select {border:1px solid #ccc; height:35px; line-height:35px; width:200px;  padding-left:10px;   }
.editSup_1{ display:none;  }
.cantEdit{ color:#c8c8c8; padding-top:8px;  }

#curID>span>span:hover{ color:#aaa; text-decoration:underline; cursor:pointer;  }
.readonly{ cursor:not-allowed;  }
#right_container{padding-bottom:5px;}
tr>td:last-child>span{ cursor:pointer;   }
#goodslMaterial , #editGoodsMaterialMess { display:none;  }

.bigContainer{ border-bottom:1px solid #ccc;   }

/* updator：张旭博，2017-04-10 10:47:48，弹窗表格样式 */

.ty-table td input{
	width:100%
}
.e_supContainer{  position: relative; margin-right:46px;  }

.supCon {
	display: none;
	position: absolute;
	width: 200px;
	max-height: 150px;
	overflow-y: auto;
	left: 0;
	border: 1px solid #98e0ce;
}
.supItem{ padding:5px 5px 5px 15px; background: #fff; cursor:default; width: 100%; text-align: left;  }
.supItem:hover{  background: #ececec;    }
.supCon .active{ background:#ececec;    }
.xing{ color:red;   }
#tb2{ display: none; }
#addSupply, #gScanInfo, #addPurchase{ width: 1130px;  }
#oralStorage { width:830px;  }
.hisCon{ height:300px; overflow-y: auto;  }
.hisHeadc{ overflow: hidden;  }
.hisHead{ margin-left: -15px; border-bottom:1px solid #ccc;   }

#addSupply input[disabled] {
	background: #eaeaea;
}
#addSupply i.fa[disabled]{ color:#aaa;  }
#addSupply .bonceCon{ max-height: 400px; overflow-y: scroll;   }
.link{ color: #0b94ea; text-decoration: underline; cursor: pointer;  }
.link:hover{ background:#eaeaea; color: #0b94ea!important; text-decoration: underline;    }
#storageHis{ width:918px; }
.purchase-link{ color:#0b94ea; cursor: default;   }
.purchase-link:hover{ color: #0b9df9;   }

/*1.88 */
.supInfoTip{ border: 1px solid #ccc; margin: 5px 50px; padding: 5px; border-radius: 4px; font-size:13px;  }
.tipBlue{ color:#0b9df9; width: 240px!important;  }
.stable,.supInfo,.canInvoice2,.incoiceType2{ display: none; }
.supInfo>.purUnit{width:10px; }
.supGreen, .supOrange, .supBlue{ width: auto!important; padding-right:0!important;   }
.supGreen{ color:#5d9285;  }
.supOrange{ color:#f58410;}
.supBlue{ color:#5d9cec; }
#addPurchase .fa[disabled]{ color:#7b7979; }
#addPurchase .radioCon[disabled],
#addSupply .radioCon[disabled]{ background:#ddd;  }
p>span.widthAuto{ width: auto}
