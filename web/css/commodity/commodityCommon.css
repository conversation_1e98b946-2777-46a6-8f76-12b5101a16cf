.wrapBody {
    margin-left: 100px;
}
.xing{
    color: red;
}

.elemFlex {
    display: flex;
    justify-content: space-between;
    padding: 26px 0 8px 0;
}
.elemFlexSpace {
    display: flex;
    justify-content: space-around;
    padding: 16px 0;
}
.operations {
    float: right;
    padding: 16px 0;
}
.oprationName{
    margin: 0 10px;
    display: inline-block;
    min-width: 60px;
}
.oper{
    display: inline-block;
    width: 100px;
    text-align:right;
}
.bg-yellow thead td {
    border-color: #fff0;
    background-color: #fff2cc;
}

.keywordSearch input,.bonceCon .keywordSearch input {
    padding: 0 10px;
    font-size: 12px;
    min-width: 200px;
    height: 26px;
    line-height: 26px;
}

input::placeholder, textarea::placeholder {
    color: #aaa;
}

.commodityInfo {
    margin-left: 46px;
}

.commodityInfo table, .smWidth {
    width: 90%;
}

#product thead td.bg-none {
    background: none;
    background-color: inherit;
}

.ty-color-redOrange {
    color: #c65911;
}

div.Con > p {
    font-weight: 400;
    font-size: 24px;
    padding: 0 15px;
    margin-top: 25px;
}

/*左面*/
.Btop {
    padding: 10px 0 10px 15px;
    background: #bdd7ee;
}

.between {
    position: relative;
    float: left;
    border: 1px solid #e3e3e3;
    border-top-color: #f0f8ff;
    border-bottom: 3px solid #fff;
    height: 100%;
    width: 10px;
    top: -2px;
    background-color: #f0f8ff;
}

.between2 {
    border-left: 1px solid #e3e3e3;
    border-right: 1px solid #e3e3e3;
    width: 109%;
    position: absolute;
    bottom: -6px;
    height: 10px;
    left: -1px;
    background-color: #f0f8ff;
}

.Btop > span {
    font-weight: bold;
}

.faceul {
    min-height: 400px;
    list-style-type: none;
}

.faceul1:hover {
    background: #eaeaea;
}

.faceul1 {
    height: 35px;
    line-height: 35px;
}

.faceul1 > a {
    color: #0069b4;
    text-decoration: none;
    display: block;
    padding: 0 0 0 26px;
}

.bottomTree .faceul1 > a {
    font-size: 12px;
}

.back-btn, .back-btn:hover {
    font-weight: 700;
    font-size: 14px;
    color: black;
}

.back-b {
    padding: 50px 0 20px 30px;
}

.add-b > a {
    color: #bf8f00;
}

.add-b > a:hover {
    text-decoration: none;
}

.add-b {
    float: left;
    padding: 10px 15px;
    margin-bottom: 15px;
}

div.add-b:hover {
    background-color: #e3e3e3 !important;
}

.faceul1 > a > span {
    display: inline-block;
    vertical-align: middle;
}

.faceul1 > a > span:nth-child(1) {
    max-width: 80%;
    overflow: hidden;
    height: 35px;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.faceul1 > a > span:nth-child(2) {
    width: 10%;
    overflow: hidden;
    text-align: center;
}

.faceul1 > a > span:nth-child(3) {
    width: 25%;
    min-width: 50px;
    text-align: right;
}

.mar {
    display: inline-block;
    margin-right: 10px;
}


/*右面*/
.bigContainer {
    border: 1px solid #e3e3e3;
}

.left_container {
    float: left;
    width: 235px;
}

.right_container {
    padding-left: 255px;
}

.Right-label, .Left-label {
    padding: 0;
}

.Left-label {
    min-height: 86px;
}

.left-bottom {
    margin-top: 45px;
}

.Right-label {
    padding: 10px 15px 10px 15px;
    min-width: 700px;
}

.conon {
    padding: 10px 0;
    display: inline-block;
    float: left;
    min-width: 300px;
}

.conon > a, .concon > a:hover {
    color: #909090;
    font-family: "方正兰亭超细黑简体";
    font-weight: 700;
}

.conon > p, .conon > p.a:hover {
    display: inline-block;
    padding: 0 5px;
    font-weight: 700;
}

.conon > a:nth-child(2), .conon > a:nth-child(2):hover {
    color: black;
    font-weight: lighter;
    text-decoration: none;
}

.dq > a {
    color: black;
}

.bj > a, .bj > a:hover {
    color: black;
    text-decoration: none;
}

.opinionCon {
    padding-left: 50px;
}

.bonceCon table input, .bonceCon table select, .bonceCon textarea {
    background-color: #fff;
    line-height: inherit;
    text-align: left;
    color: #3f3f3f;
    width: 100%;
}
.ty-table-none td span.ty-color-blue, .ty-table-none td span.ty-color-red{
    padding: 4px 8px;
    border-radius: 2px;
    font-weight: bold;
    cursor: pointer;
    white-space: nowrap;
}
.ty-table tr td span.tb-btn-sm {
    font-size: 10px;
}

.ty-table td .ty-color-red:hover {
    color: #fff;
    background-color: #ed5565;
}

.ty-table .ty-color-blue:hover {
    color: #fff;
    background-color: #5d9cec;
    transition: all .2s
}

.add-block{
    margin: 16px 0;
}
.gapLeft{
    margin-left: 30px;
}
.gapSm{
    margin-right: 10px;
}
.gapLong{
    margin-right: 60px;
}
.bg-yellow tr:nth-child(even) {
    background: #ddebf7;
}
.ty-table tr td.createInfo{
    font-size: 11px;
    white-space: nowrap;
}
.ty-indent{
    text-indent: 2em;
}
.add_invoice{
    display: inline-block;
}
.add_invoice table tr:not(last) td{
    white-space: nowrap;
}
.amountPos{position: relative;}
.amountPos input{text-align: center;}
.amountPos span{position: absolute;right: 4px;  top: 4px;  color: #333;}
.bonceCon .invoice_special input[disabled]{background: #c6c6c6;}
.modInput{}
.modTip{margin-left: 30px;width: 390px; }
.modInfo [disabled]{ background: #d1d1d1;cursor: not-allowed;}
.modItem-l,.modItem-m{position: relative;float:left;margin: 12px 32px 0 32px;}
.modItem-l {width: 870px;}
.modItem-m {width: 400px;}
.modItem-s {width: 140px;position: relative;}
.modItem-ss {margin-right: 20px;width: 80px;}
.modItem-s .ty-inputText,.modItem-ss .ty-inputSelect {min-width: 60px;}
.modInfo .ty-inputText,.modInfo .ty-inputSelect {width: 100%;}
.modItemTtl{  padding-left:8px;}
.red{display: inline-block;color: #ff0000;width: 10px;}
.nodeBtn{color: #0070c0;cursor: pointer;border: none;}
.nodeBtnGray{ color: #aaa; }
.imgsthumb {float: left;width: 40px;}
.imgsthumb .fa{color: #0070c0;font-size: 12px;}
.filePic{display: inline-block;width:20px;}
.file-box{min-height: 30px; padding: 4px 8px;border: 1px solid #dcdfe6;background: #fff;}
.inTip{color:#d1d1d1;}
#seeCommodity .ty-table td,.align-left td{text-align: left;}
.clearInputVal {position: absolute;  top: 30px;  right: 4px;  color: #0070c0;  line-height: 30px;  font-size: 14px;  display: none;}
input:focus + i.clearInputVal{  display: block;  }
.modPics{position: relative;}
.modPics .clearPicsBtn{position: absolute;  top: 0;  right: 4px;width: 30px;  color: #0070c0;  line-height: 30px;  font-size: 14px;  display: none;}
.comPics span,.comVideo span{padding-right: 4px;margin-right: 6px;display: inline-block; cursor: pointer; color: #0070c0;}
#picShow,#video-box{  position: fixed;  display: inline-flex;  justify-content: center;  border: 1px solid #aaa;  background: rgba(7, 7, 7, 0.64);  z-index: 100009;  width: 100%;
    height: 100%;  text-align: center;  vertical-align: middle;  top: 0;  left: 0;  align-items: center;  }
#picShow img{  margin-top:30px;  }
.gray{padding-left: 12px;height: 30px;line-height: 30px; background: #d1d1d1;}
.moveL{  margin-left: 32px;}
#addCommodity hr,#editCommodityOther hr{border-top: 1px solid #bfbfbf;}
.linkUploadify .uploadify-queue,.mediaBody .uploadify-queue{display: none;}
/*批量导入*/
.exportStep {  padding-left: 40px;  }
.flexRow{  margin-left: 50px;  display: flex;  justify-content:space-between;  width: 356px;  }
.fileFullName{  width: 300px; line-height:24px;background: #fff;text-align: center;}
.importSect{padding-top: 20px;margin-right: 60px;}
.narrowBody{margin: 0 auto;width: 80%;}
.changeDot{margin-bottom: 12px;}
.changeDot span{margin-right: 12px;}
.stepItem {  margin-bottom: 20px;  }
.viewBtn .uploadify-button{ padding: 0 12px;  margin: 0;display: inline-block; border-radius: 0; height: 24px;  line-height: 26px;background-color: #5d9cec;  color: #fff;border: none;}
.viewBtn .uploadify-button:hover{ background: #5d9cec;}
.mainCon3{display: none;}
.mainCon3 .importCon2 select{ border: 1px solid #d7d7d7;}
.mainCon3 .importCon2 input{width: 120px; border: 1px solid #d7d7d7;text-align: center;}
.bg-yellow thead .tdSpecial{border-bottom: 1px solid #d7d7d7;}
.gap-lt{margin-left: 50px;}
.ty-table td .darkGray{color: #aaa;font-style: normal;}
.mTtl{display: inline-block;width: 200px;}
.md-out{margin-bottom: 20px;}

.pannelTab td{ padding:10px 20px; width: 462px;  }
.pannelTab{ width: 924px; margin: 10px auto;}
.line{ border-bottom: 1px solid #bfbfbf; width: 894px; margin: 0 auto; }
.blueBtn{
    color: #0070c0;cursor: pointer;border: none;
    margin-left: 20px;
}

#twoEditLog{ width: 800px }
#twoEdit .blueTip{ color: #0a9bee; font-size: 0.8em; }
#twoEdit .padC{ padding:5px 20px; }
#twoEdit .selectItems{ margin:10px }
#twoEdit .selectItem{ padding:5px 20px; }
#twoEdit .selectItem .fa{ color: #0070c0; }
#twoEdit hr{ border-bottom: 1px solid #ccc;  }
#twoEdit input:disabled{ background: #eaeaea; }
#twoEdit{
    width: 600px;
}
.modelVal , .modelVal{
    padding-left: 30px;
}
#codeRepeatTip .repeatCode{
    color: #0b94ea;
    font-weight: bold;
}

.areaForm {margin: auto;width: 85%;}
.areaForm span.sale_ttl1 {width: 180px;text-align: left;}

