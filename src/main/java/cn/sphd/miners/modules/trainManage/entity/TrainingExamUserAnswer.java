package cn.sphd.miners.modules.trainManage.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2021-03-18 
 */

@Entity ( name ="TrainingExamUserAnswer" )
@Table ( name ="t_training_exam_user_answer" )
public class TrainingExamUserAnswer  implements Serializable {

	private static final long serialVersionUID =  4524358440960778438L;


	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * 机构ID
	 */
   	@Column(name = "org" )
	private Integer org;

	/**
	 * 考核人员ID
	 */
   	@Column(name = "exam_user" )
	private Integer examUser;

	/**
	 * 考核ID
	 */
   	@Column(name = "exam" )
	private Integer exam;

	/**
	 * 用户ID
	 */
   	@Column(name = "user" )
	private Integer user;

	/**
	 * 考题ID
	 */
   	@Column(name = "exam_question" )
	private Integer examQuestion;

	/**
	 * 答题ID,多个以逗号分隔.选择题不存
	 */
   	@Column(name = "exam_key" )
	private String examKey;

	/**
	 * 是否正确
	 */
   	@Column(name = "is_correct" )
	private Integer isCorrect;

	/**
	 * 得分
	 */
   	@Column(name = "score" )
	private Integer score;

	/**
	 * 备注
	 */
   	@Column(name = "memo" )
	private String memo;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_date" )
	private Date createDate;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_date" )
	private Date updateDate;

	/**
	 * 操作:1-增,2-删,3-改
	 */
   	@Column(name = "operation" )
	private String operation;

	/**
	 * 修改前记录ID
	 */
   	@Column(name = "previous_id" )
	private Integer previousId;

	/**
	 * 版本号,每次修改+1
	 */
   	@Column(name = "version_no" )
	private Integer versionNo;

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOrg() {
		return this.org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getExamUser() {
		return this.examUser;
	}

	public void setExamUser(Integer examUser) {
		this.examUser = examUser;
	}

	public Integer getExam() {
		return this.exam;
	}

	public void setExam(Integer exam) {
		this.exam = exam;
	}

	public Integer getUser() {
		return this.user;
	}

	public void setUser(Integer user) {
		this.user = user;
	}

	public Integer getExamQuestion() {
		return this.examQuestion;
	}

	public void setExamQuestion(Integer examQuestion) {
		this.examQuestion = examQuestion;
	}

	public String getExamKey() {
		return this.examKey;
	}

	public void setExamKey(String examKey) {
		this.examKey = examKey;
	}

	public Integer getIsCorrect() {
		return this.isCorrect;
	}

	public void setIsCorrect(Integer isCorrect) {
		this.isCorrect = isCorrect;
	}

	public Integer getScore() {
		return this.score;
	}

	public void setScore(Integer score) {
		this.score = score;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getCreator() {
		return this.creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return this.createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getUpdator() {
		return this.updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return this.updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return this.updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public String getOperation() {
		return this.operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public Integer getPreviousId() {
		return this.previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return this.versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}

}
