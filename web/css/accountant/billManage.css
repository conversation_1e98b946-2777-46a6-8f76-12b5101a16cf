.settle-date{font-size: 18px;}
.vcher-mod{padding-top: 20px;border-bottom: 1px solid #ccc;}
.vcher-mod-no{padding-top: 20px;}
.row-con{margin: 2px 0 30px 0;}
.row-con::before, .row-con::after,.row-con-no::before, .row-con-no::after {  display: block;  clear: both;  content: "";  visibility: hidden;
    height: 0;  }
.careful{font-size: 12px;color: #0070c0;}
.con-txt{display: inline-block;width: 400px;}
.sysHd{width: 100px;}
.con-txt-w{width: 300px;  display: inline-block;}
.count-con ul{padding-left: 50px;}
.count-con ul li{margin-top: 30px;line-height: 32px;}
.count-con ul li::before,.count-con ul li::after {  display: block;  clear: both;  content: "";  visibility: hidden;
    height: 0;  }
.count-it {display: inline-block;width: 230px;}
.ct-essay{margin: 0 40px 0 200px;}
.bothPriod, .bothPriodByMonth{padding-left: 70px; width: 500px;}
.bothPriod ul li, .bothPriodByMonth ul li{padding-bottom: 10px;}
.bothPriod ul li .fun-btn{margin-left: 20px;}
.txt-year{display: inline-block;width: 300px;}
.bothPriod>div span{margin-right: 30px;}
.mainCon1,.mainCon2,.mainCon3,.mainCon4{padding-left: 100px;padding-right: 50px;}
.mainCon2,.mainCon3{width: 50%;}
.mainCon{display: none;}
.gapHd{margin-left: 100px;}
.scanBtn{padding: 0 52px;}
.ty-secondTab{margin-bottom: 20px;}
.tblCon2{margin-left: 20px;}
.back-btn{margin-bottom: 20px;}
.regulation{padding-bottom: 10px;}
.inputPart{padding-top: 20px;clear: both;  }
.ellipsis{ width: 100%;overflow: hidden;white-space: nowrap;text-overflow: ellipsis;}
.mainCon5 td > div ,.mainCon8 td > div { margin: 0 -5px;height: 35px; line-height: 35px; border-bottom: 1px solid #d7d7d7;  }
.mainCon5 td > div:last-child ,.mainCon8 td > div:last-child { border-bottom: none; }
.ty-mainData .ty-table{ table-layout: fixed;}

















































