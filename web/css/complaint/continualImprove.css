.formItem{
    margin-top: 8px;
    overflow: hidden;
    clear: both;
}
.formTitle,.formCon{
    float: left;
}
.formTitle{
    width: 130px;
    text-align: right;
    padding:0 10px;
    line-height: 30px;
    min-height: 30px;
    color: #67788a;
}
.formCon{
    line-height: 30px;
}
.formCon small{
    vertical-align: top;
    margin: 0 7px;
    color: #67788a;
}
.formCon .cp_date{
    display: inline-block;
    width: 230px;
}
.formCon input{
    width: 250px;
}
.formCon input.date{
    width: 230px;
}
.formCon textarea{
    width: 630px;
    line-height:16px;
    padding:5px 10px;
    font-size: 12px;
}
.cp_input,.cp_textarea,.cp_date{
    background-color: #e9f3fb;
    line-height: 28px;
    min-height: 28px;
    text-align: left;
    padding: 0 8px;
    width: 250px;
    border-radius: 0;
}
.cp_input{
    display: inline-block;
    width: 250px;
}
.cp_textarea{
    width: 630px;
}
.flagTab{
    position: relative;
}

.panel_approve{
    display: block;
    border: 1px solid #d4e0ef;
    border-radius: 3px;
    background-color: #f0f3f8;
    margin: 16px 32px;
}
.approve_con{
    display: none;
    border-bottom:1px solid #d4e0ef;
}
.approve_state{
    display: block;
    font-size: 16px;
    padding:12px 20px 0px;
}
.approve_state i{
    margin-right: 5px;
    font-size: 22px;
}
.approve_state i.fa-check-circle{
    color:#5d9cec;
}
.approve_state i.fa-times-circle{
    color:#ed5565;
}
.approve_record{
    display: block;
    margin-left: 40px;
    color: #848f9c;
    padding:4px 20px 12px;
}
.approve_item{
    padding: 4px 0;
}
.approve_foot{
    padding: 6px 20px;
    font-size: 14px;
    color: #67788a;
}
.panel_foot i{
    margin-left: 5px;
    transition: all 0.5s;
}

.ty-container{
    padding-top: 10px;
}