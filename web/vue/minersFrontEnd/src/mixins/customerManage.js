
/*
* 机构管理 共用文件
* 目前用到的 页面如下：
*   公司总览-客户-客户信息 /views/companyOverview/customers/customerInfo.vue、
*   销售管理-客户管理 /views/sale/customerManage.vue、
*   销售管理-机构管理 /views/sale/orgManage.vue、
*
* */

import { beforeRouteLeave, initNav } from "@/utils/routeChange"
import { validatePostCode, testMobile } from "@/utils/formTest"

import auth from '@/sys/auth'
import * as customerApi from "@/api/customer.js"
import uploadFile from "@/components/uploadFile.vue"
import TyMenuTree from "@/components/TyMenuTree.vue"
import customer_blank from "@/assets/templateFiles/customer_blank.xls"

export default {
    name:"customerManageJs",
    data() {
        return {
            curPage: 'main' ,
            customer_blank: customer_blank,
            pageSize:20,
            rootPath:{},
            loginUserOid: auth.getUser().oid,
            specialOrgPart:false, // true-特殊机构， false-不是特殊机构
            rootCusBtn:'',
            mainData:{
                list: [],
                pageShow: true,
                pageInfo:{ 'currentPageNo': 3, 'pageSize': 10 , 'totalPage': 15  },
                editObj: null ,
                searchVal: null ,
                params: null ,
                curpage:0,
            },
            orgEditLogData:{
                visible: false ,
                list: []
            },
            custormerLeadingData:{
                visible: false,
                path: '',
                fileName: '',
                file: '',
            },
            addTipData:{
                visible:false,
                visible2: false
            },
            addData:{
                visible: false,
                specialBtnType: '',
                chanNum: 0,
                quanNum: 0,
                chanSum: 9,
                quanSum: 9,
                base:{
                    fullName: '',
                    cuscoding: '',
                    address1: '',
                    name: '',
                    buyCase: 1,
                    firstTime: 0,
                    firstTimeN: 0,
                    chanImg: [],
                    quanImg: [],
                    infoSource:'',
                    firstContactTime:'',
                    firstContactAddress:'',
                    memo:'',
                },
                invoice:{
                    invoiceName:'',
                    invoiceAddress:'',
                    telephone:'',
                    bankName:'',
                    bankNo:'',
                    taxpayerID:'',
                },
                huo:{
                    info:'',
                    txt:'',
                },
                contract:{
                    tableData:[{
                        no:'',
                        date:'',
                        expDur:'',
                        info:'',
                    }],
                },
                mail:{
                    tableData:[{
                        no:'',
                        address:'',
                        recivePerson:'',
                        mailNo:'',
                        tel:'',
                        info:'',
                    }],
                },
                contactLianxi:{
                    tableData:[{
                        name:'',
                        post:'',
                        info:'',
                    }],

                },
            },
            receiveData:{
                visible: false,
                type:'',
                status: 0 ,
                addressList:[{addr:'', name:'', tel:''}],
                areaList:[{addr:'', name:'', tel:''}],
            },
            receiveAreaInfoData:{
                visible: false,
                type:'', // add -新增 ， update-修改
                area: '',
                editIndex: {},
                areaArr: [],
                requirements: '',
                contact: {},
                areaList:[],
                options:[],
            },
            addressEditData:{
                visible: false,
                type:'',
                editIndex:'',
                address: '',
                contact:{ name:'' },
            },
            chooseCusContactData:{
                visible:false,
                type:'',
                contactList:[{ id:1, name:'联系人' }],
                selectConact: ''
            },
            editContactLianXiData:{
                typeStr:'', // 哪里的新增联系人按钮
                visible:false,
                tag: '' ,
                editIndex: '' ,
                showOptions: false ,
                loading:false,
                loadingBtn:'点击此处上传名片',
                name:'',
                postName:'',
                tel:'',
                defineType:'',
                type:'',
                typeList:[
                    { label: '手机', value:1 ,disabled: false },
                    { label: 'QQ', value:2 ,disabled: false },
                    { label: 'Email', value:3 ,disabled: false },
                    { label: '微信', value:4 ,disabled: false },
                    { label: '微博', value:5 ,disabled: false },
                    { label: '自定义', value:9 ,disabled: false },
                ],
                contactArr:[],
                contactList:[],
                picInfo:{}
            },
            contactDetails:{
                visible: false,
                info: {},
            },
            contactCard:{
                visible: false,
                visitCard: ''
            },
            useDefinedLabelData:{
                visible: false,
                val:'',
            },
            invoiceSetData:{
                visible: false,
                selectType:null,
                type:'',
                options:[
                    {type:1, txt:'需要主要为增值税专用发票'},
                    {type:2, txt:'需要主要为增值税专用发票以外的发票'},
                    {type:3, txt:'基本不需要开发票'},
                    {type:4, txt:'要求不定，维持不设置的状态'},
                ]
            },
            contractInfoData:{
                editContractIndex:'',
                editContract:'',
                visible: false ,
                cusName:'',
                title:'',
                type:'',
                isEnd:'',
                cNo:'',
                cSignDate:'',
                cStartDate:'',
                cMemo:'',
                cEndDate:'',
                scanPics:[],
                scanFile:{},
                selectGs:[],
                selectZSGs:[],
                oralGsList:null,

            },
            contractGoodsData:{
                visible: false,
                gsType: '', // zs-专属商品; null-通用
                type: '',
                title: '',
                num: 0,
                gslist: null,
                selectGS: null,

            },
            mailInfoData:{
                visible:false,
                editMailIndex:'',
                editObj:'',
                type:'',
                mailValidSta:false,
                address:'',
                code: '',
                contact: { name:'' },

            },
            seeAccountData:{
                visible : false ,
                visibleSH : false ,
                info:{},
                contractBaseList:[],
                areaList:[],
                addressList:[],
                selfStateTxt:'',
            },
            suspendRecordData:{
                visible: false,
                list: [],
            },
            updateRecordsData:{
                visible: false,
                title: '',
                recordTip: '',
                recordEditer: '',
                list: [],
            },
            manageBaseLogData:{
                visible : false ,
                title:'',
                info:'',
                quanImg:'',
                chanImg:'',
                hasBuyStr:'',
            },
            manageInvoiceLogData:{
                visible : false ,
                title:'',
                info:'',
            },
            updateCustomerData:{
                visible : false ,
                editCustomer:{},
                info: {} ,
                base: {} ,
                adressList: [] ,
                areaList: [] ,

            },
            manageBaseData:{
                visible: false,
                fullName:'',
                cuscoding:'',
                address1:'',
                name:'',
                firstTime:'',
                firstTimeN:'',
                initialPeriod:'',
                firstContactTime:'',
                firstContactAddress:'',
                infoSource:'',
                supervisorName:'',
                supervisorMobile:'',
                memo:'',
                quanNum: 0 ,
                chanNum: 0 ,
                quanSum: 9 ,
                chanSum: 9 ,
                quanImg:[],
                chanImg:[],

            },
            manageInvoiceData:{
                visible:false,
                invoiceType:'',
                invoiceName:'',
                invoiceAddress:'',
                telephone:'',
                bankName:'',
                bankNo:'',
                taxpayerID:'',

            },
            contractScanData:{
                visible: false,
                editContract:{},
                contractBase:{},
                opacity: false,
            },
            contractGsScanData:{
                visible: false,
                title: '',
                txt: '',
                contractGsScanData: []
            },
            contractByGsData:{
                visible: false,
                cList:[],
                gsInfo :{}
            },
            contractHisData:{
                visible: false,
                txt:'',
                hisList:'',
            },
            contractBaseSignData:{
                visible: false,
                txt:'',
                list:'',
                editContract:''
            },
            contractStopData:{
                visible: false,
                type: '',
                stopObj: {},
            },
            stopContractData:{
                visible: false,
                list: [],
            },
            endContractData:{
                visible: false,
                list: [],
            },
            stopContactData:{
                visible: false,
                editObj:null,
            },
            updateContactData:{
                visible1: false,
                editObj: null,
            },
            stopAdresstData:{
                visible1: false,
                editObj: null,
            },
            stopShAdressData:{
                visible: false,
                title: '',
                type: '',
                list: [],
            },
            adressLogData:{
                visible: false,
                title:'',
                list: [],
            },
            adressLogDetailsData:{
                visible: false,
                title: '',
                shAddress: '',
                shName: '',
                shNumber: '',
                shDisUse: '',
                nowData: {},
                frontData: {},
            },
            contactStopData:{
                visible : false,
                list : false,
            },
            contactUpdateLogData:{
                visible : false ,
                list:[],
                tip:'',
                tipTime:'',
            },
            contactUpdateLogDetailsData:{
                visible : false ,
                title : '' ,
                tel : '' ,
                postName : '' ,
                name : '' ,
                contactList : [] ,
                picInfo:{ src:'' },
            },
            delCusData:{
                visible:false,
                tip:'',
                editObj:''
            },
            paulsCusData:{
                visible: false,
                editObj: '',
                state: ''
            },
            suspendCusData:{
                list:[],
                pageShow: '',
                pageInfo: '',
                params: {} ,
            },
            orgListData:{
                list:[],
                editObj:null,
                pageShow: false,
            },
            orgEditData:{
                visible: false ,
                title: '' ,
                type: '' , // add-新增， update-修改
            },
            orgInfoData:{
                visible:false,
                info:null,
            },
            orgInfoHisData:{
                visible:false,
                list:null,
                pageInfo: null,
                pageShow: false,
            },
            orgHisInfoData:{
                visible:false,
                info:null
            },
            proInfoData:{
                visible:false,
                info:null,
            },
            packageData:{
                visible:false,
                info:null,
            },
            proScanData:{
                visible : false ,
                list : [] ,
            },
            packageListData:{
                visible: false
            },
            addServiceData:{
                visible:false,
                info: { orgInfo:{}, mpPackageInfo:{}, increaseModuleList:[], },
                type:0,
                tab1:[],
                tab2:'',
            },
            seeSetMenuData:{
                visible:false,
                list:[],
                setMenuName:'',
                create:'',
            },
            orgLoginData:{
                mainType:'',
                params:{},
                beginDate: '' ,
                endDate: '',
                visible: false,
                td1Txt: '',
                creatInfo: '',
                searchType: '',
                logData:{}
            },
            resourceManageData:{
                visible:false,
                info:'',
            },
            stopServiceData:{
                visible :false,
                visible2 :false,
                orgItem :'',
            },
            outOfOrgData:{
                list:[],
                pageShow: false,
                pageInfo: {},
            },
            dropdownData:{
                visibleIndex: '',
                timer:-1 ,
                showType: null ,
            },
        }
    },
    components: {
        uploadFile,
        TyMenuTree,
    },
    beforeRouteLeave(to, from, next) {
        beforeRouteLeave(to, from, next,this)
    },
    computed:{
        orgEditOKBtn(){
            let status = false
            if(this.orgEditData.type === 'add'){
                if(this.orgEditData.fullName && this.orgEditData.name
                    && this.orgEditData.supervisorMobile && this.orgEditData.uploadStorageType
                    && this.orgEditData.packageId){
                    if(testMobile(this.orgEditData.supervisorMobile)){
                        status = true;
                    }
                }
            }else if(this.orgEditData.type === 'update'){
                if(this.orgEditData.uploadStorageType){
                    status = true;
                }
            }else if(this.orgEditData.type === 'updatepro'){
                if(this.orgEditData.packageId){
                    status = true;
                }
            }

            return status
        },
        addDataOkBtn(){
            let status = false
            if(this.specialOrgPart){
                if(this.addData.base.supervisorName && this.addData.base.supervisorMobile && this.addData.base.fullName && this.addData.huo.txt && this.addData.base.firstTime){ status = true; }
            }else{
                if(this.addData.base.fullName && this.addData.huo.txt && this.addData.base.firstTime){ status = true; }

            }
            return status
        },
        manageBaseOkBtn(){
            let status = false
            if(this.manageBaseData.fullName ){ status = true; }
            return status
        },
        mailInfoOkBtn(){
            let status = false
            if(this.mailInfoData.address && this.mailInfoData.code && this.mailInfoData.contact.name){ status = true; }
            return status
        },
        editContactLianXiOkStatus(){
            let status = false
            if(this.editContactLianXiData.name){ status = true; }
            return status
        },
        contactLianxiTwo(){
            let newList = []
            let twoItem = {}
            let list = this.addData.contactLianxi.tableData
            list.forEach((item,index) =>{
                if(index%2 === 0){
                    twoItem = { name:item.name, post:item.post, index1: index, info1: item.info }
                    if(index+1 === list.length){
                        newList.push(twoItem)
                    }
                }else{
                    twoItem.name2 = item.name
                    twoItem.post2 = item.post
                    twoItem.info2 = item.info
                    twoItem.index2 = index
                    newList.push(twoItem)
                }
            })
            return newList
        }
    },
    destroyed () {
    },
    methods: {
        slDropdownHide(){
            let thisSco = this
            this.dropdownData.timer = setTimeout(function () {
                thisSco.dropdownData.visibleIndex = ''
            },100)
        },
        slDropdownClick( indexNum, event){
            if(event){
                this.dropdownData.showType = 'normal'
                const y = event.clientY;
                const allHeight = window.innerHeight;
                const distanceToBottom = allHeight - y
                console.log('距离下侧的位置=', distanceToBottom)
                if(distanceToBottom < 160){
                    this.dropdownData.showType = 'low'

                }
            }
            this.dropdownData.visibleIndex = indexNum
            clearTimeout(this.dropdownData.timer)
        },
        hideOrgEditLog(){
            this.orgEditLogData.visible = false
        },
        getOrgCtrlList(){
            let orgID = this.orgInfoData.info.id
            customerApi.orgOutOfRestoreHistories({ id: orgID }).then(res1=>{
                let list = res1.data.data
                this.orgEditLogData.visible = true
                list.forEach(item=>{
                    item.timeStr = item.updateName + ' ' + (new Date(item.updateTime).format('yyyy-MM-dd hh:mm:ss'))
                })
                this.orgEditLogData.list = list

            }).catch(err=>{
                console.log('err=',err)
                this.$message.error('连接错误，请重试！')
            })
        },
        hideOrgHisInfo() {
            this.orgHisInfoData.visible = false
            customerApi.getOrganizationHistoryInfo().then(res1=>{
                let res = res1.data
                this.orgHisInfoData.info = res.data

            }).catch(err=>{
                console.log('err=',err)
                this.$message.error('获取数据失败！')
            })
        },
        orgHisDetails(item, indexI){
            let orgBegId = item.id
            customerApi.getOrganizationHistoryInfo({"id": orgBegId}).then(res1=>{
                let res = res1.data.data
                this.orgHisInfoData.visible = true
                this.orgHisInfoData.info = res
                if(indexI === 0){
                    this.orgHisInfoData.info.txt = `<div>以下为机构的原始信息</div><div></div>`
                }else{

                }

            }).catch(err=>{
                console.log('err=',err)
                this.$message.error('连接错误，请重试！')
            })

        },
        orgHisClick(pageItem){
            let orgID = this.orgInfoData.info.id
            console.log('this.orgInfoData.info=', this.orgInfoData.info)
            let params = {"id": orgID, "pageSize": 20, "currentPageNo": pageItem.page }
            this.getOrganizationHistoriesFun(params)
        },
        getOrgHisList(){
            let orgID = this.orgInfoData.info.id
            console.log('this.orgInfoData.info=', this.orgInfoData.info)
            let params = {"id": orgID, "pageSize": 20, "currentPageNo": 1}
           this.getOrganizationHistoriesFun(params)
        },
        getOrganizationHistoriesFun(params){
            this.orgInfoHisData.pageShow = false
            customerApi.getOrganizationHistories(params).then(res1=>{
                let res = res1.data.data
                this.orgInfoHisData.visible = true
                let list = res.organizationHistoryList
                list.forEach((item,index) => {
                    item.operat = item.operationName + ' ' + (new Date(item.updateTime).format('yyyy-MM-dd'))
                })
                this.orgInfoHisData.list = list
                this.orgInfoHisData.pageInfo = res.pageInfo
                this.orgInfoHisData.pageShow = true

            }).catch(err=>{
                console.log('err=',err)
                this.$message.error('连接错误，请重试！')
            })
        },
        hideOrgInfoHis(){
            this.orgInfoHisData.visible = false
        },
        setName3(){
            this.orgEditData.name = this.orgEditData.fullName.substring(0,6)
        },
        setName2(){
            this.addData.base.name = this.addData.base.fullName.substring(0,6)
        },
        setName(){
            this.manageBaseData.name = this.manageBaseData.fullName.substring(0,6)
        },
        baseUpdateLog(){
            let customerId = this.updateCustomerData.editCustomer.id
            this.getRecordBaseListFun(customerId)
        },
        invoiceEditLog(){
            let customerId = this.seeAccountData.info.id
            this.getRecordInvoiceListFun(customerId)
        },
        invoiceManageUpdateLog(){
            let customerId = this.updateCustomerData.editCustomer.id
            this.getRecordInvoiceListFun(customerId)
        },
        getRecordInvoiceListFun(customerId){
            customerApi.getRecordInvoiceList({customerId: customerId}).then(res1=>{
                let res = res1.data
                let status = res.status;
                if (status == 1) {
                    this.setRecordsList(res, 'invoice');
                } else {
                    this.$message.error("查看失败！")
                }
            }).catch(err=>{
                this.$message.error(err)
            })
        },
        SSDok2(){
            let id = this.stopServiceData.orgItem.id
            customerApi.addOrgRestoreServiceApply({ organizationId: id }).then(res1=>{
                let res = res1.data
                let data = res.data
                if (data === 1) {
                    this.$message.success("操作成功，等待审批")
                    this.stopServiceData.visible2 = false
                } else {
                    this.$message.error("操作失败")
                }
            }).catch(err=>{
                console.log('err=', err)
            })
        },
        startService(orgItem){
            this.stopServiceData.visible2 = true
            this.stopServiceData.orgItem = orgItem
        },
        hideSSD2(){
            this.stopServiceData.visible2 = false
        },
        outOfOrgList(){
            this.curPage = 'suspendOrg'
            let params = { pageSize: 20 , currentPageNo: 1 }
            this.getOutOfOrgListFun(params)
        },
        stopOrgClick(pageItem){
            let params = {  'currentPageNo':pageItem.page, 'pageSize': 20 }
            this.getOutOfOrgListFun(params)
        },
        suspendPageClick(pageItem){
            let params = {  'currPage':pageItem.page, 'pageSize': 20 }
            this.getSuspendCustomerFun(params)
        },
        getOutOfOrgListFun(params){
            this.outOfOrgData.pageShow = false
            customerApi.getOutOfOrgList(params).then(res1=>{
                let res = res1.data
                let pageInfo = res.data.pageInfo
                let list = res.data.outOfMpOrganizationList || []
                list.forEach((item, index) => {
                    item.no = index + 1
                    item.updateTime = item.updateTime ? (new Date(Number(item.updateTime)).format('yyyy-MM-dd hh:mm:ss')) : '--'
                    item.updater = item.updateName + ' ' + item.updateTime

                })
                this.outOfOrgData.list = list
                this.outOfOrgData.pageShow = true
                this.outOfOrgData.pageInfo = {
                    currentPageNo: pageInfo.currentPageNo,
                    pageSize: pageInfo.pageSize ,
                    totalPage: pageInfo.totalPage
                }

            }).catch(err=>{
                console.log('err=', err)
            })
        },
        SSDok(){
            customerApi.addOrgOutOfServiceApply({ organizationId: this.stopServiceData.orgItem.id }).then(res1=>{
                let res = res1.data
                let data = res.data
                if (data === 1) {
                    this.$message.success('操作成功，等待审批')
                    this.stopServiceData.visible = false
                } else {
                    this.$message.success('操作失败!')
                }
            }).catch(err=>{
                this.$message.success('操作失败!')
                console.log('err=', err)
            })
        },
        hideSSD(){
            this.stopServiceData.visible = false
        },
        stopService(orgItem){
            this.stopServiceData.visible = true
            this.stopServiceData.orgItem = orgItem
        },
        hideRM(){
            this.resourceManageData.visible = false
        },
        resouceManage(orgItem){
            customerApi.getSpaceTrafficInfo({ id:orgItem.id  }).then(res1=>{
                let res = res1.data
                if(res.success === 1){
                    this.resourceManageData.visible = true
                    this.resourceManageData.info = res.data
                }else{
                    this.$message.error('未获取有效数据')
                }

            }).catch(err=>{
                console.log('err=', err)
            })

        },
        backOrgList(){
            this.curPage = 'orgList'
        },
        backOrgList2(){
            console.log('mainType=', this.orgLoginData.mainType)
            this.curPage = this.orgLoginData.mainType
        },
        defaultSearchBtn(){
            this.orgLoginData.visible = true
        },
        searchType(type){
            let params = { id: this.orgLoginData.params.id ,  type: type ,  beginDate: '' ,  endDate: '' }
            if(type === 3){
                params.beginDate = this.orgLoginData.beginDate
                params.endDate = this.orgLoginData.endDate
                if(!params.beginDate || !params.endDate){
                    this.$message.error('请输入完整的起止时间！')
                    return false
                }
            }
            this.getOrganizationLoginsFun(params)
            this.orgLoginData.visible = false
        },
        loginRecord(orgItem, type){
            let params = { id: orgItem.id ,  type: 1 ,  beginDate: '' ,  endDate: '' }
            this.orgLoginData.searchType = 'yue'
            this.orgLoginData.mainType = type
            this.getOrganizationLoginsFun(params)
            this.curPage = 'loginRecord'

        },
        getOrganizationLoginsFun(params){
            this.orgLoginData.searchType = params.type
            let beginYear = new Date(params.beginDate).format('yyyy')
            let beginMonth = new Date(params.beginDate).format('MM')
            let endYear = new Date(params.endDate).format('yyyy')
            let endMonth = new Date(params.endDate).format('MM')
            this.orgLoginData.params = params
            params.beginDate = new Date(params.beginDate).format('yyyy-MM-dd')
            params.endDate = new Date(params.endDate ).format('yyyy-MM-dd')
            customerApi.getOrganizationLogins(params).then(res1=>{
                let res = res1.data
                if(res.success === 1){
                    this.orgLoginData.logData = res.data
                    let defType = 0;
                    if(beginYear !== endYear){//跨年
                        defType = 3;
                        this.orgLoginData.td1Txt = '登录年份'
                    }else if(beginMonth !== endMonth){//垮月
                        defType = 2;
                        this.orgLoginData.td1Txt = '登录月份'
                    }else{
                        defType = 1;
                        this.orgLoginData.td1Txt = '登录日期'
                    }
                    console.log('res.data.createDate=', res.data.createDate)
                    this.orgLoginData.creatInfo = res.data.createName + ' ' + (new Date(Number(res.data.createDate)).format('yyyy-MM-dd hh:mm:ss') )
                    let userLogList = res.data.statisticsList || []
                    userLogList.forEach(item=>{
                        let dateFormat = ''
                        if (params.type == 1 || (params.type == 3 && defType == 1)) {
                            dateFormat = new Date(item.loginDate).format('yyyy-MM-dd');
                        } else if (params.type == 2 || (params.type == 3 && defType == 2)) {
                            dateFormat = new Date(item.loginDate).format('yyyy-MM');
                        } else {
                            dateFormat = new Date(item.loginDate).format('yyyy');
                        }
                        item.dateFormat = dateFormat
                    })

                }else{
                    this.$message.error('s获取数据失败，请重试！')
                }

            }).catch(err=>{
                console.log('err=', err)
            })
        },
        packageSubmit(){
            let incrementModules = this.addServiceData.tab1
            let setId = this.addServiceData.tab2
            let params = {
                organizationId: this.addServiceData.info.orgInfo.id  , //机构id ，
                packagesId: this.addServiceData.info.mpPackageInfo.id, // 产品id
                incrementModules: incrementModules.join(','),
                setId: setId || null
            }
            customerApi.addIncrementApply(params).then(res1=>{
                let res = res1.data
                if (res.data === 1) {
                    this.$message.success("操作成功")
                    this.addServiceData.visible = false
                } else {
                    this.$message.error("操作失败")
                }

            }).catch(err=>{
                console.log('err=', err)
            })
        },
        hideSeeSetMenu(){
            this.seeSetMenuData.visible = false
        },
        seeMenu(item){
            customerApi.getModulesByMpSetId({ id:item.id  }).then(res1=>{
                let res = res1.data
                this.seeSetMenuData.visible = true
                this.seeSetMenuData.list = res.data
                this.seeSetMenuData.setMenuName = item.name
                let create = item.createName + ' ' + (new Date(item.createDate).format("yyyy-MM-dd hh:mm:ss"))
                this.seeSetMenuData.create = create


            }).catch(err=>{
                console.log('err=',err)
            })
        },
        toggleType(type){
            if(this.addServiceData.type === type){
                this.addServiceData.type = ''
            }else{
                this.addServiceData.type = type
            }
        },
        addService(orgItem){
            this.addServiceData.visible = true
            customerApi.getIncreaseModules({ id:orgItem.id  }).then(res1=>{
                let res = res1.data
                this.addServiceData.info = res.data
                this.addServiceData.info.increaseModuleList = this.addServiceData.info.increaseModuleList || []

                console.log('addServiceData=', res.data)


            }).catch(err=>{
                console.log('err=', err)
                this.$message.error('链接错误！')
            })

        },
        hideService(){
            this.addServiceData.visible = false
        },
        hidePackageList(){
            this.packageListData.visible = false
        },
        packageList(){
            this.packageListData.visible = true
            let increaseModuleList = this.packageData.info.increaseModuleList
            let ids0 = increaseModuleList.map(item => item.id)
            let ids = ids0.length>0?ids0.join(','):0
            customerApi.getMpSets({ id:ids }).then(res1=>{
                let res = res1.data



            }).catch(err=>{
                console.log('err=', err)
            })
        },
        hideProScan(){
            this.proScanData.visible = false
        },
        seeModule(item){
            customerApi.getModuleInfo({ id: item.id }).then(res1=>{
                let res = res1.data
                let list = res.data
                this.proScanData.list = list
                this.proScanData.visible = true
                this.proScanData.name = item.name

            }).catch(err=>{
                console.log('err=', err)
            })

        },
        hidePackage(){
            this.packageData.visible = false
        },
        scanSelectPackage(packageId){
            if(!packageId){
                this.$message.info('没有产品查看！')
                return false
            }
            customerApi.getMpPackagesInfo({ packagesId: packageId }).then(res1=>{
                let res = res1.data.data
                this.packageData.visible = true
                this.packageData.info = res
                console.log('packageData.info.reNameList=', this.packageData.info.reNameList)




            }).catch(err=>{
                console.log('err=', err)
            })
        },
        updatePro(){
            this.orgEditData.title = '修改产品信息'
            this.orgEditData.type = 'updatepro'
            this.orgEditData.visible = true
            this.orgEditData.fullName = this.proInfoData.info.fullName
            this.orgEditData.name = this.proInfoData.info.name
            this.orgEditData.address = this.proInfoData.info.address
            this.orgEditData.supervisorName = this.proInfoData.info.supervisorName
            this.orgEditData.supervisorMobile = this.proInfoData.info.supervisorMobile
            this.orgEditData.uploadStorageType = this.proInfoData.info.uploadStorageType
            this.orgEditData.packageId = this.proInfoData.info.mpPackageId
            this.getPackages()
        },
        updateOrg(){
            this.orgEditData.title = '机构信息修改'
            this.orgEditData.type = 'update'
            this.orgEditData.visible = true
            this.orgEditData.fullName = this.orgInfoData.info.fullName
            this.orgEditData.name = this.orgInfoData.info.name
            this.orgEditData.address = this.orgInfoData.info.address
            this.orgEditData.supervisorName = this.orgInfoData.info.supervisorName
            this.orgEditData.supervisorMobile = this.orgInfoData.info.supervisorMobile
            this.orgEditData.uploadStorageType = this.orgInfoData.info.uploadStorageType
        },
        hideOrgInfo(){
            this.orgInfoData.visible = false
        },
        proInfo(orgItem){
            customerApi.getIncrementOrgPopedom({ id: orgItem.id , haveModule: false }).then(res1=>{
                let res = res1.data
                let mpOrganization = res.data.mpOrganization
                this.proInfoData.info = mpOrganization
                this.proInfoData.visible = true
            }).catch(err=>{
                console.log('err=',err)
            })
        },
        orgInfo(orgItem){
            customerApi.getCustomerOrganizationInfo({ id : orgItem.id }).then(res1=>{
                let res = res1.data
                this.orgInfoData.info = res.data
                this.orgInfoData.visible = true
            }).catch(err=>{
                console.log('err=', err)
            })
        },
        orgEditOK(){
            let params = { }
            console.log('orgEditData.uploadStorageType=', this.orgEditData)
            params.fullName = this.orgEditData.fullName
            params.name = this.orgEditData.name
            params.address = this.orgEditData.address
            params.supervisorName = this.orgEditData.supervisorName
            params.supervisorMobile = this.orgEditData.supervisorMobile
            params.uploadStorageType = this.orgEditData.uploadStorageType

            if(!params.uploadStorageType){
                this.$message.error('请选择数据存储地点！')
                return false
            }

            if(this.orgEditData.type === 'add'){
                console.log('看一下')
                console.log(this.orgListData.editObj)
                params.customerId = this.orgListData.editObj.id
                params.packageId = this.orgEditData.packageId
                customerApi.addOrgApply(params).then(res1=>{
                    let res = res1.data
                    if(res.data === 1){
                        this.$message.success("已提交申请！")
                        this.orgEditData.visible = false
                    }else{
                        this.$message.success("操作失败！")
                    }
                }).catch(err=>{
                    console.log('err=', err)
                })
            }
            else if(this.orgEditData.type === 'update'){
                console.log('params=', params)
                params.id = this.orgInfoData.info.id
                customerApi.updateCustomerOrganization(params).then(res1=>{
                    let res = res1.data
                    if(res.data === 1){
                        this.$message.success('修改成功')
                        this.orgEditData.visible = false
                    }else{
                        this.$message.error('修改失败')

                    }
                }).catch(err=>{
                    console.log('err=', err)
                })
            }
            else if(this.orgEditData.type === 'updatepro'){
                customerApi.updateOrgPopedoms({ packageId: this.orgEditData.packageId , organizationId: this.proInfoData.info.id }).then(res1=>{
                    let res = res1.data
                    if(res.data === 1){
                        this.$message.success('修改申请已提交')
                        this.orgEditData.visible = false
                    }else{
                        this.$message.error('修改申请已提交')
                    }

                }).catch(err=>{
                    console.log('err=', err)
                })
            }
        },
        hideProInfo(){
            this.proInfoData.visible = false
        },
        hideOrgEdit(){
            this.orgEditData.visible = false
        },
        addOrg(name, mobile){
            this.orgEditData.visible = true
            this.orgEditData.title = '新增机构'
            this.orgEditData.type = 'add'
            this.orgEditData.fullName = ''
            this.orgEditData.name = ''
            this.orgEditData.address = ''
            this.orgEditData.supervisorName = name
            this.orgEditData.supervisorMobile = mobile
            this.orgEditData.uploadStorageType = ''
            this.orgEditData.packageId = ''

            this.getPackages()

        },
        getPackages(){
            customerApi.getSelectMpPackages().then(res1=>{
                let res = res1.data
                this.orgEditData.packageList = res['data'] || []
            }).catch(err=>{
                console.log('err=', err)
            })
        },
        getOrganizationList(item){
            this.orgListData.editObj = item
            this.orgListData.pageShow = false

            customerApi.getCustomerOrganizationsByCustomerId({ customerId: item.id }).then(res1=>{
                let res = res1.data
                let list = res.data || []
                list.forEach(item=>{
                    item.create = item.createName + ' ' + (new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss'))
                })
                this.orgListData.list = list
                this.curPage = 'orgList'
                this.orgListData.pageShow = true

            }).catch(err=>{
                this.$message.error('链接错误！')
                console.log('err=', err)
            })
        },
        addTipOK(selectPro){
            if( !testMobile(this.addData.base.supervisorMobile) ){
                this.$message.error('请输入正确的手机号！')
                return false
            }
            customerApi.getSaleCtrlsByMobile({ mobile: this.addData.base.supervisorMobile  }).then(res1=>{
                let res = res1.data
                this.addTipData.visible = false
                if(res.data === 2){
                    this.addTipData.visible2 = true
                }else if(res.data === 1){
                    this.addDataOk(selectPro)
                }else{
                    this.$message.error('系统错误!')
                }


            }).catch(err=>{
                console.log('err=', err)
            })

        },
        hideAddTip2(){
            this.addTipData.visible2 = false
        },
        hideAddTip(){
            this.addTipData.visible = false
        },
        addDataOkSpecial(type){
            this.addData.specialBtnType = type
            if(type === 0){
                if( !testMobile(this.addData.base.supervisorMobile) ){
                    this.$message.error('请输入正确的手机号！')
                    return false
                }
                this.addTipData.visible = true
            }else if(type === 1){
                this.addTipOK('selectPro')
            }
        },
        hidetitleFun(){
            this.contactUpdateLogDetailsData.visible = false
        },
        contactlogDetail(logItem, indexLog){
            this.contactUpdateLogDetailsData.title = indexLog === 0 ? '原始信息' : '第' + indexLog + '修改后'
            let id = logItem.id
            let frontid = 0
            if(indexLog !== 0){ frontid = this.contactUpdateLogData.list[indexLog-1].id  }
            customerApi.getRecordContactDetails({
                id: id ,
                frontId: frontid
            }).then(res1=>{
                let res = res1.data
                this.contactUpdateLogDetailsData.visible = true
                let frontData = res.data.front
                let nowData = res.data.now
                this.contactUpdateLogDetailsData.picSrc = nowData.visitCard ?  this.rootPath.fileUrl + nowData.visitCard : ''
                if(frontData == null){
                    this.contactUpdateLogDetailsData.name = nowData.name || ''
                    this.contactUpdateLogDetailsData.postName = nowData.post || ''
                    this.contactUpdateLogDetailsData.tel = nowData.mobile || ''
                    this.contactUpdateLogDetailsData.contactList = this.setTable(nowData.socialList)

                }else{
                    this.contactUpdateLogDetailsData.name = this.compareD(frontData.name,nowData.name)
                    this.contactUpdateLogDetailsData.postName = this.compareD(frontData.post,nowData.post)
                    this.contactUpdateLogDetailsData.tel = this.compareD(frontData.mobile,nowData.mobile)

                    nowData.socialList.forEach(newItem=>{
                        let frontItem = this.getSocialItem(newItem.type, frontData.socialList)
                        if(frontItem){
                            if(newItem.name !== frontItem.name){
                                newItem.name = '<span style="color:#ed5565;">' + (newItem.name || '') + '</span>'
                            }
                            if(newItem.code !== frontItem.code){
                                newItem.code = '<span style="color:#ed5565;">' + (newItem.code || '') + '</span>'
                            }
                        }else{
                            newItem.name = '<span style="color:#ed5565;">' + (newItem.name || '') + '</span>'
                            newItem.code = '<span style="color:#ed5565;">' + (newItem.code || '') + '</span>'
                        }
                    })
                    this.contactUpdateLogDetailsData.contactList = this.setTable(nowData.socialList)

                }
            }).catch(err=>{
                console.log('err=', err)
            })
        },
        setTable(socialList){
            let list = []
            if(socialList.length > 0){
                socialList.forEach((linkI, index0) => {
                    let yu = index0%2
                    if(yu === 0){
                        list.push({
                            name:linkI.name, val: linkI.code ,
                            name2:'',  val2: '' ,
                        })
                    }else{
                        let latI = list[list.length-1]
                        latI.name2 = linkI.name
                        latI.val2 = linkI.code
                    }

                })
            }
            return list
        },
        getSocialItem(type, list){
            let aaa = null
            if(list.length > 0){
                list.forEach(item=>{
                    if(item.type === type){
                        aaa = item
                    }
                })
            }
            return aaa
        },
        hideContactUpdateLog(){
            this.contactUpdateLogData.visible = false
        },
        contactUpdateLogSee(item, type){
            let contactItem = type ===2 ? item.info2 : item.info1
            this.contactUpdateLog(contactItem)
        },
        seeNameID(item, type){
            let contactItem = type ===2 ? item.info2 : item.info1
            customerApi.getContactCard({ contactId: contactItem.id }).then(res1=>{
                let res = res1.data
                if(res.status == 1 && res.visitCard){
                    this.contactCard.visible = true
                    this.contactCard.visitCard = this.rootPath.fileUrl + res.visitCard

                }else{
                    this.$message.info('没有可供查看的名片！')
                }
            }).catch(err=>{
                this.$message.error('链接失败')
                console.log('err=', err)
            })

        },
        hideCrD(){
            this.contactCard.visible = false
        },
        hideCD(){
            this.contactDetails.visible = false
        },
        ctctDetails(item, type){
            let contactItem = type ===2 ? item.info2 : item.info1
            customerApi.getContactsSocial({ contactId: contactItem.id  }).then(res1=>{
                let res = res1.data
                this.contactDetails.info = res.data
                this.contactDetails.contactList = this.setTable(res.data.socialList)
                this.contactDetails.visible = true
            }).catch(err=>{
                this.$message.error('链接失败！')
                console.log('err=', err)
            })

        },
        contactUpdateLog(contactItem){
            customerApi.getRecordContactList({ contactId: contactItem.id }).then(res1=>{
                let res = res1.data
                let data = res
                this.contactUpdateLogData.visible = true
                let list = res.list || []
                this.contactUpdateLogData.list = list
                if(list.length === 0){
                    this.contactUpdateLogData.tip = '当前资料未经修改'
                    this.contactUpdateLogData.tipTime = '创建人：'+ data.createName + '&nbsp;&nbsp;' + new Date(data.createDate).format('yyyy-MM-dd hh:mm:ss')
                }else{
                    this.contactUpdateLogData.tip = '当前数据为第' + (list.length-1) + '次修改后的结果。'
                    this.contactUpdateLogData.tipTime = '修改人：'+ data.updateName + '&nbsp;&nbsp;' + new Date(data.updateDate).format('yyyy-MM-dd hh:mm:ss')
                }


            }).catch(err=>{
                console.log('err=', err)
            })

        },
        hideContactStop(){
            this.contactStopData.visible = false
        },
        addContactList(){
            customerApi.getDeleteContactsList({ customerId: 2979 }).then(res1=>{
                let res = res1.data
                this.contactStopData.visible = true
                this.contactStopData.list = res.data
            }).catch(err=>{
                console.log('err=', err)
            })
        },
        contactUpdate(contactItem, contactIndex){
            console.log('contactItem=', contactItem)
            customerApi.getContactsSocial({ contactId: contactItem.id }).then(res1=>{
                let res = res1.data.data
                this.editContactLianXiData.type = 'manageUpdate'
                this.editContactLianXiData.visible = true
                this.editContactLianXiData.title = '修改客户联系人'
                this.editContactLianXiData.showOptions = false
                this.editContactLianXiData.loading =  false
                this.editContactLianXiData.loadingBtn = '点击此处上传名片'

                this.editContactLianXiData.name = res.name
                this.editContactLianXiData.postName = res.post
                this.editContactLianXiData.tel = res.mobile
                // this.editContactLianXiData.typeStr = res.tags
                this.editContactLianXiData.tag = res.tags
                this.editContactLianXiData.newId = res.id
                this.editContactLianXiData.id = res.id
                this.editContactLianXiData.contactArr = []
                let typeList = [
                    { label: '手机', value:1 ,disabled: false },
                    { label: 'QQ', value:2 ,disabled: false },
                    { label: 'Email', value:3 ,disabled: false },
                    { label: '微信', value:4 ,disabled: false },
                    { label: '微博', value:5 ,disabled: false },
                    { label: '自定义', value:9 ,disabled: false },
                ]
                if(res.socialList.length > 0){
                    res.socialList.forEach(item=>{
                        let ii = {
                            name: item.name ,
                            value: item.type ,
                            val: item.code
                        }
                        this.editContactLianXiData.contactArr.push(ii)
                        typeList.forEach(typeI=>{
                            if(typeI.value === Number(item.type)){
                                typeI.disabled = true
                            }
                        })
                    })
                }
                this.editContactLianXiData.typeList = typeList
                this.setContactList()
                this.editContactLianXiData.picInfo = {
                    src : this.rootPath.fileUrl + res.visitCard  ,
                    filename :  res.visitCard  ,
                }





            }).catch(err=>{
                console.log('err=', err)
            })

        },
        addressStopBtn(address, indexArea){
            // 发票邮寄地址，收货地址，收货区域 的 停用 都这一个方法
            this.stopAdresstData.visible = true
            this.stopAdresstData.editObj = address
        },
        fpAdressUpdate(fpItem, fpIndex){
            console.log('fpItem=', fpItem)
            this.mailInfoData.visible = true
            this.mailInfoData.type = 'manageUpdate'
            this.mailInfoData.title = '修改发票邮寄信息'
            this.mailInfoData.editObj = fpItem
            this.mailInfoData.mailValidSta = true
            this.mailInfoData.address = fpItem.address
            this.mailInfoData.code = fpItem.postcode
            this.mailInfoData.contact = {
                name: fpItem.contact ,
                id: fpItem.customerContact ,
                mobile: fpItem.mobile
            }
        },
        fpAdd(){
            this.mailInfoData.visible = true
            this.mailInfoData.type = 'manageAdd'
            this.mailInfoData.title = '新增发票邮寄信息'
            this.mailInfoData.mailValidSta = true
            this.mailInfoData.address = ''
            this.mailInfoData.code = ''
            this.mailInfoData.contact = {}
        },
        shAreaItemUpdate(areaItem, indexArea){
            this.updateContactData.visible1 = true
            this.updateContactData.editObj = areaItem
            this.updateContactData.type = 'area'

        },
        shItemUpdateLog(shItem, type){
            // type: adress-收货地址修改记录，area-到货区域修改记录, fp-发票邮寄信息修改记录
            customerApi.getRecordAddressList({
                addressId: shItem.id
            }).then(res1=>{
                let list = res1.data.list || []
                let data = res1.data
                if(list.length === 0){
                    this.adressLogData.tip = '当前资料未经修改'
                    this.adressLogData.tipTime = '创建人：'+ data.createName + '&nbsp;&nbsp;' + new Date(data.createDate).format('yyyy-MM-dd hh:mm:ss')
                }else{
                    this.adressLogData.tip = '当前数据为第' + (list.length-1) + '次修改后的结果。'
                    this.adressLogData.tipTime = '修改人：'+ data.updateName + '&nbsp;&nbsp;' + new Date(data.updateDate).format('yyyy-MM-dd hh:mm:ss')
                }
                this.adressLogData.visible = true
                this.adressLogData.list = list
                this.adressLogData.type = type
                switch (type) {
                    case 'adress':
                        this.adressLogData.title = '收货地址修改记录'
                        break;
                    case 'area':
                        this.adressLogData.title = '到货区域修改记录'
                        break;
                    case 'fp':
                        this.adressLogData.title = '发票邮寄信息修改记录'
                        break;
                }

            }).catch(err=>{
                console.log('err=',err)
            })
        },
        hideAdressLogDetails(){
            this.adressLogDetailsData.visible = false
        },
        logDetail(logItem, indexLog){
            let id = logItem.id
            let frontid = 0
            if(indexLog !== 0){ frontid = this.adressLogData.list[indexLog-1].id  }
            if(this.adressLogData.type === 'fp'){
                customerApi.getRecordFpAddressDetails({ id: id , frontId: frontid }).then(res1=>{
                    let res = res1.data
                    this.adressLogDetailsData.visible = true
                    this.adressLogDetailsData.title = indexLog === 0 ? '原始信息' : '第' + indexLog + '次修改后'
                    let nowData = res.data.now
                    let frontData = res.data.front
                    if(nowData.operation == '2' || frontData == null){
                        if(nowData.operation == '2'){
                            var tip = nowData.enabled == '1'? '本地址于'+ new Date(nowData.enabledTime).format('yyyy-MM-dd hh:mm:ss') +'被'+ nowData.updateName +'启用。':'本地址于'+ new Date(nowData.enabledTime).format('yyyy-MM-dd hh:mm:ss') +'被'+nowData.updateName+'停用。'
                            this.adressLogDetailsData.shDisUse = tip
                        }else{
                            this.adressLogDetailsData.shDisUse = ''
                        }
                        this.adressLogDetailsData.shAddress = nowData.address || ''
                        this.adressLogDetailsData.shName = nowData.contact || ''
                        this.adressLogDetailsData.shNumber = nowData.mobile || ''
                        this.adressLogDetailsData.shCode = nowData.postcode || ''
                    }else{
                        this.adressLogDetailsData.shDisUse = ''
                        this.adressLogDetailsData.shAddress = this.compareD(frontData.address,nowData.address)
                        this.adressLogDetailsData.shName = this.compareD(frontData.contact,nowData.contact)
                        this.adressLogDetailsData.shNumber = this.compareD(frontData.mobile,nowData.mobile)
                        this.adressLogDetailsData.shCode = this.compareD(frontData.postcode, nowData.postcode)
                    }

                }).catch(err=>{
                    console.log('err=', err)
                })
            }else{
                customerApi.getRecordShAddressDetails({ id: id , frontId: frontid }).then(res1=>{
                    let res = res1.data
                    this.adressLogDetailsData.visible = true
                    this.adressLogDetailsData.title = indexLog === 0 ? '原始信息' : '第' + indexLog + '次修改后'
                    let nowData = res.data.now
                    let frontData = res.data.front
                    if(nowData.operation == '2' || frontData == null){
                        if(nowData.operation == '2'){
                            var tip = nowData.enabled == '1'? '本地址于'+ new Date(nowData.enabledTime).format('yyyy-MM-dd hh:mm:ss') +'被'+ nowData.updateName +'启用。':'本地址于'+ new Date(nowData.enabledTime).format('yyyy-MM-dd hh:mm:ss') +'被'+nowData.updateName+'停用。'
                            this.adressLogDetailsData.shDisUse = tip
                        }else{
                            this.adressLogDetailsData.shDisUse = ''
                        }
                        this.adressLogDetailsData.shAddress = nowData.address || ''
                        this.adressLogDetailsData.shName = nowData.contact || ''
                        this.adressLogDetailsData.shNumber = nowData.mobile || ''
                    }else{
                        this.adressLogDetailsData.shDisUse = ''
                        this.adressLogDetailsData.shAddress = this.compareD(frontData.address,nowData.address)
                        this.adressLogDetailsData.shName = this.compareD(frontData.contact,nowData.contact)
                        this.adressLogDetailsData.shNumber = this.compareD(frontData.mobile,nowData.mobile)
                    }

                }).catch(err=>{
                    console.log('err=', err)
                })
            }

        },
        compareD(front,now){
            let str = ''
            if(front === now){
                str = now || ''
            }else{
                str = '<span style="color:#ed5565;">' + (now || '') + '</span>'
            }
            console.log('返回', str)
            return str;
        },
        hideAdressLog(){
            this.adressLogData.visible = false
        },
        startAdress(adressItem){
            customerApi.startOrStopAddress({
                addressId: adressItem.id,
                enabled: 1
            }).then(res1=>{
                let res = res1.data
                if(res.status === 1){
                    this.$message.success('操作成功')
                    this.adressStopList(this.stopShAdressData.type)
                    this.manageSaler(this.updateCustomerData.editCustomer)
                }else{
                    this.$message.error('操作失败')
                }

            }).catch(err=>{
                console.log('err=',err)
            })
        },
        hideStopShAdress(){
            this.stopShAdressData.visible = false

        },
        hidestopShArea(){
            this.stopShAreaData.visible = false
        },
        adressStopList(type){ // 获取地址停用列表
            // type 1- adress  3-area 2-fp
            customerApi.getSuspendAddress({
                customerId: this.updateCustomerData.editCustomer.id,
                type: type
            }).then(res1=>{
                let res = res1.data
                this.stopShAdressData.list = res['addressList']|| []
                this.stopShAdressData.visible = true
                this.stopShAdressData.type = type
                this.stopShAdressData.typeStr = type === 1 ? 'adress' :  (type === 2 ? 'fp' : 'area')
                this.stopShAdressData.title = type === 1 ? '已被停用的收货信息' : '已被停用的到货区域'
            }).catch(err=>{
                console.log('err=', err)
            })
        },
        hideUpdateCt1(){
            this.updateContactData.visible1 = false
        },
        updateCtOK1(){
            if(this.updateContactData.type === 'area'){
                let areaItem = this.updateContactData.editObj
                this.receiveAreaInfoData.type = 'manageUpdate'
                this.receiveAreaInfoData.visible = true
                this.receiveAreaInfoData.title = '修改到货区域'
                this.receiveAreaInfoData.area = areaItem.address
                this.receiveAreaInfoData.areaArr = [ ]
                this.receiveAreaInfoData.requirements = areaItem.requirements
                this.receiveAreaInfoData.contact = {
                    name: areaItem.contact ,
                    mobile: areaItem.mobile ,
                    id: areaItem.customerContact ,
                }
                this.getTYRegionFun(areaItem.regionCode)
                this.updateContactData.visible1 = false
            }else if(this.updateContactData.type === 'address'){
                this.updateContactData.visible1 = false
                this.addressEditData.visible = true
                this.addressEditData.type = 'manageUpdate'
                this.addressEditData.title = '修改收货地址'
                this.addressEditData.address = this.updateContactData.editObj.address
                this.addressEditData.contact = {
                    name: this.updateContactData.editObj.contact ,
                    mobile: this.updateContactData.editObj.mobile ,
                    id: this.updateContactData.editObj.customerContact ,
                    post: this.updateContactData.editObj.post ,
                }
            }

        },

        hideShAdress(){
            this.stopAdresstData.visible = false
        },
        shAdressStopOK(){
            let params = {
                addressId: this.stopAdresstData.editObj.id,
                enabled: 0
            }
            customerApi.startOrStopAddress(params).then(res1=>{
                let res = res1.data
                let status = res.status;
                if (status === 1) {
                    this.manageSaler(this.updateCustomerData.editCustomer)
                    this.stopAdresstData.visible = false
                    this.$message.success('操作成功！')
                } else {
                    this.$message.error('操作失败！')
                }

            }).catch(err=>{
                console.log('err=', err)
            })
        },
        shItemUpdate(shItem, indexSh){
            this.updateContactData.visible1 = true
            this.updateContactData.editObj = shItem
            this.updateContactData.type = 'address'
        },
        stopCtOK(){
            customerApi.deleteCustomerContact({ contactId:this.stopContactData.editObj.id  }).then(res1=>{
                let data = res1.data
                let status = data.status;
                if (status === 1) {
                    this.manageSaler(this.updateCustomerData.editCustomer)
                    this.stopContactData.visible = false
                    this.$message.success("操作成功！")
                } else {
                    this.$message.error(data.message)
                }
            }).catch(err=>{
                console.log('err=', err)
            })

        },
        hideStopCt(){
            this.stopContactData.visible = false
        },
        contactDel(contactItem, contactIndex){
            this.stopContactData.visible = true
            this.stopContactData.editObj = contactItem

        },
        adressAdd(){
            this.addressEditData.visible = true
            this.addressEditData.type = 'manageAdd'
            this.addressEditData.title = '新增收货地址'
            this.addressEditData.address = ''
            this.addressEditData.contact = { name: '' }
        },
        shInfoEdit(){
            this.huoEdit()
            this.addressEditData.manageType = 'manageshInfoEdit'
        },
        hideStopC(){
            this.stopContractData.visible = false
        },
        hideEndC(){
            this.endContractData.visible = false
        },
        contractIEnds(){
            // 已到期的合同
            customerApi.listContractBase({ customer: this.updateCustomerData.editCustomer.id, type: 3 }).then(res1=>{
                let res = res1.data
                this.endContractData.visible = true
                this.endContractData.list = res.data.contractBaseList || []


            }).catch(err=>{
                console.log('err=',err)
            })
        },
        contractIStops(){
            customerApi.listContractBase({ customer: this.updateCustomerData.editCustomer.id, type: 2 }).then(res1=>{
                let res = res1.data
                this.stopContractData.visible = true
                this.stopContractData.list = res.data.contractBaseList || []

            }).catch(err=>{
                console.log('err=', err)
            })
        },
        contractIStopOK(){
            let type = this.contractStopData.type
            let params = { id: this.contractStopData.stopObj.id  }
            if(type === 'reStartContract'){ // 恢复
                params.type = 1
            }
            customerApi[type](params).then(res1=>{
                let res = res1.data
                let state = res.data.state
                if(state === 1){
                    this.$message.success("操作成功！")
                    this.getlistContractBase(this.updateCustomerData.editCustomer.id)
                    if(type === 'reStartContract'){
                        this.contractIStops()
                    }
                    this.contractStopData.visible = false
                } else if (state === 0) {
                    this.$message.error("请勿重复操作！")

                } else {
                    this.$message.error("操作失败！")

                }
            }).catch(err=>{
                console.log('err=', err)
            })

        },
        contractIStop(contractItem, type){
            this.contractStopData.visible = true
            this.contractStopData.type = type
            let str1 = '点击“确定”后，本合同将进入“已暂停/终止的合同”。 <br> 确定进行本操作吗？'
            let str2 = '点击“确定”后，本合同将回到有效合同列表。 <br> 确定进行本操作吗？'
            this.contractStopData.tipTxt = type === 'reStartContract' ? str2 : str1
            this.contractStopData.color = type === 'reStartContract' ? 'blue' : 'red'
            this.contractStopData.stopObj = contractItem
        },
        hideCS(){
            this.contractStopData.visible = false
        },
        hideCHS(){
            this.contractHisData.visible = false
        },
        xuScan(){
            this.contractBaseSignData.editContract = JSON.parse(JSON.stringify(this.contractInfoData.editContract))
            this.contractScanData.visible = false
            customerApi.contractBaseSignRecord({  primaryId: this.contractBaseSignData.editContract.primaryCont  }).then(res1=>{
                let res = res1.data
                this.contractBaseSignData.visible = true
                let list = res.data.list
                list.forEach((item, index) =>{
                    item.txt = index=== 0 ?'第1版（原始版本）':'第' + (index + 1) + '版（第' + index + '次续约后）'
                    item.create = item.createName + ' ' + (new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss'))
                })
                this.contractBaseSignData.list = res.data.list
            }).catch(err=>{
                this.$message.error('err')
            })
        },
        hideCBS(){
            this.contractBaseSignData.visible = false
            this.contractIScan(this.contractBaseSignData.editContract)
        },
        hisDetail(hisItem, type){
            if(type === 'contractHisData'){
                customerApi.contractBaseHisMes({ contractHisId: hisItem.id }).then(res1=>{
                    let res = res1.data
                    this.contractScanData.visible2 = true

                    let data = res.data
                    let contractBase = data.contracthistory // 合同信息（大多数据都从这里取出，尤其是合同id，要用这个关键字下的）
                    let listImage = data.listHisImage || [] // 合同的扫描件或照片
                    let listZS = data.listHisZS || [] // 专属商品
                    let listTY = data.listHisTY || [] // 通用型商品
                    let listHis = data.listHis || [] // 暂停恢复记录

                    listImage.forEach(img=>{
                        img.src = this.rootPath.fileUrl + img.uplaodPath
                    })
                    this.contractScanData.contractBase2 = contractBase
                    this.contractScanData.listImage2 = listImage
                    this.contractScanData.listTY2 = listTY
                    this.contractScanData.listZS2 = listZS
                    this.contractScanData.listHis2 = listHis

                }).catch(err=>{
                    console.log('err=',err)
                })
            }
        },
        hisScan(){
            //本版本合同的修改记录
            customerApi.contractBaseHistory({ id:this.contractInfoData.editContract.id }).then(res1=>{
                let res = res1.data
                let list = res.data.list || []
                list.forEach((item, index) =>{
                    item.txt = index === 0 ? '本版本合同的原始信息' : '第' + index + '次修改后'
                    item.create = index === 0 ? item.createName + ' ' + (new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')) : item.updateName + ' ' + (new Date(item.updateDate).format('yyyy-MM-dd hh:mm:ss'))
                })
                this.contractHisData.hisList = list
                let lastItem = list[list.length-1]
                this.contractHisData.txt = list.length === 1 ? '当前数据尚未修改' : `当前数据为本版本合同第${list.length-1}次修改后的结果。<span class="ty-right"> 修改时间：${lastItem.updateName + ' ' + new Date(lastItem.updateDate).format("yyyy-MM-dd hh:mm:ss")}</span>`
                this.contractHisData.visible = true


            }).catch(err=>{
                console.log('err=', err)
            })

        },
        gsScan(type, num){
            let list = []
            if(num === 2){
                list = type === 'zs' ? this.contractScanData.listZS2 : this.contractScanData.listTY2
            }else{
                list = type === 'zs' ? this.contractScanData.listZS : this.contractScanData.listTY
            }
            let title = type === 'zs' ? '本合同下的专属商品' : '本合同下的通用商品'
            let txt = type === 'zs' ? '本合同下的专属商品共有以下'+ list.length +'种' : '本合同下的通用商品共有以下'+ list.length +'种'
            this.contractGsScanData.gslist = list
            this.contractGsScanData.title = title
            this.contractGsScanData.txt = txt
            this.contractGsScanData.visible = true
        },
        gsContracts(gsItem){
            customerApi.listContractByCommodity({ id: gsItem.id }).then(res1=>{
                let res = res1.data
                let list = res.data.list
                list.forEach(ct=>{
                    ct.valid = (new Date(ct.validStart).format('yyyy-MM-dd')) + '至' + (new Date(ct.validEnd).format('yyyy-MM-dd'))
                    ct.sign = (new Date(ct.signTime).format('yyyy-MM-dd'))
                    ct.create = ct.createName+ ' ' + (new Date(ct.createDate).format('yyyy-MM-dd'))
                })
                this.contractByGsData.visible = true
                let goodArr = [gsItem.outerSn, gsItem.outerName, gsItem.model, gsItem.specifications, gsItem.unit].filter(item => item || item === 0)
                this.contractByGsData.gsInfo = goodArr.join(' / ')
                this.contractByGsData.cList = list


            }).catch(err=>{
                console.log('err=', err)
            })

        },
        hideCBG(){
            this.contractByGsData.visible = false
        },
        hideCSG(){
            this.contractGsScanData.visible = false
        },
        docScan(num){
            let path = num === 2 ? this.contractScanData.contractBase2.filePath : this.contractScanData.contractBase.filePath
            if(path){
                let allUrl = this.rootPath.ow365url + path
                window.open(allUrl, '_blank')
            }else{
                this.$message.error('未获取有效路径！')
            }

        },
        scanImgClose(img){
            img.opacity = 0
        },
        scanImg(img){
            img.opacity = 1
        },
        contractIScan(contractItem, contractIndex){
            this.contractBaseSignData.visible = false
            this.contractScanData.visible = true
            this.contractScanData.opacity = 0
            this.contractInfoData.editContract = contractItem
            customerApi.contractBaseMes({ id: contractItem.id }).then(res1=>{
                let res = res1.data
                let data = res.data
                let contractBase = data.contractBase // 合同信息（大多数据都从这里取出，尤其是合同id，要用这个关键字下的）
                let listImage = data.listImage || [] // 合同的扫描件或照片
                let listZS = data.listZS || [] // 专属商品
                let listTY = data.listTY || [] // 通用型商品
                let listHis = data.listHis || [] // 暂停恢复记录

                listImage.forEach(img=>{
                    img.src = this.rootPath.fileUrl + img.uplaodPath
                })
                this.contractScanData.contractBase = contractBase
                this.contractScanData.listImage = listImage
                this.contractScanData.listTY = listTY
                this.contractScanData.listZS = listZS
                this.contractScanData.listHis = listHis


            }).catch(err=>{
                console.log('err=',err)
            })
        },
        hideCScan(){
            this.contractScanData.visible = false
        },
        hideCScan2(){
            this.contractScanData.visible2 = false
        },
        contractIXu(contractItem, contractIndex, isEnd){
            // 续约
            this.contractInfoData.type = 'manageXu'
            this.contractInfoData.isEnd = isEnd
            this.contractInfoData.title = '合同续约'
            this.setContractBaseMes(contractItem, contractIndex)
        },
        contractIUpdate(contractItem, contractIndex){
            // 修改合同信息
            try{
                this.contractInfoData.type = 'manageUpdate'
                this.contractInfoData.title = '修改合同'
                this.setContractBaseMes(contractItem, contractIndex)
            }catch (e) {
                console.log('怎么错了', e)
            }

        },
        setContractBaseMes(contractItem, contractIndex){
            this.contractInfoData.editContractIndex = contractIndex
            this.contractInfoData.editContract = contractItem
            this.contractInfoData.cusName = this.updateCustomerData.editCustomer.name
            customerApi.contractBaseMes({ id: contractItem.id }).then(res1=>{
                let res = res1.data
                let data = res.data
                let contractBase = data.contractBase // 合同信息（大多数据都从这里取出，尤其是合同id，要用这个关键字下的）
                let listImage = data.listImage || [] // 合同的扫描件或照片
                let listZS = data.listZS || [] // 专属商品
                let listTY = data.listTY || [] // 通用型商品
                let listHis = data.listHis || [] // 暂停恢复记录

                this.contractInfoData.cNo = contractBase.sn
                this.contractInfoData.cSignDate = new Date(contractBase.signTime).format('yyyy-MM-dd')
                this.contractInfoData.cStartDate = new Date(contractBase.validStart).format('yyyy-MM-dd')
                this.contractInfoData.cEndDate = new Date(contractBase.validEnd).format('yyyy-MM-dd')
                let pics = []
                listImage.forEach(im=>{
                    pics.push({
                        filename: im.uplaodPath,
                        type: 1,
                        title: im.title,
                        id: im.id,
                        operation: 4
                    })
                })
                this.contractInfoData.scanPics = pics
                this.contractInfoData.scanFile = {
                    name: contractBase.fileName,
                    filename: contractBase.fileName,
                    originalFilename: contractBase.filePath
                }
                this.contractInfoData.selectGs = listTY
                this.contractInfoData.selectZSGs = listZS
                this.contractInfoData.cMemo = contractBase.memo

                this.contractInfoData.visible = true


            }).catch(err=>{
                console.log('err=',err)
            })
        },
        contractIAdd(){
            this.contractInfoData.visible = true
            this.contractInfoData.cusName = this.updateCustomerData.editCustomer.name
            this.contractInfoData.type = 'manageAdd'
            this.contractInfoData.title = '新增合同'
            this.contractInfoData.cNo = ''
            this.contractInfoData.cSignDate = ''
            this.contractInfoData.cStartDate = ''
            this.contractInfoData.cEndDate = ''
            this.contractInfoData.scanPics = []
            this.contractInfoData.scanFile = {}
            this.contractInfoData.selectGs = []
            this.contractInfoData.selectZSGs = []
            this.contractInfoData.cMemo = []

        },
        manageInvoiceOk(){
            let params = { id: this.updateCustomerData.editCustomer.id }
            params.invoiceName = this.manageInvoiceData.invoiceName
            params.invoiceAddress = this.manageInvoiceData.invoiceAddress
            params.telephone = this.manageInvoiceData.telephone
            params.bankName = this.manageInvoiceData.bankName
            params.bank_no = this.manageInvoiceData.bankNo
            params.taxpayerID = this.manageInvoiceData.taxpayerID
            params.invoiceRequire = this.manageInvoiceData.invoiceType.type

            customerApi.updatePdCustomerInvoice(params).then(res1=>{
                let res = res1.data
                let status = res.status
                if(status == '1'){
                    this.$message.success("操作成功！")
                    this.hideManageInvoiceFun()
                }else{
                    this.$message.error("操作失败！")
                }
            }).catch(err=>{
                console.log('err=',err)
            })
        },
        manageBaseOk(){
            let params = { id: this.updateCustomerData.editCustomer.id }
            if(this.specialOrgPart){
                params.mobile = this.manageBaseData.supervisorMobile
            }
            customerApi.getSaleCtrlsByMobile(params).then(res1=>{
                let res = res1.data
                let state = res.data
                if (state === 2) {
                    this.$message.error("系统不允许最高负责人同一手机号的再次录入。系统内已有您刚录入的手机号。请核对后重新录入！")
                } else if (state === 1) {
                    let params2 = { id: this.updateCustomerData.editCustomer.id }
                    params2.fullName = this.manageBaseData.fullName
                    params2.code = this.manageBaseData.cuscoding
                    params2.address = this.manageBaseData.address1
                    params2.name = this.manageBaseData.name
                    params2.supervisorName = this.manageBaseData.supervisorName
                    if(this.specialOrgPart){
                        params2.supervisorMobile = this.manageBaseData.supervisorMobile
                        params2.firstContactAddress = this.manageBaseData.firstContactAddress
                    }
                    params2.infoSource = this.manageBaseData.infoSource
                    params2.memo = this.manageBaseData.memo
                    params2.initialType = this.manageBaseData.firstTime
                    params2.initialPeriod = this.manageBaseData.initialPeriod
                    if(this.manageBaseData.firstContactTime ){
                        params2.firstContactTime = new Date(this.manageBaseData.firstContactTime ).format('yyyy/MM/dd')    // 2024/12/17

                    }
                    let qImages = []
                    this.manageBaseData.quanImg.forEach(qImg=>{
                        qImages.push({ normal:qImg.filename  })
                    })
                    params2.qImages = JSON.stringify(qImages); // 全景
                    let pImages = []
                    this.manageBaseData.chanImg.forEach(qImg=>{
                        pImages.push({ normal:qImg.filename  })
                    })
                    params2.pImages = JSON.stringify(pImages); // 产品

                    if(this.specialOrgPart){
                        params.supervisorName = this.manageBaseData.supervisorName
                        params.supervisorMobile = this.manageBaseData.supervisorMobile
                    }
                    customerApi.updatePdCustomerBase(params2).then(res1=>{
                        let res = res1.data
                        let status = res.status
                        if(status == 0){
                            this.$message.error("操作失败！")
                        }else { // 修改成功
                            this.$message.success("修改成功！")

                            this.manageBaseData.visible = false
                            // 更新管理页
                            this.updateCustomerData.editCustomer.name = params2.name
                            this.manageSaler(this.updateCustomerData.editCustomer)
                            // 更新 主页
                            this.getDemoListFun(this.mainData.params)

                        }

                    }).catch(err=>{
                        console.log('err=', err)
                    })

                }

            }).catch(err=>{
                console.log('err=', err)
            })

        },
        hideManageBase(){
            this.manageBaseData.visible = false
        },
        hideManageInvoiceFun(){
            this.manageInvoiceData.visible = false
        },
        invoiceManageUpdate(){
            // 开票信息修改
            customerApi.getPdCustomerOne({ id: this.updateCustomerData.editCustomer.id }).then(res1=>{
                let res = res1.data.data
                console.log('开票信息修改', res)
                this.manageInvoiceData.invoiceType = {
                    txt: this.formatInviceRequire(res.invoiceRequire),
                    type:res.invoiceRequire
                }
                this.manageInvoiceData.invoiceName = res.invoiceName
                this.manageInvoiceData.invoiceAddress = res.invoiceAddress
                this.manageInvoiceData.telephone = res.telephone
                this.manageInvoiceData.bankName = res.bankName
                this.manageInvoiceData.bankNo = res.bank_no
                this.manageInvoiceData.taxpayerID = res.taxpayerID
                this.manageInvoiceData.visible = true


            }).catch(err=>{
                console.log('err=', err)
            })
        },
        baseUpdate(){
            // 基本信息 修改
            customerApi.getPdCustomerOne({ id: this.updateCustomerData.editCustomer.id }).then(res1=>{
                let res = res1.data.data
                this.manageBaseData.visible = true
                this.manageBaseData.fullName = res.fullName
                this.manageBaseData.cuscoding = res.code
                this.manageBaseData.address1 = res.address
                this.manageBaseData.supervisorMobile = res.supervisorMobile
                this.manageBaseData.supervisorName = res.supervisorName
                this.manageBaseData.name = res.name
                let quanList = [], chanList = []
                res.qImages.forEach(qImg=>{
                    quanList.push({
                        src: this.rootPath.fileUrl + qImg.normal,
                        filename:qImg.normal
                    })
                })
                res.pImages.forEach(qImg=>{
                    chanList.push({
                        src: this.rootPath.fileUrl + qImg.normal,
                        filename:qImg.normal
                    })
                })
                this.manageBaseData.quanImg = quanList
                this.manageBaseData.chanImg = chanList
                this.manageBaseData.firstContactTime = new Date(res.firstContactTime).format('yyyy/MM/dd')
                this.manageBaseData.firstContactAddress = res.firstContactAddress
                this.manageBaseData.infoSource = res.infoSource
                this.manageBaseData.memo = res.memo
                this.manageBaseData.initialPeriod = res.initialPeriod
                this.manageBaseData.firstTime = Number(res.initialType)


            }).catch(err=>{
                console.log('err=', err)
            })
        },
        hideSuspendRecord(){
            this.suspendRecordData.visible = false
        },
        getRecordList(){
            let customerId = this.seeAccountData.info.id
            this.getRecordBaseListFun(customerId)
        },
        getRecordBaseListFun(customerId){
            customerApi.getRecordBaseList({ customerId: customerId }).then(res1=>{
                let res = res1.data
                let status = res.status;
                if (status == 1) {
                    this.setRecordsList(res, 'base');
                } else {
                    this.$message.error("查看失败！")
                }

            }).catch(err=>{
                console.log('err=',err)
            })
        },
        setRecordsList(data,type){
            this.updateRecordsData.visible = true
            this.updateRecordsData.type = type
            switch (type){
                case "base":
                    this.updateRecordsData.title = '基本信息修改记录'
                    break;
                case "invoice":
                    this.updateRecordsData.title = '开票信息修改记录'
                    break;

                default:
                    console.log('没找到type=', type)
            }
            this.updateRecordsData.list = []
            let list = data.list
            if(list.length > 0){
                this.updateRecordsData.recordTip = '当前数据为第' + (list.length -1) + '次修改后的结果。'
                this.updateRecordsData.recordEditer = "修改时间：" + data.updateName + '&nbsp;&nbsp;' + ( new Date(data.updateDate).format('yyyy-MM-dd hh:mm:ss') )
                let recordList = []
                list.forEach((item, index)=>{
                    let iitem={
                        ...item,
                        infoTxt: index === 0 ? '原始信息' : `第${ index }次修改后` ,
                        handler: index === 0 ? `${ item.createName } ${ new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')  }` : `${ item.updateName } ${ new Date(item.updateDate).format('yyyy-MM-dd hh:mm:ss')  }`  ,
                    }
                    recordList.push(iitem)
                })
                this.updateRecordsData.list = recordList
            }else{
                this.updateRecordsData.recordTip = "当前资料未经修改"
                this.updateRecordsData.recordEditer = "创建人：" + data.createName + '&nbsp;&nbsp;' + ( new Date(data.createDate).format('yyyy-MM-dd hh:mm:ss') )

            }

        },
        hideUpdateRecords(){
            this.updateRecordsData.visible = false
        },
        hideManageBaseLogInfo(){
            this.manageBaseLogData.visible = false
        },
        cus_recordDetail(logItem, indexLog){
            let params = {  id: logItem.id ,  frontId: 0  }
            if(indexLog !== 0){ params.frontId = this.updateRecordsData.list[indexLog-1].id  }
            let type = this.updateRecordsData.type
            switch (type){
                case "base":
                    this.manageBaseLogData.title = logItem.infoTxt
                    customerApi.getRecordBaseDetails( params ).then(res1=>{
                        let res = res1.data
                        let frontData = res.data.front
                        let nowData = res.data.now

                        nowData.qImages.forEach(imgq =>{
                            imgq.src = this.rootPath.fileUrl + imgq.filename
                        })
                        nowData.pImages.forEach(imgp =>{
                            imgp.src = this.rootPath.fileUrl + imgp.filename
                        })
                        this.manageBaseLogData.quanImg = nowData.qImages
                        this.manageBaseLogData.chanImg = nowData.pImages
                        let hasBuyStr = ''
                        let hasContractStr = ''
                        let createDate = new Date(nowData.createDate).format('yyyy-MM-dd hh:mm:ss')
                        let initialType = nowData.initialType;
                        let initialPeriod = nowData.initialPeriod;
                        let date = '';
                        if (initialType === null) {
                            hasBuyStr = '<div class="ty-alert ty-alert-error">'+createDate + '之前该客户尚未购买过本公司的商品</div>'
                        }
                        else {
                            if(initialType === '1'){
                                var month = initialPeriod.substr(4,2);
                                if(month.slice(0,1) == '0'){
                                    month = month.substr(1,1);
                                }
                                date = initialPeriod.substr(0,4) + '年'  + month  + '月';
                                hasBuyStr = '<div class="ty-alert ty-alert-success">'+date+'该客户首次购买过本公司的商品</div>'
                            }else if(initialType === '2'){
                                hasBuyStr = '<div class="ty-alert ty-alert-success">去年该客户首次购买过本公司的商品</div>'
                            }else if(initialType === '3'){
                                hasBuyStr = '<div class="ty-alert ty-alert-success">'+ initialPeriod +'年之前该客户首次购买过本公司的商品</div>'
                            }else{
                                hasBuyStr = '<div class="ty-alert ty-alert-success">去年之前该客户首次购买过本公司的商品</div>'
                            }
                            // 处理是否签合同
                            var hasContract = nowData.hasContract
                            if (hasContract === '1') {
                                var sn = []
                                var contractSn = nowData.contractSn
                                var expiresTime = nowData.expiresTime
                                var contractTime = nowData.contractTime

                                if (contractTime) sn.push('签署日期为'+new Date(contractTime).format("yyyy年MM月dd日"))
                                if (expiresTime) sn.push('有效期至'+new Date(expiresTime).format("yyyy年MM月dd日"))
                                if (contractSn) sn.push('合同编号为'+contractSn)
                                if (sn.length === 0) {
                                    hasContractStr += '<div class="ty-alert ty-alert-success">与该客户有处于有效期内的合同</div>'
                                } else {
                                    hasContractStr += '<div class="ty-alert ty-alert-success">与该客户有'+sn.join("、")+'的合同</div>'
                                }
                            } else {
                                hasContractStr += '<div class="ty-alert ty-alert-error">目前与该客户无处于有效期内的合同</div>'
                            }
                        }

                        if(frontData == null){
                            this.manageBaseLogData.info  = nowData
                            this.manageBaseLogData.hasBuyStr = hasBuyStr
                            this.manageBaseLogData.hasContractStr = hasContractStr
                        }else{
                            this.manageBaseLogData.info  = {
                                address: this.compareD(frontData.address,nowData.address) ,
                                name: this.compareD(frontData.name,nowData.name) ,
                                fullName: this.compareD(frontData.fullName,nowData.fullName) ,
                                code: this.compareD(frontData.code,nowData.code) ,
                                supervisorName: this.compareD(frontData.supervisorName,nowData.supervisorName) ,
                                supervisorMobile: this.compareD(frontData.supervisorMobile,nowData.supervisorMobile) ,
                                infoSource: this.compareD(frontData.infoSource,nowData.infoSource) ,
                                memo: this.compareD(frontData.memo,nowData.memo) ,
                                firstContactAddress: this.compareD(frontData.firstContactAddress,nowData.firstContactAddress) ,
                                firstContactTime: this.compareD((new Date(frontData.firstContactTime).format('yyyy-MM-dd')),(new Date(nowData.firstContactTime).format('yyyy-MM-dd'))) ,
                            }

                            let hasBuyStr2 = ''
                            let hasContractStr2 = ''
                            let createDate2 = new Date(frontData.createDate).format('yyyy-MM-dd hh:mm:ss')
                            let initialType2 = frontData.initialType;
                            let initialPeriod2 = frontData.initialPeriod;
                            let date2 = '';
                            if (initialType2 === null) {
                                hasBuyStr2 = '<div class="ty-alert ty-alert-error">'+createDate2 + '之前该客户尚未购买过本公司的商品</div>'
                            }
                            else {
                                if(initialType2 === '1'){
                                    var month2 = initialPeriod2.substr(4,2);
                                    if(month2.slice(0,1) == '0'){
                                        month2 = month2.substr(1,1);
                                    }
                                    date2 = initialPeriod2.substr(0,4) + '年'  + month2  + '月';
                                    hasBuyStr2 = '<div class="ty-alert ty-alert-success">'+date2+'该客户首次购买过本公司的商品</div>'
                                }else if(initialType2 === '2'){
                                    hasBuyStr2 = '<div class="ty-alert ty-alert-success">去年该客户首次购买过本公司的商品</div>'
                                }else if(initialType2 === '3'){
                                    hasBuyStr2 = '<div class="ty-alert ty-alert-success">'+ initialPeriod2 +'年之前该客户首次购买过本公司的商品</div>'
                                }else{
                                    hasBuyStr2 = '<div class="ty-alert ty-alert-success">去年之前该客户首次购买过本公司的商品</div>'
                                }
                                // 处理是否签合同
                                var hasContract2 = frontData.hasContract
                                if (hasContract2 === '1') {
                                    var sn2 = []
                                    var contractSn2 = frontData.contractSn
                                    var expiresTime2 = frontData.expiresTime
                                    var contractTime2 = frontData.contractTime

                                    if (contractTime2) sn.push('签署日期为'+new Date(contractTime2).format("yyyy年MM月dd日"))
                                    if (expiresTime2) sn.push('有效期至'+new Date(expiresTime2).format("yyyy年MM月dd日"))
                                    if (contractSn2) sn.push('合同编号为'+contractSn2)
                                    if (sn2.length === 0) {
                                        hasContractStr2 += '<div class="ty-alert ty-alert-success">与该客户有处于有效期内的合同</div>'
                                    } else {
                                        hasContractStr2 += '<div class="ty-alert ty-alert-success">与该客户有'+sn2.join("、")+'的合同</div>'
                                    }
                                } else {
                                    hasContractStr2 += '<div class="ty-alert ty-alert-error">目前与该客户无处于有效期内的合同</div>'
                                }
                            }

                            this.manageBaseLogData.hasBuyStr = this.compareD(hasBuyStr2 , hasBuyStr)
                            this.manageBaseLogData.hasContractStr = this.compareD(hasContractStr2 , hasContractStr)
                        }
                        this.manageBaseLogData.visible = true
                    }).catch(err=>{
                        this.$message.error(err)
                    })
                    break;
                case "invoice":
                    this.manageInvoiceLogData.title = logItem.infoTxt
                    customerApi.getRecordInvoiceDetails( params ).then(res1=>{
                        let res = res1.data
                        let frontData = res.data.front
                        let nowData = res.data.now
                        if(frontData == null){
                            this.manageInvoiceLogData.info = nowData
                        }else{
                            this.manageInvoiceLogData.info = {
                                invoiceName:this.compareD(frontData.invoiceName,nowData.invoiceName) ,
                                telephone:this.compareD(frontData.telephone,nowData.telephone) ,
                                invoiceAddress:this.compareD(frontData.invoiceAddress,nowData.invoiceAddress) ,
                                bankName:this.compareD(frontData.bankName,nowData.bankName) ,
                                bankNo:this.compareD(frontData.bankNo,nowData.bankNo) ,
                                taxpayerID:this.compareD(frontData.taxpayerID,nowData.taxpayerID)
                            }
                        }
                        this.manageInvoiceLogData.visible = true
                    }).catch(err=>{
                        this.$message.error(err)
                    })
                    break;

                default:
                    console.log('没找到type=', type)
            }

        },
        hideManageInvoiceLogFun(){
            this.manageInvoiceLogData.visible = false
        },
        getSuspendRecordList(){
            let customerId = this.seeAccountData.info.id
            customerApi.getSuspendOperationList({ customerId: customerId }).then(res1=>{
                let res = res1.data
                let data = res.data
                this.suspendRecordData.visible = true
                let list = []
                data.forEach(item=>{
                    let iit = {
                        handleTxt: item.isSuspend === '1'?'暂停合作':'恢复合作',
                        handler: item.updateName + ' ' + (new Date(item.updateDate).format('yyyy-MM-dd hh:mm:ss'))
                    }
                    list.push(iit)
                })
                this.suspendRecordData.list = list

            }).catch(err=>{
                console.log('err=', err)
            })
        },
        setFirstTimeN(){
            if(this.addData.base.initialPeriod){
                this.addData.base.firstTimeN = false
            }
        },
        toggefirstTimeN(type){
            if(type === 'manage'){ // 管理客户
                this.manageBaseData.firstTimeN = !this.manageBaseData.firstTimeN
                if(this.manageBaseData.firstTimeN){
                    this.manageBaseData.initialPeriod = ''
                }
            }else{ // 新增客户
                this.addData.base.firstTimeN = !this.addData.base.firstTimeN
                if(this.addData.base.firstTimeN){
                    this.addData.base.initialPeriod = ''
                }
            }
        },
        toggefirstTime(val, type){
            if(type === 'manage'){// 客户管理
                if(this.manageBaseData.firstTime === val){
                    this.manageBaseData.firstTime = ''
                }else{
                    this.manageBaseData.firstTime = val
                    this.manageBaseData.initialPeriod = ''
                    this.manageBaseData.firstTimeN = false
                }
            }else{ // 新增客户
                if(this.addData.base.firstTime === val){
                    this.addData.base.firstTime = ''
                }else{
                    this.addData.base.firstTime = val
                    this.addData.base.initialPeriod = ''
                    this.addData.base.firstTimeN = false
                }
            }

        },
        addDataOk(selectPro){
            if(this.addDataOkBtn){
                let params = {
                }
                params.fullName = this.addData.base.fullName
                params.code = this.addData.base.cuscoding
                params.address = this.addData.base.address1
                params.name = this.addData.base.name || ''
                if(this.addData.base.firstContactTime){
                    params.firstContactTime = (new Date(this.addData.base.firstContactTime).format('yyyy/MM/dd'))   // 首次接触时间
                }
                params.firstContactAddress = this.addData.base.firstContactAddress
                params.infoSource = this.addData.base.infoSource
                params.memo = this.addData.base.memo
                // 开票
                params.invoiceName = this.addData.invoice.invoiceName
                params.invoiceAddress = this.addData.invoice.invoiceAddress
                params.telephone = this.addData.invoice.telephone
                params.bankName = this.addData.invoice.bankName
                params.bankNo = this.addData.invoice.bankNo
                params.taxpayerID = this.addData.invoice.taxpayerID
                params.initialType = this.addData.base.firstTime || null // 成为公司客户的时间
                params.initialPeriod = this.addData.base.initialPeriod // 月份

                if(this.specialOrgPart){
                    params.supervisorName = this.addData.base.supervisorName
                    params.supervisorMobile = this.addData.base.supervisorMobile
                }

                let qImages = []
                this.addData.base.quanImg.forEach(qImg=>{
                    qImages.push({ normal:qImg.filename  })
                })
                params.qImages = JSON.stringify(qImages); // 全景
                let pImages = []
                this.addData.base.chanImg.forEach(qImg=>{
                    pImages.push({ normal:qImg.filename  })
                })
                params.pImages = JSON.stringify(pImages); // 产品

                let contractList = []; //合同
                this.addData.contract.tableData.forEach(contract=>{
                    let infoc = contract.info
                    let imgs = []
                    infoc.scanPics.forEach((im, index) => {
                        imgs.push({
                            uplaodPath: im.filename,
                            order: index ,
                            type: 1,
                            title: im.originalFilename,
                            operation: 1
                        })
                    })
                    let productTYList= []
                    infoc.selectGs.forEach((gs, index)=>{
                        productTYList.push({
                            commodity: gs.id
                        })
                    })
                    let item = {
                        sn: infoc.cNo,
                        contractSignTime: infoc.cSignDate,
                        contractStartTime:infoc.cStartDate,
                        contractEndTime:infoc.cEndDate,
                        fileName: infoc.scanFile.originalFilename,
                        filePath: infoc.scanFile.filename ,
                        memo: infoc.cMemo,
                        contractBaseImages: imgs,
                        productTYList: JSON.stringify(productTYList)
                    }
                    contractList.push(item)
                })
                params.contractBaseList = JSON.stringify(contractList)
                params.deliveryType = this.addData.huo.info.type // 收货类型：1-只提供服务，不提供实体货物;2-提供实体货物
                let addressList = [], areaList=[]
                if(params.deliveryType === 2){
                    params.selfState = ''
                    if(this.addData.huo.info.addressList && this.addData.huo.info.addressList.length > 0){
                        this.addData.huo.info.addressList.forEach(addressI=>{
                            let item = {
                                type : 1,
                                address : addressI.addr,
                                contact : addressI.name,
                                mobile: addressI.tel,
                                customerContact: addressI.contact.id
                            }
                            addressList.push(item)
                        })
                    }
                    if(this.addData.huo.info.areaList && this.addData.huo.info.areaList.length > 0){
                        this.addData.huo.info.areaList.forEach(areaI=>{
                            let codeArr = []
                            areaI.areaArr.forEach(ar=>{
                                codeArr.push(ar.code)
                            })
                            let path = codeArr.join()
                            let regionCode = areaI.areaArr[2].code
                            let item = {
                                type : 3,
                                address : areaI.addr,
                                contact : areaI.name,
                                mobile: areaI.tel,
                                customerContact: areaI.contact.id,
                                path: path,
                                regionCode:regionCode,
                                requirements: areaI.requirements,
                            }
                            areaList.push(item)
                        })
                    }
                    params.selfState = this.addData.huo.info.status // 自提状态 1 开启 0 为开启
                }
                params.shAddressList = JSON.stringify([...addressList, ...areaList] ) // 收货地址
                let mailList = []
                this.addData.mail.tableData.forEach(mailI=>{
                    let item={
                        number: this.getFive(),
                        address: mailI.address ,
                        contact:mailI.recivePerson ,
                        postcode:mailI.mailNo ,
                        mobile:mailI.tel ,
                        contactInfoNumber:mailI.contact.id ,
                    }
                    mailList.push(item)
                })
                params.fpAddressList = JSON.stringify(mailList)
                let contactsList = []
                this.addData.contactLianxi.tableData.forEach(lxI=>{
                    let socialList = []
                    lxI.info.contactArr.forEach(cc=>{
                        socialList.push({
                            code: cc.name,
                            type: cc.value,
                            name: cc.val,
                        })
                    })
                    let item = {
                        tags: lxI.info.tags,
                        name: lxI.name,
                        post: lxI.post,
                        mobile: lxI.name,
                        visitCard: lxI.info.picInfo.filename ,
                        socialList: socialList,
                        id: lxI.id,
                        groupUuid: lxI.name,
                        number: lxI.id,
                    }
                    contactsList.push(item)
                })
                params.contactsList = JSON.stringify(contactsList)
                params.invoiceRequire = this.addData.invoice.invoiceType  // 是否需要开票
                customerApi.addPdCustomer(params).then(res1=>{
                    let res = res1.data
                    console.log('res=', res)
                    if(res.status === 1){
                        this.$message.success('新增成功')
                        this.getDemoListFun(this.mainData.params)
                        this.addData.visible = false
                        if(selectPro === 'selectPro'){
                            this.orgListData.editObj = res
                            this.addOrg(res.supervisorName , res.supervisorMobile)
                        }

                    }else if(res.status === 2){
                        this.$message.error('系统不允许最高负责人同一手机号的再次录入。系统内已有您刚录入的手机号。请核对后重新录入！')
                    }else{
                        this.$message.error(res.msg)
                    }


                }).catch(err=>{
                    console.log('err=', err)
                })


            }
        },
        delContact(item, type){

        },
        updateContact(item, type){
            let info = item['info'+ type]
            let index = item['index'+ type]
            this.editContactLianXiData.visible = true
            this.editContactLianXiData.editIndex = index
            this.editContactLianXiData.type = 'update'
            this.editContactLianXiData.title = '修改客户联系人'
            this.editContactLianXiData.showOptions = false
            this.editContactLianXiData.loading =  false
            this.editContactLianXiData.loadingBtn = '点击此处上传名片'
            this.editContactLianXiData.name = info.name
            this.editContactLianXiData.postName = info.postName
            this.editContactLianXiData.tel = info.tel
            this.editContactLianXiData.typeStr = info.typeStr
            this.editContactLianXiData.tag = info.tag
            this.editContactLianXiData.newId = info.newId
            this.editContactLianXiData.contactArr = info.contactArr
            this.editContactLianXiData.picInfo = info.picInfo
            this.editContactLianXiData.typeList = info.typeList
            this.editContactLianXiData.contactList = info.contactList
        },
        chargeCode(){
            this.mailInfoData.mailValidSta = validatePostCode(this.mailInfoData.code)
        },
        mailInfoOk(){
            if(this.mailInfoOkBtn){
                if(this.rootCusBtn === 'add'){ // 新增客户
                    let item = {
                        address: this.mailInfoData.address ,
                        recivePerson: this.mailInfoData.contact.name ,
                        mailNo: this.mailInfoData.code ,
                        tel:this.mailInfoData.contact.info.tel ,
                        contact: this.mailInfoData.contact,
                    }
                    if(this.mailInfoData.type === 'add'){
                        this.addData.mail.tableData.push(item)
                    }else if(this.mailInfoData.type === 'update'){
                        this.addData.mail.tableData[this.mailInfoData.editMailIndex] = item
                    }
                    this.mailInfoData.visible = false
                }else if(this.rootCusBtn === 'update'){ // 修改客户
                    console.log('this.mailInfoData=', this.mailInfoData)
                    if(this.mailInfoData.type === 'manageAdd'){
                        let params = {  customerId: this.updateCustomerData.editCustomer.id }
                        let fpAddress = {
                            "address": this.mailInfoData.address  ,
                            "contact": this.mailInfoData.contact.name ,
                            "postcode": this.mailInfoData.code ,
                            "customerContact": this.mailInfoData.contact.id ,
                            "mobile": this.mailInfoData.contact.mobile
                        }
                        params.fpAddress = JSON.stringify(fpAddress)
                        customerApi.addCustomerAddress(params).then(res1=>{
                            let res = res1.data
                            let status = res.status
                            if (status === 1) {
                                this.manageSaler(this.updateCustomerData.editCustomer)
                                this.mailInfoData.visible = false
                                this.$message.success('新增成功！')
                            } else {
                                this.$message.error('新增失败！')
                            }
                        }).catch(err=>{
                            console.log('err=',err)
                        })
                    }else if(this.mailInfoData.type === 'manageUpdate'){
                        let params = {
                            id: this.mailInfoData.editObj.id,
                            address: this.mailInfoData.address ,
                            contact: this.mailInfoData.contact.name ,
                            postcode: this.mailInfoData.code ,
                            customerContact: this.mailInfoData.contact.id ,
                            mobile: this.mailInfoData.contact.mobile
                        }
                        customerApi.updateCustomerAddress(params).then(res1=>{
                            let res = res1.data
                            if(res.status == 1){
                                this.$message.success('操作成功')
                                this.manageSaler(this.updateCustomerData.editCustomer)
                                this.mailInfoData.visible = false
                            }else{
                                this.$message.error('操作失败')
                            }
                        }).catch(err=>{
                            console.log('err=',err)
                        })
                    }
                }
            }
        },
        delMail(item, index){
            this.addData.mail.tableData.splice(index,1)
        },
        updateMail(item, index){
            this.mailInfoData.visible = true
            this.mailInfoData.type = 'update'
            this.mailInfoData.title = '修改发票邮寄信息'
            this.mailInfoData.editMailIndex = index
            this.mailInfoData.mailValidSta = true
            this.mailInfoData.address = item.address
            this.mailInfoData.code = item.mailNo
            this.mailInfoData.contact = item.contact
        },
        addMail(){
            this.mailInfoData.visible = true
            this.mailInfoData.type = 'add'
            this.mailInfoData.title = '新增发票邮寄信息'
            this.mailInfoData.mailValidSta = true
            this.mailInfoData.address = ''
            this.mailInfoData.code = ''
            this.mailInfoData.contact = {}
        },
        hideMail(){
            this.mailInfoData.visible = false
        },
        contractGoodsOk(){
            let selectGS = []
            this.contractGoodsData.gslist.forEach(gs=>{
                if(gs.selected){
                    selectGS.push(gs)
                }
            })
            if(selectGS.length === 0) {
                this.$message.error('请先选择商品')
            }else{
                if(this.contractGoodsData.type === 'add'){
                    // 新增
                    if(this.contractGoodsData.gsType === 'zs'){
                        this.contractInfoData.selectZSGs = selectGS
                    }else {
                        this.contractInfoData.selectGs = selectGS
                    }
                }
                else if(this.contractGoodsData.type === 'del'){
                    //  移除的
                    let newSelectGs = []
                    let oldSelectGs = this.contractGoodsData.gsType === 'zs' ? this.contractInfoData.selectZSGs : this.contractInfoData.selectGs
                    oldSelectGs.forEach(selectItem=>{
                        let hav = false
                        selectGS.forEach(delItem=>{
                            if(delItem.id === selectItem.id){
                                hav = true
                            }
                        })
                        if(!hav){
                            newSelectGs.push(selectItem)
                        }
                    })
                    if(this.contractGoodsData.gsType === 'zs'){
                        this.contractInfoData.selectZSGs = newSelectGs
                    }else {
                        this.contractInfoData.selectGs = newSelectGs
                    }
                }
                this.contractGoodsData.visible = false
            }
        },
        toggleSelect(gs){
            gs.selected = !gs.selected
        },
        addTyGs(gsType){
            this.contractGoodsData.type = 'add'
            this.contractGoodsData.gsType = gsType
            this.contractGoodsData.title = gsType === 'zs' ? '向本合同添加专属商品' : '向本合同添加通用型商品'
            this.contractGoodsData.visible = true
            let oralGsList = gsType === 'zs' ?  this.contractInfoData.oralZSGsList : this.contractInfoData.oralGsList
            if(oralGsList){
                let newGss = JSON.parse(JSON.stringify(oralGsList))
                newGss.forEach(gs=>{
                    gs.selected = false
                    let selectGs = gsType === 'zs' ?  this.contractInfoData.selectZSGs : this.contractInfoData.selectGs
                    selectGs.forEach(selectGs=>{
                        if(selectGs.id === gs.id){
                            gs.selected = true
                        }
                    })
                })
                console.log('newGss=', newGss)
                this.contractGoodsData.gslist = newGss
            }else{
                let param = {}
                if(gsType === 'zs'){
                    param = { customer: this.updateCustomerData.editCustomer.id }
                }
                customerApi.insertCommodityByTypeForContract(param).then(res1=>{
                    let res = res1.data
                    let data = res.data
                    let list = gsType === 'zs' ? data.productListZS : data.productListTY
                    this.contractGoodsData.gslist = list
                    this.contractInfoData.oralGsList = data.productListTY
                    this.contractInfoData.oralZSGsList = data.productListZS

                }).catch(err=>{
                    console.log('err=', err)
                })
            }
        },
        removeTyGs(gsType){
            this.contractGoodsData.type = 'del'
            this.contractGoodsData.gsType = gsType
            this.contractGoodsData.title = gsType === 'zs' ? '从本合同移出专属商品' : '从本合同移出通用型商品'
            let newList = []
            let selectGs = gsType === 'zs' ?  this.contractInfoData.selectZSGs : this.contractInfoData.selectGs
            selectGs.forEach(selectGs=>{
                selectGs.selected = false
                newList.push(selectGs)
            })
            this.contractGoodsData.gslist = newList
            this.contractGoodsData.visible = true
        },
        hideCG(){
            this.contractGoodsData.visible = false
        },
        delSF(){
            this.contractInfoData.scanFile = {}
        },
        delSP(indexNumber){
            this.contractInfoData.scanPics.splice(indexNumber, 1)
        },
        fileSuccessContract2(file, files, ofile, totalFiles) {
            console.log('上传之后=',file)
            let imgItem = {
                src: this.rootPath.fileUrl + file.filename,
                name:file.originalFilename,
                ...file
            }
            imgItem.originalFilename = file.filename
            imgItem.filename = file.originalFilename
            this.contractInfoData.scanFile = imgItem
        },
        beforeUploadContract(file){
            // 上传合同扫描件之前
            console.log('上传之前=',file)
            if(this.contractInfoData.scanPics.length >= 9){
                this.$message.error('共可上传9张!')
                return false
            }
        },
        fileSuccessContract(file, files, ofile, totalFiles){
            // 上传合同扫描件成功
            console.log('上传之后=',file)
            if(this.contractInfoData.scanPics.length >= 9){
                this.$message.error('共可上传9张!')
                return false
            }
            let imgItem = {
                src: this.rootPath.fileUrl + file.filename,
                name:file.originalFilename,
                ...file
            }
            this.contractInfoData.scanPics.push(imgItem)
            console.log(' this.contractInfoData.scanPics=',  this.contractInfoData.scanPics)
        },
        contractInfoOk(){
            let info = {
                cNo: this.contractInfoData.cNo,
                cSignDate: this.contractInfoData.cSignDate,
                cStartDate: this.contractInfoData.cStartDate,
                cEndDate: this.contractInfoData.cEndDate,
                scanPics: this.contractInfoData.scanPics,
                scanFile: this.contractInfoData.scanFile,
                selectGs: this.contractInfoData.selectGs,
                selectZSGs: this.contractInfoData.selectZSGs,
                cMemo: this.contractInfoData.cMemo,
                oralGsList: this.contractInfoData.oralGsList ,
            }
            if(!info.cNo){ this.$message.error('请录入合同编号！'); return false;}
            if(!info.cStartDate){ this.$message.error('请选择合同的有效期！'); return false; }
            if(!info.cEndDate){ this.$message.error('请选择合同的有效期！'); return false; }

            if(this.contractInfoData.type === 'manageUpdate' || this.contractInfoData.type === 'manageAdd' || this.contractInfoData.type === 'manageXu'){
                let params = {
                    customer: this.updateCustomerData.editCustomer.id,
                    sn:info.cNo,
                    contractSignTime: info.cSignDate,
                    contractStartTime: info.cStartDate,
                    contractEndTime: info.cEndDate,
                    type: 1,
                    memo: info.cMemo || '',
                    filePath: info.scanFile.originalFilename ,
                    fileName: info.scanFile.filename
                }

                let imgs = []
                info.scanPics.forEach(im=>{
                    let imgItem = {
                        uplaodPath: im.filename,
                        type: 1,
                        title: im.title,
                        operation: im.operation || 1
                    }
                    if(imgItem.operation === 4){
                        imgItem.id = im.id
                    }
                    imgs.push(imgItem)
                })
                params.contractBaseImages = JSON.stringify(imgs)

                let zsGs = [], tyGs = []
                info.selectZSGs.forEach(zsGsI=>{
                    zsGs.push({ commodity: zsGsI.id })
                })
                info.selectGs.forEach(tyGsI=>{
                    tyGs.push({ commodity: tyGsI.id })
                })
                params.productZSList = JSON.stringify(zsGs)
                params.productTYList = JSON.stringify(tyGs)

                console.log('params=', params)
                if(this.contractInfoData.type === 'manageUpdate'){ // 管理客户-修改合同
                    params.id=this.contractInfoData.editContract.id
                    customerApi.upContractBase(params).then(res1=>{
                        let res= res1.data
                        let state = res.data.state
                        if(state === 1){
                            this.$message.success('操作成功！')
                            this.getlistContractBase(this.updateCustomerData.editCustomer.id)
                            this.contractInfoData.visible = false
                        }else if(state === 2){
                            this.$message.error('已续约不可修改!')
                            this.contractInfoData.visible = false
                        }else if(state === 3){
                            this.$message.error('修改的日期不能在上一个合同结束日之前!')
                        }else {
                            this.$message.error('操作失败')
                            this.contractInfoData.visible = false
                        }

                    }).catch(err=>{
                        console.log('err=',err)
                    })
                }
                else if(this.contractInfoData.type === 'manageXu'){ // 管理客户-续约合同
                    params.id=this.contractInfoData.editContract.id
                    customerApi.renewalContractForCommodity(params).then(res1=>{
                        let res = res1.data
                        let state = res.data.state
                        if(state === 1){
                            this.$message.success('操作成功！')
                            this.getlistContractBase(this.updateCustomerData.editCustomer.id)
                            this.contractInfoData.visible = false
                            if(this.contractInfoData.isEnd === 'end'){
                                this.contractIEnds()
                            }
                        }else if(state === 2){
                            this.$message.error('已续约不可修改!')
                            this.contractInfoData.visible = false
                        }else if(state === 3){
                            this.$message.error('修改的日期不能在上一个合同结束日之前!')
                        }else {
                            this.$message.error('操作失败')
                            this.contractInfoData.visible = false
                        }

                    }).catch(err=>{
                        console.log('err=', err)
                    })
                }
                else{ // 管理客户-新增合同
                    customerApi.insertContractBase(params).then(res1=>{
                        let res = res1.data
                        let state = res.data.state
                        if(state === 1){
                            this.$message.success('操作成功！')
                            this.getlistContractBase(this.updateCustomerData.editCustomer.id)
                            this.contractInfoData.visible = false
                        }else if(state === 2){
                            this.$message.error('已续约不可修改!')
                            this.contractInfoData.visible = false
                        }else if(state === 3){
                            this.$message.error('修改的日期不能在上一个合同结束日之前!')
                        }else {
                            this.$message.error('操作失败')
                            this.contractInfoData.visible = false
                        }

                    }).catch(err=>{
                        console.log('err=',err)
                    })
                }

            }
            else {
                let item = {
                    no: info.cNo,
                    date: info.cSignDate,
                    expDur: info.cStartDate + ' 至 ' + info.cEndDate,
                    info:info,
                }
                if(this.contractInfoData.type === 'add'){ // 新增客户-新增合同
                    this.addData.contract.tableData.push(item)
                    this.contractInfoData.visible = false
                }
                else if(this.contractInfoData.type === 'update'){ // 新增客户-修改合同
                    this.addData.contract.tableData[this.contractInfoData.editContractIndex] = item
                    this.contractInfoData.visible = false
                }

            }
        },
        updateContract(item, index){
            this.contractInfoData.editContractIndex = index
            this.contractInfoData.visible = true
            this.contractInfoData.type = 'update'
            this.contractInfoData.title = '修改合同'
            this.contractInfoData.scanPics = item.info.scanPics
            this.contractInfoData.scanFile = item.info.scanFile
            this.contractInfoData.selectGS = item.info.selectGS
            this.contractInfoData.gslist = null
            this.contractInfoData.oralGsList = item.info.oralGsList
        },
        delContract(item, index){
            this.addData.contract.tableData.splice(index, 1)
        },
        addContract(){
            this.contractInfoData.visible = true
            this.contractInfoData.type = 'add'
            this.contractInfoData.title = '新增合同'
            this.contractInfoData.scanPics = []
            this.contractInfoData.scanFile = []
            this.contractInfoData.selectGS = []
            this.contractInfoData.gslist = null
            this.contractInfoData.cNo = ''
            this.contractInfoData.cSignDate = ''
            this.contractInfoData.cStartDate = ''
            this.contractInfoData.cEndDate = ''
            this.contractInfoData.cMemo = ''
        },
        hideContractInfo(){
            this.contractInfoData = {}
            this.contractInfoData.visible = false
        },
        goSet(type){
            this.invoiceSetData.visible = true
            this.invoiceSetData.selectType = null
            this.invoiceSetData.type = type
        },
        hideinvoiceSet(){
            this.invoiceSetData.visible = false
        },
        clickInvoice(item){
            console.log('clickInvoice=', item)
            if(this.invoiceSetData.selectType && this.invoiceSetData.selectType.type == item.type){
                this.invoiceSetData.selectType = null
            }else{
                this.invoiceSetData.selectType = item
            }
        },
        invoiceSetOk(){
            this.invoiceSetData.visible = false
            if(this.invoiceSetData.type === 'manage'){ // 管理
                if(this.invoiceSetData.selectType && this.invoiceSetData.selectType.type){
                    this.manageInvoiceData.invoiceType = this.invoiceSetData.selectType
                }else{
                    this.manageInvoiceData.invoiceType = null
                }
            }else{ // 新增
                if(this.invoiceSetData.selectType && this.invoiceSetData.selectType.type){
                    this.addData.invoice.invoiceType = this.invoiceSetData.selectType
                }else{
                    this.addData.invoice.invoiceType = null
                }
            }

        },
        handleChange(value) {
            console.log('区域=', value)
            this.receiveAreaInfoData.areaArr = value
            let allAr = []
            value.forEach(ar => {
                allAr.push(ar.name)
            })
            this.receiveAreaInfoData.area = allAr.join('')
        },
        addArea(type){
            this.receiveAreaInfoData.visible = true
            this.receiveAreaInfoData.title = '新增到货区域'
            this.receiveAreaInfoData.type = type
            this.receiveAreaInfoData.area = ''
            this.receiveAreaInfoData.areaArr = []
            this.receiveAreaInfoData.requirements = ''
            this.receiveAreaInfoData.contact = {}
            if(this.receiveAreaInfoData.options.length === 0){
                this.getTYRegionFun()
            }
        },
        areaItemEdit(item, index){
            console.log('areaItemEdit addr=', item)
            this.receiveAreaInfoData.editIndex = index
            this.receiveAreaInfoData.visible = true
            this.receiveAreaInfoData.title = '修改到货区域'
            this.receiveAreaInfoData.type = 'update'
            this.receiveAreaInfoData.area = item.addr
            this.receiveAreaInfoData.areaArr = item.areaArr
            this.receiveAreaInfoData.requirements = item.requirements
            this.receiveAreaInfoData.contact = item.contact
            console.log('我看看这里怎么设置的', item.areaArr)
        },
        areaItemDel(item,index){
            this.receiveData.areaList.splice(index, 1)

        },
        getTYRegionFun(regionCode){
            customerApi.getTYRegion({ startLevel:1, endLevel:3 }).then(res1=>{
                let res = res1.data
                let list = res.data.list
                this.receiveAreaInfoData.areaList = list
                let ops = []
                let selectAreaArr = []
                list.forEach(pro => {
                    let item = {
                        value: pro ,
                        id: pro.id ,
                        label: pro.name,
                        code: pro.code
                    }
                    if(pro.subRegion){
                        item.children = []
                        pro.subRegion.forEach(city=>{
                            let cityItem = {
                                value: city ,
                                id: city.id ,
                                label: city.name,
                                code: city.code
                            }
                            if(city.subRegion){
                                cityItem.children = []
                                city.subRegion.forEach(district =>{
                                    let districtItem = {
                                        value: district ,
                                        id: district.id ,
                                        label: district.name,
                                        code: district.code
                                    }
                                    cityItem.children.push(districtItem)
                                    if(regionCode === districtItem.code){
                                        selectAreaArr = [
                                            pro,
                                            city ,
                                            district
                                        ]
                                        console.log('selectAreaArr=', selectAreaArr)
                                    }
                                })
                            }
                            item.children.push(cityItem)
                        })
                    }
                    ops.push(item)
                })
                this.receiveAreaInfoData.options = ops
                if(regionCode){
                    this.receiveAreaInfoData.areaArr = selectAreaArr
                }

            }).catch(err=>{
                console.log('err=', err)
            })
        },
        receiveAreaOK(){
            if(this.receiveAreaInfoData.area && this.receiveAreaInfoData.contact.name){
                if(this.rootCusBtn === 'add'){
                    this.receiveAreaInfoData.visible = false
                    let item = {
                        areaArr: this.receiveAreaInfoData.areaArr,
                        addr: this.receiveAreaInfoData.area,
                        requirements: this.receiveAreaInfoData.requirements,
                        contact: this.receiveAreaInfoData.contact,
                        name:this.receiveAreaInfoData.contact.name,
                        tel:this.receiveAreaInfoData.contact.info.tel
                    }
                    if(this.receiveAreaInfoData.type === 'add'){
                        this.receiveData.areaList.push(item)
                    }else if(this.receiveAreaInfoData.type === 'update'){
                        this.receiveData.areaList[this.receiveAreaInfoData.editIndex] = item
                    }
                }
                else if(this.rootCusBtn === 'update'){
                    let arrL = this.receiveAreaInfoData.areaArr
                    let lastCode = arrL[arrL.length-1]
                    if(this.receiveAreaInfoData.type === 'manageAdd'){
                        let params = { customerId: this.updateCustomerData.editCustomer.id }
                        let shAddress = {
                            "type":3,
                            "address": this.receiveAreaInfoData.area,
                            "contact": this.receiveAreaInfoData.contact.name ,
                            "mobile":  this.receiveAreaInfoData.contact.mobile ,
                            "customerContact": this.receiveAreaInfoData.contact.id ,
                            "path": lastCode.path ,
                            "regionCode": lastCode.code ,
                            "requirements": this.receiveAreaInfoData.requirements
                        }
                        params.shAddress = JSON.stringify(shAddress)
                        customerApi.addCustomerAddress(params).then(res1=>{
                            let res = res1.data
                            if(res.status == 1){
                                this.manageSaler(this.updateCustomerData.editCustomer)
                                this.$message.success('操作成功')
                                this.receiveAreaInfoData.visible = false
                            }else{
                                this.$message.error('操作失败')
                            }
                        }).catch(err=>{
                            console.log('err=',err)
                        })
                    }
                    else if(this.receiveAreaInfoData.type === 'manageUpdate'){
                        let params = {
                            address: this.receiveAreaInfoData.area,
                            contact: this.receiveAreaInfoData.contact.name ,
                            mobile: this.receiveAreaInfoData.contact.mobile  ,
                            customerContact: this.receiveAreaInfoData.contact.id ,
                            path: lastCode.path  ,
                            regionCode:  lastCode.code  ,
                            requirements: this.receiveAreaInfoData.requirements,
                            id: this.updateContactData.editObj.id ,
                        }
                        customerApi.updateCustomerAddress(params).then(res1=>{
                            let res = res1.data
                            if(res.status == 1){
                                this.$message.success('操作成功')
                                this.manageSaler(this.updateCustomerData.editCustomer)
                                this.receiveAreaInfoData.visible = false
                            }else{
                                this.$message.error('操作失败')
                            }

                        }).catch(err=>{
                            console.log('err=', err)
                        })
                    }
                    else if(this.addressEditData.manageType === 'manageshInfoEdit'){
                        this.receiveAreaInfoData.visible = false
                        let item = {
                            areaArr: this.receiveAreaInfoData.areaArr,
                            addr: this.receiveAreaInfoData.area,
                            requirements: this.receiveAreaInfoData.requirements,
                            contact: this.receiveAreaInfoData.contact,
                            name:this.receiveAreaInfoData.contact.name,
                            tel:this.receiveAreaInfoData.contact.mobile
                        }
                        if(this.receiveAreaInfoData.type === 'add'){
                            this.receiveData.areaList.push(item)
                        }else if(this.receiveAreaInfoData.type === 'update'){
                            this.receiveData.areaList[this.receiveAreaInfoData.editIndex] = item
                        }
                    }
                }

            }else{
                this.$message.error('还有必填项未填写！')
            }

        },
        hideReceiveAreaInfo(){
            this.receiveAreaInfoData.visible = false
        },
        addressItemDel(item, index){
            this.receiveData.addressList.splice(index, 1)
        },
        addressItemEdit(item, index){
            console.log('收获地址', item)
            this.addressEditData.visible = true
            this.addressEditData.type = 'update'
            this.addressEditData.title = '修改收获地址'
            this.addressEditData.editIndex = index
            this.addressEditData.address = item.addr
            this.addressEditData.contact = item.contact

        },
        toggleReceiveDataType(type){
            if(this.receiveData.type === type){
                this.receiveData.type = 0
            }else{
                this.receiveData.type = type
            }
        },
        delImg(index, imgType, type){
            if(type === 'manage'){
                this.manageBaseData[imgType].splice(index,1)
            }else{
                this.addData.base[imgType].splice(index,1)
            }
        },
        disabledDate(time){
            const date = new Date(time);
            const year = date.getFullYear();
            const curYear = (new Date()).getFullYear()
            if(year === curYear){
                return false
            }else{
                return true
            }
        },
        hideAddFun(){
            this.addData.visible = false
        },
        mainPageClick(pageItem){
            let params = { 'keyword': this.mainData.searchVal , 'currPage':pageItem.page, 'pageSize': this.pageSize  }
            this.getDemoListFun(params)
        },
        getList(){
            let rootPath = localStorage.getItem('rootPath')
            this.rootPath = JSON.parse(rootPath)
            let params = { 'keyword': '' , 'currPage':1 , 'pageSize': this.pageSize }
            this.getDemoListFun(params)
            this.getTYRegionFun()
            if(this.loginUserOid === 0){ // 特殊机构
                this.specialOrgPart = true
            }
        },
        getDemoListFun(params){
            this.mainData.pageShow = false
            this.mainData.curpage = params.currPage
            customerApi[ this.pageName === 'customerManage' ? 'getPdCustomer' : 'getAllPdCustomerList' ](params).then(res1 => {
                let res = res1.data
                this.mainData.params = params
                let pdCustomers = res.pdCustomers || [], list = []
                pdCustomers.forEach((item, index)=>{
                    list.push({
                        ...item,
                        no: index + 1,
                        code: item.code || '',
                        name: item.name || '',
                        principalName: item.principalName || '',
                        create: item.createName + ' ' + new Date(item["createDate"]).format('yyyy-MM-dd hh:mm:ss')  ,

                    })
                })
                this.mainData.list = list
                this.mainData.pageShow = true
                this.mainData.pageInfo = { 'currentPageNo': res.currPage , 'pageSize': this.pageSize , 'totalPage': res.totalPage }

            }).catch(err => {
                console.log('err=', err)
            })
        },
        searchSaler(){
            // 搜索 客户
            console.log('222')
            let params = { 'keyword': this.mainData.searchVal , 'currPage':1 , 'pageSize': this.pageSize  }
            this.getDemoListFun(params)
        },
        hideloadingFun(){
            this.custormerLeadingData.visible = false
        },
        formatInviceRequire(type) {
            let str = ''
            switch (Number(type)){
                case 0 :    str = '尚未设置'; break
                case 1 :    str = '需要主要为增值税专用发票'; break
                case 2 :    str = '需要主要为增值税专用发票以外的发票'; break
                case 3 :    str = '基本不需要开发票'; break
                case 4 :    str = '尚未设置'; break
                default: str = '尚未设置'
            }
            return str
        },
        showHuo(){
            let list = this.seeAccountData.info.shAddressList
            let selfState = this.seeAccountData.info.selfState
            console.log('list =', list)
            let addressList = [], areaList = []
            list.forEach(item=>{
                item.type === '3' ? areaList.push(item) : addressList.push(item)
            })
            this.seeAccountData.addressList = addressList
            this.seeAccountData.areaList = areaList
            this.seeAccountData.selfStateTxt = selfState === 1 ? '可上门自提' : '不可上门自提'
            this.seeAccountData.visibleSH = true

        },
        hideReceiveInfoSHFun(){
            this.seeAccountData.visibleSH = false
        },
        scanSaler(item){
            this.seeAccountData.visible = true
            customerApi.getPdCustomerOne({ id: item.id }).then(res1=>{
                let res = res1.data
                if(res.status === 0){
                    this.$message.error(res.msg)
                    return false
                }
                let data = res.data
                this.seeAccountData.info = data
                this.seeAccountData.info.contactLianxiTwo = []
                if(data.contactsList.length >0){
                    let newList = []
                    let twoItem = {}
                    let list = data.contactsList
                    list.forEach((item, index)=>{
                        if(index%2 === 0){
                            twoItem = { name:item.name, post:item.post, index1: index, info1: item }
                            if(index+1 === list.length){
                                newList.push(twoItem)
                            }
                        }else{
                            twoItem.name2 = item.name
                            twoItem.post2 = item.post
                            twoItem.info2 = item
                            twoItem.index2 = index
                            newList.push(twoItem)
                        }
                    })
                    this.seeAccountData.info.contactLianxiTwo = newList
                    console.log('newList=', newList)
                }

            }).catch(err=>{
                console.log('err=', err)
            })
            customerApi.listContractBase({ customer: item.id, type: 1}).then(res1=>{
                let res = res1.data
                let data = res.data || {}
                let list = data.contractBaseList || []
                let tabList = []
                list.forEach(info=>{
                    info.signTime = new Date(info.signTime).format('yyyy-MM-dd')
                    info.validStart = new Date(info.validStart).format('yyyy-MM-dd')
                    info.validEnd = new Date(info.validEnd).format('yyyy-MM-dd')
                    let item = {
                        no: info.sn,
                        date: info.signTime,
                        tyNum: info.generalCount,
                        zsNum: info.exclusiveCount,
                        expDur: info.validStart + ' 至 ' + info.validEnd,
                        info:info,
                        id:info.id,
                    }
                    console.log('数据=', item)
                    tabList.push(item)
                })
                this.seeAccountData.contractBaseList = tabList

            }).catch(err=>{
                console.log('err=', err)
            })

        },
        hideSeeFun(){
            this.seeAccountData.visible = false
        },
        hideUpdateCustomerFun(){
            this.updateCustomerData.visible = false
            this.addressEditData.manageType = ''
        },
        manageSaler(item){
            this.rootCusBtn = 'update'
            this.updateCustomerData.editCustomer = item
            customerApi.getUpdatePageData({ customerId: item.id  }).then(res1=>{
                let res = res1.data
                this.updateCustomerData.visible = true
                this.updateCustomerData.info = res
                let areaList = [], adressList = []
                res.shAddressList.forEach(shAddr=>{
                    if(shAddr.enabled){
                        if(shAddr.type === '1'){
                            adressList.push(shAddr)
                        }else if(shAddr.type === '3'){
                            areaList.push(shAddr)
                        }
                    }
                })
                this.updateCustomerData.areaList = areaList
                this.updateCustomerData.adressList = adressList

            }).catch(err=>{
                console.log('err=', err)
            })
            this.getlistContractBase(item.id)
        },
        getlistContractBase(customer){
            // 获取管理客户的合同列表
            customerApi.listContractBase({ customer: customer, type: 1  }).then(res1=>{
                let res = res1.data
                this.updateCustomerData.contractBaseList = res.data.contractBaseList

            }).catch(err=>{
                console.log('err=',err)
            })
        },
        delSaler(item){
            console.log('delSaler', item)
            this.delCusData.visible = true
            this.delCusData.editObj = item
            this.delCusData.tip = '确定删除客户代号为：'+ item.code +', 客户名称为：'+ item.name +' 的客户吗？'

        },
        hideDelCus(){
            this.delCusData.visible = false
        },
        delCusOK(){
            customerApi.deletePdCustomerOne({ id: this.delCusData.editObj.id }).then(res1=>{
                let res = res1.data
                if(res.status == 1){
                    this.$message.success('删除成功！')
                    this.getDemoListFun(this.mainData.params)
                }else if(res.status == 0){
                    this.$message.error('该客户下已有数据，不可删除！')
                }else{
                    this.$message.error('系统错误！！')
                }
            }).catch(err=>{
                console.log('err=', err)
            })
            this.delCusData.visible = false
        },
        stopSaler(item , type){
            this.paulsCusData.visible = true
            this.paulsCusData.editObj = item
            this.paulsCusData.state = type
            this.paulsCusData.tip = type === 1 ? '在系统中无法向被暂停合作的客户录入订单。 <br/> 确定暂停与该客户的合作吗？' : '确定与该客户恢复合作吗？'
        },
        paulsCusOK(){
            customerApi.suspendCooperation({ customerId:this.paulsCusData.editObj.id ,  state: this.paulsCusData.state  }).then(res1=>{
                let res = res1.data
                if(res.status === 1){
                    this.paulsCusData.visible = false
                    this.$message.success('操作成功！')
                    if(this.paulsCusData.state === 1){
                        this.getDemoListFun(this.mainData.params)
                    }else{
                        this.getSuspendCustomerFun(this.suspendCusData.params)
                    }
                }else{
                    this.$message.error('操作失败')
                }
            }).catch(err=>{
                console.log('err=', err)
            })
        },
        getSuspendCustomerFun(params){
            this.suspendCusData.pageShow = false
            customerApi[ this.pageName === 'customerManage' ? 'getSuspendCustomer' : 'getAllSuspendCustomerList' ](params).then(res1=>{
                let res = res1.data
                let pdCustomers = res.pdCustomers|| [] , list = []
                pdCustomers.forEach((item,index)=>{
                    list.push({
                        ...item,
                        no: index + 1,
                        code: item.code || '',
                        name: item.name || '',
                        suspendTime: item.updateName + ' ' + new Date(item["createDate"]).format('yyyy-MM-dd hh:mm:ss'),
                        create: item.createName + ' ' + new Date(item["updateDate"]).format('yyyy-MM-dd hh:mm:ss')
                    })
                })
                this.suspendCusData.list = list
                this.curPage = 'suspend'
                this.suspendCusData.suspendCusData = true
                this.suspendCusData.params = params
                this.suspendCusData.pageInfo = { currentPageNo: res.currPage,  pageSize: this.pageSize ,  totalPage: res.totalPage }

            }).catch(err=>{
                console.log('err=', err)
            })
        },
        hidePaulsCus(){
            this.paulsCusData.visible = false
        },
        backMain(){
            this.curPage = 'main'
            this.getDemoListFun(this.mainData.params)
        },
        uploadSalerBtn(){
            this.custormerLeadingData.visible = true
        },
        addSalerBtn(){
            this.addData.visible = true
            this.addDataInit()
            this.rootCusBtn = 'add'
        },
        toggleSta(){
            if(this.receiveData.status === 0){
                this.receiveData.status = 1
            }else{
                this.receiveData.status = 0
            }
        },
        huoEdit(){
            this.receiveData.visible = true
            this.receiveData.type = ''
            this.receiveData.status = 0
            this.receiveData.addressList = []
            this.receiveData.areaList = []

            if(this.addData.huo.info.type){
                this.receiveData.type = this.addData.huo.info.type
                if(this.addData.huo.info.type === 2){
                    console.log('this.addData.huo.info=', this.addData.huo.info)
                    if(this.addData.huo.info.addressList && this.addData.huo.info.addressList.length > 0){
                        this.receiveData.addressList = this.addData.huo.info.addressList
                    }
                    if(this.addData.huo.info.areaList && this.addData.huo.info.areaList.length > 0){
                        this.receiveData.areaList = this.addData.huo.info.areaList
                    }
                }
            }
        },
        hideReceiveInfoFun(){
            this.receiveData.visible = false
        },
        hideaddressEditFun(){
            this.addressEditData.visible = false
        },
        addressEditOk(){
            if(this.addressEditData.contact.name && this.addressEditData.address){
                if(this.rootCusBtn === 'add'){ // 新增客户
                    let item = {
                        addr: this.addressEditData.address ,
                        name: this.addressEditData.contact.name ,
                        tel:this.addressEditData.contact.info.tel ,
                        contact: this.addressEditData.contact
                    }
                    if(this.addressEditData.type == 'add'){
                        this.receiveData.addressList.push(item)

                    }else if(this.addressEditData.type == 'update'){
                        this.receiveData.addressList[this.addressEditData.editIndex] = item
                    }
                    this.addressEditData.visible = false
                }
                else if(this.rootCusBtn === 'update'){ // 管理客户
                    console.log('this.addressEditData.type =', this.addressEditData.type )
                    if(this.addressEditData.type === 'manageAdd'){
                        let params = { customerId: this.updateCustomerData.editCustomer.id }
                        let shAddress = {
                            "type":1,
                            "address": this.addressEditData.address ,
                            "contact": this.addressEditData.contact.name ,
                            "customerContact":this.addressEditData.contact.id ,
                            "mobile": this.addressEditData.contact.mobile
                        }
                        params.shAddress = JSON.stringify(shAddress)
                        customerApi.addCustomerAddress(params).then(res1=>{
                            let data = res1.data
                            let status = data.status
                            if (status === 1) {
                                this.manageSaler(this.updateCustomerData.editCustomer)
                                this.addressEditData.visible = false
                                this.$message.success('新增成功！')
                            } else {
                                this.$message.error('新增失败！')
                            }

                        }).catch(err=>{
                            console.log('err=', err)
                        })
                    }
                    else if(this.addressEditData.type === 'manageUpdate'){
                        let params = {
                            id: this.updateContactData.editObj.id,
                            type: 1,
                            address: this.addressEditData.address ,
                            contact: this.addressEditData.contact.name,
                            mobile: this.addressEditData.contact.mobile,
                        }
                        customerApi.updateCustomerAddress(params).then(res1=>{
                            let data = res1.data
                            let status = data.status
                            if (status === 1) {
                                this.manageSaler(this.updateCustomerData.editCustomer)
                                this.addressEditData.visible = false
                                this.$message.success('操作成功！')
                            } else {
                                this.$message.error('操作失败！')
                            }

                        }).catch(err=>{
                            console.log('err=', err)
                        })

                    }
                    else if(this.addressEditData.manageType === 'manageshInfoEdit'){
                        //整个编辑收货信息
                        console.log('this.addressEditData.contact=', this.addressEditData.contact)

                        let item = {
                            addr: this.addressEditData.address ,
                            name: this.addressEditData.contact.name ,
                            tel:this.addressEditData.contact.mobile ,
                            contact: this.addressEditData.contact
                        }
                        if(this.addressEditData.type == 'add'){
                            this.receiveData.addressList.push(item)

                        }else if(this.addressEditData.type == 'update'){
                            this.receiveData.addressList[this.addressEditData.editIndex] = item
                        }
                        this.addressEditData.visible = false
                    }
                }

            }else{
                this.$message.error('请先填写收货地址和联系人')
            }

        },
        receiveInfoOk(){
            let info = {
                type: this.receiveData.type,
                status: this.receiveData.status
            }
            if(info.type){
                if(this.receiveData.type === 1){

                }else if(this.receiveData.type === 2){
                    if(this.receiveData.addressList.length === 0 && this.receiveData.areaList.length === 0){
                        this.$message.error('收货信息编辑后才能保存！')
                        return false
                    }else{
                        this.receiveData.addressList.length > 0 ? info.addressList = JSON.parse(JSON.stringify( this.receiveData.addressList )) :''
                        this.receiveData.areaList.length > 0 ? info.areaList = JSON.parse(JSON.stringify( this.receiveData.areaList )) : ''
                    }
                }
            }else{
                this.$message.error('收货信息编辑后才能保存！')
                return false
            }
            if(this.rootCusBtn === 'update'){ // 管理客户
                let params = {
                    id:this.updateCustomerData.editCustomer.id,
                    deliveryType: info.type
                }
                if(params.deliveryType === 2){
                    let shAddressList = []
                    if(info.addressList){
                        info.addressList.forEach(address=>{
                            console.log('address=', address)
                            shAddressList.push({
                                "type":1,
                                "address": address.addr,
                                "contact": address.name,
                                "mobile": address.tel,
                                "customerContact":address.contact.id
                            })
                        })
                    }
                    if(info.areaList){
                        info.areaList.forEach(area=>{
                            console.log('area=', area)
                            debugger
                            let lastpath = ''
                            let lastcode = ''
                            area.areaArr.forEach(ddd=>{
                                lastcode = ddd.code
                                lastpath = ddd.path
                            })
                            shAddressList.push({
                                "type":3,"address":area.addr,"contact": area.name,
                                "mobile": area.tel ,"customerContact":area.contact.id,
                                "path": lastpath ,
                                "regionCode": lastcode ,"requirements": area.requirements
                            })
                        })
                    }
                    params.shAddressList = JSON.stringify(shAddressList)
                    params.selfState = info.status
                }
                customerApi.editCustomerAddress(params).then(res1=>{
                    let res = res1.data
                    if(res.success === 1){
                        this.receiveData.visible = false
                        this.manageSaler(this.updateCustomerData.editCustomer)
                        this.$message.success('新增成功！')
                    }else{
                        this.$message.error('操作失败！')
                    }

                }).catch(err=>{
                    this.$message.error('链接错误')
                    console.log('err=', err)
                })

            }else{ // 新增客户
                this.addData.huo.info = info
                this.addData.huo.txt = info.type === 1 ? '向该客户只提供服务，不提供实体货物，不需编辑收货地址' : '向该客户提供实体货物'
                this.receiveData.visible = false
            }

        },
        addReceive(){
            this.addressEditData.visible = true
            this.addressEditData.type = 'add'
            this.addressEditData.address = ''
            this.addressEditData.contact = { name: '' }
            this.addressEditData.title = '新增收货地址'
        },
        showContact(type){
            // type: 'receiveArea' - 新增到货区域; 'addressEdit' - 新增收货地址; 'mailInfo' - 新增发票邮寄信息
            if(this.rootCusBtn === 'update'){ // 管理
                this.chooseCusContactData.contactList = this.updateCustomerData.info.contactsList
            }else if(this.rootCusBtn === 'add'){
                this.chooseCusContactData.contactList = this.addData.contactLianxi.tableData
            }
            if(this.chooseCusContactData.contactList.length === 0){
                this.$message.info('暂无客户数据，请通过新增添加！')
            }else{
                this.chooseCusContactData.contactList.forEach(conc=>{
                    conc.info ? conc.mobile = conc.info.tel : ''
                })
                console.log('chooseCusContactData.contactList=', this.chooseCusContactData.contactList)
                this.chooseCusContactData.visible = true
                this.chooseCusContactData.type = type
                this.chooseCusContactData.selectConact = {}
                let selectConact = null
                switch (type) {
                    case 'receiveArea' :
                        selectConact = this.receiveAreaInfoData.contact
                        break
                    case 'addressEdit' :
                        selectConact = this.addressEditData.contact
                        break
                    case 'mailInfo' :
                        selectConact = this.mailInfoData.contact
                        break
                    default:
                }
                this.chooseCusContactData.selectConact = selectConact
            }
        },
        chooseCusContactOk(){
            if(this.chooseCusContactData.selectConact.id){
                switch (this.chooseCusContactData.type){
                    case 'addressEdit': // 新增收货地址
                        console.log(this.chooseCusContactData.selectConact)
                        this.addressEditData.contact = this.chooseCusContactData.selectConact
                        break
                    case 'receiveArea': // 新增到货区域
                        this.receiveAreaInfoData.contact = this.chooseCusContactData.selectConact
                        break
                    case 'mailInfo': // 新增发票邮寄地址
                        this.mailInfoData.contact = this.chooseCusContactData.selectConact
                        break
                    default:
                        console.log('没有找到对应的type')
                }
                this.chooseCusContactData.visible = false
            }else{
                this.$message.error('请先选择联系人')
            }

        },
        showOptionsFun(){
            this.editContactLianXiData.showOptions = true
            this.editContactLianXiData.defineType = ''
        },
        useDefinedLabelOk(){
            let indexI = this.editContactLianXiData.defineType
            let type = this.editContactLianXiData.typeList[indexI]
            let newItem = {
                name: this.useDefinedLabelData.val,
                value:type.value,
                val:''
            }
            this.editContactLianXiData.contactArr.push(newItem)
            this.setContactList()
            this.editContactLianXiData.showOptions = false
            this.editContactLianXiData.typeList[indexI].disabled = true
            this.useDefinedLabelData.visible = false
        },
        beforeUploadLianXi(){
            this.editContactLianXiData.loading = true
            this.editContactLianXiData.loadingBtn = ''
        },
        fileSuccessLianXi(file, files, ofile, totalFiles){
            file.src = this.rootPath.fileUrl + file.filename
            this.editContactLianXiData.picInfo = file
            console.log('file=', file)
            this.editContactLianXiData.loading = false
            this.editContactLianXiData.loadingBtn = '点击此处上传名片'

        },
        uploadFileLianXi(){},
        delPic(){
            this.editContactLianXiData.picInfo = {}
        },
        setArrVal(contact, type){
            let index = contact['index'+ type]
            let value = contact['value' + type]
            let val = type === 2 ? contact.val2 : contact.val
            this.editContactLianXiData.contactArr[index].val = val
            console.log('结果=', this.editContactLianXiData.contactArr[index])
        },
        delLink(contact, type){
            let value = contact['value' + type]
            let index = contact['index'+ type]
            this.editContactLianXiData.typeList.forEach(typeItem => {
                if(typeItem.value == value){
                    typeItem.disabled = false
                }
            })
            this.editContactLianXiData.contactArr.splice(index, 1)
            this.setContactList()
        },
        setLink(){
            let indexI = this.editContactLianXiData.defineType
            let type = this.editContactLianXiData.typeList[indexI]
            this.editContactLianXiData.showOptions = false

            if(type.value === 9){
                this.useDefinedLabelData.visible = true
            }else{
                let newItem = { name: type.label, value:type.value, val:''  }
                this.editContactLianXiData.contactArr.push(newItem)
                this.setContactList()
                this.editContactLianXiData.typeList[indexI].disabled = true
            }
        },
        setContactList(){
            let c = this.editContactLianXiData.contactArr, list = []
            c.forEach((linkI, index0)=>{
                let yu = index0%2
                if(yu === 0){
                    list.push({
                        name:linkI.name, val:linkI.val, value1: linkI.value, index1: index0,
                        name2:'', val2:"", value2: '' , index2: '',
                    })
                }else{
                    let latI = list[list.length-1]
                    latI.name2 = linkI.name
                    latI.val2 = linkI.val
                    latI.value2 = linkI.value
                    latI.index2 = index0
                }
            })
            this.editContactLianXiData.contactList = list
        },
        hideUseDefinedLabel(){
            this.useDefinedLabelData.visible = false
        },

        clickItem(Conac){
            if(this.chooseCusContactData.selectConact && this.chooseCusContactData.selectConact.id == Conac.id){
                this.chooseCusContactData.selectConact = null
            }else{
                this.chooseCusContactData.selectConact = Conac
            }
        },
        hideEditContactLianXiFun(){
            this.editContactLianXiData.visible = false
        },
        addContact(typeStr){
            this.editContactLianXiData = {
                typeStr: typeStr,
                visible:true,
                showOptions:false,
                loading:false,
                loadingBtn:'点击此处上传名片',
                name:'',
                postName:'',
                type:'add',
                title: '新增客户联系人',
                typeList:[
                    { label: '手机', value:1 ,disabled: false },
                    { label: 'QQ', value:2 ,disabled: false },
                    { label: 'Email', value:3 ,disabled: false },
                    { label: '微信', value:4 ,disabled: false },
                    { label: '微博', value:5 ,disabled: false },
                    { label: '自定义', value:9 ,disabled: false },
                ],
                contactArr:[],
                contactList:[],
                picInfo:{}
            }
            if(typeStr === 'receiveArea' || typeStr === 'addressEdit'){
                this.editContactLianXiData.tag = '收货人'
            }else if(typeStr === 'mailInfo'){
                this.editContactLianXiData.tag = '发票接收人'
            }else if(typeStr === 'manageAdd'){
                this.editContactLianXiData.tag = '其他'
            }
        },
        getFive() {
            const randomNumber = Math.floor(Math.random() * 100000);
            const formattedNumber = String(randomNumber).padStart(5, '0');
            return formattedNumber;
        },
        editContactLianXiOk(){
            let lianXiItem ={
                tag: this.editContactLianXiData.tag ,
                typeStr: this.editContactLianXiData.typeStr,
                name: this.editContactLianXiData.name,
                postName: this.editContactLianXiData.postName,
                tel: this.editContactLianXiData.tel,
                contactArr: this.editContactLianXiData.contactArr,
                contactList: this.editContactLianXiData.contactList,
                typeList: this.editContactLianXiData.typeList,
                picInfo: this.editContactLianXiData.picInfo
            }
            if(this.rootCusBtn == 'add'){ // 新增客户
                console.log('this.editContactLianXiData.type=', this.editContactLianXiData.type)
                if(this.editContactLianXiData.type === 'add'){
                    lianXiItem.newId = this.getFive()
                    let newContact = {
                        name:lianXiItem.name ,
                        post:lianXiItem.postName ,
                        id:lianXiItem.newId ,
                        info:lianXiItem,
                    }
                    console.log('联系人完整数据：', newContact)
                    this.addData.contactLianxi.tableData.push(newContact)
                    switch (this.editContactLianXiData.typeStr){
                        case 'addressEdit': // 新增收货地址
                            this.addressEditData.contact = newContact
                            break
                        case 'receiveArea': // 新增到货区域
                            this.receiveAreaInfoData.contact = newContact
                            break
                        case 'mailInfo': // 新增发票邮寄地址
                            this.mailInfoData.contact = newContact
                            break
                        default:
                            console.log('没有找到对应的 typeStr')
                    }
                }
                else if(this.editContactLianXiData.type === 'update'){
                    lianXiItem.newId = this.editContactLianXiData.newId
                    let index = this.editContactLianXiData.editIndex
                    let newContact = {
                        name:lianXiItem.name ,
                        post:lianXiItem.postName ,
                        id:lianXiItem.newId ,
                        info:lianXiItem,
                    }
                    this.addData.contactLianxi.tableData[index] = newContact
                    // 修改上面的
                    if(this.addData.huo.info.addressList && this.addData.huo.info.addressList.length > 0){
                        let addressList = this.addData.huo.info.addressList
                        addressList.forEach((address) => {
                            if(address.contact.id === newContact.id){
                                address.name = newContact.name
                                address.tel = newContact.info.tel
                                address.contact = newContact
                            }
                        })
                    }
                    if(this.addData.huo.info.areaList && this.addData.huo.info.areaList.length > 0){
                        let areaList = this.addData.huo.info.areaList
                        areaList.forEach((area) => {
                            if(area.contact.id === newContact.id){
                                area.name = newContact.name
                                area.tel = newContact.info.tel
                                area.contact = newContact
                            }
                        })
                    }
                    if(this.addData.mail.tableData && this.addData.mail.tableData.length > 0){
                        let mailTable = this.addData.mail.tableData
                        mailTable.forEach((mailItem, mailIndex) => {
                            if(mailItem.contact.id === newContact.id){
                                mailItem.recivePerson = newContact.name
                                mailItem.tel = newContact.info.tel
                                mailItem.contact = newContact
                                // mailTable[mailIndex] = mailItem
                            }
                        })
                    }

                }
                this.editContactLianXiData.visible = false
            }
            else if(this.rootCusBtn == 'update'){ // 管理客户
                let params = {
                    tags: lianXiItem.tag,
                    name: lianXiItem.name,
                    post: lianXiItem.postName,
                    mobile: lianXiItem.tel,
                    visitCard: '',
                    socialList: [],
                }
                if(lianXiItem.picInfo && lianXiItem.picInfo.filename){
                    params.visitCard = lianXiItem.picInfo.filename
                }
                if(lianXiItem.contactArr && lianXiItem.contactArr.length > 0){
                    lianXiItem.contactArr.forEach(cI=>{
                        let cc = {
                            code: cI.val ,
                            type: cI.value ,
                            name: cI.name
                        }
                        params.socialList.push(cc)
                    })
                }
                params.socialList = JSON.stringify(params.socialList)
                if(this.editContactLianXiData.type === 'manageUpdate'){
                    params.contactId = this.editContactLianXiData.id
                    customerApi.updateContactsSocialAndCard(params).then(res1=>{
                        let res = res1.data
                        let status = res.status
                        if(status === 1){
                            this.manageSaler(this.updateCustomerData.editCustomer)
                            this.$message.success('操作成功！')
                            this.editContactLianXiData.visible = false
                        }else{
                            this.$message.error('操作失败')
                        }
                    }).catch(err=>{
                        console.log('err=',err)
                    })

                }else if(this.editContactLianXiData.type === 'add'){
                    params.customer = this.updateCustomerData.editCustomer.id
                    customerApi.addCustomerContact(params).then(res1=>{
                        let res = res1.data
                        let status = res.status
                        if(status === 1){
                            this.manageSaler(this.updateCustomerData.editCustomer)
                            if(this.editContactLianXiData.type === 'add'){
                                lianXiItem.newId = res.id
                                let newContact = {
                                    name:lianXiItem.name ,
                                    post:lianXiItem.postName ,
                                    mobile:lianXiItem.tel ,
                                    id:lianXiItem.newId ,
                                    info:lianXiItem,
                                }
                                this.addData.contactLianxi.tableData.push(newContact)
                                switch (this.editContactLianXiData.typeStr){
                                    case 'addressEdit': // 新增收货地址
                                        this.addressEditData.contact = newContact
                                        break
                                    case 'receiveArea': // 新增到货区域
                                        this.receiveAreaInfoData.contact = newContact
                                        break
                                    case 'mailInfo': // 新增发票邮寄地址
                                        this.mailInfoData.contact = newContact
                                        break
                                    case 'manageAdd': // 新增联系人
                                        this.$message.success('操作成功！')
                                        break
                                    default:
                                        console.log('没有找到对应的 typeStr')
                                }
                            }
                            this.editContactLianXiData.visible = false


                        }else{
                            layer.msg('操作失败,请重试')
                        }

                    }).catch(err=>{
                        console.log('err=', err)
                    })
                }



            }

        },
        detailsConac(Conac){

        },
        hideChooseCusContactFun(){
            this.chooseCusContactData.visible = false
        },
        addDataInit(){
            this.addData.chanNum = 0
            this.addData.quanNum = 0
            this.addData.chanSum = 9
            this.addData.quanSum = 9
            this.addData.base = {
                cusname: '',
                cuscoding: '',
                address1: '',
                cusfullName: '',
                buyCase: 1,
                firstTime: 0,
                firstTimeN: false,
                chanImg: [],
                quanImg: [],
                infoSource:'',
                firstContactTime:'',
                firstContactAddress:'',
                memo:'',
                supervisorName:'',
                supervisorMobile:'',
            }
            this.addData.invoice = {
                invoiceName:'',
                invoiceAddress:'',
                telephone:'',
                bankName:'',
                bankNo:'',
                taxpayerID:'',
            }
            this.addData.huo = {  info:'' }
            this.addData.contract.tableData = []
            this.addData.mail.tableData = []
            this.addData.contactLianxi.tableData = []

        },
        stopSalerCon(){
            this.getSuspendCustomerFun({ currPage: 1, pageSize: 20 })
        },
        uploadFile(){},

        matImportOk(){
            if(this.custormerLeadingData.path){
                customerApi.importCustomer(this.custormerLeadingData.path).then(res1=>{
                    let res = res1.data
                    this.custormerLeadingData.visible = false
                    if(res==1){
                        this.$message.success('操作成功！')
                        let params = { 'keyword': this.mainData.searchVal , 'currPage': this.mainData.curpage , 'pageSize': this.pageSize  }
                        this.getDemoListFun(params)
                    }else{
                        let file = this.custormerLeadingData.file
                        let fileUid = file.fileUid;
                        let op = {"type":'fileUid', 'fileUid':fileUid}
                        this.$ref.uploadFile.removeRemote(file);
                        this.$message.error('上传失败！')
                    }
                }).catch(err=>{
                    console.log('err=', err)
                })
            }else{
                this.$message.error('您需选择一个文件后才能“导入”！')
            }
        },
        fileSuccess(file, files, ofile, totalFiles){
            this.custormerLeadingData.path = file.filename
            this.custormerLeadingData.fileName = file.originalFilename
            this.custormerLeadingData.file = file
        },
        beforeUpload2_manage(file){
            if(this.manageBaseData.chanNum >= this.manageBaseData.chanSum){
                return false
            }else{
                this.manageBaseData.chanNum++
            }
        },
        beforeUpload2(file){
            if(this.addData.chanNum >= this.addData.chanSum){
                return false
            }else{
                this.addData.chanNum++
            }
        },
        uploadFile1_manage(){
            this.manageBaseData.quanNum = this.manageBaseData.quanImg.length
        },
        uploadFile1(){
            this.addData.quanNum = this.addData.base.quanImg.length
        },
        uploadFile2(){
            this.addData.chanNum = this.addData.base.chanImg.length
        },
        uploadFile2_manage(){
            this.manageBaseData.chanNum = this.manageBaseData.chanImg.length
        },
        showScan(img){
            img.show = true
            console.log('显示',img)
        },
        hideScan(img){
            img.show = false
            console.log('隐藏',img)

        },
        beforeUpload1_manage(file){
            if(this.manageBaseData.quanNum >= this.manageBaseData.quanSum){
                return false
            }else{
                this.manageBaseData.quanNum++
            }
        },
        beforeUpload1(file){
            if(this.addData.quanNum >= this.addData.quanSum){
                return false
            }else{
                this.addData.quanNum++
            }
        },
        fileSuccess1_manage(file, files, ofile, totalFiles){
            let imgItem = {
                src: this.rootPath.fileUrl + file.filename,
                name:file.originalFilename,
                ...file
            }
            this.manageBaseData.quanImg.push(imgItem)
        },
        fileSuccess1(file, files, ofile, totalFiles){
            let imgItem = {
                src: this.rootPath.fileUrl + file.filename,
                name:file.originalFilename,
                ...file
            }
            this.addData.base.quanImg.push(imgItem)
        },
        fileSuccess2(file, files, ofile, totalFiles){
            let imgItem = {
                src: this.rootPath.fileUrl + file.filename,
                name:file.originalFilename,
                ...file
            }
            this.addData.base.chanImg.push(imgItem)
        },
        fileSuccess2_manage(file, files, ofile, totalFiles){
            let imgItem = {
                src: this.rootPath.fileUrl + file.filename,
                name:file.originalFilename,
                ...file
            }
            this.manageBaseData.chanImg.push(imgItem)
        },

    }
}
