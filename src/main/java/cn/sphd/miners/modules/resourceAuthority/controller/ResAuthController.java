package cn.sphd.miners.modules.resourceAuthority.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.dailyAffairs.entity.UserSuspendMsg;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.forumArea.service.ForumService;
import cn.sphd.miners.modules.personal.dao.ApprovalProcessDao;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.resourceAuthority.dto.ChunkResultDto;
import cn.sphd.miners.modules.resourceAuthority.entity.ResCategory;
import cn.sphd.miners.modules.resourceAuthority.entity.ResEntity;
import cn.sphd.miners.modules.resourceAuthority.entity.ResHistory;
import cn.sphd.miners.modules.resourceAuthority.entity.resTimeEntity;
import cn.sphd.miners.modules.resourceAuthority.service.ResCategoryService;
import cn.sphd.miners.modules.resourceAuthority.service.ResCorrelationService;
import cn.sphd.miners.modules.resourceAuthority.service.ResNoticeService;
import cn.sphd.miners.modules.resourceAuthority.service.ResService;
import cn.sphd.miners.modules.resourceAuthority.service.impl.ResUsing;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.dto.UserDto;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgService;
import cn.sphd.miners.modules.system.service.UserService;
import cn.sphd.miners.modules.uploads.service.UploadService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by 朱思旭 on 2017/10/31.
 */
@Controller
@RequestMapping("/res")
public class ResAuthController {

    @Autowired
    ResCategoryService resCategoryService;
    @Autowired
    ResService resService;
    @Autowired
    UserService userService;
    @Autowired
    OrgService orgService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    SWMessageService swMessageService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    ApprovalProcessDao approvalProcessDao;
    @Autowired
    UploadService uploadService;
    @Autowired
    ResCorrelationService resCorrelationService;
    @Autowired
    ForumService forumService;
    @Autowired
    ResNoticeService resNoticeService;

    private final String module = "文件与资料";

    //资源中心不是超管、总务、小总务的人和资料管理获取初始文件夹
    @ResponseBody
    @RequestMapping("/getInitialFolder.do")
    public JsonResult getInitialFolder(User user, String type){
        QueryData query = resCategoryService.getFirstType(user, type);
        JsonResult result = new JsonResult(1,query);
        return result;
    }

    //资源中心超管、总务、小总务获取初始文件夹
    @ResponseBody
    @RequestMapping("/getInitialFolderByManage.do")
    public JsonResult getInitialFolderByManage(User user){
        HashMap<String, Object> query = resCategoryService.getFirstTypeByManager(user);
        JsonResult result = new JsonResult(1,query);
        return result;
    }


    //资源中心不是超管、总务、小总务的人和资料管理获取本级文件夹和子集文件夹
    @ResponseBody
    @RequestMapping("/getFolderAndChildFolder.do")
    public JsonResult getFolderAndChildFolder(User user, String type, Integer categoryId){
        QueryData query = resCategoryService.getCategoryAndChildCategory(user, type, categoryId);
        JsonResult result = new JsonResult(1,query);
        return result;
    }

    //资源中心超管、总务、小总务获取本级文件夹和子集文件夹
    @ResponseBody
    @RequestMapping("/getFolderAndChildFolderManager.do")
    public JsonResult getFolderAndChildFolderManager(User user, Integer categoryId){
        QueryData query = resCategoryService.getCategoryAndChildCategoryManager(user, categoryId);
        JsonResult result = new JsonResult(1,query);
        return result;
    }

    //wyu: Begin of Forder Auth

    @ResponseBody
    @RequestMapping("/getForderAuthDepsAndUsers.do")
    public JsonResult getForderAuthDepsAndUsers(Integer categoryId, User user, String type) {
        HashMap<String, Object> result = new HashMap<>();
        Integer oid = user.getOid();
        result.put("catAuthTree",  resCategoryService.getAuthOrgWithUserTreeByOid(oid, categoryId, type));
        if (categoryId != 0) {
            ResCategory category = resCategoryService.getSingle(categoryId);
            result.put("resLockUuid", category.getLockUuid());
        }
        return  new JsonResult(1, result);
    }

    @ResponseBody
    @RequestMapping("/getFileAuthDepsAndUsers.do")
    public JsonResult getFileAuthDepsAndUsers(Integer fileId, User user) {
        Integer oid = user.getOid();
        HashMap<String, Object> result = new HashMap<>();
        result.put("fileAuthTree", resService.getAllUserIdsByFileId(oid, fileId));
        ResEntity res = resService.getSingle(fileId);
        result.put("resLockUuid", res.getLockUuid());
        return  new JsonResult(1, result);
    }

    @ResponseBody
    @RequestMapping("/updateFolderAuth.do")
    public JsonResult updateFolderAuth(User user, Integer categoryId, Integer[] userID, String resLockUuid) {
        if(userID==null)
            userID=new Integer[]{};
        String state = "1";
        if (categoryId != null) {
            ResCategory cat = resCategoryService.getSingle(categoryId);
            String lockUuid = cat.getLockUuid();
            if (lockUuid != null) {
                if (!(cat.getLockUuid().equals(resLockUuid))) {
                    state = "3";
                } else {
                    resCategoryService.updateResCatUuid(categoryId);
                }
            } else {
                resCategoryService.updateResCatUuid(categoryId);
            }
        }
        if ("1".equals(state)) {
            state = resCategoryService.updateFolderAuth(categoryId,new ArrayList<>(Arrays.asList(userID)),user,"2",null, null);
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        return new JsonResult(1,map);
    }

    @ResponseBody
    @RequestMapping("/updateFileAuth.do")
    public JsonResult updateFileAuth(User user, Integer fileId, Integer[] userID, String resLockUuid) {
        if(userID==null)
            userID=new Integer[]{};
        String state = "1";
        ResEntity res = resService.getSingle(fileId);
        String lockUuid = res.getLockUuid();
        if (lockUuid != null) {
            if (!(resLockUuid.equals(res.getLockUuid()))) {
                state = "2";
            } else {
                resService.updateResUuid(fileId);
                resService.updateFileAuth(fileId,new ArrayList<>(Arrays.asList(userID)), res, user);
            }
        }else {
            resService.updateResUuid(fileId);
            resService.updateFileAuth(fileId,new ArrayList<>(Arrays.asList(userID)), res, user);
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        return new JsonResult(1,map);
    }
    //wyu: End of Forder Auth

    //新增文件夹(资源中心)
    @ResponseBody
    @RequestMapping("/insertSameFolder.do")
    public JsonResult insertSameFolder(User user, Integer parent, String categoryName, String users){
        ResCategory category = resCategoryService.checkFolder(user,parent,categoryName,"1");
        QueryData query = new QueryData();
        String status = "1";
        if (category == null) {
            List<Integer> listUserID = null;
            if(users!=null){
                listUserID = JSON.parseArray(users, Integer.class);
            }
            ResCategory newCategory = resCategoryService.addSameCategoryByResource(user,parent,categoryName,"1", listUserID);
            List<Integer> listUserIDs = resCategoryService.getAllUserIdsByCategory(newCategory.getId());
            resService.sendCategory(newCategory, listUserIDs, user, "1","1");     //新增文件夹
            query.put("newCategory",newCategory);
        }else {
            status = "2";
        }
        query.put("status", status);
        return new JsonResult(1,query);
    }

    //资源中心确定上传文件
    @ResponseBody
    @RequestMapping("/resCentrenAffirdFile.do")
    public JsonResult resCentrenAffirdFile(User user, String files, Integer category, String content, Integer changeNum,
                                           String type, Integer auditor, String auditName, String module, String noticeType){
        String status = resService.insertReourceCentreFile(user,files,category,content,changeNum,type,auditor,auditName, noticeType);
        return new JsonResult(1, status);
    }

    //资源中心确定上传文件夹
    @ResponseBody
    @RequestMapping("/resCentrenAffirdFolder.do")
    public JsonResult resCentrenAffirdFolder(User user, Integer parent, String files, String module) {
        List<ChunkResultDto> fileList = JSON.parseArray(files,ChunkResultDto.class);
        return resService.insertReourceCentreFolder(user,parent,fileList,module);
    }

    //文件发布申请中的文件
    @ResponseBody
    @RequestMapping("/applyPublishFile.do")
    public JsonResult applyPublishFile(User user){
        List<ResHistory> listHis = resService.getApplyPublichFile(user);
        HashMap<String, Object> map = new HashMap<>();
        map.put("listHis",listHis);
        return new JsonResult(1, map);
    }


    //点击查看按钮查看发布的文件详情
    @ResponseBody
    @MessageMapping("/onePublishFileMessage")
    public JsonResult onePublishFileMessage(String json, User user, String sessionid){
        JSONObject jsonObject = JSON.parseObject(json);
        Integer hisId = jsonObject.getInteger("hisId");
        HashMap<String, Object> map = resService.oneFileMessage(hisId);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/onePublishFileMessage",null,null,user.getOid(),user.getOrganization().getName(),JSON.toJSONString(map));
        return new JsonResult(1, map);
    }

    //App点击查看按钮查看发布的文件详情（由于用不了给单写的）
    @ResponseBody
    @RequestMapping("/onePublishFileMessageByApp.do")
    public JsonResult onePublishFileMessage(String json){
        JSONObject jsonObject = JSON.parseObject(json);
        Integer hisId = jsonObject.getInteger("hisId");
        HashMap<String, Object> map = resService.oneFileMessage(hisId);
        ResHistory resHis = (ResHistory) map.get("resHis");
        return new JsonResult(1, map);
    }

    //参考资料换版文件
    @ResponseBody
    @RequestMapping("/updateFileVersion.do")
    public JsonResult updateFileVersion(User user, ResEntity resEntity, String module){
        ResEntity r = new ResEntity();
        r.setId(resEntity.getId());
        r.setCategory(resEntity.getCategory());
        r.setContent(resEntity.getContent());
        r.setReason(resEntity.getReason());
        r.setPath(resEntity.getPath());
        r.setSize(resEntity.getSize());
        r.setVersion(resEntity.getVersion());
        r.setOpertatorName(resEntity.getOpertatorName());
        if (resEntity.getOperateDate() != "") {
            r.setOperateDate(resEntity.getOperateDate());
        }
        r.setVerifierName(resEntity.getVerifierName());
        if (resEntity.getVerifyDate() != "") {
            r.setVerifyDate(resEntity.getVerifyDate());
        }
        r.setApproverName(resEntity.getApproverName());
        if (resEntity.getApproveDate() != "") {
            r.setApproveDate(resEntity.getApproveDate());
        }
        r.setApproveStatus(resEntity.getApproveStatus());
        QueryData query = resService.affirdUpdateVersion(user, r, module);
        if ((Integer) query.get("state") > 0) {
            return new JsonResult(1,query);
        } else {
            return new JsonResult("换版失败");
        }
    }

    //申请废止或换版文件时要改变文件状态并锁住文件
    @ResponseBody
    @RequestMapping("/applyUpdateFileOPeration.do")
    public JsonResult applyUpdateFileOPeration(User user, Integer id){
        Integer state = resService.updateResOperation(user,id);
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        return new JsonResult(1,map);
    }

    //解锁文件接口
    @ResponseBody
    @RequestMapping("/unlockFileOperation.do")
    public JsonResult unlockFileOperation(User user, Integer id){
        Integer state = resService.unlockFile(user,id);
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        return new JsonResult(1,map);
    }

    //当上一个接口返回1的时候 要调用此接口来开始执行换版或废止文件的操作
    @ResponseBody
    @RequestMapping("/updateFileVersionByCentre.do")
    public JsonResult updateFileVersionByCentre(User user, Integer file, String content,String path,
                                                String size, String version, Integer category, String name, String fileSn,
                                                String type, Integer approveId, String approveName, String module){
        //System.out.println("/updateFileVersionByCentre\n"+json+"\n"+new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
        resService.affridVersionForApprove(user, file, content, path, size, version, category, name, fileSn, type, approveId, approveName, module);
        return new JsonResult(1,"1");
    }

    //复用文件
    @ResponseBody
    @RequestMapping("/reuseFile.do")
    public JsonResult reuseFile(User user, Integer id){
        Integer state = resService.reuseRes(user,id);
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        return new JsonResult(1,map);
    }

    //进行处理文件
    @ResponseBody
    @RequestMapping("/handleFile.do")
    @MessageMapping("handleFile")
    public JsonResult hadleFile(String json, User user){
        JSONObject jsonObject = JSON.parseObject(json);
        ResHistory resHistory = new ResHistory();
        resHistory.setId(jsonObject.getInteger("hisId"));
        ResHistory oldResHis = resService.gethisSingle(resHistory);
        ResCategory category = resCategoryService.getSingle(oldResHis.getCategory());
        QueryData query = new QueryData();
        ApprovalProcess ap = approvalProcessDao.get(jsonObject.getInteger("processId"));
        if("1".equals(ap.getApproveStatus())){
            ApprovalProcess approvalProcess = new ApprovalProcess();
            approvalProcess.setId(jsonObject.getInteger("processId"));
            approvalProcess.setLevel(jsonObject.getInteger("level"));
            approvalProcess.setHandleTime(new Date());
            String approveStatus = jsonObject.getString("approveStatus");
            approvalProcess.setApproveStatus(approveStatus);
            resHistory.setApproveStatus(approveStatus);
            if (Byte.valueOf("1").equals(category.getIsTrash())) {
                resHistory.setIsTrash(Byte.valueOf("1"));
            }
            if(approveStatus.equals("3")){
                String reason = jsonObject.getString("reason");
                if (reason != ""){
                    approvalProcess.setReason(reason);
                    resHistory.setApplyMemo(reason);
                }
            }
            String type =jsonObject.getString("type");      //审批类型 1是选择下级审批人 2是选择下级审批是最终审批 3是进行最终审批
            if(type.equals("3")){
                approvalProcess.setToUser(user.getUserID());
                approvalProcess.setToUserName(user.getUserName());
                ApprovalProcess newAp = resService.updateApprovalProcessForResource(approvalProcess);
                ResHistory resHis = null;
                HashMap<String, Object> mapHis = new HashMap<>();
                resHistory.setAuditor(user.getUserID());
                resHistory.setAuditName(user.getUserName());
                resHistory.setAuditDate(NewDateUtils.dateToString(approvalProcess.getHandleTime(),"yyyy-MM-dd HH:mm:ss"));
                UserSuspendMsg userSuspendMsg = null;
                ResEntity r = new ResEntity();
                String moduleUse = jsonObject.getString("module");
                if (moduleUse == null) {
                    moduleUse = module;
                }
                List<Integer> listUserIDs = null;
                if(oldResHis.getChangeNum() == 0 && "0".equals(oldResHis.getTeminateState())){
                    if (resHistory.getApproveStatus().equals("2")) {
                        String allCategoryName = resService.allParentCategoryName(category.getParent(), category.getName(), null, "1");
                        r.setCategory(oldResHis.getCategory());
                        r.setCategoryPath(category.getPath());
                        r.setName(oldResHis.getName());
                        r.setContent(oldResHis.getContent());
                        r.setFileSn(oldResHis.getFileSn());
                        r.setPath(oldResHis.getPath());
                        r.setSize(oldResHis.getSize());
                        r.setVersion(oldResHis.getVersion());
                        r.setChangeNum(oldResHis.getChangeNum());
                        r.setCreator(oldResHis.getCreator());
                        r.setCreateName(oldResHis.getCreateName());
                        r.setCreateDate(oldResHis.getCreateDate());
                        r.setViewNum(0);
                        r.setDownloadNum(0);
                        r.setMoveNum(0);
                        r.setOrg(user.getOid());
                        r.setValid(1);
                        r.setAuditor(user.getUserID());
                        if (Byte.valueOf("1").equals(category.getIsTrash())) {
                            r.setIsTrash(Byte.valueOf("1"));
                            resHistory.setIsTrash(Byte.valueOf("1"));
                        }
                        r.setAuditName(user.getUserName());
                        r.setAuditDate(NewDateUtils.dateToString(newAp.getHandleTime(),"yyyy-MM-dd HH:mm:ss"));
                        r.setApproveStatus(resHistory.getApproveStatus());
                        r.setApproveMemo(resHistory.getApproveStatus());
                        resService.insertFileSingle(r,user,moduleUse);                        //在主表新增文件同时新增引用
                        resService.insertFileAuthByCategoryAuth(r);
                        resHistory.setFile(r.getId());
                        resHis = resService.updateResHisSingle(resHistory);
                        //新增移动的历史
                        resService.addHisMoveFile(resHis.getFile(), resHis.getCategory(), allCategoryName, resHis.getVersion(), category.getPath());
                        //新增名字的历史
                        resService.addHisUpNameFile(resHis.getFile(), resHis.getCategory(), resHis.getName(), resHis.getVersion());
                        //新增编号的历史
                        resService.addHisUpFileSn(resHis.getFile(), resHis.getCategory(), resHis.getFileSn(), resHis.getVersion());
                        listUserIDs = resCategoryService.getAllUserIdsByCategory(resHis.getCategory());
                        resNoticeService.addSpecialAuthUserId(listUserIDs, user);  //填入特殊权限
                        if (resHis.getBatchUuid() != null) {
                            Long num = resService.getNoApproveFileNum(resHis.getBatchUuid(), "1");
                            if (num.equals(Long.valueOf(0))) {
                                Long finishNum = resService.getNoApproveFileNum(resHis.getBatchUuid(), "2");
                                resNoticeService.sendResFileNotice(user, null, null, null, null,resHis.getBatchUuid(), finishNum, null,"1", listUserIDs,null,null);
                            }
                        } else {
                            resNoticeService.sendResFileNotice(user, null, null, null, resHis.getId(),null, null, null,"2",listUserIDs,null,null);
                        }
                        userSuspendMsg = resService.sendNewMessageByResource(resHis.getCreator(), "1", resHis.getChangeNum(), resHis.getName(), oldResHis.getId(), resHis.getAuditDate(), "fileMessageDetail");
                        resService.sendFile(r.getId(), user, "1", "1", null,"1",null);  //长连接推送新文件给资源中心模块
                    } else {
                        resHis = resService.updateResHisSingle(resHistory);
                        userSuspendMsg = resService.sendNewMessageByResource(resHis.getCreator(), "2", resHis.getChangeNum(), resHis.getName(), oldResHis.getId(), resHis.getAuditDate(), "fileMessageDetail");
                        if (resHis.getBatchUuid() != null) {
                            Long num = resService.getNoApproveFileNum(resHis.getBatchUuid(), "1");
                            if (num.equals(Long.valueOf(0))) {
                                Long finishNum = resService.getNoApproveFileNum(resHis.getBatchUuid(), "2");
                                if (!finishNum.equals(Long.valueOf(0))) {
                                    resNoticeService.sendResFileNotice(user, null, null, null, null,resHis.getBatchUuid(), finishNum, null,"1", listUserIDs,null,null);
                                }
                            }
                        }
                    }
                    mapHis.put("resHis", resHis);
                    User userCreator = userService.getUserByID(resHis.getCreator());
                    swMessageService.rejectSend(0,-1,mapHis,resHis.getCreator().toString(),"/applyPublishFile",null,null,userCreator,"releaseApply");       //给发布申请人发
                    //                   resService.sendMes(userSuspendMsg, newAp, resHis.getCreator().toString());   //发布文件被批准或驳回时给发布人推送
                } else if (oldResHis.getChangeNum() > 0 || "1".equals(oldResHis.getTeminateState())) {
                    resHis = resService.updateResHisSingle(resHistory);
                    r.setId(resHis.getFile());
                    r.setOperation("0");                                //此字段只要审批换版成功后就为0，在刚一新增时为空
                    ResEntity resNew = null;
                    listUserIDs = resCategoryService.getAllUserIdsByFile(r.getId());
                    resNoticeService.addSpecialAuthUserId(listUserIDs, user);  //填入特殊权限
                    if (resHistory.getApproveStatus().equals("2")) {
                        r.setChangeNum(resHis.getChangeNum());
                        r.setUpdator(resHis.getUpdator());
                        r.setUpdateName(resHis.getUpdateName());
                        r.setUpdateDate(resHis.getUpdateDate());
                        r.setAuditor(user.getUserID());
                        r.setAuditName(user.getUserName());
                        r.setAuditDate(resHis.getAuditDate());
                        if ("1".equals(resHis.getTeminateState())) {
                            r.setTeminateState(resHis.getTeminateState());
                            r.setTeminateTime(resHis.getTeminateTime());
                            r.setTeminater(resHis.getTeminater());
                            r.setTeminaterName(resHis.getTeminaterName());
                            resService.updateFileSingle(r);
                            userSuspendMsg = resService.sendNewMessageByResource(resHis.getUpdator(), "3", resHis.getChangeNum(), resHis.getName(),oldResHis.getId(), resHis.getAuditDate(), "fileChangeMessageDetail");
                            resNoticeService.sendResFileNotice(user, null, null, null, resHistory.getId(),null, null, null,"4",listUserIDs,null,null);
                        } else {
                            ResEntity resOld = resService.getSingle(resHis.getFile());
                            String oldFilename = resOld.getPath();
                            ResUsing callback = new ResUsing(resOld.getId(), resOld.getClass());
                            uploadService.delFileUsing(callback, oldFilename, user);         //删除主表的文件引用
                            r.setContent(resHis.getContent());
                            r.setPath(resHis.getPath());
                            r.setSize(resHis.getSize());
                            r.setVersion(resHis.getVersion());
                            r.setReason(resHis.getReason());
                            resService.updateFileSingle(r);
                            resNew = resService.getSingle(r.getId());
                            uploadService.addFileUsing(callback,resNew.getPath(), resNew.getName(), user, moduleUse);      //更换主表的引用为最新的  jsonObject.getString("module")
                            userSuspendMsg = resService.sendNewMessageByResource(resHis.getUpdator(), "1", resHis.getChangeNum(), resHis.getName(),oldResHis.getId(), resHis.getAuditDate(), "fileChangeMessageDetail");
                            resCorrelationService.batchRemoveCorrelation(resHis.getFile(), user);
                            //换版时若是讨论区有关联要发送2条消息
                            forumService.changeResVersionSendNewMes(resNew.getId(),resNew.getName(),resNew.getFileSn(),resNew.getChangeNum());
                            //发文件换版签收通知
                            resNoticeService.sendResFileNotice(user, null, null, null, resHistory.getId(),null, null, null,"3",listUserIDs,null,null);
                        }
                    }else{
                        if ("1".equals(resHis.getTeminateState())) {
                            userSuspendMsg = resService.sendNewMessageByResource(resHis.getUpdator(), "4", resHis.getChangeNum(), resHis.getName(),oldResHis.getId(), resHis.getAuditDate(), "fileChangeMessageDetail");
                        }else {
                            userSuspendMsg = resService.sendNewMessageByResource(resHis.getUpdator(), "2", resHis.getChangeNum(), resHis.getName(),oldResHis.getId(), resHis.getAuditDate(), "fileChangeMessageDetail");
                        }
                        resService.updateFileSingle(r);
                    }
                    mapHis.put("resHis", resHis);
                    User userUpdator = userService.getUserByID(resHis.getUpdator());
                    swMessageService.rejectSend(0,-1,mapHis,resHis.getUpdator().toString(),"/applyChangeFileVersion",null,null,userUpdator,"versionApply");    //给换版申请人发
                    resService.sendFile(r.getId(), user, "2", "1", null,"1",null);  //长连接推送新文件给资源中心模块
                    resService.sendSearchByFile(null, user, r.getId());  //长连接推送新文件给搜索
                }
                swMessageService.rejectSendToMidManageCode(-1,-1,mapHis,user.getOid(),"rb","/approveFileByFinal","versionApproval", null,null, "general");  //给所有终审人员发送
                clusterMessageSendingOperations.convertAndSendToUser(resHistory.getId().toString(),"/fileUpdateState",null,null,user.getOid(),user.getOrganization().getName(), "3"); //给查看当前文件详情的人推送消息，提示文件变化了
                resService.sendApprovalsFile(resHistory.getId(), mapHis,0);
            }else {
                if (type.equals("1") && approveStatus.equals("2")) {
                    resHistory.setAuditor(jsonObject.getInteger("auditor"));
                    resHistory.setAuditName(jsonObject.getString("auditName"));
                }
                resService.handleFileByNeedtoApproval(user,resHistory,approvalProcess,type);
            }
            query.put("approveStatus", ap.getApproveStatus());
        }else {
            query.put("approveStatus", "4");
            clusterMessageSendingOperations.convertAndSendToUser(resHistory.getId().toString(),"/fileUpdateState",null,null,user.getOid(),user.getOrganization().getName(), "4"); //给查看当前文件详情的人推送消息，提示文件不能重复操作
        }
        return new JsonResult(1, query);
    }

    //文件换版申请中的文件
    @ResponseBody
    @RequestMapping("/applyChangeFileVersion.do")
    public JsonResult applyChangeFileVersion(User user){
        List<ResHistory> resHistory = resService.applyChangeFileVersion(user);
        HashMap<String,Object> resHisMap =new HashMap<>();
        resHisMap.put("resHis", resHistory);
        return new JsonResult(1, resHisMap);
    }

    //文件审批中的文件
    @ResponseBody
    @RequestMapping("/approveFile.do")
    public JsonResult approveFile(User user){
        List<ResHistory> listApplyHis = resService.resourceApproveFile(user, "1");
        List<ResHistory> listApproveHis = resService.resourceApproveFile(user, "2");
        HashMap<String, Object> mapApply = new HashMap<>();
        mapApply.put("resHitoryList", listApplyHis);
        mapApply.put("resHitoryListApprove", listApproveHis);
        return new JsonResult(1, mapApply);
    }


    //文件发布/换版审批中的文件
    @ResponseBody
    @RequestMapping("/approveFileByFinal.do")
    public JsonResult approveFileByFinal(User user){
        List<ResHistory> listHis = resService.resourceFinalApproveFile(user);
        HashMap<String, Object> map = new HashMap<>();
        map.put("resHitoryList", listHis);
        return new JsonResult(1, map);
    }

    //终止发布文件
    @ResponseBody
    @RequestMapping("/stopFileProcess.do")
    @MessageMapping("/stopFileProcess")
    public JsonResult stopFileProcess(String json, User user){
        JSONObject jsonObject = JSON.parseObject(json);
        ResHistory resHistory = new ResHistory();
        resHistory.setId(jsonObject.getInteger("hisId"));
        ResHistory oldResHis = resService.gethisSingle(resHistory);
        HashMap<String, Object> map = new HashMap<>();
        if("1".equals(oldResHis.getApproveStatus())){
            resHistory.setApproveStatus("5");
            Integer processId = jsonObject.getInteger("processId");
            String status =  resService.userStopFileProcess(user,resHistory,processId);
            map.put("status", status);
        } else {
            map.put("status", "2");
        }
        return new JsonResult(1, map);
    }

    //获取本机构所有的人员
    @ResponseBody
    @RequestMapping("/getAllOidUserByResource.do")
    public JsonResult getAllOidUserByResource(User user, Integer hisId, Integer borrowId, Integer aboutId, String type){
        List<UserDto> list = resService.userList(user, hisId, borrowId, aboutId, type);
        HashMap<String, Object> map = new HashMap<>();
        map.put("list",list);
        return new JsonResult(1,map);
    }


    //返回大小总务
    @ResponseBody
    @RequestMapping("/getAllMangerForResource.do")
    public JsonResult getAllMangerForResource(User user){
        HashMap<String, Object> map = new HashMap<>();
        Integer oid = user.getOid();
        User userManager = userService.getUserByRoleCode(oid, "general");
        map.put("mangerId", userManager.getUserID());
        Integer minorAffairsId = userService.getSmallManageByMCode(oid, "general");
        if (minorAffairsId != 0){
            map.put("minorAffairsId", minorAffairsId);
        }
        return new JsonResult(1, map);
    }

    /**
     * 三种时间类型查询批准或驳回我发布的文件
     * @param eventType 查询事件类型 1是文件发布申请中查找 2是文件换版/废止申请中查找 3是文件审批中查找 4是文件发布/换版/废止审批中查找
     * @param type 查询时间类型 1是月 2是年 3是自定义 4是查近7日
     * @param timeBegin 查询时间 三种状态都要传 查月时传“2019-01” 查年时传“2019” 自定义时传“2019-01-01”
     * @param timeEnd 只有当自定义时传 “20198-03-02”
     * @param approveStatus 1是已批准 2是已驳回
     * @return
     * @throws IOException
     * @throws ParseException
     */
    @ResponseBody
    @RequestMapping("/threeTypeFindFileByHandleFile.do")
    public JsonResult threeTypeFindFileByHandleFile(User user, String eventType, String type, String timeBegin, String timeEnd, String approveStatus, Integer fromUser) throws IOException, ParseException {
        /*type =  "3";
        timeBegin = "2018-01-01";
        timeEnd = "2019-03-01";*/
        HashMap<String, Object> hisMap = new HashMap<>();
        Integer userID = user.getUserID();
        Integer oid = user.getOid();
        hisMap.put("userID", userID);
        hisMap.put("approveStatus", approveStatus);
        ArrayList<resTimeEntity> timeNumList = new ArrayList<>();
        Integer sum = 0;
        QueryData query = new QueryData();
        if (type.equals("1")) {
            timeBegin = timeBegin+ "-01 00:00:00";
            Date dateBegin = NewDateUtils.dateFromString(timeBegin, "yyyy-MM-dd HH:mm:ss");
            Date dateEnd = NewDateUtils.getLastTimeOfMonth(dateBegin);
            timeEnd = NewDateUtils.dateToString(dateEnd, "yyyy-MM-dd HH:mm:ss");
            hisMap.put("timeBegin", timeBegin);
            hisMap.put("timeEnd", timeEnd);
            List<ResHistory> listResHis = null;
            switch (eventType){
                case "1" :
                    listResHis = resService.publicFileByApproveStatus(hisMap);
                    break;
                case "2" :
                    listResHis = resService.changeFileByApproveStatus(hisMap);
                    break;
                case "3" :
                    listResHis = resService.approveFileByUserAndApproveStatus(userID, approveStatus, timeBegin, timeEnd, fromUser);
                    break;
                case "4" :
                    listResHis = resService.monthFinalApproveFileByUserAndApproveStatus(userID, approveStatus, timeBegin, timeEnd, oid, fromUser);
                    break;
            }
            sum = listResHis.size();
            query.put("listResHis", listResHis);
            query.put("timeBegin", timeBegin);
            query.put("timeEnd", timeEnd);
        } else if (type.equals("2")) {
            timeEnd = timeBegin + "-12-31";
            timeBegin = timeBegin + "-01-01";
            ArrayList<String> timeList = this.getEveryMonth(timeBegin, timeEnd);
            for (String t : timeList) {
                hisMap.put("timeBegin", t);
                Integer monthNum = 0;
                switch (eventType){
                    case "1" :
                        monthNum = resService.publishFileNumByUserAndApproveStatus(hisMap);
                        break;
                    case "2" :
                        monthNum = resService.changeFileNumByUserAndApproveStatus(hisMap);
                        break;
                    case "3" :
                        monthNum = resService.approveFileNum(userID, approveStatus, fromUser, t);
                        break;
                    case "4" :
                        monthNum = resService.finalApproveFileNumByFind(userID, approveStatus, "rb", oid, fromUser, t);
                        break;
                }
                if(!monthNum.equals(0)){
                    resTimeEntity rt = new resTimeEntity();
                    rt.setTime(t);
                    rt.setNum(monthNum);
                    timeNumList.add(rt);
                    sum = sum + monthNum;
                }
            }
            query.put("timeNumList", timeNumList);
            query.put("timeBegin", timeBegin);
            query.put("timeEnd", timeEnd);
        } else if (type.equals("3")) {
            Date begin= NewDateUtils.today(NewDateUtils.dateFromString(timeBegin,"yyyy-MM-dd"));
            Date end=NewDateUtils.tomorrow(NewDateUtils.dateFromString(timeEnd,"yyyy-MM-dd"));
            long between = NewDateUtils.getDaies(end.getTime() - begin.getTime());
            if (between > 30) {
                ArrayList<String> timeList = this.getEveryMonth(timeBegin, timeEnd);
                for (String t : timeList) {
                    hisMap.put("timeBegin", t);
                    Integer monthNum = 0;
                    switch (eventType){
                        case "1" :
                            monthNum = resService.publishFileNumByUserAndApproveStatus(hisMap);
                            break;
                        case "2" :
                            monthNum = resService.changeFileNumByUserAndApproveStatus(hisMap);
                            break;
                        case "3" :
                            monthNum = resService.approveFileNum(userID, approveStatus, fromUser, t);
                            break;
                        case "4" :
                            monthNum = resService.finalApproveFileNumByFind(userID, approveStatus, "rb", oid, fromUser, t);
                            break;
                    }
                    if(!monthNum.equals(0)){
                        resTimeEntity rt = new resTimeEntity();
                        rt.setTime(t);
                        rt.setNum(monthNum);
                        timeNumList.add(rt);
                        sum = sum + monthNum;
                    }
                }
                query.put("timeNumList", timeNumList);
            } else {
                hisMap.put("timeBegin", timeBegin);
                hisMap.put("timeEnd", timeEnd);
                List<ResHistory> listResHis = null;
                switch (eventType){
                    case "1" :
                        listResHis = resService.publicFileByApproveStatus(hisMap);
                        break;
                    case "2" :
                        listResHis = resService.changeFileByApproveStatus(hisMap);
                        break;
                    case "3" :
                        listResHis = resService.approveFileByUserAndApproveStatus(userID, approveStatus, timeBegin, timeEnd, fromUser);
                        break;
                    case "4" :
                        listResHis = resService.monthFinalApproveFileByUserAndApproveStatus(userID, approveStatus, timeBegin, timeEnd, oid, fromUser);
                        break;
                }
                sum = listResHis.size();
                query.put("listResHis", listResHis);
                query.put("timeBegin", timeBegin);
                query.put("timeEnd", timeEnd);
            }
        } else {
            Date dateEnd = NewDateUtils.tomorrow();
            timeEnd = NewDateUtils.dateToString(dateEnd,"yyyy-MM-dd HH:mm:ss");
            Date dateBegin = NewDateUtils.changeDay(dateEnd, -7);
            timeBegin = NewDateUtils.dateToString(dateBegin,"yyyy-MM-dd HH:mm:ss");
            hisMap.put("timeBegin", timeBegin);
            hisMap.put("timeEnd", timeEnd);
            List<ResHistory> listResHis = null;
            switch (eventType){
                case "1" :
                    listResHis = resService.publicFileByApproveStatus(hisMap);
                    break;
                case "2" :
                    listResHis = resService.changeFileByApproveStatus(hisMap);
                    break;
                case "3" :
                    listResHis = resService.approveFileByUserAndApproveStatus(userID, approveStatus, timeBegin, timeEnd, fromUser);
                    break;
                case "4" :
                    listResHis = resService.monthFinalApproveFileByUserAndApproveStatus(userID, approveStatus, timeBegin, timeEnd, oid, fromUser);
                    break;
            }
            sum = listResHis.size();
            query.put("listResHis", listResHis);
            query.put("timeBegin", timeBegin.substring(0,10));
            query.put("timeEnd", NewDateUtils.dateToString(NewDateUtils.changeDay(dateEnd, -1),"yyyy-MM-dd"));
        }
        query.put("sum", sum);
        return new JsonResult(1, query);
    }


    //获取两个时间间隔之内的月份 例如2018-01,2018-02
    private ArrayList<String> getEveryMonth(String timeBegin, String timeEnd) throws ParseException {
        ArrayList<String> result = new ArrayList<String>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");//格式化为年月
        Calendar min = Calendar.getInstance();
        Calendar max = Calendar.getInstance();
        min.setTime(sdf.parse(timeBegin));
        min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), 1);
        max.setTime(sdf.parse(timeEnd));
        max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH), 2);
        Calendar curr = min;
        while (curr.before(max)) {
            result.add(sdf.format(curr.getTime()));
            curr.add(Calendar.MONTH, 1);
        }
        return result;
    }


    /*
    1.260 产品录入 提供接口
     */

    //获取文件禁用和启用的记录
    @ResponseBody
    @RequestMapping("/getResForProduct.do")
    public JsonResult getResForProduct(String resIds){
        HashMap<String, Object> map = new HashMap<>();
        Integer state = 1;
        if (StringUtils.isNotBlank(resIds)) {
            String[] ids = resIds.split(",");
            List<String> listIds = new ArrayList<>(ids.length);
            Collections.addAll(listIds,ids);
            List<ResEntity> list = new ArrayList<>();
            for (String id : listIds) {
                ResEntity r = resService.getSingle(Integer.valueOf(id));
                list.add(r);
            }
            map.put("list", list);
        } else {
            state = 0;
        }
        map.put("state", state);
        return new JsonResult(1,map);
    }



    /**
     * 以下是资源中心跳转页面的接口
     */
    @RequestMapping("/changeVersion.do")
    public String changeVersion(User user, Model model ){
        String generalType = "";
        if (userService.isSuper(user)) {
            generalType = "0";
        }else if (userService.isGeneral(user)){
            generalType = "1";
        }else if (userService.isGeneralSmallManager(user)){
            generalType = "2";
        }
        model.addAttribute("generalType", generalType);
        model.addAttribute("user",user);
        return "/resourceCentre/changeVersion";
    }

    @RequestMapping("/issueManage.do")
    public String issueManage(User user, Model model ){
        String generalType = "";
        if (userService.isSuper(user)) {
            generalType = "0";
        }else if (userService.isGeneral(user)){
            generalType = "1";
        }else if (userService.isGeneralSmallManager(user)){
            generalType = "2";
        }
        model.addAttribute("generalType", generalType);
        model.addAttribute("user",user);

        return "/resourceCentre/issueManage";
    }

    @RequestMapping("/wenJian.do")
    public String wenJian(User user, Model model ){
        String generalType = "";
        if (userService.isSuper(user)) {
            generalType = "0";
        }else if (userService.isGeneral(user)){
            generalType = "1";
        }else if (userService.isGeneralSmallManager(user)){
            generalType = "2";
        }
        model.addAttribute("generalType", generalType);
        model.addAttribute("user",user);

        return "/resourceCentre/wenjian";
    }

    @RequestMapping("/gongGao.do")
    public String gongGao(User user, Model model ){
        String generalType = "";
        if (userService.isSuper(user)) {
            generalType = "0";
        }else if (userService.isGeneral(user)){
            generalType = "1";
        }else if (userService.isGeneralSmallManager(user)){
            generalType = "2";
        }
        model.addAttribute("generalType", generalType);
        model.addAttribute("user",user);
        return "/resourceCentre/gonggao";
    }

    @RequestMapping("/resHistory.do")
    public String resHistory(User user, Model model ){
        String generalType = "";
        if (userService.isSuper(user)) {
            generalType = "0";
        }else if (userService.isGeneral(user)){
            generalType = "1";
        }else if (userService.isGeneralSmallManager(user)){
            generalType = "2";
        }
        model.addAttribute("generalType", generalType);
        model.addAttribute("user",user);
        return "/resourceCentre/reshistory";
    }

}
