package cn.sphd.miners.modules.complaint.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity (name="ComplaintBaseHistory")
@Table(name = "t_complaint_base_history")
public class ComplaintBaseHistory implements Serializable {
  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;
  @Column(name = "complaint")
  private Integer complaint;
  @Column(name = "org")
  private Integer org;
  @Column(name = "customer")
  private Integer customer;
  @Column(name = "contact_name")
  private String contactName;
  @Column(name = "contact")
  private String contact;
  @Column(name = "contact_phone")
  private String contactPhone;
  @Column(name = "post")
  private String post;
  @Column(name = "receipt_time")
  private Date receiptTime;
  @Column(name = "type")
  private String type;
  @Column(name = "content")
  private String content;
  @Column(name = "state")
  private String state;
  @Column(name = "terminate_state", length=1)
  private String terminateState;
  @Column(name = "register_approve_time")
  private Date registerApproveTime;
  @Column(name = "register_approver")
  private Integer registerApprover;
  @Column(name = "register_reject_reason")
  private String registerRejectReason;
  @Column(name = "processor")
  private Integer processor;
  @Column(name = "settle_type")
  private String settleType;
  @Column(name = "settle_opinion")
  private String settleOpinion;
  @Column(name = "settler")
  private Integer settler;
  @Column(name = "settle_time")
  private Date settleTime;
  @Column(name = "settle_reject_reason")
  private String settleRejectReason;
  @Column(name = "settle_approver")
  private Integer settleApprover;
  @Column(name = "settle_approve_time")
  private Date settleApproveTime;
  @Column(name = "memo")
  private String memo;
  @Column(name = "initial_receipter")
  private Integer initialReceipter;
  @Column(name = "initial_handler")
  private Integer initialHandler;
  @Column(name = "creator")
  private Integer creator;
  @Column(name = "create_name")
  private String createName;
  @Column(name = "create_time")
  private Date createTime;
  @Column(name = "updator")
  private Integer updator;
  @Column(name = "update_name")
  private String updateName;
  @Column(name = "update_time")
  private Date updateTime;
  @Column(name = "operation")
  private String operation;
  @Column(name = "previous_id")
  private Integer previousId;
  @Column(name = "version_no")
  private Integer versionNo;

  @Transient
  private String processorName;//处理人名字

  //fuhzu
  public String getProcessorName() {
    return processorName;
  }

  public void setProcessorName(String processorName) {
    this.processorName = processorName;
  }

  //shujuku
  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Integer getComplaint() {
    return complaint;
  }

  public void setComplaint(Integer complaint) {
    this.complaint = complaint;
  }

  public Integer getOrg() {
    return org;
  }

  public void setOrg(Integer org) {
    this.org = org;
  }

  public Integer getCustomer() {
    return customer;
  }

  public void setCustomer(Integer customer) {
    this.customer = customer;
  }

  public String getContactName() {
    return contactName;
  }

  public void setContactName(String contactName) {
    this.contactName = contactName;
  }

  public String getContact() {
    return contact;
  }

  public void setContact(String contact) {
    this.contact = contact;
  }

  public String getContactPhone() {
    return contactPhone;
  }

  public void setContactPhone(String contactPhone) {
    this.contactPhone = contactPhone;
  }

  public String getPost() {
    return post;
  }

  public void setPost(String post) {
    this.post = post;
  }

  public Date getReceiptTime() {
    return receiptTime;
  }

  public void setReceiptTime(Date receiptTime) {
    this.receiptTime = receiptTime;
  }

  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public String getContent() {
    return content;
  }

  public void setContent(String content) {
    this.content = content;
  }

  public String getState() {
    return state;
  }

  public void setState(String state) {
    this.state = state;
  }

  public Date getRegisterApproveTime() {
    return registerApproveTime;
  }

  public void setRegisterApproveTime(Date registerApproveTime) {
    this.registerApproveTime = registerApproveTime;
  }

  public Integer getRegisterApprover() {
    return registerApprover;
  }

  public void setRegisterApprover(Integer registerApprover) {
    this.registerApprover = registerApprover;
  }

  public String getRegisterRejectReason() {
    return registerRejectReason;
  }

  public void setRegisterRejectReason(String registerRejectReason) {
    this.registerRejectReason = registerRejectReason;
  }

  public Integer getProcessor() {
    return processor;
  }

  public void setProcessor(Integer processor) {
    this.processor = processor;
  }

  public String getSettleType() {
    return settleType;
  }

  public void setSettleType(String settleType) {
    this.settleType = settleType;
  }

  public String getSettleOpinion() {
    return settleOpinion;
  }

  public void setSettleOpinion(String settleOpinion) {
    this.settleOpinion = settleOpinion;
  }

  public Integer getSettler() {
    return settler;
  }

  public void setSettler(Integer settler) {
    this.settler = settler;
  }

  public Date getSettleTime() {
    return settleTime;
  }

  public void setSettleTime(Date settleTime) {
    this.settleTime = settleTime;
  }

  public String getSettleRejectReason() {
    return settleRejectReason;
  }

  public void setSettleRejectReason(String settleRejectReason) {
    this.settleRejectReason = settleRejectReason;
  }

  public Integer getSettleApprover() {
    return settleApprover;
  }

  public void setSettleApprover(Integer settleApprover) {
    this.settleApprover = settleApprover;
  }

  public Date getSettleApproveTime() {
    return settleApproveTime;
  }

  public void setSettleApproveTime(Date settleApproveTime) {
    this.settleApproveTime = settleApproveTime;
  }

  public String getMemo() {
    return memo;
  }

  public void setMemo(String memo) {
    this.memo = memo;
  }

  public Integer getInitialReceipter() {
    return initialReceipter;
  }

  public void setInitialReceipter(Integer initialReceipter) {
    this.initialReceipter = initialReceipter;
  }

  public Integer getInitialHandler() {
    return initialHandler;
  }

  public void setInitialHandler(Integer initialHandler) {
    this.initialHandler = initialHandler;
  }

  public Integer getCreator() {
    return creator;
  }

  public void setCreator(Integer creator) {
    this.creator = creator;
  }

  public String getCreateName() {
    return createName;
  }

  public void setCreateName(String createName) {
    this.createName = createName;
  }

  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public Integer getUpdator() {
    return updator;
  }

  public void setUpdator(Integer updator) {
    this.updator = updator;
  }

  public String getUpdateName() {
    return updateName;
  }

  public void setUpdateName(String updateName) {
    this.updateName = updateName;
  }

  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public String getOperation() {
    return operation;
  }

  public void setOperation(String operation) {
    this.operation = operation;
  }

  public Integer getPreviousId() {
    return previousId;
  }

  public void setPreviousId(Integer previousId) {
    this.previousId = previousId;
  }

  public Integer getVersionNo() {
    return versionNo;
  }

  public void setVersionNo(Integer versionNo) {
    this.versionNo = versionNo;
  }

  public String getTerminateState() {
    return terminateState;
  }

  public void setTerminateState(String terminateState) {
    this.terminateState = terminateState;
  }
}
