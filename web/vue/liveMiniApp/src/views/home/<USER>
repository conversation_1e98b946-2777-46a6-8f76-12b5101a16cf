<template>
	<div class="uni-container">
    <Pageheader :title="offName" :showBack="false" />

    <div class="handleC">
      <div @click="tabNum(1)" :class="activeH === 1 ? 'activeH' :''">任务/申请（{{ lyricsTaskApplyMsgNum }}）</div>
      <div @click="tabNum(2)" :class="activeH === 2 ? 'activeH' :''">处理/审批（{{ lyricsTaskHandleMsgNum }}）</div>
      <div @click="tabNum(3)" :class="activeH === 3 ? 'activeH' :''">消息（{{ lyricsTaskApprovalMsgNum }}）</div>
    </div>
    <div class="mar30">
      <div v-show="activeH === 1">
        <div v-if="managerCode === 'super'" class="live-panel live-tr">
          <div @click="goPage('lycTaskApply')">
            <el-icon class="icS"><upload color="#53B5A8"/></el-icon> 歌词任务
            <el-icon class="live-right relaRight">
              <arrow-right-bold color="#53B5A8" class="houseSty8" />
            </el-icon>
          </div>
        </div>
      </div>
      <div v-show="activeH === 2">
        <div class="live-panel live-pad">
          <div class="live-tr" :class="managerCode === 'super'? 'live-tr-line' : ''" @click="goPage('lycTaskHandel')">
            <el-icon class="icS"><notification color="#53B5A8"/></el-icon>歌词任务处理
            <el-icon class="live-right relaRight">
              <arrow-right-bold color="#53B5A8" class="houseSty8" />
            </el-icon>
          </div>
          <div class="live-tr" v-if="managerCode === 'super'"  @click="goPage('lycTaskCharge')">
            <el-icon class="icS"><tickets color="#53B5A8" /></el-icon>歌词任务审批
            <el-icon class="live-right relaRight">
              <arrow-right-bold color="#53B5A8" class="houseSty8" />
            </el-icon>
          </div>
        </div>

      </div>
      <div v-show="activeH === 3">
        <div class="nullKlass">
          暂无消息数据
        </div>
      </div>

    </div>
	</div>
</template>
<script setup>
import JSONBig from 'json-bigint'
import { onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import auth from "../../sys/auth";
import sphdSocket from "../../sys/sphd";
import { getHandleMsgNumber  } from '@/api/music.js'


const activeH = ref(1)
const offName = ref('')
const managerCode = ref('')
const lyricsTaskApplyMsgNum = ref('') // 歌词任务 消息数
const lyricsTaskHandleMsgNum = ref('') // 歌词任务处理 消息数
const lyricsTaskApprovalMsgNum = ref('') // 歌词任务审批 消息数
const router = useRouter();


onMounted(() => {
  let jsonData = JSONBig.parse(localStorage.getItem('jsonData'))
  console.log('jsonData=', jsonData)

  const officeItem = auth.getOrg()
  offName.value = officeItem.name
  const userInfoSys = auth.getUser()
  managerCode.value = userInfoSys.managerCode

  let actNum = Number(localStorage.getItem('activeH'))
  console.log('actNum', actNum)
  activeH.value = actNum

  getMsgNumber()


  // 歌词任务订阅
  sphdSocket.subscribe('lyricsTaskApply', lyricsTaskApplyCallBackFun, lyricsTaskApplyErrorcallback, 'user', '' )
  // 歌词任务处理订阅
  sphdSocket.subscribe('lyricsTaskHandle', lyricsTaskHandleCallBackFun,lyricsTaskHandleErrorcallback, 'user', '' )
  // 歌词任务审批订阅
  sphdSocket.subscribe('lyricsTaskApproval', lyricsTaskApprovalCallBackFun, lyricsTaskApprovalErrorcallback, 'user', '' )

})
// 获取角标
const getMsgNumber = (res) => {
  getHandleMsgNumber().then(res => {
    const data = res.data
    console.log('角标 data=', data)
    let userBadgeNumbers = data.userBadgeNumbers
    userBadgeNumbers = JSON.parse(userBadgeNumbers)
    let list = userBadgeNumbers.list
    lyricsTaskApplyMsgNum.value = list.lyricsTaskApply
    lyricsTaskHandleMsgNum.value = list.lyricsTaskHandle
    lyricsTaskApprovalMsgNum.value = list.lyricsTaskApproval


  }).catch(err => {

  })

}
// 歌词任务订阅
const lyricsTaskApplyCallBackFun = (res) => {
  console.log('lyricsTaskApplyCallBackFun res=', res)

}
const lyricsTaskApplyErrorcallback = (err) => {
   console.log('lyricsTaskApplyErrorcallback err=', err)
}

// 歌词任务处理订阅
const lyricsTaskHandleCallBackFun = (res) => {
  console.log('lyricsTaskApplyCallBackFun res=', res)

}
const lyricsTaskHandleErrorcallback = (err) => {
   console.log('lyricsTaskApplyErrorcallback err=', err)
}

// 歌词任务审批订阅
const lyricsTaskApprovalCallBackFun = (res) => {
  console.log('lyricsTaskApplyCallBackFun res=', res)

}
const lyricsTaskApprovalErrorcallback = (err) => {
   console.log('lyricsTaskApplyErrorcallback err=', err)
}




const tabNum = (num) => {
  activeH.value = num
  localStorage.setItem('activeH', num)
}
const goPage = (url) => {
  localStorage.setItem('activeIndex', 1)
  router.push({ name: url });
}
</script>
<style lang="scss" scoped>
.icS{
  margin-right: 10px;
  font-size: 20px;
  position: relative;
  top: 5px;
}
.houseSty8{
  position: relative;
  top:5px;
}
.mar30{ margin-top: 30px; }
 .handleC{
   display: flex;
   padding-top: 10px;
   background-color: #fff;
   >div{
     flex: 1;
     line-height: 30px;
     border-bottom: 2px solid #fff;
     text-align: center;
     &.activeH{
       border-bottom-color: $livePageHeadBg;
     }
   }
 }
 .nullKlass{
   line-height: 200px;
   color: #ccc;
   font-size: 26px;
   text-align: center;
 }

</style>
