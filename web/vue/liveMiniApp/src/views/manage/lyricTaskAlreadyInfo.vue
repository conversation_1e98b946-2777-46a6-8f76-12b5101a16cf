<template>
  <div class="lyricTaskAlreadyInfo">
    <Pageheader title="歌曲信息" :showBack="true"/>
    <div class="live-container">
      <div class="txtCenter" @click="goPage('lycInfo')">
        <div class="ttl">{{ playName }}</div>
        <div>
          {{ playTime }}
        </div>
      </div>
      <div>
        <div class="tr"  @click="playLyric()">
          <span>查看已制作歌词的效果</span>
          <el-icon class="live-right " >
            <arrow-right-bold color="#53B5A8" class="houseSty8" />
          </el-icon>
        </div>
        <hr>
        <div class="tr"><span>说明：</span><span>{{ songDetails.inllustration || '' }}</span></div>
        <div class="tr"><span>第一句：</span><span>{{ songDetails.firstSentence || '' }}</span></div>
        <div class="tr"><span>最后一句：</span><span>{{ songDetails.lastSentence || '' }}</span></div>
        <div class="tr"><span>需求发布：</span><span>{{ songDetails.createName || '' }} {{ new Date(songDetails.createTime ).format('yyyy-MM-dd hh:mm:ss') }}</span></div>
        <div class="tr"><span>歌词上传：</span><span>{{ cmwRhythm.createName || '' }} {{ new Date(cmwRhythm.createTime ).format('yyyy-MM-dd hh:mm:ss') }}</span></div>
      </div>
    </div>
    <LyricPlayer v-if="playLyc" :playName="playName" :lyricTxt="lyricTxt" :fileUrl="fileUrl" @closePlayLyc="closePlayLyc"></LyricPlayer>
  </div>
</template>
<script setup>
import { ref, onMounted, reactive } from 'vue';
import { useRouter } from "vue-router";
import { ElMessageBox, ElMessage } from 'element-plus';
import { getLyriTaskApplyMes } from '@/api/music.js'
import JSONBig from 'json-bigint'
import auth from '@/sys/auth'

const router = useRouter();
const playName = ref('')
const lyricTxt = ref('')
const playLyc = ref(false)
const playTime = ref('')
const fileUrl = ref('')
let songDetails = reactive('')
let cmwRhythm = reactive('')

onMounted(() => {
  getDetails()
});
// onUnmounted(() => {
//   clearInterval(this.timer)
// })
const playLyric = (type) => {

  playLyc.value = true

}
const getDetails = () => {
  let lyricDetailsItem = JSONBig.parse(localStorage.getItem('lyricTaskAlreadyItem'));
  console.log('lyricDetailsItem=', lyricDetailsItem)
  const userInfoSys = auth.getUser()
  let type = userInfoSys.roleCode === "staff" ? 2 : 1
  getLyriTaskApplyMes({
    'id': lyricDetailsItem.song,
    // 'type': type,
    // 'isSelected':0
  }).then( res => {
    console.log('详情返回值=', res)
    const data = res.data
    localStorage.setItem('songMessage', JSONBig.stringify(data))
    const song = data.song
    songDetails = song
    console.log(' songDetails.value ',  songDetails )
    cmwRhythm = data.cmwRhythm
    if(cmwRhythm){
      lyricTxt.value = cmwRhythm.rhythm
    }
    playName.value = song.name
    playTime.value = song.songDuration
    let rhythmOption = data.cmwRhythm && data.cmwRhythm.rhythmOption; // 节奏选项:1-原唱,2-伴奏
    let uploadUrl = localStorage.getItem('uploadUrl');
    if(rhythmOption === 1){ // 原唱
      fileUrl.value = uploadUrl + songDetails.originalPath
    }else { // 伴奏
      fileUrl.value = uploadUrl + songDetails.bgmPath
    }
    console.log(' fileUrl.value =',  fileUrl.value )

  }).catch(err => {
    console.log('获取小青失败', err)
  })
}
const closePlayLyc = (val)=> {
  console.log('父级的 closePlayLyc')
  playLyc.value = val
  let timer = localStorage.getItem('timer')
  console.log('取消 clearInterval(t', timer)
  clearInterval(timer)
}
// creator：hxz 播放音乐
const playMusic = ()=> {
  playLyc.value = true
}

const goPage = (url) => {
  router.push({ name: url});
}
</script>
<style lang="scss" scoped>
.lyricTaskAlreadyInfo{
  .txtCenter{
    text-align: center;
    margin-bottom: 30px;
  }
  .ttl{
    font-weight: bolder; line-height: 30px;
  }
  .tr{
    line-height: 50px;
  }
  .houseSty8{
    position: relative;
    top:16px;
  }
}
</style>
