package cn.sphd.miners.modules.authAccount.controller;

import cn.sphd.miners.common.initializer.AuthPassport;
import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.authAccount.entity.AuthAccAccount;
import cn.sphd.miners.modules.authAccount.entity.AuthAccMdse;
import cn.sphd.miners.modules.authAccount.entity.AuthAccValidPeriod;
import cn.sphd.miners.modules.authAccount.service.AuthAccAccountService;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/authAccount")
public class AuthAccAccountController {

    @Autowired
    AuthAccAccountService authAccAccountService;

    //获取私人领地商品
    @ResponseBody
    @RequestMapping("/getAccMdes.do")
    public JsonResult getAccMdes(User user) {
        AuthAccMdse authAccMdse = authAccAccountService.getAuthAccMdse();
        if (authAccMdse == null) {
            authAccMdse = authAccAccountService.addAuthAccMdse(user, authAccMdse);
        }
        Map<String, Object> map = new HashMap<>();
        map.put("authAccMdse", authAccMdse);
        return new JsonResult(1, map);
    }

    //修改私人领地商品
    @ResponseBody
    @RequestMapping("/updateAccMdes.do")
    public JsonResult updateAccMdes(AuthAccMdse authAccMdse) {
        AuthAccMdse mdse = authAccAccountService.upAuthAccMdse(authAccMdse);
        Map<String, Object> map = new HashMap<>();
        map.put("authAccMdse", mdse);
        return new JsonResult(1, map);
    }

    //点击“XXX（暂时是展会宝）”模块
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/getOpeningMessage.do")
    public JsonResult getOpeningMessage (AuthInfoDto authInfo) {
        Map<String, Object> map = authAccAccountService.getOpenAccountMes(authInfo);
        return new JsonResult(1, map);
    }


    //“知道了，开通XXX”新增试用
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/insertAuthAccountTrial.do")
    public JsonResult insertAuthAccountTrial (AuthInfoDto authInfo) {
        AuthAccAccount account = authAccAccountService.addAuthAccAccount(authInfo);
        Date trialEndDate = NewDateUtils.changeDay(account.getApplyTime(), 15);
        Date validFrom = NewDateUtils.changeDay(account.getApplyTime(), 16);
        Date validThru = NewDateUtils.changeDay(validFrom,365);
        account.setTrailEndDate(trialEndDate);
        account.setValidFrom(validFrom);
        account.setValidThru(validThru);
        Map<String, Object> map = new HashMap<>();
        map.put("account", account);
        return new JsonResult(1, map);
    }

    //计算实际支付金额
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/calculateAuthAccountFactFee.do")
    public JsonResult calculateAuthAccountFactFee (AuthInfoDto authInfo) {
        AuthAccAccount account = authAccAccountService.countFactFee(authInfo);
        Map<String, Object> map = new HashMap<>();
        map.put("account", account);
        return new JsonResult(1, map);
    }

    //获取付费记录
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/getAuthAccountPaymentRecord.do")
    public JsonResult getAuthAccountPaymentRecord (AuthInfoDto authInfo) {
        List<AuthAccValidPeriod> list = authAccAccountService.getAuthAccValidPeriodList(authInfo);
        Map<String, Object> map = new HashMap<>();
        map.put("paymentRecord", list);
        return new JsonResult(1, map);
    }


}
