package cn.sphd.miners.modules.accountantReportTax.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTriflesDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTriflesDetailDao;
import cn.sphd.miners.modules.accountantReportTax.entity.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class AcctTriflesDetailDaoImpl extends BaseDao<AcctTriflesDetailEntity, Serializable> implements AcctTriflesDetailDao {

    @Autowired
    AcctTriflesDetailDao triflesDetailDao;

    @Override
    public Map<String,Object> getTriflesReportRecord(AcctTriflesDetailEntity triflesDetailEntity) {
        Map<String,Object> result = new HashMap<String,Object>();
        String period = triflesDetailEntity.getPeriod();//按年查询
        Integer triflesId = triflesDetailEntity.getTrifles();
        Integer org = triflesDetailEntity.getOrg();
        HashMap<String, Object> param = new HashMap<>();
        List<AcctTriflesDetailEntity> list = null;
        String hql = "from AcctTriflesDetailEntity where state='1' and trifles=:trifles and period like '%" + period + "%' and org=:org";
        param.put("trifles", triflesId);
        param.put("org",org);
        list = triflesDetailDao.getListByHQLWithNamedParams(hql,param);
        for (AcctTriflesDetailEntity item : list) {
            String itemPeriod = item.getPeriod();
            String periodHql = "from AcctTriflesPeriodHistoryEntity where trifles=:trifles and triflesHistory=:triflesHistory and beginMonth=:beginMonth order by id desc";
            HashMap<String, Object> periodParam = new HashMap<>();
            periodParam.put("trifles", triflesId);
            periodParam.put("triflesHistory", item.getTriflesHistory());
            int month = Integer.parseInt(itemPeriod.substring(5,itemPeriod.length()));
            String year = itemPeriod.substring(0,4);
            periodParam.put("beginMonth", month);
            List<AcctTriflesPeriodHistoryEntity> periodlist = triflesDetailDao.getListByHQLWithNamedParams(periodHql,periodParam);
            String beginStr = "";
            String endStr = "";
            if (periodlist.size() > 0) {
                AcctTriflesPeriodHistoryEntity peiordEntity = periodlist.get(0);
                Integer beginMonth = peiordEntity.getBeginMonth();
                Integer beginDate = peiordEntity.getBeginDate();
                Integer endMonth = peiordEntity.getEndMonth();
                Integer endDate = peiordEntity.getEndDate();
                beginStr = year + "-" + String.format("%02d",beginMonth) + "-" + String.format("%02d",beginDate);
                endStr = year + "-" + String.format("%02d",endMonth) + "-" + String.format("%02d",endDate);
                item.setStartPeriod(beginStr);//应报开始时间
                item.setEndPeriod(endStr);//应该结束时间
            }
        }
        result.put("records",list);
        setPeriods(result,org,period,triflesId);
        return result;
    }

    private void setPeriods(Map<String, Object> map,Integer org,String period,Integer triflesId) {
        String hqlGetLastReportHistory = "from AcctTriflesHistoryEntity where trifles=:triflesId and org=:org and DATE_FORMAT(createDate,'%Y')<=:period order by createDate desc";
        HashMap<String, Object> paramGetLastReportHistory = new HashMap<>();
        paramGetLastReportHistory.put("triflesId", triflesId);
        paramGetLastReportHistory.put("org", org);
        paramGetLastReportHistory.put("period", period.substring(0,4));
        List<AcctTriflesHistoryEntity> reportHistorylist = triflesDetailDao.getListByHQLWithNamedParams(hqlGetLastReportHistory,paramGetLastReportHistory);
        if (reportHistorylist.size() > 0) {
            String periodListHql = "from AcctTriflesPeriodHistoryEntity where triflesHistory=" + reportHistorylist.get(0).getId();
            List<AcctTriflesPeriodHistoryEntity> periodHistoryEntityList = triflesDetailDao.getListByHQLWithNamedParams(periodListHql,null);
            map.put("period",periodHistoryEntityList);
        }

        if(reportHistorylist.size() > 0) {
            map.put("triflesName",reportHistorylist.get(0).getName());
        }
        else {
            map.put("triflesName","");
        }

    }

}
