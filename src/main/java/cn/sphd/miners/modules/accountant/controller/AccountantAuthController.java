package cn.sphd.miners.modules.accountant.controller;

import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.common.utils.WriteJSONUtil;
import cn.sphd.miners.modules.accountant.service.AccountantAuthService;
import cn.sphd.miners.modules.system.entity.Popedom;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.entity.UserPopedom;
import cn.sphd.miners.modules.system.service.OrgService;
import cn.sphd.miners.modules.system.service.RoleService;
import cn.sphd.miners.modules.system.service.UserService;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by root on 17-1-4.
 */
@Controller
@RequestMapping("/accountant")
public class AccountantAuthController {
    @Autowired
    UserService userService;
    @Autowired
    RoleService roleService;
    @Autowired
    OrgService orgService;
    @Autowired
    AccountantAuthService accountantAuthService;

    /**
     * 跳转科目设置页
     * */
    @RequestMapping("/subjectSet.do")
    public String subjectSet(User user, Model model) {
//        User user = (User) session.getAttribute("user");
        model.addAttribute("rolePopedom", getUserPopedomString(userService.getUserByID(user.getUserID())));
        return "/accountant/subjectSet";
    }

    /**
     * 跳转科目选择页
     * */
    @RequestMapping("/subjectChoose.do")
    public String subjectChoose(User user, Model model) {
//        User user = (User) session.getAttribute("user");
        model.addAttribute("rolePopedom", getUserPopedomString(userService.getUserByID(user.getUserID())));
        return "/accountant/subjectChoose";
    }

    /**
     * 跳转凭证管理
     * */
    @RequestMapping("/voucherManage.do")
    public String voucherManage(User user, Model model) {
//        User user = (User) session.getAttribute("user");
        model.addAttribute("rolePopedom", getUserPopedomString(userService.getUserByID(user.getUserID())));
        return "/accountant/billManage";
//        return "/accountant/voucherManage";
    }

    /**
     * 跳转会计录入
     * */
    @RequestMapping("/accountantImport.do")
    public String accountantImport(User user, Model model) {
//        User user = (User) session.getAttribute("user");
        model.addAttribute("rolePopedom", getUserPopedomString(userService.getUserByID(user.getUserID())));
        return "/accountant/accountantImport";
    }

    /**
     * 跳转权限设置
     * */
    @RequestMapping("/authority.do")
    public String authority(User user, Model model) {
//        User user = (User) session.getAttribute("user");
        model.addAttribute("rolePopedom", getUserPopedomString(userService.getUserByID(user.getUserID())));
        model.addAttribute("agentType", user.getAgentType());
        return "/accountant/authority";
    }


    @RequestMapping("/productionReview.do")
    public String productionReview() {
        return "/production/review";
    }

    @RequestMapping("/productionSee.do")
    public String productionSee() {
        return "/production/scan";
    }

    /**
     * 跳转结账
     * */
    @RequestMapping("/settleAccounts.do")
    public String settleAccounts(User user, Model model) {
//        User user = (User) session.getAttribute("user");
        model.addAttribute("rolePopedom", getUserPopedomString(userService.getUserByID(user.getUserID())));

        return "/accountant/settleAccounts";
    }

    /**
     * 跳转账簿管理
     * */
    @RequestMapping("/accountBook.do")
    public String accountBook(User user, Model model) {
//        User user = (User) session.getAttribute("user");
        model.addAttribute("rolePopedom", getUserPopedomString(userService.getUserByID(user.getUserID())));
        return "/accountant/accountBook";
    }

    /**
     * 跳转对公报表
     * */
    @RequestMapping("/contrayReports.do")
    public String contrayReports(User user, Model model) {
//        User user = (User) session.getAttribute("user");
        model.addAttribute("rolePopedom", getUserPopedomString(userService.getUserByID(user.getUserID())));
        return "/accountant/contraryReports";
    }

    public String getUserPopedomString(User user) {
        StringBuffer sb = new StringBuffer("'");
        for (UserPopedom um : user.getUserPopedomHashSet()) {
            sb.append(um.getMid()).append("','");
        }
        if (sb.length() > 1) {
            return sb.substring(0, sb.length() - 2);
        }
        return "";
    }

}
