<template>
  <div id="discussionBorrowDetail">
    <TY_NavTop :title="title"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <!--除了文管待处理详情和文管查询详情都如下-->
      <div v-if="params.mark !== 'borrowApproveLast' && params.mark !== '43'">
        <a class="ui-cell" @click="showDialog()" style="margin-bottom: 8px">
          <div class="ui-cell__bd">
            <div class="ui-cell_title">
              <span>{{borrowDetails.title}}</span>
            </div>
          </div>
        </a>
        <div class="panel">
          <div class="item">
            <div class="item_title">审批记录</div>
          </div>
          <div class="item">
            <div class="rightSide" >
              <div v-if="borrowProcess.length > 0">
                <span class="process_desName">申请人</span>
                <span class="process_name">{{borrowProcess[0].userName}}</span>
                <span>{{borrowProcess[0].createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
              </div>
              <div v-for="(item, index) in borrowProcess" :key="index">
                <div v-if="item.approveStatus === '2'">
                  <div v-if="item.toMid === null">
                    <span class="process_desName">审批人</span>
                    <span class="process_name">{{item.toUserName}}</span>
                    <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                  </div>
                  <div v-else>
                    <span class="process_desName">文&nbsp;&nbsp;管</span>
                    <span class="process_name">{{item.toUserName}}</span>
                    <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                  </div>
                </div>
                <div v-if="item.approveStatus === '3'">
                  <div class="color-red" v-if="item.toMid === null">
                    <span class="process_desName">审批人</span>
                    <span class="process_name">{{item.toUserName}}</span>
                    <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                  </div>
                  <div class="color-red" v-else>
                    <span class="process_desName">文&nbsp;&nbsp;管</span>
                    <span class="process_name">{{item.toUserName}}</span>
                    <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                  </div>
                  <div class="color-red">
                    <span>驳回理由</span>
                    <span>{{item.approveMemo}}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="panel" v-if="params.mark === 'borrowApproval1'">
          <span style="padding: 0 8px">是否允许该职工阅览该讨论组？请谨慎确认！</span>
          <el-form :rules="rules" :model="selectRule" class="handleFile rightSide">
            <el-form-item prop="radio">
              <el-radio-group v-model="selectRule.radio">
                <el-radio :label="1" :disabled="usNot">本人无异议，选择下一个审批人</el-radio>
                <el-form-item v-show="selectRule.radio == '1'" class="approverNext" size="mini">
                  <el-select border v-model="selectRule.nextApprover" placeholder="选择下一位审批人" @change="nextAp" :disabled="usNot">
                    <el-option
                      v-for="item in approverSelect"
                      :key="item.userID"
                      :label="item.userName"
                      :value="item.userID">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-radio :label="2">本人无异议，请文管继续审批</el-radio>
                <br/>
                <el-radio :label="3">驳回该阅览申请</el-radio>
                <el-input :disabled="usNot" v-show="selectRule.radio == '3'" type="textarea" v-model="opposeForm.approveMemo"></el-input>
              </el-radio-group>
            </el-form-item>
          </el-form>
          <div class="handle_button">
            <input type="submit" class="ui-btn ui-btn_info" value="确定" @click="toHandle()"/>
          </div>
        </div>
      </div>

      <!--文管审批的页面和其他页面差别过大，重写一份页面-->
      <div v-else>
        <a class="ui-cell" style="margin-bottom: 8px">
          <div class="ui-cell__bd">
            <div class="ui-cell_title">
              <span>{{borrowDetails.title}}</span>
            </div>
          </div>
        </a>
        <div class="panel">
          <div class="item">
            <div class="item_content">归档时间 {{borrowDetails.enabledTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
          </div>
        </div>
        <div class="panel">
          <div class="item">
            <div class="item_title">审批记录</div>
          </div>
          <div class="item">
            <div class="rightSide">
              <div v-if="borrowProcess.length > 0">
                <span class="process_desName">申请人</span>
                <span class="process_name">{{borrowProcess[0].userName}}</span>
                <span>{{borrowProcess[0].createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
              </div>
              <div v-for="(item, index) in borrowProcess" :key="index">
                <div v-if="item.approveStatus === '2'">
                  <div v-if="item.toMid === null">
                    <span class="process_desName">审批人</span>
                    <span class="process_name">{{item.toUserName}}</span>
                    <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                  </div>
                  <div v-else>
                    <span class="process_desName">文&nbsp;&nbsp;管</span>
                    <span class="process_name">{{item.toUserName}}</span>
                    <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                  </div>
                </div>
                <div v-if="item.approveStatus === '3'">
                  <div class="color-red" v-if="item.toMid === null">
                    <span class="process_desName">审批人</span>
                    <span class="process_name">{{item.toUserName}}</span>
                    <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                  </div>
                  <div class="color-red" v-else>
                    <span class="process_desName">文&nbsp;&nbsp;管</span>
                    <span class="process_name">{{item.toUserName}}</span>
                    <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                  </div>
                  <div class="color-red">
                    <span>驳回理由</span>
                    <span>{{item.approveMemo}}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="panel" v-if="params.mark === 'borrowApproveLast'">
          <span style="padding: 0 8px">是否允许该职工阅览该讨论组？请谨慎确认！</span>
          <div class="handle_button">
            <input type="submit" class="ui-btn ui-btn_error" value="驳回" @click="approve(3)"/>
            <input type="submit" class="ui-btn ui-btn_info" value="批准" @click="approve(2)"/>
          </div>
        </div>
        <a class="ui-cell" v-on:click="seeDiscuss()">
          <div class="ui-cell__bd">
            <div class="ui-cell_title">
              <span>申请阅览的讨论组</span>
            </div>
          </div>
          <div class="ui-cell__ft"><span class="color-blue">查看</span></div>
        </a>
      </div>
    </div>
  </div>
</template>

<style>
  .panel{
    font-size: 13px;
    padding: 6px 10px;
    background-color: #fafafb;
    border-radius: 3px;
    margin-bottom: 4px;
  }
  .item{
    display: flex;
  }
  .item .item_title{
    width: 70px;
    flex: none;
    color: #666;
    line-height:32px;
    padding: 0 8px;
  }
  .item .item_content{
    text-align: right;
    flex: auto;
    line-height:32px;
  }
  .item .item_mid{
    line-height:32px;
  }
  .rightSide{
    padding-left: 36px;
  }
  .rightSide .process_name{
    display: inline-block;
    width: 70px;
    margin-left: 4px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: top;
  }
  .rightSide .process_desName{
    display: inline-block;
    width: 55px;
    vertical-align: top;
  }
  .btn-group{
    flex: auto;
  }
  .aLink{
    color: #4797f1;
    line-height: 32px;
    cursor: pointer;
  }
  .aLink:hover{
    text-decoration: underline;
  }
  .title{
    font-size: 14px;
    color: #666;
  }
</style>

<script>
import { gohistory } from '../../../js/common'
import * as moment from 'moment'
import 'moment/locale/zh-cn'
export default {
  name: 'discussionBorrowDetail',
  data () {
    return {
      params: {}, // （id: 借阅id, mark: 判断来源 11、12：申请者待审批、近期可预览的文件，21、22：中间审批人待审批、近期可预览的文件，3文管待审批列表 ）
      loading: true,
      approverSelect: [],
      opposeForm: { // 驳回表单
        approveMemo: ''
      },
      selectedAp: {},
      lastApprover: {},
      borrowDetails: {
        title: ''
      },
      borrowRecord: {},
      borrowProcess: [],
      selectRule: {
        radio: '',
        nextApprover: ''
      },
      rules: {
        radio: [
          { required: true, message: '请选择下一下一个审批人', trigger: 'change' }
        ]
      },
      listenersUid: [],
      title: '讨论组阅览申请'
    }
  },
  created: function () {
    // 赋值地址栏传值（id: 借阅id， mark: 来源标记）
    this.params = this.$route.params
    console.log('borrow' + JSON.stringify(this.params))
    // 获取借阅文件详情
    this.getForumPostMessage()
    if (this.params.mark === 'borrowApproval1') {
      // 来源为中间审批人时获取其他审批人列表
      this.getNextApprover()
    }
    if (this.params.mark === 'borrowApproveLast') {
      this.title = '讨论组借阅审批'
    }
  },
  methods: {
    getForumPostMessage () {
      let that = this
      this.$http.post('../../../read/getReadForumPostMessage.do', {
        borrowId: this.params.id
      }, {
        emulateJSON: true
      }).then((response) => {
        let res = response.body.data
        let borrowDetails = res.forumPost // 讨论详情
        let borrowProcess = res.listAp // 借阅审批流程
        let borrowRecord = res.readingRoom // 借阅记录列表
        that.borrowDetails = borrowDetails
        that.borrowProcess = borrowProcess
        that.borrowRecord = borrowRecord
        that.lastApprover = borrowProcess[borrowProcess.length - 1].id
        that.loading = false
      }).catch(() => {
        that.$kiko_message('系统错误，请重试！')
      })
    },
    getNextApprover () {
      let that = this
      let params = {
        borrowId: this.params.id,
        userID: this.sphdSocket.user.userID
      }
      this.$http.post('../../../res/getAllOidUserByResource.do', params, {
        emulateJSON: true
      }).then((response) => {
        let data = response.body
        that.approverSelect = data.data.list
        that.loading = false
      }).catch(function () {
        that.$kiko_message('系统错误，请重试！')
      })
    },
    nextAp (vId) {
      this.selectedAp = this.approverSelect.find((item) => {
        return item.userID === vId // 筛选出匹配数据
      })
    },
    toHandle () {
      // 中间审批人审批
      let borrowData = {
        userID: this.sphdSocket.user.userID,
        borrowId: this.params.id,
        processId: this.lastApprover,
        type: 0, // 1代表继续选择下级审批人，2代表选择下级审批人是总务后小总务，3代表进行最终审批
        approveStatus: 0 // 2-批准 3-驳回
      }
      if (this.selectRule.radio === 1) {
        borrowData.type = 1
        borrowData.approveStatus = 2
        borrowData.toUser = this.selectedAp.userID
        borrowData.toUserName = this.selectedAp.userName
      } else if (this.selectRule.radio === 2) {
        borrowData.type = 2
        borrowData.approveStatus = 2
      } else if (this.selectRule.radio === 3) {
        borrowData.type = 1
        borrowData.approveStatus = 3
        borrowData.approveMemo = this.opposeForm.approveMemo
      }
      console.log(this.selectRule.radio)
      console.log(typeof this.selectRule.radio)
      if (this.selectRule.radio) {
        this.loading = true
        this.handleBorrowingForum(borrowData)
      } else {
        this.$kiko_message('请勾选选项！')
      }
    },
    approve: function (approveStatus) {
      // 文管审批
      let borrowData = {
        userID: this.sphdSocket.user.userID,
        borrowId: this.params.id,
        processId: this.lastApprover,
        approveStatus: approveStatus,
        type: 3 // 1代表继续选择下级审批人，2代表选择下级审批人是总务后小总务，3代表进行最终审批
      }
      if (borrowData.type === 3) {
        borrowData.approveMemo = this.opposeForm.approveMemo
      }
      let that = this
      if (approveStatus === 2) {
        this.$confirm('您确定批准吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          that.loading = true
          that.handleBorrowingForum(borrowData)
        })
      } else {
        this.$prompt('请输入驳回理由', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPlaceholder: '最多可录入50字'
        }).then(({ value }) => {
          that.loading = true
          borrowData.approveMemo = value || ''
          if (borrowData.approveMemo.length < 50) {
            that.handleBorrowingForum(borrowData)
          } else {
            that.loading = false
            that.$kiko_message('最多可录入50字')
          }
        })
      }
    },
    handleBorrowingForum: function (borrowData) {
      let that = this
      this.$http.post('../../../read/handleBorrowingForum.do', borrowData, {
        emulateJSON: true
      }).then((response) => {
        that.loading = false
        let data = response.body.data
        let status = data.status
        if (status === '1') {
          that.$kiko_message('操作成功')
          gohistory(that)
        } else {
          that.$kiko_message('借阅文件已被审批不用重复操作')
        }
      }).catch(function () {
        that.$kiko_message('系统错误，请重试！')
      })
    },
    seeDiscuss: function () {
      if (this.borrowDetails.isOpen === 1) {
        if (this.borrowDetails.isCleaned === 1) {
          window.parent.location.href = window.parent.$.webRoot + '/forum/discussionOtherIndex.do?id='+ this.borrowDetails.id
        } else {
          let msg = '该讨论组已于' + moment(this.borrowDetails.cleanTime).format('YYYY-MM-DD hh:mm:ss') + '被' + this.borrowDetails.compereName + '清除，您已无法再阅览！'
          this.$kiko_message(msg)
        }
      } else {
        this.$kiko_message('该讨论组您已无法再阅览！')
      }
    }
  }
}
</script>
