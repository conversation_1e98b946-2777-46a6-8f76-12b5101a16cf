package cn.sphd.miners.modules.dailyAffairs.service.impl;

import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.modules.dailyAffairs.dao.PersonnelReimbursePaymentDao;
import cn.sphd.miners.modules.dailyAffairs.entity.PersonnelReimbursePayment;
import cn.sphd.miners.modules.dailyAffairs.service.PaymentApprovalService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.finance.dao.FinanceAccountDao;
import cn.sphd.miners.modules.finance.entity.FinanceAccount;
import cn.sphd.miners.modules.personal.dao.ApprovalProcessDao;
import cn.sphd.miners.modules.personal.dao.PersonnelReimburseDao;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.entity.PersonnelReimburse;
import cn.sphd.miners.modules.personal.service.PersonnelReimburseService;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.entity.ApprovalItem;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.entity.UserPopedom;
import cn.sphd.miners.modules.system.service.RoleService;
import cn.sphd.miners.modules.system.service.UserPopedomService;
import cn.sphd.miners.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2019/12/27.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class PaymentApprovalServiceImpl implements PaymentApprovalService {

    @Autowired
    PersonnelReimburseDao personnelReimburseDao;
    @Autowired
    ApprovalProcessDao approvalProcessDao;
    @Autowired
    UserDao userDao;
    @Autowired
    PersonnelReimbursePaymentDao personnelReimbursePaymentDao;
    @Autowired
    FinanceAccountDao financeAccountDao;
    @Autowired
    UserPopedomService userPopedomService;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    PersonnelReimburseService personnelReimburseService;
    @Autowired
    UserService userService;
    @Autowired
    RoleService roleService;

    @Override
    public List<PersonnelReimburse> getPaymentHandle(Integer toUser,Integer fromUser,String approvalStatusReimburse, String approvalStatus,Integer businessType,Date beginDate,Date endDate) {
        Map<String,Object> params = new HashMap<>();
        String hql = "from PersonnelReimburse where approveStatus=:approvalStatusReimburse and id in (select reimburse_ from ApprovalProcess where approveStatus=:approveStatus and businessType=:businessType";
        if (toUser!=null){
            hql+=" and toUser=:toUser";
            params.put("toUser",toUser);
        }
        if (fromUser!=null){
            hql+=" and fromUser=:fromUser";
            params.put("fromUser",fromUser);
        }
        if (beginDate!=null&&endDate!=null){
            hql+=" and createDate between :beginDate and :endDate";
            params.put("beginDate",beginDate);
            params.put("endDate",endDate);
        }
        hql+=")";
        params.put("approvalStatusReimburse",approvalStatusReimburse);
        params.put("approveStatus",approvalStatus);
        params.put("businessType",businessType);
        hql+=" order by createDate desc";
        return personnelReimburseDao.getListByHQLWithNamedParams(hql,params);
    }

    @Override
    public List<PersonnelReimburse> paymentReviewApprover(Integer toUser,Integer fromUser,String approvalStatusReimburse, String approvalStatus,Date beginDate,Date endDate,Integer... businessType) {
        Map<String,Object> params = new HashMap<>();
        String hql = "from PersonnelReimburse where approveStatus=:approvalStatusReimburse";
        if (fromUser!=null){
            hql+=" and creator=:creator";
            params.put("creator",fromUser);
        }
        hql+=" and id in (select reimburse_ from ApprovalProcess where approveStatus=:approveStatus";
        if (toUser!=null){
            hql+=" and toUser=:toUser";
            params.put("toUser",toUser);
        }
        if (businessType!=null){
            hql+=" and businessType in (:businessType)";
            params.put("businessType",businessType);
        }
        if (beginDate!=null&&endDate!=null){
            hql+=" and createDate between :beginDate and :endDate";
            params.put("beginDate",beginDate);
            params.put("endDate",endDate);
        }
        hql+=")";
        if (approvalStatusReimburse.equals("1")&&approvalStatus.equals("2")){  //待付款的需要排除在待复核中待审批的
            hql+=" and id not in (select reimburse_ from ApprovalProcess where approveStatus='2'";
            if (toUser!=null){
                hql+=" and toUser=:toUser";
                params.put("toUser",toUser);
            }
            if (businessType!=null){
                hql+=" and businessType in (:businessType)";
                params.put("businessType",businessType);
            }
            if (beginDate!=null&&endDate!=null){
                hql+=" and createDate between :beginDate and :endDate";
                params.put("beginDate",beginDate);
                params.put("endDate",endDate);
            }
            hql+=")";
        }
        params.put("approvalStatusReimburse",approvalStatusReimburse);
        params.put("approveStatus",approvalStatus);
        hql+=" order by createDate desc";
        return personnelReimburseDao.getListByHQLWithNamedParams(hql,params);
    }

    /**
     *付款审批的审批（没有驳回）
     * @param userId 登录人id
     * @param approvalProcessId  审批流程id
     * @return
     */
    @Override
    public Map<String, Object> paymentHandleApproval(Integer userId, Integer approvalProcessId) {
        Map<String,Object> map = new HashMap<>();
        ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);
        PersonnelReimburse personnelReimburse = personnelReimburseDao.get(approvalProcess.getReimburse_());
        User user = userDao.get(userId);
        if ("2".equals(approvalProcess.getApproveStatus())){
            map.put("status",2);  //已批准 ，不与重复批准
            map.put("content","已批准 ，不与重复批准");  //已批准 ，不与重复批准
        }else {
            approvalProcess.setApproveStatus("2");  // 批准
            approvalProcess.setHandleTime(new Date());
            approvalProcess.setUserName(user.getUserName());
            approvalProcessDao.update(approvalProcess);

            //财务-可付款的流程（未确定付款方式时）
            ApprovalProcess process = new ApprovalProcess();
            process.setApproveStatus("1");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
            process.setLevel(approvalProcess.getLevel());
            process.setToUserName("财务处理者");
            process.setToMid("lp");  //报销受理模块
            process.setReimburse(approvalProcess.getReimburse());
            process.setCreateDate(new Date());
            process.setOrg(user.getOid());
            process.setUserName("财务处理者");
            process.setFromUser(personnelReimburse.getUser_());
            process.setHandleTime(new Date());
            process.setBusinessType(21);
            approvalProcessDao.save(process);

            List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务为权限审批
            for (UserPopedom u : userPopedomList) {
               //财务审批人可付款+1
                String contnet = personnelReimburseService.pushContent(personnelReimburse,3);
                personnelReimburseService.reimburseRejectSend(1,1,personnelReimburse, u.getUserId(), "/financePayable",contnet,contnet,"financeReimburse");

                //财务审批人待付款审批列表数据-1，角标没有
                personnelReimburseService.reimburseRejectSend(0,-1,personnelReimburse, u.getUserId(), "/cashierPaymentHandle",null,null,"financeReimburse");
            }

            //付款审批人待处理-1
            personnelReimburseService.reimburseRejectSend(-1, -1, personnelReimburse, approvalProcess.getToUser(), "/paymentHandle",null,null,"personalReimburseApproval");
            //付款审批人已批准
            personnelReimburseService.reimburseRejectSend(0, 1, personnelReimburse, approvalProcess.getToUser(), "/paymentApproval",null,null,"personalReimburseApproval");

            if (personnelReimburse.getFactUser()!=null&&personnelReimburse.getFactUser()!=0){
                //给申请人的详情页发送(状态已变更)
                personnelReimburseService.reimburseRejectSend(0, 0, personnelReimburse, personnelReimburse.getFactUser(), "/paymentDetail", null, null, "approvalSettingsApply");
            }else {
                //给申请人的详情页发送(状态已变更)
                personnelReimburseService.reimburseRejectSend(0, 0, personnelReimburse, approvalProcess.getFromUser(), "/paymentDetail", null, null, "approvalSettingsApply");
            }
            map.put("status",1);  //成功
            map.put("content","操作成功");
        }
        return map;
    }

    /**
     * 财务-出纳-待付款审批列表
     * @param oid
     * @return
     */
    @Override
    public List<PersonnelReimburse> cashierPaymentHandle(Integer oid,String approveStatus,Integer businessType) {
        Map<String,Object> params = new HashMap<>();
        String hql = "from PersonnelReimburse where approveStatus='1' and id in (select reimburse_ from ApprovalProcess where org=:oid";
        params.put("oid",oid);
        if (!MyStrings.nulltoempty(approveStatus).isEmpty()){
            hql+=" and approveStatus=:approveStatus";
            params.put("approveStatus",approveStatus);
    }
        if (businessType!=null){
            hql+=" and businessType=:businessType ";
            params.put("businessType",businessType);
        }

        hql+=") order by createDate desc";
        return personnelReimburseDao.getListByHQLWithNamedParams(hql,params);
    }

    /**
     * 可付款的审批
     * @param method  支付方式 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
     * @param planDate  计划时间
     * @param factDate  实际时间
     * @param accountId  账户id
     * @param reimburseId  报销id
     * @param approvalProcessId  审批流程id
     * @param summary  摘要
     */
    public Map<String,Object> payableApproval(Integer userId,Integer approvalProcessId,String method, Date planDate, Date factDate, Integer accountId,Integer reimburseId,String summary,Map<String,Object> map) {
        User user = userDao.get(userId);
        if (!MyStrings.nulltoempty(method).isEmpty()&&accountId!=null&&approvalProcessId!=null){
            ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);
            PersonnelReimburse personnelReimburse = personnelReimburseDao.get(approvalProcess.getReimburse_());
            personnelReimburse.setApproveLevel(approvalProcess.getLevel());
            if ("2".equals(approvalProcess.getApproveStatus())){  //
                map.put("status",2);//已批准 ，不与重复批准
                map.put("content","不可重复操作");
            }else {
                FinanceAccount financeAccount = financeAccountDao.get(accountId);  //新的账户
                BigDecimal balance = financeAccount.getBalance().subtract(personnelReimburse.getAmount());
                if (balance.compareTo(new BigDecimal(0))<0){
                    map.put("status", 3);
                    map.put("content", "账户余额不足");
                }else {
                    //新增上的可付款方式
                    PersonnelReimbursePayment personnelReimbursePayment = new PersonnelReimbursePayment();
                    personnelReimbursePayment.setMethod(method);
                    if (planDate != null) {
                        personnelReimbursePayment.setPlanDate(planDate);
                    }
                    if (factDate != null) {
                        personnelReimbursePayment.setFactDate(factDate);
                    }
                    personnelReimbursePayment.setAccountId(accountId);
                    personnelReimbursePayment.setReimburseId(reimburseId);
                    personnelReimbursePayment.setOrg(user.getOid());
                    personnelReimbursePayment.setCreator(userId);
                    personnelReimbursePayment.setCreateName(user.getUserName());
                    personnelReimbursePayment.setCreateDate(new Date());
                    personnelReimbursePayment.setPreviousId(0);
                    personnelReimbursePayment.setVersionNo(0);
                    personnelReimbursePaymentDao.save(personnelReimbursePayment);

                    if ("1".equals(method)) {   //如果是现金，则直接进账了
                        map = personnelReimburseService.reimbursementEntry(personnelReimburse, user, "1", summary, factDate,null, approvalProcess,4);  //本身带回复
                    } else {   //支付方式为银行转账

                        ApprovalItem approvalItem = new ApprovalItem();
                        if (personnelReimburse.getAuditInstance()!=null){   //复核的审批流程
                            approvalItem = roleService.getApprovalItemById(personnelReimburse.getAuditInstance());
                        }else {
                            approvalItem = roleService.getCurrentItem(user.getOid(),"paymentAudit");  //原来的可能没有，查当前的复核流程
                            personnelReimburse.setAuditInstance(approvalItem.getId());
                        }
                        if (1==approvalItem.getStatus()&&"paymentAudit".equals(approvalItem.getCode())) {   //需要付款复核
                            approvalProcess.setApproveStatus("2");
                            approvalProcess.setHandleTime(new Date());
                            approvalProcess.setUserName(user.getUserName());
                            approvalProcessDao.update(approvalProcess);

                            User user1 = userService.getUserByRoleCode(user.getOid(), "finance");  //查询有付款复核权限的审批人员(有权限管理-权限设置的财务)
                            //待复核的流程
                            ApprovalProcess process = new ApprovalProcess();
                            process.setApproveStatus("1");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
                            process.setLevel(1);
                            process.setToUser(user1.getUserID());  //审批人id
                            process.setToUserName(approvalProcess.getToUserName());  //审批人总称
                            process.setUserName(user1.getUserName()); //审批人名称
                            process.setReimburse(personnelReimburse);
                            process.setCreateDate(new Date());
                            process.setOrg(user.getOid());
                            process.setFromUser(personnelReimburse.getUser_());
                            process.setBusiness(personnelReimburse.getId());
                            process.setBusinessType(20);
                            approvalProcessDao.save(process);  //结束

                            //付款复核的审批人+1
                            String contnet = personnelReimburseService.pushContent(personnelReimburse,3);
                            personnelReimburseService.reimburseRejectSend(1, 1, personnelReimburse, user1.getUserID(), "/paymentReviewHandle", contnet, contnet, "reviewFinanceReimburse");

                            List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务为权限审批
                            for (UserPopedom u : userPopedomList) {
                                //财务可付款-1
                                personnelReimburseService.reimburseRejectSend(-1, -1, personnelReimburse, u.getUserId(), "/financePayable", null, null, "financeReimburse");

                                //财务待复核角标不加，数据+1
                                personnelReimburseService.reimburseRejectSend(0, 1, personnelReimburse, u.getUserId(), "/financeReview", null, null, "financeReimburse");
                            }
                            map.put("status", 1);
                            map.put("content", "操作成功");
                        }else {      //无需付款复核
                            //这里只剩下银行转账的方式了(确定付账的)
                            map = personnelReimburseService.reimbursementEntry(personnelReimburse, user, "3", summary,factDate, personnelReimbursePayment.getAccountId(), approvalProcess,5);

                            Integer state = (Integer) map.get("status");  //
                            if (state!=null&&1==state) {
                                List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务为权限审批
                                for (UserPopedom u : userPopedomList) {
                                    //财务可付款-1
                                    personnelReimburseService.reimburseRejectSend(-1, -1, personnelReimburse, u.getUserId(), "/financePayable", null, null, "financeReimburse");
                                }
                            }

                        }

                    }
                }
            }
        }else {
            map.put("status",0);
            map.put("content","操作失败");
        }
        return  map;
    }

    /**
     * 待复核审批
     * @param userId  登陆人
     * @param approvalProcessId  审批流程id
     * @return
     */
    @Override
    public Map<String, Object> reviewApproval(Integer userId, Integer approvalProcessId) {
        Map<String,Object> map = new HashMap<>();
        if (approvalProcessId!=null && userId!=null) {
            ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);
            if ("2".equals(approvalProcess.getApproveStatus()) || !approvalProcess.getToUser().equals(userId)) {  //
                map.put("status", 2);//已批准 ，不与重复批准
                map.put("content", "不可重复操作");
            } else {
                PersonnelReimburse personnelReimburse = personnelReimburseDao.get(approvalProcess.getReimburse_());
                User user = userDao.get(userId);
                approvalProcess.setApproveStatus("2");
                approvalProcess.setHandleTime(new Date());
                approvalProcess.setUserName(user.getUserName());
                approvalProcessDao.update(approvalProcess);

                //待付款的审批流程
                ApprovalProcess process = new ApprovalProcess();
                process.setApproveStatus("4");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
                process.setLevel(approvalProcess.getLevel());
                process.setToUserName("财务两讫者");
                process.setToMid("lp");  //报销受理模块
                process.setReimburse(approvalProcess.getReimburse());
                process.setCreateDate(new Date());
                process.setOrg(user.getOid());
                process.setUserName("财务两讫者");
                process.setFromUser(personnelReimburse.getUser_());
                process.setHandleTime(new Date());
                approvalProcessDao.save(process);

                List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务为权限审批
                for (UserPopedom u : userPopedomList) {
                    //财务审批人待两讫(待付款)+1
                    String contnet = personnelReimburseService.pushContent(personnelReimburse,3);
                    personnelReimburseService.reimburseRejectSend(1,1,personnelReimburse, u.getUserId(), "/cashierTwoSettled",contnet,contnet,"financeReimburse");

                    //财务审批人待复核审批列表数据-1，角标没有
                    personnelReimburseService.reimburseRejectSend(0,-1,personnelReimburse, u.getUserId(), "/financeReview",null,null,"financeReimburse");
                }

                //审批人付款复核-个人报销的待复核-1
                personnelReimburseService.reimburseRejectSend(-1, -1, personnelReimburse, approvalProcess.getToUser(), "/paymentReviewHandle",null,null,"reviewFinanceReimburse");
                //审批人待付款数据+1，角标不变
                personnelReimburseService.reimburseRejectSend(0, 1, personnelReimburse, approvalProcess.getToUser(), "/paymentReviewApproval",null,null,"reviewFinanceReimburse");

//                //给申请人的详情页发送(状态已变更)
//                personnelReimburseService.reimburseRejectSend(0, 0, personnelReimburse, approvalProcess.getFromUser(), "/paymentDetail",null,null,"approvalSettingsApply");

                map.put("status", 1);
                map.put("content", "操作成功");
            }
        }else {
            map.put("status", 0);
            map.put("content", "操作失败");
        }
        return map;
    }

    private List<PersonnelReimbursePayment> getPaymentMethodByReimburseId(Integer reimburseId){
        Map<String,Object> map = new HashMap<>();
        String hql = " from PersonnelReimbursePayment where reimburseId=:reimburseId";
        map.put("reimburseId",reimburseId);
        List<PersonnelReimbursePayment> personnelReimbursePayments = personnelReimbursePaymentDao.getListByHQLWithNamedParams(hql,map);
        return personnelReimbursePayments;
    }

    @Override
    public PersonnelReimbursePayment getCurrentPaymentMethod(Integer reimburseId){
        Map<String,Object> map = new HashMap<>();
        String hql = " from PersonnelReimbursePayment where reimburseId=:reimburseId order by createDate desc";
        map.put("reimburseId",reimburseId);
        PersonnelReimbursePayment personnelReimbursePayment = (PersonnelReimbursePayment) personnelReimbursePaymentDao.getByHQLWithNamedParams(hql,map);
        return personnelReimbursePayment;
    }

    @Override
    public Map<String,Object> getPaymentMethod(Integer reimburseId) {
        Map<String,Object> map = new HashMap<>();
        PersonnelReimbursePayment personnelReimbursePayment = new PersonnelReimbursePayment();
        FinanceAccount financeAccount = new FinanceAccount();

        List<PersonnelReimbursePayment> personnelReimbursePayments = this.getPaymentMethodByReimburseId(reimburseId);
        if (personnelReimbursePayments.size()>1){  //只有修改过的才显示上一次的付款方式
            personnelReimbursePayment = personnelReimbursePayments.get(0);
            financeAccount =  financeAccountDao.get(personnelReimbursePayment.getAccountId());
        }
        map.put("personnelReimbursePayment",personnelReimbursePayment);  //付款方式信息
        map.put("financeAccount",financeAccount);  //账户信息
        return map;
    }

    /**
     * 修改原定付款方式
     * @param userId  登陆人id
     * @param approvalProcessId  审批流程id
     * @param method 支付方式 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
     * @param planDate  计划时间
     * @param factDate  实际时间
     * @param accountId  账户id
     * @param type  1-待复核的修改  2-待付款的修改
     * @return
     */
    @Override
    public Map<String, Object> updatePaymentMethod(Integer userId, Integer approvalProcessId, String method, Date planDate, Date factDate,Integer accountId, Integer type,String summary) {
        Map<String,Object> map = new HashMap<String,Object>();
        if (accountId!=null&&approvalProcessId!=null&&method!=null){
            User user = userDao.get(userId);
            ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);
            PersonnelReimburse personnelReimburse = personnelReimburseDao.get(approvalProcess.getReimburse_());
            if ("1".equals(approvalProcess.getApproveStatus()) || "4".equals(approvalProcess.getApproveStatus())) {
                List<PersonnelReimbursePayment> personnelReimbursePayments = this.getPaymentMethodByReimburseId(personnelReimburse.getId());
                if (personnelReimbursePayments.size() > 0) {
                    if (accountId.equals(personnelReimbursePayments.get(personnelReimbursePayments.size()-1).getAccountId())) {
                        map.put("status", 4);
                        map.put("content", "修改的账户为原来的账户");
                    }else {
                        FinanceAccount financeAccount = financeAccountDao.get(accountId);  //新的账户
                        BigDecimal balance = financeAccount.getBalance().subtract(personnelReimburse.getAmount());
                        if (balance.compareTo(new BigDecimal(0))<0){
                            map.put("status", 3);
                            map.put("content", "账户余额不足");
                        }else {
                            if (personnelReimbursePayments.size() == 1) {  //未进行过需修改，则直接添加
                                PersonnelReimbursePayment personnelReimbursePayment = new PersonnelReimbursePayment();
                                personnelReimbursePayment.setMethod(method);
                                personnelReimbursePayment.setPlanDate(planDate);
                                personnelReimbursePayment.setFactDate(factDate);
                                personnelReimbursePayment.setReimburseId(approvalProcess.getReimburse_());
                                personnelReimbursePayment.setAccountId(accountId);
                                personnelReimbursePayment.setOrg(user.getOid());
                                personnelReimbursePayment.setCreator(userId);
                                personnelReimbursePayment.setCreateName(user.getUserName());
                                personnelReimbursePayment.setCreateDate(new Date());
                                personnelReimbursePayment.setPreviousId(personnelReimbursePayments.get(0).getId());
                                personnelReimbursePayment.setVersionNo(1);
                                personnelReimbursePaymentDao.save(personnelReimbursePayment);
                            } else if (personnelReimbursePayments.size() == 2) {  //已经进行过修改
                                PersonnelReimbursePayment personnelReimbursePayment1 = personnelReimbursePayments.get(0);
                                PersonnelReimbursePayment personnelReimbursePayment2 = personnelReimbursePayments.get(1);
                                PersonnelReimbursePayment personnelReimbursePayment = personnelReimbursePayment2;

                                personnelReimbursePayment1.setAccountId(personnelReimbursePayment.getAccountId());
                                personnelReimbursePayment1.setPlanDate(personnelReimbursePayment.getPlanDate());
                                personnelReimbursePayment1.setMethod(personnelReimbursePayment.getMethod());
                                personnelReimbursePayment1.setUpdator(userId);
                                personnelReimbursePayment1.setUpdateName(user.getUserName());
                                personnelReimbursePayment1.setUpdateDate(new Date());
                                personnelReimbursePaymentDao.update(personnelReimbursePayment1);

                                personnelReimbursePayment2.setAccountId(accountId);
                                personnelReimbursePayment2.setPlanDate(planDate);
                                personnelReimbursePayment2.setFactDate(factDate);
                                personnelReimbursePayment2.setMethod(method);
                                personnelReimbursePayment2.setUpdator(userId);
                                personnelReimbursePayment2.setUpdateName(user.getUserName());
                                personnelReimbursePayment2.setUpdateDate(new Date());
                                personnelReimbursePaymentDao.update(personnelReimbursePayment2);
                            }

                            //付款方式修改的流程
                            ApprovalProcess process1 = new ApprovalProcess();
                            process1.setApproveStatus("2");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
                            process1.setLevel(1);
                            process1.setToUser(user.getUserID());  //审批人id
                            process1.setToUserName(approvalProcess.getToUserName());  //审批人总称
                            process1.setUserName(user.getUserName()); //审批人名称
                            process1.setReimburse(personnelReimburse);
                            process1.setCreateDate(new Date());
                            process1.setOrg(user.getOid());
                            process1.setFromUser(personnelReimburse.getUser_());
                            process1.setBusiness(personnelReimburse.getId());
                            process1.setBusinessType(22);
                            process1.setHandleTime(new Date());
                            approvalProcessDao.save(process1);  //结束

                            Integer status = 0;
                            Map<String, Object> map1 = new HashMap<String, Object>();
                            if ("1".equals(method)) {
                                map1 = personnelReimburseService.reimbursementEntry(personnelReimburse, user, "1", summary, factDate,null, approvalProcess,type);
                                status = (Integer) map1.get("status");
                            }

                            if (0 == status && 2 == type) {  //如果现在操作正确，则进行一下操作----待付款进行的修改（需将待付款这里生产的审批流程进行处理，转换为待复核的审批流程）
                                if ("5".equals(method)) {
                                    //待复核的审批流程
                                    User user1 = userService.getUserByRoleCode(user.getOid(), "finance");  //查询有付款复核权限的审批人员(有权限管理-权限设置的财务)
                                    approvalProcess.setApproveStatus("1"); // 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
                                    approvalProcess.setLevel(1);
                                    approvalProcess.setToUser(user1.getUserID());  //审批人id
                                    approvalProcess.setToUserName(approvalProcess.getToUserName());  //审批人总称
                                    approvalProcess.setUserName(user1.getUserName()); //审批人名称
                                    approvalProcess.setCreateDate(new Date());
                                    approvalProcess.setOrg(user.getOid());
                                    approvalProcess.setFromUser(personnelReimburse.getUser_());
                                    approvalProcess.setBusinessType(20);
                                    approvalProcessDao.saveOrUpdate(approvalProcess);  //结束

                                    //付款复核审批人-待复核+1
                                    String contnet = personnelReimburseService.pushContent(personnelReimburse,3);
                                    personnelReimburseService.reimburseRejectSend(+1, +1, personnelReimburse, approvalProcess.getToUser(), "/paymentReviewHandle", contnet, contnet, "reviewFinanceReimburse");
                                    //付款复核审批人-待付款数据-1，角标不变
                                    personnelReimburseService.reimburseRejectSend(0, -1, personnelReimburse, approvalProcess.getToUser(), "/paymentReviewApproval", null, null, "reviewFinanceReimburse");

                                }

                                List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "lp");  //财务为权限审批
                                for (UserPopedom u : userPopedomList) {
                                    //财务审批人待两讫(待付款)-1
                                    personnelReimburseService.reimburseRejectSend(-1, -1, personnelReimburse, u.getUserId(), "/cashierTwoSettled", null, null, "financeReimburse");

                                    if (!"1".equals(method)) {
                                        //财务审批人待复核审批数据+1，角标没有
                                        personnelReimburseService.reimburseRejectSend(0, +1, personnelReimburse, u.getUserId(), "/financeReview", null, null, "financeReimburse");
                                    }
                                }
                                map.put("status", 1);
                                map.put("content", "修改成功");
                            } else if (0 == status && 1 == type) {   //在待复核进行银行转账的修改--需将创建时间进行修改
                                approvalProcess.setCreateDate(new Date());
                                approvalProcessDao.saveOrUpdate(approvalProcess);  //结束

                                map.put("status", 1);
                                map.put("content", "修改成功");
                            } else {
                                return map1;
                            }
                        }
                    }
                } else {
                    map.put("status", 0);
                    map.put("content", "修改失败");
                }
            }else {
                map.put("status", 2);
                map.put("content", "不可重复操作 ");
            }
        }else {
            map.put("status", 0);
            map.put("content", "修改失败");
        }
        return map;
    }
}
