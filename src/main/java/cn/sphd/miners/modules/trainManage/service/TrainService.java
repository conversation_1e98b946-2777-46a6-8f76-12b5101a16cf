package cn.sphd.miners.modules.trainManage.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.material.entity.RespStatus;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.trainManage.dto.*;
import cn.sphd.miners.modules.trainManage.entity.*;

import java.util.List;

/**
 * @ClassName TrainService
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/2/3 8:13
 * @Version 1.0
 */
public interface TrainService{
    List<TrainingQuestionBank> selectTrainingQuestionBankList(User user, Integer type, PageInfo pageInfo,Integer status);
    RespTrainMaterialListPage selectQuestionBankDetailById(PageInfo pageInfo, Integer id, Integer type, Integer status);
    RespTrainQuestionList selectTrainQuestionDetailById(Integer id,Integer status);
    RespTrainQuestionKeyList selectTrainQuestionDetail(Integer id, Integer status);

    String selectQuestionBankCode(int org);
    String selectTrainingMaterialCode(int id);
    String selectTrainingQuestionCode(int id);

    int addQuestionBank(User user, TrainingQuestionBank trainingQuestionBank,String trainingQuestionBankAttachment,String module);
    RespStatus addTrainingMaterial(User user, TrainingMaterial trainingMaterial);
    int addChoiceQuestion(User user,int material,String content,String optionList);
    int addTfQuestion(User user,int material,String contentList);
    int addDisableKey(User user,int question,String disableKey);
    RespTrainQuestionAndKeyList addQuestionJudge(int type,int id);

    int updateQuestionBank(User user,int id,int enabled);
    int updateTrainingMaterial(User user,int id,int enabled);
    int updateTrainingQuestion(User user,int id,int enabled);
    int updateTrainingQuestionKey(User user,int id,int enabled);
    int updateTrainingQuestionBank(User user,int id,String name,String deleteAttachmentList,String addAttachmentList,String module);

    int deleteQuestionBankJudge(User user,int id);
    int deleteTrainingMaterialJudge(User user,int id);
    int deleteTrainingQuestionJudge(User user,int id);
    int deleteDisableKeyJudge(User user,int id);

    int deleteQuestionBank(User user,int id);
    int deleteTrainingMaterial(User user,int id);
    int deleteTrainingQuestion(User user,int id);
    int deleteDisableKey(User user,int id);

    List<TrainingQuestionBank> selectTrainingQuestionBankListByKeyword(User user, Integer type, PageInfo pageInfo,String keyword,Integer status);
    RespTrainMaterialListPage selectQuestionBankDetailByKeyword(PageInfo pageInfo,int id, Integer type,Integer status,String keyword);
    RespTrainMaterialListPage selectQuestionBankInfo(int id);
    RespTrainBankHistoryList selectTrainBankHistoryList(int id);
    RespTrainBankAttachmentHistoryList selectTrainBankHistoryInfo(int id);

    void countQuestionByMaterial(int id);
    void countMaterialByBank(int id);

    TrainingQuestionBankAttachment getTrainingQuestionBankAttachment(int id);
    TrainingQuestionBankAttachmentHistory getTrainingQuestionBankAttachmentHistory(int id);
    TrainingQuestionBankHistory getTrainingQuestionBankHistory(int id);
    TrainingQuestionBank getTrainingQuestionBank(int id);

}
