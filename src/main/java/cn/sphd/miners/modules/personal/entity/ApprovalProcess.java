package cn.sphd.miners.modules.personal.entity;

import cn.sphd.miners.modules.system.entity.ApprovalFlow;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by Administrator on 2016/12/7.
 */
@Entity
@Table(name="t_sys_approval_process")
public class ApprovalProcess implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="level"   , nullable=true , unique=false)
    private Integer level;

    @Column(name="from_org"   , nullable=true , unique=false)
    private Integer fromOrg;

    @Column(name="from_user"   , nullable=true , unique=false)
    private Integer fromUser;

    @Column(name="to_org"   , nullable=true , unique=false)
    private Integer toOrg;

    @Column(name="to_role"   , nullable=true , unique=false)
    private Integer toRole;

    @Column(name="to_post"   , nullable=true , unique=false)
    private Integer toPost;

    @Column(name="to_user"   , nullable=true , unique=false)
    private Integer toUser;

    @Column(name="description"  , length=255 , nullable=true , unique=false)
    private String description;  //描述修改内容(财务修改暂用为申请事件,加班为描述,资源中心为终审的描述)

    @Column(name="approve_status"  , length=1 , nullable=true , unique=false)
    private String approveStatus;// 报销：1 - 提交 2-批准（可付款/付款方式修改的确认），3-驳回 4-待两讫(待付款) 5-已报销 【6-待接收(待票据审批) 7-已接收(待票据审批) 暂时不用】 8-财务驳回
                                    //请假加班（1.314）：1-待处理 2-批准/已批准(计划) 3-驳回 4-申报批准 9-撤回
                                    // 采购订单（1.108） ：1-提交 2-批准 3-驳回 4-撤回  5-取消  6-修改 原信息未处理的变无效
    @Column(name="approve_memo"  , length=255 , nullable=true , unique=false)
    private String approveMemo; //审批备注

    @Column(name="is_message"   , nullable=true , unique=false)
    private Boolean isMessage;

    @Column(name="message_template"  , length=255 , nullable=true , unique=false)
    private String messageTemplate;

    @Column(name = "handle_time")
    private Date handleTime;//处理时间

    @Column(name = "to_user_name")
    private String toUserName;//审批人总称

    @Column(name = "user_name")
    private String userName;//审批人名称

    @Column(name = "business")
    private Integer business;//存业务数据表id
    //1.142 徐智加
    @Column(name = "instance")
    private Integer instance;//审批实例id

    @Column(name = "business_type")
    private Integer businessType;//业务类型  1-财务修改 2-计划加班 3-请假 4-入库 5-投诉 6-申报加班, 8-提前结束请假，10-审批流程更改,11-持续改进 ,
    // 12-项目管理,13-常规借款,14-资源中心,15潜在客户审批, 16招聘管理  17-付款审批 18-特殊机构新增 19-特殊机构模块修改 20-报销的付款复核（待复核） 21-报销的可付款流程（财务的付款方式确定）
    // 22-报销财务的付款方式修改 23-采购订单审批 24-薪资宝转出的付款审批  25-薪资宝转出的可付款(财务的付款方式确定) 26-薪资宝转出的待复核 27-薪资宝转出的待付款 28-薪资宝转出的付款方式修改
    //29-阅览室  30配方修改  31考勤修改  32-待在线审批（报销的） 33-待线下审核（报销的） 34暂停供应商 35暂停采购 36-我的考勤中的考勤修改 37模板创建 38模板修改 39产品创建 40产品修改 41产品恢复使用
    //42 车务管理  43-讨论区 44-领料申请  45-待在线审批(采购的票款处理) 46-在线审核ok(采购的票款处理，即采购审批者的采购部门的票据审核) 47-待线下审核(采购的票款处理) 48-采购审批者的采购部门的付款(采购的票款处理)
    //49付款审批者-采购部门的付款(采购的票款处理,出纳的待付款审批) 50-可付款（采购的票款处理,出纳的可付款） 51-待复核（采购的票款处理,出纳与复核审批人-待复核） 52-待付款（采购的票款处理,出纳-待付款）
    // 53-付款方式修改（采购的票款处理,出纳-付款方式修改） 54-总务的考勤修改（31的状态作废） 55-特殊机构增值服务  56-特殊机构增值服务的修改 57-特殊机构内容管理  58-特殊机构暂停服务 59-特殊机构恢复服务
    //60-指派加班审批  //61添加供应商审批 //62 恢复供应商审批 63-待付款审批(1.229采购的预付款中) 64-可付款(1.229采购的预付款中) 65-待复核(1.229采购的预付款中-待复核) 66-待付款(1.229采购的预付款中)
    //67-付款方式修改(1.229采购的预付款中)  //68采购审批设置 1.232   //69-需收回的款待处理(1.231差额处理1、1.233差额处理2)  //70-多收来的款可付款(1.233差额处理2) 71-多收来的款待复核(1.233差额处理2) 72-多收来的款待付款(1.233差额处理2) 73-多收来的款付款方式修改(1.233差额处理2)
    //74-会议中间审批， 75-会议最终审批，76-会议通知签收 77-会议记录——制作  78-会议记录——审批  79-会议记录——签收
    @Column(name = "old_id")
    private Integer oldId;  //财务修改 修改前的id（本表中） (涉及到存旧数据id的可以复用)

    @Column(name = "new_id")
    private Integer newId;  //财务修改 修改中的id（历史表中的id）(涉及到存新数据id的可以复用)

    @Column(name = "finish_id")
    private Integer finishId;  //财务修改 修改完成后的id（本表中修改审批通过后的新数据id）

    @Column(name = "update_des")
    private String updateDes; // 财务修改的修改说明

    @Column(name="reason"  , length=255 , nullable=true , unique=false)
    private String reason; // 驳回理由(1.137/1.169项目多选的其他原因录入的数据，与approveSelect对应)

    @Column(name = "ask_name")
    private String askName; // 申请人

    @Column(name = "type")
    private Integer type;  //状态 1-纯现金/银行转账 2-内部非支出性转账 3-报销 4-承兑汇票和转账支票中的外部支票 5-数据来源为支出中的转账支票(内部的)6-真实投诉 7-不属我公司的投诉 8-其他原因的投诉 9-重复投诉
    //10 车辆保养,11 车辆保险

    @Column(name = "create_date")
    private Date createDate;//创建时间

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name = "to_mid")
    private String toMid;
    //审批数据:以json存申请的业务数据
    @Column(name = "approve_data")
    private String approveData;

    @Column(name = "org")
    private Integer org;//机构id

    @Column(name = "approve_select" , length=50 , nullable=true , unique=false)
    private String approveSelect;  //审批选择项:多选以逗号分隔(驳回理由多选)
                                    //1.137的待在线审核： 1-照片不清楚，或信息被遮挡，以至于无法审核 2-无法入会计帐的票据较多 3-包含公司不允许报销的票据  4-票据内容与所录入的信息不一致  5-其他原因
                                    //1.169采购的票款处理：businessType=45 1-照片不清楚，或信息被遮挡，以至于无法审核  2-无法入会计帐的票据较多 3-包含公司不允许报销的票据  4-票据内容与所录入的信息不一致  5-其他原因
                                    //businessType=48  1-该供应商提供的产品或服务有问题  2-公司资金紧张，过些日子才能付款  3-没必要提前付款  4-其他原因
    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.LAZY )
    @JoinColumn(name="approval_flow", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private ApprovalFlow approvalFlow;

    @Column(name="approval_flow"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer approvalFlow_;

    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="reimburse", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private PersonnelReimburse reimburse;

    @Column(name="reimburse"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer reimburse_;

    @Column(name="business_group"  , length=255 , nullable=true , unique=false)
    private String businessGroup; // 业务数据 组ID,多个以逗号分隔，以处理一对多业务

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    @Transient
    private PersonnelLeave personnelLeave;

    @Transient
    private PersonnelOvertime personnelOvertime;

    @Transient
    private Date createDateItem;  //加班审批设置的创建时间

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getFromOrg() {
        return fromOrg;
    }

    public void setFromOrg(Integer fromOrg) {
        this.fromOrg = fromOrg;
    }

    public Integer getFromUser() {
        return fromUser;
    }

    public void setFromUser(Integer fromUser) {
        this.fromUser = fromUser;
    }

    public Integer getToOrg() {
        return toOrg;
    }

    public void setToOrg(Integer toOrg) {
        this.toOrg = toOrg;
    }

    public Integer getToRole() {
        return toRole;
    }

    public void setToRole(Integer toRole) {
        this.toRole = toRole;
    }

    public Integer getToPost() {
        return toPost;
    }

    public void setToPost(Integer toPost) {
        this.toPost = toPost;
    }

    public Integer getToUser() {
        return toUser;
    }

    public void setToUser(Integer toUser) {
        this.toUser = toUser;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    public Boolean getMessage() {
        return isMessage;
    }

    public void setMessage(Boolean message) {
        isMessage = message;
    }

    public String getMessageTemplate() {
        return messageTemplate;
    }

    public void setMessageTemplate(String messageTemplate) {
        this.messageTemplate = messageTemplate;
    }

    public ApprovalFlow getApprovalFlow() {
        return approvalFlow;
    }

    public void setApprovalFlow(ApprovalFlow approvalFlow) {
        this.approvalFlow = approvalFlow;
    }

    public Integer getApprovalFlow_() {
        return approvalFlow_;
    }

    public void setApprovalFlow_(Integer approvalFlow_) {
        this.approvalFlow_ = approvalFlow_;
    }

    public PersonnelReimburse getReimburse() {
        return reimburse;
    }

    public void setReimburse(PersonnelReimburse reimburse) {
        this.reimburse = reimburse;
    }

    public Integer getReimburse_() {
        return reimburse_;
    }

    public void setReimburse_(Integer reimburse_) {
        this.reimburse_ = reimburse_;
    }

    public Date getHandleTime() {
        return handleTime;
    }

    public void setHandleTime(Date handleTime) {
        this.handleTime = handleTime;
    }

    public String getToUserName() {
        return toUserName;
    }

    public void setToUserName(String toUserName) {
        this.toUserName = toUserName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getBusiness() {
        return business;
    }

    public void setBusiness(Integer business) {
        this.business = business;
    }

    public Integer getInstance() {
        return instance;
    }

    public void setInstance(Integer instance) {
        this.instance = instance;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public PersonnelLeave getPersonnelLeave() {
        return personnelLeave;
    }

    public void setPersonnelLeave(PersonnelLeave personnelLeave) {
        this.personnelLeave = personnelLeave;
    }

    public PersonnelOvertime getPersonnelOvertime() {
        return personnelOvertime;
    }

    public void setPersonnelOvertime(PersonnelOvertime personnelOvertime) {
        this.personnelOvertime = personnelOvertime;
    }

    public Integer getOldId() {
        return oldId;
    }

    public void setOldId(Integer oldId) {
        this.oldId = oldId;
    }

    public Integer getNewId() {
        return newId;
    }

    public void setNewId(Integer newId) {
        this.newId = newId;
    }

    public Integer getFinishId() {
        return finishId;
    }

    public void setFinishId(Integer finishId) {
        this.finishId = finishId;
    }

    public String getUpdateDes() {
        return updateDes;
    }

    public void setUpdateDes(String updateDes) {
        this.updateDes = updateDes;
    }

    public String getAskName() {
        return askName;
    }

    public void setAskName(String askName) {
        this.askName = askName;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getToMid() {
        return toMid;
    }

    public void setToMid(String toMid) {
        this.toMid = toMid;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Date getCreateDateItem() {
        return createDateItem;
    }

    public void setCreateDateItem(Date createDateItem) {
        this.createDateItem = createDateItem;
    }

    public String getApproveSelect() {
        return approveSelect;
    }

    public void setApproveSelect(String approveSelect) {
        this.approveSelect = approveSelect;
    }

    public String getApproveData() {
        return approveData;
    }

    public void setApproveData(String approveData) {
        this.approveData = approveData;
    }

    public String getBusinessGroup() {
        return businessGroup;
    }

    public void setBusinessGroup(String businessGroup) {
        this.businessGroup = businessGroup;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public ApprovalProcess() {
    }

    public ApprovalProcess(Integer id) {
        this.id = id;
    }
}