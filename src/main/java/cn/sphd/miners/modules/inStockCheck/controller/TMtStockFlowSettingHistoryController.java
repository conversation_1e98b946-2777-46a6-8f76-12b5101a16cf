package cn.sphd.miners.modules.inStockCheck.controller;
import cn.sphd.miners.modules.inStockCheck.entity.TMtStockFlowSetting;
import cn.sphd.miners.modules.inStockCheck.entity.TMtStockFlowSettingHistory;
import cn.sphd.miners.modules.inStockCheck.service.impl.TMtStockFlowSettingHistoryServiceImpl;
import org.springframework.web.bind.annotation.*;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
* (t_mt_stock_flow_setting_history)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/mfsh")
public class TMtStockFlowSettingHistoryController {
/**
* 服务对象
*/
    @Autowired
    private TMtStockFlowSettingHistoryServiceImpl tMtStockFlowSettingHistoryServiceImpl;

    /**
    * 通过主键查询单条数据
    *
    * @param id 主键
    * @return 单条数据
    */
    @GetMapping("selectOne")
    public TMtStockFlowSettingHistory selectOne(Integer id) {
    return tMtStockFlowSettingHistoryServiceImpl.selectByPrimaryKey(id);
    }

    @RequestMapping("/histories")
    public List<TMtStockFlowSettingHistory> getHistories(Integer id) {
        return tMtStockFlowSettingHistoryServiceImpl.getHistoriesByFlowId(id);
    }

}
