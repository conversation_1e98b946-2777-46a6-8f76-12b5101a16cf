import auth from '@/sys/auth'
import sphdSocket from '@/sys/sphd'
import router from '@/router/index'
import store from '@/store/index'
import moment from 'moment'
import {nanoid} from 'nanoid'

// import JSONBig from 'json-bigint'

export function findActivePage (pageName) {
  // if(JSONBig.parse(localStorage.getItem('wonderssPersistedstate'))?.dataList?.activePage!=null) {
  //   console.log('findActivePage wonderssPersistedstate localStorage length/obj ', JSONBig.parse(localStorage.getItem('wonderssPersistedstate'))?.dataList?.activePage.length)//, JSONBig.parse(localStorage.getItem('wonderssPersistedstate'))?.dataList?.activePage)
  // } else {
  //   console.log('findActivePage wonderssPersistedstate localStorage', localStorage.getItem('wonderssPersistedstate'))
  // }
  let userId = auth.getUserID()
  let activePage = store.state.activePage
  // console.log('findActivePage length findIndex wonderssPersistedstate localStorage', activePage.length)
  let index = activePage.findIndex(page => pageName==page.menuInfo.pageName)
  // console.log('findActivePage index1findIndex wonderssPersistedstate localStorage', index, pageName)
  if(index >= 0 ){
    let page = activePage[index]
    if(page.userId === userId) {
      return index
    } else {
      checkActivePageByUserId(activePage, userId)
    }
  }
  return -1
}

export function getShowthisPage(unchecked) {
  let activePage = store.state.activePage
  let index = activePage.findIndex(page => page.showThis)
  if(index >= 0 ){
    let page = activePage[index]
    if(unchecked===true) {
      return page
    } else {
      let userId = auth.getUserID()
      if(page.userId === userId) {
        return page
      } else {
        checkActivePageByUserId(activePage, userId)
      }
    }
  }
  return null
}

function checkActivePageByUserId(activePage, userId) {
  activePage = activePage.filter(page=>page.userId===userId)
  store.dispatch('setActivePage', activePage)
}
export function findActivePageByMid (mid) {
  let userId = auth.getUserID()
  let activePage = store.state.activePage
  let index = activePage.findIndex(page => mid==page.menuInfo.mid)
  if(index >= 0 ){
    let page = activePage[index]
    if(page.userId === userId) {
      return index
    } else {
      activePage.splice(index,1)
    }
  }
  return -1
}

export function initActivePage (menuInfo, showThis) {
  let userId = auth.getUserID()
  let activePage = store.state.activePage
  let index, page = (index = findActivePage(menuInfo.pageName)) >= 0 ? activePage[index] : null
  if (page == null || showThis && page.showThis != showThis) {//总要有点不一样，或者新增或者切换
    let activePage = store.state.activePage
    if (showThis) {
      hideAll(activePage)
    }
    if (page == null) {
      menuInfo.url ??= auth.getPackageName()
      menuInfo.navId ??= nanoid()
      if(menuInfo.url == auth.getPackageName() && !menuInfo.pageName) {
        console.error('新窗口传入的menuInfo对象的url和pageName不能都为空！')
      }
      page = {'menuInfo': menuInfo, data: null, userId: userId, showThis: showThis || activePage.length <= 0}
      activePage.push(page)
    } else {
      page.showThis = showThis
    }
    return true
  } else {
    return false //啥都没改
  }
}

export function showActivePage (index) {
  let userId = auth.getUserID()
  if (index >=0) {
    let activePage = store.state.activePage
    hideAll(activePage)
    let page = activePage[index]
    if(page && page.userId === userId ) {
      page.showThis = true
    } else {
      activePage.splice(index,1)
    }
  }
}

export function removeActivePage (index) {
  let activePage = store.state.activePage
  let length = activePage.length
  if (index >=0 && index < length) {
    let isShow = activePage[index].showThis
    activePage.splice(index,1)
    if(isShow) {
      if(index < length-1) {
        showActivePage(index)
      } else {
        showActivePage(index-1)
      }
    }
  }
}

export function removeAllActivePage () {
  let activePage = store.state.activePage
  let index, page = (index = findActivePage('desktop')) >= 0 ? activePage[index] : null
  let pages = []
  if(page) {
    pages.push(page)
  }
  store.state.activePage = pages
}

export function beforeRouteLeave(to, from, next, scopeThis) {
  let activePage = store.state.activePage
  let index, page = (index = findActivePage(from.name)) >= 0 ? activePage[index] : null
  if(page) {
    page.data = scopeThis.$data
    page.liveTime = moment().valueOf()
  }
  sphdSocket.unsubscribeAll(sphdSocket.level.page)
  next()
}

export function getActiveData(scopeThis) {
  return new Promise (resolve  => {
    let activePage = store.state.activePage
    let index, page = (index = findActivePage(scopeThis.pageName)) >= 0 ? activePage[index] : null
    if (page && page.userId == auth.getUserID()) {
      let data = page.data
      if (data) {
        // console.log('fromNow',moment(page.liveTime).fromNow(), JSONBig.stringify(data))
        if (page?.menuInfo?.pageName != 'desktop' && page.liveTime && moment.duration(moment().diff(page.liveTime)).minutes() >= 3) {
          scopeThis.$confirm('离开窗口已经超过3分钟<br />是否需要重新加载数据!', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info',
            dangerouslyUseHTMLString: true
          }).then(() => {
            resolve(null)
          }).catch(() => {
            fillData(scopeThis, data)
            resolve(data)
          })
        } else {
          fillData(scopeThis, data)
          resolve(data)
        }
      } else {
        fillData(scopeThis, data)
        resolve(data)
      }
    } else {
      resolve(null)
    }
  })
}

function hideAll(activePage) {
  activePage.forEach(page => {
    page.showThis = false
  })
}
function fillData(scopeThis, data) {
  for (let key in data) {
    scopeThis.$data[key] = data[key]
  }
}
export function jumpToOldPage(curUrl) {
  // console.log('jumpToOldPage', auth.webRoot, curUrl)
  window.location.href = auth.webRoot + (curUrl.startsWith('..') ? curUrl.substring(2) : (curUrl.startsWith('/') ? curUrl : '/'+curUrl))
}

export function jumpToMenu(menuInfo){
  if(menuInfo.pageName){
    router.push(menuInfo.pageName)
  }else{
    jumpToOldPage(menuInfo.url)
  }
}

// 页面初始化方法，写在 created 里或者 mounted 里
// 用于初始化页面数据
// params：
// ------ menuInfo : {mid: '菜单的mid', name: 'tab栏显示的名称', pageName: '一般与data里的pageName一致，最好是vue的名字，见名知意'}
// ------ loadDataFun : 所有的首次进入需要初始化的数据都写在这个方法里
// ------ scopeThis : 当前作用域
export function initNav(menuInfo, loadDataFun, scopeThis){
  initActivePage(menuInfo, menuInfo?.pageName != 'desktop')
  if(scopeThis!=null) {
    getActiveData(scopeThis).then(data => {
      if (!data && loadDataFun!=null) {
        loadDataFun()
      }
    })
  }
}