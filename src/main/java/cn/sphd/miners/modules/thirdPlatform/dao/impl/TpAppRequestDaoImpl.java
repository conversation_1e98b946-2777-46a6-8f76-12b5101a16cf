package cn.sphd.miners.modules.thirdPlatform.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.thirdPlatform.dao.TpAppRequestDao;
import cn.sphd.miners.modules.thirdPlatform.entity.TpAppRequest;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class TpAppRequestDaoImpl extends BaseDao<TpAppRequest, Serializable> implements TpAppRequestDao {
}
