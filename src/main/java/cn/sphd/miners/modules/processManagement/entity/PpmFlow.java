package cn.sphd.miners.modules.processManagement.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by lyx on 2023/5/9
 * 生产_装配流程表  1.251工序1
 */
@Entity
@Table(name="t_ppm_flow")
public class PpmFlow implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;   //ID

    @Column(name="org"   , nullable=true , unique=false)
    private Integer org;   //机构ID

    @Column(name="process"   , nullable=true , unique=false)
    private Long process;   //工序ID

    @Column(name="product"   , nullable=true , unique=false)
    private Integer product;   //产品ID

    @Column(name="activity"   , nullable=true , unique=false)
    private Long activity;   //活动ID

    @Column(name="parent"   , nullable=true , unique=false)
    private Long parent;   //父活动ID【上一级的id】

    @Column(name="level"   , nullable=true , unique=false)
    private Integer level;   //层级

    @Column(name="orders"   , nullable=true , unique=false)
    private Integer orders;   //同级排序

    @Column(name="path"   , nullable=true , unique=false)
    private String path;   //树结构路径:存activity的id,给逗号分隔

    @Column(name="is_leaf")
    private Boolean isLeaf; //叶子节点标识:true-叶子

    @Column(name="enabled")
    private Boolean enabled; //启用标志:true-启用(默认),false-停用

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;   //备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;   //创建人id

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;  //创建人

    @Column(name="create_time"   , nullable=true , unique=false)
    private Date createTime;   //创建时间

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;   //修改人id

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;   //修改人

    @Column(name="update_time"   , nullable=true , unique=false)
    private Date updateTime;   //修改时间

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private Byte operation;  //操作:1-增,2-删,3-改,4-暂停,5-恢复

    @Column(name="previous_id"  , nullable=true , unique=false)
    private Long previousId;  //修改前记录ID

    @Column(name="version_no"  , nullable=true , unique=false)
    private Integer versionNo;  //版本号,每次修改+1

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Long getProcess() {
        return process;
    }

    public void setProcess(Long process) {
        this.process = process;
    }

    public Integer getProduct() {
        return product;
    }

    public void setProduct(Integer product) {
        this.product = product;
    }

    public Long getActivity() {
        return activity;
    }

    public void setActivity(Long activity) {
        this.activity = activity;
    }

    public Long getParent() {
        return parent;
    }

    public void setParent(Long parent) {
        this.parent = parent;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Boolean getIsLeaf() {
        return isLeaf;
    }

    public void setIsLeaf(Boolean isLeaf) {
        this.isLeaf = isLeaf;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Byte getOperation() {
        return operation;
    }

    public void setOperation(Byte operation) {
        this.operation = operation;
    }

    public Long getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Long previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
}
