package cn.sphd.miners.modules.material.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 材料_计量单位最近使用表
 * 20200731 1.119计量单位 新增
 * 此表只存模块的最新访问记录(t_mt_unit_latest)表实体类
 * @date 2020-08-23 17:37:11
 */
@Entity(name = "MtUnitLatest")
@Table(name = "t_mt_unit_latest")
public class MtUnitLatest implements Serializable {

    //ID
    private Integer id;

    //模块代码:01-商品录入,02-构成管理,03-配方管理,04-材料录入
    private String module;

    //计量单位ID
    private Integer unit;

    //说明
    private String memo;

    //创建人id
    private Integer creator;

    //创建人
    private String createName;

    //创建时间
    private Date createDate;

    //修改人id
    private Integer updator;

    //修改人
    private String updateName;

    //修改时间
    private Date updateDate;

    //操作:1-增,2-删,3-修改
    private String operation;

    //修改前记录ID
    private Integer previousId;

    //版本号,每次修改+1
    private Integer versionNo;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "module")
    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    @Column(name = "unit")
    public Integer getUnit() {
        return unit;
    }

    public void setUnit(Integer unit) {
        this.unit = unit;
    }

    @Column(name = "memo")
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Column(name = "creator")
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    @Column(name = "create_name")
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Column(name = "create_date")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "updator")
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    @Column(name = "update_name")
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Column(name = "update_date")
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Column(name = "operation")
    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    @Column(name = "previous_id")
    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    @Column(name = "version_no")
    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

}