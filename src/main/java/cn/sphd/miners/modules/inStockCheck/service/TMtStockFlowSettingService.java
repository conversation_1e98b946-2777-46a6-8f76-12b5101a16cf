package cn.sphd.miners.modules.inStockCheck.service;

import cn.sphd.miners.modules.inStockCheck.entity.TMtStockFlowSetting;
import cn.sphd.miners.modules.system.entity.User;

public interface TMtStockFlowSettingService {

    int deleteByPrimaryKey(Integer id);

    int insert(TMtStockFlowSetting record);

    int insertSelective(TMtStockFlowSetting record);

    TMtStockFlowSetting selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(TMtStockFlowSetting record, User user);

    int updateByPrimaryKey(TMtStockFlowSetting record);

}
