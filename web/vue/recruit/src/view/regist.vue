<template id="register">
  <div class="register">
    <van-nav-bar
      title="注册"
      left-arrow
      @click-left="onClickLeft"
    />
    <div class="regist_container">
      <div class="regist_icon">
        <van-image
          width="60px"
          height="60px"
          fit="cover"
          position="center"
          :src="img"
        />
      </div>
      <div class="regist_title">
        注册Wonderss账号
      </div>
      <van-cell-group inset>
        <van-field
          v-model="registForm.phone"
          name="用户名"
          label="用户名"
          placeholder="请输入中国大陆地区的手机号"
          v-validate="'required|mobile'"
          :error="errors.has('mobile')"
          :error-message="errors.first('mobile')"
        />
        <van-field
          v-model="registForm.verificationCode"
          center
          clearable
          label="验证码"
          placeholder="请输入验证码"
        >
          <template #button>
            <van-button size="small" type="primary" color="#3cb0a7" @click="getCode" :disabled="btnDisabled">{{btnLabel}}</van-button>
          </template>
        </van-field>
      </van-cell-group>
      <div style="margin: 16px;">
        <van-button block type="primary" color="#3cb0a7" @click="onSubmit">
          填写完毕，立即注册
        </van-button>
        <van-checkbox v-model="checked" shape="square" style="margin: 8px 0;"><span class="agreeXieyi">同意 <span class="color-blue">《用户协议》</span> 和 <span class="color-blue">《隐私政策》</span></span></van-checkbox>
      </div>
    </div>
  </div>
</template>

<style>
html{
  background: #f2f2f2;
  height: 100%;
}
body{
  padding:0;
  margin: 0;
}
.regist_title{
  margin: 20px;
  text-align: center;
}
.regist_icon{
  text-align: center;
  margin: 32px 0 20px 0;
}
.agreeXieyi{
  font-size: 14px;
  margin: 8px 0;
}
.agreeXieyi .color-blue{
  color: #1989fa;
}
.van-checkbox__icon .van-icon {
  background: #fff;
}
</style>

<script>
// wyu：引相关的库
import img from '../assets/wonderss_logo.png'
export default {
  name: 'resumeRecord',
  data () {
    return {
      registForm: {
        phone: '',
        verificationCode: ''
      },
      img: img,
      checked: false,
      seconds: 60,
      btnLabel: '获取验证码',
      btnDisabled: false
    }
  },
  methods: {
    onSubmit () {
      this.$toast.loading({
        message: 'Loading...',
        forbidClick: true,
        loadingType: 'spinner'
      })
      this.$validator.validateAll().then((result) => {
        console.log(result)
        let that = this
        if (result) {
          this.$http.post(this.parent.$.webRoot + '/sendMessage/checkVerificationCode.do', this.registForm, {
            emulateJSON: true
          }).then((response) => {
            console.log(response.body)
            let data = response.body
            if (data) {
            } else {
              that.$toast.fail('提交失败！')
            }
          }).catch(function (res) {
            console.log('res', res)
            that.loading = false
            that.$toast.fail('系统错误，请重试！')
          })
        } else {
          this.$toast('请输入必填项！')
        }
      })
      this.$router.push({
        path: 'home',
        name: 'home'
      })
    },
    onClickLeft () {
      this.$router.go(-1)
    },
    getCode () {
      this.countdown(60)
      // let that = this
      // this.$http.post(this.parent.$.webRoot + "/auth/sendMessageVerificationCode.do", {
      //   phone: this.registForm.phone
      // }, {
      //   emulateJSON: true
      // }).then((response) => {
      //   console.log(response.body)
      //   let data = response.body
      //   let success = data.success
      //   let state = data.data
      //   if (Number(success) === 1) {
      //     switch (Number(state)) {
      //       case 1:
      //         selector.parents(".form").find(".error").html('')
      //         break;
      //       case 2:
      //       case 3:
      //       case 4:
      //         selector.parents(".form").find(".error").html('请输入正确的手机号')
      //         break;
      //       default:
      //         selector.parents(".form").find(".error").html('')
      //     }
      //   }
      // }).catch(function (res) {
      //   console.log('res', res)
      //   that.loading = false
      //   that.$toast.fail('系统错误，请重试！')
      // })
    },
    countdown (time) {
      if (time > 1) {
        time--
        this.btnLabel = time + '秒后可重新获取'
        this.btnDisabled = true
        // 定时1秒调用一次
        let that = this
        setTimeout(function () {
          that.countdown(time)
        }, 1000)
      } else {
        this.btnLabel = '获取验证码'
        this.btnDisabled = false
      }
    }
  }
}
</script>
