package cn.sphd.miners.modules.accountantReportTax.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.accountantReportTax.dao.*;
import cn.sphd.miners.modules.accountantReportTax.dto.AcctTaxDto;
import cn.sphd.miners.modules.accountantReportTax.dto.AlreadyDeclaredTaxDto;
import cn.sphd.miners.modules.accountantReportTax.dto.PayTaxDto;
import cn.sphd.miners.modules.accountantReportTax.entity.*;
import cn.sphd.miners.modules.system.entity.User;
import org.hibernate.query.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;

@Repository
public class AcctTaxDetailDaoImpl extends BaseDao<AcctTaxDetailEntity, Serializable> implements AcctTaxDetailDao {

    @Autowired
    AcctTaxDetailDao taxDetailDao;
    @Autowired
    AcctTaxReportDao reportDao;
    @Autowired
    AcctTaxPeriodHistoryDao periodHistoryDao;
    @Autowired
    AcctTaxDetailDao detailDao;
    @Autowired
    AcctTaxDetailHistoryDao taxDetailHistoryDao;
    @Autowired
    AcctTaxPeriodDao periodDao;

    @Override
    public List<AcctTaxDetailEntity> getListByDeclareDate(int reportId, String period) {
//        String hql = "from AcctTaxDetailEntity where report=:report and TO_DATE(delareTime,'yyyy-MM-dd')=TO_DATE(:date,'yyyy-MM-dd')";
        String hql = "from AcctTaxDetailEntity where report=:report and period=:period";
//        Date date = NewDateUtils.dateFromString(latestDeclare, "yyyy-MM-dd");
        HashMap<String, Object> param = new HashMap<>();
        param.put("report", reportId);
        param.put("period", period);
        List<AcctTaxDetailEntity> list = taxDetailDao.getListByHQLWithNamedParams(hql,param);
        return list;
    }

    /**
     * 更新成最新的报税时间,reportHistory
     * */
    @Override
    public int updateByReportId(AcctTaxDetailEntity taxDetailEntity) {
//        String hql = "update AcctTaxDetailEntity d set d.state='1',d.delareTime=:date,d.operation='3',d.updateDate=:curDate where d.report=:report and TO_DATE(delareTime,'yyyy-MM')=TO_DATE(:date,'yyyy-MM')";
        String hql = "update AcctTaxDetailEntity d set d.state='1',d.delareTime=:delareTime,d.operation='3',d.updateDate=:curDate,d.reportHistory=:reportHistory where d.report=:report and period=:period";
        HashMap<String, Object> param = new HashMap<>();
        Integer reportId = taxDetailEntity.getReport();
        String period = taxDetailEntity.getPeriod();
        Date delareTime = taxDetailEntity.getDelareTime();
        param.put("report", reportId);
        param.put("period", period);
        param.put("delareTime", delareTime);
        param.put("curDate", new Date());
        param.put("reportHistory", taxDetailEntity.getReportHistory());
        int res = taxDetailDao.queryHQLWithNamedParams(hql,param);
        return res;
    }

    /**
     * 报税后更新taxHistory和factAmount
     * */
    @Override
    public int updateByTaxId(AcctTaxDetailEntity taxDetailEntity) {
//        String hql = "update AcctTaxDetailEntity d set d.factAmount=:factAmount,d.operation='3' where d.report=:report and d.delareTime=:delareTime and d.tax=:tax";
        String hql = "update AcctTaxDetailEntity d set d.factAmount=:factAmount,d.operation='3',d.delareTime=:delareTime where d.report=:report and d.period=:period and d.tax=:tax";
        HashMap<String, Object> param = new HashMap<>();
        Integer reportId = taxDetailEntity.getReport();
        String period = taxDetailEntity.getPeriod();
        Date delareTime = taxDetailEntity.getDelareTime();
//        Date date = taxDetailEntity.getDelareTime();
        Integer taxId = taxDetailEntity.getTax();
        Integer taxHisId = taxDetailEntity.getTaxHistory();
        BigDecimal factAmount = taxDetailEntity.getFactAmount();
        param.put("factAmount", factAmount);
        param.put("delareTime", delareTime);
        param.put("report", reportId);
        param.put("period", period);
        param.put("tax", taxId);
//        param.put("taxHistory", taxHisId);
        int res = taxDetailDao.queryHQLWithNamedParams(hql,param);
        return res;
    }

    @Override
    public Map<String,Object> getReportTaxRecord(AcctTaxDetailEntity taxDetailEntity,int type) {
        String period = taxDetailEntity.getPeriod();//按年查询
        Integer reportId = taxDetailEntity.getReport();
        Integer taxId = taxDetailEntity.getTax();
        Integer org = taxDetailEntity.getOrg();
        HashMap<String, Object> param = new HashMap<>();
        List<AcctTaxDetailEntity> list = null;
        Map<String,Object> result = new HashMap<String,Object>();
        String hql = "";
        if (type == 1) {//报表的报税记录
            hql = "from AcctTaxDetailEntity where state='1' and report=:report and period like '%" + period + "%' and tax=null and org=:org group by DATE_FORMAT(delareTime,'%Y-%m-%d') order by createDate desc";
            param.put("report", reportId);
            param.put("org",org);
            list = taxDetailDao.getListByHQLWithNamedParams(hql,param);
            result.put("records",list);
            //设置指定年的period列表
            setPeriods(result,org,period,reportId);
            return result;
        }
        else if (type == 2) {//税种的报税记录
            hql = "from AcctTaxDetailEntity where state='1' and tax=:tax and period like '%" + period + "%' and org=:org";
            param.put("tax", taxId);
            param.put("org",org);
            list = taxDetailDao.getListByHQLWithNamedParams(hql,param);
            result.put("records",list);

//            if (list.size() > 0) {
//                reportId = list.get(0).getReport();
//                setPeriods(result,org,period,reportId);
//            }
            setPeriods(result,org,period,reportId);
            return result;
        }
        return null;
    }

    private void setPeriods(Map<String, Object> map,Integer org,String period,Integer reportId) {
        String hqlGetLastReportHistory = "from AcctReportHistoryEntity where report=:report and org=:org and DATE_FORMAT(createDate,'%Y')<=:period order by createDate desc";
//        String hqlGetLastReportHistory = "from AcctReportHistoryEntity where report=:report and org=:org  order by createDate desc";
        HashMap<String, Object> paramGetLastReportHistory = new HashMap<>();
        paramGetLastReportHistory.put("report", reportId);
        paramGetLastReportHistory.put("org", org);
        paramGetLastReportHistory.put("period", period.substring(0,4));
        List<AcctReportHistoryEntity> reportHistorylist = taxDetailDao.getListByHQLWithNamedParams(hqlGetLastReportHistory,paramGetLastReportHistory);
        if (reportHistorylist.size() > 0) {
            String periodListHql = "from AcctTaxPeriodHistoryEntity where reportHistory=" + reportHistorylist.get(0).getId();
            List<AcctTaxPeriodHistoryEntity> periodHistoryEntityList = periodHistoryDao.getListByHQLWithNamedParams(periodListHql,null);
            map.put("period",periodHistoryEntityList);
        }
//        Query query = taxDetailDao.getSession().createQuery(hqlGetLastReportHistory.toString());
//        taxDetailDao.setParams(query, paramGetLastReportHistory);
//        query.setMaxResults(1);
//        List<AcctReportHistoryEntity> reportHistorylist = query.list();

//        for (AcctReportHistoryEntity arhe : reportHistorylist) {
//            String periodListHql = "from AcctTaxPeriodHistoryEntity where reportHistory=" + arhe.getId();
//            List<AcctTaxPeriodHistoryEntity> periodHistoryEntityList = periodHistoryDao.getListByHQLWithNamedParams(periodListHql,null);
//            map.put("period",periodHistoryEntityList);
//        }

        if(reportHistorylist.size() > 0) {
            map.put("reportName",reportHistorylist.get(0).getName());
        }
        else {
            map.put("reportName","");
        }

    }

    /**
     * 获取当前已经申报的报表列表
     * */
    @Override
    public List<AcctReportHistoryEntity> getReportedList(User user, int year, int month) {
        String str = "" + year + "-" + String.format("%02d",month);
//        String hql = "from AcctReportEntity report,AcctTaxPeriodEntity period where report.org=:org and report.latestDeclare like '%" + str + "%' and period.beginMonth=:beginMonth";
        String hql = "select rh from AcctReportHistoryEntity rh,AcctTaxDetailEntity td where rh.id=td.reportHistory and td.period=:period and td.state='1' and td.tax=null and rh.org=:org";
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", user.getOid());
//        param.put("beginMonth", month);
        param.put("period", str);
        List<AcctReportHistoryEntity> list = taxDetailDao.getListByHQLWithNamedParams(hql,param);
        return list;

    }

    /**
     * 获取该报表的税种列表
     * */
    @Override
    public List<AcctTaxDto> getTaxDetailByReport(AcctReportHistoryEntity reportHistoryEntity, int year, int month) {

//        String hql = "select " +
//                "new cn.sphd.miners.modules.accountantReportTax.dto.AcctTaxDto(" +
//                "tax.id,taxDetail.report,tax.name,tax.updateDate,taxDetail.planAmount,taxDetail.factAmount) " +
//                "from " +
//                "AcctTaxEntity tax," +
//                "AcctTaxDetailEntity taxDetail" +
//                " where " +
//                "tax.id=taxDetail.tax AND " +
//                "taxDetail.report=:id " +
//                "AND (taxDetail.delareTime=null OR TO_DATE(taxDetail.delareTime,'yyyy-MM')=:date)";

        String hql = "select " +
                "new cn.sphd.miners.modules.accountantReportTax.dto.AcctTaxDto(" +
                "tax.id,td.report,tax.name,tax.updateDate,td.planAmount,td.factAmount) " +
                "from " +
                "AcctTaxHistoryEntity tax," +
                "AcctTaxDetailEntity td" +
                " where " +
                "tax.id=td.taxHistory AND " +
                "td.report=:report " +
                "AND (td.delareTime=null OR DATE_FORMAT(td.delareTime,'%Y-%m')=:date)";


        String str = "" + year + "-" + String.format("%02d",month);
//        Date date = NewDateUtils.dateFromString(str, "yyyy-MM");
        HashMap<String, Object> param = new HashMap<>();
//        Integer id = reportHistoryEntity.getId();
        Integer report = reportHistoryEntity.getReport();
        param.put("report", report);
        param.put("date", str);
        List<AcctTaxDto> list = taxDetailDao.getListByHQLWithNamedParams(hql,param);
        for (AcctTaxDto taxDto : list) {
            AcctReportHistoryEntity rhe = new AcctReportHistoryEntity();
            rhe.setId(reportHistoryEntity.getId());
            rhe.setReport(reportHistoryEntity.getReport());
            rhe.setName(reportHistoryEntity.getName());
            rhe.setPeriodHistoryEntity(reportHistoryEntity.getPeriodHistoryEntity());
            taxDto.setReportEntity(rhe);
        }
        reportHistoryEntity.setTaxDtoList(list);
        return list;

    }


    /**
     * 获取该月应报税的报表list
     * 应报报表
     * */
    @Override
    public List<AcctReportHistoryEntity> getReportListByMonth(User user, int year, int month) {

        String hql = "select " +
                "new AcctReportHistoryEntity(rh.id,rh.report,rh.name,rh.frequency,rh.createName,rh.createDate,ph) " +
                "from " +
                "AcctReportHistoryEntity rh," +
                "AcctTaxPeriodHistoryEntity ph," +
                "AcctTaxDetailEntity td" +
                " where " +
                " rh.id=td.reportHistory and rh.id=ph.reportHistory and rh.org=:org and ph.beginMonth=:beginMonth and YEAR(rh.createDate)<=:year";

        HashMap<String, Object> param = new HashMap<>();
        param.put("org", user.getOid());
        param.put("beginMonth", month);
        param.put("year", year);
        //如果year大于当前年，或者月份大于当前月份说明要查未来应报数据，需要加上enabled=1的条件
        Date date = new Date();
        int curyear = NewDateUtils.getYear(date);
        int curmonth = NewDateUtils.getMonth(date);

        if (year > curyear) {
            hql += " AND rh.enabled=1 ";
        }
        else if (year == curyear && month > curmonth) {
            hql += " AND rh.enabled=1 ";
        }
        List<AcctReportHistoryEntity> list = taxDetailDao.getListByHQLWithNamedParams(hql,param);
        return list;
    }

    /**
     * PC端录入税款后需要给税种的实际金额赋值
     * org
     * taxId            税种ID
     * report           报表ID
     * factAmount       实际金额
     * period           报表需申报的月份，格式yyyy-MM  PC端选择的某条报税记录中的起日期
     * belongsBegin     税款所属时期
     * belongsEnd       税款所属时期
     * payTime          缴款时间
     * accountDetail    财务明细账ID
     * updator          修改人ID
     * updateName       修改人名称
     * updateDate       修改日期
     * */
    @Override
    public Integer setTaxFactAmount(AcctTaxDetailEntity detailEntity) {
        Integer org = detailEntity.getOrg();
        Integer taxId = detailEntity.getTax();
        Integer report = detailEntity.getReport();
        String period = detailEntity.getPeriod();
        String periodBegin = detailEntity.getPeriodBegin();
        String pediodEnd = detailEntity.getPeriodEnd();

        AcctTaxDetailEntity detail = null;

        int res = 0;

        String checkHql = "from AcctTaxDetailEntity where report=:report and period=:period and tax=:tax and org=:org order by id";
        HashMap<String, Object> checkParam = new HashMap<>();
        checkParam.put("report",report);
        checkParam.put("period",period);
        checkParam.put("tax",taxId);
        checkParam.put("org",org);
        List<AcctTaxDetailEntity> list = taxDetailDao.getListByHQLWithNamedParams(checkHql,checkParam);
        if (list.size() > 0) {
            detail = list.get(0);
            updateAccountDetail(detailEntity);
        }
        else {
            String hql = "from AcctTaxDetailEntity where report=:report and state=:state and tax=:tax and org=:org";
            HashMap<String, Object> param = new HashMap<>();
            param.put("report",report);
            param.put("state","0");
            param.put("tax",taxId);
            param.put("org",org);
            List<AcctTaxDetailEntity> listDetail = taxDetailDao.getListByHQLWithNamedParams(hql,param);
            if (listDetail.size() > 0) {
                detail = listDetail.get(0);
                detail.setPeriod(period);
                updateAccountDetail(detailEntity);
            }

        }

        Integer insertId = detailEntity.getTaxPeriodIdInsert();//当前的申报记录ID
        Integer updateId = detailEntity.getTaxPeriodIdUpdate();//修改前的ID
        if (insertId != null) {
            //锁定某条申报记录
            AcctTaxPeriodEntity periodEntityInsert = periodDao.get(insertId);
            periodEntityInsert.setEnabled(0);
        }
        if (updateId != null) {
            //释放某条申报记录
            AcctTaxPeriodEntity periodEntityUpdate = periodDao.get(updateId);
            periodEntityUpdate.setEnabled(1);
        }

        //写历史，t_accountant_tax_detail_history ，state=0
        AcctTaxDetailHistoryEntity detailHistoryEntity = new AcctTaxDetailHistoryEntity();
        detailHistoryEntity.setOrg(org);
        detailHistoryEntity.setState("0");
        detailHistoryEntity.setPeriod(detailEntity.getPeriod());
        detailHistoryEntity.setPeriodBegin(periodBegin.replace("-",""));
        detailHistoryEntity.setPeriodEnd(pediodEnd.replace("-",""));
        detailHistoryEntity.setFactAmount(detailEntity.getFactAmount());
        detailHistoryEntity.setPayTime(detailEntity.getPayTime());
        detailHistoryEntity.setUpdator(detailEntity.getUpdator());
        detailHistoryEntity.setUpdateName(detailEntity.getUpdateName());
        detailHistoryEntity.setUpdateDate(detailEntity.getUpdateDate());
        if (detail != null) {
            detailHistoryEntity.setTaxDetail(detail.getId());
            detailHistoryEntity.setTax(detail.getTax());
            detailHistoryEntity.setTaxHistory(detail.getTaxHistory());
            detailHistoryEntity.setReport(detail.getReport());
            detailHistoryEntity.setReportHistory(detail.getReportHistory());
        }
        else {
            detailHistoryEntity.setTax(taxId);
            detailHistoryEntity.setReport(report);
        }
        taxDetailHistoryDao.save(detailHistoryEntity);
        res = detailHistoryEntity.getId();
        return res;
    }

    /**
     * 返回某年月内所有已缴税列表
     * period   yyyy-MM
     */
    @Override
    public List<AlreadyDeclaredTaxDto> getTaxedList(User user, String period) {
        Integer org = user.getOid();
        List<AlreadyDeclaredTaxDto> result = new ArrayList<>();
        //先找到改日期需报税的所有报表
        String reportHql = "select report from AcctTaxPeriodEntity period,AcctReportEntity report where " +
                "period.org=:org and period.report=report.id and DATE_FORMAT(report.createDate,'%Y-%m')<=:period and period.beginMonth=:beginMonth and report.enabled=1";

        HashMap<String, Object> param = new HashMap<>();
        param.put("org", org);
        param.put("period", period);
        param.put("beginMonth", Integer.parseInt(period.substring(5,7)));
        List<AcctReportEntity> list = reportDao.getListByHQLWithNamedParams(reportHql,param);
        for (AcctReportEntity reportEntity : list) {
            Integer rid = reportEntity.getId();
            String taxHql = "select new cn.sphd.miners.modules.accountantReportTax.dto.AcctTaxDto(" +
                    "tax.id,r.id,tax.name) from " +
                    "AcctTaxEntity tax," +
                    "AcctTaxReportEntity taxreport," +
                    "AcctReportEntity r where taxreport.tax=tax.id and taxreport.report=r.id and tax.enabled=1 and taxreport.report=:report";

            HashMap<String, Object> taxDtoParam = new HashMap<>();
            taxDtoParam.put("report", rid);
            List<AcctTaxDto> taxDtolist = detailDao.getListByHQLWithNamedParams(taxHql,taxDtoParam);
            for (AcctTaxDto taxDto : taxDtolist) {
                Integer taxId = taxDto.getTaxId();
                String factAmountHql = "from AcctTaxDetailEntity where org=:org and period=:period and tax=:tax and report=:report and factAmount>0 group by tax,period order by period desc";
                HashMap<String, Object> factAmountParam = new HashMap<>();
                factAmountParam.put("org", org);
                factAmountParam.put("period", period);
                factAmountParam.put("tax", taxId);
                factAmountParam.put("report", rid);
                List<AcctTaxDetailEntity> detailList = detailDao.getListByHQLWithNamedParams(factAmountHql,factAmountParam);
                if (detailList.size() > 0) {
                    AcctTaxDetailEntity detail = detailList.get(0);
                    Integer id = detail.getId();
                    Integer accountDetail = detail.getAccountDetail();
                    BigDecimal factAmount = detail.getFactAmount();
                    BigDecimal planAmount = detail.getPlanAmount();
                    Date payTime = detail.getPayTime();
                    Date updateTime = detail.getUpdateDate();
                    String updateName = detail.getUpdateName();
                    String belongsBegin = detail.getBelongsBegin();
                    String belongsEnd = detail.getBelongsEnd();
                    Pattern pattern = Pattern.compile("(\\S{4})(\\S{2})(\\S{2})");
                    if (belongsBegin != null)
                        belongsBegin = pattern.matcher(belongsBegin).replaceAll("$1-$2-$3");
                    if (belongsEnd != null)
                        belongsEnd = pattern.matcher(belongsEnd).replaceAll("$1-$2-$3");

                    AlreadyDeclaredTaxDto adtd = new AlreadyDeclaredTaxDto();
                    adtd.setDetailId(id);
                    adtd.setAccountDetail(accountDetail);
                    adtd.setTaxName(taxDto.getTaxName());
                    adtd.setFactAmount(factAmount);
                    adtd.setPlanAmount(planAmount);
                    adtd.setPayTime(payTime);
                    adtd.setLastEditTime(updateTime);
                    adtd.setUpdateName(updateName);
                    adtd.setBelongsBegin(belongsBegin);
                    adtd.setBelongsEnd(belongsEnd);
                    result.add(adtd);
                }

            }

        }

        return result;
    }

    private int updateAccountDetail(AcctTaxDetailEntity detailEntity) {
        Integer org = detailEntity.getOrg();
        Integer taxId = detailEntity.getTax();
        Integer report = detailEntity.getReport();
        Integer updator = detailEntity.getUpdator();
        Integer accountDetail = detailEntity.getAccountDetail();
        BigDecimal factAmount = detailEntity.getFactAmount();
        String updateName = detailEntity.getUpdateName();
        Date updateDate = detailEntity.getUpdateDate();
        Date payTime = detailEntity.getPayTime();
        String period = detailEntity.getPeriod();
        String belongsBegin = detailEntity.getBelongsBegin();
        String belongsEnd = detailEntity.getBelongsEnd();
//        String belongsBeginStr = NewDateUtils.dateToString(NewDateUtils.dateFromString(belongsBegin,"yyyyMMdd"),"yyyy-MM-dd");
//        String belongsEndStr = NewDateUtils.dateToString(NewDateUtils.dateFromString(belongsEnd,"yyyyMMdd"),"yyyy-MM-dd");

        String hql = "update AcctTaxDetailEntity set factAmount=:factAmount,accountDetail=:accountDetail,operation='3',payTime=:payTime,updator=:updator," +
                "updateName=:updateName,updateDate=:updateDate,belongsBegin=:belongsBeginStr,belongsEnd=:belongsEndStr where report=:report and period=:period and tax=:tax and org=:org";
        HashMap<String, Object> param = new HashMap<>();
        param.put("factAmount",factAmount);
        param.put("accountDetail",accountDetail);
        param.put("payTime",payTime);
        param.put("updator",updator);
        param.put("updateName",updateName);
        param.put("updateDate",updateDate);
        param.put("belongsBeginStr",belongsBegin);
        param.put("belongsEndStr",belongsEnd);
        param.put("report",report);
        param.put("period",period);
        param.put("tax",taxId);
        param.put("org",org);

        int res = taxDetailDao.queryHQLWithNamedParams(hql,param);
        return res;
    }

}
