package cn.sphd.miners.modules.generalAffairs.task;

import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.DlmService;
import cn.sphd.miners.modules.system.service.OrgPopedomService;
import cn.sphd.miners.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 职工合同定时任务逻辑
 * Created by Administrator on 2024/12/20.
 */
public class ContractTask {
    @Autowired
    DlmService dlmService;
    @Autowired
    OrgPopedomService orgPopedomService;
    @Autowired
    UserService userService;

    public void contractSettleDay() {
        //wyu:获取分布式锁
        String lockKey, methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        if ((lockKey=dlmService.getLock(methodName)) != null) {
            System.out.println("职工合同日任务开始");
            List<Organization> organizations = orgPopedomService.getOrgPopedomByMid("kb");//有职工档案的机构
            for (Organization organization : organizations) {   //接口没问题后放开
                Integer oid = organization.getId();
                User generalUser= userService.getUserByRoleCode(oid,"general");
                if (generalUser!=null) {
                    System.out.println("职工合同日任务 start oid=" + oid);
                    // 掉朱思旭方法， 等老朱做完他自己加到这里即可
                    System.out.println("职工合同日任务 finish oid：" + oid);
                }
            }
            System.out.println("职工合同日任务结束");
            //wyu:释放分布式锁
            dlmService.releaseLock(methodName, lockKey);
        }
    }
}
