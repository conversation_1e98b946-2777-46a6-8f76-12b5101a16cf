package cn.sphd.miners.modules.complaint.dto;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by 朱思旭 on 2019/5/22.
 */
public class ProductMessageByComplaint implements Serializable{

    Integer customerProduct;  //销售对照表id
    String innerSn;
    String innerName;
    String outerSn;
    String outerName;
    Integer product;           //商品品id
    Date outputTime;
    Integer outputStockSn;    //商品出入库表id

    Integer userID;
    String userName;
    String postName;
    String mobile;
    Date changeTime;
    Integer versionNo;



    public Integer getCustomerProduct() {
        return customerProduct;
    }

    public void setCustomerProduct(Integer customerProduct) {
        this.customerProduct = customerProduct;
    }

    public String getInnerSn() {
        return innerSn;
    }

    public void setInnerSn(String innerSn) {
        this.innerSn = innerSn;
    }

    public String getInnerName() {
        return innerName;
    }

    public void setInnerName(String innerName) {
        this.innerName = innerName;
    }

    public String getOuterSn() {
        return outerSn;
    }

    public void setOuterSn(String outerSn) {
        this.outerSn = outerSn;
    }

    public String getOuterName() {
        return outerName;
    }

    public void setOuterName(String outerName) {
        this.outerName = outerName;
    }

    public Integer getProduct() {
        return product;
    }

    public void setProduct(Integer product) {
        this.product = product;
    }

    public Date getOutputTime() {
        return outputTime;
    }

    public void setOutputTime(Date outputTime) {
        this.outputTime = outputTime;
    }

    public Integer getOutputStockSn() {
        return outputStockSn;
    }

    public void setOutputStockSn(Integer outputStockSn) {
        this.outputStockSn = outputStockSn;
    }

    public Integer getUserID() {
        return userID;
    }

    public void setUserID(Integer userID) {
        this.userID = userID;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Date getChangeTime() {
        return changeTime;
    }

    public void setChangeTime(Date changeTime) {
        this.changeTime = changeTime;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
}
