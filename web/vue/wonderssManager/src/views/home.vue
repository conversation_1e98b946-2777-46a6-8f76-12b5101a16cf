<template>
  <div class="home">
    <div class="contentHome">
      <router-view :homePageAndWorks="homePageAndWorks"/>
    </div>
    <div class="navBar">
      <div @click="navIndex(0)" :class="{ 'navActive': activeIndex === 0  }"><span class="icon" :class="activeIndex === 0 ? 'icon-shouye-active' :'icon-shouye'"></span><div>首页</div></div>
      <div @click="navIndex(1)" :class="{ 'navActive': activeIndex === 1  }"><span class="icon" :class="activeIndex === 1 ? 'icon-tongxunlu-active' :'icon-tongxunlu'"></span><div>通讯录</div></div>
      <div @click="navIndex(2)" :class="{ 'navActive': activeIndex === 2  }"><span class="icon" :class="activeIndex === 2 ? 'icon-gongzuo-active' :'icon-gongzuo'"></span><div>工作</div></div>
      <div @click="navIndex(3)" :class="{ 'navActive': activeIndex === 3  }"><span class="icon" :class="activeIndex === 3 ? 'icon-chuli-active' :'icon-chuli'"></span><div>处理</div></div>
      <div @click="navIndex(4)" :class="{ 'navActive': activeIndex === 4  }"><span class="icon" :class="activeIndex === 4 ? 'icon-wode-active' :'icon-wode'"></span><div>我的</div></div>
    </div>

  </div>
</template>
<script>
import {provide} from 'vue'
import {getAppHomePageAndWorks} from "@/api/api";

export default {
  data() {
    return {
      activeIndex:4,
      homePageAndWorks: {}
    }
  },
  created() {
    provide('homePageAndWorks', this.homePageAndWorks)
    getAppHomePageAndWorks().then((res)=> {
      this.homePageAndWorks = {}
      Object.assign(this.homePageAndWorks, res.data?.data)//res.data?.data
      console.log('getAppHomePageAndWorks', this.homePageAndWorks)
    })
  },
  mounted() {
  },
  methods: {
    navIndex(indexNum){
      this.activeIndex = indexNum
      let path = 'homeMy'
      switch (indexNum){
        case 0: path = 'shouye'; break;
        case 1: path = 'tongxunlu'; break;
        case 2: path = 'gongzuo'; break;
        case 3: path = 'chuli'; break;
        case 4: path = 'homeMy'; break;
      }
      this.$router.push(path)
    },

  },
}
</script>

<style lang="scss" scoped>
.home{
  .contentHome{
    padding-bottom: 55px;
  }
  .navBar{
    display: flex;  bottom:24px;  position:fixed; width: 100%; background-color: #fff; border-top: 1px solid #efefef;
    &>div{ flex: 1; text-align: center;  padding: 5px 0; font-size:12px;
    }
    &>div:last-child{  border-right:none;  }
    &>div.navActive{ color:$tyColorGreen; font-weight: bold;}

    .icon{ display: block; width: 20px; height: 20px; background-color: #fff; margin:3px auto;
      background-size: 100%; background-size: 100% 100%;
    }

    .icon-shouye{ background-image: url("@/assets/tabBar/shouye1.png");  }
    .icon-shouye-active{ background-image: url("@/assets/tabBar/shouye0.png");  }
    .icon-tongxunlu{ background-image: url("@/assets/tabBar/tongxunlu1.png");  }
    .icon-tongxunlu-active{ background-image: url("@/assets/tabBar/tongxunlu0.png");  }
    .icon-gongzuo{ background-image: url("@/assets/tabBar/gongzuo1.png");  }
    .icon-gongzuo-active{ background-image: url("@/assets/tabBar/gongzuo0.png");  }
    .icon-chuli{ background-image: url("@/assets/tabBar/chuli1.png");  }
    .icon-chuli-active{ background-image: url("@/assets/tabBar/chuli0.png");  }
    .icon-wode{ background-image: url("@/assets/tabBar/wode1.png");  }
    .icon-wode-active{ background-image: url("@/assets/tabBar/wode0.png");  }



  }

}
</style>
