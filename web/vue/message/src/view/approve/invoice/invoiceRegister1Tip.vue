<template>
  <div id="invoiceRegister1Tip">
    <TY_NavTop title="开票登记" ></TY_NavTop>

    <div class="ccon">
      <div v-if="twoInfo">
        <div>
          该客户的开票资料已修改
          <span class="ty-right linkBtn" @click="twoDetails">查看详情</span>
        </div>
        <div class="radioTr">
          <el-checkbox v-model="radio1" label="公司开票系统中资料已更新，不再提示！"></el-checkbox>
        </div>
      </div>
      <div v-if="priceInfo">
        <div>
          本次需开具的发票中，有{{ priceNum}}项商品价格发生了改动 <br>
          注：再有变动，系统会继续给予提示！<br>
          &nbsp; <span class="ty-right linkBtn" @click="priceDetails">查看详情</span>
        </div>

      </div>
      <div>
        <input type="submit" class="ui-btn ui-btn_info" value="下一步" @click="jump">

      </div>


    </div>


  </div>

</template>
<style lang="less">
#invoiceRegister1Tip{
  line-height: 30px;
  .ccon>div{
    background: #fff;
    padding:16px;
    font-size: 16px;
    margin-top: 10px;
  }
  .ty-right{ float: right;  }
  .linkBtn{
    color: #0b94ea;
    cursor: pointer;
  }
  .centerBtn{
    //margin-top: 50px;
  }
  .radioTr{
    margin-top: 30px;
  }
}
</style>
<script>
export default {
  name: 'invoiceRegister1Tip',
  data () {
    return {
      id: this.$route.params.id,
      radio1: false,
      radio2: false,
      twoInfo: false,
      twoId: 0,
      frontId: 0,
      customer: 0,
      priceInfo: false,
      priceNum: 0,
      goodsList:[]
    }
  },
  created () {
    this.getCharge({ 'id': this.id })
  },
  methods: {

    priceDetails: function () {
      let _this = this
      if( _this.goodsList && _this.goodsList.length > 0){
        let setIframeStyle = {
          'height': '380px',
          'margin-top': '200px',
          'width': '1040px',
          'border': 'none'
        }
        localStorage.setItem('goodsList', JSON.stringify(_this.goodsList))
        let data = {
          // 'id': this.id,
          // 'goods':this.notice
        }
        window.parent.floatToPage('../../../sale/toGoodsInfo', data, setIframeStyle)
      }else{
        this.$message({
          type: "error",
          message: "当前未获得商品数据"
        })
      }

    },
    twoDetails: function () {
      let setIframeStyle = {
        'height': '580px',
        'margin-top': '100px',
        'width': '810px',
        'border': 'none'
      }
      let data = {
        'customerId': this.customer,
        'frontId': this.frontId,
        'id': this.twoId
      }
      console.log('送过去的=', data)
      window.parent.floatToPage('../../../../sale/toInvoiceInfo', data, setIframeStyle)
    },
    getCharge: function (datap) {
      let _this = this
      _this.$http.post('../../../sales/pd/preRegister', datap, {
        emulateJSON: true
      }).then(res => {
        if(datap.operation ){
          _this.$router.push({
            path: "/invoiceRegisterDetail/" + this.id
          })
        }else{
          console.log('preRegister res=', res)
          let data = res.body
          _this.customer = data.customer // 开票信息修改过的记录
          let invoiceRecord = data.invoiceRecord // 开票信息修改过的记录
          let notice = data.notice || 0 // 修改过价格的数量
          _this.priceNum = notice
          let noticeData = data.noticeData || []
          _this.goodsList = noticeData
          if(invoiceRecord){
            this.twoInfo = true
            let len = invoiceRecord.length
            this.twoId = invoiceRecord[len-1]['id']
            this.frontId = invoiceRecord[len-2]['id']
            console.log('this.twoId =', this.twoId )
          }
          if(notice > 0){
            this.priceInfo = true
          }
          console.log('this.priceInfo =', this.priceInfo )
          console.log('this.twoInfo =', this.twoInfo )
          if((!this.priceInfo ) && (!this.twoInfo)){
            _this.$router.push({
              path: "/invoiceRegisterDetail/" + this.id
            })
          }
        }


      }).catch(err => {
        console.log('preRegister err=', err)


      })
    },
    jump: function () {
      console.log('jump: ', this.radio1 )
      console.log('this.radio2 ', this.radio2)
      var e = 0;
      let data = {
        'id': this.id,
        'operation': 3
      }
      this.twoInfo && (this.radio1 || e++),
      // this.priceInfo && (this.radio2 || e++),
        e > 0 ? this.$message({
          type: "error",
          message: "请确认开票资料更新情况！"
        }) : this.getCharge(data)
    },

  }
}
</script>
