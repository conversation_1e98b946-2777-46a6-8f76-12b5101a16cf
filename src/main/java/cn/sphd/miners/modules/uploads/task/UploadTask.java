package cn.sphd.miners.modules.uploads.task;

import cn.sphd.miners.modules.system.service.DlmService;
import cn.sphd.miners.modules.uploads.service.UploadService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 统一上传日任务
 * <AUTHOR>
 * @since 2021/2/19 16:41
 */
public class UploadTask {
    @Autowired
    DlmService dlmService;
    @Autowired
    UploadService service;

    public void checkUpload() {
        //wyu:获取分布式锁
        String lockKey, methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        if ((lockKey=dlmService.getLock(methodName)) != null) {
            System.out.println("UploadTask.checkUploadTask 任务开始：");
            service.checkUploadTask(null, null);
            System.out.println("UploadTask.checkUploadTask 任务结束：");
            //wyu:释放分布式锁
            dlmService.releaseLock(methodName, lockKey);
        }
    }
}
