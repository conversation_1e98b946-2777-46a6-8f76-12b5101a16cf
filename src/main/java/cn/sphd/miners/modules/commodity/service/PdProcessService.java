package cn.sphd.miners.modules.commodity.service;

import cn.sphd.miners.modules.commodity.entity.PdBase;
import cn.sphd.miners.modules.commodity.entity.PdProcess;

import java.util.List;

/**
 * Created by Administrator on 2016/10/8.
 */
public interface PdProcessService {
    List<PdProcess> findListPdProcessAndBaseThreeProperty(String innerSn, Integer oid);
    List<PdProcess> findPdProcessAndBaseDetail(Integer id, Integer oid);
    void updatePdProcess(PdProcess pdProcess);
    PdProcess getPdProcessById(Integer id);
    PdBase getById(Integer id);

    List<PdProcess> getProcessByOidInnerSn(Integer oid, String innerSn);

    void savePdProcess(PdProcess pdProcess);



}
