package cn.sphd.miners.modules.resourceAuthority.entity;


import javax.persistence.*;
import java.io.Serializable;
import java.math.BigInteger;
import java.util.Date;

@Entity
@Table( name ="t_resource_notice" )
public class ResNotice implements Serializable {

  /**
   * ID
   */
  @Id
  @Column(name="id")
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;
  /**
   * 机构ID
   */
  @Column(name = "org", nullable=true , unique=false )
  private Integer org;
  @Column(name = "category", nullable=true , unique=false )
  private Integer category;
  @Column(name = "resource", nullable=true , unique=false )
  private Integer resource;
  @Column(name = "category_history", nullable=true , unique=false )
  private Integer categoryHistory;
  @Column(name = "resource_history", nullable=true , unique=false )
  private Integer resourceHistory;
  @Column(name = "resource_correlation", nullable=true , unique=false )
  private Integer resourceCorrelation;
  @Column(name = "batch_uuid")
  private String batchUuid;
  @Column(name = "type")
  private Byte type;
  @Column(name = "user", nullable=true , unique=false )
  private Integer user;
  @Column(name = "is_signed")
  private Byte isSigned;
  @Column(name = "extinction_time")
  private Date extinctionTime;
  @Column(name = "is_latest")
  private Byte isLatest;
  @Column(name = "path")
  private String path;
  @Column(name = "creator", nullable=true , unique=false )
  private Integer creator;
  @Column(name = "create_name")
  private String createName;
  @Column(name = "create_date")
  private Date createDate;
  @Column(name = "updator", nullable=true , unique=false )
  private Integer updator;
  @Column(name = "update_name")
  private String updateName;
  @Column(name = "update_date")
  private Date updateDate;
  @Column(name = "message_id", nullable=true , unique=false )
  private Integer messageId;
  @Column(name = "operation")
  private Byte operation;
  @Column(name = "previous_id", nullable=true , unique=false )
  private Integer previousId;
  @Column(name = "version_no", nullable=true , unique=false )
  private Integer versionNo;
  @Column(name = "batch_num")
  private BigInteger batchNum;
  @Column(name = "original_feature")
  private String originalFeature;
  @Column(name = "new_feature")
  private String newFeature;


  @Transient
  private String allFileName;
  @Transient
  private String allFormName;
  @Transient
  private String noticeDisappear;
  @Transient
  private String fileName;
  @Transient
  private String fileSn;
  @Transient
  private Integer changeNum;
  @Transient
  private String version;
  @Transient
  private String categoryName;
  @Transient
  private String userName;
  @Transient
  private String mobile;



  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Integer getOrg() {
    return org;
  }

  public void setOrg(Integer org) {
    this.org = org;
  }

  public Integer getCategory() {
    return category;
  }

  public void setCategory(Integer category) {
    this.category = category;
  }

  public Integer getResource() {
    return resource;
  }

  public void setResource(Integer resource) {
    this.resource = resource;
  }

  public Integer getCategoryHistory() {
    return categoryHistory;
  }

  public void setCategoryHistory(Integer categoryHistory) {
    this.categoryHistory = categoryHistory;
  }

  public Integer getResourceHistory() {
    return resourceHistory;
  }

  public void setResourceHistory(Integer resourceHistory) {
    this.resourceHistory = resourceHistory;
  }

  public Integer getResourceCorrelation() {
    return resourceCorrelation;
  }

  public void setResourceCorrelation(Integer resourceCorrelation) {
    this.resourceCorrelation = resourceCorrelation;
  }

  public Byte getType() {
    return type;
  }

  public void setType(Byte type) {
    this.type = type;
  }

  public Integer getUser() {
    return user;
  }

  public void setUser(Integer user) {
    this.user = user;
  }

  public Byte getIsSigned() {
    return isSigned;
  }

  public void setIsSigned(Byte isSigned) {
    this.isSigned = isSigned;
  }

  public Date getExtinctionTime() {
    return extinctionTime;
  }

  public void setExtinctionTime(Date extinctionTime) {
    this.extinctionTime = extinctionTime;
  }

  public Byte getIsLatest() {
    return isLatest;
  }

  public void setIsLatest(Byte isLatest) {
    this.isLatest = isLatest;
  }

  public String getPath() {
    return path;
  }

  public void setPath(String path) {
    this.path = path;
  }

  public Integer getCreator() {
    return creator;
  }

  public void setCreator(Integer creator) {
    this.creator = creator;
  }

  public String getCreateName() {
    return createName;
  }

  public void setCreateName(String createName) {
    this.createName = createName;
  }

  public Date getCreateDate() {
    return createDate;
  }

  public void setCreateDate(Date createDate) {
    this.createDate = createDate;
  }

  public Integer getUpdator() {
    return updator;
  }

  public void setUpdator(Integer updator) {
    this.updator = updator;
  }

  public String getUpdateName() {
    return updateName;
  }

  public void setUpdateName(String updateName) {
    this.updateName = updateName;
  }

  public Date getUpdateDate() {
    return updateDate;
  }

  public void setUpdateDate(Date updateDate) {
    this.updateDate = updateDate;
  }

  public Integer getMessageId() {
    return messageId;
  }

  public void setMessageId(Integer messageId) {
    this.messageId = messageId;
  }

  public Byte getOperation() {
    return operation;
  }

  public void setOperation(Byte operation) {
    this.operation = operation;
  }

  public Integer getPreviousId() {
    return previousId;
  }

  public void setPreviousId(Integer previousId) {
    this.previousId = previousId;
  }

  public Integer getVersionNo() {
    return versionNo;
  }

  public void setVersionNo(Integer versionNo) {
    this.versionNo = versionNo;
  }

  public String getAllFileName() {
    return allFileName;
  }

  public void setAllFileName(String allFileName) {
    this.allFileName = allFileName;
  }

  public String getAllFormName() {
    return allFormName;
  }

  public void setAllFormName(String allFormName) {
    this.allFormName = allFormName;
  }

  public String getNoticeDisappear() {
    return noticeDisappear;
  }

  public void setNoticeDisappear(String noticeDisappear) {
    this.noticeDisappear = noticeDisappear;
  }

  public String getBatchUuid() {
    return batchUuid;
  }

  public void setBatchUuid(String batchUuid) {
    this.batchUuid = batchUuid;
  }

  public String getFileName() {
    return fileName;
  }

  public void setFileName(String fileName) {
    this.fileName = fileName;
  }

  public String getFileSn() {
    return fileSn;
  }

  public void setFileSn(String fileSn) {
    this.fileSn = fileSn;
  }

  public Integer getChangeNum() {
    return changeNum;
  }

  public void setChangeNum(Integer changeNum) {
    this.changeNum = changeNum;
  }

  public String getVersion() {
    return version;
  }

  public void setVersion(String version) {
    this.version = version;
  }

  public BigInteger getBatchNum() {
    return batchNum;
  }

  public void setBatchNum(BigInteger batchNum) {
    this.batchNum = batchNum;
  }

  public String getOriginalFeature() {
    return originalFeature;
  }

  public void setOriginalFeature(String originalFeature) {
    this.originalFeature = originalFeature;
  }

  public String getNewFeature() {
    return newFeature;
  }

  public void setNewFeature(String newFeature) {
    this.newFeature = newFeature;
  }

  public String getCategoryName() {
    return categoryName;
  }

  public void setCategoryName(String categoryName) {
    this.categoryName = categoryName;
  }

  public String getUserName() {
    return userName;
  }

  public void setUserName(String userName) {
    this.userName = userName;
  }

  public String getMobile() {
    return mobile;
  }

  public void setMobile(String mobile) {
    this.mobile = mobile;
  }
}
