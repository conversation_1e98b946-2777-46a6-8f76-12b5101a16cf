package cn.sphd.miners.modules.commodity.service;


import cn.sphd.miners.modules.material.entity.MtBase;
import cn.sphd.miners.modules.material.entity.MtCategory;
import cn.sphd.miners.modules.sales.model.PoOrders;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;
import java.util.Map;

public interface CpsMtService {

    Map<String,Object> getPdMtBaseList(Integer oid, Integer currPage, Integer pageSize);

    List<Map<String,Object>> getPdMtBaseList(Integer oid);
    //新增绑定材料
    String saveMt(MtBase mtBase, MtCategory category, User user);

    //昌平与材料难道
    String bindOperation(String baseList,Integer mtId,User user);
    //删除材料
    String deleteMt(Integer id,Integer oid);
    //启停 id 材料id
    String startStopMt(Integer id, String state,User user);

    //修改材料
    String updateMt(MtBase mtBase, User user);


    //根据材料获取已绑定产品
    Map<String,Object> getBindingPdList(Integer id,Integer currPage, Integer pageSize);

    //根据材料获取曾经绑定产品
    Map<String,Object> getBeforeBindingPdList(Integer id,Integer currPage, Integer pageSize);
    //根据产品获取曾经绑定材料
    Map<String,Object> getMtListByPdBase(Integer id,Integer currPage, Integer pageSize);

    //根据材料获取未完结的采购数量
    List<PoOrders>  getPoOrderByState(Integer id);

    //获取昨天有录入材料的机构
    List<Map<String, Object>> getOrg();

    //获取昨天停用的材料
    List<Map<String, Object>> getOrgForStop();

    //统计昨日录入者的录入数量
    List<Map<String,Object>> zrtj(Integer org);
    //统计昨日录入者的停用数量
    List<Map<String,Object>> zrtytj(Integer org);
    //统计昨日录入数量
    Map<String, Object> zrzj(Integer org);
}
