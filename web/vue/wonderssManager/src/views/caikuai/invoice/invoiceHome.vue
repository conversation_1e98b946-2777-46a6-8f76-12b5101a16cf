<template>
  <div class="box">
    <div class="head">
      <PageHeader :title="tit" :showBack=true @myBackFun="handleCustomBack" :showClose=true
                  @mycloseFun="handleCustomClose"
                  :runMyBackFun=true>
      </PageHeader>
    </div>
    <div class="flex ty-fff">
      <p class="h35f16">发票的使用记录</p>
      <span @click="hide(0)">
          <el-icon style="font-size: 20px" v-show="list1B == false">
          <arrow-down/>
        </el-icon>
        <el-icon v-show="list1B" style="font-size: 20px">
          <arrow-up/>
        </el-icon>
      </span>
    </div>
    <div v-show="list1B" class="margin_top_16" v-if="InvoiceUsedList&&list1B">
      <ul class="list ty-fff">
        <li class="flex">
          <div>
            <span>今年使用</span>
            <p>
              <span>{{ InvoiceUsedList.newYearUsed.num }}张</span>
              <span>{{ InvoiceUsedList.newYearUsed.totalAmount ? InvoiceUsedList.newYearUsed.totalAmount : '0'
                }}元</span>
            </p>
          </div>
          <p class="ic" @click="invoiceUse(0)">
            <el-icon style="font-size: 20px">
              <arrow-right/>
            </el-icon>
          </p>
        </li>
        <li class="flex f4">
          <div>
            <span>去年使用</span>
            <p>
              <span>{{ InvoiceUsedList.lastYearUsed.num }}张</span>
              <span>{{ InvoiceUsedList.lastYearUsed.totalAmount ? InvoiceUsedList.lastYearUsed.totalAmount : '0'
                }}元</span>
            </p>
          </div>
          <p class="ic" @click="invoiceUse(1)">
            <el-icon style="font-size: 20px">
              <arrow-right/>
            </el-icon>
          </p>
        </li>
        <li class="flex">
          <div>
            <span>前年使用</span>
            <p>
              <span>{{ InvoiceUsedList.beforeLastYearUsed.num }}张</span>
              <span>{{InvoiceUsedList.beforeLastYearUsed.totalAmount ? InvoiceUsedList.beforeLastYearUsed.totalAmount : '0'
                }}元</span>
            </p>
          </div>
          <p class="ic" @click="invoiceUse(2)">
            <el-icon style="font-size: 20px">
              <arrow-right/>
            </el-icon>
          </p>
        </li>
        <li class="flex dateP">
          <span style="margin-right: 10px">更早年份</span>
          <p @click="picker(0)">选择年份</p>
        </li>
      </ul>
    </div>
    <div class="flex ty-fff margin_top_16">
      <p class="h35f16">发票的申领记录</p>
      <span @click="hide(1)">
              <el-icon style="font-size: 20px" v-show="list2B==false">
                <arrow-down/>
              </el-icon>
              <el-icon v-show="list2B" style="font-size: 20px">
                <arrow-up/>
              </el-icon>
          </span>
    </div>
    <div v-show="list2B" class="margin_top_16" v-if="FinanceChequeTime&&list2B">
      <ul class="list ty-fff">
        <li class="flex">
          <div>
            <span>今年申领</span>
            <p>
              <span>{{ FinanceChequeTime.yearTime }}次</span>
            </p>
          </div>
          <p class="ic" @click="xQ(3)">
            <el-icon style="font-size: 20px">
              <arrow-right/>
            </el-icon>
          </p>
        </li>
        <li class="flex f4">
          <div>
            <span>去年申领</span>
            <p>
              <span>{{ FinanceChequeTime.lastYearTime }}次</span>
            </p>
          </div>
          <p class="ic" @click="xQ(4)">
            <el-icon style="font-size: 20px">
              <arrow-right/>
            </el-icon>
          </p>
        </li>
        <li class="flex">
          <div>
            <span>前年申领</span>
            <p>
              <span>{{ FinanceChequeTime.beforeYearTime }}次</span>
            </p>
          </div>
          <p class="ic" @click="xQ(5)">
            <el-icon style="font-size: 20px">
              <arrow-right/>
            </el-icon>
          </p>

        </li>
        <li class="flex margin_top_16">
          <span style="margin-right: 10px">更早年份</span>
          <el-date-picker
              v-model="year"
              type="year"
              placeholder="XXXX年"
              format="YYYY"
              value-format="YYYY"
              style="width: 60%;"
          ></el-date-picker>
        </li>
      </ul>
    </div>
    <div class="flex ty-fff margin_top_16">
      <p class="h35f16">空白发票</p>
      <span @click="hide(2)">
          <el-icon style="font-size: 20px" v-show="list3B==false">
            <arrow-down/>
          </el-icon>
          <el-icon v-show="list3B" style="font-size: 20px">
            <arrow-up/>
          </el-icon>
      </span>
    </div>
    <div v-show="list3B" class="margin_top_16">
      <ul class="list ty-fff" v-if="FinanceChequeTime&&list3B">
        <li class="flex">
          <div>
            <span style="width: 70%">空白的增值税专用发票</span>
            <p style="justify-content: center">
              <span>{{ FinanceChequeTime.specialNum }}张</span>
            </p>
          </div>
          <p class="ic" @click="xQ(6)">
            <el-icon style="font-size: 20px">
              <arrow-right/>
            </el-icon>
          </p>
        </li>
        <li class="flex f4">
          <div>
            <span style="width: 70%">空白的增值税普通发票</span>
            <p style="justify-content: center">
              <span>{{ FinanceChequeTime.ordinaryNum }}张</span>
            </p>
          </div>
          <p class="ic" @click="xQ(7)">
            <el-icon style="font-size: 20px">
              <arrow-right/>
            </el-icon>
          </p>
        </li>
        <li class="flex">
          <div>
            <span style="width: 70%">空白的其他发票</span>
            <p style="justify-content: center">
              <span>{{ FinanceChequeTime.otherNum }}张</span>
            </p>
          </div>
          <p class="ic" @click="xQ(8)">
            <el-icon style="font-size: 20px">
              <arrow-right/>
            </el-icon>
          </p>
        </li>
      </ul>
    </div>
    <DatePicker @cancel="handlecancel" type="year" @confirm="handConfirm" v-show="datePicker"/>
  </div>
</template>
<script>
import DatePicker from '@/components/DatePicker.vue';
import {getInvoiceUsed, getAllInvoiceNum} from '@/api/financeBill'
import finance from '@/mixins/finance.js'

export default {
  name: "invoiceHome",
  mixins: [finance],
  components: {
    DatePicker
  },
  methods: {
    hide(index) {
      if (index == 0) {
        this.list1B = !this.list1B
      } else if (index == 1) {
        this.list2B = !this.list2B
      } else if (index == 2) {
        this.list3B = !this.list3B
      }
    },
    handleCustomBack() {
      this.$router.push("/caikuai");
    },
    handleCustomClose() {
      this.$router.push("/caikuai")
    },
    getlist() {
      // const params = {type: this.isActive}
      getInvoiceUsed().then(res1 => {
        this.InvoiceUsedList = res1.data.data
        // console.log(res1, 'res1111111111111')
      }).catch(error => {
        console.error('Error:', error);
      });
      let org = this.auth.getOrg()
      getAllInvoiceNum({oid: org.id}).then(res1 => {
        this.FinanceChequeTime = res1.data.data
        // console.log(res1, 'res1111111111111')
      }).catch(error => {
        console.error('Error:', error);
      });
    },
    invoiceUse(index, result) {
      const typeMonth = 1
      const dateType = 1
      let invoice = {index, result, typeMonth, dateType}
      localStorage.setItem('invoice', JSON.stringify(invoice))
      this.$router.push('/annualList')
    },
    handlecancel(data) {
      this.datePicker = data;
    },
    handConfirm(result) {
      this.datePicker = false
      if (this.pickerIndex == 0) {
        this.invoiceUse(3, result.year)
      }
      // this.cmo(4, result.year)
    },
    picker(index) {
      this.datePicker = !this.datePicker
      this.pickerIndex = index
    }
  },
  data() {
    return {
      list1B: false,
      list2B: false,
      list3B: false,
      tit: "财会",
      InvoiceUsedList: {},
      FinanceChequeTime: {},
      datePicker: false,
      pickerIndex: ''
    }
  },
  mounted() {
    this.getlist();
  }
}
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";

.flex {
  align-items: center;
  padding: 8px 15px;
  line-height: 20px;

  .h35f16 {
    font-size: 14px;
  }

  > span {
    width: 40%;
    text-align: end;
  }
}

.dateP {
  line-height: 35px;

  > :nth-child(2) {
    text-align: center;
    border: 1px solid $tyColorGray;
    color: $tyColorGray;
    width: 50%;
  }
}

.list {
  > li {
    height: 40px;
    padding: 5px 15px;

    > div {
      width: 85%;
      display: flex;
      align-items: center;
      height: 100%;
      margin-left: 20px;

      > span:first-child {
        width: 40%;
      }

      > span {
        width: 50%;
        font-size: 14px;
      }

      > p {
        display: flex;
        width: 55%;

        > span:first-child {
          width: 30%;
        }

        > span:nth-child(2) {
          width: 80%;
        }

        > span {
          margin-right: 10px;
          text-align: right;
        }
      }


    }
  }

  > li:nth-child(4) {
    display: flex;
    justify-content: left;
    align-items: center;

    > span {
      width: 25%;
      text-align: left;
      margin-left: 20px;
    }

  }
}

.re {
  position: relative;
  top: 40px;
}
</style>