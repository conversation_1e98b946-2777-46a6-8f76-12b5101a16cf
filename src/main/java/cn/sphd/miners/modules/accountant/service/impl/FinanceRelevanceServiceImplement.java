package cn.sphd.miners.modules.accountant.service.impl;

import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectEntity;
import cn.sphd.miners.modules.accountant.mapper.BuildAccountMapper;
import cn.sphd.miners.modules.accountant.mapper.TAccountSubjectMapper;
import cn.sphd.miners.modules.accountant.service.BuildAccountService;
import cn.sphd.miners.modules.accountant.service.FinanceRelevanceService;
import cn.sphd.miners.modules.accountant.service.SubjectSettingService;
import cn.sphd.miners.modules.accountant.util.Tools;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2018-09-20 16:27
 * @description
 **/
@Service
@Transactional(propagation= Propagation.REQUIRED)
public class FinanceRelevanceServiceImplement implements FinanceRelevanceService {

    @Autowired
    SubjectSettingService subjectSettingService;
    @Autowired
    BuildAccountService buildAccountService;
    @Autowired
    TAccountSubjectMapper tAccountSubjectMapper;
    @Autowired
    BuildAccountMapper buildAccountMapper;
    @Autowired
    TAccountSubjectMapper subjectMapper;

    /*
    * 关联生成财务相关科目
    * 主要是1002下的子科目,每个对公户都对应生成两个会计科目，编号自动生成，二级科目名称是开户行名称，三级科目名称是账户编号
    * oid           机构id
    * financeID     财务对公户新增后的id
    * accountName   开户行名称
    * accountNO     账户编号
    * relevanceType 关联类型:1-商品，2-物料,3-供应商,4-客户,5-半成品,6-财务对公户
    * */
    @Override
    public int generateFinanceSubjects(int oid,int financeID, String accountName, String accountNO,User user) {
        //先判断是否存在该对公户对应的科目，有的话返回失败
        TAccountantSubjectEntity financeSubject = new TAccountantSubjectEntity();
        financeSubject.setLevel(2);
        financeSubject.setRelevanceType("6");
        financeSubject.setOrg(oid);
        financeSubject.setRelevanceItem(financeID);
        financeSubject = subjectMapper.getFinanceRelevanceSubject(financeSubject);//获取对公户对应的科目
        if (financeSubject != null)
            return 0;
        //先自动生成二级科目的编号
        String list = subjectSettingService.firstSubject("1002",oid);
        net.sf.json.JSONArray proArray = net.sf.json.JSONArray.fromObject(list);
        if (list != null) {
            if (!proArray.isEmpty()) {
                for (int j=0;j<proArray.size();j++) {
                    net.sf.json.JSONObject pro = proArray.getJSONObject(j);
                    String newProSubject = pro.optString("newSubject");
                    //新增1002的二级科目，
                    subjectSettingService.newAccountantSubject("1002",oid,accountName,null,user.getUserID(),user.getUserName(),newProSubject,"0","6",financeID);
                    //产生三级科目编号
                    String thirdSubject = subjectSettingService.firstSubject(newProSubject,oid);
                    net.sf.json.JSONArray thirdArray = net.sf.json.JSONArray.fromObject(thirdSubject);
                    if (!thirdArray.isEmpty()) {
                        for (int i=0;i<thirdArray.size();i++) {
                            net.sf.json.JSONObject obj = thirdArray.getJSONObject(i);
                            String newSubject = obj.optString("newSubject");
                            //新增三级科目
                            subjectSettingService.newAccountantSubject(newProSubject,oid,accountNO,null,user.getUserID(),user.getUserName(),newSubject,"0","6",financeID);
                        }
                    }
                }

                return 1;
            }
        }
        return 0;
    }

    @Override
    public int generateSubjectsByFinance(int oid, int financeID, String accountName, String accountNO, User user) {
        boolean isEnableAccountant = enableAccountantModule(oid);
        if (!isEnableAccountant) {
            //如果会计模块没有开启的话返回0
            return 0;
        }
        int res = generateFinanceSubjects(oid,financeID,accountName,accountNO,user);
        return res;
    }

    /*
    * 关联修改财务对公户
    * 在会计建账未完成时且科目未被使用过的话，财务对公户的修改会关联修改会计科目
    * */
    @Override
    public int financeRelevanceModify(int oid, int financeID, String accountName, String accountNO, User user) {
        boolean isEnableAccountant = enableAccountantModule(oid);
        if (!isEnableAccountant) {
            //如果会计模块没有开启的话返回0
            return 0;
        }
        //只有当建账未完成时可关联修改
        String buildState = buildAccountService.getBuildAccountState(oid);
        if ("4".equals(buildState)) {
            return 0;
        }
        TAccountantSubjectEntity subjectEntity = new TAccountantSubjectEntity();
        //先更新二级科目
        subjectEntity.setLevel(2);
        subjectEntity.setName(accountName);
        subjectEntity.setRelevanceItem(financeID);
        subjectEntity.setRelevanceType("6");
        subjectEntity.setOrg(oid);
        subjectEntity.setUpdator(user.getUserID());
        subjectEntity.setUpdateName(user.getUserName());
        subjectEntity.setUpdateDate(Tools.date2Str(new Date()));
        tAccountSubjectMapper.financeRelevanceModify(subjectEntity);
        //在更新三级科目
        subjectEntity.setLevel(3);
        subjectEntity.setName(accountNO);
        tAccountSubjectMapper.financeRelevanceModify(subjectEntity);
        return 1;
    }

    /*
    * 检测某机构是否开启会计模块
    * 开启返回true 否则返回false
    * */
    public boolean enableAccountantModule(Integer org) {
        QueryData qd = new QueryData();
        qd.put("org",org);
        qd.put("mid","sa");
        String mid = buildAccountMapper.enableAccountantModule(qd);
        if (mid == null)
            return false;
        else
            return true;
    }
}
