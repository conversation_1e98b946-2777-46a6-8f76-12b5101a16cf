<template>
  <div id="purchaseBillFinanceApproval" v-loading.fullscreen.lock="loading" >
    <TY_NavTop title="采购部门的票据审核" isSearch="true" @toggleList="search"></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待在线审核" name="1" closable='false' :msgCount="onlineAuditHandle.length">
        <div class="tip">
          <p>以下票据系采购部门提交，请在线审核！</p>
        </div>
        <div>
          <a class="ui-cell" v-on:click="jump('31' + item.poPaymentApplication.id,0 )" v-for="(item, index) in onlineAuditHandle" v-bind:key="index">
            <div class="item_fn">
              <div class="supName">{{item.fullName}} <span class="right">{{ item.codeName }}</span></div>
              <div class="oInfo">
                <p> {{ item.poPaymentApplication.type | formatType(item.poPaymentApplication.amount) }}</p>
                <p v-if="item.poPaymentApplication.type !== '3'">票据 {{item.poPaymentApplication.billCount }} 张，共 {{item.poPaymentApplication.billAmount.toFixed(2) }} 元</p>
                <p v-else>本次付款申请没有附带的票据</p>
              </div>
            </div>
            <div class="txt_right">申请人：{{ item.poPaymentApplication.createName  }}
              {{item.poPaymentApplication.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="在线审核ok" name="2" closable='false'>
        <div class="tip">
          <p>以下票据系采购部门提交，请在线审核！</p>
        </div>
        <div>
          <a class="ui-cell" v-on:click="jump('32' + item.poPaymentApplication.id,0 )" v-for="(item, index) in onlineAuditOK" v-bind:key="index">
            <div class="item_fn">
              <div class="supName">{{item.fullName}} <span class="right">{{ item.codeName }}</span></div>
              <div class="oInfo">
                <p> {{ item.poPaymentApplication.type | formatType(item.poPaymentApplication.amount) }}</p>
                <p v-if="item.poPaymentApplication.type !== '3'">票据 {{item.poPaymentApplication.billCount }} 张，共 {{item.poPaymentApplication.billAmount.toFixed(2) }} 元</p>
                <p v-else>本次付款申请没有附带的票据</p>
              </div>
            </div>
            <div class="txt_right">申请人：{{ item.poPaymentApplication.createName  }}
              {{item.poPaymentApplication.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="待线下审核" name="3" closable='false' :msgCount="offlineAuditHandle.length">
        <div class="tip">
          <p>以下票据有待线下审核。请核对实际票据与系统中的电子票据是否一致。</p>
        </div>
        <div>
          <a class="ui-cell" v-on:click="jump('33' + item.poPaymentApplication.id,0 )" v-for="(item, index) in offlineAuditHandle" v-bind:key="index">
            <div class="item_fn">
              <div class="supName">{{item.fullName}} <span class="right">{{ item.codeName }}</span></div>
              <div class="oInfo">
                <p> {{ item.poPaymentApplication.type | formatType(item.poPaymentApplication.amount) }}</p>
                <p v-if="item.poPaymentApplication.type !== '3'">票据 {{item.poPaymentApplication.billCount }} 张，共 {{item.poPaymentApplication.billAmount.toFixed(2) }} 元</p>
                <p v-else>本次付款申请没有附带的票据</p>
              </div>
            </div>
            <div class="txt_right">申请人：{{ item.poPaymentApplication.createName  }}
              {{item.poPaymentApplication.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>
<style lang="less">
#purchaseBillFinanceApproval{
  overflow: auto;
  .right{ float: right;  }
  .ui-cell{ display: block;  }
  .oInfo{ padding:5px 20px; }
  .txt_right{ text-align: right;  }
  .tabCount{
    top:0;
    right:0 ;
    left:auto;
  }
  .tabs-tab{
    width:33%;
  }
  .tip{
    padding:5px 12px;
    background-color: #E2EFDA;
    display: block;
  }
}
</style>
<script>
export default {
  name: 'purchaseBillFinanceApproval',
  data () {
    return {
      onlineAuditHandle: [],
      onlineAuditOK: [],
      offlineAuditHandle: [],
      listenersUid: [],
      loading: true,
      tabValue: '1'
    }
  },
  filters: {
    formatType: function (num, amount) {
      let str = ''
      switch (Number(num)) {
        case 1:
          str = '本次仅提交票据，不付款'
          break
        case 2:
          str = `申请付款${amount}元`
          break
        case 3:
          str = `申请付款${amount}元`
          break
      }
      return str
    },
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  created () {
    let that = this
    that.getList()
    that.tabValue = localStorage.navNum || 1
    this.listenersUid = [
      // 待在线审核
      this.sphdSocket.subscribe('paymentOnlineHandle', function (data) {
        let getData = JSON.parse(data)
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.onlineAuditHandle.unshift(getData)
        } else if (operate < 0) {
          let id = getData.poPaymentApplication.id
          let _index = -1
          that.onlineAuditHandle.forEach(function (item, index) {
            if (item.poPaymentApplication.id === id) {
              _index = index
            }
          })
          if (_index > -1) {
            that.onlineAuditHandle.splice(_index, 1)
          }
        }
      }, null, 'user'),
      // 在线审核ok
      this.sphdSocket.subscribe('paymentOnlineOKHandle', function (data) {
        let getData = JSON.parse(data)
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.onlineAuditOK.unshift(getData)
        } else if (operate < 0) {
          let id = getData.poPaymentApplication.id
          let _index = -1
          that.onlineAuditOK.forEach(function (item, index) {
            if (item.poPaymentApplication.id === id) {
              _index = index
            }
          })
          if (_index > -1) {
            that.onlineAuditOK.splice(_index, 1)
          }
        }
      }, null, 'user'),
      // 待线下审核
      this.sphdSocket.subscribe('paymentOfflineHandle', function (data) {
        let getData = JSON.parse(data)
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.offlineAuditHandle.unshift(getData)
        } else if (operate < 0) {
          let id = getData.poPaymentApplication.id
          let _index = -1
          that.offlineAuditHandle.forEach(function (item, index) {
            if (item.poPaymentApplication.id === id) {
              _index = index
            }
          })
          if (_index > -1) {
            that.offlineAuditHandle.splice(_index, 1)
          }
        }
      }, null, 'user')
    ]
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    getList: function () {
      let that = this
      this.axios.get('../../../purchaseInvoice/getFinanceInvoiceApply.do')
        .then(function (response) {
          let res = response.data.data
          that.loading = false
          that.onlineAuditHandle = res.paymentOnlineHandle || []
          that.onlineAuditOK = res.paymentOnlineOK || []
          that.offlineAuditHandle = res.paymentOfflineHandle || []
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    jump: function (id, pun) {
      localStorage.setItem('navNum', '1')
      this.$router.push({
        path: `/purchaseInvoiceDetails3/${id}/${pun}/0`
      })
    },
    search: function () {
      this.$router.push({
        path: `/reimburseVerificationBillsQuery`
      })
    }
  }
}
</script>
