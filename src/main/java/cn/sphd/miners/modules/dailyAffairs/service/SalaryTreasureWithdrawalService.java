package cn.sphd.miners.modules.dailyAffairs.service;

import cn.sphd.miners.modules.dailyAffairs.entity.SalaryTreasureWithdrawal;
import cn.sphd.miners.modules.finance.entity.FinancePaymentHistory;
import cn.sphd.miners.modules.system.dto.UserHonePageDto;
import cn.sphd.miners.modules.system.entity.UserPopedom;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by 19614 on 2020/9/23.
 */
public interface SalaryTreasureWithdrawalService {

    Map<String,Object> addSalaryTreasure(Integer oid, Integer userId, Integer salaryId, BigDecimal amount);

    List<SalaryTreasureWithdrawal> getSalaryApply(Integer userId,String approveStatus,Date beginDate,Date endDate,Integer type);

    List<SalaryTreasureWithdrawal> getSalaryApproval(Integer oid, Integer toUser, Integer fromUser,String mid,String approveStatusTreasure, String approveStatus,Integer businessType, Date beginDate,Date endDate,Integer type);

    Map<String,Object> getSalaryTreasureDetail(Integer salaryTreasureId);

    Map<String,Object> getSalaryPaymentApproval(Integer userId,Integer approvalProcessId);

    Map<String,Object> treasurePayableApproval(Integer userId,Integer approvalProcessId,String method,Date planDate,Date factDate,Integer accountId,String summary) throws Exception;

    FinancePaymentHistory getPaymentHistoryLast(Integer financePaymentId);

    List<FinancePaymentHistory> getPaymentHistoryByPaymentId(Integer financePaymentId);

    Map<String,Object> treasureRevoke(Integer approvalProcessId) throws Exception; //撤销

    Map<String,Object> getSalaryReviewApproval(Integer userId,Integer approvalProcessId);

    Map<String,Object> getSalaryPaidApproval(Integer userId,Integer approvalProcessId,Date factDate,String summary) throws Exception;

    List<UserHonePageDto> getTreasureApplyUserList(Integer toUserId, String userName, Integer businessType);// 查询审批人审批过得申请人列表，且按汉字排序

    Map<String,Object> updateTreasureMethod(Integer userId,Integer approvalProcessId,String method,Date planDate,Date factDate,Integer accountId,Integer type,String summary);

    Map<String,Object> getTreasureMethod(Integer salaryTreasureId);
}
