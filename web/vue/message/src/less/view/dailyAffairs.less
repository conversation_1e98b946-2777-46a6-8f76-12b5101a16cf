.container{
  width: 100%;
  overflow: auto;
  z-index: 10;
  height: calc(100% - 48px);
}
.leaveDetail{
  width: 100%;
  color: #788595;
  font-size: 14px;
  .applyName{
    font-size: 16px;
    font-weight: 600;
    color: #3e4b5b;
  }
  .applyState{
    color: @orange;
    font-weight: bold;
    position: absolute;
    right: 5px;
    top: 2px;
  }
  .applyContainer{
    .applyTime{
      small{
        background-color: #f0f2f6;
        padding: 3px 8px;
        font-weight: normal;
        border-radius: 10px;
        margin-left: -5px;
      }
    }
    .approveTime{
      position: absolute;
      right: 5px;
      top: 18px;
    }
    .applyContent{
      padding: 5px 8px;
    }
    .ui-title{
      font-size: 14px;
      border-left:4px solid @blue;
      padding-left:5px;
      color: #3e4b5b;
      line-height:1.2;
    }
    .applyHistory{
      padding:8px;
      font-size: 12px;
    }
  }
  .handle_button{
    text-align: right;
  }
}
.handle_button{
  text-align: right;
  padding:5px 0;
  .ui-btn{
    display: inline-block;
    padding: 8px 16px;
    border-radius: 2px;
    width: auto;
    height: auto;
    line-height:1;
    vertical-align: middle;
  }
}

.item-header{
  font-size: 14px;
  font-weight: 700;
  padding-left: 5px;
  border-left: 3px solid @grayC;
  margin: 16px 0;
  line-height: 1;
  color: #606266;
}
.item-content{
  padding:4px 0;
  &:not(:first-child){
    border-top: 1px solid @grayE;
  }
}
.tip{
  padding: 0 8px;
  font-size: 12px;
  box-sizing: border-box;
  border-radius: 2px;
  position: relative;
  overflow: hidden;
  opacity: 1;
  display: flex;
  align-items: center;
  transition: opacity .2s;
  margin: 8px 0 0 0;
}

.tip-success{
  background-color: #ececec;
  color: #a6b0bc;
  border: 1px dashed #e4e4e4;
}

.el-form-item {
  margin-bottom: 0px;
}

.el-dialog__body {
  padding: 30px 20px 0;
}

#monthDetail .ui-cells_none{
  text-align: center;
  height: 350px;
  font-size: 12px;
  overflow: auto;
}
#monthDetail .ui-cell{
  font-size: 1rem;
}
.ui-message .overTimeQuery{
  background-color: #7cb5f5;
  color: #fff;
  overflow: hidden;
  .allTime,.splitTime{
    float: left;
    height: 52px;
    text-align: center;
    .dec{
      font-size: 12px;
      color: #cee5ff;;
      text-align: center;
    }
  }
  .allTime{
    width: 100px;
    border-right:1px solid #89baf0;
    h3{
      display: inline;
      font-size: 24px;
      color: #fcfcfc;
      font-weight: normal;
      margin: 4px;
      line-height: 32px;
    }
  }
  .splitTime{
    padding: 0 8px;
    width: 164px;
    a{
      display: block;
      width: 100%;
      cursor: pointer;
      h4{
        display: inline;
        font-size: 18px;
        color: #fcfcfc;
        font-weight: normal;
        margin: 0 4px 0;
      }
      .dec{
        margin-left: 5px;
      }
      &:hover{
        background-color: #77ade9;
      }
    }
  }
}
.ui-message .leaveQuery{
  background-color: #7cb5f5;
  color: #fff;
  overflow: hidden;
  padding: 0.5rem 1rem;
  .tabItem{
    cursor: pointer;
    &:hover{
      background-color: #77ade9;
    }
    .queryItem{
      text-align: center;
      color: #fff;
      line-height:2;
      width: 100%;
    }
  }
}

