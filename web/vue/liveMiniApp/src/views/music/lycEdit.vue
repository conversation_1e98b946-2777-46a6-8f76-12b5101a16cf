<template>
  <div class="lyricEdit">
    <Pageheader title="修 改" :showBack="true"/>
    <div class="live-container">
      <div>
        <div class="tr">
          <span>修改{{ pageType }}基本信息</span>
          <span class="live-right line-link" @click="goDetails('edutLyricInfo')">修改</span>
        </div>
        <div class="tr">
          <span>选择或更换原唱</span>
          <el-upload v-if="uploadBtn1_txt != '正在上传...'"
                     class="avatar-uploader"
                     :headers="uploadHeaders"
                     :action="uploadAction"
                     ref="upload"
                     :limit="fileLimit"
                     :accept="uploadLyc" :data="postData"
                     :multiple="false" :show-file-list="false"
                     :on-remove="uploadRemove"
                     :on-success="handleSuccess"
                     :before-upload="beforeUpload"
          >
            <el-link type="primary" class="btnTxt">{{ uploadBtn1_txt }}</el-link>
          </el-upload>
          <el-link v-else type="primary" class="btnTxt">{{ uploadBtn1_txt }}</el-link>

        </div>
        <div class="tr">
          <span>选择或更换伴奏</span>
          <el-upload v-if="uploadBtn2_txt != '正在上传...'"
                     class="avatar-uploader"
                     :headers="uploadHeaders"
                     :action="uploadAction"
                     ref="upload"
                     :limit="fileLimit"
                     :accept="uploadLyc" :data="postData"
                     :multiple="false" :show-file-list="false"
                     :on-remove="uploadRemove"
                     :on-success="handleSuccess2"
                     :before-upload="beforeUpload2"
          >
            <el-link type="primary" class="btnTxt">{{ uploadBtn2_txt }}</el-link>
          </el-upload>
          <el-link v-else type="primary" class="btnTxt">{{ uploadBtn2_txt }}</el-link>
        </div>
        <div class="tr">
          <span class="redTxt">仅重新配节奏，歌词不变</span>
          <span class="live-right line-link">修改</span>
        </div>
        <div class="tr">
          <span class="live-red">{{ pageType }}与节奏都重新设置</span> <br> &nbsp;
          <span class="live-right line-link" >自行设置</span>
          <span class="live-right line-link" style="margin-right: 30px;">发布任务，由团队成员操作 </span>
        </div>
        <div style="margin-top: 50px">
          说明：<br>
          由团队成员进行的修改须经主播审批后生效。<br>
          其他修改后即生效。<br>
        </div>
      </div>
    </div>

  </div>
</template>
<script setup>

import { getAudioFileDuratuin, updateSongBgmOrOriginal } from '@/api/music';
import { ref, reactive, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus'
import { useRouter } from "vue-router";
import JSONBig from 'json-bigint'
import auth from '@/sys/auth.js'



const router = useRouter();
const webRoot = localStorage.getItem('webRoot');
const pageType = ref('')
const quType = localStorage.getItem('quType')
pageType.value = quType == 2 ? '戏曲' : '歌曲'

const uploadHeaders = { 'Token': auth.getToken() }
const uploadAction = webRoot + '/uploads/uploadfyByFile.do'
const uploadLyc = '.mp3,.cda,.wav,.aif,.aiff,.mid,.wma,.ra,.vqf,.ape'
const fileLimit = ''
const uploadUrl = localStorage.getItem('uploadUrl');
const banEditable = ref(false);
const uploadBtn1_txt = ref('操作');
const postData = reactive({
  module: '字幕精灵',
  userId: auth.getUserID()
})
const formData = reactive({
  trackDuration:'',
  bgmDuration:'',
  publishType:'',
  rhythmOption:'',
  originalPath:'',
  bgmPath:'',
  inllustration:'',
  firstSentence:'',
  lastSentence:'',
  approveStatus:'',
  rhythm:'',
  lyrics:'',
  user:'',
  duration:'',// 歌曲时长
  banTime: { min:'', ss:'' },
  oralTime: { min:'', ss:'' },
  uploadType: '',
  orlLyc:{ duration:'', durationTime:'', filePath:'', state:'' },
  banLyc:{ duration:'', durationTime:'', filePath:'', state:'' },
})

const goDetails = (url) => {
  router.push({ name: url});
}
const uploadRemove = (file) => {
  console.log('uploadRemove = ', file)
}
const handleSuccess2 = (res,file) => { // 伴唱
  uploadBtn2_txt.value = '更换'
  banEditable.value = true
  console.log('handleSuccess =', res)
  formData.banLyc.filePath = res.filename
  formData.banLyc.fileUid = res.fileUid
  const audio = new Audio()
  audio.src = uploadUrl + res.filename
  countAudioTime(audio, 'banLyc')
}
const handleSuccess = (res,file) => { // 原唱
  uploadBtn1_txt.value = '更换'
  banEditable.value = true
  console.log('handleSuccess =', res)
  formData.orlLyc.filePath = res.filename
  formData.orlLyc.fileUid = res.fileUid
  const audio = new Audio()
  audio.src = uploadUrl + res.filename
  countAudioTime(audio, 'orlLyc')

}
const beforeUpload2 = (file) => {
  uploadBtn2_txt.value = '正在上传...'
  console.log(' beforeUpload file=', file)
  formData.banLyc = { 'filePath':'', 'fileUid':'', 'duration':'', 'durationTime':'' }
  formData.banLyc.filename = file.name
}
const beforeUpload = (file) => {
  uploadBtn1_txt.value = '正在上传...'
  formData.orlLyc = { 'filePath':'', 'fileUid':'', 'duration':'', 'durationTime':'' }
  formData.orlLyc.filename = file.name
}
const  countAudioTime = async (audio , type) => {
  while (isNaN(audio.duration) || audio.duration === Infinity) {
    // 延迟一会 不然网页都卡死
    await new Promise(resolve => setTimeout(resolve, 200));
  }
  let dur0 = audio.duration ; // 单位是 秒
  let dain = String(dur0).split('.')[1]
  let dur = String(dur0).split('.')[0]
  console.log('音频的总时长:',dur)
  formData[type].duration = dur0 // 秒
  let min = parseInt(dur/60)
  let ss = dur%60
  formData[type].durationTime = `${min}:${ss}.${dain}` // 12:12.909090
  formData[type].durationTime2 = `${min}:${ss}` // 12:12.909090
  console.log('min=', min)
  console.log('ss=', ss)

  if(type === 'banLyc'){
    if(formData.oralTime.min){

    }else{
      formData.banTime.min = min
      formData.banTime.ss = ss
    }
  }else if(type === 'orlLyc'){
    formData.oralTime = { 'min':min, 'ss': ss}
    formData.banTime.min = min
    formData.banTime.ss = ss
  }
  debugger
  let lyricDetailsItem = JSONBig.parse(localStorage.getItem('lyricDetailsItem'));
  let params = {  id:  lyricDetailsItem.id, module: '字幕精灵', }
  if(type === 'orlLyc'){ // 原唱
    params.originalPath = formData.orlLyc.filePath
    params.trackDuration = Math.round(formData.orlLyc.duration * 1000)
  }else if(type === 'banLyc'){
    params.bgmPath = formData.banLyc.filePath
    params.bgmDuration = Math.round(formData.banLyc.duration * 1000)
  }
  updateSongBgmOrOriginal(params).then(res1=>{
    let res = res1.data
    if(res.state == 1){
      ElMessage.success('上传成功！');

    }else{
      ElMessage.error('上传成功！');

    }
    console.log('updateSongBgmOrOriginal res=', res)
  }).catch(err=>{
    console.log('err=', err)
    ElMessage.error('链接失败');
  })


}


</script>
<style lang="scss" scoped>
.lyricEdit{
  .tr{
    line-height: 50px;
  }
  .avatar-uploader{
    display: inline-block;
    position: relative;
    float: right;

  }
  .btnTxt{
    font-size:15px;
    float: right;
  }

}
</style>
