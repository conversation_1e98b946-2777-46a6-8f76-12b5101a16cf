<template>
  <div id="borrowFileApply">
    <TY_NavTop title="文件借阅申请" isBackHome="false" isSearch="true" @toggleList="screen"></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待审批" name="1" closable='false'>
        <div class="tip">您已提交下列文件的借阅申请，有待审批。</div>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="applyTo(item.id, 11)" v-for="(item, index) in listApplyReadFile" :key="index">
            <div class="fileLists">
              <div class="fileType file_xls"></div>
              <div class="fileInfo">
                <div class="fileName" v-text="item.name" :title="item.name"></div>
                <div class="fileDetail">{{item.createName}} {{item.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
              <div class="fileFormat">
                <span class="fileVersion">G{{item.changeNum}}</span>
                <span class="fileNo">编号：{{item.fileSn}}</span>
              </div>
            </div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="近期可阅览的文件" name="2" closable='false' :msgCount="unreadFileNum">
        <div class="tip">您已获得以下文件的阅览权限!</div>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="applyTo(item.id, 12)" v-for="(item, index) in listApproveReadFile" :key="index">
            <div class="fileLists">
              <div class="fileType file_xls"></div>
              <div class="fileInfo">
                <div class="fileName" v-text="item.name" :title="item.name"></div>
                <div class="fileDetail">{{item.createName}} {{item.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
              <div class="fileFormat">
                <span class="fileVersion">G{{item.changeNum}}</span>
                <span class="fileNo">编号：{{item.fileSn}}</span>
              </div>
            </div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>

<style lang="less">
  #borrowFileApply {
    .tabCount{
      position: absolute;
      left: calc(50% + 50px)
    }
  }
</style>

<script>
export default {
  name: 'borrowFileApply',
  data: function () {
    return {
      loading: true,
      weekDay: '',
      listApplyReadFile: [],
      listApproveReadFile: [],
      listenersUid: [],
      unreadFileNum: 0
    }
  },
  created: function () {
    let that = this
    let userId = that.sphdSocket.user.userID
    this.$http.post('../../../read/getApplyReadFile.do', { 'userID': userId }, {
      emulateJSON: true
    }).then((response) => {
      let reqData = response.body.data
      that.listApplyReadFile = reqData.listApplyReadFile
      that.listApproveReadFile = reqData.listApprovedReadFile
      that.unreadFileNum = reqData.unreadFileNum
      that.loading = false
    }).catch(function () {
      that.loading = false
      that.$message({
        type: 'error',
        message: '系统错误，请重试！'
      })
    })
    this.listenersUid = [
      this.sphdSocket.subscribe('applyReadFile', function (data) { // 终审时更新数据
        console.log('文件借阅申请待审批通道:' + data)
        let reqData = JSON.parse(data)
        let operate = reqData.operate
        if (operate > 0) { // 增加一条
          console.log('增加一条')
          let newFile = reqData.rd
          that.listApplyReadFile.push(newFile)
        } else if (operate < 0) { // 减少一条
          console.log('减少一条')
          let deleteId = reqData.rd.id
          that.listApplyReadFile.forEach(function (item, index) {
            if (deleteId === item.id) {
              that.listApplyReadFile.splice(index, 1)
            }
          })
        }
      }, null, 'user'),
      this.sphdSocket.subscribe('approvedReadFile', function (data) { // 终审时更新数据
        console.log('文件借阅申请近期可阅览文件通道:' + data)
        let reqData = JSON.parse(data)
        let update = reqData.operate
        if (update > 0) { // 增加一条
          console.log('增加一条')
          let newFile = reqData.rd
          that.listApproveReadFile.push(newFile)
          that.unreadFileNum = that.unreadFileNum + 1
        } else if (update < 0) { // 减少一条
          console.log('减少一条')
          let deleteId = reqData.rd.id
          that.listApproveReadFile.forEach(function (item, index) {
            if (deleteId === item.id) {
              that.listApproveReadFile.splice(index, 1)
            }
          })
          that.unreadFileNum = that.unreadFileNum - 1
        }
      }, null, 'user')
    ]
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    applyTo: function (id, mark) {
      this.$router.push({
        path: `/borrowFileDetail/${id}/${mark}`
      })
    },
    screen: function () {
      this.$router.push({
        path: '/borrowQuery/apply'
      })
    }
  }
}
</script>
