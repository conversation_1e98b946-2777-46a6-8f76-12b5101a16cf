package cn.sphd.miners.modules.material.service.impl;

import cn.sphd.miners.modules.material.dao.MtStorageDao;
import cn.sphd.miners.modules.material.entity.MtStorage;
import cn.sphd.miners.modules.material.service.MtStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by Administrator on 2017/8/2.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class MtStorageServiceImpl implements MtStorageService {
    @Autowired
    MtStorageDao mtStorageDao;
    @Override
    public void addMtStorage(MtStorage mtStorage) {
        mtStorageDao.save(mtStorage);
    }
}
