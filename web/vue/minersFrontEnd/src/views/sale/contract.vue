<template>
  <div class="contact">
    <el-dialog v-model="dialog_visible_imgShow">
      <div class="text-center">
        <img w-full :src="imgShow" alt="Preview Image" style="width: 100%"/>
      </div>
    </el-dialog>
    <!--新增合同、修改合同、续约合同-->
    <el-dialog
        v-model="dialog_visible_editContract"
        :title="editContractTitle"
        width="600"
        :close-on-click-modal="false"
        :draggable="true"
        id="editContract"
    >
      <div class="mainCon" style="width: 500px; margin: 0 auto">
        <el-form
            :rules="editContractRules"
            label-position="top"
            ref="editContractForm"
            class="editContract"
            :model="form_editContract"
            label-width="110"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="客户" prop="customer" v-if="(page === 'seeContractByAll' || page === 'main') && form_editContract.editType === 1">
                <el-select v-model="form_editContract.customer" @change="change_customer">
                  <el-option
                      v-for="item in option_customers"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="客户" prop="fullName" v-else>
                <el-input v-model="form_editContract.cusInfo.fullName" placeholder="请录入" readonly disabled/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="合同编号" prop="sn">
                <el-input v-model="form_editContract.sn" placeholder="请录入"/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="签署日期" prop="signTime">
                <el-date-picker
                    v-model="form_editContract.signTime"
                    placeholder="请录入"
                    type="date"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    style="width: 100%"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="合同的有效期" prop="validTime">
                <el-date-picker
                    v-model="form_editContract.validTime"
                    type="daterange"
                    range-separator="到"
                    start-placeholder="请选择"
                    end-placeholder="请选择"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    :unlink-panels="true"
                    :disabled-date="disabledDate"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="合同的扫描件或照片(共可上传9张)" prop="imgList">
            <template #label>
              合同的扫描件或照片(共可上传9张)
              <div style="float: right">
                <el-button type="primary" link @click="$refs.uploadImgBtn.click()">上传</el-button>
              </div>
            </template>
            <div class="fileBox">
              <el-upload
                  v-model:file-list="form_editContract.imgList"
                  :headers="imgUpload.uploadHeaders"
                  :action="imgUpload.uploadAction"
                  :accept="imgUpload.uploadLyc"
                  :data="imgUpload.postData"
                  :limit="9"
                  :multiple="true"
                  :on-success="editContract_imgSuccess"
                  :on-preview="editContract_imgPreview"
                  :on-remove="editContract_imgRemove"
                  :on-exceed="editContract_imgHandleExceed"
                  list-type="picture-card"
              >
                <span style="display: none" ref="uploadImgBtn">上传</span>
              </el-upload>
            </div>
          </el-form-item>
          <el-form-item label="合同的可编辑版" prop="fileList">
            <template #label style="padding-right: 0;">
              合同的可编辑版
              <div style="float: right">
                <el-button type="primary" link @click="$refs.uploadFileBtn.click()">上传</el-button>
              </div>
            </template>
            <div class="fileBox">
              <el-upload
                  class="upload_contract-file"
                  v-model:file-list="form_editContract.fileList"
                  :headers="fileUpload.uploadHeaders"
                  :action="fileUpload.uploadAction"
                  :accept="fileUpload.uploadLyc"
                  :data="fileUpload.postData"
                  ref="upload"
                  :limit="1"
                  :on-exceed="editContract_fileHandleExceed"
                  :on-success="editContract_fileSuccess"
              >
                <span style="display: none" ref="uploadFileBtn">上传</span>
              </el-upload>
            </div>
          </el-form-item>
          <div v-show="form_editContract.customer || form_editContract.cusInfo.id">
            <el-form-item prop="selectZS">
              <template #label style="padding-right: 0;">
                本合同下的专属商品
                <el-button type="primary" link @click="manage_ZSGood(form_editContract)">{{form_editContract.selectZS.length}}</el-button>
                种
                <div style="float: right">
                  <el-button type="primary" link @click="editContract_removeGood('ZS')">移出商品</el-button>
                  <el-button type="primary" link @click="editContract_addGood('ZS')">添加商品</el-button>
                </div>
              </template>
              <el-input :value="filter_good('ZS')" readonly disabled/>
            </el-form-item>
            <el-form-item prop="selectTY">
              <template #label style="padding-right: 0;">
                本合同下的通用型商品
                <el-button type="primary" link @click="manage_TYGood(form_editContract)">{{form_editContract.selectTY.length}}</el-button>
                种
                <div style="float: right">
                  <el-button type="primary" link @click="editContract_removeGood('TY')">移出商品</el-button>
                  <el-button type="primary" link @click="editContract_addGood('TY')">添加商品</el-button>
                </div>
              </template>
              <el-input :value="filter_good('TY')" readonly disabled/>
            </el-form-item>
          </div>

          <el-form-item label="备注" prop="memo">
            <el-input v-model="form_editContract.memo" placeholder="请录入"/>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_editContract = false">取消</el-button>
          <el-button type="primary" @click="editContract_submit">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 增加删除商品（包括专属、通用）-->
    <el-dialog
        v-model="dialog_visible_manage_changeGood"
        :title="form_changeGood.title"
        width="900"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 800px; margin: 0 auto">
        <div class="row">
          {{form_changeGood.tips}}
          <div class="rightBtn">
            已选 {{form_changeGood.select.length}} 种
          </div>
        </div>
        <el-table
            ref="table_changeGood"
            :data="form_changeGood.list"
            border
            @selection-change="editContract_changeGood_handleCheck"
            style="width: 100%; margin-top: 16px">
          <el-table-column type="selection" width="55" />
          <el-table-column label="商品代号" prop="outerSn"></el-table-column>
          <el-table-column label="商品名称" prop="outerName"></el-table-column>
          <el-table-column label="型号" prop="model"></el-table-column>
          <el-table-column label="规格" prop="specifications"></el-table-column>
          <el-table-column label="计量单位" prop="unit"></el-table-column>
          <el-table-column label="已包含的合同">
            <template #default="scope">
              <el-button type="primary" link @click="getAllContractByGood(scope.row)"><b>{{scope.row.contractNum || 0}}个</b></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_manage_changeGood = false">取消</el-button>
          <el-button type="primary" @click="editContract_changeGood_submit">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 查看商品（包括专属、通用）-->
    <el-dialog
        v-model="dialog_visible_manage_seeGood"
        title="本合同下的通用型商品"
        width="900"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 800px; margin: 0 auto">
        <div class="row">
          本合同下的通用型商品共有以下{{form_seeGood.list?.length}}种

        </div>
        <el-table :data="form_seeGood.list" border style="width: 100%; margin-top: 16px">
          <el-table-column label="商品代号" prop="outerSn"></el-table-column>
          <el-table-column label="商品名称" prop="outerName"></el-table-column>
          <el-table-column label="型号" prop="model" width="80"></el-table-column>
          <el-table-column label="规格" prop="specifications" width="80"></el-table-column>
          <el-table-column label="计量单位" prop="unit" width="100"></el-table-column>
          <el-table-column label="已包含的合同" width="120">
            <template #default="scope">
              <el-button type="primary" link @click="getAllContractByGood(scope.row)"><b>{{scope.row.contractNum || 0}}个</b></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_manage_seeGood = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_manage_img"
        title="合同的扫描件或照片"
        width="700"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 600px; margin: 0 auto">
        <el-image
            v-for="(item, index) in list_seeContractImg.list"
            :key= index
            style="width: 80px; height: 80px"
            :src="item.url"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="list_seeContractImg.preview"
            :initial-index="index"
            fit="cover"
        />
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_manage_img = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_manage_change"
        title="修改合同信息"
        width="500"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 400px; margin: 0 auto">
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_manage_change = false">取消</el-button>
          <el-button type="primary" @click="dialog_visible_manage_change = false">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_manage_stop"
        title="暂停履约/合同终止"
        width="500"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 400px; margin: 0 auto">
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_manage_stop = false">取消</el-button>
          <el-button type="primary" @click="dialog_visible_manage_stop = false">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_manage_changeHis"
        title="本版本合同的修改记录"
        width="700"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 600px; margin: 0 auto">
        <div class="row">
          当前数据为本版本合同第{{seeContractChangeHis.list.length - 1}}次修改后的结果。
          <div class="rightBtn">
            修改时间：{{$filter.format(seeContractChangeHis.list.at(-1)?.updateDate || seeContractChangeHis.list.at(-1)?.createDate)}}
          </div>
        </div>
        <el-table :data="seeContractChangeHis.list" border style="width: 100%; margin-top: 16px">
          <el-table-column label="记录">
            <template #default="scope">
              {{scope.$index === 0?'本版本合同的原始信息':'第' +scope.$index+ '次修改后'}}
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button type="primary" link @click="manage_seeContractDetail(scope.row, 'change')"><b>查看</b></el-button>
            </template>
          </el-table-column>
          <el-table-column label="创建者/修改者" width="250">
            <template #default="scope">
              {{scope.row.updateName || scope.row.createName}}
              {{$filter.format(scope.row.updateDate || scope.row.createDate)}}
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_manage_changeHis = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_manage_renewHis"
        title="本合同的续约记录"
        width="700"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 600px; margin: 0 auto">
        <el-table :data="seeContractRenewHis.list" border style="width: 100%; margin-top: 16px">
          <el-table-column label="版本" width="200">
            <template #default="scope">
              {{scope.$index === 0?'第1版（原始版本）':'第' + (scope.$index + 1) + '版（第' + scope.$index + '次续约后）'}}
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button type="primary" link @click="manage_seeContractDetail(scope.row, 'renew')"><b>查看</b></el-button>
            </template>
          </el-table-column>
          <el-table-column label="创建（时间为各版本合同的续约时间）" width="300">
            <template #default="scope">
              {{scope.row.updateName || scope.row.createName}}
              {{$filter.format(scope.row.updateDate || scope.row.createDate)}}
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_manage_renewHis = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_seeContract"
        title="查看合同"
        width="700"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 600px; margin: 0 auto">
        <el-descriptions
            class="margin-top"
            :column="1"
            border
        >
          <el-descriptions-item label="合同编号" width="200">{{seeContractDetail.info.sn}}</el-descriptions-item>
          <el-descriptions-item label="签署日期">{{seeContractDetail.info.signTime}}</el-descriptions-item>
          <el-descriptions-item label="合同的有效期">
            {{seeContractDetail.info.validStart}} 至 {{seeContractDetail.info.validEnd}}
          </el-descriptions-item>
          <el-descriptions-item label="合同的扫描件或照片">
            <el-image
                v-for="(item, index) in seeContractDetail.info.imgList"
                :key= index
                style="width: 40px; height: 40px"
                :src="item.url"
                :zoom-rate="1.2"
                :max-scale="7"
                :min-scale="0.2"
                :preview-src-list="seeContractDetail.info.previewList"
                :initial-index="index"
                fit="cover"
                :preview-teleported="true"
            />
          </el-descriptions-item>
          <el-descriptions-item label="合同的可编辑版">
            <el-button type="primary" link @click="preview_File(seeContractDetail.info.filePath)" v-if="seeContractDetail.info.filePath"><b>查看</b></el-button>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              本合同下专属的商品（共{{seeContractDetail.info.listZS?.length}}种）
            </template>
            <el-button type="primary" link @click="manage_ZSGood(seeContractDetail.info)"><b>查看</b></el-button>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              本合同下通用型的商品（共{{seeContractDetail.info.listTY?.length}}种）
            </template>
            <el-button type="primary" link @click="manage_TYGood(seeContractDetail.info)"><b>查看</b></el-button>
          </el-descriptions-item>
          <el-descriptions-item label="备注">{{seeContractDetail.info.memo}}</el-descriptions-item>
          <el-descriptions-item label="本版本合同的修改记录" v-show="seeContractDetail.info.origin === 'renew'">
            <el-button type="primary" link @click="manage_changeHis(seeContractDetail.info)"><b>查看</b></el-button>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_seeContract = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_recoveryExpire"
        title="！提示"
        width="500"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 400px; margin: 0 auto">
        本合同已于{{recoveryExpire.validEnd}}到期。请确定具体要进行哪项操作：
        <el-radio-group v-model="recoveryExpire.radio" style="flex-direction: column; align-items: flex-start; margin: 16px 0; padding-left: 46px">
          <el-radio :value="1">不再执行，转入“已到期的合同”</el-radio>
          <el-radio :value="2">续约</el-radio>
        </el-radio-group>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_recoveryExpire = false">取消</el-button>
          <el-button type="primary" @click="manage_recoveryExpire_submit()">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
        v-model="dialog_visible_seeContractByGood"
        title="已包含某商品的销售合同"
        width="900"
        :close-on-click-modal="false"
        :draggable="true"
    >
      <div class="mainCon" style="width: 800px; margin: 0 auto">
        <div class="row" style="margin-top: 16px">
          <el-text size="large">
            {{form_seeContractByGood.goodStr}}
          </el-text>
        </div>
        <el-table
            :data="form_seeContractByGood.list" border style="width: 100%; margin-top: 16px">
          <el-table-column label="合同编号" prop="sn"></el-table-column>
          <el-table-column label="所涉商品">
            <template #default="scope">
              {{scope.row.commodityCount}} 种
            </template>
          </el-table-column>
          <el-table-column label="有效期" width="220">
            <template #default="scope">
              {{$filter.format(scope.row.validStart, 'day')}} 至 {{$filter.format(scope.row.validEnd, 'day')}}
            </template>
          </el-table-column>
          <el-table-column label="签署日期" width="120">
            <template #default="scope">
              {{$filter.format(scope.row.signTime, 'day')}}
            </template>
          </el-table-column>
          <el-table-column label="本版本合同的创建" width="220">
            <template #default="scope">
              {{scope.row.createName}} {{$filter.format(scope.row.createDate)}}
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_visible_seeContractByGood = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <div class="ty-container" id="home">
      <div class="page">
        <section v-show="page === 'main'">
          <div class="row head" style="display: flex; align-items: center">
            本公司的销售合同统计如下：
            <div class="rightBtn">
              <el-button type="primary" @click="editContract(1)">新增合同</el-button>
              <el-button type="primary" @click="seeAllContract">直接查看全部合同</el-button>
            </div>
          </div>
          <el-table :data="customerContractList" border style="width: 100%; margin-top: 16px">
            <el-table-column label="客户">
              <template #default="scope">
                {{ scope.row.fullName }}
              </template>
            </el-table-column>
            <el-table-column label="专属商品">
              <template #default="scope">
                {{scope.row.numZS}}种
              </template>
            </el-table-column>
            <el-table-column label="有效期内的合同">
              <template #default="scope">
                {{scope.row.contractNum}}个
              </template>
            </el-table-column>
            <el-table-column label="所涉商品">
              <template #default="scope">
                {{scope.row.commodityCount}}种
              </template>
            </el-table-column>
            <el-table-column label="所涉专属商品">
              <template #default="scope">
                {{scope.row.inZSnum}}种
              </template>
            </el-table-column>
            <el-table-column label="没有在执行的合同">
              <template #default="scope">
                {{scope.row.otherContractNum}}个
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <el-button type="primary" link @click="seeCustomer(scope.row)"><b>查看</b></el-button>
              </template>
            </el-table-column>
          </el-table>
        </section>
        <section v-show="page === 'seeContractByCus'">
          <div class="nav" style="display: flex;justify-content: space-between ;margin-bottom: 12px">
            <div>
              <el-button type="default" @click="this.page = 'main'">返回</el-button>
              <el-button type="primary" @click="editContract(1)">新增合同</el-button>
            </div>
            <div class="rightBtn">
              <el-button type="primary" @click="seeExpireContract()">已到期的合同</el-button>
              <el-button type="primary" @click="seeStopContract()">已暂停/终止的合同</el-button>
            </div>
          </div>
          <div class="row" style="margin-top: 16px">
            <el-text size="large">
              {{seeContractByCus.info.code}} {{seeContractByCus.info.fullName}}
            </el-text>
          </div>
          <div class="row" style="margin-top: 16px">
            <el-text>
              处于有效期内的销售合同共{{seeContractByCus.list1.length}}个，具体如下
            </el-text>
          </div>
          <el-table :data="seeContractByCus.list1" border style="width: 100%; margin-top: 16px">
            <el-table-column label="合同编号">
              <template #default="scope">
                {{scope.row.sn}}
              </template>
            </el-table-column>
            <el-table-column label="所涉商品" width="100">
              <template #default="scope">
                {{scope.row.commodityCount}}种
              </template>
            </el-table-column>
            <el-table-column label="有效期" width="250">
              <template #default="scope">
                {{$filter.format(scope.row.validStart, 'day')}} 至 {{$filter.format(scope.row.validEnd, 'day')}}
              </template>
            </el-table-column>
            <el-table-column label="签署日期" width="150">
              <template #default="scope">
                {{$filter.format(scope.row.signTime, 'day')}}
              </template>
            </el-table-column>
            <el-table-column label="备注" width="150">
              <template #default="scope">
                {{scope.row.memo}}
              </template>
            </el-table-column>
            <el-table-column label="本版本合同的创建" width="250">
              <template #default="scope">
                {{scope.row.createName}} {{$filter.format(scope.row.createDate)}}
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <el-dropdown trigger="click">
                  <span class="el-dropdown-link">
                    <el-button type="primary" link><b>管理</b></el-button>
                  </span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="manage_ZSGood(scope.row)">本合同下的专属商品</el-dropdown-item>
                      <el-dropdown-item @click="manage_TYGood(scope.row)">本合同下的通用型商品</el-dropdown-item>
                      <el-dropdown-item @click="manage_file(scope.row)">合同的可编辑版</el-dropdown-item>
                      <el-dropdown-item @click="manage_img(scope.row)">合同的扫描件或照片</el-dropdown-item>
                      <el-dropdown-item @click="manage_change(scope.row)">修改合同信息</el-dropdown-item>
                      <el-dropdown-item @click="manage_renew(scope.row, 1)">续约</el-dropdown-item>
                      <el-dropdown-item @click="manage_stop(scope.row)">暂停履约/合同终止</el-dropdown-item>
                      <el-dropdown-item @click="manage_changeHis(scope.row)">本版本合同的修改记录</el-dropdown-item>
                      <el-dropdown-item @click="manage_renewHis(scope.row)">本合同的续约记录</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </section>
        <section v-show="page === 'seeExpireContractByCus'">
          <div class="nav" style="display: flex;justify-content: space-between ;margin-bottom: 12px">
            <div>
              <el-button type="default" @click="this.page = 'seeContractByCus'">返回</el-button>
            </div>
          </div>
          <div class="row" style="margin-top: 16px">
            <el-text size="large">
              {{seeContractByCus.info.code}} {{seeContractByCus.info.fullName}}
            </el-text>
          </div>
          <div class="row" style="margin-top: 16px">
            <el-text>
              已到期的销售合同{{seeContractByCus.list3.length}}个，具体如下
            </el-text>
          </div>
          <el-table :data="seeContractByCus.list3" border style="width: 100%; margin-top: 16px">
            <el-table-column label="合同编号">
              <template #default="scope">
                {{scope.row.sn}}
              </template>
            </el-table-column>
            <el-table-column label="所涉商品" width="100">
              <template #default="scope">
                {{scope.row.commodityCount}}种
              </template>
            </el-table-column>
            <el-table-column label="有效期" width="250">
              <template #default="scope">
                {{$filter.format(scope.row.validStart, 'day')}} 至 {{$filter.format(scope.row.validEnd, 'day')}}
              </template>
            </el-table-column>
            <el-table-column label="签署日期" width="150">
              <template #default="scope">
                {{$filter.format(scope.row.signTime, 'day')}}
              </template>
            </el-table-column>
            <el-table-column label="备注" width="150">
              <template #default="scope">
                {{scope.row.memo}}
              </template>
            </el-table-column>
            <el-table-column label="本版本合同的创建" width="250">
              <template #default="scope">
                {{scope.row.createName}} {{$filter.format(scope.row.createDate)}}
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <el-dropdown trigger="click">
                  <span class="el-dropdown-link">
                    <el-button type="primary" link><b>管理</b></el-button>
                  </span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="manage_ZSGood(scope.row)">本合同下专属的商品</el-dropdown-item>
                      <el-dropdown-item @click="manage_TYGood(scope.row)">本合同下的通用型商品</el-dropdown-item>
                      <el-dropdown-item @click="manage_file(scope.row)">合同的可编辑版</el-dropdown-item>
                      <el-dropdown-item @click="manage_img(scope.row)">合同的扫描件或照片</el-dropdown-item>
                      <el-dropdown-item @click="manage_renew(scope.row, 3)">续约</el-dropdown-item>
                      <el-dropdown-item @click="manage_changeHis(scope.row)">本版本合同的修改记录</el-dropdown-item>
                      <el-dropdown-item @click="manage_renewHis(scope.row)">本合同的续约记录</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </section>
        <section v-show="page === 'seeStopContractByCus'">
          <div class="nav" style="display: flex;justify-content: space-between ;margin-bottom: 12px">
            <div>
              <el-button type="default" @click="this.page = 'seeContractByCus'">返回</el-button>
            </div>
          </div>
          <div class="row" style="margin-top: 16px">
            <el-text size="large">
              {{seeContractByCus.info.code}} {{seeContractByCus.info.fullName}}
            </el-text>
          </div>
          <div class="row" style="margin-top: 16px">
            <el-text>
              已暂停履约/终止的销售合同共{{seeContractByCus.list2.length}}个，具体如下
            </el-text>
          </div>
          <el-table :data="seeContractByCus.list2" border style="width: 100%; margin-top: 16px">
            <el-table-column label="合同编号">
              <template #default="scope">
                {{scope.row.sn}}
              </template>
            </el-table-column>
            <el-table-column label="所涉商品" width="100">
              <template #default="scope">
                {{scope.row.commodityCount}}种
              </template>
            </el-table-column>
            <el-table-column label="有效期" width="250">
              <template #default="scope">
                {{$filter.format(scope.row.validStart, 'day')}} 至 {{$filter.format(scope.row.validEnd, 'day')}}
              </template>
            </el-table-column>
            <el-table-column label="签署日期" width="150">
              <template #default="scope">
                {{$filter.format(scope.row.signTime, 'day')}}
              </template>
            </el-table-column>
            <el-table-column label="备注" width="150">
              <template #default="scope">
                {{scope.row.memo}}
              </template>
            </el-table-column>
            <el-table-column label="本版本合同的创建" width="250">
              <template #default="scope">
                {{scope.row.createName}} {{$filter.format(scope.row.createDate)}}
              </template>
            </el-table-column>
            <el-table-column label="暂停履约/终止的操作" width="250">
              <template #default="scope">
                {{scope.row.createName}} {{$filter.format(scope.row.createDate)}}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-dropdown trigger="click">
                  <span class="el-dropdown-link">
                    <el-button type="primary" link><b>管理</b></el-button>
                  </span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="manage_ZSGood(scope.row)">本合同下的专属商品</el-dropdown-item>
                      <el-dropdown-item @click="manage_TYGood(scope.row)">本合同下的通用型商品</el-dropdown-item>
                      <el-dropdown-item @click="manage_file(scope.row)">合同的可编辑版</el-dropdown-item>
                      <el-dropdown-item @click="manage_img(scope.row)">合同的扫描件或照片</el-dropdown-item>
                      <el-dropdown-item @click="manage_recovery(scope.row)">恢复履约/重启合作</el-dropdown-item>
                      <el-dropdown-item @click="manage_changeHis(scope.row)">本版本合同的修改记录</el-dropdown-item>
                      <el-dropdown-item @click="manage_renewHis(scope.row)">本合同的续约记录</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </section>
        <section v-show="page === 'seeContractByAll'">
          <div class="nav" style="display: flex;justify-content: space-between ;margin-bottom: 12px">
            <div>
              <el-button type="default" @click="prevStep">返回</el-button>
              <el-button type="primary" @click="editContract(1)">新增合同</el-button>
            </div>
            <div class="rightBtn">
              <el-button type="primary" @click="seeExpireContractByAll()">已到期的合同</el-button>
              <el-button type="primary" @click="seeStopContractByAll()">已暂停/终止的合同</el-button>
            </div>
          </div>
          <div class="row" style="margin-top: 16px">
            <el-text>
              处于有效期内的销售合同共{{seeContractByCus.list1.length}}个，具体如下
            </el-text>
          </div>
          <el-table :data="seeAllContractList.list1" border style="width: 100%; margin-top: 16px">
            <el-table-column label="合同编号">
              <template #default="scope">
                {{scope.row.sn}}
              </template>
            </el-table-column>
            <el-table-column label="所属客户">
              <template #default="scope">
                {{scope.row.customerName}}
              </template>
            </el-table-column>
            <el-table-column label="所涉商品" width="150">
              <template #default="scope">
                {{scope.row.commodityCount}}种
              </template>
            </el-table-column>
            <el-table-column label="有效期" width="250">
              <template #default="scope">
                {{$filter.format(scope.row.validStart, 'day')}} 至 {{$filter.format(scope.row.validEnd, 'day')}}
              </template>
            </el-table-column>
            <el-table-column label="签署日期" width="150">
              <template #default="scope">
                {{$filter.format(scope.row.signTime, 'day')}}
              </template>
            </el-table-column>
            <el-table-column label="备注" width="150">
              <template #default="scope">
                {{scope.row.memo}}
              </template>
            </el-table-column>
            <el-table-column label="本版本合同的创建" width="250">
              <template #default="scope">
                {{scope.row.createName}} {{$filter.format(scope.row.createDate)}}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-dropdown trigger="click">
                  <span class="el-dropdown-link">
                    <el-button type="primary" link><b>管理</b></el-button>
                  </span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="manage_ZSGood(scope.row)">本合同下的专属商品</el-dropdown-item>
                      <el-dropdown-item @click="manage_TYGood(scope.row)">本合同下的通用型商品</el-dropdown-item>
                      <el-dropdown-item @click="manage_file(scope.row)">合同的可编辑版</el-dropdown-item>
                      <el-dropdown-item @click="manage_img(scope.row)">合同的扫描件或照片</el-dropdown-item>
                      <el-dropdown-item @click="manage_change(scope.row)">修改合同信息</el-dropdown-item>
                      <el-dropdown-item @click="manage_renew(scope.row, 1)">续约</el-dropdown-item>
                      <el-dropdown-item @click="manage_stop(scope.row)">暂停履约/合同终止</el-dropdown-item>
                      <el-dropdown-item @click="manage_changeHis(scope.row)">本版本合同的修改记录</el-dropdown-item>
                      <el-dropdown-item @click="manage_renewHis(scope.row)">本合同的续约记录</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </section>
        <section v-show="page === 'seeExpireContractByAll'">
          <div class="nav" style="display: flex;justify-content: space-between ;margin-bottom: 12px">
            <div>
              <el-button type="default" @click="this.page = 'seeContractByAll'">返回</el-button>
            </div>
          </div>
          <div class="row" style="margin-top: 16px">
            <el-text>
              已到期的销售合同共{{seeAllContractList.list3.length}}个，具体如下
            </el-text>
          </div>
          <el-table :data="seeAllContractList.list3" border style="width: 100%; margin-top: 16px">
            <el-table-column label="合同编号">
              <template #default="scope">
                {{scope.row.sn}}
              </template>
            </el-table-column>
            <el-table-column label="所属客户">
              <template #default="scope">
                {{scope.row.customerName}}
              </template>
            </el-table-column>
            <el-table-column label="所涉商品" width="150">
              <template #default="scope">
                {{scope.row.commodityCount}}种
              </template>
            </el-table-column>
            <el-table-column label="有效期" width="250">
              <template #default="scope">
                {{$filter.format(scope.row.validStart, 'day')}} 至 {{$filter.format(scope.row.validEnd, 'day')}}
              </template>
            </el-table-column>
            <el-table-column label="签署日期" width="150">
              <template #default="scope">
                {{$filter.format(scope.row.signTime, 'day')}}
              </template>
            </el-table-column>
            <el-table-column label="备注" width="150">
              <template #default="scope">
                {{scope.row.memo}}
              </template>
            </el-table-column>
            <el-table-column label="本版本合同的创建" width="250">
              <template #default="scope">
                {{scope.row.createName}} {{$filter.format(scope.row.createDate)}}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-dropdown trigger="click">
                  <span class="el-dropdown-link">
                    <el-button type="primary" link><b>管理</b></el-button>
                  </span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="manage_ZSGood(scope.row)">本合同下的专属商品</el-dropdown-item>
                      <el-dropdown-item @click="manage_TYGood(scope.row)">本合同下的通用型商品</el-dropdown-item>
                      <el-dropdown-item @click="manage_file(scope.row)">合同的可编辑版</el-dropdown-item>
                      <el-dropdown-item @click="manage_img(scope.row)">合同的扫描件或照片</el-dropdown-item>
                      <el-dropdown-item @click="manage_renew(scope.row, 3)">续约</el-dropdown-item>
                      <el-dropdown-item @click="manage_changeHis(scope.row)">本版本合同的修改记录</el-dropdown-item>
                      <el-dropdown-item @click="manage_renewHis(scope.row)">本合同的续约记录</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </section>
        <section v-show="page === 'seeStopContractByAll'">
          <div class="nav" style="display: flex;justify-content: space-between ;margin-bottom: 12px">
            <div>
              <el-button type="default" @click="this.page = 'seeContractByAll'">返回</el-button>
            </div>
          </div>
          <div class="row" style="margin-top: 16px">
            <el-text>
              已暂停履约/终止的销售合同共{{seeAllContractList.list2.length}}个，具体如下
            </el-text>
          </div>
          <el-table :data="seeAllContractList.list2" border style="width: 100%; margin-top: 16px">
            <el-table-column label="合同编号">
              <template #default="scope">
                {{scope.row.sn}}
              </template>
            </el-table-column>
            <el-table-column label="所属客户">
              <template #default="scope">
                {{scope.row.customerName}}
              </template>
            </el-table-column>
            <el-table-column label="所涉商品" width="150">
              <template #default="scope">
                {{scope.row.commodityCount}}种
              </template>
            </el-table-column>
            <el-table-column label="有效期" width="250">
              <template #default="scope">
                {{$filter.format(scope.row.validStart, 'day')}} 至 {{$filter.format(scope.row.validEnd, 'day')}}
              </template>
            </el-table-column>
            <el-table-column label="签署日期" width="150">
              <template #default="scope">
                {{$filter.format(scope.row.signTime, 'day')}}
              </template>
            </el-table-column>
            <el-table-column label="备注" width="150">
              <template #default="scope">
                {{scope.row.memo}}
              </template>
            </el-table-column>
            <el-table-column label="本版本合同的创建" width="250">
              <template #default="scope">
                {{scope.row.createName}} {{$filter.format(scope.row.createDate)}}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template #default="scope">
                <el-dropdown trigger="click">
                  <span class="el-dropdown-link">
                    <el-button type="primary" link><b>管理</b></el-button>
                  </span>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="manage_ZSGood(scope.row)">本合同下的专属商品</el-dropdown-item>
                      <el-dropdown-item @click="manage_TYGood(scope.row)">本合同下的通用型商品</el-dropdown-item>
                      <el-dropdown-item @click="manage_file(scope.row)">合同的可编辑版</el-dropdown-item>
                      <el-dropdown-item @click="manage_img(scope.row)">合同的扫描件或照片</el-dropdown-item>
                      <el-dropdown-item @click="manage_recovery(scope.row)">恢复履约/重启合作</el-dropdown-item>
                      <el-dropdown-item @click="manage_changeHis(scope.row)">本版本合同的修改记录</el-dropdown-item>
                      <el-dropdown-item @click="manage_renewHis(scope.row)">本合同的续约记录</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>
        </section>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.ty-container{
  margin-top:35px; padding:10px;
}
.ty-page-header{
  display: flex;
  line-height: 24px;
  padding: 0 0 0 70px;
  color: #5d9cec;
  margin-top: 16px;
  .page-header__left{
    display: flex;
    cursor: pointer;
    margin-right: 40px;
    position: relative;
    &::after {
      content: "";
      position: absolute;
      width: 1px;
      height: 16px;
      right: -20px;
      top: 50%;
      transform: translateY(-50%);
      background-color: #dcdfe6;
    }
    .icon-back {
      font-size: 18px;
      margin-right: 6px;
      align-self: center;
      position: relative;
      top: 1px;
    }
  }
}
.page{
  padding: 8px 0;
  position: relative;
  margin: 0 70px;
  .panel-box{
    border-top: 1px solid #ddd;
    padding-top: 8px;
    &:first-child{
      border: none
    }
  }
}
.ty-hr{
  margin: 16px 0;
  width: 100%;
  height: 1px;
  background: #eee;
}
.row{
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .rightBtn{
    flex: auto;
    text-align: right;
  }
  &.head{
    margin-bottom: 16px;
  }
}
.text-center{
  text-align: center;
}
:deep(.el-upload--picture-card){
  width: 64px;
  height: 64px;
  &>i{
    font-size: 16px;
  }
}
:deep(.el-upload-list--picture-card .el-upload-list__item){
  width: 64px;
  height: 64px;
}
:root{
  --el-upload-list-picture-card-size: 48px
}
.fileBox{
  border-radius: 3px;
  width: 100%;
  border: 1px solid rgb(228, 231, 237);
  min-height: 14px;
  padding: 8px;
}
:deep(.el-upload){
  display: none;
}
</style>
<script>
import {beforeRouteLeave, initNav} from "@/utils/routeChange"
import auth from '@/sys/auth'
import { getRootPathFile } from '@/api/api'
import { previewFile } from '@/utils/downloadFile'
import * as api from "@/api/sale.js"
import ElInputPlus from "@/components/shelfWh/ElInputPlus.vue"
export default {
  data() {
    return {
      page: 'main',
      rootPath: { fileUrl :'',  ow365url :'',  uploadUrl :'',  webRoot :'' },
      imgUpload: {
        uploadHeaders:{ 'Token': auth.getToken() },
        uploadAction: auth.webRoot + '/uploads/uploadfyByFile.do',
        uploadLyc: '.png,.jpeg,.jpg,.gif',
        postData: {
          module: '客户管理', userId: auth.getUserID()
        },
      },
      fileUpload: {
        uploadHeaders:{ 'Token': auth.getToken() },
        uploadAction: auth.webRoot + '/uploads/uploadfyByFile.do',
        uploadLyc: '.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt',
        postData: {
          module: '客户管理', userId: auth.getUserID()
        },
      },
      pageName: 'sale_contract',
      pageShow: false,
      pageInfo:{ 'currentPageNo': 0, 'pageSize': 20 , 'totalPage': 0  },
      customerContractList: [],
      seeAllContractList: {
        type: 1,
        list1: [],
        list2: [],
        list3: [],
      },
      seeContractByCus: {
        type: 1,
        info: {},
        list1: [],
        list2: [],
        list3: [],
      },
      seeContractChangeHis: {
        info: {},
        list: []
      },
      seeContractRenewHis: {
        info: {},
        list: []
      },
      seeContractDetail: {
        info: {
          fileList: []
        }
      },
      form_editContract: { // 合同
        editType: 1, // 1：新增 2：修改 3：续约
        cusInfo: {},
        customer: '',
        sn: '',
        signTime: '',
        validTime: [], // 自定义的，接收开始时间和结束时间的组合字段
        validTimeMin: '',
        validStart: '',
        validEnd: '',
        imgList: [],
        removeImgList: [],
        fileList: [],
        selectTY: [],
        selectZS: [],
        memo: '',
        canChooseProductZS: [],
        canChooseProductTY: [],
        type: 1 // 1普通续约 2停用续约 3过期续约 (只有续约传）
      },
      form_manageContract: {
        info: {
          contractBase: {},
          listImage: [],
          listZS: [],
          listTY: [],
          listHis: []
        }
      },
      form_changeGood: {
        title: '',
        list: [],
        select: []
      },
      form_seeGood: {
        title: '',
        list: [],
        select: []
      },
      form_seeContractByGood: {
        cusInfo: {},
        goodInfo: {},
        goodStr: '',
        list: []
      },
      list_seeContractImg: {
        list: [],
        preview: []
      },
      dialog_visible_editContract: false,
      dialog_visible_manage_changeGood: false,
      dialog_visible_manage_seeGood: false,
      dialog_visible_manage_file: false,
      dialog_visible_manage_img: false,
      dialog_visible_manage_change: false,
      dialog_visible_manage_renew: false,
      dialog_visible_manage_stop: false,
      dialog_visible_manage_changeHis: false,
      dialog_visible_manage_renewHis: false,
      dialog_visible_imgShow: false,
      dialog_visible_seeContract: false,
      dialog_visible_recoveryExpire: false,
      dialog_visible_seeContractByGood: false,
      imgShow: '',
      editContractRules: {
        customer: [
          { required: true, message: '请选择客户', trigger: 'change' }
        ],
        sn: [
          { required: true, message: '请输入合同编号', trigger: 'blur' }
        ],
        validTime: [
          { type: 'array', required: true, message: '请选择合同的有效期', trigger: 'change' }
        ]
      },
      recoveryExpire: {
        radio: '',
        validEnd: ''
      },
      option_customers: []
    }
  },
  components: {
    ElInputPlus
  },
  beforeRouteLeave(to, from, next) {
    beforeRouteLeave(to, from, next, this)
  },
  mounted() {
    initNav({mid: 'qx', name: '销售合同', pageName: this.pageName}, this.created, this)
  },
  methods: {
    created () {
      console.log('rootPath', this.rootPath)
      getRootPathFile()
          .then(res => {
            console.log('RootPath=', res.data)
            this.rootPath = res.data
          })
      this.initPage()
    },
    getCustomerContractList(option = {}) {
      api.getSalesContractList()
          .then(res => {
            let data = res.data.data.list
            this.customerContractList = data
          })
    },
    getContractDetail(id) {
      // 获取合同详情
      api.getContractBase(id)
          .then(res => {
            let data = res.data.data
            console.log(data)
            this.form_manageContract.info = {
              contractBase: data.contractBase, // 合同信息（大多数据都从这里取出，尤其是合同id，要用这个关键字下的）
              listImage: data.listImage || [] ,// 合同的扫描件或照片
              listZS: data.listZS || [], // 专属商品
              listTY: data.listTY || [], // 通用型商品
              listHis: data.listHis || [] // 暂停恢复记录
            }
          })
    },
    seeCustomer(item) {
      this.seeContractByCus.info = item
      this.page = 'seeContractByCus'
    },
    seeExpireContract() {
      this.page = 'seeExpireContractByCus'
    },
    seeStopContract() {
      this.page = 'seeStopContractByCus'
    },
    getContractList() {
      let typeArr = ['seeContractByCus', 'seeStopContractByCus', 'seeExpireContractByCus']
      let type = typeArr.findIndex(item => item === this.page) + 1
      let id = this.seeContractByCus.info.id
      api.getContractByCustomer(type, id)
          .then(res => {
            console.log(res)
            let data = res.data.data.contractBaseList
            this.seeContractByCus['list' + type] = data
          })
    },
    seeAllContract() {
      this.page = 'seeContractByAll'
    },
    seeExpireContractByAll() {
      this.page = 'seeExpireContractByAll'
    },
    seeStopContractByAll() {
      this.page = 'seeStopContractByAll'
    },
    getAllContractList() {
      let typeArr = ['seeContractByAll', 'seeStopContractByAll', 'seeExpireContractByAll']
      let type = typeArr.findIndex(item => item === this.page) + 1
      api.getAllContractList(type)
          .then(res => {
            console.log(res)
            let data = res.data.data.contractBaseList
            this.seeAllContractList['list' + type] = data
          })
    },
    getAllContractByGood(item) {
      this.dialog_visible_seeContractByGood = true
      api.getAllContractByGood(item.id)
      .then(res => {
        let data = res.data.data
        let list = data.list
        let goodArr = [item.outerSn, item.outerName, item.model, item.specifications, item.unit]
        this.form_seeContractByGood.goodInfo = item
        this.form_seeContractByGood.goodStr = [...goodArr].filter(item => item || item === 0).join(" / ")
        this.form_seeContractByGood.list = list
      })
    },
    editContract() {
      if (this.page === 'seeContractByAll' || this.page === 'main') {
        this.form_editContract.cusInfo = {}
        api.getCustomerByAddContract()
        .then(res => {
          let list = res.data.data.list || []
          this.option_customers = list.map(item => {
            return {
              label: item.fullName,
              value: item.id
            }
          })
        })
      } else {
        this.form_editContract.cusInfo = this.seeContractByCus.info
      }
      this.initContract(1)
    },
    initContract(editType, contractInfo) {
      this.dialog_visible_editContract = true
      this.$nextTick(() => {
        this.$refs['editContractForm'].resetFields()
        console.log('editContractForm', this.form_editContract)
        this.form_editContract.customer = ''
        this.form_editContract.editType = editType
        this.form_editContract.validTimeMin = ''
        this.form_editContract.removeImgList = []
        let cusId = this.form_editContract.cusInfo.id
        if (cusId) {
          this.initGoods(cusId)
        }

        if (editType > 1) {
          // 赋默认值
          api.getContractBase(contractInfo.id)
              .then(res => {
                let data = res.data.data
                let contractBase = data.contractBase
                console.log(data)
                const newEditContract = this.form_editContract
                let imgs = data.listImage.map(item => {
                  return {
                    url: auth.webRoot + '/upload/' + item.uplaodPath,
                    normalPath: item.uplaodPath,
                    name: item.title,
                    operation: 4,
                    id: item.id
                  }
                })
                this.form_editContract = {
                  ...newEditContract,
                  id: contractBase.id,
                  editType: editType, // 1：新增 2：修改 3：续约
                  sn: contractBase.sn,
                  signTime: this.$filter.format(contractBase.signTime, 'day'),
                  validTime: [this.$filter.format(contractBase.validStart, 'day'), this.$filter.format(contractBase.validEnd, 'day')], // 自定义的，接收开始时间和结束时间的组合字段
                  validTimeMin: '',
                  imgList: imgs,
                  fileList: [],
                  selectTY: data.listTY || [],
                  selectZS: data.listZS || [],
                  memo: contractBase.memo
                }
                if (editType === 3) {
                  this.form_editContract.validTime = []
                  this.form_editContract.validTimeMin = this.$filter.format(contractBase.validEnd, 'day')
                }
                console.log('validTimeMin', this.form_editContract.validTimeMin)
                if (contractBase.filePath) {
                  this.form_editContract.fileList.push({
                    url: auth.webRoot + '/upload/' + contractBase.filePath,
                    normalPath: contractBase.filePath,
                    name: contractBase.fileName
                  })
                }
                console.log('this.form_editContract', this.form_editContract)
              })
        }
      })

    },
    change_customer(value) {
      let cusInfo = this.option_customers.find(item => item.value === value)
      this.form_editContract.cusInfo = {
        fullName: cusInfo.label,
        id: cusInfo.value
      }
      this.initGoods(value)
      this.form_editContract.selectTY = []
      this.form_editContract.selectZS = []
    },
    initGoods(cusId) {
      api.getAllGoods(cusId)
          .then(res => {
            let data = res.data.data
            console.log(data)
            let productListZS = data.productListZS || []
            let productListTY = data.productListTY || []
            this.form_editContract.canChooseProductTY = productListTY
            this.form_editContract.canChooseProductZS = productListZS

          })
    },
    manage_ZSGood(item) {
      console.log(item)
      // 本合同下的专属商品
      this.form_seeGood.title = '本合同下的专属商品'
      if (item.listZS) {
        this.form_seeGood.list = item.listZS || []
        this.dialog_visible_manage_seeGood = true
      } else {
        if (item.id) {
          api.getContractBase(item.id)
              .then(res => {
                let data = res.data.data
                console.log(data)
                this.form_seeGood.list = data.listZS || []
                this.dialog_visible_manage_seeGood = true
              })

        } else {
          this.form_seeGood.list = item.selectZS || []
          this.dialog_visible_manage_seeGood = true
        }
      }
    },
    manage_TYGood(item) {
      // 本合同下的通用型商品
      this.form_seeGood.title = '本合同下的通用型商品'
      if (item.listTY) {
        this.form_seeGood.list = item.listTY || []
        this.dialog_visible_manage_seeGood = true
      } else {
        if (item.id) {
          api.getContractBase(item.id)
              .then(res => {
                let data = res.data.data
                console.log(data)
                this.form_seeGood.list = data.listTY || []
                this.dialog_visible_manage_seeGood = true
              })

        } else {
          this.form_seeGood.list = item.selectTY || []
          this.dialog_visible_manage_seeGood = true
        }
      }
    },
    manage_file(item) {
      // 合同的可编辑版
      api.getContractBase(item.id)
          .then(res => {
            let data = res.data.data
            console.log(data)
            let path = data.contractBase.filePath
            if (path) {
              this.preview_File(path)
            } else {
              this.$message({
                type: 'error',
                message: '合同的可编辑版未上传！'
              })
            }
          })

    },
    manage_img(item) {
      // 合同的扫描件或照片
      api.getContractBase(item.id)
          .then(res => {
            let data = res.data.data
            console.log(data)
            let listImage = data.listImage
            listImage.map(item => {
              item.url = auth.webRoot + '/upload/' + item.uplaodPath
              item.normalPath = item.uplaodPath
              item.name = item.title
            })
            this.list_seeContractImg.list = listImage
            this.list_seeContractImg.preview = listImage.map(item => item.url)
            this.dialog_visible_manage_img = true
          })
    },
    manage_change(item) {
      // 修改合同信息
      this.dialog_visible_editContract = true
      if (item.customer) {
        this.form_editContract.cusInfo = {id: item.customer, fullName: item.customerName}
      }
      this.initContract(2, item)
    },
    manage_renew(item, type) {
      // 续约
      this.dialog_visible_manage_renew = true
      if (item.customer) {
        this.form_editContract.cusInfo = {id: item.customer, fullName: item.customerName}
      }
      this.form_editContract.type = type
      this.initContract(3, item)
    },
    manage_stop(item) {
      console.log(this.seeContractByCus.info.id)
      // 暂停履约/合同终止
      this.$messageBox.confirm(
          `点击“确定”后，本合同将进入“已暂停/终止的合同”。<br>确定进行本操作吗？`,
          '！！提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            dangerouslyUseHTMLString: true
          }
      )
          .then(() => {
            api.stopContract(item.id)
                .then(res => {
                  let state = res.data.data.state
                  if (state === 1) {
                    this.$message({
                      type: 'success',
                      message: '操作成功！'
                    })
                    this.initPage()
                  } else if (state === 0) {
                    this.$message({
                      type: 'error',
                      message: '请勿重复操作！'
                    })
                  } else {
                    this.$message({
                      type: 'error',
                      message: '操作失败！'
                    })
                  }
                })
          })
    },
    manage_recovery(item) {
      if (item.customer) {
        this.form_editContract.cusInfo = {id: item.customer, fullName: item.customerName}
      }
      // 恢复履约/重启合作
      api.judgeRecoveryContract(item.id)
      .then(res => {
        let data = res.data.data
        let state = data.state // state 0-不要重复启用 1-没有过期直接恢复 2-已经过期了(跳转到确认弹窗 续约或者回到已过期）
        let vaildEnd = data.vaildEnd // 到期日期
        if (state === 0) {
          this.$message({
            type: 'warning',
            message: '请勿重复操作！'
          })
        } else if (state === 2) {
          this.dialog_visible_recoveryExpire = true
          this.recoveryExpire = {
            contractInfo: item,
            radio: '',
            validEnd: this.$filter.format(vaildEnd, 'day')
          }
        } else if (state === 1) {
          this.$messageBox.confirm(
              `点击“确定”后，本合同将回到有效合同列表。<br>确定进行本操作吗？`,
              '！！提示',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                dangerouslyUseHTMLString: true
              }
          )
              .then(() => {
                this.contractRecovery_submit(item.id, 1)
              })
        } else {
          this.$message({
            type: 'error',
            message: '操作失败！'
          })
        }
      })
    },
    contractRecovery_submit(id, type) {
      api.recoveryContract(id, type)
          .then(res => {
            let state = res.data.data.state
            if (state === 1) {
              this.$message({
                type: 'success',
                message: '操作成功！'
              })
              this.initPage()
            } else if (state === 0) {
              this.$message({
                type: 'error',
                message: '请勿重复操作！'
              })
            } else {
              this.$message({
                type: 'error',
                message: '操作失败！'
              })
            }
          })
    },
    manage_recoveryExpire_submit() {
      let radio = this.recoveryExpire.radio
      if (radio) {
        if (radio === 1) {
          this.dialog_visible_recoveryExpire = false
          this.contractRecovery_submit(this.recoveryExpire.contractInfo.id, 2)
        } else if (radio === 2) {
          this.dialog_visible_recoveryExpire = false
          this.form_editContract.type = 2
          this.initContract(3, this.recoveryExpire.contractInfo) // 客户数据在恢复合同按钮处已赋值
        }
      } else {
        this.$message({
          type: 'error',
          message: '请勾选！'
        })
      }
    },
    manage_changeHis(item) {
      // 本版本合同的修改记录
      this.dialog_visible_seeContract = false
      this.dialog_visible_manage_changeHis = true
      api.getContractChangeHis(item.id)
        .then(res => {
          let list = res.data.data.list
          this.seeContractChangeHis.list = list
        })
    },
    manage_renewHis(item) {
      // 本合同的续约记录
      this.dialog_visible_manage_renewHis = true
      api.getContractRenewHis(item.primaryCont)
          .then(res => {
            let list = res.data.data.list
            this.seeContractRenewHis.list = list
          })
    },
    manage_seeContractDetail(item, origin) {
      let isChange = this.dialog_visible_manage_changeHis // 修改记录还是续约记录
      let promise = isChange?api.getContractChangeHisDetail(item.id):api.getContractBase(item.id)
      promise.then(res => {
        let data = res.data.data
        let contractBase = isChange?data.contracthistory:data.contractBase
        let listImage = isChange?data.listHisImage:data.listImage
        let listTY = isChange?data.listHisTY:data.listTY
        let listZS = isChange?data.listHisZS:data.listZS
        console.log(data)
        listImage.map(item => {
          item.url = auth.webRoot + '/upload/' + item.uplaodPath
          item.normalPath = item.uplaodPath
          item.name = item.title
        })
        let previewList = listImage.map(item => {
          return auth.webRoot + '/upload/' + item.uplaodPath
        })
        this.seeContractDetail.info = {
          origin: origin,
          id: contractBase.id,
          filePath: contractBase.filePath,
          sn: contractBase.sn,
          signTime: this.$filter.format(contractBase.signTime, 'day'),
          validStart: this.$filter.format(contractBase.validStart, 'day'),
          validEnd: this.$filter.format(contractBase.validEnd, 'day'),
          imgList: listImage,
          previewList: previewList,
          fileList: [],
          listTY: listTY || [],
          listZS: listZS || [],
          memo: contractBase.memo
        }
        if (contractBase.filePath) {
          this.seeContractDetail.info.fileList.push({
            url: auth.webRoot + '/upload/' + contractBase.filePath,
            normalPath: contractBase.filePath,
            name: contractBase.fileName
          })
        }
        console.log('manage_seeContractDetail', this.seeContractDetail.info)
        this.dialog_visible_seeContract = true
      })

    },
    editContract_imgSuccess(response, file, fileList, index) {
      // 这里的response是服务器返回的数据
      // file是上传成功的文件对象
      // fileList是上传的文件列表
      console.log('response', response)
      console.log('file', file)
      console.log('fileList', fileList)
      let fileItem = {
        url: auth.webRoot + '/upload/' + response.filename,
        normalPath:  response.filename,
        name: response.originalFilename,
        operation: 1
      }
      this.form_editContract.imgList = this.form_editContract.imgList.map(f => (f.uid === file.uid ? fileItem : f));
      console.log('文件上传成功', this.form_editContract.imgList);
      // 在这里执行你需要的操作
    },
    editContract_fileSuccess(response, file, fileList) {
      // 这里的response是服务器返回的数据
      // file是上传成功的文件对象
      // fileList是上传的文件列表
      let fileItem = {
        url: auth.webRoot + '/upload/' + response.filename,
        normalPath:  response.filename,
        name: response.originalFilename
      }
      fileList[fileList.length - 1] = fileItem
      console.log('文件上传成功', fileList);
      // 在这里执行你需要的操作
    },
    editContract_imgPreview(file) {
      this.dialog_visible_imgShow = true
      this.imgShow = file.url
    },
    editContract_imgHandleExceed() {
      this.$message({
        type: 'error',
        message: '最多只能上传9张！'
      })
    },
    editContract_fileHandleExceed(files, fileList) {
      // this.$set(fileList[0], 'raw', files[0])
      // this.$set(fileList[0], 'name', files[0].name)
      this.$refs.upload.clearFiles()
      this.$refs.upload.handleStart(files[0])
      this.$refs.upload.submit()
    },
    editContract_imgRemove(file) {
      console.log('file', file)
      if (file.id) {
        file.operation = 2
        this.form_editContract.removeImgList.push(file)
      }
    },
    editContract_addGood(type) {
      this.dialog_visible_manage_changeGood = true
      const productTY = this.form_editContract.canChooseProductTY
      const productZS = this.form_editContract.canChooseProductZS
      this.form_changeGood.select = this.form_editContract['select' + type]
      if (type === 'TY') {
        this.form_changeGood.title = '向本合同添加通用型商品'
        this.form_changeGood.list = productTY
        this.form_changeGood.tips = `系统内共有以下${productTY.length}种通用型商品，均可选择`
      } else {
        this.form_changeGood.title = '向本合同添加专属商品'
        this.form_changeGood.list = productZS
        this.form_changeGood.tips = `${this.form_editContract.cusInfo.fullName}的专属商品共有以下${productZS.length}种`
      }

      this.form_changeGood.goodType = type
      this.form_changeGood.handleType = 'add'
      // this.$refs.table_changeGood.clearSelection()
      console.log(this.form_changeGood.select)
      const select = this.form_changeGood.select
      let that = this
      this.$nextTick(() => {
        select.forEach(item => {
          this.form_changeGood.list.forEach(i => {
            if (item.id === i.id) {
              that.$refs['table_changeGood'].toggleRowSelection(i, true)
            }
          })
        })
      })
    },
    editContract_removeGood(type) {
      this.dialog_visible_manage_changeGood = true
      this.form_changeGood.list = this.form_editContract['select' + type]
      this.form_changeGood.goodType = type
      this.form_changeGood.handleType = 'remove'
      if (type === 'TY') {
        this.form_changeGood.title = '从本合同移出通用型商品'
        this.form_changeGood.tips = `可供选择的通用型商品共有以下${this.form_changeGood.list.length}种`

      } else {
        this.form_changeGood.title = '从本合同移出专属商品'
        this.form_changeGood.tips = `可供选择的专属商品共有以下${this.form_changeGood.list.length}种`
      }
    },
    editContract_changeGood_handleCheck(val) {
      console.log(val)
      this.form_changeGood.select = val
    },
    editContract_changeGood_submit() {
      const goodType = this.form_changeGood.goodType
      const handleType = this.form_changeGood.handleType
      const select = this.form_changeGood.select
      if (handleType === 'add') {
        this.form_editContract['select' + goodType] = select
      }
      if (handleType === 'remove') {
        const nowSelect = this.form_editContract['select' + goodType]
        this.form_editContract['select' + goodType] = nowSelect.filter(item1 =>
            !select.some(item2 => item2.id === item1.id)
        )
      }
      this.dialog_visible_manage_changeGood = false
    },
    editContract_submit() {
      console.log(this.form_editContract)
      this.$refs['editContractForm'].validate((valid) => {
        if (valid) {
          const editType = this.form_editContract.editType

          const contractInfo = this.form_editContract
          let imgs = contractInfo.imgList.map(item => {
            let newItem = {
              uplaodPath: item.normalPath,
              type: 1,
              title: item.name,
              operation: item.operation
            }
            if (item.id) newItem.id = item.id
            if (editType === 3) newItem.operation = 1
            return newItem
          })
          if (editType === 2) {
            // 修改时需要传删除得图片 operation 1 用于表示新增 4 表示没动 2表示删除
            let removeImgs = contractInfo.removeImgList.map(item => {
              return {
                uplaodPath: item.normalPath,
                type: 1,
                title: item.name,
                operation: 2,
                id: item.id
              }
            })
            imgs = [...imgs, ...removeImgs]
          }
          let data = {
            customer: contractInfo.cusInfo.id,
            sn: contractInfo.sn,
            contractSignTime: contractInfo.signTime,
            memo: contractInfo.memo,
            contractBaseImages: JSON.stringify(imgs),
            contractStartTime: contractInfo.validTime?.[0],
            contractEndTime: contractInfo.validTime?.[1],
            productZSList: JSON.stringify(contractInfo.selectZS.map(item => {return {commodity: item.id}})),
            productTYList: JSON.stringify(contractInfo.selectTY.map(item => {return {commodity: item.id}}))
          }
          if ((this.page === 'seeContractByAll' || this.page === 'main') && this.form_editContract.editType === 1) {
            // 全部合同新增合同客户是选择的
            data.customer = contractInfo.customer
          }
          if (contractInfo.editType === 3) {
            data.type = contractInfo.type //1普通续约 2停用续约 3过期续约 (只有续约传）
          }
          if (contractInfo.fileList.length > 0) {
            data.filePath = contractInfo.fileList[0].normalPath
            data.fileName = contractInfo.fileList[0].name
          }
          if (editType > 1) {
            data.id = contractInfo.id
          }
          let promise = editType === 1?api.addContract(data):(editType === 2?api.changeContract(data): api.renewContract(data))

          promise.then(res => {
                let state = res.data.data.state
                if (state === 1) {
                  this.dialog_visible_editContract = false
                  this.$message({
                    type: 'success',
                    message: '操作成功！'
                  })
                  this.initPage()
                } else if (state === 2) {
                  this.$message({
                    type: 'error',
                    message: '已续约不可修改！'
                  })
                } else if (state === 3) {
                  this.$message({
                    type: 'error',
                    message: '修改的日期不能在上一个合同结束日之前！'
                  })
                } else {
                  this.$message({
                    type: 'error',
                    message: '操作失败！'
                  })
                }
              })
        } else {
          this.$message({
            type: 'error',
            message: '验证失败，请检查'
          })
        }
      })
    },
    preview_File(path) {
      console.log(this.seeContractDetail.info)
      previewFile(this.rootPath, path??this.seeContractDetail.info.fileList[0].normalPath)
    },

    prevStep () {
      this.page = 'main'
    },
    msg(res) {
      let code = res.data.code
      let msg = res.data.msg
      let type = ''
      switch (code) {
        case 0:
          type = 'success'
          break
        case 301:
          type = 'warning'
          break
        case 500:
          type = 'error'
          break
      }
      this.$message({
        type: type,
        message: msg,
        plain: true
      })
    },
    realPath (path) {
      return auth.webRoot + '/upload/' + path
    },
    filter_good (type, data) {
      let nameArr = []
      if (data) {
        nameArr = data.map(item => item.outerName)
      } else {
        nameArr = this.form_editContract['select'+type].map(item => item.outerName)
      }
      return nameArr.join('、')
    },
    disabledDate(time) {
      return time.getTime() < new Date(this.form_editContract.validTimeMin).getTime()
    },
    initPage(page) {
      let thisPage = page || this.page
      switch (thisPage) {
        case 'main':
          this.getCustomerContractList(1, 20)
          break
        case 'seeContractByCus':
        case 'seeExpireContractByCus':
        case 'seeStopContractByCus':
          this.getContractList()
          break
        case 'seeContractByAll':
        case 'seeExpireContractByAll':
        case 'seeStopContractByAll':
          this.getAllContractList()
          break
      }
    }
  },
  computed: {
    editContractTitle() {
      let arr = ['新增合同', '修改合同', '续约合同']
      return arr[this.form_editContract.editType - 1]
    },
  },
  watch: {
    // 每当 question 改变时，这个函数就会执行
    page(newPage) {
      this.initPage(newPage)
    }
  },
  directives: {
    // 在模板中启用 v-focus
    onlyInteger: (el) => {
      // 这会在 `mounted` 和 `updated` 时都调用
      let ele = el.querySelector('input')
      ele.addEventListener('input', () => {
        // 获取输入框的当前值
        let value = ele.value;
        // 使用正则表达式匹配非正整数的部分，并将其替换为空字符串
        value = value.replace(/[^\d]/g, '');
        // 如果值为0，则直接设置为空字符串，因为0不是正整数
        if (value === '0') {
          value = '';
        }
        // 更新输入框的值
        ele.value = value;
      });
    }
  }
}


</script>