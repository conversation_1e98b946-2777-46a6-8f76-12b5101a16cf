package cn.sphd.miners.modules.forumArea.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by 朱思旭 on 2018/10/9.
 */

@Entity(name="ForumReplyUserStat")
@Table(name = "t_forum_reply_user_stat")
public class ForumReplyUserStat implements Serializable{
    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    @Column(name = "reply")
    private Integer reply;
    @Column(name = "post")
    private Integer post;
    @Column(name = "userID")
    private Integer userID;
    @Column(name = "total_num")
    private Integer totalNum;
    @Column(name = "read_mum")
    private Integer readMum;
    @Column(name = "at_mum")
    private Integer atMum;
    @Column(name = "memo")
    private String memo;
    @Column(name = "creator")
    private Integer creator;
    @Column(name = "create_name")
    private String createName;
    @Column(name = "create_date")
    private Date create_date;
    @Column(name = "updator")
    private Integer updator;
    @Column(name = "update_name")
    private String updateName;
    @Column(name = "update_date")
    private Date updatedate;
    @Column(name = "org")
    private Integer org;

    /*@ManyToOne(fetch= FetchType.LAZY )
    @JoinColumn(name="reply", referencedColumnName = "id" , nullable=false , unique=false , insertable=false, updatable=false)
    private ForumReply forumReply;*/


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getReply() {
        return reply;
    }

    public void setReply(Integer reply) {
        this.reply = reply;
    }

    public Integer getPost() {
        return post;
    }

    public void setPost(Integer post) {
        this.post = post;
    }

    public Integer getUserID() {
        return userID;
    }

    public void setUserID(Integer userID) {
        this.userID = userID;
    }

    public Integer getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Integer totalNum) {
        this.totalNum = totalNum;
    }

    public Integer getReadMum() {
        return readMum;
    }

    public void setReadMum(Integer readMum) {
        this.readMum = readMum;
    }

    public Integer getAtMum() {
        return atMum;
    }

    public void setAtMum(Integer atMum) {
        this.atMum = atMum;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreate_date() {
        return create_date;
    }

    public void setCreate_date(Date create_date) {
        this.create_date = create_date;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdatedate() {
        return updatedate;
    }

    public void setUpdatedate(Date updatedate) {
        this.updatedate = updatedate;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    /*public ForumReply getForumReply() {
        return forumReply;
    }

    public void setForumReply(ForumReply forumReply) {
        this.forumReply = forumReply;
    }*/
}
