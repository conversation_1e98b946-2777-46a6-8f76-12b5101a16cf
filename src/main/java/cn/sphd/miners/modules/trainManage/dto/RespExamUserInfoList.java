package cn.sphd.miners.modules.trainManage.dto;

import cn.sphd.miners.common.persistence.PageInfo;

import java.util.Date;
import java.util.List;

public class RespExamUserInfoList {
    private List<RespExamUserInfo> examUserInfoList;
    private PageInfo pageInfo;
    private Date startTime;
    private Date endTime;

    public List<RespExamUserInfo> getExamUserInfoList() {
        return examUserInfoList;
    }

    public void setExamUserInfoList(List<RespExamUserInfo> examUserInfoList) {
        this.examUserInfoList = examUserInfoList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
}
