<template>
  <div class="instruction1">
    <div>
      <el-button @click="goCaoZuo">&lt; 返回上一页</el-button>
      <el-button @click="backMain">&lt;返回主页</el-button>
    </div>
    <div>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="grayCon">
            <div class="ttl">
              OBS直播简介
            </div>
            1 电脑
            <br>
            ——建议采用近两三年购买的、能正常进行常规操作的台式机或笔记本，需装有windows或其他操作系统
            <br>
            2 OBS的下载与安装
            <br>
            ——可在“联想应用商店”或其他商店中下载OBS，可通过百度或其他搜索找到“联想应用商店”。下载示例：
            <br>
            ——在电脑的百度上输入“联想应用商店”，找到带蓝色“官方”字样的链接并打开，之后在“联想应用商店”中再搜索“OBS Studio”，之后完成下载与安装
            <br>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="grayCon grayCon2">
            3 OBS的使用
            <br>
            ——OBS的版本会更新，故建议通过学会某个版本的操作后，掌握其操作的逻辑
            <br>
            ——直播平台已有很多关于如何用OBS进行直播的作品，可通过输入“OBS”查找
            <br>
          </div>

        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
    }
  },
  components: {
  },
  mounted() {

  },
  methods: {
    goCaoZuo(){
      this.$router.push('operatInstruction')
    },
    backMain(type) {
      this.$router.push('mainPage')
    },

  }
}
</script>

<style lang="scss" scoped>
.instruction1{
  padding: 40px;
  .fright{ float: right }
  .ttl{ text-align: center; font-weight: bold; margin-bottom: 20px; }
  .grayCon{ padding: 20px; margin: 40px; background: #eee; border-radius: 4px; line-height: 30px;  }
  .grayCon2{ padding-top: 150px; padding-bottom:65px  }
}
</style>
