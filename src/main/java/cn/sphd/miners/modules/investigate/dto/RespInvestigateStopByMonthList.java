package cn.sphd.miners.modules.investigate.dto;

import java.util.List;

public class RespInvestigateStopByMonthList {
    private Integer sum;
    private String year;
    private List<RespInvestigateStopByMonth> respInvestigateStopByMonthList;

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public Integer getSum() {
        return sum;
    }

    public void setSum(Integer sum) {
        this.sum = sum;
    }

    public List<RespInvestigateStopByMonth> getRespInvestigateStopByMonthList() {
        return respInvestigateStopByMonthList;
    }

    public void setRespInvestigateStopByMonthList(List<RespInvestigateStopByMonth> respInvestigateStopByMonthList) {
        this.respInvestigateStopByMonthList = respInvestigateStopByMonthList;
    }
}
