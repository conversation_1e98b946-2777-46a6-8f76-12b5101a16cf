package cn.sphd.miners.modules.commodity.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.commodity.dao.PdResourceHistoryDao;
import cn.sphd.miners.modules.commodity.entity.PdImageHistory;
import cn.sphd.miners.modules.commodity.entity.PdResource;
import cn.sphd.miners.modules.commodity.entity.PdResourceHistory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
@Repository
public class PdResourceHistoryDaoImpl  extends BaseDao<PdResourceHistory, Serializable> implements PdResourceHistoryDao {
    @Override
    public PdResourceHistory insert(PdResource pdResource) {
        PdResourceHistory history=new PdResourceHistory();

        BeanUtils.copyProperties(pdResource,history);
        history.setPdResource(pdResource.getId());
        history.setId(null);
        save(history);
        return history;
    }
}
