package cn.sphd.miners.modules.forumArea.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2018-02-1
 * @description
 **/
@Entity(name="ForumUserStat")
@Table(name = "t_forum_user_stat")
public class ForumUserStat implements Serializable{
  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;
  @Column(name = "user")
  private Integer user;
  @Column(name = "username")
  private String userName;
  @Column(name = "post")
  private Integer post;
  @Column(name = "comment")
  private Integer comment;
  @Column(name = "reply")
  private Integer reply;
  @Column(name = "unread_post")
  private Integer unreadPost;
  @Column(name = "unread_comment")
  private Integer unreadComment;
  @Column(name = "unread_reply")
  private Integer unreadReply;
  @Column(name = "memo")
  private String memo;
  @Column(name = "creator")
  private Integer creator;
  @Column(name = "create_name")
  private String createName;
  @Column(name = "create_date")
  private Date createDate;
  @Column(name = "updator")
  private Date updator;
  @Column(name = "update_name")
  private String updateName;
  @Column(name = "update_date")
  private Date updateDate;
  @Column(name = "message_id")
  private Integer messageId;
  @Column(name = "org")
  private Integer org;

  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Integer getUser() {
    return user;
  }

  public void setUser(Integer user) {
    this.user = user;
  }

  public String getUserName() {
    return userName;
  }

  public void setUserName(String userName) {
    this.userName = userName;
  }

  public Integer getPost() {
    return post;
  }

  public void setPost(Integer post) {
    this.post = post;
  }

  public Integer getComment() {
    return comment;
  }

  public void setComment(Integer comment) {
    this.comment = comment;
  }

  public Integer getReply() {
    return reply;
  }

  public void setReply(Integer reply) {
    this.reply = reply;
  }

  public Integer getUnreadPost() {
    return unreadPost;
  }

  public void setUnreadPost(Integer unreadPost) {
    this.unreadPost = unreadPost;
  }

  public Integer getUnreadComment() {
    return unreadComment;
  }

  public void setUnreadComment(Integer unreadComment) {
    this.unreadComment = unreadComment;
  }

  public Integer getUnreadReply() {
    return unreadReply;
  }

  public void setUnreadReply(Integer unreadReply) {
    this.unreadReply = unreadReply;
  }

  public String getMemo() {
    return memo;
  }

  public void setMemo(String memo) {
    this.memo = memo;
  }

  public Integer getCreator() {
    return creator;
  }

  public void setCreator(Integer creator) {
    this.creator = creator;
  }

  public String getCreateName() {
    return createName;
  }

  public void setCreateName(String createName) {
    this.createName = createName;
  }

  public Date getCreateDate() {
    return createDate;
  }

  public void setCreateDate(Date createDate) {
    this.createDate = createDate;
  }

  public Date getUpdator() {
    return updator;
  }

  public void setUpdator(Date updator) {
    this.updator = updator;
  }

  public String getUpdateName() {
    return updateName;
  }

  public void setUpdateName(String updateName) {
    this.updateName = updateName;
  }

  public Date getUpdateDate() {
    return updateDate;
  }

  public void setUpdateDate(Date updateDate) {
    this.updateDate = updateDate;
  }

  public Integer getMessageId() {
    return messageId;
  }

  public void setMessageId(Integer messageId) {
    this.messageId = messageId;
  }

  public Integer getOrg() {
    return org;
  }

  public void setOrg(Integer org) {
    this.org = org;
  }
}
