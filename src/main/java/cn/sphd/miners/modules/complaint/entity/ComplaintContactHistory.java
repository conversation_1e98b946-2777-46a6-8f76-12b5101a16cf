package cn.sphd.miners.modules.complaint.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity(name="ComplaintContactHistory")
@Table(name = "T_complaint_contact_history")
public class ComplaintContactHistory implements Serializable{
  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;
  @Column(name = "complaint_base")
  private Integer complaintBase;
  @Column(name = "complaint_base_history")
  private Integer complaintBaseHistory;
  @Column(name = "complaint_contact")
  private Integer complaintContact;
  @Column(name = "contact_name")
  private String contactName;
  @Column(name = "contact")
  private String contact;
  @Column(name = "contact_phone")
  private String contactPhone;
  @Column(name = "post")
  private String post;
  @Column(name = "creator")
  private Integer creator;
  @Column(name = "create_name")
  private String createName;
  @Column(name = "create_time")
  private Date createTime;
  @Column(name = "updator")
  private Integer updator;
  @Column(name = "update_name")
  private String updateName;
  @Column(name = "update_time")
  private Date updateTime;
  @Column(name = "message_id")
  private Integer messageId;
  @Column(name = "operation")
  private String operation;
  @Column(name = "previous_id")
  private Integer previousId;
  @Column(name = "version_no")
  private Integer versionNo;

/*  @ManyToOne(fetch= FetchType.LAZY )
  @JoinColumn(name="complaintBaseHistory", referencedColumnName = "id" , nullable=false , unique=false , insertable=false, updatable=false)
  private ComplaintContact historyComplaintContact;*/

  //waijian




  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Integer getComplaintBase() {
    return complaintBase;
  }

  public void setComplaintBase(Integer complaintBase) {
    this.complaintBase = complaintBase;
  }

  public Integer getComplaintBaseHistory() {
    return complaintBaseHistory;
  }

  public void setComplaintBaseHistory(Integer complaintBaseHistory) {
    this.complaintBaseHistory = complaintBaseHistory;
  }

  public Integer getComplaintContact() {
    return complaintContact;
  }

  public void setComplaintContact(Integer complaintContact) {
    this.complaintContact = complaintContact;
  }

  public String getContactName() {
    return contactName;
  }

  public void setContactName(String contactName) {
    this.contactName = contactName;
  }

  public String getContact() {
    return contact;
  }

  public void setContact(String contact) {
    this.contact = contact;
  }

  public String getContactPhone() {
    return contactPhone;
  }

  public void setContactPhone(String contactPhone) {
    this.contactPhone = contactPhone;
  }

  public String getPost() {
    return post;
  }

  public void setPost(String post) {
    this.post = post;
  }

  public Integer getCreator() {
    return creator;
  }

  public void setCreator(Integer creator) {
    this.creator = creator;
  }

  public String getCreateName() {
    return createName;
  }

  public void setCreateName(String createName) {
    this.createName = createName;
  }

  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public Integer getUpdator() {
    return updator;
  }

  public void setUpdator(Integer updator) {
    this.updator = updator;
  }

  public String getUpdateName() {
    return updateName;
  }

  public void setUpdateName(String updateName) {
    this.updateName = updateName;
  }

  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }

  public Integer getMessageId() {
    return messageId;
  }

  public void setMessageId(Integer messageId) {
    this.messageId = messageId;
  }

  public String getOperation() {
    return operation;
  }

  public void setOperation(String operation) {
    this.operation = operation;
  }

  public Integer getPreviousId() {
    return previousId;
  }

  public void setPreviousId(Integer previousId) {
    this.previousId = previousId;
  }

  public Integer getVersionNo() {
    return versionNo;
  }

  public void setVersionNo(Integer versionNo) {
    this.versionNo = versionNo;
  }
}
