package cn.sphd.miners.modules.purchaseInvoice.entity;

import cn.sphd.miners.modules.finance.entity.AccountDetailHistory;
import cn.sphd.miners.modules.finance.entity.FinanceAccount;
import cn.sphd.miners.modules.finance.entity.FinanceAccountBill;
import cn.sphd.miners.modules.personal.entity.PersonnelReimburse;
import cn.sphd.miners.modules.system.entity.Organization;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * 采购_票款处理申请表
 * 2021/6/16  lyx
 * 1.169采购的票款处理
 */
@Entity
@Table(name = "t_po_payment_application")
public class PoPaymentApplication implements Serializable {
    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="org"  , nullable=true, unique=false)
    private Integer org;   //机构ID

    @Column(name="applicant"  , nullable=true , unique=false)
    private Integer applicant;   //申请人ID

    @Column(name="applicant_name"  , length=255 , nullable=true , unique=false)
    private String applicantName;   //申请人姓名

    @Column(name="type"  , length=1 , nullable=true , unique=false)
    private String type;  //类型:1-仅提交票据,不提交付款申请;2-录入票据,并提交付款申请;3-仅提交付款申请,不录入票据

    @Column(name="bill_instance"  , nullable=true , unique=false)
    private Integer billInstance;   //票据申请实例ID

    @Column(name="bill_instance_chain"  , length=255 , nullable=true , unique=false)
    private String billInstanceChain ;   //票据申请实例链

    @Column(name="bill_process"  , nullable=true , unique=false)
    private Integer billProcess;   //票据审批过程ID

    @Column(name="bill_state"  , length=1 , nullable=true , unique=false)
    private String billState;  //票据审批状态:0-暂存,1-录入,2-提交,3-出纳在线审批通过,4-出纳在线审批驳回,5-采购审批通过,6-采购审批驳回,7-线下审核通过,8-线下审核驳回

    @Column(name="pay_instance"  , nullable=true , unique=false)
    private Integer payInstance;   //付款申请实例ID

    @Column(name="pay_instance_chain"  , length=255 , nullable=true , unique=false)
    private String payInstanceChain ;   //付款申请实例链

    @Column(name="pay_process"  , nullable=true , unique=false)
    private Integer payProcess;   //付款审批过程ID

    @Column(name="pay_state"  , length=1 , nullable=true , unique=false)
    private String payState ;   //付款审批状态:0-暂存,1-录入,2-提交,3-采购审批通过,4-采购审批驳回

    @Column(name="audit_instance"  , nullable=true , unique=false)
    private Integer auditInstance;   //付款复核实例ID

    @Column(name="audit_instance_chain"  , length=255 , nullable=true , unique=false)
    private String auditInstanceChain ;   //付款复核实例链

    @Column(name="audit_process"  , nullable=true , unique=false)
    private Integer auditProcess;   //付款复核过程ID

    @Column(name="audit_state"  , length=1 , nullable=true , unique=false)
    private String auditState ;   //付款复核状态:0-暂存,1-录入,2-提交,3-复核通过,4-复核驳回

    @Column(name="approve_status"  , length=1 , nullable=true , unique=false)
    private String approveStatus ;   //审批状态:0-未申请,1-申请提交,2-通过审核,3-否决审核,4-待处理 (财务的部门付款中基本是此状态)【此字段相当于总的审批状态】

    @Column(name="account_detail"  , nullable=true , unique=false)
    private Integer accountDetail;   //帐户明细ID

    @Column(name="finance_payment"  , nullable=true , unique=false)
    private Integer financePayment;   //财务统一支付表ID

    @Column(name="apply_date"   , nullable=true , unique=false)
    private Date applyDate;   //申请日期

    @Column(name="fee_category"  , length=30 , nullable=true , unique=false)
    private String feeCategory ;   //费用类别    在此默认为“采购材料”

    @Column(name="catetory_count"  , nullable=true , unique=false)
    private Integer catetoryCount;   //票据种类数

    @Column(name="bill_count"  , nullable=true , unique=false)
    private Integer billCount;   //票据数量

    @Column(name="bill_amount"   , nullable=true , unique=false)
    private BigDecimal billAmount;   //票据金额

    @Column(name="tax_amount"   , nullable=true , unique=false)
    private BigDecimal taxAmount;   //税额

    @Column(name="amount"   , nullable=true , unique=false)
    private BigDecimal amount;   //申请金额

    @Column(name="orders"  , nullable=true , unique=false)
    private Integer orders;   //订单ID

    @Column(name="application_reason"  , length=1 , nullable=true , unique=false)
    private String applicationReason ;   //申请原因:1-符合合同约定（默认）;2-不符合合同约定

    @Column(name="application_desc"  , length=255 , nullable=true , unique=false)
    private String applicationDesc ;   //付款原因说明(申请原因选择2-不符合合同约定时录入的具体原因)

    @Column(name="cashier_reject_reason"  , length=50 , nullable=true , unique=false)
    private String cashierRejectReason ;   //出纳驳回原因:1-照片不清楚,或信息被遮挡,以至于无法审核;2-无法入会计帐的票据较多;3-包含公司不允许报销的票据4-票据内容与所录入的信息不一致;5-其他原因 ',

    @Column(name="cashier_reject_desc"  , length=255 , nullable=true , unique=false)
    private String cashierRejectDesc ;   //出纳驳回其他原因说明

    @Column(name="buyer_reject_desc"  , length=255 , nullable=true , unique=false)
    private String buyerRejectDesc ;   //采购驳回原因说明

    @Column(name="pay_reject_reason"  , length=50 , nullable=true , unique=false)
    private String payRejectReason ;   //驳回原因:1-该供应商提供的产品或服务有问题;2-公司资金紧张,过些日子才能付款;3-没必要提前付款;4-其他原因

    @Column(name="pay_reject_desc"  , length=255 , nullable=true , unique=false)
    private String payRejectDesc ;   //驳回原因说明

    @Column(name="teminate_state"  , length=1 , nullable=true , unique=false)
    private String teminateState ;   //终止状态,0-未终止(默认),1-已终止

    @Column(name="teminate_time"   , nullable=true , unique=false)
    private Date teminateTime;  //终止时间

    @Column(name="teminate_reason"  , length=255 , nullable=true , unique=false)
    private String teminateReason ;   //终止原因

    @Column(name="teminater"   , nullable=true , unique=false)
    private Integer teminater;   //终止人ID

    @Column(name="teminater_name"  , length=100 , nullable=true , unique=false)
    private String teminaterName;   //终止人姓名

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo ;   //备注

    @Column(name="description"  , length=255 , nullable=true , unique=false)
    private String description ;   //描述

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;   //操作:1-增,2-删,3-改,4-终止订单,5-恢复订单

    @Column(name = "previous_id")
    private Integer previousId;   //修改前记录ID

    @Column(name = "version_no")
    private Integer versionNo;   //版本号,每次修改+1

    //1.231差额处理(1)添加--2022/11/15
    @Column(name="fact_amount"   , nullable=true , unique=false)
    private BigDecimal factAmount;   //实付金额

    @Column(name="paid_amount"   , nullable=true , unique=false)
    private BigDecimal paidAmount;   //已付金额

    @Column(name="locked_amount"   , nullable=true , unique=false)
    private BigDecimal lockedAmount;   //锁定(正在申批中)金额

    @Column(name="balance_amount"   , nullable=true , unique=false)
    private BigDecimal balanceAmount;   //差额(负数代表少付)

    @Column(name="loan_amount"   , nullable=true , unique=false)
    private BigDecimal loanAmount;   //借款金额

    @Column(name="loan"   , nullable=true , unique=false)
    private Integer loan;   //多付借款表ID

    @Column(name="pay_num"   , nullable=true , unique=false)
    private Integer payNum;   //支付笔数

    @Column(name="pay_status"   , nullable=true , unique=false)
    private Integer payStatus;   //支付现状:0-未支付,1-部分支付,2-支付完成

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getApplicant() {
        return applicant;
    }

    public void setApplicant(Integer applicant) {
        this.applicant = applicant;
    }

    public String getApplicantName() {
        return applicantName;
    }

    public void setApplicantName(String applicantName) {
        this.applicantName = applicantName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getBillInstance() {
        return billInstance;
    }

    public void setBillInstance(Integer billInstance) {
        this.billInstance = billInstance;
    }

    public String getBillInstanceChain() {
        return billInstanceChain;
    }

    public void setBillInstanceChain(String billInstanceChain) {
        this.billInstanceChain = billInstanceChain;
    }

    public Integer getBillProcess() {
        return billProcess;
    }

    public void setBillProcess(Integer billProcess) {
        this.billProcess = billProcess;
    }

    public String getBillState() {
        return billState;
    }

    public void setBillState(String billState) {
        this.billState = billState;
    }

    public Integer getPayInstance() {
        return payInstance;
    }

    public void setPayInstance(Integer payInstance) {
        this.payInstance = payInstance;
    }

    public String getPayInstanceChain() {
        return payInstanceChain;
    }

    public void setPayInstanceChain(String payInstanceChain) {
        this.payInstanceChain = payInstanceChain;
    }

    public Integer getPayProcess() {
        return payProcess;
    }

    public void setPayProcess(Integer payProcess) {
        this.payProcess = payProcess;
    }

    public String getPayState() {
        return payState;
    }

    public void setPayState(String payState) {
        this.payState = payState;
    }

    public Integer getAccountDetail() {
        return accountDetail;
    }

    public void setAccountDetail(Integer accountDetail) {
        this.accountDetail = accountDetail;
    }

    public Integer getFinancePayment() {
        return financePayment;
    }

    public void setFinancePayment(Integer financePayment) {
        this.financePayment = financePayment;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public String getFeeCategory() {
        return feeCategory;
    }

    public void setFeeCategory(String feeCategory) {
        this.feeCategory = feeCategory;
    }

    public Integer getCatetoryCount() {
        return catetoryCount;
    }

    public void setCatetoryCount(Integer catetoryCount) {
        this.catetoryCount = catetoryCount;
    }

    public Integer getBillCount() {
        return billCount;
    }

    public void setBillCount(Integer billCount) {
        this.billCount = billCount;
    }

    public BigDecimal getBillAmount() {
        return billAmount;
    }

    public void setBillAmount(BigDecimal billAmount) {
        this.billAmount = billAmount;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public String getApplicationReason() {
        return applicationReason;
    }

    public void setApplicationReason(String applicationReason) {
        this.applicationReason = applicationReason;
    }

    public String getApplicationDesc() {
        return applicationDesc;
    }

    public void setApplicationDesc(String applicationDesc) {
        this.applicationDesc = applicationDesc;
    }

    public String getCashierRejectReason() {
        return cashierRejectReason;
    }

    public void setCashierRejectReason(String cashierRejectReason) {
        this.cashierRejectReason = cashierRejectReason;
    }

    public String getCashierRejectDesc() {
        return cashierRejectDesc;
    }

    public void setCashierRejectDesc(String cashierRejectDesc) {
        this.cashierRejectDesc = cashierRejectDesc;
    }

    public String getBuyerRejectDesc() {
        return buyerRejectDesc;
    }

    public void setBuyerRejectDesc(String buyerRejectDesc) {
        this.buyerRejectDesc = buyerRejectDesc;
    }

    public String getPayRejectReason() {
        return payRejectReason;
    }

    public void setPayRejectReason(String payRejectReason) {
        this.payRejectReason = payRejectReason;
    }

    public String getPayRejectDesc() {
        return payRejectDesc;
    }

    public void setPayRejectDesc(String payRejectDesc) {
        this.payRejectDesc = payRejectDesc;
    }

    public String getTeminateState() {
        return teminateState;
    }

    public void setTeminateState(String teminateState) {
        this.teminateState = teminateState;
    }

    public Date getTeminateTime() {
        return teminateTime;
    }

    public void setTeminateTime(Date teminateTime) {
        this.teminateTime = teminateTime;
    }

    public String getTeminateReason() {
        return teminateReason;
    }

    public void setTeminateReason(String teminateReason) {
        this.teminateReason = teminateReason;
    }

    public Integer getTeminater() {
        return teminater;
    }

    public void setTeminater(Integer teminater) {
        this.teminater = teminater;
    }

    public String getTeminaterName() {
        return teminaterName;
    }

    public void setTeminaterName(String teminaterName) {
        this.teminaterName = teminaterName;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getAuditInstance() {
        return auditInstance;
    }

    public void setAuditInstance(Integer auditInstance) {
        this.auditInstance = auditInstance;
    }

    public String getAuditInstanceChain() {
        return auditInstanceChain;
    }

    public void setAuditInstanceChain(String auditInstanceChain) {
        this.auditInstanceChain = auditInstanceChain;
    }

    public Integer getAuditProcess() {
        return auditProcess;
    }

    public void setAuditProcess(Integer auditProcess) {
        this.auditProcess = auditProcess;
    }

    public String getAuditState() {
        return auditState;
    }

    public void setAuditState(String auditState) {
        this.auditState = auditState;
    }

    public BigDecimal getFactAmount() {
        return factAmount;
    }

    public void setFactAmount(BigDecimal factAmount) {
        this.factAmount = factAmount;
    }

    public BigDecimal getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(BigDecimal paidAmount) {
        this.paidAmount = paidAmount;
    }

    public BigDecimal getLockedAmount() {
        return lockedAmount;
    }

    public void setLockedAmount(BigDecimal lockedAmount) {
        this.lockedAmount = lockedAmount;
    }

    public BigDecimal getBalanceAmount() {
        return balanceAmount;
    }

    public void setBalanceAmount(BigDecimal balanceAmount) {
        this.balanceAmount = balanceAmount;
    }

    public BigDecimal getLoanAmount() {
        return loanAmount;
    }

    public void setLoanAmount(BigDecimal loanAmount) {
        this.loanAmount = loanAmount;
    }

    public Integer getLoan() {
        return loan;
    }

    public void setLoan(Integer loan) {
        this.loan = loan;
    }

    public Integer getPayNum() {
        return payNum;
    }

    public void setPayNum(Integer payNum) {
        this.payNum = payNum;
    }

    public Integer getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Integer payStatus) {
        this.payStatus = payStatus;
    }
}
