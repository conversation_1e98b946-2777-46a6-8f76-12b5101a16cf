<template>
  <div>
    <div class="head">
      <div style="width: 100%">
        <PageHeader :title="title" :showBack="true" :showClose="true" @mycloseFun="closeFun"></PageHeader>
      </div>
    </div>
    <p class="ttlSalary" v-if="info.type === 1">工资所属月份：{{ info.periodStr }}</p>
    <p class="ttlSalary" v-else >{{ info.periodStr }}{{ formatWageType(info.type) }}明细表</p>

    <div class="employeeBox" v-if="info.type === 1">
      <ul class="xiangUlB">
        <li></li>
        <li>
          <span>发放日期</span>
          <span>{{ new Date(info.personnelPayHistory.payTime).format('yyyy-MM-dd') }}</span>
        </li>
        <li>
          <span>发放人数</span>
          <span>{{ info.userNum }}人</span>
        </li>
        <li>
          <span>发放方式</span>
          <span>{{ info.personnelPayHistory.payWay === 1 ? '现金' : '转账' }}</span>
        </li>
        <li>
          <span>本次发放总额</span>
          <span>{{ formatCurrency(info.personnelPayHistory.factPay)  }}</span>
        </li>
        <li>
          <div style="display: flex;justify-content: space-between;width: 100%">
            <span style="width: 20%">创建</span>
            <div style="width: 80%; text-align: right;">
              <span>{{ this.info.personnelPayHistory.createName }}</span> &nbsp;&nbsp;
              <span style="width: 220px">{{ new Date(this.info.personnelPayHistory.createDate).format('yyyy-MM-dd hh:mm:ss')  }}</span>
            </div>
          </div>
        </li>
        <li class="cj" style="margin-bottom: 16px;">
          <span>修改记录</span>
          <div style=" display : flex; align-items: center;width: 25px" @click="changeL(2)">
            <img src="../../../assets/caikuai_img/next.svg" alt="" style="width: 25px">
          </div>
        </li>
      </ul>
    </div>
    <div class="employeeBox" v-else>
      <ul class="xiangUlB">
        <li></li>
        <li>
          <span>支出日期</span>
          <span>{{ new Date(info.personnelPayHistory.payTime).format('yyyy-MM-dd') }}</span>
        </li>
        <li>
          <span>职工人数</span>
          <span>{{ info.userNum }}人</span>
        </li>
        <li>
          <span>支出方式</span>
          <span>{{ info.personnelPayHistory.payWay === 1 ? '现金' : '转账'  }}</span>
        </li>
        <li>
          <span>支出总额</span>
          <span>{{ formatCurrency(info.personnelPayHistory.factPay)  }}</span>
        </li>
        <li>
          <div style="display: flex;justify-content: space-between;width: 100%">
            <span style="width: 20%">创建</span>
            <div style="width: 80%; text-align: right;">
              <span>{{ this.info.personnelPayHistory.createName }}</span> &nbsp;&nbsp;
              <span style="width: 220px">{{ new Date(this.info.personnelPayHistory.createDate).format('yyyy-MM-dd hh:mm:ss')  }}</span>
            </div>
          </div>
        </li>
        <li class="cj" style="margin-bottom: 16px;">
          <span>修改记录</span>
          <div style=" display : flex; align-items: center;width: 25px" @click="changeL(2)">
            <img src="../../../assets/caikuai_img/next.svg" alt="" style="width: 25px">
          </div>
        </li>
      </ul>
    </div>

    <div class="emTableBox">
      <table class="emTable">
        <thead>
        <th width="40%">姓名</th>
        <th width="30%">部门/岗位</th>
        <th width="30%">{{ info.type === 1 ? '实发金额' : '金额' }}</th>
        </thead>
        <tbody>
        <tr v-for="(item, index) in info.mapList" :key="index">
          <td><p>{{ item.userName }} {{ item.mobile }}</p></td>
          <td>{{ item.departName || '--' }}/{{ item.postName || '--' }}</td>
          <td>{{ item.factPay }}</td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
import finance from '@/mixins/finance.js'
export default {
  name: "employeeSalary",
  data() {
    return {
      info:'',
      title:'',
      tableData: []
    };
  },
  mixins: [finance],
  created() {
    let employeeSalaryData = localStorage.getItem('employeeSalary')
    if(employeeSalaryData){
      this.info = JSON.parse(employeeSalaryData)
      console.log('this.info = ', this.info )
      this.info.periodStr = this.info.period.substr(0,4) + '年' + this.info.period.substr(4,2) + '月'
      this.title = this.formatWageType(this.info.type )
    }else{
      this.$message.error('获取数据失败！')
      this.$router.go(-1)
    }
  },
  methods: {
    changeL(index) {
      let params = {
        type: this.info.type,
        period: this.info.period,
        versionNo: this.info.versionNo
      }
      localStorage.setItem('changeLog', JSON.stringify(params))
      this.$router.push("/changeLog")
    },

  }
}
</script>

<style scoped lang="scss">
@import "../../../style/account.scss";
@import "../../../style/common.scss";

.xiangUlB {
  width: 75%;

  li:first-child {
    justify-content: center;

    p {
      font-size: 18px;
    }
  }

  li {
    span {
      color: #555555;
    }

  }

  li:last-child {
    justify-content: space-between;
    line-height: 35px;

  }

}

.emTable {
  padding: 5px 0;
}

</style>