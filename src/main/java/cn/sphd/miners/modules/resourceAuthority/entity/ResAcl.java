package cn.sphd.miners.modules.resourceAuthority.entity;

import cn.sphd.miners.modules.forumArea.dto.PostUserMussage;
import org.apache.ibatis.type.Alias;

/**
 * Created by 朱思旭 on 2017/10/30.
 */

@Alias("resAcl")
public class ResAcl {
  private Integer id;
  private Integer resource;
  private Integer user;
  private String view;
  private String operate;
  private Integer creator;
  private String createName;
  private String createDate;
  private Integer updator;
  private String updateName;
  private String updateDate;
  private Integer approveItem;
  private String approveStatus;
  private Integer approveLevel;
  private Integer auditor;
  private String auditorName;
  private String auditDate;
  private String operation;
  private String applyMemo;
  private String approveMemo;
  private Integer messageId;

  private PostUserMussage postUserMussage;
  private String userName;

  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Integer getResource() {
    return resource;
  }

  public void setResource(Integer resource) {
    this.resource = resource;
  }

  public Integer getUser() {
    return user;
  }

  public void setUser(Integer user) {
    this.user = user;
  }

  public String getView() {
    return view;
  }

  public void setView(String view) {
    this.view = view;
  }

  public String getOperate() {
    return operate;
  }

  public void setOperate(String operate) {
    this.operate = operate;
  }

  public Integer getCreator() {
    return creator;
  }

  public void setCreator(Integer creator) {
    this.creator = creator;
  }

  public String getCreateName() {
    return createName;
  }

  public void setCreateName(String createName) {
    this.createName = createName;
  }

  public String getCreateDate() {
    return createDate;
  }

  public void setCreateDate(String createDate) {
    this.createDate = createDate;
  }

  public Integer getUpdator() {
    return updator;
  }

  public void setUpdator(Integer updator) {
    this.updator = updator;
  }

  public String getUpdateName() {
    return updateName;
  }

  public void setUpdateName(String updateName) {
    this.updateName = updateName;
  }

  public String getUpdateDate() {
    return updateDate;
  }

  public void setUpdateDate(String updateDate) {
    this.updateDate = updateDate;
  }

  public Integer getApproveItem() {
    return approveItem;
  }

  public void setApproveItem(Integer approveItem) {
    this.approveItem = approveItem;
  }

  public String getApproveStatus() {
    return approveStatus;
  }

  public void setApproveStatus(String approveStatus) {
    this.approveStatus = approveStatus;
  }

  public Integer getApproveLevel() {
    return approveLevel;
  }

  public void setApproveLevel(Integer approveLevel) {
    this.approveLevel = approveLevel;
  }

  public Integer getAuditor() {
    return auditor;
  }

  public void setAuditor(Integer auditor) {
    this.auditor = auditor;
  }

  public String getAuditorName() {
    return auditorName;
  }

  public void setAuditorName(String auditorName) {
    this.auditorName = auditorName;
  }

  public String getAuditDate() {
    return auditDate;
  }

  public void setAuditDate(String auditDate) {
    this.auditDate = auditDate;
  }

  public String getOperation() {
    return operation;
  }

  public void setOperation(String operation) {
    this.operation = operation;
  }

  public String getApplyMemo() {
    return applyMemo;
  }

  public void setApplyMemo(String applyMemo) {
    this.applyMemo = applyMemo;
  }

  public String getApproveMemo() {
    return approveMemo;
  }

  public void setApproveMemo(String approveMemo) {
    this.approveMemo = approveMemo;
  }

  public Integer getMessageId() {
    return messageId;
  }

  public void setMessageId(Integer messageId) {
    this.messageId = messageId;
  }

  public PostUserMussage getPostUserMussage() {
    return postUserMussage;
  }

  public void setPostUserMussage(PostUserMussage postUserMussage) {
    this.postUserMussage = postUserMussage;
  }

  public String getUserName() {
    return userName;
  }

  public void setUserName(String userName) {
    this.userName = userName;
  }

  public ResAcl(Integer resource, Integer user) {
    this.resource = resource;
    this.user = user;
  }

  public ResAcl() {
  }
}
