
.manage_conon>div{
	border-bottom: 1px solid #ccc;
	height:35px;
	line-height: 39px!important;
	width: 100%;
}
.manage_conon>div:nth-child(2){
	border-bottom: none;
}
.check_false{ background:url("icon/check_false.png") no-repeat center center ;      }
.check_true{ background:url("icon/check_true.png") no-repeat center center ;        }
.check_all_true{ background:url("icon/check_all_true.png") no-repeat center center ;        }
.check_all_false{ background:url("icon/check_all_false.png") no-repeat center center ;        }
.check_false , .check_true ,.check_all_false , .check_all_true { padding:10px; background-size:15px 15px;     }
.clr { clear: both ; }
/* 打印区域样式 */
div.printCon{ width: 210mm; font-family: "宋体" ; font-size:4.5mm;   }
.printItem{ height: 140mm;padding:18mm 1.5mm 18mm 1mm ; position: relative; margin:0 auto;  margin-bottom: 12mm;     }
.printItem>.printRight{ position: absolute; width:10mm; right:-12mm; top:34mm;  }
.printItem>.printRight>p{ height: 10mm;    }
.printItem>.head>p:nth-child(1){ text-align:center; font-size:10mm; font-weight: bold;  }
.printItem>.head>p:nth-child(2){ padding-bottom: 5mm;  }
.printItem>.head>p:nth-child(2)>span:nth-child(1){ width:40%;  }
.printItem>.head>p:nth-child(2)>span:nth-child(2), .printItem>.head>p:nth-child(2)>span:nth-child(3){ width:30%;  }
.printItem>.head>p:nth-child(2)>span{display: block; float: left;display: block;   }
.printItem>.head>p:nth-child(2)>span>span{display: inline-block; margin-left: 3mm;  }
.printItem>table{ width: 100%;  }
.printItem>table td{ border:1px solid #000; height:12mm;  }
.printItem>table>thead ,
.printItem>table>tbody>tr:last-child>td:nth-child(1),
.printItem>table>tbody>tr:last-child>td:nth-child(2) { text-align: center;  }
.printItem>table>tbody>tr:last-child>td:nth-child(1){ font-weight: bold;  }
.printItem>table>thead td , .printItem>table>tbody>tr:last-child>td{ border-bottom: 2px solid #000; border-top:2px solid #000 ;  }
.printItem>table>tbody td:nth-child(3) , .printItem>table>tbody td:nth-child(4){ text-align: right ;   }
.printItem>.foot{ padding:20px 0 ; overflow: auto; zoom: 1;   }
.printItem>.foot>span{ width: 20%; display:block; float:left;  }
.printItem>.foot>span>span{  display: inline-block; margin-left:2mm;   }








