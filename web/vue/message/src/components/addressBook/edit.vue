<template>
  <div class="edit" v-show="show" v-loading.fullscreen.lock="loading">
    <div class="edit_wrapper" id="navigationBar">
      <div class="edit_handle">
        <div class="edit_btn" @click="cancelShow"><i class="arrow arrow-left"></i></div>
        <div class="edit_title">{{editPercent}} 已选择</div>
      </div>
    </div>
    <div class="edit_container">
      <el-checkbox-group v-model="checks" @change="handleCheckedMsgChange">
        <el-checkbox v-for="(item, index) in res" :label="item.id" :key="index">
          <div class="ui-row">
            {{item.content}}
          </div>
          <div class="approveTime">
            <small class="timeContent">{{item.memo | timeSplit2}}</small> <small class="timeTitle">{{item.memo | timeSplit1}}</small><small class="blue" v-show="isDisappear">{{item.disappear}}</small>
          </div>
        </el-checkbox>
      </el-checkbox-group>
    </div>
    <div class="edit_bar">
      <div class="edit_button">
        <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
      </div>
      <div class="edit_button" @click="toRead" v-show="!isDisappear">
        <div class="chooseAll">标记已读</div>
      </div>
      <div class="edit_button" type="error" @click="delChoose">
        <div class="chooseAll">删除</div>
      </div>
    </div>
  </div>
</template>

<style lang="less">
  @green-blue   : #36c6d3;
  @red     : #ed5565;
  .edit{
    position: fixed;
    top: 0;
    left: 0;
    z-index:1000;
    width: 100%;
    height: 100%;
    background-color: #fff;
  }
  .edit_wrapper {
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    display: block;
    height: 48px;
    background-color: #36c6d3;
  }
  .edit_handle{
    display: flex;
    height: 100%;
    line-height: 48px;
    .edit_title{
      color: #fff;
    }
    .edit_btn{
      width: 32px;
      height: 100%;
      background-color: @green-blue;
      cursor: pointer;
    }
  }
  .edit_container{
    width: 100%;
    overflow: auto;
    position: absolute;
    top: 48px;
    z-index: 10;
    height: calc(100% - 96px);
    .el-checkbox{
      display: block;
      background-color: #fafafb;
      padding: .8rem 1.5rem;
      margin: 0;
      position: relative;
      display: -ms-flexbox;
      display: flex;
      -ms-flex-align: center;
      align-items: center;
      font-size: 1.2rem;
      overflow: hidden;
      color: inherit;
      cursor: pointer;
    }
  }
  .edit_bar{
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    height: 48px;
    line-height: 48px;
    background-color: @green-blue;
    display: flex;
    .edit_button {
      height: 100%;
      flex: 1;
      text-align: center;
      color: #314650;
      background-color: #eee;
      cursor: pointer;
      &[type='error']{
        background: @red;
        color: #fff;
      }

    }
  }
  .approveTime{
    display: flex;
    flex-direction: row-reverse;
    font-size: 12px;
    small{
      flex-shrink: 0;
      flex-grow: 0;
    }
  }
  .approveTime .timeTitle{
    width: 42px;
    overflow: hidden;
  }
  .approveTime .timeContent{
    width: 145px;
    margin-right: 12px;
    text-align: right;
  }
  .approveTime small.blue{
    color: #4797f1;
    flex-grow: 1;
  }
  .edit_container .ui-row{
    width: 326px;
  }
</style>

<script>
export default {
  name: 'edit',
  props: {
    res: Array,
    show: Boolean,
    isDisappear: Boolean,
    loading: false
  },
  filters: {
    'timeSplit1': function (value) {
      return value.substring(0, value.indexOf(' '))
    },
    'timeSplit2': function (value) {
      return value.substring(value.indexOf(' '), value.length)
    }
  },
  data () {
    return {
      checkAll: false,
      checks: [],
      isIndeterminate: false
    }
  },
  computed: {
    editPercent: function () {
      return this.checks.length + '/' + this.res.length
    }
  },
  methods: {
    cancelShow: function () {
      this.$emit('update:show', false)
      this.checks = []
      this.isIndeterminate = false
      this.checkAll = false
    },
    handleCheckAllChange (val) {
      this.checks = val ? this.res.map(obj => { return obj.id }) : []
      this.isIndeterminate = false
    },
    handleCheckedMsgChange (value) {
      let checkedCount = value.length
      this.checkAll = checkedCount === this.res.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.res.length
      console.log(checkedCount)
      console.log(this.res.length)
      console.log(checkedCount > 0 && checkedCount < this.res.length)
    },
    toRead () {
      let that = this
      if (this.checks.length === 0) {
        this.$kiko_message('请至少选择一个对象！')
      } else {
        this.$confirm('确定将所选设为已读吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          that.loading = true
          that.axios.post('../../../daily/userSuspendMsgsToRead.do', {
            userId: that.sphdSocket.user.userID,
            messageIds: that.checks.join(',')
          }).then(function (response) {
            that.loading = false
            console.log(response)
            let state = response.data.data
            if (state === 1) {
              that.$kiko_message('标记成功！')
              that.$emit('updateList', that.checks)
              that.$emit('update:show', false)
              that.checks = []
              that.isIndeterminate = false
              that.checkAll = false
            } else {
              that.$kiko_message('标记失败！')
            }
          }).catch(function (error) {
            console.log(error)
          })
        })
      }
    },
    delChoose () {
      let that = this
      if (this.checks.length === 0) {
        this.$kiko_message('请至少选择一个对象！')
      } else {
        this.$confirm('确定将所选删除吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          that.loading = true
          that.axios.post('../../../daily/deleteSuspendMsgs.do', {
            userId: that.sphdSocket.user.userID,
            messageIds: that.checks.join(',')
          }).then(function (response) {
            that.loading = false
            console.log(response)
            let state = response.data.data
            if (state === 1) {
              that.$kiko_message('删除成功！')
              that.$emit('updateList', that.checks)
              that.$emit('update:show', false)
              that.checks = []
              that.isIndeterminate = false
              that.checkAll = false
            } else {
              that.$kiko_message('删除失败！')
            }
          }).catch(function (error) {
            console.log(error)
          })
        })
      }
    }
  }
}
</script>
