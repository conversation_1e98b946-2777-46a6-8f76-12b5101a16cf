package cn.sphd.miners.modules.accountant.mapper;


import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectEntity;
import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectHistory;
import org.springframework.stereotype.Component;

import java.util.List;
@Component
public interface TAccountantSubjectHistoryMapper extends BaseMapper<TAccountantSubjectHistory> {

    //按期数清除科目数据
    int delSubjectHistory(TAccountantSubjectHistory sh);

    //新增科目暂存数据
    int insertOrgSubject(TAccountantSubjectEntity s);

    List<TAccountantSubjectHistory> listAllSubject(TAccountantSubjectHistory s);

    //获取一条暂存表数据
    TAccountantSubjectHistory getOneSubjecthistory(TAccountantSubjectHistory sh);

    List<TAccountantSubjectHistory> childSubject(TAccountantSubjectHistory s);

    List<TAccountantSubjectHistory> getAllHistorySubject(TAccountantSubjectHistory sh);

    /*结帐后需要把一些数据写回到科目表作为下次结帐时使用*/
    int updateOrgSubject(TAccountantSubjectHistory s);

    void swapBalance(int org);

    TAccountantSubjectHistory getSubjectHistoryBySubject(TAccountantSubjectHistory subEntityParam);

    int updateBeginning();
}