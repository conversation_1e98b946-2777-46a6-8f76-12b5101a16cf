package cn.sphd.miners.modules.material.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.material.entity.MtPackagingItem;
import cn.sphd.miners.modules.material.entity.MtPackagingItemHistory;

import java.io.Serializable;

public interface MtPackagingItemHistoryDao extends IBaseDao<MtPackagingItemHistory, Serializable> {

    //添加历史记录
    MtPackagingItemHistory insert(MtPackagingItem pdPackagingItem, Long structureHistoryId);
}
