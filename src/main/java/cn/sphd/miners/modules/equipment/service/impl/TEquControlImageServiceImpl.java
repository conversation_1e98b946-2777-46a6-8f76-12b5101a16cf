package cn.sphd.miners.modules.equipment.service.impl;

import java.util.List;

import cn.sphd.miners.modules.equipment.entity.TEquControlImage;
import cn.sphd.miners.modules.equipment.mapper.TEquControlImageMapper;
import cn.sphd.miners.modules.equipment.service.ITEquControlImageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;

/**
 * Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-04-13
 */
@Service
@Transactional
public class TEquControlImageServiceImpl implements ITEquControlImageService
{
    @Autowired
    private TEquControlImageMapper tEquControlImageMapper;

    /**
     * 查询
     *
     * @param id 主键
     * @return 
     */
    @Override
    public TEquControlImage selectTEquControlImageById(Long id)
    {
        return tEquControlImageMapper.selectTEquControlImageById(id);
    }

    /**
     * 查询列表
     * 
     * @param tEquControlImage 
     * @return 
     */
    @Override
    public List<TEquControlImage> selectTEquControlImageList(TEquControlImage tEquControlImage)
    {
        return tEquControlImageMapper.selectTEquControlImageList(tEquControlImage);
    }

    /**
     * 新增
     * 
     * @param tEquControlImage 
     * @return 结果
     */
    @Override
    public int insertTEquControlImage(TEquControlImage tEquControlImage)
    {
        return tEquControlImageMapper.insertTEquControlImage(tEquControlImage);
    }

    /**
     * 修改
     * 
     * @param tEquControlImage 
     * @return 结果
     */
    @Override
    public int updateTEquControlImage(TEquControlImage tEquControlImage)
    {
        return tEquControlImageMapper.updateTEquControlImage(tEquControlImage);
    }

    /**
     * 批量删除
     * 
     * @param ids 需要删除的主键
     * @return 结果
     */
    @Override
    public int deleteTEquControlImageByIds(String ids)
    {
        return tEquControlImageMapper.deleteTEquControlImageByIds(ids.split(","));
    }

    /**
     * 删除信息
     * 
     * @param id 主键
     * @return 结果
     */
    @Override
    public int deleteTEquControlImageById(Long id)
    {
        return tEquControlImageMapper.deleteTEquControlImageById(id);
    }
}
