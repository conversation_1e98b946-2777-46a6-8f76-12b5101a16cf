<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.accountant.mapper.SubjectHistoryMapper" >

  <select id="listPage" parameterType="hashmap" resultType="voucherSubjectHistory">
    select
    id, voucher_subject_id AS voucherSubject, org, voucher_id AS voucherId, acount_date AS acountDate, subject, direction, amount,
    subject_detail AS subjectDetail, creator, create_name AS createName, create_date AS createDate, updator,
    update_name AS updateName, update_date AS updateDate,
    approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName, audit_date AS auditDate, operation,
    apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId, subject_names AS subjectNames,reimburse_bill AS reimburseBill,reimburse_bill_item AS reimburseBillItem,memo
    from t_accountant_voucher_subject_history
  </select>

  <select id="getSingle" parameterType="voucherSubjectHistory" resultType="voucherSubjectHistory">
    select 
    id, voucher_subject_id AS voucherSubject, org, voucher_id AS voucherId, acount_date AS acountDate, subject, direction, amount,
    subject_detail AS subjectDetail, creator, create_name AS createName, create_date AS createDate, updator,
    update_name AS updateName, update_date AS updateDate,
    approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName, audit_date AS auditDate, operation,
    apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId, subject_names AS subjectNames,reimburse_bill AS reimburseBill,reimburse_bill_item AS reimburseBillItem,memo
    from t_accountant_voucher_subject_history
    where id = #{id}
  </select>

  <select id="getSubjectHistoryInfo" resultType="subjectTemp">
    SELECT v.id,v.subject AS subject, v.amount AS amount,s.name AS name, v.subject_names AS subjectNames,v.reimburse_bill AS reimburseBill,
    v.reimburse_bill_item AS reimburseBillItem,v.memo,
    d.credit_quantity AS creditQuantity,d.debit_quantity AS debitQuantity,d.unit_price AS unitPrice,s.measure_unit AS measureUnit,
    s.quantity_assisting_accounting AS quantityAssistingAccounting
    FROM `t_accountant_voucher_subject_history` v,
    t_accountant_subject s,
    t_accountant_subject_detail_history d
      WHERE v.voucher_id=#{id} AND v.direction=#{direction} AND v.subject = s.subject AND s.org=#{org} and d.subject_id=v.id
  </select>

  <!-- 从凭证科目表得到金额 -->
  <select id="getSubjectByDirection" resultType="voucherSubjectHistory">
    SELECT id,org,amount,voucher_id AS voucherId,acount_date AS acountDate,subject,direction,subject_detail AS subjectDetail,
    creator,create_name as createName,create_date AS createDate,approve_status AS approveStatus,reimburse_bill AS reimburseBill,reimburse_bill_item AS reimburseBillItem,memo
    FROM `t_accountant_voucher_subject_history` WHERE voucher_id=#{voucherId} AND direction=#{direction}
  </select>
  <select id="getSubjectByVoucher"
          resultType="voucherSubjectHistory">

        SELECT id,org,amount,voucher_id AS voucherId,acount_date AS acountDate,subject,direction,subject_detail AS subjectDetail,
    creator,create_name as createName,create_date AS createDate,approve_status AS approveStatus, subject_names AS subjectNames,
    reimburse_bill AS reimburseBill,reimburse_bill_item AS reimburseBillItem,memo
    FROM `t_accountant_voucher_subject_history` WHERE voucher_id=#{voucherId}

  </select>

  <delete id="delete" parameterType="voucherSubjectHistory" >
    delete from t_accountant_voucher_subject_history
    where id = #{id}
  </delete>

  <delete id="deleteHistorySubject">
    DELETE FROM t_accountant_voucher_subject_history WHERE voucher_id=#{voucherId}
  </delete>
  <delete id="deleteByOrg">
    DELETE FROM t_accountant_voucher_subject_history WHERE org=#{org}
  </delete>

  <insert id="insert" parameterType="voucherSubjectHistory" useGeneratedKeys="true" keyProperty="id">
    insert into t_accountant_voucher_subject_history ( voucher_subject_id, org,
      voucher_id, acount_date, subject, 
      direction, amount, subject_detail, 
      creator, create_name, create_date, 
      updator, update_name, update_date, 
      approve_Item, approve_status, approve_level, 
      auditor, auditor_name, audit_date, 
      operation, apply_memo, approve_memo, 
      message_id, subject_names,reimburse_bill,reimburse_bill_item,memo)
    values ( #{voucherSubjectId}, #{org},
      #{voucherId}, #{acountDate}, #{subject},
      #{direction}, #{amount}, #{subjectDetail},
      #{creator}, #{createName}, #{createDate},
      #{updator}, #{updateName}, #{updateDate},
      #{approveItem}, #{approveStatus}, #{approveLevel},
      #{auditor}, #{auditorName}, #{auditDate},
      #{operation}, #{applyMemo}, #{approveMemo},
      #{messageId}, #{subjectNames},#{reimburseBill},#{reimburseBillItem},#{memo})
  </insert>

  <update id="update" parameterType="voucherSubjectHistory" >
    update t_accountant_voucher_subject_history
    <set >
      <if test="voucherSubjectId != null" >
        voucher_subject_id = #{voucherSubjectId},
      </if>
      <if test="org != null" >
        org = #{org},
      </if>
      <if test="voucherId != null" >
        voucher_id = #{voucherId},
      </if>
      <if test="acountDate != null" >
        acount_date = #{acountDate},
      </if>
      <if test="subject != null" >
        subject = #{subject},
      </if>
      <if test="direction != null" >
        direction = #{direction},
      </if>
      <if test="amount != null" >
        amount = #{amount},
      </if>
      <if test="subjectDetail != null" >
        subject_detail = #{subjectDetail},
      </if>
      <if test="creator != null" >
        creator = #{creator},
      </if>
      <if test="createName != null" >
        create_name = #{createName},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate},
      </if>
      <if test="updator != null" >
        updator = #{updator},
      </if>
      <if test="updateName != null" >
        update_name = #{updateName},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate},
      </if>
      <if test="approveItem != null" >
        approve_Item = #{approveItem},
      </if>
      <if test="approveStatus != null" >
        approve_status = #{approveStatus},
      </if>
      <if test="approveLevel != null" >
        approve_level = #{approveLevel},
      </if>
      <if test="auditor != null" >
        auditor = #{auditor},
      </if>
      <if test="auditorName != null" >
        auditor_name = #{auditorName},
      </if>
      <if test="auditDate != null" >
        audit_date = #{auditDate},
      </if>
      <if test="operation != null" >
        operation = #{operation},
      </if>
      <if test="applyMemo != null" >
        apply_memo = #{applyMemo},
      </if>
      <if test="approveMemo != null" >
        approve_memo = #{approveMemo},
      </if>
      <if test="messageId != null" >
        message_id = #{messageId},
      </if>
      <if test="subjectNames != null">
        subject_names = #{subjectNames}
      </if>
    </set>
    where id = #{id}
  </update>

  <update id="updateHistorySubjectNo">
    UPDATE `t_accountant_voucher_subject_history` SET subject=#{subject} WHERE voucher_id=#{voucherId} AND direction=#{direction}
  </update>
</mapper>