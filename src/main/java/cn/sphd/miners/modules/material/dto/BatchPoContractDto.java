package cn.sphd.miners.modules.material.dto;


public class BatchPoContractDto {

    private String sn;
    private String contractSignTime;
    private String contractStartTime;
    private String contractEndTime;
    private String filePath;
    private String fileName;
    private String memo;
    private String contractBaseImages;
    private String mtList;

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getContractSignTime() {
        return contractSignTime;
    }

    public void setContractSignTime(String contractSignTime) {
        this.contractSignTime = contractSignTime;
    }

    public String getContractStartTime() {
        return contractStartTime;
    }

    public void setContractStartTime(String contractStartTime) {
        this.contractStartTime = contractStartTime;
    }

    public String getContractEndTime() {
        return contractEndTime;
    }

    public void setContractEndTime(String contractEndTime) {
        this.contractEndTime = contractEndTime;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getContractBaseImages() {
        return contractBaseImages;
    }

    public void setContractBaseImages(String contractBaseImages) {
        this.contractBaseImages = contractBaseImages;
    }

    public String getMtList() {
        return mtList;
    }

    public void setMtList(String mtList) {
        this.mtList = mtList;
    }

    public BatchPoContractDto(){}

    public BatchPoContractDto(String sn, String contractSignTime, String contractStartTime, String contractEndTime, String filePath, String fileName, String memo, String contractBaseImages, String mtList) {
        this.sn = sn;
        this.contractSignTime = contractSignTime;
        this.contractStartTime = contractStartTime;
        this.contractEndTime = contractEndTime;
        this.filePath = filePath;
        this.fileName = fileName;
        this.memo = memo;
        this.contractBaseImages = contractBaseImages;
        this.mtList = mtList;
    }
}
