import auth from '@/sys/auth'
import JSONBig from 'json-bigint'
// import store from '@/store/index'

/**
 * <AUTHOR>
 * @description 初始化本地存储变量
 * @since 2022/1/10 17:37
 * @method
 * @param accId 仅在游客账号并需要上传领地文件时输入用户accId
 * @param userId 仅在游客账号并需要上传机构文件时输入用户userId
 */
export default function initStorage ({accId, userId}) {
  if (process.env.NODE_ENV === 'development' && typeof process.env.VUE_APP_HREF === 'string' && process.env.VUE_APP_HREF.length > 0 && auth.getPath(location.href) !== process.env.VUE_APP_HREF) {
    // console.log('initStorage jump')
    location.href = process.env.VUE_APP_HREF
  }
  flashMenuBadge()
  getUploadPaths(accId, userId)
}

/**
 * <AUTHOR>
 * @description 更新路径
 * @since 2022/1/10 13:13
 * @method
 * @param accId
 * @param userId
 */
function getUploadPaths (accId, userId) {
  if (typeof window.parent.$ === 'undefined' && auth.getAcc()?.id >= 0 && typeof accId !== 'undefined' && typeof userId !== 'undefined') {
    // console.log('getUploadPaths auth.getAcc().id', typeof auth.getAcc().id, auth.getAcc().id)
    if (auth.getUserID() != null) {
      userId = auth.getUserID()
      accId = auth.getAcc().id
    } else if (auth.getAcc().id !== -1) {
      accId = auth.getAcc().id
      userId = -1
    }
    let url = auth.webRoot + '/uploads/getRootPath.do'
    let params = []
    if (parseInt(userId) >= 0) {
      params.push('userId=' + parseInt(userId))
    }
    if (parseInt(accId) >= 0) {
      params.push('accId=' + parseInt(accId))
    }
    if (params.length > 0) {
      url += '?' + params.join('&')
    }
    // console.log('getUploadPaths url', url)
    let response = auth.getUrl(url)
    // console.log(response)
    if (typeof response === 'string' && response.length > 0) {
      try {
        window.parent.$ = JSONBig.parse(response)
        // console.log('new window.parent.$', window.parent.$)
      } catch (e) {
        console.error(e)
      }
    }
  }
}

/**
 * <AUTHOR>
 * @description 更新角标
 * @since 2022/1/10 13:14
 * @method
 * @Param null:
 * @return: null:
 */
function flashMenuBadge () {
  try {
    if (auth.getUser() !== null && ((localStorage.getItem('mainMenu') && localStorage.getItem('messageMenu') && localStorage.getItem('userBadgeNumbers') && localStorage.getItem('rolePrincipals')) || false) === false) {
      let response = auth.getUrl(auth.webRoot + '/sys/getAllMenu.do')
      if (typeof response === 'string' && response.length > 0) {
        let res = JSONBig.parse(response)
        let data = res.data
        localStorage.setItem('mainMenu', data.mainMenu)
        localStorage.setItem('messageMenu', data.messageMenu)
        localStorage.setItem('rolePrincipals', JSONBig.stringify(data.rolePrincipals))
        if (data.userBadgeNumbers !== null && data.userBadgeNumbers.toString().length > 0) {
          //   let userBadge = JSONBig.parse(data.userBadgeNumbers)
          //   store.dispatch('setNewMenu', userBadge)
          //   store.dispatch('setNewVersion', userBadge)
        }
      }
    }
  } catch (e) {
    // console.log('JSONBig.parse error', e)
  }
}