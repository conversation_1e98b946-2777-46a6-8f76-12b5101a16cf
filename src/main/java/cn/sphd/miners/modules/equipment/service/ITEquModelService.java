package cn.sphd.miners.modules.equipment.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.equipment.entity.TEquFixedAssets;
import cn.sphd.miners.modules.equipment.entity.TEquModel;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;
import java.util.Map;

/**
 * Service接口
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
public interface ITEquModelService {
    /**
     * 查询
     *
     * @param id 主键
     * @return 
     */
    public TEquModel selectTEquModelById(Long id);

    /**
     * 查询列表
     *
     * @param tEquModel 
     * @return 集合
     */
    public List<TEquModel> selectTEquModelList(TEquModel tEquModel);

    /**
     * 新增
     *
     * @param tEquModel
     * @param fixedAssets
     * @param user
     * @return 结果
     */
    public int insertTEquModel(TEquModel tEquModel, TEquFixedAssets fixedAssets, User user);

    /**
     * 修改
     *
     * @param tEquModel 
     * @param user
     * @return 结果
     */
    public int updateTEquModel(TEquModel tEquModel, User user);

    /**
     * 批量删除
     *
     * @param ids 需要删除的主键集合
     * @return 结果
     */
    public int deleteTEquModelByIds(String... ids);

    /**
     * 删除信息
     *
     * @param id 主键
     * @return 结果
     */
    public int deleteTEquModelById(Long id);

    List<Map<String, Object>> selectTEquModelGroupList(TEquModel tEquModel);

    List<Map<String, Object>> selectEquModelDetailList(TEquModel tEquModel);

    Map selectEquModelDetailList(TEquModel tEquModel, PageInfo pageInfo);

    int batchClassification(String[] ids);

    Map geModelDetail(Long id);

    List<Map<String, Object>> selectEquModelDetailListNoGroup(TEquModel tEquModel);

    void updateTEquModelByModel(TEquModel model, User user);

    void batchModelAdd(List<TEquModel> tEquModels, User user);
}
