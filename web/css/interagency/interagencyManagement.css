.mainCon{margin: 0 90px;display: none;}
.organs li{float: left;width: 150px;line-height: 40px;height: 40px;}
.organs li:nth-child(2n+1){margin-right: 100px;}
.organs li i{margin-right: 14px;}
.flexBt{display: flex;justify-content: space-between;}
#otherMonth{padding: 5px 10px;margin-left: 20px;width: 150px;border: 1px solid #bebebe;}
#otherMonth + span{border-bottom-right-radius: 4px;border-top-right-radius: 4px;}
.logHead{padding-bottom: 20px;margin: 32px 0;border-bottom: 1px solid #bebebe;}
.modLogMain{margin: 100px 0 0 100px;}
.funBtn{width: 130px;height: 40px;line-height: 40px;text-align: center;border-radius: 3px;cursor: pointer;display: inline-block;border: none;}
.vtip{margin: 20px 0;clear: both;}
.moreGuideCon{margin: auto;width:85%;}
.moreGuideCon p{padding-top: 20px}
.tb-main{padding-top: 10px;}
.employeeInfo{margin-right: 62px;}
.employeeOrg{margin-left: 12px;}
.modMain{width: 80%;margin-left: 100px;margin-top: 20px;}
.modMain .employeeInfo, .paceVm{margin-bottom: 62px;}
.gapTp {padding-top: 40px}
.biaotou{width: 200px;}
.oblique_line{position: relative;}
.oblique_line:before{content: "";position: absolute;width: 1.5px;height: 205px;top: -10px;left: -18px;background-color: #d7d7d7;display: block;transform: rotate(-79deg);
    transform-origin: top;}
#opertionLog .red {color: red;}
.mainCon1 table .gray {background: #e5e3e3;}
.mainCon1 .ty-table td {max-height: 40px;}
.scrollWrap{width:100%; overflow-x:scroll;}
.scrollWrap .ty-table td{white-space: nowrap;}



