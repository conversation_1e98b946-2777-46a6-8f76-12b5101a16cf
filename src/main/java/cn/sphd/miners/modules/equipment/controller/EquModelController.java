package cn.sphd.miners.modules.equipment.controller;

import java.util.*;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.equipment.entity.TEquEquipment;
import cn.sphd.miners.modules.equipment.entity.TEquFixedAssets;
import cn.sphd.miners.modules.equipment.entity.TEquModel;
import cn.sphd.miners.modules.equipment.service.ITEquEquipmentService;
import cn.sphd.miners.modules.equipment.service.ITEquFixedAssetsService;
import cn.sphd.miners.modules.equipment.service.ITEquModelHistoryService;
import cn.sphd.miners.modules.equipment.service.ITEquModelService;
import cn.sphd.miners.modules.mci.utils.AjaxResult;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import static cn.sphd.miners.modules.mci.utils.AjaxResult.toAjax;

/**
 * Controller
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Controller
@RequestMapping("/equipment/model")
public class EquModelController {
    private String prefix = "system/model";

    @Autowired
    private ITEquModelService tEquModelService;

    @Autowired
    private ITEquEquipmentService equEquipmentService;

    @Autowired
    private ITEquModelHistoryService equModelHistoryService;

    @Autowired
    ITEquFixedAssetsService fixedAssetsService;

    @GetMapping()
    public String model() {
        return prefix + "/model";
    }

    /**
     * 查询列表
     */
    @RequestMapping("/list")
    @ResponseBody
    public List<Map<String, Object>> list(TEquModel tEquModel, User user) {
//        List<TEquModel> list = tEquModelService.selectTEquModelList(tEquModel);
//        return list;
        List<Map<String, Object>> list;

        tEquModel.setOrg(Long.valueOf(user.getOid()));
        list = tEquModelService.selectEquModelDetailListNoGroup(tEquModel);

        return list;
    }

    @PostMapping("/pageList")
    @ResponseBody
    public Map pageList(TEquModel tEquModel, User user, PageInfo pageInfo) {
//        List<TEquModel> list = tEquModelService.selectTEquModelList(tEquModel);
//        return list;
        tEquModel.setOrg(Long.valueOf(user.getOid()));
        Map pageList = tEquModelService.selectEquModelDetailList(tEquModel, pageInfo);

        return pageList;
    }


    @GetMapping("/groupList")
    @ResponseBody
    public List<Map<String, Object>> groupList(TEquModel tEquModel) {
        List<Map<String, Object>> list = tEquModelService.selectTEquModelGroupList(tEquModel);
        return list;
    }


    @RequestMapping("/emEdit")
    @ResponseBody
    public Map emEdit(String ids, Long eid, TEquModel equModel, User user) {
        TEquModel old = new TEquModel();
        List<Map<String, Object>> models = new ArrayList<>();

        HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
        if (StringUtils.isNotBlank(ids)) {
            for (String id : ids.split(",")) {
                TEquFixedAssets fixedAssets = fixedAssetsService.selectTEquFixedAssetsById(Long.valueOf(id));

                TEquModel model = tEquModelService.selectTEquModelById(fixedAssets.getModel());


                if (model != null) {
                    model.setId(Long.valueOf(id));
                    model.setEquipment(eid);
                    model.setModelName(equModel.getModelName());
                    model.setUnitId(equModel.getUnitId());
                    model.setUnit(equModel.getUnit());
                    model.setSupplier(equModel.getSupplier());
                    model.setOperation(equModel.getOperation());
                    model.setEmCount(equModel.getEmCount());
                    model.setQuantity(equModel.getEmCount());
                    tEquModelService.updateTEquModel(model, user);

                    old.setModelName(equModel.getModelName());
                    old.setSupplier(equModel.getSupplier());
                    old.setEquipment(equModel.getEquipment());
                    old.setUnitId(equModel.getUnitId());

                    models.addAll(this.list(old, user));


                }
            }
        }
        TEquEquipment e = equEquipmentService.selectTEquEquipmentById(eid);
        objectObjectHashMap.put("e", e);


        //循环拼接集合内的id
        StringBuilder sb = new StringBuilder();
        for (Map<String, Object> model : models) {
            if (!sb.toString().contains(model.get("id") + "")) {
                sb.append(model.get("id")).append(",");
            }
        }
        //获取剩余的id
        String idse = sb.toString();
        objectObjectHashMap.put("ids", idse.endsWith(",") ? idse.substring(0, idse.length() - 1) : idse);
        return objectObjectHashMap;
    }

    /**
     * 新增
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存
     */
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(TEquModel tEquModel, TEquFixedAssets fixedAssets, User user) {
        return toAjax(tEquModelService.insertTEquModel(tEquModel, fixedAssets, user));
    }

    @PostMapping("/batchModelAdd")
    @ResponseBody
    public AjaxResult batchAdd(String tEquModelsJson, User user) {

        List<TEquModel> tEquModels = JSON.parseArray(tEquModelsJson, TEquModel.class);
        tEquModelService.batchModelAdd(tEquModels, user);

        return toAjax(1);
    }


    /**
     * 修改
     */
    @GetMapping("/edit/{id}")
    @ResponseBody
    public TEquModel edit(@PathVariable("id") Long id) {
        TEquModel tEquModel = tEquModelService.selectTEquModelById(id);

        return tEquModel;
    }

    /**
     * 修改保存
     */
    @PostMapping("/edit")
    @ResponseBody
    public Map<String, Object> editSave(TEquModel tEquModel, User user) {
        TEquModel now = tEquModelService.selectTEquModelById(tEquModel.getId());

        HashMap<String, Object> objectObjectHashMap = new HashMap<>();

        tEquModel.setQuantity(tEquModel.getEmCount());



        tEquModelService.updateTEquModel(tEquModel, user);

        TEquModel old = new TEquModel();
        old.setModelName(tEquModel.getModelName());
        old.setSupplier(tEquModel.getSupplier());
        old.setEquipment(tEquModel.getEquipment());
        old.setUnitId(tEquModel.getUnitId());

        List<Map<String, Object>> models = this.list(old, user);
        //循环拼接集合内的id
        StringBuilder sb = new StringBuilder();
        for (Map<String, Object> model : models) {
            sb.append(model.get("id")).append(",");
        }


        objectObjectHashMap.put("ids", sb.toString().endsWith(",") ? sb.substring(0, sb.length() - 1) : sb.toString());

        return objectObjectHashMap;
    }

    /**
     * 删除
     */
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(tEquModelService.deleteTEquModelByIds(ids));
    }

    @GetMapping("/batchClassification")
    @ResponseBody
    public AjaxResult batchClassification(String[] ids) {
        return toAjax(tEquModelService.batchClassification(ids));
    }

    @GetMapping("/detail")
    @ResponseBody
    public Map detail(Long id) {
        Map map = tEquModelService.geModelDetail(id);

        return map;
    }
}
