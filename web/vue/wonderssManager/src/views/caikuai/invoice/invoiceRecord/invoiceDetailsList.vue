<template>
  <div class="box">
    <div class="head">
      <PageHeader title="财会" @mycloseFun="handleCustomClose" :showClose=true :showBack=true>
      </PageHeader>
    </div>
    <div class="padding-10-15 ty-fff sticky" v-if="list.monthStatistics&&invoiceDetailsMonthly">
      <p>{{ new
      Date(list.beginTime).format('yyyy-MM-dd')}}至{{ new
      Date(list.endTime).format('yyyy-MM-dd')}}期间</p>
      <p>
        开具{{ invoiceDetailsMonthly.type == 1 ? '增值税专用发票' : invoiceDetailsMonthly.type == 2 ? '增值税普通发票' : invoiceDetailsMonthly.type == 3 ? '其他发票' : 'err'}}{{list.monthStatistics.num}}张，金额{{list.monthStatistics.totalAmount ? list.monthStatistics.totalAmount : 0}}元</p>
    </div>
    <p class="h35f14 f4 margin_top_16">
      <span>所开的发票具体如下：</span>
    </p>
    <ul class="ty-fff" v-if="list.financeInvoiceDetails">
      <li v-for="(item,index) in list.financeInvoiceDetails" :key="index">
        <div>
          <span>
            <span class="padding-r-5">发票号码</span>
            {{ item.invoiceNo }}
          </span>
          <span>
            <span class="padding-r-5">金额</span>
            {{ item.amount }}元
          </span>
          <span>
            <span class="padding-r-5">发票日期</span>
            {{ new
          Date(item.operateDate).format('yyyy-MM-dd')}}
          </span>
          <span>
            <span class="padding-r-5">开给</span>
            {{ item.receiveCorp }}
          </span>
          <p>
            <span class="padding-r-5">
              开票申请
            </span>
            <span class="padding-r-5">{{ item.applicantName }}</span><span>{{ new
          Date(item.applicationTime).format('yyyy-MM-dd hh:mm:ss') }}</span>
          </p>
        </div>
        <div @click="rou(item.id)" class="ic">
          <el-icon>
            <arrow-right/>
          </el-icon>
        </div>
      </li>
    </ul>
  </div>
</template>

<script>
import {getInvoiceListMonth} from '@/api/financeBill'

export default {
  name: "invoiceDetailsList",
  data() {
    return {
      list: {},
      invoiceDetailsMonthly: {}
    }
  },
  mounted() {
    let invoiceDetailsMonthly = localStorage.getItem('invoiceDetailsMonthly');
    this.invoiceDetailsMonthly = JSON.parse(invoiceDetailsMonthly);
    console.log(invoiceDetailsMonthly, 'invoiceDetailsMonthly')
    this.getList(this.invoiceDetailsMonthly)
  },
  methods: {
    rou(id) {
      this.$router.push({path: '/invoiceDetails', query: {id: id}})
    },
    handleCustomClose() {
      this.$router.push("/caikuai")
    },
    getList(invoiceDetailsMonthly) {
      const params = {
        type: invoiceDetailsMonthly.type,
        beginDate: new Date(invoiceDetailsMonthly.beginDate).format('yyyy-MM-dd'),
        endDate: new Date(invoiceDetailsMonthly.endDate).format('yyyy-MM-dd')
      }
      getInvoiceListMonth(params).then(res1 => {
        this.list = res1.data.data
        console.log(res1.data.data, 'res1.data.data')
      }).catch(error => {
        console.error('Error:', error);
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";

ul {
  li {
    padding: 10px 15px;
    display: flex;
    align-items: center;

    > div:first-child {
      width: 90%;
      display: flex;
      flex-wrap: wrap;
      line-height: 25px;

      > span {
        width: 50%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    > div:last-child {
      width: 10%;
      height: 100%;
      display: flex;
      justify-content: end;
    }
  }

  > li:nth-of-type(even) {
    background-color: $tyBounceColorGray;
  }
}

.h35f14 {
  padding: 2px 15px;
}

.sticky {
  line-height: 22px;
}
</style>