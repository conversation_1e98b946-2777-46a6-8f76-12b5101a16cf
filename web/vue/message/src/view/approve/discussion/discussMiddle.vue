<template>
  <div id="forumLaunchApproval">
    <TY_NavTop title="讨论发起申请" isSearch="true" @toggleList="search"></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待审批" name="1" closable='false' :msgCount="approvalHandleCount">
        <div class="tip">有待审批的讨论主题有如下{{toHandle.length}}个。</div>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id, 'approveMiddle1')" v-for="(item, index) in toHandle" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.title}}</div>
                <div style="margin-left: 20px">讨论发起人 {{item.createName}} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="已批准" name="2" closable='false'>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id, 'approveMiddle2')" v-for="(item, index) in toApprove" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.title}}</div>
                <div style="margin-left: 20px">讨论发起人 {{item.createName}} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>

<script>
  export default {
    name: 'forumLaunchApproval',
    data () {
      return {
        toHandle: [],
        toApprove: [],
        listenersUid: []
      }
    },
    computed: {
      approvalHandleCount: function () {
        return this.toHandle.length
      }
    },
    created () {
      let that = this
      this.axios.post('../../../forum/getApproveForum.do')
        .then(function (response) {
          console.log(response)
          let getData = response.data.data
          that.toHandle = getData.listForumByApply
          that.toApprove = getData.listForumByApprove
        })
        .catch(function (error) {
          console.log(error)
        })
      this.listenersUid = [
        this.sphdSocket.subscribe('forumLaunchApproval', function (data) {
          console.log('approvalForumPost user:' + data)
          let getData = JSON.parse(data)
          let personnelReimburse = getData.forumPost
          let operate = Number(getData.operate)
          if (operate > 0) {
            that.toHandle.unshift(personnelReimburse)
          } else if (operate < 0) {
            let reimburseId = personnelReimburse.id
            let _index = -1
            that.toHandle.forEach(function (item, index) {
              if (item.id === reimburseId) {
                _index = index
              }
            })
            if (_index > -1) {
              that.toHandle.splice(_index, 1)
            }
          }
        }, null, 'user'),
        this.sphdSocket.subscribe('forumLaunchApprove', function (data) {
          console.log('approvalForumPost user:' + data)
          let getData = JSON.parse(data)
          let personnelReimburse = getData.forumPost
          let operate = Number(getData.operate)
          if (operate > 0) {
            that.toApprove.unshift(personnelReimburse)
          } else if (operate < 0) {
            let reimburseId = personnelReimburse.id
            let _index = -1
            that.toApprove.forEach(function (item, index) {
              if (item.id === reimburseId) {
                _index = index
              }
            })
            if (_index > -1) {
              that.toApprove.splice(_index, 1)
            }
          }
        }, null, 'user')
      ]
      console.log(that.sphdSocket.org.id)
    },
    destroyed: function () {
      let _this = this
      this.listenersUid.forEach(function (item) {
        _this.sphdSocket.unsubscribe(item)
      })
    },
    methods: {
      jump: function (id, mark) {
        this.$router.push({
          path: `/discussDetail/${id}/${mark}`
        })
      },
      search: function () {
        this.$router.push({
          path: `/discussQuery/approveMiddle`
        })
      }
    }
  }
</script>
