package cn.sphd.miners.modules.material.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.material.entity.SrmSupplier;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2016/9/19.
 */
public interface SrmSupplierDao extends IBaseDao<SrmSupplier,Serializable> {

    List<SrmSupplier> getAll();

    List<SrmSupplier>  getSuppliersByDate(Date date, Integer oid);

    List<SrmSupplier>  getChangeSuppliers(Date date, Integer oid);
}
