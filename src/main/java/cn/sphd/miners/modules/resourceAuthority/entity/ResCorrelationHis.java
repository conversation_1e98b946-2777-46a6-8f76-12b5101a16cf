package cn.sphd.miners.modules.resourceAuthority.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2022-01-28 
 */

@Entity
@Table ( name ="t_resource_correlation_history" )
public class ResCorrelationHis implements Serializable {

	/**
	 * ID
	 */
	@Id
	@Column(name="id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * 关联ID
	 */
   	@Column(name = "correlation", nullable=true , unique=false )
	private Integer correlation;

	/**
	 * 机构ID
	 */
	@Column(name = "org", nullable=true , unique=false )
	private Integer org;

	/**
	 * 附件ID
	 */
	@Column(name = "attachment", nullable=true , unique=false )
	private Integer attachment;

	/**
	 * 资源ID
	 */
   	@Column(name = "resource", nullable=true , unique=false )
	private Integer resource;

	/**
	 * 资源历史ID
	 */
   	@Column(name = "resource_history", nullable=true , unique=false )
	private Integer resourceHistory;

	/**
	 * 附件历史ID
	 */
   	@Column(name = "attachment_history", nullable=true , unique=false )
	private Integer attachmentHistory;

	/**
	 * 备注
	 */
	@Column(name = "memo", length=255, nullable=true , unique=false )
	private String memo;

	/**
	 * 创建人id
	 */
	@Column(name = "creator", nullable=true , unique=false )
	private Integer creator;

	/**
	 * 创建人
	 */
	@Column(name = "create_name", length=100, nullable=true , unique=false )
	private String createName;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date", nullable=true , unique=false )
	private Date createDate;

	/**
	 * 修改人id
	 */
	@Column(name = "updator", nullable=true , unique=false )
	private Integer updator;

	/**
	 * 修改人
	 */
	@Column(name = "update_name", length=100, nullable=true , unique=false )
	private String updateName;

	/**
	 * 修改时间
	 */
	@Column(name = "update_date", nullable=true , unique=false )
	private Date updateDate;

	/**
	 * 对应消息表的id
	 */
	@Column(name = "message_id", nullable=true , unique=false )
	private Integer messageId;

	/**
	 * 操作:1-增,2-删,3-改
	 */
	@Column(name = "operation", length=1, nullable=true , unique=false )
	private String operation;

	/**
	 * 修改前记录ID
	 */
	@Column(name = "previous_id", nullable=true , unique=false )
	private Integer previousId;

	/**
	 * 版本号,每次修改+1
	 */
	@Column(name = "version_no", nullable=true , unique=false )
	private Integer versionNo;



	@Transient
	private ResEntity resEntity;

	@Transient
	private ResAtt resAtt;


	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getCorrelation() {
		return correlation;
	}

	public void setCorrelation(Integer correlation) {
		this.correlation = correlation;
	}

	public Integer getOrg() {
		return org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getAttachment() {
		return attachment;
	}

	public void setAttachment(Integer attachment) {
		this.attachment = attachment;
	}

	public Integer getResource() {
		return resource;
	}

	public void setResource(Integer resource) {
		this.resource = resource;
	}

	public Integer getResourceHistory() {
		return resourceHistory;
	}

	public void setResourceHistory(Integer resourceHistory) {
		this.resourceHistory = resourceHistory;
	}

	public Integer getAttachmentHistory() {
		return attachmentHistory;
	}

	public void setAttachmentHistory(Integer attachmentHistory) {
		this.attachmentHistory = attachmentHistory;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getCreator() {
		return creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getUpdator() {
		return updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Integer getMessageId() {
		return messageId;
	}

	public void setMessageId(Integer messageId) {
		this.messageId = messageId;
	}

	public String getOperation() {
		return operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public Integer getPreviousId() {
		return previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}

	public ResEntity getResEntity() {
		return resEntity;
	}

	public void setResEntity(ResEntity resEntity) {
		this.resEntity = resEntity;
	}

	public ResAtt getResAtt() {
		return resAtt;
	}

	public void setResAtt(ResAtt resAtt) {
		this.resAtt = resAtt;
	}
}
