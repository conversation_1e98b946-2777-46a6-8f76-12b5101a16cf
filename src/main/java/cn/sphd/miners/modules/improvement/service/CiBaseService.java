package cn.sphd.miners.modules.improvement.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.improvement.entity.TCiBase;
import cn.sphd.miners.modules.system.entity.User;

import java.util.HashMap;
import java.util.List;

/**
 * Created by 朱思旭 on 2018/5/21.
 */
public interface CiBaseService {

    //新增一条持续改进
    void addImprovement(User user, TCiBase tCiBase);

    //根据状态获取持续改进
    List<TCiBase> getCiBase(User user, TCiBase tCiBase, Integer role, PageInfo pageInfo);

    //批准驳回持续改进
    void updateCiBase(User user, TCiBase tCiBase);

    //核心人物直接结案
    void centreSettleCase(User user, TCiBase tCiBase);

    //查看案子的详细信息
    HashMap getCiBaseMessage(Integer id, Integer role, Integer userId);

    //跟换持续立案者
    HashMap upCreator(Integer id, Integer userID, String userName);
}
