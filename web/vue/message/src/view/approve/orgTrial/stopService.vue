<template>
  <div id="stopService">
    <TY_NavTop title="暂停服务" isSearch="true" @toggleList="search"></TY_NavTop>
    <div class="container">
      <div class="nowTime">以下机构被提出了暂停服务的申请，有待审批</div>
      <div>
        <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in toApprove" v-bind:key="index">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <div>{{item.fullName}}</div>
              <div class="text-right">{{item.createName}} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            </div>
          </div>
          <div class="ui-cell__ft"></div>
        </a>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'stopService',
  data () {
    return {
      toApprove: [],
      listenersUid: []
    }
  },
  created () {
    let that = this
    this.axios.post('../../../special/getOrgOutOfServiceApprovals.do')
      .then(function (response) {
        console.log(response)
        let getData = response.data.data
        that.toApprove = getData
      })
      .catch(function (error) {
        console.log(error)
      })
  },
  methods: {
    jump: function (id) {
      parent.location.href = '../../../special/toTryApplyInfo.do?type=52&id=' + id
    },
    search: function () {
      this.$router.push({
        path: `/stopServiceQuery/approve`
      })
    }
  }
}
</script>
