package cn.sphd.miners.modules.commodity.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.commodity.dao.PdCustomerAddressHistoryDao;
import cn.sphd.miners.modules.commodity.entity.PdCustomerAddress;
import cn.sphd.miners.modules.commodity.entity.PdCustomerAddressHistory;
import org.springframework.stereotype.Repository;
import java.io.Serializable;


@Repository
public class PdCustomerAddressHistoryDaoImpl extends BaseDao<PdCustomerAddressHistory,Serializable> implements PdCustomerAddressHistoryDao {
    @Override
    public void insert(PdCustomerAddress pdCustomerAddress) {
        //添加历史记录
        PdCustomerAddressHistory ch=new PdCustomerAddressHistory();
        ch.setOperation(pdCustomerAddress.getOperation());
        ch.setCustomerAddress(pdCustomerAddress.getId());
        ch.setCustomer(pdCustomerAddress.getCustomer());
        ch.setAddress(pdCustomerAddress.getAddress());
        ch.setContact(pdCustomerAddress.getContact());
        ch.setPostcode(pdCustomerAddress.getPostcode());
        ch.setMobile(pdCustomerAddress.getMobile());
        ch.setEnabled(pdCustomerAddress.getEnabled());
        ch.setEnabledTime(pdCustomerAddress.getEnabledTime());
        ch.setType(pdCustomerAddress.getType());
        ch.setCreateDate(pdCustomerAddress.getCreateDate());
        ch.setCreateName(pdCustomerAddress.getCreateName());
        ch.setCreator(pdCustomerAddress.getCreator());
        ch.setUpdateDate(pdCustomerAddress.getUpdateDate());
        ch.setUpdateName(pdCustomerAddress.getUpdateName());
        ch.setUpdator(pdCustomerAddress.getUpdator());
        ch.setRegionCode(pdCustomerAddress.getRegionCode());
        ch.setRequirements(pdCustomerAddress.getRequirements());
        save(ch);
    }

    @Override
    public PdCustomerAddressHistory insertNoCustomerAddress(PdCustomerAddress pdCustomerAddress) {
        //添加历史记录
        PdCustomerAddressHistory ch=new PdCustomerAddressHistory();
        ch.setOperation(pdCustomerAddress.getOperation());
        //ch.setCustomerAddress(pdCustomerAddress.getId());
        ch.setCustomer(pdCustomerAddress.getCustomer());
        ch.setAddress(pdCustomerAddress.getAddress());
        ch.setContact(pdCustomerAddress.getContact());
        ch.setPostcode(pdCustomerAddress.getPostcode());
        ch.setMobile(pdCustomerAddress.getMobile());
        ch.setEnabled(pdCustomerAddress.getEnabled());
        ch.setEnabledTime(pdCustomerAddress.getEnabledTime());
        ch.setType(pdCustomerAddress.getType());
        ch.setCreateDate(pdCustomerAddress.getCreateDate());
        ch.setCreateName(pdCustomerAddress.getCreateName());
        ch.setCreator(pdCustomerAddress.getCreator());
        ch.setUpdateDate(pdCustomerAddress.getUpdateDate());
        ch.setUpdateName(pdCustomerAddress.getUpdateName());
        ch.setUpdator(pdCustomerAddress.getUpdator());
        ch.setRegionCode(pdCustomerAddress.getRegionCode());
        ch.setRequirements(pdCustomerAddress.getRequirements());
        save(ch);
        return ch;
    }
}
