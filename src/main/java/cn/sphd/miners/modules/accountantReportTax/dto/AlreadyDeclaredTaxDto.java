package cn.sphd.miners.modules.accountantReportTax.dto;

import cn.sphd.miners.modules.finance.entity.FinanceAccountBillImage;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class AlreadyDeclaredTaxDto implements Serializable {
    private static final long serialVersionUID = 4561661954607175481L;

    private Integer detailId;//t_accountant_tax_detail 表的 ID
    private Integer accountDetail;// 财务明细 ID
    private String taxName;//税种名
    private String payType;//支出方式
    private BigDecimal factAmount;//实缴金额
    private BigDecimal planAmount;//应缴金额
    private String belongsBegin;//税款所属时期的开始日期
    private String belongsEnd;//税款所属时期的截止日期
    private String updateName;//最后的编辑人
    private Date payTime;//实际缴纳的日期
    private Date lastEditTime;//最后编辑的日期
    String method;//method:0-初始资金 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账
    List<FinanceAccountBillImage> images;
    /*
    凭证图片
    * financeAccountBillImages:图片信息{
                    id：id
                    org：机构ID
                    accountDetail：帐户明细ID
                    uplaodPath：文件上传路径
                }
    * */

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public BigDecimal getPlanAmount() {
        return planAmount;
    }

    public void setPlanAmount(BigDecimal planAmount) {
        this.planAmount = planAmount;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public List<FinanceAccountBillImage> getImages() {
        return images;
    }

    public void setImages(List<FinanceAccountBillImage> images) {
        this.images = images;
    }

    public Integer getAccountDetail() {
        return accountDetail;
    }

    public void setAccountDetail(Integer accountDetail) {
        this.accountDetail = accountDetail;
    }

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    public String getTaxName() {
        return taxName;
    }

    public void setTaxName(String taxName) {
        this.taxName = taxName;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public BigDecimal getFactAmount() {
        return factAmount;
    }

    public void setFactAmount(BigDecimal factAmount) {
        this.factAmount = factAmount;
    }

    public String getBelongsBegin() {
        return belongsBegin;
    }

    public void setBelongsBegin(String belongsBegin) {
        this.belongsBegin = belongsBegin;
    }

    public String getBelongsEnd() {
        return belongsEnd;
    }

    public void setBelongsEnd(String belongsEnd) {
        this.belongsEnd = belongsEnd;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public Date getLastEditTime() {
        return lastEditTime;
    }

    public void setLastEditTime(Date lastEditTime) {
        this.lastEditTime = lastEditTime;
    }
}

