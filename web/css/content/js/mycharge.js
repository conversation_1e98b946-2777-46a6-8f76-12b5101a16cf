$(function(){
	App.init(); 
	getChargeList(1 , 5);
	
});
// 审批列表
function getChargeList(cur , perNum){
	$.ajax({
		url:"site_checkApply.action",
		data:{"pernum":perNum , "curpage":cur },
		type:"post",
		timeout:5000 ,
		beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
		success:function(data){
			data = eval('('+ data+')');
			var status = data["status"];//0权限不符，1成功，2.session丢失
			if(status == 2){
				alert("登陆失效，请重新登陆！");
				location.href = "site_loginpage.action"; 
			}else if(status == 0){
				alert("您的登陆与权限不符，请重新登陆！");
				location.href = "site_loginpage.action";
			}else if(status == 1){
				var list = data["list"];
				var cur = data["curpage"];
				var countall = data["countpage"];
				$("#chargeTbl").children(":eq(1)").html("");
				$("#ye").html("");
				setPage($("#ye"),cur,countall,"charge");
				if(list.length > 0){
					for(var i=0 ; i<list.length ; i++){
						 var content = list[i]["content"];
						 var id = list[i]["id"];
						 var valid = list[i]["valid"];
						 var category = list[i]["category"];
						 var content_id = list[i]["content_id"];
						 var create_date = list[i]["create_date"];
						 var reason = list[i]["reason"];
						 var name = list[i]["name"];
						 var create_name = list[i]["create_name"];
						 var creator = list[i]["creator"];
						 var chargeTime = list[i]["operate_time"]; // 审批时间
						 var chargeName = list[i]["operator_name"]; // 审批人
						 var chargeTip = list[i]["approve_desc"];  // 审批意见
						 var status = list[i]["approve_status"]; // 审批状态	
						 var approvalID = list[i]["approvalID"]; // 审批状态	
						 
						 name = getNull(name);
						 create_date = getNull(create_date);
						 create_name = getNull(create_name);
						 reason = getNull(reason);
						 chargeTime = getNull(chargeTime);
						 chargeName = getNull(chargeName);
						 chargeTip = getNull(chargeTip);
						 content = getNull(content);
						 status = getNull(status);
						 
						if(status == 0 ){  
							status = "<a class='nosolve' onclick='chargeBtn($(this) , chargeCase.chargeYes )'>[ 批准 ]</a>" +
								"<a class='nosolve' onclick='chargeBtn($(this) , chargeCase.chargeNo  )'>[ 驳回 ]</a>";  
						} 
						else if(status == 1 ){  status = "<span class='solve'> 已批准 </span>";  } 
						else if(status == 2 ){  status = "<span class='unsolve'> 已驳回 </span>";  } 
						
						var str = "<tr>" +
								"<td>"+ name +"</td>" +
								"<td>"+ create_date +"</td>" +
								"<td>"+ create_name +"</td>" +
								"<td>"+ reason +"</td>" +
								"<td>"+ chargeTime +"</td>" +
								"<td>"+ chargeName +"</td>" +
								"<td>"+ chargeTip +"</td>" +
								"<td><span onclick='privacyDetails($(this))' class='hand'> [ 查看 ] </span><span class='hd'>"+ content_id +"</span></td>" +
								"<td title='"+ approvalID +"'>"+ status +"</td>" +
								"</tr>";
						
						$("#chargeTbl").append(str);
					}
				}
				
			}
			
			
		},
		error:function(){ alert("连接错误，请稍后重试！"); } ,
		complete:function(){ chargeClose(1) ;  }
	});
}

// 返回，显示主页面
function ConToggle(){
	$("#addOp_con").val("");
	$("#privacyCon").show().siblings().hide();
}
 
// 查看详情
function privacyDetails(obj){ 
	editChargeObj = obj.parent().next();
	var id = obj.siblings(".hd").html();
	var approID = obj.parent().next().attr("title");
	$("#modelCharge").html(obj.parent().siblings(":eq(0)").html());
	$.ajax({
		url:"site_lookapprpval.action",
		data:{ "id":approID },
		type:"post",
		timeout:5000 ,
		beforeSend:function(){ loading.open() ;  } ,
		success:function(data){
			data = eval('('+ data +')');
			var status = data["status"];
			if(status == 1){
				var listcon = data["listapp"];
				
				var id = listcon[0]["id"];
				var type = listcon[0]["type"];
				var name = listcon[0]["name"];
				var image = listcon[0]["image"];
				var content = listcon[0]["content"];
				var category = listcon[0]["category"];
				var content_id = listcon[0]["content_id"];
				var create_date = listcon[0]["create_date"];
				var reason = listcon[0]["reason"];
				var create_name = listcon[0]["create_name"];
				var chargeTime = listcon[0]["operate_time"]; // 审批时间
				var chargeName = listcon[0]["operator_name"]; // 审批人
				var chargeTip = listcon[0]["approve_desc"];  // 审批意见
				var status = listcon[0]["approve_status"]; // 审批状态	
				 
				 content = getNull(content);
				 category = getNull(category);
				 content_id = getNull(content_id);
				 create_date = getNull(create_date);
				 reason = getNull(reason);
				 create_name = getNull(create_name);
				 chargeTime = getNull(chargeTime);
				 chargeName = getNull(chargeName);
				 chargeTip = getNull(chargeTip);
				 status = getNull(status);
				 if( status==1|| status==2 ){ $(".answer_content").hide(); }else{ $(".answer_content").show(); }
				 $("#chargedetail_id").html(content_id);
				 $("#charge_model").html(name);
				 $("#charge_creaDate").html(create_date);
				 $("#charge_reason").html(reason);
				 $("#charge_creatName").html(chargeName);
				 $("#approID").html(approID);
				 if(category == 1){      // Scan
					 console.log("${pageContext.request.contextPath }"+ image);
					 console.log( image);
					 var str = "<img alt='"+ type +"' src='"+ image +"'>" ;
					 $("#charge_content").html(str);
					 
				 }else if(category == 5){ // Help
					 var list = data["list"];
					 var countpage = data["countpage"];
					 var curpage = data["curpage"];
					 $("#ye1").html("");
					 
					 var str = " "; // 使用帮助的所有详情 	
					 if(list.length > 0){
						 for(var i=0; i<list.length; i++){
							 var det_reson = list[i]["reason"]; 
							 var det_content = list[i]["content"]; 
							 str += "<div class='answerItem'>" +
							 		"<div class='as_ttl'>"+ det_reson +"</div>" +
							 		"<div class='as_con'>"+ det_content +"</div></div>"
						 }
					 }
					 
					 $("#charge_content").html(str);
					 
				 }else{  // Normal
					 var str = content;
					 $("#charge_content").html(str);
				 }
				 
				$("#privacyDetails").show().siblings().hide();
			}
			
			
		},
		error:function(){
			alert("连接错误。请稍后重试！");
		} ,
		complete:function(){ loading.close() ;  }
	});

}

// 审批
var editChargeObj = null ;
function chargeBtn(obj , num){
	bounce.show($("#chargeCon"));
	editChargeObj = obj.parent(); ;
	var id = obj.parent().attr("title");
	var status = num ; 
	var charge_sta = "";
	if(status == 1){ charge_sta = "批准";  }
	else if(status == 2){ charge_sta = "驳回"; }
	$("#charge_tip").val("");
	$("#charge_sta").html(charge_sta);
	$("#charge_id").html(id);
	$("#charge_status").html(status);
}

// 列表中的批准
function charge(){
	var id = $("#charge_id").html();
	var status = $("#charge_status").html();
	var app_opinion = $("#charge_tip").val();
	doCharge(id, status, app_opinion);
	bounce.cancel();
	$("#charge_tip").val("");
}

//  详情中的审批
function detailsCharge(num){
	var id = $("#approID").html();
	var app_opinion = $("#addOp_con").val();
	doCharge(id, num, app_opinion);
	$("#privacyCon").show().siblings().hide();
}

function doCharge(id, status, app_opinion){
	if(confirm("确定进行该审批操作")){
		$.ajax({
			url:"site_updateStatus.action",
			data:{ "id":id, "status":status, "app_opinion":app_opinion   },
			type:"post",
			timeout:5000 ,
			beforeSend:function(){ loading.open() ;  } ,
			success:function(data){
				data = eval('('+ data +')');
				var status = data["status"];
				if(status == 1){ 
					var listapp = data["listapp"];
					var listcon = data["listcon"];
					
					var chargeTime = listapp[0]["operate_time"]; // 审批时间
					var chargeName = listapp[0]["operator_name"]; // 审批人
					var chargeTip = listapp[0]["approve_desc"];  // 审批意见
					var status = listapp[0]["approve_status"]; // 审批状态	
					
					editChargeObj.siblings(":eq(4)").html(chargeTime);
					editChargeObj.siblings(":eq(5)").html(chargeName);
					editChargeObj.siblings(":eq(6)").html(chargeTip);
					if(status == 1 ){  status = "<span class='solve'> 已批准 </span>";  } 
					else if(status == 2 ){  status = "<span class='unsolve'> 已驳回 </span>";  } 
					editChargeObj.html(status);
					
				}
				else if(status == 0){ alert("操作失败！"); }
				
			},
			error:function(){
				alert("连接错误，请稍后重试！");
			} ,
			complete:function(){ loading.close() ;  }
		});
	}
	
}

function cancleBoun(){
	$('.close').click();
	$("#charge_sta").html("");
	$("#charge_id").html("");
	$("#charge_status").html("");
	$("#charge_tip").val("");
}












	