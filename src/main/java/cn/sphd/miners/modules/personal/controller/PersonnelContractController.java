package cn.sphd.miners.modules.personal.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.modules.personal.entity.PersonnelContract;
import cn.sphd.miners.modules.personal.entity.PersonnelContractHistory;
import cn.sphd.miners.modules.personal.service.PersonnelContractService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("perCon")
public class PersonnelContractController {

    @Autowired
    PersonnelContractService personnelContractService;
    @Autowired
    UserService userService;

    //新增劳动合同
    @ResponseBody
    @RequestMapping("/insertPersonnelContract.do")
    public JsonResult insertPersonnelContract(User user, PersonnelContract personnelContract,
                                              String contractSignTime, String contractStartTime,
                                              String contractEndTime, String contractBaseImages) {
        Map<String, Object> map = new HashMap<String, Object>();
        PersonnelContract contract = personnelContractService.insertPersonnelController(user,personnelContract,contractSignTime,contractStartTime,contractEndTime,contractBaseImages);
        map.put("state", 1);
        map.put("contract", contract);
        return new JsonResult(1,map);
    }

    //修改劳动合同
    @ResponseBody
    @RequestMapping("/updatePersonnelContract.do")
    public JsonResult updatePersonnelContract(User user, PersonnelContract personnelContract,
                                             String contractSignTime, String contractStartTime,
                                             String contractEndTime, String contractBaseImages){
        Map<String, Object> map = new HashMap<>();
        Integer state = personnelContractService.upPerCon(user,personnelContract,contractSignTime,contractStartTime,contractEndTime,contractBaseImages);
        map.put("state", state);
        return new JsonResult(1, map);
    }

    //续约劳动合同
    @ResponseBody
    @RequestMapping("/renewalPersonnelContract.do")
    public JsonResult renewalPersonnelContract(User user, PersonnelContract personnelContract,
                                               String contractSignTime, String contractStartTime,
                                               String contractEndTime, String contractBaseImages){
        Map<String, Object> map = new HashMap<>();
        Integer state = personnelContractService.renewalPerCon(user,personnelContract,contractSignTime,contractStartTime,contractEndTime,contractBaseImages);
        map.put("state", state);
        return new JsonResult(1, map);
    }

    //终止劳动合同
    @ResponseBody
    @RequestMapping("/terminatePersonnelContract.do")
    public JsonResult terminatePersonnelContract(Integer userID,User user) {
        Map<String, Object> map = new HashMap<>();
        Integer state = personnelContractService.terminatePerCon(userID);
        map.put("state", 1);
        userService.forLeaving(userID,null,user);
        return new JsonResult(1, map);
    }

    //获取某个合同的全部签约记录
    @ResponseBody
    @RequestMapping("/getPersonnelContractSignRecord.do")
    public JsonResult getPersonnelContractSignRecord(Integer userID) {
        List<PersonnelContract> list = personnelContractService.getPerConSignRecord(userID);
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return new JsonResult(1, map);
    }

    //获取劳动合同详情
    @ResponseBody
    @RequestMapping("/getPersonnelContractMessage.do")
    public JsonResult getPersonnelContractMessage(Long id) {
        Map<String, Object> map = personnelContractService.perConMes(id);
        return new JsonResult(1, map);
    }

    //获取合同的修改记录
    @ResponseBody
    @RequestMapping("/personnelContractHistory.do")
    public JsonResult personnelContractHistory(Long id) {
        List<PersonnelContractHistory> list = personnelContractService.getListPerConHis(id);
        Map<String, Object> map = new HashMap<>();
        map.put("list", list);
        return new JsonResult(1, map);
    }

    //获取历史合同详情
    @ResponseBody
    @RequestMapping("/getPersonnelContractHisMes.do")
    public JsonResult getPersonnelContractHisMes(Long contractHisId) {
        Map<String, Object> map = personnelContractService.perConHisMes(contractHisId);
        return new JsonResult(1, map);
    }

}
