<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.accountant.mapper.BuildAccountMapper" >

    <select id="getBaseSubjectsByOrg" resultType="accountSubject">
        SELECT
        a.id,a.name, a.subject, a.max_child_subjects AS maxChildSubjects, a.balance_direction AS balanceDirection,
        a.level, a.category, a.state, a.parent,a.previous_balance AS previousBalance, s.name AS categoryname,a.org,
        a.end_direction AS  endDirection,a.quantity_assisting_accounting AS  quantityAssistingAccounting,a.useable,
        a.beginning_quantity AS beginningQuantity,a.credit_accumulative_quantity AS creditAccumulativeQuantity,
        a.debit_accumulative_quantity AS debitAccumulativeQuantity,a.balance,a.quantity,a.credit_accumulative AS creditAccumulative,a.debit_accumulative AS debitAccumulative,
        a.beginning_balance as beginningBalance,a.beginning_direction AS beginningDirection
        FROM t_accountant_subject a, t_accountant_subject_category s
        <where>
            a.category = s.id AND a.org = #{org} AND a.max_child_subjects=0 and a.state=1
            <if test="balance > 0">
                AND (a.balance!=0 OR a.credit_accumulative!=0 OR a.debit_accumulative!=0 OR a.beginning_balance!=0)
            </if>
        </where>
        ORDER BY a.`subject`
    </select>

    <select id="getBuildAccountState" resultType="link">
        SELECT key_,value_,update_name as updateName,update_date as updateDate FROM `t_accountant_setting` WHERE org=#{org} AND key_='operate_state';
    </select>

    <select id="getParentSubject" resultType="accountSubject">
        SELECT
        a.id,a.name, a.subject, a.max_child_subjects AS maxChildSubjects, a.balance_direction AS balanceDirection,
        a.level, a.category, a.state, a.parent,a.previous_balance AS previousBalance, s.name AS categoryname,a.org,
        a.end_direction AS  endDirection,a.quantity_assisting_accounting AS  quantityAssistingAccounting,a.useable,
        a.beginning_quantity AS beginningQuantity,a.credit_accumulative_quantity AS creditAccumulativeQuantity,
        a.debit_accumulative_quantity AS debitAccumulativeQuantity,a.balance,a.quantity,a.credit_accumulative AS creditAccumulative,a.debit_accumulative AS debitAccumulative,
        a.beginning_balance as beginningBalance,a.beginning_direction AS beginningDirection
        FROM t_accountant_subject a, t_accountant_subject_category s
        WHERE a.subject=#{parent} and a.org=#{org} and a.category = s.id;

    </select>
    <select id="checkRebuildFlag" resultType="link">
        SELECT value_ FROM `t_accountant_setting` WHERE org=#{org} AND key_='rebuild_label';
    </select>
    <select id="getTempCashFlowByOrg" resultType="tempcash">
        SELECT id, org, cash_flow_code AS cashFlowCode, cash_flow_name AS cashFlowName, accumulative, amount FROM `t_accountant_cash` WHERE org=#{org};
    </select>

    <select id="getSettingByKey" resultType="link">
        SELECT value_ FROM `t_accountant_setting` WHERE org=#{org} AND key_=#{key_};
    </select>
    <select id="enableAccountantModule" resultType="java.lang.String">
        SELECT mid FROM `t_sys_org_popedom` WHERE org=#{org} AND mid=#{mid};
    </select>
    <select id="checkSettingKey" resultType="link">
        SELECT id FROM `t_accountant_setting` WHERE org=#{org} AND key_=#{key_};
    </select>

    <update id="setBuildAccountState">
        UPDATE `t_accountant_setting` SET value_=#{state} WHERE org=#{org} AND key_='operate_state';
    </update>

    <update id="saveBuildAccount">
        UPDATE `t_accountant_subject`
        <set>
            <if test="quantity != null" >
                quantity=#{quantity},
            </if>
            <if test="beginningBalance != null" >
                beginning_balance=#{beginningBalance},
            </if>
                <if test="beginningDirection != null" >
                    beginning_direction=#{beginningDirection},
                </if>
                <if test="beginningQuantity != null" >
                    beginning_quantity=#{beginningQuantity},
                </if>
                <if test="creditAccumulative != null" >
                    credit_accumulative=#{creditAccumulative},
                </if>
                <if test="creditAccumulativeQuantity != null" >
                    credit_accumulative_quantity=#{creditAccumulativeQuantity},
                </if>
                <if test="debitAccumulative != null" >
                    debit_accumulative=#{debitAccumulative},
                </if>
                <if test="debitAccumulativeQuantity != null" >
                    debit_accumulative_quantity=#{debitAccumulativeQuantity},
                </if>
                <if test="balance != null" >
                    balance=#{balance},
                </if>
                <if test="endDirection != null" >
                    end_direction=#{endDirection},
                </if>
            </set>
             WHERE id=#{id};
    </update>

    <update id="clearParentSubject">
        UPDATE `t_accountant_subject` SET beginning_balance=0,
        beginning_quantity=0,
        credit_accumulative=0,
        credit_accumulative_quantity=0,
        debit_accumulative=0,
        debit_accumulative_quantity=0 WHERE org=#{org};
    </update>
    <update id="buildFinishCashFlow">
       UPDATE  `t_accountant_cash_flow` SET state='3' WHERE org=#{org};
    </update>
    <update id="buildFinishBalanceSheet">
        UPDATE `t_accountant_balance_sheet` SET state='3' WHERE org=#{org};
    </update>
    <update id="buildFinishProfitSheet">
        UPDATE `t_accountant_profit_statement` SET state='3' WHERE org=#{org};
    </update>
    <update id="updateSettingByOrg">
        UPDATE `t_accountant_setting` SET value_=#{value_},update_name=#{update_name},update_date=#{update_date} WHERE key_=#{key_} AND org=#{org};
    </update>
    <update id="setInitialBuildMonth">
        UPDATE `t_accountant_setting` SET value_=#{value_} WHERE key_='inital_month' AND org=#{org};
    </update>
    <update id="clearAllParentSubject">
        UPDATE `t_accountant_subject`
        SET quantity = 0,
         beginning_quantity = 0,
         credit_accumulative_quantity = 0,
         debit_accumulative_quantity = 0,
         credit_accumulative = 0,
         debit_accumulative = 0,
         beginning_balance = 0,
         beginning_direction = '3',
         balance = 0,
         end_direction = '3'
        WHERE
            org = #{org}
        AND max_child_subjects &gt; 0;
    </update>
    <update id="buildFinishPeriod">
        UPDATE t_accountant_subject_peroid SET state=#{state}
    WHERE state='4' and (subject_id in (SELECT id FROM t_accountant_subject WHERE org=#{org}))
    </update>
    <update id="setOrgBuildAccountState">
        UPDATE `t_sys_org` SET account_state=#{state} WHERE id=#{org};
    </update>
    <update id="setOrgRebuildState">
        UPDATE `t_sys_org` SET rebuild_label=#{state} WHERE id=#{org};
    </update>

    <update id="initOrgBuildState">
        UPDATE `t_sys_org` SET account_state='0',rebuild_label='N' WHERE id=#{org};
    </update>

    <delete id="deleteOldData">
        DELETE p FROM t_accountant_subject_peroid p,t_accountant_subject s WHERE p.subject_id=s.id AND s.org=#{org} AND p.state=#{state};
    </delete>
    <delete id="deleteOldCashFlow">
        DELETE FROM `t_accountant_cash_flow` WHERE org=#{org} AND state='4';
    </delete>
    <delete id="deleteBalanceSheet">
        DELETE FROM `t_accountant_balance_sheet` WHERE org=#{org} AND state='4';
    </delete>
    <delete id="deleteProfitSheet">
        DELETE FROM `t_accountant_profit_statement` WHERE org=#{org} AND state='4';
    </delete>
    <delete id="deletePeriodData">
        DELETE p FROM `t_accountant_subject_peroid` p,`t_accountant_subject` s WHERE p.subject_id=s.id AND s.org=#{org};
    </delete>
    <!--<delete id="deleteReport">-->
        <!--DELETE FROM `t_accountant_balance_sheet` WHERE org=#{org};-->
        <!--DELETE FROM `t_accountant_cash` WHERE org=#{org};-->
        <!--DELETE FROM `t_accountant_cash_history` WHERE org=#{org};-->
        <!--DELETE FROM `t_accountant_cash_flow` WHERE org=#{org};-->
        <!--DELETE FROM `t_accountant_profit_statement` WHERE org=#{org};-->
        <!--DELETE FROM `t_accountant_setting` WHERE org=#{org};-->
        <!--DELETE FROM `t_accountant_settle` WHERE org=#{org};-->
    <!--</delete>-->
    <delete id="deleteCashReport">
        DELETE FROM `t_accountant_cash` WHERE org=#{org};
    </delete>
    <delete id="deleteCashFlowReport">
        DELETE FROM `t_accountant_cash_flow` WHERE org=#{org};
    </delete>
    <delete id="deleteCashHistoryReport">
        DELETE FROM `t_accountant_cash_history` WHERE org=#{org};
    </delete>
    <delete id="deleteBalanceSheetReport">
        DELETE FROM `t_accountant_balance_sheet` WHERE org=#{org};
    </delete>
    <delete id="deleteProfitReport">
        DELETE FROM `t_accountant_profit_statement` WHERE org=#{org};
    </delete>
    <delete id="deleteSettle">
        DELETE FROM `t_accountant_settle` WHERE org=#{org};
    </delete>
    <delete id="deleteSetting">
        DELETE FROM `t_accountant_setting` WHERE org=#{org};
    </delete>
    <delete id="deleteSubject">
        DELETE FROM `t_accountant_subject` WHERE org=#{org} ;
    </delete>
    <delete id="deleteSubjectHistory">
        DELETE FROM `t_accountant_subject_history` WHERE org=#{org} ;
    </delete>

    <insert id="insertSettingKey">
        INSERT INTO `t_accountant_setting` (org,key_,value_) values (#{org},#{key_},#{value_})
    </insert>

</mapper>