/**
	* Copyright (c) minuteproject, <EMAIL>
	* All rights reserved.
	*
	* Licensed under the Apache License, Version 2.0 (the "License")
	* you may not use this file except in compliance with the License.
	* You may obtain a copy of the License at
	*
	* http://www.apache.org/licenses/LICENSE-2.0
	*
	* Unless required by applicable law or agreed to in writing, software
	* distributed under the License is distributed on an "AS IS" BASIS,
	* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
	* See the License for the specific language governing permissions and
	* limitations under the License.
	*
	* More information on minuteproject:
	* twitter @minuteproject
	* wiki http://minuteproject.wikispaces.com
	* blog http://minuteproject.blogspot.net
	*
*/
/**
	* template reference :
	* - Minuteproject version : 0.9.5
	* - name      : DomainEntityJPA2Annotation
	* - file name : DomainEntityJPA2Annotation.vm
	* - time      : 2016/09/12 ��Ԫ at 14:29:06 GMT+08:00
*/
package cn.sphd.miners.modules.commodity.entity;


import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 *
 * <p>Title: TPdMerchandiseHistory</p>
 *
 * <p>Description: Domain Object describing a TPdMerchandiseHistory entity</p>
 *
 */
@Entity (name="PdMerchandiseHistory")
@Table (name="t_pd_merchandise_history")

public class PdMerchandiseHistory implements Serializable {
    @Id @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;



    @Column(name="product_customer"  , nullable=true , unique=false)
    private Integer productCustomer;   //机构

    @Column(name="org"  , nullable=true , unique=false)
    private Integer org;   //机构

    @Column(name = "operation", length = 1, nullable = true, unique = false)
    private String operation;  // 1-增,2-删,3-修改基本信息,4-修改初始库存,5-修改最小库存',

    @Column(name="inner_sn"  , length=100 , nullable=true , unique=false)
    private String innerSn;   //内部图号

    @Column(name="outer_sn"  , length=100 , nullable=true , unique=false)
    private String outerSn;   //外部图号

    @Column(name="outer_name"  , length=100 , nullable=true , unique=false)
    private String outerName;   //外部名称

    @Column(name="customer_name"   , nullable=true , unique=false)
    private String customerName;   //客户名称


    @Column(name="mini_packing_mode"  , length=100 , nullable=true , unique=false)
    private String miniPackingMode;   //小包装方式

    @Column(name="mini_packing_material"  , length=100 , nullable=true , unique=false)
    private String miniPackingMaterial;   //小包装材料

    @Column(name="mini_packing_amount"   , nullable=true , unique=false)
    private Long miniPackingAmount;   //小包装数量

    @Column(name="outer_packing_mode"  , length=100 , nullable=true , unique=false)
    private String outerPackingMode;    //外包装方式

    @Column(name="outer_packing_material"  , length=100 , nullable=true , unique=false)
    private String outerPackingMaterial;   //外包装材料

    @Column(name="outer_packing_amount"   , nullable=true , unique=false)
    private Long outerPackingAmount;   //外包装数量

    @Column(name="unit_price"   , nullable=true , unique=false)
    private BigDecimal unitPrice;   //含税单价(增专)

    @Column(name="tax_rate"   , nullable=true , unique=false)
    private BigDecimal taxRate;  //税率

    @Column(name="unit_price_notax"   , nullable=true , unique=false)
    private BigDecimal unitPriceNotax;   //不含税单价(增专)


    @Column(name="unit_price_invoice"   , nullable=true , unique=false)
    private BigDecimal unitPriceInvoice;//开票单价(增通)


    @Column(name="unit_price_noinvoice"   , nullable=true , unique=false)
    private BigDecimal unitPriceNoinvoice;//不开票单价

    @Column(name="unit_price_reference"   , nullable=true , unique=false)
    private BigDecimal unitPriceReference;//参考单价

    @Column(name="contract_sn"  , length=100 , nullable=true , unique=false)
    private String contractSn;    //供货合同编号


    @Column(name="technical_principal"   , nullable=true , unique=false)
    private String technicalPrincipal;
    @Column(name="technical_principal_name"   , nullable=true , unique=false)
    private String technicalPrincipalName;
    @Column(name="process_dept"   , nullable=true , unique=false)
    private Integer processDept;
    @Column(name="process_dept_name"   , nullable=true , unique=false)
    private String processDeptName;
    @Column(name="phrase"   , nullable=true , unique=false)
    private String phrase;
    @Column(name="is_contract"   , nullable=true , unique=false)
    private String isContract;
    @Column(name="expiration_date"   , nullable=true , unique=false)
    private Date expirationDate;
    @Column(name="signature_date"   , nullable=true , unique=false)
    private Date signatureDate;
    @Column(name="is_contain"   , nullable=true , unique=false)
    private String isContain;
    @Column(name="has_invoice"   , nullable=true , unique=false)
    private String hasInvoice;
    @Column(name="invoice_category"   , nullable=true , unique=false)
    private String invoiceCategory;
    @Column(name="is_freight"   , nullable=true , unique=false)
    private String isFreight;
    @Column(name="mininum_stock"   , nullable=true , unique=false)
    private BigDecimal minimumStock;

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;    //备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    //1.254
    @Column(name = "financer")
    private Integer financer;

    @Column(name = "financer_name")
    private String financerName;

    @Column(name = "financer_time")
    private Date financerTime;

    //是否在售
    @Column(name="enabled"  , length=1 , nullable=true , unique=false)
    private String enabled;
    //是否销售过
    @Column(name="is_saled"  , length=1 , nullable=true , unique=false)
    private String isSaled;


    @Column(name="product"  )
    private Integer product;



    @Column(name="customer"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer customer;

    @Column(name="type"  , length=100 , nullable=true , unique=false)
    private String type; //类型:1-通用,2-专属

    @Column(name="model"  , length=100 , nullable=true , unique=false)
    private String model;//型号

    @Column(name="specifications"  , length=100 , nullable=true , unique=false)
    private String specifications;  //规格

    @Column(name="unit"  , length=100 , nullable=true , unique=false)
    private String unit; //单位

    //关联人id
    @Column(name="correlater"  , nullable=true , unique=false)
    private Integer correlater;

    //关联人姓名
    @Column(name="correlater_name"  , length=100 , nullable=true , unique=false)
    private String correlaterName;

    @Column(name="correlate_date"   , nullable=true , unique=false)
    private Date correlateDate; //关联时间

    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId;

    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo;


    @Column(name="category"  , length=100 , nullable=true , unique=false)
    private String category;

    public Integer getFinancer() {
        return financer;
    }

    public void setFinancer(Integer financer) {
        this.financer = financer;
    }

    public String getFinancerName() {
        return financerName;
    }

    public void setFinancerName(String financerName) {
        this.financerName = financerName;
    }

    public Date getFinancerTime() {
        return financerTime;
    }

    public void setFinancerTime(Date financerTime) {
        this.financerTime = financerTime;
    }

    @Column(name = "current_stock", nullable = true, unique = false)
    private BigDecimal currentStock;        //bigint comment '当前库存',

    @Column(name = "available_stock", nullable = true, unique = false)
    private BigDecimal availableStock;      //bigint comment '可用库存',

    @Column(name = "initial_stock", nullable = true, unique = false)
    private BigDecimal initialStock;      //bigint comment '初始库存',

    @Column(name="price_desc"   , nullable=true , unique=false)
    private String priceDesc; //价格说明

    //1.119新增字段 unit_id
    @Column(name="unit_id"  , nullable=true , unique=false)
    private Integer unitId;

    //名称是否需要修改
    @Column(name="name_linkable"   , nullable=true , unique=false)
    private String nameLinkable;
    //规格型号是否需要修改
    @Column(name="model_linkable"   , nullable=true , unique=false)
    private String modelLinkable;
    //修改基本信息原因
    @Column(name="basic_reason"   , nullable=true , unique=false)
    private String basicReason;
    //修改价格元素原因
    @Column(name="price_reason"   , nullable=true , unique=false)
    private String priceReason;
    //生效选项:1-立即,2-择期
    @Column(name="effective_option"   , nullable=true , unique=false)
    private String effectiveOption;
    //价格生效日期
    @Column(name="effective_date"   , nullable=true , unique=false)
    private Date effectiveDate;

    @Column(name = "exclusive_time", nullable = true, unique = false)
    private Integer exclusiveTime;      //成为专属时间:1-今年,2-去年,3-更久之前
    @Column(name = "exclusive_month", nullable = true, unique = false)
    private String exclusiveMonth;
    //文件列表
    @Transient
    private List fileList;
    //启停记录
    @Transient
    private List startAndStopList;

    //合同ids
    @Transient
    private Integer contractId;
    //订单数量
    @Transient
    private Integer orderNum;
    //合同编号
    @Transient
    private String contractNumber;
    @Transient
    private String productName;

    @Transient
    private Date endDate;


    //地址列表
    @Transient
    private List<Map<String,Object>> addressList;


    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }
    public Integer getId() {
        return id;
    }

    public void setId (Integer id) {
        this.id =  id;
    }

    public String getInnerSn() {
        return innerSn;
    }

    public void setInnerSn (String innerSn) {
        this.innerSn =  innerSn;
    }

    public String getOuterSn() {
        return outerSn;
    }

    public void setOuterSn (String outerSn) {
        this.outerSn =  outerSn;
    }

    public String getOuterName() {
        return outerName;
    }

    public void setOuterName (String outerName) {
        this.outerName =  outerName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName (String customerName) {
        this.customerName =  customerName;
    }

    public String getMiniPackingMode() {
        return miniPackingMode;
    }

    public void setMiniPackingMode (String miniPackingMode) {
        this.miniPackingMode =  miniPackingMode;
    }

    public String getMiniPackingMaterial() {
        return miniPackingMaterial;
    }

    public void setMiniPackingMaterial (String miniPackingMaterial) {
        this.miniPackingMaterial =  miniPackingMaterial;
    }

    public Long getMiniPackingAmount() {
        return miniPackingAmount;
    }

    public void setMiniPackingAmount (Long miniPackingAmount) {
        this.miniPackingAmount =  miniPackingAmount;
    }

    public String getOuterPackingMode() {
        return outerPackingMode;
    }

    public void setOuterPackingMode (String outerPackingMode) {
        this.outerPackingMode =  outerPackingMode;
    }


    public String getOuterPackingMaterial() {
        return outerPackingMaterial;
    }

    public void setOuterPackingMaterial (String outerPackingMaterial) {
        this.outerPackingMaterial =  outerPackingMaterial;
    }

    public Long getOuterPackingAmount() {
        return outerPackingAmount;
    }

    public void setOuterPackingAmount (Long outerPackingAmount) {
        this.outerPackingAmount =  outerPackingAmount;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice (BigDecimal unitPrice) {
        this.unitPrice =  unitPrice;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate (BigDecimal taxRate) {
        this.taxRate =  taxRate;
    }

    public BigDecimal getUnitPriceNotax() {
        return unitPriceNotax;
    }

    public void setUnitPriceNotax (BigDecimal unitPriceNotax) {
        this.unitPriceNotax =  unitPriceNotax;
    }

    public String getContractSn() {
        return contractSn;
    }

    public void setContractSn (String contractSn) {
        this.contractSn =  contractSn;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo (String memo) {
        this.memo =  memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator (Integer creator) {
        this.creator =  creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName (String createName) {
        this.createName =  createName;
    }

    public Date getCreateDate() {
        return createDate;
    }


    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public void setCreateDate (Date createDate) {
        this.createDate =  createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator (Integer updator) {
        this.updator =  updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName (String updateName) {
        this.updateName =  updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate (Date updateDate) {
        this.updateDate =  updateDate;
    }


    public BigDecimal getCurrentStock() {
        return currentStock;
    }

    public void setCurrentStock(BigDecimal currentStock) {
        this.currentStock = currentStock;
    }

    public BigDecimal getAvailableStock() {
        return availableStock;
    }

    public void setAvailableStock(BigDecimal availableStock) {
        this.availableStock = availableStock;
    }

    public String getTechnicalPrincipal() {
        return technicalPrincipal;
    }

    public void setTechnicalPrincipal(String technicalPrincipal) {
        this.technicalPrincipal = technicalPrincipal;
    }

    public String getTechnicalPrincipalName() {
        return technicalPrincipalName;
    }

    public void setTechnicalPrincipalName(String technicalPrincipalName) {
        this.technicalPrincipalName = technicalPrincipalName;
    }

    public Integer getProcessDept() {
        return processDept;
    }

    public void setProcessDept(Integer processDept) {
        this.processDept = processDept;
    }

    public String getProcessDeptName() {
        return processDeptName;
    }

    public void setProcessDeptName(String processDeptName) {
        this.processDeptName = processDeptName;
    }

    public String getPhrase() {
        return phrase;
    }

    public void setPhrase(String phrase) {
        this.phrase = phrase;
    }



    public Date getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }

    public Date getSignatureDate() {
        return signatureDate;
    }

    public void setSignatureDate(Date signatureDate) {
        this.signatureDate = signatureDate;
    }

    public String getIsContract() {
        return isContract;
    }

    public void setIsContract(String isContract) {
        this.isContract = isContract;
    }

    public String getIsContain() {
        return isContain;
    }

    public void setIsContain(String isContain) {
        this.isContain = isContain;
    }

    public String getHasInvoice() {
        return hasInvoice;
    }

    public void setHasInvoice(String hasInvoice) {
        this.hasInvoice = hasInvoice;
    }

    public String getInvoiceCategory() {
        return invoiceCategory;
    }

    public void setInvoiceCategory(String invoiceCategory) {
        this.invoiceCategory = invoiceCategory;
    }

    public String getIsFreight() {
        return isFreight;
    }

    public void setIsFreight(String isFreight) {
        this.isFreight = isFreight;
    }

    public BigDecimal getMinimumStock() {
        return minimumStock;
    }

    public void setMinimumStock(BigDecimal minimumStock) {
        this.minimumStock = minimumStock;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public BigDecimal getInitialStock() {
        return initialStock;
    }

    public void setInitialStock(BigDecimal initialStock) {
        this.initialStock = initialStock;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getIsSaled() {
        return isSaled;
    }

    public void setIsSaled(String isSaled) {
        this.isSaled = isSaled;
    }

    public BigDecimal getUnitPriceInvoice() {
        return unitPriceInvoice;
    }

    public void setUnitPriceInvoice(BigDecimal unitPriceInvoice) {
        this.unitPriceInvoice = unitPriceInvoice;
    }

    public BigDecimal getUnitPriceNoinvoice() {
        return unitPriceNoinvoice;
    }

    public void setUnitPriceNoinvoice(BigDecimal unitPriceNoinvoice) {
        this.unitPriceNoinvoice = unitPriceNoinvoice;
    }

    public BigDecimal getUnitPriceReference() {
        return unitPriceReference;
    }

    public void setUnitPriceReference(BigDecimal unitPriceReference) {
        this.unitPriceReference = unitPriceReference;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getCorrelater() {
        return correlater;
    }

    public void setCorrelater(Integer correlater) {
        this.correlater = correlater;
    }

    public String getCorrelaterName() {
        return correlaterName;
    }

    public void setCorrelaterName(String correlaterName) {
        this.correlaterName = correlaterName;
    }

    public Date getCorrelateDate() {
        return correlateDate;
    }

    public void setCorrelateDate(Date correlateDate) {
        this.correlateDate = correlateDate;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }


    public Integer getProductCustomer() {
        return productCustomer;
    }

    public void setProductCustomer(Integer productCustomer) {
        this.productCustomer = productCustomer;
    }

    public Integer getProduct() {
        return product;
    }

    public void setProduct(Integer product) {
        this.product = product;
    }

    public Integer getCustomer() {
        return customer;
    }

    public void setCustomer(Integer customer) {
        this.customer = customer;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getPriceDesc() {
        return priceDesc;
    }

    public void setPriceDesc(String priceDesc) {
        this.priceDesc = priceDesc;
    }

    public List getFileList() {
        return fileList;
    }

    public void setFileList(List fileList) {
        this.fileList = fileList;
    }

    public List getStartAndStopList() {
        return startAndStopList;
    }

    public void setStartAndStopList(List startAndStopList) {
        this.startAndStopList = startAndStopList;
    }

    public Integer getContractId() {
        return contractId;
    }

    public void setContractId(Integer contractId) {
        this.contractId = contractId;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getContractNumber() {
        return contractNumber;
    }

    public void setContractNumber(String contractNumber) {
        this.contractNumber = contractNumber;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public List<Map<String, Object>> getAddressList() {
        return addressList;
    }

    public void setAddressList(List<Map<String, Object>> addressList) {
        this.addressList = addressList;
    }

    public String getNameLinkable() {
        return nameLinkable;
    }

    public void setNameLinkable(String nameLinkable) {
        this.nameLinkable = nameLinkable;
    }

    public String getModelLinkable() {
        return modelLinkable;
    }

    public void setModelLinkable(String modelLinkable) {
        this.modelLinkable = modelLinkable;
    }

    public String getBasicReason() {
        return basicReason;
    }

    public void setBasicReason(String basicReason) {
        this.basicReason = basicReason;
    }

    public String getPriceReason() {
        return priceReason;
    }

    public void setPriceReason(String priceReason) {
        this.priceReason = priceReason;
    }

    public String getEffectiveOption() {
        return effectiveOption;
    }

    public void setEffectiveOption(String effectiveOption) {
        this.effectiveOption = effectiveOption;
    }

    public Date getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(Date effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getExclusiveTime() {
        return exclusiveTime;
    }

    public void setExclusiveTime(Integer exclusiveTime) {
        this.exclusiveTime = exclusiveTime;
    }

    public String getExclusiveMonth() {
        return exclusiveMonth;
    }

    public void setExclusiveMonth(String exclusiveMonth) {
        this.exclusiveMonth = exclusiveMonth;
    }
}
