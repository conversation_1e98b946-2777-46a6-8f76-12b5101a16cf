package cn.sphd.miners.modules.personal.service.impl;

import cn.sphd.miners.modules.personal.dao.PersonnelReimbursetAttachmentDao;
import cn.sphd.miners.modules.personal.entity.PersonnelReimbursetAttachment;
import cn.sphd.miners.modules.personal.service.PersonnelReimbursetAttachmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/*
**
*<AUTHOR>
*@Date 2016/12/7 17:53
*/
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class PersonnelReimbursetAttachmentServiceImpl implements PersonnelReimbursetAttachmentService {

    @Autowired
    PersonnelReimbursetAttachmentDao personnelReimbursetAttachmentDao;
    @Override
    public void addPersonnelReimbursetAttachment(PersonnelReimbursetAttachment personnelReimbursetAttachment) {
        personnelReimbursetAttachmentDao.save(personnelReimbursetAttachment);

    }
}
