package cn.sphd.miners.modules.finance.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.finance.entity.FinanceInvoiceRateHistory;
import cn.sphd.miners.modules.finance.entity.FinanceInvoiceSetting;
import cn.sphd.miners.modules.finance.entity.FinanceInvoiceSettingHistory;
import cn.sphd.miners.modules.finance.service.InvoiceService;
import cn.sphd.miners.modules.system.entity.User;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
 * Created by Administrator on 2018/7/20.
 * 1.48开票管理之前期准备
 * 发票设置模块
 */
@Controller
@RequestMapping("/invoiceSetting")
public class InvoiceSettingController {

    @Autowired
    InvoiceService invoiceService;

    /**
     *<AUTHOR>
     *@date 2018/7/20 15:04
     *跳转发票设置页面
    */
    @RequestMapping("/toInvoiceSettingPage.do")
    public String toInvoiceSettingPage(){
        return "/finance/invoiceSetting";
    }

    /**
     *<AUTHOR>
     *@date 2018/8/1 15:04
     *新增发票设置
    */
    @ResponseBody
    @RequestMapping("/addInvoiceSetting.do")
    public void addOrUpdate(FinanceInvoiceSetting financeInvoiceSetting, User user, HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        if (!"".equals(financeInvoiceSetting.getCategory()) && financeInvoiceSetting.getCategory()!=null ){
            FinanceInvoiceSetting financeInvoiceSetting1 = invoiceService.getFinanceInvoiceSetting(user.getOid(),financeInvoiceSetting.getCategory(),null);
            if (financeInvoiceSetting1!=null){
                map.put("state",2);  //系统中已有此发票类型
            }else {
                //添加发票设置
                financeInvoiceSetting.setCreator(user.getUserID());
                financeInvoiceSetting.setCreateName(user.getUserName());
                financeInvoiceSetting.setCreateDate(new Date());
                financeInvoiceSetting.setOrganization(user.getOrganization());
                invoiceService.addFinanceInvoiceSetting(financeInvoiceSetting);

                //添加发票设置历史记录
                FinanceInvoiceSettingHistory financeInvoiceSettingHistory = this.addFinanceInvoiceSettingHistory(financeInvoiceSetting,user,null);

                String addTaxRate = financeInvoiceSetting.getEnableTaxTate();
                String[] addTaxRate1 = addTaxRate.split(",");
                for (int i=0; i<addTaxRate1.length;i++){
                    this.addRateHistory(user,addTaxRate1[i],financeInvoiceSettingHistory,financeInvoiceSetting.getId(),1);  //添加税率
                }
                map.put("state",1);  //添加成功
            }
        }else {
            map.put("state",0);  //添加失败
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    /**
     *<AUTHOR>
     *@date 2017/7/23 11:07
     *查询机构中的发票设置
     */
    @ResponseBody
    @RequestMapping("/getFinanceInvoiceSetting.do")
    public void getFinanceInvoiceSetting(User user, HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        Integer oid = user.getOid();
        List<FinanceInvoiceSetting> financeInvoiceSettings = invoiceService.getFinanceInvoiceSettingByOid(oid);
        map.put("financeInvoiceSettings",financeInvoiceSettings);
        ObjectToJson.objectToJson1(map,new String[]{"organization","financeInvoiceSettingHistoryHashSet"},response);
    }

    /**
     *<AUTHOR>
     *@date 2018/8/2 15:54
     *修改记录查看列表
     * invoiceSettingId 发票设置id
     */
    @ResponseBody
    @RequestMapping("/getModifyRecord.do")
    public void getModifyRecord(Integer invoiceSettingId,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        if (invoiceSettingId!=null){
            List<FinanceInvoiceSettingHistory> invoiceSettingHistories = invoiceService.getFinanceInvoiceSettingHistoryByInvoiceSettingId(invoiceSettingId);
            map.put("invoiceSettingHistories",invoiceSettingHistories);
            map.put("status",1); //成功
        }else {
            map.put("status",0); //查询失败
        }
        ObjectToJson.objectToJson1(map,new String[]{"setting","financeInvoiceRateHistoryHashSet"},response);
    }

    /**
     *<AUTHOR>
     *@date 2018/8/6 16:08
     *查看修改记录的详情
    */
    @ResponseBody
    @RequestMapping("/getInvoiceSettingDetail.do")
    public void getInvoiceSettingDetail(Integer invoiceSettingHistoryId,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        if (invoiceSettingHistoryId!=null){
            FinanceInvoiceSettingHistory financeInvoiceSettingHistory = invoiceService.getFinanceInvoiceSettingHistoryById(invoiceSettingHistoryId,null);  //查找详情
            map.put("financeInvoiceSettingHistory",financeInvoiceSettingHistory);
        }
        ObjectToJson.objectToJson1(map,new String[]{"setting","financeInvoiceRateHistoryHashSet",},response);
    }

    /**
     *<AUTHOR>
     *@date 2018/8/7 9:13
     *获取某发票类型的可用税率
     * String category(发票类型:1-增值税专用票,2-增值税普通票,3-其它普通票)
    */
    @ResponseBody
    @RequestMapping("/getTaxRate.do")
    public void getTaxRate(String category,User user,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        Integer oid = user.getOid();
        if (!"".equals(category) && category!=null){
            FinanceInvoiceSetting financeInvoiceSetting = invoiceService.getFinanceInvoiceSetting(oid,category,null);
            map.put("financeInvoiceSetting",financeInvoiceSetting);
        }
        ObjectToJson.objectToJson1(map,new String[]{"organization","financeInvoiceSettingHistoryHashSet","messageId","approveMemo","applyMemo","operation","auditDate",
                          "auditorName","auditor","approveLevel","approveStatus","approveItem","updateDate","updateName","updator"},response);
    }

    /**
     *<AUTHOR>
     *@date 2018/8/4 17:24
     *发票设置的修改
     * addTaxRate(添加的税率)、stopTaxRate（要停用的税率）、startTaxRate（要启用的税率）
     * 新增 type:1-初始化(初始化的项目不需要修改记录)  (1.307初始化-20240903/lyx)
    */
    @ResponseBody
    @RequestMapping("/updateInvoiceSetting.do")
    public void updateInvoiceSetting(String addTaxRate,String stopTaxRate,String startTaxRate,FinanceInvoiceSetting financeInvoiceSetting,HttpServletResponse response,User user,Integer type) throws IOException {
        Map<String,Object> map = new HashMap<>();
        if (financeInvoiceSetting.getId()!=null){
            FinanceInvoiceSetting originalInvoiceSetting = invoiceService.getFinanceInvoiceSetting(null,null,financeInvoiceSetting.getId());  //原始的发票设置
            FinanceInvoiceSettingHistory financeInvoiceSettingHistory1 = invoiceService.getPreviousInvoiceSetting(originalInvoiceSetting.getId());  //上一次的修改信息

            String num = this.num(addTaxRate,originalInvoiceSetting);  //返回1，则有相同的税率；返回null，则无相同的税率
            if (StringUtils.isNotEmpty(num) && "1".equals(num)){
                map.put("status",1);   //系统中有相同的税率
            }else {
                if (originalInvoiceSetting.getId()!=null){
                    if (type!=null&&1==type){  //初始化的没有修改记录，直接更新数据
                        if (!"".equals(addTaxRate) || !"".equals(startTaxRate) || !"".equals(stopTaxRate)) {
                            if (addTaxRate != null && !"".equals(addTaxRate)) {   //添加税率(判断系统中有没有添加的税率)
//                                String[] addTaxRate1 = addTaxRate.split(",");
//                                for (int i = 0; i < addTaxRate1.length; i++) {
//                                    this.addRateHistory(user, addTaxRate1[i], financeInvoiceSettingHistory, originalInvoiceSetting.getId(), 1);  //添加税率
//                                }
                                if (!"".equals(originalInvoiceSetting.getEnableTaxTate()) && originalInvoiceSetting.getEnableTaxTate() != null) {
                                    originalInvoiceSetting.setEnableTaxTate(originalInvoiceSetting.getEnableTaxTate() + "," + addTaxRate);
                                } else {
                                    originalInvoiceSetting.setEnableTaxTate(addTaxRate);
                                }
                            }
                            if (startTaxRate != null && !"".equals(startTaxRate)) {  //启用税率
                                String[] startTaxRate1s = startTaxRate.split(",");
                                String disableTaxRate = originalInvoiceSetting.getDisableTaxRate();  //原来的无效税率
                                for (String startTaxRate1 : startTaxRate1s) {
//                                    this.addRateHistory(user, startTaxRate1, financeInvoiceSettingHistory, originalInvoiceSetting.getId(), 2);  //启用税率

                                    if (disableTaxRate.indexOf(startTaxRate1) > 0) {   //无效税率中去掉启用的税率
                                        disableTaxRate = disableTaxRate.replace("," + startTaxRate1, "");
                                    } else if (disableTaxRate.length() > startTaxRate.length()) {
                                        disableTaxRate = disableTaxRate.replace(startTaxRate1 + ",", "");
                                    } else {
                                        disableTaxRate = "";
                                    }
                                    originalInvoiceSetting.setDisableTaxRate(disableTaxRate);
                                }
                                //将税率添加到启用税率中
                                if (!"".equals(originalInvoiceSetting.getEnableTaxTate()) && originalInvoiceSetting.getEnableTaxTate() != null) {
                                    originalInvoiceSetting.setEnableTaxTate(originalInvoiceSetting.getEnableTaxTate() + "," + startTaxRate);
                                } else {
                                    originalInvoiceSetting.setEnableTaxTate(startTaxRate);
                                }
                            }
                            if (stopTaxRate != null && !"".equals(stopTaxRate)) {     //停用税率
                                String[] stopTaxRates = stopTaxRate.split(",");
                                String stopTaxRate2 = originalInvoiceSetting.getEnableTaxTate();
                                for (String stopTaxRate1 : stopTaxRates) {
//                                    this.addRateHistory(user, stopTaxRate1, financeInvoiceSettingHistory, originalInvoiceSetting.getId(), 3);  //停用税率
                                    if (stopTaxRate2.indexOf(stopTaxRate1) > 0) {   //有效税率中去掉停用的税率
                                        stopTaxRate2 = stopTaxRate2.replace("," + stopTaxRate1, "");
                                    } else if (stopTaxRate2.length() > startTaxRate.length()) {
                                        stopTaxRate2 = stopTaxRate2.replace(stopTaxRate1 + ",", "");
                                    } else {
                                        stopTaxRate2 = "";
                                    }
                                    originalInvoiceSetting.setEnableTaxTate(stopTaxRate2);
                                }
                                if (!"".equals(originalInvoiceSetting.getDisableTaxRate()) && originalInvoiceSetting.getDisableTaxRate() != null) {
                                    originalInvoiceSetting.setDisableTaxRate(originalInvoiceSetting.getDisableTaxRate() + "," + stopTaxRate);
                                } else {
                                    originalInvoiceSetting.setDisableTaxRate(stopTaxRate);
                                }
                            }
                        }
                        originalInvoiceSetting.setAmountLimited(financeInvoiceSetting.getAmountLimited());
                        originalInvoiceSetting.setLinesLimited(financeInvoiceSetting.getLinesLimited());
                        originalInvoiceSetting.setHasAttachment(financeInvoiceSetting.isHasAttachment());
                        originalInvoiceSetting.setMultipleRate(financeInvoiceSetting.isMultipleRate());
                        originalInvoiceSetting.setUpdateName(user.getUserName());
                        originalInvoiceSetting.setUpdateDate(new Date());
                        originalInvoiceSetting.setUpdator(user.getUserID());
                        invoiceService.updateFinanceInvoiceSetting(originalInvoiceSetting);

                        //将修改后的发票设置更新到历史表中
                        financeInvoiceSettingHistory1.setEnableTaxTate(originalInvoiceSetting.getEnableTaxTate());
                        financeInvoiceSettingHistory1.setDisableTaxRate(originalInvoiceSetting.getDisableTaxRate());
                        financeInvoiceSettingHistory1.setAmountLimited(originalInvoiceSetting.getAmountLimited());
                        financeInvoiceSettingHistory1.setLinesLimited(originalInvoiceSetting.getLinesLimited());
                        financeInvoiceSettingHistory1.setHasAttachment(originalInvoiceSetting.isHasAttachment());
                        financeInvoiceSettingHistory1.setMultipleRate(originalInvoiceSetting.isMultipleRate());
                        invoiceService.updateFinanceInvoiceSettingHistory(financeInvoiceSettingHistory1);

                        List<FinanceInvoiceRateHistory> financeInvoiceRateHistories = invoiceService.getFinanceInvoiceRateHistoryBySettingId(originalInvoiceSetting.getId(),financeInvoiceSettingHistory1.getId(),null,null,null);
                        for (FinanceInvoiceRateHistory fr:financeInvoiceRateHistories) {
                            invoiceService.deleteInvoiceRateHistory(fr);
                        }

                        String enableTaxTate = originalInvoiceSetting.getEnableTaxTate();  //更新后的有效税率
                        if (StringUtils.isNotEmpty(enableTaxTate)){
                            String[] enableTaxTates = stopTaxRate.split(",");
                            for (String enableTaxTate1 : enableTaxTates) {
                                 this.addRateHistory(user, enableTaxTate1, financeInvoiceSettingHistory1, originalInvoiceSetting.getId(), 1);  //添加税率
                            }
                        }
                        String disableTaxRate = originalInvoiceSetting.getDisableTaxRate();  //更新后的无效税率
                        if (StringUtils.isNotEmpty(disableTaxRate)){
                            String[] disableTaxRates = stopTaxRate.split(",");
                            for (String disableTaxRate1 : disableTaxRates) {
                                this.addRateHistory(user, disableTaxRate1, financeInvoiceSettingHistory1, originalInvoiceSetting.getId(), 3);  //添加税率
                            }
                        }

                    }else {
                        //添加发票设置历史
                        FinanceInvoiceSettingHistory financeInvoiceSettingHistory = this.addFinanceInvoiceSettingHistory(originalInvoiceSetting, user, financeInvoiceSettingHistory1);
                        if (!"".equals(addTaxRate) || !"".equals(startTaxRate) || !"".equals(stopTaxRate)) {
                            if (addTaxRate != null && !"".equals(addTaxRate)) {   //添加税率(判断系统中有没有添加的税率)
                                String[] addTaxRate1 = addTaxRate.split(",");
                                for (int i = 0; i < addTaxRate1.length; i++) {
                                    this.addRateHistory(user, addTaxRate1[i], financeInvoiceSettingHistory, originalInvoiceSetting.getId(), 1);  //添加税率
                                }
                                if (!"".equals(originalInvoiceSetting.getEnableTaxTate()) && originalInvoiceSetting.getEnableTaxTate() != null) {
                                    originalInvoiceSetting.setEnableTaxTate(originalInvoiceSetting.getEnableTaxTate() + "," + addTaxRate);
                                } else {
                                    originalInvoiceSetting.setEnableTaxTate(addTaxRate);
                                }
                            }
                            if (startTaxRate != null && !"".equals(startTaxRate)) {  //启用税率
                                String[] startTaxRate1s = startTaxRate.split(",");
                                String disableTaxRate = originalInvoiceSetting.getDisableTaxRate();  //原来的无效税率
                                for (String startTaxRate1 : startTaxRate1s) {
                                    this.addRateHistory(user, startTaxRate1, financeInvoiceSettingHistory, originalInvoiceSetting.getId(), 2);  //启用税率

                                    if (disableTaxRate.indexOf(startTaxRate1) > 0) {   //无效税率中去掉启用的税率
                                        disableTaxRate = disableTaxRate.replace("," + startTaxRate1, "");
                                    } else if (disableTaxRate.length() > startTaxRate.length()) {
                                        disableTaxRate = disableTaxRate.replace(startTaxRate1 + ",", "");
                                    } else {
                                        disableTaxRate = "";
                                    }
                                    originalInvoiceSetting.setDisableTaxRate(disableTaxRate);
                                }
                                //将税率添加到启用税率中
                                if (!"".equals(originalInvoiceSetting.getEnableTaxTate()) && originalInvoiceSetting.getEnableTaxTate() != null) {
                                    originalInvoiceSetting.setEnableTaxTate(originalInvoiceSetting.getEnableTaxTate() + "," + startTaxRate);
                                } else {
                                    originalInvoiceSetting.setEnableTaxTate(startTaxRate);
                                }
                            }
                            if (stopTaxRate != null && !"".equals(stopTaxRate)) {     //停用税率
                                String[] stopTaxRates = stopTaxRate.split(",");
                                String stopTaxRate2 = originalInvoiceSetting.getEnableTaxTate();
                                for (String stopTaxRate1 : stopTaxRates) {
                                    this.addRateHistory(user, stopTaxRate1, financeInvoiceSettingHistory, originalInvoiceSetting.getId(), 3);  //停用税率
                                    if (stopTaxRate2.indexOf(stopTaxRate1) > 0) {   //有效税率中去掉停用的税率
                                        stopTaxRate2 = stopTaxRate2.replace("," + stopTaxRate1, "");
                                    } else if (stopTaxRate2.length() > startTaxRate.length()) {
                                        stopTaxRate2 = stopTaxRate2.replace(stopTaxRate1 + ",", "");
                                    } else {
                                        stopTaxRate2 = "";
                                    }
                                    originalInvoiceSetting.setEnableTaxTate(stopTaxRate2);
                                }
                                if (!"".equals(originalInvoiceSetting.getDisableTaxRate()) && originalInvoiceSetting.getDisableTaxRate() != null) {
                                    originalInvoiceSetting.setDisableTaxRate(originalInvoiceSetting.getDisableTaxRate() + "," + stopTaxRate);
                                } else {
                                    originalInvoiceSetting.setDisableTaxRate(stopTaxRate);
                                }

                            }
                        }
                        originalInvoiceSetting.setAmountLimited(financeInvoiceSetting.getAmountLimited());
                        originalInvoiceSetting.setLinesLimited(financeInvoiceSetting.getLinesLimited());
                        originalInvoiceSetting.setHasAttachment(financeInvoiceSetting.isHasAttachment());
                        originalInvoiceSetting.setMultipleRate(financeInvoiceSetting.isMultipleRate());
                        originalInvoiceSetting.setUpdateName(user.getUserName());
                        originalInvoiceSetting.setUpdateDate(new Date());
                        originalInvoiceSetting.setUpdator(user.getUserID());
                        invoiceService.updateFinanceInvoiceSetting(originalInvoiceSetting);

                        //将修改后的发票设置更新到历史表中
                        financeInvoiceSettingHistory.setEnableTaxTate(originalInvoiceSetting.getEnableTaxTate());
                        financeInvoiceSettingHistory.setDisableTaxRate(originalInvoiceSetting.getDisableTaxRate());
                        financeInvoiceSettingHistory.setAmountLimited(originalInvoiceSetting.getAmountLimited());
                        financeInvoiceSettingHistory.setLinesLimited(originalInvoiceSetting.getLinesLimited());
                        financeInvoiceSettingHistory.setHasAttachment(originalInvoiceSetting.isHasAttachment());
                        financeInvoiceSettingHistory.setMultipleRate(originalInvoiceSetting.isMultipleRate());
                        invoiceService.updateFinanceInvoiceSettingHistory(financeInvoiceSettingHistory);
                    }
                    map.put("status",2);  //修改成功
                }else {
                    map.put("status",0);  //修改失败
                }
            }
        }else {
            map.put("status",0);  //修改失败
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    //添加发票设置的历史记录  financeInvoiceSetting(原始的发票设置)、financeInvoiceSettingHistory(上一次的修改信息,即修改前的信息)
    public FinanceInvoiceSettingHistory addFinanceInvoiceSettingHistory(FinanceInvoiceSetting financeInvoiceSetting,User user,FinanceInvoiceSettingHistory lastFinanceInvoiceSettingHistory){
        FinanceInvoiceSettingHistory financeInvoiceSettingHistory = new FinanceInvoiceSettingHistory();
        financeInvoiceSettingHistory.setAmountLimited(financeInvoiceSetting.getAmountLimited());  //每张发票能开的金额上限
        financeInvoiceSettingHistory.setLinesLimited(financeInvoiceSetting.getLinesLimited());   //每张发票能开的行数上限
        financeInvoiceSettingHistory.setCategory(financeInvoiceSetting.getCategory());   //'发票类型:1-增值税专用票,2-增值税普通票,3-其它普通票'
        financeInvoiceSettingHistory.setHasAttachment(financeInvoiceSetting.isHasAttachment());  //是否含有附件,true-是/false-否
        financeInvoiceSettingHistory.setMultipleRate(financeInvoiceSetting.isMultipleRate());  //是否包含多税率,true-是/false-否
        financeInvoiceSettingHistory.setEnableTaxTate(financeInvoiceSetting.getEnableTaxTate());//有效税率,多税率以,分隔
        financeInvoiceSettingHistory.setDisableTaxRate(financeInvoiceSetting.getDisableTaxRate());  //失效税率,多税率以,分隔
        if (lastFinanceInvoiceSettingHistory!=null){
            financeInvoiceSettingHistory.setPreviousId(lastFinanceInvoiceSettingHistory.getId());  //修改前ID
        }else {
            financeInvoiceSettingHistory.setPreviousId(0);  //修改前ID
        }
        financeInvoiceSettingHistory.setSetting(financeInvoiceSetting);
        financeInvoiceSettingHistory.setCreateDate(financeInvoiceSetting.getCreateDate());
        financeInvoiceSettingHistory.setCreateName(financeInvoiceSetting.getCreateName());
        financeInvoiceSettingHistory.setCreator(financeInvoiceSetting.getCreator());
        financeInvoiceSettingHistory.setUpdateName(user.getUserName());
        financeInvoiceSettingHistory.setUpdateDate(new Date());
        financeInvoiceSettingHistory.setUpdator(user.getUserID());
        invoiceService.addFinanceInvoiceSettingHistory(financeInvoiceSettingHistory);

        return financeInvoiceSettingHistory;
    }


    /**
     *<AUTHOR>
     *@date 2018/8/11 17:15
     *添加税率历史表
     * type 1-添加税率 2-启用税率 3-停用税率
    */
    public void addRateHistory(User user,String rate,FinanceInvoiceSettingHistory settingHistory,Integer settingId,Integer type){
        FinanceInvoiceRateHistory financeInvoiceRateHistory = new FinanceInvoiceRateHistory();

        if (!"".equals(rate) && rate!=null){
            financeInvoiceRateHistory.setRate(BigDecimal.valueOf(Double.valueOf(rate)));  //税率,取百分之部分
            financeInvoiceRateHistory.setSetting(settingId);   //设置ID
            if (settingHistory!=null){
                financeInvoiceRateHistory.setSettingHistory(settingHistory);  //发票设置历史
            }
            if (type==1){
                financeInvoiceRateHistory.setCreator(user.getUserID());
                financeInvoiceRateHistory.setCreateName(user.getUserName());
                financeInvoiceRateHistory.setCreateDate(new Date());
                financeInvoiceRateHistory.setEnabled(true);//启用标志:true-启用,false-停用
            }else {
                if (type==2){
                    financeInvoiceRateHistory.setEnabled(true);//启用标志:true-启用,false-停用
                }else if (type==3){
                    financeInvoiceRateHistory.setEnabled(false);//启用标志:true-启用,false-停用
                }
                financeInvoiceRateHistory.setUpdator(user.getUserID());
                financeInvoiceRateHistory.setUpdateDate(new Date());
                financeInvoiceRateHistory.setUpdateName(user.getUserName());
            }
            invoiceService.addFinanceInvoiceRateHistory(financeInvoiceRateHistory);
        }
    }

    /**
     *<AUTHOR>
     *@date 2018/8/13 9:21
     *判断有无重复的税率
    */
    public String num(String addTaxRate,FinanceInvoiceSetting originalInvoiceSetting){
        String status = null;
        if (!"".equals(addTaxRate) && addTaxRate!=null){
            String allTaxRate = this.allTaxRate(originalInvoiceSetting);
            String[] addTaxRate1 = addTaxRate.split(",");
            String[] allTaxRate1 = allTaxRate.split(",");
            for (int i=0;i<addTaxRate1.length;i++){
                String startTaxRate2 = addTaxRate1[i];
                for (int j=0;j<allTaxRate1.length;j++){
                    if (startTaxRate2.equals(allTaxRate1[j])){
                        status = "1";
                        break;
                    }
                }
            }
        }
        return status;
    }
    
    /**
     *<AUTHOR>
     *@date 2018/8/13 10:45
     *将可用税率和不可用税率整合到一起
    */
    public String allTaxRate(FinanceInvoiceSetting originalInvoiceSetting){
        String allTaxRate = null;
        if (!"".equals(originalInvoiceSetting.getEnableTaxTate()) && originalInvoiceSetting.getEnableTaxTate()!=null){
            allTaxRate = originalInvoiceSetting.getEnableTaxTate();
        }
        if (!"".equals(originalInvoiceSetting.getDisableTaxRate()) && originalInvoiceSetting.getDisableTaxRate()!=null){
            if (allTaxRate!=null){
                allTaxRate = allTaxRate+","+originalInvoiceSetting.getDisableTaxRate();
            }else {
                allTaxRate = originalInvoiceSetting.getDisableTaxRate();
            }
        }
        return allTaxRate;
    }

    /**
     *<AUTHOR>
     *@date 2018/8/13 11:55
     *查询已停用的税率接口
     * settingHistoryId（修改历史id）、settingId（设置id）
    */
    @ResponseBody
    @RequestMapping("/deactivatedTaxRate.do")
    public void deactivatedTaxRate(Integer settingHistoryId,Integer settingId,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        List<Map<String,Object>> rateHistories = new ArrayList<>();
        if (settingId!=null){
            Integer maxTaxRateLogId = invoiceService.getMaxTaxRateHistoryId(settingId,settingHistoryId);  //最大的税率历史表的id
            List<FinanceInvoiceRateHistory> financeInvoiceRateHistories = invoiceService.getFinanceInvoiceRateHistoryBySettingId(settingId,null,null,maxTaxRateLogId,1);
            for (FinanceInvoiceRateHistory financeInvoiceRateHistory:financeInvoiceRateHistories) {
                Map<String,Object> map1 = new HashMap<>();
                List<FinanceInvoiceRateHistory> f = invoiceService.getFinanceInvoiceRateHistoryBySettingId(settingId,null,financeInvoiceRateHistory.getRate(),maxTaxRateLogId,null);
                if (f.size()>0){
                    FinanceInvoiceRateHistory financeInvoiceRateHistory1 = f.get(f.size()-1);
                    if (!financeInvoiceRateHistory1.isEnabled()){   //只有最后是停用的税率才返回
                        map1.put("rate",financeInvoiceRateHistory.getRate());
                        map1.put("financeInvoiceRateHistorys",f);
                        rateHistories.add(map1);
                    }
                }
            }
            map.put("rateHistories",rateHistories);
        }
        ObjectToJson.objectToJson1(map,new String[]{"settingHistory"},response);
    }

    /**
     *<AUTHOR>
     *@date 2018/8/14 13:45
     *查看某发票设置的详情
    */
    @ResponseBody
    @RequestMapping("/getSettingDetail.do")
    public void getSettingDetail(Integer settingId,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        if (settingId!=null){
            FinanceInvoiceSetting financeInvoiceSetting = invoiceService.getFinanceInvoiceSetting(null,null,settingId);
            map.put("financeInvoiceSetting",financeInvoiceSetting);
        }
        ObjectToJson.objectToJson1(map,new String[]{"organization","financeInvoiceSettingHistoryHashSet"},response);
    }

    /**
     *<AUTHOR>
     *@date 2018/8/14 9:59
     *修改记录详情中查询的已停用税率
     * settingHistoryId（修改历史id）、settingId（设置id）
    */
    @ResponseBody
    @RequestMapping("/deactivatedTaxRateLog.do")
    public void deactivatedTaxRateLog(Integer settingHistoryId,Integer settingId,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        List<Map<String,Object>> rateHistories = new ArrayList<>();
        if (settingHistoryId!=null && settingId!=null){
        }
//        map.put("str",str);
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    /**
     * <AUTHOR>
     * @Description 获取某发票类型的设置
     * @Date 2022/5/16
     * @param String category(发票类型1-增值税专用票,2-增值税普通票,3-其它普通票)
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getSettingByCategory.do")
    public JsonResult getSettingByCategory(String category, User user) {
        Map<String,Object> map = new HashMap<>();
        Integer oid = user.getOid();
        if (!"".equals(category) && category!=null){
            FinanceInvoiceSetting financeInvoiceSetting = invoiceService.getFinanceInvoiceSetting(oid,category,null);
            map.put("financeInvoiceSetting",financeInvoiceSetting);
        }
        return new JsonResult(1,map);
    }

}
