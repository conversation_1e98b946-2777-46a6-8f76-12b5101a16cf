<template>
  <div id="paymentAdvanceApproval">
    <TY_NavTop title="采购的预付款" isSearch="true" @toggleList="search"></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待付款审批" name="1" closable='false' :msgCount="onlineAuditHandle.length">
        <div class="tip"><p>下列预付款申请系采购部门提交，您审批后方可付款！</p></div>
        <div>
          <a class="ui-cell" v-on:click="jump('21' + item.poOrderPrepayment.id, 0,0, '1')" v-for="(item, index) in onlineAuditHandle" v-bind:key="index">
            <div class="item_fn">
              <div class="supName">供应商:{{item.fullName}} <span class="right">{{ item.codeName }}</span></div>
              <div class="oInfo">
                <p>计划付款金额&nbsp;{{item.poOrderPrepayment.planAmount && item.poOrderPrepayment.planAmount.toFixed(2)}}元</p>
                <p>计划付款时间&nbsp;{{item.poOrderPrepayment.planDate | formatDay('YYYY-MM-DD')}}</p>
              </div>
            </div>
            <div class="txt_right">申请人：{{ item.poOrders.createName  }}
              {{item.poOrders.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="已批准" name="2" closable='false' >
        <div class="tip"> 下列付款申请已获审批通过，有待付款。</div>
        <div>
          <a class="ui-cell" v-on:click="jump('22' + item.poOrderPrepayment.id, 1,1, '2')" v-for="(item, index) in onlineAuditOK" v-bind:key="index">
            <div class="item_fn">
              <div class="supName">供应商:{{item.fullName}} <span class="right">{{ item.codeName }}</span></div>
              <div class="oInfo">
                <p>计划付款金额&nbsp;{{item.poOrderPrepayment.planAmount && item.poOrderPrepayment.planAmount.toFixed(2)}}元</p>
                <p>计划付款时间&nbsp;{{item.poOrderPrepayment.planDate | formatDay('YYYY-MM-DD')}}</p>
              </div>
            </div>
            <div class="txt_right">申请人：{{ item.poOrders.createName  }}
              {{item.poOrders.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>
<style lang = "less">
#paymentAdvanceApproval{
  overflow: auto;
  .ui-cell{ display: block; }
  .tip{
    padding:5px 12px;
    background-color: #E2EFDA;
  }
  .txt_right{
    text-align: right;
    font-size:11px;
    padding-right:15px;
  }
  .oInfo{ padding:5px 20px; }
  .right{ float: right;}
}
</style>
<script>
export default {
  name: 'paymentAdvanceApproval',
  data () {
    return {
      onlineAuditHandle: [], // 待处理
      onlineAuditOK: [], // 已批准
      // listenersUid: [],
      tabValue: '1',
      offlineAuditHandle: [],
      // toHandle: [],
      nowTime: 0
    }
  },
  filters: {
    formatType: function (num, amount) {
      switch (Number(num)) {
        case 1:
          return '本次仅提交票据，不付款';
          break;
        case 2:
          return `申请付款${amount.toFixed(2)}元`;
          break;
        case 3:
          return `申请付款${amount.toFixed(2)}元`;
          break;
      }
    },
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num);
      } else {
        return ''
      }
    }
  },
  created () {
    this.tabValue = localStorage.navNum || 1;
    let that = this;
    this.getList();
  },
  methods: {
    getList: function () {
      let that = this;
      this.axios.get('../../../purchaseInvoice/getAdvancePaymentPay.do')
        .then(function (response) {
          let res = response.data.data;// 获取列表数据
          console.log('列表：', res);
          let list = res.advancePaymentPayHandle || []; // 待处理
          let list2 = res.advancePaymentPayApproved || []; // 已批准
          that.onlineAuditHandle = list;// 待处理
          that.onlineAuditOK = list2;// 已批准
        })
        .catch(function (error) {
          console.log(error);
        })
    },
    jump: function (id,pun,num) {
      let that = this;
      if(num === 1){//6是有按钮，3是无按钮
        this.$router.push({//已批准
          path: `/purchaseInvoiceDetails3/${id}/${pun}`
        })
      }else{//num == 0
        this.$router.push({//待付款审批 1代表不是采购与付款申请
          path: `/purchaseInvoiceDetails4/${id}/1`
        })
      }
    },
    search: function () {
      this.$router.push({
        path: `/reimburseVerificationBillsQuery`
      })
    }
  }
}
</script>
