package cn.sphd.miners.modules.orderReview.mapper;

import cn.sphd.miners.modules.orderReview.entity.TSlReviewItemDeliveryHistory;

import java.util.List;

/**
 * Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
public interface TSlReviewItemDeliveryHistoryMapper 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    public TSlReviewItemDeliveryHistory selectTSlReviewItemDeliveryHistoryById(Long id);

    /**
     * 查询列表
     * 
     * @param tSlReviewItemDeliveryHistory 
     * @return 集合
     */
    public List<TSlReviewItemDeliveryHistory> selectTSlReviewItemDeliveryHistoryList(TSlReviewItemDeliveryHistory tSlReviewItemDeliveryHistory);

    /**
     * 新增
     * 
     * @param tSlReviewItemDeliveryHistory 
     * @return 结果
     */
    public int insertTSlReviewItemDeliveryHistory(TSlReviewItemDeliveryHistory tSlReviewItemDeliveryHistory);

    /**
     * 修改
     * 
     * @param tSlReviewItemDeliveryHistory 
     * @return 结果
     */
    public int updateTSlReviewItemDeliveryHistory(TSlReviewItemDeliveryHistory tSlReviewItemDeliveryHistory);

    /**
     * 删除
     * 
     * @param id 主键
     * @return 结果
     */
    public int deleteTSlReviewItemDeliveryHistoryById(Long id);

    /**
     * 批量删除
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTSlReviewItemDeliveryHistoryByIds(Long[] ids);
}
