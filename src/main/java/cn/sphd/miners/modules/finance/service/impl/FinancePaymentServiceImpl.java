package cn.sphd.miners.modules.finance.service.impl;

import cn.sphd.miners.common.service.BaseServiceImpl;
import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.modules.finance.dao.FinancePaymentDao;
import cn.sphd.miners.modules.finance.dao.FinancePaymentHistoryDao;
import cn.sphd.miners.modules.finance.entity.FinancePayment;
import cn.sphd.miners.modules.finance.entity.FinancePaymentHistory;
import cn.sphd.miners.modules.finance.service.FinancePaymentService;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2021/6/22
 * @Description
 **/
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED)
public class FinancePaymentServiceImpl extends BaseServiceImpl implements FinancePaymentService {

    @Autowired
    FinancePaymentDao financePaymentDao;
    @Autowired
    FinancePaymentHistoryDao financePaymentHistoryDao;

    @Override
    public FinancePayment addFinancePayment(Integer business,String businessType, BigDecimal amount,BigDecimal planAmount,BigDecimal factAmount, User user, String method, Date planDate,
                                            Date factDate, Integer accountId,Integer accountBill,String summary){
        //新增上的可付款方式
        FinancePayment financePayment = new FinancePayment();
        financePayment.setOrg(user.getOid());
        financePayment.setBusinessType(businessType);
        financePayment.setBusiness(business);
//        financePayment.setBusinessNo(business);
        financePayment.setAmount(amount);
        if ("6".equals(method)) {
            financePayment.setMethod("3");
        }else {
            financePayment.setMethod(method);
        }
        financePayment.setAccountId(accountId);
        if (planDate != null) {
            financePayment.setPlanDate(planDate);
        }
        financePayment.setPlanAmount(planAmount);
        financePayment.setPlanMethod(method);
        if (factDate != null) {
            financePayment.setFactDate(factDate);
        }
        financePayment.setFactAmount(factAmount);
        financePayment.setFactMethod(method);
        financePayment.setStatus("5");
        financePayment.setAccountBill(accountBill);
        financePayment.setSummary(summary);
        financePayment.setCreator(user.getUserID());
        financePayment.setCreateName(user.getUserName());
        financePayment.setCreateDate(new Date());
        financePayment.setUpdator(user.getUserID());
        financePayment.setUpdateName(user.getUserName());
        financePayment.setUpdateDate(financePayment.getCreateDate());
        financePayment.setOperation("1");
        financePayment.setPreviousId(0);
        financePayment.setVersionNo(0);
        financePaymentDao.saveOrUpdate(financePayment);

        this.addPaymentHistory(financePayment,user); //添加历史信息
        return financePayment;
    }

    @Override
    public FinancePayment getPaymentByBusiness(Integer id,Integer business,String businessType){
//        Map<String,Object> map = new HashMap<>();
//        String hql = "from FinancePayment where businessType=:businessType";
//        map.put("businessType",businessType);
//        if (id!=null){
//            hql+=" and id=:id";
//            map.put("id",id);
//        }
//        if (business!=null){
//            hql+=" and business=:business";
//            map.put("business",business);
//        }
        Map<String,Object> params = new HashMap<>();
        StringBuffer hql = new StringBuffer("from FinancePayment");
        List<String> where = new ArrayList<>();
        if (id!=null){
            where.add(" id=:id ");
            params.put("id",id);
        }
        if (business!=null){
            where.add(" business=:business ");
            params.put("business",business);
        }
        if (!MyStrings.nulltoempty(businessType).isEmpty()){
            where.add(" businessType=:businessType ");
            params.put("businessType",businessType);
        }
        if (!where.isEmpty()) {
            hql.append(" where ").append(org.apache.commons.lang3.StringUtils.join(where, " and "));
        }
        return (FinancePayment) financePaymentDao.getByHQLWithNamedParams(hql.toString(),params);
    }

    @Override
    public FinancePaymentHistory addPaymentHistory(FinancePayment financePayment,User user){
        FinancePaymentHistory financePaymentHistory1 = new FinancePaymentHistory();
        BeanUtils.copyPropertiesIgnoreNull(financePayment,financePaymentHistory1);
        financePaymentHistory1.setId(null);
        financePaymentHistory1.setCreateDate(new Date());
        financePaymentHistory1.setCreator(user.getUserID());
        financePaymentHistory1.setCreateName(user.getUserName());
        financePaymentHistory1.setFinancePayment(financePayment.getId());
        financePaymentHistoryDao.save(financePaymentHistory1);
        return financePaymentHistory1;
    }

    @Override
    public List<FinancePaymentHistory> getPaymentHistorysByPaymentId(Integer financePaymentId){  //查找所有的记录
        Map<String,Object> map = new HashMap<>();
        String hql = "from FinancePaymentHistory where financePayment=:financePayment ";
        map.put("financePayment",financePaymentId);
        return financePaymentHistoryDao.getListByHQLWithNamedParams(hql,map);
    }

    @Override
    public FinancePaymentHistory getPaymentHistoryByPaymentId(Integer financePaymentId){  //查找最后一次的记录
        Map<String,Object> map = new HashMap<>();
        String hql = "from FinancePaymentHistory where financePayment=:financePayment order by id desc";
        map.put("financePayment",financePaymentId);
        return (FinancePaymentHistory) financePaymentHistoryDao.getByHQLWithNamedParams(hql,map);
    }

    @Override
    public List<FinancePaymentHistory> getPaymentHistoryByBusiness(Integer business,Integer financePayment,String businessType){
        Map<String,Object> map = new HashMap<>();
        String hql = "from FinancePaymentHistory where businessType=:businessType";
        map.put("businessType",businessType);
        if (business!=null){
            hql+=" and business=:business";
            map.put("business",business);
        }
        if (financePayment!=null){
            hql+=" and financePayment=:financePayment";
            map.put("financePayment",financePayment);
        }
        return financePaymentHistoryDao.getListByHQLWithNamedParams(hql,map);
    }


}
