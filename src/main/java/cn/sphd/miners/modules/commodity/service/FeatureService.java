package cn.sphd.miners.modules.commodity.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.commodity.entity.*;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;
import java.util.Map;

public interface FeatureService {

    //获取特性设置列表
    List<Map<String, Object>> getProductFeatureList(Integer oid, int type, PageInfo pageInfo);

    Object getYszNum(Integer oid);

    Object getMaxCode(Integer oid);

    PdCheckControl getCheckControlById(Long id);
    //获取等级
    List<PdRank> getRankList(Integer oid);
    //等级设置
    String getRankSetting(List<PdRank> list, User user);
    //等级设置
    String addRankSetting(PdRank pdRank, User user);
    //等级设置
    String updateRankSetting(PdRank pdRank, User user);
    //等级设置
    String deleteRankSetting(Long id, User user);

    List<PdRankHistory> getRankRecord(Integer oid);
    List<PdRankHistory> getRankRecordDetail(Long id);
    //获取控制方法列表
    List<PdCheckControl> getPdCheckControlList(Integer oid,Integer enabled);
    //新增控制方法
    String addCheckControl(PdCheckControl checkControl);
    //修改控制方法
    String updateCheckControl(PdCheckControl checkControl);
    //启停控制方法
    String startOrStopCheckControl(Long id,Integer enabled,User user);
    //删除
    String deleteCheckControl(Long id);
    List<Map<String,Object>> getCheckControlRecord(Long id);
    PdCheckControlHistory getCheckControlRecordDetail(Long id);
    //新增特性
    String  addFeatureItem(PdFeatureItem pdFeatureItem,User user);
    //修改特性
    String  updateFeatureItem(PdFeatureItem pdFeatureItem,User user);
    //获取特性列表
    List<PdFeatureItem> getFeatureItemList(Integer product,Integer oid);

    //根据等级设置获取特性列表
    List<PdFeatureItem> getFeatureItemListByRank(Integer rankId);
    //删除特性
    String deleteFeatureItem(Long id);
    //初始化设置提交
    String productFeatureSetting(Integer product,User user);
}
