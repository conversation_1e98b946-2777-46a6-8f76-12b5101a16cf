<template>
  <div id="dailyAffairs">
    <TY_NavTop title="请假审批"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="bg">
        <div class="item">
          <div class="item_title">{{leaveDetails.createName}}</div>
          <div class="item_content"></div>
        </div>
        <div class="item">
          <div class="item_title">请假记录</div>
          <div class="item_content"></div>
        </div>
        <!--提前结束请假部分-->
        <div v-for="(item, index) in personnelLeaveItemList" v-bind:key="index">
          <div class="kj-panel">
            <div class="item">
              <div class="item_title">提前结束请假</div>
              <div class="item_content"></div>
            </div>
            <div class="item">
              <div class="item_title">计划上班时间</div>
              <div class="item_content">{{item.actualEndTime | formatDay('YYYY-MM-DD HH:mm')}}</div>
            </div>
            <div class="item">
              <div class="item_title">说明</div>
              <div class="item_content">{{item.actualReason}}</div>
            </div>
            <div v-if="is.approveStatus === '3'" v-for="(is, index) in item.processList" v-bind:key="index">
              <div class="item color-red">
                提前结束请假的申请被{{is.createName}}驳回！
              </div>
              <div class="item">
                <div class="item_title color-red">驳回理由：</div>
                <div class="item_content">{{is.reason}}</div>
              </div>
            </div>
          </div>
          <div class="process">
            <div>申请人 <span class="processName">{{item.createName}}</span> {{item.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div v-if="it.approveStatus !== '1'" v-for="(it, index) in item.processList" v-bind:key="index">
              审批人 <span class="processName">{{it.userName}}</span> {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}
            </div>
          </div>
        </div>
        <!--主要申请部分-->
        <div>
          <div class="kj-panel">
            <div class="item">
              <div class="item_title">开始时间</div>
              <div class="item_content">{{leaveDetails.beginTime | formatDay('YYYY-MM-DD HH:mm')}}</div>
            </div>
            <div class="item">
              <div class="item_title">结束时间</div>
              <div class="item_content">{{leaveDetails.endTime | formatDay('YYYY-MM-DD HH:mm')}}</div>
            </div>
            <div class="item">
              <div class="item_title">请假类型</div>
              <div class="item_content">{{leaveDetails.leaveTypeName}}</div>
            </div>
            <div class="item">
              <div class="item_title">请假事由</div>
              <div class="item_content">{{leaveDetails.reason}}</div>
            </div>
            <div v-if="td.approveStatus === '3'" v-for="(td, index) in processList" v-bind:key="index">
              <div class="item color-red">
                请假申请被{{td.userName}}驳回！
              </div>
              <div class="item">
                <div class="item_title color-red">驳回理由：</div>
                <div class="item_content">{{td.reason}}</div>
              </div>
            </div>
          </div>
          <div class="process">
            <div>申请人 <span class="processName">{{leaveDetails.createName}}</span> {{leaveDetails.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div v-if="ta.approveStatus !== '1'" v-for="(ta, index) in processList" v-bind:key="index">
              <div>审批人 <span class="processName">{{ta.userName}}</span> {{ta.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            </div>
          </div>
        </div>
        <div class="handle_button" v-if="showBtn">
          <button class="ui-btn ui-btn_info" @click="approveLeave('2')">驳回</button>
          <button class="ui-btn ui-btn_info" @click="approveLeave('1')">批准</button>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
  .bg{
    background: #fff;
    padding: 4px 16px;
  }
  .item{
    display: flex;
    padding: 4px 0;
    line-height: 18px;
    .item_title{
      width: 90px;
      flex: none;
      color: #666;
      line-height: 2rem;
      padding: 0;
    }
    .item_content{
      flex: auto;
      line-height: 2rem;
      text-align: left;
    }
  }
  .kj-panel{
    border-top:1px solid #ddd;
  }
  .process{
    font-size: 12px;
    color: #666;
    margin-top: 8px;
    margin-bottom: 8px;
    margin-left: 90px;
    .processName{
      display: inline-block;
      width: 52px;
      margin-left: 4px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      vertical-align: top;
      text-align: left;
    }
  }
  .item .item_title.color-red{
    color: red;
  }
</style>
<script>
import { gohistory } from '@/js/common'
export default {
  name: 'applyLeaveDetail',
  data () {
    return {
      approvalProcessId: 0,
      loading: false,
      approveInfo: {},
      leaveDetails: {},
      processList: [],
      personnelLeaveItemList: [],
      listenersUid: [],
      showBtn: false
    }
  },
  created () {
    let that = this
    let leaveId = this.$route.params.leaveId
    this.listenersUid = [
      this.sphdSocket.subscribe('notice', function (data) {
        that.loading = false
        let status = Number(data)
        if (status === 1) {
          that.$kiko_message('操作成功')
          gohistory(that)
          // that.$router.push({
          //   path: '/approvalLeave',
          //   name: 'approvalLeave',
          //   params: {
          //     tabValue: that.tabValue || '2'
          //   }
          // })
        } else if (status === 4) {
          that.$kiko_message('已审批，不能重复审批')
        } else if (status === 9) {
          that.$kiko_message('操作失败，因为该条申请已被撤销')
          gohistory(that)
          // that.$router.push({
          //   path: '/approvalLeave',
          //   name: 'approvalLeave',
          //   params: {
          //     tabValue: that.tabValue || '2'
          //   }
          // })
        } else {
          that.$kiko_message('操作失败')
        }
        console.log('notice' + data)
      })
    ]
    let userId = this.sphdSocket.user.userID
    let session = this.sphdSocket.sessionid
    this.sphdSocket.send('leaveDetails', { session: session, leaveId: leaveId, type: 1 })
    this.approveInfo = {
      session: session,
      userId: userId,
      reason: ''
    }
    this.getLeaveDetail()
  },
  destroyed: function () {
    let that = this
    this.listenersUid.forEach(function (item) {
      that.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    getLeaveDetail: function () {
      this.loading = true
      let that = this
      let params = {
        leaveId: this.$route.params.leaveId,
        session: ''
      }
      this.$http.post('../../../leaveAndOutTime/leaveDetails.do', { json: JSON.stringify(params) }, {
        emulateJSON: true
      }).then((response) => {
        let reqData = response.body.data

        that.leaveDetails = reqData.personnelLeave // 计划请假详情
        that.processList = reqData.processList // 计划请假的审批流程
        that.personnelLeaveItemList = reqData.personnelLeaveItemList // 提前结束请假详情

        let nowProcessItem = null
        if (reqData.personnelLeaveItemList.length > 0) {
          let nowProcess = reqData.personnelLeaveItemList[0].processList
          nowProcessItem = nowProcess[nowProcess.length - 1]
        } else {
          nowProcessItem = reqData.processList[reqData.processList.length - 1]
        }
        let ori = that.$store.getters.getLeaveNav
        if (nowProcessItem) {
          if (Number(nowProcessItem.approveStatus) === 1 && Number(ori) === 1 && nowProcessItem.toUser === that.sphdSocket.user.userID) {
            that.showBtn = true
          }
        }
        that.approvalProcessId = reqData.approvalProcessId
        that.loading = false
      }).catch(function () {
        that.loading = false
        that.$message({
          type: 'error',
          message: '系统错误，请重试！'
        })
      })
    },
    jump: function (path) {
      this.$router.push({
        path: '/' + path,
        name: path
      })
    },
    approveLeave: function (approvalStatus) {
      let that = this
      // 逐级审批请假申请 progressiveApprovalLeave 所需参数：session，userId（登录人），approvalProcessId，reason（原因），approvalStatus 1-批准 2-驳回
      this.approveInfo.approvalStatus = approvalStatus
      this.approveInfo.approvalProcessId = this.approvalProcessId
      this.approveInfo.type = this.leaveDetails.actualState === '1' ? 2 : 1
      if (approvalStatus === '2') {
        this.$prompt('驳回理由', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(({ value }) => {
          that.approveInfo.reason = value || ''
          console.log(JSON.stringify(that.approveInfo))
          that.sphdSocket.send('progressiveApprovalLeave', that.approveInfo)
          that.loading = true
        })
      } else {
        console.log(JSON.stringify(this.approveInfo))
        that.sphdSocket.send('progressiveApprovalLeave', this.approveInfo)
        that.loading = true
      }
    }
  }
}
</script>
