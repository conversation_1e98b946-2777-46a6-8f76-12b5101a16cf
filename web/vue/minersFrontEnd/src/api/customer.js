import {request} from "@/utils/request";

// creator: hxz，2024-10-18 主列表
export function getPdCustomer(data) {
    return request({
        url: '/sales/getPdCustomer.do',
        data
    })
}

// creator: hxz，2024-10-18 公司总揽 主列表
export function getAllPdCustomerList(data) {
    return request({
        url: '/sales/getAllPdCustomerList.do',
        data
    })
}


// creator: 张旭博，2024-05-28 09:57:25， 客户清单上传
export function importCustomer(file) {
    return request({
        url: '/export/importCustomer.do',
        data: {
            file: file
        }
    })
}


// creator: hxz 2024-11-13 获取省市区
export function getTYRegion(data) {
    return request({
        url: '/region/getTYRegion.do',
        data
    })
}

/*
    creator: hxz 2024-11-13 获取客户的专属商品和通用商品
    params: customer: 2971
*/
export function insertCommodityByTypeForContract(data) {
    return request({
        url: '/sales/insertCommodityByTypeForContract.do',
        data
    })
}


// creator: hxz 2024-11-28 新增客户
export function addPdCustomer(data) {
    return request({
        url: '/sales/addPdCustomer.do',
        data
    })
}


// creator: hxz 2024-12-01 客户详情
export function getPdCustomerOne(data) {
    return request({
        url: '/sales/getPdCustomerOne.do',
        data
    })
}

/*
creator: hxz 2024-12-02 客户管理数据 - 合同
    params:{
        customer: 2971
        type: 1-现在的， 2-已暂停或停用的， 3-已到期的合同
    }
*/
export function listContractBase(data) {
    return request({
        url: '/sales/listContractBase.do',
        data
    })
}

/*
creator: hxz 2024-12-01 获取暂停/恢复合作历史记录列表
    customerId: customerId
*/
export function getSuspendOperationList(data) {
    return request({
        url: '/sales/getSuspendOperationList.do',
        data
    })
}


/*
creator: hxz 2024-12-02 基本信息修改记录
    customerId: customerId
*/
export function getRecordBaseList(data) {
    return request({
        url: '/sales/getRecordBaseList.do',
        data
    })
}


/*
creator: hxz 2024-12-02 开票信息修改记录
    customerId: 3020
*/
export function getRecordInvoiceList(data) {
    return request({
        url: '/sales/getRecordInvoiceList.do',
        data
    })
}


/*
creator: hxz 2024-12-02 开票信息修改记录 详情
    customerId: 3020
*/
export function getRecordInvoiceDetails(data) {
    return request({
        url: '/sales/getRecordInvoiceDetails.do',
        data
    })
}


/*
creator: hxz 2024-12-02 合同续约记录
    primaryId: 54
*/
export function contractBaseSignRecord(data) {
    return request({
        url: '/sales/contractBaseSignRecord.do',
        data
    })
}


/*
creator: hxz 2024-12-02 客户管理数据
    customerId: customerId
*/
export function getUpdatePageData(data) {
    return request({
        url: '/sales/getUpdatePageData.do',
        data
    })
}


/*
creator: hxz 2024-12-02 客户管理 - 修改基本信息

*/
export function updatePdCustomerBase(data) {
    return request({
        url: '/sales/updatePdCustomerBase.do',
        data
    })
}

/*
creator: hxz 2024-12-02 客户管理 - 校验手机号
    params:
        mobile
        id
*/
export function getSaleCtrlsByMobile(data) {
    return request({
        url: '/special/getSaleCtrlsByMobile.do',
        data
    })
}


/*
creator: hxz 2024-12-02 客户管理 - 修改开票信息
    params:
        id: 2971
        invoiceName: 开票公司名称
        invoiceAddress: 开票地址
        telephone: ***********
        bankName: 中国农业银行
        bank_no: **********
        taxpayerID: AD111
        invoiceRequire: 2
*/
export function updatePdCustomerInvoice(data) {
    return request({
        url: '/sales/updatePdCustomerInvoice.do',
        data
    })
}



/*
creator: hxz 2024-12-02 合同详情
    params:
        id: 6
*/
export function contractBaseMes(data) {
    return request({
        url: '/sales/contractBaseMes.do',
        data
    })
}


/*
creator: hxz 2024-12-02 合同详情
    params:
        id: 6
*/
export function editCustomerAddress(data) {
    return request({
        url: '/sales/editCustomerAddress.do',
        data
    })
}


/*
creator: hxz 2024-12-02 新增合同
params:
    customer: 2971
    sn: 管理新增
    contractSignTime: 2024-12-05
    contractStartTime: 2024-12-04
    contractEndTime: 2024-12-31
    type: 1
    memo: 补班
    contractBaseImages: [
        {"uplaodPath":"org/4238/202412/cf045546ceb84a15ba5f6b157c56014b.png","type":1,"title":"logo.png","operation":1},
        {"uplaodPath":"org/4238/202412/370b3edb306e4ab0b1b95851ab8f36c7.png","type":1,"title":"tip.png","operation":1}
    ]
    productZSList: []
    productTYList: [{"commodity":1666 }]
    filePath: org/4238/202412/90c6744f4fb64ba4bcc96182fcabc3cc.xlsx
    fileName: 2024年天津贝塔员工考勤表.xlsx
*/
export function insertContractBase(data) {
    return request({
        url: '/sales/insertContractBase.do',
        data
    })
}


/*
creator: hxz 2024-12-02 修改合同
params:
    customer: 2971
    sn: www2
    contractSignTime: 2024-12-04
    contractStartTime: 2024-12-04
    contractEndTime: 2024-12-17
    type: 1
    memo: wwwwbeizhu
    contractBaseImages: [
        {"uplaodPath":"org/4238/202412/0a3b13725d0d4ff8bcf383dec286cd16.png","type":1,"title":"logo.png","id":4,"operation":4},
        {"uplaodPath":"org/4238/202412/56f67a033771470ca1799dae27e56221.jpeg","type":1,"title":"e5bfb5dd83ac5b2e29caa41c471b44a9.jpeg","id":5,"operation":4}
    ]
    productZSList: []
    productTYList: [{"commodity":1666}]
    filePath: org/4238/202412/7f1b7e15c786457eac51b7cf7a3179ca.xlsx
    fileName: 2024年天津贝塔员工考勤表.xlsx
    id: 6


*/
export function upContractBase(data) {
    return request({
        url: '/sales/upContractBase.do',
        data
    })
}

/*
creator: hxz 2024-12-02 续约合同
params:
    customer: 2971
    sn: www2
    contractSignTime: 2024-12-04
    contractStartTime: 2025-01-01
    contractEndTime: 2025-01-08
    type: 1
    memo: wwwwbeizhu
    contractBaseImages: [{"uplaodPath":"org/4238/202412/0a3b13725d0d4ff8bcf383dec286cd16.png","type":1,"title":"logo.png","operation":1},{"uplaodPath":"org/4238/202412/56f67a033771470ca1799dae27e56221.jpeg","type":1,"title":"e5bfb5dd83ac5b2e29caa41c471b44a9.jpeg","operation":1}]
    productZSList: []
    productTYList: [{"commodity":1666}]
    filePath: org/4238/202412/7f1b7e15c786457eac51b7cf7a3179ca.xlsx
    fileName: 2024年天津贝塔员工考勤表.xlsx
    id: 6
*/

export function renewalContractForCommodity(data) {
    return request({
        url: '/sales/renewalContractForCommodity.do',
        data
    })
}


/*
* creator: hxz 2024-12-12 包含某商品的合同
* params: id: 商品id
*
* */
export function listContractByCommodity(data) {
    return request({
        url: '/sales/listContractByCommodity.do',
        data
    })
}


/*
* creator: hxz 2024-12-12 合同修改记录
* params: id: 合同id
*
* */
export function contractBaseHistory(data) {
    return request({
        url: '/sales/contractBaseHistory.do',
        data
    })
}


/*
* creator: hxz 2024-12-12 合同修改记录 查看详情
* params: contractHisId: 记录id
*
* */
export function contractBaseHisMes(data) {
    return request({
        url: '/sales/contractBaseHisMes.do',
        data
    })
}

/*
* creator: hxz 2024-12-12 合同 暂停
* params: id : 合同id
*
* */
export function terminateContract(data) {
    return request({
        url: '/sales/terminateContract.do',
        data
    })
}


/*
* creator: hxz 2024-12-12 合同 恢复
* params: id : 合同id ， type: 1
*
* */
export function reStartContract(data) {
    return request({
        url: '/sales/reStartContract.do',
        data
    })
}


/*
* creator: hxz 2024-12-12 管理-新增 收货地址
* params:
**** adress 情况
*   customerId: 2979
    shAddress: {
        "type":1,"address":"收货地址 manageAdd", "contact":"李飞","customerContact":1492,"mobile":"李飞"}
**** area 情况
*   customerId: 2979,
              fpAddress: {"address":"邮寄地址","contact":"管理新增","postcode":"445566","mobile":"1540000999"}

* */
export function addCustomerAddress(data) {
    return request({
        url: '/sales/addCustomerAddress.do',
        data
    })
}



/*
* creator: hxz 2024-12-18 管理 - 新增联系人
* params:
    tags: 收货人
    name: 姓名
    post: 职位
    mobile: 15699997777
    visitCard: org/4312/202412/d75302b71fbf4601a43c24ed00725b94.jpg
    socialList: [{"code":"<EMAIL>","type":3,"name":"Email"},{"code":"weixin","type":4,"name":"微信"}]
    customer: 2979
* */

export function addCustomerContact(data) {
    return request({
        url: '/sales/addCustomerContact.do',
        data
    })
}

/*
* creator: hxz 2024-12-18 管理 - 删除联系人
* params:
    contactId: 1509
* */
export function deleteCustomerContact(data) {
    return request({
        url: '/sales/deleteCustomerContact.do',
        data
    })
}



/*
* creator: hxz 2024-12-18 管理 - 修改地址
* params:
*   address情况
    id: 1902
    type: 1
    address: 333gai
    contact: 3434
    mobile: 3434

    * area 情况
    * type: 3
        address: 山西省长治市沁源县
        contact: 管理新增
        mobile: 1540000999
        customerContact: 1503
        path: 140000000000,140400000000,140431000000
        regionCode: 140431000000
        requirements: x特殊要求改
        id: 1910
    *
    *
* */
export function updateCustomerAddress(data) {
    return request({
        url: '/sales/updateCustomerAddress.do',
        data
    })
}

/*
* creator: hxz 2024-12-18 管理 - 停用、启用 收货地址
* params:
    addressId: 1902
    enabled: 0-停用，1-启用
*/
export function startOrStopAddress(data) {
    return request({
        url: '/sales/startOrStopAddress.do',
        data
    })
}



/*
* creator: hxz 2024-12-18 管理 - 已停用的收货地址
* params:
    customerId: 2979
    type: 1
*/
export function getSuspendAddress(data) {
    return request({
        url: '/sales/getSuspendAddress.do',
        data
    })
}



/*
* creator: hxz 2024-12-18 管理 - 收货地址的修改记录
* params:
    addressId: 1906
*/
export function getRecordAddressList(data) {
    return request({
        url: '/sales/getRecordAddressList.do',
        data
    })
}


/*
* creator: hxz 2024-12-18 管理 - 地址修改记录  详情查看
* params:
    id: 2298
    frontId: 2293
*/
export function getRecordShAddressDetails(data) {
    return request({
        url: '/sales/getRecordShAddressDetails.do',
        data
    })
}

/*
* creator: hxz 2024-12-18 管理 - 发票地址修改记录  详情查看
* params:
    id: 2298
    frontId: 2293
*/
export function getRecordFpAddressDetails(data) {
    return request({
        url: '/sales/getRecordFpAddressDetails.do',
        data
    })
}

/*
* creator: hxz 2024-12-18 管理 - 联系人详情
* params:
    contactId: 1503
*/
export function getContactsSocial(data) {
    return request({
        url: '/sales/getContactsSocial.do',
        data
    })
}


/*
* creator: hxz 2024-12-18   联系人 名片
* params:
    contactId: 1526
    * res:
    * visitCard
*/
export function getContactCard(data) {
    return request({
        url: '/sales/getContactCard.do',
        data
    })
}




/*
* creator: hxz 2024-12-18 管理 - 联系人修改
* params:
    tags: 销售其他添加
    name: xingmingGai
    post: zhiwei
    mobile: shouji
    visitCard: org/4312/202412/d684fd615e5a4721a1a12da1216d68e9.png
    socialList: [{"code":"zzz","type":9,"name":"自定义标签"},{"code":"13244445555","type":1,"name":"手机"},{"code":"<EMAIL>","type":3,"name":"Email"}]
    contactId: 1518
*/
export function updateContactsSocialAndCard(data) {
    return request({
        url: '/sales/updateContactsSocialAndCard.do',
        data
    })
}



/*
* creator: hxz 2024-12-18 管理 - 联系人修改
* params:
    customerId: 2979
*/
export function getDeleteContactsList(data) {
    return request({
        url: '/sales/getDeleteContactsList.do',
        data
    })
}



/*
* creator: hxz 2024-12-18 管理 - 联系人修改记录
* params:
    contactId: 1517
*/
export function getRecordContactList(data) {
    return request({
        url: '/sales/getRecordContactList.do',
        data
    })
}

/*
* creator: hxz 2024-12-18 管理 - 联系人修改记录 详情
* params:
    id: 1834
    frontId: 1832
*/
export function getRecordContactDetails(data) {
    return request({
        url: '/sales/getRecordContactDetails.do',
        data
    })
}


/*
* creator: hxz 2024-12-18 删除客户
* params:
    id: 1834
*/
export function deletePdCustomerOne(data) {
    return request({
        url: '/sales/deletePdCustomerOne.do',
        data
    })
}



/*
* creator: hxz 2024-12-23 客户 暂停合作
* params:
    customerId: 2995
    state: 1 - 暂停合作  0 - 恢复合作
*/
export function suspendCooperation(data) {
    return request({
        url: '/sales/suspendCooperation.do',
        data
    })
}




/*
* creator: hxz 2024-12-23 客户 暂停合作 列表
* params:
    currPage: 1
    pageSize: 20
*/
export function getSuspendCustomer(data) {
    return request({
        url: '/sales/getSuspendCustomer.do',
        data
    })
}

/*
* creator: hxz 2024-12-23 公司总揽 客户 暂停合作 列表
* params:
    currPage: 1
    pageSize: 20
*/
export function getAllSuspendCustomerList(data) {
    return request({
        url: '/sales/getAllSuspendCustomerList.do',
        data
    })
}



/*
* creator: hxz 2024-12-23 获取机构列表
* params:
    customerId: 2980
*/
export function getCustomerOrganizationsByCustomerId(data) {
    return request({
        url: '/special/getCustomerOrganizationsByCustomerId.do',
        data
    })
}


/*
* creator: hxz 2024-12-23 新增机构
* params:
    fullName: 机构名称
    name: 机名
    address: 机构地址
    supervisorName: 董事长
    supervisorMobile: 17800000000
    uploadStorageType: NFS
    packageId: 2
    customerId: 2980
*/
export function addOrgApply(data) {
    return request({
        url: '/special/addOrgApply.do',
        data
    })
}


/*
* creator: hxz 2024-12-23 新增机构 - 获取产品列表
* params:
*/
export function getSelectMpPackages(data) {
    return request({
        url: '/productSetting/getSelectMpPackages.do',
        data
    })
}


/*
* creator: hxz 2024-12-23 机构信息
* params:
*   id: 519
*/
export function getCustomerOrganizationInfo(data) {
    return request({
        url: '/special/getCustomerOrganizationInfo.do',
        data
    })
}


/*
* creator: hxz 2024-12-23 机构信息修改
* params:
*   id: 519
    fullName: 测试机构1
    name: 测试机构1
    address:
    supervisorName: 董事长
    supervisorMobile: 17800000000
    uploadStorageType: NFS
*/
export function updateCustomerOrganization(data) {
    return request({
        url: '/special/updateCustomerOrganization.do',
        data
    })
}


/*
* creator: hxz 2024-12-23 产品信息
* params:
*   id: 519
    haveModule: false
*/
export function getIncrementOrgPopedom(data) {
    return request({
        url: '/special/getIncrementOrgPopedom.do',
        data
    })
}


/*
* creator: hxz 2024-12-23 产品信息修改
* params:
*   packageId: 1
    organizationId: 414
*/
export function updateOrgPopedoms(data) {
    return request({
        url: '/special/updateOrgPopedoms.do',
        data
    })
}


/*
* creator: hxz 2024-12-23 产品信息修改
* params:
*   packageId: 1
    organizationId: 414
*/
export function getMpPackagesInfo(data) {
    return request({
        url: '/thali/getMpPackagesInfo.do',
        data
    })
}


/*
* creator: hxz 2024-12-23 产品信息修改
* params:
*   id: 28
*/
export function getModuleInfo(data) {
    return request({
        url: '/thali/getModuleInfo.do',
        data
    })
}


/*
* creator: hxz 2024-12-23 产品信息修改
* params:
*   id: 519
*/
export function getIncreaseModules(data) {
    return request({
        url: '/thali/getIncreaseModules.do',
        data
    })
}



/*
* creator: hxz 2024-12-23 查看套餐
* params:
*   id: 519
*/
export function getModulesByMpSetId(data) {
    return request({
        url: '/thali/getModulesByMpSetId.do',
        data
    })
}


/*
* creator: hxz 2024-12-23 查看套餐
* params:
*   id: 519
*/
export function addIncrementApply(data) {
    return request({
        url: '/thali/addIncrementApply.do',
        data
    })
}


/*
* creator: hxz 2024-12-23 查看套餐
* params:
*   ids: 0
*/
export function getMpSets(data) {
    return request({
        url: '/thali/getMpSets.do',
        data
    })
}


/*
* creator: hxz 2024-12-23 登录记录
* params:
*   ids: 0
*/
export function getOrganizationLogins(data) {
    return request({
        url: '/special/getOrganizationLogins.do',
        data
    })
}



/*
* creator: hxz 2024-12-23 空间与流量
* params:
*   ids: 0
*/
export function getSpaceTrafficInfo(data) {
    return request({
        url: '/st/getSpaceTrafficInfo.do',
        data
    })
}



/*
* creator: hxz 2024-12-23 暂停服务
* params:
*   ids: 0
*/
export function addOrgOutOfServiceApply(data) {
    return request({
        url: '/special/addOrgOutOfServiceApply.do',
        data
    })
}



/*
* creator: hxz 2024-12-23  已暂停服务的机构
* params:
*   pageSize: 20
    currentPageNo: 1
*/
export function getOutOfOrgList(data) {
    return request({
        url: '/special/getOutOfOrgList.do',
        data
    })
}

/*
* creator: hxz 2024-12-23  恢复服务
* params:
*   organizationId: 128
*/
export function addOrgRestoreServiceApply(data) {
    return request({
        url: '/special/addOrgRestoreServiceApply.do',
        data
    })
}


/*
* creator: hxz 2024-12-23  查看修改详情
* params:
*   id: 3355
    frontId: 3322
*/
export function getRecordBaseDetails(data) {
    return request({
        url: '/sales/getRecordBaseDetails.do',
        data
    })
}






/*
* creator: hxz 2025-03-13 特殊机构 机构管理
* 传入参数： pageSize 每页条数 currentPageNo 页数
*           name 名称 (按名称查询 时才传)
*           mobile 手机号 (按手机号查询 时才传)
* 返回值： pageInfo 分页信息 ， orgList 机构列表
* 调用示例： https://dvm01.btransmission.com/thali/getOrgManageList.do?pageSize=20&currentPageNo=1
*/
export function getOrgManageList(data) {
    return request({
        url: '/thali/getOrgManageList.do',
        data
    })
}



/*
* creator: hxz 2025-03-13  特殊机构 机构管理 - 暂停服务机构列表
* 传入参数： pageSize 每页条数 currentPageNo 页数
*           name 名称(按名称查询 时才传)
* 返回值：  pageInfo 分页信息 ， orgList 机构列表
* 调用示例： https://dvm01.btransmission.com/thali/getLockedOrgManageList.do?pageSize=20&currentPageNo=1
*/
export function getLockedOrgManageList(data) {
    return request({
        url: '/thali/getLockedOrgManageList.do',
        data
    })
}



/*
* creator: hxz 2025-03-13   机构信息变动记录
* 传入参数： "id": orgHisId, "pageSize": 20, "currentPageNo": 1
* 返回值：organizationHistoryList 列表
*
*/
export function getOrganizationHistories(data) {
    return request({
        url: '/site/getOrganizationHistories.do',
        data
    })
}

/*
* creator: hxz 2025-03-13   机构信息变动记录 详情
* 传入参数： "id": orgBegId,
* 返回值：organizationHistoryList 列表
*
*/
export function getOrganizationHistoryInfo(data) {
    return request({
        url: '/site/getOrganizationHistoryInfo.do',
        data
    })
}


/*
* creator: hxz 2025-05-13  暂停服务/恢复服务的操作记录
* 传入参数： id: 547
* 返回值：data 列表
*
*/
export function orgOutOfRestoreHistories(data) {
    return request({
        url: '/special/orgOutOfRestoreHistories.do',
        data
    })
}


/*
* creator: hxz 2025-05-22   特殊机构 机构管理 可重命名菜单列表
* 传入参数： oid 机构id
* 返回值：{
* "page":null,
* "success":1,
* "data":[{"valid":null,"name":"公司总览","mid":"ba","checked":false,"pid":"0","disabled":false,"url":"","users":null,"subPopdoms":[{"valid":null,"name":"实物资产","mid":"bc","checked":false,"pid":"ba","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"财会","mid":"bb","checked":false,"pid":"ba","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"车务","mid":"bca","checked":false,"pid":"ba","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"文件与资料","mid":"bd","checked":false,"pid":"ba","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"职工","mid":"be","checked":false,"pid":"ba","disabled":false,"url":"","users":null,"subPopdoms":[{"valid":null,"name":"登录记录","mid":"bea","checked":false,"pid":"be","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/loginRecord","users":null,"subPopdoms":[]},{"valid":null,"name":"职工档案","mid":"beb","checked":false,"pid":"be","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"考勤","mid":"bec","checked":false,"pid":"be","disabled":false,"url":"../workAttendance/companyOverviewAttendance.do","users":null,"subPopdoms":[]},{"valid":null,"name":"请假动态","mid":"bed","checked":false,"pid":"be","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"加班动态","mid":"bee","checked":false,"pid":"be","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"工资","mid":"bef","checked":false,"pid":"be","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"工作记录","mid":"beg","checked":false,"pid":"be","disabled":false,"url":"../mywork/toBrowsePage.do","users":null,"subPopdoms":[]},{"valid":null,"name":"近7日打卡记录","mid":"beh","checked":false,"pid":"be","disabled":false,"url":"../","users":null,"subPopdoms":[]}]},{"valid":null,"name":"客户","mid":"bf","checked":false,"pid":"ba","disabled":false,"url":"../","users":null,"subPopdoms":[{"valid":null,"name":"客户信息","mid":"bfa","checked":false,"pid":"bf","disabled":false,"url":"../sales/overviewSlCustomerPage.do","users":null,"subPopdoms":[]},{"valid":null,"name":"订购信息","mid":"bfb","checked":false,"pid":"bf","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"账务信息","mid":"bfc","checked":false,"pid":"bf","disabled":false,"url":"../","users":null,"subPopdoms":[]}]},{"valid":null,"name":"财务","mid":"bg","checked":false,"pid":"ba","disabled":false,"url":"../","users":null,"subPopdoms":[{"valid":null,"name":"资金","mid":"bga","checked":false,"pid":"bg","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/overviewIndexForAdmin","users":null,"subPopdoms":[]},{"valid":null,"name":"票据","mid":"bgb","checked":false,"pid":"bg","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/overviewReplyBill","users":null,"subPopdoms":[]}]},{"valid":null,"name":"订单","mid":"bh","checked":false,"pid":"ba","disabled":false,"url":"../sales/overviewOrderPage.do","users":null,"subPopdoms":[]},{"valid":null,"name":"权限","mid":"bi","checked":false,"pid":"ba","disabled":false,"url":"../popedom/toAuthority.do","users":null,"subPopdoms":[{"valid":null,"name":"审批权限","mid":"bia","checked":false,"pid":"bi","disabled":false,"url":"../","users":null,"subPopdoms":[]}]},{"valid":null,"name":"投诉","mid":"bj","checked":false,"pid":"ba","disabled":false,"url":"../complaint/companyOverviewComplaint.do","users":null,"subPopdoms":[]},{"valid":null,"name":"商品","mid":"bk","checked":false,"pid":"ba","disabled":false,"url":"../sales/overviewProductPage.do","users":null,"subPopdoms":[]},{"valid":null,"name":"销售统计","mid":"bl","checked":false,"pid":"ba","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"仓库","mid":"bm","checked":false,"pid":"ba","disabled":false,"url":"","users":null,"subPopdoms":[{"valid":null,"name":"原辅材料库","mid":"bma","checked":false,"pid":"bm","disabled":false,"url":"../mt/ovRawMt","users":null,"subPopdoms":[]},{"valid":null,"name":"成品库","mid":"bmb","checked":false,"pid":"bm","disabled":false,"url":"../finishedOverview/home.do","users":null,"subPopdoms":[]}]}]},{"valid":null,"name":"手机端工作","mid":"ca","checked":false,"pid":"0","disabled":false,"url":null,"users":null,"subPopdoms":[{"valid":null,"name":"浏览管理","mid":"cb","checked":false,"pid":"ca","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"车务管理","mid":"cc","checked":false,"pid":"ca","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"车务支出","mid":"cd","checked":false,"pid":"ca","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"会议管理","mid":"ce","checked":false,"pid":"ca","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"资源设置","mid":"cf","checked":false,"pid":"ca","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"资源管理","mid":"cg","checked":false,"pid":"ca","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"加油卡","mid":"ch","checked":false,"pid":"ca","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"领料","mid":"ci","checked":false,"pid":"ca","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"报税事务","mid":"cj","checked":false,"pid":"ca","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"会计杂项","mid":"ck","checked":false,"pid":"ca","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"收款单位","mid":"cl","checked":false,"pid":"ca","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"考核成绩公示板","mid":"cm","checked":false,"pid":"ca","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"公共信息管理","mid":"cn","checked":false,"pid":"ca","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"公共信息","mid":"co","checked":false,"pid":"ca","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"参展商","mid":"cp","checked":false,"pid":"ca","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"展会宝","mid":"cq","checked":false,"pid":"ca","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"成品库库位设置","mid":"cr","checked":false,"pid":"ca","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"半成品库库位设置","mid":"cs","checked":false,"pid":"ca","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"原辅材料库库位设置","mid":"ct","checked":false,"pid":"ca","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"办公用品存放位置设置","mid":"cu","checked":false,"pid":"ca","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"考勤宝","mid":"cv","checked":false,"pid":"ca","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"考勤打卡","mid":"cw","checked":false,"pid":"ca","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"销售计划","mid":"cx","checked":false,"pid":"ca","disabled":false,"url":"../","users":null,"subPopdoms":[]}]},{"valid":null,"name":"权限管理","mid":"ea","checked":false,"pid":"0","disabled":false,"url":"","users":null,"subPopdoms":[{"valid":null,"name":"权限设置","mid":"eb","checked":false,"pid":"ea","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/authority","users":null,"subPopdoms":[]},{"valid":null,"name":"审批设置","mid":"ec","checked":false,"pid":"ea","disabled":false,"url":"../popedom/toApprovalPowerSetting.do","users":null,"subPopdoms":[]},{"valid":null,"name":"当前权限","mid":"ed","checked":false,"pid":"ea","disabled":false,"url":"../popedom/toCurrentRoutinePower.do","users":null,"subPopdoms":[]},{"valid":null,"name":"审批查看","mid":"ee","checked":false,"pid":"ea","disabled":false,"url":"../popedom/toPowerSetting.do","users":null,"subPopdoms":[]},{"valid":null,"name":"职工权限","mid":"ef","checked":false,"pid":"ea","disabled":false,"url":"../popedom/toWorkerAuthority.do","users":null,"subPopdoms":[]},{"valid":null,"name":"修改记录","mid":"eg","checked":false,"pid":"ea","disabled":false,"url":"../popedom/toUpdateLog.do","users":null,"subPopdoms":[]},{"valid":null,"name":"初始化","mid":"eh","checked":false,"pid":"ea","disabled":false,"url":"../initialize/toInitAuthority.do","users":null,"subPopdoms":[]}]},{"valid":null,"name":"总务管理","mid":"ka","checked":false,"pid":"0","disabled":false,"url":null,"users":null,"subPopdoms":[{"valid":null,"name":"职工档案","mid":"kb","checked":false,"pid":"ka","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/employeeIndex","users":null,"subPopdoms":[]},{"valid":null,"name":"跨机构管理","mid":"kc","checked":false,"pid":"ka","disabled":false,"url":"../sonOrg/toInteragencyManagement.do","users":null,"subPopdoms":[]},{"valid":null,"name":"岗位管理","mid":"kd","checked":false,"pid":"ka","disabled":false,"url":"../general/postManagement.do","users":null,"subPopdoms":[]},{"valid":null,"name":"固定资产","mid":"kf","checked":false,"pid":"ka","disabled":false,"url":"../capitalAsserts/capitalAsserts.do","users":null,"subPopdoms":[]},{"valid":null,"name":"企业信息","mid":"kg","checked":false,"pid":"ka","disabled":false,"url":"../business/toPage.do","users":null,"subPopdoms":[]},{"valid":null,"name":"分工设置","mid":"ki","checked":false,"pid":"ka","disabled":false,"url":"../coreSetting/toDivisionSetting.do","users":null,"subPopdoms":[]},{"valid":null,"name":"考勤管理","mid":"kj","checked":false,"pid":"ka","disabled":false,"url":"../workAttendance/attendance.do","users":null,"subPopdoms":[]},{"valid":null,"name":"修改记录","mid":"kk","checked":false,"pid":"ka","disabled":false,"url":"../workAttendance/attendanceLog.do","users":null,"subPopdoms":[]},{"valid":null,"name":"招聘管理","mid":"kl","checked":false,"pid":"ka","disabled":false,"url":"../general/goRecruitManagement.do","users":null,"subPopdoms":[]}]},{"valid":null,"name":"财务管理","mid":"la","checked":false,"pid":"0","disabled":false,"url":null,"users":null,"subPopdoms":[{"valid":null,"name":"数据查看","mid":"lb","checked":false,"pid":"la","disabled":false,"url":"../sys/toAdminIndex.do","users":null,"subPopdoms":[]},{"valid":null,"name":"账户管理","mid":"ld","checked":false,"pid":"la","disabled":false,"url":"../finance/financeManage.do","users":null,"subPopdoms":[]},{"valid":null,"name":"工资管理","mid":"le","checked":false,"pid":"la","disabled":false,"url":"../salary/goSalaryIndex.do","users":null,"subPopdoms":[]},{"valid":null,"name":"支票管理","mid":"lm","checked":false,"pid":"la","disabled":false,"url":"../financeJump/checkManage.do","users":null,"subPopdoms":[]},{"valid":null,"name":"数据录入","mid":"ln","checked":false,"pid":"la","disabled":false,"url":"../financeJump/dataImport.do","users":null,"subPopdoms":[]},{"valid":null,"name":"回款票据","mid":"lo","checked":false,"pid":"la","disabled":false,"url":"../financeJump/replyBill.do","users":null,"subPopdoms":[]},{"valid":null,"name":"报销设置","mid":"lp","checked":false,"pid":"la","disabled":false,"url":"../financeJump/expenseReceive.do","users":null,"subPopdoms":[]},{"valid":null,"name":"发票管理","mid":"lr","checked":false,"pid":"la","disabled":false,"url":"../invoice/toPage.do","users":null,"subPopdoms":[]},{"valid":null,"name":"发票设置","mid":"ls","checked":false,"pid":"la","disabled":false,"url":"../invoiceSetting/toInvoiceSettingPage.do","users":null,"subPopdoms":[]}]},{"valid":null,"name":"销售管理","mid":"qa","checked":false,"pid":"0","disabled":false,"url":null,"users":null,"subPopdoms":[{"valid":null,"name":"通用型商品","mid":"qh","checked":false,"pid":"qa","disabled":false,"url":"../commodity/commonGoods.do","users":null,"subPopdoms":[]},{"valid":null,"name":"专属商品","mid":"qe","checked":false,"pid":"qa","disabled":false,"url":"../commodity/basic.do","users":null,"subPopdoms":[]},{"valid":null,"name":"客户管理","mid":"qb","checked":false,"pid":"qa","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/customerManage","users":null,"subPopdoms":[]},{"valid":null,"name":"订单管理","mid":"qc","checked":false,"pid":"qa","disabled":false,"url":"../sales/orderManage.do","users":null,"subPopdoms":[]},{"valid":null,"name":"订单评审","mid":"qd","checked":false,"pid":"qa","disabled":false,"url":"../sales/orderScan.do","users":null,"subPopdoms":[]},{"valid":null,"name":"销售分工","mid":"qf","checked":false,"pid":"qa","disabled":false,"url":"../sales/salesDivision.do","users":null,"subPopdoms":[]},{"valid":null,"name":"发票送达管理","mid":"qg","checked":false,"pid":"qa","disabled":false,"url":"../sale/invoiceSend.do","users":null,"subPopdoms":[]},{"valid":null,"name":"服务项目","mid":"qi","checked":false,"pid":"qa","disabled":false,"url":"../saleService/index.do","users":null,"subPopdoms":[]},{"valid":null,"name":"服务套餐","mid":"qj","checked":false,"pid":"qa","disabled":false,"url":"../service/servicePackagePage.do","users":null,"subPopdoms":[]},{"valid":null,"name":"服务合同","mid":"qk","checked":false,"pid":"qa","disabled":false,"url":"../saleContract/index.do","users":null,"subPopdoms":[]},{"valid":null,"name":"销售合同","mid":"qx","checked":false,"pid":"qa","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/sale_contract","users":null,"subPopdoms":[]},{"valid":null,"name":"机构管理","mid":"qz","checked":false,"pid":"qa","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/orgManage","users":null,"subPopdoms":[]}]},{"valid":null,"name":"生产管理","mid":"ua","checked":false,"pid":"0","disabled":false,"url":"","users":null,"subPopdoms":[{"valid":null,"name":"订单评审","mid":"ub","checked":false,"pid":"ua","disabled":false,"url":"../sr/toReview.do","users":null,"subPopdoms":[]},{"valid":null,"name":"订单查看","mid":"uc","checked":false,"pid":"ua","disabled":false,"url":"../sr/toScan.do","users":null,"subPopdoms":[]},{"valid":null,"name":"入库申请","mid":"ud","checked":false,"pid":"ua","disabled":false,"url":"../mtStock/mtStockIndex.do","users":null,"subPopdoms":[]},{"valid":null,"name":"让步申请","mid":"ue","checked":false,"pid":"ua","disabled":false,"url":"../mtStock/mtStockGiveInOne.do","users":null,"subPopdoms":[]}]},{"valid":null,"name":"商品管理","mid":"pa","checked":false,"pid":"0","disabled":false,"url":null,"users":null,"subPopdoms":[{"valid":null,"name":"工序信息","mid":"pd","checked":false,"pid":"pa","disabled":false,"url":"../commodity/process.do","users":null,"subPopdoms":[]},{"valid":null,"name":"包装信息","mid":"pe","checked":false,"pid":"pa","disabled":false,"url":"../pack/packIndex.do","users":null,"subPopdoms":[]}]},{"valid":null,"name":"工序管理","mid":"pl","checked":false,"pid":"0","disabled":false,"url":null,"users":null,"subPopdoms":[{"valid":null,"name":"基础设置","mid":"pm","checked":false,"pid":"pl","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/processSet","users":null,"subPopdoms":[]},{"valid":null,"name":"工序查看","mid":"pn","checked":false,"pid":"pl","disabled":false,"url":"../processView/toProcessView.do","users":null,"subPopdoms":[]}]},{"valid":null,"name":"工序初始化","mid":"po","checked":false,"pid":"0","disabled":false,"url":null,"users":null,"subPopdoms":[{"valid":null,"name":"产品特性","mid":"pp","checked":false,"pid":"po","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/productFeatures","users":null,"subPopdoms":[]}]},{"valid":null,"name":"物料管理","mid":"oa","checked":false,"pid":"0","disabled":false,"url":null,"users":null,"subPopdoms":[{"valid":null,"name":"物料信息","mid":"ob","checked":false,"pid":"oa","disabled":false,"url":"../material/materialManage.do","users":null,"subPopdoms":[]},{"valid":null,"name":"供应商名录","mid":"oc","checked":false,"pid":"oa","disabled":false,"url":"../material/supplierDirectory.do","users":null,"subPopdoms":[]}]},{"valid":null,"name":"配方管理","mid":"ui","checked":false,"pid":"0","disabled":false,"url":"","users":null,"subPopdoms":[{"valid":null,"name":"配方","mid":"uj","checked":false,"pid":"ui","disabled":false,"url":"../formula/formula.do","users":null,"subPopdoms":[]},{"valid":null,"name":"材料","mid":"uk","checked":false,"pid":"ui","disabled":false,"url":"../formula/materialsInFormula.do","users":null,"subPopdoms":[]}]},{"valid":null,"name":"启用管理","mid":"ia","checked":false,"pid":"0","disabled":false,"url":"../startManage/toStartManages.do","users":null,"subPopdoms":[]},{"valid":null,"name":"直播助手","mid":"ib","checked":false,"pid":"0","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"项目管理","mid":"ta","checked":false,"pid":"0","disabled":false,"url":"","users":null,"subPopdoms":[{"valid":null,"name":"综合管理","mid":"tb","checked":false,"pid":"ta","disabled":false,"url":"../project/toProjectBaseManage.do","users":null,"subPopdoms":[]},{"valid":null,"name":"立案","mid":"tc","checked":false,"pid":"ta","disabled":false,"url":"../project/toProjectBaseIndex.do","users":null,"subPopdoms":[]}]},{"valid":null,"name":"投诉管理","mid":"fb","checked":false,"pid":"0","disabled":false,"url":"../coreSetting/toIntegrateManage.do","users":null,"subPopdoms":[]},{"valid":null,"name":"投诉录入","mid":"fc","checked":false,"pid":"0","disabled":false,"url":"../coreSetting/toComplaintFiling.do","users":null,"subPopdoms":[]},{"valid":null,"name":"投诉处理","mid":"fd","checked":false,"pid":"0","disabled":false,"url":"../coreSetting/toComplaintHandling.do","users":null,"subPopdoms":[]},{"valid":null,"name":"持续改进","mid":"ha","checked":false,"pid":"0","disabled":false,"url":null,"users":null,"subPopdoms":[{"valid":null,"name":"综合管理","mid":"hb","checked":false,"pid":"ha","disabled":false,"url":"../improvement/integrateManage.do","users":null,"subPopdoms":[]},{"valid":null,"name":"立案","mid":"hc","checked":false,"pid":"ha","disabled":false,"url":"../improvement/improvementFiling.do","users":null,"subPopdoms":[]}]},{"valid":null,"name":"备忘与日程","mid":"ja","checked":false,"pid":"0","disabled":false,"url":"","users":null,"subPopdoms":[{"valid":null,"name":"事件管理","mid":"jc","checked":false,"pid":"ja","disabled":false,"url":"../schedule/toScheduleManage.do","users":null,"subPopdoms":[]}]},{"valid":null,"name":"工作记录点评","mid":"km","checked":false,"pid":"0","disabled":false,"url":"../mywork/toWorkReviewPage.do","users":null,"subPopdoms":[]},{"valid":null,"name":"职工导入","mid":"ko","checked":false,"pid":"0","disabled":false,"url":"../org/toUserImport.do","users":null,"subPopdoms":[]},{"valid":null,"name":"企业需求调查","mid":"ku","checked":false,"pid":"0","disabled":false,"url":null,"users":null,"subPopdoms":[{"valid":null,"name":"二维码","mid":"kv","checked":false,"pid":"ku","disabled":false,"url":"../survey/goQRcode.do","users":null,"subPopdoms":[]},{"valid":null,"name":"信息调查","mid":"kw","checked":false,"pid":"ku","disabled":false,"url":"../survey/goSurveyAnswerList.do","users":null,"subPopdoms":[]}]},{"valid":null,"name":"调查管理","mid":"kx","checked":false,"pid":"0","disabled":false,"url":null,"users":null,"subPopdoms":[{"valid":null,"name":"问卷库","mid":"ky","checked":false,"pid":"kx","disabled":false,"url":"../investigationManage/goQuestionBank.do","users":null,"subPopdoms":[]},{"valid":null,"name":"调查管理","mid":"kz","checked":false,"pid":"kx","disabled":false,"url":"../investigationManage/goInvestigation.do","users":null,"subPopdoms":[]}]},{"valid":null,"name":"常规借款","mid":"lt","checked":false,"pid":"0","disabled":false,"url":"../loan/ordinaryLoanList.do","users":null,"subPopdoms":[]},{"valid":null,"name":"高管管理","mid":"mb","checked":false,"pid":"0","disabled":false,"url":"../sys/toManagesList.do","users":null,"subPopdoms":[]},{"valid":null,"name":"中枢管控","mid":"mc","checked":false,"pid":"0","disabled":false,"url":"../sys/toManagesList.do","users":null,"subPopdoms":[]},{"valid":null,"name":"冻结账号","mid":"md","checked":false,"pid":"0","disabled":false,"url":"../userLock/frozenAccount.do","users":null,"subPopdoms":[]},{"valid":null,"name":"购销统筹","mid":"ql","checked":false,"pid":"0","disabled":false,"url":"../sales/salesPlan.do","users":null,"subPopdoms":[]},{"valid":null,"name":"供应商","mid":"qm","checked":false,"pid":"0","disabled":false,"url":null,"users":null,"subPopdoms":[{"valid":null,"name":"常规信息","mid":"qn","checked":false,"pid":"qm","disabled":false,"url":"../supplier/index.do","users":null,"subPopdoms":[]},{"valid":null,"name":"MCI","mid":"qo","checked":false,"pid":"qm","disabled":false,"url":"../eis/index.do","users":null,"subPopdoms":[]},{"valid":null,"name":"采购合同","mid":"qy","checked":false,"pid":"qm","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/purchaseContract","users":null,"subPopdoms":[]}]},{"valid":null,"name":"装备器具","mid":"qp","checked":false,"pid":"0","disabled":false,"url":null,"users":null,"subPopdoms":[{"valid":null,"name":"装备清单","mid":"qq","checked":false,"pid":"qp","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/equipmentList","users":null,"subPopdoms":[]},{"valid":null,"name":"名称管理","mid":"qr","checked":false,"pid":"qp","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/nameManageMent","users":null,"subPopdoms":[]},{"valid":null,"name":"类别管理","mid":"qs","checked":false,"pid":"qp","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/categoryManageMent","users":null,"subPopdoms":[]},{"valid":null,"name":"供应商","mid":"qt","checked":false,"pid":"qp","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/equipMentSupplier","users":null,"subPopdoms":[]},{"valid":null,"name":"补录","mid":"qu","checked":false,"pid":"qp","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/equitmentAddRe","users":null,"subPopdoms":[]}]},{"valid":null,"name":"机器设备","mid":"qv","checked":false,"pid":"0","disabled":false,"url":null,"users":null,"subPopdoms":[{"valid":null,"name":"初始设置","mid":"qw","checked":false,"pid":"qv","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/initialSettings","users":null,"subPopdoms":[]}]},{"valid":null,"name":"文件与资料","mid":"ra","checked":false,"pid":"0","disabled":false,"url":null,"users":null,"subPopdoms":[{"valid":null,"name":"文件夹管理","mid":"rb","checked":false,"pid":"ra","disabled":false,"url":"../res/gongGao.do","users":null,"subPopdoms":[]},{"valid":null,"name":"文件管理","mid":"rc","checked":false,"pid":"ra","disabled":false,"url":"../res/wenJian.do","users":null,"subPopdoms":[]},{"valid":null,"name":"表格管理","mid":"rd","checked":false,"pid":"ra","disabled":false,"url":"../res/resFormManage.do","users":null,"subPopdoms":[]}]},{"valid":null,"name":"会计管理","mid":"sa","checked":false,"pid":"0","disabled":false,"url":"","users":null,"subPopdoms":[{"valid":null,"name":"科目管理","mid":"sb","checked":false,"pid":"sa","disabled":false,"url":"../accountant/subjectSet.do","users":null,"subPopdoms":[]},{"valid":null,"name":"建账管理","mid":"sc","checked":false,"pid":"sa","disabled":false,"url":"../accountant/buildMange.do","users":null,"subPopdoms":[]},{"valid":null,"name":"凭证管理","mid":"sd","checked":false,"pid":"sa","disabled":false,"url":"../accountant/voucherManage.do","users":null,"subPopdoms":[]},{"valid":null,"name":"会计录入","mid":"sg","checked":false,"pid":"sa","disabled":false,"url":"../accountant/accountantImport.do","users":null,"subPopdoms":[]},{"valid":null,"name":"结账管理","mid":"sh","checked":false,"pid":"sa","disabled":false,"url":"../accountant/settleAccounts.do","users":null,"subPopdoms":[]}]},{"valid":null,"name":"质量管理","mid":"va","checked":false,"pid":"0","disabled":false,"url":null,"users":null,"subPopdoms":[{"valid":null,"name":"入库检验","mid":"vb","checked":false,"pid":"va","disabled":false,"url":"../mtStock/MtStockQuality.do","users":null,"subPopdoms":[]}]},{"valid":null,"name":"初始化","mid":"vc","checked":false,"pid":"0","disabled":false,"url":"","users":null,"subPopdoms":[{"valid":null,"name":"权限","mid":"vd","checked":false,"pid":"vc","disabled":false,"url":"../initialize/toAuthorityMain.do","users":null,"subPopdoms":[]},{"valid":null,"name":"采购","mid":"ve","checked":false,"pid":"vc","disabled":false,"url":"../initialize/toPurchaseMain.do","users":null,"subPopdoms":[]},{"valid":null,"name":"原辅材料库","mid":"vf","checked":false,"pid":"vc","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/init_RAMtWh","users":null,"subPopdoms":[]},{"valid":null,"name":"货架与库位","mid":"vg","checked":false,"pid":"vc","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/init_shelvesWarehouse","users":null,"subPopdoms":[]},{"valid":null,"name":"财务","mid":"vh","checked":false,"pid":"vc","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/openFinance","users":null,"subPopdoms":[]},{"valid":null,"name":"会计","mid":"vi","checked":false,"pid":"vc","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/accountInit","users":null,"subPopdoms":[]},{"valid":null,"name":"装备器具","mid":"vj","checked":false,"pid":"vc","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/equipmentInit","users":null,"subPopdoms":[]}]},{"valid":null,"name":"技术管理","mid":"wa","checked":false,"pid":"0","disabled":false,"url":null,"users":null,"subPopdoms":[{"valid":null,"name":"产品档案","mid":"pc","checked":false,"pid":"wa","disabled":false,"url":"../commodity/composition.do","users":null,"subPopdoms":[]},{"valid":null,"name":"让步受理","mid":"wb","checked":false,"pid":"wa","disabled":false,"url":"../mtStock/mtStockGiveInTwo.do","users":null,"subPopdoms":[]},{"valid":null,"name":"关联管理","mid":"wc","checked":false,"pid":"wa","disabled":false,"url":"../commodity/relatedCommodities.do","users":null,"subPopdoms":[]},{"valid":null,"name":"构成处理","mid":"wd","checked":false,"pid":"wa","disabled":false,"url":"../mtStock/constituteManage.do","users":null,"subPopdoms":[]},{"valid":null,"name":"材料","mid":"we","checked":false,"pid":"wa","disabled":false,"url":"../mtStock/material.do","users":null,"subPopdoms":[]},{"valid":null,"name":"零组件档案","mid":"wf","checked":false,"pid":"wa","disabled":false,"url":"../parts/partsIndex.do","users":null,"subPopdoms":[]},{"valid":null,"name":"包装管理","mid":"wg","checked":false,"pid":"wa","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/packingManage","users":null,"subPopdoms":[]}]},{"valid":null,"name":"仓库管理","mid":"xa","checked":false,"pid":"0","disabled":false,"url":null,"users":null,"subPopdoms":[{"valid":null,"name":"成品库","mid":"xb","checked":false,"pid":"xa","disabled":false,"url":"../mtStock/MtStockApproval.do","users":null,"subPopdoms":[]}]},{"valid":null,"name":"库存查询","mid":"xd","checked":false,"pid":"0","disabled":false,"url":null,"users":null,"subPopdoms":[{"valid":null,"name":"原辅材料查询","mid":"xe","checked":false,"pid":"xd","disabled":false,"url":"../mt/rawMt","users":null,"subPopdoms":[]},{"valid":null,"name":"成品查询","mid":"xf","checked":false,"pid":"xd","disabled":false,"url":"../finishedOverview/queryHome.do","users":null,"subPopdoms":[]}]},{"valid":null,"name":"原辅材料库","mid":"xg","checked":false,"pid":"0","disabled":false,"url":"","users":null,"subPopdoms":[{"valid":null,"name":"仓库管理","mid":"xh","checked":false,"pid":"xg","disabled":false,"url":"/mt/mtStore.do","users":null,"subPopdoms":[]},{"valid":null,"name":"盘点管理","mid":"xi","checked":false,"pid":"xg","disabled":false,"url":"/mt/inventoryMent","users":null,"subPopdoms":[]},{"valid":null,"name":"手动入库","mid":"xj","checked":false,"pid":"xg","disabled":false,"url":"/mt/manualWareh","users":null,"subPopdoms":[]},{"valid":null,"name":"手动领料","mid":"xk","checked":false,"pid":"xg","disabled":false,"url":"/mt/manualResition","users":null,"subPopdoms":[]},{"valid":null,"name":"出入库记录","mid":"xl","checked":false,"pid":"xg","disabled":false,"url":"/mt/entryExitRecords","users":null,"subPopdoms":[]}]},{"valid":null,"name":"物流管理","mid":"ya","checked":false,"pid":"0","disabled":false,"url":null,"users":null,"subPopdoms":[{"valid":null,"name":"发货管理","mid":"yb","checked":false,"pid":"ya","disabled":false,"url":"../inOutStock/shippingManagement.do","users":null,"subPopdoms":[]},{"valid":null,"name":"签收管理","mid":"yc","checked":false,"pid":"ya","disabled":false,"url":"../inOutStock/receiptManagement.do","users":null,"subPopdoms":[]}]},{"valid":null,"name":"采购","mid":"za","checked":false,"pid":"0","disabled":false,"url":"","users":null,"subPopdoms":[{"valid":null,"name":"入库","mid":"zb","checked":false,"pid":"za","disabled":false,"url":"../inStock/inStockIndex.do","users":null,"subPopdoms":[]},{"valid":null,"name":"材料录入","mid":"zc","checked":false,"pid":"za","disabled":false,"url":"../mt/in.do","users":null,"subPopdoms":[]},{"valid":null,"name":"材料管理","mid":"zd","checked":false,"pid":"za","disabled":false,"url":"../mt/manage.do","users":null,"subPopdoms":[]},{"valid":null,"name":"订单管理","mid":"ze","checked":false,"pid":"za","disabled":false,"url":"../po/index.do","users":null,"subPopdoms":[]},{"valid":null,"name":"收货地点","mid":"zf","checked":false,"pid":"za","disabled":false,"url":"../dac/index.do","users":null,"subPopdoms":[]},{"valid":null,"name":"材料包装","mid":"zg","checked":false,"pid":"za","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/materalPakaging","users":null,"subPopdoms":[]}]},{"valid":null,"name":"企业云盘","mid":"zaa","checked":false,"pid":"0","disabled":false,"url":"http://seafilepro.btransm.com","users":null,"subPopdoms":[]},{"valid":null,"name":"个人中心","mid":"ma","checked":false,"pid":"0","disabled":false,"url":null,"users":null,"subPopdoms":[{"valid":null,"name":"系统设置","mid":"mf","checked":false,"pid":"ma","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/systemSet","users":null,"subPopdoms":[]},{"valid":null,"name":"登录设置","mid":"mi","checked":false,"pid":"ma","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/loginSet","users":null,"subPopdoms":[]}]},{"valid":null,"name":"参考资料","mid":"na","checked":false,"pid":"0","disabled":false,"url":null,"users":null,"subPopdoms":[{"valid":null,"name":"类别设置","mid":"nb","checked":false,"pid":"na","disabled":false,"url":"../reference/goResourceCatory.do","users":null,"subPopdoms":[]},{"valid":null,"name":"文件管理","mid":"nc","checked":false,"pid":"na","disabled":false,"url":"../reference/goResourceWenjian.do","users":null,"subPopdoms":[]}]},{"valid":null,"name":"阅览室","mid":"rh","checked":false,"pid":"0","disabled":false,"url":"","users":null,"subPopdoms":[{"valid":null,"name":"文件与资料","mid":"ri","checked":false,"pid":"rh","disabled":false,"url":"../read/readingRoom.do","users":null,"subPopdoms":[]},{"valid":null,"name":"讨论组","mid":"rj","checked":false,"pid":"rh","disabled":false,"url":"../read/archivedForum.do","users":null,"subPopdoms":[]}]},{"valid":null,"name":"小程序","mid":"ro","checked":false,"pid":"0","disabled":false,"url":"","users":null,"subPopdoms":[{"valid":null,"name":"目录管理","mid":"rp","checked":false,"pid":"ro","disabled":false,"url":"../mpRes/wxResDirection.do","users":null,"subPopdoms":[]},{"valid":null,"name":"文章管理","mid":"rq","checked":false,"pid":"ro","disabled":false,"url":"../mpRes/wxResContentManage.do","users":null,"subPopdoms":[]}]},{"valid":null,"name":"日常事务","mid":"db","checked":false,"pid":"0","disabled":false,"url":"../daily/dailyAffairs.do","users":null,"subPopdoms":[]},{"valid":null,"name":"回款录入","mid":"gb","checked":false,"pid":"0","disabled":false,"url":"../salesBack/cashBackInput.do","users":null,"subPopdoms":[]},{"valid":null,"name":"潜在客户","mid":"gc","checked":false,"pid":"0","disabled":false,"url":"../sale/potentialCustomer.do","users":null,"subPopdoms":[]},{"valid":null,"name":"访谈记录","mid":"gd","checked":false,"pid":"0","disabled":false,"url":"../sales/interviewManag.do","users":null,"subPopdoms":[]},{"valid":null,"name":"计量单位","mid":"dc","checked":false,"pid":"0","disabled":false,"url":"../unit/unitManage.do","users":null,"subPopdoms":[]},{"valid":null,"name":"领料分工","mid":"ge","checked":false,"pid":"0","disabled":false,"url":"../picking/index.do","users":null,"subPopdoms":[]},{"valid":null,"name":"我的团队","mid":"dd","checked":false,"pid":"0","disabled":false,"url":"","users":null,"subPopdoms":[{"valid":null,"name":"团队考勤","mid":"de","checked":false,"pid":"dd","disabled":false,"url":"../workAttendanceTeam/attendancePage.do","users":null,"subPopdoms":[]},{"valid":null,"name":"登录记录","mid":"df","checked":false,"pid":"dd","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/myTeamLogin","users":null,"subPopdoms":[]},{"valid":null,"name":"请假动态","mid":"dg","checked":false,"pid":"dd","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"加班动态","mid":"dh","checked":false,"pid":"dd","disabled":false,"url":"../","users":null,"subPopdoms":[]},{"valid":null,"name":"工作记录","mid":"di","checked":false,"pid":"dd","disabled":false,"url":"../mywork/toMyTeamPage.do","users":null,"subPopdoms":[]}]},{"valid":null,"name":"跨机构访问","mid":"dj","checked":false,"pid":"0","disabled":false,"url":"../sonOrg/toInteragencyVisit.do","users":null,"subPopdoms":[]},{"valid":null,"name":"培训管理","mid":"nf","checked":false,"pid":"0","disabled":false,"url":null,"users":null,"subPopdoms":[{"valid":null,"name":"试题库","mid":"ng","checked":false,"pid":"nf","disabled":false,"url":"/vue/minersFrontEnd/dist/index.html#/train_questionBank","users":null,"subPopdoms":[]},{"valid":null,"name":"考核管理","mid":"nh","checked":false,"pid":"nf","disabled":false,"url":"../trainManage/goAssessManage.do","users":null,"subPopdoms":[]}]},{"valid":null,"name":"公共信息","mid":"ni","checked":false,"pid":"0","disabled":false,"url":null,"users":null,"subPopdoms":[{"valid":null,"name":"目录","mid":"nj","checked":false,"pid":"ni","disabled":false,"url":"../pi/publicDirection.do","users":null,"subPopdoms":[]},{"valid":null,"name":"信息","mid":"nk","checked":false,"pid":"ni","disabled":false,"url":"../pi/publicInformation.do","users":null,"subPopdoms":[]}]}],"error":null,"others":null,"errorMsg":null,"errorCode":null}
*
*/
export function getSelectPopedomsByOid(data) {
    return request({
        url: '/thali/getSelectPopedomsByOid.do',
        data
    })
}


/*
* creator: hxz 2025-05-22  特殊机构 机构 菜单重命名保存
* 传入参数： oid 机构id ， midRenames=[{mid菜单id, newName新名},{}]（jsontoString串） ，
*
*/
export function orgMidRenames(data) {
    return request({
        url: '/thali/orgMidRenames.do',
        data
    })
}

/*
* creator: hxz 2025-05-22  特殊机构 机构 菜单重命名保存
* 传入参数： oid 机构id
* 返回值： initialName 菜单初始名称， newName 是新名称， 操作人 createName， 时间 createDate ， 重命名后的菜单名称 newName, memo 模板生成时的重命名
*
*/
export function getOrgRenameHistories(data) {
    return request({
        url: '/thali/getOrgRenameHistories.do',
        data
    })
}


/*
* creator: hxz 2025-05-22 特殊机构 菜单重命名记录 单菜单记录
* 传入参数： oid 机构id，mid 菜单id
* 返回值： initialName 初始名称， currentName是当前名称 ， 操作人 createName， 时间 createDate ， 重命名后的菜单名称 newName, memo 模板生成时的重命名
*       {"page":null,"success":1,"data":{"historyList":[{"creator":null,"updateDate":1747293545000,"previousId":null,"org":3575,"mid":"kb","memo":"模板生成时的重命名","type":"1","updateName":null,"initialName":null,"newName":"百鬼夜行","versionNo":0,"updator":null,"id":353,"state":null,"operation":null,"createName":"系统","createDate":1747293545000}],"currentName":"百鬼夜行","initialName":"职工档案"},"error":null,"others":null,"errorMsg":null,"errorCode":null}

*/
export function getOrgRenameHistoryInfo(data) {
    return request({
        url: '/thali/getOrgRenameHistoryInfo.do',
        data
    })
}













