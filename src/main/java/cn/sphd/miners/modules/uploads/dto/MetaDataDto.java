package cn.sphd.miners.modules.uploads.dto;

import com.alibaba.fastjson.JSON;
import org.apache.commons.text.StringEscapeUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description kendo chunks 上传参数识别类
 * @date Create at 2020/3/4 09:40
**/
public class MetaDataDto implements Serializable{
    private Long chunkIndex;
    private String contentType;
    private String fileName;
    private String relativePath;
    private Long totalFileSize;
    private Long totalChunks;
    private String uploadUid;
    private String hash;

    public Long getChunkIndex() {
        return chunkIndex;
    }

    public void setChunkIndex(Long chunkIndex) {
        this.chunkIndex = chunkIndex;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getRelativePath() {
        return relativePath;
    }

    public void setRelativePath(String relativePath) {
        this.relativePath = relativePath;
    }

    public Long getTotalFileSize() {
        return totalFileSize;
    }

    public void setTotalFileSize(Long totalFileSize) {
        this.totalFileSize = totalFileSize;
    }

    public Long getTotalChunks() {
        return totalChunks;
    }

    public void setTotalChunks(Long totalChunks) {
        this.totalChunks = totalChunks;
    }

    public String getUploadUid() {
        return uploadUid;
    }

    public void setUploadUid(String uploadUid) {
        this.uploadUid = uploadUid;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public static MetaDataDto parse(String metadata) {
        MetaDataDto result = JSON.parseObject(metadata,MetaDataDto.class);
        //wyu：处理KendoUI的BUG，relativePath 在文件上传时可能被转义。
        if(!result.getRelativePath().contains(result.getFileName())){
            String escape = StringEscapeUtils.escapeHtml4(result.getFileName());
            if(result.getRelativePath().contains(escape)) {
                result.setRelativePath(result.getRelativePath().replace(escape,result.getFileName()));
            }
        }
        return result;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
