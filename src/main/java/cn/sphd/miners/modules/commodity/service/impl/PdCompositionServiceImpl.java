package cn.sphd.miners.modules.commodity.service.impl;

import cn.sphd.miners.modules.commodity.dao.PdBaseDao;
import cn.sphd.miners.modules.commodity.dao.PdCompositionDao;
import cn.sphd.miners.modules.commodity.entity.PdBase;
import cn.sphd.miners.modules.commodity.entity.PdComposition;
import cn.sphd.miners.modules.commodity.service.PdCompositionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by Administrator on 2016/10/13.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class PdCompositionServiceImpl implements PdCompositionService {
    @Autowired
    PdBaseDao pdBaseDao;
    @Autowired
    PdCompositionDao pdCompositionDao;

    @Override
    public List<PdBase> getPdBase(Integer oid, String innerSn) {
        String hql=" and o.oid="+oid;
        if (innerSn!=null&&!"".equals(innerSn)){
            hql+=" and o.innerSn like '%"+innerSn+"%'";
        }
        List<PdBase> pdBases=pdBaseDao.findCollectionByConditionNoPage(hql,null,null);
        return pdBases;
    }

    @Override
    public PdComposition getPdCompositionById(Integer id) {
        return pdCompositionDao.get(id);
    }

    @Override
    public void deletePdComposition(PdComposition pdComposition) {
        pdCompositionDao.delete(pdComposition);
    }

    @Override
    public void savePdComposition(PdComposition pdComposition) {
        pdCompositionDao.save(pdComposition);
    }

    @Override
    public void updatePdComposition(PdComposition pdComposition) {
        pdCompositionDao.update(pdComposition);
    }

    @Override
    public List<PdComposition> getPdCompositionLists(Integer baseID) {
        String hql="from PdComposition o where o.parent="+baseID+" order by id asc";
        return pdCompositionDao.getListByHQL(hql);
    }
}
