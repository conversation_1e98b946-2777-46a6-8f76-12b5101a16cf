<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sphd.miners.modules.equipment.mapper.TEquModelHistoryMapper">
    
    <resultMap type="TEquModelHistory" id="TEquModelHistoryResult">
        <result property="id"    column="id"    />
        <result property="org"    column="org"    />
        <result property="model"    column="model"    />
        <result property="equipment"    column="equipment"    />
        <result property="equipmentHistory"    column="equipment_history"    />
        <result property="modelName"    column="model_name"    />
        <result property="modelCode"    column="model_code"    />
        <result property="unitId"    column="unit_id"    />
        <result property="category"    column="category"    />
        <result property="supplier"    column="supplier"    />
        <result property="lifeSpan"    column="life_span"    />
        <result property="originalValue"    column="original_value"    />
        <result property="receiveDate"    column="receive_date"    />
        <result property="conditions"    column="conditions"    />
        <result property="enabled"    column="enabled"    />
        <result property="enabledTime"    column="enabled_time"    />
        <result property="productCount"    column="product_count"    />
        <result property="memo"    column="memo"    />
        <result property="creator"    column="creator"    />
        <result property="createName"    column="create_name"    />
        <result property="createDate"    column="create_date"    />
        <result property="updator"    column="updator"    />
        <result property="updateName"    column="update_name"    />
        <result property="updateDate"    column="update_date"    />
        <result property="operation"    column="operation"    />
        <result property="previousId"    column="previous_id"    />
        <result property="versionNo"    column="version_no"    />
        <result property="quantity"    column="quantity"    />
    </resultMap>

    <sql id="selectTEquModelHistoryVo">
        select id, org, model, equipment, equipment_history, model_name, model_code, unit_id, category, supplier, life_span, original_value, receive_date, conditions, enabled, enabled_time, product_count, memo, creator, create_name, create_date, updator, update_name, update_date, operation, previous_id, version_no,quantity from t_equ_model_history
    </sql>

    <select id="selectTEquModelHistoryList" parameterType="TEquModelHistory" resultMap="TEquModelHistoryResult">
        <include refid="selectTEquModelHistoryVo"/>
        <where>  
            <if test="org != null "> and org = #{org}</if>
            <if test="model != null "> and model = #{model}</if>
            <if test="equipment != null "> and equipment = #{equipment}</if>
            <if test="equipmentHistory != null "> and equipment_history = #{equipmentHistory}</if>
            <if test="modelName != null  and modelName != ''"> and model_name like concat('%', #{modelName}, '%')</if>
            <if test="modelCode != null  and modelCode != ''"> and model_code = #{modelCode}</if>
            <if test="unitId != null "> and unit_id = #{unitId}</if>
            <if test="category != null "> and category = #{category}</if>
            <if test="supplier != null "> and supplier = #{supplier}</if>
            <if test="lifeSpan != null "> and life_span = #{lifeSpan}</if>
            <if test="originalValue != null "> and original_value = #{originalValue}</if>
            <if test="receiveDate != null "> and receive_date = #{receiveDate}</if>
            <if test="conditions != null "> and conditions = #{conditions}</if>
            <if test="enabled != null "> and enabled = #{enabled}</if>
            <if test="enabledTime != null "> and enabled_time = #{enabledTime}</if>
            <if test="productCount != null "> and product_count = #{productCount}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
            <if test="creator != null "> and creator = #{creator}</if>
            <if test="createName != null  and createName != ''"> and create_name like concat('%', #{createName}, '%')</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updator != null "> and updator = #{updator}</if>
            <if test="updateName != null  and updateName != ''"> and update_name like concat('%', #{updateName}, '%')</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="operation != null "> and operation = #{operation}</if>
            <if test="previousId != null "> and previous_id = #{previousId}</if>
            <if test="versionNo != null "> and version_no = #{versionNo}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
        </where>
    </select>
    
    <select id="selectTEquModelHistoryById" parameterType="Long" resultMap="TEquModelHistoryResult">
        <include refid="selectTEquModelHistoryVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTEquModelHistory" parameterType="TEquModelHistory" useGeneratedKeys="true" keyProperty="id">
        insert into t_equ_model_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="org != null">org,</if>
            <if test="model != null">model,</if>
            <if test="equipment != null">equipment,</if>
            <if test="equipmentHistory != null">equipment_history,</if>
            <if test="modelName != null">model_name,</if>
            <if test="modelCode != null">model_code,</if>
            <if test="unitId != null">unit_id,</if>
            <if test="category != null">category,</if>
            <if test="supplier != null">supplier,</if>
            <if test="lifeSpan != null">life_span,</if>
            <if test="originalValue != null">original_value,</if>
            <if test="receiveDate != null">receive_date,</if>
            <if test="conditions != null">conditions,</if>
            <if test="enabled != null">enabled,</if>
            <if test="enabledTime != null">enabled_time,</if>
            <if test="productCount != null">product_count,</if>
            <if test="memo != null">memo,</if>
            <if test="creator != null">creator,</if>
            <if test="createName != null">create_name,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updator != null">updator,</if>
            <if test="updateName != null">update_name,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="operation != null">operation,</if>
            <if test="previousId != null">previous_id,</if>
            <if test="versionNo != null">version_no,</if>
            <if test="quantity != null">quantity,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="org != null">#{org},</if>
            <if test="model != null">#{model},</if>
            <if test="equipment != null">#{equipment},</if>
            <if test="equipmentHistory != null">#{equipmentHistory},</if>
            <if test="modelName != null">#{modelName},</if>
            <if test="modelCode != null">#{modelCode},</if>
            <if test="unitId != null">#{unitId},</if>
            <if test="category != null">#{category},</if>
            <if test="supplier != null">#{supplier},</if>
            <if test="lifeSpan != null">#{lifeSpan},</if>
            <if test="originalValue != null">#{originalValue},</if>
            <if test="receiveDate != null">#{receiveDate},</if>
            <if test="conditions != null">#{conditions},</if>
            <if test="enabled != null">#{enabled},</if>
            <if test="enabledTime != null">#{enabledTime},</if>
            <if test="productCount != null">#{productCount},</if>
            <if test="memo != null">#{memo},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createName != null">#{createName},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updator != null">#{updator},</if>
            <if test="updateName != null">#{updateName},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="operation != null">#{operation},</if>
            <if test="previousId != null">#{previousId},</if>
            <if test="versionNo != null">#{versionNo},</if>
            <if test="quantity != null">#{quantity},</if>
         </trim>
    </insert>

    <update id="updateTEquModelHistory" parameterType="TEquModelHistory">
        update t_equ_model_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="org != null">org = #{org},</if>
            <if test="model != null">model = #{model},</if>
            <if test="equipment != null">equipment = #{equipment},</if>
            <if test="equipmentHistory != null">equipment_history = #{equipmentHistory},</if>
            <if test="modelName != null">model_name = #{modelName},</if>
            <if test="modelCode != null">model_code = #{modelCode},</if>
            <if test="unitId != null">unit_id = #{unitId},</if>
            <if test="category != null">category = #{category},</if>
            <if test="supplier != null">supplier = #{supplier},</if>
            <if test="lifeSpan != null">life_span = #{lifeSpan},</if>
            <if test="originalValue != null">original_value = #{originalValue},</if>
            <if test="receiveDate != null">receive_date = #{receiveDate},</if>
            <if test="conditions != null">conditions = #{conditions},</if>
            <if test="enabled != null">enabled = #{enabled},</if>
            <if test="enabledTime != null">enabled_time = #{enabledTime},</if>
            <if test="productCount != null">product_count = #{productCount},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="createName != null">create_name = #{createName},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updator != null">updator = #{updator},</if>
            <if test="updateName != null">update_name = #{updateName},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="operation != null">operation = #{operation},</if>
            <if test="previousId != null">previous_id = #{previousId},</if>
            <if test="versionNo != null">version_no = #{versionNo},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTEquModelHistoryById" parameterType="Long">
        delete from t_equ_model_history where id = #{id}
    </delete>

    <delete id="deleteTEquModelHistoryByIds" parameterType="String">
        delete from t_equ_model_history where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>