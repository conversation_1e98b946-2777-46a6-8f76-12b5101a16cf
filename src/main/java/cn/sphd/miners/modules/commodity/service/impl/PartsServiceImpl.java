package cn.sphd.miners.modules.commodity.service.impl;

import cn.sphd.miners.modules.commodity.dao.*;
import cn.sphd.miners.modules.commodity.entity.*;
import cn.sphd.miners.modules.commodity.service.PartsService;
import cn.sphd.miners.modules.commodity.service.ProService;
import cn.sphd.miners.modules.material.dao.MtBaseHistoryDao;
import cn.sphd.miners.modules.material.entity.MtBaseHistory;
import cn.sphd.miners.modules.material.service.UnitService;
import cn.sphd.miners.modules.sales.entity.PdModelSettings;
import cn.sphd.miners.modules.system.entity.ApprovalItem;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.RoleService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Criteria;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

@Service
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class PartsServiceImpl implements PartsService {

    @Autowired
    private PdBaseDao pdBaseDao;
    @Autowired
    private PdViewSettingDao pdViewSettingDao;
    @Autowired
    private PdAssembleDao pdAssembleDao;
    @Autowired
    private PdBaseHistoryDao pdBaseHistoryDao;
    @Autowired
    private MtBaseHistoryDao mtBaseHistoryDao;
    @Autowired
    private PdFormulaHistoryDao pdFormulaHistoryDao;
    @Autowired
    private RoleService roleService;
    @Autowired
    private ProService proService;
    @Autowired
    private PdMerchandiseDao productDao;
    @Autowired
    private PdMerchandiseHistoryDao productHistoryDao;
    @Autowired
    private UnitService unitService;

    @Override
    public Map<String, Object> getPartsList(Integer oid, String category, String process, String composition, String enabled, String param, Integer pageNo, Integer pageSize) {
        Object[] p = new Integer[]{};
        String sql = "SELECT " +
                "p.id AS id,\n" +
                "p.inner_sn as innerSn,\n" +
                "p.name AS name,\n" +
                "IF(p.model='',null,p.model) AS model,\n" +
                "IF(p.specifications='',null,p.specifications) AS specifications,\n" +
                "IF(p.process='',null,p.process) as process,\n" +
                "p.unit AS unit,\n" +
                "p.unit_id AS unitId,\n" +
                "p.source AS source,\n" +
                "p.composition AS composition,\n" +
                "p.net_weight AS netWeight,\n" +
                "IF(p.weight_unit='',null,p.weight_unit) as weightUnit,\n" +
                "p.create_name AS createName,\n" +
                "p.create_date AS createDate,\n";

        if ("1".equals(enabled)) {
            sql = sql + "(SELECT count(b.id)  from  t_pd_base b where b.oid= " + oid + "  and b.path like CONCAT('%1:',p.id,'%') and b.type='1' ) as proNum,\n" +
                    "(SELECT count(b.id)  from  t_pd_base b where b.oid= " + oid + " and b.path like CONCAT('%:',p.id,'%') and b.type='2') as num  \n";
        } else {
            sql = sql + "(SELECT count( DISTINCT b.product)  from  t_pd_base_history b  where b.oid= " + oid + " and b.path like CONCAT('%1:',p.id,'%') and b.type='1' ) as proNum,\n" +
                    "(SELECT count(DISTINCT b.product)  from  t_pd_base_history b where b.oid= " + oid + "  and b.path like CONCAT('%:',p.id,'%') and b.type='2') as num  \n";
        }

        sql = sql + " from\n" +
                "t_pd_base p\n" +
                "WHERE\n" +
                " p.oid =" + oid + "   and type='2'  ";

        if (StringUtils.isNotEmpty(enabled)) {
            sql = sql + " and p.enabled=" + enabled;
        }
        System.out.println(sql);
        if (process != null && !"".equals(process)) {
            sql = sql + " and p.process='" + process + "'";

        }
        if (composition != null && !"".equals(composition)) {
            sql = sql + " and p.composition='" + composition + "'";

        }
        if (param != null && !"".equals(param)) {
            sql = sql + " and (p.inner_sn like '%" + param + "%' or p.name like '%" + param + "%') ";
        }
        return pdBaseDao.findMapByConditionByPage(sql, "", p, pageNo, pageSize);
    }

    @Override
    public Map<String, Object> getPartsListForTemporary(Integer oid, String source, String composition, String param, Integer pageNo, Integer pageSize) {
        String sql = "SELECT " +
                "p.id AS id," +
                "p.inner_sn as innerSn," +
                "p.name AS name," +
                "p.model AS model," +
                "p.specifications AS specifications," +
                "p.unit AS unit," +
                "p.unit_id AS unitId," +
                "p.source AS source," +
                "p.composition AS composition," +
                "p.net_weight AS netWeight," +
                "IF(p.weight_unit='',null,p.weight_unit) as weightUnit," +
                "p.create_name AS createName," +
                "p.create_date AS createDate," +
                "0 as proNum," +
                "(SELECT count(b.id)  from  t_pd_base b where  b.path like CONCAT('%:',p.id,'%')) num \n" +
                "FROM\n" +
                "t_pd_base p\n" +
                "WHERE\n" +
                " p.oid =" + oid + " and p.enabled=1 and type='2' ";
        Object[] p = new Integer[]{};
        System.out.println(sql);
        if (source != null && !"".equals(source)) {
            sql = sql + " and p.source='" + source + "'";

        }
        if (composition != null && !"".equals(composition)) {
            sql = sql + " and p.composition='" + composition + "'";

        }
        if (param != null && !"".equals(param)) {
            sql = sql + " and (p.innerSn like '%" + param + "%' or p.name like '%" + param + "%') ";
        }

        sql = sql + " having num=0";

        return pdBaseDao.findMapByConditionByPage(sql, "", p, pageNo, pageSize);
    }

    @Override
    public PdAssemble getPdAssemble(Integer pdBaseId) {
        return pdAssembleDao.getByPdBaseId(pdBaseId);
    }

    @Override
    public List<Map<String, Object>> getProductListForZhiJie(Integer id, String type) {
        String sql = "SELECT new Map(" +
                "p.id AS id," +
                "p.innerSn as innerSn," +
                "p.name AS name," +
                "p.model AS model," +
                "p.specifications AS specifications," +
                "p.unit AS unit," +
                "p.unitId AS unitId," +
                "p.source AS source," +
                "p.composition AS composition," +
                "p.netWeight AS netWeight," +
                "p.weightUnit AS weightUnit," +
                "p.json as json )" +
                "FROM\n" +
                "PdBase p\n" +
                "WHERE\n" +
                "p.type=:type and p.id in ( SELECT b.id  from  PdBase b where b.json is not null and  b.path like CONCAT('%1:'," + id + ",'%') )";

        Map<String, Object> p = new HashMap<>();
        p.put("type", type);

        return pdBaseDao.getListByHQLWithNamedParams(sql, p);
    }

    @Override
    public List<Map<String, Object>> getProductListForJianJie(Integer id, String type) {
        String sql = "SELECT new Map(" +
                "p.id AS id," +
                "p.innerSn as innerSn," +
                "p.name AS name," +
                "p.model AS model," +
                "p.specifications AS specifications," +
                "p.unit AS unit," +
                "p.unitId AS unitId," +
                "p.source AS source," +
                "p.composition AS composition," +
                "p.netWeight AS netWeight," +
                "p.weightUnit AS weightUnit," +
                "p.json as json )" +
                "FROM\n" +
                "PdBase p\n" +
                "WHERE\n" +
                "p.type=:type and p.id in ( SELECT b.id  from  PdBase b where b.json is not null and b.path like CONCAT('%:'," + id + ",'%') ) AND " +
                "p.id not in ( SELECT b.id  from  PdBase b where  b.path like CONCAT('%1:'," + id + ",'%') )";

        Map<String, Object> p = new HashMap<>();
        p.put("type", type);

        return pdBaseDao.getListByHQLWithNamedParams(sql, p);
    }

    @Override
    public List<Map<String, Object>> getProductListForBeforeZhiJie(Integer id, String type) {
        String sql = "select * from (SELECT  " +
                "p.product as product,"+
                "p.inner_sn as innerSn," +
                "p.name AS name," +
                "p.model AS model," +
                "p.specifications AS specifications," +
                "p.json as json " +
                "FROM\n" +
                "t_pd_base_history p \n" +
                "WHERE\n" +
                "p.type="+type+" and  p.json is not null and p.path like CONCAT('%1:'," + id + ",'%') order by p.id desc ) s group by s.product ";

        return pdBaseDao.getSession().createSQLQuery(sql).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list();
    }

    @Override
    public List<Map<String, Object>> getProductListForBeforeJianjie(Integer id, String type) {
        String sql = "select * from (SELECT  "+
                "p.product as product,"+
                "p.inner_sn as innerSn," +
                "p.name AS name," +
                "p.model AS model," +
                "p.specifications AS specifications," +
                "p.json as json "+
                "FROM\n" +
                "t_pd_base_history p\n"+
                "WHERE\n" +
                "p.type="+type+" and   p.json is not null and p.path like CONCAT('%:',"+id+",'%')  AND " +
                "p.product not in ( SELECT b.product  from  t_pd_base_history b where  b.path like CONCAT('%1:',"+id+",'%') )) s group by s.product";


        return pdBaseDao.getSession().createSQLQuery(sql).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list();
    }

    @Override
    public List<Map<String, Object>> getAllPartsByParts(Integer id, Integer level) {

        String sql = "SELECT\n" +
                "p.id AS id,\n" +
                "p.inner_sn as innerSn,\n" +
                "p.name AS name,\n" +
                "IF(p.model='',null,p.model) AS model,\n" +
                "IF(p.specifications='',null,p.specifications) AS specifications,\n" +
                "IF(p.process='',null,p.process) as process,\n" +
                "p.unit AS unit,\n" +
                "p.unit_id AS unitId,\n" +
                "p.create_name AS createName,\n" +
                "p.create_date AS createDate,\n" +
                "p.memo AS memo,\n" +
                "IF(p.weight_unit='',null,p.weight_unit) as weightUnit,\n" +
                "p.net_weight as netWeight,\n" +
                "p.composition as composition,\n" +
                "(select count(0) from t_pd_base pb where pb.path like CONCAT('%1:',p.id,'%') ) num, " +
                "p.split as split,\n" +
                "p.json as json\n" +
                "from t_pd_base p\n";


        PdBase pdBase = pdBaseDao.get(id);
        com.alibaba.fastjson.JSONArray array = com.alibaba.fastjson.JSONArray.parseArray(pdBase.getJson());
        if (array == null || array.size() == 0) {
            return new ArrayList();
        }
        String ids = null;
        if (level != null && "1".equals(level.toString())) {
            ids = ConstituteUtil.getIds(array, 1);
        } else {
            ids = ConstituteUtil.getIds(array);
        }

        if (ids == null || ids.length() == 0) {
            return new ArrayList();
        }
        sql += " where p.id in (" + ids + ")";
        List<Map<String, Object>> list = pdBaseDao.getSession().createSQLQuery(sql).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list();

        JSONArray all= ConstituteUtil.getAllObj(array);
        for (Map<String, Object> p : list) {
            for (int i = 0; i < all.size(); i++) {
                Integer pid = (Integer) p.get("id");
                if (pid.intValue() == all.getJSONObject(i).getInteger("id").intValue()) {
                    p.put("level", all.getJSONObject(i).get("level"));
                    p.put("amount", all.getJSONObject(i).get("amount"));
                    p.put("date", all.getJSONObject(i).get("date"));
                    p.put("gcName", all.getJSONObject(i).get("gcName"));
                }
            }
        }
        return list;
    }

    @Override
    public List<Map<String, Object>> getAllPartsByBeforeParts(Integer id) {

        PdBase pdBase = pdBaseDao.get(id);
        com.alibaba.fastjson.JSONArray array = com.alibaba.fastjson.JSONArray.parseArray(pdBase.getJson());
        if (array == null || array.size() == 0) {
            array = new JSONArray();
        }

        String sql = "FROM\n" +
                "PdBaseHistory p\n" +
                "WHERE\n" +
                " p.product=:product and p.operation='8' and p.composition='2' order by id asc";
        Map<String, Object> p = new HashMap<>();
        p.put("product", id);

        List<PdBaseHistory> pdBaseHistoryList=pdBaseDao.getListByHQLWithNamedParams(sql, p);
        List<Map<String,Object>> list=new ArrayList<Map<String,Object>>();
        for (PdBaseHistory pdBaseHistory:pdBaseHistoryList) {
            com.alibaba.fastjson.JSONArray a = com.alibaba.fastjson.JSONArray.parseArray(pdBaseHistory.getJson());
            if (a != null &&a.size() != 0) {
                Map<String,Object> m=new HashMap<>();
                m.put("date",pdBaseHistory.getUpdateDate());
                m.put("delName",pdBaseHistory.getUpdateName());
                m.put("data",a);
                list.add(m);
            }else{
                Map<String,Object> m=new HashMap<>();
                m.put("date",pdBaseHistory.getUpdateDate());
                m.put("delName",pdBaseHistory.getUpdateName());
                m.put("data",new JSONArray());
                list.add(m);
            }
        }
        List<Map<String,Object>> list2=new ArrayList<>(list);
        if(list2.size()>0){
            list2.remove(0);

            Map<String,Object> m=new HashMap<>();
            m.put("date",pdBase.getUpdateDate());
            m.put("delName",pdBase.getUpdateName());
            m.put("data",array);
            list2.add(m);
        }

        List<Map<String,Object>> hav=new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            JSONArray a1= (JSONArray) list.get(i).get("data");
            JSONArray a2= (JSONArray) list2.get(i).get("data");

            Date date= (Date) list2.get(i).get("date");
            String delName= (String) list2.get(i).get("delName");
            for (int j = 0; j <a1.size(); j++) {
                int f=0;
                for (int k = 0; k < a2.size(); k++) {

                    if(a1.getJSONObject(j).get("id").toString().equals(a2.getJSONObject(k).get("id").toString())){
                        f=1;
                        break;
                    }
                }
                if(f==0){
                    a1.getJSONObject(j).put("delDate",date);
                    a1.getJSONObject(j).put("delName",delName);
                    hav.add(a1.getJSONObject(j));
                }
            }

        }
        return hav;
    }

    @Override
    public Map<String, Object> getPdViewSetting(User user) {

        String hql = "from PdViewSetting where user=:user and category=:category";
        Map<String, Object> param = new HashMap<>();
        param.put("user", user.getUserID());
        param.put("category", "product");
        PdViewSetting product = (PdViewSetting) pdViewSettingDao.getByHQLWithNamedParams(hql, param);

        param.put("category", "component");
        PdViewSetting component = (PdViewSetting) pdViewSettingDao.getByHQLWithNamedParams(hql, param);

        param.put("category", "frame");
        PdViewSetting frame = (PdViewSetting) pdViewSettingDao.getByHQLWithNamedParams(hql, param);

        if (product == null) {
            product = new PdViewSetting();
            product.setCreator(user.getUserID());
            product.setCreateDate(new Date());
            product.setCreateName(user.getUserName());
            product.setEnable(1);
            product.setKeyKey("1");
            product.setValue("都展示");
            product.setCategory("product");
            product.setOrg(user.getOid());
            product.setUser(user.getUserID());
            pdViewSettingDao.save(product);
        }

        if (frame == null) {
            frame = new PdViewSetting();
            frame.setCreator(user.getUserID());
            frame.setCreateDate(new Date());
            frame.setCreateName(user.getUserName());
            frame.setEnable(1);
            frame.setKeyKey("1");
            frame.setValue("是");
            frame.setCategory("frame");
            frame.setOrg(user.getOid());
            frame.setUser(user.getUserID());
            pdViewSettingDao.save(frame);
        }

        if (component == null) {
            component = new PdViewSetting();
            component.setCreator(user.getUserID());
            component.setCreateDate(new Date());
            component.setCreateName(user.getUserName());
            component.setEnable(1);
            component.setKeyKey("1");
            component.setValue("都展示");
            component.setCategory("component");
            component.setOrg(user.getOid());
            component.setUser(user.getUserID());
            pdViewSettingDao.save(component);
        }

        Map<String, Object> map = new HashMap<>();
        map.put("component", component.getKeyKey());
        map.put("product", product.getKeyKey());
        map.put("frame", frame.getKeyKey());

        return map;
    }

    @Override
    public void setPdViewSetting(User user, String cp, String cc, String cf) {
        String hql = "from PdViewSetting where user=:user and category=:category";
        Map<String, Object> param = new HashMap<>();
        param.put("user", user.getUserID());
        param.put("category", "product");
        PdViewSetting product = (PdViewSetting) pdViewSettingDao.getByHQLWithNamedParams(hql, param);

        param.put("category", "component");
        PdViewSetting component = (PdViewSetting) pdViewSettingDao.getByHQLWithNamedParams(hql, param);


        param.put("category", "frame");
        PdViewSetting frame = (PdViewSetting) pdViewSettingDao.getByHQLWithNamedParams(hql, param);

        if (product == null) {
            product = new PdViewSetting();
            product.setCreator(user.getUserID());
            product.setCreateDate(new Date());
            product.setCreateName(user.getUserName());
            product.setEnable(1);
            product.setCategory("product");
            product.setKeyKey("1");
            product.setValue("都展示");
            product.setOrg(user.getOid());
            product.setUser(user.getUserID());
            pdViewSettingDao.save(product);
        } else {
            if (StringUtils.isNotEmpty(cp)) {
                product.setKeyKey(cp);
                if ("1".equals(cp)) {
                    product.setValue("都展示");
                } else if ("2".equals(cp)) {
                    product.setValue("都不展示");
                } else if ("3".equals(cp)) {
                    product.setValue("只展示规格");
                } else if ("4".equals(cp)) {
                    product.setValue("只展示型号");
                }
                product.setUpdator(user.getUserID());
                product.setUpdateDate(new Date());
                product.setUpdateName(user.getUserName());
                pdViewSettingDao.update(product);
            }
        }

        if (component == null) {
            component = new PdViewSetting();
            component.setCreator(user.getUserID());
            component.setCreateDate(new Date());
            component.setCreateName(user.getUserName());
            component.setEnable(1);
            component.setCategory("component");
            component.setKeyKey("1");
            component.setValue("都展示");
            component.setOrg(user.getOid());
            component.setUser(user.getUserID());
            pdViewSettingDao.save(component);
        } else {
            component.setKeyKey(cc);
            if ("1".equals(cc)) {
                component.setValue("都展示");
            } else if ("2".equals(cc)) {
                component.setValue("都不展示");
            } else if ("3".equals(cc)) {
                component.setValue("只展示规格");
            } else if ("4".equals(cc)) {
                component.setValue("只展示型号");
            }
            component.setUpdator(user.getUserID());
            component.setUpdateDate(new Date());
            component.setUpdateName(user.getUserName());
            pdViewSettingDao.update(component);
        }

        if (frame == null) {
            frame = new PdViewSetting();
            frame.setCreator(user.getUserID());
            frame.setCreateDate(new Date());
            frame.setCreateName(user.getUserName());
            frame.setEnable(1);
            frame.setKeyKey(cf);
            frame.setCategory("frame");
            frame.setValue("是");
            frame.setOrg(user.getOid());
            frame.setUser(user.getUserID());
            pdViewSettingDao.save(frame);
        } else {
            frame.setKeyKey(cf);
            if ("1".equals(cf)) {
                frame.setValue("是");
            } else if ("0".equals(cf)) {
                frame.setValue("否");
            }
            frame.setUpdator(user.getUserID());
            frame.setUpdateDate(new Date());
            frame.setUpdateName(user.getUserName());
            pdViewSettingDao.update(frame);
        }


    }

    @Override
    public void updateComposition(User user, Integer id, String composition, String process) {
        PdBase pdBase = pdBaseDao.get(id);
        pdBase.setComposition(composition);
        pdBase.setProcess(process);

        String json = pdBase.getJson();
        if ("1".equals(pdBase.getProcess())) {
            pdBase.setComposition("");
            pdBase.setSource("1");
            pdBase.setSplit("0");
            pdBase.setOperation("8");

        } else {
            pdBase.setSource("2");
            pdBase.setSplit("1");
        }

        /**
         * 删除配方，材料绑定
         */
        pdBaseDao.queryHQLWithNamedParams("delete from PdCompositionMaterial where product=" + id, null);

        pdBase.setJson(null);
        pdBase.setPath(null);
        pdBase.setUpdateDate(new Date());
        pdBase.setUpdateName(user.getUserName());
        pdBase.setUpdator(user.getUserID());
        pdBaseDao.update(pdBase);
        if ("1".equals(pdBase.getProcess())) {
            Integer hid = pdBaseHistoryDao.setHistory(pdBase);
        }

        if ("1".equals(pdBase.getProcess())) {
            com.alibaba.fastjson.JSONArray array = com.alibaba.fastjson.JSONArray.parseArray(json);
            if (array != null) {
                String ids = ConstituteUtil.getIds(array);
                if (ids.length() > 0) {
                    for (String i : ids.split(",")) {
                        jsProAndParts(Integer.valueOf(i), user);
                    }
                }
            }
        }
    }

    @Override
    public String updatePartsBase(User user, PdBase pdBase) {

        PdBase old = pdBaseDao.get(pdBase.getId());

        PdBase np = pdBaseDao.getByHQL("from PdBase where innerSn='" + pdBase.getInnerSn() + "' and oid=" + user.getOid());
        if (np != null && !np.getId().toString().equals(old.getId().toString())) {
            return "该图号已存在";
        }
        //是否简易模式
        ApprovalItem jyItem = roleService.getCurrentItem(user.getOid(),"finishedProductCheck");

        ApprovalItem approvalItem = roleService.getCurrentItem(user.getOid(), "commodityProduct");
        PdModelSettings pdModelSettings = null;
        if (approvalItem != null||jyItem!=null) {
            pdModelSettings = roleService.getPdModelSettings(approvalItem.getId());
            if (pdModelSettings != null||(jyItem!=null&&jyItem.getStatus()==0)) {
                PdMerchandise p=null;
                List<Map<String,Object>> list=proService.getProductListByPdBase(pdBase.getId());
                if(list.size()==1){
                    Map<String,Object> m=list.get(0);
                    if("系统".equals(m.get("createName").toString())||"系统".equals(old.getCreateName())){

                        PdMerchandise pcp = productDao.getByHQL("from PdMerchandise where outerSn='" + pdBase.getInnerSn() + "' and org=" + user.getOid());
                        p=productDao.get((Serializable) m.get("id"));
                        if (pcp != null) {
                            if (pcp.getId() != p.getId()) {
                                return "该商品代号已存在!";
                            }
                        }

                    }
                }
                if(p!=null){
                    p.setUpdateDate(new Date());
                    p.setUpdateName(user.getUserName());
                    p.setUpdator(user.getUserID());
                    p.setOuterSn(pdBase.getInnerSn());
                    p.setOuterName(pdBase.getName());
                    p.setModel(pdBase.getModel());
                    p.setSpecifications(pdBase.getSpecifications());
                    p.setUnit(pdBase.getUnit());
                    p.setUnitId(pdBase.getUnitId());
                    p.setMemo(pdBase.getMemo());
                    int module = 1;


                    if ("1".equals(p.getType())) {
                        module = 1;
                    } else {
                        module = 2;
                    }
                    unitService.selectUnit(p.getUnitId(), module);

                    p.setVersionNo(p.getVersionNo() + 1);
                    p.setOperation("3");//修改基本信息
                    productDao.update(p);
                    int hisId = productHistoryDao.insert(p);
                }

            }
        }

        old.setInnerSn(pdBase.getInnerSn());
        old.setCode(pdBase.getCode());
        old.setSpecifications(pdBase.getSpecifications());
        old.setModel(pdBase.getModel());
        old.setMemo(pdBase.getMemo());
        old.setName(pdBase.getName());
        old.setProcessDept(pdBase.getProcessDept());
        old.setProcessDeptName(pdBase.getProcessDeptName());
        old.setUnitId(pdBase.getUnitId());
        old.setUnit(pdBase.getUnit());
        old.setPhrase(pdBase.getPhrase());

        BigDecimal a = UnitUnit.toMilligram(pdBase.getNetWeight(), pdBase.getWeightUnit());
        if (a != null) {
            old.setNetWeight(UnitUnit.milligramToBest(a));
            old.setWeightUnit(UnitUnit.milligramToUnit(a));
        }


        old.setOperation("3");
        old.setUpdateDate(new Date());
        old.setUpdateName(user.getUserName());
        old.setUpdator(user.getUserID());
        pdBaseDao.update(old);

        pdBaseHistoryDao.setHistory(old);

        //查出所有直接上级
        List<PdBase> list = pdBaseDao.getListByHQL("from PdBase where path like '%1:" + pdBase.getId() + "%'");
        for (PdBase pd : list) {
            calculation(pd);
        }
        return "success";
    }

    /**
     * 重新计算单重
     */
    public void calculation(PdBase pdBase) {
        String sql = "SELECT\n" +
                "p.id AS id,\n" +
                "p.inner_sn as innerSn,\n" +
                "p.name AS name,\n" +
                "IF(p.model='',null,p.model) AS model,\n" +
                "IF(p.specifications='',null,p.specifications) AS specifications,\n" +
                "IF(p.process='',null,p.process) as process,\n" +
                "p.unit AS unit,\n" +
                "p.unit_id AS unitId,\n" +
                "p.create_name AS createName,\n" +
                "p.create_date AS createDate,\n" +
                "p.memo AS memo,\n" +
                "IF(p.weight_unit='',null,p.weight_unit) as weightUnitt,\n" +
                "p.net_weight as netWeight,\n" +
                "p.composition as composition,\n" +
                "(select count(0) from t_pd_base pb where pb.path like CONCAT('%1:',p.id,'%') ) num, " +
                "p.split as split\n" +
                "from t_pd_base p\n";

        com.alibaba.fastjson.JSONArray array = com.alibaba.fastjson.JSONArray.parseArray(pdBase.getJson());

        //总重量
        BigDecimal sum = new BigDecimal(0);
        if (array != null && array.size() != 0) {

            String ids = ConstituteUtil.getIds(array, 1);
            if (ids != null && ids.length() != 0) {
                sql += " where p.id in (" + ids + ")";
                List<Map<String, Object>> list2 = pdBaseDao.getSession().createSQLQuery(sql).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list();


                //计算单重
                for (Map<String, Object> p : list2) {
                    for (int i = 0; i < array.size(); i++) {

                        //获取毫克
                        if (p.get("netWeight") != null && p.get("weightUnit") != null) {
                            //毫克
                            BigDecimal amount = UnitUnit.toMilligram(new BigDecimal(p.get("netWeight").toString()), p.get("weightUnit").toString());
                            sum = sum.add(amount.multiply(array.getJSONObject(i).getBigDecimal("amount")));
                        }
                    }
                }
            }
        }

        pdBase.setNetWeight(UnitUnit.milligramToBest(sum));
        pdBase.setWeightUnit(UnitUnit.milligramToUnit(sum));
        pdBaseDao.update(pdBase);

        //查出所有直接上级
        List<PdBase> list2 = pdBaseDao.getListByHQL("from PdBase where path like '%1:" + pdBase.getId() + "%'");
        for (PdBase p : list2) {
            calculation(p);
        }

    }

    @Override
    public List<Map<String, Object>> getFormulaList(Integer pdBaseId) {
        Map<String, Object> param = new HashMap<>();
        String hql = "select\n" +
                "new Map(f.id AS id,\n" +
                "f.code AS code,\n" +
                "f.name AS name,\n" +
                "f.materialName AS materialName,\n" +
                "f.majorIngredient AS majorIngredient,\n" +
                "f.minorIngredient AS minorIngredient,\n" +
                "f.products AS products,\n" +
                "f.createName AS createName,\n" +
                "f.createDate AS createDate,\n" +
                "f.updateName AS updateName,\n" +
                "f.updateDate AS updateDate,\n" +
                "f.enabled AS enabled,\n" +
                "cm.product as product,\n" +
                "cm.formula as formula) \n" +
                "from\n" +
                "PdCompositionMaterial cm\n" +
                "LEFT JOIN PdFormula f on cm.formula=f.id\n" +
                "where cm.product =:product";
        param.put("product", pdBaseId);
        return pdBaseDao.getListByHQLWithNamedParams(hql, param);

    }

    @Override
    public List<Map<String, Object>> getCompositionRecordBaseList(Integer pdBaseId) {
        List<Map<String, Object>> list = pdBaseDao.getListByHQLWithNamedParams("select new map(id as id,createName as createName,createDate as createDate,updateName as updateName,updateDate as updateDate) from PdBaseHistory where product=" + pdBaseId + " and operation='8'", new HashMap<>());
        return list;
    }

    @Override
    public Map<String, Object> getCompositionRecordBaseDetails(Integer id, Integer frontId) {
        Map<String, Object> map = new HashMap<>();
        String hql = "SELECT\n" +
                "new Map(id AS id,\n" +
                "innerSn as innerSn,\n" +
                "name AS name,\n" +
                "model AS model,\n" +
                "specifications AS specifications,\n" +
                "unit AS unit,\n" +
                "source AS source,\n" +
                "composition AS composition,\n" +
                "netWeight AS netWeight,\n" +
                "weightUnit AS weightUnit,\n" +
                "createName AS createName,\n" +
                "createDate AS createDate," +
                "processDeptName AS processDeptName,\n" +
                "phrase AS phrase," +
                "memo as memo," +
                "process as process," +
                "json as json )\n" +
                "FROM\n" +
                "PdBaseHistory\n" +
                "WHERE\n" +
                "id=:id";
        Map param = new HashMap();
        param.put("id", id);
        Map<String, Object> now = (Map<String, Object>) pdBaseDao.getByHQLWithNamedParams(hql, param);

        //是否装配
        if (("2".equals(now.get("process")) || "3".equals(now.get("process"))) && "2".equals(now.get("composition"))) {
            try {
                now.put("list", JSON.parseArray((String) now.get("json")));
            } catch (Exception e) {
                e.printStackTrace();
                now.put("list", new ArrayList<>());
            }
        }

        //是否配方
        if (("2".equals(now.get("process")) || "3".equals(now.get("process"))) && "3".equals(now.get("composition"))) {
            PdCompositionMaterialHistory pdCompositionMaterialHistory = (PdCompositionMaterialHistory) pdBaseDao.getByHQLWithNamedParams("from PdCompositionMaterialHistory where product=" + id, null);
            if (pdCompositionMaterialHistory != null) {
                if(StringUtils.isNotEmpty(pdCompositionMaterialHistory.getMemo())) {
                    PdFormulaHistory pdFormulaHistory = pdFormulaHistoryDao.get(Integer.valueOf(pdCompositionMaterialHistory.getMemo()));
                    if (pdFormulaHistory != null) {
                        List list = new ArrayList();
                        list.add(pdFormulaHistory);
                        now.put("list", list);
                    }else{
                        now.put("list", new ArrayList<>());
                    }
                }

            } else {
                now.put("list", new ArrayList<>());
            }

        }
        //是否单材料
        if (("2".equals(now.get("process")) || "3".equals(now.get("process"))) && "4".equals(now.get("composition"))) {
            PdCompositionMaterialHistory pdCompositionMaterialHistory = (PdCompositionMaterialHistory) pdBaseDao.getByHQLWithNamedParams("from PdCompositionMaterialHistory where product=" + id, null);
            if (pdCompositionMaterialHistory != null) {
                MtBaseHistory mtBaseHistory = mtBaseHistoryDao.get(pdCompositionMaterialHistory.getMaterialHistory());
                if (mtBaseHistory != null) {
                    List list = new ArrayList();
                    list.add(mtBaseHistory);
                    now.put("list", list);
                }else{
                    now.put("list", new ArrayList<>());
                }
            } else {
                now.put("list", new ArrayList<>());
            }
        }
        Map<String, Object> front = null;
        if (frontId != null && frontId != 0) {
            param.put("id", frontId);
            front = (Map<String, Object>) pdBaseDao.getByHQLWithNamedParams(hql, param);
        }
        if(front!=null){
            //是否装配
            if (("2".equals(front.get("process")) || "3".equals(front.get("process"))) && "2".equals(front.get("composition"))) {
                try {
                    front.put("list", JSON.parseArray((String) front.get("json")));
                } catch (Exception e) {
                    e.printStackTrace();
                    front.put("list", new ArrayList<>());
                }
            }

            //是否配方
            if (("2".equals(front.get("process")) || "3".equals(front.get("process"))) && "3".equals(front.get("composition"))) {
                PdCompositionMaterialHistory pdCompositionMaterialHistory = (PdCompositionMaterialHistory) pdBaseDao.getByHQLWithNamedParams("from PdCompositionMaterialHistory where product=" + id, null);
                if (pdCompositionMaterialHistory != null) {
                    if(StringUtils.isNotEmpty(pdCompositionMaterialHistory.getMemo())) {
                        PdFormulaHistory pdFormulaHistory = pdFormulaHistoryDao.get(Integer.valueOf(pdCompositionMaterialHistory.getMemo()));
                        if (pdFormulaHistory != null) {
                            List list = new ArrayList();
                            list.add(pdFormulaHistory);
                            front.put("list", list);
                        } else {
                            front.put("list", new ArrayList<>());
                        }
                    }
                } else {
                    front.put("list", new ArrayList<>());
                }

            }
            //是否单材料
            if (("2".equals(front.get("process")) || "3".equals(front.get("process"))) && "4".equals(front.get("composition"))) {
                PdCompositionMaterialHistory pdCompositionMaterialHistory = (PdCompositionMaterialHistory) pdBaseDao.getByHQLWithNamedParams("from PdCompositionMaterialHistory where product=" + id, null);
                if (pdCompositionMaterialHistory != null) {
                    MtBaseHistory mtBaseHistory = mtBaseHistoryDao.get(pdCompositionMaterialHistory.getMaterialHistory());
                    if (mtBaseHistory != null) {
                        List list = new ArrayList();
                        list.add(mtBaseHistory);
                        front.put("list", list);
                    }else{
                        front.put("list", new ArrayList<>());
                    }
                } else {
                    front.put("list", new ArrayList<>());
                }
            }
        }
        map.put("now", now);
        map.put("front", front);
        return map;
    }

    @Override
    public List<Map<String, Object>> getMaterialList(Integer pdBaseId) {
        Map<String, Object> param = new HashMap<>();
        String hql = "select\n" +
                "new Map(p.code as code,\n" +
                "p.name AS name,\n" +
                "p.model AS model,\n" +
                "p.specifications AS specifications,\n" +
                "p.unit AS unit,\n" +
                "p.createName AS createName,\n" +
                "p.createDate AS createDate,\n" +
                "p.updateName AS updateName,\n" +
                "p.updateDate AS updateDate,\n" +
                "cm.product as product,\n" +
                "cm.material as material)\n" +
                "from\n" +
                "PdCompositionMaterial cm\n" +
                "LEFT JOIN MtBase p on cm.material=p.id\n" +
                "where  cm.product =:product";
        param.put("product", pdBaseId);
        return pdBaseDao.getListByHQLWithNamedParams(hql, param);
    }


    /**
     * 计算 含有本件的产品组建
     */

    private void jsProAndParts(Integer pdBaseId, User user) {

        PdAssemble pdAssemble = pdAssembleDao.getByPdBaseId(pdBaseId);
        if (pdAssemble == null) {
            pdAssemble = new PdAssemble();
            pdAssemble.setCreateDate(new Date());
            pdAssemble.setCreateName(user.getUserName());
            pdAssemble.setCreator(user.getUserID());
            pdAssemble.setOrg(user.getOid());
            pdAssemble.setOperation("1");
            pdAssemble.setProduct(pdBaseId);
        } else {
            pdAssemble.setOperation("3");
        }
        pdAssemble.setUpdateDate(new Date());
        pdAssemble.setUpdator(user.getUserID());
        pdAssemble.setUpdateName(user.getUpdateName());
        //直接参与组装的产品的种数
        pdAssemble.setDirectPartProduct(pdBaseDao.directComposition(user.getOid(), pdBaseId, "1"));
        //直接参与组装的组件的种数
        pdAssemble.setDirectPartComposition(pdBaseDao.directComposition(user.getOid(), pdBaseId, "2"));
        //间接参与组装的产品的种数
        pdAssemble.setIndirectPartProduct(pdBaseDao.indirectPartProduct(user.getOid(), pdBaseId));

        //曾经直接参与组装的产品的种数
        pdAssemble.setFormerDirectPartProduct(pdBaseDao.formerdirectPartComposition(user.getOid(), pdBaseId, "1"));
        //曾经直接参与组装的组件的种数
        pdAssemble.setFormerdirectPartComposition(pdBaseDao.formerdirectPartComposition(user.getOid(), pdBaseId, "2"));
        //曾经间接参与组装的产品的种数
        pdAssemble.setFormerIndirectPartProduct(pdBaseDao.formerIndirectPartProduct(user.getOid(), pdBaseId));

        pdAssembleDao.saveOrUpdate(pdAssemble);

    }
}
