<!--点击本日下的按账户查看中的下一步后 该账户本日的流水页面-->
<template>
  <div class="head">
    <div style="width: 100%">
      <PageHeader title="财会" :showBack="true" :showClose="true" @mycloseFun="closeFun"></PageHeader>
    </div>
  </div>
  <p class="margin-10-15">{{ todayTime }}</p>
  <div class="hr_gray"></div>
  <div v-if="accID && accID!='null'">
    <p class="margin-10-15">以下展示为{{ bankName }}的数据</p>
    <div class="hr_gray"></div>
  </div>
  <div class="current-balance-1 margin-10-15">
    <div>
      <p class="ty-color-orange">{{ bankName }}</p>
      <p>本期余额</p>
    </div>
    <span>{{ allBalance }}</span>
  </div>
  <div class="hr_gray"></div>
  <div class="flex">
    <div class="balance1" v-for="(item) in bala_list" :key="item.id">
      <div>
        <img :src="item.sr" alt="">
      </div>
      <div>
        <span :class="item.style">{{ item.da }} <br> {{ item.money }}</span>
      </div>
    </div>
  </div>
  <div class="hr_gray"></div>
  <div>
    <ul class="val_list">
      <li v-for="item in accountList" :key="item.id" class="ta-list" >
        <div @click="seeDetails(item)">
          <span>{{ item.dat }}</span>
          <span>
            <span :class="item.style"> {{ item.le }} {{ item.money }}</span>
            <span class="btnM" >
              <i class="fa fa-angle-right"></i>
            </span>
          </span>
        </div>

      </li>
    </ul>
  </div>
</template>

<script>
import finance from '@/mixins/finance.js'
export default {
  name: "currentAccountDate",
  mixins: [finance],
  data() {
    return {
      accID:'',
      todayTime:'今天是XXXX年X月XX日 星期X',
      allBalance:0,
      bala_list: [
      ],
      accountList: []
    }
  },
  mounted() {
    let accID = this.$route.params.id
    console.log('accID=', accID)
    if(accID){
      this.accID = accID
      this.getAccountList(accID)
    }else{
      this.$router.push('ViewByAccount')
    }

  },
  methods: {
    getAccountList(fid){
      let org = this.auth.getOrg()
      let currentAccountDate_beginDate = localStorage.getItem('currentAccountDate_beginDate')
      if(currentAccountDate_beginDate){
        let beginDate = currentAccountDate_beginDate
        let endDate = currentAccountDate_beginDate
        this.todayTime = currentAccountDate_beginDate
        let params = { oid:org.id , monthOrDay:3 , beginDate:beginDate, endDate:endDate }
        if(fid == 'null'){

        }else{
          params.fid = fid
        }
        this.financeApi.getAccountMonthOrDayApp(params).then(res1=>{
          let res = res1.data.data
          console.log('getAccountMonthOrDayApp res=', res)
          this.allBalance = this.formatCurrency(res.allBalance)
          this.bankName = res.bankName
          let accountDetailList = res.accountDetailList || []
          let accountPeriodList = res.accountPeriodList || []
          this.bala_list = [
            {id: 0, da: "上期余额", money: this.formatCurrency(res.allPreviousBalance ) , sr: this.balance1 },
            {id: 1, da: "本期收入", money: this.formatCurrency(res.allCredit ) , sr:  this.balance2 ,style: "ty-color-green"},
            {id: 2, da: "本期支出", money: this.formatCurrency(res.allDebit) , sr: this.balance3 ,style: "ty-color-red"},
          ]
          this.accountList = []
          accountDetailList.forEach(item=>{
            let itemA = {}
            if(item.credit || item.credit === 0){ // 收入
              itemA = {
                id: item.id , dat: (new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')) , le: "收入",
                money: item.credit ,
                style: "ty-color-green"}
            }else if(item.debit){ // 支出
              itemA =  {
                id:  item.id,
                dat: (new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss'))  ,
                le: "支出",
                money: item.debit,
                style: "ty-color-red"
              }
            }
            itemA.info = item
            this.accountList.push(itemA)
          })


        }).catch(err=>{
          console.log('err=', err)
          this.$message.error('链接失败，请重试！')
        })
      }else{
        this.$message.error('没有具体日期哦！')
      }

    },
  }
}
</script>

<style scoped lang="scss">
@import "../../../style/account.scss";
@import "../../../style/common.scss";
.centerSp{ text-align: center; }
</style>