<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.resourceAuthority.mapper.ResCategoryMapper">

    <select id="getSingle" parameterType="resCat" resultType="resCat">
        select
        id, org, org_name AS orgName, parent, type, category, valid, max_child_categories AS maxChildCategories,
        children, descendant, leafs, path, size, name, is_trash AS isTrash, memo, orders, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator, update_name AS updateName,
        DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate, approve_Item AS approveItem,
        approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
        DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, operation, apply_memo AS applyMemo,
        approve_memo AS approveMemo, message_id AS messageId, previous_id AS previousId, version_no AS versionNo,
        lock_uuid AS lockUuid, batch_uuid AS batchUuid
        from t_resource_category
        where id = #{id}
    </select>

    <select id="listPage" parameterType="hashmap" resultType="resCat">
        select
        id, org, org_name AS orgName, parent, type, category, valid, max_child_categories AS maxChildCategories,
        children, descendant, leafs, path, size, name, is_trash AS isTrash, memo, orders, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator, update_name AS updateName,
        DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate, approve_Item AS approveItem,
        approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
        DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, operation, apply_memo AS applyMemo,
        approve_memo AS approveMemo, message_id AS messageId, previous_id AS previousId, version_no AS versionNo
        from t_resource_category
    </select>

    <select id="getCategoryByParent" resultType="resCat">
        select
        trc.id, trc.org, trc.org_name AS orgName, trc.parent, trc.type, trc.category, trc.name,
        trc.is_trash AS isTrash, trc.memo, trc.valid,
        trc.max_child_categories AS maxChildCategories, trc.children, trc.descendant, trc.leafs, trc.path,
        trc.size, trc.orders, trc.creator, trc.create_name AS createName,
        DATE_FORMAT(trc.create_date,'%Y-%m-%d %H:%i:%s') AS createDate, trc.updator,
        trc.update_name AS updateName, DATE_FORMAT(trc.update_date ,'%Y-%m-%d %H:%i:%s') AS updateDate,
        trc.approve_Item AS approveItem, trc.approve_status AS approveStatus,
        trc.approve_level AS approveLevel, trc.auditor, trc.auditor_name AS auditorName,
        DATE_FORMAT(trc.audit_date ,'%Y-%m-%d %H:%i:%s') AS auditDate, trc.operation,
        trc.apply_memo AS applyMemo, trc.approve_memo AS approveMemo, trc.message_id AS messageId,
        trc.previous_id AS previousId, trc.version_no AS versionNo
        from t_resource_category trc, t_resource_category_acl trca
        where trc.id = trca.category and trc.org = #{org} and trc.type = #{type} and trca.user = #{user} and
        trc.parent is null and trc.valid = 1
        ORDER BY trc.create_date ASC
    </select>

    <select id="getFirstCategoryByAllChild" resultType="resCat">
        select
        trc.id, trc.org, trc.org_name AS orgName, trc.parent, trc.type, trc.category, trc.name,
        trc.is_trash AS isTrash, trc.memo, trc.valid,
        trc.max_child_categories AS maxChildCategories, trc.children, trc.descendant, trc.leafs, trc.path,
        trc.size, trc.orders, trc.creator, trc.create_name AS createName,
        DATE_FORMAT(trc.create_date,'%Y-%m-%d %H:%i:%s') AS createDate, trc.updator,
        trc.update_name AS updateName, DATE_FORMAT(trc.update_date ,'%Y-%m-%d %H:%i:%s') AS updateDate,
        trc.approve_Item AS approveItem, trc.approve_status AS approveStatus,
        trc.approve_level AS approveLevel, trc.auditor, trc.auditor_name AS auditorName,
        DATE_FORMAT(trc.audit_date ,'%Y-%m-%d %H:%i:%s') AS auditDate, trc.operation,
        trc.apply_memo AS applyMemo, trc.approve_memo AS approveMemo, trc.message_id AS messageId,
        trc.previous_id AS previousId, trc.version_no AS versionNo
        from t_resource_category trc, t_resource_category_acl trca
        where trc.id = trca.category and trc.org = #{org} and trc.type = #{type} and trca.user = #{user} and
        trc.parent in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getChildCategory" resultType="resCat">
        select
        trc.id, trc.org, trc.org_name AS orgName, trc.parent, trc.type, trc.category, trc.name,
        trc.is_trash AS isTrash, trc.memo, trc.valid,
        trc.max_child_categories AS maxChildCategories, trc.children, trc.descendant, trc.leafs, trc.path,
        trc.size, trc.orders, trc.creator, trc.create_name AS createName,
        DATE_FORMAT(trc.create_date,'%Y-%m-%d %H:%i:%s') AS createDate, trc.updator,
        trc.update_name AS updateName, DATE_FORMAT(trc.update_date ,'%Y-%m-%d %H:%i:%s') AS updateDate,
        trc.approve_Item AS approveItem, trc.approve_status AS approveStatus,
        trc.approve_level AS approveLevel, trc.auditor, trc.auditor_name AS auditorName,
        DATE_FORMAT(trc.audit_date ,'%Y-%m-%d %H:%i:%s') AS auditDate, trc.operation,
        trc.apply_memo AS applyMemo, trc.approve_memo AS approveMemo, trc.message_id AS messageId,
        trc.previous_id AS previousId, trc.version_no AS versionNo
        from t_resource_category trc, t_resource_category_acl trca
        where trc.id = trca.category and trc.org = #{org} and trc.type = #{type} and trca.user = #{user} and
        trc.parent = #{parent} and trc.valid = 1
    </select>

    <select id="getChildCategoryIds" resultType="Integer">
        select id
        from t_resource_category
        where parent = #{parent}
    </select>

    <select id="getDepartAndEmployee" resultType="departAndEmployee">
        SELECT u.userID AS uid,u.oid,u.department AS departid,o.pid,u.departName FROM `t_sys_user` u LEFT JOIN t_sys_org o ON u.department=o.id  WHERE u.oid=#{oid} ORDER BY u.department;

    </select>

    <select id="getDepartment" resultType="departAndEmployee">
        SELECT id AS departid,name AS departName FROM `t_sys_org` WHERE org_type=2 AND pid=#{pid}
    </select>

    <select id="getDepart" resultType="departAndEmployee">
        SELECT id AS departid,name AS departName FROM `t_sys_org` WHERE org_type=2 AND id=#{oid}
    </select>

    <select id="getEmployee" resultType="departAndEmployee">
        SELECT userID AS uid,userName FROM `t_sys_user` WHERE department=#{departid}
    </select>

    <select id="getEmployeeOther" resultType="departAndEmployee">
        SELECT userID AS uid,userName FROM `t_sys_user` WHERE oid=#{oid} AND department IS NULL AND
        (user_type != '0' OR user_type != '3' )
    </select>

    <select id="getCategoryByNameAndOid" resultType="resCat">
        select
        id, org, org_name AS orgName, parent, type, category, valid, max_child_categories AS maxChildCategories,
        children, descendant, leafs, path, size, name, is_trash AS isTrash, memo, orders, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator, update_name AS updateName,
        DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate, approve_Item AS approveItem,
        approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
        DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, operation, apply_memo AS applyMemo,
        approve_memo AS approveMemo, message_id AS messageId, previous_id AS previousId, version_no AS versionNo
        from t_resource_category
        where org = #{org} and name = #{name} and type = #{type} and parent is null and valid = 1
    </select>

    <select id="getCategoryByNameAndOidAndType" resultType="resCat">
        select
        id, org, org_name AS orgName, parent, type, category, valid, max_child_categories AS maxChildCategories,
        children, descendant, leafs, path, size, name, is_trash AS isTrash, memo, orders, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator, update_name AS updateName,
        DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate, approve_Item AS approveItem,
        approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
        DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, operation, apply_memo AS applyMemo,
        approve_memo AS approveMemo, message_id AS messageId, previous_id AS previousId, version_no AS versionNo
        from t_resource_category
        where creator = #{creator} and name = #{name} and type = 2 and parent is null and valid = 1
    </select>

    <select id="getCategoryByNameAndOidAndParent" resultType="resCat">
        select
        id, org, org_name AS orgName, parent, type, category, valid, max_child_categories AS maxChildCategories,
        children, descendant, leafs, path, size, name, is_trash AS isTrash, memo, orders, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator, update_name AS updateName,
        DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate, approve_Item AS approveItem,
        approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
        DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, operation, apply_memo AS applyMemo,
        approve_memo AS approveMemo, message_id AS messageId, previous_id AS previousId, version_no AS versionNo
        from t_resource_category
        where org = #{org} and name = #{name} and parent = #{parent} and valid = 1
    </select>

    <select id="getCategoryByNameAndOidAndParentAndType" resultType="resCat">
        select
        id, org, org_name AS orgName, parent, type, category, valid, max_child_categories AS maxChildCategories,
        children, descendant, leafs, path, size, name, is_trash AS isTrash, memo, orders, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator, update_name AS updateName,
        DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate, approve_Item AS approveItem,
        approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
        DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, operation, apply_memo AS applyMemo,
        approve_memo AS approveMemo, message_id AS messageId, previous_id AS previousId, version_no AS versionNo
        from t_resource_category
        where  creator= #{creator} and name = #{name} and parent = #{parent} and type = 2
    </select>

    <select id="getAllCategoryByOidAndType" resultType="resCat">
        select
        id, org, org_name AS orgName, parent, type, category, valid, max_child_categories AS maxChildCategories,
        children, descendant, leafs, path, size, name, is_trash AS isTrash, memo, orders, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator, update_name AS updateName,
        DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate, approve_Item AS approveItem,
        approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
        DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, operation, apply_memo AS applyMemo,
        approve_memo AS approveMemo, message_id AS messageId, previous_id AS previousId, version_no AS versionNo
        from t_resource_category
        where org = #{org} and type = 1 and valid = 1
    </select>

    <select id="getAllCategoryByOidAndTypeForFirst" resultType="resCat">
        select
        id, org, org_name AS orgName, parent, type, category, valid, max_child_categories AS maxChildCategories,
        children, descendant, leafs, path, size, name, is_trash AS isTrash, memo, orders, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator, update_name AS updateName,
        DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate, approve_Item AS approveItem,
        approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
        DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, operation, apply_memo AS applyMemo,
        approve_memo AS approveMemo, message_id AS messageId, previous_id AS previousId, version_no AS versionNo
        from t_resource_category
        where org = #{org} and type = 1 and parent is null and valid = 1
        ORDER BY create_date ASC
    </select>

    <select id="getOrdinaryFirstCategory" resultType="resCat">
        select
        id, org, org_name AS orgName, parent, type, category, valid, max_child_categories AS maxChildCategories,
        children, descendant, leafs, path, size, name, is_trash AS isTrash, memo, orders, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator, update_name AS updateName,
        DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate, approve_Item AS approveItem,
        approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
        DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, operation, apply_memo AS applyMemo,
        approve_memo AS approveMemo, message_id AS messageId, previous_id AS previousId, version_no AS versionNo
        from t_resource_category
        where org = #{org} and type = 1 and parent is null and valid = 1 and is_trash is false
        ORDER BY create_date ASC
    </select>

    <select id="getAllCategory" resultType="resCat">
        select
        id, org, org_name AS orgName, parent, type, category, valid, max_child_categories AS maxChildCategories,
        children, descendant, leafs, path, size, name, is_trash AS isTrash, memo, orders, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator, update_name AS updateName,
        DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate, approve_Item AS approveItem,
        approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
        DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, operation, apply_memo AS applyMemo,
        approve_memo AS approveMemo, message_id AS messageId, previous_id AS previousId, version_no AS versionNo
        from t_resource_category
        where org = #{org} and type = 1 and valid = 1
        ORDER BY create_date ASC
    </select>

    <select id="getTrashCategory" resultType="resCat">
        select
        id, org, org_name AS orgName, parent, type, category, valid, max_child_categories AS maxChildCategories,
        children, descendant, leafs, path, size, name, is_trash AS isTrash, memo, orders, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator, update_name AS updateName,
        DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate, approve_Item AS approveItem,
        approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
        DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, operation, apply_memo AS applyMemo,
        approve_memo AS approveMemo, message_id AS messageId, previous_id AS previousId, version_no AS versionNo
        from t_resource_category
        where org = #{org} and type = 1 and valid = 1 and is_trash is true
        ORDER BY create_date ASC
    </select>


    <select id="getAllFirstCategoryByOid" resultType="resCat">
        select
        id, org, org_name AS orgName, parent, type, category, valid, max_child_categories AS maxChildCategories,
        children, descendant, leafs, path, size, name, is_trash AS isTrash, memo, orders, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator, update_name AS updateName,
        DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate, approve_Item AS approveItem,
        approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
        DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, operation, apply_memo AS applyMemo,
        approve_memo AS approveMemo, message_id AS messageId, previous_id AS previousId, version_no AS versionNo
        from t_resource_category
        where org = #{org} and type = 1
    </select>

    <select id="getAllChildCategoryByParent" resultType="resCat">
        select
        id, org, org_name AS orgName, parent, type, category, valid, max_child_categories AS maxChildCategories,
        children, descendant, leafs, path, size, name, is_trash AS isTrash, memo, orders, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator, update_name AS updateName,
        DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate, approve_Item AS approveItem,
        approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
        DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, operation, apply_memo AS applyMemo,
        approve_memo AS approveMemo, message_id AS messageId, previous_id AS previousId, version_no AS versionNo
        from t_resource_category
        where parent = #{parent} and valid = 1
    </select>

    <select id="getAllChildCategoryNumByParent" resultType="java.lang.Integer">
        select
        COUNT(id) num
        from t_resource_category
        where parent = #{parent} and valid = 1
    </select>

    <select id="getOneCategoryByParentlistPage" resultType="resCat">
        select
        id
        from t_resource_category
        where parent = #{parent} and valid = 1
    </select>

    <select id="getOneCategoryByParentAuthlistPage" resultType="resCat">
        select
        trc.id
        from t_resource_category trc, t_resource_category_acl trca
        where trc.id = trca.category and trc.type = #{type} and trca.user = #{user} and
        trc.parent = #{parent} and trc.valid = 1
    </select>

    <select id="searchFolderlistPage" resultType="resCat">
        select
        id, org, org_name AS orgName, parent, type, category, valid, max_child_categories AS maxChildCategories,
        children, descendant, leafs, path, size, name, is_trash AS isTrash, memo, orders, creator, create_name AS createName,
        DATE_FORMAT(create_date,'%Y-%m-%d %H:%i:%s') AS createDate, updator, update_name AS updateName,
        DATE_FORMAT(update_date,'%Y-%m-%d %H:%i:%s') AS updateDate, approve_Item AS approveItem,
        approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
        DATE_FORMAT(audit_date,'%Y-%m-%d %H:%i:%s') AS auditDate, operation, apply_memo AS applyMemo,
        approve_memo AS approveMemo, message_id AS messageId, previous_id AS previousId, version_no AS versionNo
        from t_resource_category
        where org = #{org} and valid = 1 and type = 1 and name like CONCAT('%',#{name},'%') order by name
    </select>

    <select id="searchFolderByAuthlistPage" resultType="resCat">
        select
        trc.id, trc.org, trc.org_name AS orgName, trc.parent, trc.type, trc.category, trc.name,
        trc.is_trash AS isTrash, trc.memo, trc.valid,
        trc.max_child_categories AS maxChildCategories, trc.children, trc.descendant, trc.leafs, trc.path,
        trc.size, trc.orders, trc.creator, trc.create_name AS createName,
        DATE_FORMAT(trc.create_date,'%Y-%m-%d %H:%i:%s') AS createDate, trc.updator,
        trc.update_name AS updateName, DATE_FORMAT(trc.update_date ,'%Y-%m-%d %H:%i:%s') AS updateDate,
        trc.approve_Item AS approveItem, trc.approve_status AS approveStatus,
        trc.approve_level AS approveLevel, trc.auditor, trc.auditor_name AS auditorName,
        DATE_FORMAT(trc.audit_date ,'%Y-%m-%d %H:%i:%s') AS auditDate, trc.operation,
        trc.apply_memo AS applyMemo, trc.approve_memo AS approveMemo, trc.message_id AS messageId,
        trc.previous_id AS previousId, trc.version_no AS versionNo
        from t_resource_category trc, t_resource_category_acl trca
        where trc.id = trca.category and trc.org = #{org} and trc.type = 1 and trc.valid = 1 and trca.user = #{user} and
        name like CONCAT('%',#{name},'%') order by name
    </select>


    <delete id="delete" parameterType="resCat" >
        delete from t_resource_category
        where id = #{id}
    </delete>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="resCat" >
        insert into t_resource_category (org, org_name,
        parent, type, category, valid, max_child_categories,
        children, descendant, leafs, path, size, name, is_trash,
        memo, orders, creator,
        create_name, create_date, updator,
        update_name, update_date, approve_Item,
        approve_status, approve_level, auditor,
        auditor_name, audit_date, operation,
        apply_memo, approve_memo, message_id, previous_id, version_no
        ) values (#{org}, #{orgName}, #{parent}, #{type}, #{category}, #{valid}, #{maxChildCategories},
        #{children}, #{descendant}, #{leafs}, #{path}, #{size}, #{name},#{isTrash},
        #{memo}, #{orders}, #{creator}, #{createName}, #{createDate}, #{updator}, #{updateName},
        #{updateDate}, #{approveItem}, #{approveStatus}, #{approveLevel}, #{auditor}, #{auditorName},
        #{auditDate}, #{operation}, #{applyMemo}, #{approveMemo}, #{messageId}, #{previousId}, #{versionNo})
    </insert>


    <update id="update" parameterType="resCat" >
        update t_resource_category
        <set >
            <if test="org != null" >
                org = #{org},
            </if>
            <if test="orgName != null" >
                org_name = #{orgName},
            </if>
            <if test="parent != null" >
                parent = #{parent},
            </if>
            <if test="type != null" >
                type = #{type},
            </if>
            <if test="category != null" >
                category = #{category},
            </if>
            <if test="valid != null" >
                valid = #{valid},
            </if>
            <if test="maxChildCategories != null" >
                max_child_categories = #{maxChildCategories},
            </if>
            <if test="children != null" >
                children = #{children},
            </if>
            <if test="descendant != null" >
                descendant = #{descendant},
            </if>
            <if test="leafs != null" >
                leafs = #{leafs},
            </if>
            <if test="size != null" >
                size = #{size},
            </if>
            <if test="isTrash != null" >
                is_trash = #{isTrash},
            </if>
            <if test="memo != null" >
                memo = #{memo},
            </if>
            <if test="orders != null" >
                orders = #{orders},
            </if>
            <if test="creator != null" >
                creator = #{creator},
            </if>
            <if test="createName != null" >
                create_name = #{createName},
            </if>
            <if test="createDate != null" >
                create_date = #{createDate},
            </if>
            <if test="updator != null" >
                updator = #{updator},
            </if>
            <if test="updateName != null" >
                update_name = #{updateName},
            </if>
            <if test="updateDate != null" >
                update_date = #{updateDate},
            </if>
            <if test="approveItem != null" >
                approve_Item = #{approveItem},
            </if>
            <if test="approveStatus != null" >
                approve_status = #{approveStatus},
            </if>
            <if test="approveLevel != null" >
                approve_level = #{approveLevel},
            </if>
            <if test="auditor != null" >
                auditor = #{auditor},
            </if>
            <if test="auditorName != null" >
                auditor_name = #{auditorName},
            </if>
            <if test="auditDate != null" >
                audit_date = #{auditDate},
            </if>
            <if test="operation != null" >
                operation = #{operation},
            </if>
            <if test="applyMemo != null" >
                apply_memo = #{applyMemo},
            </if>
            <if test="approveMemo != null" >
                approve_memo = #{approveMemo},
            </if>
            <if test="messageId != null" >
                message_id = #{messageId},
            </if>
            <if test="previousId != null" >
                previous_id = #{previousId},
            </if>
            <if test="versionNo != null" >
                version_no = #{versionNo},
            </if>
            <if test="path != null" >
                path = #{path},
            </if>
            <if test="name != null" >
                name = #{name},
            </if>
            <if test="lockUuid != null" >
                lock_uuid = #{lockUuid},
            </if>
            <if test="batchUuid != null" >
                batch_uuid = #{batchUuid},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>