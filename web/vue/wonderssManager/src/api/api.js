import qs from 'qs'
import auth from '@/sys/auth'
import {request} from "@/utils/request"

/**
 * 入口函数处理：
 *  1、开发环境已登录，带VUE_APP_HREF，跳转到VUE_APP_HREF页面
 *  2、开发环境已登录，不带VUE_APP_HREF，按照有无VUE_APP_USER_ID分别跳转机构勾选页或者已登录机构
 *  3、有code参数，调用小程序登录
 *  4、已登录，且有url参数，是跳出返回，按照跳出前前提供的data.url地址跳转；
 *  跳转规则，返回值301，url返回给Start.vue跳转到router.name=url的页面
 */
export function authInit() {
    // console.log('authInit0', location.search, auth.isDevEnv() && auth.getToken(), auth.isDevEnv(), auth.getToken())
    let params = qs.parse(location.search, {ignoreQueryPrefix: true})
    // console.log('authInit params', params)
    // console.log('authInit isDevEnv', auth.isDevEnv(), auth.isLogged())
    //auth.isDevEnv() 在url的params.code && params.tgCode && params.appId均不为空时返回false，走微信登录流程
    if (auth.isDevEnv() && auth.isLogged()) {//开发环境已登录
        console.log('authInit dev')
        if (params.url || (params.url = process.env.VUE_APP_HREF)) {
            return new Promise(resolve => {
                resolve({success: 301, url: params.url})
            })
        } else {
            return new Promise(resolve => {
                resolve({success: 200})
            })
        }
    } else if (params.code && params.tgCode && params.appId) {//有code参数，调用小程序登录
        // console.log('code', params.code)
        // console.log('tgCode', params.tgCode)
        // console.log('appId', params.appId)
        // console.log('authInit mpCodeLogin.do')
        console.log('authInit navigationBarTitleText', params.navigationBarTitleText)
        return request({
            url: '/tp/mpCodeLogin.do',
            method: 'post',
            data: params
        })
        // return new Promise(resolve => {
        //     console.log('authInit1')
        //     resolve({success: 200})
        // })
    } else if (auth.isLogged() && params.url) {//已登录，且有url参数，跳转跳出前提供的url地址
        // console.log('params.url', params.url)
        return new Promise(resolve => {
            localStorage.setItem('webViewParams', JSON.stringify(params))
            resolve({success: 301, url: params.url, params: params})
        })
    }
}

/**
 * getAppHomePageAndWorks 获取主页和工作权限
 */
export function getAppHomePageAndWorks (data) {
    data = data || {}
    return request({
        url: '/sys/getAppHomePageAndWorks.do',
        data:data
    })
}

/*
 * creator: 2022-8-16  hxz
 *   登录某个工作室
 * params:   mobile， oid
 * res: user{"userName"姓名 ,"roleCode",}
*/
export function sureLogin (data) {
    data = data || {}
    return request({
        url: '/sys/sureLogin.do',
        data:data
    })
}


/*
 * creator: 2022-8-16  hxz
 *   机构列表
 * params:
*/
export function organizationList (data) {
    data = data || {}
    // const commonData = getCommonData()
    // data.tgCode = commonData.tgCode
    // data.appId = commonData.appId
    return request({
        url: '/appManage/organizationList.do',
        // data:data
    })
}

/*
 * creator: 2023-10-24 hxz
 * 注册页 发送短信验证码
 * params: phone 手机号
 * 返回值：data 中： 1-成功 0-失败，3-手机号格式不对，5 验证码已经发过还未过时
*/
export function sendMobileCode (data) {
    // const commonData = getCommonData()
    // data.tgCode = commonData.tgCode
    // data.appId = commonData.appId
    return request({
        method: "GET",
        url: '/org/broadcastVerificationCode.do',
        data: data
    })
}

/*
 * creator: 2024-7-17 hxz
 * 注册页 微信小程序（通用框架）  发送短信验证码
 * params: phone 手机号
 * 返回值：success 1 验证通过 / success 0 验证失败 抛出失败信息 “手机号长度不对”，“手机号格式不对”， “验证码未过时”。
*/
export function tpRegisterVerificationCode (data) {
    // const commonData = getCommonData()
    // data.tgCode = commonData.tgCode
    // data.appId = commonData.appId
    return request({
        url: '/tp/tpRegisterVerificationCode.do',
        data: data
    })
}



/*
 * creator: 2024-7-17 hxz
 * 注册页 微信小程序（通用框架） 校验 短信验证码
 * params:  phone 手机号， code 验证码
 * 返回值： success 1 验证通过 / success 0 验证失败 抛出失败信息 ”验证码错误或者超时！“
*/
export function tpCheckRegisterVerificationCode (data) {
    // const commonData = getCommonData()
    // data.tgCode = commonData.tgCode
    // data.appId = commonData.appId
    return request({
        url: '/tp/tpCheckRegisterVerificationCode.do',
        data: data
    })
}