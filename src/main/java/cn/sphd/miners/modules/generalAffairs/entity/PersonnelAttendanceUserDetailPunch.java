package cn.sphd.miners.modules.generalAffairs.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by Administrator on 2023/9/25
 * 人事_员工考勤明细打卡对照表
 */
@Entity
@Table(name = "t_personnel_attendance_user_detail_punch")
public class PersonnelAttendanceUserDetailPunch implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="org"   , nullable=true , unique=false)
    private Integer org;//机构id ，考虑到以前好多接口，暂时未加集联关系

    @Column(name="user"   , nullable=true , unique=false)
    private Integer user;//员工id ，考虑到以前好多接口，暂时未加集联关系

    @Column(name="detail"   , nullable=true , unique=false)
    private Integer detail;//明细id

    @Column(name="punch"  , length=50 , nullable=true , unique=false)
    private Integer punch; //打卡ID

    @Column(name="type"   , nullable=true , unique=false)
    private Byte type;//类型:1-开始,2-结束

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;//备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator; //创建人id

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;  //创建人

    @Column(name="create_time"   , nullable=true , unique=false)
    private Date createTime;  //创建时间

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator; //修改人id

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName; //修改人

    @Column(name="update_time"   , nullable=true , unique=false)
    private Date updateTime; //修改时间

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getUser() {
        return user;
    }

    public void setUser(Integer user) {
        this.user = user;
    }

    public Integer getDetail() {
        return detail;
    }

    public void setDetail(Integer detail) {
        this.detail = detail;
    }

    public Integer getPunch() {
        return punch;
    }

    public void setPunch(Integer punch) {
        this.punch = punch;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
