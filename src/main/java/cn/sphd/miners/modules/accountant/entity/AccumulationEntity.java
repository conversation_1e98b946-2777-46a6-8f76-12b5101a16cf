package cn.sphd.miners.modules.accountant.entity;

import java.math.BigDecimal;

/**
 * Created by 刘洪涛 on 17-11-1.
 * 这个类主要是存放一些中间数据，用户累加两个类之间的credit、debit、balance、prebalance、balance_direction、prebalance_direction
 */
public class AccumulationEntity {

    private BigDecimal credit;
    private BigDecimal debit;
    private BigDecimal balance;
    private BigDecimal preBalance;
    private String balanceDirection;        //期末余额方向
    private String preBalanceDirection;     //期初余额方向

    public BigDecimal getCredit() {
        return credit;
    }

    public void setCredit(BigDecimal credit) {
        this.credit = credit;
    }

    public BigDecimal getDebit() {
        return debit;
    }

    public void setDebit(BigDecimal debit) {
        this.debit = debit;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public BigDecimal getPreBalance() {
        return preBalance;
    }

    public void setPreBalance(BigDecimal preBalance) {
        this.preBalance = preBalance;
    }

    public String getBalanceDirection() {
        return balanceDirection;
    }

    public void setBalanceDirection(String balanceDirection) {
        this.balanceDirection = balanceDirection;
    }

    public String getPreBalanceDirection() {
        return preBalanceDirection;
    }

    public void setPreBalanceDirection(String preBalanceDirection) {
        this.preBalanceDirection = preBalanceDirection;
    }
}
