<template name="tabBar">
	<div class="">
		<div class="uni-tabbar" v-if="mainTab === 1">
			<div v-if="isShowData" @click="tabTo('homeData', 0)">
				<div class="icon" :class="activeIndex === 0 ? 'icon-data-active' :'icon-data'"></div>
				<div class="txt" :class="activeIndex === 0 ? 'txt-active' :''">数据</div>
			</div>
			<div @click="tabTo('homeManage', 1)">
				<div class="icon" :class="activeIndex === 1 ? 'icon-manage-active' :'icon-manage'"></div>
				<div class="txt" :class="activeIndex === 1 ? 'txt-active' :''">管理</div>
			</div>
			<div @click="tabTo('homeHandle', 6)">
				<div class="icon" :class="activeIndex === 6 ? 'icon-handle-active' :'icon-handle'"></div>
				<div class="txt" :class="activeIndex === 6 ? 'txt-active' :''">处理</div>
			</div>
			<div @click="tabTo('homeMy', 2)" >
				<div class="icon" :class="activeIndex === 2 ? 'icon-my-active' :'icon-my'"></div>
				<div class="txt" :class="activeIndex === 2 ? 'txt-active' :''">我的</div>
			</div>
		</div>
		<div class="uni-tabbar" v-else="mainTab === 2">
			<div @click="tabTo('life')">
				<div class="icon icon-life"></div>
				<div class="txt" :class="activeIndex === 1 ? 'txt-active2' :''">生活</div>
			</div>
			<div @click="tabTo('mailList')">
				<div class="icon icon-mailList"></div>
				<div class="txt" :class="activeIndex === 2 ? 'txt-active2' :''">通讯录</div>
			</div>
			<div @click="tabTo('manage')">
				<div class="icon icon-manage-active"></div>
				<div class="txt" :class="activeIndex === 3 ? 'txt-active2' :''">管理</div>
			</div>
			<div @click="tabTo('remind')">
				<div class="icon icon-remind"></div>
				<div class="txt" :class="activeIndex === 4 ? 'txt-active2' :''">提醒</div>
			</div>
			<div @click="tabTo('mine')">
				<div class="icon icon-my-active"></div>
				<div class="txt" :class="activeIndex === 5 ? 'txt-active2' :''">我的</div>
			</div>

		</div>
		
	</div>
</template>
<script>
	export default {
		name: "TabBar",
		props: {
			activeIndex0: {
				type: Number,
				default: 0
			},
			mainTab: {
				type: Number,
				default: 1
			},
			isShowData:{
				type: Boolean,
				default: false
			}
		},
    data(){
		  return{
        activeIndex : this.activeIndex0
      }
    },
    mounted(){
		  console.log('this.activeIndex0', this.activeIndex0)
		  console.log('this.activeIndex', this.activeIndex)
    },
		methods:{
			tabTo:function(path, actIndex){
			  this.activeIndex = actIndex
        console.log('this.activeIndex', this.activeIndex)
        if(path === 'homeHandle'){
          localStorage.setItem('activeH', 1)
        }
        this.$router.push({name: path});
			}
		}
	}
</script>
<style lang="scss" scoped>
.icon-data{ background: url("../../../liveMiniApp/src/assets/img/tabbar/data2.png"); }
.icon-data-active{ background: url("../../../liveMiniApp/src/assets/img/tabbar/data1.png"); }
.icon-manage{ background: url("../../../liveMiniApp/src/assets/img/tabbar/manage2.png"); }
.icon-manage-active{ background: url("../../../liveMiniApp/src/assets/img/tabbar/manage1.png"); }
.icon-handle{ background: url("../../../liveMiniApp/src/assets/img/tabbar/handle.png"); }
.icon-handle-active{ background: url("../../../liveMiniApp/src/assets/img/tabbar/handleA.png"); }
.icon-my{ background: url("../../../liveMiniApp/src/assets/img/tabbar/my2.png"); }
.icon-my-active{ background: url("../../../liveMiniApp/src/assets/img/tabbar/my1.png"); }
.icon-life{ background: url("../../../liveMiniApp/src/assets/img/tabbar/life.png"); }
.icon-mailList{ background: url("../../../liveMiniApp/src/assets/img/tabbar/mailList.png"); }
.icon-remind{ background: url("../../../liveMiniApp/src/assets/img/tabbar/remind.png"); }

.icon{ width: 20px; height: 20px; background-color: #fff; margin:3px auto; background-size: 100%; }

	//@import "../../uni.scss";
	.uni-tabbar{
		display: flex;
		// height:56px;
		background: #fff;
		// border-top: 1px solid #333;
		position: fixed;
		bottom: 0;
		width: 100%;
		z-index: 1;
		padding:4px 0;
		>div{ flex: 1;  text-align: center;  }

		.txt{
			font-size: 10px;
		}
		.txt-active{
			color:$livePageHeadBg
		}
		.txt-active2{
			color:$livePageHeadBg
		}

	}
</style>