package cn.sphd.miners.modules.generalAffairs.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.generalAffairs.dao.*;
import cn.sphd.miners.modules.generalAffairs.entity.*;
import cn.sphd.miners.modules.generalAffairs.service.*;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName UserImportServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/28 11:19
 * @Version 1.0
 */
@Service
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class UserLockServiceImpl implements UserLockService {

    @Autowired
    UserLockDao userLuckDao;
    @Autowired
    UserDao userDao;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    UserService userService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;


    @Override
    public List<User> selectStaffList(int org, PageInfo pageInfo) {
        String hql = "from User where oid = "+ org +" and roleCode in ('staff','agent') and isDuty in ('1','9')";
        List<User> list = userDao.getListByHQLWithNamedParams(hql,null,pageInfo);
        return list;
    }

    @Override
    public List<User> userLockCurrent(int org) {
        String hql = "from User where oid = "+ org +" and roleCode='agent' and isDuty in ('1','9')";
        List<User> list= userDao.getListByHQL(hql);
        return list;
    }

    @Override
    public List<User> substituteList(int org) {
        String hql = "from User where oid = "+ org +" and roleCode in ('staff','super') and isDuty in ('1','9')";
        List<User> list = userDao.getListByHQL(hql);
        return list;
    }

    @Override
    public List<User> selectAllUserForUserId(int userId) {
        User user=userDao.get(userId);
        String hql="from User where masterUserID="+userId+" and isDuty in ('1','9')";
        List<User> list=userDao.getListByHQL(hql);
        if (user.getMasterUserID()==null||"".equals(user.getMasterUserID()))
            list.add(user);
        return list;
    }

    @Override
    public int determineRoleCode(User user, List<User> list) {
        int status=1;
        if(list.size()<=1)
        {
            return 1;
        }
        int masterUserId;
        if(list.get(0).getMasterUserID()!=null)
            masterUserId=list.get(0).getMasterUserID();
        else
            masterUserId=list.get(0).getUserID();
        if(user.getMasterUserID()==null){
            if(user.getUserID().equals(masterUserId)){
                return 0;//自己不能冻结自己
            }
        }else{
            if(user.getMasterUserID().equals(masterUserId))
                return 0;//自己不能冻结自己
        }
       // 2总务--general 3财务--finance 4销售--sale 会计--accounting
        if("general".equals(user.getRoleCode())){
            for(User u:list){
                if("smallSuper".equals(u.getRoleCode()))
                {
                    return 0;//总务不能冻结小超管
                }else if("finance".equals(u.getRoleCode()))
                {
                    return 0;//小总务不能冻结财务
                }else if("sale".equals(u.getRoleCode()))
                {
                    return 0;//小总务不能冻结销售
                }else if("accounting".equals(u.getRoleCode()))
                {
                    return 0;//小总务不能冻结会计
                }
            }
        }else if("staff".equals(user.getRoleCode())){
            for(User u:list){
                if("smallSuper".equals(u.getRoleCode()))
                {
                    return 0;//总务不能冻结小超管
                }else if("general".equals(u.getRoleCode()))
                {
                    return 0;//小总务不能冻结总务
                }else if("finance".equals(u.getRoleCode()))
                {
                    return 0;//小总务不能冻结财务
                }else if("sale".equals(u.getRoleCode()))
                {
                    return 0;//小总务不能冻结销售
                }else if("accounting".equals(u.getRoleCode()))
                {
                    return 0;//小总务不能冻结会计
                }
            }
        }
        for(User u:list){
            if("agent".equals(u.getRoleCode()))
            {
                return -1;//该2用户代理着其他人身份，无法冻结
            }
        }
        return status;
    }

    @Override
    public int userLockEnter(User user, ReqUserLock reqUserLock) {
        int status=1;
        User restricedUser = userDao.get(reqUserLock.getUserId());//被冻结的人
        User substituteUser= userDao.get(reqUserLock.getSubstitute());//代理人
        //新增userLock
        SysUserLock userLock=new SysUserLock();
        userLock.setOrg(user.getOid());
        userLock.setUser(restricedUser.getUserID());
        userLock.setAccId(restricedUser.getAccId());
        userLock.setCreateName(user.getUserName());
        userLock.setCreateTime(Timestamp.valueOf(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())));
        userLock.setCreator(user.getUserID());
        userLock.setSubstitute(substituteUser.getUserID());
        userLock.setIsValid(1);
        userLock.setLockedMobile(restricedUser.getMobile());
        userLock.setLockedName(restricedUser.getUserName());
        userLock.setLockedPassword(restricedUser.getLogonPwd());
        userLock.setOperation("4");
        userLuckDao.save(userLock);
        //发推送消息
        String messageCont = restricedUser.getUserName()+"在系统中的工作暂已由您代办，请妥善处理！";
        String memo="操作时间  "+user.getUserName()+" " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        userSuspendMsgService.saveUserSuspendMsg(1, messageCont, messageCont,memo
                                                    , substituteUser.getUserID(),"",restricedUser.getUserID());//推送我的消息
        //user表变化
        //masterUserId存为代办人id， roleCode为agent 姓名为，a代b，手机号更换为代办人的，密码更换为代办人的
        restricedUser.setMasterUserID(substituteUser.getUserID());
        restricedUser.setRoleCode("agent");
        restricedUser.setIsDuty("1");
        restricedUser.setRoleName("代"+restricedUser.getUserName());
        restricedUser.setUserName(substituteUser.getUserName()+"代"+restricedUser.getUserName());
        restricedUser.setMobile(substituteUser.getMobile());
//        restricedUser.setLogonName(substituteUser.getMobile());
//        restricedUser.setLogonPwd(substituteUser.getLogonPwd());
        restricedUser.setUpdateName(user.getUserName());
        restricedUser.setUpdateDate(Timestamp.valueOf(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())));
        restricedUser.setUpdator(user.getUserID());

        //1.59,冻结账号模块，冻结和更换代办人时accid更换为代办人accid
        if(substituteUser.getAccId()!=null)
        restricedUser.setAccId(substituteUser.getAccId());

        userDao.update(restricedUser);
        return status;
    }

    @Override
    public List<UserLock> selectUserLock(int userId) {
        String hql = "from SysUserLock where user = "+ userId +" and isValid=1";
        List<SysUserLock> sysUserLockList=userLuckDao.getListByHQL(hql);
        List<UserLock> list=new ArrayList<>();
        for (SysUserLock s:sysUserLockList)
        {
            UserLock userLock= new UserLock();
            BeanUtils.copyProperties(s,userLock);
            User user=userDao.get(userLock.getSubstitute());
            userLock.setSubstituteName(user.getUserName());
            list.add(userLock);
        }
        return list;
    }

    @Override
    public List<UserLock> userLockAll(int org, PageInfo pageInfo) {
        String hql = "from SysUserLock where org = "+ org +" order by createTime desc";
        List<SysUserLock> sysUserLockList = userLuckDao.getListByHQLWithNamedParams(hql,null,pageInfo);
        List<UserLock> list=new ArrayList<>();
        for (SysUserLock s:sysUserLockList) {
            UserLock userLock = new UserLock();
            BeanUtils.copyProperties(s, userLock);
            User restricedUser = userDao.get(userLock.getUser());
            User user = new User();
            if (userLock.getSubstitute() != null)
            {
                user = userDao.get(userLock.getSubstitute());
                userLock.setSubstituteName(user.getUserName());
                userLock.setSubstituteMobile(user.getMobile());
            }
            userLock.setSubmitState(restricedUser.getSubmitState());
            userLock.setGender(restricedUser.getGender());
            userLock.setPostName(restricedUser.getPostName());
            userLock.setDepartName(restricedUser.getDepartName());
            list.add(userLock);
        }
        return list;
    }

    @Override
    public int updateSubstitute(User user, ReqUserLock reqUserLock) {
        String hql = "from SysUserLock where user = "+ reqUserLock.getUserId() +" and isValid=1";
        List<SysUserLock> sysUserLockList=userLuckDao.getListByHQL(hql);
        SysUserLock sysUserLock=sysUserLockList.get(sysUserLockList.size()-1);

        SysUserLock userLock=new SysUserLock();
        userLock.setOrg(user.getOid());
        userLock.setUser(sysUserLock.getUser());
        //1.159新增 accid替换
        userLock.setAccId(sysUserLock.getAccId());
        userLock.setCreateName(user.getUserName());
        userLock.setCreateTime(Timestamp.valueOf(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())));
        userLock.setCreator(user.getUserID());
        userLock.setSubstitute(reqUserLock.getSubstitute());
        userLock.setIsValid(1);
        userLock.setLockedMobile(sysUserLock.getLockedMobile());
        userLock.setLockedName(sysUserLock.getLockedName());
        userLock.setLockedPassword(sysUserLock.getLockedPassword());
        userLock.setOperation("6");
        userLock.setPreviousId(sysUserLock.getId());
        userLuckDao.save(userLock);

        //发推送消息
        String messageCont = sysUserLock.getLockedName()+"在系统中的工作暂已由您代办，请妥善处理！";
        String memo="操作时间  "+user.getUserName()+" " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        userSuspendMsgService.saveUserSuspendMsg(1, messageCont, messageCont,memo
                , userLock.getSubstitute(),"",sysUserLock.getUser());//推送我的消息

        String messageCont1 = sysUserLock.getLockedName()+"在系统中的工作暂已不再由您代办！";
        userSuspendMsgService.saveUserSuspendMsg(1, messageCont1, messageCont1,memo
                , sysUserLock.getSubstitute(),"",sysUserLock.getUser());//推送我的消息
        User restricedUser = userDao.get(reqUserLock.getUserId());//被冻结的人
        User substituteUser= userDao.get(reqUserLock.getSubstitute());//代理人
        //user表变化
        //masterUserId存为代办人id， roleCode为agent 姓名为，a代b，手机号更换为代办人的，密码更换为代办人的
        restricedUser.setMasterUserID(substituteUser.getUserID());
        restricedUser.setUserName(substituteUser.getUserName()+"代"+sysUserLock.getLockedName());
        restricedUser.setMobile(substituteUser.getMobile());
//        restricedUser.setLogonPwd(substituteUser.getLogonPwd());
//        restricedUser.setLogonName(substituteUser.getMobile());
        restricedUser.setUpdateName(user.getUserName());
        restricedUser.setUpdateDate(Timestamp.valueOf(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())));
        restricedUser.setUpdator(user.getUserID());

        //1.159新增 accid替换
        restricedUser.setAccId(substituteUser.getAccId());
        userDao.update(restricedUser);
        return 1;
    }

    @Override
    public int selectMasterUserId(int userId) {
        String hql = "from SysUserLock where user = "+ userId +" and isValid=1";
        List<SysUserLock> sysUserLockList=userLuckDao.getListByHQL(hql);
        SysUserLock sysUserLock=sysUserLockList.get(sysUserLockList.size()-1);
        int user=sysUserLock.getSubstitute();
        return user;
    }

    @Override
    public int clearUserLock(User user, int userId) {
        int status=1;
        String hql = "from SysUserLock where user = "+ userId +" and isValid=1";
        List<SysUserLock> sysUserLockList=userLuckDao.getListByHQL(hql);
        SysUserLock sysUserLock=new SysUserLock();
        if(sysUserLockList.size()>0)
        {
            sysUserLock=sysUserLockList.get(sysUserLockList.size()-1);
        }else{
            status=0;
        }
        SysUserLock userLock=new SysUserLock();
        userLock.setOrg(user.getOid());
        userLock.setUser(sysUserLock.getUser());
        //1.159新增 accid
        userLock.setAccId(sysUserLock.getAccId());
        userLock.setCreateName(user.getUserName());
        userLock.setCreateTime(Timestamp.valueOf(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())));
        userLock.setCreator(user.getUserID());
        userLock.setIsValid(0);
        userLock.setLockedMobile(sysUserLock.getLockedMobile());
        userLock.setLockedName(sysUserLock.getLockedName());
        userLock.setLockedPassword(sysUserLock.getLockedPassword());
        userLock.setOperation("5");
        userLock.setPreviousId(sysUserLock.getId());
        userLuckDao.save(userLock);
        String memo="操作时间  "+user.getUserName()+" " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        String messageCont1 = sysUserLock.getLockedName()+"在系统中的工作暂已不再由您代办！";
        userSuspendMsgService.saveUserSuspendMsg(1, messageCont1, messageCont1,memo
                , sysUserLock.getSubstitute(),"",sysUserLock.getUser());//推送我的消息
        for(SysUserLock s:sysUserLockList)
        {
            s.setIsValid(0);
            userLuckDao.update(s);
        }
        User restricedUser = userDao.get(userId);
        restricedUser.setMasterUserID(null);
        restricedUser.setRoleCode("staff");
        //1.159新增 accid
        if(sysUserLock.getAccId()!=null)
        restricedUser.setAccId(sysUserLock.getAccId());
        restricedUser.setUserName(sysUserLock.getLockedName());
        restricedUser.setMobile(sysUserLock.getLockedMobile());
//        restricedUser.setLogonName(sysUserLock.getLockedMobile());
        restricedUser.setUpdateName(user.getUserName());
        restricedUser.setUpdateDate(Timestamp.valueOf(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())));
        restricedUser.setUpdator(user.getUserID());
        restricedUser.setRoleName(null);
        String hqlUser = "from User where mobile = '"+ sysUserLock.getLockedMobile() +"' and isDuty in ('1','9') and userId!="+userId;
        List<User> userList=userDao.getListByHQL(hqlUser);
//        if(userList.size()>0)
//            restricedUser.setLogonPwd(userService.getLogonPwdByPhone(user.getMobile()));//获取密码
//        else
//            restricedUser.setLogonPwd(sysUserLock.getLockedPassword());
        userDao.update(restricedUser);

        List<User> rolePrincipals= userService.getUsersByMasterUserID(sysUserLock.getSubstitute());// 包含员工本身 的身份列表
        for(User user1:rolePrincipals) {
            clusterMessageSendingOperations.convertAndSendToUser(user1.getUserID().toString(), "/changeRolePrincipals", null, null, user.getOid(), user.getOrganization().getName(), JSON.toJSONString(rolePrincipals));
        }

        return status;
    }
    @Override
    public void updateLockUserNameByOidMobile(Integer oid, String mobile, String newUserName) {
        String hql="from User where oid=:oid and mobile=:mobile and roleCode='agent'";
        Map<String,Object> map = new HashMap<>();
        map.put("oid",oid);
        map.put("mobile",mobile);
        List<User> userList=userDao.getListByHQLWithNamedParams(hql,map);
        for(User user:userList)
        {
            user.setUserName(newUserName+user.getRoleName());
            userDao.update(user);
        }
    }
}
