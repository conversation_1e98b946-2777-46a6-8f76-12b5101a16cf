package cn.sphd.miners.modules.generalAffairs.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2021-01-09 
 */

@Entity ( name ="ApprovalInstance" )
@Table ( name ="t_sys_approval_instance" )
public class ApprovalInstance  implements Serializable {

	private static final long serialVersionUID =  4233594898710952989L;

	@Id
	@Column(name="id" )
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	@Column(name = "org" )
	private Integer org;
	/**
	 * 业务数据表ID,目前多级审批中，报销部分用了reimburse	
            加班和请假分别对应于t_personnel_overtime、t_personnel_leave的主键ID
	 */
   	@Column(name = "business" )
	private Integer business;

	/**
	 * 申请标题
	 */
   	@Column(name = "title" )
	private String title;

	/**
	 * 申批项目
	 */
   	@Column(name = "item" )
	private Integer item;

	/**
	 * 审批状态：0-未申请 ，1-申请提交，2-通过审核，3-否决审核,4-待处理
	 */
   	@Column(name = "status" )
	private String status;

	/**
	 * 审批次级
	 */
   	@Column(name = "level" )
	private Integer level;

	/**
	 * 审批者ID
	 */
   	@Column(name = "auditor" )
	private Integer auditor;

	/**
	 * 审批者
	 */
   	@Column(name = "auditor_name" )
	private String auditorName;

   	@Column(name = "audit_date" )
	private Date auditDate;

	/**
	 * 操作：1-增，2—删，3-—改
	 */
   	@Column(name = "operation" )
	private String operation;

	/**
	 * 申请备注
	 */
   	@Column(name = "apply_memo" )
	private String applyMemo;

	/**
	 * 审批备注
	 */
   	@Column(name = "approve_memo" )
	private String approveMemo;

	/**
	 * 描述
	 */
   	@Column(name = "description" )
	private String description;

	/**
	 * 对应消息表的id
	 */
   	@Column(name = "message_id" )
	private Integer messageId;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

   	@Column(name = "create_date" )
	private Date createDate;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

   	@Column(name = "update_date" )
	private Date updateDate;

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public static long getSerialVersionUID() {
		return serialVersionUID;
	}

	public Integer getOrg() {
		return org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getBusiness() {
		return this.business;
	}

	public void setBusiness(Integer business) {
		this.business = business;
	}

	public String getTitle() {
		return this.title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public Integer getItem() {
		return this.item;
	}

	public void setItem(Integer item) {
		this.item = item;
	}

	public String getStatus() {
		return this.status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Integer getLevel() {
		return this.level;
	}

	public void setLevel(Integer level) {
		this.level = level;
	}

	public Integer getAuditor() {
		return this.auditor;
	}

	public void setAuditor(Integer auditor) {
		this.auditor = auditor;
	}

	public String getAuditorName() {
		return this.auditorName;
	}

	public void setAuditorName(String auditorName) {
		this.auditorName = auditorName;
	}

	public Date getAuditDate() {
		return this.auditDate;
	}

	public void setAuditDate(Date auditDate) {
		this.auditDate = auditDate;
	}

	public String getOperation() {
		return this.operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public String getApplyMemo() {
		return this.applyMemo;
	}

	public void setApplyMemo(String applyMemo) {
		this.applyMemo = applyMemo;
	}

	public String getApproveMemo() {
		return this.approveMemo;
	}

	public void setApproveMemo(String approveMemo) {
		this.approveMemo = approveMemo;
	}

	public String getDescription() {
		return this.description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Integer getMessageId() {
		return this.messageId;
	}

	public void setMessageId(Integer messageId) {
		this.messageId = messageId;
	}

	public Integer getCreator() {
		return this.creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return this.createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getUpdator() {
		return this.updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return this.updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return this.updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

}
