package cn.sphd.miners.modules.message.controller;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.commodity.entity.PdBase;
import cn.sphd.miners.modules.commodity.entity.PdMerchandise;
import cn.sphd.miners.modules.commodity.service.ProductService;
import cn.sphd.miners.modules.forumArea.service.ForumService;
import cn.sphd.miners.modules.material.entity.MtStock;
import cn.sphd.miners.modules.material.service.MtStockService;
import cn.sphd.miners.modules.message.service.MessageService;
import cn.sphd.miners.modules.personal.entity.*;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.personal.service.OvertimeService;
import cn.sphd.miners.modules.personal.service.UserMessageService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserLogService;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2017/3/27.
 */
@Controller
@RequestMapping("/message")
public class MessageController {
    @Autowired
    MessageService messageService;
    @Autowired
    UserMessageService userMessageService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    MtStockService mtStockService;
    @Autowired
    ProductService productService;
    @Autowired
    ForumService forumService;
    @Autowired
    UserService userService;
    @Autowired
    UserLogService userLogService;
    @Autowired
    OvertimeService overtimeService;

    /**
     *<AUTHOR>
     *@date 2017/5/19 10:04
     *跳转消息页面
    */
    @RequestMapping("/messagePage.do")
    public String messagePage(){
        return "/message/message";
    }


    /**
     *<AUTHOR>
     *@date 2017/3/28 10:14
     *获取所有的消息
     * status 1-未处理  2-已处理
    */
    @ResponseBody
    @RequestMapping("/getAllMessage.do")
    public void getAllMessage(Integer status,User user, PageInfo pageInfo, HttpServletResponse response, HttpSession session) throws IOException {
        Map<String,Object> map = new HashMap<>();
        if (status!=null){
            List<UserMessage> userMessages = messageService.getMessageByUidAndState(status,user.getUserID(),pageInfo);
            if (userMessages.size()>0){
                for (UserMessage userMessage:userMessages) {
                    if (userMessage.getUser()!=null){
                        userMessage.setApplicant(userMessage.getUser().getUserName());
                    }
                }
            }
            map.put("userMessages",userMessages);
            map.put("pageInfo",pageInfo);
        }
        ObjectToJson.objectToJson1(map,new String[]{"user"},response);
    }

    /**
     *<AUTHOR>
     *@date 2017/3/28 11:53
     *查看消息明细
     * userMessageId 此消息的id，eventType 事件类型
    */
    @ResponseBody
    @RequestMapping("/getMessageDetail.do")
    public void getMessageDetail(Integer userMessageId,String eventType,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        UserMessage userMessage = userMessageService.getUserMessageById(userMessageId);
            String messageType = userMessage.getMessageType(); //消息类型  1-财务  2-加班  3-请假   4-报销
            if ("1".equals(messageType)){
                map.put("source",1);
                map.put("content",null);  //财务信息预留
            }else if ("2".equals(messageType)){
                PersonnelOvertime personnelOvertime = overtimeService.getPersonnelOvertimeById(userMessage.getMessageId());
                map.put("source",2);
                map.put("content",personnelOvertime);  //加班审批的信息
            }else if ("3".equals(messageType)){
                PersonnelLeave personnelLeave = userMessageService.getPersonnelLeaveById(userMessage.getMessageId());
                map.put("source",3);
                map.put("content",personnelLeave);  //请假审批的信息
            }else if("4".equals(messageType)){
                ApprovalProcess a = approvalProcessService.getApprovalProcessById(userMessage.getMessageId());
                if (a!=null){
                    PersonnelReimburse personnelReimburse = a.getReimburse();
                    map.put("content",personnelReimburse);
                }
                map.put("source",4);
            }else if("5".equals(messageType)){//入库
                List<UserMessage> userMessageList=userMessageService.getManyMessageByMtStock(userMessage.getMtStockId());
                if(userMessageList.get(0).getId()==userMessageId){
                MtStock mtStock=mtStockService.getById(userMessage.getMtStockId());
                if(mtStock!=null) {
                    PdMerchandise pdMerchandise = productService.getByMtStock0(mtStock.getProductId());
                    if (pdMerchandise != null) {
                        PdBase pdBase = mtStockService.getPdBaseById(pdMerchandise.getProduct_());
                        mtStock.setInnerSn(pdBase.getInnerSn());
                        mtStock.setInnerSnName(pdBase.getName());
                        mtStock.setUnit(pdBase.getUnit());
                        mtStock.setOuterSn(pdMerchandise.getOuterSn());
                        mtStock.setOuterName(pdMerchandise.getOuterName());

                    }
                }

                map.put("mtStock",mtStock);
                map.put("userMessageId",userMessageId);
                map.put("num",userMessage.getTitle());
                map.put("createTime",new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(userMessage.getCreateDate()));
                String s="0";
                try {
                    s=mtStock.getInPlan().stripTrailingZeros().toPlainString();
                }catch (Exception e){

                }

                map.put("record","仓库的操作者将商品的数量由"+s+"更改为"+userMessage.getTitle());
                ObjectToJson.objectToJson1(map,new String[]{"applyId","inFact","creator","createName","createDate","billNo","updator","updateName","updateDate","unitPrice","storageNo","storageLocation","state","source","reason","qualified","outPlan","outFact",
                        "orderNo","orderId","operatorName","operator","oid","memo","materialName","material","inspectionNo","destination","description","accountNo","userID","type","totalAmount","state","senderName","sender","sendTime","receiveUserId","qingjiatype","pushTime",
                        "personnelReimburId","operationType","messageType","messageId","level","isNull","illustrate","historyId","handleTime",
                        "handleReply","handleName","handleId","function","feeCatName",
                        "feeCat","eventType","endTime","duration","content","clientId","billCatName","billCat","beginTime","approvalStatus","approvalItem","approDateTime","applicant","accountType"
                        ,"accountOrDetailed","accountId","user","reimburset","approvalFlow","reimburse"},response);
                return;}else{
                    map.put("status",0);//该消息已被修改
                    ObjectToJson.objectToJson1(map,new String[]{""},response);
                    return;

                }
            }
            if ("请假结果".equals(eventType) || "加班结果".equals(eventType) || "报销结果".equals(eventType) || "两讫结果".equals(eventType)){
                userMessage.setState(2);  //对消息的处理  1-未处理 2-已处理
                userMessageService.updateUserMassage(userMessage);  //更新消息提醒的状态
            }
            map.put("userMessage",userMessage);  //请假和加班审批流程
        ObjectToJson.objectToJson1(map,new String[]{"user","reimburset","approvalFlow","reimburse"},response);
    }
    
    /**
     *<AUTHOR>
     *@date 2017/4/10 17:18
     *消息数等轮询接口
    */
    @ResponseBody
    @RequestMapping("/getMessageNum.do")
    public String getMessageNum(User user) {
        Map<String,Object> map = new HashMap<>();
        Long userMessageNumber = userMessageService.getMessageCountByUidAndState(user.getUserID(), 1);
        map.put("userMessageNumber",userMessageNumber);
        map.put("forumCount", null!=user.getForumCount()?user.getForumCount():0);

        return JSONObject.toJSONString(map);
    }

    /**
     *<AUTHOR>
     *@date 2017/4/12 14:01
     *从消息提醒中跳转到报销详情的页面
     * userMessageId 此消息的id
    */
    @RequestMapping("/getPersonnelReimburseDetail.do")
    public String getPersonnelReimburseDetail(Integer userMessageId, Model model,HttpSession session) throws IOException {
        User user = (User) session.getAttribute("user");
        UserMessage userMessage = userMessageService.getUserMessageById(userMessageId);
        if (userMessage!=null){
            ApprovalProcess a = approvalProcessService.getApprovalProcessById(userMessage.getMessageId());
            if (a!=null){
                PersonnelReimburse personnelReimburse = a.getReimburse();
                List<ApprovalProcess> processList=approvalProcessService.getApprovalProcessByPersonnelReimburseId(personnelReimburse.getId());
                model.addAttribute("personnelReimburse",personnelReimburse);
                model.addAttribute("processList",processList);
            }else {
                model.addAttribute("personnelReimburse", "");
            }
            if (userMessage.getEventType().equals("报销结果")||userMessage.getEventType().equals("两讫结果") ||userMessage.getApprovalStatus()==3){
//                userMessage.setState(2);  //对消息的处理  1-未处理 2-已处理
//                userMessageService.updateUserMassage(userMessage);  //更新消息提醒的状态

                List<UserMessage> userMessages = messageService.getMessageByPersonnelReimburseId(a.getReimburse().getId(),1,user.getUserID(),4);//消息类型  1-财务  2-加班  3-请假   4-报销
                if (userMessages.size()>0){
                    for (UserMessage u:userMessages) {
                        u.setState(2);  //对消息的处理  1-未处理 2-已处理
                        userMessageService.updateUserMassage(u);  //更新消息提醒的状态
                    }
                }

            }
            model.addAttribute("isNull",userMessage.getIsNull());
        }
        return "/user/manageBill";
    }

}
