package cn.sphd.miners.modules.resourceAuthority.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2022-01-28 
 */

@Entity
@Table ( name ="t_resource_attachment_history" )
public class ResAttHis implements Serializable {

	/**
	 * ID
	 */
	@Id
	@Column(name="id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * 机构ID
	 */
	@Column(name = "org", nullable=true , unique=false )
	private Integer org;

	/**
	 * 附件ID
	 */
   	@Column(name = "attachment", nullable=true , unique=false )
	private Integer attachment;

	/**
	 * 编号
	 */
	@Column(name = "sn", length=255, nullable=true , unique=false )
	private String sn;

	/**
	 * 名称
	 */
	@Column(name = "name", length=50, nullable=true , unique=false )
	private String name;

	/**
	 * 类型:以扩展名或自定义,xlsx-excel表格,docx-word文档
	 */
	@Column(name = "type", length=20, nullable=true , unique=false )
	private String type;

	/**
	 * 路径
	 */
	@Column(name = "path", length=255, nullable=true , unique=false )
	private String path;

	/**
	 * 大小
	 */
	@Column(name = "size",  nullable=true , unique=false )
	private Long size;

	/**
	 * 状态:false-不启用,true-启用
	 */
	@Column(name = "enabled", nullable=true , unique=false )
	private Boolean enabled;

	/**
	 * 启停用时间
	 */
	@Column(name = "enabled_time", nullable=true , unique=false )
	private Date enabledTime;

	/**
	 * 状态:0-未关联,1-已关联
	 */
	@Column(name = "state", length=1, nullable=true , unique=false )
	private String state;

	/**
	 * 版本号
	 */
	@Column(name = "version", length=50, nullable=true , unique=false )
	private String version;

	/**
	 * 备注
	 */
	@Column(name = "memo", length=255, nullable=true , unique=false )
	private String memo;

	/**
	 * 创建人id
	 */
	@Column(name = "creator", nullable=true , unique=false )
	private Integer creator;

	/**
	 * 创建人
	 */
	@Column(name = "create_name", length=100, nullable=true , unique=false )
	private String createName;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date", nullable=true , unique=false )
	private Date createDate;

	/**
	 * 修改人id
	 */
	@Column(name = "updator", nullable=true , unique=false )
	private Integer updator;

	/**
	 * 修改人
	 */
	@Column(name = "update_name", length=255, nullable=true , unique=false )
	private String updateName;

	/**
	 * 修改时间
	 */
	@Column(name = "update_date", nullable=true , unique=false )
	private Date updateDate;

	/**
	 * 对应消息表的id
	 */
	@Column(name = "message_id", nullable=true , unique=false )
	private Integer messageId;

	/**
	 * 操作:1-增,2-删,3-改
	 */
	@Column(name = "operation", length=1, nullable=true , unique=false )
	private String operation;

	/**
	 * 修改前记录ID
	 */
	@Column(name = "previous_id", nullable=true , unique=false )
	private Integer previousId;

	/**
	 * 版本号,每次修改+1
	 */
	@Column(name = "version_no", nullable=true , unique=false )
	private Integer versionNo;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOrg() {
		return org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getAttachment() {
		return attachment;
	}

	public void setAttachment(Integer attachment) {
		this.attachment = attachment;
	}

	public String getSn() {
		return sn;
	}

	public void setSn(String sn) {
		this.sn = sn;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public Long getSize() {
		return size;
	}

	public void setSize(Long size) {
		this.size = size;
	}

	public Boolean getEnabled() {
		return enabled;
	}

	public void setEnabled(Boolean enabled) {
		this.enabled = enabled;
	}

	public Date getEnabledTime() {
		return enabledTime;
	}

	public void setEnabledTime(Date enabledTime) {
		this.enabledTime = enabledTime;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getCreator() {
		return creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getUpdator() {
		return updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Integer getMessageId() {
		return messageId;
	}

	public void setMessageId(Integer messageId) {
		this.messageId = messageId;
	}

	public String getOperation() {
		return operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public Integer getPreviousId() {
		return previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}
}
