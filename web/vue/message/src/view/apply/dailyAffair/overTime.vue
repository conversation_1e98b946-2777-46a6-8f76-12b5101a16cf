<template>
  <div id="overTimeApply">
    <TY_NavTop title="加班申请" isSearch="true" @toggleList="search"></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待处理" name="1" closable='false'>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in applyHandle" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.beginTime | formatDay('YYYY年MM月DD日 dddd')}}</div>
                <div v-if="item.actualType !== '1'">计划时长  {{item.duration}}h</div>
                <div v-if="item.actualType === '1'">申报时长  {{item.actualDuration}}h</div>
              </div>
            </div>
            <div class="ui-ti" v-show="item.kind === 2">补</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="已批准" name="2" closable='false' :msgCount="applyApprovalCount">
        <div class="tipAv">以下申请已审批通过。加班完成后，请及时申报实际情况！</div>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in applyApproval" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.beginTime | formatDay('YYYY年MM月DD日 dddd')}}</div>
                <div>计划时长  {{item.duration}}h</div>
              </div>
            </div>
            <div class="ui-ti" v-show="item.kind === 2">补</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>
<style lang="less">
#overTimeApply{
  .tipAv{ margin-left: 10px;margin-top: 4px;font-size: 1.2rem;}
}
</style>
<script>
export default {
  name: 'applyOverTime',
  data () {
    return {
      tabValue: '1',
      applyHandle: [],
      applyApproval: [],
      listenersUid: []
    }
  },
  computed: {
    applyApprovalCount: function () {
      return this.applyApproval.length
    }
  },
  created () {
    this.tabValue = this.$route.params.tabValue || '1'
    let that = this
    this.listenersUid = [
      this.sphdSocket.subscribe('applyOutTimeHandle', function (data) {
        that.applyHandle = JSON.parse(data)
        console.log('applyOutTimeHandle session Socket received OK:' + data)
      }),

      this.sphdSocket.subscribe('applyOutTimeHandle', function (data) {
        console.log('applyOutTimeHandle user Socket received OK:' + data)

        let getData = JSON.parse(data)
        let personnelOvertime = getData.personnelOvertime
        let operate = Number(getData.operate)
        if (operate > 0) {
          if (personnelOvertime.actualType === '1') {
            that.applyHandle.push(personnelOvertime)
          } else {
            that.applyHandle.unshift(personnelOvertime)
          }
        } else if (operate < 0) {
          let leaveId = personnelOvertime.id
          let _index = -1
          that.applyHandle.forEach(function (item, index) {
            if (item.id === leaveId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.applyHandle.splice(_index, 1)
          }
        }
      }, function () {
        console.log('applyOutTimeHandle user Socket Error:')
      }, 'user'),

      this.sphdSocket.subscribe('applyOutTimeApproval', function (data) {
        console.log('applyOutTimeApproval session Socket received OK:' + data)
        that.applyApproval = JSON.parse(data)
      }),

      this.sphdSocket.subscribe('applyOutTimeApproval', function (data) {
        console.log('applyOutTimeApproval user Socket received OK:' + data)

        let getData = JSON.parse(data)
        let personnelOvertime = getData.personnelOvertime
        let operate = Number(getData.operate)
        if (operate > 0) {
          if (personnelOvertime.actualType === '1') {
            that.applyApproval.push(personnelOvertime)
          } else {
            that.applyApproval.unshift(personnelOvertime)
          }
        } else if (operate < 0) {
          let leaveId = personnelOvertime.id
          let _index = -1
          that.applyApproval.forEach(function (item, index) {
            if (item.id === leaveId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.applyApproval.splice(_index, 1)
          }
        }
      }, function () {
        console.log('applyOutTime user Socket Error:')
      }, 'user')
    ]

    let userId = this.sphdSocket.user.userID
    let session = this.sphdSocket.sessionid
    this.sphdSocket.send('applyOutTime', { userId: userId, session: session })
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    jump: function (id) {
      this.$router.push({
        path: `/applyOutTimeDetail/${id}`//,
        // params: {
        //   tabValue: this.tabValue || '2'
        // }
      })
    },
    search: function () {
      this.$router.push({
        path: `/applyOverTimeQuery`
      })
    }
  },
  watched: {
    activeKey (curVal, oldVal) {
      console.log(curVal, oldVal)
    }
  }
}
</script>
