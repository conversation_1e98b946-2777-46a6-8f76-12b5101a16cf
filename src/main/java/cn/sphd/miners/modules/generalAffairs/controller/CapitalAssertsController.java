package cn.sphd.miners.modules.generalAffairs.controller;

import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.generalAffairs.entity.FinanceAssetsMaintain;
import cn.sphd.miners.modules.generalAffairs.entity.FinanceAssetsReceive;
import cn.sphd.miners.modules.generalAffairs.entity.FinanceFixedAssets;
import cn.sphd.miners.modules.generalAffairs.service.FinanceAssetsMaintainService;
import cn.sphd.miners.modules.generalAffairs.service.FinanceAssetsReceiveService;
import cn.sphd.miners.modules.generalAffairs.service.FinanceFixedAssetsService;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.Collator;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by Administrator on 2017/7/6.
 */
@Controller
@RequestMapping("/capitalAsserts")
public class CapitalAssertsController {

    @Autowired
    FinanceAssetsMaintainService financeAssetsMaintainService;
    @Autowired
    FinanceFixedAssetsService financeFixedAssetsService;
    @Autowired
    FinanceAssetsReceiveService financeAssetsReceiveService;


    /**
     * @Author:林成
     * @Date:2017/7/6 11:14
     * @description:固定资产
     */
    @RequestMapping("/capitalAsserts.do")
    public String ProjectBase(User user, Model model) {
        return "/generalAffairs/capitalAsserts";
    }


    /**
     * @Author:林成
     * @Date:2017/7/11 12:12
     * @description:新增固定资产 url:http://localhost:9991/capitalAsserts/addCapitalAsserts.do?snName=SDK0012&name=11&modelNumber=1554&buyerName=154&buyDate=1991/01/01&storageDate=1991/01/02&originalValue=124&memo=124
     */
    @RequestMapping("/addCapitalAsserts.do")
    @ResponseBody
    public void addCapitalAsserts(HttpServletResponse response, String storageDate1, String buyDate1, FinanceFixedAssets financeFixedAssets, User user) throws IOException, ParseException {
        Map<String, Object> map = new HashMap<String, Object>();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        Organization organization = user.getOrganization();
        FinanceFixedAssets ffa = new FinanceFixedAssets();
        Integer org = user.getOid();
        String sourceStr = financeFixedAssets.getSnPrefix();//前缀
        String fSn = financeFixedAssets.getSn();
        if (sourceStr.equals("") || fSn.equals("")) {
            List<FinanceFixedAssets> ffaList = financeFixedAssetsService.getAllSnNameList(org);//查询固定资产
            if (ffaList.size() > 0) {
                String snPrefix = ffaList.get(0).getSnPrefix();
                String sn = ffaList.get(0).getSn();
                Integer integer1 = Integer.parseInt(ffaList.get(0).getSn());
                String hb = snPrefix + sn;
                String result = "";
                String valueOfSn = "";
                Set setValue = new HashSet();
                for (FinanceFixedAssets f : ffaList) {
                    valueOfSn = f.getSnPrefix() + f.getSn().toString();
                    setValue.add(valueOfSn);
                }
                while (setValue.contains(hb)) {
                    integer1 += 1;
                    if (sn.length() != integer1.toString().length() && sn.substring(0, 1).equals("0")) {//判断是否包含0开头
                        Integer appendNum = sn.length() - integer1.toString().length();
                        for (int i = 0; i < appendNum; i++) {
                            result += "0";
                            sn = integer1.toString();
                        }

                    } else {
                        sn = integer1.toString();
                    }
                    if (result.equals("")) {
                        hb = snPrefix + sn;
                    } else {
                        hb = snPrefix + result + sn;
                    }

                }

                ffa.setSnPrefix(snPrefix);
                ffa.setSn(result + sn);
                ffa.setName(financeFixedAssets.getName());//固定资产名称
                if (financeFixedAssets.getModeNumber() != null) {
                    ffa.setModeNumber(financeFixedAssets.getModeNumber());//型号
                }
                if (financeFixedAssets.getBuyerName() != null) {
                    ffa.setBuyerName(financeFixedAssets.getBuyerName());//采购员姓名
                }
                if (buyDate1 != null && !buyDate1.equals("")) {
                    ffa.setBuyDate(formatter.parse(buyDate1));//采购员日期
                }
                if (storageDate1 != null && !storageDate1.equals("")) {
                    ffa.setStorageDate(formatter.parse(storageDate1));//入库日期
                }
                if (financeFixedAssets.getOriginalValue() != null) {
                    ffa.setOriginalValue(financeFixedAssets.getOriginalValue());//原值
                }
                if (financeFixedAssets.getMemo() != null) {
                    ffa.setMemo(financeFixedAssets.getMemo());//备注
                }
                ffa.setOrg(organization);
                ffa.setCreateDate(new Date());
                ffa.setCreateName(user.getUserName());
                ffa.setCreator(user.getUserID());
                ffa.setOperation("1");//操作：1-增，2—删，3-—改
                ffa.setUseState("1");//使用状态:0-报废,1-在用,2-维修中
                ffa.setReceiveState("0");//领用状态:0-正常,1-领用
                financeFixedAssetsService.addFinanceFixedAssets(ffa);
                map.put("financeFixedAssets", ffa);
                map.put("status", 1);//新增成功
                ObjectToJson.objectToJson1(map, new String[]{"org", "financeAssetsMaintainHashSet", "financeAssetsReceiveHashSet",
                        "applyMemo", "approveItem", "approveLevel", "approveMemo", "approveStatus", "auditor", "auditorName", "auditDate", "createDate",
                        "createName", "creator", "operation", "updateDate", "updateName", "updator", "summary", "snPrefix",
                        "sn", "messageId", "buyer", "org_", "receiveDate", "receiverName"}, response);

                return;

            } else {
                ffa.setSnPrefix("XH");
                ffa.setSn("0001");
                ffa.setName(financeFixedAssets.getName());//固定资产名称
                if (financeFixedAssets.getModeNumber() != null) {
                    ffa.setModeNumber(financeFixedAssets.getModeNumber());//型号
                }
                if (financeFixedAssets.getBuyerName() != null) {
                    ffa.setBuyerName(financeFixedAssets.getBuyerName());//采购员姓名
                }
                if (buyDate1 != null && !buyDate1.equals("")) {
                    ffa.setBuyDate(formatter.parse(buyDate1));//采购员日期
                }
                if (storageDate1 != null && !storageDate1.equals("")) {
                    ffa.setStorageDate(formatter.parse(storageDate1));//入库日期
                }
                if (financeFixedAssets.getOriginalValue() != null) {
                    ffa.setOriginalValue(financeFixedAssets.getOriginalValue());//原值
                }
                if (financeFixedAssets.getMemo() != null) {
                    ffa.setMemo(financeFixedAssets.getMemo());//备注
                }
                ffa.setOrg(organization);
                ffa.setCreateDate(new Date());
                ffa.setCreateName(user.getUserName());
                ffa.setCreator(user.getUserID());
                ffa.setOperation("1");//操作：1-增，2—删，3-—改
                ffa.setUseState("1");//使用状态:0-报废,1-在用,2-维修中
                ffa.setReceiveState("0");//领用状态:0-正常,1-领用
                financeFixedAssetsService.addFinanceFixedAssets(ffa);
                map.put("financeFixedAssets", ffa);
                map.put("status", 1);//新增成功
                ObjectToJson.objectToJson1(map, new String[]{"org", "financeAssetsMaintainHashSet", "financeAssetsReceiveHashSet",
                        "applyMemo", "approveItem", "approveLevel", "approveMemo", "approveStatus", "auditor", "auditorName", "auditDate", "createDate",
                        "createName", "creator", "operation", "updateDate", "updateName", "updator", "summary", "snPrefix",
                        "sn", "messageId", "buyer", "org_", "receiveDate", "receiverName"}, response);
                return;
            }

        }


        Integer integer1 = Integer.parseInt(fSn);//序号
        String[] sourceStrArray = sourceStr.split("");//分割字符串
        int count = 0;
        for (int i = sourceStrArray.length - 1; i >= 0; i--) {
            Pattern pattern = Pattern.compile("[0-9]*");//数字判断
            Matcher isNum = pattern.matcher(sourceStrArray[i]);
            if (!isNum.matches()) {
                count++;
            }
        }

        if (count != sourceStr.length()) {
            map.put("status", 2);//前缀不可以包含数字
            ObjectToJson.objectToJson1(map, new String[]{""}, response);
            return;
        }
        String hb = sourceStr + fSn;//snPrefix+sn
        List<FinanceFixedAssets> snName = financeFixedAssetsService.getAllSnNameList(org);//查询固定资产
        String result = "";

        if (snName.size() > 0) {
            String valueOfSn = "";
            Set setValue = new HashSet();
            for (FinanceFixedAssets f : snName) {
                valueOfSn = f.getSnPrefix() + f.getSn().toString();
                setValue.add(valueOfSn);

            }

            while (setValue.contains(hb)) {
                integer1 += 1;
                if (fSn.length() != integer1.toString().length() && fSn.substring(0, 1).equals("0")) {//判断是否包含0开头
                    Integer appendNum = financeFixedAssets.getSn().length() - integer1.toString().length();
                    for (int i = 0; i < appendNum; i++) {
                        result += "0";
                        fSn = integer1.toString();
                    }

                } else {
                    fSn = integer1.toString();
                }
                if (result.equals("")) {
                    hb = sourceStr + fSn;
                } else {
                    hb = sourceStr + result + fSn;
                }
            }


            ffa.setSnPrefix(sourceStr);
            ffa.setSn(result + fSn);


        } else if (snName.size() == 0) {

            ffa.setSnPrefix(sourceStr);
            ffa.setSn(fSn);

        } else {
            ffa.setSnPrefix("XH");
            ffa.setSn("0001");
        }


        ffa.setName(financeFixedAssets.getName());//固定资产名称
        if (financeFixedAssets.getModeNumber() != null) {
            ffa.setModeNumber(financeFixedAssets.getModeNumber());//型号
        }
        if (financeFixedAssets.getBuyerName() != null) {
            ffa.setBuyerName(financeFixedAssets.getBuyerName());//采购员姓名
        }
        if (buyDate1 != null && !buyDate1.equals("")) {
            ffa.setBuyDate(formatter.parse(buyDate1));//采购员日期
        }
        if (storageDate1 != null && !storageDate1.equals("")) {
            ffa.setStorageDate(formatter.parse(storageDate1));//入库日期
        }
        if (financeFixedAssets.getOriginalValue() != null) {
            ffa.setOriginalValue(financeFixedAssets.getOriginalValue());//原值
        }
        if (financeFixedAssets.getMemo() != null) {
            ffa.setMemo(financeFixedAssets.getMemo());//备注
        }
        ffa.setOrg(organization);
        ffa.setCreateDate(new Date());
        ffa.setCreateName(user.getUserName());
        ffa.setCreator(user.getUserID());
        ffa.setOperation("1");//操作：1-增，2—删，3-—改
        ffa.setUseState("1");//使用状态:0-报废,1-在用,2-维修中
        ffa.setReceiveState("0");//领用状态:0-正常,1-领用
        financeFixedAssetsService.addFinanceFixedAssets(ffa);
        map.put("financeFixedAssets", ffa);
        map.put("status", 1);//新增成功
        ObjectToJson.objectToJson1(map, new String[]{"org", "financeAssetsMaintainHashSet", "financeAssetsReceiveHashSet",
                "applyMemo", "approveItem", "approveLevel", "approveMemo", "approveStatus", "auditor", "auditorName", "auditDate", "createDate",
                "createName", "creator", "operation", "updateDate", "updateName", "updator", "summary", "snPrefix",
                "sn", "messageId", "buyer", "org_", "receiveDate", "receiverName"}, response);


    }

    /**
     * @Author:林成
     * @Date:2017/7/11 14:07
     * @description:总务固定资产分页--在用/已报废
     */
    @RequestMapping("/getAllCapitalAsserts.do")
    @ResponseBody
    public void getAllCapitalAsserts(HttpServletResponse response, User user, Integer currPage, Integer rank, Integer pageSize, String useState) throws IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        Integer org = user.getOid();
        //User user=(User)session.getAttribute("user");
        if (useState != null) {
            List<FinanceFixedAssets> financeFixedAssetsList = new ArrayList<FinanceFixedAssets>();
            if (rank == 0) {
                financeFixedAssetsList = financeFixedAssetsService.getFinanceFixedAssetsByOrg(org, useState);
            }
            if (rank == 1) {//降序
                List<FinanceFixedAssets> ffa = financeFixedAssetsService.getFinanceFixedAssetsByOrg1(org, useState);
                Collections.sort(ffa, new ComparatorUser());

                if (ffa.size() > 0) {
                    for (int i = ffa.size() - 1; i >= 0; i--) {
                        financeFixedAssetsList.add(ffa.get(i));
                    }
                }
            }
            if (rank == 2) {//升序
                financeFixedAssetsList = financeFixedAssetsService.getFinanceFixedAssetsByOrg2(org, useState);
                Collections.sort(financeFixedAssetsList, new ComparatorUser());

            }
            if (rank == 3) {
                financeFixedAssetsList = financeFixedAssetsService.getFinanceFixedAssetsByOrg3(org, useState);
            }
            if (rank == 4) {
                financeFixedAssetsList = financeFixedAssetsService.getFinanceFixedAssetsByOrg4(org, useState);

            }
            for (FinanceFixedAssets financeFixedAssets : financeFixedAssetsList) {
                List<FinanceAssetsReceive> far = financeAssetsReceiveService.getFinanceAssetsReceiveList(financeFixedAssets.getId());

                if (far.size() > 0) {
                    if (financeFixedAssets.getFinanceAssetsReceiveHashSet().size() > 0) {
                        financeFixedAssets.setLyghRecord(1);//有领用记录
                    } else {
                        financeFixedAssets.setLyghRecord(0);//无
                    }
                    if (financeFixedAssets.getFinanceAssetsMaintainHashSet().size() > 0) {
                        financeFixedAssets.setWxRecord(1);//有维修记录

                    } else {
                        financeFixedAssets.setWxRecord(0);//无记录
                    }

                    financeFixedAssets.setReceiverName(far.get(0).getReceiverName());
                    financeFixedAssets.setReceiveDate(far.get(0).getReceiveDate());
                    financeFixedAssets.setFarId(far.get(0).getId());
                } else {

                    if (financeFixedAssets.getFinanceAssetsReceiveHashSet().size() > 0) {
                        financeFixedAssets.setLyghRecord(1);//有领用记录
                    } else {
                        financeFixedAssets.setLyghRecord(0);//无
                    }
                    if (financeFixedAssets.getFinanceAssetsMaintainHashSet().size() > 0) {
                        financeFixedAssets.setWxRecord(1);//有维修记录

                    } else {
                        financeFixedAssets.setWxRecord(0);//无记录
                    }

                }
            }


            if (currPage == null || "".equals(currPage) || currPage <= 0) {
                currPage = 1;//默认当前页
            }

            int totalPage = 1;  //默认总页数

            if (pageSize == null || "".equals(pageSize) || pageSize <= 0) {
                map.put("currPage", currPage);//当前页
                map.put("totalPage", totalPage);//总页数
                map.put("financeFixedAssets", financeFixedAssetsList);//

            } else {
                int maxResult = currPage * pageSize;//显示最大结果
                int minResult = (currPage - 1) * pageSize;//初始化结果
                int total = financeFixedAssetsList.size();
               /*
                * 计算总页数
               * */
                double total1 = total;
                double pageSize1 = pageSize;
                double num = total1 / pageSize1;
                double totalPage1 = Math.ceil(num);
                totalPage = (int) totalPage1;
                if (total > 0) {
                    List<FinanceFixedAssets> list = new ArrayList<FinanceFixedAssets>();
                    for (int i = minResult; i < maxResult; i++) {
                        if (i < total) {
                            list.add(financeFixedAssetsList.get(i));
                        }
                    }
                    map.put("currPage", currPage);//当前页
                    map.put("totalPage", totalPage);//总页数
                    map.put("financeFixedAssets", list);//
                } else {
                    totalPage = 1;
                    map.put("currPage", currPage);//当前页
                    map.put("totalPage", totalPage);//总页数
                    map.put("financeFixedAssets", financeFixedAssetsList);//
                }
            }
            ObjectToJson.objectToJson1(map, new String[]{"org", "financeAssetsMaintainHashSet", "financeAssetsReceiveHashSet"
                    , "messageId", "approveMemo", "applyMemo", "operation", "auditDate", "auditorName", "auditor", "approveLevel", "approveStatus"
                    , "approveItem", "updateName", "updator", "createDate", "createName", "creator", "summary", "buyer"
            }, response);


        } else {

            map.put("status", 0);//在用/已报废 状态为空
            ObjectToJson.objectToJson1(map, new String[]{""}, response);

        }

    }


    /**
     * @Author:林成
     * @Date:2017/7/12 9:59
     * @description:固定资产--领用/归还/报废
     */
    @RequestMapping("/addFinanceAssetsReceive.do")
    @ResponseBody
    public void addFinanceAssetsReceive(HttpServletResponse response, String returnDate1, String receiveDate1, User user, Integer id, Integer sign, String reason, Integer farId, FinanceAssetsReceive financeAssetsReceive) throws IOException, ParseException {
        Map<String, Object> map = new HashMap<String, Object>();
        Integer org = user.getOid();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        FinanceFixedAssets financeFixedAssets = financeFixedAssetsService.getFinanceFixedAssets(org, id);

        FinanceAssetsReceive far = new FinanceAssetsReceive();
        if (sign == 1) {//领用
            far.setCreator(user.getUserID());
            far.setCreateDate(new Date());
            far.setCreateName(user.getUserName());
            far.setAssets(financeFixedAssets);//固定资产
            if (receiveDate1 != null && !receiveDate1.equals("")) {
                far.setReceiveDate(formatter.parse(receiveDate1));//领用时间
            } else {
                map.put("status", 3);//必输项为空
                ObjectToJson.objectToJson1(map, new String[]{""}, response);

            }
            far.setReceiveDeptName(financeAssetsReceive.getReceiveDeptName());//领用部门名称
            if (financeAssetsReceive.getReceiverName() != null) {
                far.setReceiverName(financeAssetsReceive.getReceiverName());//领用人
            } else {
                map.put("status", 2);//必输项为空
                ObjectToJson.objectToJson1(map, new String[]{""}, response);

            }
            financeFixedAssets.setReceiveState("1");
            financeFixedAssets.setOperation("3");
            financeFixedAssetsService.updateFinanceFixedAssets(financeFixedAssets);
            financeAssetsReceiveService.addFinanceAssetsReceive(far);
            map.put("status", 1);
            map.put("financeAssetsReceive", far);
            ObjectToJson.objectToJson1(map, new String[]{"assets"}, response);

        }

        if (sign == 2) {//归还
            FinanceAssetsReceive far0 = financeAssetsReceiveService.getFinanceAssetsReceive(farId);
            far0.setUpdator(user.getUserID());
            far0.setUpdateDate(new Date());
            far0.setUpdateName(user.getUserName());
            far0.setAssets(financeFixedAssets);//固定资产
            if (returnDate1 != null && !returnDate1.equals("")) {
                far0.setReturnDate(formatter.parse(returnDate1));//归还日期
            } else {
                map.put("status", 5);//必输项为空
                ObjectToJson.objectToJson1(map, new String[]{""}, response);

            }
            if (financeAssetsReceive.getReturnerName() != null) {
                far0.setReturnerName(financeAssetsReceive.getReturnerName());//归还人
            } else {
                map.put("status", 6);//必输项为空
                ObjectToJson.objectToJson1(map, new String[]{""}, response);

            }
            far0.setMemo(financeAssetsReceive.getMemo());//归还备注
            financeFixedAssets.setReceiveState("0");
            financeFixedAssets.setOperation("3");
            financeFixedAssetsService.updateFinanceFixedAssets(financeFixedAssets);
            financeAssetsReceiveService.updateFinanceAssetsReceive(far0);
            map.put("status", 7);
            map.put("financeAssetsReceive", far0);
            ObjectToJson.objectToJson1(map, new String[]{"assets"}, response);

        }

        if (sign == 3) {//报废
            financeFixedAssets.setUseState("0");
            financeFixedAssets.setUpdateDate(new Date());
            financeFixedAssets.setUpdator(user.getUserID());
            financeFixedAssets.setUpdateName(user.getUserName());
            financeFixedAssets.setRetirementDate(new Date());
            financeFixedAssets.setOperation("3");
            if (!reason.equals("") && reason != null) {
                financeFixedAssets.setReason(reason);
            }
            financeFixedAssetsService.updateFinanceFixedAssets(financeFixedAssets);
            map.put("status", 9);
            map.put("financeFixedAssets", financeFixedAssets);
            ObjectToJson.objectToJson1(map, new String[]{"org", "financeAssetsMaintainHashSet", "financeAssetsReceiveHashSet"
                    , "messageId", "approveMemo", "applyMemo", "operation", "auditDate", "auditorName", "auditor", "approveLevel", "approveStatus"
                    , "approveItem", "updateDate", "updateName", "updator", "createDate", "createName", "creator", "summary", "buyer", "sn"
                    , "snPrefix"}, response);

        }


    }


    /**
     * @Author:林成
     * @Date:2017/7/12 11:29
     * @description:固定资产维修
     */
    @RequestMapping("/addFinanceAssetsMaintain.do")
    @ResponseBody
    public void addFinanceAssetsMaintain(HttpServletResponse response, String repairDate1, User user, Integer id, FinanceAssetsMaintain financeAssetsMaintain) throws IOException, ParseException {

        Map<String, Object> map = new HashMap<String, Object>();
        Integer org = user.getOid();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        FinanceFixedAssets financeFixedAssets = financeFixedAssetsService.getFinanceFixedAssets(org, id);
        if (financeFixedAssets != null) {
            FinanceAssetsMaintain famm = new FinanceAssetsMaintain();
            famm.setAssets(financeFixedAssets);//固定资产
            if (repairDate1 != null && !repairDate1.equals("")) {
                famm.setRepairDate(formatter.parse(repairDate1));//报修日期
            } else {
                map.put("status", 4);
                ObjectToJson.objectToJson1(map, new String[]{""}, response);
            }
            famm.setFaultReason(financeAssetsMaintain.getFaultReason());//故障原因
            famm.setNature(financeAssetsMaintain.getNature());//维修性质
            if (financeAssetsMaintain.getContent() != null) {
                famm.setContent(financeAssetsMaintain.getContent());//维修内容

            } else {
                map.put("status", 2);
                ObjectToJson.objectToJson1(map, new String[]{""}, response);

            }
            famm.setExistProblem(financeAssetsMaintain.getExistProblem());//存在问题
            famm.setWorkerName(financeAssetsMaintain.getWorkerName());//维修工姓名
            famm.setContactPhone(financeAssetsMaintain.getContactPhone());//联系方式
            if (financeAssetsMaintain.getContactCorp() != null) {
                famm.setContactCorp(financeAssetsMaintain.getContactCorp());//维修单位名称
            } else {
                map.put("status", 3);
                ObjectToJson.objectToJson1(map, new String[]{""}, response);

            }
            financeAssetsMaintainService.addFinanceAssetsMaintain(famm);
            map.put("status", 1);
            map.put("financeAssetsMaintain", famm);
            ObjectToJson.objectToJson1(map, new String[]{"assets"}, response);

        } else {
            map.put("status", 0);
            ObjectToJson.objectToJson1(map, new String[]{""}, response);
        }


    }

    /**
     * @Author:林成
     * @Date:2017/7/12 11:58
     * @description:领用归还记录、维修记录查看
     */
    @RequestMapping("/getRecordsDetail.do")
    @ResponseBody
    public void getRecordsDetail(HttpServletResponse response, User user, Integer id, Integer flag) throws IOException {

        Map<String, Object> map = new HashMap<String, Object>();
        Integer aid = id;
        if (flag == 1) {//维修记录查看

            List<FinanceAssetsMaintain> famList = financeAssetsMaintainService.getFinanceAssetsMaintainList(aid);
            map.put("financeAssetsMaintainList", famList);
            ObjectToJson.objectToJson1(map, new String[]{"assets", "updateDate", "updateName", "updator", "createDate", "createName", "creator",
                    "memo", "worker"}, response);
        }


        if (flag == 2) {//领用归还记录
            List<FinanceAssetsReceive> farList = financeAssetsReceiveService.getFinanceAssetsReceiveList(aid);
            map.put("financeAssetsMaintainList", farList);
            ObjectToJson.objectToJson1(map, new String[]{"assets", "updateDate", "updateName", "updator", "createDate", "createName", "creator"
                    , "receiver", "returner", "receiveDept"}, response);
        }


        ObjectToJson.objectToJson1(map, new String[]{""}, response);

    }

    /**
     * @Author:林成
     * @Date:2017/7/12 13:48
     * @description:获取固定资新增和归还数据
     */
    @RequestMapping("/getDataSource.do")
    @ResponseBody
    public void getDataSource(HttpServletResponse response, User user, Integer mark, Integer farId) throws IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        Integer org = user.getOid();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat formatter1 = new SimpleDateFormat("yyyy-MM-dd");
        if (mark == 1) {//新增固定资产
            List<FinanceFixedAssets> ffaList = financeFixedAssetsService.getAllSnNameList(org);//查询固定资产
            if (ffaList.size() > 0) {
                String snPrefix = ffaList.get(0).getSnPrefix();
                String sn = ffaList.get(0).getSn();
                Integer integer1 = Integer.parseInt(ffaList.get(0).getSn());
                String hb = snPrefix + sn;
                String result = "";
                Set setValue = new HashSet();
                String valueOfSn = "";
                for (FinanceFixedAssets f : ffaList) {
                    valueOfSn = f.getSnPrefix() + f.getSn().toString();
                    setValue.add(valueOfSn);
                }
                while (setValue.contains(hb)) {
                    integer1 += 1;
                    if (sn.length() != integer1.toString().length() && sn.substring(0, 1).equals("0")) {//判断是否包含0开头
                        Integer appendNum = sn.length() - integer1.toString().length();
                        for (int i = 0; i < appendNum; i++) {
                            result += "0";
                            sn = integer1.toString();
                        }

                    } else {
                        sn = integer1.toString();
                    }
                    if (result.equals("")) {
                        hb = snPrefix + sn;
                    } else {
                        hb = snPrefix + result + sn;
                    }

                }


                map.put("snPrefix", snPrefix);
                if (ffaList.get(0).getModeNumber() != null) {
                    map.put("modeNumber", ffaList.get(0).getModeNumber());
                } else {
                    map.put("modeNumber", "");
                }
                map.put("sn", result + sn);
                if (ffaList.get(0).getName() != null) {
                    map.put("name", ffaList.get(0).getName());
                } else {
                    map.put("name", "");
                }
                if (ffaList.get(0).getBuyerName() != null) {
                    map.put("buyerName", ffaList.get(0).getBuyerName());
                } else {
                    map.put("buyerName", "");
                }
                if (ffaList.get(0).getBuyDate() != null) {
                    map.put("buyDate", formatter1.format(ffaList.get(0).getBuyDate()));
                } else {
                    map.put("buyDate", "");
                }
                if (ffaList.get(0).getStorageDate() != null) {
                    map.put("storageDate", formatter1.format(ffaList.get(0).getStorageDate()));
                } else {
                    map.put("storageDate", "");
                }
                if (ffaList.get(0).getOriginalValue() != null) {
                    map.put("originalValue", ffaList.get(0).getOriginalValue());
                } else {
                    map.put("originalValue", "");
                }
                if (ffaList.get(0).getMemo() != null) {
                    map.put("memo", ffaList.get(0).getMemo());
                } else {
                    map.put("memo", "");
                }

            } else {
                map.put("snPrefix", "XH");
                map.put("sn", "0001");

                map.put("modeNumber", "");

                map.put("name", "");
                map.put("buyerName", "");
                map.put("buyDate", "");
                map.put("storageDate", "");
                map.put("originalValue", "");
                map.put("memo", "");
            }
            ObjectToJson.objectToJson1(map, new String[]{""}, response);
        }

        if (mark == 2) {//归还固定资产
            FinanceAssetsReceive far = financeAssetsReceiveService.getFinanceAssetsReceive(farId);
            if (far != null) {
                map.put("createDate", formatter.format(new Date()));
                map.put("receiverName", far.getReceiverName());
            } else {
                map.put("receiverName", "");
                map.put("createDate", formatter.format(new Date()));
            }
            ObjectToJson.objectToJson1(map, new String[]{""}, response);
        }


    }


    /**
     *@Author:林成
     *@Date:2017/7/20 13:50
     *@description:实现Comparator接口的compare方法进行中文规则排序：需要导入icu4jar包
     * 备注：中文排序使用ULocale.SIMPLIFIED_CHINESE，Locale.SIMPLIFIED_CHINESE排序不准确
     */
 /*   public class ComparatorUser1 implements Comparator {
        @Override
        public int compare(Object o1, Object o2) {
            FinanceFixedAssets info1=(FinanceFixedAssets)o1;
            FinanceFixedAssets info2=(FinanceFixedAssets)o2;
            return Collator.getInstance(ULocale.SIMPLIFIED_CHINESE).compare(info1.getName(), info2.getName());
        }
    }*/

    /**
     * Created by Administrator on 2017/7/21.
     */
    public static class ComparatorUser implements Comparator {
        @Override
        public int compare(Object o1, Object o2) {
            FinanceFixedAssets info1=(FinanceFixedAssets)o1;
            FinanceFixedAssets info2=(FinanceFixedAssets)o2;
            return Collator.getInstance(Locale.CHINESE).compare(info1.getName(), info2.getName());
        }

    }
}
