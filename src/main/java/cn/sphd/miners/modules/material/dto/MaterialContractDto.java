package cn.sphd.miners.modules.material.dto;

import cn.sphd.miners.modules.material.entity.PoContractBase;

import java.util.Date;
import java.util.List;

public class MaterialContractDto {

    private Integer id;
    private String code;
    private String name;
    private String model;
    private String specifications;
    private String unit;
    private Long contractNum;
    private Integer unitId;
    private List<PoContractBase> listPoContract;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Long getContractNum() {
        return contractNum;
    }

    public void setContractNum(Long contractNum) {
        this.contractNum = contractNum;
    }

    public List<PoContractBase> getListPoContract() {
        return listPoContract;
    }

    public void setListPoContract(List<PoContractBase> listPoContract) {
        this.listPoContract = listPoContract;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public MaterialContractDto(){}

    public MaterialContractDto(Integer id, String code, String name, String model, String specifications, String unit) {
        this.id = id;
        this.code = code;
        this.name = name;
        this.model = model;
        this.specifications = specifications;
        this.unit = unit;
    }

    public MaterialContractDto(Integer id, String code, String name, String model, String specifications, String unit, Long contractNum) {
        this.id = id;
        this.code = code;
        this.name = name;
        this.model = model;
        this.specifications = specifications;
        this.unit = unit;
        this.contractNum = contractNum;
    }

    public MaterialContractDto(Integer id, String code, String name, String model, String specifications, String unit, Long contractNum, List<PoContractBase> listPoContract) {
        this.id = id;
        this.code = code;
        this.name = name;
        this.model = model;
        this.specifications = specifications;
        this.unit = unit;
        this.contractNum = contractNum;
        this.listPoContract = listPoContract;
    }

    public MaterialContractDto(Integer id, String code, String name, String model, String specifications, String unit, Integer unitId) {
        this.id = id;
        this.code = code;
        this.name = name;
        this.model = model;
        this.specifications = specifications;
        this.unit = unit;
        this.unitId = unitId;
    }

    public MaterialContractDto(Integer id, String code, String name, String model, String specifications, String unit, Long contractNum, Integer unitId, List<PoContractBase> listPoContract) {
        this.id = id;
        this.code = code;
        this.name = name;
        this.model = model;
        this.specifications = specifications;
        this.unit = unit;
        this.contractNum = contractNum;
        this.unitId = unitId;
        this.listPoContract = listPoContract;
    }
}
