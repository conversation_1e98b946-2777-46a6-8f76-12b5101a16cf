package cn.sphd.miners.modules.forumArea.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Date 2020-12-14 
 */

@Entity ( name ="ResourceForum" )
@Table ( name ="t_resource_forum" )
public class ResourceForum  implements Serializable {

	@Id
	@Column
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	@Column(name = "org" )
	private Integer org;

	/**
	 * 资源ID
	 */
	@Column(name = "resource" )
	private Integer resource;

	/**
	 * 类型:1-小归档,2-大归档
	 */
	@Column(name = "type" )
	private Integer type;

	/**
	 * 归档编号
	 */
	@Column(name = "archive_no" )
	private String archiveNo;

	/**
	 * 论坛主题ID
	 */
	@Column(name = "post" )
	private Integer post;

	/**
	 * 论坛回帖/回复ID
	 */
	@Column(name = "reply" )
	private Integer reply;

	/**
	 * 备注
	 */
	@Column(name = "memo" )
	private String memo;

	/**
	 * 创建人id
	 */
	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
	@Column(name = "create_date" )
	private Date createDate;

	/**
	 * 修改人id
	 */
	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
	@Column(name = "update_date" )
	private Date updateDate;

	/**
	 * 对应消息表的id
	 */
	@Column(name = "message_id" )
	private Integer messageId;

	/**
	 * 操作:1-增,2-删,3-改
	 */
	@Column(name = "operation" )
	private String operation;

	/**
	 * 修改前记录ID
	 */
	@Column(name = "previous_id" )
	private Integer previousId;

	/**
	 * 版本号,每次修改+1
	 */
	@Column(name = "version_no" )
	private Integer versionNo;

	@Transient
	private ForumPost forumPost;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOrg() {
		return org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getResource() {
		return resource;
	}

	public void setResource(Integer resource) {
		this.resource = resource;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getArchiveNo() {
		return archiveNo;
	}

	public void setArchiveNo(String archiveNo) {
		this.archiveNo = archiveNo;
	}

	public Integer getPost() {
		return post;
	}

	public void setPost(Integer post) {
		this.post = post;
	}

	public Integer getReply() {
		return reply;
	}

	public void setReply(Integer reply) {
		this.reply = reply;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getCreator() {
		return creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getUpdator() {
		return updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Integer getMessageId() {
		return messageId;
	}

	public void setMessageId(Integer messageId) {
		this.messageId = messageId;
	}

	public String getOperation() {
		return operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public Integer getPreviousId() {
		return previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}

	public ForumPost getForumPost() {
		return forumPost;
	}

	public void setForumPost(ForumPost forumPost) {
		this.forumPost = forumPost;
	}
}
