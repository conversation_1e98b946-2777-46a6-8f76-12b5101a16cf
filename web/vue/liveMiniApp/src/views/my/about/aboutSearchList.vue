<template>
  <div class="usingHelp">
    <Pageheader title="使用帮助" :showBack="true"/>
    <div class="live-contain">
      <el-alert :title="resultCount" type="info" show-icon :closable="false"/>
      <div class="live-cells mainCon">
        <div class="live-cell" v-for="(item, index) in searchList" :key="item.id" @click="jumpDetail(item)">
          <el-icon><Document /></el-icon>
          <span class="fileName">{{item.name}}</span>
          <el-icon class="arrow-right"><ArrowRight /></el-icon>
        </div>
      </div>
      <el-pagination
          background
          layout="prev, pager, next"
          :page-size="pageSize"
          :page-count="pageInfo.totalPage"
          class="mt-4"
          @current-change="handleCurrentChange"
          hide-on-single-page
      />
    </div>
  </div>
</template>

<script setup>
import { getAboutSearchList } from "@/api/api.js";
import { reactive, ref, onMounted, watch } from "vue";
import { useRouter, useRoute } from "vue-router";

let searchList = ref([])
let pageInfo = ref({totalPage: 0})
let resultCount = ref('')
const pageSize = 10

const router = useRouter();
const route = useRoute();
console.log('params', route.params)


onMounted(() => {
  getNextModuleList(1)
})

const getNextModuleList = (currentPageNo) => {
  let searchInput = route.params.name
  let data = {
    name: searchInput,
    currentPageNo: currentPageNo,
    pageSize: pageSize,
    type: 2
  }
  getAboutSearchList(data).then(res => {
    let list = res.data.list
    let page = res.data.pageInfo
    let count = page.totalResult
    pageInfo.value = page
    searchList.value = list
    resultCount.value = `共有 ${count} 个文件符合查询结果`
  })
}

const handleCurrentChange = (val) => {
  getNextModuleList(val)
}

const jumpDetail = (item) => {
  router.push({ path: '/aboutFinalDetail/usingHelp/' + item.id });
}

</script>

<style lang="scss" scoped>
.fileName{
  margin-left: 8px;
}
.mt-4{
  padding: 8px;
  justify-content: right;
  background: #fff;
}
.mainCon{
  min-height: calc(100% - 56px);
}
.live-contain{
  height: calc(100vh - 44px);
  overflow-y: auto;
}
</style>



