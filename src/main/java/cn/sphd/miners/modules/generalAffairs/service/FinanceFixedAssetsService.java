package cn.sphd.miners.modules.generalAffairs.service;

import cn.sphd.miners.modules.generalAffairs.entity.FinanceFixedAssets;

import java.util.List;

/**
 * Created by Administrator on 2017/7/11.
 */
public interface FinanceFixedAssetsService {

    void addFinanceFixedAssets(FinanceFixedAssets financeFixedAssets);

    List<FinanceFixedAssets> getAllSnNameList(Integer org);

    List<FinanceFixedAssets> getFinanceFixedAssetsByOrg(Integer org, String useState);//默认排序

    List<FinanceFixedAssets> getFinanceFixedAssetsByOrg1(Integer org, String useState);//固定资产名称倒序

    List<FinanceFixedAssets> getFinanceFixedAssetsByOrg2(Integer org, String useState);//固定资产名称顺序

    List<FinanceFixedAssets> getFinanceFixedAssetsByOrg3(Integer org, String useState);//按照入库日期倒序

    List<FinanceFixedAssets> getFinanceFixedAssetsByOrg4(Integer org, String useState);//按照入库日期顺序

    List<FinanceFixedAssets> getFinanceFixedAssetsReceByOrg(Integer org, String receiveState);

    List<FinanceFixedAssets> findAll(Integer org);

    FinanceFixedAssets getFinanceFixedAssets(Integer org, Integer id);

    void updateFinanceFixedAssets(FinanceFixedAssets financeFixedAssets);

    List<FinanceFixedAssets> getSnNameList(Integer org, String useState, String bhxh);//


}
