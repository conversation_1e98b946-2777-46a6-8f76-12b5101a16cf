package cn.sphd.miners.modules.resourceAuthority.service.impl;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.common.utils.TimeUtils;
import cn.sphd.miners.modules.dailyAffairs.entity.UserSuspendMsg;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.forumArea.dto.PostUserMussage;
import cn.sphd.miners.modules.forumArea.service.ForumService;
import cn.sphd.miners.modules.personal.dao.ApprovalProcessDao;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.resourceAuthority.dto.ChunkResultDto;
import cn.sphd.miners.modules.resourceAuthority.dto.ResSignedMes;
import cn.sphd.miners.modules.resourceAuthority.entity.*;
import cn.sphd.miners.modules.resourceAuthority.mapper.*;
import cn.sphd.miners.modules.resourceAuthority.service.*;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.dto.OrganizationDto;
import cn.sphd.miners.modules.system.dto.UserDto;
import cn.sphd.miners.modules.system.dto.UserHonePageDto;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgService;
import cn.sphd.miners.modules.system.service.UserService;
import cn.sphd.miners.modules.uploads.service.UploadService;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by 朱思旭 on 2017/11/15.
 */
@Service("resService")
@Transactional(readOnly=false)
public class ResServiceImpl implements ResService {
    @Autowired
    ResMapper resMapper;
    @Autowired
    ResCategoryMapper resCategoryMapper;
    @Autowired
    ResAclMapper resAclMapper;
    @Autowired
    ResHistoryMapper resHistoryMapper;
    @Autowired
    ResCategoryAclMapper resCategoryAclMapper;
    @Autowired
    ResVisMapper resVisMapper;
    @Autowired
    ReadingRoomMapper readingRoomMapper;


    @Autowired
    UserService userService;
    @Autowired
    OrgService orgService;
    @Autowired
    ApprovalProcessDao approvalProcessDao;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    SWMessageService swMessageService;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    ResCategoryService resCategoryService;
    @Autowired
    UploadService uploadService;
    @Autowired
    ReadRoomService readRoomService;
    @Autowired
    ForumService forumService;
    @Autowired
    ResCorrelationService resCorrelationService;
    @Autowired
    ResNoticeService resNoticeService;

    private final String module = "文件与资料";

    public String getModule() {
        return module;
    }

    @Override
    public List<ResEntity> judgementFileByCategoryId(Integer categoryId) {
        List<ResEntity> listFile = resMapper.getFileBycategory(categoryId);
        return listFile;
    }

    @Override
    public QueryData judgementFileByCategoryIdCentre(User user,Integer categoryId) {
        QueryData query = new QueryData();
        String state = "0";     //0的时候是此文件夹不存在，或者不是本机构的
        ResCategory rc = new ResCategory();
        rc.setId(categoryId);
        ResCategory categoryMessage = resCategoryMapper.getSingle(rc);
        if (categoryMessage.getValid()) {
            Integer authCount = resCategoryAclMapper.countAuth(categoryId);
            if (authCount > 0) {
                query.put("authCount", authCount);
                String stateFile = "1";
                String stateCat = "1";
                if(categoryMessage != null && categoryMessage.getOrg()!=null && categoryMessage.getOrg().equals(user.getOid())&& categoryMessage.getValid()==true) {
                    QueryData param = new QueryData();
                    PageInfo pageInfo = new PageInfo();
                    pageInfo.setPageSize(1);
                    param.put("pageInfo", pageInfo);
                    param.put("parent",rc.getId());
                    ResEntity listFile = resMapper.getOneFileByCategorylistPage(param);
                    if(listFile == null){
                        List<ResHistory> listHisFile = resHistoryMapper.getHIsFileByCategoryidAndApproveStatus(categoryId);
                        if(listHisFile.isEmpty()){
                            stateFile = "0";
                        }
                    }
                    Integer childrenNum = resCategoryMapper.getAllChildCategoryNumByParent(categoryId);
                    if(childrenNum==0){
                        stateCat = "0";
                    }
                    if ("0".equals(stateFile) && "0".equals(stateCat)) {
                        state = "1";            //既没有文件夹也没有文件，空的
                    } else {
                        state = "2";
                    }
                }
            } else {
                state = "3";
            }
        }
        query.put("state", state);
        return query;

    }

    @Override
    public QueryData getFile(User user, Integer categoryId, PageInfo pageInfo, String type, String teminateState) {
        ResEntity r = new ResEntity();
        r.setCategory(categoryId);
        r.setUser(user.getUserID());
        QueryData query = new QueryData();
        query.put("pageInfo", pageInfo);
        query.put("category", categoryId);
        query.put("user", user.getUserID());
        if ("5".equals(type)) {
            query.put("type", "2");
        } else {
            query.put("type", type);
        }
        if (teminateState == null) {
            query.put("teminateState", "0");
        }
        List<ResEntity> listPageFile = resMapper.filelistPage(query);
        QueryData queryData = new QueryData();
        queryData.put("pageInfo", pageInfo);
        queryData.put("list", listPageFile);
        return  queryData;
    }

    @Override
    public QueryData getFileByManager(User user, Integer categoryId, PageInfo pageInfo, String type, String teminateState) {
        QueryData query = new QueryData();
        query.put("pageInfo", pageInfo);
        query.put("category", categoryId);
        query.put("type", type);
        if (teminateState == null) {
            query.put("teminateState", "0");
        }
        List<ResEntity> listPageFile = resMapper.fileByManagerlistPage(query);
        QueryData queryData = new QueryData();
        queryData.put("pageInfo", pageInfo);
        queryData.put("list", listPageFile);
        return queryData;
    }

    /*
     * 第一版查询的是：不是总务和小总务上传的文件
     * 第二版查询的是：type为1时申请发布的需要审批的文件，此时是包括总务小总务的
     */
    @Override
    public QueryData getPublishFileByApproveStatus(User user, PageInfo pageInfo, String approveStatus) {
        ArrayList list = getAllCategoryID(user);
        QueryData query = new QueryData();
        query.put("list", list);
        query.put("approveStatus", approveStatus);
        query.put("pageInfo", pageInfo);
        List<ResHistory> listPageRes = resHistoryMapper.getFileByPublishlistPage(query);

        //          query.put("creator", user.getUserID());
        //         listPageRes = resHistoryMapper.getFileByPublishAndUserlistPage(query);
        QueryData queryData = new QueryData();
        queryData.put("pageInfo", JSON.toJSON(pageInfo));
        queryData.put("list", listPageRes);
        return queryData;
    }

    @Override
    public List<ResHistory> getApplyPublichFile(User user) {
        List<ResHistory> listHis = resHistoryMapper.getPublishFileByUser(user.getUserID());
        return listHis;
    }

    @Override
    public String insertReourceCentreFile(User user, String files, Integer category, String content, Integer changeNum,
                                          String type, Integer auditor, String auditName, String noticeType) {
        List<ResEntity> listAtt = JSON.parseArray(files, ResEntity.class);
        String status = "2";  //先把状态设为文件编号重复
        Integer resCheckFileSn = 0;
        for (ResEntity checkRes : listAtt) {
            Integer i = 0;
            for (ResEntity checkAllRes : listAtt) {
                if (checkRes.getFileSn().equals(checkAllRes.getFileSn())) {
                    i++;
                    if (i>1) {
                        resCheckFileSn = 1;
                        break;
                    }
                }
            }
            //调共有方法看这个编号是否被占用
            if (resCheckFileSn.equals(0)) {
                resCheckFileSn = this.checkFileSn(user.getOid(), checkRes.getFileSn());
            }
            if (resCheckFileSn.equals(0)) {
                status = "3";
            }else {
                status = "2";
                break;
            }
        }
        if ("2".equals(status)) {
            return status;
        } else {
            String batchUuid = null;
            if ("1".equals(noticeType)) {
                String uu = UUID.randomUUID().toString();
                batchUuid = uu.replaceAll("-", "");
            }
            List<Integer> listUserIDs = resCategoryAclMapper.getAllUserIdsByCategory(category);
            resNoticeService.addSpecialAuthUserId(listUserIDs, user);
            for (ResEntity res : listAtt) {
                QueryData query = this.judgementFileByCategoryIdCentre(user,category);
                String state = (String) query.get("state");
                if(!("0".equals(state))){
                    status = "1";
                    res.setCategory(category);
                    if(content != ""){
                        res.setContent(content); //此值若是不填时要传过来空
                    }
                    res.setChangeNum(changeNum);
                    if(type.equals("1")){
                        if (auditor != null) {
                            res.setAuditor(auditor);
                            res.setAuditName(auditName);
                        }
                        res.setApproveStatus("1");
                    } else {
                        res.setApproveStatus("2");
                    }
                    this.insertPublicMethod(user,res,type,module,batchUuid,listUserIDs);
                    if ("2".equals(type)) {
                        //给文件夹推送新增文件
                        this.sendFile(res.getId(), user, "1", "1", null,"1",null);
                    }
                }
            }
            if ("2".equals(type) && "1".equals(noticeType)) {
                resNoticeService.sendResFileNotice(user, null, null, null,null,batchUuid, Long.valueOf(listAtt.size()), null, noticeType, listUserIDs,null,null);
            }
            return status;
        }
    }

    @Override
    public JsonResult insertReourceCentreFolder(User user, Integer parent, List<ChunkResultDto> fileResultList, String module) {
        ResCategory rc = new ResCategory();
        rc.setId(parent);
        ResCategory categoryMessage = resCategoryMapper.getSingle(rc);
        String state = "0";
        if (categoryMessage!=null) {
            Integer authCount = resCategoryAclMapper.countAuth(parent);
            if (authCount > 0) {
                state = "1";
            } else {
                state = "2";
            }
        }
        switch(state) {//父目录异常处理
            case "0"://0的时候是此文件夹不存在
                return new JsonResult(new MyException("1","保存位置不存在"));
//                break;
            case "2"://只有文件夹
                return new JsonResult(new MyException("2","文件夹没有权限，不能上传"));
//                break;
//            case "3": //只有文件
//            case "1": //既没有文件夹也没有文件，空的
//            default:
        }
        //缓存数组
        HashMap<String, ResCategory> cache = new HashMap<>();
        ResCategory path;
        Integer catCount=0,entCount=0;
        String uu = UUID.randomUUID().toString();
        String batchUuid = uu.replaceAll("-", "");
        List<Integer> listUserIDs = resCategoryAclMapper.getAllUserIdsByCategory(parent);
        for(ChunkResultDto filedto: fileResultList) {
            String relativePath = filedto.getRelativePath();
            System.out.println("filedto: "+JSON.toJSONString(filedto));
            //结尾的"/"处理，kendoui传上来的relativePath以"/"结尾，如果没有就加上。
            relativePath = relativePath.endsWith("/") ? relativePath : relativePath + "/";
            System.out.println("relativePath: "+relativePath);
            if ((path = cache.get(relativePath)) == null) {
                //新增目录
                InnerResultAddAndGetParents addResult= addAndGetParents(user, parent, filedto.getRelativePath(), cache, listUserIDs);
                path = addResult.category;
                catCount+=addResult.addedCount;
            }
            ResEntity resEntity = new ResEntity();
            resEntity.setCategory(path.getId());
            String name = filedto.getOriginalFilename().substring(0,filedto.getOriginalFilename().lastIndexOf("."));
            resEntity.setName(name);
            String filsn = this.checkFileNum(user.getOid(), name, null);
            resEntity.setFileSn(filsn);
            resEntity.setPath(filedto.getFilename());
            resEntity.setSize(filedto.getSize());
            resEntity.setVersion(filedto.getLastName());
            resEntity.setChangeNum(0);
            resEntity.setApproveStatus("2");
            this.insertPublicMethod(user, resEntity, "2",module,batchUuid,listUserIDs);
            entCount++;
        }
        List<Integer> listCategoryIds = new ArrayList<>();
        for (String ResCategory : cache.keySet()) {
            Integer categoryId = cache.get(ResCategory).getId();
            listCategoryIds.add(categoryId);
        }
        Collections.sort(listCategoryIds);
        ResCategory cat = new ResCategory();
        cat.setId(listCategoryIds.get(0));
        ResCategory category = resCategoryMapper.getSingle(cat);
        List<Integer> listUserIDsForNotice = new ArrayList<>();
        listUserIDsForNotice.addAll(listUserIDs);
        this.sendCategory(category, listUserIDs, user, "1","1");  //推送新增的文件夹
        resNoticeService.addSpecialAuthUserId(listUserIDsForNotice, user);
        resNoticeService.sendResFileNotice(user, null, null, null,null,batchUuid, new Long(entCount), null, "1", listUserIDsForNotice,null,null);
        return new JsonResult(1, "保存成功，共上传"+entCount+"个文件！新建"+catCount+"个文件夹");
    }
    final class InnerResultAddAndGetParents{
        final ResCategory category;
        final Integer addedCount;
        public InnerResultAddAndGetParents(ResCategory category, Integer addedCount) {
            this.category = category;
            this.addedCount = addedCount;
        }
    }
    //循环新增文件夹,新增的文件夹数量
    private InnerResultAddAndGetParents addAndGetParents(User user, Integer parent, String paths, HashMap<String, ResCategory> cache, List<Integer> userIDs) {
        Integer count=0;
        StringBuffer path = new StringBuffer();
        //去掉最后的"/"并用"/"切分。
        System.out.println("addAndGetParents: "+paths);
        String[] categoryList = paths.substring(0,paths.length()-1).split("/");
        System.out.println(JSON.toJSONString(categoryList));
        ResCategory category=null;
        for(String categoryname : categoryList){
            path.append(categoryname).append("/");
            category = resCategoryService.checkFolder(user, parent, categoryname, "1");
            if(category == null){
                category = resCategoryService.addSameCategoryByResource(user,parent,categoryname,"1",userIDs);
                count++;
                parent = category.getId();
            } else {
                parent = category.getId();
            }
            //添加到缓存数组。
            cache.put(path.toString(),category);
        }
        InnerResultAddAndGetParents result = new InnerResultAddAndGetParents(category,count);
        return result;
    }


    //新增文件和文件夹时公用的方法
    private void insertPublicMethod(User user, ResEntity resEntity, String type, String module, String batchUuid,List<Integer> listUserIDs){
        resEntity.setCreator(user.getUserID());
        resEntity.setCreateName(user.getUserName());
        resEntity.setCreateDate(NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"));
        resEntity.setAuditDate(resEntity.getCreateDate());
        resEntity.setOrg(user.getOid());
        resEntity.setValid(1);
        resEntity.setViewNum(0);
        resEntity.setDownloadNum(0);
        resEntity.setMoveNum(0);
        //新增版本的历史
        ResCategory rc = new ResCategory();
        rc.setId(resEntity.getCategory());
        ResCategory category = resCategoryMapper.getSingle(rc);
        ResHistory rhVersion = new ResHistory();
        rhVersion.setCategory(resEntity.getCategory());
        rhVersion.setCategoryPath(category.getPath());
        rhVersion.setEnabled(true);
        rhVersion.setIsDeleted(false);
        rhVersion.setValid(1);
        if (Byte.valueOf("1").equals(category.getIsTrash())) {
            rhVersion.setIsTrash(Byte.valueOf("1"));
            resEntity.setIsTrash(Byte.valueOf("1"));
        }else {
            rhVersion.setIsTrash(Byte.valueOf("0"));
            resEntity.setIsTrash(Byte.valueOf("0"));
        }
        rhVersion.setName(resEntity.getName());
        rhVersion.setContent(resEntity.getContent());
        rhVersion.setFileSn(resEntity.getFileSn());
        rhVersion.setPath(resEntity.getPath());
        rhVersion.setSize(resEntity.getSize());
        rhVersion.setVersion(resEntity.getVersion());
        rhVersion.setChangeNum(resEntity.getChangeNum());
        rhVersion.setApproveStatus(resEntity.getApproveStatus());
        rhVersion.setCreator(resEntity.getCreator());
        rhVersion.setCreateName(resEntity.getCreateName());
        rhVersion.setCreateDate(resEntity.getCreateDate());
        rhVersion.setAuditDate(resEntity.getAuditDate());
        rhVersion.setOperation("4");                  //为4时代表是代表这是一个版本
        rhVersion.setType(type);
        rhVersion.setOrg(user.getOid());
        rhVersion.setApproveStatus(resEntity.getApproveStatus());
        rhVersion.setTeminateState("0");
        if (batchUuid != null) {
            rhVersion.setBatchUuid(batchUuid);
        }
        resHistoryMapper.insert(rhVersion);
        ResUsing callback = new ResUsing(rhVersion.getId(), rhVersion.getClass());
        uploadService.addFileUsing(callback, resEntity.getPath(), resEntity.getName(), user, module);
        if (type.equals("2")) {
            String allCategoryName = this.allParentCategoryName(category.getParent(), category.getName(), null, "1");
            //总务上传时，新增文件以及文件权限
            resEntity.setCategoryPath(category.getPath());
            int stateFile = resMapper.insert(resEntity);
            //写入回调
            callback = new ResUsing(resEntity.getId(), resEntity.getClass());
            uploadService.addFileUsing(callback, resEntity.getPath(), resEntity.getName(), user, module);
            int stateFileAuth = resAclMapper.insertAuthByCategoryAuth(resEntity);
            //新增移动的历史
            int insertFileHisMove = addHisMoveFile(resEntity.getId(), resEntity.getCategory(), allCategoryName, resEntity.getVersion(),category.getPath());
            //新增名字的历史
            int insertFileHisName = addHisUpNameFile(resEntity.getId(), resEntity.getCategory(), resEntity.getName(), resEntity.getVersion());
            //新增编号的历史
            int insertFileHisFileSn = addHisUpFileSn(resEntity.getId(), resEntity.getCategory(), resEntity.getFileSn(), resEntity.getVersion());
            ResHistory rhVersionNew = new ResHistory();
            rhVersionNew.setFile(resEntity.getId());
            rhVersionNew.setId(rhVersion.getId());
            resHistoryMapper.update(rhVersionNew);
            if (batchUuid == null) {
                resNoticeService.sendResFileNotice(user, null, null, null, rhVersionNew.getId(),null, null, null,"2",listUserIDs,null,null);
            }
        } else {
            ResHistory resHis = resHistoryMapper.getSingle(rhVersion);
            HashMap<String,Object> resHisMap =new HashMap<>();
            resHisMap.put("resHis", resHis);
            //新增审批流程
            if (resEntity.getAuditor() != null) {
                this.addApprovalProcessForResource(rhVersion.getId(), user, resEntity.getAuditor(), 1, resEntity.getAuditName(), resEntity.getCreateDate(),"",14);
                User userAuditor = userService.getUserByID(resEntity.getAuditor());
                swMessageService.rejectSend(1,1,resHisMap,resEntity.getAuditor().toString(),"/approveFile", resHis.getCreateName()+"在"+resHis.getCreateDate()+"提交了文件发布申请", resHis.getCreateName()+"在"+resHis.getCreateDate()+"提交了文件发布申请",userAuditor,"fileApproval");   //给审批人“文件审批”加一
            } else {
                this.addApprovalProcessForResource(rhVersion.getId(), user, 0, 1, "", resEntity.getCreateDate(),"rb",14);
                swMessageService.rejectSendToMidManageCode(1,1,resHisMap,user.getOid(),"rb","/approveFileByFinal","versionApproval", resHis.getCreateName()+"在"+resHis.getCreateDate()+"提交了文件发布申请", resHis.getCreateName()+"在"+resHis.getCreateDate()+"提交了文件发布申请", "general");  //发给终审人员
            }
            swMessageService.rejectSend(0,1,resHisMap,user.getUserID().toString(),"/applyPublishFile",null,null,user,"releaseApply");   //给申请人“文件发布申请”加一
        }
    }

    @Override
    public ResEntity insertResManageFile(User user, ResEntity resEntity, String module) {
        resEntity.setCreator(user.getUserID());
        resEntity.setCreateName(user.getUserName());
        resEntity.setCreateDate(NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"));
        resEntity.setAuditDate(resEntity.getCreateDate());
        int stateFile = resMapper.insert(resEntity);
        ResUsing callback = new ResUsing(resEntity.getId(), resEntity.getClass());
        uploadService.addFileUsing(callback, resEntity.getPath(), resEntity.getName(), user, module);
        ResAcl r = new ResAcl();
        r.setUser(user.getUserID());
        r.setResource(resEntity.getId());
        int statAuth = resAclMapper.insert(r);
        ResEntity res = resMapper.getSingle(resEntity);
        ResHistory rh = new ResHistory();
        rh.setFile(resEntity.getId());
        rh.setCategory(resEntity.getCategory());
        rh.setName(resEntity.getName());
        rh.setContent(resEntity.getContent());
        rh.setFileSn(resEntity.getFileSn());
        rh.setPath(resEntity.getPath());
        rh.setVersion(resEntity.getVersion());
        rh.setSize(resEntity.getSize());
        rh.setChangeNum(resEntity.getChangeNum());
        rh.setOpertatorName(resEntity.getOpertatorName());
        rh.setOperateDate(resEntity.getOperateDate());
        rh.setVerifierName(resEntity.getVerifierName());
        rh.setVerifyDate(resEntity.getVerifyDate());
        rh.setApproverName(resEntity.getApproverName());
        rh.setApproveDate(resEntity.getApproveDate());
        rh.setApproveStatus(resEntity.getApproveStatus());
        rh.setCreator(resEntity.getCreator());
        rh.setCreateName(resEntity.getCreateName());
        rh.setCreateDate(resEntity.getCreateDate());
        rh.setAuditDate(resEntity.getAuditDate());
        rh.setOperation("4");
        int insetFileHistory = resHistoryMapper.insert(rh);
        callback = new ResUsing(rh.getId(), rh.getClass());
        uploadService.addFileUsing(callback, rh.getPath(), rh.getName(), user, module);
        return res;
    }

    @Override
    public QueryData getFileMessage(Integer id, User user) {
        ResHistory r = new ResHistory();
        r.setId(id);
        ResHistory res = resHistoryMapper.getSingle(r);
        ResCategory cat = this.getCategory(res.getCategory());
        String categoryName = this.allParentCategoryName(cat.getParent(), cat.getName(), null, "1");     //获取文件类别
        List<User> list = new ArrayList<User>(2);
        User userManager = userService.getUserByRoleCode(user.getOid(), "general");
        list.add(userManager);
        Integer minorAffairsId = userService.getSmallManageByMCode(user.getOid(),"general");
        if (minorAffairsId != 0) {
            User userAfffairsId = new User();
            userAfffairsId.setUserID(minorAffairsId);
            list.add(userAfffairsId);
        }
        QueryData query = new QueryData();
        query.put("resource", res);
        query.put("categoryName", categoryName);
        query.put("listUser", list);
        return query;
    }

    @Override
    public HashMap oneFileMessage(Integer hisId) {
        ResHistory rh = new ResHistory();
        rh.setId(hisId);
        ResHistory resHis = resHistoryMapper.getSingle(rh);
        if (resHis.getContent() == null) {
            resHis.setContent("");
        }
        ResCategory cat = this.getCategory(resHis.getCategory());
        String categoryName = this.allParentCategoryName(cat.getParent(), cat.getName(), null, "1");     //获取文件类别
        List<ApprovalProcess> listAp = approvalProcessService.getApprovalProcessByBusiness(hisId, 14, "");
        HashMap<String,Object> map = new HashMap<>();
        if (resHis.getChangeNum() > 0) {
            ResEntity r = new ResEntity();
            r.setId(resHis.getFile());
            ResEntity res = resMapper.getSingle(r);
            map.put("validFilePath", res.getPath());
        }
        map.put("resHis", resHis);
        map.put("categoryName", categoryName);
        map.put("listAp", listAp);
        return map;
    }

    @Override
    public QueryData affirdUpdateVersion(User user, ResEntity resEntity, String module) {
        ResEntity resOld = resMapper.getSingle(resEntity);
        ResUsing callback = new ResUsing(resOld.getId(), resOld.getClass());
        uploadService.delFileUsing(callback,resOld.getPath(),user);
        addNewHisFile(resOld);
        resEntity.setChangeNum(resOld.getChangeNum()+1);
        resEntity.setUpdator(user.getUserID());
        resEntity.setUpdateName(user.getUserName());
        resEntity.setUpdateDate(NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"));
        resEntity.setAuditDate(resEntity.getUpdateDate());
        int updateFileState = resMapper.update(resEntity);
        int state = 0;
        ResEntity r = resMapper.getSingle(resEntity);
        callback = new ResUsing(r.getId(),r.getClass());
        uploadService.addFileUsing(callback,r.getPath(),r.getName(),user,module);
        if (updateFileState > 0) {
            ResHistory rh = new ResHistory();
            rh.setFile(r.getId());
            rh.setCategory(r.getCategory());
            rh.setName(r.getName());
            rh.setContent(r.getContent());
            rh.setFileSn(r.getFileSn());
            rh.setPath(r.getPath());
            rh.setSize(r.getSize());
            rh.setVersion(r.getVersion());
            rh.setChangeNum(r.getChangeNum());
            rh.setReason(r.getReason());
            rh.setOpertatorName(r.getOpertatorName());
            rh.setOperateDate(r.getOperateDate());
            rh.setVerifierName(r.getVerifierName());
            rh.setVerifyDate(r.getVerifyDate());
            rh.setApproverName(r.getApproverName());
            rh.setApproveDate(r.getApproveDate());
            rh.setApproveStatus(r.getApproveStatus());
            rh.setCreator(r.getCreator());
            rh.setCreateName(r.getCreateName());
            rh.setCreateDate(r.getCreateDate());
            rh.setAuditDate(r.getAuditDate());
            rh.setUpdator(r.getUpdator());
            rh.setUpdateName(r.getUpdateName());
            rh.setUpdateDate(r.getUpdateDate());
            rh.setOperation("4");
            int insetFileHistory = resHistoryMapper.insert(rh);
            callback = new ResUsing(rh.getId(),rh.getClass());
            uploadService.addFileUsing(callback,rh.getPath(),rh.getName(),user,module);
            state = 1;
        }
        QueryData query = new QueryData();
        query.put("state", state);
        query.put("query", r);
        return query;
    }

    //判断最新历史文件和当前文件是否是一样的，若是不一样就得加一条最新历史文件
    private void addNewHisFile(ResEntity resOld){
        ResHistory resHisOld = resHistoryMapper.getNewFileHistory(resOld.getId());
        if(!(resOld.getChangeNum().equals(resHisOld.getChangeNum()))){
            ResHistory rhOld = new ResHistory();
            rhOld.setFile(resOld.getId());
            rhOld.setCategory(resOld.getCategory());
            rhOld.setCategoryPath(resHisOld.getCategoryPath());
            rhOld.setName(resOld.getName());
            rhOld.setContent(resOld.getContent());
            rhOld.setFileSn(resOld.getFileSn());
            rhOld.setPath(resOld.getPath());
            rhOld.setSize(resOld.getSize());
            rhOld.setVersion(resOld.getVersion());
            rhOld.setChangeNum(resOld.getChangeNum());
            rhOld.setReason(resOld.getReason());
            rhOld.setOpertatorName(resOld.getOpertatorName());
            rhOld.setOperateDate(resOld.getOperateDate());
            rhOld.setVerifierName(resOld.getVerifierName());
            rhOld.setVerifyDate(resOld.getVerifyDate());
            rhOld.setApproverName(resOld.getApproverName());
            rhOld.setApproveDate(resOld.getApproveDate());
            rhOld.setApproveStatus(resOld.getApproveStatus());
            rhOld.setCreator(resOld.getCreator());
            rhOld.setCreateName(resOld.getCreateName());
            rhOld.setCreateDate(resOld.getCreateDate());
            rhOld.setAuditDate(resOld.getAuditDate());
            rhOld.setUpdator(resOld.getUpdator());
            rhOld.setUpdateName(resOld.getUpdateName());
            rhOld.setUpdateDate(resOld.getUpdateDate());
            int insetFileHistory = resHistoryMapper.insert(rhOld);
        }
    }

    @Override
    public void affridVersionForApprove(User user, Integer file, String content,String path,
                                        String size, String version, Integer category, String name, String fileSn,
                                        String type, Integer approveId, String approveName, String module) {
        //  System.out.println("affridVersionForApprove 资源中心进入换版方法……");
        ResEntity res = this.getSingle(file);
        this.addNewHisFile(res);
        Date date = new Date();
        ResHistory resHistory = new ResHistory();
        resHistory.setFile(file);
        resHistory.setCategory(category);
        resHistory.setCategoryPath(res.getCategoryPath());
        resHistory.setType(type);
        resHistory.setEnabled(true);
        resHistory.setIsTrash(res.getIsTrash());
        resHistory.setIsDeleted(false);
        resHistory.setValid(1);
        resHistory.setCreator(res.getCreator());
        resHistory.setCreateName(res.getCreateName());
        resHistory.setCreateDate(res.getCreateDate());
        resHistory.setUpdator(user.getUserID());
        resHistory.setUpdateName(user.getUserName());
        resHistory.setUpdateDate(NewDateUtils.dateToString(date,"yyyy-MM-dd HH:mm:ss"));
        resHistory.setAuditDate(resHistory.getUpdateDate());
        resHistory.setOperation("4");
        resHistory.setOrg(user.getOid());
        if (path != null && size != null && version != null && name != null && fileSn != null) {
            resHistory.setContent(content);
            resHistory.setChangeNum(res.getChangeNum() + 1);
            resHistory.setPath(path);
            resHistory.setSize(Long.valueOf(size));
            resHistory.setVersion(version);
            resHistory.setName(name);
            resHistory.setFileSn(fileSn);
            resHistory.setTeminateState("0");
            //类型 1是需要审批 2是不用审批
            resHistory.setType(type);
            if(type.equals("1")){
                resHistory.setApproveStatus("1");
            } else {
                resHistory.setApproveStatus("2");
            }
        } else {
            resHistory.setChangeNum(res.getChangeNum());
            resHistory.setContent(res.getContent());
            resHistory.setPath(res.getPath());
            resHistory.setSize(res.getSize());
            resHistory.setVersion(res.getVersion());
            resHistory.setName(res.getName());
            resHistory.setFileSn(res.getFileSn());
            resHistory.setTeminateState("1");
            resHistory.setTeminater(user.getUserID());
            resHistory.setTeminaterName(user.getUserName());
            resHistory.setTeminateTime(date);
            //类型 1是需要审批 2是不用审批
            resHistory.setType(type);
            if("1".equals(type)){
                resHistory.setApproveStatus("1");
            }else {
                resHistory.setApproveStatus("2");
            }
        }
        int state = resHistoryMapper.insert(resHistory);
        ResUsing callback = new ResUsing(resHistory.getId(), resHistory.getClass());
        uploadService.addFileUsing(callback, resHistory.getPath(), resHistory.getName(), user, module);
        if("1".equals(type)){
            HashMap<String,Object> resHisMap =new HashMap<>();
            resHisMap.put("resHis", resHistory);
            //存入审批流程
            if (approveId == null) {
                this.addApprovalProcessForResource(resHistory.getId(), user,  approveId, 1, approveName, resHistory.getUpdateDate(),"rb",14);
                String title = null;
                if ("1".equals(resHistory.getTeminateState())) {
                    title = resHistory.getUpdateName()+"在"+resHistory.getUpdateDate()+"提交了文件废止申请";
                }else {
                    title = resHistory.getUpdateName()+"在"+resHistory.getUpdateDate()+"提交了文件换版申请";
                }
                swMessageService.rejectSendToMidManageCode(1,1,resHisMap,user.getOid(),"rb","/approveFileByFinal","versionApproval", title, title, "general");  //发给终审人员
            } else {
                this.addApprovalProcessForResource(resHistory.getId(), user,  approveId, 1, approveName, resHistory.getUpdateDate(),"",14);
                User userApprove = userService.getUserByID(approveId);
                String title = null;
                if ("1".equals(resHistory.getTeminateState())) {
                    title = resHistory.getUpdateName()+"在"+resHistory.getUpdateDate()+"提交了文件废止申请";
                }else {
                    title = resHistory.getUpdateName()+"在"+resHistory.getUpdateDate()+"提交了文件换版申请";
                }
                swMessageService.rejectSend(1,1,resHisMap,approveId.toString(),"/approveFile",title, title,userApprove,"fileApproval");                     //给审批人的“文件审批”发
            }
            swMessageService.rejectSend(0,1,resHisMap,user.getUserID().toString(),"/applyChangeFileVersion",null,null,user,"versionApply");  //给自己的“文件换版申请”发
        }else{
            //wyu：删除被换版文件的引用（关联ResEntity表的引用）
            List<Integer> listUserIDs =  resAclMapper.getAllUserIdsByFileId(res.getId());
            //List<Integer> listUserIDs = resCategoryAclMapper.getAllUserIdsByCategory(category);
            resNoticeService.addSpecialAuthUserId(listUserIDs, user);
            res.setChangeNum(resHistory.getChangeNum());
            res.setOperation("0");
            res.setUpdator(resHistory.getUpdator());
            res.setUpdateName(resHistory.getUpdateName());
            res.setUpdateDate(resHistory.getUpdateDate());
            res.setAuditDate(resHistory.getUpdateDate());
            if ("1".equals(resHistory.getTeminateState())) {
                res.setTeminateState("1");
                res.setTeminater(user.getUserID());
                res.setTeminaterName(user.getUserName());
                res.setTeminateTime(date);
                int updateFileState = resMapper.update(res);
                resCorrelationService.batchRemoveCorrelation(file, user);
                resNoticeService.sendResFileNotice(user, null, null, null, resHistory.getId(),null, null, null, "4",listUserIDs,null,null);
            }else {
                String oldFilename = res.getPath();
                callback = new ResUsing(res.getId(), res.getClass());
                uploadService.delFileUsing(callback, oldFilename, user);
                resHistory.setContent(content);
                res.setContent(resHistory.getContent());
                res.setPath(resHistory.getPath());
                res.setSize(resHistory.getSize());
                res.setVersion(resHistory.getVersion());
                int updateFileState = resMapper.update(res);
                uploadService.addFileUsing(callback, res.getPath(), res.getName(), user, module);
                //换版时若是讨论区有关联要发送2条消息
                forumService.changeResVersionSendNewMes(res.getId(),res.getName(),res.getFileSn(),res.getChangeNum());
                resNoticeService.sendResFileNotice(user, null, null, null, resHistory.getId(),null, null, null, "3",listUserIDs,null,null);
            }
        }
        this.sendFile(res.getId(), user, "2", "1", null,"1", res);  //长连接推送新文件给资源中心模块
        this.sendSearchByFile(res, user, file);  //长连接推送新文件给搜索
    }

    @Override
    public void handleFileByNeedtoApproval(User user, ResHistory resHistory, ApprovalProcess approvalProcess, String type) {
        ApprovalProcess ap = updateApprovalProcessForResource(approvalProcess);
        ResHistory resHis = null;
        HashMap<String, Object> mapHis = new HashMap<>();
        if(approvalProcess.getApproveStatus().equals("3")){
            resHistory.setAuditor(user.getUserID());
            resHistory.setAuditName(user.getUserName());
            resHistory.setAuditDate(NewDateUtils.dateToString(approvalProcess.getHandleTime(),"yyyy-MM-dd HH:mm:ss"));
            int upStateHis = resHistoryMapper.update(resHistory);
            resHis = resHistoryMapper.getSingle(resHistory);
            mapHis.put("resHis", resHis);
            //若此文件是换版的文件 驳回时要把文件夹中文件的状态修改成可以进行换版
            if (resHis.getChangeNum() > 0) {
                ResEntity r = new ResEntity();
                r.setId(resHis.getFile());
                r.setOperation("0");                //此字段只要审批换版成功后就为0，在刚一新增时为
                int upFileState = resMapper.update(r);
                this.sendFile(r.getId(), user, "2", "1", null,"1",null);                   //长连接推送新文件给资源中心模块
                this.sendSearchByFile(null, user, r.getId());  //长连接推送新文件给搜索
                User userUpdator = userService.getUserByID(resHis.getUpdator());
                swMessageService.rejectSend(0,-1,mapHis,resHis.getUpdator().toString(),"/applyChangeFileVersion",null,null,userUpdator,"versionApply");    //给换版申请人发
                if ("1".equals(resHis.getTeminateState())) {
                    UserSuspendMsg userSuspendMsg = this.sendNewMessageByResource(resHis.getUpdator(),"4",resHis.getChangeNum(),resHis.getName(), resHis.getId(), resHis.getAuditDate(), "fileChangeMessageDetail");  //驳回时给我的消息发推送
                }else {
                    UserSuspendMsg userSuspendMsg = this.sendNewMessageByResource(resHis.getUpdator(),"2",resHis.getChangeNum(),resHis.getName(), resHis.getId(), resHis.getAuditDate(), "fileChangeMessageDetail");  //驳回时给我的消息发推送
                }
            } else {
                User userCreator = userService.getUserByID(resHis.getCreator());
                swMessageService.rejectSend(0,-1,mapHis,resHis.getCreator().toString(),"/applyPublishFile",null,null,userCreator,"releaseApply");       //给发布申请人发
                UserSuspendMsg userSuspendMsg = this.sendNewMessageByResource(resHis.getCreator(),"2",resHis.getChangeNum(),resHis.getName(), resHis.getId(), resHis.getAuditDate(), "fileMessageDetail");   //向文件发布申请跳转
                List<Integer> listUserIDs = resCategoryService.getAllUserIdsByCategory(resHis.getCategory());
                resNoticeService.addSpecialAuthUserId(listUserIDs, user);  //填入特殊权限
                if (resHis.getBatchUuid() != null) {
                    Long num = this.getNoApproveFileNum(resHis.getBatchUuid(), "1");
                    if (num.equals(Long.valueOf(0))) {
                        Long finishNum = this.getNoApproveFileNum(resHis.getBatchUuid(), "2");
                        if (!finishNum.equals(Long.valueOf(0))) {
                            resNoticeService.sendResFileNotice(user, null, null, null, null,resHis.getBatchUuid(), finishNum, null,"1", listUserIDs,null,null);
                        }
                    }
                }

            }
            this.sendApprovalsFile(resHis.getId(),mapHis,ap.getId());                 //文件驳回时给当前所有的审批人发推送
        } else {
            resHis = resHistoryMapper.getSingle(resHistory);
            mapHis.put("resHis", resHis);
            String title = null;
            if (type.equals("1")) {
                this.addApprovalProcessForResource(resHistory.getId(), user, resHistory.getAuditor(), ap.getLevel()+1, resHistory.getAuditName(), NewDateUtils.dateToString(approvalProcess.getHandleTime(),"yyyy-MM-dd HH:mm:ss"),"",14);
                User userAuditor = userService.getUserByID(resHistory.getAuditor());
                if (resHis.getChangeNum() > 0) {
                    if ("1".equals(resHis.getTeminateState())) {
                        title = resHis.getUpdateName()+"在"+resHis.getUpdateDate()+"提交了文件废止申请";
                    }else {
                        title = resHis.getUpdateName()+"在"+resHis.getUpdateDate()+"提交了文件换版申请";
                    }
                    swMessageService.rejectSend(1,1,mapHis,resHistory.getAuditor().toString(),"/approveFile",title, title, userAuditor,"fileApproval");    //给审批人的“文件审批”发
                } else {
                    title = resHis.getCreateName()+"在"+resHis.getCreateDate()+"提交了文件发布申请";
                    swMessageService.rejectSend(1,1,mapHis,resHistory.getAuditor().toString(),"/approveFile", title, title, userAuditor,"fileApproval");    //发给下一个审批人
                }
            } else {
                this.addApprovalProcessForResource(resHis.getId(), user, user.getUserID(), ap.getLevel()+1, user.getUserName(), NewDateUtils.dateToString(approvalProcess.getHandleTime(),"yyyy-MM-dd HH:mm:ss"),"rb",14);
                if (resHis.getChangeNum() > 0) {
                    if ("1".equals(resHis.getTeminateState())) {
                        title = resHis.getUpdateName()+"在"+resHis.getUpdateDate()+"提交了文件废止申请";
                    }else {
                        title = resHis.getUpdateName()+"在"+resHis.getUpdateDate()+"提交了文件换版申请";
                    }
                    swMessageService.rejectSendToMidManageCode(1,1,mapHis,user.getOid(),"rb","/approveFileByFinal","versionApproval", title, title, "general");  //发给终审人员
                } else {
                    title = resHis.getCreateName()+"在"+resHis.getCreateDate()+"提交了文件发布申请";
                    swMessageService.rejectSendToMidManageCode(1,1,mapHis,user.getOid(),"rb","/approveFileByFinal","versionApproval", title, title, "general");  //发给终审人员
                }
            }
            swMessageService.rejectSend(0,1,mapHis,user.getUserID().toString(),"/approvalsFile",null,null,user,"fileApproval");      //给自己的已批准中发
        }
        clusterMessageSendingOperations.convertAndSendToUser(resHistory.getId().toString(),"/fileUpdateState",null,null,user.getOid(),user.getOrganization().getName(), "1"); //给查看当前文件详情的人推送消息，提示文件变化了
        swMessageService.rejectSend(-1,-1,mapHis,user.getUserID().toString(),"/approveFile",null,null,user,"fileApproval");         //给自己待审批发
    }


    @Override
    public String userStopFileProcess(User user, ResHistory resHistory, Integer processId) {
        List<ApprovalProcess> listAp = approvalProcessService.getApprovalProcessByBusiness(resHistory.getId(), 14, "");
        String status = "1";
        if(listAp != null){
            ApprovalProcess approvalEnd = listAp.get(listAp.size()-1);
            if (!processId.equals(approvalEnd.getId())) {
                status = "3";
            }else {
                if("1".equals(approvalEnd.getApproveStatus())){
                    ResHistory oldResHis = resHistoryMapper.getSingle(resHistory);
                    oldResHis.setApproveStatus("5");
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("resHis", oldResHis);
                    ApprovalProcess ap = new ApprovalProcess();
                    ap.setId(processId);
                    ap.setApproveStatus("5");
                    ap.setHandleTime(new Date());
                    ApprovalProcess approvalProcess = updateApprovalProcessForResource(ap);
                    if(approvalProcess.getToMid() != null){
                        swMessageService.rejectSendToMidManageCode(-1,-1,map,user.getOid(),"rb","/approveFileByFinal","versionApproval",null,null, "general");  //给所有终审人员发送
                        this.sendApprovalsFile(resHistory.getId(),map,0);     //给审批中间流程的人发
                    } else {
                        User userApprove = userService.getUserByID(approvalProcess.getToUser());
                        swMessageService.rejectSend(-1,-1,map, approvalProcess.getToUser().toString(), "/approveFile",null,null,userApprove,"fileApproval");         //给审批人发
                        this.sendApprovalsFile(resHistory.getId(),map,approvalProcess.getId());     //给审批中间流程的人发
                    }
                    clusterMessageSendingOperations.convertAndSendToUser(resHistory.getId().toString(),"/fileUpdateState",null,null,user.getOid(),user.getOrganization().getName(),"2"); //给查看当前文件详情的人推送消息，提示文件终止了
                    if(oldResHis.getChangeNum() > 0 || "1".equals(oldResHis.getTeminateState())){
                        ResEntity r = new ResEntity();
                        r.setId(oldResHis.getFile());
                        r.setOperation("0");
                        int upFileState = resMapper.update(r);
                        swMessageService.rejectSend(0,-1,map,oldResHis.getUpdator().toString(),"/applyChangeFileVersion",null,null,user,"versionApply");    //给换版申请人发
                        this.sendFile(r.getId(), user, "2", "1", null,"1",null);    //给文件夹发文件可以继续换版
                    } else {
                        swMessageService.rejectSend(0,-1,map,oldResHis.getCreator().toString(),"/applyPublishFile",null,null,user,"releaseApply");           //给发布申请人发
                    }
                    int upStateHis = resHistoryMapper.update(resHistory);
                } else {
                    status = "4";
                }
            }
        }
        return status;
    }

    /*
     *第一版查询的是： 不是大小总务换版文件
     * 第二版查询的是： 需要审批的换版文件
     * 区别在于type字段代表的意思不一样
     */
    @Override
    public QueryData getPageUpdatePublishFile(User user, PageInfo pageInfo, String approveStatus) {
        ArrayList list = getAllCategoryID(user);
        QueryData query = new QueryData();
        query.put("list", list);
        query.put("approveStatus", approveStatus);
        query.put("pageInfo", pageInfo);
        List<ResHistory> listPageHis = resHistoryMapper.getAllUpdatePublishlistPage(query);
        QueryData queryData = new QueryData();
        queryData.put("pageInfo", pageInfo);
        queryData.put("list", listPageHis);
        return queryData;
    }

    @Override
    public List<ResHistory> applyChangeFileVersion(User user) {
        List<ResHistory> listHis = resHistoryMapper.getApplyUpdateFileVersionByUser(user.getUserID());
        return listHis;
    }

    @Override
    public List<ResHistory> resourceApproveFile(User user, String approveStatus) {
        HashMap<String,Object> param = new HashMap<>();
        param.put("userID", user.getUserID());
        param.put("approveStatus", approveStatus);
        List<ResHistory>  listHis = resHistoryMapper.getApproveFileByUser(param);
        return listHis;
    }

    @Override
    public List<ResHistory> resourceFinalApproveFile(User user) {
        ApprovalProcess ap = new ApprovalProcess();
        ap.setFromOrg(user.getOid());
        ap.setToMid("rb");
        List<ResHistory> listHis = resHistoryMapper.getFinalFile(ap);
        return listHis;
    }

    @Override
    public List<UserDto> userList(User user, Integer hisId, Integer borrowId, Integer aboutId, String type) {
        List list = new ArrayList();
        List listAuth = new ArrayList();
        listAuth.add("super");
        listAuth.add("staff");
        listAuth.add("smallSuper");
        HashMap<String, Object> map = new HashMap<>();
        List<UserDto> listUser = null;
        map.put("oid", user.getOid());
        map.put("listAuth", listAuth);
        if (hisId !=null || borrowId !=null || aboutId != null) {
            List<ApprovalProcess> listAp = null;
            if (hisId !=null) {
                listAp = approvalProcessService.getApprovalProcessByBusiness(hisId, 14, "");
            }else if(borrowId != null){
                listAp = approvalProcessService.getApprovalProcessByBusiness(borrowId, 29, "");
            }else if(aboutId != null){
                listAp = approvalProcessService.getApprovalProcessByBusiness(aboutId, 57, "");
            }
            if (!listAp.isEmpty()) {
                for (ApprovalProcess ap : listAp) {
                    list.add(ap.getToUser());
                }
                list.add(listAp.get(0).getFromUser());
            }
            map.put("list", list);
            listUser = resAclMapper.listUserByResource(map);
        }else {
            if(type == null){
                list.add(user.getUserID());
                map.put("list", list);
                listUser = resAclMapper.listUserByResource(map);
            } else {
                listUser = resAclMapper.listAllUserByResource(map);
            }
        }
        return listUser;
    }

    @Override
    public QueryData getHistoryPublishFileMessage(Integer hisId, User user) {
        ResHistory rh = new ResHistory();
        rh.setId(hisId);
        ResHistory resHis = resHistoryMapper.getSingle(rh);
        ResHistory resFirstHis = resHistoryMapper.getFirstFileHistory(resHis);
        ResCategory cat = this.getCategory(resHis.getCategory());
        String categoryName = this.allParentCategoryName(cat.getParent(), cat.getName(), null, "1");     //获取文件类别
        List<User> list = new ArrayList<User>(2);
        User userManager = userService.getUserByRoleCode(user.getOid(), "general");
        list.add(userManager);
        Integer minorAffairsId = userService.getSmallManageByMCode(user.getOid(),"general");
        if (minorAffairsId != 0) {
            User userAfffairsId = new User();
            userAfffairsId.setUserID(minorAffairsId);
            list.add(userAfffairsId);
        }
        QueryData query = new QueryData();
        query.put("fileHistory", resHis);
        query.put("firstAuditDate", resFirstHis.getAuditDate());
        query.put("categoryName",categoryName);
        query.put("listUser", list);
        return query;
    }

    private ResCategory getCategory(Integer id){
        ResCategory rc = new ResCategory();
        rc.setId(id);
        ResCategory category = resCategoryMapper.getSingle(rc);
        return category;
    }

    @Override
    public QueryData getUpFile(User user, PageInfo pageInfo, Integer fileId) {
        ResEntity r = new ResEntity();
        r.setId(fileId);
        ResEntity res = resMapper.getSingle(r);
        QueryData queryData = new QueryData();
        queryData.put("file", fileId);
        queryData.put("changeNum", res.getChangeNum());
        queryData.put("pageInfo", pageInfo);
        List<ResHistory> listPageHistory = resHistoryMapper.getHistoryByFilelistPage(queryData);
        QueryData query = new QueryData();
        query.put("pageInfo",pageInfo);
        query.put("list", listPageHistory);
        return query;
    }

    @Override
    public QueryData getFileMes(Integer id, User user) {
        ResEntity r = new ResEntity();
        r.setId(id);
        ResEntity  resource = resMapper.getSingle(r);
        ResCategory cat = this.getCategory(resource.getCategory());
        String categoryName = this.allParentCategoryName(cat.getParent(), cat.getName(), null, "1");      //获取文件类别
        String audit = null;
        ResHistory resHis = new ResHistory();
        resHis.setFile(id);
        ResHistory resFirstHis = resHistoryMapper.getFirstFileHistory(resHis);
        if(resFirstHis == null){
            resFirstHis = new ResHistory();
            resFirstHis.setFile(resource.getId());
            resFirstHis.setCategory(resource.getCategory());
            resFirstHis.setCategoryPath(resource.getCategoryPath());
            resFirstHis.setName(resource.getName());
            resFirstHis.setContent(resource.getContent());
            resFirstHis.setFileSn(resource.getFileSn());
            resFirstHis.setPath(resource.getPath());
            resFirstHis.setSize(resource.getSize());
            resFirstHis.setVersion(resource.getVersion());
            resFirstHis.setChangeNum(resource.getChangeNum());
            resFirstHis.setOpertatorName(resource.getOpertatorName());
            resFirstHis.setOperateDate(resource.getOperateDate());
            resFirstHis.setVerifierName(resource.getVerifierName());
            resFirstHis.setVerifyDate(resource.getVerifyDate());
            resFirstHis.setApproverName(resource.getApproverName());
            resFirstHis.setApproveDate(resource.getApproveDate());
            resFirstHis.setApproveStatus(resource.getApproveStatus());
            resFirstHis.setCreator(resource.getCreator());
            resFirstHis.setCreateName(resource.getCreateName());
            resFirstHis.setCreateDate(resource.getCreateDate());
            resFirstHis.setAuditDate(resource.getCreateDate());
            resFirstHis.setTeminateState("0");
            resFirstHis.setOperation("4");
            int insetFileHistory = resHistoryMapper.insert(resFirstHis);
        }
        resHis.setOperation("5");
        List<ResHistory> resHisMove = resHistoryMapper.getRecordByOperation(resHis);
        if(resHisMove.size() < 1){
            Integer moveState =  addHisMoveFile(resource.getId(), resource.getCategory(), categoryName, resource.getVersion(),resource.getCategoryPath());
        }
        resHis.setOperation("6");
        Integer upName = 0;
        Integer upFileSn = 0;
        List<ResHistory> resHisUpName = resHistoryMapper.getRecordByOperation(resHis);
        if(resHisUpName.size() < 1) {
            Integer upNameState = addHisUpNameFile(resource.getId(), resource.getCategory(), resource.getName(), resource.getVersion());
            resHisUpName = resHistoryMapper.getRecordByOperation(resHis);
        }
        if (resHisUpName.size() > 1) {
            upName = resHisUpName.size() - 1;
        }
        resHis.setOperation("7");
        List<ResHistory> resHisUpFileSn = resHistoryMapper.getRecordByOperation(resHis);
        if (resHisUpFileSn.size() < 1) {
            Integer UpfileSn = addHisUpFileSn(resource.getId(), resource.getCategory(), resource.getFileSn(), resource.getVersion());
            resHisUpFileSn = resHistoryMapper.getRecordByOperation(resHis);
        }
        if (resHisUpFileSn.size() > 1) {
            upFileSn = resHisUpFileSn.size() - 1;
        }
        audit = resFirstHis.getAuditDate();
        List<User> list = new ArrayList<User>(2);
        User userManager = userService.getUserByRoleCode(user.getOid(), "general");
        list.add(userManager);
        Integer minorAffairsId = userService.getSmallManageByMCode(user.getOid(),"general");
        if (minorAffairsId != 0) {
            User userAfffairsId = new User();
            userAfffairsId.setUserID(minorAffairsId);
            list.add(userAfffairsId);
        }
        //获取审批流程
        ResHistory resHisNow = resHistoryMapper.getNewFileHistory(id);
        List<ApprovalProcess> listAp = this.getListApForResource(resHisNow);

        QueryData query = new QueryData();
        query.put("resource", resource);
        query.put("firstAuditDate", audit);
        query.put("categoryName",categoryName);
        query.put("listUser", list);
        query.put("upName", upName);
        query.put("upFileSn", upFileSn);
        query.put("listAp", listAp);
        if (userService.isGeneral(user) || userService.isGeneralSmallManager(user)) {
            ResSignedMes resSignedMes = new ResSignedMes();
            ResHistory newResHis = resHistoryMapper.getNewFileHistoryByVersion(id);
            resSignedMes.setResHisId(newResHis.getId());
            Integer signedNum = 0;
            Integer haveSignedNum = 0;
            if (newResHis.getBatchUuid() != null) {
                signedNum = resNoticeService.resNoticeNumByType(null, null, newResHis.getBatchUuid(), "1", null);
                haveSignedNum = resNoticeService.resNoticeNumByType(null, null, newResHis.getBatchUuid(), "1", "1");
                resSignedMes.setSignedNum(signedNum);
                resSignedMes.setHaveSignedNum(haveSignedNum);
            } else {
                if (newResHis.getChangeNum().equals(0)) {
                    signedNum = resNoticeService.resNoticeNumByType(null, newResHis.getId(), null, "2", null);
                    haveSignedNum = resNoticeService.resNoticeNumByType(null, newResHis.getId(), null, "2", "1");
                } else {
                    signedNum = resNoticeService.resNoticeNumByType(null, newResHis.getId(), null, "3", null);
                    haveSignedNum = resNoticeService.resNoticeNumByType(null, newResHis.getId(), null, "3", "1");
                }

                resSignedMes.setSignedNum(signedNum);
                resSignedMes.setHaveSignedNum(haveSignedNum);
            }
            query.put("resSignedMes", resSignedMes);
        }
        return query;
    }

    @Override
    public QueryData getFileMesByReference(Integer id, User user) {
        ResEntity r = new ResEntity();
        r.setId(id);
        ResEntity  resource = resMapper.getSingle(r);
        ResCategory cat = this.getCategory(resource.getCategory());
        String categoryName = this.allParentCategoryName(cat.getParent(), cat.getName(), null, "1");      //获取文件类别
        QueryData query = new QueryData();
        query.put("resource", resource);
        query.put("categoryName",categoryName);
        return query;
    }

    @Override
    public QueryData getHisFileMes(Integer id, User user) {
        ResHistory rh = new ResHistory();
        rh.setId(id);
        ResHistory his = resHistoryMapper.getSingle(rh);
        ResCategory cat = this.getCategory(his.getCategory());
        String categoryName = this.allParentCategoryName(cat.getParent(), cat.getName(), null, "1");    //获取文件类别
        String audit = null;
        if (his.getChangeNum() > 0) {
            ResHistory resHis = new ResHistory();
            resHis.setFile(his.getFile());
            ResHistory resFirstHis = resHistoryMapper.getFirstFileHistory(resHis);
            audit = resFirstHis.getAuditDate();
        }
        List<User> list = new ArrayList<User>(2);
        User userManager = userService.getUserByRoleCode(user.getOid(), "general");
        list.add(userManager);
        Integer minorAffairsId = userService.getSmallManageByMCode(user.getOid(),"general");
        if (minorAffairsId != 0) {
            User userAfffairsId = new User();
            userAfffairsId.setUserID(minorAffairsId);
            list.add(userAfffairsId);
        }
        List<ApprovalProcess> listAp = this.getListApForResource(his);
        QueryData query = new QueryData();
        query.put("resource", his);
        query.put("firstAuditDate", audit);
        query.put("categoryName",categoryName);
        query.put("listUser", list);
        query.put("listAp", listAp);
        if (userService.isGeneral(user) || userService.isGeneralSmallManager(user)) {
            ResSignedMes resSignedMes = new ResSignedMes();
            resSignedMes.setResHisId(id);
            Integer signedNum = 0;
            Integer haveSignedNum = 0;
            if (his.getBatchUuid() != null) {
                signedNum = resNoticeService.resNoticeNumByType(null, null, his.getBatchUuid(), "1", null);
                haveSignedNum = resNoticeService.resNoticeNumByType(null, null, his.getBatchUuid(), "1", "1");
                resSignedMes.setSignedNum(signedNum);
                resSignedMes.setHaveSignedNum(haveSignedNum);
            } else {
                if (his.getChangeNum().equals(0)) {
                    signedNum = resNoticeService.resNoticeNumByType(null, id, null, "2", null);
                    haveSignedNum = resNoticeService.resNoticeNumByType(null, id, null, "2", "1");
                } else {
                    signedNum = resNoticeService.resNoticeNumByType(null, id, null, "3", null);
                    haveSignedNum = resNoticeService.resNoticeNumByType(null, id, null, "3", "1");
                }
                resSignedMes.setSignedNum(signedNum);
                resSignedMes.setHaveSignedNum(haveSignedNum);
            }
            query.put("resSignedMes", resSignedMes);
        }
        return query;
    }

    @Override
    public QueryData getHisFileMesByReference(Integer id) {
        ResHistory rh = new ResHistory();
        rh.setId(id);
        ResHistory his = resHistoryMapper.getSingle(rh);
        ResCategory cat = this.getCategory(his.getCategory());
        String categoryName = this.allParentCategoryName(cat.getParent(), cat.getName(), null, "1");    //获取文件类别
        QueryData query = new QueryData();
        query.put("resource", his);
        query.put("categoryName",categoryName);
        return query;
    }

    @Override
    public Integer removeStatus(Integer id, Integer categoryId) {
        ResEntity r = new ResEntity();
        r.setId(id);
        r.setCategory(categoryId);
        Integer upFileCategory = resMapper.update(r);
        Integer state = 0;
        if(upFileCategory > 0){
            ResHistory rh = new ResHistory();
            rh.setFile(id);
            rh.setCategory(categoryId);
            Integer upHistoryFileCategory = resHistoryMapper.updateHIstoryByfirst(rh);
            if (upHistoryFileCategory > 0) {
                state = 1;
                int delAlloldAuth = resAclMapper.delAllAuthByFile(id);
                Integer insertNewAuthByFile = resAclMapper.insertAuthByCategoryAuth(r);
            }
        }
        return state;
    }

    @Override
    public String removeStatusByCentre(Integer id, Integer categoryId, User user, ResEntity res, Integer newResHisId, String type) {
        ResCategory newCat = resCategoryService.getSingle(categoryId);
        if (Byte.valueOf("1").equals(newCat.getIsTrash())) {
            if("1".equals(type)){
                this.handleOrGetBorrowedFile(res,"2",user);
            }
        }
        //新增一版本历史
        ResCategory oldCat = resCategoryService.getSingle(res.getCategory());
        String oldAllCategoryName = this.allParentCategoryName(oldCat.getParent(), oldCat.getName(), null, "1");
        ResHistory rhMove = new ResHistory();
        rhMove.setFile(id);
        rhMove.setCategory(categoryId);
        rhMove.setCreateName(user.getUserName());
        rhMove.setCreateDate(NewDateUtils.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
        rhMove.setVersion(res.getVersion());
        rhMove.setOperation("5");
        String allCategoryName = this.allParentCategoryName(newCat.getParent(), newCat.getName(), null, "1");
        rhMove.setCategoryName(allCategoryName);
        rhMove.setCategoryPath(newCat.getPath());
        ResEntity r = new ResEntity();
        r.setId(id);
        r.setCategory(categoryId);
        r.setMoveNum(res.getMoveNum()+1);
        r.setCategoryPath(newCat.getPath());
        ResHistory rh = new ResHistory();
        rh.setCategoryPath(newCat.getPath());
        rh.setFile(id);
        List<Integer> userIds = resAclMapper.getAllUserIdsByFileId(id);
        resNoticeService.addSpecialAuthUserId(userIds, user);
        if (Byte.valueOf("1").equals(newCat.getIsTrash())) {
            r.setIsTrash(Byte.valueOf("1"));
            rh.setIsTrash(Byte.valueOf("1"));
            rhMove.setIsTrash(Byte.valueOf("1"));
            if (newResHisId != null) {
                //发送文件停用通知
                resNoticeService.sendResFileNotice(user, null, null, null, newResHisId,null,null, null, "6",userIds,null,null);
            }
        } else {
            r.setIsTrash(Byte.valueOf("0"));
            rh.setIsTrash(Byte.valueOf("0"));
            rhMove.setIsTrash(Byte.valueOf("0"));
            if (newResHisId != null) {
                resNoticeService.sendResFileNotice(user, null, null, null, newResHisId,null,null, null, "12",userIds,oldAllCategoryName,allCategoryName);
            }
        }
        Integer intFileMoveRecordHis = resHistoryMapper.insert(rhMove);
        resHistoryMapper.updateHIstoryByfirst(rh);
        resMapper.update(r);
        String path = oldAllCategoryName + "转移到" + allCategoryName;    //给下个方法中新增权限记录使用
        return path;
    }

    @Override
    public void changeAuthByMoveFile(String type, Integer id, Integer newResHisId, Integer categoryId, User user, ResEntity res, List<Integer> userID, String allCategoryName, String operateType){
        this.sendFile(id,user,"2","2",null,"1",res);           //删除文件
        ArrayList<Integer> oldUserIds = new ArrayList<>();
        ArrayList<Integer> delUserIds = new ArrayList<>();
        if ("1".equals(type)) {
            List<ResAcl> lsitAcl = resAclMapper.getFileAuth(id);
            int delAlloldAuth = resAclMapper.delAllAuthByFile(id);
            for (Integer userId : userID){
                resAclMapper.insert( new ResAcl(id, userId));
            }
            for(ResAcl acl : lsitAcl){
                if(userID.contains(acl.getUser())) {
                    oldUserIds.add(acl.getUser());
                } else {
                    delUserIds.add(acl.getUser());
                }
            }
            userID.removeAll(oldUserIds);
            if (!userID.isEmpty()) {
                resNoticeService.sendResFileNotice(user,null, null, null, newResHisId,null,null, null, "2", userID,null,null);
            }
            resCategoryService.insertResAclLog(null, res.getId(), userID, delUserIds, user, new Date(), allCategoryName, user.getOid(), "5");
        }else {
            List<ResCategoryAcl> listParentCategoryAuth = resCategoryAclMapper.getAllAuthByCategory(categoryId);
            userID = new ArrayList<>();
            for(ResCategoryAcl acl : listParentCategoryAuth){
                userID.add(acl.getUser());
            }
            resCategoryService.insertResAclLog(null, res.getId(), userID, delUserIds, user, new Date(), allCategoryName, user.getOid(), "5");
            resNoticeService.sendResFileNotice(user,null, null, null, newResHisId,null,null, null, "8", userID,null,null);
            ResEntity r = new ResEntity();
            r.setId(id);
            r.setCategory(categoryId);
            Integer insertNewAuthByFile = resAclMapper.insertAuthByCategoryAuth(r);
        }
        this.sendFile(id,user,"2","1",null,"1",null);           //新增文件
    }

    //获取某机构的全部的文件夹ID的list
    private ArrayList getAllCategoryID(User user){
        ResCategory rc = new ResCategory();
        rc.setOrg(user.getOid());
        List<ResCategory> listCategory = resCategoryMapper.getAllFirstCategoryByOid(rc);
        ArrayList list = new ArrayList(listCategory.size());
        for (ResCategory r : listCategory) {
            list.add(r.getId());
        }
        return list;
    }

    //判断是否是大小总务是返回1不是返回0
    protected String judgementGeneral(Integer userId){
        User user = userService.getUserByID(userId);
        String status = "0";
        User userManager = userService.getUserByRoleCode(user.getOid(), "general");
        Integer minorAffairsId = userService.getSmallManageByMCode(user.getOid(),"general");
        if (user.getUserID().equals(userManager.getUserID())) {
            status = "1";
        } else if (user.getUserID().equals(minorAffairsId) && minorAffairsId != 0) {
            status = "1";
        }
        return status;
    }
    //wyu: Begin of Forder Auth

    @Override
    public ResEntity getSingle(Integer fileId) {
        return resMapper.getSingle( new ResEntity(fileId) );
    }

    @Override
    public List<OrganizationDto> getAllUserIdsByFileId(Integer oid, Integer fileId) {
        List<Integer> userIds = resAclMapper.getAllUserIdsByFileId(fileId);
        ResEntity res = this.getSingle(fileId);
        List<Integer> userParentIds = resCategoryAclMapper.getAllUserIdsByCategory(res.getCategory());
        return orgService.getOrgWithAuthUserTreeByResLocking(oid,userParentIds,userIds,"2");
    }

    @Override
    public void updateFileAuth(Integer fileId, ArrayList<Integer> userID, ResEntity res, User user) {
        List<ResAcl> oldAcls = resAclMapper.getFileAuth(fileId);
        ArrayList<Integer> oldUserIds = new ArrayList<>();
        ArrayList<Integer> delUserIds = new ArrayList<>();
        for(ResAcl acl : oldAcls){
            if(userID.contains(acl.getUser())) {
                oldUserIds.add(acl.getUser());
            } else {
                delUserIds.add(acl.getUser());
                resAclMapper.delete(acl);
            }
        }
        userID.removeAll(oldUserIds);
        for (Integer userId : userID){
            resAclMapper.insert( new ResAcl(fileId, userId));
        }
        ResHistory resHistory = resHistoryMapper.getNewFileHistory(fileId);
        if (userID != null) {
            this.sendFile(fileId, user, "3", "1", userID,"2",null);    //给新增人新增文件
            //给新增人员发送新增文件权限通知
            resNoticeService.sendResFileNotice(user,null, null, null, resHistory.getId(),null,null, null, "2", userID,null,null);
        }
        if (!delUserIds.isEmpty()) {
            this.sendFile(fileId, user, "3", "2", delUserIds,"2",null);    //给人删除文件
            //给删除人员发送文件权限取消通知
            resNoticeService.sendResFileNotice(user,null, null, null, resHistory.getId(),null,null, null, "9", delUserIds,null,null);
        }
        ResCategory cat = resCategoryService.getSingle(res.getCategory());
        String allCategoryName = this.allParentCategoryName(cat.getParent(), cat.getName(), null, "1");
        resCategoryService.insertResAclLog(null, res.getId(), userID, delUserIds, user, new Date(), allCategoryName, user.getOid(), "2");
    }
    //wyu: End of Forder Auth

    @Override
    public QueryData generalSearchFile(User user, PageInfo pageInfo, ResEntity resEntity, String type) {
        ArrayList list = getAllCategoryID(user);
        QueryData query = new QueryData();
        query.put("list", list);
        query.put("name", resEntity.getName());
        query.put("type", type);
        query.put("user", user.getUserID());
        List<ResEntity> listFiles = new ArrayList<>();     //搜索出来的全部文件
        List<UserHonePageDto> listCreator = new ArrayList<>();   //去重后的全部发起人
        Set<String> set = new HashSet<>();
        Integer creator = resEntity.getCreator();
        if (creator != null) {
            query.put("creator", creator);
            UserHonePageDto userCreator = userService.getUserHonePageDtoByUserId(creator);
            listCreator.add(userCreator);
        } else {
            if (userService.isGeneral(user) || userService.isGeneralSmallManager(user) || userService.isSuper(user)) {
                listFiles = resMapper.searchfile(query);
            } else {
                listFiles = resMapper.searchfileByAuth(query);
            }
            for(ResEntity r : listFiles){
                if(r.getCreator() != null){
                    if(set.add(r.getCreator().toString())){
                        UserHonePageDto userCreator = userService.getUserHonePageDtoByUserId(r.getCreator());
                        listCreator.add(userCreator);
                    }
                }
            }
        }
        List<ResEntity> listFile = new ArrayList<>();      //搜索出来的分页文件
        if (userService.isGeneral(user) || userService.isGeneralSmallManager(user) || userService.isSuper(user)) {
            query.put("pageInfo", pageInfo);
            listFile = resMapper.searchfileByAuthlistPage(query);
        } else {
            query.put("pageInfo", pageInfo);
            listFile = resMapper.searchfilelistPage(query);
        }
        QueryData queryData = new QueryData();
        queryData.put("pageInfo", pageInfo);
        queryData.put("listFile", listFile);
        queryData.put("listCreator", listCreator);
        return queryData;
    }

    @Override
    public QueryData advancedSearchFile(User user, PageInfo pageInfo, ResEntity resEntity) {
        ArrayList list = getAllCategoryID(user);
        QueryData query = new QueryData();
        query.put("list", list);
        if(resEntity.getName() != null){
            query.put("name", resEntity.getName());
        }
        if(resEntity.getFileSn() != null){
            query.put("fileSn", resEntity.getFileSn());
        }
        if(resEntity.getVersion() != null){
            query.put("version", resEntity.getVersion());
        }
        if(resEntity.getCreateName() != null){
            query.put("createName", resEntity.getCreateName());
        }
        if(resEntity.getCreateDateBegin() != null){
            query.put("createDate", "1");
            query.put("createDateBegin", resEntity.getCreateDateBegin());
            if (resEntity.getCreateDateEnd() == null) {
                query.put("createDateEnd", NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"));
            }
        }
        if(resEntity.getCreateDateEnd() != null){
            query.put("createDate", "1");
            query.put("createDateEnd", resEntity.getCreateDateEnd());
            if (resEntity.getCreateDateBegin() == null) {
                query.put("createDateBegin", "1900-01-01 00:00:00");
            }
        }
        if(resEntity.getUpdateName() != null){
            query.put("updateName", resEntity.getUpdateName());
        }
        if(resEntity.getUpdateDateBegin() != null){
            query.put("updateDate","1");
            query.put("updateDateBegin", resEntity.getUpdateDateBegin());
            if (resEntity.getUpdateDateEnd() == null) {
                query.put("updateDateEnd", NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"));
            }
        }
        if(resEntity.getUpdateDateEnd() != null){
            query.put("updateDate","1");
            query.put("updateDateEnd", resEntity.getUpdateDateEnd());
            if (resEntity.getUpdateDateEnd() == null) {
                query.put("updateDateBegin", "1900-01-01 00:00:00");
            }
        }
        query.put("pageInfo", pageInfo);
        List<ResEntity> listFile = new ArrayList<>();
        if (userService.isGeneral(user) || userService.isGeneralSmallManager(user) || userService.isSuper(user)) {
            listFile = resMapper.searchFileByAuthlistPage(query);
        } else {
            query.put("user", user.getUserID());
            listFile = resMapper.searchFilelistPage(query);
        }
        QueryData queryData = new QueryData();
        queryData.put("pageInfo", pageInfo);
        queryData.put("listFile", listFile);
        return queryData;
    }

    @Override
    public QueryData searchFolder(User user, PageInfo pageInfo, String name) {
        QueryData query = new QueryData();
        query.put("pageInfo", pageInfo);
        query.put("org", user.getOid());
        query.put("name", name);
        List<ResCategory> listCategory = null;
        PageInfo pageInfoNew = new PageInfo();
        pageInfoNew.setPageSize(1);
        QueryData param = new QueryData();
        param.put("pageInfo", pageInfoNew);
        if (userService.isGeneral(user) || userService.isGeneralSmallManager(user) || userService.isSuper(user)) {
            listCategory = resCategoryMapper.searchFolderlistPage(query);
            for (ResCategory r : listCategory) {
                //获取到一级文件夹下是否有子文件夹，若是有就给childStatus属性填入1.
                param.put("parent",r.getId());
                Integer childrenNum = resCategoryMapper.getAllChildCategoryNumByParent(r.getId());
                if (childrenNum > 0) {
                    r.setChildStatus("1");
                }
                ResEntity listFile = resMapper.getOneFileByCategorylistPage(param);
                if (listFile != null) {
                    r.setChildStatus("2");
                }
                if(r.getParent() != null){
                    ResCategory cat = this.getCategory(r.getParent());
                    String categoryName = this.allParentCategoryName(cat.getParent(),cat.getName(), null, "1");
                    r.setAllParentCategory(categoryName);
                }
            }
        } else {
            query.put("user", user.getUserID());
            listCategory = resCategoryMapper.searchFolderByAuthlistPage(query);
            for (ResCategory r : listCategory) {
                param.put("user", user.getUserID());
                param.put("type","1");
                param.put("parent",r.getId());
                ResCategory catChild = resCategoryMapper.getOneCategoryByParentAuthlistPage(param);
                if (catChild != null) {
                    r.setChildStatus("1");
                }
                ResEntity oneFile = resMapper.getOneFileByCategorylistPage(param);
                if (oneFile != null) {
                    r.setChildStatus("2");
                }
                if(r.getParent() != null){
                    ResCategory cat = this.getCategory(r.getParent());
                    String categoryName = this.allParentCategoryName(cat.getParent(),cat.getName(), null, "1");
                    r.setAllParentCategory(categoryName);
                }
            }
        }
        QueryData queryData = new QueryData();
        queryData.put("pageInfo", pageInfo);
        queryData.put("listCategory", listCategory);
        return queryData;
    }

    @Override
    public QueryData getFileUserAuth(Integer fileId) {
        ResEntity res = getSingle(fileId);
        List<ResAcl> list = resAclMapper.getFileAuth(fileId);
        List<PostUserMussage> allAuth = new ArrayList<>();
        if(!list.isEmpty()){
            for(ResAcl r : list){
                allAuth.add(writeUserMessage(r.getUser()));
            }
        }
        QueryData query = new QueryData();
        query.put("resource",res);
        query.put("authNum", list.size());
        query.put("allAuth", allAuth);
        return query;
    }

    @Override
    public List<ResVisit> BrowseOrDownloadRecordByDate(Integer oid, String eventType, String type, String dateType, String startTime, String endTime, PageInfo pageInfo) {
        List<String> listFile = getAllFile(oid);
        QueryData query = new QueryData();
        query.put("type", type);
        query.put("startTime", startTime);
        if(!dateType.equals("1")){
            query.put("endTime", endTime);
        }
        query.put("pageInfo", pageInfo);
        query.put("list", listFile);
        query.put("eventType", eventType);
        List<ResVisit> listVisit = resVisMapper.getVisitByDatelistPage(query);
        if (!listVisit.isEmpty()) {
            for (ResVisit r : listVisit) {
                if(eventType.equals("1")){
                    r.setPostUserMussage(writeUserMessage(r.getCreator()));
                }else{
                    ResEntity res = new ResEntity();
                    res.setId(r.getFile());
                    r.setFileMessage(resMapper.getSingle(res));
                }
            }
        }
        return listVisit;
    }

    @Override
    public ResVisit getBrowseOrDownloadNum(Integer oid, String type, Date startTime, Date endTime, Integer fileId, Integer userId) {
        List<String> list = getAllFile(oid);
        QueryData query = new QueryData();
        query.put("type", type);
        query.put("startTime", startTime);
        query.put("endTime", endTime);
        if (fileId != null) {
            query.put("file", fileId);
        }
        if (userId != null) {
            query.put("creator", userId);
        }
        query.put("list", list);
        ResVisit rv = resVisMapper.getRecordNum(query);
        return rv;
    }

    @Override
    public List<ResVisit> browseOrDownloadDetailRecordByPerson(Integer userId, String type, String date, PageInfo pageInfo) {
        QueryData queryData = new QueryData();
        queryData.put("type", type);
        queryData.put("creator", userId);
        queryData.put("time", date);
        queryData.put("pageInfo", pageInfo);
        List<ResVisit> list = resVisMapper.getDetailRecordByPersonlistPage(queryData);
        return list;
    }

    @Override
    public QueryData getbrowseOrDownloadAllNumByfile(Integer file, String type,  String date, PageInfo pageInfo) {
        QueryData queryData = new QueryData();
        queryData.put("file", file);
        queryData.put("type", type);
        if(date != null){
            queryData.put("date", date);
        }
        queryData.put("pageInfo", pageInfo);
        List<ResVisit> list = resVisMapper.getAllPersonVisNumByFilelistPage(queryData);
        if(!list.isEmpty()){
            for (ResVisit r : list) {
                r.setPostUserMussage(writeUserMessage(r.getCreator()));
                PageInfo page = new PageInfo();
                page.setPageSize(1);
                QueryData queryAll = new QueryData();
                queryAll.put("type", type);
                queryAll.put("file", file);
                queryAll.put("creator", r.getCreator());
                if(date != null){
                    queryAll.put("date", date);
                }
                queryAll.put("pageInfo", page);
                List<ResVisit> listAllVisit = resVisMapper.getDetailRecordByFilelistPage(queryAll);
                r.setCreateDate(listAllVisit.get(0).getCreateDate());
            }
        }
        listTimeDesc(list);                  //从新排列list中的数据按时间倒叙显示
        QueryData query = new QueryData();
        query.put("list",list);
        query.put("pageInfo", pageInfo);
        return query;
    }

    @Override
    public QueryData getbrowseOrDownloadAllNumByCreator(Integer userId, String type, String date, PageInfo pageInfo) {
        QueryData queryData = new QueryData();
        queryData.put("creator", userId);
        queryData.put("type", type);
        if(date != null){
            queryData.put("date", date);
        }
        queryData.put("pageInfo", pageInfo);
        List<ResVisit> list = resVisMapper.getAllPersonVisNumByCreatorlistPage(queryData);
        if(!list.isEmpty()){
            for (ResVisit r : list) {
                ResEntity res = new ResEntity();
                res.setId(r.getFile());
                r.setFileMessage(resMapper.getSingle(res));
                PageInfo page = new PageInfo();
                page.setPageSize(1);
                QueryData queryAll = new QueryData();
                queryAll.put("type", type);
                queryAll.put("file", r.getFile());
                queryAll.put("creator", r.getCreator());
                if(date != null){
                    queryAll.put("date", date);
                }
                queryAll.put("pageInfo", page);
                List<ResVisit> listAllVisit = resVisMapper.getDetailRecordByFilelistPage(queryAll);
                r.setCreateDate(listAllVisit.get(0).getCreateDate());
            }
        }
        listTimeDesc(list);                            //从新排列list中的数据按时间倒叙显示
        QueryData query = new QueryData();
        query.put("list",list);
        query.put("pageInfo", pageInfo);
        return query;
    }

    //按时间倒序排序
    private void listTimeDesc(List<ResVisit> list){
        Collections.sort(list, new Comparator<ResVisit>(){
            final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            @Override
            public int compare(ResVisit o1, ResVisit o2) {
                return (o2.getCreateDate().toString()).compareTo(o1.getCreateDate().toString());
            }
        });
    }


    @Override
    public QueryData getDetailRecord(String type, Integer file, Integer userId, String date, PageInfo pageInfo) {
        QueryData queryData = new QueryData();
        queryData.put("creator", userId);
        queryData.put("file",file);
        queryData.put("type", type);
        if(date != null){
            queryData.put("date", date);
        }
        queryData.put("pageInfo", pageInfo);
        List<ResVisit> list = resVisMapper.getDetailRecordByFilelistPage(queryData);
        QueryData query = new QueryData();
        query.put("list",list);
        query.put("pageInfo", pageInfo);
        return query;
    }

    @Override
    public Integer updateFileName(String name, User user, Integer id) {
        ResEntity r = new ResEntity();
        r.setId(id);
        r.setName(name);
        ResEntity oldRes = resMapper.getSingle(r);
        Integer upFileCategory = resMapper.update(r);
        ResEntity res = resMapper.getSingle(r);
        List<Integer> userIds = resAclMapper.getAllUserIdsByFileId(id);
        resNoticeService.addSpecialAuthUserId(userIds, user);  //填入特殊权限
        String oldName = oldRes.getFileSn() + "/" + oldRes.getName();
        String newName = res.getFileSn() + "/" + res.getName();
        resNoticeService.sendResFileNotice(user,null,null,null,null,null,null,null,"14",userIds,oldName,newName);
        Integer state = 0;
        if(upFileCategory > 0){
            ResHistory rh = new ResHistory();
            rh.setFile(id);
            rh.setName(name);
            rh.setCategory(res.getCategory());
            rh.setCreateName(user.getUserName());
            rh.setCreateDate(NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"));
            rh.setVersion(res.getVersion());
            rh.setOperation("6");
            Integer intFileMoveRecordHis = resHistoryMapper.insert(rh);
            state = 1;
        }
        return state;
    }

    @Override
    public Integer updateFileSn(User user, Integer id, String fileSn) {
        Integer resCheckFileSn = checkFileSn(user.getOid(), fileSn);
        Integer state = 0;
        if(resCheckFileSn.equals(0)){
            ResEntity r = new ResEntity();
            r.setId(id);
            r.setFileSn(fileSn);
            ResEntity oldRes = resMapper.getSingle(r);
            Integer upFileCategory = resMapper.update(r);
            ResEntity res = resMapper.getSingle(r);
            List<Integer> userIds = resAclMapper.getAllUserIdsByFileId(id);
            resNoticeService.addSpecialAuthUserId(userIds, user);  //填入特殊权限
            String oldName = oldRes.getFileSn() + "/" + oldRes.getName();
            String newName = res.getFileSn() + "/" + res.getName();
            resNoticeService.sendResFileNotice(user,null,null,null,null,null,null,null,"14",userIds,oldName,newName);
            if(upFileCategory > 0){
                ResHistory rh = new ResHistory();
                rh.setFile(id);
                rh.setFileSn(fileSn);
                rh.setCategory(res.getCategory());
                rh.setCreateName(user.getUserName());
                rh.setCreateDate(NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"));
                rh.setVersion(res.getVersion());
                rh.setOperation("7");
                upFileCategory = resHistoryMapper.insert(rh);
                if(upFileCategory > 0){
                    state = 1;
                }else {
                    state = 2;
                }
            }
        }
        return state;
    }

    @Override
    public Integer recordViewAndDownload(User user, Integer id, String type) {
        ResEntity r = new ResEntity();
        r.setId(id);
        ResEntity res = resMapper.getSingle(r);
        if(type.equals("1")){
            r.setViewNum(res.getViewNum() + 1);
        }else{
            r.setDownloadNum(res.getDownloadNum() + 1);
        }
        if (Byte.valueOf("1").equals(res.getIsTrash())) {
            r.setIsTrash(Byte.valueOf("1"));
        }
        Integer state = resMapper.update(r);
        if(state > 0){
            ResVisit rv = new ResVisit();
            rv.setFile(res.getId());
            rv.setType(type);
            rv.setCreator(user.getUserID());
            rv.setCreateName(user.getUserName());
            rv.setCreateDate(NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"));
            state = resVisMapper.insert(rv);
        }
        return state;
    }

    @Override
    public List<FiveRecordEntity> getRecordByOperation(String operation, Integer fileId) {
        ResHistory rh = new ResHistory();
        rh.setFile(fileId);
        rh.setOperation(operation);
        List<ResHistory> list = resHistoryMapper.getRecordByOperation(rh);
        List<FiveRecordEntity> listRecord = new ArrayList<>();
        for(int i = 1; i < list.size(); i++){
            FiveRecordEntity Record = new FiveRecordEntity();
            if(operation.equals("5")){
                Record.setNameBefore(list.get(i-1).getCategoryName());
                Record.setNameAfter(list.get(i).getCategoryName());
                Record.setCreateName(list.get(i).getCreateName());
                Record.setCreateDate(list.get(i).getCreateDate());
            } else if(operation.equals("6")){
                Record.setNameBefore(list.get(i-1).getName());
                Record.setNameAfter(list.get(i).getName());
                Record.setCreateName(list.get(i).getCreateName());
                Record.setCreateDate(list.get(i).getCreateDate());
            }else {
                Record.setNameBefore(list.get(i-1).getFileSn());
                Record.setNameAfter(list.get(i).getFileSn());
                Record.setCreateName(list.get(i).getCreateName());
                Record.setCreateDate(list.get(i).getCreateDate());
            }
            listRecord.add(Record);
        }
        return listRecord;
    }

    @Override
    public List<ResVisit> getRecordByType(String type, Integer fileid) {
        ResVisit rv = new ResVisit();
        rv.setType(type);
        rv.setFile(fileid);
        List<ResVisit> listResVisit = resVisMapper.getResVisit(rv);
        return listResVisit;
    }

    @Override
    public List<ResHistory> publicFileByApproveStatus(HashMap hisMap) {
        List<ResHistory> list = resHistoryMapper.getMonthPublishFile(hisMap);
        return list;
    }

    @Override
    public Integer publishFileNumByUserAndApproveStatus(HashMap hisMap) {
        Integer num = resHistoryMapper.publishFileNum(hisMap);
        return num;
    }

    @Override
    public List<ResHistory> changeFileByApproveStatus(HashMap hisMap) {
        List<ResHistory> list = resHistoryMapper.getMonthChangeFile(hisMap);
        return list;
    }

    @Override
    public Integer changeFileNumByUserAndApproveStatus(HashMap hisMap) {
        Integer num = resHistoryMapper.changeFileVersionNum(hisMap);
        return num;
    }

    @Override
    public List<ResHistory> approveFileByUserAndApproveStatus(Integer userID, String approveStatus, String timeBegin, String timeEnd, Integer fromUser) {
        HashMap<String, Object> hisMap = new HashMap<>();
        hisMap.put("userID", userID);
        hisMap.put("approveStatus", approveStatus);
        hisMap.put("timeBegin", timeBegin);
        hisMap.put("timeEnd", timeEnd);
        List<ResHistory> list = null;
        if(fromUser != null){
            hisMap.put("creator", fromUser);
            list = resHistoryMapper.getMonthApproveFile(hisMap);
            HashMap<String, Object> hisMapUpdator = new HashMap<>();
            hisMapUpdator.put("userID", userID);
            hisMapUpdator.put("approveStatus", approveStatus);
            hisMapUpdator.put("timeBegin", timeBegin);
            hisMapUpdator.put("timeEnd", timeEnd);
            hisMapUpdator.put("updator",fromUser);
            List<ResHistory> listUpdator = resHistoryMapper.getMonthApproveFile(hisMapUpdator);
            list.addAll(listUpdator);
        } else {
            list = resHistoryMapper.getMonthApproveFile(hisMap);
        }
        return list;
    }

    @Override
    public Integer approveFileNum(Integer userID, String approveStatus, Integer fromUser, String timeBegin) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("userID", userID);
        map.put("approveStatus", approveStatus);
        map.put("timeBegin", timeBegin);
        Integer approveFileNum = 0;
        if(fromUser != null){
            map.put("creator", fromUser);
            approveFileNum = resHistoryMapper.getApproveFileByUserNum(map);
            HashMap<String, Object> mapUpdator = new HashMap<>();
            mapUpdator.put("userID", userID);
            mapUpdator.put("approveStatus", approveStatus);
            mapUpdator.put("timeBegin", timeBegin);
            mapUpdator.put("updator", fromUser);
            Integer approveFileNumUpdator = resHistoryMapper.getApproveFileByUserNum(mapUpdator);
            approveFileNum = approveFileNum + approveFileNumUpdator;
        } else {
            approveFileNum = resHistoryMapper.getApproveFileByUserNum(map);
        }
        return approveFileNum;
    }

    @Override
    public List<ResHistory> monthFinalApproveFileByUserAndApproveStatus(Integer userID, String approveStatus, String timeBegin, String timeEnd, Integer oid, Integer fromUser) {
        HashMap<String, Object> hisMap = new HashMap<>();
        hisMap.put("userID", userID);
        hisMap.put("approveStatus", approveStatus);
        hisMap.put("timeBegin", timeBegin);
        hisMap.put("timeEnd", timeEnd);
        hisMap.put("toMid", "rb");
        hisMap.put("fromOrg", oid);
        List<ResHistory> list = null;
        if(fromUser != null){
            hisMap.put("creator", fromUser);
            list = resHistoryMapper.getMonthFinalFile(hisMap);
            HashMap<String, Object> hisMapUpdator = new HashMap<>();
            hisMapUpdator.put("userID", userID);
            hisMapUpdator.put("approveStatus", approveStatus);
            hisMapUpdator.put("timeBegin", timeBegin);
            hisMapUpdator.put("timeEnd", timeEnd);
            hisMapUpdator.put("toMid", "rb");
            hisMapUpdator.put("fromOrg", oid);
            hisMapUpdator.put("updator", fromUser);
            List<ResHistory> listUpdator = resHistoryMapper.getMonthFinalFile(hisMapUpdator);
            list.addAll(listUpdator);
        } else {
            list = resHistoryMapper.getMonthFinalFile(hisMap);
        }
        return list;
    }

    @Override
    public Integer finalApproveFileNumByFind(Integer userID, String approveStatus, String toMid, Integer oid, Integer fromUser, String timeBegin) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("userID", userID);
        map.put("approveStatus", approveStatus);
        map.put("toMid", toMid);
        map.put("fromOrg", oid);
        map.put("timeBegin", timeBegin);
        Integer finalApproveFileNum = 0;
        if(fromUser != null){
            map.put("creator", fromUser);
            finalApproveFileNum = resHistoryMapper.getFinalFileByUserNum(map);
            HashMap<String, Object> mapUpdator = new HashMap<>();
            mapUpdator.put("userID", userID);
            mapUpdator.put("approveStatus", approveStatus);
            mapUpdator.put("toMid", "rb");
            mapUpdator.put("fromOrg", oid);
            mapUpdator.put("timeBegin", timeBegin);
            mapUpdator.put("updator", fromUser);
            Integer finalApproveFileNumUpdator = resHistoryMapper.getFinalFileByUserNum(mapUpdator);
            finalApproveFileNum = finalApproveFileNum + finalApproveFileNumUpdator;
        } else {
            finalApproveFileNum = resHistoryMapper.getFinalFileByUserNum(map);
        }
        return finalApproveFileNum;
    }

    @Override
    public Integer finalApproveFileNum(Integer userID, String approveStatus, String toMid, Integer oid, Integer fromUser, String timeBegin) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("userID", userID);
        map.put("approveStatus", approveStatus);
        map.put("toMid", toMid);
        map.put("fromOrg", oid);
        if(fromUser != null){
            map.put("fromUser", fromUser);
        }
        if (timeBegin != null) {
            map.put("timeBegin", timeBegin);
        }
        Integer finalApproveFileNum = resHistoryMapper.getFinalFileNum(map);
        return finalApproveFileNum;
    }

    @Override
    public Integer needApproveFileNum(Integer userID, String approveStatus) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("userID", userID);
        map.put("approveStatus", approveStatus);
        Integer num = resHistoryMapper.getNeedApproveFile(map);
        return num;
    }

    //循环得到某文件夹的全部父级文件夹（如“图书/历史图书/中国历史”这种字样）
    @Override
    public String allParentCategoryName(Integer parent, String categoryName, String path, String type){
        while (this.getCategory(parent) != null){
            ResCategory category = getCategory(parent);
            if("1".equals(type)){
                categoryName = category.getName() + "/" + categoryName;
            } else {
                path = category.getId()+ "," + path;
            }
            parent = category.getParent();
        }
        if("1".equals(type)){
            return  categoryName;
        }else {
            return  path;
        }
    }

    //判断文件编号是否重复
    private Integer checkFileSn(Integer oid, String fileSn){
        List<ResCategory> listCategory = listCategory = resCategoryMapper.getAllCategory(oid);
        ArrayList list = new ArrayList(listCategory.size());
        for (ResCategory r : listCategory) {
            list.add(r.getId());
        }
        HashMap map = new HashMap();
        map.put("list", list);
        map.put("fileSn", fileSn);
        Integer checkFileSn = resMapper.checckFileSnByFile(map);
        if (checkFileSn.equals(0)) {
            checkFileSn = resHistoryMapper.checckFileSnByHistory(map);
        }
        return checkFileSn;
    }

    //新增移动历史
    @Override
    public Integer addHisMoveFile(Integer fileId, Integer category, String categoryName, String version, String categoryPath){
        ResHistory rhMove = new ResHistory();
        rhMove.setFile(fileId);
        rhMove.setCategory(category);
        rhMove.setCategoryPath(categoryPath);
        rhMove.setOperation("5");
        rhMove.setCategoryName(categoryName);
        rhMove.setVersion(version);
        Integer approveState = resHistoryMapper.insert(rhMove);
        return approveState;
    }

    //新增名字历史
    @Override
    public Integer addHisUpNameFile(Integer fileId, Integer category, String name, String version){
        ResHistory rhUpName = new ResHistory();
        rhUpName.setFile(fileId);
        rhUpName.setCategory(category);
        rhUpName.setName(name);
        rhUpName.setOperation("6");
        rhUpName.setVersion(version);
        Integer approveState = resHistoryMapper.insert(rhUpName);
        return approveState;
    }

    //新增编号历史
    @Override
    public Integer addHisUpFileSn(Integer fileId, Integer category, String FileSn, String version){
        ResHistory rhUpFileSn = new ResHistory();
        rhUpFileSn.setFile(fileId);
        rhUpFileSn.setCategory(category);
        rhUpFileSn.setFileSn(FileSn);
        rhUpFileSn.setOperation("7");
        rhUpFileSn.setVersion(version);
        Integer approveState = resHistoryMapper.insert(rhUpFileSn);
        return approveState;
    }

    //获取全部的文件id，并放到一个list中
    private List<String> getAllFile(Integer oid){
        List<ResCategory> listAllCategory = resCategoryMapper.getAllCategoryByOidAndType(oid);
        List list = new ArrayList();
        if (!listAllCategory.isEmpty()) {
            for (ResCategory c : listAllCategory) {
                list.add(c.getId());
            }
        }
        HashMap<String, Object> mapCategory = new HashMap<>();
        mapCategory.put("list", list);
        List<ResEntity> listAllFile = resMapper.getAllFileByListCategory(mapCategory);
        List listFile = new ArrayList();
        if (!listAllFile.isEmpty()) {
            for(ResEntity r : listAllFile){
                listFile.add(r.getId());
            }
        }
        return listFile;
    }

    //填入user的信息返回
    private PostUserMussage writeUserMessage(Integer userId){
        User user = userService.getUserByID(userId);
        PostUserMussage p = new PostUserMussage();
        p.setUserID(user.getUserID());
        p.setUserName(user.getUserName());
        p.setDepartName(user.getDepartName());
        p.setMobile(user.getMobile());
        p.setImgPath(user.getImgPath());
        p.setIsDuty(user.getIsDuty());
        return p;
    }

    //向审批流程表中存入数据
    private void addApprovalProcessForResource(Integer hisId, User user, Integer approveId, Integer level, String approveName, String createDate, String toMid, Integer businessType){
        ApprovalProcess ap = new ApprovalProcess();
        ap.setApproveStatus("1");
        ap.setBusiness(hisId);
        ap.setBusinessType(businessType);
        ap.setLevel(level);
        ap.setOrg(user.getOid());
        ap.setFromUser(user.getUserID());
        ap.setUserName(user.getUserName());
        if (toMid == "") {
            ap.setToUser(approveId);
            ap.setToUserName(approveName);
        } else {
            ap.setToMid(toMid);
        }
        ap.setCreateDate(NewDateUtils.dateFromString(createDate,"yyyy-MM-dd HH:mm:ss"));
        approvalProcessDao.save(ap);
    }

    @Override
    public ApprovalProcess updateApprovalProcessForResource(ApprovalProcess approvalProcess){
        ApprovalProcess ap = approvalProcessService.getApprovalProcessById(approvalProcess.getId());
        ap.setApproveStatus(approvalProcess.getApproveStatus());
        ap.setHandleTime(approvalProcess.getHandleTime());
        if(approvalProcess.getReason() != null){
            ap.setReason(approvalProcess.getReason());
        }
        if(approvalProcess.getToUser() != null){
            ap.setToUser(approvalProcess.getToUser());
        }
        if(approvalProcess.getToUserName() != null){
            ap.setToUserName(approvalProcess.getToUserName());
        }
        approvalProcessService.saveApprovalProcess(ap);
        return ap;
    }

    @Override
    public ResHistory gethisSingle(ResHistory resHistory) {
        ResHistory resHis = resHistoryMapper.getSingle(resHistory);
        return resHis;
    }

    @Override
    public ResHistory updateResHisSingle(ResHistory resHistory) {
        Integer approveState = resHistoryMapper.update(resHistory);
        ResHistory resHis = resHistoryMapper.getSingle(resHistory);
        return  resHis;
    }

    @Override
    public ResEntity insertFileSingle(ResEntity r, User user, String module) {
        int approveState = resMapper.insert(r);
        ResUsing callback = new ResUsing(r.getId(), r.getClass());
        uploadService.addFileUsing(callback, r.getPath(), r.getName(), user, module);
        return r;
    }

    @Override
    public void updateFileSingle(ResEntity r) {
        int upFileState = resMapper.update(r);
    }

    @Override
    public void insertFileAuthByCategoryAuth(ResEntity r) {
        Integer insertAuth = resAclMapper.insertAuthByCategoryAuth(r);
    }

    //资源中心发送新的消息形式
    @Override
    public UserSuspendMsg sendNewMessageByResource(Integer userID, String type, Integer changeNum,String name, Integer hisId, String date, String bussiness){
        UserSuspendMsg userSuspendMsg = new UserSuspendMsg();
        if(changeNum == 0){
            if(type.equals("1")){
                userSuspendMsg.setContent("您申请发布的文件《" + name + "》被批准了");
                userSuspendMsg.setMemo("批准时间 "+ date);
            } else if (type.equals("2")) {
                userSuspendMsg.setContent("您申请换版的文件《" + name + "》被驳回了");
                userSuspendMsg.setMemo("驳回时间 "+ date);
            }else if (type.equals("3")) {
                userSuspendMsg.setContent("您提交的《" + name + "》文件的终止申请被批准了！");
                userSuspendMsg.setMemo("批准时间 "+ date);
            }else if (type.equals("4")) {
                userSuspendMsg.setContent("您提交的《" + name + "》文件的终止申请被驳回了！");
                userSuspendMsg.setMemo("驳回时间 "+ date);
            }
        }else{
            if(type.equals("1")){
                userSuspendMsg.setContent("您申请换版的文件《" + name + "》被批准了");
                userSuspendMsg.setMemo("批准时间 "+ date);
            } else if (type.equals("2")) {
                userSuspendMsg.setContent("您申请换版的文件《" + name + "》被驳回了");
                userSuspendMsg.setMemo("驳回时间 "+ date);
            }else if (type.equals("3")) {
                userSuspendMsg.setContent("您提交的《" + name + "》文件的终止申请被批准了！");
                userSuspendMsg.setMemo("批准时间 "+ date);
            }else if (type.equals("4")) {
                userSuspendMsg.setContent("您提交的《" + name + "》文件的终止申请被驳回了！");
                userSuspendMsg.setMemo("驳回时间 "+ date);
            }
        }
        userSuspendMsgService.saveUserSuspendMsg(1, userSuspendMsg.getContent(), userSuspendMsg.getMemo(), userID, bussiness, hisId);
        return userSuspendMsg;
    }

    /**
     * 给资源中心发送新增的文件
     * @param fileId  文件id
     * @param user  登录人
     * @param type  发送类型 1上传文件 2是换版文件 3是自己传的人
     * @param operationTag 文件增减类型 1是新增文件 2是删除文件
     * @param userIDs  type为3时传过来的发送人
     * @param AuthType  判断发送人员是否包括高管1是包括2是不包括
     */
    @Override
    public void sendFile(Integer fileId, User user, String type, String operationTag, List<Integer> userIDs, String AuthType, ResEntity res){
        if (res == null) {
            ResEntity r = new ResEntity();
            r.setId(fileId);
            res = resMapper.getSingle(r);
        }
        res.setOperationTag(operationTag);
        HashMap<String,Object> resMap =new HashMap<>();
        resMap.put("res", res);
        if ("1".equals(type)) {
            userIDs = new ArrayList<Integer>();
            List<ResCategoryAcl> listCategoryAuth = resCategoryAclMapper.getAllAuthByCategory(res.getCategory());
            for (ResCategoryAcl rl : listCategoryAuth) {
                Integer rlId = rl.getUser();
                userIDs.add(rlId);
            }
        } else if ("2".equals(type)) {
            userIDs = resAclMapper.getAllUserIdsByFileId(res.getId());
        }
        if ("1".equals(AuthType)) {
            User userSuper = userService.getUserByRoleCode(user.getOid(), "super");
            User userSmallSuper = userService.getUserByRoleCode(user.getOid(),"smallSuper");
            User userManager = userService.getUserByRoleCode(user.getOid(), "general");
            Integer userManagerId = userManager.getUserID();
            Integer minorAffairsId = userService.getSmallManageByMCode(user.getOid(), "general");
            userIDs.add(userSuper.getUserID());
            userIDs.add(userManagerId);
            if (userSmallSuper != null) {
                userIDs.add(userSmallSuper.getUserID());
            }
            if (!minorAffairsId.equals(0)) {
                userIDs.add(minorAffairsId);
            }
        }
        HashSet<Integer> userSet = new HashSet<>(userIDs);
        userIDs.clear();
        userIDs.addAll(userSet);
        for (Integer u : userIDs) {
            clusterMessageSendingOperations.convertAndSendToUser(u.toString(),"/resCentreAddFile",null,null,user.getOid(),user.getOrganization().getName(),JSON.toJSONString(resMap));
        }
    }

    //给资源中心发送新增的文件夹
    @Override
    public void sendCategory(ResCategory category, List<Integer> userIDs, User user, String type, String operationTag){
        HashMap<String,Object> resMap =new HashMap<>();
        category.setOperationTag(operationTag);
        resMap.put("category",category);
        if ("1".equals(type)) {
            User userSuper = userService.getUserByRoleCode(user.getOid(), "super");
            User userManager = userService.getUserByRoleCode(user.getOid(), "general");
            Integer userManagerId = userManager.getUserID();
            Integer minorAffairsId = userService.getSmallManageByMCode(user.getOid(), "general");
            userIDs.add(userSuper.getUserID());
            userIDs.add(userManagerId);
            if (!minorAffairsId.equals(0)) {
                userIDs.add(minorAffairsId);
            }
        }
        HashSet<Integer> userSet = new HashSet<>(userIDs);
        userIDs.clear();
        userIDs.addAll(userSet);
        for (Integer u : userIDs) {
            clusterMessageSendingOperations.convertAndSendToUser(u.toString(),"/resCentreAddFile",null,null,user.getOid(),user.getOrganization().getName(),JSON.toJSONString(resMap));   //给资源中心发送文件夹
        }
    }

   /* //给我的消息发送要新增消息
    @Override
    public void sendMes(UserSuspendMsg userSuspendMsg, ApprovalProcess ap, String userId){
        HashMap<String, Object> mapByTurnDown = new HashMap<>();
        mapByTurnDown.put("userSuspendMsg",userSuspendMsg);
        mapByTurnDown.put("ap", ap);
        User user = userService.getUserByID(Integer.parseInt(userId));
        swMessageService.rejectSend(1,1,mapByTurnDown,userId,"/mesageForfile",null,null,user,"message");
    }*/

    //文件批准或驳回时同时发给多个审批人的消息
    @Override
    public void sendApprovalsFile(Integer hisId, HashMap mapHis, Integer processId){
        List<ApprovalProcess> listAp = approvalProcessService.getApprovalProcessByBusiness(hisId, 14, "");
        String toMid= listAp.get(listAp.size()-1).getToMid();
        if("rb".equals(toMid)){
            listAp.remove(listAp.size()-1);
        }
        if (!listAp.isEmpty()) {
            for(ApprovalProcess ap : listAp){
                if (!ap.getId().equals(processId)){
                    User user = userService.getUserByID(ap.getToUser());
                    swMessageService.rejectSend(0,-1,mapHis,ap.getToUser().toString(),"/approvalsFile",null,null,user,"fileApproval");
                }
            }
        }
    }

    @Override
    public void sendSearchByFile(ResEntity res, User user, Integer id){
        if (res == null) {
            res = this.getSingle(id);
        }
        HashMap<String,Object> resMap =new HashMap<>();
        resMap.put("res", res);
        clusterMessageSendingOperations.convertAndSendToUser(res.getCategory().toString(),"/sendSearchFile",null,null,user.getOid(),user.getOrganization().getName(),JSON.toJSONString(resMap));   //给资源中心发
    }

    @Override
    public Integer checkResAuth(Integer oid) {
        List<ResCategory> listFirstCategory = resCategoryMapper.getOrdinaryFirstCategory(oid);     //获取第一层全部文件夹不包括回收站
        Integer status = 0;
        Integer haveStatus = 0;
        Integer noHaveStatus = 0;
        if (!listFirstCategory.isEmpty()) {
            for (ResCategory r : listFirstCategory) {
                Integer authIds = resCategoryAclMapper.countAuth(r.getId());
                if (authIds > 0) {
                    status = 1;
                    break;
                }
            }
        }
        return status;
    }

    @Override
    public Integer handleOrGetBorrowedFile(ResEntity resEntity, String type, User user) {
        Integer state = 1;
        HashMap<String, Object> param = new HashMap<>();
        param.put("file", resEntity.getId());
        Date timeBegin = NewDateUtils.changeDay(new Date(), -29);
        param.put("timeBegin", timeBegin);
        param.put("isTrash", false);
        List<ReadingRoomEntity> list = readingRoomMapper.getReadRoomByFileId(param);
        if (!list.isEmpty()) {
            if ("1".equals(type)) {
                state = 3;
            } else {
                List<Integer> listUser = new ArrayList<>();
                Set<String> set = new HashSet<>();
                for (ReadingRoomEntity room : list) {
                    if ("1".equals(room.getApproveStatus())) {
                        List<ApprovalProcess> listAp = approvalProcessService.getApprovalProcessByBusiness(room.getId(), 29, "");
                        ApprovalProcess ap = listAp.get(listAp.size()-1);
                        if (ap.getToMid() != null) {
                            readRoomService.handleBorrowFile(user,room.getId(),ap.getId(),"3",null,null,null,"3");
                        } else {
                            User handlerUser = userService.getUserByID(ap.getToUser());
                            readRoomService.handleBorrowFile(handlerUser,room.getId(),ap.getId(),"3",null,null,null,"2");
                        }
                    } else {
                        if(set.add(room.getCreator().toString())){
                            listUser.add(room.getCreator());
                        }
                    }
                }
                if (!listUser.isEmpty()) {
                    for (Integer u : listUser) {
                        String taskbar = "总务人员" +user.getUserName()+ "已改变了" + resEntity.getFileSn() +"的保存位置,故您所借阅的有关文件已无法继续查看。";
                        userSuspendMsgService.saveUserSuspendMsg(1, taskbar, "操作时间 "+user.getUserName()+" "+NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"), u, null, null);
                    }
                }
            }
        }
        return state;
    }

    @Override
    public Integer handleOrGetBorrowedFileHistory(Integer fileHisId, String type, User user) {
        ResHistory rh = new ResHistory();
        rh.setId(fileHisId);
        ResHistory resHistory = resHistoryMapper.getSingle(rh);
        Integer state = 1;
        if (resHistory.getEnabled()) {
            List<ReadingRoomEntity> list = readingRoomMapper.getReadRoomByHisFileId(fileHisId);
            if (!list.isEmpty()) {
                if ("1".equals(type)) {
                    state = 3;
                } else {
                    List<Integer> listUser = new ArrayList<>();
                    Set<String> set = new HashSet<>();
                    for (ReadingRoomEntity room : list) {
                        if ("1".equals(room.getApproveStatus())) {
                            List<ApprovalProcess> listAp = approvalProcessService.getApprovalProcessByBusiness(room.getId(), 29, "");
                            ApprovalProcess ap = listAp.get(listAp.size()-1);
                            if (ap.getToMid() != null) {
                                readRoomService.handleBorrowFile(user,room.getId(),ap.getId(),"3",null,null,null,"3");
                            } else {
                                User handlerUser = userService.getUserByID(ap.getToUser());
                                readRoomService.handleBorrowFile(handlerUser,room.getId(),ap.getId(),"3",null,null,null,"2");
                            }
                        } else {
                            if(set.add(room.getCreator().toString())){
                                listUser.add(room.getCreator());
                            }
                        }
                    }
                    if (!listUser.isEmpty()) {
                        for (Integer u : listUser) {
                            String taskbar = resHistory.getFileSn() +"已被总务人员" + user.getUserName() +  "禁用,故您所借阅的该文件已无法继续查看！";
                            userSuspendMsgService.saveUserSuspendMsg(1, taskbar, "操作时间 "+user.getUserName()+" "+NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"), u, null, null);
                        }
                    }
                }
            }
        } else {
            state = 2;
        }
        return state;
    }

    @Override
    public HashMap<String, Object> getReadroomUserRecordByFileid(User user, Integer fileId, Integer fileHisId) {
        HashMap<String, Object> map = new HashMap<>();
        List<ReadingRoomEntity> list = null;
        if (fileId != null) {
            ResEntity res = this.getSingle(fileId);
            map.put("res", res);
            HashMap<String, Object> param = new HashMap<>();
            param.put("file", fileId);
            Date timeBegin = NewDateUtils.changeDay(new Date(), -29);
            param.put("timeBegin", timeBegin);
            param.put("isTrash", false);
            list = readingRoomMapper.getReadRoomByFileId(param);
        }
        if (fileHisId != null) {
            ResHistory rh = new ResHistory();
            rh.setId(fileHisId);
            ResHistory resHistory = resHistoryMapper.getSingle(rh);
            map.put("resHistory", resHistory);
            list = readingRoomMapper.getReadRoomByHisFileId(fileHisId);
        }
        List<PostUserMussage> listUserDto = new ArrayList<>();
        if (!list.isEmpty()) {
            List<Integer> listUser = new ArrayList<>();
            HashSet<String> set = new HashSet<>();
            for (ReadingRoomEntity room : list) {
                if(set.add(room.getCreator().toString())){
                    listUser.add(room.getCreator());
                }
            }
            if (!listUser.isEmpty()) {
                for (Integer u : listUser) {
                    User userReading = userService.getUserByID(u);
                    listUserDto.add(forumService.backUser(userReading));
                }
            }
        }
        map.put("listUserDto", listUserDto);
        return map;
    }

    @Override
    public Integer restoreFile(User user, Integer fileId) {
        ResEntity res = this.getSingle(fileId);
        ResCategory cat = resCategoryService.getSingle(res.getCategory());
        Integer state = 1;
        if (cat.getParent() != null) {
            state = 2;
        }else {
            ResHistory rh = new ResHistory();
            rh.setFile(fileId);
            List<ResHistory> list = resHistoryMapper.getMoveRecordDesc(rh);
            Integer num = 0;
            ResHistory beforeMoveHisVersion = null;
            for (ResHistory r : list) {
                if(r.getIsTrash() != null){
                    if (!(Byte.valueOf("1").equals(r.getIsTrash()))) {
                        beforeMoveHisVersion = r;
                        break;
                    }
                }else {
                    beforeMoveHisVersion = r;
                    break;
                }
            }
            ResCategory beforeMoveHisCategory = resCategoryService.getSingle(beforeMoveHisVersion.getCategory());
            if (beforeMoveHisCategory.getValid()) {
                if (Byte.valueOf("1").equals(beforeMoveHisCategory.getIsTrash())) {
                    state = 2;
                } else {
                    this.removeStatusByCentre(fileId, beforeMoveHisCategory.getId(), user, res, null,"2");
                }
            } else {
                state = 4;
            }
        }
        return state;
    }

    @Override
    public Integer updateFileValid(User user, Integer fileId) {
        ResEntity res = this.getSingle(fileId);
        ResHistory rh = new ResHistory();
        rh.setFile(fileId);
        rh.setIsTrash(Byte.valueOf("1"));
        List<ResHistory> list = resHistoryMapper.getMoveRecordDesc(rh);
        Date fiveDaysAgo = NewDateUtils.changeDay(new Date(),-4);
        Integer state = 1;
        String createDate = list.get(0).getCreateDate();
        Date date = NewDateUtils.dateFromString(createDate,"yyyy-MM-dd HH:mm:ss");
        if (date.getTime() < fiveDaysAgo.getTime()) {
            ResHistory rhMove = new ResHistory();
            rhMove.setFile(fileId);
            rhMove.setCategory(0);
            Date now = new Date();
            rhMove.setCreateName(user.getUserName());
            rhMove.setCreateDate(NewDateUtils.dateToString(now, "yyyy-MM-dd HH:mm:ss"));
            rhMove.setVersion(res.getVersion());
            rhMove.setOperation("5");
            rhMove.setIsTrash(Byte.valueOf("1"));
            rhMove.setCategoryName("即将消失的文件");
            ResEntity r = new ResEntity();
            r.setId(fileId);
            r.setMoveNum(res.getMoveNum()+1);
            r.setValid(0);
            r.setIsTrash(Byte.valueOf("1"));
            r.setDeleteTime(now);
            Integer intFileMoveRecordHis = resHistoryMapper.insert(rhMove);
            resMapper.update(r);
            HashMap<String, Object> map = new HashMap<>();
            Date fileValidTime = NewDateUtils.joinDateTimeString(NewDateUtils.changeDay(now, 4), "00:00") ;
            res.setFileValidTime("将于"+ TimeUtils.toTimeString(fileValidTime.getTime()-now.getTime(),3) +"钟后消失");
            map.put("res",res);
            String noticeMessage = res.getFileSn()+"文件即将消失";
            User userManager = userService.getUserByRoleCode(user.getOid(), "general");
            swMessageService.rejectSend(1,1,map,userManager.getUserID().toString(),"/soonDisappearFile", "有一条申请待审批", noticeMessage,userManager,"soonDisappearFile");   //发给总务
            Integer minorAffairsId = userService.getSmallManageByMCode(user.getOid(), "general");
            if (minorAffairsId != 0){
                User userSmallManager = userService.getUserByID(minorAffairsId);
                swMessageService.rejectSend(1,1,map,userSmallManager.getUserID().toString(),"/soonDisappearFile", "有一条申请待审批", noticeMessage,userSmallManager,"soonDisappearFile");   //发给小总务
            }
            User superUser = userService.getUserByRoleCode(user.getOid(),"super");
            swMessageService.rejectSend(1,1,map,superUser.getUserID().toString(),"/soonDisappearFile", "有一条申请待审批", noticeMessage,superUser,"soonDisappearFile");   //发给超管
            this.sendFile(fileId,user,"2","2",null,"1",res);           //删除文件
        }else {
            state = 2;
        }
        return state;
    }

    @Override
    public List<ResEntity> getAboutToDisapperFile(User user) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("org", user.getOid());
        List<ResEntity> list = resMapper.getFileByValid(map);
        if (!list.isEmpty()) {
            PageInfo pageInfo = new PageInfo();
            pageInfo.setPageSize(1);
            QueryData query = new QueryData();
            query.put("pageInfo", pageInfo);
            for (ResEntity r : list) {
                query.put("file", r.getId());
                ResHistory rh = resHistoryMapper.getLastMoveRecordlistPage(query);
                Date delDate = NewDateUtils.dateFromString(rh.getCreateDate(),"yyyy-MM-dd HH:mm:ss");
                Date threeDayAfter = NewDateUtils.joinDateTimeString(NewDateUtils.changeDay(delDate, 4), "00:00");
                Date now = new Date();
                r.setFileValidTime("将于"+ TimeUtils.toTimeString(threeDayAfter.getTime()-now.getTime(),3) +"钟后消失");
            }
        }
        return list;
    }

    @Override
    public HashMap<String, Object> getAboutToDisapperFileMessage(Integer fileId) {
        ResEntity res = this.getSingle(fileId);
        ResHistory reshis = new ResHistory();
        reshis.setFile(fileId);
        List<ResHistory> listMoveRecord = resHistoryMapper.getMoveRecordDesc(reshis);
        List<ResHistory> listTwoRecord = new ArrayList<>();
        ResHistory lastHis = listMoveRecord.get(0);
        listTwoRecord.add(listMoveRecord.get(1));
        listTwoRecord.add(lastHis);
        Date delDate = NewDateUtils.dateFromString(lastHis.getCreateDate(),"yyyy-MM-dd HH:mm:ss");
        Date threeDayAfter = NewDateUtils.joinDateTimeString(NewDateUtils.changeDay(delDate, 4), "00:00");
        Date now = new Date();
        res.setFileValidTime("将于"+ TimeUtils.toTimeString(threeDayAfter.getTime()-now.getTime(),3) +"钟后消失");
        HashMap<String, Object> map = new HashMap<>();
        map.put("res", res);
        map.put("listTwoRecord", listTwoRecord);
        return map;
    }

    @Override
    public Integer restoreDelFile(User user, Integer fileId) {
        ResEntity res = this.getSingle(fileId);
        ResCategory cat = resCategoryService.getSingle(res.getCategory());
        Integer state = 0;
        if (cat != null) {
            state = 1;
            ResHistory rhMove = new ResHistory();
            rhMove.setFile(fileId);
            rhMove.setCategory(res.getCategory());
            rhMove.setCreateName(user.getUserName());
            rhMove.setCreateDate(NewDateUtils.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss"));
            rhMove.setVersion(res.getVersion());
            rhMove.setOperation("5");
            rhMove.setIsTrash(Byte.valueOf("1"));
            String allCategoryName = this.allParentCategoryName(cat.getParent(), cat.getName(), null, "1");
            rhMove.setCategoryName(allCategoryName);
            resHistoryMapper.insert(rhMove);
            ResEntity r = new ResEntity();
            r.setId(fileId);
            r.setMoveNum(res.getMoveNum()+1);
            r.setValid(1);
            r.setIsTrash(Byte.valueOf("1"));
            resMapper.update(r);
            HashMap<String, Object> map = new HashMap<>();
            map.put("res", res);
            User userManager = userService.getUserByRoleCode(user.getOid(), "general");
            swMessageService.rejectSend(-1,-1,map,userManager.getUserID().toString(),"/soonDisappearFile",null,null,userManager,"soonDisappearFile");     //发给总务
            Integer minorAffairsId = userService.getSmallManageByMCode(user.getOid(), "general");
            if (minorAffairsId != 0){
                User userSmallManager = userService.getUserByID(minorAffairsId);
                swMessageService.rejectSend(-1,-1,map,userSmallManager.getUserID().toString(),"/soonDisappearFile",null,null,userSmallManager,"soonDisappearFile");   //发给小总务
            }
            User superUser = userService.getUserByRoleCode(user.getOid(),"super");
            swMessageService.rejectSend(-1,-1,map,superUser.getUserID().toString(),"/soonDisappearFile",null,null,superUser,"soonDisappearFile");     //发给超管
            this.sendFile(fileId,user,"2","1",null,"1",null);           //新增文件
        }
        return state;
    }

    @Override
    public String getFileSize(User user, String categoryPath, String type) {
        Long size = null;
        HashMap<String,Object> map = new HashMap<>();
        if (categoryPath != null) {
            map.put("categoryPath", categoryPath);
        }else {
            map.put("org",user.getOid());
        }
        if ("1".equals(type)) {
            size = resMapper.getFileSize(map);
        }else {
            size = resHistoryMapper.getHisFileSize(map);
        }
        String usingSize = MyStrings.sizeConver(size);
        return usingSize;
    }

    @Override
    public Integer disablefileHistoryVersion(User user, Integer fileHisId) {
        ResHistory rh = new ResHistory();
        rh.setId(fileHisId);
        ResHistory resHistory = resHistoryMapper.getSingle(rh);
        Integer state = 1;
        if (resHistory.getEnabled()) {
            this.handleOrGetBorrowedFileHistory(fileHisId,"2",user);
            rh.setEnabled(false);
            rh.setEnabledTime(NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"));
            resHistoryMapper.update(rh);
        } else {
            state = 2;
        }
        return state;
    }

    @Override
    public void regularlyCleanedDisapperFile() {
        Date date = NewDateUtils.changeDay(new Date(), -3);
        HashMap<String, Object> param = new HashMap<>();
        param.put("deleteTime", date);
        List<ResEntity> list = resMapper.getFileByValid(param);
        if(!list.isEmpty()){
            User userManager = userService.getUserByRoleCode(list.get(0).getOrg(), "general");
            Integer minorAffairsId = userService.getSmallManageByMCode(list.get(0).getOrg(), "general");
            User userSmallManager = null;
            if (minorAffairsId != 0){
                userSmallManager = userService.getUserByID(minorAffairsId);
            }
            User superUser = userService.getUserByRoleCode(list.get(0).getOrg(),"super");
            for (ResEntity res : list) {
                forumService.changeResFileIntoAtt(res.getId(),res.getPath(),res.getName(),res.getVersion(),res.getSize().intValue());
                String oldFilename = res.getPath();
                ResUsing callback = new ResUsing(res.getId(), res.getClass());
                uploadService.delFileUsing(callback, oldFilename, userManager);         //删除主表的文件引用
                ResEntity r = new ResEntity();
                r.setId(res.getId());
                r.setIsDeleted(true);
                r.setIsTrash(Byte.valueOf("1"));
                r.setPath("");
                r.setSize(Long.valueOf(0));
                resMapper.update(r);
                PageInfo pageInfo = new PageInfo();
                pageInfo.setPageSize(3);
                pageInfo.setCurrentPageNo(1);
                this.batchDelHisFile(res.getId(),pageInfo,userManager);
                HashMap<String, Object> map = new HashMap<>();
                map.put("res",res);
                swMessageService.rejectSend(-1,-1,map,userManager.getUserID().toString(),"/soonDisappearFile",null,null,userManager,"soonDisappearFile");     //发给总务
                if (userSmallManager != null) {
                    swMessageService.rejectSend(-1,-1,map,userSmallManager.getUserID().toString(),"/soonDisappearFile",null,null,userSmallManager,"soonDisappearFile");   //发给小总务
                }
                swMessageService.rejectSend(-1,-1,map,superUser.getUserID().toString(),"/soonDisappearFile",null,null,superUser,"soonDisappearFile");     //发给超管
            }
        }
    }

    @Override
    public Integer getNewFileHisId(Integer id) {
        ResHistory resHistory = resHistoryMapper.getNewFileHistory(id);
        return resHistory.getId();
    }

    @Override
    public Integer updateResOperation(User user, Integer id) {
        ResEntity res = this.getSingle(id);
        Integer state = 1;
        if ("1".equals(res.getTeminateState()) || "3".equals(res.getOperation())) {
            state = 2;
        }else {
            res.setOperation("3");
            resMapper.update(res);
        }
        this.sendFile(id, user, "2", "1", null,"1",res);
        this.sendSearchByFile(res,user,id);
        return state;
    }

    @Override
    public Integer unlockFile(User user, Integer id) {
        ResEntity res = this.getSingle(id);
        Integer state = 1;
        if ("3".equals(res.getOperation())) {
            res.setOperation("0");
            resMapper.update(res);
        }
        this.sendFile(id, user, "2", "1", null,"1",res);
        this.sendSearchByFile(res,user,id);
        return state;
    }

    @Override
    public void updateResUuid(Integer fileId) {
        ResEntity r = new ResEntity();
        r.setId(fileId);
        String uu = UUID.randomUUID().toString();
        String uuid = uu.replaceAll("-", "");
        r.setLockUuid(uuid);
        resMapper.update(r);
    }

    @Override
    public Long getNoApproveFileNum(String batchUuid, String approveStatus) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("batchUuid", batchUuid);
        param.put("approveStatus", approveStatus);
        Long num = resHistoryMapper.getNotApproveHisFileNum(param);
        return num;
    }

    @Override
    public List<ResHistory> listHisFileByBatchUuid(String batchUuid) {
        List<ResHistory> list = resHistoryMapper.getListHisFileByBatchUuid(batchUuid);
        return list;
    }

    @Override
    public List<ResHistory> listHisFileByTemiante(Integer fileId) {
        List<ResHistory> list = resHistoryMapper.listHisFileByTemiante(fileId);
        if (list != null) {
            for (ResHistory rh : list) {
                String type = "4";
                if ("2".equals(rh.getTeminateState())) {
                    type = "5";
                }
                Integer signedNum = resNoticeService.resNoticeNumByType(null, rh.getId(), null, type, null);
                Integer haveSignedNum = resNoticeService.resNoticeNumByType(null, rh.getId(), null, type, "1");
                ResSignedMes resSignedMes = new ResSignedMes();
                resSignedMes.setSignedNum(signedNum);
                resSignedMes.setHaveSignedNum(haveSignedNum);
                resSignedMes.setResHisId(rh.getId());
                rh.setResSignedMes(resSignedMes );
            }
        }
        return list;
    }


    @Override
    public Integer reuseRes(User user, Integer id) {
        ResEntity res = this.getSingle(id);
        Integer state = 0;
        if ("1".equals(res.getTeminateState())) {
            ResEntity upRes = new ResEntity();
            upRes.setId(id);
            upRes.setUpdator(user.getUserID());
            upRes.setUpdateName(user.getUserName());
            upRes.setUpdateDate(NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss"));
            upRes.setAuditDate(upRes.getUpdateDate());
            state = resMapper.reuserFile(upRes);
            ResEntity resnew = this.getSingle(id);
            ResHistory resHistory = new ResHistory();
            resHistory.setFile(resnew.getId());
            resHistory.setCategory(resnew.getCategory());
            resHistory.setCategoryPath(resnew.getCategoryPath());
            resHistory.setType("2");
            resHistory.setEnabled(true);
            resHistory.setIsTrash(resnew.getIsTrash());
            resHistory.setIsDeleted(false);
            resHistory.setValid(1);
            resHistory.setCreator(resnew.getCreator());
            resHistory.setCreateName(resnew.getCreateName());
            resHistory.setCreateDate(resnew.getCreateDate());
            resHistory.setUpdator(user.getUserID());
            resHistory.setUpdateName(user.getUserName());
            resHistory.setUpdateDate(resnew.getUpdateDate());
            resHistory.setAuditDate(resnew.getUpdateDate());
            resHistory.setOperation("4");
            resHistory.setOrg(user.getOid());
            resHistory.setChangeNum(resnew.getChangeNum());
            resHistory.setContent(resnew.getContent());
            resHistory.setPath(resnew.getPath());
            resHistory.setSize(resnew.getSize());
            resHistory.setVersion(resnew.getVersion());
            resHistory.setName(resnew.getName());
            resHistory.setFileSn(resnew.getFileSn());
            resHistory.setTeminateState("2");
            resHistory.setApproveStatus("2");
            int stateHis = resHistoryMapper.insert(resHistory);
            ResUsing callback = new ResUsing(resHistory.getId(), resHistory.getClass());
            uploadService.addFileUsing(callback, resHistory.getPath(), resHistory.getName(), user, module);
            List<Integer> listUserIDs = resCategoryAclMapper.getAllUserIdsByCategory(resHistory.getCategory());
            resNoticeService.addSpecialAuthUserId(listUserIDs, user);
            resNoticeService.sendResFileNotice(user, null, null, null, resHistory.getId(),null, null, null, "5",listUserIDs,null,null);
            this.sendFile(id, user, "2", "1", null,"1",resnew);
            this.sendSearchByFile(resnew,user,id);
        }
        return state;
    }

    private void batchDelHisFile(Integer file, PageInfo pageInfo, User user){
        QueryData query = new QueryData();
        query.put("file",file);
        query.put("pageInfo", pageInfo);
        List<ResHistory> listAllHis = resHistoryMapper.getAllFileHislistPage(query);
        Integer record = pageInfo.getCurrentPageSize();
        for (ResHistory rh : listAllHis) {
            String oldFilename = rh.getPath();
            ResUsing callback = new ResUsing(rh.getId(), rh.getClass());
            uploadService.delFileUsing(callback, oldFilename, user);         //删除主表的文件引用
            ResHistory his = new ResHistory();
            his.setId(rh.getId());
            his.setIsDeleted(true);
            his.setValid(0);
            his.setPath("");
            his.setSize(Long.valueOf(0));
            resHistoryMapper.update(his);
            record--;
        }
        if(record == 0){
            int CurpageNo = pageInfo.getCurrentPageNo()+1;
            if (CurpageNo <= pageInfo.getTotalPage()) {
                PageInfo pageInfoNew = new PageInfo();
                pageInfoNew.setPageSize(pageInfo.getPageSize());
                pageInfoNew.setCurrentPageNo(CurpageNo);
                batchDelHisFile(file,pageInfoNew,user);
            }
        }
    }

    //获取某历史文件的审批流程，若是没有就给补上
    private List<ApprovalProcess> getListApForResource(ResHistory resHistory){
        //获取审批流程
        List<ApprovalProcess> listAp = approvalProcessService.getApprovalProcessByBusiness(resHistory.getId(), 14, "");
        if(listAp.isEmpty()){
            ApprovalProcess ap = new ApprovalProcess();
            ap.setHandleTime(NewDateUtils.dateFromString(resHistory.getAuditDate(),"yyyy-MM-dd HH:mm:ss"));
            if (resHistory.getAuditName() != null) {
                ap.setToUserName(resHistory.getAuditName());
                if(resHistory.getChangeNum() > 0){
                    ap.setUserName(resHistory.getUpdateName());
                    ap.setCreateDate(NewDateUtils.dateFromString(resHistory.getUpdateDate(),"yyyy-MM-dd HH:mm:ss"));
                } else {
                    ap.setUserName(resHistory.getCreateName());
                    ap.setCreateDate(NewDateUtils.dateFromString(resHistory.getCreateDate(),"yyyy-MM-dd HH:mm:ss"));
                }
            } else {
                if(resHistory.getChangeNum() > 0){
                    ap.setUserName(resHistory.getUpdateName());
                    ap.setToUserName(resHistory.getUpdateName());
                    ap.setCreateDate(NewDateUtils.dateFromString(resHistory.getUpdateDate(),"yyyy-MM-dd HH:mm:ss"));
                } else {
                    ap.setUserName(resHistory.getCreateName());
                    ap.setToUserName(resHistory.getCreateName());
                    ap.setCreateDate(NewDateUtils.dateFromString(resHistory.getCreateDate(),"yyyy-MM-dd HH:mm:ss"));
                }
            }
            listAp.add(ap);
        }
        return listAp;
    }

    @Override
    public Integer getBadgeNumber(User user, String code) {
        Integer number=null;
        switch (code){
            //资源中心部分
            case "releaseApply": //文件发布申请
                break;
            case "versionApply": //文件换版申请
                break;
            case "versionApproval": //文件换版审批
                if ("general".equals(user.getManagerCode())) {
                    number = this.finalApproveFileNum(user.getUserID(), "1", "rb", user.getOid(), null, null);
                }
                break;
            case "fileApproval"://文件审批
                number = this.needApproveFileNum(user.getUserID(), "1");
                break;
            case "soonDisappearFile":
                number = resMapper.getFileByValidNum(user.getOid());
                break;
        }
        return number;
    }

    //检测文件编号是否重复，并返回最新的文件编号
    private String checkFileNum(Integer oid, String fileSn, List<String> listFileSn){
        // List<String> listNum = new ArrayList<>();    //编号集合
        // listNum.addAll(listFileSn);
        List<ResCategory> listCategory = resCategoryMapper.getAllCategoryByOidAndType(oid);
        ArrayList list = new ArrayList(listCategory.size());
        for (ResCategory r : listCategory) {
            list.add(r.getId());
        }
        QueryData query = new QueryData();
        query.put("fileSn", fileSn);
        query.put("list", list);
        Integer resCheckFileSn = checkFileSn(oid, fileSn);
        Integer n = 1;
        while (!resCheckFileSn.equals(0)) {
            Integer c = fileSn.length();
            String b = fileSn.substring(fileSn.length()-1);
            if (fileSn.substring(fileSn.length()-1).equals(")")) {
                String index = fileSn.substring(fileSn.lastIndexOf("(")+1,fileSn.lastIndexOf(")"));
                int flag = 1;
                for(int i = 0; i < index.length(); i++){
                    char ch = index.charAt(i);
                    if (!Character.isDigit(ch)) {
                        flag = 0;
                        break;
                    }
                }
                if (flag == 1) {
                    n = Integer.valueOf(index)+1;
                    fileSn = fileSn.substring(0,fileSn.lastIndexOf("("))+ "(" + n + ")";
                }else {
                    fileSn = fileSn+ "(" + n + ")";
                }
            } else {
                fileSn = fileSn+ "(" + n + ")";
            }
            query.put("fileSn", fileSn);
            resCheckFileSn = checkFileSn(oid, fileSn);
        }
        return fileSn;
    }

}
