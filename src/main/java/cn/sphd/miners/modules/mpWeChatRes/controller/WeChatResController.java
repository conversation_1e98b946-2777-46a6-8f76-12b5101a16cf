package cn.sphd.miners.modules.mpWeChatRes.controller;

import cn.sphd.miners.common.initializer.AuthPassport;
import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.auth.service.AuthService;
import cn.sphd.miners.modules.ec.entity.EcOrder;
import cn.sphd.miners.modules.ec.service.EcService;
import cn.sphd.miners.modules.mpWeChatRes.entity.WxResource;
import cn.sphd.miners.modules.mpWeChatRes.entity.WxResourceCategory;
import cn.sphd.miners.modules.mpWeChatRes.entity.WxResourceCategoryHistory;
import cn.sphd.miners.modules.mpWeChatRes.entity.WxResourceHistory;
import cn.sphd.miners.modules.mpWeChatRes.service.WxResCatService;
import cn.sphd.miners.modules.mpWeChatRes.service.WxResService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.List;

@Controller
@RequestMapping("/mpRes")
public class WeChatResController {
    @Autowired
    WxResCatService wxResCatService;
    @Autowired
    WxResService wxResService;
    @Autowired
    UserService userService;
    @Autowired
    AuthService authService;
    @Autowired
    EcService ecService;

    //小程序模块获取初始目录
    @ResponseBody
    @RequestMapping("/getMiniProgramInitialDirectory.do")
    public JsonResult getMiniProgramInitialDirectory(User user){
        HashMap<String, Object> map = wxResCatService.mimiProgramInitialDirectory(user);
        return new JsonResult(1,map);
    }

    //小程序模块获取本级目录和子级目录
    @ResponseBody
    @RequestMapping("/getMiniProgramDircetionAndChild.do")
    public JsonResult getMiniProgramDircetionAndChild(Integer category){
        HashMap<String, Object> map = wxResCatService.minidirectionAndChild(category);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //小程序模块获取目录下文件
    @ResponseBody
    @RequestMapping("/getMiniProgramFile.do")
    public JsonResult getMiniProgramFile(Integer category, PageInfo pageInfo){
        HashMap<String, Object> map = wxResService.getWxFileByCategory(category,pageInfo);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //新增目录
    @ResponseBody
    @RequestMapping("/addMiniProgramDirectory.do")
    public JsonResult addMiniProgramDirectory(User user, Integer parent, String categoryName){
        HashMap<String, Object> map = wxResCatService.addWxDirectory(user,parent,categoryName);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //修改目录名称
    @ResponseBody
    @RequestMapping("/upMiniProgramDirectoryName.do")
    public JsonResult upMiniProgramDirectoryName(User user, Integer category, Integer parent, String name){
        HashMap<String, Object> map = wxResCatService.upWxCategoryName(user,category,parent,name);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //目录修改记录
    @ResponseBody
    @RequestMapping("/miniProgramDirectoryHistory.do")
    public JsonResult miniProgramDirectoryHistory(Integer category){
        List<WxResourceCategoryHistory> list = wxResCatService.listWxCategoryHistory(category);
        HashMap<String, Object> map = new HashMap<>();
        map.put("list",list);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //删除目录
    @ResponseBody
    @RequestMapping("/delMiniProgramDirectory.do")
    public JsonResult delMiniProgramDirectory(Integer category){
        Integer state = wxResCatService.delWxCategory(category);
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //上传文章
    @ResponseBody
    @RequestMapping("/minniProgramAffirdFile.do")
    public JsonResult minniProgramAffirdFile(User user, WxResource resource, String listResAtt){
        HashMap<String, Object> map = wxResService.wxAddFile(user, resource, listResAtt);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //修改文章
    @ResponseBody
    @RequestMapping("/minniProgramUpdateFile.do")
    public JsonResult minniProgramUpdateFile(User user, WxResource resource, String listResAtt){
        HashMap<String, Object> map = wxResService.wxUpFile(user, resource, listResAtt);
        JsonResult result = new JsonResult(1,map);
        return result;
    }

    //停用文章
    @ResponseBody
    @RequestMapping("/minniProgramStopFile.do")
    public JsonResult minniProgramStopFile(User user, Integer file){
        Integer state = wxResService.wxStopFile(user,file);
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        return new JsonResult(1,map);
    }

    //还原文章
    @ResponseBody
    @RequestMapping("/minniProgramFileRestore.do")
    public JsonResult minniProgramFileRestore(User user, Integer file){
        Integer state = wxResService.wxFileRestore(user,file);
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        return new JsonResult(1,map);
    }

    //获取文章操作记录
    @ResponseBody
    @RequestMapping("/minniProgramFileRecord.do")
    public JsonResult minniProgramFileRecord(Integer file, PageInfo pageInfo){
        List<WxResourceHistory> list = wxResService.listWxfileRecord(file,pageInfo);
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", list);
        map.put("pageInfo", pageInfo);
        return new JsonResult(1,map);
    }

    //获取某文章的详细信息
    @ResponseBody
    @RequestMapping("/minniProgramFileMessage.do")
    public JsonResult minniProgramFileMessage(Integer file){
        HashMap<String, Object> map = wxResService.getWxResMessage(file);
        return new JsonResult(1,map);
    }

    //获取文章某次历史的详细信息
    @ResponseBody
    @RequestMapping("/minniProgramHisFileMessage.do")
    public JsonResult minniProgramHisFileMessage(Integer hisFile){
        HashMap<String, Object> map = wxResService.getWxHisResMessage(hisFile);
        return new JsonResult(1,map);
    }

    //单独的初始化接口，应该是用不上
    @ResponseBody
    @RequestMapping("/initialMinniProgram.do")
    public JsonResult initialMinniProgram(Integer org, String appId, String appname, String appsecret, String token, String canpayStr, String module){
        Boolean canpay;
        if (canpayStr.equals("1")) {
            canpay = true;
        }else {
            canpay = false;
        }
        List<WxResourceCategory> list = wxResCatService.addInitialDirectory(org,appId,appname,appsecret,token,canpay,module);
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", list);
        return new JsonResult(1,map);
    }


    //小程序获取首页
    @AuthPassport(guest = true, tpMember = true)
    @ResponseBody
    @RequestMapping("/getWeChatHome.do")
    public JsonResult getWeChatHome(){
        HashMap<String, Object> map = wxResCatService.getWxHomePageMes();
        return new JsonResult(1,map);
    }

    //小程序获取子级目录
    @AuthPassport(guest = true, tpMember = true)
    @ResponseBody
    @RequestMapping("/getWeChatParentAndChildDir.do")
    public JsonResult getWeChatParentAndChildDir(String category){
        HashMap<String, Object> map = wxResCatService.getWxParentAndChild(Integer.valueOf(category));
        return new JsonResult(1,map);
    }

    //小程序获取某文件夹下文件
    @AuthPassport(guest = true, tpMember = true)
    @ResponseBody
    @RequestMapping("/getWeChatCategoryFile.do")
    public JsonResult getWeChatCategoryFile(Integer category, PageInfo pageInfo){
        HashMap<String, Object> map = wxResService.getWxFileByCategory(category,pageInfo);
        return new JsonResult(1,map);
    }

    //小程序获取文件信息
    @AuthPassport(guest = true, tpMember = true)
    @ResponseBody
    @RequestMapping("/getWeChatFileMes.do")
    public JsonResult getWeChatFileMes(Integer file){
        HashMap<String, Object> map = wxResService.getWxResMessage(file);
        return new JsonResult(1,map);
    }

    //小程序搜索
    @AuthPassport(guest = true, tpMember = true)
    @ResponseBody
    @RequestMapping("/weChatFindFile.do")
    public JsonResult weChatFindFile (String findName,PageInfo pageInfo){
        HashMap<String, Object> map = wxResService.findFile(findName,pageInfo);
        return new JsonResult(1,map);
    }

    //目录
    @RequestMapping("/wxResDirection.do")
    public String aboutDirection(User user, Model model){
        String generalType = "";
        if (userService.isSuper(user)) {
            generalType = "0";
        }else if (userService.isGeneral(user)){
            generalType = "1";
        }else if (userService.isGeneralSmallManager(user)){
            generalType = "2";
        }
        model.addAttribute("generalType", generalType);
        model.addAttribute("user",user);
        return "/resourceMiniApp/catalogue";
    }

    //内容管理
    @RequestMapping("/wxResContentManage.do")
    public String aboutContentManage(User user, Model model){
        String generalType = "";
        if (userService.isSuper(user)) {
            generalType = "0";
        }else if (userService.isGeneral(user)){
            generalType = "1";
        }else if (userService.isGeneralSmallManager(user)){
            generalType = "2";
        }
        model.addAttribute("generalType", generalType);
        model.addAttribute("user",user);
        return "/resourceMiniApp/file";
    }

}
