package cn.sphd.miners.modules.generalAffairs.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.car.service.DriverService;
import cn.sphd.miners.modules.system.dto.OrganizationDto;
import cn.sphd.miners.modules.system.dto.UserDto;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.entity.UserRole;
import cn.sphd.miners.modules.system.entity.UserRoleHistory;
import cn.sphd.miners.modules.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * Created by Administrator on 2018/1/16.
 * 分工设置（设置核心人物）
 */
@Controller
@RequestMapping("/coreSetting")
public class CoreSettingController {

    @Autowired
    UserService userService;
    @Autowired
    UserPopedomService userPopedomService;
    @Autowired
    OrgService orgService;
    @Autowired
    UserRoleService userRoleService;
    @Autowired
    PopedomTaskService popedomTaskService;
    @Autowired
    UserRoleHistoryService userRoleHistoryService;
    @Autowired
    DriverService driverService;
    @Autowired
    OrgPopedomService orgPopedomService;

   /**
   * <AUTHOR>
   * @Date 2018/1/16 18:32
   * 查询投诉管理当前核心人物
   */
    @ResponseBody
    @RequestMapping("/getCoreUser.do")
    public void getCoreUser(User user, HttpServletResponse response) throws IOException {
        User coreUser=userService.getUserByCoreCode(user.getOid(),"core");
        Map<String,Object> map=new HashMap<String,Object>();
        if (user!=null) {
            map.put("status",1);
            map.put("coreUser",coreUser);
        }else {
            map.put("status",0);
        }
        ObjectToJson.objectToJson1(map,new String[]{"personnelFolksHashSet","personnelInterviewHashSet","user","userPopedomHashSet","personnelOvertimeUser","personnelLeaveUser","parent","roles","educationClasses","teacherRecommend","organization","educations","leaves"
                ,"userMessages","personnelOccupationUser","personalEducationUser","personnelSalaryLogUser","personalRewardPunishmentUser","personalAssessmentUser"
                ,"userFeedbackUser","userLogs","opMemberAssignerUser","opMemberTracerUser","opMemberSubmitterUser","opTraceTracerUser","opTraceTracerDetailUser","inputStream","volumeM"
                ,"volumeY","transferTime","page","offDutyDate","onDutyDate","volume","submit","submitM","submitY","password"
                ,"money","moneyY","moneym","lv","lvM","lvY","logonState","new","newDiary","contentApprovalHashSet","contentHashSet","personnelReimburseHashSet","personnelOccupations","personalEducations","personnelSalaryLogUser"
                ,"personalRewardPunishments","personalAssessments","personnelLeaveUser","personnelOvertimeUser","userLogs","personnelReimburseHashSet","handleTime","default","collegeName1","ebeginTime1","eendTime1","major1","degree1","ememo1","collegeName2"
                ,"ebeginTime2","eendTime2","major2","degree2","ememo2","collegeName3","ebeginTime3","eendTime3","major3","degree3","ememo3","occurDate1","ocontent1","omemo1","ooperator1","ooperatorName1","occurDate2","ocontent2","omemo2","ooperator2","ooperatorName2","occurDate3"
                ,"ocontent3","omemo3","ooperator3","ooperatorName3","corpName1","beginTime1","endTime1","post1","csalary1","dmemo1","operatingDuty1","corpName2","beginTime2","endTime2","post2","csalary2","dmemo2","operatingDuty2"
                ,"corpName3","beginTime3","endTime3","post3","csalary3","dmemo3","operatingDuty3","assessDate1","assessUser1","type1","content1","amemo1","assessUserName1","assessDate2","assessUser2","type2","content2","amemo2","assessUserName2"
                ,"assessDate3","assessUser3","type3","content3","amemo3","assessUserName3","salary1","adjustDate1","admustResaon1","operateTime1","operator1","operatorName1","smemo1","salary2","adjustDate2","admustResaon2","operateTime2","operator2"
                ,"operatorName2","smemo2","salary3","adjustDate3","admustResaon3","operateTime3","operator3","operatorName3","smemo3"
        },response);

    }

    /**
     *<AUTHOR>
     *@date 2018/1/18 19:29
     *获取立案者、处理者、 查看着信息列表
     * coreCode filing-立案者 handle-处理者 see-查看者
    */
    @ResponseBody
    @RequestMapping("/getThreeUser.do")
    public void getThreeUser(String coreCode,HttpServletResponse response,User user) throws IOException {
        Map<String,Object> map = new HashMap<>();
        Integer oid = user.getOid();

        if (coreCode!=null && !"".equals(coreCode)){
            List<User> codeUsers = userService.getUserListByCoreCodeLocking(oid,coreCode);
            map.put("codeUsers",codeUsers);
            map.put("status",1);  //成功
        }else {
            map.put("status",0);  //失败
        }
       ObjectToJson.objectToJson1(map,new String[]{"personnelFolksHashSet","personnelInterviewHashSet","user","userPopedomHashSet","personnelOvertimeUser","personnelLeaveUser","parent","roles","educationClasses","teacherRecommend","organization","educations","leaves"
               ,"userMessages","personnelOccupationUser","personalEducationUser","personnelSalaryLogUser","personalRewardPunishmentUser","personalAssessmentUser"
               ,"userFeedbackUser","userLogs","opMemberAssignerUser","opMemberTracerUser","opMemberSubmitterUser","opTraceTracerUser","opTraceTracerDetailUser","inputStream","volumeM"
               ,"volumeY","transferTime","page","offDutyDate","onDutyDate","volume","submit","submitM","submitY","password"
               ,"money","moneyY","moneym","lv","lvM","lvY","logonState","new","newDiary","contentApprovalHashSet","contentHashSet","personnelReimburseHashSet","personnelOccupations","personalEducations","personnelSalaryLogUser"
               ,"personalRewardPunishments","personalAssessments","personnelLeaveUser","personnelOvertimeUser","userLogs","personnelReimburseHashSet","handleTime","default","collegeName1","ebeginTime1","eendTime1","major1","degree1","ememo1","collegeName2"
               ,"ebeginTime2","eendTime2","major2","degree2","ememo2","collegeName3","ebeginTime3","eendTime3","major3","degree3","ememo3","occurDate1","ocontent1","omemo1","ooperator1","ooperatorName1","occurDate2","ocontent2","omemo2","ooperator2","ooperatorName2","occurDate3"
               ,"ocontent3","omemo3","ooperator3","ooperatorName3","corpName1","beginTime1","endTime1","post1","csalary1","dmemo1","operatingDuty1","corpName2","beginTime2","endTime2","post2","csalary2","dmemo2","operatingDuty2"
               ,"corpName3","beginTime3","endTime3","post3","csalary3","dmemo3","operatingDuty3","assessDate1","assessUser1","type1","content1","amemo1","assessUserName1","assessDate2","assessUser2","type2","content2","amemo2","assessUserName2"
               ,"assessDate3","assessUser3","type3","content3","amemo3","assessUserName3","salary1","adjustDate1","admustResaon1","operateTime1","operator1","operatorName1","smemo1","salary2","adjustDate2","admustResaon2","operateTime2","operator2"
               ,"operatorName2","smemo2","salary3","adjustDate3","admustResaon3","operateTime3","operator3","operatorName3","smemo3"
       },response);

    }


    /**
    * <AUTHOR>
    * @Date 2018/1/19 10:57
    * 新增立案者、处理者、查看者接口
     * filing-立案者 handle-处理者 see-查看者  projectFiling-项目立案者  improvementFiling- 持续改进立案者 vehicleDriver-车务司机 pickingPerson 领料者
    */
    @ResponseBody
    @RequestMapping("/addManyUsers.do")
    public void addManyUsers(String coreCode,User user,HttpServletResponse response,Integer... userId) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();
        if (userId!=null&&!coreCode.isEmpty()) {
//            User loginUser;
//            if (loginUserId!=null){
//                loginUser =userService.getUserByID(loginUserId);
//            }else {
//                loginUser= (User) session.getAttribute("user");
//                loginUser=userService.getUserByID(loginUser.getUserID());
//            }
            for (Integer uid:userId) {
                User coreUser = userService.getUserByID(uid);//新任特殊人物
                userPopedomService.saveUserPopedomByCoreCode(coreUser, coreCode);//保存新任特殊人物权限
                userRoleService.saveUserRole(uid,coreCode,user);//保存userRole
                userService.getPopedomStringByUser(coreUser,true);//变更权限时用
                popedomTaskService.updateUserBadgeNumber(coreUser);//重新计算角标
                if (coreCode.equals("vehicleDriver")){
                    driverService.addDriver(coreUser);//如果是司机往司机表加人
                }
            }
            map.put("status", 1);
        }else {
            map.put("status", 0);//参数不正确
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    /**
    * <AUTHOR>
    * @Date 2018/1/19 14:21
    * 删除立案者、处理者、查看者接口
     * filing-立案者 handle-处理者 see-查看者 projectFiling-项目立案者  improvementFiling- 持续改进立案者 vehicleDriver-司机  pickingPerson 领料者
    */
    @ResponseBody
    @RequestMapping("/deleteSpecialUser.do")
    public void  deleteSpecialUser(Integer userId,String coreCode,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();
        if (userId!=null) {
            User user = userService.getUserByID(userId);
            userPopedomService.deleteUserPopedomByCoreCode(user.getUserID(), coreCode);//清除投诉特有权限
            userRoleService.deleteUserRoleByUserIdRoleCode(user.getUserID(), coreCode);//清除userRole
            userService.updateUser(user);
            userService.getPopedomStringByUser(user,true);//变更权限时用
            popedomTaskService.updateUserBadgeNumber(user);//重新计算角标
            if (coreCode.equals("vehicleDriver")){
                driverService.cancelDriverAuth(user);//如果是司机操作司机表
            }
            map.put("status", 1);
        }else {
            map.put("status", 0);
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);

    }

    /**
    * <AUTHOR>
    * @Date 2018/1/22 9:32
    * 获取有人员的部门 列表和人员列表
    */
    @ResponseBody
    @RequestMapping("/getOrgListUserList.do")
    public JsonResult getOrgListUserList(User user,String coreCode){
        List<OrganizationDto>  list=orgService.getOrgAndUserByOidLocking(user.getOid(),coreCode);
        JsonResult result=new JsonResult(1,list);
        return result;
    }

    /**
    * <AUTHOR>
    * @Date 2018/1/22 9:57
    * 投诉查看：  complaint/complaintCheck.jsp
    */
    @RequestMapping("/toComplaintCheck.do")
    public String toComplaintCheck(Model model, HttpServletRequest request){

        return "/complaint/complaintCheck";
    }
    
    /**
    * <AUTHOR>
    * @Date 2018/1/22 9:59
    * 投诉立案：  complaint/complaintFiling.jsp
    */
    @RequestMapping("/toComplaintFiling.do")
    public String toComplaintFiling(Model model, HttpServletRequest request){

        return "/complaint/complaintFiling";
    }
    
    /**
    * <AUTHOR>
    * @Date 2018/1/22 10:00
    * 投诉处理：  complaint/complaintHandling.jsp
     */
    @RequestMapping("/toComplaintHandling.do")
    public String toComplaintHandling(Model model, HttpServletRequest request){

        return "/complaint/complaintHandling";
    }
    
    /**
    * <AUTHOR>
    * @Date 2018/1/22 10:01
    * 综合管理：  complaint/integrateManage.jsp
     */
    @RequestMapping("/toIntegrateManage.do")
    public String toIntegrateManage(Model model, HttpServletRequest request){
        return  "/complaint/integrateManage";
    }
    
    /**
    * <AUTHOR>
    * @Date 2018/1/22 10:02
    * 分工设置：  generalAffairs/divisionSetting.jsp
     */
    @RequestMapping("/toDivisionSetting.do")
    public String toDivisionSetting(){
        return  "/generalAffairs/divisionSetting";
    }



    /**
    * <AUTHOR>
    * @Date 2018/5/31 9:33
    * 核心人物管理(项目管理、与持续改进、后续同功能复用、领料)
     *type 5-项目管理核心人物 7-持续改进核心人物  9-汽车核心人物 11-领料核心人物  13-购销统筹核心人物
    */
    @ResponseBody
    @RequestMapping("/editCoreUserRole.do")
    public void editCoreUserRole(User user,Integer userId,Integer type,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();
        if (userId!=null&&type!=null) {
            Integer oid=user.getOid();
//            User loginUser;
//            if (loginUserId!=null){
//                loginUser =userService.getUserByID(loginUserId);
//                oid=loginUser.getOid();
//            }else {
//                oid = (Integer) session.getAttribute("oid");
//                loginUser= (User) session.getAttribute("user");
//                loginUser=userService.getUserByID(loginUser.getUserID());
//            }
            String code=userRoleService.getUserRoleCodeByType(type);
            User coreUser = userService.getUserByCoreCode(oid, code);
            if(coreUser!=null){
                UserRoleHistory userRoleHistory=userRoleHistoryService.getUserRoleHistoryByRoleId(type,oid);
                if (userRoleHistory==null){//历史版本核心人物没产生 修改记录，则补上
                    userRoleHistoryService.saveUserRoleHistoryByUserRole(user,coreUser.getUserID(),type);//补上历史版本核心人物记录
                }
                userPopedomService.deleteUserPopedomByCoreCode(coreUser.getUserID(), code);//清除之前核心人物权限
                userRoleService.deleteUserRoleByUserIdRoleCode(coreUser.getUserID(), code);//清除userRole
                userService.getPopedomStringByUser(coreUser,true);//变更权限时用
                popedomTaskService.updateUserBadgeNumber(coreUser);//重新计算角标
//                userService.updateUser(user);//清空之前核心人物code
            }
            User newUser = userService.getUserByID(userId);//新任核心人物
            userService.updateUser(newUser);
            userPopedomService.saveUserPopedomByCoreCode(newUser,code);//保存新任核心人物权限
            userRoleService.saveUserRole(userId,code,user);
            userRoleHistoryService.saveUserRoleHistoryByUserRole(user,userId,type);//保存历史记录

            if ("vehicleCore".equals(code)){
                UserRole userRole=userRoleService.getUserRoleByUserIdRoleCode(userId,"vehicleDriver");
                if (userRole==null) {
                    userPopedomService.saveUserPopedomByCoreCode(newUser, "vehicleDriver");//保存司机权限
                    userRoleService.saveUserRole(userId, "vehicleDriver", user);
                    driverService.addDriver(newUser); //2.98汽车优化  车务的核心人物勾选生成的时候，他同时也需要成为司机
                }
            }
            userService.getPopedomStringByUser(newUser,true);//变更权限时用
            popedomTaskService.updateUserBadgeNumber(newUser);//重新计算角标
            map.put("status", 1);
        }else {
            map.put("status", 0);//参数不正确

        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    /**
    * <AUTHOR>
    * @Date 2018/5/31 10:52
    * 查询当前核心人物（项目管理、与持续改进、后续同功能复用、领料）
     * type 1-投诉核心人物 5-项目管理核心人物 7-持续改进核心人物  9-汽车核心人物 11-领料核心人物 13-购销统筹核心人物
    */
    @ResponseBody
    @RequestMapping("/getCoreUserRole.do")
    public void getCoreUserRole(User user,Integer type ,HttpServletResponse response) throws IOException {

        List<Map<String,Object>> list=new ArrayList<>();
        Integer oid= user.getOid();
//            String code=userRoleService.getUserRoleCodeByType(1);
        if (orgPopedomService.getOrgPopedomByMid(oid,"fb")) {
            User tousu = userService.getUserByCoreCode(oid, "core");
            Map<String, Object> tousumap = new HashMap<String, Object>();
            tousumap.put("project", "core");
            tousumap.put("coreUser", tousu);
            list.add(tousumap);
        }

        if (orgPopedomService.getOrgPopedomByMid(oid,"ta")){
            User project=userService.getUserByCoreCode(oid,"projectCore");
            Map<String,Object> projectmap=new HashMap<String,Object>();
            projectmap.put("project","projectCore");
            projectmap.put("coreUser",project);
            list.add(projectmap);
        }

        if (orgPopedomService.getOrgPopedomByMid(oid,"ha")) {
            User improvement = userService.getUserByCoreCode(oid, "improvementCore");
            Map<String, Object> improvementmap = new HashMap<String, Object>();
            improvementmap.put("project", "improvementCore");
            improvementmap.put("coreUser", improvement);
            list.add(improvementmap);
        }

        if (orgPopedomService.getOrgPopedomByMid(oid,"cc")) {
            User vehicle = userService.getUserByCoreCode(oid, "vehicleCore");
            Map<String, Object> vehicleMap = new HashMap<>();
            vehicleMap.put("project", "vehicleCore");
            vehicleMap.put("coreUser", vehicle);
            list.add(vehicleMap);
        }

        if (orgPopedomService.getOrgPopedomByMid(oid,"ge")) {
            User picking = userService.getUserByCoreCode(oid, "pickingCore");
            Map<String, Object> pickingMap = new HashMap<>();
            pickingMap.put("project", "pickingCore");
            pickingMap.put("coreUser", picking);
            list.add(pickingMap);
        }

        if (orgPopedomService.getOrgPopedomByMid(oid,"ql")) {
            User buySales = userService.getUserByCoreCode(oid, "buySalesCore");//购销统筹核心人物
            Map<String, Object> buySalesMap = new HashMap<>();
            buySalesMap.put("project", "buySalesCore");
            buySalesMap.put("coreUser", buySales);
            list.add(buySalesMap);
        }


        ObjectToJson.objectToJson(list,new String[]{"personnelFolksHashSet","personnelInterviewHashSet","user","userPopedomHashSet","personnelOvertimeUser","personnelLeaveUser","parent","roles","educationClasses","teacherRecommend","organization","educations","leaves"
                ,"userMessages","personnelOccupationUser","personalEducationUser","personnelSalaryLogUser","personalRewardPunishmentUser","personalAssessmentUser"
                ,"userFeedbackUser","userLogs","opMemberAssignerUser","opMemberTracerUser","opMemberSubmitterUser","opTraceTracerUser","opTraceTracerDetailUser","inputStream","volumeM"
                ,"volumeY","transferTime","page","offDutyDate","onDutyDate","volume","submit","submitM","submitY","password"
                ,"money","moneyY","moneym","lv","lvM","lvY","logonState","new","newDiary","contentApprovalHashSet","contentHashSet","personnelReimburseHashSet","personnelOccupations","personalEducations","personnelSalaryLogUser"
                ,"personalRewardPunishments","personalAssessments","personnelLeaveUser","personnelOvertimeUser","userLogs","personnelReimburseHashSet","handleTime","default","collegeName1","ebeginTime1","eendTime1","major1","degree1","ememo1","collegeName2"
                ,"ebeginTime2","eendTime2","major2","degree2","ememo2","collegeName3","ebeginTime3","eendTime3","major3","degree3","ememo3","occurDate1","ocontent1","omemo1","ooperator1","ooperatorName1","occurDate2","ocontent2","omemo2","ooperator2","ooperatorName2","occurDate3"
                ,"ocontent3","omemo3","ooperator3","ooperatorName3","corpName1","beginTime1","endTime1","post1","csalary1","dmemo1","operatingDuty1","corpName2","beginTime2","endTime2","post2","csalary2","dmemo2","operatingDuty2"
                ,"corpName3","beginTime3","endTime3","post3","csalary3","dmemo3","operatingDuty3","assessDate1","assessUser1","type1","content1","amemo1","assessUserName1","assessDate2","assessUser2","type2","content2","amemo2","assessUserName2"
                ,"assessDate3","assessUser3","type3","content3","amemo3","assessUserName3","salary1","adjustDate1","admustResaon1","operateTime1","operator1","operatorName1","smemo1","salary2","adjustDate2","admustResaon2","operateTime2","operator2"
                ,"operatorName2","smemo2","salary3","adjustDate3","admustResaon3","operateTime3","operator3","operatorName3","smemo3"
        },response);

    }

    /**
    * <AUTHOR>
    * @Date 2019/5/23 15:38
    * 获取可选人员
    */
    @ResponseBody
    @RequestMapping("/getCoreSettingUserList.do")
    public JsonResult getCoreSettingUserList(User user,String coreCode){
        List<UserDto>  list=userService.getCoreSettingUserList(user.getOid(),coreCode);
        JsonResult result=new JsonResult(1,list);
        return result;
    }

    /**
    * <AUTHOR>
    * @Date 2019/8/26 11:10
    * 查看 各项目核心人物修改记录接口
    * type 1-投诉核心人物 5-项目管理核心人物 7-持续改进核心人物  9-汽车核心人物 11-领料核心人物 13-购销统筹核心人物
    */
    @ResponseBody
    @RequestMapping("/getUserRoleHistoryList.do")
    public JsonResult getUserRoleHistoryList(User user,Integer type, PageInfo pageInfo){
        List<UserRoleHistory> userRoleHistoryList= userRoleHistoryService.getUserRoleHistoryListByRoleId(user.getOid(),type,pageInfo);
        List<Map<String,Object>> mapList=new ArrayList<>();
        int number=0;
        String dataState="";
        String updateName="";
        Date updateTime=new Date();
        String coreUser="";
        for (UserRoleHistory userRoleHistory:userRoleHistoryList){
            Map<String,Object> map=new HashMap<>();

            String userName=userService.getUserByID(userRoleHistory.getUserID()).getUserName();//核心人物名
            if (number==0){
                dataState="原始信息";
                updateName=userRoleHistory.getCreateName();
                updateTime=userRoleHistory.getCreateTime();

            }else {
                dataState="第"+number+"次修改后";
                updateName=userRoleHistory.getUpdateName();
                updateTime=userRoleHistory.getUpdateTime();
            }
            map.put("dataState",dataState);//资料状态
            map.put("userName",userName);//核心人物名
            map.put("id",userRoleHistory.getId());
            map.put("updateName",updateName);//操作人名
            map.put("updateTime",updateTime);//修改时间
            mapList.add(map);
            coreUser=userName;
            number+=1;
        }
//        if (userRoleHistoryList.size()<=1){
//            userRoleHistoryList.clear();//如果只有一条记录，认为是原始核心人物  未修改
//        }

        Map<String,Object> map=new HashMap<>();
        map.put("number",number==0?0:number-1);//最新的修改次数
        map.put("updateName",updateName);//人名
        map.put("userName",coreUser);//核心人物名

        map.put("updateTime",updateTime);//修改时间
        map.put("userRoleHistoryList",mapList);
        map.put("pageInfo",pageInfo);
        return new JsonResult(1,map);

    }


}
