package cn.sphd.miners.modules.material.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.material.dao.PoContractBaseDao;
import cn.sphd.miners.modules.material.entity.PoContractBase;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
@Repository
public class PoContractBaseDaoImpl extends BaseDao<PoContractBase, Serializable> implements PoContractBaseDao {
}
