<template>
  <div class="processSet">
    <div class="homeset" v-show="mainPage==='homeset'">
      <div class="panel-box">
        <p>设置工件（含产品与零组件）的操作规范，主要是设置控制点、材料、文件及操作人员等，这需要先在本页面完成工序的“基础设置”。</p>
      </div>
      <div class="panel-box">
        <p>工序“基础设置”的前置设置</p>
        <div class="ty-alert">
          <div class="com_address">进行工序“基础设置”时，需在已有选项中选择。建议提前设置选项，具体请点击“选项设置”。</div>
          <span class=" ty-linkBtn ty-right stopcat" @click="gotenchose">选项设置</span>
        </div>
        <div class="ty-alert">
          <div class="com_address" v-show="setten === 1">进行工序“基础设置”时，目前“允许工序下设置子工序”。
            如果此状态与公司要求不符，请点击“状态设置”。</div>
          <div class="com_address" v-show="setten === 2">进行工序“基础设置”时，目前“不允许工序带有子工序”。
            如果此状态与公司要求不符，请点击“状态设置”。</div>
          <span class="ty-linkBtn ty-right stopcat" @click="sttusetings">状态设置</span>
        </div>
      </div>
      <div class="panel-box" style="font-size: 0.9em;">
        <div class="ty-alert">工序的“基础设置”</div>
        <div class="ty-alert">
          <div class="">
            <span class=" ty-linkBtn ty-right goten stopcat" @click="goensusses(1,0)">去管理</span>
            <span>产品的制造/装配工序</span>
            <span class="wrtdboad">应设置<span class="yses1">{{ homeSetData.yses1Data }}</span>条，已设置<span class="edsess1">{{ homeSetData.edsess1Data }}</span>条</span>
          </div>
        </div>
        <div class="ty-alert">
          <div class="">
            <span class="ty-color-gray ty-linkBtn ty-right goten stopcat" >去管理</span>
            <span>产品的包装工序</span>
            <span class="wrtdboad2">应设置<span class="yses2">{{ homeSetData.yses2Data }}</span>条，已设置<span class="edsess2">{{ homeSetData.edsess2Data }}</span>条</span>
          </div>
        </div>
        <div class="ty-alert">
          <div class="">
            <span class=" ty-linkBtn ty-right goten stopcat" @click="goensusses(2,0)">去管理</span>
            <span>自制零组件的制造/装配工序</span>
            <span class="wrtdboad3">应设置<span class="yses3">{{ homeSetData.yses3Data }}</span>条，已设置<span class="edsess3">{{ homeSetData.edsess3Data }}</span>条</span>
          </div>
        </div>
        <div class="ty-alert">
          <div class="">
            <span class="ty-color-gray ty-linkBtn ty-right goten stopcat">去管理</span>
            <span>自制零组件存放的包装工序</span>
            <span class="wrtdboad4">应设置<span class="yses4">{{ homeSetData.yses4Data }}</span>条，已设置<span class="edsess4">{{ homeSetData.edsess4Data }}</span>条</span>
          </div>
        </div>
      </div>
      <!--<div class="panel-box">
        <div class="ty-alert">
          <div class="">
            <div>
              <span class="ty-color-gray ty-linkBtn ty-right ">改变状态</span>
              <span>设置工序时，是否需设置“子工序”？</span>
              <span class="wrtbdoad5">XXX</span>
            </div>
            <div class="ty-color-blue tipSize">注：上述状态如与贵公司需求不符，请点击“改变状态”。</div>
          </div>

        </div>
      </div>-->
    </div>

    <!--   点击‘去管理’跳转页面(b页) -->
    <div class="tomanage" v-show="mainPage==='tomanage'">
      <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainpageSet('homeset')">返回</span>
      <div class="pageStyle container_item allplnce">
        <div class="initBody bnck">
          <table class="ty-table ty-table-control ty-table-headerLine">
            <thead>
            <tr>
              <td>图号（代号）/名称/规格/型号/计量单位</td>
              <td>单重</td>
              <td>工序</td>
              <td>子工序</td>
              <td>操作</td>
              <td>操作</td>
            </tr>
            </thead>
            <tbody  id="tdonalty">
            <tr v-for="(item, index) in tomanageData" :key="index">
              <td>{{ item.innerSn || '' }}/{{ item.name || '' }}/{{ item.specifications|| '' }}/{{ item.model || '' }}/{{ item.unit ||'' }}</td>
              <td>{{ item.netWeight || '--' }}</td>
              <td>已设置{{ item.processCount || 0 }}个</td>
              <td>已设置{{ item.subProcessCount || 0 }}个</td>
              <td>已设置{{ item.operateCount || 0 }}项</td>
              <td>
                <span class="ty-color-blue" >查看</span>
                <span class="ty-color-blue" @click="getsetunup(item)">设置</span>
                <span class="ty-color-blue" >操作日志</span>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!--分页功能-->
      <TyPage v-if="tomanageData"
              :curPage="tomanageData.curPage" :pageSize="tomanageData.pageSize"
              :allPage="tomanageData.totalPage" :pageClickFun="pageClick">
      </TyPage>

    </div>


    <!--  点击‘设置’展示‘查看设置’页面(c页)2 -->
    <div class="tolooksetup" v-show="mainPage==='tolooksetup'">
      <div class="bnck">
        <div class="ty-right">
          <span v-show="collse === 1" style="color: #808080;cursor: not-allowed;" @click.stop>
            <i class="fa fa-circle-o icon-gray"></i>本产品已设置工序0个，设置完成！
          </span>
          <span @click="toggleSetOk" v-show="collse === 0">
            <i id="upsetHangAccount1" class="fa" :class="tolooksetupData.setOk? 'fa-dot-circle-o' : 'fa-circle-o'"></i> 本产品已设置工序
            <span class="onderal">{{ tolooksetupData.settingsList2.length }}</span>个，设置完成！
          </span>
          <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-5" style="cursor: not-allowed;background-color:#D9D9D9;color: #A8ABB2;"
                @click.stop v-show="collse === 1">确&nbsp;&nbsp;定</span>
          <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-5" style="cursor: not-allowed;background-color: #D9D9D9;color: #A8ABB2;"
                @click.stop v-show="collse === 0 && collse2 === ''">确&nbsp;&nbsp;定</span>
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="makdsure" v-show="collse === 0 && collse2 === 0">确&nbsp;&nbsp;定</span>
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="makdsure" v-show="collse === ''">确&nbsp;&nbsp;定</span>
        </div>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainpageSet('homeset')">返回首页</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 marL30" @click="mainpageSet('tomanage')">返回上一页</span>
      </div>
      <div class="">
        <div class="messg1" >
          <div class="ty-alert">
            <div class="ty-right bussine">
              <span class="ty-linkBtn" @click="">选择模板</span>
              <span class="ty-linkBtn" @click="choneprod('bck')">添加工序</span>
              <br />
              <div class="ty-right nire">
                <span class="ty-linkBtn" @click="morecatmig">更多操作说明</span>
              </div>
            </div>
            <!--<div class="ty-right bueete">
              <el-button type="text" @click="">选中模板</el-button>
              <el-button type="text" @click="">添加工序</el-button>
              <br />
              <div class="ty-right">
                <el-button type="text" @click="">更多操作说明</el-button>
              </div>
             <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" >选择“工序套餐”</span>
              <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 marL30" @click="choneprod">选择一道“工序”</span>
            </div>-->
            <div class="com_address">
              <div>
                <span v-if="tolooksetupData.editPro === null"></span>
                <span id="alone" v-else>{{ tolooksetupData.editPro.innerSn || '' }}/{{ tolooksetupData.editPro.name || '' }}/{{ tolooksetupData.editPro.specifications|| '' }}/{{ tolooksetupData.editPro.model || '' }}/{{ tolooksetupData.editPro.unit ||'' }}</span>
              </div>
              <div class="ty-color-blue tipSize" style="margin-top: -10px;">
                注：请根据实际情况配置本产品的工序。配置时，既可点击“添加工序”，也可点击“选择模板”。</div>
            </div>
          </div>
        </div>
        <div class="messg2 scrollable-div"><!-- 在这个div内容过长时，会出现滚动条-->
          <div v-show="tolooksetupData.settingsList2">
            <div class="process1" v-for="(process1, indecProcess1) in tolooksetupData.settingsList2" :key="indecProcess1" style="padding: 10px 0;">
              <div class="flexsty">
                <div>
                  {{ process1.timing }}{{ process1.kind }}{{ process1.code }}/{{ process1.name }}
                  <!--<span v-if="process1.havSonProcess === 1" class="ty-color-blue tipSize">注：请根据实际情况，选择本工序下的各道子工序！</span>
                  <span v-else class="ty-color-blue tipSize">注：请根据实际情况，选择本工序下的各项操作！</span>-->
                </div>
                <!--<div>
                  <span class="ty-linkBtn-blue" @click="process1Ctrol('sort', process1,indecProcess1 )">排序</span>
                  <span class="ty-linkBtn-red" @click="process1Ctrol('del', process1 ,indecProcess1)">删除</span>
                </div>-->
                <div>
                  <!--<div v-if="process1.havSonProcess === 1">
                    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" >选择”子工序套餐“</span>
                    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 marL30" @click="selectSonProcess(process1)">选择一道“子工序“</span>
                  </div>
                  <div v-else>
                    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" >选择“操作套餐“</span>
                    <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 marL30" @click="selectOprateBtn(null, process1, 1)">选择一项”操作“</span>
                  </div>-->
                  <!--clkset代表上一次点击是否点击了‘设置’按钮-->
                  <div v-show="process1.clkset === 2">
                    <span class="ty-linkBtn" @click="choneprod('bef',indecProcess1)" style="margin-right: 15px;">在上方添加工序</span>
                    <!--isLeaf代表的是选项管理中设置的，configu代表的是首页状态管理处设置的,.havSonProcess代表工序列表中是选择了有-1还是没有-2-->
                    <!--此时需要的是：在第二次设置时第一次设置的工序数据不管configu，而是通过isLeaf和havSonProcess来判断按钮的展示-->
                    <!--相当于是在点击’设置‘后跳转的页面无需判断configu，而在选择工序那页再判断configu-->
                    <span class="ty-linkBtn" @click="selectSonProcess(process1,'bck')"
                          v-show="this.configu === true && process1.isLeaf === true && process1.havSonProcess === 1">添加本工序下的子工序</span>
                    <span class="ty-linkBtn" @click="selectOprateBtn(null,process1,1 ,'bck')" style="margin-right: 15px;"
                          v-show="this.configu === true && process1.isLeaf === true && process1.havSonProcess === 2">添加本工序下的操作</span>
                    <!--                    <span class="ty-linkBtn" @click="selectOprateBtn(null,process1,1 ,'bck')" style="margin-right: 15px;"-->
                    <!--                          v-if="this.configu === true && process1.isLeaf === false || this.configu === true && process1.isLeaf === null">添加本工序下的操作</span>-->
                    <span class="ty-linkBtrn" @click.stop style="cursor: not-allowed;margin-right: 15px;padding: 0 10px;"
                          v-show="this.configu === true && process1.isLeaf === false || this.configu === true && process1.isLeaf === null">添加本工序下的操作</span>
                    <span class="ty-linkBtn" @click="selectOprateBtn(null,process1,1 ,'bck')" style="margin-right: 15px;"
                          v-show="this.configu === false && process1.isLeaf === true">添加本工序下的操作</span>
                    <span class="ty-linkBtrn" v-show="this.configu === false && process1.isLeaf === false || this.configu === false && process1.isLeaf === null"
                          @click.stop style="cursor: not-allowed;margin-right: 15px;padding: 0 10px;">添加本工序下的操作</span>
                    <!--                    <span class="ty-linkBtn" v-on:click="process1Ctrol('sort',process1,indecProcess1)">排序</span>-->
                    <span class="ty-linkBtn" @click="process1Ctrol('sort',process1,indecProcess1 )">排序</span>
                    <span class="ty-linkBtn" @click="process1Ctrol('del',process1,indecProcess1 )">删除</span>
                  </div>
                  <div v-show="process1.clkset === 1">
                    <span class="ty-linkBtn" @click="choneprod('bef',indecProcess1)" style="margin-right: 15px;">在上方添加工序</span>
                    <span class="ty-linkBtn" @click="selectSonProcess(process1,'bck')"
                          v-show="process1.isLeaf === true && process1.havSonProcess === 1">添加本工序下的子工序</span>
                    <span class="ty-linkBtn" @click="selectOprateBtn(null,process1,1 ,'bck')" style="margin-right: 15px;"
                          v-show="process1.isLeaf === true && process1.havSonProcess === 2">添加本工序下的操作</span>
                    <span class="ty-linkBtrn" @click.stop style="cursor: not-allowed;margin-right: 15px;padding: 0 10px;"
                          v-show="process1.isLeaf === false || process1.isLeaf === null">添加本工序下的操作</span>
                    <span class="ty-linkBtn" @click="process1Ctrol('sort',process1,indecProcess1 )">排序</span>
                    <span class="ty-linkBtn" @click="process1Ctrol('del',process1,indecProcess1 )">删除</span>
                  </div>
                </div>
              </div>
              <div>
                <!-- 有子工序的-->
                <div v-if="process1.havSonProcess === 1">
                  <div class="process2" v-for="(process2, indexProcess2) in process1.sonProcesses" :key="indexProcess2" style="padding: 5px 0;">
                    <div class="flexsty">
                      <div>——{{ process2.timing }}{{ process2.kind }}{{ process2.code }}/{{ process2.name }}</div>
                      <div v-show="process2.clkset === 2">
                        <span class="ty-linkBtn" @click="selectSonProcess(process1,'bef',indexProcess2)">在上方添加子工序</span>
                        <span class="ty-linkBtn" @click="selectOprateBtn(process2, process1, 2,'bck')"
                              v-show="this.configu === true && process2.isLeaf === true">添加本子工序下的操作</span>
                        <span class="ty-linkBtrn" v-show="this.configu === true && process2.isLeaf === false || this.configu === false && process2.isLeaf === true ||
                              this.configu === false && process2.isLeaf === false" style="cursor: not-allowed;padding: 0 10px;" @click.stop>添加本子工序下的操作</span>
                        <span class="ty-linkBtn" @click="process2Ctrol('sort', process1, process2 , indexProcess2)">排序</span>
                        <span class="ty-linkBtn" @click="process2Ctrol('del', process1, process2,indexProcess2 )">删除</span>
                      </div>
                      <div v-show="process2.clkset === 1">
                        <span class="ty-linkBtn" @click="selectSonProcess(process1,'bef',indexProcess2)">在上方添加子工序</span>
                        <span class="ty-linkBtn" @click="selectOprateBtn(process2, process1, 2,'bck')"
                              v-show="process2.isLeaf === true">添加本子工序下的操作</span>
                        <span class="ty-linkBtrn" v-show="process2.isLeaf === false" style="cursor: not-allowed;padding: 0 10px;" @click.stop>添加本子工序下的操作</span>
                        <span class="ty-linkBtn" @click="process2Ctrol('sort', process1, process2 , indexProcess2)">排序</span>
                        <span class="ty-linkBtn" @click="process2Ctrol('del', process1, process2,indexProcess2 )">删除</span>
                      </div>
                      <!--<div>
                        {{ process2.code }} / {{ process2.name }} <br>
                        <span class="ty-color-blue tipSize">注：请根据实际情况，选择本工序下的各项操作！</span>
                      </div>
                      <div>
                        <span class="ty-linkBtn-blue" @click="process2Ctrol('sort', process1, process2 , indexProcess2)">排序</span>
                        <span class="ty-linkBtn-red" @click="process2Ctrol('del', process1, process2,indexProcess2 )">删除</span>
                      </div>
                      <div>
                        <div>
                          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" >选择“操作套餐“</span>
                          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 marL30" @click="selectOprateBtn(process2, process1, 2)">选择一项”操作“</span>
                        </div>
                      </div>-->
                    </div>
                    <div class="Operate" v-show="process2.sonOperateList && process2.sonOperateList.length >0">
                      <table class="ty-table ty-table-control ty-table-headerLine">
                        <thead class="coldor">
                        <tr>
                          <td>项目</td>
                          <!--<td>周期属性</td>-->
                          <td>操作</td>
                          <!--<td>操作人员需进行的操作</td>
                          <td>标签</td>
                          <td>是否为周期性操作</td>
                          <td>操作</td>-->
                        </tr>
                        </thead>
                        <tr v-for="(operitem, indexOperate) in process2.sonOperateList" :key="indexOperate">
                          <td style="text-align: left;">{{ operitem.timing }}{{ operitem.kind }}{{ operitem.code }}/{{ operitem.name }}</td>
                          <!--<td>{{ operitem.periodicity }}</td>
                          <td v-html="operitem.name"></td>
                          <td>{{ operitem.categoryName }} </td>
                          <td>{{ operitem.isCyclicity ? '是' : '否' }} </td>-->
                          <td style="text-align: right;">
                            <span class="ty-color-blue" @click="selectOprateBtn(process2, process1, 2,'bef',indexOperate)">在上方添加操作</span>
                            <span class="ty-color-blue" @click="OperateCrol('sort',process1, process2, operitem,  indexOperate)">排序</span>
                            <span class="ty-color-red" @click="OperateCrol('del', process1, process2, operitem, indexOperate )">删除</span>
                          </td>
                        </tr>
                      </table>
                    </div>
                  </div>
                </div>
                <!-- 没有子工序的-->
                <div v-else>
                  <div class="Operate" v-show="process1.operateList && process1.operateList.length > 0">
                    <table class="ty-table ty-table-control ty-table-headerLine">
                      <thead class="coldor">
                      <tr>
                        <td>项目</td>
                        <!--<td>周期属性</td>-->
                        <!--<td>操作人员需进行的操作</td>
                        <td>标签</td>
                        <td>是否为周期性操作</td>-->
                        <td>操作</td>
                      </tr>
                      </thead>
                      <tr v-for="(operitem2, indexOperate2) in process1.operateList" :key="indexOperate2">
                        <td style="text-align: left;">{{ operitem2.timing }}{{ operitem2.kind }}{{ operitem2.code }}/{{ operitem2.name }}</td>
                        <!--<td>{{ operitem2.periodicity }}</td>
                        <td v-html="operitem2.name"></td>
                        <td>{{ operitem2.categoryName }} </td>
                        <td>{{ operitem2.isCyclicity ? '是' : '否' }} </td>-->
                        <td style="text-align: right;">
                          <span class="ty-color-blue" @click="selectOprateBtn(null,process1,1,'bef',indexOperate2)">在上方添加操作</span>
                          <span class="ty-color-blue" @click="OperateCrol('sort',process1, 'no1', operitem2, indexOperate2 )">排序</span>
                          <span class="ty-color-red" @click="OperateCrol('del', process1, 'no1', operitem2, indexOperate2 )">删除</span>
                        </td>
                      </tr>
                    </table>
                    <!--</div>-->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 选择工序列表 -->
    <div class="choseseten" v-show="mainPage==='choseseten'">
      <div>
        <div class="ty-right">
          <el-button class="ty-btn-blue" @click="addPrecess1Btn(1,'sep','mor')">创建选项</el-button>
          <el-button class="ty-btn-blue" @click="selectProcess1Ok" v-show="choseover === 1">选择完毕，确定添加</el-button>
          <el-button type="info" @click.stop style="cursor: not-allowed;margin-left: 12px;background-color: #D9D9D9;
              color: #A8ABB2;" disabled v-show="choseover === 0">选择完毕，确定添加</el-button>
        </div>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainpageSet('homeset')">返回首页</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 marL30" @click="mainpageSet('tolooksetup')">返回上一页</span>
      </div>
      <div class="chose">
        <span class="ty-right">
          按标签筛选选项
           <el-select v-model="chosesetenData.selectCat" placeholder="请选择" @change="changeCat" class="selecter">
              <el-option
                  v-for="catItem in chosesetenData.catList "
                  :key="catItem.id"
                  :label="catItem.name"
                  :value="catItem.id"
              >
              </el-option>
            </el-select>
        </span>
        <span>以下为可供选择的选项，可多选。选完后，请点击右上角的“选择完毕，确定添加”。</span>
        <div class="ty-color-blue" style="margin-top: -10px;">注： 选项如不全，可创建选项；</div>
      </div>
      <div>
        <table class="ty-table ty-table-control ty-table-headerLine">
          <thead class="coldor">
          <tr>
            <td>选择</td>
            <td>项目</td>
            <!--<td>周期属性</td>-->
            <!--<td v-if="chidchis === 1 || chidchis === 2">本工序下是否有子工序</td>-->
            <td v-show="setten === 1">本工序下是否有子工序</td>
            <!--            <td>工序代号</td>
          <td>工序名称</td>
           <td>本工序下是否有子工序</td>-->
            <td>创建</td>
          </tr>
          </thead>
          <tr v-for="(processItem, indexProcess) in chosesetenData.processList " :key="indexProcess">
            <td>
              <el-checkbox v-model="processItem.isSelect" true-label="1" label=""
                           @change="toggleSelectItem(processItem)" v-show="processItem.checkick === true"></el-checkbox>
              <el-checkbox v-show="processItem.checkick === false" v-model="processItem.isSelect" @change="handleCheckboxChange"
                           true-label="1" label="" class="box-color">
              </el-checkbox>
            </td>
            <td>{{ processItem.timing }}{{ processItem.kind }}{{ processItem.code }}/{{ processItem.name }}</td>
            <!--<td>{{ processItem.periodicity }}</td>
            下面应该还有一种可能性：haschild = 3——不展示那一列-->
            <td v-show="this.setten === 1 && processItem.haschild === 2">——</td>
            <td v-show="this.setten === 1 && processItem.haschild === 1">
              <el-radio-group v-model="processItem.havSonProcess" @change="changeHavSon(processItem)"
                              v-show="processItem.checkick === true">
                <el-radio :label="1" @click.native.prevent="getchidt(1,processItem)">有</el-radio>
                <el-radio :label="2" @click.native.prevent="getchidt(2,processItem)">没有</el-radio>
              </el-radio-group>
              <el-radio-group v-model="processItem.havSonProcess"  @change="handleChdioChange"
                              v-show="processItem.checkick === false">
                <el-radio :label="1" :class="{'radio-color': shouldApplyCustomStyle(1)}"
                          :disabled="processItem.selectedOption = 2">有</el-radio>
                <el-radio :label="2" :class="{'radio-color': shouldApplyCustomStyle(2)}"
                          :disabled="processItem.selectedOption = 1">没有</el-radio>
                <!--  :disabled="selectedOption === 'Option 2'">:disabled="processItem.selectedOption === 2"-->
              </el-radio-group>
            </td>
            <!--            <td v-show="this.setten === 1 && processItem.haschild === 0" class="custom-radio-group">-->
            <!--              <el-radio-group :disabled="true">-->
            <!--                <el-radio :label="1" @click="">有</el-radio>-->
            <!--                <el-radio :label="2" @click="">没有</el-radio>-->
            <!--              </el-radio-group>-->
            <!--            </td>-->
            <!--<td v-if="chidchis === 2">——</td>
            <td v-if="chidchis === 1 && processItem.haschild === 1">
              <el-radio-group v-model="processItem.havSonProcess" @change="changeHavSon(processItem)">
                <el-radio :label="1" @click.native.prevent="getchidt(1,processItem)">有</el-radio>
                <el-radio :label="2" @click.native.prevent="getchidt(2,processItem)">没有</el-radio>
              </el-radio-group>
            </td>
            <td v-if="chidchis === 1 && processItem.haschild === 0" class="custom-radio-group">
              <el-radio-group :disabled="true">
                <el-radio :label="1" @click="">有</el-radio>
                <el-radio :label="2" @click="">没有</el-radio>
              </el-radio-group>
            </td>-->
            <!--<td>{{ processItem.code }}</td>
            <td>{{ processItem.name }}</td>
            <td>
              <el-radio-group v-model="processItem.havSonProcess" @change="changeHavSon(processItem)">
                <el-radio :label="1">有</el-radio>
                <el-radio :label="2">没有</el-radio>
              </el-radio-group>
            </td>-->
            <td>{{ processItem.createName }} {{ formatTimestamp(processItem.createTime) }}</td>
          </tr>
        </table>
      </div>
    </div>

    <!-- 选择子工序列表 -->
    <div class="chidlistplc" v-show="mainPage==='chidlistplc'">
      <div>
        <div class="ty-right">
          <el-button class="ty-btn-blue" @click="addPrecess1Btn(2,'sep','mor')">创建选项</el-button>
          <el-button class="ty-btn-blue" @click="selectProcess2Ok" v-show="choseover2 === 1">选择完毕，确定添加</el-button>
          <el-button type="info" @click.stop style="cursor: not-allowed;margin-left: 12px;background-color: #D9D9D9;
              color:#A8ABB2;" disabled v-show="choseover2 === 0">选择完毕，确定添加</el-button>
        </div>
        <el-button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainpageSet('homeset')">返回首页</el-button>
        <el-button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 marL30" @click="mainpageSet('tolooksetup')">返回上一页</el-button>
      </div>
      <div class="chose">
        <span class="ty-right">
          按标签筛选选项
           <el-select v-model="chidlistplcData.selectCat" placeholder="请选择" @change="changeSonCat" class="selecter">
              <el-option
                  v-for="catItem in chidlistplcData.catList "
                  :key="catItem.id"
                  :label="catItem.name"
                  :value="catItem.id"
              >
              </el-option>
            </el-select>
        </span>
        <p> 以下为可供选择的选项，可多选。选完后，请点击右上角的“选择完毕，确定添加”。</p>
        <div class="ty-color-blue" style="margin-top: -10px;">注： 选项如不全，可创建选项；</div>
        <!--        <span>选择一个选项，并确认该工序是否带有子工序后，点击右上角的“选择完毕”。</span>-->
      </div>
      <div>
        <table class="ty-table ty-table-control ty-table-headerLine">
          <thead class="coldor">
          <tr>
            <td>选择</td>
            <td>项目</td>
            <!--<td>周期属性</td>-->
            <td>创建</td>
          </tr>
          </thead>
          <tr v-for="(processItem, indexProcessSon) in chidlistplcData.processList " :key="indexProcessSon">
            <td>
              <el-checkbox v-model="processItem.isSelect" true-label="1" label=""
                           @change="toggleSelectSonItem(processItem)" v-show="processItem.checkchick === true"></el-checkbox>
              <el-checkbox v-model="processItem.isSelect" v-show="processItem.checkchick === false" @change="handleCheckboxChange2"
                           true-label="1" label="" class="box-color"></el-checkbox>
            </td>
            <td>{{ processItem.timing }}{{ processItem.kind }}{{ processItem.code }}/{{ processItem.name }}</td>
            <!--<td>操作次数的值/时间单位前的值 时间单位</td>
            <td>{{ processItem.code }}</td>
            <td>{{ processItem.name }}</td>-->
            <td>{{ processItem.createName }} {{ formatTimestamp(processItem.createTime) }}</td>
          </tr>
        </table>
      </div>
    </div>

    <!-- 选择操作列表 -->
    <div class="oprateContainer" v-show="mainPage==='oprateContainer'">
      <div>
        <div class="ty-right">
          <el-button class="ty-btn-blue" @click="addPrecess1Btn(3,'sep','mor')">创建选项</el-button>
          <el-button class="ty-btn-blue" @click="selectOprateOk" v-show="choseover3 === 1">选择完毕，确定添加</el-button>
          <el-button type="info" @click.stop style="cursor: not-allowed;margin-left:12px;background-color:#D9D9D9;
              color:#A8ABB2;" disabled v-show="choseover3 === 0">选择完毕，确定添加</el-button>
        </div>
        <el-button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainpageSet('homeset')">返回首页</el-button>
        <el-button class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 marL30" @click="mainpageSet('tolooksetup')">返回上一页</el-button>
      </div>
      <!--<div style="padding-bottom: 20px;">
        <ul class="ty-secondTab " >
          <li :class="oprateContainerData.secondTabNum === 1 ?'ty-active':''" @click="changeTab(1)">准备工作</li>
          <li :class="oprateContainerData.secondTabNum === 2 ?'ty-active':''" @click="changeTab(2)">正式操作</li>
          <li :class="oprateContainerData.secondTabNum === 3 ?'ty-active':''" @click="changeTab(3)">收尾工作</li>
        </ul>
      </div>-->
      <div class="chose">
        <span class="ty-right"><!--发现的问题：在点击到‘正在运行’列表后，进行筛选，回到‘全部类别’，表格展示的是‘准备工作’的列表数据-->
          按标签筛选选项
           <el-select v-model="oprateContainerData.selectCat" placeholder="请选择" @change="changeOprateCat" class="selecter">
              <el-option
                  v-for="catItem in oprateContainerData.catList "
                  :key="catItem.id"
                  :label="catItem.name"
                  :value="catItem.id"
              >
              </el-option>
            </el-select>
        </span>
        <p> 以下为可供选择的选项，可多选。选完后，请点击右上角的“选择完毕，确定添加”。</p>
        <div class="ty-color-blue" style="margin-top: -10px;">注： 选项如不全，可创建选项；</div>
        <!--        <span>选择一个选项，并确认该工序是否带有子工序后，点击右上角的“选择完毕”。</span>-->
      </div>
      <div>
        <table class="ty-table ty-table-control ty-table-headerLine">
          <thead class="coldor">
          <tr>
            <td>选择</td>
            <td>项目</td>
            <!--<td>周期属性</td>-->
            <!--            <td>标签</td>-->
            <!--            <td>是否为周期性操作</td>-->
            <td>创建</td>
          </tr>
          </thead>
          <tr v-for="(oprateItem, indexOprate) in oprateContainerData.oprateList " :key="indexOprate">
            <td>
              <el-checkbox v-model="oprateItem.isSelect" true-label="1" label="" v-show="oprateItem.checkopick === true"
                           @change="toggleoprateItem(oprateItem)"></el-checkbox>
              <el-checkbox v-model="oprateItem.isSelect" v-show="oprateItem.checkopick === false" true-label="1" label=""
                           @change="handleCheckboxChange3" class="box-color"></el-checkbox>
            </td>
            <td>{{ oprateItem.timing }}{{ oprateItem.kind }}{{ oprateItem.code }}/{{ oprateItem.name }}</td>
            <!--<td>{{ oprateItem.periodicity }}</td>-->
            <!--<td>{{ oprateItem.name }}</td>
            <td>{{ oprateItem.categoryName }}</td>
            <td>{{ oprateItem.isCyclicity ? '是' :'否' }}</td>-->
            <td>{{ oprateItem.createName }} {{ formatTimestamp(oprateItem.createTime) }}</td>
          </tr>
        </table>
      </div>
    </div>
    <!--<div style="padding-bottom: 20px;">
      <ul class="ty-secondTab " >
        <li :class="oprateContainerData.secondTabNum === 1 ?'ty-active':''" @click="changeTab(1)">准备工作</li>
        <li :class="oprateContainerData.secondTabNum === 2 ?'ty-active':''" @click="changeTab(2)">正式操作</li>
        <li :class="oprateContainerData.secondTabNum === 3 ?'ty-active':''" @click="changeTab(3)">收尾工作</li>
      </ul>
    </div>-->


    <!-- 操作排序 -->
    <div class="oprateOrder" v-show="mainPage==='oprateOrder'">
      <div>
        <div class="ty-right">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="selectOprateOrdOk" v-show="statbegn3 === true">确定</span>
          <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-5" @click.stop style="cursor:not-allowed;" v-show="statbegn3 === false">确定</span>
        </div>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainpageSet('homeset')">返回首页</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 marL30" @click="mainpageSet('tolooksetup')">返回上一页</span>
      </div>
      <p v-if="oprateOrderData.editOprate === null"></p>
      <p class="p1" v-else> {{ oprateOrderData.editOprate.name }} / {{ oprateOrderData.editOprate.code }} </p>
      <hr>
      <p class="p2">
        要把本操作排到哪里？请选择！
        <el-radio-group v-model="oprateOrderData.orderOprateItemIndex">
          <el-radio :label="1" style="margin-left: 100px;"
                    @click.native.prevent="getordent(1,oprateOrderData)">排在某项后面</el-radio>
          <el-radio :label="0" style="margin-left: 100px;"
                    @click.native.prevent="getordent(0,oprateOrderData)">排到最前面</el-radio>
        </el-radio-group>
      </p>
      <div>
        <table class="ty-table ty-table-control ty-table-headerLine">
          <thead class="coldor">
          <tr>
            <td>选择</td>
            <td>项目</td>
            <!--<td>周期属性</td>
            <td>操作人员需进行的操作</td>
            <td>标签</td>
            <td>是否为周期性操作</td>-->
          </tr>
          </thead>
          <tr v-for="(oprateOrdItem, indexOprateOrd) in oprateOrderData.oprateList " :key="indexOprateOrd">
            <td>
              <el-radio v-model="oprateOrderData.orderOprateItemIndex2"
                        :label="oprateOrdItem.productProcessId || oprateOrdItem.activity"
                        @click.native.prevent="getnetochoe(oprateOrdItem.productProcessId || oprateOrdItem.activity,oprateOrderData)">&nbsp;</el-radio>
              <!--<el-radio-group v-model="oprateOrderData.orderOprateItemIndex2">
                <el-radio :label="1" @click.native.prevent="getnetochoe(1,oprateOrderData)">&nbsp;</el-radio>
              </el-radio-group>-->
            </td>
            <td>{{ oprateOrdItem.timing }}{{ oprateOrdItem.kind }}{{ oprateOrdItem.code }}/{{ oprateOrdItem.name }}</td>
            <!--<td>操作次数的值/时间单位前的值 时间单位</td>
            <td>{{ oprateOrdItem.name }}</td>
            <td>{{ oprateOrdItem.categoryName }}</td>
            <td>{{ oprateOrdItem.isCyclicity ? '是' :'否' }}</td>-->
          </tr>
        </table>
      </div>
    </div>
    <!-- 工序排序 -->
    <div class="process1Order" v-show="mainPage==='process1Order'">
      <div>
        <div class="ty-right">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="selectProcess1OrdOk" v-show="statbegn === true">确定</span>
          <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-5" v-show="statbegn === false" @click.stop style="cursor: not-allowed;">确定</span>
        </div>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainpageSet('homeset')">返回首页</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 marL30" @click="mainpageSet('tolooksetup')">返回上一页</span>
      </div>
      <p v-if="process1OrderData.editProcess1 === null"></p>
      <p class="p1" v-else>{{ process1OrderData.editProcess1.name }} / {{ process1OrderData.editProcess1.code }}</p>
      <hr>
      <p class="p2">
        要把本工序排到哪里？请选择！
        <el-radio-group v-model="process1OrderData.orderProcess1ItemIndex">
          <el-radio :label="1" style="margin-left: 100px;"
                    @click.native.prevent="gethident(1,process1OrderData)">排在某项后面</el-radio>
          <el-radio :label="0" style="margin-left: 100px;"
                    @click.native.prevent="gethident(0,process1OrderData)">排到最前面</el-radio>
        </el-radio-group>
        <!--<el-radio-group v-model="process1OrderData.orderProcess1ItemIndex">
          <el-radio :label="1" style="margin-left: 100px;" @click.native.prevent="gethident(1,process1OrderData)">排到某项后面</el-radio>
          <el-radio :label="0" style="margin-left: 100px;" @click.native.prevent="gethident(0,process1OrderData)">排到最前面</el-radio>
        </el-radio-group>
        相当于是选中‘排列某项后面‘后还需要选中下面表格中的一条数据，否则不允许点击右上角的确定按钮-->
      </p>
      <div>
        <!--现在这个位置的选中逻辑改成了：选中‘排到某项后面’后，需要再选中列表中的一条数据，右上角的按钮才可点击-->
        <table class="ty-table ty-table-control ty-table-headerLine">
          <thead class="coldor">
          <tr>
            <td>选择</td>
            <td>项目</td>
            <!--<td>周期属性</td>-->
          </tr>
          </thead>
          <tr v-for="(process1OrdItem,indexProcess1Ord) in process1OrderData.process1List" :key="indexProcess1Ord">
            <td>
              <el-radio-group v-model="process1OrderData.orderProcess1ItemIndex2">
                <el-radio :label="process1OrdItem.productProcessId || process1OrdItem.process"
                          @click.native.prevent="getnetchoe(process1OrdItem.productProcessId || process1OrdItem.process,process1OrderData)"
                >&nbsp;</el-radio>
              </el-radio-group>
              <!--<el-radio
                  v-model="process1OrderData.orderProcess1ItemIndex2"
                  :label="1"
                  @click.native.prevent="getnetchoe(1,process1OrderData)"
              >选中
              </el-radio>-->
              <!--<el-radio-group v-model="process1OrderData.orderProcess1ItemIndex2">
                <el-radio :label="1" @click.native.prevent="getnetchoe(1,process1OrderData)">&nbsp;</el-radio>
              <el-radio :label="1" @click="getnetchoe(1,process1OrderData)">&nbsp;</el-radio>
              </el-radio-group>-->
              <!--<el-radio-group v-model="process1OrderData.orderProcess1ItemIndex2" @change="condetend(process1OrderData)">
                <el-radio :label="1" @click.native.prevent="getnetchoe(1,process1OrderData)">&nbsp;</el-radio>
              </el-radio-group>-->
            </td>
            <td>{{ process1OrdItem.timing }}{{ process1OrdItem.kind }}{{ process1OrdItem.code }}/{{ process1OrdItem.name }}</td>
            <!--<td>操作次数的值/时间单位前的值 时间单位</td>
            <td>{{ process1OrdItem.code }}</td>
            <td>{{ process1OrdItem.name }}</td>-->
          </tr>
        </table>
        <!--        {{ process1OrderData.orderProcess1ItemIndex2 }}-->
      </div>
    </div>
    <!-- 子工序排序 -->
    <div class="process2Order" v-show="mainPage === 'process2Order'">
      <div>
        <div class="ty-right">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="selectProcess2OrdOk" v-show="statbegn2 === true">确定</span>
          <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-5" @click.stop style="cursor: not-allowed;" v-show="statbegn2 === false">确定</span>
        </div>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainpageSet('homest')">返回首页</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 marL30" @click="mainpageSet('tolooksetup')">返回上一页</span>
      </div>
      <p v-if="process2OrderData.editProcess2 === null"></p>
      <p class="p1" v-else>{{ process2OrderData.editProcess2.name }} / {{ process2OrderData.editProcess2.code }}</p>
      <hr>
      <p class="p2">
        要把本子工序排到哪里？请选择！
        <el-radio-group v-model="process2OrderData.orderProcess1ItemIndex">
          <el-radio :label="1" style="margin-left: 100px;"
                    @click.native.prevent="getchident(1,process2OrderData)">排在某项后面</el-radio>
          <el-radio :label="0" style="margin-left: 100px;"
                    @click.native.prevent="getchident(0,process2OrderData)">排在最前面</el-radio>
        </el-radio-group>
      </p>
      <div>
        <table class="ty-table ty-table-control ty-table-headerLine">
          <thead class="coldor">
          <tr>
            <td>选择</td>
            <td>项目</td>
            <!--            <td>周期属性</td>-->
          </tr>
          </thead>
          <tr v-for="(process2OrdItem,indexProcess2Ord) in process2OrderData.process2List" :key="indexProcess2Ord">
            <td>
              <el-radio v-model="process2OrderData.orderProcess1ItemIndex2"
                        :label="process2OrdItem.productProcessId || process2OrdItem.activity"
                        @click.native.prevent="getnetchichoe(process2OrdItem.productProcessId || process2OrdItem.activity,process2OrderData)">&nbsp;
              </el-radio>
              <!--<el-radio-group v-model="process2OrderData.orderProcess1ItemIndex2">
                <el-radio :label="1" @click.native.prevent="getnetchichoe(1,process2OrderData)">&nbsp;</el-radio>
              </el-radio-group>-->
            </td>
            <td>{{ process2OrdItem.timing }}{{ process2OrdItem.kind }}{{ process2OrdItem.code }}/{{ process2OrdItem.name }}</td>
            <!--<td>操作次数的值/时间单位前的值 时间单位</td>
          <td>{{ process2OrdItem.code }}</td>
           <td>{{ process2OrdItem.name }}</td>-->
          </tr>
        </table>
      </div>
    </div>

    <!--选项管理-->
    <div class="processletion" v-show="mainPage === 'processletion'">
      <div>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainpageSet('homeset')">返回</span>
      </div>
      <el-row>
        <el-col :span="24"><div class="grid-content bg-purple-dark">
          <p>在“基础设置”下设置工序、子工序或操作时，都需要在已有选项中选择。</p>
          <p>工序、子工序或操作选项的设置与管理需在本页面进行，</p>
        </div></el-col>
      </el-row>
      <el-divider></el-divider>
      <div class="panel-box">
        <div class="ty-alert">
          <div class="" style="width: 100%;">
            <div class="ty-right" style="margin-left: auto;">
              <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 addpress1-1" @click="addPrecess1Btn(1,'opt','mor')">创建选项</span>
              <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @clic="">管理已有选项</span>
            </div>
            <span>"工序"</span>&nbsp;<span class="wrtdboad">已有选项{{ havoptionData.processNum }}种</span>
          </div>
        </div>
        <div class="ty-alert">
          <div style="width: 100%;">
            <div class="ty-right" style="margin-left: auto;">
              <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 addpress1-1" @click="">组建选项的“套餐”</span>
              <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @clic="">管理已有“套餐”</span>
            </div>
            <span style="margin-left: 161px;">已有选项的"套餐"0种</span>
          </div>
        </div>
      </div>
      <el-divider></el-divider>
      <div class="panel-box">
        <div class="ty-alert">
          <div class="" style="width: 100%;">
            <div class="ty-right" style="margin-left: auto;">
              <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 addpress1" @click="addPrecess1Btn(2,'opt','mor')">创建选项</span>
              <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @clic="">管理已有选项</span>
            </div>
            <span>"子工序"</span>&nbsp;<span class="wrtdboad" style="margin-left: 108px;">已有选项{{ havoptionData.sonProcessNum }}种</span>
          </div>
        </div>
        <div class="ty-alert">
          <div style="width: 100%;">
            <div class="ty-right" style="margin-left: auto;">
              <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 addpress1-1" @click="">组建选项的“套餐”</span>
              <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @clic="">管理已有“套餐”</span>
            </div>
            <span style="margin-left: 161px;">已有选项的"套餐"0种</span>
          </div>
        </div>
      </div>
      <el-divider></el-divider>
      <div class="panel-box">
        <div class="ty-alert">
          <div class="" style="width: 100%;">
            <div class="ty-right" style="margin-left: auto;">
              <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 addpress1" @click="addPrecess1Btn(3,'opt','mor')">创建选项</span>
              <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @clic="">管理已有选项</span>
            </div>
            <span>"操作"</span>&nbsp;<span class="wrtdboad">已有选项{{ havoptionData.operateNum }}种</span>
          </div>
        </div>
        <div class="ty-alert">
          <div style="width: 100%;">
            <div class="ty-right" style="margin-left: auto;">
              <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 addpress1-1" @click="">组建选项的“套餐”</span>
              <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @clic="">管理已有“套餐”</span>
            </div>
            <span style="margin-left: 161px;">已有选项的"套餐"0种</span>
          </div>
        </div>
      </div>
    </div>



    <TyDialog v-show="operateCrolData.operateDelVisible" width="400" dialogTitle="！提示 " color="red" :dialogHide="OperateDelCancel">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="OperateDelCancel">取消</el-button>
        <el-button class="bounce-ok" @click="OperateDelOk">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center">
          {{ operateCrolData.tip  }}
        </div>
      </template>
    </TyDialog>
    <TyDialog v-show="process1CrolData.process1DelVisible" width="400" dialogTitle="！提示" color="red" :dialogHide="Process1DelCancel">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="Process1DelCancel">取消</el-button>
        <el-button class="bounce-ok" @click="Process1DelOk">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center">
          {{ process1CrolData.pro1tip }}
        </div>
      </template>
    </TyDialog>
    <TyDialog v-show="process2CrolData.process2DelVisible" width="400" dialogTitle="！提示" color="red" :dialogHide="Process2DelCancel">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="Process2DelCancel">取消</el-button>
        <el-button class="bounce-ok" @click="Process2DelOk">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center">
          {{ process2CrolData.pro2tip }}
        </div>
      </template>
    </TyDialog>
    <TyDialog  v-show="stvisible" width="400" dialogTitle="！提示" color="red" :dialogHide="stvisCancel">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="stvisCancel">取消</el-button>
        <el-button class="bounce-ok" @click="stvisOk">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center">
          <span v-show="setten === 1">确定后，工序状态将被更改为不允许工序带有子工序。</span>
          <span v-show="setten === 2">确定后，工序状态将被更改为允许工序下设置子工序。</span>
          <br />
          确定改变状态吗？
        </div>
      </template>
    </TyDialog>
    <TyDialog v-show="mdesurn" width="400" dialogTitle="！提示" color="red" :dialogHide="mdesurnCancel">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="mdesurnCancel">知道了</el-button>
      </template>
      <template #dialogBody>
        <p>您所选择的工序或子工序中，有些工序下需设置“子工序”或“操作”，有些子工序下需设置“操作”。</p>
        <p>请设置完成！</p>
      </template>
    </TyDialog>
    <TyDialog v-show="tckming" width="650" dialogTitle="操作说明" color="blue" :dialogHide="tckmiCancel">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="tckmiCancel">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-left plctr">
          1、请根据实际情况，完成产品的工序配置。
          <br />
          <br />
          2、配置产品的工序，既可点击“添加工序”，也可点击“选择模板”。
          <br />
          <br />
          3、配置某道工序下的各步操作，方式为点击“ 添加本工序下的操作”按钮。
          <br />
          <br />
          4、设有子工序的工序，配置各步操作前需先配置子工序。
          <br />
          <br />
          5、配置时，可根据需要进行“查看”、“排序”或“删除”等操作。
          <br />
          <br />
          6、如确认配置完成，则请勾选页面右上角的选项，之后点击“确定”按钮。
        </div>
      </template>
    </TyDialog>


    <TyDialog v-show="addPress1Data.visible" width="580" dialogTitle='创建"工序"的选项' color="green" :dialogHide="addPrecess1Cancel">
      <template #dialogFooter>
        <span class="ty-btn ty-circle-3 ty-btn-big bounce-cancel" @click="addPrecess1Cancel">取消</span>
        <span class="ty-btn ty-circle-3 ty-btn-big bounce-ok" @click="addPrecess1Ok" v-show="addPress1Data.clock === true">确定</span>
        <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-3" @click.stop style="cursor: not-allowed;color: #101010;"
              v-show="addPress1Data.clock === false" :class="{'disabled-button': !isFormComplete1}">确定</span>
      </template>
      <template #dialogBody>
        <div>
          <el-form label-position="top" label-width="80px" :model="addPress1Data.formData" :rules="addPress1Data.rules"
              ref="all1formRef">
            <el-form-item label="代号" prop="code">
              <el-input v-model="addPress1Data.formData.code" placeholder="请录入"></el-input>
            </el-form-item>
            <el-form-item label="名称" prop="name">
              <el-input v-model="addPress1Data.formData.name" placeholder="请录入"></el-input>
            </el-form-item>
            <span class="ty-right">
                <span class="ty-linkBtn ty-linkBtn-blue stopcat" @click="Cateoryagment(1)">标签管理</span>
                <span class="ty-linkBtn ty-linkBtn-blue stopcat" @click="addCateoy(1)">新增标签</span>
            </span>
            <el-form-item label="标签" prop="cat">
              <el-select v-model="addPress1Data.formData.cat" placeholder="请选择" style="width: 100%;">
                <el-option
                    v-for="item in addPress1Data.optionsCat"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                >
                </el-option>
              </el-select>
              <p class="ty-color-blue">注：选择工序、子工序或操作的选项时，通过标签进行筛选可提高效率！</p>
            </el-form-item>
            <el-form-item label="本工序下是否设置“子工序”或“操作”？" prop="gxcat">
              <div>
                <el-radio-group v-model="addPress1Data.formData.gxcat">
                  <el-radio :label="1" @click.native.prevent="gxsetcat(1)">设置</el-radio>
                  <el-radio :label="2" @click.native.prevent="gxsetcat(2)">不设置</el-radio>
                </el-radio-group>
              </div>
            </el-form-item>
            <el-form-item label="时间点属性" prop="time" v-show="addPress1Data.timecs === 1">
              <el-select v-model="addPress1Data.formData.time" placeholder="请选择" style="width: 100%;" @change="timehav1">
                <el-option v-for="item1 in addPress1Data.optionsTime1" :key="item1.id" :label="item1.name" :value="item1.id"></el-option>
              </el-select>
            </el-form-item>
            <span class="ty-right" v-show="addPress1Data.catoglk1 === 2">
              <el-radio-group v-model="addPress1Data.formData.catog1" @change="handleRadioChange1(1)">
                <el-radio :label="0" @click.native.prevent="handleClick1(0,1)">主要操作</el-radio>
                <el-radio :label="1" @click.native.prevent="handleClick1(1,1)">辅助性操作</el-radio>
              </el-radio-group>
            </span>
            <el-form-item label="类别" prop="catog1" v-show="addPress1Data.catoglk1 === 2"></el-form-item>
            <!--<span class="ty-right" v-if="addPress1Data.isCyiy === 2">
              <el-radio-group v-model="addPress1Data.formData.isCyclicity" @change="handleRadioChange1(2)">
                <el-radio :label="0" @click.native.prevent="handleClick1(0,2)">是</el-radio>
                <el-radio :label="1" @click.native.prevent="handleClick1(1,2)">不是</el-radio>
              </el-radio-group>
            </span>
            <el-form-item label='是否为周期性"操作"' prop="isCyclicity" v-if="addPress1Data.isCyiy === 2"></el-form-item>
            <el-form-item label="请选择周期的形式" prop="pattern" v-if="addPress1Data.pate === 0">
              <el-selct v-model="addPress1Data.formDataw1.pattern" placeholder="请选择" @change="getshowse1">
                <el-option v-for="itemw1 in addPress1Data.optionsweek1" :key="itemw1.id" :label="itemw1.name"
                           :value="itemw1.id" :disabled="itemw1.disabled">
                </el-option>
              </el-selct>
            </el-form-item>
            <el-form-item label="请确定多长时间需进行多少次操作" prop="frequency" :inline="true" v-if="addPress1Data.fret1 === 1">
              <el-input v-model="addPress1Data.formData.frequency" placeholder="请录入数字" onkeyup="value=value.replace(/\D/g,'')"
                        onafterpaste="value=value.repace(/\D/g,'')" style="width: 105px;margin-right: 2px;"></el-input>
              <el-select v-model="addPress1Data.formData.timeUnit" placeholder="请选择时间单位"
                         style="width: 110px;margin-right: 3px;">
                <el-option v-for="item13 in addPress1Data.timeUnitweek" :key="item13.name" :value="item13.id"></el-option>
              </el-select>
              需进行
              <el-input v-model="addPress1Data.formData.executeTimes" placeholder="请录入数字" onkeyup="value=value.replace(/\D/g,'')"
                        onafterpaste="value=value.replace(/\D/g,'')" style="width: 105px;margin-left: 3px;margin-right: 4px;"></el-input>
              次本操作
            </el-form-item>-->
          </el-form>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-show="addPress2Data.visible" width="580" dialogTitle='创建"子工序"的选项' color="green" :dialogHide="addPrecess2Cancel">
      <template #dialogFooter>
        <span class="ty-btn ty-circle-3 ty-btn-big bounce-cancel" @click="addPrecess2Cancel">取消</span>
        <span class="ty-btn ty-circle-3 ty-btn-big bounce-ok" @click="addPrecess2Ok" v-show="addPress2Data.clock === true">确定</span>
        <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-3" @click.stop style="cursor: not-allowed;color: #101010;"
              v-show="addPress2Data.clock === false" :class="{'disabled-button': !isFormComplete2}">确定</span>
      </template>
      <template #dialogBody>
        <div>
          <el-form label-position="top" label-width="80px" :model="addPress2Data.formData2" :rules="addPress2Data.rules2"
              ref="all2formRef">
            <el-form-item label="代号" prop="code">
              <el-input v-model="addPress2Data.formData2.code" placeholder="请录入"></el-input>
            </el-form-item>
            <el-form-item label="名称" prop="name">
              <el-input v-model="addPress2Data.formData2.name" placeholder="请录入"></el-input>
            </el-form-item>
            <span class="ty-right">
              <span class="ty-linkBtn ty-linkBtn-blue stopcat" @click="process2catment">标签管理</span>
              <span class="ty-linkBtn ty-linkBtn-blue stopcat" @click="addpro2Cary(2)">新增标签</span>
            </span>
            <el-form-item label="标签" prop="cat">
              <el-select v-model="addPress2Data.formData2.cat" placeholder="请选择" style="width: 100%;">
                <el-option
                    v-for="item2 in addPress2Data.optionsCat2"
                    :key="item2.id"
                    :label="item2.name"
                    :value="item2.id"
                ></el-option>
              </el-select>
              <p class="ty-color-blue">注：选择工序、子工序或操作的选项时，通过标签进行筛选可提高效率！</p>
            </el-form-item>
            <el-form-item label="本子工序下是否需设置“操作”？" prop="chidcat">
              <div>
                <el-radio-group v-model="addPress2Data.formData2.chidcat">
                  <el-radio :label="1" @click.native.prevent="setchiat(1)">设置</el-radio>
                  <el-radio :label="2" @click.native.prevent="setchiat(2)">不设置</el-radio>
                </el-radio-group>
              </div>
            </el-form-item>
            <el-form-item label="时间点属性" prop="time" v-show="addPress2Data.timecs === 1">
              <el-select v-model="addPress2Data.formData2.time" placeholder="请选择" style="width: 100%;" @change="timehav2">
                <el-option
                    v-for="item34 in addPress2Data.optionsTime2" :key="item34.id" :label="item34.name" :value="item34.id"></el-option>
              </el-select>
            </el-form-item>
            <span class="ty-right" v-show="addPress2Data.catoglk === 2">
              <el-radio-group v-model="addPress2Data.formData2.catog" @change="handleRadioChange2(1)">
                <el-radio :label="0" @click.native.prevent="handleClick2(0,1)">主要操作</el-radio>
                <el-radio :label="1" @click.native.prevent="handleClick2(1,1)">辅助性操作</el-radio>
              </el-radio-group>
            </span>
            <el-form-item label="类别" prop="catog" v-show="addPress2Data.catoglk === 2"></el-form-item>
            <!--<span class="ty-right" v-if="addPress2Data.isCyiy === 2">
                <el-radio-group v-model="addPress2Data.formData2.isCyclicity" @change="handleRadioChange2(2)">
                  <el-radio :label="0" @click.native.prevent="handleClick2(0,2)">是</el-radio>
                  <el-radio :label="1" @click.native.prevent="handleClick2(1,2)">不是</el-radio>
                </el-radio-group>
              </span>
            <el-form-item label='是否为周期性"操作"' prop="isCyclicity" v-if="addPress2Data.isCyiy === 2"></el-form-item>
            <el-form-item label="请选择周期的形式" prop="pattern" v-if="addPress2Data.pate === 0">
              <el-select v-model="addPress2Data.formDataw2.pattern" placeholder="请选择" @change="getshowse2">
                <el-option v-for="itemw in addPress2Data.optionsweek" :key="itemw.id" :label="itemw.name"
                           :value="itemw.id" :disabled="itemw.disabled">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="请确定多长时间需进行多少次操作" prop="frequency" :inline="true" v-if="addPress2Data.fret === 1">
              <el-input v-model="addPress2Data.formDataw2.frequency" placeholder="请录入数字" onkeyup="value=value.replace(/\D/g,'')"
                        onafterpaste="value=value.replace(/\D/g,'')" style="width: 105px;margin-right:2px;"></el-input>
              <el-select v-model="addPress2Data.formDataw2.timeUnit" placeholder="请选择时间单位"
                         style="width: 110px;margin-right: 3px;">
                <el-option v-for="item33 in addPress2Data.timeUnitweek" :key="item33.id" :label="item33.name" :value="item33.id"></el-option>
              </el-select>
              需进行
              <el-input v-model="addPress2Data.formDataw2.executeTimes" placeholder="请录入数字" onkeyup="value=value.replace(/\D/g,'')"
                        onafterpaste="value=value.replace(/\D/g,'')" style="width: 105px;margin-left: 3px;margin-right: 4px;"></el-input>
              次本操作
            </el-form-item>-->
          </el-form>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-show="addPress3Data.visible" width="600" dialogTitle='创建"操作"的选项' color="green" :dialogHide="addPrecess3Cancel">
      <template #dialogFooter>
        <span class="ty-btn ty-circle-3 ty-btn-big bounce-cancel" @click="addPrecess3Cancel">取消</span>
        <span class="ty-btn ty-circle-3 ty-btn-big bounce-ok" @click="addPrecess3Ok" v-show="addPress3Data.clock === true">确定</span>
        <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-3" @click.stop style="cursor: not-allowed;color: #101010;"
              v-show="addPress3Data.clock === false" :class="{'disabled-button': !isFormComplete3}">确定</span>
      </template>
      <template #dialogBody>
        <div>
          <el-form label-position="top" label-width="80px" :model="addPress3Data.formData3" :rules="addPress3Data.rules3"
              ref="all3formRef">
            <el-form-item label="代号" prop="code">
              <el-input v-model="addPress3Data.formData3.code" placeholder="请录入"></el-input>
            </el-form-item>
            <el-form-item label="名称" prop="name">
              <el-input v-model="addPress3Data.formData3.name" placeholder="请录入"></el-input>
            </el-form-item>
            <span class="ty-right">
              <span class="ty-linkBtn ty-linkBtn-blue stopcat" @click="process3catment">标签管理</span>
              <span class="ty-linkBtn ty-linkBtn-blue stopcat" @click="addpro3Cary(3)">新增标签</span>
            </span>
            <el-form-item label="标签" prop="cat">
              <el-select v-model="addPress3Data.formData3.cat" placeholder="请选择" style="width: 100%;">
                <el-option
                    v-for="item3 in addPress3Data.optionsCat3"
                    :key="item3.id"
                    :label="item3.name"
                    :value="item3.id"
                ></el-option>
              </el-select>
              <p class="ty-color-blue">注：选择工序、子工序或操作的选项时，通过标签进行筛选可提高效率！</p>
            </el-form-item>
            <el-form-item label="时间点属性" prop="time">
              <el-select v-model="addPress3Data.formData3.time" placeholder="请选择" style="width: 100%;" @change="timehav">
                <el-option
                    v-for="item32 in addPress3Data.optionsTime3"
                    :key="item32.id"
                    :label="item32.name"
                    :value="item32.id"
                ></el-option>
              </el-select>
            </el-form-item>
            <span class="ty-right" v-show="addPress3Data.catoglk === 2">
              <el-radio-group v-model="addPress3Data.formData3.catog" @change="handleRadioChange(1)">
                <el-radio :label="0" @click.native.prevent="handleClick(0,1)">主要操作</el-radio>
                <el-radio :label="1" @click.native.prevent="handleClick(1,1)">辅助性操作</el-radio>
              </el-radio-group>
            </span>
            <el-form-item label="类别" prop="catog" v-show="addPress3Data.catoglk === 2"></el-form-item>
            <!--<span class="ty-right" v-if="addPress3Data.isCyiy === 2">
              <el-radio-group v-model="addPress3Data.formData3.isCyclicity" @change="handleRadioChange(2)">
                <el-radio :label="0" @click.native.prevent="handleClick(0,2)">是</el-radio>
                <el-radio :label="1" @click.native.prevent="handleClick(1,2)">不是</el-radio>
              </el-radio-group>
            </span>
            <el-form-item label='是否为周期性"操作“' prop="isCyclicity" v-if="addPress3Data.isCyiy === 2"></el-form-item>
            <el-form-item label="请选择周期的形式" prop="pattern" v-if="addPress3Data.pate === 0">
              <el-select v-model="addPress3Data.formDataw.pattern" placeholder="请选择" @change="getshowse">
                <el-option
                    v-for="itemw in addPress3Data.optionsweek"
                    :key="itemw.id" :label="itemw.name" :value="itemw.id" :disabled="itemw.disabled">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="请确定多长时间需进行多少次本操作" prop="frequency" :inline="true" v-if="addPress3Data.fret === 1">
              <el-input v-model="addPress3Data.formDataw.frequency" placeholder="请录入数字" onkeyup="value=value.replace(/\D/g,'')"
                        onafterpaste="value=value.replace(/\D/g,'')" style="width: 120px;margin-right:2px;"></el-input>
              <el-select v-model="addPress3Data.formDataw.timeUnit" placeholder="请选择时间单位"
                         style="width: 200px;margin-right:3px;">
                <el-option v-for="item33 in addPress3Data.timeUnitweek"
                           :key="item33.id" :label="item33.name" :value="item33.id"></el-option>
              </el-select>
              需进行
              <el-input v-model="addPress3Data.formDataw.executeTimes" placeholder="请录入数字" onkeyup="value=value.replace(/\D/g,'')"
                        onafterpaste="value=value.replace(/\D/g,'')" style="width: 120px;margin-left: 3px;margin-right: 4px;"></el-input>
              次本操作
            </el-form-item>
            <el-form-item label='是否为周期性"操作"？' prop="isCyclicity" style="margin-bottom: 0;">
              <div>
                <el-radio v-model="radio1" label="1" @change="getCyclic">不是</el-radio>
                <el-radio v-model="radio1" label="2" @change="getCyclic">是</el-radio>
              </div>
              <div style="margin-left: auto;">
                <el-button type="text" disabled v-if="timk1">编辑周期</el-button>
                <span class="ty-linkBtn ty-linkBtn-blue stopcat" @click="getweekest" v-if="timk2">编辑周期</span>
              </div>
            </el-form-item>
            <el-form-item>
              <span v-if="messgen">每{{ weekData.formDataw.frequency }}{{ weekData.formDataw.timechone }}
                进行{{ weekData.formDataw.executeTimes }}次本操作</span>
            </el-form-item>-->
          </el-form>
        </div>
      </template>
    </TyDialog>
    <!--<TyDialog v-if="weekData.visible" width="800" dialogTitle="周期设置" color="blue" :dialogHide="weekCancel">
      <template #dialogFooter>
        <span class="ty-btn ty-circle-3 ty-btn-big bounce-cancel" @click="weekCancel">取消</span>
        <span class="ty-btn ty-circle-3 ty-btn-big bounce-ok" @click="weekOk">确定</span>
      </template>
      <template #dialogBody>
        <el-form label-position="top" label-width="80px" :model="weekData.formDataw" :rules="weekData.rules">
          <el-form-item label="请选择周期的形式" prop="pattern">
            <el-select v-model="weekData.formDataw.pattern" placeholder="请选择" @change="getshowse" style="width: 100%;">
              <el-option
                  v-for="itemw in weekData.optionsweek"
                  :key="itemw.id"
                  :label="itemw.name"
                  :value="itemw.id"
                  :disabled = "itemw.disabled"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="请确定多长时间需进行多少次本操作" prop="frequency" :inline="true" v-if="looken">
            <el-input v-model="weekData.formDataw.frequency" placeholder="请录入数字"  onkeyup="value=value.replace(/\D/g,'')"
                      onafterpaste="value=value.replace(/\D/g,'')" style="width: 200px;margin-right:2px;"></el-input>
            <el-select v-model="weekData.formDataw.timeUnit" placeholder="请选择时间单位" style="width: 255px;margin-right:3px;">
              <el-option
                  v-for="item33 in weekData.timeUnitweek"
                  :key="item33.id"
                  :label="item33.name"
                  :value="item33.id"
              >
              </el-option>
            </el-select>
            需进行
            <el-input v-model="weekData.formDataw.executeTimes" placeholder="请录入数字" onkeyup="value=value.replace(/\D/g,'')"
                      onafterpaste="value=value.replace(/\D/g,'')" style="width: 200px;margin-left: 3px;margin-right: 4px;"></el-input>
            次本操作
          </el-form-item>
        </el-form>
      </template>
    </TyDialog>-->

    <TyDialog v-show="process1StoData.cateorynagent" width="700" dialogTitle="标签管理" color="blue" :dialogHide="cateorygentCancel">
      <template #dialogFooter>
        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" @click="cateorygentCancel">关闭</span>
      </template>
      <template #dialogBody>
        <el-row>
          <el-col :span="24"><div class="grid-content bg-purple-dark">
            选择工序、子工序或操作的选项时，通过标签进行筛选可提高效率！
          </div></el-col>
        </el-row>
        <el-row class="linklis">
          <el-col :span="10"><div class="grid-content bg-purple">
            “工序”的标签中，在用的如下：
          </div></el-col>
          <el-col :span="2"><div class="grid-content bg-purple-light"></div></el-col>
          <el-col :span="6"><div class="grid-content bg-purple"></div></el-col>
          <el-col :span="6"><div class="grid-content bg-purple-light">
            <el-row>
              <el-col :span="9"><div class="grid-content bg-purple"></div></el-col>
              <el-col :span="15"><div class="grid-content bg-purple-light stopcat">
                <span class="ty-linkBtn ty-linkBtn-blue" @click="stopusecan(1)">被停用的标签</span>
              </div></el-col>
            </el-row>
          </div></el-col>
        </el-row>
        <el-table :data="this.process1StoData.tableData" style="width: 100%" class="ty-table-control">
          <el-table-column prop="name" label="标签名称" width="210" align="center"> </el-table-column>
          <el-table-column label="创建" width="230" align="center">
            <template #default="scope">
              {{ scope.row.createName }}&nbsp;{{ formatTimestamp(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <span class="ty-color-blue stopcat" @click="stopusen('sto',scope.row,scope.$index,scope.row.id)">停用</span>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </TyDialog>
    <TyDialog v-show="process1StoData.stoporyagent" width="900" dialogTitle="被停用的标签" color="blue" :dialogHide="stoporyagentCancel">
      <template #dialogFooter>
        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" @click="stoporyagentCancel">关闭</span>
      </template>
      <template #dialogBody>
        <el-row>
          <el-col :span="24"><div class="grid-content bg-purple-dark">
            “工序”的标签中，已被停用的如下：
          </div></el-col>
        </el-row>
        <el-table :data="this.process1StoData.tableData2" style="width: 100%" class="ty-table-control">
          <el-table-column prop="name" label="标签名称" width="190" align="center"> </el-table-column>
          <el-table-column label="本次停用" width="230" align="center">
            <template #default="scope">
              {{ scope.row.updateName }}&nbsp;{{ formatTimestamp(scope.row.updateTime) }}
            </template>
          </el-table-column>
          <el-table-column label="创建记录" width="200" align="center">
            <template #default="scope">
              {{ scope.row.createName }}&nbsp;{{ formatTimestamp(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <span class="ty-color-blue stopcat" @click="stopusen('str',scope.row,scope.$index,scope.row.id)">复用</span>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </TyDialog>
    <TyDialog v-show="process1StoData.process1StoVisible" width="400" dialogTitle="！提示" color="red" :dialogHide="Process1StoCancel">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="Process1StoCancel">取消</el-button>
        <el-button class="bounce-ok" @click="Process1StoOk">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center allplnce">
          {{ process1StoData.pro1top }}
        </div>
      </template>
    </TyDialog>

    <TyDialog v-show="addcatent" width="500" dialogTitle="新增标签" color="green" :dialog-Hide="addcatcancel">
      <template #dialogFooter>
        <span class="ty-btn ty-circle-3 ty-btn-big bounce-cancel" @click="addcatcancel">取消</span>
        <span class="ty-btn ty-circle-3 ty-btn-big bounce-ok" @click="addcatOk" v-show="addcatData.clock === true">确定</span>
        <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-3" @click.stop style="cursor:not-allowed;color: #101010;"
              v-show="addcatData.clock === false">确定</span>
      </template>
      <template #dialogBody>
        <div>
          <el-form label-position="top" label-width="80px" :model="addcatData.formData" :rules="addcatData.rules"
                   ref="formRef">
            <el-form-item label="标签名称" prop="name">
              <el-input v-model="addcatData.formData.name" placeholder="请录入"  @input="handleInputChange"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <el-row>
          <el-col :span="24"><div class="grid-content bg-purple-dark">
            <span class="ty-color-blue">注：选择选项时，先按标签筛选，查找更容易！</span>
          </div></el-col>
        </el-row>
      </template>
    </TyDialog>
    <TyDialog v-show="addcatent2" width="500" dialogTitle="新增标签" color="green" :dialog-Hide="addcat2cancel">
      <template #dialogFooter>
        <span class="ty-btn ty-circle-3 ty-btn-big bounce-cancel" @click="addcat2cancel">取消</span>
        <span class="ty-btn ty-circle-3 ty-btn-big bounce-ok" @click="addcat2Ok" v-show="addcat2Data.clock2 === true">确定</span>
        <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-3" @click.stop style="cursor: not-allowed;color: #101010;"
              v-show="addcat2Data.clock2 === false">确定</span>
      </template>
      <template #dialogBody>
        <div>
          <el-form label-position="top" label-width="80px" :model="addcat2Data.form2Data" :rules="addcat2Data.rules2"
            ref="chiformRef">
            <el-form-item label="标签名称" prop="name2"><!--prop指向的是规则的，使用required时是需要rules+prop一起，所以prop的值需要跟rules中的值相同-->
              <el-input v-model="addcat2Data.form2Data.name2" placeholder="请录入" @input="handleInputChange2"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <el-row>
          <el-col :span="24"><div class="grid-content bg-purple-dark">
            <span class="ty-color-blue">注：选择选项时，先按标签筛选，查找更容易！</span>
          </div></el-col>
        </el-row>
      </template>
    </TyDialog>
    <TyDialog v-show="addcatent3" width="500" dialogTitle="新增标签" color="green" :dialog-Hide="addcat3cancel">
      <template #dialogFooter>
        <span class="ty-btn ty-circle-3 ty-btn-big bounce-cancel" @click="addcat3cancel">取消</span>
        <span class="ty-btn ty-circle-3 ty-btn-big bounce-ok" @click="addcat3Ok" v-show="addcat3Data.clock3 === true">确定</span>
        <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-3" @click.stop style="cursor: not-allowed;color: #101010;"
              v-show="addcat3Data.clock3 === false">确定</span>
      </template>
      <template #dialogBody>
        <div>
          <el-form label-position="top" label-width="80px" :model="addcat3Data.form3Data" :rules="addcat3Data.rules3"
            ref="opformRef">
            <el-form-item label="标签名称" prop="name3">
              <el-input v-model="addcat3Data.form3Data.name3" placeholder="请录入" @input="handleInputChange3"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <el-row>
          <el-col :span="24"><div class="grid-content bg-purple-dark">
            <span class="ty-color-blue">注：选择选项时，先按标签筛选，查找更容易！</span>
          </div></el-col>
        </el-row>
      </template>
    </TyDialog>

    <TyDialog v-show="process2StoData.cateorynagent2" width="700" dialogTitle="标签管理" color="blue" :dialogHide="process2cateCancel">
      <template #dialogFooter>
        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" @click="process2cateCancel">关闭</span>
      </template>
      <template #dialogBody>
        <el-row>
          <el-col :span="24"><div class="grid-content bg-purple-dark">
            选择工序、子工序或操作的选项时，通过标签进行筛选可提高效率！
          </div></el-col>
        </el-row>
        <el-row class="linklis">
          <el-col :span="10"><div class="grid-content bg-purple">
            “子工序”的标签中，在用的如下：
          </div></el-col>
          <el-col :span="2"><div class="grid-content bg-purple-light"></div></el-col>
          <el-col :span="6"><div class="grid-content bg-purple"></div></el-col>
          <el-col :span="6"><div class="grid-content bg-purple-light">
            <el-row>
              <el-col :span="9"><div class="grid-content bg-purple"></div></el-col>
              <el-col :span="15"><div class="grid-content bg-purple-light stopcat">
                <span class="ty-linkBtn ty-linkBtn-blue" @click="process2stocat(2)">被停用的标签</span>
              </div></el-col>
            </el-row>
          </div></el-col>
        </el-row>
        <el-table :data="this.process2StoData.tableData2" style="width: 100%" class="ty-table-control">
          <el-table-column prop="name" label="标签名称" width="210" align="center"> </el-table-column>
          <el-table-column label="创建" width="230" align="center">
            <template #default="scope">
              {{ scope.row.createName }}&nbsp;{{ formatTimestamp(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <span class="ty-color-blue stopcat" @click="process2stouse('sto',scope.row,scope.$index,scope.row.id)">停用</span>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </TyDialog>
    <TyDialog v-show="process2StoData.stoporyagent2" width="900" dialogTitle="被停用的标签" color="blue" :dialogHide="process2yenCancel">
      <template #dialogFooter>
        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" @click="process2yenCancel">关闭</span>
      </template>
      <template #dialogBody>
        <el-row>
          <el-col :span="24"><div class="grid-content bg-purple-dark">
            “子工序”的标签中，已被停用的如下：
          </div></el-col>
        </el-row>
        <el-table :data="this.process2StoData.tableData22" style="width: 100%" class="ty-table-control">
          <el-table-column prop="name" label="标签名称" width="190" align="center"> </el-table-column>
          <el-table-column label="本次停用" width="230" align="center">
            <template #default="scope">
              {{ scope.row.updateName }}&nbsp;{{ formatTimestamp(scope.row.updateTime) }}
            </template>
          </el-table-column>
          <el-table-column label="创建记录" width="200" align="center">
            <template #default="scope">
              {{ scope.row.createName }}&nbsp;{{ formatTimestamp(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <span class="ty-color-blue stopcat" @click="process2stouse('str',scope.row,scope.$index,scope.row.id)">复用</span>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </TyDialog>
    <TyDialog v-show="process2StoData.process2StoVisible" width="400" dialogTitle="！提示" color="red" :dialogHide="Process2StoCancel">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="Process2StoCancel">取消</el-button>
        <el-button class="bounce-ok" @click="Process2StoOk">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center allplnce">
          {{ process2StoData.pro2top }}
        </div>
      </template>
    </TyDialog>


    <TyDialog v-show="process3StoData.cateorynagent3" width="700" dialogTitle="标签管理" color="blue" :dialogHide="process3cateCancel">
      <template #dialogFooter>
        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" @click="process3cateCancel">关闭</span>
      </template>
      <template #dialogBody>
        <el-row>
          <el-col :span="24"><div class="grid-content bg-purple-dark">
            选择工序、子工序或操作的选项时，通过标签进行筛选可提高效率！
          </div></el-col>
        </el-row>
        <el-row class="linklis">
          <el-col :span="10"><div class="grid-content bg-purple">
            “操作”的标签中，在用的如下：
          </div></el-col>
          <el-col :span="2"><div class="grid-content bg-purple-light"></div></el-col>
          <el-col :span="6"><div class="grid-content bg-purple"></div></el-col>
          <el-col :span="6"><div class="grid-content bg-purple-light">
            <el-row>
              <el-col :span="9"><div class="grid-content bg-purple"></div></el-col>
              <el-col :span="15"><div class="grid-content bg-purple-light stopcat">
                <span class="ty-linkBtn ty-linkBtn-blue" @click="process3stocat(3)">被停用的标签</span>
              </div></el-col>
            </el-row>
          </div></el-col>
        </el-row>
        <el-table :data="this.process3StoData.tableData3" style="width: 100%" class="ty-table-control">
          <el-table-column prop="name" label="标签名称" width="210" align="center"> </el-table-column>
          <el-table-column label="创建" width="230" align="center">
            <template #default="scope">
              {{ scope.row.createName }}&nbsp;{{ formatTimestamp(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <span class="ty-color-blue stopcat" @click="process3stouse('sto',scope.row,scope.$index,scope.row.id)">停用</span>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </TyDialog>
    <TyDialog v-show="process3StoData.stoporyagent3" width="900" dialogTitle="被停用的标签" color="blue" :dialogHide="process3yenCancel">
      <template #dialogFooter>
        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" @click="process3yenCancel">关闭</span>
      </template>
      <template #dialogBody>
        <el-row>
          <el-col :span="24"><div class="grid-content bg-purple-dark">
            “操作”的标签中，已被停用的如下：
          </div></el-col>
        </el-row>
        <el-table :data="this.process3StoData.tableData32" style="width: 100%" class="ty-table-control">
          <el-table-column prop="name" label="标签名称" width="190" align="center"> </el-table-column>
          <el-table-column label="本次停用" width="230" align="center">
            <template #default="scope">
              {{ scope.row.updateName }}&nbsp;{{ formatTimestamp(scope.row.updateTime) }}
            </template>
          </el-table-column>
          <el-table-column label="创建记录" width="200" align="center">
            <template #default="scope">
              {{ scope.row.createName }}&nbsp;{{ formatTimestamp(scope.row.createTime) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <span class="ty-color-blue stopcat" @click="process3stouse('str',scope.row,scope.$index,scope.row.id)">复用</span>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </TyDialog>
    <TyDialog v-show="process3StoData.process3StoVisible" width="400" dialogTitle="！提示" color="red" :dialogHide="Process3StoCancel">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="Process3StoCancel">取消</el-button>
        <el-button class="bounce-ok" @click="Process3StoOk">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center allplnce">
          {{ process3StoData.pro3top }}
        </div>
      </template>
    </TyDialog>

  </div>
</template>
<script>
import { beforeRouteLeave, initNav } from "@/utils/routeChange"
import { getProcessSettingsList, getToManageList, getSettingsList,
  getProcessList,getCategoryList, getSonProcessList,getOperateList,
  chooseProcess, createProcess,
} from "@/api/processManagement.js";
import {getDemoList} from "../../api/api";
import {
  addcatCory,
  catuseory,
  createProcess2,
  getalloption,
  getcatorylist,
  upProcesSettings
} from "../../api/processManagement";


export default {
  data() {
    return {
      pageName:'processSet',
      mainPage:'homeset',
      ordiffen: '',
      pageInfo2d: {},
      pageInfo: {'currentPageNo':1,'pageSize':10,'totalPage': 15},
      collse: '',//判断右上角的按钮是否展示为不可用状态
      collse2: '',//根据确定按钮旁边的选项是否勾选判断确定按钮是否可以点击
      // containerHeight: 0,//容器高度
      // listHeight: 0,//列表高度
      // thumbHeight: 0,//滚动条高度
      // items: [
      //   { id: 1, text:1},{ id: 2, text: 2},{ id:3, text: 3}
      // ],//数据数组
      // containerHeight: 0,//容器高度
      // listHeight: 0,//列表高度
      // thumbHeight: 0,//滚动条高度
      oirdiff: '',//代表是产品还是零组件
      homeSetData:{
        yses1Data: 0,
        edsess1Data: 0,
        yses2Data: 0,
        edsess2Data: 0,
        yses3Data: 0,
        edsess3Data: 0,
        yses4Data: 0,
        edsess4Data: 0,
      },
      tomanageData: [], // 商品列表
      tolooksetupData:{ // 商品设置页 数据   用于存储全部数据
        setOk:'', // 是否 点击了选择完毕
        increase:0, // 1-已经存在 2-新增的（通过判断 productProcessId 是否有值来判断是1还是2，该字段不需要传值）
        editPro: null, // 操作的商品信息
        settingsList:[{
          code : "1-1" , //code
          name :  "工序1" ,
          operateList : [{
            product: '产品id',
            process: '工序id',
            activity: '活动ID(操作id)',
            parent: '父活动ID',
            code: '代号',
            name: '名称',
            orders: '同级排序',
            category: '类别id',
            categoryName: '类别名称',
            type : '类型:2-子工序,3-操作,4-套餐',
            //isCyclicity:'是否周期性:true-是,false-否(默认)'
          }], //工序下的操作列表
          orders :  1, // 同级排序
          org : 3681,
          process :  1, // 工序id
          product :  8801, //产品id
          productProcessId : 306,
          havSonProcess:1, // 1 有, 2 没有
          sonProcesses  :  [{
            product : '产品id',
            process : '工序id',
            activity : '活动ID(子工序id或者是操作id)',
            parent : '父活动ID',
            code : '代号',
            name : '名称',
            orders : '同级排序',
            category : '类别id',
            categoryName : '类别名称',
            type : '类型:2-子工序,3-操作,4-套餐',
            //isCyclicity : '是否周期性:true-是,false-否(默认)',
            sonOperateList : { // 子工序下的操作列表
              product : '产品id',
              process : '工序id',
              activity : '活动ID(操作id)',
              parent : '父活动ID(子工序下有操作时，此值是子工序id)',
              code : '代号',
              name : '名称',
              orders : '同级排序',
              category : '类别id',
              categoryName : '类别名称',
              type : '类型:2-子工序,3-操作,4-套餐',
              //isCyclicity : '是否周期性:true-是,false-否(默认)'
            }
          }]
        }], // 商品 原来的设置
        settingsList2:[], // 商品修改后的 设置
      },
      //chidchis: 0,
      chosesetenData:{ // 选择工序页 数据
        catList:[
          {
            enabled : true ,
            id :  1,
            name : "工序类别1",
            operation :  null ,
            orders :  null,
            previousId :  null,
            type  : 1
          }
        ], // 分类列表
        selectCat:'', // 选择的分类
        processList:[{
          category : 1 ,
          code :  "1-1" ,
          createName :  "张三",
          enabled : true ,
          id :  1,
          isLeaf : null ,
          clkset: '',//1-点击了‘设置’后跳转过来的，2-点击了‘设置’以外的按钮跳转过来的
          name  :  "工序1",
          haschild: 1,//用于区分什么时候可以选中什么时候不能选中,
          // 0-可以设置子工序但还未设置，1-已确认是否可以设置子工序，2-不允许设置子工序
          havSonProcess: false,
          isLocked: false, // 用于标记是否锁定选项
          checkick: false,//false代表不可进行取消勾选，true代表可以进行取消勾选
          selectedOption: '',//用于跟踪此时是哪个选项是勾选状态

        }],// 展示的工序列表
        selectProcess:[],// 选择好的工序
        cantSelectProcess:[
          {
            code : "1-1" ,
            name  :  "工序1" ,
            operateList : [],
            orders :  1,
            org : 3681,
            process :  1,
            product :  8801,
            productProcessId : 306,
            sonProcesses  :  null
          }
        ], // 不能选择的工序
      },
      choseover: 0,//用于判断工序列表中是否有选项被勾选了
      choseover2: 0,//用于判断子工序列表中是否有选项被勾选了
      choseover3: 0,//用于判断操作列表中是否有选项被勾选了
      //创建工序
      addPress1Data:{
        clock: '',//用于判断确定按钮是否可以点击（创建工序弹窗）
        visible:false,
        timecs: '',//时间点属性
        catoglk1: 0,
        isCyiy: 0,
        pate: 1,
        fret1: 0,
        optionsCat:[],
        formData:{
          code: '' ,
          name: '' ,
          cat: '' ,
          gxcat: '',
          time: '',
          catog1: '',
          isCyclicity: ''
        },
        formDataw1:{
          pattern: '',
          frequency: '',
          timeUnit: '',
          executeTimes: '',
          timechone: ''
        },
        optionsweek1: [],
        optionsTime1: [],
        timeUnitweek: [],
        rules:{
          code: [{ required: true, message: '代号不能为空', trigger: 'blur' }],
          name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
          cat: [{ required: true, message: '标签不能为空', trigger: 'blur' }],
          gxcat: [{ required: true, message: '本工序下是否设置“子工序”或“操作”不能为空', trigger: 'blur'}],
          time: [{ required: true, message: '时间点属性不能为空',trigger: 'blur'}],
          catog1: [{ required: true, message: '类别不能为空',trigger: 'blur'}],
          //isCyclicity: [{ required: true, message: '是否为周期操作不能为空', trigger: 'blur'}],
          //pattern: [{ required: true, message: '周期的形式不能为空', trigger: 'blur'}],
        }
      },
      //创建子工序
      addPress2Data:{
        clock: '',
        visible: false,
        optionsCat2: [],
        catoglk: 0,
        isCyiy: 0,
        pate: 1,
        fret: 0,
        lastRadioValue: null,
        radio2: '',
        timecs: 0,//时间点属性的隐藏和展示
        formData2:{
          code:'',
          name:'',
          catog: '',
          cat:'',
          chidcat: '',
          time: '',
          isCyclicity: '',
          // pattern: '',
          // frequency: '',
          // timeUnit: '',
          // executeTimes: '',
          // timechose: ''
        },
        formDataw2:{
          pattern: '',
          frequency: '',
          timeUnit: '',
          executeTimes: '',
          timechone: ''
        },
        timeUnitweek: [],
        optionsweek: [],
        optionsTime2: [],
        rules2:{
          code: [{ required: true, message: '代号不能为空', trigger: 'blur' }],
          name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
          cat: [{ required: true, message: '类别不能为空', trigger: 'blur' }],
          chidcat: [{ required: true,message: '本工序下是否设置"子工序"或"操作"不能为空',trigger: 'blur'}],
          time: [{ required: true, message:'时间点不能为空',trigger:'blur'}],
          //isCyclicity: [{ required: true, message:'周期操作不能为空',trigger:'blur'}],
          catog: [{ required: true,message:'类别不能为空',trigger: 'blur'}],
          //pattern: [{ required: true, message: '周期形式不能为空',trigger: 'blur'}]
        }
      },
      //创建操作数据
      addPress3Data:{
        clock: '',
        visible: false,
        optionsCat3: [],
        catoglk : 0,
        isCyiy: 0,
        pate: 1,
        fret: 0,
        lastRadioValue: null,
        formData3:{
          code:'',
          name:'',
          catog: '',
          cat:'',
          time:'',
          isCyclicity: '',
        },
        formDataw:{
          pattern: '',
          frequency: '',
          timeUnit: '',
          executeTimes: '',
          timechone: ''
        },
        timeUnitweek: [],
        optionsweek: [],
        optionsTime3: [],
        rules3:{
          code: [{ required: true, message: '代号不能为空', trigger: 'blur' }],
          name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
          cat: [{ required: true, message: '类别不能为空', trigger: 'blur' }],
          time: [{ required: true, message:'时间点不能为空',trigger:'blur'}],
          //isCyclicity: [{ required: true, message:'周期操作不能为空',trigger:'blur'}],
          catog: [{ required: true,message:'类别不能为空',trigger: 'blur'}],
          //pattern: [{ required: true, message: '周期形式不能为空',trigger: 'blur'}]
        }
      },
      messgen: false,
      //周期数据
      // weekData:{
      //   visible: false,
      //   formDataw: {pattern: '',frequency: '', timeUnit: '', executeTimes: '',timechone: ''},
      //   optionsweek: [],
      //   timeUnitweek: [],
      //   rules:{
      //     pattern: [{ required: true, message: '周期形式不能为空',trigger: 'blur'}],
      //     frequency: [{ required: true, message: '时长/频次不能为空',trigger: 'blur'}]
      //   }
      // },
      //选择子工序列表
      chidlistplcData:{ // 选择子工序页 数据
        process1:null,
        catList:[
          {
            enabled : true ,
            id :  1,
            name : "工序类别1",
            operation :  null ,
            orders :  null,
            previousId :  null,
            type  : 1
          }
        ], // 分类列表
        selectCat:'', // 选择的分类
        processList:[{
          category : 1 ,
          code :  "1-1" ,
          createName :  "张三",
          enabled : true ,
          id :  1,
          isLeaf : null ,
          name  :  "工序1",
          checkchick: false,//false代表不可进行取消勾选，true代表可以进行取消勾选
          isLockched: false,//用于标记子工序是否锁定选项
        }],// 展示的工序列表
        selectProcess:[],// 选择好的子工序
        cantSelectProcess:[
          {
            code : "1-1" ,
            name  :  "工序1" ,
            operateList : [],
            orders :  1,
            org : 3681,
            process :  1,
            product :  8801,
            productProcessId : 306,
            sonProcesses  :  null
          }
        ], // 不能选择的工序
      },
      oprateContainerData:{
        process1: {},
        process2: {},
        type: 1, //  1-工序下的操作，2-子工序下的操作
        oprateList:[{
          id : 'id',
          code:'工序代号',
          name:'工序名称',
          category: '类别id',
          categoryName: '类别名称',
          timing: '时间点:1-准备工作,2-正式操作,3-收尾工作',
          //isCyclicity: '是否周期性:true-是,false-否',
          createName: '创建人',
          creator: '创建人id',
          createTime: '创建时间',
          checkopick: false,//false代表不可进行取消勾选，true代表可以进行取消勾选
          isLockoped: false,//用于标记操作是否锁定选项
        }],
        oprateList1:[],
        oprateList2:[],
        oprateList3:[],
        secondTabNum:1,
        selectOprateList:[],
        selectCat:'', // 选择的分类
        catList:[],
        cantSelectOprateList:[]
      },
      //存储操作删除的数据
      operateCrolData:{
        type :'', // 排序 'sort', 删除 del ;
        process1 :'',
        process2 :'', // 'no1'-工序的操作, jsonObj-子工序的操作
        operitem :'', //  操作详情  操作的各项数据
        indexOperate :'', //  操作详情  操作的索引值
        operateDelVisible :false,
        tip: ''
      },
      //存储操作排序的数据
      oprateOrderData: {
        editOprate: null , // 当前的操作
        orderOprateItemIndex: '',
        orderOprateItemIndex2: '',
        oprateList: [], // 前几个
        process1: {},
        process2: {},
        indexOperate : 0,// 当前操作 的 索引
      },
      //工序删除的数据
      process1CrolData:{
        type: '',//排序'sort',删除'del'
        process1: '',//工序数据
        indexProcess1: '',//工序的索引值
        process1DelVisible: false,
        pro1tip: ''//工序弹窗中的文字
      },
      //工序排序的数据
      process1OrderData:{
        editProcess1: null,
        orderProcess1ItemIndex: '',
        orderProcess1ItemIndex2: '',
        process1List:[],
        indexProcess1:0,
        chosid: ''
      },
      //子工序删除的数据
      process2CrolData:{
        type:'',//排序'sort',删除'del'
        process1: '',
        process2:'',
        indexProcess2: '',
        process2DelVisible: false,
        pro2tip: ''
      },
      //子工序排序的数据
      process2OrderData:{
        editProcess2: null,
        orderProcess1ItemIndex: '',
        orderProcess1ItemIndex2: '',
        process2List:[],
        process1:{},
        indexProcess2: 0
      },
      addcatent: false,
      //工序新增类别
      addcatData:{
        formData:{name:''},
        type: '',
        rules:{
          //name: []
          name:[{required: true, message: '类别名称不能为空',trigger: 'blur'}]
        },
        clock: false,//用于判断确定按钮是否可以点击
      },
      addcatent2: false,
      //子工序新增类别
      addcat2Data:{
        form2Data:{name2:''},
        type2: '',
        rules2:{//规则的
          //name2: []
          name2:[{required: true, message: '类别名称不能为空',trigger: 'blur'}]
        },
        clock2: false,//用于判断确定按钮是否可以点击（子工序标签弹窗）
      },
      addcatent3: false,
      //操作新增类别
      addcat3Data:{
        form3Data:{
          name3:''
        },
        type3: '',
        rules3:{
          //name3: []
          name3:[{required: true, message: '类别名称不能为空',trigger: 'blur'}]
        },
        clock3: false,//用于判断确定按钮是否可以点击（操作标签弹窗）
      },
      //工序类别停用复用的数据
      process1StoData:{
        cateorynagent: false,//类别管理弹窗隐藏或显示
        stoporyagent: false,//被停用的类别管理弹窗隐藏和显示
        catsetting: 0,//1-从‘标签管理’过来的，0-从其他地方过来的
        tableData: [],//启用的数据
        tableData2: [],//停用的数据
        type: '',//停用‘sto',启用'str'
        editstprocess1: '',//停用或启用的数据
        indexstoProcess1: '',//停用或启用的数据的索引值
        process1StoVisible: false,//默认关闭提示弹窗
        pro1top: '',//弹窗中的文字内容
        editId: ''//需要停用或启用的数据的id
      },
      //子工序类别停用复用的数据
      process2StoData:{
        cateorynagent2: false,//类别管理弹窗隐藏或显示
        stoporyagent2: false,//被停用的类别管理弹窗隐藏和显示
        catsetting: 0,//1-从‘标签管理’过来的,0-从其他位置过来的
        tableData2: [],//启用的数据
        tableData22: [],//停用的数据
        type: '',//停用‘sto',启用'str'
        editstprocess2: '',//停用或启用的数据
        indexstoProcess2: '',//停用或启用的数据的索引值
        process2StoVisible: false,//默认关闭提示弹窗
        pro2top: '',//弹窗中的文字内容
        editId2: ''//需要停用或启用的数据的id
      },
      //操作类别停用复用的数据
      process3StoData:{
        cateorynagent3: false,
        stoporyagent3: false,
        catsetting: 0,//1-从标签管理过来的,0-从其他位置过来的
        tableData3: [],
        tableData32: [],
        type: '',
        editstprocess3: '',
        indexstoProcess3: '',
        process3StoVisible: false,
        pro3top: '',
        editId3: ''
      },
      havoptionData:{},
      opdiff: '',
      radio1: '1',
      timk1: false,
      timk2: false,
      looken: false,
      stvisible: false,//用于判断状态提示弹窗是否展示
      setten: 1,//用于区分是‘允许设置子工序’还是‘不允许设置子工序’（如果是不允许设置子工序，则可以设置该工序下的操作数据）
      tckming: false,//用于判断更多操作说明弹窗是否展示
      mdesurn: false,
      //chidchis: 0,//用于判断该工序下是否可以设置子工序,若未勾选‘设置’或‘不设置’，chidchis=0;若勾选了‘设置’
      //chidchis=1;若勾选了‘不设置’，chidchis=2;
      // haschild: 0//用于区分什么时候可以选中什么时候不能选中
      teppt1: '',//区分增加工序数据时是需要数据加在点击的按钮所在那条数据的最前面-2，还是加在数组最后面-1
      tepindex1: '',//代表添加工序按钮所在那行数据的索引值
      teppt2:'',//区分增加子工序数据时是需要数据加在点击的按钮所在那条数据的最前面-2，还是加在数组最后面-1
      tepindex2: '',//代表添加子工序按钮所在那行数据的索引值
      teppt3: '',//区分增加操作数据时是需要数据加在点击的按钮所在的那条数据的最前面-2，还是加在数组最后面-1
      tepindex3: '',//代表添加操作按钮所在那行数据的索引值
      statbegn: false,//判断工序何时可以点击右上角的确定按钮
      statbegn2: false,//判断子工序排序页面中何时可以点击右上角的确定按钮
      statbegn3: false,//判断操作排序页面中何时可以点击右上角的确定按钮
      configu: '',
      //clkset: '',//1-上一秒是点击‘设置’按钮跳转的，2-上一秒不是点击’设置‘按钮跳转的
    }
  },
  mounted(){ // 页面刷新后立即执行的函数
    this.$ref_box = this.$refs.ref_box
    this.$ref_wrap = this.$refs.ref_wrap
    this.$ref_barY = this.$refs.ref_barY
    this.$ref_barX = this.$refs.ref_barX
    this.$nextTick(this.updated)

    //this.setten = 1;
  },
  beforeRouteLeave(to, from, next) {
    beforeRouteLeave(to, from, next,this)
  },
  created(){
    initNav({mid: 'pm', name: '基础设置', pageName: this.pageName}, this.mountedFun, this)
    //加载数据
    //this.loadData();
  },
  destroyed () {},
  computed: {
    isFormComplete1(){//用于监听弹窗中所有输入框和选项是否都已经勾选完毕，判断确定按钮是否可以点击
      if(this.addPress1Data.formData.code && this.addPress1Data.formData.name && this.addPress1Data.formData.cat){
        if(this.addPress1Data.formData.gxcat === 1){//勾选的设置
          this.addPress1Data.formData.time = '';//时间点属性数据需要清空
          this.addPress1Data.catoglk1 = 0;//需要将类别选项隐藏掉
          this.addPress1Data.formData.catog1 = '';//需要将类别那行勾选的选项清空
          this.addPress1Data.clock = true;
        }else if(this.addPress1Data.formData.gxcat === 2){//勾选的不设置
          if(this.addPress1Data.formData.time === 1 || this.addPress1Data.formData.time === 3){//选择的除去2以外的内容
            this.addPress1Data.formData.catog1 = '';//需要将类别那行勾选的选项清空
            this.addPress1Data.clock = true;
          }else if(this.addPress1Data.formData.time === 2){//选择的是2
            this.addPress1Data.clock = false;
            if(this.addPress1Data.formData.catog1 === 0 || this.addPress1Data.formData.catog1 === 1){
              this.addPress1Data.clock = true;}else{this.addPress1Data.clock = false;}
          }else{this.addPress1Data.clock = false;}
        }else{this.addPress1Data.clock = false;}
      }else{
        this.addPress1Data.clock = false;
        if(this.addPress1Data.formData.gxcat === 1){//勾选的设置
          this.addPress1Data.formData.time = '';//时间点属性数据需要清空
          this.addPress1Data.catoglk1 = 0;//需要将类别选项隐藏掉
          this.addPress1Data.formData.catog1 = '';//需要将类别那行勾选的选项清空
        }else if(this.addPress1Data.formData.gxcat === 2){//勾选的不设置
          if(this.addPress1Data.formData.time === 1 || this.addPress1Data.formData.time === 3){//选择的除去2以外的内容
            this.addPress1Data.formData.catog1 = '';//需要将类别那行勾选的选项清空
          }else if(this.addPress1Data.formData.time === 2){//选择的是2
            if(this.addPress1Data.formData.catog1 === 0 || this.addPress1Data.formData.catog1 === 1){}else{}
          }else{}
        }else{}
      }
    },
    isFormComplete2(){//用于监听弹窗中所有输入框和选项是否都已经勾选完毕，判断确定按钮是否可以点击
      if(this.addPress2Data.formData2.code && this.addPress2Data.formData2.name && this.addPress2Data.formData2.cat){
        if(this.addPress2Data.formData2.chidcat === 1){//勾选的设置
          this.addPress2Data.formData2.time = '';//时间点属性数据需要清空
          this.addPress2Data.catoglk = 0;//需要将类别选项隐藏掉
          this.addPress2Data.formData2.catog = '';//需要将类别那行勾选的选项清空
          this.addPress2Data.clock = true;
        }else if(this.addPress2Data.formData2.chidcat === 2){//勾选的不设置
          if(this.addPress2Data.formData2.time === 1 || this.addPress2Data.formData2.time === 3){//选择的除去2以外的内容
            this.addPress2Data.formData2.catog = '';//需要将类别那行勾选的选项清空
            this.addPress2Data.clock = true;
          }else if(this.addPress2Data.formData2.time === 2){//选择的是2
            this.addPress2Data.clock = false;
            if(this.addPress2Data.formData2.catog === 0 || this.addPress2Data.formData2.catog === 1){
              this.addPress2Data.clock = true;}else{this.addPress2Data.clock = false;}
          }else{this.addPress2Data.clock = false;}
        }else{this.addPress2Data.clock = false;}
      }else{
        this.addPress2Data.clock = false;
        if(this.addPress2Data.formData2.chidcat === 1){//勾选的设置
          this.addPress2Data.formData2.time = '';//时间点属性数据需要清空
          this.addPress2Data.catoglk = 0;//需要将类别选项隐藏掉
          this.addPress2Data.formData2.catog = '';//需要将类别那行勾选的选项清空
        }else if(this.addPress2Data.formData2.chidcat === 2){//勾选的不设置
          if(this.addPress2Data.formData2.time === 1 || this.addPress2Data.formData2.time === 3){//选择的除去2以外的内容
            this.addPress2Data.formData2.catog = '';//需要将类别那行勾选的选项清空
          }else if(this.addPress2Data.formData2.time === 2){//选择的是2
            if(this.addPress2Data.formData2.catog === 0 || this.addPress2Data.formData2.catog === 1){}else{}
          }else{}
        }else{}
      }
    },
    isFormComplete3(){
      if(this.addPress3Data.formData3.code && this.addPress3Data.formData3.name && this.addPress3Data.formData3.cat){
        if(this.addPress3Data.formData3.time === 1 || this.addPress3Data.formData3.time === 3){//选择的除去2以外的内容
          this.addPress3Data.formData3.catog = '';
          this.addPress3Data.clock = true;
        }else if(this.addPress3Data.formData3.time === 2){//选择的是2
          this.addPress3Data.clock = false;
          if(this.addPress3Data.formData3.catog === 0 || this.addPress3Data.formData3.catog === 1){
            this.addPress3Data.clock = true;}else{this.addPress3Data.clock = false;}
        }else{this.addPress3Data.clock = false;}
      }else{
        this.addPress3Data.clock = false;
        if(this.addPress3Data.formData3.time === 1 || this.addPress3Data.formData3.time === 3){
          this.addPress3Data.formData3.catog = '';
          if(this.addPress3Data.formData3.catog === 0 || this.addPress3Data.formData3.catog === 1){}else{}
        }else{}
      }
    },
    // isCheckboxDisabled(list) {
    //   // 当 checkbox 被勾选后，禁用取消勾选
    //  //return this.chosesetenData.checkboxValue && this.chosesetenData.shouldDisableUncheck;
    //   return list.checkboxValue && list.shouldDisableUncheck;
    // },
    //容器高度
    // containerHeight(){
    //   return this.$refs.container.offsetHeight;
    // },
    //列表高度
    // listHeight() {
    //   return this.$refs.list.offsetHeight;
    // }
  },
  methods: {
    // validateInput(fieldName) {
    //   this.$refs.all1formRef.resetFields(fieldName);
    // },
    handleCheckboxChange(value) {
      console.log('12.16-16:52',value);
      console.log('12.17-14:15-processList=',this.chosesetenData.processList);
      this.chosesetenData.processList.forEach(itemc => {
        if(itemc.checkick === false){
          //itemc.isSelect = value === true || false ? value : true;是不是就是这个道理，因为value=true，而isSelect='1'，而
          //让第一次设置了的数据能默认勾选，就需要将这两个值统一。
          itemc.isSelect = value === '1' || '' ? value : '1';//哇！！！！成功了！！！！惊呆了！！！！
        }
      })
      // 当用户尝试取消勾选时，重置为true（勾选状态）
      // this.isChecked = value === true || false ? value : true;
    },
    handleChdioChange(value){
      console.log('12.17-16:57-value=',value);
      // console.log('12.17-16:58-processList=',this.chosesetenData.processList);
      this.chosesetenData.processList.forEach((itemo,indexitem) => {
        if(itemo.checkick === false){
          console.log('12.18-9:05',itemo.havSonProcess);
          if(value === 1 && !this.isLocked){//这里需要加上一个：在value=1时，
            // 不仅要锁定value=1的选项，且value=2的选项也不应该被勾选，
            // 因为现在这条数据是不能取消勾选的，自然子工序这一列也不能随意勾选其他选项。
            itemo.isLocked = true;//锁定选项-将itemo代表的这条数据的isLocked的值赋为true
            //itemo代表的是这条数据，现在需要让这条数据的另一个选项也变成不可选中的
            itemo.selectedOption = 2;
            this.shouldApplyCustomStyle(1);
          }else if(itemo.isLocked){
            itemo.havSonProcess = 1;
          }
          if(value === 2 && !this.isLocked){
            itemo.isLocked = true;
            itemo.selectedOption = 1;
            this.shouldApplyCustomStyle(2);
          }else if(itemo.isLocked){
            itemo.havSonProcess = 2;
          }
          // if (value === '有' && !this.isLocked) {
          //   this.isLocked = true; // 锁定选项
          // } else if (this.isLocked) {
          //   // 如果已经锁定且尝试改变选项，则重置为“有”
          //   this.selectedOption = '有';
          // }
          //      // 当需要特定情况不能取消选中时，重新设置为原来的值
          // if (this.radio === null) {
          //   this.radio = this.defaultRadioValue;
          // }
          // itemo.havSonProcess = value === 1 || '' ? value : 1;
          // if(value === '1'){
          //   itemo.havSonProcess = value === '1' || '' ? value : '1';
          // }else if(value === '2'){
          //   itemo.havSonProcess = value === '2' || '' ? value : '2';
          // }
          // if(value === '1'){
          //   itemo.havSonProcess = value === '1' || '2' ? value : '1';
          // }else if(value === '2'){
          //   itemo.havSonProcess = value === '1' || '2' ? value : '2';
          // }
        }
      })
    },
    //工序部分
    shouldApplyCustomStyle(value){
      return value === value;
    },
    // handleCheckboxChange(value){
    //   console.log('12.16-15:18',value);
    //   // 当 checkbox 被勾选时，设置 shouldDisableUncheck 为 true
    //   if (value) {this.chosesetenData.shouldDisableUncheck = true;}
    // },
    //滚动事件回调函数
    //加载数据
    // loadData(){
    //   //向服务器请求数据，这里省略具体实现
    //},
    addPrecess1Btn(ord,plc,orcat){
      //debugger;
      if(plc === 'opt'){
        //在选项管理页面进行选项的选择
        this.opdiff = plc;
        if(ord === 1){
          // 新增 工序
          if(this.process1StoData.catsetting === 1){}else{
            this.process1StoData.catsetting = 0;
          }
          //debugger;
          console.log('12.17-11:39-formData=',this.addPress1Data.formData);
          console.log('12.17-11:40-formData2=',this.addPress1Data.formData2);
          if(orcat === 'mor'){//代表前一刻没有点击标签管理或新增标签
            this.addPress1Data.formData = { code: '' ,  name: '' ,   cat: '' , }
          }else if(orcat === 'addc'){
            this.addPress1Data.formData.cat = '';
          }
          this.addPress1Data.visible = true;
          //相当于现在只需要保证在打开弹窗的时候输入框下面不展示文字提示即可，其余的内容现已成功实现
          this.$refs.all1formRef.clearValidate(['code','name','cat','gxcat','time','catog1']); // 清除指定字段的验证信息而不清除字段的内容
          // 注意：如果您有多个字段需要清除验证信息，可以在数组中传入它们的 prop 值

          //this.$nextTick(() => {
            //this.validateInput('code');
            //this.$refs.all1formRef.resetFields(code);
            //this.$refs.all1formRef.validateField(code);
            //if(this.addPress1Data.formData.code === ''){this.validateInput('code');}
          //})
          this.addPress1Data.clock = false;
          //this.addPress1Data.catoglk1 = 0;
          let data = { type: 1 , enabled: 1  };
          this.addPress1Data.optionsCat = [];
          // this.addPress1Data.optionsCat = [];
          this.addPress1Data.optionsTime1 = [
            { id: 1,name: "正式操作之前的准备工作"},
            { id: 2,name: "正式操作"},
            { id: 3,name: "正式操作后的收尾工作"}
          ];
          this.addPress1Data.optionsweek1 = [
            { id:1, name:"一定时间内需进行一次或多次"},
            { id:2,name:"一定次数的操作后需进行一次或数次",disabled:true},
            { id:3,name: "一个或几个班次后需进行一次或数次",disabled:true}
          ];
          this.addPress1Data.timeUnitweek = [
            { id:1,name: "秒"},
            { id:2,name: "分钟"},
            { id:3,name: "小时"},
            { id:4,name: "天"}
          ];
          if(this.process1StoData.catsetting === 1){}else{
            this.addPress1Data.timecs = 0;
            this.addPress1Data.catoglk1 = 0;
          }
          getcatorylist(data).then(res1 => {
            //debugger;
            let res = res1.data;
            let list = res.data.ppmActivityCategories
            this.addPress1Data.optionsCat.push(...list)//很诡异到这里addPress1Data中就没有需要展示的数据了
            console.log('2025.2.7-有值么',this.addPress1Data.optionsCat);
          }).catch(err => {
            console.log('getCategoryList optionsCat=', err)
          })
        }else if(ord === 2){//新增子工序
          if(this.process2StoData.catsetting === 1){}else{
            this.process2StoData.catsetting = 0;
          }
          if(orcat === 'mor'){
            this.addPress2Data.formData2 = { code: '', name: '', cat: '' };
          }else if(orcat === 'addc'){this.addPress2Data.formData2.cat = '';}
          this.addPress2Data.visible = true;
          this.$refs.all2formRef.clearValidate(['code','name','chidcat','time','catog']);
          // this.$nextTick(() => {
          //   this.$refs.all2formRef.resetFields();
          // })
          this.addPress2Data.clock = false;
          //this.addPress2Data.catoglk = 0;
          let data2 = {type: 2, enabled: 1};
          this.addPress2Data.optionsCat2 = [];
          this.addPress2Data.optionsTime2 = [
            { id: 1,name:"正式操作之前的的准备工作" },
            { id:2,name:"正式操作" },
            { id:3,name:"正式操作后的收尾工作" }
          ];
          this.addPress2Data.optionsweek = [
            {id: 1,name: "一定时间内需进行一次或多次"},
            {id: 2,name: "一定次数的操作后需进行一次或数次",disabled: true},
            {id: 3,name: "一个或几个班次后需进行一次或数次",disabled: true}
          ];
          this.addPress2Data.timeUnitweek = [
            { id:1, name:"秒"},
            { id:2, name:"分钟"},
            { id:3,name: "小时"},
            { id:4,name: "天"}
          ];
          if(this.process2StoData.catsetting === 1){}else{
            this.addPress2Data.timecs = 0;
            this.addPress2Data.catoglk = 0;
          }
          this.addPress2Data.isCyiy = 0;
          this.addPress2Data.pate = 1;
          getcatorylist(data2).then(res2 => {
            let res = res2.data;
            let list2 = res.data.ppmActivityCategories;
            this.addPress2Data.optionsCat2.push(...list2);
          }).catch(err => {
            console.log('getCategoryList optionsCat2=', err);
          })
        }else if(ord === 3){//新增操作选项
          console.log('跳到这里了么');
          if(this.process3StoData.catsetting === 1){}else{
            this.process3StoData.catsetting = 0;
          }
          if(orcat === 'mor'){
            this.addPress3Data.formData3 = { code: '', name: '', cat: '',time: '',isCyclicity: '',catog: ''};
          }else if(orcat === 'addc'){this.addPress3Data.formData3.cat = ''}
          this.addPress3Data.visible = true;
          this.$refs.all3formRef.clearValidate(['code','name','time','catog']);
          // this.$nextTick(() => {
          //   this.$refs.all3formRef.resetFields();
          // })
          this.addPress3Data.clock = false;
          if(this.process3StoData.catsetting === 1){}else{
            this.addPress3Data.catoglk = 0;
          }
          let data3 = { type: 3, enabled: 1};
          this.addPress3Data.optionsCat3 = [];
          this.addPress3Data.optionsweek = [
            {id: 1,name: "一定时间内需进行一次或多次"},
            {id: 2,name: "一定次数的操作后需进行一次或数次",disabled: true},
            {id: 3,name: "一个或几个班次后需进行一次或数次",disabled: true}
          ];
          this.addPress3Data.timeUnitweek = [
            { id:1,name:"秒"},
            { id:2,name:"分钟"},
            { id:3,name:"小时"},
            { id:4,name:"天" }
          ];
          this.timk1 = true;
          this.timk2 = false;
          this.radio1 = '1';
          this.messgen = false;
          this.addPress3Data.formDataw = { frequency: '', timechone: '',executeTimes: '',timeUnit: '',pattern: '' };
          // let list2 = [];
          // list2 = [
          //   { id: 1,name:"正式操作之前的的准备工作" },
          //   { id:2,name:"正式操作" },
          //   { id:3,name:"正式操作后的收尾工作" }
          // ];
          this.addPress3Data.optionsTime3 = [
            { id: 1,name:"正式操作之前的的准备工作" },
            { id:2,name:"正式操作" },
            { id:3,name:"正式操作后的收尾工作" }
          ]
          getcatorylist(data3).then(res3 => {
            console.log('获取到数据了么',res3);
            let res = res3.data;
            let list = res.data.ppmActivityCategories;
            this.addPress3Data.optionsCat3.push(...list);
          }).catch(err => {
            console.log('getCategoryList optionsCat=', err);
          })
        }
      }else if(plc === 'sep'){
        //在去管理页面点击按钮后进行选项的选择
        this.opdiff = plc;
        if(ord === 1){
          // 新增 工序
          if(this.process1StoData.catsetting === 1){}else{
            this.process1StoData.catsetting = 0;
          }
          //debugger;
          if(orcat === 'mor'){
            this.addPress1Data.formData = { code: '' ,  name: '' ,   cat: '' , }
          }else if(orcat === 'addc'){this.addPress1Data.formData.cat = '';}
          this.addPress1Data.visible = true;
          this.$refs.all1formRef.clearValidate(['code','name','cat','gxcat','time','catog1']);
          // this.$nextTick(() => {
          //   this.$refs.all1formRef.resetFields();
          // })
          this.addPress1Data.clock = false;
          //this.addPress1Data.catoglk1 = 0;
          let data = { type: 1 , enabled: 1  }
          this.addPress1Data.optionsCat = [];
          this.addPress1Data.optionsweek1 = [
            { id:1,name:"一定时间内需进行一次或多次"},
            { id:2,name:"一定次数的操作后需进行一次或数次",disabled: true},
            { id:3,name:"一个或几个班次后需进行一次或数次",disabled: true}
          ];
          this.addPress1Data.timeUnitweek = [
            { id:1,name:"秒"},
            { id:2,name:"分钟"},
            { id:3,name:"小时"},
            { id:4,name:"天"}
          ];
          this.addPress1Data.optionsTime1 = [
            { id:1, name:"正式操作之前的准备工作"},
            { id:2, name:"正式操作"},
            { id:3, name:"正式操作后的收尾工作"}
          ];
          if(this.process1StoData.catsetting === 1){}else{
            this.addPress1Data.timecs = 0;
            this.addPress1Data.catoglk1 = 0;
          }
          getcatorylist(data).then(res1 => {
            let res = res1.data
            let list = res.data.ppmActivityCategories
            this.addPress1Data.optionsCat.push(...list);
            console.log('2025.2.7-有值么',this.addPress1Data.optionsCat);
          }).catch(err => {
            console.log('getCategoryList optionsCat=', err)
          })
        }else if(ord === 2){//新增子工序
          if(this.process2StoData.catsetting === 1){}else{
            this.process2StoData.catsetting = 0;
          }
          if(orcat === 'mor'){
            this.addPress2Data.formData2 = { code: '', name: '', cat: '' };
          }else if(orcat === 'addc'){this.addPress2Data.formData2.cat = '';}
          this.addPress2Data.visible = true;
          this.$refs.all2formRef.clearValidate(['code','name','chidcat','time','catog']);
          // this.$nextTick(() => {
          //   this.$refs.all2formRef.resetFields();
          // })
          this.addPress2Data.clock = false;
          //this.addPress2Data.catoglk = 0;
          let data2 = {type: 2, enabled: 1};
          this.addPress2Data.optionsCat2 = [];
          this.addPress2Data.optionsweek = [
            { id:1,name:"一定时间内需进行一次或多次"},
            { id:2,name:"一定次数的操作后需进行一次或数次",disabled: true},
            { id:3,name:"一个或几个班次后需进行一次或数次",disabled: true}
          ];
          this.addPress2Data.timeUnitweek = [
            {id:1,name:"秒"},
            {id:2,name:"分钟"},
            {id:3,name:"小时"},
            {id:4,name:"天"}
          ];
          this.addPress2Data.optionsTime2 = [
            { id: 1,name:"正式操作之前的的准备工作" },
            { id:2,name:"正式操作" },
            { id:3,name:"正式操作后的收尾工作" }
          ];
          if(this.process2StoData.catsetting === 1){}else{
            this.addPress2Data.timecs = 0;
            this.addPress2Data.catoglk = 0;
          }
          this.addPress2Data.isCyiy = 0;
          this.addPress2Data.pate = 1;
          getcatorylist(data2).then(res2 => {
            let res = res2.data;
            let list2 = res.data.ppmActivityCategories;
            this.addPress2Data.optionsCat2.push(...list2);
          }).catch(err => {
            console.log('getCategoryList optionsCat2=', err);
          })
        }else if(ord === 3){//新增操作选项
          if(this.process3StoData.catsetting === 1){}else{
            this.process3StoData.catsetting = 0;
          }
          if(orcat === 'mor'){
            this.addPress3Data.formData3 = { code: '', name: '', cat: '',time: '',isCyclicity: '',catog: ''};
          }else if(orcat === 'addc'){this.addPress3Data.formData3.cat = '';}
          this.addPress3Data.visible = true;
          this.$refs.all3formRef.clearValidate(['code','name','time','catog']);
          // this.$nextTick(() =>{
          //   this.$refs.all3formRef.resetFields();
          // })
          this.addPress3Data.clock = false;
          if(this.process3StoData.catsetting === 1){}else{
            this.addPress3Data.catoglk = 0;
          }
          let data3 = { type: 3, enabled: 1};
          this.addPress3Data.optionsCat3 = [];
          this.addPress3Data.optionsweek = [
            {id: 1,name: "一定时间内需进行一次或多次"},
            {id: 2,name: "一定次数的操作后需进行一次或数次",disabled: true},
            {id: 3,name: "一个或几个班次后需进行一次或数次",disabled: true}
          ];
          this.addPress3Data.timeUnitweek = [
            { id:1,name:"秒"},
            { id:2,name:"分钟"},
            { id:3,name:"小时"},
            { id:4,name:"天" }
          ];
          if(this.process3StoData.catsetting === 1){}else{
            this.addPress3Data.catoglk = 0;
          }
          this.timk1 = true;
          this.timk2 = false;
          this.radio1 = '1';
          this.addPress3Data.formDataw = { frequency: '', timechone: '',executeTimes: '',timeUnit: '',pattern: '' };
          this.addPress3Data.optionsTime3 = [
            { id: 1,name:"正式操作之前的的准备工作" },
            { id:2,name:"正式操作" },
            { id:3,name:"正式操作后的收尾工作" }
          ];
          getcatorylist(data3).then(res3 => {
            let res = res3.data;
            let list = res.data.ppmActivityCategories;
            console.log('获取到数据了么',list);
            this.addPress3Data.optionsCat3.push(...list);
          }).catch(err => {
            console.log('getCategoryList optionsCat=', err);
          })
        }
      }
    },
    timehav(){
      console.log('2024.10.30-1-Radio value changed to:',this.addPress3Data.formData3.time);
      const time = this.addPress3Data.formData3.time;
      if(time === 2){//正式操作
        this.addPress3Data.catoglk = 2;
      }else{
        this.addPress3Data.catoglk = 0;
      }
    },
    timehav2(){
      console.log('2024.11.1万圣节-Radio value changed to:',this.addPress2Data.formData2.time);
      const time = this.addPress2Data.formData2.time;
      if(time === 2){
        this.addPress2Data.catoglk = 2;
      }else{
        this.addPress2Data.catoglk = 0;
      }
    },
    timehav1(){
      console.log('2024.11.6-Radio value changed to:',this.addPress1Data.formData.time);
      const time = this.addPress1Data.formData.time;
      if(time === 2){
        this.addPress1Data.catoglk1 = 2;
      }else{
        this.addPress1Data.catoglk1 = 0;
      }
    },
    tckmiCancel(){
      this.tckming = false;
    },
    //下面两个函数方法(handleRadioChange和handleClick)用于实现el-radio单选框的第二次点击选中按钮取消选中效果
    handleRadioChange(n){
      if(n === 1){
        console.log('2024.10.30-1-Selected radio changed:', this.addPress3Data.formData3.catog);
      }else if(n === 2){
        console.log('2024.10.30-2-Selected radio changed:', this.addPress3Data.formData3.isCyclicity);
        // if(this.addPress3Data.formData3.catog === 1){
        //   this.addPress3Data.isCyiy = 2;
        // }
      }
    },
    handleRadioChange2(n2){
      if(n2 === 1){
        console.log('2024.11.4-1-Selected radio changed:',this.addPress2Data.formData2.catog);
      }else if(n2 === 2){
        console.log('2024-11.4-2-Selected radio changed:',this.addPress2Data.formData2.isCyclicity);
      }
    },
    handleRadioChange1(n1){
      if(n1 === 1){
        console.log('2024.11.6-Selected radio changed:',this.addPress1Data.formData.catog1);
      }else if(n1 === 2){
        console.log('2024.11.6-Selected radio changed:',this.addPress1Data.formData.isCyclicity);
      }
    },
    handleClick(label,n){
      if(n === 1){
        if(this.addPress3Data.formData3.catog === label){
          this.addPress3Data.formData3.catog = '';
          this.addPress3Data.isCyiy = 0;
          //this.addPress3Data.fret = 0;
        }else{
          this.addPress3Data.formData3.catog = label;
          if(this.addPress3Data.formData3.catog === 1){
            this.addPress3Data.isCyiy = 2;
          }else{
            this.addPress3Data.isCyiy = 0;
            //this.addPress3Data.fret = 0;
          }
        }
      }else if(n === 2){
        if(this.addPress3Data.formData3.isCyclicity === label){
          this.addPress3Data.formData3.isCyclicity = '';
          this.addPress3Data.pate = 1;
          this.addPress3Data.formDataw.pattern = '';
          this.addPress3Data.formDataw.frequency = '';
          this.addPress3Data.formDataw.timeUnit = '';
          this.addPress3Data.formDataw.executeTimes = '';
          this.addPress3Data.fret = 0;
        }else{
          this.addPress3Data.formData3.isCyclicity = label;
          if(this.addPress3Data.formData3.isCyclicity === 1){
            this.addPress3Data.pate = 1;
            this.addPress3Data.formDataw.pattern = '';
            this.addPress3Data.formDataw.frequency = '';
            this.addPress3Data.formDataw.timeUnit = '';
            this.addPress3Data.formDataw.executeTimes = '';
            this.addPress3Data.fret = 0;
          }else{
            this.addPress3Data.pate = 0;
          }
        }
      }
    },
    handleClick2(label2,n2){
      if(n2 === 1){
        if(this.addPress2Data.formData2.catog === label2){
          this.addPress2Data.formData2.catog = '';
          this.addPress2Data.isCyiy = 0;
        }else{
          this.addPress2Data.formData2.catog = label2;
          if(this.addPress2Data.formData2.catog === 1){
            this.addPress2Data.isCyiy = 2;
          }else{
            this.addPress2Data.isCyiy = 0;
          }
        }
      }else if(n2 === 2){
        if(this.addPress2Data.formData2.isCyclicity === label2){
          this.addPress2Data.formData2.isCyclicity = '';
          this.addPress2Data.pate = 1;
          this.addPress2Data.formDataw2.pattern = '';
          this.addPress2Data.formDataw2.frequency = '';
          this.addPress2Data.formDataw2.timeUnit = '';
          this.addPress2Data.formDataw2.executeTimes = '';
          this.addPress2Data.fret = 0;
        }else{
          this.addPress2Data.formData2.isCyclicity = label2;
          if(this.addPress2Data.formData2.isCyclicity === 1){
            this.addPress2Data.pate = 1;
            this.addPress2Data.formDataw2.pattern = '';
            this.addPress2Data.formDataw2.frequency = '';
            this.addPress2Data.formDataw2.timeUnit = '';
            this.addPress2Data.formDataw2.executeTimes = '';
            this.addPress2Data.fret = 0;
          }else{
            this.addPress2Data.pate = 0;
          }
        }
      }
    },
    handleClick1(label1,n1){
      if(n1 === 1){
        if(this.addPress1Data.formData.catog1 === label1){
          this.addPress1Data.formData.catog1 = '';
          this.addPress1Data.isCyiy = 0;
        }else{
          this.addPress1Data.formData.catog1 = label1;
          if(this.addPress1Data.formData.catog1 === 1){
            this.addPress1Data.isCyiy = 2;
          }else{
            this.addPress1Data.isCyiy = 0;
          }
        }
      }else if(n2 === 2){
        if(this.addPress1Data.formData.isCyclicity === label1){
          this.addPress1Data.formData.isCyclicity = '';
          this.addPress1Data.pate = 1;
          this.addPress1Data.formDataw1.pattern = '';
          this.addPress1Data.formDataw1.frequency = '';
          this.addPress1Data.formDataw1.timeUnit = '';
          this.addPress1Data.formDataw1.executeTimes = "";
          this.addPress1Data.fret = 0;
        }else{
          this.addPress1Data.formDataw1.isCyclicity = label1;
          if(this.addPress1Data.formDataw1.isCyclicity === 1){
            this.addPress1Data.pate = 1;
            this.addPress1Data.formDataw1.pattern = '';
            this.addPress1Data.formDataw1.frequency = '';
            this.addPress1Data.formDataw1.timeUnit = '';
            this.addPress1Data.formDataw1.executeTimes = '';
            this.addPress1Data.fret1 = 0;
          }else{
            this.addPress1Data.pate = 0;
          }
        }
      }
    },
    getshowse2(ros){
      console.log('Selected value changed to:',ros);
      if(ros === '一定时间内需进行一次或数次' || ros === 1){
        this.looken = true;
        this.addPress2Data.fret = 1;
      }else{
        this.looken = false;
      }
    },
    gxsetcat(label){
      console.log('2024.11.6-this.gxcat',this.addPress1Data.formData.gxcat);
      if(this.addPress1Data.formData.gxcat === label){
        this.addPress1Data.formData.gxcat = '';
        this.addPress1Data.timecs = 0;
      }else{
        this.addPress1Data.formData.gxcat = label;
        if(this.addPress1Data.formData.gxcat === 2){
          this.addPress1Data.timecs = 1;
        }else{
          this.addPress1Data.timecs = 0;
        }
      }
    },
    getchidt(labelt,list){
      //chosesetenData竟然是个数组啊！！！那该怎么判断特定的某条数据的选项呢？哦对，可以将那条数据的传值传过来
      console.log('2024.11.13-this.havSonProcess',list.havSonProcess);
      if(list.havSonProcess === labelt){
        list.havSonProcess = '';
      }else{
        list.havSonProcess = labelt;
      }
      //debugger;
      if(list.havSonProcess !== ''){//当选项被选中时
        list.checkick = true;
        if(list.isSelect){//此时是最前面一列的选框已经选中了
        }else{//此时是最前面一列的选框并未选中
          list.isSelect = '1';this.chosesetenData.selectProcess.push(list);
        }
        //在‘有’或‘没有‘选项被选中时会有下面两种情况：
        //1.先选中的’有‘或’没有‘，然后自动勾选最前面的选框.
        //2.在选中’有‘或’没有‘之前最前面的选框已经选中了.
        //第1种情况时，则需要将勾选的这条数据存入数组chosesetenData中；而在取消勾选后，则需要从数组中将这条数据删除
        //第2种情况时，则不用再重复一次将选中的数据存入数组种了；而在取消勾选时，则需要从数组种将那条数据移除
        //this.chosesetenData.selectProcess.push(list);
        // if(this.chosesetenData.selectProcess.length === 0){this.chosesetenData.selectProcess.push(list);}else{
        //   this.chosesetenData.selectProcess.forEach((seleItem, seleIndex) => {
        //     debugger;
        //     if(seleItem.id === list.id){
        //       //this.chosesetenData.selectProcess.splice(seleIndex,1);
        //     }else{this.chosesetenData.selectProcess.push(list);}
        //   })
        // }
      }else{//当取消选中时
        //debugger;
        list.checkick = true;list.isSelect = '';
        //let getIndex = -1;
        // for(let t in this.chosesetenData.selectProcess){
        //   if(this.chosesetenData.selectProcess[t].id === list.id){
        //     getIndex = Number(t);
        //     this.chosesetenData.selectProcess.splice(getIndex,1);
        //   }
        // }
        this.chosesetenData.selectProcess = this.chosesetenData.selectProcess.filter(item => item.id !== list.id);//用于在数组中找到id相同的数据并将其删除
        // this.chosesetenData.selectProcess.forEach((seleItem, seleIndex) => {
        //   debugger;
        //   if(seleItem.id === list.id){
        //     getIndex = seleIndex;
        //     this.chosesetenData.selectProcess.splice(getIndex, 1);
        //   }
        // })
      }
      //下面是数组去重（没法，单次选中‘有’或‘没有’，不往数组中存数据，手动存储就会出现这个问题
      // for(let a = 0;a<this.chosesetenData.selectProcess.length;a++){
      //   for(let a1 = a + 1;a1<this.chosesetenData.selectProcess.length;a1++){
      //     if(this.chosesetenData.selectProcess[a].id === this.chosesetenData.selectProcess[a1].id){
      //       this.chosesetenData.selectProcess.splice(a1,1);
      //       a1--;
      //     }
      //   }
      // }
      console.log(this.chosesetenData.selectProcess);//走到这里相当于勾选的‘有’或‘没有’根本没有存入selectProcess中。
      for(let c in this.chosesetenData.selectProcess){//用于将勾选的‘有’或‘没有’数据存入selectProcess中。
        if(this.chosesetenData.selectProcess[c].id === list.id){
          this.chosesetenData.selectProcess[c].havSonProcess = list.havSonProcess;
        }
      }
      console.log(this.chosesetenData.selectProcess);
      if(this.chosesetenData.selectProcess.length === 0){this.choseover = 0;}else{
        //debugger;
        //这个位置有个需要注意的问题：在排除数组的长度是0之后，数组中不仅会有可以设置子工序或操作的数据，也有不能设置的数据，故仅仅只是通过
        //havSonProcess来判断，并不准确；虽然是挺奇怪的，按理来说havSonProcess=false的大概率就是不能设置子工序或操作的数据了，但
        //总是有那么几个特例，明明是不能设置子工序或操作的数据，但havSonProcess的值却是undefined，离谱。
        // this.chosesetenData.selectProcess[s].havSonProcess === undefined
        //在排除了数组的数据为0的时候以后，就需要判断数组种是否有哪条需要勾选‘有’或‘没有’的工序数据未进行勾选，只要有就跳出循环
        for(let s in this.chosesetenData.selectProcess){
          //不是所有的工序数据都有havSonProcess这个属性，故需要先区分下有和没有的，因为首页的状态会发生改变，故不能将isLeaf用于下面的判断
          //好像是有这种情况：同一个工序数据，但一会儿就是havSonProcess=undefined,一会儿又变成了havSonProcess=''，但若这两种情况都写成choseover
          //=0,似乎也不太对。
          //故只有先勾选最右侧的选框后勾选‘有’或‘没有’的工序数据才会出现上述的情况，故需要先判断数组中的数据是否含有勾选了左侧选框
          //但未勾选右侧选项的
          if(this.chosesetenData.selectProcess[s].isSelect){
            if(this.chosesetenData.selectProcess[s].havSonProcess === ''){
              if(this.configu === true){
                if(this.chosesetenData.selectProcess[s].isLeaf === false){this.choseover = 1;}
                else if(this.chosesetenData.selectProcess[s].isLeaf === true){
                  this.choseover = 0;break;
                }
              }
              // if(this.chosesetenData.selectProcess[s].isLeaf === false){
              //   this.choseover = 1;
              // }else if(this.chosesetenData.selectProcess[s].isLeaf === true){
              //   this.choseover = 0;break;
              // }
            }
            //下面两种情况需要debugger下查看下是什么时候才会执行
            else if(this.chosesetenData.selectProcess[s].havSonProcess === false){this.choseover = 1;}
            else if(this.chosesetenData.selectProcess[s].havSonProcess === undefined){
              //数组中的数据都是不需要选中’有‘或’没有‘的数据，故它们的havSonProcess=undefined
              if(this.configu === true){
                if(this.chosesetenData.selectProcess[s].isLeaf === true){this.choseover = 0;break;}
                else if(this.chosesetenData.selectProcess[s].isLeaf === false){this.choseover = 1;}
              }else if(this.configu === false){this.choseover = 1;}
              // if(this.chosesetenData.selectProcess[s].isLeaf === true){this.choseover = 0;break;}
              // else if(this.chosesetenData.selectProcess[s].isLeaf === false){this.choseover = 1;}
            }else{this.choseover = 1;}
          }
          // if(this.chosesetenData.selectProcess[s].havSonProcess === ''){this.choseover = 0;break;}else{this.choseover = 1;}
        }
      }
      // this.chosesetenData.selectProcess.forEach((selectItem,seleIndex) => {//this.chosesetenData.selectgProcess代表本次列表中选中的所有工序数据
      //   debugger;
      //   if(selectItem.havSonProcess === '' || selectItem.havSonProcess === undefined){
      //     this.choseover = 0;return false;}else{this.choseover = 1;}
      // })
    },
    addPrecess1Ok(){
      //debugger;
      let params = {
        category: this.addPress1Data.formData.cat,
        code: this.addPress1Data.formData.code,
        name: this.addPress1Data.formData.name
      }
      //本工序下是否设置“子工序”或“操作” 0-不设置 1-设置
      if(this.addPress1Data.formData.gxcat === 1){params.isLeaf = 1;}
      else if(this.addPress1Data.formData.gxcat === 2){params.isLeaf = 0}
      if(params.isLeaf === null){this.$message.error('11.21请录入必填项！');return false;}
      if(params.isLeaf === 0){
        params.timing = this.addPress1Data.formData.time;//时间点:1-准备工作,2-正式操作,3-收尾工作
        // （isLeaf=0的时候才需要传，否则就不传这个值）
        if(!params.timing){this.$message.error('11.22请录入必填项！');return false;}
        if(params.timing === 2){
          //kind Byte 操作类型:1-主要,2-辅助（timing=2的的时候才需要传，否则就不传这个值）
          if(this.addPress1Data.formData.catog1 === 0){params.kind = 1;}
          else if(this.addPress1Data.formData.catog1 === 1){params.kind = 2;}
          if(!params.kind){this.$message.error('11.23请录入必填项！');return false;}
          if(params.kind === 2){
            //isCyclicity Boolean 是否周期性:1-是,0-否（kind=2的的时候才需要传，否则就不传这个值）
            // if(this.addPress1Data.formData.isCyclicity === 0){params.isCyclicity = 1;}
            // else if(this.addPress1Data.formData.isCyclicity === 1){params.isCyclicity = 0;}
            // if(!params.isCyclicity){this.$message.error('11.24请录入必填项！');return false;}
            // if(params.isCyclicity === 1){
            //   //pattern Byte 周期型式:1-一定时间内需进行一次或数次(isCyclicity=1的的时候才需要传，否则就不传这个值)
            //   // 2-一定次数的操作后需进行一次或数次,3-一个或几个班次后需进行一次或数次
            //   // （kind=2的的时候才需要传，否则就不传这个值）
            //   params.pattern = this.addPress1Data.formDataw1.pattern;
            //   if(!params.pattern){this.$message.error('11.25请录入必填项！');return false;}
            //   if(params.pattern === 1){
            //     params.frequency = this.addPress1Data.formData.frequency;//时长/频次
            //     params.timeUnit = this.addPress1Data.formData.timeUnit;//时间单位：1-秒，2-分钟，3-小时，4-天
            //     params.executeTimes = this.addPress1Data.formData.executeTimes;//执行次数
            //   }
            // }
          }
        }
      }
      // if(!params.code || !params.name){
      //   this.$message.error('请将必填项补充完整！')
      //   return false
      // }
      this.addPress1Data.visible = false
      createProcess(params).then(res1 => {
        let res = res1.data
        let content = res.data.content
        if(content === '操作成功'){
          this.$message.success(content);
          const opchone = this.opdiff;
          if(opchone === 'opt'){//是从选项管理页面开始的
            this.gotenchose();
          }else{//是从去管理页面点击按钮后开始的选项创建
            this.getProcessListFun(this.chosesetenData.selectCat, this.chosesetenData.cantSelectProcess);
          }
        }else{
          this.$message.error(content);
        }
      }).catch(err => {
        console.log('跳到这里了?');
      })

    },
    addPrecess1Cancel(){
      this.addPress1Data.visible = false;
      let params = { type:1, enabled: 1};
      getCategoryList(params).then(res1 => {
        let res = res1.data
        this.chosesetenData.catList = res.data.ppmActivityCategories;
        let jsonall = {
          enabled: true,
          id: 0,
          name: "全部类别",
          operation: null,
          orders: null,
          previousId: null,
          type: 1
        };
        this.chosesetenData.catList.unshift(jsonall);
        //debugger;
        console.log('2024.12.12-10:56',this.chosesetenData.processList);//这个是列表中的全部数据
        //如果需要判断右上角是否能点击应该判断的是selectProcess
        //this.chosesetenData.processList.forEach(itemc => {
          //if(!itemc.havSonProcess){itemc.isSelect = '1';}else{itemc.isSelect = '';}
       // })
        if(this.chosesetenData.selectProcess.length === 0){this.choseover = 0;}else{
          for(let o in this.chosesetenData.selectProcess){
            if(this.chosesetenData.selectProcess[o].havSonProcess === '' ||
                this.chosesetenData.selectProcess[o].havSonProcess === undefined){
              if(this.configu === true){
                if(this.chosesetenData.selectProcess[o].isLeaf === false){
                  this.choseover = 1;
                }else if(this.chosesetenData.selectProcess[o].isLeaf === true){
                  this.choseover = 0;break;
                }
              }else if(this.configu === false){this.choseover = 1;}
            }else{this.choseover = 1;}
          }
        }
      }).catch(err => {
        console.log('getCategoryList err=', err)
      })
      let opchone = this.opdiff;
      if(opchone === 'opt'){//是从选项管理页面开始的
        this.gotenchose();
      }else{//是从去管理页面点击按钮后开始的选项创建
        this.getProcessListFun(this.chosesetenData.selectCat, this.chosesetenData.cantSelectProcess);
      }
      this.process1StoData.catsetting = 0;
    },
    addPrecess2Cancel(){
      this.addPress2Data.visible = false;
      let data = { type: 2 , enabled: 1  }
      getCategoryList(data).then(res1 => {
        let res = res1.data
        this.chidlistplcData.catList = res.data.ppmActivityCategories;
        let jsonall = {
          enabled : true ,
          id :  0,
          name : "全部类别",
          operation :  null ,
          orders :  null,
          previousId :  null,
          type  : 1
        };
        this.chidlistplcData.catList.unshift(jsonall);

      }).catch(err => {
        console.log('getCategoryList son=', err)
      })
      if(this.opdiff === 'opt'){
        this.gotenchose();
      }else if(this.opdiff === 'sep'){
        this.getSonProcessListFun(this.chidlistplcData.selectCat, this.chidlistplcData.cantSelectProcess);
      }
      this.process2StoData.catsetting = 0;
    },
    addPrecess2Ok(){
      let params2 = {
        code: this.addPress2Data.formData2.code,//代号
        name: this.addPress2Data.formData2.name,//名称
        category: this.addPress2Data.formData2.cat,//标签
        type: 2,//类型:2-子工序,3-操作,4-套餐
      };
      if(params2.type === 2){//子工序
        //params2.isLeaf = this.addPress2Data.formData2.chidcat;//本子工序下是否需设置“操作” 0-不设置 1-设置
        //isLeaf这个值只有在type=2的时候才传，type=3的时候由于是没有这个选项所以是不需要传的
        console.log('11.1这里获取不到值么',this.addPress2Data.formData2.chidcat);//这里有值的啊
        if(this.addPress2Data.formData2.chidcat === 1){//设置
          params2.isLeaf = 1;
        }else if(this.addPress2Data.formData2.chidcat === 2){//不设置
          console.log(11.1);
          params2.isLeaf = 0;
        }
        if(params2.isLeaf === null){this.$message.error('11.1请录入必填项！');return false;}
        if(params2.isLeaf === 0){
          params2.timing = this.addPress2Data.formData2.time;//时间点:1-准备工作,2-正式操作,3-收尾工作
          // （isLeaf=0或不传的时候才需要传timing，否则就不传这个值）
          if(!params2.timing){this.$message.error('11.2请录入必填项！');return false;}
          if(params2.timing === 2){
            //params2.kind = this.addPress2Data.formData2.catog;//操作类型:1-主要,2-辅助
            // （timing=2的的时候才需要传kind，否则就不传这个值）
            if(this.addPress2Data.formData2.catog === 0){params2.kind = 1;}
            else if(this.addPress2Data.formData2.catog === 1){params2.kind = 2;}
            if(!params2.kind){this.$message.error('11.3请录入必填项！');return false;}
            if(params2.kind === 2){
              //params2.isCyclicity = this.addPress2Data.formData2.isCyclicity;//是否周期性:1-是,0-否
              // （kind=2的的时候才需要传isCyclicity，否则就不传这个值）
              // if(this.addPress2Data.formData2.isCyclicity === 0){
              //   params2.isCyclicity = 1;
              // }else if(this.addPress2Data.formData2.isCyclicity === 1){
              //   params2.isCyclicity = 0;
              // }
              // if(!params2.isCyclicity){this.$message.error('11.4请录入必填项！');return false;}
              // if(params2.isCyclicity === 1){
              //   params2.pattern = this.addPress2Data.formDataw2.pattern;
              //   //周期型式:1-一定时间内需进行一次或数次2-一定次数的操作后需进行一次或数次3-一个或几个班次后需进行一次或数次
              //   //(isCyclicity=1的的时候才需要传，否则就不传这个值)
              //   if(!params2.pattern){this.$message.error('11.5请录入必填项！');return false;}
              //   if(params2.pattern === 1){
              //     params2.frequency = this.addPress2Data.formDataw2.frequency;//时长/频次
              //     params2.timeUnit = this.addPress2Data.formDataw2.timeUnit;//时间单位:1-秒,2-分钟,3-小时,4-天
              //     params2.executeTimes = this.addPress2Data.formDataw2.executeTimes;//执行次数
              //   }
              // }
            }
          }
        }
      }
      console.log('2024.11.14-params2没走这里么？',params2);
      // if(!params2.code || !params2.name){this.$message.error('请录入必填项！');return false;}
      // if(params2.type === 2){
      //   if(!params2.isLeaf){this.$message.error('请录入必填项！');return false;}
      //   if(params2.isLeaf === 0){if(!params2.timing){this.$message.error('请录入必填项！');return false;}
      //   if(params2.timing === 2){
      //     if(!params2.kind){this.$message.error('请录入必填项！');return false;}
      //     if(params2.kind === 2){
      //       if(!params2.isCyclicity){this.$message.error('请录入必填项！');return false;}
      //       if(params2.isCyclicity === 1){
      //         if(!params2.pattern){this.$message.error('请录入必填项！');return false;}
      //       }
      //     }else{console.log('既然走到这里了，就不需要判断是否没有填写下面的内容了对吧');}
      //   }
      //   }
      // }
      // if(!params2.code || !params2.name || !params2.category ||
      //     !params2.isLeaf || !params2.timing || !params2.kind || !params2.isCyclicity || !params2.pattern){
      //   this.$message.error('请录入必填项！');
      //   return false;
      // }
      createProcess2(params2).then(res2 => {
        let resd2 = res2.data;
        let content2 = resd2.data.content;
        if(content2 === '操作成功'){
          this.$message.success(content2);
          this.addPress2Data.visible = false;
          if(this.opdiff === 'opt'){
            this.gotenchose();
          }else if(this.opdiff === 'sep'){
            this.getSonProcessListFun(this.chidlistplcData.selectCat, this.chidlistplcData.cantSelectProcess);
          }
        }else{
          this.$message.error(content2);
        }
      }).catch(err => {})
    },
    addPrecess3Cancel(){
      this.process3StoData.catsetting = 0;
      this.addPress3Data.visible = false;
      this.messgen = false;
      let data = { type: 3 , enabled: 1  }
      getCategoryList(data).then(res1 => {
        let res = res1.data
        this.oprateContainerData.catList = res.data.ppmActivityCategories;
        let jsonall = {
          enabled : true ,
          id :  0,
          name : "全部类别",
          operation :  null ,
          orders :  null,
          previousId :  null,
          type  : 1
        };
        this.oprateContainerData.catList.unshift(jsonall);
      }).catch(err => {
        console.log('getCategoryList oprate=', err)
      })
      if(this.opdiff === 'opt'){
        this.gotenchose();
      }else if(this.opdiff === 'sep'){
        this.getOperateListFun(this.oprateContainerData.selectCat,this.oprateContainerData.process1.operateList);
      }
    },
    addPrecess3Ok(){
      console.log('this.weekData现在的值',this.weekData);
      console.log('this.addPress3Data现在的值',this.addPress3Data);
      let params = {
        code: this.addPress3Data.formData3.code,
        name: this.addPress3Data.formData3.name,
        category: this.addPress3Data.formData3.cat,
        type: 3,
        // timing:'',//时间点:1-准备工作,2-正式操作,3-收尾工作
        // isCyclicity:this.addPress3Data.formData3.isCyclicity,//是否周期性:1-是,0-否(默认)
        // pattern:this.weekData.formDataw.pattern,//周期型式:1-一定时间内需进行一次或数次,2-一定次数的操作后需进行
        // // 一次或数次,3-一个或几个班次后需进行一次或数次
        // frequency:this.weekData.formDataw.frequency,//时长/频次
        // timeUnit:this.weekData.formDataw.timeUnit,//时间单位:1-秒,2-分钟,3-小时,4-天
        // executeTimes:this.weekData.formDataw.executeTimes//执行次数
      };
      if(params.type === 3){//操作
        //timing Byte 时间点:1-准备工作,2-正式操作,3-收尾工作
        params.timing = this.addPress3Data.formData3.time;
        if(!params.timing){this.$message.error('11.31请录入必填项！');return false;}
        if(params.timing === 2){
          //kind Byte 操作类型:1-主要,2-辅助（timing=2的的时候才需要传，否则就不传这个值）
          if(this.addPress3Data.formData3.catog === 0){params.kind = 1;}
          else if(this.addPress3Data.formData3.catog === 1){params.kind = 2;}
          if(!params.kind){this.$message.error('11.32请录入必填项！');return false;}
          if(params.kind === 2){
            //isCyclicity Boolean 是否周期性:1-是,0-否（kind=2的的时候才需要传，否则就不传这个值）
            // if(this.addPress3Data.formData3.isCyclicity === 0){params.isCyclicity = 1;}
            // else if(this.addPress3Data.formData3.isCyclicity === 1){params.isCyclicity = 0;}
            // if(!params.isCyclicity){this.$message.error('11.33请录入必填项！');return false;}
            // if(params.isCyclicity === 1){
            //   params.pattern = this.addPress3Data.formDataw.pattern;
            //   if(!params.pattern){this.$message.error('11.34请录入必填项！');return false;}
            //   if(params.patter === 1){
            //     params.frequency = this.addPress3Data.formDataw.frequency;//时长/频次
            //     params.timeUnit = this.addPress3Data.formDataw.timeUnit;//时间单位：1-秒，2-分钟，3-小时，4-天
            //     params.executeTimes = this.addPress3Data.formDataw.executeTimes;//执行次数
            //   }
            // }
          }
        }
      }
      // if(this.addPress3Data.formData3.time === 1){
      //   params.timing = 1;
      // }else if(this.addPress3Data.formData3.time === 2){//正式操作
      //   params.timing = 2;
      //   // this.addPress3Data.formData3.catog = 2;
      // }else if(this.addPress3Data.formData3.time === 3){
      //   params.timing = 3;
      // }
      console.log('现在params的值是什么样的',params);
      // if(!params.code || !params.name || !params.category || !params.timing){
      //   this.$mesage.error('请将必填项补充完整!');
      //   return false;
      // }
      createProcess2(params).then(res3 => {
        let resd3 = res3.data;
        let content3 = resd3.data.content;
        //this.$message.info(content3);
        if(content3 === '操作成功'){
          this.$message.success(content3);
          this.addPress3Data.visible = false;
          if(this.opdiff === 'opt'){
            this.gotenchose();
          }else if(this.opdiff === 'sep'){
            this.getOperateListFun(this.oprateContainerData.selectCat,this.oprateContainerData.process1.operateList);
          }
        }else{
          this.$message.error(content3);
        }
      }).catch(err => {

      })
    },
    Cateoryagment(type){
      if(type === 1){
        //debugger;
        this.process1StoData.cateorynagent = true;
        const process1on = {type: type,enabled:1};
        getcatorylist(process1on).then(res1 => {
          let lank = res1.data.data;
          this.process1StoData.tableData = lank.ppmActivityCategories;
        })
      }
    },
    process2catment(){
      this.process2StoData.cateorynagent2 = true;
      this.process2StoData.catsetting = 1;
      const process2on = {type:2, enabled:1};
      getcatorylist(process2on).then(res2 => {
        let lank2 = res2.data.data;
        this.process2StoData.tableData2 = lank2.ppmActivityCategories;
      })
    },
    process3catment(){
      this.process3StoData.cateorynagent3 = true;
      this.process3StoData.catsetting = 1;
      const process3on = { type: 3,enabled: 1};
      getcatorylist(process3on).then(res3 => {
        let lank3 = res3.data.data;
        this.process3StoData.tableData3 = lank3.ppmActivityCategories;
      })
    },
    //转换时间戳数据
    formatTimestamp(timestamp){
      const date = new Date(timestamp);
      const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以加1，并用0填充
      const daten = String(date.getDate()).padStart(2,'0');//日从0开始，所以加1，并用0补充
      const hours = String(date.getHours()).padStart(2,'0');//时从0开始，所以加1，并用0补充
      const minutes = String(date.getMinutes()).padStart(2,'0');//分从0开始，所以加1，并用0补充
      const seconds = String(date.getSeconds() + 1).padStart(2,'0');//分从0开始，所以加1，并用0补充
      return `${date.getFullYear()}-${month}-${daten} ${hours}:${minutes}:${seconds}`;
    },
    cateorygentCancel(){
      this.process1StoData.cateorynagent = false;
      console.log('1',this.process1StoData);
      console.log('2',this.addPress1Data);
      this.process1StoData.catsetting = 1;
      if(this.opdiff === 'opt'){//从选项管理页面进行点击
        this.addPrecess1Btn(1,'opt','addc');//mor代表前一刻没有点击标签管理或新增标签
      }else if(this.opdiff === 'sep'){
        this.addPrecess1Btn(1,'sep','addc');
      }
    },
    process2cateCancel(){
      this.process2StoData.cateorynagent2 = false;
      this.process2StoData.catsetting = 1;
      if(this.opdiff === 'opt'){
        this.addPrecess1Btn(2,'opt','addc');
      }else if(this.opdiff === 'sep'){
        this.addPrecess1Btn(2,'sep','addc');
      }
    },
    process3cateCancel(){
      //debugger;
      this.process3StoData.cateorynagent3 = false;
      this.process3StoData.catsetting = 1;
      if(this.opdiff === 'opt'){
        this.addPrecess1Btn(3,'opt','addc');
      }else if(this.opdiff === 'sep'){
        this.addPrecess1Btn(3,'sep','addc');
      }
    },
    addCateoy(type){
      this.addcatData.formData = { name: '' };//将弹窗中的缓存清空
      this.addcatent = true;
      // 重置表单验证状态
      this.$nextTick(() => {
        this.$refs.formRef.resetFields();
      });
      this.addcatData.type = type;
      console.log('7.18测试',this.addcatData);
      this.addcatData.clock = false;
    },
    //监听工序选项新增标签弹窗中的输入框中是否有数据
    handleInputChange(){//在输入框中输入或删除内容时才会执行这个方法，现在需要的是鼠标点击了就执行
      //this.addcatData.rules.name = [{required: true,message: '类别名称不能为空',trigger: 'blur'}];
      //console.log('12.24-14:10',this.addcatData.formData.name);
      //这个位置是不是其实应该是：当光标移出按钮并点击其他位置时，判断输入框中是否已经填写了内容，若未填写内容，则给予文字提示呢？
      if(this.addcatData.formData.name !== ''){this.addcatData.clock = true;}else{this.addcatData.clock = false;}
    },
    addpro2Cary(typ2){
      this.addcat2Data.form2Data = { name2:  ''};
      this.addcatent2 = true;
      //重置表单验证状态
      this.$nextTick(() => {//好怪，在工序里就好用，在子工序里就不行！！！！服了。
        ///debugger;
        if(this.$refs.chiformRef){
          this.$refs.chiformRef.resetFields()
          //console.log('还没进来么？')
        }else{}
      })
      this.addcat2Data.type2 = typ2;
      this.addcat2Data.clock2 = false;
    },
    handleInputChange2(){
      //debugger;
      //this.addcat2Data.rules2.name2 = [{required: true, message: '类别名称不能为空',trigger: 'blur'}];
      if(this.addcat2Data.form2Data.name2 !== ''){this.addcat2Data.clock2 = true;}else{this.addcat2Data.clock2 = false;}
    },
    addpro3Cary(typ3){
      this.addcat3Data.form3Data = { name3: ''};
      this.addcatent3 = true;
      //重置表单验证状态
      this.$nextTick(() => {
        this.$refs.opformRef.resetFields();
      })
      this.addcat3Data.type3 = typ3;
      this.addcat3Data.clock3 = false;
    },
    handleInputChange3(){
      //this.addcat3Data.rules3.name3 = [{required: true,message: '类别名称不能为空',trigger: 'blur'}];
      if(this.addcat3Data.form3Data.name3 !== ''){this.addcat3Data.clock3 = true;}else{this.addcat3Data.clock3 = false;}
    },
    addcatcancel(){
      this.addcatent = false;
      this.$refs.all1formRef.clearValidate(['code','name','cat','gxcat','time','catog1']);
      //this.addcatData.rules.name = [{required: false,message: '类别名称不能为空',trigger: 'blur'}];
    },
    addcat2cancel(){
      this.addcatent2 = false;
      this.$refs.all2formRef.clearValidate(['code','name','chidcat','time','catog']);
      //this.addcat2Data.rules2.name2 = [{required: false,message: '类别名称不能为空',trigger: 'blur'}];
    },
    addcat3cancel(){
      this.addcatent3 = false;
      this.$refs.all3formRef.clearValidate(['code','name','time','catog']);
      //this.addcat3Data.rules3.name3 = [{required: false,message: '类别名称不能为空',trigger: 'blur'}]
    },
    addcatOk(){
      let name = this.addcatData.formData.name;
      let typen = this.addcatData.type;
      let paramsc = { name: name,type:typen };
      console.log('执行到这里了么',paramsc);
      addcatCory(paramsc).then(resc => {
        let content = resc.data.data.content;
        console.log('content的值',content);
        if(content === "操作成功"){
          this.$message.success(content);
          console.log('执行到这里了么');
          this.addcatent = false;
          this.process1StoData.catsetting = 1;
          if(this.opdiff === 'opt'){
            this.addPrecess1Btn(1,'opt','addc');//addc代表点击了新增标签或标签管理
          }else if(this.opdiff === 'sep'){
            this.addPrecess1Btn(1,'sep','addc');
          }
        }else{
          console.log(content);
          this.$message.error(content);
        }
      })
      //this.addcatData.rules.name = [{required: false,message: '类别名称不能为空',trigger: 'blur'}];
    },
    addcat2Ok(){
      let name2 = this.addcat2Data.form2Data.name2;
      let typen2 = this.addcat2Data.type2;
      let paramsc2 = { name: name2,type: typen2};
      addcatCory(paramsc2).then(res2 => {
        let content2 = res2.data.data.content;
        if(content2 === '操作成功'){
          this.addcatent2 = false;
          this.process2StoData.catsetting = 1;
          if(this.opdiff === 'opt'){
            this.addPrecess1Btn(2,'opt','addc');
          }else if(this.opdiff === 'sep'){
            this.addPrecess1Btn(2,'sep','addc');
          }
        }else{
          this.$message.error(content2);
        }
      })
      //this.addcat2Data.rules2.name2 = [{required: false,message: '类别名称不能为空',trigger: 'blur'}];
    },
    addcat3Ok(){
      let name3 = this.addcat3Data.form3Data.name3;
      let typen3 = this.addcat3Data.type3;
      let paramsc3 = { name: name3,type: typen3};
      addcatCory(paramsc3).then(res3 => {
        let content3 = res3.data.data.content;
        if(content3 === '操作成功'){
          this.addcatent3 = false;
          this.process3StoData.catsetting  = 1;
          if(this.opdiff === 'opt'){
            this.addPrecess1Btn(3,'opt','addc');
          }else if(this.opdiff === 'sep'){
            this.addPrecess1Btn(3,'sep','addc');
          }
        }else{
          this.$message.error(content3);
        }
      })
    },
    stopusecan(type){
      if(type === 1){//工序的被停用数据
        this.process1StoData.stoporyagent = true;
        const process1st = { type: type, enabled: 0};
        getcatorylist(process1st).then(res1 => {
          let list = res1.data.data;
          this.process1StoData.tableData2 = list.ppmActivityCategories;
          console.log('获取到数据了么',this.process1StoData.tableData2);
        })
      }
    },
    process2stocat(ty2){
      if(ty2 === 2){
        this.process2StoData.stoporyagent2 = true;
        const process2st = {type: ty2,enabled:0};
        getcatorylist(process2st).then(res2 => {
          let list2 = res2.data.data;
          this.process2StoData.tableData22 = list2.ppmActivityCategories;
        })
      }
    },
    process3stocat(ty3){
      if(ty3 === 3){
        this.process3StoData.stoporyagent3 = true;
        const process3st = { type: ty3,enabled: 0};
        getcatorylist(process3st).then(res3 => {
          let list3 = res3.data.data;
          this.process3StoData.tableData32 = list3.ppmActivityCategories;
        })
      }
    },
    stoporyagentCancel(){
      this.process1StoData.stoporyagent = false;
      this.Cateoryagment(1);
    },
    process2yenCancel(){
      this.process2StoData.stoporyagent2 = false;
      this.process2catment();
    },
    process3yenCancel(){
      this.process3StoData.stoporyagent3 = false;
      this.process3catment();
    },

    changeOprateCat(){
      this.getOperateListFun(this.oprateContainerData.selectCat,this.oprateContainerData.process1.operateList)
    },
    toggleoprateItem(oprateItem){
      console.log('点击的操作=', oprateItem)
      if(oprateItem.isSelect){
        if(oprateItem.isLeaf === null || oprateItem.isLeaf === false){}else if(oprateItem.isLeaf === true){
          this.choseover3 = 0;}else{this.choseover3 = 1;}
        this.oprateContainerData.selectOprateList.push(oprateItem)
      }else{
        let getIndex = -1
        this.oprateContainerData.selectOprateList.forEach((seleItem, seleIndex) => {
          if(seleItem.id == oprateItem.id){
            getIndex = seleIndex
          }
        })
        this.oprateContainerData.selectOprateList.splice(getIndex, 1)
        console.log('删除之后的 以选中=', this.oprateContainerData.selectOprateList)
      }
      console.log('12.18-15:06', this.oprateContainerData.selectOprateList);
      if( this.oprateContainerData.selectOprateList.length > 0){this.choseover3 = 1;}
      else{this.choseover3 = 0;}
    },
    handleCheckboxChange3(value){
      console.log('12.18-15:08-value=',value);
      console.log('12.18-15:09-oprateList=',this.oprateContainerData.oprateList);
      this.oprateContainerData.oprateList.forEach(itemo => {
        if(itemo.checkopick === false){
          itemo.isSelect = value === '1' || '' ? value : '1';
        }
      })
    },
    changeTab(num){
      console.log('changeTab=', num)
      this.oprateContainerData.secondTabNum = num
      let str = 'oprateList'+ num
      console.log(str)
      this.oprateContainerData.oprateList = this.oprateContainerData[str]
    },
    selectOprateOk(){
      // 操作选择完毕
      this.mainPage = 'tolooksetup';
      this.oprateContainerData.selectOprateList.forEach((selectOItem, indexSelectOItem) => {
        debugger;
        let newItem = {  }
        if(this.oprateContainerData.type === 1){ // 工序的
          let process1 = this.oprateContainerData.process1;
          console.log('11.19-1很想知道process1的值呢',process1);
          if(this.oprateContainerData.process1.operateList === null){this.oprateContainerData.process1.operateList = [];}
          let len = this.oprateContainerData.process1.operateList.length;
          newItem = {
            product: process1.product, //'产品id',
            process: process1.process, // '工序id',
            activity: selectOItem.id, //'活动ID(操作id)'
            parent: '父活动ID',
            code: selectOItem.code,  // '操作代号'
            name: selectOItem.name, // '名称'
            orders: len + 1+ indexSelectOItem,
            category: selectOItem.category, //'类别id'
            categoryName:  selectOItem.categoryName, // '类别名称'
            type : 4, //  '类型:2-子工序,3-操作,4-套餐'
            //isCyclicity:selectOItem.isCyclicity, // '是否周期性:true-是,false-否(默认)'
            //periodicity: process1.periodicity || "——",//周期性
          }
          selectOItem.product = newItem.product;
          selectOItem.process = newItem.process;
          this.oprateContainerData.process1.operateList.push(newItem)
        }
        else if(this.oprateContainerData.type === 2){ // 子工序的
          let process2 = this.oprateContainerData.process2
          let len = this.oprateContainerData.process2.sonOperateList.length
          debugger;
          newItem = {
            product : process2.product, //'产品id',
            process :process2.process, // '工序id',
            activity : selectOItem.id, //'活动ID(操作id)'
            //parent : '父活动ID(子工序下有操作时，此值是子工序id)',
            parent: process2.activity,//子工序id
            code : selectOItem.code,  // '操作代号'
            name : selectOItem.name, // '名称'
            orders : len + 1+ indexSelectOItem, // '同级排序',
            category : selectOItem.category, //'类别id'
            categoryName :  selectOItem.categoryName, // '类别名称'
            type :  4, //  '类型:2-子工序,3-操作,4-套餐'
            //isCyclicity :selectOItem.isCyclicity, // '是否周期性:true-是,false-否(默认)'
          }
          selectOItem.parent = newItem.parent;
          selectOItem.product = newItem.product;
          selectOItem.process = newItem.process;
          this.oprateContainerData.process2.sonOperateList.push(newItem)

        }
      })
      console.log('12.3',this.tolooksetupData.settingsList2.length);
      let list2t = this.tolooksetupData.settingsList2.length;//这个是新设置的数据
      let listt = this.tolooksetupData.settingsList.length;//这个是旧数据
      //当旧数据有数据时，表示现在是在已经设置了工序相关数据的产品的基础上进行设置，此时右上角不需要展示选项，确定按钮随时都可以
      //点；而如果listt=0则代表是第一次设置的，这时是需要展示右上角的选项和确定按钮灰色不可点击的。
      if(listt === 0){
        list2t = list2t === 0 ? this.collse = 1 : this.collse = 0;
        this.tolooksetupData.setOk = '';
        this.collse2 = '';
      }else{
        this.collse = '';
      }
      console.log('3.7-1',this.tolooksetupData.settingsList2);
      console.log('3.7-2',this.oprateContainerData.selectOprateList);
      for(let a in this.tolooksetupData.settingsList2){
        let sonProcesses = this.tolooksetupData.settingsList2[a].sonProcesses;
        let operateList = this.tolooksetupData.settingsList2[a].operateList;
        for(let b in sonProcesses){
          let bid = sonProcesses[b].activity;//子工序Id
          for(let o in this.oprateContainerData.selectOprateList){
            let oid = this.oprateContainerData.selectOprateList[o].parent;//子工序id
            if(bid === oid){//子工序id相同时
              let sonOper = sonProcesses[b].sonOperateList;//子工序下的操作
              for(let c in sonOper){
                let cid = sonOper[c].activity;
                let opid = this.oprateContainerData.selectOprateList[o].id;
                if(cid === opid){
                  sonOper[c].timing = this.oprateContainerData.selectOprateList[o].timing;
                  sonOper[c].kind = this.oprateContainerData.selectOprateList[o].kind;
                }
              }
            }
          }
        }
        for(let d in operateList){
          let did = operateList[d].activity;
          for(let o in this.oprateContainerData.selectOprateList){
            let pid = this.oprateContainerData.selectOprateList[o].id;
            if(did === pid){
              operateList[d].timing = this.oprateContainerData.selectOprateList[o].timing;
              operateList[d].kind = this.oprateContainerData.selectOprateList[o].kind;
            }
          }
        }
      }
    },
    getOperateListFun(categoryId,listo){
      let data = null;
      console.log('12.3现在的值',this.tolooksetupData.editPro.id);
      let productID = this.tolooksetupData.editPro.id;
      data = { product: productID};
      console.log(categoryId);
      if(categoryId){
        data.categoryId = categoryId;
        //data = { product: categoryId }
      }
      let cantList = this.oprateContainerData.cantSelectOprateList
      let selectList = this.oprateContainerData.selectOprateList
      getOperateList(data).then(res1 => {
        let res = res1.data;
        //console.log('11.18它现在的值',res);
        let operateActivityList = res.data.operateActivityList;//操作数据列表
        // let list1 = res.data.preparationActivityList // 准备工作列表
        // let list2 = res.data.formalActivityList // 正式工作列表
        // let list3 = res.data.closingActivityList // 收尾工作列表
        // let newList1 = [], newList2 = [], newList3 = []
        // list1.forEach(item1 => {
        //   let havSame = false
        //   cantList.forEach(cantItem => {
        //     if(cantItem.activity == item1.id){
        //       havSame = true
        //     }
        //   })
        //   if(!havSame){
        //     item1.isSelect = ''
        //     selectList.forEach(seleItem => {
        //       seleItem.id == item1.id ? item1.isSelect = '1' : ''
        //     })
        //     newList1.push(item1)
        //   }
        // })
        // list2.forEach(item2 => {
        //   let havSame = false
        //   cantList.forEach(cantItem => {
        //     if(cantItem.activity == item2.id){
        //       havSame = true
        //     }
        //   })
        //   if(!havSame){
        //     item2.isSelect = ''
        //     selectList.forEach(seleItem => {
        //       seleItem.id == item2.id ? item2.isSelect = '1' : ''
        //     })
        //     newList2.push(item2)
        //   }
        // })
        // list3.forEach(item3 => {
        //   let havSame = false
        //   cantList.forEach(cantItem => {
        //     if(cantItem.activity == item3.id){
        //       havSame = true
        //     }
        //   })
        //   if(!havSame){
        //     item3.isSelect = ''
        //     selectList.forEach(seleItem => {
        //       seleItem.id == item3.id ? item3.isSelect = '1' : ''
        //     })
        //     newList3.push(item3)
        //   }
        // })
        console.log('11.18-2-1现在的值',cantList);
        console.log('11.18-3-selectList现在的值',selectList);
        //let newList1 = [];
        operateActivityList.forEach(item => {
          let havSame = false;
          // cantList.forEach(cantItem => {
          //   if(cantItem.activity === item.id){
          //     havSame = true;
          //   }
          // })
          // if(!havSame){
          //这个位置需要将item这条数据勾选上
          //item.isSelect = '';
          // item.isSelect = '';
          // selectList.forEach(seleItem => {
          //   seleItem.id === item.id ? item.isSelect = '1' : '';
          // })
          // newList1.push(item);
          // }
          selectList.forEach(selProcess => {
            if(selProcess.id === item.id){
              havSame = true;
            }
          })
          if(havSame === true){item.isSelect = '1';}else{item.isSelect = '';}
          if(item.timing === null){item.timing = "";}else if(item.timing === 1){item.timing = '正式操作之前的准备工作'+"/";}
          else if(item.timing === 2){item.timing = "正式操作"+"/";}else if(item.timing === 3){item.timing = "正式操作之后的收尾工作"+"/";}
          if(item.kind === null){item.kind = "";}else if(item.kind === 1){item.kind ="主要操作"+"/";}
          else if(item.kind === 2){item.kind = "辅助操作"+"/";}
          item.periodicity = item.periodicity == null || undefined ? "——" : item.periodicity;
        })
        this.oprateContainerData.oprateList = operateActivityList;
        // this.oprateContainerData.oprateList1 = newList1
        // this.oprateContainerData.oprateList2 = newList2
        // this.oprateContainerData.oprateList3 = newList3
        this.oprateContainerData.secondTabNum = 1;
        console.log('12-18-15:16-operateActivityList=',operateActivityList);
        console.log('12.18-15:34-listo=',listo);
        for(let o = 0; o<operateActivityList.length;o++){
          let oid = operateActivityList[o].id;
          if(listo === null){listo = [];}
          if(listo.length === 0){operateActivityList[o].checkopick = true;}else{
            for(let i = 0;i<listo.length;i++){
              let aid = listo[i].activity;
              if(oid === aid){
                operateActivityList[o].checkopick = false;
                //好怪，明明下面赋值了的，怎么还是不行呢，怪。
                operateActivityList[o].isSelect = '1';
                break;
              }else{operateActivityList[o].checkopick = true;}
            }
          }
        }
        console.log('12.19-11:49-operateActivityList=',operateActivityList);
        this.oprateContainerData.oprateList = operateActivityList;
      }).catch(err=> {
        console.log('getOperateList err=', err)
      })

    },
    selectOprateBtn(process2, process1, type,pic3,pindx3){
      if(pic3 === 'bck'){//将新设置的操作加在最后面
        this.teppt3 = 1;
      }else if(pic3 === 'bef'){//将新设置的操作加在按钮所在那条数据的前面
        this.teppt3 = 2;
        this.tepindex3 = pindx3;
      }
      // type: 1-工序下的操作，2-子工序下的操作
      //this.oprateContainerData.selectCat = [];
      this.oprateContainerData.selectOprateList = []
      if(type === 1){
        this.oprateContainerData.cantSelectOprateList = process1.operateList
      }else{
        this.oprateContainerData.cantSelectOprateList = process2.sonOperateList
      }
      this.oprateContainerData.secondTabNum = 1
      this.oprateContainerData.process1 = process1;//工序数据
      this.oprateContainerData.process2 = process2;//子工序数据
      this.oprateContainerData.type = type
      this.mainPage = 'oprateContainer';
      if(type === 1){
        this.getOperateListFun(this.oprateContainerData.selectCat,process1.operateList);
      }else{
        this.getOperateListFun(this.oprateContainerData.selectCat,process2.sonOperateList);
      }
      let data = { type: 3 , enabled: 1  }
      getCategoryList(data).then(res1 => {
        let res = res1.data
        this.oprateContainerData.catList = res.data.ppmActivityCategories;
        let jsonall = {
          enabled : true ,
          id :  0,
          name : "全部类别",
          operation :  null ,
          orders :  null,
          previousId :  null,
          type  : 1
        };
        this.oprateContainerData.catList.unshift(jsonall);
      }).catch(err => {
        console.log('getCategoryList oprate=', err)
      })
      if(this.oprateContainerData.selectOprateList.length === 0){this.choseover3 = 0;}else{this.choseover3 = 1;}
      console.log('12.19-11:53-oprateList=',this.oprateContainerData.oprateList);

    },
    selectSonProcess(process1,pic2,pindx){
      this.mainPage = 'chidlistplc';
      if(pic2 === 'bck'){//将新设置的子工序加在最后面
        this.teppt2 = 1;
      }else if(pic2 === 'bef'){//将新设置的子工序加在按钮所在那条数据的前面
        this.teppt2 = 2;
        this.tepindex2 = pindx;
      }
      this.chidlistplcData.process1 = process1
      //this.chidlistplcData.selectCat =  []
      this.chidlistplcData.selectProcess =  []
      this.chidlistplcData.cantSelectProcess = process1.sonProcesses
      this.getSonProcessListFun(this.chidlistplcData.selectCat,this.chidlistplcData.cantSelectProcess)
      let data = { type: 2 , enabled: 1  }
      getCategoryList(data).then(res1 => {
        let res = res1.data
        this.chidlistplcData.catList = res.data.ppmActivityCategories;
        let jsonall = {
          enabled : true ,
          id :  0,
          name : "全部类别",
          operation :  null ,
          orders :  null,
          previousId :  null,
          type  : 1
        };
        this.chidlistplcData.catList.unshift(jsonall);

      }).catch(err => {
        console.log('getCategoryList son=', err)
      })
      if(this.chidlistplcData.selectProcess.length > 0){this.choseover2 = 1;}else{this.choseover2 = 0;}
    },
    changeSonCat(){
      this.getSonProcessListFun(this.chidlistplcData.selectCat, this.chidlistplcData.cantSelectProcess)
    },
    getSonProcessListFun(categoryId,listson){
      let productID = this.tolooksetupData.editPro.id
      let data = { product: productID }
      if(categoryId){
        data.categoryId = categoryId
      }
      getSonProcessList(data).then(res1 => {
        let res = res1.data
        let list = res.data.ppmActivityList
        let showProcessList =  []
        let cantChooseProcess = this.chidlistplcData.cantSelectProcess
        let selectList = this.chidlistplcData.selectProcess
        //debugger;
        list.forEach(item => {
          console.log('item=', item);
          let hasSame = false;
          selectList.forEach(seleProcess => {
            if(seleProcess.id === item.id){
              item.havSonProcess = seleProcess.havSonProcess;
              hasSame = true;
            }
          })
          cantChooseProcess.forEach(cantProcess => {
            console.log('cantProcess=',cantProcess)
            if(item.id === cantProcess.activity){
              hasSame = true
            }
          })
          if(hasSame === true){item.isSelect = '1';}else{item.isSelect = '';}
          showProcessList.push(item);
          // if(!hasSame){
          //   item.isSelect = ''
          //   item.havSonProcess = ''
          //   selectList.forEach(seleItem => {
          //     if(seleItem.id == item.id){
          //       item.isSelect = '1'
          //       item.havSonProcess = seleItem.havSonProcess
          //     }
          //   })
          // }
        })
        showProcessList.forEach(items => {
          if(items.timing === null){items.timing = "";}else if(items.timing === 1){items.timing = "正式操作之前的准备工作"+"/";}
          else if(items.timing === 2){items.timing = "正式操作"+"/";}else if(items.timing === 3){items.timing = "正式操作之后的收尾工作"+"/";}
          if(items.kind === null){items.kind = "";}else if(items.kind === 1){items.kind = "主要操作"+"/";}else if(items.kind === 2){items.kind = "辅助操作"+"/";}
          //items.periodicity = items.periodicity == null  || undefined ? "——" : items.periodicity;
        })
        this.chidlistplcData.processList = showProcessList;
        console.log('12.18-14:31-showProcessList=',showProcessList);
        console.log('12.18-14:33-this.tolooksetupData.settingsList2=',this.tolooksetupData.settingsList2);
        console.log('12.18-14:44-listson=',listson);
        for(let s = 0;s<showProcessList.length;s++){
          let sid = showProcessList[s].id;
          //这里需要知道此时‘添加本工序下的子工序‘按钮所在的那条工序下是否已经有子工序数据了
          if(listson.length === 0){showProcessList[s].checkchick = true;}else{
            for(let t = 0;t<listson.length;t++){
              let tid = listson[t].activity;
              if(tid === sid){showProcessList[s].checkchick = false;break;}else{showProcessList[s].checkchick = true;}
            }
          }
        }
        console.log('展示子列表', showProcessList);
        this.chidlistplcData.processList = showProcessList;
      }).catch(err => {
        console.log('getSonProcessList=', err)

      })
    },
    selectProcess2Ok(){
      // 子工序 选择完毕
      debugger;
      this.mainPage = 'tolooksetup'
      let order = this.chidlistplcData.process1.sonProcesses.length
      // let productID = this.tolooksetupData.editPro.id ;
      let productID = this.chidlistplcData.process1.product;
      let process1ID = this.chidlistplcData.process1.process;
      console.log('productID的值',productID);
      console.log('process1ID的值',process1ID);
      this.chidlistplcData.selectProcess.forEach((seleItem2, seleIndex2) => {
        let newitem = {
          product : productID, //  '产品id',
          process : process1ID , // '工序id' ,
          activity : seleItem2.id, // '(子工序id)',
          code : seleItem2.code , // '代号'
          name : seleItem2.name , //  '名称'
          orders : order + 1 + seleIndex2 ,
          category : seleItem2.category, // '类别id'
          categoryName : seleItem2.categoryName, // '类别名称',
          type :2, //  '类型:2-子工序,3-操作,4-套餐'
          sonOperateList :  [],
        };
        newitem.isLeaf = seleItem2.isLeaf;
        //if(seleItem2.isLeaf === false)
        if(this.teppt2 === 1){
          this.chidlistplcData.process1.sonProcesses.push(newitem);
        }else if(this.teppt2 === 2){
          this.chidlistplcData.process1.sonProcesses.splice(this.tepindex2,0,newitem);
        }
        console.log('12.3现在的值',this.chidlistplcData.process1.sonProcesses);
        console.log('12.3一共的数据',this.tolooksetupData.settingsList2);
      })
      console.log('12.3',this.tolooksetupData.settingsList2);
      let list = this.tolooksetupData.settingsList.length;
      let list2k = this.tolooksetupData.settingsList2.length;
      if(list === 0){
        list2k = list2k === 0 ? this.collse = 1 : this.collse = 0;
        this.tolooksetupData.setOk = '';
        this.collse2 = '';
      }else{
        this.collse = '';
      }
      //debugger;
      console.log('2',this.chidlistplcData.selectProcess);
      console.log('3',this.tolooksetupData.settingsList2);
      for(let a in this.tolooksetupData.settingsList2){
        debugger;
        let sonProcesses = this.tolooksetupData.settingsList2[a].sonProcesses;
        for(let a1 in sonProcesses){
          let a1id = sonProcesses[a1].activity;
          for(let b in this.chidlistplcData.selectProcess){
            let bid = this.chidlistplcData.selectProcess[b].activity || this.chidlistplcData.selectProcess[b].id;
            debugger;
            if(sonProcesses[a1].clkset === 2){
              if(this.tolooksetupData.settingsList2[a].clkset === 2){
                sonProcesses[a1].clkset = 2;
              }else if(this.tolooksetupData.settingsList2[a].clkset === 1){
                sonProcesses[a1].clkset = 1;
              }
            }else{
              if(a1id === bid){//a1id是在作为旧数据的工序上新设置的子工序,cllkset需要=1
                if(sonProcesses[a1].clkset === undefined){
                  if(this.tolooksetupData.settingsList2[a].clkset === 2){
                    sonProcesses[a1].clkset = 2;
                  }else if(this.tolooksetupData.settingsList2[a].clkset === 1){
                    sonProcesses[a1].clkset = 1;
                  }
                  //console.log('4.15-8:47',this.tolooksetupData.settingsList2)
                  // if(this.tolooksetupData.settingsList2[a].clkset === 2){
                  //   sonProcesses[a1].clkset = 2;
                  // }else{
                    // var a2 = Number(a1)-1;
                    // sonProcesses[a1].clkset = sonProcesses[a2].clkset;
                  //}
                  // if(sonProcess[a2.clkset === undefined){
                  // }else{
                  //   sonProcesses[a1].clkset = sonProcesses[a2].clkset;
                  // }
                  // if(sonProcesses[a1].clkset === ""){sonProcesses[a1].clkset = 2;}
                }else{
                  if(this.tolooksetupData.settingsList2[a].clkset === 2){
                    sonProcesses[a1].clkset = 2;
                  }else if(this.tolooksetupData.settingsList2[a].clkset === 1){
                    sonProcesses[a1].clkset = 1;
                  }
                  //sonProcesses[a1].clkset = 2;//   clkset: '',//1-点击了‘设置’后跳转过来的，2-点击了‘设置’以外的按钮跳转过来的
                }
              }else{sonProcesses[a1].clkset = 1;}
            }
            if(a1id === bid){
              sonProcesses[a1].timing = this.chidlistplcData.selectProcess[b].timing;
              sonProcesses[a1].kind = this.chidlistplcData.selectProcess[b].kind;
            }
            //console.log('4.14-13:40',sonProcesses[a1].clkset);
//            console.log('4.14-13:45',this.configu);
            console.log('4.14-14:15',sonProcesses[a1].isLeaf);
            console.log('4.14-14:44',sonProcesses[a1].clkset);
          }
        }
      }
      console.log('3.7-1',this.tolooksetupData.settingsList2);
    },
    selectProcess1Ok(){
      // 工序 选择完毕
      //debugger;
      let noComplete = 0
      this.chosesetenData.selectProcess.forEach((seleItem, seleIndex) => {
        console.log('seleItem=', seleItem)
        // if(!seleItem.havSonProcess){
        //   noComplete++;
        // }
      })
      if(noComplete > 0){
        //this.$message.error('还有未选择的部分需要填写！')
      }else{
        this.mainPage = 'tolooksetup';
        let order = this.tolooksetupData.settingsList2.length
        console.log('11.19chosesetenData现在的数据',this.chosesetenData);
        this.chosesetenData.selectProcess.forEach((seleItem2, seleIndex2) => {
          let newitem = {
            code :  seleItem2.code ,
            name  :  seleItem2.name ,
            operateList : [], //工序下的操作列表
            orders : order + 1 + seleIndex2, // 同级排序
            org : '',
            process : seleItem2.id , // 工序id
            product :  '', //产品id
            sonProcesses  :  [],
            havSonProcess: seleItem2.havSonProcess,
            //periodicity: seleItem2.periodicity
          };
          if(seleItem2.isLeaf === false || seleItem2.isLeaf === null){
            seleItem2.havSonProcess = 2;//this.chidchis = 2;
          }else if(seleItem2.isLeaf === true){seleItem2.havSonProcess = 1;//this.chidchis = 1;
          }
          newitem.isLeaf = seleItem2.isLeaf;
          if(this.teppt1 === 1){
            this.tolooksetupData.settingsList2.push(newitem);
          }else if(this.teppt1 === 2){
            //该如何获取到’在上面添加工序‘按钮所在的那条工序数据的索引值呢？
            // this.items.splice(index, 0, newItem);
            this.tolooksetupData.settingsList2.splice(this.tepindex1,0,newitem);
            //因为每次获取索引值都是单一一个，相当于点击了索引值是1的工序那行的按钮，索引值就是1；
            //待点击另一个工序对应的按钮时，又会重新赋值新的索引值，所以不用担心索引值会有误。
          }
          //this.tolooksetupData.settingsList2.unshift(newitem);
          console.log('11.20现在的值',this.tolooksetupData.settingsList2);

        })
      }
      if(this.tolooksetupData.settingsList.length === 0){
        if(this.tolooksetupData.settingsList2.length === 0){
          this.collse = 1;
        }else{
          this.collse = 0;
        }
      }else{
        this.collse = '';
      }
      //现在需要的是：无论是增加一条新数据还是进行删除、排序，只要进行了设置，tolooksetupData.setOk就要
      //变成未选中状态
      this.tolooksetupData.setOk = '';
      this.collse2 = '';
      console.log('1',this.tolooksetupData.settingsList2);
      console.log('2',this.chosesetenData.selectProcess);
      //process iid
      //{1,2}   {3,4}
      //如果tollooksetupData中只有旧数据，则下面的可以，但如果tolooksetupDataa中
      //有了新数据，再用下面的方式排除就会出现乱套了
      for(let a in this.tolooksetupData.settingsList2){
        let aid = this.tolooksetupData.settingsList2[a].process;
        for(let b in this.chosesetenData.selectProcess){
          let bid = this.chosesetenData.selectProcess[b].id;
          if(this.tolooksetupData.settingsList2[a].clkset === 2){//此时代表tolooksetupData中已经有新数据了，所以在判断的时候需要将它
            //排除掉
          }else{
            if(aid === bid){
              this.tolooksetupData.settingsList2[a].clkset = 2;
            }else{this.tolooksetupData.settingsList2[a].clkset = 1;}
          }
          if(aid === bid){
            this.tolooksetupData.settingsList2[a].timing = this.chosesetenData.selectProcess[b].timing;
            this.tolooksetupData.settingsList2[a].kind = this.chosesetenData.selectProcess[b].kind;
          }
        }
      }
    },
    toggleSelectItem(processItem){
      console.log('点击的工序=', processItem);
      //debugger;
      if(processItem.isSelect){//这里相当于是判断那条被勾选了，然后该条数据是否需要展示或是隐藏什么。
        if(processItem.isLeaf === null || processItem.isLeaf === false){//此时的情况是该工序数据不能设置子工序或操作数据
          processItem.haschild = 2;
          //debugger;
          this.chosesetenData.selectProcess.push(processItem);
          if(this.chosesetenData.selectProcess.length === 0){this.choseover = 1;}else{
            for(let c in this.chosesetenData.selectProcess){
              if(this.chosesetenData.selectProcess[c].havSonProcess === false){this.choseover = 1;}else{
                if(this.chosesetenData.selectProcess[c].havSonProcess === ''){
                  if(this.configu === true){//首页状态为‘允许设置子工序’
                    if(this.chosesetenData.selectProcess[c].isLeaf === true){
                      this.choseover = 0;break;
                    }else if(this.chosesetenData.selectProcess[c].isLeaf === false){this.choseover = 1;}
                  }else if(this.configu === false){//首页状态为‘不允许设置子工序’
                    this.choseover = 1;
                  }
                  // if(this.chosesetenData.selectProcess[c].isLeaf === false){this.choseover = 1;}
                  // else if(this.chosesetenData.selectProcess[c].isLeaf === true){this.choseover =0;break;}
                }else if(this.chosesetenData.selectProcess[c].havSonProcess === undefined){
                  if(this.configu === true){//首页状态为‘允许设置子工序’
                    if(this.chosesetenData.selectProcess[c].isLeaf === true){
                      this.choseover = 0;break;
                    }else if(this.chosesetenData.selectProcess[c].isLeaf === false){this.choseover = 1;}
                  }else if(this.configu === false){//首页状态为‘不允许设置子工序’
                    this.choseover = 1;
                  }
                  //12.2-2属于明明是可以设置子工序或操作的，但因为没进行选中，所以12.2-2的havSonProcess=undefined，但
                  //因为它还没有勾选是否设置子工序或操作，故不能让右上角的按钮可以点击
                  // if(this.chosesetenData.selectProcess[c].isLeaf === true){this.choseover = 0;break;}else{this.choseover = 1;}
                }else{this.choseover = 1;}
              }
            }
            // this.chosesetenData.selectProcess.forEach((seleItem2,seleIndex2) => {
            //   if(seleItem2.havSonProcess === false){this.choseover = 1;}else{
            //     if(seleItem2.havSonProcess === '' || seleItem2.havSonProcess === undefined){
            //       this.choseover = 0;return false;}else{this.choseover = 1;}
            //   }
            // })
          }
        }else if(processItem.isLeaf === true){//此时的情况是该工序数据可以设置子工序或操作
          processItem.haschild = 1;
          //debugger;
          //console.log(processItem.havSonProcess);
          //我知道了，不能只用isLeaf去判断该工序数据是否需要‘有’或‘没有’
          if(this.configu === false){//首页设置为不允许设置子工序
            this.choseover = 1;
          }else if(this.configu === true){//首页设置允许设置子工序
            if(processItem.havSonProcess === '' || processItem.havSonProcess === undefined){//此时是并未进行‘有’还是‘没有’的选择
              this.choseover = 0;
            }else{this.choseover = 1;}
          }
          this.chosesetenData.selectProcess.push(processItem);
          if(this.chosesetenData.selectProcess.length === 0){}else {
            for (let c in this.chosesetenData.selectProcess) {
              if (this.chosesetenData.selectProcess[c].havSonProcess === false) {
                this.choseover = 1;
              } else {
                if (this.chosesetenData.selectProcess[c].havSonProcess === '') {
                  if (this.configu === true) {//首页状态为‘允许设置子工序’
                    if (this.chosesetenData.selectProcess[c].isLeaf === true) {
                      this.choseover = 0;
                      break;
                    } else if (this.chosesetenData.selectProcess[c].isLeaf === false) {
                      this.choseover = 1;
                    }
                  } else if (this.configu === false) {//首页状态为‘不允许设置子工序’
                    this.choseover = 1;
                  }
                  // if(this.chosesetenData.selectProcess[c].isLeaf === false){this.choseover = 1;}
                  // else if(this.chosesetenData.selectProcess[c].isLeaf === true){this.choseover =0;break;}
                } else if (this.chosesetenData.selectProcess[c].havSonProcess === undefined) {
                  if (this.configu === true) {//首页状态为‘允许设置子工序’
                    if (this.chosesetenData.selectProcess[c].isLeaf === true) {
                      this.choseover = 0;
                      break;
                    } else if (this.chosesetenData.selectProcess[c].isLeaf === false) {
                      this.choseover = 1;
                    }
                  } else if (this.configu === false) {//首页状态为‘不允许设置子工序’
                    this.choseover = 1;
                  }
                  //12.2-2属于明明是可以设置子工序或操作的，但因为没进行选中，所以12.2-2的havSonProcess=undefined，但
                  //因为它还没有勾选是否设置子工序或操作，故不能让右上角的按钮可以点击
                  // if(this.chosesetenData.selectProcess[c].isLeaf === true){this.choseover = 0;break;}else{this.choseover = 1;}
                } else {
                  this.choseover = 1;
                }
              }
            }
          }
          // if(processItem.havSonProcess === '' || processItem.havSonProcess === undefined){//此时是并未进行‘有’还是‘没有’的选择
          //   this.choseover = 0;}else{this.choseover = 1;}
        }
        // this.chosesetenData.selectProcess.forEach((seleItem2,seleIndex2) => {
        //   //debugger;
        //   if(seleItem2.havSonProcess === false){this.choseover = 1;}else{
        //     if(seleItem2.havSonProcess === '' || seleItem2.havSonProcess === undefined){
        //       this.choseover = 0;return false;}else{this.choseover = 1;}
        //     }
        // })
        //if(this.setten === 1){processItem.haschild = 0;}
        //if(this.chidchis === 1){processItem.haschild = 1;}
      }else{//取消勾选
        let getIndex = -1;
        if(processItem.isLeaf === null || processItem.isLeaf === false){
          processItem.haschild = 2;
        }else if(processItem.isLeaf === true){
          processItem.haschild = 1;
          //processItem.haschild = 0;
        }
        processItem.havSonProcess = '';
        this.chosesetenData.selectProcess = this.chosesetenData.selectProcess.filter(item => item.id !== processItem.id);
        // this.chosesetenData.selectProcess.forEach((seleItem, seleIndex) => {
        //   if(seleItem.id === processItem.id){
        //     getIndex = seleIndex
        //   }
        // })
        // this.chosesetenData.selectProcess.splice(getIndex, 1);
        console.log('删除之后的 以选中=', this.chosesetenData.selectProcess);
        if(this.chosesetenData.selectProcess.length === 0){this.choseover = 0;}
        for(let o in this.chosesetenData.selectProcess){
          if(this.chosesetenData.selectProcess[o].havSonProcess === '' ||
              this.chosesetenData.selectProcess[o].havSonProcess === undefined){
            if(this.configu === true){
              if(this.chosesetenData.selectProcess[o].isLeaf === false){
                this.choseover = 1;
              }else if(this.chosesetenData.selectProcess[o].isLeaf === true){
                this.choseover = 0;break;
              }
            }else if(this.configu === false){this.choseover = 1;}
            // if(this.chosesetenData.selectProcess[o].isLeaf === false){
            //   this.choseover = 1;
            // }else{this.choseover = 0;break;}
          }else{this.choseover = 1;}
          // if(this.chosesetenData.selectProcess[o].havSonProcess === ''){this.choseover = 0;break;}
          // else if(this.chosesetenData.selectProcess[o].havSonProcess === undefined){this.choseover = 1;}
          //else{this.choseover = 1;}
        }
        // this.chosesetenData.selectProcess.forEach((seleItem2,seleIndex2) => {
        //   if(seleItem2.havSonProcess === ''){this.choseover = 0;return false;}else if(seleItem2.havSonProcess === undefined){
        //     this.choseover = 1;}else{this.choseover = 1;}
        // })
        //if(this.chidchis === 1){processItem.haschild = 0;}
      }
      console.log('2024.12.12-11:39',this.chosesetenData.selectProcess);
      //if(this.chosesetenData.selectProcess.length > 0){this.choseover = 1;}else{this.choseover = 0;}
    },
    toggleSelectSonItem(processItem){
      console.log('点击的工序=', processItem)
      //debugger;
      if(processItem.isSelect){
        if(processItem.isLeaf === null || processItem.isLeaf === false){}else if(processItem.isLeaf === true){
          this.choseover2 = 0;}else{this.choseover2 = 1;}
        this.chidlistplcData.selectProcess.push(processItem)
      }else{
        let getIndex = -1
        this.chidlistplcData.selectProcess.forEach((seleItem, seleIndex) => {
          if(seleItem.id == processItem.id){
            getIndex = seleIndex
          }
        })
        this.chidlistplcData.selectProcess.splice(getIndex, 1)
        console.log('删除之后的 以选中=', this.chidlistplcData.selectProcess)
      }
      console.log('12.18-11:59',this.chidlistplcData.selectProcess);
      debugger;
      if(this.chidlistplcData.selectProcess.length > 0){this.choseover2 = 1;}
      else{this.choseover2 = 0;}
    },
    handleCheckboxChange2(value){
      console.log('12.18-13:27-value=',value);
      console.log('12.18-13:28-processList',this.chidlistplcData.processList);
      this.chidlistplcData.processList.forEach(itemh => {
        if(itemh.checkchick === false){
          itemh.isSelect = value === '1' || '' ? value : '1';
        }
      })
    },
    OperateCrol(type, process1, process2, operitem, indexOperate){//process1是工序，process2是子工序，operitem是操作数据,indexOperate
      //是索引值
      if(type === 'del'){
        //debugger;
        // 删除操作
        let tip = '确定删除这条数据吗？'
        if(process2 !== 'no1'){//用于区分是该工序下是否有子工序，若没有子工序，process2='no1'
          tip = '确定删除这条数据吗？'
        }
        this.operateCrolData.operateDelVisible = true
        this.operateCrolData.type = type
        this.operateCrolData.process1 = process1
        this.operateCrolData.process2 = process2
        this.operateCrolData.operitem = operitem
        this.operateCrolData.indexOperate = indexOperate
        this.operateCrolData.tip = tip
      }
      else if(type === 'sort'){
        this.statbegn3 = false;
        // 排序操作
        debugger;
        let needArr = []
        if(process2 === 'no1'){ // 工序下的
          needArr = process1.operateList
        } else { // 子工序下的
          needArr = process2.sonOperateList
        }
        this.oprateOrderData.needArr = needArr
        let needArr2 = JSON.parse(JSON.stringify(needArr))
        needArr2.splice(indexOperate, 1);
        this.oprateOrderData.oprateList = needArr2
        //debugger;
        console.log('2.19-1',this.oprateContainerData.oprateList);
        console.log('2.19-2',this.oprateOrderData.oprateList);
        for(let a in this.oprateContainerData.oprateList){
          for(let b in this.oprateOrderData.oprateList){
            if(this.oprateContainerData.oprateList[a].id === this.oprateOrderData.oprateList[b].activity){
              this.oprateOrderData.oprateList[b].timing = this.oprateContainerData.oprateList[a].timing;
              this.oprateOrderData.oprateList[b].kind = this.oprateContainerData.oprateList[a].kind;
            }
          }
        }
        // if(this.oprateOrderData.oprateList.length === 0){
        //   this.$message.error('此操作前没有可供选择的操作哦！')
        //   return false
        // }
        this.oprateOrderData.orderOprateItemIndex = ''
        this.oprateOrderData.orderOprateItemIndex2 = '';
        this.oprateOrderData.process1 = process1
        this.oprateOrderData.process2 = process2
        this.oprateOrderData.editOprate = operitem
        this.oprateOrderData.indexOperate = indexOperate
        console.log('oprateOrderData现在的数据',this.oprateOrderData.orderOprateItemIndex);
        this.mainPage = 'oprateOrder'
      }
    },
    selectOprateOrdOk(){
      debugger;
      let oprateSelectIndex = this.oprateOrderData.orderOprateItemIndex
      //let oprateSelectIndex2 = this.oprateOrderData.orderOprateItemIndex2
      if(oprateSelectIndex === ''){
        this.$message.error('您还没有选择！')
      } else {
        let needArr = []
        if(this.oprateOrderData.process2 === 'no1'){ // 工序下的
          needArr = this.oprateOrderData.process1.operateList
        } else { // 子工序下的
          needArr = this.oprateOrderData.process2.sonOperateList
        }
        if(oprateSelectIndex === 0){ // 最前面
          let indexOperate = this.oprateOrderData.indexOperate
          let editOprate = this.oprateOrderData.editOprate
          needArr.splice(indexOperate, 1)
          needArr.unshift(editOprate)
        }
        else { // 不是最前面的
          needArr.splice(this.oprateOrderData.indexOperate, 1); // 先删除原来的
         // console.log(this.oprateOrderData);
          let poid = this.oprateOrderData.orderOprateItemIndex2;
          let poeleindex = "";
          needArr.forEach((item1,index1) => {
            if(item1.activity === poid || item1.id === poid){
              poeleindex = index1;
            }
          })
          poeleindex = Number(poeleindex) + 1;
          needArr.splice(poeleindex, 0, this.oprateOrderData.editOprate); // 再插入到新的位置
        }
        console.log(needArr);
        let newArr = []
        needArr.forEach((newOrdOprate, index) => {
          newOrdOprate.orders = index + 1;
          newArr.push(newOrdOprate)
        })
        if(this.oprateOrderData.process2 === 'no1'){ // 工序下的
          this.oprateOrderData.process1.operateList = newArr
        } else { // 子工序下的
          this.oprateOrderData.process2.sonOperateList = newArr
        }
        this.mainPage = 'tolooksetup'
      }
      console.log('12.3',this.tolooksetupData.settingsList2.length);
      //debugger;
      let setlinks = this.tolooksetupData.settingsList.length;//旧数据
      let setlink = this.tolooksetupData.settingsList2.length;//这里得根据是初次设置还是之后的设置来判断
      if(setlinks === 0){
        setlink = setlink === 0 ? this.collse = 1 : this.collse = 0;
        this.tolooksetupData.setOk = '';
        this.collse2 = '';
      }else{
        this.collse = '';
      }
    },
    OperateDelCancel(){
      this.operateCrolData.operateDelVisible = false
    },
    OperateDelOk(){
      debugger;
      let process1 = this.operateCrolData.process1;//工序数据
      let process2 = this.operateCrolData.process2;//子工序数据
      let indexOperate = this.operateCrolData.indexOperate
      if(process2 !== 'no1'){
        if(process2.sonOperateList.length === 1){
          process2.sonOperateList.splice(indexOperate, 1);
          // 重新排序
          process2.sonOperateList.forEach((sonOItem, indexSonOperItem) => {
            sonOItem.orders = indexSonOperItem + 1
          })
          //  this.$message.error('操作失败！一个产品至少需有一道工序，每道工序下至少需有一项操作！');
        }else{
          process2.sonOperateList.splice(indexOperate, 1);
          // 重新排序
          process2.sonOperateList.forEach((sonOItem, indexSonOperItem) => {
            sonOItem.orders = indexSonOperItem + 1
          })
        }
      }else{
        if(process1.operateList.length === 1){
          process1.operateList.splice(indexOperate, 1)
          // 重新排序
          process1.operateList.forEach((oItem, indexOperItem) => {
            oItem.orders = indexOperItem + 1
          })
          //this.$message.error('操作失败！一个产品至少需有一道工序，每道工序下至少需有一项操作！');
        }else{
          process1.operateList.splice(indexOperate, 1)
          // 重新排序
          process1.operateList.forEach((oItem, indexOperItem) => {
            oItem.orders = indexOperItem + 1
          })
        }
      }
      this.operateCrolData.operateDelVisible = false
      this.tolooksetupData.setOk = '';
      console.log(this.collse);
      this.collse2 = '';
    },
    getordent(labo2,listo2){
      //console.log('12.18-10:42-labo2=',labo2);
      //console.log('12.18-10:42',listo2.orderProcess1ItemIndex);
      debugger;
      if(listo2.orderOprateItemIndex === labo2){
        listo2.orderOprateItemIndex = '';
      }else{
        listo2.orderOprateItemIndex = labo2;
        if(listo2.orderOprateItemIndex === 0){listo2.orderOprateItemIndex2 = '';}else{}
      }
      let index1 = listo2.orderOprateItemIndex;
      let index2 = listo2.orderOprateItemIndex2;
      //console.log('12.18-10:46-index1=',index1);
      //console.log('12.18-10:47-index2=',index2);
      if(index1 === 1 && index2){this.statbegn3 = true;}
      else if(index1 === 0){this.statbegn3 = true;}
      else{this.statbegn3 = false;}
    },
    getnetochoe(labo,listro){
      //console.log('12.18-10:50-labo=',labo);
      //console.log('12.18-10:51-listro=',listro);
      debugger;
      if(listro.orderOprateItemIndex2 === labo){
        listro.orderOprateItemIndex2 = '';
      }else{
        listro.orderOprateItemIndex2 = labo;
        if(listro.orderOprateItemIndex !== 1){listro.orderOprateItemIndex = ""}
        // if(listro.orderProcess1ItemIndex2 === 1){}
      }
      let index1 = listro.orderOprateItemIndex;
      let index2 = listro.orderOprateItemIndex2;
      //console.log('12.18-10:53-index1=',index1);
      //console.log('12.18-10:54-index2=',index2);
      if(index1 === 1 && index2 === labo){this.statbegn3 = true;}
      else if(index1 === 0){this.statbegn3 = true;}
      else{this.statbegn3 = false;}
      if(index2 === labo){
        if(index1 !== 1){listro.orderOprateItemIndex = '';this.statbegn3 = false;}
      }
    },
    process2Ctrol(type, process1, process2,indexProcess2){
      // 子工序按钮方法  type :排序 'sort', 删除 del
      if(type === 'sort'){
        console.log('7.17是否执行到这里');
        this.statbegn2 = false;
        //debugger;
        let needpr2Arr = process1.sonProcesses;
        this.process2OrderData.needpr2Arr = needpr2Arr;
        console.log('needpr2Arr现在的值',needpr2Arr);
        let needpr2Arr2 = JSON.parse(JSON.stringify(needpr2Arr));
        needpr2Arr2.splice(indexProcess2,1);
        console.log(needpr2Arr2);
        this.process2OrderData.process2List = needpr2Arr2;
        console.log('11:39现在的值',this.chidlistplcData.processList);
        for(let a in this.chidlistplcData.processList){
          for(let b in this.process2OrderData.process2List){
            if(this.chidlistplcData.processList[a].id === this.process2OrderData.process2List[b].activity){
              this.process2OrderData.process2List[b].timing = this.chidlistplcData.processList[a].timing;
              this.process2OrderData.process2List[b].kind = this.chidlistplcData.processList[a].kind;
            }
          }
        }
        this.process2OrderData.process1 = process1;
        this.process2OrderData.editProcess2 = process2;
        this.process2OrderData.orderProcess1ItemIndex = '';
        this.process2OrderData.orderProcess1ItemIndex2 = '';
        this.process2OrderData.indexProcess2 = indexProcess2;
        console.log('子工序数据默认取消勾选了么',this.process2OrderData);
        this.mainPage = 'process2Order';
      }else if(type === 'del'){
        //删除子工序
        let delcg = '';
        let third = '确定删除这条数据吗？';
        let ford = '';
        delcg = third + ford;
        this.process2CrolData.process2DelVisible = true;
        this.process2CrolData.process1 = process1;
        this.process2CrolData.process2 = process2;
        this.process2CrolData.indexProcess2 = indexProcess2;
        this.process2CrolData.pro2tip = delcg;
        this.process2CrolData.type = type;
        console.log('7.17',this.process2CrolData);
      }
    },
    getchident(labc2,listc2){
      //console.log('12.18-10:10-labc2=',labc2);
      //console.log('12.18-10:10-listc2=',listc2);
      if(listc2.orderProcess1ItemIndex === labc2){
        listc2.orderProcess1ItemIndex = '';
      }else{
        listc2.orderProcess1ItemIndex = labc2;
        if(listc2.orderProcess1ItemIndex === 0){listc2.orderProcess1ItemIndex2 = '';}else{}
      }
      let indexc1 = listc2.orderProcess1ItemIndex;
      let indexc2 = listc2.orderProcess1ItemIndex2;
      if(indexc1 === 1 && indexc2){this.statbegn2 = true;}
      else if(indexc1 === 0){this.statbegn2 = true;}
      else{this.statbegn2 = false;}
    },
    getnetchichoe(labc,listrc){//labc:勾选的数据的id
      debugger;
      this.process2OrderData.chosid = labc;
      // console.log('12.18-10:22-labc=',labc);
      // console.log('12.18-10:23-listrc=',listrc);
      if(listrc.orderProcess1ItemIndex2 === labc){
        listrc.orderProcess1ItemIndex2 = '';
      }else{
        listrc.orderProcess1ItemIndex2 = labc;
        // if(listrc.orderProcess1ItemIndex !== 1){listrc.orderProcess1ItemIndex = "";}
        // if(listrc.orderProcess1ItemIndex2 === 1){}
      }
      let indexc1 = listrc.orderProcess1ItemIndex;
      let indexc2 = listrc.orderProcess1ItemIndex2;
      //console.log('12.18-10:25-indexc1=',indexc1);
      //console.log('12.18-10:34-indexc2=',indexc2);
      if(indexc1 === 1 && indexc2 === labc){this.statbegn2 = true;}
      else if(indexc1 === 0){this.statbegn2 = true;}
      else{this.statbegn2 = false;}
      if(indexc2 === labc){
        if(indexc1 !== 1){listrc.orderProcess1ItemIndex = '';this.statbegn2 = false;}
      }
    },
    selectProcess2OrdOk(){
      debugger;
      let process2SelectIndex = this.process2OrderData.orderProcess1ItemIndex;
      //let process2SelectIndex2 = this.process2OrderData.orderProcess1ItemIndex2;
      if(process2SelectIndex === ''){
        this.$message.error('您还没有选择！');
      }else{
        let needpr2Arr = [];
        needpr2Arr = this.process2OrderData.process1.sonProcesses;
        if(process2SelectIndex === 0){
          let indexProcess2 = this.process2OrderData.indexProcess2;
          let editProcess2 = this.process2OrderData.editProcess2;
          needpr2Arr.splice(indexProcess2,1);
          needpr2Arr.unshift(editProcess2);
        }else{
          needpr2Arr.splice(this.process2OrderData.indexProcess2,1);
          let pchid = this.process2OrderData.orderProcess1ItemIndex2;
          let pchindex = '';
          needpr2Arr.forEach((item2,index2) => {
            if(item2.activity  === pchid || item2.id === pchid){
              pchindex = index2;
            }
          })
          pchindex = Number(pchindex) + 1;
          needpr2Arr.splice(pchindex,0,this.process2OrderData.editProcess2);
        }
        let newpr2Arr = [];
        needpr2Arr.forEach((newOrdProcess2,index) =>{
          newOrdProcess2.orders = index + 1;
          newpr2Arr.push(newOrdProcess2);
        })
        this.process2OrderData.process1.sonProcesses = newpr2Arr;
        this.mainPage = 'tolooksetup';
        console.log('3.7',needpr2Arr);
      }
      //console.log('3.7',this.process2StoData.sonProcesses);
      console.log('12.3',this.tolooksetupData.settingsList2.length);
      //debugger;
      let listts = this.tolooksetupData.settingsList.length;//旧数据
      let listt = this.tolooksetupData.settingsList2.length;
      if(listts === 0){
        listt = listt === 0 ? this.collse = 1 : this.collse = 0;
        this.tolooksetupData.setOk = '';
        this.collse2 = '';
      }else{this.collse = '';}
    },
    Process2DelCancel(){
      this.process2CrolData.process2DelVisible = false;
    },
    Process2DelOk(){
      let process1 = this.process2CrolData.process1;//工序数据
      let process2 = this.process2CrolData.process2;//子工序数据
      let indexProcess2 = this.process2CrolData.indexProcess2;
      //子工序删除时应该分为有子工序的工序和没有子工序的工序吧
      console.log('7.17',process1);//工序数据
      console.log('7.17',process2);//子工序数据
      //嗯好像也不用判断是否有子工序
      if(process1.sonProcesses.length === 1){
        process1.sonProcesses.splice(indexProcess2,1);
        process1.sonProcesses.forEach((pro2Item,indexPro2Item) => {
          pro2Item.orders = indexPro2Item + 1;
        })
        this.process2CrolData.process2DelVisible = false;
        //this.$message.error('操作失败！一个产品至少需有一道工序，每道工序下至少需有一项操作！');
      }else{
        process1.sonProcesses.splice(indexProcess2,1);
        console.log(process1.sonProcesses);
        process1.sonProcesses.forEach((pro2Item,indexPro2Item) => {
          pro2Item.orders = indexPro2Item + 1;
        })
        this.process2CrolData.process2DelVisible = false;
      }
      this.tolooksetupData.setOk = '';
      this.collse2 = '';
    },
    process1Ctrol(type, process1,indecProcess1){//process1是工序
      // 工序按钮方法  type :排序 'sort', 删除 del
      if(type === 'sort'){
        //工序排序
        //debugger;
        console.log('执行到这里了么');
        this.statbegn = false;
        let needpr1Arr = this.tolooksetupData.settingsList2;//可以进行改变的数据
        this.process1OrderData.needpr1Arr = needpr1Arr;
        console.log(needpr1Arr);
        let needpr1Arr2 = JSON.parse(JSON.stringify(needpr1Arr));
        needpr1Arr2.splice(indecProcess1,1);//将所选的这条数据清除
        console.log('12.12-8:42',needpr1Arr2);//这里的数据现在是表格中的数据
        this.process1OrderData.orderProcess1ItemIndex = '';
        this.process1OrderData.orderProcess1ItemIndex2 = '';
        this.process1OrderData.process1List = needpr1Arr2;//表格中的数据
        this.process1OrderData.editProcess1 = process1;//表格外的数据
        this.process1OrderData.indexProcess1 = indecProcess1;//表格外的数据的索引值
        console.log('7.17',this.process1OrderData);
        this.mainPage= 'process1Order';
      }else if(type === 'del'){
        //删除工序
        let delg = '';
        let one = '确定后，本工序已录入数据均将消失。';
        let two = '确定删除吗？';
        delg = one + two;
        console.log(delg);
        this.process1CrolData.process1DelVisible = true;
        this.process1CrolData.process1 = process1;
        this.process1CrolData.indexProcess1 = indecProcess1;
        this.process1CrolData.pro1tip = delg;
        this.process1CrolData.type = type;
        console.log(this.process1CrolData);
      }
    },
    selectProcess1OrdOk(){
      //debugger;
      let process1SelectIndex = this.process1OrderData.orderProcess1ItemIndex;//1-排在某项后面
      if(process1SelectIndex === '') {
        this.$message.error('您还没有选择！');
      }else{
        let needpr1Arr = [];
        needpr1Arr = this.tolooksetupData.settingsList2;
        if(process1SelectIndex === 0){
          let indexProcess1 = this.process1OrderData.indexProcess1;
          let editProcess1 = this.process1OrderData.editProcess1;
          needpr1Arr.splice(indexProcess1,1);
          needpr1Arr.unshift(editProcess1);
        }else{
          console.log(this.process1OrderData.indexProcess1);//表格外数据的索引值 初始状态是0
          console.log(needpr1Arr);
          needpr1Arr.splice(this.process1OrderData.indexProcess1,1);
          console.log(needpr1Arr);
          //let pSeleindex = Number(process1SelectIndex) + 1;
          //此时列表中勾选的数据的id获取不到
          let pSeleid = this.process1OrderData.chosid;
          let pSeleindex = '';
          needpr1Arr.forEach((item1,index1)=> {
            if(item1.productProcessId === pSeleid || item1.process === pSeleid){
              pSeleindex = index1;
            }
          })
          pSeleindex = Number(pSeleindex) + 1;
          needpr1Arr.splice(pSeleindex,0,this.process1OrderData.editProcess1);
          //needpr1Arr.splice(process1SelectIndex,0,this.process1OrderData.editProcess1);
          console.log('排序后的数据',needpr1Arr);//needpr1Arr是排序后的数据
        }
        let newpr1Arr = [];
        needpr1Arr.forEach((newOrdProcess1,index) =>{
          newOrdProcess1.orders = index + 1;
          newpr1Arr.push(newOrdProcess1);
        })
        this.tolooksetupData.settingsList2 = newpr1Arr;
        this.mainPage = 'tolooksetup';
      }
      console.log('12.3',this.tolooksetupData.settingsList2.length);
      let liste = this.tolooksetupData.settingsList.length;
      let list2e = this.tolooksetupData.settingsList2.length;
      if(liste === 0){
        list2e = list2e === 0 ? this.collse = 1 : this.collse = 0;
        this.tolooksetupData.setOk = '';
        this.collse2 = '';
      }else{
        this.collse = '';
      }
    },
    Process1DelCancel(){
      this.process1CrolData.process1DelVisible = false;
    },
    Process1DelOk(){
      let process1p = this.process1CrolData.process1;//需要删除的那条工序数据
      let indexProcess1 = this.process1CrolData.indexProcess1;//需要删除的工序数据的索引值
      //console.log('执行到这里了么7.16');
      let tookall = this.tolooksetupData.settingsList2;
      tookall.splice(indexProcess1,1);
      tookall.forEach((proceItem,indexproceItem) => {
        proceItem.orders = indexproceItem + 1;
      })
      this.process1CrolData.process1DelVisible = false;
      //debugger;
      let tookall2 = this.tolooksetupData.settingsList.length;
      if(tookall2 === 0){
        if(tookall.length === 0){this.collse = 1;}
        this.tolooksetupData.setOk = '';
        this.collse2 = '';
      }else{this.collse = '';}
      // if(tookall.length == 1){
      //   this.$message.error('操作失败！一个产品至少需有一道工序，每道工序下至少需有一项操作！');
      // }else{
      //   tookall.splice(indexProcess1,1);
      //   tookall.forEach((proceItem,indexproceItem) => {
      //     proceItem.orders = indexproceItem + 1;
      //   })
      //   this.process1CrolData.process1DelVisible = false;
      // }
    },

    makdsure(){
      // 设置完成 确定
      let params = { product: this.tolooksetupData.editPro.id  }
      let processSJson = []
      let nullNum = 0
      this.tolooksetupData.settingsList2.forEach((set2Item,indexSet2) => {
        let nullNumItem = 0;
        debugger;
        let processItem = {
          processId:set2Item.process,
          order: set2Item.orders,
          sonProcessHaving: set2Item.havSonProcess === 1 ? 1 : 0,
          sonProcess:[],
          operate:[]
        }
        if(set2Item.clkset === 1){//1-这条工序数据是旧数据，不需要根据首页的状态进行判断
          if(set2Item.isLeaf === true){//该工序可以设置子工序或操作
            set2Item.operateList = set2Item.operateList;
            set2Item.sonProcesses = set2Item.sonProcesses;
            set2Item.operateList.forEach( operate1 => {
              processItem.operate.push({
                operateId : operate1.activity ,
                operateOrders : operate1.orders,
                isLeaf: operate1.isLeaf
              })
            })
            set2Item.sonProcesses.forEach( pross2Item => {
              if(pross2Item.isLeaf === false){
                //走到这里有满足‘是子工序但现在不能设置操作数据的
                let pross2 = {
                  sonProcessId :  pross2Item.activity ,
                  sonProcessOrders: pross2Item.orders ,
                  isLeaf: pross2Item.isLeaf
                };
                processItem.sonProcess.push(pross2);
              }else if(pross2Item.isLeaf === true){
                let pross2 = {
                  sonProcessId :  pross2Item.activity ,
                  sonProcessOrders: pross2Item.orders ,
                  sonOperate: [],
                  isLeaf: pross2Item.isLeaf
                }
                pross2Item.sonOperateList.forEach(oprate2Item => {
                  pross2.sonOperate.push({
                    sonOperateId : oprate2Item.activity ,
                    sonOperateOrders : oprate2Item.orders ,
                  })
                })
                processItem.sonProcess.push(pross2);
              }
            })
          }else if(set2Item.isLeaf === false){}
        }else if(set2Item.clkset === 2){//2-这条工序数据是新数据，需要根据首页的状态进行判断
          //从这里就需要判断循环中的每条工序是否被设置为‘不可设置子工序或操作’。
          if(this.configu === false) {//首页状态为‘不允许设置子工序’
            set2Item.operateList = set2Item.operateList;
            set2Item.operateList.forEach( operate1 => {
              processItem.operate.push({
                operateId : operate1.activity ,
                operateOrders : operate1.orders,
                isLeaf: operate1.isLeaf
              })
            })
          }else if(this.configu === true){
            if(set2Item.isLeaf === true){//代表该工序数据下可以设置子工序或操作
              set2Item.operateList = set2Item.operateList;
              set2Item.sonProcesses = set2Item.sonProcesses;
              set2Item.operateList.forEach( operate1 => {
                processItem.operate.push({
                  operateId : operate1.activity ,
                  operateOrders : operate1.orders,
                  isLeaf: operate1.isLeaf
                })
              })
              set2Item.sonProcesses.forEach( pross2Item => {
                if(pross2Item.isLeaf === false){
                  //走到这里有满足‘是子工序但现在不能设置操作数据的
                  let pross2 = {
                    sonProcessId :  pross2Item.activity ,
                    sonProcessOrders: pross2Item.orders ,
                    isLeaf: pross2Item.isLeaf
                  };
                  processItem.sonProcess.push(pross2);
                }else if(pross2Item.isLeaf === true){
                  let pross2 = {
                    sonProcessId :  pross2Item.activity ,
                    sonProcessOrders: pross2Item.orders ,
                    sonOperate: [],
                    isLeaf: pross2Item.isLeaf
                  }
                  pross2Item.sonOperateList.forEach(oprate2Item => {
                    pross2.sonOperate.push({
                      sonOperateId : oprate2Item.activity ,
                      sonOperateOrders : oprate2Item.orders ,
                    })
                  })
                  processItem.sonProcess.push(pross2);
                }
              })
            }else if(set2Item.isLeaf === false){}
          }
        }
        console.log('processSJson=', processSJson);
        if(processItem.sonProcessHaving === 1) { // 有子工序
          if(processItem.sonProcess.length > 0){
            processItem.sonProcess.forEach(sonProcessItem=>{
              if(set2Item.clkset === 1){//1-这条工序数据是旧数据
                if(sonProcessItem.isLeaf === true){
                  if(sonProcessItem.sonOperate && sonProcessItem.sonOperate.length > 0){}else{nullNumItem++}
                }else if(sonProcessItem.isLeaf === false){}
              }else if(set2Item.clkset === 2){//2-这条工序数据是新数据
                if(this.configu === false){
                  if(sonProcessItem.isLeaf === true){
                    if(sonProcessItem.sonOperate && sonProcessItem.sonOperate.length > 0){}else{nullNumItem++}
                  }else if(sonProcessItem.isLeaf === false){}
                }else if(this.configu === true){
                  if(sonProcessItem.isLeaf === true){
                    if(sonProcessItem.sonOperate && sonProcessItem.sonOperate.length > 0){}else{nullNumItem++}
                  }else if(sonProcessItem.isLeaf === false){}
                }
              }
            })
          }else{
            if(set2Item.clkset === 1){}else if(set2Item.clkset === 2){
              if(this.configu === false){}else if(this.configu === true){nullNumItem++;}
            }
          }
        }else{//没有子工序
          if(processItem.operate.length > 0){
            // 工序下面 至少一个操作
          }else{
            if(set2Item.clkset === 1){//1-旧数据
              if(set2Item.isLeaf === true){nullNumItem++;}else if(set2Item.isLeaf === false){}
            }else if(set2Item.clkset === 2){//2-新数据
              if(this.configu === false) {//首页状态为‘不允许设置子工序’
                if(set2Item.isLeaf === true){nullNumItem++;}else if(set2Item.isLeaf === false){}
              }else if(this.configu === true) {//首页状态为‘允许设置子工序’
                if(set2Item.isLeaf === true) {//该工序可以设置操作数据
                  nullNumItem++;
                }else if(set2Item.isLeaf === false){
                  //该工序选择的不设置子工序或操作
                }
              }
            }
          }
        }


        // if(this.configu === false){//首页状态为‘不允许设置子工序’
        // }else if(this.configu === true){
        //   if(set2Item.isLeaf === true){//代表该工序数据下可以设置子工序或操作
        //     set2Item.operateList = set2Item.operateList || [];
        //     set2Item.sonProcesses = set2Item.sonProcesses || [];
        //     set2Item.operateList.forEach( operate1 => {
        //       processItem.operate.push({
        //         operateId : operate1.activity ,
        //         operateOrders : operate1.orders,
        //         isLeaf: operate1.isLeaf
        //       })
        //     })
        //     set2Item.sonProcesses.forEach( pross2Item => {
        //       if(pross2Item.isLeaf === false){
        //         //走到这里有满足‘是子工序但现在不能设置操作数据的
        //         let pross2 = {
        //           sonProcessId :  pross2Item.activity ,
        //           sonProcessOrders: pross2Item.orders ,
        //           isLeaf: pross2Item.isLeaf
        //         };
        //         processItem.sonProcess.push(pross2);
        //       }else if(pross2Item.isLeaf === true){
        //         let pross2 = {
        //           sonProcessId :  pross2Item.activity ,
        //           sonProcessOrders: pross2Item.orders ,
        //           sonOperate: [],
        //           isLeaf: pross2Item.isLeaf
        //         }
        //         pross2Item.sonOperateList.forEach(oprate2Item => {
        //           pross2.sonOperate.push({
        //             sonOperateId : oprate2Item.activity ,
        //             sonOperateOrders : oprate2Item.orders ,
        //           })
        //         })
        //         processItem.sonProcess.push(pross2);
        //       }
        //     })
        //   }else if(set2Item.isLeaf === false){}
        // }
        // if(processItem.sonProcessHaving === 1){ // 有子工序
        //   if(processItem.sonProcess.length > 0){//此时有个情况：首页设置为允许设置子工序或操作，该工序也可以设置子工序或操作，但是子工序数据的长度为0
        //     //本来这总情况下该提示没有填写完整的，但很怪的是，就有数据满足上面提到的几点，但它又不该提示
        //     processItem.sonProcess.forEach(sonProcessItem=>{
        //       if(this.configu === false){
        //         if(sonProcessItem.isLeaf === true){
        //           if(sonProcessItem.sonOperate && sonProcessItem.sonOperate.length > 0){}else{nullNumItem++}
        //         }else if(sonProcessItem.isLeaf === false){}
        //       }else if(this.configu === true){
        //         if(sonProcessItem.isLeaf === true){
        //           if(sonProcessItem.sonOperate && sonProcessItem.sonOperate.length > 0){}else{nullNumItem++}
        //         }else if(sonProcessItem.isLeaf === false){}
        //       }
        //       // if(sonProcessItem.isLeaf === false){}else if(sonProcessItem.isLeaf === true){
        //       //   if(sonProcessItem.sonOperate && sonProcessItem.sonOperate.length > 0){
        //       //     //这里需要区分该子工序是否是可以设置操作的，因为有的子工序在创建时就设为了不可设置操作
        //       //     // 子工序下面至少一个操作
        //       //   }else{
        //       //     nullNumItem++
        //       //   }
        //       // }
        //     })
        //   }else{//此时相当于是工序可以设置子工序，但并未设置
        //     if(this.configu === false){}else if(this.configu === true){nullNumItem++;}
        //   }
        // }else{ // 没有子工序
        //   if(processItem.operate.length > 0){
        //     // 工序下面 至少一个操作
        //   }else{
        //     debugger;
        //     if(this.configu === false){//首页状态为‘不允许设置子工序’
        //       if(set2Item.isLeaf === true){}else if(set2Item.isLeaf === false){}
        //     }else if(this.configu === true){//首页状态为‘允许设置子工序’
        //       if(set2Item.isLeaf === true){//该工序可以设置操作数据
        //         nullNumItem++;
        //       }else if(set2Item.isLeaf === false){
        //         //该工序选择的不设置子工序或操作
        //       }
        //       // nullNumItem++
        //     }
        //   }
        // }
        if(nullNumItem > 0){
          // 这条不满足 保存要求
          nullNum += nullNumItem
          console.log('错误的processItem=', processItem)
        }else {
          processSJson.push(processItem)
        }
      })
      console.log('nullNum=', nullNum);//nullNum是settingsList2的长度。这里需要获取下现有的状态及选择的工序数据的状态。
      //console.log('12.17-9:21',this.tolooksetupData.settingsList2);
      console.log('processSJson=', processSJson)
      if(nullNum >0){
        //this.$message.error('一个产品至少需有一道工序，每道工序下至少需有一项操作或者子工序，且子工序下至少有一项操作！')
        this.mdesurn = true;
        //这里需要根据选择的工序数据的状态判断选择的工序是否还可以继续设置子工序或操作数据，如果可以则给予提示
        //如果不可以则不给予提示。
        //可以判断settingsList2中是否有任意一条数据的isLeaf=true，如果有就给予提示，如果没有就不给予提示。
        // this.tolooksetupData.settingsList2.forEach(itemt => {
        //   if(this.configu === false){//此时不允许设置子工序，而在选项管理设置时设置‘不设置’子工序或操作的数据，此时就不能设置任何子工序或操作了。
        //     if(itemt.isLeaf === true){this.mdesurn = true;return false;}else if(itemt.isLeaf === false){this.mdesurn = false;}
        //   }else if(this.configu === true){//此时允许设置子工序，故此时无论isLeaf是true还是false，只有该工序数据能不能设置子工序的区别，即便isLeaf=
        //     //false,该工序也可以设置工序下的操作，就也需要给予提示。
        //     this.mdesurn = true;
        //   }
        // })
      }else{
        processSJson = JSON.stringify(processSJson);
        params.processSJson = processSJson;
        //return false;
        chooseProcess(params).then(res1 => {
          let res = res1.data
          console.log('chooseProcess = ', res)
          let content = res.data.content
          if(content=== "操作成功"){
            this.$message.success(content)
            this.goensusses(this.oirdiff);
          }else{//下面那行待定是否需要，但感觉应该得有吧，不然页面点击的时候一点反应都没有算怎么回事。
            this.$message.error('操作失败！一个产品至少需有一道工序，每道工序下至少需有一项操作！');
          }
        }).catch(err => {
          console.log('chooseProcess err=', err)
        })
      }
    },
    mainpageSet(pageStr){
      this.mainPage = pageStr ;
      //故为了让排序列表中不会留下上一次选择时选择的缓存，就需要在返回上一页的时候将上一秒选择的取消勾选
      //console.log('12.12-8:55',this.process1OrderData.process1List);
      //是不是只要点击‘返回上一页’无论表格中勾选了几条数据，再次回到表格页面的时候，表格中都不该出现任何勾选呢？
      //毕竟排序不是添加数据，要保持上一次勾选的数据的选中状态，排序点击确定排完就完了。
      this.process1OrderData.orderProcess1ItemIndex2 = '';
      this.process2OrderData.orderProcess1ItemIndex2 = '';
      this.oprateOrderData.orderOprateItemIndex2 = '';
      console.log('13:33',this.tolooksetupData.settingsList2);
      // this.tolooksetupData.settingsList2.forEach(itemt => {
      //   itemt.clkset = 1;
      // })
      if(pageStr === 'homeset'){//此时是需要回到首页
        this.getprcsetting();
      }
    },
    toggleSetOk(){
      //下面这一行就是实现一个选项按钮的可以任意勾选和取消勾选的切换功能
      this.tolooksetupData.setOk = !this.tolooksetupData.setOk;
      //this.tolooksetupData.setOk: true or false
      this.tolooksetupData.setOk ? this.collse2 = 0 : this.collse2 = '';
    },
    choneprod(pic,picindex){
      //this.clkset = 2;
      //debugger;
      console.log('2025-3.3',this.tolooksetupData.settingsList2);
      // this.tolooksetupData.settingsList2.forEach(tost => {
      //   tost.clkset = 1;
      // })
      //想要在‘添加工序’按钮点击的时候判断是第一次设置还是第二次及以后设置
      // console.log('12.16-14:33',this.tolooksetupData.settingsList2.length);
      // console.log('12.16-16:13',this.tolooksetupData.settingsList2);
      // console.log('12.16-16:08',this.chosesetenData.processList);
      // this.chosesetenData.processList.forEach(itemc => {
      //   if(this.tolooksetupData.settingsList2.length === 0){//代表此时该产品是第一次设置工序数据
      //     itemc.checkick = true;
      //   }else{
      //     this.tolooksetupData.settingsList2.forEach(itemt => {
      //       let tid = itemt.process;
      //       let choid = itemc.id;
      //       if(tid === choid){itemc.checkick = false;}else{itemc.checkick = true;}
      //     })
      //   }
      // })
      this.mainPage = 'choseseten';
      if(pic === 'bck'){//在数组后面增加数据
        this.teppt1 = 1;
      }else if(pic === 'bef'){//在按钮所在这条数据上方增加新数据
        this.teppt1 = 2;this.tepindex1 = picindex;
      }
      //this.chosesetenData.selectCat =  []
      this.chosesetenData.selectProcess =  [];
      this.chosesetenData.cantSelectProcess =  this.tolooksetupData.settingsList2;
      let params = { type:1, enabled:1 }
      getCategoryList(params).then(res1 => {
        let res = res1.data
        this.chosesetenData.catList = res.data.ppmActivityCategories;
        let jsonall = {
          enabled: true,
          id: 0,
          name: "全部类别",
          operation: null,
          orders: null,
          previousId: null,
          type: 1
        };
        this.chosesetenData.catList.unshift(jsonall);
        console.log('4.24-1',this.chosesetenData.catList);
        //console.log('2024.11.19this.setten现在的值',this.setten);
        //console.log('2024.11.19为何会有有的数据没有选中按钮的情况呢？明明都在一个数组中',this.chosesetenData.processList);
        console.log('2024.12.12-10:56',this.chosesetenData.processList);//如何判断表格中是否已有选择的选项呢？
        //debugger;
        if(this.chosesetenData.selectProcess.length === 0){this.choseover = 0;}else{this.choseover = 1;}
        // this.chosesetenData.processList.forEach(itemc => {
        //   if(!itemc.havSonProcess){this.choseover = 0;}else{this.choseover = 1;}
        // })
        //console.log('11.25-如果这个是没有值的，则就是第一次设置这个产品数据，否则则是已经设置过工序相关数据的',this.tolooksetupData.settingsList2);
        // for(let a in this.chosesetenData.processList) {
        //   //console.log('11.25-怎么感觉实现的效果跟预期的就是不同呢', this.chosesetenData.processList[a]);
        //   if (this.chosesetenData.processList[a].isLeaf === true) {//勾选了设置子工序或操作
        //     //如果它是指该工序下可以设置子工序，且已经设置了子工序的话，那就是
        //     this.chosesetenData.processList[a].haschild = 0;
        //     //this.chidchis = 1;
        //     //this.chosesetenData.processList[a].haschild = 0;
        //   } else if (this.chosesetenData.processList[a].isLeaf === false || this.chosesetenData.processList[a].isLeaf === null) {//勾选了不设置子工序或
        //     //操作，或原本就是旧数据
        //     console.log('为何没有跳入这里?');
        //     this.chosesetenData.processList[a].haschild = 2;
        //     //this.chidchis = 2;
        //   }
        // }
        // if (this.setten === 1) {//现在是允许工序下设置子工序，则此时才会展示‘本工序下是否有子工序’这一列
        // }else if (this.setten === 2) {//而此时因为是设置为不允许工序下设置子工序，故此时不展示‘本工序下是否有子工序’那一列
        // }
        //因为现在不清楚调用接口后到底是怎么判断值的，所以先在这里写一下可以设置子工序时的情况：
        //看原型上是在选项设置中设置该工序下是否设置子工序或操作时，因为并不是必选项，所以可能
        //会不选中‘设置’或是‘不设置’
        //假设用于判断是否勾选了‘是否设置子工序或操作’的选项的字段是choonse
        //  let choonse = 1;
        //  console.log('1',choonse);
        //  if(choonse === 0){this.chidchis = 0;}else if(choonse === 1) {
        //    this.chidchis = 1;
        //    for(let a in this.chosesetenData.processList){
        //      this.chosesetenData.processList[a].haschild = 0;
        //    }
        //  }
      }).catch(err => {
        console.log('getCategoryList err=', err)
      })
      this.getProcessListFun(this.chosesetenData.selectCat, this.chosesetenData.cantSelectProcess,pic);
    },
    changeCat(){
      this.getProcessListFun(this.chosesetenData.selectCat, this.chosesetenData.cantSelectProcess)
    },

    getProcessListFun(catID, cantSelectProcessList,pic){
      //debugger;
      let productID = this.tolooksetupData.editPro.id
      let data = { product: productID }
      if(catID){
        data.categoryId = catID
      }
      getProcessList(data).then(res1 => {
        let res = res1.data
        let showProcessList =  []
        let ppmProcessList = res.data.ppmProcessList
        let selectList = this.chosesetenData.selectProcess
        ppmProcessList.forEach(processItem => {
          let hasSame = false
          cantSelectProcessList.forEach( cantProcess => {
            if(processItem.id === cantProcess.process){
              hasSame = true
            }
          })
          selectList.forEach(seleProcess => {
            if(processItem.id === seleProcess.id){
              processItem.havSonProcess = seleProcess.havSonProcess
              hasSame = true;
            }
          })
          if(hasSame === true){processItem.isSelect = '1';}else{processItem.isSelect = '';}
          showProcessList.push(processItem)
          // if(!hasSame){
          //   processItem.isSelect = ''
          //   processItem.havSonProcess = ''
          //   selectList.forEach(seleItem => {
          //     if(seleItem.id == processItem.id){
          //       processItem.isSelect = '1'
          //       processItem.havSonProcess = seleItem.havSonProcess
          //     }
          //   })
          //   showProcessList.push(processItem)
          // }
        })
        console.log('展示的列表=11.18', showProcessList);
        for(let s in showProcessList){
          if(showProcessList[s].isLeaf === true){
            //有子工序
            console.log('11.25',this.tolooksetupData.settingsList2);
            if(this.tolooksetupData.settingsList2.length === 0){
              showProcessList[s].haschild = 1;
            }else{
              for(let t in this.tolooksetupData.settingsList2){
                let itemid = this.tolooksetupData.settingsList2[t].process;
                if(showProcessList[s].id === itemid){//1-1,1-2
                  showProcessList[s].haschild = 1;
                  if(this.tolooksetupData.settingsList2[t].havSonProcess === 1){showProcessList[s].havSonProcess = 1;}
                  else if(this.tolooksetupData.settingsList2[t].havSonProcess === 2){showProcessList[s].havSonProcess = 2;}
                  break;
                }else{showProcessList[s].haschild = 1;}
              }
              // this.tolooksetupData.settingsList2.forEach((item1,index1) => {
              //   //这个位置似乎不能通过子工序的数量来判断
              //   //其实也可以通过id判断哪一条数据需要选中
              //   //havSonProcess代表是否选中了‘有’和‘没有’选项
              //   let itemid = item1.process;//工序Id
              //   if(showProcessList[s].id === itemid){
              //    showProcessList[s].haschild = 1;
              //    if(item1.havSonProcess === 1){showProcessList[s].havSonProcess = 1;}else if(item1.havSonProcess === 2){
              //      showProcessList[s].havSonProcess = 2;
              //    }
              //     // if(item1.sonProcesses.length === 1){
              //     //   showProcessList[s].haschild = 1;
              //     //   showProcessList[s].havSonProcess = 1;
              //     // }else{
              //     //   showProcessList[s].haschild = 0;
              //     // }
              //   }else{
              //     showProcessList[s].haschild = 0;
              //   }
              //   // if(item1.sonProcesses.length === 1){
              //   //   showProcessList[s].haschild = 1;
              //   //   showProcessList[s].havSonProcess = 1;
              //   // }else{
              //   //   showProcessList[s].haschild = 0;
              //   // }
              // })
            }
            // if(showProcessList[s].sonProcesses.length === 1){
            //   showProcessList[s].haschild = 1;
            //   showProcessList[s].havSonProcess = 1;
            // }else{
            //   showProcessList[s].haschild = 0;
            // }
          }else if(showProcessList[s].isLeaf === false || showProcessList[s].isLeaf === null){
            //没有子工序
            showProcessList[s].haschild = 2;
            //showProcessList[s].haschild = 0;
          }
          //showProcessList[s].timing = showProcessList[s].timing === null ? "——" : showProcessList[s].timing;
          if(showProcessList[s].timing === null){showProcessList[s].timing = "";}
          else if(showProcessList[s].timing === 1){showProcessList[s].timing = "正式操作之前的准备工作"+"/";}
          else if(showProcessList[s].timing === 2){showProcessList[s].timing = "正式操作"+"/";}
          else if(showProcessList[s].timing === 3){showProcessList[s].timing = "正式操作之后的收尾工作"+"/";}
          if(showProcessList[s].kind === null){showProcessList[s].kind = "";}
          else if(showProcessList[s].kind === 1){showProcessList[s].kind = "主要操作"+"/";}
          else if(showProcessList[s].kind === 2){showProcessList[s].kind = "辅助操作"+"/";}
          // showProcessList[s].periodicity = showProcessList[s].periodicity === null  || undefined ? "——" : showProcessList[s].periodicity;
        }
        this.chosesetenData.processList = showProcessList;
        console.log('12.17-16:04-this.chosesetenData.processList=',this.chosesetenData.processList);
        console.log('12.16-14:33',this.tolooksetupData.settingsList2.length);
        console.log('12.16-16:13',this.tolooksetupData.settingsList2);
        // let a = [{name: 1},{name: 2},{name: 3}];
        // let b = [{namer: 1},{namer: 2}];
        // for(let i = 0;i<a.length;i++){
        //   let namea = a[i].name;
        //   for(let j = 0; j<b.length;j++){
        //     let nameb = b[j].namer;
        //     if(namea === nameb){a[i].ked = 1;break;}else{a[i].ked = 2;}
        //   }
        // }
        // console.log('a=',a);
        // showProcessList.forEach(items => {
        //   if(this.tolooksetupData.settingsList2.length === 0) {//代表此时该产品是第一次设置工序数
        //     items.checkick = true;
        //   }else{
        //     this.tolooksetupData.settingsList2.forEach(itemt => {
        //       let tid = itemt.process;
        //       let choid = items.id;
        //       if(tid === choid){items.checkick = false;}else{items.checkick = true;}
        //     })
        //   }
        // })
        for(let s = 0; s<showProcessList.length;s++){
          let sid = showProcessList[s].id;
          // if(this.chosesetenData.selectProcess.length === 0){showProcessList[s].checkick = true;}else{
          //   for(let t = 0;t<this.chosesetenData.selectProcess.length;t++){
          //     let tid = this.chosesetenData.selectProcess[t].id;
          //     if(tid === sid){showProcessList[s].checkick = false;break;}else{showProcessList[s].checkick = true;}
          //   }
          // }
          if(this.tolooksetupData.settingsList2.length === 0){showProcessList[s].checkick = true;}else{
            for(let t = 0;t<this.tolooksetupData.settingsList2.length;t++){
              let tid = this.tolooksetupData.settingsList2[t].process;
              if(tid === sid){showProcessList[s].checkick = false;break;}else{showProcessList[s].checkick = true;}
            }
          }
        }
        console.log('12.16-16:40',showProcessList);
        //debugger;
        this.chosesetenData.processList = showProcessList;
        if(this.chosesetenData.selectProcess.length === 0){this.choseover = 0;}else{
          for(let o in this.chosesetenData.selectProcess){
            if(this.chosesetenData.selectProcess[o].havSonProcess === '' ||
                this.chosesetenData.selectProcess[o].havSonProcess === undefined){
              if(this.configu === true){
                if(this.chosesetenData.selectProcess[o].isLeaf === false){
                  this.choseover = 1;
                }else if(this.chosesetenData.selectProcess[o].isLeaf === true){
                  this.choseover = 0;break;
                }
              }else if(this.configu === false){this.choseover = 1;}
            }else{this.choseover = 1;}
          }
        }
        // if(this.chidchis === 1){
        //   for(let s in showProcessList){
        //     showProcessList[s].haschild = 0;
        //   }
        //   this.chosesetenData.processList = showProcessList;
        // }
      }).catch(err => {
        console.log('getProcessList err=', err)
      })
    },

    changeHavSon(processItem){
      // console.log()
      this.chosesetenData.selectProcess.forEach(seleItem => {
        if(seleItem.id === processItem.id){
          seleItem.havSonProcess = processItem.havSonProcess
        }
      })
    },
    getsetunup(productItem){
      //debugger;
      //this.clkset = 1;//代表此时是点击了‘设置’跳转的
      let undent = productItem
      //content用于判断是产品还是零组件
      this.mainPage = 'tolooksetup'
      this.tolooksetupData.editPro = undent
      var unid = undent.id; // 产品id
      let  data = {  product:unid }
      getSettingsList(data).then(res1 => {
        let res = res1.data
        let list = res.data.settingsList;
        let configurable = res.data.configurable;//首页的状态，用于在后面调用
        this.configu = configurable;
        console.log('12.2.1-this.configu有值么',this.configu);
        list.forEach(item =>{
          if(item.sonProcesses && item.sonProcesses.length > 0){
            item.havSonProcess = 1;//这里已经代表该工序下已设置了子工序了
          }else {
            item.havSonProcess = 2
          }
        })
        this.tolooksetupData.settingsList = JSON.parse(JSON.stringify(list)) ;
        this.tolooksetupData.settingsList2 = JSON.parse(JSON.stringify(list));
        console.log('2025-3-3-现在的值',this.tolooksetupData.settingsList2);
        //debugger;
        this.tolooksetupData.settingsList2.forEach(tost => {
          tost.clkset = 1;
          let sonProcesses = tost.sonProcesses;
          let operateList = tost.operateList;
          if(sonProcesses === null){}else{
            sonProcesses.forEach(ites => {ites.clkset = 1;})
          }
          if(operateList === null){}else{
            operateList.forEach(ito => {ito.clkset = 1;})
          }
        })
        if(this.tolooksetupData.settingsList.length === 0){//[]
          this.collse = 1;
        }else{//settingsList.length > 0
          this.collse = '';
        }
        // if(this.tolooksetupData.settingsList2.length === 0){
        //   this.collse = 1;
        // }else{
        //   this.collse = 0;
        // }
        console.log('2024.11.21isLeaf的值到底是多少？',this.tolooksetupData.settingsList2);
        //好怪，第一次设置的时候就有isLeaf这个属性值，第二次设置的时候就没有这个属性值了，难道是点击
        //确定的时候没传的问题？
        // this.tolooksetupData.settingsList2.forEach((item1,index1) => {
        //   let sonProcesses = item1.sonProcesses;//代表子工序数据
        //   //就是可以理解为现在的configurable代表首页’状态管理‘设置的是否允许设置子工序，故当configurable=false时，就是不允许设置子工序及操作
        //   //疑问1：产品下已经设置了子工序数据，相当于该工序肯定能继续设置子工序及操作么？无关状态管理设法设置为不允许设置工序？这种情况是老数据，先不管。
        //   //现在的情况是：状态管理设置为可以设置子工序，但是某一个工序选项管理设置的是不设置子工序或操作，所以这条工序在选择的时候不能设置
        //   //子工序或操作；那么疑问2：状态管理设置为不可以设置子工序，按照逻辑工序列表中不能展示设置子工序那一列，但是工序列表中某一个工序
        //   //在选项管理那里设置的是可以设置子工序或操作，那么此时该工序可以设置子工序或操作么》这种情况时该工序不可设置子工序，但必须设置操作
        //   //configurable = true只是代表现在的状态是允许设置子工序，
        //   //if(sonProcesses){if(sonProcesses.length > 0){item1.isLeaf = true;}else{item1.isLeaf = false;}}else{item1.isLeaf = false;}
        //   // let operateList = item1.operateList;//代表操作数据
        //   // if(item1.isLeaf === false){}else if(item1.isLeaf === true){
        //   //
        //   // }
        //   if(sonProcesses === null){item1.isLeaf = false;}else{
        //     if(sonProcesses.length!== 0){item1.isLeaf = true;}else{item1.isLeaf = false;}
        //   }
        // })
        if(this.tolooksetupData.settingsList2.length === 0){}else{
          for(let  t in this.tolooksetupData.settingsList2){
            let processt = this.tolooksetupData.settingsList2[t];
            if(processt.timing === null){processt.timing = "";}
            else if(processt.timing === 1){processt.timing = "正式操作之前的准备工作"+"/";}
            else if(processt.timing === 2){processt.timing = "正式操作"+"/";}
            else if(processt.timing === 3){processt.timing = "正式操作之后的收尾工作"+"/";}
            if(processt.kind === null){processt.kind = "";}
            else if(processt.kind === 1){processt.kind = "主要操作"+"/";}
            else if(processt.kind === 2){processt.kind = "辅助操作"+"/";}
            let operateList = processt.operateList;
            let sonProcesses = processt.sonProcesses;
            if(sonProcesses.length === 0){}else{
              for(let s in sonProcesses){
                let sont = sonProcesses[s];
                if(sont.timing === null){sont.timing = "";}
                else if(sont.timing === 1){sont.timing = "正式操作之前的准备工作"+"";}
                else if(sont.timing === 2){sont.timing = "正式操作"+"";}
                else if(sont.timing === 3){sont.timing = "正式操作之后的收尾工作"+"";}
                if(sont.kind === null){sont.kind = "";}
                else if(sont.kind === 1){sont.kind = "主要操作"+"/";}
                else if(sont.kind === 2){sont.kind = "辅助操作"+"/";}
                let sonOperateList = sont.sonOperateList;
                if(sonOperateList.length === 0){}else{
                  for(let sn in sonOperateList){
                    let snst = sonOperateList[sn];
                    if(snst.timing === null){snst.timing = "";}
                    else if(snst.timing === 1){snst.timing = "正式操作之前的准备工作"+"/";}
                    else if(snst.timing === 2){snst.timing = "正式操作"+"/";}
                    else if(snst.timing === 3){snst.timing = "正式操作之后的收尾工作"+"/";}
                    if(snst.kind === null){snst.kind = "";}
                    else if(snst.kind === 1){snst.kind = "主要操作"+"/";}
                    else if(snst.kind === 2){snst.kind ="辅助操作"+"/";}
                  }
                }
              }
            }
            if(operateList.length === 0){}else{
              for(let o in operateList){
                let opst = operateList[o];
                if(opst.timing === null){opst.timing = "";}
                else if(opst.timing === 1){opst.timing = "正式操作之前的准备工作"+"/";}
                else if(opst.timing === 2){opst.timing = "正式操作"+"/";}
                else if(opst.timing === 3){opst.timing ="正式操作之后的收尾工作"+"/";}
                if(opst.kind === null){opst.kind = "";}
                else if(opst.kind === 1){opst.kind = "主要操作"+"/";}
                else if(opst.kind === 2){opst.kind = "辅助操作"+"/";}
              }
            }
          }
        }

      }).catch(err => {
        console.log('getSettingsList err=', err)
      })
    },
    goensusses(ordiff,obje){
      this.mainPage = 'tomanage'
      this.oirdiff = ordiff;
      if(ordiff === 1){//产品
        let key = 1;
        this.ordiffen = key;
        let params = {  type:key, currentPage:1,pageSize: 10};
        getToManageList(params).then(res1=>{
          let res = res1.data
          this.tomanageData = res.data.manageList;
          this.tomanageData.curPage = res.data.pageInfo.currentPageNo;
          this.tomanageData.pageSize = res.data.pageInfo.pageSize;
          this.tomanageData.totalPage = res.data.pageInfo.totalPage;
          this.pageInfo2d = res.data.pageInfo;
        }).catch(err=>{
          console.log('getToManageList err=', err)
        })
      }else if(ordiff === 2){//零组件
        let key = 2;
        this.ordiffen = key;
        let params = {  type:key, currentPage: 1,pageSize: 20 };
        getToManageList(params).then(res1=>{
          let res = res1.data;
          this.tomanageData = res.data.manageList;
          this.tomanageData.curPage = res.data.pageInfo.currentPageNo;
          this.tomanageData.pageSize = res.data.pageInfo.pageSize;
          this.tomanageData.totalPage = res.data.pageInfo.totalPage;
          this.pageInfo2d = res.data.pageInfo;
        }).catch(err=>{
          console.log('getToManageList err=', err)
        })
      }
    },
    mountedFun(){//这个函数是在刷新时或是点击左侧菜单时触发的
      this.getprcsetting()
    },
    getprcsetting(){
      getProcessSettingsList().then(res => {
        let data = res.data
        var lants = data.data.ppmProcessStatList;
        for(let i in lants){
          if(lants[i].type == 1){
            if(lants[i].assembly == 1){
              this.homeSetData.yses1Data = lants[i].tbcAmount
              this.homeSetData.edsess1Data = lants[i].abdAmount
            }else if(lants[i].assembly == 2){
              this.homeSetData.yses2Data = lants[i].tbcAmount
              this.homeSetData.edsess2Data = lants[i].abdAmount
            }
          }else if(lants[i].type == 2){
            if(lants[i].assembly == 1){
              this.homeSetData.yses3Data = lants[i].tbcAmount
              this.homeSetData.edsess3Data = lants[i].abdAmount
            }else if(lants[i].assembly == 2){
              this.homeSetData.yses4Data = lants[i].tbcAmount
              this.homeSetData.edsess4Data = lants[i].abdAmount
            }
          }
          if(lants[i].configurable === true){
            //有些工序配置有子工序
            this.setten = 1;
          }else if(lants[i].configurable === false){
            //所有工序都不配置子工序
            this.setten = 2;
          }
        }
      }).catch(err => {
        console.log('getProcessSettingsList err=', err)
      })
    },
    pageClick(pageInfo){
      // pageItem 参考值如下：
      // pageItem = { 'name': '尾页', 'page': 30 , 'klass':'ye', 'type':'last' }
      // pageItem = { 'name': 1, 'page': 3 , 'klass':'yecur', 'type':'curPage' }
      // pageItem = { 'name': 2, 'page': 4 , 'klass':'ye', 'type':'normal' }
      // pageItem = { 'name': '首页', 'page':1, 'klass':'ye', 'type':'first' }
      let data = {
        type: this.ordiffen,
        pageSize: 10,
        currentPageNo: pageInfo.page
      }
      this.getList(data);

    },
    getList(data){
      // let params = { 'currentPageNo': 1 , 'pageSize': 20 , 'totalPage': 15  }
      //this.getDemoListFun(params)
      getToManageList(data).then(res1 => {
        let res = res1.data
        this.tomanageData = res.data.manageList;
        this.tomanageData.curPage = res.data.pageInfo.currentPageNo;
        this.tomanageData.pageSize = res.data.pageInfo.pageSize;
        this.tomanageData.totalPage = res.data.pageInfo.totalPage;
        this.pageInfo2d = res.data.pageInfo;
      }).catch(err=>{
        console.log('getToManageList err=', err)
      })
    },
    getDemoListFun(params){
      getDemoList(params).then(res => {
        res = [
          { name:'张三1', num: 10, fullName: '我就是张三11'  } ,
          { name:'张三2', num: 10, fullName: '我就是张三22'  } ,
          { name:'张三3', num: 10, fullName: '我就是张三33'  } ,
          { name:'张三4', num: 10, fullName: '我就是张三444'  } ,
          { name:'张三5', num: 10, fullName: '我就是张三555'  }
        ]
        this.list = res
        this.pageInfo = { 'currentPageNo': params.currentPageNo , 'pageSize': 20 , 'totalPage': 15  }
      }).catch(err => {
        console.log('err=', err)
      })
    },
    delBtn(item){
      this.editObj = item
      this.delVisible = true
    },
    hideFun(){
      this.delVisible = false
    },
    tipOk(){
      this.delVisible = false
    },
    stopusen(type,process1,indexProcess1,process1Id){
      //检验方法type：停用'sto',启用'str'
      //debugger;
      if(type === 'sto'){//停用
        let stonp = '确定停用此标签吗？';
        this.process1StoData.pro1top = stonp;
      }else if(type === 'str'){
        let stronp = '确定恢复使用此标签吗？';
        this.process1StoData.pro1top = stronp;
      }
      this.process1StoData.type = type;
      this.process1StoData.editstprocess1 = process1;//需要停用的该条数据
      this.process1StoData.indexstoProcess1 = indexProcess1;//需要停用的数据的索引值
      this.process1StoData.process1StoVisible = true;
      this.process1StoData.editId = process1Id;
    },
    process2stouse(type2,process2,indexProcess2,process2Id){
      if(type2 === 'sto'){//停用
        let stonp2 = '确定停用此标签吗？';
        this.process2StoData.pro2top = stonp2;
      }else if(type2 === 'str'){//启用
        let stonp2 = '确定恢复使用此标签吗？';
        this.process2StoData.pro2top = stonp2;
      }
      this.process2StoData.type = type2;
      this.process2StoData.editstprocess2 = process2;
      this.process2StoData.indexstoProcess2 = indexProcess2;
      this.process2StoData.process2StoVisible = true;
      this.process2StoData.editId2 = process2Id;
    },
    process3stouse(type3,process3,indexProcess3,process3Id){
      if(type3 === 'sto'){
        let stonp3 = '确定停用此标签吗？';
        this.process3StoData.pro3top = stonp3;
      }else if(type3 === 'str'){
        let stonp3 = '确定恢复使用此标签吗？';
        this.process3StoData.pro3top = stonp3;
      }
      this.process3StoData.type = type3;
      this.process3StoData.editstprocess3 = process3;
      this.process3StoData.indexstoProcess3 = indexProcess3;
      this.process3StoData.process3StoVisible = true;
      this.process3StoData.editId3 = process3Id;
    },
    Process1StoCancel(){
      this.process1StoData.process1StoVisible = false;
    },
    Process2StoCancel(){
      this.process2StoData.process2StoVisible = false;
    },
    Process3StoCancel(){
      this.process3StoData.process3StoVisible = false;
    },
    Process1StoOk(){
      //debugger;
      let process1Sto = this.process1StoData.editstprocess1;//需要停用的数据
      let indexStoProcess1 = this.process1StoData.indexstoProcess1;//需要停用的数据的索引值
      let stopId = this.process1StoData.editId;//需要停用的数据的Id
      let typed = this.process1StoData.type;//区分是停用还是启用
      let paramst = { categoryId : stopId,enabled: ''};
      if(typed === 'sto'){
        paramst.enabled = 0;
      }else if(typed === 'str'){
        paramst.enabled = 1;
      }
      catuseory(paramst).then(rest => {
        let content = rest.data.data.content;
        if(content === "操作成功"){
          this.process1StoData.process1StoVisible = false;
          const keed = this.process1StoData.type;
          if(keed === 'sto'){
            this.Cateoryagment(1);
          }else{
            this.stopusecan(1);
          }
        }else{
          if(content === "操作失败！因为此类别正在被使用！"){
            content = "操作失败！因为此标签正在被使用！";
          }
          this.$message.error(content);
        }
      })
    },
    Process2StoOk(){
      let process2Sto = this.process2StoData.editstprocess2;
      let indexStoProcess2 = this.process2StoData.indexstoProcess2;
      let stopId2 = this.process2StoData.editId2;
      let typed2 = this.process2StoData.type;
      let paramst2 = { categoryId: stopId2,enabled: ''};
      if(typed2 === 'sto'){
        paramst2.enabled = 0;
      }else if(typed2 === 'str'){
        paramst2.enabled = 1;
      }
      catuseory(paramst2).then(rest2 => {
        let content2 = rest2.data.data.content;
        if(content2 === "操作成功"){
          this.process2StoData.process2StoVisible = false;
          const keed = this.process2StoData.type;
          if(keed === 'sto'){
            this.process2catment();
          }else{
            this.process2stocat(2);
          }
        }else{
          if(content2 === "操作失败！因为此类别正在被使用！"){
            content2 = "操作失败！因为此标签正在被使用！";
          }
          this.$message.error(content2);
        }
      })
    },
    Process3StoOk(){
      let process3Sto = this.process3StoData.editstprocess3;
      let indexStoProcess2 = this.process3StoData.indexstoProcess3;
      let stopId3 = this.process3StoData.editId3;
      let typed3 = this.process3StoData.type;
      let paramst3 = { categoryId: stopId3,enabled: ''};
      if(typed3 === 'sto'){
        paramst3.enabled = 0;
      }else if(typed3 === 'str'){
        paramst3.enabled = 1;
      }
      catuseory(paramst3).then(rest3 => {
        let content3 = rest3.data.data.content;
        if(content3 === "操作成功"){
          this.process3StoData.process3StoVisible = false;
          const keed3 = this.process3StoData.type;
          if(keed3 === 'sto'){
            this.process3catment();
          }else{
            this.process3stocat(3);
          }
        }else{
          if(content3 === "操作失败！因为此类别正在被使用！"){
            content3 = "操作失败！因为此标签正在被使用！";
          }
          this.$message.error(content3);
        }
      })
    },
    gotenchose(){
      this.mainPage = 'processletion';
      const jsong = {};
      getalloption(jsong).then(res => {
        const lank = res.data.data;
        console.log('lank的值',lank);
        this.havoptionData = lank;
      })
    },
    getCyclic(){
      console.log('Radio value changed to:', this.radio1);
      const keedn = this.radio1;
      if(keedn == 1){
        this.timk1 = true;
        this.timk2 = false;
        this.messgen = false;
        this.addPress3Data.formData3.isCyclicity = 0;
      }else if(keedn == 2){
        this.timk2 = true;
        this.messgen = false;
        this.timk1 = false;
        this.addPress3Data.formData3.isCyclicity = 1;
      }
    },
    setchiat(label){
      console.log('2024.10.31-1',this.addPress2Data.formData2.chidcat);
      if(this.addPress2Data.formData2.chidcat === label){
        this.addPress2Data.formData2.chidcat = '';
        this.addPress2Data.timecs = 0;
      }else{
        this.addPress2Data.formData2.chidcat = label;
        if(this.addPress2Data.formData2.chidcat === 2){
          this.addPress2Data.timecs = 1;
        }else{
          this.addPress2Data.timecs = 0;
        }
      }
    },
    getweekest(){
      this.weekData.visible = true;
      this.weekData.formDataw = { pattern: '',frequency: '',timeUnit: '',executeTimes: '',timechone: '' }
      this.weekData.optionsweek = [
        {id: 1,name: "一定时间内需进行一次或多次"},
        {id: 2,name: "一定次数的操作后需进行一次或数次",disabled: true},
        {id: 3,name: "一个或几个班次后需进行一次或数次",disabled: true}
      ];
      this.weekData.timeUnitweek = [
        { id:1,name:"秒"},
        { id:2,name:"分钟"},
        { id:3,name:"小时"},
        { id:4,name:"天" }
      ];
      this.looken = false;
    },
    weekCancel(){
      this.weekData.visible = false;
    },
    weekOk(){
      this.weekData.visible = false;
      this.addPress3Data.visible = true;
      this.messgen = true;
      if(this.weekData.formDataw.pattern === ""){
        this.$message.error("有必填项未填写完整");
        return false;
      }else{
        if(this.weekData.formDataw.pattern === 1){
          if(this.weekData.formDataw.frequency === "" ||
              this.weekData.formDataw.timeUnit === "" ||
              this.weekData.formDataw.executeTimes === ""){
            this.$message.error("有必填项未填写完整");
            return false;
          }
        }
      }
      console.log('可选的数据',this.weekData.timeUnitweek);
      console.log('选中的数据的索引值',this.weekData.formDataw.timeUnit);
      const lankt = this.weekData.timeUnitweek;
      for(let k in lankt){
        if(lankt[k].id === this.weekData.formDataw.timeUnit){
          this.weekData.formDataw.timechone = lankt[k].name;
        }
      }
      console.log('值呢？',this.addPress3Data.formData3);
    },
    getshowse(ros){
      console.log('Selected value changed to:',ros);
      if(ros === "一定时间内需进行一次或数次" || ros === 1){
        // if(n === 1){this.addPress3Data.fret = 1;this.looken = false;}else{this.looken = true;this.addPress3Data.fret = 0;}
        this.looken = true;
        this.addPress3Data.fret = 1;
      }else{
        // if(n === 1){this.addPress3Data.fret = 1;}else{this.looken = false;this.addPress3Data.fret = 0;}
        this.looken = false;
      }
    },
    sttusetings(){
      this.stvisible = true;
    },
    stvisCancel(){
      this.stvisible = false;
    },
    stvisOk(){
      this.stvisible = false;
      //this.setten = 2;
      upProcesSettings().then(res => {//感觉这个接口调用后下面的写法有待确认
        let data = res.data;
        var lanks = data.data.ppmProcessStatList;
        for(let n in lanks){
          if(lanks[n].type === 1){
            if(lanks[n].assembly === 1){
              this.homeSetData.yses1Data = lanks[n].tbcAmount;
              this.homeSetData.edsess1Data = lanks[n].abdAmount;
            }else if(lanks[n].assembly === 2){
              this.homeSetData.yses2Data = lanks[n].tbcAmount;
              this.homeSetData.edsess2Data = lanks[n].abdAmount;
            }
          }else if(lanks[n].type === 2){
            if(lanks[n].assembly === 1){
              this.homeSetData.yses3Data = lanks[n].tbcAmount;
              this.homeSetData.edsess3Data = lanks[n].abdAmount;
            }else if(lanks[n].assembly === 2){
              this.homeSetData.yses4Data = lanks[n].tbcAmount;
              this.homeSetData.edsess4Data = lanks[n].abdAmount;
            }
          }
          //setten=1时，是允许设置子工序及操作数据；setten=2时是不允许设置子工序，但可以设置操作数据
          if(lanks[n].configurable === true){this.setten = 1;}else if(lanks[n].configurable === false){this.setten = 2;}
          //原来是字段拼写错误………………
        }
      }).catch(err => {
        console.log('upProcessSettings err=',err);
      })
    },
    morecatmig(){
      this.tckming = true;
    },
    mdesurnCancel(){
      this.mdesurn = false;
    },
    // chosetune(){
    //   if(this.process1OrderData.orderProcess1ItemIndex === 0){
    //     console.log('12.12-16:13-进入这里了么');
    //     console.log('1',this.process1OrderData.orderProcess1ItemIndex2);
    //     this.process1OrderData.orderProcess1ItemIndex2 = 0;
    //     console.log('2',this.process1OrderData.orderProcess1ItemIndex2);
    //   }
    // },
    getnetchoe(lab,listr){
      //console.log('2024.12.12-9:26',lab);
      //console.log('2024.12.11-16:28',listr.orderProcess1ItemIndex2);//表格选项框是否被选中
      //console.log('12.19-8:22-this.process1OrderData.process1List=',this.process1OrderData.process1List);
      this.process1OrderData.chosid = lab;
      if(listr.orderProcess1ItemIndex2 === lab){
        listr.orderProcess1ItemIndex2 = '';
      }else{
        listr.orderProcess1ItemIndex2 = lab;
        //if(listr.orderProcess1ItemIndex !== 1){listr.orderProcess1ItemIndex = ""}else{}
        // if(listr.orderProcess1ItemIndex2 === lab){}
      }
      let index1 = listr.orderProcess1ItemIndex;
      let index2 = listr.orderProcess1ItemIndex2;
      //console.log('12.12-9:35',index1);
      //console.log('12.12-9:36',index2);//就是说这个位置就一直获取不到值喽？
      // if(index2){index2 = 1;}
      // if(index1 === 1 && index2 === 1){this.statbegn = true;}
      // else if(index1 === 0){this.statbegn = true;}
      // else{this.statbegn = false;}
      if(index1 === 1 && index2 === lab){this.statbegn = true;}
      else if(index1 === 0){this.statbegn = true;}
      else{this.statbegn = false;}
      if(index2 === lab){
        if(index1 !==1){listr.orderProcess1ItemIndex = '';this.statbegn = false;}
      }
      // if(index1 === 1){//我就说怎么这里就可以呢，感情是index2 = lab = undefined啊，无语了
      //   if(index2 === lab){this.statbegn = true;}else{this.statbegn = false;}
      // }else if(index1 === 0){this.statbegn = true;}
      // else{this.statbegn = false;}
      // this.process1OrderData.orderProcess1ItemIndex2 = index2;
      //console.log('12.23-13:20',this.process1OrderData);
    },
    // changenetSo(listc){
    //   let index1 = listc.orderProcess1ItemIndex;
    //   let index2 = listc.orderProcess1ItemIndex2;
    //   console.log('12.19-8:35',index1);
    //   console.log('12.19-8:36',index2);
    //   if(index1 === 1 && index2 === 1){this.statbegn = true;}
    //   else if(index1 === 0){this.statbegn = true;}
    //   else{this.statbegn = false;}
    // },
    // handleChose(hand){
    //   if(this.process1OrderData.orderProcess1ItemIndex2.includes(hand) && this.process1OrderData.orderProcess1ItemIndex === 0){
    //     this.process1OrderData.orderProcess1ItemIndex = '';
    //   }
    // },
    gethident(lab2,list2){
      //console.log('2024.12.12-9:25',lab2);
      //console.log('2024.12.11-16:38',list2.orderProcess1ItemIndex);
      //console.log('12.23-11:51',list2.orderProcess1ItemIndex2);
      //坏了又把自己给弄晕了，不是，选项选中=lab2，然后就将该值变成空？
      if(list2.orderProcess1ItemIndex === lab2){
        list2.orderProcess1ItemIndex = '';
      }else{
        list2.orderProcess1ItemIndex = lab2;
        if(list2.orderProcess1ItemIndex === 0){list2.orderProcess1ItemIndex2 = '';}else{}
      }
      //console.log('12.23-11:57',list2.orderProcess1ItemIndex2);
      let index1 = list2.orderProcess1ItemIndex;
      //console.log('12.23-13:13',this.process1OrderData.orderProcess1ItemIndex2);
      let index2 = list2.orderProcess1ItemIndex2;//为何这个值在这里就获取不到呢
      //console.log('12.12-9:35',index1);//代表表格外面的选项的值
      //console.log('12.12-9:36',index2);//代表表格中的选项
      if(index1 === 1 && index2){this.statbegn = true;}//下一行这样写出现了一个问题：勾选排序到最前面就需要表格中的取消选中，反之亦然，
      else if(index1 === 0){this.statbegn = true;}
      else{this.statbegn = false;}
      // if(index1 === 1){
      //   if(index2 === lab2){this.statbegn = true;}else{this.statbegn = false;}
      // }else if(index1 === 0){this.statbegn = true;}
      // else{this.statbegn = false;}
      // if(index2){//这样写index2还是获取不到,就先index1后index2能获取到，好怪。
      //   if(index1 === 1){this.statbegn = true;}else if(index1 === 0){this.statbegn = true;list2.orderProcess1ItemIndex2 = '';}
      //   else{this.statbegn = false;}
      // }else{
      //   if(index1 === 1){this.statbegn = false;}else if(index1 === 0){this.statbegn = true;}else{this.statbegn = false;}
      // }
    },
    // condetend(){
    //   console.log('11.26-1',this.process1OrderData.orderProcess1ItemIndex);
    //   console.log('11.26-2',this.process1OrderData.orderProcess1ItemIndex2);
    //   let index1 = this.process1OrderData.orderProcess1ItemIndex;
    //   let index2 = this.process1OrderData.orderProcess1ItemIndex2;
    //   if(index1 === 1 && index2 === 1){this.statbegn = true;}else{
    //     this.statbegn = false;
    //   }
    //   // if(this.process1OrderData.orderProcess1ItemIndex === 0 ||
    //   //     this.process1OrderData.orderProcess1ItemIndex2 === 0){
    //   //   this.statbegn = false;
    //   // }else{
    //   //   this.statbegn = true;
    //   // }
    // },
    // condetend2(){
    //   console.log('11.26-3',this.process2OrderData.orderProcess1ItemIndex);
    //   console.log('11.26-4',this.process2OrderData.orderProcess1ItemIndex2);
    //   let index1 = this.process2OrderData.orderProcess1ItemIndex;
    //   let index2 = this.process2OrderData.orderProcess1ItemIndex2;
    //   if(index1 === 1 && index2 === 1){this.statbegn2 = true;}else{
    //     this.statbegn2 = false;
    //   }
    //   // if(this.process2OrderData.orderProcess1ItemIndex === 0 ||
    //   //   this.process2OrderData.orderProcess1ItemIndex2 === 0){
    //   //   this.statbegn2 = false;
    //   // }else{
    //   //   this.statbegn2 = true;
    //   // }
    // },
    condetend3(){
      console.log('11.26-5',this.oprateOrderData.orderProcess1ItemIndex);
      console.log('11.26-6',this.oprateOrderData.orderProcess1ItemIndex2);
      let index1 = this.oprateOrderData.orderProcess1ItemIndex;
      let index2 = this.oprateOrderData.orderProcess1ItemIndex2;
      if(index1 === 1 && index2 === 1){this.statbegn3 = true;}else{
        this.statbegn3 = false;
      }
      // if(this.oprateOrderData.orderOprateItemIndex === 0 ||
      //   this.oprateOrderData.orderOprateItemIndex2 === 0){
      //   this.statbegn3 = false;
      // }else{
      //   this.statbegn3 = true;
      // }
    }
    // orderClick(c){
    //   if(this.process1OrderData.orderProcess1ItemIndex === c){
    //     this.process1OrderData.orderProcess1ItemIndex = '';
    //   }else{
    //     this.process1OrderData.orderProcess1ItemIndex = c;
    //   }
    // }
  }
}

</script>

<style lang="scss">
$myFontSize: 50px;
$myFontWith: bold;
$myMargn: 20px 0;
//.content{
//  //position: absolute;
//  //top: 0;
//  //left: 0;
//  will-change: transform;
//}
.processSet{
  $myFontSizep: 15px;
  $myHeight:30px;
  padding: $myFontSize;
  line-height: $myHeight;
  font-size: $myFontSizep;

  .tipSize{ $myFontSize: 0.8em; font-size: $myFontSize;}
  .marL30{ $myLeiftSize: 6px; margin-left: $myLeiftSize;}
  //&>div{ max-width: 1000px; min-width:800px;  }


  .homeset{
    //$myWidth: 650px;
    //width: $myWidth;
    margin-left: 170px;width: 59%;margin-top: 40px;
    .panel-box{ $mypadSize: 10px 20px; padding:$mypadSize; }
    .panel-box:nth-child(n+2){ $myBodTop: 1px solid #ccc; border-top: $myBodTop; }
    .ty-alert{ $myClear: both; clear: $myClear; }
    .el-button{ font-weight: $myFontWith; font-size: 0.9em; }



    .panel-box:nth-of-type(n+2){
      border-top: 1px solid #c7c7c7;padding-top: 8px;margin: $myMargn;
    }
    .ty-alert{
      color: #101010;justify-content: space-between;
      width: 100%;
      padding: 8px 0;
      box-sizing: border-box;
      border-radius: 2px;
      position: relative;
      font-size: 13px;
      overflow: hidden;
      opacity: 1;
      display: flex;
      align-items: center;
      transition: opacity .2s;
      margin-bottom: 4px;
      p{
        margin: 0 0 10px;
      }
      .thin-btn{
        color: #0070c0;
        margin-right: 20px;
        font-weight: $myFontWith;
      }
      .thin-btn-hid{
        color:#D9D9D9;margin-right: 20px;font-weight: $myFontWith;
      }
      .wrtdboad{
        margin-left: 121px;
      }
      .wrtdboad2{
        margin-left: 152px;
      }
      .wrtdboad3{
        margin-left: 81px;
      }
      .wrtdboad4{
        margin-left: 87px;
      }
      .wrtbdoad5{
        margin-left: 21px;
      }
      .ty-color-blue{
        color: #5d9cec;
      }
      .goten{
        position: absolute;
        right: -9px;
      }
    }
  }
  .glmang{
    margin: 35px 20px 20px 20px;
    padding: 10px;
    .bnck-bon{
      width: 90px;height: 41px;
    }
    .bnck{
      margin: 0 96px;
    }


  }
  .tolooksetup{
    .bnck{
      .icon-gray {
        pointer-events: none; /* 使得<i>标签不可点击 */
        color: grey; /* 设置灰色字体 */
        opacity: 0.6; /* 设置透明度以达到灰色的视觉效果 */
      }
    }
    .messg1{
      .bussine{
        span{font-weight: bold;}
        .nire{
          span{margin-right: 9px;}
        }
      }
      .com_address{
        padding-bottom: 20px;
        .tipSize{
          margin-top: -10px;
        }
      }
    }
    .messg2{
      .process1{
        .flexsty{
          .tipSize{
            margin-top: -10px;
          }
          .process2{
            .flexsty{
              .tipSize{
                margin-top: -10px;
              }
            }
          }
        }
        &>div:nth-child(2){
          .process2{
            .flexsty{
              &>div:nth-child(2){
                span{font-weight: bold;}
                span:nth-child(4){color: #ff0000;}
              }
            }
          }
        }
      }
    }
    .flexsty{
      display: flex;
      &>div{ flex: 1 }
      &>div:nth-child(2){
        text-align: right; padding-right: 10px;font-weight: $myFontWith;
        div{
          span{font-weight: bold;}
          span:nth-child(4){color: #ff0000;}
          .dette{color: #FF0000;}
        }
      }
      &>div:nth-child(3){ text-align: right }
    }
    .process1{  }
    .process2{ padding:10px 0 10px 0; }
    .Operate{ padding:10px 0 10px 0; }
    .com_address{ border-bottom: 2px solid #ccc; margin-bottom: 20px; margin-top: 20px; }
  }
  .choseseten{
    .chose{
      margin-top: 20px;margin-bottom: 20px;
      .ty-right{
        position: relative;right: 0;
        .selecter{
          width: 187px;
        }
      }
    }
    .custom-radio-group{
      background-color: #D9D9D9; /* 设置背景颜色 */
    }
    .cannot-uncheck {
      /* 可以添加一些样式来表明 checkbox 是不可修改的，比如灰色背景或禁用状态的样式 */
      pointer-events: none; /* 这将阻止用户与该元素交互，但不会影响样式显示 */
      opacity: 0.6;         /* 降低透明度以表明它是不可用的 */
    }
    .locked-indicator {
      /* 添加一个指示器来表明 checkbox 是锁定的 */
      margin-left: 8px;
      color: red;
      font-size: 12px;
    }
    .box-color .el-checkbox__input.is-checked .el-checkbox__inner{
      background-color: #D9D9D9;
      border-color: #D9D9D9;
    }
    .radio-color{
      .el-radio__input.is-checked .el-radio__inner{
        background-color: #D9D9D9;
        border-color: #D9D9D9;
      }
      .el-radio__input.is-checked + .el-radio__label{
        color: #A8ABB2;
      }
    }
  }
  .chidlistplc{
    .chose{
      margin-top: 20px;margin-bottom: 20px;
      .ty-right{
        position: relative;right: 0;
        .selecter{
          width: 187px;
        }
      }
    }
    .box-color .el-checkbox__input.is-checked .el-checkbox__inner{
      background-color: #D9D9D9;
      border-color: #D9D9D9;
    }
  }
  .oprateContainer{
    p{
      margin-top: 14px;
    }
    .chose{
      padding-bottom: 0;
      margin-bottom: 9px;
      .ty-right{
        position: relative;right: 0;top: 2px;
        .selecter{
          width: 187px;
        }
      }
    }
    .box-color .el-checkbox__input.is-checked .el-checkbox__inner{
      background-color: #D9D9D9;
      border-color: #D9D9D9;
    }
  }
  .process1Order{
    .p1{
      margin-top: 20px;margin-bottom: 20px;
    }
    .p2{
      margin: $myMargn;
    }
  }
  .process2Order{
    .p1{
      margin-top: 20px;margin-bottom: 20px;
    }
    .p2{
      margin: $myMargn;
    }
  }
  .oprateOrder{
    .p1{
      margin-top: 20px;margin-bottom: 20px;
    }
    .p2{
      margin: $myMargn;
    }
  }
  .stopcat{
    font-weight: $myFontWith;
  }
  .linklis{
    margin-bottom: 10px;
  }
  .allplnce{
    margin-top: 20px;
  }
  .processletion{
    .ty-alert{ $myClear: both; clear: $myClear; }
    .ty-alert{
      color: #101010;justify-content: space-between;
      width: 100%;
      padding: 8px 0;
      box-sizing: border-box;
      border-radius: 2px;
      position: relative;
      font-size: 13px;
      overflow: hidden;
      opacity: 1;
      display: flex;
      align-items: center;
      transition: opacity .2s;
      margin-bottom: 4px;
      p{
        margin: 0 0 10px;
      }
      .thin-btn{
        color: #0070c0;
        margin-right: 20px;
        font-weight: $myFontWith;
      }
      .thin-btn-hid{
        color:#D9D9D9;margin-right: 20px;font-weight: $myFontWith;
      }
      .wrtdboad{
        margin-left: 121px;
      }
      .wrtdboad2{
        margin-left: 152px;
      }
      .wrtdboad3{
        margin-left: 106px;
      }
      .wrtdboad4{
        margin-left: 138px;
      }
      .wrtbdoad5{
        margin-left: 21px;
      }
      .ty-color-blue{
        color: #5d9cec;
      }
      .goten{
        position: absolute;
        right: 0;
      }
    }
    .addpress1{
      margin-right: 6.5px;
    }
    .addpress1-1{
      margin-right: 4px;
    }
    .downtwn{
      margin-top:20px;
    }
  }
  .plctr{
    margin-left: 58px;
    margin-top: 20px;
  }


  .scrollable-div{
    width: 100%;//设置宽度
    height: 425px;//设置高度
    overflow: auto;//当内容超出盒子尺寸时显示滚动条
  }

}



</style>
