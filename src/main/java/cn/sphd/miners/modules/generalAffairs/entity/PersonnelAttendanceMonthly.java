package cn.sphd.miners.modules.generalAffairs.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.generalAffairs.service.PersonnelAttendanceMonthlyService.WholeDayStatus;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.Logger;

import javax.persistence.*;
import java.lang.reflect.InvocationTargetException;
import java.util.*;

/**
 * Created by Wyu on 2018/10/26.
 * 员工月考勤表
 */
@Entity
@Table(name = "t_personnel_attendance_monthly")
public class PersonnelAttendanceMonthly extends BaseEntity {
    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="yearmonth")
    private Integer yearmonth;//年月数（如：201810）

    @Column(name="user")
    private Integer user;//用户id ，考虑到以前好多接口，暂时未加集联关系

    @Column(name="user_name", length = 20)
    private String userName;//用户姓名

    @Column(name="org")
    private Integer org;//机构id

    @Column(name="department")
    private Integer department;//部门id

    @Column(name="department_name", length = 64)
    private String departmentName;//部门名称

    @Column(name="first_date")
    @JsonIgnore @JSONField(serialize = false)
    private Byte firstDate;//首日几号

    @Column(name="monthdiff")
    @JsonIgnore @JSONField(serialize = false)
    private Byte monthdiff;//月份与首日月份的差,0或-1

    @Column(name="no_need_01")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed01;//1号无需考勤

    @Column(name="attendanced_01")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced01;//1号是否考勤人员

    @Column(name="working_hours_01", length = 3, scale = 1)
    private Float workingHours01;//1号工作时间,规定的上班时间 `working_hours_01` FLOAT(3,1) DEFAULT 0.0 COMMENT '1号工作时间,规定的上班时间'

    @Column(name="leave_01")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave01;//1号请假次数 `leave_01` TINYINT(4) NULL DEFAULT '0' COMMENT '1号请假次数'

    @Column(name="leave_duration_01", length = 3, scale = 1)
    private Float leaveDuration01;//FLOAT(3,1) DEFAULT 0.0 COMMENT '1号请假时长'

    @Column(name="travel_01")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel01;//1号出差次数

    @Column(name="late_01")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late01;//1号迟到

    @Column(name="leave_early_01")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly01;//1号早退

    @Column(name="outside_01")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside01;//1号外出次数

    @Column(name="absenteeism_01")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism01;//1号旷工次数

    @Column(name="overtime_01")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime01;//1号加班次数 `overtime_01` TINYINT(4) DEFAULT 0 COMMENT '1号加班次数'

    @Column(name="overtime_duration_01", length = 3, scale = 1)
    private Float overtimeDuration01;//1号加班时长 `overtime_duration_01` FLOAT(3,1) DEFAULT 0.0 COMMENT '1号加班时长'

    @Column(name="no_need_02")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed02;//2号无需考勤

    @Column(name="attendanced_02")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced02;//2号是否考勤人员

    @Column(name="working_hours_02", length = 3, scale = 1)
    private Float workingHours02;//2号工作时间,规定的上班时间

    @Column(name="leave_02")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave02;//2号请假次数

    @Column(name="leave_duration_02", length = 3, scale = 1)
    private Float leaveDuration02;//2号请假时长

    @Column(name="travel_02")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel02;//2号出差次数

    @Column(name="late_02")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late02;//2号迟到

    @Column(name="leave_early_02")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly02;//2号早退

    @Column(name="outside_02")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside02;//2号外出次数

    @Column(name="absenteeism_02")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism02;//2号旷工次数

    @Column(name="overtime_02")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime02;//2号加班次数

    @Column(name="overtime_duration_02", length = 3, scale = 1)
    private Float overtimeDuration02;//2号加班时长

    @Column(name="no_need_03")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed03;//3号无需考勤

    @Column(name="attendanced_03")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced03;//3号是否考勤人员

    @Column(name="working_hours_03", length = 3, scale = 1)
    private Float workingHours03;//3号工作时间,规定的上班时间

    @Column(name="leave_03")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave03;//3号请假次数

    @Column(name="leave_duration_03", length = 3, scale = 1)
    private Float leaveDuration03;//3号请假时长

    @Column(name="travel_03")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel03;//3号出差次数

    @Column(name="late_03")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late03;//3号迟到

    @Column(name="leave_early_03")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly03;//3号早退

    @Column(name="outside_03")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside03;//3号外出次数

    @Column(name="absenteeism_03")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism03;//3号旷工次数

    @Column(name="overtime_03")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime03;//3号加班次数

    @Column(name="overtime_duration_03", length = 3, scale = 1)
    private Float overtimeDuration03;//3号加班时长

    @Column(name="no_need_04")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed04;//4号无需考勤

    @Column(name="attendanced_04")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced04;//4号是否考勤人员

    @Column(name="working_hours_04", length = 3, scale = 1)
    private Float workingHours04;//4号工作时间,规定的上班时间

    @Column(name="leave_04")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave04;//4号请假次数

    @Column(name="leave_duration_04", length = 3, scale = 1)
    private Float leaveDuration04;//4号请假时长

    @Column(name="travel_04")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel04;//4号出差次数

    @Column(name="late_04")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late04;//4号迟到

    @Column(name="leave_early_04")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly04;//4号早退

    @Column(name="outside_04")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside04;//4号外出次数

    @Column(name="absenteeism_04")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism04;//4号旷工次数

    @Column(name="overtime_04")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime04;//4号加班次数

    @Column(name="overtime_duration_04", length = 3, scale = 1)
    private Float overtimeDuration04;//4号加班时长

    @Column(name="no_need_05")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed05;//5号无需考勤

    @Column(name="attendanced_05")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced05;//5号是否考勤人员

    @Column(name="working_hours_05", length = 3, scale = 1)
    private Float workingHours05;//5号工作时间,规定的上班时间

    @Column(name="leave_05")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave05;//5号请假次数

    @Column(name="leave_duration_05", length = 3, scale = 1)
    private Float leaveDuration05;//5号请假时长

    @Column(name="travel_05")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel05;//5号出差次数

    @Column(name="late_05")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late05;//5号迟到

    @Column(name="leave_early_05")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly05;//5号早退

    @Column(name="outside_05")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside05;//5号外出次数

    @Column(name="absenteeism_05")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism05;//5号旷工次数

    @Column(name="overtime_05")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime05;//5号加班次数

    @Column(name="overtime_duration_05", length = 3, scale = 1)
    private Float overtimeDuration05;//5号加班时长

    @Column(name="no_need_06")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed06;//6号无需考勤

    @Column(name="attendanced_06")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced06;//6号是否考勤人员

    @Column(name="working_hours_06", length = 3, scale = 1)
    private Float workingHours06;//6号工作时间,规定的上班时间

    @Column(name="leave_06")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave06;//6号请假次数

    @Column(name="leave_duration_06", length = 3, scale = 1)
    private Float leaveDuration06;//6号请假时长

    @Column(name="travel_06")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel06;//6号出差次数

    @Column(name="late_06")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late06;//6号迟到

    @Column(name="leave_early_06")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly06;//6号早退

    @Column(name="outside_06")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside06;//6号外出次数

    @Column(name="absenteeism_06")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism06;//6号旷工次数

    @Column(name="overtime_06")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime06;//6号加班次数

    @Column(name="overtime_duration_06", length = 3, scale = 1)
    private Float overtimeDuration06;//6号加班时长

    @Column(name="no_need_07")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed07;//7号无需考勤

    @Column(name="attendanced_07")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced07;//7号是否考勤人员

    @Column(name="working_hours_07", length = 3, scale = 1)
    private Float workingHours07;//7号工作时间,规定的上班时间

    @Column(name="leave_07")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave07;//7号请假次数

    @Column(name="leave_duration_07", length = 3, scale = 1)
    private Float leaveDuration07;//7号请假时长

    @Column(name="travel_07")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel07;//7号出差次数

    @Column(name="late_07")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late07;//7号迟到

    @Column(name="leave_early_07")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly07;//7号早退

    @Column(name="outside_07")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside07;//7号外出次数

    @Column(name="absenteeism_07")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism07;//7号旷工次数

    @Column(name="overtime_07")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime07;//7号加班次数

    @Column(name="overtime_duration_07", length = 3, scale = 1)
    private Float overtimeDuration07;//7号加班时长

    @Column(name="no_need_08")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed08;//8号无需考勤

    @Column(name="attendanced_08")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced08;//8号是否考勤人员

    @Column(name="working_hours_08", length = 3, scale = 1)
    private Float workingHours08;//8号工作时间,规定的上班时间

    @Column(name="leave_08")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave08;//8号请假次数

    @Column(name="leave_duration_08", length = 3, scale = 1)
    private Float leaveDuration08;//8号请假时长

    @Column(name="travel_08")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel08;//8号出差次数

    @Column(name="late_08")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late08;//8号迟到

    @Column(name="leave_early_08")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly08;//8号早退

    @Column(name="outside_08")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside08;//8号外出次数

    @Column(name="absenteeism_08")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism08;//8号旷工次数

    @Column(name="overtime_08")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime08;//8号加班次数

    @Column(name="overtime_duration_08", length = 3, scale = 1)
    private Float overtimeDuration08;//8号加班时长

    @Column(name="no_need_09")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed09;//9号无需考勤

    @Column(name="attendanced_09")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced09;//9号是否考勤人员

    @Column(name="working_hours_09", length = 3, scale = 1)
    private Float workingHours09;//9号工作时间,规定的上班时间

    @Column(name="leave_09")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave09;//9号请假次数

    @Column(name="leave_duration_09", length = 3, scale = 1)
    private Float leaveDuration09;//9号请假时长

    @Column(name="travel_09")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel09;//9号出差次数

    @Column(name="late_09")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late09;//9号迟到

    @Column(name="leave_early_09")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly09;//9号早退

    @Column(name="outside_09")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside09;//9号外出次数

    @Column(name="absenteeism_09")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism09;//9号旷工次数

    @Column(name="overtime_09")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime09;//9号加班次数

    @Column(name="overtime_duration_09", length = 3, scale = 1)
    private Float overtimeDuration09;//9号加班时长

    @Column(name="no_need_10")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed10;//10号无需考勤

    @Column(name="attendanced_10")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced10;//10号是否考勤人员

    @Column(name="working_hours_10", length = 3, scale = 1)
    private Float workingHours10;//10号工作时间,规定的上班时间

    @Column(name="leave_10")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave10;//10号请假次数

    @Column(name="leave_duration_10", length = 3, scale = 1)
    private Float leaveDuration10;//10号请假时长

    @Column(name="travel_10")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel10;//10号出差次数

    @Column(name="late_10")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late10;//10号迟到

    @Column(name="leave_early_10")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly10;//10号早退

    @Column(name="outside_10")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside10;//10号外出次数

    @Column(name="absenteeism_10")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism10;//10号旷工次数

    @Column(name="overtime_10")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime10;//10号加班次数

    @Column(name="overtime_duration_10", length = 3, scale = 1)
    private Float overtimeDuration10;//10号加班时长

    @Column(name="no_need_11")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed11;//11号无需考勤

    @Column(name="attendanced_11")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced11;//11号是否考勤人员

    @Column(name="working_hours_11", length = 3, scale = 1)
    private Float workingHours11;//11号工作时间,规定的上班时间

    @Column(name="leave_11")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave11;//11号请假次数

    @Column(name="leave_duration_11", length = 3, scale = 1)
    private Float leaveDuration11;//11号请假时长

    @Column(name="travel_11")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel11;//11号出差次数

    @Column(name="late_11")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late11;//11号迟到

    @Column(name="leave_early_11")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly11;//11号早退

    @Column(name="outside_11")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside11;//11号外出次数

    @Column(name="absenteeism_11")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism11;//11号旷工次数

    @Column(name="overtime_11")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime11;//11号加班次数

    @Column(name="overtime_duration_11", length = 3, scale = 1)
    private Float overtimeDuration11;//11号加班时长

    @Column(name="no_need_12")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed12;//12号无需考勤

    @Column(name="attendanced_12")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced12;//12号是否考勤人员

    @Column(name="working_hours_12", length = 3, scale = 1)
    private Float workingHours12;//12号工作时间,规定的上班时间

    @Column(name="leave_12")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave12;//12号请假次数

    @Column(name="leave_duration_12", length = 3, scale = 1)
    private Float leaveDuration12;//12号请假时长

    @Column(name="travel_12")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel12;//12号出差次数

    @Column(name="late_12")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late12;//12号迟到

    @Column(name="leave_early_12")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly12;//12号早退

    @Column(name="outside_12")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside12;//12号外出次数

    @Column(name="absenteeism_12")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism12;//12号旷工次数

    @Column(name="overtime_12")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime12;//12号加班次数

    @Column(name="overtime_duration_12", length = 3, scale = 1)
    private Float overtimeDuration12;//12号加班时长

    @Column(name="no_need_13")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed13;//13号无需考勤

    @Column(name="attendanced_13")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced13;//13号是否考勤人员

    @Column(name="working_hours_13", length = 3, scale = 1)
    private Float workingHours13;//13号工作时间,规定的上班时间

    @Column(name="leave_13")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave13;//13号请假次数

    @Column(name="leave_duration_13", length = 3, scale = 1)
    private Float leaveDuration13;//13号请假时长

    @Column(name="travel_13")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel13;//13号出差次数

    @Column(name="late_13")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late13;//13号迟到

    @Column(name="leave_early_13")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly13;//13号早退

    @Column(name="outside_13")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside13;//13号外出次数

    @Column(name="absenteeism_13")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism13;//13号旷工次数

    @Column(name="overtime_13")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime13;//13号加班次数

    @Column(name="overtime_duration_13", length = 3, scale = 1)
    private Float overtimeDuration13;//13号加班时长

    @Column(name="no_need_14")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed14;//14号无需考勤

    @Column(name="attendanced_14")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced14;//14号是否考勤人员

    @Column(name="working_hours_14", length = 3, scale = 1)
    private Float workingHours14;//14号工作时间,规定的上班时间

    @Column(name="leave_14")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave14;//14号请假次数

    @Column(name="leave_duration_14", length = 3, scale = 1)
    private Float leaveDuration14;//14号请假时长

    @Column(name="travel_14")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel14;//14号出差次数

    @Column(name="late_14")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late14;//14号迟到

    @Column(name="leave_early_14")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly14;//14号早退

    @Column(name="outside_14")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside14;//14号外出次数

    @Column(name="absenteeism_14")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism14;//14号旷工次数

    @Column(name="overtime_14")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime14;//14号加班次数

    @Column(name="overtime_duration_14", length = 3, scale = 1)
    private Float overtimeDuration14;//14号加班时长

    @Column(name="no_need_15")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed15;//15号无需考勤

    @Column(name="attendanced_15")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced15;//15号是否考勤人员

    @Column(name="working_hours_15", length = 3, scale = 1)
    private Float workingHours15;//15号工作时间,规定的上班时间

    @Column(name="leave_15")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave15;//15号请假次数

    @Column(name="leave_duration_15", length = 3, scale = 1)
    private Float leaveDuration15;//15号请假时长

    @Column(name="travel_15")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel15;//15号出差次数

    @Column(name="late_15")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late15;//15号迟到

    @Column(name="leave_early_15")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly15;//15号早退

    @Column(name="outside_15")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside15;//15号外出次数

    @Column(name="absenteeism_15")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism15;//15号旷工次数

    @Column(name="overtime_15")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime15;//15号加班次数

    @Column(name="overtime_duration_15", length = 3, scale = 1)
    private Float overtimeDuration15;//15号加班时长

    @Column(name="no_need_16")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed16;//16号无需考勤

    @Column(name="attendanced_16")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced16;//16号是否考勤人员

    @Column(name="working_hours_16", length = 3, scale = 1)
    private Float workingHours16;//16号工作时间,规定的上班时间

    @Column(name="leave_16")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave16;//16号请假次数

    @Column(name="leave_duration_16", length = 3, scale = 1)
    private Float leaveDuration16;//16号请假时长

    @Column(name="travel_16")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel16;//16号出差次数

    @Column(name="late_16")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late16;//16号迟到

    @Column(name="leave_early_16")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly16;//16号早退

    @Column(name="outside_16")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside16;//16号外出次数

    @Column(name="absenteeism_16")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism16;//16号旷工次数

    @Column(name="overtime_16")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime16;//16号加班次数

    @Column(name="overtime_duration_16", length = 3, scale = 1)
    private Float overtimeDuration16;//16号加班时长

    @Column(name="no_need_17")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed17;//17号无需考勤

    @Column(name="attendanced_17")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced17;//17号是否考勤人员

    @Column(name="working_hours_17", length = 3, scale = 1)
    private Float workingHours17;//17号工作时间,规定的上班时间

    @Column(name="leave_17")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave17;//17号请假次数

    @Column(name="leave_duration_17", length = 3, scale = 1)
    private Float leaveDuration17;//17号请假时长

    @Column(name="travel_17")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel17;//17号出差次数

    @Column(name="late_17")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late17;//17号迟到

    @Column(name="leave_early_17")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly17;//17号早退

    @Column(name="outside_17")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside17;//17号外出次数

    @Column(name="absenteeism_17")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism17;//17号旷工次数

    @Column(name="overtime_17")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime17;//17号加班次数

    @Column(name="overtime_duration_17", length = 3, scale = 1)
    private Float overtimeDuration17;//17号加班时长

    @Column(name="no_need_18")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed18;//18号无需考勤

    @Column(name="attendanced_18")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced18;//18号是否考勤人员

    @Column(name="working_hours_18", length = 3, scale = 1)
    private Float workingHours18;//18号工作时间,规定的上班时间

    @Column(name="leave_18")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave18;//18号请假次数

    @Column(name="leave_duration_18", length = 3, scale = 1)
    private Float leaveDuration18;//18号请假时长

    @Column(name="travel_18")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel18;//18号出差次数

    @Column(name="late_18")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late18;//18号迟到

    @Column(name="leave_early_18")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly18;//18号早退

    @Column(name="outside_18")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside18;//18号外出次数

    @Column(name="absenteeism_18")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism18;//18号旷工次数

    @Column(name="overtime_18")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime18;//18号加班次数

    @Column(name="overtime_duration_18", length = 3, scale = 1)
    private Float overtimeDuration18;//18号加班时长

    @Column(name="no_need_19")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed19;//19号无需考勤

    @Column(name="attendanced_19")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced19;//19号是否考勤人员

    @Column(name="working_hours_19", length = 3, scale = 1)
    private Float workingHours19;//19号工作时间,规定的上班时间

    @Column(name="leave_19")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave19;//19号请假次数

    @Column(name="leave_duration_19", length = 3, scale = 1)
    private Float leaveDuration19;//19号请假时长

    @Column(name="travel_19")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel19;//19号出差次数

    @Column(name="late_19")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late19;//19号迟到

    @Column(name="leave_early_19")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly19;//19号早退

    @Column(name="outside_19")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside19;//19号外出次数

    @Column(name="absenteeism_19")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism19;//19号旷工次数

    @Column(name="overtime_19")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime19;//19号加班次数

    @Column(name="overtime_duration_19", length = 3, scale = 1)
    private Float overtimeDuration19;//19号加班时长

    @Column(name="no_need_20")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed20;//20号无需考勤

    @Column(name="attendanced_20")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced20;//20号是否考勤人员

    @Column(name="working_hours_20", length = 3, scale = 1)
    private Float workingHours20;//20号工作时间,规定的上班时间

    @Column(name="leave_20")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave20;//20号请假次数

    @Column(name="leave_duration_20", length = 3, scale = 1)
    private Float leaveDuration20;//20号请假时长

    @Column(name="travel_20")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel20;//20号出差次数

    @Column(name="late_20")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late20;//20号迟到

    @Column(name="leave_early_20")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly20;//20号早退

    @Column(name="outside_20")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside20;//20号外出次数

    @Column(name="absenteeism_20")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism20;//20号旷工次数

    @Column(name="overtime_20")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime20;//20号加班次数

    @Column(name="overtime_duration_20", length = 3, scale = 1)
    private Float overtimeDuration20;//20号加班时长

    @Column(name="no_need_21")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed21;//21号无需考勤

    @Column(name="attendanced_21")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced21;//21号是否考勤人员

    @Column(name="working_hours_21", length = 3, scale = 1)
    private Float workingHours21;//21号工作时间,规定的上班时间

    @Column(name="leave_21")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave21;//21号请假次数

    @Column(name="leave_duration_21", length = 3, scale = 1)
    private Float leaveDuration21;//21号请假时长

    @Column(name="travel_21")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel21;//21号出差次数

    @Column(name="late_21")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late21;//21号迟到

    @Column(name="leave_early_21")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly21;//21号早退

    @Column(name="outside_21")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside21;//21号外出次数

    @Column(name="absenteeism_21")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism21;//21号旷工次数

    @Column(name="overtime_21")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime21;//21号加班次数

    @Column(name="overtime_duration_21", length = 3, scale = 1)
    private Float overtimeDuration21;//21号加班时长

    @Column(name="no_need_22")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed22;//22号无需考勤

    @Column(name="attendanced_22")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced22;//22号是否考勤人员

    @Column(name="working_hours_22", length = 3, scale = 1)
    private Float workingHours22;//22号工作时间,规定的上班时间

    @Column(name="leave_22")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave22;//22号请假次数

    @Column(name="leave_duration_22", length = 3, scale = 1)
    private Float leaveDuration22;//22号请假时长

    @Column(name="travel_22")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel22;//22号出差次数

    @Column(name="late_22")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late22;//22号迟到

    @Column(name="leave_early_22")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly22;//2号早退

    @Column(name="outside_22")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside22;//22号外出次数

    @Column(name="absenteeism_22")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism22;//22号旷工次数

    @Column(name="overtime_22")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime22;//22号加班次数

    @Column(name="overtime_duration_22", length = 3, scale = 1)
    private Float overtimeDuration22;//22号加班时长

    @Column(name="no_need_23")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed23;//23号无需考勤

    @Column(name="attendanced_23")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced23;//23号是否考勤人员

    @Column(name="working_hours_23", length = 3, scale = 1)
    private Float workingHours23;//23号工作时间,规定的上班时间

    @Column(name="leave_23")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave23;//23号请假次数

    @Column(name="leave_duration_23", length = 3, scale = 1)
    private Float leaveDuration23;//23号请假时长

    @Column(name="travel_23")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel23;//23号出差次数

    @Column(name="late_23")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late23;//23号迟到

    @Column(name="leave_early_23")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly23;//23号早退

    @Column(name="outside_23")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside23;//23号外出次数

    @Column(name="absenteeism_23")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism23;//23号旷工次数

    @Column(name="overtime_23")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime23;//23号加班次数

    @Column(name="overtime_duration_23", length = 3, scale = 1)
    private Float overtimeDuration23;//23号加班时长

    @Column(name="no_need_24")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed24;//24号无需考勤

    @Column(name="attendanced_24")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced24;//24号是否考勤人员

    @Column(name="working_hours_24", length = 3, scale = 1)
    private Float workingHours24;//24号工作时间,规定的上班时间

    @Column(name="leave_24")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave24;//24号请假次数

    @Column(name="leave_duration_24", length = 3, scale = 1)
    private Float leaveDuration24;//24号请假时长

    @Column(name="travel_24")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel24;//24号出差次数

    @Column(name="late_24")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late24;//24号迟到

    @Column(name="leave_early_24")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly24;//24号早退

    @Column(name="outside_24")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside24;//24号外出次数

    @Column(name="absenteeism_24")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism24;//24号旷工次数

    @Column(name="overtime_24")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime24;//24号加班次数

    @Column(name="overtime_duration_24", length = 3, scale = 1)
    private Float overtimeDuration24;//24号加班时长

    @Column(name="no_need_25")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed25;//25号无需考勤

    @Column(name="attendanced_25")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced25;//25号是否考勤人员

    @Column(name="working_hours_25", length = 3, scale = 1)
    private Float workingHours25;//25号工作时间,规定的上班时间

    @Column(name="leave_25")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave25;//25号请假次数

    @Column(name="leave_duration_25", length = 3, scale = 1)
    private Float leaveDuration25;//25号请假时长

    @Column(name="travel_25")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel25;//25号出差次数

    @Column(name="late_25")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late25;//25号迟到

    @Column(name="leave_early_25")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly25;//25号早退

    @Column(name="outside_25")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside25;//25号外出次数

    @Column(name="absenteeism_25")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism25;//25号旷工次数

    @Column(name="overtime_25")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime25;//25号加班次数

    @Column(name="overtime_duration_25", length = 3, scale = 1)
    private Float overtimeDuration25;//25号加班时长

    @Column(name="no_need_26")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed26;//26号无需考勤

    @Column(name="attendanced_26")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced26;//26号是否考勤人员

    @Column(name="working_hours_26", length = 3, scale = 1)
    private Float workingHours26;//26号工作时间,规定的上班时间

    @Column(name="leave_26")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave26;//26号请假次数

    @Column(name="leave_duration_26", length = 3, scale = 1)
    private Float leaveDuration26;//26号请假时长

    @Column(name="travel_26")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel26;//26号出差次数

    @Column(name="late_26")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late26;//26号迟到

    @Column(name="leave_early_26")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly26;//26号早退

    @Column(name="outside_26")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside26;//26号外出次数

    @Column(name="absenteeism_26")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism26;//26号旷工次数

    @Column(name="overtime_26")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime26;//26号加班次数

    @Column(name="overtime_duration_26", length = 3, scale = 1)
    private Float overtimeDuration26;//26号加班时长

    @Column(name="no_need_27")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed27;//27号无需考勤

    @Column(name="attendanced_27")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced27;//27号是否考勤人员

    @Column(name="working_hours_27", length = 3, scale = 1)
    private Float workingHours27;//27号工作时间,规定的上班时间

    @Column(name="leave_27")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave27;//27号请假次数

    @Column(name="leave_duration_27", length = 3, scale = 1)
    private Float leaveDuration27;//27号请假时长

    @Column(name="travel_27")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel27;//27号出差次数

    @Column(name="late_27")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late27;//27号迟到

    @Column(name="leave_early_27")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly27;//27号早退

    @Column(name="outside_27")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside27;//27号外出次数

    @Column(name="absenteeism_27")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism27;//27号旷工次数

    @Column(name="overtime_27")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime27;//27号加班次数

    @Column(name="overtime_duration_27", length = 3, scale = 1)
    private Float overtimeDuration27;//27号加班时长

    @Column(name="no_need_28")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed28;//28号无需考勤

    @Column(name="attendanced_28")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced28;//28号是否考勤人员

    @Column(name="working_hours_28", length = 3, scale = 1)
    private Float workingHours28;//28号工作时间,规定的上班时间

    @Column(name="leave_28")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave28;//28号请假次数

    @Column(name="leave_duration_28", length = 3, scale = 1)
    private Float leaveDuration28;//28号请假时长

    @Column(name="travel_28")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel28;//28号出差次数

    @Column(name="late_28")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late28;//28号迟到

    @Column(name="leave_early_28")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly28;//28号早退

    @Column(name="outside_28")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside28;//28号外出次数

    @Column(name="absenteeism_28")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism28;//28号旷工次数

    @Column(name="overtime_28")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime28;//28号加班次数

    @Column(name="overtime_duration_28", length = 3, scale = 1)
    private Float overtimeDuration28;//28号加班时长

    @Column(name="no_need_29")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed29;//29号无需考勤

    @Column(name="attendanced_29")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced29;//29号是否考勤人员

    @Column(name="working_hours_29", length = 3, scale = 1)
    private Float workingHours29;//29号工作时间,规定的上班时间

    @Column(name="leave_29")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave29;//29号请假次数

    @Column(name="leave_duration_29", length = 3, scale = 1)
    private Float leaveDuration29;//29号请假时长

    @Column(name="travel_29")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel29;//29号出差次数

    @Column(name="late_29")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late29;//29号迟到

    @Column(name="leave_early_29")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly29;//29号早退

    @Column(name="outside_29")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside29;//29号外出次数

    @Column(name="absenteeism_29")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism29;//29号旷工次数

    @Column(name="overtime_29")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime29;//29号加班次数

    @Column(name="overtime_duration_29", length = 3, scale = 1)
    private Float overtimeDuration29;//29号加班时长

    @Column(name="no_need_30")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed30;//30号无需考勤

    @Column(name="attendanced_30")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced30;//30号是否考勤人员

    @Column(name="working_hours_30", length = 3, scale = 1)
    private Float workingHours30;//30号工作时间,规定的上班时间

    @Column(name="leave_30")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave30;//30号请假次数

    @Column(name="leave_duration_30", length = 3, scale = 1)
    private Float leaveDuration30;//30号请假时长

    @Column(name="travel_30")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel30;//30号出差次数

    @Column(name="late_30")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late30;//30号迟到

    @Column(name="leave_early_30")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly30;//30号早退

    @Column(name="outside_30")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside30;//30号外出次数

    @Column(name="absenteeism_30")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism30;//30号旷工次数

    @Column(name="overtime_30")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime30;//30号加班次数

    @Column(name="overtime_duration_30", length = 3, scale = 1)
    private Float overtimeDuration30;//30号加班时长

    @Column(name="no_need_31")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean noNeed31;//31号无需考勤

    @Column(name="attendanced_31")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean attendanced31;//31号是否考勤人员

    @Column(name="working_hours_31", length = 3, scale = 1)
    private Float workingHours31;//31号工作时间,规定的上班时间

    @Column(name="leave_31")
    @JsonIgnore @JSONField(serialize = false)
    private Byte leave31;//31号请假次数

    @Column(name="leave_duration_31", length = 3, scale = 1)
    private Float leaveDuration31;//31号请假时长

    @Column(name="travel_31")
    @JsonIgnore @JSONField(serialize = false)
    private Byte travel31;//31号出差次数

    @Column(name="late_31")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean late31;//31号迟到

    @Column(name="leave_early_31")
    @JsonIgnore @JSONField(serialize = false)
    private Boolean leaveEarly31;//31号早退

    @Column(name="outside_31")
    @JsonIgnore @JSONField(serialize = false)
    private Byte outside31;//31号外出次数

    @Column(name="absenteeism_31")
    @JsonIgnore @JSONField(serialize = false)
    private Byte absenteeism31;//31号旷工次数

    @Column(name="overtime_31")
    @JsonIgnore @JSONField(serialize = false)
    private Byte overtime31;//31号加班次数

    @Column(name="overtime_duration_31", length = 3, scale = 1)
    private Float overtimeDuration31;//31号加班时长


    @Transient
    private Float workingHours;//月工时

    @Transient
    private Short leaveNum;//月请假次数

    @Transient
    private Float leaveDuration;//月请假总时长

    @Transient
    private Short travelNum;//月出差次数

    @Transient
    private Short lateNum;//月迟到次数

    @Transient
    private Short leaveEarlyNum;//月早退次数

    @Transient
    private Short outsideNum;//月外出次数

    @Transient
    private Short absenteeismNum;//月旷工次数

    @Transient
    private Short overtimeNum;//月加班次数

    @Transient
    private Float overtimeDuration;//月加班总时长

    @Transient
    private String dayStatus01;//1号考勤状态

    @Transient
    private String dayStatus02;//2号考勤状态

    @Transient
    private String dayStatus03;//3号考勤状态

    @Transient
    private String dayStatus04;//4号考勤状态

    @Transient
    private String dayStatus05;//5号考勤状态

    @Transient
    private String dayStatus06;//6号考勤状态

    @Transient
    private String dayStatus07;//7号考勤状态

    @Transient
    private String dayStatus08;//8号考勤状态

    @Transient
    private String dayStatus09;//9号考勤状态

    @Transient
    private String dayStatus10;//10号考勤状态

    @Transient
    private String dayStatus11;//11号考勤状态

    @Transient
    private String dayStatus12;//12号考勤状态

    @Transient
    private String dayStatus13;//13号考勤状态

    @Transient
    private String dayStatus14;//14号考勤状态

    @Transient
    private String dayStatus15;//15号考勤状态

    @Transient
    private String dayStatus16;//16号考勤状态

    @Transient
    private String dayStatus17;//17号考勤状态

    @Transient
    private String dayStatus18;//18号考勤状态

    @Transient
    private String dayStatus19;//19号考勤状态

    @Transient
    private String dayStatus20;//20号考勤状态

    @Transient
    private String dayStatus21;//21号考勤状态

    @Transient
    private String dayStatus22;//22号考勤状态

    @Transient
    private String dayStatus23;//23号考勤状态

    @Transient
    private String dayStatus24;//24号考勤状态

    @Transient
    private String dayStatus25;//25号考勤状态

    @Transient
    private String dayStatus26;//26号考勤状态

    @Transient
    private String dayStatus27;//27号考勤状态

    @Transient
    private String dayStatus28;//28号考勤状态

    @Transient
    private String dayStatus29;//29号考勤状态

    @Transient
    private String dayStatus30;//30号考勤状态

    @Transient
    private String dayStatus31;//31号考勤状态

    @Transient
    private String imgPath;  //职工图片路径

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getYearmonth() {
        return yearmonth;
    }

    public void setYearmonth(Integer yearmonth) {
        this.yearmonth = yearmonth;
    }

    public Integer getUser() {
        return user;
    }

    public void setUser(Integer user) {
        this.user = user;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {

        if (!MyStrings.nulltoempty(userName).isEmpty() && userName.length()>20) {
            this.userName = userName.substring(0, 20);
        }else {
            this.userName = userName;
        }
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getDepartment() {
        return department;
    }

    public void setDepartment(Integer department) {
        this.department = department;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        if (!MyStrings.nulltoempty(departmentName).isEmpty() && departmentName.length()>64) {
            this.departmentName = departmentName.substring(0, 63);
        }else {
            this.departmentName = departmentName;
        }
    }

    public Byte getFirstDate() {
        return firstDate;
    }

    public void setFirstDate(Byte firstDate) {
        this.firstDate = firstDate;
    }

    public Byte getMonthdiff() {
        return monthdiff;
    }

    public void setMonthdiff(Byte monthdiff) {
        this.monthdiff = monthdiff;
    }

    public Boolean getNoNeed01() {
        return noNeed01;
    }

    public void setNoNeed01(Boolean noNeed01) {
        this.noNeed01 = noNeed01;
    }

    public Boolean getattendanced01() {
        return attendanced01;
    }

    public void setattendanced01(Boolean attendanced01) {
        this.attendanced01 = attendanced01;
    }

    public Float getWorkingHours01() {
        return workingHours01;
    }

    public void setWorkingHours01(Float workingHours01) {
        this.workingHours01 = workingHours01;
    }

    public Byte getLeave01() {
        return leave01;
    }

    public void setLeave01(Byte leave01) {
        this.leave01 = leave01;
    }

    public Float getLeaveDuration01() {
        return leaveDuration01;
    }

    public void setLeaveDuration01(Float leaveDuration01) {
        this.leaveDuration01 = leaveDuration01;
    }

    public Byte getTravel01() {
        return travel01;
    }

    public void setTravel01(Byte travel01) {
        this.travel01 = travel01;
    }

    public Boolean getLate01() {
        return late01;
    }

    public void setLate01(Boolean late01) {
        this.late01 = late01;
    }

    public Boolean getLeaveEarly01() {
        return leaveEarly01;
    }

    public void setLeaveEarly01(Boolean leaveEarly01) {
        this.leaveEarly01 = leaveEarly01;
    }

    public Byte getOutside01() {
        return outside01;
    }

    public void setOutside01(Byte outside01) {
        this.outside01 = outside01;
    }

    public Byte getAbsenteeism01() {
        return absenteeism01;
    }

    public void setAbsenteeism01(Byte absenteeism01) {
        this.absenteeism01 = absenteeism01;
    }

    public Byte getOvertime01() {
        return overtime01;
    }

    public void setOvertime01(Byte overtime01) {
        this.overtime01 = overtime01;
    }

    public Float getOvertimeDuration01() {
        return overtimeDuration01;
    }

    public void setOvertimeDuration01(Float overtimeDuration01) {
        this.overtimeDuration01 = overtimeDuration01;
    }

    public Boolean getNoNeed02() {
        return noNeed02;
    }

    public void setNoNeed02(Boolean noNeed02) {
        this.noNeed02 = noNeed02;
    }

    public Boolean getattendanced02() {
        return attendanced02;
    }

    public void setattendanced02(Boolean attendanced02) {
        this.attendanced02 = attendanced02;
    }

    public Float getWorkingHours02() {
        return workingHours02;
    }

    public void setWorkingHours02(Float workingHours02) {
        this.workingHours02 = workingHours02;
    }

    public Byte getLeave02() {
        return leave02;
    }

    public void setLeave02(Byte leave02) {
        this.leave02 = leave02;
    }

    public Float getLeaveDuration02() {
        return leaveDuration02;
    }

    public void setLeaveDuration02(Float leaveDuration02) {
        this.leaveDuration02 = leaveDuration02;
    }

    public Byte getTravel02() {
        return travel02;
    }

    public void setTravel02(Byte travel02) {
        this.travel02 = travel02;
    }

    public Boolean getLate02() {
        return late02;
    }

    public void setLate02(Boolean late02) {
        this.late02 = late02;
    }

    public Boolean getLeaveEarly02() {
        return leaveEarly02;
    }

    public void setLeaveEarly02(Boolean leaveEarly02) {
        this.leaveEarly02 = leaveEarly02;
    }

    public Byte getOutside02() {
        return outside02;
    }

    public void setOutside02(Byte outside02) {
        this.outside02 = outside02;
    }

    public Byte getAbsenteeism02() {
        return absenteeism02;
    }

    public void setAbsenteeism02(Byte absenteeism02) {
        this.absenteeism02 = absenteeism02;
    }

    public Byte getOvertime02() {
        return overtime02;
    }

    public void setOvertime02(Byte overtime02) {
        this.overtime02 = overtime02;
    }

    public Float getOvertimeDuration02() {
        return overtimeDuration02;
    }

    public void setOvertimeDuration02(Float overtimeDuration02) {
        this.overtimeDuration02 = overtimeDuration02;
    }

    public Boolean getNoNeed03() {
        return noNeed03;
    }

    public void setNoNeed03(Boolean noNeed03) {
        this.noNeed03 = noNeed03;
    }

    public Boolean getattendanced03() {
        return attendanced03;
    }

    public void setattendanced03(Boolean attendanced03) {
        this.attendanced03 = attendanced03;
    }

    public Float getWorkingHours03() {
        return workingHours03;
    }

    public void setWorkingHours03(Float workingHours03) {
        this.workingHours03 = workingHours03;
    }

    public Byte getLeave03() {
        return leave03;
    }

    public void setLeave03(Byte leave03) {
        this.leave03 = leave03;
    }

    public Float getLeaveDuration03() {
        return leaveDuration03;
    }

    public void setLeaveDuration03(Float leaveDuration03) {
        this.leaveDuration03 = leaveDuration03;
    }

    public Byte getTravel03() {
        return travel03;
    }

    public void setTravel03(Byte travel03) {
        this.travel03 = travel03;
    }

    public Boolean getLate03() {
        return late03;
    }

    public void setLate03(Boolean late03) {
        this.late03 = late03;
    }

    public Boolean getLeaveEarly03() {
        return leaveEarly03;
    }

    public void setLeaveEarly03(Boolean leaveEarly03) {
        this.leaveEarly03 = leaveEarly03;
    }

    public Byte getOutside03() {
        return outside03;
    }

    public void setOutside03(Byte outside03) {
        this.outside03 = outside03;
    }

    public Byte getAbsenteeism03() {
        return absenteeism03;
    }

    public void setAbsenteeism03(Byte absenteeism03) {
        this.absenteeism03 = absenteeism03;
    }

    public Byte getOvertime03() {
        return overtime03;
    }

    public void setOvertime03(Byte overtime03) {
        this.overtime03 = overtime03;
    }

    public Float getOvertimeDuration03() {
        return overtimeDuration03;
    }

    public void setOvertimeDuration03(Float overtimeDuration03) {
        this.overtimeDuration03 = overtimeDuration03;
    }

    public Boolean getNoNeed04() {
        return noNeed04;
    }

    public void setNoNeed04(Boolean noNeed04) {
        this.noNeed04 = noNeed04;
    }

    public Boolean getattendanced04() {
        return attendanced04;
    }

    public void setattendanced04(Boolean attendanced04) {
        this.attendanced04 = attendanced04;
    }

    public Float getWorkingHours04() {
        return workingHours04;
    }

    public void setWorkingHours04(Float workingHours04) {
        this.workingHours04 = workingHours04;
    }

    public Byte getLeave04() {
        return leave04;
    }

    public void setLeave04(Byte leave04) {
        this.leave04 = leave04;
    }

    public Float getLeaveDuration04() {
        return leaveDuration04;
    }

    public void setLeaveDuration04(Float leaveDuration04) {
        this.leaveDuration04 = leaveDuration04;
    }

    public Byte getTravel04() {
        return travel04;
    }

    public void setTravel04(Byte travel04) {
        this.travel04 = travel04;
    }

    public Boolean getLate04() {
        return late04;
    }

    public void setLate04(Boolean late04) {
        this.late04 = late04;
    }

    public Boolean getLeaveEarly04() {
        return leaveEarly04;
    }

    public void setLeaveEarly04(Boolean leaveEarly04) {
        this.leaveEarly04 = leaveEarly04;
    }

    public Byte getOutside04() {
        return outside04;
    }

    public void setOutside04(Byte outside04) {
        this.outside04 = outside04;
    }

    public Byte getAbsenteeism04() {
        return absenteeism04;
    }

    public void setAbsenteeism04(Byte absenteeism04) {
        this.absenteeism04 = absenteeism04;
    }

    public Byte getOvertime04() {
        return overtime04;
    }

    public void setOvertime04(Byte overtime04) {
        this.overtime04 = overtime04;
    }

    public Float getOvertimeDuration04() {
        return overtimeDuration04;
    }

    public void setOvertimeDuration04(Float overtimeDuration04) {
        this.overtimeDuration04 = overtimeDuration04;
    }

    public Boolean getNoNeed05() {
        return noNeed05;
    }

    public void setNoNeed05(Boolean noNeed05) {
        this.noNeed05 = noNeed05;
    }

    public Boolean getattendanced05() {
        return attendanced05;
    }

    public void setattendanced05(Boolean attendanced05) {
        this.attendanced05 = attendanced05;
    }

    public Float getWorkingHours05() {
        return workingHours05;
    }

    public void setWorkingHours05(Float workingHours05) {
        this.workingHours05 = workingHours05;
    }

    public Byte getLeave05() {
        return leave05;
    }

    public void setLeave05(Byte leave05) {
        this.leave05 = leave05;
    }

    public Float getLeaveDuration05() {
        return leaveDuration05;
    }

    public void setLeaveDuration05(Float leaveDuration05) {
        this.leaveDuration05 = leaveDuration05;
    }

    public Byte getTravel05() {
        return travel05;
    }

    public void setTravel05(Byte travel05) {
        this.travel05 = travel05;
    }

    public Boolean getLate05() {
        return late05;
    }

    public void setLate05(Boolean late05) {
        this.late05 = late05;
    }

    public Boolean getLeaveEarly05() {
        return leaveEarly05;
    }

    public void setLeaveEarly05(Boolean leaveEarly05) {
        this.leaveEarly05 = leaveEarly05;
    }

    public Byte getOutside05() {
        return outside05;
    }

    public void setOutside05(Byte outside05) {
        this.outside05 = outside05;
    }

    public Byte getAbsenteeism05() {
        return absenteeism05;
    }

    public void setAbsenteeism05(Byte absenteeism05) {
        this.absenteeism05 = absenteeism05;
    }

    public Byte getOvertime05() {
        return overtime05;
    }

    public void setOvertime05(Byte overtime05) {
        this.overtime05 = overtime05;
    }

    public Float getOvertimeDuration05() {
        return overtimeDuration05;
    }

    public void setOvertimeDuration05(Float overtimeDuration05) {
        this.overtimeDuration05 = overtimeDuration05;
    }

    public Boolean getNoNeed06() {
        return noNeed06;
    }

    public void setNoNeed06(Boolean noNeed06) {
        this.noNeed06 = noNeed06;
    }

    public Boolean getattendanced06() {
        return attendanced06;
    }

    public void setattendanced06(Boolean attendanced06) {
        this.attendanced06 = attendanced06;
    }

    public Float getWorkingHours06() {
        return workingHours06;
    }

    public void setWorkingHours06(Float workingHours06) {
        this.workingHours06 = workingHours06;
    }

    public Byte getLeave06() {
        return leave06;
    }

    public void setLeave06(Byte leave06) {
        this.leave06 = leave06;
    }

    public Float getLeaveDuration06() {
        return leaveDuration06;
    }

    public void setLeaveDuration06(Float leaveDuration06) {
        this.leaveDuration06 = leaveDuration06;
    }

    public Byte getTravel06() {
        return travel06;
    }

    public void setTravel06(Byte travel06) {
        this.travel06 = travel06;
    }

    public Boolean getLate06() {
        return late06;
    }

    public void setLate06(Boolean late06) {
        this.late06 = late06;
    }

    public Boolean getLeaveEarly06() {
        return leaveEarly06;
    }

    public void setLeaveEarly06(Boolean leaveEarly06) {
        this.leaveEarly06 = leaveEarly06;
    }

    public Byte getOutside06() {
        return outside06;
    }

    public void setOutside06(Byte outside06) {
        this.outside06 = outside06;
    }

    public Byte getAbsenteeism06() {
        return absenteeism06;
    }

    public void setAbsenteeism06(Byte absenteeism06) {
        this.absenteeism06 = absenteeism06;
    }

    public Byte getOvertime06() {
        return overtime06;
    }

    public void setOvertime06(Byte overtime06) {
        this.overtime06 = overtime06;
    }

    public Float getOvertimeDuration06() {
        return overtimeDuration06;
    }

    public void setOvertimeDuration06(Float overtimeDuration06) {
        this.overtimeDuration06 = overtimeDuration06;
    }

    public Boolean getNoNeed07() {
        return noNeed07;
    }

    public void setNoNeed07(Boolean noNeed07) {
        this.noNeed07 = noNeed07;
    }

    public Boolean getattendanced07() {
        return attendanced07;
    }

    public void setattendanced07(Boolean attendanced07) {
        this.attendanced07 = attendanced07;
    }

    public Float getWorkingHours07() {
        return workingHours07;
    }

    public void setWorkingHours07(Float workingHours07) {
        this.workingHours07 = workingHours07;
    }

    public Byte getLeave07() {
        return leave07;
    }

    public void setLeave07(Byte leave07) {
        this.leave07 = leave07;
    }

    public Float getLeaveDuration07() {
        return leaveDuration07;
    }

    public void setLeaveDuration07(Float leaveDuration07) {
        this.leaveDuration07 = leaveDuration07;
    }

    public Byte getTravel07() {
        return travel07;
    }

    public void setTravel07(Byte travel07) {
        this.travel07 = travel07;
    }

    public Boolean getLate07() {
        return late07;
    }

    public void setLate07(Boolean late07) {
        this.late07 = late07;
    }

    public Boolean getLeaveEarly07() {
        return leaveEarly07;
    }

    public void setLeaveEarly07(Boolean leaveEarly07) {
        this.leaveEarly07 = leaveEarly07;
    }

    public Byte getOutside07() {
        return outside07;
    }

    public void setOutside07(Byte outside07) {
        this.outside07 = outside07;
    }

    public Byte getAbsenteeism07() {
        return absenteeism07;
    }

    public void setAbsenteeism07(Byte absenteeism07) {
        this.absenteeism07 = absenteeism07;
    }

    public Byte getOvertime07() {
        return overtime07;
    }

    public void setOvertime07(Byte overtime07) {
        this.overtime07 = overtime07;
    }

    public Float getOvertimeDuration07() {
        return overtimeDuration07;
    }

    public void setOvertimeDuration07(Float overtimeDuration07) {
        this.overtimeDuration07 = overtimeDuration07;
    }

    public Boolean getNoNeed08() {
        return noNeed08;
    }

    public void setNoNeed08(Boolean noNeed08) {
        this.noNeed08 = noNeed08;
    }

    public Boolean getattendanced08() {
        return attendanced08;
    }

    public void setattendanced08(Boolean attendanced08) {
        this.attendanced08 = attendanced08;
    }

    public Float getWorkingHours08() {
        return workingHours08;
    }

    public void setWorkingHours08(Float workingHours08) {
        this.workingHours08 = workingHours08;
    }

    public Byte getLeave08() {
        return leave08;
    }

    public void setLeave08(Byte leave08) {
        this.leave08 = leave08;
    }

    public Float getLeaveDuration08() {
        return leaveDuration08;
    }

    public void setLeaveDuration08(Float leaveDuration08) {
        this.leaveDuration08 = leaveDuration08;
    }

    public Byte getTravel08() {
        return travel08;
    }

    public void setTravel08(Byte travel08) {
        this.travel08 = travel08;
    }

    public Boolean getLate08() {
        return late08;
    }

    public void setLate08(Boolean late08) {
        this.late08 = late08;
    }

    public Boolean getLeaveEarly08() {
        return leaveEarly08;
    }

    public void setLeaveEarly08(Boolean leaveEarly08) {
        this.leaveEarly08 = leaveEarly08;
    }

    public Byte getOutside08() {
        return outside08;
    }

    public void setOutside08(Byte outside08) {
        this.outside08 = outside08;
    }

    public Byte getAbsenteeism08() {
        return absenteeism08;
    }

    public void setAbsenteeism08(Byte absenteeism08) {
        this.absenteeism08 = absenteeism08;
    }

    public Byte getOvertime08() {
        return overtime08;
    }

    public void setOvertime08(Byte overtime08) {
        this.overtime08 = overtime08;
    }

    public Float getOvertimeDuration08() {
        return overtimeDuration08;
    }

    public void setOvertimeDuration08(Float overtimeDuration08) {
        this.overtimeDuration08 = overtimeDuration08;
    }

    public Boolean getNoNeed09() {
        return noNeed09;
    }

    public void setNoNeed09(Boolean noNeed09) {
        this.noNeed09 = noNeed09;
    }

    public Boolean getattendanced09() {
        return attendanced09;
    }

    public void setattendanced09(Boolean attendanced09) {
        this.attendanced09 = attendanced09;
    }

    public Float getWorkingHours09() {
        return workingHours09;
    }

    public void setWorkingHours09(Float workingHours09) {
        this.workingHours09 = workingHours09;
    }

    public Byte getLeave09() {
        return leave09;
    }

    public void setLeave09(Byte leave09) {
        this.leave09 = leave09;
    }

    public Float getLeaveDuration09() {
        return leaveDuration09;
    }

    public void setLeaveDuration09(Float leaveDuration09) {
        this.leaveDuration09 = leaveDuration09;
    }

    public Byte getTravel09() {
        return travel09;
    }

    public void setTravel09(Byte travel09) {
        this.travel09 = travel09;
    }

    public Boolean getLate09() {
        return late09;
    }

    public void setLate09(Boolean late09) {
        this.late09 = late09;
    }

    public Boolean getLeaveEarly09() {
        return leaveEarly09;
    }

    public void setLeaveEarly09(Boolean leaveEarly09) {
        this.leaveEarly09 = leaveEarly09;
    }

    public Byte getOutside09() {
        return outside09;
    }

    public void setOutside09(Byte outside09) {
        this.outside09 = outside09;
    }

    public Byte getAbsenteeism09() {
        return absenteeism09;
    }

    public void setAbsenteeism09(Byte absenteeism09) {
        this.absenteeism09 = absenteeism09;
    }

    public Byte getOvertime09() {
        return overtime09;
    }

    public void setOvertime09(Byte overtime09) {
        this.overtime09 = overtime09;
    }

    public Float getOvertimeDuration09() {
        return overtimeDuration09;
    }

    public void setOvertimeDuration09(Float overtimeDuration09) {
        this.overtimeDuration09 = overtimeDuration09;
    }

    public Boolean getNoNeed10() {
        return noNeed10;
    }

    public void setNoNeed10(Boolean noNeed10) {
        this.noNeed10 = noNeed10;
    }

    public Boolean getattendanced10() {
        return attendanced10;
    }

    public void setattendanced10(Boolean attendanced10) {
        this.attendanced10 = attendanced10;
    }

    public Float getWorkingHours10() {
        return workingHours10;
    }

    public void setWorkingHours10(Float workingHours10) {
        this.workingHours10 = workingHours10;
    }

    public Byte getLeave10() {
        return leave10;
    }

    public void setLeave10(Byte leave10) {
        this.leave10 = leave10;
    }

    public Float getLeaveDuration10() {
        return leaveDuration10;
    }

    public void setLeaveDuration10(Float leaveDuration10) {
        this.leaveDuration10 = leaveDuration10;
    }

    public Byte getTravel10() {
        return travel10;
    }

    public void setTravel10(Byte travel10) {
        this.travel10 = travel10;
    }

    public Boolean getLate10() {
        return late10;
    }

    public void setLate10(Boolean late10) {
        this.late10 = late10;
    }

    public Boolean getLeaveEarly10() {
        return leaveEarly10;
    }

    public void setLeaveEarly10(Boolean leaveEarly10) {
        this.leaveEarly10 = leaveEarly10;
    }

    public Byte getOutside10() {
        return outside10;
    }

    public void setOutside10(Byte outside10) {
        this.outside10 = outside10;
    }

    public Byte getAbsenteeism10() {
        return absenteeism10;
    }

    public void setAbsenteeism10(Byte absenteeism10) {
        this.absenteeism10 = absenteeism10;
    }

    public Byte getOvertime10() {
        return overtime10;
    }

    public void setOvertime10(Byte overtime10) {
        this.overtime10 = overtime10;
    }

    public Float getOvertimeDuration10() {
        return overtimeDuration10;
    }

    public void setOvertimeDuration10(Float overtimeDuration10) {
        this.overtimeDuration10 = overtimeDuration10;
    }

    public Boolean getNoNeed11() {
        return noNeed11;
    }

    public void setNoNeed11(Boolean noNeed11) {
        this.noNeed11 = noNeed11;
    }

    public Boolean getattendanced11() {
        return attendanced11;
    }

    public void setattendanced11(Boolean attendanced11) {
        this.attendanced11 = attendanced11;
    }

    public Float getWorkingHours11() {
        return workingHours11;
    }

    public void setWorkingHours11(Float workingHours11) {
        this.workingHours11 = workingHours11;
    }

    public Byte getLeave11() {
        return leave11;
    }

    public void setLeave11(Byte leave11) {
        this.leave11 = leave11;
    }

    public Float getLeaveDuration11() {
        return leaveDuration11;
    }

    public void setLeaveDuration11(Float leaveDuration11) {
        this.leaveDuration11 = leaveDuration11;
    }

    public Byte getTravel11() {
        return travel11;
    }

    public void setTravel11(Byte travel11) {
        this.travel11 = travel11;
    }

    public Boolean getLate11() {
        return late11;
    }

    public void setLate11(Boolean late11) {
        this.late11 = late11;
    }

    public Boolean getLeaveEarly11() {
        return leaveEarly11;
    }

    public void setLeaveEarly11(Boolean leaveEarly11) {
        this.leaveEarly11 = leaveEarly11;
    }

    public Byte getOutside11() {
        return outside11;
    }

    public void setOutside11(Byte outside11) {
        this.outside11 = outside11;
    }

    public Byte getAbsenteeism11() {
        return absenteeism11;
    }

    public void setAbsenteeism11(Byte absenteeism11) {
        this.absenteeism11 = absenteeism11;
    }

    public Byte getOvertime11() {
        return overtime11;
    }

    public void setOvertime11(Byte overtime11) {
        this.overtime11 = overtime11;
    }

    public Float getOvertimeDuration11() {
        return overtimeDuration11;
    }

    public void setOvertimeDuration11(Float overtimeDuration11) {
        this.overtimeDuration11 = overtimeDuration11;
    }

    public Boolean getNoNeed12() {
        return noNeed12;
    }

    public void setNoNeed12(Boolean noNeed12) {
        this.noNeed12 = noNeed12;
    }

    public Boolean getattendanced12() {
        return attendanced12;
    }

    public void setattendanced12(Boolean attendanced12) {
        this.attendanced12 = attendanced12;
    }

    public Float getWorkingHours12() {
        return workingHours12;
    }

    public void setWorkingHours12(Float workingHours12) {
        this.workingHours12 = workingHours12;
    }

    public Byte getLeave12() {
        return leave12;
    }

    public void setLeave12(Byte leave12) {
        this.leave12 = leave12;
    }

    public Float getLeaveDuration12() {
        return leaveDuration12;
    }

    public void setLeaveDuration12(Float leaveDuration12) {
        this.leaveDuration12 = leaveDuration12;
    }

    public Byte getTravel12() {
        return travel12;
    }

    public void setTravel12(Byte travel12) {
        this.travel12 = travel12;
    }

    public Boolean getLate12() {
        return late12;
    }

    public void setLate12(Boolean late12) {
        this.late12 = late12;
    }

    public Boolean getLeaveEarly12() {
        return leaveEarly12;
    }

    public void setLeaveEarly12(Boolean leaveEarly12) {
        this.leaveEarly12 = leaveEarly12;
    }

    public Byte getOutside12() {
        return outside12;
    }

    public void setOutside12(Byte outside12) {
        this.outside12 = outside12;
    }

    public Byte getAbsenteeism12() {
        return absenteeism12;
    }

    public void setAbsenteeism12(Byte absenteeism12) {
        this.absenteeism12 = absenteeism12;
    }

    public Byte getOvertime12() {
        return overtime12;
    }

    public void setOvertime12(Byte overtime12) {
        this.overtime12 = overtime12;
    }

    public Float getOvertimeDuration12() {
        return overtimeDuration12;
    }

    public void setOvertimeDuration12(Float overtimeDuration12) {
        this.overtimeDuration12 = overtimeDuration12;
    }

    public Boolean getNoNeed13() {
        return noNeed13;
    }

    public void setNoNeed13(Boolean noNeed13) {
        this.noNeed13 = noNeed13;
    }

    public Boolean getattendanced13() {
        return attendanced13;
    }

    public void setattendanced13(Boolean attendanced13) {
        this.attendanced13 = attendanced13;
    }

    public Float getWorkingHours13() {
        return workingHours13;
    }

    public void setWorkingHours13(Float workingHours13) {
        this.workingHours13 = workingHours13;
    }

    public Byte getLeave13() {
        return leave13;
    }

    public void setLeave13(Byte leave13) {
        this.leave13 = leave13;
    }

    public Float getLeaveDuration13() {
        return leaveDuration13;
    }

    public void setLeaveDuration13(Float leaveDuration13) {
        this.leaveDuration13 = leaveDuration13;
    }

    public Byte getTravel13() {
        return travel13;
    }

    public void setTravel13(Byte travel13) {
        this.travel13 = travel13;
    }

    public Boolean getLate13() {
        return late13;
    }

    public void setLate13(Boolean late13) {
        this.late13 = late13;
    }

    public Boolean getLeaveEarly13() {
        return leaveEarly13;
    }

    public void setLeaveEarly13(Boolean leaveEarly13) {
        this.leaveEarly13 = leaveEarly13;
    }

    public Byte getOutside13() {
        return outside13;
    }

    public void setOutside13(Byte outside13) {
        this.outside13 = outside13;
    }

    public Byte getAbsenteeism13() {
        return absenteeism13;
    }

    public void setAbsenteeism13(Byte absenteeism13) {
        this.absenteeism13 = absenteeism13;
    }

    public Byte getOvertime13() {
        return overtime13;
    }

    public void setOvertime13(Byte overtime13) {
        this.overtime13 = overtime13;
    }

    public Float getOvertimeDuration13() {
        return overtimeDuration13;
    }

    public void setOvertimeDuration13(Float overtimeDuration13) {
        this.overtimeDuration13 = overtimeDuration13;
    }

    public Boolean getNoNeed14() {
        return noNeed14;
    }

    public void setNoNeed14(Boolean noNeed14) {
        this.noNeed14 = noNeed14;
    }

    public Boolean getattendanced14() {
        return attendanced14;
    }

    public void setattendanced14(Boolean attendanced14) {
        this.attendanced14 = attendanced14;
    }

    public Float getWorkingHours14() {
        return workingHours14;
    }

    public void setWorkingHours14(Float workingHours14) {
        this.workingHours14 = workingHours14;
    }

    public Byte getLeave14() {
        return leave14;
    }

    public void setLeave14(Byte leave14) {
        this.leave14 = leave14;
    }

    public Float getLeaveDuration14() {
        return leaveDuration14;
    }

    public void setLeaveDuration14(Float leaveDuration14) {
        this.leaveDuration14 = leaveDuration14;
    }

    public Byte getTravel14() {
        return travel14;
    }

    public void setTravel14(Byte travel14) {
        this.travel14 = travel14;
    }

    public Boolean getLate14() {
        return late14;
    }

    public void setLate14(Boolean late14) {
        this.late14 = late14;
    }

    public Boolean getLeaveEarly14() {
        return leaveEarly14;
    }

    public void setLeaveEarly14(Boolean leaveEarly14) {
        this.leaveEarly14 = leaveEarly14;
    }

    public Byte getOutside14() {
        return outside14;
    }

    public void setOutside14(Byte outside14) {
        this.outside14 = outside14;
    }

    public Byte getAbsenteeism14() {
        return absenteeism14;
    }

    public void setAbsenteeism14(Byte absenteeism14) {
        this.absenteeism14 = absenteeism14;
    }

    public Byte getOvertime14() {
        return overtime14;
    }

    public void setOvertime14(Byte overtime14) {
        this.overtime14 = overtime14;
    }

    public Float getOvertimeDuration14() {
        return overtimeDuration14;
    }

    public void setOvertimeDuration14(Float overtimeDuration14) {
        this.overtimeDuration14 = overtimeDuration14;
    }

    public Boolean getNoNeed15() {
        return noNeed15;
    }

    public void setNoNeed15(Boolean noNeed15) {
        this.noNeed15 = noNeed15;
    }

    public Boolean getattendanced15() {
        return attendanced15;
    }

    public void setattendanced15(Boolean attendanced15) {
        this.attendanced15 = attendanced15;
    }

    public Float getWorkingHours15() {
        return workingHours15;
    }

    public void setWorkingHours15(Float workingHours15) {
        this.workingHours15 = workingHours15;
    }

    public Byte getLeave15() {
        return leave15;
    }

    public void setLeave15(Byte leave15) {
        this.leave15 = leave15;
    }

    public Float getLeaveDuration15() {
        return leaveDuration15;
    }

    public void setLeaveDuration15(Float leaveDuration15) {
        this.leaveDuration15 = leaveDuration15;
    }

    public Byte getTravel15() {
        return travel15;
    }

    public void setTravel15(Byte travel15) {
        this.travel15 = travel15;
    }

    public Boolean getLate15() {
        return late15;
    }

    public void setLate15(Boolean late15) {
        this.late15 = late15;
    }

    public Boolean getLeaveEarly15() {
        return leaveEarly15;
    }

    public void setLeaveEarly15(Boolean leaveEarly15) {
        this.leaveEarly15 = leaveEarly15;
    }

    public Byte getOutside15() {
        return outside15;
    }

    public void setOutside15(Byte outside15) {
        this.outside15 = outside15;
    }

    public Byte getAbsenteeism15() {
        return absenteeism15;
    }

    public void setAbsenteeism15(Byte absenteeism15) {
        this.absenteeism15 = absenteeism15;
    }

    public Byte getOvertime15() {
        return overtime15;
    }

    public void setOvertime15(Byte overtime15) {
        this.overtime15 = overtime15;
    }

    public Float getOvertimeDuration15() {
        return overtimeDuration15;
    }

    public void setOvertimeDuration15(Float overtimeDuration15) {
        this.overtimeDuration15 = overtimeDuration15;
    }

    public Boolean getNoNeed16() {
        return noNeed16;
    }

    public void setNoNeed16(Boolean noNeed16) {
        this.noNeed16 = noNeed16;
    }

    public Boolean getattendanced16() {
        return attendanced16;
    }

    public void setattendanced16(Boolean attendanced16) {
        this.attendanced16 = attendanced16;
    }

    public Float getWorkingHours16() {
        return workingHours16;
    }

    public void setWorkingHours16(Float workingHours16) {
        this.workingHours16 = workingHours16;
    }

    public Byte getLeave16() {
        return leave16;
    }

    public void setLeave16(Byte leave16) {
        this.leave16 = leave16;
    }

    public Float getLeaveDuration16() {
        return leaveDuration16;
    }

    public void setLeaveDuration16(Float leaveDuration16) {
        this.leaveDuration16 = leaveDuration16;
    }

    public Byte getTravel16() {
        return travel16;
    }

    public void setTravel16(Byte travel16) {
        this.travel16 = travel16;
    }

    public Boolean getLate16() {
        return late16;
    }

    public void setLate16(Boolean late16) {
        this.late16 = late16;
    }

    public Boolean getLeaveEarly16() {
        return leaveEarly16;
    }

    public void setLeaveEarly16(Boolean leaveEarly16) {
        this.leaveEarly16 = leaveEarly16;
    }

    public Byte getOutside16() {
        return outside16;
    }

    public void setOutside16(Byte outside16) {
        this.outside16 = outside16;
    }

    public Byte getAbsenteeism16() {
        return absenteeism16;
    }

    public void setAbsenteeism16(Byte absenteeism16) {
        this.absenteeism16 = absenteeism16;
    }

    public Byte getOvertime16() {
        return overtime16;
    }

    public void setOvertime16(Byte overtime16) {
        this.overtime16 = overtime16;
    }

    public Float getOvertimeDuration16() {
        return overtimeDuration16;
    }

    public void setOvertimeDuration16(Float overtimeDuration16) {
        this.overtimeDuration16 = overtimeDuration16;
    }

    public Boolean getNoNeed17() {
        return noNeed17;
    }

    public void setNoNeed17(Boolean noNeed17) {
        this.noNeed17 = noNeed17;
    }

    public Boolean getattendanced17() {
        return attendanced17;
    }

    public void setattendanced17(Boolean attendanced17) {
        this.attendanced17 = attendanced17;
    }

    public Float getWorkingHours17() {
        return workingHours17;
    }

    public void setWorkingHours17(Float workingHours17) {
        this.workingHours17 = workingHours17;
    }

    public Byte getLeave17() {
        return leave17;
    }

    public void setLeave17(Byte leave17) {
        this.leave17 = leave17;
    }

    public Float getLeaveDuration17() {
        return leaveDuration17;
    }

    public void setLeaveDuration17(Float leaveDuration17) {
        this.leaveDuration17 = leaveDuration17;
    }

    public Byte getTravel17() {
        return travel17;
    }

    public void setTravel17(Byte travel17) {
        this.travel17 = travel17;
    }

    public Boolean getLate17() {
        return late17;
    }

    public void setLate17(Boolean late17) {
        this.late17 = late17;
    }

    public Boolean getLeaveEarly17() {
        return leaveEarly17;
    }

    public void setLeaveEarly17(Boolean leaveEarly17) {
        this.leaveEarly17 = leaveEarly17;
    }

    public Byte getOutside17() {
        return outside17;
    }

    public void setOutside17(Byte outside17) {
        this.outside17 = outside17;
    }

    public Byte getAbsenteeism17() {
        return absenteeism17;
    }

    public void setAbsenteeism17(Byte absenteeism17) {
        this.absenteeism17 = absenteeism17;
    }

    public Byte getOvertime17() {
        return overtime17;
    }

    public void setOvertime17(Byte overtime17) {
        this.overtime17 = overtime17;
    }

    public Float getOvertimeDuration17() {
        return overtimeDuration17;
    }

    public void setOvertimeDuration17(Float overtimeDuration17) {
        this.overtimeDuration17 = overtimeDuration17;
    }

    public Boolean getNoNeed18() {
        return noNeed18;
    }

    public void setNoNeed18(Boolean noNeed18) {
        this.noNeed18 = noNeed18;
    }

    public Boolean getattendanced18() {
        return attendanced18;
    }

    public void setattendanced18(Boolean attendanced18) {
        this.attendanced18 = attendanced18;
    }

    public Float getWorkingHours18() {
        return workingHours18;
    }

    public void setWorkingHours18(Float workingHours18) {
        this.workingHours18 = workingHours18;
    }

    public Byte getLeave18() {
        return leave18;
    }

    public void setLeave18(Byte leave18) {
        this.leave18 = leave18;
    }

    public Float getLeaveDuration18() {
        return leaveDuration18;
    }

    public void setLeaveDuration18(Float leaveDuration18) {
        this.leaveDuration18 = leaveDuration18;
    }

    public Byte getTravel18() {
        return travel18;
    }

    public void setTravel18(Byte travel18) {
        this.travel18 = travel18;
    }

    public Boolean getLate18() {
        return late18;
    }

    public void setLate18(Boolean late18) {
        this.late18 = late18;
    }

    public Boolean getLeaveEarly18() {
        return leaveEarly18;
    }

    public void setLeaveEarly18(Boolean leaveEarly18) {
        this.leaveEarly18 = leaveEarly18;
    }

    public Byte getOutside18() {
        return outside18;
    }

    public void setOutside18(Byte outside18) {
        this.outside18 = outside18;
    }

    public Byte getAbsenteeism18() {
        return absenteeism18;
    }

    public void setAbsenteeism18(Byte absenteeism18) {
        this.absenteeism18 = absenteeism18;
    }

    public Byte getOvertime18() {
        return overtime18;
    }

    public void setOvertime18(Byte overtime18) {
        this.overtime18 = overtime18;
    }

    public Float getOvertimeDuration18() {
        return overtimeDuration18;
    }

    public void setOvertimeDuration18(Float overtimeDuration18) {
        this.overtimeDuration18 = overtimeDuration18;
    }

    public Boolean getNoNeed19() {
        return noNeed19;
    }

    public void setNoNeed19(Boolean noNeed19) {
        this.noNeed19 = noNeed19;
    }

    public Boolean getattendanced19() {
        return attendanced19;
    }

    public void setattendanced19(Boolean attendanced19) {
        this.attendanced19 = attendanced19;
    }

    public Float getWorkingHours19() {
        return workingHours19;
    }

    public void setWorkingHours19(Float workingHours19) {
        this.workingHours19 = workingHours19;
    }

    public Byte getLeave19() {
        return leave19;
    }

    public void setLeave19(Byte leave19) {
        this.leave19 = leave19;
    }

    public Float getLeaveDuration19() {
        return leaveDuration19;
    }

    public void setLeaveDuration19(Float leaveDuration19) {
        this.leaveDuration19 = leaveDuration19;
    }

    public Byte getTravel19() {
        return travel19;
    }

    public void setTravel19(Byte travel19) {
        this.travel19 = travel19;
    }

    public Boolean getLate19() {
        return late19;
    }

    public void setLate19(Boolean late19) {
        this.late19 = late19;
    }

    public Boolean getLeaveEarly19() {
        return leaveEarly19;
    }

    public void setLeaveEarly19(Boolean leaveEarly19) {
        this.leaveEarly19 = leaveEarly19;
    }

    public Byte getOutside19() {
        return outside19;
    }

    public void setOutside19(Byte outside19) {
        this.outside19 = outside19;
    }

    public Byte getAbsenteeism19() {
        return absenteeism19;
    }

    public void setAbsenteeism19(Byte absenteeism19) {
        this.absenteeism19 = absenteeism19;
    }

    public Byte getOvertime19() {
        return overtime19;
    }

    public void setOvertime19(Byte overtime19) {
        this.overtime19 = overtime19;
    }

    public Float getOvertimeDuration19() {
        return overtimeDuration19;
    }

    public void setOvertimeDuration19(Float overtimeDuration19) {
        this.overtimeDuration19 = overtimeDuration19;
    }

    public Boolean getNoNeed20() {
        return noNeed20;
    }

    public void setNoNeed20(Boolean noNeed20) {
        this.noNeed20 = noNeed20;
    }

    public Boolean getattendanced20() {
        return attendanced20;
    }

    public void setattendanced20(Boolean attendanced20) {
        this.attendanced20 = attendanced20;
    }

    public Float getWorkingHours20() {
        return workingHours20;
    }

    public void setWorkingHours20(Float workingHours20) {
        this.workingHours20 = workingHours20;
    }

    public Byte getLeave20() {
        return leave20;
    }

    public void setLeave20(Byte leave20) {
        this.leave20 = leave20;
    }

    public Float getLeaveDuration20() {
        return leaveDuration20;
    }

    public void setLeaveDuration20(Float leaveDuration20) {
        this.leaveDuration20 = leaveDuration20;
    }

    public Byte getTravel20() {
        return travel20;
    }

    public void setTravel20(Byte travel20) {
        this.travel20 = travel20;
    }

    public Boolean getLate20() {
        return late20;
    }

    public void setLate20(Boolean late20) {
        this.late20 = late20;
    }

    public Boolean getLeaveEarly20() {
        return leaveEarly20;
    }

    public void setLeaveEarly20(Boolean leaveEarly20) {
        this.leaveEarly20 = leaveEarly20;
    }

    public Byte getOutside20() {
        return outside20;
    }

    public void setOutside20(Byte outside20) {
        this.outside20 = outside20;
    }

    public Byte getAbsenteeism20() {
        return absenteeism20;
    }

    public void setAbsenteeism20(Byte absenteeism20) {
        this.absenteeism20 = absenteeism20;
    }

    public Byte getOvertime20() {
        return overtime20;
    }

    public void setOvertime20(Byte overtime20) {
        this.overtime20 = overtime20;
    }

    public Float getOvertimeDuration20() {
        return overtimeDuration20;
    }

    public void setOvertimeDuration20(Float overtimeDuration20) {
        this.overtimeDuration20 = overtimeDuration20;
    }

    public Boolean getNoNeed21() {
        return noNeed21;
    }

    public void setNoNeed21(Boolean noNeed21) {
        this.noNeed21 = noNeed21;
    }

    public Boolean getattendanced21() {
        return attendanced21;
    }

    public void setattendanced21(Boolean attendanced21) {
        this.attendanced21 = attendanced21;
    }

    public Float getWorkingHours21() {
        return workingHours21;
    }

    public void setWorkingHours21(Float workingHours21) {
        this.workingHours21 = workingHours21;
    }

    public Byte getLeave21() {
        return leave21;
    }

    public void setLeave21(Byte leave21) {
        this.leave21 = leave21;
    }

    public Float getLeaveDuration21() {
        return leaveDuration21;
    }

    public void setLeaveDuration21(Float leaveDuration21) {
        this.leaveDuration21 = leaveDuration21;
    }

    public Byte getTravel21() {
        return travel21;
    }

    public void setTravel21(Byte travel21) {
        this.travel21 = travel21;
    }

    public Boolean getLate21() {
        return late21;
    }

    public void setLate21(Boolean late21) {
        this.late21 = late21;
    }

    public Boolean getLeaveEarly21() {
        return leaveEarly21;
    }

    public void setLeaveEarly21(Boolean leaveEarly21) {
        this.leaveEarly21 = leaveEarly21;
    }

    public Byte getOutside21() {
        return outside21;
    }

    public void setOutside21(Byte outside21) {
        this.outside21 = outside21;
    }

    public Byte getAbsenteeism21() {
        return absenteeism21;
    }

    public void setAbsenteeism21(Byte absenteeism21) {
        this.absenteeism21 = absenteeism21;
    }

    public Byte getOvertime21() {
        return overtime21;
    }

    public void setOvertime21(Byte overtime21) {
        this.overtime21 = overtime21;
    }

    public Float getOvertimeDuration21() {
        return overtimeDuration21;
    }

    public void setOvertimeDuration21(Float overtimeDuration21) {
        this.overtimeDuration21 = overtimeDuration21;
    }

    public Boolean getNoNeed22() {
        return noNeed22;
    }

    public void setNoNeed22(Boolean noNeed22) {
        this.noNeed22 = noNeed22;
    }

    public Boolean getattendanced22() {
        return attendanced22;
    }

    public void setattendanced22(Boolean attendanced22) {
        this.attendanced22 = attendanced22;
    }

    public Float getWorkingHours22() {
        return workingHours22;
    }

    public void setWorkingHours22(Float workingHours22) {
        this.workingHours22 = workingHours22;
    }

    public Byte getLeave22() {
        return leave22;
    }

    public void setLeave22(Byte leave22) {
        this.leave22 = leave22;
    }

    public Float getLeaveDuration22() {
        return leaveDuration22;
    }

    public void setLeaveDuration22(Float leaveDuration22) {
        this.leaveDuration22 = leaveDuration22;
    }

    public Byte getTravel22() {
        return travel22;
    }

    public void setTravel22(Byte travel22) {
        this.travel22 = travel22;
    }

    public Boolean getLate22() {
        return late22;
    }

    public void setLate22(Boolean late22) {
        this.late22 = late22;
    }

    public Boolean getLeaveEarly22() {
        return leaveEarly22;
    }

    public void setLeaveEarly22(Boolean leaveEarly22) {
        this.leaveEarly22 = leaveEarly22;
    }

    public Byte getOutside22() {
        return outside22;
    }

    public void setOutside22(Byte outside22) {
        this.outside22 = outside22;
    }

    public Byte getAbsenteeism22() {
        return absenteeism22;
    }

    public void setAbsenteeism22(Byte absenteeism22) {
        this.absenteeism22 = absenteeism22;
    }

    public Byte getOvertime22() {
        return overtime22;
    }

    public void setOvertime22(Byte overtime22) {
        this.overtime22 = overtime22;
    }

    public Float getOvertimeDuration22() {
        return overtimeDuration22;
    }

    public void setOvertimeDuration22(Float overtimeDuration22) {
        this.overtimeDuration22 = overtimeDuration22;
    }

    public Boolean getNoNeed23() {
        return noNeed23;
    }

    public void setNoNeed23(Boolean noNeed23) {
        this.noNeed23 = noNeed23;
    }

    public Boolean getattendanced23() {
        return attendanced23;
    }

    public void setattendanced23(Boolean attendanced23) {
        this.attendanced23 = attendanced23;
    }

    public Float getWorkingHours23() {
        return workingHours23;
    }

    public void setWorkingHours23(Float workingHours23) {
        this.workingHours23 = workingHours23;
    }

    public Byte getLeave23() {
        return leave23;
    }

    public void setLeave23(Byte leave23) {
        this.leave23 = leave23;
    }

    public Float getLeaveDuration23() {
        return leaveDuration23;
    }

    public void setLeaveDuration23(Float leaveDuration23) {
        this.leaveDuration23 = leaveDuration23;
    }

    public Byte getTravel23() {
        return travel23;
    }

    public void setTravel23(Byte travel23) {
        this.travel23 = travel23;
    }

    public Boolean getLate23() {
        return late23;
    }

    public void setLate23(Boolean late23) {
        this.late23 = late23;
    }

    public Boolean getLeaveEarly23() {
        return leaveEarly23;
    }

    public void setLeaveEarly23(Boolean leaveEarly23) {
        this.leaveEarly23 = leaveEarly23;
    }

    public Byte getOutside23() {
        return outside23;
    }

    public void setOutside23(Byte outside23) {
        this.outside23 = outside23;
    }

    public Byte getAbsenteeism23() {
        return absenteeism23;
    }

    public void setAbsenteeism23(Byte absenteeism23) {
        this.absenteeism23 = absenteeism23;
    }

    public Byte getOvertime23() {
        return overtime23;
    }

    public void setOvertime23(Byte overtime23) {
        this.overtime23 = overtime23;
    }

    public Float getOvertimeDuration23() {
        return overtimeDuration23;
    }

    public void setOvertimeDuration23(Float overtimeDuration23) {
        this.overtimeDuration23 = overtimeDuration23;
    }

    public Boolean getNoNeed24() {
        return noNeed24;
    }

    public void setNoNeed24(Boolean noNeed24) {
        this.noNeed24 = noNeed24;
    }

    public Boolean getattendanced24() {
        return attendanced24;
    }

    public void setattendanced24(Boolean attendanced24) {
        this.attendanced24 = attendanced24;
    }

    public Float getWorkingHours24() {
        return workingHours24;
    }

    public void setWorkingHours24(Float workingHours24) {
        this.workingHours24 = workingHours24;
    }

    public Byte getLeave24() {
        return leave24;
    }

    public void setLeave24(Byte leave24) {
        this.leave24 = leave24;
    }

    public Float getLeaveDuration24() {
        return leaveDuration24;
    }

    public void setLeaveDuration24(Float leaveDuration24) {
        this.leaveDuration24 = leaveDuration24;
    }

    public Byte getTravel24() {
        return travel24;
    }

    public void setTravel24(Byte travel24) {
        this.travel24 = travel24;
    }

    public Boolean getLate24() {
        return late24;
    }

    public void setLate24(Boolean late24) {
        this.late24 = late24;
    }

    public Boolean getLeaveEarly24() {
        return leaveEarly24;
    }

    public void setLeaveEarly24(Boolean leaveEarly24) {
        this.leaveEarly24 = leaveEarly24;
    }

    public Byte getOutside24() {
        return outside24;
    }

    public void setOutside24(Byte outside24) {
        this.outside24 = outside24;
    }

    public Byte getAbsenteeism24() {
        return absenteeism24;
    }

    public void setAbsenteeism24(Byte absenteeism24) {
        this.absenteeism24 = absenteeism24;
    }

    public Byte getOvertime24() {
        return overtime24;
    }

    public void setOvertime24(Byte overtime24) {
        this.overtime24 = overtime24;
    }

    public Float getOvertimeDuration24() {
        return overtimeDuration24;
    }

    public void setOvertimeDuration24(Float overtimeDuration24) {
        this.overtimeDuration24 = overtimeDuration24;
    }

    public Boolean getNoNeed25() {
        return noNeed25;
    }

    public void setNoNeed25(Boolean noNeed25) {
        this.noNeed25 = noNeed25;
    }

    public Boolean getattendanced25() {
        return attendanced25;
    }

    public void setattendanced25(Boolean attendanced25) {
        this.attendanced25 = attendanced25;
    }

    public Float getWorkingHours25() {
        return workingHours25;
    }

    public void setWorkingHours25(Float workingHours25) {
        this.workingHours25 = workingHours25;
    }

    public Byte getLeave25() {
        return leave25;
    }

    public void setLeave25(Byte leave25) {
        this.leave25 = leave25;
    }

    public Float getLeaveDuration25() {
        return leaveDuration25;
    }

    public void setLeaveDuration25(Float leaveDuration25) {
        this.leaveDuration25 = leaveDuration25;
    }

    public Byte getTravel25() {
        return travel25;
    }

    public void setTravel25(Byte travel25) {
        this.travel25 = travel25;
    }

    public Boolean getLate25() {
        return late25;
    }

    public void setLate25(Boolean late25) {
        this.late25 = late25;
    }

    public Boolean getLeaveEarly25() {
        return leaveEarly25;
    }

    public void setLeaveEarly25(Boolean leaveEarly25) {
        this.leaveEarly25 = leaveEarly25;
    }

    public Byte getOutside25() {
        return outside25;
    }

    public void setOutside25(Byte outside25) {
        this.outside25 = outside25;
    }

    public Byte getAbsenteeism25() {
        return absenteeism25;
    }

    public void setAbsenteeism25(Byte absenteeism25) {
        this.absenteeism25 = absenteeism25;
    }

    public Byte getOvertime25() {
        return overtime25;
    }

    public void setOvertime25(Byte overtime25) {
        this.overtime25 = overtime25;
    }

    public Float getOvertimeDuration25() {
        return overtimeDuration25;
    }

    public void setOvertimeDuration25(Float overtimeDuration25) {
        this.overtimeDuration25 = overtimeDuration25;
    }

    public Boolean getNoNeed26() {
        return noNeed26;
    }

    public void setNoNeed26(Boolean noNeed26) {
        this.noNeed26 = noNeed26;
    }

    public Boolean getattendanced26() {
        return attendanced26;
    }

    public void setattendanced26(Boolean attendanced26) {
        this.attendanced26 = attendanced26;
    }

    public Float getWorkingHours26() {
        return workingHours26;
    }

    public void setWorkingHours26(Float workingHours26) {
        this.workingHours26 = workingHours26;
    }

    public Byte getLeave26() {
        return leave26;
    }

    public void setLeave26(Byte leave26) {
        this.leave26 = leave26;
    }

    public Float getLeaveDuration26() {
        return leaveDuration26;
    }

    public void setLeaveDuration26(Float leaveDuration26) {
        this.leaveDuration26 = leaveDuration26;
    }

    public Byte getTravel26() {
        return travel26;
    }

    public void setTravel26(Byte travel26) {
        this.travel26 = travel26;
    }

    public Boolean getLate26() {
        return late26;
    }

    public void setLate26(Boolean late26) {
        this.late26 = late26;
    }

    public Boolean getLeaveEarly26() {
        return leaveEarly26;
    }

    public void setLeaveEarly26(Boolean leaveEarly26) {
        this.leaveEarly26 = leaveEarly26;
    }

    public Byte getOutside26() {
        return outside26;
    }

    public void setOutside26(Byte outside26) {
        this.outside26 = outside26;
    }

    public Byte getAbsenteeism26() {
        return absenteeism26;
    }

    public void setAbsenteeism26(Byte absenteeism26) {
        this.absenteeism26 = absenteeism26;
    }

    public Byte getOvertime26() {
        return overtime26;
    }

    public void setOvertime26(Byte overtime26) {
        this.overtime26 = overtime26;
    }

    public Float getOvertimeDuration26() {
        return overtimeDuration26;
    }

    public void setOvertimeDuration26(Float overtimeDuration26) {
        this.overtimeDuration26 = overtimeDuration26;
    }

    public Boolean getNoNeed27() {
        return noNeed27;
    }

    public void setNoNeed27(Boolean noNeed27) {
        this.noNeed27 = noNeed27;
    }

    public Boolean getattendanced27() {
        return attendanced27;
    }

    public void setattendanced27(Boolean attendanced27) {
        this.attendanced27 = attendanced27;
    }

    public Float getWorkingHours27() {
        return workingHours27;
    }

    public void setWorkingHours27(Float workingHours27) {
        this.workingHours27 = workingHours27;
    }

    public Byte getLeave27() {
        return leave27;
    }

    public void setLeave27(Byte leave27) {
        this.leave27 = leave27;
    }

    public Float getLeaveDuration27() {
        return leaveDuration27;
    }

    public void setLeaveDuration27(Float leaveDuration27) {
        this.leaveDuration27 = leaveDuration27;
    }

    public Byte getTravel27() {
        return travel27;
    }

    public void setTravel27(Byte travel27) {
        this.travel27 = travel27;
    }

    public Boolean getLate27() {
        return late27;
    }

    public void setLate27(Boolean late27) {
        this.late27 = late27;
    }

    public Boolean getLeaveEarly27() {
        return leaveEarly27;
    }

    public void setLeaveEarly27(Boolean leaveEarly27) {
        this.leaveEarly27 = leaveEarly27;
    }

    public Byte getOutside27() {
        return outside27;
    }

    public void setOutside27(Byte outside27) {
        this.outside27 = outside27;
    }

    public Byte getAbsenteeism27() {
        return absenteeism27;
    }

    public void setAbsenteeism27(Byte absenteeism27) {
        this.absenteeism27 = absenteeism27;
    }

    public Byte getOvertime27() {
        return overtime27;
    }

    public void setOvertime27(Byte overtime27) {
        this.overtime27 = overtime27;
    }

    public Float getOvertimeDuration27() {
        return overtimeDuration27;
    }

    public void setOvertimeDuration27(Float overtimeDuration27) {
        this.overtimeDuration27 = overtimeDuration27;
    }

    public Boolean getNoNeed28() {
        return noNeed28;
    }

    public void setNoNeed28(Boolean noNeed28) {
        this.noNeed28 = noNeed28;
    }

    public Boolean getattendanced28() {
        return attendanced28;
    }

    public void setattendanced28(Boolean attendanced28) {
        this.attendanced28 = attendanced28;
    }

    public Float getWorkingHours28() {
        return workingHours28;
    }

    public void setWorkingHours28(Float workingHours28) {
        this.workingHours28 = workingHours28;
    }

    public Byte getLeave28() {
        return leave28;
    }

    public void setLeave28(Byte leave28) {
        this.leave28 = leave28;
    }

    public Float getLeaveDuration28() {
        return leaveDuration28;
    }

    public void setLeaveDuration28(Float leaveDuration28) {
        this.leaveDuration28 = leaveDuration28;
    }

    public Byte getTravel28() {
        return travel28;
    }

    public void setTravel28(Byte travel28) {
        this.travel28 = travel28;
    }

    public Boolean getLate28() {
        return late28;
    }

    public void setLate28(Boolean late28) {
        this.late28 = late28;
    }

    public Boolean getLeaveEarly28() {
        return leaveEarly28;
    }

    public void setLeaveEarly28(Boolean leaveEarly28) {
        this.leaveEarly28 = leaveEarly28;
    }

    public Byte getOutside28() {
        return outside28;
    }

    public void setOutside28(Byte outside28) {
        this.outside28 = outside28;
    }

    public Byte getAbsenteeism28() {
        return absenteeism28;
    }

    public void setAbsenteeism28(Byte absenteeism28) {
        this.absenteeism28 = absenteeism28;
    }

    public Byte getOvertime28() {
        return overtime28;
    }

    public void setOvertime28(Byte overtime28) {
        this.overtime28 = overtime28;
    }

    public Float getOvertimeDuration28() {
        return overtimeDuration28;
    }

    public void setOvertimeDuration28(Float overtimeDuration28) {
        this.overtimeDuration28 = overtimeDuration28;
    }

    public Boolean getNoNeed29() {
        return noNeed29;
    }

    public void setNoNeed29(Boolean noNeed29) {
        this.noNeed29 = noNeed29;
    }

    public Boolean getattendanced29() {
        return attendanced29;
    }

    public void setattendanced29(Boolean attendanced29) {
        this.attendanced29 = attendanced29;
    }

    public Float getWorkingHours29() {
        return workingHours29;
    }

    public void setWorkingHours29(Float workingHours29) {
        this.workingHours29 = workingHours29;
    }

    public Byte getLeave29() {
        return leave29;
    }

    public void setLeave29(Byte leave29) {
        this.leave29 = leave29;
    }

    public Float getLeaveDuration29() {
        return leaveDuration29;
    }

    public void setLeaveDuration29(Float leaveDuration29) {
        this.leaveDuration29 = leaveDuration29;
    }

    public Byte getTravel29() {
        return travel29;
    }

    public void setTravel29(Byte travel29) {
        this.travel29 = travel29;
    }

    public Boolean getLate29() {
        return late29;
    }

    public void setLate29(Boolean late29) {
        this.late29 = late29;
    }

    public Boolean getLeaveEarly29() {
        return leaveEarly29;
    }

    public void setLeaveEarly29(Boolean leaveEarly29) {
        this.leaveEarly29 = leaveEarly29;
    }

    public Byte getOutside29() {
        return outside29;
    }

    public void setOutside29(Byte outside29) {
        this.outside29 = outside29;
    }

    public Byte getAbsenteeism29() {
        return absenteeism29;
    }

    public void setAbsenteeism29(Byte absenteeism29) {
        this.absenteeism29 = absenteeism29;
    }

    public Byte getOvertime29() {
        return overtime29;
    }

    public void setOvertime29(Byte overtime29) {
        this.overtime29 = overtime29;
    }

    public Float getOvertimeDuration29() {
        return overtimeDuration29;
    }

    public void setOvertimeDuration29(Float overtimeDuration29) {
        this.overtimeDuration29 = overtimeDuration29;
    }

    public Boolean getNoNeed30() {
        return noNeed30;
    }

    public void setNoNeed30(Boolean noNeed30) {
        this.noNeed30 = noNeed30;
    }

    public Boolean getattendanced30() {
        return attendanced30;
    }

    public void setattendanced30(Boolean attendanced30) {
        this.attendanced30 = attendanced30;
    }

    public Float getWorkingHours30() {
        return workingHours30;
    }

    public void setWorkingHours30(Float workingHours30) {
        this.workingHours30 = workingHours30;
    }

    public Byte getLeave30() {
        return leave30;
    }

    public void setLeave30(Byte leave30) {
        this.leave30 = leave30;
    }

    public Float getLeaveDuration30() {
        return leaveDuration30;
    }

    public void setLeaveDuration30(Float leaveDuration30) {
        this.leaveDuration30 = leaveDuration30;
    }

    public Byte getTravel30() {
        return travel30;
    }

    public void setTravel30(Byte travel30) {
        this.travel30 = travel30;
    }

    public Boolean getLate30() {
        return late30;
    }

    public void setLate30(Boolean late30) {
        this.late30 = late30;
    }

    public Boolean getLeaveEarly30() {
        return leaveEarly30;
    }

    public void setLeaveEarly30(Boolean leaveEarly30) {
        this.leaveEarly30 = leaveEarly30;
    }

    public Byte getOutside30() {
        return outside30;
    }

    public void setOutside30(Byte outside30) {
        this.outside30 = outside30;
    }

    public Byte getAbsenteeism30() {
        return absenteeism30;
    }

    public void setAbsenteeism30(Byte absenteeism30) {
        this.absenteeism30 = absenteeism30;
    }

    public Byte getOvertime30() {
        return overtime30;
    }

    public void setOvertime30(Byte overtime30) {
        this.overtime30 = overtime30;
    }

    public Float getOvertimeDuration30() {
        return overtimeDuration30;
    }

    public void setOvertimeDuration30(Float overtimeDuration30) {
        this.overtimeDuration30 = overtimeDuration30;
    }

    public Boolean getNoNeed31() {
        return noNeed31;
    }

    public void setNoNeed31(Boolean noNeed31) {
        this.noNeed31 = noNeed31;
    }

    public Boolean getattendanced31() {
        return attendanced31;
    }

    public void setattendanced31(Boolean attendanced31) {
        this.attendanced31 = attendanced31;
    }

    public Float getWorkingHours31() {
        return workingHours31;
    }

    public void setWorkingHours31(Float workingHours31) {
        this.workingHours31 = workingHours31;
    }

    public Byte getLeave31() {
        return leave31;
    }

    public void setLeave31(Byte leave31) {
        this.leave31 = leave31;
    }

    public Float getLeaveDuration31() {
        return leaveDuration31;
    }

    public void setLeaveDuration31(Float leaveDuration31) {
        this.leaveDuration31 = leaveDuration31;
    }

    public Byte getTravel31() {
        return travel31;
    }

    public void setTravel31(Byte travel31) {
        this.travel31 = travel31;
    }

    public Boolean getLate31() {
        return late31;
    }

    public void setLate31(Boolean late31) {
        this.late31 = late31;
    }

    public Boolean getLeaveEarly31() {
        return leaveEarly31;
    }

    public void setLeaveEarly31(Boolean leaveEarly31) {
        this.leaveEarly31 = leaveEarly31;
    }

    public Byte getOutside31() {
        return outside31;
    }

    public void setOutside31(Byte outside31) {
        this.outside31 = outside31;
    }

    public Byte getAbsenteeism31() {
        return absenteeism31;
    }

    public void setAbsenteeism31(Byte absenteeism31) {
        this.absenteeism31 = absenteeism31;
    }

    public Byte getOvertime31() {
        return overtime31;
    }

    public void setOvertime31(Byte overtime31) {
        this.overtime31 = overtime31;
    }

    public Float getOvertimeDuration31() {
        return overtimeDuration31;
    }

    public void setOvertimeDuration31(Float overtimeDuration31) {
        this.overtimeDuration31 = overtimeDuration31;
    }

    public Float getWorkingHours() {
        return workingHours;
    }

    public void setWorkingHours(Float workingHours) {
        this.workingHours = workingHours;
    }

    public Short getLeaveNum() {
        return leaveNum;
    }

    public void setLeaveNum(Short leaveNum) {
        this.leaveNum = leaveNum;
    }

    public Float getLeaveDuration() {
        return leaveDuration;
    }

    public void setLeaveDuration(Float leaveDuration) {
        this.leaveDuration = leaveDuration;
    }

    public Short getTravelNum() {
        return travelNum;
    }

    public void setTravelNum(Short travelNum) {
        this.travelNum = travelNum;
    }

    public Short getLateNum() {
        return lateNum;
    }

    public void setLateNum(Short lateNum) {
        this.lateNum = lateNum;
    }

    public Short getLeaveEarlyNum() {
        return leaveEarlyNum;
    }

    public void setLeaveEarlyNum(Short leaveEarlyNum) {
        this.leaveEarlyNum = leaveEarlyNum;
    }

    public Short getOutsideNum() {
        return outsideNum;
    }

    public void setOutsideNum(Short outsideNum) {
        this.outsideNum = outsideNum;
    }

    public Short getAbsenteeismNum() {
        return absenteeismNum;
    }

    public void setAbsenteeismNum(Short absenteeismNum) {
        this.absenteeismNum = absenteeismNum;
    }

    public Short getOvertimeNum() {
        return overtimeNum;
    }

    public void setOvertimeNum(Short overtimeNum) {
        this.overtimeNum = overtimeNum;
    }

    public Float getOvertimeDuration() {
        return overtimeDuration;
    }

    public void setOvertimeDuration(Float overtimeDuration) {
        this.overtimeDuration = overtimeDuration;
    }

    public String getDayStatus01() {
        return dayStatus01;
    }

    public void setDayStatus01(String dayStatus01) {
        this.dayStatus01 = dayStatus01;
    }

    public String getDayStatus02() {
        return dayStatus02;
    }

    public void setDayStatus02(String dayStatus02) {
        this.dayStatus02 = dayStatus02;
    }

    public String getDayStatus03() {
        return dayStatus03;
    }

    public void setDayStatus03(String dayStatus03) {
        this.dayStatus03 = dayStatus03;
    }

    public String getDayStatus04() {
        return dayStatus04;
    }

    public void setDayStatus04(String dayStatus04) {
        this.dayStatus04 = dayStatus04;
    }

    public String getDayStatus05() {
        return dayStatus05;
    }

    public void setDayStatus05(String dayStatus05) {
        this.dayStatus05 = dayStatus05;
    }

    public String getDayStatus06() {
        return dayStatus06;
    }

    public void setDayStatus06(String dayStatus06) {
        this.dayStatus06 = dayStatus06;
    }

    public String getDayStatus07() {
        return dayStatus07;
    }

    public void setDayStatus07(String dayStatus07) {
        this.dayStatus07 = dayStatus07;
    }

    public String getDayStatus08() {
        return dayStatus08;
    }

    public void setDayStatus08(String dayStatus08) {
        this.dayStatus08 = dayStatus08;
    }

    public String getDayStatus09() {
        return dayStatus09;
    }

    public void setDayStatus09(String dayStatus09) {
        this.dayStatus09 = dayStatus09;
    }

    public String getDayStatus10() {
        return dayStatus10;
    }

    public void setDayStatus10(String dayStatus10) {
        this.dayStatus10 = dayStatus10;
    }

    public String getDayStatus11() {
        return dayStatus11;
    }

    public void setDayStatus11(String dayStatus11) {
        this.dayStatus11 = dayStatus11;
    }

    public String getDayStatus12() {
        return dayStatus12;
    }

    public void setDayStatus12(String dayStatus12) {
        this.dayStatus12 = dayStatus12;
    }

    public String getDayStatus13() {
        return dayStatus13;
    }

    public void setDayStatus13(String dayStatus13) {
        this.dayStatus13 = dayStatus13;
    }

    public String getDayStatus14() {
        return dayStatus14;
    }

    public void setDayStatus14(String dayStatus14) {
        this.dayStatus14 = dayStatus14;
    }

    public String getDayStatus15() {
        return dayStatus15;
    }

    public void setDayStatus15(String dayStatus15) {
        this.dayStatus15 = dayStatus15;
    }

    public String getDayStatus16() {
        return dayStatus16;
    }

    public void setDayStatus16(String dayStatus16) {
        this.dayStatus16 = dayStatus16;
    }

    public String getDayStatus17() {
        return dayStatus17;
    }

    public void setDayStatus17(String dayStatus17) {
        this.dayStatus17 = dayStatus17;
    }

    public String getDayStatus18() {
        return dayStatus18;
    }

    public void setDayStatus18(String dayStatus18) {
        this.dayStatus18 = dayStatus18;
    }

    public String getDayStatus19() {
        return dayStatus19;
    }

    public void setDayStatus19(String dayStatus19) {
        this.dayStatus19 = dayStatus19;
    }

    public String getDayStatus20() {
        return dayStatus20;
    }

    public void setDayStatus20(String dayStatus20) {
        this.dayStatus20 = dayStatus20;
    }

    public String getDayStatus21() {
        return dayStatus21;
    }

    public void setDayStatus21(String dayStatus21) {
        this.dayStatus21 = dayStatus21;
    }

    public String getDayStatus22() {
        return dayStatus22;
    }

    public void setDayStatus22(String dayStatus22) {
        this.dayStatus22 = dayStatus22;
    }

    public String getDayStatus23() {
        return dayStatus23;
    }

    public void setDayStatus23(String dayStatus23) {
        this.dayStatus23 = dayStatus23;
    }

    public String getDayStatus24() {
        return dayStatus24;
    }

    public void setDayStatus24(String dayStatus24) {
        this.dayStatus24 = dayStatus24;
    }

    public String getDayStatus25() {
        return dayStatus25;
    }

    public void setDayStatus25(String dayStatus25) {
        this.dayStatus25 = dayStatus25;
    }

    public String getDayStatus26() {
        return dayStatus26;
    }

    public void setDayStatus26(String dayStatus26) {
        this.dayStatus26 = dayStatus26;
    }

    public String getDayStatus27() {
        return dayStatus27;
    }

    public void setDayStatus27(String dayStatus27) {
        this.dayStatus27 = dayStatus27;
    }

    public String getDayStatus28() {
        return dayStatus28;
    }

    public void setDayStatus28(String dayStatus28) {
        this.dayStatus28 = dayStatus28;
    }

    public String getDayStatus29() {
        return dayStatus29;
    }

    public void setDayStatus29(String dayStatus29) {
        this.dayStatus29 = dayStatus29;
    }

    public String getDayStatus30() {
        return dayStatus30;
    }

    public void setDayStatus30(String dayStatus30) {
        this.dayStatus30 = dayStatus30;
    }

    public String getDayStatus31() {
        return dayStatus31;
    }

    public void setDayStatus31(String dayStatus31) {
        this.dayStatus31 = dayStatus31;
    }

    public PersonnelAttendanceMonthly() {
        firstDate = Byte.valueOf((byte) 1);
        monthdiff = Byte.valueOf((byte) 0);
        noNeed01 = Boolean.FALSE;
        attendanced01 = Boolean.TRUE;
        workingHours01 = Float.valueOf(0.0F);
        leave01 = Byte.valueOf((byte) 0);
        leaveDuration01 = Float.valueOf(0.0F);
        travel01 = Byte.valueOf((byte) 0);
        late01 = Boolean.FALSE;
        leaveEarly01 = Boolean.FALSE;
        outside01 = Byte.valueOf((byte) 0);
        absenteeism01 = Byte.valueOf((byte) 0);
        overtime01 = Byte.valueOf((byte) 0);
        overtimeDuration01 = Float.valueOf(0.0F);
        noNeed02 = Boolean.FALSE;
        attendanced02 = Boolean.TRUE;
        workingHours02 = Float.valueOf(0.0F);
        leave02 = Byte.valueOf((byte) 0);
        leaveDuration02 = Float.valueOf(0.0F);
        travel02 = Byte.valueOf((byte) 0);
        late02 = Boolean.FALSE;
        leaveEarly02 = Boolean.FALSE;
        outside02 = Byte.valueOf((byte) 0);
        absenteeism02 = Byte.valueOf((byte) 0);
        overtime02 = Byte.valueOf((byte) 0);
        overtimeDuration02 = Float.valueOf(0.0F);
        noNeed03 = Boolean.FALSE;
        attendanced03 = Boolean.TRUE;
        workingHours03 = Float.valueOf(0.0F);
        leave03 = Byte.valueOf((byte) 0);
        leaveDuration03 = Float.valueOf(0.0F);
        travel03 = Byte.valueOf((byte) 0);
        late03 = Boolean.FALSE;
        leaveEarly03 = Boolean.FALSE;
        outside03 = Byte.valueOf((byte) 0);
        absenteeism03 = Byte.valueOf((byte) 0);
        overtime03 = Byte.valueOf((byte) 0);
        overtimeDuration03 = Float.valueOf(0.0F);
        noNeed04 = Boolean.FALSE;
        attendanced04 = Boolean.TRUE;
        workingHours04 = Float.valueOf(0.0F);
        leave04 = Byte.valueOf((byte) 0);
        leaveDuration04 = Float.valueOf(0.0F);
        travel04 = Byte.valueOf((byte) 0);
        late04 = Boolean.FALSE;
        leaveEarly04 = Boolean.FALSE;
        outside04 = Byte.valueOf((byte) 0);
        absenteeism04 = Byte.valueOf((byte) 0);
        overtime04 = Byte.valueOf((byte) 0);
        overtimeDuration04 = Float.valueOf(0.0F);
        noNeed05 = Boolean.FALSE;
        attendanced05 = Boolean.TRUE;
        workingHours05 = Float.valueOf(0.0F);
        leave05 = Byte.valueOf((byte) 0);
        leaveDuration05 = Float.valueOf(0.0F);
        travel05 = Byte.valueOf((byte) 0);
        late05 = Boolean.FALSE;
        leaveEarly05 = Boolean.FALSE;
        outside05 = Byte.valueOf((byte) 0);
        absenteeism05 = Byte.valueOf((byte) 0);
        overtime05 = Byte.valueOf((byte) 0);
        overtimeDuration05 = Float.valueOf(0.0F);
        noNeed06 = Boolean.FALSE;
        attendanced06 = Boolean.TRUE;
        workingHours06 = Float.valueOf(0.0F);
        leave06 = Byte.valueOf((byte) 0);
        leaveDuration06 = Float.valueOf(0.0F);
        travel06 = Byte.valueOf((byte) 0);
        late06 = Boolean.FALSE;
        leaveEarly06 = Boolean.FALSE;
        outside06 = Byte.valueOf((byte) 0);
        absenteeism06 = Byte.valueOf((byte) 0);
        overtime06 = Byte.valueOf((byte) 0);
        overtimeDuration06 = Float.valueOf(0.0F);
        noNeed07 = Boolean.FALSE;
        attendanced07 = Boolean.TRUE;
        workingHours07 = Float.valueOf(0.0F);
        leave07 = Byte.valueOf((byte) 0);
        leaveDuration07 = Float.valueOf(0.0F);
        travel07 = Byte.valueOf((byte) 0);
        late07 = Boolean.FALSE;
        leaveEarly07 = Boolean.FALSE;
        outside07 = Byte.valueOf((byte) 0);
        absenteeism07 = Byte.valueOf((byte) 0);
        overtime07 = Byte.valueOf((byte) 0);
        overtimeDuration07 = Float.valueOf(0.0F);
        noNeed08 = Boolean.FALSE;
        attendanced08 = Boolean.TRUE;
        workingHours08 = Float.valueOf(0.0F);
        leave08 = Byte.valueOf((byte) 0);
        leaveDuration08 = Float.valueOf(0.0F);
        travel08 = Byte.valueOf((byte) 0);
        late08 = Boolean.FALSE;
        leaveEarly08 = Boolean.FALSE;
        outside08 = Byte.valueOf((byte) 0);
        absenteeism08 = Byte.valueOf((byte) 0);
        overtime08 = Byte.valueOf((byte) 0);
        overtimeDuration08 = Float.valueOf(0.0F);
        noNeed09 = Boolean.FALSE;
        attendanced09 = Boolean.TRUE;
        workingHours09 = Float.valueOf(0.0F);
        leave09 = Byte.valueOf((byte) 0);
        leaveDuration09 = Float.valueOf(0.0F);
        travel09 = Byte.valueOf((byte) 0);
        late09 = Boolean.FALSE;
        leaveEarly09 = Boolean.FALSE;
        outside09 = Byte.valueOf((byte) 0);
        absenteeism09 = Byte.valueOf((byte) 0);
        overtime09 = Byte.valueOf((byte) 0);
        overtimeDuration09 = Float.valueOf(0.0F);
        noNeed10 = Boolean.FALSE;
        attendanced10 = Boolean.TRUE;
        workingHours10 = Float.valueOf(0.0F);
        leave10 = Byte.valueOf((byte) 0);
        leaveDuration10 = Float.valueOf(0.0F);
        travel10 = Byte.valueOf((byte) 0);
        late10 = Boolean.FALSE;
        leaveEarly10 = Boolean.FALSE;
        outside10 = Byte.valueOf((byte) 0);
        absenteeism10 = Byte.valueOf((byte) 0);
        overtime10 = Byte.valueOf((byte) 0);
        overtimeDuration10 = Float.valueOf(0.0F);
        noNeed11 = Boolean.FALSE;
        attendanced11 = Boolean.TRUE;
        workingHours11 = Float.valueOf(0.0F);
        leave11 = Byte.valueOf((byte) 0);
        leaveDuration11 = Float.valueOf(0.0F);
        travel11 = Byte.valueOf((byte) 0);
        late11 = Boolean.FALSE;
        leaveEarly11 = Boolean.FALSE;
        outside11 = Byte.valueOf((byte) 0);
        absenteeism11 = Byte.valueOf((byte) 0);
        overtime11 = Byte.valueOf((byte) 0);
        overtimeDuration11 = Float.valueOf(0.0F);
        noNeed12 = Boolean.FALSE;
        attendanced12 = Boolean.TRUE;
        workingHours12 = Float.valueOf(0.0F);
        leave12 = Byte.valueOf((byte) 0);
        leaveDuration12 = Float.valueOf(0.0F);
        travel12 = Byte.valueOf((byte) 0);
        late12 = Boolean.FALSE;
        leaveEarly12 = Boolean.FALSE;
        outside12 = Byte.valueOf((byte) 0);
        absenteeism12 = Byte.valueOf((byte) 0);
        overtime12 = Byte.valueOf((byte) 0);
        overtimeDuration12 = Float.valueOf(0.0F);
        noNeed13 = Boolean.FALSE;
        attendanced13 = Boolean.TRUE;
        workingHours13 = Float.valueOf(0.0F);
        leave13 = Byte.valueOf((byte) 0);
        leaveDuration13 = Float.valueOf(0.0F);
        travel13 = Byte.valueOf((byte) 0);
        late13 = Boolean.FALSE;
        leaveEarly13 = Boolean.FALSE;
        outside13 = Byte.valueOf((byte) 0);
        absenteeism13 = Byte.valueOf((byte) 0);
        overtime13 = Byte.valueOf((byte) 0);
        overtimeDuration13 = Float.valueOf(0.0F);
        noNeed14 = Boolean.FALSE;
        attendanced14 = Boolean.TRUE;
        workingHours14 = Float.valueOf(0.0F);
        leave14 = Byte.valueOf((byte) 0);
        leaveDuration14 = Float.valueOf(0.0F);
        travel14 = Byte.valueOf((byte) 0);
        late14 = Boolean.FALSE;
        leaveEarly14 = Boolean.FALSE;
        outside14 = Byte.valueOf((byte) 0);
        absenteeism14 = Byte.valueOf((byte) 0);
        overtime14 = Byte.valueOf((byte) 0);
        overtimeDuration14 = Float.valueOf(0.0F);
        noNeed15 = Boolean.FALSE;
        attendanced15 = Boolean.TRUE;
        workingHours15 = Float.valueOf(0.0F);
        leave15 = Byte.valueOf((byte) 0);
        leaveDuration15 = Float.valueOf(0.0F);
        travel15 = Byte.valueOf((byte) 0);
        late15 = Boolean.FALSE;
        leaveEarly15 = Boolean.FALSE;
        outside15 = Byte.valueOf((byte) 0);
        absenteeism15 = Byte.valueOf((byte) 0);
        overtime15 = Byte.valueOf((byte) 0);
        overtimeDuration15 = Float.valueOf(0.0F);
        noNeed16 = Boolean.FALSE;
        attendanced16 = Boolean.TRUE;
        workingHours16 = Float.valueOf(0.0F);
        leave16 = Byte.valueOf((byte) 0);
        leaveDuration16 = Float.valueOf(0.0F);
        travel16 = Byte.valueOf((byte) 0);
        late16 = Boolean.FALSE;
        leaveEarly16 = Boolean.FALSE;
        outside16 = Byte.valueOf((byte) 0);
        absenteeism16 = Byte.valueOf((byte) 0);
        overtime16 = Byte.valueOf((byte) 0);
        overtimeDuration16 = Float.valueOf(0.0F);
        noNeed17 = Boolean.FALSE;
        attendanced17 = Boolean.TRUE;
        workingHours17 = Float.valueOf(0.0F);
        leave17 = Byte.valueOf((byte) 0);
        leaveDuration17 = Float.valueOf(0.0F);
        travel17 = Byte.valueOf((byte) 0);
        late17 = Boolean.FALSE;
        leaveEarly17 = Boolean.FALSE;
        outside17 = Byte.valueOf((byte) 0);
        absenteeism17 = Byte.valueOf((byte) 0);
        overtime17 = Byte.valueOf((byte) 0);
        overtimeDuration17 = Float.valueOf(0.0F);
        noNeed18 = Boolean.FALSE;
        attendanced18 = Boolean.TRUE;
        workingHours18 = Float.valueOf(0.0F);
        leave18 = Byte.valueOf((byte) 0);
        leaveDuration18 = Float.valueOf(0.0F);
        travel18 = Byte.valueOf((byte) 0);
        late18 = Boolean.FALSE;
        leaveEarly18 = Boolean.FALSE;
        outside18 = Byte.valueOf((byte) 0);
        absenteeism18 = Byte.valueOf((byte) 0);
        overtime18 = Byte.valueOf((byte) 0);
        overtimeDuration18 = Float.valueOf(0.0F);
        noNeed19 = Boolean.FALSE;
        attendanced19 = Boolean.TRUE;
        workingHours19 = Float.valueOf(0.0F);
        leave19 = Byte.valueOf((byte) 0);
        leaveDuration19 = Float.valueOf(0.0F);
        travel19 = Byte.valueOf((byte) 0);
        late19 = Boolean.FALSE;
        leaveEarly19 = Boolean.FALSE;
        outside19 = Byte.valueOf((byte) 0);
        absenteeism19 = Byte.valueOf((byte) 0);
        overtime19 = Byte.valueOf((byte) 0);
        overtimeDuration19 = Float.valueOf(0.0F);
        noNeed20 = Boolean.FALSE;
        attendanced20 = Boolean.TRUE;
        workingHours20 = Float.valueOf(0.0F);
        leave20 = Byte.valueOf((byte) 0);
        leaveDuration20 = Float.valueOf(0.0F);
        travel20 = Byte.valueOf((byte) 0);
        late20 = Boolean.FALSE;
        leaveEarly20 = Boolean.FALSE;
        outside20 = Byte.valueOf((byte) 0);
        absenteeism20 = Byte.valueOf((byte) 0);
        overtime20 = Byte.valueOf((byte) 0);
        overtimeDuration20 = Float.valueOf(0.0F);
        noNeed21 = Boolean.FALSE;
        attendanced21 = Boolean.TRUE;
        workingHours21 = Float.valueOf(0.0F);
        leave21 = Byte.valueOf((byte) 0);
        leaveDuration21 = Float.valueOf(0.0F);
        travel21 = Byte.valueOf((byte) 0);
        late21 = Boolean.FALSE;
        leaveEarly21 = Boolean.FALSE;
        outside21 = Byte.valueOf((byte) 0);
        absenteeism21 = Byte.valueOf((byte) 0);
        overtime21 = Byte.valueOf((byte) 0);
        overtimeDuration21 = Float.valueOf(0.0F);
        noNeed22 = Boolean.FALSE;
        attendanced22 = Boolean.TRUE;
        workingHours22 = Float.valueOf(0.0F);
        leave22 = Byte.valueOf((byte) 0);
        leaveDuration22 = Float.valueOf(0.0F);
        travel22 = Byte.valueOf((byte) 0);
        late22 = Boolean.FALSE;
        leaveEarly22 = Boolean.FALSE;
        outside22 = Byte.valueOf((byte) 0);
        absenteeism22 = Byte.valueOf((byte) 0);
        overtime22 = Byte.valueOf((byte) 0);
        overtimeDuration22 = Float.valueOf(0.0F);
        noNeed23 = Boolean.FALSE;
        attendanced23 = Boolean.TRUE;
        workingHours23 = Float.valueOf(0.0F);
        leave23 = Byte.valueOf((byte) 0);
        leaveDuration23 = Float.valueOf(0.0F);
        travel23 = Byte.valueOf((byte) 0);
        late23 = Boolean.FALSE;
        leaveEarly23 = Boolean.FALSE;
        outside23 = Byte.valueOf((byte) 0);
        absenteeism23 = Byte.valueOf((byte) 0);
        overtime23 = Byte.valueOf((byte) 0);
        overtimeDuration23 = Float.valueOf(0.0F);
        noNeed24 = Boolean.FALSE;
        attendanced24 = Boolean.TRUE;
        workingHours24 = Float.valueOf(0.0F);
        leave24 = Byte.valueOf((byte) 0);
        leaveDuration24 = Float.valueOf(0.0F);
        travel24 = Byte.valueOf((byte) 0);
        late24 = Boolean.FALSE;
        leaveEarly24 = Boolean.FALSE;
        outside24 = Byte.valueOf((byte) 0);
        absenteeism24 = Byte.valueOf((byte) 0);
        overtime24 = Byte.valueOf((byte) 0);
        overtimeDuration24 = Float.valueOf(0.0F);
        noNeed25 = Boolean.FALSE;
        attendanced25 = Boolean.TRUE;
        workingHours25 = Float.valueOf(0.0F);
        leave25 = Byte.valueOf((byte) 0);
        leaveDuration25 = Float.valueOf(0.0F);
        travel25 = Byte.valueOf((byte) 0);
        late25 = Boolean.FALSE;
        leaveEarly25 = Boolean.FALSE;
        outside25 = Byte.valueOf((byte) 0);
        absenteeism25 = Byte.valueOf((byte) 0);
        overtime25 = Byte.valueOf((byte) 0);
        overtimeDuration25 = Float.valueOf(0.0F);
        noNeed26 = Boolean.FALSE;
        attendanced26 = Boolean.TRUE;
        workingHours26 = Float.valueOf(0.0F);
        leave26 = Byte.valueOf((byte) 0);
        leaveDuration26 = Float.valueOf(0.0F);
        travel26 = Byte.valueOf((byte) 0);
        late26 = Boolean.FALSE;
        leaveEarly26 = Boolean.FALSE;
        outside26 = Byte.valueOf((byte) 0);
        absenteeism26 = Byte.valueOf((byte) 0);
        overtime26 = Byte.valueOf((byte) 0);
        overtimeDuration26 = Float.valueOf(0.0F);
        noNeed27 = Boolean.FALSE;
        attendanced27 = Boolean.TRUE;
        workingHours27 = Float.valueOf(0.0F);
        leave27 = Byte.valueOf((byte) 0);
        leaveDuration27 = Float.valueOf(0.0F);
        travel27 = Byte.valueOf((byte) 0);
        late27 = Boolean.FALSE;
        leaveEarly27 = Boolean.FALSE;
        outside27 = Byte.valueOf((byte) 0);
        absenteeism27 = Byte.valueOf((byte) 0);
        overtime27 = Byte.valueOf((byte) 0);
        overtimeDuration27 = Float.valueOf(0.0F);
        noNeed28 = Boolean.FALSE;
        attendanced28 = Boolean.TRUE;
        workingHours28 = Float.valueOf(0.0F);
        leave28 = Byte.valueOf((byte) 0);
        leaveDuration28 = Float.valueOf(0.0F);
        travel28 = Byte.valueOf((byte) 0);
        late28 = Boolean.FALSE;
        leaveEarly28 = Boolean.FALSE;
        outside28 = Byte.valueOf((byte) 0);
        absenteeism28 = Byte.valueOf((byte) 0);
        overtime28 = Byte.valueOf((byte) 0);
        overtimeDuration28 = Float.valueOf(0.0F);
        noNeed29 = Boolean.FALSE;
        attendanced29 = Boolean.TRUE;
        workingHours29 = Float.valueOf(0.0F);
        leave29 = Byte.valueOf((byte) 0);
        leaveDuration29 = Float.valueOf(0.0F);
        travel29 = Byte.valueOf((byte) 0);
        late29 = Boolean.FALSE;
        leaveEarly29 = Boolean.FALSE;
        outside29 = Byte.valueOf((byte) 0);
        absenteeism29 = Byte.valueOf((byte) 0);
        overtime29 = Byte.valueOf((byte) 0);
        overtimeDuration29 = Float.valueOf(0.0F);
        noNeed30 = Boolean.FALSE;
        attendanced30 = Boolean.TRUE;
        workingHours30 = Float.valueOf(0.0F);
        leave30 = Byte.valueOf((byte) 0);
        leaveDuration30 = Float.valueOf(0.0F);
        travel30 = Byte.valueOf((byte) 0);
        late30 = Boolean.FALSE;
        leaveEarly30 = Boolean.FALSE;
        outside30 = Byte.valueOf((byte) 0);
        absenteeism30 = Byte.valueOf((byte) 0);
        overtime30 = Byte.valueOf((byte) 0);
        overtimeDuration30 = Float.valueOf(0.0F);
        noNeed31 = Boolean.FALSE;
        attendanced31 = Boolean.TRUE;
        workingHours31 = Float.valueOf(0.0F);
        leave31 = Byte.valueOf((byte) 0);
        leaveDuration31 = Float.valueOf(0.0F);
        travel31 = Byte.valueOf((byte) 0);
        late31 = Boolean.FALSE;
        leaveEarly31 = Boolean.FALSE;
        outside31 = Byte.valueOf((byte) 0);
        absenteeism31 = Byte.valueOf((byte) 0);
        overtime31 = Byte.valueOf((byte) 0);
        overtimeDuration31 = Float.valueOf(0.0F);
    }

    //End of automatically generated codes

    @JsonIgnore @JSONField(serialize = false)
    private static final List<String> dayProperties = Arrays.asList("noNeed","workingHours","leave","leaveDuration","travel","late","leaveEarly","outside","absenteeism","overtime","overtimeDuration");

    private void SetDayProperty(String prefix, Integer day, Object value) throws InvocationTargetException, IllegalAccessException {
        String name = day<10?prefix+"0"+day:prefix+day;
        if(dayProperties.contains(prefix)||day<=31) {
            BeanUtils.copyProperty(this, name, value);
        } else {
            throw new NoSuchFieldError("Daily porperty \""+name +"\" not found");
        }
    }
    public void SetNoNeed(Integer day, Boolean value, Boolean attendanced) throws InvocationTargetException, IllegalAccessException {
        SetDayProperty("noNeed",day,value);
        SetDayProperty("attendanced",day,attendanced);
        if(Boolean.TRUE.equals(value)) {
            SetDayProperty("workingHours", day, Float.valueOf(0.0F));
        }
    }

    public void SetAttendanced(Integer day, Boolean value) throws InvocationTargetException, IllegalAccessException {
        SetDayProperty("attendanced",day,value);
    }

    public void SetWorkingHours(Integer day, Float value) throws InvocationTargetException, IllegalAccessException {
        SetDayProperty("workingHours",day,value);
    }

    public void SetLeave(Integer day, Byte num, Float duration) throws InvocationTargetException, IllegalAccessException {
        SetDayProperty("leave",day,num);
        SetDayProperty("leaveDuration",day,duration);
    }

    public void SetTravel(Integer day, Byte value) throws InvocationTargetException, IllegalAccessException {
        SetDayProperty("travel",day,value);
    }

    public void SetLate(Integer day, Boolean value) throws InvocationTargetException, IllegalAccessException {
        SetDayProperty("late",day,value);
    }

    public void SetLeaveEarly(Integer day, Boolean value) throws InvocationTargetException, IllegalAccessException {
        SetDayProperty("leaveEarly",day,value);
    }

    public void SetOutside(Integer day, Byte value) throws InvocationTargetException, IllegalAccessException {
        SetDayProperty("outside",day,value);
    }

    public void SetAbsenteeisme(Integer day, Byte value) throws InvocationTargetException, IllegalAccessException {
        SetDayProperty("absenteeism",day,value);
    }

    public void SetOvertime(Integer day, Byte num, Float duration) throws InvocationTargetException, IllegalAccessException {
        SetDayProperty("overtime",day,num);
        SetDayProperty("overtimeDuration",day,duration);
    }

    private String GetDayProperty(String prefix, Integer day)  throws InvocationTargetException, IllegalAccessException {
        String name = day<10?prefix+"0"+day:prefix+day;
        if(dayProperties.contains(prefix)||day<=31) {
            try {
                return BeanUtils.getProperty(this, name);
            } catch (NoSuchMethodException e) {
                e.printStackTrace();
                throw new NoSuchFieldError("Daily porperty \""+name +"\" not found");
            }
        } else {
            throw new NoSuchFieldError("Daily porperty \""+name +"\" not found");
        }
    }
    public Boolean GetNoNeed(Integer day) throws InvocationTargetException, IllegalAccessException {
        Boolean result = Boolean.valueOf(GetDayProperty("noNeed",day));
        result = result==null?Boolean.FALSE:result;
        return result;
    }
    public Boolean GetAttendanced(Integer day) throws InvocationTargetException, IllegalAccessException {
        Boolean result = Boolean.valueOf(GetDayProperty("attendanced",day));
        result = result==null?Boolean.FALSE:result;
        return result;
    }

    public Float GetWorkingHours(Integer day) throws InvocationTargetException, IllegalAccessException {
        Float value = null;
        if(Boolean.FALSE.equals(GetNoNeed(day))) {
            value = new Float(GetDayProperty("workingHours", day));
        }
        return value==null ? Float.valueOf(0.0F) : value;
    }

    public Pair<Byte, Float> GetLeave(Integer day) throws InvocationTargetException, IllegalAccessException {
        Byte leave = Byte.valueOf(GetDayProperty("leave",day));
        Float leaveDuration = new Float(GetDayProperty("leaveDuration",day));
        return Pair.of(leave==null? Byte.valueOf((byte)0):leave, leaveDuration==null?Float.valueOf(0.0F):leaveDuration);
    }

    public Byte GetTravel(Integer day) throws InvocationTargetException, IllegalAccessException {
        Byte result = Byte.valueOf(GetDayProperty("travel",day));
        result = result==null? Byte.valueOf((byte)0):result;
        return result;
    }

    public Boolean GetLate(Integer day) throws InvocationTargetException, IllegalAccessException {
        Boolean result = Boolean.valueOf(GetDayProperty("late",day));
        result = result==null?Boolean.FALSE:result;
        return result;
    }

    public Boolean GetLeaveEarly(Integer day) throws InvocationTargetException, IllegalAccessException {
        Boolean result = Boolean.valueOf(GetDayProperty("leaveEarly",day));
        result = result==null?Boolean.FALSE:result;
        return result;
    }

    public Byte GetOutside(Integer day) throws InvocationTargetException, IllegalAccessException {
        Byte result = Byte.valueOf(GetDayProperty("outside",day));
        result = result==null? Byte.valueOf((byte)0):result;
        return result;
    }

    public Byte GetAbsenteeisme(Integer day) throws InvocationTargetException, IllegalAccessException {
        Byte result = Byte.valueOf(GetDayProperty("absenteeism",day));
        result = result==null? Byte.valueOf((byte)0):result;
        return result;
    }

    public Pair<Byte, Float> GetOvertime(Integer day) throws InvocationTargetException, IllegalAccessException {
        Byte overtime = Byte.valueOf(GetDayProperty("overtime",day));
        Float overtimeDuration = new Float(GetDayProperty("overtimeDuration",day));
        return Pair.of(overtime==null? Byte.valueOf((byte)0):overtime, overtimeDuration==null?Float.valueOf(0.0F):overtimeDuration);
    }

    public String SetTransientPorpertis() {
        return SetTransientPorpertis(Boolean.TRUE);
    }
    public String SetTransientPorpertis(Boolean evicted) {
        int maxday=GetMaxDay();
        //设置月统计数事务字段
        SetTransientPorpertis(maxday, evicted);
        //设置日状态
        SetDayStatus(maxday);
        return JSON.toJSONString(this);
    }

    private int GetMaxDay() {
        int maxday=31;//非当月显示1~31日
        Date now = new Date(System.currentTimeMillis());
        if(this.getYearmonth()!=null && this.getYearmonth().equals(NewDateUtils.getYearMonth(now))) {
            //如果是本月
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(now);
            maxday = calendar.get(Calendar.DAY_OF_MONTH);
        }
        return maxday>0 && maxday<=31 ? maxday : 31;
    }

    //设置月统计数
    private void SetTransientPorpertis(int maxday, boolean evicted) {
        final List<String> excludeProperties = Arrays.asList("workingHours","leaveDuration","overtimeDuration");
        for (String prefix : dayProperties) {
            if(excludeProperties.contains(prefix)){//统计时长
                SetTransientDurationPorperty(prefix, maxday);
            } else {//统计数量“Num”
                SetTransientNumPorperty(prefix, maxday);
            }
        }
        if(maxday < 31 && evicted) {
            for(int i = maxday+1; maxday < 31; maxday++) {//如果是当月，清除今天以后的加班数据，需要先调用dao.getSession().evict(entity);
                try {
                    this.SetOvertime(i, (byte)0, Float.valueOf(0.0F));
                } catch (InvocationTargetException | IllegalAccessException e) {
                    Logger.getLogger(getClass()).error("SetTransientPorpertis error!", e);
                }
            }
        }
    }
    private void SetTransientNumPorperty(String prefix, int maxday) {
        Integer value = 0;
        if(dayProperties.contains(prefix)) {
            StringBuffer name = new StringBuffer(prefix.length()+3);
            name.append(prefix);
            int day=1;
            int mday = maxday < 31 ? maxday : 31;
            try {
                for (; day <= mday; day++) {
                    name.delete(prefix.length(), name.length());
                    name.append(String.format("%02d",day));
                    value += GetFieldNum(name.toString());
                }
                name.delete(prefix.length(),  name.length());
                name.append("Num");
                BeanUtils.setProperty(this, name.toString(), value);
            } catch (InvocationTargetException | IllegalAccessException e) {
                Logger.getLogger(getClass()).error("SetTransientNumPorperty error!", e);
            }
        } else {
            throw new NoSuchFieldError("Daily porperties \""+prefix +"*\" not found");
        }
    }
    private void SetTransientDurationPorperty(String name, int maxday) {
        Float value = Float.valueOf(0.0F);
        if(dayProperties.contains(name)) {
            int day=1;
            int mday = maxday < 31 ? maxday : 31;
            try {
                for (; day <= mday; day++) {
                    value += Float.valueOf(GetDayProperty(name,day));
                }
                BeanUtils.setProperty(this, name, value);
            } catch (InvocationTargetException | IllegalAccessException e) {
                Logger.getLogger(getClass()).error("SetTransientDurationPorperty error!", e);
            }
        } else {
            throw new NoSuchFieldError("Monthly porperties "+name +" not found");
        }
    }

    private Byte GetFieldNum(String name) {
        Byte result = 0;
        try {
            String val = BeanUtils.getProperty(this, name);
            switch(val) {
                case "true":
                    result = 1;
                    break;
                case "false":
                    result = 0;
                    break;
                default:
                    result = Byte.valueOf(val);
            }
        } catch (InvocationTargetException | IllegalAccessException | NoSuchMethodException e) {
            Logger.getLogger(getClass()).error("GetFieldNum error!", e);
        }
        return result;
    }

    //设置日状态
    private void SetDayStatus(int maxday) {
        for(int day=1; day<maxday; day++) {
            SetDayStatus(String.format("%02d",day));
        }
    }
    //设置日状态
    private void SetDayStatus(String dayString) {
        final List<String> excludeProperties = Arrays.asList("workingHours","leaveDuration","overtime","overtimeDuration");
        String status=null;
        Byte tmp;
        for (String prefix : dayProperties) {
            if(!excludeProperties.contains(prefix)){//不统计加班“overtime”和时长属性。
                tmp = GetFieldNum(prefix+dayString);
                if(tmp>0) {
                    if(status==null) {
                        status =prefix;
                    } else {
                        status = "complex";
                    }
                }
            }
        }
        if(status==null) {
            status = "normal";
        }
        try {
            BeanUtils.setProperty(this, "dayStatus"+dayString, MyStrings.nulltoempty(status));
        } catch (IllegalAccessException e) {
            //e.printStackTrace();
        } catch (InvocationTargetException e) {
            //e.printStackTrace();
        }
    }
    public String GetDayStatus(String dayString) {
        String result = "";
        try {
            result = BeanUtils.getProperty(this, "dayStatus"+dayString);
        } catch (IllegalAccessException e) {
            //e.printStackTrace();
        } catch (InvocationTargetException e) {
            //e.printStackTrace();
        } catch (NoSuchMethodException e) {
            //e.printStackTrace();
        }
        return result;
    }

    public String getImgPath() {
        return imgPath;
    }

    public void setImgPath(String imgPath) {
        this.imgPath = imgPath;
    }

    public WholeDayStatus GetWholeDay(int day) {//"workingDay", "leaveDay", "overtimeDay"
        try {
            if(!GetAttendanced(day)) {//非考勤人员
                return WholeDayStatus.noAttendanced;
            } else if (GetNoNeed(day)) {//非工作日
                if (GetOvertime(day).getLeft()>0) {//有加班
                    return WholeDayStatus.overtimeDay;
                } else {
                    return WholeDayStatus.noNeedDay;
                }
            } else if (GetWorkingHours(day).compareTo(GetLeave(day).getRight()) > 0) {//工作时长大于请假时长
                return WholeDayStatus.workingDay;
            } else {//请假时长大于等于工作时长
                return WholeDayStatus.leaveDay;
            }
        } catch (InvocationTargetException | IllegalAccessException e) {
            Logger.getLogger(getClass()).error("GetWholeDay error!", e);
        }
        return null;
    }

    public Boolean GetWorkingDay(int day) {//是否应出勤
        try { ////工作时长大于请假时长 或者 有加班
            return GetWorkingHours(day).compareTo(GetLeave(day).getRight()) > 0
                    || GetOvertime(day).getLeft()>0;
        } catch (InvocationTargetException | IllegalAccessException e) {
            Logger.getLogger(getClass()).error("GetWorkingDay error!", e);
        }
        return Boolean.FALSE;
    }

    private void MergeWholeDays(int maxday, Map<String, Set<Date>> wholeDays) { //获取整月的工作日、请假日、加班日数量
        int day = 1;
        int mday = maxday < 31 ? maxday : 31;
        for (; day <= mday; day++) {
            Date date = NewDateUtils.dateFromString(yearmonth+ StringUtils.right("0"+day,2),"yyyyMMdd");
            switch (GetWholeDay(day)) {
                case workingDay:
                    wholeDays.get(WholeDayStatus.workingDay.getName()).add(date);
                    break;
                case noNeedDay:
                    wholeDays.get(WholeDayStatus.noNeedDay.getName()).add(date);
                    break;
                case leaveDay:
                    wholeDays.get(WholeDayStatus.leaveDay.getName()).add(date);
                    break;
                case overtimeDay:
                    wholeDays.get(WholeDayStatus.overtimeDay.getName()).add(date);
                    break;
                case noAttendanced:
                    wholeDays.get(WholeDayStatus.noAttendanced.getName()).add(date);
                    break;
            }
        }
    }
    public Map<String, Set<Date>> GetWholeDays() { //获取整月的工作日、请假日、加班日数量
        Map<String, Set<Date>> wholeDays = WholeDayStatus.getEmptyWholeDays();
        MergeWholeDays(wholeDays);
        return wholeDays;
    }
    public void MergeWholeDays(Map<String, Set<Date>> wholeDays) { //获取整月的工作日、请假日、加班日数量
        int maxday=GetMaxDay();
        MergeWholeDays(maxday, wholeDays);
    }
}