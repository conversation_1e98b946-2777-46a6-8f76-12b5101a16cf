.Upload{
    width: 100%;
    height: 64px;
    border:1px dashed #ccc;
    text-align: center;
    margin-top:8px;
    border-radius: 5px;
}
.Upload p{
    color: #666;
}
.col-hr{
    float: left;
    border-left:1px dashed #ccc;
    height: 390px;
    margin-left: 23px;
}
.uploadify-button {
    display: inline;
    margin: 0;
    border: none;
    background-color: #e7e7e7;
    line-height: 24px;
    border-radius: 3px;
    padding: 3px 6px;
    font-size: 12px;
    font-weight: 400;
    color: #535353;
    cursor: pointer;
    text-decoration: none;

}
.uploadify-button:hover{
    background-color: #5d9cec;
    color: #fff;
}
.tips{
    line-height: 60px;
}
.uploadify-progress {
    display: block;
    width: 340px;
    height: 3px;
    background-color: #ccc;
    border-radius: 20px;
    border: 0px groove #666;
    vertical-align: middle;
    margin-top: 15px;
    float: left;
    margin: 4px 0;
}
.fileType{
    width: 30px;
    height: 40px;
    margin-top:12px;
    margin-left: 15px;
    margin-right:12px;
    float: left;

}
.progressnum, .delfilebtn, .uploadbtn, .up_percent {
    margin-right: 15px;
    margin-left: 0;
}
.up_filename{
    float: left;
    width:200px;
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis;
    margin-left: 0;
    text-align: left;
}
.uploadify-progress-bar {
    width: 0;
    height: 100%;
    border-radius:0;
    background-color: #0099FF;
}
.uploadbtn{
    margin: 0;
    outline: none;
    border: none;
    background: none;
    line-height: 1.42857;
    padding: 0;
    font-size: 12px;
    font-weight: 400;
    color: #0099FF;
    cursor: pointer;
    text-decoration: none;
    float: left;
}
.uploadDeleteBtn{
    float: left;
}
.ty-fileNull{
    width: 800px;
    height: 128px;
    border:2px dashed #0099FF;
    padding:20px 40px;
    text-align: center;
    background-color: #fff;
    margin-bottom: 8px;
}
.ty-fileNull h3{
    color: #333;
}
.ty-fileNull p{
    color: #aaa;
}
.seeDetail{
    display: block;
    font-size: 12px;
    padding: 2px 10px;
    border-radius: 3px;
    font-weight: bold;
    cursor: pointer;
    text-align: center;
}
.seeDetail:hover{
    color: #fff;
    background-color: #5d9cec;
}
ul.hel{
    position: absolute;
    width: 84px;
    right: 0;
    top: 25px;
    padding: 2px 0;
    background-color: #fff;
    box-shadow: 0 0 3px #aaa;
    z-index: 2;
}
.ulli{
    float: left;
    width: 100%;
}
.ulli span{
    display: block;
    height: 28px;
    line-height: 28px;
    text-align:left;
    margin:0 2px;
    padding: 0 4px;
    border-radius: 2px;
    transition: all .2s;
}
.ty-disabled, .ty-disabled span {
    color: #aaa;
    cursor: default;
}
.ulli:not(.ty-disabled) span{
    color: #5d9cec;
}
.ulli:not(.ty-disabled) span:hover{
    background-color: #5d9cec;
    color: #fff;
}
.ty-fileItem > .ty-fileHandle >.handleActive{
    background-color: #5d9cec;
    color:#fff;
}
.ty-fileHandle{
    position: absolute;
    right: 10px;
    bottom: 10px;
}
.ty-fileList {
    margin-top: 8px;
}
.progressnum,.up_percent{float: right;}