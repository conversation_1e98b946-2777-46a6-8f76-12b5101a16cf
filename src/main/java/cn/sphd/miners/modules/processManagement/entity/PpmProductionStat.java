package cn.sphd.miners.modules.processManagement.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by lyx on 2023/5/9
 * 生产_生产统计表  1.251工序1
 */
@Entity
@Table(name="t_ppm_production_stat")
public class PpmProductionStat implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;   //ID

    @Column(name="org"   , nullable=true , unique=false)
    private Integer org;   //机构ID

    @Column(name="product"   , nullable=true , unique=false)
    private Integer product;   //产品ID

    @Column(name="type")
    private Byte type; //类型:0-全部,1-产品,2-零组件

    @Column(name="process_count"   , nullable=true , unique=false)
    private Integer processCount;   //工序数量

    @Column(name="sub_process_count"   , nullable=true , unique=false)
    private Integer subProcessCount;   //子工序数量

    @Column(name="operate_count"   , nullable=true , unique=false)
    private Integer operateCount;   //操作数量

    @Column(name="package_count"   , nullable=true , unique=false)
    private Integer packageCount;   //套餐数量

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;   //备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;   //创建人id

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;  //创建人

    @Column(name="create_time"   , nullable=true , unique=false)
    private Date createTime;   //创建时间

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;   //修改人id

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;   //修改人

    @Column(name="update_time"   , nullable=true , unique=false)
    private Date updateTime;   //修改时间

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;  //操作:1-增,2-删,3-改

    @Column(name="previous_id"  , nullable=true , unique=false)
    private Integer previousId;  //修改前记录ID

    @Column(name="version_no"  , nullable=true , unique=false)
    private Integer versionNo;  //版本号,每次修改+1

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public Integer getProduct() {
        return product;
    }

    public void setProduct(Integer product) {
        this.product = product;
    }

    public Integer getProcessCount() {
        return processCount;
    }

    public void setProcessCount(Integer processCount) {
        this.processCount = processCount;
    }

    public Integer getSubProcessCount() {
        return subProcessCount;
    }

    public void setSubProcessCount(Integer subProcessCount) {
        this.subProcessCount = subProcessCount;
    }

    public Integer getOperateCount() {
        return operateCount;
    }

    public void setOperateCount(Integer operateCount) {
        this.operateCount = operateCount;
    }

    public Integer getPackageCount() {
        return packageCount;
    }

    public void setPackageCount(Integer packageCount) {
        this.packageCount = packageCount;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
}
