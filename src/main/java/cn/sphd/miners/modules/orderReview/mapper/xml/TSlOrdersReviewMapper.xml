<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sphd.miners.modules.orderReview.mapper.TSlOrdersReviewMapper">

    <resultMap type="TSlOrdersReview" id="TSlOrdersReviewResult">
        <result property="id" column="id"/>
        <result property="org" column="org"/>
        <result property="orders" column="orders"/>
        <result property="phase" column="phase"/>
        <result property="reviewNum" column="review_num"/>
        <result property="previousReview" column="previous_review"/>
        <result property="itemCount" column="item_count"/>
        <result property="scheduledCount" column="scheduled_count"/>
        <result property="reviewer" column="reviewer"/>
        <result property="reviewerName" column="reviewer_name"/>
        <result property="reviewDate" column="review_date"/>
        <result property="salesResult" column="sales_result"/>
        <result property="salesDate" column="sales_date"/>
        <result property="creator" column="creator"/>
        <result property="createName" column="create_name"/>
        <result property="createDate" column="create_date"/>
        <result property="updator" column="updator"/>
        <result property="updateName" column="update_name"/>
        <result property="updateDate" column="update_date"/>
        <result property="operation" column="operation"/>
        <result property="previousId" column="previous_id"/>
        <result property="versionNo" column="version_no"/>
    </resultMap>

    <sql id="selectTSlOrdersReviewVo">
        select id,
               org,
               orders,
               phase,
               review_num,
               previous_review,
               item_count,
               scheduled_count,
               reviewer,
               reviewer_name,
               review_date,
               sales_result,
               sales_date,
               creator,
               create_name,
               create_date,
               updator,
               update_name,
               update_date,
               operation,
               previous_id,
               version_no
        from t_sl_orders_review
    </sql>

    <select id="selectTSlOrdersReviewList" parameterType="TSlOrdersReview" resultMap="TSlOrdersReviewResult">
        <include refid="selectTSlOrdersReviewVo"/>
        <where>
            <if test="org != null ">and org = #{org}</if>
            <if test="orders != null ">and orders = #{orders}</if>
            <if test="phase != null ">and phase = #{phase}</if>
            <if test="reviewNum != null ">and review_num = #{reviewNum}</if>
            <if test="previousReview != null ">and previous_review = #{previousReview}</if>
            <if test="itemCount != null ">and item_count = #{itemCount}</if>
            <if test="scheduledCount != null ">and scheduled_count = #{scheduledCount}</if>
            <if test="reviewer != null ">and reviewer = #{reviewer}</if>
            <if test="reviewerName != null  and reviewerName != ''">and reviewer_name like concat('%', #{reviewerName},
                '%')
            </if>
            <if test="reviewDate != null ">and review_date = #{reviewDate}</if>
            <if test="salesResult != null ">and sales_result = #{salesResult}</if>
            <if test="salesDate != null ">and sales_date = #{salesDate}</if>
            <if test="creator != null ">and creator = #{creator}</if>
            <if test="createName != null  and createName != ''">and create_name like concat('%', #{createName}, '%')
            </if>
            <if test="createDate != null ">and create_date = #{createDate}</if>
            <if test="updator != null ">and updator = #{updator}</if>
            <if test="updateName != null  and updateName != ''">and update_name like concat('%', #{updateName}, '%')
            </if>
            <if test="updateDate != null ">and update_date = #{updateDate}</if>
            <if test="operation != null ">and operation = #{operation}</if>
            <if test="previousId != null ">and previous_id = #{previousId}</if>
            <if test="versionNo != null ">and version_no = #{versionNo}</if>
        </where>
    </select>

    <select id="selectTSlOrdersReviewById" parameterType="Long" resultMap="TSlOrdersReviewResult">
        <include refid="selectTSlOrdersReviewVo"/>
        where id = #{id}
    </select>
    <select id="selectTSlOrdersReviewListByCondition" parameterType="TSlOrdersItemReview"
            resultType="java.util.HashMap">
        select distinct so.id,
                        DATE_FORMAT(so.sign_date, '%Y-%m-%d %H:%i:%s')   as sign_date,
                        so.customer_name,
                        so.customer
                                                                         as customer_id,
                        so.sn,
                        soe.review_num,
                        soe.reviewer_name,
                        DATE_FORMAT(so.create_date, '%Y-%m-%d %H:%i:%s') as
                                                                            create_date,
                        so.create_name,
                        ifnull(soe.phase, 0)                             as
                                                                            phase,
                        so.amount,
                        so.customer_address,
                        so.invoice_require,
                        so.production_principal,
                        su.userName                                      as user_name
        from t_sl_orders so
                 left join t_sl_orders_review soe on soe.orders = so.id
                 left join t_sys_user su on
            so.production_principal = su.userID
        <where>
            <if test="org != null">
                and so.oid = #{org}
            </if>
            and phase not in (3, 4)
            <if test="reviewer != null">
                and ((phase in (0, 1, 2) and so.production_principal = #{reviewer}))
            </if>
            and ifnull(so.is_scheduled, 0) not in (2)
        </where>
    </select>


    <select id="selectTSlOrdersReviewByOrderId" parameterType="Long"
            resultType="cn.sphd.miners.modules.orderReview.entity.TSlOrdersReview">
        <include refid="selectTSlOrdersReviewVo"/>
        where orders = #{orders}
    </select>

    <insert id="insertTSlOrdersReview" parameterType="TSlOrdersReview">
        insert into t_sl_orders_review
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="org != null">org,</if>
            <if test="orders != null">orders,</if>
            <if test="phase != null">phase,</if>
            <if test="reviewNum != null">review_num,</if>
            <if test="previousReview != null">previous_review,</if>
            <if test="itemCount != null">item_count,</if>
            <if test="scheduledCount != null">scheduled_count,</if>
            <if test="reviewer != null">reviewer,</if>
            <if test="reviewerName != null">reviewer_name,</if>
            <if test="reviewDate != null">review_date,</if>
            <if test="salesResult != null">sales_result,</if>
            <if test="salesDate != null">sales_date,</if>
            <if test="creator != null">creator,</if>
            <if test="createName != null">create_name,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updator != null">updator,</if>
            <if test="updateName != null">update_name,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="operation != null">operation,</if>
            <if test="previousId != null">previous_id,</if>
            <if test="versionNo != null">version_no,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="org != null">#{org},</if>
            <if test="orders != null">#{orders},</if>
            <if test="phase != null">#{phase},</if>
            <if test="reviewNum != null">#{reviewNum},</if>
            <if test="previousReview != null">#{previousReview},</if>
            <if test="itemCount != null">#{itemCount},</if>
            <if test="scheduledCount != null">#{scheduledCount},</if>
            <if test="reviewer != null">#{reviewer},</if>
            <if test="reviewerName != null">#{reviewerName},</if>
            <if test="reviewDate != null">#{reviewDate},</if>
            <if test="salesResult != null">#{salesResult},</if>
            <if test="salesDate != null">#{salesDate},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createName != null">#{createName},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updator != null">#{updator},</if>
            <if test="updateName != null">#{updateName},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="operation != null">#{operation},</if>
            <if test="previousId != null">#{previousId},</if>
            <if test="versionNo != null">#{versionNo},</if>
        </trim>
    </insert>

    <update id="updateTSlOrdersReview" parameterType="TSlOrdersReview">
        update t_sl_orders_review
        <trim prefix="SET" suffixOverrides=",">
            <if test="org != null">org = #{org},</if>
            <if test="orders != null">orders = #{orders},</if>
            <if test="phase != null">phase = #{phase},</if>
            <if test="reviewNum != null">review_num = #{reviewNum},</if>
            <if test="previousReview != null">previous_review = #{previousReview},</if>
            <if test="itemCount != null">item_count = #{itemCount},</if>
            <if test="scheduledCount != null">scheduled_count = #{scheduledCount},</if>
            <if test="reviewer != null">reviewer = #{reviewer},</if>
            <if test="reviewerName != null">reviewer_name = #{reviewerName},</if>
            <if test="reviewDate != null">review_date = #{reviewDate},</if>
            <if test="salesResult != null">sales_result = #{salesResult},</if>
            <if test="salesDate != null">sales_date = #{salesDate},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="createName != null">create_name = #{createName},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updator != null">updator = #{updator},</if>
            <if test="updateName != null">update_name = #{updateName},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="operation != null">operation = #{operation},</if>
            <if test="previousId != null">previous_id = #{previousId},</if>
            <if test="versionNo != null">version_no = #{versionNo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTSlOrdersReviewById" parameterType="Long">
        delete
        from t_sl_orders_review
        where id = #{id}
    </delete>

    <delete id="deleteTSlOrdersReviewByIds" parameterType="String">
        delete from t_sl_orders_review where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>