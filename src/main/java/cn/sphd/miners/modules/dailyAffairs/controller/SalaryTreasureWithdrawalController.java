package cn.sphd.miners.modules.dailyAffairs.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.dailyAffairs.entity.SalaryTreasureWithdrawal;
import cn.sphd.miners.modules.dailyAffairs.service.SalaryTreasureWithdrawalService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.dto.UserHonePageDto;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by 19614 on 2020/9/23
 * 2.68/2.69 薪资宝转出
 */
@Controller
@RequestMapping("/salaryTreasureWithdrawal")
public class SalaryTreasureWithdrawalController {

    @Autowired
    SalaryTreasureWithdrawalService salaryTreasureWithdrawalService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    UserService userService;

    //申请数据，使用service层的
    public void addSalaryTreasure(){
        salaryTreasureWithdrawalService.addSalaryTreasure(null,null,null,null);
    }

    /**
    * <AUTHOR>
    * @Description  申请人待处理列表
    * @Date 2020/9/28
    */
    @ResponseBody
    @RequestMapping("/getSalaryApply.do")
    @MessageMapping("/getSalaryApply")
    public JsonResult getSalaryApply(User user,String sessionid){
        Map<String,Object> map = new HashMap<String,Object>();
//        JSONObject jsonObject = JSON.parseObject(json);
//        String sessionId=jsonObject.getString("session"); //登陆的设备标识
//        Integer userId = jsonObject.getInteger("userId");  //登录人id
        List<SalaryTreasureWithdrawal> salaryTreasureApply = salaryTreasureWithdrawalService.getSalaryApply(user.getUserID(),"10",null,null,2);  //付款审批待处理
        map.put("salaryTreasureApply",salaryTreasureApply);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/salaryTreasureApply",null,null,null,null,salaryTreasureApply);  //返回结果的订阅
       return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Description  付款审批审批人待处理/已批准列表
    * @Date 2020/9/28
    */
    @ResponseBody
    @RequestMapping("/getSalaryPayment.do")
    @MessageMapping("/getSalaryPayment")
    public JsonResult getSalaryPayment(User user,String sessionid){
        Map<String,Object> map = new HashMap<String,Object>();
//        JSONObject jsonObject = JSON.parseObject(json);
//        String sessionId=jsonObject.getString("session"); //登陆的设备标识
//        Integer userId = jsonObject.getInteger("userId");  //登录人id
        List<SalaryTreasureWithdrawal> salaryPaymentHandle = salaryTreasureWithdrawalService.getSalaryApproval(null,user.getUserID(),null,null,null,"1",24,null,null,2);  //付款审批待处理
        List<SalaryTreasureWithdrawal> salaryPaymentApproval = salaryTreasureWithdrawalService.getSalaryApproval(null,user.getUserID(),null,null,"10","2",24,null,null,2);  //付款审批已批准
        map.put("salaryPaymentHandle",salaryPaymentHandle);
        map.put("salaryPaymentApproval",salaryPaymentApproval);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/salaryTreasurePaymentHandle",null,null,null,null,salaryPaymentHandle);  //返回结果的订阅
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/salaryTreasurePaymentApproval",null,null,null,null,salaryPaymentApproval);
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Description  查看详情
    * @Date 2020/9/28
    */
    @ResponseBody
    @RequestMapping("/getSalaryTreasureDetail.do")
    public JsonResult getSalaryTreasureDetail(Integer salaryTreasureId){
        Map<String,Object> map = salaryTreasureWithdrawalService.getSalaryTreasureDetail(salaryTreasureId);
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Description  付款审批人的审批(无驳回只有批准)
    * @Date 2020/9/29
     * userId-登陆人id
     * approvalProcessId-审批流程id
    */
    @ResponseBody
    @RequestMapping("/getSalaryPaymentApproval.do")
    @MessageMapping("/getSalaryPaymentApproval")
    public JsonResult getSalaryPaymentApproval(String json,String sessionid,User user){
        JSONObject jsonObject = JSON.parseObject(json);
//        String sessionId=jsonObject.getString("session"); //登陆的设备标识
//        Integer userId=jsonObject.getInteger("userId"); //登陆人id
        Integer approvalProcessId = jsonObject.getInteger("approvalProcessId");  //审批流程id
        Map<String,Object> map = salaryTreasureWithdrawalService.getSalaryPaymentApproval(user.getUserID(),approvalProcessId);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/salaryTreasureNotice",null,null,null,null,map);
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Description  出纳-薪资宝转出-可付款/待复核/待付款列表
    * @Date 2020/9/29
    */
    @ResponseBody
    @RequestMapping("/getSalaryFinanceApproval.do")
    @MessageMapping("/getSalaryFinanceApproval")
    public JsonResult getSalaryFinanceApproval(String sessionid,User user){
        Map<String,Object> map = new HashMap<>();
//        JSONObject jsonObject = JSON.parseObject(json);
//        String sessionId=jsonObject.getString("session"); //登陆的设备标识
//        Integer userId=jsonObject.getInteger("userId"); //登陆人id
//        User user = userService.getUserByID(userId);
        List<SalaryTreasureWithdrawal> salaryTreasurePayable = salaryTreasureWithdrawalService.getSalaryApproval(user.getOid(),null,null,"at",null,"1",25,null,null,2);  //出纳-薪资宝转出的可付款(给所有的财务)
        List<SalaryTreasureWithdrawal> salaryTreasureReview = salaryTreasureWithdrawalService.getSalaryApproval(null,user.getUserID(),null,null,null,"1",26,null,null,2);  //出纳-薪资宝转出的待复核
        List<SalaryTreasureWithdrawal> salaryTreasurePaid = salaryTreasureWithdrawalService.getSalaryApproval(user.getOid(),null,null,"at",null,"1",27,null,null,2);  //出纳-薪资宝转出的待付款
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/salaryTreasurePayable",null,null,null,null,salaryTreasurePayable);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/salaryTreasureReview",null,null,null,null,salaryTreasureReview);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/salaryTreasurePaid",null,null,null,null,salaryTreasurePaid);
        map.put("salaryTreasurePayable",salaryTreasurePayable);
        map.put("salaryTreasureReview",salaryTreasureReview);
        map.put("salaryTreasurePaid",salaryTreasurePaid);
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Description  可付款审批(确定支付方式)
    * @Date 2020/9/29
    */
    @ResponseBody
    @RequestMapping("/treasurePayableApproval.do")
    @MessageMapping("/treasurePayableApproval")
    public JsonResult treasurePayableApproval(String json,String sessionid,User user) throws Exception {
        JSONObject jsonObject = JSON.parseObject(json);
//        String sessionId=jsonObject.getString("session"); //登陆的设备标识
//        Integer userId=jsonObject.getInteger("userId"); //登陆人id
        Integer approvalProcessId = jsonObject.getInteger("approvalProcessId");  //审批流程id
        String method = jsonObject.getString("method");  //支付方式:1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
        Date planDate = jsonObject.getDate("planDate");   //计划时间
        Date factDate = jsonObject.getDate("factDate");    //实际时间
        Integer accountId = jsonObject.getInteger("accountId");   //账户id
        String summary = jsonObject.getString("summary");  //摘要
        Map<String,Object> map = salaryTreasureWithdrawalService.treasurePayableApproval(user.getUserID(),approvalProcessId,method,planDate,factDate,accountId,summary);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/salaryTreasureNotice",null,null,null,null,map);
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Description  撤销接口
    * @Date 2020/9/29
    */
    @ResponseBody
    @RequestMapping("/treasureRevoke.do")
    @MessageMapping("/treasureRevoke")
    public JsonResult treasureRevoke(String json,String sessionid) throws Exception {
        JSONObject jsonObject = JSON.parseObject(json);
//        String sessionId=jsonObject.getString("session"); //登陆的设备标识
//        Integer userId=jsonObject.getInteger("userId"); //登陆人id
        Integer approvalProcessId = jsonObject.getInteger("approvalProcessId");  //审批流程id

        Map<String,Object> map = salaryTreasureWithdrawalService.treasureRevoke(approvalProcessId);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/salaryTreasureNotice",null,null,null,null,map);
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Description  待复核审批人待处理/已批准列表
    * @Date 2020/9/30
    */
    @ResponseBody
    @RequestMapping("/salaryReviewApproval.do")
    @MessageMapping("/salaryReviewApproval")
    public JsonResult salaryReviewApproval(String sessionid,User user){
        Map<String,Object> map = new HashMap<String,Object>();
//        JSONObject jsonObject = JSON.parseObject(json);
//        String sessionId=jsonObject.getString("session"); //登陆的设备标识
//        Integer userId = jsonObject.getInteger("userId");  //登录人id
        List<SalaryTreasureWithdrawal> salaryReviewHandle = salaryTreasureWithdrawalService.getSalaryApproval(null,user.getUserID(),null,null,null,"1",26,null,null,2);  //付款审批待处理
        List<SalaryTreasureWithdrawal> salaryReviewApproval = salaryTreasureWithdrawalService.getSalaryApproval(null,user.getUserID(),null,null,"10","2",26,null,null,2);  //付款审批已批准
        map.put("salaryReviewHandle",salaryReviewHandle);
        map.put("salaryReviewApproval",salaryReviewApproval);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/salaryTreasureReviewHandle",null,null,null,null,salaryReviewHandle);  //返回结果的订阅
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/salaryTreasureReviewApproval",null,null,null,null,salaryReviewApproval);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description  付款复核审批人的审批(无驳回)
     * @Date 2020/9/30
     */
    @ResponseBody
    @RequestMapping("/getSalaryReviewApproval.do")
    @MessageMapping("/getSalaryReviewApproval")
    public JsonResult getSalaryReviewApproval(String json,String sessionid,User user){
        JSONObject jsonObject = JSON.parseObject(json);
//        String sessionId=jsonObject.getString("session"); //登陆的设备标识
//        Integer userId=jsonObject.getInteger("userId"); //登陆人id
        Integer approvalProcessId = jsonObject.getInteger("approvalProcessId");  //审批流程id
        Map<String,Object> map = salaryTreasureWithdrawalService.getSalaryReviewApproval(user.getUserID(),approvalProcessId);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/salaryTreasureNotice",null,null,null,null,map);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description  出纳-薪资宝转出-待付款的审批（审批后可进账）
     * @Date 2020/9/30
     */
    @ResponseBody
    @RequestMapping("/getSalaryPaidApproval.do")
    @MessageMapping("/getSalaryPaidApproval")
    public JsonResult getSalaryPaidApproval(String json,String sessionid,User user) throws Exception {
        JSONObject jsonObject = JSON.parseObject(json);
//        String sessionId=jsonObject.getString("session"); //登陆的设备标识
//        Integer userId=jsonObject.getInteger("userId"); //登陆人id
        Integer approvalProcessId = jsonObject.getInteger("approvalProcessId");  //审批流程id
        Date factDate = jsonObject.getDate("factDate");  //实际日期
        String summary = jsonObject.getString("summary");  //摘要
        Map<String,Object> map = salaryTreasureWithdrawalService.getSalaryPaidApproval(user.getUserID(),approvalProcessId,factDate,summary);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/salaryTreasureNotice",null,null,null,null,map);
        return new JsonResult(1,map);
    }


    /**
    * <AUTHOR>
    * @Description  修改付款方式
    * @Date 2020/10/9
    */
    @ResponseBody
    @RequestMapping("/updateTreasureMethod.do")
    @MessageMapping("/updateTreasureMethod")
    public JsonResult updatePaymentMethod(String json,String sessionid,User user){
        Map<String,Object> map = new HashMap<String,Object>();
        JSONObject jsonObject = JSONObject.parseObject(json);
//        String sessionId=jsonObject.getString("session"); //登陆的设备标识
//        Integer userId = jsonObject.getInteger("userId");   //登陆人id
        Integer approvalProcessId = jsonObject.getInteger("approvalProcessId");  //审批过程id
        String method = jsonObject.getString("method");  //支付方式:1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
        Date planDate = jsonObject.getDate("planDate");   //计划时间
        Date factDate = jsonObject.getDate("factDate");   //实际时间
        Integer accountId = jsonObject.getInteger("accountId");   //账户id
        Integer type = jsonObject.getInteger("type");   //1-待复核的修改  2-待付款的修改
        String summary = jsonObject.getString("summary");  //摘要
        map = salaryTreasureWithdrawalService.updateTreasureMethod(user.getUserID(),approvalProcessId,method,planDate,factDate,accountId,type,summary);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/salaryTreasureNotice",null,null,null,null, map);  //订阅的通道
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2020/10/29 8:50
     *查看原定的付款方式
    */
    @ResponseBody
    @RequestMapping("/getTreasureMethod.do")
    @MessageMapping("/getTreasureMethod")
    public JsonResult getTreasureMethod(Integer salaryTreasureId){
        Map<String,Object> map = new HashMap<String,Object>();
        if (salaryTreasureId!=null){
            map = salaryTreasureWithdrawalService.getTreasureMethod(salaryTreasureId);
        }
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Description 获取申请人
    * @Date 2020/10/9
    */
    @ResponseBody
    @RequestMapping("/getTreasureApplyUserList.do")
    public JsonResult getTreasureApplyUserList(Integer userId,String userName,Integer businessType){
        List<UserHonePageDto> uList=salaryTreasureWithdrawalService.getTreasureApplyUserList(userId,userName,businessType);
        return new JsonResult(1,uList);
    }

    /**
    * <AUTHOR>
    * @Description  申请人查询
    * @Date 2020/9/30
    */
    @ResponseBody
    @RequestMapping("/getTreasureApply.do")
    public JsonResult getTreasureApply(User user,Integer type,String beginDate,String endDate) throws Exception {
        Map<String,Object> map = new HashMap<>();
        Date beginTime = null;
        Date endTime = null;
        if (1==type){  //1-近七日
            beginTime = NewDateUtils.changeDay(new Date(),-6);
            endTime = new Date();
        }else if (2==type){  //2-本月
            beginTime = NewDateUtils.changeMonth(new Date(),0);
            endTime = new Date();
        }else if (3==type){
            beginTime = NewDateUtils.today(new SimpleDateFormat("yyyy-MM-dd").parse(beginDate));
            endTime = NewDateUtils.today(new SimpleDateFormat("yyyy-MM-dd").parse(endDate));
        }
        List<SalaryTreasureWithdrawal> salaryTreasureWithdrawals = salaryTreasureWithdrawalService.getSalaryApply(user.getUserID(),"9",beginTime,endTime,2);
        map.put("salaryTreasureWithdrawals",salaryTreasureWithdrawals);
        map.put("beginDate",beginTime);
        map.put("endDate",endTime);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description  付款审批审批人查询
     * @Date 2020/9/30
     */
    @ResponseBody
    @RequestMapping("/etTreasurePaymentApproval.do")
    public JsonResult getTreasurePaymentApproval(User user,Integer applyUserId,Integer type,String beginDate,String endDate) throws Exception {
        Map<String,Object> map = new HashMap<>();
        Date beginTime = null;
        Date endTime = null;
        if (1==type){  //1-近七日
            beginTime = NewDateUtils.changeDay(new Date(),-6);
            endTime = new Date();
        }else if (2==type){  //2-本月
            beginTime = NewDateUtils.changeMonth(new Date(),0);
            endTime = new Date();
        }else if (3==type){
            beginTime = NewDateUtils.today(new SimpleDateFormat("yyyy-MM-dd").parse(beginDate));
            endTime = NewDateUtils.today(new SimpleDateFormat("yyyy-MM-dd").parse(endDate));
        }
        List<SalaryTreasureWithdrawal> salaryTreasureWithdrawals = salaryTreasureWithdrawalService.getSalaryApproval(null,user.getUserID(),applyUserId,null,"9","2",24,beginTime,endTime,2);
        map.put("salaryTreasureWithdrawals",salaryTreasureWithdrawals);
        map.put("beginDate",beginTime);
        map.put("endDate",endTime);
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Description  付款复核审批人查询
    * @Date 2020/10/9
    */
    @ResponseBody
    @RequestMapping("/getTreasureReviewApproval.do")
    public JsonResult getTreasureReviewApproval(User user,Integer applyUserId,Integer type,String beginDate,String endDate) throws Exception {
        Map<String,Object> map = new HashMap<>();
        Date beginTime = null;
        Date endTime = null;
        if (1==type){  //1-近七日
            beginTime = NewDateUtils.changeDay(new Date(),-6);
            endTime = new Date();
        }else if (2==type){  //2-本月
            beginTime = NewDateUtils.changeMonth(new Date(),0);
            endTime = new Date();
        }else if (3==type){
            beginTime = NewDateUtils.today(new SimpleDateFormat("yyyy-MM-dd").parse(beginDate));
            endTime = NewDateUtils.today(new SimpleDateFormat("yyyy-MM-dd").parse(endDate));
        }
        List<SalaryTreasureWithdrawal> salaryTreasureWithdrawals = salaryTreasureWithdrawalService.getSalaryApproval(null,user.getUserID(),applyUserId,null,"9","2",26,beginTime,endTime,2);
        map.put("salaryTreasureWithdrawals",salaryTreasureWithdrawals);
        map.put("beginDate",beginTime);
        map.put("endDate",endTime);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 出纳查询
     * @Date 2020/10/9
     */
    @ResponseBody
    @RequestMapping("/getTreasureFinanceApproval.do")
    public JsonResult getTreasureFinanceApproval(User user,Integer applyUserId,Integer type,String beginDate,String endDate) throws Exception {
        Map<String,Object> map = new HashMap<>();
        Date beginTime = null;
        Date endTime = null;
        if (1==type){  //1-近七日
            beginTime = NewDateUtils.changeDay(new Date(),-6);
            endTime = new Date();
        }else if (2==type){  //2-本月
            beginTime = NewDateUtils.changeMonth(new Date(),0);
            endTime = new Date();
        }else if (3==type){
            beginTime = NewDateUtils.today(new SimpleDateFormat("yyyy-MM-dd").parse(beginDate));
            endTime = NewDateUtils.today(new SimpleDateFormat("yyyy-MM-dd").parse(endDate));
        }
        List<SalaryTreasureWithdrawal> salaryTreasureWithdrawals = salaryTreasureWithdrawalService.getSalaryApproval(null,user.getUserID(),applyUserId,null,"9","2",27,beginTime,endTime,2);
        map.put("salaryTreasureWithdrawals",salaryTreasureWithdrawals);
        map.put("beginDate",beginTime);
        map.put("endDate",endTime);
        return new JsonResult(1,map);
    }
}
