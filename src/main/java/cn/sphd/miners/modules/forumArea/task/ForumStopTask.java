package cn.sphd.miners.modules.forumArea.task;

import cn.sphd.miners.modules.forumArea.service.ForumService;
import cn.sphd.miners.modules.system.service.DlmService;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.ParseException;
import java.text.SimpleDateFormat;

/**
 * Created by 朱思旭 on 2020/12/15.
 */
public class ForumStopTask {

    @Autowired
    DlmService dlmService;
    @Autowired
    ForumService forumService;



    public void stopUsingForumTaskDay() throws ParseException {
        //wyu:获取分布式锁
        String lockKey, methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        if ((lockKey=dlmService.getLock(methodName)) != null) {
            System.out.println("讨论区和阅览时定时任务开始：" + new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
            forumService.stopUsingForumPost();
            forumService.removeForumPost();
            System.out.println("讨论区和阅览时定时任务结束：" + new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
            dlmService.releaseLock(methodName, lockKey);
        }
    }



}
