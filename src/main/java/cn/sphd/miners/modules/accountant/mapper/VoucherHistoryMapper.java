package cn.sphd.miners.modules.accountant.mapper;

import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.accountant.entity.TAccountantVoucherHistory;
import cn.sphd.miners.modules.accountant.entity.TAccountantVoucherSubjectHistory;
import org.springframework.stereotype.Component;

import java.util.List;
@Component
public interface VoucherHistoryMapper extends BaseMapper<TAccountantVoucherHistory> {

    /*从凭证历史表中得到凭证*/
    List<TAccountantVoucherHistory> getVouchersFormHistory(TAccountantVoucherHistory param);

    /*根据voucher_id字段更新历史表的凭证审批状态*/
    int updateVouchersHistoryApproveStatus(TAccountantVoucherHistory param);


    /*根据voucher_id得到历史凭证*/
    TAccountantVoucherHistory getVoucherHistoryByVoucherID(TAccountantVoucherHistory param);

    /*根据voucher_id 和 direction 得到历史凭证对应的科目*/
    TAccountantVoucherSubjectHistory getHistorySubject(TAccountantVoucherSubjectHistory param);

    /*删除历史科目表对应的科目明细,有两个，借方明细和贷方明细*/
    int deleteHistorySubjectDetail(QueryData qd);

    /*根据voucher_id删除历史凭证对应的科目*/
    int deleteHistoryVoucherSubject(int voucherID);

    /*根据voucher_id删除历史凭证*/
    int deleteHistoryVoucherByVoucherID(int voucherID);

    /*点击结帐按钮时把上个月标记为已结帐*/
    int updateHistorySettle(TAccountantVoucherHistory param);

    List<TAccountantVoucherHistory> getOrgHistoryVouchers(int org);

    void updateHistoryByDate(TAccountantVoucherHistory voucherHistory);
}