<template>
  <div id="forumApproval">
    <TY_NavTop title="讨论发起审批" isSearch="true" @toggleList="search"></TY_NavTop>
    <div class="container">
      <div class="nowTime">有待审批的讨论主题有如下{{toApprove.length}}个</div>
      <div>
        <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in toApprove" v-bind:key="index">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <div>{{item.title}}</div>
              <div style="margin-left: 20px">讨论发起人 {{item.createName}} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            </div>
          </div>
          <div class="ui-cell__ft"></div>
        </a>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'forumApproval',
  data () {
    return {
      toApprove: [],
      listenersUid: []
    }
  },
  created () {
    let that = this
    this.axios.post('../../../forum/getLastApproveForum.do', {
      userID: this.sphdSocket.user.userID,
      type: 1
    })
      .then(function (response) {
        console.log(response)
        let getData = response.data.data
        that.toApprove = getData.listApproveForum
      })
      .catch(function (error) {
        console.log(error)
      })
    this.listenersUid = [
      this.sphdSocket.subscribe('approvalForumPost', function (data) {
        console.log('approvalForumPost user:' + data)
        let getData = JSON.parse(data)
        let personnelReimburse = getData.forumPost
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.toApprove.unshift(personnelReimburse)
        } else if (operate < 0) {
          let reimburseId = personnelReimburse.id
          let _index = -1
          that.toApprove.forEach(function (item, index) {
            if (item.id === reimburseId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.toApprove.splice(_index, 1)
          }
        }
      }, null, 'custom', that.sphdSocket.org.id + 'AuthApproval')
    ]
    console.log(that.sphdSocket.org.id)
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    jump: function (id) {
      this.$router.push({
        path: `/discussDetail/${id}/approveLast`
      })
    },
    search: function () {
      this.$router.push({
        path: `/discussQuery/approveLast`
      })
    }
  }
}
</script>
