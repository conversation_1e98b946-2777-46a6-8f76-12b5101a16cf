package cn.sphd.miners.modules.equipment.entity;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 对象 t_equ_control_image
 * 
 * <AUTHOR>
 * @date 2024-04-13
 */
public class TEquControlImage
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 机构ID */

    private Long org;

    /** 控制点ID */

    private Long controlPoint;

    /** 标题 */

    private String title;

    /** 描述 */

    private String content;

    /** 正常图标路径 */

    private String normalPath;

    /** 缩略图路径 */

    private String thumbnailPath;

    /** 排序 */

    private Long orders;

    /** 备注 */

    private String memo;

    /** 创建人id */

    private Long creator;

    /** 创建人 */

    private String createName;

    /** 创建时间 */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createDate;

    /** 修改人id */

    private Long updator;

    /** 修改人 */

    private String updateName;

    /** 修改时间 */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateDate;

    /** 操作:1-增,2-删,3-改,4-启停用 */

    private Long operation;

    /** 修改前记录ID */

    private Long previousId;

    /** 版本号,每次修改+1 */

    private Long versionNo;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOrg(Long org) 
    {
        this.org = org;
    }

    public Long getOrg() 
    {
        return org;
    }
    public void setControlPoint(Long controlPoint) 
    {
        this.controlPoint = controlPoint;
    }

    public Long getControlPoint() 
    {
        return controlPoint;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }
    public void setNormalPath(String normalPath) 
    {
        this.normalPath = normalPath;
    }

    public String getNormalPath() 
    {
        return normalPath;
    }
    public void setThumbnailPath(String thumbnailPath) 
    {
        this.thumbnailPath = thumbnailPath;
    }

    public String getThumbnailPath() 
    {
        return thumbnailPath;
    }
    public void setOrders(Long orders) 
    {
        this.orders = orders;
    }

    public Long getOrders() 
    {
        return orders;
    }
    public void setMemo(String memo) 
    {
        this.memo = memo;
    }

    public String getMemo() 
    {
        return memo;
    }
    public void setCreator(Long creator) 
    {
        this.creator = creator;
    }

    public Long getCreator() 
    {
        return creator;
    }
    public void setCreateName(String createName) 
    {
        this.createName = createName;
    }

    public String getCreateName() 
    {
        return createName;
    }
    public void setCreateDate(Date createDate) 
    {
        this.createDate = createDate;
    }

    public Date getCreateDate() 
    {
        return createDate;
    }
    public void setUpdator(Long updator) 
    {
        this.updator = updator;
    }

    public Long getUpdator() 
    {
        return updator;
    }
    public void setUpdateName(String updateName) 
    {
        this.updateName = updateName;
    }

    public String getUpdateName() 
    {
        return updateName;
    }
    public void setUpdateDate(Date updateDate) 
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() 
    {
        return updateDate;
    }
    public void setOperation(Long operation) 
    {
        this.operation = operation;
    }

    public Long getOperation() 
    {
        return operation;
    }
    public void setPreviousId(Long previousId) 
    {
        this.previousId = previousId;
    }

    public Long getPreviousId() 
    {
        return previousId;
    }
    public void setVersionNo(Long versionNo) 
    {
        this.versionNo = versionNo;
    }

    public Long getVersionNo() 
    {
        return versionNo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("org", getOrg())
            .append("controlPoint", getControlPoint())
            .append("title", getTitle())
            .append("content", getContent())
            .append("normalPath", getNormalPath())
            .append("thumbnailPath", getThumbnailPath())
            .append("orders", getOrders())
            .append("memo", getMemo())
            .append("creator", getCreator())
            .append("createName", getCreateName())
            .append("createDate", getCreateDate())
            .append("updator", getUpdator())
            .append("updateName", getUpdateName())
            .append("updateDate", getUpdateDate())
            .append("operation", getOperation())
            .append("previousId", getPreviousId())
            .append("versionNo", getVersionNo())
            .toString();
    }
}
