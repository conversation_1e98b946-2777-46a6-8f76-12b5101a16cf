body,html{
  padding: 0;
  margin: 0;
  font: 12px/1.5 "Helvetica Neue", "Luxi Sans", "DejaVu Sans", <PERSON><PERSON><PERSON>, "Hiragino Sans GB", <PERSON><PERSON><PERSON><PERSON>, "Microsoft YaHei";
  height: 100%;
}
h3,h4,p,ul li{
  margin: 0;
  padding: 0;
}
ul li{
  list-style: none;
}
[v-cloak] {
  display: none !important;
}
.clearfix:before,
.clearfix:after{
  display: table;
  content: " ";
}
.clearfix:after{
  clear: both;
}
.text-right{
  text-align: right;
}


/* -------------------------- Color ---------------------------*/

.color-red{
  color: @red
}
.color-green{
  color: @green
}
.color-blue{
  color: @blue
}
.color-red{
  color: @orange
}

/* -------------------------- link ---------------------------*/
.link-red{
  color: @red;
  cursor: pointer;
  &:hover{
    color: darken(@red,5%);
  }
}
.link-green{
  color: @green ;
  cursor: pointer;
  &:hover{
    color: darken(@green,5%);
  }
}
.link-blue{
  color: @blue;
  cursor: pointer;
  &:hover{
    color: darken(@blue,5%);
  }
}
.link-red{
  color: @orange;
  cursor: pointer;
  &:hover{
    color: darken(@orange,5%);
  }
}
/* -------------------------- Cell ---------------------------*/

.ui-cells{
  padding-top:0.6rem;
  overflow: auto;
  .ui-cells__title{
    margin-top: .77rem;
    margin-bottom: .3rem;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    color: #999999;
    font-size: 1.2rem;
  }
}
.ui-cells__title + .ui-cells {
  margin-top: 0;
}
.page .ui-cells:nth-of-type(1){
  margin-top: 0;
}
.ui-cell{
  background-color: @white;
  padding: 0.8rem 1.5rem;
  position: relative;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  font-size: 1.2rem;
  overflow: hidden;
  color:inherit;
  cursor:pointer;
  &:before{
    content: " ";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 0.1rem;
    border-top: 0.1rem solid @color-list-line;
    color: @color-list-line;
    -webkit-transform-origin: 0 0;
    transform-origin: 0 0;
    -webkit-transform: scaleY(.5);
    transform: scaleY(.5);
  }
  &:first-child{
    &:before{
      display: none;
    }
  }
  .ui-cell__hd{
    padding-right: 0.5em;
  }
  .ui-cell__bd{
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    flex: 1;
    .ui-cell_con_s{
      font-size: 12px;
      line-height:1.2;
    }
  }
}
.ui-message{
  display: block;
  width: 98%;
  margin: 4px auto 0;
  position: relative;
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
  font-size: 1.2rem;
  color:inherit;
  /* max-height: 492px;
  overflow: auto; */
  .sendTime_wrapper{
    position: relative;
    margin-bottom: 8px;
    text-align: center;
    .sendTime{
      position: relative;
      background-color: lighten(@blue,20%);
      color: #fff;
      padding: 3px 12px;
      font-weight: normal;
      border-radius: 10px;
      font-size: 12px;
    }
  }
  .sendContainer{
    padding: 1rem;
    background-color: @white;
    border-radius: 3px;
    box-shadow: 0 1px 2px lighten(@grayC,8%);
  }
}

/* arrow-right */

.ui-cell__ft {
  text-align: right;
  color: @gray9;
  padding-right: 1.3rem;
  &:after{
    content: " ";
    display: inline-block;
    height: 0.6rem;
    width: 0.6rem;
    border-width: 0.1rem 0.1rem 0 0;
    border-color: @color-arrow;
    border-style: solid;
    -webkit-transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
    transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
    position: relative;
    top: -2px;
    position: absolute;
    top: 50%;
    margin-top: -0.4rem;
    right: 1.2rem;
  }
}
.ui-ti{
  height: 2rem;
  width: 2rem;
  display: inline-block;
  border-radius: 1rem;
  background-color: @orange;
  color: @white;
  line-height:2rem;
  text-align: center;

}
.ui-row{
  position: relative;
  font-size: 12px;
  margin-top: 4px;
}


/* -------------------------- Close ---------------------------*/
.close{
  display: inline-block;
  width: 15px;
  height: 1px;
  background: #fff;
  line-height: 0;
  font-size: 0;
  vertical-align: middle;
  -webkit-transform: rotate(45deg);
  &:after{
    content: '/';
    display: block;
    width: 15px;
    height: 1px;
    background: #fff;
    -webkit-transform: rotate(-90deg);
    cursor: pointer;
  }
}
.arrow{
  text-align: right;
  color: @gray9;
  padding-right: 1.3rem;
  &:after{
    content: " ";
    display: inline-block;
    height: 0.6rem;
    width: 0.6rem;
    border-color: @white;
    border-style: solid;
    -webkit-transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
    transform: matrix(0.71, 0.71, -0.71, 0.71, 0, 0);
    position: relative;
    top: -2px;
    position: absolute;
    top: 50%;
    margin-top: -0.3rem;
    left: 1.2rem;
  }
}
.arrow-left:after{
  border-width: 0 0 0.1rem 0.1rem;
}
.arrow-right:after{
  border-width: 0 0.1rem 0.1rem 0;
}
.arrow-top:after{
  border-width: 0 0 0.1rem 0.1rem;
}
.arrow-bottom:after{
  border-width: 0.1rem 0 0 0.1rem;
}

.arrow-close:after{
  border-width: 0.1rem 0 0 0.1rem;
}
.messageContainer{
  height: 100%;
  overflow: hidden;
}
.messageHead{
  width: 100%;
  padding: 0 20px;
  color: #fff;
  background-color: #colors[primary];
  box-sizing: border-box;
  z-index: 2;
}
.messageHead .user_logo-wrapper{
  float: left;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: rgba(255,255,255,0.3);
  margin: 20px 10px;
}
.messageHead .user_logo{
  display: block;
  background-color: #fff;
  border-radius: 50%;
  margin: 5px;
  width: 54px;
  height: 54px;
}
.messageHead .user_info{
  float: left;
  color: #fff;
  padding:30px 20px 0;
}
.messageHead .user_info .user_name{
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  text-align: left;
}
.messageHead .user_info .user_phone{
  font-size: 14px;
  color: #f1f1f1;
}
.message_close{
  position: absolute;
  top: 15px;
  right: 10px;
  cursor: pointer;
  text-align: center;
}
.messageCon{
  overflow: auto;
  background-color: #eee;
  position :relative;
  box-sizing: border-box;
}
.message_count{
  display: inline-block;
  background-color: #colors[primary];
  color: #fff;
  border-radius: 10px;
  padding:0 6px;
  text-align: center;
  line-height: 18px;
  margin-left: 8px;
}

.el-message-box {
  width: 260px !important;
}

.tab-title{
  padding: 0.5rem 1rem;
  background-color: #fafafb;
  text-align: center;
  border-bottom: 1px solid #eaeaea;
  margin-top: 8px;
  color: #909399;
  font-weight: bold;
}

.el-message{
  min-width: 99%;
  top: 48px;
}

.el-dialog__title{font-size:16px;}

/* -------------------------- Button ---------------------------*/
.ui-btn-area{
  margin:0.8rem;
}
.ui-btn {
  position: relative;
  height: 3.8rem;
  display: block;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1.4rem;
  padding-right: 1.4rem;
  box-sizing: border-box;
  font-size: 1.2rem;
  text-align: center;
  text-decoration: none;
  line-height: 3.8rem;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  overflow: hidden;
  border:none;
  outline: none;
  width: 100%;
  cursor: pointer;
  background-color: #f1f1f1;
  color: #787878;
  &:disabled{
    cursor: default;
  }
}
.ui-btn_primary {
  color: #fff;
  background-color: #colors[primary];
  &:hover{
    background-color: darken(#colors[primary],5%);
  }
  &:disabled{
    background-color: lighten(#colors[primary],30%);
  }
}
.ui-btn_success {
  color: #fff;
  background-color: #colors[success];
  &:hover{
    background-color: darken(#colors[success],5%);
  }
  &:disabled{
    background-color: lighten(#colors[success],30%);
  }
}
.ui-btn_error {
  color: #fff;
  background-color: #colors[error];
  &:hover{
    background-color: darken(#colors[error],5%);
  }
  &:disabled{
    background-color: lighten(#colors[error],30%);
  }
}
.ui-btn_info {
  color: #fff;
  background-color: #colors[info];
  &:hover{
    background-color: darken(#colors[info],5%);
  }
  &:disabled{
    background-color: lighten(#colors[info],20%);
  }
}
.ui-btn_warning {
  color: #fff;
  background-color: #colors[warning];
  &:hover{
    background-color: darken(#colors[warning],5%);
  }
  &:disabled{
    background-color: lighten(#colors[warning],30%);
  }
}
.ui-btn_disabled{
  color: #fff;
  background-color: #colors[disabled];
  &:hover{
    background-color: #colors[disabled];
  }
  &:disabled{
    background-color: #colors[disabled];
  }
}
/* -------------------------- Dialog ---------------------------*/

.bounce{
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 9999;
  .bounceContainer{
    width: 86%;
    background: #fff;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 4px;
    position: relative;
    .bounceHead{
      width: 100%;
      color: #696969;
      font-weight: 600;
      padding: 8px 50px 0 16px;
      box-sizing: border-box;
      font-size: 16px;
    }
    .bounceCon{
      color: #797979;
      min-height: 52px;
      padding: 0 20px;
      box-sizing: border-box;
      .ui-cells{
        margin-top: 0;
      }
    }
    .bounceFoot{
      width: 100%;
      color: #696969;
      padding: 0 16px 8px;
      box-sizing: border-box;
      text-align: right;
      .ui-btn{
        width: 60px;
        padding: 0;
        height: 30px;
        line-height: 30px;
        border-radius: 2px;
        display: inline-block;
      }
    }
    .close-btn{
      position: absolute;
      top: 16px;
      right: 16px;
      width: 30px;
      height: 30px;
      line-height: 30px;
      text-align: center;
      font-size: 18px;
      cursor: pointer;
      &:hover{
        font-weight: 600;
      }
    }
  }
}

/* ------------------------- table --------------------------- */
.ui-table{
  width:100%;
  table-layout: auto;
  border-collapse: separate;
  border-left:1px solid #ebeef5;
  border-top:1px solid #ebeef5;
  border-spacing: 0;
  thead{
    background-color: #f2f2f2;
    color: #5a5c60;
    font-weight: 500;
    td{
      background-color: #f4f6f9;
      color: #46516a;
      font-weight: bold;
    }
  }
  tbody{
    color: #606266;
  }
  td{
    padding: 8px 10px;
    min-width: 0;
    box-sizing: border-box;
    text-overflow: ellipsis;
    vertical-align: middle;
    position: relative;
    text-align: left;
    border-bottom: 1px solid #ebeef5;
    background-color: #fff;
    border-right: 1px solid #ebeef5;
  }
}
.ui-table-center td{
  text-align: center;
}
.ui-table-striped tbody tr:nth-of-type(2n) td{
  background-color: #f9f9f9;
}
.ui-table-none{
  border: none;
  td{
    border: none;
  }
}

.table {
  width: 100%;
  border-spacing: 0;
  border-collapse: collapse;
  td {
    border: 1px solid #d7d7d7;
    text-align: center;
    word-wrap: break-word;
    height: 30px;
    word-spacing: normal;
    word-break: break-all;
    color: #101010
  }
  thead td {
    background-color: #fff;
  }
  tbody td {
    background-color: #f5f5f5;
  }

}

/*  table  */
.table{ width:100%;   }
.table td{ border:1px solid #ebeef5; font-size:14px; text-align:left; padding:0.4em 0.8em; word-wrap: break-word;  word-spacing:normal;word-break: break-all ;color:#606266     }
.table thead th, .table tbody .th{ border:1px solid #ebeef5;  background:#f5f8ff; color:#909399 ; font-weight: bold}
.table tbody td{ background-color:#fff; }

/* ------------------------- alert --------------------------- */
.ui-alert{
  width: 100%;
  padding: 6px;
  box-sizing: border-box;
  position: relative;
  font-size: 12px;
  color: #909399;
  overflow: hidden;
  opacity: 1;
  display: flex;
  align-items: center;
  transition: opacity .2s;
  margin-bottom: 2px;
}
.ui-alert i.fa{
  margin:0 6px;
  color: #909399;
  display: inline;
  width: auto;
}
.ui-alert-success{
  background-color: #f0f9eb;  color:#67c23a;
}
.ui-alert-info{
  background-color: #f4f4f5;
  color:#909399;
}
.ui-alert-info i.fa {
  color:#909399;
}
.ui-alert-warning{
  background-color: #fdf6ec;
  color:#e6a23c;
}
.ui-alert-warning i.fa {
  color:#e6a23c;
}
.ui-alert-error{
  background-color: #fef0f0;
  color:#f56c6c;
}
.ui-alert-error i.fa {
  color:#f56c6c;
}

@green-blue   : #36c6d3;
.tabs {
  font-size: 14px;
  color:#657180;
  width:100%;
  overflow:hidden;
  z-index:10;
  height:100%;
  overflow: hidden;
  /*不要加height：100%*/
}
.tabs-bar{
  width: 100%;
  overflow: hidden;
  background-color: @green-blue;
  &:after{
    content:'';
    display: block;
    width: 100%;
    height: 1px;
    background: #d7dde4;
    margin-top:-1px;
  }
}
.tabs-tab {
  float: left;
  display: block;
  width: 50%;
  height: 42px;
  line-height: 42px;
  cursor: pointer;
  position: relative;
  text-align: center;
  color: lighten(@green-blue,30%);
}
.tabs-tab-active {
  color: #fff;
  background-color: darken(@green-blue,5%);
}
.tabs-tab-active:after{
  content: '';
  position: absolute;
  display: block;
  border: 6px solid transparent;
  border-bottom-color: transparent;
  border-bottom-color: #eee;
  bottom: 0;
  left: calc(50% - 6px);
}
.tabs-content{
  height: calc(100% - 90px);
  overflow: auto;
}

.el-popup-parent--hidden{
  padding-right: 0 !important;
}
