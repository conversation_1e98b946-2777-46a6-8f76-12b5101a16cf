package cn.sphd.miners.modules.generalAffairs.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

/**
 * PersonnelAttendanceManual_in_generalAffairs 1.342考勤打卡 手工录入数据表
 * @author: wuyu
 * @since: 4.0
 * @date: 2025-05-08 15:20:02
 **/
@Entity
@Table( name ="t_personnel_attendance_manual" )
public class PersonnelAttendanceManual extends BaseEntity {
    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;//`id` INTEGER NOT NULL AUTO_INCREMENT COMMENT '主键',
    @Column(name = "pau_id")
    private Integer pauId;//`pau_id` INTEGER DEFAULT NULL COMMENT '考勤表id',
    @Column
    private Date date;//`date` DATETIME(3) DEFAULT NULL COMMENT '考勤日期',
    @Column(name = "begin_state")
    private Byte beginState;//`begin_state` TINYINT DEFAULT NULL COMMENT '上班状态:0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他',
    @Column(name = "end_state")
    private Byte endState;//`end_state` TINYINT DEFAULT NULL COMMENT '下班状态:0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他',
    @Column
    private Byte type;//`type` TINYINT DEFAULT NULL COMMENT '总类型：0-未考勤，1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,9-其他，8-加班(周六日加班的)，-1 无需考勤',
    @Column
    private String absenteeisms;//`absenteeisms` VARCHAR(255) DEFAULT NULL COMMENT '旷工时间对数组JSON[{"begin":"2025-05-08 08:00:00","end":"2025-05-08 09:00:00"}]',
    @Column(name = "begin_commited")
    private Boolean beginCommited;//`begin_commited` BOOLEAN DEFAULT NULL COMMENT '上班状态已写入考勤明细：NULL-没有数据，FALSE-未更新到明细，TRUE-已更新到明细',
    @Column(name = "end_commited")
    private Boolean endCommited;//`end_commited` BOOLEAN DEFAULT NULL COMMENT '下班状态已写入考勤明细：NULL-没有数据，FALSE-未更新到明细，TRUE-已更新到明细',
    @Column(name = "abs_md5")
    private String absMd5;//`abs_md5` CHAR(32) DEFAULT NULL COMMENT '旷工写入考勤明细后的md5，NULL-absenteeisms没有旷工数据',
    @Column
    private Integer creator;//`creator` INTEGER DEFAULT NULL COMMENT '创建人',
    @Column(name = "create_name", length = 32)
    private String createName;//`create_name` VARCHAR(32) DEFAULT NULL COMMENT '创建人姓名'
    @Column(name = "create_time")
    @CreationTimestamp
    private Date createTime;//`create_time` DATETIME(3) NULL DEFAULT NULL COMMENT '创建时间',
    @Column
    private Integer updator;//`updator` INTEGER DEFAULT NULL COMMENT '修改人',
    @Column(name = "update_name", length = 32)
    private String updateName;//`update_name` VARCHAR(32) DEFAULT NULL COMMENT '修改人姓名',
    @Column(name = "update_time")
    @UpdateTimestamp
    private Date updateTime;//`update_time` DATETIME(3) NULL DEFAULT NULL COMMENT '修改时间',

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getPauId() {
        return pauId;
    }

    public void setPauId(Integer pauId) {
        this.pauId = pauId;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Byte getBeginState() {
        return beginState;
    }

    public void setBeginState(Byte beginState) {
        this.beginState = beginState;
    }

    public Byte getEndState() {
        return endState;
    }

    public void setEndState(Byte endState) {
        this.endState = endState;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public String getAbsenteeisms() {
        return absenteeisms;
    }

    public void setAbsenteeisms(String absenteeisms) {
        this.absenteeisms = absenteeisms;
    }

    public Boolean getBeginCommited() {
        return beginCommited;
    }

    public void setBeginCommited(Boolean beginCommited) {
        this.beginCommited = beginCommited;
    }

    public Boolean getEndCommited() {
        return endCommited;
    }

    public void setEndCommited(Boolean endCommited) {
        this.endCommited = endCommited;
    }

    public String getAbsMd5() {
        return absMd5;
    }

    public void setAbsMd5(String absMd5) {
        this.absMd5 = absMd5;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
