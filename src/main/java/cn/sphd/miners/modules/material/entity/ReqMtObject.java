package cn.sphd.miners.modules.material.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName ReqMt
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/9 15:40
 * @Version 1.0
 */

public class ReqMtObject implements Serializable {
    private String mtBasesList;
    private List<MtBase> mtBaseList;
    private Integer org;
    private String isPurchased;//1是采购过 0是未采购过
    private Integer importSum;//导入总数
    private Integer falseImportSum;//无法导入总数
    private Integer tureImportSum;//可导入总数
    private Integer buttonState;

    private String category;

    public Integer getButtonState() {
        return buttonState;
    }

    public void setButtonState(Integer buttonState) {
        this.buttonState = buttonState;
    }

    public String getMtBasesList() {
        return mtBasesList;
    }

    public void setMtBasesList(String mtBasesList) {
        this.mtBasesList = mtBasesList;
    }

    public String getIsPurchased() {
        return isPurchased;
    }

    public void setIsPurchased(String isPurchased) {
        this.isPurchased = isPurchased;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getTureImportSum() {
        return tureImportSum;
    }

    public void setTureImportSum(Integer tureImportSum) {
        this.tureImportSum = tureImportSum;
    }

    public List<MtBase> getMtBaseList() {
        return mtBaseList;
    }

    public void setMtBaseList(List<MtBase> mtBaseList) {
        this.mtBaseList = mtBaseList;
    }

    public Integer getImportSum() {
        return importSum;
    }

    public void setImportSum(Integer importSum) {
        this.importSum = importSum;
    }

    public Integer getFalseImportSum() {
        return falseImportSum;
    }

    public void setFalseImportSum(Integer falseImportSum) {
        this.falseImportSum = falseImportSum;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }
}
