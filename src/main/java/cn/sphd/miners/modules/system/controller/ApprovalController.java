package cn.sphd.miners.modules.system.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.entity.PersonnelReimburse;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.personal.service.LeaveService;
import cn.sphd.miners.modules.personal.service.OvertimeService;
import cn.sphd.miners.modules.system.dto.PopedomDto;
import cn.sphd.miners.modules.system.dto.UserDto;
import cn.sphd.miners.modules.system.dto.UserHonePageDto;
import cn.sphd.miners.modules.system.entity.*;
import cn.sphd.miners.modules.system.service.ApprovalService;
import cn.sphd.miners.modules.system.service.OrgService;
import cn.sphd.miners.modules.system.service.PopedomService;
import cn.sphd.miners.modules.system.service.UserService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

/**
 * Created by Administrator on 2016/12/19.
 * 李旭
 * 审批流程定义
 */
@Controller
@RequestMapping("/approval")
public class ApprovalController {

    @Autowired
    OrgService orgService;
    @Autowired
    ApprovalService approvalService;
    @Autowired
    UserService userService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    PopedomService popedomService;
    @Autowired
    LeaveService leaveService;
    @Autowired
    OvertimeService overtimeService;


    /**
     * <AUTHOR>
     * @Date 2017/2/10 11:30
     * 获取可选审批者列表接口
     * 2.30手机端审批设置修改-2019/11/8 李娅星
     * 此接口改为/popedom/getOptionalUser.do与此接口的区别是返回值格式不同 1.186机构流程优化2101-2021/9/23 李娅星
     */
    @ResponseBody
    @RequestMapping("/getOptionalUser.do")
    public void getOptionalUser(User user, HttpServletResponse response,Integer... userId) throws IOException {
        Integer oid=user.getOid();
        List<User> userList = new ArrayList<User>();
        User superUser=userService.getUserByRoleCode(oid,"super");
        userList.add(superUser);
        List<User> users=userService.getNotDirectLowerGrade(oid,userId);
        if (userId.length==0||userId[userId.length-1]==0){
            User user1=new User();
            user1.setUserID(0);
            user1.setUserName("直接上级");
            userList.add(user1);//把直接上级 加入可选人员列表中
        }
        for (User u : users) {
            if ((u.getOrdinaryEmployees() == null || u.getOrdinaryEmployees() == 0)&& !"2".equals(u.getIsDuty())) {
                userList.add(u);
            }
        }
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("users", userList);
        ObjectToJson.objectToJson1(map, new String[]{"personnelOvertimeUser", "personnelLeaveUser", "parent", "roles", "educationClasses", "teacherRecommend", "organization", "educations", "leaves"
                , "userMessages", "personnelOccupationUser", "personalEducationUser", "personnelSalaryLogUser", "personalRewardPunishmentUser", "personalAssessmentUser"
                , "userFeedbackUser", "userLogs", "opMemberAssignerUser", "opMemberTracerUser", "opMemberSubmitterUser", "opTraceTracerUser", "opTraceTracerDetailUser", "inputStream", "volumeM"
                , "volumeY", "transferTime", "page", "offDutyDate", "onDutyDate", "volume", "submit", "submitM", "submitY", "password"
                , "money", "moneyY", "moneym", "lv", "lvM", "lvY", "logonState", "new", "newDiary", "contentApprovalHashSet", "contentHashSet", "personnelReimburseHashSet","roles","userPopedomHashSet","personnelFolksHashSet","personnelInterviewHashSet","personalAssessments","personalEducations","personalRewardPunishments","personnelOccupations",
                "assessUser3","assessUser2","assessUser1","nation","idCard","userLogOperateTime","marry","major2","major1","path","major3","computerLevel","qq","forumCount","admins","corpName3","corpName2","corpName1","postName","firstForeignLevel","email","address","beginTime1","ooperator3","maxStaff","beginTime2","beginTime3","ememo2","ememo1","ememo3","dmemo2","operatorName1","dmemo3","operatorName2","operatorName3","dmemo1","ooperator1","ooperator2","assessDate2","assessDate3","homeAddress","type3","type2","emergencyContact"
                ,"type1","omemo2","omemo1","omemo3","operateTime3","operateTime2","operateTime1","nativePlace","salary2","salary3","offerSalaty","csalary3","csalary1","csalary2","roleCreateDate","roleID","roleflag","secondLanguage","postID","degree1","degree3","degree2","endTime1","endTime3","endTime2","company","ordinaryEmployees","loginStatus","assessDate1","emergencyName","content3","content2","content1","nativeType","offerPost","workYear","logonPwd","approveStatus","QRPath","adjustDate3","operator2","operator3","adjustDate1","adjustDate2"
                ,"operator1","operatingDuty1","operatingDuty2","operatingDuty3","otherSkills","offerStatus","birthplace","manageName","versionMemo","roleCreateUserName","status","agentType","flag","gender","auditDate","handleTime","politicalStatus","eendTime3","eendTime2","eendTime1","contactTel","ocontent3","ocontent2","ocontent1","residence","haveSubordinate","occurDate1","occurDate2","occurDate3","collegeName1","userLogIp","collegeName3","collegeName2","secondForeignLevel","edu","transferReason","firstLanguage","birthday","amemo2","amemo1","handleFlag"
                ,"smemo1","smemo2","remark","smemo3","post2","amemo3","post3","post1","assessUserName1","speciality","ooperatorName2","ooperatorName3","admustResaon3","department","admustResaon2","admustResaon1","assessUserName3","assessUserName2","ooperatorName1","otherLanguage","ebeginTime2","ebeginTime1","ebeginTime3","interesting","currentUserLogId","rankUrl","realName","fingerID","updateDate","createTime","default","previousId","manager","passwordTime"}, response);
    }



    /**
     * <AUTHOR>
     * @Date 2017/2/13 9:23
     * 获取报销申请审批级别
     */
    @ResponseBody
    @RequestMapping("/getCurrentApproval.do")
    public void getCurrentApproval(User user, HttpServletResponse response) throws IOException {
        Integer oid = user.getOid();
        ApprovalItem approvalItem = approvalService.getApprovalItemByOidAndCode(oid, "reimburseApply");
        Map<String, Object> map = new HashMap<String, Object>();
        if (approvalItem != null) {

            map.put("disabled", 0);//0-不置灰  1-置灰

            if (approvalItem.getName().equals("加班")){
//                List<Integer> userIds=new ArrayList<>();
//                User superUser=userService.getUserByRoleCode(oid,"super");
//                User general=userService.getUserByRoleCode(oid,"general");
//                userIds.add(superUser.getUserID());
//                if (general!=null) {
//                    userIds.add(general.getUserID());
//                }
                List<ApprovalProcess> list=approvalProcessService.getApprovalProcessByBusinessType(oid,"1",10);
                for (ApprovalProcess a:list){
                    if (a.getDescription()!=null&&!a.getDescription().equals("")){
                        if (a.getDescription().indexOf("报销申请")!=-1){
                            map.put("disabled", 1);//0-不置灰  1-置灰
                            break;
                        }
                    }
                }
            }

            List<ApprovalFlow> approvalFlowList=approvalService.approvalFlowList(approvalItem.getId());
            for (ApprovalFlow a:approvalFlowList){
                if (a.getToUserId()!=0){
                    a.setMobile(userService.getUserByID(a.getToUserId()).getMobile());
                }
            }
            map.put("status", 1);//成功
            map.put("approvalFlowList", approvalFlowList);
        } else {
            map.put("status", 0);//没有审批流程
        }
        ObjectToJson.objectToJson1(map, new String[]{"approvalProcessHashSet", "item"}, response);
    }



    /**
    * <AUTHOR>
    * @Date 2017/5/15 10:48
    * 查询当前请假加班审批流程
    */
    @ResponseBody
    @RequestMapping("/getLeaveOutTimeLevelList.do")
    public void getLeaveOutTimeLevelList(User user,Integer itemId, HttpServletResponse response) throws IOException {
        Integer oid = user.getOid();
        ApprovalItem approvalItem = approvalService.getById(itemId);

        Map<String, Object> map = new HashMap<String, Object>();

        if (approvalItem != null) {
            map.put("disabled", 0);//0-不置灰  1-置灰
//            List<Integer> userIds=new ArrayList<>();
//            User superUser=userService.getUserByRoleCode(oid,"smallSuper");
//            if (superUser==null) {
//                superUser = userService.getUserByRoleCode(oid, "super");
//            }
//            User general=userService.getUserByRoleCode(oid,"general");
//            userIds.add(superUser.getUserID());
//            if(general!=null) {
//                userIds.add(general.getUserID());
//            }
            List<ApprovalProcess> list=approvalProcessService.getApprovalProcessByBusinessType(oid,"1",10);
            if (approvalItem.getName().equals("加班")){

                for (ApprovalProcess a:list){
                    if (a.getDescription()!=null&&!a.getDescription().equals("")){
                        if (a.getDescription().indexOf("加班申请")!=-1){
                            map.put("disabled", 1);//0-不置灰  1-置灰
                            break;
                        }
                    }
                }
            }else if (approvalItem.getName().equals("请假")){

                for (ApprovalProcess a:list){
                    if (a.getDescription()!=null&&!a.getDescription().equals("")){
                        if (a.getDescription().indexOf("请假申请")!=-1){
                            map.put("disabled", 1);//0-不置灰  1-置灰
                            break;
                        }
                    }
                }
            }else if (approvalItem.getName().equals("修改审批设置")){

                for (ApprovalProcess a:list){
                    if (a.getDescription()!=null&&!a.getDescription().equals("")){
                        if (a.getDescription().indexOf("审批设置")!=-1){
                            map.put("disabled", 1);//0-不置灰  1-置灰
                            break;
                        }
                    }
                }
            }

            map.put("status", 1);//成功
            map.put("approvalItem",approvalItem);
            List<ApprovalFlow> approvalFlowList=approvalService.approvalFlowList(approvalItem.getId());
            for (ApprovalFlow f:approvalFlowList){
                if (f.getToUserId()!=0){
                    f.setMobile(userService.getUserByID(f.getToUserId()).getMobile());
                }
            }
            map.put("approvalFlowList", approvalFlowList);
        } else {
            map.put("status", 0);//没有审批流程
        }
        ObjectToJson.objectToJson1(map, new String[]{"approvalProcessHashSet", "item"}, response);
    }


    /**
    * <AUTHOR>
    * @Date 2017/5/10 10:24
    * 判断list集合里是否有重复元素
    */
    public static boolean hasSame(List<? extends Object> list)
    {
        if(null == list)
            return false;
        return list.size() == new HashSet<Object>(list).size();
    }


//    -------------------------------------------以下为权限2.1 审批流程修改申请接口-----------------------------------------

    /**
    * <AUTHOR>
    * @Date 2018/3/22 9:27
    * 请假审批流程修改申请接口
    */
    @ResponseBody
    @RequestMapping("/leaveApprovalApply.do")
    public void leaveApprovalApply(User user,HttpServletRequest request,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();
        String item=request.getParameter("item");
        String levelList=request.getParameter("levelList");//级别列表
        Integer oid= user.getOid();
//        List<Integer> userIds=new ArrayList<>();
//        User superUser=userService.getUserByRoleCode(oid,"super");
//        User general=userService.getUserByRoleCode(oid,"general");
//        userIds.add(superUser.getUserID());
//        if (general!=null) {
//            userIds.add(general.getUserID());
//        }
        List<ApprovalProcess> list=approvalProcessService.getApprovalProcessByBusinessType(oid,"1",10);
        int keyi=1;
        for (ApprovalProcess a:list){
            if (a.getDescription()!=null&&!a.getDescription().equals("")){
                if (a.getDescription().indexOf("请假")!=-1){
                    keyi=4;
                    break;
                }
            }
        }
        if (keyi==1){
            map=approvalService.leaveApprovalApply(oid,item,levelList,user);
        }else {
            map.put("status", keyi);//4- 此修改申请正在申请中，不能再次提交
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    /**
    * <AUTHOR>
    * @Date 2018/3/22 10:49
    * 加班审批流程修改申请接口
    */
    @ResponseBody
    @RequestMapping("/outTimeApprovalApply.do")
    public void  outTimeApprovalApply(User user,HttpServletRequest request,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();
        Integer oid= user.getOid();
        String item=request.getParameter("item");
        String outTimeList=request.getParameter("outTimeList");//级别列表

//        List<Integer> userIds=new ArrayList<>();
//        User superUser=userService.getUserByRoleCode(oid,"super");
//        User general=userService.getUserByRoleCode(oid,"general");
//        userIds.add(superUser.getUserID());
//        if (general!=null) {
//            userIds.add(general.getUserID());
//        }
        List<ApprovalProcess> list=approvalProcessService.getApprovalProcessByBusinessType(oid,"1",10);
        int keyi=1;
        for (ApprovalProcess a:list){
            if (a.getDescription()!=null&&!a.getDescription().equals("")){
                if (a.getDescription().indexOf("加班")!=-1){
                    keyi=4;
                    break;
                }
            }
        }
        if (keyi==1){
            map=approvalService.outTimeApprovalApply(oid,item,outTimeList,user);
        }else {
            map.put("status", keyi);//4- 此修改申请正在申请中，不能再次提交
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);

    }

    /**
    * <AUTHOR>
    * @Date 2018/3/22 11:46
    * 报销审批流程修改申请
    */
    @ResponseBody
    @RequestMapping("/reimburseApprovalApply.do")
    public void reimburseApprovalApply(User user,HttpServletRequest request,HttpServletResponse response) throws IOException {
        Integer oid = user.getOid();
        String item=request.getParameter("item");
        String levelList=request.getParameter("levelList");//级别列表
        Map<String,Object> map=new HashMap<String,Object>();
//        List<Integer> userIds=new ArrayList<>();
//        User superUser=userService.getUserByRoleCode(oid,"super");
//        User general=userService.getUserByRoleCode(oid,"general");
//        userIds.add(superUser.getUserID());
//        if (general!=null) {
//            userIds.add(general.getUserID());
//        }
        List<ApprovalProcess> list=approvalProcessService.getApprovalProcessByBusinessType(oid,"1",10);
        int keyi=1;
        for (ApprovalProcess a:list){
            if (a.getDescription()!=null&&!a.getDescription().equals("")){
                if (a.getDescription().indexOf("报销")!=-1){
                    keyi=4;
                    break;
                }
            }
        }
        if (keyi==1){
            map=approvalService.reimburseApprovalApply(oid,item,levelList,user);
        }else {
            map.put("status", keyi);//4- 此修改申请正在申请中，不能再次提交
        }
        ObjectToJson.objectToJson1(map, new String[]{}, response);
    }

    /**
    * <AUTHOR>
    * @Date 2018/3/22 15:30
    * 审批审批流程修改申请
    */
    @ResponseBody
    @RequestMapping("/approvalApprovalApply.do")
    public void approvalApprovalApply(User user,Integer id, Integer level,Integer userId,HttpServletResponse response) throws IOException {
        Integer oid = user.getOid();
        Map<String,Object> map=new HashMap<String,Object>();
//        List<Integer> userIds=new ArrayList<>();
//        User superUser=userService.getUserByRoleCode(oid,"super");
//        User general=userService.getUserByRoleCode(oid,"general");
//        userIds.add(superUser.getUserID());
//        if (general!=null) {
//            userIds.add(general.getUserID());
//        }
        List<ApprovalProcess> list=approvalProcessService.getApprovalProcessByBusinessType(oid,"1",10);
        int keyi=1;
        for (ApprovalProcess a:list){
            if (a.getDescription()!=null&&!a.getDescription().equals("")){
                if (a.getDescription().indexOf("审批设置")!=-1){
                    keyi=4;
                    break;
                }
            }
        }
        if (keyi==1){
            map=approvalService.approvalApprovalApply(list.size(),oid,id,level,userId,user);
        }else {
            map.put("status", keyi);//4- 此修改申请正在申请中，不能再次提交
        }

        ObjectToJson.objectToJson1(map, new String[]{}, response);
    }

    
    /**
    * <AUTHOR>
    * @Date 2018/3/23 11:10
    * 权限管理 修改记录列表
    */
    @ResponseBody
    @RequestMapping("/editRecord.do")
    public void editRecord(User user, String approvalStatus, PageInfo page, HttpServletResponse response) throws IOException {
        List<ApprovalProcess> apps=approvalProcessService.getApprovalProcessByBusinessType(user.getUserID(),approvalStatus,10,null,null,page,null);
        Map<String,Object> map=new HashMap<String,Object>();
        map.put("approvalProcess",apps);
        map.put("pageInfo",page);
        ObjectToJson.objectToJson1(map,new String[]{"approvalFlow","approvalProcessHashSet","user"},response);
    }

    /**
    * <AUTHOR>
    * @Date 2018/3/23 11:47
    * 查看修改记录单条详情
    */
    @ResponseBody
    @RequestMapping("/editInfo.do")
    public void editInfo(Integer id,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();
        if (id!=null) {

            ApprovalProcess approvalProcess = approvalProcessService.getApprovalProcessById(id);

            ApprovalItem approvalItem = approvalService.getById(approvalProcess.getOldId());
            ApprovalItemHistory approvalItemHistory = approvalService.getApprovalItemHistoryById(approvalProcess.getNewId());
            List<ApprovalFlowHistory> approvalFlowHistoryList=approvalService.getApprovalFlowHistoryListByItemHId(approvalItemHistory.getId());
            approvalItemHistory.setApprovalFlowHistories(approvalFlowHistoryList);
            approvalItemHistory.setName(approvalItem.getName());
            approvalItemHistory.setDescription(approvalItem.getDescription());
            approvalItemHistory.setType(approvalItem.getType());

            ApprovalItemHistory approvalItemHistory1=new ApprovalItemHistory();
            approvalItemHistory1.setDescription(approvalItem.getDescription());
            approvalItemHistory1.setBelongTo(approvalItem.getBelongTo());
            approvalItemHistory1.setName(approvalItem.getName());
            approvalItemHistory1.setLevel(approvalItem.getLevel());
            approvalItemHistory1.setStatus(approvalItem.getStatus());

            List<ApprovalFlowHistory> approvalFlowHistories=new ArrayList<>();
            List<ApprovalFlow> approvalFlowList=approvalService.approvalFlowList(approvalItem.getId());
            for (ApprovalFlow af : approvalFlowList) {
                //把修改前的审批定义放到历史表中
                ApprovalFlowHistory approvalFlowHistory = new ApprovalFlowHistory();
                approvalFlowHistory.setLimitAmount(af.getAmountCeiling());
                approvalFlowHistory.setToUserId(af.getToUserId());
                approvalFlowHistory.setToUser(af.getToUser());
                approvalFlowHistory.setUserName(af.getUserName());
                approvalFlowHistory.setLevel(af.getLevel());
                approvalFlowHistories.add(approvalFlowHistory);
            }
            approvalItemHistory1.setApprovalFlowHistories(approvalFlowHistories);


            if (approvalProcess.getMessage()&&approvalProcess.getApproveStatus().equals("2")) {
                map.put("oldApprovalItem", approvalItemHistory);
                map.put("newApprovalItem", approvalItemHistory1);
            }else {
                map.put("oldApprovalItem", approvalItemHistory1);
                map.put("newApprovalItem", approvalItemHistory);
            }
                map.put("status", 1);//成功
        }else {
            map.put("status", 0);//失败
        }
        ObjectToJson.objectToJson1(map,new String[]{"approvalProcessHashSet","projectApprovalProcessHashSet","item"},response);

    }
    
    /**
    * <AUTHOR>
    * @Date 2018/3/23 15:52
    * 批准 驳回 审批设置修改申请
    */
    @ResponseBody
    @RequestMapping("/approvalApprovalItem.do")
    public void approvalApprovalItem(User user,Integer id,Integer approvalStatus,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<>();
        Integer oid= user.getOid();
        if (id!=null&&approvalStatus!=null) {
            ApprovalProcess approvalProcess = approvalProcessService.getApprovalProcessById(id);
            if (approvalStatus==1) {
                ApprovalItem approval = approvalService.getApprovalItemByOidAndCode(oid, "itemApply");
                if (approval.getLevel() <= approvalProcess.getLevel()) {//审批级别是最终级别，执行更改

                    ApprovalItem approvalItem = approvalService.getById(approvalProcess.getOldId());
                    int keyi=1;
                    List<User> users = userService.getUserByOid(oid, null);
                    if (approvalItem.getName().equals("加班")){
                        Integer counts=overtimeService.getPersonnelOvertimeCountsByOid(oid);
                        if (counts>0){
                           keyi=2;// 加班有新申请，老流程没走完，不能批准。
                        }

                    }else if (approvalItem.getName().equals("请假")){
                        Integer counts=leaveService.getPersonnelLeaveCountsByOid(oid);
                        if (counts>0) {
                             keyi=3;// 请假有新申请，老流程没走完，不能批准
                        }

                    }else if (approvalItem.getName().equals("报销")){
                        for (User ur : users) {
                            for (PersonnelReimburse p : ur.getPersonnelReimburseHashSet()) {
                                if ("1".equals(p.getApproveStatus())) {
                                    keyi=4; //报销有新申请，老流程没走完，不能批准
                                    break;
                                }
                            }
                        }
                    }
                    if (keyi==1) {
                        ApprovalItemHistory approvalItemHistory = approvalService.getApprovalItemHistoryById(approvalProcess.getNewId());
                        List<ApprovalFlowHistory> approvalFlowHistoryList = approvalService.getApprovalFlowHistoryListByItemHId(approvalItemHistory.getId());

                        for (ApprovalFlow af : approvalItem.getApprovalFlowHashSet()) {
                            //把修改前的审批定义放到历史表中
                            ApprovalFlowHistory approvalFlowHistory = new ApprovalFlowHistory();
                            approvalFlowHistory.setLimitAmount(af.getAmountCeiling());
                            approvalFlowHistory.setToUserId(af.getToUserId());
                            approvalFlowHistory.setToUser(af.getToUser());
                            approvalFlowHistory.setUserName(af.getUserName());
                            approvalFlowHistory.setLevel(af.getLevel());
                            approvalFlowHistory.setItem(approvalItemHistory.getId());
                            approvalService.saveApprovalFlowHistory(approvalFlowHistory);//老审批流程定义

                            approvalService.deleteApprovalFlow(af);//清空之前审批定义
                        }

                        Integer oldLevel = approvalItem.getLevel();
                        Integer oldStatus = approvalItem.getStatus();

                        approvalItem.setLevel(approvalItemHistory.getLevel());//审批级别
                        approvalItem.setStatus(approvalItemHistory.getStatus());//审批状态
                        approvalService.updateApprovalItem(approvalItem);//更新审批项

                        approvalItemHistory.setLevel(oldLevel);
                        approvalItemHistory.setStatus(oldStatus);
                        approvalService.updateApprovalItemHistory(approvalItemHistory);

                        for (ApprovalFlowHistory afh : approvalFlowHistoryList) {
                            ApprovalFlow approvalFlow = new ApprovalFlow();
                            approvalFlow.setAmountCeiling(afh.getLimitAmount());
                            approvalFlow.setToUserId(afh.getToUserId());
                            approvalFlow.setToUser(afh.getToUser());
                            approvalFlow.setUserName(afh.getUserName());
                            approvalFlow.setLevel(afh.getLevel());
                            approvalFlow.setType(Integer.valueOf(afh.getType()));
                            approvalFlow.setItem(approvalItem);
                            approvalService.saveApprovalFlow(approvalFlow);//新审批流程定义

                            approvalService.deleteApprovalFlowHistory(afh);
                        }

//                    approvalProcess.setOldId(approvalItemHistory.getId());//审批前与审批后id转换
//                    approvalProcess.setNewId(approvalItem.getId());

                        approvalProcess.setApproveStatus("2");//审批过程 批准
                        approvalProcess.setHandleTime(new Date());
                        approvalProcess.setMessage(true);//是结果
                        approvalProcessService.updateApprovalProcess(approvalProcess);

                        map.put("status", 1);//成功

                    }else {
                        map.put("status", keyi);//2-加班，3-请假，4-报销 有新申请，老流程没走完，不能批准。
                    }
                }else {

                    String handleName = "";
                    String userName = "";
                    Integer uid = null;
                    Integer level = approvalProcess.getLevel() + 1;
                    for (ApprovalFlow f : approval.getApprovalFlowHashSet()) {
                        if (f.getLevel() == level) {
                            uid = f.getToUserId();
                            handleName = f.getToUser();//审批人名称
                            userName = f.getUserName();
                        }
                    }
                    ApprovalProcess xiaji=new ApprovalProcess();
                    xiaji.setLevel(level);
                    xiaji.setApproveStatus("1");
                    xiaji.setToUser(uid);
                    xiaji.setToUserName(handleName);//审批人总称
                    xiaji.setUserName(userName);//审批人名称
                    xiaji.setCreateDate(new Date());
                    xiaji.setBusiness(approvalProcess.getBusiness());
                    xiaji.setBusinessType(approvalProcess.getBusinessType());
                    xiaji.setOldId(approvalProcess.getOldId());
                    xiaji.setNewId(approvalProcess.getNewId());
                    xiaji.setFromUser(approvalProcess.getFromUser());
                    xiaji.setAskName(approvalProcess.getAskName());
                    xiaji.setDescription(approvalProcess.getDescription());
                    xiaji.setMessage(false);
                    approvalProcessService.saveApprovalProcess(xiaji);

                    approvalProcess.setApproveStatus("2");//审批过程 批准
                    approvalProcess.setHandleTime(new Date());
                    approvalProcess.setMessage(false);//不是结果

                    approvalProcessService.updateApprovalProcess(approvalProcess);
                    map.put("status", 1);//成功

                }

            }else {
                approvalProcess.setApproveStatus("3");//审批过程 驳回
                approvalProcess.setHandleTime(new Date());
                approvalProcessService.updateApprovalProcess(approvalProcess);
                map.put("status", 1);//成功

            }
        }else {
            map.put("status", 0);//失败

        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }


    /**
    * <AUTHOR>
    * @Date 2018/3/26 10:55
    * 职工权限 当前权限查看
    */
    @ResponseBody
    @RequestMapping("/userPopedom.do")
    public void userPopedom(User user,String userName, String departName,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();
        Integer oid= user.getOid();
        List<User> users = userService.getUserListByUserNameDepartName(oid,userName,departName);
        ArrayList<String> mids = new ArrayList<>();
        ArrayList<PopedomDto> popedomDtoArrayList = popedomService.getAllPopedomDtoListByOid(oid,mids);
        for (User u:users){
            u.setUserPopedomSet(u.getUserPopedomHashSet());
        }
        map.put("users",users);
        map.put("popedoms",popedomDtoArrayList);

        ObjectToJson.objectToJson1(map,new String[]{"parent","userMessages","personnelOccupations","personalEducations","personnelSalaryLogUser",
                "personalRewardPunishments","personalAssessments","personnelLeaveUser","personnelOvertimeUser","userLogs",
                "personnelReimburseHashSet","roles","organization","submit","volume","lv","money","submitM",
                "volumeM","lvM","moneym","submitY","volumeY","lvY","moneyY","userLogType","userLogOperateTime","userLogIp","flag",
                "roleflag","password","handleFlag","inputStream","collegeName1","ebeginTime1","eendTime1","major1","degree1","ememo1",
                "collegeName2","ebeginTime2","eendTime2","major2","degree2","ememo2","collegeName3","ebeginTime3","eendTime3","major3",
                "degree3","ememo3","occurDate1","ocontent1","omemo1","ooperator1","ooperatorName1","occurDate2","ocontent2","omemo2",
                "ooperator2","ooperatorName2","occurDate3","ocontent3","omemo3","ooperator3","ooperatorName3","corpName1","beginTime1",
                "endTime1","post1","csalary1","dmemo1","operatingDuty1","corpName2","beginTime2","endTime2","post2","csalary2","dmemo2",
                "operatingDuty2","corpName3","beginTime3","endTime3","post3","csalary3","dmemo3","operatingDuty3","assessDate1","assessUser1",
                "type1","content1","amemo1","assessUserName1","assessDate2","assessUser2","type2","content2","amemo2","assessUserName2",
                "assessDate3","assessUser3","type3","content3","amemo3","assessUserName3","salary1","adjustDate1","admustResaon1",
                "operateTime1","operator1","operatorName1","smemo1","salary2","adjustDate2","admustResaon2","operateTime2","operator2",
                "operatorName2","smemo2","salary3","adjustDate3","admustResaon3","operateTime3","operator3","operatorName3","smemo3"},response);
    }

    /**
    * <AUTHOR>
    * @Date 2018/3/27 15:52
    * 职工权限  审批设置查看
    */
    @ResponseBody
    @RequestMapping("/userApproval.do")
    public JsonResult userApproval(User user,String userName, String departName){
        List<UserDto> userDtoList=userService.getUserDtoByUserNameDepartName(user.getOid(),userName,departName);
        JsonResult result=new JsonResult(1,userDtoList);
        return result;
    }

    /**
    * <AUTHOR>
    * @Date 2018/4/8 16:41
    * 超管和总务
    */
    @ResponseBody
    @RequestMapping("/getSuperAndGeneral.do")
    public JsonResult getSuperAndGeneral(User user){
        List<UserHonePageDto> userList=new ArrayList<>();
        Integer oid= user.getOid();
        User superUser= userService.getUserByRoleCode(oid,"smallSuper");
        if (superUser==null) {
            superUser = userService.getUserByRoleCode(oid, "super");
        }
        User generalUser= userService.getUserByRoleCode(oid,"general");
        UserHonePageDto superUserDto=new UserHonePageDto();
        superUserDto.setUserID(superUser.getUserID());
        superUserDto.setUserName(superUser.getUserName());
        superUserDto.setMobile(superUser.getMobile());
        userList.add(superUserDto);

        if (generalUser!=null) {
            UserHonePageDto generalUserDto = new UserHonePageDto();
            generalUserDto.setUserID(generalUser.getUserID());
            generalUserDto.setUserName(generalUser.getUserName());
            generalUserDto.setMobile(generalUser.getMobile());
            userList.add(generalUserDto);
        }

        JsonResult result=new JsonResult(1,userList);
        return result;
    }

    /**
    * <AUTHOR>
    * @Date 2018/5/4 10:12
    * 报销审批设置八级版
    */
    @ResponseBody
    @RequestMapping("/updateReimbursementApprovalProcess.do")
    public void updateReimbursementApprovalProcess(User user,HttpServletRequest request,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();
        List<Integer> isNo=new ArrayList<Integer>();//存储userId ，用来判断userId 是否重复

        Integer oid= user.getOid();

        String item=request.getParameter("item");
        JSONObject jsonItem= JSONObject.fromObject(item);

        String levelList=request.getParameter("levelList");//级别列表
        JSONArray j = JSONArray.fromObject(levelList);
        List levels = JSONArray.toList(j);

        List<User> users = userService.getUserByOid(oid, null);
        List<User> userList = new ArrayList<User>();
        for (User ur : users) {
            for (PersonnelReimburse p : ur.getPersonnelReimburseHashSet()) {
                if ("1".equals(p.getApproveStatus())) {
                    userList.add(ur);
                }
            }
        }

        if (userList.size()<=0){
            ApprovalItem approvalItem = approvalService.getById(jsonItem.getInt("itemId"));
            Integer zonglevel=levels.size();
            if (zonglevel>0){
                approvalItem.setStatus(1);//需要审批
                approvalItem.setLevel(zonglevel);//最大审批级别
                approvalService.updateApprovalItem(approvalItem);

                for (int i = 0; i < zonglevel; i++) {
                    JSONObject jo = JSONObject.fromObject(j.get(i));
                    isNo.add(jo.getInt("userId"));
                }
//                if (ApprovalController.hasSame(isNo)){
                    for (ApprovalFlow f : approvalItem.getApprovalFlowHashSet()) {
                        ApprovalFlow deleteFlow = approvalService.getApprovalFlow(f.getId());
                        if (deleteFlow != null) {
                            approvalService.deleteApprovalFlow(deleteFlow);//清空当前请假审批流程
                        }
                    }

                    for (int i = 0; i < zonglevel; i++) {
                        JSONObject jo = JSONObject.fromObject(j.get(i));
                        Integer userId=jo.getInt("userId");

                        ApprovalFlow approvalFlow = new ApprovalFlow();

                        if(userId==0){
                            approvalFlow.setToUserId(0);
                            approvalFlow.setToUser("直接上级");
                            approvalFlow.setUserName("直接上级");
                        }else {
                            User approvalUser=userService.getUserByID(userId);
                            approvalFlow.setToUserId(userId);
                            approvalFlow.setToUser("指定审批人");
                            approvalFlow.setUserName(approvalUser.getUserName());//人名
                        }
                        approvalFlow.setLevel(i+1);//第i级
                        approvalFlow.setType(1);
                        approvalFlow.setItem(approvalItem);
                        if (approvalFlow.getLevel()!=zonglevel) {
                            approvalFlow.setAmountCeiling(Double.valueOf(jo.getString("amountCeiling")));//时长上限
                        }
                        approvalService.saveApprovalFlow(approvalFlow);//新增
                    }
                    map.put("status",1);

//                }else {
//                    map.put("status",3);//有重复的userId
//                }

            }else {
                map.put("status",0);//失败
            }

        }else {
            //还有没处理完的申请
            map.put("status",2);
        }

        ObjectToJson.objectToJson1(map,new String[]{},response);
    }



    
}