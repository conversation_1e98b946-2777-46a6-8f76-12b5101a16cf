package cn.sphd.miners.modules.thirdPlatform.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.thirdPlatform.dao.TpAppVersionDao;
import cn.sphd.miners.modules.thirdPlatform.entity.TpAppVersion;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class TpAppVersionDaoImpl extends BaseDao<TpAppVersion, Serializable> implements TpAppVersionDao {
}
