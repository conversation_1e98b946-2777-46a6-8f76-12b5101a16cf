<template>
  <div id="openInvoice">
    <TY_NavTop title="已终止的申请" @toggleList="search"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="ui-message">
        <div class="sendContainer">
          <span v-if="info.teminate_state==1" class="end1">终止</span>
          <div><span>客户名称：</span><span>{{info.name}}</span></div>
          <div><span>需要发票 {{info.invoice_count}} <span v-if="info.invoice_count !== '--'">张</span></span><span class="wid"></span><span>总额{{info.invoice_amount | numberFilter(2)}}元</span></div>
          <div v-if="info.create_name"><span>申请人：{{info.create_name}}</span><span class="wid"></span><span>{{info.create_date | formatDate}}</span></div>
          <div v-if="info.state==5||info.state==7||info.state==3"><span>审批人：{{info.update_name}}</span><span class="wid"></span><span>{{info.update_date | formatDate}}</span></div>
          <div v-if="info.state==5||info.state==7"><span>开票人：{{info.auditor_name}}</span><span class="wid"></span><span>{{info.audit_date | formatDate}}</span></div>
          <div v-if="info.teminate_state==1" class="hr">
            <span>终止操作人：{{info.teminater_name}}</span><span class="wid"></span><span>{{info.teminate_time | formatDate}}</span><br>
            <span>终止原因：</span><span>{{info.teminate_reason}}</span>
          </div>
        </div>
      </div>
      <p style="height: 15px"></p>
      <table class="table">
        <thead>
        <td>金额</td>
        <td>发票种类</td>
        <td v-if="info.state == 3||info.state==5||info.state==7">发票号码</td>
        <td v-if="info.state == 3||info.state==5||info.state==7">开票日期</td>
        <td>行数</td>
        </thead>
        <tbody>
        <tr @click="toInfo(item.id, applicationId, item.fid)" v-for="(item, index) in list" v-bind:key="index">
          <td>{{item.amount | numberFilter(2)}}</td>
          <td>{{item.invoice_category | filterInvoiceType}}</td>
          <td v-if="info.state == 3 || info.state == 5 || info.state == 7">{{item.invoice_no}}</td>
          <td v-if="info.state == 3 || info.state == 5 || info.state == 7">{{item.operate_date | formatDate('yyyy/MM/dd')}}</td>
          <td>{{item.line}}</td>
        </tr>
        </tbody>
      </table>
      <p style="height: 20px;"></p>
    </div>
  </div>
</template>
<style lang="less">
  .em2{ text-indent: 2em; }
  .red{ color:#ed5565;  }
  .hr{ border-top: 1px solid #333; padding:0.5rem 1rem;  }
  .wid{ display: inline-block; width: 30px; }
  .end1 {
    position: absolute; top:3rem; right:2rem;
    display: inline-block;
    border:3px solid #B87237;
    background:#F79646;
    border-radius:50%;
    color: #fff;
    font-size: 1rem;
    padding: 0.5rem 1rem;
  }
</style>
<script>
import { formatDate, filterInvoiceType, numberFilter } from '../../../js/common'
export default {
  name: 'invoiceEndInfo',
  data () {
    return {
      dialogVisible: false,
      loading: true,
      list: [],
      info: {}
    }
  },
  filters: {
    formatDate,
    numberFilter,
    filterInvoiceType
  },
  created () {
    let _this = this
    _this.applicationId = this.$route.params.id
    let data = { 'applicationId': _this.applicationId }
    // 获取申请详情
    _this.$http.post('../../../sale/getApplicationItemListByApplicationId.do', data, {
      emulateJSON: true
    }).then((response) => {
      let res = response.body
      _this.list = res.data
      _this.applicationId = res.applicationId
      _this.info = res.base[0]
      _this.info['invoice_amount'] = 0
      _this.list.forEach(function (item) {
        _this.info['invoice_amount'] += Number(item['amount'])
      })
      _this.loading = false
    }).catch(function () {
      _this.$message({
        type: 'error',
        message: '系统错误，请重试！'
      })
      this.$router.push({
        path: `/invoiceEnd`
      })
    })
  },
  destroyed: function () {
  },
  methods: {
    toInfo: function (invoiceId, apId, fid) {
      let _this = this
      this.$router.push({
        path: `/applyInvoiceDetailInfo/${apId}/${invoiceId}/${_this.info.division_way}/${fid}`
      })
    }
  }
}
</script>
