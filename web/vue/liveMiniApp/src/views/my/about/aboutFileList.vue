<template>
  <div class="usingHelp">
    <Pageheader title="使用帮助" :showBack="true"/>
    <div class="live-contain">
      <div class="live-cells mainCon">
        <div class="live-cell" v-for="(item, index) in fileList" :key="item.id" @click="jumpDetail(item)">
          <el-icon><Document /></el-icon>
          <span class="fileName">{{item.name}}</span>
          <el-icon class="arrow-right"><ArrowRight /></el-icon>
        </div>
      </div>
      <el-pagination
          layout="prev, pager, next"
          :page-size="pageSize"
          :page-count="pageInfo.totalPage"
          class="mt-4"
          @current-change="handleCurrentChange"
          hide-on-single-page
      />
    </div>
  </div>
</template>

<script setup>
import { getAboutFileList } from "@/api/api.js";
import { reactive, ref, onMounted, watch } from "vue";
import { useRouter, useRoute } from "vue-router";

let fileList = ref([])
let pageInfo = ref({totalPage: 0})
const pageSize = 15

const router = useRouter();
const route = useRoute();
console.log('params', route.params)


onMounted(() => {
  getNextModuleList(1)
})

const getNextModuleList = (currentPageNo) => {
  let categoryId = route.params.id
  let data = {
    category: categoryId,
    currentPageNo: currentPageNo,
    pageSize: pageSize
  }
  getAboutFileList(data).then(res => {
    let files = res.data.list
    let page = res.data.pageInfo
    pageInfo.value = page
    fileList.value = files
  })
}

const handleCurrentChange = (val) => {
  getNextModuleList(val)
}

const jumpDetail = (item) => {
  router.push({ path: '/aboutFinalDetail/usingHelp/' + item.id });
}

</script>

<style lang="scss" scoped>
.fileName{
  margin-left: 8px;
}
.live-cells{
  margin-top: 8px;
}
.mt-4{
  padding: 8px;
  justify-content: right;
  background: #fff;
}
.mainCon{
  min-height: calc(100% - 56px);
}
.live-contain{
  height: calc(100vh - 44px);
  overflow-y: auto;
}
</style>




