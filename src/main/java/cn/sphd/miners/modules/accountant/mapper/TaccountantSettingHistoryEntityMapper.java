package cn.sphd.miners.modules.accountant.mapper;

import cn.sphd.miners.modules.accountant.entity.TAccountantSetting;
import cn.sphd.miners.modules.accountant.entity.TaccountantSettingHistoryEntity;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
public interface TaccountantSettingHistoryEntityMapper extends BaseMapper<TaccountantSettingHistoryEntity> {

    List<TaccountantSettingHistoryEntity> getRecordsByOrg(Integer oid);

    TaccountantSettingHistoryEntity getLastShutTime(int oid);
}