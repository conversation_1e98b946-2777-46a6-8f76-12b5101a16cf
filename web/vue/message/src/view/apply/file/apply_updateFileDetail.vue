<template>
  <div id="issueFile">
    <TY_NavTop title="文件换版/废止申请"></TY_NavTop>
    <div class="container" v-loading="loading">
      <div class="place" v-if="isAbolishFile">
        <div class="placeContainer">
          <p>这是一条<span class="color-red">文件废止的申请</span>，请谨慎操作！</p>
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          <p class="fileTtl" v-text="fileDetails.details.name"></p>
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          <div class="line-p">
            <div class="tt-cell">文件编号</div>
            <div class="con-cell" v-text="fileDetails.details.fileSn"></div>
          </div>
          <div class="line-p">
            <div class="tt-cell">保存位置</div>
            <div class="con-cell" v-html="filePosition"></div>
          </div>
          <div class="line-p">
            <div class="tt-cell">文件类型</div>
            <div class="con-cell" v-text="fileDetails.details.version"></div>
          </div>
          <div class="line-p">
            <div class="tt-cell">文件大小</div>
            <div class="con-cell">{{fileDetails.details.size | formatByte}}</div>
          </div>
          <div class="line-p" v-if="fileDetails.details.changeNum">
            <div class="tt-cell">版本号</div>
            <div class="con-cell">G{{fileDetails.details.changeNum}}</div>
          </div>
          <div class="line-p" v-if="!fileDetails.details.changeNum">
            <div class="tt-cell">说明</div>
            <div class="con-cell" v-text="fileDetails.details.content"></div>
          </div>
          <div class="line-p" v-if="fileDetails.details.changeNum">
            <div class="tt-cell">换版原因</div>
            <div class="con-cell" v-text="fileDetails.details.content"></div>
          </div>
        </div>

      </div>
      <div class="place">
        <div class="placeContainer">
          <div class="line-btn-groups" v-if="isAbolishFile">
            <div class="line-p">
              <div class="tt-cell">将废止的文件</div>
              <div class="con-cell">
                <span class="fc-btn-see" @click="showDialog(2)">查看</span>
              </div>
            </div>
          </div>
          <div class="line-btn-groups" v-else>
            <div class="line-p">
              <div class="tt-cell">待审批的文件</div>
              <div class="con-cell">
                <span class="fc-btn-see" @click="showDialog(0)">查看</span>
              </div>
            </div>
            <div class="line-p">
              <div class="tt-cell">当前版本</div>
              <div class="con-cell">
                <span class="fc-btn-see" @click="showDialog(1)">查看</span>
              </div>
            </div>
            <div class="line-p">
              <div class="tt-cell">历史版本</div>
              <div class="con-cell">
                <span class="fc-btn-see">查看</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          <div class="record">
            <span>审批记录</span>
            <div class="script">
              <div v-for="(item ,index) in approveRecord" :key="index">
                <div v-if="index === 0">
                  <span class="sm-ttl">申请人：</span>
                  <span class="sm-con">{{item.userName}}</span>
                  <span>{{item.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                </div>
                <div v-if="index !== 0">
                  <span class="sm-ttl">审批人：</span>
                  <span class="sm-con">{{item.userName}}</span>
                  <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                </div>
                <div v-if="index === approveRecord.length-1">
                  <span>等待
                    <span class="font-orange" v-if="item.toUserName">{{item.toUserName}}</span>
                    <span class="font-orange" v-else>文管</span>
                    审批
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div class="handle_button handle-center" v-if="isNotCharged">
            <button class="ui-btn ui-btn_success" @click="stopApply" :disabled="stopControl">
              <span v-if="isAbolishFile">终止废止申请</span>
              <span v-else>终止换版申请</span>
            </button>
          </div>
        </div>
      </div>
    </div>
    <!-- 预览下载弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="pendingDialog"
      width="70%">
      <el-form class="preview">
        <el-form-item>
          <el-radio v-model="browser" :disabled="seeOnlineDisabled" :label="1">在线预览</el-radio>
          <el-radio v-model="browser" :label="2">下载</el-radio>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button handle-right">
          <button class="ui-btn" @click="pendingDialog = false">取消</button>
          <button class="ui-btn ui-btn_info" :path="fileHandleDetail.path" :download="fileHandleDetail.name + '.' + fileHandleDetail.version" @click="oprationFile($event)">确定</button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { canSeeOnline } from '@/js/common'
export default {
  name: 'updateFileDetail',
  data () {
    return {
      loading: true,
      isNotCharged: true,
      pendingDialog: false,
      fileDetails: {
        categoryName: '',
        filePath: '',
        fileVersion: '',
        details: {}
      },
      approveRecord: [],
      listenersUid: [],
      browser: 1,
      fileHandleDetail: {},
      dialogTitle: '',
      seeOnlineDisabled: true,
      isAbolishFile: false // 是否是废止的文件详情（默认不是）
    }
  },
  computed: {
    stopControl () {
      let len = this.approveRecord.length - 1
      if (len > 0) {
        if (this.approveRecord[len].approveStatus === '3' || this.approveRecord[len].approveStatus === '5' || (this.approveRecord[len].approveStatus === '2' && this.approveRecord[len].toMid === 'rb')) {
          return true
        }
      }
      return false
    },
    filePosition () {
      let pos = this.fileDetails.categoryName
      let posArr = pos.split('/')
      let posStr = ''
      for (let a = 0; a < posArr.length; a++) {
        if (a === posArr.length - 1) {
          posStr += '<span class="font-orange">' + posArr[a] + '</span>'
        } else {
          posStr += posArr[a] + '/'
        }
      }
      return posStr
    }
  },
  created: function () {
    var _this = this
    var fileId = this.$route.params.id
    _this.sphdSocket.send('onePublishFileMessage', { 'session': _this.sphdSocket.sessionid, 'hisId': fileId, 'userID': _this.sphdSocket.user.userID })
    this.listenersUid = [
      this.sphdSocket.subscribe('onePublishFileMessage', function (data) {
        console.log('换版详情：' + data)
        let fileData = JSON.parse(data)
        let teminateState = fileData.resHis.teminateState

        let filePath = ''
        if (teminateState === '1') {
          filePath = fileData.resHis.path
        } else {
          filePath = fileData.validFilePath
        }
        let pathArr = filePath.split('.')
        let version = pathArr[pathArr.length - 1]


        _this.fileDetails = {
          categoryName: fileData.categoryName,
          filePath: filePath,
          fileVersion: version,
          details: fileData.resHis,
        }
        _this.isAbolishFile = teminateState === '1'
        if (fileData.listAp.length > 0) {
          _this.approveRecord = fileData.listAp
          let listAp = fileData.listAp
          let lastSta = listAp[listAp.length - 1]['approveStatus']
          if (lastSta === '2' || lastSta === '3') {
            _this.isNotCharged = false
          }
        }
        _this.loading = false
      }, null, 'session', fileId),
      this.sphdSocket.subscribe('fileUpdateState', function (data) {
        console.log('终止的处理:' + data)
        _this.loading = false
        let val = JSON.parse(data)
        if (val === 2) { // 2=终止
          _this.$message({
            type: 'success',
            message: '操作成功！'
          })
          _this.refreshFile()
        } else {
          _this.$alert('本文件已被审批', '！提示', {
            confirmButtonText: '确定'
          });
          _this.isNotCharged = false
        }
      }, null, 'custom', fileId)
    ]
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    stopFileProcess () {
      var userId = this.sphdSocket.user.userID
      var fileId = this.$route.params.id
      var processLength = this.approveRecord.length - 1
      var processId = this.approveRecord[processLength].id
      this.loading = true
      this.sphdSocket.send('stopFileProcess', { 'userID': userId, 'hisId': fileId, 'processId': processId })
    },
    screen: function () {
      this.$router.push({
        path: `/applyFileQuery`
      })
    },
    refreshFile: function () {
      this.$router.push({
        path: `/versionApply`
      })
    },
    oprationFile: function (event) {
      let el = event.currentTarget
      window.parent.previewOrDownload(el, this.browser)
    },
    stopApply: function () {
      let tips = this.isAbolishFile?'确定终止本次废止申请？':'确定终止本次换版申请？'
      let that = this
      this.$confirm(tips, '！提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.stopFileProcess()
      }).catch(() => {})
    },
    showDialog: function (type) {
      let fileHandleDetail = {}
      if (type === 0) {
        this.dialogTitle = '待审批的文件'
        fileHandleDetail = {
          path: this.fileDetails.details.path,
          name: this.fileDetails.details.name,
          version: this.fileDetails.details.version
        }
      } else if (type === 1) {
        this.dialogTitle = '当前的有效文件'
        fileHandleDetail = {
          path: this.fileDetails.filePath,
          name: this.fileDetails.details.name,
          version: this.fileDetails.fileVersion
        }
      } else if (type === 2) {
        this.dialogTitle = '将废止的文件'
        fileHandleDetail = {
          path: this.fileDetails.details.path,
          name: this.fileDetails.details.name,
          version: this.fileDetails.details.version
        }
      }
      this.seeOnlineDisabled = !canSeeOnline(fileHandleDetail.version)
      this.fileHandleDetail = fileHandleDetail
      this.pendingDialog = true
      if (this.seeOnlineDisabled) {
        this.browser = 2
      } else {
        this.browser = 1
      }
    }
  }
}
</script>
