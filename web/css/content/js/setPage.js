/* 
 *  分页的页数设置
 *  obj,分页插入的对象
 *  cur, 当前页数
 *  countall， 总页数
 */
function setPage(obj,cur,countall,Typestr){
	var all = countall;
	var div_obj = obj;
	var str = "<div class='yeCon'>";
	if(parseInt(all)>8){
		 var star=cur-3;
		 var star_cha=0;
		 var end=parseInt(cur)+3;
		 var end_cha=0;
		 if(parseInt(star)<0){ star=0; star_cha=3-cur; }
		 if(parseInt(end)>all){ end=all; end_cha=all-end; }
		 end=parseInt(end)+parseInt(star_cha);
		 star=parseInt(star)+parseInt(end_cha);
		 if(star!=0){str=str+"......"; }
		 for(var i=star;i<=end;i++){
			 if(i==cur){ str=str+"<div class='yecur'>"+i+"</div>"; }else{ str=str+"<div class='ye' onclick='showPage($(this))'>"+i+"</div>"; };
		 }
		 if(end!=all){str=str+"......"; }
		 
	}else{
		for(var i=1; i<=all;i++){
			if(i==cur){
				str=str+"<div class='yecur'>"+i+"</div>";
			}else{
				str=str+"<div class='ye' onclick='showPage($(this))'>"+i+"</div>";
			};
		}
	}
	str=str+"<div class='yeC'>共"+all+"页</div><div class='yeC'>当前第"+cur+"页</div></div><div class='hd type'>"+ Typestr +"</div>";
	div_obj.append(str);
};


/*  跳转到某页
 * obj 跳转到页数的div
 * str 要调去的是哪一类数据，‘staff’调去员工的数据
 */
function showPage(obj)
{
	var cur=obj.html();
	var item=obj.parent().siblings(".type").html();
	// alert("cur:"+cur+", item:"+item);
	switch(item){
		case 'feedback':
			getfeedbackList(cur,10);
			break;
		case 'scan':
			getScanList(cur,5);
			break;
		case 'service':
			getServiceList(cur,5);
			break;
		case 'privcy':
			getPrivacyList(cur,5);
			break;
		case 'version':
			getVersionList(cur,5);
			break;
		case 'help':
			getHelpList(cur,5);
			break;
		case 'helpDetails':
			updateHelpTbl(cur,5);
			break;
		case 'help_add':
			upHelp(cur,5);
			break;
		case 'charge':
			getChargeList(cur,5);
			break;
			
		 
			
		default:
	
	}
	
}
