package cn.sphd.miners.modules.material.service.impl;

import java.util.List;

import cn.sphd.miners.modules.material.entity.TSrmSupplierImport;
import cn.sphd.miners.modules.material.mapper.TSrmSupplierImportMapper;
import cn.sphd.miners.modules.material.service.ITSrmSupplierImportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 供应商管理_供应商导入信息
 * 20231108 1.267供应商之批量导入Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Service
@Transactional
public class TSrmSupplierImportServiceImpl implements ITSrmSupplierImportService {
    @Autowired
    private TSrmSupplierImportMapper tSrmSupplierImportMapper;

    /**
     * 查询供应商管理_供应商导入信息
     * 20231108 1.267供应商之批量导入
     *
     * @param id 供应商管理_供应商导入信息
     *           20231108 1.267供应商之批量导入主键
     * @return 供应商管理_供应商导入信息
     * 20231108 1.267供应商之批量导入
     */
    @Override
    public TSrmSupplierImport selectTSrmSupplierImportById(Long id) {
        return tSrmSupplierImportMapper.selectTSrmSupplierImportById(id);
    }

    /**
     * 查询供应商管理_供应商导入信息
     * 20231108 1.267供应商之批量导入列表
     *
     * @param tSrmSupplierImport 供应商管理_供应商导入信息
     *                           20231108 1.267供应商之批量导入
     * @return 供应商管理_供应商导入信息
     * 20231108 1.267供应商之批量导入
     */
    @Override
    public List<TSrmSupplierImport> selectTSrmSupplierImportList(TSrmSupplierImport tSrmSupplierImport) {
        return tSrmSupplierImportMapper.selectTSrmSupplierImportList(tSrmSupplierImport);
    }

    /**
     * 新增供应商管理_供应商导入信息
     * 20231108 1.267供应商之批量导入
     *
     * @param tSrmSupplierImport 供应商管理_供应商导入信息
     *                           20231108 1.267供应商之批量导入
     * @return 结果
     */
    @Override
    public int insertTSrmSupplierImport(TSrmSupplierImport tSrmSupplierImport) {
        return tSrmSupplierImportMapper.insertTSrmSupplierImport(tSrmSupplierImport);
    }

    /**
     * 修改供应商管理_供应商导入信息
     * 20231108 1.267供应商之批量导入
     *
     * @param tSrmSupplierImport 供应商管理_供应商导入信息
     *                           20231108 1.267供应商之批量导入
     * @return 结果
     */
    @Override
    public int updateTSrmSupplierImport(TSrmSupplierImport tSrmSupplierImport) {
        return tSrmSupplierImportMapper.updateTSrmSupplierImport(tSrmSupplierImport);
    }

    /**
     * 批量删除供应商管理_供应商导入信息
     * 20231108 1.267供应商之批量导入
     *
     * @param ids 需要删除的供应商管理_供应商导入信息
     *            20231108 1.267供应商之批量导入主键
     * @return 结果
     */
    @Override
    public int deleteTSrmSupplierImportByIds(String ids) {
        return tSrmSupplierImportMapper.deleteTSrmSupplierImportByIds(ids.split(","));
    }

    /**
     * 删除供应商管理_供应商导入信息
     * 20231108 1.267供应商之批量导入信息
     *
     * @param id 供应商管理_供应商导入信息
     *           20231108 1.267供应商之批量导入主键
     * @return 结果
     */
    @Override
    public int deleteTSrmSupplierImportById(Long id) {
        return tSrmSupplierImportMapper.deleteTSrmSupplierImportById(id);
    }
}
