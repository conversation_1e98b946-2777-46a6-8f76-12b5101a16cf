package cn.sphd.miners.modules.equipment.entity;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * 装备管理_装备器具
 * 20230209 1.247装备器具新增
 * 对象 t_equ_equitment
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
public class TEquEquipment {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 组织机构ID
     */

    private Long org;

    /**
     * 名称
     */

    private String name;

    /**
     * 全称
     */

    private String fullName;

    /**
     * 型号
     */

    private String modelName;

    /**
     * 计量单位,多个以逗号分隔
     */

    private String units;

    /**
     * 型号个数
     */

    private Long modelCount;

    /**
     * 来源个数
     */

    private Long supplierCount;

    /**
     * 数量
     */

    private Long quantity;

    /**
     * 备注
     */

    private String memo;

    /**
     * 状态:0-不启用,1-启用
     */

    private Long enabled;

    /**
     * 启停用时间
     */

    private Date enabledTime;

    /**
     * 创建人id
     */

    private Long creator;

    /**
     * 创建人
     */

    private String createName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createDate;

    /**
     * 修改人id
     */

    private Long updator;

    /**
     * 修改人
     */

    private String updateName;

    /**
     * 修改时间
     */

    private Date updateDate;

    /**
     * 操作:1-增,2-删,3-更改,4-启用,5-停用
     */

    private Long operation;

    /**
     * 修改前记录ID
     */

    private Long previousId;

    /**
     * 版本号,每次修改+1
     */

    private Long versionNo;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setOrg(Long org) {
        this.org = org;
    }

    public Long getOrg() {
        return org;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getFullName() {
        return fullName;
    }

    public void setUnits(String units) {
        this.units = units;
    }

    public String getUnits() {
        return units;
    }

    public void setModelCount(Long modelCount) {
        this.modelCount = modelCount;
    }

    public Long getModelCount() {
        return modelCount;
    }

    public void setSupplierCount(Long supplierCount) {
        this.supplierCount = supplierCount;
    }

    public Long getSupplierCount() {
        return supplierCount;
    }

    public void setQuantity(Long quantity) {
        this.quantity = quantity;
    }

    public Long getQuantity() {
        return quantity;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getMemo() {
        return memo;
    }

    public void setEnabled(Long enabled) {
        this.enabled = enabled;
    }

    public Long getEnabled() {
        return enabled;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Long getCreator() {
        return creator;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setUpdator(Long updator) {
        this.updator = updator;
    }

    public Long getUpdator() {
        return updator;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setOperation(Long operation) {
        this.operation = operation;
    }

    public Long getOperation() {
        return operation;
    }

    public void setPreviousId(Long previousId) {
        this.previousId = previousId;
    }

    public Long getPreviousId() {
        return previousId;
    }

    public void setVersionNo(Long versionNo) {
        this.versionNo = versionNo;
    }

    public Long getVersionNo() {
        return versionNo;
    }
}
