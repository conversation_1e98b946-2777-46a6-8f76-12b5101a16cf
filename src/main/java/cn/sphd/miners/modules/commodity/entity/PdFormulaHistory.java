package cn.sphd.miners.modules.commodity.entity;

import javax.persistence.*;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Entity
@Table(name = "t_pd_formula_history")
public class PdFormulaHistory {
    private Integer id;
    private Integer org;
    private String code;
    private String name;
    private String materialName;
    private Integer majorIngredient;
    private Integer minorIngredient;
    private Integer products;
    private Byte enalbed;
    private String memo;
    private Integer creator;
    private String createName;
    private Date createDate;
    private Integer updator;
    private String updateName;
    private Date updateDate;
    private String operation;
    private Integer previousId;//被用于审批id
    private Integer versionNo;
    private Integer formula;
    private Integer instance;
    private String instanceChain;
    private String approveStatus;
    private String rejectReasionDesc;
    private List<Map<String,Object>> zlList;
    private List<Map<String,Object>> flList;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Basic
    @Column(name = "formula")
    public Integer getFormula() {
        return formula;
    }

    public void setFormula(Integer formula) {
        this.formula = formula;
    }

    @Basic
    @Column(name = "org")
    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    @Basic
    @Column(name = "code")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Basic
    @Column(name = "name")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Basic
    @Column(name = "material_name")
    public String getMaterialName() {
        return materialName;
    }

    public void setMaterialName(String materialName) {
        this.materialName = materialName;
    }

    @Basic
    @Column(name = "major_ingredient")
    public Integer getMajorIngredient() {
        return majorIngredient;
    }

    public void setMajorIngredient(Integer majorIngredient) {
        this.majorIngredient = majorIngredient;
    }

    @Basic
    @Column(name = "minor_ingredient")
    public Integer getMinorIngredient() {
        return minorIngredient;
    }

    public void setMinorIngredient(Integer minorIngredient) {
        this.minorIngredient = minorIngredient;
    }

    @Basic
    @Column(name = "products")
    public Integer getProducts() {
        return products;
    }

    public void setProducts(Integer products) {
        this.products = products;
    }

    @Basic
    @Column(name = "enalbed")
    public Byte getEnalbed() {
        return enalbed;
    }

    public void setEnalbed(Byte enalbed) {
        this.enalbed = enalbed;
    }

    @Basic
    @Column(name = "memo")
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Basic
    @Column(name = "creator")
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    @Basic
    @Column(name = "create_name")
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Basic
    @Column(name = "create_date")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "updator")
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    @Basic
    @Column(name = "update_name")
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Basic
    @Column(name = "update_date")
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Basic
    @Column(name = "operation")
    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    @Basic
    @Column(name = "previous_id")
    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    @Basic
    @Column(name = "version_no")
    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    @Basic
    @Column(name = "instance")
    public Integer getInstance() {
        return instance;
    }

    public void setInstance(Integer instance) {
        this.instance = instance;
    }
    @Basic
    @Column(name = "instance_chain")
    public String getInstanceChain() {
        return instanceChain;
    }

    public void setInstanceChain(String instanceChain) {
        this.instanceChain = instanceChain;
    }

    @Basic
    @Column(name = "approve_status")
    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    @Basic
    @Column(name = "reject_reasion_desc")
    public String getRejectReasionDesc() {
        return rejectReasionDesc;
    }

    public void setRejectReasionDesc(String rejectReasionDesc) {
        this.rejectReasionDesc = rejectReasionDesc;
    }

    @Transient
    public List<Map<String, Object>> getZlList() {
        return zlList;
    }

    public void setZlList(List<Map<String, Object>> zlList) {
        this.zlList = zlList;
    }
    @Transient
    public List<Map<String, Object>> getFlList() {
        return flList;
    }

    public void setFlList(List<Map<String, Object>> flList) {
        this.flList = flList;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PdFormulaHistory that = (PdFormulaHistory) o;
        return id == that.id &&
                Objects.equals(org, that.org) &&
                Objects.equals(code, that.code) &&
                Objects.equals(name, that.name) &&
                Objects.equals(materialName, that.materialName) &&
                Objects.equals(majorIngredient, that.majorIngredient) &&
                Objects.equals(minorIngredient, that.minorIngredient) &&
                Objects.equals(products, that.products) &&
                Objects.equals(enalbed, that.enalbed) &&
                Objects.equals(memo, that.memo) &&
                Objects.equals(creator, that.creator) &&
                Objects.equals(createName, that.createName) &&
                Objects.equals(createDate, that.createDate) &&
                Objects.equals(updator, that.updator) &&
                Objects.equals(updateName, that.updateName) &&
                Objects.equals(updateDate, that.updateDate) &&
                Objects.equals(operation, that.operation) &&
                Objects.equals(previousId, that.previousId) &&
                Objects.equals(versionNo, that.versionNo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, org, code, name, materialName, majorIngredient, minorIngredient, products, enalbed, memo, creator, createName, createDate, updator, updateName, updateDate, operation, previousId, versionNo);
    }
}
