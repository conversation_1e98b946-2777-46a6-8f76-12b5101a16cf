<template>
  <div id="forumApproval">
    <TY_NavTop title="管理的讨论组"></TY_NavTop>
    <div class="container">
      <div class="nowTime">
        以下讨论组即将从系统中消失。<br>
        认为有用的讨论组，可在消失前还原
      </div>
      <div class="container">
        <a class="ui-cell" v-on:click="jump(item.id, 'manage')" v-for="(item, index) in toApprove" v-bind:key="index">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <div><span class="participantsNum">{{item.participantsNum}}</span>{{item.title}}</div>
              <div class="blue">{{item.expriationTime}}</div>
            </div>
          </div>
          <div class="ui-cell__ft"></div>
        </a>
      </div>
    </div>
  </div>
</template>

<style scoped>
  .participantsNum{
    display: inline-block;
    width: 30px;
    height: 16px;
    vertical-align: top;
    margin-top: 2px;
    position: relative;
    text-align: center;
    background-color: #5badff;
    color: #fff;
    font-size: 12px;
    border-radius: 1px;
    margin-right: 6px;
    line-height:16px;
  }
  .blue{
    font-size: 1rem;
    color: #5badff;
    margin-left: 37px;
  }
</style>

<script>
export default {
  name: 'forumApproval',
  data () {
    return {
      toApprove: [],
      listenersUid: []
    }
  },
  created () {
    let that = this
    this.axios.post('../../../forum/getManagerForumPost.do', {
      userID: this.sphdSocket.user.userID
    })
      .then(function (response) {
        console.log(response)
        let getData = response.data.data
        that.toApprove = getData.managerList
      })
      .catch(function (error) {
        console.log(error)
      })
    this.listenersUid = [
      this.sphdSocket.subscribe('manageTheForum', function (data) {
        console.log('approvalForumPost user:' + data)
        let getData = JSON.parse(data)
        let personnelReimburse = getData.forumPost
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.toApprove.unshift(personnelReimburse)
        } else if (operate < 0) {
          let reimburseId = personnelReimburse.id
          let _index = -1
          that.toApprove.forEach(function (item, index) {
            if (item.id === reimburseId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.toApprove.splice(_index, 1)
          }
        }
      }, null, 'user')
    ]
    console.log(that.sphdSocket.org.id)
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    jump: function (id, mark) {
      this.$router.push({
        path: `/discussDetail/${id}/${mark}`
      })
    }
  }
}
</script>
