import {request} from "@/utils/request";
/* ------------------培训管理------------------ */

// creator: 张旭博，2024-07-05 08:39:35，获取题库列表
export function getQuestionBankList(data) {
    return request({
        url: '/trainManage/selectQuestionBankList.do',
        data: data
    })
}

// creator: 张旭博，2024-07-05 10:54:15， 获取题库编号
export function getQuestionBankNewCode() {
    return request({
        url: '/trainManage/selectQuestionBankCode.do'
    })
}

// creator: 张旭博，2024-07-05 10:54:15， 新增题库
export function sureNewQuestionBank(data) {
    return request({
        url: '/trainManage/addQuestionBank.do',
        data: data
    })
}

// creator: 张旭博，2024-07-05 02:14:32， 获取某试题库的素材列表
export function getMtListByBank(data) {
    return request({
        url: '/trainManage/selectQuestionBankDetail.do',
        data: data
    })
}

// creator: 张旭博，2024-07-05 02:07:39， 保存素材
export function saveMt(data) {
    return request({
        url: '/trainManage/addTrainingMaterial.do',
        data: data
    })
}

// creator: 张旭博，2024-07-05 02:07:39， 查看素材
export function seeMt(data) {
    return request({
        url: '/trainManage/selectTrainMaterialDetail.do',
        data: data
    })
}

// creator: 张旭博，2024-07-11 05:00:20， 停用题库
export function stateQBank(data) {
    return request({
        url: '/trainManage/updateQuestionBank.do',
        data: data
    })
}

// creator: 张旭博，2024-07-05 02:07:39， 停用题库前的判断
export function deleteJudgeQBank(id) {
    return request({
        url: '/trainManage/deleteQuestionBankJudge.do',
        data: {id: id}
    })
}
// creator: 张旭博，2024-07-05 02:07:39， 删除题库
export function deleteQBank(id) {
    return request({
        url: '/trainManage/deleteQuestionBank.do',
        data: {id: id}
    })
}

// creator: 张旭博，2024-07-05 02:07:39， 停用素材
export function stateMt(data) {
    return request({
        url: '/trainManage/updateTrainingMaterial.do',
        data: data
    })
}

// creator: 张旭博，2024-07-05 02:07:39， 删除素材前的判断
export function deleteJudgeMt(id) {
    return request({
        url: '/trainManage/deleteTrainingMaterialJudge.do',
        data: {id: id}
    })
}
// creator: 张旭博，2024-07-05 02:07:39， 删除素材前的判断
export function deleteMt(id) {
    return request({
        url: '/trainManage/deleteTrainingMaterial.do',
        data: {id: id}
    })
}

// creator: 张旭博，2024-07-16 09:47:56， 试题查看(获取试题详情)
export function getQuestionDetail(data) {
    return request({
        url: '/trainManage/selectTrainQuestionDetail.do',
        data: data
    })
}
// creator: 张旭博，2024-07-16 09:47:56， 新增干扰项
export function addDistracter(data) {
    return request({
        url: '/trainManage/addDisableKey.do',
        data: data
    })
}

// creator: 张旭博，2024-07-16 09:47:56， 停用/启用干扰项
export function stateDistracter(data) {
    return request({
        url: '/trainManage/updateTrainingQuestionKey.do',
        data: data
    })
}
// creator: 张旭博，2024-07-16 09:47:56， 删除干扰项
export function deleteDistracter(data) {
    return request({
        url: '/trainManage/deleteDisableKey.do',
        data: data
    })
}

// creator: 张旭博，2024-07-16 09:47:56， 制作单选题
export function editSCQ(data) {
    return request({
        url: '/trainManage/addChoiceQuestion.do',
        data: data
    })
}

// creator: 张旭博，2024-07-16 09:47:56， 制作判断题
export function editTFQ(data) {
    return request({
        url: '/trainManage/addTfQuestion.do',
        data: data
    })
}

