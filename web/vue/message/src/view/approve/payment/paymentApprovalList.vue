<template>
  <div id="paymentApprovalList">
    <TY_NavTop title="个人报销付款审批" isSearch="true" @toggleList="search"></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待付款审批" name="1" closable='false' :msgCount="list1.length">
        <div class="tip">下列报销票据已获出纳审核通过，您审批后出纳方可付款。</div>
        <div>
          <a class="ui-cell" v-on:click="jump('21' + item.id, 0, '1')" v-for="(item, index) in list1" v-bind:key="index">
            <div class="item_fn">
              <div>{{item.createName | stringSplit(4)}}</div>
              <div>
                <p>申请报销 {{item.amount}} 元</p>
                <p>票据 {{item.billQuantity}} 张，共 {{item.billAmount}} 元</p>
              </div>
            </div>
            <div class="txt_right">{{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="已批准" name="2" closable='false' >
        <div class="tip"> 下列付款申请已获审批通过，有待付款。</div>
        <div>
          <a class="ui-cell" v-on:click="jump('22' + item.id, 0, '2')" v-for="(item, index) in list2" v-bind:key="index">
            <div class="item_fn">
              <div>{{item.createName | stringSplit(4)}}</div>
              <div>
                <p>申请报销 {{item.amount}} 元</p>
                <p>票据 {{item.billQuantity}} 张，共 {{item.billAmount}} 元</p>
              </div>
            </div>
            <div class="txt_right">{{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>
<style lang="less">
  #paymentApprovalList{
    .ui-cell{ display: block; }
    .tabs-tab{   width: 50%;  }
    .item_fn{
      display: flex;
    div:nth-child(1){ flex:1;  }
    div:nth-child(2){ flex:3;  }
    }
    .tip{
      padding:5px 12px;
      background-color: #E2EFDA;
    }
    .txt_right{
      text-align: right;
      font-size:11px;
      padding-right:15px;
    }
  }

</style>

<script>
export default {
  name: 'paymentApprovalList',
  data () {
    return {
      list1: [],
      list2: [],
      listenersUid: [],
      tabValue: '1'
    }
  },
  created () {
    this.tabValue = this.$store.getters.getFinReimburseNav
    let that = this
    this.listenersUid = [
      this.sphdSocket.subscribe('paymentHandle', function (data) {
        let info = JSON.parse(data)
        that.list1 = info
        console.log('paymentApply')
        console.log(that.list1)
      }),
      this.sphdSocket.subscribe('paymentApproval', function (data) {
        let info = JSON.parse(data)
        that.list2 = info
        console.log('paymentApproval-1')
        console.log(that.list2)
      }),
      this.sphdSocket.subscribe('paymentHandle', function (data) {
        let info = JSON.parse(data)
        that.list1 = info
        console.log('paymentApproval-2')
        console.log(that.list1)
      }, 'user'),
      this.sphdSocket.subscribe('paymentApproval', function (data) {
        let info = JSON.parse(data)
        that.list2 = info
        console.log('paymentApproval-3')
        console.log(that.list2)
      }, 'user')
    ]
    let userId = this.sphdSocket.user.userID
    let session = this.sphdSocket.sessionid
    this.sphdSocket.send('getPayApply', { userId: userId, session: session })
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  filters: {
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  methods: {
    jump: function (id, param, tab) {
      console.log('param : ' + param)
      this.$store.dispatch('setFinReimburseNav', tab)
      this.$router.push({
        path: `/paymentApprovalDetail/${id}/${param}`
      })
    },
    search: function () {
      this.$router.push({
        path: `/paymentHandleSearch`
      })
    }
  }
}
</script>
