package cn.sphd.miners.modules.investigate.dto;

import java.util.List;

public class RespInvestigateArea implements Comparable<RespInvestigateArea> {
    private List<RespInvestigateArea> investigateAreaList;
    private Integer row;//用于临时储存行数
    private  String  name;
    private  Integer sum;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    public Integer getSum() {
        return sum;
    }

    public void setSum(Integer sum) {
        this.sum = sum;
    }

    @Override
    public int compareTo(RespInvestigateArea p) {
        return p.getSum() - this.getSum();
    }
    public Integer getRow() {
        return row;
    }

    public void setRow(Integer row) {
        this.row = row;
    }

    public List<RespInvestigateArea> getInvestigateAreaList() {
        return investigateAreaList;
    }

    public void setInvestigateAreaList(List<RespInvestigateArea> investigateAreaList) {
        this.investigateAreaList = investigateAreaList;
    }
}
