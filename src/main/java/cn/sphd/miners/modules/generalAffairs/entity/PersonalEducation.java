package cn.sphd.miners.modules.generalAffairs.entity;

import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 教育表
 * Created by Administrator on 2015/12/10.
 */
@Entity
@Table(name="t_personal_education")
public class PersonalEducation implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="college_name"  , length=100 , nullable=true , unique=false)
    private String collegeName;//学校名称
    @Column(name="department_name"  , length=100 , nullable=true , unique=false)
    private String departmentName;//院系名称
    @Column(name="begin_time"   , nullable=true , unique=false)
    private Date beginTime;//起始时间
    @Column(name="end_time"   , nullable=true , unique=false)
    private Date endTime;//毕业时间
    @Column(name="major"  , length=100 , nullable=true , unique=false)
    private String major;//专业
    @Column(name="degree"  , length=100 , nullable=true , unique=false)
    private String degree;//学历 1-研究生，2-本科，3-大专，4-中专或高中，5-其他
    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;//说明
    @Column(name="offer")
    private Integer offer;//招聘id
    @Column(name="major_desc"  , length=255 , nullable=true , unique=false)
    private String majorDesc;//专业描述
    @Column(name="degree_desc"  , length=255 , nullable=true , unique=false)
    private String degreeDesc;//学历/学位描述
    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;
    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;
    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;
    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;
    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;
    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;
    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation; //操作:1-增,2-删,3-改
    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId; //修改前记录ID
    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo=0; //版本号
    @Column(name="version_memo"  , length=100 , nullable=true , unique=false)
    private String versionMemo; //版本说明
    @Column(name="sub_previous_id"   , nullable=true , unique=false)
    private Integer subPreviousId; //子表修改前记录ID
    @Column(name="sub_version_no"   , nullable=true , unique=false)
    private Integer subVersionNo; //子表版本号
    @Column(name="sub_version_memo"  , length=100 , nullable=true , unique=false)
    private String subVersionMemo; //子表版本说明
    @Column(name = "is_deleted")
    private boolean isDeleted=false;//逻辑删除标志:true-已删除

    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.LAZY )
    @JoinColumn(name="user", referencedColumnName = "userId" , nullable=true , unique=false , insertable=true, updatable=true)
    private User user;

    @Column(name="user"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer user_;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getCollegeName() {
        return collegeName;
    }

    public void setCollegeName(String collegeName) {
        this.collegeName = collegeName;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getMajor() {
        return major;
    }

    public void setMajor(String major) {
        this.major = major;
    }

    public String getDegree() {
        return degree;
    }

    public void setDegree(String degree) {
        this.degree = degree;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Integer getUser_() {
        return user_;
    }

    public void setUser_(Integer user_) {
        this.user_ = user_;
    }

    public Integer getOffer() {
        return offer;
    }

    public void setOffer(Integer offer) {
        this.offer = offer;
    }

    public String getMajorDesc() {
        return majorDesc;
    }

    public void setMajorDesc(String majorDesc) {
        this.majorDesc = majorDesc;
    }

    public String getDegreeDesc() {
        return degreeDesc;
    }

    public void setDegreeDesc(String degreeDesc) {
        this.degreeDesc = degreeDesc;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public String getVersionMemo() {
        return versionMemo;
    }

    public void setVersionMemo(String versionMemo) {
        this.versionMemo = versionMemo;
    }

    public Integer getSubPreviousId() {
        return subPreviousId;
    }

    public void setSubPreviousId(Integer subPreviousId) {
        this.subPreviousId = subPreviousId;
    }

    public Integer getSubVersionNo() {
        return subVersionNo;
    }

    public void setSubVersionNo(Integer subVersionNo) {
        this.subVersionNo = subVersionNo;
    }

    public String getSubVersionMemo() {
        return subVersionMemo;
    }

    public void setSubVersionMemo(String subVersionMemo) {
        this.subVersionMemo = subVersionMemo;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public boolean isDeleted() {
        return isDeleted;
    }

    public void setDeleted(boolean deleted) {
        isDeleted = deleted;
    }
}
