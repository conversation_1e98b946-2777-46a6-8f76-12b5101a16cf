package cn.sphd.miners.modules.publicInformation.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.publicInformation.dao.PiInfoVisitDto;
import cn.sphd.miners.modules.publicInformation.entity.PiInfoVisit;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
@Repository
public class PiInfoVisitDaoImpl extends BaseDao<PiInfoVisit, Serializable> implements PiInfoVisitDto {
}
