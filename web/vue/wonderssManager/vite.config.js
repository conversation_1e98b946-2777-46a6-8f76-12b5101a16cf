import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
//export default defineConfig({
//  plugins: [
//    vue(),
//  ],
//  resolve: {
//    alias: {
//      '@': fileURLToPath(new URL('./src', import.meta.url))
//    }
//  }
//})
export default ({command, mode}) => {
  const envConfig = loadEnv(mode, './', ['VITE_', 'VUE_'])
  let config = {
    server: {
      allowedHosts: true, // 允许所有主机
      // 或者指定特定的主机名
      // allowedHosts: ['localhost', 'example.com']
    },
    plugins: [
        vue(),
    ],
    resolve: {
      api: 'modern-compiler',
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },

    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern',
          silenceDeprecations: ["legacy-js-api"],
          additionalData: `@use "./src/style/variable.scss" as *;` // 此处全局的scss文件
        }
      }
    },
    define: {
      'process.env': envConfig
    },
    base: './',
    publicPath: './',
    transpileDependencies: true,
    build: {
      chunkSizeWarningLimit: 65536,
      target: [ 'es2022' ]
    },
    performance: {
      hints: 'warning',
      maxEntrypointSize: 1048576,
      maxAssetSize: 33554432
    }
  }
  return defineConfig(config)
}
