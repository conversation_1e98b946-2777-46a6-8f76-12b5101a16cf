package cn.sphd.miners.modules.finance.service;

import cn.sphd.miners.modules.finance.entity.FinanceAccountBill;
import cn.sphd.miners.modules.finance.entity.FinanceReturn;
import cn.sphd.miners.modules.finance.entity.FinanceReturnHistory;
import cn.sphd.miners.modules.system.entity.User;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2016/12/15.
 */
public interface FinanceReturnService {
    List<FinanceReturn> getAllReturn(Integer oid, Integer type, String state, Date beginDate);

    FinanceReturn getReturn(Integer returnId);

    void updateReturn(FinanceReturn financeReturn1);

    List<FinanceAccountBill> getAccountBill(Integer oid, String type);

    FinanceAccountBill getBill(Integer returnId);
    FinanceAccountBill getBill(Integer returnId,String type);

    Map<String,Object> chooseCheque(Integer oid,String type,Map<String,Object> map);

    Map<String, Object> saveBank(FinanceReturn financeReturn, Integer returnId ,User user,Map<String,Object> map);   //存入银行

    Map<String, Object> chooseChequeOrBank(Integer oid,Integer type,Map<String, Object> map);  //返回各支出方式的支票、汇票、银行

    Map<String, Object> getReturnNum(Integer oid, Integer type);

    Map<String,Object> getReturnNumByConditional(Integer oid, Integer type,Integer year,String state);

    Map<String,Object> getReturnStatistics(Integer oid, Integer type, String state, Date beginDate);

    List<FinanceReturn> getReturnStatisticsList(Integer oid, Integer type, String state, Date beginDate,String oppositeCorp,Integer returnId);

    Map<String,Object> getReturnStatisticState(Integer oid, Integer type, Date beginDate);

    Map<String,Object> updateReturnDetail(User user,FinanceReturn financeReturn);

    List<FinanceReturnHistory> updateReturnRecords(Integer returnId);

    Map<String,Object> updateBankAndPay(User user,FinanceReturn financeReturn);

}
