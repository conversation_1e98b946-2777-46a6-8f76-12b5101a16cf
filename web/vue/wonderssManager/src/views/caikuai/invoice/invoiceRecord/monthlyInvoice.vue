<template>
  <div class="box">
    <div class="head">
      <PageHeader title="财会" @mycloseFun="handleCustomClose" :showClose=true :showBack=true>
      </PageHeader>
    </div>
    <div class="sticky padding-10-15 ty-fff" v-if="list.monthStatistics">
      <p>{{ new
      Date(list.beginTime).format('yyyy-MM-dd')}}至{{ new
      Date(list.endTime).format('yyyy-MM-dd')}}期间</p>
      <p>开具发票{{list.monthStatistics.num}}张，金额{{list.monthStatistics.totalAmount}}元</p>
    </div>
    <div class="re">
      <router-view/>
    </div>
  </div>

</template>

<script>
import {getInvoiceListMonth} from '@/api/financeBill'

export default {
  name: "monthlyInvoice",
  data() {
    return {
      list: {},
      invoiceDetailsList: {},
    }
  },
  mounted() {
    let invoiceDetailsList = localStorage.getItem('invoiceDetailsList');
    this.invoiceDetailsList = JSON.parse(invoiceDetailsList);
    console.log(invoiceDetailsList, 'invoiceDetailsList')
    this.getList(this.invoiceDetailsList)
  },
  methods: {
    handleCustomClose() {
      this.$router.push("/caikuai")
    },
    getList(invoiceDetailsList) {
      const params = {
        type: invoiceDetailsList.type,
        beginDate: new Date(invoiceDetailsList.beginDate).format('yyyy-MM-dd'),
        endDate: new Date(invoiceDetailsList.endDate).format('yyyy-MM-dd'),
      }
      console.log(params, 'paramssssssssssssssssssssssssss')
      getInvoiceListMonth(params).then(res1 => {
        this.list = res1.data.data
        let beginDate = new Date(this.list.beginTime).format('yyyy-MM-dd')
        const typeMonth = 2
        const dateType = 2
        let invoiceTypeList = {beginDate, typeMonth, dateType}
        console.log(invoiceTypeList, 'invoiceTypeListttttttttttttt')
        localStorage.setItem('invoiceTypeList', JSON.stringify(invoiceTypeList))
        console.log(res1.data.data, 'res1.data.data')
      }).catch(error => {
        console.error('Error:', error);
      })
    }

  }

}
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";

.head {
  position: fixed;
  width: 100%;
  z-index: 999;
}

.re {
  position: relative;
  top: 40px;
}

.sticky {
  line-height: 30px;
  padding: 2px 15px;
}
</style>