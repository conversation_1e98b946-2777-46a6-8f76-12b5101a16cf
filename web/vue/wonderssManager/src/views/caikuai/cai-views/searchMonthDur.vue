<!-- 搜索 日列表  -->
<template>
  <div class="head">
    <div style="width: 100%">
      <PageHeader title="财会" :showBack="true" :runMyBackFun="true" @myBackFun="backFun" :showClose="true" @mycloseFun="closeFun"></PageHeader>
    </div>

  </div>
  <p class="margin-10-15">{{ todayTime }}</p>
  <div class="hr_gray"></div>
  <div class="current-balance-1 margin-10-15">
    <div>
      <p class="ty-color-orange">{{ bankName }}</p>
      <p>本期余额</p>
    </div>
    <span>{{ allBalance }}</span>
  </div>
  <div class="hr_gray"></div>
  <div class="flex">
    <div class="balance1" v-for="(item) in bala_list" :key="item.id">
      <div>
        <img :src="item.sr" alt="">
      </div>
      <div>
        <span :class="item.style">{{ item.da }} <br> {{ item.money }}</span>
      </div>
    </div>
  </div>
  <div class="hr_gray"></div>
  <div class="flex date-account">
    <div @click="tab(1)" :class="num === 1 ? 'padding_bottom_green':''">按时间查看</div>
    <div @click="tab(2)" :class="num === 2 ? 'padding_bottom_green':''">按账户查看</div>
  </div>
  <div>
    <router-view />
  </div>
</template>

<script>
import finance from '@/mixins/finance.js'
export default {
  name: "currentAccountDate",
  mixins: [finance],
  data() {
    return {
      accID:'',
      num:1,
      todayTime:'今天是XXXX年X月XX日 星期X',
      allBalance:0,
      bala_list: [
      ],
      accountList: []
    }
  },
  mounted() {
    this.getAccountList()
    let name = this.$route.name
    console.log('name=', name)
    if(name === 'viewByTimeDayList'){
      this.num = 1
    }else{
      this.num = 2
    }
  },
  methods: {
    backFun(){
      this.$router.push("/timePicker");
    },
    tab(num){
      if(this.num != num){
        this.num = num
        if(num === 1){
          let path = `/viewByTimeDayList`
          this.$router.push({ path:path })
        }else if(num === 2){
          let path = `/ViewByAccountDayList`
          this.$router.push({ path:path })
        }
      }
    },
    getAccountList(fid){
      let org = this.auth.getOrg()
      let beginDate = localStorage.getItem('searchMonthDur3_beginDate')
      let endDate = localStorage.getItem('searchMonthDur3_endDate')
      console.log('beginDate =', beginDate)
      console.log('endDate =', endDate)

      if(beginDate){
        if(!endDate){
          let curMonth = beginDate.substr(0,7)
          let mm = curMonth.split('-')[1]
          let endDay = '30'
          if([1,3,5,7,8,10,12].indexOf(Number(mm) ) > -1){
            endDay = '31'
          }else if(Number(mm) === 2){
            let year = curMonth.split('-')[0]
            if(year % 4 ==0 && year % 100 !=0 || year % 400 ==0){ // alert('闰年')
              endDay = '29'
            }else{ // alert('平年')
              endDay = '28'
            }
          }
          endDate = curMonth + '-' + endDay
        }

        this.todayTime = beginDate + ' ~ ' + endDate
        let params = { oid:org.id , monthOrDay:2 , beginDate:beginDate, endDate:endDate }
        this.financeApi.getAccountMonthOrDayApp(params).then(res1=>{
          let res = res1.data.data
          console.log('getAccountMonthOrDayApp res=', res)
          this.allBalance = this.formatCurrency(res.allBalance)
          this.bankName = res.bankName
          let accountDetailList = res.accountDetailList || []
          let accountPeriodList = res.accountPeriodList || []
          this.bala_list = [
            {id: 0, da: "上期余额", money: this.formatCurrency(res.allPreviousBalance ) , sr: this.balance1 },
            {id: 1, da: "本期收入", money: this.formatCurrency(res.allCredit ) , sr:  this.balance2 ,style: "ty-color-green"},
            {id: 2, da: "本期支出", money: this.formatCurrency(res.allDebit) , sr: this.balance3 ,style: "ty-color-red"},
          ]

        }).catch(err=>{
          console.log('err=', err)
          this.$message.error('链接失败，请重试！')
        })
      }else{
        this.$message.error('没有具体日期哦！')
      }

    },

  }
}
</script>

<style scoped lang="scss">
@import "../../../style/account.scss";
@import "../../../style/common.scss";
.centerSp{ text-align: center; }
</style>