<template>
  <div id="applyLeaveQuery">
    <TY_NavTop title="筛选"></TY_NavTop>
    <div class="container">
      <div class="ui-message">
        <div class="sendContainer">
          <div class="tip">请确定要查询内容的筛选条件</div>
          <el-form :model="queryForm" ref="educationForm" label-width="70px" class="educationForm">
            <h4 class="item-header">事件结果</h4>
            <el-radio-group v-model="queryForm.approveStatus">
              <el-radio :label="2">被批准的请假</el-radio>
              <el-radio :label="3">被驳回的请假</el-radio>
            </el-radio-group>
            <h4 class="item-header">发生时间</h4>
            <div class="el-form-item">
              <el-date-picker type="date" placeholder="开始日期" v-model="beginDate" style="width: 130px;" value-format="yyyy-MM-dd"  size="small" v-on:change="clearOther('date')"></el-date-picker> -
              <el-date-picker type="date" placeholder="结束日期" v-model="endDate" style="width: 130px;" value-format="yyyy-MM-dd"  size="small" v-on:change="clearOther('date')"></el-date-picker>
            </div>
            <el-form-item label="仅看年报">
              <el-date-picker type="year" placeholder="选择年份" v-model="year" style="width: 100%;" size="small" value-format="yyyy" v-on:change="clearOther('year')"></el-date-picker>
            </el-form-item>
            <el-form-item label="仅看月报">
              <el-date-picker type="month" placeholder="选择月份" v-model="month" style="width: 100%;" size="small" v-on:change="clearOther('month')"></el-date-picker>
            </el-form-item>
          </el-form>
          <div class="handle_button">
            <input type="submit" class="ui-btn ui-btn_info" value="确定" @click="submitForm(1)">
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'applyLeaveQuery',
  data () {
    return {
      queryForm: {
        approveStatus: 2
      },
      year: '',
      month: '',
      beginDate: '',
      endDate: ''
    }
  },
  methods: {
    submitForm: function () {
      // userId 登录人
      // state （1- 自定义 ，2 -仅看年报，3- 仅看月报）
      // approveStatus   2- 被批准的，3-被驳回的
      // beginDate 开始时间   格式举例： 2018-09-01   选择年报时需要拼成这个格式，月报同理
      // endDate 结束时间   格式举例： 2018-09-01
      this.loading = true
      this.queryForm.userId = this.sphdSocket.user.userID
      if (this.year !== '') {
        this.queryForm.state = 2
        this.queryForm.beginDate = this.year + '-01-01'
        this.queryForm.endDate = this.year + '-12-31'
      } else if (this.month !== '') {
        this.queryForm.state = 3
        let date = new Date(this.month)
        console.log(date)
        let year = date.getFullYear()
        let month = date.getMonth() + 1
        let lastDayOfMonth = new Date(year, month, 0)
        this.queryForm.beginDate = year + '-' + month + '-01'
        this.queryForm.endDate = year + '-' + month + '-' + lastDayOfMonth.getDate()
      } else {
        this.queryForm.state = 1
        this.queryForm.beginDate = this.beginDate
        this.queryForm.endDate = this.endDate
      }
      if (this.queryForm.beginDate) {
        console.log(JSON.stringify(this.queryForm))
        localStorage.setItem('query', JSON.stringify(this.queryForm))
        this.$router.push({
          path: '/applyLeaveQueryPer',
          name: 'applyLeaveQueryPer',
          params: {
            data: this.queryForm
          }
        })
      } else {
        this.loading = false
        this.$kiko_message('请选择查询时间')
      }
    },
    clearOther: function (type) {
      if (type === 'date') {
        this.year = ''
        this.month = ''
      } else if (type === 'year') {
        this.month = ''
        this.beginDate = ''
        this.endDate = ''
      } else if (type === 'month') {
        this.year = ''
        this.beginDate = ''
        this.endDate = ''
      }
    }
  }
}
</script>
