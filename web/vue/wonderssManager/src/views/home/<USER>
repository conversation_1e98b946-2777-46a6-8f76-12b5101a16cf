<template>
  <div class="shouye">
    <PageHeader title="首页" :showClose=false :showBack="false">
    </PageHeader>
    <div>
      <el-row class="trItem">
        <el-col v-for="(item, index) in enableButtons" :span="8"><div class="grid-content" @click="homePage(item.url)">
          <img :src="item.iconData" alt=""><br>
          <span>{{item.name}}</span>
        </div></el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import caikuai from '@/assets/commonImg/caikuai.png'
import dingdan from '@/assets/commonImg/dingdan.png'
import kehu from '@/assets/commonImg/kehu.png'
import qiceh from '@/assets/commonImg/qiceh.png'
import quanxian from '@/assets/commonImg/quanxian.png'
import shiwuzichan from '@/assets/commonImg/shiwuzichan.png'
import tousu from '@/assets/commonImg/tousu.png'
import xiaoshoutongji from '@/assets/commonImg/xiaoshoutongji.png'
import zhigong from '@/assets/commonImg/zhigong.png'
import ziyuanzhongxin from '@/assets/commonImg/ziyuanzhongxin.png'
import JSONBig from "json-bigint";

export default {
  props: ['homePageAndWorks'],
  data() {
    return {
      enableButtons: [],
    }
  },
  components: {},
  watch: {
    homePageAndWorks : {
      immediate: true,
      deep: true,
      handler (newVal) {
        let homePageList = newVal.homePageList??[]
        // console.log('shouye watch homePageList', homePageList)
        this.mergeButtons(homePageList)
      }
    }
  },
  created() {

  },
  mounted() {
  },
  destroyed() {

  },
  methods: {
    mergeButtons(homePageList) {
      const readybuttons = [
        {iconData: kehu, url: '', mid: 'bf'},//客户
        {iconData: dingdan, url: '', mid: 'bh'},//订单
        {iconData: zhigong, url: '', mid: 'be'},//职工
        {iconData: caikuai, url: 'caikuai', mid: 'bb'},//财会
        {iconData: ziyuanzhongxin, url: '', mid: 'bd'},//文件与资料
        {iconData: shiwuzichan, url: '', mid: 'bc'},//实物资产
        {iconData: qiceh, url: '', mid: 'bca'},//车务
        {iconData: quanxian, url: '', mid: 'bi'},//权限
        {iconData: tousu, url: '', mid: 'bj'},//投诉
        {iconData: xiaoshoutongji, url: '', mid: 'bl'},//销售统计
        // {iconData: '', url: '', mid: 'bk'},//商品
        // {iconData: '', url: '', mid: 'bm'},//仓库
        // {iconData: '', url: '', mid: 'bea'},//登录记录
        // {iconData: '', url: '', mid: 'beb'},//职工档案
        // {iconData: '', url: '', mid: 'bec'},//考勤
        // {iconData: '', url: '', mid: 'bed'},//请假动态
        // {iconData: '', url: '', mid: 'bee'},//加班动态
        // {iconData: '', url: '', mid: 'bef'},//工资
        // {iconData: '', url: '', mid: 'beg'},//工作记录
        // {iconData: '', url: '', mid: 'bfa'},//客户信息
        // {iconData: '', url: '', mid: 'bfb'},//订购信息
        // {iconData: '', url: '', mid: 'bfc'},//账务信息
        // {iconData: '', url: '', mid: 'bia'},//审批权限
        // {iconData: '', url: '', mid: 'bma'},//原辅材料库
        // {iconData: '', url: '', mid: 'bmb'},//成品库
      ]
      let buttonList = JSONBig.parse(JSONBig.stringify(readybuttons))
      this.enableButtons = buttonList.filter(button => {
        const popedom = homePageList.find(item => item.mid === button.mid);
        if (popedom) {
          return Object.assign(button, popedom)
        }
        return false
      })
      console.log('mergeButtons', this.enableButtons)
    },
    homePage(url) {
      console.log('url=', url) // caikuai
      if((url??'').trim().length>0) {
        // this.$router.push({ name: url })
        this.$router.push('/'+url)
      } else {
        this.$message.info('后续内容 敬请期待！')
      }
    }
    //   if (index == 0) {
    //     this.$router.push("/caikuai")
    //   } else if(index==1){
    //     this.$router.push("/salesHome")
    //   } else if(index==2){
    //     this.$router.push("/customerList")
    //   }
    // }
  }
}

</script>

<style lang="scss" scoped>
.shouye {
  .grid-content{
    text-align: center;
    padding: 30px 0; border-right: 1px solid #eee;
    border-bottom: 1px solid #eee;
    img{
      width: 20%; display: inline-block;
    }
    span{
      font-size: 14px; color: #666; margin-top: 10px; display: inline-block;
    }
  }
  .trItem{
    &>div:nth-child(3){
      .grid-content {
        border-right: none;
      }
    }

  }
}
</style>