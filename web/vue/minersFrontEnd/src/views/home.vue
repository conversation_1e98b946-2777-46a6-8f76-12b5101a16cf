<template>
  <div class="home">
    <div class="navList">
      <div class="navItem" v-for="(nav, indexNav) in navList" :key="indexNav" :class="navActive === indexNav ? 'active' : ''" @click="navClick(nav, indexNav)">
        <div  >
          <span>{{ nav.name }}</span>
          <el-icon v-if="nav.pageName!='desktop'" @click.stop="closeNav(nav, indexNav)"><Close /></el-icon>
        </div>
      </div>
    </div>
    <div class="homeHetght" id="mainContent">
      <router-view/>
    </div>
  </div>
</template>
<script>
import JSONBig from 'json-bigint'
import {nanoid} from 'nanoid'
import {initNav, jumpToMenu, removeActivePage, showActivePage} from "@/utils/routeChange";
import store from "@/store";

export default {
  data() {
    return {
      // activePages:[],
      // rootPath: {},
      timer:'',
      navList: [],
        // { 'name':'我的桌面', 'mid':'wodezhuomian', 'url':'', 'pageName':'desktop', 'navId': nanoid() },
      // ],
      navActive:0
    }
  },
  watch: {
    '$store.state': {
      deep: true,
      immediate: true,
      handler(newVal,oldVal){
        let newActivePage = newVal.activePage
        if(newActivePage.length>0) { // 空白的话等待initDesktop后再执行
          this.updateNavList(newActivePage)
          this.$store.dispatch('setActivePage', newActivePage)
        }
      }
    },
  },
  mounted() {
    initNav({mid:'wodezhuomian', name:'我的桌面', pageName:'desktop'})
    this.updateNavList()
  },
  methods: {
    updateNavList(newActivePage){
      let newActivePages = newActivePage??this.$store.state.activePage
      let narr = []
      let oldNArr = JSONBig.parse(JSONBig.stringify(this.navList))
      let allSame = newActivePages.length == oldNArr.length
      // console.log('allSame-1', newActivePages.length, oldNArr.length, oldNArr)
      // console.log('allSame0', allSame)
      let newIndex = -1
      newActivePages.forEach((page, index) => {
        let menuI = page.menuInfo
        let showThis = page.showThis
        narr.push({
          'showThis': showThis,
          'name': menuI.name,
          'mid': menuI.mid,
          'url': menuI.url,
          'pageName': menuI.pageName,
          'navId': nanoid()
        })
        if(showThis){
          newIndex = index
        }
        // console.log('allSame1', allSame)
        if(allSame && oldNArr.find(oldAr => oldAr.mid === menuI.mid) === undefined) {//新加入的
          allSame = false
        }
      })
      // console.log('allSame2', allSame)
      if(newIndex >= 0 && this.navActive != newIndex){//切换tab
        this.navActive = newIndex
        allSame = false
      }
      // console.log('allSame3', allSame)
      if(!allSame){
        this.navList = narr
        // 设置了， 说明有新的需要跳转的
        let curNav = this.navList[this.navActive]
        jumpToMenu(curNav)
      }
    },
    closeNav(navItem, indexNav){
      if (indexNav >= 0) {
        removeActivePage(indexNav)
        this.navList.splice(indexNav, 1)
      }
    },
    navClick(navItem, indexNav){
      // console.log('tab index navClick indexNav', indexNav)
      showActivePage(indexNav)
    },
  },
}
</script>

<style lang="scss" scoped>
.home{
  .navList{
    background: #f3f5f9;
    height: 30px;
    line-height: 30px;
    display: flex;
    align-items: center;
    .navItem{
      font-size:14px;
      cursor: default;
      background: #e1e4ea;
      position: relative;
      color: #646b73;
      border-right: 1px solid #dbdee3;
      padding: 0 24px 0 16px;
      .el-icon{
        position: absolute;
        right: 4px;
        top: 8px;
        vertical-align: middle;
        color: #ccc;
        margin-left: 4px;
        cursor: pointer;

      }
      &:hover{
        background: #dadee4;
        color: #4d545c;
        .el-icon{
          color: #333;
        }
      }
      &.active{
        background: #fff;
        color: #2b3643;
        border-right-color: #e8ebf0;
      }
    }
  }
  .homeHetght{
    overflow: auto;
    &:before{
      content: '';
      display: table;
    }
  }
}
</style>
