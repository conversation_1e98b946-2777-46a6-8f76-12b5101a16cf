.btnn{ display:inline-block;line-height:25px; padding:0 10px; border:1px solid #ccc;border-left-color:#ccc; margin:0; border-radius:5px;    }
.btnn-big{ padding:0 30px; line-height:35px;  }
.btn-circle{ border-radius:5px; }
.btnn:hover{ background:#ececec; cursor:default;    }
.btnnActive{ background:#eaeaea; }
.control{ color:#00a0e9;   }
.btnGroup{ display:inline-block; border:1px solid #ccc; border-radius:5px; overflow:hidden;      }
.btnGroup .btnn:first-child{ border-left:none;  }
.btnGroup .btnn{ border-right:none; border-top:none; border-bottom:none; margin-left:-5px; border-radius:0px;      }
.em2{ padding:2px 15px;  }
.left{ float:left; }
.right{ float:right; }
.clr{ clear:both;  }
.navtab{ padding:10px 0;   }
.riBtn{ position:absolute;right:0; top:0;    }

.dataNav table td.textLeft, .dataList table td.textLeft {  text-align:left;       }
.dataNav table td.ind2, .dataList table td.ind2 {  text-align:left; text-indent:2em;       }
/*这是很重要的东西*/
#betweenDate{   font-size:16px;     }
/* Four big data  */
.indexBg{background:url(indexForAdmin.png)  no-repeat; display:inline-block; height:128px; width: 128px;    }
.theMonthPay{height:80px;width:80px;background-position:0 0;}
.prevMonth{height:80px;width:80px;background-position:-80px 0;}
.theMonthGet{height:80px;width:80px;background-position:-160px 0;}
.theMonthLast{height:80px;width:80px;background-position:-240px 0;}
/* SearchContainer */
ul.searchCon:before{  border-bottom: 7px solid rgba(0,0,0,0.2);  border-left: 7px solid transparent;  border-right: 7px solid transparent; content: "";
     display: inline-block !important; position: absolute;  right: 58px;  top: -7px;      }
ul .trigle{  border-bottom: 5px solid #fff;  border-left: 5px solid transparent;  border-right: 5px solid transparent;  content: "";
     display: inline-block !important; position: absolute;  right: 60px; top: -5px;       }
.searchCon{ left: 100px; width:350px; border:1px solid #ccc; box-shadow:3px 3px 5px #666;      }
.searchCon .ttl{ display:inline-block; width:100px; text-align:right;    }
.searchCon input{ display:inline-block; width:215px; height:35px; line-height:35px;padding:0 5px;      }
.searchCon li{margin-top:10px; }
.searchCon .ctl{ text-align:right; padding-right:30px; margin :30px 0 15px 0 ;     }
/*四个的*/
.mainNav{display: flex; justify-content: space-between}
.pal{ color:#fff; text-align:right; height:100px; position:relative; overflow:hidden ;  width: 410px;margin-left: 8px;   }
.visual{ position:absolute; bottom:0;  left:0;      }
.visual i{ font-size:100px; opacity: 0.1;     }
.number{ font-size:35px;  }
.details{ padding:10px;  }
.desc{ font-size:16px;   }
.bg_blue{ background: #3598DC ;  }
.bg_red{ background: #E7505A ;  }
.bg_green{ background: #32C5D2 ;  }
.bg_purple{ background: #8E44AD ;  }
#duringLevel{ margin-bottom:50px;  }
.dataInfo{ display:none;  }

/*radioBtn style*/
span.radioBtn{ padding:0 4px; display:inline-block; width:20px; min-width:20px; height:20px; border-radius:10px!important; top:3px; background:#fff; position:relative; border:1px solid #ccc;    }
span.radioBtn>.val{ display:none;    }
span.valShow{ width:150px; min-width:150px; border:none; padding:0 5px; line-height:35px;    }
span.radioBtn>.radioShow{ display:inline-block; background:#fff; border-radius:5px!important; width:10px; height:10px;position: relative;  top: -2px; }
span.radioActive>.radioShow{  background:#9d9d9d; box-shadow:0 0 2px #666;  }
.con span.radioBtn>.radioShow{ top: -7px; }
/*详情页的基本展示*/
.itemTr{ padding:5px 0; position:relative;     }
.itemTr>span.ttl{ display: inline-block ; width:200px; text-align:right ; padding-right:10px;line-height:30px; position: absolute; top:5px;  }
.con{ display: inline-block ; line-height:30px; width:350px; position:relative; margin-left:200px;    }
.scan{ display:block ;  }
.update{ display: inline-block }
.update.disable{background: rgb(234, 234, 234) none repeat scroll 0% 0%}
.scan ,.update{ width:350px; border:1px solid #ccc; padding-left:10px; height:30px; overflow:hidden ;   }
.updateRadio{ width:350px; padding-left:10px; height:30px;    }
.mark{ background:rgba(0,0,0,0.15); position:absolute; width:100%; height:100%; z-index:1;           }
 /*动态展示图片*/
#info_images>img{ height:50px;      }
#bigImagcon{ position:absolute;  bottom: 100px; left:100px; display:none;      }
#bigImag{ display: block; width:650px; box-shadow:0 0 2px #000; }

.approvCon{ padding:20px; }
/*回款*/
#collectDetailSee ul{overflow: hidden;}
#collectDetailSee ul li{ float: left; width:50%; }
.cltByCrah,.cltByCheque,.cltByBill,.cltByBank{display: none;}
.pTtl{float:left;width:40%;}
.pCon{float:left;width:60%;}
#searchByFlow{display: none;}
#dataByLevel,#dataByAccount{display: none;}

.laydate_btn{ display:none;  }
.updateSubmit{
     width: auto;
     border: none;
     padding: 0 20px;
     overflow: hidden;
     background-color: #5d9cec;
     color: #fff;
     line-height: 30px;
     border-radius: 3px;
     cursor:pointer;
}
#info_images{
     overflow: hidden;
}
.imgBox{
     float: left;
     width:180px;
     position: relative;
     border: 1px solid #efefef;
     margin-bottom: 5px;
}
.imgBox img{
     width: 180px;
}
.imgClose{
     display: none;
     position: absolute;
     top: 0px;
     right: 0px;
     width: 20px;
     height: 20px;
     line-height: 19px;
     color: #fff;
     background-color: #666666;
     border-radius: 15px;
     text-align: center;
}
.imgClose:hover{
     background-color: #ed5565;
}
.imgBox:hover .imgClose{
     display: block;
}
.uploadFiles{
     width: 360px;
     border:1px dashed #666;
     padding: 20px 5px;

}
.item {
     overflow: hidden;
     clear: both;
     padding:5px 0;
     margin-bottom: 8px;
}
.item input{
     color: #7e7e7e;
}

.item-title{
     display: inline-block;
     width:130px;
     text-align: right;
     margin-right: 5px;
     vertical-align: top;
     line-height:30px;
     color: #5a5e6c;
}
.item-title-big{
     display: inline-block;
     width:300px;
     text-align: right;
     margin-right: 5px;
     vertical-align: top;
     line-height:30px;
     color: #5a5e6c;
}

.item-content{
     display: inline-block;
     width:160px;
     height: 30px;
     border-radius: 2px;
     color: #7e7e7e;
     line-height:20px;
     padding: 6px 8px;
}
.item-content-big{
     display: inline-block;
     width:700px;
     border-radius: 2px;
     color: #7e7e7e;
     line-height:20px;
     padding: 6px 8px;
}
.repaymentList .ty-table thead td {
     border-color: #d7d7d7 #d7d7d7 #d7d7d7 #d7d7d7;
     background-color: #eee;
     font-weight: bold;
     color: #666;
}
#ordLoanAddEdit, #generalScan, #borrowInfo, #inOrOut {
     width: 1080px;
}
.memo{
     width: 866px;
     height:60px;
     text-overflow:ellipsis;
     line-height:20px;
     padding: 6px 8px;
}

#chooseC li{ padding-left: 50px; }
#chooseC li:hover{ background: rgba(10, 158, 242, 0.05) }
#oppositeCorpDiv ul{
}
#oppositeCorpDiv ul li i{
     margin-right: 15px;
     color: #5d9cec;
}
#oppositeCorpDiv ul li{
     list-style: none;
     line-height: 30px;
}
.stakeholderCategoryContsiner{
     position: relative;
}
.stakeholderCategory option:hover{
     background: #eee;
}
.stakeholderCategory option{
     border-bottom: 1px solid #ddd;
     padding: 0 20px;
     cursor: pointer;
}
.stakeholderCategory{
     position: absolute;
     top:30px;
     background: #fff;
     z-index: 1;
     border: 1px solid #ddd;
     border-bottom: none;
     width: 100%;
     line-height: 28px;
     left: -1px;
}
.stakeholderCategoryText{ display: inline-block; width: 100%; height: 100%; }
.e3>span{
     width: 100px;
     display: inline-block;
     text-align: right;
     margin-right: 10px;
}
.linkBtn{
     cursor: pointer;
     color: #5d9cec;
     padding: 3px 10px;
     border: none;
     width: auto;
     text-align: center;
     line-height: 30px;
}
.poReL{
     position: relative;
}
.addOopBtn{
     position: absolute;
     right: -54px;
     top: -6px;
}

.cp_img_box{
     display: inline-block;
     width: 100px;
     position: relative;
     vertical-align: top;
}
.cp_img_box .fileType{
     padding-top: 20px;
}
.cp_img_box .fileType a{
     display: none;
     background-color: rgba(93, 156, 236,.9);
     color: #fff;
     height: 18px;
     line-height:18px;
     width:36px;
     margin: 0 auto 5px;
     font-size: 12px;
     text-align: center;
     border-radius: 2px;
     cursor: pointer;
}
.cp_img_box .fa-times {
     position: absolute;
     right: 6px;
     top: 0px;
     color: #d64835;
}
.cp_img_box:hover .fileType a{
     display: block;
}
.cp_img_box .fileType span.fa{
     display: none;
}
.cp_img_box:hover .fileType span.fa{
     display: block;
}
.cp_img_name{
     font-size: 12px;
     color: #666;
     text-align: center;
     word-break:break-all;
     max-height: 70px;
}


#scan_imgs_32,#scan_imgs_3_{
     border: none;
     margin-left: 180px;
     display: flex;
}
#scan_imgs_3{
     border: none;
     /*margin-left: 180px;*/
     display: flex;
}
.fileType {
     width: 60px;
     height: 80px;
     margin-left: 20px;
     margin-bottom: 2px;
     background-position: center;
     background-color: #eee;
     background-size:60px 80px;
}

.hd{ display: none!important; }
.flexbox{ display: flex; text-align: right; margin-top:15px;margin-bottom:15px; }
.flexbox>div{ flex:1; text-align: left; line-height: 30px; }
.flexbox>div:last-child{ text-align: right; }
.martop30{margin-top:30px;}

#detailKind_3 .bonceCon2 .scan000 .ttl30{
     width: 130px;
     display: inline-block;
     text-align: right;
     margin-right: 15px;
}
#detailKind_3 .bonceCon2 .scan000 {
     padding: 16px;
     background: #eff6f7;
     margin-top: 30px;
}
#rateEntryFrm{ width: 600px; }
#rateEntryFrm .rateTtl{ width: 100px; display: inline-block; text-align: right; margin-right: 10px;}
#rateEntryFrm .tip{ text-align: center; font-size: 0.8em; margin-top: -10px; }
#rateEntryFrm .cc>div{ margin-bottom: 10px; }
#rateEntryFrm .sm.form-control{ width: 190px; }
#rateEntryFrm .bg.form-control{ width: 400px; }
#rateEntryFrm .bonceCon{ background: #fff; }
#rateEntryFrm .bonceFoot{ background: #fff; }
#ratePic{padding-left:100px;}
.screenArea{display: none;}
.screenBody{ margin-left: 130px;display: inline-block;overflow: hidden;}
.screenBody span{ margin-right: 12px;display: inline-block;line-height: 32px;}
#searchOrg{padding: 0px 20px;background: #fff;border: 1px solid #ccc;border-radius: 5px;line-height: 35px;height: 35px;}
.updateFormWrap{padding-left: 50px;}
.singleRow{margin-bottom: 30px;}
.singleRow .fa{margin-right: 12px;}
.toggleCon {margin-top: 20px;}
#billNolist{width: 260px;}
.check_See{padding: 5px 0;}
.check_Seebg,.check_SeeType{position: relative;z-index: 1;}
.check_See>span,.check_Seebg>span{width: 26.5%;display: inline-block;text-align: right;}
.check_SeeType>span:nth-child(1){width: 26.5%;display: inline-block;text-align: right;}
.check_SeeType{padding: 5px 0;}
.check_SeeType>span:nth-child(2),.check_SeeType>span:nth-child(4),.check_SeeType>span:nth-child(6){margin-left: 20px;}
.check_See>input{margin-left: 8px;padding: 0 0 0 10px;width: 60%;height: 35px!important;background-color: #fff;}
.check_Seebg>input{margin-left: 8px;padding: 0 0 0 10px;width: 60%;height: 35px!important;}
.check_See>select{margin-left: 8px;padding: 0 0 0 6px;width: 60.5%;height: 35px!important;border: 1px solid #ccc;}
.check_SeeHide,.accept_hide{display: none;}  .bank_Con>span:nth-child(2){color: red;width: 10px;font-size: 16px;}
