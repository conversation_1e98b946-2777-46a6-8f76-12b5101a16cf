package cn.sphd.miners.modules.accountant.service.impl;

import cn.sphd.miners.modules.accountant.entity.CashFlow;
import cn.sphd.miners.modules.accountant.mapper.CashFlowMapper;
import cn.sphd.miners.modules.accountant.service.AccountantCashFlowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by root on 17-6-1.
 */
@Service
@Transactional(readOnly=false)
public class AccountantCashFlowServiceImpl implements AccountantCashFlowService {

    @Autowired
    CashFlowMapper cashFlowMapper;

    @Override
    public List<CashFlow> getCashDataByPeriod(CashFlow cashFlow) {
        List<CashFlow> list = cashFlowMapper.getCashDataByPeriod(cashFlow);
        return list;
    }


}
