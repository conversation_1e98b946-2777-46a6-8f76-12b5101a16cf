package cn.sphd.miners.modules.accountant.controller;

import cn.sphd.miners.common.utils.WriteJSONUtil;
import cn.sphd.miners.modules.accountant.entity.MaterialSubjectParam;
import cn.sphd.miners.modules.accountant.entity.TAccountantSettle;
import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectEntity;
import cn.sphd.miners.modules.accountant.mapper.SettleMapper;
import cn.sphd.miners.modules.accountant.service.BuildAccountService;
import cn.sphd.miners.modules.accountant.service.SubjectSettingService;
import cn.sphd.miners.modules.accountant.util.DownFindSubjects;
import cn.sphd.miners.modules.accountant.util.Tools;
import cn.sphd.miners.modules.system.entity.User;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * Created by root on 17-1-4.
 */
@Controller
@RequestMapping("/accountant")
public class SubjectSettingController {

    @Autowired
    SubjectSettingService settingService;
    @Autowired
    BuildAccountService buildAccountService;

    //获取所有科目
    @RequestMapping("/getSubject.do")
    public void getSubject(HttpServletResponse response, String category, User user){
        if(category == null || "".equals(category)) {
            category="1";
        }
        Integer oid = user.getOid();
        JSONObject result = new JSONObject();
        List idesBySubjectcategory = settingService.idesBySubjectCategory(category);
        HashMap<String, Object> param = new HashMap<String, Object>();
        param.put("list", idesBySubjectcategory);
        param.put("org", oid);
        param.put("subjectState", 0);
        List<TAccountantSubjectEntity> list = settingService.listAccountantSubject(param);
        result.put("list" , list);
        result.put("status", 1);
        WriteJSONUtil.writeJSON(result.toString(),response);
    }

    //点击新增科目
    @RequestMapping("/clickAdd.do")
    public void clickAdd(HttpServletResponse response , String subject , User user ){
        JSONObject result=new JSONObject();
        if (subject.startsWith("1002")) {
            result.put("status",2);
            WriteJSONUtil.writeJSON(result.toString(),response);
            return;
        }
        int oid = user.getOid();
        String list = settingService.firstSubject(subject, oid);
        if(list != null || !list.equals(null)){
            JSONArray arrList = new JSONArray(list);
            result.put("list", arrList);
            result.put("status", 1);
            WriteJSONUtil.writeJSON(result.toString(),response);
        }else {
            result.put("status",0);
            WriteJSONUtil.writeJSON(result.toString(),response);
        }
    }

    //确定新增科目,返回新增后的科目
    @RequestMapping("/addSubject.do")
    public void addSubject(HttpServletResponse response, User user, String newsubject, String parent, String name,
                           String measure_unit, String quantityAssistingAccounting, String relevanceType, Integer relevanceItem){
        JSONObject result = new JSONObject();
        int oid = user.getOid();
        int staffId = user.getUserID();
        String staffName = user.getUserName();

        net.sf.json.JSONObject resSubject = settingService.newAccountantSubject(parent,oid,name,measure_unit,staffId,staffName,newsubject,quantityAssistingAccounting,relevanceType,relevanceItem);
        int res = resSubject.optInt("newSubjectID");
        if (res < 0) {
            result.put("status" ,0);
            WriteJSONUtil.writeJSON(result.toString(),response);
        }else{
            TAccountantSubjectEntity list = settingService.getOneAccountSubject(newsubject,oid);
            result.put("list" ,list);
            result.put("status" ,1);
            WriteJSONUtil.writeJSON(result.toString(),response);
        }

    }

    /*
    * 同时生成供应商和物料的科目
    * supplier					供应商名称
    * material					物料名称
    * supplierSubject			供应商科目编号
    * supplierParentSubject	    供应商的父科目编号
    * relevanceType			    关联类型:1-商品，2-物料，3-供应商
    * relevanceItem			    关联项的id,根据代码去不同的表中查,商品去找商品表找id，物料去找物料表id
    * */
    @RequestMapping("/generateSubjects.do")
    public void generateSubjects(HttpServletResponse response, User user, MaterialSubjectParam param){
        JSONObject result = new JSONObject();
//        User user=(User)session.getAttribute("user");
        int res = settingService.generateSubjects(user,param);//0-失败    1-成功    2-已存在改科目
        result.put("res",res);
        WriteJSONUtil.writeJSON(result.toString(),response);
    }

    //点击修改
    @RequestMapping("/clickUpdate.do")
    public void clickUpdate(HttpServletResponse response, String subject, User user){
        JSONObject result = new JSONObject();
//        User user = (User)session.getAttribute("user");
        int oid = user.getOid();
//        Integer oid = (Integer) session.getAttribute("oid");
        //1002开头的科目在建账未完成前只能由财务关联修改，会计模块不能改
        if (subject.startsWith("1002")) {
            String buildState = buildAccountService.getBuildAccountState(oid);
            if (!"4".equals(buildState)) {
                result.put("status", 2);
                WriteJSONUtil.writeJSON(result.toString(),response);
                return;
            }
        }
        DownFindSubjects dfs=new DownFindSubjects();
        List subjectes = new ArrayList();
        try {
            dfs.downFind(subject, oid, settingService);
            subjectes = dfs.getFindSubjects();
        } catch (Exception e) {
            e.printStackTrace();
        }
        HashMap map = settingService.subjectMessage(subjectes,oid,subject);
        if (map.isEmpty()) {
            result.put("status", 0);
            WriteJSONUtil.writeJSON(result.toString(),response);
        }else{
            TAccountantSubjectEntity list = (TAccountantSubjectEntity) map.get("data");
            String parentName = (String) map.get("parentName");
            result.put("data" ,JSONObject.wrap(list));
            result.put("parentName" ,parentName);
            result.put("status", 1);
            WriteJSONUtil.writeJSON(result.toString(),response);
        }
    }

    //确认修改科目
    @RequestMapping("/updateSubject.do")
    public void updateSubject(HttpServletResponse response, User user, TAccountantSubjectEntity param){
        JSONObject result = new JSONObject();
        int oid = user.getOid();
        param.setOrg(oid);
        int res = settingService.updateSubjectMessagw(user,param);
        if(res > 0){
            TAccountantSubjectEntity list = settingService.getOneAccountSubject(param.getSubject(),oid);
            result.put("list", list);
            result.put("status", 1);
            WriteJSONUtil.writeJSON(result.toString(),response);
        }else{
            result.put("status", 0);
            WriteJSONUtil.writeJSON(result.toString(),response);
        }
    }

    //删除科目
    @RequestMapping("/deleteSubject.do")
    public void deleteSubject(HttpServletResponse response, User user, String subject){
        JSONObject result=new JSONObject();
//        User user=(User)session.getAttribute("user");
        int oid=user.getOid();
//        Integer oid = (Integer) session.getAttribute("oid");
        int res=settingService.deleteSubject(oid,subject);
        if(res>0){
            result.put("status", 1);
            WriteJSONUtil.writeJSON(result.toString(),response);
        }else{
            result.put("status", 0);
            WriteJSONUtil.writeJSON(result.toString(),response);
        }
    }

    //点击启禁用
    @RequestMapping("/changeSubjectState.do")
    public void changeSubjectState(HttpServletResponse response, User user, String state, String subject){
        JSONObject result = new JSONObject();
//        User user=(User)session.getAttribute("user");
        int oid = user.getOid();
//        Integer oid = (Integer) session.getAttribute("oid");
        //如果是改的三级科目且要改成启用的话需要考虑父科目是否禁用，如果禁用的话不能启用
        int flag = 1;
        if(subject != null && subject.length() == 10 && "1".equals(state))
        {
            //得到他的父科目检查当前状态
            flag = settingService.checkPerentState(subject,state,oid);
        }
        if(flag == 1)
        {
            DownFindSubjects dfs = new DownFindSubjects();
            List subjectes = new ArrayList();
            try {
                dfs.downFind(subject, oid, settingService);
                subjectes = dfs.getFindSubjects();
            } catch (Exception e) {
                e.printStackTrace();
            }
            String status = settingService.subjectState(subjectes, oid, state,user);
            result.put("status" , status);
        }
        else {
            result.put("status" , "0");
        }
        WriteJSONUtil.writeJSON(result.toString(),response);
    }


    //获取现在可以选择的所有科目
    @RequestMapping("/getChoseSubject.do")
    public void getChoseSubject(HttpServletResponse response, String category, User user){
        if(category==null||category==""){
            category="1";
        }
//        User user=(User)session.getAttribute("user");
        Integer oid=user.getOid();
//        Integer oid = (Integer) session.getAttribute("oid");
        JSONObject result=new JSONObject();
        List idesBySubjectcategory = settingService.idesBySubjectCategory(category);
        HashMap<String, Object> param = new HashMap<String, Object>();
        param.put("list", idesBySubjectcategory);
        param.put("org", oid);
        param.put("subjectState", 1);
        List<TAccountantSubjectEntity> list = settingService.listAccountantSubject(param);
        result.put("list" , list);
        result.put("status", 1);
        WriteJSONUtil.writeJSON(result.toString(),response);
    }

    //点击资产负债表或利润表
    @RequestMapping("/getSublectBalance.do")
    public void getSublectBalance(HttpServletResponse response, User user, String nowDate, Integer code,String state){
        JSONObject result=new JSONObject();
        if (nowDate.equals(""))
        {
            nowDate = Tools.date2Str(new Date()).substring(0, 7);
        }
        String period = nowDate;
        String beginDate = nowDate + "-01";
        int oid = user.getOid();
        HashMap map = new HashMap();
        if(code == 1){
            map = settingService.listByTBalanceSheet(oid, beginDate, period, code,state);
        }else{
            map = settingService.maoByProfitStatement(oid,beginDate, period, code,state);
        }
        List list = (List) map.get("list");
        Integer status = (Integer) map.get("status");
        if(status == 0){
            result.put("status", status);
            WriteJSONUtil.writeJSON(result.toString(),response);
        }else{
            result.put("list" , list);
            result.put("status", 1);
            WriteJSONUtil.writeJSON(result.toString(),response);
        }
    }

    //点击试算
    @ResponseBody
    @RequestMapping("tentativeConculation.do")
    public void tentativeConculation(HttpServletResponse response, User user){
        Integer status = settingService.tentativeConculationstatus(user);
        JSONObject result=new JSONObject();
        result.put("status", status);
        WriteJSONUtil.writeJSON(result.toString(),response);
    }

    //小结账、结账
    @ResponseBody
    @RequestMapping("closeAccounts.do")
    public void closeAccounts(HttpServletResponse response, User user, String state){
        Integer status = settingService.closeAccountsStatus(user, state);
        JSONObject result = new JSONObject();
        result.put("flag", status);
        result.put("status", state);
        WriteJSONUtil.writeJSON(result.toString(),response);
    }

    //反结账
    @ResponseBody
    @RequestMapping("claseAccountsBack.do")
    public void claseAccountsBack(HttpServletResponse response, User user){
        Integer status = settingService.backCloseAccountsStatus(user);
        JSONObject result = new JSONObject();
        result.put("status", 0);
        WriteJSONUtil.writeJSON(result.toString(),response);
    }

    //结转损益
    @ResponseBody
    @RequestMapping("carryOverAccount.do")
    public void carryOverAccount (User user, HttpServletResponse response) {
//        User user=(User)session.getAttribute("user");
        boolean status = settingService.carryOver(user);
        JSONObject result=new JSONObject();
        if(status){
            result.put("status", 1);
        } else {
            result.put("status", 0);
        }
        WriteJSONUtil.writeJSON(result.toString(),response);
    }

    /**
     *  再点击对公报表和总结账时调用，返回一个上月时间period。
     *  返回一个状态0代表上月什么都没做此时按钮只有试算按钮亮起，表示只可以点击试算；
     *  1代表已经进行过试算，此时试算，小结账，和总结账按钮,都亮起。
     *  2代表此时已经小结账，此时以前显示小结账的按钮亮起并显示成反结账，总结账按钮亮起。
     *  3.代表已经总结账，所有按钮都不亮。
     *  4.代表现在是当月，还不允许做这些操作，所有都不亮。（已作废并取消了nowPeriod参数）
     * @param response
     */
    @ResponseBody
    @RequestMapping("clickReportForm.do")
    public void clickReportForm(HttpServletResponse response, User user){
        int oid = user.getOid();
        TAccountantSettle settle = settingService.settingStatus(oid);
        //taxStatus=true表示可以显示报税按钮，taxStatus=false表示不能显示
        int taxStatus = settingService.isDisplayTaxBtn(oid);
        JSONObject result = new JSONObject();
        String state = settle.getState();
        String prompt = "";
        if ("2".equals(state)) {
            //如果总结账按钮可点击的话需要提示用户结的是哪个月的帐
            prompt = "本次结账所属月份" + settle.getPeriod();
        }
        result.put("status", state);
        result.put("period", settle.getPeriod());
        result.put("prompt",prompt);
        if (taxStatus > 0) {
            result.put("isDisplayTaxBtn",1);
        }
        else
            result.put("isDisplayTaxBtn",0);
        WriteJSONUtil.writeJSON(result.toString(),response);
    }

    /*
    * 返回1413和********科目的基本信息
    * 用在物料和会计关联的地方
    * */
    @ResponseBody
    @RequestMapping("getSpecifySubjectInfo.do")
    public void getSpecifySubjectInfo(HttpServletResponse response, User user){
//        User user=(User)session.getAttribute("user");
        int oid = user.getOid();
//        Integer oid = (Integer) session.getAttribute("oid");
        List<TAccountantSubjectEntity> subjectInfo = settingService.getSpecifySubjectInfo(oid);
        JSONObject result=new JSONObject();
        result.put("subjectInfo", subjectInfo);
        WriteJSONUtil.writeJSON(result.toString(),response);
    }

    /*
     * 初始化某机构会计数据的应急预案
     * 1、先删除现有可能存在的数据
     * 2、新建初始化数据
     * */
    @ResponseBody
    @RequestMapping("initialKJdata.do")
    public void initialKJdata(HttpServletResponse response, User user){
//        User user=(User)session.getAttribute("user");
        int oid = user.getOid();
        JSONObject result = new JSONObject();
        result.put("status", 1);
        buildAccountService.initialKJdata(oid,user);
        WriteJSONUtil.writeJSON(result.toString(),response);
    }

}
