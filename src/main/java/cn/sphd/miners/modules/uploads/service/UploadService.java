package cn.sphd.miners.modules.uploads.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.uploads.dto.LogDto;
import cn.sphd.miners.modules.uploads.dto.ResFileDto;
import cn.sphd.miners.modules.uploads.dto.ResPathDto;
import cn.sphd.miners.modules.uploads.entity.UploadFile;
import cn.sphd.miners.modules.uploads.entity.UploadJunkFileClean;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;

/**
 * Created by 朱思旭 on 2021/1/23.
 */
public interface UploadService {
    //1.152接口
    List<Integer> getUploadFileUids(Integer oid);//获取已上传文件用户id列表
    List<String> getUploadFileModules(Integer oid);//获取已上传文件module列表
    List<String> getUploadFileModules(AuthAcc acc);//获取已上传文件module列表
    List<Integer> getJunkFileUids(Integer oid);//获取垃圾文件文件用户id列表
    List<String> getJunkFileModules(Integer oid);//获取垃圾文件module列表
    Long uploadFileSize(Integer oid, User user, String lastName, String module);//查询已上传文件大小（字节数）
    Long uploadFileSize(Integer oid, User user, List<String> notlastName, String module);//查询已上传文件大小（字节数）
    Long uploadFileSize(AuthAcc acc, String lastName, String module);//查询已上传文件大小（字节数）
    Long uploadFileSize(AuthAcc acc, List<String> notlastName, String module);//查询已上传文件大小（字节数）
    Long junkFileSize(Integer oid, User user, String lastName, String module);//查询垃圾文件大小（单位字节数）
    Long junkFileSize(Integer oid, User user, List<String> notlastName, String module);//查询垃圾文件大小（单位字节数）
    Long junkFileSize(AuthAcc acc, String lastName, String module);//查询垃圾文件大小（单位字节数）
    Long junkFileSize(AuthAcc acc, List<String> notlastName, String module);//查询垃圾文件大小（单位字节数）
    List<UploadFile>uploadFileList(Integer oid, User user, List<String> notlastName, String module, PageInfo pageInfo);
    List<UploadFile>uploadFileList(Integer oid, User user, String lastName, String module, PageInfo pageInfo);
    List<UploadFile>uploadFileList(AuthAcc acc, List<String> notlastName, String module, PageInfo pageInfo);
    List<UploadFile>uploadFileList(AuthAcc acc, String lastName, String module, PageInfo pageInfo);
    void cleanOrgJunkFile(User user, CleanJunkFilesListener listener);//清理垃圾文件
    void cleanAccJunkFile(AuthAcc acc, CleanJunkFilesListener listener);//清理垃圾文件
    List<UploadJunkFileClean> getCleanJunkListByOid(Integer oid, PageInfo pageInfo);//获取垃圾文件清理记录
    List<UploadJunkFileClean> getCleanJunkListByAcc(AuthAcc acc, PageInfo pageInfo);//获取垃圾文件清理记录
    //获取文件
    File copyTempFile(String filename) throws IOException;//获取文件到服务器临时目录，进行后续处理
    //不同机构或者领地往机构机构复制文件
    UploadFile copyFile(String filename, User user, String module, HttpServletRequest request) throws IOException;//不同机构间，或者从领地往机构内复制文件
    //文件引用
    void addFileUsing(FileUsingCallback callback, String filename, String displayName, User user, String module);//新增引用表
    void delFileUsing(FileUsingCallback callback, String filename, User user);//删除引用表
    Boolean checkUploadFileByFilename(String filename);//查找文件是否被引用
    //领地专用
    UploadFile copyFile(String filename, AuthAcc acc, String module, HttpServletRequest request) throws IOException;//从机构往领地复制文件
    void addFileUsing(FileUsingCallback callback, String filename, String displayName, AuthAcc acc, String module);//新增引用表
    void delFileUsing(FileUsingCallback callback, String filename, AuthAcc acc);//删除引用表
    //上传
    void delUploadFile(UploadFile uploadFile, User user, AuthAcc acc, LogDto logDto);//上传界面remove删除文件
    //上传内部用
    UploadFile createOrUpdateUploadFile(ResFileDto fileDto, ResPathDto pathDto, File file, String module, String groupUuid, User user, AuthAcc acc, UploadFile uploadFile);
    void saveUploadFile(UploadFile uploadFile, User user, AuthAcc acc, LogDto logDto);
    UploadFile getUploadFile(String filename);
    //多文件、文件夹上传
    void updateCreateDateByGroupUuid(String groupUuid, Date now);
    //抓取远程文件
    UploadFile copyFileFromUrl(HttpServletRequest request, String url, User user, AuthAcc acc, String displayName, String lastName, String module);
    //定时任务
    void checkUploadTask(Integer readLimit, Integer updateLimit);
    void cleanOrgJunkFileByUser(User user, CleanJunkFilesListener listener);
    void cleanOrgJunkFileByAcc(AuthAcc acc, CleanJunkFilesListener listener);
}
