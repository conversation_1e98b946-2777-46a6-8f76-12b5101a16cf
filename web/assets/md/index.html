<header>
    <link href='github-markdown.min.css' rel='stylesheet'>
    <link href='github-gist.min.css' rel='stylesheet'>
</header>
<body>
<div id='content' class='markdown-body'>
</div>
<script src='../global/plugins/jquery.min.js'></script>
<script src='marked.min.js'></script>
<script src='highlight.min.js'></script>
<script>
var getUrlParameter = function getUrlParameter(sParam) {
    var sPageURL = window.location.search.substring(1),
        sURLVariables = sPageURL.split('&'),
        sParameterName,
        i;
    for (i = 0; i < sURLVariables.length; i++) {
        sParameterName = sURLVariables[i].split('=');
        if (sParameterName[0] === sParam) {
            return sParameterName[1] === undefined ? true : decodeURIComponent(sParameterName[1]);
        }
    }
};
var src = getUrlParameter('src');
if (!$.isEmptyObject(src)) {
    $.get(src, function (response, status, xhr) {
        $('#content').html(marked(response));
    });
}
</script>
</body>