package cn.sphd.miners.modules.commodity.dto;


import cn.sphd.miners.modules.commodity.entity.PdCommodityImport;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

public class ReqPdObject implements Serializable {
    private Integer customer;//客户id
    private String pdProductImportListJson;
    private List<RespCommodityImport> pdProductImportList;
    private Integer org;
    private String isPurchased;//1是采购过 0是未采购过
    private Integer produdctCatetory;//1为通用商品 2为专属商品
    private Integer importOption;//导入选项:1-全部,2-开增值税专用发票的,3-开普通发票的,4-不开发票的,5-通用商品,不开增值税专用发票的
    private BigDecimal taxRate;//税率
    private Integer taxInclusive;//是否含税价:1-含税,0不含税
    private Integer isSaled;//是否销售过,1销售过,0未销售过
    private Integer importSum;//导入总数
    private Integer falseImportSum;//无法导入总数
    private Integer tureImportSum;//可导入总数
    private Integer buttonState;

    public Integer getImportOption() {
        return importOption;
    }

    public void setImportOption(Integer importOption) {
        this.importOption = importOption;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public Integer getTaxInclusive() {
        return taxInclusive;
    }

    public void setTaxInclusive(Integer taxInclusive) {
        this.taxInclusive = taxInclusive;
    }

    public Integer getIsSaled() {
        return isSaled;
    }

    public void setIsSaled(Integer isSaled) {
        this.isSaled = isSaled;
    }

    public Integer getCustomer() {
        return customer;
    }

    public void setCustomer(Integer customer) {
        this.customer = customer;
    }

    public String getPdProductImportListJson() {
        return pdProductImportListJson;
    }

    public void setPdProductImportListJson(String pdProductImportListJson) {
        this.pdProductImportListJson = pdProductImportListJson;
    }

    public List<RespCommodityImport> getPdProductImportList() {
        return pdProductImportList;
    }

    public void setPdProductImportList(List<RespCommodityImport> pdProductImportList) {
        this.pdProductImportList = pdProductImportList;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public String getIsPurchased() {
        return isPurchased;
    }

    public void setIsPurchased(String isPurchased) {
        this.isPurchased = isPurchased;
    }

    public Integer getProdudctCatetory() {
        return produdctCatetory;
    }

    public void setProdudctCatetory(Integer produdctCatetory) {
        this.produdctCatetory = produdctCatetory;
    }

    public Integer getImportSum() {
        return importSum;
    }

    public void setImportSum(Integer importSum) {
        this.importSum = importSum;
    }

    public Integer getFalseImportSum() {
        return falseImportSum;
    }

    public void setFalseImportSum(Integer falseImportSum) {
        this.falseImportSum = falseImportSum;
    }

    public Integer getTureImportSum() {
        return tureImportSum;
    }

    public void setTureImportSum(Integer tureImportSum) {
        this.tureImportSum = tureImportSum;
    }

    public Integer getButtonState() {
        return buttonState;
    }

    public void setButtonState(Integer buttonState) {
        this.buttonState = buttonState;
    }
}
