<template>
  <div id="weixinBind" class="weixinBind">
    <div class="mainCon">
      <p>绑定微信后,登录Wonderss可用微信，登录免于验证</p>
      <div style="line-height: 100px;">
        <span>当前绑定的微信</span>
        <div id="bindMsg">
          {{ bindMsg_name }}
          <img style="position: relative; top:12px; " :src="bindMsg_src" alt="">
        </div>
      </div>
      <hr>
      <p style="margin-bottom: 20px;">您可进行以下操作</p>
      <p>去绑定微信 <span class="linkBtn ty-right" @click="bind">绑定</span></p>
      <p>更换所绑定的微信号 <span class="linkBtn ty-right" @click="changeBind">更换</span></p>
      <p>查看微信绑定的操作记录 <span class="linkBtn ty-right" @click="bindLog">查看</span></p>
      <p>解除所绑定的微信，且暂不再绑定微信 <span class="linkBtn ty-right" @click="unbind">解除绑定</span></p>
    </div>


    <!-- 绑定微信  -->
    <TyDialog v-if="bindWxC" :dialogTitle="bindttl" color="blue" :dialogHide="hideFun">
      <template #dialogBody>
        <div id="Qr">
          <wxlogin
              style="height: 416px;width: 100%"
              :appid="app.appId"
              scope="snsapi_login"
              theme='black'
              :redirect_uri="redirectUriStr"
              :href="href"
              :state="state"
              rel="external nofollow"
          ></wxlogin>
        </div>
        <div style="text-align: center;">
          要与哪个微信号绑定？<br/>
          请用手机打开这个微信，之后扫一扫
        </div>
      </template>
    </TyDialog>

    <!-- 解除 绑定 提示  -->
    <TyDialog v-if="unbindC" dialogTitle="！提示" color="red" :dialogHide="hideUnbindcFun">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideUnbindcFun">取消</el-button>
        <el-button class="bounce-ok" @click="unbindFn">确定</el-button>
      </template>
      <template #dialogBody>
        <div style="text-align: center; ">
          确定解除该Wonderss账号与微信地绑定吗？
        </div>
      </template>
    </TyDialog>

    <!-- 微信绑定记录  -->
    <TyDialog v-if="bindLogC" dialogTitle="微信绑定记录" color="blue" :dialogHide="hideBindLogCFun">
      <template #dialogFooter>
      </template>
      <template #dialogBody>
        <div style="text-align: center; max-height:300px; overflow-y:auto;   ">
          <table class="ty-table" id="logTab">
            <tr>
              <td>事项</td>
              <td>操作时间</td>
              <td>所绑微信的头像与昵称</td>
            </tr>
            <tr v-for="(item, index) in bindLogList" :key="index">
              <td>{{ formatType(item.operation)  }}</td>
              <td>{{ (new Date(item.createTime)).format('yyyy-MM-dd hh:mm:ss')  }}</td>
              <td>
                <span v-if="item.operation === '2'">--</span>
                <span v-else>
                  <img class="logImg" :src="item.avatar">
                  {{ item.nickName }}
                </span>
              </td>
            </tr>
          </table>

        </div>
      </template>
    </TyDialog>

    <!-- 微信绑定前手机验证码  -->
    <TyDialog v-if="bindWxPhoneCodeC" dialogTitle="Wonderss绑定微信" color="blue" :dialogHide="hideBindWxPhoneCodeCFun">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hidebindWxPhoneCodeCFun">取消</el-button>
        <el-button class="bounce-ok" @click="bindWxPhoneCodeOK">确定</el-button>
      </template>
      <template #dialogBody>
        <div style="text-align: center; margin-bottom: 20px;">
          安全起见，系统需对下方的手机号进行验证！
        </div>
        <div class="ppcc">
          <div class="pItem">
            <span class="pTtl">账号：</span>
            <span class="pCon">{{ bindWxPhoneCodeCPCon }}</span>
          </div>
          <div class="pItem">
            <span class="pTtl">验证码：</span>
            <el-input v-model="bindWxPhoneCodeCLitput" placeholder="请输入验证码" class="litPut">
              <template #append>
                <el-button type="primary" @click.stop="getWxCode">获取验证码</el-button>
              </template>
            </el-input>
          </div>
        </div>
      </template>
    </TyDialog>

  </div>
</template>
<script lang="js">

import '@/utils/GVerify.js'
import { checkBindingWX, getWxCodeFun, tpCodeBindAcc, getOrGroupByAcc, tpBindAccHistories, tpUnBindAcc } from '@/api/weChatBind.js'
import { inject } from 'vue';
import { beforeRouteLeave, initNav } from "@/utils/routeChange"
import wxlogin from 'vue-next-wxlogin'
import auth from '@/sys/auth'
import qs from 'qs';

export default {
  data() {
    return {
      pageName:'weixinBind',
      bindWxC: false,
      bindttl: '',
      bindMsg_name: '',
      bindMsg_src: '',
      bindLogC: false,
      unbindC: false,
      bindWxPhoneCodeC: false,
      bindWxPhoneCodeCType:'',
      bindWxPhoneCodeCPCon:'',
      bindWxPhoneCodeCLitput:'',
      delVisible: null ,
      redirectUriStr:'',
      state:'',
      href:'',
      app: {
        tgCode: 'weChat',
        appId: 'wx6344f9634107c46e',
      },
    }
  },
  components: {
    wxlogin
  },
  beforeRouteLeave(to, from, next) {
      beforeRouteLeave(to, from, next,this)
  },
  created(){
    initNav({mid: 'weixinBind', name: '微信绑定', pageName: this.pageName}, this.mainMounted, this)
    const globalEnum = inject("globalConstEnum")
    console.log('globalEnum=', globalEnum)
    this.passportHost = globalEnum.getDescByKey('Passport', 'host')
    this.app = globalEnum.getDescByKey('WonderssTpApps', 'WxPcLogin')
    console.log('app的数据')
    let urlParams = location.search.lastIndexOf('?') >= 0 ? location.search : location.hash
    let params = qs.parse(urlParams.substring(urlParams.lastIndexOf('?')), { ignoreQueryPrefix: true })
    console.log('qs', params, params.code, params.state, params.state == auth.getToken())
    console.log('auth.getToken()=', auth.getToken())
    if(typeof params.code !== 'undefined' && typeof params.state !== 'undefined' && params.state == auth.getToken()) {
      this.clearWxMember()
      let paramsData = {
        appId: this.app.appId,
        tgCode: this.app.tgCode,
        code: params.code
      }
      tpCodeBindAcc(paramsData).then(res => {
        let data = res.data
        console.log(data)
        if(data.success > 0) {
          console.log('绑定成功！', location.href)
          this.setWxMember(auth.getAcc().id, data.data)

        } else {
          let error = data.error
          console.log('绑定失败！error msg', error)
          // layer.msg('操作失败！\n' +
          //     '因为该微信已绑定了Wonderss账号！');
          this.$alert('操作失败！\n因为该微信已绑定了Wonderss账号！', '提示', { confirmButtonText: '确定' })
          setTimeout(function(){
            location.href = auth.webRoot + '/vue/minersFrontEnd/dist/index.html#/weixinBind'
          },3000)

        }
      }).catch(err => {
        console.log('err=', err)
      })

    }
    else {
      this.flushWxMember()
    }
  },
  destroyed () {
  },
  methods: {
    unbind(){
      let member = this.getWxMember(auth.getAcc().id)
      if(member){
        this.unbindC = true
      }else{
        this.$message.error('本账号还没有绑定微信呢!')
      }
    },
    unbindFn() {
      let params = {
        appId: this.app.appId,
        tgCode: this.app.tgCode
      }
      tpUnBindAcc(params).then(res => {
        let data = res.data
        this.unbindC = false
        if (data.success > 0) {
          this.$message.success('已成功解绑')
          this.flushWxMember()
        } else {
          let error = data.error
          console.log('操作失败！error msg', error)
          this.$message.error('操作失败')
        }
      }).catch(error => {

      })

    },
    clearWxMember(){

    },
    formatType(operation) {
      let typeStr = ''
      // operation 操作:1-绑定,2-解绑
      if(operation == 1){
        typeStr = '绑定'
      }else{
        typeStr = '解绑'
      }
      return typeStr

    },
    bindLog() {
      tpBindAccHistories().then(res => {
        let data = res.data
        let list = data.data || []
        if(list.length === 0){
          layer.msg('本账号还没有绑定微信呢!')
          return false
        }
        this.bindLogC = true
        this.bindLogList = list

      }).catch(err=>{

      })
    },
    bind() {
      // create:hxz 2022-12-05 绑定
      let member = this.getWxMember(auth.getAcc().id)
      if(member){
        this.$message.error('本账号已经绑定微信了!')
      }else{
        // 不发短信的
        // this.bindttl = 'Wonderss绑定微信'
        // this.bingWx()
        // 发送短信验证码的
        this.shoeBindWxPhoneCodeC('bind')
      }
    },
    shoeBindWxPhoneCodeC(type) {
      let user = auth.getUser()
      console.log('user', user)
      this.bindWxPhoneCodeCType = type
      this.bindWxPhoneCodeCPCon = user.mobile
      this.bindWxPhoneCodeC =  true
    },
    getWxCode() {
      let mobile = this.bindWxPhoneCodeCPCon
      let url = '/auth/sendMessageBindingWX.do'
      let typt = this.bindWxPhoneCodeCType
      if(typt === 'changeBind'){
        url = '/auth/sendMessageReplaceBindingWX.do'
      }
      let data = { 'phone': mobile }
      getWxCodeFun(url , data).then(res => {
        console.log('getWxCodeFun res=', res)
        let data = res.data.success
        // success 1 验证通过 / success 0 验证失败 抛出失败信息 “手机号长度不对”，“手机号格式不对”， “验证码未过时”。
        if(data == 1){
          this.$message.success('短信验证码已发送，请注意查收！')
        }else if(data == 0){
          this.$message.error(res.data.errorMsg)
        }
      }).catch(err => {
        console.log('err=', err)
      })

    },
    bindWxPhoneCodeOK() {
      // creater:hxz 2024-03-21 验证 验证码是否正确
      //  phone 手机号， verificationCode 验证码
      let mobile = this.bindWxPhoneCodeCPCon
      let code = this.bindWxPhoneCodeCLitput
      if(code.length !== 4){
        this.$message.error('短信验证码输入不正确！')
        return false
      }
      let data = { 'phone': mobile, 'code': code }
      let url = '/auth/checkBindingWX.do'
      let typt = this.bindWxPhoneCodeCType
      if(typt === 'changeBind'){
        url = '/auth/checkReplaceBindingWX.do'
      }
      checkBindingWX(url,data).then(res => {
        let data = res.data.success
        // data中： 1- 成功， 0-失败 ， 2 手机号不是11位 ， 3 手机号格式不对 ， 5 验证码已经发过还未过时
        if(data === 1){
          this.bindWxPhoneCodeC =  false
          this.$message.success('验证成功！')
          let typt = this.bindWxPhoneCodeCType
          if(typt === 'changeBind'){
            this.bindttl = '更换所绑定微信号-用新地微信扫码'
            this.bingWx()
          }else{
            this.bindttl = 'Wonderss绑定微信'
            this.bingWx()
          }
        }else {
          this.$message.error(res.data.errorMsg)
          // this.bingWx()
          // this.bindWxPhoneCodeC =  false
        }
      }).catch(err => {
        console.log('err=', err)
      })
    },
    bingWx() {
      this.bindWxC = true
      if (location.href.indexOf('/vue/') > 0) {
        this.redirectUriStr = auth.webRoot.substring( 0, auth.webRoot.indexOf('://') + 3) + this.passportHost + '/' + (auth.getWebRootShort() ?? auth.getDomain(location.href) + '/vue/minersFrontEnd/dist/index.html#' ) + '/weixinBind'
      } else {
        this.redirectUriStr = location.protocol + '//' + this.passportHost + '/' + auth.getDomain(location.href) + '/weixinBind'
      }
      this.state = auth.getToken()
      console.log('redirectUriStr=', this.redirectUriStr)
      console.log('this.state=', this.state)
    },
    changeBind() {
      let member = this.getWxMember(auth.getAcc().id)
      if(member){
        // 不需要发短信的
        // this.bindttl = '更换所绑定微信号-用新地微信扫码'
        // this.bingWx()

        // 需要发短信的
        this.shoeBindWxPhoneCodeC('changeBind')

      }else{
        this.$message.error('本账号还没有绑定微信呢!')
      }
    },
    hidebindWxPhoneCodeCFun(){
      this.bindWxPhoneCodeC = false
    },
    flushWxMember() {
      let accId = auth.getAcc().id
      let data = {
        appId: this.app.appId,
        tgCode: this.app.tgCode,
      }
      getOrGroupByAcc(data).then(res => {
        console.log('res=', res)
        if (res.data.success > 0) {
          this.setWxMember(accId, res.data.data)
        } else {
          this.setWxMember(accId, null)
        }
        this.getWxMember(accId)
        
      }).catch(err => {
        console.log('err=', err)
      })
    },
    setWxMember(accId, data) {
      console.log('设置微信数据=', data)
      let ddats = localStorage.getItem('wxMsg')
      ddats = (ddats && JSON.parse(ddats)) || {}
      if(data===null) {
        data = 'null'
        this.bindMsg_name = '未绑定'
        this.bindMsg_src = ''
      }else{
        this.bindMsg_name = data.nickName
        this.bindMsg_src = data.avatar
      }
      ddats[accId] = data
      localStorage.setItem('wxMsg',JSON.stringify(ddats))

    },
    getBindMsg() {
      let member = this.getWxMember(auth.getAcc().id)
      let accId = auth.getAcc().id
      if (!member) {
        console.log('accId=', accId)
        let data = {
          appId: this.app.appId,
          tgCode: this.app.tgCode,
        }
        getOrGroupByAcc(data).then(res => {
          console.log('res=', res)
          if (data.success > 0) {
            this.setWxMember(accId, data.data)
          } else {
            this.setWxMember(accId, null)
          }
          this.getWxMember(accId)
          // 设置 微信绑定信息
        }).catch(err => {
          console.log('err=', err)
        })

      } else {
        // 设置 微信绑定信息
        this.setWxMember(accId, member)
      }
    },
    getWxMember(accId) {
      //没绑定返回null，没调用过后端接口返回undefined
      let ddats = localStorage.getItem('wxMsg')
      ddats = (ddats && JSON.parse(ddats)) || {}
      if(ddats[accId] === 'null') {
        return null
      }
      return ddats[accId]
    },
    mainMounted(){

      // if(params.logout != null && auth.isLogged()) {
      //   clearStorage()
      //   auth.getGuestToken()
      // }


    },
    setQR(){
      let protocolStr = location.protocol
      let webRootSubStr = location.href.substring( location.href.indexOf('://')+2)
      console.log('webRootSubStr', webRootSubStr)
      if (location.href.indexOf('/vue/') > 0) {
        this.redirectUriStr = protocolStr + '//' + this.passportHost + webRootSubStr + '/vue/minersFrontEnd/dist/index.html#/weixinBind'
      } else {
        this.redirectUriStr = protocolStr + '//' + this.passportHost + webRootSubStr
      }
      this.state = auth.getToken()
      console.log('redirectUriStr=', this.redirectUriStr)
    },
    hideFun(){
      this.bindWxC = false
    },
    hideUnbindcFun(){
      this.unbindC = false
    },
    hideBindLogCFun(){
      this.bindLogC = false
    },
    hideBindWxPhoneCodeCFun(){
      this.bindWxPhoneCodeC = false
    },
    tipOk(){
      this.delVisible = false
    },

  }
}

</script>

<style lang="scss" scoped>
#weixinBind{
  .mainCon{ border:1px solid #cdcdcd; border-radius: 4px; width: 500px; padding: 40px; margin: 50px; }
  hr{ border-top: 1px solid #ddd; margin-top: 0; margin-bottom:30px;  }
  .linkBtn{ color: #036bc0; line-height: 30px; padding: 0 20px; cursor: pointer;}
  .linkBtn:hover{ color: #0482ea;text-decoration: underline; }
  #bindMsg{ display: inline-block; float: right; }
  #bindMsg img{  width: 40px; display: inline-block; }
  .mainCon>p{ clear: both; }
  #Qr{ text-align: center; }
  #Qr iframe{margin: 0 auto;display: block;height: 300px;}
  #Qr iframe .title{ display: none; }
  .logImg{ display: inline-block; width: 20px; }
  #bindLogC{ width: 600px; }
  .ppcc{ width: 80%; margin: 0 auto 20px; }
  .pItem{ line-height: 40px;  border-bottom: 1px solid #ddd; }
  .pTtl{ width:76px; display: inline-block  }
  .litPut{ width:266px;  }

}


</style>
