<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.resourceAuthority.mapper.ResAclLogMapper">

    <select id="getSingle" parameterType="resAclLog" resultType="resAclLog">
        select
        id, org, category, resource, operate_type AS operateType, grant_user_num AS grantUserNum,
        increased_num AS increasedNum, decreased_num AS decreasedNum,increased_users AS increasedUsers,
        decreased_users AS decreasedUsers, is_latest AS isLatest, path, creator,
        create_name AS createName, create_date AS createDate, updator, update_name AS updateName,
        update_date AS updateDate, message_id AS messageId, operation, previous_id AS previousId,
        version_no AS versionNo
        from t_resource_acl_log
        where id = #{id}
    </select>

    <select id="listPage" parameterType="hashmap" resultType="resAclLog">
        select
        id, org, category, resource, operate_type AS operateType, grant_user_num AS grantUserNum,
        increased_num AS increasedNum, decreased_num AS decreasedNum,increased_users AS increasedUsers,
        decreased_users AS decreasedUsers, is_latest AS isLatest, path, creator,
        create_name AS createName, create_date AS createDate, updator, update_name AS updateName,
        update_date AS updateDate, message_id AS messageId, operation, previous_id AS previousId,
        version_no AS versionNo
        from t_resource_acl_log
    </select>

    <select id="resAclLoglistPage" parameterType="hashmap" resultType="resAclLog">
        select
        id, org, category, resource, operate_type AS operateType, grant_user_num AS grantUserNum,
        increased_num AS increasedNum, decreased_num AS decreasedNum,increased_users AS increasedUsers,
        decreased_users AS decreasedUsers, is_latest AS isLatest, path, creator,
        create_name AS createName, create_date AS createDate, updator, update_name AS updateName,
        update_date AS updateDate, message_id AS messageId, operation, previous_id AS previousId,
        version_no AS versionNo
        from t_resource_acl_log
        where org = #{org} and create_date between #{dateBegin} and #{dateEnd}
        <if test="name != null" >
            <if test='type == "1"' >
                and id in (
                    SELECT a.id
                    FROM t_resource_acl_log a, t_resource_category r
                    WHERE r.id = a.category AND r.name like CONCAT('%',#{name},'%') AND a.org = #{org})
            </if>
            <if test='type == "2"' >
                and id in (
                    SELECT a.id
                    FROM t_resource_acl_log a, t_resource r
                    WHERE r.id = a.resource AND (r.name like CONCAT('%',#{name},'%') or r.file_sn like CONCAT('%',#{name},'%'))
                          AND a.org = #{org})
            </if>
            <if test='type == "3"' >
                and id in (
                    SELECT a.id
                    FROM t_resource_acl_log a, t_resource r
                    WHERE r.id = a.resource AND (r.name like CONCAT('%',#{name},'%') or r.file_sn like CONCAT('%',#{name},'%'))
                          AND a.org = #{org}
                    UNION ALL SELECT a.id
                    FROM t_resource_acl_log a, t_resource_category r
                    WHERE r.id = a.category AND r.name like CONCAT('%',#{name},'%') AND a.org = #{org})
            </if>
        </if>
        order by create_date desc
    </select>

    <delete id="delete" parameterType="resAclLog" >
        delete from t_resource_acl_log
        where id = #{id}
    </delete>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="resAclLog">
        insert into t_resource_acl_log (org, category, resource,
        operate_type, grant_user_num, increased_num,
        decreased_num, is_latest, path,
        creator, create_name, create_date,
        updator, update_name, update_date,
        message_id, operation, previous_id,
        version_no, increased_users, decreased_users
        ) values (#{org}, #{category}, #{resource},
        #{operateType}, #{grantUserNum}, #{increasedNum},
        #{decreasedNum}, #{isLatest}, #{path},
        #{creator}, #{createName}, #{createDate},
        #{updator}, #{updateName}, #{updateDate},
        #{messageId}, #{operation}, #{previousId},
        #{versionNo}, #{increasedUsers}, #{decreasedUsers} )
    </insert>

    <update id="update" parameterType="resAcl" >
        update t_resource_acl_log
        <set >
            <if test="org != null" >
                org = #{org},
            </if>
            <if test="category != null" >
                category = #{category},
            </if>
            <if test="resource != null" >
                resource = #{resource},
            </if>
            <if test="operateType != null" >
                operate_type = #{operateType},
            </if>
            <if test="grantUserNum != null" >
                grant_user_num = #{grantUserNum},
            </if>
            <if test="increasedNum != null" >
                increased_num = #{increasedNum},
            </if>
            <if test="decreasedNum != null" >
                decreased_num = #{decreasedNum},
            </if>
            <if test="isLatest != null" >
                is_latest = #{isLatest},
            </if>
            <if test="path != null" >
                path = #{path},
            </if>
            <if test="creator != null" >
                creator = #{creator},
            </if>
            <if test="createName != null" >
                create_name = #{createName},
            </if>
            <if test="createDate != null" >
                create_date = #{createDate},
            </if>
            <if test="updator != null" >
                updator = #{updator},
            </if>
            <if test="updateName != null" >
                update_name = #{updateName},
            </if>
            <if test="updateDate != null" >
                update_date = #{updateDate},
            </if>
            <if test="messageId != null" >
                message_id = #{messageId},
            </if>
            <if test="operation != null" >
                operation = #{operation},
            </if>
            <if test="previousId != null" >
                previous_id = #{previousId},
            </if>
            <if test="versionNo != null" >
                version_no = #{versionNo},
            </if>
            <if test="increasedUsers != null" >
                increased_users = #{increasedUsers},
            </if>
            <if test="decreasedUsers != null" >
                decreased_users = #{decreasedUsers},
            </if>
        </set>
        where id = #{id}
    </update>







</mapper>