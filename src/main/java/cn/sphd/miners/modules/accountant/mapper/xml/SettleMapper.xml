<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.accountant.mapper.SettleMapper" >

  <select id="getSingle" parameterType="settle" resultType="settle">
    select 
    id, org, period, type, begin_date, end_date AS endDate, settle_date AS settleDate, memo, creator, create_name AS createName,
    create_date AS createDate, updator, update_name AS updateName, update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus,
     approve_level AS approveLevel, auditor, auditor_name AS auditorName, audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo,
      message_id AS messageId, state,need_trial AS needTrial
    from t_accountant_settle
    where id = #{id}
  </select>

  <select id="listPage" parameterType="hashmap" resultType="settle">
    select
    id, org, period, type, begin_date, end_date AS endDate, settle_date AS settleDate, memo, creator, create_name AS createName,
    create_date AS createDate, updator, update_name AS updateName, update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus,
    approve_level AS approveLevel, auditor, auditor_name AS auditorName, audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo,
    message_id AS messageId, state,need_trial AS needTrial,tax_state AS  taxState,tax_date AS  taxDate,super_state AS  superState
    from t_accountant_settle
  </select>

  <select id="getSettleDay" resultType="settle">
        SELECT      id, org, period, type, begin_date, end_date AS endDate, settle_date AS settleDate, memo, creator, create_name AS createName,
    create_date AS createDate, updator, update_name AS updateName, update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus,
    approve_level AS approveLevel, auditor, auditor_name AS auditorName, audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo,
    message_id AS messageId, state,need_trial AS needTrial FROM `t_accountant_settle` WHERE org=#{org} and (state='2' or state='3') AND period=#{period} order by create_date desc limit 1;
  </select>

  <select id="getSettleByMonth" resultType="settle">
    SELECT
    id, org, period, type, begin_date AS beginDate, end_date AS endDate, settle_date AS settleDate,
    memo, creator, create_name AS createName, create_date AS createDate, updator,
    update_name AS updateName, update_date AS updateDate, approve_Item AS approveItem,
    approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
    audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo,
    message_id AS messageId, state,need_trial AS needTrial
    FROM
    t_accountant_settle
    WHERE
    org=#{org} AND period = #{period}
  </select>

  <select id="judgeCharge" resultType="settle">
    SELECT state,period FROM `t_accountant_settle` WHERE org=#{oid} ORDER BY create_date DESC LIMIT 1;
  </select>

  <select id="all" resultType="settle">
    select
    id, org, period, type, begin_date, end_date AS endDate, settle_date AS settleDate, memo, creator, create_name AS createName,
    create_date AS createDate, updator, update_name AS updateName, update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus,
    approve_level AS approveLevel, auditor, auditor_name AS auditorName, audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo,
    message_id AS messageId, state,need_trial AS needTrial,tax_state AS  taxState,tax_date AS  taxDate,super_state AS  superState
    from t_accountant_settle
  </select>

  <select id="getByOrg" resultType="settle">
        select
    id, org, period, begin_date, end_date AS endDate, settle_date AS settleDate, creator, create_name AS createName,
    create_date AS createDate,  state,tax_state AS  taxState,tax_date AS  taxDate,super_state AS  superState
    from t_accountant_settle WHERE org=#{org} and state='3' ORDER BY create_date DESC LIMIT 1;
  </select>

  <select id="isDisplayTaxBtn" resultType="settle">
    select id from t_accountant_settle WHERE state='3' AND tax_state='0' AND org=#{oid} ORDER BY  create_date DESC LIMIT 1;
  </select>
  <select id="checkRebuild" resultType="java.lang.Integer">
    SELECT PERIOD_DIFF(#{now},DATE_FORMAT(create_date,'%Y%m')) FROM `t_accountant_settle` WHERE org=#{org} AND tax_state='1' ORDER BY create_date DESC LIMIT 1;
  </select>

  <select id="getMessageIDs" resultType="java.lang.Integer">
    SELECT id FROM `t_sys_user_message` WHERE message_type=12 AND receive_user_id=#{receiver} AND DATE_FORMAT(create_time,'%Y-%m-%d')=#{nowstr};
  </select>
  <select id="enableAccountant" resultType="java.lang.Integer">

    SELECT org FROM t_sys_org org,t_sys_org_popedom popedom WHERE org.id=popedom.org AND org.id=#{org} AND popedom.mid='sb' AND org.super_id IS NOT NULL;
  </select>
  <select id="getAccountOrg" resultType="java.lang.Integer">
    SELECT org FROM t_sys_org org,t_sys_org_popedom popedom WHERE org.id=popedom.org AND popedom.mid='sb' ;
  </select>

  <select id="getSettleListByYear" resultType="settle">
    SELECT
      id, org, period, type, begin_date AS beginDate, end_date AS endDate, settle_date AS settleDate,
      memo, creator, create_name AS createName, create_date AS createDate, updator,
      update_name AS updateName, update_date AS updateDate, approve_Item AS approveItem,
      approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
      audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo,
      message_id AS messageId, state,need_trial AS needTrial
    FROM
      t_accountant_settle
    WHERE
      org=#{org} AND period like CONCAT(#{period},'%') and state='3'
  </select>

  <delete id="delete" parameterType="settle" >
    delete from t_accountant_settle
    where id = #{id}
  </delete>

  <delete id="delSettle">
    delete from t_accountant_settle
    where period = #{period} and org=#{org}
  </delete>

  <delete id="deleteByDate">
    DELETE FROM `t_accountant_settle` WHERE DATE_FORMAT(settletime,'%Y-%m')=DATE_FORMAT(#{createDate},'%Y-%m') AND oid=#{org}
  </delete>

  <insert id="insert" useGeneratedKeys="true" keyProperty="id">
    insert into t_accountant_settle (id, org, period,
    type, begin_date, end_date, settle_date,
    memo, creator, create_name,
    create_date, updator, update_name,
    update_date, approve_Item, approve_status,
    approve_level, auditor, auditor_name,
    audit_date, operation, apply_memo,
    approve_memo, message_id, state,need_trial)
    values (#{id}, #{org}, #{period},
    #{type}, #{beginDate}, #{endDate}, #{settleDate},
    #{memo}, #{creator}, #{createName},
    #{createDate}, #{updator}, #{updateName},
    #{updateDate}, #{approveItem}, #{approveStatus},
    #{approveLevel}, #{auditor}, #{auditorName},
    #{auditDate}, #{operation}, #{applyMemo},
    #{approveMemo}, #{messageId}, #{state},#{needTrial})
  </insert>

  <update id="update" parameterType="settle" >
    update t_accountant_settle
    <set >
      <if test="org != null" >
        org = #{org},
      </if>
      <if test="period != null" >
        period = #{period},
      </if>
      <if test="type != null" >
        type = #{type},
      </if>
      <if test="beginDate != null" >
        begin_date = #{beginDate},
      </if>
      <if test="endDate != null" >
        end_date = #{endDate},
      </if>
      <if test="settleDate != null" >
        settle_date = #{settleDate},
      </if>
      <if test="memo != null" >
        memo = #{memo},
      </if>
      <if test="creator != null" >
        creator = #{creator},
      </if>
      <if test="createName != null" >
        create_name = #{createName},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate},
      </if>
      <if test="updator != null" >
        updator = #{updator},
      </if>
      <if test="updateName != null" >
        update_name = #{updateName},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate},
      </if>
      <if test="approveItem != null" >
        approve_Item = #{approveItem},
      </if>
      <if test="approveStatus != null" >
        approve_status = #{approveStatus},
      </if>
      <if test="approveLevel != null" >
        approve_level = #{approveLevel},
      </if>
      <if test="auditor != null" >
        auditor = #{auditor},
      </if>
      <if test="auditorName != null" >
        auditor_name = #{auditorName},
      </if>
      <if test="auditDate != null" >
        audit_date = #{auditDate},
      </if>
      <if test="operation != null" >
        operation = #{operation},
      </if>
      <if test="applyMemo != null" >
        apply_memo = #{applyMemo},
      </if>
      <if test="approveMemo != null" >
        approve_memo = #{approveMemo},
      </if>
      <if test="messageId != null" >
        message_id = #{messageId},
      </if>
      <if test="state != null" >
        state = #{state},
      </if>
      <if test="needTrial != null" >
        need_trial = #{needTrial},
      </if>
    </set>
    where id = #{id}
  </update>
  <update id="updateSettleState">
      update t_accountant_settle set state = #{state} where org = #{org} and period = #{period}
    </update>
  <update id="updateNeedTrial">
    UPDATE  `t_accountant_settle` SET need_trial=#{needTrial} WHERE org=#{org} AND period=#{period};
  </update>

  <update id="updateTaxState">
    UPDATE `t_accountant_settle` SET tax_state='1',tax_date=#{taxDate} WHERE org=#{org}  AND state='3' AND tax_state='0';
  </update>
  <update id="updateRebuild">
    UPDATE `t_accountant_setting` SET value_='Y' WHERE org=#{org} AND key_='rebuild_label';
  </update>
</mapper>