package cn.sphd.miners.modules.accountantReportTax.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctReportDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctReportHistoryDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTaxPeriodHistoryDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTaxReportHistoryDao;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctReportEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctReportHistoryEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxPeriodHistoryEntity;
import cn.sphd.miners.modules.system.entity.User;
import org.hibernate.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Repository
public class AcctReportHistoryDaoImpl extends BaseDao<AcctReportHistoryEntity, Serializable> implements AcctReportHistoryDao {

    @Autowired
    AcctReportHistoryDao reportHistoryDao;
    @Autowired
    AcctTaxPeriodHistoryDao periodHistoryDao;

    /**
     * 把最后一条report数据更新成不再申报
     * */
    @Override
    public void setDisenabled(Integer id) {
        String hql = " from AcctReportHistoryEntity where report=" + id + " order by createDate desc";
        Query query = getSession().createQuery(hql);
//        query.setFirstResult(pageNum);
        query.setMaxResults(1);
        List<AcctReportHistoryEntity> list = query.list();
        AcctReportHistoryEntity reportHistoryEntity = list.get(0);
        if (reportHistoryEntity != null) {
//            reportHistoryEntity.setEnabled(0);
//            reportHistoryEntity.setEnabledTime(new Date());
//            reportHistoryDao.update(reportHistoryEntity);
            reportHistoryDao.queryHql("update AcctReportHistoryEntity set enabled=0,enabledTime=?0 where id=?1",new Object[]{new Date(),reportHistoryEntity.getId()});
        }
    }

    @Override
    public AcctReportHistoryEntity getLastByReportId(int reportId) {
        String hql = " from AcctReportHistoryEntity where report=" + reportId + " order by createDate desc";

        Query query = getSession().createQuery(hql);

//        query.setFirstResult(pageNum);
        query.setMaxResults(1);
        List<AcctReportHistoryEntity> list = query.list();
        AcctReportHistoryEntity reportHistoryEntity = list.get(0);
        return reportHistoryEntity;
    }

    @Override
    public List<AcctReportHistoryEntity> reportUpdateRecord(Integer org, int reportId) {
        String hql = " from AcctReportHistoryEntity where report=:report and org=:org";

        HashMap<String, Object> param = new HashMap<>();
        param.put("org", org);
        param.put("report", reportId);
        List<AcctReportHistoryEntity> list = reportHistoryDao.getListByHQLWithNamedParams(hql,param);
        for (AcctReportHistoryEntity reportHistoryEntity : list) {
            String periodListHql = "from AcctTaxPeriodHistoryEntity where reportHistory=" + reportHistoryEntity.getId();
            List<AcctTaxPeriodHistoryEntity> periodHistoryEntityList = periodHistoryDao.getListByHQLWithNamedParams(periodListHql,null);
            reportHistoryEntity.setPeriodHistoryList(periodHistoryEntityList);
        }
        return list;
    }

}
