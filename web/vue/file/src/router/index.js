import { createRouter, createWebHashHistory } from 'vue-router'
import HomeView from '@/views/HomeView.vue'
// import folderOrFile from '@/views/folderOrFile.vue'
const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView
    },
    {
      path: '/folderOrFile',
      name: 'folderOrFile', 
      // component: folderOrFile
      component: () => import('@/views/folderOrFile.vue')
    },
    {
      path: '/details',
      name: 'details', 
      component: () => import('@/views/fileDetails.vue')
    },
    {
      path: '/createOrder',
      name: 'createOrder',
      component: () => import('@/views/createOrder.vue')
    },
      {
          path: '/myOrders',
          name: 'myOrders',
          component: () => import('@/views/myOrders.vue')
    },
    {
      path: '/vod',
      name: 'vod',
      component: () => import('@/views/vod.vue')
    }
  ]
})

export default router
