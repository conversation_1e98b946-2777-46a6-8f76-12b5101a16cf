package cn.sphd.miners.modules.publicInformation.entity;


import cn.sphd.miners.common.persistence.BaseEntity;
import cn.sphd.miners.modules.system.dto.UserHonePageDto;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table ( name ="t_pi_acl_detail")
public class PiAclDetail extends BaseEntity {

    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;//`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    @Column(name = "org")
    private Integer org;
    @Column(name = "file")
    private Integer file;
    @Column(name = "acl_log")
    private Integer aclLog;
    @Column(name = "acl_type")
    private Byte aclType;
    @Column(name = "user")
    private Integer user;
    @Column(name = "memo")
    private String memo;
    @Column(name = "creator")
    private Integer creator;
    @Column(name = "create_name")
    private String createName;
    @Column(name = "create_date")
    private Date createDate;
    @Column(name = "updator")
    private Integer updator;
    @Column(name = "update_name")
    private String updateName;
    @Column(name = "update_date")
    private Date updateDate;
    @Column(name = "message_id")
    private Integer messageId;
    @Column(name = "operation")
    private Byte operation;
    @Column(name = "previous_id")
    private Integer previousId;
    @Column(name = "version_no")
    private Integer versionNo;


    @Transient
    private String fileName;  //文件名字
    @Transient
    private UserHonePageDto userHonePageDto;  //人的一些信息


    public Integer getId() {
      return id;
    }

    public void setId(Integer id) {
      this.id = id;
    }

    public Integer getOrg() {
      return org;
    }

    public void setOrg(Integer org) {
      this.org = org;
    }

    public Integer getFile() {
        return file;
    }

    public void setFile(Integer file) {
        this.file = file;
    }

    public Integer getAclLog() {
      return aclLog;
    }

    public void setAclLog(Integer aclLog) {
      this.aclLog = aclLog;
    }

    public Byte getAclType() {
      return aclType;
    }

    public void setAclType(Byte aclType) {
      this.aclType = aclType;
    }

    public Integer getUser() {
      return user;
    }

    public void setUser(Integer user) {
      this.user = user;
    }

    public String getMemo() {
      return memo;
    }

    public void setMemo(String memo) {
      this.memo = memo;
    }

    public Integer getCreator() {
      return creator;
    }

    public void setCreator(Integer creator) {
      this.creator = creator;
    }

    public String getCreateName() {
      return createName;
    }

    public void setCreateName(String createName) {
      this.createName = createName;
    }

    public Date getCreateDate() {
      return createDate;
    }

    public void setCreateDate(Date createDate) {
      this.createDate = createDate;
    }

    public Integer getUpdator() {
      return updator;
    }

    public void setUpdator(Integer updator) {
      this.updator = updator;
    }

    public String getUpdateName() {
      return updateName;
    }

    public void setUpdateName(String updateName) {
      this.updateName = updateName;
    }

    public Date getUpdateDate() {
      return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
      this.updateDate = updateDate;
    }

    public Integer getMessageId() {
      return messageId;
    }

    public void setMessageId(Integer messageId) {
      this.messageId = messageId;
    }

    public Byte getOperation() {
      return operation;
    }

    public void setOperation(Byte operation) {
      this.operation = operation;
    }

    public Integer getPreviousId() {
      return previousId;
    }

    public void setPreviousId(Integer previousId) {
      this.previousId = previousId;
    }

    public Integer getVersionNo() {
      return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
      this.versionNo = versionNo;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public UserHonePageDto getUserHonePageDto() {
        return userHonePageDto;
    }

    public void setUserHonePageDto(UserHonePageDto userHonePageDto) {
        this.userHonePageDto = userHonePageDto;
    }
}
