package cn.sphd.miners.modules.accountantReportTax.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.accountantReportTax.dto.AcctTriflesDto;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxReportHistoryEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTriflesEntity;
import cn.sphd.miners.modules.system.entity.User;

import java.io.Serializable;
import java.util.List;

public interface AcctTriflesDao extends IBaseDao<AcctTriflesEntity,Serializable> {
    List<AcctTriflesEntity> getTriflesList(User user);

    List<AcctTriflesDto> getByPeriod(User user, int year, int month);

    List<AcctTriflesDto> getListFromDetail(User user, int year, int month);

    AcctTriflesEntity getTriflesDetial(User user, int triflesId);
}
