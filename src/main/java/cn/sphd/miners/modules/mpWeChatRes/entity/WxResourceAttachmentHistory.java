package cn.sphd.miners.modules.mpWeChatRes.entity;

import cn.sphd.miners.modules.resourceAuthority.entity.ResEntity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2021-12-21 
 */

@Entity
@Table ( name ="t_wx_resource_attachment_history" )
public class WxResourceAttachmentHistory  implements Serializable {

	/**
	 * ID
	 */
	@Id
	@Column(name="id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * 微信应用账号ID
	 */
   	@Column(name = "app", nullable=true , unique=false )
	private Integer app;

	/**
	 * 微信应用账号ID
	 */
   	@Column(name = "appid", length=128, nullable=true , unique=false )
	private String appid;

	/**
	 * 机构ID
	 */
   	@Column(name = "org", nullable=true , unique=false )
	private Integer org;

	/**
	 * 附件ID
	 */
   	@Column(name = "attachment", nullable=true , unique=false )
	private Integer attachment;

	/**
	 * 文件ID
	 */
   	@Column(name = "file", nullable=true , unique=false )
	private Integer file;

	/**
	 * 文件历史ID
	 */
   	@Column(name = "file_history", nullable=true , unique=false )
	private Integer fileHistory;

	/**
	 * 标题
	 */
   	@Column(name = "title", length=255, nullable=true , unique=false )
	private String title;

	/**
	 * 类型:1-文字,2-图片,3-视频
	 */
   	@Column(name = "type", length=1, nullable=true , unique=false )
	private String type;

	/**
	 * 内容
	 */
   	@Column(name = "content", nullable=true , unique=false )
	private String content;

	/**
	 * 描述
	 */
   	@Column(name = "description", length=255, nullable=true , unique=false )
	private String description;

	/**
	 * wonders文件ID
	 */
   	@Column(name = "wonders_file" , nullable=true , unique=false)
	private Integer wondersFile;

	/**
	 * 存放路径
	 */
   	@Column(name = "path", length=255, nullable=true , unique=false )
	private String path;

	/**
	 * 换版次数
	 */
   	@Column(name = "change_num", nullable=true , unique=false )
	private Long changeNum;

	/**
	 * 下载次数
	 */
   	@Column(name = "download_num", nullable=true , unique=false )
	private Long downloadNum;

	/**
	 * 浏览次数
	 */
   	@Column(name = "view_num", nullable=true , unique=false )
	private Long viewNum;

	/**
	 * 修改次数
	 */
   	@Column(name = "modify_num", nullable=true , unique=false )
	private Long modifyNum;

	/**
	 * 是否垃圾
	 */
   	@Column(name = "is_trash", nullable=true , unique=false )
	private Boolean isTrash;

	/**
	 * 是否已删除
	 */
   	@Column(name = "is_deleted", nullable=true , unique=false )
	private Boolean isDeleted;

	/**
	 * 删除时间
	 */
   	@Column(name = "delete_time", nullable=true , unique=false )
	private Date deleteTime;

	/**
	 * 版本号
	 */
   	@Column(name = "version", length=50, nullable=true , unique=false )
	private String version;

	/**
	 * 关键词
	 */
   	@Column(name = "keyword", length=100, nullable=true , unique=false )
	private String keyword;

	/**
	 * 是否有效
	 */
   	@Column(name = "valid", nullable=true , unique=false )
	private Boolean valid;

	/**
	 * 是否置顶
	 */
   	@Column(name = "is_stick", nullable=true , unique=false )
	private Boolean isStick;

	/**
	 * 排序
	 */
   	@Column(name = "orders", nullable=true , unique=false )
	private Integer orders;

	/**
	 * 备注
	 */
   	@Column(name = "memo", length=255, nullable=true , unique=false )
	private String memo;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator", nullable=true , unique=false )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name", length=100, nullable=true , unique=false )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_time", nullable=true , unique=false )
	private Date createTime;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator", nullable=true , unique=false )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name", length=100, nullable=true , unique=false )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_time", nullable=true , unique=false )
	private Date updateTime;

	/**
	 * 操作:1-增,2-删,3-改
	 */
   	@Column(name = "operation", length=1, nullable=true , unique=false )
	private String operation;

	/**
	 * 修改前记录ID
	 */
   	@Column(name = "previous_id", nullable=true , unique=false )
	private Integer previousId;

	/**
	 * 版本号,每次修改+1
	 */
   	@Column(name = "version_no", nullable=true , unique=false )
	private Integer versionNo;


	@Transient
	private ResEntity resEntity;



	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getApp() {
		return app;
	}

	public void setApp(Integer app) {
		this.app = app;
	}

	public String getAppid() {
		return appid;
	}

	public void setAppid(String appid) {
		this.appid = appid;
	}

	public Integer getOrg() {
		return org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getAttachment() {
		return attachment;
	}

	public void setAttachment(Integer attachment) {
		this.attachment = attachment;
	}

	public Integer getFile() {
		return file;
	}

	public void setFile(Integer file) {
		this.file = file;
	}

	public Integer getFileHistory() {
		return fileHistory;
	}

	public void setFileHistory(Integer fileHistory) {
		this.fileHistory = fileHistory;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Integer getWondersFile() {
		return wondersFile;
	}

	public void setWondersFile(Integer wondersFile) {
		this.wondersFile = wondersFile;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}

	public Long getChangeNum() {
		return changeNum;
	}

	public void setChangeNum(Long changeNum) {
		this.changeNum = changeNum;
	}

	public Long getDownloadNum() {
		return downloadNum;
	}

	public void setDownloadNum(Long downloadNum) {
		this.downloadNum = downloadNum;
	}

	public Long getViewNum() {
		return viewNum;
	}

	public void setViewNum(Long viewNum) {
		this.viewNum = viewNum;
	}

	public Long getModifyNum() {
		return modifyNum;
	}

	public void setModifyNum(Long modifyNum) {
		this.modifyNum = modifyNum;
	}

	public Boolean getTrash() {
		return isTrash;
	}

	public void setTrash(Boolean trash) {
		isTrash = trash;
	}

	public Boolean getDeleted() {
		return isDeleted;
	}

	public void setDeleted(Boolean deleted) {
		isDeleted = deleted;
	}

	public Date getDeleteTime() {
		return deleteTime;
	}

	public void setDeleteTime(Date deleteTime) {
		this.deleteTime = deleteTime;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getKeyword() {
		return keyword;
	}

	public void setKeyword(String keyword) {
		this.keyword = keyword;
	}

	public Boolean getValid() {
		return valid;
	}

	public void setValid(Boolean valid) {
		this.valid = valid;
	}

	public Boolean getStick() {
		return isStick;
	}

	public void setStick(Boolean stick) {
		isStick = stick;
	}

	public Integer getOrders() {
		return orders;
	}

	public void setOrders(Integer orders) {
		this.orders = orders;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getCreator() {
		return creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Integer getUpdator() {
		return updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getOperation() {
		return operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public Integer getPreviousId() {
		return previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}

	public ResEntity getResEntity() {
		return resEntity;
	}

	public void setResEntity(ResEntity resEntity) {
		this.resEntity = resEntity;
	}
}
