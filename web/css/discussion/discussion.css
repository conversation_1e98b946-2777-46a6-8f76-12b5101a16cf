/*-------------张旭博 - 1.68 新增讨论部分---------------*/
.bounceMainCon{
    width: 90%;
    margin: 0 auto;
}
.atContainer {
    display:none;
    width:300px;
    position:fixed;
    max-height:150px;
    overflow-y:auto;
    background:#fff;
    z-index:99999;
    top:50%;
    left:50%;
    border:1px solid #c9ded3;
    border-radius:4px
}
.atContainer > div.atItem {
    height:30px;
    line-height:28px;
    padding:0 10px;
    cursor:default;
    overflow:hidden
}
.atContainer > div.atItem:hover {
    background:#c9ded3
}
.atContainer > div.atItem > span:nth-child(2) {
    color:#48cfad
}
.atPerson {
    padding:0 5px;
    border-radius:4px;
    color:#318d76;
    background:#c9ded3
}
.uploadify-button,.uploadDeleteBtn {
    display: inline-block;
    border: none;
    background-color: #e7e7e7;
    height: 24px;
    border-radius: 3px;
    padding: 0 12px;
    font-size: 12px;
    font-weight: 400;
    color: #535353;
    cursor: pointer;
    text-decoration: none;
    flex-shrink: 0;
    line-height: 24px;
    margin: 4px;
}
.uploadify-button:hover{
    background-color: #5d9cec;
    color: #fff;
}
.progressnum, .uploadify_bottom{
    display: none;
}
.uploadify-progress{
    width: 150px;
    height: 6px;
    background-color: #ddd;
    margin: 3px auto;
    border-radius: 2px;
}
.file_item{
    width: 80px;
    height: 80px;
    position: relative;
}
.file_item .ty-fileType{
    width: 36px;
    height: 40px;
    margin: auto;
    background-size:36px;
    background-repeat: no-repeat;
}
.file_item  .file_name {
    font-size: 12px;
    display:inline-block;
    width:100%;
    overflow:hidden;
    height: 50px;
    margin-top: 4px;
    text-align: center;
}
.left_choose_file{
    text-align: left;
    width: 100px;
    position: relative;
}
.left_choose_file .uploadify-queue{
    position: absolute;
    left: 100px;
    background: #fff;
    z-index: 50;
}
.left_choose_file .up_filename{
    width: 150px;
}
.left_choose_file .uploadify-progress{
    width: 150px;
}
.right_show_file{
    width: 292px;
    flex: none;
}
.fileShowList{
    display: flex;
}
.field{
    display: flex;
    flex-direction: row;
    margin-top: 6px;
}
.field label{
    width: 80px;
    flex-grow: 0;
    min-height: 5px;
    flex-shrink: 0;
    font-weight: bold;
}
#seeDetail .see_processList{
    margin-left: 120px;
    font-size: 13px;
    margin-bottom: 8px;
}
#seeDetail .field label{
    width: 120px;
}
#seeDetail .see_fileList{
    flex: auto;
    background: #fff;
    padding: 8px;
}
#importFile .field{
    margin-top: 16px;
}
#importFile .field .text-right{
    flex: auto;
}
.item_input{
    width: 440px;
    height: 36px;
    line-height: 36px;
}
.textMax{
    font-size: 12px;
    color: #aaa;
}
.file_box{
    border: 1px solid #dcdfe6;
    overflow-y: auto;
    width: 420px;
    background-color: #fff;
    padding: 8px;
    display: flex;
}
.input_choose{
    display: flex;
    position: relative;
    border: 1px solid #dcdfe6;
    min-height: 32px;
    width: 420px;
    background-color: #fff;
    padding: 4px 8px;
    color: #666;
}
.input_choose input.search{
    border: none;
    width: 20px;
}
.search{
    min-width: 80px;
}
.selected_item{
    display: inline-block;
    height: 26px;
    line-height: 26px;
    padding: 0 12px;
    background-color: #eef1f2;
    border-radius: 13px;
    font-size: 14px;
}
.input_choose_list {
    width: 420px;
    display: flex;
    flex-direction: column;
    background: #fff;
    max-height: 192px;
    overflow: auto;
    box-shadow: 0 2px 4px #716d6d;
}
.input_choose_list.dir{
    display: flex;
    max-height: 300px
}
.input_choose_item{
    display: flex;
    padding: 4px 10px;
    cursor: default;
}
.input_choose_item .icon_avatar{
    width: 32px;
    height: 32px;
    margin-right: 8px;
    margin-top: 2px;
    border-radius: 16px;
    background-color: #dcdfe6;
}
.input_choose_item_info{
    display: flex;
    flex-direction:column;
}
.input_choose_item_post{
    color: #aaa;
    font-size: 12px;
}
.input_choose_item.selected{
    opacity:0.4;
    filter:alpha(opacity=40);
}
.input_choose_item:not(.selected):hover{
    background-color: #e6f0f7;
}
.delRole, .delFile{
    transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    display: inline-block;
    font-size: 18px;
    width: 18px;
    height: 18px;
    text-align: center;
    vertical-align: middle;
    line-height: 18px;
    cursor: pointer;
}
.delFile{
    position: absolute;
    right: 5px;
    top: -5px;
}

.icon{
    display: inline-block;
    font-size: 36px;
    margin-top: 18px;
    max-width: 100%;
    width: 120px;
    height: 120px;
    vertical-align: middle;
    fill: currentColor;
    overflow: hidden;
    -webkit-background-size: 120px 120px;
    background-size: 120px auto;
    background-repeat: no-repeat;
}

.icon_discuss{
    background-image: url("head_img/discuss.svg");
}
.app_null{
    display: flex;
    width: 100%;
    flex-direction: column;
    align-items: center;
    padding-top: 200px;
    background: #fafafa;
}

/*------------------讨论区App Css-----------------*/
.app_avatar{
    display: flex;
    justify-content: space-between;
    color: #606060;
    font-family: 'Microsoft YaHei';
    box-shadow: 0 0 6px #ccc;
    min-height: 500px;
    height: calc(100vh - 120px);
    min-width: 950px;
}
@media screen and (max-width: 1367px) {
    .app_avatar{
        margin-top: -10px;
    }
}
.clearInput{
    position: absolute;
    color: rgba(0, 0, 0, 0.2);
    font-size: 16px;
    display: none;
    right: 8px;
    top: 8px;
}
.clearInput:hover{
    color: rgba(0, 0, 0, 0.5);
}

/*----- 左侧主题曲 -----*/
.app_avatar .themeList{
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    background-color: #fefefe;
    border-right: 1px solid #e3e3e3;
    min-width: 100px;
}
@media screen and (min-width: 1367px) {
    .app_avatar .themeList{
        width: 350px;
        min-width: 100px;
    }
}
@media screen and (max-width: 1367px) {
    .app_avatar .themeList{
        width: 250px;
        min-width: 100px;
    }
}
.themeList .btnBar{
    height: 49px;
    line-height: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    flex: none;
    border-bottom: 1px solid #e3e3e3;
}
.themeList .searchBar{
    height: 48px;
    line-height:1;
    padding:0 16px;
    flex: none;
    position: relative;
}
.themeList .searchInput{
    position: relative;
    margin-top: 8px;
}
input:focus + i.clearInput{
    display: block;
}
.themeList .ty-search{
    position: relative;
    color: #96a2b0;
    border: 1px solid #f0f0f0;
    display: flex;
    line-height: 32px;
    padding-left: 32px;
    width: 100%;
}
.themeList .ty-search:focus{
    border-color: #5badff;
}

.themeList i.fa-search{
    position: absolute;
    left: 0;
    top: 0;
    padding: 0 8px;
    color: #999;
    font-size: 16px;
    cursor: pointer;
    line-height: 32px;
    z-index: 1;
}

.themeList .list{
    flex: auto;
    overflow-y: auto;
}
.themeList_avatar{
    flex: auto;
    overflow-y: auto;
}
.themeItem{
    display: flex;
    padding: 12px 16px;
    color: #46494d;
}
.themeItem:hover {
    background-color: #f6f6f6;
}
.themeItem .item_title {
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    position: relative;
}
.themeItem .item_title .title {
    font-size: 14px;
    line-height:1.2;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 0;
}
.themeItem .item_title .lastReplyTime{
    font-size: 12px;
    color: #a8b3bd;
}
.themeItem .participantsNum {
    position: relative;
    padding:0 4px;
    background-color: #5badff;
    color: #fff;
    font-size:12px;
    border-radius: 1px;
    margin-right: 6px;
    flex-shrink: 0
}
.themeItem .participantsNum .dotted{
    display: block;
    position: absolute;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #ed5565;
    top: -4px;
    left: -4px;
}
.themeItem .item_des{
    font-size: 12px;
    color: #a8b3bd;
    line-height:16px;
    display: flex;
}
.themeItem .item_des .atTag, .themeItem .item_des .replyTag, .themeItem .item_des .corner {
    flex-grow: 0;
    flex-shrink: 0;
}
.themeItem .item_des .lastReply {
    width: 0;
    flex: 1;
    overflow: hidden;/*超出部分隐藏*/
    text-overflow:ellipsis;/* 超出部分显示省略号 */
    white-space: nowrap;/*规定段落中的文本不进行换行 */
    margin: 0 2px;
}
.search_choose_list {
    width: 317px;
    display: flex;
    flex-direction: column;
    min-height: 260px;
    /*height: 60px;*/
    position: absolute;
    top: 42px;
    left: 16px;
    z-index: 20;
    box-shadow: 0 0 6px #a8a8a8;
    max-height: 600px;
    background-color: #fff;
}
.search_choose_list_head{
    line-height: 32px;
    font-size: 13px;
    color: #aaa;
    padding: 0 12px;
    border-bottom: 1px solid #eee;
}
.search_choose_list_head span{
    float: right;
    color: #5d9cec;
}
.search_choose_list_head span i{
    -webkit-animation: spin 1s linear 1s 5 alternate;
    animation: spin 1s linear infinite;
}
.search_choose_list_avatar{
    overflow-y: auto;
}
.corner{
    background-color: #ed5565;
    display: inline-block;
    padding: 0 4px;
    border-radius: 9px;
    color: #fff;
    font-size: 12px;
    line-height: 14px;
    min-width: 16px;
    height: 16px;
    text-align: center;
}
.themeItem.active{
    background-color: #f0f0f0;
}

/*----- 中间回复区 -----*/
.app_avatar .mainChat{
    position: relative;
    width: 100%;
    min-width: 400px; /*解决flex和white-space冲突*/
    background-color: #f6f6f6;
    display: flex;
    flex-direction: column;
}
.mainChat .chat_avatar{
    overflow-y: auto;
    padding: 12px 12px 0;
    flex:auto;
}
.mainChat .chatBar{
    border-bottom: 1px solid #e3e3e3;
    flex: none;
}
.mainChat .chatBar .chat_title{
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 32px;
    line-height: 32px;
    margin-top: 16px;
    padding: 0 16px;
}
.chatBar .chat_title .title{
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 0 4px;
}
.mainChat .unReadMsg{
    position: absolute;
    top: 60px;
    right: 16px;
    height: 32px;
    padding: 0 18px;
    background: #fff;
    border-radius: 24px 0 0 24px;
    border: 1px solid #ddd;
    line-height: 30px;
    font-size: 12px;
    z-index: 99;
    cursor: pointer;
    color: #4bca31;
    display: none;
}

.avatar{
    width: 36px;
    height: 36px;
    cursor: pointer;
    border-radius: 50%;
    flex-shrink: 0;
    background: #eee;
    border: 1px solid #fff;
}
.avatar.small{
    width: 24px;
    height: 24px;
}
.msgItem{
    padding:0 8px;
    display: flex;
    flex-direction: row;
    /*justify-content: flex-end;*/
    margin-bottom: 8px;
}
.msgItem.selfMsg{
    flex-direction: row-reverse;
}
.msgItem.reviseMsg{
    flex-direction: row;
}
.msgItem.system{
    justify-content: center;
}
.msgItem .msgItemCon{
    margin: 0 8px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}
.msgItem.system .msgItemCon{
    align-items: center;
}
.msgItem.selfMsg .msgItemCon{
    align-items: flex-end;
}
.msgItem.reviseMsg .msgItemCon{
    align-items: flex-start;
}
.msgItemCon .applyTime{
    color: #999;
    font-size: 12px;
    margin-right: 8px;
}
.msgItemCon .userName{
    color: #666;
    font-size: 12px;
}
.msgItem .userInfo{
    display: flex;
    flex-direction: row;
    align-items: flex-end;
}
.msgItem.selfMsg .userInfo{
    flex-direction: row-reverse;
}
.msgItem.reviseMsg .userInfo{
    flex-direction: row;
}
.msgItem .userInfo .applyTime{
    margin: 0 8px;
}
.msgItem.selfMsg .msgReply{
    color: #d3e6ff;
}
.msgItem.reviseMsg .msgReply{
    color: inherit;
}
.msgItem p,.replyItem p{
    margin: 0;
}

.reply, .recall{
    font-size: 12px;
    color: #999;
    cursor: pointer;
    padding:4px 8px;
}
.reply:hover, .recall:hover{
    color: #5d9cec;
}
.msgReply{
    color: #345784;
    padding-bottom: 4px;
    margin-bottom: 4px;
    border-bottom: 1px solid rgba(0,0,0,.1);
    font-size: 12px;
}
.msgReply .replyName{
    display: inline-block;
    margin-left: 3px;
    border-radius: 2px;
    color: #234b8a;
    background-color: rgba(255, 255, 255, .8);
    padding: 0 2px;
    line-height:1.2;
}
.seeOrigin{
    color: #234b8a;
    text-decoration: underline;
    cursor: pointer;
}
.bubble_avatar{
    background: #fff;
    color: #333;
    border-radius: 0 4px 4px 4px;
    padding: 8px 12px;
    margin-top: 4px;
    position: relative;
    box-shadow: 0 0 6px #eee;
}
.msgItem:not(.system) .bubble_avatar:before{
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border: 4px solid transparent;
    left: -4px;
    top: 0;
    border-top: 6px solid #fff;
}
.msgItem.selfMsg .bubble_avatar{
    border-radius: 4px 0 4px 4px;
    box-shadow: 0 0 6px #deebff;
}
.msgItem.reviseMsg .bubble_avatar{
    border-radius: 0 4px 4px 4px;
    box-shadow: 0 0 6px #eee;
    width: 350px; /*特殊处理*/
}
.msgItem.selfMsg .bubble_avatar:before{
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border: 4px solid transparent;
    right: -4px;
    top: 0;
    border-top: 6px solid #5d9cec;
    left: auto;
}
.msgItem.reviseMsg .bubble_avatar:before{
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border: 4px solid transparent;
    left: -4px;
    top: 0;
    border-top: 6px solid #fff;
}
.msgItem.selfMsg .bubble_avatar{
    background: #5d9cec;
    color: #fff;
    position: relative;
}
.msgItem.reviseMsg .bubble_avatar{
    background-color: #fff;
    color: #666;
    position: relative;
    word-break: break-all;
}
.msgItem.choosen .bubble_avatar {
    background: #f7dfc2;
    color: #fff;
}
.msgItem.choosen .bubble_avatar::before{
    border-top-color: #f7dfc2;
}
.msgItem.system .bubble_avatar {
    font-size: 12px;
    padding: 4px 12px;
    border-radius: 10px;
    background-color: #eaeaea;
    color: #666;
    min-height: 12px;
    line-height: 12px;
}
.fileItem{
    display: block;
    background: #f6f6f6;
    padding: 0;
}
.fileItem .fileInfo_avatar{
    display: flex;
    border-bottom: 1px solid #eee;
    padding: 8px 12px 4px
}
.fileInfo_avatar .ty-fileType{
    margin-right: 8px;
}
.fileItem .fileHandle{
    padding: 4px 12px 8px;
    position: relative;
}
.fileItem .fileHandle > a{
    margin-left: 4px;
}
.fileItem:hover{
    background: #f6f6f6;
}
.fileItem .fileInfo {
    width: 250px;
    flex: none;
}
.bubble_fileList .fileItem{
    margin-top: 4px;
}


.mainChat .seeMore{
    color: #5d9cec;
}
.mainChat .messageInput{
    display: flex;
    position: relative;
    justify-content: space-between;
    flex: none;
    min-height: 183px;
}
.mainChat .textInput {
    width: 100%;
}
.mainChat .history{
    position: absolute;
    right: 4px;
    bottom: 124px;
    color: #999;
    cursor: pointer;
    border-radius: 2px;
    font-size: 12px;
    height: 24px;
    line-height: 24px;
    padding: 0 8px;
    z-index: 998;
}
.mainChat .history i{
    margin-right: 4px;
    font-size: 14px;
}
.mainChat .history:active{
    background-color: #e5e5e5;
}
.mainChat .messageInput textarea{
    width: 100%;
    border: none;
    height: 120px;
    padding: 20px;
}


/*----- 右侧记录区 -----*/
.app_avatar .history_avatar{
    flex: none;
    border-left: 1px solid #e3e3e3;
}
@media screen and (min-width: 1367px) {
    .app_avatar .history_avatar{
        width: 350px;
    }
}
@media screen and (max-width: 1367px) {
    .app_avatar .history_avatar{
        width: 300px;
    }
}
.historyTab{
    display: flex;
    width: 100%;
    margin-top: 16px;
    padding: 0 8px;
}
.historyTab li{
    display: block;
    flex: 1;
    text-align: center;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    height: 32px;
    line-height: 32px;
    font-size: 12px;
}
.historyTab li.active{
    border-bottom: 2px solid #5d9cec;
    color: #5d9cec;
}
.history_avatar .functionBar{
    display: flex;
    align-items: center;
    height: 32px;
    line-height: 32px;
    margin-top: 17px;
    padding: 0 16px;
    border-bottom: 1px solid #e3e3e3;
}
.functionBar button{
    margin-right: 4px;
}
.history_avatar .tabCon{
    display: flex;
    flex-direction: column;
    height: calc(100% - 100px);
}
.history_avatar .tabCon .tblContainer{
    padding: 8px;
    overflow: auto;
    flex:auto;
}

.roleItem{
    display: flex;
    font-size: 12px;
    align-items: center;
}
@media screen and (min-width: 1367px) {
    .roleItem{
        padding:8px 16px;
    }
}
@media screen and (max-width: 1367px) {
    .roleItem{
        padding:4px;
    }
}
.roleItem:hover{
    background-color: #f3f3f3;
}
.roleItem:hover .delUser{
    display: inline;
}
.roleItem .name{
    width: 72px;
    margin-left: 8px;
}
.roleItem .manager_s{
    display: inline-block;
    width: 18px;
    height: 18px;
    background: url("head_img/manager.svg") no-repeat;
    background-size: 18px;
    vertical-align: bottom;
}
.roleItem .post{
    margin-left: 12px;
    width: 80px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.roleItem i{
    font-size: 14px;
    color: #5d9cec;
    margin-left: 4px;
    cursor: pointer;
}
.roleItem i.delUser{
    color: #ed5565;
    display: none;
}
.continueAdd{
    padding:4px 0;
    text-align: center;
}
.continueAdd,.footBar{
    flex: none;
}
.little_tip{
    font-size: 12px;
    color: #999;
    margin-right: 5px;
    margin-top: 3px;
}

.see_roleList_item{
    display: inline-block;
    background-color: #fff;
    padding: 4px 8px;
    border-radius: 3px;
    margin: 2px;
}
.see_roleList_item i.delUser {
    color: #ed5565;
    margin-left: 4px;
}


.sendBar{
    text-align: right;
    padding:4px;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
}
.replyAt{
    display: none;
    background-color: #ffeece;
    padding: 4px 12px;
}
.replyAt_con{
    color: #604e2d;
}
.replyAt_con .replyName {
    display: inline-block;
    padding:0 4px;
    border-radius: 5px;
    color: #234b8a;
    background: #fff;
    margin-left: 5px;
}
.atPerson{
    display: inline-block;
    padding:0 4px;
    border-radius: 5px;
    color: #234b8a;
    background: #fff;
    margin-left: 5px;
}
.w-e-text img{
    max-width: 200px;
    max-height: 100px;
}
#discuss_apply{
    flex-shrink: 0;
}
#discuss_apply{
    background: linear-gradient(-45deg, #57ce2c, #21bd41);
}
.imgUpload{
    display: flex;
}
.imgUpload .uploadify-button{
    display: none;
}
.imgUpload .up_filename{
    width: 90px;
}
.imgUpload .uploadify-progress{
    width: 90px;
}
.replyItem{
    font-size: 12px;
    margin-bottom: 8px;
}
.replyItem img{
    border-radius: 5px;
    border: 3px solid #eee
}
.replyItem .fileItem{
    background-color: #eee;
}
.replyItem .fileItem:hover{
    background-color: #eee;
}
.queryCondition{
    display: flex;
    font-size: 12px;
    display: none;
}
.queryCondition input,.queryCondition select{
    width: 50px;
}
.pageBtnGroup i{
    font-size: 20px;
}
.pageBtnGroup i.disabled{
    color: #ddd;
}
.dateChoose{
    display: flex;
    justify-content: space-around;
    align-items: center;
}
#calendar{
    border:1px solid #e6e6e6;
    padding:2px 6px;
}
.seeMore{
    cursor: pointer;
}
.at{
    background-color: #ffb35e;
}
#editor_con .atName{
    display: inline-block;
    padding:0 2px;
    border-radius: 2px;
    background-color: #eee;
}
.null{
    text-align: center;
    color: #999;
    margin-top: 100px;
    font-size: 12px;
}
.searchNull {
    text-align: center;
    color: #999;
    margin-top: 40px;
    font-size: 12px;
}
.file_replace{
    color: #234b8a;
    text-decoration: underline;
    margin: 0 4px;
}
.ty-alert-small{
    padding: 3px 8px;
    font-size: 12px;
    box-sizing: border-box;
    border-radius: 2px;
    position: relative;
    background-color: #f0f8ff;
    overflow: hidden;
    opacity: 1;
    align-items: center;
    transition: opacity .2s;
    margin-bottom: 8px;
}
.queryAlert{
    display: none;
}
.queryBack {
    padding: 1px 8px;
    cursor: pointer;
    background-color: #5d9cec;
    color: #fff;
    float: right;
    border: none;
}

.edui-default .edui-editor-wordcount{
    padding:0 4px;
}
.replyTag, .atTag{
    margin-right: 4px;
}
@-webkit-keyframes spin {
    from {
        -webkit-transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(360deg);
    }
}
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
.refresh {
    z-index: 100;
    color: #5badff;
    text-align: center;
    padding: 4px;
    cursor: pointer;
}
/*.refresh i{*/
    /*-webkit-animation: spin 1s linear 1s 5 alternate;*/
    /*animation: spin 1s linear infinite;*/
/*}*/
.kj-btn{
    padding: 0 18px;
    cursor: pointer;
    background-color: #fff;
    color: #101010;
    height: 32px;
    line-height: 30px;
    display: inline-block;
    border: none;
}
.kj-btn.green {
    background:linear-gradient(-45deg, #57ce2c, #21bd41);
    color: #fff;
}
.kj-btn.blue {
    background:linear-gradient(-45deg, #5d9cec, #5f9ff0);
    color: #fff;
}
.textInput_disabled{
    width: 100%;
    position: relative;
    border-top: 1px solid #e3e3e3;
    background: #ececec;
    cursor: not-allowed;
}
.textInput_disabled .history{
    top: 3px
}
.textInput_disabled .toolbarbox{
    height: 30px;
    line-height: 30px;
    position: relative;
}
.textInput_disabled .inputBox{
    padding: 12px;
    color: #666;
}
.textInput_disabled .inputBox p{
    margin-top: 8px;
}
.fileChoose_avatar{
    border: 1px solid #eee;
    padding: 0 32px;
    background-color: #fff;
    margin-top: 8px;
}
.fileChoose_avatar .queryTitle{
    display: flex;
}
.kj-table{
    width:100%;
    table-layout: auto;
    border-collapse: separate;
    border-left:1px solid #ebeef5;
    border-top:1px solid #ebeef5;
}
.kj-table thead th, .kj-table tbody td{
    padding: 10px;
    min-width: 0;
    box-sizing: border-box;
    text-overflow: ellipsis;
    vertical-align: middle;
    position: relative;
    border-bottom: 1px solid #ebeef5;
    border-right: 1px solid #ebeef5;
    line-height: 24px;
}
.kj-table tbody{
    font-size: 14px;
    color: #606266;
    background: #f6f6f6;
}
.kj-table tbody td a{
    cursor: pointer;
}
.fileChoose_avatar .fileItem{
    background-color: #fff;
}
.fileRow,.query_fileChoose_avatar{
    display: flex;
    padding: 8px 0;
}
.fileShow_avatar{
    color: #606266;
    background: #f6f6f6;
    max-height: 400px;
    overflow-y: auto;
}
.fileShow_avatar .fileRow:nth-child(even){
    background: #f0f2f4;
}
.fileShow_avatar .fileRow.active{
    background-color: #d0deee;
}
.fileRow_radio{
    flex: auto;
    text-align: center;
    padding: 30px 0;
}
.fileShow_avatar .msgItem{
    margin-bottom: 0;
}

.ty-search{
    border:1px solid #ccc;
    overflow: hidden;
    vertical-align: top;
}
.ty-search input{
    border: none;
    height: 30px;
    line-height: 30px;
    padding: 0 8px;
    float: left;
    outline: 0;
    width: 200px;
}
.ty-search i {
    line-height: 30px;
    color: #aaa;
    font-size: 14px;
}
/*.themeList{*/
    /*width: 300px;*/
    /*border:1px solid #e3e3e3;*/
    /*min-height: 600px;*/
/*}*/
#chooseSaveFolder .mar{
    width: 730px;
}
#chooseSaveFolder .ty-fileItem > .ty-fileInfo > .ty-fileRow > .ty-fileName {
    width: 350px;
}
#chooseSaveFolder .ty-fileItem > .ty-fileInfo > .ty-fileRow > .ty-fileNo{
    width: 210px;
}

.item-row, .item-column{
    display: flex;
    flex-direction: row;
    margin-bottom: 8px;
    padding: 0 8px;
}
.item-column{
    flex-direction: column;
}
.item-title{
    width: 80px;
    line-height: 30px;
}
.item-title-long{
    width: 280px;
}
.item-content{
    flex-grow: 1;
    position: relative;
}
.item-content .clearInput{
    position: absolute;
    top: 1px;
    right: 0;
    color: #ccc;
    line-height: 30px;
    font-size: 16px;
}
#newDiscussion .uploadify-button{
    margin: 4px;
}
.fileChooseBtn_sys{
    text-align: left;
    position: relative;
}

.fileChooseBtn_sys ul.chooseDialog{
    display: none;
    position: absolute;
    left: 100px;
    top: -22px;
    width: 120px;
    background-color: #fff;
    box-shadow: 0 0 2px #aaa;
    transition: 0.5s;
    font-size: 12px;
    z-index: 10;
}
.fileChooseBtn_sys ul.chooseDialog:before {
    content: '';
    position: absolute;
    display: block;
    border: 6px solid transparent;
    border-bottom-color: transparent;
    border-right-color: #dedede;
    bottom: 6px;
    left: -12px;
}
.fileChooseBtn_sys ul.chooseDialog:after {
    content: '';
    position: absolute;
    display: block;
    border: 6px solid transparent;
    border-bottom-color: transparent;
    border-right-color: #eeeeee;
    bottom: 6px;
    left: -11px;
}

.fileChooseBtn_sys ul.chooseDialog li{
    display: block;
    line-height:24px;
    text-align: center;
    color: #535353;
}
.fileChooseBtn_sys ul.chooseDialog li:hover{
    background-color: #5d9cec;
    color: #fff;
    cursor: default;
}
.fileChooseBtn_sys ul.chooseDialog li.disabled{
    background-color: #eee;
    color: #999;
}
.fileChooseBtn_sys:hover ul.chooseDialog{
    display: block;
}
#fileUpload input.ty-inputText:disabled{
    background-color: #dff0ff;
}
.useBtn{
    width: 100px;
    text-align: center;
    line-height: 28px;
    cursor: pointer;
}

#picShow{  position: fixed;  display: inline-flex;  justify-content: center; background: rgba(7, 7, 7, 0.64);  z-index: 100009;  width: 100%;
    height: 100%;  text-align: center;  vertical-align: middle;  top: 0;  left: 0;  align-items: center;  }
#picShow img{  margin-top:30px;  }
.sendBtn{
    flex:none;
}
#addRow .roleList{
    display: flex;
    flex-direction: column;
}
#addRow .roleList .ty-checkbox{
    margin-top: 4px ;
}
body::-webkit-scrollbar{
    width:0;
}
.w50{
    display: inline-block;
    max-width: 50px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: bottom;
    text-align: center;
}
#ye_archiveFile .yeCon{
    margin-top: 0;
}
.themeItem .themeItem_avatar{
    flex: auto;
}
.themeItem .ty-checkbox{
    margin-top: 6px;
}
.themeItem i.delTheme{
    line-height: 41px;
    margin-left: 12px;
    font-size: 18px;
    color: #ccc;
}
.chox{
    display: flex;
    background-color: #fff;
    border: 1px solid #eee;
    width: 702px;
    margin: 0 auto;
}
.chox_tip{
    padding: 8px 16px;
}
.chox_choose{
    width: 350px;
    flex: none;
    min-height: 400px;
}
.chox_show{
    flex: auto;
    border-left: 1px solid #eee;
    background: #fafafa;
}
.chox_show_avatar{
    height: calc(100% - 82px);
}
.chox_input{
    padding: 8px 16px;
}
.chox_input input{
    width: 100%;
}
.chox_show_avatar .themeList_avatar{
    background: #f2f1f1;
}
.fileUpload_progress{
    height: 2px;
}
.cvState{
    position: absolute;
    top: 6px;
    left: 16px;
    color: #21bd41;
}
.cvState i{
    color: #21bd41;
    font-size: 16px;
    vertical-align: bottom;
}
.ty-secondTab{
    line-height: 36px;
    height: 36px;
}
.ty-secondTab li {
    height: 36px;
}
.selectItem{
    display: flex;
    flex-direction: row;
    margin-top: 8px;
}
.selectItem .avatar{
    width: 32px;
    height: 32px;
    cursor: pointer;
    border-radius: 2px;
    flex-shrink: 0;
    background: #ecb984;
    border: none;
}
.selectItem .ty-checkbox label{
    margin-top: 6px;
}
.right_info{
    margin-left: 8px;
}
.right_info_name{
    line-height: 16px;
}
.right_info_des{
    display: flex;
    font-size: 13px;
    color: #555;
}
.right_info_des .right_info_depart{
    width: 130px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-left: 4px;
}