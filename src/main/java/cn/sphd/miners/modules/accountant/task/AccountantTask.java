package cn.sphd.miners.modules.accountant.task;

import cn.sphd.miners.common.utils.GetLocalIPUtils;
import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.accountant.entity.TAccountantSettle;
import cn.sphd.miners.modules.accountant.mapper.BuildAccountMapper;
import cn.sphd.miners.modules.accountant.mapper.SettleMapper;
import cn.sphd.miners.modules.accountant.service.AccountantSettleService;
import cn.sphd.miners.modules.message.dao.MessageDao;
import cn.sphd.miners.modules.personal.entity.UserMessage;
import cn.sphd.miners.modules.personal.service.UserMessageService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.DlmService;
import cn.sphd.miners.modules.system.service.OrgPopedomService;
import cn.sphd.miners.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.sql.DataSource;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-05-16 10:26
 * @description 该类主要是处理会计模块的计划任务
 **/
public class AccountantTask {

    @Autowired
    OrgPopedomService orgPopedomService;
    @Autowired
    UserMessageService userMessageService;
    @Autowired
    UserService userService;
    @Autowired
    MessageDao messageDao;
    @Autowired
    DlmService dlmService;
    @Autowired
    SettleMapper settleMapper;
    @Autowired
    BuildAccountMapper buildAccountMapper;
    @Autowired
    AccountantSettleService settleService;
    @Autowired
    DataSource dataSource;

    /*
    * 计划任务，每天上午10:20判断所有启用的且有会计模块的机构
    * 1、如果机构没有使用过会计模块，即从来没有结过账的话不需要给超管发消息
    * 2、如果机构结账的月份比上个月还要早，且一直没有点过报税按钮则需要给超管消息提示
    * 3、如果机构上月已经结账且没点报税，且今天还没到12号上午10点，也不用提示
    * 4、如果机构上月已经结账且没点报税且今天已经超过了12号上午10点，则需要提示
    * */
    public void doScheduled() {
//        if(!System.getProperty("miners.share_file_url").isEmpty()){//此处代码服务器才执行，本地不执行。
        if(!GetLocalIPUtils.isServer(dataSource)){//此处代码服务器才执行，本地不执行。
            //wyu:获取分布式锁
            String lockKey, methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
            if ((lockKey=dlmService.getLock(methodName)) != null) {
                System.out.println("开始判断会计模块是否报税："+new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
                //获取当前的日期
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
                Calendar calendar = Calendar.getInstance();
                int today = calendar.get(Calendar.DAY_OF_MONTH);
                calendar.add(Calendar.MONTH, -1);
                String curDate = sdf.format(calendar.getTime());
                //得到所有启用的有会计模块的机构列表
//                List<Organization> orgList = orgPopedomService.getOrgPopedomByType("2");//1- 薪资宝 2-会计
                List<Integer> orgList = settleMapper.getAccountOrg();
                //循环到结账表中找机构最后一次结账数据
                for (Integer org : orgList) {
//                    int org = organization.getId();
                    TAccountantSettle settle = settleMapper.getByOrg(org);
                    if (settle != null) {
//                    if (org == 1573) {
//                        System.out.println(11);
//                    }
                        //如果结账的月份是上个月且没点报税且今天已经超过12号10点的话则需要提示
                        if (curDate.equals(settle.getPeriod())) {
                            if (today >= 12 && settle.getTaxState().equals("0")) {
                                //需要给超管发消息提示
                                String mes = "您本月尚未报税，请您及时报税";
                                sendMes2Super(org,mes);
                            }
                        }
                        else {
                            //如果机构结账的月份不是上个月，且没有点击报税按钮，需要发消息
                            if (settle.getTaxState().equals("0")) {
                                //需要给超管发消息提示
                                String mes = "您本月尚未报税，请您及时报税";
                                sendMes2Super(org,mes);
                            }
                        }

                    }
                }
                System.out.println("ThreadID=" + Thread.currentThread().getId());
                System.out.println("会计报税判断结束：" + new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
                //wyu:释放分布式锁
                dlmService.releaseLock(methodName, lockKey);
            }
        }

    }

    /*给超管和大会计发报税提醒的消息*/
    public void sendMes2Super(int oid,String msg) {
        //获取超管
        User superUser = userService.getUserByRoleCode(oid,"super");
        int superid = superUser.getUserID();
        //发消息前先判断该机构是否已经存在了今天的报税消息，如果已经发过则不再发
        SimpleDateFormat now = new SimpleDateFormat("yyyy-MM-dd");
        QueryData qd = new QueryData();
        qd.put("receiver",superid);
        qd.put("nowstr",now.format(new Date()));
        List<Integer> messageids = settleMapper.getMessageIDs(qd);
        if (messageids.isEmpty()) {
            UserMessage userMessage = new UserMessage();
//        userMessage.setUser(leave.getUser());
            userMessage.setApprovalStatus(1);
            userMessage.setHandleId(superid + "");
//        userMessage.setMessageId(app.getId());
            userMessage.setEventType("会计报税");
            userMessage.setIllustrate(msg);
//        userMessage.setPersonnelReimburId(leave.getId());
            userMessage.setMessageType("12");
            userMessage.setState(1);
            userMessage.setReceiveUserId(superid);  //接收消息人
            userMessage.setCreateDate(new Date());
            userMessageService.addUserMassage(userMessage);
        }

        //给大会计发消息提示
        User accountant = null;
        accountant = userService.getUserByRoleCode(oid, "accounting");
        if (accountant == null) {
            accountant = userService.getUserByRoleCode(oid, "agentAccounting");
        }
        if (accountant != null) {
            int accountantid = accountant.getUserID();
            //发消息前先判断该机构是否已经存在了今天的报税消息，如果已经发过则不再发
            qd.put("receiver",accountantid);
            qd.put("nowstr",now);
            List<Integer> mesids = settleMapper.getMessageIDs(qd);
            if (mesids.isEmpty()) {
                UserMessage accMessage = new UserMessage();
                accMessage.setApprovalStatus(1);
                accMessage.setHandleId(accountantid + "");
                accMessage.setEventType("会计报税");
                accMessage.setIllustrate(msg);
                accMessage.setMessageType("12");
                accMessage.setState(1);
                accMessage.setReceiveUserId(accountantid);  //接收消息人
                accMessage.setCreateDate(new Date());
                userMessageService.addUserMassage(accMessage);
            }

        }

    }

    /*
    * 检测每个机构是否需要重新建账
    * */
    public void checkRebuild() {
        //wyu:获取分布式锁
        String lockKey, methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        if ((lockKey=dlmService.getLock(methodName)) != null) {
            //会计月初更新数据
            System.out.println("会计当结一月份帐时初始化年初余额及本年累计金额和数量开始："+new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
            settleService.initializationKjMonth();
            System.out.println("会计当结一月份帐时初始化年初余额及本年累计金额和数量结束："+new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));

            System.out.println("会计检测重新建账开始："+new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
//            List<Organization> superList = orgPopedomService.getOrgPopedomByType("2");//1- 薪资宝 2-会计
            List<Integer> orgList = settleMapper.getAccountOrg();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
            String now = sdf.format(new Date());
            QueryData qd = new QueryData();
            qd.put("state","Y");
            qd.put("now", now);
            for (Integer oid : orgList) {
//                int oid = org.getId();
                //检测改机构当前时间与上次报税之间相隔的月数，如果大于等于2的话需要进入重新建账逻辑
                qd.put("org", oid);
                Integer months = settleMapper.checkRebuild(qd);
                if (months != null && months >= 2) {
                    //更新rebuild为Y
                    settleMapper.updateRebuild(oid);
                    //更新机构表的重新建账字段
                    buildAccountMapper.setOrgRebuildState(qd);
                    //更新建账状态，如果需要重新建账的话也需要把当前的建账状态改成0
//                qd.put("state","0");
//                buildAccountMapper.setBuildAccountState(qd);
                }
            }
            System.out.println("会计检测重新建账结束："+new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
            //wyu:释放分布式锁
            dlmService.releaseLock(methodName, lockKey);
        }
    }

}

