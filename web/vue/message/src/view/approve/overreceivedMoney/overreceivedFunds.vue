<template>
  <div id="overreceivedFunds">
    <TY_NavTop title="多收来的款" isSearch="true"></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待复核" name="1" closable='false' :msgCount="onlineAuditReview.length">
        <div class="tip">
          <p>以下待付款项有待您复核。</p>
        </div>
        <div>
          <a class="ui-cell" v-on:click="jump('51' + item.loanBiz.id,0 , item)" v-for="(item, index) in onlineAuditReview" v-bind:key="index">
            <div class="item_fn">
              <div class="supName">原始的付款方： <span>{{ item.loanBiz.supplierName }}</span></div>
              <div>
                <p> 多收来的金额：{{ (item.loanBiz.amount || 0).toFixed(2) }}元</p>
              </div>
            </div>
            <div class="txt_right">数据创建：{{ item.loanBiz.createName  }}
              {{item.loanBiz.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="待付款" name="2" closable='false'>
        <div class="tip">
          <p>下列待付款项复核通过，有待付款。</p>
        </div>
        <div>
          <a class="ui-cell" v-on:click="jump('51' + item.loanBiz.id,1, item)" v-for="(item, index) in onlineAuditAble" v-bind:key="index">
            <div class="item_fn">
              <div class="supName">原始的付款方： <span>{{ item.loanBiz.supplierName }}</span></div>
              <div>
                <p> 多收来的金额：{{ (item.loanBiz.amount || 0).toFixed(2) }}元</p>
              </div>
            </div>
            <div class="txt_right">数据创建：{{ item.loanBiz.createName  }}
              {{item.loanBiz.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>
<style lang="less">
#overreceivedFunds{
  overflow: auto;
  .ui-cell{ display: block;  }
  .txt_right{ text-align: right;  }
  .tabCount{
    top:0;
    right:0 ;
    left:auto;
  }
  .tabs-tab{
    width:50%;
  }
  .tip{
    padding:5px 12px;
    background-color: #E2EFDA;
    display: block;
  }
}
</style>
<script>
export default {
  name: 'overreceivedFunds',
  data () {
    return {
      onlineAuditAble: [],
      onlineAuditReview: [],
      listenersUid: [],
      tabValue: '1'
    }
  },
  created () {
    this.tabValue = localStorage.navNum || 1
    this.getList()
    let that = this
    this.listenersUid = [
      // 待复核
      this.sphdSocket.subscribe('overpaymentReviewed', function (data) {
        console.log('overpaymentReviewed OK:')
        let getData = JSON.parse(data)
        console.log(getData)
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.onlineAuditReview.unshift(getData)
        } else if (operate < 0) {
          let id = getData.loanBiz.id
          let _index = -1
          that.onlineAuditReview.forEach(function (item, index) {
            if (item.loanBiz.id === id) {
              _index = index
            }
          })
          if (_index > -1) {
            that.onlineAuditReview.splice(_index, 1)
          }
        }
      }, null, 'user'),
      // 待付款
      this.sphdSocket.subscribe('overpaymentWaiting', function (data) {
        console.log('overpaymentWaiting  ')
        let getData = JSON.parse(data)
        console.log(getData)
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.onlineAuditAble.unshift(getData)
        } else if (operate < 0) {
          let id = getData.loanBiz.id
          let _index = -1
          that.onlineAuditAble.forEach(function (item, index) {
            if (item.loanBiz.id === id) {
              _index = index
            }
          })
          if (_index > -1) {
            that.onlineAuditAble.splice(_index, 1)
          }
        }
      }, null, 'user')
    ]
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    getList: function () {
      let that = this
      this.axios.get('../../../overpayment/getOverpaymentReviewedList.do')
        .then(function (response) {
          let res = response.data.data
          console.log('列表：', res)
          that.onlineAuditReview = res.overpaymentReviewedHandle //待复核
          that.onlineAuditAble = res.overpaymentReviewedApproval
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    jump: function (id, num, item) { // num用于判断是待复核还是待付款，pun则用于判断是票据数据还是预付款数据
      let businessGroup = Number(item.businessGroup || 0)
      if (num === 0) { // 待复核
        this.$router.push({
          path: `/purchasePaymentFinanceApprovalDetail/${id}/3/${businessGroup}`
        })
      } else if (num === 1) { // 待付款
        this.$router.push({
          path: `/purchasePaymentFinanceApprovalDetail2/${id}/3/${businessGroup}`
        })
      }
    }
  }
}
</script>
