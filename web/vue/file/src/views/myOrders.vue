<template>
  <div class="">
    <loading v-show="isLoad" />
    <div class="logo">
      <span>End<PERSON> + Hauser</span>
      <img src="../assets/logo.png" class="logoimg" />
    </div>
    <div>
      <div class="search-wrapper">
        <input
            placeholder="搜索"
            type="text"
            placeholder-class="search-placeholder"
            class="search-input"
            v-model="searchtxt"
        />
        <img src="../assets/search.png" class="search">
        <span class="searchbtn" @click="search">搜索</span>
      </div>
    </div>
    <div class="nav">
      <div class="listcon">
        <span v-for="(catelog, key) in catelogs"  :key="index" class="listI" @click="clickItem(key)"
              :class="'all' == key || 'border-left', cateActive == key ? 'active': ''" >
          <span class="title"> {{ catelog }} </span>
        </span>
      </div>
    </div>
    <div class="bobo" v-for="(order, index) in list">
      <div class="item border-left">
        <span class="title">{{ order.description }}</span>
        <span class='detailTotal'>{{ order.orderTotal }}元</span>
        <span class='count'>共 {{ order.totalCount }}{{ order.details[0]?.unit??'件' }}</span>
      </div>
      <div class="item">
        <span v-if="order?.operates?.includes('refund')" class="operateBtn" @click="refundOrder(order)">退款</span>
        <span v-if="order?.operates?.includes('refunding')" class="operateBtn disabled" disabled='disabled'>退款中</span>
        <span v-if="order?.operates?.includes('invoice')" class="operateBtn" onclick="alert('敬请期待');return false;">查看发票</span>
        <span v-if="order?.operates?.includes('cancel')" class="operateBtn" @click="cancelOrder(order)">取消订单</span>
        <span v-if="order?.operates?.includes('delete')" class="operateBtn" @click="deleteOrder(order)">删除订单</span>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.logo {
  text-align: right;
  height: 33px;
  line-height: 33px;
  padding: 0 10px;
}
.logo span {
  font-size: 22px;
}
.logoimg {
  width: 38px;
  height: 25px;
}
.search-wrapper {
  height: 48px;
  padding: 6px 13px;
  /* background: #960e0e; */
  position: relative;
  margin-bottom: 12px;
}
.search-input {
  width: 100%;
  height: 100%;
  padding-left: 32px;
  font-size: 14px;
  color: #666;
  box-sizing: border-box;
  border: 1px solid #ccc;
}
.search {
  position: absolute;
  width: 16px;
  height: 16px;
  left: 20px;
  top: 22px;
}
.bobo {
  margin-top: 10px;
  box-shadow: 0 2px 2px #eee;
  .border-left {
    border-left: 5px solid #61aad9;
  }
  .item {
    text-align: left;
    padding: 0.5em;
    position: relative;
    min-height: 4em;
    .title {
      display: block;
      float: left;
      width: 60%;
      min-height: 3em;
    }
    .detailTotal {
      display: block;
      float: right;
      text-align: right;
      width: 30%;
      font-size: medium;
    }
    .count {
      display: block;
      float: right;
      text-align: right;
      font-size: small;
      width: 30%;
    }
    .operateBtn {
      display: block;
      float: right;
      background-color: #63a8c9;
      color: #fff;
      padding: 0.1em 0.7em;
      margin-right: 0.5em;
      border-radius: 1em;
      cursor: pointer;
    }
  }
}
.searchbtn {
  position: absolute;
  top:6px;
  right: 13px;
  background-color: #63a8c9;
  color: #fff;
  height: 35px;
  line-height: 35px;
  padding: 0 20px;
  z-index: 99;
}
.navbtn-wrapper {
  margin: 1px 0;
}
.navbtn-around{
  width: 100%;
}
.navbtn {
  background-color: #63a8c9;
  color: #fff;
  display:block;
  width: 100%;
  height: 35px;
  line-height: 35px;
  padding: 0 20px;
  text-align: center;
}
.xflow {
  margin: 50px;
}

.nav {
  padding: 15px;
  overflow-x: auto;
  position: relative;
  box-shadow: 0 0 3px #ccc;
  white-space: nowrap;
  margin-bottom: 15px;
  width: 100%;
  box-sizing: border-box;
}
.nav .listcon {
  word-break: none;
}
.nav .active {
  color: #63a8c9;
}
.nav .listI {
  display:inline-block;
  width: v-bind("(100 / Object.keys(catelogs).length) + '%'");
  padding: 0 20px;
  font-size: 17px;
  cursor: pointer;
}
.nav .border-left {
  border-left:1px solid #63a8c9;
}
.toggleBtn {
  position: absolute;
  right: 0;
  top: 2px;
  background: #fff;
  width: 30px;
  height: 45px;
  text-align: center;
  background-color: #fff;
  z-index: 1;
}
.toggleBtn img {
  width: 20px;
  height: 20px;
  transform: rotate(-90deg);
  margin-top: 20px;
}
.list {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  padding: 0;
  margin: 0 10px;
}
.list .pakI {
  flex-grow: 0;
  overflow: hidden;
  width: 33%;
  height: 60px;
  position: relative;
  margin-bottom: 8px;
}
.list .pakI .packcon {
  display: flex;
  width: 90%;
  height: 98%;
  background-color: #e7ecf0;
  text-align: center;
  cursor: pointer;
  box-shadow: 0 2px 2px #ccc;
  border-radius: 4px;
  align-items: center;
  flex-direction: row;
  position: relative;
  right: -7%;
}
.packcon text {
  display: block;
  color: #596a75;
  font-size: 10px;
  width: 100%;
  text-align: center;
}
.packges {
  position: relative;
}

.swiper {
  height: 166px;
}

.swiperContainer {
  position: relative;
}
.img {
  width: 100%;
  height: 100%;
}
.imageCount {
  width: 60px;
  height: 25px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 20px;
  line-height: 25px;
  color: #fff;
  text-align: center;
  font-size: 13px;
  position: absolute;
  left: 13px;
  bottom: 10px;
}
.dots {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 10px;
  display: flex;
  justify-content: center;
}
.dots .dot {
  margin: 0 2px;
  width: 25px;
  height: 4px;
  background: #999;
  border-radius: 4px;
  transition: all 0.6s;
}
.dots .dot.active {
  width: 25px;
  background: #63a8c9;
}
.nav2 {
  position: relative;
  height: 50px;
  padding: 0 20px;
  /* background: #ccc; */
  line-height: 50px;
}
.nav2 .toggleBtn {
  right: 20px;
}
.nav2 .toggleBtn img {
  transform: rotate(90deg);
}
</style>
<script>
import dayjs from 'dayjs'
import { myOrders, cancelOrder, deleteOrder, refundOrder } from "@/api/api"
import sphdSocket from '@/sys/sphd'
import loading from "@/components/loading.vue"
export default {
  data() {
    return {
      dayjs: dayjs,
      searchtxt: "",
      isLoad: true,
      catelogs: {
        all : '全部',
        // shipping : '待收货',
        unpaid : '待付款',
        received : '已完成',
        returned : '已取消'
      },
      cateActive : 'all',
      list: [],
    };
  },
  components: {
    loading,
  },
  mounted() {
    let search = localStorage.getItem('myOrder')//获取存储的元素
    if (search) {
      this.searchtxt = search;
    }
    this.search();
    //wyu：订阅订单更新推送。
    sphdSocket.subscribe('ecMyOrders',function (res) {
      console.log('ecMyOrders', res);
      let operate = res.operate
      let orders = res.orders
      //ToDo:处理catlogs
      // public Triple<List<OrderStatus>, List<ShippingStatus>, List<PayStatus>> getStatus() {
      //   switch (this) {
      //     case unpaid:
      //       return Triple.of(Arrays.asList(OrderStatus.confirmed), Arrays.asList(ShippingStatus.pending), Arrays.asList(PayStatus.unpaid));
      //     case shipping:
      //       return Triple.of(Arrays.asList(OrderStatus.confirmed), Arrays.asList(ShippingStatus.pending, ShippingStatus.shipped, ShippingStatus.backordered), Arrays.asList(PayStatus.paid));
      //     case received:
      //       return Triple.of(Arrays.asList(OrderStatus.confirmed), Arrays.asList(ShippingStatus.received), Arrays.asList(PayStatus.paid));
      //     case returned:
      //       return Triple.of(Arrays.asList(OrderStatus.canceled, OrderStatus.returning, OrderStatus.returned), null, null);
      //     default://case all:
      //       return Triple.of(Arrays.asList(OrderStatus.confirmed, OrderStatus.canceled, OrderStatus.returning, OrderStatus.returned), null, null);
      //   }
      // }
      orders.foreach( newOrder => {
        this.list = this.list.filter(oldOrder => {
          return oldOrder.id != newOrder.id
        })
      })
      if (operate in [0,1]) {
        this.list = [...orders, ...this.list]
      }
    },null,'logged');
  },
  methods: {
    search() {
      this.isLoad = true
      this.list = []
      myOrders(wonderss.module, this.cateActive, this.searchtxt).then((res) => {
        console.log('search',res)
        this.isLoad = false
        this.list = res.data || []
        console.log(this.list)
      });
    },
    cancelOrder(order) {
      if(!this.isLoad) {
        this.isLoad = true
        cancelOrder(order.id).then((data) => {
          console.log('cancleOrder', typeof data, data)
          if (data.success === 0) {
            console.log(data.error.message)
            this.$message({type: 'error', message: data.error.message})
          } else {
            this.search()
          }
        })
      }
    },
    deleteOrder(order) {
      if(!this.isLoad) {
        this.isLoad = true
        deleteOrder(order.id).then((data) => {
          console.log('deleteOrder', typeof data, data)
          if (data.success === 0) {
            console.log(data.error.message)
            this.$message({type: 'error', message: data.error.message})
          } else {
            this.search()
          }
        })
      }
    },
    refundOrder(order) {
      if(!this.isLoad) {
        this.isLoad = true
        refundOrder(order.id).then((data) => {
          console.log('refundOrder', typeof data, data)
          if (data.success === 0) {
            console.log(data.error.message)
            this.$message({type: 'error', message: data.error.message})
          } else {
            setTimeout(() => {
              this.search()
            }, 60000)//OK:TODO +订阅
          }
        })
      }
    },
    clickItem(key) {
      this.cateActive = key
      this.search()
    }
  },
};
</script>