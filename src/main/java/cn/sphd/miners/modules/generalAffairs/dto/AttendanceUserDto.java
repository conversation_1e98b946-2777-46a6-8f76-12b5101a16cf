package cn.sphd.miners.modules.generalAffairs.dto;

import cn.sphd.miners.modules.generalAffairs.entity.*;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName AttendanceDto
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/1/9 11:25
 * @Version 1.0
 */
public class AttendanceUserDto extends PersonnelAttendanceUser{
    private List<PersonnelAttendanceUserDetail> attendanceUserDetailList;

    public List<PersonnelAttendanceUserDetail> getAttendanceUserDetailList() {
        return attendanceUserDetailList;
    }

    public void setAttendanceUserDetailList(List<PersonnelAttendanceUserDetail> attendanceUserDetailList) {
        this.attendanceUserDetailList = attendanceUserDetailList;
    }
}
