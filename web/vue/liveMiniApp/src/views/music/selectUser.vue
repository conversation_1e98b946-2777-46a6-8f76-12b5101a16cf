<template>
  <div class="selectUser">
    <Pageheader title="选择任务的接收对象" :showBack="true"/>
    <div class="">
      <div class="live-container">
        <div>
          <el-button type="text" class="live-right" @click="selectAll()">选择全部</el-button>
          <span>请选择任务的接收对象</span>
        </div>
        <div class="marTop30">已选择{{ selectNum }}人</div>
        <div class="userList">
          <div class="userItem" v-for="(item,index) in userList" :key="index">
            <div @click.stop="toggleSelect(item, $event)">
              <el-checkbox v-if="!item.isSelect" v-model="check1"></el-checkbox>
              <el-checkbox v-if="item.isSelect" v-model="check2"></el-checkbox>
              <span class="uMsg">{{item.name}} {{item.mobile}}</span>
            </div>

          </div>
        </div>
        <span @click="selectOk" class="primary live-button">选择完毕</span>
      </div>
    </div>
  </div>
</template>
<script setup>



import { ref, reactive, onMounted, computed } from 'vue';
import { getTeamMembers } from "@/api/api.js";
import { useRouter } from "vue-router";
import { ElMessage } from 'element-plus'
import JSONBig from 'json-bigint'

const router = useRouter();
const check1 = ref(false);
const check2 = ref(true);
const userList = reactive([]);

let selectNum = computed( () => {
  let selectUser = []
  console.log(userList)
  userList.forEach( sta => {
    sta.isSelect ? selectUser.push(sta) : ''
  })
  return selectUser.length
})

onMounted(() => {
  getMembers()
});

const getMembers = function(){
  getTeamMembers().then(res => {
    console.log('getTeamMembers response', res )
    let members = res.data;
    members.forEach( stf => {
      userList.push( { id: stf.userID, 'isSelect':false, name:stf.userName, mobile:stf.mobile })
    })
  }).catch(res =>{
    console.log('txt error', res)
  });
}
const selectAll = () => {
  userList.forEach( item => {
    item.isSelect = true
  })
}
const toggleSelect = (item, ev) => {
  console.log(ev)
  console.log('d调几次呢？')
  console.log('修改前', item)

  item.isSelect = !item.isSelect
  ev.stopPropagation();

  console.log('修改后',item)
  console.log('userList ii=', userList)
}
const selectOk = () => {
  let staffList = []
  userList.forEach( item => {
    item.isSelect? staffList.push(item) : ''
  })
  localStorage.setItem('staffList', JSONBig.stringify(staffList))
  router.push({
    name: 'assignTasks'
  });
}



</script>
<style lang="scss" scoped>
.selectUser{
  .userList{
    padding:10px;
  }
  .marTop30{ margin-top: 20px; }
  .uMsg{ display: inline-block; margin-left: 10px; }
}
</style>
