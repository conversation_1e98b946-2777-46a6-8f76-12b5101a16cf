package cn.sphd.miners.modules.material.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.accountant.service.SubjectSelectService;
import cn.sphd.miners.modules.accountant.service.SubjectSettingService;
import cn.sphd.miners.modules.commodity.entity.PdBase;
import cn.sphd.miners.modules.commodity.entity.PdMerchandise;
import cn.sphd.miners.modules.commodity.service.PdCompositionService;
import cn.sphd.miners.modules.commodity.service.PdProcessService;
import cn.sphd.miners.modules.commodity.service.ProductService;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.material.dao.SrmSupplierHistoryDao;
import cn.sphd.miners.modules.material.entity.*;
import cn.sphd.miners.modules.material.service.*;
import cn.sphd.miners.modules.material.utils.SubjectUtils;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.entity.UserMessage;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.personal.service.UserMessageService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.entity.ApprovalFlow;
import cn.sphd.miners.modules.system.entity.ApprovalItem;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.ApprovalService;
import cn.sphd.miners.modules.system.service.OrgService;
import cn.sphd.miners.modules.system.service.RoleService;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSON;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
//import java.util.function.Supplier;

/**
 * Created by Administrator on 2016/9/18.
 */
@Controller
@RequestMapping("/material")
public class MaterialController {

    @Autowired
    MaterielService materielService;
    @Autowired
    OrgService orgService;
    @Autowired
    SrmSupplierService srmSupplierContact;
    @Autowired
    PdProcessService pdProcessService;
    @Autowired
    ProductService productService;
    @Autowired
    PdCompositionService pdCompositionService;
    @Autowired
    MtStockService mtStockService;
    @Autowired
    MtStockInfoHistoryService mtStockInfoHistoryService;

    @Autowired
    SubjectSelectService subjectSelectService;

    @Autowired
    SubjectSettingService settingService;

    @Autowired
    UserMessageService userMessageService;

    @Autowired
    UserService userService;

    @Autowired
    MtService mtService;
    @Autowired
    UserSuspendMsgService suspendMsgService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    SWMessageService swMessageService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;

    @Autowired
    SrmSupplierHistoryDao  srmSupplierHistoryDao;

    @Autowired
    ApprovalService approvalService;
    @Autowired
    RoleService roleService;

    @Autowired
    SrmSupplierService supplierService;

    //进入物料信息主页- 李旭
    @RequestMapping("/materialManage.do")
    public String materialManage() {
        return "/material/materialManage";
    }

    //进入供应商名录-  李旭
    @RequestMapping("/supplierDirectory.do")
    public String supplierDirectory() {
        return "/material/providerManage";
    }


    //获取一级或子级物料类别  李旭
    @ResponseBody
    @RequestMapping("/getMtCategoryBypid.do")
    public void getMtCategoryBy(User user, Integer pid, HttpServletResponse response) throws IOException {
        Integer oid = user.getOid();
        List<MtCategory> mtCategories = materielService.getMtCategoryByOidAndPid(oid, pid);
        List<MtCategory> mtCategoryList = new ArrayList<>();
        for (MtCategory m : mtCategories) {
            if (!"外购成品".equals(m.getName()) && !"半成品".equals(m.getName()) && !"商品".equals(m.getName())) {
                mtCategoryList.add(m);
            }
        }
        Map<String, Object> map = new HashMap<>();
        if (mtCategories.size() > 0) {
            map.put("status", 1);//新建成功
            map.put("mtCategories", mtCategoryList);
        } else {
            map.put("status", 0);//新建成功
        }
        ObjectToJson.objectToJson1(map, new String[]{"parent", "org", "mtBaseHashSet", "mtCategoryHashSet", "category", "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet"}, response);

    }

    //根据物料类型id获取物料列表信息 李旭
    @ResponseBody
    @RequestMapping("/getMtBaseByMtCategoryId.do")
    public void getMtBaseById(Integer categoryId, HttpServletResponse response) throws IOException {
        List<MtBase> mtBases = materielService.getMtBaseByCategoryId(categoryId);
        for (MtBase m : mtBases) {
            BigDecimal minimumStock = new BigDecimal(0);
            for (MtStockInfo ms : m.getMtStockInfoHashSet()) {
                minimumStock =minimumStock.add( ms.getMinimumStock());
            }
            m.setMinimumStock(minimumStock);
        }
        ObjectToJson.objectToJson(mtBases, new String[]{"category", "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet"}, response);

    }

    //新增分类  李旭
//    @ResponseBody
    @RequestMapping("/addMtCategoryByPid.do")

    public void addMtCategoryByPid(User user, Integer pid, Integer type, String name, HttpServletResponse response) throws IOException {

        Integer oid = user.getOid();
        if (type == 0) pid = null;

        Organization organization = orgService.getByOid(oid);

        if (name != null && type != null && !"".equals(name)) {
            if (pid != null) {
                List<MtCategory> categories = materielService.getMtCategoryByOidAndPid(oid, pid);

                for (MtCategory c : categories) {

                    if (name.equals(c.getName())){
                        Map map = new HashMap();
                        map.put("status", "2");
                        ObjectToJson.objectToJson(map, new String[]{}, response);
                        return;

                    }
                }
                MtCategory PCategory = materielService.getMtCategoryById(pid);
                MtCategory mtCategory = new MtCategory();
                mtCategory.setOrg(organization);
                mtCategory.setName(name);
                mtCategory.setCreateDate(new Date());
                if (PCategory.getLevel() == 1) {
                    mtCategory.setFirstGradeId(PCategory.getId());//如果父级为第一级，新增分类的一级id存此父级id
                } else {
                    mtCategory.setFirstGradeId(PCategory.getFirstGradeId());//否则新增分类的一级id存上级的一级id
                }
                //type==0是同级
                if (type == 0) {
                    mtCategory.setLevel(PCategory.getLevel());
                    mtCategory.setParent(PCategory.getParent());
                } else {
                    mtCategory.setLevel(PCategory.getLevel() + 1);
                    mtCategory.setParent(PCategory);
                }
                materielService.saveMtCategory(mtCategory);
                Map map = new HashMap();
                map.put("category", mtCategory);
                try {
                    ObjectToJson.objectToJson(map, new String[]{"org", "mtBaseHashSet", "parent", "mtCategoryHashSet"}, response);
                } catch (IOException e) {
                    Logger.getLogger(getClass()).error("addMtCategoryByPid.do ObjectToJson.objectToJson IOException", e);
                }

            } else {
                List<MtCategory> categories = materielService.getMtCategoryByOidAndPid(oid, null);

                for (MtCategory c : categories) {
                    if (name.equals(c.getName())){
                        Map map = new HashMap();
                        map.put("status", "2");
                        ObjectToJson.objectToJson(map, new String[]{}, response);
                        return;
                    }

                }
                //否则新增一级分类
                MtCategory mtCategory = new MtCategory();
                mtCategory.setOrg(organization);
                mtCategory.setName(name);
                mtCategory.setCreateDate(new Date());
                mtCategory.setLevel(1);

                materielService.saveMtCategory(mtCategory);

                mtCategory.setFirstGradeId(mtCategory.getId());

                //在新增分类下初始化下级待分类
                MtCategory daifenlei = new MtCategory();
                daifenlei.setOrg(organization);
                daifenlei.setName("待分类");
                daifenlei.setLevel(mtCategory.getLevel() + 1);
                daifenlei.setParent(mtCategory);
                daifenlei.setCreateDate(new Date());
                daifenlei.setFirstGradeId(mtCategory.getId());
                materielService.saveMtCategory(daifenlei);
                materielService.updateMtCategory(mtCategory);

                Map map = new HashMap();
                map.put("category", mtCategory);
                map.put("child", daifenlei);

                try {
                    ObjectToJson.objectToJson(map, new String[]{"org", "mtBaseHashSet", "parent", "mtCategoryHashSet"}, response);
                } catch (IOException e) {
                    Logger.getLogger(getClass()).error("addMtCategoryByPid.do ObjectToJson.objectToJson IOException1", e);
                }

            }


        } else {


            Map map = new HashMap();
            map.put("status", "0");
            ObjectToJson.objectToJson(map, new String[]{}, response);

        }
    }

    /**
     * <AUTHOR>
     * @Date 2016/10/14 11:30
     * 第一次获取物料信息主页面数据
     */
    @ResponseBody
    @RequestMapping("/getMtCategoryAndMtBaseOne.do")
    public void getMtCategoryAndMtBaseOne(User user, Integer pid, Integer pageNumber, Integer quantum, HttpServletResponse response) throws IOException {
        Integer oid = user.getOid();

        List<MtCategory> mtCategories = new ArrayList<MtCategory>();

        mtCategories = materielService.getMtCategoryByOidAndPid(oid, pid);

        Map<String, Object> map = new HashMap<String, Object>();

        if (mtCategories.size() > 0) {
            List<MtBase> mbs = new ArrayList<MtBase>();//物料当前分类下所有子分类的结果集
            for (MtCategory mc : mtCategories) {
                List<MtCategory> mtCategoryList = materielService.getMtCategoriesById(mc.getId());
                mtCategoryList.add(mc);
                if (mtCategoryList != null && mtCategoryList.size() > 0) {
                    List<MtBase> zhongzhuan = new ArrayList<MtBase>();//存值中转站
                    for (MtCategory c : mtCategoryList) {
                        List<MtBase> mtBases = materielService.getMtBaseByCategoryId(c.getId());
                        for (MtBase m : mtBases) {
                            BigDecimal minimumStock = new BigDecimal(0);
                            BigDecimal currentStock = new BigDecimal(0);

                            Set<MtSupplierMaterial> supplierMaterials = m.getMtSupplierMaterialHashSet();
                            for (MtSupplierMaterial mtSupplierMaterial : supplierMaterials) {
                                minimumStock =minimumStock.add( mtSupplierMaterial.getMinimumStock() == null ? new BigDecimal(0): mtSupplierMaterial.getMinimumStock());
                                currentStock =currentStock.add( mtSupplierMaterial.getInitialStock() == null ? new BigDecimal(0): mtSupplierMaterial.getInitialStock());
                            }

                            for (MtStockInfo ms : m.getMtStockInfoHashSet()) {
                                if (ms.getCurrentStock() != null)
                                    currentStock =currentStock.add( (ms.getCurrentStock()));
                                m.setStockPosition(ms.getStockPosition());
                            }
                            m.setMinimumStock(minimumStock);
                            m.setCurrentStock(currentStock + "");
                            if (m.getCategory().getParent() != null) {
                                m.setCategoryName(m.getCategory().getParent().getName());
                            } else {
                                m.setCategoryName(m.getCategory().getName());
                            }
                            if (!mbs.contains(m))
                                zhongzhuan.add(m);
                        }
                    }
//                    mbs.addAll(zhongzhuan);
                    mc.setAmount(zhongzhuan.size());
                    materielService.updateMtCategory(mc);
                }
            }
            List<MtCategory> mtCategorys = materielService.getMtCategoriesById(mtCategories.get(0).getId());

            for (MtCategory mc : mtCategorys) {
                List<MtBase> mtBasess = materielService.getMtBaseByCategoryId(mc.getId());
                for (MtBase m : mtBasess) {
                    List<MtBase> chuanzhi = new ArrayList<MtBase>();//存值中转站
                    BigDecimal minimumStock = new BigDecimal(0);
                    BigDecimal currentStock = new BigDecimal(0);

                    Set<MtSupplierMaterial> supplierMaterials = m.getMtSupplierMaterialHashSet();
                    for (MtSupplierMaterial mtSupplierMaterial : supplierMaterials) {
                        minimumStock =minimumStock.add( mtSupplierMaterial.getMinimumStock() == null ? new BigDecimal(0): mtSupplierMaterial.getMinimumStock());
                        currentStock =currentStock.add( mtSupplierMaterial.getInitialStock() == null ? new BigDecimal(0): mtSupplierMaterial.getInitialStock());
                    }

                    for (MtStockInfo ms : m.getMtStockInfoHashSet()) {
                        if (ms.getCurrentStock() != null)
                            currentStock =currentStock.add( (ms.getCurrentStock()));
                        m.setStockPosition(ms.getStockPosition());
                    }
                    m.setMinimumStock(minimumStock);
                    m.setCurrentStock(currentStock + "");
                    m.setCategoryName(m.getCategory().getParent().getName());
                    chuanzhi.add(m);
                }
                mbs.addAll(mtBasess);
            }

            List<MtBase> mtBaseList = new ArrayList<MtBase>();
            if (pageNumber != null && mbs.size() > 0) {
                int max = pageNumber * quantum;
                int min = pageNumber * quantum - quantum;
                int total = mbs.size();
                for (int i = min; i < max; i++) {
                    if (i < total)
                        mtBaseList.add(mbs.get(i));
                }

                int totalPage = (total + quantum - 1) / quantum;//计算总页数

                map.put("cur", pageNumber);//第几页
                map.put("countall", totalPage);//总页数
            } else {
                map.put("cur", 1);//第几页
                map.put("countall", 1);//总页数
            }
            map.put("mtBases", mtBaseList);//分页，页数和每页条数由调用时传
        } else {
            List<MtBase> mbs = new ArrayList<MtBase>();//物料当前分类下所有子分类的结果集
            List<MtBase> mtBases = materielService.getMtBaseByCategoryId(pid);
            for (MtBase m : mtBases) {
                BigDecimal minimumStock = new BigDecimal(0);
                BigDecimal currentStock = new BigDecimal(0);

                Set<MtSupplierMaterial> supplierMaterials = m.getMtSupplierMaterialHashSet();
                for (MtSupplierMaterial mtSupplierMaterial : supplierMaterials) {
                    minimumStock =minimumStock.add( mtSupplierMaterial.getMinimumStock() == null ? new BigDecimal(0): mtSupplierMaterial.getMinimumStock());
                    currentStock =currentStock.add( mtSupplierMaterial.getInitialStock() == null ? new BigDecimal(0): mtSupplierMaterial.getInitialStock());
                }

                for (MtStockInfo ms : m.getMtStockInfoHashSet()) {
                    if (ms.getCurrentStock() != null)
                        currentStock =currentStock.add( (ms.getCurrentStock()));
                    m.setStockPosition(ms.getStockPosition());
                }
                m.setMinimumStock(minimumStock);
                m.setCurrentStock(currentStock + "");
                m.setCategoryName(m.getCategory().getParent().getName());
                if (!mbs.contains(m))
                    mbs.add(m);
            }

            List<MtBase> mtBaseList = new ArrayList<MtBase>();
            if (pageNumber != null && mbs.size() > 0) {
                int max = pageNumber * quantum;
                int min = pageNumber * quantum - quantum;
                int total = mbs.size();
                for (int i = min; i < max; i++) {
                    if (i < total)
                        mtBaseList.add(mbs.get(i));
                }

                int totalPage = (total + quantum - 1) / quantum;//计算总页数

                map.put("cur", pageNumber);//第几页
                map.put("countall", totalPage);//总页数
            } else {
                map.put("cur", 1);//第几页
                map.put("countall", 1);//总页数
            }
            map.put("mtBases", mtBaseList);//分页，页数和每页条数由调用时传

        }
        map.put("mtCategories", mtCategories);


        List<MtCategory> daozheMtCategory = new ArrayList<MtCategory>();
        if (pid != null) {
            MtCategory mtCategory = materielService.getMtCategoryById(pid);
            daozheMtCategory.add(mtCategory);
            for (int i = 0; i < mtCategory.getLevel(); i++) {

                if (mtCategory.getParent() != null) {
                    daozheMtCategory.add(mtCategory.getParent());
                    mtCategory = mtCategory.getParent();
                } else {
                    continue;
                }
            }
        } else {
            if (mtCategories.size() > 0)
                daozheMtCategory.add(mtCategories.get(0));
        }
        Collections.reverse(daozheMtCategory);//倒序排列
        map.put("daozheMtCategory", daozheMtCategory);

        ObjectToJson.objectToJson1(map, new String[]{"parent", "org", "mtBaseHashSet", "mtCategoryHashSet", "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet", "PdCompositionMaterialHashSet", "pdCompositionMaterialHashSet", "product", "category", "pdPackHashSet"}, response);

    }


    //从第二次之后每次获取物料信息主页面数据  李旭
    @ResponseBody
    @RequestMapping("/getMtCategoryAndMtBase.do")
    public void getMtCategoryAndMtBase(User user, Integer pid, Integer pageNumber, Integer quantum, HttpServletResponse response) throws IOException {
        Integer oid = user.getOid();
        List<MtCategory> mtCategories = new ArrayList<MtCategory>();
//        if (pid==null){
        mtCategories = materielService.getMtCategoryByOidAndPid(oid, pid);
//        }else {
//            mtCategories=materielService.getMtCategoryByOidAndPid(oid,materielService.getMtCategoryById(pid).getId());
//            if (mtCategories==null||mtCategories.size()<=0){
//                MtCategory mtCategory=materielService.getMtCategoryById(pid);
//                if (mtCategory.getLevel()!=1)
//                mtCategories.add(mtCategory);
//            }
//        }
        Map<String, Object> map = new HashMap<String, Object>();

        if (mtCategories.size() > 0) {
            List<MtBase> mbs = new ArrayList<MtBase>();//物料当前分类下所有子分类的结果集
            for (MtCategory mc : mtCategories) {
                List<MtCategory> mtCategoryList = materielService.getMtCategoriesById(mc.getId());
                mtCategoryList.add(mc);
                if (mtCategoryList != null && mtCategoryList.size() > 0) {
                    List<MtBase> zhongzhuan = new ArrayList<MtBase>();//存值中转站

                    for (MtCategory c : mtCategoryList) {
                        List<MtBase> mtBases = materielService.getMtBaseByCategoryId(c.getId());
                        for (MtBase m : mtBases) {
                            BigDecimal minimumStock = new BigDecimal(0);
                            BigDecimal currentStock = new BigDecimal(0);

                            Set<MtSupplierMaterial> supplierMaterials = m.getMtSupplierMaterialHashSet();
                            for (MtSupplierMaterial mtSupplierMaterial : supplierMaterials) {
                                minimumStock =minimumStock.add( mtSupplierMaterial.getMinimumStock() == null ? new BigDecimal(0): mtSupplierMaterial.getMinimumStock());
                                currentStock =currentStock.add( mtSupplierMaterial.getInitialStock() == null ? new BigDecimal(0): mtSupplierMaterial.getInitialStock());
                            }

                            for (MtStockInfo ms : m.getMtStockInfoHashSet()) {
                                if (ms.getCurrentStock() != null)
                                    currentStock =currentStock.add( (ms.getCurrentStock()));
                                m.setStockPosition(ms.getStockPosition());
                            }
                            m.setMinimumStock(minimumStock);
                            m.setCurrentStock(currentStock + "");
                            m.setCategoryName(m.getCategory().getParent().getName());
                            if (!mbs.contains(m))
                                zhongzhuan.add(m);
                        }
                    }
                    mbs.addAll(zhongzhuan);
                    mc.setAmount(zhongzhuan.size());
                    materielService.updateMtCategory(mc);
                } else {
                    List<MtBase> mtBases = materielService.getMtBaseByCategoryId(mtCategories.get(0).getId());
                    for (MtBase m : mtBases) {
                        BigDecimal minimumStock =new BigDecimal(0) ;
                        BigDecimal currentStock = new BigDecimal(0) ;

                        Set<MtSupplierMaterial> supplierMaterials = m.getMtSupplierMaterialHashSet();

                        for (MtSupplierMaterial mtSupplierMaterial : supplierMaterials) {
                            minimumStock =minimumStock.add(mtSupplierMaterial.getMinimumStock() == null ? new BigDecimal(0): mtSupplierMaterial.getMinimumStock()) ;
                            currentStock =currentStock.add(mtSupplierMaterial.getInitialStock() == null ? new BigDecimal(0) : mtSupplierMaterial.getInitialStock()) ;
                        }

                        for (MtStockInfo ms : m.getMtStockInfoHashSet()) {
                            if (ms.getCurrentStock() != null)
                                currentStock =currentStock.add( (ms.getCurrentStock()));
                            m.setStockPosition(ms.getStockPosition());
                        }
                        m.setMinimumStock(minimumStock);
                        m.setCurrentStock(currentStock + "");
                        m.setCategoryName(m.getCategory().getParent().getName());

                    }
                    mbs.addAll(mtBases);
                }
            }
            List<MtBase> mtBaseList = new ArrayList<MtBase>();
            if (pageNumber != null && mbs.size() > 0) {
                int max = pageNumber * quantum;
                int min = pageNumber * quantum - quantum;
                int total = mbs.size();
                for (int i = min; i < max; i++) {
                    if (i < total)
                        mtBaseList.add(mbs.get(i));
                }

                int totalPage = (total + quantum - 1) / quantum;//计算总页数

                map.put("cur", pageNumber);//第几页
                map.put("countall", totalPage);//总页数
            } else {
                map.put("cur", 1);//第几页
                map.put("countall", 1);//总页数
            }
            map.put("mtBases", mtBaseList);//分页，页数和每页条数由调用时传
        } else {
            List<MtBase> mbs = new ArrayList<MtBase>();//物料当前分类下所有子分类的结果集
            List<MtBase> mtBases = materielService.getMtBaseByCategoryId(pid);
            for (MtBase m : mtBases) {
                BigDecimal minimumStock = new BigDecimal(0);
                BigDecimal currentStock = new BigDecimal(0);

                Set<MtSupplierMaterial> supplierMaterials = m.getMtSupplierMaterialHashSet();

                for (MtSupplierMaterial mtSupplierMaterial : supplierMaterials) {
                    minimumStock =minimumStock.add( mtSupplierMaterial.getMinimumStock() == null ? new BigDecimal(0): mtSupplierMaterial.getMinimumStock());
                    currentStock =currentStock.add( mtSupplierMaterial.getInitialStock() == null ? new BigDecimal(0): mtSupplierMaterial.getInitialStock());
                }

                for (MtStockInfo ms : m.getMtStockInfoHashSet()) {
                    if (ms.getCurrentStock() != null)
                        currentStock =currentStock.add( (ms.getCurrentStock()));
                    m.setStockPosition(ms.getStockPosition());
                }
                m.setMinimumStock(minimumStock);
                m.setCurrentStock(currentStock + "");
                if (m.getCategory().getParent() != null) {
                    m.setCategoryName(m.getCategory().getParent().getName());
                } else {
                    m.setCategoryName(m.getCategory().getName());
                }
                if (!mbs.contains(m))
                    mbs.add(m);
            }

            List<MtBase> mtBaseList = new ArrayList<MtBase>();
            if (pageNumber != null && mbs.size() > 0) {
                int max = pageNumber * quantum;
                int min = pageNumber * quantum - quantum;
                int total = mbs.size();
                for (int i = min; i < max; i++) {
                    if (i < total)
                        mtBaseList.add(mbs.get(i));
                }

                int totalPage = (total + quantum - 1) / quantum;//计算总页数

                map.put("cur", pageNumber);//第几页
                map.put("countall", totalPage);//总页数
            } else {
                map.put("cur", 1);//第几页
                map.put("countall", 1);//总页数
            }
            map.put("mtBases", mtBaseList);//分页，页数和每页条数由调用时传

//            map.put("cur",1);//第几页
//            map.put("countall",1);//总页数
//            map.put("mtBases",new ArrayList<MtBase>());//分页，页数和每页条数由调用时传
        }
        map.put("mtCategories", mtCategories);


        List<MtCategory> daozheMtCategory = new ArrayList<MtCategory>();
        if (pid != null) {
            MtCategory mtCategory = materielService.getMtCategoryById(pid);
            daozheMtCategory.add(mtCategory);
            for (int i = 0; i < mtCategory.getLevel(); i++) {

                if (mtCategory.getParent() != null) {
                    daozheMtCategory.add(mtCategory.getParent());
                    mtCategory = mtCategory.getParent();
                } else {
                    continue;
                }
            }
        } else {
//            daozheMtCategory.add(mtCategories.get(0));
        }
        Collections.reverse(daozheMtCategory);//倒序排列
        map.put("daozheMtCategory", daozheMtCategory);


        ObjectToJson.objectToJson1(map, new String[]{"parent", "org", "mtBaseHashSet", "mtCategoryHashSet", "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet", "PdCompositionMaterialHashSet", "pdCompositionMaterialHashSet", "product", "category", "pdPackHashSet"}, response);

    }


    //打开新增物料时保存个空物料id的接口  李旭
    @ResponseBody
    @RequestMapping("/addMtBase.do")
    public void addMtBase(User user, HttpServletResponse response) throws IOException {
        MtBase mtBase = new MtBase();
        mtBase.setSource("2");//等于2 为物料录入的
        materielService.saveMtBase(mtBase);

        Map<String, Object> map = new HashMap<>();
        map.put("baseId", mtBase.getId());

        Integer oid = user.getOid();
        List<MtCategory> mtCategories = materielService.getMtCategoryByOidAndPid(oid, null);
        map.put("mtCategories", mtCategories);
        ObjectToJson.objectToJson(map, new String[]{"parent", "org", "mtBaseHashSet", "mtCategoryHashSet", "category", "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet"}, response);
    }


    //新增供应商及采购信息   李旭
    @ResponseBody
    @RequestMapping("/addMtSupplierMaterial.do")
    public void addMtSupplierMaterial(MtSupplierMaterial mtSupplierMaterial, Integer supplierId, Integer baseId, User user, String name, String fullName, String codeName, HttpServletResponse response) throws IOException {
        Integer oid = user.getOid();
        Map<String, Object> map = new HashMap<>();

        if (name != null) {
            if (mtSupplierMaterial.getUnitPrice() != null && mtSupplierMaterial.getTaxRate() != null) {
                BigDecimal taxRate = mtSupplierMaterial.getTaxRate() .divide(new BigDecimal(100)) ;

                mtSupplierMaterial.setUnitPriceNotax(mtSupplierMaterial.getUnitPrice().multiply((new BigDecimal(1).subtract(taxRate) )));//不含税单价
            }
            if (supplierId != null) {
                SrmSupplier supplier = srmSupplierContact.getSrmSupplier(supplierId);
                mtSupplierMaterial.setSupplier(supplier.getId());//不为空说明供应商存在 存已存在的供应商外键
            } else {
                //否则认为不存在，新增供应商
                SrmSupplier mtSupplier = new SrmSupplier();
                mtSupplier.setName(name);
                mtSupplier.setFullName(fullName);
                mtSupplier.setCodeName(codeName);
                mtSupplier.setOrg(oid);
                mtSupplier.setSupplyCount(0);
                mtSupplier.setExclusiveCount(0);
                mtSupplier.setCutCount(0);
                materielService.saveSrmSupplier(mtSupplier);//新增供应商
                mtSupplierMaterial.setSupplier(mtSupplier.getId());//供应商外键
            }
            MtBase mtBase = materielService.getMtBaseById(baseId);

            mtSupplierMaterial.setDeleteType(1);//删除状态 1为可以删除
            mtSupplierMaterial.setMaterial(mtBase);//物料外键
            materielService.saveMtSupplierMaterial(mtSupplierMaterial);
            map.put("status", 1);//新建成功
            map.put("mtSupplierMaterial", mtSupplierMaterial);
            map.put("name", name);
            map.put("fullName", fullName);
        } else {
            map.put("status", 0);//新建失败
        }
        ObjectToJson.objectToJson1(map, new String[]{"supplier", "material"}, response);

    }

    //编辑供应商及采购信息  李旭
    @ResponseBody
    @RequestMapping("/updateMtSupplierMaterial.do")
    public void updateMtSupplierMaterial(MtSupplierMaterial mtSupplierMaterial, String name, String fullName, HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<>();

        if (name != null) {
            MtSupplierMaterial material = materielService.getMtSupplierMaterialById(mtSupplierMaterial.getMaterial_());
            material.setMinimumPurchase(mtSupplierMaterial.getMinimumPurchase());
            material.setPerchaseCycle(mtSupplierMaterial.getPerchaseCycle());
            material.setUnitPrice(mtSupplierMaterial.getUnitPrice());
            material.setTaxRate(mtSupplierMaterial.getTaxRate());
            if (mtSupplierMaterial.getUnitPrice() != null && mtSupplierMaterial.getTaxRate() != null) {
                BigDecimal taxRate = mtSupplierMaterial.getTaxRate() .divide(new BigDecimal(100)) ;
                material.setUnitPriceNotax(mtSupplierMaterial.getUnitPrice().multiply((new BigDecimal(1) .subtract(taxRate) )));//不含税单价
            }
            material.setContractSn(material.getContractSn());
            materielService.updateMtSupplierMaterial(mtSupplierMaterial);//供货对照表

            if (name != null && !"".equals(name)) {
                SrmSupplier supplier = materielService.getSrmSupplierById(mtSupplierMaterial.getSupplier());
                supplier.setName(name);
                supplier.setFullName(fullName);
                materielService.updateSrmSupplier(supplier);// 供应商表
            }
            map.put("status", 1);//更新成功
            map.put("mtSupplierMaterial", material);
            map.put("name", name);
        } else {
            map.put("status", 0);//更新失败
        }
        ObjectToJson.objectToJson1(map, new String[]{"supplier", "material"}, response);
    }

    //删除供应商及其供货信息接口   李旭
    @ResponseBody
    @RequestMapping("/deleteMtSupplierMaterial.do")
    public void deleteMtSupplierMaterial(Integer materialId, HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<>();
        if (materialId != null) {
            MtSupplierMaterial material = materielService.getMtSupplierMaterialById(materialId);
            SrmSupplier mtSupplier = srmSupplierContact.getSrmSupplier(material.getSupplier());

            if (materielService.getListBySupplierId(mtSupplier.getId()).size() >= 2) {
                materielService.deleteMtSupplierMaterial(material);//供应商采购信息大于2个物料时，不可直接删除供应商，只能删除采购信息
            } else {
                //否则删除供应商
                materielService.deleteSrmSupplier(mtSupplier);
            }
            map.put("status", 1);
        } else {
            map.put("status", 0);
        }
        ObjectToJson.objectToJson1(map, new String[]{}, response);
    }

    //保存物料基本信息、库房信息、质量信息接口   李旭
    @ResponseBody
    @RequestMapping("/addBaseStockInfoQuality.do")
    public void addBaseStockInfoQuality(String baseMemo, String qualityMemo, Integer baseId, Integer categoryId, MtBase mtBase, MtStockInfo mtStockInfo, MtQuality mtQuality, HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<>();

        if (baseId != null) {
            MtBase base = materielService.getMtBaseById(baseId);
            base.setName(mtBase.getName());
            base.setStockPosition(mtBase.getStockPosition());
            base.setMinimumPurchase(mtBase.getMinimumPurchase());
            base.setCode(mtBase.getCode());
            base.setUnitPrice(mtBase.getUnitPrice());
            base.setUnit(mtBase.getUnit());
            base.setNetWeight(mtBase.getNetWeight());
            base.setMemo(baseMemo);
            MtCategory mtCategory = materielService.getMtCategoryById(categoryId);
            base.setCategory(mtCategory);
            materielService.updateMtBase(base);//更新物料基本信息

            for (MtSupplierMaterial m : base.getMtSupplierMaterialHashSet()) {
                m.setDeleteType(0);//更新删除状态为0，编辑取消时不可以删除
                materielService.updateMtSupplierMaterial(m);
            }

            mtStockInfo.setMaterial(base);//库存表存入物料外键
            materielService.saveMtStockInfo(mtStockInfo);//保存库存信息

            mtQuality.setMaterial(base);//质量表存入物料外键
            mtQuality.setMemo(qualityMemo);//质量备注
            materielService.saveMtQuality(mtQuality);//保存质量信息


            BigDecimal minimumStock = new BigDecimal(0);
            for (MtStockInfo ms : base.getMtStockInfoHashSet()) {
                minimumStock =minimumStock.add( ms.getMinimumStock());
                base.setStockPosition(ms.getStockPosition());
            }
            base.setMinimumStock(minimumStock);
            map.put("status", 1);
            map.put("base", base);

        } else {
            map.put("status", 0);

        }
        ObjectToJson.objectToJson1(map, new String[]{"parent", "org", "mtBaseHashSet", "mtCategoryHashSet", "category",
                "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet"}, response);

    }


    //根据供应商名检索供应商的接口  李旭
    @ResponseBody
    @RequestMapping("/retrievalSupplierByName.do")
    public void retrievalSupplierByName(String name,Integer enabled,Integer supplierId, HttpServletResponse response, User user,Integer oid) throws IOException {
        Map<String, Object> map = new HashMap<>();
        oid = oid==null?user.getOid():oid;

        List<SrmSupplier > supplierList;


        supplierList = materielService.getSrmSupplierByName(name, oid,enabled,supplierId);

        for (SrmSupplier s : supplierList) {
            MtSupplierMaterial supplierMaterial = materielService.getMtSupplierMaterialBySupplierAndMaterial(s.getId(), null);

            s.setSupplierMaterial(supplierMaterial);


            s.setYs(mtService.getLocationHisDetails(supplierMaterial,s));

        }


        map.put("supplierList", supplierList);

        ObjectToJson.objectToJson1(map, new String[]{"material", "supploer", "updateDate", "createDate", "creator", "updator", "updateName", "createName"}, response);

    }

    /**
     * <AUTHOR>
     * @date 2016/9/26 11:43
     * 打开编辑物料信息
     */
    @ResponseBody
    @RequestMapping("/toUpdateBaseStockInfoQuality.do")
    public void toUpdateBaseStockInfoQuality(Integer baseId, User user, HttpServletResponse response) throws IOException {
        Integer oid = user.getOid();
        List<MtCategory> mtCategories = materielService.getMtCategoryByOidAndPid(oid, null);

        MtBase base = materielService.getMtBaseById(baseId);
        Map<String, Object> map = new HashMap<>();
        List<MtSupplierMaterial> mList = new ArrayList<MtSupplierMaterial>();
        if (base.getMtSupplierMaterialHashSet().size() > 0) {
            for (MtSupplierMaterial m : base.getMtSupplierMaterialHashSet()) {
                SrmSupplier supplier = materielService.getSrmSupplierById(m.getSupplier());
                m.setName(supplier.getName());
                m.setFullName(supplier.getFullName());
                m.setId(m.getSupplier());
                mList.add(m);
            }
        }
        map.put("mtSupplierMaterialList", mList);
        MtCategory mtCategory = materielService.getMtCategoryById(base.getCategory_());
//        map.put("currentCategory",base.getCategory());//当前物料类别
//        map.put("mtCategories",mtCategories);//物料一级分类
        map.put("base", base);
        map.put("mtStockInfo", base.getMtStockInfoHashSet());
        map.put("mtQuality", base.getMtQualityMtBaseViaId());
        map.put("supplier", base.getMtSupplierMaterialHashSet());

        List<List<MtCategory>> listList = new ArrayList<List<MtCategory>>();

        if (mtCategory.getId() != null) {
            MtCategory mtCategoryxun = materielService.getMtCategoryById(mtCategory.getId());
            for (int i = 0; i < base.getCategory().getLevel(); i++) {

                if (mtCategoryxun != null) {
                    List<MtCategory> jieguo = new ArrayList<>();
                    jieguo.add(mtCategoryxun);//当前分类

                    List<MtCategory> feileiLiset = new ArrayList<>();
                    if (mtCategoryxun.getParent() != null) {
                        feileiLiset = materielService.getMtCategoryByOidAndPid(oid, mtCategoryxun.getParent().getId());
                    } else {
                        feileiLiset = mtCategories;
                    }
                    for (MtCategory m : feileiLiset) {
                        if (!jieguo.contains(m)) {
                            jieguo.add(m);
                        }
                    }
                    listList.add(jieguo);
                    mtCategoryxun = mtCategoryxun.getParent();
                } else {
                    continue;
                }
            }
        }
        Collections.reverse(listList);//倒序排列

        map.put("continuityCategory", listList);
        ObjectToJson.objectToJson1(map, new String[]{"pdPackHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet", "mtStockInfoHashSet", "mtQualityMtBaseViaId", "category", "mtBaseHashSet", "mtCategoryHashSet", "org", "parent", "material", "supplier"}, response);

    }

    /**
     * <AUTHOR>
     * @date 2016/9/26 15:34
     * 取消新增物料
     */
    @ResponseBody
    @RequestMapping("/cancelAddMtBase.do")
    public void cancelMtBase(Integer baseId, HttpServletResponse response) {
        MtBase base = materielService.getMtBaseById(baseId);
        for (MtSupplierMaterial m : base.getMtSupplierMaterialHashSet()) {
            //删除状态为1，说明供应关系并未生效，可以删除供应商
            if (m.getDeleteType() == 1) {
                SrmSupplier supplier = srmSupplierContact.getSrmSupplier (m.getSupplier());

                if (materielService.getListBySupplierId(supplier.getId()).size() >= 2) {
                    materielService.deleteMtSupplierMaterial(m);//供应商采购信息大于2个物料时，不可直接删除供应商，只能删除采购信息
                } else {
                    //否则删除供应商
                    materielService.deleteSrmSupplier (supplier);
                }
            }
        }
        materielService.deleteMtBase(base);//删除此物料那条数据
    }

    /**
     * <AUTHOR>
     * @date 2016/10/12 9:21
     * 编辑物料、供应商、供货关系、库存、质量信息等
     */
    @ResponseBody
    @RequestMapping("/updateBase.do")
    public void updateBase(HttpServletRequest request, User user, HttpServletResponse response) throws IOException, ParseException {
        Integer oid = user.getOid();
        

        materielService.updateBase(request, response, oid, user, subjectSelectService, userMessageService, userService, productService);
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd");


    }


    /**
     * <AUTHOR>
     * @date 2016/10/8 10:04
     * 编辑物料类型名称
     */
    @ResponseBody
    @RequestMapping("/updateCategory.do")
    public void updateCategory(Integer id, String name, HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<>();

        if (id != null) {
            MtCategory category = materielService.getMtCategoryById(id);
            category.setName(name);
            materielService.updateMtCategory(category);
            map.put("category", category);
            map.put("status", 1);
        } else {
            map.put("status", 0);
        }
        ObjectToJson.objectToJson1(map, new String[]{"org", "mtBaseHashSet", "parent", "mtCategoryHashSet"}, response);

    }

    /**
     * <AUTHOR>
     * @date 2016/10/8 10:42
     * 删除物料类型接口
     */
    @ResponseBody
    @RequestMapping("/deleteCategory.do")
    public void deleteCategory(Integer id, HttpServletResponse response) throws IOException {
        MtCategory category = materielService.getMtCategoryById(id);
        Map<String, Object> map = new HashMap<>();

        if (category.getMtCategoryHashSet().size() <= 1) {
            if (category.getMtCategoryHashSet().size() > 0) {
                for (MtCategory m : category.getMtCategoryHashSet()) {
                    if (m.getMtBaseHashSet().size() <= 0) {
                        materielService.deleteCategory(category);
                        map.put("status", 1);
                    } else {
                        map.put("status", 3);//子分类下有物料不能删除
                    }
                }
            } else {
                if (category.getMtBaseHashSet().size() <= 0) {
                    materielService.deleteCategory(category);
                    map.put("status", 1);
                } else {
                    map.put("status", 4);//当前分类有物料不能删除
                }
            }
        } else {
            map.put("status", 2);//有子分类不可删除
        }
        ObjectToJson.objectToJson1(map, new String[]{"org", "mtBaseHashSet", "parent", "mtCategoryHashSet"}, response);
    }

    /**
     * <AUTHOR>
     * @date 2016/10/8 10:58
     * 根据id删除物料
     */
    @ResponseBody
    @RequestMapping("/deleteBaseById.do")
    public void deleteBaseById(Integer id, HttpServletResponse response, User user) throws IOException {
        Map<String, Object> map = new HashMap<>();

        if (id != null) {
            MtBase mtBase = materielService.getMtBaseById(id);
            materielService.deleteMtBase(mtBase);
            map.put("status", 1);
        } else {
            map.put("status", 0);
        }

        List<SrmSupplier > suppliers = srmSupplierContact.getByOid(user.getOid(), 1, null, null, 1);

        for (SrmSupplier mt : suppliers) {

                if (materielService.getListBySupplierId(mt.getId()).size() <= 0)
                    materielService.deleteSrmSupplier (mt);
        }
        ObjectToJson.objectToJson1(map, new String[]{"parent", "org", "mtBaseHashSet", "mtCategoryHashSet", "category", "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet"}, response);

    }

    /**
     * <AUTHOR>
     * @date 2016/10/12 16:24
     * 供应商名录删除供应商接口
     */
    @ResponseBody
    @RequestMapping("/deleteSupplier.do")
    public void deleteSupplier(Integer id, HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<>();
        if (id != null) {
            SrmSupplier supplier = srmSupplierContact.getSrmSupplier (id);
            if (materielService.getListBySupplierId(supplier.getId()).size() > 0) {
                map.put("status", 2);//存在对物料的供货关系不可删除
            } else {
                materielService.deleteSrmSupplier (supplier);
                map.put("status", 1);//删除成功
            }
        } else {
            map.put("status", 0);//id为空删除失败
        }
        ObjectToJson.objectToJson1(map, new String[]{""}, response);
    }

    /**
     * <AUTHOR>
     * @date 2016/10/8 10:19
     *
     */

//        ArrayList ww= (ArrayList) s.get(0);
//        JSONObject jsonBase = new JSONObject(base);
//        JSONObject jsonKuInfo=new JSONObject(kuInfo);
//        JSONObject jsonQualityInfo=new JSONObject(qualityInfo);
//        JSONArray jsonArray=new JSONArray(supplierList);
//        jsonArray.get(0);
//        String code = jsonBase.getString("code");
//
//        String aa="[[0,\"供应商1·\",\"20\",\"20\",\"20\",\"17%\",\"不含税单价 \",\"合同编号\"],[0,\"供应商2\",\"20\",\"20\",\"20\",\"17%\",\"不含税单价 \",\"合同编号\"],[0\n" +
//                ",\"供应商3\",\"30\",\"30\",\"30\",\"17%\",\"不含税单价 \",\"321651\"]]\n";
//        String[] bb = aa.split(";");
//        int m = bb.length;
//        int n = bb[0].split(",").length;

    /**
     * 供应商名录展示
     * 李娅星
     */
    @ResponseBody
    @RequestMapping("/getAllMtSupplier.do")
    public void getAllMtSupplier(HttpServletResponse response, User user) throws IOException {
        Integer oid = user.getOid();
        List<SrmSupplier > mtSuppliers = srmSupplierContact.getByOid(oid, 1, null, null, 1);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("mtSuppliers", mtSuppliers);
        ObjectToJson.objectToJson1(map, new String[]{"fullName", "paymentType", "bankCode", "bankName", "bankNo", "keywords", "creator", "createName", "createDate", "updator", "updateName", "updateDate", "mtSupplierMaterialHashSet"}, response);
    }

    /**
     * 查看（check）
     * 李娅星
     */
    @ResponseBody
    @RequestMapping("/checkMtSupplierContact.do")
    public void checkMtSupplierContact(HttpServletResponse response, Integer supplierId) throws IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        SrmSupplier mtSuppliers = srmSupplierContact.getById(supplierId);
        List<SrmSupplierContact> mtSupplierContacts = srmSupplierContact.getBySupplierId(supplierId);
        map.put("mtSuppliers", mtSuppliers);
        map.put("mtSupplierContacts", mtSupplierContacts);
        ObjectToJson.objectToJson1(map, new String[]{"bankCode", "keywords", "creator", "createName", "createDate", "updator", "updateName", "updateDate", "mtSupplierMaterialHashSet", "supplier", "supplier_"}, response);
    }

    //修改供应商详细信息 林成

    /**
     * <AUTHOR>
     * @Date 2016/9/25 0025 16:58
     */
    @ResponseBody
    @RequestMapping("/updateSupplierDirectory.do")
    public void supplierUpdateDirectory(User user, SrmSupplier mtSupplier, Integer id, HttpServletResponse response) throws IOException {

        Map<String, Object> map = new HashMap<String, Object>();
        SrmSupplier mtSupplier1 = srmSupplierContact.getSrmSupplier (id);
        if (mtSupplier != null && id != null) {
            

            mtSupplier1.setUpdateDate(Timestamp.valueOf(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())));
            mtSupplier1.setUpdator(user.getUserID());
            mtSupplier1.setUpdateName(user.getUserName());
            mtSupplier1.setContact(mtSupplier.getContact());
            mtSupplier1.setMobile(mtSupplier.getMobile());
            mtSupplier1.setFax(mtSupplier.getFax());
            mtSupplier1.setPaymentMethod(mtSupplier.getPaymentMethod());
            mtSupplier1.setBankNo(mtSupplier.getBankNo());
            mtSupplier1.setTelephone(mtSupplier.getTelephone());
            mtSupplier1.setEmail(mtSupplier.getEmail());
            mtSupplier1.setAddress(mtSupplier.getAddress());
            mtSupplier1.setBankName(mtSupplier.getBankName());
            mtSupplier1.setPaymentType(mtSupplier.getPaymentType());
            srmSupplierContact.updateSrmSupplier (mtSupplier1);
             /*ObjectToJson.objectToJson(mtSupplier, new String[]{"creator", "createName",
                     "createDate","fullName","keywords"}, response);*/
            map.put("status", 1);
            map.put("mtSupplier", mtSupplier1);
        } else {


            map.put("status", 0);
        }
        ObjectToJson.objectToJson1(map, new String[]{"mtSupplierMaterialHashSet", "creator", "createName",
                "createDate", "fullName", "keywords"}, response);


    }

    //新增联系人 林成

//    /**
//     * <AUTHOR>
//     * @Date 2016/9/25 0025 16:57
//     */
//    @ResponseBody
//    @RequestMapping("/addSupplierContact.do")
//    public void addSupplierContact(User user, SrmSupplierContact mtSupplierContact, Integer msid, HttpServletResponse response) throws IOException {
//
//        Map<String, Object> map = new HashMap<String, Object>();
//        SrmSupplier mtSupplier = srmSupplierContact.getSrmSupplier (msid);
//        if (mtSupplierContact != null && mtSupplier != null) {
//
//            mtSupplierContact.setCreateName(user.getUserName());
//            mtSupplierContact.setCreator(user.getUserID());
//            mtSupplierContact.setSupplier(mtSupplier);
//            mtSupplierContact.setCreateDate(Timestamp.valueOf(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())));
//            srmSupplierContact.addSrmSupplierContact(mtSupplierContact);
//            /*ObjectToJson.objectToJson(mtSupplierContact, new String[]{"updator", "updateName", "updateDate"}, response);*/
//            map.put("status", 1);
//            map.put("mtSupplierContact", mtSupplierContact);
//        } else {
//
//            map.put("status", 0);
//
//        }
//        ObjectToJson.objectToJson1(map, new String[]{"supplier", "updator", "updateName", "updateDate"}, response);
//
//    }


    //删除联系人 林成

    /**
     * <AUTHOR>
     * @Date 2016/9/25 0025 16:57
     */
    @ResponseBody
    @RequestMapping("/deleteSupplierContact.do")
    public void SupplierDeleteContact(User user, Integer id, Integer mtSupplierId, HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        if (id != null && mtSupplierId != null) {

            SrmSupplierContact mtSupplierContact = srmSupplierContact.getSrmSupplierContactById(id);
            srmSupplierContact.deleteSrmSupplierContact(mtSupplierContact);

            map.put("status", 1);
        } else {
            map.put("status", 0);

        }
        ObjectToJson.objectToJson1(map, new String[]{}, response);

    }

//    //编辑联系人  林成
//
//    /**
//     * <AUTHOR>
//     * @Date 2016/9/25 0025 16:57
//     */
//    @ResponseBody
//    @RequestMapping("/updateSupplierContact.do")
//    public void SupplierUpdateContact(User user, SrmSupplierContact mtSupplierContact, Integer id, Integer msid, HttpServletResponse response) throws IOException {
//
//        Map<String, Object> map = new HashMap<String, Object>();
//        SrmSupplier mtSupplier = srmSupplierContact.getSrmSupplier (msid);
//        SrmSupplierContact mtSupplierContact1 = srmSupplierContact.getSrmSupplierContactById(id);
//        if (mtSupplierContact != null && id != null && mtSupplier != null) {
//
//
//
//            mtSupplierContact1.setUpdator(user.getUserID());
//            mtSupplierContact1.setUpdateDate(Timestamp.valueOf(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())));
//            mtSupplierContact1.setUpdateName(user.getUserName());
//            mtSupplierContact1.setSupplier(mtSupplier);
//
//            mtSupplierContact1.setName(mtSupplierContact.getName());
//            mtSupplierContact1.setMobile(mtSupplierContact.getMobile());
//            mtSupplierContact1.setTelephone(mtSupplierContact.getTelephone());
//            mtSupplierContact1.setEmail(mtSupplierContact.getEmail());
//            mtSupplierContact1.setFax(mtSupplierContact.getFax());
//            mtSupplierContact1.setMemo(mtSupplierContact.getMemo());
//            mtSupplierContact1.setDepartName(mtSupplierContact.getDepartName());
//            mtSupplierContact1.setPostName(mtSupplierContact.getPostName());
//
//            srmSupplierContact.updateSrmSupplierContact(mtSupplierContact1);
//       /* ObjectToJson.objectToJson(mtSupplierContact, new String[]{"creator","createName",
//                "createDate"}, response);*/
//            map.put("mtSupplierContact", mtSupplierContact1);
//            map.put("status", 1);
//        } else {
//            map.put("status", 0);
//        }
//        ObjectToJson.objectToJson1(map, new String[]{"supplier", "creator", "createName",
//                "createDate"}, response);
//
//    }


    /**
     * <AUTHOR>
     * @Date 2016/10/26 14:46
     * 物料主页面重定向
     */
    @RequestMapping("/partMaterial.do")
    public String partMaterial(Integer pid, Integer pageNumber, Integer quantum) {
        MtCategory category = materielService.getMtCategoryById(pid);
        if ("外购成品".equals(category.getName()) || "商品".equals(category.getName()) || "半成品".equals(category.getName())) {
            return "redirect:/material/getCommodityEntry.do?pid=" + pid + "&pageNumber=" + pageNumber + "&quantum=" + quantum;
        }
        if (category.getFirstGradeId() != null) {
            MtCategory c = materielService.getMtCategoryById(category.getFirstGradeId());
            if ("外购成品".equals(c.getName()) || "商品".equals(c.getName()) || "半成品".equals(c.getName())) {
                return "redirect:/material/getCommodityEntry.do?pid=" + pid + "&pageNumber=" + pageNumber + "&quantum=" + quantum;
            } else {
                return "redirect:/material/getMtCategoryAndMtBase.do?pid=" + pid + "&pageNumber=" + pageNumber + "&quantum=" + quantum;
            }
        }
        return "redirect:/material/getMtCategoryAndMtBase.do?pid=" + pid + "&pageNumber=" + pageNumber + "&quantum=" + quantum;
    }

    /**
     * <AUTHOR>
     * @Date 2016/10/28 14:25
     * 在物料主页面获取商品、半成品、外购成品列表信息
     */
    @ResponseBody
    @RequestMapping("/getCommodityEntry.do")
    public void getCommodityEntry(User user, Integer pid, Integer pageNumber, Integer quantum, HttpServletResponse response) throws IOException {
        Integer oid = user.getOid();
        List<MtCategory> mtCategories = new ArrayList<MtCategory>();

        mtCategories = materielService.getMtCategoryByOidAndPid(oid, pid);
        Map<String, Object> map = new HashMap<String, Object>();

        if (mtCategories.size() > 0) {
            List<MtBase> mbs = new ArrayList<MtBase>();//物料当前分类下所有子分类的结果集
            List<PdBase> pbs = new ArrayList<>();//当前分类下由商品录入的所有子分类的结果集
            for (MtCategory mc : mtCategories) {
                List<MtCategory> mtCategoryList = materielService.getMtCategoriesById(mc.getId());
                mtCategoryList.add(mc);
                if (mtCategoryList != null && mtCategoryList.size() > 0) {
                    List<MtBase> zhongzhuan = new ArrayList<MtBase>();//物料存值中转站
                    List<PdBase> pBaseZhuan = new ArrayList<>();//商品传值中转站

                    for (MtCategory c : mtCategoryList) {
                        List<MtBase> mtBases = materielService.getMtBaseByCategoryId(c.getId());
                        for (MtBase m : mtBases) {
                            BigDecimal minimumStock = new BigDecimal(0);
                            BigDecimal currentStock = new BigDecimal(0);
                            Set<MtSupplierMaterial> supplierMaterials = m.getMtSupplierMaterialHashSet();
                            for (MtSupplierMaterial mtSupplierMaterial : supplierMaterials) {
                                minimumStock =minimumStock.add( mtSupplierMaterial.getMinimumStock() == null ? new BigDecimal(0): mtSupplierMaterial.getMinimumStock());
                                currentStock =currentStock.add( mtSupplierMaterial.getInitialStock() == null ? new BigDecimal(0): mtSupplierMaterial.getInitialStock());
                            }

                            for (MtStockInfo ms : m.getMtStockInfoHashSet()) {
                                if (ms.getCurrentStock() != null)
                                    currentStock =currentStock.add( (ms.getCurrentStock()));
                                m.setStockPosition(ms.getStockPosition());
                            }

                            if ("待分类".equals(m.getCategory().getName())) {
                                m.setCategoryName(m.getCategory().getParent().getName());
                            } else {
                                m.setCategoryName(m.getCategory().getName());
                            }

                            if (m.getProduct() != null) {
                                minimumStock = m.getProduct().getMinimumiStock();
                                currentStock = m.getProduct().getCurrentStock();
                                m.getProduct().setStockPosition(m.getStockPosition());//把库位放到商品中
                                m.getProduct().setCategoryName(m.getCategoryName());//把物料分类放到商品中
                            }

                            m.setMinimumStock(minimumStock);
                            m.setCurrentStock(currentStock + "");
                            if (!mbs.contains(m))
                                zhongzhuan.add(m);

                            PdBase pdBase = m.getProduct();
                            if (pdBase != null) {
                                pdBase.setCurrentStock(currentStock);
                                pdBase.setMinimumiStock(minimumStock);
                            }

                            if (!pbs.contains(m.getProduct()) && m.getProduct() != null)
                                pBaseZhuan.add(pdBase);
                        }
                    }
                    mbs.addAll(zhongzhuan);
                    pbs.addAll(pBaseZhuan);
                    mc.setAmount(pBaseZhuan.size());
                    materielService.updateMtCategory(mc);
                } else {
                    List<MtBase> mtBases = materielService.getMtBaseByCategoryId(mtCategories.get(0).getId());
                    List<PdBase> pdBases = new ArrayList<>();
                    for (MtBase m : mtBases) {
                        BigDecimal minimumStock = new BigDecimal(0);
                        BigDecimal currentStock = new BigDecimal(0);

                        Set<MtSupplierMaterial> supplierMaterials = m.getMtSupplierMaterialHashSet();
                        for (MtSupplierMaterial mtSupplierMaterial : supplierMaterials) {
                            minimumStock =minimumStock.add( mtSupplierMaterial.getMinimumStock() == null ? new BigDecimal(0):mtSupplierMaterial.getMinimumStock());
                            currentStock =currentStock.add( mtSupplierMaterial.getInitialStock() == null ? new BigDecimal(0):mtSupplierMaterial.getInitialStock());
                        }

                        for (MtStockInfo ms : m.getMtStockInfoHashSet()) {
                            if (ms.getCurrentStock() != null)
                                currentStock =currentStock.add( (ms.getCurrentStock()));
                            m.setStockPosition(ms.getStockPosition());
                        }


                        if ("待分类".equals(m.getCategory().getName())) {
                            m.setCategoryName(m.getCategory().getParent().getName());
                        } else {
                            m.setCategoryName(m.getCategory().getName());
                        }

                        if (m.getProduct() != null) {
                            minimumStock = m.getProduct().getMinimumiStock();
                            currentStock = m.getProduct().getCurrentStock();
                            m.getProduct().setStockPosition(m.getStockPosition());//把库位放到商品中
                            m.getProduct().setCategoryName(m.getCategoryName());//把物料分类放到商品中
                        }
                        m.setMinimumStock(minimumStock);
                        m.setCurrentStock(currentStock + "");
                    }
                    mbs.addAll(mtBases);
                    pbs.addAll(pdBases);
                }
            }
            List<PdBase> pdBaseList = new ArrayList<PdBase>();
            if (pageNumber != null && pbs.size() > 0) {
                int max = pageNumber * quantum;
                int min = pageNumber * quantum - quantum;
                int total = pbs.size();
                for (int i = min; i < max; i++) {
                    if (i < total)
                        pdBaseList.add(pbs.get(i));
                }

                int totalPage = (total + quantum - 1) / quantum;//计算总页数

                map.put("cur", pageNumber);//第几页
                map.put("countall", totalPage);//总页数
            } else {
                map.put("cur", 1);//第几页
                map.put("countall", 1);//总页数
            }
            map.put("pdBases", pdBaseList);//分页，页数和每页条数由调用时传
        } else {
            List<MtBase> mbs = new ArrayList<MtBase>();//物料当前分类下所有子分类的结果集
            List<PdBase> pbs = new ArrayList<PdBase>();
            List<MtBase> mtBases = materielService.getMtBaseByCategoryId(pid);
            for (MtBase m : mtBases) {
                BigDecimal minimumStock = new BigDecimal(0);
                BigDecimal currentStock = new BigDecimal(0);

                Set<MtSupplierMaterial> supplierMaterials = m.getMtSupplierMaterialHashSet();
                for (MtSupplierMaterial mtSupplierMaterial : supplierMaterials) {
                    minimumStock =minimumStock.add( mtSupplierMaterial.getMinimumStock() == null ? new BigDecimal(0): mtSupplierMaterial.getMinimumStock());
                    currentStock =currentStock.add( mtSupplierMaterial.getInitialStock() == null ? new BigDecimal(0): mtSupplierMaterial.getInitialStock());
                }

                for (MtStockInfo ms : m.getMtStockInfoHashSet()) {
                    if (ms.getCurrentStock() != null)
                        currentStock =currentStock.add( (ms.getCurrentStock()));
                    m.setStockPosition(ms.getStockPosition());
                }
                if (m.getProduct() != null) {
                    minimumStock = m.getProduct().getMinimumiStock();
                    currentStock = m.getProduct().getCurrentStock();
                }

                m.setMinimumStock(minimumStock);
                m.setCurrentStock(currentStock + "");
                if ("待分类".equals(m.getCategory().getName())) {
                    m.setCategoryName(m.getCategory().getParent().getName());
                } else {
                    m.setCategoryName(m.getCategory().getName());
                }

                if (!mbs.contains(m))
                    mbs.add(m);

                if (m.getProduct() != null) {
                    m.getProduct().setStockPosition(m.getStockPosition());//把库位放到商品中
                    m.getProduct().setCategoryName(m.getCategoryName());//把物料分类放到商品中
                }

                if (!pbs.contains(m.getProduct()) && m.getProduct() != null)
                    pbs.add(m.getProduct());
            }

            List<PdBase> pdBaseList = new ArrayList<PdBase>();
            if (pageNumber != null && pbs.size() > 0) {
                int max = pageNumber * quantum;
                int min = pageNumber * quantum - quantum;
                int total = pbs.size();
                for (int i = min; i < max; i++) {
                    if (i < total)
                        pdBaseList.add(pbs.get(i));
                }

                int totalPage = (total + quantum - 1) / quantum;//计算总页数

                map.put("cur", pageNumber);//第几页
                map.put("countall", totalPage);//总页数
            } else {
                map.put("cur", 1);//第几页
                map.put("countall", 1);//总页数
            }
            map.put("pdBases", pdBaseList);//分页，页数和每页条数由调用时传
            map.put("mtBases", mtBases);//分页，页数和每页条数由调用时传

//            map.put("cur",1);//第几页
//            map.put("countall",1);//总页数
//            map.put("mtBases",new ArrayList<MtBase>());//分页，页数和每页条数由调用时传
        }
        map.put("mtCategories", mtCategories);


        List<MtCategory> daozheMtCategory = new ArrayList<MtCategory>();
        if (pid != null) {
            MtCategory mtCategory = materielService.getMtCategoryById(pid);
            daozheMtCategory.add(mtCategory);
            for (int i = 0; i < mtCategory.getLevel(); i++) {

                if (mtCategory.getParent() != null) {
                    daozheMtCategory.add(mtCategory.getParent());
                    mtCategory = mtCategory.getParent();
                } else {
                    continue;
                }
            }
        } else {
//            daozheMtCategory.add(mtCategories.get(0));
        }
        Collections.reverse(daozheMtCategory);//倒序排列
        map.put("daozheMtCategory", daozheMtCategory);


        ObjectToJson.objectToJson1(map, new String[]{"parent", "org", "mtBaseHashSet", "mtCategoryHashSet", "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet", "TMtBaseViaProduct", "tMtBaseViaProduct", "TPdCompositionViaParent", "tPdCompositionViaParent", "TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct", "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct", "TPdCompositionViaParent"}, response);

    }


    /**
     * <AUTHOR>
     * @Date 2016/12/8 10:21
     * 在物料中查看来源于商品的物料详情
     */
    @ResponseBody
    @RequestMapping("/getPdBaseSupplerByPdBaseId.do")
    public void getPdBaseSupplerByPdBaseId(User user, Integer pdBaseId, String categoryName, HttpServletResponse response) throws IOException {
        Integer oid = user.getOid();

        PdBase pdBase = productService.getById(pdBaseId);
        MtBase mbase = new MtBase();
        for (MtBase m : pdBase.getTMtBaseViaProduct()) {
            MtCategory category = materielService.getMtCategoryById(m.getCategory().getFirstGradeId());
            if (categoryName.equals(category.getName())) {
                mbase = m;
            }
        }

        List<MtCategory> mtCategories = materielService.getMtCategoryByOidAndPid(oid, null);

        MtBase base = materielService.getMtBaseById(mbase.getId());
        Map<String, Object> map = new HashMap<>();
        List<MtSupplierMaterial> mList = new ArrayList<MtSupplierMaterial>();
        if (base.getMtSupplierMaterialHashSet().size() > 0) {
            for (MtSupplierMaterial m : base.getMtSupplierMaterialHashSet()) {
                SrmSupplier supplier = materielService.getSrmSupplierById(m.getSupplier());
                m.setName(supplier.getName());
                mList.add(m);
            }
        }


        map.put("mtStockInfo", base.getMtStockInfoHashSet());

        map.put("mtSupplierMaterialList", mList);
        MtCategory mtCategory = materielService.getMtCategoryById(base.getCategory_());
        map.put("pdBase", pdBase);

        map.put("mtQuality", base.getMtQualityMtBaseViaId());

        List<List<MtCategory>> listList = new ArrayList<List<MtCategory>>();

        if (mtCategory.getId() != null) {
            MtCategory mtCategoryxun = materielService.getMtCategoryById(mtCategory.getId());
            for (int i = 0; i < base.getCategory().getLevel(); i++) {

                if (mtCategoryxun != null) {
                    List<MtCategory> jieguo = new ArrayList<>();
                    jieguo.add(mtCategoryxun);//当前分类

                    List<MtCategory> feileiLiset = new ArrayList<>();
                    if (mtCategoryxun.getParent() != null) {
                        feileiLiset = materielService.getMtCategoryByOidAndPid(oid, mtCategoryxun.getParent().getId());
                    } else {
                        feileiLiset = mtCategories;
                    }
                    for (MtCategory m : feileiLiset) {
                        if (!jieguo.contains(m)) {
                            jieguo.add(m);
                        }
                    }
                    listList.add(jieguo);
                    mtCategoryxun = mtCategoryxun.getParent();
                } else {
                    continue;
                }

            }
        }
        Collections.reverse(listList);//倒序排列


        map.put("continuityCategory", listList);
        ObjectToJson.objectToJson1(map, new String[]{"mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet", "mtStockInfoHashSet", "mtQualityMtBaseViaId", "category", "mtBaseHashSet", "mtCategoryHashSet", "org", "parent", "material", "supplier", "TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct", "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct", "TPdCompositionViaParent"}, response);

    }

    /**
     * <AUTHOR>
     * @Date 2016/12/8 15:25
     * 在物料中编辑保存商品信息接口
     */
    @ResponseBody
    @RequestMapping("/updatePdBaseAndSuppler.do")
    public Map updatePdBaseAndSuppler(HttpServletRequest request, User user, HttpServletResponse response) throws IOException, ParseException {
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-mm-dd");
        
        System.out.println(user.getUserName());
        Integer oid = user.getOid();
        String pdBaseId = request.getParameter("pd_id");//商品id
        String kind = request.getParameter("save_mtKind");//物料所属的分类
        String kuInfo = request.getParameter("ku_info");//库存信息
        String qualityInfo = request.getParameter("quality_info");//质量信息
        String supplierList = request.getParameter("supplierList");//供应商名录
        String supplerDeleID = request.getParameter("supplerDeleID");//要删除的供货信息列表
        String firstCategory = request.getParameter("firstCategory");//所属一级分类id
        Integer pid = Integer.valueOf(request.getParameter("firstCategory"));//
        MtBase mtBase = new MtBase();
        List<SrmSupplier > mtSuppliers = new ArrayList<>();


        JSONArray sJson = JSONArray.fromObject(supplierList);
        List sList = JSONArray.toList(sJson);

        for (int i = 0; i < sList.size(); i++) {
            JSONObject jo = JSONObject.fromObject(sList.get(i));
            String codeName = jo.optString("codeName");
            String name = jo.optString("name");
            List<MtSupplierMaterial> ms = materielService.getMtSupplierMaterialByNameAndCodeName(oid, codeName, null);
            if (ms != null && ms.size() > 0) {
                SrmSupplier supplier = materielService.getSrmSupplierById(ms.get(0).getSupplier());
                if (!name.equals(supplier.getName())) {
                    Map map = new HashMap();
                    map.put("status", "0");
                    map.put("message", "代号-" + codeName + "-重复，请重新输入");
                    return map;
                }
            }
        }

        BigDecimal stock ;
        String stockString=request.getParameter("stock");
        if (StringUtils.isNotEmpty(stockString)){
            stock=new BigDecimal(stockString);
        }else{
            stock=new BigDecimal(0);
        }


        PdBase pdBase = productService.getById(Integer.valueOf(pdBaseId));

        List<PdMerchandise> pdMerchandises = productService.getByProductIdAndOid(pdBase.getId(), oid);

        for (PdMerchandise product : pdMerchandises) {
            List<MtStock> mtStocks = mtStockService.getMtStocksByOidAndProductId(oid, product.getId());

            if (mtStocks != null && mtStocks.size() > 0) {
                Map map = new HashMap();
                map.put("state", 3);
                map.put("message", "修改失败！已入库出库不能修改！！");
                return map;
            }
        }


        JSONObject jsonKuInfo = JSONObject.fromObject(kuInfo);
        JSONObject jsonQualityInfo = JSONObject.fromObject(qualityInfo);
        productService.updatePdBase(pdBase);
        Map<String, Object> map = new HashMap<>();

        //删除每个供货关系
        if (supplerDeleID != null) {
            JSONArray deleteSupplerInfo = JSONArray.fromObject(supplerDeleID);
            List di = JSONArray.toList(deleteSupplerInfo);
            if (di.size() > 0) {
                for (int i = 0; i < di.size(); i++) {

                    if (di.get(i) != null && !"".equals(di.get(i))) {
                        MtSupplierMaterial mtSupplierMaterial = materielService.getMtSupplierMaterialById((Integer) di.get(i));

                        if (mtSupplierMaterial != null) {
                            materielService.deleteMtSupplierMaterial(mtSupplierMaterial);
                        }
                    }

                }
            }
        }
        PdBase base = null;
        for (MtBase m : pdBase.getTMtBaseViaProduct()) {
            if (m != null) {
                mtBase = materielService.getMtBaseById(m.getId());

                MtCategory category = materielService.getMtCategoryById(m.getCategory_());//第一级分类
                MtCategory mtCategory = materielService.getMtCategoryById(Integer.parseInt(kind));//选中的分类
                if (category.getFirstGradeId() == Integer.decode(firstCategory)) {
                    mtBase.setCategory(mtCategory);
                }
                materielService.updateMtBase(mtBase);


                if (mtBase.getMtStockInfoHashSet().size() > 0) {
                    for (MtStockInfo mtStockInfo : mtBase.getMtStockInfoHashSet()) {
                        if (mtBase.getProduct_() != null) {
                            base = productService.getById(mtBase.getProduct_());
                            if (jsonKuInfo.has("minimumStock"))
                                base.setMinimumiStock(new BigDecimal(jsonKuInfo.optString("minimumStock")));
                            base.setCurrentStock(stock);
                            base.setAvailableStock(stock);
                            productService.updatePdBase(base);
                        }


                        if ("[]".equals(supplierList)) { //说明外购没有修改供应商
                            if (m.getName() != null && m.getCode() != null) {
                                //增加修改历史
                                MtStockInfoHistory mtSH = new MtStockInfoHistory();
                                mtSH.setStockInfo(mtStockInfo.getId());
                                if (mtBase.getProduct_() != null)
                                    mtSH.setMaterial(mtBase.getProduct_());
                                else mtSH.setMaterial(mtBase.getId());
                                mtSH.setInitialStock(mtStockInfo.getInitialStock());
                                mtSH.setAvailableStock(mtStockInfo.getAvailableStock());
                                mtSH.setMinimumStock(mtStockInfo.getMinimumStock());
                                mtSH.setStockPosition(mtStockInfo.getStockPosition());
                                mtSH.setStockRequirements(mtStockInfo.getStockRequirements());
                                mtSH.setCreator(user.getUserID());
                                mtSH.setCreateName(user.getUserName());
                                mtSH.setCreateDate(new Date());
                                mtSH.setProviderName(mtBase.getLastProviderName());
                                mtSH.setAfterInitialStock(stock);
                                mtSH.setAfterMinimumStock(mtStockInfo.getMinimumStock());
                                mtStockInfoHistoryService.saveMtStockInfoHistory(mtSH);
                            }
                        }


                        if (jsonKuInfo.optString("minimumStock") != null && !"".equals(jsonKuInfo.optString("minimumStock")))
                            mtStockInfo.setMinimumStock(new BigDecimal(jsonKuInfo.optString("minimumStock","0")));
                        if (stock != null) {
                            mtStockInfo.setStock(stock);
                            mtStockInfo.setCurrentStock(stock);
                            mtStockInfo.setAvailableStock(stock);
                            mtStockInfo.setInitialStock(stock);
                        }
                        mtStockInfo.setStockPosition(jsonKuInfo.optString("stockPosition"));
                        mtStockInfo.setStockRequirements(jsonKuInfo.optString("stockRequirements"));
                        mtStockInfo.setStock(stock);

                        materielService.updateMtStockInfo(mtStockInfo);  //更新库存信息
                    }
                } else {
                    MtStockInfo mtStockInfo = new MtStockInfo();


                    if (mtBase.getProduct_() != null) {
                        base = productService.getById(mtBase.getProduct_());
                        if (jsonKuInfo.has("minimumStock"))
                            base.setMinimumiStock(new BigDecimal(jsonKuInfo.optString("minimumStock","0")));
                        base.setCurrentStock(stock);
                        base.setAvailableStock(stock);
                        productService.updatePdBase(base);
                    }

                    if ("[]".equals(supplierList)) { //说明外购没有修改供应商
                        if (m.getName() != null && m.getCode() != null) {
                            //增加修改历史
                            MtStockInfoHistory mtSH = new MtStockInfoHistory();
                            mtSH.setStockInfo(mtStockInfo.getId());
                            if (mtBase.getProduct_() != null)
                                mtSH.setMaterial(mtBase.getProduct_());
                            else mtSH.setMaterial(mtBase.getId());
                            mtSH.setInitialStock(mtStockInfo.getInitialStock());
                            mtSH.setAvailableStock(mtStockInfo.getAvailableStock());
                            mtSH.setMinimumStock(mtStockInfo.getMinimumStock());
                            mtSH.setStockPosition(mtStockInfo.getStockPosition());
                            mtSH.setStockRequirements(mtStockInfo.getStockRequirements());
                            mtSH.setCreator(user.getUserID());
                            mtSH.setCreateName(user.getUserName());
                            mtSH.setCreateDate(new Date());
                            mtSH.setProviderName(mtBase.getLastProviderName());
                            mtSH.setAfterInitialStock(stock);
                            mtSH.setAfterMinimumStock(mtStockInfo.getMinimumStock());
                            mtStockInfoHistoryService.saveMtStockInfoHistory(mtSH);
                        }
                    }

                    if (jsonKuInfo.optString("minimumStock") != null && !"".equals(jsonKuInfo.optString("minimumStock")))
                        mtStockInfo.setMinimumStock(new BigDecimal(jsonKuInfo.optString("minimumStock","0")));
                    if (stock != null) {
                        mtStockInfo.setStock(stock);
                        mtStockInfo.setCurrentStock(stock);
                        mtStockInfo.setAvailableStock(stock);
                        mtStockInfo.setInitialStock(stock);
                    }
                    mtStockInfo.setStockPosition(jsonKuInfo.optString("stockPosition"));
                    mtStockInfo.setStockRequirements(jsonKuInfo.optString("stockRequirements"));
                    mtStockInfo.setStock(stock);
                    mtStockInfo.setMaterial(mtBase);

                    materielService.saveMtStockInfo(mtStockInfo);//新增库存信息
                }
                if (mtBase.getMtQualityMtBaseViaId().size() > 0) {
                    for (MtQuality mtQuality : mtBase.getMtQualityMtBaseViaId()) {
                        mtQuality.setExperimentalMethod(jsonQualityInfo.optString("experimentalMethod"));
                        mtQuality.setExpirationDate(jsonQualityInfo.optString("expirationDate"));
                        mtQuality.setInspectionStandard(jsonQualityInfo.optString("inspectionStandard"));
                        mtQuality.setInspectionOperation(jsonQualityInfo.optString("inspectionOperation"));
                        mtQuality.setInspectionInstructions(jsonQualityInfo.optString("inspectionInstructions"));
                        mtQuality.setProtocolSn(jsonQualityInfo.optString("protocolSn"));
                        mtQuality.setOuterRecord(jsonQualityInfo.optString("outerRecord"));
                        mtQuality.setSealedSamplesSn(jsonQualityInfo.optString("sealedSamplesSn"));
                        mtQuality.setMemo(jsonQualityInfo.optString("memo"));
                        materielService.updateMtQuality(mtQuality);// 更新质量信息
                    }
                } else {
                    MtQuality mtQuality = new MtQuality();
                    mtQuality.setExperimentalMethod(jsonQualityInfo.optString("experimentalMethod"));
                    mtQuality.setExpirationDate(jsonQualityInfo.optString("expirationDate"));
                    mtQuality.setInspectionStandard(jsonQualityInfo.optString("inspectionStandard"));
                    mtQuality.setInspectionOperation(jsonQualityInfo.optString("inspectionOperation"));
                    mtQuality.setInspectionInstructions(jsonQualityInfo.optString("inspectionInstructions"));
                    mtQuality.setProtocolSn(jsonQualityInfo.optString("protocolSn"));
                    mtQuality.setOuterRecord(jsonQualityInfo.optString("outerRecord"));
                    mtQuality.setSealedSamplesSn(jsonQualityInfo.optString("sealedSamplesSn"));
                    mtQuality.setMemo(jsonQualityInfo.optString("memo"));
                    mtQuality.setMaterial(mtBase);
                    materielService.saveMtQuality(mtQuality);//新增质量信息
                }

                JSONArray j = JSONArray.fromObject(supplierList);
                List s = JSONArray.toList(j);

                for (int i = 0; i < s.size(); i++) {
                    JSONObject jo = JSONObject.fromObject(j.get(i));
                    MtStockInfo mSInfo = mtStockService.getMtStockInfoByMId(mtBase.getId());
                    //如果供货关系id（sbId）存在，执行编辑供货关系
                    if (!"".equals(jo.optString("sbId")) && jo.optString("sbId") != null) {
                        MtSupplierMaterial sm = materielService.getMtSupplierMaterialByMIdANDSID(jo.optInt("sbId"), jo.optInt("id"));
                        if (!"".equals(jo.optString("minimumPurchase")) && jo.optString("minimumPurchase") != null)
                            sm.setMinimumPurchase(jo.optInt("minimumPurchase"));
                        if (!"".equals(jo.optString("perchaseCycle")) && jo.optString("perchaseCycle") != null)
                            sm.setPerchaseCycle(jo.optInt("perchaseCycle"));
                        if (!"".equals(jo.optString("unitPrice")) && jo.optString("unitPrice") != null)
                            sm.setUnitPrice(new BigDecimal(jo.optString("unitPrice")));
                        if (!"".equals(jo.optString("taxRate")) && jo.optString("taxRate") != null)
                            sm.setTaxRate(new BigDecimal(jo.optString("taxRate")));
                        if (!"".equals(jo.optString("unitPriceNotax")) && jo.optString("unitPriceNotax") != null)
                            sm.setUnitPriceNotax(new BigDecimal(jo.optString("unitPrice")));
                        if (!"".equals(jo.optString("contractSn")) && jo.optString("contractSn") != null)
                            sm.setContractSn(jo.optString("contractSn"));

                        MtStockInfoHistory mtSH = new MtStockInfoHistory();
                        mtSH.setInitialStock(sm.getInitialStock());
                        mtSH.setMinimumStock(sm.getMinimumStock());
                        if (mSInfo != null) {
                            mtSH.setStockInfo(mSInfo.getId());
                        }
                        mtSH.setMaterial(base.getId());
                        mtSH.setAfterInitialStock(new BigDecimal(jo.optString("initialStock")));
                        mtSH.setAfterMinimumStock(new BigDecimal(jo.optString("minimumStock")));
                        mtSH.setSupplier(sm.getSupplier());
                        mtSH.setCreator(user.getUserID());
                        mtSH.setCreateName(user.getUserName());
                        mtSH.setCreateDate(new Date());
                        if (mtStockInfoHistoryService.get(mtSH) == null)
                            mtStockInfoHistoryService.saveMtStockInfoHistory(mtSH);


                        sm.setPriceStable(jo.optString("priceStable"));
                        sm.setCodeName(jo.optString("codeName"));
                        sm.setDraftAcceptable(jo.optInt("draftAcceptable"));
                        sm.setHasContact(jo.optString("hasContact"));
                        sm.setValidDate(jo.optString("validDate").equals("") ? null : sd.parse(jo.optString("validDate")));
                        sm.setSignDate(jo.optString("signDate").equals("") ? null : sd.parse(jo.optString("signDate")));
                        sm.setInvoicable(jo.optString("invoicable"));

                        sm.setInvoiceCategory(StringUtils.isBlank(jo.optString("invoiceCategory"))?null:jo.optString("invoiceCategory"));

                        sm.setAtPar(StringUtils.isNotBlank(jo.optString("atPar"))?jo.optString("atPar"):null);
                        sm.setMinimumStock(new BigDecimal(jo.optString("minimumStock")));
                        sm.setInitialStock(new BigDecimal(jo.optString("initialStock")));
                        sm.setChargeBegin(jo.optString("chargeBegin",null));
                        sm.setChargePeriod(jo.optInt("chargePeriod"));
                        sm.setIsInclude(jo.optString("isInclude",null));
                        sm.setMaterialInvoicable(jo.optInt("materialInvoicable"));
                        sm.setMaterialInvoiceCategory(jo.optString("materialInvoiceCategory",null));
                        String materialTaxRate = jo.getString("materialTaxRate");
                        if (!"".equals(jo.getString("materialTaxRate")))
                            sm.setMaterialTaxRate(new BigDecimal(jo.getString("materialTaxRate")));
                        sm.setIsImprest(jo.optString("isImprest",null));
                        sm.setInclusiveFreight(jo.optInt("inclusiveFreight"));
                        //包装方式
                        sm.setPackageMethod(jo.optString("packageMethod",null));
                        sm.setIsParValue(jo.optString("isParValue",null));

                        materielService.updateMtSupplierMaterial(sm); //编辑供货关系

                        //添加供应商 库存历史


                    } else {
                        //否则执行新增供货关系
                        SrmSupplier supplier = new SrmSupplier ();
                        //如果供应商id为空，执行新增供应商
                        if ("".equals(jo.optString("id")) || jo.optString("id") == null) {
                            SrmSupplier mtSupplier = srmSupplierContact.getSrmSupplierByName(oid, jo.optString("name"));
                            if (mtSupplier != null) {
                                MtSupplierMaterial supplierMaterial = new MtSupplierMaterial();
                                if (!"".equals(jo.optString("minimumPurchase")) && jo.optString("minimumPurchase") != null)
                                    supplierMaterial.setMinimumPurchase(jo.optInt("minimumPurchase"));
                                if (!"".equals(jo.optString("perchaseCycle")) && jo.optString("perchaseCycle") != null)
                                    supplierMaterial.setPerchaseCycle(jo.optInt("perchaseCycle"));
                                if (!"".equals(jo.optString("unitPrice")) && jo.optString("unitPrice") != null)
                                    supplierMaterial.setUnitPrice(new BigDecimal(jo.optString("unitPrice")));
                                if (!"".equals(jo.optString("taxRate")) && jo.optString("taxRate") != null)
                                    supplierMaterial.setTaxRate(new BigDecimal(jo.optString("taxRate")));
                                if (!"".equals(jo.optString("unitPriceNotax")) && jo.optString("unitPriceNotax") != null)
                                    supplierMaterial.setUnitPriceNotax(new BigDecimal(jo.optString("unitPrice")));
                                if (!"".equals(jo.optString("contractSn")) && jo.optString("contractSn") != null)
                                    supplierMaterial.setContractSn(jo.optString("contractSn"));
                                supplierMaterial.setSupplier(mtSupplier.getId());
                                supplierMaterial.setMaterial(mtBase);
                                materielService.saveMtSupplierMaterial(supplierMaterial); //新增供货关系

                                //添加供应商 库存历史
                                MtStockInfoHistory mtSH = new MtStockInfoHistory();
                                if (mSInfo != null) {
                                    mtSH.setStockInfo(mSInfo.getId());
                                }
                                mtSH.setMaterial(base.getId());
                                mtSH.setInitialStock(new BigDecimal(0));
                                mtSH.setMinimumStock(new BigDecimal(0));
                                mtSH.setAfterInitialStock(new BigDecimal(jo.optString("initialStock")));
                                mtSH.setAfterMinimumStock(new BigDecimal(jo.optString("minimumStock")));
                                mtSH.setSupplier(supplier.getId());
                                mtSH.setCreator(user.getUserID());
                                mtSH.setCreateName(user.getUserName());
                                mtSH.setCreateDate(new Date());
                                if (mtStockInfoHistoryService.get(mtSH) == null)
                                    mtStockInfoHistoryService.saveMtStockInfoHistory(mtSH);
                            } else {
                                supplier.setName(jo.optString("name"));//没有则新增此供应商
                                supplier.setFullName(jo.optString("fullName"));
                                supplier.setCodeName(jo.optString("codeName"));
                                supplier.setOrg(oid);//存入机构id
                                supplier.setSupplyCount(0);
                                supplier.setExclusiveCount(0);
                                supplier.setCutCount(0);
                                materielService.saveSrmSupplier (supplier);

                                MtSupplierMaterial supplierMaterial = new MtSupplierMaterial();
                                if (!"".equals(jo.optString("minimumPurchase")) && jo.optString("minimumPurchase") != null)
                                    supplierMaterial.setMinimumPurchase(jo.optInt("minimumPurchase"));
                                if (!"".equals(jo.optString("perchaseCycle")) && jo.optString("perchaseCycle") != null)
                                    supplierMaterial.setPerchaseCycle(jo.optInt("perchaseCycle"));
                                if (!"".equals(jo.optString("unitPrice")) && jo.optString("unitPrice") != null)
                                    supplierMaterial.setUnitPrice(new BigDecimal(jo.optString("unitPrice")));
                                if (!"".equals(jo.optString("taxRate")) && jo.optString("taxRate") != null)
                                    supplierMaterial.setTaxRate(new BigDecimal(jo.optString("taxRate")));
                                if (!"".equals(jo.optString("unitPriceNotax")) && jo.optString("unitPriceNotax") != null)
                                    supplierMaterial.setUnitPriceNotax(new BigDecimal(jo.optString("unitPrice")));
                                if (!"".equals(jo.optString("contractSn")) && jo.optString("contractSn") != null)
                                    supplierMaterial.setContractSn(jo.optString("contractSn"));


                                supplierMaterial.setPriceStable(jo.optString("priceStable"));
                                supplierMaterial.setCodeName(jo.optString("codeName"));
                                supplierMaterial.setDraftAcceptable(jo.optInt("draftAcceptable"));
                                supplierMaterial.setChargeAcceptable(jo.optInt("chargeAcceptable"));
                                supplierMaterial.setHasContact(jo.optString("hasContact"));
                                supplierMaterial.setValidDate(jo.optString("validDate").equals("") ? null : sd.parse(jo.optString("validDate")));
                                supplierMaterial.setSignDate(jo.optString("signDate").equals("") ? null : sd.parse(jo.optString("signDate")));
                                supplierMaterial.setInvoicable(jo.optString("invoicable",null));
                                supplierMaterial.setInvoiceCategory(jo.optString("invoiceCategory",null));
                                supplierMaterial.setAtPar(StringUtils.isBlank(jo.optString("atPar"))?null:jo.optString("atPar"));
                                supplierMaterial.setMinimumStock(new BigDecimal(jo.optString("minimumStock")));
                                supplierMaterial.setInitialStock(new BigDecimal(jo.optString("initialStock")));
                                supplierMaterial.setChargeBegin(StringUtils.isBlank(jo.optString("chargeBegin"))?null:jo.optString("chargeBegin"));
                                supplierMaterial.setChargePeriod(jo.optInt("chargePeriod"));
                                supplierMaterial.setIsInclude(jo.optString("isInclude",null));
                                supplierMaterial.setMaterialInvoicable(jo.optInt("materialInvoicable"));
                                supplierMaterial.setMaterialInvoiceCategory(jo.optString("materialInvoiceCategory",null));
                                if (!jo.optString("materialTaxRate").equals(""))
                                    supplierMaterial.setMaterialTaxRate(new BigDecimal(jo.optString("materialTaxRate")));
                                supplierMaterial.setIsImprest(jo.optString("isImprest",null));
                                supplierMaterial.setInclusiveFreight(jo.optInt("inclusiveFreight"));
                                //包装方式
                                supplierMaterial.setPackageMethod(jo.optString("packageMethod",null));
                                supplierMaterial.setIsParValue(jo.optString("isParValue",null));
                                supplierMaterial.setSupplier(supplier.getId());
                                mtSuppliers.add(supplier);
                                supplierMaterial.setMaterial(mtBase);

                                //添加供应商 库存历史
                                MtStockInfoHistory mtSH = new MtStockInfoHistory();
                                if (mSInfo != null) {
                                    mtSH.setStockInfo(mSInfo.getId());
                                }
                                mtSH.setMaterial(base.getId());
                                mtSH.setInitialStock(new BigDecimal(0));
                                mtSH.setMinimumStock(new BigDecimal(0));
                                mtSH.setAfterInitialStock(new BigDecimal(jo.optString("initialStock")));
                                mtSH.setAfterMinimumStock(new BigDecimal(jo.optString("minimumStock")));
                                mtSH.setSupplier(supplier.getId());
                                mtSH.setCreator(user.getUserID());
                                mtSH.setCreateName(user.getUserName());
                                mtSH.setCreateDate(new Date());
                                if (mtStockInfoHistoryService.get(mtSH) == null)
                                    mtStockInfoHistoryService.saveMtStockInfoHistory(mtSH);


                                materielService.saveMtSupplierMaterial(supplierMaterial); //编辑供货关系
                            }
                        } else {
                            //否则 认为供应商是检索出来已经存在的，查出此供应商
                            supplier = srmSupplierContact.getSrmSupplier (Integer.decode(jo.optString("id")));//有则查出供应商
                            mtSuppliers.add(supplier);
                            MtSupplierMaterial supplierMaterial = new MtSupplierMaterial();
                            if (!"".equals(jo.optString("minimumPurchase")) && jo.optString("minimumPurchase") != null)
                                supplierMaterial.setMinimumPurchase(jo.optInt("minimumPurchase"));
                            if (!"".equals(jo.optString("perchaseCycle")) && jo.optString("perchaseCycle") != null)
                                supplierMaterial.setPerchaseCycle(jo.optInt("perchaseCycle"));
                            if (!"".equals(jo.optString("unitPrice")) && jo.optString("unitPrice") != null)
                                supplierMaterial.setUnitPrice(new BigDecimal(jo.optString("unitPrice")));
                            if (!"".equals(jo.optString("taxRate")) && jo.optString("taxRate") != null)
                                supplierMaterial.setTaxRate(new BigDecimal(jo.optString("taxRate")));
                            if (!"".equals(jo.optString("unitPriceNotax")) && jo.optString("unitPriceNotax") != null)
                                supplierMaterial.setUnitPriceNotax(new BigDecimal(jo.optString("unitPrice")));
                            if (!"".equals(jo.optString("contractSn")) && jo.optString("contractSn") != null)
                                supplierMaterial.setContractSn(jo.optString("contractSn"));

                            supplierMaterial.setPriceStable(jo.optString("priceStable"));
                            supplierMaterial.setCodeName(jo.optString("codeName"));
                            supplierMaterial.setDraftAcceptable(jo.optInt("draftAcceptable"));
                            supplierMaterial.setChargeAcceptable(jo.optInt("chargeAcceptable"));
                            supplierMaterial.setHasContact(jo.optString("hasContact"));
                            supplierMaterial.setValidDate(jo.optString("validDate").equals("") ? null : sd.parse(jo.optString("validDate")));
                            supplierMaterial.setSignDate(jo.optString("signDate").equals("") ? null : sd.parse(jo.optString("signDate")));
                            supplierMaterial.setInvoicable(jo.optString("invoicable",null));
                            supplierMaterial.setInvoiceCategory(jo.optString("invoiceCategory",null));
                            supplierMaterial.setAtPar(StringUtils.isBlank(jo.optString("atPar"))?null:jo.optString("atPar"));
                            supplierMaterial.setMinimumStock(new BigDecimal(jo.optString("minimumStock")));
                            supplierMaterial.setInitialStock(new BigDecimal(jo.optString("initialStock")));
                            supplierMaterial.setChargeBegin(StringUtils.isBlank(jo.optString("chargeBegin"))?null:jo.optString("chargeBegin"));
                            supplierMaterial.setChargePeriod(jo.optInt("chargePeriod"));
                            supplierMaterial.setIsInclude(StringUtils.isBlank(jo.optString("isInclude"))?null:jo.optString("isInclude"));
                            supplierMaterial.setMaterialInvoicable(jo.optInt("materialInvoicable"));
                            supplierMaterial.setMaterialInvoiceCategory(StringUtils.isBlank(jo.optString("materialInvoiceCategory"))?null:jo.optString("materialInvoiceCategory"));
                            if (!jo.optString("materialTaxRate").equals(""))
                                supplierMaterial.setMaterialTaxRate(new BigDecimal(jo.optString("materialTaxRate")));
                            supplierMaterial.setIsImprest(jo.optString("isImprest",null));
                            supplierMaterial.setInclusiveFreight(jo.optInt("inclusiveFreight"));
                            //包装方式
                            supplierMaterial.setPackageMethod(jo.optString("packageMethod",null));
                            supplierMaterial.setIsParValue(jo.optString("isParValue",null));
                            supplierMaterial.setSupplier(supplier.getId());
                            supplierMaterial.setMaterial(mtBase);

                            materielService.saveMtSupplierMaterial(supplierMaterial); //编辑供货关系

                            //添加供应商 库存历史
                            MtStockInfoHistory mtSH = new MtStockInfoHistory();
                            if (mSInfo != null) {
                                mtSH.setStockInfo(mSInfo.getId());
                            }
                            mtSH.setMaterial(base.getId());
                            mtSH.setInitialStock(new BigDecimal(0));
                            mtSH.setMinimumStock(new BigDecimal(0));
                            mtSH.setAfterInitialStock(new BigDecimal(jo.optString("initialStock")));
                            mtSH.setAfterMinimumStock(new BigDecimal(jo.optString("minimumStock")));
                            mtSH.setSupplier(supplier.getId());
                            mtSH.setCreator(user.getUserID());
                            mtSH.setCreateName(user.getUserName());
                            mtSH.setCreateDate(new Date());
                            if (mtStockInfoHistoryService.get(mtSH) == null)
                                mtStockInfoHistoryService.saveMtStockInfoHistory(mtSH);
                        }
                    }
                }

                BigDecimal minimumStock = new BigDecimal(0);
                for (MtStockInfo ms : mtBase.getMtStockInfoHashSet()) {
                    minimumStock =minimumStock.add( (ms.getMinimumStock() == null ? new BigDecimal(0):ms.getMinimumStock()));
                    mtBase.setStockPosition(ms.getStockPosition());//库位
                }
                mtBase.setMinimumStock(minimumStock);//库存
                mtBase.setCategoryName(mtBase.getCategory().getParent().getName());//分类名称
                map.put("status", 1);
                map.put("message", "修改成功！");
//            map.put("mtBase",mtBase);
            } else {
                map.put("status", 0);
                map.put("message", "修改失败！");
            }
        }

        new SubjectUtils().addSubject(user, mtSuppliers, materielService.getMtCategoryById(pid), "", subjectSelectService, settingService, oid, mtBase, null, userMessageService, userService, null, productService);

        return map;
    }


    /**
     * 物料类型修改、编辑  孙文
     */
    @ResponseBody
    @RequestMapping("/updatePdBaseAndType.do")
    public Map updatePdBaseAndType(HttpServletRequest request, User user, HttpServletResponse response) throws IOException {
        Integer oid = user.getOid();
        
        String mtBaseId = request.getParameter("mtId");//物料id
        String mtType = request.getParameter("mtType");//物料所属的分类
        BigDecimal initialStock = null;
        if (request.getParameter("initialStock") != null) {
            initialStock = new BigDecimal(request.getParameter("initialStock"));//最低库存
        }

        BigDecimal minimumStock = null;
        if (request.getParameter("minimumStock") != null) {
            minimumStock = new BigDecimal(request.getParameter("minimumStock"));//最低库存
        }
        String stockPosition = request.getParameter("stockPosition");//库位
        String stockRequirements = request.getParameter("stockRequirements");//库存要求
        //  String category=request.getParameter("category");//物料类型

        Map<String, Object> map = new HashMap<String, Object>();
        MtBase mtBase = materielService.getMtBaseById(Integer.valueOf(mtBaseId));

        MtStockInfoHistory mtSH = new MtStockInfoHistory();
        if (mtBase != null) {
            if (StringUtils.isNotEmpty(mtType)) {
                mtBase.setCategory_(Integer.valueOf(mtType));
            }
            MtStockInfo mtStockInfo = mtStockService.getMtStockInfoByMId(mtBase.getId());
            if (mtStockInfo != null) {

                BigDecimal thisInitialStock = mtStockInfo.getInitialStock(); //初始库存
                //修改库存为题
                if (thisInitialStock == null) {

                    mtSH.setStockInfo(mtStockInfo.getId());
                    mtSH.setMaterial(mtBase.getId());
                    mtSH.setInitialStock(mtStockInfo.getInitialStock());
                    mtSH.setCurrentStock(mtStockInfo.getCurrentStock());
                    mtSH.setAvailableStock(mtStockInfo.getAvailableStock());
                    mtSH.setMinimumStock(mtStockInfo.getMinimumStock());
                    mtSH.setStockPosition(mtStockInfo.getStockPosition());
                    mtSH.setStockRequirements(mtStockInfo.getStockRequirements());
                    mtSH.setCreator(user.getCreator());
                    mtSH.setCreateName(user.getCreateName());
                    mtSH.setCreateDate(new Date());
                    mtSH.setProviderName(mtBase.getLastProviderName());
                    mtSH.setAfterInitialStock(initialStock);
                    mtSH.setAfterMinimumStock(minimumStock);
                    mtStockInfoHistoryService.saveMtStockInfoHistory(mtSH);

                    if (mtStockInfo.getCurrentStock() == null) {
                        mtStockInfo.setCurrentStock(initialStock);
                    } else {
                        mtStockInfo.setCurrentStock(mtStockInfo.getCurrentStock().add(initialStock) );
                    }

                    if (mtStockInfo.getAvailableStock() == null) {
                        mtStockInfo.setAvailableStock(initialStock);
                    } else {
                        mtStockInfo.setAvailableStock(mtStockInfo.getAvailableStock() .add(initialStock) );
                    }

                    mtStockInfo.setInitialStock(initialStock);
                    //添加库存情况历史

                } else {
                    System.out.println(thisInitialStock.equals(initialStock));

                    if (initialStock != null && !thisInitialStock.equals(initialStock)) {//为null或跟上次一样是不修改

                        //添加库存情况历史
                        mtSH.setStockInfo(mtStockInfo.getId());
                        mtSH.setMaterial(mtBase.getId());
                        mtSH.setInitialStock(mtStockInfo.getInitialStock());
                        mtSH.setCurrentStock(mtStockInfo.getCurrentStock());
                        mtSH.setAvailableStock(mtStockInfo.getAvailableStock());
                        mtSH.setMinimumStock(mtStockInfo.getMinimumStock());
                        mtSH.setStockPosition(mtStockInfo.getStockPosition());
                        mtSH.setStockRequirements(mtStockInfo.getStockRequirements());
                        mtSH.setCreator(user.getCreator());
                        mtSH.setCreateName(user.getCreateName());
                        mtSH.setCreateDate(new Date());
                        mtSH.setProviderName(mtBase.getLastProviderName());
                        mtSH.setAfterInitialStock(initialStock);
                        mtSH.setAfterMinimumStock(minimumStock);


                        if (initialStock.compareTo(thisInitialStock) ==1 ) {//大于上次库存，当前 与可用应当加上差值
                            mtStockInfo.setCurrentStock(mtStockInfo.getCurrentStock() .add (initialStock.subtract(thisInitialStock) ));
                            mtStockInfo.setAvailableStock(mtStockInfo.getAvailableStock() .add (initialStock .subtract(thisInitialStock) ));
                            mtStockInfoHistoryService.saveMtStockInfoHistory(mtSH);
                        } else {//小与上次库存，当前 与可用 应当减去差值
                            mtStockInfo.setCurrentStock(mtStockInfo.getCurrentStock() .subtract (thisInitialStock .subtract(initialStock) ));
                            mtStockInfo.setAvailableStock(mtStockInfo.getAvailableStock() .subtract (thisInitialStock .subtract(initialStock) ));
                            mtStockInfoHistoryService.saveMtStockInfoHistory(mtSH);
                        }


                        mtStockInfo.setInitialStock(initialStock);
                    }
                }

                if (minimumStock != null && minimumStock.doubleValue() != 0 && mtStockInfo.getMinimumStock() != minimumStock) {
                    mtStockInfo.setMinimumStock(minimumStock);
                }
                //修改库存信息
                mtStockInfo.setStockPosition(stockPosition);
                mtStockInfo.setStockRequirements(stockRequirements);
                mtStockService.updateMtStockInfo(mtStockInfo);
            }

            //修改物料信息
            // mtBase.setCategory_(Integer.valueOf(category));
            materielService.updateMtBase(mtBase);
            map.put("struts", 1);
        } else {
            map.put("struts", 0);
        }

        return map;
    }

    /**
     * 最低、初始库存修改记录  孙文 state
     */
    @ResponseBody
    @RequestMapping("/modifyTheRecord.do")
    public void modifyTheRecord(String state, HttpServletRequest request, User user, HttpServletResponse response) throws IOException {
        String mtBaseId = request.getParameter("mtId");//物料id

        Map<String, Object> map = new HashMap<String, Object>();
        if (StringUtils.isNotEmpty(mtBaseId)) {
            //获取最低库存记录
            List<Map> minlist = mtStockInfoHistoryService.getMtSIHListByStockInfoIdForMap(Integer.valueOf(mtBaseId), "1", null);
            if (minlist != null) {
                map.put("minlist", minlist);
                map.put("struts", 1);
            }
            //获取初始库存记录
            List<Map> initialList = mtStockInfoHistoryService.getMtSIHListByStockInfoIdForMap(Integer.valueOf(mtBaseId), "2", null);
            if (initialList != null) {
                map.put("initialList", initialList);
                map.put("struts", 1);
            }
        } else {
            map.put("struts", 0);
        }

        ObjectToJson.objectToJson1(map, new String[]{"updateDate", "updateName", "updator", "creator", "currentStock", "lastInventoryTime", "material", "stockInfo", "stockPosition", "stockRequirements", "availableStock"}, response);
    }

    //改-zy
    @ResponseBody
    @RequestMapping("/modifyRecord.do")
    public void modifyRecord(String mtBaseId, Integer supplier, HttpServletResponse response) throws IOException {

        Map<String, Object> map = new HashMap<String, Object>();
        if (StringUtils.isNotEmpty(mtBaseId)) {
            //获取初始库存记录
            List<Map> initialList = mtStockInfoHistoryService.getMtSIHListByStockInfoIdForMap(Integer.valueOf(mtBaseId), "2", supplier);
            if (initialList != null) {
                map.put("initialList", initialList);
                map.put("struts", 1);
            }
        } else {
            map.put("struts", 0);
        }

        ObjectToJson.objectToJson1(map, new String[]{"updateDate", "updateName", "updator", "creator", "currentStock", "lastInventoryTime", "material", "stockInfo", "stockPosition", "stockRequirements", "availableStock"}, response);
    }


    //    @RequestBody List<MtSupplierMaterial> supplierList,@RequestBody MtBase base,@RequestBody MtSupplierMaterial material,@RequestBody MtQuality qualityInfo,
    //获取字符串转json
    //新增物料、供应商、供货关系、库存、质量信息等
    @ResponseBody
    @RequestMapping("/addBase.do")
    public void addBase(HttpServletRequest request, User user, HttpServletResponse response) throws IOException, ParseException {
        MtCategory mtCategory = null;
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd");
        
        Integer oid = user.getOid();
        String base = request.getParameter("mt_info");//基本信息
        String kind = request.getParameter("save_mtKind");//物料所属的分类
        String kuInfo = request.getParameter("ku_info");//库存信息
        String qualityInfo = request.getParameter("quality_info");//质量信息
        String supplierList = request.getParameter("supplierList");//供应商名录
        Integer pid = Integer.valueOf(request.getParameter("pid"));//供应商名录


        // 供应商
        JSONArray sJson = JSONArray.fromObject(supplierList);
        List sList = JSONArray.toList(sJson);

        for (int i = 0; i < sList.size(); i++) {
            JSONObject jo = JSONObject.fromObject(sList.get(i));
            String codeName = jo.optString("codeName");
            String name = jo.optString("name");
            List<MtSupplierMaterial> ms = materielService.getMtSupplierMaterialByNameAndCodeName(oid, codeName, null);
            if (ms != null && ms.size() > 0) {
                SrmSupplier supplier = materielService.getSrmSupplierById(ms.get(0).getSupplier());
                if (!name.equals(supplier.getName())) {
                    Map map = new HashMap();
                    map.put("status", "0");
                    map.put("message", "代号-" + codeName + "-重复，请重新输入");
                    ObjectToJson.objectToJson1(map, new String[]{"parent", "org", "mtBaseHashSet", "mtCategoryHashSet", "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet"}, response);
                    return;
                }
            }
        }

        List<SrmSupplier > mtSuppliers = new ArrayList<>();

        JSONObject jsonBase = JSONObject.fromObject(base);
        JSONObject jsonKuInfo = JSONObject.fromObject(kuInfo);
        JSONObject jsonQualityInfo = JSONObject.fromObject(qualityInfo);

        Map<String, Object> map = new HashMap<>();
        if (jsonBase.optString("name") != null && !"".equals(jsonBase.optString("name")) && kind != null && !"".equals(kind) && !"".equals(jsonBase.optString("code")) && jsonBase.optString("code") != null) {
            List<MtBase> mbs = materielService.getMtBaseByOidAndCode(oid, jsonBase.optString("code"));

            if (mbs.size() <= 0) {
                MtBase mtBase = new MtBase();
                mtBase.setName(jsonBase.optString("name"));
                mtBase.setCode(jsonBase.optString("code"));
                mtBase.setUnit(jsonBase.optString("unit"));
                if (jsonBase.optString("netWeight") != null && !"".equals(jsonBase.optString("netWeight")))
                    mtBase.setNetWeight(new BigDecimal(jsonBase.optString("netWeight")));
                mtBase.setSpecifications((String) jsonBase.get("specifications"));
                mtBase.setMemo((String) jsonBase.get("memo"));
                mtBase.setModel((String) jsonBase.get("model"));
                mtBase.setSource("2");
                mtBase.setCreateDate(new Date());
                mtBase.setCreator(user.getUserID());
                mtBase.setCreateName(user.getUserName());
                mtCategory = materielService.getMtCategoryById(Integer.parseInt(kind));
                mtBase.setCategory(mtCategory);
                materielService.saveMtBase(mtBase);

                MtStockInfo mtStockInfo = new MtStockInfo();
                if (jsonKuInfo.optString("minimumStock") != null && !"".equals(jsonKuInfo.optString("minimumStock")))
                    mtStockInfo.setMinimumStock(new BigDecimal(jsonKuInfo.optString("minimumStock")));
                if (jsonKuInfo.optString("stock") != null && !"".equals(jsonKuInfo.optString("stock"))) {
                    mtStockInfo.setStock(new BigDecimal(jsonKuInfo.optString("stock")));
                    mtStockInfo.setCurrentStock(new BigDecimal(jsonKuInfo.optString("stock")));
                    mtStockInfo.setAvailableStock(new BigDecimal(jsonKuInfo.optString("stock")));
                }
                mtStockInfo.setStockPosition(jsonKuInfo.optString("stockPosition"));
                mtStockInfo.setStockRequirements(jsonKuInfo.optString("stockRequirements"));
                mtStockInfo.setMaterial(mtBase);
                materielService.saveMtStockInfo(mtStockInfo);

                MtQuality mtQuality = new MtQuality();
                mtQuality.setExperimentalMethod(jsonQualityInfo.optString("experimentalMethod"));
                mtQuality.setExpirationDate(jsonQualityInfo.optString("expirationDate"));
                mtQuality.setInspectionStandard(jsonQualityInfo.optString("inspectionStandard"));
                mtQuality.setInspectionOperation(jsonQualityInfo.optString("inspectionOperation"));
                mtQuality.setInspectionInstructions(jsonQualityInfo.optString("inspectionInstructions"));
                mtQuality.setProtocolSn(jsonQualityInfo.optString("protocolSn"));
                mtQuality.setOuterRecord(jsonQualityInfo.optString("outerRecord"));
                mtQuality.setSealedSamplesSn(jsonQualityInfo.optString("sealedSamplesSn"));
                mtQuality.setMemo(jsonQualityInfo.optString("memo"));
                mtQuality.setMaterial(mtBase);
                materielService.saveMtQuality(mtQuality);

                JSONArray j = JSONArray.fromObject(supplierList);
                List s = JSONArray.toList(j);
                for (int i = 0; i < s.size(); i++) {
                    JSONObject jo = JSONObject.fromObject(j.get(i));
                    SrmSupplier supplier = new SrmSupplier ();

                    if ("".equals(jo.optString("id")) || jo.optString("id") == null) {
                        supplier.setName(jo.optString("name"));//没有则新增此供应商
                        supplier.setFullName(jo.optString("fullName"));//全称
                        supplier.setCodeName(jo.optString("codeName"));
                        supplier.setOrg(oid);//存入当前机构id
                        supplier.setSupplyCount(0);
                        supplier.setExclusiveCount(0);
                        supplier.setCutCount(0);
                        materielService.saveSrmSupplier (supplier);

                    } else {
                        supplier = srmSupplierContact.getSrmSupplier (Integer.decode(jo.optString("id")));//有则查出供应商
                    }
                    MtSupplierMaterial supplierMaterial = new MtSupplierMaterial();
                    //最低采购量
                    if (!"".equals(jo.optString("minimumPurchase")) && jo.optString("minimumPurchase") != null)
                        supplierMaterial.setMinimumPurchase(jo.optInt("minimumPurchase"));
                    if (!"".equals(jo.optString("perchaseCycle")) && jo.optString("perchaseCycle") != null)
                        supplierMaterial.setPerchaseCycle(jo.optInt("perchaseCycle"));
                    if (!"".equals(jo.optString("unitPrice")) && jo.optString("unitPrice") != null)
                        supplierMaterial.setUnitPrice(new BigDecimal(jo.optString("unitPrice")));
                    if (!"".equals(jo.optString("taxRate")) && jo.optString("taxRate") != null)
                        supplierMaterial.setTaxRate(new BigDecimal(jo.optString("taxRate")));
                    if (!"".equals(jo.optString("unitPriceNotax")) && jo.optString("unitPriceNotax") != null)
                        supplierMaterial.setUnitPriceNotax(new BigDecimal(jo.optString("unitPrice")));

                    //合同编号
                    if (!"".equals(jo.optString("contractSn")) && jo.optString("contractSn") != null)
                        supplierMaterial.setContractSn(jo.optString("contractSn",null));

                    //新字段

                    supplierMaterial.setPriceStable(jo.optString("priceStable"));
                    supplierMaterial.setCodeName(jo.optString("codeName"));
                    supplierMaterial.setDraftAcceptable(jo.optInt("draftAcceptable"));
                    // 是否有采购合同
                    supplierMaterial.setHasContact(jo.optString("hasContact"));
                    //合同有效期至
                    supplierMaterial.setValidDate(jo.optString("validDate").equals("") ? null : sd.parse(jo.optString("validDate")));
                    //签署日期
                    supplierMaterial.setSignDate(jo.optString("signDate").equals("") ? null : sd.parse(jo.optString("signDate")));
                    supplierMaterial.setInvoicable(jo.optString("invoicable",null));
                    supplierMaterial.setInvoiceCategory(jo.optString("invoiceCategory",null));
                    supplierMaterial.setIsTax(jo.optInt("isTax"));
                    //已约定单价
                    supplierMaterial.setAtPar(StringUtils.isBlank(jo.optString("atPar"))?null:jo.optString("atPar"));
                    supplierMaterial.setPayMethod(jo.optString("payMethod",null));
                    supplierMaterial.setMinimumStock(new BigDecimal(jo.optString("minimumStock")));
                    supplierMaterial.setInitialStock(new BigDecimal(jo.optString("initialStock")));
                    //挂帐开始类型:1-自入库之日起,2-自发票提交之日起
                    supplierMaterial.setChargeBegin(StringUtils.isBlank(jo.optString("chargeBegin"))?null:jo.optString("chargeBegin"));
                    //帐期(天)
                    supplierMaterial.setChargePeriod(jo.optInt("chargePeriod"));
                    //是否接收挂账
                    supplierMaterial.setChargeAcceptable(jo.optInt("chargeAcceptable"));
                    supplierMaterial.setIsInclude(jo.optString("isInclude",null));
                    //能否开发票
                    supplierMaterial.setMaterialInvoicable(jo.optInt("materialInvoicable"));
                    //本物料发票类型:1-增值税专票,2-普通发票',
                    String materialInvoiceCategory = jo.optString("materialInvoiceCategory",null);
                    if(StringUtils.isNotBlank(materialInvoiceCategory))
                        supplierMaterial.setMaterialInvoiceCategory(materialInvoiceCategory);
                    //税率
                    if (!jo.optString("materialTaxRate").equals(""))
                        supplierMaterial.setMaterialTaxRate(new BigDecimal(jo.optString("materialTaxRate")));

                    supplierMaterial.setInclusiveFreight(jo.optInt("inclusiveFreight"));
                    //是否需要预付款
                    supplierMaterial.setIsImprest(jo.optString("isImprest",null));

                    //包装方式
                    supplierMaterial.setPackageMethod(jo.optString("packageMethod",null));
                    supplierMaterial.setIsParValue(jo.optString("isParValue",null));
                    supplierMaterial.setSupplier(supplier.getId());
                    supplierMaterial.setMaterial(mtBase);

                    //materielService.saveMtSupplierMaterial(supplierMaterial);
                    srmSupplierContact.saveMtSupplierMaterial(supplierMaterial);

                    mtSuppliers.add(supplier);

                }
               BigDecimal minimumStock = new BigDecimal(0);
               BigDecimal currentStock = new BigDecimal(0);
                for (MtStockInfo ms : mtBase.getMtStockInfoHashSet()) {
                    minimumStock =minimumStock.add( ms.getMinimumStock());
                    currentStock =currentStock.add( ms.getCurrentStock());
                    mtBase.setStockPosition(ms.getStockPosition());//库位
                }
                mtBase.setMinimumStock(minimumStock);//库存
                mtBase.setCurrentStock(currentStock + "");//库存
                if (mtBase.getCategory().getParent() != null) {
                    mtBase.setCategoryName(mtBase.getCategory().getParent().getName());//分类名称
                } else {
                    mtBase.setCategoryName(mtBase.getCategory().getName());//分类名称
                }


                map.put("status", 1);
                new SubjectUtils().addSubject(user, mtSuppliers, materielService.getMtCategoryById(pid), "", subjectSelectService, settingService, oid, mtBase, null, userMessageService, userService, null, productService);
//            map.put("mtBase",mtBase);
            } else {
                map.put("status", 2);//代号已存在，不可重复录入
            }
        } else {
            map.put("status", 0);
        }


        ObjectToJson.objectToJson1(map, new String[]{"parent", "org", "mtBaseHashSet", "mtCategoryHashSet", "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet"}, response);

    }
    //获取供应商供应的材料
    @ResponseBody
    @RequestMapping("getSupplierMaterials")
    public Map getSupplierMaterials(Integer supplierId,Integer state,Integer enabled,Integer invoiceType,User user){
        return materielService.getSupplierMaterials(supplierId,state,enabled,invoiceType,user.getOid());
    }

    //新增顶点供应商
    @RequestMapping("locationSuppliers")
    @ResponseBody
    public Integer locationSuppliers(MtSupplierMaterial mtSupplierMaterial, Integer supplierMaterialId, Integer oid, Integer mtId, Integer supplierId, SrmSupplier supplier, User user,Integer operation,@RequestParam(defaultValue = "0") Integer init) throws ParseException {



        if(supplierMaterialId!=null){
            //新增时需要查重
            MtBase base = null;
            if(operation!=null){
                Map map = mtService.getSuppliersByMbId(mtId,"1");

                List<HashMap> list = (List) map.get("data");

                if(list!=null&&list.size()>0)
                    for (HashMap m :list){
                        if(supplierId.equals(m.get("supplier_id"))){
                            return 2;
                        }
                    }
            }

            if (supplierId == null) {
                supplier.setVatsPayable("2".equals(mtSupplierMaterial.getInvoiceCategory())?"0":"1");
                supplier.setCreateDate(new Date());
                supplier.setOrg(user.getOid());//存入当前机构id
                supplier.setCreateName(user.getUserName());
                supplier.setCreator(user.getUserID());
                supplier.setEnabled(1);
                supplier.setVersionNo(1);
                supplier.setSupplyCount(0);
                supplier.setExclusiveCount(0);
                supplier.setCutCount(0);
                supplier.setOperation("1");
                supplier.setType(1);
                materielService.saveSrmSupplier (supplier);

                srmSupplierHistoryDao.saveHis(supplier);


            } else {
                supplier = srmSupplierContact.getSrmSupplier (supplierId);//有则查出供应商
            }

            //添加供应商后需要增加科目
            List<SrmSupplier> suppliers = new ArrayList<>();
            suppliers.add(supplier);
            if (mtId != null) {
                base = materielService.getMtBaseById(mtId);


            mtSupplierMaterial.setMaterial(base);
            Integer supplierNumber = base.getSupplierNumber();
            if(operation!=null)
            base.setSupplierNumber(1 + (supplierNumber == null ? 0 : supplierNumber));
            base.setIsAppointed("1");
            materielService.updateMtBase(base);
            new SubjectUtils().addSubject(user,suppliers,base.getCategory(),"",subjectSelectService,settingService,oid,base,null,userMessageService,userService,null,productService);

            } else {//么有传材料id代表新增系统供应商
                MtBase m = new MtBase();
                m.setId(0);
                mtSupplierMaterial.setMaterial(m);
            }


            if (supplierMaterialId != null&&supplierMaterialId.intValue()!=0)
                mtSupplierMaterial.setId(supplierMaterialId);
            else
                mtSupplierMaterial.setId(null);
            mtSupplierMaterial.setEnabled("1");
            mtSupplierMaterial.setEnabledTime(new Date());
            mtSupplierMaterial.setCreateDate(new Date());
            mtSupplierMaterial.setCreateName(user.getUserName());
            mtSupplierMaterial.setCreator(user.getUserID());
            materielService.locationSuppliers(mtSupplierMaterial,supplierMaterialId,supplier,base, user);
            return 1;
        }else{
            //新增时需要查重
            MtBase base = null;
            if(operation!=null){
                Map map = mtService.getSuppliersByMbId(mtId,"1");

                List<HashMap> list = (List) map.get("data");

                if(list!=null&&list.size()>0)
                    for (HashMap m :list){
                        if(supplierId.equals(m.get("supplier_id"))){
                            return 2;
                        }
                    }
            }

            if (supplierId == null) {
                supplier.setVatsPayable("2".equals(mtSupplierMaterial.getInvoiceCategory())?"0":"1");
                supplier.setCreateDate(new Date());
                supplier.setOrg(user.getOid());//存入当前机构id
                supplier.setCreateName(user.getUserName());
                supplier.setCreator(user.getUserID());
                supplier.setEnabled(1);
                supplier.setVersionNo(1);
                supplier.setSupplyCount(0);
                supplier.setExclusiveCount(0);
                supplier.setCutCount(0);
                supplier.setOperation("1");
                supplier.setType(1);
                materielService.saveSrmSupplier (supplier);
                srmSupplierHistoryDao.saveHis(supplier);

            } else {
                supplier = srmSupplierContact.getSrmSupplier (supplierId);//有则查出供应商
            }

            //添加供应商后需要增加科目
            List<SrmSupplier> suppliers = new ArrayList<>();
            suppliers.add(supplier);
            if (mtId != null) {
                base = materielService.getMtBaseById(mtId);
                //新增审批

                JSONObject data=new JSONObject();
                data.put("mtSupplierMaterial",mtSupplierMaterial);
                data.put("supplierMaterialId",supplierMaterialId);
                data.put("mtId",mtId);
                data.put("supplier",supplier);
                data.put("operation",operation);
                data.put("supplierList",srmSupplierContact.getSupplierByMt(mtId));
                MtBaseHistory mtBase=new MtBaseHistory();
                BeanUtils.copyProperties(base,mtBase);
                data.put("base", mtBase);
                data.put("supplierId", supplierId);
                List<ApprovalProcess> apOl=approvalProcessService.getApprovalProcessByBusiness(mtId,61,"1");
                if(!apOl.isEmpty()){
                    return 3;
                }

                //user.getOid(), "purchaseApprovalSettings"
                ApprovalItem applyItem =roleService.getCurrentItem(user.getOid(), "purchaseApprovalSettings");

                //1.259初始化时不需要进行审批
                if(applyItem==null||applyItem.getStatus()==null||applyItem.getStatus()==0||init==1){


                    //处理
                    addMateriel( base,  mtSupplierMaterial, supplier, suppliers, operation,  supplierMaterialId, user);

                }else {
                    //获取第一个审批人
                    ApprovalFlow applyFlow = approvalService.getApprovalFlowByItemIdAndLevel(applyItem.getId(), 1);
                    User flowUser=userService.getUserByID(applyFlow.getToUserId());
                    ApprovalProcess ap = new ApprovalProcess();
                    ap.setBusiness(mtId);
                    ap.setLevel(1);
                    ap.setDescription("从"+supplier.getName()+"处购买"+base.getName());
                    ap.setBusinessType(61);
                    ap.setUserName(user.getUserName());//审批人
                    ap.setCreateDate(new Date());
                    ap.setToUser(flowUser.getUserID());
                    ap.setToUserName(flowUser.getUserName());
                    ap.setOrg(user.getOid());
                    ap.setFromUser(user.getUserID());
                    ap.setFromOrg(user.getOid());
                    ap.setApproveStatus("1");
                    ap.setApproveData(data.toString());
                    ap.setApproveMemo(ap.getCreateDate().getTime()+"");
                    approvalProcessService.saveApprovalProcess(ap);

                    HashMap<String,Object> hashMap=new HashMap<>();
                    hashMap.put("ap", ap);
                    hashMap.put("id",supplier.getId());

                    //申请人
                    swMessageService.rejectSend(0,1,hashMap,user.getUserID().toString(),"/addMaterielApplyList",null,null,user,"addMaterielApply");
                    //审批人
                    swMessageService.rejectSend(1,1,hashMap,flowUser.getUserID().toString(),"/addMaterielApprovalList","有一条申请待审批","在"+ supplier.getName()+"处购买"+mtBase.getName(),flowUser,"addMaterielApproval");

                }



            } else {//么有传材料id代表新增系统供应商
                MtBase m = new MtBase();
                m.setId(0);
                mtSupplierMaterial.setMaterial(m);
            }


            if (supplierMaterialId != null&&supplierMaterialId.intValue()!=0) {
                mtSupplierMaterial.setId(supplierMaterialId);
            }else{
                mtSupplierMaterial.setId(null);
            }

            mtSupplierMaterial.setEnabled("1");
            mtSupplierMaterial.setEnabledTime(new Date());
            mtSupplierMaterial.setCreateDate(new Date());
            mtSupplierMaterial.setCreateName(user.getUserName());
            mtSupplierMaterial.setCreator(user.getUserID());

            return 1;
        }

    }

    /**
     * 获取新增供应商申请列表
     */
    @RequestMapping("/addMaterielApplyList.do")
    @MessageMapping("/addMaterielApplyList")
    @ResponseBody
    public JsonResult addMaterielApplyList(User user) {
        Integer userId = user.getUserID(); //用户id
        List<ApprovalProcess> list = approvalProcessService.getApprovalProcessByFromUser(userId, "1", 61, null, null, "desc");
        clusterMessageSendingOperations.convertAndSendToUser(userId + "", "/addMaterielApplyList", null, null, null, null, JSON.toJSONString(list));
        return new JsonResult(1, list);
    }

    /**
     * 获取新增供应商待审批列表
     */
    @RequestMapping("/addMaterielApprovalList.do")
    @MessageMapping("/addMaterielApprovalList")
    @ResponseBody
    public JsonResult addMaterielApprovalList(User user) {
        Integer userId = user.getUserID(); //用户id

        List<ApprovalProcess> list = approvalProcessService.getApprovalProcessByBusinessType(userId, "1", 61, null, null, null, "desc");

        clusterMessageSendingOperations.convertAndSendToUser(userId + "", "/addMaterielApprovalList", null, null, null, null, JSON.toJSONString(list));
        return new JsonResult(1, list);
    }

    /**
     * 获取新增供应商已审批列表
     */
    @RequestMapping("/addMaterielApprovalAlreadyList.do")
    @MessageMapping("/addMaterielApprovalAlreadyList")
    @ResponseBody
    public JsonResult addMaterielApprovalAlreadyList(User user) {
        Integer userId = user.getUserID(); //用户id

        List<ApprovalProcess> list = approvalProcessService.getApprovalProcessByBusinessType(userId, "2", 61, null, null, null, "desc");
        List<ApprovalProcess> l = new ArrayList<>();
        for (ApprovalProcess ap:list) {
            List<ApprovalProcess> nums=approvalProcessService.getApprovalProcessByBusinessDesc(ap.getBusiness(),61,"1");
            for (ApprovalProcess ap2:nums) {
                if(ap.getApproveMemo().equals(ap2.getApproveMemo())){
                    l.add(ap);
                    break;
                }
            }
        }
        clusterMessageSendingOperations.convertAndSendToUser(userId + "", "/addMaterielApprovalAlreadyList", null, null, null, null, JSON.toJSONString(list));
        return new JsonResult(1, l);
    }
    /**
     * 新增供应商审批查看
     */
    @RequestMapping("/addMaterielDetails.do")
    @ResponseBody
    public Map<String, Object> addMaterielDetails(Integer approvalId) {
        ApprovalProcess approvalProcess = approvalProcessService.getApprovalProcessById(approvalId);
        List<ApprovalProcess> list = approvalProcessService.getApprovalProcessByBusiness(approvalProcess.getBusiness(),approvalProcess.getBusinessType(),null);
        List<Map<String,Object>> l=new ArrayList<>();
        for (ApprovalProcess a:list) {
            if(approvalProcess.getApproveMemo().equals(a.getApproveMemo())){
                 Map<String,Object> m=new HashMap<>();
                m.put("userName",a.getToUserName());
                m.put("userId",a.getToUser());
                m.put("createDate",a.getCreateDate());
                m.put("handleTime",a.getHandleTime());
                m.put("approveStatus",a.getApproveStatus());
                l.add(m);
            }
        }
        Map data = mtService.getLocationHisDetails(approvalProcess);
        data.put("approval", approvalProcess);
        data.put("auditList",l);
        data.put("code", 200);
        return data;
    }
    /**
     * 新增订单供应商审批
     */
    @RequestMapping("/addMaterielApproval.do")
    @MessageMapping("/addMaterielApproval")
    @ResponseBody
    public JsonResult locationSuppliersApproval(String json) {
     com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(json);
        Integer approvalId = jsonObject.getInteger("approvalId"); // 审批id
        String approveStatus = jsonObject.getString("approveStatus"); //审批结果
        String reason = jsonObject.getString("reason"); //审批原因


//        Integer approvalId = approvalId=17330;
//        String approveStatus =  approveStatus="2";
//        String reason = "";
        Map<String, Object> map = new HashMap<>();

        ApprovalProcess approvalProcess = approvalProcessService.getApprovalProcessById(approvalId);

        if (approvalProcess == null) {
            map.put("status", 0);
            map.put("msg", "操作失败");
            return new JsonResult(1, map);
        }
        if (!"1".equals(approvalProcess.getApproveStatus())) {
            map.put("status", 0);
            map.put("msg", "已审批，请勿重复操作");
            return new JsonResult(1, map);
        }
        approvalProcess.setApproveStatus(approveStatus);
        approvalProcess.setReason(reason);
        approvalProcess.setHandleTime(new Date());

        JSONObject obj=JSONObject.fromObject(approvalProcess.getApproveData());
        Integer mtId=obj.optInt("mtId");
        Integer operation=obj.optInt("operation");
        Integer supplierId=obj.optInt("supplierId");
        Integer supplierMaterialId=obj.optInt("supplierMaterialId");
        MtSupplierMaterial mtSupplierMaterial=  (MtSupplierMaterial) JSONObject.toBean(obj.getJSONObject("mtSupplierMaterial"),MtSupplierMaterial.class);
        SrmSupplier supplier= srmSupplierContact.getSrmSupplier (supplierId);
        List<SrmSupplier> suppliers = new ArrayList<>();
        suppliers.add(supplier);

        HashMap<String,Object> hashMap=new HashMap<>();
        hashMap.put("ap",approvalProcess);
        hashMap.put("status",1);
        User user=userService.getUserByID(approvalProcess.getFromUser());
        User thisUser=userService.getUserByID(approvalProcess.getToUser());
        MtBase base = materielService.getMtBaseById(mtId);
        //2批准 3驳回
        if ("2".equals(approvalProcess.getApproveStatus())) {

            //判断是否有下级审批
            //查下下一级审批人
            ApprovalItem applyItem = roleService.getCurrentItem(user.getOid(), "purchaseApprovalSettings");
            //获取下一个审批人
            ApprovalFlow applyFlow = approvalService.getApprovalFlowByItemIdAndLevel(applyItem.getId(), approvalProcess.getLevel()+1);
            //直接通过
            if(applyItem==null||"0".equals(applyItem.getStatus())||applyFlow==null){

                //处理
                addMateriel( base,  mtSupplierMaterial, supplier, suppliers, operation,  supplierMaterialId, user);

                //todo
                //通过之后，更新供应商表汇票字段
                //

                String s=approvalProcess.getDescription()+"的申请被批准了!";
                suspendMsgService.saveUserSuspendMsg(1,s,"操作时间 "+new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),approvalProcess.getFromUser(),"addMaterielApply",approvalProcess.getId());
                approvalProcessService.updateApprovalProcess(approvalProcess);
                swMessageService.rejectSend(0,-1,hashMap,user.getUserID().toString(),"/addMaterielApplyList",null,null,user,"addMaterielApply");
                swMessageService.rejectSend(-1,-1,hashMap,thisUser.getUserID().toString(),"/addMaterielApprovalList",null,null,thisUser,"addMaterielApproval");

                map.put("status","1");
                map.put("msg", "操作成功");
                return new JsonResult(1, map);
            }

            //下一个审批人
            User flowUser=userService.getUserByID(applyFlow.getToUserId());


            //传给下一个审批人
            ApprovalProcess approvalProcess1 = new ApprovalProcess();
            approvalProcess1.setLevel(approvalProcess.getLevel() + 1);

            approvalProcess1.setToUser(applyFlow.getToUserId());
            approvalProcess1.setToUserName(applyFlow.getUserName());//审批人总称
            approvalProcess1.setUserName(approvalProcess.getUserName());//审批人名称
            approvalProcess1.setFromOrg(approvalProcess.getFromOrg());
            approvalProcess1.setFromUser(approvalProcess.getFromUser());
            approvalProcess1.setCreateDate(new Date());
            approvalProcess1.setBusiness(approvalProcess.getBusiness());
            approvalProcess1.setBusinessType(approvalProcess.getBusinessType());

            approvalProcess1.setNewId(approvalProcess.getBusiness());
            approvalProcess1.setFromUser(user.getUserID());
            approvalProcess1.setAskName(user.getUserName());
            approvalProcess1.setDescription(approvalProcess.getDescription());
            approvalProcess1.setMessage(false);
            approvalProcess1.setOrg(user.getOid());
            approvalProcess1.setApproveStatus("1");
            approvalProcess1.setApproveData(approvalProcess.getApproveData());
            approvalProcess1.setApproveMemo(approvalProcess.getApproveMemo());
            approvalProcessService.saveApprovalProcess(approvalProcess1);

            approvalProcessService.updateApprovalProcess(approvalProcess);

            //减去当前申请人待处理
            swMessageService.rejectSend(-1,-1,hashMap,thisUser.getUserID().toString(),"/addMaterielApprovalList",null,null,thisUser,"addMaterielApproval");
            //给当前处理人已处理发送
            swMessageService.rejectSend(0,1,hashMap,thisUser.getUserID().toString(),"/addMaterielApprovalAlreadyList",null,null,thisUser,"addMaterielApproval");
            //下级审批人待处理发送
            HashMap<String, Object> hashMap1 = new HashMap<>();
            hashMap1.put("ap", approvalProcess1);
            hashMap1.put("status", 1);
            swMessageService.rejectSend(1,1,hashMap1,flowUser.getUserID().toString(),"/addMaterielApprovalList","有一条申请待审批","在"+ supplier.getName()+"处购买"+base.getName(),flowUser,"addMaterielApproval");


        }else{

            String s=approvalProcess.getDescription()+"的申请被驳回了!";
            suspendMsgService.saveUserSuspendMsg(1,s,"操作时间 "+new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),approvalProcess.getFromUser(),"addMaterielApply",approvalProcess.getId());
            approvalProcessService.updateApprovalProcess(approvalProcess);

            //把所有审批通过的驳回
            List<ApprovalProcess> approvalProcesses = approvalProcessService.getApprovalProcessByBusiness(approvalProcess.getBusiness(), approvalProcess.getBusinessType(), "2");
            for (ApprovalProcess a : approvalProcesses) {  //最终审批结束，将所有审批人的已批准数据减掉
                if(approvalProcess.getApproveMemo().equals(a.getApproveMemo())){
                    a.setApproveStatus("3");
                    a.setReason(reason);
                    approvalProcessService.updateApprovalProcess(a);
                    HashMap<String, Object> hp = new HashMap<>();
                    hp.put("ap", a);
                    hp.put("status", 1);
                    User approvalUser = userService.getUserByID(a.getToUser()); //推送人
                    swMessageService.rejectSend(0, -1, hp, a.getToUser().toString(), "/addMaterielApprovalAlreadyList", null, null, approvalUser,"addMaterielApproval");

                }
           }

            swMessageService.rejectSend(0,-1,hashMap,user.getUserID().toString(),"/addMaterielApplyList",null,null,user,"addMaterielApply");
            swMessageService.rejectSend(-1,-1,hashMap,thisUser.getUserID().toString(),"/addMaterielApprovalList",null,null,thisUser,"addMaterielApproval");


        }

        map.put("status","1");
        map.put("msg", "操作成功");
        return new JsonResult(1, map);

    }

    @RequestMapping("/getMtSupplierByMtId.do")
    @ResponseBody
    public void getMtSupplierByMtId(Integer id) {

        MtBase mtBase = materielService.getMtBaseById(id);


        mtBase.getMtSupplierMaterialHashSet();


    }


    @RequestMapping("/getMtAndSupplierByMid.do")
    @ResponseBody
    public void getMtAndSupplierByMid(Integer messageId, HttpServletResponse response) throws IOException {
        UserMessage message = userMessageService.getUserMessageById(messageId);

        Integer mtid = message.getMtStockId();
        Integer suid = message.getMessageId();


        MtBase mtBase = materielService.getMtBaseById(mtid);

        SrmSupplier mtSupplier = srmSupplierContact.getSrmSupplier (suid);


        if (mtBase == null || mtSupplier == null) {
            message.setState(2);
            userMessageService.updateUserMassage(message);
        }
        Map map = new HashMap();

        map.put("mtBase", mtBase);
        map.put("mtSupplier", mtSupplier);

        ObjectToJson.objectToJson1(map, new String[]{"parent", "category", "org", "mtBaseHashSet", "mtCategoryHashSet",
                "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet",
                "pdCompositionMaterialHashSet", "pdPackHashSet", "mtSupplierContactHashSet"}, response);

    }


    @RequestMapping("/generate.do")
    @ResponseBody
    public void getAllPdBaseAndMtBase(User user) {
        materielService.getAllPdBaseAndMtBase(user.getOid(), user,null, settingService, userService, userMessageService);

    }


    private void addMateriel(MtBase base, MtSupplierMaterial mtSupplierMaterial,SrmSupplier supplier,List<SrmSupplier> suppliers,Integer operation, Integer supplierMaterialId,User user){
        mtSupplierMaterial.setMaterial(base);
        Integer supplierNumber = base.getSupplierNumber();
        if(operation!=null)
            base.setSupplierNumber(1 + (supplierNumber == null ? 0 : supplierNumber));
        base.setIsAppointed("1");
        materielService.updateMtBase(base);
        new SubjectUtils().addSubject(user,suppliers,base.getCategory(),"",subjectSelectService,settingService,user.getOid(),base,null,userMessageService,userService,null,productService);

        if (supplierMaterialId != null&&supplierMaterialId.intValue()!=0) {
            mtSupplierMaterial.setId(supplierMaterialId);
        }else {
            mtSupplierMaterial.setId(null);
        }
        mtSupplierMaterial.setEnabled("1");
        mtSupplierMaterial.setEnabledTime(new Date());
        mtSupplierMaterial.setCreateDate(new Date());
        mtSupplierMaterial.setCreateName(user.getUserName());
        mtSupplierMaterial.setCreator(user.getUserID());


        materielService.locationSuppliers(mtSupplierMaterial,supplierMaterialId,supplier,base, user);

        //批准后维护供应商的相关字段
        if(StringUtils.isNotBlank(mtSupplierMaterial.getInvoicable())){
            if(mtSupplierMaterial.getInvoicable().equals("1")){//可以开发票
                if("1".equals(mtSupplierMaterial.getMaterialInvoiceCategory())){//开增值税票
                    supplier.setVatsCount(supplier.getVatsCount()==null?1:supplier.getVatsCount()+1);
                }else if("2".equals(mtSupplierMaterial.getMaterialInvoiceCategory())){//开其他发票
                    supplier.setOtherInvoiceCount(supplier.getOtherInvoiceCount()==null?1:supplier.getOtherInvoiceCount()+1);
                }else {//不开或不确定
                    supplier.setNotInvoiceCount(supplier.getNotInvoiceCount()==null?1:supplier.getNotInvoiceCount()+1);
                }//不给开
            }else if("0".equals(mtSupplierMaterial.getInvoicable())){
                supplier.setNotInvoiceCount(supplier.getNotInvoiceCount()==null?1:supplier.getNotInvoiceCount()+1);
            }
        }else{
            supplier.setNotInvoiceCount(supplier.getNotInvoiceCount()==null?1:supplier.getNotInvoiceCount()+1);
        }
        supplier.setSupplyCount(supplier.getSupplyCount()==null?1:supplier.getSupplyCount()+1);//供应中的材料数+1  暂停采购时-1

        materielService.updateSrmSupplier(supplier);

        //维护汇票元素
        //获取该供应商的所有供应关系
        int invoiceAble = 0;
        supplier.setDraftAcceptable(null);//正常啥也不显示
        if(supplier.getDraftAcceptable()==null||supplier.getDraftAcceptable()==2){
            List<MtSupplierMaterial> mtSupplierMaterials = materielService.getListBySupplierId(supplier.getId());
            for (MtSupplierMaterial supplierMaterial : mtSupplierMaterials) {
                if("1".equals(supplierMaterial.getInvoicable())){//可以开发票
                    if("1".equals(mtSupplierMaterial.getMaterialInvoiceCategory())){//开增值税票
                        supplier.setInvoicable("1");
                    }else if("2".equals(mtSupplierMaterial.getMaterialInvoiceCategory())){//开其他发票
                        supplier.setInvoicable("1");
                    }else {//不开或不确定
                        supplier.setInvoicable("2");
                    }//不给开
                }else if("0".equals(mtSupplierMaterial.getInvoicable())){
                    supplier.setInvoicable("0");
                }
            }
        }

        Integer draftState = mtService.draftState(supplier.getId());
        supplier.setDraftAcceptable(draftState);

        materielService.updateSrmSupplier(supplier);
    }

}
