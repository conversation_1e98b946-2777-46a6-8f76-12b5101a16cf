package cn.sphd.miners.modules.investigate.dto;

import cn.sphd.miners.modules.investigate.entity.InvestigateQr;

import java.util.Date;
import java.util.List;

public class RespInvestigateQrList {
    private Date createTime;
    private Date endTime;
    private Integer sum;
    private List<InvestigateQr> investigateQrs;

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getSum() {
        return sum;
    }

    public void setSum(Integer sum) {
        this.sum = sum;
    }

    public List<InvestigateQr> getInvestigateQrs() {
        return investigateQrs;
    }

    public void setInvestigateQrs(List<InvestigateQr> investigateQrs) {
        this.investigateQrs = investigateQrs;
    }
}
