package cn.sphd.miners.modules.generalAffairs.service;

import cn.sphd.miners.modules.generalAffairs.entity.PersonnelLeaveType;

import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2021/4/29.
 */
public interface LeaveTypeService {

    List<PersonnelLeaveType> getAllLeaveType(Integer oid,Integer enabled);

    List<PersonnelLeaveType> getLeaveTypes(Integer oid,Integer enabled);

    Map<String,Object> pauseResumeUse(Integer leaveTypeId,Integer enabled,Integer userId);

    Map<String,Object> deleteLeaveType(Integer leaveTypeId);

    Map<String,Object> addLeaveType(String name,Integer userId);
}
