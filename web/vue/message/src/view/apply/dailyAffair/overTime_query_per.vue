<template>
  <div id="applyOverTimeQueryPer">
    <TY_NavTop title="查询"></TY_NavTop>
    <div class="container">
      <div v-if="type !== 4">
        <div class="ui-message">
          <div class="tip tip-success">从 {{getParams.beginDate}} 至 {{getParams.endDate}}</div>
          <div class="sendContainer overTimeQuery">
            <div v-if="type === 1">
              <div class="allTime">
                <h3>{{allCountsTime}}</h3>h
                <div class="dec">加班总时长</div>
              </div>
              <div class="splitTime">
                <a v-on:click="tab('week')">
                  <h4>{{weekCountsTime}}</h4>h
                  <span class="dec">六日加班总时长</span>
                </a>
                <a v-on:click="tab('daily')">
                  <h4>{{dailyCountsTime}}</h4>h
                  <span class="dec">平时加班总时长</span>
                </a>
              </div>
            </div>
            <div v-if="type == 2">
              未被认可的加班共{{allCountsNum}}次
            </div>
            <div v-if="type == 3">
              提交了加班申请，但实际未加班的共{{allCountsNum}}次
            </div>
            <div v-if="type == 5">
              加班申请被驳回共{{allCountsNum}}次
            </div>
          </div>
        </div>
        <div id="monthDetail" v-if="!haveDetail">
          <div v-if="type == 1">
            <el-row class="tab-title">
              <el-col :span="7">月份</el-col>
              <el-col :span="7">加班总时长</el-col>
              <el-col :span="5">平时加班</el-col>
              <el-col :span="5">周末加班</el-col>
            </el-row>
            <div class="ui-cells_none">
              <a class="ui-cell" v-for="(item, index) in hashMapList" v-bind:key="index" v-show="item.pingDurations !== 0 || item.jiaDurations !== 0">
                <div class="ui-cell__bd">
                  <div class="ui-cell_con">
                    <el-row>
                      <el-col :span="7">{{item.beginDate | formatDay('YYYY年MM月')}}</el-col>
                      <el-col :span="7">{{item.pingDurations + item.jiaDurations}}</el-col>
                      <el-col :span="5">{{item.pingDurations}}</el-col>
                      <el-col :span="5">{{item.jiaDurations}}</el-col>
                    </el-row>
                  </div>
                </div>
              </a>
            </div>
          </div>
          <div v-if="type != 1">
            <div class="ui-cells">
              <a class="ui-cell" v-for="(item, index) in hashMapList" v-bind:key="index" v-show="item.count > 0">
                <div class="ui-cell__bd">
                  <div class="ui-cell_con">
                    <el-row>
                      <el-col :span="12">{{item.beginDate | formatDay('YYYY年MM月')}}</el-col>
                      <el-col :span="12">{{item.count}}次</el-col>
                    </el-row>
                  </div>
                </div>
              </a>
            </div>
          </div>
        </div>
        <div v-if="haveDetail">
          <div class="tab-title">
            <div>{{name}}清单</div>
          </div>
          <div class="ui-cells" style="height: 348px; padding-top: 0">
            <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in weekday.overtimeList" v-bind:key="index" v-show="name !== '六日加班'">
              <div class="ui-cell__bd">
                <div class="ui-cell_con">
                  <div>{{item.beginTime | formatDay('YYYY年MM月DD日 dddd')}}</div>
                  <div v-if="Number(getParams.type) === 1">批准时长  {{item.approveDuration}}h</div>
                  <div v-if="Number(getParams.type) === 2">申报时长  {{item.actualDuration}}h</div>
                  <div v-if="Number(getParams.type) > 2">计划时长  {{item.duration}}h</div>
                </div>
              </div>
              <div class="ui-ti" v-show="item.kind === 2">补</div>
              <div class="ui-cell__ft"></div>
            </a>
            <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in holiday.overtimeList" v-bind:key="index" v-show="name !== '平时加班'">
              <div class="ui-cell__bd">
                <div class="ui-cell_con">
                  <div>{{item.beginTime | formatDay('YYYY年MM月DD日 dddd')}}</div>
                  <div v-if="Number(getParams.type) === 1">批准时长  {{item.approveDuration}}h</div>
                  <div v-if="Number(getParams.type) === 2">申报时长  {{item.actualDuration}}h</div>
                  <div v-if="Number(getParams.type) > 2">计划时长  {{item.duration}}h</div>
                </div>
              </div>
              <div class="ui-ti" v-show="item.kind === 2">补</div>
              <div class="ui-cell__ft"></div>
            </a>
          </div>
        </div>
      </div>
      <div v-else>
        <div class="tip">符合查询条件的数据共如下{{allMap.overtimeList.length}}条</div>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in allMap.overtimeList" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.beginTime | formatDay('YYYY年MM月DD日 dddd')}}</div>
                <div>计划时长  {{item.duration}}h</div>
              </div>
            </div>
            <div class="ui-ti" v-show="item.kind === 2">补</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'applyOverTimeQueryPer',
  data () {
    return {
      activeKey: '1',
      queryDetail: {},
      type: 0,
      state: '',
      haveDetail: true,
      hashMapList: [],
      weekday: {
        overtimeList: []
      },
      holiday: {
        overtimeList: []
      },
      allMap: {
        overtimeList: []
      },
      getParams: {},
      name: '加班'
    }
  },
  computed: {
    allCountsNum: function () {
      let counts = 0
      if (this.haveDetail) {
        console.log(this.weekday.count)
        counts = this.weekday.count + this.holiday.count
      } else {
        counts = this.queryDetail.sumCounts
      }
      return counts
    },
    allCountsTime: function () {
      let counts = 0
      if (this.haveDetail) {
        counts = this.weekday.sum + this.holiday.sum
      } else {
        counts = this.queryDetail.sumPingDurations + this.queryDetail.sumJiaDurations
      }
      return counts
    },
    dailyCountsTime: function () {
      let counts = 0
      if (this.weekday.sum) {
        counts = this.weekday.sum
      } else if (this.queryDetail.sumPingDurations) {
        counts = this.queryDetail.sumPingDurations
      }
      return counts
    },
    weekCountsTime: function () {
      let counts = 0
      if (this.holiday.sum) {
        counts = this.holiday.sum
      } else if (this.queryDetail.sumJiaDurations) {
        counts = this.queryDetail.sumJiaDurations
      }
      return counts
    }
  },
  created: function () {
    this.loading = true
    let paramQuery = this.$route.params.data
    if (!paramQuery) {
      paramQuery = JSON.parse(localStorage.getItem('query'))
    }
    this.getParams = paramQuery
    // state （1- 自定义 ，2 -仅看年报，3- 仅看月报）
    // type 1-已完成的加班 2-未被认可的加班 3-实际未加班 4-加班申请被驳回
    this.type = paramQuery.type
    this.state = paramQuery.state
    let _this = this
    this.$http.post('../../../leaveAndOutTime/myOverTimeQuery.do', paramQuery, {
      emulateJSON: true
    }).then((response) => {
      this.loading = false
      let data = response.body.data
      if (data) {
        console.log(JSON.stringify(data))
        this.queryDetail = data
        let haveDetail = data.haveDetail
        _this.haveDetail = haveDetail
        console.log(_this.haveDetail)
        if (haveDetail) {
          _this.allMap = data.allMap
          _this.weekday = data.weekday
          _this.holiday = data.holiday
        } else {
          _this.hashMapList = data.hashMapList
        }
      } else {
        console.log('加载失败！')
      }
    }).catch(function () {
      // this.jumpError('系统错误，请重试！')
    })
  },
  methods: {
    jump: function (id) {
      this.$router.push({
        path: `/applyOutTimeDetail/${id}`
      })
    },
    tab: function (type) {
      if (type === 'week') {
        this.name = '六日加班'
      } else if (type === 'daily') {
        this.name = '平时加班'
      }
    }
  }
}
</script>
