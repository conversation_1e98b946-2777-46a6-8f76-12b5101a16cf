<template>
  <Pageheader :title="title" :showBack="true"></Pageheader>
  <div class="container_main">
    <div v-if="params.code === 'accounting' && params.type === 'add'">
      <el-row>
        <el-col :span="20" :offset="2">
          是否由客必盛代理会计事务
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="20" :offset="2">
          <el-radio-group v-model="agentType">
            <el-radio :value="1">是</el-radio>
            <el-radio :value="0">否</el-radio>
          </el-radio-group>
          <el-divider />
        </el-col>
      </el-row>
    </div>
    <el-row v-show="!(params.code === 'accounting' && agentType === 1)">
      <el-col :span="20" :offset="2">
        <el-form
            label-position="top"
            size="large"
        >
          <el-form-item prop="user">
            <template #label>
              <span v-if="params.code === 'general'">请在在册职工中选择总务权限分配者</span>
              <span v-if="params.code === 'finance'">请在在册职工中选择财务权限分配者</span>
              <span v-if="params.code === 'sale'">请在在册职工中选择销售权限分配者</span>
              <span v-if="params.code === 'accounting'">请在在册职工中选择会计权限分配者</span>
              <span v-if="params.code === 'smallSuper'">请在在册职工中选择全权负责人</span>
            </template>
            <el-select v-model="passiveUser" placeholder="请选择">
              <el-option
                  v-for="item in option_users"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
              />
            </el-select>
            <el-text type="primary" size="normal" v-if="params.code === 'general'">注：负责分配除销售与财会外的权限</el-text>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <el-row v-show="params.code === 'accounting' && agentType === 1">
      <el-col :span="20" :offset="2">
        请输入客必盛业务人员信息
        <el-form
            label-position="top"
            size="large"
        >
          <el-form-item prop="userName" label="姓名">
            <el-input v-model="agentForm.userName" placeholder="请录入"></el-input>
          </el-form-item>
          <el-form-item prop="mobile" label="手机">
            <el-input v-model="agentForm.mobile" placeholder="请录入"></el-input>
          </el-form-item>
          <el-text type="primary" v-show="type === 'change' || type === 'reset'">注：所录入号码需能接收短信验证码！</el-text>
        </el-form>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="20" :offset="2" style="text-align: center">
        <el-button type="primary" size="large" @click="chooseManager_submit">确定</el-button>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import * as api from "@/api/centralControl";
import auth from '@/sys/auth'
export default {
  data() {
    return {
      title: '',
      type: 'add',
      params: {
        initState: '', // 是否全部完成
        code: '', // 编辑的高管code
        userInfo: { // 传过来的用户
          userName: '',
          mobile: ''
        }
      },
      agentType: 1,
      isShowRadioAgent: true,
      agentForm: {
        userName: '',
        mobile: ''
      },
      passiveUser: '',
      option_users: []
    }
  },
  created() {
    this.title = auth.getUser().roleCode === 'super'?'核心人物管控':'高管管理'
    this.params = {
      initState: this.$store.getters.getManage, // 是否已经全部完成
      code: this.$store.getters.getManageCode, // 要编辑的高管code
      userInfo: this.$store.getters.getManageParam, // 用户信息
      type: this.$route.params.type // 增加还是修改
    }
    this.isShowRadioAgent = !this.params.userInfo
    if (this.params.type === 'add') {
      this.agentType = 1
    } else if (this.params.type === 'change') {
      this.agentType = Number(this.params.userInfo.agentType)
    } else if (this.params.type === 'changeAgent') {
      this.agentType = 1-this.params.userInfo.agentType
    }

    console.log('params', this.params)

    let code = this.params.code
    let promise = api.getSelectStaffUsers()
    promise.then(res => {
          let data = res.data.data
          console.log('data:::' , data)
          let filterData = data.filter(item => !(item.roleCode === 'super' && code === 'smallSuper'))
          let roleArr = filterData.map(item => {
            return {
              value: item.userID,
              label: (item.userName + ' ' + item.mobile),
              userName: item.userName,
              mobile: item.mobile
            }
          })
          this.option_users = roleArr
          console.log('option_users', this.option_users)
        })
  },
  methods: {
    jump: function (path, query) {
      this.$router.push({
        path: path
      })
    },
    chooseManager_submit() {
      let type = this.params.type
      let code = this.params.code
      let passiveUserId = this.passiveUser
      let data = {}

      if (!passiveUserId && !(this.params.code === 'accounting' && this.agentType === 1)) {
        this.$message({
          type: 'error',
          message: '请选择人员！'
        })
        return false
      }

      console.log('type', type)
      if (type === 'add') {
        if (code === 'smallSuper') {
          api.addOrgSmallSuper(passiveUserId)
          .then(res => {
            let status = res.data.data.status
            if (status === 1) {
              this.$message({
                type: 'success',
                message: '操作成功！'
              })
              this.$router.go(-1)
            } else if (status === 2) {
              this.$message({
                type: 'error',
                message: '登录人不是董事长,操作失败！'
              })
            } else if (status === 3) {
              this.$message({
                type: 'error',
                message: '手机号已存在！'
              })
            } else {
              this.$message({
                type: 'error',
                message: '新增失败！'
              })
            }
          })
        } else if (code === 'accounting') {
          let agentType = this.agentType
          if (agentType === 0) {
            data = {
              agentType: agentType,
              passiveUserId: passiveUserId
            }
          } else {
            data = {
              agentType: agentType,
              userName: this.agentForm.userName,
              phone: this.agentForm.mobile
            }
            if (!this.testMobile(data.phone)) {
              this.$message({
                type: 'error',
                message: '请输入正确的手机号！'
              })
              return false
            }
          }
          api.addAccounting(data)
            .then(res => {
              let status = res.data.status
              if (status === 1) {
                this.$message({
                  type: 'success',
                  message: '操作成功！'
                })
                this.$router.go(-1)
              } else if (status === 2 || status === 3) {
                this.$message({
                  type: 'error',
                  message: '手机号已存在！'
                })
              } else {
                this.$message({
                  type: 'error',
                  message: '新增失败！'
                })
              }
          })
        } else {
          api.addManager(code, passiveUserId)
            .then(res => {
              let status = res.data.status
              if (status === 1) {
                this.$message({
                  type: 'success',
                  message: '操作成功！'
                })
                this.$router.go(-1)
              } else if (status === 2 || status === 3) {
                this.$message({
                  type: 'error',
                  message: '手机号已存在！'
                })
              } else {
                this.$message({
                  type: 'error',
                  message: '新增失败！'
                })
              }
            })
        }
      } else if (type === 'change') {
        if (code === 'smallSuper') {
          api.updateOrgSmallSuper(this.params.userInfo.userID, this.passiveUser)
          .then(res => {
            let data = res.data.data.status
            if (data === 1) {
              this.$message({
                type: 'success',
                message: '换为他人成功！'
              })
              this.$router.go(-2)
            } else {
              this.$message({
                type: 'success',
                message: '换为他人失败！'
              })
            }
          })
        } else if (code === 'accounting' && this.agentType === 1) {
          if (this.agentType === 1) {
            let data = {
              manageId: this.params.userInfo.userID,
              userName: this.agentForm.userName,
              phone: this.agentForm.mobile
            }
            if (!this.testMobile(this.agentForm.mobile)) {
              this.$message({
                type: 'error',
                message: '请输入正确的手机号！'
              })
              return false
            }
            api.updateAccounting(data)
            .then(res => {
              let data = res.data.data
              if (data === 1) {
                this.$message({
                  type: 'success',
                  message: '换为他人成功！'
                })
                this.$router.go(-2)
              } else {
                this.$message({
                  type: 'error',
                  message: '换为他人失败！'
                })
              }
            })
          }

        } else {
          api.updateManager(this.params.userInfo.userID, this.passiveUser)
          .then(res => {
            let data = res.data.data
            if (data === 1) {
              this.$message({
                type: 'success',
                message: '换为他人成功！'
              })
              this.$router.go(-2)
            } else {
              this.$message({
                type: 'success',
                message: '换为他人失败！'
              })
            }
          })
        }
      } else if (type === 'changeAgent') {
        let data = {}
        if (this.agentType === 1) {
          data = {
            userId: this.params.userInfo.userID,
            agentType: this.agentType,
            userName: this.agentForm.userName,
            phone: this.agentForm.mobile
          }
          if (!this.testMobile(this.agentForm.mobile)) {
            this.$message({
              type: 'error',
              message: '请输入正确的手机号！'
            })
            return false
          }
        } else {
          let option_users = this.option_users
          let findUser  = option_users.find(item => item.value === this.passiveUser)

          data = {
            userId: this.params.userInfo.userID,
            agentType: this.agentType,
            passiveUserId: this.passiveUser,
            userName: findUser.userName,
            phone: findUser.mobile
          }
        }
        api.editAccountingAgentType(data)
        .then(res => {
          let data = res.data
          let status = data.status
          if (status === 1) {
            this.$message({
              type: 'success',
              message: '操作成功！'
            })
            this.$router.go(-2)
          } else if (status === 2) {
            this.$message({
              type: 'error',
              message: '手机号已存在！'
            })
          } else {
            this.$message({
              type: 'error',
              message: '操作失败！'
            })
          }
        })
      }
    },
    testMobile(val) {
      let pattern =/^(0|86|17951)?(13[0-9]|15[0-9]|16[0-9]|17[0-9]|18[0-9]|14[0-9]|19[0-9])[0-9]{8}$/
      console.log('pattern.exec(val)', pattern.exec(val))
      return pattern.exec(val)
    }
  }
}

</script>

<style lang="scss" scoped>
.el-row {
  margin-bottom: 16px;
}
.el-row:last-child {
  margin-bottom: 0;
}
</style>
