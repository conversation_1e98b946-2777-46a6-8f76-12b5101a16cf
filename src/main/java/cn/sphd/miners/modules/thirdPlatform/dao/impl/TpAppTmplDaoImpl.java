package cn.sphd.miners.modules.thirdPlatform.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.thirdPlatform.dao.TpAppTmplDao;
import cn.sphd.miners.modules.thirdPlatform.entity.TpAppTmpl;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class TpAppTmplDaoImpl extends BaseDao<TpAppTmpl, Serializable> implements TpAppTmplDao {
}
