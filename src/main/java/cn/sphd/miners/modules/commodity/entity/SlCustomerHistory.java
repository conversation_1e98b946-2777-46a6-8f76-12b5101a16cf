package cn.sphd.miners.modules.commodity.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity(name="SlCustomerHistory")
@Table(name="t_sl_customer_history")
public class SlCustomerHistory implements Serializable {
    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="customer" ,nullable=true , unique=false)
    private Integer customer;

    @Column(name="name"  , length=100 , nullable=true , unique=false)
    private String name;

    @Column(name="full_name"  , length=255 , nullable=true , unique=false)
    private String fullName;

    @Column(name="code"  , length=100 , nullable=true , unique=false)
    private String code;//客户代号

    @Column(name="category"  , length=1 , nullable=true , unique=false)
    private String category;//客户类别

    @Column(name="post_code"  , length=6 , nullable=true , unique=false)
    private String postCode;//邮政编码

    @Column(name="post_address"  , length=100 , nullable=true , unique=false)
    private String postAddress;//邮政地址

    @Column(name="taxpayerID"  , length=20 , nullable=true , unique=false)
    private String taxpayerID;//税号

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;//备注

    @Column(name="telephone"  , length=100 , nullable=true , unique=false)
    private String telephone;

    @Column(name="address"  , length=100 , nullable=true , unique=false)
    private String address;

    @Column(name="bank_code"  , length=100 , nullable=true , unique=false)
    private String bankCode;

    @Column(name="bank_name"  , length=100 , nullable=true , unique=false)
    private String bankName;

    @Column(name="bank_no"  , length=100 , nullable=true , unique=false)
    private String bankNo;

    @Column(name="keywords"  , length=100 , nullable=true , unique=false)
    private String keywords;

    @Column(name="principal"  , nullable=true , unique=false)
    private Integer principal;

    @Column(name="principal_name"  , nullable=true , unique=false)
    private String principalName;

    @Column(name="operation"  ,length=1 , nullable=true , unique=false)
    private String operation;

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    //修改前记录id
    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId;

    //修改版本号
    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo;

    @Column(name="type"  , length=1 , nullable=true , unique=false)
    private String type;
    /**
     * 首次购买类型:1-今年,2-去年,3-更久之前
     * isNullAble:1
     */
    @Column(name="initial_type"  , length=1 , nullable=true , unique=false)
    private String initialType;

    /**
     * 首次购买年月:YYYYMM
     * isNullAble:1
     */
    @Column(name="initial_period"  , length=6 , nullable=true , unique=false)
    private String initialPeriod;

    /**
     * 首次接触时间
     * isNullAble:1
     */
    @Column(name="first_contact_time"   , nullable=true , unique=false)
    private Date firstContactTime;

    /**
     * 首次接触地点
     * isNullAble:1
     */
    @Column(name="first_contact_address"  , length=100 , nullable=true , unique=false)
    private String firstContactAddress;

    /**
     * 信息获取渠道
     * isNullAble:1
     */
    @Column(name="info_source"  , length=100 , nullable=true , unique=false)
    private String infoSource;

    /**
     * 开票名称
     * isNullAble:1
     */
    @Column(name="invoice_name"  , length=100 , nullable=true , unique=false)
    private String invoiceName;

    /**
     * 开票地址
     * isNullAble:1
     */
    @Column(name="invoice_address"  , length=100 , nullable=true , unique=false)
    private String invoiceAddress;

    @Column(name="supervisor_name"  , length=50 , nullable=true , unique=false)
    private String supervisorName;   //超管姓名（特殊机构 lixu添加）

    @Column(name="supervisor_mobile"  , length=50 , nullable=true , unique=false)
    private String supervisorMobile;   //超管手机 （特殊机构 lixu添加）

    @Column(name="g_tenant_id"   , nullable=true , unique=false)
    private Integer gTenantId;//管理平台租户ID

    @Column(name = "has_contract" )
    private String hasContract;//1.91 是否有合同且在有效期内 1是 0否

    @Column(name = "contract_sn" )
    private String contractSn; //1.91 合同编号

    @Column(name = "expires_time" )
    private Date expiresTime; //1.91 过期时间

    @Column(name = "contract_time" )
    private Date contractTime;//1.91 合同签署时间

    @Column(name = "is_suspend" )
    private String isSuspend;//1.121 1暂停合作 0否

    @Column(name = "delivery_way" )
    private Integer deliveryWay;//收货方式1-送货上门;2-配送至指定区域;3-上门自提

    @Column(name = "delivery_type" )
    private Integer deliveryType;//收货类型:1-只提供服务,不提供实体货物;2-提供实体货物

    @Column(name = "self_state" )
    private Integer selfState;//自提状态

    @Column(name = "function_opt" )
    private String functionOpt;//1.315四个问题加

    @Column(name = "sale_opt" )
    private String saleOpt; //1.315四个问题加

    @Column(name = "commodity_opt" )
    private String commodityOpt; //1.315四个问题加

    @Column(name = "warehouse_opt" )
    private String warehouseOpt; //1.315四个问题加

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getCustomer() {
        return customer;
    }

    public void setCustomer(Integer customer) {
        this.customer = customer;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public String getPostAddress() {
        return postAddress;
    }

    public void setPostAddress(String postAddress) {
        this.postAddress = postAddress;
    }

    public String getTaxpayerID() {
        return taxpayerID;
    }

    public void setTaxpayerID(String taxpayerID) {
        this.taxpayerID = taxpayerID;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankNo() {
        return bankNo;
    }

    public void setBankNo(String bankNo) {
        this.bankNo = bankNo;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public Integer getPrincipal() {
        return principal;
    }

    public void setPrincipal(Integer principal) {
        this.principal = principal;
    }

    public String getPrincipalName() {
        return principalName;
    }

    public void setPrincipalName(String principalName) {
        this.principalName = principalName;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public String getInitialType() {
        return initialType;
    }

    public void setInitialType(String initialType) {
        this.initialType = initialType;
    }

    public String getInitialPeriod() {
        return initialPeriod;
    }

    public void setInitialPeriod(String initialPeriod) {
        this.initialPeriod = initialPeriod;
    }

    public Date getFirstContactTime() {
        return firstContactTime;
    }

    public void setFirstContactTime(Date firstContactTime) {
        this.firstContactTime = firstContactTime;
    }

    public String getFirstContactAddress() {
        return firstContactAddress;
    }

    public void setFirstContactAddress(String firstContactAddress) {
        this.firstContactAddress = firstContactAddress;
    }

    public String getInfoSource() {
        return infoSource;
    }

    public void setInfoSource(String infoSource) {
        this.infoSource = infoSource;
    }

    public String getInvoiceName() {
        return invoiceName;
    }

    public void setInvoiceName(String invoiceName) {
        this.invoiceName = invoiceName;
    }

    public String getInvoiceAddress() {
        return invoiceAddress;
    }

    public void setInvoiceAddress(String invoiceAddress) {
        this.invoiceAddress = invoiceAddress;
    }

    public String getSupervisorName() {
        return supervisorName;
    }

    public void setSupervisorName(String supervisorName) {
        this.supervisorName = supervisorName;
    }

    public String getSupervisorMobile() {
        return supervisorMobile;
    }

    public void setSupervisorMobile(String supervisorMobile) {
        this.supervisorMobile = supervisorMobile;
    }

    public Integer getgTenantId() {
        return gTenantId;
    }

    public void setgTenantId(Integer gTenantId) {
        this.gTenantId = gTenantId;
    }

    public String getHasContract() {
        return hasContract;
    }

    public void setHasContract(String hasContract) {
        this.hasContract = hasContract;
    }

    public String getContractSn() {
        return contractSn;
    }

    public void setContractSn(String contractSn) {
        this.contractSn = contractSn;
    }

    public Date getExpiresTime() {
        return expiresTime;
    }

    public void setExpiresTime(Date expiresTime) {
        this.expiresTime = expiresTime;
    }

    public Date getContractTime() {
        return contractTime;
    }

    public void setContractTime(Date contractTime) {
        this.contractTime = contractTime;
    }

    public String getIsSuspend() {
        return isSuspend;
    }

    public void setIsSuspend(String isSuspend) {
        this.isSuspend = isSuspend;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getDeliveryWay() {
        return deliveryWay;
    }

    public void setDeliveryWay(Integer deliveryWay) {
        this.deliveryWay = deliveryWay;
    }

    public Integer getDeliveryType() {
        return deliveryType;
    }

    public void setDeliveryType(Integer deliveryType) {
        this.deliveryType = deliveryType;
    }

    public Integer getSelfState() {
        return selfState;
    }

    public void setSelfState(Integer selfState) {
        this.selfState = selfState;
    }


    public String getFunctionOpt() {
        return functionOpt;
    }

    public void setFunctionOpt(String functionOpt) {
        this.functionOpt = functionOpt;
    }

    public String getSaleOpt() {
        return saleOpt;
    }

    public void setSaleOpt(String saleOpt) {
        this.saleOpt = saleOpt;
    }

    public String getCommodityOpt() {
        return commodityOpt;
    }

    public void setCommodityOpt(String commodityOpt) {
        this.commodityOpt = commodityOpt;
    }

    public String getWarehouseOpt() {
        return warehouseOpt;
    }

    public void setWarehouseOpt(String warehouseOpt) {
        this.warehouseOpt = warehouseOpt;
    }
}
