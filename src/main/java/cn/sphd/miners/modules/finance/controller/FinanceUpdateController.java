package cn.sphd.miners.modules.finance.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.accountantReportTax.service.AcctReportTaxService;
import cn.sphd.miners.modules.finance.entity.*;
import cn.sphd.miners.modules.finance.service.*;
import cn.sphd.miners.modules.message.service.MessageService;
import cn.sphd.miners.modules.personal.entity.*;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.personal.service.PersonnelReimburseService;
import cn.sphd.miners.modules.personal.service.PersonnelReimbursetAttachmentService;
import cn.sphd.miners.modules.personal.service.UserMessageService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.entity.Code;
import cn.sphd.miners.modules.system.entity.CodeCategory;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.CodeService;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
//import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by Administrator on 2017/5/11.
 */
@Controller
@RequestMapping("/update")
public class FinanceUpdateController {

    @Autowired
    DataService dataService;
    @Autowired
    UserService userService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    UserMessageService userMessageService;
    @Autowired
    FinanceChequeService financeChequeService;
    @Autowired
    FinanceAccountService financeAccountService;
    @Autowired
    PersonnelReimburseService personnelReimburseService;
    @Autowired
    FinanceReturnService financeReturnService;
    @Autowired
    AccountService accountService;
    @Autowired
    PersonnelReimbursetAttachmentService personnelReimbursetAttachmentService;
    @Autowired
    CodeService codeService;
    @Autowired
    MessageService messageService;
    @Autowired
    FinanceUpdateService financeUpdateService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    AcctReportTaxService acctReportTaxService;


    /**
     *<AUTHOR>
     *@date 2017/6/9 15:26
     *跳转修改记录的页面
     */
    @RequestMapping("/toUpdatePage.do")
    public String toUpdatePage(){
        return "/finance/updateHistory";
    }

    /**
     *<AUTHOR>
     *@date 2017/5/11 10:05
     *@date 2019/8/22 15:47(1.78处理之财务修改1)
     *@date 2021/11/25 (1.179财务优化2101)
     *提交纯现金/银行转账修改申请
     * 修改字段：1-类别(Genre)【categoryDesc（当类别为其他时候的说明）】 2-摘要(Summary) 3-收入(credit) 4-支出(debit) 5-用途(purpose) 6-经手人(auditorName)
     * 7-付款单位(OppositeCorp)【stakeholder(干系人ID/收款单位id)】 8-备注(memo)9-到账时间(receiveAccountDate) 10-账户id(accountId_)[10-收款/付款银行(accountBank) ] 14-票面金额(billAmount)
     *     支出中： 12-票据数量(billQuantity) 15-stakeholderCategory(类别:1-供应商,2-员工,3-自行录入) 16-billDate(票据日期) 17-factDate(实际(付款/支出)日期'，实际缴纳日期(1.207加的))
     * {18-imageType-1-图片有修改  0-图片未修改[用来判断图片是否修改了]  imgPaths-图片链接(多个的)}
     *  1.179去掉的字段：11-票据类型(billCat)  13-票据所属月份(billPeriod 1-本月票据,2-非本月票据)
     * 2022/7/25 1.207财务优化之税种--lyx新增
     * taxId(税种id) taxCategoryName(税种名称) 税款所属时期(beginDate-开始时间 endDate-结束时间,Integer 格式是yyyyMMdd) report(报表id)
     * periodBegin(需申报的开始日期 yyyy-MM-dd) periodEnd(需申报的截止日期 yyyy-MM-dd)  taxPeriodIdInsert(申报记录id修改后的) taxPeriodIdUpdate(申报记录id修改前的)
     */
    @ResponseBody
    @RequestMapping("/updateCash.do")
    @MessageMapping("/updateCash")
    public JsonResult updateCash(String json,String sessionid,User user) {
        JSONObject jsonObject = JSONObject.parseObject(json);
        Integer detailId = jsonObject.getInteger("detailId");  //详情id
        String genre = StringUtils.isNotEmpty((String) jsonObject.get("genre")) ? jsonObject.getString("genre") : null;  //类别
        String categoryDesc = StringUtils.isNotEmpty((String) jsonObject.get("categoryDesc")) ? jsonObject.getString("categoryDesc") : null; //当类别为其他时候的说明
        String summary = jsonObject.getString("summary");  //摘要
        BigDecimal credit = StringUtils.isNotEmpty((String) jsonObject.get("credit")) ? jsonObject.getBigDecimal("credit") : null;  //收入
        BigDecimal debit = StringUtils.isNotEmpty((String) jsonObject.get("debit")) ? jsonObject.getBigDecimal("debit") : null;   //支出
        String purpose = StringUtils.isNotEmpty((String) jsonObject.get("purpose")) ? jsonObject.getString("purpose") : null;  //用途
        String auditorName = StringUtils.isNotEmpty((String) jsonObject.get("auditorName")) ? jsonObject.getString("auditorName") : null;   //经手人
        String oppositeCorp = StringUtils.isNotEmpty((String) jsonObject.get("oppositeCorp")) ? jsonObject.getString("oppositeCorp") : null; //付款单位
        String memo = jsonObject.getString("memo");  //备注
        Date receiveAccountDate =StringUtils.isNotEmpty((String) jsonObject.get("receiveAccountDate")) ? jsonObject.getDate("receiveAccountDate") : null;  //到账时间
        Integer accountId = jsonObject.getInteger("accountId");  //账户id
//        String billCat = jsonObject.getString("billCat");   //票据类型
        Integer billQuantity = StringUtils.isNotEmpty((String) jsonObject.get("billQuantity")) ? jsonObject.getInteger("billQuantity") : null;  //票据数量
//        String billPeriod = jsonObject.getString("billPeriod");  //票据所属月份  1-本月票据,2-非本月票据
        BigDecimal billAmount = null;  //票面金额
        if (StringUtils.isNotEmpty((String) jsonObject.get("billAmount"))){
            billAmount = jsonObject.getBigDecimal("billAmount");
        }
        String stakeholderCategory = StringUtils.isNotEmpty((String) jsonObject.get("stakeholderCategory")) ? jsonObject.getString("stakeholderCategory") : null;  //类别:1-供应商,2-员工,3-自行录入
        Integer stakeholder = (Integer) jsonObject.get("stakeholder")!=null ? jsonObject.getInteger("stakeholder") : null;  //干系人ID
        Date billDate = StringUtils.isNotEmpty((String) jsonObject.get("billDate")) ? jsonObject.getDate("billDate") : null;  //票据日期
        Date factDate = StringUtils.isNotEmpty((String) jsonObject.get("factDate")) ? jsonObject.getDate("factDate") : null;  //实际(付款/支出)日期
        Integer imgType = jsonObject.getInteger("imgType");  //1-图片有修改  0-图片未修改
        String imgPaths = jsonObject.getString("imgPaths"); //图片链接
        Integer taxId = StringUtils.isNotEmpty((String) jsonObject.get("taxId"))?jsonObject.getInteger("taxId"):null;  //税种id
        String taxCategoryName = StringUtils.isNotEmpty((String) jsonObject.get("taxCategoryName"))?jsonObject.getString("taxCategoryName"):null;  //税种名称
        Integer report = StringUtils.isNotEmpty((String) jsonObject.get("report"))?jsonObject.getInteger("report"):null;  //报表id
        Integer businessPeriodBegin = StringUtils.isNotEmpty((String) jsonObject.get("businessPeriodBegin"))?jsonObject.getInteger("businessPeriodBegin"):null;  //税款所属时期(开始时间
        Integer businessPeriodEnd = StringUtils.isNotEmpty((String) jsonObject.get("businessPeriodEnd"))?jsonObject.getInteger("businessPeriodEnd"):null;  //税款所属时期(结束时间
        String periodBegin = StringUtils.isNotEmpty((String) jsonObject.get("periodBegin"))?jsonObject.getString("periodBegin"):null;  //需申报的开始日期 yyyy-MM-dd
        String periodEnd = StringUtils.isNotEmpty((String) jsonObject.get("periodEnd"))?jsonObject.getString("periodEnd"):null;  //需申报的结束日期 yyyy-MM-dd
        //申报记录id(修改后的)
        Integer taxPeriodIdInsert = jsonObject.get("taxPeriodIdInsert")!=null?jsonObject.getInteger("taxPeriodIdInsert"):null;
        //申报记录id(修改前的)
        Integer taxPeriodIdUpdate = jsonObject.get("taxPeriodIdUpdate")!=null?jsonObject.getInteger("taxPeriodIdUpdate"):null;

        Map<String,Object> map = financeUpdateService.updateCash(user.getUserID(),detailId,genre,categoryDesc,summary,credit,debit,purpose,auditorName,oppositeCorp,memo,
                receiveAccountDate,accountId,billQuantity,billAmount,stakeholderCategory,stakeholder,billDate,factDate,imgType,imgPaths,taxId,taxCategoryName,report,businessPeriodBegin,businessPeriodEnd,periodBegin,periodEnd,taxPeriodIdInsert,taxPeriodIdUpdate);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid, "/backMessage", null, null, null, null, JSON.toJSONString(map));//推送 待处理

        return new JsonResult(1, map);
    }


    /**
     *<AUTHOR>
     *@date 2017/5/17 16:45
     *@date 2019/8/23 15:47(1.78处理之财务修改1)
     *数据来源为内部非支出性转账的修改申请
     * 修改数据：1-chequeNo(支票号)[1-支票id(ChequeId)] 2-收入(credit) 3-支出(debit) 4-业务发生时间(auditDate) 5-备注(memo)
     * sessionid 浏览器sessionID
    */
    @ResponseBody
    @RequestMapping("/updateTransferAccounts.do")
    @MessageMapping("/updateTransferAccounts")
    public JsonResult updateTransferAccounts(String json,String sessionid,User user) {

        JSONObject jsonObject = JSONObject.parseObject(json);
        Integer detailId = jsonObject.getInteger("detailId");  //详情id
        BigDecimal credit = jsonObject.getBigDecimal("credit");  //收入
        BigDecimal debit = jsonObject.getBigDecimal("debit");  //支出
        Date auditDate = jsonObject.getDate("auditDate");  //业务发生时间
        Integer chequeId = jsonObject.getInteger("chequeId");  //支票id
        String memo = jsonObject.getString("memo");  //备注

        Map<String,Object> map = financeUpdateService.updateTransferAccounts(user.getUserID(),detailId,credit,debit,auditDate,chequeId,memo);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid, "/backMessage", null, null, null, null, JSON.toJSONString(map));//推送 待处理
        return new JsonResult(1, map);
    }

    /**
     *<AUTHOR>
     *@date 2017/5/22 11:58
     *@date 2019/8/23 17:08(1.78处理之财务修改1)
     *承兑汇票和转账支票中支票的修改申请
     * 修改说明: 1-存入银行账户[存入银行账户的id(accountId) 存入银行账户(accout) 存入银行的银行名称(saveBankName)] 2-存入时间(depositDate)
     *          3-存入经手人(depositorName) 4-到账时间(receiveAccountDate)  {5-imageType-1-图片有修改  0-图片未修改[用来判断图片是否修改了]  imgPaths-图片链接(多个的)}
     *添加一个参数 updateType:1-修改其他信息(修改存入银行的信息，默认) 2-更换承兑汇票  returnIdNew:新的票据id(1.290公司总览之财务--改/********)
     */
    @ResponseBody
    @RequestMapping("/updateReturn.do")
    @MessageMapping("/updateReturn")
    public JsonResult updateReturn(String json,String sessionid,User user) {
        JSONObject jsonObject = JSONObject.parseObject(json);
        Integer returnId = jsonObject.getInteger("returnId");  //票据详情id
        Integer accountId = jsonObject.getInteger("accountId");  //存入银行账户id
        Date depositDate = jsonObject.getDate("depositDate");  //存入时间
        String depositorName = jsonObject.getString("depositorName");  //存入经手人
        Date receiveAccountDate = jsonObject.getDate("receiveAccountDate");  //到账时间
        Integer imgType = jsonObject.getInteger("imgType");  //1-图片有修改  0-图片未修改
        String imgPaths = jsonObject.getString("imgPaths"); //图片链接
        Integer updateType = jsonObject.getInteger("updateType"); //1-修改其他信息(修改存入银行的信息，默认) 2-更换承兑汇票
        if (updateType==null){
            updateType = 1;
        }
        Integer returnIdNew = jsonObject.getInteger("returnIdNew");  //新的票据id
        Map<String,Object> map = financeUpdateService.updateReturn(user.getUserID(),returnId,accountId,depositDate,depositorName,receiveAccountDate,imgType,imgPaths,updateType,returnIdNew);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid, "/backMessage", null, null, null, null, JSON.toJSONString(map));//推送 待处理

        return new JsonResult(1, map);
    }

    /**
     *<AUTHOR>
     *@date 2017/5/22 12:01
     *@date 2019/8/23 17:56(1.78处理之财务修改1)
     *@date 2021/11/26 (1.179财务优化2101)
     *数据来源为支出中的转账支票(内部的转账支票或者改为外部的汇款票据)的修改申请
     * 修改说明：3-摘要(summary) 4-票据数量(billQuantity) 5-金额(amount)
     *           6-用途(purpose) 7-经手人(financialHandling) 8-支出方式(type)[1-转帐支票(内部的)，2-现金汇票 3-承兑汇票(仅在此处用) 4-外部的转账支票(仅在此处用)]
     *           9-银行账户(accountId){银行名称(bankName) 银行账号(account)} 10-支票号(chequeNo) 11-支票到期日(expireDate) 12-收款单位(oppositeCorp)【stakeholder(干系人ID，收款单位id)】
     *           13-接收日期(receiveDate) 14-接收经手人(receiver) 15-支付经手人(operator) 16-备注(memo) 17-票面金额(billAmount)
     *           18-stakeholderCategory(类别:1-供应商,2-员工,3-自行录入) 19-billDate(票据日期) 20-Date factDate(实际(付款/支出)日期')
     *           {21-imageType 1-图片有修改  0-图片未修改[用来判断图片是否修改了]  imgPaths-图片链接(多个的)}
     * chequeId:修改后使用的支票id
     * 去掉：1-票据种类(billCat) 2-票据所属月份(billPeriod)
    */
    @ResponseBody
    @RequestMapping("/updateBankTransfer.do")
    @MessageMapping("/updateBankTransfer")
    public JsonResult updateBankTransfer(String json,String sessionid,User user) {
        JSONObject jsonObject = JSONObject.parseObject(json);
        Integer chequeDetailId = jsonObject.getInteger("chequeDetailId");  //票据详情id
        Integer accountId = jsonObject.getInteger("accountId");  //银行账户id
//        String billCat = jsonObject.getString("billCat");  //票据种类
//        String billPeriod = jsonObject.getString("billPeriod");  //票据所属月份
        String summary = jsonObject.getString("summary");  //摘要
        Integer billQuantity = jsonObject.getInteger("billQuantity");  //票据数量
        BigDecimal amount = jsonObject.getBigDecimal("amount");  //金额
        BigDecimal billAmount = jsonObject.getBigDecimal("billAmount");  //票面金额
        String purpose = jsonObject.getString("purpose");  //用途
        String financialHandling = jsonObject.getString("financialHandling");  //经手人
        String type = jsonObject.getString("type");  //支出方式(1-转帐支票(内部的)，2-现金汇票 3-承兑汇票(仅在此处用) 4-外部的转账支票(仅在此处用))
        String chequeNo = jsonObject.getString("chequeNo");  //支票号
        Date expireDate = jsonObject.getDate("expireDate");  //支票到期日
        Date receiveDate = jsonObject.getDate("receiveDate");  //接收日期
        String receiver = jsonObject.getString("receiver");  //接收经手人
        String operator = jsonObject.getString("operator");  //支付经手人
        String memo = jsonObject.getString("memo");  //备注
        Integer chequeId = jsonObject.getInteger("chequeId");  //修改后使用的支票id
        String stakeholderCategory = jsonObject.getString("stakeholderCategory");  //类别:1-供应商,2-员工,3-自行录入
        Integer stakeholder = jsonObject.getInteger("stakeholder");  //干系人ID
        Date billDate = jsonObject.getDate("billDate");  //票据日期
        Date factDate = jsonObject.getDate("factDate");  //实际(付款/支出)日期
        String receiveCorp = jsonObject.getString("oppositeCorp");  //收款单位(这两个字段差不多)
        Integer imgType = jsonObject.getInteger("imgType");  //1-图片有修改  0-图片未修改
        String imgPaths = jsonObject.getString("imgPaths"); //图片链接

        Map<String,Object> map = financeUpdateService.updateBankTransfer(user.getUserID(),chequeDetailId,accountId,summary,
                billQuantity,amount,billAmount,purpose,financialHandling,type,chequeNo,expireDate,receiveCorp,receiveDate,receiver,operator,memo,
                chequeId,stakeholderCategory,stakeholder,billDate,factDate,imgType,imgPaths);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid, "/backMessage", null, null, null, null, JSON.toJSONString(map));//推送 待处理

        return new JsonResult(1, map);
    }

    /**
     *<AUTHOR>
     *@date 2019/8/26 10:58
     *审批人待处理列表
    */
    @ResponseBody
    @RequestMapping("/updateApprovalHandle.do")
    @MessageMapping("/updateApprovalHandle")
    private JsonResult updateApprovalHandle(String sessionid,User user){
        Map<String,Object> map = new HashMap<>();
        List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcessByBusinessType(user.getUserID(),"1",1,null,null,null,"asc");
        map.put("approvalProcessList",approvalProcessList);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/updateApprovalHandle", null, null, null, null, JSON.toJSONString(map));
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2019/8/26 15:31
     *申请人待处理列表
     * sessionid 浏览器sessionid
    */
    @ResponseBody
    @RequestMapping("/updateApplyHandle.do")
    @MessageMapping("/updateApplyHandle")
    public JsonResult updateApplyHandle(String sessionid,User user){
        Map<String,Object> map = new HashMap<>();
        List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcessByFromUser(user.getUserID(),"1",1,null,null,"asc");
        map.put("approvalProcessList",approvalProcessList);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/updateApplyHandle", null, null, null, null, JSON.toJSONString(map));
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2019/8/26 9:33
     *审批人查询接口
    */
    @ResponseBody
    @RequestMapping("/getAllUpdateApproval.do")
    @MessageMapping("/getAllUpdateApproval")
    public JsonResult getAllUpdateApproval(String json,String sessionid,User user){
        Map<String,Object> map = new HashMap<>();
        JSONObject jsonObject = JSONObject.parseObject(json);
        Integer type = jsonObject.getInteger("type");  //1-近七日 2-本月 3-自定义
        String approvalStatus = jsonObject.getString("approvalStatus");   //2-已批准 3-驳回
        Date beginTime = jsonObject.getDate("beginTime");  //开始时间
        Date endTime = jsonObject.getDate("endTime");  //结束时间

        if (type!=null){
            if (type==1){   //近七日
                beginTime = NewDateUtils.changeDay(new Date(),-6);
                endTime = new Date();
            }else if (type==2){ //本月
                beginTime = NewDateUtils.changeMonth(new Date(),0);
                endTime = new Date();
            } if (type==3) {
                if (endTime.getTime() > new Date().getTime() || NewDateUtils.today(endTime).getTime()==NewDateUtils.today(new Date()).getTime()) {
                    endTime = new Date();
                }
            }
        }
        List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcessByBusinessType(user.getUserID(),approvalStatus,1,beginTime,endTime,null,"desc");
        map.put("approvalProcessList",approvalProcessList);
        map.put("beginTime",beginTime);  //开始时间
        map.put("endTime",endTime);  //结束时间
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/getAllUpdateApproval",null,null,null,null,JSON.toJSONString(map));
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2019/8/26 15:05
     *申请人查询接口
    */
    @ResponseBody
    @RequestMapping("/getAllUpdateApply.do")
    @MessageMapping("/getAllUpdateApply")
    public JsonResult getAllUpdateApply(String json,String sessionid,User user){
        Map<String,Object> map = new HashMap<>();
        JSONObject jsonObject = JSONObject.parseObject(json);
        Integer type = jsonObject.getInteger("type");  //1-近七日 2-本月 3-自定义
        String approvalStatus = jsonObject.getString("approvalStatus");   //2-已批准 3-驳回
        Date beginTime = jsonObject.getDate("beginTime");  //开始时间
        Date endTime = jsonObject.getDate("endTime");  //结束时间

        if (type!=null){
            if (type==1){   //近七日
                beginTime = NewDateUtils.changeDay(new Date(),-6);
                endTime = new Date();
            }else if (type==2){ //本月
                beginTime = NewDateUtils.changeMonth(new Date(),0);
                endTime = new Date();
            }else if (type==3){
                if (endTime.getTime()>new Date().getTime()|| NewDateUtils.today(endTime).getTime()==NewDateUtils.today(new Date()).getTime()){
                    endTime = new Date();
                }
            }

        }
        List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcessByFromUser(user.getUserID(),approvalStatus,1,beginTime,endTime,"desc");
        map.put("approvalProcessList",approvalProcessList);
        map.put("beginTime",beginTime);  //开始时间
        map.put("endTime",endTime);  //结束时间
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/getAllUpdateApply",null,null,null,null,JSON.toJSONString(map));
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2017/5/24 15:31
     * @date 2019/8/27 10:23
     *修改记录中的查看数据详情
     * 审批流程中的type 状态 1-纯现金/银行转账 2-内部非支出性转账 3-报销 4-承兑汇票和转账支票中的外部支票 5-数据来源为支出中的转账支票(内部的)
    */
    @ResponseBody
    @RequestMapping("/getUpdateDetail.do")
    @MessageMapping("/getUpdateDetail")
    public JsonResult getUpdateDetail(User user,String json,String sessionid) {
        Map<String,Object> map = new HashMap<>();
        JSONObject jsonObject = JSONObject.parseObject(json);
        Integer approvalProcessId = jsonObject.getInteger("approvalProcessId");  //审批流程id（登录人）
        Date beginDebug = new Date();
        System.out.println("查看详情开始："+beginDebug);
        if (approvalProcessId!=null){
            ApprovalProcess approvalProcess = approvalProcessService.getApprovalProcessById(approvalProcessId);
            if (user.getOid().equals(approvalProcess.getOrg())) {
                if (approvalProcess.getType() == 1) {  //纯现金/银行转账
                    AccountDetail accountDetail = dataService.getOneDetail(approvalProcess.getOldId());
                    FinanceAccountBill financeAccountBill = accountDetail.getBillDetail();
                    if (financeAccountBill != null) {
                        accountDetail.setBillCat(financeAccountBill.getBillCat());
                        accountDetail.setBillPeriod(financeAccountBill.getBillPeriod());
                        accountDetail.setBillQuantity(financeAccountBill.getBillQuantity());
                    }
                    if (approvalProcess.getNewId() != null) {
                        AccountDetailHistory accountDetailHistory = dataService.getAccountDetailHistoryById(approvalProcess.getNewId());
                        List<FinanceAccountBillImageHistory> financeAccountBillImageHistories = dataService.getBillImageHistoryByIds(user.getOid(),accountDetailHistory.getId(),null);
                        if (StringUtils.isNotEmpty(accountDetail.getSubType())&&"5".equals(accountDetail.getSubType())){  //税款的数据
                            Map<String,Object> map1 = acctReportTaxService.getById(accountDetail.getBusiness());
                            accountDetail.setTaxName((String) map1.get("taxName"));  //税种名称
                            accountDetail.setBeginDate(NewDateUtils.dateFromString((String) map1.get("periodBegin"),"yyyyMMdd")); //税款的申报记录开始时间
                            accountDetail.setEndDate(NewDateUtils.dateFromString((String) map1.get("periodEnd"),"yyyyMMdd")); //税款的申报记录结束时间
                            accountDetail.setReport((Integer) map1.get("report"));  //报表id
                            accountDetail.setTaxPeriodId((Integer) map1.get("periodId"));  //申报记录id

                            Map<String,Object> map2 = acctReportTaxService.getById(accountDetailHistory.getBusiness());
                            accountDetailHistory.setTaxName((String) map2.get("taxName"));  //税种名称
                            accountDetailHistory.setBeginDate(NewDateUtils.dateFromString((String) map2.get("periodBegin"),"yyyyMMdd")); //税款的申报记录开始时间
                            accountDetailHistory.setEndDate(NewDateUtils.dateFromString((String) map2.get("periodEnd"),"yyyyMMdd")); //税款的申报记录结束时间
                            accountDetailHistory.setReport((Integer) map2.get("report"));  //报表id
                            accountDetailHistory.setTaxPeriodId((Integer) map2.get("periodId"));  //申报记录id
                        }

                        map.put("newDetail", accountDetailHistory);
                        map.put("newBillImageHistories", financeAccountBillImageHistories);
                    }
                    List<FinanceAccountBillImage> financeAccountBillImages = dataService.getBillImageByIds(user.getOid(),accountDetail.getId(),null);
                    map.put("source", 1);
                    map.put("oldDetail", accountDetail);
                    map.put("oldBillImages", financeAccountBillImages);
                } else if (approvalProcess.getType() == 2) {  //2-内部非支出性转账
                    AccountDetail accountDetail = dataService.getOneDetail(approvalProcess.getOldId());
                    if (accountDetail.getBillDetail() != null) {
                        FinanceAccountBill financeAccountBill = accountDetail.getBillDetail();
                        if (financeAccountBill.getCheque() != null) {
                            FinanceChequeDetail financeChequeDetail = financeAccountBill.getCheque();
                            accountDetail.setChequeId(financeChequeDetail.getId());
                            accountDetail.setChequeNo(financeChequeDetail.getChequeNo());
                        }
                    }
                    AccountDetailHistory accountDetailHistory = dataService.getAccountDetailHistoryById(approvalProcess.getNewId());
                    if (accountDetailHistory.getBillHistoryId() != null) {
                        FinanceAccountBillHistory financeAccountBillHistory = dataService.getBillHistoryById(accountDetailHistory.getBillHistoryId());
                        accountDetailHistory.setChequeId(financeAccountBillHistory.getCheque());
                        accountDetailHistory.setChequeNo(financeAccountBillHistory.getBillNo());
                    }
                    List<FinanceAccountBillImageHistory> financeAccountBillImageHistories = dataService.getBillImageHistoryByIds(user.getOid(),accountDetailHistory.getId(),null);
                    List<FinanceAccountBillImage> financeAccountBillImages = dataService.getBillImageByIds(user.getOid(),accountDetail.getId(),null);

                    map.put("source", 2);
                    map.put("oldDetail", accountDetail);
                    map.put("newDetail", accountDetailHistory);
                    map.put("oldBillImages", financeAccountBillImages);
                    map.put("newBillImageHistories", financeAccountBillImageHistories);
                } else if (approvalProcess.getType() == 3) {   //3-报销（在此1.78版本，此数据暂时不可修改）
                    PersonnelReimburse personnelReimburse = personnelReimburseService.personnelReimburseById(approvalProcess.getOldId());
                    PersonnelReimburseHistory personnelReimburseHistory = personnelReimburseService.getPersonnelReimburseHistoryById(approvalProcess.getNewId());
                    List<ApprovalProcess> processList = approvalProcessService.getApprovalProcessByPersonnelReimburseId(personnelReimburse.getId());
                    map.put("source", 3);
                    map.put("oldDetail", personnelReimburse);
                    map.put("newDetail", personnelReimburseHistory);
                    map.put("processList", processList);  //旧数据财务报销的申请与审批流程
                } else {
                    FinanceAccountBill financeAccountBill = dataService.getAccountBillByBillId(approvalProcess.getOldId());
                    FinanceAccountBillHistory financeAccountBillHistory = dataService.getBillHistoryById(approvalProcess.getNewId());
                    if (approvalProcess.getType() == 4) {  //4-承兑汇票和转账支票中的外部支票
                        FinanceReturn financeReturn = financeAccountBill.getFinanceReturn();
                        if (financeReturn != null) {
                            financeReturn.setBillCat(financeAccountBill.getBillCat());
                            financeReturn.setBillQuantity(financeAccountBill.getBillQuantity());
                            financeReturn.setBillPeriod(financeAccountBill.getBillPeriod());
                            financeReturn.setBillAmount(financeAccountBill.getBillAmount());
                        }
                        if (financeAccountBillHistory.getReturnBill()!=null&&financeReturn.getId()!=financeAccountBillHistory.getReturnBill()){
                            FinanceReturn financeReturnNew = financeReturnService.getReturn(financeAccountBillHistory.getReturnBill());
                            if (financeReturnNew != null) {
                                financeReturnNew.setBillCat(financeAccountBillHistory.getBillCat());
                                financeReturnNew.setBillQuantity(financeAccountBillHistory.getBillQuantity());
                                financeReturnNew.setBillPeriod(financeAccountBillHistory.getBillPeriod());
                                financeReturnNew.setBillAmount(financeAccountBillHistory.getBillAmount());
                            }
                            map.put("financeReturnNew", financeReturnNew);
                        }

                        List<FinanceAccountBillImage> financeAccountBillImages = dataService.getBillImageByIds(user.getOid(),null,financeAccountBill.getId());
                        List<FinanceAccountBillImageHistory> financeAccountBillImageHistories = dataService.getBillImageHistoryByIds(user.getOid(),null,financeAccountBillHistory.getId());

                        map.put("source", 4);
                        map.put("oldDetail", financeReturn);
                        map.put("newDetail", financeAccountBillHistory);
                        map.put("oldBillImages", financeAccountBillImages);
                        map.put("newBillImageHistories", financeAccountBillImageHistories);
                    } else if (approvalProcess.getType() == 5) {  //5-数据来源为支出中的转账支票(内部的)
                        if (financeAccountBill.getCheque() != null) {
                            FinanceChequeDetail financeChequeDetail = financeAccountBill.getCheque();
//                            financeChequeDetail.setBillPeriod(financeAccountBill.getBillPeriod());
//                            financeChequeDetail.setBillCat(financeAccountBill.getBillCat());
                            financeChequeDetail.setBillQuantity(financeAccountBill.getBillQuantity());
                            financeChequeDetail.setBillAmount(financeAccountBill.getBillAmount());
                            map.put("source", 5);
                            map.put("oldDetail", financeChequeDetail);
                            map.put("newDetail", financeAccountBillHistory);

                            List<FinanceAccountBillImage> financeAccountBillImages = dataService.getBillImageByIds(user.getOid(),null,financeAccountBill.getId());
                            List<FinanceAccountBillImageHistory> financeAccountBillImageHistories = dataService.getBillImageHistoryByIds(user.getOid(),null,financeAccountBillHistory.getId());
                            map.put("oldBillImages", financeAccountBillImages);
                            map.put("newBillImageHistories", financeAccountBillImageHistories);
                        }
                    }
                    map.put("oldAccountBill", financeAccountBill); // 现金支票/内部支票和承兑汇票/外部支票的信息，还有factDate【收款单位的信息】

                }
                map.put("approvalProcess", approvalProcess);
            }else {
                map.put("content", "不是本机构数据，不可查看");
            }
        }else {
            map.put("approvalProcess","");
        }

        System.out.println("查看详情结束："+(new Date().getTime()-beginDebug.getTime()));
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/getUpdateDetail",null,null,null,null,JSON.toJSONString(map));

        return new JsonResult(1,map);
    }
    
    /**
     *<AUTHOR>
     *@date 2017/5/25 16:07
     * @date 2019/8/27 11:45
     * @date 2021/11/29 (1.179财务优化2101)
     * 纯现金/银行转账修改申请的审批--批准/驳回
     * 修改字段：1-类别(Genre)【categoryDesc（当类别为其他时候的说明）】 2-摘要(Summary) 3-收入(credit) 4-支出(debit) 5-用途(purpose) 6-经手人(auditorName) 7-付款单位(OppositeCorp)【stakeholder(干系人ID/收款单位id)】 8-备注(memo)
     *           9-到账时间(receiveAccountDate) 10-账户id(accountId)[10-收款/付款银行(accountBank) ]  14-票面金额(billAmount)
     *           支出中：12-票据数量(billQuantity) 15-stakeholderCategory(类别:1-供应商,2-员工,3-自行录入) 16-billDate(票据日期) 17-factDate(实际(付款/支出)日期')
     *  1.179去掉的字段：11-票据类型(billCat)  13-票据所属月份(billPeriod 1-本月票据,2-非本月票据)
     * state 1-批准 0-驳回
    */
    @ResponseBody
    @RequestMapping("/approvalCash.do")
    @MessageMapping("/approvalCash")
    public JsonResult approvalCash(String json,String sessionid,User user) {
        JSONObject jsonObject = JSONObject.parseObject(json);
        Integer approvalProcessId = jsonObject.getInteger("approvalProcessId");  //审批流程id
        Integer state = jsonObject.getInteger("state");  //1-批准 0-驳回
        String reason = jsonObject.getString("reason");  //驳回理由
        Map<String,Object> map = financeUpdateService.approvalCash(user.getUserID(),approvalProcessId,state,reason);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/approvalResults",null,null,null,null,JSON.toJSONString(map));
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2017/5/31 13:44
     * @date 2019/8/27 14:49
     *承兑汇票和转账支票中的外部支票的修改申请的审批
     * 修改说明: 1-存入银行账户[存入银行账户的id(accountId) 存入银行账户(account) 存入银行的银行名称(saveBankName)] 2-存入时间(depositDate)
     *          3-存入经手人(depositorName) 4-到账时间(receiveAccountDate)
     * state 1-批准 0-驳回
    */
    @ResponseBody
    @RequestMapping("/approvalReturn.do")
    @MessageMapping("/approvalReturn")
    public JsonResult approvalReturn(String json,String sessionid,User user) {
        JSONObject jsonObject = JSONObject.parseObject(json);
        Integer approvalProcessId = jsonObject.getInteger("approvalProcessId");  //审批流程id
        Integer state = jsonObject.getInteger("state");  //1-批准 0-驳回
        String reason = jsonObject.getString("reason");  //驳回理由

        Map<String,Object> map = financeUpdateService.approvalReturn(user.getUserID(),approvalProcessId,state,reason);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/approvalResults",null,null,null,null,JSON.toJSONString(map));
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2017/6/1 17:49
     * @date 2019/8/27 16:49
     * @date 2021/11/29 (1.179财务优化2101)
     *数据来源为支出中的转账支票(内部的转账支票或者改为外部的汇款票据)的修改申请审批
     * 修改说明：3-摘要(summary) 4-票据数量(billQuantity) 5-金额(amount)
     *           6-用途(purpose) 7-经手人(financialHandling) 8-支出方式(type)[1-转帐支票(内部的)，2-现金汇票 3-承兑汇票(仅在此处用) 4-外部的转账支票(仅在此处用)]
     *           9-银行账户(accountId){银行名称(bankName) 银行账号(account)} 10-支票号(chequeNo) 11-支票到期日(expireDate) 12-收款单位(receiveCorp)【stakeholder(干系人ID，收款单位id)】
     *           13-接收日期(receiveDate) 14-接收经手人(receiver) 15-支付经手人(operator) 16-备注(memo) 17-票面金额(billAmount)
     *           18-stakeholderCategory(类别:1-供应商,2-员工,3-自行录入) 19-billDate(票据日期) 20-Date factDate(实际(付款/支出)日期')
     * chequeId:修改后使用的支票id
     * 去掉：1-票据种类(billCat) 2-票据所属月份(billPeriod)
     *state 1-批准 0-驳回
    */
    @ResponseBody
    @RequestMapping("/approvalBankTransfer.do")
    @MessageMapping("/approvalBankTransfer")
    public JsonResult approvalBankTransfer(String json,String sessionid,User user) {

        JSONObject jsonObject = JSONObject.parseObject(json);
        Integer approvalProcessId = jsonObject.getInteger("approvalProcessId");  //审批流程id
        Integer state = jsonObject.getInteger("state");  //1-批准 0-驳回
        String reason = jsonObject.getString("reason");  //驳回理由

        Map<String,Object> map = financeUpdateService.approvalBankTransfer(user.getUserID(),approvalProcessId,state,reason);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/approvalResults",null,null,null,null,JSON.toJSONString(map));
        return new JsonResult(1,map);
    }


    /**
     * <AUTHOR>
     * @Date 2017/6/1 10:15
     * @date 2019/8/27 17:49
     * 内部非支出性转账 批准 驳回
     * 修改数据：1-chequeNo(支票号)[1-支票id(ChequeId)] 2-收入(credit) 3-支出(debit) 4-业务发生时间(auditDate) 5-备注(memo)
     */
    @ResponseBody
    @RequestMapping("/approvalTransferAccounts.do")
    @MessageMapping("/approvalTransferAccounts")
    public JsonResult approvalTransferAccounts(String json,String sessionid,User user) {

        JSONObject jsonObject = JSONObject.parseObject(json);
//        String sessionid = jsonObject.getString("session"); //浏览器sessionID
//        Integer userId = jsonObject.getInteger("userId");  //登录人id
        Integer approvalProcessId = jsonObject.getInteger("approvalProcessId");  //审批流程id
        Integer state = jsonObject.getInteger("state");  //1-批准 0-驳回
        String reason = jsonObject.getString("reason");  //驳回理由

        Map<String,Object> map = financeUpdateService.approvalTransferAccounts(user.getUserID(),approvalProcessId,state,reason);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/approvalResults",null,null,null,null,JSON.toJSONString(map));
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2017/5/18 15:32
     *数据来源为报销的修改申请(1.78版本报销的数据不可以修改)
     * 修改数据：1-费用类别(feeCat) 2-票据种类(billCat) 3-票据所属月份(billDate) 4-摘要(summary) 5-用途(purpose) 6-票据数量(billQuantity)
     *           7-金额(amount) 8-备注(memo) 9-附件() 10-票面金额(billAmount)
     * type 修改附件(1-不修改附件 2-删除附件 3-新增附件 4-即删除又新增附件)
     * pictures:当附件中有图片时传图片的路径
     */
    @RequestMapping("/updatePerReim.do")
    public String updatePerReim(User user,PersonnelReimburse personnelReimburse, String[] pictures, Integer type, HttpServletRequest request, @RequestParam(value = "files",required = false) MultipartFile[] files) throws IOException {
        Map<String,Object> map = new HashMap<>();
        PersonnelReimbursetAttachmentHistory prah = new PersonnelReimbursetAttachmentHistory();  //附件历史表
        PersonnelReimburseHistory prh = new PersonnelReimburseHistory();  //报销历史表
        PersonnelReimburse oldPer = new PersonnelReimburse();
        AccountDetail accountDetail = new AccountDetail();
        FinanceAccount financeAccount = new FinanceAccount();
        Double balance = 0.0;  // 余额
        String des = "";
        String descriptions = "";  //消息中的申请事件使用
        if (personnelReimburse.getId()!=null){
            oldPer = personnelReimburseService.personnelReimburseById(personnelReimburse.getId());
            accountDetail = dataService.getOneDetail(oldPer.getDetailId());
            financeAccount = accountDetail.getAccountId();

            //校验数据是否已修改
            if (!"".equals(oldPer.getModityStatus()) && "2".equals(oldPer.getModityStatus())) {
                map.put("state",3);  //此数据已修改
            }else {
                if (!personnelReimburse.getFeeCat().equals(oldPer.getFeeCat())) {
                    CodeCategory codeCategory=codeService.getCodeCategoryByNameOid(user.getOid(),"费用类别");
                    if (codeCategory!=null){
                        List<Code> feeCats = codeService.getCodesByCategory(codeCategory.getId());
                        for (Code code:feeCats) {
                            if (code.getId()==personnelReimburse.getFeeCat()){
                                prh.setFeeCat(personnelReimburse.getFeeCat());
                                prh.setFeeCatName(code.getName());
                                des += "1" + ";";
                                descriptions += "费用类别" + "、";
                            }
                        }
                    }
                } else {
                    prh.setFeeCat(oldPer.getFeeCat());
                    prh.setFeeCatName(oldPer.getFeeCatName());
                }
                if (!"".equals(personnelReimburse.getBillCatName()) && !personnelReimburse.getBillCatName().equals(oldPer.getBillCatName())) {
                    CodeCategory codeCategory=codeService.getCodeCategoryByNameOid(user.getOid(),"票据种类");
                    if (codeCategory!=null){
                        List<Code> billCats = codeService.getCodesByCategory(codeCategory.getId());
                        for (Code code:billCats) {
                            if (!"".equals(code.getName()) && code.getName().equals(personnelReimburse.getBillCatName())){
                                prh.setBillCat(code.getId());
                                prh.setBillCatName(personnelReimburse.getBillCatName());
                                des += "2" + ";";
                                descriptions += "票据种类" + "、";
                            }
                        }
                    }
                } else {
                    prh.setBillCat(oldPer.getBillCat());
                    prh.setBillCatName(oldPer.getBillCatName());
                }
                if (!personnelReimburse.getBillDate().equals(oldPer.getBillDate().toString())) {
                    prh.setBillDate(personnelReimburse.getBillDate());
                    des += "3" + ";";
                    descriptions += "票据所属月份" + "、";
                } else {
                    prh.setBillDate(oldPer.getBillDate());
                }
                if (!personnelReimburse.getSummary().equals(oldPer.getSummary())) {
                    prh.setSummary(personnelReimburse.getSummary());
                    des += "4" + ";";
                    descriptions += "摘要" + "、";
                } else {
                    prh.setSummary(oldPer.getSummary());
                }
                if (!personnelReimburse.getPurpose().equals(oldPer.getPurpose())) {
                    prh.setPurpose(personnelReimburse.getPurpose());
                    des += "5" + ";";
                    descriptions += "用途" + "、";
                } else {
                    prh.setPurpose(oldPer.getPurpose());
                }
                if (!personnelReimburse.getBillQuantity().equals(oldPer.getBillQuantity())) {
                    prh.setBillQuantity(personnelReimburse.getBillQuantity());
                    des += "6" + ";";
                    descriptions += "票据数量" + "、";
                } else {
                    prh.setBillQuantity(oldPer.getBillQuantity());
                }
                if (personnelReimburse.getAmount().doubleValue() != oldPer.getAmount().doubleValue()) {
                    prh.setAmount(personnelReimburse.getAmount());
                    balance = financeAccount.getBalance().add(oldPer.getAmount()).subtract(personnelReimburse.getAmount()).doubleValue();
                    des += "7" + ";";
                    descriptions += "金额" + "、";
                } else {
                    prh.setAmount(oldPer.getAmount());
                }
                if (!personnelReimburse.getMemo().equals(oldPer.getMemo())) {
                    prh.setMemo(personnelReimburse.getMemo());
                    des += "8" + ";";
                    descriptions += "备注" + "、";
                } else {
                    prh.setMemo(oldPer.getMemo());
                }
                if (personnelReimburse.getBillAmount()!=null && personnelReimburse.getBillAmount().doubleValue() != oldPer.getBillAmount().doubleValue()){
                    prh.setBillAmount(personnelReimburse.getBillAmount());
                    des += "10" + ";";
                    descriptions += "票面金额" + "、";
                }else {
                    prh.setBillAmount(oldPer.getBillAmount());
                }
                prh.setCreateDate(new Date());
                prh.setApproveStatus("1");
                prh.setReimburseId(personnelReimburse.getId());

                //判断是否余额不足
                if (balance >= 0) {
                    personnelReimburseService.addPersonnelReimburseHistory(prh);

                    oldPer.setModityStatus("2");
                    personnelReimburseService.updatePersonnelReimburse(oldPer);

                    accountDetail.setModityStatus("2");
                    accountService.updateAccountDetail(accountDetail);

                    /**附件是否有修改*/
                    if (type != 1) {
                        if (type == 2 || type == 4) {
                            if (pictures != null) {
                                for (int i = 0; i < pictures.length; i++) {
                                    prah.setPath(pictures[i]);
                                    prah.setReimbursetH(prh);
                                    personnelReimburseService.addPersonnelReimburseAttachmentHistory(prah);
                                }
                            }
                        }
                        if (type == 3 || type == 4) {
                            if (files == null || "".equals(files)) {
                                map.put("result", "文件为空");//文件为空
               /*  map.put("result","文件为空");*/
                            } else {
                                List fileTypes = new ArrayList();//可以上传的图片类型
                                fileTypes.add("jpg");
                                fileTypes.add("jpeg");
                                fileTypes.add("bmp");
                                fileTypes.add("gif");
                                fileTypes.add("png");
                                String path = request.getServletContext().getRealPath("/upload");//项目路径
//                                String datepath = new SimpleDateFormat("/yyyy/MM/dd/").format(new Date());//当前时间路径
                                String datepath = NewDateUtils.dateToString(new Date(),"/yyyy/MM/dd/");//当前时间路径
                                String imgName = "";
                                List<PersonnelReimbursetAttachmentHistory> lsi = new ArrayList<>();
                                for (MultipartFile f : files) {
                                    imgName = f.getOriginalFilename();
                                    String ext = imgName.substring(imgName.lastIndexOf(".") + 1, imgName.length());
                                    //对扩展名进行小写转换
                                    ext = ext.toLowerCase();
                                    if (fileTypes.contains(ext)) {
                                        PersonnelReimbursetAttachmentHistory prah1 = new PersonnelReimbursetAttachmentHistory();

                                        FileUtils.copyInputStreamToFile(f.getInputStream(), new File(path + datepath + imgName));//如果扩展名属于允许上传的类型，则创建文件
                                        prah1.setPath("upload" + datepath + imgName);
                                        prah1.setReimbursetH(prh);//报销历史ID
                                        personnelReimburseService.addPersonnelReimburseAttachmentHistory(prah1);
                                        lsi.add(prah1);
                                    }
                                }
                                map.put("uploadSucess", "上传成功");//上传成功
                                map.put("personnelReimbursetAttachment", lsi);
                            }
                        }
                        des += "9" + ";";
                        descriptions += "附件" + "、";
                    } else {
                        if (pictures != null) {
                            for (int i = 0; i < pictures.length; i++) {
                                prah.setPath(pictures[i]);
                                prah.setReimbursetH(prh);
                                personnelReimburseService.addPersonnelReimburseAttachmentHistory(prah);
                            }
                        }
                    }

                    //审批流程
                    ApprovalProcess approvalProcess = new ApprovalProcess();
                    User administrators = userService.getUserByRoleCode(user.getOid(), "super");  //查找机构下的超管
                    approvalProcess.setLevel(1);  //审批级次
                    approvalProcess.setApproveStatus("1");  //审批状态
                    approvalProcess.setToUser(administrators.getUserID());  //审批人
                    approvalProcess.setBusiness(oldPer.getId()); //数据修改前的id（财务修改 修改前的id（本表中））
                    approvalProcess.setOldId(oldPer.getId());  //财务修改 修改前的id（本表中）
                    approvalProcess.setNewId(prh.getId()); //财务修改 修改中的id（历史表中的id）
                    approvalProcess.setBusinessType(1); //业务类型  1-财务修改，2- 加班，3-请假
                    approvalProcess.setToUserName("超管");  //审批人总称
                    approvalProcess.setUserName(administrators.getUserName());  //审批人名字
                    approvalProcess.setUpdateDes(des);  //修改说明(需要修改的字段使用数字代替)
                    if (!"".equals(descriptions)){
                        descriptions = descriptions.substring(0,descriptions.length()-1);
                    }
                    approvalProcess.setDescription("关于" + descriptions + "的修改申请");  //修改记录中的申请事件
                    approvalProcess.setAskName(user.getUserName());  //申请人
                    approvalProcess.setFromUser(user.getUserID());  //申请人id
                    approvalProcess.setType(3);  //状态 1-纯现金/银行转账 2-内部非支出性转账 3-报销 4-承兑汇票和转账支票中的外部支票 5-数据来源为支出中的转账支票(内部的)
                    approvalProcess.setCreateDate(new Date());
                    approvalProcessService.saveApprovalProcess(approvalProcess);

                    //以下为消息
                    UserMessage userMessage = new UserMessage();
                    userMessage.setUser(user);
                    userMessage.setHandleId(administrators.getUserID().toString()); //审批人id
                    userMessage.setHandleName(administrators.getUserName());  //审批人姓名
                    userMessage.setMessageId(approvalProcess.getId());
                    userMessage.setAskName(user.getUserName()); //请求人
                    userMessage.setEventType("修改申请");
                    userMessage.setIllustrate("关于" + descriptions + "的修改申请");
                    userMessage.setMessageType("1");
                    userMessage.setApprovalStatus(1);
                    userMessage.setState(1);
                    userMessage.setCreateDate(new Date());
                    userMessage.setReceiveUserId(administrators.getUserID()); //接收消息人
                    userMessageService.addUserMassage(userMessage);

                    map.put("state", 1);
                } else {
                    map.put("state", 2);  //余额不足
                }
            }
        }else {
            map.put("state",0);
        }
        return "redirect:/sys/toAdminIndex.do?state="+map.get("state")+"";
    }

    /**
     * <AUTHOR>
     * @Date 2017/6/5 15:03
     * 批准驳回报销修改申请
     *  * 修改数据：1-费用类别(feeCat) 2-票据种类(billCat) 3-票据所属月份(billDate) 4-摘要(summary) 5-用途(purpose) 6-票据数量(billQuantity)
     *           7-金额(amount) 8-备注(memo) 9-附件()
     * type 修改附件(1-不修改附件 2-删除附件 3-新增附件 4-即删除又新增附件)
     * pictures:当附件中有图片时传图片的路径
     */
    @ResponseBody
    @RequestMapping("/approvalPerReim.do")
    public void approvalPerReim(User user, Integer state, Integer approvalProcesId, HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();
        if (approvalProcesId!=null&&state!=null) {
            ApprovalProcess approvalProcess = approvalProcessService.getApprovalProcessById(approvalProcesId);
            PersonnelReimburse personnelReimburse = personnelReimburseService.personnelReimburseById(approvalProcess.getOldId());
            AccountDetail detail = accountService.getAccountDetailById(personnelReimburse.getDetailId());
            if (1 == state) {
                PersonnelReimburseHistory newPersonnelReimburse = personnelReimburseService.getPersonnelReimburseHistoryById(approvalProcess.getNewId());

                personnelReimburse.setFeeCat(newPersonnelReimburse.getFeeCat());
                personnelReimburse.setBillCat(newPersonnelReimburse.getBillCat());
                personnelReimburse.setBillDate(newPersonnelReimburse.getBillDate());
                personnelReimburse.setSummary(newPersonnelReimburse.getSummary());
                personnelReimburse.setPurpose(newPersonnelReimburse.getPurpose());
                personnelReimburse.setBillQuantity(newPersonnelReimburse.getBillQuantity());
                personnelReimburse.setMemo(newPersonnelReimburse.getMemo());

                if (newPersonnelReimburse.getPersonnelReimbursetAttachmentHistoryHashSet().size() > 0) {
                    for (PersonnelReimbursetAttachment p : personnelReimburse.getPersonnelReimbursetAttachmentHashSet()) {
                        personnelReimburseService.deletePersonnelReimburseAttachment(p);
                    }
                    for (PersonnelReimbursetAttachmentHistory ph : newPersonnelReimburse.getPersonnelReimbursetAttachmentHistoryHashSet()) {
                        PersonnelReimbursetAttachment pra = new PersonnelReimbursetAttachment();
                        pra.setPath(ph.getPath());
                        pra.setReimburset(personnelReimburse);
                        personnelReimbursetAttachmentService.addPersonnelReimbursetAttachment(pra);
                    }
                }
                if (personnelReimburse.getAmount().doubleValue() != newPersonnelReimburse.getAmount().doubleValue()) {
                    FinanceAccount financeAccount = accountService.getFinanceAccountById(detail.getAccountId_());
                    AccountPeriod yue = accountService.getPeroidByType(financeAccount.getOrg().getId(), financeAccount.getAccountType(), financeAccount.getId(), 1);
                    AccountPeriod ri = accountService.getPeroidByType(financeAccount.getOrg().getId(), financeAccount.getAccountType(), financeAccount.getId(), 2);

                    AccountDetail chong = new AccountDetail();
                    chong.setModityStatus("2");
                    chong.setType(detail.getType());
                    chong.setAccountantStatus(detail.getAccountantStatus());
                    chong.setAccountBank(detail.getAccountBank());
                    chong.setAccount(detail.getAccount());
                    chong.setAccountId(detail.getAccountId());
                    chong.setApproveItem(detail.getApproveItem());
                    chong.setApplyMemo(detail.getApplyMemo());
                    chong.setBillDetail(detail.getBillDetail());
                    chong.setAuditor(detail.getAuditor());
                    chong.setAuditorName(detail.getAuditorName());
                    chong.setBillCat(detail.getBillCat());
                    chong.setChequeId(detail.getChequeId());
                    chong.setChequeNo(detail.getChequeNo());
                    chong.setMethod(detail.getMethod());
                    chong.setType(detail.getType());
                    chong.setFid(detail.getFid());
                    chong.setAuditDate(new Date());
                    chong.setMemo(detail.getMemo());
                    chong.setCreateDate(new Date());
                    chong.setPersonnelReimburse(detail.getPersonnelReimburse());
                    chong.setSummary(detail.getSummary());
                    chong.setOrg(detail.getOrg());

                    AccountDetail accountDetail = new AccountDetail();
                    accountDetail.setModityStatus("1");
                    accountDetail.setType(detail.getType());
                    accountDetail.setAccountantStatus(detail.getAccountantStatus());
                    accountDetail.setAccountBank(detail.getAccountBank());
                    accountDetail.setAccount(detail.getAccount());
                    accountDetail.setAccountId(detail.getAccountId());
                    accountDetail.setApproveItem(detail.getApproveItem());
                    accountDetail.setApplyMemo(detail.getApplyMemo());
                    accountDetail.setBillDetail(detail.getBillDetail());
                    accountDetail.setAuditor(detail.getAuditor());
                    accountDetail.setAuditorName(detail.getAuditorName());
                    accountDetail.setBillCat(detail.getBillCat());
                    accountDetail.setChequeId(detail.getChequeId());
                    accountDetail.setChequeNo(detail.getChequeNo());
                    accountDetail.setMethod(detail.getMethod());
                    accountDetail.setType(detail.getType());
                    accountDetail.setFid(detail.getFid());
                    accountDetail.setAuditDate(new Date());
                    accountDetail.setMemo(detail.getMemo());
                    accountDetail.setCreateDate(new Date());
                    accountDetail.setPersonnelReimburse(detail.getPersonnelReimburse());
                    accountDetail.setSummary(detail.getSummary());
                    accountDetail.setOrg(detail.getOrg());

                    accountDetail.setBalance(financeAccount.getBalance().add(personnelReimburse.getAmount()).subtract(newPersonnelReimburse.getAmount()));
                    accountDetail.setDebit(newPersonnelReimburse.getAmount());//新支出

                    chong.setBalance(financeAccount.getBalance().add(personnelReimburse.getAmount()));
                    chong.setDebit(new BigDecimal(0).subtract(personnelReimburse.getAmount()));

                    financeAccount.setBalance(financeAccount.getBalance().add(personnelReimburse.getAmount()).subtract(newPersonnelReimburse.getAmount()));
                    financeAccount.setDebit(financeAccount.getDebit().subtract(personnelReimburse.getAmount()).add(newPersonnelReimburse.getAmount()));

                    yue.setBalance(financeAccount.getBalance());
                    yue.setDebit(yue.getDebit().subtract(personnelReimburse.getAmount()).add(newPersonnelReimburse.getAmount()));

                    ri.setBalance(financeAccount.getBalance());
                    ri.setDebit(ri.getDebit().subtract(personnelReimburse.getAmount()).add(newPersonnelReimburse.getAmount()));

                    if (financeAccount.getBalance().doubleValue() >= 0) {
                        detail.setModityStatus("2");//已冲账
                        accountService.updateAccountDetail(detail);

                        accountService.saveAccountDetail(chong);
                        accountService.saveAccountDetail(accountDetail);

                        accountService.updateAccountPeroid(yue);
                        accountService.updateAccountPeroid(ri);
                        accountService.updateFinanceAccount(financeAccount);

                        personnelReimburseService.updatePersonnelReimburse(personnelReimburse);

                        approvalProcess.setApproveStatus("2");
                        approvalProcessService.updateApprovalProcess(approvalProcess);

                        map.put("status", 1);//成功
                    } else {
                        map.put("status", 2);//余额不足
                    }
                } else {
                    map.put("status", 1);//成功

                    approvalProcess.setApproveStatus("2");
                    approvalProcessService.updateApprovalProcess(approvalProcess);

                    personnelReimburse.setModityStatus("1");
                    personnelReimburseService.updatePersonnelReimburse(personnelReimburse);

                    detail.setModityStatus("1");
                    accountService.updateAccountDetail(detail);
                }
            } else {
                approvalProcess.setApproveStatus("3");
                approvalProcessService.updateApprovalProcess(approvalProcess);

                personnelReimburse.setModityStatus("1");
                personnelReimburseService.updatePersonnelReimburse(personnelReimburse);

                detail.setModityStatus("1");
                accountService.updateAccountDetail(detail);
                map.put("status", 1);//成功
            }

            if (map.get("status").equals(1)) {
                UserMessage userMessage = userMessageService.getUserMessageByMessageId(approvalProcesId, "1");
                //   批准/驳回后此审批者查看的消息改为已读状态
                userMessage.setState(2);
                userMessage.setHandleId(user.getUserID().toString());
                userMessage.setHandleName(user.getUserName());
                //   userMessage1.setHandleReply(approveMemo);
                userMessage.setHandleTime(new Date());
                userMessageService.updateUserMassage(userMessage);

                //   批准/驳回后给申请者的消息
                UserMessage userMessage1 = new UserMessage();
                userMessage1.setUser(userMessage.getUser());
                if (state == 1) {
                    userMessage1.setApprovalStatus(2); //批准
                } else {
                    userMessage1.setApprovalStatus(3);  //驳回
                }
                userMessage1.setHandleId(approvalProcess.getToUser().toString());
                userMessage1.setMessageId(approvalProcess.getId());
                userMessage1.setEventType("审批结果");
                userMessage1.setIllustrate(userMessage.getIllustrate());
//            userMessage1.setPersonnelReimburId();
                userMessage1.setMessageType("1");
                userMessage1.setState(1);
                userMessage1.setReceiveUserId(approvalProcess.getFromUser());  //接收消息人
                userMessage1.setIsNull("1");  //为空时在请求和申请列表展示，不为空只是消息通知，不在前两个列表展示。
                userMessage1.setCreateDate(new Date());
                userMessageService.addUserMassage(userMessage1);
            }
        }else {
            map.put("status", 0);//失败
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }
}
