export function getWeekday() {
    const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    const date = new Date();
    const weekday = date.getDay();
    return weekDays[weekday];
}


export function formatCurrency (numOral, decimal = 2, split = ',') {
    /*
      parameter：
      num：格式化目标数字
      decimal：保留几位小数，默认2位
      split：千分位分隔符，默认为,
      formatCurrency(123456789.87654321, 2, ',') // 123,456,789.88
    */
    function thousandFormat (num) {
        const len = num.length
        return len <= 3 ? num : thousandFormat(num.slice(0, len - 3)) + split + num.slice(len - 3, len)
    }
    if (isFinite(numOral)) { // num是数字
        let num = numOral
        let hasFu = false
        if(numOral < 0){
            num = Math.abs(numOral)
            hasFu = true
        }
        if (num === 0) { // 为0
            return 0
        } else { // 非0
            var res = ''
            var dotIndex = String(num).indexOf('.')
            if (dotIndex === -1) { // 整数
                if (decimal === 0) {
                    res = thousandFormat(String(num))
                } else {
                    // res = thousandFormat(String(num)) + '.' + '0'.repeat(decimal)
                    res = thousandFormat(String(num))
                }
            } else { // 非整数
                // js四舍五入 Math.round()：正数时4舍5入，负数时5舍6入
                // Math.round(1.5) = 2
                // Math.round(-1.5) = -1
                // Math.round(-1.6) = -2
                // 保留decimals位小数
                const numStr = String((Math.round(num * Math.pow(10, decimal)) / Math.pow(10, decimal)).toFixed(decimal)) // 四舍五入，然后固定保留2位小数
                const decimals = numStr.slice(dotIndex, dotIndex + decimal + 1) // 截取小数位
                res = thousandFormat(numStr.slice(0, dotIndex)) + decimals
            }
            if(hasFu){
                res = '-' + res
            }
            return res
        }
    } else {
        return '--'
    }
}
// console.log('result:', moneyFormat(123456789.87654321)) // '123,456,789.88'