package cn.sphd.miners.modules.schedule.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * Created by Administrator on 2019/1/24.
 */
@Entity
@Table(name = "t_memo_job")
public class MemoJob implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="`schedule`"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer scheduleId;//日程ID

    @Column(name="job_uid"  , length=72 , nullable=true , unique=false)
    private String jobUid;   //提醒的唯一标识符,可用日程UID+提醒首次运行时间 生成，再次提醒时此值保持不变

    @Column(name="user"   , nullable=true , unique=false)
    private Integer user;//用户ID

    @Column(name="name"  , length=100 , nullable=true , unique=false)
    private String name;   //名称

    @Column(name="title"  , length=100 , nullable=true , unique=false)
    private String title;   //标题

    @Column(name="enabled"  ,length = 1 ,nullable=true , unique=false)
    private Integer enabled=1;   //指示是否启用要执行的提醒,0-否,1-是

    @Column(name="enabled_time"   , nullable=true , unique=false)
    private Date enabledTime;//启停用时间

    @Column(name="is_valid"   ,nullable=true , unique=false)
    private boolean isValid=true;   //逻辑删除标志,false-无效,true-有效

    @Column(name="type"   ,nullable=true , unique=false)
    private Integer type;   //类型：1-约定提醒(按约定规则进行的),2-再次提醒(人工选择了再次提醒)

    @Column(name="state"   ,nullable=true , unique=false)
    private Integer state;   //状态:1-已完成、2-提醒中、3-不再提醒、4-提醒已过期、5-尚未开始提醒

    @Column(name="place"  , length=255 , nullable=true , unique=false)
    private String place;   //地点

    @Column(name="description" , nullable=true , unique=false)
    private String description;   //说明

    @Column(name="version_number"   ,nullable=true , unique=false)
    private Integer versionNumber;   //版本

    @Column(name="previous_job"   ,nullable=true , unique=false)
    private Integer previousJob;   //前一job的ID(再次提醒)

    @Column(name="previous_state"   ,nullable=true , unique=false)
    private Integer previousState;   //前一状态(手工改时,记录状态变化过程)

    @Column(name="trace_path"  , length=255 , nullable=true , unique=false)
    private String tracePath;   //记录提醒ID的历史路径,以逗号分隔,按时间前后排列

    @Column(name="run_number"   ,nullable=true , unique=false)
    private Integer runNumber=0;   //运行次数

    @Column(name="notice_number"   ,nullable=true , unique=false)
    private Integer noticeNumber=0;   //提醒次数(含再次提醒)

    @Column(name="run_time"   , nullable=true , unique=false)
    private Date runTime;//运行时间

    @Column(name="run_time_path"  , length=255 , nullable=true , unique=false)
    private String runTimePath;   //运行时间序列，按先后，以逗号分隔 用于记录再次提醒中除自定义外的八种情况

    @Column(name="oper_time_path"  , length=255 , nullable=true , unique=false)
    private String operTimePath;   //再次提醒操作时间序列，按先后，以逗号分隔 用于记录再次提醒中除自定义外的八种情况

    @Column(name = "supplement", nullable=true , unique=false)
    private String supplement;//补充说明

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @JsonIgnore
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="`schedule`", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private MemoSchedule schedule;//日程集联

    @Transient
    Set<MemoScheduleImage> memoScheduleImageHashSet;

    @Transient
    private Date activeStartDate;//可以开始执行提醒的日期

    @Transient
    private Integer freqType;   //此日程的频率。1 = 仅一次,4 = 每日,8 = 每周,16 =每月,32 =每年,64 =相对于 = 每月


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(Integer scheduleId) {
        this.scheduleId = scheduleId;
    }

    public String getJobUid() {
        return jobUid;
    }

    public void setJobUid(String jobUid) {
        this.jobUid = jobUid;
    }

    public Integer getUser() {
        return user;
    }

    public void setUser(Integer user) {
        this.user = user;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    public boolean isValid() {
        return isValid;
    }

    public void setValid(boolean valid) {
        isValid = valid;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getState() {
        return state;
    }

    public String getSupplement() {
        return supplement;
    }

    public void setSupplement(String supplement) {
        this.supplement = supplement;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(Integer versionNumber) {
        this.versionNumber = versionNumber;
    }

    public Integer getPreviousJob() {
        return previousJob;
    }

    public void setPreviousJob(Integer previousJob) {
        this.previousJob = previousJob;
    }

    public Integer getPreviousState() {
        return previousState;
    }

    public void setPreviousState(Integer previousState) {
        this.previousState = previousState;
    }

    public String getTracePath() {
        return tracePath;
    }

    public void setTracePath(String tracePath) {
        this.tracePath = tracePath;
    }

    public Integer getRunNumber() {
        return runNumber;
    }

    public void setRunNumber(Integer runNumber) {
        this.runNumber = runNumber;
    }

    public Integer getNoticeNumber() {
        return noticeNumber;
    }

    public void setNoticeNumber(Integer noticeNumber) {
        this.noticeNumber = noticeNumber;
    }

    public Date getRunTime() {
        return runTime;
    }

    public void setRunTime(Date runTime) {
        this.runTime = runTime;
    }

    public String getRunTimePath() {
        return runTimePath;
    }

    public void setRunTimePath(String runTimePath) {
        this.runTimePath = runTimePath;
    }

    public String getOperTimePath() {
        return operTimePath;
    }

    public void setOperTimePath(String operTimePath) {
        this.operTimePath = operTimePath;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public MemoSchedule getSchedule() {
        return schedule;
    }

    public void setSchedule(MemoSchedule schedule) {
        this.schedule = schedule;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getPlace() {
        return place;
    }

    public void setPlace(String place) {
        this.place = place;
    }

    public Integer getFreqType() {
        return freqType;
    }

    public void setFreqType(Integer freqType) {
        this.freqType = freqType;
    }

    public Set<MemoScheduleImage> getMemoScheduleImageHashSet() {
        return memoScheduleImageHashSet;
    }

    public void setMemoScheduleImageHashSet(Set<MemoScheduleImage> memoScheduleImageHashSet) {
        this.memoScheduleImageHashSet = memoScheduleImageHashSet;
    }

    public Date getActiveStartDate() {
        return activeStartDate;
    }

    public void setActiveStartDate(Date activeStartDate) {
        this.activeStartDate = activeStartDate;
    }


}
