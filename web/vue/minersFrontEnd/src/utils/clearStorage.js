/**
 * 使用此函数清理localStorage，注销后需要保存的变量名称放在unclearStorageNames中
 */
export function clearStorage() {//清理缓存并保留unclearStorageNames中的属性
    const unclearStorageNames = [

    ]
    let name
    for (let i = localStorage.length - 1; i >= 0; i--) {
        if (!unclearStorageNames.includes(name = localStorage.key(i))) {
            // console.log('localStorage.removeItem(name)', i, name, localStorage.key(i))
            localStorage.removeItem(name);
        }
    }
    // let unclearStorages = {}
    // let value
    // unclearStorageNames.forEach(name => {
    //     if((value = localStorage.getItem(name))!=null) {
    //         unclearStorages[name] = value
    //     }
    // })
    // localStorage.clear()
    // Object.keys(unclearStorages).forEach(name => {
    //     localStorage.setItem(name, unclearStorages[name])
    // })
}