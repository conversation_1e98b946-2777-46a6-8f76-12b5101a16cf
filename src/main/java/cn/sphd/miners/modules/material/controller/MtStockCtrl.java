package cn.sphd.miners.modules.material.controller;

import cn.sphd.miners.modules.material.entity.MtMaterialApply;
import cn.sphd.miners.modules.material.entity.MtStock;
import cn.sphd.miners.modules.material.entity.MtStockInfo;
import cn.sphd.miners.modules.material.service.MtMaterialApplyService;
import cn.sphd.miners.modules.material.service.MtStockService;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by 赵应 on 2017/8/7.
 */
@Controller
@RequestMapping("/mts")
public class MtStockCtrl {

    @Autowired
    MtStockService stockService;
    @Autowired
    MtMaterialApplyService mtMaterialApplyService;

    @RequestMapping("/acceptIndex")
    public String acceptIndex(){
        return "";
    }



    //入库受理列表接口
    @RequestMapping("/typeList.do")
    public Map accept(User user,Integer per,Integer state,Integer type,Integer qualified){
        Integer oid= user.getOid();
        Map records = stockService.getMtStocksByStateAndType(state,type,qualified,oid,per);

        return records;
    }

    //type:1:数量无误-入库---2:数量不对-修改数量

    @RequestMapping("/amountSure.do")
    public Map amountSure(Integer type,Integer stockId,User user){

        Integer oid= user.getOid();

        if(type==1){
            MtStock mtStock = stockService.getByIdAndOid(stockId,oid);

            MtStockInfo stockInfo = stockService.getMtStockInfoByMId(mtStock.getMaterial());//得到当前库存情况

            if(stockInfo==null){//无库存情况

            }else {
                stockInfo.setStock(stockInfo.getStock().add(mtStock.getInPlan()));
                stockInfo.setUpdateDate(new Date());
                stockInfo.setUpdateName(user.getUserName());
                stockInfo.setUpdator(user.getUserID());
                stockInfo.setStockPosition(mtStock.getStorageLocation());
            }


            mtStock.setState("7");
            stockService.updateMtStock(mtStock);
            stockService.updateMtStockInfo(stockInfo);

        }else if(type==2){


        }

        return null;
    }


    //进行让步申请
    @RequestMapping("/concession")
    public Map concession(User user,Integer parent,String reason){


        Integer oid= user.getOid();
        MtMaterialApply apply = new MtMaterialApply();

        apply.setApplicant(user.getUserID());
        apply.setApplicantName(user.getUserName());
        apply.setParent(parent);
        apply.setType("2");
        apply.setReason(reason);
        apply.setAction("");
        apply.setApplyDate(new Date());
        apply.setApplyType("");
        apply.setState("2");
        apply.setCreator(user.getUserID());
        apply.setCreateName(user.getCreateName());
        apply.setCreateDate(new Date());
        apply.setOid(oid);

        mtMaterialApplyService.addMtMaterialApply(apply);

        Map map = new HashMap();
        return map;
    }

}
