import {request} from "@/utils/request";
/* ------------------销售合同------------------ */

// creator: 张旭博，2024-08-06 02:27:24， 获取客户合同列表
export function getSalesContractList() {
    return request({
        url: '/sales/getSalesContractList.do'
    })
}

// creator: 张旭博，2024-08-06 02:27:24， 获取全部合同(正常 终止 到期)
export function getAllContractList(type) {
    return request({
        url: '/sales/getListAllContractBase.do',
        data: {
            type: type // 1-正常 2-终止 3-到期
        }
    })
}

// creator: 张旭博，2024-08-06 02:27:24， 获取客户下的合同列表(正常 终止 到期)
export function getContractByCustomer(type, id) {
    let data = {
        type: type
    }
    if (id) data.id = id
    return request({
        url: '/sales/listContractBase.do',
        data: {
            customer: id, // 客户id
            type: type // 1-正常 2-终止 3-到期
        }
    })
}

// creator: 张旭博，2024-08-06 02:27:24， 获取客户下的合同详情
export function getContractBase(id) {
    return request({
        url: '/sales/contractBaseMes.do',
        data: {id: id}
    })
}

// creator: 张旭博，2024-08-06 02:27:24， 获取所有商品（通用，专属）
export function getAllGoods(id) {
    let data = id?{customer: id}:{}
    return request({
        url: '/sales/insertCommodityByTypeForContract.do',
        data: data
    })
}


// creator: 张旭博，2024-08-06 02:27:24， 确定新增合同
export function addContract(data) {
    return request({
        url: '/sales/insertContractBase.do',
        data: data
    })
}

// creator: 张旭博，2024-08-06 02:27:24， 确定修改合同
export function changeContract(data) {
    return request({
        url: '/sales/upContractBase.do',
        data: data
    })
}
// creator: 张旭博，2024-08-06 02:27:24， 确定续约合同
export function renewContract(data) {
    return request({
        url: '/sales/renewalContractForCommodity.do',
        data: data
    })
}

// creator: 张旭博，2024-08-06 02:27:24， 获取某商品的全部合同
export function getAllContractByGood(id) {
    return request({
        url: '/sales/listContractByCommodity.do',
        data: {id: id}
    })
}

// creator: 张旭博，2024-08-06 02:27:24， 确定终止合同
export function stopContract(id) {
    return request({
        url: '/sales/terminateContract.do',
        data: { id: id }
    })
}

// creator: 张旭博，2024-08-06 02:27:24， 启用合同时判断合同的状态
export function judgeRecoveryContract(id) {
    return request({
        url: '/sales/checkReStartContractState.do',
        data: {
            id: id
        }
    })
}

// creator: 张旭博，2024-08-06 02:27:24， 确定启用合同
export function recoveryContract(id, type) {
    return request({
        url: '/sales/reStartContract.do',
        data: {
            id: id,
            type: type
        }
    })
}

// creator: 张旭博，2024-08-06 02:27:24， 获取合同的修改记录
export function getContractChangeHis(id) {
    return request({
        url: '/sales/contractBaseHistory.do',
        data: {
            id: id
        }
    })
}

// creator: 张旭博，2024-08-06 02:27:24， 获取某个合同的全部续约记录
export function getContractRenewHis(id) {
    return request({
        url: '/sales/contractBaseSignRecord.do',
        data: {
            primaryId: id
        }
    })
}

// creator: 张旭博，2024-08-06 02:27:24， 获取某个合同修改记录的详情
export function getContractChangeHisDetail(id) {
    return request({
        url: '/sales/contractBaseHisMes.do',
        data: {
            contractHisId: id
        }
    })
}

// creator: 张旭博，2024-08-06 02:27:24， 新增合同获取所有客户
export function getCustomerByAddContract() {
    return request({
        url: '/sales/getCustomerByAddContract.do'
    })
}

/* ------------------类别管理------------------ */
// creator: 张旭博，2025-06-10 10:49:11， 一级类别列表
export function getFirstCategoryListByOid(data) {
    return request({
        url: '/category/getFirstCategoryListByOid.do',
        data: data
    })
}

// creator: 张旭博，2025-06-10 10:49:11， 管理下一级类别列表接口（待分类不能）
export function getSonCategoryListByPid(data) {
    return request({
        url: '/category/getSonCategoryListByPid.do',
        data: data
    })
}

// creator: 张旭博，2025-06-10 10:49:11， 新增类别 类别从属列表
export function getPathCategoryListById(id) {
    return request({
        url: '/category/getPathCategoryListById.do',
        data: {
            id: id
        }
    })
}


// creator: 张旭博，2025-06-10 10:49:11， 新增类别 保存接口 同级不能同名/
export function addPdCategory(data) {
    return request({
        url: '/category/addPdCategory.do',
        data: data
    })
}

// creator: 张旭博，2025-06-10 10:49:11， 修改类别 保存接口 同级不能同名
export function editPdCategory(data) {
    return request({
        url: '/category/editPdCategory.do',
        data: data
    })
}

// creator: 张旭博，2025-06-10 10:49:11， 删除类别 待分类不能删除
export function deletePdCategory(id) {
    return request({
        url: '/category/deletePdCategory.do',
        data: {
            id: id
        }
    })
}