import Vue from 'vue'
import Router from 'vue-router'
import base from '../view/base'
import home from '../view/home'
import resume from '../view/resume'
import error from '../view/error'
import resumeRecord from '../view/resumeRecord'
import otherData from '../view/otherData'
import regist from '../view/regist'
import login from '../view/login'
import wxShare from '../view/wxShare'
import wxShareDetail from '../view/wxShareDetail'

Vue.use(Router)

export default new Router({
  routes: [
    {
      path: '/',
      name: 'base',
      component: base
    }, {
      path: '/item/:item',
      name: 'base',
      component: base
    }, {
      path: '/error',
      name: 'error',
      component: error
    }, {
      path: '/home',
      name: 'home',
      component: home
    }, {
      path: '/resume/:id',
      name: 'resume',
      component: resume
    }, {
      path: '/resumeRecord',
      name: 'resumeRecord',
      component: resumeRecord
    }, {
      path: '/otherData/:id',
      name: 'otherData',
      component: otherData
    }, {
      path: '/regist',
      name: 'regist',
      component: regist
    }, {
      path: '/login',
      name: 'login',
      component: login
    }, {
      path: '/wxShare/:id/:userid/:oid',
      name: 'wxShare',
      component: wxShare
    }, {
      path: '/wxShareDetail/:id',
      name: 'wxShareDetail',
      component: wxShareDetail
    }
  ]
})
