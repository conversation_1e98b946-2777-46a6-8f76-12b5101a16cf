var change_ver="";

$(function(){
	App.init();

	//获取第一层类别
	$("#secondkuang").hide();
	$("#allFile").hide();
	$(".back").hide();
	$(".cs_name").hide();
	$("#intsecond").hide();
	$("#intthird").hide();
	$("#intforth").hide();
	$("#addOch_id").hide();
	$("#addFirstOp_size").hide();
	$("#category_id").hide();
	$("#fi_amount").hide();
	$("#addOseefile_size").hide();
	$("#addOseefile_category").hide();
	$("#addOseefile_id").hide();
	$("#addhuanbanfile_size").hide();
	$("#addhuanbanfile_category").hide();
	$("#addhuanbanfile_id").hide();

	getFirstType();

	//在第一层类别选中第一个下拉选
	$("#addFirstOp_ttl").change(function(){
		var id=$(this).val();
		$("#addFirstOp_id").val(id);
		$("#secondkuang").hide();
		$("#threekuang").hide();
		getnexttype(id);
	});

	//在第一层类别选中第二个下拉选
	$("#addSecondOp_ttl").change(function(){
		$("#addFirstOp_id").val("");
		var id=$(this).val();
		$("#addFirstOp_id").val(id);
		$("#threekuang").hide();
		getnextonetype(id);
	});

	//在第一层类别选中第三个下拉选
	$("#addthreeOp_ttl").change(function(){
		$("#addFirstOp_id").val("");
		var id=$(this).val();
		$("#addFirstOp_id").val(id);
	});

	//在第二层类别中选中下拉选
	$("#addFirstOp_ttl").change(function(){
		$("#addFirstOp_id").val("");
		var id=$(this).val();
		$("#addFirstOp_id").val(id);
		getsecnexttype(id);
	});
	//在第二层类别中选中下拉选
	$("#addthrOp_ttl").change(function(){
		$("#addFirstOp_id").val("");
		var id=$(this).val();
		$("#addFirstOp_id").val(id);
	});
	//在第三层类别中选中下拉选
	$("#addFirstOp_ttl").change(function(){
		$("#addFirstOp_id").val("");
		var id=$(this).val();
		$("#addFirstOp_id").val(id);
	});
});





// 动态插入控件
function insertUploadyfyFirst(){
	$("#addOseefile_size").val("");
	var str = "<div id='upload'></div>";
	$("#scanUpload").html(str);
	// init Uploadfy
	$('#upload').Huploadify({
		auto:false,
		fileTypeExts:'*.doc;*.docx;*.xls;*.xlsx;*.zip;*.rar;*.apk;*.ipa;*.ppt;*.txt;*.pdf;*.png;*.jpg;*.wps;*.et;*.md;',
		// fileTypeExts:'*.*;',
		multi:true,
		formData:{"addName":$("#addName").val()},
		fileSizeLimit:314572800,  // 300M = ( 300 * 1024 * 1024 ) B
		showUploadedPercent:true,
		showUploadedSize:true,
		removeTimeout:9999999,
		uploader:"res_uploadPic.action",
		onUploadStart:function(){},
		onInit:function(){},
		onUploadComplete:function(fileObj){
			var size=fileObj.size;
			$("#addOseefile_size").val(size);

		},
		onDelete:function(file){
			console.log(''+file);
			console.log(file);
		}
	});
}
// 换版动态插入控件
function insertUploadyfyFourth(){
	$("#addhuanbanfile_size").val("");
	var str = "<div id='uploadFou'></div>";
	$("#scanUploadFou").html(str);
	// init Uploadfy
	$('#uploadFou').Huploadify({
		auto:false,
		fileTypeExts:'*.doc;*.docx;*.xls;*.xlsx;*.zip;*.rar;*.apk;*.ipa;*.ppt;*.txt;*.pdf;*.png;*.jpg;*.wps;*.et;*.md;' ,
		// fileTypeExts:'*.*;',
		multi:true,
		formData:{"addName":$("#addName").val()},
		fileSizeLimit:314572800,
		showUploadedPercent:true,
		showUploadedSize:true,
		removeTimeout:9999999,
		uploader:"res_uploadPic.action",
		onUploadStart:function(){ },
		onInit:function(){ },
		onUploadComplete:function(fileObj){
			var size=fileObj.size;
			$("#addhuanbanfile_size").val(size);
		},
		onDelete:function(file){
			console.log(''+file);
			console.log(file);
		}
	});
}

//点击上传文件
function addFile(){
    if (chargeRole('超管')) { // 0 表示超管
		bounce.show( $("#mtTip") ) ;  $("#mt_tip_ms").html("您没有此权限！"); return false ;
	}
	if($("#intfirst").children("span").html()==1){
		firstAddFile();
	}else if($("#intfirst").children("span").html()==2){
		secondAddFile();
	}else if($("#intfirst").children("span").html()==3){
		thirdAddFile();
	}else if($("#intfirst").children("span").html()==4){
		forthAddFile();
	}
}

///获取第一层列表
function getFirstType(){
	$.ajax({
		url:"${pageContext.request.contextPath}/res_getFirstType.action",
		data:{},
		Type:"post",
		dataType:"json",
		success:function(data){
			var status=data["status"];
			var list=data["list"];
			var typeSize=data["typeSize"];
			$("#typesize").append(typeSize);
			if(status==0){
				alert("登陆失效，请重新登陆！");
				location.href = "res_gonggao.action";
			}else{
				for(var i=0;i<list.length;i++){
					var id=list[i]["id"];
					var name=list[i]["name"];
					var str="<tr><td class='fileid' >"+id+"</td><td onclick='getsecondtype($(this))' class='tz'>"+name+"</td></tr>";
					$("#filetype").append(str);
				}
			}
		},
		error:function(){
			alert("连接错误，请稍后重试！");
		}
	});
}

//在第一层点击上传文件
function firstAddFile(){
	bounce.show($("#addOup"));
	$("#firstkuang").show();
	$("#secondkuang").hide();
	$("#threekuang").hide();
	$("#addOseefile").hide();

	$.ajax({
		url:"${pageContext.request.contextPath}/res_addFile.action",
		data:{},
		type:"post",
		dataType:"json",
		success:function(data){
			var list=data["list"];
			var status=data["status"];
			if(status==0){
				alert("登陆失效，请重新登陆！");
				location.href = "res_gonggao.action";
			}else if(status==1){
				$("#addFirstOp_ttl").children(":gt(0)").remove();
				$("#addFirstOp_id").val("");
				$("#addFirstOp_size").val("");
				$("#addFirstOp_number").val("");
				$("#addFirstOp_name").val("");
				$("#addFirstOp_content").val("");
				$("#addfirstOp_makeMan").val("");
				$("#addFirstOp_makeTime").val("");
				$("#addFirstOp_checkMan").val("");
				$("#addFirstOp_checkTime").val("");
				$("#addFirstOp_agreeMan").val("");
				$("#addFirstOp_agreeTime").val("");
				for(var i=0;i<list.length;i++){
					var id=list[i]["id"];
					var name=list[i]["name"];
					var str="<option value='"+ id +"'>"+ name +"</option>";
					$("#addFirstOp_ttl").append(str);
				}
				$("#seckong").show();
			}
		},
		error:function(){
		}
	});
}

//第一层中若是有子类的类型显示子类的下拉选
function getnexttype(id){
	$.ajax({
		url:"${pageContext.request.contextPath}/res_getSecondType.action",
		data:{ id:id  },
		type:"post",
		dataType:"json",
		success:function(data){
			var status=data["status"];
			if(status==0){
				alert("登陆失效，请重新登陆！");
				location.href = "res_gonggao.action";
			}else if(status==1){
				$("#secondkuang").show();
				$("#addSecondOp_ttl").children(":gt(0)").remove();
				var list=data["list"];
				for(var i=0;i<list.length;i++){
					var id=list[i]["id"];
					var name=list[i]["name"];
					var str="<option value='"+ id +"'>"+ name +"</option>";
					$("#addSecondOp_ttl").append(str);
				}
			}else{}
		},
		error:function(){
		}
	});
}

//在第一层若是子类还有子类类型（孙子），则继续显示子类（孙子）的下拉选
function getnextonetype(id){
	$.ajax({
		url:"${pageContext.request.contextPath}/res_getSecondType.action",
		data:{ id:id  },
		type:"post",
		dataType:"json",
		success:function(data){
			var status=data["status"];
			if(status==0){
				alert("登陆失效，请重新登陆！");
				location.href = "res_gonggao.action";
			}else if(status==1){
				$("#threekuang").show();
				$("#addthreeOp_ttl").children(":gt(0)").remove();
				var list=data["list"];
				for(var i=0;i<list.length;i++){
					var id=list[i]["id"];
					var name=list[i]["name"];
					var str="<option value='"+ id +"'>"+ name +"</option>";
					$("#addthreeOp_ttl").append(str);
				}
			}else{}
		},
		error:function(){
		}
	});
}

//在第一层中确定新增文件
function firstIntFile(obj){
	insertUploadyfyFirst();
	var id=$("#addFirstOp_id").val();
	var file_sn=$("#addFirstOp_number").val(); // 编号
	var name=$("#addFirstOp_name").val(); //名称
	var content=$("#addFirstOp_content").val();  // 说明
	var opertator_name=$("#addfirstOp_makeMan").val();
	var operate_date=$("#addFirstOp_makeTime").val();
	var verifier_name=$("#addFirstOp_checkMan").val();
	var verify_date=$("#addFirstOp_checkTime").val();
	var approver_name=$("#addFirstOp_agreeMan").val();
	var approve_date=$("#addFirstOp_agreeTime").val();
	if($('#addSure').children("span").eq(0).html()=="确定"){
		$('#addSure').children("span").eq(0).remove();
	}
	if($("#center").children("span").eq(0).html()=="确定"){
		$('#center').children("span").eq(0).remove();
	}
	if(id==""||name==""||file_sn==""||content=="" ){
		alert("请将信息补充完整");
	}else{

		$.ajax({
			url:"${pageContext.request.contextPath}/res_affirmAddFile.action",
			data:{ id:id,file_sn:file_sn,name:name,content:content,opertator_name:opertator_name,operate_date:operate_date,verifier_name:verifier_name,verify_date:verify_date,approver_name:approver_name,approve_date:approve_date  },
			type:"post",
			dataType:"json",
			success:function(data){
				var status=data["status"];
				if(status==0){
					alert("登陆失效，请重新登陆！");
					location.href = "res_gonggao.action";
				}else if(status==2){
					alert("新增失败");
					addOp_cacel($(this));
				}else if(status==3){
					alert("此类中含有子类，请到子类中新增文件");
					addOp_cacel($(this));
				}else{
					bounce.show($("#addOseefile"));
					$("#addOseefile_id").html("");
					$("#addOseefile_category").html("");
					var list=data["list"];
					var resId=list[0]["id"];
					var resCategory=list[0]["category"];
					$("#addOseefile_id").val(resId);
					$("#addOseefile_category").val(resCategory);
				}
			},
			error:function(){
			}
		});
	}
}

// 各层的取消
function cancel_update(){
	bounce.cancel();
	var id=$("#addOseefile_id").val();
	delDoc_unupload(id);
}

function cancel_update_2(){
	bounce.cancel();
	var id=$("#addhuanbanfile_id").val();
	$.ajax({
		url:"res_cancelVersion.action",
		data:{ id:id },
		type:"post",
		dataType:"json",
		success:function(data){
			var status=data["status"];
		},
		error:function(){
		}
	});
}

// 取消
function delDoc_unupload(id){
	$.ajax({
		url:"res_deleteIncompleta.action",
		data:{ id : id  },
		type:"post",
		dataType:"json",
		success:function(data){
			var status=data["status"];

		},
		error:function(){
		}
	});
}
var switchAjax = 1;//设置开关 1:可以ajax 0:不能ajax
// 最后做的事情(上传页面)
function AfterUplaod(){
	if($('#addSure').children("span").eq(0).html()=="确定"){
		$('#addSure').children("span").eq(0).remove();
	}
	if($("#center").children("span").eq(0).html()=="确定"){
		$('#center').children("span").eq(0).remove();
	}

		var id=$("#addOseefile_id").val();
		var catid=$("#addOseefile_category").val();
		var size=$("#addOseefile_size").val();
		if(size==""){
			alert("请上传文件");
		}else{
			loading.open();
			if(switchAjax == 1){
				switchAjax = 0;
				$.ajax({
					url:"${pageContext.request.contextPath}/res_saveUploadFile.action",
					data:{ id:id,catid:catid,size:size },
					type:"post",
					dataType:"json",
					success:function(data){
						loading.close();
						var status=data["status"];
						if(status==0){
							alert("登陆失效，请重新登陆！");
							location.href = "res_gonggao.action";
						}else if($("#intfirst").children("span").html()==1){
							if (status == 2) {
								alert("新增失败");
							} else if (status == 3) {
								alert("此类中含有子类，请到子类中新增文件");
							} else {
								alert("新增成功，请到对应类别下查看");
								addOp_cacel($(this));
							}
						}else if(  ($("#intfirst").children("span").html()==2)||($("#intfirst").children("span").html()==3)     ){
							if (status == 1) {
								if($("td").hasClass("fileid")){
									addOp_cacel($(this));
									alert("新增成功，请到此类别下查看");
								}else{
									if (status == 1) {
										var list = data["list"];
										for (var i = 0; i < list.length; i++) {
											var id = list[i]["id"];
											var name = list[i]["name"];
											var file_sn = list[i]["file_sn"];
											var type = list[i]["type"];
											var change_num = "--";
											var size = list[i]["size"];
											var operator_name = list[i]["opertator_name"];
											var create_date = list[i]["create_date"];
											var str = "<tr>" +
												"<td class='re_id'>" + id + "</td>" +
												"<td>" + file_sn + "</td>" +
												"<td>" + chargeLen(name) + "</td>" +
												"<td>" + chargeNull(type) + "</td>" +
												"<td>" + change_num + "</td>" +
												"<td>" + chargeNull(size) + "</td>" +
												"<td>" + operator_name + "</td>" +
												"<td>" + create_date + "</td>" +
												"<td><a class='ty-color-blue' target='_blanck' onclick='ddown($(this))'>下载</a>" +
												"<label style='display:none;'>" + list[i]["path"] + "</label>" +
												"<a class='ty-color-blue' onclick='up_Och($(this))' >换版</a>" +
												"<a class='ty-color-blue' onclick='see_allMessage($(this))'>查看详情</a>" +
												"<a class='ty-color-blue' onclick='see_online($(this))' >在线预览</a></td></tr>"
											$("#allFile").prepend(str);
											var siz = $("#fisize").html();
											siz = Number(siz) + 1;
											$("#fisize").html("");
											$("#fisize").append(siz);
											addOp_cacel($(this));
										}
									} else if (status == 4) {
										$("#filetype").hide();
										$("#allFile").show();
										var list = data["list"];
										for (var i = 0; i < list.length; i++) {
											var id = list[i]["id"];
											var name = list[i]["name"];
											var file_sn = list[i]["file_sn"];
											var type = list[i]["type"];
											var change_num = "--";
											var size = list[i]["size"];
											var operator_name = list[i]["opertator_name"];
											var create_date = list[i]["create_date"];
											var str = "<tr>" +
												"<td class='re_id'>" + id + "</td>" +
												"<td>" + file_sn + "</td>" +
												"<td>" + chargeLen(name) + "</td>" +
												"<td>" + chargeNull(type) + "</td>" +
												"<td>" + change_num + "</td>" +
												"<td>" + chargeNull(size) + "</td>" +
												"<td>" + operator_name + "</td>" +
												"<td>" + create_date + "</td>" +
												"<td><a class='ty-color-blue' target='_blanck' onclick='ddown($(this))' style='position:static;'>下载</a>" +
												"<label style='display:none;'>" + list[i]["path"] + "</label>" +
												"<a class='ty-color-blue' onclick='up_Och($(this))'>换版</a>" +
												"<a class='ty-color-blue' onclick='see_allMessage($(this))'>查看详情</a>" +
												"<a class='ty-color-blue' onclick='see_online($(this))' >在线预览</a></td></tr>";
											$("#allFile").prepend(str);
											var siz = $("#fisize").html();
											siz = Number(siz) + 1;
											$("#fisize").html("");
											$("#fisize").append(siz);
											addOp_cacel($(this));
										}
									} else {
										addOp_cacel($(this));
										alert("新增失败");
									}
								}
							} else if (status == 4) {
								$("#filetype").hide();
								$("#allFile").show();
								var list = data["list"];
								for (var i = 0; i < list.length; i++) {
									var id = list[i]["id"];
									var name = list[i]["name"];
									var file_sn = list[i]["file_sn"];
									var type = list[i]["type"];
									var change_num = list[i]["change_num"];
									if (change_num == 0) {
										change_num = "--";
									} else {
										change_num = "G" + change_num;
									}
									var size = list[i]["size"];
									var operator_name = list[i]["opertator_name"];
									var create_date = list[i]["create_date"];
									var str = "<tr>" +
										"<td class='re_id'>" + id + "</td>" +
										"<td>" + file_sn + "</td>" +
										"<td>" + chargeLen(name) + "</td>" +
										"<td>" + chargeNull(type) + "</td>" +
										"<td>" + change_num + "</td>" +
										"<td>" + chargeNull(size) + "</td>" +
										"<td>" + operator_name + "</td>" +
										"<td>" + create_date + "</td>" +
										"<td><a class='ty-color-blue' target='_blanck' onclick='ddown($(this))'>下载</a>" +
										"<label style='display:none;'>" + list[i]["path"] + "</label>" +
										"<a class='ty-color-blue' onclick='up_Och($(this))' >换版</a>" +
										"<a class='ty-color-blue' onclick='see_allMessage($(this))'>查看详情</a>" +
										"<a class='ty-color-blue' onclick='see_online($(this))' >在线预览</a></td></tr>" ;
									$("#allFile").prepend(str);
									addOp_cacel($(this));

								}
							} else {
								addOp_cacel($(this));
								alert("新增失败");

							}
						}else if($("#intfirst").children("span").html()==4){
							if (status == 1) {
								var list = data["list"];
								for (var i = 0; i < list.length; i++) {
									var id = list[i]["id"];
									var name = list[i]["name"];
									var file_sn = list[i]["file_sn"];
									var type = list[i]["type"];
									var change_num = "--";
									var size = list[i]["size"];
									var operator_name = list[i]["opertator_name"];
									var create_date = list[i]["create_date"];
									var str = "<tr>" +
										"<td class='re_id'>" + id + "</td>" +
										"<td>" + file_sn + "</td>" +
										"<td>" + chargeLen(name) + "</td>" +
										"<td>" + chargeNull(type) + "</td>" +
										"<td>" + change_num + "</td><" +
										"td>" + chargeNull(size) + "</td>" +
										"<td>" + operator_name + "</td>" +
										"<td>" + create_date + "</td>" +
										"<td><a class='ty-color-blue'  target='_blanck' onclick='ddown($(this))' >下载</a>" +
										"<label style='display:none;'>" + list[i]["path"] + "</label>" +
										"<a class='ty-color-blue' onclick='up_Och($(this))' >[换版]</a>" +
										"<a class='ty-color-blue' onclick='see_allMessage($(this))' >查看详情</a>" +
										"<a class='ty-color-blue' onclick='see_online($(this))' >在线预览</a></td></tr>" ;
									$("#allFile").prepend(str);
									var siz = $("#fisize").html();
									siz = Number(siz) + 1;
									$("#fisize").html("");
									$("#fisize").append(siz);
									addOp_cacel($(this));
								}
							} else if (status == 4) {
								$("#filetype").hide();
								$("#allFile").show();
								var list = data["list"];
								for (var i = 0; i < list.length; i++) {
									var id = list[i]["id"];
									var name = list[i]["name"];
									var file_sn = list[i]["file_sn"];
									var type = list[i]["type"];
									var change_num = "--";
									var size = list[i]["size"];
									var operator_name = list[i]["opertator_name"];
									var create_date = list[i]["create_date"];
									var str = "<tr><td class='re_id'>" + id + "</td><td>" + file_sn + "</td>" +
										"<td>" + chargeLen(name) + "</td>" +
										"<td>" + chargeNull(type) + "</td><td>" + change_num + "</td><td>" + chargeNull(size) + "</td>" +
										"<td>" + operator_name + "</td><td>" + create_date + "</td>" +
										"<td><a class='ty-color-blue' target='_blanck' onclick='ddown($(this))'>下载</a>" +
										"<label style='display:none;'>" + list[i]["path"] + "</label>" +
										"<a class='ty-color-blue' onclick='up_Och($(this))' >换版</a>" +
										"<a class='ty-color-blue' onclick='see_allMessage($(this))' >查看详情</a>" +
										"<a class='ty-color-blue' onclick='see_online($(this))' >在线预览</a></td></tr>";
									$("#allFile").prepend(str);
									var siz = $("#fisize").html();
									siz = Number(siz) + 1;
									$("#fisize").html("");
									$("#fisize").append(siz);
									addOp_cacel($(this));
								}
							} else {
								addOp_cacel($(this));
								alert("新增失败");
							}


						}
						switchAjax = 1 ;
					},
					error:function(){
						alert("系统错误，请重试！");
						switchAjax = 1 ;
					}
				})
			}
		}

}

//获取第二层列表
function getsecondtype(obj){
	$("#intfirst").children("span").html(+$("#intfirst").children("span").html()+1);
	var id=obj.parent().children(":eq(0)").html();
	$("#filetype").children(":gt(0)").remove();
	$("#typesize").html("");
	$("#fisize").html("");
	$(".back").show();
	$(".cs_name").show();
	// $("#intfirst").hide();
	// $("#intsecond").show();
	$.ajax({
		url:"${pageContext.request.contextPath}/res_getSecondFileType.action",
		data:{ id:id },
		type:"post",
		dataType:"json",
		success:function(data){
			var status=data["status"];
			var list=data["list"];
			var typeSize=data["typeSize"];
			if(status==0){
				alert("登陆失效，请重新登陆！");
				location.href = "res_gonggao.action";
			}else if(status==2){
				$("#filetype").hide();
				$("#ty_amount").hide();
				$("#fi_amount").show();
				$("#allFile").show();
				var typename=data["typename"];
				var parentid=data["backid"];
				for(var i=0;i<list.length;i++){
					var id=list[i]["id"];
					var name=list[i]["name"];
					var file_sn=list[i]["file_sn"];
					var type=list[i]["type"];
					var change_num=list[i]["change_num"];
					if(change_num==0){
						change_num="--";
					}else{
						change_num="G"+change_num;
					}
					var size=list[i]["size"];
					var operator_name=list[i]["opertator_name"];
					var create_date=list[i]["create_date"];
					var str="<tr>" +
						"<td class='re_id'>"+id+"</td>" +
						"<td>"+file_sn+"</td>" +
						"<td>" + chargeLen(name) + "</td>" +
						"<td>"+ chargeNull(type) +"</td>" +
						"<td>"+change_num+"</td>" +
						"<td>"+ chargeNull(size) +"</td>" +
						"<td>"+operator_name+"</td>" +
						"<td>"+create_date+"</td>" +
						"<td>" +
							"<a class='ty-color-blue' target='_blanck' onclick='ddown($(this))' style='position:static;'>下载</a>" +
							"<label style='display:none;'>"+list[i]["path"]+"</label>" +
							"<a class='ty-color-blue' onclick='up_Och($(this))'>换版</a>" +
							"<a class='ty-color-blue' onclick='see_allMessage($(this))' >查看详情</a>" +
							"<a class='ty-color-blue' onclick='see_online($(this))' >在线预览</a></td></tr>"
					$("#allFile").append(str);
				}
				$("#category_id").append(parentid);
				$("#type_name").append(typename);
				$("#fisize").append(typeSize);
			}else if(status==3){
				$("#ty_amount").hide();
				$("#fi_amount").show();
				var parentid=data["backid"];
				var typename=data["typename"];
				$("#category_id").append(parentid);
				$("#type_name").append(typename);
				$("#fisize").append(typeSize);
				$("#filetype").hide();
				$("#allFile").show();
			}else{
				var typename=data["typename"];
				var parentid=data["backid"];
				for(var i=0;i<list.length;i++){
					var id=list[i]["id"];
					var name=list[i]["name"];
					var str="<tr><td class='fileid'>"+ id +"</td><td onclick='getthirdtype($(this))' class='tz'>"+ name +"</td></tr>"
					$("#filetype").append(str);
				}
				$("#category_id").append(parentid);
				$("#type_name").append(typename);
				$("#typesize").append(typeSize);
			}
		},
		error:function(){
		}
	});
}

//在第二层中点击上传文件
function secondAddFile(){
    var userType = chargeRole('超管');
    if (userType) { // 0 表示超管
		bounce.show( $("#mtTip") ) ;
		return false ;
	}

	bounce.show($("#addOup"));

	if($("td").hasClass("fileid")){
		$("#firstkuang").show();
		$("#secondkuang").hide();
		$("#threekuang").hide();
	}else{
		$("#firstkuang").hide();
		$("#secondkuang").hide();
		$("#threekuang").hide();
	};


	var id=$("#category_id").html();
	$("#addFirstOp_ttl").children(":gt(0)").remove();
	$("#addFirstOp_id").val("");
	$("#addFirstOp_size").val("");
	$("#addFirstOp_number").val("");
	$("#addFirstOp_name").val("");
	$("#addFirstOp_content").val("");
	$("#addFirstOp_makeMan").val("");
	$("#addFirstOp_makeTime").val("");
	$("#addFirstOp_checkMan").val("");
	$("#addFirstOp_checkTime").val("");
	$("#addFirstOp_agreeMan").val("");
	$("#addFirstOp_agreeTime").val("");
	$.ajax({
		url:"${pageContext.request.contextPath}/res_addFile.action",
		data:{ id:id },
		type:"post",
		dataType:"json",
		success:function(data){
			var list=data["list"];
			var status=data["status"];
			if(status==0){
				alert("登陆失效，请重新登陆！");
				location.href = "res_gonggao.action";
			}else if(status==1){
				for(var i=0;i<list.length;i++){
					var id=list[i]["id"];
					var name=list[i]["name"];
					var str="<option value='"+ id +"'>"+ name +"</option>";
					$("#addFirstOp_ttl").append(str);
				}
			}else if(status==3){
				var id=$("#category_id").html();
				$("#seckong").hide();
				$("#addFirstOp_id").val(id);
			}else{
				alert("系统错误，新增失败！！！");
			}
		},
		error:function(){
		}
	});
}

//在第二层中若是有子类显示子类下拉选
function getsecnexttype(id){
	$.ajax({
		url:"${pageContext.request.contextPath}/res_getSecondType.action",
		data:{ id:id  },
		type:"post",
		dataType:"json",
		success:function(data){
			var status=data["status"];
			if(status==0){
				alert("登陆失效，请重新登陆！");
				location.href = "res_gonggao.action";
			}else if(status==1){
				$("#thrkuang").show();
				$("#addthrOp_ttl").children(":gt(0)").remove();
				var list=data["list"];
				for(var i=0;i<list.length;i++){
					var id=list[i]["id"];
					var name=list[i]["name"];
					var str="<option value='"+ id +"'>"+ name +"</option>";
					$("#addthrOp_ttl").append(str);
				}
			}else{}
		},
		error:function(){
		}
	});

}

//在第三层列表中点击上传文件
function thirdAddFile(obj){
	bounce.show($("#addOup"));

	if($("td").hasClass("fileid")){
		$("#firstkuang").show();
		$("#secondkuang").hide();
		$("#threekuang").hide();
	}else{
		$("#firstkuang").hide();
		$("#secondkuang").hide();
		$("#threekuang").hide();
	};
	var id=$("#category_id").html();
	$("#addFirstOp_ttl").children(":gt(0)").remove();
	$("#addFirstOp_id").val("");
	$("#addFirstOp_number").val("");
	$("#addFirstOp_name").val("");
	$("#addFirstOp_content").val("");
	$("#addFirstOp_makeMan").val("");
	$("#addFirstOp_makeTime").val("");
	$("#addFirstOp_checkMan").val("");
	$("#addFirstOp_checkTime").val("");
	$("#addFirstOp_agreeMan").val("");
	$("#addFirstOp_agreeTime").val("");
	$.ajax({
		url:"${pageContext.request.contextPath}/res_addFile.action",
		data:{ id:id },
		type:"post",
		dataType:"json",
		success:function(data){
			var list=data["list"];
			var status=data["status"];
			if(status==0){
				alert("登陆失效，请重新登陆！");
				location.href = "res_gonggao.action";
			}else if(status==1){
				for(var i=0;i<list.length;i++){
					var id=list[i]["id"];
					var name=list[i]["name"];
					var str="<option value='"+ id +"'>"+ name +"</option>";
					$("#addFirstOp_ttl").append(str);
				}
			}else if(status==3){
				var id=$("#category_id").html();
				$("#thokong").hide();
				$("#addFirstOp_id").val(id);
			}else{
				alert("系统错误，新增失败！！！");
			}
		},
		error:function(){
		}
	});
}

//获取第四层层列表
function getthirdtype(obj){
	$("#intfirst").children("span").html(+$("#intfirst").children("span").html()+1);
	var id=obj.parent().children(":eq(0)").html();
	$("#filetype").children(":gt(0)").remove();
	$("#fisize").html("");
	$("#category_id").html("");
	$("#type_name").html("");
	$(".back").show();
	$(".cs_name").show();
	$("#intsecond").hide();
	$.ajax({
		url:"${pageContext.request.contextPath}/res_getSecondFileType.action",
		data:{ id:id },
		type:"post",
		dataType:"json",
		success:function(data){
			var status=data["status"];
			var list=data["list"];
			var typeSize=data["typeSize"];
			if(status==0){
				alert("登陆失效，请重新登陆！");
				location.href = "res_gonggao.action";
			}else if(status==2){
				$("#ty_amount").hide();
				$("#fi_amount").show();
				$("#intforth").hide();
				$("#filetype").hide();
				$("#allFile").show();
				$("#intthird").show();
				var typename=data["typename"];
				var parentid=data["backid"];
				for(var i=0;i<list.length;i++){
					var id=list[i]["id"];
					var name=list[i]["name"];
					var file_sn=list[i]["file_sn"];
					var type=list[i]["type"];
					var change_num=list[i]["change_num"];
					if(change_num==0){
						change_num="--";
					}else{
						change_num="G"+change_num;
					}
					var size=list[i]["size"];
					var operator_name=list[i]["opertator_name"];
					var create_date=list[i]["create_date"];
					var str="<tr>" +
						"<td class='re_id'>"+id+"</td>" +
						"<td>"+file_sn+"</td>" +
						"<td>" + chargeLen(name) + "</td>" +
						"<td>"+ chargeNull(type) +"</td>" +
						"<td>"+change_num+"</td>" +
						"<td>"+chargeNull(size) +"</td>" +
						"<td>"+operator_name+"</td>" +
						"<td>"+create_date+"</td>" +
						"<td>" +
						"<a class='ty-color-blue'  target='_blanck' onclick='ddown($(this))' >下载</a>" +
						"<label style='display:none;'>"+list[i]["path"]+"</label>" +
						"<a class='ty-color-blue' onclick='up_Och($(this))' >换版</a>" +
						"<a class='ty-color-blue' onclick='see_allMessage($(this))' >查看详情</a>" +
						"<a class='ty-color-blue' onclick='see_online($(this))' >在线预览</a></td></tr>" ;
					$("#allFile").append(str);
				}
				$("#category_id").append(parentid);
				$("#type_name").append(typename);
				$("#fisize").append(typeSize);
			}else if(status==3){
				$("#intforth").hide();
				$("#filetype").hide();
				$("#intthird").show();
				$("#allFile").show();
				$("#ty_amount").hide();
				$("#fi_amount").show();
				var parentid=data["backid"];
				var typename=data["typename"];
				$("#category_id").append(parentid);
				$("#type_name").append(typename);
				$("#fisize").append(typeSize);
			}else{
				$("#intforth").show();
				var typename=data["typename"];
				var parentid=data["backid"];
				for(var i=0;i<list.length;i++){
					var id=list[i]["id"];
					var name=list[i]["name"];
					var str="<tr><td class='fileid'>"+ id +"</td><td onclick='getthirdtype($(this))' class='tz'>"+ name +"</td></tr>"
					$("#filetype").append(str);
				}
				$("#category_id").append(parentid);
				$("#type_name").append(typename);
				$("#fisize").append(typeSize);
			}
		},
		error:function(){
		}
	});
}

//第四层点击上传文件
function forthAddFile(obj){
	bounce.show($("#addOup"));
	$("#firstkuang").hide();
	$("#secondkuang").hide();
	$("#threekuang").hide();
	insertUploadyfyFirst();
	var id=$("#category_id").html();
	$("#addFirstOp_id").val("");
	$("#addFirstOp_size").val("");
	$("#addFirstOp_number").val("");
	$("#addFirstOp_name").val("");
	$("#addFirstOp_content").val("");
	$("#addFirstOp_makeMan").val("");
	$("#addFirstOp_makeTime").val("");
	$("#addFirstOp_checkMan").val("");
	$("#addFirstOp_checkTime").val("");
	$("#addFirstOp_agreeMan").val("");
	$("#addFirstOp_agreeTime").val("");
	$.ajax({
		url:"${pageContext.request.contextPath}/res_addFile.action",
		data:{ id:id },
		type:"post",
		dataType:"json",
		success:function(data){
			var list=data["list"];
			var status=data["status"];
			if(status==0){
				alert("登陆失效，请重新登陆！");
				location.href = "res_gonggao.action";
			}else if(status==1){
			}else if(status==3){
				var id=$("#category_id").html();
				$("#thikong").hide();
				$("#addFirstOp_id").val(id);
			}else{
				alert("系统错误，新增失败！！！");
			}
		},
		error:function(){
		}
	});
}



//返回上一页
function goback(){
	$("#intfirst").children("span").html($("#intfirst").children("span").html()-1);
	var id=$("#category_id").html();
	$("#typesize").html("");
	$("#fisize").html("");
	$("#category_id").html("");
	$("#type_name").html("");
	$.ajax({
		url:"${pageContext.request.contextPath}/res_getFileBack.action",
		data:{ id:id },
		type:"post",
		dataType:"json",
		success:function(data){
			var status=data["status"];
			var list=data["list"];
			if(status==0){
				alert("登陆失效，请重新登陆！");
				location.href = "res_gonggao.action";
			}else if(status==1){
				$("#intsecond").hide();
				$("#intfirst").show();
				$(".back").hide();
				$("#filetype").children(":gt(0)").remove();
				var typename=data["typename"];
				var typeSize=data["typeSize"];
				var parentid=data["backid"];
				for(var i=0;i<list.length;i++){
					var id=list[i]["id"];
					var name=list[i]["name"];
					var str="<tr><td class='fileid'>"+ id +"</td><td onclick='getsecondtype($(this))' class='tz'>"+ name +"</td></tr>"
					$("#filetype").append(str);
				}
				$("#category_id").append(parentid);
				$("#type_name").append(typename);
				$("#typesize").append(typeSize);
			}else if(status==2){
				$("#intsecond").hide();
				$(".back").hide();
				$("#intfirst").show();
				$("#allFile").hide();
				$("#fi_amount").hide();
				$("#filetype").show();
				$("#ty_amount").show();
				$("#allFile").children(":gt(0)").remove();
				var typename=data["typename"];
				var typeSize=data["typeSize"];
				var parentid=data["backid"];
				for(var i=0;i<list.length;i++){
					var id=list[i]["id"];
					var name=list[i]["name"];
					var str="<tr><td class='fileid'>"+ id +"</td><td onclick='getsecondtype($(this))' class='tz'>"+ name +"</td></tr>"
					$("#filetype").append(str);
				}
				$("#category_id").append(parentid);
				$("#type_name").append(typename);
				$("#typesize").append(typeSize);
			}else{
				$("#intsecond").show();
				$("#intthird").hide();
				$("#intforth").hide();
				$("#allFile").hide();
				$("#fi_amount").hide();
				$("#filetype").show();
				$("#ty_amount").show();
				$("#allFile").children(":gt(0)").remove();
				$("#filetype").children(":gt(0)").remove();
				var typename=data["typename"];
				var typeSize=data["typeSize"];
				var parentid=data["backid"];
				for(var i=0;i<list.length;i++){
					var id=list[i]["id"];
					var name=list[i]["name"];
					var str="<tr><td class='fileid'>"+ id +"</td><td onclick='getthirdtype($(this))' class='tz'>"+ name +"</td></tr>"
					$("#filetype").append(str);
				}
				$("#category_id").append(parentid);
				$("#type_name").append(typename);
				$("#typesize").append(typeSize);
			}
		},
		error:function(){
		}
	});
}

//点击换版
function up_Och(obj){
    if (chargeRole('超管')) { // 0 表示超管
		bounce.show( $("#mtTip") ) ;  $("#mt_tip_ms").html("您没有此权限！"); return false ;
	}
	
	bounce.show($("#addOch"));
	var id=obj.parent().parent().children(":eq(0)").html();
	change_ver=obj.parent().parent();
	$("#addOch_id").val("");
	$("#addOch_type").val("");
	$("#addOch_filesn").val("");
	$("#addOch_name").val("");
	$("#addOch_text").val("");
	$("#addOch_reason").val("");
	$("#addOch_makename").val("");
	$("#addOch_maketime").val("");
	$("#addOch_checkname").val("");
	$("#addOch_checktime").val("");
	$("#addOch_agreename").val("");
	$("#addOch_agreetime").val("");
	$.ajax({
		url:"${pageContext.request.contextPath}/res_clickReplace.action",
		data:{ id:id },
		type:"post",
		dataType:"json",
		success:function(data){
			var list=data["list"];
			var status=data["status"];
			var filtType=data["fileType"];
			if(status==0){
				alert("登陆失效，请重新登陆！");
				location.href = "res_gonggao.action";
			}else{
				$("#addOch_type").val(filtType);
				$("#addOch_id").val(list[0]["id"]);
				$("#addOch_filesn").val(list[0]["file_sn"]);
				$("#addOch_name").val(list[0]["name"]);
				$("#addOch_text").val(list[0]["content"]);
				$("#addOch_reason").val(list[0]["reason"]);
				$("#addOch_makename").val(list[0]["opertator_name"]);
				$("#addOch_maketime").val(cutDate( list[0]["operate_date"] ));
				$("#addOch_checkname").val(cutDate(list[0]["verifier_name"]));
				$("#addOch_checktime").val(cutDate(list[0]["verify_date"]));
				$("#addOch_agreename").val(cutDate(list[0]["approver_name"]));
				$("#addOch_agreetime").val( cutDate(list[0]["approve_date"]) );
			}
		},
		error:function(){
		}
	});
}
function cutDate(date_str){
	if (date_str == undefined || date_str =="1900-01-01 00:00:00.0" || date_str =="1900-01-01 00:00:00") {
		return "";
	} else{
		return date_str.substring(0,19);
	};
}

//点击换版下一步
function addOch_updateType(){
	insertUploadyfyFourth();
	var id=$("#addOch_id").val();
	var content=$("#addOch_text").val();  // 说明
	var reason=$("#addOch_reason").val();  // 换版理由
	var opertator_name=$("#addOch_makename").val();
	var operate_date=$("#addOch_maketime").val();
	var verifier_name=$("#addOch_checkname").val();
	var verify_date=$("#addOch_checktime").val();
	var approver_name=$("#addOch_agreename").val();
	var approve_date=$("#addOch_agreetime").val();
	if($('#addSure').children("span").eq(0).html()=="确定"){
		$('#addSure').children("span").eq(0).remove();
	}
	if($("#center").children("span").eq(0).html()=="确定"){
		$('#center').children("span").eq(0).remove();
	}
	if(id==""||content=="" ||reason==""){
		alert("请将信息补充完整");
	}else{
		$.ajax({
			url:"res_affirmReplace.action",
			data:{ id:id,content:content,reason:reason,opertator_name:opertator_name,operate_date:operate_date,verifier_name:verifier_name,verify_date:verify_date,approver_name:approver_name,approve_date:approve_date },
			type:"post",
			dataType:"json",
			success:function(data){
				var list=data["list"];
				var status=data["status"];
				if(status==0){
					alert("登陆失效，请重新登陆！");
					location.href = "res_gonggao.action";
				}else{
					bounce.show($("#addhuanbanfile"));
					$("#addhuanbanfile_id").html("");
					$("#addhuanbanfile_category").html("");
					var list=data["list"];
					var resId=list[0]["id"];
					var resCategory=list[0]["category"];
					$("#addhuanbanfile_id").val(resId);
					$("#addhuanbanfile_category").val(resCategory);
				}
			},
			error:function(){
			}
		});
	}
}
//换版最后做的事情(上传页面)
function huanbanAfterUplaod(){
	var id=$("#addhuanbanfile_id").val();
	var catid=$("#addhuanbanfile_category").val();
	var size=$("#addhuanbanfile_size").val();
	if(size==""){
		alert("请上传文件");
	}else{
		$.ajax({
			url:"${pageContext.request.contextPath}/res_saveUploadFile.action",
			data:{ id:id,catid:catid,size:size },
			type:"post",
			dataType:"json",
			success:function(data){
				var list=data["list"];
				var status=data["status"];
				if(status==0){
					alert("登陆失效，请重新登陆！");
					location.href = "res_gonggao.action";
				}else{
					bounce.cancel();
					change_ver.remove();
					var change_num=list[0]["change_num"];
					if(change_num==0){
						change_num="--";
					}else{
						change_num="G"+change_num;
					}
					var str="<tr>" +
						"<td class='re_id'>"+list[0]["id"]+"</td>" +
						"<td>"+list[0]["file_sn"]+"</td>" +
						"<td>" + chargeLen(list[0]["name"]) + "</td>" +
						"<td>"+ chargeNull(list[0]["type"]) +"</td>" +
						"<td>"+change_num+"</td>" +
						"<td>"+chargeNull(list[0]["size"]) +"</td>" +
						"<td>"+list[0]["opertator_name"]+"</td>" +
						"<td>"+list[0]["create_date"]+"</td>" +
						"<td>" +
						"<a class='ty-color-blue' target='_blanck' onclick='ddown($(this))' >下载</a>" +
						"<label style='display:none;'>"+list[0]["path"]+"</label>" +
						"<a class='ty-color-blue' onclick='up_Och($(this))' >换版</a>" +
						"<a class='ty-color-blue' onclick='see_allMessage($(this))' >查看详情</a>" +
						"<a class='ty-color-blue' onclick='see_online($(this))' >在线预览</a></td></tr>"
					$("#allFile").prepend(str);

				}
			},
			error:function(){
			}
		});
	}
}

//查看详情
function see_allMessage(obj){
	bounce.show($("#addOsee"));
	var id=obj.parent().parent().children(":eq(0)").html();
	$("#Osee_createdate").html("");
	$("#Osee_filesn").html("");
	$("#Osee_name").html("");
	$("#Osee_size").html("");
	$("#Osee_type").html("");
	$("#Osee_change").html("");
	$("#Osee_updatedate").html("");
	$("#Osee_reason").html("");
	$("#Osee_content").html("");
	$("#Osee_opertatorname").html("");
	$("#Osee_operatedate").html("");
	$("#Osee_verifiername").html("");
	$("#Osee_verifydate").html("");
	$("#Osee_approvername").html("");
	$("#Osee_approvedate").html("");
	$.ajax({
		url:"${pageContext.request.contextPath}/res_getFileMessage.action",
		data:{ id:id },
		type:"post",
		dataType:"json",
		success:function(data){
			var list=data["list"];
			var status=data["status"];
			if(status==0){
				alert("登陆失效，请重新登陆！");
				location.href = "res_gonggao.action";
			}else{
				$("#Osee_createdate").html(list[0]["create_date"]);
				$("#Osee_filesn").html(list[0]["file_sn"]);
				$("#Osee_name").html(list[0]["name"]);
				$("#Osee_size").html(list[0]["size"]);
				$("#Osee_type").html(list[0]["type"]);
				var changenum=list[0]["change_num"];
				$("#Osee_change").html(changenum);
				if(changenum==0){
					$("#Osee_change").html("--");
					$("#Osee_updatedate").html("");
				}else{
					$("#Osee_change").html("G"+changenum);
					$("#Osee_updatedate").html(list[0]["create_date"]);
				}
				$("#Osee_reason").html(list[0]["reason"]);
				$("#Osee_content").html(list[0]["content"]);
				$("#Osee_opertatorname").html(list[0]["opertator_name"]);
				var date_operate=list[0]["operate_date"];
				if(date_operate=='1900-01-01 00:00:00'){
					date_operate=null;
				}
				$("#Osee_operatedate").html(date_operate);
				$("#Osee_verifiername").html(list[0]["verifier_name"]);
				var date_verity=list[0]["verify_date"];
				if(date_verity=='1900-01-01 00:00:00'){
					date_verity=null;
				}
				$("#Osee_verifydate").html(date_verity);
				$("#Osee_approvername").html(list[0]["approver_name"]);
				var date_approve= list[0]["approve_date"];
				if(date_approve=='1900-01-01 00:00:00'){
					date_approve=null;
				}
				$("#Osee_approvedate").html(date_approve);
			}
		},
		error:function(){
		}
	});
}

//时间插件
laydate.render({elem: '#addFirstOp_makeTime', format: 'YYYY-MM-DD hh:mm:ss'});
laydate.render({elem: '#addFirstOp_checkTime', format: 'YYYY-MM-DD hh:mm:ss'});
laydate.render({elem: '#addFirstOp_agreeTime', format: 'YYYY-MM-DD hh:mm:ss'});
laydate.render({elem: '#addOch_maketime', format: 'YYYY-MM-DD hh:mm:ss'});
laydate.render({elem: '#addOch_checktime', format: 'YYYY-MM-DD hh:mm:ss'});
laydate.render({elem: '#addOch_agreetime', format: 'YYYY-MM-DD hh:mm:ss'});




//删除弹框的方法

function addOp_cacel(obj){
	bounce.cancel();
}

//下载文件
function ddown(obj){
	var name = obj.siblings("label").html();
	if (name == "" || name == undefined || name == null || name == "undefined" || name == "null") {
		alert("文件不存在！");
		return false;
	};

	obj.attr("download","").attr("target","_blank").attr("href",$.uploadUrl+path);
}
// 在线预览
function see_online(obj) {
	var name = obj.siblings("label").html();
	if (name == "" || name == undefined || name == null || name == "undefined" || name == "null") {
		alert("文件不存在！");
		return false;
	};
	var nameArr = name.split(".");

	var type = nameArr[nameArr.length - 1];
    switch (type){
        case 'doc':case 'docx':
        case 'xls':case 'xlsx':
        case 'ppt':
            obj.attr('target','_blank').attr('href','https://view.officeapps.live.com/op/view.aspx?src='+$.fileUrl+path);
            break;
        case 'png':case 'jpg':case 'jpeg':case 'gif':
        case "pdf": // wyu：ow365对文件大小有限制，改为直接用浏览器预览  本文件没有被调用，代码 not used
        case "txt":
			obj.attr('target','_blank').attr('href',$.fileUrl+path);
			break;
		case "md":
            obj.attr('target','_blank').attr('href',$.webRoot+'/assets/md/index.html?src='+$.fileUrl+path);
			break;
        default:
            // case "rar":case "zip":
            // case "pdf":
            // case 'et':
            obj.attr('target','_blank').attr('href',$.ow365url+path);
    }
}

//点击上传之后删除上传按钮,以及文件大小随上传跳动
function uploadbtn(obj){
	var tb=obj.siblings(".delfilebtn");
	obj.on("click",function(){
		obj.remove();
	})

	var t=setInterval(function(){
		console.log(1)
		tb.siblings(".progressnum").children(".uploadedsize").html((parseFloat(tb.siblings(".progressnum").children(".totalsize").html())*parseFloat(tb.siblings(".up_percent").html())/100).toFixed(2)+tb.siblings(".progressnum").children(".totalsize").html().charAt(tb.siblings(".progressnum").children(".totalsize").html().length-2)+tb.siblings(".progressnum").children(".totalsize").html().charAt(tb.siblings(".progressnum").children(".totalsize").html().length-1))
		if(parseInt(tb.parent().children(".up_percent").html())=="100"){
			clearInterval(t);
			if($('#addSure').children("span").eq(0).html()!="确定"){
				$('#addSure').prepend('<span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="AfterUplaod()">确定</span>');
			}
			if($("#center").children("span").eq(0).html()!="确定"){
				$('#center').prepend('<span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" onclick="huanbanAfterUplaod()">确定</span>');
			}
		}
	},10);

}

//点击删除按钮，删除页面上的文件标签
function delfilebtn(obj){
	obj.parent().remove();
	if($('#addSure').children("span").eq(0).html()=="确定"){
		$('#addSure').children("span").eq(0).remove();
	}
	if($("#center").children("span").eq(0).html()=="确定"){
		$('#center').children("span").eq(0).remove();
	}
	var addOseefilesize=$("#addOseefile_size").val();
	var addhuanbanfilesize=$("#addhuanbanfile_size").val();


	if(addOseefilesize!=null||addhuanbanfilesize!=null){
		if(addOseefilesize!=null){
			$("#addOseefile_size").val("");
		};
		if(addhuanbanfilesize!=null){
			$("#addhuanbanfile_size").val("");
		};
		$.ajax({
			url:"res_deleteSaveFile.action",
			data:{ },
			type:"post",
			dataType:"json",
			success:function(data){
				var status=data["status"];
			},
			error:function(){
			}
		});
	}
}

// 为空赋值
function chargeNull(str){
	if(!str || str =="null"  || str =="undefined"){
		return "--"
	}else{

		return str ; 
	}
}
function chargeLen(str){
	if(str){
		if(str.length >10 ){
			return str.substr(0,10) + "...";	
		}else{
			return str ; 			
		}
	}else{
		return "--"
	}
}