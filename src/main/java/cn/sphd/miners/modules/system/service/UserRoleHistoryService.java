package cn.sphd.miners.modules.system.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.entity.UserRoleHistory;

import java.util.List;

/**
 * Created by Administrator on 2019/8/26.
 */
public interface UserRoleHistoryService {

    void saveUserRoleHistoryByUserRole(User user,Integer userId,Integer roleId);

    UserRoleHistory getUserRoleHistoryByRoleId(Integer roleId,Integer oid);

    List<UserRoleHistory> getUserRoleHistoryListByRoleId(Integer oid,Integer roleId, PageInfo pageInfo);
}
