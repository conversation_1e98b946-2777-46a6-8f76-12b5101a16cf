package cn.sphd.miners.modules.dmmSchedule.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.dmmSchedule.dao.DmmJobDao;
import cn.sphd.miners.modules.dmmSchedule.dao.DmmScheduleDao;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmJob;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmSchedule;
import cn.sphd.miners.modules.dmmSchedule.service.DmmJobService;
import io.netty.util.internal.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Created by Administrator on 2019/1/24.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class DmmJobServiceImpl implements DmmJobService {
    @Autowired
    DmmJobDao dmmJobDao;
    @Autowired
    DmmScheduleDao dmmScheduleDao;

    @Override
    public void updateDmmJob(DmmJob dmmJob) {
        dmmJobDao.update(dmmJob);
    }

    @Override
    public DmmJob getLastDmmJobBySid(Integer sId) {
        String hql=" from DmmJob where scheduleId=:sId order by createDate desc";
        Map<String,Object> params=new HashMap<>();
        params.put("sId",sId);
        DmmJob dmmJob= (DmmJob) dmmJobDao.getByHQLWithNamedParams(hql,params);
        return dmmJob;
    }

    @Override
    public DmmJob addDmmJobByUserSchedule(DmmSchedule dmmSchedule) {
        DmmJob dmmJob=new DmmJob();
        addDmmJobByUserScheduleTask(dmmSchedule, dmmJob, dmmSchedule.getNextRunTime().getTime(), System.currentTimeMillis());
        return dmmJob;
    }

//    @Override
//    public DmmJob addDmmJobByUserSchedule(DmmSchedule DmmSchedule) {
//        DmmJob DmmJob=new DmmJob();
//        DmmJob.setSchedule(DmmSchedule);
//        DmmJob.setCreator(DmmSchedule.getUser());
//        DmmJob.setCreateDate(new Date());
//        DmmJob.setUser(DmmSchedule.getUser());
//        DmmJob.setCreateName(DmmSchedule.getCreateName());
//        DmmJob.setName(DmmSchedule.getName());
//        DmmJob.setTitle(DmmSchedule.getTitle());
//        DmmJob.setType(1);//约定提醒 -1
//        DmmJob.setRunTime(DmmSchedule.getNextRunTime());//运行时间
//        DmmJob.setUpdateDate(DmmSchedule.getNextRunTime());// 存成这条提醒的的初次运行时间
//        DmmJob.setEnabled(1);//需要提醒
////        date= NewDateUtils.changeDay(NewDateUtils.getLastTimeOfMonth(new Date()),-DmmSchedule.getSpecialInterval());//每月倒数提醒日
//
////        Calendar calendar = Calendar.getInstance();
////        calendar.setTime(NewDateUtils.changeMonth(DmmSchedule.getNextRunTime(),1));
////        int maxday = calendar.get(Calendar.DAY_OF_MONTH);
////        new Date(DmmSchedule.getNextRunTime().getTime()+TimeUnit.DAYS.toMillis(maxday));
//
//        //计算 下次提醒时间
//        Date runTime=DmmSchedule.getNextRunTime();
//        if(DmmSchedule.getFreqType()>1) {//是重复日程
//            if (1==DmmSchedule.getFreqType()){
//                DmmSchedule.setNextRunTime(null);//一次性日程，下次提醒时间清空
//            }else if (4==DmmSchedule.getFreqType()) { //每天重复
//                Date nextRunTime = new Date(runTime.getTime()+TimeUnit.DAYS.toMillis(1));//下一天
//                DmmSchedule.setNextRunTime(nextRunTime);//新的下次提醒时间
//
//            }else if (8==DmmSchedule.getFreqType()){ // 每周重复
//                Date nextRunTime = new Date(runTime.getTime()+TimeUnit.DAYS.toMillis(7));//下一周
//                DmmSchedule.setNextRunTime(nextRunTime);//新的下次提醒时间
//
//            }else if (16==DmmSchedule.getFreqType()){//每月重复
//                if (DmmSchedule.isSpecial()) {// 是特殊时间提醒（每月倒数第几日）
//                    Calendar calendar = Calendar.getInstance();
//                    calendar.setTime(NewDateUtils.getLastTimeOfMonth(NewDateUtils.changeMonth(runTime,1)));
//                    int days=calendar.get(Calendar.DAY_OF_MONTH);//调取当月的天数
//                    Date nextRunTime = new Date(runTime.getTime() + TimeUnit.DAYS.toMillis(days));
//
//                    DmmSchedule.setNextRunTime(nextRunTime);//新的下次提醒时间
//                }else {
//                    Calendar calendar = Calendar.getInstance();
//                    Date nextRunTime;
//                    int i=1;
//                    do{
//                        calendar.setTime(runTime);
//                        calendar.add(Calendar.MONTH, i); // 月份再调至下个月；
//                        nextRunTime=calendar.getTime();
//                        i++;
//                    }while (!NewDateUtils.getDate(runTime).equals(NewDateUtils.getDate(nextRunTime)));// 这月提醒 日期那天 不等于 下月提醒日期那天
//                    DmmSchedule.setNextRunTime(nextRunTime);
//                }
//            }else if (32==DmmSchedule.getFreqType()){//每年重复
//                Calendar calendar = Calendar.getInstance();
//                Date nextRunTime;
//                int i=1;
//                do {
//                    calendar.setTime(runTime);
//                    calendar.add(Calendar.YEAR,i);//年调至下一年
//                    nextRunTime=calendar.getTime();
//                    i++;
//                }while (!NewDateUtils.getDate(runTime).equals(NewDateUtils.getDate(nextRunTime)));// 这年提醒 月和日 不等于 下一年 月和日
//                DmmSchedule.setNextRunTime(nextRunTime);
//            }
//        }
//        DmmJob.setState(5);//状态:1-已完成、2-提醒中、3-不再提醒、4-提醒已过期、5-尚未开始提醒
//        DmmJob.setDescription(DmmSchedule.getDescription());
//        DmmJob.setPlace(DmmSchedule.getPlace());
//        DmmJobDao.save(DmmJob);
//        return DmmJob;
//    }

    @Override
    public Long addDmmJobByUserScheduleTask(DmmSchedule dmmSchedule, DmmJob dmmJob, Long runTime, Long now) {
        dmmJob.setSchedule(dmmSchedule);
//        dmmJob.setCreator(dmmSchedule.getUser());
        dmmJob.setCreateDate(new Date(now));
        dmmJob.setAccId(dmmSchedule.getAccId());
        dmmJob.setCreateName(dmmSchedule.getCreateName());
        dmmJob.setName(dmmSchedule.getName());
        dmmJob.setTitle(dmmSchedule.getTitle());
        dmmJob.setType(1);//约定提醒 -1
        dmmJob.setRunTime(new Date(runTime));//运行时间
        dmmJob.setUpdateDate(new Date(runTime));// 存成这条提醒的的初次运行时间
        if(runTime>now) {
            dmmJob.setEnabled(1);//需要提醒
            dmmJob.setState(5);//状态:1-已完成、2-提醒中、3-不再提醒、4-提醒已过期、5-尚未开始提醒
        } else {
            dmmJob.setEnabled(0);//需要提醒
            dmmJob.setState(4);//状态:1-已完成、2-提醒中、3-不再提醒、4-提醒已过期、5-尚未开始提醒
        }

        //计算 下次提醒时间
        if(dmmSchedule.getFreqType()>1) {//是重复日程
            Date begin,end;
            switch (dmmSchedule.getFreqType()) {
                case 4: //每天重复
                    runTime += TimeUnit.DAYS.toMillis(1);//下一天
                    break;
                case 8: // 每周重复
                    runTime += TimeUnit.DAYS.toMillis(7);//下一周
                    break;
                case 16: //每月重复
                if (dmmSchedule.isSpecial()) {// 是特殊时间提醒（每月倒数第几日）
                    begin = NewDateUtils.changeMonth(new Date(runTime),1);
                    end = NewDateUtils.changeMonth(new Date(runTime),2);
                    runTime += end.getTime()-begin.getTime();
                }else {
                    begin = NewDateUtils.changeMonth(new Date(runTime), 0);
                    end = NewDateUtils.changeMonth(new Date(runTime), 1);
                    runTime += end.getTime() - begin.getTime();
                }
                break;
                case 32: //每年重复
                    begin = NewDateUtils.getNewYearsDay(new Date(runTime));
                    end = NewDateUtils.changeYear(new Date(runTime),1);
                    runTime += end.getTime()-begin.getTime();
                    break;
            }
                dmmSchedule.setNextRunTime(new Date(runTime));//新的下次提醒时间
        } else {
            dmmSchedule.setNextRunTime(null);//一次性日程，下次提醒时间清空
            runTime = Long.MAX_VALUE;
        }

        dmmJob.setDescription(dmmSchedule.getDescription());
        dmmJob.setPlace(dmmSchedule.getPlace());
        dmmJobDao.save(dmmJob);
        System.out.println("addDmmJobByUserScheduleTask.DmmJob.getRunTime:"+dmmJob.getRunTime());
        return runTime;
    }

    @Override
    public List<DmmJob> getDmmJobListBySid(Integer sId) {
        String hql=" from DmmJob where scheduleId=:sId and isValid=true order by runTime asc";
        Map<String,Object> params=new HashMap<>();
        params.put("sId",sId);
        List<DmmJob> dmmJobList=  dmmJobDao.getListByHQLWithNamedParams(hql,params);
        return dmmJobList;
    }

    @Override
    public DmmJob getDmmJobById(Integer id) {
        return dmmJobDao.get(id);
    }

    @Override
    public void deleteDmmJob(DmmJob dmmJob) {
        dmmJobDao.delete(dmmJob);
    }

    @Override
    public List<DmmJob> getDmmJobListByDate(Long accId, Date beginDate, Date endDate, PageInfo pageInfo) {
        String hql=" from DmmJob where isValid=true and schedule.accId=:accId and :beginDate<runTime and runTime<:endDate order by runTime asc";

        Map<String,Object> params=new HashMap<>();
        params.put("accId",accId);
        params.put("beginDate",beginDate);
        params.put("endDate",endDate);
        List<DmmJob> dmmJobList= dmmJobDao.getListByHQLWithNamedParams(hql,params,pageInfo);
        return dmmJobList;
    }

    @Override
    public List<DmmJob> getDmmJobListByUserId(Long accId, String keyword, PageInfo pageInfo) {
        String hql=" from DmmJob where isValid=true and accId=:accId";
        Map<String,Object> params=new HashMap<>();
            params.put("accId", accId);
        if (!StringUtil.isNullOrEmpty(keyword)){
            hql+=" and (title like:keyword or description like:keyword or supplement like:keyword)";
            params.put("keyword","%"+keyword+"%");
        }
        List<DmmJob> dmmJobList=dmmJobDao.getListByHQLWithNamedParams(hql,params,pageInfo);
        return dmmJobList;
    }
}
