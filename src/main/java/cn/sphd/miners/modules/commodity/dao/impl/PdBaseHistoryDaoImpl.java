package cn.sphd.miners.modules.commodity.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.modules.commodity.dao.PdBaseHistoryDao;
import cn.sphd.miners.modules.commodity.entity.PdBase;
import cn.sphd.miners.modules.commodity.entity.PdBaseHistory;
import com.alibaba.fastjson.JSONArray;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class PdBaseHistoryDaoImpl extends BaseDao<PdBaseHistory,Serializable> implements PdBaseHistoryDao {
    @Override
    public int setHistory(PdBase pdBase) {
        PdBaseHistory history=new PdBaseHistory();
        BeanUtils.copyPropertiesIgnoreNull(pdBase,history);
        history.setId(null);
        history.setProduct(pdBase.getId());
        save(history);

        getSession().flush();

        return history.getId();
    }

    @Override
    public int setHistory(PdBase pdBase, String path, JSONArray jsonArray) {

        PdBaseHistory history=new PdBaseHistory();
        BeanUtils.copyPropertiesIgnoreNull(pdBase,history);
        history.setId(null);
        history.setProduct(pdBase.getId());
        history.setPath(path);
        history.setJson(jsonArray.toJSONString());
        save(history);

        getSession().flush();

        return history.getId();
    }
}
