package cn.sphd.miners.modules.commodity.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * 产品_检查控制方法
20240508 1.296产品特性新增对象 t_pd_check_control
 * 
 * <AUTHOR>
 * @date 2024-06-13
 */
@Entity(name="PdCheckControl")
@Table(name="t_pd_check_control")
public class PdCheckControl
{
    private static final long serialVersionUID = 1L;

    /** ID */

    private Long id;

    /** 机构ID */

    private Integer org;

    /** 名称 */

    private String name;

    /** 编码 */

    private String code;

    /** 简要说明 */

    private String briefDesc;

    /** 排序 */

    private Integer orders;

    /** 启用标志:true-启用,false-停用 */

    private Integer enabled;

    /** 备注 */
    private String memo;

    /** 创建人id */
    private Integer creator;

    /** 创建人 */
    private String createName;

    /** 创建时间 */
    private Date createDate;

    /** 修改人id */
    private Integer updator;

    /** 修改人 */
    private String updateName;

    /** 修改时间 */
    private Date updateDate;

    /** 操作:1-增,2-删,3-改,4-暂停,5-恢复 */
    private Integer operation;

    /** 修改前记录ID */

    /** 版本号,每次修改+1 */
    private Integer versionNo;

    /** 版本号,每次修改+1 */
    private Integer previousId;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Basic
    @Column(name = "org")
    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    @Basic
    @Column(name = "name")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Basic
    @Column(name = "code")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Basic
    @Column(name = "brief_desc")
    public String getBriefDesc() {
        return briefDesc;
    }

    public void setBriefDesc(String briefDesc) {
        this.briefDesc = briefDesc;
    }

    @Basic
    @Column(name = "orders")
    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    @Basic
    @Column(name = "enabled")
    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    @Basic
    @Column(name = "memo")
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Basic
    @Column(name = "creator")
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    @Basic
    @Column(name = "create_name")
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Basic
    @Column(name = "create_date")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "updator")
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    @Basic
    @Column(name = "update_name")
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Basic
    @Column(name = "update_date")
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Basic
    @Column(name = "operation")
    public Integer getOperation() {
        return operation;
    }

    public void setOperation(Integer operation) {
        this.operation = operation;
    }

    @Basic
    @Column(name = "previous_id")
    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    @Basic
    @Column(name = "version_no")
    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
}
