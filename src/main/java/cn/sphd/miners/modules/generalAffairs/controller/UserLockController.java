package cn.sphd.miners.modules.generalAffairs.controller;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.generalAffairs.entity.*;
import cn.sphd.miners.modules.generalAffairs.service.UserImportService;
import cn.sphd.miners.modules.generalAffairs.service.UserLockService;
import cn.sphd.miners.modules.material.entity.RespStatus;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @ClassName UserLuckController
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/28 11:01
 * @Version 1.0
 */
@Controller
@RequestMapping("/userLock")
public class UserLockController {
    @Autowired
    UserService userService;
    @Autowired
    UserHistoryService userHistoryService;
    @Autowired
    UserImportService userImportService;
    @Autowired
    OrgService orgService;
    @Autowired
    UserLockService userLockService;
    @Autowired
    RolePrincipalHistoryService rolePrincipalHistoryService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;

    @RequestMapping("/frozenAccount.do")
    public String unitIndex() {
        return "frozenAccount/frozenAccount";
    }

    //冻结账号首页接口
    //pageSize 每页条数 currentPageNo 当前页数
    @ResponseBody
    @RequestMapping("/userLockIndex.do")
    public ReqUserObject userLockIndex(User user, PageInfo pageInfo) throws IOException {
        ReqUserObject reqUserObject=new ReqUserObject();
        Integer org = user.getOid();
        List<User> list=userLockService.selectStaffList(org,pageInfo);
        reqUserObject.setUserList(list);
        if(pageInfo.getTotalPage()==0)
            pageInfo.setTotalPage(1);
        reqUserObject.setPageInfo(pageInfo);
        return reqUserObject;
    }

    //冻结账号判断接口
    //传值 要冻结的userId
    //返回值 status 1，可以冻结 ，0无权限冻结，-1代办着其他职工的工作
    @ResponseBody
    @RequestMapping("/determineRoleCode.do")
    public ReqUserObject determineRoleCode(User user, Integer userId) throws IOException {
        ReqUserObject reqUserObject=new ReqUserObject();
        List<User> list=userLockService.selectAllUserForUserId(userId);
        int status =userLockService.determineRoleCode(user,list);
        reqUserObject.setStatus(status);
        List<User> managerList=new ArrayList<>();
        for(User u:list)
        {
            if(u.getMasterUserID()==null)
                reqUserObject.setUser(u);
            else{
                managerList.add(u);
            }
        }
        reqUserObject.setManageList(managerList);
        return reqUserObject;
    }

    //可选代办人列表接口
    @ResponseBody
    @RequestMapping("/substituteList.do")
    public ReqUserObject substituteList(User user) throws IOException {
        ReqUserObject reqUserObject=new ReqUserObject();
        Integer org = user.getOid();
        List<User> list=userLockService.substituteList(org);
        reqUserObject.setUserList(list);
        return reqUserObject;
    }

    //冻结账号接口
    //userId;//被冻结人
    //substitute;//代办人
    //userList;//manageId,passiveUserId  被冻结人高管id,代办人 ,组成的json串
    //返回值 1成功，0失败
    @ResponseBody
    @RequestMapping("/userLockEnter.do")
    public RespStatus userLockEnter(User user, ReqUserLock reqUserLock, HttpServletRequest request, AuthInfoDto authInfo) throws IOException {
        RespStatus respStatus=new RespStatus();
        Integer org =user.getOid();
        List<SubstituteInfo> list = JSONArray.parseArray(reqUserLock.getUserList(),SubstituteInfo.class);
        for(SubstituteInfo s:list){
            rolePrincipalHistoryService.replaceManages(user.getUserID(),Arrays.asList(new AbstractMap.SimpleEntry<>(s.getManageId(),s.getPassiveUserId())),request,authInfo);
        }
        int status=userLockService.userLockEnter(user,reqUserLock);

        List<User> userList= userService.getUsersByMasterUserID(reqUserLock.getSubstitute());// 包含员工本身 的身份列表
        for(User user1:userList) {
            clusterMessageSendingOperations.convertAndSendToUser(user1.getUserID().toString(), "/changeRolePrincipals", null, null, user.getOid(), user.getOrganization().getName(), JSON.toJSONString(userList));
        }
        respStatus.setStatus(status);
        return respStatus;
    }
    //冻结记录接口  此接口为点击解冻和更换代理人时调用
    //userId;//被冻结人
    //返回值status为1代表操作人一致，为0代表，操作人不一致,取UserlockCreator为冻结操作人
    //UserLockList 为冻结记录，更换代理人时，显示历史
    @ResponseBody
    @RequestMapping("/selectUserLock.do")
    public ReqUserObject selectUserLock(User user,int userId) throws IOException {
        ReqUserObject reqUserObject=new ReqUserObject();
        reqUserObject.setUserId(userId);
        List<UserLock> list=userLockService.selectUserLock(userId);
        if(list.size()>0)
        {
            if(list.get(0).getCreator().equals(user.getUserID())){
                reqUserObject.setStatus(1);
            }else {
                reqUserObject.setStatus(0);
                reqUserObject.setUserlockCreator(list.get(0).getCreateName());
            }
        }
        reqUserObject.setUserLockList(list);
        return reqUserObject;
    }
    //更换代办人接口
    //userId;//被冻结人
    //substitute;//代办人
    @ResponseBody
    @RequestMapping("/updateSubstitute.do")
    public RespStatus updateSubstitute(User user,ReqUserLock reqUserLock) throws IOException {
        RespStatus respStatus=new RespStatus();
        Integer org = user.getOid();
        Integer userId=userLockService.selectMasterUserId(reqUserLock.getUserId());
        int status=userLockService.updateSubstitute(user,reqUserLock);
        respStatus.setStatus(status);
        //原代办人的身份推送
        List<User> userList= userService.getUsersByMasterUserID(userId);// 包含员工本身 的身份列表
        for(User user1:userList) {
            clusterMessageSendingOperations.convertAndSendToUser(user1.getUserID().toString(), "/changeRolePrincipals", null, null, user.getOid(), user.getOrganization().getName(), JSON.toJSONString(userList));
        }
        //新代办人的身份推送
        List<User> userListtSubstitute= userService.getUsersByMasterUserID(reqUserLock.getSubstitute());// 包含员工本身 的身份列表
        for(User user1:userListtSubstitute) {
            clusterMessageSendingOperations.convertAndSendToUser(user1.getUserID().toString(), "/changeRolePrincipals", null, null, user.getOid(), user.getOrganization().getName(), JSON.toJSONString(userListtSubstitute));
        }
        return respStatus;
    }
    //解冻接口
    //userId;//被冻结人
    @ResponseBody
    @RequestMapping("/clearUserLock.do")
    public RespStatus clearUserLock(User user,int userId) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=userLockService.clearUserLock(user,userId);
        respStatus.setStatus(status);
        return respStatus;
    }
    //当前被冻结的账号列表
    //pageSize 每页条数 currentPageNo 当前页数
    @ResponseBody
    @RequestMapping("/userLockCurrent.do")
    public ReqUserObject userLockCurrent(User user) throws IOException {
        ReqUserObject reqUserObject=new ReqUserObject();
        Integer org =user.getOid();
        List<User> list=userLockService.userLockCurrent(org);
        reqUserObject.setUserList(list);
        return reqUserObject;
    }

    //冻结账号记录
    //当前被冻结的账号列表
    //pageSize 每页条数 currentPageNo 当前页数
    //返回值
    //substituteName;//代理人名字  substituteMobile;//代理人手机号  gender;//性别1-男 0-女 postName;//职位  departName;//部门
    //operation //4-冻结帐号,5-解冻帐号,6-换代办人
    @ResponseBody
    @RequestMapping("/userLockAll.do")
    public ReqUserObject userLockAll(User user, PageInfo pageInfo) throws IOException {
        ReqUserObject reqUserObject=new ReqUserObject();
        Integer org =user.getOid();
        List<UserLock> list=userLockService.userLockAll(org,pageInfo);
        reqUserObject.setUserLockList(list);
        if(pageInfo.getTotalPage()==0)
            pageInfo.setTotalPage(1);
        reqUserObject.setPageInfo(pageInfo);
        return reqUserObject;
    }
}
