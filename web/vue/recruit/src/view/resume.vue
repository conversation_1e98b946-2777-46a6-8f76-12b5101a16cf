<template id="resume">
  <div class="resume">
    <van-nav-bar
      title="个人情况表"
      left-arrow
      @click-left="onClickLeft"
    />
    <van-overlay :show="loading">
      <div class="wrapper">
        <van-loading />
      </div>
    </van-overlay>
    <div class="main_container">
      <div>
        <van-cell-group>
          <div class="van-cell head">
            <van-image
              width="70"
              height="70"
              :src="recruitForm.imgPath"
            >
              <template v-slot:error>未设置</template>
            </van-image>
            <div class="user_simple">
              <span class="userName">{{recruitForm.userName}}</span>
              <div class="user_simple_gray">{{recruitForm.mobile}}</div>
              <div class="user_simple_gray" v-if="recruitForm.offerPost">应聘职位： {{recruitForm.offerPost}}</div>
            </div>
          </div>
          <div class="van-cell" v-if="(recruitForm.gender !== null && recruitForm.gender !== '') || recruitForm.age || recruitForm.date || (recruitForm.marry !== null && recruitForm.marry !== '')">
            <span v-if="recruitForm.gender !== null && recruitForm.gender !== ''">{{chargeRecruit('gender', recruitForm.gender)}}</span>
            <span v-if="recruitForm.age"><span class="break">|</span> {{chargeRecruit('age', recruitForm.birthday)}}岁</span>
            <span v-if="recruitForm.birthday"><span class="break">|</span> ({{recruitForm.birthday | formatDay('YYYY-MM-DD')}})</span>
            <span v-if="recruitForm.marry !== null && recruitForm.marry !== ''"><span class="break">|</span> {{chargeRecruit('marry', recruitForm.marry)}}</span>
          </div>
          <div class="van-cell" v-if="recruitForm.nation || recruitForm.politicalStatus || (recruitForm.degree !== null && recruitForm.degree !== '')">
            <span v-if="recruitForm.nation">{{recruitForm.nation}}</span>
            <span v-if="recruitForm.politicalStatus"><span class="break">|</span> {{recruitForm.politicalStatus}}</span>
            <span v-if="recruitForm.degree !== null && recruitForm.degree !== ''"><span class="break">|</span> {{chargeRecruit('degree', recruitForm.degree)}}</span>
          </div>
          <van-cell title="出生地" :value="recruitForm.address" v-if="recruitForm.address"/>
          <van-cell title="现居地：" :value="recruitForm.actualAddress" v-if="recruitForm.actualAddress"/>
          <van-cell title="身份证号" :value="recruitForm.idCard" v-if="recruitForm.idCard"/>
          <van-cell title="特长：" :value="recruitForm.interesting" v-if="recruitForm.interesting"/>
          <van-cell title="爱好：" :value="recruitForm.speciality" v-if="recruitForm.speciality"/>
          <van-cell title="期望薪资：" :value="recruitForm.offerSalaty" v-if="recruitForm.offerSalaty"/>
          <van-cell title="QQ： " :value="recruitForm.qq" v-if="recruitForm.qq "/>
          <van-cell title="E-mail： " :value="recruitForm.email" v-if="recruitForm.email"/>
          <van-cell title="个人版本的简历：" v-if="recruitForm.filePath">
            <template #default>
              <a class="color-blue" :href="recruitForm.filePath" target="_blank">查看</a>
            </template>
          </van-cell>
        </van-cell-group>
      </div>
      <div class="panel">
        <div class="panel_title">个人技能</div>
        <div class="panel_content">
          <van-cell-group>
            <div class="van-cell" v-if="recruitForm.firstLanguage">第一外语语种：{{recruitForm.firstLanguage}} {{recruitForm.firstForeignLevel}}</div>
            <div class="van-cell" v-if="recruitForm.secondLanguage">第二外语语种：{{recruitForm.secondLanguage}} {{recruitForm.secondForeignLevel}}</div>
            <van-cell title="计算机：" :value="recruitForm.computerLevel" v-if="recruitForm.computerLevel"/>
            <van-cell title="其他技能描述：" :value="recruitForm.otherSkills" v-if="recruitForm.otherSkills"/>
          </van-cell-group>
        </div>
      </div>
      <div class="panel">
        <div class="panel_title">教育经历</div>
        <div class="panel_content">
          <div v-for="(education, index) in recruitForm.educations" v-bind:key="index">
            <div class="van-cell" v-if="education.beginTime">{{education.beginTime | formatDay('YYYY-MM-DD')}} - {{education.endTime | formatDay('YYYY-MM-DD')}}</div>
            <div class="van-cell" v-if="education.collegeName">{{education.collegeName}}</div>
            <div class="van-cell" v-if="education.collegeName || education.major || education.degreeDesc">
              <span v-if="education.collegeName">{{education.departmentName}}</span>
              <span v-if="education.major"> | {{education.major}}</span>
              <span v-if="education.degreeDesc"> | {{education.degreeDesc}}</span>
            </div>
            <div class="van-cell" v-if="education.majorDesc">专业描述 <br> {{education.majorDesc}} </div>
            <div class="van-cell" v-if="education.memo">补充说明 <br> {{education.memo}} </div>
          </div>
        </div>
      </div>
      <div class="panel">
        <div class="panel_title">工作经历</div>
        <div class="panel_content">
          <div v-for="(occupation, index) in recruitForm.occupations" v-bind:key="index">
            <div class="van-cell" v-if="occupation.beginTime">{{occupation.beginTime | formatDay('YYYY-MM-DD')}} - {{occupation.endTime | formatDay('YYYY-MM-DD')}}</div>
            <div class="van-cell" v-if="occupation.corpName">{{occupation.corpName}}</div>
            <div class="van-cell" v-if="occupation.jobDesc || occupation.corpSize">
              <span v-if="occupation.corpNature">{{occupation.corpNature}}</span>
              <span v-if="occupation.corpSize"> | {{occupation.corpSize}}</span>
            </div>
            <div class="van-cell" v-if="occupation.corpDepartment || occupation.post || occupation.salary">
              {{[occupation.corpDepartment, occupation.post, occupation.salary].filter(Boolean).join(' | ')}}
            </div>
            <div class="van-cell" v-if="occupation.memo"><span class="color-red">未继续工作原因：</span><br>{{occupation.memo}}</div>
            <div class="van-cell" v-if="occupation.jobDesc">工作描述 <br>{{occupation.jobDesc}}</div>
            <div class="van-cell" v-if="occupation.operatingDuty">工作职责 <br>{{occupation.operatingDuty}}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
  .main_container {
    height: calc(100vh - 66px);
  }
  .head{
    display: flex;
  }
  .head .userName{
    font-size: 16px;
    font-weight: bold;
  }
  .user_simple{
    margin-left: 12px;
  }
  .break{
    margin: 0 4px;
  }
</style>

<script>
export default {
  data () {
    return {
      recruitForm: {},
      id: '',
      loading: true
    }
  },
  created () {
    console.log('fileUrl', window.parent.$)

    let paramId = this.$route.params.id
    if (paramId) {
      this.id = paramId
      let that = this
      this.$http.post('../../../recruit/offerInfo.do', {id: paramId}, {
        emulateJSON: true
      }).then((response) => {
        let res = response.body
        if (res.success === 1) {
          let data = res.data
          let offer = data.offer
          offer.date = offer.birthday ? that.$moment(offer.birthday).format('YYYY-MM-DD HH:mm:ss') : ''
          if (offer.gender !== '') {
            offer.gender = Number(offer.gender)
          }
          if (offer.marry !== '') {
            offer.marry = Number(offer.marry)
          }
          offer.imgPath = offer.imgPath ? (window.parent.$.webRoot + '/upload/' + offer.imgPath) : ''
          offer.filePath = data.filePath ? (window.parent.$.webRoot + '/upload/' + data.filePath) : ''
          offer.educations = offer.personalEducations
          offer.occupations = offer.personnelOccupations
          that.recruitForm = offer
          console.log(that.recruitForm)
        } else {
          that.$toast.fail('获取失败！')
        }
        that.loading = false
      }).catch(function () {
        that.$toast.fail('系统错误，请重试！')
      })
    } else {
      let params = localStorage.getItem('recruitForm')
      this.recruitForm = JSON.parse(params)
      this.recruitForm.age = this.getAge(this.recruitForm.date)
      console.log(this.recruitForm)
    }
  },
  methods: {
    onClickLeft () {
      this.$router.go(-1)
    },
    getAge (birth) {
      if (birth) {
        let birthDay = new Date(birth)
        let nowDate = new Date()
        let year1 = nowDate.getFullYear()
        let year2 = birthDay.getFullYear()
        let age = year1 - year2
        console.log(year1, year2)
        return age
      }
    },
    getGuid () {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        let r = Math.random() * 16 | 0
        let v = c === 'x' ? r : (r & 0x3 | 0x8)
        return v.toString(16)
      })
    },
    chargeRecruit (name, data) {
      if (typeof data === 'string' || typeof data === 'number') {
        data = Number(data)
        if (name === 'gender') {
          if (data === 0 || data === 2) {
            return '女'
          } else if (data === 1) {
            return '男'
          } else {
            return ''
          }
        }
        if (name === 'marry') {
          if (data === 0 || data === 2) {
            return '已婚'
          } else if (data === 1) {
            return '未婚'
          } else {
            return ''
          }
        }
        if (name === 'degree') {
          let arr = ['研究生', '本科', '大专', '中专或高中', '其它']
          return arr[data - 1] || ''
        }
        if (name === 'age') {
          let birthDay = new Date(data)
          let nowDate = new Date()
          let year1 = nowDate.getFullYear()
          let year2 = birthDay.getFullYear()
          return year1 - year2
        }
      } else {
        return ''
      }
    },
    cancelFileDel (option, isDel) {
      let fileDelArr = JSON.parse(window.localStorage.getItem('fileDelArr') || '[]')
      let n = -1
      fileDelArr.forEach(function (item, index) {
        if (option.type === item.type && option[option.type] === item[item.type]) {
          n = index
          fileDelArr.splice(index, 1)
        }
      })
      if (n === -1) {
        console.log('可能面临风险')
        console.log('没匹配到：', option)
      }
      // 如果传了此字段为true，那么也同时会调用删除文件接口
      if (isDel) {
        let type = option.type
        let url = window.parent.$.webRoot + '/uploads/removeByFile.do'
        let data = {
          fileUid: option[type],
          userId: this.$route.params.userId
        }
        this.$http.post(url, data)
      }
      window.localStorage.setItem('fileDelArr', JSON.stringify(fileDelArr))
    },
    close () {
      if (typeof (window.WeixinJSBridge) !== 'undefined') {
        window.WeixinJSBridge.call('closeWindow')
      } else if (navigator.userAgent.indexOf('MSIE') > 0) {
        if (navigator.userAgent.indexOf('MSIE 6.0') > 0) {
          window.opener = null
          window.close()
        } else {
          window.open('', '_top')
          window.top.close()
        }
      } else {
        try {
          this.focus()
          self.opener = null
          self.close()
        } catch (e) {
        }
        try {
          window.opener = null
          window.open('', '_self', '')
          window.close()
        } catch (e) {
        }
        try {
          window.open(window.location, '_self').close()
        } catch (e) {
        }
        window.location.replace('about:blank')
      }
    }
  }
}
</script>
