package cn.sphd.miners.modules.material.service;


import cn.sphd.miners.modules.material.entity.MtBase;
import cn.sphd.miners.modules.material.entity.MtCategory;
import cn.sphd.miners.modules.material.entity.ReqMtObject;
import cn.sphd.miners.modules.material.entity.RespStatus;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;

/**
 * @ClassName MtImportService
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/9 11:20
 * @Version 1.0
 */
public interface MtImportService {
    //根据org 查询全部材料
    List<MtBase> getMtByOid(int org);
    RespStatus updateFalseUserEnter(MtBase mtBase);
    int whetherUnfinishedImport(int org,String isPurchased);
    ReqMtObject allImportMtEnter(ReqMtObject reqMtObject);
    ReqMtObject saveImportMt(ReqMtObject reqMtObject, User user, MtCategory mtCategory);
    int getNoUnitNumberCountByOrg(int org,String isPurchased);
    ReqMtObject unfinishedImportMt(String isPurchased,User user);
    int finishImportMt(String isPurchased,User user,int type,Integer category);
    RespStatus updateImportMt(MtBase mtBase);
    int deleteImportMt(int id,User user,String isPurchased);
    int finishImportMtEnter(String isPurchased,User user);
}
