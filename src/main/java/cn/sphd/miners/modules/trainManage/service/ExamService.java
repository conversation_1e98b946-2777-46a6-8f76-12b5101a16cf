package cn.sphd.miners.modules.trainManage.service;


import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.BadgeNumberCallback;
import cn.sphd.miners.modules.trainManage.dto.*;
import cn.sphd.miners.modules.trainManage.entity.TrainingExam;
import cn.sphd.miners.modules.trainManage.entity.TrainingExamBank;
import cn.sphd.miners.modules.trainManage.entity.TrainingExamRule;

import java.util.Date;
import java.util.List;

/**
 * @ClassName TrainService
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/2/3 8:13
 * @Version 1.0
 */
public interface ExamService extends  BadgeNumberCallback {
    List<TrainingExam> selectExamList(User user, Integer type, PageInfo pageInfo, Integer status);
    List<TrainingExam> selectFinishExamList(User user, Integer type, PageInfo pageInfo,Integer peroid);
    List<TrainingExam> selectFinishExamListByYear(User user, Integer type, PageInfo pageInfo,Integer year);
    RespPeroidExamList selectExamListByMonth(Integer org,Integer year);
    RespExamListForUser  selectExamForUserList(User user);
    RespExamInfoForUser  startExam(User user,int id);
    RespExamInfoForUser  selectExamDetail(User user,int id);
    RespExamInfoForUser  selectExamDetailErrors(User user,int id);
    Integer selectLastExam(User user);
    RespExamInfoForUser  practiceExam(User user,int id);
    int endExam(User user,int id);
    int updatePublicity(User user,Integer id,String publicity,Integer publicityRanking);
    Integer selectAnswerQuestion(User user,Integer examQuestion);
    RespExamInfoForUser addExamQuestionAnswer(User user,Integer examQuestion,String memo,String examKey);
    RespExamInfoForUser updateExamQuestionAnswer(User user,Integer examQuestion,String memo,String examKey,Integer answerId);
    List<User> selectStaffByOrgList(int org);
    RespCountQuestion selectOptionalQuestionCount(int id);
    int addTrainingExam(User user, ReqTrainingExam reqTrainingExam);
    int addTrainingExamAgain(User user, int id, Date endTime);

    int addTrainingExamQuestion(User user, TrainingExamBank trainingExamBank);
    //练习试题生成
    List<RespExamQuestion>  addpracticeExamQuestion(User user, TrainingExamBank trainingExamBank);
    void updateExamQuestionScore(List<TrainingExamRule> questionScoreList, TrainingExam trainingExam);
    //练习试题赋分
    List<RespExamQuestion> updatepracticeExamQuestionScore(List<TrainingExamRule> questionScoreList,List<RespExamQuestion> respExamQuestionList);
    void examOver(Integer id);
    void answerOver(Integer id);

    TrainingExam selectExamById(int id);
    List<RespExamBank> selectExamBankListByExam(int id);
    List<RespExamUser> selectExamUserListByExam(int id);
    List<RespExamUser> selectExamFailUserListByExam(int id);
    int whetherExamAgainByExam(int id);
    int selectExamAgainCountByExam(int id);
    List<RespExamUser> selectExamAgainUserListByExam(int id);
    RespExamAgain selectExamAgainDetail(int id,int user);
    List<RespExamQuestion> selectExamQuestionListByExam(int id);
    RespExamUserInfoList selectExamUserInfoList(int org,int peroid);
    RespExamUserInfo selectExamUserInfo(int org,int peroid,int user);
    RespExamUserInfo selectExamUserOnePassInfo(int org,int peroid,int user);
    RespExamUserInfo selectExamUserAgainInfo(int org,int peroid,int user);

    int stopExamJudge(User user,int id);
    int stopExam(User user,int id);

    void trainTaskDay();
    void exceptionsExam();
    void initializationPeroid();
    void initializationUserPeroid();

}


