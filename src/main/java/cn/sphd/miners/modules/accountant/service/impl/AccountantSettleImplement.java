package cn.sphd.miners.modules.accountant.service.impl;

import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.accountant.dao.AccountantSettleDao;
import cn.sphd.miners.modules.accountant.entity.*;
import cn.sphd.miners.modules.accountant.mapper.*;
import cn.sphd.miners.modules.accountant.service.AccountantSettleService;
import cn.sphd.miners.modules.accountant.task.AccountantTask;
import cn.sphd.miners.modules.accountant.util.Tools;
import cn.sphd.miners.modules.message.dao.MessageDao;
import cn.sphd.miners.modules.personal.service.UserMessageService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgPopedomService;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * Created by root on 17-2-8.
 */
@Service
@Transactional(readOnly=false)
public class AccountantSettleImplement implements AccountantSettleService {

//    @Autowired
//    private JdbcTemplate jdbcTemplate;

    @Autowired
    AccountantSettleDao asd;

    @Autowired
    SettleMapper settleMapper;
    @Autowired
    VoucherMapper voucherMapper;
    @Autowired
    VoucherHistoryMapper voucherHistoryMapper;
    @Autowired
    VoucherSubjectMapper voucherSubjectMapper;
    @Autowired
    DetailMapper detailMapper;
    @Autowired
    TAccountSubjectMapper subjectMapper;
    @Autowired
    SubjectPeroidMapper peroidMapper;
    @Autowired
    TAccountantCashMapper cashMapper;
    @Autowired
    CashFlowMapper cashFlowMapper;
    @Autowired
    TAccountSubjectMapper tAccountSubjectMapper;
    @Autowired
    TAccountantSubjectHistoryMapper subjectHistoryMapper;
    @Autowired
    TAccountantSubjectCashMapper subjectCashMapper;
    @Autowired
    OrgPopedomService orgPopedomService;
    @Autowired
    UserMessageService userMessageService;
    @Autowired
    UserService userService;
    @Autowired
    MessageDao messageDao;
    @Autowired
    AccountantTask accountantTask;
    @Autowired
    VoucherUpdate voucherUpdate;

    @Override
    public String getLastSettleTime(int oid) {
//        String hql = " and o.oid=" + oid + " order by o.id desc" ;
//        List<TAccountantSettleEntity> tas = asd.findCollectionByConditionNoPage(hql,null,null);
//        if (!tas.isEmpty())
//            return tas.get(0).getSettletime();
        return null;
    }


    /*
    * state 1表示把凭证都标记为已结帐
    * state 0表示未结帐
    * 小结帐的时候需要把上期凭证的结帐状态都修改成1，反结帐的时候都修改成0
    * */
    @Override
    public int changeVoucherSettleState(User user, int state){

        int oid = user.getOid();
        //凭证日期，上月最后一天
        String voucherDate = voucherUpdate.getPreMonthLastDay(oid);
        //点击结帐按钮时把上个月标记为已结帐，包括凭证表和历史凭证表
        TAccountantVoucher voucher = new TAccountantVoucher();
        voucher.setCreateDate(voucherDate);
        voucher.setOrg(oid);
        voucher.setIsSettled((byte) state);
        int flag = voucherMapper.updateSettle(voucher);

        TAccountantVoucherHistory history = new TAccountantVoucherHistory();
        history.setCreateDate(voucherDate);
        history.setOrg(oid);
        history.setIsSettled((byte) state);
        voucherHistoryMapper.updateHistorySettle(history);
        return flag;
    }

    /*设置科目的期初余额方向*/
    private void setPreviousDirection(BigDecimal previousBalance, TAccountantSubjectHistory subjectEntity) {
        if(previousBalance != null)
        {
            String subDirection = subjectEntity.getBalanceDirection();
            if(previousBalance.doubleValue() > 0)
            {
                subjectEntity.setPreviousDirection(subDirection);
            }
            else if(previousBalance.doubleValue() == 0)
            {
                subjectEntity.setPreviousDirection("3");
            }
            else
            {
                if("1".equals(subDirection))
                    subjectEntity.setPreviousDirection("2");
                else if("2".equals(subDirection))
                    subjectEntity.setPreviousDirection("1");
            }
        }
        else
        {
            subjectEntity.setPreviousDirection("3");
        }

    }

    @Override
    public int canselsSettleAccount(Integer oid) {
        String voucherDate = voucherUpdate.getPreMonthLastDay(oid);
//        String sqlUpdateVoucher = "UPDATE `t_accountant_voucher` SET is_settled=0  where is_settled=1 and create_date='" + voucherDate + "' AND org=" + oid;
//        jdbcTemplate.update(sqlUpdateVoucher);
        TAccountantVoucher param = new TAccountantVoucher();
        param.setCreateDate(voucherDate);
        param.setOrg(oid);
        voucherMapper.updateVoucherByDate(param);

        TAccountantVoucherHistory voucherHistory = new TAccountantVoucherHistory();
        voucherHistory.setCreateDate(voucherDate);
        voucherHistory.setOrg(oid);
        voucherHistoryMapper.updateHistoryByDate(voucherHistory);

        TAccountantSettle settle = new TAccountantSettle();
        settle.setCreateDate(voucherDate);
        settle.setOrg(oid);
        settleMapper.deleteByDate(settle);

//        String sqlUpdateVoucherHistory = "UPDATE `t_accountant_voucher_history` SET is_settled=0  where is_settled=1 and create_date='" + voucherDate + "' AND org=" + oid;
//        jdbcTemplate.update(sqlUpdateVoucherHistory);
//        String sqlDelete = "DELETE FROM `t_accountant_settle` WHERE DATE_FORMAT(settletime,'%Y-%m')=DATE_FORMAT('" + voucherDate + "','%Y-%m') AND oid=" + oid;
//        jdbcTemplate.update(sqlDelete);
        return 1;
    }

    @Override
    public int initializationKjMonth() {
        String nowtime = Tools.date2Str(new Date()).substring(5, 10);
        if (nowtime.equals("02-01")) {
//            List<TAccountantSubjectEntity> listAllSubject = subjectMapper.listAll();
//            for (TAccountantSubjectEntity subject : listAllSubject)
//            {
//                TAccountantSubjectEntity s = new TAccountantSubjectEntity();
//                s.setOrg(subject.getOrg());
//                s.setSubject(subject.getSubject());
//
//                s.setCreditAccumulative(new BigDecimal(0));
//                s.setDebitAccumulative(new BigDecimal(0));
//                s.setBeginningBalance(subject.getBalance());
//                s.setBeginningDirection(subject.getEndDirection());
//                int res = subjectMapper.updateBehiningMonth(s);
//            }
            subjectMapper.updateBeginning();
        }
        /*List<TAccountantVoucher> listVoucher = voucherMapper.listByUpdatorNull();
        for (TAccountantVoucher vou : listVoucher)
        {
            TAccountantVoucher v = new TAccountantVoucher();
            v.setBillPeriod(2);
            v.setBelongPeroid("2");
            v.setId(vou.getId());
            voucherMapper.update(v);
        }*/
        voucherMapper.updateVoucherByIsSettled();

        return 0;
    }


//    private String getPreMonthLastDay()
//    {
//        String voucherDate = "";
//        Calendar calendar = Calendar.getInstance();
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//        //上月最后一天,先将日期设置为本月的第一天，然后减去一天
//        calendar.set(Calendar.DAY_OF_MONTH, 1);
//        calendar.add(Calendar.DATE, -1);
//        voucherDate = sdf.format(calendar.getTime());
//        return voucherDate;
//    }

    /*试算*/
    public void trial(User user)
    {

        int oid = user.getOid();
        //凭证日期，上月最后一天
        String voucherDate = voucherUpdate.getPreMonthLastDay(oid);
        String period2 = voucherDate.substring(0,7);
        Calendar calendar = Calendar.getInstance();
        int yearCur = calendar.get(Calendar.YEAR);
//        int yearCur = Integer.parseInt(period2.substring(0,4));
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
//        calendar.add(Calendar.MONTH, -1);
//        calendar.set(Calendar.DAY_OF_MONTH, 1);
        //期数起始日期,上月第一天
//        sdf.applyPattern("yyyy-MM-dd");
//        String beginDay = sdf.format(calendar.getTime());
        String beginDay = period2 + "-01";
//        int month = calendar.get(Calendar.MONTH);
        int month = Integer.parseInt(period2.substring(5,7));
        //当期月报
//        sdf.applyPattern("yyyy-MM");
//        String term = sdf.format(calendar.getTime());
        String term = period2;

//        calendar.add(Calendar.MONTH, -1);

        Date date = NewDateUtils.dateFromString(voucherDate,"yyyy-MM-dd");
        Date changeDate = NewDateUtils.changeMonth(date,-1);
        String preMonth2 = NewDateUtils.dateToString(changeDate,"yyyy-MM");

//        int year = calendar.get(Calendar.YEAR);
        int year = Integer.parseInt(preMonth2.substring(0,4));

//        String termPre = sdf.format(calendar.getTime());
        String termPre = preMonth2;
        //写现金流量表时需要4步：
        //1、清空cash中间表的本期及年累计
        //2、把上两个月的年累计写回到cash中间表，如果上两个月是去年的话则不许要写
        //3、把本期的现金明细统计到cash中间表
        //4、把中间表的数据写到flow现金流量表

        TAccountantCash cashP = new TAccountantCash();
        cashP.setOrg(oid);
        cashMapper.cleanData(cashP);

        //如果上两个月不是去年,把上月的年累计写回到现金中间表
        if (year == yearCur || (year != yearCur && month == 11))
        {
            QueryData qd = new QueryData();
            qd.put("org",oid);
            qd.put("peroid",termPre);
            cashMapper.updateAccumulative(qd);
        }

        //把该机构下的科目暂存表中的balance和preBalance互换
        subjectHistoryMapper.swapBalance(oid);

//        sdf.applyPattern("yyyy-MM-dd HH:mm:ss");
//        String now = sdf.format(new Date());
        String now = NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss");

        //统计所有相关的凭证明细帐
        List<TAccountantSubjectDetail> listDetails = voucherUpdate.getDetailsByVouchers(oid,voucherDate);

        if(listDetails.size() == 0)
            return;

        //把相关的明细帐写到科目周期表中，然后把设计到的金额统计到科目上，本期借、贷、余额。明细帐的余额和方向要用到年初余额和方向
        for (TAccountantSubjectDetail subDetail : listDetails)
        {
            //把现金明细数据写道cash中间表
            TAccountantSubjectCash cashParam = new TAccountantSubjectCash();
            cashParam.setDetailId(subDetail.getId());
            TAccountantSubjectCash subjectCash = subjectCashMapper.getSingleByDetailID(cashParam);
            if(subjectCash != null)
            {
                TAccountantCash cash = new TAccountantCash();
                cash.setOrg(oid);
                cash.setCashFlowCode(subjectCash.getCashFlowCode());
                cash.setAmount(subjectCash.getCredit());
                cash.setAccumulative(subjectCash.getCredit());
                cashMapper.updateCash(cash);
            }


            //得到该明细对应的科目
            TAccountantSubjectHistory subEntityParam = new TAccountantSubjectHistory();
            subEntityParam.setOrg(oid);
            subEntityParam.setSubject(subDetail.getSubject());
            TAccountantSubjectHistory subEntity = subjectHistoryMapper.getOneSubjecthistory(subEntityParam);
            if(subEntity != null)
            {
                //得到该科目明细所属凭证的凭证月份
//                    TAccountantVoucher vparam = new TAccountantVoucher();
//                    vparam.setId(subDetail.getVoucherid());
//                    TAccountantVoucher v = voucherMapper.getSingle(vparam);
//                    String belongPeroid = v.getBelongPeroid();
                String subject = subDetail.getSubject();
                double quantity = subEntity.getQuantity();
                //2、将审批通过后的科目数据写道科目周期表
                TAccountantSubjectPeroid subjectPeroidParam = new TAccountantSubjectPeroid();
                subjectPeroidParam.setPeriod(voucherDate);
                subjectPeroidParam.setSubject(subDetail.getSubject());
                subjectPeroidParam.setSubjectId(subEntity.getSubjectId());
                subjectPeroidParam.setCredit(subDetail.getCredit());
                subjectPeroidParam.setDebit(subDetail.getDebit());
                subjectPeroidParam.setState("1");//1代表试算
                //明细帐的余额、方向跟科目明细的余额、方向还有该科目本身的年初余额、方向都有关
                //如果科目明细的方向和科目年初方向相同则余额相加，否则相减
                String preDirection = subEntity.getEndDirection() == null ? "3" : subEntity.getEndDirection();
                String curDirection = subDetail.getBalanceDirection();
                BigDecimal pregBalance = subEntity.getBalance() == null ? BigDecimal.valueOf(0) : subEntity.getBalance();
                BigDecimal curBalance = subDetail.getBalance() == null ? BigDecimal.valueOf(0) : subDetail.getBalance();
                DirAndBalance dab = voucherUpdate.getBalanceAndDirection(preDirection,pregBalance,curDirection,curBalance);
                double creditQuantity = subDetail.getCreditQuantity();
                double debitQuantity = subDetail.getDebitQuantity();
                subjectPeroidParam.setBalanceDirection(dab.getDir());
                subjectPeroidParam.setBalance(dab.getBalance());
                subjectPeroidParam.setPreviousBalance(subEntity.getPreviousBalance());
                subjectPeroidParam.setPreviousDirection(subEntity.getPreviousDirection());
                subjectPeroidParam.setCreateDate(now);
                subjectPeroidParam.setSummary(subDetail.getSummary());
                subjectPeroidParam.setBeginningDirection(subEntity.getBeginningDirection() == null ? "3" : subEntity.getBeginningDirection());
                subjectPeroidParam.setBeginningBalance(subEntity.getBeginningBalance() == null ? BigDecimal.ZERO : subEntity.getBeginningBalance());
                subjectPeroidParam.setCreditQuantity(creditQuantity);
                subjectPeroidParam.setDebitQuantity(debitQuantity);
                subjectPeroidParam.setUnitPrice(subDetail.getUnitPrice());
                if(creditQuantity > 0)
                {
                    subjectPeroidParam.setQuantity(quantity + creditQuantity);
                }
                else if (debitQuantity > 0)
                {
                    subjectPeroidParam.setQuantity(quantity - debitQuantity);
                }

                peroidMapper.insert(subjectPeroidParam);


                BigDecimal credit = subDetail.getCredit() == null ? BigDecimal.valueOf(0) : subDetail.getCredit();
                BigDecimal debit = subDetail.getDebit() == null ? BigDecimal.valueOf(0) : subDetail.getDebit();
                BigDecimal creditA = subEntity.getCreditAccumulative() == null ? BigDecimal.valueOf(0) : subEntity.getCreditAccumulative();//本期借
                BigDecimal debitA = subEntity.getDebitAccumulative() == null ? BigDecimal.valueOf(0) : subEntity.getDebitAccumulative();//本期贷
                double subjectQuantity = subEntity.getQuantity();
                if (creditQuantity > 0)
                {
                    subEntity.setQuantity(quantity + creditQuantity);
                }
                else if (debitQuantity > 0)
                {
                    subEntity.setQuantity(quantity - debitQuantity);
                }
                subEntity.setCreditAccumulative(creditA.add(credit));
                subEntity.setDebitAccumulative(debitA.add(debit));
                subEntity.setBalance(dab.getBalance());
                subEntity.setEndDirection(dab.getDir());

                subjectHistoryMapper.update(subEntity);

            }

        }

        //把该机构下的科目暂存表中的balance和preBalance互换
//        subjectHistoryMapper.swapBalance(oid);

        //把详情写道科目周期表，按照科目统计，所以在写到表中的时候应该先判断是否存在
        //先汇总明细张，把重复的科目去掉
        listDetails = subjectDetailDuplicateRemoval(listDetails);

//        List<TAccountantSubjectPeroid> listPeroids = new ArrayList<TAccountantSubjectPeroid>();
        //存放所有涉及到的科目id
        List<Integer> subjectids = new ArrayList<Integer>();
        //将明细张写进科目周期表，有些值来自科目表，比如上期余额,期末余额，本期借，本期贷
        for(TAccountantSubjectDetail subjectDetail : listDetails)
        {
            String subject = subjectDetail.getSubject();
            TAccountantSubjectHistory subParam = new TAccountantSubjectHistory();
            subParam.setOrg(oid);
            subParam.setSubject(subject);
            int subjectID = 0;
            //上期余额
            BigDecimal balancePre = BigDecimal.valueOf(0);
            //本期借
            BigDecimal credit = BigDecimal.valueOf(0);
            //本期贷
            BigDecimal debit = BigDecimal.valueOf(0);

            TAccountantSubjectHistory subjectEntity = subjectHistoryMapper.getOneSubjecthistory(subParam);

            if(subjectEntity != null)
            {
                subjectID = subjectEntity.getSubjectId();
                balancePre = subjectEntity.getPreviousBalance() == null ? BigDecimal.valueOf(0) : subjectEntity.getPreviousBalance();
                credit = subjectDetail.getCredit() == null ? BigDecimal.valueOf(0) : subjectDetail.getCredit();
                debit = subjectDetail.getDebit() == null ? BigDecimal.valueOf(0) : subjectDetail.getDebit();
            }
            double quantity = subjectEntity.getQuantity();
            TAccountantSubjectPeroid subjectPeroid = new TAccountantSubjectPeroid();
            subjectPeroid.setState("1");
            subjectPeroid.setSubject(subject);
            subjectPeroid.setSubjectId(subjectID);
            //月报4位，即，2017-05
            subjectPeroid.setPeriod(term);
            subjectPeroid.setBeginDate(beginDay);
            subjectPeroid.setEndDate(voucherDate);
            //本期和上期余额都来自科目表
//                subjectPeroid.setPreviousBalance(balancePre);

            BigDecimal curBalance = subjectDetail.getBalance() == null ? BigDecimal.valueOf(0) : subjectDetail.getBalance();
            String preDirection = subjectEntity.getPreviousDirection() == null ? "3" : subjectEntity.getPreviousDirection();
            String curDirection = subjectDetail.getBalanceDirection();
            DirAndBalance dab = voucherUpdate.getBalanceAndDirection(preDirection,balancePre,curDirection,curBalance);
            subjectPeroid.setBalanceDirection(dab.getDir());
            subjectPeroid.setBalance(dab.getBalance());
            subjectPeroid.setPreviousDirection(preDirection);
            subjectPeroid.setPreviousBalance(balancePre);
            //本期借贷方
            subjectPeroid.setCredit(credit);
            subjectPeroid.setDebit(debit);
            //创建人信息,就是指当前登录人员
            subjectPeroid.setCreator(user.getUserID());
            subjectPeroid.setCreateName(user.getUserName());
            subjectPeroid.setCreateDate(now);
            subjectPeroid.setSummary("本月合计");
            subjectPeroid.setBeginningBalance(subjectEntity.getBeginningBalance());
            subjectPeroid.setBeginningDirection(subjectEntity.getBeginningDirection() == null ? "3" : subjectEntity.getBeginningDirection());
            subjectPeroid.setCreditQuantity(subjectDetail.getCreditQuantity());
            subjectPeroid.setDebitQuantity(subjectDetail.getDebitQuantity());
//            double creditQuantity = subjectDetail.getCreditQuantity();
//            double debitQuantity = subjectDetail.getDebitQuantity();
//            subjectPeroid.setQuantity(BigDecimal.valueOf(quantity.intValue() + creditQuantity - debitQuantity));
            subjectPeroid.setQuantity(quantity);
            subjectPeroid.setUnitPrice(subjectDetail.getUnitPrice());
            peroidMapper.insert(subjectPeroid);
            subjectids.add(subjectID);

            //如果该科目是二级或三级的话也要把父科目写道周期表里,如果周期表里已经有了就累计进去，如果没有的话就新增进去
            if (subject.length() > 4)
            {
                TAccountantSubjectPeroid parent = null;
                String subLevel2 = subject.substring(0,subject.length() - 4);
                //写二级科目
                subParam.setOrg(oid);
                subParam.setSubject(subLevel2);
                TAccountantSubjectHistory subjectLevel2 = subjectHistoryMapper.getOneSubjecthistory(subParam);

                //判断他的二级在周期表中有没有，有的话累加，没有的话新增
                TAccountantSubjectPeroid checkParentParam = new TAccountantSubjectPeroid();
                checkParentParam.setPeriod(term);
                checkParentParam.setSubjectId(subjectLevel2.getSubjectId());
                TAccountantSubjectPeroid level2 = peroidMapper.judgeAnnualReport(checkParentParam);
                if (level2 == null)
                {
                    //没有的话新增
                    parent = newParentPeroid(subLevel2,subjectLevel2,subjectPeroid,subjectids,"1");
                }
                else
                {
                    //如果父科目已经有了就需要把当前要写的科目累加上去
                    parent = accumulationThisPeroid(level2,subjectPeroid,subjectLevel2,"1");

                }

                //更新父科目的金额
                updateSubjectTable(parent,subjectLevel2,subjectPeroid);

                if (subLevel2.length() > 4)
                {
                    String subLevel1 = subLevel2.substring(0,subLevel2.length() - 4);
                    //写一级科目
                    subParam.setOrg(oid);
                    subParam.setSubject(subLevel1);
                    TAccountantSubjectHistory subjectLevel1 = subjectHistoryMapper.getOneSubjecthistory(subParam);
                    //判断他的二级在周期表中有没有，有的话累加，没有的话新增
                    checkParentParam.setPeriod(term);
                    checkParentParam.setSubjectId(subjectLevel1.getSubjectId());
                    TAccountantSubjectPeroid level1 = peroidMapper.judgeAnnualReport(checkParentParam);
                    if(level1 == null)
                    {
                        //判断他的一级在周期表中有没有，有的话累加，没有的话新增
                        parent = newParentPeroid(subLevel1,subjectLevel1,subjectPeroid,subjectids,"1");
                    }
                    else
                    {
                        //如果父科目已经有了就需要把当前要写的科目累加上去
                        parent = accumulationThisPeroid(level1,subjectPeroid,subjectLevel1,"1");
                    }
                    //更新父科目的金额
                    updateSubjectTable(parent,subjectLevel1,subjectPeroid);

                    if (subLevel1.length() > 4)
                    {
                        String subLevel0 = subLevel1.substring(0,subLevel1.length() - 4);
                        //写一级科目
                        subParam.setOrg(oid);
                        subParam.setSubject(subLevel0);
                        TAccountantSubjectHistory subjectLevel0 = subjectHistoryMapper.getOneSubjecthistory(subParam);
                        //判断他的二级在周期表中有没有，有的话累加，没有的话新增
                        checkParentParam.setPeriod(term);
                        checkParentParam.setSubjectId(subjectLevel0.getSubjectId());
                        TAccountantSubjectPeroid level0 = peroidMapper.judgeAnnualReport(checkParentParam);
                        if(level0 == null)
                        {
                            //判断他的一级在周期表中有没有，有的话累加，没有的话新增
                            parent = newParentPeroid(subLevel0,subjectLevel0,subjectPeroid,subjectids,"1");
                        }
                        else
                        {
                            //如果父科目已经有了就需要把当前要写的科目累加上去
                            parent = accumulationThisPeroid(level0,subjectPeroid,subjectLevel0,"1");
                        }
                        //更新父科目的金额
                        updateSubjectTable(parent,subjectLevel0,subjectPeroid);
                    }

                }
            }


//                BigDecimal creditA = subjectEntity.getCreditAccumulative() == null ? BigDecimal.valueOf(0) : subjectEntity.getCreditAccumulative();//本期借
//                BigDecimal debitA = subjectEntity.getDebitAccumulative() == null ? BigDecimal.valueOf(0) : subjectEntity.getDebitAccumulative();//本期贷
//                subjectEntity.setCreditAccumulative(creditA.add(credit));
//                subjectEntity.setDebitAccumulative(debitA.add(debit));
//                subjectEntity.setBalance(subjectPeroid.getBalance());
//                subjectEntity.setEndDirection(subjectPeroid.getBalanceDirection());
//                subjectHistoryMapper.update(subjectEntity);

            //判断是否存在该科目的年报，如果不存在就insert否则就累加
            //每个科目每年都只存在一个年报数据
//            listPeroids.add(subjectPeroid);
        }

        //本期总帐写完之后需要把科目表中年初或本年累计有数但本期中没有使用到的科目也写到周期表中
        QueryData qd = new QueryData();
        qd.put("term",term);
        qd.put("subidList",subjectids);
        qd.put("org",oid);
        qd.put("now",now);
        peroidMapper.insertImportantData(qd);

        //把科目暂存表的本年累计数据更新到科目周期表，查询往期数据的时候要用
        QueryData param = new QueryData();
        param.put("org",oid);
        param.put("peroid",term);
        peroidMapper.updateAccumulative(param);

        //按照公式统计各项目；子项目要累加到父项目上
        //把26的项目加到27上
        TAccountantCash plus27Param = new TAccountantCash();
        plus27Param.setOrg(oid);
        cashMapper.plus27to26(plus27Param);
        //把33的项目加到32上
        cashMapper.plus33to32(plus27Param);
        //统计第五项
        cashMapper.statistic5(plus27Param);
        //统计第10项
        cashMapper.statistic10(plus27Param);
        //统计第11项,第十项-第五项
        plus27Param.setCashFlowCode("r10c4");
        TAccountantCash cash10 = cashMapper.getSingleByCashFlowCode(plus27Param);
        plus27Param.setCashFlowCode("r5c4");
        TAccountantCash cash5 = cashMapper.getSingleByCashFlowCode(plus27Param);
        plus27Param.setCashFlowCode("r11c4");
        BigDecimal accumulative10 = cash10.getAccumulative() == null ? BigDecimal.valueOf(0) : cash10.getAccumulative();
        BigDecimal accumulative5 = cash5.getAccumulative() == null ? BigDecimal.valueOf(0) : cash5.getAccumulative();
        plus27Param.setAccumulative(accumulative5.subtract(accumulative10));
        BigDecimal amount10 = cash10.getAmount() == null ? BigDecimal.valueOf(0) : cash10.getAmount();
        BigDecimal amount5 = cash5.getAmount() == null ? BigDecimal.valueOf(0) : cash5.getAmount();
        plus27Param.setAmount(amount5.subtract(amount10));
        cashMapper.updateBycode(plus27Param);
        //统计第18项
        cashMapper.statistic18(plus27Param);
        //统计第23项
        cashMapper.statistic23(plus27Param);
        //统计第24项,第十八-第二十三
        plus27Param.setCashFlowCode("r18c4");
        TAccountantCash cash18 = cashMapper.getSingleByCashFlowCode(plus27Param);
        plus27Param.setCashFlowCode("r23c4");
        TAccountantCash cash23 = cashMapper.getSingleByCashFlowCode(plus27Param);
        plus27Param.setCashFlowCode("r24c4");
        BigDecimal accumulative18 = cash18.getAccumulative() == null ? BigDecimal.valueOf(0) : cash18.getAccumulative();
        BigDecimal accumulative23 = cash23.getAccumulative() == null ? BigDecimal.valueOf(0) : cash23.getAccumulative();
        plus27Param.setAccumulative(accumulative18.subtract(accumulative23));
        BigDecimal amount18 = cash18.getAmount() == null ? BigDecimal.valueOf(0) : cash18.getAmount();
        BigDecimal amount23 = cash23.getAmount() == null ? BigDecimal.valueOf(0) : cash23.getAmount();
        plus27Param.setAmount(amount18.subtract(amount23));
        cashMapper.updateBycode(plus27Param);
        //统计第30项
        cashMapper.statistic30(plus27Param);
        //统计第35项
        cashMapper.statistic35(plus27Param);
        //统计第36项,第30-第35
        plus27Param.setCashFlowCode("r30c4");
        TAccountantCash cash30 = cashMapper.getSingleByCashFlowCode(plus27Param);
        plus27Param.setCashFlowCode("r35c4");
        TAccountantCash cash35 = cashMapper.getSingleByCashFlowCode(plus27Param);
        plus27Param.setCashFlowCode("r36c4");
        BigDecimal accumulative30 = cash30.getAccumulative() == null ? BigDecimal.valueOf(0) : cash30.getAccumulative();
        BigDecimal accumulative35 = cash35.getAccumulative() == null ? BigDecimal.valueOf(0) : cash35.getAccumulative();
        plus27Param.setAccumulative(accumulative30.subtract(accumulative35));
        BigDecimal amount30 = cash30.getAmount() == null ? BigDecimal.valueOf(0) : cash30.getAmount();
        BigDecimal amount35 = cash35.getAmount() == null ? BigDecimal.valueOf(0) : cash35.getAmount();
        plus27Param.setAmount(amount30.subtract(amount35));
        cashMapper.updateBycode(plus27Param);
//        统计第39项，39的本期数等于40项上月的本期数，39的本年累计等于40项上年的本年累计
//        先获得上期的第40项的数据
        CashFlow cashFlowParam = new CashFlow();
        cashFlowParam.setOrg(oid);
        cashFlowParam.setPeriod(termPre);
        cashFlowParam.setCellCode("r40c4");
        cashFlowParam = cashFlowMapper.getCashDataByCode(cashFlowParam);
        if (cashFlowParam != null) {
            TAccountantCash plus39Param = new TAccountantCash();
            plus39Param.setOrg(oid);
            plus39Param.setCashFlowCode("r39c4");
//            plus39Param.setAmount(cashFlowParam.getAmount());
            plus39Param.setAmount(cashFlowParam.getAccumulative());
            //如果上期是去年末，也就是12月份的话，也需要给39的本年累计赋值
            String period = cashFlowParam.getPeriod();
            String preMonth = period.substring(5, 7);
            if ("12".equals(preMonth)) {
                plus39Param.setAccumulative(cashFlowParam.getAccumulative());
            }
            cashMapper.statistic39(plus39Param);
        }
        //统计第38项
        cashMapper.statistic38(plus27Param);
        //统计第40项
        cashMapper.statistic40(plus27Param);

        //最新的第39项的统计规则是：直接等于本月的最后一项。
        //也可以等于最后一项的本年累计，但是本年和本月的最后一项是相等的
        //上面注释的第39项的统计目前已经作废了，以防后来再改使用
//        plus27Param.setCashFlowCode("r39c4");
//        cashMapper.statistic39(plus27Param);

        //结帐之后需要把现金流量暂存表的数据写到现金流量表中t_accountant_cash_flow
        TAccountantCash cashTempParam = new TAccountantCash();
        cashTempParam.setOrg(oid);
        List<TAccountantCash> cashTempList = cashMapper.getCashTempList(cashTempParam);
        if(cashTempList != null)
        {
            for(TAccountantCash cashTemp : cashTempList)
            {
                CashFlow cashFlow = new CashFlow();
                cashFlow.setState("1");
                cashFlow.setOrg(oid);
                cashFlow.setPeriod(term);
                cashFlow.setAmount(cashTemp.getAmount());
                cashFlow.setAccumulative(cashTemp.getAccumulative());
                cashFlow.setCellCode(cashTemp.getCashFlowCode());
                cashFlow.setCreateDate(new Date());
                cashFlow.setCreateName(user.getUserName());
                cashFlow.setCreator(user.getUserID());
                cashFlowMapper.insert(cashFlow);
            }
        }

        //试算后需要更新结帐表的need_trial=0
        voucherUpdate.updateNeedTrialField(oid,"0");
    }


    @Override
    public List<TAccountantSettle> listPage() {
        QueryData qd = new QueryData();
        return settleMapper.all();
    }

    @Override
    public int clickTaxBtn(User user, String taxDate) {
        //把最近一次的结账数据的报税状态更新为1
        int org = user.getOid();
        QueryData qd = new QueryData();
        qd.put("org",org);
        qd.put("taxDate",taxDate);
        int res = settleMapper.updateTaxState(qd);
        //发送已报税的消息给超管
        String msg = "本月已于" + taxDate + "报税完毕";
//        AccountantTask accountantTask = new AccountantTask();
        accountantTask.sendMes2Super(org,msg);
        return res;
    }

    @Override
    public int updateMessage(Integer mid) {
        String messageHql = "update UserMessage m set m.state=:state where m.id=:id ";
        Map<String,Object> msgParams = new HashMap<>();
        msgParams.put("state",2);
        msgParams.put("id",mid);//
        int res = messageDao.queryHQLWithNamedParams(messageHql,msgParams);
        return res;
    }

    public TAccountantSubjectPeroid accumulationThisPeroid(TAccountantSubjectPeroid parentPeroid, TAccountantSubjectPeroid childPeroid, TAccountantSubjectHistory subjectHistory,String state) {

        AccumulationEntity parent = new AccumulationEntity();
        AccumulationEntity child = new AccumulationEntity();
        parent.setBalanceDirection(parentPeroid.getBalanceDirection());
        parent.setBalance(parentPeroid.getBalance());
        parent.setCredit(parentPeroid.getCredit());
        parent.setDebit(parentPeroid.getDebit());
        child.setBalanceDirection(childPeroid.getBalanceDirection());
        child.setBalance(childPeroid.getBalance());

        child.setCredit(childPeroid.getCredit());
        child.setDebit(childPeroid.getDebit());
        parent = voucherUpdate.accumulationThisPeroid(parent,child);

        if (!"4".equals(state)) {
            //父科目的余额是用借方减贷方加期初得出来的，不是统计子科目余额得出来的，因为子科目在第二个月不见得都会用
            //得到父科目的本身方向
            String subDir = subjectHistory.getBalanceDirection();
            //统计借贷方
            BigDecimal credit = parent.getCredit() == null ? BigDecimal.valueOf(0) : parent.getCredit();
            BigDecimal debit = parent.getDebit() == null ? BigDecimal.valueOf(0) : parent.getDebit();
            //得到期初余额
            BigDecimal preBalance = subjectHistory.getPreviousBalance();
            String preDir = subjectHistory.getPreviousDirection();
            BigDecimal balance = BigDecimal.valueOf(0);

            String balanceDir = subDir;
            if(credit.compareTo(debit) > 0)
            {
                balance = credit.subtract(debit);
                balanceDir = "1";
            }
            if(credit.compareTo(debit) < 0)
            {
                balance = debit.subtract(credit);
                balanceDir = "2";
            }
            if(credit.compareTo(debit) == 0)
            {
                balanceDir = "3";
            }
            DirAndBalance dab = voucherUpdate.getBalanceAndDirection(preDir,preBalance,balanceDir,balance);
            parentPeroid.setBalance(dab.getBalance());
            parentPeroid.setBalanceDirection(dab.getDir());
            parentPeroid.setPreviousBalance(preBalance);
            parentPeroid.setPreviousDirection(preDir);
            parentPeroid.setCredit(credit);
            parentPeroid.setDebit(debit);
        }

        peroidMapper.update(parentPeroid);
        return parentPeroid;

    }

    /*
    * 得到科目实时科目余额
    * */
    @Override
    public JSONObject getSubjectBalanceRealTime(int org) {
        /*
        * 1、如果当前没有记账或总结账，需要根据科目明细账表读取数据计算科目余额
        * 2、如果已经记账或结账的话直接读取科目表的余额即可
        * 3、在读取或计算之前先判断该机构是否启用了会计模块
        * */
        JSONObject result = new JSONObject();
         Integer orgID = settleMapper.enableAccountant(org);
         if (orgID != null) {
             TAccountantSettle settle = settleMapper.judgeCharge(org);
             if (settle != null) {
                 String state = settle.getState();
                 //1-试算 2-记账 3-结账
                 //如果是记账或结账的话直接读取科目的数据，记账的话读取的是科目暂存表的数据，结账的话读取的是科目原表的数据
                 TAccountantSubjectHistory subjectEntity = null;
                 switch (state) {
                     case "2":
                         TAccountantSubjectHistory subParam = new TAccountantSubjectHistory();
                         subParam.setOrg(org);
                         subParam.setSubject("********");
                         subjectEntity = subjectHistoryMapper.getOneSubjecthistory(subParam);
                         result.put("********",subjectEntity.getBalance());

                         subParam.setSubject("********");
                         subjectEntity = subjectHistoryMapper.getOneSubjecthistory(subParam);
                         result.put("********",subjectEntity.getBalance());

                         subParam.setSubject("********");
                         subjectEntity = subjectHistoryMapper.getOneSubjecthistory(subParam);
                         result.put("********",subjectEntity.getBalance());

                         subParam.setSubject("********");
                         subjectEntity = subjectHistoryMapper.getOneSubjecthistory(subParam);
                         result.put("********",subjectEntity.getBalance());
                         return result;
                     case "3":
                         TAccountantSubjectEntity ordinaryEntity = null;
                         TAccountantSubjectEntity subjectParam = new TAccountantSubjectEntity();
                         subjectParam.setOrg(org);
                         subjectParam.setSubject("********");
                         ordinaryEntity = subjectMapper.getByOrgAndSubject(subjectParam);
                         result.put("********",ordinaryEntity.getBalance());

                         subjectParam.setSubject("********");
                         ordinaryEntity = subjectMapper.getByOrgAndSubject(subjectParam);
                         result.put("********",ordinaryEntity.getBalance());

                         subjectParam.setSubject("********");
                         ordinaryEntity = subjectMapper.getByOrgAndSubject(subjectParam);
                         result.put("********",ordinaryEntity.getBalance());

                         subjectParam.setSubject("********");
                         ordinaryEntity = subjectMapper.getByOrgAndSubject(subjectParam);
                         result.put("********",ordinaryEntity.getBalance());
                         return result;
                 }

             }
//             String voucherDate = voucherUpdate.getPreMonthLastDay(org);
             Date changeDate = NewDateUtils.changeMonth(new Date(),-1);
             Date date = NewDateUtils.getLastTimeOfMonth(changeDate);
             String voucherDate = NewDateUtils.dateToString(date,"yyyy-MM-dd");

             //统计所有相关的凭证明细帐
             List<TAccountantSubjectDetail> listDetails = voucherUpdate.getDetailsByVouchers(org,voucherDate);

             if(listDetails.size() == 0)
                 return null;

             //先汇总明细张，把重复的科目去掉
             listDetails = subjectDetailDuplicateRemoval(listDetails);

             for(TAccountantSubjectDetail subjectDetail : listDetails)
             {
                 String subject = subjectDetail.getSubject();
                 if (!"********".equals(subject) && !"********".equals(subject) && !"********".equals(subject) && !"********".equals(subject)) {
                     continue;
                 }
                 TAccountantSubjectEntity subParam = new TAccountantSubjectEntity();
                 subParam.setOrg(org);
                 subParam.setSubject(subject);
                 //上期余额
                 BigDecimal balancePre = BigDecimal.valueOf(0);
                 TAccountantSubjectEntity subjectEntity = subjectMapper.getByOrgAndSubject(subParam);

                 if(subjectEntity != null)
                 {
                     balancePre = subjectEntity.getBalance() == null ? BigDecimal.valueOf(0) : subjectEntity.getBalance();
                 }

                 BigDecimal curBalance = subjectDetail.getBalance() == null ? BigDecimal.valueOf(0) : subjectDetail.getBalance();
                 String preDirection = subjectEntity.getBalanceDirection();
                 String curDirection = subjectDetail.getBalanceDirection();
                 DirAndBalance dab = voucherUpdate.getBalanceAndDirection(preDirection,balancePre,curDirection,curBalance);

                 switch (subject) {
                     case "********":
                         result.put("********",dab.getBalance());
                         break;
                     case "********":
                         result.put("********",dab.getBalance());
                         break;
                     case "********":
                         result.put("********",dab.getBalance());
                         break;
                     case "********":
                         result.put("********",dab.getBalance());
                         break;
                 }


             }

         }

        return result;
    }

    //去掉重复的明细账，把相同的科目明细账相加然后删除冗余的
    private List<TAccountantSubjectDetail> subjectDetailDuplicateRemoval(List<TAccountantSubjectDetail> listDetails) {
        for(int i=0;i<listDetails.size() - 1;i++)
        {
            TAccountantSubjectDetail subjectDetail = listDetails.get(i);
            for(int j=i + 1;j<listDetails.size();j++)
            {
                if(subjectDetail.getSubject().equals(listDetails.get(j).getSubject()))
                {
                    //统计余额
                    TAccountantSubjectDetail nextDetail = listDetails.get(j);
                    //统计借方金额
                    BigDecimal prevCredit = subjectDetail.getCredit() == null ? BigDecimal.valueOf(0) : subjectDetail.getCredit();
                    BigDecimal nextCredit = nextDetail.getCredit() == null ? BigDecimal.valueOf(0) : nextDetail.getCredit();
                    prevCredit = prevCredit.add(nextCredit);
                    subjectDetail.setCredit(prevCredit);
                    // 统计贷方金额
                    BigDecimal prevDebit = subjectDetail.getDebit() == null ? BigDecimal.valueOf(0) : subjectDetail.getDebit();
                    BigDecimal nextDebit = nextDetail.getDebit() == null ? BigDecimal.valueOf(0) : nextDetail.getDebit();
                    prevDebit = prevDebit.add(nextDebit);
                    subjectDetail.setDebit(prevDebit);
                    //统计借数量
                    double prevCreditQuantity = subjectDetail.getCreditQuantity();
                    double prevDebitQuantity = subjectDetail.getDebitQuantity();
                    //统计贷数量
                    double nextCreditQuantity = nextDetail.getCreditQuantity();
                    double nextDebitQuantity = nextDetail.getDebitQuantity();

                    subjectDetail.setCreditQuantity(prevCreditQuantity + nextCreditQuantity);
                    subjectDetail.setDebitQuantity(prevDebitQuantity + nextDebitQuantity);
                    //统计单价
                    double prevUnitPrice = subjectDetail.getUnitPrice();
                    double nextUnitPrice = nextDetail.getUnitPrice();
                    subjectDetail.setUnitPrice(prevUnitPrice + nextUnitPrice);

                    //统计期末余额
                    String preDirection = subjectDetail.getBalanceDirection();
                    String curDirection = nextDetail.getBalanceDirection();
                    BigDecimal pregBalance = subjectDetail.getBalance() == null ? BigDecimal.valueOf(0) : subjectDetail.getBalance();
                    BigDecimal curBalance = nextDetail.getBalance() == null ? BigDecimal.valueOf(0) : nextDetail.getBalance();
                    DirAndBalance dab = voucherUpdate.getBalanceAndDirection(preDirection,pregBalance,curDirection,curBalance);
                    subjectDetail.setBalanceDirection(dab.getDir());
                    subjectDetail.setBalance(dab.getBalance());

                    listDetails.remove(j);
                    j--;
                }
            }
        }
        return listDetails;
    }

    public TAccountantSubjectPeroid newParentPeroid(String parentSubject, TAccountantSubjectHistory subject, TAccountantSubjectPeroid subjectPeroid,
                                                     List<Integer> subjectids,String state) {
        TAccountantSubjectPeroid parentPeroid = new TAccountantSubjectPeroid();
        parentPeroid.setState(state);
        parentPeroid.setSubject(parentSubject);
        parentPeroid.setSubjectId(subject.getSubjectId());
        parentPeroid.setPeriod(subjectPeroid.getPeriod());
        parentPeroid.setBeginDate(subjectPeroid.getBeginDate());
        parentPeroid.setEndDate(subjectPeroid.getEndDate());
        parentPeroid.setBalanceDirection(subjectPeroid.getBalanceDirection());
        parentPeroid.setBalance(subjectPeroid.getBalance());

        //如果不是建账时产生的周期表数据的话期末余额来自本期发生额，但是如果是建账产生的周期表数据的话则来自用户输入的期末余额数
        if (!"4".equals(state)) {
            //得到父科目的本身方向
            String subDir = subject.getBalanceDirection();
            BigDecimal credit = subjectPeroid.getCredit() == null ? BigDecimal.valueOf(0) : subjectPeroid.getCredit();
            BigDecimal debit = subjectPeroid.getDebit() == null ? BigDecimal.valueOf(0) : subjectPeroid.getDebit();
            String balanceDir = subDir;
            BigDecimal balance = BigDecimal.valueOf(0);
            if(credit.compareTo(debit) > 0)
            {
                balance = credit.subtract(debit);
                balanceDir = "1";
            }
            if(credit.compareTo(debit) < 0)
            {
                balance = debit.subtract(credit);
                balanceDir = "2";
            }
            if(credit.compareTo(debit) == 0)
            {
                balanceDir = "3";
            }
            BigDecimal preBalance = subject.getPreviousBalance();
            String preDir = subject.getPreviousDirection();
            DirAndBalance dab = voucherUpdate.getBalanceAndDirection(preDir,preBalance,balanceDir,balance);
            parentPeroid.setBalance(dab.getBalance());
            parentPeroid.setBalanceDirection(dab.getDir());
            parentPeroid.setPreviousBalance(preBalance);
            parentPeroid.setPreviousDirection(preDir);
            parentPeroid.setCredit(credit);
            parentPeroid.setDebit(debit);
        }
        else {
            parentPeroid.setBalance(subject.getBalance());
            parentPeroid.setBalanceDirection(subject.getEndDirection());
        }

        parentPeroid.setCreator(subjectPeroid.getCreator());
        parentPeroid.setCreateName(subjectPeroid.getCreateName());
        parentPeroid.setCreateDate(subjectPeroid.getCreateDate());
        parentPeroid.setSummary("本月合计");
        parentPeroid.setBeginningBalance(subject.getBeginningBalance());
        parentPeroid.setBeginningDirection(subject.getBeginningDirection());
        peroidMapper.insert(parentPeroid);
        subjectids.add(subject.getSubjectId());
        return parentPeroid;
    }

    private  void updateSubjectTable(TAccountantSubjectPeroid parent, TAccountantSubjectHistory subject, TAccountantSubjectPeroid subjectPeroid){
        BigDecimal creditA = subject.getCreditAccumulative() == null ? BigDecimal.valueOf(0) : subject.getCreditAccumulative();//本期借
        BigDecimal debitA = subject.getDebitAccumulative() == null ? BigDecimal.valueOf(0) : subject.getDebitAccumulative();//本期贷
        subject.setCreditAccumulative(creditA.add(subjectPeroid.getCredit()));
        subject.setDebitAccumulative(debitA.add(subjectPeroid.getDebit()));
        subject.setBalance(parent.getBalance());
        subject.setEndDirection(parent.getBalanceDirection());
        subjectHistoryMapper.update(subject);
    }

}
