<template>
  <div class="box">
    <div class="head">
      <PageHeader :title="tit" :showClose=true @mycloseFun="handleCustomClose" :showBack=true>
      </PageHeader>
    </div>
    <div class="sticky ty-fff" v-if="list">
      <p class="padding-0-15">{{ new
      Date(billStatistics.beginTime).format('yyyy-MM-dd') }}至{{ new
      Date(billStatistics.endDate).format('yyyy-MM-dd')
        }}期间收到的外部{{ billStatistics.billType == 2 ? '承兑汇票' : billStatistics.billType == 1 ? '转账支票' : 'err'}}</p>
      <p class="padding-0-15">共{{ list.numAll }}张，{{ list.amountAll == null ? 0 : this.formatCurrency(list.amountAll)
        }}元</p>
    </div>
    <div class="re margin_top_16" v-if="list">
      <div class="spaceBetween padding-0-15 ty-fff" v-if="list.available" @click="rou(1)">
        <div>
          <p>
            还在手中的
          </p>
          <p>共{{ list.available.num
            }}张，{{ list.available.amount == null ? 0 : this.formatCurrency(list.available.amount) }}元</p>
        </div>
        <div class="ic">
          <el-icon style="font-size: 20px">
            <arrow-right/>
          </el-icon>
        </div>
      </div>
      <div class="spaceBetween padding-0-15 cai-liGray" v-if="list.saveBanks" @click="rou(2)">
        <div>
          <p>
            存入银行的
          </p>
          <p>共{{ list.saveBanks.num
            }}张，{{ list.saveBanks.amount == null ? 0 : this.formatCurrency(list.saveBanks.amount) }}元</p>
        </div>
        <div class="ic">
          <el-icon style="font-size: 20px">
            <arrow-right/>
          </el-icon>
        </div>
      </div>
      <div class="spaceBetween padding-0-15 ty-fff" v-if="list.payReturns" @click="rou(4)">
        <div>
          <p>
            又付出去的
          </p>
          <p>共{{ list.payReturns.num
            }}张，{{ list.payReturns.amount == null ? 0 : this.formatCurrency(list.payReturns.amount) }}元</p>
        </div>
        <div class="ic">
          <el-icon style="font-size: 20px">
            <arrow-right/>
          </el-icon>
        </div>
      </div>
    </div>
  </div>

</template>

<script>
import {getReturnStatisticState} from '@/api/financeBill'
import {formatCurrency} from '@/utils/utils'

export default {
  name: "statisticsHome",
  data() {
    return {
      tit: "财会",
      billStatistics: {},
      list: {},
      formatCurrency: formatCurrency,
    }
  },
  methods: {
    rou(index) {
      let billStatistics = {
        billType: this.billStatistics.billType,
        beginTime: this.billStatistics.beginTime,
        state: index
      }
      localStorage.setItem('billStatistics', JSON.stringify(billStatistics))
      if (this.billStatistics.billType == 2) {
        this.$router.push({path: "/statisticsDetails"})
      } else if (this.billStatistics.billType == 1) {
        this.$router.push({path: "/cmoStatisticsDetails"})
      }
    },
    handleCustomClose() {
      this.$router.push("/caikuai")
    },
    getStatistics(billStatistics) {
      const params = {
        type: billStatistics.billType,
        beginTime: new Date(billStatistics.beginTime).format('yyyy-MM-dd')
      }
      getReturnStatisticState(params).then(res1 => {
        this.list = res1.data.data
      }).catch(error => {
        console.error('Error:', error);
      });
    }
  },
  computed: {},
  mounted() {
    let billStatistics = localStorage.getItem('billStatistics');
    this.billStatistics = JSON.parse(billStatistics);
    this.getStatistics(this.billStatistics)
  },
}
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";

.head {
  position: fixed;
  width: 100%;
  z-index: 999;
}

.sticky {
  line-height: 20px;
  padding: 10px 0 10px 0;
}

.re {
  position: relative;
  top: 40px;
  line-height: 25px;

  > div {
    padding: 10px 15px;
  }
}
</style>