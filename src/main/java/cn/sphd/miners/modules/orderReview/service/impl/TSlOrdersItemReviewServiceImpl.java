package cn.sphd.miners.modules.orderReview.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import cn.sphd.miners.modules.commodity.entity.PdCustomerAddress;
import cn.sphd.miners.modules.orderReview.entity.TSlOrdersItemReview;
import cn.sphd.miners.modules.orderReview.entity.TSlOrdersItemReviewHistory;
import cn.sphd.miners.modules.orderReview.mapper.TSlOrdersItemReviewHistoryMapper;
import cn.sphd.miners.modules.orderReview.mapper.TSlOrdersItemReviewMapper;
import cn.sphd.miners.modules.orderReview.service.ITSlOrdersItemReviewService;
import cn.sphd.miners.modules.sales.controller.SaleOrderController;
import cn.sphd.miners.modules.sales.controller.SalesController;
import cn.sphd.miners.modules.sales.dao.SlOrdersItemHistoryDao;
import cn.sphd.miners.modules.sales.entity.SlOrders;
import cn.sphd.miners.modules.sales.entity.SlOrdersItem;
import cn.sphd.miners.modules.sales.entity.SlOrdersItemHistory;
import cn.sphd.miners.modules.sales.entity.SlOrdersTeminate;
import cn.sphd.miners.modules.sales.service.PdCustomerAddressService;
import cn.sphd.miners.modules.sales.service.SaleService;
import cn.sphd.miners.modules.sales.service.SlOrdersService;
import cn.sphd.miners.modules.system.entity.User;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-24
 */
@Service
@Transactional
public class TSlOrdersItemReviewServiceImpl implements ITSlOrdersItemReviewService {
    @Autowired
    private TSlOrdersItemReviewMapper tSlOrdersItemReviewMapper;

    @Autowired
    private SaleService saleService;

    @Autowired
    private TSlOrdersItemReviewHistoryMapper tSlOrdersItemReviewHistoryMapper;

    @Autowired
    private SlOrdersItemHistoryDao ordersItemHistoryDao;

    @Autowired
    PdCustomerAddressService customerAddressService;

    @Autowired
    SlOrdersService ordersService;

    @Autowired
    SalesController salesController;

    @Autowired
    SaleOrderController saleOrderController;
    /**
     * 查询
     *
     * @param id 主键
     * @return 
     */
    @Override
    public TSlOrdersItemReview selectTSlOrdersItemReviewById(Long id) {
        return tSlOrdersItemReviewMapper.selectTSlOrdersItemReviewById(id);
    }

    /**
     * 查询列表
     *
     * @param tSlOrdersItemReview 
     * @return 
     */
    @Override
    public List<TSlOrdersItemReview> selectTSlOrdersItemReviewList(TSlOrdersItemReview tSlOrdersItemReview) {
        return tSlOrdersItemReviewMapper.selectTSlOrdersItemReviewList(tSlOrdersItemReview);
    }

    /**
     * 新增
     *
     * @param tSlOrdersItemReview 
     * @return 结果
     */
    @Override
    public int insertTSlOrdersItemReview(TSlOrdersItemReview tSlOrdersItemReview) {
        return tSlOrdersItemReviewMapper.insertTSlOrdersItemReview(tSlOrdersItemReview);
    }

    /**
     * 修改
     *
     * @param tSlOrdersItemReview 
     * @return 结果
     */
    @Override
    public int updateTSlOrdersItemReview(TSlOrdersItemReview tSlOrdersItemReview) {
        return tSlOrdersItemReviewMapper.updateTSlOrdersItemReview(tSlOrdersItemReview);
    }

    /**
     * 批量删除
     *
     * @param ids 需要删除的主键
     * @return 结果
     */
    @Override
    public int deleteTSlOrdersItemReviewByIds(Long[] ids) {
        return tSlOrdersItemReviewMapper.deleteTSlOrdersItemReviewByIds(ids);
    }

    /**
     * 删除信息
     *
     * @param id 主键
     * @return 结果
     */
    @Override
    public int deleteTSlOrdersItemReviewById(Long id) {
        return tSlOrdersItemReviewMapper.deleteTSlOrdersItemReviewById(id);
    }

    @Override
    public boolean orderSplit(HttpServletRequest request, HttpServletResponse response,Integer orderId, User user) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SlOrders orders = ordersService.getById(orderId);

        //1.317: 生产评审时，如某条数据K“能按时到货的数量”n被录入为小于订购数量m，则必已在“剩余数量的到货日期”中选择了一个原“要求到货日期”A之后的日期B 则拆分订单
        //拆分逻辑： 具有K特性的数据需拆分为两条要货计划：
        //——其中一条“订购数量”与“要求到货日期”的数据分别为n与A
        //——其中另一条“订购数量”与“要求到货日期”的数据分别为m-n与B
        //查询是否符合拆分逻辑
        Map<String, Object> items = saleService.getItemMapByOrderId(orderId, "id");

        List<HashMap> itemList = (List<HashMap>) items.get("data");

        List<Map<String, Object>> list1 = new ArrayList<>();//存订单1的明细
        List<Map<String, Object>> list2 = new ArrayList<>();//存订单2的明细

        for (HashMap i : itemList) {

            Integer itemId = (Integer) i.get("id");
            TSlOrdersItemReview itemReview = new TSlOrdersItemReview();
            itemReview.setOrdersItem(Long.valueOf(itemId));
            itemReview.setOrg(Long.valueOf(user.getOid()));

            //获取生产评审明细
            List<Map<String, Object>> preReviews = tSlOrdersItemReviewMapper.getPreReviewsByItemId(itemId);
            if (preReviews != null&& !preReviews.isEmpty()) {
                Map<String, Object> preReview = preReviews.get(preReviews.size()-1);
                Map<String, Object> item1 = new HashMap<>();
                Map<String, Object> item2 = new HashMap<>();


                BigDecimal amount = (BigDecimal) preReview.get("amount");//要货数量
                BigDecimal scheduledAmount = (BigDecimal) preReview.get("scheduledAmount");//可到货数量
                String deliveryDate = (String) preReview.get("deliveryDate");//要求到货日期
                String surplusDate = (String) preReview.get("surplusDate");//剩余到货日期

                if(!((Boolean) preReview.get("isScheduled"))){//不能按时到货

                    item1.put("itemId", itemId);
                    item1.put("amount", scheduledAmount);
                    item1.put("deliveryDate", deliveryDate);
                    list1.add(item1);

                    item2.put("itemId", itemId);
                    item2.put("amount", amount.subtract(scheduledAmount));
                    item2.put("deliveryDate", surplusDate);
                    list1.add(item2);

                }else {//能按时到货的 放到订单1
                    Map<String,Object> item = new HashMap<>();
                    item.put("itemId", itemId);
                    item.put("amount", amount);
                    item.put("deliveryDate", deliveryDate);
                    list1.add(item);

                }
            }

        }
        //订单基础信息的json为  save_slOrders: {"cusid":"2838","cusName":"客户名称1","signDate":"2024-11-13","invoiceRequire":"3","principal":"8931","deliveryType":1,"sn":"ui","contractAmount":"90.00","ordersAmount":"90.00","address":"1885"}
        //订单明细信息的json为 save_slOrdersItemList: [{"id":1666,"deliveryDate":"2024-11-27","amount":"30","address":"55","customerAddress":"1884","addressId":"1884","unitPriceNoinvoice":"3","priceDesc":""}]

        //开始生成订单1的基础信息json
        Map m1 = new HashMap();
        m1.put("cusid", orders.getCustomer_());
        m1.put("cusName", orders.getCustomerName());
        m1.put("signDate", sdf.format(orders.getSignDate()));
        m1.put("invoiceRequire", orders.getInvoiceRequire());
        m1.put("principal", orders.getProductionPrincipal());
        m1.put("deliveryType", orders.getDeliveryType());
        m1.put("sn", orders.getSn());
        m1.put("address", orders.getAddress());

        BigDecimal contractAmount = new BigDecimal(0);
        //生成订单1的明细json
        List<Map<String, Object>> itemList1 = new ArrayList<>();
        for (Map item : list1) {
            SlOrdersItem slOrdersItem = ordersService.getSlOrdersItemById((Integer)item.get("itemId"));
            if (slOrdersItem != null) {
                Map<String, Object> item1 = new HashMap<>();
                item1.put("id", slOrdersItem.getSalesRelationship_());
                item1.put("deliveryDate", item.get("deliveryDate"));
                item1.put("amount", item.get("amount"));
                item1.put("address",  slOrdersItem.getDeliveryAddress());
                item1.put("customerAddress", slOrdersItem.getDeliveryAddressId());
                item1.put("addressId", slOrdersItem.getDeliveryAddressId());
                item1.put("unitPriceNoinvoice", slOrdersItem.getUnitPriceNoinvoice());
                item1.put("unitPrice", slOrdersItem.getUnitPrice());
                item1.put("unitPriceInvoice", slOrdersItem.getUnitPriceInvoice());
                item1.put("unitPriceNoTax", slOrdersItem.getUnitPriceNotax());
                item1.put("unitPriceReference",slOrdersItem.getUnitPriceReference());
                item1.put("priceType", slOrdersItem.getPriceType());
                item1.put("priceDesc", slOrdersItem.getPriceDesc());
                itemList1.add(item1);
                //根据invoiceRequire计算金额
                if(orders.getInvoiceRequire()==1){
                    contractAmount = contractAmount.add((BigDecimal)item.get("amount")).multiply(slOrdersItem.getUnitPrice());
                }else if (orders.getInvoiceRequire()==2){
                    contractAmount = contractAmount.add((BigDecimal)item.get("amount")).multiply(slOrdersItem.getUnitPriceInvoice());
                }else
                contractAmount = contractAmount.add((BigDecimal)item.get("amount")).multiply(slOrdersItem.getUnitPriceNoinvoice());
            }
        }
        m1.put("contractAmount", contractAmount);
        m1.put("ordersAmount", contractAmount);
        //m1转为json字符串
        String orderFromSplit = JSONObject.fromObject(m1).toString();
        String orderItemsFromSplit = JSONArray.fromObject(itemList1).toString();

        //调用新增订单方法
        try {
            salesController.addSlOrders(request,user,response,null,null,null,null,orderFromSplit,orderItemsFromSplit,true,0);
        } catch (IOException | ParseException e) {
            throw new RuntimeException(e);
        }

        //订单拆分结束 删除原始订单
        SlOrdersTeminate slOrdersTeminate = new SlOrdersTeminate();
        slOrdersTeminate.setReason("订单由于评审后被拆分，该订单被终止");
        saleOrderController.deleteOrder(orderId,slOrdersTeminate,user,0);
        return true;
    }

    @Override
    public Map getReviewDetails(Integer orderId, User user) {
        //获取当前明细
        Map m = new HashMap();
        Map<String, Object> items = saleService.getItemMapByOrderId(orderId, "id");

        Date produceReviewDate = null;
        String reviewName= "";
        Date saleReviewDate = null;
        String saleName;
        List<HashMap> itemList = (List<HashMap>) items.get("data");

        if (itemList != null&&!itemList.isEmpty()) {
            Date s = (Date) itemList.get(0).get("update_date");
            saleReviewDate = s==null?(Date) itemList.get(0).get("create_date"):s;
            String sl = (String) itemList.get(0).get("update_name");
            saleName = StringUtils.isBlank(sl) ? (String) itemList.get(0).get("create_name") :sl;
        }

        //获取上一次的评审明细

        List<HashMap> is = (List<HashMap>) items.get("data");
        for (HashMap i : is) {
            Integer itemId = (Integer) i.get("id");
            TSlOrdersItemReview itemReview = new TSlOrdersItemReview();
            itemReview.setOrdersItem(Long.valueOf(itemId));
            itemReview.setOrg(Long.valueOf(user.getOid()));
            //获取生产评审明细
            List<TSlOrdersItemReview> reviews = tSlOrdersItemReviewMapper.selectTSlOrdersItemReviewList(itemReview);

            List<Map<String, Object>> preReviews = tSlOrdersItemReviewMapper.getPreReviewsByItemId(itemId);
            i.put("reviews",preReviews);

//            i.put("reviews", reviews);
            if (reviews != null&&!reviews.isEmpty()) {
                produceReviewDate = reviews.get(reviews.size()-1).getCreateDate();
                reviewName = reviews.get(reviews.size()-1).getCreateName();
            }

            TSlOrdersItemReviewHistory itemReviewHistory = new TSlOrdersItemReviewHistory();
            itemReviewHistory.setOrdersItem(Long.valueOf(itemId));
            List<TSlOrdersItemReviewHistory> reviewHistories = tSlOrdersItemReviewHistoryMapper.selectTSlOrdersItemReviewHistoryList(itemReviewHistory);
            i.put("reviewsHistory", reviewHistories);


            //加入分批发货数据
            itemReview = new TSlOrdersItemReview();
            itemReview.setOrdersItem(Long.valueOf(itemId));
            itemReview.setOrg(Long.valueOf(user.getOid()));
            itemReview.setState(3L);
            //获取生产评审明细
            List<TSlOrdersItemReview> partialShipment =  tSlOrdersItemReviewMapper.selectTSlOrdersItemReviewList(itemReview);
            i.put("partialShipment", partialShipment);

            //加入修改历史
            List<SlOrdersItemHistory> itemHistories = ordersItemHistoryDao.getListByHQL(" from SlOrdersItemHistory o where o.item_ = ?0 order by o.id desc",i.get("id"));
            if (itemHistories != null&& !itemHistories.isEmpty()){
                SlOrdersItemHistory itemHistory = itemHistories.get(0);

                SlOrdersItemHistory h = new SlOrdersItemHistory();
                BeanUtils.copyProperties(itemHistory,h);

                h.setItem(null);
                h.setDelivery_date(itemHistory.getDeliveryDate());

                if(itemHistory.getPeroid()!=null){
                    PdCustomerAddress pdCustomerAddress = customerAddressService.getAddressById(itemHistory.getPeroid());
                    if (pdCustomerAddress != null) {
                        h.setDelivery_address(pdCustomerAddress.getAddress());
                    }

                }
                i.put("itemHistories", h);

            }
        }
        m.put("items", items);
        m.put("produceReviewDate",produceReviewDate);
        m.put("reviewName",reviewName);
        m.put("saleReviewDate",saleReviewDate);
        m.put("saleName",reviewName);

        return m;
    }
}
