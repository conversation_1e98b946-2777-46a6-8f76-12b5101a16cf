package cn.sphd.miners.modules.schedule.entity;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.util.Date;

/**
 * Created by Administrator on 2020/7/28.
 * 日程分享接收人表
 */
@Entity
@Table(name = "t_memo_schedule_share_recipients")
public class MemoScheduleShareRecipients {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="schedule_share"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer scheduleShareId;//日程分享ID

    @Column(name="recipient"   , nullable=true , unique=false)
    private Integer recipient; //接收人

    @Column(name="receive_time"   , nullable=true , unique=false)
    private Date receiveTime;// 接收时间

    @Column(name="memo"  , length=100 , nullable=true , unique=false)
    private String memo;   //备注

    @Column(name="orders"   , nullable=true , unique=false)
    private Integer orders;

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_time"   , nullable=true , unique=false)
    private Date createTime;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_time"   , nullable=true , unique=false)
    private Date updateTime;

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;//操作:1-增,2-删,3-改

    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId;

    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo;


    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="schedule_share", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private MemoScheduleShare scheduleShare;//日程fenxiang

    @Transient
    private String recipientUserName;;//接收人用户名

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getScheduleShareId() {
        return scheduleShareId;
    }

    public void setScheduleShareId(Integer scheduleShareId) {
        this.scheduleShareId = scheduleShareId;
    }

    public Integer getRecipient() {
        return recipient;
    }

    public void setRecipient(Integer recipient) {
        this.recipient = recipient;
    }

    public Date getReceiveTime() {
        return receiveTime;
    }

    public void setReceiveTime(Date receiveTime) {
        this.receiveTime = receiveTime;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }


    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public MemoScheduleShare getScheduleShare() {
        return scheduleShare;
    }

    public void setScheduleShare(MemoScheduleShare scheduleShare) {
        this.scheduleShare = scheduleShare;
    }

    public String getRecipientUserName() {
        return recipientUserName;
    }

    public void setRecipientUserName(String recipientUserName) {
        this.recipientUserName = recipientUserName;
    }
}
