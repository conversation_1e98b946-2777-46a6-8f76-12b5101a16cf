package cn.sphd.miners.modules.thirdPlatform.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table ( name ="t_tp_app" )
public class TpApp extends BaseEntity {
    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;//`id` INTEGER NOT NULL AUTO_INCREMENT COMMENT '主键',
    @Column(name = "tg_code")
    private Byte tgCode;//enum TgCode //`tg_code` TINYINT DEFAULT NULL COMMENT '第三方大厂(1-weChat,2-byteDance)',
    @Column(name = "app_id", length=34)
    private String appId;//`app_id` VARCHAR(34) CHARACTER SET latin1 COLLATE 'latin1_bin' DEFAULT NULL COMMENT 'appId',
    @Column(name = "app_secret", length=64)
    private String appSecret;//`app_secret` VARCHAR(64) CHARACTER SET latin1 COLLATE 'latin1_bin' DEFAULT NULL COMMENT 'appSecret',
    @Column
    private String mchid;//`mchid` VARCHAR(32) NULL DEFAULT NULL COMMENT '商户号' COLLATE 'latin1_bin',
    @Column(name = "need_acc")
    private Boolean needAcc;//`need_acc` BOOLEAN DEFAULT NULL COMMENT '需要注册AuthAcc登录使用',
    @Column(name = "need_user_info")
    private Boolean needUserInfo;//`need_user_info` BOOLEAN DEFAULT NULL COMMENT '需要获取第三方UserInfo',
    @Column(name = "tp_type")
    private Byte tpType;//`tp_type` TINYINT DEFAULT NULL COMMENT '类型:1-第三方小程序,2-独立小程序,3-移动应用,4-网站应用',
    @Column(name = "group_id")
    private Integer groupId;//`group_id` INTEGER DEFAULT NULL COMMENT '分组ID,同一分组的app支持共同绑定/解绑账号',
    @Column(name = "platform_id")
    private Integer platformId;//`platform_id` INTEGER DEFAULT NULL COMMENT '第三方平台ID',
    @Column(name = "app_name", length=30)
    private String appName;//`app_name` VARCHAR(30) DEFAULT NULL COMMENT '第三方应用名称appName/nickName,tt:4-30chars;wx:4-30chars',
    @Column
    private Boolean enabled;//`enabled` BOOLEAN DEFAULT NULL COMMENT '有效：true-受wonderss管理，更新accesstoken，false/null-不受wonderss管理，不获取accesstoken',

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Byte getTgCode() {
        return tgCode;
    }

    public void setTgCode(Byte tgCode) {
        this.tgCode = tgCode;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getMchid() {
        return mchid;
    }

    public void setMchid(String mchid) {
        this.mchid = mchid;
    }

    public Boolean getNeedAcc() {
        return needAcc;
    }

    public void setNeedAcc(Boolean needAcc) {
        this.needAcc = needAcc;
    }

    public Boolean getNeedUserInfo() {
        return needUserInfo;
    }

    public void setNeedUserInfo(Boolean needUserInfo) {
        this.needUserInfo = needUserInfo;
    }

    public Byte getTpType() {
        return tpType;
    }

    public void setTpType(Byte tpType) {
        this.tpType = tpType;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public Integer getPlatformId() {
        return platformId;
    }

    public void setPlatformId(Integer platformId) {
        this.platformId = platformId;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
}
