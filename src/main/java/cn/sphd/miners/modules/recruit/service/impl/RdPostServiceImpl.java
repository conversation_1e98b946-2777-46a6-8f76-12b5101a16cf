package cn.sphd.miners.modules.recruit.service.impl;

import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.recruit.dao.RdPostDao;
import cn.sphd.miners.modules.recruit.dao.RdPostHistoryDao;
import cn.sphd.miners.modules.recruit.entity.RdPost;
import cn.sphd.miners.modules.recruit.entity.RdPostHistory;
import cn.sphd.miners.modules.recruit.service.RdPostService;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class RdPostServiceImpl implements RdPostService {

    @Autowired
    RdPostDao rdPostDao;
    @Autowired
    RdPostHistoryDao rdPostHistoryDao;

    @Override
    public void saveRdPost(RdPost rdPost) {
        rdPostDao.save(rdPost);
        saveRdPostHistory(rdPost,1);
    }

    public void  saveRdPostHistory(RdPost rdPost,Integer operation){
        RdPostHistory rdPostHistory=new RdPostHistory();
        BeanUtils.copyProperties(rdPost,rdPostHistory);
//        if (operation==1){
//            rdPostHistory.setEnabled(false); // 原始信息失效
//        }
        rdPostHistory.setCreateDate(new Date());
        rdPostHistory.setRdPost(rdPost);
        rdPostHistory.setOperation(operation);
        rdPostHistoryDao.save(rdPostHistory);
    }

    @Override
    public RdPost getRdPostById(Long id) {
        return rdPostDao.get(id);
    }

    @Override
    public List<RdPost> getRdPostsByOid(Integer oid,Boolean enabled) {
        String hql="from RdPost where org=:oid and enabled=:enabled";
        Map<String,Object> map=new HashMap<>();
        map.put("oid",oid);
        map.put("enabled",enabled);
        List<RdPost> rdPostList= rdPostDao.getListByHQLWithNamedParams(hql,map);
        return rdPostList;
    }

    @Override
    public void updateRdPost(RdPost rdPost, User user,Integer operation) {
        List<RdPostHistory> rdPostHistoryList=getRdPostHistoriesByPostId(rdPost.getId(),new Integer[]{operation,1});
        for (RdPostHistory rdPostHistory:rdPostHistoryList){
            rdPostHistory.setEnabled(false);
            rdPostHistoryDao.update(rdPostHistory);  //历史同类型的更改变成失效
        }
        rdPost.setUpdateDate(new Date());
        rdPost.setUpdator(user.getUserID());
        rdPost.setUpdateName(user.getUserName());
        rdPostDao.update(rdPost);
        saveRdPostHistory(rdPost,operation); //操作:1-增,2-删,3-改内容,4-启用,5-停用,6-更改名称,7-改错别字
    }

    @Override
    public List<RdPostHistory> getRdPostHistoriesByPostId(Long postId,Integer[] operations) {
        String hql="from RdPostHistory where postId=:postId and operation in(:operations)";
        Map<String,Object> map=new HashMap<>();
        map.put("postId",postId);
        map.put("operations",operations);
        List<RdPostHistory> rdPostHistories= rdPostHistoryDao.getListByHQLWithNamedParams(hql,map);
        return rdPostHistories;
    }

    @Override
    public void deleteRdPost(RdPost rdPost) {
        rdPostDao.delete(rdPost);
    }


    @Override
    public void saveRdPostHistory(RdPostHistory rdPostHistory,User user) {
        rdPostHistory.setCreator(user.getUserID());
        rdPostHistory.setCreateDate(new Date());
        rdPostHistory.setCreateName(user.getUserName());
        rdPostHistoryDao.save(rdPostHistory);
    }

    @Override
    public void rdPostExecuteChanges(Integer oid, Date beginDate) {
        String hql="from RdPostHistory where enabled=true and updateDate is null and org=:oid and beginTime<=:beginDate";
        Map<String,Object> map=new HashMap<>();
        map.put("oid",oid);
        map.put("beginDate",beginDate);
        List<RdPostHistory> rdPostHistoryList=rdPostHistoryDao.getListByHQLWithNamedParams(hql,map);
        Map<Long,Date> finalDateMap=new HashMap<>();// 职位最新更改时间 键值对
        for (RdPostHistory rdPostHistory:rdPostHistoryList){
            RdPost rdPost=rdPostHistory.getRdPost();
            Date finalDate=finalDateMap.get(rdPost.getId());
            if (finalDate==null||(finalDate!=null
                    &&finalDate.getTime()<rdPostHistory.getBeginTime().getTime())){
                rdPost.setName(rdPostHistory.getName());
                rdPost.setBeginTime(rdPostHistory.getBeginTime());
                rdPostDao.update(rdPost);
                finalDateMap.put(rdPost.getId(),rdPost.getBeginTime());

                String updateHql="update RdPostHistory set enabled=false where postId=:postId and operation in(1,6,7) and enabled=true and  beginTime<:finalDate";
                Map<String,Object> result=new HashMap<>();
                result.put("postId",rdPost.getId());
                result.put("finalDate",finalDate);
                rdPostHistoryDao.queryHQLWithNamedParams(updateHql,result);
            }
//            rdPost.setUpdateDate(finalDate);
            rdPostHistory.setUpdateDate(new Date()); //执行了实际更改时间
            rdPostHistoryDao.update(rdPostHistory);
        }
    }


    @Override
    public void updateRdPost(RdPost rdPost) {
        rdPostDao.update(rdPost);
    }
}
