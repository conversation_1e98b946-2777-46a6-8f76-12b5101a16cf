<template>
  <div id="applyPotentialQuery" class="query special potential">
    <TY_NavTop :title="searchTtl" isButton="true" @toggleSubmit="submit"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div style="padding: 0 4px;">
        <div class="tip tip-success">您既可按提交时间的范围查询，也可进行其他组合查询。</div>
      </div>
      <div class="ui-message">
        <div class="sendContainer">
          <el-form :model="queryForm" ref="queryForm" label-width="70px">
            <div>
              <h4 class="item-header">提交时间</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.type" size="small">
                  <el-radio label="7day" border>近七日</el-radio>
                  <el-radio label="thisMonth" border>本月</el-radio>
                  <el-radio label="3" border>自定义</el-radio>
                </el-radio-group>
              </div>
              <div class="el-form-item" v-show="queryForm.type === '3'">
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.beginDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker> -
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.endDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker>
              </div>
            </div>
            <div>
              <h4 class="item-header">审批结果</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.state" size="small">
                  <el-radio label="1" border>已正式合作</el-radio>
                  <el-radio label="0" border>终止接触</el-radio>
                </el-radio-group>
              </div>
            </div>
            <div v-if="source === '2'">
              <h4 class="item-header">录入者</h4>
              <div class="el-form-item">
                <span :class="queryForm.creator === ''?'btn_all btn_active':'btn_all'" v-on:click="chooseApplier(0)">全部</span>
                <span class="chooseApplier" v-on:click="chooseApplier(1)">{{queryForm.creatorName}} <i class="el-icon-arrow-right"></i></span>
              </div>
            </div>
            <div>
              <h4 class="item-header">销售负责人</h4>
              <div class="el-form-item">
                <span :class="queryForm.sale === ''?'btn_all btn_active':'btn_all'" v-on:click="chooseSaler(0)">全部</span>
                <span class="chooseApplier" v-on:click="chooseSaler(1)">{{queryForm.saleName}} <i class="el-icon-arrow-right"></i></span>
              </div>
            </div>
          </el-form>
        </div>
      </div>
    </div>
    <TY_AddressBook :rolelist="roleList" :title="选择销售负责人" v-bind:show.sync="bookVisible" @select_role="setRole"></TY_AddressBook>
  </div>
</template>
<script>
export default {
  name: 'applyPotentialQuery',
  data () {
    return {
      loading: false,
      roleType: '',
      searchTtl: '',
      source: this.$route.params.per,
      queryForm: {
        type: '7day',
        beginDate: '',
        endDate: '',
        state: '1',
        creator: '',
        creatorName: '选择录入者',
        sale: '',
        saleName: '选择销售负责人'
      },
      roleList: [],
      bookVisible: false
    }
  },
  created: function () {
    let _this = this
    if (_this.source === 1) {
      _this.searchTtl = '潜在客户申请'
    } else {
      _this.searchTtl = '潜在客户受理'
    }
  },
  methods: {
    chooseApplier: function (type) {
      let _this = this
      if (type === 0) {
        _this.queryForm.creator = ''
        _this.queryForm.creatorName = '选择录入者'
      } else {
        _this.roleType = '1' // 1=选择申请人
        this.axios.post('../../../sale/getEntryUser.do'
        ).then(function (response) {
          _this.roleList = response.data.list
          _this.bookVisible = true
        }).catch(function (error) {
          console.log(error)
        })
      }
    },
    chooseSaler: function (type) {
      let _this = this
      if (type === 0) {
        _this.queryForm.sale = ''
        _this.queryForm.saleName = '选择销售负责人'
      } else {
        _this.roleType = '2' // 2=选择销售负责人
        this.axios.post('../../../sale/getPotentialCustomerSales.do'
        ).then(function (response) {
          _this.roleList = response.data.list
          _this.bookVisible = true
        }).catch(function (error) {
          console.log(error)
        })
      }
    },
    setRole: function (data) {
      console.log('获取的申请人：' + JSON.stringify(data))
      if (this.roleType === '1') {
        this.queryForm.creator = data.userID
        this.queryForm.creatorName = data.userName
      } else if (this.roleType === '2') {
        this.queryForm.sale = data.userID
        this.queryForm.saleName = data.userName
      }
    },
    submit: function () {
      console.log(JSON.stringify(this.queryForm))
      let queryParam = {
        date: this.queryForm.type,
        source: this.source,
        state: this.queryForm.state,
        sale: this.queryForm.sale,
        saleName: this.queryForm.saleName,
        creatorName: this.queryForm.creatorName,
        creator: this.queryForm.creator
      }
      if (this.source === '1') {
        queryParam.creator = this.sphdSocket.user.userID
        queryParam.creatorName = ''
      }
      if (this.queryForm.type === '3') {
        queryParam.startTime = this.queryForm.beginDate
        queryParam.endTime = this.queryForm.endDate
      }
      localStorage.setItem('query', JSON.stringify(queryParam))
      this.$router.push({
        path: '/potentialQuerylist',
        name: 'potentialQuerylist',
        params: {
          data: queryParam
        }
      })
    },
    setSecondFeeCat: function (val) {
      this.loading = true
      let that = this
      this.$http.post('../../../reimburseWindow/getSecondCodeList.do', { id: val }, {
        emulateJSON: true
      }).then((response) => {
        that.loading = false
        let data = response.body
        if (data) {
          let data = response.data
          this.secondFeeCatList = data
        } else {
          console.log('加载失败！')
        }
      })
    }
  }
}
</script>
<style>
#applyPotentialQuery .el-radio{
  width: 96px; text-align: center;
}
#applyPotentialQuery .btn_all{
  width: 54px; text-align: center;
}
#applyPotentialQuery .chooseApplier{
  margin-left: 130px;
  cursor: pointer;
  text-align: right;
  display: inline-block; width: 121px;
}
</style>
