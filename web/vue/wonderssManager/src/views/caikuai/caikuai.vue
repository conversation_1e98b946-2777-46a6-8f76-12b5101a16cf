<template>
  <div class="accounting">
    <div>
      <div style="flex-grow: 1;display: flex; flex-direction: column;">
        <router-view/>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: "caikuai",
  data() {
    return {}
  },
  computed: {
  },
  mounted() {
    this.currJobData = this.$store.state.currJobData;

  },
  methods: {


  },

  beforeUpdate() {
    // console.log(in1)
    this.currTitle = this.$store.state.currTitle;
  },
}
</script>

<style lang="scss" scoped>
@import "src/style/account.scss";


</style>