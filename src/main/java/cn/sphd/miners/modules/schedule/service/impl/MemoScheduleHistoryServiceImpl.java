package cn.sphd.miners.modules.schedule.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.schedule.dao.MemoScheduleHistoryDao;
import cn.sphd.miners.modules.schedule.dao.MemoScheduleImageHistoryDao;
import cn.sphd.miners.modules.schedule.entity.MemoSchedule;
import cn.sphd.miners.modules.schedule.entity.MemoScheduleHistory;
import cn.sphd.miners.modules.schedule.entity.MemoScheduleImage;
import cn.sphd.miners.modules.schedule.entity.MemoScheduleImageHistory;
import cn.sphd.miners.modules.schedule.service.MemoScheduleHistoryService;
import cn.sphd.miners.modules.schedule.service.MemoScheduleService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.uploads.service.UploadService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * Created by Administrator on 2019/8/13.
 */
@Service("memoScheduleHistoryService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class MemoScheduleHistoryServiceImpl implements MemoScheduleHistoryService {
    @Autowired
    MemoScheduleHistoryDao memoScheduleHistoryDao;
    @Autowired
    MemoScheduleImageHistoryDao memoScheduleImageHistoryDao;
    @Autowired
    UploadService uploadService;
    @Autowired
    MemoScheduleService memoScheduleService;

    @Override
    public Map<String,Object> getMemoScheduleHistories(Integer id, PageInfo pageInfo) {
        String hql=" from MemoScheduleHistory where scheduleId=:scheduleId and source<6";
        Map<String,Object> params=new HashMap<>();
        params.put("scheduleId",id);
        List<MemoScheduleHistory> memoScheduleHistoryList=memoScheduleHistoryDao.getListByHQLWithNamedParams(hql,params,pageInfo);

        MemoSchedule memoSchedule=memoScheduleService.getMemoScheduleById(id);
        List<Map<String,Object>> mapList=new ArrayList<>();
        int number=0;
        if (pageInfo.getCurrentPageNo()>1){
            number=(pageInfo.getCurrentPageNo()-1)*pageInfo.getPageSize();
        }
        for (MemoScheduleHistory memoScheduleHistory:memoScheduleHistoryList){
            Map<String,Object> map=new HashMap<>();
            if (pageInfo.getCurrentPageNo()==1&&number==0){
                map.put("dataState","原始信息");
            }else {
                map.put("dataState","第"+number+"次修改后");
            }
            map.put("id",memoScheduleHistory.getId());
            map.put("updateName",memoScheduleHistory.getCreateName());//人名
            map.put("updateDate",memoScheduleHistory.getUpdateDate());//修改时间
            mapList.add(map);
            number+=1;
        }
        Map<String,Object> map=new HashMap<>();
        map.put("number",memoSchedule.getMemoScheduleHistoryHashSet().size()==0?0:memoSchedule.getMemoScheduleHistoryHashSet().size()-1);//最新的修改次数
        map.put("updateName",memoSchedule.getUpdateName());//人名
        map.put("updateDate",memoSchedule.getUpdateDate());//修改时间
        map.put("memoScheduleHistoryList",mapList);
        map.put("pageInfo",pageInfo);

        return map;
    }

    @Override
    public MemoScheduleHistory getNewMemoScheduleHistory(Integer id) {
        String hql=" from MemoScheduleHistory where scheduleId=:scheduleId order by id desc";
        Map<String,Object> params=new HashMap<>();
        params.put("scheduleId",id);
        MemoScheduleHistory memoScheduleHistory= (MemoScheduleHistory) memoScheduleHistoryDao.getByHQLWithNamedParams(hql,params);
        return memoScheduleHistory;
    }

    @Override
    public MemoScheduleHistory saveMemoScheduleHistory(MemoSchedule memoSchedule, User user, Boolean delFileUsing,String source) {
        MemoScheduleHistory scheduleHistory= this.getNewMemoScheduleHistory(memoSchedule.getId());//最新历史记录

        MemoScheduleHistory memoScheduleHistory=new MemoScheduleHistory();
        BeanUtils.copyProperties(memoSchedule,memoScheduleHistory);
        if (scheduleHistory==null){
            memoScheduleHistory.setVersionNo(1);//此次为第一次的历史记录
        }else {
            memoScheduleHistory.setVersionNo(scheduleHistory.getVersionNo()+1);//版本号递增
            memoScheduleHistory.setPreviousId(scheduleHistory.getId());//前一次记录id
        }
        if (StringUtils.isNotEmpty(source)){
            memoScheduleHistory.setSource(source);
        }
        memoScheduleHistory.setSchedule(memoSchedule);
        memoScheduleHistoryDao.save(memoScheduleHistory);

        for (MemoScheduleImage memoScheduleImage:memoSchedule.getMemoScheduleImageHashSet()){
            MemoScheduleImageHistory memoScheduleImageHistory=new MemoScheduleImageHistory();
            BeanUtils.copyProperties(memoScheduleImage,memoScheduleImageHistory);
            memoScheduleImageHistory.setUpdateDate(new Date());
            memoScheduleImageHistory.setVersionNo(memoScheduleHistory.getVersionNo());//版本号递增
            memoScheduleImageHistory.setPreviousId(memoScheduleHistory.getPreviousId());//前一次记录id
            memoScheduleImageHistory.setScheduleId(memoSchedule.getId());// 日程id
            memoScheduleImageHistory.setScheduleHistory(memoScheduleHistory);
            memoScheduleImageHistoryDao.save(memoScheduleImageHistory);


            uploadService.addFileUsing(new MemoUsing(memoScheduleImageHistory.getId(), memoScheduleImageHistory.getClass()), memoScheduleImageHistory.getNormal(),
                    memoSchedule.getTitle() + "(进历史)", user, "备忘与日程"); // 添加历史表的附件引用

            if (delFileUsing) {
                uploadService.delFileUsing(new MemoUsing(memoScheduleImage.getId(), memoScheduleImage.getClass()), memoScheduleImage.getNormal(), user); // 删除主表的附件 引用
            }

        }
        return memoScheduleHistory;
    }

    @Override
    public MemoScheduleHistory getMemoScheduleHistoryById(Integer id) {
        MemoScheduleHistory memoScheduleHistory= memoScheduleHistoryDao.get(id);
        return memoScheduleHistory;
    }

    @Override
    public MemoScheduleImageHistory getMemoScheduleImageHistoryById(Integer id) {
        return memoScheduleImageHistoryDao.get(id);
    }

    @Override
    public List<MemoScheduleImageHistory> getMemoScheduleImageHistories(Integer memoScheduleHistoryId) {
        String hql=" from MemoScheduleImageHistory where scheduleHistoryId =:memoScheduleHistoryId";
        Map<String,Object> params=new HashMap<>();
        params.put("memoScheduleHistoryId",memoScheduleHistoryId);
        List<MemoScheduleImageHistory> memoScheduleImageHistories= memoScheduleImageHistoryDao.getListByHQLWithNamedParams(hql,params);
        return memoScheduleImageHistories;
    }
}
