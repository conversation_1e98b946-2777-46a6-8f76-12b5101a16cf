package cn.sphd.miners.modules.accountant.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.accountant.entity.*;
import cn.sphd.miners.modules.accountant.service.*;
import cn.sphd.miners.modules.car.util.Result;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2022-04-18 10:39
 * @description
 **/
@Controller
@RequestMapping("/accountant")
public class Accountant_1_209_Contrtoller {

    @Autowired
    CommodityRelevanceService commodityRelevanceService;
    @Autowired
    TaccountantEstablishService establishService;
    @Autowired
    SubjectJournalService subjectJournalService;
    @Autowired
    Accountant1_209_Service accountant1_209_service;

    private static  final int SUCCESS = 1;
    private static  final int ERROR = 0;

    /*
     * 获取采购关联记录
     * */
    @RequestMapping("/getRelationRecords.do")
    @ResponseBody
    public JsonResult getRelationRecords(User user) {
        List<TaccountantSettingHistoryEntity> list = commodityRelevanceService.getRelationRecords(user);
        JsonResult result = new JsonResult();
        result.setSuccess(SUCCESS);
        result.setData(list);
        return result;
    }

    /*
     * 获取建账记录
     * */
    @RequestMapping("/getEstablishRecords.do")
    @ResponseBody
    public JsonResult getEstablishRecords(User user) {
        List<TaccountantEstablishEntity> list = establishService.getEstablishRecords(user);
        JsonResult result = new JsonResult();
        result.setSuccess(SUCCESS);
        result.setData(list);
        return result;
    }

    /*
     * 获取科目变动记录
     * establish    建账ID    选填
     * */
    @RequestMapping("/getSubjectJournal.do")
    @ResponseBody
    public JsonResult getSubjectJournal(User user, Integer establish,PageInfo pageInfo) {
//        query.put("pageInfo", pageInfo);
        List<TaccountantSubjectJournal> list = accountant1_209_service.getSubjectJournal(user,establish,pageInfo);
        HashMap<String, Object> map = new HashMap<>();
        map.put("pageInfo", pageInfo);
        map.put("list", list);
        JsonResult result = new JsonResult();
        result.setSuccess(SUCCESS);
        result.setData(map);
        return result;
    }

    /*
     * 获取科目变动内容
     * 科目的操作记录
     * establish    建账ID    选填
     * createDate   变动日期   必填
     * */
    @RequestMapping("/getSubjectRecords.do")
    @ResponseBody
    public JsonResult getSubjectRecords(User user, TaccountantSubjectRecord subjectRecord) {
        List<TaccountantSubjectRecord> list = accountant1_209_service.getSubjectRecords(user,subjectRecord);
        JsonResult result = new JsonResult();
        result.setSuccess(SUCCESS);
        result.setData(list);
        return result;
    }


    /*
     * 获取科目修改前的数据
     * id            查看修改后时传
     * previousId    修改前时数据ID
     * */
    @RequestMapping("/getSubjectModifyInfo.do")
    @ResponseBody
    public JsonResult getSubjectModifyInfo(User user, TaccountantSubjectRecordDto subjectRecordDto) {
        subjectRecordDto.setOrg(user.getOid());
        TaccountantSubjectRecordDto record = accountant1_209_service.getSubjectModifyInfo(subjectRecordDto);
        JsonResult result = new JsonResult();
        result.setSuccess(SUCCESS);
        result.setData(record);
        return result;
    }

    /*
     * 获取变动后的科目或建账后的科目
     * isChanged            必填，0-建账后，1-变动后
     * changeDate           当上一个参数=1时必填，否则不填，修改前时数据ID
     * establish            建账ID
     * category             科目类别
     * */
    @RequestMapping("/getChangeSubjects.do")
    @ResponseBody
    public JsonResult getChangeSubjects(User user, TaccountantSubjectChange subjectChange) {
        subjectChange.setOrg(user.getOid());
        List<TaccountantSubjectChange> list = accountant1_209_service.getChangeSubjects(subjectChange);
        JsonResult result = new JsonResult();
        result.setSuccess(SUCCESS);
        result.setData(list);
        return result;
    }

    /*
     * 获取新凭证管理首页数据
     * */
    @RequestMapping("/getVoucherHomeData.do")
    @ResponseBody
    public JsonResult getVoucherHomeData(User user) {
        VoucherHomeData voucherHomeData = accountant1_209_service.getVoucherHomeData(user);
        JsonResult result = new JsonResult();
        result.setSuccess(SUCCESS);
        result.setData(voucherHomeData);
        return result;
    }

    /*
     * 获取指定月份不同类型的凭证
     * month            选填，需结账的月份 yyyy-MM。不填为查询所有
     * type             选填，0-手动录入的凭证，1-会计录入的凭证，2-系统自动选择的凭证。不填为查询所有
     * */
    @RequestMapping("/getVoucherByMonth.do")
    @ResponseBody
    public JsonResult getVoucherByMonth(User user,String month,String type) {
        List<TAccountantVoucherDto> list = accountant1_209_service.getVoucherByMonth(user,month,type);
        JsonResult result = new JsonResult();
        result.setSuccess(SUCCESS);
        result.setData(list);
        return result;
    }

    /*
     * 获取结账管理首页数据
     * */
    @RequestMapping("/getSettleHomeData.do")
    @ResponseBody
    public JsonResult getSettleHomeData(User user) {
        SettleHomeData settleHomeData = accountant1_209_service.getSettleHomeData(user);
        JsonResult result = new JsonResult();
        result.setSuccess(SUCCESS);
        result.setData(settleHomeData);
        return result;
    }

    /*
     * 获取某年度的结账记录
     * year     查询年份，不填时默认查询本年度的结账记录
     * */
    @RequestMapping("/getSettleListByYear.do")
    @ResponseBody
    public JsonResult getSettleListByYear(User user,Integer year) {
        List<TAccountantSettle> settleList = accountant1_209_service.getSettleListByYear(user,year);
        JsonResult result = new JsonResult();
        result.setSuccess(SUCCESS);
        result.setData(settleList);
        return result;
    }

    /*
     * 查询凭证，请输入凭证金额、经手人或摘要内可能包含的内容
     * settleMonth  凭证所属月份
     * type         0-手动录入的凭证，1-会计录入的凭证，2-系统自动选择的凭证
     * con          查询内容，可能是凭证金额或经手人或摘要
     * */
    @RequestMapping("/searchVoucher.do")
    @ResponseBody
    public JsonResult searchVoucher(User user, String settleMonth,String type,String con) {
        List<TAccountantVoucherDto> voucherList = accountant1_209_service.searchVoucher(user,settleMonth,type,con);
        JsonResult result = new JsonResult();
        result.setSuccess(SUCCESS);
        result.setData(voucherList);
        return result;
    }

    /*
     * 跳转到建账管理页面
     * */
    @RequestMapping("/buildMange.do")
    public String buildMange(User user) {
        return "/accountant/accountSetManage";
    }

    //TAccountantSetting settingList = subjectSelectMapper.checkOrgSettingData(org);

    @Autowired
    SubjectSettingService subjectSettingService;

    /*
    * 返回1.307初始化中 会计页面需要的信息
    * 主要是操作者和操作时间
    * */
    @RequestMapping("/initKJinfo.do")
    @ResponseBody
    public JsonResult initKJinfo(User user) {
        TAccountantSetting res = subjectSettingService.initKJinfo(user);
        JsonResult result = new JsonResult();
        result.setSuccess(SUCCESS);
        result.setData(res);
        return result;
    }

    /*
     * test
     * */
    @RequestMapping("/test.do")
    @ResponseBody
    public JsonResult test(User user) {
        JsonResult res = accountant1_209_service.test(user);
        return res;
//        Integer org = user.getOid();
//        List list = subjectSettingService.getEquipmentToolsSubjectNames(user);
//        JsonResult result = new JsonResult();
//        result.setSuccess(SUCCESS);
//        result.setData(list);
//        return result;
    }

}
