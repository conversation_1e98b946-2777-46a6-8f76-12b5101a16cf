package cn.sphd.miners.modules.processManagement.dto;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 查看设置详情中工序以及工序下的子工序或操作
 * <AUTHOR>
 * @Date 20233/5/23
 */
public class ProcessesDto implements Serializable {

    private Long productProcessId; //产品工序id
    private Integer product;   //产品ID
    private Integer org;   //机构ID
    private Long process;   //工序ID
    private String code;   //工序代号
    private String name;   //工序名称
    private Integer orders;   //同级排序
    private Boolean isLeaf;   //是否有子工序或操作
    private Byte timing;     //时间点:1-准备工作,2-正式操作,3-收尾工作
    private Byte kind;  //操作类型:1-主要,2-辅助
    private List<SonProcessesDto> sonProcesses;  //子工序列表
    private List<SonProcessesDto> operateList;  //工序下的操作列表

    public Long getProductProcessId() {
        return productProcessId;
    }

    public void setProductProcessId(Long productProcessId) {
        this.productProcessId = productProcessId;
    }

    public Integer getProduct() {
        return product;
    }

    public void setProduct(Integer product) {
        this.product = product;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Long getProcess() {
        return process;
    }

    public void setProcess(Long process) {
        this.process = process;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public List<SonProcessesDto> getSonProcesses() {
        return sonProcesses;
    }

    public void setSonProcesses(List<SonProcessesDto> sonProcesses) {
        this.sonProcesses = sonProcesses;
    }

    public List<SonProcessesDto> getOperateList() {
        return operateList;
    }

    public void setOperateList(List<SonProcessesDto> operateList) {
        this.operateList = operateList;
    }

    public Boolean getIsLeaf() {
        return isLeaf;
    }

    public void setIsLeaf(Boolean isLeaf) {
        this.isLeaf = isLeaf;
    }

    public Byte getTiming() {
        return timing;
    }

    public void setTiming(Byte timing) {
        this.timing = timing;
    }

    public Byte getKind() {
        return kind;
    }

    public void setKind(Byte kind) {
        this.kind = kind;
    }

    public ProcessesDto(){}

    public ProcessesDto(Long productProcessId,Integer product,Integer org,Long process,String code,String name,Integer orders){
        this.productProcessId=productProcessId;
        this.product=product;
        this.org=org;
        this.process=process;
        this.code=code;
        this.name=name;
        this.orders=orders;
    }

    public ProcessesDto(Long productProcessId, Integer product, Integer org, Long process, String code, String name, Integer orders, Boolean isLeaf) {
        this.productProcessId = productProcessId;
        this.product = product;
        this.org = org;
        this.process = process;
        this.code = code;
        this.name = name;
        this.orders = orders;
        this.isLeaf = isLeaf;
    }

    public ProcessesDto(Long productProcessId, Integer product, Integer org, Long process, String code, String name, Integer orders, Boolean isLeaf, Byte timing, Byte kind) {
        this.productProcessId = productProcessId;
        this.product = product;
        this.org = org;
        this.process = process;
        this.code = code;
        this.name = name;
        this.orders = orders;
        this.isLeaf = isLeaf;
        this.timing = timing;
        this.kind = kind;
    }
}
