package cn.sphd.miners.modules.accountantReportTax.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctReportEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxHistoryEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxPeriodEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxReportEntity;

import java.io.Serializable;
import java.util.List;

public interface AcctTaxReportDao extends IBaseDao<AcctTaxReportEntity,Serializable> {
    List<AcctTaxHistoryEntity> getTaxByReportId(Integer id,int year,int month);//根据历史报表ID获取历史税种列表

    AcctTaxReportEntity getByTaxAndReport(int taxId, int reportId);

    List<AcctTaxReportEntity> getTaxReportByTaxId(Integer oid, Integer id);

}
