package cn.sphd.miners.modules.material.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.material.entity.SrmSupplierContact;
import cn.sphd.miners.modules.material.entity.SrmSupplierContactHistory;

import java.io.Serializable;

public interface SrmSupplierContactHistoryDao extends IBaseDao<SrmSupplierContactHistory, Serializable> {

    int insertAndResultId(SrmSupplierContact srmSupplierContact);
}
