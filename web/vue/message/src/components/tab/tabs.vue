<template>
  <div class="tabs">
    <div class="tabs-bar">
      <div
        v-bind:key="index"
        :class="tabCls(item)"
        v-for="(item, index) in navList"
        @click="handleChange( index )">{{ item.label }}
        <span class="tabCount" v-show="item.msgCount !== 0">{{ item.msgCount }}</span>
      </div>
    </div>
    <div class="tabs-content">
      <slot></slot>
    </div>
  </div>
</template>

<style lang="less">
  @green-blue   : #36c6d3;
  [v-cloak] {
    display: none;
  }
  .tabs {
    font-size: 14px;
    color:#657180;
    width:100%;
    overflow:hidden;
    z-index:10;
    height:100%;
    overflow: hidden;
    /*不要加height：100%*/
  }
  .tabs-bar{
    width: 100%;
    overflow: hidden;
    background-color: @green-blue;
    &:after{
      content:'';
      display: block;
      width: 100%;
      height: 1px;
      background: #d7dde4;
      margin-top:-1px;
    }
  }
  .tabs-tab {
    float: left;
    display: block;
    width: 50%;
    height: 42px;
    line-height: 42px;
    cursor: pointer;
    position: relative;
    text-align: center;
    color: lighten(@green-blue,30%);
  }
  .tabs-tab-active {
    color: #fff;
    background-color: darken(@green-blue,5%);
  }
  .tabs-tab-active:after{
    content: '';
    position: absolute;
    display: block;
    border: 6px solid transparent;
    border-bottom-color: transparent;
    border-bottom-color: #eee;
    bottom: 0;
    left: calc(50% - 6px);
  }
  .tabs-content{
    height: calc(100% - 90px);
    overflow: auto;
  }
</style>

<script>
export default {
  name: 'tabs',
  props: {
    value: {
      type: [String, Number]
    }
  },
  data: function () {
    return {
      currentValue: this.value,
      navList: []
    }
  },
  methods: {
    tabCls: function (item) {
      return [
        'tabs-tab',
        {
          'tabs-tab-active': Number(item.name) === Number(this.currentValue)
        }
      ]
    },
    getTabs: function () {
      return this.$children.filter(function (item) {
        return item.$options.name === 'tab-pane'
      })
    },
    updateNav: function () {
      this.navList = []
      let _this = this
      this.getTabs().forEach(function (pane, index) {
        _this.navList.push({
          label: pane.label,
          name: pane.name || index,
          closable: pane.closable,
          isTab: pane.isTab,
          msgCount: pane.msgCount
        })
        let curVal = pane.name
        if (!pane.name) {
          curVal = index
        }
        if (index === 0) {
          if (!_this.currentValue) {
            _this.currentValue = curVal || index
          }
        }
      })
      this.updateStatus()
    },
    updateStatus: function () {
      let tabs = this.getTabs()
      let _this = this
      tabs.forEach(function (tab) {
        let _tab = tab
        _tab.show = _this.currentValue.toString() === _tab.name
      })
    },
    handleChange: function (index) {
      let nav = this.navList[index]
      let name = nav.name
      if (nav.isTab === 'true') {
        this.currentValue = name
      }
      this.$emit('beforeSend', name)
      this.$emit('input', name)
    },
    deleteTab: function (index, event) {
      // 添加关闭功能，即是将navList中对应的元素删除即可。
      // 存在一个问题，当关闭了所有的tab后，tab-content中依然会显示内容
      // 那么，我们不能仅仅只删除navList中对应的元素，还应该将pane中对应的内容也隐藏
      // 可以通过改变currentValue的值来实现
      if (this.navList[index].name === this.currentValue) {
        if (index > 0) {
          this.currentValue = this.navList[index - 1].name
          this.navList.splice(index, 1)
          event.stopPropagation() // 阻止冒泡，避免触发handleChange(index)方法
        } else {
          this.navList.splice(index, 1)
          event.stopPropagation()
          if (this.navList.length > 0) {
            this.currentValue = this.navList[0].name
          } else {
            this.currentValue = ''
          }
        }
      } else {
        this.navList.splice(index, 1)
        event.stopPropagation() // 阻止冒泡，避免触发handleChange(index)方法
        if (this.navList.length === 0) {
          this.currentValue = ''
        }
      }
    },
    isShown: function (item) {
      let flag = item.closable === 'true'
      return flag
    }
  },
  watch: {
    currentValue: function () {
      this.updateStatus()
    },
    value: function () {
      this.updateStatus()
    }
  }
}
</script>
