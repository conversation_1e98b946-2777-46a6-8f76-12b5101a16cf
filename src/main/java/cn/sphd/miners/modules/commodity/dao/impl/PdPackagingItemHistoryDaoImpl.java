package cn.sphd.miners.modules.commodity.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.commodity.dao.PdPackagingItemHistoryDao;
import cn.sphd.miners.modules.commodity.entity.PdPackagingItem;
import cn.sphd.miners.modules.commodity.entity.PdPackagingItemHistory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class PdPackagingItemHistoryDaoImpl extends BaseDao<PdPackagingItemHistory, Serializable> implements PdPackagingItemHistoryDao {
    @Override
    public PdPackagingItemHistory insert(PdPackagingItem pdPackagingItem,Integer structureHistoryId,Integer mtBaseHistoryId) {
        PdPackagingItemHistory history=new PdPackagingItemHistory();

        BeanUtils.copyProperties(pdPackagingItem,history);
        history.setPackagingItem(pdPackagingItem.getId());
        history.setPackagingStruct(structureHistoryId);
        history.setMaterial(mtBaseHistoryId);
        history.setId(null);
        save(history);
        return history;
    }
}
