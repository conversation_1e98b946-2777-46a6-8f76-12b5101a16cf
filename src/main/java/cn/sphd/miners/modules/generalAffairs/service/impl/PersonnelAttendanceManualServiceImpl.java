package cn.sphd.miners.modules.generalAffairs.service.impl;

import cn.sphd.miners.common.service.BaseServiceImpl;
import cn.sphd.miners.modules.generalAffairs.dao.PersonnelAttendanceManualDao;
import cn.sphd.miners.modules.generalAffairs.entity.PersonnelAttendanceManual;
import cn.sphd.miners.modules.generalAffairs.entity.PersonnelAttendanceUser;
import cn.sphd.miners.modules.generalAffairs.service.AttendanceEventSerivce;
import cn.sphd.miners.modules.generalAffairs.service.PersonnelAttendanceManualService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * PersonnelAttendanceManualServiceImpl_in_generalAffairs
 *
 * @author: wuyu
 * @since: 4.0
 * @date: 2025-05-12 13:18:27
 **/
@Service("personnelAttendanceManualService")
public class PersonnelAttendanceManualServiceImpl extends BaseServiceImpl implements PersonnelAttendanceManualService, AttendanceEventSerivce {
    @Autowired
    PersonnelAttendanceManualDao personnelAttendanceManualDao;

    @Override
    public PersonnelAttendanceManual getOrNewByPau(PersonnelAttendanceUser pau) {
        PersonnelAttendanceManual pam = getByPau(pau);
        if (pam == null) {
            pam = new PersonnelAttendanceManual();
            pam.setPauId(pau.getId());
            pam.setDate(pau.getAttendanceDate());
        }
        return pam;
    }

    @Override
    public PersonnelAttendanceManual getByPau(PersonnelAttendanceUser pau) {
        Map<String, Object> params = new HashMap<>(1){{
            put("pauId", pau.getId());
        }};
        PersonnelAttendanceManual pam = (PersonnelAttendanceManual) personnelAttendanceManualDao.getByHQLWithNamedParams("from PersonnelAttendanceManual where pauId=:pauId", params);
        return pam;
    }

    @Override
    public void saveOrUpdate(PersonnelAttendanceManual pam) {
        personnelAttendanceManualDao.saveOrUpdate(pam);
    }
}