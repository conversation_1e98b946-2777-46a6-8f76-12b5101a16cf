<template>
  <div id="overtimeAssign">
    <TY_NavTop title="被指派的加班" isSearch="true" @toggleList="search"></TY_NavTop>
    <div class="container">
      <div class="nowTime">以下为您被指派的加班，请确认。</div>
      <div>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in toApprove" v-bind:key="index">
            <div class="ui-cell__hd">{{item.createName}}</div>
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.beginTime |formatDay('YYYY年MM月DD日 dddd')}}</div>
                <div>加班时长  {{item.duration}}h <div style="float:right">{{item.assignerName}} {{item.assigneTime|formatDay}}</div></div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'overtimeAssign',
  data () {
    return {
      toApprove: [],
      listenersUid: []
    }
  },
  created () {
    let that = this
    this.axios.post('../../../leaveAndOutTime/assignOvertimeList.do')
      .then(function (response) {
        console.log(response)
        let getData = response.data.data
        that.toApprove = getData.personnelOvertimes
      })
      .catch(function (error) {
        console.log(error)
      })
  },
  methods: {
    jump: function (id) {
      this.$router.push({
        path: `/overtimeAssignDetail/${id}/approve`
      })
    },
    search: function () {
      this.$router.push({
        path: `/overtimeAssignQuery`
      })
    }
  }
}
</script>
