package cn.sphd.miners.modules.orderReview.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.orderReview.entity.TSlOrdersItemReview;
import cn.sphd.miners.modules.orderReview.entity.TSlOrdersReview;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;
import java.util.Map;

/**
 * Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
public interface ITSlOrdersReviewService 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    public TSlOrdersReview selectTSlOrdersReviewById(Long id);

    /**
     * 查询列表
     * 
     * @param tSlOrdersReview 
     * @return 集合
     */
    public List<TSlOrdersReview> selectTSlOrdersReviewList(TSlOrdersReview tSlOrdersReview);

    /**
     * 新增
     * 
     * @param tSlOrdersReview 
     * @return 结果
     */
    public int insertTSlOrdersReview(TSlOrdersReview tSlOrdersReview);

    /**
     * 修改
     * 
     * @param tSlOrdersReview 
     * @return 结果
     */
    public int updateTSlOrdersReview(TSlOrdersReview tSlOrdersReview);

    /**
     * 批量删除
     * 
     * @param ids 需要删除的主键集合
     * @return 结果
     */
    public int deleteTSlOrdersReviewByIds(Long[] ids);

    /**
     * 删除信息
     * 
     * @param id 主键
     * @return 结果
     */
    public int deleteTSlOrdersReviewById(Long id);

    List<Map<String, Object>> selectTSlOrdersReviewListByCondition(TSlOrdersItemReview ordersItemReview, PageInfo pageInfo);

    TSlOrdersReview selectTSlOrdersReviewByOrderId(Long orders);
}
