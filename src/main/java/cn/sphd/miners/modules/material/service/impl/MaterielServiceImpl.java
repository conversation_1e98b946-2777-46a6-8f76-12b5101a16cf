package cn.sphd.miners.modules.material.service.impl;

import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.accountant.service.SubjectSelectService;
import cn.sphd.miners.modules.accountant.service.SubjectSettingService;
import cn.sphd.miners.modules.commodity.dao.PdBaseDao;
import cn.sphd.miners.modules.commodity.entity.PdBase;
import cn.sphd.miners.modules.commodity.service.ProductService;
import cn.sphd.miners.modules.material.dao.*;
import cn.sphd.miners.modules.material.entity.*;
import cn.sphd.miners.modules.material.service.MaterielService;
import cn.sphd.miners.modules.material.service.MtService;
import cn.sphd.miners.modules.material.utils.SubjectUtils;
import cn.sphd.miners.modules.personal.entity.UserMessage;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.personal.service.UserMessageService;
import cn.sphd.miners.modules.sales.dao.PdCustomerDao;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.CriteriaSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by Administrator on 2016/9/20.
 */
@Service
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class MaterielServiceImpl implements MaterielService {
    @Autowired
    MtCategoryDao mtCategoryDao;
    @Autowired
    MtBaseDao mtBaseDao;
    @Autowired
    MtSupplierMaterialDao mtSupplierMaterialDao;
    @Autowired
    SrmSupplierDao supplierDao;
    @Autowired
    MtStockInfoDao mtStockInfoDao;
    @Autowired
    MtQualityDao mtQualityDao;
    @Autowired
    SubjectSettingService settingService;
    @Autowired
    MtStockInfoHistoryDao mtStockInfoHistoryDao;
    @Autowired
    PdCustomerDao customerDao;

    @Autowired
    MtService mtService;

    @Autowired
    SrmSupplierHistoryDao smrHistoryDao;
    @Autowired
    PdBaseDao pdBaseDao;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    MtSupplierMaterialHistoryDao mtSupplierMaterialHistoryDao;
    @Autowired
    UserService userService;

    @Override
    public MtBase getMtBaseById(Integer id) {
        return mtBaseDao.get(id);
    }

    @Override
    public void updateMtBase(MtBase mtBase) {
        mtBaseDao.update(mtBase);
    }

    @Override
    public void saveMtBase(MtBase mtBase) {
        mtBaseDao.save(mtBase);
    }

    @Override
    public void deleteMtBase(MtBase mtBase) {
        mtBaseDao.delete(mtBase);
    }

    @Override
    public void saveMtStockInfo(MtStockInfo mtStockInfo) {
        mtStockInfoDao.save(mtStockInfo);
    }

    @Override
    public void updateMtStockInfo(MtStockInfo mtStockInfo) {
        mtStockInfoDao.update(mtStockInfo);
    }

    @Override
    public void updateMtQuality(MtQuality mtQuality) {
        mtQualityDao.update(mtQuality);
    }

    @Override
    public void saveMtQuality(MtQuality mtQuality) {
        mtQualityDao.save(mtQuality);
    }

    @Override
    public void updateMtCategory(MtCategory mtCategory) {
        mtCategoryDao.update(mtCategory);
    }

    @Override
    public List<MtCategory> getMtCategoryByOidAndPid(Integer oid, Integer pid) {
        String hql = " and o.org=" + oid;
        if (pid == null) {
            hql += " and o.level=1";
        } else {
            hql += " and o.parent=" + pid;
        }
        return mtCategoryDao.findCollectionByConditionNoPage(hql, null, null);
    }

    @Override
    public List<Map<String, Object>> getMtCategoryByOidAndName(Integer oid, String param) {
        String hql = "select  new map(COUNT(p.id) as num,0 as id ,'待分类' as name,'待分类' as fullName, 0 as isSuspend)  from PdMerchandise p where p.category=0 and p.enabled='1' and p.org=" + oid;


        if (param != null && !"".equals(param)) {
            hql = hql + " and (p.outerSn like '%" + param + "%' or p.outerName like '%" + param + "%')\n";
        }


        return mtCategoryDao.getListByHQLWithNamedParams(hql, null);
    }

    @Override
    public List<Map<String, Object>> getMtCategoryByOidAndNameForChengPin(Integer oid, String name, Integer userId) {
        String hql = "select  new map(COUNT( DISTINCT p.id) as num,0 as id ,'待分类' as name,'待分类' as fullName)  from PdMerchandise p left join PdLocation l on l.product = p.id where l.id is not null and p.category=0 and p.enabled='1' and p.org=" + oid;

        List<Map<String, Object>> list1 = mtCategoryDao.getListByHQLWithNamedParams(hql, null);


        String sql = "SELECT\n" +
                "	sc.id,\n" +
                "	sc.`name`,\n" +
                "	(select count(p.id) from t_pd_merchandise p left join t_pd_location l ON p.id = l.product where l.id is not null and p.customer=sc.id ) num,'zs' as type \n" +
                "FROM\n" +
                "	t_srm_customer sc\n" +
                "WHERE\n" +
                "	sc.principal =" + userId + " and sc.type='1'";
        List<Map<String, Object>> list2 = customerDao.getSession().createSQLQuery(sql).setResultTransformer(CriteriaSpecification.ALIAS_TO_ENTITY_MAP).list();

        List<Map<String, Object>> l = new ArrayList<>();
        l.add(list1.get(0));

        for (int i = 0; i < list2.size(); i++) {
            l.add(list2.get(i));
        }

        for (int i = 1; i < list1.size(); i++) {
            l.add(list1.get(i));
        }
        return l;
    }

    @Override
    public List<MtBase> getMtBaseByCategoryId(Integer categoryId) {
        String hql = " and o.category=" + categoryId;
        return mtBaseDao.findCollectionByConditionNoPage(hql, null, null);
    }

    @Override
    public List<MtCategory> getMtCategoriesById(Integer id) {
        String hql = " and o.parent=" + id;
        List<MtCategory> mtCategories = mtCategoryDao.findCollectionByConditionNoPage(hql, null, null);//得到当前类别
        List<MtCategory> ms = new ArrayList<MtCategory>();//创建结果集
        List<MtCategory> mtCategoryList = this.getMtCategories(mtCategories, ms);
        mtCategories.addAll(mtCategoryList);
        return mtCategories;
    }

    private List<MtCategory> getMtCategories(List<MtCategory> mtCategories, List<MtCategory> ms) {
        List<MtCategory> mtCategoryList = new ArrayList<MtCategory>();
        for (MtCategory m : mtCategories) {
            String hql = " and o.parent=" + m.getId();
            List<MtCategory> mts = new ArrayList<MtCategory>();
            mts = mtCategoryDao.findCollectionByConditionNoPage(hql, null, null);//得到当前类别
            if (mts.size() > 0) {
                mtCategoryList.addAll(mts);
            }
        }
        ms.addAll(mtCategoryList);
        if (mtCategoryList.size() > 0) {//若得到的子级数量大于0，继续往下遍历,否则结束
            getMtCategories(mtCategoryList, ms);
        }
        return ms;
    }

    @Override
    public MtCategory getMtCategoryById(Integer id) {

        return mtCategoryDao.get(id);
    }

    @Override
    public void deleteCategory(MtCategory mtCategory) {
        mtCategoryDao.delete(mtCategory);
    }

    @Override
    public void saveMtCategory(MtCategory mtCategory) {
        mtCategoryDao.save(mtCategory);
    }

    @Override
    public void saveMtSupplierMaterial(MtSupplierMaterial mtSupplierMaterial) {

        mtSupplierMaterialDao.saveOrUpdate(mtSupplierMaterial);
    }

    @Override
    public void saveSrmSupplier(SrmSupplier mtSupplier) {
        supplierDao.save(mtSupplier);
    }

    @Override
    public void updateSrmSupplier(SrmSupplier mtSupplier) {
        supplierDao.update(mtSupplier);
    }

    @Override
    public void updateMtSupplierMaterial(MtSupplierMaterial mtSupplierMaterial) {
        mtSupplierMaterialDao.update(mtSupplierMaterial);
    }

    @Override
    public SrmSupplier getSrmSupplierById(Integer id) {
        return supplierDao.get(id);
    }

    @Override
    public MtSupplierMaterial getMtSupplierMaterialById(Integer mid) {
        return mtSupplierMaterialDao.getByHQL(" from MtSupplierMaterial o where o.id=" + mid);
    }

    @Override
    public void deleteMtSupplierMaterial(MtSupplierMaterial mtSupplierMaterial) {
        mtSupplierMaterialDao.delete(mtSupplierMaterial);
    }

    @Override
    public List<SrmSupplier> getSrmSupplierByName(String name, Integer oid, Integer enabled, Integer supplierId) {
        String hql = "select distinct o from SrmSupplier o where 1=1";

        if (StringUtils.isNotBlank(name)) {
            hql += " and o.name like '%" + name + "%'";
        }

        if (enabled != null) {
            hql += " and o.enabled=" + enabled;
        }
        if (supplierId != null) {
            hql += " and o.id =" + supplierId;
        }
        hql += " and o.org = " + oid;
        hql += " and ifnull(o.type,1)!=2";
        return supplierDao.getListByHQL(hql);
    }

    @Override
    public Map getSrmSupplierByNameAndOid(String name, Integer oid, Integer state) {
        String sql = "select o.id,o.name,o.full_name,o.code_name,o.contact,o.mobile,o.telephone,o.fax,o.email,o.address,o.payment_type,o.payment_method,o.bank_code,o.bank_no from t_srm_supplier o left join t_mt_supplier_material msm on msm.supplier = o.id left join t_mt_stock_info msi on msi.material = msm.material left join t_mt_base mb on msm.material = mb.id where 1=1";
        if (name == null || "".equals(name)) {
            sql += " and o.org = " + oid;
        } else {
            sql += " and o.name like '%" + name + "%' and o.org = " + oid;
        }
        if (state != null) {//
            sql += " and (select count(*) from t_mt_supplier_material where supplier = o.id and msm.enabled=1) !=0 and ((ifnull(msi.current_stock,-1)!=-1 and ifnull(msi.initial_stock,-1)!=-1) or ifnull(mb.is_purchased,-1)=-1)";
        }

        sql += " group by o.id";
        return supplierDao.findMapByConditionNoPage(sql, new Object[]{});
    }


    @Override
    public void deleteSrmSupplier(SrmSupplier srmSupplier) {
        supplierDao.delete(srmSupplier);
    }

    @Override
    public MtCategory getMtCategoryByOidCName(Integer oid, String categoryName) {
        String hql = "from MtCategory where org=" + oid + " and name='" + categoryName + "'";
        MtCategory mtCategory = mtCategoryDao.getByHQL(hql);
        return mtCategory;
    }

    @Override
    public List<MtBase> getMtBaseByOidAndCode(Integer oid, String code) {
        String hql = " and o.category.org=" + oid;
        if (StringUtils.isNotBlank(code)) {
            hql += " and o.code like '%" + code + "%'";
        }
        return mtBaseDao.findCollectionByConditionNoPage(hql, null, null);
    }

    @Override
    public List<MtBase> getMtBaseListByCode(Integer oid, String code) {
        String hql = " and o.category.org=" + oid + " and o.code='" + code + "'";
        return mtBaseDao.findCollectionByConditionNoPage(hql, null, null);
    }

    @Override
    public MtBase getMtBaseByProduct(Integer product) {
        return mtBaseDao.getByHQL(" from MtBase o where o.product_ = ?0", product);
    }

    @Override
    public List<SrmSupplier> getSrmSupplierByOid(Integer oid) {
        return supplierDao.getListByHQL(" from SrmSupplier o where o.oid = ?0", oid);
    }


    @Override
    public List<MtSupplierMaterial> getMtSupplierMaterialByNameAndCodeName(Integer oid, String codeName, String name) {

        return mtSupplierMaterialDao.getMtSupplierMaterialByNameAndCodeName(oid, codeName, name);
    }

    @Override
    public List<MtSupplierMaterial> getListBySupplierId(Integer supplierId) {
        return mtSupplierMaterialDao.getListByHQL(" from MtSupplierMaterial o where o.supplier = ?0", supplierId);
    }

    @Override
    public MtSupplierMaterial getMtSupplierMaterialByMIdANDSID(int mid, int sid) {
        return mtSupplierMaterialDao.getMtSupplierMaterialByMIdANDSID(mid, sid);
    }

    @Override
    public List<MtBase> getMtBaseByProductId(Integer id) {
        return mtBaseDao.getListByHQL(" from MtBase o where o.product = '" + id + "'");
    }

    @Override
    public Map getAllPdBaseAndMtBase(Integer oid, User user, String time, SubjectSettingService settingService, UserService userService, UserMessageService messageService) {
        String mtSql = "SELECT DISTINCT\n" +
                "	pd.`name` AS product_name,\n" +
                "	mb.`name`,\n" +
                "	mb. code,\n" +
                "	mb.id,\n" +
                "	ms.id AS supplier_id,\n" +
                "	pcp.inner_sn,\n" +
                "	pcp.outer_sn,\n" +
                "	pcp.customer_name,\n" +
                "	pcp.id AS customer_product_id,\n" +
                "	sc.id AS sl_customer_id,\n" +
                "	ms.`name` AS supplier_name,\n" +
                "	mc. NAME AS categoty_name,\n" +
                "	mc.parent AS parent_category,\n" +
                "	mc.org,\n" +
                "	mc.first_grade_id,\n" +
                "	parent_mc.`name` AS parent_category_name\n" +
                "FROM\n" +
                "	t_mt_base mb\n" +
                "LEFT JOIN t_pd_base pd ON mb.product = pd.id\n" +
                "LEFT JOIN t_pd_merchandise pcp ON pcp.product = pd.id\n" +
                "LEFT JOIN t_sl_customer sc ON pcp.customer = sc.id\n" +
                "LEFT JOIN t_mt_category AS mc ON mb.category = mc.id\n" +
                "LEFT JOIN t_mt_category AS parent_mc ON parent_mc.id = mc.first_grade_id\n" +
                "LEFT JOIN t_mt_supplier_material msm ON msm.material = mb.id\n" +
                "LEFT JOIN t_srm_supplier ms ON msm.supplier = ms.id\n" +
                "WHERE\n" +
                "	mc.org = ?0";

        if (StringUtils.isNotBlank(time)) {
            mtSql += " and (mb.create_date between '" + time + "' and NOW() or pd.create_date between '" + time + "' and NOW())";
        }

        Map map = mtBaseDao.findMapByConditionNoPage(mtSql, new Object[]{oid});


        ArrayList<HashMap> mtAndPd = (ArrayList) map.get("data");
        String parent;
        String subjectName;
        String endSubjectName;
        JSONObject result;
        if (mtAndPd != null && mtAndPd.size() > 0) {
            for (HashMap o : mtAndPd) {
                if ("商品".equals((String) o.get("parent_category_name"))) {
                    parent = "1405";
                    subjectName = (String) o.get("customer_name");
                    endSubjectName = (String) o.get("product_name") + (String) o.get("outer_sn") + (String) o.get("customer_name");
                    //TODO 新增科目 (注意事项：已有相同供应商科目时，在已有供应商下添加)
                    Integer relevanceItem = (Integer) o.get("sl_customer_id");
                    changeIntoSubject(oid, user, subjectName, parent, endSubjectName, o, "1", relevanceItem);
                } else if ("构成商品的原辅材料".equals((String) o.get("parent_category_name")) || "外购成品".equals((String) o.get("parent_category_name"))) {
                    String code = (String) o.get("code");
                    String name = (String) o.get("name");
                    //外购成品特殊处理
                    if (StringUtils.isBlank(name)) {
                        name = (String) o.get("product_name");
                    }
                    if (StringUtils.isBlank(code)) {
                        code = (String) o.get("inner_sn");
                    }
                    parent = "1403";
                    subjectName = (String) o.get("supplier_name");
                    endSubjectName = name + code + (String) o.get("supplier_name");
                    //TODO 新增科目 (注意事项：已有相同供应商科目时，在已有供应商下添加)
                    Integer relevanceItem = (Integer) o.get("supplier_id");
                    changeIntoSubject(oid, user, subjectName, parent, endSubjectName, o, "2", relevanceItem);
                } else if ("办公用品".equals((String) o.get("parent_category_name"))) {
                    parent = "56020012";
                    subjectName = (String) o.get("supplier_name");
                    endSubjectName = (String) o.get("name") + (String) o.get("code") + (String) o.get("supplier_name");
                    //TODO 新增科目 (注意事项：已有相同供应商科目时，在已有供应商下添加)
                    Integer relevanceItem = (Integer) o.get("supplier_id");
                    changeIntoSubject(oid, user, subjectName, parent, endSubjectName, o, "2", relevanceItem);
                } else if ("商品的包装物".equals((String) o.get("parent_category_name"))) {
                    parent = "1412";
                    subjectName = (String) o.get("supplier_name");
                    endSubjectName = (String) o.get("name") + (String) o.get("code") + (String) o.get("supplier_name");
                    //TODO 新增科目 (注意事项：已有相同供应商科目时，在已有供应商下添加)
                    Integer relevanceItem = (Integer) o.get("supplier_id");
                    changeIntoSubject(oid, user, subjectName, parent, endSubjectName, o, "2", relevanceItem);
                } else {//发送消息
                    if (o.get("supplier_name") != null) {
//                        result = settingService.newAccountantSubject("", oid, String.valueOf(o.get("supplier_name")),
//                                null, user.getUserID(), user.getUserName(), "",
//                                "", "3", null);
//
//                        if ("".equals(result.optString("existSubject"))) {
//                            User accountant = userService.getUserByRoleCode(oid, "accounting");
                        User accountant = null;
                        accountant = userService.getUserByRoleCode(oid, "accounting");
                        if (accountant == null) {
                            accountant = userService.getUserByRoleCode(oid, "agentAccounting");
                        }
                        if (accountant != null) {
                            UserMessage userMessage = new UserMessage();
                            userMessage.setUser(user);
                            userMessage.setApprovalStatus(1);//审批状态：0-未申请 ，1-申请提交，2-通过审核，3-否决审核
                            userMessage.setHandleId(user.getUserID().toString());
                            userMessage.setEventType("科目设置");
                            userMessage.setIllustrate((String) o.get("name") + (String) o.get("code") + (String) o.get("supplier_name") + "的科目设置");//操作说明（申请事件）
                            userMessage.setMessageType("11");////消息类型  1-财务  2-加班  3-请假   4-报销  5-仓库修正 6-出库
                            userMessage.setState(1);//1-未处理  2-已处理
                            userMessage.setReceiveUserId(accountant.getUserID());  //接收消息人
                            userMessage.setCreateDate(new Date());
                            userMessage.setMtStockId((Integer) o.get("id"));
                            userMessage.setMessageId((Integer) o.get("supplier_id"));
                            messageService.addUserMassage(userMessage);
                        }
                    }
//                    }
                }
            }

        }
        return map;

    }

    @Override
    public List<MtBase> getMtBaseListByCodeAndName(Integer oid, String code, String name) {

        String hql = " and o.category.org=" + oid;

        if (StringUtils.isNotBlank(code) || StringUtils.isNotBlank(name)) {
            hql += " and (o.code='" + code + "'" + " or o.name='" + name + "')";
        }
        return mtBaseDao.findCollectionByConditionNoPage(hql, null, null);
    }

    @Override
    public Map getMtBaseListByCodeAndNameMap(Integer oid, Integer supplierId, String code, String name, Integer state) {

        String sql = "SELECT distinct msm.expiration_days as msm_expiration_days,o.id, o.name, o.code, o.model, o.specifications ,o.exp_required,o.open_duration,o.related_item,o.same_expiration,o.expiration_days, mu.name AS unit FROM t_mt_base o LEFT JOIN t_mt_category mc ON o.category = mc.id LEFT JOIN t_mt_supplier_material msm ON o.id = msm.material LEFT JOIN t_srm_supplier ms ON msm.supplier = ms.id LEFT JOIN t_po_orders_item poi ON poi.supplier_material = msm.id LEFT JOIN t_mt_unit mu ON o.unit_id = mu.id left join t_mt_stock_info msi on msi.material = o.id WHERE 1 = 1 AND mc.org = " + oid;

        if (StringUtils.isNotBlank(code) || StringUtils.isNotBlank(name)) {
            sql += " and (o.code='" + code + "'" + " or o.name='" + name + "')";
        }

        if (supplierId != null) {
            sql += " and ms.id=" + supplierId;
        }
        if (state != null) {
            sql += " and ((ifnull(msi.current_stock,-1)!=-1 and ifnull(msi.initial_stock,-1)!=-1) or ifnull(o.is_purchased,0)=0) and ifnull(o.enabled,0)!=0 and ifnull(msm.enabled,0)!=0";
        }
        return mtBaseDao.findMapByConditionNoPage(sql, new Object[]{});
    }

    @Override
    public void updateBase(HttpServletRequest request, HttpServletResponse response, Integer oid, User user, SubjectSelectService subjectSelectService, UserMessageService userMessageService, UserService userService, ProductService productService) throws IOException, ParseException {
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd");

        String base = request.getParameter("mt_info");//基本信息
        String kind = request.getParameter("save_mtKind");//物料所属的分类
        String kuInfo = request.getParameter("ku_info");//库存信息
        String qualityInfo = request.getParameter("quality_info");//质量信息
        String supplierList = request.getParameter("supplierList");//供应商名录
        String supplerDeleID = request.getParameter("supplerDeleID");//要删除的供货信息列表

        JSONObject jsonBase = JSONObject.fromObject(base);
        JSONObject jsonKuInfo = JSONObject.fromObject(kuInfo);
        JSONObject jsonQualityInfo = JSONObject.fromObject(qualityInfo);


        JSONArray sJson = JSONArray.fromObject(supplierList);
        List sList = JSONArray.toList(sJson);

        for (int i = 0; i < sList.size(); i++) {
            JSONObject jo = JSONObject.fromObject(sList.get(i));
            String codeName = jo.optString("codeName");
            String name = jo.optString("name");
            List<MtSupplierMaterial> ms = mtSupplierMaterialDao.getMtSupplierMaterialByNameAndCodeName(oid, codeName, null);
            if (ms != null && ms.size() > 0) {
                SrmSupplier supplier = getSrmSupplierById(ms.get(0).getSupplier());
                if (!name.equals(supplier.getName())) {
                    Map map = new HashMap();
                    map.put("status", "0");
                    map.put("message", "代号-" + codeName + "-重复，请重新输入");
                    ObjectToJson.objectToJson1(map, new String[]{"parent", "org", "mtBaseHashSet", "mtCategoryHashSet", "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet", "org", "parent", "material", "supplier", "category", "isIgnore"}, response);
                    return;
                }
            }
        }
        //老数据
        MtBase oldBase = mtBaseDao.get(jsonBase.optInt("id"));
        String name = oldBase.getName(), code = oldBase.getCode();

        List<SrmSupplier> srmSuppliers = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();

        //删除每个供货关系
        if (supplerDeleID != null) {
            JSONArray deleteSupplerInfo = JSONArray.fromObject(supplerDeleID);
            List di = JSONArray.toList(deleteSupplerInfo);
            if (di.size() > 0) {
                for (int i = 0; i < di.size(); i++) {
                    if (di.get(i) != null && !"".equals(di.get(i))) {
                        MtSupplierMaterial mtSupplierMaterial = mtSupplierMaterialDao.getMtSupplierMaterialByMIdANDSID(Integer.valueOf(jsonBase.getString("id")), Integer.valueOf(String.valueOf(di.get(i))));

                        if (mtSupplierMaterial != null) {
                            mtSupplierMaterialDao.delete(mtSupplierMaterial);
                        }
                    }

                }
            }
        }
        if (jsonBase.optInt("id") != 0 && jsonBase.optString("name") != null && !"".equals(jsonBase.optString("name")) && kind != null && !"".equals(kind)) {
            MtBase mtBase = mtBaseDao.get(jsonBase.optInt("id"));
            System.out.print(mtBase.getId());
            mtBase.setName(jsonBase.optString("name"));
            mtBase.setCode((String) jsonBase.get("code"));
            mtBase.setUnit((String) jsonBase.get("unit"));
            if (jsonBase.optString("netWeight") != null && !"".equals(jsonBase.optString("netWeight"))) {
                mtBase.setNetWeight(new BigDecimal(jsonBase.optString("netWeight")));
            }
            mtBase.setSpecifications((String) jsonBase.get("specifications"));
            mtBase.setMemo((String) jsonBase.get("memo"));
            mtBase.setModel((String) jsonBase.get("model"));
//            mtBase.setSource("2");
            mtBase.setCreateDate(new Date());
            MtCategory mtCategory = mtCategoryDao.get(Integer.parseInt(kind));
            mtBase.setCategory(mtCategory);
            mtBaseDao.update(mtBase);

            //添加库存历史信息
            MtStockInfo mSInfo = mtStockInfoDao.getByHQL(" from MtStockInfo m where m.material_=?0", mtBase.getId());


            for (MtStockInfo mtStockInfo : mtBase.getMtStockInfoHashSet()) {
                if (jsonKuInfo.optString("minimumStock") != null && !"".equals(jsonKuInfo.optString("minimumStock"))) {
                    mtStockInfo.setMinimumStock(new BigDecimal(jsonKuInfo.optString("minimumStock", "0")));
                }
                if (jsonKuInfo.optString("stock") != null && !"".equals(jsonKuInfo.optString("stock"))) {
                    mtStockInfo.setStock(new BigDecimal(jsonKuInfo.optString("stock", "0")));
                    mtStockInfo.setCurrentStock(new BigDecimal(jsonKuInfo.optString("stock", "")));
                    mtStockInfo.setAvailableStock(new BigDecimal(jsonKuInfo.optString("stock", "0")));
                }

                if (mtBase.getProduct_() != null) {
                    PdBase pdBase = pdBaseDao.get(mtBase.getProduct_());
                    if (mtBase.getProduct_() != null) {
                        if (pdBase.getCurrentStock() != null) {
                            if (pdBase.getCurrentStock().compareTo(new BigDecimal(jsonKuInfo.optString("stock", "0"))) < 0) {
                                map.put("status", 0);
                                map.put("message", "修改失败！已入库或供应商重复！");
                                ObjectToJson.objectToJson1(map, new String[]{"parent", "org", "mtBaseHashSet", "mtCategoryHashSet", "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet", "org", "parent", "material", "supplier", "category", "isIgnore"}, response);
                                return;
                            }
                        }
                    }
                    if (jsonKuInfo.has("minimumStock")) {
                        pdBase.setMinimumiStock(BigDecimal.valueOf(jsonKuInfo.optLong("minimumStock")));
                    }
                    pdBase.setCurrentStock(BigDecimal.valueOf(jsonKuInfo.optLong("stock")));
                    pdBase.setAvailableStock(BigDecimal.valueOf(jsonKuInfo.optLong("stock")));
                    pdBaseDao.update(pdBase);
                }


                mtStockInfo.setStockPosition(jsonKuInfo.optString("stockPosition"));
                mtStockInfo.setStockRequirements(jsonKuInfo.optString("stockRequirements"));
                mtStockInfoDao.update(mtStockInfo);  //更新库存信息

            }
            for (MtQuality mtQuality : mtBase.getMtQualityMtBaseViaId()) {
                mtQuality.setExperimentalMethod(jsonQualityInfo.optString("experimentalMethod"));
                mtQuality.setExpirationDate(jsonQualityInfo.optString("expirationDate"));
                mtQuality.setInspectionStandard(jsonQualityInfo.optString("inspectionStandard"));
                mtQuality.setInspectionOperation(jsonQualityInfo.optString("inspectionOperation"));
                mtQuality.setInspectionInstructions(jsonQualityInfo.optString("inspectionInstructions"));
                mtQuality.setProtocolSn(jsonQualityInfo.optString("protocolSn"));
                mtQuality.setOuterRecord(jsonQualityInfo.optString("outerRecord"));
                mtQuality.setSealedSamplesSn(jsonQualityInfo.optString("sealedSamplesSn"));
                mtQuality.setMemo(jsonQualityInfo.optString("memo"));
                mtQualityDao.update(mtQuality);// 更新质量信息
            }
            JSONArray j = JSONArray.fromObject(supplierList);
            List s = JSONArray.toList(j);

            for (int i = 0; i < s.size(); i++) {
                JSONObject jo = JSONObject.fromObject(j.get(i));


                //如果供货关系id（sbId）存在，执行编辑供货关系
                if (!"".equals(jo.optString("sbId")) && jo.optString("sbId") != null) {
                    MtSupplierMaterial sm = mtSupplierMaterialDao.getMtSupplierMaterialByMIdANDSID(jo.optInt("sbId"), jo.optInt("id"));

                    if (!sm.getMinimumStock().equals(jo.optLong("minimumStock")) || !sm.getInitialStock().equals(jo.optLong("initialStock"))) {
                        MtStockInfoHistory mtSH = new MtStockInfoHistory();
                        if (mSInfo != null) {
                            mtSH.setStockInfo(mSInfo.getId());
                        }
                        mtSH.setMaterial(mtBase.getId());
                        mtSH.setInitialStock(sm.getInitialStock());
                        mtSH.setMinimumStock(sm.getMinimumStock());
                        mtSH.setAfterInitialStock(new BigDecimal(jo.optString("initialStock", "0")));
                        mtSH.setAfterMinimumStock(new BigDecimal(jo.optString("minimumStock", "0")));
                        mtSH.setSupplier(sm.getSupplier());
                        mtSH.setCreator(user.getUserID());
                        mtSH.setCreateName(user.getUserName());
                        mtSH.setCreateDate(new Date());
                        mtStockInfoHistoryDao.save(mtSH);
                    }


                    if (!"".equals(jo.optString("minimumPurchase")) && jo.optString("minimumPurchase") != null) {
                        sm.setMinimumPurchase(jo.optInt("minimumPurchase"));
                    }
                    if (!"".equals(jo.optString("perchaseCycle")) && jo.optString("perchaseCycle") != null) {
                        sm.setPerchaseCycle(jo.optInt("perchaseCycle"));
                    }
                    if (!"".equals(jo.optString("unitPrice")) && jo.optString("unitPrice") != null) {
                        sm.setUnitPrice(new BigDecimal(jo.optString("unitPrice")));
                    }
                    if (!"".equals(jo.optString("taxRate")) && jo.optString("taxRate") != null) {
                        sm.setTaxRate(new BigDecimal(jo.optString("taxRate")));
                    }
//                    if (!"".equals(jo.optString("unitPriceNotax")) && jo.optString("unitPriceNotax") != null)
//                        sm.setUnitPriceNotax(BigDecimal.valueOf(jo.optString("unitPrice")));
                    if (!"".equals(jo.optString("contractSn")) && jo.optString("contractSn") != null) {
                        sm.setContractSn(jo.optString("contractSn"));
                    }

                    sm.setPriceStable(jo.optString("priceStable"));
                    sm.setCodeName(jo.optString("codeName"));
                    sm.setName(jo.optString("name"));
                    sm.setDraftAcceptable(jo.optInt("draftAcceptable"));
                    sm.setHasContact(jo.optString("hasContact"));
                    sm.setValidDate(jo.optString("validDate").equals("") ? null : sd.parse(jo.optString("validDate")));
                    sm.setSignDate(jo.optString("signDate").equals("") ? null : sd.parse(jo.optString("signDate")));
                    sm.setInvoicable(jo.optString("invoicable"));
                    String invoiceCategory = jo.optString("invoiceCategory");
                    if (StringUtils.isNotBlank(invoiceCategory)) {
                        sm.setInvoiceCategory(invoiceCategory);
                    }
                    sm.setAtPar(StringUtils.isBlank(jo.optString("atPar")) ? null : jo.optString("atPar"));
                    sm.setMinimumStock(new BigDecimal(jo.optString("minimumStock")));
                    sm.setInitialStock(new BigDecimal(jo.optString("initialStock")));
                    sm.setChargeBegin(StringUtils.isBlank(jo.optString("chargeBegin")) ? null : jo.optString("chargeBegin"));
                    sm.setChargeAcceptable(jo.optInt("chargeAcceptable"));
                    sm.setChargePeriod(jo.optInt("chargePeriod"));
                    sm.setIsInclude(jo.optString("isInclude", null));
                    sm.setMaterialInvoicable(jo.optInt("materialInvoicable"));
                    String materialInvoiceCategory = jo.optString("materialInvoiceCategory");
                    if (StringUtils.isNotBlank(materialInvoiceCategory)) {
                        sm.setMaterialInvoiceCategory(materialInvoiceCategory);
                    }
                    if (!jo.optString("materialTaxRate").equals("")) {
                        sm.setMaterialTaxRate(new BigDecimal(jo.optString("materialTaxRate")));
                    }
                    sm.setIsImprest(jo.optString("isImprest"));
                    sm.setInclusiveFreight(jo.optInt("inclusiveFreight"));
                    //包装方式
                    sm.setPackageMethod(StringUtils.isBlank(jo.optString("packageMethod")) ? null : jo.optString("packageMethod"));
                    sm.setIsParValue(jo.optString("isParValue", null));
                    SrmSupplier srmSupplier = supplierDao.get(jo.optInt("id"));

                    srmSupplier.setName(jo.optString("name"));
                    srmSupplier.setFullName(jo.optString("fullName"));

                    supplierDao.update(srmSupplier);

                    mtSupplierMaterialDao.update(sm); //编辑供货关系
                } else {
                    //否则执行新增供货关系
                    SrmSupplier supplier = new SrmSupplier();
                    //如果供应商id为空，执行新增供应商
                    if ("".equals(jo.optString("id")) || jo.optString("id") == null) {
                        supplier.setName(jo.optString("name"));//没有则新增此供应商
                        supplier.setFullName(jo.optString("fullName"));
                        supplier.setCodeName(jo.optString("codeName"));
                        supplier.setOrg(oid);//存入机构id
                        supplier.setSupplyCount(0);
                        supplier.setExclusiveCount(0);
                        supplier.setCutCount(0);
                        supplierDao.save(supplier);
                    } else {
                        //否则 认为供应商是检索出来已经存在的，查出此供应商
                        supplier = supplierDao.get(Integer.decode(jo.optString("id")));//有则查出供应商
                    }

                    srmSuppliers.add(supplier);

                    MtStockInfoHistory mtSH = new MtStockInfoHistory();
                    if (mSInfo != null) {
                        mtSH.setStockInfo(mSInfo.getId());
                    }


                    mtSH.setMaterial(mtBase.getId());
                    mtSH.setInitialStock(new BigDecimal(0));
                    mtSH.setMinimumStock(new BigDecimal(0));
                    mtSH.setAfterInitialStock(new BigDecimal(jo.optString("initialStock", "0")));
                    mtSH.setAfterMinimumStock(new BigDecimal(jo.optString("minimumStock", "0")));
                    mtSH.setSupplier(supplier.getId());
                    mtSH.setCreator(user.getUserID());
                    mtSH.setCreateName(user.getUserName());
                    mtSH.setCreateDate(new Date());

                    MtSupplierMaterial supplierMaterial = new MtSupplierMaterial();
                    if (!"".equals(jo.optString("minimumPurchase")) && jo.optString("minimumPurchase") != null) {
                        supplierMaterial.setMinimumPurchase(jo.optInt("minimumPurchase"));
                    }
                    if (!"".equals(jo.optString("perchaseCycle")) && jo.optString("perchaseCycle") != null) {
                        supplierMaterial.setPerchaseCycle(jo.optInt("perchaseCycle"));
                    }
                    if (!"".equals(jo.optString("unitPrice")) && jo.optString("unitPrice") != null) {
                        supplierMaterial.setUnitPrice(new BigDecimal(jo.optString("unitPrice")));
                    }
                    if (!"".equals(jo.optString("taxRate")) && jo.optString("taxRate") != null) {
                        supplierMaterial.setTaxRate(new BigDecimal(jo.optString("taxRate")));
                    }
//                    if (!"".equals(jo.optString("unitPriceNotax")) && jo.optString("unitPriceNotax") != null)
//                        supplierMaterial.setUnitPriceNotax(BigDecimal.valueOf(jo.optString("unitPrice")));
                    if (!"".equals(jo.optString("contractSn")) && jo.optString("contractSn") != null) {
                        supplierMaterial.setContractSn(jo.optString("contractSn"));
                    }

                    supplierMaterial.setPriceStable(jo.optString("priceStable"));
                    supplierMaterial.setCodeName(jo.optString("codeName"));
                    supplierMaterial.setDraftAcceptable(jo.optInt("draftAcceptable"));
                    supplierMaterial.setHasContact(jo.optString("hasContact"));
                    supplierMaterial.setValidDate(jo.optString("validDate").equals("") ? null : sd.parse(jo.optString("validDate")));
                    supplierMaterial.setSignDate(jo.optString("signDate").equals("") ? null : sd.parse(jo.optString("signDate")));
                    supplierMaterial.setInvoicable(jo.optString("invoicable"));
                    String invoiceCategory = jo.optString("invoiceCategory");
                    if (StringUtils.isNotBlank(invoiceCategory)) {
                        supplierMaterial.setInvoiceCategory(invoiceCategory);
                    }
                    supplierMaterial.setAtPar(StringUtils.isBlank(jo.optString("atPar")) ? null : jo.optString("atPar"));
                    supplierMaterial.setMinimumStock(new BigDecimal(jo.optString("minimumStock", "0")));
                    supplierMaterial.setInitialStock(new BigDecimal(jo.optString("initialStock", "0")));
                    supplierMaterial.setChargeBegin(StringUtils.isBlank(jo.optString("chargeBegin")) ? null : jo.optString("chargeBegin"));
                    supplierMaterial.setChargePeriod(jo.optInt("chargePeriod"));
                    supplierMaterial.setChargeAcceptable(jo.optInt("chargeAcceptable"));
                    supplierMaterial.setIsInclude(StringUtils.isBlank(jo.optString("isInclude")) ? null : jo.optString("isInclude"));
                    supplierMaterial.setMaterialInvoicable(jo.optInt("materialInvoicable"));
                    String materialInvoiceCategory = jo.optString("materialInvoiceCategory");
                    if (StringUtils.isNotBlank(materialInvoiceCategory)) {
                        supplierMaterial.setMaterialInvoiceCategory(materialInvoiceCategory);
                    }
                    //包装方式
                    supplierMaterial.setPackageMethod(StringUtils.isBlank(jo.optString("packageMethod")) ? null : jo.optString("packageMethod"));
                    supplierMaterial.setIsParValue(jo.optString("isParValue"));
                    if (!jo.optString("materialTaxRate").equals("")) {
                        supplierMaterial.setMaterialTaxRate(new BigDecimal(jo.optString("materialTaxRate", "0")));
                    }
                    supplierMaterial.setIsImprest(jo.optString("isImprest"));
                    supplierMaterial.setInclusiveFreight(jo.optInt("inclusiveFreight"));

                    supplierMaterial.setSupplier(supplier.getId());
                    supplierMaterial.setMaterial(mtBase);

                    mtSupplierMaterialDao.save(supplierMaterial); //编辑供货关系

                    mtStockInfoHistoryDao.save(mtSH);

//                    }

                }
            }
            BigDecimal minimumStock = new BigDecimal(0);
            BigDecimal currentStock = new BigDecimal(0);
            for (MtStockInfo ms : mtBase.getMtStockInfoHashSet()) {

                if (mtBase.getMtStockInfoHashSet() != null && mtBase.getMtStockInfoHashSet().size() > 0) {
                    minimumStock = minimumStock.add(ms.getMinimumStock() == null ? new BigDecimal(0) : ms.getMinimumStock());
                    currentStock = currentStock.add(ms.getCurrentStock() == null ? new BigDecimal(0) : ms.getCurrentStock());
                    mtBase.setStockPosition(ms.getStockPosition());//库位
                }
            }

            mtBase.setMinimumStock(minimumStock);//库存
            mtBase.setCurrentStock(currentStock + "");
            if (mtBase.getCategory().getParent() != null) {
                mtBase.setCategoryName(mtBase.getCategory().getParent().getName());//分类名称
            } else {
                mtBase.setCategoryName(mtBase.getCategory().getName());//分类名称

            }
            map.put("status", 1);
            MtBase newBase = mtBaseDao.get(jsonBase.optInt("id"));
            if (!name.equals(newBase.getName()) || !code.equals(newBase.getCode()))//修改了名字或者代号，也得更新科目
            {
                if (srmSuppliers.size() <= 0) {
                    Set<MtSupplierMaterial> materialHashSet = mtBase.getMtSupplierMaterialHashSet();

                    for (MtSupplierMaterial sm : materialHashSet) {
                        srmSuppliers.add(getSrmSupplierById(sm.getSupplier()));
                    }
                }
            }

            new SubjectUtils().addSubject(user, srmSuppliers, mtCategoryDao.get(Integer.valueOf(request.getParameter("pid"))), "", subjectSelectService, settingService, oid, newBase, null, userMessageService, userService, null, productService);

        } else {
            map.put("status", 0);
        }

        ObjectToJson.objectToJson1(map, new String[]{"parent", "org", "mtBaseHashSet", "mtCategoryHashSet", "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet", "org", "parent", "material", "supplier", "category", "isIgnore"}, response);

    }


    /*
     * subjectName       经销商科目名称
     * parent            经销商父科目编号
     * endSubjectName    要转换成的商品/物料/供应商/客户/半成品的科目名称
     * relevanceType     1-商品，2-物料,3-供应商,4-客户,5-半成品
     * */
    @Override
    public void changeIntoSubject(Integer oid, User user, String subjectName, String parent, String endSubjectName, HashMap o, String relevanceType, Integer relevanceItem) {
        //判断供应商是否存在
        String supplierSubject = settingService.checkRepeat(parent, subjectName, relevanceItem);
        if ("".equals(supplierSubject)) {
            //供应商不存在在1405下面增加供应商
            String newSupplier = settingService.firstSubject(parent, oid);
            JSONArray supplierArray = JSONArray.fromObject(newSupplier);
            for (int i = 0; i < supplierArray.size(); i++) {
                JSONObject obj = supplierArray.getJSONObject(i);
                String p = obj.getString("subject");
                String newSupplierSubject = obj.optString("newSubject");
//                int relevanceItem = (int) o.get("supplier_id");
//                Integer relevanceItem = (Integer) o.get("supplier_id");
                if (relevanceItem == null) {
                    continue;
                }
                JSONObject newSupplierID = settingService.newAccountantSubject(p, oid, subjectName, "", user.getUserID(), user.getUserName(), newSupplierSubject, "", relevanceType, relevanceItem);
                int newid = newSupplierID.optInt("newSubjectID", 0);
                if (newid > 0) {
                    String newPro = settingService.firstSubject(newSupplierSubject, oid);
                    JSONArray proArray = JSONArray.fromObject(newPro);
                    if (!proArray.isEmpty()) {
                        for (int j = 0; j < proArray.size(); j++) {
                            JSONObject pro = proArray.getJSONObject(j);
                            String newProSubject = pro.optString("newSubject");
                            if (o != null) {
                                relevanceItem = (int) o.get("id");
                            }
                            //新增商品科目
                            settingService.newAccountantSubject(newSupplierSubject, oid, endSubjectName, "", user.getUserID(), user.getUserName(), newProSubject, "", relevanceType, relevanceItem);
                        }
                    }

                }
            }
        } else {
            //供应商存在的话在已有供应商下增加
            String list = settingService.firstSubject(supplierSubject, oid);
            JSONArray proArray = JSONArray.fromObject(list);
            if (!proArray.isEmpty()) {
                for (int j = 0; j < proArray.size(); j++) {
                    JSONObject pro = proArray.getJSONObject(j);
                    String newProSubject = pro.optString("newSubject");
                    int relevanceItem_ = (int) o.get("id");
                    //新增商品科目
                    settingService.newAccountantSubject(supplierSubject, oid, endSubjectName, "", user.getUserID(), user.getUserName(), newProSubject, "", relevanceType, relevanceItem_);
                }
            }
        }
    }

    @Override
    public MtSupplierMaterial getMtSupplierMaterialBySupplierAndMaterial(Integer id, Integer mtId) {

        String hql = " from MtSupplierMaterial o where 1=1 and o.supplier=?0";
        if (mtId != null) {
            hql += " and o.material_=" + mtId;
        } else {
            hql += " and coalesce(o.material_,0)=0";
        }
        return mtSupplierMaterialDao.getByHQL(hql, id);
    }

    @Override
    public MtBase getMtBaseVersionAndPreVersion(int version, Integer pre) {
        String hql = " from MtBase o where o.previousId=?0 and versionNo=?1";

        return mtBaseDao.getByHQL(hql, pre, version);
    }

    @Override
    public Map getOrdersDetailsByCondition(Object id, Integer supplierId, Integer oid) {
        String sql = "SELECT poi.id, poi.unit_price, poi.memo, poi.orders FROM t_po_orders_item poi LEFT JOIN t_mt_supplier_material tmsm ON tmsm.id = poi.supplier_material LEFT JOIN t_mt_base mb ON mb.id = tmsm.material  LEFT JOIN t_srm_supplier ms ON tmsm.supplier = ms.id left join t_po_orders po on poi.orders=po.id";
        sql += " where ms.org=?0";
        sql += " and ifnull(poi.state,'') not in ('0','1')";
        sql += " and ms.id=?1";
        sql += " and mb.id=?2";
        sql += " and ifnull(po.approve_status,0)=2";
        return mtBaseDao.findMapByConditionNoPage(sql, new Object[]{oid, supplierId, id});
    }

    @Override
    public Integer locationSuppliers(MtSupplierMaterial mtSupplierMaterial, Integer supplierMaterialId, SrmSupplier supplier, MtBase base, User user) {

        mtSupplierMaterial.setSupplier(supplier.getId());
        mtSupplierMaterial.setEnabled("1");
        mtSupplierMaterial.setEnabledTime(new Date());
        mtSupplierMaterial.setCreateDate(new Date());
        mtSupplierMaterial.setCreateName(user.getUserName());
        mtSupplierMaterial.setCreator(user.getUserID());

        //处理空置报错问题  charat(0)问题
        if ("".equals(mtSupplierMaterial.getInvoicable())) {
            mtSupplierMaterial.setInvoicable(null);
        }
        if ("".equals(mtSupplierMaterial.getInvoiceCategory())) {
            mtSupplierMaterial.setInvoiceCategory(null);
        }
        if ("".equals(mtSupplierMaterial.getChargeBegin())) {
            mtSupplierMaterial.setChargeBegin(null);
        }
        if ("".equals(mtSupplierMaterial.getIsParValue())) {
            mtSupplierMaterial.setIsParValue(null);
        }
        if ("".equals(mtSupplierMaterial.getAtPar())) {
            mtSupplierMaterial.setAtPar(null);
        }
        if ("".equals(mtSupplierMaterial.getIsInclude())) {
            mtSupplierMaterial.setIsInclude(null);
        }
        if ("".equals(mtSupplierMaterial.getMaterialInvoiceCategory())) {
            mtSupplierMaterial.setMaterialInvoiceCategory(null);
        }
        if ("".equals(mtSupplierMaterial.getPriceStable())) {
            mtSupplierMaterial.setPriceStable(null);
        }
        if ("".equals(mtSupplierMaterial.getIsImprest())) {
            mtSupplierMaterial.setIsImprest(null);
        }
        if ("".equals(mtSupplierMaterial.getPayMethod())) {
            mtSupplierMaterial.setPayMethod(null);
        }
        if ("".equals(mtSupplierMaterial.getPackageMethod())) {
            mtSupplierMaterial.setPackageMethod(null);
        }
        if ("".equals(mtSupplierMaterial.getEnabled())) {
            mtSupplierMaterial.setEnabled(null);
        }
        if ("".equals(mtSupplierMaterial.getIsAppointed())) {
            mtSupplierMaterial.setIsAppointed(null);
        }

        //填加定点后 计算独家供应的材料
        //判断当前操作的材料是否同时被其他供应商供应 若否 怎独家供应+1  若是  则判断其他供应此材料的供应商是否为独家供应 若是 则 其他供应商独家供应的材料数-1
        MtBase b = mtBaseDao.get(base.getId());

        if (b != null) {
            List<MtSupplierMaterial> supplierMaterials = mtSupplierMaterialDao.getListByHQL(" from MtSupplierMaterial o where o.material_ = ?0", b.getId());

            if (supplierMaterials != null && supplierMaterials.size() == 1) {
                for (MtSupplierMaterial o : supplierMaterials) {
                    if (!o.getId().equals(mtSupplierMaterial.getId())) {
                        SrmSupplier s = supplierDao.get(o.getSupplier());//此供应商也供应当前操作的材料
                        if (s != null) {
                            s.setExclusiveCount(s.getExclusiveCount() == null || s.getExclusiveCount() <= 0 ? 0 : s.getExclusiveCount() - 1);
                            supplierDao.update(s);
                        }
                    }
                }
            } else if (supplierMaterials != null && supplierMaterials.size() <= 0) {
                supplier.setExclusiveCount(supplier.getExclusiveCount() == null ? 1 : supplier.getExclusiveCount() + 1);
            }
        }

        if (supplier.getChargeAcceptable() == null) {
            supplier.setChargeAcceptable(0);
        }


        supplierDao.update(supplier);
        if (mtSupplierMaterial.getId() == null || supplierMaterialId == 0) {
            mtSupplierMaterial.setId(null);
            mtSupplierMaterialDao.save(mtSupplierMaterial);
        } else {
            mtSupplierMaterialDao.getSession().clear();
            mtSupplierMaterialDao.update(mtSupplierMaterial);
        }

        Integer state = null;
        List<MtSupplierMaterial> supplierMaterials = mtSupplierMaterialDao.getBySupplierId(supplier.getId());
        if (supplierMaterials != null) {
            for (MtSupplierMaterial supplierMaterial : supplierMaterials) {
                if (supplierMaterial.getDraftAcceptable() != null && supplierMaterial.getDraftAcceptable() == 2) {
                    state = 2;
                }

                if (supplierMaterial.getDraftAcceptable() != null && supplierMaterial.getDraftAcceptable() == 1) {
                    state = 1;
                    break;
                }
            }
        } else {
            state = -1;
        }

        supplier.setDraftAcceptable(state);
        supplierDao.update(supplier);
        return 1;
    }


    @Override
    public Map getSupplierMaterials(Integer supplierId, Integer state, Integer enabled, Integer invoiceType, Integer org) {
        String sql = "select tmb.id as materialId,tmb.name , tmb.code, tmb.specifications , tmb.model , tmb.create_name , tmb.create_date,tmsm .supplier,tmu.name as unit ,tmu.id as unit_id from t_mt_supplier_material tmsm left join t_srm_supplier tss on tmsm.supplier = tss.id left join t_mt_base tmb on tmsm.material = tmb.id left join t_mt_unit tmu on tmb.unit_id = tmu.id where tmsm .supplier =?0 and tmsm .enabled =?1";
        Object[] params = new Object[]{supplierId, enabled};
        if (state != null && state == 2) {//查询独家供应的材料
            sql += " and ifnull((select count(distinct msm.id) from t_mt_supplier_material msm left join t_mt_base mb on msm.material = mb.id where mb.id = tmsm.material),0)=1 and ifnull((select count(distinct msm.id) from t_mt_supplier_material msm left join t_mt_base mb on msm.material = mb.id where mb.id = tmsm.material and msm.supplier = tmsm.supplier),0)=1";
            //params = new Object[]{supplierId,enabled,supplierId};
        }
        //1-给开增值税专用发票的 2-给开其他发票的 3-不给开票或不确定是否开票的
        if (invoiceType != null) {
            switch (invoiceType) {
                case 1:
                    sql += " and (ifnull(material_invoiceable,0)=1 and ifnull(tmsm.material_invoice_category,0)=1)";
                    break;
                case 2:
                    sql += " and (ifnull(material_invoiceable,0)=1 and ifnull(tmsm.material_invoice_category, 0) = 2)";
                    break;
                case 3:
                    sql += " and ifnull(tmsm.invoice_able,0) in (0,2)";
                    break;
            }
        }

        sql += " AND tmb.name IS NOT NULL";
        sql += " AND tmb.org=" + org;
        return mtSupplierMaterialDao.findMapByConditionNoPage(sql, params);
    }

    @Override
    public List<SrmSupplierHistory> getSrmSupplierHistory(Integer id, String orderBy, String rule) {
        String hql = " from SrmSupplierHistory o where o.supplier = ?0";
        if (StringUtils.isNotBlank(orderBy)) {
            hql += " order by " + orderBy + " " + rule;
        }
        return smrHistoryDao.getListByHQL(hql, id);
    }

    @Override
    public void saveSrmSupplierHistory(SrmSupplierHistory supplierHistory) {
        smrHistoryDao.save(supplierHistory);
    }
}
