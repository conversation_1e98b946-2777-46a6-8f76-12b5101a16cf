package cn.sphd.miners.modules.schedule.service;

import cn.sphd.miners.modules.schedule.entity.MemoScheduleSupplement;
import cn.sphd.miners.modules.schedule.entity.MemoScheduleSupplementImage;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;

/**
 * Created by Administrator on 2019/8/14.
 */
public interface MemoScheduleSupplementService {

    List<MemoScheduleSupplement>  getMemoScheduleSupplementListBySid(Integer scheduleId);

    boolean addMemoScheduleSupplement(User user, Integer memoJobId, String description, String[] imagePaths);

    boolean deleteMemoScheduleSupplement(Integer supplementId);

    List<MemoScheduleSupplement>  getMemoScheduleSupplementListByJobId(Integer memoJobId);

    MemoScheduleSupplement getMemoScheduleSupplementById(Integer id);

    MemoScheduleSupplementImage getMemoScheduleSupplementImageById(Integer id);
}
