package cn.sphd.miners.modules.material.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.material.dao.SrmSupplierContactDao;
import cn.sphd.miners.modules.material.entity.SrmSupplierContact;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

/**
 * Created by Administrator on 2016/9/19.
 */
@Repository
public class SrmSupplierContactDaoImpl extends BaseDao<SrmSupplierContact,Serializable> implements SrmSupplierContactDao {


    @Override
    public void deleteSrmSupplierContact(SrmSupplierContact mtSupplierContact) {
        delete(mtSupplierContact);



    }

    @Override
    public SrmSupplierContact getSrmSupplierContactById(Integer id) {

        String hql="from SrmSupplierContact where id="+id;
        SrmSupplierContact mtSupplierContact=getByHQL(hql);

        return mtSupplierContact;
    }
}
