<template>
  <div id="backDetail" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="回款录入" :isBackHome="isBackHome"></TY_NavTop>
    <div class="container">
      <div class="ui-message">
        <div class="sendContainer">
          <div class="lHead">
            <p class="line-s">{{customer}}</p>
            <p class="listCon line-s">本次回款 {{detail.method | incomeType}} {{detail.amount | formatMoney}}元</p>
          </div>
          <div class="sectCenter">
            <div class="checkWay" v-if="detail.method === '1'">
              <div class="line-p">
                <span class="tt-cl">收到日期</span>
                <span class="sub-cl">{{detail.receiveDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
            </div>
            <div class="checkWay" v-if="detail.method === '3'">
              <div class="line-p">
                <span class="tt-cl">收到日期</span>
                <span class="sub-cl">{{detail.receiveDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
              <div class="line-p">
                <span class="tt-cl">支票号</span>
                <span class="sub-cl">{{detail.returnNo}}</span>
              </div>
              <div class="line-p">
                <span class="tt-cl">到期日</span>
                <span class="sub-cl">{{detail.expireDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
              <div class="line-p">
                <span class="tt-cl">出具的单位</span>
                <span class="sub-cl">{{detail.originalCorp}}</span>
              </div>
              <div class="line-p">
                <span class="tt-cl">出具的银行</span>
                <span class="sub-cl">{{detail.bankName}}</span>
              </div>
            </div>
            <div class="checkWay" v-if="detail.method === '4'">
              <div class="line-p">
                <span class="tt-cl">收到日期</span>
                <span class="sub-cl">{{detail.receiveDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
              <div class="line-p">
                <span class="tt-cl">汇票号</span>
                <span class="sub-cl">{{detail.returnNo}}</span>
              </div>
              <div class="line-p">
                <span class="tt-cl">到期日</span>
                <span class="sub-cl">{{detail.expireDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
              <div class="line-p">
                <span class="tt-cl">最初出具的单位</span>
                <span class="sub-cl ellipsisSet">{{detail.originalCorp}}</span>
              </div>
              <div class="line-p">
                <span class="tt-cl">出具的银行</span>
                <span class="sub-cl ellipsisSet">{{detail.bankName}}</span>
              </div>
            </div>
            <div class="checkWay" v-if="detail.method === '5'">
              <div class="line-s">预计到账的日期范围</div>
              <div class="line-p">
                <span class="sub-cl">{{detail.expectBeginDate | formatDate('yyyy年MM月dd日')}}</span>
                -
                <span class="sub-cl">{{detail.expectEndDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
            </div>
            <p class="line-s inputerInfo">录入者：{{detail.createName}} {{detail.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</p>
          </div>
          <div class="handle_button handle-center">
            <input type="submit" :class="{'fd-btn fc-btn-green': !isCan, 'fd-btn ui-btn_disabled': isCan}" value="撤销" :disabled="isCan" @click="stopDialog = true"/>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      title=" ! 提示"
      :visible.sync="stopDialog"
      width="80%" custom-class="def-danger">
      <p class="handle-center">确定撤销本次回款申请？</p>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button handle-center">
          <input type="submit" class="fc-btn-normal" value="取消" @click="stopDialog = false">
          <input type="submit" class="fc-btn ui-btn_error" value="确定" @click="stopApprove()">
        </div>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { incomeType, formatDate, formatMoney } from '../../../js/common'
export default {
  name: 'payApplyDetail',
  filters: {
    incomeType,
    formatDate,
    formatMoney
  },
  data () {
    return {
      isCan: false,
      detail: {},
      customer: '',
      loading: true,
      agreeForm: {
        type: false
      },
      stopDialog: false,
      listenersUid: []
    }
  },
  computed: {
  },
  created () {
    let _this = this
    let dlId = _this.$route.params.sId
    _this.listenersUid = [
      _this.sphdSocket.subscribe('getSlCollectDetail', function (data) {
        let resDetail = JSON.parse(data)
        _this.customer = resDetail.customerName
        _this.detail = resDetail.slCollectApplication
        _this.loading = false
      })
    ]
    _this.sphdSocket.send('getSlCollectDetail', { 'collectId': dlId, 'session': _this.sphdSocket.sessionid })
  },
  destroyed: function () {
    let _this = this
    _this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    stopApprove: function () {
      this.loading = true
      let dlId = this.$route.params.sId
      this.$http.post('../../../salesBack/receivableUndo.do', { 'id': dlId }, {
        emulateJSON: true
      }).then((response) => {
        let data = response.body
        this.stopDialog = false
        if (data.status === 1) {
          this.isCan = true
          this.$message({
            type: 'success',
            message: '撤销成功'
          })
        } else {
          this.$message({
            type: 'error',
            message: '系统错误，请重试！'
          })
        }
        this.reBack()
      }).catch(function () {
        this.$message({
          type: 'error',
          message: '系统错误，请重试！'
        })
      })
    },
    reBack: function () {
      let floatingUrl = window.localStorage.getItem('floating')
      if (floatingUrl) {
        floatingUrl = floatingUrl.split('-')
        if (floatingUrl.length > 0) {
          floatingUrl.pop()
          if (floatingUrl.length > 0) {
            this.$router.push({
              path: floatingUrl[floatingUrl.length - 1]
            })
          } else {
            this.$router.push({
              path: '/'
            })
          }
          floatingUrl.pop()
          window.localStorage.setItem('floating', floatingUrl.join('-'))
        } else {
          this.$router.push({
            path: '/'
          })
        }
      }
    }
  }
}
</script>
