<template>
  <div id="app">
    <div class="container">
      <router-view></router-view>
    </div>
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>

<style>
  .tip{
    background-color: #eee;
    font-size: 12px;
    padding:4px 12px;
    color: #666;
  }
  .main_container{
    padding: 10px 0;
    background-color: #fafafa;
    height: calc(100vh - 66px);
    overflow: auto;
  }
  .panel{
    border-bottom: 1px solid #f3f3f3;
    padding-left: 8px;
    font-size: 14px;
    line-height: 32px;
    color: #6e6e6e;
    margin: 16px 0;
  }
  .panelTitle{
    display: inline-block;
    border-bottom: 2px solid #409EFF;
  }
  .blue{
    color: #3cb0a7;
  }
  #app {
    font-family: 'Avenir', Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: #2c3e50;
  }
</style>
