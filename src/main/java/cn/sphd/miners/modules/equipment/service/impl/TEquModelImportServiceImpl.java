package cn.sphd.miners.modules.equipment.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import cn.sphd.miners.modules.equipment.entity.*;
import cn.sphd.miners.modules.equipment.mapper.*;
import cn.sphd.miners.modules.equipment.service.ITEquModelImportService;
import cn.sphd.miners.modules.material.dao.SrmSupplierDao;
import cn.sphd.miners.modules.material.entity.SrmSupplier;
import cn.sphd.miners.modules.mci.utils.AjaxResult;
import cn.sphd.miners.modules.system.entity.User;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-03-30
 */
@Service
@Transactional
public class TEquModelImportServiceImpl implements ITEquModelImportService
{
    @Autowired
    private TEquModelImportMapper tEquModelImportMapper;

    @Autowired
    private TEquEquipmentMapper equEquipmentMapper;

    @Autowired
    private TEquModelMapper modelMapper;

    @Autowired
    TEquCategoryMapper categoryMapper;

    @Autowired
    SrmSupplierDao supplierDao;

    @Autowired
    TEquFixedAssetsMapper fixedAssetsMapper;
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    @Override
    public TEquModelImport selectTEquModelImportById(Long id)
    {
        return tEquModelImportMapper.selectTEquModelImportById(id);
    }

    /**
     * 查询列表
     * 
     * @param tEquModelImport 
     * @return 
     */
    @Override
    public List<TEquModelImport> selectTEquModelImportList(TEquModelImport tEquModelImport)
    {
        List<TEquModelImport> modelImports =  tEquModelImportMapper.selectTEquModelImportList(tEquModelImport);
        if (modelImports != null) {
            for (TEquModelImport modelImport : modelImports) {
                if(modelImport.getCategory()!=null){
                    TEquCategory c = categoryMapper.selectTEquCategoryById(modelImport.getCategory());
                    if (c != null) {
                        modelImport.setCategoryName(c.getName());
                    }
                }
            }
        }

        return modelImports;
    }

    /**
     * 新增
     * 
     * @param tEquModelImport 
     * @return 结果
     */
    @Override
    public int insertTEquModelImport(TEquModelImport tEquModelImport)
    {
        return tEquModelImportMapper.insertTEquModelImport(tEquModelImport);
    }

    /**
     * 修改
     *
     * @param tEquModelImport 
     * @param user
     * @return 结果
     */
    @Override
    public int updateTEquModelImport(TEquModelImport tEquModelImport, User user)
    {
        //查重
        if(StringUtils.isNotBlank(tEquModelImport.getModelCode())){
            TEquModelImport modelImport = new TEquModelImport();
            modelImport.setModelCode(tEquModelImport.getModelCode());
            modelImport.setOrg(Long.valueOf(user.getOid()));

            List<TEquModelImport> imports = tEquModelImportMapper.selectTEquModelImportList(modelImport);
            if(imports!=null&& !imports.isEmpty()){
                return 0;

            }

            TEquModel model = new TEquModel();
            model.setModelCode(tEquModelImport.getModelCode());
            model.setOrg(Long.valueOf(user.getOid()));
            List<TEquModel> models = modelMapper.selectTEquModelList(model);
            if(models!=null&& !models.isEmpty()){
                return 0;

            }
        }

        return tEquModelImportMapper.updateTEquModelImport(tEquModelImport);
    }

    /**
     * 批量删除
     * 
     * @param ids 需要删除的主键
     * @return 结果
     */
    @Override
    public int deleteTEquModelImportByIds(String ids)
    {
        return tEquModelImportMapper.deleteTEquModelImportByIds(ids.split(","));
    }

    /**
     * 删除信息
     * 
     * @param id 主键
     * @return 结果
     */
    @Override
    public int deleteTEquModelImportById(Long id)
    {
        return tEquModelImportMapper.deleteTEquModelImportById(id);
    }

    @Override
    public AjaxResult importData(String ids, User user) {
        TEquModelImport modelImport = new TEquModelImport();
        modelImport.setOrg(Long.valueOf(user.getOid()));
        modelImport.setState(1L);
        List<TEquModelImport> imports = selectTEquModelImportList(modelImport);
        if (imports != null) {
            for (TEquModelImport i : imports) {
                if (i != null) {
                    TEquEquipment e = new TEquEquipment();
                    e.setOrg(Long.valueOf(user.getOid()));
                    e.setName(i.getEquipmentName());
                    TEquEquipment equipment = equEquipmentMapper.selectTEquEquipmentByName(e);
                    if (equipment != null) {//查到了名称
                        equipment.setQuantity(equipment.getQuantity()==null ? 1 : equipment.getQuantity()+1);
                        equipment.setModelCount(equipment.getModelCount()==null?1:equipment.getModelCount()+1);
                        equEquipmentMapper.updateTEquEquipment(equipment);
                        i.setEquipment(equipment.getId());
                    }else {//没查到，新增名称
                        equipment =new TEquEquipment();
                        equipment.setName(i.getEquipmentName());
                        equipment.setFullName(i.getEquipmentName());
                        equipment.setOrg(Long.valueOf(user.getOid()));
                        equipment.setQuantity(1L);
                        equipment.setModelCount(1L);
                        equipment.setCreator(Long.valueOf(user.getUserID()));
                        equipment.setCreateDate(new Date());
                        equipment.setCreateName(user.getUserName());
                        equEquipmentMapper.insertTEquEquipment(equipment);
                        i.setEquipment(equipment.getId());
                    }

                    TEquModel model = new TEquModel();
                    BeanUtils.copyProperties(i,model);

                    model.setCreateDate(new Date());
                    model.setCreateName(user.getUserName());
                    model.setCreator(Long.valueOf(user.getUserID()));
                    modelMapper.insertTEquModel(model);
                    //1.292将字段移到了新表
                    TEquFixedAssets fixedAssets = new TEquFixedAssets();

                    if(StringUtils.isNotBlank(i.getoLifeSpan())){
                        fixedAssets.setLifeSpan(Integer.valueOf(i.getoLifeSpan()));
                    }
                    if(StringUtils.isNotBlank(i.getoValue()))
                        fixedAssets.setOriginalValue(Double.valueOf(i.getoValue()));
                    if(StringUtils.isNotBlank(i.getrDate())){
                        SimpleDateFormat sd = new SimpleDateFormat("yyyyMM");
                        try {
                            fixedAssets.setReceiveDate(sd.parse(i.getrDate()));
                        } catch (ParseException ec) {
                            throw new RuntimeException(ec);
                        }
                    }

                    fixedAssets.setModel(model.getId());
                    fixedAssets.setCreateDate(new Date());
                    fixedAssets.setCreateName(user.getUserName());
                    fixedAssets.setEquipment(equipment.getId());
                    fixedAssets.setEnabled(1);
                    fixedAssets.setEnabledTime(new Date());
                    fixedAssets.setOrg(user.getOid());
                    fixedAssets.setCode(model.getModelCode());

                    fixedAssetsMapper.insertTEquFixedAssets(fixedAssets);

                    if (model.getSupplier()!=null&&model.getSupplier()!=0L){
                        SrmSupplier supplier = supplierDao.get(model.getSupplier().intValue());
                        if (supplier != null) {
                            supplier.setSupplyCount(supplier.getSupplyCount()==null?1:supplier.getSupplyCount()+1);
                            supplierDao.update(supplier);
                        }
                    }

                    i.setState(2L);
                    tEquModelImportMapper.updateTEquModelImport(i);
                }


            }
        }

        return new AjaxResult(AjaxResult.Type.SUCCESS,"");
    }

    @Override
    public List<TEquModelImport> check(TEquModelImport modelImport,  User user) {
        List<TEquModelImport> result = new ArrayList<TEquModelImport>();
            TEquModelImport i = new TEquModelImport();
            i.setOrg(Long.valueOf(user.getOid()));
            i.setState(1L);
            List<TEquModelImport> imports = tEquModelImportMapper.selectTEquModelImportList(i);
            for (TEquModelImport anImport : imports) {
                if(StringUtils.isNotBlank(anImport.getModelCode())){
                    TEquModel model = new TEquModel();
                    model.setModelCode(anImport.getModelCode());
                    model.setOrg(Long.valueOf(user.getOid()));
                    List<TEquModel> models = modelMapper.selectTEquModelList(model);

                    if (models!=null&&models.size()>0){
                        result.add(anImport);
                    }
                }

            }

        return result;
    }
}
