package cn.sphd.miners.modules.investigate.controller;

import cn.sphd.miners.common.initializer.AuthPassport;
import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.DigestUtils;
import cn.sphd.miners.common.utils.GetLocalIPUtils;
import cn.sphd.miners.common.utils.JsencryptUtils;
import cn.sphd.miners.modules.investigate.dto.*;
import cn.sphd.miners.modules.investigate.entity.InvestigateObject;
import cn.sphd.miners.modules.investigate.entity.InvestigateObjectHistory;
import cn.sphd.miners.modules.investigate.entity.InvestigatePublish;
import cn.sphd.miners.modules.investigate.entity.InvestigateSubject;
import cn.sphd.miners.modules.investigate.service.InvestigationService;
import cn.sphd.miners.modules.investigate.service.PublishService;
import cn.sphd.miners.modules.material.entity.RespStatus;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgService;
import com.alibaba.fastjson.JSON;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;

/**
 * @ClassName PublishController
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/1/13 15:57
 * @Version 1.0
 *
 */
@Controller
@RequestMapping("/investigationPublish")
public class PublishController {
    @Autowired
    PublishService publishService;
    @Autowired
    OrgService orgService;
    @Autowired
    InvestigationService investigationService;
    /**
     * 生成二维码
     * <AUTHOR>
     * @since 2021/3/8 10:33
     * @param session
     * @param request
     * @param userId 用户id，PC前端可不传
     * @param id 调查表id
     * @param expireDate time有效期，时间值.getTime()
     * @return: cn.sphd.miners.common.persistence.JsonResult
     */
    @ResponseBody
    @RequestMapping("/getQRLink.do")
    public JsonResult getQRLink(User user , HttpServletRequest request, Integer id, Long expireDate) {
        if (id==null) {
            throw new IllegalArgumentException("id must not be null(id不能为空)!");
        }
        if (expireDate==null) {
            throw new IllegalArgumentException("expireDate must not be null(过期时间不能为空)!");
        }
//        User user = getUser(session,userId);
        final String salt = "bC1]gB5@";
        final String enc = "UTF-8";
        HashMap<String, Object> params = new HashMap<>();
        params.put("id", id);
        params.put("userId", user.getUserID());
        params.put("oid", user.getOid());
        //params.put("orgName",orgService.orgName(user.getOid()));
        //InvestigatePublish publish=publishService.selectInvestigatePublishByid(id);
        //params.put("modifyLimit",publish.getModifyLimit());
        params.put("expire_date", expireDate);
        params.put("salt", DigestUtils.digest(id + user.getUserID() + user.getOid() + expireDate + salt, DigestUtils.SHA1));
        String item = new JsencryptUtils().encrypt(JSON.toJSONString(params));
        if (item != null && !item.isEmpty()) {
            try {
                item = URLEncoder.encode(item, enc);
                String items=GetLocalIPUtils.getRootPath(request) + "/vue/investigation/dist/index.html#/item/" + item;
                return new JsonResult(1, GetLocalIPUtils.getRootPath(request) + "/vue/investigation/dist/index.html#/item/" + item);
            } catch (UnsupportedEncodingException e) {
                Logger.getLogger(getClass()).error("二维码链接生成错误",e);
            }
        }
        return new JsonResult(new MyException("500","二维码链接生成错误"));
    }

    //调查管理首页接口
    //status 1启用状态的，0停用状态的
    //pageSize 每页条数 currentPageNo 当前页数
    //返回值  pageSize 每页条数 currentPageNo 当前页数  sumPage 总页数 totalResult 总条数
    @ResponseBody
    @RequestMapping("/selectPublishList.do")
    public RespInvestigatePublishListPage selectPublishList(User user, PageInfo pageInfo, Integer status, String month) throws IOException {
        RespInvestigatePublishListPage respInvestigatePublishListPage =new RespInvestigatePublishListPage();
        //处理已经完成的调查因定时任务未执行导致的数据错误
        publishService.selectTask();
        respInvestigatePublishListPage.setOrgName(orgService.orgName(user.getOid()));
        if(status==null)
            status=1;
        List<InvestigatePublish> list=publishService.selectInvestigatePublishList(user,pageInfo,status,month);
        respInvestigatePublishListPage.setInvestigatePublishList(list);
        if(pageInfo.getTotalPage()==0)
            pageInfo.setTotalPage(1);
        int sum=publishService.countInvestigatePublish(user,status,month);
        respInvestigatePublishListPage.setSum(sum);
        respInvestigatePublishListPage.setPageInfo(pageInfo);
        return respInvestigatePublishListPage;
    }

    //已停用调查月列表
    @ResponseBody
    @RequestMapping("/selectPublishStopByMonthList.do")
    public RespInvestigateStopByMonthList selectPublishStopByMonthList(User user, String year) throws IOException {
        RespInvestigateStopByMonthList respInvestigateStopByMonthList =publishService.selectSubjectStopByMonthList(user,year);
        return respInvestigateStopByMonthList;
    }

    //查询接口
    //keyword 内容
    //pageSize 每页条数 currentPageNo 当前页数
    //返回值  pageSize 每页条数 currentPageNo 当前页数  sumPage 总页数 totalResult 总条数
    @ResponseBody
    @RequestMapping("/selectPublishByKeywordList.do")
    public RespInvestigatePublishListPage selectPublishByKeywordList(User user, PageInfo pageInfo, String keyword) throws IOException {
        RespInvestigatePublishListPage respInvestigatePublishListPage =new RespInvestigatePublishListPage();
        List<InvestigatePublish> list=publishService.selectPublishByNameList(user,pageInfo,keyword);
        respInvestigatePublishListPage.setInvestigatePublishList(list);
        if(pageInfo.getTotalPage()==0)
            pageInfo.setTotalPage(1);
        respInvestigatePublishListPage.setPageInfo(pageInfo);
        respInvestigatePublishListPage.setSum(pageInfo.getTotalResult());
        return respInvestigatePublishListPage;
    }

    //可选择问卷
    @ResponseBody
    @RequestMapping("/selectSubjectOptionsList.do")
    public List<InvestigateSubject>  selectSubjectOptionsList(User user) throws IOException {
        List<InvestigateSubject> list=publishService.selectSubjectOptionsList(user.getOid());
        return list;
    }

    //发起调查
    //传值 id(Subject的id) expireDate截止日期 modifyLimit 允许修改时间
    @ResponseBody
    @RequestMapping("/addPublish.do")
    public RespStatus addPublish(Long expireDate, User user, Integer id, Integer modifyLimit,String coverImage) {
        RespStatus respStatus=new RespStatus();
        int status=publishService.addPublish(expireDate,user,id,modifyLimit,coverImage);
        respStatus.setStatus(status);
        return respStatus;
    }

    //二维码管理
    //传值 id
    @ResponseBody
    @RequestMapping("/selectInvestigateQr.do")
    public RespInvestigateQrList selectInvestigateQr(User user, Integer id) {
        publishService.initializationQr(id);
        RespInvestigateQrList respInvestigateQrList=publishService.selectInvestigateQr(user,id);
        return respInvestigateQrList;
    }
    //管理接口    1.242新增
    //type为1只修改允许修改时间，为2有截止日期修改，为3立即终止   expireDate截止日期 modifyLimit 允许修改时间
    @ResponseBody
    @RequestMapping("/managePublish.do")
    public RespStatus managePublish(User user, Integer id,Long expireDate,Integer type,Integer modifyLimit) {
        RespStatus respStatus=new RespStatus();
        int status=0;
        if(type!=3)
        {
            status=publishService.updatePublishModifyLimit(modifyLimit,user,id);
        }
        if (type==2) {
            status=publishService.updatePublish(expireDate,user,id);
        } else if(type==3){
            status=publishService.stopPublish(user,id);
        }
        respStatus.setStatus(status);
        return respStatus;
    }
    //修改微信分享图片 1.242新增
    //传值 id(Publish的id) coverImage 封面图片存储路径
    @ResponseBody
    @RequestMapping("/updatePublishcoverImage.do")
    public RespStatus updatePublishCoverImage(User user, Integer id,String coverImage) {
        RespStatus respStatus=new RespStatus();
        int status=publishService.updatePublishCoverImage(coverImage,user,id);
        respStatus.setStatus(status);
        return respStatus;
    }



    //重新生成二维码
    //传值 id(Publish的id)
    //expireDate
    @ResponseBody
    @RequestMapping("/updatePublish.do")
    public RespStatus updatePublish(User user, Integer id,Long expireDate) {
        RespStatus respStatus=new RespStatus();
        int status=publishService.updatePublish(expireDate,user,id);
        respStatus.setStatus(status);
        return respStatus;
    }


    //终止调查
    @ResponseBody
    @RequestMapping("/stopPublish.do")
    public RespStatus stopPublish(User user, Integer id) {
        RespStatus respStatus=new RespStatus();
        int status=publishService.stopPublish(user,id);
        respStatus.setStatus(status);
        return respStatus;
    }

    //已回收的问卷
    //pageSize 每页条数 currentPageNo 当前页数
    //type 为1时，纳入统计，为0是，未纳入统计
    //返回值  pageSize 每页条数 currentPageNo 当前页数  sumPage 总页数 totalResult 总条数
    @ResponseBody
    @RequestMapping("/completeSubjectList.do")
    public RespShowQuestion completeSubjectList(PageInfo pageInfo,Integer id,Integer type) {
        RespShowQuestion respShowQuestion=publishService.completeSubjectList(pageInfo,id,type);
        if(pageInfo.getTotalPage()==0)
            pageInfo.setTotalPage(1);
        respShowQuestion.setPageInfo(pageInfo);
        return respShowQuestion;
    }

    //修改是否纳入统计
    //pageSize 每页条数 currentPageNo 当前页数
    //type 1降状态修改为纳入统计，0将状态修改为未纳入统计
    //返回值  status 1成功，0失败
    @ResponseBody
    @RequestMapping("/updateObjectSubsumable.do")
    public RespStatus updateObjectSubsumable(Integer id,Integer type) {
        RespStatus respStatus=new RespStatus();
        int status=publishService.updateObjectSubsumable(id,type);
        respStatus.setStatus(status);
        return respStatus;
    }

    //导出excel
    //传值 id(Publish的id) updateQuestionJson[{question:}] 将所有最新的被选中的questionId传上来
    @ResponseBody
    @RequestMapping("/exportExcel.do")
    public void exportExcel(@RequestBody ExportExcelJson exportExcelJson, HttpServletResponse response) throws IOException {
        RespShowQuestion respShowQuestion=publishService.exportExcelSubjectList(exportExcelJson.getId(),exportExcelJson.getUpdateQuestionJson());
        publishService.exportExcel(respShowQuestion,exportExcelJson.getId(),response);
    }
    //可选择显示问题列表
    //传值 id(Publish的id)
    @ResponseBody
    @RequestMapping("/selectQuestionOptionsList.do")
    public List<RespInvestigateQuestion> selectQuestionOptionsList(Integer id) {
        List<RespInvestigateQuestion>  investigateQuestionList=publishService.selectQuestionOptionsList(id);
        return investigateQuestionList;
    }

    //修改显示问题接口
    //传值 id(Publish的id) updateQuestionJson[{question:}] 将所有最新的被选中的questionId传上来
    @ResponseBody
    @RequestMapping("/updateShowQuestion.do")
    public RespStatus updateShowQuestion(User user, Integer id,String updateQuestionJson) {
        RespStatus respStatus=new RespStatus();
        int status=publishService.updateShowQuestion(user,id,updateQuestionJson);
        respStatus.setStatus(status);
        return respStatus;
    }

    //查看答题情况
    //传值 id(objectId)
    @ResponseBody
    @RequestMapping("/selectObjectById.do")
    public RespInvestigateSubject selectObjectById(Integer id) {
        RespInvestigateSubject respInvestigateSubject=publishService.selectObjectById(id);
        return respInvestigateSubject;
    }


    //调查表
    //传值 id（Publish的id)
    //hashKey
    //返回值
    @AuthPassport(validate = false)//游客权限可访问
    @ResponseBody
    @RequestMapping("/investigateSubjectDetails.do")
    public RespInvestigateSubject investigateSubjectDetails(int id, String hashKey) {
        RespInvestigateSubject respInvestigateSubject=new RespInvestigateSubject();
        int status=0;
        int objectId=0;
        if(!"".equals(hashKey)&&hashKey!=null)
            objectId=publishService.selectHashKey(hashKey,id);
        if (objectId!=0)
        {
            respInvestigateSubject = publishService.selectObjectById(objectId);
        }else {
            InvestigatePublish investigatePublish=publishService.selectObjectIdByPublishId(id);
            respInvestigateSubject = investigationService.selectSubject(investigatePublish.getSubject());
            respInvestigateSubject.setPublishEnabled(investigatePublish.getEnabled());
            respInvestigateSubject.setStatus(status);
        }

        InvestigatePublish publish=publishService.selectInvestigatePublishByid(id);
        respInvestigateSubject.setOrgName(orgService.orgName(publish.getOrg()));
        respInvestigateSubject.setPublishId(id);
        respInvestigateSubject.setModifyLimit(publish.getModifyLimit());
        return respInvestigateSubject;
    }

    //应答接口
    // hashKey
    // publish 调查id
    // investigateAnswerJson 应答内容{question:问题id，answer:"应答id多个以以逗号分隔"，content:应答内容，多个以逗号分割}
    @AuthPassport(validate = false)//游客权限可访问
    @ResponseBody
    @RequestMapping("/addInvestigateObject.do")
    public JsonResult addInvestigateObject(ReqInvestigateObject reqInvestigateObject) {
        InvestigateObject investigateObject=publishService.addInvestigateObject(reqInvestigateObject);
        final String salt = "CPs%1J:3";
        final String enc = "UTF-8";
        HashMap<String, Object> params = new HashMap<>();
        params.put("id", investigateObject.getPublish());
        params.put("org", investigateObject.getOrg());
        params.put("objective", investigateObject.getId());
        params.put("answerTime", investigateObject.getCreateDate().getTime());
        params.put("hashKey", investigateObject.getHashKey());
        String item = new JsencryptUtils().encrypt (JSON.toJSONString(params));
        if (item != null && !item.isEmpty()) {
            try {
                item = URLEncoder.encode(item, enc);
                item = DigestUtils.digest(investigateObject.getId().toString() + investigateObject.getCreateDate().getTime() + investigateObject.getHashKey() + salt, DigestUtils.SHA1) + item;
                return new JsonResult(1, item);
            } catch (UnsupportedEncodingException e) {
                Logger.getLogger(getClass()).error("addInvestigateObject.do",e);
            }
        }
        return new JsonResult(new MyException("500","返回数据生成错误"));
    }
    //修改调查表
    // hashKey
    // objective
    // publish 调查id
    // investigateAnswerJson 应答内容{question:问题id，answer:"应答id多个以以逗号分隔"，content:应答内容，多个以逗号分割}
    @AuthPassport(validate = false)//游客权限可访问
    @ResponseBody
    @RequestMapping("/updateInvestigateObject.do")
    public JsonResult updateSurveyObject(ReqInvestigateObject reqInvestigateObject) {
        InvestigateObject investigateObject=publishService.updateInvestigateObject(reqInvestigateObject);
        final String salt = "CPs%1J:3";
        final String enc = "UTF-8";
        HashMap<String, Object> params = new HashMap<>();
        params.put("id", investigateObject.getPublish());
        params.put("org", investigateObject.getOrg());
        params.put("objective", investigateObject.getId());
        params.put("answerTime", investigateObject.getCreateDate().getTime());
        params.put("hashKey", investigateObject.getHashKey());
        String item = new JsencryptUtils().encrypt (JSON.toJSONString(params));
        if (item != null && !item.isEmpty()) {
            try {
                item = URLEncoder.encode(item, enc);
                item = DigestUtils.digest(investigateObject.getId().toString() + investigateObject.getCreateDate().getTime() + investigateObject.getHashKey() + salt, DigestUtils.SHA1) + item;
                return new JsonResult(1, item);
            } catch (UnsupportedEncodingException e) {
                Logger.getLogger(getClass()).error("updateSurveyObject.do",e);
            }
        }
        return new JsonResult(new MyException("500","返回数据生成错误"));
    }
    //应答历史
    //id objectId
    @AuthPassport(validate = false)//游客权限可访问
    @ResponseBody
    @RequestMapping("/investigateObjectHistoryList.do")
    public  List<InvestigateObjectHistory> investigateObjectHistoryList(int id) {
        List<InvestigateObjectHistory> list=publishService.investigateObjectHistoryList(id);
        return list;
    }
    //其他数据
    //id objectId   hashKey
    @AuthPassport(validate = false)//游客权限可访问
    @ResponseBody
    @RequestMapping("/investigateObjectOtherList.do")
    public  List<RespInvestigateObject> investigateObjectOtherList(int id,String hashKey) {
        List<RespInvestigateObject> list=publishService.investigateObjectOtherList(id,hashKey);
        return list;
    }
    //应答历史详情
    //id objectId
    @AuthPassport(validate = false)//游客权限可访问
    @ResponseBody
    @RequestMapping("/investigateAnswerHistoryDetails.do")
    public RespInvestigateSubject investigateAnswerHistoryDetails(int id) {
        RespInvestigateSubject respInvestigateSubject=publishService.investigateAnswerHistoryDetails(id);
        return respInvestigateSubject;
    }

    //调查分析首页
    //publish 调查id
    @ResponseBody
    @RequestMapping("/investigateAnswerStatistics.do")
    public RespAnswerStatistics investigateAnswerStatistics(int publish) {
        RespAnswerStatistics respAnswerStatistics=publishService.investigateAnswerStatistics(publish);
        return respAnswerStatistics;
    }
    //问题统计
    //publish 调查id
    //type 1问答,2单选,3多选
    //pageSize 每页条数 currentPageNo 当前页数
    //返回值  pageSize 每页条数 currentPageNo 当前页数  sumPage 总页数 totalResult 总条数
    @ResponseBody
    @RequestMapping("/investigateQuestionStatistics.do")
    public RespInvestigateQuestionStatistics investigateQuestionStatistics(int publish,PageInfo pageInfo,int type) {
        RespAnswerStatistics respAnswerStatistics=publishService.investigateAnswerStatistics(publish);
        publishService.updateInvestigateAnswerStatistics(publish);
        RespInvestigateQuestionStatistics respInvestigateQuestionStatistics=publishService.investigateQuestionStatistics(publish,type,pageInfo);
        respInvestigateQuestionStatistics.setEssayQuestionCount(respAnswerStatistics.getEssayQuestionCount());
        respInvestigateQuestionStatistics.setMultipleChoiceCount(respAnswerStatistics.getMultipleChoiceCount());
        respInvestigateQuestionStatistics.setSingleChoiceCount(respAnswerStatistics.getSingleChoiceCount());
        if(pageInfo.getTotalPage()==0)
            pageInfo.setTotalPage(1);
        respInvestigateQuestionStatistics.setPageInfo(pageInfo);
        return respInvestigateQuestionStatistics;
    }

    //解答题查看答案
    //publish 调查id
    //question 问题id
    //type 1普通问答题 3地址问答题，4 日期问答题（此处的type为，question的type为9时，special_model为3，是地址，为4，是日期，其他情况均目前传1）
    //pageSize 每页条数 currentPageNo 当前页数
    //返回值
    @ResponseBody
    @RequestMapping("/investigateAnswerListByQuestion.do")
    public RespShowQuestion investigateAnswerListByQuestion(int question,PageInfo pageInfo,Integer publish,Integer type) {
        RespShowQuestion respShowQuestion=publishService.investigateAnswerListByQuestion(publish,question,pageInfo,type);
        if(pageInfo.getTotalPage()==0)
            pageInfo.setTotalPage(1);
        respShowQuestion.setPageInfo(pageInfo);
        return respShowQuestion;
    }

    //问答题地址题详情
    //publish 调查id
    //question 问题id
    //type 3地址问答题，4 日期问答题 special_model为3，是地址，为4，是日期，其他情况均目前传1）
    //content 值 （比如日期是查询2024年的，此处传2024年，地址为天津市此处传天津市，如果地址为二级和平区，此处传 天津市 / 和平区）如果是查询全部，此处不传
    //pageSize 每页条数 currentPageNo 当前页数
    @ResponseBody
    @RequestMapping("/areaListByQuestion.do")
    public RespShowQuestion areaListByQuestion(int question,PageInfo pageInfo,Integer publish,Integer type,String content) {
        RespShowQuestion respShowQuestion=publishService.areaListByQuestion(publish,question,pageInfo,type,content);
        if(pageInfo.getTotalPage()==0)
            pageInfo.setTotalPage(1);
        respShowQuestion.setPageInfo(pageInfo);
        return respShowQuestion;
    }

    //解答题导出
    //publish 调查id
    //question 问题id
    //type 1基本问答题,2地址题,3时间题
    @ResponseBody
    @RequestMapping("/exportExcelEssayQuestion.do")
    public void exportExcelEssayQuestion(Integer publish,Integer question,Integer type, HttpServletResponse response) throws IOException {
        RespShowQuestion respShowQuestion=publishService.investigateAnswerListByQuestion(publish,question,null,type);
        publishService.exportExcelEssayQuestion(publish,question,type,respShowQuestion,null,response);
    }

    //问答题地址题详情
    //publish 调查id
    //question 问题id
    //type 3地址问答题，4 日期问答题 special_model为3，是地址，为4，是日期，其他情况均目前传1）
    //content 值 （比如日期是查询2024年的，此处传2024年，地址为天津市此处传天津市，如果地址为二级和平区，此处传 天津市 / 和平区）如果是查询全部，此处不传
    @ResponseBody
    @RequestMapping("/exportExcelAreaListByQuestion.do")
    public void exportExcelAreaListByQuestion(int question,PageInfo pageInfo,Integer publish,Integer type,String content, HttpServletResponse response) {
        RespShowQuestion respShowQuestion=publishService.areaListByQuestion(publish,question,pageInfo,type,content);
        publishService.exportExcelEssayQuestion(publish,question,1,respShowQuestion,content,response);
    }

    //多选题详情
    //publish 调查id
    //question 问题id
    @ResponseBody
    @RequestMapping("/answerListByMultipleChoiceQuestion.do")
    public RespMultipleChoiceQuestion answerListByMultipleChoiceQuestion(int question,int publish) {
        RespMultipleChoiceQuestion respMultipleChoiceQuestion=publishService.answerListByMultipleChoiceQuestion(publish,question);
        return respMultipleChoiceQuestion;
    }
    //题列表导出
    //publish 调查id
    //type 1问答,2单选,3多选
    @ResponseBody
    @RequestMapping("/exportExcelQuestionStatistics.do")
    public void exportExcelQuestionStatistics(Integer publish,Integer type, HttpServletResponse response) throws IOException {
        RespAnswerStatistics respAnswerStatistics=publishService.investigateAnswerStatistics(publish);
        RespInvestigateQuestionStatistics respInvestigateQuestionStatistics=publishService.investigateQuestionStatistics(publish,type,null);
        respInvestigateQuestionStatistics.setEssayQuestionCount(respAnswerStatistics.getEssayQuestionCount());
        respInvestigateQuestionStatistics.setMultipleChoiceCount(respAnswerStatistics.getMultipleChoiceCount());
        respInvestigateQuestionStatistics.setSingleChoiceCount(respAnswerStatistics.getSingleChoiceCount());
        publishService.exportExcelQuestionStatistics(publish,type,respInvestigateQuestionStatistics,response);
    }

    //多选题详情
    //publish 调查id
    //question 问题id
    @ResponseBody
    @RequestMapping("/exportExcelMultipleChoiceQuestion.do")
    public void exportExcelMultipleChoiceQuestion(int question,int publish, HttpServletResponse response) {
        RespMultipleChoiceQuestion respMultipleChoiceQuestion=publishService.answerListByMultipleChoiceQuestion(publish,question);
        publishService.exportExcelMultipleChoiceQuestion(publish,question,respMultipleChoiceQuestion,response);

    }
}
