package cn.sphd.miners.modules.material.service.impl;

import cn.sphd.miners.modules.material.dao.*;
import cn.sphd.miners.modules.material.entity.*;
import cn.sphd.miners.modules.material.service.SupplierImageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("supplierImageService")
public class SupplierImageServiceImpl implements SupplierImageService {
    @Autowired
    private PoContractBaseDao poContractBaseDao;
    @Autowired
    private SrmSupplierDao srmSupplierDao;
    @Autowired
    private PoContractBaseHistoryDao poContractBaseHistoryDao;
    @Autowired
    private PoContractImageDao poContractImageDao;
    @Autowired
    private PoContractImageHistoryDao poContractImageHistoryDao;
    @Autowired
    private SrmSupplierContactDao srmSupplierContactDao;
    @Autowired
    private SrmSupplierContactHistoryDao srmSupplierContactHistoryDao;
    @Autowired
    private SrmSupplierImageDao srmSupplierImageDao;
    @Autowired
    private SrmSupplierImageHistoryDao srmSupplierImageHistoryDao;
    @Override
    public PoContractBase getPoContractBase(Integer id) {
        return poContractBaseDao.get(id);
    }

    @Override
    public PoContractBaseHistory getPoContractBaseHistory(Integer id) {
        return poContractBaseHistoryDao.get(id);
    }

    @Override
    public SrmSupplier getSrmSupplier(Integer id) {
        return srmSupplierDao.get(id);
    }

    @Override
    public PoContractImage getPoContractImage(Integer id) {
        return poContractImageDao.get(id);
    }

    @Override
    public PoContractImageHistory getPoContractImageHistory(Integer id) {
        return poContractImageHistoryDao.get(id);
    }

    @Override
    public SrmSupplierContact getSrmSupplierContact(Integer id) {
        return srmSupplierContactDao.getSrmSupplierContactById(id);
    }

    @Override
    public SrmSupplierContactHistory getSrmSupplierContactHistory(Integer id) {
        return srmSupplierContactHistoryDao.get(id);
    }

    @Override
    public SrmSupplierImage getSrmSupplierImage(Integer id) {
        return srmSupplierImageDao.get(id);
    }

    @Override
    public SrmSupplierImageHistory getSrmSupplierImageHistory(Integer id) {
        return srmSupplierImageHistoryDao.get(id);
    }
}
