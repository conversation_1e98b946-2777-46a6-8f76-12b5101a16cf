package cn.sphd.miners.modules.accountantReportTax.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctReportDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctReportHistoryDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTaxDetailDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTaxPeriodDao;
import cn.sphd.miners.modules.accountantReportTax.dto.AcctReportDetailDto;
import cn.sphd.miners.modules.accountantReportTax.dto.AcctTaxDto;
import cn.sphd.miners.modules.accountantReportTax.entity.*;
import cn.sphd.miners.modules.finance.service.DataService;
import cn.sphd.miners.modules.system.entity.User;
import org.hibernate.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;

@Repository
public class AcctReportDaoImpl extends BaseDao<AcctReportEntity, Serializable> implements AcctReportDao {

    @Autowired
    AcctReportDao reportDao;
    @Autowired
    AcctTaxDetailDao detailDao;
    @Autowired
    AcctReportHistoryDao reportHistoryDao;
    @Autowired
    AcctTaxPeriodDao taxPeriodDao;
    @Autowired
    DataService dataService;

    /**
     * 不再申报的报表集合
     * */
    @Override
    public List<AcctReportHistoryEntity> getDisenabledReportByDate(User user, int year, int month) {
        String str = "" + year + "-" + String.format("%02d",month);

        String hql = "select " +
                "new AcctReportHistoryEntity(rh.id,rh.report,rh.name,rh.frequency,rh.createName,rh.createDate,ph) " +
                "from " +
                "AcctReportHistoryEntity rh," +
                "AcctTaxPeriodHistoryEntity ph " +
                "where " +
                " rh.id=ph.reportHistory and ph.org=:org and ph.beginMonth=:beginMonth and rh.enabled=0 and DATE_FORMAT(rh.enabledTime,'%Y-%m')<=:date";

        HashMap<String, Object> param = new HashMap<>();
        param.put("org", user.getOid());
        param.put("beginMonth", month);
        param.put("date", str);
        List<AcctReportHistoryEntity> list = reportDao.getListByHQLWithNamedParams(hql,param);
        return list;
    }

    /**
     * 根据年月查询报税记录表里的数据
     * */
    @Override
    public List<AcctReportDetailDto> getListFromDetail(User user, int year,int month) {
        String str = "" + year + "-" + String.format("%02d",month);
        Integer org = user.getOid();
        List<AcctReportDetailDto> list = new ArrayList<>();
        String hql = "from AcctTaxDetailEntity where org=:org and period=:period and tax=null and state=:state";
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", org);
        param.put("period", str);
        param.put("state", "1");
        List<AcctTaxDetailEntity> detaillist = detailDao.getListByHQLWithNamedParams(hql,param);//得到detail列表
        for (AcctTaxDetailEntity detailEntity : detaillist) {
            AcctReportDetailDto reportDetailDto = new AcctReportDetailDto();
            Integer reportHistoryId = detailEntity.getReportHistory();
            reportDetailDto.setReportHisId(reportHistoryId);
            reportDetailDto.setDetailId(detailEntity.getId());
            Integer reportId = detailEntity.getReport();
            AcctReportHistoryEntity reportEntityHistory = reportHistoryDao.get(reportHistoryId);
            reportDetailDto.setReportId(reportId);//报表ID
            reportDetailDto.setFactReportDate(detailEntity.getDelareTime());//实际报税时间
            reportDetailDto.setLatestDeclare(reportEntityHistory.getLatestDeclare());//最后编辑时间
            reportDetailDto.setCreateDate(detailEntity.getCreateDate());//数据创建时间
            reportDetailDto.setCreateName(detailEntity.getCreateName());
//            Integer hisreportId = detailEntity.getReportHistory();
//            AcctReportHistoryEntity historyEntity =  reportHistoryDao.get(hisreportId);
            if (reportEntityHistory != null) {
                reportDetailDto.setReportName(reportEntityHistory.getName());//报表名称
            }
            //查找period history的时间段
            String periodHisHql = "from AcctTaxPeriodHistoryEntity where org=:org and report=:report and reportHistory=:reportHistory and beginMonth=:beginMonth";
            HashMap<String, Object> periodParam = new HashMap<>();
            periodParam.put("org", org);
            periodParam.put("report", reportId);
            periodParam.put("reportHistory", reportHistoryId);
            periodParam.put("beginMonth", month);
            List<AcctTaxPeriodHistoryEntity> periodHislist = detailDao.getListByHQLWithNamedParams(periodHisHql,periodParam);
            String beginStr = "";
            String endStr = "";
            if (periodHislist.size() > 0) {
                AcctTaxPeriodHistoryEntity periodHistoryEntity = periodHislist.get(0);
                Integer beginMonth = periodHistoryEntity.getBeginMonth();
                Integer beginDate = periodHistoryEntity.getBeginDate();
                Integer endMonth = periodHistoryEntity.getEndMonth();
                Integer endDate = periodHistoryEntity.getEndDate();
                beginStr = "" + year + "-" + String.format("%02d",beginMonth) + "-" + String.format("%02d",beginDate);
                endStr = "" + year + "-" + String.format("%02d",endMonth) + "-" + String.format("%02d",endDate);
                reportDetailDto.setStartPeriod(beginStr);//应该开始日期
                reportDetailDto.setEndPeriod(endStr);//应报结束日期
            }
            //根据report ID查找税种列表tax list
            String taxHql = "select " +
                    "new cn.sphd.miners.modules.accountantReportTax.dto.AcctTaxDto(" +
                    "tax.tax,tax.id,tax.name,hr.name,td.id) " +
                    "from " +
                    "AcctTaxHistoryEntity tax," +
                    "AcctTaxDetailEntity td," +
                    "AcctReportHistoryEntity hr" +
                    " where " +
                    "tax.id=td.taxHistory AND " +
                    "td.reportHistory=hr.id and " +
                    "td.report=:report and td.period=:period order by td.id desc";

            HashMap<String, Object> taxParam = new HashMap<>();
            taxParam.put("report", reportId);
            taxParam.put("period", str);
//            if (reportId == 39) {
//                System.out.println(11);
//            }
            List<AcctTaxDto> taxDtolist = detailDao.getListByHQLWithNamedParams(taxHql,taxParam);
//            for (AcctTaxDto taxDto : taxDtolist) {
//                taxDto.setStartPeriod(beginStr);
//                taxDto.setEndPeriod(endStr);
//            }
            Integer tax = 0;
            if (taxDtolist.size() > 0) {
                for(int i=0;i<=taxDtolist.size()-1;i++) {
                    AcctTaxDto taxDto = taxDtolist.get(i);
                    Integer taxId = taxDto.getTaxId();
                    for (int j=i+1;j<taxDtolist.size();j++) {
                        AcctTaxDto taxDtoJ = taxDtolist.get(j);
                        Integer taxIdJ = taxDtoJ.getTaxId();
                        if (taxId.compareTo(taxIdJ) == 0) {
                            taxDtolist.remove(j);
                            j--;
                        }
                    }
                    taxDto.setStartPeriod(beginStr);
                    taxDto.setEndPeriod(endStr);
                }
            }

            for (AcctTaxDto taxDto : taxDtolist) {
                Integer detailId = taxDto.getDetailId();
                AcctTaxDetailEntity detailEntity1 = detailDao.get(detailId);
                if (detailEntity1 != null) {
                    //String createName,Date createDate,Date factReportDate,BigDecimal planAmount,BigDecimal factAmount

                    setTaxDtoFromTaxDetail(detailEntity1,taxDto,user);

                }
            }

            reportDetailDto.setTaxList(taxDtolist);//税种列表
            list.add(reportDetailDto);
        }
        return list;
    }

    private void setTaxDtoFromTaxDetail(AcctTaxDetailEntity detailEntity1,AcctTaxDto taxDto,User user) {
        String createName = detailEntity1.getCreateName();
        Date createDate = detailEntity1.getCreateDate();
        Date factReportDate = detailEntity1.getDelareTime();
        BigDecimal planAmount = detailEntity1.getPlanAmount();
        BigDecimal factAmount = detailEntity1.getFactAmount();
        Integer accountDetail = detailEntity1.getAccountDetail();
        Date updateDate = detailEntity1.getUpdateDate();
        String updateName = detailEntity1.getUpdateName();
        String belongsBegin = detailEntity1.getBelongsBegin();
        String belongsEnd = detailEntity1.getBelongsEnd();
        Pattern pattern = Pattern.compile("(\\S{4})(\\S{2})(\\S{2})");
        if (belongsBegin != null)
            belongsBegin = pattern.matcher(belongsBegin).replaceAll("$1-$2-$3");
        if (belongsEnd != null)
            belongsEnd = pattern.matcher(belongsEnd).replaceAll("$1-$2-$3");
        Date payTime = detailEntity1.getPayTime();
        Map<String,Object> map = dataService.getTaxMethod(user,accountDetail);
        String method = (String) map.get("method");//0-初始资金 1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐，6-存现金，7-取现金，8-其他内部转账

        taxDto.setCreateDate(createDate);
        taxDto.setCreateName(createName);
        taxDto.setFactReportDate(factReportDate);
        taxDto.setPlanAmount(planAmount);
        taxDto.setFactAmount(factAmount);
        taxDto.setUpdateDate(updateDate);
        taxDto.setUpdateName(updateName);
        taxDto.setBelongsBegin(belongsBegin);
        taxDto.setBelongsEnd(belongsEnd);
        taxDto.setMethod(method);
        taxDto.setPayTime(payTime);
    }

    /**
     * 根据年月查询所有符合的报表
     * */
    @Override
    public Map<String,Object> getByPeriod(User user, int year,int month) {
        String str = "" + year + "-" + String.format("%02d",month);

        String hql = "select report from AcctTaxPeriodEntity period,AcctReportEntity report where " +
                "period.org=:org and period.report=report.id and DATE_FORMAT(report.createDate,'%Y-%m')<=:period and period.beginMonth=:beginMonth";
        Integer org = user.getOid();
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", org);
        param.put("period", str);
        param.put("beginMonth", month);
        List<AcctReportEntity> list = reportDao.getListByHQLWithNamedParams(hql,param);
        List<AcctReportDetailDto> result = new ArrayList<>();
        BigDecimal totalFactAmount = BigDecimal.ZERO;
        Map<String,Object> res = new HashMap<>();
        for (AcctReportEntity reportEntity : list) {
            AcctReportDetailDto reportDetailDto = new AcctReportDetailDto();
            Integer rid = reportEntity.getId();
            reportDetailDto.setReportId(rid);//报表ID
            reportDetailDto.setReportName(reportEntity.getName());//报表名称
            AcctReportHistoryEntity reportHistory = reportHistoryDao.getLastByReportId(rid);//获取最新的历史报表
            reportDetailDto.setReportHisId(reportHistory.getId());
            String periodHql = "from AcctTaxPeriodEntity where report=:report and beginMonth=:beginMonth";
            HashMap<String, Object> periodParam = new HashMap<>();
            periodParam.put("report", rid);
            periodParam.put("beginMonth", month);
            List<AcctTaxPeriodEntity> periodlist = reportDao.getListByHQLWithNamedParams(periodHql,periodParam);
            String beginStr = "";
            String endStr = "";
            if (periodlist.size() > 0) {
                AcctTaxPeriodEntity peiordEntity = periodlist.get(0);
                Integer beginMonth = peiordEntity.getBeginMonth();
                Integer beginDate = peiordEntity.getBeginDate();
                Integer endMonth = peiordEntity.getEndMonth();
                Integer endDate = peiordEntity.getEndDate();
                beginStr = "" + year + "-" + String.format("%02d",beginMonth) + "-" + String.format("%02d",beginDate);
                endStr = "" + year + "-" + String.format("%02d",endMonth) + "-" + String.format("%02d",endDate);
                reportDetailDto.setStartPeriod(beginStr);//应报开始时间
                reportDetailDto.setEndPeriod(endStr);//应报结束时间
            }

            String taxHql = "select new cn.sphd.miners.modules.accountantReportTax.dto.AcctTaxDto(" +
                            "tax.id,tax.name,r.name,r.latestDeclare) from " +
                    "AcctTaxEntity tax," +
                    "AcctTaxReportEntity taxreport," +
                    "AcctReportEntity r where taxreport.tax=tax.id and taxreport.report=r.id and tax.enabled=1 and taxreport.report=:report";

            HashMap<String, Object> taxDtoParam = new HashMap<>();
            taxDtoParam.put("report", rid);
            List<AcctTaxDto> taxDtolist = detailDao.getListByHQLWithNamedParams(taxHql,taxDtoParam);
            for (AcctTaxDto taxDto : taxDtolist) {
                Integer taxId = taxDto.getTaxId();
                taxDto.setStartPeriod(beginStr);
                taxDto.setEndPeriod(endStr);
                //给taxhis 赋值
                String taxhisHql = " from AcctTaxHistoryEntity where tax=" + taxId + " order by createDate desc";
                Query query = getSession().createQuery(taxhisHql);
                query.setMaxResults(1);
                List<AcctTaxHistoryEntity> taxHislist = query.list();
                AcctTaxHistoryEntity taxHistoryEntity = taxHislist.get(0);
                taxDto.setTaxHisId(taxHistoryEntity.getId());
                //查询税款录入的信息
                String factAmountHql = "from AcctTaxDetailEntity where org=:org and period=:period and tax=:tax and report=:report";
                HashMap<String, Object> factAmountParam = new HashMap<>();
                factAmountParam.put("org", org);
                factAmountParam.put("period", str);
                factAmountParam.put("tax", taxId);
                factAmountParam.put("report", rid);
                List<AcctTaxDetailEntity> detailList = detailDao.getListByHQLWithNamedParams(factAmountHql,factAmountParam);
                if (detailList.size() > 0) {
                    //报过税
                    AcctTaxDetailEntity detail = detailList.get(0);
                    taxDto.setFactAmount(detail.getFactAmount());
                    taxDto.setAccountDetail(detail.getAccountDetail());
                    BigDecimal factAmount = detail.getFactAmount() == null ? BigDecimal.ZERO : detail.getFactAmount();
                    totalFactAmount = totalFactAmount.add(factAmount);
                    setTaxDtoFromTaxDetail(detail,taxDto,user);
                }
                else {
                    //没报过税，去state-0的值
                    String hql2 = "from AcctTaxDetailEntity where report=:report and state=:state and tax=:tax and org=:org";
                    HashMap<String, Object> param2 = new HashMap<>();
                    param2.put("report",rid);
                    param2.put("state","0");
                    param2.put("tax",taxId);
                    param2.put("org",org);
                    List<AcctTaxDetailEntity> listDetail = detailDao.getListByHQLWithNamedParams(hql2,param2);
                    if (listDetail.size() > 0) {
                        AcctTaxDetailEntity detailEntity = listDetail.get(0);
                        setTaxDtoFromTaxDetail(detailEntity,taxDto,user);
                    }

                }

            }
            reportDetailDto.setTaxList(taxDtolist);//税种列表
            result.add(reportDetailDto);
        }
        res.put("list",result);
        res.put("totalFactAmount",totalFactAmount);
        return res;
    }

    @Override
    public boolean checkReportName(String name, Integer org) {
        String hql = "from AcctReportEntity where org=:org and name=:name";
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", org);
        param.put("name", name);
        List<AcctReportEntity> list = reportDao.getListByHQLWithNamedParams(hql,param);
        if (list.size() == 0)
            return true;
        else return false;
    }

    @Override
    public AcctReportEntity getReportDetial(User user, int reportId) {
//        String hql = "from AcctReportEntity where id=:id";
        AcctReportEntity reportEntity = reportDao.get(reportId);
        if (reportEntity != null) {
            String getTaxsHql = "select tax from AcctTaxEntity tax,AcctTaxReportEntity taxreport where " +
                    "tax.org=:org and tax.id=taxreport.tax and taxreport.report=:report";
            HashMap<String, Object> param = new HashMap<>();
            param.put("org",user.getOid());
            param.put("report", reportId);

            List<AcctTaxEntity> taxList = taxPeriodDao.getListByHQLWithNamedParams(getTaxsHql,param);
            reportEntity.setTaxList(taxList);//获取该报表对应的tax集合

//            String getPeriodsHql = "select period from AcctTaxPeriodEntity period,AcctReportEntity report where " +
//                    "period.org=:org and period.report=:report";
            String getPeriodsHql = "from AcctTaxPeriodEntity where org=:org and report=:report";
//            param.put("report", reportId);
//            param.put("org", item.getOrg());
            List<AcctTaxPeriodEntity> periodList = taxPeriodDao.getListByHQLWithNamedParams(getPeriodsHql,param);
            reportEntity.setPeriodList(periodList);//获取该报表对应的period集合

        }
        return reportEntity;
    }
}
