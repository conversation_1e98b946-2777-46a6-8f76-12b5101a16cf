package cn.sphd.miners.modules.investigate.dto;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.investigate.entity.*;

import java.util.List;

public class RespShowQuestion {
    private Integer answerSum;//回答本题总份数
    private Integer objSum;//回收总份数
    private Integer contentSum;//content时，回答题数量
    private List<RespInvestigatePublishShow> respInvestigatePublishShowList;
    private List<InvestigatePublishShow> investigatePublishShowList;
    private List<RespInvestigateObject> respInvestigateObjectList;
    private  Integer level;

    private InvestigateQuestion investigateQuestion;

    public Integer getContentSum() {
        return contentSum;
    }

    public void setContentSum(Integer contentSum) {
        this.contentSum = contentSum;
    }

    public InvestigateQuestion getInvestigateQuestion() {
        return investigateQuestion;
    }

    public void setInvestigateQuestion(InvestigateQuestion investigateQuestion) {
        this.investigateQuestion = investigateQuestion;
    }

    public Integer getObjSum() {
        return objSum;
    }

    public void setObjSum(Integer objSum) {
        this.objSum = objSum;
    }

    private List<RespInvestigateArea> investigateAreaList;

    public List<RespInvestigateArea> getInvestigateAreaList() {
        return investigateAreaList;
    }

    public void setInvestigateAreaList(List<RespInvestigateArea> investigateAreaList) {
        this.investigateAreaList = investigateAreaList;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    private PageInfo pageInfo;

    public Integer getAnswerSum() {
        return answerSum;
    }

    public void setAnswerSum(Integer answerSum) {
        this.answerSum = answerSum;
    }

    public List<RespInvestigatePublishShow> getRespInvestigatePublishShowList() {
        return respInvestigatePublishShowList;
    }

    public void setRespInvestigatePublishShowList(List<RespInvestigatePublishShow> respInvestigatePublishShowList) {
        this.respInvestigatePublishShowList = respInvestigatePublishShowList;
    }

    public List<InvestigatePublishShow> getInvestigatePublishShowList() {
        return investigatePublishShowList;
    }

    public void setInvestigatePublishShowList(List<InvestigatePublishShow> investigatePublishShowList) {
        this.investigatePublishShowList = investigatePublishShowList;
    }

    public List<RespInvestigateObject> getRespInvestigateObjectList() {
        return respInvestigateObjectList;
    }

    public void setRespInvestigateObjectList(List<RespInvestigateObject> respInvestigateObjectList) {
        this.respInvestigateObjectList = respInvestigateObjectList;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }
}
