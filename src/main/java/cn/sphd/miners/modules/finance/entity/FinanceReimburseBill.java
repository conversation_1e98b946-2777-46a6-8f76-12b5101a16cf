package cn.sphd.miners.modules.finance.entity;

import cn.sphd.miners.modules.personal.entity.PersonnelReimburse;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

/**
 * Created by Administrator on 2017/11/29.
 * 财务报销票据表
 */
@Entity
@Table(name = "t_finance_reimburse_bill")
public class FinanceReimburseBill implements Serializable{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "reimburse" ,insertable = true)
    private Integer reimburseId;//报销id

    @Column(name="amount"   , nullable=true , unique=false)
    private BigDecimal amount;//实际金额

    @Column(name="bill_amount"   , nullable=true , unique=false)
    private BigDecimal billAmount;//票面金额(含税)

    @Column(name="price"   , nullable=true , unique=false)
    private BigDecimal price;//税前价格

    @Column(name="tax_amount"   , nullable=true , unique=false)
    private BigDecimal taxAmount;//税额

    @Column(name="issue_date"   , nullable=true , unique=false)
    private Date issueDate;//开票日期

    @Column(name="fee_cat"  ,  nullable=true , unique=false)
    private Integer feeCat;//费用类别 (字典表id)

    @Column(name="bill_cat"  , nullable=true , unique=false)
    private Integer billCat;//票据类型 (字典表id)                                                                                                                                                                          ,参见数据字典表定义

    @Column(name = "item_quantity" ,insertable = true)
    private Integer itemQuantity;//明细数量

    @Column(name = "bill_period" ,length = 1)
    private String billPeriod;//1-本月票据,2-非本月票据

    @Column(name = "bill_code" ,length = 255)
    private String billCode;//发票代码

    @Column(name = "bill_no" ,length = 255)
    private String billNo;//发票号码

    @Column(name = "memo" ,length = 255)
    private String memo;//备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="approve_Item"   , nullable=true , unique=false)
    private Integer approveItem;//申批项目

    @Column(name="approve_status"  , length=1 , nullable=true , unique=false)
    private String approveStatus;//审批状态：0-未申请 ，1-申请提交，2-通过审核，3-否决审核

    @Column(name="approve_level"   , nullable=true , unique=false)
    private Integer approveLevel;//审批次级

    @Column(name="auditor"   , nullable=true , unique=false)
    private Integer auditor;//审批者ID(认证的)

    @Column(name="auditor_name"  , length=100 , nullable=true , unique=false)
    private String auditorName;//审批者(认证的)

    @Column(name="audit_date"   , nullable=true , unique=false)
    private Date auditDate;//审批日期(认证的)

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;//操作：1-增，2—删，3-—改

    @Column(name="apply_memo"  , length=255 , nullable=true , unique=false)
    private String applyMemo;//申请备注

    @Column(name="approve_memo"  , length=255 , nullable=true , unique=false)
    private String approveMemo;//审批备注

    @Column(name="message_id"   , nullable=true , unique=false)
    private Integer messageId;//消息ID

    @Column(name = "certification_state",length = 1,nullable = true,unique = false)
    private String certificationState;//认证状态 0-未认证 1-认证通过，2-认证失败（认证未通过，不抵扣，仅报销），3-无需认证

    @Column(name="item_amount"   , nullable=true , unique=false)
    private BigDecimal itemAmount;//单张票据金额

    @Column(name="primary_bill"   , nullable=true , unique=false)
    private Integer primaryBill=0;//主票号码ID,多张票录入时用

    @Column(name="relative_bill_quantity"   , nullable=true , unique=false)
    private Integer relativeBillQuantity;//相同票据数量

    @Column(name="relative_bill_no"  , length=255 , nullable=true , unique=false)
    private String relativeBillNo;//相同票据号码,多个以逗号分隔

    @Column(name="subject"  , length=16 , nullable=true , unique=false)
    private String subject;//科目代码

    @JsonIgnore @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="reimburse", referencedColumnName = "id" , nullable=true , unique=false , insertable=false, updatable=false)
    private PersonnelReimburse reimburse;

    //报销票据入库明细表
    @JsonIgnore @JSONField(serialize = false)
    @OneToMany(targetEntity=FinanceReimburseBillItem.class, fetch= FetchType.LAZY, mappedBy="reimburseBill", cascade= CascadeType.REMOVE)
    private Set<FinanceReimburseBillItem> financeReimburseBillItemHashSet = new HashSet<FinanceReimburseBillItem>();

    //报销票据附件表
    @JsonIgnore @JSONField(serialize = false)
    @OneToMany(targetEntity=FinanceReimburseBillAttachment.class, fetch= FetchType.LAZY, mappedBy="financeReimburseBill", cascade= CascadeType.REMOVE)
    private Set<FinanceReimburseBillAttachment> financeReimburseBillAttachmentHashSet = new HashSet<FinanceReimburseBillAttachment>();

    @Column(name = "fee_cat_name", length = 255, nullable=true , unique=false)
    private String feeCatName;  //费用类别名称

    @Column(name = "bill_cat_name", length = 255, nullable=true , unique=false)
    private String billCatName;  //票据种类名称

    @Column(name="item_count"   , nullable=true , unique=false)
    private Integer itemCount;  //是否是一行内容  1-是  0-否

    @Column(name="qr_info"  ,length = 100, nullable=true , unique=false)
    private String qrInfo;  //发票信息:扫二维码取的所有信息

    @Transient
    private List<FinanceReimburseBillItem> financeReimburseBillItemList = new ArrayList<>();  //list存入票据具体的货物

    @Transient
    private List<FinanceReimburseBillAttachment> pictures = new ArrayList<>();  //list报销票据附件表

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getReimburseId() {
        return reimburseId;
    }

    public void setReimburseId(Integer reimburseId) {
        this.reimburseId = reimburseId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getBillAmount() {
        return billAmount;
    }

    public void setBillAmount(BigDecimal billAmount) {
        this.billAmount = billAmount;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public Date getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(Date issueDate) {
        this.issueDate = issueDate;
    }

    public Integer getBillCat() {
        return billCat;
    }

    public void setBillCat(Integer billCat) {
        this.billCat = billCat;
    }

    public Integer getItemQuantity() {
        return itemQuantity;
    }

    public void setItemQuantity(Integer itemQuantity) {
        this.itemQuantity = itemQuantity;
    }

    public String getBillPeriod() {
        return billPeriod;
    }

    public void setBillPeriod(String billPeriod) {
        this.billPeriod = billPeriod;
    }

    public String getBillCode() {
        return billCode;
    }

    public void setBillCode(String billCode) {
        this.billCode = billCode;
    }

    public String getBillNo() {
        return billNo;
    }

    public void setBillNo(String billNo) {
        this.billNo = billNo;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    public PersonnelReimburse getReimburse() {
        return reimburse;
    }

    public void setReimburse(PersonnelReimburse reimburse) {
        this.reimburse = reimburse;
    }

    public Set<FinanceReimburseBillItem> getFinanceReimburseBillItemHashSet() {
        return financeReimburseBillItemHashSet;
    }

    public void setFinanceReimburseBillItemHashSet(Set<FinanceReimburseBillItem> financeReimburseBillItemHashSet) {
        this.financeReimburseBillItemHashSet = financeReimburseBillItemHashSet;
    }

    public String getCertificationState() {
        return certificationState;
    }

    public void setCertificationState(String certificationState) {
        this.certificationState = certificationState;
    }

    public BigDecimal getItemAmount() {
        return itemAmount;
    }

    public void setItemAmount(BigDecimal itemAmount) {
        this.itemAmount = itemAmount;
    }

    public Integer getPrimaryBill() {
        return primaryBill;
    }

    public void setPrimaryBill(Integer primaryBill) {
        this.primaryBill = primaryBill;
    }

    public Integer getRelativeBillQuantity() {
        return relativeBillQuantity;
    }

    public void setRelativeBillQuantity(Integer relativeBillQuantity) {
        this.relativeBillQuantity = relativeBillQuantity;
    }

    public String getRelativeBillNo() {
        return relativeBillNo;
    }

    public void setRelativeBillNo(String relativeBillNo) {
        this.relativeBillNo = relativeBillNo;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public Integer getFeeCat() {
        return feeCat;
    }

    public void setFeeCat(Integer feeCat) {
        this.feeCat = feeCat;
    }

    public String getFeeCatName() {
        return feeCatName;
    }

    public void setFeeCatName(String feeCatName) {
        this.feeCatName = feeCatName;
    }

    public String getBillCatName() {
        return billCatName;
    }

    public void setBillCatName(String billCatName) {
        this.billCatName = billCatName;
    }

    public Set<FinanceReimburseBillAttachment> getFinanceReimburseBillAttachmentHashSet() {
        return financeReimburseBillAttachmentHashSet;
    }

    public void setFinanceReimburseBillAttachmentHashSet(Set<FinanceReimburseBillAttachment> financeReimburseBillAttachmentHashSet) {
        this.financeReimburseBillAttachmentHashSet = financeReimburseBillAttachmentHashSet;
    }

    public List<FinanceReimburseBillItem> getFinanceReimburseBillItemList() {
        return financeReimburseBillItemList;
    }

    public void setFinanceReimburseBillItemList(List<FinanceReimburseBillItem> financeReimburseBillItemList) {
        this.financeReimburseBillItemList = financeReimburseBillItemList;
    }

    public List<FinanceReimburseBillAttachment> getPictures() {
        return pictures;
    }

    public void setPictures(List<FinanceReimburseBillAttachment> pictures) {
        this.pictures = pictures;
    }

    public Integer getItemCount() {
        return itemCount;
    }

    public void setItemCount(Integer itemCount) {
        this.itemCount = itemCount;
    }

    public String getQrInfo() {
        return qrInfo;
    }

    public void setQrInfo(String qrInfo) {
        this.qrInfo = qrInfo;
    }
}
