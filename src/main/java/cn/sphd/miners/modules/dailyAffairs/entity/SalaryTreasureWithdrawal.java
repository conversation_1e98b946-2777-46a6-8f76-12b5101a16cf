package cn.sphd.miners.modules.dailyAffairs.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by Administrator on 2020/09/23
 * 2.68/2.69薪资宝转出
 */
@Entity
@Table(name = "t_salary_treasure_withdrawal")
public class SalaryTreasureWithdrawal implements Serializable{

    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "org" ,insertable = true)
    private Integer org;//机构id

    @Column(name = "user" ,insertable = true)
    private Integer user;//用户ID

    @Column(name = "instance" ,insertable = true)
    private Integer instance;//申请实例ID

    @Column(name = "business_no",nullable = true,unique = false)
    private Integer businessNo;//业务号(如薪薪宝的业务流水号)

    @Column(name = "instance_chain",length = 255,nullable = true,unique = false)
    private String instanceChain;//申请实例ID链,以逗号分隔

    @Column(name = "purpose",length = 255,nullable = true,unique = false)
    private String purpose;//理由

    @Column(name="amount"   , nullable=true , unique=false)
    private BigDecimal amount;   //申请金额

    @Column(name = "memo",length = 255,nullable = true,unique = false)
    private String memo;//备注

    @Column(name="plan_date"   , nullable=true , unique=false)
    private Date planDate;//计划支出日期

    @Column(name="plan_amount"   , nullable=true , unique=false)
    private BigDecimal planAmount;   //计划金额

    @Column(name = "plan_pay_method",length = 1,nullable = true,unique = false)
    private String planPayMethod;//计划支付方式:1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐

    @Column(name = "plan_account",length = 255,nullable = true,unique = false)
    private String planAccount;//'计划银行帐号'

    @Column(name = "account_detail" ,insertable = true)
    private Integer accountDetail;//帐户明细ID

    @Column(name="fact_pay_date"   , nullable=true , unique=false)
    private Date factPayDate;//实际支出时间

    @Column(name="fact_amount"   , nullable=true , unique=false)
    private BigDecimal factAmount;   //实际金额

    @Column(name = "fact_pay_method",length = 1,nullable = true,unique = false)
    private String factPayMethod;//实际支付方式:1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐

    @Column(name = "fact_account",length = 255,nullable = true,unique = false)
    private String factAccount;//'实际银行帐号'

    @Column(name = "summary",length = 255,nullable = true,unique = false)
    private String summary;//'摘要

    @Column(name="status"  , length=1 , nullable=true , unique=false)
    private String status;  //状态:0-撤回,1-待申请,2-申请,3-申请通过(付款审批),4-申请驳回,5-付款方式确认 6-付款复核通过,7-付款方式修改,8-付款修改复核通过,9-付款完成

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;  //'创建人id'

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;   //'创建人'

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;   //'创建时间'

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;   //'修改人id'

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;   //'修改人'

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;    //'修改时间'

    @Column(name = "message_id" ,insertable = true)
    private Integer messageId;//对应消息表的id

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;//操作:1-增,2-删,3-修改

    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId;  //修改前记录ID

    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo;  //版本号,每次修改+1

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getUser() {
        return user;
    }

    public void setUser(Integer user) {
        this.user = user;
    }

    public Integer getInstance() {
        return instance;
    }

    public void setInstance(Integer instance) {
        this.instance = instance;
    }

    public String getInstanceChain() {
        return instanceChain;
    }

    public void setInstanceChain(String instanceChain) {
        this.instanceChain = instanceChain;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getPlanAmount() {
        return planAmount;
    }

    public void setPlanAmount(BigDecimal planAmount) {
        this.planAmount = planAmount;
    }

    public String getPlanPayMethod() {
        return planPayMethod;
    }

    public void setPlanPayMethod(String planPayMethod) {
        this.planPayMethod = planPayMethod;
    }

    public String getPlanAccount() {
        return planAccount;
    }

    public void setPlanAccount(String planAccount) {
        this.planAccount = planAccount;
    }

    public Integer getAccountDetail() {
        return accountDetail;
    }

    public void setAccountDetail(Integer accountDetail) {
        this.accountDetail = accountDetail;
    }

    public Date getFactPayDate() {
        return factPayDate;
    }

    public void setFactPayDate(Date factPayDate) {
        this.factPayDate = factPayDate;
    }

    public BigDecimal getFactAmount() {
        return factAmount;
    }

    public void setFactAmount(BigDecimal factAmount) {
        this.factAmount = factAmount;
    }

    public String getFactPayMethod() {
        return factPayMethod;
    }

    public void setFactPayMethod(String factPayMethod) {
        this.factPayMethod = factPayMethod;
    }

    public String getFactAccount() {
        return factAccount;
    }

    public void setFactAccount(String factAccount) {
        this.factAccount = factAccount;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    public Date getPlanDate() {
        return planDate;
    }

    public void setPlanDate(Date planDate) {
        this.planDate = planDate;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public Integer getBusinessNo() {
        return businessNo;
    }

    public void setBusinessNo(Integer businessNo) {
        this.businessNo = businessNo;
    }
}
