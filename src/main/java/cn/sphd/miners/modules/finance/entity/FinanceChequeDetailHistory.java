package cn.sphd.miners.modules.finance.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by Administrator on 2019/1/18.
 */
@Entity
@Table(name = "t_finance_cheque_detail_history")
public class FinanceChequeDetailHistory implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="cheque_detail"   , nullable=true , unique=false)
    private Integer chequeDetail;//支票明细ID

    @Column(name="cheque_no"  , length=50 , nullable=true , unique=false)
    private String chequeNo;//支票号码

    @Column(name="cheque_reg"   , nullable=true , unique=false)
    private Integer chequeReg;//支票登记ID

    @Column(name="account_id"  , nullable=true , unique=false)
    private Integer accountId;

    @Column(name="type"  , length=1 , nullable=true , unique=false)
    private String type;//1-转帐支票，2-现金汇票

    @Column(name="account"  , length=100 , nullable=true , unique=false)
    private String account;

    @Column(name="bank_no"  , length=100 , nullable=true , unique=false)
    private String bankNo;

    @Column(name="bank_name"  , length=100 , nullable=true , unique=false)
    private String bankName;//银行名称

    @Column(name="category"  , length=1 , nullable=true , unique=false)
    private String category;//种类:1-采购,2-报销

    @Column(name="state"  , length=1 , nullable=true , unique=false)
    private String state;//空或1-未使用,2-已使用,3-作废

    @Column(name="amount"   , nullable=true , unique=false)
    private BigDecimal amount;//金额

    @Column(name="summary"  , length=255 , nullable=true , unique=false)
    private String summary;//摘要

    @Column(name="purpose"  , length=255 , nullable=true , unique=false)
    private String purpose;//用途

    @Column(name="expire_date"   , nullable=true , unique=false)
    private Date expireDate;  //到期日期

    @Column(name="operate_date"   , nullable=true , unique=false)
    private Date operateDate;  //发生日期

    @Column(name="receive_corp"  , length=100 , nullable=true , unique=false)
    private String receiveCorp;//接收公司

    @Column(name="receive_date"   , nullable=true , unique=false)
    private Date receiveDate;  //接收日期

    @Column(name="receiver"  , length=100 , nullable=true , unique=false)
    private String receiver;//接收经手人

    @Column(name="operator"  , length=100 , nullable=true , unique=false)
    private String operator;//支付经手人

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;//说明

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateDate;

    @Column(name="approve_Item"   , nullable=true , unique=false)
    private Integer approveItem;

    @Column(name="approve_status"  , length=1 , nullable=true , unique=false)
    private String approveStatus;

    @Column(name="approve_level"   , nullable=true , unique=false)
    private Integer approveLevel;

    @Column(name="auditor"   , nullable=true , unique=false)
    private Integer auditor;

    @Column(name="auditor_name"  , length=100 , nullable=true , unique=false)
    private String auditorName;

    @Column(name="audit_date"   , nullable=true , unique=false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date auditDate;

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation; //操作：1-增(开票录入),2-删,3-改,4-作废,5-改为尚未开具(启用)

    @Column(name="apply_memo"  , length=255 , nullable=true , unique=false)
    private String applyMemo;

    @Column(name="approve_memo"  , length=255 , nullable=true , unique=false)
    private String approveMemo;

    @Column(name="message_id"   , nullable=true , unique=false)
    private Integer messageId;

    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId;//修改前记录ID

    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo;//版本号,每次修改+1


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getChequeDetail() {
        return chequeDetail;
    }

    public void setChequeDetail(Integer chequeDetail) {
        this.chequeDetail = chequeDetail;
    }

    public String getChequeNo() {
        return chequeNo;
    }

    public void setChequeNo(String chequeNo) {
        this.chequeNo = chequeNo;
    }

    public Integer getChequeReg() {
        return chequeReg;
    }

    public void setChequeReg(Integer chequeReg) {
        this.chequeReg = chequeReg;
    }

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getBankNo() {
        return bankNo;
    }

    public void setBankNo(String bankNo) {
        this.bankNo = bankNo;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    public Date getOperateDate() {
        return operateDate;
    }

    public void setOperateDate(Date operateDate) {
        this.operateDate = operateDate;
    }

    public String getReceiveCorp() {
        return receiveCorp;
    }

    public void setReceiveCorp(String receiveCorp) {
        this.receiveCorp = receiveCorp;
    }

    public Date getReceiveDate() {
        return receiveDate;
    }

    public void setReceiveDate(Date receiveDate) {
        this.receiveDate = receiveDate;
    }

    public String getReceiver() {
        return receiver;
    }

    public void setReceiver(String receiver) {
        this.receiver = receiver;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
}
