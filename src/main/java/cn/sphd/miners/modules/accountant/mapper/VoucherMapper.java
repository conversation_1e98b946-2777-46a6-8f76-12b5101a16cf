package cn.sphd.miners.modules.accountant.mapper;


import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.accountant.entity.TAccountantVoucher;
import cn.sphd.miners.modules.accountant.entity.TAccountantVoucherDto;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
@Component
public interface VoucherMapper extends BaseMapper<TAccountantVoucher> {

    /*从原凭证表中得到凭证*/
    List<TAccountantVoucher> getVouchersFormOriginal(TAccountantVoucher param);

    /*根据voucher字段得到和他值一样的凭证，如果有冲洪的化会找到三个凭证*/
    List<TAccountantVoucher> getVouchersByVoucherField(int voucherID);

    /*得到冲洪的凭证和原凭证*/
    List<TAccountantVoucher> getRedVoucher(QueryData qd);

    /*凭证冲洪之后把type更新成2*/
    int updateVoucherAfterRed(QueryData qd);

    /*联表更新科目和详情表*/
    int updateOriginalSubjectAndDetail(Map param);

    /*点击结帐按钮时把上个月标记为已结帐*/
    int updateSettle(TAccountantVoucher param);

    /*得到所有会计录入的凭证*/
    List<TAccountantVoucher> getInPutVouchers(TAccountantVoucher param);

    /**/
    List<TAccountantVoucher> getSettledVouchers(TAccountantVoucher param);

    List<TAccountantVoucher> getApproveVouchers(TAccountantVoucher param);

    //把没有进行操作的凭证转为上月凭证
    //List<TAccountantVoucher> listByUpdatorNull();

    //模糊查找某个月的凭证
    List<TAccountantVoucher> likeVoucher(TAccountantVoucher param);

    //得到所有凭证
    List<TAccountantVoucher> getAllVoucher(TAccountantVoucher param);

    //获取凭证表中的某期结转的凭证
    TAccountantVoucher getCarryOverVoucher(TAccountantVoucher param);

    //月初把上月凭证变为本月凭证
    int updateVoucherByIsSettled();

    List<TAccountantVoucher> getOrgVouchers(int org);

    void updateVoucherByDate(TAccountantVoucher param);

    List<TAccountantVoucherDto> getVoucherBySource(TAccountantVoucher param);

    List<TAccountantVoucherDto> getVoucherBySearcherCon(TAccountantVoucherDto param);

    TAccountantVoucherDto getDtoById(Integer voucherId);
}