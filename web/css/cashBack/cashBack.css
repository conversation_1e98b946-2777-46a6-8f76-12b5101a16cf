/*回款*/
.dividePane{margin-left:100px;width: 650px;}
.dividePane div{padding: 30px 0; overflow: hidden;}
#rebackBtn{margin-left:20px;}
#addInputer{margin-left:10%;}
.inputerTip{padding:8px 0 8px 10px;width:700px;color:#fff;background: #31869b;}
.moneyBack{width:1000px;}
#addPayInputer .fa{color:#48cfad;}
#addPayInputer .bonceCon{max-height:500px;overflow-y: auto;}
.checkWrap{margin:14px 10px;}
.checkWrap div{}
.checkWrap div span{margin-right:15px;}
.selectBtn{background: #31bfb1;}
.byAll,.byDepart{display:none;}
.byAll div{width: 280px; padding: 4px 25px; background: #fde9d9;}
.byDepart span{display: inline-block;}
.byDepart ul li{width:100px;padding:4px 10px;background:#fde9d9;}
.stuff{width: 220px; padding: 4px 14px; background: #fde9d9;}
.levelA{text-align: center;}
.limitTextTip{text-align:center;}
.handleCenter{text-align:center;}

.form_payBack{margin-left:100px;width:900px;}
.form_payBack ul li{float: left;margin: 14px 5% 0 auto;width:45%;height:40px;}
.form_payBack ul li>span:not(.bkBtn):first-child{display: inline-block;width:120px;line-height: 36px;}
.form_payBack .bkBtn{display: inline-block;}
.form_payBack .bkBtn1{margin-right: 52px;width:70px;}
.form_payBack .bkBtn2{margin-right: 60px;width:62px;}
.form_payBack ul li input{padding-left: 8px;width:212px;line-height:32px;display: inline-block;border: 1px solid #bebebe;vertical-align: top;}
.form_payBack ul li input#payBegin{width:98px;}
.form_payBack ul li input#payEnd{width:100px;}
.form_payBack ul li select{padding:8px 0 8px 8px;width:212px;border: 1px solid #bebebe;}
.form_payBack .select2-container--default .select2-selection--single {border: 1px solid #bebebe;}
.submitBtn{margin-top: 100px;width: 800px;text-align: right;}
.submitBtn span{margin-right:10px;}
.collectDealCon{  padding: 0;  font-size:15px; background-color: #f0f8ff;  overflow-x: auto;  }
.portlet-body{padding:0 50px;}
#mtTip span,#mtTip p{font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;}
#mtTip p{margin:0;}
.clear:before,.clear:after{display:block; clear:both; content:"";  visibility:hidden;  height:0}
.scanNoSupplying ul li{float:left;margin:0 0 12px 4%;width:45%;line-height:30px;font-size:15px;}
.allocatedDetail li{float:left;margin:0 40px 20px 0;width:45%;line-height:30px;font-size:15px;}
.tt-row{display: inline-block;width: 140px;}
.manageTip{margin-bottom:0;line-height:30px;background: #b8cce4;text-indent:2em;}
.listPay{margin-bottom: 30px;}
.listPay thead tr td{border-color:#d7d7d7}
.listPay tbody tr td input{text-align: center;}
.makeAgain li{float: left;padding:20px 50px 0 0;}
.makeAgain li i{margin-right:10px;}
#paySure[disabled]{background: #ccc;color:#333;border-color: #aaa;}
.supplyAgain{display: none;}
.cashTypeSl,.chequeTypeSl,.billTypeSl,.bankTypeSl{display: none;}
.recordSect p{text-align:right;margin:0;}
.customerName[disabled]{  background: #ddd;  cursor: no-drop;  }
.msTxtTip{color:#ff0000;}
.cancelBtn{background: #ffff00;  }
.cashType,.checkType,.billType,.bankType,.financeBankType{display: none;}
.con-footer{padding: 15px;  text-align: right;  border-top: 1px solid #e5e5e5;}
.infoSize{display: inline-block;  min-width: 62px;}
.objectSelect .fa{color:#48cfad;}
/*潜在客户*/
.commonBtn{margin-left:10%;}
.potentialCustomers{max-width:1000px;}
body select{background-color: #fff;  border: 1px solid #dcdfe6;}
#somethingItemDetailsPage > iframe{width: 1480px;}
.cusInput{position: relative;}
#selectCompany{ display: none; position: absolute; background:#fff;  width:192px; max-height:200px; overflow-y:auto; border-radius:4px; border:1px solid #ccc; border-top:none;   }
#selectCompany option{padding: 4px; border-bottom:1px solid #eee; background:#f9f8f8;    }
#selectCompany option:hover, #selectCompany option.active{ background:#ddd; cursor: default;    }
.dvdBtn{font-weight: bold;}
.bankDate{display: inline-block;vertical-align: top;}