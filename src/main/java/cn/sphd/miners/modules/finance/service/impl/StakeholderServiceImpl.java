package cn.sphd.miners.modules.finance.service.impl;

import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.modules.finance.dao.AccountDetailDao;
import cn.sphd.miners.modules.finance.dao.FinanceAccountBillDao;
import cn.sphd.miners.modules.finance.dao.FinanceStakeholderDao;
import cn.sphd.miners.modules.finance.dao.FinanceStakeholderHistoryDao;
import cn.sphd.miners.modules.finance.entity.AccountDetail;
import cn.sphd.miners.modules.finance.entity.FinanceAccountBill;
import cn.sphd.miners.modules.finance.entity.FinanceStakeholder;
import cn.sphd.miners.modules.finance.entity.FinanceStakeholderHistory;
import cn.sphd.miners.modules.finance.service.StakeholderService;
import cn.sphd.miners.modules.system.entity.User;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/11/19
 * @Description
 **/
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class StakeholderServiceImpl implements StakeholderService {

    @Autowired
    FinanceStakeholderDao financeStakeholderDao;
    @Autowired
    FinanceStakeholderHistoryDao financeStakeholderHistoryDao;
    @Autowired
    FinanceAccountBillDao financeAccountBillDao;
    @Autowired
    AccountDetailDao accountDetailDao;

    @Override
    public List<FinanceStakeholder> getStakeholderList(Integer oid, Integer enabledType,String name) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from FinanceStakeholder where org=:org";
        map.put("org",oid);
        if (enabledType!=null){
            if (1==enabledType){
                hql+=" and enabled=true";
            }else if (0==enabledType){
                hql+=" and enabled=false";
            }
        }
        if (StringUtils.isNotEmpty(name)){
            hql+=" and name like:name";
            map.put("name","%"+name+"%");
        }
        List<FinanceStakeholder> stakeholders = financeStakeholderDao.getListByHQLWithNamedParams(hql,map);
        return stakeholders;
    }

    @Override
    public FinanceStakeholder addStakeholder(User user, String name, String remark) {
        FinanceStakeholder financeStakeholder = new FinanceStakeholder();
        financeStakeholder.setRemark(remark);
        financeStakeholder.setName(name);
        financeStakeholder.setOrg(user.getOid());
        financeStakeholder.setDirection("2");   //帐务类别:1-收入,2-支出(默认),3-内部非支出性转账
        financeStakeholder.setEnabled(true);  //是否启用  0-否(false) 1-启用（true,默认）
        financeStakeholder.setEnabledTime(new Date());
        financeStakeholder.setCreateDate(financeStakeholder.getEnabledTime());  //为了保持这两个时间一样
        financeStakeholder.setCreateName(user.getUserName());
        financeStakeholder.setCreator(user.getUserID());
        financeStakeholder.setOperation("1");
        financeStakeholderDao.save(financeStakeholder);

        FinanceStakeholderHistory financeStakeholderHistory = new FinanceStakeholderHistory();
        BeanUtils.copyPropertiesIgnoreNull(financeStakeholder, financeStakeholderHistory);  //相同的部分已复制
        financeStakeholderHistory.setStakeholder(financeStakeholder.getId());
        financeStakeholderHistory.setVersionNo(0);
        financeStakeholderHistory.setPreviousId(0);
        financeStakeholderHistory.setId(null);
        financeStakeholderHistoryDao.save(financeStakeholderHistory);

        return financeStakeholder;
    }

    @Override   //有其他条件时再改
    public FinanceStakeholder getStakeholderById(Integer stakeholderId) {
        return financeStakeholderDao.get(stakeholderId);
    }

    @Override
    public void deactivate(User user,Integer stakeholderId, Integer enable) {
        FinanceStakeholder financeStakeholder = financeStakeholderDao.get(stakeholderId);
        if (0==enable) {
            financeStakeholder.setEnabled(false);
            financeStakeholder.setOperation("8");
        }else if (1==enable){
            financeStakeholder.setEnabled(true);
            financeStakeholder.setOperation("7");
        }
        financeStakeholder.setEnabledTime(new Date());
        financeStakeholder.setUpdateDate(new Date());
        financeStakeholder.setUpdateName(user.getUserName());
        financeStakeholder.setUpdator(user.getUserID());
        financeStakeholderDao.update(financeStakeholder);
    }

    @Override
    public void updateStakeholder(User user, Integer stakeholderId, String name, String remark, Integer type) {
        FinanceStakeholder financeStakeholder = financeStakeholderDao.get(stakeholderId);
        if (1==type){  //1-改名称中的错别字,2-改新名,3-修改说明
            financeStakeholder.setName(name);
            financeStakeholder.setOperation("4");  //操作:1-增,2-删,3-修改,4-改名称中的别字,5-改新名,6-修改说明,7-启用,8-停用
        }else if (2==type){
            financeStakeholder.setName(name);
            financeStakeholder.setOperation("5");  //操作:1-增,2-删,3-修改,4-改名称中的别字,5-改新名,6-修改说明,7-启用,8-停用
        }else {
            financeStakeholder.setRemark(remark);
            financeStakeholder.setOperation("6");  //操作:1-增,2-删,3-修改,4-改名称中的别字,5-改新名,6-修改说明,7-启用,8-停用
        }
        financeStakeholder.setUpdateDate(new Date());
        financeStakeholder.setUpdateName(user.getUserName());
        financeStakeholder.setUpdator(user.getUserID());
        financeStakeholderDao.update(financeStakeholder);

        FinanceStakeholderHistory financeStakeholderHistoryPrevious = getStakeholderHistoryLast(stakeholderId);
        FinanceStakeholderHistory financeStakeholderHistory = new FinanceStakeholderHistory();
        BeanUtils.copyPropertiesIgnoreNull(financeStakeholder, financeStakeholderHistory);  //相同的部分已复制
        financeStakeholderHistory.setStakeholder(financeStakeholder.getId());
        if (financeStakeholderHistoryPrevious!=null){
            financeStakeholderHistory.setVersionNo(financeStakeholderHistoryPrevious.getVersionNo()+1);
            financeStakeholderHistory.setPreviousId(financeStakeholderHistoryPrevious.getId());
        }
        financeStakeholder.setUpdateDate(new Date());
        financeStakeholder.setUpdateName(user.getUserName());
        financeStakeholder.setUpdator(user.getUserID());
        financeStakeholderHistoryDao.save(financeStakeholderHistory);

        if (1==type){   //1-改名称中的错别字(将历史数据中的均改过来)
            this.updateOppositeCorpByStakeholderId(user,stakeholderId,name);
        }
    }

    //获取最后修改的数据
    private FinanceStakeholderHistory getStakeholderHistoryLast(Integer stakeholderId){
        Map<String,Object> map = new HashMap<>();
        String hql = "from FinanceStakeholderHistory where stakeholder=:stakeholder order by id desc";
        map.put("stakeholder",stakeholderId);
        return (FinanceStakeholderHistory) financeStakeholderHistoryDao.getByHQLWithNamedParams(hql,map);
    }

    private void updateOppositeCorpByStakeholderId(User user,Integer stakeholderId,String name){
        Map<String,Object> map = new HashMap<>();
        String hql = "update AccountDetail set oppositeCorp=:oppositeCorp where org_=:org and stakeholder=:stakeholder";
        map.put("org",user.getOid());
        map.put("stakeholder",stakeholderId);
        map.put("oppositeCorp",name);
        accountDetailDao.queryHQLWithNamedParams(hql,map);

        String hql1 = "update FinanceAccountBill set oppositeCorp=:oppositeCorp where org_=:org and stakeholder=:stakeholder";
        financeAccountBillDao.queryHQLWithNamedParams(hql1,map);

        String hql2 = "update FinanceChequeDetail set receiveCorp=:oppositeCorp  where id in (select cheque_ from FinanceAccountBill where org_=:org and stakeholder=:stakeholder and cheque_ is not null)";
        financeAccountBillDao.queryHQLWithNamedParams(hql2,map);
    }

    @Override
    public List<FinanceStakeholderHistory> getStakeholderHistory(Integer stakeholderId){
        Map<String,Object> map = new HashMap<>();
        String hql = "from FinanceStakeholderHistory where stakeholder=:stakeholder";
        map.put("stakeholder",stakeholderId);
        List<FinanceStakeholderHistory> financeStakeholderHistories = financeStakeholderHistoryDao.getListByHQLWithNamedParams(hql,map);
        return financeStakeholderHistories;
    }
}
