<template>
  <div id="principalApproveList" class="potential" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="潜在客户受理" :isBackHome="isBackHome" isSearch="true" @toggleList="screen"></TY_NavTop>
    <div class="container">
      <div class="panel-title">您已被指定为下列客户的销售负责人，请开展销售工作。</div>
      <div class="ui-cells">
        <div class="ui-cells">
          <a class="ui-col" v-for="(item,index) in untreatList" :key="index" @click="jumpDetail(item.id)">
            <div class="ui-col__bd col-flex">
              <div class="ui-col_con">
                <div>{{item.fullName}}</div>
              </div>
              <div class="ui-col__flag">
              </div>
            </div>
            <div class="ui-col__ft">
              <div class="createDetail">录入者 {{item.createName}} {{item.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</div>
            </div>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { formatDate } from '@/js/common'
export default {
  name: 'potentialCustomerApprovalDuty',
  filters: {
    formatDate
  },
  data () {
    return {
      loading: true,
      untreatList: [],
      listenersUid: []
    }
  },
  created () {
    let _this = this
    let oid = this.sphdSocket.user.oid
    let userId = this.sphdSocket.user.userID
    let session = this.sphdSocket.sessionid
    this.listenersUid = [
      this.sphdSocket.subscribe('potentialAcceptListForSale', function (data) {
        console.log('我的审批：' + data)
        let resData = JSON.parse(data)
        _this.untreatList = resData.list
        _this.loading = false
      })
    ]
    this.sphdSocket.send('potentialAcceptListForSale', { 'oid': oid, userId: userId, session: session })
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    jumpDetail: function (id) {
      this.$router.push({
        path: `/potentialApproveDetail/${id}/${3}`
      })
    },
    screen: function () {
      this.$router.push({
        path: `/applyPotentialQuery/${2}`
      })
    }
  }
}
</script>
