<template>
  <Pageheader title="核心人物管控" :showBack="true"></Pageheader>
  <div class="container_main">
    <el-row>
      <el-col :span="24">
        请设置以下各项事务的负责人。
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        如设置全权负责人，则其他负责人将由全权负责人管控。 <br>
        您将仅管控全权负责人。
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        全权负责人可不设置，仅设置其他负责人。
      </el-col>
    </el-row>
    <el-table
        :data="managerList"
        border
        style="width: 100%; margin-top: 16px">
      <el-table-column label="高级事务的负责人/高级权限的分配者" width="150">
        <template #default="scope">
          {{ scope.row.name }}
        </template>
      </el-table-column>
      <el-table-column label="姓名/手机号">
        <template #default="scope">
          <div @click="chooseManager(scope.row)">
            {{ scope.row.user?(scope.row.user.userName + ' ' + scope.row.user.mobile):'--' }}
            <el-button link class="ty-right"><el-icon><arrow-right /></el-icon></el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="initState === '0' && !isHasCharge">
      <el-row style="margin-top: 16px">
        <el-col :span="24">
          <el-text type="primary">！提示</el-text>
          <br>
          <el-text type="info">确定后，所设置的负责人将收到需设置权限的提示短信，其他职工将收到关于本公司开始使用系统的提示短信。</el-text>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-checkbox-group v-model="radios.setAll" size="large">
            <el-checkbox value="1" label="以上负责人中，能设置的都已设置。"></el-checkbox>
          </el-checkbox-group>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" style="text-align: center">
          <el-button type="primary" size="large" style="padding-left: 32px; padding-right: 32px" @click="allSet_submit" :disabled="radios.setAll.length === 0">确定</el-button>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import * as api from "@/api/centralControl";
import auth from '@/sys/auth'
export default {
  data() {
    return {
      managerList: [],
      initState: '1',
      isHasCharge: false,
      radios: {
        setAll: []
      }
    }
  },
  created() {
    api.getManageList()
        .then(res => {
          let data = res.data
          console.log('data:::' , data)
          let managerList = data.manageList
          let initState = data.initState
          this.managerList = managerList
          this.initState = initState
          this.$store.dispatch('setManage', initState)
          this.isHasCharge = managerList[0].user !== null
        })
  },
  methods: {
    jump: function (path, query) {
      this.$router.push({
        path: path
      })
    },
    chooseManager(item) {
      let code = item.code
      this.$store.dispatch('setManageCode', code)

      if (item.user) {
        let params = {
          userID:item.user.userID,
          userName:item.user.userName,
          mobile:item.user.mobile,
          agentType: Number(item.user.agentType),
          isHasCharge: this.isHasCharge,
          mangeName: item.name
        }
        this.$store.dispatch('setManageParam', params)
        this.$router.push({
          path: '/managerDone'
        })
      } else {
        if (code === 'smallSuper') {
          this.$messageBox.confirm(
              '有全权负责人后，您将无法再直接管控其他事务的负责人。<br>确定设置全权负责人吗？',
              '！！提示',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
                dangerouslyUseHTMLString: true
              }
          )
              .then(() => {
                this.$store.dispatch('setManageParam', null)
                this.$router.push({
                  path: '/managerInput/add'
                })
              })
        } else {
          if (this.isHasCharge && auth.getUser().roleCode === 'super') {
            this.$message({
              type: 'error',
              message: '操作失败！<br>因为您已选择了全权负责人！',
              dangerouslyUseHTMLString: true
            })
          } else {
            this.$store.dispatch('setManageParam', null)
            this.$router.push({
              path: '/managerInput/add'
            })
          }
        }
      }
    },
    allSet_submit() {
      // 暂时没用
      this.$router.replace('orgList')
    },
    singleClick() {
      if (this.radios.setAll == 1) {
        this.radios.setAll = ''
      }
      console.log(this.radios.setAll)
    }
  }
}

</script>

<style lang="scss" scoped>
.el-row {
  margin-bottom: 16px;
}
.el-row:last-child {
  margin-bottom: 0;
}
</style>
