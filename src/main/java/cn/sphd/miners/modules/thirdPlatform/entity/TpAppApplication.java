package cn.sphd.miners.modules.thirdPlatform.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity ( name ="TpAppApplication" )
@Table ( name ="t_tp_app_application" )
public class TpAppApplication  implements Serializable {

    private static final long serialVersionUID = 7849118753157808528L;

    /**
     * ID
     */
    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 独立小程序平台代码
     */
    @Column(name = "tg_code")
    private String tgCode;

    /**
     * 平台ID
     */
    @Column(name = "platform")
    private Integer platform;

    /**
     * 应用ID
     */
    @Column(name = "app")
    private Integer app;

    /**
     * 阶段:1-注册,2-绑定,3-提交草稿,4-提交模板,5--提交小程序,6-提审/发布
     */
    @Column(name = "phase")
    private Integer phase;

    /**
     * 申请时间
     */
    @Column(name = "appl_time")
    private Date applTime;

    /**
     * 状态:0-草稿,1-提交申请,2-审批通过,3-审批否决
     */
    @Column(name = "approve_status")
    private Integer approveStatus;

    /**
     * 审批描述:记录驳回原因等信息
     */
    @Column(name = "approve_desc")
    private String approveDesc;

    /**
     * 批准时间
     */
    @Column(name = "approve_time")
    private Date approveTime;

    /**
     * 批准人
     */
    @Column(name = "approver")
    private String approver;

    /**
     * 接入三方url
     */
    @Column(name = "url")
    private String url;

    /**
     * 回调类
     */
    @Column(name = "callback_class")
    private String callbackClass;

    /**
     * 回调对象
     */
    @Column(name = "callback_json")
    private String callbackJson;

    /**
     * 回调表id
     */
    @Column(name = "callback_key")
    private String callbackKey;

    /**
     * 标题
     */
    @Column(name = "title")
    private String title;

    /**
     * 内容
     */
    @Column(name = "content")
    private String content;

    /**
     * 备注
     */
    @Column(name = "memo")
    private String memo;

    /**
     * 创建人id
     */
    @Column(name = "creator")
    private Integer creator;

    /**
     * 创建人
     */
    @Column(name = "create_name")
    private String createName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改人id
     */
    @Column(name = "updator")
    private Integer updator;

    /**
     * 修改人
     */
    @Column(name = "update_name")
    private String updateName;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    public Integer getId() {
        return this.id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTgCode() {
        return this.tgCode;
    }

    public void setTgCode(String tgCode) {
        this.tgCode = tgCode;
    }

    public Integer getPlatform() {
        return this.platform;
    }

    public void setPlatform(Integer platform) {
        this.platform = platform;
    }

    public Integer getApp() {
        return this.app;
    }

    public void setApp(Integer app) {
        this.app = app;
    }

    public Integer getPhase() {
        return this.phase;
    }

    public void setPhase(Integer phase) {
        this.phase = phase;
    }

    public Date getApplTime() {
        return this.applTime;
    }

    public void setApplTime(Date applTime) {
        this.applTime = applTime;
    }

    public Integer getApproveStatus() {
        return this.approveStatus;
    }

    public void setApproveStatus(Integer approveStatus) {
        this.approveStatus = approveStatus;
    }

    public String getApproveDesc() {
        return this.approveDesc;
    }

    public void setApproveDesc(String approveDesc) {
        this.approveDesc = approveDesc;
    }

    public Date getApproveTime() {
        return this.approveTime;
    }

    public void setApproveTime(Date approveTime) {
        this.approveTime = approveTime;
    }

    public String getApprover() {
        return this.approver;
    }

    public void setApprover(String approver) {
        this.approver = approver;
    }

    public String getUrl() {
        return this.url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getCallbackClass() {
        return this.callbackClass;
    }

    public void setCallbackClass(String callbackClass) {
        this.callbackClass = callbackClass;
    }

    public String getCallbackJson() {
        return this.callbackJson;
    }

    public void setCallbackJson(String callbackJson) {
        this.callbackJson = callbackJson;
    }

    public String getCallbackKey() {
        return this.callbackKey;
    }

    public void setCallbackKey(String callbackKey) {
        this.callbackKey = callbackKey;
    }

    public String getTitle() {
        return this.title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return this.content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getMemo() {
        return this.memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return this.creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return this.createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return this.updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return this.updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return this.updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}