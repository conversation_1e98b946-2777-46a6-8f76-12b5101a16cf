package cn.sphd.miners.modules.processManagement.dto;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 查看设置详情中的工序下子工序以及子工序下的操作
 * <AUTHOR>
 * @Date 20233/5/23
 */
public class SonProcessesDto implements Serializable {

    private Long flowId;   //生产流程ID
    private Integer product;   //产品ID
    private Long process;   //工序ID
    private Long activity;   //活动ID
    private Long parent;   //父活动ID
    private String code;   //代号
    private String name;   //工序名称
    private Integer orders;   //同级排序
    private Integer category;   //类别
    private String categoryName;   //类别名称
    private Byte type;   //类型:2-子工序,3-操作,4-套餐
    private Boolean isCyclicity; //是否周期性:true-是,false-否(默认)
    private List<SonProcessesDto> sonOperateList;  //子工序下的操作列表
    private String periodicity;
    private Boolean isLeaf;   //是否有子工序或操作
    private Byte timing;     //时间点:1-准备工作,2-正式操作,3-收尾工作
    private Byte kind;  //操作类型:1-主要,2-辅助

    public Long getFlowId() {
        return flowId;
    }

    public void setFlowId(Long flowId) {
        this.flowId = flowId;
    }

    public Integer getProduct() {
        return product;
    }

    public void setProduct(Integer product) {
        this.product = product;
    }

    public Long getProcess() {
        return process;
    }

    public void setProcess(Long process) {
        this.process = process;
    }

    public Long getActivity() {
        return activity;
    }

    public void setActivity(Long activity) {
        this.activity = activity;
    }

    public Long getParent() {
        return parent;
    }

    public void setParent(Long parent) {
        this.parent = parent;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public Boolean getIsCyclicity() {
        return isCyclicity;
    }

    public void setIsCyclicity(Boolean isCyclicity) {
        this.isCyclicity = isCyclicity;
    }

    public List<SonProcessesDto> getSonOperateList() {
        return sonOperateList;
    }

    public void setSonOperateList(List<SonProcessesDto> sonOperateList) {
        this.sonOperateList = sonOperateList;
    }

    public String getPeriodicity() {
        return periodicity;
    }

    public void setPeriodicity(String periodicity) {
        this.periodicity = periodicity;
    }

    public Boolean getIsLeaf() {
        return isLeaf;
    }

    public void setIsLeaf(Boolean isLeaf) {
        this.isLeaf = isLeaf;
    }

    public Byte getTiming() {
        return timing;
    }

    public void setTiming(Byte timing) {
        this.timing = timing;
    }

    public Byte getKind() {
        return kind;
    }

    public void setKind(Byte kind) {
        this.kind = kind;
    }

    public SonProcessesDto(){}

    public SonProcessesDto(Long flowId,Integer product,Long process,Long activity,Long parent,String code,String name,Integer orders,Integer category,String categoryName,Byte type,Boolean isCyclicity){
        this.flowId=flowId;
        this.product=product;
        this.process=process;
        this.activity=activity;
        this.parent=parent;
        this.code=code;
        this.name=name;
        this.orders=orders;
        this.category=category;
        if (category!=null) {
            this.categoryName = categoryName;
        }else {
            this.categoryName = "";
        }
        this.type=type;
        this.isCyclicity=isCyclicity;
    }

    public SonProcessesDto(Long flowId, Integer product, Long process, Long activity, Long parent, String code, String name, Integer orders, Integer category, String categoryName, Byte type, Boolean isCyclicity, Boolean isLeaf) {
        this.flowId = flowId;
        this.product = product;
        this.process = process;
        this.activity = activity;
        this.parent = parent;
        this.code = code;
        this.name = name;
        this.orders = orders;
        this.category = category;
        this.categoryName = categoryName;
        this.type = type;
        this.isCyclicity = isCyclicity;
        this.isLeaf = isLeaf;
    }

    public SonProcessesDto(Long flowId, Integer product, Long process, Long activity, Long parent, String code, String name, Integer orders, Integer category, String categoryName, Byte type, Boolean isCyclicity, String periodicity){
        this.flowId=flowId;
        this.product=product;
        this.process=process;
        this.activity=activity;
        this.parent=parent;
        this.code=code;
        this.name=name;
        this.orders=orders;
        this.category=category;
        if (category!=null) {
            this.categoryName = categoryName;
        }else {
            this.categoryName = "";
        }
        this.type=type;
        this.isCyclicity=isCyclicity;
        this.periodicity=periodicity;
    }

    public SonProcessesDto(Long flowId, Integer product, Long process, Long activity, Long parent, String code, String name, Integer orders, Integer category, String categoryName, Byte type, Boolean isCyclicity, Boolean isLeaf, Byte timing, Byte kind) {
        this.flowId = flowId;
        this.product = product;
        this.process = process;
        this.activity = activity;
        this.parent = parent;
        this.code = code;
        this.name = name;
        this.orders = orders;
        this.category=category;
        if (category!=null) {
            this.categoryName = categoryName;
        }else {
            this.categoryName = "";
        }
        this.type = type;
        this.isCyclicity = isCyclicity;
        this.isLeaf = isLeaf;
        this.timing = timing;
        this.kind = kind;
    }
}
