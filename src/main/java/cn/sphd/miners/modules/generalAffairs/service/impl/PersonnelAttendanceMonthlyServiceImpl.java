package cn.sphd.miners.modules.generalAffairs.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.service.BaseServiceImpl;
import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.generalAffairs.dao.PersonnelAttendanceMonthlyDao;
import cn.sphd.miners.modules.generalAffairs.dto.PersonnelAttendanceMonthlyDto;
import cn.sphd.miners.modules.generalAffairs.entity.PersonnelAttendanceMonthly;
import cn.sphd.miners.modules.generalAffairs.service.PersonnelAttendanceMonthlyService;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceOldService;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class PersonnelAttendanceMonthlyServiceImpl extends BaseServiceImpl implements PersonnelAttendanceMonthlyService {
    @Autowired
    PersonnelAttendanceMonthlyDao dao;
    @Autowired
    UserDao userDao;

    @Autowired
    UserService userService;

    @Autowired
    WorkAttendanceOldService workAttendanceOldService;

    private Map<Long, Integer> ids = new ConcurrentHashMap<>();
    public Integer getPersonnelAttendanceMonthlyId(Integer uid, Integer yearmonth){//缓存函数
        Integer result;
        Long key = uid * 1000000L + yearmonth;
        if((result = ids.get(key))==null) {
            String hql = "select id from PersonnelAttendanceMonthly where user=:user and yearmonth=:yearmonth";
            Map<String, Object> params = new HashMap<String, Object>(2) {{
                put("user", uid);
                put("yearmonth", yearmonth);
            }};
            result = (Integer) dao.getByHQLWithNamedParams(hql, params);
            if(result!=null) {
                ids.put(key, result);
            }
        }
        return result;
    }

    private PersonnelAttendanceMonthly GetOrAddEntity(User user, Date day) {
        Integer yearmonth = NewDateUtils.getYearMonth(day);
        Integer id = getPersonnelAttendanceMonthlyId(user.getUserID(),yearmonth);
        PersonnelAttendanceMonthly entity;
        if(id==null) {
            entity = NewEntity(user, yearmonth);
            dao.saveOrUpdate(entity);
            Long key = user.getUserID() * 1000000L + yearmonth;
            ids.put(key, entity.getId());
        } else {
            entity = dao.get(id);
        }
        return entity;
    }

    @Override
    public PersonnelAttendanceMonthly NewEntity(User user, Integer yearmonth) {
        PersonnelAttendanceMonthly entity = new PersonnelAttendanceMonthly();
        entity.setUser(user.getUserID());
        entity.setYearmonth(yearmonth);
        entity.setOrg(user.getOid());
        entity.setUserName(user.getUserName());
        updateDepartment(entity, user);
        return entity;
    }

    private PersonnelAttendanceMonthly GetEntity(User user, Date day) {
        Integer yearmonth = NewDateUtils.getYearMonth(day);
//        Integer id = service.getPersonnelAttendanceMonthlyId(user.getUserID(),yearmonth);
        Integer id = getPersonnelAttendanceMonthlyId(user.getUserID(),yearmonth);
        PersonnelAttendanceMonthly entity =  null;
        if(id!=null) {
            entity = dao.get(id);
        }
        return entity;
    }

    @Override
    public void AddUpdateUserInfo(User user, Date day) {
        String hql = "from PersonnelAttendanceMonthly where user=:user and yearmonth=:yearmonth";
        HashMap<String, Object> params = new HashMap<>();
        params.put("user", user.getUserID());
        Integer yearmonth = NewDateUtils.getYearMonth(day);
        params.put("yearmonth", yearmonth);
        PersonnelAttendanceMonthly entity = (PersonnelAttendanceMonthly) dao.getByHQLWithNamedParams(hql,params);
        if(entity==null) {
            entity = new PersonnelAttendanceMonthly();
            entity.setUser(user.getUserID());
            entity.setYearmonth(yearmonth);
            entity.setOrg(user.getOid());
            dao.save(entity);
        }
        entity.setUserName(user.getUserName());
        updateDepartment(entity, user);
    }

    private void updateDepartment(PersonnelAttendanceMonthly entity, User user) {
        Integer dept=0;
        try {
            dept = Integer.parseInt(user.getDepartment());
        } catch (NumberFormatException e) {
            logger.debug(Thread.currentThread().getStackTrace()[2].getMethodName() + " NumberFormatException !", e);
        }
        entity.setDepartment(dept);
        if(dept == 0) {
            entity.setDepartmentName("其他");
        } else {
            entity.setDepartmentName(user.getDepartName());
        }
    }

    @Override
    public void ClearDay(User user, Date date) {
        PersonnelAttendanceMonthly entity = GetOrAddEntity(user, date);
        Integer day = NewDateUtils.getDate(date);
        try {
            entity.SetNoNeed(day, Boolean.FALSE, Boolean.TRUE);
            entity.SetLeave(day, (byte)0, Float.valueOf(0.0F));
            entity.SetTravel(day, (byte)0);
            entity.SetLate(day, Boolean.FALSE);
            entity.SetLeaveEarly(day, Boolean.FALSE);
            entity.SetOutside(day, (byte)0);
            entity.SetAbsenteeisme(day, (byte)0);
            entity.SetOvertime(day, (byte)0, Float.valueOf(0.0F));
        } catch (InvocationTargetException | IllegalAccessException e) {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " InvocationTargetException | IllegalAccessException !", e);
        }
    }

    @Override
    public void SetRemoveUser(Integer oid,List<Integer> uids, Date date) {
        Triple<List<Date>, Set<Date>, Set<Date>> workingDates = workAttendanceOldService.getWorkingDates(date, date, oid);//left 总, middle 班, right假,
        Boolean isWorkingDay = workingDates.getMiddle().contains(date);
        Integer yearmonth = NewDateUtils.getYearMonth(date);
        Integer day = NewDateUtils.getDate(date);
        StringBuffer hql = new StringBuffer("from PersonnelAttendanceMonthly where yearmonth=:yearmonth and org=:org");
        HashMap<String, Object> params = new HashMap<>();
        params.put("yearmonth", yearmonth);
        params.put("org", oid);
        if(uids!=null && uids.size()>0){
            hql.append(" and user not in (:uids)");
            params.put("uids", uids);
        }
        List<PersonnelAttendanceMonthly> records=dao.getListByHQLWithNamedParams(hql.toString(), params);
        if(!records.isEmpty()) {
            for (PersonnelAttendanceMonthly record : records) {
                try {
                    record.SetNoNeed(day, Boolean.TRUE, Boolean.FALSE);
                    if(isWorkingDay) {
                        record.SetWorkingHours(day, Float.valueOf(8.0F));
                    }
                } catch (InvocationTargetException | IllegalAccessException e) {
                    logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " InvocationTargetException | IllegalAccessException !", e);
                }
            }
        }
    }

    @Override
    public void SetNewUserNoNeed(User user, Date day, Date firstDate) {
        if(user == null) {
            throw new IllegalArgumentException("传入的user为空");
        }
        firstDate = firstDate == null ? NewDateUtils.changeMonth(day, 0) : firstDate;
        if(day.getTime()<firstDate.getTime()) {
            Date tmp = day;
            day = firstDate;
            firstDate = tmp;
        }
        int endDay = NewDateUtils.getDate(day);
        int firstDay = NewDateUtils.getDate(firstDate);
        PersonnelAttendanceMonthly entity = GetOrAddEntity(user, day);
        entity.SetTransientPorpertis();
        Triple<List<Date>, Set<Date>, Set<Date>> workingDates = workAttendanceOldService.getWorkingDates(firstDate, day, user.getOid());//left 总, middle 班, right假,
        for (; endDay >= firstDay; endDay--) {
            if ("normal".equalsIgnoreCase(entity.GetDayStatus(String.format("%02d", endDay)))) {
                try {
                    entity.SetNoNeed(endDay, Boolean.TRUE, Boolean.FALSE);
                    if(workingDates.getMiddle().contains(endDay)) {
                        entity.SetWorkingHours(endDay, Float.valueOf(8.0F));
                    }
                } catch (InvocationTargetException | IllegalAccessException e) {
                    logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " InvocationTargetException | IllegalAccessException !", e);
                }
            } else {
                break;//遇到非空就退出
            }
        }
    }

    @Override
    public void SetNoNeed(User user, Date day, Boolean value) {
        PersonnelAttendanceMonthly entity = GetOrAddEntity(user, day);
        try {
            entity.SetNoNeed(NewDateUtils.getDate(day),value, Boolean.TRUE);
        } catch (InvocationTargetException | IllegalAccessException e) {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " InvocationTargetException | IllegalAccessException !", e);
        }
    }

    @Override
    public Boolean GetNoNeed(User user, Date day) {
        PersonnelAttendanceMonthly entity = GetEntity(user, day);
        if(entity!=null) {
            try {
                return entity.GetNoNeed(NewDateUtils.getDate(day));
            } catch (InvocationTargetException | IllegalAccessException e) {
                logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " InvocationTargetException | IllegalAccessException !", e);
            }
        }
        return Boolean.TRUE;
    }

    @Override
    public void SetWorkingHours(User user, Date day, Float value) {
        if(value > 24.0F || value < 0.0F) {
            logger.error("SetWorkingHours overflow error! value = " + value + " user_id = "+ user.getUserID() + " day = " + NewDateUtils.dateToString(day, sdf));
        }
        PersonnelAttendanceMonthly entity = GetOrAddEntity(user, day);
        if(entity!=null && value!=null) {
            try {
                Integer d = NewDateUtils.getDate(day);
                Pair<Byte, Float> leave = entity.GetLeave(d);
                if (leave != null && leave.getRight() != null) {
                    Float duration = Math.min(leave.getRight(), value);
                    if(duration > 24.0F || duration < 0.0F) {
                        logger.error("SetWorkingHours set leave overflow error! duration = " + duration + " user_id = "+ user.getUserID() + " day = " + NewDateUtils.dateToString(day, sdf));
                    }
                    if (!leave.getRight().equals(duration)) {
                        entity.SetLeave(d, leave.getLeft(), duration);
                    }
                }
                entity.SetWorkingHours(d, value);
            } catch (InvocationTargetException | IllegalAccessException e) {
                logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " InvocationTargetException | IllegalAccessException !", e);
            }
        }
    }

    @Override
    public Float GetWorkingHours(User user, Date day) {
        PersonnelAttendanceMonthly entity = GetEntity(user, day);
        if(entity!=null) {
            try {
                return entity.GetWorkingHours(NewDateUtils.getDate(day));
            } catch (InvocationTargetException | IllegalAccessException e) {
                logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " InvocationTargetException | IllegalAccessException !", e);
            }
        }
        return Float.valueOf(0.0F);
    }

    @Override
    public void SetLeave(User user, Date day, Byte value, Float duration) {
        if(duration > 24.0F || duration < 0.0F) {
            logger.error("SetLeave overflow error! duration = " + duration + " user_id = "+ user.getUserID() + " day = " + NewDateUtils.dateToString(day, sdf));
        }
        PersonnelAttendanceMonthly entity = GetOrAddEntity(user, day);
        try {
            Integer d = NewDateUtils.getDate(day);
            Float workHours = entity.GetWorkingHours(d);
            duration = Math.min(duration, workHours);
            entity.SetLeave(d, value, duration);
        } catch (InvocationTargetException | IllegalAccessException e) {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " InvocationTargetException | IllegalAccessException !", e);
        }
    }

    @Override
    public Pair<Byte, Float> GetLeave(User user, Date day) {
        PersonnelAttendanceMonthly entity = GetEntity(user, day);
        if(entity!=null) {
            try {
                return entity.GetLeave(NewDateUtils.getDate(day));
            } catch (InvocationTargetException | IllegalAccessException e) {
                logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " InvocationTargetException | IllegalAccessException !", e);
            }
        }
        return Pair.of((byte)0, Float.valueOf(0.0F));
    }

    @Override
    public void SetTravel(User user, Date day, Byte value) {
        PersonnelAttendanceMonthly entity = GetOrAddEntity(user, day);
        try {
            entity.SetTravel(NewDateUtils.getDate(day),value);
        } catch (InvocationTargetException | IllegalAccessException e) {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " InvocationTargetException | IllegalAccessException !", e);
        }
    }

    @Override
    public Byte GetTravel(User user, Date day) {
        PersonnelAttendanceMonthly entity = GetEntity(user, day);
        if(entity!=null) {
            try {
                return entity.GetTravel(NewDateUtils.getDate(day));
            } catch (InvocationTargetException | IllegalAccessException e) {
                logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " InvocationTargetException | IllegalAccessException !", e);
            }
        }
        return (byte)0;
    }

    @Override
    public void SetLate(User user, Date day, Boolean value) {
        PersonnelAttendanceMonthly entity = GetOrAddEntity(user, day);
        try {
            entity.SetLate(NewDateUtils.getDate(day),value);
        } catch (InvocationTargetException | IllegalAccessException e) {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " InvocationTargetException | IllegalAccessException !", e);
        }
    }

    @Override
    public Boolean GetLate(User user, Date day) {
        PersonnelAttendanceMonthly entity = GetEntity(user, day);
        if(entity!=null) {
            try {
                return entity.GetLate(NewDateUtils.getDate(day));
            } catch (InvocationTargetException | IllegalAccessException e) {
                logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " InvocationTargetException | IllegalAccessException !", e);
            }
        }
        return Boolean.FALSE;
    }

    @Override
    public void SetLeaveEarly(User user, Date day, Boolean value) {
        PersonnelAttendanceMonthly entity = GetOrAddEntity(user, day);
        try {
            entity.SetLeaveEarly(NewDateUtils.getDate(day),value);
        } catch (InvocationTargetException | IllegalAccessException e) {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " InvocationTargetException | IllegalAccessException !", e);
        }
    }

    @Override
    public Boolean GetLeaveEarly(User user, Date day) {
        PersonnelAttendanceMonthly entity = GetEntity(user, day);
        if(entity!=null) {
            try {
                return entity.GetLeaveEarly(NewDateUtils.getDate(day));
            } catch (InvocationTargetException | IllegalAccessException e) {
                logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " InvocationTargetException | IllegalAccessException !", e);
            }
        }
        return Boolean.FALSE;
    }

    @Override
    public void SetOutside(User user, Date day, Byte value) {
        PersonnelAttendanceMonthly entity = GetOrAddEntity(user, day);
        try {
            entity.SetOutside(NewDateUtils.getDate(day),value);
        } catch (InvocationTargetException | IllegalAccessException e) {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " InvocationTargetException | IllegalAccessException !", e);
        }
    }

    @Override
    public Byte GetOutside(User user, Date day) {
        PersonnelAttendanceMonthly entity = GetEntity(user, day);
        if(entity!=null) {
            try {
                return entity.GetOutside(NewDateUtils.getDate(day));
            } catch (InvocationTargetException | IllegalAccessException e) {
                logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " InvocationTargetException | IllegalAccessException !", e);
            }
        }
        return Byte.valueOf((byte)0);
    }

    @Override
    public void SetAbsenteeisme(User user, Date day, Byte value) {
        PersonnelAttendanceMonthly entity = GetOrAddEntity(user, day);
        try {
            entity.SetAbsenteeisme(NewDateUtils.getDate(day),value);
        } catch (InvocationTargetException | IllegalAccessException e) {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " InvocationTargetException | IllegalAccessException !", e);
        }
    }

    @Override
    public Byte GetAbsenteeisme(User user, Date day) {
        PersonnelAttendanceMonthly entity = GetEntity(user, day);
        if(entity!=null) {
            try {
                return entity.GetAbsenteeisme(NewDateUtils.getDate(day));
            } catch (InvocationTargetException | IllegalAccessException e) {
                logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " InvocationTargetException | IllegalAccessException !", e);
            }
        }
        return Byte.valueOf((byte)0);
    }

    @Override
    public void SetOvertime(User user, Date day, Byte num, Float duration) {
        PersonnelAttendanceMonthly entity = GetOrAddEntity(user, day);
        try {
            entity.SetOvertime(NewDateUtils.getDate(day),num,duration);
        } catch (InvocationTargetException | IllegalAccessException e) {
            logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " InvocationTargetException | IllegalAccessException !", e);
        }
    }

    @Override
    public Pair<Byte, Float> GetOvertime(User user, Date day) {
        PersonnelAttendanceMonthly entity = GetEntity(user, day);
        if(entity!=null) {
            try {
                return entity.GetOvertime(NewDateUtils.getDate(day));
            } catch (InvocationTargetException | IllegalAccessException e) {
                logger.error(Thread.currentThread().getStackTrace()[1].getMethodName() + " InvocationTargetException | IllegalAccessException !", e);
            }
        }
        return Pair.of(Byte.valueOf((byte) 0), Float.valueOf(0.0F));
    }

    @Override
    public PersonnelAttendanceMonthly getAttendanceMonthly(Integer yearmonth, Integer uid) {
        String hql = "from PersonnelAttendanceMonthly where yearmonth=:yearmonth and user=:uid";
        Map<String, Object> params = new HashMap<String, Object>(2){{
            put("yearmonth",yearmonth);
            put("uid",uid);
        }};
        PersonnelAttendanceMonthly entity = (PersonnelAttendanceMonthly) dao.getByHQLWithNamedParams(hql, params);
        if(entity!=null) {
            dao.getSession().evict(entity);
            entity.SetTransientPorpertis();
        }
        return entity;
    }

    @Override
    public List<PersonnelAttendanceMonthly> getAttendanceMonthliesOrderbyName(Integer oid, Integer yearmonth, Integer deptId,Integer loginUserId,Integer userId,String userName, PageInfo pageInfo) {
        List<String> where = new ArrayList<>();
        Map<String, Object> params = new HashMap<>();
        where.add("yearmonth=:yearmonth");
        params.put("yearmonth", yearmonth);
        pushOtherParams(Pair.of(where,params), oid, deptId, loginUserId, userId, userName);
        StringBuffer hql = new StringBuffer("from PersonnelAttendanceMonthly");
        if(!where.isEmpty()) {
            hql.append(" where ").append(StringUtils.join(where, " and "));
        }
        hql.append(" order by convert(userName, 'gbk')");
        List<PersonnelAttendanceMonthly>  entities = dao.getListByHQLWithNamedParams(hql.toString(),params,pageInfo);
        for(PersonnelAttendanceMonthly entity : entities) {
            dao.getSession().evict(entity);
            entity.SetTransientPorpertis();
        }
        return entities;
    }

    @Override
    public List<PersonnelAttendanceMonthly> getAttendanceMonthliesOrderbyNameWithImage(Integer oid, Integer yearmonth, Integer deptId, Integer loginUserId, Integer userId, String userName, PageInfo pageInfo) {
        List<PersonnelAttendanceMonthly>  entities = getAttendanceMonthliesOrderbyName(oid, yearmonth, deptId,loginUserId,userId,userName, pageInfo);
        for(PersonnelAttendanceMonthly entity : entities) {
            entity.setImgPath(userDao.get(entity.getUser()).getImgPath());  //手机端需要的职工图片
        }
        return entities;
    }

    @Override
    public List<PersonnelAttendanceMonthly> getAttendanceMonthliesByPeriod(Integer oid, Integer yearmonthBegin,Integer yearmonthEnd, Integer deptId,Integer loginUserId,Integer userId,String userName, PageInfo pageInfo) {
        List<String> where = new ArrayList<>();
        HashMap<String, Object> params = new HashMap<>();
        if (yearmonthBegin!=null&&yearmonthEnd!=null){
            where.add(" yearmonth between :yearmonthBegin and :yearmonthEnd");
            params.put("yearmonthBegin", yearmonthBegin);
            params.put("yearmonthEnd", yearmonthEnd);
        }
        pushOtherParams(Pair.of(where,params), oid, deptId, loginUserId, userId, userName);
        StringBuffer hql = new StringBuffer("from PersonnelAttendanceMonthly");
        if(!where.isEmpty()) {
            hql.append(" where ").append(StringUtils.join(where, " and "));
        }
        List<PersonnelAttendanceMonthly>  entities = dao.getListByHQLWithNamedParams(hql.toString(),params,pageInfo);
        for(PersonnelAttendanceMonthly entity : entities) {
            dao.getSession().evict(entity);
            entity.SetTransientPorpertis();
        }
        return entities;
    }

    @Override
    public List<PersonnelAttendanceMonthly> getAttendanceMonthliesByPeriod(Integer oid, Integer yearmonthBegin, Integer yearmonthEnd, Collection<Integer> userIds) {
        List<String> where = new ArrayList<>();
        HashMap<String, Object> params = new HashMap<>(3);
        if(oid!=null) {
            where.add("org=:oid");
            params.put("oid", oid);
        }
        if(userIds!=null){
            where.add("user in (:userIds)");
            params.put("userIds", userIds);
        }
        if (yearmonthBegin!=null&&yearmonthEnd!=null){
            where.add("yearmonth between :yearmonthBegin and :yearmonthEnd");
            params.put("yearmonthBegin", yearmonthBegin);
            params.put("yearmonthEnd", yearmonthEnd);
        }
        StringBuffer hql = new StringBuffer("from PersonnelAttendanceMonthly");
        if(!where.isEmpty()) {
            hql.append(" where ").append(StringUtils.join(where, " and "));
        }
        List<PersonnelAttendanceMonthly>  entities = dao.getListByHQLWithNamedParams(hql.toString(),params);
        for(PersonnelAttendanceMonthly entity : entities) {
            dao.getSession().evict(entity);
            entity.SetTransientPorpertis();
        }
        return entities;
    }

    private void pushOtherParams(Pair<List<String>, Map<String, Object>> result, Integer oid, Integer deptId, Integer loginUserId, Integer userId, String userName) {
        List<String> where = result.getLeft()==null ? new ArrayList<>() : result.getLeft();
        Map<String, Object> params = result.getRight()==null ? new HashMap<>() : result.getRight();
        if(oid!=null) {
            where.add("org=:oid");
            params.put("oid", oid);
        }
        if(deptId!=null && deptId.intValue()>=0) {//deptId == 0时查部门为空的人员
            where.add("department=:department");
            params.put("department", deptId);
        }
        if(userId!=null){
            where.add("user=:userId");
            params.put("userId", userId);
        }
        if (loginUserId!=null) {
            Map<String, Object> map = userService.getUserIds(loginUserId, 1);
            List<Integer> userIds = (List<Integer>) map.get("userIdsList");
            if (userIds != null) {
                where.add("user in (:userIds)");
                params.put("userIds", userIds);
            }
        }
        if(!MyStrings.nulltoempty(userName).isEmpty()) {
            where.add("userName like :userName");
            params.put("userName", '%'+userName+'%');
        }
    }

    @Override
    public List<PersonnelAttendanceMonthly> getAllAttendanceMonthliesForInit() {
        String hql = "from PersonnelAttendanceMonthly order by org, user, yearmonth";
        List<PersonnelAttendanceMonthly>  entities = dao.getListByHQLWithNamedParams(hql.toString(), null);
        for(PersonnelAttendanceMonthly entity : entities) {
            dao.getSession().evict(entity);
            entity.SetTransientPorpertis(Boolean.FALSE);
        }
        return entities;
    }

    @Override
    public List<PersonnelAttendanceMonthlyDto> getAttendanceMonthlyStatistics(Integer oid, Integer yearmonth, Integer deptId, Integer loginUserId, Integer userId, String userName, PageInfo pageInfo) {
        List<PersonnelAttendanceMonthlyDto> personnelAttendanceMonthlyDtos = new ArrayList<>();
        List<PersonnelAttendanceMonthly> personnelAttendanceMonthlys = this.getAttendanceMonthliesOrderbyName(oid,yearmonth,deptId,loginUserId,userId,userName,pageInfo);
        for (PersonnelAttendanceMonthly p:personnelAttendanceMonthlys) {
            PersonnelAttendanceMonthlyDto personnelAttendanceMonthlyDto = new PersonnelAttendanceMonthlyDto();
            BeanUtils.copyPropertiesIgnoreNull(p, personnelAttendanceMonthlyDto);
            personnelAttendanceMonthlyDtos.add(personnelAttendanceMonthlyDto);
        }

        return personnelAttendanceMonthlyDtos;
    }

    @Override
    public List<Map<String, Object>> getAttendanceUsers(Integer oid, Integer yearmonth,Integer userId) {
        StringBuffer hql = new StringBuffer("select user,userName from PersonnelAttendanceMonthly where org=:org and yearmonth=:yearmonth");
        HashMap<String, Object> params = new HashMap<>();
        if (userId!=null){
            Map<String, Object> map = userService.getUserIds(userId, 1);
            List<Integer> userIds = (List<Integer>) map.get("userIdsList");
            hql.append(" and user in (:userIds)");
            params.put("userIds", userIds);
        }
        params.put("org", oid);
        params.put("yearmonth", yearmonth);
        hql.append(" order by convert(userName, 'gbk')");
        List<Object[]> objects = (List<Object[]>) dao.getListByHQLWithNamedParams(hql.toString(),params);
        List<Map<String, Object>> listMap = new ArrayList<>();
        for (Object[] object:objects) {
            Map<String, Object> map = new HashMap<>();
            map.put("userId",object[0]);
            map.put("userName",object[1]);
            listMap.add(map);
        }
        return listMap;
    }
}