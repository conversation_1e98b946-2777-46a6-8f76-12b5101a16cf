package cn.sphd.miners.modules.accountant.zEnum;


import cn.sphd.miners.modules.accountant.zEnum.utils.EnumValue;

/**
 * Created by 刘洪涛 on 17-3-17.
 */
public class CommonEnum {

    /*审批状态*/
    public static enum VoucherApproveEnum implements EnumValue
    {
        /*approve_status
                     1-正常待审批        5-不予下帐待审批     4-修改待审批
                     2-正常审批通过      7-财务修改审批通过   9-不予下帐审批通过
                     6-不予下帐已驳回    8-财务修改驳回       3-正常已驳回
                     */
        NORMAL_PENDING(1,"正常待审批"),
        NOT_TO_PAY_PENDING(5,"不予下帐待审批"),
        MODIFY_PENDING(4,"修改待审批"),

        NORMAL_APPROVE(2,"正常审批通过"),
        MODIFY_APPROVE(7,"修改审批通过"),
        NOT_TO_PAY_APPROVE(9,"不予下帐审批通过"),

        NORMAL_FAIL(3,"正常已驳回"),
        MODIFY_FAIL(8,"修改已驳回"),
        NOT_TO_PAY_FAIL(6,"不予下帐已驳回");


        private Integer index;
        private String name;

        private VoucherApproveEnum(Integer index, String name) {
            this.index = index;
            this.name = name;
        }

        public static String getNameByIndex(int index)
        {
            for(VoucherApproveEnum c : VoucherApproveEnum.values())
            {
                if(c.getIndex() == index)
                {
                    return c.name;
                }
            }
            return null;
        }

        @Override
        public String getName() {
            return this.name;
        }

        @Override
        public int getIndex() {
            return this.index;
        }
    }

}
