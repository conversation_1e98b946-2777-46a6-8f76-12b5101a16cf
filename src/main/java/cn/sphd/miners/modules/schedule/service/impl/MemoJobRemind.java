package cn.sphd.miners.modules.schedule.service.impl;

import cn.sphd.miners.modules.schedule.entity.MemoJob;
import cn.sphd.miners.modules.schedule.service.MemoScheduleService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.socket.service.DelayCallback;
import org.springframework.context.ApplicationContext;

import java.util.Date;

/**
 * Created by Administrator on 2019/1/24.
 * 备忘与日程 延时调用
 */
public class MemoJobRemind implements DelayCallback {
    private Integer id;

    private Date updateDate;

    public Integer getId() {
        return id;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public MemoJobRemind(Integer id, Date updateDate) {
        this.id=id;
        this.updateDate=updateDate;
    }

    @Override
    public void delayCall(ClusterMessageSendingOperations clusterMessageSendingOperations, ApplicationContext ac) {
        MemoScheduleService memoScheduleService = ac.getBean(MemoScheduleService.class);
        memoScheduleService.overdueMemoJobSchedule(id,updateDate);
    }
}
