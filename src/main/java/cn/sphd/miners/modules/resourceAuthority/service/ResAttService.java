package cn.sphd.miners.modules.resourceAuthority.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.resourceAuthority.entity.ResAtt;
import cn.sphd.miners.modules.resourceAuthority.entity.ResAttHis;
import cn.sphd.miners.modules.system.entity.User;

import java.util.HashMap;

/**
 * Created by 朱思旭 on 2022/1/28.
 */
public interface ResAttService {

    //新增表格
    HashMap<String, Object> insertForm(User user, String sn, String name, String type, String path, String size, String module, Integer resource);

    //获取三种状态的表格（带搜索）
    HashMap<String, Object> getResAttByType(User user, String type, PageInfo pageInfo, String name);

    //删除表格
    Integer delResAtt(User user, Integer id);

    //修改表格
    HashMap<String, Object> updateResAtt(User user, Integer id, String sn, String name);

    //换版表格
    ResAtt changeResAttVersion(User user, Integer id, String type, String path, String size, String module);

    //获取表格的操作记录
    HashMap<String, Object> getResAttHisRecord(Integer id, PageInfo pageInfo);

    //获取表格实体
    ResAtt getResAttSingle(Integer id);

    //获取历史表格实体
    ResAttHis getResAttHisSingle(Integer id);

}
