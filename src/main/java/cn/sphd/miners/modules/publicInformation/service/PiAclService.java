package cn.sphd.miners.modules.publicInformation.service;

import cn.sphd.miners.modules.publicInformation.entity.PiAclDetail;
import cn.sphd.miners.modules.publicInformation.entity.PiAclLog;
import cn.sphd.miners.modules.publicInformation.entity.PiInfoAcl;
import cn.sphd.miners.modules.system.dto.OrganizationDto;
import cn.sphd.miners.modules.system.dto.UserHonePageDto;
import cn.sphd.miners.modules.system.entity.User;

import java.util.Date;
import java.util.List;

public interface PiAclService {

    //获取文件的可见人员数量
    Long piAclNumByFile(Integer fileId);

    //获取文件当前的权限
    List<PiInfoAcl> listPiAcl(Integer userId, Integer fileId);

    //获取文件权限树
    List<OrganizationDto> getPiInfoAuthTree(User user, Integer fileId);

    //批量新增权限
    void addPiInfoAuth(User user, Integer fileId, List<PiInfoAcl> listPiAcl);

    //批量删除权限
    void delPiInfoAuth(User user, Integer fileId, List<PiInfoAcl> listPiAc);

    //获取某机构被赋予过权限的所有人员
    List<UserHonePageDto> listAuthAllUser(User user);

    //根据时间获取权限操作记录
    List<PiAclLog> listAclLog(User user, Date beginDate, Date endDate);

    //获取权限操作记录详情
    List<PiAclDetail> listAclDetail(Integer userId, Integer aclLog);
}
