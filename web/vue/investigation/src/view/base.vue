<template id="base">
  <div class="base">
    <div class="panel"><span class="panelTitle">正在加载……</span></div>
  </div>
</template>
<script>
// wyu：引相关的库
import JsEncrypt from 'jsencrypt/bin/jsencrypt'
import sha1 from 'js-sha1'
export default {
  data () {
    return {
      serverTime: '',
      status:''
    }
  },
  created () {
    // this.$toast.loading({
    //   message: 'Loading...',
    //   forbidClick: true,
    //   loadingType: 'spinner'
    // })
    if (typeof (this.$route.params.item) === 'undefined') {
      console.log('二维码错误，没有参数！')
      this.jumpError('二维码错误，没有参数！')
    }
    this.$http({}).then((response) => { // wyu: 获取服务器时间
      this.serverTime = new Date(response.headers.get('Date'))
      this.checkParams(this.serverTime)
    }, (response) => {
      console.log(response)
      console.log('服务器无法访问！')
      this.jumpError('服务器无法访问！')
    })
  },
  methods: {
    jumpError (message) {
      this.$toast.clear()
      this.$router.replace({name: 'error', params: {message: message}})
    },
    checkParams (now) {
      try {
        console.log('this.$route.params.item=', this.$route.params.item)
        console.log('decodeURIComponent(this.$route.params.item)=',decodeURIComponent(this.$route.params.item))
        let data = this.decrypt(decodeURIComponent(this.$route.params.item))
        let items = JSON.parse(data)
        console.log('url 上的 items = ', items)
        let salt = sha1(items.id + items.userId + items.oid + items.expire_date + 'bC1]gB5@')
        if (salt !== items.salt) {
          console.log('二维码错误，参数不正确！')
          this.jumpError('二维码错误，参数不正确！')
        } else {
          this.checkCache(items,now >= new Date(items.expire_date))
        }
      } catch(e) {
        console.log('二维码错误，参数错误！', e)
        this.jumpError('二维码错误，参数错误！')
      }
    },
    checkCache(items,expired) {
      console.log('再打一次啊', items)
      let wonderssInvestigationObject = window.localStorage.getItem('wonderssInvestigationObject')
      if (typeof wonderssInvestigationObject === 'string' && wonderssInvestigationObject.length > 40) {
        let shaVal = wonderssInvestigationObject.substr(0, 40)
        let data = wonderssInvestigationObject.substr(40)
        try {
          let keys = JSON.parse(this.decrypt(decodeURIComponent(data)))
          console.log('wonderssInvestigationObject 解出来的数据=', keys)
          let salt = sha1(keys.objective.toString() + keys.answerTime + keys.hashKey + 'CPs%1J:3')
          if (salt === shaVal && keys.id === items.id && keys.org === items.oid) {
            this.jumpNext(items.id, items.userId, items.oid, keys.hashKey, keys.objective, keys.answerTime, items.orgName, items.modifyLimit)
            return
          }
        } catch (e) {
          console.log('数据错误，缓存错误！', e)
        }
      }
      if (expired) {
        console.log('二维码已过期！')
        this.jumpError('二维码已过期！')
      } else {
        console.log('这里跳转 jumpNext hashKey=null 了')
        this.jumpNext(items.id, items.userId, items.oid, null)
      }
    },
    jumpNext (id, userId, oid, hashKey, objective, answerTime,orgName, modifyLimit) {
      // wyu：设置系统路径
      if (window.location.href.indexOf('/vue/') > 0) {
        if (typeof window.parent.$ === 'undefined') {
          window.parent.$ = {}
        }
        window.parent.$.webRoot = window.location.href.substring(0, window.location.href.indexOf('/vue/'))
        let wonderssSurveyKey = window.localStorage.getItem('wonderssSurveyKey')
        if (typeof wonderssSurveyKey !== 'string' || wonderssSurveyKey.length !== 32) {
          wonderssSurveyKey = this.uuid()
          window.localStorage.setItem('wonderssSurveyKey', wonderssSurveyKey)
        } else if(hashKey !== wonderssSurveyKey) {
          hashKey = null
        }
        // wyu：注释主干相关更改，等合并主干后可以恢复
        if (hashKey===null) {
          this.jumpOr(id, userId, oid, wonderssSurveyKey,hashKey, objective, answerTime,orgName, modifyLimit,this.serverTime)
        } else {
          let url = '../../../investigationPublish/investigateSubjectDetails.do'
          let data = { 'id': id, 'hashKey':wonderssSurveyKey }
          this.$http.post(url, data, {
            emulateJSON: true
          }).then(response => {
            let res = response.body
            let publishEnabled = res.publishEnabled
            console.log(' publishEnabled', publishEnabled)
            let status = res.status
            console.log(' status', status)
            let topage = 'home'
            if(publishEnabled === 0){ // 停用了问卷
              if(status === 0){ // 未曾答过题
                topage = 'error'
              }else{
                topage = 'home'
              }
            }else if(status === 0){ // 未曾答过题
              topage = 'home'
            }else if(status === 1){ // 答过题且可修改
              topage = 'home'
            }else if(status === 2 ){ // 答过题且不可修改
              topage = 'ans'
            }
            console.log('111 这是前面的 topage=', topage)
            if (topage === 'home') {
              this.$router.replace({name: 'home', params: {id: id, userId: userId, oid: oid, hashKey: wonderssSurveyKey, objective: objective}})
            } else if(topage === 'ans') {
              let ansData = {
                'publishId': res.publishId,
                'orgname': res.orgName,
                'org': res.org,
                'modifyLimit': res.modifyLimit,
              }
              console.log('ansData111', ansData)
              localStorage.setItem('ansdata', JSON.stringify(ansData))
              this.$router.replace({name: 'answer', params: {id: id, userId: userId, oid: oid, hashKey: hashKey, objective: objective, answerTime: answerTime, serverTime: this.serverTime}})

            }


          }).catch(err=>{
            console.log('err=', err)
          })

        }

      } else {
        this.jumpError('服务器路径错误！')
      }
    },
    jumpOr(id, userId, oid, wonderssSurveyKey,hashKey, objective, answerTime,orgName, modifyLimit, serverTime){
      console.log('jumpOr里面的', )
      console.log('objective', objective)
      console.log('hashKey', hashKey)
      let _this = this
      console.log('查询id:' + id)
      let url = '../../../investigationPublish/investigateSubjectDetails.do'
      let data = { 'id': id, 'hashKey':wonderssSurveyKey }
      this.$http.post(url, data, {
        emulateJSON: true
      }).then((response) => {
        _this.$toast.clear()
        console.log(response.body)
        let res = response.body
        let publishEnabled = res.publishEnabled
        console.log(' publishEnabled', publishEnabled)
        let status = res.status
        console.log(' status', status)
        let topage = 'home'
        if(publishEnabled === 0){ // 停用了问卷
          if(status === 0){ // 未曾答过题
            topage = 'error'
          }else{
            topage = 'home'
          }
        }else if(status === 0){ // 未曾答过题
          topage = 'home'
        }else if(status === 1){ // 答过题且可修改
          topage = 'home'
        }else if(status === 2 ){ // 答过题且不可修改
          topage = 'ans'
        }
        console.log('topage=', topage)
        if (topage === 'home') {
          this.$router.replace({name: 'home', params: {id: id, userId: userId, oid: oid, hashKey: wonderssSurveyKey, objective: objective }})
        } else if(topage === 'ans') {
          let ansData = {
            'orgname': res.orgName,
            'publishId': res.publishId,
            'org': res.org,
            'modifyLimit': res.modifyLimit,
          }
          console.log('ansData', ansData)
          localStorage.setItem('ansdata', JSON.stringify(ansData))
          this.$router.replace({name: 'answer', params: {id: id, userId: userId, oid: oid, hashKey: hashKey, objective: objective, answerTime: answerTime, serverTime: serverTime}})
        }else{
          _this.jumpError('本次调查已结束，<br>欢迎下次再参与！')
        }

      }).catch(function (err) {
        console.log('跳转错误')
        console.log(err)
        _this.loading = false
        _this.$toast.fail('系统错误，请重试！')
      })
    },
    uuid() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        let r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8)
        return v.toString(16)
      }).replace(/-/g,'')
    },
    decrypt (data) {
      let jse = new JsEncrypt()
      jse.setPrivateKey(`**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`)
      return jse.decrypt(data)
    },
    encrypt (data) {
      let jse = new JsEncrypt()
      jse.setPublicKey(`-----BEGIN PUBLIC KEY-----
MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAvySpSYm/34kJikFY7MqL
/sGWSbhUvKug94zeAQzvMEvJSkqRLHL1mYt1kluCD0xyqO/2opIiODuzZuskZokB
/VRyAkL8G5Ntgsv48cxvnpnL2fP+TP+PLdZYan7p+Ygs/aKdm01mT94L26kex41M
lkvghzncBfHtYhTH5lWhtmXi7mHPpUKrFVyDjPA3JrTJzZzeQvAdrT8UflHf2/Ea
hQo4EDwZczIiVYVosjiWKBoX4QKPXAJhIVt88Y39/mDaLYeOkDNE/5/jNrGZm3Pl
Wb/xB+mWRf1cH4UZl3qIqI/a9eyhjMFpX5FP3DB97TVg1s49AzLL0exBFjd2jK+Z
LLFShpLVpiN+tJAMJT3t5cvh5Ctf5kuLPp4Bh8+YD+GnuSmfdvK+cpt25atRkdbn
S76KY3u17VdKxRi2kklOl2T7HZQzAK9FPg9e0v99DHYN/4t1R33pm+u5cANF8uAl
INKpipDP2+eDKiYbasGeSe9henPyO1rcu9JFHF0fbZJuup8nhMZLpvmj5B3jHhBs
j36pdwwFGPAbxsv1E7FIaIBb0mf1+xyFWIJnkDOf7UIeDDKHqYEe/2u82D3I1xV8
wWdml7K33E7fpqTi8ShWGV89STAnUTLO1N4yy7A4p90jobYmHte1Amoy1HbvsQJg
x0o+z7lqYGkzb/yE+a67xf8CAwEAAQ==
-----END PUBLIC KEY-----`)
      return jse.encrypt(data)
    }
  }
}
</script>
