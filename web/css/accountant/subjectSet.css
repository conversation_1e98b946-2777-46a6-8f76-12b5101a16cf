body{position:relative;}
.tbl{margin:0 auto 200px; border:1px solid #ccc; position:relative; }
.theader ,.tbody{ text-align:center;  }
.theader{position:absolute; top:0; background:#f1f1f1; width: 100%; border-bottom:1px solid #ccc; font-weight:bolder; box-shadow: 0 1px #ddd;   }
.fl{  margin-right:-1px;}
.fl>span{  line-height:17px;  }
.ttl_control , .fl {border-right:1px solid #ccc;}
.ttl_control{  border-right:0;}
.ttl_control1{  margin-left:2.5%;  }
.tbody{ height:600px; margin-top:35px; overflow-x:hidden; overflow-y:auto;   }
.tr{border-bottom:1px solid #ccc; position:relative;   }
.tr:hover{  background-color: #eef1f5;  }
.clr{ clear:both;   }
.azury{  height: 30px;  }
td.fl1{  text-align: left;  padding:0 1%;  overflow:hidden; white-space: nowrap;text-overflow: ellipsis; }
.mar{ padding-left:50px;   }
#open .fa{ color: #5d9cec; }
#caseStatetable tbody tr td:first-child{  text-align: left;  }
.ty-table td > span[level="0"] {  font-weight: bold;  }
.ty-table td > span[level="1"] {  margin-left: 30px;  }
.ty-table td > span[level="2"] {  margin-left: 50px;  }
.subject-save{padding:30px 0;float:right;overflow: hidden;}
.subject-save span{margin-right:50px;}
.red{color:red;}
.editBtnGroup{padding:30px;float:right;}
#enterInitFinance tr td input{text-align:center;display:inline-block;width:100%;}
.ty-table .border-none{border:none;}
.remain{margin-left:100px;overflow:hidden;}
.balanceYear,.balanceMonth{margin-left:6px;padding:2px 10px;border:1px solid #c6c6c6;background:#ddd;}
.balanceYear {margin-right:100px;}
.ty-table tbody .warningErr{border:1px solid #ff0000;color:red;}
#caseListCheck tr td:first-child{text-align: left;}
.main_input thead tr td{line-height:40px;}
#caseTable tr td{line-height:40px;}
#caseTable tr td input{line-height:20px;}
.alignCenter{line-height:36px;}
.colLeft tbody tr td:first-child{text-align:left;}
#amountCheck .ty-table td{padding:0;}
#reportCheck .ty-table td{padding:0;}
#balanceTip{min-height:230px;}
.kt_tip_ms{text-indent:2em;}
.kt_tip_ms span{color:red;}
.topTip{overflow: hidden;}
#amountCheck .bonceCon,#balanceTip .bonceCon{max-height:550px;overflow-y: auto;}

.associate_tips{  border: 1px solid #5d9cec;  background-color: #fff;  line-height:20px;  padding: 8px 20px;
    margin-top: 8px;  color: #333;  }
.blackTip {  display: inline-block;  position: relative;  padding: 0 5px;  }
.blackTip-arrow {  position: absolute;  width: 0;  height: 0;  border-color: transparent;  border-style: solid;
    top: 50%;  right: 0;  margin-top: -5px;  border-width: 5px 0 5px 5px;  border-left-color: #d9eafd;  }
.blackTip-inner{  padding: 5px 12px;  color: #444;  text-align: center;  background-color: #d9eafd;
    border-radius: 4px;  }
.gapB{  margin-bottom: 30px;  }
.narrowBody{  margin: 0 auto;  width: 80%;  }
.set-item{padding-bottom: 10px;}
.select-active{  background: #bfbfbf;  }
.set-operation span{margin-left: 30px;font-weight: 600;}
.changeDot{padding: 6px 10px;}
.changeDot i{margin-right: 12px;}
.changeDot .fa-dot-circle-o{color:#48cfad ;}
#importantNote{width: 700px;}
.noteCon{  margin: auto;width: 82%;}
.noteCon p span{  margin-right: 4px;}
.topMag {margin-top: 40px;}
#openLink { margin-left: 4px;}
.mainCon{margin:0 96px;display: none;}
.checkSection{padding-top: 20px;}
.back-btn{margin-bottom: 20px;}
.tbScroll table,.tbScroll thead,.tbScroll tbody{display: table;}
.tbScroll thead{table-layout: fixed;width: calc(100% - 6px)}
.tbScroll tbody tr{display: table;  width: 100%;table-layout: fixed;}
.tbScroll tbody{display:block;max-height:500px;overflow-y:auto;overflow-x:hidden;}
.tbScroll tbody{ scrollbar-width: thin;}
.tbScroll tbody::-webkit-scrollbar {  width: 8px;  }
.tbScroll tbody::-webkit-scrollbar-track {  background-color: darkgrey;  }
.tbScroll tbody::-webkit-scrollbar-thumb {  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);  }
#amountCheckTab .ellipsisSet{white-space: nowrap;overflow: hidden;text-overflow: ellipsis;}
.funBtn{width: 134px;height: 32px;line-height: 32px;text-align: center;border-radius: 3px;cursor: pointer;display: inline-block;
    border: none;}
#set_body tr td:nth-child(1),#set_body tr td:nth-child(3),#set_body tr td:nth-child(4),#set_body tr td:nth-child(5),#set_body tr td:nth-child(6){width: 180px;}





