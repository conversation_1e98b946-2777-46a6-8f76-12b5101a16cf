package cn.sphd.miners.modules.generalAffairs.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by Wyu on 2018/10/27.
 * 人员考勤变更表
 */
@Entity
@Table(name = "t_user_department_history")
public class UserDepartmentHistory implements Serializable {
    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="org")
    private Integer org;//机构id

    @Column(name="user")
    private Integer user;//用户id

    @Column(name="effdt")
    private Date effdt;//变更生效日期

    @Column(name="old_dept_id", length = 10)
    private String oldDeptId;//旧部门id

    @Column(name="new_dept_id", length = 10)
    private String newDeptId;//旧部门id

    @Column(name="reason", length = 15)
    private String reason;//变更原因:换部门ExchangDept、离职enroll、入职resign'

    @Column(name="is_used")
    private Boolean isUsed=false;//是否已扫描

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getUser() {
        return user;
    }

    public void setUser(Integer user) {
        this.user = user;
    }

    public Date getEffdt() {
        return effdt;
    }

    public void setEffdt(Date effdt) {
        this.effdt = effdt;
    }

    public String getOldDeptId() {
        return oldDeptId;
    }

    public void setOldDeptId(String oldDeptId) {
        this.oldDeptId = oldDeptId;
    }

    public String getNewDeptId() {
        return newDeptId;
    }

    public void setNewDeptId(String newDeptId) {
        this.newDeptId = newDeptId;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Boolean getUsed() {
        return isUsed;
    }

    public void setUsed(Boolean used) {
        isUsed = used;
    }

    public UserDepartmentHistory() {
    }

    public UserDepartmentHistory(Integer org, Integer user, Date effdt, String oldDeptId, String newDeptId, String reason) {
        this.org = org;
        this.user = user;
        this.effdt = effdt;
        this.oldDeptId = oldDeptId;
        this.newDeptId = newDeptId;
        this.reason = reason;
        this.isUsed = false;
    }
}