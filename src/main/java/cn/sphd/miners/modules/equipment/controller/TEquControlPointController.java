package cn.sphd.miners.modules.equipment.controller;

import java.util.Date;
import java.util.List;
import java.util.Map;

import cn.sphd.miners.modules.equipment.entity.TEquControlImage;
import cn.sphd.miners.modules.equipment.entity.TEquControlPoint;
import cn.sphd.miners.modules.equipment.entity.TEquModel;
import cn.sphd.miners.modules.equipment.service.ITEquControlImageService;
import cn.sphd.miners.modules.equipment.service.ITEquControlPointService;
import cn.sphd.miners.modules.equipment.service.ITEquModelService;
import cn.sphd.miners.modules.mci.utils.AjaxResult;
import cn.sphd.miners.modules.system.entity.User;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import static cn.sphd.miners.modules.mci.utils.AjaxResult.toAjax;

/**
 * 装备管理_控制点
 * 20240407 1.292机器设备之初始设置 新增Controller
 *
 * <AUTHOR>
 * @date 2024-04-13
 */
@Controller
@RequestMapping("/equ/control/point")
public class TEquControlPointController {
    private String prefix = "system/point";

    @Autowired
    private ITEquControlPointService tEquControlPointService;

    @Autowired
    private ITEquControlImageService tEquControlImageService;

    @Autowired
    private ITEquModelService tEquModelService;

    @GetMapping()
    public String point() {
        return prefix + "/point";
    }

    /**
     * 查询装备管理_控制点
     * 20240407 1.292机器设备之初始设置 新增列表
     */
    @PostMapping("/list")
    @ResponseBody
    public List list(TEquControlPoint tEquControlPoint) {
        List<TEquControlPoint> list = tEquControlPointService.selectTEquControlPointList(tEquControlPoint);
        return list;
    }

    @RequestMapping("/pointSet")
    @ResponseBody
    public AjaxResult pointSet(TEquModel model,User user) {
        TEquModel m = tEquModelService.selectTEquModelById(model.getId());
        if (m != null&&model.getState()!=null) {

            //-1-尚未设置,-2-不在此设置,0-无控制点

            if (model.getState() == 1){//尚未设置
                m.setPointState(-1);
            }
            else if (model.getState() == 2) {//暂无控制点
                m.setPointState(0);
            }else if (model.getState() == 3) {//不在此处设置
                m.setPointState(-2);
            }else if(model.getState() == 4){
                m.setPointState(m.getPointCount());
            }else {
                return AjaxResult.warn("参数错误");
            }
        }
        tEquModelService.updateTEquModelByModel(m, user);
        return AjaxResult.success();
    }

    @RequestMapping("/listWithMc")
    @ResponseBody
    public List listWithMc(TEquControlPoint tEquControlPoint) {
        List<Map<String, Object>> maps = tEquControlPointService.selectTEquControlPointListWithPics(tEquControlPoint);
        return maps;
    }

    /**
     * 新增装备管理_控制点
     * 20240407 1.292机器设备之初始设置 新增
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存装备管理_控制点
     * 20240407 1.292机器设备之初始设置 新增
     */

    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(TEquControlPoint tEquControlPoint, User user) {
        tEquControlPoint.setOrg(Long.valueOf(user.getOid()));
        tEquControlPoint.setCreateDate(new Date());
        tEquControlPoint.setCreateName(user.getUserName());
        tEquControlPointService.insertTEquControlPoint(tEquControlPoint);


        TEquModel model = tEquModelService.selectTEquModelById(tEquControlPoint.getModel());
        Integer p = model.getPointCount();
        if(p==null||p==-1){
            p=0;
        }
        model.setPointCount(p+1);
        tEquModelService.updateTEquModelByModel(model, user);
        if (StringUtils.isNotBlank(tEquControlPoint.getPics())) {
            for (String s : tEquControlPoint.getPics().split(",")) {
                TEquControlImage image = new TEquControlImage();
                image.setOrg(Long.valueOf(user.getOid()));
                image.setNormalPath(s);
                image.setControlPoint(tEquControlPoint.getId());
                image.setCreateDate(new Date());
                image.setCreateName(user.getUserName());
                tEquControlImageService.insertTEquControlImage(image);
            }

        }
        return toAjax(1);
    }

    /**
     * 修改装备管理_控制点
     * 20240407 1.292机器设备之初始设置 新增
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        TEquControlPoint tEquControlPoint = tEquControlPointService.selectTEquControlPointById(id);
        mmap.put("tEquControlPoint", tEquControlPoint);
        return prefix + "/edit";
    }

    /**
     * 修改保存装备管理_控制点
     * 20240407 1.292机器设备之初始设置 新增
     */

    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(TEquControlPoint tEquControlPoint, User user) {
        TEquControlImage image = new TEquControlImage();
        image.setControlPoint(tEquControlPoint.getId());
        List<TEquControlImage> images = tEquControlImageService.selectTEquControlImageList(image);
        if (images != null && !images.isEmpty()) {
            for (TEquControlImage tEquControlImage : images) {
                tEquControlImageService.deleteTEquControlImageById(tEquControlImage.getId());
            }
        }

        //修改时若改变了图片 则删除原有图片新增新的
        if (StringUtils.isNotBlank(tEquControlPoint.getPics())) {
            for (String s : tEquControlPoint.getPics().split(",")) {
                TEquControlImage imageNew = new TEquControlImage();
                imageNew.setOrg(Long.valueOf(user.getOid()));
                imageNew.setNormalPath(s);
                imageNew.setControlPoint(tEquControlPoint.getId());
                imageNew.setCreateDate(new Date());
                imageNew.setCreateName(user.getUserName());
                tEquControlImageService.insertTEquControlImage(imageNew);
            }
        }


        tEquControlPoint.setUpdateDate(new Date());
        tEquControlPoint.setUpdator(Long.valueOf(user.getUserID()));
        tEquControlPoint.setUpdateName(user.getUserName());
        return toAjax(tEquControlPointService.updateTEquControlPoint(tEquControlPoint));
    }

    //批量启用
    @PostMapping("/enable")
    @ResponseBody
    public AjaxResult enable(String ids) {
        return toAjax(tEquControlPointService.enable(ids));
    }

    /**
     * 删除装备管理_控制点
     * 20240407 1.292机器设备之初始设置 新增
     */

    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {

        return toAjax(tEquControlPointService.deleteTEquControlPointByIds(ids));
    }
}
