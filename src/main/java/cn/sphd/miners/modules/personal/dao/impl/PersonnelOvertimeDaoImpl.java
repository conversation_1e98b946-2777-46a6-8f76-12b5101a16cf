package cn.sphd.miners.modules.personal.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.personal.dao.PersonnelOvertimeDao;
import cn.sphd.miners.modules.personal.entity.PersonnelOvertime;
import org.hibernate.Query;
import org.hibernate.SQLQuery;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2016/10/25.
 */
@Repository
public class PersonnelOvertimeDaoImpl extends BaseDao<PersonnelOvertime,Serializable> implements PersonnelOvertimeDao {
}
