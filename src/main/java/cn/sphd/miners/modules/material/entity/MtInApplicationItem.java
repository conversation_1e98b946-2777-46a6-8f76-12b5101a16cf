package cn.sphd.miners.modules.material.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2019-10-12 
 */

@Entity ( name ="MtInApplicationItem" )
@Table ( name ="t_mt_in_application_item" )
public class MtInApplicationItem  implements Serializable {

	private static final long serialVersionUID =  7318376596379219751L;

	@Id
   	@Column(name = "id" )
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * 申请人ID
	 */
   	@Column(name = "applicant" )
	private Integer applicant;

	/**
	 * 申请人姓名
	 */
   	@Column(name = "applicant_name" )
	private String applicantName;

	/**
	 * 申请实例ID
	 */
   	@Column(name = "instance" )
	private Integer instance;

	/**
	 * 申请实例ID链,以逗号分隔
	 */
   	@Column(name = "instance_chain" )
	private String instanceChain;

	/**
	 * 采购单明细ID
	 */
   	@Column(name = "order_item" )
	private Integer orderItem;

	/**
	 * 不合格信息
	 */
   	@Column(name = "unqualified_info" )
	private String unqualifiedInfo;

	/**
	 * check
	 */
	@Column(name = "check_name" )
	private String checkName;

	/**
	 * check
	 */
	@Column(name = "check_date" )
	private Date checkDate;

	/**
	 * 不合格品处理意见:1-退货,2-通知供应商,由其到现场挑选,3-通知供应商,由其到现场返工,4-代供应商挑选,5-代供应商返工,A-由于紧急需要，建议特采，提出让步申请
	 */
   	@Column(name = "unqualified_handle" )
	private String unqualifiedHandle;

	/**
	 * 计划到达日期
	 */
   	@Column(name = "arrive_date" )
	private Date arriveDate;

	/**
	 * 审批类型:1-正常审批,2-让步审批
	 */
   	@Column(name = "approval_type" )
	private String approvalType;

	/**
	 * 审批结果:1-通过,0-否决,为空时说明正在审批中
	 */
   	@Column(name = "result_type" )
	private String resultType;

	/**
	 * 数量类型:1-数量一致,0-数量不致
	 */
   	@Column(name = "quantity_type" )
	private String quantityType;

	/**
	 * 不让步理由
	 */
   	@Column(name = "reject_reason" )
	private String rejectReason;

	/**
	 * 让步时间
	 */
	@Column(name = "reject_date" )
	private Date rejectDate;

	/**
	 * 让步名
	 */
	@Column(name = "reject_name" )
	private String rejectName;

	/**
	 * 让步审批时间
	 */
	@Column(name = "reject_approval_date" )
	private Date rejectApprovalDate;

	/**
	 * 让步审批名
	 */
	@Column(name = "reject_approval_name" )
	private String rejectApprovalName;


	/**
	 * 计划入库数量
	 */
   	@Column(name = "quantity_plan" )
	private BigDecimal quantityPlan;

	/**
	 * 实际入库数量,通过三个type字段可判断出是哪种情况入库,拒绝时为0
	 */
   	@Column(name = "quantity_fact" )
	private BigDecimal quantityFact;

	/**
	 * 状态：1-采购录入,2-采购提交,3-检验审批通过,4-检验审批否决,5-让步申请，6-让步审批通过,7-让步审批否决,8-仓库确认,9-仓库数量异议,A- 采购确认
            
	 */
   	@Column(name = "state" )
	private String state;

	/**
	 * 备注
	 */
   	@Column(name = "memo" )
	private String memo;

	/**
	 * 描述
	 */
   	@Column(name = "description" )
	private String description;

	/**
	 * 创建人id,实际为采购入库人
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_date" )
	private Date createDate;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_date" )
	private Date updateDate;

   	@Column(name = "material")

	private Integer material;


	/**
	 * 质检员ID
	 */
	@Column(name = "inspector")
	private Integer inspector;
	/**
	 * 质检员姓名
	 */
	@Column(name = "inspector_name")
	private String inspectorName;

	//1.316新增字段 begin
	/**
	 * 检验状态:0-待检验,1-检验通过,2-检验否决
	 */
	@Column(name = "inspect_state")
	private Integer inspectState;

	/**
	 * 库管包装检验情况:0-不一致,1-一致
	 */
	@Column(name = "packaging_info")
	private Integer packagingInfo;

	/**
	 * 库管包装检验结果:1-包装完好,无问题; 2-包装有瑕疵但可接受;3-有不可接受的包装;1,2为检验通过
	 */
	@Column(name = "packaging_result")
	private Integer packagingResult;

	/**
	 * 检验合格数量
	 */
	@Column(name = "inspect_quantity")
	private BigDecimal inspectQuantity;

	/**
	 * 检验包装检验情况:0-不一致,1-一致
	 */
	@Column(name = "inspect_packaging_info")
	private Integer inspectPackagingInfo;

	/**
	 * 检验包装检验结果:1-包装完好,无问题; 2-包装有瑕疵但可接受;3-有不可接受的包装;1,2为检验通过
	 */
	@Column(name = "inspect_packaging_result")
	private Integer inspectPackagingResult;

	/**
	 * 检验质量检查结果:0-不合格,1-合格
	 */
	@Column(name = "inspect_quality_result")
	private Integer inspectQualityResult;

	/**
	 * 检验不合格信息
	 */
	@Column(name = "inspect_quality_info")
	private String inspectQualityInfo;

	/**
	 * 包装个数
	 */
	@Column(name = "packaging_count")
	private Integer packagingCount;

	/**
	 * 已分配库位包装个数
	 */
	@Column(name = "location_assigned")
	private Integer locationAssigned;

	/**
	 * 占用库位个数
	 */
	@Column(name = "location_count")
	private Integer locationCount;

	@Column(name = "expiration_date")
	private Date expirationDate;
	//1.316新增字段 end



	public static long getSerialVersionUID() {
		return serialVersionUID;
	}


	public String getApplicantName() {
		return applicantName;
	}

	public void setApplicantName(String applicantName) {
		this.applicantName = applicantName;
	}


	public String getInstanceChain() {
		return instanceChain;
	}

	public void setInstanceChain(String instanceChain) {
		this.instanceChain = instanceChain;
	}

	public BigDecimal getQuantityPlan() {
		return quantityPlan;
	}

	public void setQuantityPlan(BigDecimal quantityPlan) {
		this.quantityPlan = quantityPlan;
	}

	public BigDecimal getQuantityFact() {
		return quantityFact;
	}

	public void setQuantityFact(BigDecimal quantityFact) {
		this.quantityFact = quantityFact;
	}

	public String getUnqualifiedInfo() {
		return unqualifiedInfo;
	}

	public void setUnqualifiedInfo(String unqualifiedInfo) {
		this.unqualifiedInfo = unqualifiedInfo;
	}

	public String getUnqualifiedHandle() {
		return unqualifiedHandle;
	}

	public void setUnqualifiedHandle(String unqualifiedHandle) {
		this.unqualifiedHandle = unqualifiedHandle;
	}

	public Date getArriveDate() {
		return arriveDate;
	}

	public void setArriveDate(Date arriveDate) {
		this.arriveDate = arriveDate;
	}

	public String getApprovalType() {
		return approvalType;
	}

	public void setApprovalType(String approvalType) {
		this.approvalType = approvalType;
	}

	public String getResultType() {
		return resultType;
	}

	public void setResultType(String resultType) {
		this.resultType = resultType;
	}

	public String getQuantityType() {
		return quantityType;
	}

	public void setQuantityType(String quantityType) {
		this.quantityType = quantityType;
	}

	public String getRejectReason() {
		return rejectReason;
	}

	public void setRejectReason(String rejectReason) {
		this.rejectReason = rejectReason;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Integer getCreator() {
		return creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
		return updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getApplicant() {
		return applicant;
	}

	public void setApplicant(Integer applicant) {
		this.applicant = applicant;
	}

	public Integer getInstance() {
		return instance;
	}

	public void setInstance(Integer instance) {
		this.instance = instance;
	}

	public Integer getOrderItem() {
		return orderItem;
	}

	public void setOrderItem(Integer orderItem) {
		this.orderItem = orderItem;
	}

	public Integer getMaterial() {
		return material;
	}

	public Date getRejectDate() {
		return rejectDate;
	}

	public void setRejectDate(Date rejectDate) {
		this.rejectDate = rejectDate;
	}

	public String getRejectName() {
		return rejectName;
	}

	public void setRejectName(String rejectName) {
		this.rejectName = rejectName;
	}

	public void setMaterial(Integer material) {
		this.material = material;
	}

	public Date getRejectApprovalDate() {
		return rejectApprovalDate;
	}

	public String getCheckName() {
		return checkName;
	}

	public void setCheckName(String checkName) {
		this.checkName = checkName;
	}

	public Date getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(Date checkDate) {
		this.checkDate = checkDate;
	}

	public void setRejectApprovalDate(Date rejectApprovalDate) {
		this.rejectApprovalDate = rejectApprovalDate;
	}

	public String getRejectApprovalName() {
		return rejectApprovalName;
	}

	public void setRejectApprovalName(String rejectApprovalName) {
		this.rejectApprovalName = rejectApprovalName;
	}

	public Integer getInspector() {
		return inspector;
	}

	public void setInspector(Integer inspector) {
		this.inspector = inspector;
	}

	public String getInspectorName() {
		return inspectorName;
	}

	public void setInspectorName(String inspectorName) {
		this.inspectorName = inspectorName;
	}

	public Integer getInspectState() {
		return inspectState;
	}

	public void setInspectState(Integer inspectState) {
		this.inspectState = inspectState;
	}

	public Integer getPackagingInfo() {
		return packagingInfo;
	}

	public void setPackagingInfo(Integer packagingInfo) {
		this.packagingInfo = packagingInfo;
	}

	public Integer getPackagingResult() {
		return packagingResult;
	}

	public void setPackagingResult(Integer packagingResult) {
		this.packagingResult = packagingResult;
	}

	public BigDecimal getInspectQuantity() {
		return inspectQuantity;
	}

	public void setInspectQuantity(BigDecimal inspectQuantity) {
		this.inspectQuantity = inspectQuantity;
	}

	public Integer getInspectPackagingInfo() {
		return inspectPackagingInfo;
	}

	public void setInspectPackagingInfo(Integer inspectPackagingInfo) {
		this.inspectPackagingInfo = inspectPackagingInfo;
	}

	public Integer getInspectPackagingResult() {
		return inspectPackagingResult;
	}

	public void setInspectPackagingResult(Integer inspectPackagingResult) {
		this.inspectPackagingResult = inspectPackagingResult;
	}

	public Integer getInspectQualityResult() {
		return inspectQualityResult;
	}

	public void setInspectQualityResult(Integer inspectQualityResult) {
		this.inspectQualityResult = inspectQualityResult;
	}

	public String getInspectQualityInfo() {
		return inspectQualityInfo;
	}

	public void setInspectQualityInfo(String inspectQualityInfo) {
		this.inspectQualityInfo = inspectQualityInfo;
	}

	public Integer getPackagingCount() {
		return packagingCount;
	}

	public void setPackagingCount(Integer packagingCount) {
		this.packagingCount = packagingCount;
	}

	public Integer getLocationAssigned() {
		return locationAssigned;
	}

	public void setLocationAssigned(Integer locationAssigned) {
		this.locationAssigned = locationAssigned;
	}

	public Integer getLocationCount() {
		return locationCount;
	}

	public void setLocationCount(Integer locationCount) {
		this.locationCount = locationCount;
	}

	public Date getExpirationDate() {
		return expirationDate;
	}

	public void setExpirationDate(Date expirationDate) {
		this.expirationDate = expirationDate;
	}
}
