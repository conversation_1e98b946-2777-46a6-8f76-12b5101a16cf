<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.accountant.mapper.StatetementDefMapper">

    <select id="getSingle" resultType="statementDef" parameterType="statementDef" >
        select
        id, code, cell_code, formula, balance_in AS balanceIn, balance_out AS balanceOut,
        memo, creator, create_name AS createName,create_date AS createDate, updator,
        update_name AS updateName, update_date AS updateDate, approve_Item AS approveItem,
        approve_status AS approveStatus, approve_level AS approveLevel,
        auditor, auditor_name AS auditorName, audit_date AS auditDate, operation,
        apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId
        from
        t_accountant_statement_def
        where
        id = #{id}
    </select>

    <select id="listPage" resultType="statementDef" parameterType="hashmap" >
        select
        id, code, cell_code, formula, balance_in AS balanceIn, balance_out AS balanceOut,
        memo, creator, create_name AS createName,create_date AS createDate, updator,
        update_name AS updateName, update_date AS updateDate, approve_Item AS approveItem,
        approve_status AS approveStatus, approve_level AS approveLevel,
        auditor, auditor_name AS auditorName, audit_date AS auditDate, operation,
        apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId
        from
        t_accountant_statement_def
    </select>

    <select id="listByOrgStatementDef" resultType="statementDef">
        select
        a.id, a.code, a.cell_code AS cellCode, a.formula
        from
        t_accountant_statement_def a
        where
        a.code = #{code}
    </select>

    <insert id="insert" parameterType="statementDef" useGeneratedKeys="true" keyProperty="id" >
        insert into t_accountant_statement_def (id, code, cell_code,
        formula, balance_in, balance_out,
        memo, creator, create_name,
        create_date, updator, update_name,
        update_date, approve_Item, approve_status,
        approve_level, auditor, auditor_name,
        audit_date, operation, apply_memo,
        approve_memo, message_id)
        values (#{id}, #{code}, #{cellCode},
        #{formula}, #{balanceIn}, #{balanceOut},
        #{memo}, #{creator}, #{createName},
        #{createDate}, #{updator}, #{updateName},
        #{updateDate}, #{approveItem}, #{approveStatus},
        #{approveLevel}, #{auditor}, #{auditorName},
        #{auditDate}, #{operation}, #{applyMemo},
        #{approveMemo}, #{messageId})
    </insert>

    <update id="update" parameterType="statementDef" >
        update t_accountant_statement_def
        <set >
            <if test="code != null" >
                code = #{code},
            </if>
            <if test="cellCode != null" >
                cell_code = #{cellCode},
            </if>
            <if test="formula != null" >
                formula = #{formula},
            </if>
            <if test="balanceIn != null" >
                balance_in = #{balanceIn},
            </if>
            <if test="balanceOut != null" >
                balance_out = #{balanceOut},
            </if>
            <if test="memo != null" >
                memo = #{memo},
            </if>
            <if test="creator != null" >
                creator = #{creator},
            </if>
            <if test="createName != null" >
                create_name = #{createName},
            </if>
            <if test="createDate != null" >
                create_date = #{createDate},
            </if>
            <if test="updator != null" >
                updator = #{updator},
            </if>
            <if test="updateName != null" >
                update_name = #{updateName},
            </if>
            <if test="updateDate != null" >
                update_date = #{updateDate},
            </if>
            <if test="approveItem != null" >
                approve_Item = #{approveItem},
            </if>
            <if test="approveStatus != null" >
                approve_status = #{approveStatus},
            </if>
            <if test="approveLevel != null" >
                approve_level = #{approveLevel},
            </if>
            <if test="auditor != null" >
                auditor = #{auditor},
            </if>
            <if test="auditorName != null" >
                auditor_name = #{auditorName},
            </if>
            <if test="auditDate != null" >
                audit_date = #{auditDate},
            </if>
            <if test="operation != null" >
                operation = #{operation},
            </if>
            <if test="applyMemo != null" >
                apply_memo = #{applyMemo},
            </if>
            <if test="approveMemo != null" >
                approve_memo = #{approveMemo},
            </if>
            <if test="messageId != null" >
                message_id = #{messageId},
            </if>
        </set>
        where id = #{id}
    </update>

    <delete id="delete" parameterType="statementDef" >
        delete from t_accountant_statement_def
        where id = #{id}
    </delete>
</mapper>