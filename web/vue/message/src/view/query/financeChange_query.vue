<template>
  <div id="financeChangeQuery" class="financeChangeQuery">
    <TY_NavTop title="财务修改"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div style="padding: 0 4px;">
        <div class="tip tip-success">您可更换查询条件，以得到其他查询结果。</div>
      </div>
      <div class="ui-message">
        <div class="sendContainer">
          <el-form :model="queryForm" ref="queryForm" label-width="70px">
            <div>
              <h4 class="item-header">提交时间</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.type" size="small">
                  <el-radio label="1" border>近七日</el-radio>
                  <el-radio label="2" border>本月</el-radio>
                  <el-radio label="3" border>自定义</el-radio>
                </el-radio-group>
              </div>
              <div class="el-form-item" v-show="queryForm.type === '3'">
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.beginDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker> -
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.endDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker>
              </div>
            </div>
            <div>
              <h4 class="item-header">审批结果</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.approveStatus" size="small">
                  <el-radio label="2" border>已批准</el-radio>
                  <el-radio label="3" border>已驳回</el-radio>
                </el-radio-group>
              </div>
            </div>
            <div class="handle_button" style="margin-top: 20px">
              <input type="submit" class="ui-btn ui-btn_info" value="确定" @click="submit">
            </div>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
  .financeChangeQuery .el-radio-group .el-radio {
    display: inline-block;
    margin: 0 5px 5px 0;
  }
</style>

<script>
// import { formatDate, filterLeaveType } from '../../js/common'
export default {
  name: 'applyReimburseQuery',
  data () {
    return {
      queryForm: {
        type: '1',
        beginDate: '',
        endDate: '',
        approveStatus: '2'
      },
      billCat: [],
      feeCat: [],
      secondFeeCatList: [],
      name: ''
    }
  },
  created () {
    let name = this.$route.params.name
    this.name = name
  },
  methods: {
    submit: function () {
      // type 1-近七日，2-本月，3-自定义
      // 自定义时传 beginDate 开始时间，endDate 结束时间
      // approveStatus 2- 批准 3- 驳回
      // feeCat 费用类别id
      // feeCatName 费用类别名称
      // billCat 票据种类 id
      // billCatName 票据种类名称
      let queryParam = {
        approvalStatus: this.queryForm.approveStatus,
        beginTime: this.queryForm.beginDate,
        endTime: this.queryForm.endDate,
        type: this.queryForm.type,
        userId: this.sphdSocket.user.userID,
        session: this.sphdSocket.sessionid,
        name: this.name
      }
      console.log(JSON.stringify(queryParam))
      localStorage.setItem('query', JSON.stringify(queryParam))
      this.$router.push({
        path: '/financeChangeQueryPer',
        name: 'financeChangeQueryPer',
        params: {
          data: queryParam
        }
      })
    }
  }
}
</script>
