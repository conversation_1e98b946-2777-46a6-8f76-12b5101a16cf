import Vue from 'vue'
import Vuex from 'vuex'
import actions from './action'

Vue.use(Vuex)

const state = {
  // menu: JSON.parse(localStorage.getItem('messageMenu')) || [], // 首页的菜单
  menu: [
    { 'code': 'handle', 'corner': 0, 'description': '悬浮窗最上层', 'id': 1, 'isMenu': 0, 'isView': '1', 'level': 0, 'mid': '', 'name': '处理', 'orders': 10, 'path': '/handle/', 'pid': 0 },
    { 'code': 'apply', 'corner': 0, 'id': 2, 'isMenu': 0, 'isView': '2', 'level': 1, 'mid': '', 'name': '我的申请', 'orders': 20, 'path': '/handle/apply/', 'pid': 1 },
    { 'code': 'approval', 'corner': 0, 'id': 3, 'isMenu': 0, 'isView': '3', 'level': 1, 'mid': '', 'name': '我要审批', 'orders': 20, 'path': '/handle/approval/', 'pid': 1 },
    { 'code': 'message', 'corner': 0, 'description': '', 'id': 4, 'isMenu': 0, 'isView': '4', 'level': 1, 'mid': '', 'name': '我的消息', 'orders': 20, 'path': '/handle/message/', 'pid': 1, 'serviceClass': 'userSuspendMsgService' }
  ], // 首页的菜单
  version: 0,
  messageCountsAll: 0, // 消息的数目
  messageDisappear: false, // 我的消息（我的消息和即将消失的消息）
  homeNav: 1, // 标记首页的tab
  overTimeNav: '1', // 开票申请各状态列表数据
  leaveNav: '1', // 开票申请各状态列表数据
  invoiceList: null, // 开票申请各状态列表数据
  invoiceNav: 1, // 标记开票申请、开票审批的tab
  invoiceFinNav: 1, // 标记开票申请、开票审批的tab
  fileNav: 1, // 文件审批
  fileList: { fileHandle: [], fileApprove: [] }, // 文件审批请各状态列表数据
  complaintList: null, // 投诉列表
  complaintNav: 1, // 投诉的tab
  complaintHe: null, // 投诉标记是否核心人物:1-是，2-否
  billParam: {}, // 票据传值
  reimburseNav: '1', // 审批者报销导航
  finReimburseNav: '1', // 财务报销导航
  payReimburseNav: '1', // 付款复核导航
  potentialNav: '1',
  saleManageNav: '1', // 受理导航tab
  potentialEditId: '', //
  potentialEditSonId: '', // 联系人、访谈记录id,修改时用
  potentialPer: 1, // 查询1-不带有申请人的查询 2-带有申请人的查询
  potentialRecordType: 1, // 修改记录
  materialParam: {}, // 物料传值
  approvalSettingSearchInfo: {}, // 审批设置搜索的条件
  purchaseNav: '1', // 入库导航
  purchaseInvoice: {}, // 采购订单 票据处理 付款选择的 票据信息
  invoiceTicketInfo: {}, // 采购订单 票据处理 查看 票据详情
  attendanceLeaveParam: {},
  cusorg: {},
  purchaseFinanceInfo: {},
  beRecoveredInfo:{}
}

const mutations = {
  SET_MENU (state, userBadge) { // 调用：this.$store.commit('setMenu',obj)
    console.log('store index.js SET_MENU state.menu', typeof state.menu, state.menu, JSON.stringify(state.menu), state.menu.length)
    // wyu：
    // 1）state.menu为空数组的时候取$store.state.menu[1]报错；
    // 2）run dev加载本js的时候 localStorage.getItem('messageMenu') 为 null，需要在首次调用后台/sys/getAllMenu.do后调用store.dispatch('setNewMenu', userBadge)赋值。
    let newMenu = state.menu.length > 4 ? state.menu : JSON.parse(localStorage.getItem('messageMenu')) || state.menu
    // // 必须深拷贝（查了了一晚上）
    // let newMenu = state.menu.length > 0 ? JSON.parse(JSON.stringify(state.menu)) : JSON.parse(localStorage.getItem('messageMenu')) || []
    console.log('store index.js SET_MENU newMenu', typeof newMenu, newMenu)
    for (var i in newMenu) {
      for (var key in userBadge.list) {
        if (newMenu[i].code === key) {
          // //wyu：测试表明，报错的都是空字符串
          // if(isNaN(parseInt(userBadge.list[key]))) {
          //   console.log('SET_MENU userBadge.list',key, userBadge.list[key], '+'+userBadge.list[key]+'+')
          // }
          newMenu[i].corner = isNaN(parseInt(userBadge.list[key])) ? 0 : parseInt(userBadge.list[key])
        }
      }
    }
    window.localStorage.setItem('userBadgeNumbers', JSON.stringify(userBadge))
    state.menu = newMenu
    console.log('store index.js SET_MENU state.menu', state.menu)
  },
  SET_VERSION (state, userBadge) {
    state.version = userBadge.ver
  },
  SET_MsgCount (state, num) {
    state.messageCountsAll = num
  },
  SET_MSGDISAPPEAR (state, bool) {
    state.messageDisappear = bool
  },
  SET_HOMENAV (state, num) {
    state.homeNav = num
  },
  SET_OVERTIMENAV (state, num) {
    state.overTimeNav = num
  },
  SET_LEAVENAV (state, num) {
    state.leaveNav = num
  },
  SET_INVOICELIST (state, obj) {
    state.invoiceList = obj
  },
  SET_INVOICENAV (state, num) {
    state.invoiceNav = num
  },
  SET_INVOICEFINNAV (state, num) {
    state.invoiceFinNav = num
  },
  SET_FILENAV (state, num) {
    state.fileNav = num
  },
  SET_FILELIST (state, obj) {
    state.fileList = obj
  },
  SET_COMPLAINTLIST (state, obj) {
    state.complaintList = obj
  },
  SET_COMPLAINTNAV (state, num) {
    state.complaintNav = num
  },
  SET_COMPLAINTHE (state, num) {
    state.complaintHe = num
  },
  SET_BillPARAM (state, obj) {
    state.billParam = obj
  },
  SET_REIMBURSENAV (state, num) {
    state.reimburseNav = num
  },
  SET_FINREIMBURSENAV (state, num) {
    state.finReimburseNav = num
  },
  SET_PAYREIMBURSENAV (state, num) {
    state.payReimburseNav = num
  },
  SET_POTENTIALNAV (state, num) {
    state.potentialNav = num
  },
  SET_SALEMANAGENAV (state, num) {
    state.saleManageNav = num
  },
  SET_MATERIALPARAM (state, obj) {
    state.materialParam = obj
  },
  SET_APPROVALSETTINGSEARCHINFO (state, obj) {
    state.approvalSettingSearchInfo = obj
  },
  SET_PURCHASENAV (state, num) {
    state.purchaseNav = num
  },
  SET_PURCHASEINVOICE (state, obj) {
    state.purchaseInvoice = obj
  },
  SET_INVOICETICKETINFO (state, obj) {
    state.invoiceTicketInfo = obj
  },
  SET_ATTENDANCELEAVEPARAM (state, obj) {
    state.attendanceLeaveParam = obj
  },
  SET_CUSORG (state, obj) {
    state.cusorg = obj
  },
  SET_PURCHASEFINANCEINFO (state, obj) {
    state.purchaseFinanceInfo = obj
  },
  SETBERECOVEREDINFO (state, obj) {
    state.beRecoveredInfo = obj
  }

}
const getters = {
  getMenu (state) {
    return state.menu
  },
  getVersion (state) {
    return state.version
  },
  getMsgCount (state) {
    return state.messageCountsAll
  },
  getMsgDisappear (state) {
    return state.messageDisappear
  },
  getHomeNav (state) {
    return state.homeNav
  },
  getOverTimeNav (state) {
    return state.overTimeNav
  },
  getLeaveNav (state) {
    return state.leaveNav
  },
  getInvoiceList (state) {
    return state.invoiceList
  },
  getInvoiceNav (state) {
    return state.invoiceNav
  },
  getInvoiceFinNav (state) {
    return state.invoiceFinNav
  },
  getFileNav (state) {
    return state.fileNav
  },
  getFileList (state) {
    return state.fileList
  },
  getComplaintList (state) {
    return state.complaintList
  },
  getComplaintNav (state) {
    return state.complaintNav
  },
  getComplaintHe (state) {
    return state.complaintHe
  },
  getBillParam (state) {
    return state.billParam
  },
  getReimburseNav (state) {
    return state.reimburseNav
  },
  getFinReimburseNav (state) {
    return state.finReimburseNav
  },
  getPayReimburseNav (state) {
    return state.payReimburseNav
  },
  getPotentialNav (state) {
    return state.potentialNav
  },
  getSaleManageNav (state) {
    return state.saleManageNav
  },
  getMaterialParam (state) {
    return state.materialParam
  },
  getPurchaseNav (state) {
    return state.purchaseNav
  },
  getPurchaseInvoice (state) {
    return state.purchaseInvoice
  },
  getInvoiceTicketInfo (state) {
    return state.invoiceTicketInfo
  },
  getApprovalSettingSearchInfo (state) {
    return state.approvalSettingSearchInfo
  },
  getAttendanceLeaveParam (state) {
    return state.attendanceLeaveParam
  },
  getCusorg (state) {
    return state.cusorg || ''
  },
  getPurchaseFinanceInfo () {
    return state.purchaseFinanceInfo
  },
  getBeRecoveredInfo () {
    return state.beRecoveredInfo
  }
}

export default new Vuex.Store({
  state,
  mutations,
  getters,
  actions
})
