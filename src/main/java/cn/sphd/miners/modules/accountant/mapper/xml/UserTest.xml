<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.sphd.miners.modules.accountant.mapper.UserTestMapper">
    <insert id="insert" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user(username,password) VALUES (#{username},#{password})
    </insert>
    <update id="update">
        UPDATE user SET username=#{username},password=#{password} where id=d
    </update>
    <insert id="insertSwap" useGeneratedKeys="false">
        INSERT INTO user (username,password) SELECT username,password FROM user_history WHERE id=1
    </insert>

    <!--<select id="listPage" parameterType="java.util.Map" resultType="userTest">-->
        <!--select * from "user";-->
    <!--</select>-->

    <!--<select id="getSingle" parameterType="userTest" resultType="userTest">-->
        <!--SELECT * FROM user WHERE username=#{username} AND password=#{password}-->
    <!--</select>-->

    <!--<insert id="insert" parameterType="userTest" useGeneratedKeys="true">-->
        <!--INSERT INTO user (UserTest.id,UserTest.username,password) VALUES (#{id},#{username},#{password})-->
    <!--</insert>-->


    <!--<update id="update" parameterType="userTest">-->
        <!--UPDATE user SET username=#{username},password='999999' WHERE id=10-->
    <!--</update>-->

    <!--<delete id="delete" parameterType="int">-->
        <!--DELETE FROM user WHERE id=sd-->
    <!--</delete>-->

    <delete id="deleteAll" parameterType="int">
        DELETE  FROM  user WHERE id=#{id}

    </delete>

    <select id="selectPage"  resultType="userTest">

        SELECT * FROM  user
    </select>

</mapper>