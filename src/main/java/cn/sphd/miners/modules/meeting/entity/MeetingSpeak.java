package cn.sphd.miners.modules.meeting.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.util.Date;

//会议管理_发言表
@Entity
@Table(name = "t_meeting_speak")
public class MeetingSpeak {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name="org"  , nullable=true , unique=false)
    private Integer org;//机构ID

    @Column(name="meeting"  ,nullable=true , unique=false, insertable=false, updatable=false)
    private Long meetingId;//会议ID

    @Column(name="user"  , nullable=true , unique=false)
    private Integer user;//'发言人ID'

    @Column(name="user_name"  , length=50 , nullable=true , unique=false)
    private String userName;   //发言人

    @Column(name="post"  , nullable=true , unique=false)
    private Integer post;//职位ID

    @Column(name="topic"  , length=50 , nullable=true , unique=false)
    private String topic;   //发言主题

    @Column(name="summary"  , length=255 , nullable=true , unique=false)
    private String summary;   //摘要

    @Column(name="speak_time"   , nullable=true , unique=false)
    private Date speakTime;  //发言时间

    @Column(name="orders"  , nullable=true , unique=false)
    private Integer orders;//排序

    @Column(name="memo"  ,length = 255, nullable=true , unique=false)
    private String memo;//备注

    @Column(name="creator"  ,nullable=true , unique=false)
    private Integer creator;   //创建人

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="operation"  ,nullable=true , unique=false)
    private Integer operation;   //操作:1-增,2-删,3-改

    @Column(name="previous_id"  ,nullable=true , unique=false)
    private Integer previousId;   //修改前记录ID

    @Column(name="version_no"  ,nullable=true , unique=false)
    private Integer versionNo;   //版本号,每次修改+1

    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="meeting", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private MeetingInfo meetingInfo ;//


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Long getMeetingId() {
        return meetingId;
    }

    public void setMeetingId(Long meetingId) {
        this.meetingId = meetingId;
    }

    public Integer getUser() {
        return user;
    }

    public void setUser(Integer user) {
        this.user = user;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getPost() {
        return post;
    }

    public void setPost(Integer post) {
        this.post = post;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public Date getSpeakTime() {
        return speakTime;
    }

    public void setSpeakTime(Date speakTime) {
        this.speakTime = speakTime;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getOperation() {
        return operation;
    }

    public void setOperation(Integer operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public MeetingInfo getMeetingInfo() {
        return meetingInfo;
    }

    public void setMeetingInfo(MeetingInfo meetingInfo) {
        this.meetingInfo = meetingInfo;
    }
}
