<template>
  <div id="contentQuery" class="query">
    <TY_NavTop :title="title"></TY_NavTop>
    <div class="container">
      <div class="ui-message">
        <div class="sendContainer">
          <div class="tip" v-if="name == 'apply' || name == 'update'">请确定要查询内容的筛选条件</div>
          <div class="tip" v-if="name == 'approve' || name == 'approveLast'">您可按以下筛选条件，查询您经手审批的数据。</div>
          <el-form :model="queryForm" ref="queryForm" label-width="70px">
            <div v-if="name == 'approve' || name == 'approveLast'">
              <h4 class="item-header">申请人</h4>
              <div class="el-form-item">
                <span :class="queryForm.creator === null?'btn_all btn_active':'btn_all'" v-on:click="chooseApplier(0)">全部</span>
                <span class="chooseApplier" v-on:click="chooseApplier(1)">{{queryForm.applyName}} <i class="el-icon-arrow-right"></i></span>
              </div>
            </div>
            <h4 class="item-header">事件结果</h4>
            <div class="el-form-item" v-if="name == 'apply'">
              <el-radio-group v-model="queryForm.approveStatus">
                <el-radio :label="2">已发布的内容</el-radio>
                <el-radio :label="3">被驳回的内容</el-radio>
              </el-radio-group>
            </div>
            <div class="el-form-item" v-if="name == 'update'">
              <el-radio-group v-model="queryForm.approveStatus">
                <el-radio :label="2">已换版的内容</el-radio>
                <el-radio :label="3">被驳回的内容</el-radio>
              </el-radio-group>
            </div>
            <div class="el-form-item" v-if="name == 'approve'">
              <el-radio-group v-model="queryForm.approveStatus">
                <el-radio :label="2">已发布/换版的文件</el-radio>
                <el-radio :label="3">已驳回的文件</el-radio>
              </el-radio-group>
            </div>
            <div class="el-form-item"  v-if="name == 'approveLast'">
              <el-radio-group v-model="queryForm.approveStatus">
                <el-radio :label="2">已发布/换版的文件</el-radio>
                <el-radio :label="3">驳回的发布或者换版申请</el-radio>
              </el-radio-group>
            </div>
            <h4 class="item-header">发生时间</h4>
            <div class="el-form-item">
              <el-radio-group v-model="queryForm.type" size="small">
                <el-radio :label="1" border>近七日</el-radio>
                <el-radio :label="2" border>本月</el-radio>
                <el-radio :label="3" border>自定义</el-radio>
              </el-radio-group>
            </div>
            <div class="el-form-item" v-show="queryForm.type === 3">
              <el-date-picker type="date" placeholder="请选择" v-model="queryForm.timeBegin" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker> -
              <el-date-picker type="date" placeholder="请选择" v-model="queryForm.timeEnd" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker>
            </div>
          </el-form>
          <div class="handle_button">
            <input type="submit" class="fc-btn ui-btn_info" value="确定" @click="submitForm(1)" v-loading.fullscreen.lock="loading">
          </div>
        </div>
      </div>
    </div>
    <TY_AddressBook :rolelist="roleList" :title="选择申请人" v-bind:show.sync="bookVisible" @select_role="setRole"></TY_AddressBook>
  </div>
</template>

<script>
  export default {
    name: 'contentQuery',
    data () {
      return {
        name: '',
        title: '',
        queryForm: {
          eventType: 1,
          approveStatus: 2,
          type: 1,
          timeBegin: '',
          timeEnd: '',
          creator: null,
          applyName: '选择申请人'
        },
        loading: false,
        roleList: [],
        bookVisible: false
      }
    },
    created: function () {
      let that = this
      let name = this.$route.params.name
      console.log(name)
      let title = ''
      switch (name) {
        case 'apply':
          title = '内容发布申请'
          this.queryForm.eventType = 1
          break
        case 'update':
          title = '内容换版申请'
          this.queryForm.eventType = 2
          break
        case 'approve':
          title = '内容审批'
          this.queryForm.eventType = 3
          break
        case 'approveLast':
          title = '内容发布/换版审批'
          this.queryForm.eventType = 4
          break
      }
      this.title = title
      this.name = name
      if (this.queryForm.eventType === 3 || this.queryForm.eventType === 4) {
        this.$http.post('../../../forum/getAllParticipants.do', { userID: this.sphdSocket.user.userID, type: 5 }, {
          emulateJSON: true
        }).then((response) => {
          that.loading = false
          let data = response.body
          console.log(response.body)
          if (data) {
            that.roleList = data.listPostUser
            console.log(that.roleList)
          } else {
            console.log('加载失败！')
          }
        })
      }
    },
    methods: {
      submitForm: function () {
        let eventType = this.queryForm.eventType
        let type = this.queryForm.type
        let queryParam = {
          eventType: eventType,
          type: type,
          approveStatus: this.queryForm.approveStatus
        }
        if (type === 3) {
          queryParam.timeBegin = this.queryForm.timeBegin
          queryParam.timeEnd = this.queryForm.timeEnd
        }
        if (this.queryForm.creator !== null) {
          queryParam.fromUser = this.queryForm.creator
          queryParam.fromUserName = this.queryForm.applyName
        }
        localStorage.setItem('query', JSON.stringify(queryParam))
        this.$router.push({
          path: '/contentQueryPer',
          name: 'contentQueryPer',
          params: {
            data: queryParam
          }
        })
      },
      chooseApplier: function (type) {
        if (type === 0) {
          this.queryForm.creator = null
          this.queryForm.applyName = '请选择申请人'
        } else {
          this.bookVisible = true
        }
      },
      setRole: function (data) {
        this.queryForm.creator = data.userID
        this.queryForm.applyName = data.userName
      }
    }
  }
</script>
