<template>
  <div class="success">
    <h2><i class="el-icon-success"></i><span class="tip">录入成功！</span></h2>
    <el-button type="primary" @click="close()">关闭</el-button>
  </div>
</template>

<script>
export default {
  data () {
    return {
    }
  },
  methods: {
    close () {
      if (typeof (window.WeixinJSBridge) !== 'undefined') {
        window.WeixinJSBridge.call('closeWindow')
      } else if (navigator.userAgent.indexOf('MSIE') > 0) {
        if (navigator.userAgent.indexOf('MSIE 6.0') > 0) {
          window.opener = null
          window.close()
        } else {
          window.open('', '_top')
          window.top.close()
        }
      } else {
        try {
          this.focus()
          self.opener = null
          self.close()
        } catch (e) {
        }
        try {
          window.opener = null
          window.open('', '_self', '')
          window.close()
        } catch (e) {
        }
        try {
          window.open(window.location, '_self').close()
        } catch (e) {
        }
        window.location.replace('about:blank')
      }
    }
  }
}
</script>

<style scoped>
  h1, h2 {
    font-weight: normal;
  }
  .success{
    background-color: #f1f1f2;
    border-radius: 4px;
    text-align: center;
    padding: 20px;
  }
  .success i{
    color: #67C23A;
  }
  .tip{
    margin-left: 8px;
  }
</style>
