package cn.sphd.miners.modules.thirdPlatform.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2022-09-29 
 */

@Entity ( name ="TpTmpl" )
@Table ( name ="t_tp_tmpl" )
public class TpTmpl  implements Serializable {

	private static final long serialVersionUID =  3180669918468773457L;

	/**
	 * ID
	 */
	@Id
	@Column
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * 平台ID
	 */
   	@Column(name = "platform" )
	private Integer platform;

	/**
	 * 名称
	 */
   	@Column(name = "name" )
	private String name;

	/**
	 * 代码
	 */
   	@Column(name = "code" )
	private String code;

	/**
	 * 代码版本号
	 */
   	@Column(name = "version" )
	private String version;

	/**
	 * 描述
	 */
   	@Column(name = "basic_desc" )
	private String basicDesc;

	/**
	 * 类型:1-草稿,2-普通库,3-标准库
	 */
   	@Column(name = "type" )
	private Integer type;

	/**
	 * 简要说明
	 */
   	@Column(name = "brief_desc" )
	private String briefDesc;

	/**
	 * 功能说明
	 */
   	@Column(name = "function_desc" )
	private String functionDesc;

	/**
	 * 版本说明
	 */
   	@Column(name = "version_desc" )
	private String versionDesc;

	/**
	 * 来源
	 */
   	@Column(name = "source" )
	private String source;

	/**
	 * 开发者
	 */
   	@Column(name = "developer" )
	private String developer;

	/**
	 * 状态:0-草稿,1-提交申请,2-审批通过,3-审批否决
	 */
   	@Column(name = "approve_status" )
	private Integer approveStatus;

	/**
	 * 批准时间
	 */
   	@Column(name = "approve_time" )
	private Date approveTime;

	/**
	 * 是否停用
	 */
   	@Column(name = "is_trash" )
	private Integer isTrash;

	/**
	 * 停用时间
	 */
   	@Column(name = "trash_time" )
	private Date trashTime;

	/**
	 * 二维码路径
	 */
   	@Column(name = "QR_path" )
	private String qrPath;

	/**
	 * 入库时间
	 */
   	@Column(name = "library_time" )
	private Date libraryTime;

	/**
	 * 备注
	 */
   	@Column(name = "memo" )
	private String memo;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_time" )
	private Date createTime;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_time" )
	private Date updateTime;

	/**
	 * 操作:1-增,2-删,3-改,4-停用,5-启用,6-添加到普通库,7-添加到标准库,8-改图库
	 */
   	@Column(name = "operation" )
	private Integer operation;

	/**
	 * 修改前记录ID
	 */
   	@Column(name = "previous_id" )
	private Integer previousId;

	/**
	 * 版本号,每次修改+1
	 */
   	@Column(name = "version_no" )
	private Integer versionNo;

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getPlatform() {
		return this.platform;
	}

	public void setPlatform(Integer platform) {
		this.platform = platform;
	}

	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCode() {
		return this.code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getVersion() {
		return this.version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getBasicDesc() {
		return this.basicDesc;
	}

	public void setBasicDesc(String basicDesc) {
		this.basicDesc = basicDesc;
	}

	public Integer getType() {
		return this.type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getBriefDesc() {
		return this.briefDesc;
	}

	public void setBriefDesc(String briefDesc) {
		this.briefDesc = briefDesc;
	}

	public String getFunctionDesc() {
		return this.functionDesc;
	}

	public void setFunctionDesc(String functionDesc) {
		this.functionDesc = functionDesc;
	}

	public String getVersionDesc() {
		return this.versionDesc;
	}

	public void setVersionDesc(String versionDesc) {
		this.versionDesc = versionDesc;
	}

	public String getSource() {
		return this.source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getDeveloper() {
		return this.developer;
	}

	public void setDeveloper(String developer) {
		this.developer = developer;
	}

	public Integer getApproveStatus() {
		return this.approveStatus;
	}

	public void setApproveStatus(Integer approveStatus) {
		this.approveStatus = approveStatus;
	}

	public Date getApproveTime() {
		return this.approveTime;
	}

	public void setApproveTime(Date approveTime) {
		this.approveTime = approveTime;
	}

	public Integer getIsTrash() {
		return this.isTrash;
	}

	public void setIsTrash(Integer isTrash) {
		this.isTrash = isTrash;
	}

	public Date getTrashTime() {
		return this.trashTime;
	}

	public void setTrashTime(Date trashTime) {
		this.trashTime = trashTime;
	}

	public String getQrPath() {
		return this.qrPath;
	}

	public void setQrPath(String qrPath) {
		this.qrPath = qrPath;
	}

	public Date getLibraryTime() {
		return this.libraryTime;
	}

	public void setLibraryTime(Date libraryTime) {
		this.libraryTime = libraryTime;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getCreator() {
		return this.creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return this.createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Integer getUpdator() {
		return this.updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return this.updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateTime() {
		return this.updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Integer getOperation() {
		return this.operation;
	}

	public void setOperation(Integer operation) {
		this.operation = operation;
	}

	public Integer getPreviousId() {
		return this.previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return this.versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}

}
