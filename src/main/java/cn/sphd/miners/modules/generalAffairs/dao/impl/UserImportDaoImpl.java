package cn.sphd.miners.modules.generalAffairs.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.generalAffairs.dao.UserImportDao;
import cn.sphd.miners.modules.generalAffairs.entity.UserImport;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

/**
 * @ClassName UserImportLogDaoImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/12 14:18
 * @Version 1.0
 */
@Repository
public class UserImportDaoImpl extends BaseDao<UserImport,Serializable> implements UserImportDao {
}
