package cn.sphd.miners.modules.schedule.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.schedule.dao.*;
import cn.sphd.miners.modules.schedule.entity.*;
import cn.sphd.miners.modules.schedule.service.*;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.entity.Schedule;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.ScheduleService;
import cn.sphd.miners.modules.uploads.entity.UploadFile;
import cn.sphd.miners.modules.uploads.service.UploadService;
import com.alibaba.fastjson.JSON;
import io.netty.util.internal.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Created by Administrator on 2019/1/24.
 */
@Service("memoScheduleService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class MemoScheduleServiceImpl implements MemoScheduleService{
    @Autowired
    UserDao userDao;
    @Autowired
    MemoScheduleDao memoScheduleDao;
    @Autowired
    MemoJobDao memoJobDao;
    @Autowired
    MemoScheduleImageDao memoScheduleImageDao;
    @Autowired
    MemoScheduleHistoryDao memoScheduleHistoryDao;
    @Autowired
    MemoJobMsgDao memoJobMsgDao;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    MemoJobService memoJobService;
    @Autowired
    MemoScheduleHistoryService memoScheduleHistoryService;
    @Autowired
    MemoJobMsgService memoJobMsgService;
    @Autowired
    ScheduleService scheduleService;
    @Autowired
    UploadService uploadService;
    @Autowired
    MemoScheduleSupplementService memoScheduleSupplementService;
    @Autowired
    MemoScheduleShareService memoScheduleShareService;

    @Override
    public Integer saveMemoSchedule(MemoSchedule memoSchedule,User user,String source,String[] imagePaths) {
        memoSchedule.setCreateDate(new Date());
        memoSchedule.setUser(user.getUserID());
        memoSchedule.setCreateName(user.getUserName());
        memoSchedule.setCreator(user.getUserID());
        memoSchedule.setUpdator(user.getUserID());
        memoSchedule.setUpdateName(user.getUserName());

        memoSchedule.setSource(source);
        memoSchedule.setUpdateDate(new Date());

        if (memoSchedule.isNotify()) {//需要通知
            memoSchedule.setState(5);//状态:1-已完成、2-提醒中、3-不再提醒、4-提醒已过期、5-尚未开始提醒
        }
        memoSchedule.setNextRunTime(memoSchedule.getActiveStartDate());//下次提醒时间是日程开始提醒时间
        memoScheduleDao.save(memoSchedule);

        if (imagePaths!=null) {
            this.saveMemoImages(memoSchedule, imagePaths,user); //保存日程附件
        }

        if (memoSchedule.getActiveStartDate()!=null) {//首次提醒时间不等于null
            Date beginDate = memoSchedule.getActiveStartDate();
            if (memoSchedule.isNotify() &&
                    beginDate.getTime() > new Date().getTime() &&
                    NewDateUtils.today(beginDate).getTime() == NewDateUtils.today(new Date()).getTime()) {
                //需要提醒 且开始提醒时间为 当天，此刻以后的时间
                MemoJob memoJob = memoJobService.addMemoJobByUserSchedule(memoSchedule);
                //日程 到时间 提醒
//                if (1==memoJob.getEnabled()) {
                    MemoScheduleRemind memoScheduleRemind = new MemoScheduleRemind(memoJob.getId());
                    clusterMessageSendingOperations.delayCall(memoJob.getRunTime(),memoScheduleRemind);

//                    memoJob.setEnabled(0);//提醒已放到延时队列中 不需要再
//                }
            }
        }
        return memoSchedule.getId();

    }
    private void saveMemoImages(MemoSchedule memoSchedule, String[] imagePaths, User user){
        Integer orders=1;
        for (String imagePath:imagePaths){
            MemoScheduleImage memoScheduleImage=new MemoScheduleImage();
            memoScheduleImage.setCreateDate(memoSchedule.getCreateDate());
            memoScheduleImage.setCreator(memoSchedule.getCreator());
            memoScheduleImage.setCreateName(memoSchedule.getCreateName());
            memoScheduleImage.setNormal(imagePath);//正常图片路径
            memoScheduleImage.setOrders(orders);
            memoScheduleImage.setSchedule(memoSchedule);
            memoScheduleImageDao.save(memoScheduleImage);
            memoSchedule.getMemoScheduleImageHashSet().add(memoScheduleImage);
            MemoUsing callback = new MemoUsing(memoScheduleImage.getId(), memoScheduleImage.getClass());
            String name=memoSchedule.getTitle();
            if (imagePaths.length>1){ //多个文件
                name=name+orders;
            }
            uploadService.addFileUsing(callback, memoScheduleImage.getNormal(), name, user, "备忘与日程");
            orders++;
        }
    }

    @Override
    public void updateMemoSchedule(MemoSchedule memoSchedule) {
        memoSchedule.setUpdateDate(new Date());
        memoScheduleDao.update(memoSchedule);
    }

    @Override
    public MemoSchedule getMemoScheduleById(Integer id) {
        return memoScheduleDao.get(id);
    }

    @Override
    public List<MemoSchedule> getMemoScheduleListByUserId(Integer userId, PageInfo pageInfo,String keyWord) {
        String hql=" from MemoSchedule where isValid=true and notify=true  and (freqType>1 or (freqType=1 and activeStartDate>:lingChenDate)) and user=:userId";
        Map<String,Object> params=new HashMap<>();
        params.put("userId", userId);
//        params.put("dangQianDate",new Date());//当前时间  and activeStartDate<:dangQianDate)
        params.put("lingChenDate",NewDateUtils.today(new Date()));//本日开始时刻
        if (!StringUtil.isNullOrEmpty(keyWord)){
            params.put("keyword","%"+keyWord+"%");
            hql+=" and (title like:keyword or description like:keyword)";
        }

        List<MemoSchedule> memoScheduleList=memoScheduleDao.getListByHQLWithNamedParams(hql+" order by freqType asc,activeStartDate asc",params,pageInfo);
        return memoScheduleList;
    }

    @Override
    public void addMemoScheduleRemind(Integer id) {
        MemoJob memoJob=memoJobDao.get(id);
        memoJob.getSchedule().setModifiable(false);//设置成不可修改
        if (memoJob.isValid() && memoJob.getEnabled().equals(1)) {//要执行的提醒
            memoJob.setState(2);//状态:1-已完成、2-提醒中、3-不再提醒、4-提醒已过期、5-尚未开始提醒
//            memoJobDao.update(memoJob);
            memoJob.getSchedule().setNoticeNumber(memoJob.getSchedule().getNoticeNumber()+1);
            memoJob.setNoticeNumber(memoJob.getNoticeNumber()+1);//提醒次数加1
            memoJob.setEnabled(0);//本次提醒 过了 不需要再提醒
//            memoJob.setUpdateDate(new Date());
//            String hql=" update MemoJob set enabled=0 where id=:id";
//            HashMap<String,Object> params = new HashMap<>();
//            params.put("id",id);
//            memoJobDao.queryHQLWithNamedParams(hql,params);//为防止机群情况直接在数据库更新 状态

            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String runDate=sdf.format(memoJob.getRunTime());
            memoJob.setRunTimePath(memoJob.getRunTimePath()==null?runDate:memoJob.getRunTimePath()+","+runDate);//提醒时间路径

//            if (memoJob.getSchedule().getFreqType()==1){
                memoJob.getSchedule().setState(2); //日程最新状态改为提醒中
//            }
//            memoScheduleDao.update(memoJob.getSchedule());
            String cont = "您在" + new SimpleDateFormat("yyyy年MM月dd日 HH:mm").format(memoJob.getRunTime()) + "有“" + memoJob.getTitle() + "”的日程!";
            String memo = "提醒时间：" + new SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss").format(memoJob.getRunTime());
// 注释掉发送我的消息          userSuspendMsgService.saveUserSuspendMsg(1, cont,cont, memo, memoJob.getUser(),"scheduleDetail",memoJob.getScheduleId(),"memoScheduleService",memoJob.getId().toString());
            memoJobMsgService.saveMemoJobMsg(1,cont,cont,memo,memoJob.getUser(),memoJob.getId(),memoJob.getScheduleId());
            //日程 提醒 开始一个小时后自动过期
            MemoJobRemind memoJobRemind = new MemoJobRemind(memoJob.getId(),memoJob.getUpdateDate());
//            clusterMessageSendingOperations.delayCall(NewDateUtils.changeHour(memoJob.getRunTime(),1),memoJobRemind);
            clusterMessageSendingOperations.delayCall(new Date(memoJob.getRunTime().getTime() + TimeUnit.HOURS.toMillis(1)),memoJobRemind);

            User user=userDao.get(memoJob.getUser());
            //日程主页推送
            clusterMessageSendingOperations.convertAndSendToUser(memoJob.getUser().toString(),"/scheduleHomepage",null,null,user.getOid(),null,JSON.toJSONString(memoJob.getSchedule()));
            //日程提醒记录页推送
            clusterMessageSendingOperations.convertAndSendToUser(memoJob.getUser().toString(),"/memoJobPage",null,null,user.getOid(),null,JSON.toJSONString(memoJob));


        }
    }

    @Override
    public void overdueMemoJobSchedule(Integer id,Date updateDate) {
        MemoJob memoJob=memoJobDao.get(id);
        if (memoJob.getState()==2&&
                memoJob.getUpdateDate().getTime()<=updateDate.getTime()){

            memoJob.setState(4);//状态:1-已完成、2-提醒中、3-不再提醒、4-提醒已过期、5-尚未开始提醒
//            if (memoJob.getSchedule().getFreqType()==1){//一次性日程，日程也过期
                memoJob.getSchedule().setState(4);
//            }
        }
    }

    @Override
    public List<MemoSchedule> getMemoScheduleListByDate(Integer userId,Date begin, Date end) {
        String hql=" from MemoSchedule where user=:userId and ((freqType>1 and activeStartDate<:begin) or (freqType=1 and :begin<activeStartDate and activeStartDate<:end))";

//        (freqType>1 or (freqType=1 and activeStartDate>:lingChenDate))
        if (end.getTime()>new Date().getTime()){
            hql+=" and notify=true";
        }
        Map<String,Object> params=new HashMap<>();
        params.put("begin",begin);
        params.put("end",end);
        params.put("userId",userId);
        List<MemoSchedule> memoScheduleList=memoScheduleDao.getListByHQLWithNamedParams(hql,params);
        return memoScheduleList;
    }

    @Override
    public List<MemoSchedule> keywordQuerySchedule(Integer userId, String keyword, String description, String supplement, String place,PageInfo pageInfo) {
        Map<String,Object> params=new HashMap<>();
        params.put("userId",userId);
        params.put("keyword","%"+keyword+"%");
        String hql=" from MemoSchedule where isValid=true and user=:userId and (";
        if (!StringUtil.isNullOrEmpty(description)){
            hql+=" title like:keyword or";
        }
        if (!StringUtil.isNullOrEmpty(supplement)){
            hql+=" supplement like:keyword or";
        }
        if (!StringUtil.isNullOrEmpty(place)){
            hql+=" place like:keyword or";
        }
        hql= StringUtils.substringBeforeLast(hql,"or");
        hql+=")";
        List<MemoSchedule> memoScheduleList=memoScheduleDao.getListByHQLWithNamedParams(hql,params,pageInfo);
        return memoScheduleList;
    }

    @Override
    public void scheduleNoRemind(Integer id) {
//        List<String> memoJobList=new ArrayList<>();
        MemoSchedule m = memoScheduleDao.get(id);
        for (MemoJob mj : m.getMemoJobHashSet()) {
//            if (mj.getEnabled().equals(1)) {
            if(1!=mj.getState()&&4!=mj.getState()){
                mj.setEnabled(0);
                mj.setState(3);
                mj.setUpdateDate(new Date());
                memoJobDao.update(mj);
            }
//            memoJobList.add(mj.getId().toString());
        }
        m.setModifiable(false);//设置成不可修改
        m.setEnabled(0);
        m.setState(3);
        m.setUpdateDate(new Date());
        this.updateMemoSchedule(m);

        m.setMemoScheduleImageHashSet(null);
//        if (memoJobList.size()>0) {//删除消息记录
//            userSuspendMsgService.deleteUserSuspendMsg(userDao.get(m.getUser()), "memoScheduleService", memoJobList);
//        }
        List<MemoJobMsg> memoJobMsgList=memoJobMsgService.getMemoJobMsgByScheduleId(id,"1");
        for (MemoJobMsg memoJobMsg:memoJobMsgList){
            memoJobMsgService.updateMemoJobMsg(memoJobMsg,"2",memoJobMsg.getUserId());//变成已读
        }
        User user=userDao.get(m.getUser());
        //日程主页推送
        clusterMessageSendingOperations.convertAndSendToUser(m.getUser().toString(),"/scheduleHomepage",null,null,user.getOid(),null,JSON.toJSONString(m));
        //日程提醒记录页推送
        clusterMessageSendingOperations.convertAndSendToUser(m.getUser().toString(),"/memoJobPage",null,null,user.getOid(),null,JSON.toJSONString(m));

    }

    @Override
    public void handleSchedule(MemoSchedule memoSchedule, String type,Date remindDate) {

        String hql=" from MemoJob where scheduleId=:sId order by createDate desc";
        Map<String,Object> params=new HashMap<>();
        params.put("sId",memoSchedule.getId());
        MemoJob memoJob= (MemoJob) memoJobDao.getByHQLWithNamedParams(hql,params);//最新一次提醒记录
        memoJob.setUpdateDate(new Date());
        memoJob.getSchedule().setUpdateDate(new Date());
        switch (type){
            case "1":
                memoJob.setState(1);
                memoJob.setEnabledTime(new Date());
                if (memoSchedule.getFreqType()==1){
                    memoSchedule.setState(1);
                    memoSchedule.setActiveEndDate(new Date());
                }else {
                    memoSchedule.setState(4);//日程详情 状态要展示成下次提醒时间
                }
                break;
            case "2":
                memoJob.setState(3);
                if (memoSchedule.getFreqType()==1){
                    memoSchedule.setState(3);
                }else {
                    memoSchedule.setState(4);//日程详情 状态要展示成下次提醒时间
                }
                memoJob.getSchedule().setModifiable(false);//设置成不可修改

                break;
            case "3":
                memoJob.setType(2);//类型：1-约定提醒(按约定规则进行的),2-再次提醒(人工选择了再次提醒)
                memoJob.setRunTime(remindDate);//再次提醒时间
                memoJob.setEnabled(1);//提醒 过了 需要再次提醒
                memoJob.getSchedule().setLastRunTime(remindDate);//延时的再次提醒时间(纯为详情中展示用)
                //日程 到时间 提醒
                MemoScheduleRemind memoScheduleRemind = new MemoScheduleRemind(memoJob.getId());
                clusterMessageSendingOperations.delayCall(memoJob.getRunTime(),memoScheduleRemind);
                break;
        }
        User user=userDao.get(memoJob.getUser());
        //日程主页推送
        clusterMessageSendingOperations.convertAndSendToUser(memoJob.getUser().toString(),"/scheduleHomepage",null,null,user.getOid(),null,JSON.toJSONString(memoJob.getSchedule()));
        //日程提醒记录页推送
        clusterMessageSendingOperations.convertAndSendToUser(memoJob.getUser().toString(),"/memoJobPage",null,null,user.getOid(),null,JSON.toJSONString(memoJob));

    }

    @Override
    public void scheduleSettleDay(String code) {
        long now = System.currentTimeMillis();
//        Date dangQianDate=new Date();
        Schedule schedule=scheduleService.getScheduleByCode(code);//记录定时器 最后 执行时间
        Date begin=schedule.getRunTime();
        Date end = new Date(now+TimeUnit.DAYS.toMillis(1));
        List<MemoSchedule> memoScheduleList = this.getNextMemoScheduleList(begin,end);//下次提醒时间在  上次执行日程定时器时间 和 当前时间24小时后  之间
        System.out.println("符合条件的日程列表总数："+memoScheduleList.size());
        for (MemoSchedule memoSchedule : memoScheduleList) {
            Long runTime=memoSchedule.getNextRunTime().getTime();
            MemoJob memoJob = null;
            if(runTime<end.getTime()){
                System.out.println("生成日程"+memoSchedule.getId()+" 的单个提醒开始");
                while (runTime<end.getTime()) {
                    memoJob = new MemoJob();
                    runTime = memoJobService.addMemoJobByUserScheduleTask(memoSchedule, memoJob,runTime ,now);//生成此日程单个周期提醒记录
                }
                System.out.println("生成日程"+memoSchedule.getId()+" 的单个提醒结束");
            }
            //日程 到时间 提醒
            if (memoJob!=null&&memoJob.getRunTime().getTime()>now) {
                MemoScheduleRemind memoScheduleRemind = new MemoScheduleRemind(memoJob.getId());
                clusterMessageSendingOperations.delayCall(memoJob.getRunTime(),memoScheduleRemind);
            }
        }
        schedule.setRunTime(new Date(now+TimeUnit.DAYS.toMillis(1)));
    }


    public List<MemoSchedule> getNextMemoScheduleList(Date beginTime,Date nextDate) {

        String hql=" from MemoSchedule where state not in(3) and isValid=true and notify=true and :beginTime<nextRunTime and nextRunTime<=:nextDate";
        Map<String,Object> params=new HashMap<>();
//        params.put("userId",userId);
        params.put("beginTime",beginTime);//当前时间  and activeStartDate<:dangQianDate)
        params.put("nextDate",nextDate);//24小时之后

        List<MemoSchedule> memoScheduleList=memoScheduleDao.getListByHQLWithNamedParams(hql,params);
        return memoScheduleList;
    }

    @Override
    public Integer getMemoScheduleCounts(Integer userId) {
        String hql="select count(id) from MemoSchedule where user=:userId";
        Map<String,Object> params=new HashMap<>();
        params.put("userId",userId);
        Long number= (Long) memoScheduleDao.getByHQLWithNamedParams(hql,params);
        return number.intValue();
    }

    @Override
    public List<MemoSchedule> getMemoListByUserId(Integer userId, PageInfo pageInfo) {
        String hql=" from MemoSchedule where isValid=true and notify=false and id not in(select scheduleId from MemoFavoriteSchedule)  and user=:userId";
        Map<String,Object> params=new HashMap<>();
        params.put("userId", userId);
        List<MemoSchedule> memoScheduleList=memoScheduleDao.getListByHQLWithNamedParams(hql+" order by updateDate desc,id desc",params,pageInfo);
        return memoScheduleList;
    }

    @Override
    public void updateMemoSchedule(MemoSchedule memoSchedule, User user, String[] imagePaths) {
        memoSchedule.setUpdateDate(new Date());
        memoSchedule.setUser(user.getUserID());
        memoSchedule.setUpdator(user.getUserID());
        memoSchedule.setUpdateName(user.getUserName());
        memoSchedule.setSource("2"); //来源：1-新增,2-修改原任务而来,3-设置新的提醒时间复制而来
        if (memoSchedule.isNotify()) {//需要通知
            memoSchedule.setEnabled(1);
            memoSchedule.setState(5);//状态:1-已完成、2-提醒中、3-不再提醒、4-提醒已过期、5-尚未开始提醒
        }
        memoSchedule.setNextRunTime(memoSchedule.getActiveStartDate());//下次提醒时间是日程开始提醒时间
        memoScheduleDao.update(memoSchedule);

        this.saveMemoImages(memoSchedule,imagePaths,user); //保存日程附件

        if (memoSchedule.getNextRunTime()!=null) {//下次提醒时间不等于null
            MemoJob lastMemoJob=memoJobService.getLastMemoJobBySid(memoSchedule.getId());//
            if (lastMemoJob!=null
                    &&NewDateUtils.changeDay(lastMemoJob.getRunTime(),0).getTime()==NewDateUtils.today(new Date()).getTime()
                    &&lastMemoJob.getRunTime().getTime()>new Date().getTime()) {//
                //原日程 最新一次的提醒设定为当日 当前时间之后的时间
                lastMemoJob.setValid(false);// 设成删除 状态
            }
            Date beginDate = memoSchedule.getNextRunTime();
            if (memoSchedule.isNotify() &&
                    beginDate.getTime() > new Date().getTime() &&
                    NewDateUtils.today(beginDate).getTime() == NewDateUtils.today(new Date()).getTime()) {
                //需要提醒 且开始提醒时间为 当天，此刻以后的时间
                MemoJob memoJob = memoJobService.addMemoJobByUserSchedule(memoSchedule);
                //日程 到时间 提醒
                if (1 == memoJob.getEnabled()) {
                    MemoScheduleRemind memoScheduleRemind = new MemoScheduleRemind(memoJob.getId());
                    clusterMessageSendingOperations.delayCall(memoJob.getRunTime(), memoScheduleRemind);

//                    memoJob.setEnabled(0);//提醒已放到延时队列中 不需要再
                }
            }
        }
    }

    @Override
    public List<MemoSchedule> getMemoScheduleListByKeyword(Integer userId, String keyword,PageInfo pageInfo) {
        Map<String,Object> params=new HashMap<>();
        params.put("userId",userId);
        params.put("keyword","%"+keyword+"%");
        String hql=" from MemoSchedule where isValid=true and notify=false and user=:userId and (title like:keyword or description like:keyword)";
        List<MemoSchedule> memoScheduleList=memoScheduleDao.getListByHQLWithNamedParams(hql,params,pageInfo);
        return memoScheduleList;
    }

    @Override
    public void deleteMemoSchedule(MemoSchedule memoSchedule) {
        memoScheduleDao.delete(memoSchedule);
    }

    @Override
    public void deleteMemoSchedule(Integer id,User user) {
        MemoSchedule m = memoScheduleDao.get(id);
        if (m.getFreqType()!=null) { //有提醒的日程，逻辑删除  现改成物理删除 2021/3/9 lixu
//                for (MemoJob mj : m.getMemoJobHashSet()) {
//                    mj.setEnabled(0);
//                    mj.setValid(false);
////                memoJobList.add(mj.getId().toString());
//                    memoJobService.updateMemoJob(mj);
//                }
//            if (memoJobList.size()>0) {
//                userSuspendMsgService.deleteUserSuspendMsg(user, "memoScheduleService", memoJobList);
//            }
            List<MemoJobMsg> memoJobMsgList = memoJobMsgService.getMemoJobMsgByScheduleId(id, null);
            for (MemoJobMsg memoJobMsg : memoJobMsgList) {
                memoJobMsgService.updateMemoJobMsg(memoJobMsg, "4", user.getUserID());//目前未处理的 变成删除 并推送减去角标和数据
            }
            memoJobMsgService.deleteAllMemoJobMsg(memoJobMsgList);//删除所有该日程的具体提醒记录

            m.setValid(false);
//                memoScheduleService.updateMemoSchedule(m);
        }
//            else {
//                memoScheduleService.deleteMemoSchedule(m);// 不需要提醒 的日程 物理删除
//            }

        //删除日程的 文件引用  2021/3/9 lixu 1.52空间与流量
        for (MemoScheduleImage memoScheduleImage:m.getMemoScheduleImageHashSet()){
            UploadFile uploadFile=uploadService.getUploadFile(memoScheduleImage.getNormal());
            if (uploadFile!=null) {
                MemoUsing callback = new MemoUsing(memoScheduleImage.getId(), memoScheduleImage.getClass());
                try {
                    uploadService.delFileUsing(callback, memoScheduleImage.getNormal(), user);
                }catch (Exception e){

                }
            }
        }

        for (MemoScheduleHistory memoScheduleHistory:m.getMemoScheduleHistoryHashSet()){
            for (MemoScheduleImageHistory memoScheduleImageHistory:memoScheduleHistory.getMemoScheduleImageHistoryHashSet()){
                //删除日程的 文件引用  2021/3/9 lixu 1.52空间与流量
                UploadFile uploadFile=uploadService.getUploadFile(memoScheduleImageHistory.getNormal());
                if (uploadFile!=null) {
                    MemoUsing callback = new MemoUsing(memoScheduleImageHistory.getId(), memoScheduleImageHistory.getClass());
                    try {
                        uploadService.delFileUsing(callback, memoScheduleImageHistory.getNormal(), user);
                    }catch (Exception e){

                    }
                }
            }
        }

        List<MemoScheduleSupplement> memoScheduleSupplementList=memoScheduleSupplementService.getMemoScheduleSupplementListBySid(id);
        for (MemoScheduleSupplement scheduleSupplement:memoScheduleSupplementList){
            delMemoScheduleSupplementImagFileUsing(scheduleSupplement.getMemoScheduleSupplementImageHashSet(),user);  //删除日程 补充记录 的 文件引用  2021/3/20 lixu 1.52空间与流量
        }

        List<MemoScheduleShare> memoScheduleShareList= memoScheduleShareService.getMemoScheduleSharesByScheduleId(m.getId(),null);// 全部分享
        memoScheduleShareService.deleteAllMemoScheduleShare(memoScheduleShareList);//删除全部分享
        memoScheduleDao.delete(m);// 需不需要提醒的日程 都改成物理删除0
    }

    //删除补充记录的附件引用
    @Override
    public void delMemoScheduleSupplementImagFileUsing(Set<MemoScheduleSupplementImage> memoScheduleSupplementImageHashSet, User user) {
        for (MemoScheduleSupplementImage memoScheduleSupplementImage:memoScheduleSupplementImageHashSet){
            UploadFile uploadFile=uploadService.getUploadFile(memoScheduleSupplementImage.getNormal());
            if (uploadFile!=null) {
                try{
                    uploadService.delFileUsing(new MemoUsing(memoScheduleSupplementImage.getId(), memoScheduleSupplementImage.getClass()), memoScheduleSupplementImage.getNormal(), user); // 删除补充记录的附件 引用
                }catch (Exception e) {
                }
            }
        }
    }
}
