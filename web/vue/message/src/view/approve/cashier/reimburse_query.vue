<template>
  <div id="reimburseApproveQuery" class="reimburseQuery">
    <TY_NavTop :title="tittle" isButton="true" @toggleSubmit="submit"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div style="padding: 0 4px;">
        <div class="tip tip-success">{{ tittleTip}}</div>
      </div>
      <div class="ui-message">
        <div class="sendContainer">
          <el-form :model="queryForm" ref="queryForm" label-width="70px">
            <div>
              <h4 class="item-header">提交时间</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.type" size="small">
                  <el-radio label="1" border>近七日</el-radio>
                  <el-radio label="2" border>本月</el-radio>
                  <el-radio label="3" border>自定义</el-radio>
                </el-radio-group>
              </div>
              <div class="el-form-item" v-show="queryForm.type === '3'">
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.beginDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker> -
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.endDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker>
              </div>
            </div>
            <div>
              <h4 class="item-header">审批结果</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.approveStatus" size="small">
                  <el-radio label="2" border>通过</el-radio>
                  <el-radio v-if="titleType === 1" label="3" border>驳回</el-radio>
                </el-radio-group>
              </div>
            </div>
            <div>
              <h4 class="item-header">申请人</h4>
              <div class="el-form-item">
                <span :class="queryForm.applier === 0?'btn_all btn_active':'btn_all'" v-on:click="chooseApplier(0)">全部</span>
                <span class="chooseApplier" v-on:click="chooseApplier(1)">{{queryForm.applyName}} <i class="el-icon-arrow-right"></i></span>
              </div>
            </div>
            <div>
              <h4 class="item-header">一级费用类别</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.feeCatAll" size="small">
                  <el-radio label="0" border>全部</el-radio>
                  <el-radio label="1" style="margin-left: 70px">选择一级费用类别 <i class="el-icon-arrow-right"></i></el-radio>
                </el-radio-group>
              </div>
              <div class="el-form-item" v-show="queryForm.feeCatAll === '1'">
                <el-radio-group v-model="queryForm.feeCat" size="small">
                  <el-radio :label="item.id" border v-for="item in feeCat" v-bind:key="item.id" @change="setSecondFeeCat">{{item.name}}</el-radio>
                </el-radio-group>
              </div>
              <div v-show="secondFeeCatList.length > 0 && queryForm.feeCatAll === '1'">
                <div class="el-form-item">
                  <el-radio-group v-model="queryForm.secondFeeCatAll" size="small">
                    <el-radio label="0" border>全部</el-radio>
                    <el-radio label="1" style="margin-left: 70px">选择二级费用类别 <i class="el-icon-arrow-right"></i></el-radio>
                  </el-radio-group>
                </div>
                <div class="el-form-item" v-show="queryForm.secondFeeCatAll === '1'">
                  <el-radio-group v-model="queryForm.secondFeeCat" size="small">
                    <el-radio :label="item.id" border v-for="item in secondFeeCatList" v-bind:key="item.id">{{item.name}}</el-radio>
                  </el-radio-group>
                </div>
              </div>
            </div>
            <div>
              <h4 class="item-header">票据种类</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.billCatAll" size="small">
                  <el-radio label="0" border>全部</el-radio>
                  <el-radio label="1" style="margin-left: 100px">选择票据种类 <i class="el-icon-arrow-right"></i></el-radio>
                </el-radio-group>
              </div>
              <div class="el-form-item" v-show="queryForm.billCatAll === '1'">
                <el-radio-group v-model="queryForm.billCat" size="small">
                  <el-radio :label="item.id" border v-for="item in billCat" v-bind:key="item.id">{{item.name}}</el-radio>
                </el-radio-group>
              </div>
            </div>
          </el-form>
        </div>
      </div>
    </div>
    <TY_AddressBook :rolelist="roleList" :title="选择申请人" v-bind:show.sync="bookVisible" @select_role="setRole"></TY_AddressBook>
  </div>
</template>
<style>
  .btn_all{
    display: inline-block;
    padding: 5px 20px;
    border-radius: 3px;
    font-size: 12px;
    border: 1px solid #dcdfe6;
    margin-left: 10px;
  }
  .btn_active{
    border: 1px solid #409eff;
    color: #409eff;
  }
  #reimburseApproveQuery .el-radio__input, #reimburseApplyQuery .el-radio__input {
    display: none;
  }
  .chooseApplier{
    margin-left: 110px;
    cursor: pointer;
  }
</style>

<script>
export default {
  name: 'approveReimburseQuery',
  data () {
    return {
      loading: true,
      titleType: '1',
      tittle: '报销审批',
      tittleTip: '您可通过确定更具体的查询条件，得到想要的数据。',
      queryForm: {
        type: '1',
        beginDate: '',
        endDate: '',
        approveStatus: '2',
        feeCat: '',
        secondFeeCat: '',
        billCat: '',
        billCatAll: '0',
        feeCatAll: '0',
        secondFeeCatAll: '0',
        applier: 0,
        applyName: '选择申请人'
      },
      billCat: [],
      feeCat: [],
      secondFeeCatList: [],
      roleList: [],
      bookVisible: false
    }
  },
  created: function () {
    let that = this
    let type = this.$route.params.type
    this.titleType = Number(type) // 1-报销审批人 2-财务
    if (this.titleType === 2) {
      that.tittle = '个人报销的付款'
      that.tittleTip = '您既可按提交时间的范围查询，也可进行其他组合查询。'
    }
    this.axios.get('../../../reimburse/toCode.do')
      .then(function (response) {
        let feeCats = response.data.codeList
        console.log(response.data)
        that.feeCat = feeCats
      })
      .catch(function (error) {
        console.log(error)
      })
    this.axios.get('../../../expense/queryCodeCategory.do')
      .then(function (response) {
        let billCats = response.data.billCats
        console.log(response.data)
        that.billCat = billCats
      })
      .catch(function (error) {
        console.log(error)
      })
    let sendD = {
      userId: '',
      oid: this.sphdSocket.user.oid,
      userName: ''
    }
    if( this.titleType == 2){
      sendD.businessType = '17'
    }
    this.$http.post('../../../reimburseWindow/getReimburseApplyUserList.do',sendD, {
      emulateJSON: true
    }).then((response) => {
      that.loading = false
      let data = response.body
      if (data) {
        let data = response.data
        let list = data.data
        that.roleList = list
      } else {
        console.log('加载失败！')
      }
    })
  },
  methods: {
    submit: function () {
      // type 1-近七日，2-本月，3-自定义
      // 自定义时传 beginDate 开始时间，endDate 结束时间
      // approveStatus 2- 批准 3- 驳回
      // feeCat 费用类别id
      // feeCatName 费用类别名称
      // billCat 票据种类 id
      // billCatName 票据种类名称
      console.log(JSON.stringify(this.queryForm))
      let approveStatus = Number(this.queryForm.approveStatus)
      let queryParam = {
        titleType: this.titleType,
        approveStatus: approveStatus,
        beginDate: this.queryForm.beginDate,
        endDate: this.queryForm.endDate,
        type: this.queryForm.type,
        applyId: this.queryForm.applier,
        applyUserName: this.queryForm.applyName,
        userId: this.sphdSocket.user.userID
      }
      console.log(this.type + typeof (this.type))
      if (Number(this.type) === 2) {
        if (approveStatus === 2) {
          queryParam.approveStatus = 5
        } else if (approveStatus === 3) {
          queryParam.approveStatus = 8
        }
      }
      // 赋值票据类型id和name
      if (this.queryForm.billCatAll === '0') {
        queryParam.billCat = 0
      } else {
        let obj = this.billCat.find((item) => {
          return item.id === this.queryForm.billCat
        })
        queryParam.billCatName = obj.name
        queryParam.billCat = obj.id
      }
      // 赋值费用类别id和name（一级和二级）
      if (this.queryForm.feeCatAll === '0') {
        queryParam.feeCat = 0
      } else {
        let obj1 = this.feeCat.find((item) => {
          return item.id === this.queryForm.feeCat
        })
        if (this.queryForm.secondFeeCatAll === '0' || !this.queryForm.secondFeeCat) {
          queryParam.feeCat = this.queryForm.feeCat
          queryParam.feeCatName = obj1.name
        } else {
          queryParam.feeCat = this.queryForm.secondFeeCat
          let obj2 = this.secondFeeCatList.find((item) => {
            return item.id === this.queryForm.secondFeeCat
          })
          queryParam.feeCatName = obj1.name + '-' + obj2.name
        }
      }
      localStorage.setItem('query', JSON.stringify(queryParam))
      this.$router.push({
        path: '/approveReimburseQueryPer',
        name: 'approveReimburseQueryPer',
        params: {
          data: queryParam
        }
      })
    },
    chooseApplier: function (type) {
      if (type === 0) {
        this.queryForm.applier = 0
        this.queryForm.applyName = '请选择申请人'
      } else {
        this.bookVisible = true
      }
    },
    setRole: function (data) {
      this.queryForm.applier = data.userID
      this.queryForm.applyName = data.userName
    },
    setSecondFeeCat: function (val) {
      this.$http.post('../../../reimburseWindow/getSecondCodeList.do', { id: val }, {
        emulateJSON: true
      }).then((response) => {
        this.loading = false
        let data = response.body
        if (data) {
          let data = response.data
          console.log(data)
          this.secondFeeCatList = data
        } else {
          console.log('加载失败！')
        }
      })
    }
  }
}
</script>
