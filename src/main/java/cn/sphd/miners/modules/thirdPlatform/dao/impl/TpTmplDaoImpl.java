package cn.sphd.miners.modules.thirdPlatform.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.thirdPlatform.dao.TpTmplDao;
import cn.sphd.miners.modules.thirdPlatform.entity.TpTmpl;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class TpTmplDaoImpl extends BaseDao<TpTmpl, Serializable> implements TpTmplDao {
}
