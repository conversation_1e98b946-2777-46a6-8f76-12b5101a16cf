
package cn.sphd.miners.modules.material.entity;


import java.sql.*;
import java.util.Date;

import java.io.Serializable;
import javax.persistence.*;


@Entity (name="MtPurchaseApply")
@Table (name="t_mt_purchase_apply")
//物料_采购申请单(预留)
public class MtPurchaseApply implements Serializable {
	
    @Id @Column(name="id" ) 
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

//MP-MANAGED-ADDED-AREA-BEGINNING @applicant-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @applicant-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-applicant@
    @Column(name="applicant"   , nullable=true , unique=false)
    private Integer applicant; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @applicant_name-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @applicant_name-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-applicant_name@
    @Column(name="applicant_name"  , length=255 , nullable=true , unique=false)
    private String applicantName; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @apply_date-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @apply_date-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-apply_date@
    @Column(name="apply_date"   , nullable=true , unique=false)
    private Date applyDate; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @apply_type-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @apply_type-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-apply_type@
    @Column(name="apply_type"  , length=1 , nullable=true , unique=false)
    private String applyType; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @state-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @state-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-state@
    @Column(name="state"  , length=1 , nullable=true , unique=false)
    private String state; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @memo-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @memo-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-memo@
    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @description-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @description-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-description@
    @Column(name="description"  , length=255 , nullable=true , unique=false)
    private String description; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @creator-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @creator-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-creator@
    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @create_name-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @create_name-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-create_name@
    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @create_date-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @create_date-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-create_date@
    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @updator-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @updator-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-updator@
    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @update_name-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @update_name-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-update_name@
    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName; 
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-ADDED-AREA-BEGINNING @update_date-field-annotation@
//MP-MANAGED-ADDED-AREA-ENDING @update_date-field-annotation@
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @ATTRIBUTE-update_date@
    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;
//MP-MANAGED-UPDATABLE-ENDING



    public Integer getId() {
        return id;
    }
	
    public void setId (Integer id) {
        this.id =  id;
    }
    
//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-applicant@
    public Integer getApplicant() {
        return applicant;
    }
	
    public void setApplicant (Integer applicant) {
        this.applicant =  applicant;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-applicant_name@
    public String getApplicantName() {
        return applicantName;
    }
	
    public void setApplicantName (String applicantName) {
        this.applicantName =  applicantName;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-apply_date@
    public Date getApplyDate() {
        return applyDate;
    }
	
    public void setApplyDate (Date applyDate) {
        this.applyDate =  applyDate;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-apply_type@
    public String getApplyType() {
        return applyType;
    }
	
    public void setApplyType (String applyType) {
        this.applyType =  applyType;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-state@
    public String getState() {
        return state;
    }
	
    public void setState (String state) {
        this.state =  state;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-memo@
    public String getMemo() {
        return memo;
    }
	
    public void setMemo (String memo) {
        this.memo =  memo;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-description@
    public String getDescription() {
        return description;
    }
	
    public void setDescription (String description) {
        this.description =  description;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-creator@
    public Integer getCreator() {
        return creator;
    }
	
    public void setCreator (Integer creator) {
        this.creator =  creator;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-create_name@
    public String getCreateName() {
        return createName;
    }
	
    public void setCreateName (String createName) {
        this.createName =  createName;
    }
	

	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-updator@
    public Integer getUpdator() {
        return updator;
    }
	
    public void setUpdator (Integer updator) {
        this.updator =  updator;
    }
	
//MP-MANAGED-UPDATABLE-ENDING

//MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-update_name@
    public String getUpdateName() {
        return updateName;
    }
	
    public void setUpdateName (String updateName) {
        this.updateName =  updateName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
}
