<template>
  <div class="audioCon">
    <div class='daudio'>
      <el-icon class="closeLyc" @click="closeLyc">
        <close-bold />
      </el-icon>
      <div class="AudioCon">
        <AudioC :fileUrl="fileUrl" :playName="playName"></AudioC>
      </div>
    </div>
  </div>
</template>
<script>
import AudioC from '@/components/AudioC.vue'
export default {
  name: "AudioPlayer",
  data() {
    return {
    };
  },
  components: {
    AudioC
  },
  props: {
    fileUrl: {
      type: String,
      default: ''
    },
    playName: {
      type: String,
      default: ''
    },
  },
  created() {},
  mounted() {},
  methods: {
    closeLyc(){
      console.log('去调用父级 closePlayLyc ')
      this.$emit("closePlayLyc", false)
    },

  }
};
</script>
<style scoped lang="scss">
.audioCon{
  position: fixed;
  width: 100%;
  height: 100%;
  background: rgba(100,100,100,0.5);
  top:0;
  left: 0;
  .closeLyc{
    position: absolute;
    top: -65px;
    color: #fff;
    right: 40px;
    font-size: 24px;
  }
  .daudio{
    position: relative;
  }
  .AudioCon{
    width: 311px;
    margin:200px auto;
  }


}


</style>

