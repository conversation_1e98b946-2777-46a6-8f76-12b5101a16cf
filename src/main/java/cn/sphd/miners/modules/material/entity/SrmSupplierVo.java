package cn.sphd.miners.modules.material.entity;

import java.math.BigDecimal;
import java.util.Date;

public class SrmSupplierVo {

    private Integer id;
    private Integer org;
    private String name;
    private String fullName;
    private String codeName;
    private Integer contactCount;
    private String contact;
    private String mobile;
    private String telephone;
    private String fax;
    private String email;
    private String address;
    private String paymentType;
    private String paymentMethod;
    private String bankCode;
    private String bankName;
    private String bankNo;
    private Integer principal;
    private String principalName;
    private String principalMobile;
    private String keywords;
    private Integer supplyCount;
    private Integer exclusiveCount;
    private Integer cutCount;
    private Integer chargeAcceptable;
    private String chargeBegin;
    private Integer chargePeriod;
    private String isImprest;
    private String invoicable;
    private String vatsPayable;
    private Integer draftAcceptable;
    private Integer vatsCount;
    private Integer otherInvoiceCount;
    private Integer notInvoiceCount;
    private Integer enabled;
    private Date enabledTime;
    private Integer creator;
    private String createName;
    private Date createDate;
    private Integer updator;
    private String updateName;
    private Date updateDate;
    private String operation;
    private Integer previousId;
    private Integer versionNo;
    private BigDecimal taxRate;
    private BigDecimal imprestProportion;

    private Integer type;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getCodeName() {
        return codeName;
    }

    public void setCodeName(String codeName) {
        this.codeName = codeName;
    }

    public Integer getContactCount() {
        return contactCount;
    }

    public void setContactCount(Integer contactCount) {
        this.contactCount = contactCount;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankNo() {
        return bankNo;
    }

    public void setBankNo(String bankNo) {
        this.bankNo = bankNo;
    }

    public Integer getPrincipal() {
        return principal;
    }

    public void setPrincipal(Integer principal) {
        this.principal = principal;
    }

    public String getPrincipalName() {
        return principalName;
    }

    public void setPrincipalName(String principalName) {
        this.principalName = principalName;
    }

    public String getPrincipalMobile() {
        return principalMobile;
    }

    public void setPrincipalMobile(String principalMobile) {
        this.principalMobile = principalMobile;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public Integer getSupplyCount() {
        return supplyCount;
    }

    public void setSupplyCount(Integer supplyCount) {
        this.supplyCount = supplyCount;
    }

    public Integer getExclusiveCount() {
        return exclusiveCount;
    }

    public void setExclusiveCount(Integer exclusiveCount) {
        this.exclusiveCount = exclusiveCount;
    }

    public Integer getCutCount() {
        return cutCount;
    }

    public void setCutCount(Integer cutCount) {
        this.cutCount = cutCount;
    }

    public Integer getChargeAcceptable() {
        return chargeAcceptable;
    }

    public void setChargeAcceptable(Integer chargeAcceptable) {
        this.chargeAcceptable = chargeAcceptable;
    }

    public String getChargeBegin() {
        return chargeBegin;
    }

    public void setChargeBegin(String chargeBegin) {
        this.chargeBegin = chargeBegin;
    }

    public Integer getChargePeriod() {
        return chargePeriod;
    }

    public void setChargePeriod(Integer chargePeriod) {
        this.chargePeriod = chargePeriod;
    }

    public String getIsImprest() {
        return isImprest;
    }

    public void setIsImprest(String isImprest) {
        this.isImprest = isImprest;
    }

    public String getInvoicable() {
        return invoicable;
    }

    public void setInvoicable(String invoicable) {
        this.invoicable = invoicable;
    }

    public String getVatsPayable() {
        return vatsPayable;
    }

    public void setVatsPayable(String vatsPayable) {
        this.vatsPayable = vatsPayable;
    }

    public Integer getDraftAcceptable() {
        return draftAcceptable;
    }

    public void setDraftAcceptable(Integer draftAcceptable) {
        this.draftAcceptable = draftAcceptable;
    }

    public Integer getVatsCount() {
        return vatsCount;
    }

    public void setVatsCount(Integer vatsCount) {
        this.vatsCount = vatsCount;
    }

    public Integer getOtherInvoiceCount() {
        return otherInvoiceCount;
    }

    public void setOtherInvoiceCount(Integer otherInvoiceCount) {
        this.otherInvoiceCount = otherInvoiceCount;
    }

    public Integer getNotInvoiceCount() {
        return notInvoiceCount;
    }

    public void setNotInvoiceCount(Integer notInvoiceCount) {
        this.notInvoiceCount = notInvoiceCount;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public BigDecimal getImprestProportion() {
        return imprestProportion;
    }

    public void setImprestProportion(BigDecimal imprestProportion) {
        this.imprestProportion = imprestProportion;
    }
}
