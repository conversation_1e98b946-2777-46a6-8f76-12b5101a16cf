.loginRecordContainer {
  padding: 0 10px;
}
.ty-container {
  padding: 20px 0;
}
.gapLt {
  margin-left: 10px;
}
.btnGroup{ display:inline-block; border:1px solid #ccc; border-radius:5px; overflow:hidden;      }
.btnGroup .btnn:first-child{ border-left:none;  }
.btnGroup .btnn{ border-right:none; border-top:none; border-bottom:none; display:inline-block; margin-left:-5px; border-radius:0px;      }

.dataNav table td.textLeft, .dataList table td.textLeft {  text-align:left;       }
.dataNav table td.ind2, .dataList table td.ind2 {  text-align:left; text-indent:2em;       }
/*这是很重要的东西*/
#betweenDate{   font-size:16px;     }


ul.searchCon:before{  border-bottom: 7px solid rgba(0,0,0,0.2);  border-left: 7px solid transparent;  border-right: 7px solid transparent; content: "";
  display: inline-block !important; position: absolute;  left: 58px;  top: -7px;      }
ul .trigle{  border-bottom: 5px solid #fff;  border-left: 5px solid transparent;  border-right: 5px solid transparent;  content: "";
  display: inline-block !important; position: absolute;  right: 60px; top: -5px;       }
.searchCon{ left: 0; width:350px; border:1px solid #ccc; box-shadow:3px 3px 5px #666;      }
.searchCon .ttl{ display:inline-block; width:100px; text-align:right;    }
.searchCon input{ display:inline-block; width:215px; height:35px; line-height:35px;padding:0 5px;      }
.searchCon li{margin-top:10px; }
.searchCon .ctl{ text-align:right; padding-right:30px; margin :30px 0 15px 0 ;     }

.pal{ color:#fff; text-align:right; height:100px; position:relative; overflow:hidden ;     }
.head-pall{float:none;padding: 2px 0;margin:auto;color:#fff;text-align: center;}
.record-total{font-size:28px;line-height:52px;height:52px;}
.pcdata{margin-right:20px;}
.subdetail{margin-top:8px;margin-bottom: 8px;font-size:18px;  }
.subdetail p{margin-bottom:4px;}
.pallCon{color:#fff;text-align:center;padding:4px 0;  }
.pallCon span{padding:0 10px;}
.loginQuery .searchCon{
  width: 350px;
  padding: 20px 0;
}
.loginQuery table td{
  border: none;
}
.loginQuery input{
  height:30px
}
.ty-btn-group > .ty-btn {
  line-height: 32px;
}
.ty-btn.loginQuery{
  background-color: #f2f2f2;
  color: #aaa;
  position: relative;
  top: 2px;
  font-size: 14px;
}
.ty-btn-blue.loginQuery{
  background-color: #5d9cec;
  color: #fff;
}
.firstWrapper{
  min-width:800px;
  height:90px;
  margin: 0 auto;
  padding:20px 0;
  filter:alpha(opacity=100, finishopacity=50, style=1, startx=0,starty=0,finishx=0,finishy=150) progid:DXImageTransform.Microsoft.gradient(startcolorstr=#5d9cec,endcolorstr=#668ceb,gradientType=0);
  -ms-filter:alpha(opacity=100, finishopacity=50, style=1, startx=0,starty=0,finishx=0,finishy=150) progid:DXImageTransform.Microsoft.gradient(startcolorstr=#5d9cec,endcolorstr=#668ceb,gradientType=0);/*IE8*/
  background:#5d9cec; /* 一些不支持背景渐变的浏览器 */
  /*background:-moz-linear-gradient(top, #5d9cec, #668ceb);
  background:-webkit-gradient(linear, 0 0, 0 bottom, from(#5d9cec), to(#668ceb));
  background:-o-linear-gradient(top, #5d9cec, #668ceb);*/
}
.loginPersonInfo{
  width: 28%;
  text-align: center;
}
.totalLogin,
.mobileLogin,
.pcLogin{
  width: 24%;
  text-align: center;
}
.loginPersonInfo,
.totalLogin
{
  float: left;
  color: #fff;
}
.pcLogin,
.mobileLogin
{
  float: right;
}
.loginPersonInfo .LPI_time{
  font-size:22px;
  text-align: center;
  margin-right: 16px;
}
.loginPersonInfo .LPI_time_b{
  font-size:28px;
  line-height: 80px;
}
.loginPersonInfo .LPL_person{
  text-align: center;
  /*  height: 62px;*/
}
.loginPersonInfo .LPI_name{
  font-size:24px;
}
.loginPersonInfo .LPI_post{
  opacity: 1;
  padding: 0 5px;
  border-radius: 2px;
  color: #d7e1ef;
  margin-top: 5px;
  font-size:14px;
}
.loginPersonInfo .LPI_org{
  font-size: 14px;
  color: #d3e6fd;
  padding: 0 5px;
}
.totalLogin .loginSum{
  font-family: Arial;
  color: #fff;
  font-size: 60px;
  line-height:60px;
}
.totalLogin .loginSumDesc{
  color: #fff;
  font-size: 14px;
  color: #d3e6fd;
}
.pcNumDesc, .mobileNumDesc{
  font-size: 14px;
}
.pcLogin,.mobileLogin{
  color: #d3e6fd;
}
.pcLogin i{
  font-size: 66px;
  color: #80b5f9;
  vetical-align:middle;
  margin-right: 20px;
  line-height:88px;
}
.mobileLogin i{
  font-size: 80px;
  color: #80b5f9;
  margin-right: 20px;
}

.pcLogin .pcNum,.mobileLogin .mobileNum{
  font-family: Arial;
  font-size: 40px;
  line-height: 40px;
  color: #fff;
}
.ty-inline-block{
  display: inline-block;
  vetical-align:middle;
}
.ttl_sign{margin-top: 6px;text-align: center;color:#d3e6fd;font-size: 14px;}
.total_log{font-size:28px;margin-top: 6px;}
.gapTop{
  margin-top: 10px;
}
.gapRt{margin-right: 10px;}
.ty-btn-group :deep(.el-button) {  position: relative; padding: 0px 24px;  background-color: #f2f2f2;  color: #aaa; font-size: 14px }
.ty-btn-group :deep(.el-button--primary) { background-color:#5d9cec; color:#fff; }