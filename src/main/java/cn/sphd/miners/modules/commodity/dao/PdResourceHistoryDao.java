package cn.sphd.miners.modules.commodity.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.commodity.entity.PdResource;
import cn.sphd.miners.modules.commodity.entity.PdResourceHistory;

import java.io.Serializable;

public interface PdResourceHistoryDao  extends IBaseDao<PdResourceHistory, Serializable> {
    //添加历史记录
    PdResourceHistory insert(PdResource pdResource);
}
