<template>
  <div id="outMaterialTest">
    <TY_NavTop title="外购物料检验" isSearch="true" @toggleList="search" :isBackHome=false></TY_NavTop>
    <div class="container">
      <div class="ui-cells">
        <a class="ui-cell" v-on:click="jump(item)" v-for="(item, index) in applyHandle" v-bind:key="index">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <div>{{item.name}}</div>
              <div>采购人 {{item.create_name}} {{item.create_date | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            </div>
          </div>
          <div class="ui-cell__ft"></div>
        </a>
      </div>
    </div>
  </div>
</template>

<style>
  #outMaterialTest .ui-cell{
    font-size: 12px;
  }
</style>

<script>
export default {
  name: 'outMaterialTest',
  data () {
    return {
      pid: 0,
      isBackHome: false,
      listenersUid: [],
      applyHandle: []
    }
  },
  created: function () {
    this.tabValue = this.$route.params.tabValue || '1'
    // 从路由获取父级菜单id
    this.pid = this.$route.params.pid
    let that = this
    this.axios.post('../../../inStock/list', {
      oid: that.sphdSocket.user.oid
    }).then(function (response) {
      let list = response.data.data
      that.applyHandle = list
    })
  },
  methods: {
    jump: function (item) {
      this.$router.push({
        path: `/outMaterialTestList/${item.id}`
      })
      localStorage.setItem('approveItem', JSON.stringify(item))
    },
    search: function () {
      this.$router.push({
        path: `/purchaseQuery/2`
      })
    }
  }
}
</script>
