package cn.sphd.miners.modules.accountant.mapper;


import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectCash;
import org.springframework.stereotype.Component;

@Component
public interface TAccountantSubjectCashMapper extends BaseMapper<TAccountantSubjectCash> {

    TAccountantSubjectCash getSingleByDetailID(TAccountantSubjectCash cashParam);

    void deleteByDetailID(TAccountantSubjectCash cashDetail);

    void deleteOldCashDetail(TAccountantSubjectCash cashParam);
}