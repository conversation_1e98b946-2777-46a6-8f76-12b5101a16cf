package cn.sphd.miners.modules.equipment.service.impl;

import java.util.Date;
import java.util.List;

import cn.sphd.miners.modules.equipment.entity.TEquControlOption;
import cn.sphd.miners.modules.equipment.entity.TEquControlPoint;
import cn.sphd.miners.modules.equipment.mapper.TEquControlOptionMapper;
import cn.sphd.miners.modules.equipment.service.ITEquControlOptionService;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 装备管理_控制选项
 * 20240407 1.292机器设备之初始设置 新增Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-13
 */
@Service
@Transactional
public class TEquControlOptionServiceImpl implements ITEquControlOptionService {
    @Autowired
    private TEquControlOptionMapper tEquControlOptionMapper;

    /**
     * 查询装备管理_控制选项
     * 20240407 1.292机器设备之初始设置 新增
     *
     * @param id 装备管理_控制选项
     *           20240407 1.292机器设备之初始设置 新增主键
     * @return 装备管理_控制选项
     * 20240407 1.292机器设备之初始设置 新增
     */
    @Override
    public TEquControlOption selectTEquControlOptionById(Long id) {
        return tEquControlOptionMapper.selectTEquControlOptionById(id);
    }

    /**
     * 查询装备管理_控制选项
     * 20240407 1.292机器设备之初始设置 新增列表
     *
     * @param tEquControlOption 装备管理_控制选项
     *                          20240407 1.292机器设备之初始设置 新增
     * @return 装备管理_控制选项
     * 20240407 1.292机器设备之初始设置 新增
     */
    @Override
    public List<TEquControlOption> selectTEquControlOptionList(TEquControlOption tEquControlOption) {
        return tEquControlOptionMapper.selectTEquControlOptionList(tEquControlOption);
    }

    /**
     * 新增装备管理_控制选项
     * 20240407 1.292机器设备之初始设置 新增
     *
     * @param tEquControlOption 装备管理_控制选项
     *                          20240407 1.292机器设备之初始设置 新增
     * @return 结果
     */
    @Override
    public int insertTEquControlOption(TEquControlOption tEquControlOption) {
        return tEquControlOptionMapper.insertTEquControlOption(tEquControlOption);
    }

    /**
     * 修改装备管理_控制选项
     * 20240407 1.292机器设备之初始设置 新增
     *
     * @param tEquControlOption 装备管理_控制选项
     *                          20240407 1.292机器设备之初始设置 新增
     * @return 结果
     */
    @Override
    public int updateTEquControlOption(TEquControlOption tEquControlOption) {
        return tEquControlOptionMapper.updateTEquControlOption(tEquControlOption);
    }

    /**
     * 批量删除装备管理_控制选项
     * 20240407 1.292机器设备之初始设置 新增
     *
     * @param ids 需要删除的装备管理_控制选项
     *            20240407 1.292机器设备之初始设置 新增主键
     * @return 结果
     */
    @Override
    public int deleteTEquControlOptionByIds(String ids) {
        return tEquControlOptionMapper.deleteTEquControlOptionByIds(ids.split(","));
    }

    /**
     * 删除装备管理_控制选项
     * 20240407 1.292机器设备之初始设置 新增信息
     *
     * @param id 装备管理_控制选项
     *           20240407 1.292机器设备之初始设置 新增主键
     * @return 结果
     */
    @Override
    public int deleteTEquControlOptionById(Long id) {
        return tEquControlOptionMapper.deleteTEquControlOptionById(id);
    }

    @Override
    public void init(User user) {


        for(String s :"温度1、温度2、温度3、压力1、压力2、压力3、速度1、速度2、速度3、时间1、时间2、时间3、湿度1、湿度2".split("、")){
            TEquControlOption p = new TEquControlOption();
            p.setCreateDate(new Date());
            p.setCreateName("系统自带");
            p.setOrg(Long.valueOf(user.getOid()));
            p.setIsSystem(1);
            p.setName(s);
            if(s.equals("温度1")||s.equals("温度2")||s.equals("压力1")){
                p.setEnabled(1L);
            }else {
                p.setEnabled(0L);
            }

            tEquControlOptionMapper.insertTEquControlOption(p);
        }


    }

    @Override
    public TEquControlOption selectTEquControlOptionByNameAndOrg(TEquControlOption tEquControlOption) {
        return tEquControlOptionMapper.selectTEquControlOptionByNameAndOrg(tEquControlOption);
    }
}
