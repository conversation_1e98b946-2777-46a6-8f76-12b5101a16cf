<template>
  <div class="reg2">
    <Pageheader title="注册主播助手"></Pageheader>
    <div class="live-container">
      <div class="centerTxt">
        <div>您需要注册一个“字幕精灵”账号！</div>
      </div>

      <form class="li-form" ref="userForm" >
        <div class="li-formItem">
          <span class="li-ttl">手机号</span>
          <input type="text" v-model="userForm.phone" placeholder="请输入中国大陆地区的手机号"/>
        </div>
        <div class="li-formItem">
          <span class="li-ttl">验证码</span>
          <span class="li-codeBtn" @click="getCode">获取验证码</span>
          <input type="text" v-model="userForm.code" class="litCode" placeholder="请输入验证码" />
        </div>
        <span @click="nextStep" class="primary live-button">填写完毕，注册</span>

        <div class="li-formItem">
          <el-checkbox color="#0ca751" v-model="check1"  label="同意《用户协议》和《隐私政策》"></el-checkbox>
        </div>
        <div class="centerD">
          <div class="bluetip">系统默认团队内人员看不到您的手机号，您
            可勾选“让团队内人员看到我的手机号”</div>
          <div class="li-formItem">
            <el-checkbox color="#0ca751" v-model="check2" label="让团队内人员看到我的手机号"></el-checkbox>
          </div>

        </div>
      </form>
    </div>

  </div>
</template>

<script>
import { sendMobileCode , checkCode, joinTeam , registerVerificationCode, checkRegisterVerificationCode } from '@/api/api.js'
import { ElMessage } from 'element-plus'
import { useRouter } from "vue-router";
import JSONBig from 'json-bigint'

const router = useRouter();

export default {
  data() {
    return {
      userForm:{
        phone:'',
        code:''
      },
      sendcodeMobile:'',
      shareMesage: {
        type:1,
        shareName:'账簿你',
        shareTime:'2022-12-12 12：12：12',

      },
      check1:false,
      check2:false,
      phoneSate:0
    }
  },
  methods:{
    goPage(url) {
      console.log('$router.push goPage=', url)
      this.$router.push({name: url});
    },
    getCode(){
      if(this.userForm.phone.length !== 11){
        ElMessage.error('请输入正确的手机号');
        return false
      }
      sendMobileCode({ phone: this.userForm.phone }).then(res => {
        this.sendcodeMobile = this.userForm.phone
        let tip = '验证码已发送，注意查收！'
        let status = res.data
        if(status == 0 ){
          tip = '验证码发送失败'
        }else if(status == 3){
          tip = '手机号格式不对';
        }else if(status == 5){
          tip = '验证码已经发过还未过时';
        }else if(status == 4){
          this.phoneSate = 4
        }
        ElMessage.error(tip);

      }).catch( err => {
        ElMessage.error('ElMessage.error(验证码发送失败，请重新发送);');

      })
    },
    joinTeamFun(){
      let that = this
      console.log('joinTeamFun 接口调用')
      const jsonData = JSONBig.parse(localStorage.getItem('jsonData'))
      const shareMesage = jsonData.share
      if(!this.check1){
        ElMessage.success('请先勾选同意《用户协议》和《隐私政策');
        return false
      }
      let submitState = this.check2 ? 1 : 0
      joinTeam({ 'submitState': submitState, 'oid': shareMesage.oid }).then(res => {
        console.log('joinTeam = ', res)
        let dd = res
        if(dd){
          ElMessage.success('注册完毕，正在进入系统!');
          that.goPage('officeList')
        }else{
          ElMessage.success('操作失败，请重试！');
        }

      }).catch(err => {
        console.log('joinTeam err = ', err)

      })
    },
    nextStep(){
      if(this.phoneSate == 4){
        ElMessage.error('这个号不能再注册字幕精灵了，请换一个！');
      }else if(this.userForm.phone.length !== 11){
        ElMessage.error('请输入正确的手机号');
      }else if(this.userForm.code.length == 0){
        ElMessage.error('请输入正确的验证码');
      }else if(!this.check1){
        ElMessage.error('请勾选同意《用户协议》和《隐私政策》!');
      }else{
        checkCode({ phone: this.userForm.phone, code:this.userForm.code })
            .then((res)=>{
              let data = res.data
              if(data == "绑定成功"){
                console.log('data=', data)
                ElMessage.success('注册完毕!');
                console.log('注册完成，继续调用 加入机构的 接口')
                this.joinTeamFun()
              }else{
                console.log("注册失败", res)
                let err = res.data.error
                ElMessage.error(err.message);
              }

            }).catch(()=>{
          ElMessage.error('登录失败！');
        });
      }


    }
  }
}
</script>

<style lang="scss" scoped>
$leftWid: 100px;
.centerTxt{ text-align: center; margin-top: 30px; margin-bottom: 50px; }
.centerD{
  padding: 20px;
}
.bluetip{
  font-size: 0.8em;
  color: #0b94ea;
}
.el-checkbox{
  margin-top: 20px;
}
.li-form{
  .li-formItem{
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;
    position: relative;
    .li-ttl{
      position: absolute;
      width: $leftWid;
      text-align: center;
    }
    input.litCode{
      padding-right:150px;
    }
    input{
      font-size: 1.1em;
      color: #333;
      outline: none;
      padding-left: $leftWid;
      border: none;
      display: block;
      height: 30px;
      line-height: 30px;
      padding-bottom: 8px;
      width: 100%;
      background-color:#f8f8f8 ;
      box-sizing: border-box;
    }
    .li-codeBtn{
      border-radius: 3px;
      background-color: $livePageHeadBg;
      position: absolute;
      right: 10px;
      color: #fff;
      padding: 2px 5px;
      font-size: 0.9em;
    }
  }
}
</style>

