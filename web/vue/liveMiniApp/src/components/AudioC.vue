<template>
  <div class='AudioC'>
    <audio ref="audio" @timeupdate="updateProgress" @ended="end" @loadedmetadata="loadedmetadata" controls
           style="display: none" :src="fileUrl" preload="metadata">
      您的浏览器不支持音频播放
    </audio>
    <div class="audioBox">
      <div class="audioInfo">
        <h3>{{ playName }}</h3>
        <div class="progressDiv" ref="progressDiv"><div class="percent" :style="{width: perWidth}"></div></div>
        <p id="time">{{ duration }}/{{ current }}</p>
      </div>
      <el-icon>
        <video-play v-if="!isPlay" @click="audioPlay(true)" class="audioControls" />
        <video-pause v-else @click="audioPlay(false)" class="audioControls" />
      </el-icon>
    </div>
  </div>
</template>

<script>

export default {
  name: "AudioC",
  data() {
    return {
      audio: null,
      contorl: null,
      contorlDot: null,
      contorlProgress: null,
      contorlProgressBox: null,
      current: "00:00",
      currentTime:0,
      duration: "00:00",
      duration2: "00:00",
      customColor:'#000',
      percentage: 0,
      perWidth: 0,
      isPlay: false,
    };
  },
  components: {
  },
  props: {
    fileUrl: {
      type: String,
      default: ''
    },
    playName: {
      type: String,
      default: '歌曲名称未知'
    },
  },
  created() {},
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.audio = this.$refs.audio;
      this.contorl = this.$refs.contorl;
    },
    // 时分秒转换
    transTime(value) {
      let that = this;
      var time = "";
      var h = parseInt(`${value / 3600}`);
      value %= 3600;
      var m = parseInt(`${value / 60}`);
      var s = parseInt(`${value % 60}`);
      var ns = String(value).split('.')[1]
      if(ns){
        ns = String(ns).substr(0,3)
        let nsLen = String(ns).length
        let chaL = 3 - nsLen
        if(chaL === 1){
          ns = ns + '0'
        }else if(chaL === 2){
          ns = ns + '00'
        }else if(chaL === 3){
          ns = '000'
        }
      }else{
        ns = '000'
      }


      m = m >= 10 ? m : ('0' + m )
      s = s >= 10 ? s : ('0' + s )
      if (h > 0) {
        h = h > 10 ? h : ('0' + h )
        time = that.formatTime(h + ":" + m + ":" + s + "." + ns);
      } else {
        time = that.formatTime(m + ":" + s + "." + ns);
      }
      return time;
    },
    // 补零
    formatTime(value) {
      var time = "";
      var s = value.split(":");
      var i = 0;
      for (; i < s.length - 1; i++) {
        time += s[i].length === 1 ? "0" + s[i] : s[i];
        time += ":";
      }
      time += s[i].length === 1 ? "0" + s[i] : s[i];

      return time;
    },
    // 音频播放暂停
    audioPlay(status) {
      if (status) {
        this.audio.play();
        this.$emit("audioPlayLyc", true)

      } else {
        this.audio.pause();
        this.$emit("audioPlayLyc", false)
      }

      this.isPlay = status
    },
    // 更新进度条与当前播放时间
    updateProgress(e) {
      // console.log('e.target.currentTime==', e.target.currentTime)
      this.current = this.transTime(e.target.currentTime);
      this.currentTime = e.target.currentTime
      let value = e.target.currentTime / e.target.duration;
      this.percentage = value * 100
      if(this.$refs.progressDiv){
        let allWidth = this.$refs.progressDiv.offsetWidth
        let rrr = allWidth * value
        this.perWidth = rrr.toFixed(0) + 'px';
      }
    },
    getCurrent(type){
      return type ? this.currentTime : this.current
    },
    getDuration(){
      return this.duration
    },
    // 音频结束
    end(e) {
      this.isPlay = false
      this.$emit("endPlayLyc")
    },
    loadedmetadata(e) {
      this.duration = this.transTime(e.target.duration);
    }
  },
  //如果页面有keep-alive缓存功能，这个函数会触发
  activated() { },
};
</script>
<style scoped lang="scss">
.AudioC {
  .progressDiv{
    width: 95%;
    background: #ccc;
    height: 4px;
    border-radius: 2px;
    .percent{
      background: #0b94ea;
      border-radius: 2px;
      height: 100%;
    }
  }

  .daudio{
    position: relative;
    margin:200px auto;
    width: 311px;
  }
  .audioBox {
    padding: 8px 16px;
    background: #eaebed;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
  }
  .audioControls {
    width: 22px;
    height: 22px;
  }
  .audioInfo {
    width: 100%;
    h3 {
      font-family: PingFangSC-Regular;
      font-size: 14px;
      color: #333333;
      letter-spacing: 0.2px;
      text-align: justify;
      font-weight: 400;
      margin-bottom: 4px;
    }

    p {
      font-family: PingFangSC-Regular;
      font-size: 12px;
      color: #999999;
      letter-spacing: 0.17px;
      text-align: justify;
      font-weight: 400;
    }
  }

}


</style>

