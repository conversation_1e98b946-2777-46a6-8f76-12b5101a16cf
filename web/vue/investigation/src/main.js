// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from 'vue'
import App from './App'
import router from './router'

import VueResource from 'vue-resource'
import moment from 'moment'
import 'moment/locale/zh-cn'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import Vant from 'vant'
import 'vant/lib/index.css'
import Auth from '@/js/auth.js'

Vue.config.productionTip = false
Vue.use(VueResource)
Vue.use(Vant)
Vue.use(ElementUI)

Vue.prototype.$moment = moment;

let auth = new Auth(Vue)
// let auth = new Auth()
// let token = auth.getToken()
// if (typeof token !== 'string' || token.length === 0) {
//   token = auth.getGuestToken()
// }
// if (typeof token !== 'string' || token.length === 0) {
//   router.replace({name: 'error', params: {message: '�������޷�����'}})
// } else {
//   Vue.http.headers.common['token'] = token
// }
// Vue.http.interceptors.push((request, next) => {
//   // request.headers.set('token', auth.getToken())
//   next((response) => {
//     if (typeof response === 'object' && typeof response.headers === 'object' && typeof response.headers.get('token') === 'string' && response.headers.get('token').length > 0) {
//       auth.saveToken(response.headers.get('token'))
//     }
//     return response
//   })
// })

/* eslint-disable no-new */
new Vue({
  el: '#app',
  router,
  components: { App },
  template: '<App/>'
})
