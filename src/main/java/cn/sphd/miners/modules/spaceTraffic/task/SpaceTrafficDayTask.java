package cn.sphd.miners.modules.spaceTraffic.task;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.HttpClientUtils;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.TimeUtils;
import cn.sphd.miners.modules.spaceTraffic.service.SpaceTrafficService;
import cn.sphd.miners.modules.system.service.DlmService;
import cn.sphd.miners.modules.system.service.OrgService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Created by 朱思旭 on 2021/4/23.
 */
public class SpaceTrafficDayTask {
    @Autowired
    DlmService dlmService;
    @Autowired
    SpaceTrafficService spaceTrafficService;
    @Autowired
    OrgService orgService;
    final Logger logger = Logger.getLogger(getClass());

    public void spaceTrafficDayTask() {
        //获取分布式锁
        final Logger logger = Logger.getLogger(getClass());
        logger.warn("空间与流量每日定时任务:开始定时获取分布式锁");
        String lockKey, methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        if ((lockKey=dlmService.getLock(methodName)) != null) {
            logger.warn("空间与流量每日定时任务开始延时，错开其它服务器组的查询：" + NewDateUtils.dateToString(System.currentTimeMillis(), "yyyy/MM/dd-HH:mm:ss:SSS"));
            try {//wyu: 2点到2点半，错开各个服务器组的查询时间
                Thread.sleep((long) (Math.random() * 1800));
            } catch (InterruptedException e) {
                Logger.getLogger(getClass()).warn("Thread sleep exception : ", e);
                Thread.currentThread().interrupt();
                return;
            }
            Long time = System.currentTimeMillis();
            logger.warn("空间与流量每日定时任务开始：" + NewDateUtils.dateToString(System.currentTimeMillis(), "yyyy/MM/dd-HH:mm:ss:SSS"));
            spaceTrafficEveryDayTask();
            logger.warn("空间与流量每日定时任务结束：" + NewDateUtils.dateToString(System.currentTimeMillis(), "yyyy/MM/dd-HH:mm:ss:SSS"));
            logger.warn("空间与流量每日定时任务耗时：" + TimeUtils.toTimeString2sf(System.currentTimeMillis() - time));
            logger.warn("空间与流量每日定时任务:开始释放分布式锁");
            dlmService.releaseLock(methodName, lockKey);
            logger.warn("空间与流量每日定时任务:完成");
        } else {
            logger.warn("空间与流量每日定时任务:未成功获取分布式锁");
        }
    }
    private void spaceTrafficEveryDayTask() {
        final String url=System.getProperty("miners.glptApiRoot")+"/st/getBatchSpaceTrafficByOid.do";
        List<Integer> oids = orgService.getAllOrgIds();
        if (oids.size()>0) {
            final Logger logger = Logger.getLogger(getClass());
            logger.warn("getBatchSpaceTrafficByOid.do oids size=" + oids.size());
            Map<String, String> bodyMap = new HashMap<String, String>(1) {{
                put("oids", JSON.toJSONString(oids));
            }};
            //wyu：由于请求可能超过数据库事务时间，从Service提出来。
            HttpClientUtils client = new HttpClientUtils(url);
            client.setTimeout((int) TimeUnit.HOURS.toMillis(2));
            JsonResult jsonResult = client.jsonResponseToT(client.doPost(null, url, null, null, bodyMap), JsonResult.class);
            String data = (String) jsonResult.getData();
            logger.warn("getBatchSpaceTrafficByOid.do glpt response data : " + data);
            spaceTrafficService.spaceTrafficEveryDayTask(oids, JSONObject.parseObject(data, Map.class));
        } else {
            Logger.getLogger(getClass()).error("spaceTrafficEveryDayTask orgService.getAllOrgIds() get Organizations empty.");
        }
    }
}