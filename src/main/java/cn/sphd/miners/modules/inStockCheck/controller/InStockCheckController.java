package cn.sphd.miners.modules.inStockCheck.controller;

import cn.sphd.miners.modules.material.entity.MtInApplicationItem;
import cn.sphd.miners.modules.material.service.MtStockService;
import cn.sphd.miners.modules.mci.utils.AjaxResult;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/insc")
public class InStockCheckController {
    @Autowired
    MtStockService mtStockService;


    //检验员检验包装
    @RequestMapping("/inboundPackCheck")
    @ResponseBody
    public AjaxResult checkApplicationList(MtInApplicationItem inApplicationItem, User user){
        MtInApplicationItem item = mtStockService.getInApplicationItemById(inApplicationItem.getId());
        if (item != null) {
            item.setInspectPackagingInfo(inApplicationItem.getInspectPackagingInfo());
            item.setInspectPackagingResult(inApplicationItem.getInspectPackagingResult());
            item.setPackagingInfo(inApplicationItem.getPackagingInfo());
            item.setPackagingResult(inApplicationItem.getPackagingResult());
            mtStockService.updateInApplicationItem(item);
        }

        return AjaxResult.toAjax(1);
    }



}
