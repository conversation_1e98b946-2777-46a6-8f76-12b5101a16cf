package cn.sphd.miners.modules.meeting.service;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.modules.meeting.entity.*;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.BadgeNumberCallback;
import com.alibaba.fastjson.JSONArray;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface MeetingService extends BadgeNumberCallback {


    List<MeetingInfo> getMeetingInfoListByUserId(Integer userId);

    void handleMeetingExpire(Integer userId); // 处理会议过期

    void  saveMeetingInfo(User user, Integer[] attendees, List<String> questions, Integer recorderUserId, String topic, Integer planDuration, String planTime, Integer pattern, String matters);

    void  saveMeetingInfoHistory(User user, Integer[] attendees, List<String> questions, Integer recorderUserId, Integer approver, String topic, Integer planDuration, String planTime, Integer pattern, String matters, Long meetingId, String memo, String address);

    List<MeetingInfoHistory> getMeetingInfoHistoriesByCreator(Integer creatorId);

    MeetingInfo getMeetingInfoById(Long id);

    MeetingInfoHistory getMeetingInfoHistoryById(Long id);

    MeetingInfoHistory getMeetingInfoHistoryByMeetingId(Long meetingId);

    List<MeetingInfoHistory> getMeetingInfoHistoriesByMeetingId(Long meetingId,Integer approveStatus);

    void withdrawMeetingApply(MeetingInfoHistory meetingInfoHistory,User loginUser);

    List<MeetingInfoHistory> getMeetingInfoHistoriesInIds(List<Integer> ids);

    JsonResult approvalMeetingMiddle(User user, Long id, Integer approvalStatus, Integer approver,String approveMemo);

    JsonResult approvalMeetingFinal(User user, Long id, Integer approvalStatus,String approveMemo);

    JsonResult approvalMeetingSign(User user, Long id, Integer approvalStatus, Integer signType);

    JsonResult cancelMeeting(User user,Long id);

    void  updateMeetingInfo(MeetingInfo meetingInfo);

    MeetingImage getMeetingImageById(Long id);

    void addMeetingImage(User user,Long id,String[] uploadPaths,String memo,Integer type);

    MeetingParticipant getMeetingParticipant(Integer userId, Long meetingId);

    MeetingParticipantHistory getMeetingParticipantHistory(Integer userId, Long meetingHistoryId);

    JsonResult enterMeeting(User user,Long id);

    List<Map<String,Object>> getMeetingImagesByMeetingIdAndUserId(Long meetingId, Integer userId,Integer type);

    void updateMeetingImage(User user,Long id,String[] uploadPaths,String memo,Integer versionNo);

    MeetingParticipant getMeetingRecorder(Long meetingId);  //单独查询会议记录者

    MeetingParticipantHistory getMeetingRecorderHistory(Long meetingHistoryId);  //单独查询会议记录者


    List<MeetingInfo> recorderMeetingMakeList(Integer userId,Integer approveStatus);

    List<MeetingInfo> founderMeetingMakeList(Integer userId,Integer approveStatus);

    List<MeetingParticipant> getMeetingParticipantsByMeetingId(Long meetingId,Boolean sign);

    List<MeetingQuestion> getMeetingQuestionByMeetingId(Long meetingId);

    List<MeetingImage> getMeetingImagesByMeetingId(Long meetingId,Integer type,Integer userId);

    List<MeetingInfo> participantsMeetingReceiptList(Integer userId);

    JsonResult meetingRecordReceipt(User user, Long id);

    List<MeetingInfo> getHistoryMeetingListByUserId(Integer userId, Date beginDate,Date endDate);

    List<MeetingInfo> screenHistoryMeetingList(Integer userId,Integer promoterId,Integer recorderId,Integer state,Integer recordState);

    void  addMeetingRecordApply(User user,Long id,Integer conveneState,String factTime,String finishTime,Integer[] otherAttendees,String questionJsonArray,String resolutionJsonArray,String speakJsonArray,String[] sitePhotos);

    void approvalMeetingRecord(User user,Long id,Integer approval,String approveMemo);

    Integer getRecorderMeetingMakeNumber(Integer userId, Integer[] approveStatus);  //会议记录-制作角标： 待制作 + 已驳回

    Integer getFounderMeetingMakeNumber(Integer userId, Integer[] approveStatus);  //会议记录—审批角标： 带制作 + 待审批

}

