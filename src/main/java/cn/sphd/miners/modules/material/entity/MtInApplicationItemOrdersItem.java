package cn.sphd.miners.modules.material.entity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Entity( name ="MtInApplicationItemOrdersItem" )
@Table( name ="t_mt_in_application_item_orders_item" )
public class MtInApplicationItemOrdersItem {
    @Id
    @Column(name = "id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;//	id	int	Y		Y		ID
    @Column(name = "org" )
    private Integer org;//	org	int			Y		机构ID
    @Column(name = "orders" )
    private Integer orders;//	orders	int			Y		采购订单ID
    @Column(name = "orders_item" )
    private Integer ordersItem;//	orders_item	int		Y	Y		采购单明细ID
    @Column(name = "application" )
    private Integer application;//	application	int			Y		申请单ID
    @Column(name = "application_item" )
    private Integer applicationItem;//	application_item	int		Y	Y		入库申请表明细
    @Column(name = "allot_quantity" )
    private BigDecimal allotQuantity;//	allot_quantity	decimal(20,10)			Y	默认值:0
    @Column(name = "locked_quantity" )
    private BigDecimal lockedQuantity;//	locked_quantity	decimal(20,10)			Y	默认值:0
    @Column(name = "adjust_quantity" )
    private BigDecimal adjustQuantity;//	adjust_quantity	decimal(20,10)			Y	默认值:0
    @Column(name = "fact_quantity" )
    private BigDecimal factQuantity;//	fact_quantity	decimal(20,10)			Y	默认值:0
    @Column(name = "process_mode" )
    private String processMode;//	process_mode	char(1)			Y		处理:1-正常分配(无异议),2-多余分配到已有未完结订单,3-多余补发订单
    @Column(name = "progress" )
    private Integer progress;//	process_mode	char(1)			进度:0-审批驳回;1-订单已提交,待审批;2-已下单,待到货;3已到货,待检验,4-检验ok,待入库,5-检验不合格,待提交让步评审;6-检验不合格,待让步评审;7-让步评审未通过;Z-完结
    @Column(name = "memo" )
    private String memo;//	memo	varchar(255)			Y		备注
    @Column(name = "creator" )
    private Integer creator;//	creator	int			Y		创建人id
    @Column(name = "create_name" )
    private String createName;//	create_name	varchar(100)			Y		创建人
    @Column(name = "create_date" )
    private Date createDate;//	create_date	datetime			Y		创建时间
    @Column(name = "updator" )
    private Integer updator;//	updator	int			Y		修改人id
    @Column(name = "update_name" )
    private String updateName;//	update_name	varchar(100)			Y		修改人
    @Column(name = "update_date" )
    private Date updateDate;//	update_date	datetime			Y		修改时间
    @Column(name = "operation" )
    private Integer operation;//	operation	char(1)			Y		操作:1-增,2-删,3-修改,4-调整数量
    @Column(name = "previous_id" )
    private Integer previousId;//	previous_id	int			Y		修改前记录ID
    @Column(name = "version_no" )
    private Integer versionNo;//	version_no	int			Y		版本号,每次修改+1


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public Integer getOrdersItem() {
        return ordersItem;
    }

    public void setOrdersItem(Integer ordersItem) {
        this.ordersItem = ordersItem;
    }

    public Integer getApplication() {
        return application;
    }

    public void setApplication(Integer application) {
        this.application = application;
    }

    public Integer getApplicationItem() {
        return applicationItem;
    }

    public void setApplicationItem(Integer applicationItem) {
        this.applicationItem = applicationItem;
    }

    public BigDecimal getAllotQuantity() {
        return allotQuantity;
    }

    public void setAllotQuantity(BigDecimal allotQuantity) {
        this.allotQuantity = allotQuantity;
    }

    public BigDecimal getLockedQuantity() {
        return lockedQuantity;
    }

    public void setLockedQuantity(BigDecimal lockedQuantity) {
        this.lockedQuantity = lockedQuantity;
    }

    public BigDecimal getAdjustQuantity() {
        return adjustQuantity;
    }

    public void setAdjustQuantity(BigDecimal adjustQuantity) {
        this.adjustQuantity = adjustQuantity;
    }

    public BigDecimal getFactQuantity() {
        return factQuantity;
    }

    public void setFactQuantity(BigDecimal factQuantity) {
        this.factQuantity = factQuantity;
    }

    public String getProcessMode() {
        return processMode;
    }

    public void setProcessMode(String processMode) {
        this.processMode = processMode;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getOperation() {
        return operation;
    }

    public void setOperation(Integer operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
}
