/* 文件夹树形目录 */
/*竖型目录树*/
.ty-colFileTree{  float: left;  min-width: 270px; max-width: 500px;  background-color: #f4f5f9;  color: #333;}
.ty-colFileTree .ty-treeItem{  height: 30px;  line-height:30px;  vertical-align: middle;  padding:0 5px;  cursor: default;  }
.ty-colFileTree .ty-treeItemActive{  background-color: #5d9ded; color:#fff   }
.ty-colFileTree .ty-treeItem>i{  vertical-align: top;  color: #666;  margin: 8px 0 0 3px;  width: 15px;  height: 15px;  text-align: center;  }
.ty-colFileTree .ty-treeItem>i.fa-folder,.ty-colFileTree .ty-treeItem>i.fa-file{  color: #ddc667; margin-right: 5px }
.ty-colFileTree .ty-treeItem>i.fa-trash{ color: #666; margin-right: 5px}
.ty-colFileTree .ty-treeItemActive>i.fa{  color: #fff;}

/*.ty-colFileTree .level2,.ty-colFileTree .level3,.ty-colFileTree .level4,.ty-colFileTree .level5{   }*/
.ty-colFileTree li>div:nth-child(2){  margin-left:20px; }
.ty-fileContent .ty-panelHeader{  height: 50px ;  border-bottom:1px solid #48cfad;  margin-bottom: 15px;  }
.ty-fileContent .ty-panelHeader>h3{  margin: 0;  font-size: 16px;  font-weight: 500;  color: #48cfad;  line-height: 50px;  }

.ty-fileItem{ height: 64px;  background-color: #fff;  padding:12px 16px;  margin-bottom:8px;  cursor: pointer;  position: relative;  }
.ty-fileItem:hover{  background-color: #f4f5f9;   }
.ty-fileItemActive{  box-shadow: 0 0 3px #0075ff;   }
.ty-fileType{  width: 36px;  height: 40px;  margin-right:16px;  float: left; background: url("../../../css/content/img/other.png") no-repeat 0 1px; background-size: 36px }
.ty-file_doc,.ty-file_docx{  background-image: url("../../../css/content/img/doc.png") }
.ty-file_xls,.ty-file_xlsx,.ty-file_et{  background-image: url("../../../css/content/img/xls.png") }
.ty-file_ppt,.ty-file_pptx{  background-image: url("../../../css/content/img/ppt.png") }
.ty-file_rar,.ty-file_zip{  background-image: url("../../../css/content/img/rar.png") }
.ty-file_apk,.ty-file_ipa{  background-image: url("../../../css/content/img/apk.png") }
.ty-file_pdf{  background-image: url("../../../css/content/img/pdf.png")}
.ty-file_txt{  background-image: url("../../../css/content/img/txt.png")}
.ty-file_md{  background-image: url("../../../css/content/img/md.png")}
.ty-file_mp3{  background-image: url("../../../css/content/img/mp3.png")}
.ty-file_mp4{  background-image: url("../../../css/content/img/mp4.png")}
.ty-file_jpg,.ty-file_png,.ty-file_gif,.ty-file.jpeg{  background-image: url("../../../css/content/img/jpg.png")}
.ty-fileItem>.ty-fileType>img{  width: 36px;  height: 40px;  background-size: 36px; background-repeat: no-repeat }
.ty-fileItem>.ty-fileInfo{  width:700px ;height: 40px;  }
.ty-fileItem>.ty-fileInfo>.ty-fileName{ width:570px; word-wrap:break-word;  overflow:hidden; text-overflow:ellipsis; white-space: nowrap }
.ty-fileItem>.ty-fileInfo>.ty-fileNo{  width:300px; white-space:nowrap; overflow:hidden; text-overflow:ellipsis;  position: absolute;  right: 70px;  top: 10px; line-height: 20px; text-align: right ;font-size:12px; padding: 2px 10px; border-radius:3px; font-weight:bold; color:#999;}
.ty-fileItem>.ty-fileInfo>.ty-fileVersion{ white-space:nowrap; overflow:hidden; text-overflow:ellipsis;  position: absolute;  right:20px;  top: 12px; font-size:12px; padding: 0 5px; border-radius:3px; border:1px solid #dcdfe6 ; background-color: #fff; color:#24292e; line-height: 16px}
.ty-fileItem .ty-fileInfo .ty-fileDetail{  color: #aaa;  font-size: 13px;  font-weight: 300;  margin-top:2px;  }
.ty-fileItem>.ty-fileHandle>a{  display: block;font-size:12px; padding: 2px 10px; border-radius:3px; font-weight:bold; cursor:pointer; margin-left: 5px; color:#5d9cec;   }
.ty-fileItem>.ty-fileHandle>a.ty-disabled,.ty-fileItem>.ty-fileHandle>span.ty-disabled span{  color: #aaa;  cursor: default;  }
.ty-fileItem>.ty-fileHandle>a:not(.ty-disabled):hover{ background-color:#5d9cec; color:#fff  }
.ty-filePreview{  float: left;  width: 340px;  padding: 5px 20px;  background-color: #f4f5f9;  color: #666;  }
.ty-filePreview .ty-json{  width: 100%;  overflow: hidden;  margin: 10px;  }
.ty-filePreview hr{  border-top: 1px solid #999;  margin: 20px 0;  }
.ty-filePreview .ty-json>.ty-jsonHead{  font-weight: bold ; font-size: 14px ; border-left: 4px solid #5d9cec; padding-left: 10px ;margin: 5px 0}
.ty-filePreview .ty-json>.ty-key{  float: left;  width: 40%; font-weight: bold ; color:#999; }
.ty-filePreview .ty-json>.ty-val{  float: left;  width: 60%; word-wrap: break-word;  word-spacing:normal; }
.ty-fa{width: 15px;  height: 15px;  display: inline-block}

.ty-folderItem{ height: 64px;  background-color: #fff;  padding:12px 16px;  margin-bottom:8px;  cursor: pointer;  position: relative;  }
.ty-folderItem>.ty-folderType{  width: 30px;  height: 40px;  margin-right:20px;  float: left; }
.ty-folderItem>.ty-folderType>i{  color: #ddc667 ; font-size: 36px ; line-height: 40px;}
.ty-folderItem>.ty-folderInfo{  width:700px ;height: 40px;  }
.ty-folderItem>.ty-folderInfo>.ty-folderName{ line-height:20px; min-width:165px; word-wrap:break-word; white-space:nowrap; overflow:hidden; text-overflow:ellipsis; }
.ty-folderItem>.ty-folderInfo>.firstLevel{ line-height:40px; }
.ty-folderItem>.ty-folderInfo>.ty-folderParent{  color: #aaa; }

.departTree { width: 710px; margin: auto }
.departTree .arrow { color: #4E9DFF; font-size:34px;  line-height:300px ; position: relative;  }
.departTree > ul , .departTree>form{ width: 320px; padding:15px; margin: 0 5px;  background-color: #fff; border: 1px solid #dceeff; position: relative; height:300px; overflow:auto;   }
.departTree i.fa{  color: #4E9DFF; margin:0 8px;   }
.departTree i.fa.ty-gray{  color: #aaa  }
.departTree li>div{ position: relative;  height:30px; line-height: 30px   }
.departTree li>div:hover{ background: #eaeaea; cursor: pointer; color: #333; transition: all .2s   }
.departTree li>div>i.fa-plus-square, .departTree li>div>i.fa-minus-square{ float: right;  line-height: 30px   }
.departTree ul ul{ display: none;padding-left:15px;  }


.fileItem{ display: flex; flex-direction: row;  padding:12px 16px;  cursor: pointer;  position: relative; justify-content: space-between; font-size: 12px;background: #fcfcfc;  border-radius: 2px;margin: 1px; }
.fileItem:hover{ background: #f7dfc2  }
.fileItem>.fileType{  width: 36px;  height: 40px; margin-right: 8px ; flex-grow: 0; background-size: 36px; background-repeat: no-repeat}
.fileItem>.fileInfo{  width: 260px; flex-grow: 1}
.fileItem .fileTitle{ font-size: 14px; line-height: 1.2; color: #666 }
.fileItem .fileTitle span{ vertical-align: middle}
.fileItem .fileName{ display: inline-block; max-width:180px; word-wrap:break-word;  overflow:hidden; text-overflow:ellipsis; white-space: nowrap ;}
.fileItem .fileSize{ font-size: 12px}
.fileItem .fileDetail{  flex-grow:1; line-height: 20px; display: flex; justify-content: space-between }
.fileItem .fileDetail .fileDes{  color: #aaa;  }
.fileItem .fileHandle{  flex-shrink: 0;}
.fileItem .fileHandle>a{  color:#5d9cec;  }
.fileItem .fileHandle>a:hover{  color: #5691db; text-decoration: underline  }
.fileItem .fileHandle>i.ty-disabled{  color: #aaa;  cursor: default;  }
.fileItem .fileHandle>i:not(.ty-disabled):hover{ background-color:#5d9cec; color:#fff  }