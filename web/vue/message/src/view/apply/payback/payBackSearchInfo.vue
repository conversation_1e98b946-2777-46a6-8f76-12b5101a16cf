<template>
  <div id="backDetail" v-loading.fullscreen.lock="loading">
    <TY_NavTop :title="ssTtl" :isBackHome="isBackHome"></TY_NavTop>
    <div class="container">
      <div class="ui-message">
        <div class="sendContainer">
          <div class="lHead">
            <p class="line-s">{{customerName}}</p>
            <p class="listCon line-s">本次回款 {{ baseInfo.method | incomeType}} {{baseInfo.amount | formatMoney}}元</p>
          </div>
          <div> <!--1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐-->
            <div class="checkWay" v-if="baseInfo.method === '1'">
              <div class="line-p">
                <span class="tt-cl">收到日期</span>
                <span class="sub-cl">{{baseInfo.receiveDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
            </div>
            <div class="checkWay" v-if="baseInfo.method === '3'">
              <div class="line-p">
                <span class="tt-cl">收到日期</span>
                <span class="sub-cl">{{baseInfo.receiveDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
              <div class="line-p">
                <span class="tt-cl">支票号</span>
                <span class="sub-cl">{{baseInfo.returnNo}}</span>
              </div>
              <div class="line-p">
                <span class="tt-cl">到期日</span>
                <span class="sub-cl">{{baseInfo.expireDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
              <div class="line-p">
                <span class="tt-cl">出具的单位</span>
                <span class="sub-cl ellipsisSet">{{baseInfo.originalCorp}}</span>
              </div>
              <div class="line-p">
                <span class="tt-cl">出具的银行</span>
                <span class="sub-cl">{{baseInfo.bankName}}</span>
              </div>
            </div>
            <div class="checkWay" v-if="baseInfo.method === '4'">
              <div class="line-p">
                <span class="tt-cl">收到日期</span>
                <span class="sub-cl">{{baseInfo.receiveDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
              <div class="line-p">
                <span class="tt-cl">汇票号</span>
                <span class="sub-cl">{{baseInfo.returnNo}}</span>
              </div>
              <div class="line-p">
                <span class="tt-cl">到期日</span>
                <span class="sub-cl">{{baseInfo.expireDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
              <div class="line-p">
                <span class="tt-cl">最初出具的单位</span>
                <span class="sub-cl">{{baseInfo.originalCorp}}</span>
              </div>
              <div class="line-p">
                <span class="tt-cl">出具的银行</span>
                <span class="sub-cl">{{baseInfo.bankName}}</span>
              </div>
            </div>
            <div class="checkWay" v-if="baseInfo.method === '5'">
              <div v-if="(baseInfo.state === '5')">
                <div class="line-s">预计到账的日期范围</div>
                <div class="line-p">
                  <span class="sub-cl">{{baseInfo.expectBeginDate | formatDate('yyyy年MM月dd日')}}</span>
                  -
                  <span class="sub-cl">{{baseInfo.expectEndDate | formatDate('yyyy年MM月dd日')}}</span>
                </div>
              </div>
              <div v-else>
                <div class="line-s">
                  <span class="tt-cl">到期日期</span>
                  <span class="sub-cl">{{baseInfo.expireDate | formatDate('yyyy年MM月dd日')}}</span>
                </div>
                <div class="line-s">
                  <span class="tt-cl">收款银行</span>
                  <span class="sub-cl">{{baseInfo.bankName}}</span>
                </div>
              </div>
            </div>
            <p class="line-p inputerInfo">录入者：{{baseInfo.createName}} {{baseInfo.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</p>
            <p class="line-p inputerInfo" v-if="!(baseInfo.state === '5')">财&nbsp;&nbsp;&nbsp;务：{{baseInfo.financerName}} {{baseInfo.approvalDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { incomeType, formatDate, formatMoney } from '../../../js/common'
export default {
  name: 'paySearchDetail',
  filters: {
    incomeType,
    formatDate,
    formatMoney
  },
  data () {
    return {
      ssTtl: '',
      ssType: '',
      loading: true,
      baseInfo: {},
      collectInfo: {},
      customerName: ''
    }
  },
  created () {
    let itemId = this.$route.params.id
    let type = JSON.parse(localStorage.getItem('collectParams')).type
    this.ssType = type
    if (type === '1') {
      this.ssTtl = '回款录入'
    } else if (type === '2') {
      this.ssTtl = '回款入账'
    }
    this.$http.post('../../../salesBack/collectQueriesDetail.do', {
      'id': itemId
    }, {
      emulateJSON: true
    }).then((response) => {
      let baseDate = response.body.list
      this.customerName = baseDate.customerName
      this.baseInfo = baseDate.slCollectApplication
      this.collectInfo = baseDate.collectIteam[0]
      this.loading = false
    }).catch(function () {
      this.$message({
        type: 'error',
        message: '系统错误，请重试！'
      })
    })
  }
}
</script>
