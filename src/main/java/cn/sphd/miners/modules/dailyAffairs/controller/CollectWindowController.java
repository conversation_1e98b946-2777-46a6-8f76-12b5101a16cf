package cn.sphd.miners.modules.dailyAffairs.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.commodity.entity.SlCustomer;
import cn.sphd.miners.modules.dailyAffairs.service.CollectWindowService;
import cn.sphd.miners.modules.finance.entity.AccountDetail;
import cn.sphd.miners.modules.finance.entity.FinanceAccount;
import cn.sphd.miners.modules.finance.entity.FinanceAccountBill;
import cn.sphd.miners.modules.finance.service.AccountService;
import cn.sphd.miners.modules.finance.service.DataService;
import cn.sphd.miners.modules.sales.entity.SlCollectApplication;
import cn.sphd.miners.modules.sales.service.PdCustomerService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2019/5/14.
 * 1.58 销售之回款   此为回款入账模块(此文件中的接口大多用的是service层)
 */
@Controller
@RequestMapping("/collectWindow")
public class CollectWindowController {

    @Autowired
    CollectWindowService collectWindowService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    PdCustomerService pdCustomerService;
    @Autowired
    AccountService accountService;
    @Autowired
    DataService dataService;

    /**
     *<AUTHOR>
     *@date 2019/5/15 11:06
     *回款入账列表(悬浮窗有回款入账的财务可审批)
    */
    @ResponseBody
    @RequestMapping("/collectPaymentList.do")
    @MessageMapping("/collectPaymentList")
    private JsonResult collectPaymentlist(String sessionid,User user){
//        JSONObject jsonObject = JSON.parseObject(json);
//        String sessionid=jsonObject.getString("session"); //登陆的设备标识
        Integer oid=user.getOid(); //机构id
        List<Map<String,Object>> collectApplications = collectWindowService.collectPaymentList(oid);
        Map<String,Object> map = new HashMap<>();
        map.put("collectApplications",collectApplications);
        map.put("num",collectApplications.size());
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/collectPaymentList",null,null,null,null, JSON.toJSONString(map));
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2019/5/16 16:42
     *查看回款详情(长链接/手机端使用)
    */
    @ResponseBody
    @RequestMapping("/getSlCollectDetail.do")
    @MessageMapping("/getSlCollectDetail")
    private JsonResult getSlCollectDetail(String sessionid,String json){
        JSONObject jsonObject = JSON.parseObject(json);
//        String sessionid=jsonObject.getString("session"); //登陆的设备标识
        Integer collectId=jsonObject.getInteger("collectId"); //回款申请id
        Map<String,Object> map = new HashMap<>();
        SlCollectApplication slCollectApplication = collectWindowService.getSlCollectDetail(collectId);
        SlCustomer slCustomer = pdCustomerService.getPdCustomerById(slCollectApplication.getCustomer());
        map.put("slCollectApplication",slCollectApplication);
        map.put("customerName",slCustomer.getFullName());
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/getSlCollectDetail",null,null,null,null, map);
        return new JsonResult(1,map);
    }
    
    /**
     *<AUTHOR>
     *@date 2019/5/16 17:06
     *财务回款入账确定
    */
    @ResponseBody
    @RequestMapping("/collectPayment.do")
    @MessageMapping("/collectPayment")
    private JsonResult collectPayment(String sessionid,String json,User user){
        JSONObject jsonObject = JSON.parseObject(json);
//        String sessionid=jsonObject.getString("session"); //登陆的设备标识
        Integer collectId=jsonObject.getInteger("collectId"); //回款申请id
        Integer financeAccountId = jsonObject.getInteger("financeAccountId");  //银行id
        Date receiveDate = jsonObject.getDate("receiveDate");  //到账日期
        Integer type = jsonObject.getInteger("type");  // 1-确定 2-取消
//        Integer userId = jsonObject.getInteger("userId");  //登录人id
        String customerName=jsonObject.getString("customerName"); //客户名称
        Map<String,Object> map = new HashMap<>();
        collectWindowService.collectPayment(collectId,financeAccountId,receiveDate,type,user.getUserID(),customerName,map);
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/collectPayment",null,null,null,null, JSON.toJSONString(map));
        return new JsonResult(1,map);
    }

    //---回款中的财务存入的银行获取的是报销中的银行  /reimburseWindow/getAccounts.do

    /**
     *<AUTHOR>
     *@date 2019/5/20 14:46
     *回款入账的查看（放大镜）
    */
    @ResponseBody
    @RequestMapping("/getCollectChecks.do")
    @MessageMapping("/getCollectChecks")
    private JsonResult getCollectChecks(String json){
        JSONObject jsonObject = JSON.parseObject(json);
//        String sessionid=jsonObject.getString("session"); //登陆的设备标识
        Date createDate = jsonObject.getDate("createDate");  //录入日期
        Date approvalDate = jsonObject.getDate("approvalDate");  //受理时间
        String method=jsonObject.getString("method"); //1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
        Integer userIdLogin = jsonObject.getInteger("userIdLogin");  //登录人id
        Integer userId = jsonObject.getInteger("userId");  //录入者id
        Map<String,Object> map = new HashMap<>();
        collectWindowService.getCollectChecks(createDate,approvalDate,method,userIdLogin,userId,map);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2019/6/10 17:45
     *查看回款详情(PC端ajax使用)
     * collectId:回款申请id
    */
    @ResponseBody
    @RequestMapping("/getSlCollectDetailPC.do")
    private void getSlCollectDetailPC(Integer collectId, HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        SlCollectApplication slCollectApplication = collectWindowService.getSlCollectDetail(collectId);
        String customerName = "";  //客户名称
        if (slCollectApplication!=null){
            if (slCollectApplication.getCustomer()!=null) {   //客户名称
                SlCustomer slCustomer = pdCustomerService.getPdCustomerById(slCollectApplication.getCustomer());
                if (slCustomer!=null) {
                    customerName = slCustomer.getFullName();
                }
            }

            if (!MyStrings.nulltoempty(slCollectApplication.getReceiveBank()).isEmpty()){  //收款银行
                FinanceAccount financeAccount = accountService.getFinanceAccountById(Integer.parseInt(slCollectApplication.getReceiveBank()));
                if (financeAccount!=null) {
                    slCollectApplication.setReceiveBankName(financeAccount.getBankName());
                    map.put("saveBankAccount",financeAccount.getBankName()+"+"+financeAccount.getAccount()); //存入支票/汇票的银行名称+存入银行帐号
                }
            }

            if ("3".equals(slCollectApplication.getMethod()) || "4".equals(slCollectApplication.getMethod())){  //承兑汇票和转账支票的数据
                FinanceAccountBill financeAccountBill = dataService.getAccountBillByBillId(slCollectApplication.getFinanceAccountBill());
                if (financeAccountBill!=null){
                    slCollectApplication.setReceiveAccountDate(financeAccountBill.getFinanceReturn().getReceiveAccountDate());  //票据的到账时间
                    slCollectApplication.setDepositDate(financeAccountBill.getFinanceReturn().getDepositDate());   //票据的存入银行时间
                    slCollectApplication.setDepositorName(financeAccountBill.getFinanceReturn().getDepositorName());  //存入经手人
                    slCollectApplication.setReturnId(financeAccountBill.getReturnBill());  //票据id
                    map.put("summary",financeAccountBill.getSummary());  //摘要
                    map.put("purpose",financeAccountBill.getPurpose());  //用途
                 }
            }
            if (slCollectApplication.getFinanceAccountDetail()!=null){
                AccountDetail accountDetail = accountService.getAccountDetailById(slCollectApplication.getFinanceAccountDetail());
                map.put("summary",accountDetail.getSummary());  //摘要
                map.put("purpose",accountDetail.getPurpose());  //用途
            }
        }
        map.put("customerName",customerName);
        map.put("slCollectApplication",slCollectApplication);

        ObjectToJson.objectToJson1(map,new String[]{},response);
    }
    
    /**
     *<AUTHOR>
     *@date 2019/12/4 9:33
     *查询回款中确已收到且已录入系统的数据
     *查询条件是customer(客户ID)和时间
     * method(回款方式:1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐),amount(金额),returnNo（支票号）,originalCorp（原始出具票据单位）
     * bankName（出具支票银行【银行转账的收款银行名称】）,expireDate（到期日期【银行转账的到账日期】）
    */
    @ResponseBody
    @RequestMapping("/getCollectFinance.do")
    public JsonResult getCollectFinance(User user,Integer customer){
        Map<String,Object> map = new HashMap<>();
        Date beginDate = NewDateUtils.changeMonth(new Date(),-5);  //距当前时间是半年（6个月）
        Date endDate = new Date();  //结束时间
        if (user!=null && customer!=null) {
            List<SlCollectApplication> slCollectApplications = collectWindowService.getCollectFinance(user.getOid(),beginDate,endDate,customer);
            map.put("slCollectApplications",slCollectApplications);
        }
        return new JsonResult(1,map);
    }
    
    /**
     *<AUTHOR>
     *@date 2019/12/4 14:57
     *选择已录入系统的数据进行“确定”操作
     * collectId:回款申请id  userId：登录人id  collectPayId:已入账的回款id
    */
    @ResponseBody
    @RequestMapping("/getDetermine.do")
    @MessageMapping("/getDetermine")
    public JsonResult getDetermine(String sessionid,String json,User user){
        Map<String,Object> map = new HashMap<>();
        JSONObject jsonObject = JSON.parseObject(json);
//        String sessionid=jsonObject.getString("session"); //登陆的设备标识
        Integer collectId=jsonObject.getInteger("collectId");  //回款申请id
//        Integer userId=jsonObject.getInteger("userId");  //登录人id
        Integer collectPayId=jsonObject.getInteger("collectPayId");  //回款已入账的id
        if (collectId!=null && user!=null && collectPayId!=null){
            map = collectWindowService.getDetermine(collectId,user.getUserID(),collectPayId);
        }else {
            map.put("status",0);
            map.put("content","操作失败");
        }
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/getDetermine",null,null,null,null, JSON.toJSONString(map));
        return new JsonResult(1,map);
    }

    @ResponseBody
    @RequestMapping("/test.do")
    private void test(HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        Date start = NewDateUtils.changeMonth(new Date(),0);
        Date end = NewDateUtils.changeMonth(new Date(),1);
        for (;start.getTime()<=end.getTime();start = NewDateUtils.changeDay(start,1)) {
            Map<String,Object> map1 = new HashMap<>();
            Date begin = start;
            String type = NewDateUtils.isWeekend(start)?"1":"2";
//            String type = DateUtils.getWeekByDate(start)==0?"1":"2";
            map1.put("start",begin);
            map1.put("type",type);
            map.put("map1",map1);
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }
    
    
}
