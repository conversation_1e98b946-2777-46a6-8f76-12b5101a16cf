package cn.sphd.miners.modules.generalAffairs.service;

import cn.sphd.miners.modules.generalAffairs.dto.WorkOffBlockDto;
import cn.sphd.miners.modules.generalAffairs.entity.PersonnelAttendanceUserDetail;
import cn.sphd.miners.modules.system.entity.User;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.Logger;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContext;
import java.util.Date;
import java.util.List;

/**
 * AttendanceEventSerivce_in_generalAffairs 考勤事件
 * @author: wuyu
 * @since: 4.0
 * @date: 2025-04-27 15:51:25
 **/
public interface AttendanceEventSerivce {
    /**
     * getBlocks 获取时间段内的考勤时间块列表，申请审批类考勤事件需要实现此方法
     * @apiNote 仅用于需要审批的考勤事件
     * <AUTHOR>
     * @since 4.0
     * @param user
     * @param begin
     * @param end
     * @return List<WorkOffBlockDto>
     * @date 2025-05-12 13:24:15
     **/
    default List<WorkOffBlockDto> getBlocks(User user, Date begin, Date end){
        Logger.getLogger(getClass()).warn("getBlocks not implemented yet.");
        return null;
    }
    default Pair<Integer, WorkOffBlockDto> getToUpdateEvent(Integer business){
        Logger.getLogger(getClass()).warn("getToUpdateEvent not implemented yet.");
        return null;
    }
    /**
     * fillNewDetail 为new出来的detail更新各个考勤事件独有的属性赋值
     * <AUTHOR>
     * @since 4.0
     * @param detail
     * @return void
     * @date 2025-05-13 14:38:35
     **/
    default void fillNewDetail(PersonnelAttendanceUserDetail detail){
        Logger.getLogger(getClass()).warn("fillNewDetail not implemented yet.");
    }
    /**
     * getBeanName 获取当前service的名称，可以直接使用
     * <AUTHOR>
     * @since 4.0
     * @return String
     * @date 2025-05-13 14:35:54
     **/
    default String getBeanName() {
        ServletContext sc = ContextLoader.getCurrentWebApplicationContext().getServletContext();
        ApplicationContext ac = WebApplicationContextUtils.getRequiredWebApplicationContext(sc);
        String[] names = ac.getBeanNamesForType(getClass());
        return names != null && names.length > 0 ? names[0] : null;
    }
}
