package cn.sphd.miners.modules.material.service.impl;

import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.modules.accountant.util.DateUtil;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.inv.dao.InvWarehouseLocationDao;
import cn.sphd.miners.modules.inv.entity.InvWarehouseLocation;
import cn.sphd.miners.modules.iws.entity.TIwsMaterialLocation;
import cn.sphd.miners.modules.iws.mapper.TIwsMaterialLocationMapper;
import cn.sphd.miners.modules.iws.mapper.TIwsWarehouseBaseMapper;
import cn.sphd.miners.modules.iws.mapper.TIwsWarehouseLocationMapper;
import cn.sphd.miners.modules.iws.mapper.TIwsWarehouseShelfMapper;
import cn.sphd.miners.modules.material.dao.*;
import cn.sphd.miners.modules.material.entity.*;
import cn.sphd.miners.modules.material.service.MaterielService;
import cn.sphd.miners.modules.material.service.MtService;
import cn.sphd.miners.modules.material.service.MtStockService;
import cn.sphd.miners.modules.personal.dao.ApprovalProcessDao;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.sales.service.PoService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.dao.InitAllotDao;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.entity.ApprovalFlow;
import cn.sphd.miners.modules.system.entity.ApprovalItem;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.ApprovalService;
import cn.sphd.miners.modules.system.service.RoleService;
import cn.sphd.miners.modules.system.service.UserService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Criteria;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("mtService")
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class MtServiceImpl implements MtService {
    @Autowired
    MtBaseDao mtBaseDao;

    @Autowired
    UserDao userDao;

    @Autowired
    MtCategoryDao categoryDao;

    @Autowired
    MtSupplierMaterialDao mtSupplierMaterialDao;

    @Autowired
    MtLocationDao locationDao;

    @Autowired
    MtStockService stockService;

    @Autowired
    InvWarehouseLocationDao warehouseLocationDao;

    @Autowired
    MtStockInfoDao stockInfoDao;

    @Autowired
    MtCategoryDao mtCategoryDao;

    @Autowired
    SrmSupplierDao supplierDao;

    @Autowired
    MtSupplierMaterialDao supplierMaterialDao;

    @Autowired
    MaterielService materielService;

    @Autowired
    PoService poService;

    @Autowired
    UserService userService;

    @Autowired
    ApprovalProcessService approvalProcessService;

    @Autowired
    ApprovalProcessDao approvalProcessDao;

    @Autowired
    SWMessageService swMessageService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    MtBaseHistoryDao mtBaseHistoryDao;
    @Autowired
    MtSupplierMaterialHistoryDao mtSupplierMaterialHistoryDao;
    @Autowired
    UserSuspendMsgService suspendMsgService;
    @Autowired
    MtUnitDao mtUnitDao;

    @Autowired
    MtStockDetailDao stockDetailDao;

    @Autowired
    PoContractMaterialDao poContractMaterialDao;
    @Autowired
    PoContractBaseDao poContractBaseDao;
    @Autowired
    ApprovalService approvalService;
    @Autowired
    RoleService roleService;
    @Autowired
    InitAllotDao initAllotDao;
    @Autowired
    TIwsMaterialLocationMapper mtLocationMapper;

    @Autowired
    MtPackagingInfoDao mtPackagingInfoDao;


    @Autowired
    TIwsWarehouseBaseMapper warehouseBaseMapper;

    @Autowired
    TIwsWarehouseShelfMapper warehouseShelfMapper;

    @Autowired
    TIwsWarehouseLocationMapper warehouseLocationMapper;

    @Override
    public Map operation(MtBase base, String operation, Integer userId) {
        Map map = new HashMap();
        User user = userDao.get(userId);
        String userName = user.getUserName();
        List<MtBase> mtBases = materielService.getMtBaseByOidAndCode(user.getOid(), null);

        BigDecimal zero = new BigDecimal(0);

        if ("1".equals(operation) || "3".equals(operation)) {
            if (base.getId() != null) {
                MtBase mtBase = materielService.getMtBaseById(base.getId());

                mtBases.remove(mtBase);
            }

            for (MtBase m : mtBases) {

                String arr[] = new String[]{"3", "4", "5", "6", "7"};
                if (!Arrays.asList(arr).contains(base.getOrigin())) {

                    if (base.getCode().equals(m.getCode())) {

                        map.put("code", "2");
                        map.put("message", "已有相同代号材料，不可修改或增加！");
                        return map;
                    }
                }
            }
        }
        if (StringUtils.isBlank(operation))
            operation = "9";
        switch (operation) {
            case "1":
                MtCategory category = categoryDao.get(base.getCategory_());

                base.setCategory(category);
                base.setCreateDate(new Date());
                base.setCreateName(userName);
                base.setEnabled("1");
                base.setSource("2");
                base.setPreviousId(0);
                base.setVersionNo(0);
                base.setOrg(category.getOrg_());
                base.setOperation("1");
                mtBaseDao.save(base);

                MtStockInfo stockInfo = new MtStockInfo();

                stockInfo.setMaterial(base);
                stockInfo.setCreateDate(new Date());
                stockInfo.setCreator(userId);
                stockInfo.setCreateName(userName);
                stockInfo.setCurrentStock(zero);
                stockInfo.setInitialStock(null); // 设置为null。null为为设置过初始库存
                stockInfo.setAvailableStock(zero);
                stockInfo.setMinimumStock(zero);
                stockService.saveMtStockInfo(stockInfo);
                break;
            case "3": // 修改情况，避免影响到采购订单，保留一条修改历史
                MtBase mtBase = materielService.getMtBaseById(base.getId());

                MtBase old = materielService.getMtBaseVersionAndPreVersion(-1, mtBase.getId());
                if (old == null) {
                    old = new MtBase();

                    BeanUtils.copyProperties(mtBase, old);

                    old.setId(null);
                    old.setPreviousId(mtBase.getId());
                    old.setEnabled("0");
                    old.setVersionNo(-1); // 版本号为-1 订单列表那直接取-1
                    old.setMtQualityMtBaseViaId(null);
                    old.setProduct(null);
                    old.setCategory(null);
                    old.setMtStockInfoHashSet(null);
                    old.setMtSupplierHistoryHashSet(null);
                    old.setMtSupplierMaterialHashSet(null);
                    old.setPdPackHashSet(null);
                    old.setOperation("3");
                    mtBaseDao.save(old);
                }

                MtBaseHistory baseHistory = new MtBaseHistory();
                BeanUtils.copyProperties(mtBase, baseHistory);
                baseHistory.setOperation("3");
                baseHistory.setMaterial(mtBase.getId());
                baseHistory.setId(null);
                mtBaseHistoryDao.save(baseHistory);

                MtCategory mtCategory = categoryDao.get(base.getCategory_());

                mtBase.setUpdateDate(new Date());
                mtBase.setUpdateName(userName);
                mtBase.setCreator(base.getCreator());
                mtBase.setPreviousId(base.getId());
                mtBase.setVersionNo(base.getVersionNo() == null ? 1 : base.getVersionNo() + 1);

                mtBase.setCategory(mtCategory);
                mtBase.setName(base.getName());
                mtBase.setCode(base.getCode());
                mtBase.setUnitId(base.getUnitId());
                mtBase.setSpecifications(base.getSpecifications());
                mtBase.setModel(base.getModel());
                mtBase.setMemo(base.getMemo());
                mtBase.setExpRequired(base.getExpRequired());
                mtBase.setExpirationDays(base.getExpirationDays());
                mtBase.setOpenDuration(base.getOpenDuration());
                mtBase.setRelatedItem(base.getRelatedItem());
                mtBase.setSameExpiration(base.getSameExpiration());
                mtBase.setOperation("3");

                // 修改后 更改初始化状态
                if ("1".equals(mtBase.getIsInitialized())) {
                    mtBase.setIsInitialized(null);
                }
                mtBaseDao.saveOrUpdate(mtBase);
                break;
            case "2":
                base = mtBaseDao.get(base.getId());

                if (StringUtils.isNotBlank(base.getIsAppointed()))
                    if (!"0".equals(base.getIsAppointed())) {
                        map.put("code", 2);
                        map.put("message", "系统不支持“删除”已有采购数据的材料，而此材料在系统中已有与采购有关的数据！");

                        return map;
                    }
                if (StringUtils.isNotBlank(base.getOrigin())) {
                    String arr[] = new String[]{"3", "4", "5", "6", "7"};
                    if (Arrays.asList(arr).contains(base.getOrigin())) {
                        map.put("code", 2);
                        map.put("message", "此材料与产品有关，不可在此删除！");

                        return map;
                    }
                }

                mtBaseDao.deleteById(base.getId());
                break;
            case "4": // 暂停或恢复采购
                if (base.getProduct_() != null) {
                    MtBase b = mtBaseDao.get(base.getId());
                    MtSupplierMaterial supplierMaterial = mtSupplierMaterialDao
                            .getByHQL(" from MtSupplierMaterial o where 1=1 and o.id=?0", base.getProduct_());
                    // 暂停采购后，若当前材料为此供应商独家供应，则独家供应材料-1
                    boolean exclusive = this.exclusiveByMtIdAndSupplier(supplierMaterial.getMaterial_(),
                            supplierMaterial.getSupplier());

                    SrmSupplier supplier = supplierDao.get(supplierMaterial.getSupplier());
                    if ("0".equals(base.getEnabled())) { // 暂停采购后，供应商供应的材料减1
                        b.setSupplierNumber((b.getSupplierNumber() == null ? 0 : b.getSupplierNumber()) - 1);
                        supplier.setSupplyCount(supplier.getSupplyCount() == null ? 0 : supplier.getSupplyCount() - 1);

                        if (exclusive) { // 为独家供应
                            supplier.setExclusiveCount(
                                    supplier.getExclusiveCount() == null ? 0 : supplier.getExclusiveCount() - 1);
                        }
                    }
                    if ("1".equals(base.getEnabled())) { // 恢复采购后，供应商供应的材料加1
                        b.setSupplierNumber((b.getSupplierNumber() == null ? 0 : b.getSupplierNumber()) + 1);
                        b.setEnabled(base.getEnabled());
                        supplier.setSupplyCount(supplier.getSupplyCount() == null ? 0 : supplier.getSupplyCount() + 1);
                    }
                    supplierMaterial.setEnabled(base.getEnabled());
                    supplierMaterial.setEnabledTime(new Date());
                    supplierMaterial.setUpdateDate(new Date());
                    supplierMaterial.setUpdateName(userName);
                    supplierMaterial.setApproveStatus("2");

                    mtSupplierMaterialDao.update(supplierMaterial);

                    exclusive = this.exclusiveByMtIdAndSupplier(supplierMaterial.getMaterial_(),
                            supplierMaterial.getSupplier());
                    if (exclusive) {
                        if ("1".equals(base.getEnabled()))
                            supplier.setExclusiveCount(
                                    supplier.getExclusiveCount() == null ? 0 : supplier.getExclusiveCount() + 1);
                    }

                    mtBaseDao.update(b);

                    supplierDao.update(supplier);
                } else {
                    String enabled = base.getEnabled();
                    base = mtBaseDao.get(base.getId());
                    base.setEnabled(enabled);
                    base.setEnabledTime(new Date());
                    base.setUpdateDate(new Date());
                    base.setUpdateName(userName);
                    mtBaseDao.update(base);
                }

                break;
            case "9": // 确认为无需定点采购后，n-1 向原辅材料库”权限获得者以“系统中增加了m1+m3种材料。的消息
                if (base.getProduct_() != null) {
                    base = mtBaseDao.get(base.getId());
                    MtSupplierMaterial material = mtSupplierMaterialDao.get(base.getProduct_());
                    material.setIsAppointed("9");
                    material.setAppointedTime(new Date());
                    material.setAppointedUser(userName);

                    base.setAppointedTime(new Date());
                    base.setAppointedUser(userName);
                    mtBaseDao.update(base);
                    mtSupplierMaterialDao.update(material);
                } else {
                    base = mtBaseDao.get(base.getId());
                    base.setIsAppointed("9");
                    base.setAppointedTime(new Date());
                    base.setAppointedUser(userName);
                    base.setUpdateName(userName);
                    base.setUpdateDate(new Date());
                    mtBaseDao.update(base);
                }
                break;
            case "10":
                base = mtBaseDao.get(base.getId());

                break;
        }

        String sql =
                "SELECT mb.exp_required,mb.open_duration,mb.related_item,mb.same_expiration,mb.expiration_days,mb.id, mb.origin,mb.is_purchased,mb.`name`,mb.update_name,mb.enabled_time, mb.enabled,mb.memo,mb.version_no,mb.is_appointed,mc.name as category_name,mc.id as category_id,mb.`code`, mb.specifications, mb.model, mb.create_name,mb.create_date, mu.id as unit_id,mu.name as unit, mb.location_number, mb.supplier_number, si.current_stock, si.minimum_stock,si.initial_stock FROM t_mt_base mb left JOIN t_mt_stock_info si ON si.material = mb.id LEFT JOIN t_mt_category mc ON mb.category = mc.id left join t_mt_unit mu on mb.unit_id=mu.id WHERE mb.id=?0";

        if ("10".equals(operation)) {
            map = mtBaseDao.findMapByConditionNoPage(sql, new Object[]{base.getId() == null ? 0 : base.getId()});

            String orderCount =
                    "SELECT COUNT(DISTINCT po.id) from PoOrders po LEFT JOIN PoOrdersItem poi ON poi.orders = po.id LEFT JOIN MtSupplierMaterial msm on poi.supplierMaterial = msm.id LEFT JOIN MtBase mb ON msm.material = mb.id where po.org=?0 and mb.id=?1";
            Long order_count = (Long) locationDao.getDtoByHql(orderCount, user.getOid().longValue(), base.getId());
            map.put("order_count", order_count);
        } else
            map = new HashMap();

        StringBuilder path = new StringBuilder();
        if (base.getCategory_() != null) {
            MtCategory c = categoryDao.get(base.getCategory_());

            path.append(c.getName());
            while (c.getParent_() != null) {
                c = categoryDao.get(c.getParent_());
                path.insert(0, c.getName() + ">");
            }
        }
        map.put("path", path);

        if (!path.toString().isEmpty()) {
            String firstGradeCategory = path.toString().split(">")[0];
            // 如果不属于构成商品的原辅材料，外购成品，商品的包装物，则可以修改
            if (!"构成商品的原辅材料".equals(firstGradeCategory) && !"外购成品".equals(firstGradeCategory)
                    && !"商品的包装物".equals(firstGradeCategory)) {
                map.put("canEdit", true);
            } else {
                map.put("canEdit", false);
            }

            // firstGradeCategory如果是商品的原辅材料 origin返回3 如果是外购成品 返回4 如果是商品的包装物返回5
            if ("构成商品的原辅材料".equals(firstGradeCategory)) {
                map.put("origin", 3);
            } else if ("外购成品".equals(firstGradeCategory)) {
                map.put("origin", 4);
            } else if ("商品的包装物".equals(firstGradeCategory)) {
                map.put("origin", 5);
            }
        }

        map.put("code", 1);
        map.put("message", "success");
        return map;
    }

    // 判断是否为独家供应
    public Boolean exclusiveByMtIdAndSupplier(Integer material, Integer supplier) {
        List<MtSupplierMaterial> mtSupplierMaterials = mtSupplierMaterialDao
                .getListByHQL(" from MtSupplierMaterial o where o.material_ = ?0 and ifnull(o.enabled,0)=1", material);
        if (mtSupplierMaterials != null && mtSupplierMaterials.size() == 1) {
            for (MtSupplierMaterial supplierMaterial : mtSupplierMaterials) {
                if (supplierMaterial.getSupplier().equals(supplier)) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public List<ApprovalProcess> getApprovalProcessByFromUserId(Integer fromUserId, String approvalStatus,
                                                                Integer businessType, Date beginTime, Date endTime, String sort) {
        Map<String, Object> map = new HashMap<>();
        String hql = "from ApprovalProcess where fromUser=:userId  and approveStatus=:approvalStatus ";
        map.put("userId", fromUserId);
        map.put("approvalStatus", approvalStatus);

        if (businessType != null) {
            hql += " and businessType=:businessType";
            map.put("businessType", businessType);
        }
        if (beginTime != null && endTime != null) {
            hql += " and createDate between :beginTime and :endTime";
            map.put("beginTime", beginTime);
            map.put("endTime", endTime);
        }

        hql += " group by business,approveMemo";
        if (!MyStrings.nulltoempty(sort).isEmpty()) {
            hql += " order by handleTime";
            if ("desc".equals(sort)) {
                hql += " desc";
            }
        }
        List<ApprovalProcess> approvalProcesses = approvalProcessDao.getListByHQLWithNamedParams(hql, map);

        return approvalProcesses;
    }

    @Override
    public Map getList(String state, Integer oid, String keyword, Integer category, Integer supplier, Integer menu,
                       Integer type, Integer pageNum, Integer per, Integer isConsistent) {
        if (type == null)
            type = 0;
        if (menu == null)
            menu = 0;
        String sql =
                """
                        SELECT DISTINCT COUNT(DISTINCT iml.location) as location_number,
                        	mb.exp_required,
                        	mb.expiration_days,
                        	mb.open_duration,
                        	mb.related_item,
                        	mb.same_expiration,
                        	si.create_date AS si_create_date,
                        	si.create_name AS si_create_name,
                        	si.update_date AS si_update_date,
                        	si.update_name AS si_update_name,
                        	mb.update_name AS mb_update_name,
                        	mb.update_date AS mb_update_date,
                        	msa.update_name,
                        	msa.enabled_time,
                        	msa.id AS supplier_material_id,
                        	msa.material_invoiceable,
                        	msa.unit_price,
                        	msa.unit_price_notax,
                        	msa.minimum_purchase,
                        	msa.perchase_cycle,
                        	mc.id AS category_id,
                        	pmc.NAME AS p_name,
                        	mb.id,
                        	mb.`name`,
                        	mb.is_purchased,
                        	mb.is_appointed,
                        	mb.`code`,
                        	mb.specifications,
                        	mb.model,
                        	mb.create_name,
                        	mb.memo,
                        	mb.create_date,
                        	mu.id AS unit_id,
                        	mu.NAME AS unit,
                        	mb.supplier_number,
                        	ifnull( si.current_stock, 0 ) AS current_stock,
                        	(
                        	SELECT
                        		SUM( mm.minimum_stock )
                        	FROM
                        		t_mt_supplier_material mm
                        	WHERE
                        		mm.material = mb.id
                        		AND ifnull( mm.enabled, 0 )!= 0
                        	) AS minimum_stock,
                        	si.initial_stock,
                        	msa.price_stable,
                        	msa.is_par_value,
                        	msa.at_par,
                        	msa.inclusive_freight,
                        	msa.material_invoice_category,
                        	msa.material_tax_rate,
                        	msa.is_tax,
                        	msa.tax_rate,(
                        	SELECT
                        		sum( poi.transit_quantity )
                        	FROM
                        		t_po_orders_item poi
                        	WHERE
                        		poi.supplier_material = msa.id
                        	) AS way_num
                        FROM
                        	t_mt_base mb
                        	LEFT JOIN t_mt_stock_info si ON si.material = mb.id
                        	LEFT JOIN t_mt_category mc ON mb.category = mc.id
                        	LEFT JOIN t_pd_base pb ON pb.id = mb.product
                        	LEFT JOIN (
                        	SELECT
                        		*
                        	FROM
                        		t_mt_category
                        	WHERE
                        	NAME NOT IN ( '商品' )) pmc ON mc.first_grade_id = pmc.id
                        	LEFT JOIN t_mt_supplier_material msa ON msa.material = mb.id
                        	LEFT JOIN t_srm_supplier ms ON msa.supplier = ms.id
                        	LEFT JOIN t_mt_location ml ON ml.material = mb.id
                        	LEFT JOIN t_mt_unit mu ON mb.unit_id = mu.id
                        	LEFT JOIN t_po_orders_item poi ON poi.supplier_material = msa.id
                        	LEFT JOIN t_iws_material_supplier_stat iwss ON iwss.material = mb.id
                        	LEFT JOIN t_iws_material_location iml on iml.material = mb.id
                        WHERE
                        	mc.org = ?0
                        """;
        if (state == null)
            state = "0";
        if (StringUtils.isNotBlank(state))
            switch (state) {
                // 查询待确认定点信息列表
                case "1":
                    sql += " and ifnull(mb.is_appointed,0)=0 and ifnull(mb.enabled,0)!=0";
                    break;
                // 查询暂停采购的材料
                case "2":
                    sql += " and ifnull(mb.enabled,0)=0 and ifnull(mb.version_no,0)>=0";
                    sql += " and pmc.name not in ('半成品','办公用品')";
                    break;
                // 待录入初始库存数量
                case "3":
                    sql +=
                            " and (si.initial_stock is null or si.initial_stock = 0) AND IFNULL(mb.is_initialized,0)=0 and ifnull(mb.is_purchased,0)=1 and (ifnull(mb.is_appointed,0)!=0)";
                    break;
                // 已进入材料库
                case "4":
                    // sql += " and ifnull(mb.enabled,0)!=0 and (( IFNULL(mb.is_purchased,0)=0 AND
                    // ifnull(mb.is_appointed, 0) != 0) OR ( (IFNULL(mb.is_purchased,0)=1 AND
                    // ifnull(mb.is_appointed, 0) != 0 AND ifnull( mb.operation, 0 ) in (7,8,9)) ))";
                    sql +=
                            " and ifnull(mb.enabled,0)!=0 and ((ifnull(mb.is_appointed, 0) != 0) OR ( (IFNULL(mb.is_purchased,0)=1 AND ifnull(mb.is_appointed, 0) != 0  AND ifnull( mb.operation, 0 ) in (7,8,9)) ))";
                    break;
                // 已录入初始库存
                case "5":
                    sql +=
                            "  and si.initial_stock is not null and ifnull(mb.is_purchased,0)=1 and (ifnull(mb.is_appointed,0)!=0)";
                    break;
                // 已录入初始库存且初始库存为0

                case "6":
                    sql +=
                            "  and si.initial_stock is not null and si.initial_stock = 0 and ifnull(mb.is_purchased,0)=1 and (ifnull(mb.is_appointed,0)!=0)";
                    break;

                // 一录入初始库存且初始库存大于等于0
                case "7":
                    sql +=
                            "  and si.initial_stock is not null and si.initial_stock > 0 and ifnull(mb.is_purchased,0)=1 and (ifnull(mb.is_appointed,0)!=0)";
                    break;
                // 未录入初始库存数量或者已录入初始库存数量大于0
                case "8":
                    sql +=
                            "  and (si.initial_stock is null or si.initial_stock > 0) AND IFNULL(mb.is_initialized,0)=0 and ifnull(mb.is_purchased,0)=1 and (ifnull(mb.is_appointed,0)!=0)";
                    break;

                default:
                    sql += "";

                    if (menu == 2)
                        sql += "  and (ifnull(mb.enabled,0)!=0 and ifnull(mb.is_appointed,0)!=0)";
                    else
                        sql += " and ifnull(mb.enabled,0)!=0";
                    break;
            }

        // sql += " and pmc.name not in ('商品')";
        if (StringUtils.isNotBlank(keyword))
            sql += " and (mb.name like '%" + keyword + "%' or mb.code like '%" + keyword + "%')";

        // if (type == 1)
        // sql += " and ( ifnull(mb.enabled,0)!=0 and ifnull(mb.is_appointed,0)!=0
        // )";//仅查询已进入材料库的材料

        if (category != null) {
            sql += " and (mc.id=" + category + " or mc.first_grade_id=" + category + ")";
        }
        if (supplier != null)
            sql += " and ms.id=" + supplier + " and ifnull(msa.enabled,0)!=0";

        if (isConsistent != null) {

            sql += " and IFNULL(iwss.is_consistent,0)=" + isConsistent;


        }

        sql += " and ifnull(mb.source,0)!=0";

        sql += " GROUP BY mb.id";

        Map all = mtBaseDao.findMapByConditionNoPage(sql, new Object[]{oid});

        if (pageNum != null)
            sql += " limit " + (pageNum - 1) * per + "," + per;

        Map map = mtBaseDao.findMapByConditionNoPage(sql, new Object[]{oid});

        List allList = (List) all.get("data");
        String countSql =
                "SELECT COUNT(DISTINCT mb.id) as id FROM t_mt_base mb left JOIN t_mt_stock_info si ON si.material = mb.id LEFT JOIN t_mt_category mc ON mb.category = mc.id where  mc.org = ?0 and ifnull(mb.is_appointed,0)=0 and ifnull(mb.enabled,0)!=0 and ifnull(mb.source,0)!=0";

        map.put("count", mtBaseDao.getListBySQL(countSql, oid));

        String enabledCount =
                "SELECT COUNT(DISTINCT mb.id) as id FROM t_mt_base mb left JOIN t_mt_stock_info si ON si.material = mb.id LEFT JOIN t_mt_category mc ON mb.category = mc.id where  mc.org = ?0 and ifnull(mb.enabled,0)=0 and ifnull(mb.source,0)!=0";
        List list = mtBaseDao.getListBySQL(enabledCount, oid);
        map.put("enabledCount", list == null && list.size() == 0 ? 0 : list.get(0));

        String hql =
                "SELECT DISTINCT c.id, c.name, c.parent, c.LEVEL, c.org, ( SELECT COUNT( DISTINCT id ) FROM t_mt_base b WHERE b.category = c.id AND ( ifnull( b.enabled, 0 ) != 0)) AS count FROM t_mt_category c LEFT JOIN t_mt_base mb ON mb.category = c.id WHERE c.org =?0 AND c.`name` NOT in ('商品','半成品','办公用品')";
        if (menu == 2)
            hql =
                    "SELECT DISTINCT c.id, c.name, c.parent, c.LEVEL, c.org, ( SELECT COUNT( DISTINCT id ) FROM t_mt_base b WHERE b.category = c.id AND ( ifnull( b.enabled, 0 ) != 0)  and (ifnull(b.enabled,0)!=0 and ifnull(b.is_appointed,0)!=0)) AS count FROM t_mt_category c LEFT JOIN t_mt_base mb ON mb.category = c.id WHERE c.org =?0 AND c.`name` NOT in ('商品','半成品','办公用品')";
        if ("4".equals(state))
            hql =
                    "SELECT DISTINCT c.id, c.name, c.parent, c.LEVEL, c.org , c.first_grade_id , ( SELECT COUNT(DISTINCT b.id) FROM t_mt_base b WHERE (b.category = c.id AND (ifnull(b.enabled, 0) != 0) AND ((ifnull(b.is_appointed, 0) != 0) OR (IFNULL(b.is_purchased, 0) = 1 AND ifnull(b.is_appointed, 0) != 0 AND ifnull(b.operation, 0) IN (7, 8, 9)))) ) AS count FROM t_mt_category c LEFT JOIN t_mt_base mb ON mb.category = c.id LEFT JOIN t_mt_stock_info si ON si.material = mb.id WHERE c.org = ?0 AND c.`name` NOT IN ('商品', '半成品', '办公用品')";

        Map categories = categoryDao.findMapByConditionNoPage(hql, new Object[]{oid});
        map.put("categories", categories.get("data"));

        map.put("pageNum", pageNum);
        map.put("per", per);
        if (allList == null)
            allList = new ArrayList();
        Integer totalCount = allList.size() / 20 + (allList.size() % 20 > 0 ? 1 : 0);
        map.put("totalCount", totalCount);
        map.put("totalSize", allList.size());
        return map;
    }

    @Override
    public Map getSuppliersByMbId(Integer id, String enabled) {
        MtBase mtBase = mtBaseDao.get(id);

        String sql =
                "SELECT mu.name as unit_name,mu.id as unit_id,msm.id, msm.invoice_able, msm.material_invoiceable, msm.is_tax, msm.is_appointed, msm.appointed_time, msm.appointed_user, mb.appointed_time as mb_appointed_time, mb.appointed_user as mb_appointed_user, mb.enabled_time as mb_enabled_time, mb.update_name as mb_enabled_name, mb.enabled as mb_enabled, ms.invoicable, msm.id as material_supplier_id, ms.id as supplier_id, ms.`name`, ms.full_name, ms.contact, ms.code_name, msm.enabled_time, msm.update_name as enabled_name, msm.contract_sn, msm.unit_price, msm.unit_price_notax, msm.create_name, msm.create_date, msm.creator, msm.perchase_cycle, msm.minimum_stock, msm.initial_stock, pb.current_stock, (SELECT COUNT(*) FROM t_mt_location m WHERE m.material = mb.id AND m.supplier_material = msm.id) as location_number, (SELECT COUNT(DISTINCT tn.id) FROM t_mt_supplier_material tn WHERE tn.material = mb.id and IFNULL(tn.enabled, 0) = 0) as ended, msm.enabled FROM t_mt_base mb LEFT JOIN t_mt_supplier_material msm ON msm.material = mb.id LEFT JOIN t_srm_supplier ms ON msm.supplier = ms.id LEFT JOIN t_mt_stock_info si ON si.material = mb.id LEFT JOIN t_pd_base pb ON pb.id = mb.product LEFT JOIN t_mt_unit mu on mb.unit_id = mu.id  where mb.id = ?0 ";

        if (StringUtils.isNotBlank(enabled))
            sql += " and ifnull(msm.enabled,0)='" + enabled + "'";
        else if (StringUtils.isNotBlank(enabled) && "-1".equals(enabled)) {
            // 传-1 查全部数据 孙文修改
        } else {
            sql += " and ifnull(msm.enabled,0)<>0";
        }

        String enabledCount =
                "select COUNT(mm.id) FROM t_mt_supplier_material mm WHERE mm.material = ?0 AND IFNULL(mm.enabled,0) =0";

        List list = mtBaseDao.getListBySQL(enabledCount, id);

        Map map = mtBaseDao.findMapByConditionNoPage(sql, new Object[]{id});

        List<HashMap> maps = (List<HashMap>) map.get("data");
        if (maps != null) {
            for (HashMap hashMap : maps) {
                Integer msmId = (Integer) hashMap.get("id");
                Integer supplierId = (Integer) hashMap.get("supplier_id");
                // 获取包装信息
                List<MtPackagingInfo> mtPackagingInfos = mtPackagingInfoDao.getListByHQL(
                        "from MtPackagingInfo o where o.supplierMaterial=?0 and o.supplier=?1 and o.material=?2", msmId,
                        supplierId, id);
                if (mtPackagingInfos != null) {
                    hashMap.put("packagingInfos", mtPackagingInfos);
                }
            }
        }

        map.put("is_appointed", mtBase.getIsAppointed());
        map.put("appointed_time", mtBase.getAppointedTime());
        map.put("appointed_user", mtBase.getAppointedUser());
        map.put("enabled_time", mtBase.getEnabledTime());
        map.put("enabled", mtBase.getEnabled());
        map.put("update_name", mtBase.getUpdateName());
        map.put("enabledCount", list == null && list.size() == 0 ? 0 : list.get(0));
        return map;
    }

    @Override
    public Map getStocksByMtId(Integer id) {

        String sql = "";
        return null;
    }

    @Override
    public Map getLocationDetails(Integer id, Integer supplier, Integer relationId) {

        Map map = new HashMap();

        String sql = " from MtSupplierMaterial msm where 1=1";
        if (id != null && supplier != null)
            sql += " and msm.material=" + id + " and msm.supplier=" + supplier;
        if (relationId != null)
            sql += " and msm.id=" + relationId;

        Map orders = poService.getOrdersByMtIdAndSupplier(id, supplier);

        List list = (List) orders.get("data");

        MtSupplierMaterial mtSupplierMaterial = mtSupplierMaterialDao.getByHQL(sql);

        mtSupplierMaterial.setMaterial(null);

        SrmSupplier mtSupplier = materielService.getSrmSupplierById(mtSupplierMaterial.getSupplier());

        MtSupplierMaterial supplierMaterial =
                materielService.getMtSupplierMaterialBySupplierAndMaterial(mtSupplierMaterial.getSupplier(), null);

        if (mtSupplier != null)
            mtSupplier.setSupplierMaterial(supplierMaterial);

        map.put("mtSupplierMaterial", mtSupplierMaterial);
        map.put("supplier", mtSupplier);
        if (mtSupplier != null)
            map.put("ys", getLocationHisDetails(mtSupplierMaterial, mtSupplier));

        if (list != null)
            map.put("orders", list.size());
        else
            map.put("orders", 0);
        return map;
    }

    // 获取供应商承兑汇票状态
    @Override
    public Integer draftState(Integer supplierId) {

        Integer state = null; // 0-没有任何材料设置过 1-有材料设置过
        SrmSupplier supplier = supplierDao.get(supplierId);
        if (supplier != null) {
            List<MtSupplierMaterial> supplierMaterials = mtSupplierMaterialDao.getBySupplierId(supplierId);
            if (supplierMaterials != null) {
                for (MtSupplierMaterial supplierMaterial : supplierMaterials) {
                    if (supplierMaterial.getDraftAcceptable() != null && supplierMaterial.getDraftAcceptable() == 2) {
                        state = 2;
                    }
                    //
                    // state=2;
                    if (supplierMaterial.getDraftAcceptable() != null && supplierMaterial.getDraftAcceptable() == 1) {
                        state = 1;
                        break;
                    }
                }
            } else {
                state = -1;
            }

            // supplier.setDraftAcceptable(state==-1?2:state);
        }
        // supplierDao.update(supplier);
        return state;
    }

    public Map getLocationHisDetails(MtSupplierMaterial history, SrmSupplier sh) {

        Map map = new HashMap();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        if (history != null) {
            Map param = new HashMap();
            param.put("supplier", sh.getId());
            param.put("material", history.getMaterial_());
            // 合同查询
            List<PoContractMaterial> list = poContractMaterialDao.getListByHQLWithNamedParams(
                    "from PoContractMaterial where contract in (select id from PoContractBase where supplier=:supplier and enabled=1 and is_expired=0) and material=:material",
                    param);

            // 供应商信息
            String ht = ""; // 合同元素
            if ("1".equals(history.getHasContact())) {
                ht = "采购合同已签订";
                if (history.getContractSn() != null) {
                    if (history.getContractSn().length() < 10) {
                        ht += "，合同编号" + history.getContractSn();
                    } else {
                        ht += "，合同编号" + history.getContractSn().substring(0, 10);
                    }
                }
                if (history.getValidDate() != null) {
                    ht += "，有效期至" + sdf.format(history.getValidDate());
                }
                if (history.getSignDate() != null) {
                    ht += "，签署日期为" + sdf.format(history.getSignDate());
                }
            } else {
                ht = "与该供应商暂无采购合同";
            }

            String cght = ""; // 本材料与采购合同的关系元素
            if (list.size() > 0) {
                if ("1".equals(history.getIsInclude())) {
                    PoContractBase poContractBase = poContractBaseDao.get(list.get(0).getContract());

                    if (poContractBase != null) {
                        cght = "本材料已包含于采购合同" + poContractBase.getSn() + "中";
                    } else {
                        cght = "本材料已包含于采购合同中";
                    }
                }
                if ("0".equals(history.getIsInclude())) {
                    cght = "本材料不包含于采购合同中";
                }
            }

            String bz = ""; // 包装方式元素

            if ("1".equals(history.getPackageMethod())) {
                bz = "该供应商供应的本材料包装方式基本固定";
            } else if ("2".equals(history.getPackageMethod())) {
                bz = "该供应商供应的本材料包装方式型式不定";
            } else {
                bz = "该供应商供应的本材料包装方式暂不清楚";
            }

            String jgwd = ""; // 价格稳定元素已约定的单价,

            if ("1".equals(history.getPriceStable())) {
                jgwd += "价格相对稳定";

            } else if ("2".equals(history.getPriceStable())) {
                jgwd += "价格变动较频繁";
            }

            String kpys = ""; // 开票元素
            if (history.getMaterialInvoicable() == 1) {
                if ("1".equals(history.getMaterialInvoiceCategory())) {
                    kpys += "给开税率为" + history.getTaxRate().stripTrailingZeros().toPlainString() + "%的增值税专用发票";
                } else if ("2".equals(history.getMaterialInvoiceCategory())) {
                    kpys += "给开其他发票";
                }
            } else if (history.getMaterialInvoicable() == 0) {
                kpys += "不给开发票";
            }

            String zdcg = "最低采购量为" + history.getMinimumPurchase(); // 最低采购元素
            String zdkc = "最低库存量为"; // 最低库存元素
            if (history.getMinimumStock() != null) {
                zdkc += history.getMinimumStock().stripTrailingZeros().toPlainString();
            } else {
                zdkc += 0;
            }

            String cgzq = "采购周期为" + history.getPerchaseCycle() + "天"; // 采购周期元素

            String price = chargePriceShow(history); // 价格元素

            map.put("price", price);
            map.put("cght", cght);
            map.put("bz", bz);
            map.put("zdcg", zdcg);
            map.put("cgzq", cgzq);
            map.put("jgwd", jgwd);
            map.put("kpys", kpys);
            map.put("ht", ht);
            map.put("zdkc", zdkc);
        }

        String kp = ""; // 开票能力元素
        int invoiceAble = 0; // 等于0 时 不给开发票
        int result = 0;

        List<MtSupplierMaterial> supplierMaterials =
                mtSupplierMaterialDao.getListByHQL(" from MtSupplierMaterial o where o.supplier = ?0", sh.getId());
        if (supplierMaterials != null && supplierMaterials.size() > 0) {
            StringBuilder sb = new StringBuilder("该供应商给开税率为");
            for (MtSupplierMaterial ms : supplierMaterials) {
                if (ms.getMaterialInvoicable() == 1) { // 可开票
                    if ("1".equals(ms.getMaterialInvoiceCategory())) { // 可以开增专
                        if (!sb.toString().contains(ms.getTaxRate() + ""))
                            if (ms != null && ms.getTaxRate() != null)
                                sb.append(ms.getTaxRate().setScale(2, RoundingMode.HALF_UP)).append("%").append(",");
                        invoiceAble += 1;
                    } else if ("2".equals(ms.getMaterialInvoiceCategory())) { // 可以开增普
                        invoiceAble += 1;

                        result += Integer.parseInt(ms.getMaterialInvoiceCategory());
                    }
                }
            }

            if (sb.toString().contains(","))
                kp = sb.substring(0, sb.lastIndexOf(",")) + "的增值税专用发票";
        }
        if (supplierMaterials != null && supplierMaterials.size() > 0)
            if (result / supplierMaterials.size() == 2) {
                kp = "该供应商仅给开普通发票";
            }

        if (invoiceAble == 0) {
            kp = "该供应商不给开发票";
        }

        // if("1".equals(sh.getInvoicable())){
        //
        // if("1".equals(sh.getVatsPayable())){
        // if(sh.getTaxRate()!=null){
        // kp = "该供应商能开税率为"+ sh.getTaxRate().stripTrailingZeros().toPlainString()
        // +"%的增值税专用发票";
        // }else{
        // kp = "该供应商能开增值税专用发票";
        // }
        // }else{
        // kp = "该供应商仅给开普通发票";
        // }
        // }else if ("2".equals(sh.getInvoicable())){
        // kp = "该供应商不确定能否开票";
        // }else{
        // kp = "该供应商不给开发票";
        // }

        String gz = ""; // 挂账元素

        if (sh.getChargeAcceptable() != null) {
            if (sh.getChargeAcceptable() == 1) {
                gz += "可接受挂账，";
                if (sh.getChargePeriod() != null) {
                    gz += "账期" + sh.getChargePeriod() + "天";
                }
                if ("1".equals(sh.getChargeBegin())) {
                    gz += "自货物入库之日起计算";
                } else if ("2".equals(sh.getChargeBegin())) {
                    gz += "自发票入账之日起计算";
                }
            } else {
                gz += "不接受挂账";
            }
        } else {
            gz += "不接受挂账";
        }

        // 更新 -- 该供应商供应的材料中 最少有一种接受 则为
        String hp = ""; // 承兑汇票元素

        // if (sh.getDraftAcceptable() != null) {
        // if (sh.getDraftAcceptable() == 1) {
        // hp += "可接受承兑汇票";
        // } else {
        // hp += "不确定能接受承兑汇票";
        // }
        //
        // } else {
        // hp += "不确定能接受承兑汇票";
        // }
        Integer state = draftState(sh.getId());
        if (state != null) {
            if (draftState(sh.getId()) == 1) {
                hp += "可接受承兑汇票";
            } else if (state == -1) {
                hp = "不接受承兑汇票";
            } else if (state == 0) {
                hp += "不接受承兑汇票";
            } else if (state == 2) {
                hp += "不确定能接受承兑汇票";
            }
        } else {
            hp += "不接受承兑汇票";
        }

        String yfk = ""; // 预付款元素
        if ("1".equals(sh.getIsImprest())) { // 0-不确定,1-需要,2-不需要
            yfk += "需要预付款";
        } else if ("2".equals(sh.getIsImprest())) {
            yfk += "无需预付款";
        } else if ("0".equals(sh.getIsImprest())) {
            yfk += "不确定是否需预付款";
        }

        Map<String, Object> supplier = new HashMap<>();
        supplier.put("name", sh.getName());
        supplier.put("codeName", sh.getCodeName());

        map.put("kp", kp);
        map.put("gz", gz);
        map.put("hp", hp);

        map.put("yfk", yfk);
        return map;
    }

    @Override
    public Map getLocationHisDetails(Integer hisId) {

        Map map = new HashMap();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        MtSupplierMaterialHistory history = mtSupplierMaterialHistoryDao.get(hisId);

        SrmSupplier sh = supplierDao.get(history.getSupplier());
        // 供应商信息
        String ht = ""; // 合同元素
        if ("1".equals(history.getHasContact())) {
            ht = "采购合同已签订";
            if (history.getContractSn() != null) {
                if (history.getContractSn().length() < 10) {
                    ht += "，合同编号" + history.getContractSn();
                } else {
                    ht += "，合同编号" + history.getContractSn().substring(0, 10);
                }
            }
            if (history.getValidDate() != null) {
                ht += "，有效期至" + sdf.format(history.getValidDate());
            }
            if (history.getSignDate() != null) {
                ht += "，签署日期为" + sdf.format(history.getSignDate());
            }
        } else {
            ht = "与该供应商暂无采购合同";
        }

        String cght = ""; // 本材料与采购合同的关系元素
        if ("1".equals(history.getIsInclude())) {
            cght = "本材料已包含于采购合同中";
        }
        if ("0".equals(history.getIsInclude())) {
            cght = "本材料不包含于采购合同中";
        }

        String bz = ""; // 包装方式元素

        if ("1".equals(history.getPackageMethod())) {
            bz = "该供应商供应的本材料包装方式基本固定";
        } else if ("2".equals(history.getPackageMethod())) {
            bz = "该供应商供应的本材料包装方式型式不定";
        } else {
            bz = "该供应商供应的本材料包装方式暂不清楚";
        }

        String kp = ""; // 开票能力元素
        if ("1".equals(sh.getInvoicable())) {

            if ("1".equals(sh.getVatsPayable())) {
                if (sh.getTaxRate() != null) {
                    kp = "该供应商能开税率为" + sh.getTaxRate().stripTrailingZeros().toPlainString() + "%的增值税专用发票";
                } else {
                    kp = "该供应商能开增值税专用发票";
                }
            } else {
                kp = "该供应商仅能开普通发票";
            }
        } else {
            kp = "该供应商不能开发票";
        }

        String gz = ""; // 挂账元素

        if (sh.getChargeAcceptable() != null) {
            if (sh.getChargeAcceptable() == 1) {
                gz += "可接受挂账，";
                if (sh.getChargePeriod() != null) {
                    gz += "账期" + sh.getChargePeriod() + "天";
                }
                if ("1".equals(sh.getChargeBegin())) {
                    gz += "自货物入库之日起计算";
                } else if ("2".equals(sh.getChargeBegin())) {
                    gz += "自发票入账之日起计算";
                }
            } else {
                gz += "不接受挂账";
            }
        } else {
            gz += "不接受挂账";
        }

        String hp = ""; // 承兑汇票元素

        if (sh.getDraftAcceptable() != null) {
            if (sh.getDraftAcceptable() == 1) {
                hp += "可接受承兑汇票";
            } else if (sh.getDraftAcceptable() == 0) {
                hp += "不接受承兑汇票";
            }

        } else {
            hp += "不确定能接受承兑汇票";
        }

        String jgwd = ""; // 价格稳定元素已约定的单价,

        if ("1".equals(history.getPriceStable())) {
            jgwd += "价格相对稳定";

        } else if ("2".equals(history.getPriceStable())) {
            jgwd += "价格变动较频繁";
        }

        String kpys = ""; // 开票元素
        if (history.getMaterialInvoicable() == 1) {
            if ("1".equals(history.getMaterialInvoiceCategory())) {
                kpys += "给开税率为" + history.getTaxRate().stripTrailingZeros().toPlainString() + "%的增值税专用发票";
            } else if ("2".equals(history.getMaterialInvoiceCategory())) {
                kpys += "给开其他发票";
            }
        } else if (history.getMaterialInvoicable() == 0) {
            kpys += "不给开发票";
        }
        String yfk = ""; // 预付款元素
        if ("1".equals(sh.getIsImprest())) { // 0-不确定,1-需要,2-不需要
            yfk += "需要预付款";
        } else if ("2".equals(sh.getIsImprest())) {
            yfk += "无需预付款";
        } else if ("0".equals(sh.getIsImprest())) {
            yfk += "不确定是否需预付款";
        }

        String zdcg = "最低采购量为" + history.getMinimumPurchase(); // 最低采购元素

        String zdkc = "最低库存量为"; // 最低库存元素
        if (history.getMinimumStock() != null) {
            zdkc += history.getMinimumStock().stripTrailingZeros().toPlainString();
        } else {
            zdkc += 0;
        }

        String cgzq = "采购周期为" + history.getPerchaseCycle() + "天"; // 采购周期元素

        String price = chargePriceShow(history); // 价格元素
        SrmSupplier mtSupplier = supplierDao.get(history.getSupplier());
        Map<String, Object> supplier = new HashMap<>();
        supplier.put("name", mtSupplier.getName());
        supplier.put("codeName", mtSupplier.getCodeName());

        MtBaseHistory mtBaseHistory = mtBaseHistoryDao.get(history.getMaterialHistory());

        // 寻找计量单位
        MtUnit mtUnit = mtUnitDao.get(mtBaseHistory.getUnitId());
        if (mtUnit != null) {
            mtBaseHistory.setUnit(mtUnit.getName());
        }

        List<SrmSupplier> list = supplierDao.getListByHQL(
                "from SrmSupplier s where s.id in (select su.supplier from MtSupplierMaterialHistory su where su.material !=0 and su.instance="
                        + history.getInstance() + " ) ");

        mtBaseHistory.setSupplierList(list);

        map.put("mtBase", mtBaseHistory);
        map.put("mtSupplier", supplier);
        map.put("name", mtBaseHistory.getName());
        map.put("codeName", mtBaseHistory.getCode());
        map.put("ht", ht);
        map.put("kp", kp);
        map.put("gz", gz);
        map.put("hp", hp);
        map.put("price", price);
        map.put("cght", cght);
        map.put("bz", bz);
        map.put("zdcg", zdcg);
        map.put("cgzq", cgzq);
        map.put("jgwd", jgwd);
        map.put("kpys", kpys);
        map.put("yfk", yfk);
        map.put("zdkc", zdkc);
        return map;
    }

    @Override
    public Map getLocationHisDetails(ApprovalProcess approvalProcess) {

        Map map = new HashMap();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        JSONObject obj = JSONObject.fromObject(approvalProcess.getApproveData());

        MtSupplierMaterial history =
                (MtSupplierMaterial) JSONObject.toBean(obj.getJSONObject("mtSupplierMaterial"), MtSupplierMaterial.class);
        SrmSupplier sh = (SrmSupplier) JSONObject.toBean(obj.getJSONObject("supplier"), SrmSupplier.class);
        // 供应商信息
        String ht = ""; // 合同元素
        if ("1".equals(history.getHasContact())) {
            ht = "采购合同已签订";
            if (history.getContractSn() != null) {
                if (history.getContractSn().length() < 10) {
                    ht += "，合同编号" + history.getContractSn();
                } else {
                    ht += "，合同编号" + history.getContractSn().substring(0, 10);
                }
            }
            if (history.getValidDate() != null) {
                ht += "，有效期至" + sdf.format(history.getValidDate());
            }
            if (history.getSignDate() != null) {
                ht += "，签署日期为" + sdf.format(history.getSignDate());
            }
        } else {
            ht = "与该供应商暂无采购合同";
        }

        String cght = ""; // 本材料与采购合同的关系元素
        if ("1".equals(history.getIsInclude())) {
            cght = "本材料已包含于采购合同中";
        }
        if ("0".equals(history.getIsInclude())) {
            cght = "本材料不包含于采购合同中";
        }

        String bz = ""; // 包装方式元素

        if ("1".equals(history.getPackageMethod())) {
            bz = "该供应商供应的本材料包装方式基本固定";
        } else if ("2".equals(history.getPackageMethod())) {
            bz = "该供应商供应的本材料包装方式型式不定";
        } else {
            bz = "该供应商供应的本材料包装方式暂不清楚";
        }

        String kp = ""; // 开票能力元素
        if ("1".equals(sh.getInvoicable())) {

            if ("1".equals(sh.getVatsPayable())) {
                if (sh.getTaxRate() != null) {
                    kp = "该供应商能开税率为" + sh.getTaxRate().stripTrailingZeros().toPlainString() + "%的增值税专用发票";
                } else {
                    kp = "该供应商能开增值税专用发票";
                }
            } else {
                kp = "该供应商仅能开普通发票";
            }
        } else {
            kp = "该供应商不能开发票";
        }

        String gz = ""; // 挂账元素

        if (sh.getChargeAcceptable() != null) {
            if (sh.getChargeAcceptable() == 1) {
                gz += "可接受挂账，";
                if (sh.getChargePeriod() != null) {
                    gz += "账期" + sh.getChargePeriod() + "天";
                }
                if ("1".equals(sh.getChargeBegin())) {
                    gz += "自货物入库之日起计算";
                } else if ("2".equals(sh.getChargeBegin())) {
                    gz += "自发票入账之日起计算";
                }
            } else {
                gz += "不接受挂账";
            }
        } else {
            gz += "不接受挂账";
        }

        String hp = ""; // 承兑汇票元素

        if (sh.getDraftAcceptable() != null) {
            if (sh.getDraftAcceptable() == 1) {
                hp += "可接受承兑汇票";
            } else {
                hp += "不确定能接受承兑汇票";
            }

        } else {
            hp += "不确定能接受承兑汇票";
        }

        String jgwd = ""; // 价格稳定元素已约定的单价,

        if ("1".equals(history.getPriceStable())) {
            jgwd += "价格相对稳定";

        } else if ("2".equals(history.getPriceStable())) {
            jgwd += "价格变动较频繁";
        }

        String kpys = ""; // 开票元素
        if (history.getMaterialInvoicable() == 1) {
            if ("1".equals(history.getMaterialInvoiceCategory())) {
                kpys += "给开税率为" + history.getTaxRate().stripTrailingZeros().toPlainString() + "%的增值税专用发票";
            } else if ("2".equals(history.getMaterialInvoiceCategory())) {
                kpys += "给开其他发票";
            }
        } else if (history.getMaterialInvoicable() == 0) {
            kpys += "不给开发票";
        }
        String yfk = ""; // 预付款元素
        if ("1".equals(sh.getIsImprest())) { // 0-不确定,1-需要,2-不需要
            yfk += "需要预付款";
        } else if ("2".equals(sh.getIsImprest())) {
            yfk += "无需预付款";
        } else if ("0".equals(sh.getIsImprest())) {
            yfk += "不确定是否需预付款";
        }

        String zdcg = "最低采购量为" + history.getMinimumPurchase(); // 最低采购元素
        String zdkc = "最低库存量为"; // 最低库存元素
        if (history.getMinimumStock() != null) {
            zdkc += history.getMinimumStock().stripTrailingZeros().toPlainString();
        } else {
            zdkc += 0;
        }
        String cgzq = "采购周期为" + history.getPerchaseCycle() + "天"; // 采购周期元素

        String price = chargePriceShow(history); // 价格元素

        Map<String, Object> supplier = new HashMap<>();
        supplier.put("name", sh.getName());
        supplier.put("codeName", sh.getCodeName());

        MtBaseHistory mtBaseHistory = (MtBaseHistory) JSONObject.toBean(obj.getJSONObject("base"), MtBaseHistory.class);

        if (mtBaseHistory.getUnitId() != null) {
            MtUnit mtUnit = mtUnitDao.get(mtBaseHistory.getUnitId());
            if (mtUnit != null) {
                mtBaseHistory.setUnit(mtUnit.getName());
            }
        }

        JSONArray supplierList = obj.getJSONArray("supplierList");

        List<SrmSupplier> s = new ArrayList<>();
        for (int i = 0; i < supplierList.size(); i++) {
            SrmSupplier ss = new SrmSupplier();
            ss.setName(supplierList.getJSONObject(i).optString("name"));
            ss.setCodeName(supplierList.getJSONObject(i).optString("codeName"));
            ss.setFullName(supplierList.getJSONObject(i).optString("fullName"));
            s.add(ss);
        }

        mtBaseHistory.setSupplierList(s);

        map.put("mtBase", mtBaseHistory);
        map.put("mtSupplier", supplier);
        map.put("name", mtBaseHistory.getName());
        map.put("codeName", mtBaseHistory.getCode());
        map.put("ht", ht);
        map.put("kp", kp);
        map.put("gz", gz);
        map.put("hp", hp);
        map.put("price", price);
        map.put("cght", cght);
        map.put("bz", bz);
        map.put("zdcg", zdcg);
        map.put("cgzq", cgzq);
        map.put("jgwd", jgwd);
        map.put("kpys", kpys);
        map.put("yfk", yfk);
        map.put("zdkc", zdkc);
        return map;
    }

    String chargePriceShow(MtSupplierMaterialHistory history) {

        // var ttl = "", info = supplier["isTax"] === "1"?"含税":"不含税";
        String ttl = "", info = "";
        String isStable = history.getPriceStable();
        String canInvoice = history.getMaterialInvoicable() + "";
        String incoiceTypeVal = history.getMaterialInvoiceCategory();

        if ("1".equals(isStable)) { // 稳定
            if ("1".equals(canInvoice)) { // 能开票
                ttl = "已约定的单价";
                info += "开票价";
            } else if ("0".equals(canInvoice)) { // 不能开票
                ttl = "已约定的单价";
                info += "不开票价";
            }
        } else if ("2".equals(isStable)) { // 变动频繁
            if ("1".equals(canInvoice)) { // 能开票
                if ("1".equals(incoiceTypeVal)) { // 能开增值税专用发票
                    ttl = "参考单价";
                    info += "开票价";
                } else if ("2".equals(incoiceTypeVal)) { // 开普通发票
                    ttl = "参考单价";
                    info += "开票价";
                }
            } else if ("0".equals(canInvoice)) { // 不能开票
                ttl = "参考单价";
                info += "不开票价";

            } else if ("2".equals(canInvoice)) { // 不确定
                ttl = "参考单价";
                info += "不开票价";
            }
        }

        String price = "";
        if (history != null) {
            BigDecimal upn = history.getUnitPriceNotax();
            if (upn != null) {
                price = upn.stripTrailingZeros().toPlainString();
            }
        }
        Integer isTax = history.getIsTax();
        if (history.getIsTax() != null && history.getIsTax() == 1) { // 含税
            price = history.getUnitPrice().stripTrailingZeros().toPlainString();
        }
        info += price + "元";
        String infoall = "";
        switch (history.getInclusiveFreight() + "") {
            case "1":
                infoall = "含运费的" + info;
                break;
            case "2":
                infoall = "含运费的" + info + "，但材料到本市后需我司自提";
                break;
            case "3":
                infoall = "不含运费的" + info;
                break;
            default:
                infoall = info;
        }
        return ttl + "," + infoall;
    }

    String chargePriceShow(MtSupplierMaterial history) {

        // var ttl = "", info = supplier["isTax"] === "1"?"含税":"不含税";
        String ttl = "", info = "";
        String isStable = history.getPriceStable();
        String canInvoice = history.getMaterialInvoicable() + "";
        String incoiceTypeVal = history.getMaterialInvoiceCategory();

        if ("1".equals(isStable)) { // 稳定
            if ("1".equals(canInvoice)) { // 能开票
                ttl = "已约定的单价";
                info += "开票价";
            } else if ("0".equals(canInvoice)) { // 不能开票
                ttl = "已约定的单价";
                info += "不开票价";
            }
        } else if ("2".equals(isStable)) { // 变动频繁
            if ("1".equals(canInvoice)) { // 能开票
                if ("1".equals(incoiceTypeVal)) { // 能开增值税专用发票
                    ttl = "参考单价";
                    info += "开票价";
                } else if ("2".equals(incoiceTypeVal)) { // 开普通发票
                    ttl = "参考单价";
                    info += "开票价";
                }
            } else if ("0".equals(canInvoice)) { // 不能开票
                ttl = "参考单价";
                info += "不开票价";

            } else if ("2".equals(canInvoice)) { // 不确定
                ttl = "参考单价";
                info += "不开票价";
            }
        }

        String price = history.getUnitPriceNotax() == null ? "0"
                : history.getUnitPriceNotax().stripTrailingZeros().toPlainString();
        Integer isTax = history.getIsTax();
        if (history.getIsTax() != null && history.getIsTax() == 1) { // 含税
            price = history.getUnitPrice().stripTrailingZeros().toPlainString();
        }
        info += price + "元";
        String infoall = "";
        switch (history.getInclusiveFreight() + "") {
            case "1":
                infoall = "含运费的" + info;
                break;
            case "2":
                infoall = "含运费的" + info + "，但材料到本市后需我司自提";
                break;
            case "3":
                infoall = "不含运费的" + info;
                break;
            default:
                infoall = info;
        }
        return ttl + "," + infoall;
    }

    @Override
    public Map getMaterielDetails(Integer hisId) {

        Map map = new HashMap();

        MtBaseHistory mtBaseHistory = mtBaseHistoryDao.get(hisId);
        // 寻找计量单位
        MtUnit mtUnit = mtUnitDao.get(mtBaseHistory.getUnitId());
        if (mtUnit != null) {
            mtBaseHistory.setUnit(mtUnit.getName());
        }

        List<SrmSupplier> list = supplierDao.getListByHQL(
                "from SrmSupplier s where s.id in (select su.supplier from MtSupplierMaterialHistory su where su.materialHistory="
                        + hisId + " ) ");

        mtBaseHistory.setSupplierList(list);

        map.put("mtBase", mtBaseHistory);
        return map;
    }

    @Override
    public Map<String, Object> getMtList(String state, String keyword, List<Integer> code, Integer currPage,
                                         Integer pageSize) {
        Object[] p = new Object[]{};
        StringBuilder str = new StringBuilder();
        for (Integer c : code) {
            if (str.length() == 0) {
                str.append(c);
            } else {
                str.append("," + c);
            }
        }

        String sql =
                "select m.id as id,m.code as code,m.name as name,m.specifications as specifications,m.model as model,m.unit as unit,m.unit_id as unitId,m.memo as memo,m.create_name as createName,m.create_date as createDate,m.update_name as updateName,m.update_date as updateDate,m.enabled as enabled,(select count(c.id) from t_pd_composition_material c where c.product is not null and c.material=m.id ) as num from t_mt_base m where m.category in ("
                        + str.toString() + ")  ";
        if ("1".equals(state)) {
            sql = sql + " and m.enabled='1'";
        } else {
            sql = sql + " and m.enabled='0'";
        }

        if (StringUtils.isNotBlank(keyword)) {

            sql += " and (m.name like '%" + keyword + "%' or m.code like '%" + keyword + "%')";
        }

        sql += " order by m.create_date desc";
        return mtBaseDao.findMapByConditionByPage(sql, null, p, currPage, pageSize);
    }

    @Override
    public Map<String, Object> getMtListForFormula(String state, String keyword, List<Integer> code, Integer currPage,
                                                   Integer pageSize) {
        Object[] p = new Object[]{};
        StringBuilder str = new StringBuilder();
        for (Integer c : code) {
            if (str.length() == 0) {
                str.append(c);
            } else {
                str.append("," + c);
            }
        }

        String sql =
                "select m.id as id,m.code as code,m.name as name,m.specifications as specifications,m.model as model,m.unit as unit,m.unit_id as unitId,m.memo as memo,m.create_name as createName,m.create_date as createDate,m.update_name as updateName,m.update_date as updateDate,m.enabled as enabled,m.origin,(select count(c.id) from t_pd_composition_material c where c.material=m.id and c.formula is not null ) as num,(SELECT count(c.id) FROM t_pd_composition_material_history c WHERE c.material = m.id AND c.formula IS NOT NULL) AS num2 from t_mt_base m where m.category in ("
                        + str.toString() + ")  ";
        if ("1".equals(state)) {
            sql = sql + " and m.enabled='1'";
        } else {
            sql = sql + " and m.enabled='0'";
        }

        if (StringUtils.isNotBlank(keyword)) {

            sql += " and (m.name like '%" + keyword + "%' or m.code like '%" + keyword + "%')";
        }

        sql += " HAVING !(origin = 5  AND num2 = 0) and !(origin = 4  AND num2 = 0)  order by m.create_date desc";
        return mtBaseDao.findMapByConditionByPage(sql, null, p, currPage, pageSize);
    }

    @Override
    public Map<String, Object> getStopMtBaseList(List<Integer> code, Integer currPage, Integer pageSize) {
        Object[] p = new Object[]{};
        StringBuilder str = new StringBuilder();
        for (Integer c : code) {
            if (str.length() == 0) {
                str.append(c);
            } else {
                str.append("," + c);
            }
        }

        String sql =
                "select m.id as id,m.code as code,m.name as name,m.specifications as specifications,m.model as model,m.unit as unit,m.memo as memo,m.create_name as createName,m.create_date as createDate,m.update_name as updateName,m.update_date as updateDate,m.enabled as enabled,(SELECT count(cm.id) FROM t_pd_composition_material_history cm LEFT JOIN t_pd_composition_material c on cm.composition_material=c.id  and c.material=cm.material WHERE c.id is  null and cm.material =m.id ) as num from t_mt_base m where m.category in ("
                        + str.toString() + ")  ";

        sql = sql + " and m.enabled='0'";

        sql += " order by m.create_date desc";
        return mtBaseDao.findMapByConditionByPage(sql, null, p, currPage, pageSize);
    }

    @Override
    public MtBase getStopMtBaseData(Integer id) {
        MtBase mtBase = mtBaseDao.get(id);

        if (mtBase != null) {

            String categoryName = getCategoryName("", mtBase.getCategory_());
            mtBase.setCategoryName(categoryName);
        }
        MtBase m = new MtBase();
        BeanUtils.copyProperties(mtBase, m);
        m.setCreateDate(mtBase.getCreateDate());
        m.setUpdateDate(mtBase.getUpdateDate());
        m.setCategory(null);
        return m;
    }

    private String getCategoryName(String name, Integer category) {
        if (category == null || category == 0) {
            return name;
        }
        MtCategory mtCategory = mtCategoryDao.get(category);
        if (mtCategory == null) {
            return name;
        }
        if ("".equals(name)) {
            name = mtCategory.getName();
        } else {
            name = mtCategory.getName() + ">" + name;
        }

        return getCategoryName(name, mtCategory.getParent_());
    }

    @Override
    public Map<String, Object> getStopMtBaseListForFormula(List<Integer> code, String keyword, Integer currPage,
                                                           Integer pageSize) {
        Object[] p = new Object[]{};
        StringBuilder str = new StringBuilder();
        for (Integer c : code) {
            if (str.length() == 0) {
                str.append(c);
            } else {
                str.append("," + c);
            }
        }

        String sql =
                "select m.id as id,m.code as code,m.name as name,m.specifications as specifications,m.model as model,m.unit as unit,m.memo as memo,m.create_name as createName,m.create_date as createDate,m.update_name as updateName,m.update_date as updateDate,m.enabled as enabled,(SELECT count(cm.id) FROM t_pd_composition_material_history cm LEFT JOIN t_pd_composition_material c on cm.composition_material=c.id and c.formula is not null and c.material=cm.material WHERE c.id is  null and cm.material =m.id ) as num,m.origin from t_mt_base m where m.category in ("
                        + str.toString() + ") ";

        sql = sql + " and m.enabled='0'";

        if (StringUtils.isNotBlank(keyword)) {

            sql += " and (m.name like '%" + keyword + "%' or m.code like '%" + keyword + "%')";
        }
        sql += "  HAVING !(origin = 5  AND num = 0) and !(origin = 4  AND num = 0)  order by m.create_date desc";
        return mtBaseDao.findMapByConditionByPage(sql, null, p, currPage, pageSize);
    }

    @Override
    public List<Map<String, Object>> getMtList(String state, List<Integer> code) {
        Object[] p = new Object[]{};
        StringBuilder str = new StringBuilder();
        for (Integer c : code) {
            if (str.length() == 0) {
                str.append(c);
            } else {
                str.append("," + c);
            }
        }

        String sql =
                "select m.id as id,m.code as code,m.name as name,m.specifications as specifications,m.model as model,m.unit as unit,m.unit_id as unitId,m.memo as memo,m.create_name as createName,m.create_date as createDate,m.enabled as enabled,(select count(c.id) from t_pd_composition_material c where c.material=m.id ) as num from t_mt_base m where m.category in ("
                        + str.toString() + ")  ";
        if ("1".equals(state)) {
            sql = sql + " and m.enabled='1'";
        } else {
            sql = sql + " and m.enabled='0'";
        }

        return mtBaseDao.getSession().createSQLQuery(sql).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list();
    }

    @Override
    public Map getIndex(Long org) {
        Map m = new HashMap();

        String sql1 =
                "SELECT COUNT(DISTINCT wb.id) as warehouse_count FROM InvWarehouseBase wb LEFT JOIN InvWarehouseCategory wc ON wb.category = wc.id where wb.org = ?0 AND wc.id = 3";
        String sql2 =
                "SELECT COUNT(DISTINCT wr.id) as region_count FROM InvWarehouseRegion wr LEFT JOIN InvWarehouseBase wb ON wr.warehouse = wb.id  LEFT JOIN InvWarehouseCategory wc ON wb.category = wc.id WHERE wr.org = ?0 and wc.id=3";
        String sql3 =
                "SELECT COUNT(DISTINCT ws.id) as shelf_count FROM InvWarehouseShelf ws LEFT JOIN InvWarehouseRegion wr ON ws.region = wr.id LEFT JOIN InvWarehouseBase wb ON wr.warehouse = wb.id  LEFT JOIN InvWarehouseCategory wc ON wb.category = wc.id WHERE wr.org = ?0 and wc.id=3 and IFNULL(ws.shelfModel,0)!=-1";
        String sql4 =
                "SELECT COUNT(DISTINCT wl.id) as location_count FROM InvWarehouseLocation wl LEFT JOIN InvWarehouseShelf ws ON wl.shelf = ws.id LEFT JOIN InvWarehouseRegion wr ON ws.region = wr.id LEFT JOIN InvWarehouseBase wb ON wr.warehouse = wb.id  LEFT JOIN InvWarehouseCategory wc ON wb.category = wc.id WHERE wr.org = ?0 and wc.id=3";
        String sql5 =
                "SELECT COUNT(DISTINCT location) as shelf_count FROM MtLocation ml LEFT JOIN MtBase mb on mb.id = ml.material where mb.org=?0";
        String sql6 =
                "SELECT DISTINCT mb.id AS material_count FROM t_mt_base mb LEFT JOIN t_mt_supplier_material msa ON msa.material = mb.id LEFT JOIN t_mt_location ml ON ml.supplier_material = msa.id LEFT JOIN t_mt_stock_info msi ON msi.material = mb.id LEFT JOIN t_mt_stock_info si ON si.material = mb.id WHERE mb.org = ?0 AND ((ifnull(mb.is_purchased, 0) = 0 AND (ifnull(mb.enabled, 0) != 0 AND ifnull(mb.is_appointed, 0) != 0)) OR ifnull( mb.operation, 0 ) in (7,8,9))";


        sql6 = """
                SELECT COUNT(DISTINCT ml.material) as count
                                   FROM t_iws_material_location ml
                                   LEFT JOIN t_iws_warehouse_location wl ON ml.location = wl.id
                                 
                                   LEFT JOIN t_iws_warehouse_base wbs ON wl.warehouse = wbs.id
                                   WHERE IFNULL(ml.material, 0) != 0
                                   AND wbs.org=?0
                                   AND wbs.warehouse_name = '原辅材料库'
                	""";
        String sql7 = """
                SELECT DISTINCT
                	mb.id AS material_count
                FROM
                	t_mt_base mb
                	LEFT JOIN t_mt_supplier_material msa ON msa.material = mb.id
                	LEFT JOIN t_mt_location ml ON ml.supplier_material = msa.id
                	LEFT JOIN t_mt_stock_info msi ON msi.material = mb.id
                WHERE
                	mb.org = ?0
                	AND ( msi.initial_stock IS NULL OR msi.initial_stock = 0 )
                
                	AND ifnull( mb.is_purchased, 0 )= 1
                	AND ( ifnull( mb.is_appointed, 0 )!= 0 )
                	AND ( ifnull( mb.version_no, 0 )>= 0 )
                	AND IFNULL( mb.is_initialized, 0 )= 0
                	AND (
                	ifnull( mb.enabled, 0 )!= 0)
                """;

        Long warehouse_count = (Long) locationDao.getDtoByHql(sql1, org);

        warehouse_count = warehouseBaseMapper.getWarehouseCount(org, "原辅材料库");

        Long region_count = (Long) locationDao.getDtoByHql(sql2, org);


        Long shelf_count = (Long) locationDao.getDtoByHql(sql3, org);
        shelf_count = warehouseShelfMapper.getShelfCount(org, "原辅材料库");

        Long location_count = (Long) locationDao.getDtoByHql(sql4, org);

        location_count = warehouseLocationMapper.getLocationCount(org, "原辅材料库");

        Long location_count_null = 0L;

        location_count_null = warehouseLocationMapper.getLocationNullCount(org, "原辅材料库");
//        if (org != null) {
//            if (locationDao.getDtoByHql(sql5, org.intValue()) != null) {
//                location_count_null = (Long)locationDao.getDtoByHql(sql5, org.intValue());
//            }
//        }

        Map maps = locationDao.findMapByConditionNoPage(sql6, new Object[]{org});

        List list = (List) maps.get("data");
        if(list!=null){
            HashMap mt = (HashMap) list.get(0);
            if (mt != null) {
                m.put("material_count", mt.get("count"));
            }else {
                m.put("material_count", 0);
            }

        }

        Map waitingFor = locationDao.findMapByConditionNoPage(sql7, new Object[]{org});

        List list1 = (List) waitingFor.get("data");

        String warehouseSql =
                "SELECT wb.warehouse_name, wb.id, wb.region_num, wb.shelf_num, wb.location_num , ( SELECT COUNT(DISTINCT ml.location) AS location_count_null FROM t_mt_location ml LEFT JOIN t_inv_warehouse_location wl ON ml.location = wl.id LEFT JOIN t_inv_warehouse_shelf ws ON wl.shelf = ws.id LEFT JOIN t_inv_warehouse_region wr ON ws.region = wr.id LEFT JOIN t_inv_warehouse_base wbh ON wr.warehouse = wbh.id LEFT JOIN t_inv_warehouse_category wc ON wbh.category = wc.id WHERE wc.id = 3 AND wbh.id = wb.id ) AS location_count_null , ( SELECT COUNT(ml.material) AS material_count FROM t_mt_location ml LEFT JOIN t_inv_warehouse_location wl ON ml.location = wl.id LEFT JOIN t_inv_warehouse_shelf ws ON wl.shelf = ws.id LEFT JOIN t_inv_warehouse_region wr ON ws.region = wr.id LEFT JOIN t_inv_warehouse_base wbs ON wr.warehouse = wbs.id LEFT JOIN t_inv_warehouse_category wc ON wbs.category = wc.id WHERE IFNULL(ml.material, 0) != 0 AND wbs.id = wb.id ) AS material_count FROM t_inv_warehouse_base wb LEFT JOIN t_inv_warehouse_category wc ON wb.category = wc.id WHERE wb.org = ?0 AND wc.id = 3 AND wb.id not IN (SELECT b.previous_id FROM t_inv_warehouse_base b WHERE IFNULL(b.previous_id,0)!=0)";

        warehouseSql = """
                SELECT
                    wb.warehouse_name,
                    wb.id,
                    COUNT(DISTINCT iwl.id) AS location_num,
                    (
                        SELECT SUM(amount)
                        FROM t_iws_warehouse_shelf
                        WHERE warehouse = wb.id
                    ) AS shelf_num,
                    (
                        SELECT COUNT(1)
                        FROM t_iws_warehouse_location iwl
                        LEFT JOIN t_iws_warehouse_base iwb ON iwl.warehouse = iwb.id
                        WHERE NOT EXISTS (
                            SELECT 1
                            FROM t_iws_material_location iml
                            WHERE iml.location = iwl.id
                        )
                        AND iwb.id = wb.id
                    ) AS location_count_null,
                    (
                        SELECT COUNT(DISTINCT ml.material)
                        FROM t_iws_material_location ml
                        LEFT JOIN t_iws_warehouse_location wl ON ml.location = wl.id
                        LEFT JOIN t_iws_warehouse_shelf ws ON wl.warehouse_shelf = ws.id
                        LEFT JOIN t_iws_warehouse_base wbs ON ws.warehouse = wbs.id
                        WHERE IFNULL(ml.material, 0) != 0
                        AND wbs.id = wb.id
                        AND wbs.warehouse_name = '原辅材料库'
                    ) AS material_count
                FROM
                    t_iws_warehouse_base wb
                LEFT JOIN t_iws_warehouse_location iwl ON iwl.warehouse = wb.id
                WHERE
                    wb.org = ?0
                    AND wb.warehouse_name = '原辅材料库'
                GROUP BY wb.id;
                """;
        m.put("warehouse_count", warehouse_count);
        m.put("region_count", region_count);
        m.put("shelf_count", shelf_count);
        m.put("location_count", location_count);
        m.put("location_count_null", location_count_null);


        m.put("waitingFor", list1 == null ? 0 : list1.size());

        Map map = locationDao.findMapByConditionNoPage(warehouseSql, new Object[]{org});

        m.put("warehouse", map.get("data"));
        return m;
    }

    @Override
    public Map location(Integer supplierMaterial, Integer mtID, String params, BigDecimal initialStock,
                        Integer operation, Integer re, User user) {

        Map map = new HashMap();
        map.put("code", 200);
        MtSupplierMaterial mtSupplierMaterial;
        MtBase mtBase = materielService.getMtBaseById(mtID);
        List<MtLocation> locations = locationDao.getListByHQL(" from MtLocation o where o.material=?0", mtID);
        mtBase.setOperation(operation + "");
        if (re != null) {
            if (locations != null && locations.size() > 0) {
                // 重新选库位，把原来的清掉
                locationDao.deleteAll(locations);

                mtBase.setLocationNumber(
                        mtBase.getLocationNumber() == 0 ? 0 : mtBase.getLocationNumber() - locations.size());
            }
        }
        mtBaseDao.saveOrUpdate(mtBase);

        switch (operation) {
            case 7: // 仅录入初始库存
                MtStockInfo info = stockService.getMtStockInfoByMId(mtID);

                BigDecimal s = info.getInitialStock() == null ? new BigDecimal(0) : info.getInitialStock();

                info.setInitialStock(s.add(initialStock));
                info.setCurrentStock(s.add(initialStock));
                info.setUpdator(user.getUserID());
                info.setUpdateName(user.getUserName());
                info.setUpdateDate(new Date());
                stockService.updateMtStockInfo(info);
                mtBase.setIsInitialized("1");
                mtBaseDao.saveOrUpdate(mtBase);
                this.saveStockRecord(mtBase.getOrg(), mtBase.getId(), user.getCreator(), user.getUserName(), null,
                        "modify_initial", initialStock, new BigDecimal(0), initialStock, null);
                break;

            case 8: // 录入初始库存总数，不区分是哪家供应商的
                if (StringUtils.isNotBlank(params)) {
                    JSONArray ins = JSONArray.fromObject(params);
                    if (ins.size() > 0) {
                        for (int i = 0; i < ins.size(); i++) {

                            JSONObject in = ins.getJSONObject(i);
                            MtLocation location = new MtLocation();
                            location.setCreateDate(new Date());
                            // 选择了库位，占用库位+1
                            if (in.opt("location") != null && in.getLong("location") > 0) {
                                location.setLocation(in.getLong("location"));
                                mtBase.setLocationNumber(
                                        mtBase.getLocationNumber() == null ? 1 : mtBase.getLocationNumber() + 1);
                                mtBaseDao.saveOrUpdate(mtBase);
                            }
                            location.setAmount(BigDecimal.valueOf(in.getDouble("amount")));
                            location.setUnit(in.optString("unit"));
                            location.setMaterial(in.optInt("material"));
                            location.setSupplierMaterial(in.optInt("supplierMaterial"));
                            location.setCreateName(user.getUserName());
                            location.setCreator(in.optInt("creator"));
                            location.setOperation(String.valueOf(operation));
                            if (re != null) {
                                location.setUpdateDate(new Date());
                                location.setUpdator(user.getUserID());
                                location.setUpdateName(user.getUserName());
                            }
                            locationDao.save(location);

                            InvWarehouseLocation warehouseLocation = warehouseLocationDao.get(in.getLong("location"));
                            warehouseLocation.setUtilizationMaterial(
                                    warehouseLocation.getUtilizationMaterial() == null ? in.optInt("material") + ""
                                            : warehouseLocation.getUtilizationMaterial() + "," + in.optInt("material"));

                            warehouseLocationDao.update(warehouseLocation);

                            if (in.optInt("supplierMaterial") != 0 && in.optInt("supplierMaterial") != -1) { // 区分供应商

                                mtSupplierMaterial = mtSupplierMaterialDao.getByHQL(
                                        " from MtSupplierMaterial o where o.id=?0", in.optInt("supplierMaterial"));

                                if (mtSupplierMaterial.getInitialStock() == null)
                                    mtSupplierMaterial.setInitialStock(new BigDecimal(0));

                                if (re == null)
                                    mtSupplierMaterial.setInitialStock(mtSupplierMaterial.getInitialStock()
                                            .add(new BigDecimal(in.optDouble("amount"))));

                                this.saveStockRecord(mtBase.getOrg(), mtSupplierMaterial.getMaterial_(),
                                        user.getUserID(), user.getUserName(), mtSupplierMaterial.getSupplier(),
                                        "modify_initial", initialStock, new BigDecimal(0), initialStock, null);
                                mtSupplierMaterialDao.update(mtSupplierMaterial);
                            }

                            if (re == null) {
                                MtStockInfo stockInfo = stockService.getMtStockInfoByMId(in.optInt("material"));

                                if (stockInfo == null) {
                                    stockInfo = new MtStockInfo();
                                    stockInfo.setCreator(in.optInt("creator"));
                                    stockInfo.setCreateName(user.getUserName());
                                    stockInfo.setMaterial(mtBaseDao.get(in.optInt("material")));
                                    stockInfoDao.save(stockInfo);
                                }

                                BigDecimal l = stockInfo.getInitialStock() == null ? new BigDecimal(0)
                                        : stockInfo.getInitialStock();

                                stockInfo.setInitialStock(l.add(new BigDecimal(in.optDouble("amount"))));

                                stockInfo.setCurrentStock(l.add(new BigDecimal(in.optDouble("amount"))));

                                stockInfo.setUpdator(user.getUserID());
                                stockInfo.setUpdateName(user.getUserName());
                                stockInfo.setUpdateDate(new Date());
                                stockService.updateMtStockInfo(stockInfo);
                            }

                            map.put("location", location);
                        }
                    }
                }
                break;

            case 9: // 放弃选择库位
                break;
        }

        return map;
    }

    @Override
    public Integer getNum(int i, Integer id) {
        String sql = "";

        switch (i) {
            // 查询m1
            case 1:
                sql =
                        "SELECT id FROM t_mt_base mb WHERE TO_DAYS(NOW())-TO_DAYS(mb.create_date)<=1 AND IFNULL(mb.is_purchased,0)=1 AND IFNULL(mb.is_appointed,0)=0 AND mb.org=?0";
                break;
            case 2: // 查询m2
                sql =
                        "SELECT id FROM t_mt_base mb WHERE TO_DAYS(NOW())-TO_DAYS(mb.create_date)<=1 AND IFNULL(mb.is_purchased,0)=0 AND IFNULL(mb.is_appointed,0)=0 AND mb.org=?0";
                break;
            case 3: // 查询m3
                sql =
                        "SELECT id FROM t_mt_base mb WHERE TO_DAYS(NOW())-TO_DAYS(mb.create_date)<=1 AND origin=3 AND mb.org=?0";
                break;
            case 4: // 查询m3
                sql =
                        "SELECT id FROM t_mt_base mb WHERE TO_DAYS(NOW())-TO_DAYS(mb.create_date)<=1 AND origin=4 AND mb.org=?0";
                break;
            case 5: // 查询m3
                sql =
                        "SELECT id FROM t_mt_base mb WHERE TO_DAYS(NOW())-TO_DAYS(mb.create_date)<=1 AND origin=5 AND mb.org=?0";
                break;
            case 6: // 查询m3
                sql =
                        "SELECT id FROM t_mt_base mb WHERE TO_DAYS(NOW())-TO_DAYS(mb.create_date)<=1 AND origin=6 AND mb.org=?0";
                break;
            case 7: // 查询m3
                sql =
                        "SELECT id FROM t_mt_base mb WHERE TO_DAYS(NOW())-TO_DAYS(mb.create_date)<=1 AND origin=7 AND mb.org=?0";
                break;
        }
        Map map = mtBaseDao.findMapByConditionNoPage(sql, new Object[]{id});
        List dataList = (List) map.get("data");
        if (dataList != null && dataList.size() > 0)
            return dataList.size();
        else
            return 0;
    }

    @Override
    public Map getHistory(Integer id) {

        return null;
    }

    @Override
    public Map getmtLocation(Integer id, Integer supplierMaterialId) {
        Map result = new HashMap();
        String sql =
                "SELECT iwl.id,iwl.location_code,ml.amount,ml.supplier_material,ms.`name`,ms.full_name,ms.code_name from t_mt_location ml LEFT JOIN t_mt_base mb ON ml.material = mb.id LEFT JOIN t_mt_supplier_material msm ON msm.id = ml.supplier_material LEFT JOIN t_inv_warehouse_location iwl ON ml.location = iwl.id LEFT JOIN t_srm_supplier ms ON msm.supplier = ms.id where mb.id=?0";

        if (supplierMaterialId != null)
            sql += " and msm.id = " + supplierMaterialId;

        String sql_count =
                "SELECT iwl.id, ml.supplier_material FROM t_mt_location ml LEFT JOIN t_mt_base mb ON ml.material = mb.id LEFT JOIN t_mt_supplier_material msm ON msm.id = ml.supplier_material LEFT JOIN t_inv_warehouse_location iwl ON ml.location = iwl.id LEFT JOIN t_mt_supplier ms ON msm.supplier = ms.id WHERE mb.id = ?0 GROUP BY ml.supplier_material";
        Map map = locationDao.findMapByConditionNoPage(sql, new Object[]{id});

        Map countMap = locationDao.findMapByConditionNoPage(sql_count, new Object[]{id});

        List<HashMap> dataList = (List) map.get("data");
        List<HashMap> countList = (List) countMap.get("data");

        if (dataList != null && dataList.size() > 0 && countList != null && countList.size() > 0) {
            for (HashMap m : countList) {
                Integer supplierMaterial = (Integer) m.get("supplier_material");
                List list = new ArrayList();

                for (HashMap n : dataList) {

                    if (n.get("supplier_material").equals(supplierMaterial)) {
                        list.add(n);
                    }

                    result.put(supplierMaterial, list);
                }
            }
        } else {
            result.put("code", "400");
        }

        return result;
    }

    @Override
    public String getSupplierState(Integer supplierId, Integer mtId) {

        String hql = " from MtSupplierMaterial o where 1=1 and o.supplier.id=?0 and o.material.id=?1";
        MtSupplierMaterial mtSupplierMaterial = mtSupplierMaterialDao.getByHQL(hql, supplierId, mtId);

        if (mtSupplierMaterial != null)
            return mtSupplierMaterial.getEnabled();
        else
            return "1";
    }

    @Override
    public Map addSuspendSupplier(MtBase base, String operation, Integer userId, String enabled) {
        Map map = new HashMap();

        User user = userDao.get(userId);

        JSONObject data = new JSONObject();
        data.put("userId", userId);
        data.put("operation", operation);
        data.put("enabled", base.getEnabled());
        data.put("product_", base.getProduct_());
        data.put("id", base.getId());

        ApprovalItem applyItem = roleService.getCurrentItem(user.getOid(), "purchaseApprovalSettings");

        // 判断是否需要审批
        if (applyItem == null || "0".equals(applyItem.getStatus())) {
            // 暂停供应商|恢复供应商
            if ("4".equals(operation) && base.getProduct_() != null) {
                Map res = this.operation(base, operation, userId);
                Integer code = (Integer) res.get("code");
                map.put("code", code);
                map.put("message", "success");

                return map;
            } else {
                base.setEnabled("0");
                base.setEnabledTime(new Date());
                base.setUpdateDate(new Date());
                base.setUpdateName(user.getUserName());
                base.setApproveStatus("2");
                mtBaseDao.update(base);
                map.put("code", "1");
                map.put("message", "success");
                return map;
            }

        } else {
            // 获取第一个审批人
            ApprovalFlow applyFlow = approvalService.getApprovalFlowByItemIdAndLevel(applyItem.getId(), 1);
            String s = "暂停";
            if ("1".equals(enabled)) {
                s = "恢复";
            }

            if (applyFlow == null) {
                map.put("code", "2");
                map.put("message", "没有审批人，无法修改");
                return map;
            }

            User flowUser = userService.getUserByID(applyFlow.getToUserId());
            // 暂停供应商
            if ("4".equals(operation) && base.getProduct_() != null) {

                MtBase b = mtBaseDao.get(base.getId());
                Integer mtBaseHis = mtBaseHistoryDao.insert2(b);
                MtSupplierMaterial supplierMaterial =
                        mtSupplierMaterialDao.getByHQL(" from MtSupplierMaterial o where o.id=" + base.getProduct_());
                if ("1".equals(supplierMaterial.getApproveStatus())) {
                    map.put("code", "2");
                    map.put("message", "有未完成的" + s + "审批");
                    return map;
                }

                SrmSupplier supplier = materielService.getSrmSupplierById(supplierMaterial.getSupplier());
                if (supplier == null) {
                    map.put("code", "2");
                    map.put("message", "该供应已被删除");
                    return map;
                }
                supplierMaterial.setOperation("5");
                MtSupplierMaterialHistory supplierMaterialHistory =
                        mtSupplierMaterialHistoryDao.saveHis(supplierMaterial, new MtSupplierMaterialHistory());
                ApprovalProcess ap = new ApprovalProcess();
                ap.setBusiness(supplierMaterialHistory.getId());
                ap.setLevel(1);
                ap.setDescription(s + "从" + materielService.getSrmSupplierById(supplierMaterial.getSupplier()).getName()
                        + "处购买" + b.getName());
                if ("0".equals(enabled)) {
                    ap.setBusinessType(34);
                } else {
                    ap.setBusinessType(62);
                }

                ap.setUserName(user.getUserName()); // 审批人
                ap.setCreateDate(new Date());
                ap.setToUser(applyFlow.getToUserId());
                ap.setToUserName(applyFlow.getUserName());
                ap.setOrg(user.getOid());
                ap.setFromUser(user.getUserID());
                ap.setFromOrg(user.getOid());
                ap.setUserName(user.getUserName());
                ap.setApproveStatus("1");
                ap.setApproveData(data.toString());
                ap.setApproveMemo(ap.getCreateDate().getTime() + "");
                approvalProcessDao.save(ap);

                supplierMaterial.setInstance(ap.getId());
                supplierMaterial.setApproveStatus("1");
                if (supplierMaterial.getInstanceChain() == null) {
                    supplierMaterial.setInstanceChain(ap.getId().toString());
                } else {
                    supplierMaterial.setInstanceChain(supplierMaterial.getInstanceChain() + "," + ap.getId());
                }
                supplierMaterialHistory.setInstance(supplierMaterial.getInstance());
                supplierMaterialHistory.setInstanceChain(supplierMaterial.getInstanceChain());
                supplierMaterialHistory.setMaterialHistory(mtBaseHis);
                mtSupplierMaterialHistoryDao.update(supplierMaterialHistory);

                // 当前供应商添加进历史
                // MtSupplierMaterial ms=mtSupplierMaterialDao.getByHQL(" from MtSupplierMaterial
                // o where o.supplier="+ supplierMaterial.getSupplier_()+" and o.material=0");
                // MtSupplierMaterialHistory msh=new MtSupplierMaterialHistory();
                // msh.setInstance(supplierMaterial.getInstance());
                // msh.setInstanceChain(supplierMaterial.getInstanceChain());
                // mtSupplierMaterialHistoryDao.saveHis(ms,msh);

                List<MtSupplierMaterial> list = mtSupplierMaterialDao
                        .getListByHQL(" from MtSupplierMaterial o where o.material=" + base.getId() + " and o.enabled='1'");
                for (MtSupplierMaterial m : list) {
                    if (!m.getId().equals(supplierMaterial.getId())) {
                        MtSupplierMaterialHistory mshh = new MtSupplierMaterialHistory();
                        mshh.setInstance(supplierMaterial.getInstance());
                        mshh.setInstanceChain(supplierMaterial.getInstanceChain());
                        mtSupplierMaterialHistoryDao.saveHis(m, mshh);
                    }
                }

                mtSupplierMaterialDao.update(supplierMaterial);
                HashMap<String, Object> hashMap = new HashMap<>();
                hashMap.put("ap", ap);
                hashMap.put("hisId", supplierMaterialHistory.getId());

                if ("0".equals(enabled)) {

                    // 暂停
                    // 申请人
                    swMessageService.rejectSend(0, 1, hashMap, user.getUserID().toString(), "/suspendMaterielApplyList",
                            null, null, user, "suspendMaterielApply");

                    swMessageService.rejectSend(1, 1, hashMap, flowUser.getUserID().toString(),
                            "/suspendMaterielApprovalList", "有一条申请待审批",
                            user.getUserName() + "在" + DateUtil.getTime() + "提交了暂停某供应商供应某物料的资格", flowUser,
                            "suspendMaterielApproval");

                } else {
                    // 恢复

                    // 申请人
                    swMessageService.rejectSend(0, 1, hashMap, user.getUserID().toString(),
                            "/recoveryMaterielApplyList", null, null, user, "recoveryMaterielApply");
                    // 审批人
                    swMessageService.rejectSend(1, 1, hashMap, flowUser.getUserID().toString(),
                            "/recoveryMaterielApprovalList", "有一条申请待审批",
                            user.getUserName() + "在" + DateUtil.getTime() + "提交了恢复某供应商供应某物料的资格", flowUser,
                            "recoveryMaterielApproval");
                }

            } else {
                // 暂停采购
                MtBase b = mtBaseDao.get(base.getId());
                if ("1".equals(b.getApproveStatus())) {
                    map.put("code", "2");
                    map.put("message", "有未完成的暂停审批");
                    return map;
                }

                b.setOperation("4");

                int hisId = mtBaseHistoryDao.insert(b);
                ApprovalProcess ap = new ApprovalProcess();
                ap.setBusiness(hisId);
                ap.setLevel(1);
                ap.setDescription("暂停采购" + b.getName());
                ap.setBusinessType(35);
                ap.setUserName(user.getUserName()); // 申请人
                ap.setCreateDate(new Date());
                ap.setToUser(applyFlow.getToUserId()); // 审批人
                ap.setToUserName(applyFlow.getUserName());
                ap.setOrg(user.getOid());
                ap.setFromUser(user.getUserID());
                ap.setFromOrg(user.getOid());
                ap.setApproveStatus("1");
                ap.setApproveData(data.toString());
                ap.setApproveMemo(ap.getCreateDate().getTime() + "");
                approvalProcessDao.save(ap);

                b.setInstance(ap.getId());
                b.setApproveStatus("1");
                if (b.getInstanceChain() == null) {
                    b.setInstanceChain(ap.getId().toString());
                } else {
                    b.setInstanceChain(b.getInstanceChain() + "," + ap.getId());
                }
                mtBaseDao.update(b);
                List<MtSupplierMaterial> list = mtSupplierMaterialDao
                        .getListByHQL(" from MtSupplierMaterial where material=" + b.getId() + " and enabled='1'");
                for (MtSupplierMaterial sm : list) {
                    MtSupplierMaterialHistory msh = new MtSupplierMaterialHistory();
                    msh.setMaterialHistory(hisId);
                    mtSupplierMaterialHistoryDao.saveHis(sm, msh);
                }

                HashMap<String, Object> hashMap = new HashMap<>();
                hashMap.put("ap", ap);
                hashMap.put("hisId", hisId);

                // 给申请人发送
                swMessageService.rejectSend(0, 1, hashMap, user.getUserID().toString(),
                        "/suspendPurchaseMaterielApplyList", null, null, user, "suspendPurchaseMaterielApply");
                // 给审批人发送
                swMessageService.rejectSend(1, 1, hashMap, applyFlow.getToUserId().toString(),
                        "/suspendPurchaseMaterielApprovalList", "有一条申请待审批",
                        user.getUserName() + "在" + DateUtil.getTime() + "提交了暂停采购" + b.getName() + "材料", flowUser,
                        "suspendPurchaseMaterielApproval");
            }
        }

        map.put("code", "1");
        map.put("message", "success");
        return map;
    }

    @Override
    public String suspendSupplierApproval(ApprovalProcess approvalProcess) {
        JSONObject jsonObject = JSONObject.fromObject(approvalProcess.getApproveData());
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("ap", approvalProcess);
        hashMap.put("status", 1);
        // 申请人
        User user = userService.getUserByID(approvalProcess.getFromUser());

        MtBase mtBase = new MtBase();
        mtBase.setId(jsonObject.optInt("id"));
        mtBase.setProduct_(jsonObject.optInt("product_"));
        mtBase.setEnabled("0");
        String operation = jsonObject.getString("operation");
        Integer updaetUserId = jsonObject.optInt("userId");

        // 当前审批人
        User thisUser = userService.getUserByID(approvalProcess.getToUser());
        // 2批准 3驳回
        if ("2".equals(approvalProcess.getApproveStatus())) {

            // 判断是否有下级审批
            // 查下下一级审批人
            ApprovalItem applyItem = roleService.getCurrentItem(user.getOid(), "purchaseApprovalSettings");
            // 获取下一个审批人
            ApprovalFlow applyFlow =
                    approvalService.getApprovalFlowByItemIdAndLevel(applyItem.getId(), approvalProcess.getLevel() + 1);

            // 直接通过
            if (applyItem == null || "0".equals(applyItem.getStatus()) || applyFlow == null) {
                Map res = this.operation(mtBase, operation, updaetUserId);
                Integer code = (Integer) res.get("code");
                if (code != 1) {
                    // 如果修改失败，按处理驳回处置
                    MtSupplierMaterial supplierMaterial =
                            mtSupplierMaterialDao.getByHQL("from MtSupplierMaterial where id=" + mtBase.getProduct_());
                    supplierMaterial.setApproveStatus("3");
                    mtSupplierMaterialDao.update(supplierMaterial);

                    String msg = (String) res.get("message");
                    approvalProcess.setApproveStatus("3");
                    approvalProcess.setReason(msg);

                    String s = approvalProcess.getDescription() + "的申请被驳回了!";

                    suspendMsgService.saveUserSuspendMsg(1, s,
                            "操作时间 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                            approvalProcess.getFromUser(), "suspendSupplier", approvalProcess.getId());

                } else {
                    MtSupplierMaterial supplierMaterial =
                            mtSupplierMaterialDao.getByHQL("from MtSupplierMaterial where id=" + mtBase.getProduct_());
                    SrmSupplier supplier = supplierDao.get(supplierMaterial.getSupplier());
                    if (supplier != null) {
                        supplier.setCutCount(supplier.getCutCount() + 1);
                        // supplier.setSupplyCount(supplier.getSupplyCount()-1);
                    }
                    String s = approvalProcess.getDescription() + "的申请被批准了!";
                    suspendMsgService.saveUserSuspendMsg(1, s,
                            "操作时间 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                            approvalProcess.getFromUser(), "suspendSupplier", approvalProcess.getId());
                }
                approvalProcessDao.update(approvalProcess);
                // 减去当前申请人待处理
                swMessageService.rejectSend(-1, -1, hashMap, thisUser.getUserID().toString(),
                        "/suspendMaterielApprovalList", null, null, thisUser, "suspendMaterielApproval");
                // 给申请人发送
                swMessageService.rejectSend(0, -1, hashMap, user.getUserID().toString(), "/suspendMaterielApplyList",
                        null, null, user, "suspendMaterielApproval");
                return "success";
            }
            // 下一个审批人
            User flowUser = userService.getUserByID(applyFlow.getToUserId());

            // 传给下一个审批人
            ApprovalProcess approvalProcess1 = new ApprovalProcess();
            approvalProcess1.setLevel(approvalProcess.getLevel() + 1);
            approvalProcess1.setApproveStatus("1");
            approvalProcess1.setToUser(applyFlow.getToUserId());
            approvalProcess1.setToUserName(applyFlow.getUserName()); // 审批人总称
            approvalProcess1.setFromOrg(approvalProcess.getFromOrg());
            approvalProcess1.setFromUser(approvalProcess.getFromUser());
            approvalProcess1.setUserName(approvalProcess.getUserName()); // 审批人名称
            approvalProcess1.setCreateDate(new Date());
            approvalProcess1.setBusiness(approvalProcess.getBusiness());
            approvalProcess1.setBusinessType(approvalProcess.getBusinessType());

            approvalProcess1.setNewId(approvalProcess.getBusiness());
            approvalProcess1.setFromUser(user.getUserID());
            approvalProcess1.setAskName(user.getUserName());
            approvalProcess1.setDescription(approvalProcess.getDescription());
            approvalProcess1.setMessage(false);
            approvalProcess1.setOrg(user.getOid());
            approvalProcess1.setApproveData(approvalProcess.getApproveData());
            approvalProcess1.setApproveMemo(approvalProcess.getApproveMemo());
            approvalProcessDao.save(approvalProcess1);

            approvalProcessDao.update(approvalProcess);

            // 减去当前申请人待处理
            swMessageService.rejectSend(-1, -1, hashMap, thisUser.getUserID().toString(),
                    "/suspendMaterielApprovalList", null, null, thisUser, "suspendMaterielApproval");
            // 给当前处理人已处理发送
            swMessageService.rejectSend(0, 1, hashMap, thisUser.getUserID().toString(),
                    "/suspendMaterielApprovalAlreadyList", null, null, thisUser, "suspendMaterielApproval");
            // 下级审批人待处理发送
            HashMap<String, Object> hashMap1 = new HashMap<>();
            hashMap1.put("ap", approvalProcess1);
            hashMap1.put("status", 1);
            swMessageService.rejectSend(1, 1, hashMap1, flowUser.getUserID().toString(), "/suspendMaterielApprovalList",
                    "有一条申请待审批", user.getUserName() + "在" + DateUtil.getTime() + "提交了暂停某供应商供应某物料的资格", flowUser,
                    "suspendMaterielApproval");

        } else {
            MtSupplierMaterial supplierMaterial =
                    mtSupplierMaterialDao.getByHQL("from MtSupplierMaterial where id=" + mtBase.getProduct_());
            supplierMaterial.setApproveStatus("3");
            mtSupplierMaterialDao.update(supplierMaterial);

            String s = approvalProcess.getDescription() + "的申请被驳回了!";
            suspendMsgService.saveUserSuspendMsg(1, s,
                    "操作时间 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), approvalProcess.getFromUser(),
                    "suspendSupplier", approvalProcess.getId());
            approvalProcessDao.update(approvalProcess);
            // 把所有审批通过的驳回
            List<ApprovalProcess> approvalProcesses = approvalProcessService
                    .getApprovalProcessByBusiness(approvalProcess.getBusiness(), approvalProcess.getBusinessType(), "2");
            for (ApprovalProcess a : approvalProcesses) { // 最终审批结束，将所有审批人的已批准数据减掉
                if (approvalProcess.getApproveMemo().equals(a.getApproveMemo())) {
                    a.setApproveStatus("3");
                    a.setReason(approvalProcess.getReason());
                    approvalProcessDao.update(a);
                    HashMap<String, Object> hp = new HashMap<>();
                    hp.put("ap", a);
                    hp.put("status", 1);
                    User approvalUser = userDao.get(a.getToUser()); // 推送人
                    swMessageService.rejectSend(0, -1, hp, a.getToUser().toString(),
                            "/suspendPurchaseMaterielApprovalAlreadyList", null, null, approvalUser,
                            "suspendPurchaseMaterielApproval");
                }
            }
            // 减去当前申请人待处理
            swMessageService.rejectSend(-1, -1, hashMap, thisUser.getUserID().toString(),
                    "/suspendMaterielApprovalList", null, null, thisUser, "suspendMaterielApproval");
            // 给申请人发送
            swMessageService.rejectSend(0, -1, hashMap, user.getUserID().toString(), "/suspendMaterielApplyList", null,
                    null, user, "suspendMaterielApproval");
        }

        return "success";
    }

    @Override
    public String suspendMaterielApproval(ApprovalProcess approvalProcess) {

        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("ap", approvalProcess);
        hashMap.put("status", 1);
        User user = userService.getUserByID(approvalProcess.getFromUser());

        MtBaseHistory mtBaseHistory = mtBaseHistoryDao.get(approvalProcess.getBusiness());

        // 当前审批人
        User thisUser = userService.getUserByID(approvalProcess.getToUser());
        MtBase mtBase = mtBaseDao.get(mtBaseHistory.getMaterial());
        // 2批准 3驳回
        if ("2".equals(approvalProcess.getApproveStatus())) {

            // 判断是否有下级审批
            // 查下下一级审批人
            ApprovalItem applyItem = roleService.getCurrentItem(user.getOid(), "purchaseApprovalSettings");
            // 获取下一个审批人
            ApprovalFlow applyFlow =
                    approvalService.getApprovalFlowByItemIdAndLevel(applyItem.getId(), approvalProcess.getLevel() + 1);

            // 直接通过
            if (applyItem == null || "0".equals(applyItem.getStatus()) || applyFlow == null) {
                mtBase.setEnabled("0");
                mtBase.setEnabledTime(new Date());
                mtBase.setUpdateDate(new Date());
                mtBase.setUpdateName(user.getUserName());
                mtBase.setApproveStatus("2");
                mtBaseDao.update(mtBase);
                String s = approvalProcess.getDescription() + "的申请被批准了!";

                suspendMsgService.saveUserSuspendMsg(1, s,
                        "操作时间 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                        approvalProcess.getFromUser(), "suspendMateriel", approvalProcess.getId());

                approvalProcessDao.update(approvalProcess);
                // 给申请人发消息
                swMessageService.rejectSend(0, -1, hashMap, user.getUserID().toString(),
                        "/suspendPurchaseMaterielApplyList", null, null, user, "suspendPurchaseMaterielApply");
                // 给审批人发消息
                swMessageService.rejectSend(-1, -1, hashMap, thisUser.getUserID().toString(),
                        "/suspendPurchaseMaterielApprovalList", null, null, thisUser, "suspendPurchaseMaterielApproval");

                return "success";
            }
            // 下一个审批人
            User flowUser = userService.getUserByID(applyFlow.getToUserId());

            // 传给下一个审批人
            ApprovalProcess approvalProcess1 = new ApprovalProcess();
            approvalProcess1.setLevel(approvalProcess.getLevel() + 1);
            approvalProcess1.setApproveStatus("1");
            approvalProcess1.setToUser(applyFlow.getToUserId());
            approvalProcess1.setToUserName(applyFlow.getUserName()); // 审批人总称
            approvalProcess1.setFromOrg(approvalProcess.getFromOrg());
            approvalProcess1.setFromUser(approvalProcess.getFromUser());
            approvalProcess1.setUserName(approvalProcess.getUserName()); // 审批人名称
            approvalProcess1.setCreateDate(new Date());
            approvalProcess1.setBusiness(approvalProcess.getBusiness());
            approvalProcess1.setBusinessType(approvalProcess.getBusinessType());

            approvalProcess1.setNewId(approvalProcess.getBusiness());
            approvalProcess1.setFromUser(user.getUserID());
            approvalProcess1.setAskName(user.getUserName());
            approvalProcess1.setDescription(approvalProcess.getDescription());
            approvalProcess1.setMessage(false);
            approvalProcess1.setOrg(user.getOid());
            approvalProcess1.setApproveData(approvalProcess.getApproveData());
            approvalProcess1.setApproveMemo(approvalProcess.getApproveMemo());
            approvalProcessDao.save(approvalProcess1);

            approvalProcessDao.update(approvalProcess);

            // 减去当前申请人待处理
            swMessageService.rejectSend(-1, -1, hashMap, thisUser.getUserID().toString(),
                    "/suspendPurchaseMaterielApprovalList", null, null, thisUser, "suspendPurchaseMaterielApproval");
            // 给当前处理人已处理发送
            swMessageService.rejectSend(0, 1, hashMap, thisUser.getUserID().toString(),
                    "/suspendPurchaseMaterielApprovalAlreadyList", null, null, thisUser, "suspendPurchaseMaterielApproval");
            // 下级审批人待处理发送
            HashMap<String, Object> hashMap1 = new HashMap<>();
            hashMap1.put("ap", approvalProcess1);
            hashMap1.put("status", 1);
            swMessageService.rejectSend(1, 1, hashMap1, flowUser.getUserID().toString(),
                    "/suspendPurchaseMaterielApprovalList", "有一条申请待审批",
                    user.getUserName() + "在" + DateUtil.getTime() + "提交了暂停采购" + mtBase.getName() + "材料", flowUser,
                    "suspendPurchaseMaterielApproval");

        } else {
            mtBase.setApproveStatus("3");
            mtBaseDao.update(mtBase);

            String s = approvalProcess.getDescription() + "的申请被驳回了!";

            suspendMsgService.saveUserSuspendMsg(1, s,
                    "操作时间 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), approvalProcess.getFromUser(),
                    "suspendMateriel", approvalProcess.getId());

            approvalProcessDao.update(approvalProcess);
            // 把所有审批通过的驳回
            List<ApprovalProcess> approvalProcesses = approvalProcessService
                    .getApprovalProcessByBusiness(approvalProcess.getBusiness(), approvalProcess.getBusinessType(), "2");
            for (ApprovalProcess a : approvalProcesses) { // 最终审批结束，将所有审批人的已批准数据减掉
                if (approvalProcess.getApproveMemo().equals(a.getApproveMemo())) {
                    a.setApproveStatus("3");
                    a.setReason(approvalProcess.getReason());
                    approvalProcessDao.update(a);
                    HashMap<String, Object> hp = new HashMap<>();
                    hp.put("ap", a);
                    hp.put("status", 1);
                    User approvalUser = userDao.get(a.getToUser()); // 推送人
                    swMessageService.rejectSend(0, -1, hp, a.getToUser().toString(),
                            "/suspendPurchaseMaterielApprovalAlreadyList", null, null, approvalUser,
                            "suspendPurchaseMaterielApproval");
                }
            }
            // 给申请人发消息
            swMessageService.rejectSend(0, -1, hashMap, user.getUserID().toString(),
                    "/suspendPurchaseMaterielApplyList", null, null, user, "suspendPurchaseMaterielApply");
            // 给审批人发消息
            swMessageService.rejectSend(-1, -1, hashMap, thisUser.getUserID().toString(),
                    "/suspendPurchaseMaterielApprovalList", null, null, thisUser, "suspendPurchaseMaterielApproval");
        }

        return "success";
    }

    @Override
    public String recoveryMaterielApproval(ApprovalProcess approvalProcess) {
        JSONObject jsonObject = JSONObject.fromObject(approvalProcess.getApproveData());
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("ap", approvalProcess);
        hashMap.put("status", 1);
        User user = userService.getUserByID(approvalProcess.getFromUser());
        User thisUser = userService.getUserByID(approvalProcess.getToUser());
        MtBase mtBase = new MtBase();
        mtBase.setId(jsonObject.optInt("id"));
        mtBase.setProduct_(jsonObject.optInt("product_"));
        mtBase.setEnabled("1");
        String operation = jsonObject.getString("operation");
        Integer updaetUserId = jsonObject.optInt("userId");
        // 2批准 3驳回
        if ("2".equals(approvalProcess.getApproveStatus())) {

            // 判断是否有下级审批
            // 查下下一级审批人
            ApprovalItem applyItem = roleService.getCurrentItem(user.getOid(), "purchaseApprovalSettings");
            // 获取下一个审批人
            ApprovalFlow applyFlow =
                    approvalService.getApprovalFlowByItemIdAndLevel(applyItem.getId(), approvalProcess.getLevel() + 1);
            // 直接通过
            if (applyItem == null || "0".equals(applyItem.getStatus()) || applyFlow == null) {
                Map res = this.operation(mtBase, operation, updaetUserId);
                Integer code = (Integer) res.get("code");
                if (code != 1) {
                    // 如果修改失败，按处理驳回处置
                    MtSupplierMaterial supplierMaterial =
                            mtSupplierMaterialDao.getByHQL("from MtSupplierMaterial where id=" + mtBase.getProduct_());
                    supplierMaterial.setApproveStatus("3");
                    mtSupplierMaterialDao.update(supplierMaterial);

                    String msg = (String) res.get("message");
                    approvalProcess.setApproveStatus("3");
                    approvalProcess.setReason(msg);

                    String s = approvalProcess.getDescription() + "的申请被驳回了!";

                    suspendMsgService.saveUserSuspendMsg(1, s,
                            "操作时间 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                            approvalProcess.getFromUser(), "suspendSupplier", approvalProcess.getId());

                } else {
                    MtSupplierMaterial supplierMaterial =
                            mtSupplierMaterialDao.getByHQL("from MtSupplierMaterial where id=" + mtBase.getProduct_());
                    SrmSupplier supplier = supplierDao.get(supplierMaterial.getSupplier());
                    if (supplier != null) {
                        supplier.setCutCount(supplier.getCutCount() - 1);
                        // supplier.setSupplyCount(supplier.getSupplyCount()-1);
                    }

                    String s = approvalProcess.getDescription() + "的申请被批准了!";
                    suspendMsgService.saveUserSuspendMsg(1, s,
                            "操作时间 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()),
                            approvalProcess.getFromUser(), "suspendSupplier", approvalProcess.getId());
                }
                approvalProcessDao.update(approvalProcess);
                swMessageService.rejectSend(0, -1, hashMap, user.getUserID().toString(), "/recoveryMaterielApplyList",
                        null, null, user, "recoveryMaterielApply");
                swMessageService.rejectSend(-1, -1, hashMap, thisUser.getUserID().toString(),
                        "/recoveryMaterielApprovalList", null, null, thisUser, "recoveryMaterielApproval");

                return "success";
            }

            // 下一个审批人
            User flowUser = userService.getUserByID(applyFlow.getToUserId());

            // 传给下一个审批人
            ApprovalProcess approvalProcess1 = new ApprovalProcess();
            approvalProcess1.setLevel(approvalProcess.getLevel() + 1);
            approvalProcess1.setApproveStatus("1");
            approvalProcess1.setToUser(applyFlow.getToUserId());
            approvalProcess1.setToUserName(applyFlow.getUserName()); // 审批人总称
            approvalProcess1.setFromOrg(approvalProcess.getFromOrg());
            approvalProcess1.setFromUser(approvalProcess.getFromUser());
            approvalProcess1.setUserName(approvalProcess.getUserName()); // 审批人名称
            approvalProcess1.setCreateDate(new Date());
            approvalProcess1.setBusiness(approvalProcess.getBusiness());
            approvalProcess1.setBusinessType(approvalProcess.getBusinessType());

            approvalProcess1.setNewId(approvalProcess.getBusiness());
            approvalProcess1.setFromUser(user.getUserID());
            approvalProcess1.setAskName(user.getUserName());
            approvalProcess1.setDescription(approvalProcess.getDescription());
            approvalProcess1.setMessage(false);
            approvalProcess1.setOrg(user.getOid());
            approvalProcess1.setApproveData(approvalProcess.getApproveData());
            approvalProcess1.setApproveMemo(approvalProcess.getApproveMemo());
            approvalProcessDao.save(approvalProcess1);

            approvalProcessDao.update(approvalProcess);

            // 减去当前申请人待处理
            swMessageService.rejectSend(-1, -1, hashMap, thisUser.getUserID().toString(),
                    "/recoveryMaterielApprovalList", null, null, thisUser, "recoveryMaterielApproval");
            // 给当前处理人已处理发送
            swMessageService.rejectSend(0, 1, hashMap, thisUser.getUserID().toString(),
                    "/recoveryMaterielApprovalAlreadyList", null, null, thisUser, "recoveryMaterielApproval");
            // 下级审批人待处理发送
            HashMap<String, Object> hashMap1 = new HashMap<>();
            hashMap1.put("ap", approvalProcess1);
            hashMap1.put("status", 1);
            swMessageService.rejectSend(1, 1, hashMap1, flowUser.getUserID().toString(),
                    "/recoveryMaterielApprovalList", "有一条申请待审批",
                    user.getUserName() + "在" + DateUtil.getTime() + "提交了恢复某供应商供应某物料的资格", flowUser,
                    "recoveryMaterielApproval");

        } else {
            MtSupplierMaterial supplierMaterial =
                    mtSupplierMaterialDao.getByHQL("from MtSupplierMaterial where id=" + mtBase.getProduct_());
            supplierMaterial.setApproveStatus("3");
            mtSupplierMaterialDao.update(supplierMaterial);

            String s = approvalProcess.getDescription() + "的申请被驳回了!";
            suspendMsgService.saveUserSuspendMsg(1, s,
                    "操作时间 " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), approvalProcess.getFromUser(),
                    "suspendSupplier", approvalProcess.getId());
            approvalProcessDao.update(approvalProcess);

            // 把所有审批通过的驳回
            List<ApprovalProcess> approvalProcesses = approvalProcessService
                    .getApprovalProcessByBusiness(approvalProcess.getBusiness(), approvalProcess.getBusinessType(), "2");
            for (ApprovalProcess a : approvalProcesses) { // 最终审批结束，将所有审批人的已批准数据减掉
                if (approvalProcess.getApproveMemo().equals(a.getApproveMemo())) {
                    a.setApproveStatus("3");
                    a.setReason(approvalProcess.getReason());
                    approvalProcessDao.update(a);
                    HashMap<String, Object> hp = new HashMap<>();
                    hp.put("ap", a);
                    hp.put("status", 1);
                    User approvalUser = userDao.get(a.getToUser()); // 推送人
                    swMessageService.rejectSend(0, -1, hp, a.getToUser().toString(),
                            "/recoveryMaterielApprovalAlreadyList", null, null, approvalUser, "recoveryMaterielApproval");
                }
            }
            swMessageService.rejectSend(0, -1, hashMap, user.getUserID().toString(), "/recoveryMaterielApplyList", null,
                    null, user, "recoveryMaterielApply");
            swMessageService.rejectSend(-1, -1, hashMap, thisUser.getUserID().toString(),
                    "/recoveryMaterielApprovalList", null, null, thisUser, "recoveryMaterielApproval");
        }

        return "success";
    }

    @Override
    public Integer getBadgeNumber(User user, String code) {
        Integer number = null;
        switch (code) {
            case "suspendMaterielApply":
                number = 0;
                break;
            case "suspendPurchaseMaterielApply":
                number = 0;
                break;
            case "recoveryMaterielApply":
                number = 0;
                break;
            case "addMaterielApply":
                number = 0;
                break;
            case "suspendMaterielApproval":
                String hql =
                        "select count(id)  from ApprovalProcess where approveStatus=:approveStatus and toUser = :toUser and businessType=:businessType";
                HashMap<String, Object> param = new HashMap<>();
                param.put("approveStatus", "1");
                param.put("businessType", 34);
                param.put("toUser", user.getUserID());
                Long num = (Long) approvalProcessDao.getByHQLWithNamedParams(hql, param);
                number = num.intValue();
                break;
            case "suspendPurchaseMaterielApproval":
                String hql2 =
                        "select count(id)  from ApprovalProcess where approveStatus=:approveStatus and toUser = :toUser and businessType=:businessType";
                HashMap<String, Object> param2 = new HashMap<>();
                param2.put("approveStatus", "1");
                param2.put("businessType", 35);
                param2.put("toUser", user.getUserID());
                Long num2 = (Long) approvalProcessDao.getByHQLWithNamedParams(hql2, param2);
                number = num2.intValue();
                break;
            case "recoveryMaterielApproval":
                String hql3 =
                        "select count(id)  from ApprovalProcess where approveStatus=:approveStatus and toUser = :toUser and businessType=:businessType";
                HashMap<String, Object> param3 = new HashMap<>();
                param3.put("approveStatus", "1");
                param3.put("businessType", 62);
                param3.put("toUser", user.getUserID());
                Long num3 = (Long) approvalProcessDao.getByHQLWithNamedParams(hql3, param3);
                number = num3.intValue();
                break;
            case "addMaterielApproval":
                String hql4 =
                        "select count(id)  from ApprovalProcess where approveStatus=:approveStatus and toUser = :toUser and businessType=:businessType";
                HashMap<String, Object> param4 = new HashMap<>();
                param4.put("approveStatus", "1");
                param4.put("businessType", 61);
                param4.put("toUser", user.getUserID());
                Long num4 = (Long) approvalProcessDao.getByHQLWithNamedParams(hql4, param4);
                number = num4.intValue();
                break;
        }
        return number;
    }

    @Override
    public void saveStockRecord(Integer org, Integer material, Integer creator, String createName, Integer supplier,
                                String event, BigDecimal quantity, BigDecimal oldStock, BigDecimal endStock, Integer business) {
        MtStockDetail stockDetail = new MtStockDetail();
        stockDetail.setOrg(org);
        stockDetail.setMaterial(material);
        stockDetail.setSupplier(supplier);
        stockDetail.setEvent(event);
        stockDetail.setEventTime(new Date());
        if (endStock != null) {
            stockDetail.setQuantity(endStock.subtract(oldStock));
        }
        stockDetail.setEndStock(endStock);
        stockDetail.setBusiness(business);
        stockDetail.setCreateDate(new Date());
        stockDetail.setCreator(creator);
        stockDetail.setCreateName(createName);

        stockDetailDao.save(stockDetail);
    }

    @Override
    public MtBase getMtById(Integer id) {
        return mtBaseDao.get(id);
    }

    @Override
    public Map getMtListByDate(Date date, User user) {
        String sql =
                "select * from t_mt_base where create_date>?0 and org=?1 and enabled=1 and ifnull(is_initialized,-1) not in (1)";
        return mtBaseDao.findMapByConditionNoPage(sql, new Object[]{date, user.getOid()});
    }

    @Override
    public Map getMtListByCondition(Date date, String operation, Integer oid) {
        Date d = Calendar.getInstance().getTime();

        // return mtBaseDao.getListByHQL(" from MtBase where operation=?0 and oid=?1 and
        // update_date<?3", operation, oid, date);

        return mtBaseDao.findMapByConditionNoPage(
                "select * from t_mt_base where operation=?0 and org=?1 and update_date>?2 and ifnull(is_initialized,-1) not in (1)",
                new Object[]{operation, oid, date});
    }

    @Override
    public void updateMaBase(MtBase mtBase) {
        mtBaseDao.update(mtBase);
    }

    @Override
    public List<MtBaseHistory> getMtBaseHistoryById(Integer id, String operation) {
        return mtBaseHistoryDao.getListByHQL(" from MtBaseHistory o where o.material=?0 and o.operation=?1", id,
                operation);
    }

    @Override
    public Map getMtListByWarehouse(Integer oid, Long previousId) {
        String sql =
                "SELECT DISTINCT ml.update_date as ml_update_date,sum(ml.amount) as re_amount,si.create_date as si_create_date, si.create_name as si_create_name, si.update_date as si_update_date, si.update_name as si_update_name, mb.update_name AS mb_update_name, mb.update_date AS mb_update_date, msa.update_name, msa.enabled_time, msa.id AS supplier_material_id, msa.material_invoiceable, msa.unit_price, msa.unit_price_notax, msa.minimum_purchase, msa.perchase_cycle, mc.id AS category_id, pmc.NAME AS p_name, mb.id, mb.`name`, mb.is_purchased, mb.is_appointed, mb.`code`, mb.specifications, mb.model, mb.create_name, mb.memo, mb.create_date, mu.id AS unit_id, mu.NAME AS unit, mb.location_number, mb.supplier_number, ifnull(si.current_stock, 0) AS current_stock, (SELECT SUM(mm.minimum_stock) FROM t_mt_supplier_material mm WHERE mm.material = mb.id AND ifnull(mm.enabled, 0) != 0) AS minimum_stock, si.initial_stock, msa.price_stable, msa.is_par_value, msa.at_par, msa.inclusive_freight, msa.material_invoice_category, msa.material_tax_rate, msa.is_tax, msa.tax_rate, (SELECT sum(poi.transit_quantity) FROM t_po_orders_item poi WHERE poi.supplier_material = msa.id) AS way_num FROM t_mt_base mb LEFT JOIN t_mt_stock_info si ON si.material = mb.id LEFT JOIN t_mt_category mc ON mb.category = mc.id LEFT JOIN t_pd_base pb ON pb.id = mb.product INNER JOIN t_mt_category pmc ON mc.first_grade_id = pmc.id LEFT JOIN t_mt_supplier_material msa ON msa.material = mb.id LEFT JOIN t_srm_supplier ms ON msa.supplier = ms.id LEFT JOIN t_mt_location ml ON ml.material = mb.id LEFT JOIN t_inv_warehouse_location iwl ON ml.location = iwl.id left join t_inv_warehouse_shelf iws on iwl.shelf = iws.id left join t_inv_warehouse_region iwr on iws.region = iwr.id left join t_inv_warehouse_base iwb on iwr.warehouse = iwb.id LEFT JOIN t_mt_unit mu ON mb.unit_id = mu.id LEFT JOIN t_po_orders_item poi ON poi.supplier_material = msa.id WHERE mc.org = ?0 and si.initial_stock is not null and ifnull(mb.is_purchased, 0) = 1 and (ifnull(mb.is_appointed, 0) != 0) and ifnull(mb.enabled, 0) != 0 and pmc.name not in ('商品') and ifnull(mb.source, 0) != 0 ";

        sql += " and iwb.id = ?1";

        sql += " GROUP BY mb.id";

        return mtBaseDao.findMapByConditionNoPage(sql, new Object[]{oid, previousId});
    }

    @Override
    public void setStockZero(String ids, User user) {
        for (String id : ids.split(",")) {
            int i = Integer.parseInt(id);
            MtBase mtBase = mtBaseDao.get(i);
            if (mtBase != null) {
                MtStockInfo mtStockInfo = stockInfoDao.getByHQL(" from MtStockInfo o where o.material_=?0", i);
                if (mtStockInfo != null) {
                    mtStockInfo.setInitialStock(new BigDecimal(0));
                    mtStockInfo.setUpdateDate(new Date());
                    mtStockInfo.setUpdateName(user.getUserName());
                    mtStockInfo.setUpdator(user.getUserID());
                    stockInfoDao.update(mtStockInfo);
                }
                mtBase.setIsInitialized("1");
                mtBaseDao.update(mtBase);
            }
        }
    }

    @Override
    public void deleteMaterialLocation(Integer mtId) {

        TIwsMaterialLocation materialLocation = new TIwsMaterialLocation();
        materialLocation.setMaterial(mtId);
        materialLocation.setOperation(3);
        List<TIwsMaterialLocation> materialLocations = mtLocationMapper.selectTIwsMaterialLocationList(materialLocation);
        if (materialLocations != null) {
            for (TIwsMaterialLocation tIwsMaterialLocation : materialLocations) {
                mtLocationMapper.deleteTIwsMaterialLocationById(tIwsMaterialLocation.getId());
            }
        }

    }

    @Override
    public void initialStock22(User user, Integer mtId, BigDecimal initialStock, Integer dripSupplier,
                               Integer dripPackaging) {


        MtBase mtBase = mtBaseDao.get(mtId);
        if (mtBase != null) {
            mtBase.setDripSupplier(dripSupplier); // 是否区分供应商
            mtBase.setDripPackaging(dripPackaging); // 是否区分包装
            mtBaseDao.update(mtBase);
        }

        MtStockInfo mtStockInfo = stockInfoDao.getByHQL(" from MtStockInfo o where o.material_=?0", mtId);
        if (mtStockInfo != null) {
            mtStockInfo.setInitialStock(initialStock);
            mtStockInfo.setCurrentStock(initialStock);
            mtStockInfo.setUpdateDate(new Date());
            mtStockInfo.setUpdateName(user.getUserName());
            mtStockInfo.setUpdator(user.getUserID());
            stockInfoDao.update(mtStockInfo);
        }
    }

    @Override
    public void initialStock12(User user, List<TIwsMaterialLocation> materialLocations, Integer dripSupplier,
                               Integer dripPackaging) {
        if (materialLocations == null || materialLocations.isEmpty())
            return;

        BigDecimal initialStock = new BigDecimal(0);

        for (TIwsMaterialLocation materialLocation : materialLocations) {
            if (materialLocation.getId() != null) {
                TIwsMaterialLocation materialLocation1 = mtLocationMapper.selectTIwsMaterialLocationById(materialLocation.getId());


                materialLocation1.setUpdateName(user.getUserName());
                materialLocation1.setUpdator(user.getUserID());
                materialLocation1.setUpdateTime(new Date());

                BeanUtils.copyProperties(materialLocation1, materialLocation, "material", "supplierMaterial", "amount", "packaging", "packagingCount");
            }

            materialLocation.setOrg(user.getOid());
            materialLocation.setCreateTime(new Date());
            materialLocation.setCreateName(user.getUserName());
            materialLocation.setCreator(user.getUserID());
            materialLocation.setAccountQantity(0);
            materialLocation.setIsInitial("1");
            materialLocation.setOperation(3);
            if (materialLocation.getId() == null) {
                mtLocationMapper.insertTIwsMaterialLocation(materialLocation);

            } else {
                mtLocationMapper.updateTIwsMaterialLocation(materialLocation);
            }
            // 更新供应商的初始库存
            if (materialLocation.getSupplierMaterial() != null && materialLocation.getSupplierMaterial() != 0) {
                MtSupplierMaterial supplierMaterial = mtSupplierMaterialDao.getById(materialLocation.getSupplierMaterial());

                if (supplierMaterial != null) {
                    supplierMaterial.setInitialStock(materialLocation.getAmount());
                    mtSupplierMaterialDao.update(supplierMaterial);
                }
            }

            initialStock = initialStock.add(materialLocation.getAmount());
        }

        this.initialStock22(user, materialLocations.get(0).getMaterial(), initialStock, dripSupplier, dripPackaging);
    }

    @Override
    public void initialStock21(User user, List<TIwsMaterialLocation> materialLocations, Integer dripSupplier,
                               Integer dripPackaging) {
        this.initialStock12(user, materialLocations, dripSupplier, dripPackaging);
    }

    @Override
    public List<Map<String, Object>> getStocksDetailByMtId(Integer material) {
        MtBase base = mtBaseDao.get(material);

        List<Map<String, Object>> data = mtLocationMapper.getStocksDetailByMtId(material);
        return data;
    }

    @Override
    public List<Map<String, Object>> getStocksDetailByMtSupplierMaterial(TIwsMaterialLocation tIwsMaterialLocation) {

        List<Map<String, Object>> data =
                mtLocationMapper.getStocksDetailByMtIdAndSupplierMaterial(tIwsMaterialLocation);
        return data;
    }
}
