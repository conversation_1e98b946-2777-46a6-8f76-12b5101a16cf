<template>
<!--  文件发布申请详情-->
  <div id="issueFile" v-loading="loading">
    <TY_NavTop title="文件发布申请"></TY_NavTop>
    <div class="container">
      <div class="place">
        <div class="placeContainer">
          <p class="fileTtl" v-text="fileDetails.name"></p>
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          <div class="line-p">
            <div class="tt-cell">文件编号</div>
            <div class="con-cell" v-text="fileDetails.fileSn"></div>
          </div>
          <div class="line-p">
            <div class="tt-cell">保存位置</div>
            <div class="con-cell" v-html="filePosition"></div>
          </div>
          <div class="line-p">
            <div class="tt-cell">文件类型</div>
            <div class="con-cell" v-text="fileDetails.version"></div>
          </div>
          <div class="line-p">
            <div class="tt-cell">文件大小</div>
            <div class="con-cell">{{fileDetails.size | formatByte}}</div>
          </div>
          <div class="line-p" v-if="fileDetails.changeNum">
            <div class="tt-cell">版本号</div>
            <div class="con-cell">G{{fileDetails.changeNum}}</div>
          </div>
          <div class="line-p" v-if="!fileDetails.changeNum">
            <div class="tt-cell">说明</div>
            <div class="con-cell" v-text="fileDetails.content"></div>
          </div>
          <div class="line-p" v-if="fileDetails.changeNum">
            <div class="tt-cell">换版原因</div>
            <div class="con-cell" v-text="fileDetails.content"></div>
          </div>
          <div class="line-p">
            <div class="tt-cell">内容</div>
            <div class="con-cell"><span class="fc-btn-see" @click="showDialog(fileDetails.id)">查看</span></div>
          </div>
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          <div class="record">
            <span>审批记录</span>
            <div class="script">
              <div v-for="(item ,index) in approveRecord" :key="index">
                <div v-if="index === 0">
                  <span class="sm-ttl">申请人：</span>
                  <span class="sm-con" v-text="item.userName"></span>
                  <span>{{item.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                </div>
                <div v-if="index != 0">
                  <span class="sm-ttl">审批人：</span>
                  <span class="sm-con" v-text="item.userName"></span>
                  <span>{{item.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                </div>
                <div v-if="index === approveRecord.length-1">
                <span>等待
                  <span class="font-orange" v-if="item.toUserName">{{item.toUserName}}</span>
                  <span class="font-orange" v-else>文管</span>
                  审批
                </span>
                </div>
              </div>
            </div>
          </div>
          <div class="handle_button handle-center" v-if="isNotCharged">
            <button class="ui-btn ui-btn_success" :disabled="stopControl" @click="stopApply">终止发布申请</button>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      title="待审批的文件"
      :visible.sync="pendingDialog"
      width="70%">
      <el-form class="preview">
        <el-form-item>
          <el-radio-group v-model="browser">
            <el-radio :disabled="seeOnlineDisabled" :label="1">在线预览</el-radio>
            <el-radio :label="2">下载</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button handle-center">
          <a class="fc-btn-normal" @click="pendingDialog = false">取消</a>
          <span class="fc-btn ui-btn_info" ref="operate" :path="fileDetails.path" :download="fileDetails.name + '.' + fileDetails.version" @click="oprationFile($event)">确定</span>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { canSeeOnline } from '@/js/common'
export default {
  name: 'fileDetailSee',
  data () {
    return {
      loading: true,
      isNotCharged: true,
      dialogVisible: false,
      pendingDialog: false,
      tipVisible: false,
      categoryName: '',
      fileDetails: {},
      approveRecord: [],
      listenersUid: [],
      browser: 1,
      seeOnlineDisabled: true
    }
  },
  computed: {
    stopControl () {
      let len = this.approveRecord.length - 1
      if (len > 0) {
        if (this.approveRecord[len].approveStatus === '3' || this.approveRecord[len].approveStatus === '5' || (this.approveRecord[len].approveStatus === '2' && this.approveRecord[len].toMid === 'rb')) {
          return true
        }
      }
      return false
    },
    bytesToSize () {
      let sizeStr = ''
      let fileSize = this.fileDetails.size
      sizeStr = fileSize < 102400 ? parseFloat(fileSize / 1024).toFixed(2) + 'KB' : parseFloat(fileSize / 1048576).toFixed(2) + 'MB'
      return sizeStr
    },
    filePosition () {
      let pos = this.categoryName
      let posArr = pos.split('/')
      let posStr = ''
      for (let a = 0; a < posArr.length; a++) {
        if (a === posArr.length - 1) {
          posStr += '<span class="font-orange">' + posArr[a] + '</span>'
        } else {
          posStr += posArr[a] + '/'
        }
      }
      return posStr
    }
  },
  created: function () {
    var _this = this
    var fileId = this.$route.params.id
    this.sphdSocket.send('onePublishFileMessage', { 'session': _this.sphdSocket.sessionid, 'hisId': fileId, 'userID': _this.sphdSocket.user.userID })
    this.listenersUid = [
      this.sphdSocket.subscribe('onePublishFileMessage', function (data) {
        var fileData = JSON.parse(data)
        _this.categoryName = fileData.categoryName
        _this.fileDetails = fileData.resHis
        _this.approveRecord = fileData.listAp
        let listAp = fileData.listAp
        let lastSta = listAp[listAp.length - 1]['approveStatus']
        if (lastSta === '2' || lastSta === '3') {
          _this.isNotCharged = false
        }
        _this.loading = false
      }, null, 'session', this.sphdSocket.sessionid),
      this.sphdSocket.subscribe('fileUpdateState', function (data) {
        _this.loading = false
        let val = JSON.parse(data)
        if (val === 2) {
          _this.$message({
            type: 'success',
            message: '操作成功！'
          })
          _this.refreshFile()
        } else {
          _this.$alert('本文件已被审批', '！提示', {
            confirmButtonText: '确定'
          });
          _this.isNotCharged = false
        }
      }, null, 'custom', fileId)
    ]
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    stopFileProcess () {
      this.loading = true
      var userId = this.sphdSocket.user.userID
      var fileId = this.$route.params.id
      var processLength = this.approveRecord.length * 1 - 1
      var processId = this.approveRecord[processLength].id
      this.sphdSocket.send('stopFileProcess', { 'userID': userId, 'hisId': fileId, 'processId': processId })
    },
    oprationFile (event) {
      let el = event.currentTarget
      window.parent.previewOrDownload(el, this.browser)
    },
    screen: function () {
      this.$router.push({
        path: `/applyFileQuery`
      })
    },
    refreshFile: function () {
      this.$router.push({
        path: `/releaseApply`
      })
    },
    showDialog: function () {
      this.seeOnlineDisabled = !canSeeOnline(this.fileDetails.version)
      this.pendingDialog = true
      if (this.seeOnlineDisabled) {
        this.browser = 2
      } else {
        this.browser = 1
      }
    },
    stopApply: function () {
      let that = this
      this.$confirm('确定终止本次发布申请？', '！提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.stopFileProcess()
      }).catch(() => {})
    }
  }
}
</script>
