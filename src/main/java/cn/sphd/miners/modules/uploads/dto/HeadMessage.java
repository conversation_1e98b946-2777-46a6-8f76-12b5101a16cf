package cn.sphd.miners.modules.uploads.dto;

import org.apache.http.client.methods.HttpRequestBase;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by 朱思旭 on 2019/11/11.
 */
public class HeadMessage {

    private String token;
    private String Accept;
    private HashMap<String, String> headers = new HashMap<>();

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getAccept() {
        return Accept;
    }

    public void setAccept(String accept) {
        this.Accept = accept;
    }

    public void addHeader(String name,String value) {
        headers.put(name, value);
    }

    public void setHeader(HttpRequestBase request) {
        if(getToken() != null){
            request.addHeader("Authorization", getToken());
        }
        if(getAccept() != null){
            request.addHeader("Accept", getAccept());
        }
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            String name = entry.getKey();
            String value = entry.getValue();
            request.addHeader(name,value);
        }
    }
}
