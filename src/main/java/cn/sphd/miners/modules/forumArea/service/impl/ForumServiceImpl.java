package cn.sphd.miners.modules.forumArea.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.service.BaseServiceImpl;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.TimeUtils;
import cn.sphd.miners.modules.dailyAffairs.entity.UserSuspendMsg;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.forumArea.dao.*;
import cn.sphd.miners.modules.forumArea.dto.PostUserMussage;
import cn.sphd.miners.modules.forumArea.entity.*;
import cn.sphd.miners.modules.forumArea.service.ForumService;
import cn.sphd.miners.modules.personal.dao.ApprovalProcessDao;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.personal.service.UserMessageService;
import cn.sphd.miners.modules.resourceAuthority.entity.ResEntity;
import cn.sphd.miners.modules.resourceAuthority.entity.ResHistory;
import cn.sphd.miners.modules.resourceAuthority.service.ResService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.dto.UserHonePageDto;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import cn.sphd.miners.modules.uploads.service.UploadService;
import com.alibaba.fastjson.JSON;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Created by 朱思旭 on 2018/2/2.
 */
@Service("forumService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED)
public class ForumServiceImpl extends BaseServiceImpl implements ForumService {

    @Autowired
    ForumPostDao forumPostDao;
    @Autowired
    ForumPostAttachmentDao forumPostAttachmentDao;
    @Autowired
    ForumPostUserDao forumPostUserDao;
    @Autowired
    UserDao userDao;
    @Autowired
    UserService userService;
    @Autowired
    ForumReplyDao forumReplyDao;
    @Autowired
    ForumReplyAttachmentDao forumReplyAttachmentDao;
    @Autowired
    ForumReplyUserDao forumReplyUserDao;
    @Autowired
    ForumUserStatDao forumUserStatDao;
    @Autowired
    UserMessageService userMessageService;
    @Autowired
    ForumReplyUserStatDao forumReplyUserStatDao;
    @Autowired
    ForumReplyAttHisDao forumReplyAttHisDao;
    @Autowired
    ForumPostHistoryDao forumPostHistoryDao;
    @Autowired
    ForumPostUserHistoryDao forumPostUserHistoryDao;
    @Autowired
    ResourceForumDao resourceForumDao;
    @Autowired
    ApprovalProcessDao approvalProcessDao;


    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    SWMessageService swMessageService;
    @Autowired
    UploadService uploadService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    ResService resService;

    @Override
    public void insertForumStatus(ForumPost forumPost, User user) {
        forumPost.setOrg(user.getOid());
        forumPost.setCreator(user.getUserID());
        forumPost.setCreateName(user.getUserName());
        forumPost.setCreateDate(new Date());
        forumPost.setUpdateDate(forumPost.getCreateDate());
        forumPost.setValid(1);
        forumPost.setEnabled(1);
        forumPost.setIsOpen(0);
        forumPost.setIsCleaned(1);
        forumPost.setIsArchived(0);
        if(forumPost.getCategory() == null){
            forumPost.setCategory(2);
            forumPost.setApproveStatus("1");
            forumPost.setCompere(user.getUserID());
            forumPost.setCompereName(user.getUserName());
        }else {
            forumPost.setCategory(1);
            forumPost.setApproveStatus("2");
        }
        forumPostDao.save(forumPost);
    }

    @Override
    public void insertForumattachment(ForumPostAttachment forumPostAttachment, User user) {
        if(forumPostAttachment != null){
            if (forumPostAttachment.getResourceId() != null) {
                Integer resHistoryId = resService.getNewFileHisId(forumPostAttachment.getResourceId());
                forumPostAttachment.setResourceHistory(resHistoryId);
            }
            forumPostAttachmentDao.save(forumPostAttachment);
            if (forumPostAttachment.getResourceId() == null) {
                ForumUsing callback = new ForumUsing(forumPostAttachment.getId(), forumPostAttachment.getClass());
                uploadService.addFileUsing(callback,forumPostAttachment.getPath(),forumPostAttachment.getTitle(),user,forumPostAttachment.getModule());
            }
        }
    }

    @Override
    public void insertFotrumUser(ForumPostUser forumPostUser) {
        if (forumPostUser != null) {
            forumPostUserDao.save(forumPostUser);
        }
    }

    @Override
    public ForumPost forumPostMessageByhead(Integer id) {
        String hql = "from ForumPost where id = :id";
        HashMap<String, Object> param1 = new HashMap<>();
        param1.put("id",id);
        ForumPost forumPost = (ForumPost) forumPostDao.getByHQLWithNamedParams(hql,param1);
        if (forumPost.getValid()==0 && forumPost.getIsCleaned()==1) {
            Date date = new Date();
            forumPost.setExpriationTime("将于"+ TimeUtils.toTimeString(forumPost.getCleanTime().getTime()-date.getTime(),3) +"钟后消失");
        }
        return forumPost;
    }

    @Override
    public HashMap forumPostMessage(Integer id, Integer userID) {
        ForumPost forumPost = this.forumPostMessageByhead(id);
        HashMap map = this.forumReplyMessage(id, userID, null,"1",null,null);
        //HashMap<String, Object> map = new HashMap<>();
        map.put("forumPost", forumPost);
        //map.put("forumPostAttachmentHashSer", forumPost.getForumPostAttachmentHashSer());
        //map.put("forumPostUserHashSer",forumPost.getForumPostUserHashSer());
        //map.put("listReplyPage", map2.get("listReplyPage"));
        //map.put("unreadNum", map2.get("unreadNum"));
        //  map.put("pageInfo", map2.get("pageInfo"));

        return map;
    }

    @Override
    public HashMap forumReplyMessage(Integer postId, Integer userID, Integer replyId, String type, String unreadNum, Integer parentReplyId){
        PageInfo pageInfo = new PageInfo();
        HashMap<String, Object> map = new HashMap<>();
        //获取全部的回帖
        String hqlReply = "from ForumReply where post = :post ";
        HashMap<String, Object> paramReply = new HashMap<>();
        paramReply.put("post", postId);
        List<ForumReply> listParentReply = null;
        if (type.equals("1") || type.equals("3")) {
            if (replyId != null && !"".equals(replyId)) {
                hqlReply = hqlReply + " and id < :id";
                paramReply.put("id", replyId);
                if (unreadNum != null && !"".equals(unreadNum)) {
                    Integer unreadNumber = Integer.valueOf(unreadNum);
                    if (unreadNumber.compareTo(200)>0) {
                        pageInfo.setPageSize(200);
                    }else {
                        pageInfo.setPageSize(unreadNumber);
                    }
                } else {
                    pageInfo.setPageSize(20);
                }
            } else {
                List<ForumReplyUserStat> listReplyUserstate = this.listReplyUserStat(postId, userID);
                map.put("unreadNum", listReplyUserstate.size());
                pageInfo.setPageSize(20);
            }
            hqlReply = hqlReply + " order by createDate desc";
            listParentReply = forumReplyDao.getListByHQLWithNamedParams(hqlReply, paramReply, pageInfo);
        } else{
            hqlReply = hqlReply + " and id < :id and id >= :parentId order by createDate desc";
            paramReply.put("id", replyId);
            paramReply.put("parentId", parentReplyId);
            listParentReply = forumReplyDao.getListByHQLWithNamedParams(hqlReply, paramReply);
        }
        //获取每条人员详情和删除按钮是否显示以及附件情况
        if (!listParentReply.isEmpty()) {
            for (ForumReply fp : listParentReply) {
                this.fillInReplyNumAndDelStatus(fp,userID);
                if (fp.getAttachmentId() != null) {
                    List<ForumReplyAttachment> listAtt = new ArrayList<>();
                    ForumReplyAttachment fa = this.getSingleByReplyAtt(fp.getAttachmentId());
                    ForumReplyAttachment newFa = new ForumReplyAttachment();
                    this.addImportUserMessage(fp,fa,newFa);
                    listAtt.add(newFa);
                    fp.setForumReplyAttachmentHashSet(listAtt);
                }
            }
        }
        map.put("listReplyPage", listParentReply);
        return map;
    }

    @Override
    public HashMap allReplyByType(User user, Integer postId, String time, Integer findUser, String mesType,
                                  Date startTime, Date endTime, PageInfo pageInfo, String type) {
        String hqlReply = "from ForumReply where post = :post ";
        HashMap<String, Object> paramReply = new HashMap<>();
        paramReply.put("post", postId);
        Integer key = 0;
        if(time != null && !"".equals(time)){
            String hqlCount = "select count(id) from ForumReply where  post = :post and createDate > :createDate order by createDate desc";
            HashMap<String, Object> paramCount = new HashMap<>();
            paramCount.put("post", postId);
            paramCount.put("createDate", NewDateUtils.tomorrow(NewDateUtils.dateFromString(time,"yyyy-MM-dd")));
            Long num = (Long) forumReplyDao.getByHQLWithNamedParams(hqlCount, paramCount);
            int pageNum = (int)(num/pageInfo.getPageSize()) + 1;
            key = (int)(num%pageInfo.getPageSize() + 1);
            pageInfo.setCurrentPageNo(pageNum);
        }
        if (findUser != null) {
            hqlReply = hqlReply + "and creator = :creator";
            paramReply.put("creator", findUser);
        }
        if ("2".equals(mesType)) {
            String hqlReplyUserstat = "from ForumReplyUserStat where post = :post and userID = :userID and atMum is not null";
            HashMap<String, Object> paramReplyUserstat = new HashMap<>();
            paramReplyUserstat.put("post", postId);
            paramReplyUserstat.put("userID", user.getUserID());
            List<ForumReplyUserStat> listReplyUserstate = forumReplyUserStatDao.getListByHQLWithNamedParams(hqlReplyUserstat, paramReplyUserstat);
            List<Integer> listReplyId = new ArrayList<>();
            if (!listReplyUserstate.isEmpty()) {
                for (ForumReplyUserStat frs : listReplyUserstate) {
                    listReplyId.add(frs.getReply());
                }
                hqlReply = hqlReply + " and id in (:listReplyId)";
                paramReply.put("listReplyId", listReplyId);
            } else {
                hqlReply = hqlReply + " and id in (null)";
            }

        }else if("3".equals(mesType)){
            String hqlParentReply = "from ForumReply where post = :post and creator = :creator and replyNum > 0";
            HashMap<String, Object> paranParentReply = new HashMap<>();
            paranParentReply.put("post", postId);
            paranParentReply.put("creator", user.getUserID());
            List<ForumReply> listParentReply = forumReplyDao.getListByHQLWithNamedParams(hqlParentReply,paranParentReply);
            if (!listParentReply.isEmpty()) {
                List<Integer> listUserId = new ArrayList<>();
                for (ForumReply fr : listParentReply) {
                    listUserId.add(fr.getId());
                }
                hqlReply = hqlReply + " and parent in (:listUserId) ";
                paramReply.put("listUserId",listUserId);
            } else {
                hqlReply = hqlReply + " and parent in (:null) ";
            }
        }
        if (startTime != null) {
            hqlReply = hqlReply + " and createDate >= :startTime and createDate < :endTime";
            paramReply.put("startTime", startTime);
            paramReply.put("endTime", endTime);
        }
        hqlReply = hqlReply + " order by createDate desc";
        List<ForumReply> listReply = null;
        HashMap<String, Object> map = new HashMap<>();
        if("1".equals(type)){
            listReply = forumReplyDao.getListByHQLWithNamedParams(hqlReply, paramReply, pageInfo);
            map.put("pageInfo", pageInfo);
        } else {
            listReply = forumReplyDao.getListByHQLWithNamedParams(hqlReply, paramReply);
        }
        if (!listReply.isEmpty()) {
            if(time != null && !"".equals(time)){
                Integer i = 0;
                for (ForumReply f : listReply)  {
                    if (f.getAttachmentId() != null) {
                        List<ForumReplyAttachment> listAtt = new ArrayList<>();
                        ForumReplyAttachment fa = this.getSingleByReplyAtt(f.getAttachmentId());
                        ForumReplyAttachment newFa = new ForumReplyAttachment();
                        this.addImportUserMessage(f,fa,newFa);
                        listAtt.add(newFa);
                        f.setForumReplyAttachmentHashSet(listAtt);
                    }
                    if(i.equals(key)){
                        f.setKeyword("1");
                    }
                    i ++;
                }
            }else {
                for (ForumReply f : listReply)  {
                    if (f.getAttachmentId() != null) {
                        List<ForumReplyAttachment> listAtt = new ArrayList<>();
                        ForumReplyAttachment fa = this.getSingleByReplyAtt(f.getAttachmentId());
                        ForumReplyAttachment newFa = new ForumReplyAttachment();
                        this.addImportUserMessage(f,fa,newFa);
                        listAtt.add(newFa);
                        f.setForumReplyAttachmentHashSet(listAtt);
                    }
                }
            }
        }
        map.put("listReply", listReply);
        return map;
    }

    @Override
    public List<ForumReply> getReplyByDay(Integer postId, Date startDay, Date endDay, PageInfo pageInfo) {
        String hqlReply = "from ForumReply where post = :post and createDate >= :startDay and createDate < :endDay";
        HashMap<String, Object> paramReply = new HashMap<>();
        paramReply.put("post", postId);
        paramReply.put("startDay", startDay);
        paramReply.put("endDay", endDay);
        List<ForumReply> listReply = forumReplyDao.getListByHQLWithNamedParams(hqlReply, paramReply, pageInfo);
        return listReply;
    }

    @Override
    public List<ForumReplyAttachment> getSubsidiaryFile(Integer postId, String type, String attType, String place, PageInfo pageInfo) {
        String hqlAllReply = " from ForumReply where post = :post and valid = 1 and attachmentId is not null";
        HashMap<String, Object> paramReply = new HashMap<>();
        paramReply.put("post", postId);
        if (attType != null) {
            if ("3".equals(attType)) {
                hqlAllReply = hqlAllReply + " and replyType in :list";
                List<Integer> list = new ArrayList<>();
                list.add(3);
                list.add(4);
                paramReply.put("list",list);
            } else if ("1".equals(attType)) {
                hqlAllReply = hqlAllReply + " and replyType = :replyType";
                paramReply.put("replyType",2);
            } else if ("2".equals(attType)) {
                hqlAllReply = hqlAllReply + " and replyType = :replyType";
                paramReply.put("replyType",3);
            } else if ("4".equals(attType)){
                hqlAllReply = hqlAllReply + " and isUploaded is not null group by attachmentId";
            }
        } else {
            hqlAllReply = hqlAllReply + " and replyType not in :list";
            List<Integer> list = new ArrayList<>();
            list.add(1);
            list.add(4);
            paramReply.put("list",list);
        }
        if("1".equals(type)){
            hqlAllReply = hqlAllReply +" order by createDate asc";
        } else {
            hqlAllReply = hqlAllReply +" order by createDate desc";
        }
        List<ForumReply> listReply = forumReplyDao.getListByHQLWithNamedParams(hqlAllReply,paramReply,pageInfo);
        List<ForumReplyAttachment> listReplyAtt = new ArrayList<>();
        if (!listReply.isEmpty()) {
            for (ForumReply f : listReply) {
                ForumReplyAttachment fa = this.getSingleByReplyAtt(f.getAttachmentId());
                ForumReplyAttachment newFa = new ForumReplyAttachment();
                this.addImportUserMessage(f,fa,newFa);
                newFa.setReplyId(f.getId());
                listReplyAtt.add(newFa);
                if (f.getCreator() != null) {
                    UserHonePageDto userDto = userService.getUserHonePageDtoByUserId(f.getCreator());
                    fa.setImgPath(userDto.getImgPath());
                    fa.setGender(userDto.getGender());
                }
            }
        }
        return listReplyAtt;
    }

    //给消息添加人员的详情并判断删除按钮是否显示
    private void fillInReplyNumAndDelStatus(ForumReply fp, Integer userID){
        User UserReply = userService.getUserByID(fp.getCreator());
        fp.setPostUserMussage(backUser(UserReply));
        //判断删除按钮是否显示 delStatus为1时显示为0时不显示
        if (fp.getReplyType().equals(1)) {
            if (fp.getMsgType().equals("1")) {
                if (fp.getCreator().equals(userID)) {
                    if (fp.getReplyNum().compareTo(0) == 0) {
                        BigInteger nowDateNum = BigInteger.valueOf((new Date()).getTime());
                        BigInteger createDateNum = BigInteger.valueOf(fp.getCreateDate().getTime());
                        BigInteger num = nowDateNum.subtract(createDateNum);
                        if (num.compareTo(BigInteger.valueOf(300000)) < 1) {
                            fp.setDelStatus(1);
                        }
                    }
                }
            }
        }
    }

    //统计未查看的回帖和回复数
    private BigInteger getRepyUserStat(Integer post, Integer reply, Integer userID, BigInteger number){
        String hqlParentReplyUser = "from ForumReplyUserStat where post = :post and userID = :userID";
        HashMap<String, Object> paramParentReply = new HashMap<>();
        paramParentReply.put("post", post);
        paramParentReply.put("userID", userID);
        if(!reply.equals(0)){
            hqlParentReplyUser = hqlParentReplyUser + " and reply = :reply";
            paramParentReply.put("reply", reply);
        }
        ForumReplyUserStat replyUserStat = (ForumReplyUserStat) forumReplyUserStatDao.getByHQLWithNamedParams(hqlParentReplyUser, paramParentReply);
        if(replyUserStat != null){
            number = new BigInteger(String.valueOf(replyUserStat.getTotalNum())).subtract(new BigInteger(String.valueOf(replyUserStat.getReadMum())));
        }
        return number;
    }

    public PostUserMussage backUser(User user){
        PostUserMussage postUserMussage = new PostUserMussage();
        postUserMussage.setUserID(user.getUserID());
        postUserMussage.setUserName(user.getUserName());
        postUserMussage.setMobile(user.getMobile());
        postUserMussage.setPostName(user.getPostName());
        postUserMussage.setDepartName(user.getDepartName());
        postUserMussage.setImgPath(user.getImgPath());
        postUserMussage.setGender(user.getGender());
        return postUserMussage;
    }

    @Override
    public int delMessageForForumPost(Integer id, Integer userID) {
        HashMap<String,Object> param = new HashMap<>();
        param.put("post", id);
        param.put("userID", userID);
        String hql = "from ForumUserStat where post = :post and user = :userID";
        ForumUserStat forumUserStat = (ForumUserStat) forumUserStatDao.getByHQLWithNamedParams(hql,param);
        if(forumUserStat != null){
            String hqlUserStat = "delete from ForumUserStat where post = :post and user = :userID";
            int state = forumUserStatDao.queryHQLWithNamedParams(hqlUserStat, param);
        }
        return 0;
    }

    @Override
    public Integer approveStatus(User user, ForumPost forumPost) {
        String hql = "from ForumPost where id = :id";
        HashMap<String, Object> param1 = new HashMap<>();
        param1.put("id",forumPost.getId());
        ForumPost newForumPost = (ForumPost) forumPostDao.getByHQLWithNamedParams(hql,param1);
        Integer status = 1;
        if ("1".equals(newForumPost.getApproveStatus())) {
            newForumPost.setAuditor(user.getUserID());
            newForumPost.setAuditorName(user.getUserName());
            newForumPost.setAuditDate(new Date());
            newForumPost.setUpdateDate(newForumPost.getAuditDate());
            newForumPost.setApproveStatus(forumPost.getApproveStatus());
            if (("3").equals(forumPost.getApproveStatus()) && forumPost.getApproveMemo() != null) {
                newForumPost.setApproveMemo(forumPost.getApproveMemo());
            }
            User userCreate = userService.getUserByID(newForumPost.getCreator());
            HashMap<String, Object> map = new HashMap<>();
            if (("3").equals(forumPost.getApproveStatus())) {
                this.sendForumPostMessage(2, newForumPost, null);              //给我的消息发推送
            }else if (("2").equals(forumPost.getApproveStatus())){
                this.sendForumPostMessage(1, newForumPost, null);              //给我的消息发推送
                //给讨论区图标添加消息
                StringBuffer allName = new StringBuffer();
                List<ForumPostUser> listForumPostUser = this.listPostUser(forumPost.getId());
                for(ForumPostUser fpu : listForumPostUser){
                    allName.append(fpu.getUserName()+ " ");
                }
                ForumReply forumReply = this.insertSystemReply(user.getUserID(), newForumPost.getId(), newForumPost.getCreateName(), "1" , allName.toString(), null, null, null);
                forumReply.setPostUserMussage(this.backUser(user));
                List<ForumReply> listReplyPush = new ArrayList<>();
                listReplyPush.add(forumReply);
                map.put("addForumReply", listReplyPush);
                this.pushNewReplyByType(listForumPostUser, "1", newForumPost.getCreator(), newForumPost.getId(), forumReply.getId(), null, null, null, map,newForumPost, null, null);
            }
            //给大小总务发推送
            map.put("forumPost", newForumPost);
            swMessageService.rejectSend(0,-1,map,newForumPost.getCreator().toString(),"/applyForumPost",null,null,userCreate,"forumApply");    //给讨论申请人发
            swMessageService.rejectSendToMid(-1,-1,map,user.getOid(),"AuthApproval","/approvalForumPost","forumApproval", null,null);   //给所大小总务发送
            clusterMessageSendingOperations.convertAndSendToUser(newForumPost.getId().toString(), "/newForumPostMes",null, null, user.getOid(), user.getOrganization().getName(), JSON.toJSONString(map));
        } else {
            status = 2;
        }
        return status;
    }

    @Override
    public ForumPost upForumPost(ForumPost forumPost) {
        ForumPost post = forumPostDao.get(forumPost.getId());
        post.setUpdateDate(forumPost.getUpdateDate());
        if (forumPost.getLastestReplyTime() != null) {
            post.setLastestReplyTime(forumPost.getLastestReplyTime());
        }
        if (forumPost.getParticipantsNum() != null) {
            post.setParticipantsNum(forumPost.getParticipantsNum());
        }
        forumPostDao.saveOrUpdate(post);
        return post;
    }

    @Override
    public void upPostStatus(ForumPost forumPost, String title, String content, Integer compere, String compereName, Integer participantsNum, Date date) {
        if(title != null){
            forumPost.setTitle(title);
        }
        if (content != null) {
            forumPost.setContent(content);
        }
        if (compere != null) {
            forumPost.setCompere(compere);
            forumPost.setCompereName(compereName);
        }
        if (participantsNum != null) {
            forumPost.setParticipantsNum(participantsNum);
        }
        forumPost.setUpdateDate(date);
    }

    /*@Override
    public void upForumPostParticipantsNum(ForumPost forumPost) {
        String hql = "from ForumPost where id =" +forumPost.getId();
        ForumPost post = (ForumPost) forumPostDao.getByHQLWithNamedParams(hql,null);

        post.setUpdateDate(new Date());
        forumPostDao.saveOrUpdate(post);
    }*/


    @Override
    public List<ForumPost> getForumPost(Integer userID, String title, Integer enabled, String updateDate, PageInfo pageInfo) {
//        String hql = "FROM ForumPost WHERE id in (SELECT post FROM ForumPostUser WHERE userID = :userID)";
        Date now = new Date(System.currentTimeMillis());
        System.out.println("getForumPost now = " + NewDateUtils.dateToString(now, sdf));
//        StringBuffer hql = new StringBuffer("from ForumPost t join fetch t.forumPostUsers u");
        StringBuffer hql = new StringBuffer("select u.forumPost from ForumPostUser u");
        List<String> where = new ArrayList<String>(){{
            add("u.userID = :userID");
        }};
        Map<String, Object> param = new HashMap<String, Object>(){{
            put("userID", userID);
        }};
        if(updateDate != null && updateDate != ""){
//            where.add("t.updateDate < :updateDate");
            where.add("u.forumPost.updateDate < :updateDate");
            param.put("updateDate", NewDateUtils.dateFromString(updateDate, "yyyy-MM-dd HH:mm:ss"));
        }
        if (StringUtils.isNotEmpty(title)) {
//            where.add("t.title like :title");
            where.add("u.forumPost.title like :title");
            param.put("title", "%"+title+"%");
        }
        if (enabled != null) {
//            where.add("t.enabled = :enabled");
            where.add("u.forumPost.enabled = :enabled");
            param.put("enabled", enabled);
            if (enabled==0) {
                where.add("u.forumPost.isCleaned = 1");
            }
        }
//        where.add("t.approveStatus = 2");
        where.add("u.forumPost.approveStatus = 2");
//        hql.append(" where ").append(StringUtils.join(where," and ")).append(" order by t.updateDate desc");
        hql.append(" where ").append(StringUtils.join(where," and ")).append(" order by u.forumPost.updateDate desc");
        List<ForumPost> listPost = forumPostDao.getListByHQLWithNamedParams(hql.toString(), param, pageInfo);

        Date tailer = new Date(System.currentTimeMillis());
        System.out.println("getForumPost now = " + NewDateUtils.dateToString(tailer, sdf) + ", Duration = " + (tailer.getTime()-now.getTime()));
        for (ForumPost fp : listPost)  {
            fp.setLastReply(this.getLastOneForumReply(fp.getId()));
            this.setMesForpost(userID, fp);
        }
        Date end = new Date(System.currentTimeMillis());
        System.out.println("getForumPost now = " + NewDateUtils.dateToString(end, sdf) + ", Duration = " + (end.getTime()-tailer.getTime()));
        return listPost;
    }

    @Override
    public void insertForumReply(ForumReply forumReply) {
        forumReplyDao.save(forumReply);
    }

    @Override
    public ForumReply upForumReply(ForumReply forumReply) {
        forumReplyDao.saveOrUpdate(forumReply);
        return forumReply;
    }

    @Override
    public ForumReply oneForumReply(Integer id) {
        ForumReply forumReply = forumReplyDao.get(id);
        return forumReply;
    }

    @Override
    public ForumReply getLastOneForumReply(Integer postId) {
        String hql = "from ForumReply where post = :post order by createDate desc";
        HashMap<String, Object> param = new HashMap<>();
        param.put("post", postId);
        ForumReply forumReply = (ForumReply) forumReplyDao.getByHQLWithNamedParams(hql,param);
        if(forumReply != null){
            if (forumReply.getAttachmentId() != null) {
                List<ForumReplyAttachment> listAtt = new ArrayList<>();
                ForumReplyAttachment fa = this.getSingleByReplyAtt(forumReply.getAttachmentId());
                ForumReplyAttachment newFa = new ForumReplyAttachment();
                this.addImportUserMessage(forumReply,fa,newFa);
                listAtt.add(newFa);
                forumReply.setForumReplyAttachmentHashSet(listAtt);
            }
        }
        return forumReply;
    }

    @Override
    public void delReplyAttachment(Integer replyId, User user) {
        String hql = "from ForumReplyAttachment where reply = :reply";
        HashMap<String, Object> param = new HashMap<>();
        param.put("reply", replyId);
        List<ForumReplyAttachment> list = forumReplyAttachmentDao.getListByHQLWithNamedParams(hql,param);
        if (!(list.isEmpty())) {
            for (ForumReplyAttachment f : list) {
                if (!"3".equals(f.getType())) {
                    ForumUsing callback = new ForumUsing(f.getId(), f.getClass());
                    uploadService.delFileUsing(callback,f.getPath(),user);
                }
            }
        }
        hql = "delete from ForumReplyAttachment where reply = :reply";
        HashMap<String, Object> paramDel = new HashMap<>();
        paramDel.put("reply", replyId);
        Integer del = forumReplyAttachmentDao.queryHQLWithNamedParams(hql, paramDel);
    }

    @Override
    public void insertReplyUser(ForumReplyUser forumReplyUser) {
        forumReplyUserDao.save(forumReplyUser);
    }

    @Override
    public List<ForumPostUser> listPostUser(Integer id) {
        String hql = "from ForumPostUser where post = :post";
        HashMap<String, Object> param = new HashMap<>();
        param.put("post", id);
        List<ForumPostUser> list = forumPostUserDao.getListByHQLWithNamedParams(hql,param);
        return list;
    }

    @Override
    public ForumPostUser getOnePostUser(Integer postId, Integer userId) {
        String hql = "from ForumPostUser where post = :post and userID = :userID";
        HashMap<String, Object> param = new HashMap<>();
        param.put("post", postId);
        param.put("userID", userId);
        ForumPostUser forumPostUser = (ForumPostUser) forumPostUserDao.getByHQLWithNamedParams(hql, param);
        return forumPostUser;
    }

    @Override
    public void delPostUser(Integer post, Integer userId, ForumPostUser forumPostUser) {
        forumPostUserDao.delete(forumPostUser);
        String hqlForumUserStat = "from ForumUserStat where post = :post and user = :userID";
        HashMap<String, Object> param = new HashMap<>();
        param.put("post", post);
        param.put("userID", userId);
        ForumUserStat forumUserStat = (ForumUserStat) forumUserStatDao.getByHQLWithNamedParams(hqlForumUserStat, param);
        if(forumUserStat != null){
            forumUserStatDao.delete(forumUserStat);
        }
        this.delReplyUserByPostAndUser(post, userId);    //删除t_forum_reply_user表中数据
        String hqlReplyUserStat = "delete from ForumReplyUserStat where post = :post and userID = :userID";
        Integer replyUserStat = forumReplyUserStatDao.queryHQLWithNamedParams(hqlReplyUserStat, param);
    }

    @Override
    public String addPostUser(Integer post, Integer userId) {
        ForumPostUser forumPostUser = this.getOnePostUser(post, userId);
        String state = "0";
        if(forumPostUser == null){
            state = "1";
            User user = userService.getUserByID(userId);
            ForumPostUser postUser  = new ForumPostUser();
            postUser.setPost(post);
            postUser.setUserID(userId);
            postUser.setUserName(user.getUserName());
            this.insertFotrumUser(postUser);
            ForumUserStat forumUserStat = new ForumUserStat();
            forumUserStat.setUser(userId);
            forumUserStat.setPost(post);
            forumUserStatDao.save(forumUserStat);
        }
        return state;
    }

    @Override
    public ForumReplyUser getReplyUser(Integer replyId, Integer userID) {
        String hql = "from ForumRepyUser where reply = :replyId and userID = :userID";
        HashMap<String, Object> param = new HashMap<>();
        param.put("replyId", replyId);
        param.put("userID", userID);
        ForumReplyUser forumReplyUser = (ForumReplyUser) forumReplyUserDao.getByHQLWithNamedParams(hql, param);
        return forumReplyUser;
    }


    @Override
    public Integer delReplyUser(Integer id) {
        String hql = "delete from ForumRepyUser where reply = :reply";
        HashMap<String, Object> param = new HashMap<>();
        param.put("reply", id);
        Integer state = forumReplyDao.queryHQLWithNamedParams(hql, param);
        return state;
    }

    @Override
    public void delReplyUserByPostAndUser(Integer id, Integer userID) {
        String hql = "delete from ForumRepyUser where post = :post and userID = :userID";
        Map<String, Object> paramUser = new HashMap<String, Object>(2){{
            put("post", id);
            put("userID", userID);
        }};
        Integer state = forumReplyUserDao.queryHQLWithNamedParams(hql, paramUser);
    }

    @Override
    public void delReplyUserState(Integer postId, Integer replyId, Integer userID) {
        String hql = "delete from ForumReplyUserStat where post = :post";
        HashMap<String, Object> param = new HashMap<>();
        param.put("post", postId);
        Integer state = 0;
        if (replyId != null) {
            hql = hql + " and reply = :reply";
            param.put("reply", replyId);
            state = forumReplyUserStatDao.queryHQLWithNamedParams(hql, param);
        }
        if (userID != null) {
            hql = hql + " and userID = :userID and atMum is null";
            param.put("userID", userID);
            state = forumReplyUserStatDao.queryHQLWithNamedParams(hql, param);
            List<ForumReplyUserStat> listReplyUserstate = this.listReplyUserStat(postId, userID);
            if(!listReplyUserstate.isEmpty()){
                for (ForumReplyUserStat f : listReplyUserstate) {
                    if(f.getAtMum() != null){
                        f.setReadMum(1);
                        forumReplyUserStatDao.saveOrUpdate(f);
                    }
                }
            }
        }
    }


    @Override
    public boolean sendForumPostMessage(Integer type, ForumPost forumPost, Integer userId) {
        UserSuspendMsg userSuspendMsg = new UserSuspendMsg();
        if(type == 1){
            userSuspendMsg.setContent("《" + forumPost.getTitle() + "》的讨论申请被批准了！");
            userSuspendMsg.setMemo("审批时间 "+ NewDateUtils.dateToString(forumPost.getAuditDate(),"yyyy-MM-dd HH:mm:ss"));
            userSuspendMsgService.saveUserSuspendMsg(1, userSuspendMsg.getContent(), userSuspendMsg.getMemo(), forumPost.getCreator(), "applyDiscussDetail", forumPost.getId());
        }else if (type == 2) {
            userSuspendMsg.setContent("《" + forumPost.getTitle() + "》的讨论申请被驳回了！");
            userSuspendMsg.setMemo("审批时间 "+ NewDateUtils.dateToString(forumPost.getAuditDate(),"yyyy-MM-dd HH:mm:ss"));
            userSuspendMsgService.saveUserSuspendMsg(1, userSuspendMsg.getContent(), userSuspendMsg.getMemo(), forumPost.getCreator(), "applyDiscussDetail", forumPost.getId());
        }else if (type == 3) {
            userSuspendMsg.setContent("您已被移出《" + forumPost.getTitle() + "》讨论组！");
            userSuspendMsg.setMemo("操作时间 "+ NewDateUtils.dateToString(forumPost.getUpdateDate(),"yyyy-MM-dd HH:mm:ss"));
            userSuspendMsgService.saveUserSuspendMsg(1, userSuspendMsg.getContent(), userSuspendMsg.getMemo(), userId, "applyDiscussDetail", forumPost.getId());
        } else if (type == 4) {
            userSuspendMsg.setContent("《" + forumPost.getTitle() + "》讨论组因长期无人留言，已被停用！");
            userSuspendMsg.setMemo("操作时间 系统 "+ NewDateUtils.dateToString(forumPost.getEnabledTime(),"yyyy-MM-dd HH:mm:ss"));
            userSuspendMsgService.saveUserSuspendMsg(1, userSuspendMsg.getContent(), userSuspendMsg.getMemo(), userId, null, null);
        } else if (type == 5) {
            String cleanTime = NewDateUtils.dateToString( forumPost.getCleanTime(), "yyyy-MM-dd");
            userSuspendMsg.setContent("《" + forumPost.getTitle() + "》将于"+ cleanTime + "消失，之前您还可查看！");
            userSuspendMsg.setMemo("操作时间 "+forumPost.getCompereName()+ " " + NewDateUtils.dateToString(forumPost.getValidTime(),"yyyy-MM-dd HH:mm:ss"));
            userSuspendMsgService.saveUserSuspendMsg(1, userSuspendMsg.getContent(), userSuspendMsg.getMemo(), userId, null, null);
        }
        return true;
    }

    @Override
    public void insertReplyUserStat(ForumReplyUserStat forumReplyUserStat) {
        forumReplyUserStatDao.save(forumReplyUserStat);
    }

    @Override
    public Integer getBadgeNumber(User user, String code) {
        Integer number=null;
        String hql = null;
        HashMap<String, Object> param = new HashMap<>();
        Long num = null;
        switch (code){
            case "forumApproval"://讨论发起审批
                hql = "SELECT count(f.id) FROM ForumPost f, ApprovalProcess ap WHERE f.id = ap.business AND ap.businessType = 43 AND ap.toMid = :toMid AND ap.org = :org AND f.approveStatus = 1";
                param.put("org", user.getOid());
                param.put("toMid", "AuthApproval");
                num = (Long) forumPostDao.getByHQLWithNamedParams(hql,param);
                number = num.intValue();
                break;
            case "manageTheForum"://管理的讨论组
                number = this.getManagerPostListNum(user);
                break;
            case "forumLaunchApproval"://管理的讨论组
                hql = "SELECT count(f.id) FROM ForumPost f, ApprovalProcess ap WHERE f.id = ap.business AND ap.businessType = 43 AND ap.toUser = :toUser AND ap.approveStatus = 1 AND ap.toMid is null";
                param.put("toUser", user.getUserID());
                num = (Long) forumPostDao.getByHQLWithNamedParams(hql,param);
                number = num.intValue();
                break;
        }
        return number;
    }

    @Override
    public void twoUserForumPost(Integer org, List<ForumPostUser> listPostUser, User user, ForumPost forumPost) {
        String hql = "select post from ForumPostUser where userID = :user1 and post in (select f.id from ForumPost f, ForumPostUser u where f.id = u.post and f.category = 1 and f.org = :org  and u.userID = :user2 group by f.id)";
        HashMap<String, Object> param = new HashMap<>();
        param.put("user1", listPostUser.get(0).getUserID());
        param.put("org", org);
        param.put("user2", user.getUserID());
        ForumPostUser postUserByMine  = new ForumPostUser();
        postUserByMine.setUserID(user.getUserID());
        postUserByMine.setUserName(user.getUserName());
        listPostUser.add(postUserByMine);
        Integer post = (Integer) forumPostUserDao.getByHQLWithNamedParams(hql, param);
        HashMap<String, Object> map = new HashMap<>();
        ForumReply forumReply = null;
        List<ForumReply> listReplyPush = new ArrayList<>();
        if (post != null) {
            forumReply = this.insertSystemReply(user.getUserID(), post, user.getUserName(),"3",null, null, null, null);
            ForumPost oldforumPost = this.forumPostMessageByhead(post);
            this.upPostStatus(oldforumPost, null, null, null, null, null, forumReply.getCreateDate());
            forumReply.setPostUserMussage(this.backUser(user));
            listReplyPush.add(forumReply);
            map.put("addForumReply", listReplyPush);
            this.pushNewReplyByType(listPostUser, "1", user.getUserID(), post, forumReply.getId(), null, null, null, map, oldforumPost, null, null);
        }else {
            String allName = user.getUserName() + " " + listPostUser.get(0).getUserName()+ " ";
            forumPost.setTitle(allName);
            this.insertForumStatus(forumPost, user);
            //新增参与人
            for (ForumPostUser fpu : listPostUser) {
                fpu.setPost(forumPost.getId());
                this.insertFotrumUser(fpu);
            }
            forumReply = this.insertSystemReply(user.getUserID(), forumPost.getId(), user.getUserName(), "1", allName.toString(), null, null, null);
            forumReply.setPostUserMussage(this.backUser(user));
            listReplyPush.add(forumReply);
            map.put("addForumReply", listReplyPush);
            this.pushNewReplyByType(listPostUser, "1", forumPost.getCreator(), forumPost.getId(), forumReply.getId(), null, null, null, map, forumPost, null, null);
        }
    }

    /**
     * 按人员推送讨论区消息的方法
     * @param forumPostUserList     需要遍历的人
     * @param type                   状态 1发送初始化的消息 2发送正常消息 3发送删除的消息
     * @param userID                 登录人id
     * @param postId                 当前所在的讨论id
     * @param replyId                新增的回复的id
     * @param reply                  回复标记，只在type是2时才可能传
     * @param creator                回复人，当传了回复标记时，要把回复人给传过来
     * @param listUser               @的人员，只在type是2时才可能传
     * @param map                    发送的数据包
     * @param forumPost             由于讨论的一些标记得重新赋值，所以要把讨论传过来然后赋值放到数据包中
     * @param operationType         操作类型 1是换版操作发消息 null是其它操作
     */
    @Override
    public void pushNewReplyByType(List<ForumPostUser> forumPostUserList, String type, Integer userID, Integer postId,
                                   Integer replyId, Integer reply, Integer creator, String listUser,
                                   HashMap<String, Object> map, ForumPost forumPost, String forumAndroidId, String operationType) {
        for (ForumPostUser fpu : forumPostUserList) {
            forumPost.setReplyTag(0);
            forumPost.setAtTag(0);
            Integer userIdPost = fpu.getUserID();
            HashMap<String, Object> mapPost = new HashMap<>();
            if ("2".equals(type)) {
                String state = "1";
                if (operationType == null) {
                    if (userIdPost.equals(userID)) {
                        state = "2";
                    }
                }
                if ("1".equals(state)) {
                    ForumReplyUser replyUser = new ForumReplyUser();   //创建用户回复表对象用于主页每条讨论上的未读数字
                    ForumReplyUserStat replyUserStat = new ForumReplyUserStat();    //创建用户统计表对象用于M条数和记录@人的跳转
                    if(reply != null){
                        if(fpu.getUserID().equals(creator)){
                            forumPost.setReplyTag(1);   //标记回复
                            //    map.put("replyTag", "1");  //标记回复
                            replyUser.setMemo("1");
                        }
                    } else {
                        forumPost.setReplyTag(0);
                    }
                    if(listUser != null){
                        List<String> userList = JSON.parseArray(listUser, String.class);
                        for(String u : userList){
                            if (u.equals(userIdPost.toString())) {
                                replyUser.setAtMum(1);
                                replyUserStat.setAtMum(1);
                                forumPost.setAtTag(1);   //标记@
                            }
                        }
                    }else {
                        forumPost.setAtTag(0);
                    }
                    replyUser.setUserID(fpu.getUserID());
                    replyUser.setPost(postId);
                    replyUser.setReply(replyId);
                    this.insertReplyUser(replyUser);
                    replyUserStat.setUserID(fpu.getUserID());
                    replyUserStat.setPost(postId);
                    replyUserStat.setReply(replyId);
                    replyUserStat.setTotalNum(1);
                    replyUserStat.setReadMum(0);
                    this.insertReplyUserStat(replyUserStat);
                }
            }else if ("1".equals(type)){
                ForumUserStat forumUserStat = new ForumUserStat();  //创建讨论用户统计表对象
                ForumReplyUser replyUser = new ForumReplyUser();   //创建用户回复表对象用于主页每条讨论上的未读数字
                ForumReplyUserStat replyUserStat = new ForumReplyUserStat();    //创建用户统计表对象用于M条数和记录@人的跳转
                forumUserStat.setUser(fpu.getUserID());
                forumUserStat.setPost(fpu.getPost());
                forumUserStatDao.save(forumUserStat);
                replyUser.setUserID(fpu.getUserID());
                replyUser.setPost(postId);
                replyUser.setReply(replyId);
                this.insertReplyUser(replyUser);
                replyUserStat.setUserID(fpu.getUserID());
                replyUserStat.setPost(postId);
                replyUserStat.setReply(replyId);
                replyUserStat.setTotalNum(1);
                replyUserStat.setReadMum(0);
                this.insertReplyUserStat(replyUserStat);
            }
            this.setMesForpost(userIdPost, forumPost);
            map.put("forumPost", forumPost);
            if (forumAndroidId != null) {
                map.put("forumAndroidId", forumAndroidId);
            }
            clusterMessageSendingOperations.convertAndSendToUser(fpu.getUserID().toString(), "/addOneReply",null, null, forumPost.getOrg(), null, JSON.toJSONString(map));
            mapPost.put("twinkleTag","1");
            clusterMessageSendingOperations.convertAndSendToUser(fpu.getUserID().toString(), "/formPostNumforSuperscript",null, null, null, null, JSON.toJSONString(mapPost));
        }
    }

    @Override
    public ForumReply insertSystemReply(Integer userID, Integer postId, String creatName, String type, String allName, String title, String content, Integer compere) {
        ForumReply forumReply = new ForumReply();
        User user = userService.getUserByID(userID);
        forumReply.setPost(postId);
        if(type.equals("1")){
            forumReply.setContent(allName + "组成了讨论组");
        } else if (type.equals("2")) {
            forumReply.setContent(creatName+ "修改了讨论组的参与人员");
        } else if (type.equals("3")) {
            forumReply.setContent(creatName+ "再次发起讨论");
        }else if (type.equals("4")) {
            forumReply.setContent(allName + "组成了讨论组，讨论组带有附件，具体如下！");
        }else {
            String mes = creatName + "修改了讨论组的";
            if (title != null) {
                mes = mes + " 主题";
            }
            if (content != null) {
                mes = mes + " 描述";
            }
            if (compere != null) {
                mes = mes + " 主持人";
            }
            forumReply.setContent(mes);
        }
        forumReply.setOrg(user.getOid());
        forumReply.setCreator(userID);
        forumReply.setCreateName(creatName);
        Date now = new Date();
        forumReply.setCreateDate(now);
        forumReply.setValid(1);
        forumReply.setReplyNum(0);
        forumReply.setMsgType("3");
        forumReply.setReplyType(1);
        this.insertForumReply(forumReply);
        return forumReply;
    }

    @Override
    public Integer checkeForumPostHistory(Integer postId, ForumPost forumPost, List<ForumPostUser> listPostUser, User user) {
        String hql = "select versionNo from ForumPostHistory where post =:postId order by versionNo desc";
        HashMap<String, Object> param = new HashMap<>();
        param.put("postId", postId);
        Integer versionNo = (Integer) forumReplyDao.getByHQLWithNamedParams(hql, param);
        if (versionNo == null) {
            if (listPostUser == null) {
                listPostUser = this.listPostUser(postId);
            }
            versionNo = 0;
            this.insertForumPostHistory(forumPost, versionNo, listPostUser, user);
        }
        versionNo++;
        return versionNo;
    }

    @Override
    public void insertForumPostHistory(ForumPost forumPost, Integer versionNo, List<ForumPostUser> listPostUser, User user) {
        ForumPostHistory forumPostHistory = new ForumPostHistory();
        forumPostHistory.setPost(forumPost.getId());
        forumPostHistory.setOrg(forumPost.getOrg());
        forumPostHistory.setCategory(forumPost.getCategory());
        forumPostHistory.setTitle(forumPost.getTitle());
        forumPostHistory.setContent(forumPost.getContent());
        forumPostHistory.setValid(forumPost.getValid());
        forumPostHistory.setEnabled(forumPost.getEnabled());
        forumPostHistory.setParticipantsNum(forumPost.getParticipantsNum());
        forumPostHistory.setCompere(forumPost.getCompere());
        forumPostHistory.setCompereName(forumPost.getCompereName());
        forumPostHistory.setCreator(forumPost.getCreator());
        forumPostHistory.setCreateName(forumPost.getCreateName());
        forumPostHistory.setCreateDate(forumPost.getCreateDate());
        forumPostHistory.setUpdator(user.getUserID());
        forumPostHistory.setUpdateName(user.getUserName());
        forumPostHistory.setUpdateDate(forumPost.getUpdateDate());
        forumPostHistory.setAuditor(forumPost.getAuditor());
        forumPostHistory.setAuditorName(forumPost.getAuditorName());
        forumPostHistory.setAuditDate(forumPost.getAuditDate());
        forumPostHistory.setApproveStatus(forumPost.getApproveStatus());
        forumPostHistory.setVersionNo(versionNo);
        forumPostHistoryDao.save(forumPostHistory);
        for (ForumPostUser fu : listPostUser) {
            ForumPostUserHistory fuh = new ForumPostUserHistory();
            fuh.setOrg(forumPost.getOrg());
            fuh.setPost(forumPost.getId());
            fuh.setPostHistory(forumPostHistory.getId());
            fuh.setUserID(fu.getUserID());
            fuh.setUserName(fu.getUserName());
            forumPostUserHistoryDao.save(fuh);
        }
    }

    @Override
    public List<ForumPostHistory> listForumPostHistory(Integer postId) {
        String hql = "from ForumPostHistory where post = :post";
        Map<String, Object> param = new HashMap<String, Object>(1){{
            put("post", postId);
        }};
        List<ForumPostHistory> list = forumPostHistoryDao.getListByHQLWithNamedParams(hql,param);
        return list;
    }

    @Override
    public List<ForumPostUserHistory> listForumPostHistoryUser(Integer postId, Integer postHistoryId) {
        String hql = "from ForumPostUserHistory where post = :postId and postHistory =:postHistoryId";
        HashMap<String, Object> param = new HashMap<>();
        param.put("postId", postId);
        param.put("postHistoryId", postHistoryId);
        List<ForumPostUserHistory> list = forumPostUserHistoryDao.getListByHQLWithNamedParams(hql,param);
        if (!list.isEmpty()) {
            for (ForumPostUserHistory fh : list) {
                UserHonePageDto user = userService.getUserHonePageDtoByUserId(fh.getUserID());
                fh.setImgPath(user.getImgPath());
            }
        }
        return list;
    }

    @Override
    public void  stopUsingForumPost() {
        String hql = "from ForumPost where approveStatus = :approveStatus and enabled = :enabled and category = :category and updateDate < :updateDate order by updateDate desc";
        Date updateDate = new Date(NewDateUtils.today().getTime() - TimeUnit.DAYS.toMillis(180));
        HashMap<String, Object> param = new HashMap<>();
        param.put("approveStatus", "2");
        param.put("enabled", 1);
        param.put("category", 2);
        param.put("updateDate", updateDate);
        List<ForumPost> listForumPostAdd = forumPostDao.getListByHQLWithNamedParams(hql, param);
        if (!listForumPostAdd.isEmpty()) {
            for (ForumPost fp : listForumPostAdd) {
                fp.setEnabled(0);
                fp.setEnabledTime(new Date());
                forumPostDao.saveOrUpdate(fp);
                List<ForumPostUser> listPostUser = this.listPostUser(fp.getId());
                for (ForumPostUser fpu : listPostUser) {
                    this.sendForumPostMessage(4,fp,fpu.getUserID());
                }
            }
        }
    }

    @Override
    public void removeForumPost() {
        String hql = "from ForumPost where approveStatus = :approveStatus and enabled = :enabled and valid = :valid and isCleaned = :isCleaned and cleanTime <= :date";
        HashMap<String, Object> param = new HashMap<>();
        param.put("approveStatus", "2");
        param.put("enabled", 0);
        param.put("valid", 0);
        param.put("isCleaned", 1);
        param.put("date", new Date());
        List<ForumPost> listForumPost = forumPostDao.getListByHQLWithNamedParams(hql, param);
        if (!listForumPost.isEmpty()) {
            for (ForumPost fps : listForumPost) {
                fps.setIsCleaned(0);
                forumPostDao.saveOrUpdate(fps);
                User userCompere = userService.getUserByID(fps.getCompere());
                HashMap<String, Object> map = new HashMap<>();
                map.put("forumPost", fps);
                User userCom = userService.getUserByID(fps.getCompere());
                swMessageService.rejectSend(-1,-1,map,userCom.getUserID().toString(),"/manageTheForum",null,null,userCom,"manageTheForum");
                User superUser = userService.getUserByRoleCode(userCom.getOid(),"super");
                if (!(userCom.getUserID().equals(superUser.getUserID()))) {
                    swMessageService.rejectSend(-1,-1,map,superUser.getUserID().toString(),"/manageTheForum",null,null,superUser,"manageTheForum");
                }
            }
        }
    }

    @Override
    public Integer forumPostByIsOpen(Integer postId, Integer isOpen) {
        ForumPost forumPost = forumPostDao.get(postId);
        forumPost.setIsOpen(isOpen);
        forumPost.setOpenTime(new Date());
        return isOpen;
    }

    @Override
    public List<ForumPost> listOpenForumPost(Integer oid, String title, PageInfo pageInfo) {
        String hql = "from ForumPost where isOpen = 1 and  isCleaned = 1 ";
        HashMap<String, Object> param = new HashMap<>();
        if (title != null && title != "") {
            hql = hql + " and title like :title";
            param.put("title", "%"+title+"%");
        }
        hql = hql + " and org = :oid order by enabledTime desc ";
        param.put("oid", oid);
        List<ForumPost> list = forumPostDao.getListByHQLWithNamedParams(hql,param,pageInfo);
        if (!list.isEmpty()) {
            for (ForumPost fp : list) {
                List<ForumPostUser> listPostUser = this.listPostUser(fp.getId());
                String alluserName = fp.getCompereName();
                for (ForumPostUser fpu : listPostUser) {
                    if (!fpu.getUserID().equals(fp.getCompere())) {
                        alluserName = alluserName + "," + fpu.getUserName();
                    }
                }
                fp.setListForumPostUser(alluserName);
            }
        }
        return list;
    }

    @Override
    public HashMap delForumPost(User user, Integer postId) {
        ForumPost forumPost = forumPostDao.get(postId);
        Date fiveDaysAgo = NewDateUtils.changeDay(new Date(),-4);
        Integer state = 1;
        HashMap<String, Object> mapBack = new HashMap<>();
        if (forumPost.getEnabledTime().getTime() < fiveDaysAgo.getTime()) {
            if (forumPost.getValid() ==1) {
                forumPost.setValid(0);
                forumPost.setValidTime(new Date());
                Date forumPosttest = NewDateUtils.joinDateTimeString(NewDateUtils.changeDay(forumPost.getValidTime(), 3), "00:20") ;
                forumPost.setCleanTime(forumPosttest);
                forumPost.setExpriationTime("将于"+ TimeUtils.toTimeString(forumPost.getCleanTime().getTime()-forumPost.getValidTime().getTime(),3) +"钟后消失");
                HashMap<String, Object> map = new HashMap<>();
                map.put("forumPost", forumPost);
                String noticeMessage = "《" + forumPost.getTitle()+ "》讨论组已被删除";
                swMessageService.rejectSend(1,1,map,user.getUserID().toString(),"/manageTheForum","有一条申请待审批",noticeMessage,user,"manageTheForum");
                User superUser = userService.getUserByRoleCode(user.getOid(),"super");
                if (!(user.getUserID().equals(superUser.getUserID()))) {
                    swMessageService.rejectSend(1,1,map,superUser.getUserID().toString(),"/manageTheForum","有一条申请待审批",noticeMessage,superUser,"manageTheForum");
                }
                List<ForumPostUser> listPostUser = this.listPostUser(postId);
                for (ForumPostUser fpu : listPostUser) {
                    if (!(user.getUserID().equals(fpu.getUserID()))) {
                        this.sendForumPostMessage(5,forumPost,fpu.getUserID());
                    }
                }
            }else {
                state = 3; //不要重复删除
            }
        } else {
            state = 2;  //没超过5天
        }
        mapBack.put("state", state);
        mapBack.put("forumPost", forumPost);
        return mapBack;
    }

    @Override
    public Integer restoreForum(Integer postId) {
        ForumPost forumPost = forumPostDao.get(postId);
        Integer state = 1;
        Date date = new Date();
        if (forumPost.getValid() == 0 && forumPost.getIsCleaned() == 1 && forumPost.getEnabled() == 0 && forumPost.getCleanTime().getTime() > date.getTime()) {
            forumPost.setValid(1);
            forumPost.setValidTime(null);
            forumPost.setCleanTime(null);
            HashMap<String, Object> map = new HashMap<>();
            map.put("forumPost", forumPost);
            User userCom = userService.getUserByID(forumPost.getCompere());
            swMessageService.rejectSend(-1,-1,map,userCom.getUserID().toString(),"/manageTheForum",null,null,userCom,"manageTheForum");
            User superUser = userService.getUserByRoleCode(userCom.getOid(),"super");
            if (!(userCom.getUserID().equals(superUser.getUserID()))) {
                swMessageService.rejectSend(-1,-1,map,superUser.getUserID().toString(),"/manageTheForum",null,null,superUser,"manageTheForum");
            }
        } else {
            state = 0;
        }
        return state;
    }

    @Override
    public ForumPostAttachment getSingleByPostAtt(Integer id) {
        ForumPostAttachment forumPostAttachment = forumPostAttachmentDao.get(id);
        return forumPostAttachment;
    }

    @Override
    public List<ForumPostAttachment> listPostAtt(Integer postId) {
        String hql = "from ForumPostAttachment where post = :post";
        Map<String, Object> param = new HashMap<String, Object>(1){{
            put("post", postId);
        }};
        List<ForumPostAttachment> list = forumPostAttachmentDao.getListByHQLWithNamedParams(hql,param);
        if (!list.isEmpty()) {
            for (ForumPostAttachment f : list) {
                if (f.getResourceId() != null) {
                    ResEntity res = resService.getSingle(f.getResourceId());
                    f.setResEntity(res);
                }
            }
        }
        return list;
    }

    @Override
    public ForumReplyAttachment getSingleByReplyAtt(Integer id) {
        ForumReplyAttachment forumReplyAttachment = forumReplyAttachmentDao.get(id);
        return forumReplyAttachment;
    }

    @Override
    public ForumPost getSingleByForum(Integer id) {
        ForumPost forumPost = forumPostDao.get(id);
        return forumPost;
    }

    @Override
    public void addApprovalProcessForForum(Integer postId, User user, Integer level, Integer toUser, String toUserName, Date createDate, String toMid) {
        ApprovalProcess ap = new ApprovalProcess();
        ap.setApproveStatus("1");
        ap.setBusiness(postId);
        ap.setBusinessType(43);
        ap.setLevel(level);
        ap.setOrg(user.getOid());
        ap.setFromUser(user.getUserID());
        ap.setUserName(user.getUserName());
        if (toMid == "") {
            ap.setToUser(toUser);
            ap.setToUserName(toUserName);
        } else {
            ap.setToMid(toMid);
        }
        ap.setCreateDate(createDate);
        approvalProcessDao.save(ap);
    }

    @Override
    public String handlerForumPost(User user, Integer postId, Integer processId, String approveStatus, String approveMemo, Integer toUser, String toUserName, String type) {
        ApprovalProcess ap = approvalProcessDao.get(processId);
        Date newDate = new Date();
        String status = "1";
        HashMap<String,Object> map =new HashMap<>();
        if("1".equals(ap.getApproveStatus())){
            ForumPost forumPost = this.forumPostMessageByhead(postId);
            map.put("forumPost", forumPost);
            forumPost.setAuditor(user.getUserID());
            forumPost.setAuditorName(user.getUserName());
            forumPost.setAuditDate(new Date());
            forumPost.setUpdateDate(newDate);
            ap.setApproveStatus(approveStatus);
            ap.setHandleTime(newDate);
            if ("3".equals(approveStatus)) {
                forumPost.setApproveStatus(approveStatus);
                forumPost.setApproveMemo(approveMemo);
                ap.setApproveMemo(approveMemo);
                if ("3".equals(type)) {
                    ap.setToUser(user.getUserID());
                    ap.setToUserName(user.getUserName());
                    swMessageService.rejectSendToMid(-1,-1,map,user.getOid(),"AuthApproval","/approvalForumPost","forumApproval", null,null);   //发给终审人员
                } else {
                    swMessageService.rejectSend(-1,-1,map,user.getUserID().toString(),"/forumLaunchApproval", null, null, user,"forumLaunchApproval");   //给中间人"讨论发起申请"待审批
                }
                this.sendForumPostMessage(2, forumPost, null);              //给申请人发送驳回消息
                User userCreator = userService.getUserByID(forumPost.getCreator());
                swMessageService.rejectSend(0,-1,map,forumPost.getCreator().toString(),"/applyForumPost",null,null,userCreator,"forumApply");    //给讨论申请人发
                if (ap.getLevel() > 1) {
                    this.sendApprovedForumPost(postId,map,processId);   //发给中间审批人的已批准-1
                }
            } else {
                if ("3".equals(type)) {
                    forumPost.setApproveStatus(approveStatus);
                    ap.setToUser(user.getUserID());
                    ap.setToUserName(user.getUserName());
                    swMessageService.rejectSendToMid(-1,-1,map,user.getOid(),"AuthApproval","/approvalForumPost","forumApproval", null,null);   //发给终审人员
                    User userCreator = userService.getUserByID(forumPost.getCreator());
                    swMessageService.rejectSend(0,-1,map,forumPost.getCreator().toString(),"/applyForumPost",null,null,userCreator,"forumApply");    //给讨论申请人发
                    if (ap.getLevel() > 1) {
                        this.sendApprovedForumPost(postId,map,processId);   //发给中间审批人的已批准-1
                    }
                    this.sendForumPostMessage(1, forumPost, null);              //给申请人推送批准消息
                    //给讨论区图标添加消息
                    StringBuffer allName = new StringBuffer();
                    List<ForumPostUser> listForumPostUser = this.listPostUser(forumPost.getId());
                    for(ForumPostUser fpu : listForumPostUser){
                        allName.append(fpu.getUserName()+ " ");
                    }

                    List<ForumPostAttachment> listPostAtt = this.listPostAtt(forumPost.getId());
                    ForumReply forumReply = null;
                    if (listPostAtt.isEmpty()) {
                        forumReply = this.insertSystemReply(user.getUserID(), forumPost.getId(), forumPost.getCreateName(), "1" , allName.toString(), null, null, null);
                    } else {
                        forumReply = this.insertSystemReply(user.getUserID(), forumPost.getId(), forumPost.getCreateName(), "4" , allName.toString(), null, null, null);
                    }
                    forumReply.setPostUserMussage(this.backUser(user));
                    List<ForumReply> listReplyPush = new ArrayList<>();
                    listReplyPush.add(forumReply);
                    map.put("addForumReply", listReplyPush);
                    this.pushNewReplyByType(listForumPostUser, "1", forumPost.getCreator(), forumPost.getId(), forumReply.getId(), null, null, null, map,forumPost, null, null);
                    if (!(listPostAtt.isEmpty())) {
                        for (ForumPostAttachment att : listPostAtt) {
                            JsonArray jsonArray = new JsonArray();
                            JsonObject jsonObject = new JsonObject();
                            if ((Type.resource.getIndex()).equals(Byte.valueOf(att.getType()))) {
                                jsonObject.addProperty("resourceId", att.getResourceId());
                                jsonObject.addProperty("resourceVersion", att.getResourceVersion());
                            } else {
                                jsonObject.addProperty("path", att.getPath());
                                jsonObject.addProperty("size", att.getSize());
                                jsonObject.addProperty("title", att.getTitle());
                                jsonObject.addProperty("module", "事务讨论");
                            }
                            jsonObject.addProperty("type", att.getType());
                            jsonObject.addProperty("place", att.getPlace());
                            jsonArray.add(jsonObject);
                            String listforumReplyAttachment = jsonArray.toString();
                            ForumReply addForumReplyAtt = this.addReplyForPub(userCreator,forumPost.getId(),null,"",null,listforumReplyAttachment,"[]",null);
                        }
                    }
                } else {
                    String noticeMessage = user.getUserName()+"在"+ NewDateUtils.dateToString(forumPost.getCreateDate(),"yyyy-MM-dd HH:mm:ss")+"提交了讨论发起申请";
                    if ("1".equals(type)) {
                        this.addApprovalProcessForForum(forumPost.getId(), user, ap.getLevel()+1, toUser, toUserName, newDate, "");
                        User approveUser = userService.getUserByID(toUser);
                        swMessageService.rejectSend(1,1,map,toUser.toString(),"/forumLaunchApproval", "有一条申请待审批", noticeMessage, approveUser,"forumLaunchApproval");   //给下一个中间人"讨论发起申请"待审批+1
                    } else if ("2".equals(type)) {
                        this.addApprovalProcessForForum(forumPost.getId(), user, ap.getLevel()+1, null, null, newDate, "AuthApproval");
                        swMessageService.rejectSendToMid(1,1,map,user.getOid(),"AuthApproval","/approvalForumPost","forumApproval", "有一条申请待审批",noticeMessage);   //发给终审人员
                    }
                    swMessageService.rejectSend(-1,-1,map,user.getUserID().toString(),"/forumLaunchApproval", null, null, user,"forumLaunchApproval");   //给上一个中间人"讨论发起申请"待审批-1
                    swMessageService.rejectSend(0,1,map,user.getUserID().toString(),"/forumLaunchApprove",null,null,user,"forumLaunchApproval");   //给上一个中间人“讨论发起申请”已批准+1
                }
            }
        } else {
            status = "2";
        }
        return status;
    }

    @Override
    public List<ForumPost> getApplyForum(Integer userID) {
        String hql = "FROM ForumPost  WHERE creator = :creator AND approveStatus = 1 order by updateDate desc";
        HashMap<String, Object> param = new HashMap<>();
        param.put("creator", userID);
        List<ForumPost> list = forumPostDao.getListByHQLWithNamedParams(hql,param);
        return list;
    }

    @Override
    public HashMap<String, Object> getApproveForum(Integer userID) {
        String hql = "SELECT new cn.sphd.miners.modules.forumArea.dto.ForumMetaDto(f.id,f.title,f.createName,f.createDate) FROM ForumPost f, ApprovalProcess ap WHERE f.id = ap.business AND ap.businessType = 43 AND ap.toUser = :toUser AND ap.approveStatus = :approveStatus AND ap.toMid is null";
        HashMap<String, Object> param = new HashMap<>();
        param.put("toUser", userID);
        param.put("approveStatus", "1");
        List<ForumPost> listForumByApply = forumPostDao.getListByHQLWithNamedParams(hql,param);
        hql = hql + " AND f.approveStatus = 1";
        param.put("approveStatus", "2");
        List<ForumPost> listForumByApprove = forumPostDao.getListByHQLWithNamedParams(hql,param);
        HashMap<String, Object> map = new HashMap<>();
        map.put("listForumByApply",listForumByApply);
        map.put("listForumByApprove",listForumByApprove);
        return map;
    }

    @Override
    public List<ForumPost> getLastForum(Integer oid) {
        String hql = "SELECT new cn.sphd.miners.modules.forumArea.dto.ForumMetaDto(f.id,f.title,f.createName,f.createDate) FROM ForumPost f, ApprovalProcess ap WHERE f.id = ap.business AND ap.businessType = 43 AND ap.toMid = :toMid AND ap.org = :fromOrg AND f.approveStatus = 1";
        HashMap<String, Object> param = new HashMap<>();
        param.put("toMid", "AuthApproval");
        param.put("fromOrg", oid);
        List<ForumPost> list = forumPostDao.getListByHQLWithNamedParams(hql,param);
        return list;
    }

    @Override
    public List<ForumPost> findForumByPage(HashMap param, String eventType, Integer fromUser) {
        String hql = null;
        if ("1".equals(eventType)) {
            hql = "FROM ForumPost  WHERE creator = :toUser AND approveStatus = :approveStatus AND createDate >= :timeBegin and createDate <= :timeEnd and category = 2 order by updateDate desc";
        }else {
            if ("2".equals(eventType)) {
                hql = "SELECT new cn.sphd.miners.modules.forumArea.dto.ForumMetaDto(f.id,f.title,f.createName,f.createDate) FROM ForumPost f, ApprovalProcess ap WHERE f.id = ap.business AND ap.businessType = 43 AND ap.toUser = :toUser AND ap.approveStatus = :approveStatus AND ap.toMid is null AND f.createDate >= :timeBegin and f.createDate <= :timeEnd";
            }else if ("3".equals(eventType)) {
                hql = "SELECT new cn.sphd.miners.modules.forumArea.dto.ForumMetaDto(f.id,f.title,f.createName,f.createDate) FROM ForumPost f, ApprovalProcess ap WHERE f.id = ap.business AND ap.businessType = 43 AND ap.toMid = :toMid AND ap.org = :fromOrg AND f.approveStatus = :approveStatus AND f.createDate >= :timeBegin and f.createDate <= :timeEnd";
            }
            if (fromUser != 0) {
                hql = hql + " and f.creator = :creator";
                param.put("creator", fromUser);
            }
            hql = hql + " order by f.updateDate desc";
        }
        List<ForumPost> list = forumPostDao.getListByHQLWithNamedParams(hql,param);
        return list;
    }

    @Override
    public List<ApprovalProcess> getlistApprovalProcess(Integer id, Integer bussinessType) {
        List<ApprovalProcess> listAp = approvalProcessService.getApprovalProcessByBusiness(id, bussinessType, "");
        return listAp;
    }

    @Override
    public ForumReplyAttachment forumReplyAttSingle(String path) {
        String hql = "from ForumReplyAttachment where path = :path";
        HashMap<String, Object> param = new HashMap<>();
        param.put("path", path);
        ForumReplyAttachment forumReplyAttachment = (ForumReplyAttachment) forumReplyAttachmentDao.getByHQLWithNamedParams(hql,param);
        return forumReplyAttachment;
    }

    @Override
    public Object[]   getReplyAttNumByType(Integer post) {
        String hql = "select count(case when replyType = 2 then 1 end) as a, count(case when replyType in (3,4) then 1 end) as b from ForumReply where post = :post and valid = 1";
        HashMap<String, Object> param = new HashMap<>();
        param.put("post", post);
        Object[] obj = (Object[]) forumReplyAttachmentDao.getByHQLWithNamedParams(hql, param);
        return obj;
    }

    @Override
    public void changeResVersionSendNewMes(Integer fileId,String name, String fileSn, Integer changeNum) {
        String hql = " from ForumReply where resourceId = :resourceId group by post order by createDate";
        HashMap<String, Object> paramReply = new HashMap<>();
        paramReply.put("resourceId", fileId);
        List<ForumReply> listReply = forumReplyDao.getListByHQLWithNamedParams(hql,paramReply);
        String content = "系统消息:<br/>上方的 \"" + fileSn +"/" + name + "\"已换至G" + changeNum + "版，请相关同事及时查阅。<br/>此文件的旧版本可通过点击\"历史版本\"查阅。<br/>特此告知！";
        if (!listReply.isEmpty()) {
            for (ForumReply f : listReply) {
                User createUser = userService.getUserByID(f.getCreator());
                HashMap<String, Object> hashMapFile = this.insertOneReply(createUser,f.getPost(),null,"",null,null,null,null,"3", "3", f.getAttachmentId(),f.getResourceId(),f.getResourceVersion(),f.getResourceHistory(), null, null);
                HashMap<String, Object> hashMapReply = this.insertOneReply(createUser,f.getPost(),null,content,null,null,null,null,"4",null,null,null,null,null, null,null);
                ForumReply forumReplyFile = (ForumReply) hashMapFile.get("addForumReply");
                ForumReply forumReply = (ForumReply) hashMapReply.get("addForumReply");
                ForumPost forumPost = (ForumPost) hashMapFile.get("forumPost");
                HashMap<String, Object> map = new HashMap<>();
                List<ForumReply> listReplyPush = new ArrayList<>();
                listReplyPush.add(forumReplyFile);
                listReplyPush.add(forumReply);
                map.put("addForumReply", listReplyPush);
                List<ForumPostUser> listPostUser = this.listPostUser(forumReply.getPost());
                this.pushNewReplyByType(listPostUser, "2", createUser.getUserID(), forumReply.getPost(), forumReply.getId() , null, null, null, map, forumPost, null, "1");
            }
        }
    }

    @Override
    public void changeAttVersionSend(User user, Integer replyId, String type, String path, String size, String module) {
        ForumReply fr = this.oneForumReply(replyId);
        ForumReplyAttachment forumReplyAttachment = this.getSingleByReplyAtt(fr.getAttachmentId());
        String hqlAttHis = " from ForumReplyAttHis where replyAttachment = :attachmentId order by createTime";
        HashMap<String, Object> paramReply = new HashMap<>();
        paramReply.put("attachmentId", fr.getAttachmentId());
        ForumReplyAttHis forumReplyAttHis = (ForumReplyAttHis) forumReplyAttHisDao.getByHQLWithNamedParams(hqlAttHis,paramReply);
        ForumReplyAttHis oldFrah = null;
        if (forumReplyAttHis == null) {
            User userCreator = userService.getUserByID(fr.getCreator());
            oldFrah = this.insertReplyAttHis(forumReplyAttachment, userCreator, module);
        }
        ForumUsing callBack = new ForumUsing(forumReplyAttachment.getId(), forumReplyAttachment.getClass());
        uploadService.delFileUsing(callBack,forumReplyAttachment.getPath(),user);
        forumReplyAttachment.setType(type);
        forumReplyAttachment.setSize(Integer.valueOf(size));
        forumReplyAttachment.setPath(path);
        forumReplyAttachment.setResourceVersion(forumReplyAttachment.getResourceVersion()+1);
        forumReplyAttachment.setUpdator(user.getUserID());
        forumReplyAttachment.setUpdateName(user.getUserName());
        Date now = new Date();
        forumReplyAttachment.setUpdateTime(now);
        uploadService.addFileUsing(callBack,path,forumReplyAttachment.getTitle(),user,module);
        this.insertReplyAttHis(forumReplyAttachment, user, module);
        String hql = " from ForumReply where attachmentId = :attachmentId group by post order by createDate";
        List<ForumReply> listReply = forumReplyDao.getListByHQLWithNamedParams(hql,paramReply);
        String content = "系统消息:<br/>上方的 \"" + forumReplyAttachment.getTitle() + "\"已换至G" + forumReplyAttachment.getResourceVersion() + "版，请相关同事及时查阅。<br/>此文件的旧版本可通过点击\"历史版本\"查阅。<br/>特此告知！";
        if (!listReply.isEmpty()) {
            for (ForumReply f : listReply) {
                User createUser;
                String isUploaded = null;
                if (f.getPost().equals(fr.getPost())) {
                    createUser = user;
                    isUploaded = "1";
                }else {
                    createUser  = userService.getUserByID(f.getCreator());
                }
                HashMap<String, Object> hashMapFile = this.insertOneReply(createUser,f.getPost(),null,"",null,null,null,null,"3", type, f.getAttachmentId(),null,f.getResourceVersion(),null, null,isUploaded);
                HashMap<String, Object> hashMapReply = this.insertOneReply(createUser,f.getPost(),null,content,null,null,null,null,"4",null,null,null,null,null, null, null);
                ForumReply forumReplyFile = (ForumReply) hashMapFile.get("addForumReply");
                ForumReply forumReply = (ForumReply) hashMapReply.get("addForumReply");
                ForumPost forumPost = (ForumPost) hashMapFile.get("forumPost");
                HashMap<String, Object> map = new HashMap<>();
                List<ForumReply> listReplyPush = new ArrayList<>();
                listReplyPush.add(forumReplyFile);
                listReplyPush.add(forumReply);
                map.put("addForumReply", listReplyPush);
                List<ForumPostUser> listPostUser = this.listPostUser(forumReply.getPost());
                this.pushNewReplyByType(listPostUser, "2", createUser.getUserID(), forumReply.getPost(), forumReply.getId() , null, null, null, map, forumPost, null, "1");
            }
        }
    }

    @Override
    public ForumReply addReplyForPub(User user, Integer postId, Integer reply, String content, String parentContent,
                                     String listforumReplyAttachment, String listUser, String forumAndroidId) {
        HashMap<String, Object> hashMap = this.insertOneReply(user, postId, reply, content, parentContent, listforumReplyAttachment, listUser, forumAndroidId,"1",null,null,null,null,null,null,null);
        ForumReply forumReply = (ForumReply) hashMap.get("addForumReply");
        Integer parentCreator = (Integer) hashMap.get("parentCreator");
        ForumPost forumPost = (ForumPost) hashMap.get("forumPost");
        HashMap<String, Object> map = new HashMap<>();
        List<ForumReply> listReply = new ArrayList<>();
        listReply.add(forumReply);
        map.put("addForumReply", listReply);
        List<ForumPostUser> listPostUser = this.listPostUser(postId);
        this.pushNewReplyByType(listPostUser, "2", user.getUserID(), postId, forumReply.getId() , reply, parentCreator, listUser, map, forumPost,forumAndroidId,null);
        return forumReply;
    }

    /**
     * @param listUser      回复的人 为null的时候就不会去处理 先手机端的传没有的时候就不传是null PC端传没有的时候是[]
     * @param forumAndroidId      设备id 安卓在用 不传就不会处理
     * @param type          1-正常发消息 2-转发 3-换版
     * @param attType       1-图片 2-文件 3-内部文件
     * @param attId         type=3 换版时使用
     * @param resId         type=3&&attType=3 换版且是内部文件时使用
     * @param resVersion    换版次数 type=3 换版时使用
     * @param resHisId      文件历史id type=3 换版内部文件时使用
     * @param replyIdByAtt  type=2 转发时使用
     * @param isUploaded    type=3 换版时使用
     * @return
     */
    @Override
    public HashMap insertOneReply(User user, Integer postId, Integer reply, String content,
                                  String parentContent, String listforumReplyAttachment,
                                  String listUser, String forumAndroidId, String type, String attType,
                                  Integer attId, Integer resId, Integer resVersion ,Integer resHisId,
                                  Integer replyIdByAtt, String isUploaded) {
        Integer userID = user.getUserID();
        ForumReply forumReply = new ForumReply();
        forumReply.setPost(postId);
        ForumReply forumReplyParent = null;
        Integer parentCreator = null;
        if(reply != null){
            forumReply.setParent(reply);
            if(parentContent != null){
                forumReply.setParentContent(parentContent);
            }
            //给父亲的回复次数加1，用在删除时不被允许
            forumReplyParent = this.oneForumReply(reply);
            forumReplyParent.setReplyNum(forumReplyParent.getReplyNum() + 1);
            parentCreator = forumReplyParent.getCreator();
        }
        if(content != null){
            forumReply.setContent(content);
            forumReply.setReplyType(1);
        } else {
            forumReply.setContent("");
        }
        forumReply.setOrg(user.getOid());
        forumReply.setCreator(userID);
        forumReply.setCreateName(user.getUserName());
        Date now = new Date();
        forumReply.setCreateDate(now);
        forumReply.setValid(1);
        forumReply.setReplyNum(0);
        forumReply.setMsgType("1");
        this.insertForumReply(forumReply);
        Integer replyId = forumReply.getId();
        //新增附件
        List<ForumReplyAttachment> listNewAtt = new ArrayList<>();
        if ("1".equals(type)) {
            if (listforumReplyAttachment != null) {
                List<ForumReplyAttachment> listReplyAtt = JSON.parseArray(listforumReplyAttachment, ForumReplyAttachment.class);
                for (ForumReplyAttachment fra : listReplyAtt) {
                    fra.setReply(replyId);
                    fra.setOrg(user.getOid());
                    fra.setCreator(userID);
                    fra.setCreateName(user.getUserName());
                    fra.setCreateTime(now);
                    fra.setLocation("1");
                    ForumReplyAttachment newFa = new ForumReplyAttachment();
                    if (fra.getResourceId() == null) {
                        fra.setResourceVersion(0);
                        forumReplyAttachmentDao.save(fra);
                        forumReply.setResourceVersion(0);
                        forumReply.setAttachmentId(fra.getId());
                        forumReply.setIsUploaded(new Byte("1"));
                        ForumUsing callback = new ForumUsing(fra.getId(), fra.getClass());
                        uploadService.addFileUsing(callback,fra.getPath(),fra.getTitle(),user,fra.getModule());
                        this.addImportUserMessage(forumReply,fra,newFa);
                    } else {
                        forumReply.setResourceId(fra.getResourceId());
                        forumReply.setResourceVersion(fra.getResourceVersion());
                        Integer resHistoryId = resService.getNewFileHisId(fra.getResourceId());
                        forumReply.setResourceHistory(resHistoryId);
                        ForumReplyAttachment fReplyAtt = this.getForumReplyAttachmentByResId(fra.getResourceId());
                        if (fReplyAtt == null) {
                            forumReplyAttachmentDao.save(fra);
                            forumReply.setAttachmentId(fra.getId());
                            this.addImportUserMessage(forumReply,fra,newFa);
                        }else {
                            forumReply.setAttachmentId(fReplyAtt.getId());
                            this.addImportUserMessage(forumReply,fReplyAtt,newFa);
                        }
                    }
                    if ("1".equals(fra.getType())) {
                        forumReply.setReplyType(2);
                    } else if ("2".equals(fra.getType())){
                        forumReply.setReplyType(3);
                    }else if ("3".equals(fra.getType())){
                        forumReply.setReplyType(4);
                    }
                    listNewAtt.add(newFa);
                }
                forumReply.setForumReplyAttachmentHashSet(listNewAtt);
            }
        } else if ("2".equals(type)) {
            ForumReply forumReplyAtt = this.oneForumReply(replyIdByAtt);
            ForumReplyAttachment fra = this.getSingleByReplyAtt(forumReplyAtt.getAttachmentId());
            if (forumReplyAtt.getResourceId() != null) {
                ResEntity res = resService.getSingle(forumReplyAtt.getResourceId());
                Integer resHistoryId = resService.getNewFileHisId(forumReplyAtt.getResourceId());
                forumReply.setResourceId(res.getId());
                forumReply.setResourceVersion(res.getChangeNum());
                forumReply.setResourceHistory(resHistoryId);
            } else {
                forumReply.setResourceVersion(fra.getResourceVersion());
            }
            forumReply.setIsRevised(new Byte("1"));
            forumReply.setAttachmentId(forumReplyAtt.getAttachmentId());
            ForumReplyAttachment newFa = new ForumReplyAttachment();
            this.addImportUserMessage(forumReply,fra,newFa);
            if ("1".equals(fra.getType())) {
                forumReply.setReplyType(2);
            } else if ("2".equals(fra.getType())){
                forumReply.setReplyType(3);
            }else if ("3".equals(fra.getType())){
                forumReply.setReplyType(4);
            }
            listNewAtt.add(newFa);
            forumReply.setForumReplyAttachmentHashSet(listNewAtt);
        } else if ("3".equals(type)) {
            forumReply.setAttachmentId(attId);
            if ("3".equals(attType)) {
                forumReply.setResourceId(resId);
                forumReply.setResourceHistory(resHisId);
                forumReply.setReplyType(4);
            } else {
                if ("1".equals(attType)) {
                    forumReply.setReplyType(2);
                } else {
                    forumReply.setReplyType(3);
                }
                if (isUploaded != null) {
                    forumReply.setIsUploaded(new Byte(isUploaded));
                }
            }
            forumReply.setIsRevised(new Byte("1"));
            forumReply.setResourceVersion(resVersion);
            ForumReplyAttachment forumReplyAttachment = this.getSingleByReplyAtt(attId);
            ForumReplyAttachment newFa = new ForumReplyAttachment();
            this.addImportUserMessage(forumReply, forumReplyAttachment,newFa);
            listNewAtt.add(newFa);
            forumReply.setForumReplyAttachmentHashSet(listNewAtt);
        } else if ("4".equals(type)) {
            forumReply.setIsRevised(new Byte("1"));
        }
        HashMap<String, Object> map = new HashMap<>();
        forumReply.setPostUserMussage(this.backUser(user));
        map.put("addForumReply", forumReply);
        //修改最新回复时间
        ForumPost forumPost = this.forumPostMessageByhead(postId);
        this.upPostStatus(forumPost, null, null, null, null, null, forumReply.getCreateDate());
        map.put("parentCreator", parentCreator);
        map.put("forumPost", forumPost);
        return map;
    }

    @Override
    public ForumReplyAttHis getSingleByReplyAttHis(Integer id) {
        ForumReplyAttHis forumReplyAttHis = forumReplyAttHisDao.get(id);
        return forumReplyAttHis;
    }

    @Override
    public List<ForumReplyAttHis> listReplyAttHis(Integer replyId, PageInfo pageInfo) {
        ForumReply forumReply = this.oneForumReply(replyId);
        ForumReplyAttachment forumReplyAttachment = this.getSingleByReplyAtt(forumReply.getAttachmentId());
        String hql = "from ForumReplyAttHis where replyAttachment = :replyAttachment and resourceVersion != :resourceVersion order by id desc";
        HashMap<String, Object> param = new HashMap<>();
        param.put("replyAttachment", forumReply.getAttachmentId());
        param.put("resourceVersion", forumReplyAttachment.getResourceVersion());
        List<ForumReplyAttHis> list = forumReplyAttHisDao.getListByHQLWithNamedParams(hql,param,pageInfo);
        return list;
    }

    @Override
    public void changeResFileIntoAtt(Integer fileId, String path, String fileName, String version, Integer size) {
        ForumReplyAttachment forumReplyAttachment = this.getForumReplyAttachmentByResId(fileId);
        if (forumReplyAttachment != null) {
            if (version.equals("jpg") || version.equals("png")) {
                forumReplyAttachment.setType("1");
            }else {
                forumReplyAttachment.setType("2");
            }
            forumReplyAttachment.setTitle(fileName + "." + version);
            forumReplyAttachment.setPath(path);
            forumReplyAttachment.setPlace("1");
            forumReplyAttachment.setSize(size);
            forumReplyAttachment.setResourceId(null);
            forumReplyAttachment.setResourceVersion(0);
            forumReplyAttachment.setResourceHistory(null);
            User userCreator = userService.getUserByID(forumReplyAttachment.getCreator());
            ForumUsing callback = new ForumUsing(forumReplyAttachment.getId(), forumReplyAttachment.getClass());
            uploadService.addFileUsing(callback,forumReplyAttachment.getPath(),forumReplyAttachment.getTitle(),userCreator,"事务讨论");
            String hql = "from ForumReply where attachmentId = :attachmentId and resourceId = :resourceId order by id";
            HashMap<String, Object> param = new HashMap<>();
            param.put("attachmentId", forumReplyAttachment.getId());
            param.put("resourceId", fileId);
            List<ForumReply> listReply = forumReplyDao.getListByHQLWithNamedParams(hql,param);
            int tag = 0;
            for (ForumReply fp : listReply) {
                if (tag == 0) {
                    fp.setIsUploaded(Byte.valueOf("1"));
                    tag++;
                }
                fp.setResourceId(null);
                fp.setResourceVersion(0);
                fp.setResourceHistory(null);
                if ("1".equals(forumReplyAttachment.getType())) {
                    fp.setReplyType(2);
                } else {
                    fp.setReplyType(3);
                }
            }
        }
    }

    @Override
    public List<ForumPost> getManagerPostList(Integer userID) {
        User user = userService.getUserByID(userID);
        String hql = "FROM ForumPost WHERE valid = 0 AND enabled = 0 AND isCleaned = 1";
        HashMap<String, Object> param = new HashMap<>();
        if (userService.isSuper(user)) {
            hql = hql + " AND org = :oid";
            param.put("oid", user.getOid());
        } else {
            hql = hql + " AND compere = :userID";
            param.put("userID", userID);
        }
        hql = hql + " order by validTime";
        List<ForumPost> list = forumPostDao.getListByHQLWithNamedParams(hql,param);
        Date date = new Date();
        if (!list.isEmpty())
            for (ForumPost fp : list){
                fp.setExpriationTime("将于"+ TimeUtils.toTimeString(fp.getCleanTime().getTime()-date.getTime(),3) +"钟后消失");
            }
        return list;
    }

    private Integer getManagerPostListNum(User user){
        String hql = "SELECT count(id) FROM ForumPost WHERE valid = 0 AND enabled = 0 AND isCleaned = 1";
        HashMap<String, Object> param = new HashMap<>();
        if (userService.isSuper(user)) {
            hql = hql + " AND org = :oid";
            param.put("oid", user.getOid());
        } else {
            hql = hql + " AND compere = :userID";
            param.put("userID", user.getUserID());
        }
        Long num = (Long) forumPostDao.getByHQLWithNamedParams(hql,param);
        return num.intValue();
    }

    //获取讨论_回复用户统计表（t_forum_reply_user_stat）中某人未读的消息
    private List<ForumReplyUserStat> listReplyUserStat(Integer postId, Integer userID){
        String hqlParentReplyUser = "from ForumReplyUserStat where post = :post and userID = :userID and read_mum = 0";
        HashMap<String, Object> paramParentReply = new HashMap<>();
        paramParentReply.put("post", postId);
        paramParentReply.put("userID", userID);
        List<ForumReplyUserStat> listReplyUserstate = forumReplyUserStatDao.getListByHQLWithNamedParams(hqlParentReplyUser, paramParentReply);
        return listReplyUserstate;
    }

    //公用方法，给forumPost填入未读数、是否@和回复标记等信息
    private void setMesForpost(Integer userID, ForumPost fp){
        String hql = "select count(id) as mesNum, count(atMum) as atTag, count(memo) as replyTag from ForumRepyUser where post = :post and userID = :userID";
        HashMap<String, Object> paramUser = new HashMap<>();
        paramUser.put("post", fp.getId());
        paramUser.put("userID", userID);
        Object[] obj = (Object[]) forumReplyUserDao.getByHQLWithNamedParams(hql, paramUser);
        //讨论的未读数
        fp.setMessageNum(Integer.valueOf(obj[0].toString()));
        //标记@
        Integer atTag = Integer.valueOf(obj[1].toString()) >0 ? 1:0;
        fp.setAtTag(atTag);
        //标记回复
        Integer replyTag = Integer.valueOf(obj[2].toString()) >0 ? 1:0;
        fp.setReplyTag(replyTag);

    }

    //循环发给中间审批人的已批准
    private void sendApprovedForumPost(Integer postId, HashMap map, Integer processId){
        List<ApprovalProcess> listAp = approvalProcessService.getApprovalProcessByBusiness(postId, 43, "");
        String toMid= listAp.get(listAp.size()-1).getToMid();
        if("AuthApproval".equals(toMid)){
            listAp.remove(listAp.size()-1);
        }
        if (!listAp.isEmpty()) {
            for(ApprovalProcess ap : listAp){
                if (!ap.getId().equals(processId)){
                    User user = userService.getUserByID(ap.getToUser());
                    swMessageService.rejectSend(0,-1,map,ap.getToUser().toString(),"/forumLaunchApprove",null,null,user,"forumLaunchApproval");
                }
            }
        }
    }

    //存一些系统内文件的东西和其它或略的字段
    private void addImportUserMessage(ForumReply forumReply, ForumReplyAttachment fa, ForumReplyAttachment newFa) {
        newFa.setId(fa.getId());
        newFa.setTitle(fa.getTitle());
        newFa.setType(fa.getType());
        newFa.setPath(fa.getPath());
        newFa.setPlace(fa.getPlace());
        newFa.setLocation(fa.getLocation());
        newFa.setSize(fa.getSize());
        newFa.setCreator(fa.getCreator());
        newFa.setCreateName(fa.getCreateName());
        newFa.setCreateTime(fa.getCreateTime());
        newFa.setUpdator(fa.getUpdator());
        newFa.setUpdateName(fa.getUpdateName());
        newFa.setUpdateTime(fa.getUpdateTime());
        newFa.setResourceVersion(fa.getResourceVersion());
        if (forumReply.getAttachmentId() != null) {
            newFa.setImportVerson(forumReply.getResourceVersion());
            newFa.setIsUploaded(new Byte("0"));
            if (forumReply.getResourceId() != null) {
                ResHistory history = new ResHistory();
                history.setId(forumReply.getResourceHistory());
                ResHistory resHis = resService.gethisSingle(history);
                if (resHis.getChangeNum() > 0) {
                    newFa.setResourceImportName(resHis.getUpdateName());
                    newFa.setResourceImportDate(NewDateUtils.dateFromString(resHis.getUpdateDate(), "yyyy-MM-dd HH:mm:ss"));
                } else {
                    newFa.setResourceImportName(resHis.getCreateName());
                    newFa.setResourceImportDate(NewDateUtils.dateFromString(resHis.getCreateDate(), "yyyy-MM-dd HH:mm:ss"));
                }
                ResEntity resEntity = resService.getSingle(forumReply.getResourceId());
                newFa.setResEntity(resEntity);
            } else {
                Byte isUploaded = forumReply.getIsUploaded();
                if (isUploaded != null) {
                    newFa.setIsUploaded(isUploaded);
                } else {
                    newFa.setIsUploaded(new Byte("0"));
                }
                if (forumReply.getResourceVersion() > 0) {
                    String hql = " from ForumReplyAttHis where replyAttachment = :attachmentId and resourceVersion = :resourceVersion";
                    HashMap<String, Object> param = new HashMap<>();
                    param.put("attachmentId", fa.getId());
                    param.put("resourceVersion", forumReply.getResourceVersion());
                    ForumReplyAttHis forumReplyAttHis = (ForumReplyAttHis) forumReplyAttHisDao.getByHQLWithNamedParams(hql, param);
                    newFa.setResourceImportName(forumReplyAttHis.getUpdateName());
                    newFa.setResourceImportDate(forumReplyAttHis.getUpdateTime());
                } else {
                    newFa.setResourceImportName(fa.getCreateName());
                    newFa.setResourceImportDate(fa.getCreateTime());
                }
            }
        }
    }

    //新增外部文件的历史
    private ForumReplyAttHis insertReplyAttHis(ForumReplyAttachment fra, User user, String module){
        ForumReplyAttHis frah = new ForumReplyAttHis();
        frah.setReplyAttachment(fra.getId());
        frah.setReply(fra.getReply());
        frah.setTitle(fra.getTitle());
        frah.setType(fra.getType());
        frah.setPath(fra.getPath());
        frah.setSize(fra.getSize());
        frah.setPlace(fra.getPlace());
        frah.setLocation(fra.getLocation());
        frah.setResourceVersion(fra.getResourceVersion());
        frah.setOrg(fra.getOrg());
        frah.setCreator(fra.getCreator());
        frah.setCreateName(fra.getCreateName());
        frah.setCreateTime(fra.getCreateTime());
        if (fra.getUpdator() != null) {
            frah.setUpdator(fra.getUpdator());
            frah.setUpdateName(fra.getUpdateName());
            frah.setUpdateTime(fra.getUpdateTime());
        }
        forumReplyAttHisDao.saveOrUpdate(frah);
        ForumUsing callback = new ForumUsing(frah.getId(), frah.getClass());
        uploadService.addFileUsing(callback,frah.getPath(),frah.getTitle(),user,module);
        return frah;
    }

    //根据resId获取文件实体
    private ForumReplyAttachment getForumReplyAttachmentByResId(Integer resId){
        String hql = "from ForumReplyAttachment where resourceId = :resourceId order by id";
        HashMap<String, Object> paramReply = new HashMap<>();
        paramReply.put("resourceId", resId);
        ForumReplyAttachment fReplyAtt = (ForumReplyAttachment) forumReplyAttachmentDao.getByHQLWithNamedParams(hql,paramReply);
        return fReplyAtt;
    }

    /*@Override
    public void testForumReply() {
        String hql = "SELECT new cn.sphd.miners.modules.forumArea.dto.AttachmentReplyDto(fr.post, fa.id, fa.creator, fa.type) FROM ForumReplyAttachment fa, ForumReply fr WHERE fa.reply = fr.id AND fa.reply in ( SELECT a.reply FROM ForumReplyAttachment a , ForumReply f where a.reply = f.id  GROUP BY a.reply HAVING COUNT(a.reply) > 1)";
        List<AttachmentReplyDto> list = forumReplyAttachmentDao.getListByHQLWithNamedParams(hql,null);
    }*/


}
