package cn.sphd.miners.modules.generalAffairs.controller;

import cn.sphd.miners.common.controller.BaseController;
import cn.sphd.miners.common.initializer.AuthPassport;
import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.*;
import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.auth.service.AuthService;
import cn.sphd.miners.modules.generalAffairs.entity.*;
import cn.sphd.miners.modules.generalAffairs.service.PersonalService;
import cn.sphd.miners.modules.generalAffairs.service.UserLockService;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceOldService;
import cn.sphd.miners.modules.personal.entity.PersonnelContract;
import cn.sphd.miners.modules.personal.entity.PersonnelFolks;
import cn.sphd.miners.modules.personal.service.PersonnelContractService;
import cn.sphd.miners.modules.recruit.entity.RdPost;
import cn.sphd.miners.modules.recruit.service.RdPostService;
import cn.sphd.miners.modules.sales.service.PdCustomerService;
import cn.sphd.miners.modules.sms.service.SendMessageService;
import cn.sphd.miners.modules.sms.service.SmsService;
import cn.sphd.miners.modules.sms.service.TemplateService;
import cn.sphd.miners.modules.system.entity.*;
import cn.sphd.miners.modules.system.service.*;
import cn.sphd.miners.modules.system.service.impl.UserHistoryUsing;
import cn.sphd.miners.modules.system.service.impl.UserUsing;
import cn.sphd.miners.modules.uploads.entity.UploadFile;
import cn.sphd.miners.modules.uploads.service.UploadService;
import com.alibaba.fastjson.JSON;
import io.netty.util.internal.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.ParseException;
//import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by Administrator on 2016/8/12.
 */
@Controller
@RequestMapping("/general")
public class GeneralController extends BaseController {

    @Autowired
    UserService userService;
    @Autowired
    UserLockService userLockService;
    @Autowired
    PersonalService personalService;
    @Autowired
    OrgService orgService;
    @Autowired
    UserLogService userLogService;
    @Autowired
    RoleService roleService;
    @Autowired
    ApprovalService approvalService;
    @Autowired
    UserPopedomService userPopedomService;
    @Autowired
    PopedomService popedomService;
    @Autowired
    UserRoleService userRoleService;
//    @Autowired
//    SalaryAction salaryAction;
    @Autowired
WorkAttendanceOldService workAttendanceOldService;
    @Autowired
    SendMessageService sendMessageService;
    @Autowired
    PopedomTaskService popedomTaskService;
    @Autowired
    PdCustomerService customerService;
    @Autowired
    UserHistoryService userHistoryService;
    @Autowired
    UploadService uploadService;
    @Autowired
    AuthService authService;
    @Autowired
    RdPostService rdPostService;
    @Autowired
    RoleTmplService roleTmplService;
    @Autowired
    SmsService smsService;
    @Autowired
    PersonnelContractService personnelContractService;

    public GeneralController() {
//        关闭父类参数自动转换
        setInitBinderConfig(InitBinderConfig.DisableXSSDefence, InitBinderConfig.DisableDateChange);
    }

    /*岗位管理*/
    @RequestMapping("/postManagement.do")
    public String postManagement(User user, Model model) {
//        Integer oid = user.getOid();
//        List<Organization> organizationList = orgService.checkDepartmentByPId(oid, 2);
//        List<Organization> organizations = orgService.checkDepartmentByPId(oid, 3);
//        for (Organization o : organizations) {
//            List<User> users = userService.getUserListByPostId(o.getId());
//            o.setAllUser(users.size());
//        }
//        model.addAttribute("organizationList", organizationList);
//        model.addAttribute("organizations", organizations);
        return "/generalAffairs/postManagement";
    }

    /*岗位管理异步子机构查询*/
    @ResponseBody
    @RequestMapping("/querySubsystem.do")
    public String querySubsystem(Integer id, HttpServletResponse httpServletResponse) throws IOException {
        String str = "";
        List<Organization> organizationsList = orgService.getAllBranchById(id);
        if (organizationsList.size() > 0) {
            for (Organization org : organizationsList) {
                str = str + org.getId();
                str = str + "+";
                str = str + org.getName();
                str = str + "+";
            }
            String strNew = str.substring(0, str.length() - 1);
            httpServletResponse.setCharacterEncoding("UTF-8");  // 加上此处可解决页面js显示乱码问题
            httpServletResponse.getWriter().write(strNew);
            return null;
        } else {
            return "cuowu";
        }
    }


    /**
     * <AUTHOR>
     * @date 2017/12/6 9:45
     * 职位列表更新提交
     */
    @ResponseBody
    @RequestMapping("/positionEditSubmit.do")
    public void positionEditSubmit(Integer positionId, String positionName, User user, HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<>();
        if (positionId != null && positionName != null && !"".equals(positionName)) {
            Organization organization = orgService.getOrgByOid(positionId, OrgService.OrgType.post);
            organization.setName(positionName);
            orgService.updateOrg(organization);
            Integer oid = user.getOid();

            List<User> users = userService.getUserByOid(oid, null);
            for (User u : users) {
                if (u.getPostID() != null && !"".equals(u.getPostID())) {
                    if (Integer.decode(u.getPostID()).equals(organization.getId())) {
                        u.setPostName(organization.getName());//变更每个在该职位下员工的职位
                        userService.updateUser(u);
                    }
                }
            }
            map.put("status", 1);  //修改成功
        } else {
            map.put("status", 0);  //修改失败
        }
        ObjectToJson.objectToJson1(map, new String[]{}, response);
    }

    //校验职位下是否有人
    @RequestMapping("/checkPost.do")
    @ResponseBody
    public void checkPost(User user, Integer positionId, HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<>();
        if (positionId != null && !"".equals(positionId)) {
            Integer oid = user.getOid();
            List<User> userList = userService.getUserByOidAndPostId(oid, positionId);
            if (userList.size() > 0) {
                map.put("status", 1); //有人，不删
            } else {
                map.put("status", 0); //删除
            }
        }
        ObjectToJson.objectToJson1(map, new String[]{}, response);
    }


    /**
     * <AUTHOR>
     * @date 2017/12/5 18:54
     * 职位列表的删除提交
     */
    @ResponseBody
    @RequestMapping("/positionDeleteSubmit.do")
    public void positionDeleteSubmit(Integer positionId, HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<>();
        if (positionId != null) {
            Organization organization = orgService.getOrgByOid(positionId, OrgService.OrgType.post);
            orgService.deletePost(organization);
            map.put("state", 1);  //删除成功
        } else {
            map.put("state", 0);  //删除失败
        }
        ObjectToJson.objectToJson1(map, new String[]{}, response);
    }

    /**
     * <AUTHOR>
     * @date 2017/12/6 9:49
     * 新增职位提交
     */
    @ResponseBody
    @RequestMapping("/addNewPositionSubmit.do")
    public void addNewPositionSubmit(String positionName, User user, HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<>();
        if (positionName != null && !"".equals(positionName)) {
            Integer oid = user.getOid();
            Organization organization = new Organization();
            organization.setOrgType(3);
            organization.setName(positionName);
            organization.setUserNum(0);
            organization.setChildNum(0);
            organization.setDisabledChildNum(0);
            organization.setEstablishDate(NewDateUtils.today(new Date()));
            organization.setEstablishDate(NewDateUtils.today(new Date()));
            organization.setCreator(user.getUserID());
            organization.setCreateName(user.getUserName());
            organization.setCreateDate(new Date());
            organization.setPid(oid);
            orgService.saveOrg(organization);
            map.put("state", 1);  //删除成功
        } else {
            map.put("state", 0);  //删除失败
        }
        ObjectToJson.objectToJson1(map, new String[]{}, response);
    }

    // wyu：登录记录的页面
    @RequestMapping("/userLog.do")
    public String userLog(User user, Model model, User selectUser) {
        Organization organization = user.getOrganization();
        List<User> users = userService.getAllUsersByOrganization(organization.getId(), selectUser);
        Map<User, UserLog> map = new LinkedHashMap<User, UserLog>();
        for (User u : users) {
            UserLog userLog=userLogService.getLastUserLogByUserId(u.getUserID());
            if (userLog!= null) {//说明有此用户的操作记录，获取最新的
                map.put(u, userLog);
            }
        }
        List<Organization> organizations = orgService.getAllDepartmentById(organization.getId());//获取所有部门
        List<Organization> organizationList = orgService.checkDepartmentByPId(organization.getId(), 3);//该机构下所有职位
        model.addAttribute("map", map);
        model.addAttribute("organizations", organizations);
        model.addAttribute("organizationList", organizationList);
        return "/generalAffairs/loginRecord";
    }

    /**
     * <AUTHOR>
     * @date 2017/10/30 9:38
     * 返回职工档案的页面
     */
    @RequestMapping("/employeeIndex.do")
    public String employeeIndex(User user, Model model) {
        Integer oid = user.getOid();
        Organization o=orgService.getByOid(oid,true,false);
        List<Organization> organizations = orgService.checkDepartmentByPId(oid, 2);//3为职位, 2为部门
//        List<Organization> organization = orgService.checkDepartmentByPId(oid, 3);//3为职位, 2为部门
        List<RdPost> rdPostList=rdPostService.getRdPostsByOid(oid,true);// 新版岗位 2023/5/25
        model.addAttribute("departmentList", organizations);
        model.addAttribute("postList", rdPostList);
        if (o.getInitState().equals("0")) {//没正式启用
            // 因1.126时需求需要，批量导入未完成状态时的跳转，改为点击批量导入后，所以，将相关跳转需要
//            List<User> userList=userService.getUserListByOidRoleCode(oid,"staff",null);
//            if (userList.size()>0){
//                model.addAttribute("importState", 1);//导入状态  1- 已导入，0 未导入
//            }else {
//                model.addAttribute("importState", 0);//导入状态  1- 已导入，0 未导入
//            }
            return "/generalAffairs/employeeImportBefore";
        }else {
            return "/generalAffairs/employeeIndex";//正式使用
        }
    }


    /**
     * <AUTHOR>
     * @date 2017/10/31 17:14
     * 职工档案的筛选功能
     * edus(学历，以逗号隔开拼接成字符串String类型) gender(性别  1-男 0-女) departments(部门，以逗号隔开拼接成字符串String类型) posts(职位，以逗号隔开拼接成字符串String类型) marry(0-已婚 1-未婚)
     * birthday（出生日期） onDutyDate(入职时间)   isDuty 是否在职  1表示在职，2表示离职
     * quantum 每页多少条   pageNumber 当前页
     */
    @ResponseBody
    @RequestMapping("/screen.do")
    public void screen(String isDuty, String edus, String gender, String departments, String posts, String marry, String birthday1, String birthday2, String onDutyDate1, String onDutyDate2, User user, Integer quantum, Integer pageNumber, HttpServletResponse response,String oids) throws IOException, ParseException {
        Map<String, Object> map = new HashMap<>();
        if (oids==null){
            oids = user.getOid().toString();
        }
        List<User> users = userService.screenUsers(edus, gender, departments, posts, marry, birthday1, birthday2, onDutyDate1, onDutyDate2, isDuty,oids);

        /**分页*/
        List list = new ArrayList<>();
        int total = 0;
        if (users.size() > 0) {
            total = users.size();
        }
        int totalPage;
        if (total != 0 && quantum != null) {
            if (total % quantum == 0) {
                totalPage = total / quantum;
            } else {
                totalPage = total / quantum + 1;
            }
        } else {
            totalPage = 1;
        }
        if (pageNumber != null && quantum != null && total != 0) {
            int max = pageNumber * quantum;
            int min = pageNumber * quantum - quantum;
            for (int i = min; i < max; i++) {
                if (i < total) {
                    String department = users.get(i).getDepartment();  //部门id
                    if (!"".equals(department) && department != null) {
                        Organization o = orgService.getOrgByOid(Integer.parseInt(department), OrgService.OrgType.department);
                        if (o != null) {
                            users.get(i).setDepartName(o.getName());
                        } else {
                            users.get(i).setDepartName("");
                        }
                    }
                    users.get(i).setOrgName(users.get(i).getOrganization().getName());
                    String postId = users.get(i).getPostID();  //职位id
                    if (!"".equals(postId) && postId != null) {
                        RdPost o2 = rdPostService.getRdPostById(Long.valueOf(postId));
                        if (o2 != null) {
                            users.get(i).setPostName(o2.getName());
                        } else {
                            users.get(i).setPostName("");
                        }
                    }
                    String leader = users.get(i).getLeader();  //直接上级
                    if ("super".equals(users.get(i).getRoleCode())||"staff".equals(users.get(i).getRoleCode())||"agent".equals(users.get(i).getRoleCode())||"smallSuper".equals(users.get(i).getRoleCode())) { //2020/9/20  1.125多重身份 lixu改   2020/10/30 1.128在此要求 代理要展示 lixu改
                        list.add(users.get(i));// 只有员工 和 董事长被列在 职工档案中
                    }
                }
            }
        }
        map.put("users", list);
        map.put("pageNumber", pageNumber);  //当前页
        map.put("totalPage", totalPage);  //总页数
        ObjectToJson.objectToJson1(map, new String[]{"parent", "userMessages", "personnelOccupations", "personalEducations", "personnelSalaryLogUser",
                "personalRewardPunishments", "personalAssessments", "personnelLeaveUser", "personnelOvertimeUser", "userLogs",
                "personnelReimburseHashSet", "userPopedomHashSet", "roles", "organization", "submit", "volume", "lv", "money", "submitM",
                "volumeM", "lvM", "moneym", "submitY", "volumeY", "lvY", "moneyY", "userLogType", "userLogOperateTime", "userLogIp", "flag",
                "roleflag", "password", "handleFlag", "inputStream", "collegeName1", "ebeginTime1", "eendTime1", "major1", "degree1", "ememo1",
                "collegeName2", "ebeginTime2", "eendTime2", "major2", "degree2", "ememo2", "collegeName3", "ebeginTime3", "eendTime3", "major3",
                "degree3", "ememo3", "occurDate1", "ocontent1", "omemo1", "ooperator1", "ooperatorName1", "occurDate2", "ocontent2", "omemo2",
                "ooperator2", "ooperatorName2", "occurDate3", "ocontent3", "omemo3", "ooperator3", "ooperatorName3", "corpName1", "beginTime1",
                "endTime1", "post1", "csalary1", "dmemo1", "operatingDuty1", "corpName2", "beginTime2", "endTime2", "post2", "csalary2", "dmemo2",
                "operatingDuty2", "corpName3", "beginTime3", "endTime3", "post3", "csalary3", "dmemo3", "operatingDuty3", "assessDate1", "assessUser1",
                "type1", "content1", "amemo1", "assessUserName1", "assessDate2", "assessUser2", "type2", "content2", "amemo2", "assessUserName2",
                "assessDate3", "assessUser3", "type3", "content3", "amemo3", "assessUserName3", "salary1", "adjustDate1", "admustResaon1",
                "operateTime1", "operator1", "operatorName1", "smemo1", "salary2", "adjustDate2", "admustResaon2", "operateTime2", "operator2",
                "operatorName2", "smemo2", "salary3", "adjustDate3", "admustResaon3", "operateTime3", "operator3", "operatorName3", "smemo3"}, response);
    }

    /**
     * <AUTHOR>
     * @date 2017/10/31 10:25
     * 打开办理入职页面(获取直属上级的接口)
     */
    @RequestMapping("/handleEntry.do")
    public String handleEntry(User user, Model model) throws IOException {
        Integer oid = user.getOid();
        List<Organization> organizations = orgService.checkDepartmentByPId(oid, 2);//3为职位, 2为部门
        model.addAttribute("departmentList", organizations);
        List<Organization> organization = orgService.checkDepartmentByPId(oid, 3);//3为职位, 2为部门
        model.addAttribute("postList", organization);

        List<User> userList = new ArrayList<User>();
        List<User> users = userService.getUserByOid(oid, null);
        for (User u : users) {
            if ((u.getOrdinaryEmployees() == null || u.getOrdinaryEmployees() == 0) && "1".equals(u.getIsDuty())&&u.getRankUrl()!=null&&!u.getRankUrl().equals("")) {
                userList.add(u);
            }
        }
        User pUser = userService.getUserByRoleCode(oid, "general");//机构总务
        User chuanguan=userService.getUserByRoleCode(oid,"smallSuper");
        if(chuanguan==null){
            chuanguan=userService.getUserByRoleCode(oid,"super");
        }
        List<User> users1 = userService.getManages(oid);  //高管列表
        for (User u : users1) {
            if (!"".equals(u.getRoleCode())) {
                if ("general".equals(u.getRoleCode())) {
                    u.setManageName("总务");
                } else if ("finance".equals(u.getRoleCode())) {
                    u.setManageName("财务");
                } else if ("sale".equals(u.getRoleCode())) {
                    u.setManageName("销售");
                } else if ("accounting".equals(u.getRoleCode())) {
                    u.setManageName("会计");
                }
            }
        }

        model.addAttribute("user", user);
        model.addAttribute("pUser", pUser);
        model.addAttribute("chaoguan", chuanguan);
        model.addAttribute("userList", userList);//直属上级列表
        model.addAttribute("manages", users1);  //高管信息
        return "/generalAffairs/addUser";
    }

    /**
     * <AUTHOR>
     * @date 2017/10/24 16:09
     * 查看职工信息
     */
    @RequestMapping("/toEmployeeInfo.do")
    public String toEmployeeInfo(Integer id, Model model) throws IOException {
        User user = userService.getUserByID(id);

        if (!"".equals(user.getManagerCode())) {
            if ("general".equals(user.getManagerCode())) {
                user.setManageName("总务");
            } else if ("finance".equals(user.getManagerCode())) {
                user.setManageName("财务");
            } else if ("sale".equals(user.getManagerCode())) {
                user.setManageName("销售");
            } else if ("accounting".equals(user.getManagerCode())) {
                user.setManageName("会计");
            }
        }
        String leader = user.getLeader();  //直接上级


        List<PersonnelOccupation> personnelOccupations = personalService.getPersonnelOccupationByUserId(id);
        List<PersonalEducation> personalEducations = personalService.getPersonalEducationByUserId(id);
        List<PersonnelSalaryLog> personnelSalaryLogs = personalService.getPersonnelSalaryLogByUserId(id);
        List<PersonalRewardPunishment> personalRewardPunishments = personalService.getPersonalRewardPunishmentByUserId(id);
        List<PersonalAssessment> personalAssessments = personalService.getPersonalAssessmentByUserId(id);
        List<PersonnelFolks> folkses = personalService.getFolksByUserId(id);  //家庭成员

        model.addAttribute("user", user);
        model.addAttribute("personalAssessments", personalAssessments);
        model.addAttribute("personalRewardPunishments", personalRewardPunishments);
        model.addAttribute("personnelSalaryLogs", personnelSalaryLogs);
        model.addAttribute("personalEducations", personalEducations);
        model.addAttribute("personnelOccupations", personnelOccupations/*user.getPersonnelOccupations()*/);//工作经历
        model.addAttribute("folkses", folkses);
        return "/generalAffairs/employeeInfo";
    }

    //打开新增工作经历页面
    @RequestMapping("/toAddPersonnelOccupation.do")
    public String toAddPersonnelOccupation(Integer userId, Model model) {
        model.addAttribute("userId", userId);
        return "/generalAffairs/personnelOccupation";
    }

    //打开编辑工作经历页面
    @RequestMapping("/toUpdatePersonnelOccupation.do")
    public String toUpdatePersonnelOccupation(Integer id, Model model) {
        PersonnelOccupation personnelOccupation = personalService.getPersonnelOccupationById(id);
        model.addAttribute("personnelOccupation", personnelOccupation);
        return "/generalAffairs/personnelOccupation";
    }

    //打开新增教育经历页面
    @RequestMapping("/toAddPersonalEducation.do")
    public String toAddPersonalEducation(Integer userId, Model model) {
        model.addAttribute("userId", userId);
        return "/generalAffairs/personalEducation";
    }

    /**
     * <AUTHOR>
     * @date 2017/10/26 10:04
     * 打开编辑教育经历页面
     */
    @RequestMapping("/toUpdatePersonalEducation.do")
    public String toUpdatePersonalEducation(Integer id, Model model) throws IOException {
        PersonalEducation personalEducation = personalService.getPersonalEducationById(id);
        model.addAttribute("personalEducation", personalEducation);
        return "/generalAffairs/personalEducation";
    }

    //打开新增薪资情况页面
    @RequestMapping("/toAddPersonnelSalaryLog.do")
    public String toAddPersonnelSalaryLog(Integer userId, Model model) {
        model.addAttribute("userId", userId);
        return "/generalAffairs/personnelSalaryLog";
    }

    /**
     * <AUTHOR>
     * @date 2017/10/26 10:10
     * 打开修改薪资情况页面
     */
    @RequestMapping("/toUpdatePersonnelSalaryLog.do")
    public String toUpdatePersonnelSalaryLog(Integer id, Model model) throws IOException {
        PersonnelSalaryLog personnelSalaryLog = personalService.getPersonnelSalaryLogById(id);
        model.addAttribute("personnelSalaryLog", personnelSalaryLog);
        return "/generalAffairs/personnelSalaryLog";
    }

    //打开新增奖惩情况页面
    @RequestMapping("/toAddPersonalRewardPunishment.do")
    public String toAddPersonalRewardPunishment(Integer userId, Model model) {
        model.addAttribute("userId", userId);
        return "/generalAffairs/personalRewardPunishment";
    }

    /**
     * <AUTHOR>
     * @date 2017/10/26 10:14
     * 打开修改奖惩情况页面
     */
    @RequestMapping("/toUpdatePersonalRewardPunishment.do")
    public String toUpdatePersonalRewardPunishment(Integer id, Model model) throws IOException {
        PersonalRewardPunishment personalRewardPunishment = personalService.getPersonalRewardPunishmentById(id);
        model.addAttribute("personalRewardPunishment", personalRewardPunishment);
        return "/generalAffairs/personalRewardPunishment";
    }

    //打开新增评价情况页面
    @RequestMapping("/toAddPersonalAssessment.do")
    public String toAddPersonalAssessment(Integer userId, Model model) {
        model.addAttribute("userId", userId);
        return "/generalAffairs/personalAssessment";
    }

    /**
     * <AUTHOR>
     * @date 2017/10/26 10:17
     * 打开修改评价情况页面
     */
    @RequestMapping("/toUpdatePersonalAssessment.do")
    public String toUpdatePersonalAssessment(Integer id, Model model) throws IOException {
        PersonalAssessment personalAssessment = personalService.getPersonalAssessmentById(id);
        model.addAttribute("personalAssessment", personalAssessment);
        return "/generalAffairs/personalAssessment";
    }

    /**
     * <AUTHOR>
     * @date 2017/10/25 15:21
     * 更新员工基本信息
     *
     */
    @ResponseBody
    @RequestMapping("/updateEssentialInformation.do")
    public String updateEssentialInformation(User user,User modifyUser,  String date1,  HttpServletRequest request/*, @RequestParam(value = "imgFile", required = false) MultipartFile[] files*/) throws ParseException, IOException {
        Integer oid = user.getOid();
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        String operation="1";
        User user1 = userService.getUserByID(modifyUser.getUserID());
        if (!user1.getUserName().equals(modifyUser.getUserName())){
            operation="2";
        }
        userHistoryService.addUserHistoryByUser(user1,operation,false);//保存历史记录
        user1.setUserName(modifyUser.getUserName());

        List<User> users = userService.getUsersByLeader(oid, user1.getUserID().toString());
        for (User u : users) {
            u.setLeaderName(user1.getUserName());
            userService.updateUser(u);
        }
        if (date1 != null && !date1.equals("")) {
//            user1.setOnDutyDate(sdf.parse(date1));  //入职时间
            user1.setOnDutyDate(NewDateUtils.dateFromString(date1,"yyyy/MM/dd"));  //入职时间
        }else
            user1.setOnDutyDate(null);
        if (modifyUser.getManagerCode() != null && !"".equals(modifyUser.getManagerCode())) {
//                    String mid=popedomService.getNoStaffMid();
//                    userPopedomService.deleteUserPopedomAllBy(user1,null,mid);//删除原有专业权限
            if (user1.getManagerCode() != null && !"".equals(user1.getManagerCode())) {
                if (!user1.getManagerCode().equalsIgnoreCase(modifyUser.getManagerCode())) {
                    List<String> delMids=userRoleService.getRolePopedomList();//各种核心类权限
                    List<String> initMids=popedomService.getMidListByPid("vc");//初始化 类权限
                    List<String> del=roleTmplService.getMidsByRoleTmplCode("seniorStaff");
                    delMids.addAll(del);
                    delMids.addAll(initMids);
                    List<UserPopedom> userPopedomList=userPopedomService.getUserPopedomByUserIdDelMids(user1.getUserID(),delMids);//查询除核心类权限外的人员权限
                    userPopedomService.deleteUserPopedomAll(userPopedomList);
                    userPopedomService.saveUserPopedomByCode("staff", user1);//给普通员工附上权限
//                    if (user1.getOrdinaryEmployees()!=1) {
//                        userPopedomService.saveUserPopedomByCode("seniorStaff", user1); //添加非普通员工权限  2020/11/2 我的团队版  lixu改
//                    }
                    userService.getPopedomStringByUser(user1,true);//变更权限时用。
                }
            }

            if (user1.getManagerCode() != null && !"".equals(user1.getManagerCode()) && !user1.getManagerCode().equals(modifyUser.getManagerCode())) {
                String cannotDimission = popedomService.cannotDimission(user1);
                if (cannotDimission != null) {
//                            model.addAttribute("error", cannotDimission + ",不能修改所属高管");
                    return "5";
                }
            }

            if (user1.getManagerCode() == null || "".equals(user1.getManagerCode()) || !user1.getManagerCode().equals(user1.getRoleCode())) {
                if (user1.getManagerCode().equals("sale")){
                    //删除对象管理数据
                    customerService.deleteAllWarrantCustomer(user1.getUserID(),null);
                }

//                    user1.setManager(user.getManager());   //高管idd
                user1.setManagerCode(modifyUser.getManagerCode());  //高管权限
            }
        }
        if(!MyStrings.nulltoempty(user1.getDepartment()).isEmpty() && !MyStrings.nulltoempty(modifyUser.getDepartment()).isEmpty() && !user1.getDepartment().equals(user.getDepartment())) {//if ( RoleTmpl.getStaffCodes().contains(user.getRoleCode()) && () //如果今后董事长可以修改档案，需要加上此判断Integer deptId = Integer.parseInt(user1.getDepartment());
            Organization organization1=orgService.getOrgByOid(Integer.valueOf(user1.getDepartment()), OrgService.OrgType.department);  //原来的部门
            organization1.setUserNum(organization1.getUserNum()==null||organization1.getUserNum()==0?0:organization1.getUserNum()-1);   //原来的部门，职工数-1
            orgService.updateOrg(organization1);

            workAttendanceOldService.changeUserDepartment(user1,modifyUser.getDepartment(), WorkAttendanceOldService.ChangeDeptReasons.ExchangDept);
            user1.setDepartment(modifyUser.getDepartment());  //部门id
            Organization organization=orgService.getOrgByOid(Integer.valueOf(modifyUser.getDepartment()), OrgService.OrgType.department);
            user1.setDepartName(organization.getName());   //部门

            organization.setUserNum(organization.getUserNum()==null?1:organization.getUserNum()+1);   //新修改的部门，职工数加1
            orgService.updateOrg(organization);
        }else if (MyStrings.nulltoempty(user1.getDepartment()).isEmpty() && !MyStrings.nulltoempty(modifyUser.getDepartment()).isEmpty()){   //user1.getDepartment()==null
            workAttendanceOldService.changeUserDepartment(user1,modifyUser.getDepartment(), WorkAttendanceOldService.ChangeDeptReasons.ExchangDept);
            user1.setDepartment(modifyUser.getDepartment());  //部门id
            Organization organization=orgService.getOrgByOid(Integer.valueOf(modifyUser.getDepartment()), OrgService.OrgType.department);
            user1.setDepartName(organization.getName());   //部门
            organization.setUserNum(organization.getUserNum()==null?1:organization.getUserNum()+1); //新修改的部门，职工数加1
            orgService.updateOrg(organization);
        }else if (!MyStrings.nulltoempty(user1.getDepartment()).isEmpty() && MyStrings.nulltoempty(modifyUser.getDepartment()).isEmpty()){  //將部門刪除
            workAttendanceOldService.changeUserDepartment(user1,modifyUser.getDepartment(), WorkAttendanceOldService.ChangeDeptReasons.ExchangDept);
            user1.setDepartment(modifyUser.getDepartment());  //部门id
            Organization organization=orgService.getOrgByOid(Integer.valueOf(modifyUser.getDepartment()), OrgService.OrgType.department);
            user1.setDepartName("");   //部门
            organization.setUserNum(organization.getUserNum()==null||organization.getUserNum()==0?0:organization.getUserNum()-1); //新修改的部门，职工数加1
            orgService.updateOrg(organization);
        }

        if (StringUtils.isNotEmpty(modifyUser.getPostID())) {
            RdPost rdPost = rdPostService.getRdPostById(Long.valueOf(modifyUser.getPostID()));
            user1.setPostName(rdPost.getName());   //职位
            user1.setPostID(modifyUser.getPostID());  //职位id
        }else if (user1.getPostID()!=null){  //原本有职位，改成无职位的
            user1.setPostName(modifyUser.getPostName());   //职位
            user1.setPostID(modifyUser.getPostID());  //职位id
        }

//            if (!StringUtil.isNullOrEmpty(modifyUser.getPostID()) && !StringUtil.isNullOrEmpty(user1.getPostID()) && !modifyUser.getPostID().equals(user1.getPostID())){
//                Organization organization3=orgService.getOrgByOid(Integer.valueOf(user1.getPostID()), OrgService.OrgType.post);  //原来的职位
//                organization3.setUserNum(organization3.getUserNum()==null||organization3.getUserNum()==0?0:organization3.getUserNum()-1);   //原来的职位，职工数-1
//                orgService.updateOrg(organization3);
//
//                Organization organization4=orgService.getOrgByOid(Integer.valueOf(modifyUser.getPostID()), OrgService.OrgType.post);
//                user1.setPostName(organization4.getName());   //职位
//                organization4.setUserNum(organization4.getUserNum()==null?1:organization4.getUserNum()+1);   //新修改的职位，职工数加1
//                orgService.updateOrg(organization4);
//
//                user1.setPostID(modifyUser.getPostID());  //职位id
//            }else if (StringUtil.isNullOrEmpty(user1.getPostID()) && !StringUtil.isNullOrEmpty(modifyUser.getPostID())){  //原来没有添加岗位
//                Organization organization2=orgService.getOrgByOid(Integer.valueOf(modifyUser.getPostID()), OrgService.OrgType.post);
//                user1.setPostName(organization2.getName());  //职位
//                organization2.setUserNum(organization2.getUserNum()==null?1:organization2.getUserNum()+1); //新修改的岗位，职工数加1
//                orgService.updateOrg(organization2);
//                user1.setPostID(modifyUser.getPostID());  //职位id
//            }else if (!StringUtil.isNullOrEmpty(user1.getPostID()) && StringUtil.isNullOrEmpty(modifyUser.getPostID())){  //將崗位刪除
//                user1.setPostName("");  //职位
//                Organization organization5=orgService.getOrgByOid(Integer.valueOf(user1.getPostID()), OrgService.OrgType.post);  //原来的职位
//                organization5.setUserNum(organization5.getUserNum()==null|| organization5.getUserNum()==0 ?0:organization5.getUserNum()-1);   //原来的职位，职工数-1
//                orgService.updateOrg(organization5);
//                user1.setPostID(modifyUser.getPostID());  //职位id
//            }


        if (!"".equals(modifyUser.getLeader()) && modifyUser.getLeader()!=null&&!user1.getRoleCode().equals("smallSuper")){
            if (user1.getLeader()!=null && !user1.getLeader().equals(modifyUser.getLeader())) {
                User pUser = userService.getUserByID(Integer.valueOf(modifyUser.getLeader()));
                user1.setLeader(pUser.getUserID().toString());
                user1.setLeaderName(pUser.getUserName());
                String oldUrl = user1.getRankUrl();//修改直接上级前，其下属的从属关系
                String rankUrl ="";
                if (pUser.getRankUrl()!=null){
                    rankUrl+=pUser.getRankUrl();//改成的上级从属关系
                }
                if(rankUrl.equals("")){
                    rankUrl+="/";
                }
                rankUrl+=modifyUser.getLeader();//更改后的从属关系
                List<User> xiaji = userService.getDirectLowerGrade("/"+user1.getUserID()+"/", oid);//直系下级
                for (User x : xiaji) {
                    String xurl = x.getRankUrl();
                    xurl = StringUtils.substringAfter(xurl, oldUrl);
                    x.setRankUrl(rankUrl +"/"+ xurl);//更改直系下级的从属关系
                    userService.updateUser(x);
                }
                user1.setRankUrl(rankUrl);//从属关系
            }else {
                if (!user1.getRoleCode().equals("super")) {
                    User pUser = userService.getUserByID(Integer.valueOf(modifyUser.getLeader()));
                    user1.setLeader(pUser.getUserID().toString());
                    user1.setLeaderName(pUser.getUserName());

                    String rankUrl = "";
                    if (pUser.getRankUrl() != null) {
                        rankUrl += pUser.getRankUrl();//改成的上级从属关系
                    }
                    if(rankUrl.equals("")){
                        rankUrl+="/";
                    }
                    rankUrl += modifyUser.getLeader();//更改后的从属关系
                    user1.setRankUrl(rankUrl);//从属关系
                }
            }
        }

        if (user1.getOrdinaryEmployees() == null || "".equals(user1.getOrdinaryEmployees())) {
            user1.setOrdinaryEmployees(0);  //为空时是非普通员工，为0时也未非普通员工，赋值以防出现空指针(前端传值为0-非普通员工，1-普通员工)
        }
        if (!user1.getOrdinaryEmployees().equals(modifyUser.getOrdinaryEmployees())) {    //是否为普通员工 1-是

            user1 = userPopedomService.updateUserOrdinaryEmployees(user1, modifyUser.getOrdinaryEmployees());//变更是否为非普通员工
            userService.getPopedomStringByUser(user1,true);//变更权限时用。
        }
        user1.setVersionNo(user1.getVersionNo() == null ? 1 : user1.getVersionNo() + 1);
        user1.setUpdateDate(new Date());
        user1.setUpdateName(user.getUserName());
        userService.updateUser(user1);
        popedomTaskService.updateUserBadgeNumber(user1);//重新计算角标

        userHistoryService.addUserHistoryByUser(user1,operation,true);//保存历史记录

        userService.updateUserNameByOidMobile(oid,user1.getMobile(),user1.getUserName());// 2020/9/20 1.125多重身份 lixu改 更新机构下 所有相同手机号身份的 姓名
        userLockService.updateLockUserNameByOidMobile(oid,user1.getMobile(),user1.getUserName());//2021.7.26 修改代办人名称，被代办人跟随变更为新名字代XX

        return "1";
    }

    /**
     * <AUTHOR>
     * @date 2017/10/26 10:22
     * 工作经历新增或修改
     */
    @RequestMapping("/addOrUpdatePersonnelOccupation.do")
    public String addOrUpdatePersonnelOccupation(Integer id, PersonnelOccupation personnelOccupation, String beginTime1, String endTime1, Integer userId, Model model) throws ParseException, IOException {
//        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        //id等于空为新增
        if (id == null) {
            if (beginTime1 != null && !beginTime1.equals("")) {
//                Date beginTime = sf.parse(beginTime1);
                Date beginTime = NewDateUtils.dateFromString(beginTime1,"yyyy-MM-dd");

                personnelOccupation.setBeginTime(beginTime);
            }
            if (endTime1 != null && !endTime1.equals("")) {
//                Date endTime = sf.parse(endTime1);
                Date endTime = NewDateUtils.dateFromString(endTime1,"yyyy-MM-dd");
                personnelOccupation.setEndTime(endTime);
            }
            User user = userService.getUserByID(userId);
            List<PersonnelOccupation> personnelOccupations = personalService.getPersonnelOccupationByUserId(userId);
            if (personnelOccupations.size() == 8) {
                model.addAttribute("error", "不能再添加了");
                return "error";
            }
            personnelOccupation.setUser(user);
            personalService.savePersonnelOccupation(personnelOccupation);
            //否则为修改
        } else {
            PersonnelOccupation po = personalService.getPersonnelOccupationById(id);
            if (beginTime1 != null && !beginTime1.equals("")) {
                Date beginTime = NewDateUtils.dateFromString(beginTime1,"yyyy-MM-dd");
                po.setBeginTime(beginTime);
            }
            if (endTime1 != null && !endTime1.equals("")) {
                Date endTime = NewDateUtils.dateFromString(endTime1,"yyyy-MM-dd");
                po.setEndTime(endTime);
            }
            po.setCorpName(personnelOccupation.getCorpName());
            po.setMemo(personnelOccupation.getMemo());
            po.setPost(personnelOccupation.getPost());
            po.setSalary(personnelOccupation.getSalary());
            po.setOperatingDuty(personnelOccupation.getOperatingDuty());
            personalService.updatePersonnelOccupation(po);
        }
        return "close";
//        return "redirect:/general/toUpdateEssentialInformation.do?id="+userId;
    }

    /**
     * <AUTHOR>
     * @date 2017/10/27 15:23
     * 删除工作经历
     */
    @RequestMapping("/deletePersonnelOccupation.do")
    public String deletePersonnelOccupation(Integer id, Integer userId) throws IOException {
        personalService.deletePersonnelOccupation(personalService.getPersonnelOccupationById(id));
        return "redirect:/general/toUpdateEssentialInformation.do?id=" + userId;
    }

    /**
     * <AUTHOR>
     * @date 2017/10/27 15:27
     * 学习经历新增或修改
     */
    @RequestMapping("/addOrUpdatePersonalEducation.do")
    public String addOrUpdatePersonalEducation(Integer id, PersonalEducation personalEducation, String beginTime1, String endTime1, Integer userId, Model model) throws ParseException, IOException {
//        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
       /* Date beginTime=new Date();
        Date endTime=new Date();*/
        //id等于空为新增
        if (id == null) {
            if (beginTime1 != null && !beginTime1.equals("")) {
                Date beginTime = NewDateUtils.dateFromString(beginTime1,"yyyy-MM-dd");
                personalEducation.setBeginTime(beginTime);
            }
            if (endTime1 != null && !endTime1.equals("")) {
                Date endTime = NewDateUtils.dateFromString(endTime1,"yyyy-MM-dd");
                personalEducation.setEndTime(endTime);
            }
            User user = userService.getUserByID(userId);
            List<PersonalEducation> personalEducations = personalService.getPersonalEducationByUserId(userId);
            if (personalEducations.size() == 8) {
                model.addAttribute("error", "不能再添加了");
                return "error";
            }
            personalEducation.setUser(user);
            personalService.savePersonalEducation(personalEducation);
            //否则为修改
        } else {
            PersonalEducation pe = personalService.getPersonalEducationById(id);
            if (beginTime1 != null && !beginTime1.equals("")) {
                Date beginTime = NewDateUtils.dateFromString(beginTime1,"yyyy-MM-dd");
                pe.setBeginTime(beginTime);
            }
            if (endTime1 != null && !endTime1.equals("")) {
                Date endTime = NewDateUtils.dateFromString(endTime1,"yyyy-MM-dd");
                pe.setEndTime(endTime);
            }
            pe.setCollegeName(personalEducation.getCollegeName());
            pe.setMemo(personalEducation.getMemo());
            pe.setDegree(personalEducation.getDegree());
            pe.setMajor(personalEducation.getMajor());
            personalService.updatePersonalEducation(pe);
        }
        return "close";
    }

    /**
     * <AUTHOR>
     * @date 2017/10/27 15:32
     * 删除学习经历
     */
    @RequestMapping("/deletePersonalEducation.do")
    public String deletePersonalEducation(Integer id, Integer userId) throws IOException {
        personalService.deletePersonalEducation(personalService.getPersonalEducationById(id));
        return "redirect:/general/toUpdateEssentialInformation.do?id=" + userId;
    }

    /**
     * <AUTHOR>
     * @date 2017/10/27 15:34
     * 薪资情况新增或修改
     */
    @RequestMapping("/addOrUpdatePersonnelSalaryLog.do")
    public String addOrUpdatePersonnelSalaryLog(Integer id, PersonnelSalaryLog personnelSalaryLog, User user, Integer userId) throws IOException {
        Map<String, Object> map = new HashMap<>();
        //id等于空为新增
        if (id == null) {
            personnelSalaryLog.setOperateTime(new Date());
            personnelSalaryLog.setOperator(user.getUserID());
            personnelSalaryLog.setOperatorName(user.getUserName());
            User u = userService.getUserByID(userId);
            personnelSalaryLog.setUser(u);
            personalService.savePersonnelSalaryLog(personnelSalaryLog);
            //否则为修改
        } else {
            PersonnelSalaryLog personnelSalaryLog1 = personalService.getPersonnelSalaryLogById(id);
            personnelSalaryLog1.setOperateTime(new Date());
            personnelSalaryLog1.setOperatorName(user.getUserName());
            personnelSalaryLog1.setOperator(user.getUserID());
            personnelSalaryLog1.setAdmustResaon(personnelSalaryLog.getAdmustResaon());
            personnelSalaryLog1.setMemo(personnelSalaryLog.getMemo());
            personnelSalaryLog1.setSalary(personnelSalaryLog.getSalary());
            personalService.updatePersonnelSalaryLog(personnelSalaryLog1);
        }
//        return "redirect:/general/toUpdateEssentialInformation.do?id="+userId;
        return "close";
    }

    /**
     * <AUTHOR>
     * @date 2017/10/27 15:39
     * 删除薪资情况
     */
    @RequestMapping("/deletePersonnelSalaryLog.do")
    public String deletePersonnelSalaryLog(Integer id, Integer userId) throws IOException {
        personalService.deletePersonnelSalaryLog(personalService.getPersonnelSalaryLogById(id));
//        return "redirect:/general/toEmployeeInfo.do?id="+userId;
        return "redirect:/general/toUpdateEssentialInformation.do?id=" + userId;
    }

    /**
     * <AUTHOR>
     * @date 2017/10/27 15:40
     * 奖惩情况新增或修改
     */
    @RequestMapping("/addOrUpdatePersonalRewardPunishment.do")
    public String addOrUpdatePersonalRewardPunishment(Integer id, PersonalRewardPunishment personalRewardPunishment, User user, Integer userId) throws IOException {
        Map<String, Object> map = new HashMap<>();
        //id等于空为新增
        if (id == null) {
            personalRewardPunishment.setOccurDate(new Date());
            personalRewardPunishment.setOperator(user.getUserID());
            personalRewardPunishment.setOperatorName(user.getUserName());
            User u = userService.getUserByID(userId);
            personalRewardPunishment.setUser(u);
            personalService.savePersonalRewardPunishment(personalRewardPunishment);
            //否则为修改
        } else {
            PersonalRewardPunishment personalRewardPunishment1 = personalService.getPersonalRewardPunishmentById(id);
            personalRewardPunishment1.setOccurDate(new Date());
            personalRewardPunishment1.setOperatorName(user.getUserName());
            personalRewardPunishment1.setOperator(user.getUserID());
            personalRewardPunishment1.setContent(personalRewardPunishment.getContent());
            personalRewardPunishment1.setMemo(personalRewardPunishment.getMemo());
            personalService.updatePersonalRewardPunishment(personalRewardPunishment1);
        }
        return "close";
    }

    /**
     * <AUTHOR>
     * @date 2017/10/27 15:44
     * 删除奖惩情况
     */
    @RequestMapping("/deletePersonalRewardPunishment.do")
    public String deletePersonalRewardPunishment(Integer id, Integer userId) throws IOException {
        personalService.deletePersonalRewardPunishment(personalService.getPersonalRewardPunishmentById(id));
//        return "redirect:/general/toEmployeeInfo.do?id="+userId;
        return "redirect:/general/toUpdateEssentialInformation.do?id=" + userId;
    }

    /**
     * <AUTHOR>
     * @date 2017/10/27 15:46
     * 评价情况的新增或修改
     */
    @RequestMapping("/addOrUpdatePersonalAssessment.do")
    public String addOrUpdatePersonalAssessment(Integer id, PersonalAssessment personalAssessment, User user, Integer userId, Model model) throws IOException {
        //id等于空为新增
        if (id == null) {
            personalAssessment.setAssessDate(new Date());
            personalAssessment.setAssessUser(user.getUserID());
            personalAssessment.setAssessUserName(user.getUserName());
            User u = userService.getUserByID(userId);
            List<PersonalAssessment> personalAssessments = personalService.getPersonalAssessmentByUserId(userId);
            if (personalAssessments.size() == 8) {
                model.addAttribute("error", "不能再添加了");
                return "error";
            }
            personalAssessment.setUser(u);
            personalService.savePersonalAssessment(personalAssessment);
            //否则为修改
        } else {
            PersonalAssessment personalAssessment1 = personalService.getPersonalAssessmentById(id);
            personalAssessment1.setAssessDate(new Date());
            personalAssessment1.setAssessUserName(user.getUserName());
            personalAssessment1.setAssessUser(user.getUserID());
            personalAssessment1.setContent(personalAssessment.getContent());
            personalAssessment1.setMemo(personalAssessment.getMemo());
            personalService.updatePersonalAssessment(personalAssessment1);
        }
        return "close";
    }

    /**
     * <AUTHOR>
     * @date 2017/10/27 15:49
     * 删除评价情况
     */
    @RequestMapping("/deletePersonalAssessment.do")
    public String deletePersonalAssessment(Integer id, Integer userId) throws IOException {
        personalService.deletePersonalAssessment(personalService.getPersonalAssessmentById(id));
        return "redirect:/general/toUpdateEssentialInformation.do?id=" + userId;
//        return "/generalAffairs/updateEssentialInformation";
    }

    /**
     * <AUTHOR>
     * @date 2017/12/11 14:46
     * 跳转新增员工的家庭成员页面
     */
    @RequestMapping("/toAddOrUpdateFolk.do")
    public String toAddOrUpdateFolk(Integer userId, Integer folkId, Model model) {
        PersonnelFolks personnelFolk = new PersonnelFolks();
        model.addAttribute("userId", userId);
        if (folkId != null) {
            personnelFolk = personalService.getFolkById(folkId);
        }
        model.addAttribute("personnelFolk", personnelFolk);
        return "/generalAffairs/addOrUpdateFamily";
    }

    /**
     * <AUTHOR>
     * @date 2017/12/1 16:41
     * 新增或者修改员工的家庭成员
     * userId--为要添加或者修改的用户id
     * id--要修改某家庭成员的id
     */
    @RequestMapping("/addOrUpdateFolk.do")
    public String addOrUpdateFolk(Integer userId, PersonnelFolks personnelFolks, Integer folkId) throws IOException {
        if (folkId == null) {   //id为空说明是添加新的家庭成员
            if (userId != null) {
                User u = userService.getUserByID(userId);
                personnelFolks.setUserId(userId);
                personnelFolks.setUser(u);
                personalService.savePersonnelFolks(personnelFolks);
            }
        } else {
            PersonnelFolks folk = personalService.getFolkById(folkId);
            if (folk != null) {
                folk.setName(personnelFolks.getName());
                folk.setAge(personnelFolks.getAge());
                folk.setGender(personnelFolks.getGender());
                folk.setRelation(personnelFolks.getRelation());
                folk.setMemo(personnelFolks.getMemo());
                personalService.updatePersonnelFolks(folk);
            }
        }
        return "close";
//        return "redirect:/general/toUpdateEssentialInformation.do?id="+userId;
    }

    /**
     * <AUTHOR>
     * @date 2017/12/1 17:08
     * 删除某家庭成员
     */
    @RequestMapping("/deletePersonnelFolks.do")
    public String deletePersonnelFolks(Integer folkId, Integer userId, HttpServletResponse response) throws IOException {
        PersonnelFolks personnelFolks = personalService.getFolkById(folkId);
        if (personnelFolks != null) {
            personalService.deletePersonnelFolks(personnelFolks);
        }
        return "redirect:/general/toUpdateEssentialInformation.do?id=" + userId;
//        return "/generalAffairs/updateEssentialInformation";
    }

   /*
    * lixu
    * 保存用户头像的路径
    * 1.159私人领地奠基
    * */

    @RequestMapping("/updateUserimgByUserID.do")
    public void updateUserimgByUserID(User user, String path, HttpServletResponse response) throws IOException {
        if (user.getImgPath() != null && !user.getImgPath().isEmpty()) {
            UploadFile uploadFile = uploadService.getUploadFile(user.getImgPath());
            if (uploadFile != null) {
                uploadService.delFileUsing(new UserUsing(user.getUserID()), user.getImgPath(), user); // 如果 有原头像 则删除原头像 的文件引用
            }
        }
        user.setImgPath(path);
        userService.updateUser(user);
//        String u = JSON.toJSONString(user);
//        CookieUtils.setCookie(response, "user", u);

        uploadService.addFileUsing(new UserUsing(user.getUserID()), user.getImgPath(), user.getUserName(), user, "职工头像");
        userHistoryService.addUserHistoryByUser(user, "3", false);//查原始信息，没有就用户补充基本信息的原始信息
        UserHistory userHistory = new UserHistory();
        userHistory.setImgPath(user.getImgPath());
        userHistory.setOid(user.getOid());
        userHistory.setUserId(user.getUserID());
        userHistory.setUpdator(user.getUserID());
        userHistory.setUpdateName(user.getUserName());
        userHistory.setUpdateDate(new Date());
        userHistory.setCreator(user.getUserID());
        userHistory.setCreateTime(new Date());
        userHistory.setCreateName(user.getUserName());
        userHistory.setOperation("4");//操作: 1- 普通更改（不改姓名、手机号、头像，只改其他信息） 2- 含姓名更改（不含 手机号，头像），3 只改手机号（手机号只能单独修改），4- 更换头像的历史记录
        userHistoryService.saveUserHistory(userHistory);
        uploadService.addFileUsing(new UserHistoryUsing(userHistory.getId()), userHistory.getImgPath(), userHistory.getUserName() + "头像更换进历史", user, "职工头像");

        try {
            response.setHeader("user", URLEncoder.encode(JSON.toJSONString(user), "utf-8"));
        } catch (UnsupportedEncodingException e) {
            logger.error("user对象转换报错", e);
        }
    }

//    //修改照片
//    @RequestMapping("/updateUserImage.do")
//    public String updateUserImage(HttpServletResponse response, User user, Integer userId, HttpServletRequest request, @RequestParam("imgFile") MultipartFile[] files) throws IOException {
//        User user = userService.getUserByID(userId);
//        User loginUser = (User) session.getAttribute("user");
//        List fileTypes = new ArrayList();//可以上传的图片类型
//        fileTypes.add("jpg");
//        fileTypes.add("jpeg");
//        fileTypes.add("bmp");
//        fileTypes.add("gif");
//        fileTypes.add("png");
//        String path = request.getServletContext().getRealPath("/upload");//项目路径
//        String datepath = new SimpleDateFormat("/yyyy/MM/dd/").format(new Date());//当前时间路径
//        String imgName = "";
//        for (MultipartFile f : files) {
//            imgName = f.getOriginalFilename();
//            String ext = imgName.substring(imgName.lastIndexOf(".") + 1, imgName.length());
//            //对扩展名进行小写转换
//            ext = ext.toLowerCase();
//
//            if (fileTypes.contains(ext)) {
//                FileUtils.copyInputStreamToFile(f.getInputStream(), new File(path + datepath + imgName));//如果扩展名属于允许上传的类型，则创建文件
//                user.setImgPath("upload" + datepath + imgName);
//            }
//        }
//        userService.updateUser(user);
//        if (loginUser.getUserID() != null && userId != null && userId.equals(loginUser.getUserID())) {
//            session.setAttribute("user", SerializationUtils.clone(user));
//            CookieUtils.setCookie(response, "user", JSON.toJSONString(user));
//        }
//        return "redirect:/general/toEmployeeInfo.do?id=" + userId;
//    }

    /**
     * <AUTHOR>
     * @date 2017/10/30 14:48
     * 获取所有的部门或者岗位
     * orgType 2为部门，3为职位
     */
    @ResponseBody
    @RequestMapping("/getAllManageAndPosts.do")
    public void getAllManageAndPosts(Integer orgType, HttpServletResponse response, User user) throws IOException {
        Map<String, Object> map = new HashMap<>();
        Integer oid = user.getOid();
        List<Organization> organizations = orgService.checkDepartmentByPId(oid, orgType);//2为部门，3为职位
        map.put("organizations", organizations);
        ObjectToJson.objectToJson1(map, new String[]{"roles", "user", "area", "financeAccounts", "accountDetails", "accountPeriods", "mtCategoryOrg",
                "financeReturnHashSet", "financeChequeHashSet", "orgPopedomHashSet"}, response);
    }

    //打开办理离职页面
    @RequestMapping("/toForLeaving.do")
    public String toForLeaving(Integer userId, Model model) {
        model.addAttribute("userId", userId);
        return "/generalAffairs/forLeaving";
    }

    /**
     * <AUTHOR>
     * @date 2017/10/26 9:50
     * 办理离职
     */
    @ResponseBody
    @RequestMapping("/forLeaving.do")
    public JsonResult forLeaving(Integer userId,User user, String transferReason, Model model) throws IOException {
        return userService.forLeaving(userId,transferReason,user);  //办理离职方法转移到Service中 lixu 2024/12/21  1.322劳动合同
//        return "close";
    }


    /**
     * <AUTHOR>
     * @date 2017/10/24 16:57
     * 保存新user（添加user）
     * 2019-11-12 李旭改
     */
    @ResponseBody
    @RequestMapping("/addUser.do")
    public String addUser(User user, User newUser, String date, Integer pid, String date1, Model model, HttpServletRequest request, AuthInfoDto authInfo, PersonnelContract personnelContract, String contractSignTime, String contractStartTime,
                          String contractEndTime, String contractBaseImages) throws ParseException {
        Integer oid = user.getOid();
        String remind="";
        if (newUser.getMobile().length() != 11) {
            model.addAttribute("error", "你手机号不是11位的，请输入11位的正确的手机号");
            remind="你手机号不是11位的，请输入11位的正确的手机号";
            return "2";
        } else {
            if (newUser.getMobile().length() == 11) {
                User userByLogonName = userService.getUserByOidAndPhone1(oid,newUser.getMobile());
                if(userByLogonName!=null) {
                    model.addAttribute("error", "系统中已存在手机号，请输入其他的手机号码！");  //
                    remind="您录入的手机号与公司"+userByLogonName.getUserName()+"手机号相同。请确认！";
                    return "3";
                }

//                List<User> userList = userService.getUserByMobeilAndIsDuty(oid, user.getMobile(), "2");  //离职人员
//                if (userList.size() > 0) {
//                    model.addAttribute("error", "此手机号已办理离职，请更换！");
//                    remind="您录入的手机号与公司已离职者"+userList.get(0).getUserName()+"手机号相同。确定该号码无误吗？";
//                    return "4";
//                }
            }
            Organization organization = user.getOrganization();
//            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");
            Date onDutyDate = new Date();

            //基本信息
            User user1 = new User();
            if (date1 != null && !"".equals(date1)) {
//                onDutyDate = simpleDateFormat.parse(date1);
                onDutyDate = NewDateUtils.dateFromString(date1,"yyyy/MM/dd");
                user1.setOnDutyDate(onDutyDate);//入职时间
            }

            if (date != null && !"".equals(date)) {
                Date bDate = new Date();
//                bDate = simpleDateFormat.parse(date);
                bDate = NewDateUtils.dateFromString(date,"yyyy/MM/dd");
                user1.setBirthday(bDate);//出生日期
            }
            user1.setUserName(newUser.getUserName());//用户姓名
            user1.setRealName(newUser.getUserName());//真实姓名
            user1.setGender(newUser.getGender());//性别
            user1.setSuperId(organization.getSuperId());//薪资宝的
            List<User> userList = userService.getUserByMobileAndIsDuty(oid,newUser.getMobile(), "9");  // 本机构相同手机号锁定的数据
            for (User u:userList){
                u.setIsDuty("2");
                userService.updateUser(u); // 锁定的手机号 被重新录入机构，需要把原锁定的员工 变为离职。 1.117 奠基优化 20210823 lixu 改
            }

            user1.setMobile(newUser.getMobile());//手机f
            user1.setEmail(newUser.getEmail());//电子邮箱
            user1.setQq(newUser.getQq());//qq号
            user1.setPoliticalStatus(newUser.getPoliticalStatus());//政治面貌
            user1.setMarry(newUser.getMarry());//0已婚。1未婚
            user1.setNation(newUser.getNation());//民族
            user1.setIdCard(newUser.getIdCard());//身份证号
            user1.setHomeAddress(newUser.getHomeAddress());//家庭地址
            user1.setInteresting(newUser.getInteresting());  //兴趣
            user1.setSpeciality(newUser.getSpeciality());  //爱好
            user1.setNativePlace(newUser.getNativePlace());  //籍贯
            user1.setEdu(newUser.getEdu());  //最高学历
            //岗位信息
            if (!MyStrings.nulltoempty(newUser.getDepartment()).isEmpty()) {
                Organization d = orgService.getOrgByOid(Integer.decode(newUser.getDepartment()), OrgService.OrgType.department);
                if (d != null) {
                    user1.setDepartment(String.valueOf(d.getId()));//所属部门
                    user1.setDepartName(d.getName());//所属部门

                    d.setUserNum(d.getUserNum()==null?1:d.getUserNum()+1);
                    orgService.updateOrg(d);
                }
            }
            if (!MyStrings.nulltoempty(newUser.getPostID()).isEmpty()) {
                RdPost rdPost = rdPostService.getRdPostById(Long.valueOf(newUser.getPostID()));
                user1.setPostName(rdPost.getName());   //职位
                user1.setPostID(newUser.getPostID());  //职位id
//                Organization p = orgService.getOrgByOid(Integer.decode(newUser.getPostID()), OrgService.OrgType.post);
//                if (p != null) {
//                    user1.setPostID(String.valueOf(p.getId())); //职位
//                    user1.setPostName(p.getName()); //职位
//
//                    p.setUserNum(p.getUserNum()==null?1:p.getUserNum()+1);
//                    orgService.updateOrg(p);
//                }
            }
            user1.setOrdinaryEmployees(newUser.getOrdinaryEmployees());//是否为普通员工 1-普通员工
            User pUser = userService.getUserByID(pid);
            if (pUser != null) {
                user1.setLeader(String.valueOf(pid));//从属于某位领导
                String rankUrl;
                if (!StringUtil.isNullOrEmpty(pUser.getRankUrl())){
                    rankUrl=pUser.getRankUrl()+pid;
                }else {
                    rankUrl="/"+pid;
                }
                user1.setRankUrl(rankUrl);//从属关系
                user1.setLeaderName(pUser.getUserName());//从属于某位领导
            }
            user1.setCreateTime(new Date());//创建时间
            user1.setCreator(user.getUserID());//创建者
            user1.setCreateName(user.getUserName());//创建者
            user1.setStatus("2");////状态：1-禁用 其它-启用
            user1.setIsDuty("1");//是否在职  1表示在职，2表示离职
//            user1.setLogonName(user.getMobile());
            user1.setCompany(newUser.getCompany());//所属公司
            user1.setTransferReason(newUser.getTransferReason());//转岗原因
            user1.setWorkYear(newUser.getWorkYear());//工作年限
            user1.setDegree(newUser.getDegree());//学历 '1-小学以下，2-小学，3-初中，4-高中，5-中专，6-大专 7-本科，8-硕士，9-博士
            user1.setResidence(newUser.getResidence());//户籍
            user1.setNativeType(newUser.getNativeType());//户籍性质
            user1.setAddress(newUser.getAddress());//身份证住址
            user1.setActualAddress(newUser.getActualAddress());//实际住址
            user1.setPay(newUser.getPay());//薪资
            user1.setOrganization(organization);
            user1.setOid(organization.getId());
//            user1.setUserType(10);//代表用户的类型 目前暂定为：0：超级管理员  1：财务  2：浏览者 3:总务 4:销售
            user1.setRemark(newUser.getRemark());//备注
            user1.setDefault(false);
//            user1.setManager(user.getManager());  //高管id
//            User manage=userService.getUserByID(user.getManager());
            user1.setManagerCode(newUser.getManagerCode());  //高管权限
            user1.setRoleCode("staff");  //此员工的权限
            user1.setSuperId(organization.getSuperId());
//            if (newUser.getMobile().length() == 11) {
//                user1.setLogonPwd(userService.getLogonPwdByPhone(newUser.getMobile()));//获取该有的登录密码
//            }
//            for (MultipartFile f : files) {
//                String path = UploadImages.uploadImages(request, f);
//                user1.setImgPath(path);
//            }
//            AuthAcc authAcc= authService.newEnabledAcc(user1.getMobile(),user1.getUserName());// 生成登陆账号
//            user1.setAccId(authAcc.getId());
            userService.addUser(user1, organization);

            AuthAcc authAcc=authService.getLockedByMobile(user1.getMobile());
            if (authAcc!=null) {
                String phoneNo = user1.getMobile();
                String url = GetLocalIPUtils.getRootPath(request);
                Map<String, String> params = new HashMap<String, String>(3) {{
                    put("activeCode", authService.getOrCreateActiveCode(authAcc).getRight());
                    put("url", url);
                    put("mobile", user.getMobile());
                }};
                smsService.sendMessage(SmsService.SendType.role, phoneNo, authInfo, TemplateService.SmsContentTemplate.addSmallSuper, params, null);
            }

            if (newUser.getImgPath()!=null&&!newUser.getImgPath().isEmpty()) {
                user1.setImgPath(newUser.getImgPath());
                uploadService.addFileUsing(new UserUsing(user1.getUserID()), user1.getImgPath(), user1.getUserName()+"(办理入职)", user1, "职工头像");
            }
            userService.updateUser(user1);

//            //新增薪资宝用户[摘除薪资宝时注释]
//            salaryAction.addSalaryUser(user1);
            userPopedomService.saveUserPopedomByCode("staff", user1);//给普通员工附上权限
            if (1!=user1.getOrdinaryEmployees()){  // 非普通员工   2020/11/3 我的团队版  lixu加
                userPopedomService.saveUserPopedomByCode("seniorStaff",user1); //添加非普通员工权限
            }
            userService.getPopedomStringByUser(user1,true);//变更权限时用。
            popedomTaskService.updateUserBadgeNumber(user1);//重新计算角标

//            String cont="我公司将使用软件系统进行管理，请点击下载手机端，安装后使用您"+user1.getMobile()+"手机号登录，并按页面提示操作。用电脑登录"+ System.getProperty("BaseUrl")+"也可进行同样操作。\n" +
//                    "有疑问可给我"+user.getMobile()+"回电话。";

            //            return "redirect:/general/toUpdateEssentialInformation.do?id=" + user1.getUserID();

//            String cont = "我公司要使用的Wonderss系统初始化已完成，请点击下载手机端，安装后使用您"+user1.getMobile()+"手机号登录，并按页面提示操作。用电脑登录"+System.getProperty("BaseUrl")+"也可进行同样操作。\n" +
//                    "有疑问可拨打我手机"+user.getMobile();
//            sendMessageService.sendMessage(user1.getUserID(),oid,newUser.getMobile(),cont);//发短信

            // 1.322劳动合同
            if (StringUtils.isNotBlank(personnelContract.getSn()) && StringUtils.isNotBlank(contractStartTime) && StringUtils.isNotBlank(contractEndTime)) {
                personnelContract.setUser(user1.getUserID());
                personnelContract.setUsername(user1.getUserName());
                personnelContractService.insertPersonnelController(user,personnelContract,contractSignTime, contractStartTime, contractEndTime, contractBaseImages);
            }
            // 短信重构 2024/5/22 李旭更改
            String phoneNo=user1.getMobile();
            String url = GetLocalIPUtils.getRootPath(request);
            Map<String, String> params = new HashMap<String, String>(3) {{
                put("phoneNo", phoneNo);
                put("url", url);
                put("mobile", user.getMobile());
            }};
            smsService.sendMessage(SmsService.SendType.general, phoneNo, authInfo, TemplateService.SmsContentTemplate.addUser, params, null);

            userService.sendH5UnLockedActiveCode(request,authInfo,user,phoneNo); // 发送h5激活短信
            return "1";
//            return "我公司要使用的Wonderss系统初始化已完成，请点击下载手机端，安装后使用您A手机号登录，并按页面提示操作。用电脑登录"+url+"也可进行同样操作。\n有疑问可拨打我手机"+user.getMobile()+"。\n此账号有被冻结的数据，您需点击"+url+"/vue/minersActiveAcc/dist/index.html?activeCode="+authService.getOrCreateActiveCode(authAcc).getRight()+"以确认情况。";

        }
    }

    //根据上级部门id查询子部门---删除后重新复制的接口
    @RequestMapping("/checkSonDepartment.do")
    @ResponseBody
    public void checkSonDepartment(Integer id, HttpServletResponse response) throws IOException {
        List<Organization> organizations = orgService.getAllBranchById(id);
        ObjectToJson.objectToJson(organizations, new String[]{"user", "area", "financeAccounts", "accountDetails", "accountPeriods", "MtCategoryOrg", "financeReturnHashSet", "financeChequeHashSet","orgPopedomHashSet"}, response);
    }


    /**
     * <AUTHOR>
     * @date ********
     * 打开招聘管理页面
     */
    @ResponseBody
    @RequestMapping("/goRecruitManagement.do")
    public ModelAndView goRecruitManagement(){
        return new ModelAndView("generalAffairs/recruitManagement");
    }

    /**
     * <AUTHOR>
     * @date 2021/5/26
     * 1.159私人领地奠基  用户超出89天后 总务给用户激活
     */
    @AuthPassport(verifiedCode = "helpActivationVerificationCode")
    @ResponseBody
    @RequestMapping("/activationUser.do")
    public JsonResult activationUser(User user, Integer passiveUserId){
        return  new JsonResult(1,userService.activationUser(user,passiveUserId));

    }


    /**
     * <AUTHOR>
     * @date 2024/6/21
     * 1.289账号验证与安全问题   总务帮他激活  发送激活用户 验证码
     */
    @ResponseBody
    @RequestMapping("/helpActivationVerificationCode.do")
    public JsonResult helpActivationVerificationCode(User user,String phone, AuthInfoDto authInfo) {
        final List<String> verifyMethodNames = new ArrayList<String>(2) {{
            add("checkHelpActivationVerification");
            add("activationUser");
        }};
        Map<String, String> params = new HashMap<String, String>(1) {{
            put("userName", user.getUserName());
        }};
        return smsService.sendVerificationCodeForGeneral(verifyMethodNames, phone, authInfo, TemplateService.SmsContentTemplate.generalActivation,params);
    }

    /**
     * <AUTHOR>
     * @Date 2024/6/21
     * 1.289账号验证与安全问题    验证 总务帮他激活 验证码
     */
    @ResponseBody
    @RequestMapping("/checkHelpActivationVerification.do")
    public JsonResult checkHelpActivationVerification(AuthInfoDto authInfo, String phone, String code, HttpServletResponse response){
        final String sendMethordName = "helpActivationVerificationCode";
        if(smsService.verificationCode(response, authInfo,sendMethordName,phone,code)) {
            return new JsonResult(1, "验证通过");
        } else {
            return new JsonResult(new MyException("-1", "验证码错误或者超时！"));
        }
    }


    /**
     * <AUTHOR>
     * @Date 2024/6/22
     * 1.289账号验证与安全问题    异常账户-即将冻结的账号列表接口
     */
    @ResponseBody
    @RequestMapping("/soonFrozenUsers.do")
    public JsonResult soonFrozenUsers(User user, PageInfo pageInfo){
        List<User> userList=userService.getSoonFrozenUserList(user.getOid(),pageInfo);
        Map<String,Object> map=new HashMap<>();
        map.put("userList",userList);
        map.put("pageInfo",pageInfo);
        return new JsonResult(1,map);
    }


    /**
     * <AUTHOR>
     * @Date 2024/6/22
     * 1.289账号验证与安全问题    异常账户-已冻结的账号列表接口
     */
    @ResponseBody
    @RequestMapping("/frozenUsers.do")
    public JsonResult frozenUsers(User user, PageInfo pageInfo){
        List<User> userList=userService.getFrozenUserList(user.getOid(),pageInfo);
        Map<String,Object> map=new HashMap<>();
        map.put("userList",userList);
        map.put("pageInfo",pageInfo);
        return new JsonResult(1,map);
    }



    /**
     * <AUTHOR>
     * @date 2024/7/22
     * 职工档案的筛选功能    重构优化
     * edus(学历，以逗号隔开拼接成字符串String类型) gender(性别  1-男 0-女) departments(部门，以逗号隔开拼接成字符串String类型) posts(职位，以逗号隔开拼接成字符串String类型) marry(0-已婚 1-未婚)
     * birthday（出生日期） onDutyDate(入职时间)   isDuty 是否在职  1表示在职，2表示离职
     * quantum 每页多少条   pageNumber 当前页
     */
    @ResponseBody
    @RequestMapping("/employeeList.do")
    public JsonResult employeeList(String isDuty, String[] edus, String[] gender, String[] departments, String[] posts, String[] marry, String birthday1, String birthday2, String onDutyDate1, String onDutyDate2, User user,Integer[] oids,PageInfo pageInfo){
        Map<String, Object> map = new HashMap<>();
        if (oids==null||oids.length==0){
            oids = new Integer[]{user.getOid()};
        }
        List<User> users = userService.employeeList(edus, gender, departments, posts, marry,birthday1,birthday2,onDutyDate1,onDutyDate2, isDuty,oids,pageInfo);
        map.put("userList", users);
        map.put("pageInfo", pageInfo);
        return new JsonResult(1,map);
    }

}
