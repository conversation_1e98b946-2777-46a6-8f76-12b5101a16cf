<template>
	<div class="uni-container">
		<div class="wating icon-mailList">  </div>
			<div class="watTxt">敬请期待</div>

	</div>
</template>

<script>
	export default {
		data() {
			return {
				accountDetails: "/pages/accountDetails/accountDetails"
			}
		},
		onLoad() {

		},
		methods: {
			goPage:function(url){
				uni.navigateTo({
					url:url
				})
			}
		}
	}
</script>


<style lang="scss">
.icon-mailList{ background: url("../../../../../liveMiniApp/src/assets/img/tabbar/mailList.png");   background-size: 200px; }
	
	
</style>