package cn.sphd.miners.modules.uploads.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.service.BaseServiceImpl;
import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.common.utils.DigestUtils;
import cn.sphd.miners.common.utils.HttpClientUtils;
import cn.sphd.miners.common.utils.TimeUtils;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.auth.service.AuthService;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgService;
import cn.sphd.miners.modules.uploads.dao.UploadFileDao;
import cn.sphd.miners.modules.uploads.dao.UploadFileUsingDao;
import cn.sphd.miners.modules.uploads.dao.UploadFileUsingLogDao;
import cn.sphd.miners.modules.uploads.dao.UploadJunkFileCleanDao;
import cn.sphd.miners.modules.uploads.dto.LogDto;
import cn.sphd.miners.modules.uploads.dto.ResFileDto;
import cn.sphd.miners.modules.uploads.dto.ResPathDto;
import cn.sphd.miners.modules.uploads.entity.UploadFile;
import cn.sphd.miners.modules.uploads.entity.UploadFileUsing;
import cn.sphd.miners.modules.uploads.entity.UploadFileUsingLog;
import cn.sphd.miners.modules.uploads.entity.UploadJunkFileClean;
import cn.sphd.miners.modules.uploads.service.CleanJunkFilesListener;
import cn.sphd.miners.modules.uploads.service.FileUsingCallback;
import cn.sphd.miners.modules.uploads.service.UploadService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.Logger;
import org.hibernate.Transaction;
import org.hibernate.resource.transaction.spi.TransactionStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Created by 朱思旭 on 2021/1/23.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class UploadServiceImpl extends BaseServiceImpl implements UploadService {
    final int updateLimit = 1000;
    final int readLimit = 1000;

    @Autowired
    UploadFileDao uploadFileDao;
    @Autowired
    UploadFileUsingDao uploadFileUsingDao;
    @Autowired
    UploadFileUsingLogDao uploadFileUsingLogDao;
    @Autowired
    UploadJunkFileCleanDao uploadJunkFileCleanDao;
    @Autowired
    OrgService orgService;
    @Autowired
    AuthService authService;
    @Autowired
    ThreadPoolTaskExecutor taskExecutor;

    @Override
    public void saveUploadFile(UploadFile uploadFile,User user, AuthAcc acc, LogDto logDto) {
        uploadFileDao.saveOrUpdate(uploadFile);
        createLog(uploadFile,null, user, acc,logDto);
    }

    /**
     * 多文件上传的统一创建时间，不记录日志。
     * @param groupUuid
     * @param now
     */
    @Override
    public void updateCreateDateByGroupUuid(String groupUuid, Date now) {
        String hql = "update UploadFile set createDate=:createDate where groupUuid=:groupUuid";
        Map<String,Object> params = new HashMap<>(2);
        params.put("createDate", now);
        params.put("groupUuid", groupUuid);
        uploadFileDao.queryHQLWithNamedParams(hql,params);
    }

    @Override
    public UploadFile getUploadFile(String filename) {
        return uploadFileDao.get(filename);
//        String hql = "from UploadFile where filename=:filename";
//        Map<String,Object> params = new HashMap<>(1);
//        params.put("filename",filename);
//        UploadFile result = (UploadFile) uploadFileDao.getByHQLWithNamedParams(hql, params);
//        if(result==null) {
//            System.out.println("getUploadFile null filename = " + filename + " , hql = " + hql );
//        } else {
//            System.out.println("getUploadFile not null filename = " + filename + " , hql = " + hql );
//        }
//        return result;
    }

    @Override
    public void delUploadFile(UploadFile uploadFile, User user, AuthAcc acc, LogDto logDto) {
        createLog(uploadFile,null, user, acc,logDto);
        uploadFileDao.delete(uploadFile);
    }

    private UploadFileUsing getUploadFileUsingByCallbackFilename(FileUsingCallback callback, String filename) {
        String callbackClass = callback.getClass().getName();
        String callbackKey = callback.getKey();
        String hql = "from UploadFileUsing where filename=:filename and callbackClass=:callbackClass and callbackKey=:callbackKey";
        Map<String,Object> params = new HashMap<>(2);
        params.put("filename",filename);
        params.put("callbackClass",callbackClass);
        params.put("callbackKey", callbackKey);
        System.out.println("params : " + JSON.toJSONString(params));
        return (UploadFileUsing) uploadFileUsingDao.getByHQLWithNamedParams(hql,params);
    }

    @Override
    public Boolean checkUploadFileByFilename(String filename) {
        String hql = "select 1 from UploadFileUsing where filename=:filename";
        Map<String,Object> params = new HashMap<>(1);
        params.put("filename",filename);
        return uploadFileUsingDao.getByHQLWithNamedParams(hql,params)!=null;
    }

    @Override
    public void addFileUsing(FileUsingCallback callback, String filename, String displayName, User user, String module) {
        AuthAcc acc = authService.getEnabledAcc(user.getAccId());
        addFileUsing(callback, filename, displayName, user, acc, module);
    }

    @Override
    public void delFileUsing(FileUsingCallback callback, String filename, User user) {
        delFileUsing(callback, filename, user, null);
    }

    @Override
    public void addFileUsing(FileUsingCallback callback, String filename, String displayName, AuthAcc acc, String module) {
        addFileUsing(callback, filename, displayName, null, acc, module);
    }

    @Override
    public void delFileUsing(FileUsingCallback callback, String filename, AuthAcc acc) {
        delFileUsing(callback, filename, null, acc);
    }

    public void addFileUsing(FileUsingCallback callback, String filename, String displayName, User user, AuthAcc acc, String module) {
        if(StringUtils.isEmpty(module)) {
            throw new IllegalArgumentException("module must not be null(模块不能为空)!");
        }
        if(user==null && acc==null && acc.getId()<=0) {
            throw new IllegalArgumentException("User must not be null(操作人不能为空)!");
        }
        UploadFile file = getUploadFile(filename);
        if(file==null) {
            throw new IllegalArgumentException("file not found(找不到文件记录)!\nfilename = " + filename);
        }
        UploadFileUsing using = new UploadFileUsing();
        using.setCallbackClass(callback.getClass().getName());
        using.setCallbackJson(JSON.toJSONString(callback));
        using.setCallbackKey(callback.getKey());
        using.setFilename(file.getFilename());
        using.setDisplayName(displayName);
        if(StringUtils.isNotEmpty(displayName)) {
            //判断是否是第一次引用
            String hql = "select 1 from UploadFileUsing where filename=:filename";
            Map<String, Object> params = new HashMap<>(1);
            params.put("filename", file.getFilename());
            if (uploadFileUsingDao.getByHQLWithNamedParams(hql, params) == null) {
                file.setDisplayName(displayName);
            }
        }
        if(StringUtils.isNotEmpty(file.getGroupUuid())) {
            file.setGroupUuid(null);
        }
        using.setAccId(acc.getId());
        using.setModule(module);
        if (ObjectUtils.isNotEmpty(user)) {
            using.setOrg(user.getOid());
            using.setUserId(user.getUserID());
            using.setUserName(user.getUserName());
        } else {
            using.setUserName(acc.getMobile());
        }
        System.out.println("addFileUsing: "+ JSON.toJSONString(using));
        uploadFileUsingDao.save(using);
        createLog(file,using, user, acc,"添加引用");
    }

    public void delFileUsing(FileUsingCallback callback, String filename, User user, AuthAcc acc) {
        UploadFile file = getUploadFile(filename);
        if(file==null) {
            throw new IllegalArgumentException("File not found(找不到上传文件记录)! filename:"+filename);
        }
        if(user==null && acc==null && acc.getId()<=0) {
            throw new IllegalArgumentException("User must not be null(操作人不能为空)!");
        }
        UploadFileUsing using = getUploadFileUsingByCallbackFilename(callback, file.getFilename());
        UploadFileUsing usingCpy = null;
        String info = "删除引用";
        if(using!=null) {
            usingCpy = SerializationUtils.clone(using);
            uploadFileUsingDao.delete(using);
            System.out.println("delFileUsing id="+ usingCpy.getId() + " JSON: " + JSON.toJSONString(usingCpy));
//        file.setUsingCount(file.getUsingCount()-1);
        } else {
            info += ",File using not found(UploadFileUsing表找不到文件使用记录)!";
            logger.error(info);
        }
        createLog(file,usingCpy,user, acc,info);
    }
    private void createLog(UploadFile file, UploadFileUsing using, User user, AuthAcc acc, String info) {
        LogDto logDto = new LogDto(info, getClass().getName());
        createLog(file,using,user, acc,logDto);
    }
    private void createLog(UploadFile file, UploadFileUsing using, User user, AuthAcc acc, LogDto logDto) {
        UploadFileUsingLog fileLog = new UploadFileUsingLog();
        BeanUtils.copyPropertiesIgnoreNull(file, fileLog);
        BeanUtils.copyPropertiesIgnoreNull(logDto, fileLog);
        if(ObjectUtils.isNotEmpty(using)) {
            BeanUtils.copyPropertiesIgnoreNull(using, fileLog);
            fileLog.setUploadFileUsingId(using.getId());
        }
        if(ObjectUtils.isNotEmpty(acc)) {
            fileLog.setAccId(acc.getId());
            fileLog.setUserName(acc.getMobile());
        }
        if(ObjectUtils.isNotEmpty(user)) {
            fileLog.setUserId(user.getUserID());
            fileLog.setUserName(user.getUserName());
        }
        fileLog.setId(null);
        fileLog.setCreateDate(null);
        uploadFileUsingLogDao.save(fileLog);
    }

    @Override
    public UploadFile copyFile(String filename, AuthAcc acc, String module, HttpServletRequest request) throws IOException {
        UploadFile file = uploadFileDao.get(filename);
        if(authService.checkAcc(acc)) {
            throw new IllegalArgumentException("账号错误");
        }
        if(file.getOrg()==null && acc.getId().equals(file.getAccId())) {
            throw new IllegalArgumentException("同领地无需复制文件");
        }
        File oldFile = getRealFile(file);
        if(ObjectUtils.isEmpty(oldFile)) {
            throw new IllegalArgumentException("原始文件不存在");
        }
        ResPathDto pathDto = new ResPathDto(null, acc.getId());
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String newFilename =  uuid + "." + file.getLastName();
        UploadFile uploadFile = new UploadFile();
        BeanUtils.copyPropertiesIgnoreNull(file, uploadFile);
        uploadFile.setRealPath(pathDto.getAppendPath() + pathDto.separator + newFilename);
        uploadFile.setFilename(uploadFile.getRealPath().replace(pathDto.separator, "/"));
        uploadFile.setUserId(null);
        uploadFile.setUserName(acc.getMobile());
        uploadFile.setOrg(null);
        uploadFile.setAccId(acc.getId());
        uploadFile.setModule(module);
        File dir = new File(pathDto.getRootPath() + pathDto.getAppendPath());
        copyFile(oldFile, dir, newFilename);
        LogDto logDto = new LogDto(request, "复制文件");
        saveUploadFile(uploadFile, null, acc, logDto);
        return uploadFile;
    }

    @Override
    public UploadFile copyFileFromUrl(HttpServletRequest request, String url, User user, AuthAcc acc, String displayName, String lastName, String module) {
        if(StringUtils.isEmpty(module)) {
            throw new IllegalArgumentException("module must not be null(模块不能为空)!");
        }
        if(user==null && acc==null && acc.getId()<=0) {
            throw new IllegalArgumentException("User must not be null(操作人不能为空)!");
        }
        ResPathDto pathDto = new ResPathDto(request, user, acc);
        pathDto.mkdir();
        HttpClientUtils utils = new HttpClientUtils(url);
        Pair<File, String> pair = utils.doDownload(null, url, null, null);
        if (StringUtils.isNotEmpty(pair.getRight())) {
            lastName = pair.getRight();
        }
        if(StringUtils.isEmpty(lastName)) {
            throw new IllegalArgumentException("文件扩展名lastName不能为空");
        }
        File file = pair.getLeft();
        if(!file.isFile()) {
            throw new IllegalArgumentException("file not found(找不到下载文件)!");
        }
        String originalFilename = copyFileFromUrlOriginalFilename(url, lastName);
        ResFileDto fileDto = new ResFileDto(originalFilename);
        File toFile = new File(pathDto.getFullPath() + fileDto.getFileName());
        mvFile(file, toFile);
        UploadFile uploadFile = createOrUpdateUploadFile(fileDto, pathDto, toFile, module, null, user, acc, null);
        LogDto logDto = new LogDto(request, "远程抓取");
        saveUploadFile(uploadFile, user, acc, logDto);
        return uploadFile;
    }
    private final void mvFile(File srcFile, File dstFile) {
        if(!srcFile.renameTo(dstFile)) {
            try {
                FileUtils.moveFile(srcFile, dstFile);
                System.out.println("copyFileFromUrl renameTo error and moveFile OK：" + srcFile.getAbsolutePath() + dstFile.getAbsolutePath());
            } catch (IOException e) {
                logger.info("copyFileFromUrl moveFile IOException：" + srcFile.getAbsolutePath() + dstFile.getAbsolutePath(), e);
                try {
                    FileUtils.copyFile(srcFile, dstFile);
                } catch (IOException ex) {
                    logger.error("copyFileFromUrl copyFile IOException：" + srcFile.getAbsolutePath() + dstFile.getAbsolutePath(), ex);
                }
            }
        }
        if(srcFile.exists()) {
            srcFile.delete();
        }
    }
    private final String copyFileFromUrlOriginalFilename(String url, String lastName) {
        if(url.endsWith("." + lastName)) {
            return url.substring(0, url.length()-lastName.length()) + lastName;
        } else {
            return url + "." +lastName;
        }
    }
    @Override
    public UploadFile createOrUpdateUploadFile(ResFileDto fileDto, ResPathDto pathDto, File file, String module, String groupUuid, User user, AuthAcc acc, UploadFile uploadFile) {
        if (uploadFile == null) {
            uploadFile = new UploadFile();
        }
        uploadFile.setOriginalFilename(fileDto.getOriginalFilename());
        if (fileDto.getExtName().isEmpty() || fileDto.getExtName().equals(fileDto.getOriginalFilename())) {
            uploadFile.setDisplayName(fileDto.getOriginalFilename());
        } else {
            uploadFile.setDisplayName(fileDto.getOriginalFilename().substring(0, fileDto.getOriginalFilename().length() - fileDto.getExtName().length() - 1));
        }
        uploadFile.setRealPath(pathDto.getAppendPath() + pathDto.separator + fileDto.getFileName());
        uploadFile.setFilename(uploadFile.getRealPath().replace(pathDto.separator, "/"));
        uploadFile.setLastName(fileDto.getExtName());
        uploadFile.setSize(file.length());
        uploadFile.setHash(DigestUtils.digest(file, DigestUtils.SHA512));
        if(user != null) {
            uploadFile.setUserId(user.getUserID());
            uploadFile.setUserName(user.getUserName());
            uploadFile.setOrg(user.getOid());
        }
        uploadFile.setAccId(acc.getId());
        uploadFile.setModule(module);
        uploadFile.setGroupUuid(groupUuid);
        return uploadFile;
    }

    @Override
    public void checkUploadTask(Integer readLimit, Integer updateLimit) {
        long now=System.currentTimeMillis();
        Integer oid=Integer.MIN_VALUE;
        updateLimit = updateLimit!=null?updateLimit:1000;
        readLimit = readLimit!=null?readLimit:500;
        List<Organization> orgs = orgService.getOrgLimit(oid,readLimit);
        while (orgs.size()>0){
            System.out.println("checkUploadTask from oid = " + orgs.get(0).getId() + " and size = " + orgs.size());
            for (Organization o : orgs) {
                oid = o.getId();
                resetJunkFileByOid(oid, updateLimit);
            }
            orgs = orgService.getOrgLimit(oid,readLimit);
        }
        resetAccJunkFile(updateLimit);
        delCaches(System.currentTimeMillis(), 5);
        String duration = TimeUtils.toTimeString(System.currentTimeMillis()-now);
        System.out.println("checkUploadTask finished Duration : " + duration);
    }
    private Long accJunkFileCount() {
        String hql = "select count(*) from UploadFile f left join UploadFileUsing u on f.filename=u.filename where u.id is null and f.org is null and f.createDate<:createDate";
        Map<String, Object> params = new HashMap<>(1);
        params.put("createDate", getJunkFilesTime());
        return (Long) uploadFileDao.getByHQLWithNamedParams(hql,params);
    }
    private List<String> accJunkFileList(String lastId,Integer limit) {
        String hql = "select f.filename from UploadFile f left join UploadFileUsing u on f.filename=u.filename where u.id is null and f.org is null and f.createDate<:createDate and f.filename>:filename";
        Map<String, Object> params = new HashMap<>(2);
        params.put("filename",lastId);
        params.put("createDate", getJunkFilesTime());
        return uploadFileDao.getListByHQLWithNamedParamsLimit(hql,params,limit);
    }
    private void resetAccJunkFile(Integer updateLimit) {
        final Long total = accJunkFileCount();
        if(total>0L) {
            Long cleanedCount = 0L;
            Long cleanedSize = 0L;
            Transaction transaction = uploadFileDao.getSession().getTransaction();
            boolean commitBegin = true;
            if (transaction.getStatus().equals(TransactionStatus.NOT_ACTIVE)) {
                commitBegin = false;
                transaction.begin();
            }
            String fileStart = "";
            LogDto logDto = new LogDto("清除领地垃圾文件", getClass().getName());
            List<String> junkFiles;
            while (!(junkFiles = accJunkFileList(fileStart, readLimit)).isEmpty()) {
                for (String filename : junkFiles) {
                    UploadFile uploadFile = uploadFileDao.get(filename);
                    File file = new File(ResPathDto.getRootPath() + uploadFile.getRealPath());
                    if (file.exists()) {
                        file.delete();
                        checkAndDelDir(file.getParentFile());
                    }
                    fileStart = uploadFile.getFilename();
                    cleanedSize += uploadFile.getSize();
                    AuthAcc acc = null;
                    if (ObjectUtils.isNotEmpty(uploadFile.getAccId()) && uploadFile.getAccId().longValue() > 0L) {
                        acc = authService.getEnabledOrDisabledAcc(uploadFile.getAccId());
                    }
                    delUploadFile(uploadFile, null, acc, logDto);
                    if (++cleanedCount >= updateLimit) {
                        transaction.commit();
                        transaction.begin();
                    }
                }
            }
            UploadJunkFileClean clean = new UploadJunkFileClean();
            clean.setUserName("System");
            clean.setSize(cleanedSize);
            uploadJunkFileCleanDao.save(clean);
            if (transaction.getStatus().equals(TransactionStatus.ACTIVE)) {
                transaction.commit();
            }
            if (commitBegin) {
                transaction.begin();
            }
        }
    }
    private void delCaches(long now, int days) {
        String tempPathRoot = ResPathDto.getTempPath();
        File rootDir = new File(tempPathRoot);
        if (rootDir.isDirectory()) {
            File[] subPaths = rootDir.listFiles();
            for (File tempDir : subPaths) {
                long lastModified = tempDir.lastModified();
                if (tempDir.isDirectory()) {
                    File[] files = tempDir.listFiles();
                    if (files.length > 0) {
                        long subMaxModified = Arrays.stream(files).max((f1, f2) -> {
                            return f1.lastModified() >= f2.lastModified() ? 1 : -1;
                        }).get().lastModified();
                        lastModified = lastModified > subMaxModified ? lastModified : subMaxModified;
                    }
                    if (now - lastModified > TimeUnit.DAYS.toMillis(days)) {
                        try {
                            FileUtils.deleteDirectory(tempDir);
                        } catch (IOException e) {
                            Logger.getLogger(getClass()).warn("delCaches 删除超过"+days+"天的缓存目录报错", e);
                        }
                    }
                }
            }
        }
    }
    private Pair<String, Boolean> checkUploadOrg(Integer oid, String startId, Integer updateLimit) {
        boolean changed = false;
        Long now = System.currentTimeMillis();
        List<UploadFile> list=getUploadFileLimit(oid, startId, updateLimit);
        System.out.println("checkUploadTask checkUploadOrg oid = " + oid + " and file size = " + list.size());
        if (ObjectUtils.isNotEmpty(list)) {
            for (UploadFile file : list) {
                boolean change = false;
                if (now - file.getCreateDate().getTime() > TimeUnit.DAYS.toMillis(3)//wyu：上传超过三天
                        && (change = (checkFileUsing(file) != null))) {
                    //wyu:Todo no using
//                System.out.println("check no using:"+file.getFilename());
                }
                changed = changed || change;
                startId = file.getFilename();
            }
        } else {
            startId = "";
        }
        return Pair.of(startId, Boolean.valueOf(changed));
    }

    private List<UploadFile> getUploadFileLimit(Integer oid, String filename, Integer limit) {
//        System.out.println(filename + " | " + limit.toString());
        String hql = "from UploadFile where org=:org and filename>:filename order by filename";
        Map<String,Object> params = new HashMap<>(2);
        params.put("org",  oid);
        params.put("filename",  filename);
        return uploadFileDao.getListByHQLWithNamedParamsLimit(hql,params,limit);
    }

    private Boolean checkFileUsing(UploadFile file) {
        String hql = "from UploadFileUsing where filename=:filename";
        Map<String,Object> params = new HashMap<>(1);
        params.put("filename",file.getFilename());
        List<UploadFileUsing> list = uploadFileUsingDao.getListByHQLWithNamedParams(hql, params);
        System.out.println("checkFileUsing.file: " + file.getFilename());
        for(UploadFileUsing using : list) {
            try {
                FileUsingCallback callback = JSON.parseObject(using.getCallbackJson(), (Type) Class.forName(using.getCallbackClass()));
                System.out.println("checkFileUsing.callback: "+JSON.toJSONString(callback));
                boolean used = false;
                try{
                    used = callback.checkUsing(using.getFilename());
                } catch (Exception e) {
                    logger.error("反射调用 checkFileUsing 回调接口异常, class = "+callback.getClass().getName()+",  using id="+using.getId(),e);
                }
                if(!used) {
                    User user = new User();
                    user.setUserID(null);
                    user.setUserName("定时任务");
                    delFileUsing(callback, file.getFilename(), user, null);
                }
                return used;
            } catch (ClassNotFoundException e) {
                logger.error("checkFileUsing 找不到对象类",e);
            } catch (JSONException e) {
                logger.error("checkFileUsing 还原对象异常,可能对象结构已变, using id="+using.getId(),e);
            } catch (Exception e) {
                logger.error("checkFileUsing 异常, using id="+using.getId(),e);
            }
        }
        return null;
    }

//    @Override
//    public void updateUsingCount(Integer oid) {
//        StringBuffer sqlZero = new StringBuffer("update t_upload_file f left join t_upload_file_using u on f.filename=u.filename set f.using_count=0 where u.id is null");
//        String sql;
//        HashMap<String, Object> params = new HashMap<>();
//        if(oid==null) {
//            sql ="update t_ t_upload_file join (select filename,count(*) using_count from t_upload_file_using group by filename) u ON f.filename=u.filename set f.using_count=u.using_count";
//        } else {
//            sqlZero.append(" and f.org=:oid");
//            sql ="update t_upload_file f join (select filename,count(*) using_count from t_upload_file_using where org=:oid group by filename) u ON f.filename=u.filename set f.using_count=u.using_count where f.org=:oid";
//            params.put("oid",oid);
//        }
//        System.out.println(uploadFileDao.querySql(sqlZero.toString(),params));
//        System.out.println(uploadFileDao.querySql(sql,params));
//    }

    @Override
    public List<Integer> getUploadFileUids(Integer oid) {
        String hql = "select distinct userId from UploadFile where org=:org order by userId";
        Map<String,Object> params = new HashMap<>(1);
        params.put("org", oid);
        return uploadFileDao.getListByHQLWithNamedParams(hql,params);
    }

    @Override
    public List<String> getUploadFileModules(Integer oid) {
        checkOrg(oid);
        String hql = "select distinct module from UploadFile where org=:org order by convert(module, 'GBK')";
        Map<String,Object> params = new HashMap<>(1);
        params.put("org", oid);
        return uploadFileDao.getListByHQLWithNamedParams(hql,params);
    }
    @Override
    public List<String> getUploadFileModules(AuthAcc acc) {
        String hql = "select distinct module from UploadFile where org is null and userId is null and accId=:accId order by convert(module, 'GBK')";
        Map<String,Object> params = new HashMap<>(1);
        params.put("accId", acc.getId());
        return uploadFileDao.getListByHQLWithNamedParams(hql,params);
    }


    @Override
    public List<Integer> getJunkFileUids(Integer oid) {
        checkOrg(oid);
        String hql = "select distinct f.userId from UploadFile f left join UploadFileUsing u on f.filename=u.filename where u.id is null and f.org=:org and f.createDate<:createDate order by f.userId";
        Map<String,Object> params = new HashMap<>(2);
        params.put("org", oid);
        params.put("createDate", getJunkFilesTime());
        return uploadFileDao.getListByHQLWithNamedParams(hql,params);
    }

    @Override
    public List<String> getJunkFileModules(Integer oid) {
        checkOrg(oid);
        String hql = "select distinct f.module from UploadFile f left join UploadFileUsing u on f.filename=u.filename where u.id is null and f.org=:org and f.createDate<:createDate order by convert(f.module, 'GBK')";
        Map<String,Object> params = new HashMap<>(2);
        params.put("org", oid);
        params.put("createDate", getJunkFilesTime());
        return uploadFileDao.getListByHQLWithNamedParams(hql,params);
    }

    @Override
    public Long uploadFileSize(Integer oid, User user, String lastName, String module) {
//        StringBuffer hql = new StringBuffer("select coalesce(sum(f.size),0) from UploadFile f where f.filename in (select distinct filename from UploadFileUsing where org=:org)");
        StringBuffer hql = new StringBuffer("select coalesce(sum(f.size),0) from UploadFile f where f.org=:org");
        Map<String, Object> params = fillParams(hql,oid,user,lastName,module);
        return (Long) uploadFileDao.getByHQLWithNamedParams(hql.toString(),params);
    }

    @Override
    public Long uploadFileSize(Integer oid, User user, List<String> notlastName, String module) {
        StringBuffer hql = new StringBuffer("select coalesce(sum(f.size),0) from UploadFile f where f.org=:org");
        Map<String, Object> params = fillParams(hql,oid,user,notlastName,module);
        return (Long) uploadFileDao.getByHQLWithNamedParams(hql.toString(),params);
    }

    @Override
    public Long uploadFileSize(AuthAcc acc, String lastName, String module) {
        StringBuffer hql = new StringBuffer("select coalesce(sum(f.size),0) from UploadFile f where f.org is null and f.userId is null");
        Map<String, Object> params = fillParams(hql,acc,lastName,module);
        return (Long) uploadFileDao.getByHQLWithNamedParams(hql.toString(),params);
    }

    @Override
    public Long uploadFileSize(AuthAcc acc, List<String> notlastName, String module) {
        StringBuffer hql = new StringBuffer("select coalesce(sum(f.size),0) from UploadFile f where f.org is null and f.userId is null");
        Map<String, Object> params = fillParams(hql,acc,notlastName,module);
        return (Long) uploadFileDao.getByHQLWithNamedParams(hql.toString(),params);
    }

    @Override
    public Long junkFileSize(Integer oid, User user, String lastName, String module) {
//        resetJunkFileByOid(oid,true, updateLimit);
        StringBuffer hql = new StringBuffer("select coalesce(sum(f.size),0) from UploadFile f left join UploadFileUsing u on f.filename=u.filename where u.id is null and f.org=:org and f.createDate<:createDate");
        Map<String, Object> params = fillParams(hql,oid,user,lastName,module);
        params.put("createDate", getJunkFilesTime());
        return (Long) uploadFileDao.getByHQLWithNamedParams(hql.toString(),params);
    }

    @Override
    public Long junkFileSize(Integer oid, User user, List<String> notlastName, String module) {
//        resetJunkFileByOid(oid,true, updateLimit);
        StringBuffer hql = new StringBuffer("select coalesce(sum(f.size),0) from UploadFile f left join UploadFileUsing u on f.filename=u.filename where u.id is null and f.org=:org and f.createDate<:createDate");
        Map<String, Object> params = fillParams(hql,oid,user,notlastName,module);
        params.put("createDate", getJunkFilesTime());
        return (Long) uploadFileDao.getByHQLWithNamedParams(hql.toString(),params);
    }

    @Override
    public Long junkFileSize(AuthAcc acc, String lastName, String module) {
        StringBuffer hql = new StringBuffer("select coalesce(sum(f.size),0) from UploadFile f left join UploadFileUsing u on f.filename=u.filename where u.id is null and f.org is null and f.userId is null and f.accId=:accId and f.createDate<:createDate");
        Map<String, Object> params = fillParams(hql,acc,lastName,module);
        params.put("createDate", getJunkFilesTime());
        return (Long) uploadFileDao.getByHQLWithNamedParams(hql.toString(),params);
    }

    @Override
    public Long junkFileSize(AuthAcc acc, List<String> notlastName, String module) {
        StringBuffer hql = new StringBuffer("select coalesce(sum(f.size),0) from UploadFile f left join UploadFileUsing u on f.filename=u.filename where u.id is null and f.org is null and f.userId is null and f.accId=:accId and f.createDate<:createDate");
        Map<String, Object> params = fillParams(hql,acc,notlastName,module);
        params.put("createDate", getJunkFilesTime());
        return (Long) uploadFileDao.getByHQLWithNamedParams(hql.toString(),params);
    }

    private void resetJunkFileByOid(Integer oid, Integer updateLimit) {
        //wyu：Todo 检查磁盘文件
        //wyu:Todo 检查磁盘文件：1、检查缓存文件，删除超过5天的缓存文件。
        //wyu:Todo 检查磁盘文件：2、检查未入库文件，添加为垃圾文件。
        //wyu：Todo 检查磁盘文件：3、检查非法目录，上线一月后删除非法目录。
        String fileStart = "";
        Pair<String, Boolean> checkResult;
        Transaction transaction = uploadFileDao.getSession().getTransaction();
        boolean commitBegin;
        if (transaction.getStatus().equals(TransactionStatus.NOT_ACTIVE)) {
            commitBegin = false;
            transaction.begin();
        } else {
            commitBegin = true;
        }
        while ((checkResult = checkUploadOrg(oid, fileStart, updateLimit)) != null
                && StringUtils.isNotEmpty(fileStart = checkResult.getLeft())) {
            if(checkResult.getRight()) {
                transaction.commit();
                transaction.begin();
            }
        }
        if (!commitBegin && transaction.getStatus().equals(TransactionStatus.ACTIVE)) {
            transaction.commit();
        }
    }
    @Override
    public List<UploadFile> uploadFileList(Integer oid, User user, String lastName, String module, PageInfo pageInfo) {
        StringBuffer hql = new StringBuffer("from UploadFile f where f.org=:org");
        Map<String, Object> params = fillParams(hql,oid,user,lastName,module);
        return uploadFileDao.getListByHQLWithNamedParams(hql.toString(),params, pageInfo);
    }

    @Override
    public List<UploadFile> uploadFileList(Integer oid, User user, List<String> notlastName, String module, PageInfo pageInfo) {
        StringBuffer hql = new StringBuffer("from UploadFile f where f.org=:org");
        Map<String, Object> params = fillParams(hql,oid,user,notlastName,module);
        return uploadFileDao.getListByHQLWithNamedParams(hql.toString(),params, pageInfo);
    }

    @Override
    public List<UploadFile> uploadFileList(AuthAcc acc, List<String> notlastName, String module, PageInfo pageInfo) {
        StringBuffer hql = new StringBuffer("from UploadFile f where f.org is null and userId is null");
        Map<String, Object> params = fillParams(hql,acc,notlastName,module);
        return uploadFileDao.getListByHQLWithNamedParams(hql.toString(),params, pageInfo);
    }

    @Override
    public List<UploadFile> uploadFileList(AuthAcc acc, String lastName, String module, PageInfo pageInfo) {
        StringBuffer hql = new StringBuffer("from UploadFile f where f.org is null and userId is null");
        Map<String, Object> params = fillParams(hql,acc,lastName,module);
        return uploadFileDao.getListByHQLWithNamedParams(hql.toString(),params, pageInfo);
    }

    private Map<String, Object> fillParams(StringBuffer hql, Integer oid, User user, String module) {
        checkOrg(oid);
        Map<String, Object> params = new HashMap<>();
        params.put("org",oid);
        if(user!=null){
            hql.append(" and f.userId=:userId");
            params.put("userId", user.getUserID());
        }
        if(StringUtils.isNotEmpty(module)) {
            hql.append(" and f.module=:module");
            params.put("module", module);
        }
        return params;
    }
    private Map<String, Object> fillParams(StringBuffer hql, AuthAcc acc, String module) {
        Map<String, Object> params = new HashMap<>();
        if(acc!=null){
            hql.append(" and f.accId=:accId");
            params.put("accId", acc.getId());
        }
        if(StringUtils.isNotEmpty(module)) {
            hql.append(" and f.module=:module");
            params.put("module", module);
        }
        return params;
    }
    private Map<String, Object> fillParams(StringBuffer hql, Integer oid, User user, String lastName, String module) {
        Map<String, Object> params = fillParams(hql, oid, user, module);
        if(StringUtils.isNotEmpty(lastName)) {
            hql.append(" and f.lastName=:lastName");
            params.put("lastName", lastName);
        }
        return params;
    }
    private Map<String, Object> fillParams(StringBuffer hql, Integer oid, User user, List<String> notlastName, String module) {
        Map<String, Object> params = fillParams(hql, oid, user, module);
        if(notlastName!=null && !notlastName.isEmpty()) {
            hql.append(" and f.lastName not in (:notlastName)");
            params.put("notlastName", notlastName);
        }
        return params;
    }
    private Map<String, Object> fillParams(StringBuffer hql, AuthAcc acc, String lastName, String module) {
        Map<String, Object> params = fillParams(hql, acc, module);
        if(StringUtils.isNotEmpty(lastName)) {
            hql.append(" and f.lastName=:lastName");
            params.put("lastName", lastName);
        }
        return params;
    }
    private Map<String, Object> fillParams(StringBuffer hql, AuthAcc acc, List<String> notlastName, String module) {
        Map<String, Object> params = fillParams(hql, acc, module);
        if(notlastName!=null && !notlastName.isEmpty()) {
            hql.append(" and f.lastName not in (:notlastName)");
            params.put("notlastName", notlastName);
        }
        return params;
    }
    private void checkOrg(Integer oid) {
        if(oid==null || orgService.getByOid(oid, Boolean.TRUE, Boolean.FALSE, null)==null) {
            throw new IllegalArgumentException("Organization can not be null(机构不能为空)");
        }
    }

    @Override
    public void cleanOrgJunkFile(User user, CleanJunkFilesListener listener) {
        if(user.getOid()==null) {
            throw new IllegalArgumentException("org must not be null(用户的机构不能为空)!");
        }
        taskExecutor.execute(new CleanThread(user,listener));
    }

    @Override
    public void cleanAccJunkFile(AuthAcc acc, CleanJunkFilesListener listener) {
        taskExecutor.execute(new CleanThread(acc,listener));
    }

    private List<String> junkFileList(Integer oid, String lastId, Integer limit) {
        String hql = "select f.filename from UploadFile f left join UploadFileUsing u on f.filename=u.filename where u.id is null and f.org=:org and f.createDate<:createDate and f.filename>:filename";
        Map<String, Object> params = new HashMap<>(3);
        params.put("org",oid);
        params.put("filename",lastId);
        params.put("createDate", getJunkFilesTime());
        return uploadFileDao.getListByHQLWithNamedParamsLimit(hql,params,limit);
    }
    private List<String> junkFileList(AuthAcc acc, String lastId, Integer limit) {
        String hql = "select f.filename from UploadFile f left join UploadFileUsing u on f.filename=u.filename where u.id is null and f.org is null and f.userId is null and f.accId=:accId and f.createDate<:createDate and f.filename>:filename";
        Map<String, Object> params = new HashMap<>(3);
        params.put("accId",acc.getId());
        params.put("filename",lastId);
        params.put("createDate", getJunkFilesTime());
        return uploadFileDao.getListByHQLWithNamedParamsLimit(hql,params,limit);
    }
    private Long junkFileCount(Integer oid) {
        String hql = "select count(*) from UploadFile f left join UploadFileUsing u on f.filename=u.filename where u.id is null and f.org=:org and f.createDate<:createDate";
        Map<String, Object> params = new HashMap<>(2);
        params.put("org",oid);
        params.put("createDate", getJunkFilesTime());
        return (Long) uploadFileDao.getByHQLWithNamedParams(hql,params);
    }
    private Long junkFileCount(AuthAcc acc) {
        String hql = "select count(*) from UploadFile f left join UploadFileUsing u on f.filename=u.filename where u.id is null and f.org is null and f.userId is null and f.accId=:accId and f.createDate<:createDate";
        Map<String, Object> params = new HashMap<>(2);
        params.put("accId",acc.getId());
        params.put("createDate", getJunkFilesTime());
        return (Long) uploadFileDao.getByHQLWithNamedParams(hql,params);
    }

    @Override
    public void cleanOrgJunkFileByUser(User user, CleanJunkFilesListener listener) {
        final double prePercent = 1;
        final double mainPercent = 99;
        listener.progress(0.0);
        Long cleanedCount = 0L;
        Long cleanedSize = 0L;
        Integer oid = user.getOid();
//        resetJunkFileByOid(oid,true, updateLimit);
        Transaction transaction = uploadFileDao.getSession().getTransaction();
        boolean commitBegin = true;
        if (transaction.getStatus().equals(TransactionStatus.NOT_ACTIVE)) {
            commitBegin = false;
            transaction.begin();
        }
        String fileStart = "";
        listener.progress(prePercent);
        final Long total = junkFileCount(oid);
        LogDto logDto = new LogDto("清除垃圾文件", getClass().getName());
        List<String> junkFiles;
        while (!(junkFiles = junkFileList(oid, fileStart, readLimit)).isEmpty()) {
            for (String filename : junkFiles) {
                UploadFile uploadFile = uploadFileDao.get(filename);
                File file = new File(ResPathDto.getRootPath()+uploadFile.getRealPath());
                if(file.exists()) {
                    file.delete();
                    checkAndDelDir(file.getParentFile());
                }
                fileStart = uploadFile.getFilename();
                cleanedSize += uploadFile.getSize();
                delUploadFile(uploadFile, user, null, logDto);
                if(++cleanedCount>=updateLimit) {
                    transaction.commit();
                    transaction.begin();
                }
                listener.progress(prePercent + cleanedCount*mainPercent/total);
            }
        }
        UploadJunkFileClean clean = new UploadJunkFileClean();
        clean.setOrg(oid);
        clean.setUserId(user.getUserID());
        clean.setUserName(user.getUserName());
        clean.setSize(cleanedSize);
        uploadJunkFileCleanDao.save(clean);
        if (transaction.getStatus().equals(TransactionStatus.ACTIVE)) {
            transaction.commit();
        }
        if (commitBegin) {
            transaction.begin();
        }
        listener.progress(100.0);
        listener.finished(cleanedSize);
    }

    @Override
    public void cleanOrgJunkFileByAcc(AuthAcc acc, CleanJunkFilesListener listener) {
        final double prePercent = 1;
        final double mainPercent = 99;
        listener.progress(0.0);
        Long cleanedCount = 0L;
        Long cleanedSize = 0L;
//        resetJunkFileByOid(oid,true, updateLimit);
        Transaction transaction = uploadFileDao.getSession().getTransaction();
        boolean commitBegin = true;
        if (transaction.getStatus().equals(TransactionStatus.NOT_ACTIVE)) {
            commitBegin = false;
            transaction.begin();
        }
        String fileStart = "";
        listener.progress(prePercent);
        final Long total = junkFileCount(acc);
        LogDto logDto = new LogDto("清除垃圾文件", getClass().getName());
        List<String> junkFiles;
        while (!(junkFiles = junkFileList(acc, fileStart, readLimit)).isEmpty()) {
            for (String filename : junkFiles) {
                UploadFile uploadFile = uploadFileDao.get(filename);
                File file = new File(ResPathDto.getRootPath()+uploadFile.getRealPath());
                if(file.exists()) {
                    file.delete();
                    checkAndDelDir(file.getParentFile());
                }
                fileStart = uploadFile.getFilename();
                cleanedSize += uploadFile.getSize();
                delUploadFile(uploadFile, null, acc, logDto);
                if(++cleanedCount>=updateLimit) {
                    transaction.commit();
                    transaction.begin();
                }
                listener.progress(prePercent + cleanedCount*mainPercent/total);
            }
        }
        UploadJunkFileClean clean = new UploadJunkFileClean();
        clean.setAccId(acc.getId());
        clean.setUserName(acc.getName());
        clean.setSize(cleanedSize);
        uploadJunkFileCleanDao.save(clean);
        if (transaction.getStatus().equals(TransactionStatus.ACTIVE)) {
            transaction.commit();
        }
        if (commitBegin) {
            transaction.begin();
        }
        listener.progress(100.0);
        listener.finished(cleanedSize);
    }

    private void checkAndDelDir(File dir) {
        if(!ResPathDto.getRootPath().equals(dir.getAbsolutePath())
                && dir.isDirectory()
                && dir.list().length==0) {
            dir.delete();
        }
    }
    private Date getJunkFilesTime() {
        return new Date(System.currentTimeMillis()-TimeUnit.DAYS.toMillis(5));
    }

    @Override
    public List<UploadJunkFileClean> getCleanJunkListByOid(Integer oid, PageInfo pageInfo) {
        String hql = "from UploadJunkFileClean where org=:org order by cleanedDate desc";
        Map<String, Object> params = new HashMap<>(1);
        params.put("org",oid);
        return uploadJunkFileCleanDao.getListByHQLWithNamedParams(hql,params,pageInfo);
    }

    @Override
    public List<UploadJunkFileClean> getCleanJunkListByAcc(AuthAcc acc, PageInfo pageInfo) {
        String hql = "from UploadJunkFileClean where accId=:accId order by cleanedDate desc";
        Map<String, Object> params = new HashMap<>(1);
        params.put("accId",acc.getId());
        return uploadJunkFileCleanDao.getListByHQLWithNamedParams(hql,params,pageInfo);
    }

    private File getRealFile(UploadFile file) {
        Organization org;
        Long fileAccId=null;
        if(file==null || (org = orgService.getByOid(file.getOrg())) == null && (fileAccId = file.getAccId()) == null ) {
            throw new IllegalArgumentException("文件不存在或者文件不合法！");
        }
        if(org!=null && !"NFS".equalsIgnoreCase(org.getUploadStorageType())) {
            throw new IllegalArgumentException("不支持的机构文件存储类型！");
//            return null;//TODO:其他企业存储后端未实现。
        }
        ResPathDto pathDto = new ResPathDto(org, fileAccId);
        File result = new File(pathDto.getLoadpath() + pathDto.separator + file.getRealPath());
        if(!result.exists()) {
            throw new IllegalArgumentException("文件不存在！");
        }
        return result;
    }

    private File copyFile(File oldFile, File dir, String newFilename) throws IOException {
        if(oldFile==null) {
            return null;
        }
        if(!dir.exists()) {
            dir.mkdirs();
        }
        String path;
        if(dir.getAbsolutePath().endsWith(File.separator)) {
            path = dir.getAbsolutePath() + newFilename;
        } else {
            path = dir.getAbsolutePath() + File.separator +newFilename;
        }
        File newFile = new File(path);
        FileUtils.copyFile(oldFile,newFile);
        return newFile;
    }

    @Override
    public UploadFile copyFile(String filename, User user, String module, HttpServletRequest request) throws IOException {
        UploadFile file = uploadFileDao.get(filename);
        Organization org;
        if(ObjectUtils.isEmpty(user) || ObjectUtils.isEmpty(org = user.getOrganization())) {
            throw new IllegalArgumentException("用户或用户机构错误");
        }
        if(org.getId().equals(file.getOrg())) {
            throw new IllegalArgumentException("同机构无需复制文件");
        }
        File oldFile = getRealFile(file);
        if(ObjectUtils.isEmpty(oldFile)) {
            throw new IllegalArgumentException("原始文件不存在");
        }
        ResPathDto pathDto = new ResPathDto(org, null);
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String newFilename =  uuid + "." + file.getLastName();
        UploadFile uploadFile = new UploadFile();
        BeanUtils.copyPropertiesIgnoreNull(file, uploadFile);
        uploadFile.setRealPath(pathDto.getAppendPath() + pathDto.separator + newFilename);
        uploadFile.setFilename(uploadFile.getRealPath().replace(pathDto.separator, "/"));
        uploadFile.setUserId(user.getUserID());
        uploadFile.setUserName(user.getUserName());
        uploadFile.setOrg(user.getOid());
        uploadFile.setAccId(null);
        uploadFile.setModule(module);
        File dir = new File(pathDto.getRootPath() + pathDto.getAppendPath());
        copyFile(oldFile, dir, newFilename);
        LogDto logDto = new LogDto(request, "复制文件");
        logDto.setOriginalFilename(file.getFilename());
        saveUploadFile(uploadFile, user, authService.getEnabledOrDisabledAcc(user.getAccId()), logDto);
        return uploadFile;
    }

    @Override
    public File copyTempFile(String filename) throws IOException {
        UploadFile file = uploadFileDao.get(filename);
        File oldFile = getRealFile(file);
        String tempPath = ResPathDto.getTempPath();
        File dir = new File(tempPath + UUID.randomUUID().toString().replaceAll("-", ""));
        while(dir.exists()) {
            dir = new File(tempPath + UUID.randomUUID().toString().replaceAll("-", ""));
        }
        dir.mkdirs();
        return copyFile(oldFile, dir, file.getFilename().substring(file.getFilename().lastIndexOf('/')+1));
    }
}
class CleanThread implements Runnable {
    private CleanJunkFilesListener listener;
    private User user;
    private AuthAcc acc;

    public CleanThread() {
        user = null;
        acc = null;
    }

    public CleanThread(User user, CleanJunkFilesListener listener) {
        this();
        this.user = user;
        this.listener = listener;
    }

    public CleanThread(AuthAcc acc, CleanJunkFilesListener listener) {
        this();
        this.acc = acc;
        this.listener = listener;
    }

    @Override
    public void run() {
        ServletContext sc = ContextLoader.getCurrentWebApplicationContext().getServletContext();
        ApplicationContext ac = WebApplicationContextUtils.getRequiredWebApplicationContext(sc);
        UploadService service = ac.getBean(UploadService.class, "uploadService");
        if(this.user!=null) {
            service.cleanOrgJunkFileByUser(user, listener);
        }
        if(this.acc!=null) {
            service.cleanOrgJunkFileByAcc(acc, listener);
        }
    }
}