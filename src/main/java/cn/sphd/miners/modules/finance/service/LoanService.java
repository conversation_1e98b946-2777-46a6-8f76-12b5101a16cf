package cn.sphd.miners.modules.finance.service;

import cn.sphd.miners.modules.finance.entity.*;
import cn.sphd.miners.modules.sales.model.PoOrders;
import cn.sphd.miners.modules.system.entity.User;

import javax.servlet.http.HttpSession;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2018/12/20.
 */
public interface LoanService {

    //借款收入(财务的新增借款收入)
    Map<String,Object> addLoanCredit(Integer userId,Integer business, String method, Double money, Date receiveAccountDate, Integer accountId,
                                     String returnNo, Date expireDate, String originalCorp, String bankName, String memo,String loanType,String operatorName,
                                     String partnerName,String withinOrAbroad, Date paymentDate,Integer returnId,String oppositeCorp,String payer);

    //借款支出(财务的新增借款支出)
    Map<String,Object> addLoanDebit(Integer userId,Integer business, String method, Double money, Date receiveAccountDate, Integer accountId, String returnNo, Date expireDate,
                                    String originalCorp, String bankName, String memo,String loanType,String operatorName,String partnerName,String withinOrAbroad, Date paymentDate,
                                    Integer returnId,String oppositeAccount,String oppositeBankno,String oppositeBankcode,String oppositeCorp,String payer);

    //修改借款收入信息(财务的新增借款收入修改)
    Map<String,Object> updateLoanCredit(Integer userId, Integer business,Integer businessHistory,Integer previousId, String method, Double money,
                                        Date receiveAccountDate, Integer accountId, String returnNo, Date expireDate, String originalCorp, String bankName,
                                        String memo, String operatorName,String partnerName,Date paymentDate,Integer type,String oppositeCorp,String payer);

    //借款的付款录入(财务的借款支出)
    Map<String,Object> loanPayEntry(Integer userId,Integer business,String method, Double money,String withinOrAbroad, Integer checkId,Integer accountId,
                                    Date receiveDate, Date expireDate,String operator, String receiver,Date paymentDate,String oppositeCorp,String payer);

    //修改借款的付款录入(财务的借款支出修改)
    Map<String,Object> updateLoanPayEntry(Integer userId,Integer payId,Integer payHistoryId,Integer previousId,String methodOld,String method, Double money,
                                          String withinOrAbroad, Integer checkId,Integer accountId,Date receiveDate, Date expireDate,String operator,
                                          String receiver,Date paymentDate,String oppositeCorp,String payer);

    Map<String,Object> getLoanCreditDetail(Integer business,String method,Integer type);  //查询某一条借款收入/支出的详情

    Map<String,Object> getLoanPayUpdateDetail(Integer businessHistory,String method,String businessType,Map<String,Object> map); //查看某一条借款还款的修改记录详情（财务借款收入/支出的修改详情）

    //借出去的款-收款（收入）
    Map<String,Object> loanReceivables(Integer userId,Integer business,String method,Double money,Integer accountId,Date receiveDate,Date expireDate,
                                       String operator,String receiver,Date paymentDate,String returnNo,String originalCorp,String bankName,String oppositeCorp,String payer);

    //修改借款支出信息(财务的新增借款支出修改)
    Map<String,Object> updateLoanDebit(Integer userId, Integer business,Integer businessHistory,Integer previousId, String method, Double money, Date receiveAccountDate, Integer accountId,
                                       String returnNo, Date expireDate, String originalCorp, String bankName, String memo,String operatorName,String partnerName,Date paymentDate,Integer returnId,
                                       String withinOrAbroad,String oppositeAccount,String oppositeBankno,String oppositeBankcode,String oppositeCorp,String payer);

    //修改借款的收款录入(财务的借款收款-收入修改)
    Map<String,Object> updateLoanPayCredit(Integer userId,Integer payId,Integer payHistoryId,Integer previousId,String methodOld,String method, Double money,
     Integer accountId,Date receiveDate,String operator, String receiver,Date paymentDate,Integer type,String returnNo,Date expireDate,String originalCorp,String bankName,String oppositeCorp,String payer);

//    PoLoan addPoLoan(User user, Integer paymentApply, Integer financePayment, Integer ordersPrepayment, BigDecimal amount, Integer type, Integer supplier,String supplierName,Integer orders); //票据或预付款多支付出去的款，生成的借款

    FinanceReturnHistory getReturnHistoryById(Integer returnId);
}
