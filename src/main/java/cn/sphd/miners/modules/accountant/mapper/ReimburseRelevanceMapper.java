package cn.sphd.miners.modules.accountant.mapper;


import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectRelevance;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
@Component
public interface ReimburseRelevanceMapper extends BaseMapper<TAccountantSubjectRelevance> {


    List<TAccountantSubjectRelevance> getRelevances(Integer org);

    TAccountantSubjectRelevance getByFeeCat(TAccountantSubjectRelevance param);

    Integer updateByFeeCat(TAccountantSubjectRelevance subjectRelevance);

    Integer deleteDisable(TAccountantSubjectRelevance subjectRelevance);

    List<TAccountantSubjectRelevance> getListByFeeCat(TAccountantSubjectRelevance relevance);

    void deleteByOrg(int org);
}