package cn.sphd.miners.modules.commodity.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.commodity.dao.PdFormulaHistoryDao;
import cn.sphd.miners.modules.commodity.entity.PdFormula;
import cn.sphd.miners.modules.commodity.entity.PdFormulaHistory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
@Repository
public class PdFormulaHistoryDaoImpl  extends BaseDao<PdFormulaHistory, Serializable> implements PdFormulaHistoryDao {
    @Override
    public int setHistory(PdFormula pdFormula) {
        PdFormulaHistory history=new PdFormulaHistory();
        BeanUtils.copyProperties(pdFormula,history);
        history.setId(null);
        history.setFormula(pdFormula.getId());
        save(history);

        getSession().flush();

        if(history.getPreviousId()==null){
            history.setPreviousId(history.getId());
        }
        update(history);
        return history.getId();
    }
}
