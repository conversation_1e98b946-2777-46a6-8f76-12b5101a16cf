<template>
  <Pageheader title="核心人物管控" :showBack="true"></Pageheader>
  <div class="container_main">
    <el-row :gutter="20" v-show="type === 'set'">
      <el-col :span="24">
        系统的启用需先将职工导入。您可亲自导入职工，也可选一位职工作为临时管理员来操作。<br>
        <el-text type="primary">注：您亲自导入，需要到电脑端去操作。</el-text>
      </el-col>
    </el-row>
    <el-row :gutter="20" v-show="type === 'set'">
      <el-col :span="24">在本页面，可录入临时管理员的姓名与手机号：</el-col>
    </el-row>
    <el-row>
      <el-col :span="20" :offset="2">
        <el-form
            label-position="top"
            size="large"
        >
          <el-form-item prop="userName" label="姓名">
            <el-input v-model="temporaryAdmin.userName" placeholder="请录入"></el-input>
          </el-form-item>
          <el-form-item prop="mobile" label="手机">
            <el-input v-model="temporaryAdmin.mobile" placeholder="请录入"></el-input>
          </el-form-item>
          <el-text size="small" type="primary" v-show="type === 'change' || type === 'reset'">注：所录入号码需能接收短信验证码！</el-text>
        </el-form>
      </el-col>
    </el-row>
    <el-row :gutter="20" v-show="type === 'set'">
      <el-col :span="24">系统将向该手机号发送短信，以便其开始导入职工。所以您需确保该手机号能收到短信！</el-col>
    </el-row>
    <el-row :gutter="20" v-show="type === 'change'">
      <el-col :span="20" :offset="2">修改后，原手机号将无法继续操作，该号码已导入的数据也将被清除！</el-col>
    </el-row>
    <el-row :gutter="20" v-show="type === 'reset'">
      <el-col :span="20" :offset="2">确定后，您电脑端已导入的的数据将被清除！</el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="24" style="text-align: center">
        <el-button
            type="primary"
            style="padding-left: 32px; padding-right: 32px"
            @click="addTemporaryAdmin_submit"
            :disabled="!(temporaryAdmin.userName && temporaryAdmin.mobile)"
            size="large"
        >
          确定
        </el-button>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import * as api from "@/api/centralControl";
export default {
  data() {
    return {
      type: 'set',
      temporaryAdmin: {
        userName: '',
        mobile: ''
      }
    }
  },
  components: {},
  created() {
    this.type = this.$route.params.type
  },
  mounted() {

  },
  destroyed() {

  },
  methods: {
    jump: function (path) {
      this.$router.push(path)
    },
    addTemporaryAdmin_submit() {
      let type = this.type
      let data = {}
      if (type === 'set') {
        data = this.temporaryAdmin
        api.addTemporaryAdmin(data)
            .then(res => {
              let data = res.data.data
              if (data === 1) {
                this.$message({
                  type: 'success',
                  message: '系统将向该职工发送短信，以告知其开始导入。'
                })
                this.$router.go(-1)
              } else {
                this.$message({
                  type: 'error',
                  message: `系统无法向这个手机号发送短信。<br>请检查确认，或输入其他手机号！`,
                  dangerouslyUseHTMLString: true
                })
              }
            })
      } else if (type === 'change') {
        data = this.temporaryAdmin
        data.userId = this.$route.query.userId
        api.updateTemporaryAdmin(data)
            .then(res => {
              let data = res.data
              if (data === 1) {
                this.$message({
                  type: 'success',
                  message: '系统将向该职工发送短信，以告知其开始导入。'
                })
                this.$router.go(-2)
              } else {
                this.$message({
                  type: 'error',
                  message: `<small>系统无法向这个手机号发送短信。</small><br><small>请检查确认，或输入其他手机号！</small>`,
                  dangerouslyUseHTMLString: true
                })
              }
            })
      } else if (type === 'reset') {
        let that = this
        api.giveUpImportUser()
        .then(res => {
          let status = res.data.status;
          if (status === 1) {
            api.addTemporaryAdmin(that.temporaryAdmin)
                .then(res => {
                  let data = res.data.data
                  if (data === 1) {
                    this.$message({
                      type: 'success',
                      message: '系统将向该职工发送短信，以告知其开始导入。'
                    })
                    this.$router.go(-2)
                  } else {
                    this.$message({
                      type: 'error',
                      message: `<small>系统无法向这个手机号发送短信。</small><br><small>请检查确认，或输入其他手机号！</small>`,
                      dangerouslyUseHTMLString: true
                    })
                  }
                })
          } else {
            this.$message({
              type: 'error',
              message: '清除数据失败！'
            })
          }
        })
      }

    }
  }
}

</script>

<style lang="scss" scoped>
.el-row {
  margin-bottom: 16px;
}
.el-row:last-child {
  margin-bottom: 0;
}
</style>
