<template>
  <div id="testPaperApproval" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="试卷" isSearch="true" @toggleList="search"></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="正常考核" name="1" :msgCount="examSum">
        <div class="tip">
          <p>现有如下考核，需您及时答题并交卷</p>
          <p style="color:red;">！！交卷截止时间前未能交卷者，以零分计</p>
        </div>
        <div>
          <a class="ui-cell" v-on:click="jump(item)" v-for="(item, index) in list" v-bind:key="index">
            <div class="">
              <p><span class="ttl">参与考核</span><span class="con">{{item.trainingExam.userNum}}人</span></p>
              <p><span class="ttl">交卷截至时间</span><span class="con">{{item.trainingExam.endTime | formatDay('M月DD日 HH:mm')}}</span></p>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="补考" name="2" closable='false' :msgCount="examAgainSum">
        <div class="tip">
          <p>您现有如下补考有待完成。请您及时答题并交卷。</p>
        </div>
        <div>
          <a class="ui-cell" v-on:click="jump(item)" v-for="(item, index) in list2" v-bind:key="index">
            <div class="">
              <p><span class="ttl">补考</span></p>
              <p><span class="ttl">交卷截至时间</span><span class="con">{{item.trainingExam.endTime | formatDay('M月DD日 HH:mm')}}</span></p>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>
<style lang="less">
  #testPaperApproval{
    overflow: auto;
    .ui-cell{ display: block; }
    .tabCount{  top:0;  right:0 ;  left:auto;  }
    .tabs-tab{  width:50%;  }
    .ttl{ display:inline-block; width:100px;  }
    .tip{ padding:5px 12px; background-color: #E2EFDA; display: block; }
  }
</style>
<script>
export default {
  name: 'testPaperApproval',
  data () {
    return {
      tabValue: '1',
      list: [],
      list2: [],
      // examSum: 0,
      // examAgainSum: 0,
      loading: true,
      listenersUid: []
    }
  },
  computed: {
    examSum: function () {
      return this.list.length
    },
    examAgainSum: function () {
      return this.list2.length
    }
  },
  created () {
    let that = this
    that.getLists()
    let userId = this.sphdSocket.user.userID
    this.listenersUid = [
      this.sphdSocket.subscribe('trainingExamUserList', function (data) {
        console.log('trainingExamUserList user OK:' + data)
        /*
         * {"respExamForUser":{"trainingExam":{"answerDuration":60,"bankNum":2,"beginTime":*************,"choiceNum":3,"createDate":*************,"createName":"徐晨","creator":7086,"enabled":0,"enabledTime":*************,"endTime":*************,"failNum":0,"goal":"gfd","id":40,"onePassNum":0,"org":3531,"passingScore":50,"publicity":"成绩前三名将给予公示，但无奖励。","resitTimes":0,"state":"2","tfNum":0,"updateDate":*************,"updateName":"徐晨","updator":7086,"userNum":3},"trainingExamUser":{"answerTimes":0,"createDate":*************,"createName":"徐晨","creator":7086,"exam":40,"id":61,"org":3531,"user":7086}},"operate":-1} */
        let getData = JSON.parse(data)
        let info = getData.respExamForUser
        let info2 = getData.respExamForUserAgain
        let operate = Number(getData.operate)
        if (operate > 0) {
          info && that.list.unshift(info)
          info2 && that.list2.unshift(info2)
        } else if (operate < 0) {
          let reimburseId = ''
          let _index = -1
          let _index2 = -1
          if (info) {
            reimburseId = info.id
            that.list.forEach(function (item, index) {
              if (item.id === reimburseId) {
                _index = index
              }
            })
            if (_index > -1) {
              that.list.splice(_index, 1)
            }
          }
          if (info2) {
            reimburseId = info2.id
            that.list2.forEach(function (item, index) {
              if (item.id === reimburseId) {
                _index2 = index
              }
            })
            if (_index2 > -1) {
              that.list2.splice(_index2, 1)
            }
          }

        }
      }, null, 'user')
    ]
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    jump: function (item) {
      item.isAgin = this.tabValue
      localStorage.setItem('info', JSON.stringify(item))
      this.$router.push({
        path: `/testDetails`
      })
    },
    getLists: function () {
      let that = this
      let url = '../../../examManage/selectExamForUserList.do'
      that.axios.post(url).then((response) => {
        that.loading = false
        let res = response.data
        console.log('返回值')
        console.log(res)
        if (res) {
          that.list = res.respExamForUserList || []
          that.list2 = res.respExamForUserAgainList || []
          // that.examSum = res.examSum || 0
          // that.examAgainSum = res.examAgainSum || 0
        } else {
          that.$kiko_message('加载失败')
        }
      }).catch(function (res) {
        console.log(res)
      })
    },
    search: function () {
    }
  }
}
</script>
