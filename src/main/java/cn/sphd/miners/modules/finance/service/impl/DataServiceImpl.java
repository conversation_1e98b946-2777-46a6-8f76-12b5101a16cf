package cn.sphd.miners.modules.finance.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.service.BaseServiceImpl;
import cn.sphd.miners.common.utils.FinanceUtils;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxDetailEntity;
import cn.sphd.miners.modules.accountantReportTax.service.AcctReportTaxService;
import cn.sphd.miners.modules.finance.dao.*;
import cn.sphd.miners.modules.finance.entity.*;
import cn.sphd.miners.modules.finance.service.AccountService;
import cn.sphd.miners.modules.finance.service.DataService;
import cn.sphd.miners.modules.personal.service.PersonnelReimburseService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.uploads.service.UploadService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by Administrator on 2016/12/16.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED)
public class DataServiceImpl extends BaseServiceImpl implements DataService {
    @Autowired
    FinanceAccountDao financeAccountDao;
    @Autowired
    AccountPeroidDao accountPeroidDao;
    @Autowired
    AccountDetailDao accountDetailDao;
    @Autowired
    FinanceReturnDao financeReturnDao;
    @Autowired
    FinanceAccountBillDao financeAccountBillDao;
    @Autowired
    AccountDetailHistoryDao accountDetailHistoryDao;
    @Autowired
    FinanceAccountBillHistoryDao financeAccountBillHistoryDao;
    @Autowired
    FinanceAccountBillImageDao financeAccountBillImageDao;
    @Autowired
    FinanceAccountBillImageHistoryDao financeAccountBillImageHistoryDao;

    @Autowired
    PersonnelReimburseService personnelReimburseService;
    @Autowired
    AcctReportTaxService acctReportTaxService;
    @Autowired
    AccountService accountService;
    @Autowired
    UploadService uploadService;

    @Override
    public AccountPeriod getDataMonth(Integer accountId) {
        String hql = " from AccountPeriod o where o.periodType = 1 and o.accountId = " + accountId + " and MONTHNAME(o.beginDate)=MONTHNAME(now())";
//        String hql = " select * from t_finance_account_peroid o where o.periodType = 1 and o.account_id = "+accountId+" and MONTHNAME(o.begin_date)=MONTHNAME('"+beginDate+"')";
        AccountPeriod accountPeriod1 = accountPeroidDao.getByHQL(hql);
        return accountPeriod1;
    }

    @Override
    public List<AccountPeriod> getDataByDay(Integer accountId, String beginDate, String endDate) {
        String condition = " and o.periodType = 2 and o.accountId = " + accountId;
        if (!"".equals(beginDate) && !"".equals(endDate)) {
            condition += " and o.createDate between '" + beginDate + "' and '" + endDate + "'";
        }
        List<AccountPeriod> accountPeriods = accountPeroidDao.findCollectionByConditionNoPage(condition, null, null);
        return accountPeriods;
    }

    @Override
    public AccountPeriod getDetail(Integer accountPerId) {
        return accountPeroidDao.get(accountPerId);
    }

    @Override
    public List<AccountDetail> getMonthDetailDay(Integer accountId, String beginDate, String endDate) {
        String condition = " and o.accountId = " + accountId;
        if (!"".equals(beginDate) && !"".equals(endDate)) {
            condition += " and o.createDate between '" + beginDate + "' and '" + endDate + "'";
        }
        List<AccountDetail> accountDetails = accountDetailDao.findCollectionByConditionNoPage(condition, null, null);
        return accountDetails;
    }

    @Override
    public List<AccountDetail> getDayDetail(Integer accountId, String beginDate) {
        String hql = " from AccountDetail o where o.accountId = " + accountId + " and TO_DAYS(o.createDate) = TO_DAYS('" + beginDate + "')";
        List<AccountDetail> accountDetails = accountDetailDao.getListByHQL(hql);
        return accountDetails;
    }

    @Override
    public AccountDetail getOneDetail(Integer detailId) {
        return accountDetailDao.get(detailId);
    }

    @Override
    public void addMonth(AccountPeriod accountPeriod) {
        accountPeroidDao.save(accountPeriod);
    }

    @Override
    public void addDay(AccountPeriod accountPeriod1) {
        accountPeroidDao.save(accountPeriod1);
    }

    @Override
    public void addInTransfers(AccountDetail accountDetail) {
        accountDetailDao.save(accountDetail);
    }

    @Override
    public void saveFinanceReturn(FinanceReturn financeReturn) {
        financeReturnDao.save(financeReturn);
    }

    @Override
    public void updateFinanceReturn(FinanceReturn financeReturn) {
        financeReturnDao.update(financeReturn);
    }

    @Override
    public void saveFinanceAccountBill(FinanceAccountBill financeAccountBill) {
        financeAccountBillDao.save(financeAccountBill);
    }

    @Override
    public void updateFinanceAccountBill(FinanceAccountBill financeAccountBill) {
        financeAccountBillDao.update(financeAccountBill);
    }

    @Override
    public void addAccountDetail(AccountDetail accountDetail) {
        accountDetailDao.save(accountDetail);
    }

    @Override
    public AccountPeriod getDataDayByAccountId(Integer accountId) {
        String hql = "from AccountPeriod o where o.periodType = 2 and o.accountId = " + accountId + " and TO_DAYS(o.beginDate) = TO_DAYS(now())";
        AccountPeriod accountPeriod = accountPeroidDao.getByHQL(hql);
        return accountPeriod;
    }

    @Override
    public void updateAccountPeriod(AccountPeriod accountPeriod) {
        accountPeroidDao.update(accountPeriod);
    }

    @Override
    public List<AccountPeriod> getAccountData(Integer oid) {
        String condition = " and o.org = " + oid + " and date_format(o.beginDate,'%Y-%m')=date_format(now(),'%Y-%m')";
        List<AccountPeriod> accountPeriods = accountPeroidDao.findCollectionByConditionNoPage(condition, null, null);
        return accountPeriods;
    }

    @Override
    public List<AccountPeriod> getDataByMonth(Integer accountId, String beginDate, String endDate) {
        String condition = " and o.accountId = " + accountId;
        if (!"".equals(beginDate) && !"".equals(endDate)) {
            condition += " and o.begindate between " + beginDate + " and '" + endDate + "'";
        }
        List<AccountPeriod> accountPeriods = accountPeroidDao.findCollectionByConditionNoPage(condition, null, null);
        return accountPeriods;
    }

    @Override
    public List<FinanceAccountBill> getDataByOid(Integer oid,String accountantStatus) {
        Map<String,Object> params = new HashMap<>();
        String hql = " from FinanceAccountBill where org_=:oid and accountantStatus!=:accountantStatus and source='1'";
        params.put("oid",oid);
        params.put("accountantStatus",accountantStatus);
        List<FinanceAccountBill> financeAccountBills = financeAccountBillDao.getListByHQLWithNamedParams(hql,params);
        return financeAccountBills;
    }

    @Override
    public List<AccountDetail> getAccountingByOid(Integer oid) {
        Map<String,Object> params = new HashMap<>();
        String hql = " from AccountDetail where org_=:oid and source='1'";
        params.put("oid",oid);
        List<AccountDetail> accountDetails = accountDetailDao.getListByHQLWithNamedParams(hql,params);
        return accountDetails;
    }

    @Override
    public FinanceAccountBill getAccountBillByBillId(Integer detailId) {
        FinanceAccountBill financeAccountBill = financeAccountBillDao.get(detailId);
        return financeAccountBill;
    }

    /**
     * <AUTHOR>
     * @date 2017/2/17 11:01
     * 会计数据状态的更新
     * kind 1-除收据外的报销两讫后的数据详情和收入中现金/银行转账的数据(detail表) 2-收入所有和支出除收据外的数据详情(bill表)
     */
    @Override
    public String updateAccountantStatus(Integer detailId, String kind,String state) {
        String status = null;
        if ("1".equals(kind)) {
            AccountDetail accountDetail = accountDetailDao.get(detailId);
            if (accountDetail != null) {
                accountDetail.setAccountantStatus(state);
//                accountDetail.setAccountantStatus("2");
                accountDetailDao.update(accountDetail);
                status = "1";
            }else {
                status = "2";
            }
        } else {
            FinanceAccountBill financeAccountBill = financeAccountBillDao.get(detailId);
            if (financeAccountBill != null) {
                financeAccountBill.setAccountantStatus(state);
                financeAccountBillDao.update(financeAccountBill);
                status = "1";
            }else {
                status = "2";
            }
        }
        return status;
    }

    @Override
    public AccountDetail getByBillId(Integer detailId) {
        String hql = " from AccountDetail o where o.billDetail = "+detailId;
        AccountDetail accountDetail = accountDetailDao.getByHQL(hql);
        return accountDetail;
    }

    @Override
    public List<AccountPeriod> getAccountDataByOidDay(Integer oid, Date day) {
        SimpleDateFormat sdf=new SimpleDateFormat();
        String hql = " from AccountPeriod o where o.periodType = 2 and o.org = " + oid + " and TO_DAYS(o.createDate) = TO_DAYS('"+sdf.format(day)+"')" ;
        List<AccountPeriod> accountPeriods = accountPeroidDao.getListByHQL(hql);
        return accountPeriods;
    }

    @Override
    public List<AccountPeriod> getDataMonthByDate(Integer accountId, String beginDate, String endDate, Integer periodType) {
        String hql = " and o.periodType = " + periodType + " and o.accountId = "+accountId;
        if (!"".equals(beginDate) && !"".equals(endDate)){
            if (periodType==1){
                hql+=" and date_format(o.beginDate,'%Y-%m') >= date_format('"+beginDate+"','%Y-%m') and date_format(o.endDate,'%Y-%m') <= date_format('"+endDate+"','%Y-%m')" ;
            }else {
                hql+=" and TO_DAYS(o.beginDate) between TO_DAYS('" + beginDate + "') and TO_DAYS('" + endDate + "')";
            }
        }
        Map<String,String> orderBy = new HashMap<>();
        orderBy.put("o.beginDate","desc");
        List<AccountPeriod> accountPeriodList = accountPeroidDao.findCollectionByConditionNoPage(hql,null,orderBy);
        return accountPeriodList;
    }

    @Override
    public AccountPeriod getDataByMonth1(Integer accountId, String beginDate) {
        String hql = " from AccountPeriod o where o.periodType = 1 and o.accountId = "+accountId+" and date_format(o.beginDate,'%Y-%m') = date_format('"+beginDate+"','%Y-%m')";
        AccountPeriod accountPeriod = accountPeroidDao.getByHQL(hql);
        return accountPeriod;
    }

    @Override
    public void addAccountDetailHistory(AccountDetailHistory accountDetailHistory) {
        accountDetailHistoryDao.save(accountDetailHistory);
    }

    @Override
    public void addFinanceAccountBillHistory(FinanceAccountBillHistory financeAccountBillHistory) {
        financeAccountBillHistoryDao.save(financeAccountBillHistory);
    }

    @Override
    public FinanceAccountBill getAccountBillByReturnId(Integer returnId) {
        String hql = " from FinanceAccountBill o where o.financeReturn = "+returnId;
        FinanceAccountBill financeAccountBill = financeAccountBillDao.getByHQL(hql);
        return financeAccountBill;
    }

    @Override
    public FinanceAccountBill getAccountBillByChequeId(Integer chequeId) {
        String hql = " from FinanceAccountBill o where o.cheque = "+chequeId;
        FinanceAccountBill financeAccountBill = financeAccountBillDao.getByHQL(hql);
        return financeAccountBill;
    }

    @Override
    public AccountDetailHistory getAccountDetailHistoryById(Integer id) {
        return accountDetailHistoryDao.get(id);
    }

    @Override
    public FinanceAccountBillHistory getBillHistoryById(Integer billHistoryId) {
        return financeAccountBillHistoryDao.get(billHistoryId);
    }

    @Override
    public void updateFinanceAccountBillHistory(FinanceAccountBillHistory financeAccountBillHistory) {
        financeAccountBillHistoryDao.save(financeAccountBillHistory);
    }

    @Override
    public AccountDetail getDetailByReimburse(Integer reimburseId) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from AccountDetail where reimburseId=:reimburseId";
        map.put("reimburseId",reimburseId);
        return (AccountDetail) accountDetailDao.getByHQLWithNamedParams(hql,map);
    }

    /**
     * 按月流水查看
     * @param oid  机构id
     * @param beginDate  开始时间
     * @param endDate  结束时间
     * @param orgIntegerList 总机构以及字机构的id
     * @return
     */
    @Override
    public List<AccountDetail> getDetailByMonth(Integer oid, Date beginDate, Date endDate,List<Integer> orgIntegerList,PageInfo pageInfo) {
        Map<String,Object> params = new HashMap<>();
        String hql = "from AccountDetail where";
        if (oid!=null){
            hql+=" org_=:org";
            params.put("org",oid);
        }
        if (orgIntegerList!=null&&orgIntegerList.size()>0){
            hql+=" org_ in (:orgIntegerList)";
            params.put("orgIntegerList",orgIntegerList);
        }
        if (beginDate!=null){
            hql+=" and createDate>=:beginDate";
            params.put("beginDate",beginDate);
        }
        if (endDate!=null){
            hql+=" and createDate<:endDate";
            params.put("endDate",endDate);
        }
        hql+=" order by id desc";   //倒序
        return accountDetailDao.getListByHQLWithNamedParams(hql,params,pageInfo);
    }

    @Override
    public FinanceAccountBillImage addBillImage(User user, Integer accountDetail, Integer accountBill, String type, String uplaodPath, Integer orders) {
        FinanceAccountBillImage financeAccountBillImage = new FinanceAccountBillImage();
        financeAccountBillImage.setUplaodPath(uplaodPath);
        financeAccountBillImage.setOrg(user.getOid());
        financeAccountBillImage.setAccountDetail(accountDetail);
        financeAccountBillImage.setAccountBill(accountBill);
        financeAccountBillImage.setType(type);  //1-图片
        financeAccountBillImage.setOrders(orders);
        financeAccountBillImage.setCreateDate(new Date());
        financeAccountBillImage.setCreateName(user.getUserName());
        financeAccountBillImage.setCreator(user.getUserID());
        financeAccountBillImageDao.save(financeAccountBillImage);
        return financeAccountBillImage;
    }

    @Override
    public FinanceAccountBillImageHistory addBillImageHistory(User user, Integer accountDetail,Integer accountDetailHistory,Integer accountBill,Integer accountBillHistory,String type, String uplaodPath, Integer orders) {
        FinanceAccountBillImageHistory financeAccountBillImageHistory = new FinanceAccountBillImageHistory();
        financeAccountBillImageHistory.setOrg(user.getOid());
        financeAccountBillImageHistory.setAccountDetail(accountDetail);
        financeAccountBillImageHistory.setAccountDetailHistory(accountDetailHistory);
        financeAccountBillImageHistory.setAccountBill(accountBill);
        if (accountBillHistory!=null) {
            financeAccountBillImageHistory.setAccountBillHistory(accountBillHistory.toString());
        }
        financeAccountBillImageHistory.setType(type);
        financeAccountBillImageHistory.setUplaodPath(uplaodPath);
        financeAccountBillImageHistory.setOrders(orders);
        financeAccountBillImageHistory.setCreateDate(new Date());
        financeAccountBillImageHistory.setCreateName(user.getUserName());
        financeAccountBillImageHistory.setCreator(user.getUserID());
        financeAccountBillImageHistoryDao.save(financeAccountBillImageHistory);
        return financeAccountBillImageHistory;
    }

    @Override
    public FinanceAccountBillImage getBillImage(Integer id) {
        return financeAccountBillImageDao.get(id);
    }

    @Override
    public FinanceAccountBillImageHistory getBillImageHistory(Integer id) {
        return financeAccountBillImageHistoryDao.get(id);
    }

    @Override
    public List<FinanceAccountBillImage> getBillImageByIds(Integer org,Integer accountDetail, Integer accountBill) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from FinanceAccountBillImage where org=:org";
        map.put("org",org);
        if (accountDetail!=null){
            hql+=" and accountDetail=:accountDetail";
            map.put("accountDetail",accountDetail);
        }
        if (accountBill!=null){
            hql+=" and accountBill=:accountBill";
            map.put("accountBill",accountBill);
        }
        hql+=" order by orders";
        List<FinanceAccountBillImage> financeAccountBillImages = financeAccountBillImageDao.getListByHQLWithNamedParams(hql,map);
        return financeAccountBillImages;
    }

    @Override
    public List<FinanceAccountBillImageHistory> getBillImageHistoryByIds(Integer org, Integer accountDetailHistory, Integer accountBillHistory) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from FinanceAccountBillImageHistory where org=:org";
        map.put("org",org);
        if (accountDetailHistory!=null){
            hql+=" and accountDetailHistory=:accountDetailHistory";
            map.put("accountDetailHistory",accountDetailHistory);
        }
        if (accountBillHistory!=null){
            hql+=" and accountBillHistory=:accountBillHistory";
            map.put("accountBillHistory",accountBillHistory.toString());
        }
        hql+=" order by orders";
        List<FinanceAccountBillImageHistory> financeAccountBillImageHistories = financeAccountBillImageHistoryDao.getListByHQLWithNamedParams(hql,map);
        return financeAccountBillImageHistories;
    }

    @Override
    public Map<String, Object> getNumTotal(Integer org,Date beginDate,Date endDate) {
        Map<String,Object> map = new HashMap<>();
        //可做凭证
        Integer detailNum = getNumDetailBill(org,beginDate,endDate); //正常待选择和修改待选择总和(上月的)
        map.put("numSelected",detailNum); //结账的总数(会计用于判断是否可以结账，只算待选择中的)
        Date yearBefore = NewDateUtils.changeYear(beginDate,-1); //beginDate的去年时间
        Integer detailNum1 = getNumNoAccounting(org,yearBefore,endDate); //去年与今年的“不予下账”
        map.put("numVoucher",detailNum+detailNum1); //可做凭证的总数(包括待选择和不予下账的)

        //尚未入会计账的[不分时间的，除了结账和不予下账的所有数据，其实就是所有的未结账数据]
        Integer numNotRecorded = getNumDetailBill(org,null,null);
        map.put("numNotRecorded",numNotRecorded); //尚未入会计账的总数

        //系统中所有已制作过凭证但选为“不予做账”的
        Integer numNoAccounting = getNumNoAccounting(org,null,null);
        map.put("numNoAccounting",numNoAccounting);
        return map;
    }

    //正常待选择、修改待选择这三类的总数
    public Integer getNumDetailBill(Integer org, Date beginDate,Date endDate) {
        Map<String,Object> params = new HashMap<>();
        String hql = "select count(id) from AccountDetail where voucher is null and accountantStatus='1' and org_=:org";
        params.put("org",org);
        if (beginDate!=null){
            hql+=" and createDate>=:beginDate";
            params.put("beginDate",beginDate);
        }
        if (endDate!=null){
            hql+=" and createDate<:endDate";
            params.put("endDate",endDate);
        }
        hql+=" and id not in (select id from AccountDetail where method in ('6','7','8') and credit is not null)"; //去掉内部支出性转账的收入，只取支出
        Long detailNum = (Long) accountDetailDao.getByHQLWithNamedParams(hql,params);  //AccountDetail进行下账的正常待选择和修改待选择总和

        params.clear();
        String hql1 = "select count(id) from FinanceAccountBill where voucher is null and accountantStatus='1' and org_=:org";
        params.put("org",org);
        if (beginDate!=null){
            hql1+=" and createDate>=:beginDate";
            params.put("beginDate",beginDate);
        }
        if (endDate!=null){
            hql1+=" and createDate<:endDate";
            params.put("endDate",endDate);
        }
        hql1+=" and id not in (select billDetail_ from AccountDetail where org_=:org and billDetail_ is not null)";  //去掉和AccountDetail中重复的
        Long billNum = (Long) financeAccountBillDao.getByHQLWithNamedParams(hql1,params);  //FinanceAccountBill进行下账的正常待选择和修改待选择总和

        Integer detail = detailNum!=null?detailNum.intValue():0;
        Integer bill = billNum!=null?billNum.intValue():0;
        return detail+bill;
    }

    // “不予下账”的统计
    public Integer getNumNoAccounting(Integer org, Date beginDate,Date endDate) {
        Map<String,Object> params = new HashMap<>();
        String hql = "select count(id) from AccountDetail where aIsAccount=false and org_=:org";
        params.put("org",org);
        if (beginDate!=null){
            hql+=" and createDate>=:beginDate";
            params.put("beginDate",beginDate);
        }
        if (endDate!=null){
            hql+=" and createDate<:endDate";
            params.put("endDate",endDate);
        }
        Long detailNum = (Long) accountDetailDao.getByHQLWithNamedParams(hql,params);  //AccountDetail进行下账的正常待选择和修改待选择总和

        params.clear();
        String hql1 = "select count(id) from FinanceAccountBill where isAccount=false and org_=:org";
        params.put("org",org);
        if (beginDate!=null){
            hql1+=" and createDate>=:beginDate";
            params.put("beginDate",beginDate);
        }
        if (endDate!=null){
            hql1+=" and createDate<:endDate";
            params.put("endDate",endDate);
        }
        Long billNum = (Long) financeAccountBillDao.getByHQLWithNamedParams(hql1,params);  //FinanceAccountBill进行下账的正常待选择和修改待选择总和

        Integer detail = detailNum!=null?detailNum.intValue():0;
        Integer bill = billNum!=null?billNum.intValue():0;
        return detail+bill;
    }

    @Override
    public Map<String,Object> voucherList(Integer org, Date beginDate,Date endDate) {
        Map<String,Object> map = new HashMap<>();

        //正常待选择的
        List<AccountDetail> accountDetails = getDetailVoucher(org,beginDate,endDate);
        for (AccountDetail accountDetail:accountDetails) {
            if (accountDetail.getReimburseId()!=null){   //会计中使用的，单独取出来【报销只有现金和银行转账】
                List<FinanceReimburseBill> financeReimburseBills = personnelReimburseService.getReimburseBillByReimburseId(accountDetail.getReimburseId(),null);
                List<String> billCatNameList = new ArrayList<>();
                for (FinanceReimburseBill financeReimburseBill:financeReimburseBills) {
                    billCatNameList.add(financeReimburseBill.getBillCatName());
                }
                accountDetail.setBillCatNameList(billCatNameList);
            }
        }
        List<FinanceAccountBill> financeAccountBills = getBillVoucher(org,beginDate,endDate);
        map.put("accountDetails",accountDetails);
        map.put("financeAccountBills",financeAccountBills);
        map.put("numNormal",accountDetails.size()+financeAccountBills.size());

        //修改待选择的【包括老数据、冲账的数据、修改正确的数据】
        List<AccountDetail> accountDetailsUpdate = getDetailUpdateList(org,beginDate,endDate);
        for (AccountDetail accountDetail:accountDetails) {
            if (accountDetail.getReimburseId()!=null){   //会计中使用的，单独取出来【报销只有现金和银行转账】
                List<FinanceReimburseBill> financeReimburseBills = personnelReimburseService.getReimburseBillByReimburseId(accountDetail.getReimburseId(),null);
                List<String> billCatNameList = new ArrayList<>();
                for (FinanceReimburseBill financeReimburseBill:financeReimburseBills) {
                    billCatNameList.add(financeReimburseBill.getBillCatName());
                }
                accountDetail.setBillCatNameList(billCatNameList);
            }
        }
        List<FinanceAccountBill> financeAccountBillsUpdate = getBillUpdateList(org,beginDate,endDate);
        map.put("accountDetailsUpdate",accountDetailsUpdate);
        map.put("financeAccountBillsUpdate",financeAccountBillsUpdate);
        map.put("numUpdate",accountDetailsUpdate.size()+financeAccountBillsUpdate.size());

        //不予下账的【这里的时间按照票据日期查询】
        List<AccountDetail> accountDetailsNo = getNoAccountingDetailList(org,NewDateUtils.changeYear(beginDate,-1),endDate,false);
        List<FinanceAccountBill> financeAccountBillsNo = getNoAccountingBillList(org,NewDateUtils.changeYear(beginDate,-1),endDate,false);
        map.put("accountDetailsNo",accountDetailsNo);
        map.put("financeAccountBillsNo",financeAccountBillsNo);
        map.put("numNo",accountDetailsNo.size()+financeAccountBillsNo.size());

        //结账月份的不予下账统计
        Integer numNoAccounting = getNumNoAccounting(org,beginDate,endDate);
        map.put("numNoAccounting",numNoAccounting);
        return map;
    }

    public List<AccountDetail> getDetailVoucher(Integer org, Date beginDate,Date endDate) {
        Map<String,Object> params = new HashMap<>();
        String hql = "from AccountDetail where voucher is null and accountantStatus='1' and isModify is null and org_=:org";
        params.put("org",org);
        if (beginDate!=null){
            hql+=" and createDate>=:beginDate";
            params.put("beginDate",beginDate);
        }
        if (endDate!=null){
            hql+=" and createDate<=:endDate";
            params.put("endDate",endDate);
        }
        hql+=" and id not in (select id from AccountDetail where method in ('6','7','8') and credit is not null)"; //去掉内部支出性转账的收入，只取支出
        List<AccountDetail> accountDetails = accountDetailDao.getListByHQLWithNamedParams(hql,params);
        return accountDetails;
    }

    public List<FinanceAccountBill> getBillVoucher(Integer org, Date beginDate,Date endDate) {
        Map<String,Object> params = new HashMap<>();
        String hql = "from FinanceAccountBill where voucher is null and accountantStatus='1' and isModify is null and (cheque_ is not null or financeReturn.id is not null) and org_=:org";
        params.put("org",org);
        if (beginDate!=null){
            hql+=" and createDate>=:beginDate";
            params.put("beginDate",beginDate);
        }
        if (endDate!=null){
            hql+=" and createDate<:endDate";
            params.put("endDate",endDate);
        }
        hql+=" and id not in (select billDetail_ from AccountDetail where org_=:org and billDetail_ is not null)";  //去掉和AccountDetail中重复的
        List<FinanceAccountBill> financeAccountBills = financeAccountBillDao.getListByHQLWithNamedParams(hql,params);
        return financeAccountBills;
    }
    public List<AccountDetail> getDetailUpdateList(Integer org, Date beginDate,Date endDate) {
        Map<String,Object> params = new HashMap<>();
        String hql = "from AccountDetail where voucher is null and accountantStatus='1' and isModify is true and org_=:org";
        params.put("org",org);
        if (beginDate!=null){
            hql+=" and createDate>=:beginDate";
            params.put("beginDate",beginDate);
        }
        if (endDate!=null){
            hql+=" and createDate<:endDate";
            params.put("endDate",endDate);
        }
        hql+=" and id not in (select id from AccountDetail where method in ('6','7','8') and credit is not null)"; //去掉内部支出性转账的收入，只取支出
        List<AccountDetail> accountDetails = accountDetailDao.getListByHQLWithNamedParams(hql,params);
        return accountDetails;
    }

    public List<FinanceAccountBill> getBillUpdateList(Integer org, Date beginDate,Date endDate) {
        Map<String,Object> params = new HashMap<>();
        String hql = "from FinanceAccountBill where voucher is null and accountantStatus='1' and isModify is true and org_=:org";
        params.put("org",org);
        if (beginDate!=null){
            hql+=" and createDate>=:beginDate";
            params.put("beginDate",beginDate);
        }
        if (endDate!=null){
            hql+=" and createDate<:endDate";
            params.put("endDate",endDate);
        }
        hql+=" and id not in (select billDetail_ from AccountDetail where org_=:org and billDetail_ is not null)";  //去掉和AccountDetail中重复的
        List<FinanceAccountBill> financeAccountBills = financeAccountBillDao.getListByHQLWithNamedParams(hql,params);
        return financeAccountBills;
    }
    @Override
    public Map<String, Object> noAccountingList(Integer org, Date beginDate, Date endDate, Boolean isAccount,Integer type) {
        Map<String,Object> map = new HashMap<>();
        List<Map<String,Object>> accountNum = getDetailNoAccounting(org,beginDate,endDate,isAccount,type);
        List<Map<String,Object>> billNum = getBillNoAccounting(org,beginDate,endDate,isAccount,type);
        List<Map<String,Object>> numList = new ArrayList<>();
        if (accountNum.size()>0&&billNum.size()>0){
            numList.addAll(accountNum);
            numList.addAll(billNum);
        }else if (accountNum.size()>0){
            numList.addAll(accountNum);
        }else if (billNum.size()>0){
            numList.addAll(billNum);
        }
        List<Map<String,Object>> numList1 = new ArrayList<>();
        List<Map<String,Object>> numListRemove = new ArrayList<>();
        for (Map<String,Object> map1:numList) {
            String year = (String) map1.get("year");
            Long num = (Long) map1.get("num");
            String typeEntity = (String) map1.get("typeEntity");
            for (int i=numList.size()-1;i>=0&&i<numList.size();i--) {
                Map<String,Object> map3 = numList.get(i);
                String year1 = (String) map3.get("year");
                String typeEntity1 = (String) map3.get("typeEntity");
                if (StringUtils.isNotEmpty(year1)&&StringUtils.isNotEmpty(year)&&year.equals(year1)&&typeEntity.equals("accountDetail")&&typeEntity1.equals("accountBill")){  //不能是一个
                    Long num1 = (Long) map3.get("num");
                    num = num+num1;
                    map1.put("num",num.intValue());
                    numListRemove.add(map3);
                    break;
                }
            }
            numList1.add(map1);
        }
        numList1.removeAll(numListRemove);
        map.put("numList",numList1);
        return map;
    }

    public List<Map<String,Object>> getDetailNoAccounting(Integer org, Date beginDate,Date endDate,Boolean isAccount,Integer type) {
        Map<String,Object> params = new HashMap<>();
        String hql = "select";
        if (1==type){
            hql+= " date_format(billDate,'%Y年'), "; //按年
        }else if (2==type){
            hql+= " date_format(billDate,'%Y年%m月'), "; //按年月
        }
        hql+="count(id) from AccountDetail where org_=:org";
        params.put("org",org);
//        if (type==1&&beginDate!=null&&endDate!=null) {  //可做凭证中查询不予做账的票据
//            hql += " and createDate>=:beginDate and createDate<:endDate";
//            params.put("beginDate", beginDate);
//            params.put("endDate", endDate);
//        }else {
//            if (beginDate != null) {
//                hql += " and billDate>=:beginDate";
//                params.put("beginDate", beginDate);
//            }
//            if (endDate != null) {
//                hql += " and billDate<:endDate";
//                params.put("endDate", endDate);
//            }
//        }
        if (beginDate != null) {
            hql += " and billDate>=:beginDate";
            params.put("beginDate", beginDate);
        }
        if (endDate != null) {
            hql += " and billDate<:endDate";
            params.put("endDate", endDate);
        }
        if (isAccount!=null){
            hql+=" and aIsAccount=:aIsAccount";
            params.put("aIsAccount",isAccount);
        }
        hql+=" and id not in (select id from AccountDetail where method in ('6','7','8') and credit is not null)"; //去掉内部支出性转账的收入，只取支出
        if (1==type){
            hql+= " group by date_format(billDate,'%Y年')"; //按年
        }else if (2==type){
            hql+= " group by date_format(billDate,'%Y年%m月')"; //按年月
        }
        List<Object[]> list = accountDetailDao.getListByHQLWithNamedParams(hql,params);
        List<Map<String,Object>> mapList = new ArrayList<>();
        for (Object[] ob:list) {
            Map<String,Object> map = new HashMap<>();
            map.put("year",ob[0]); //年份
            map.put("num",ob[1]); //数量
            map.put("typeEntity","accountDetail"); //为了区分是accountDetail还是bill里面的数据
            mapList.add(map);
        }
        return mapList;
    }

    public List<Map<String,Object>> getBillNoAccounting(Integer org, Date beginDate,Date endDate,Boolean isAccount,Integer type) {
        Map<String,Object> params = new HashMap<>();
        String hql = "select";
        if (1==type){
            hql+= " date_format(billDate,'%Y年'), "; //按年
        }else if (2==type){
            hql+= " date_format(billDate,'%Y年%m月'), "; //按年月
        }
        hql+="count(id) from FinanceAccountBill where org_=:org";
        params.put("org",org);
        if (beginDate!=null){
            hql+=" and billDate>=:beginDate";
            params.put("beginDate",beginDate);
        }
        if (endDate!=null){
            hql+=" and billDate<:endDate";
            params.put("endDate",endDate);
        }
        if (isAccount!=null){
            hql+=" and isAccount=:isAccount";
            params.put("isAccount",isAccount);
        }
        hql+=" and id not in (select billDetail_ from AccountDetail where org_=:org and billDetail_ is not null)";  //去掉和AccountDetail中重复的
        if (1==type){
            hql+= " group by date_format(billDate,'%Y年')"; //按年
        }else if (2==type){
            hql+= " group by date_format(billDate,'%Y年%m月')"; //按年月
        }
        List<Object[]> list = financeAccountBillDao.getListByHQLWithNamedParams(hql,params);
        List<Map<String,Object>> mapList = new ArrayList<>();
        for (Object[] ob:list) {
            Map<String,Object> map = new HashMap<>();
            map.put("year",ob[0]); //年份
            map.put("num",ob[1]); //数量
            map.put("typeEntity","accountDetail"); //为了区分是accountDetail还是bill里面的数据
            mapList.add(map);
        }
        return mapList;
    }

    @Override
    public Map<String, Object> notRecordedList(Integer org, Date beginDate, Date endDate,Integer type) {
        Map<String,Object> map = new HashMap<>();
        List<Map<String,Object>> accountNum = getDetailNotRecorded(org,beginDate,endDate,type);
        List<Map<String,Object>> billNum = getBillNotRecorded(org,beginDate,endDate,type);
        List<Map<String,Object>> numList = new ArrayList<>();
        if (accountNum.size()>0&&billNum.size()>0){
            numList.addAll(accountNum);
            numList.addAll(billNum);
        }else if (accountNum.size()>0){
            numList.addAll(accountNum);
        }else if (billNum.size()>0){
            numList.addAll(billNum);
        }
        List<Map<String,Object>> numList1 = new ArrayList<>();
        List<Map<String,Object>> numListRemove = new ArrayList<>();
        for (Map<String,Object> map1:numList) {
            String year = (String) map1.get("year");
            Long num = (Long) map1.get("num");
            String typeEntity = (String) map1.get("typeEntity");
            for (int i=numList.size()-1;i>=0&&i<numList.size();i--) {
                Map<String,Object> map3 = numList.get(i);
                String year1 = (String) map3.get("year");
                String typeEntity1 = (String) map3.get("typeEntity");
                if (StringUtils.isNotEmpty(year1)&&StringUtils.isNotEmpty(year)&&year.equals(year1)&&typeEntity.equals("accountDetail")&&typeEntity1.equals("accountBill")){  //不能是一个
                    Long num1 = (Long) map3.get("num");
                    num = num+num1;
                    map1.put("num",num.intValue());
                    numListRemove.add(map3);
                    break;
                }
            }
            numList1.add(map1);
        }
        numList1.removeAll(numListRemove);
        map.put("numList",numList1);
        return map;
    }

    //type 1-按年查询  2-按年月查询
    public List<Map<String,Object>> getDetailNotRecorded(Integer org, Date beginDate,Date endDate,Integer type) {
        Map<String,Object> params = new HashMap<>();
        String hql = "select";
        if (1==type){
            hql+= " date_format(billDate,'%Y年'), "; //按年
        }else if (2==type){
            hql+= " date_format(billDate,'%Y年%m月'), "; //按年月
        }
        hql+="count(id) from AccountDetail where voucher is null and accountantStatus='1' and org_=:org";
        params.put("org",org);
        if (beginDate!=null){
            hql+=" and billDate>=:beginDate";
            params.put("beginDate",beginDate);
        }
        if (endDate!=null){
            hql+=" and billDate<=:endDate";
            params.put("endDate",endDate);
        }
        hql+=" and id not in (select id from AccountDetail where method in ('6','7','8') and credit is not null)"; //去掉内部支出性转账的收入，只取支出
        if (1==type){
            hql+= " group by date_format(billDate,'%Y年')"; //按年
        }else if (2==type){
            hql+= " group by date_format(billDate,'%Y年%m月')"; //按年月
        }
        List<Object[]> list = accountDetailDao.getListByHQLWithNamedParams(hql,params);
        List<Map<String,Object>> mapList = new ArrayList<>();
        for (Object[] ob:list) {
            Map<String,Object> map = new HashMap<>();
            map.put("year",ob[0]); //年份
            map.put("num",ob[1]); //数量
            map.put("typeEntity","accountDetail"); //为了区分是accountDetail还是bill里面的数据
            mapList.add(map);
        }
        return mapList;
    }

    public List<Map<String,Object>> getBillNotRecorded(Integer org, Date beginDate,Date endDate,Integer type) {
        Map<String,Object> params = new HashMap<>();
        String hql = "select";
        if (1==type){
            hql+= " date_format(billDate,'%Y年'), "; //按年
        }else if (2==type){
            hql+= " date_format(billDate,'%Y年%m月'), "; //按年月
        }
        hql+= "count(id) from FinanceAccountBill where voucher is null and accountantStatus='1' and org_=:org";
        params.put("org",org);
        if (beginDate!=null){
            hql+=" and billDate>=:beginDate";
            params.put("beginDate",beginDate);
        }
        if (endDate!=null){
            hql+=" and billDate<=:endDate";
            params.put("endDate",endDate);
        }
        hql+=" and id not in (select billDetail_ from AccountDetail where org_=:org and billDetail_ is not null)";  //去掉和AccountDetail中重复的
        if (1==type){
            hql+= " group by date_format(billDate,'%Y年')"; //按年
        }else if (2==type){
            hql+= " group by date_format(billDate,'%Y年%m月')"; //按年月
        }
        List<Object[]> list = financeAccountBillDao.getListByHQLWithNamedParams(hql,params);
        List<Map<String,Object>> mapList = new ArrayList<>();
        for (Object[] ob:list) {
            Map<String,Object> map = new HashMap<>();
            map.put("year",ob[0]); //年份
            map.put("num",ob[1]); //数量
            map.put("typeEntity","accountBill"); //为了区分是accountDetail还是bill里面的数据
            mapList.add(map);
        }
        return mapList;
    }

    @Override
    public List<AccountDetail> getNotRecordedDetailList(Integer org, Date beginDate, Date endDate) {
        Map<String,Object> params = new HashMap<>();
        String hql="from AccountDetail where voucher is null and accountantStatus='1' and org_=:org";
        params.put("org",org);
        if (beginDate!=null){
            hql+=" and billDate>=:beginDate";
            params.put("beginDate",beginDate);
        }
        if (endDate!=null){
            hql+=" and billDate<:endDate";
            params.put("endDate",endDate);
        }
        hql+=" and id not in (select id from AccountDetail where method in ('6','7','8') and credit is not null)"; //去掉内部支出性转账的收入，只取支出
        List<AccountDetail> list = accountDetailDao.getListByHQLWithNamedParams(hql,params);
        return list;
    }

    @Override
    public List<FinanceAccountBill> getNotRecordedBillList(Integer org, Date beginDate, Date endDate) {
        Map<String,Object> params = new HashMap<>();
        String hql = "from FinanceAccountBill where voucher is null and accountantStatus='1' and (cheque_ is not null or financeReturn.id is not null) and org_=:org";
        params.put("org",org);
        if (beginDate!=null){
            hql+=" and billDate>=:beginDate";
            params.put("beginDate",beginDate);
        }
        if (endDate!=null){
            hql+=" and billDate<=:endDate";
            params.put("endDate",endDate);
        }
        hql+=" and id not in (select billDetail_ from AccountDetail where org_=:org and billDetail_ is not null)";  //去掉和AccountDetail中重复的
        List<FinanceAccountBill> list = financeAccountBillDao.getListByHQLWithNamedParams(hql,params);
        return list;
    }

    @Override
    public List<AccountDetail> getNoAccountingDetailList(Integer org, Date beginDate, Date endDate,Boolean isAccount) {
        Map<String,Object> params = new HashMap<>();
        String hql ="from AccountDetail where org_=:org";
        params.put("org",org);
//        if (sourceType!=null&&1==sourceType){
//            hql += " and createDate>=:beginDate and createDate<:endDate";
//            params.put("beginDate", beginDate);
//            params.put("endDate", endDate);
//        }else {
//            if (beginDate != null) {
//                hql += " and billDate>=:beginDate";
//                params.put("beginDate", beginDate);
//            }
//            if (endDate != null) {
//                hql += " and billDate<:endDate";
//                params.put("endDate", endDate);
//            }
//        }
        if (beginDate != null) {
            hql += " and billDate>=:beginDate";
            params.put("beginDate", beginDate);
        }
        if (endDate != null) {
            hql += " and billDate<=:endDate";
            params.put("endDate", endDate);
        }
        if (isAccount!=null){
            hql+=" and aIsAccount=:aIsAccount";
            params.put("aIsAccount",isAccount);
        }

        List<AccountDetail> accountDetails = accountDetailDao.getListByHQLWithNamedParams(hql,params);
        return accountDetails;
    }

    @Override
    public List<FinanceAccountBill> getNoAccountingBillList(Integer org, Date beginDate, Date endDate,Boolean isAccount) {
        Map<String,Object> params = new HashMap<>();
        String hql ="from FinanceAccountBill where org_=:org";
        params.put("org",org);
//        if (sourceType!=null&&1==sourceType){
//            hql += " and createDate>=:beginDate and createDate<:endDate";
//            params.put("beginDate", beginDate);
//            params.put("endDate", endDate);
//        }else {
//            if (beginDate != null) {
//                hql += " and billDate>=:beginDate";
//                params.put("beginDate", beginDate);
//            }
//            if (endDate != null) {
//                hql += " and billDate<=:endDate";
//                params.put("endDate", endDate);
//            }
//        }
        if (beginDate != null) {
            hql += " and billDate>=:beginDate";
            params.put("beginDate", beginDate);
        }
        if (endDate != null) {
            hql += " and billDate<=:endDate";
            params.put("endDate", endDate);
        }
        if (isAccount!=null){
            hql+=" and isAccount=:isAccount";
            params.put("isAccount",isAccount);
        }
        List<FinanceAccountBill> financeAccountBills = financeAccountBillDao.getListByHQLWithNamedParams(hql,params);
        return financeAccountBills;
    }

    @Override
    public Integer updateVoucher(Integer datailId, Integer billId, Integer voucher, Boolean isAccount) {
        Integer status = 1;
        if (datailId!=null){
            AccountDetail accountDetail = accountDetailDao.get(datailId);
            accountDetail.setAccountantStatus("2");
            if (voucher!=null){
                accountDetail.setVoucher(voucher);
            }
            if (isAccount!=null){
                accountDetail.setaIsAccount(isAccount);
            }else {
                accountDetail.setaIsAccount(true);
            }
            accountDetailDao.update(accountDetail);

        }else if (billId!=null){
            FinanceAccountBill financeAccountBill = financeAccountBillDao.get(billId);
            financeAccountBill.setAccountantStatus("2");
            if (voucher!=null){
                financeAccountBill.setVoucher(voucher);
            }
            if (isAccount!=null){
                financeAccountBill.setAccount(isAccount);
            }else {
                financeAccountBill.setAccount(true);
            }
            financeAccountBillDao.update(financeAccountBill);
        }else {
            status=0;
        }
        return status;
    }

    @Override
    public AccountDetail getAccountDetailByMethod(Integer org,Integer accountId, String method) {
        Map<String,Object> params = new HashMap<>();
        String hql ="from AccountDetail where org_=:org";
        params.put("org",org);
        if (accountId!=null){
            hql+=" and accountId_=:accountId";
            params.put("accountId",accountId);
        }
        if (StringUtils.isNotEmpty(method)){
            hql+=" and method=:method";
            params.put("method",method);
        }
        hql+=" order by id desc";
        AccountDetail accountDetail = (AccountDetail) accountDetailDao.getByHQLWithNamedParams(hql,params);
        return accountDetail;
    }

    @Override
    public Map<String, Object> getDetailBillCreateDate(Integer datailId, Integer billId) {
        Integer creator = null;  //创建人id
        String createName = ""; //创建人
        Date createDate = null;  //创建时间
        if (datailId!=null){
            AccountDetail accountDetail = accountDetailDao.get(datailId);
            if (accountDetail!=null) {
                creator = accountDetail.getCreator();
                createName = accountDetail.getCreateName();
                createDate = accountDetail.getCreateDate();
            }
        }
        if (billId!=null){
            FinanceAccountBill financeAccountBill = financeAccountBillDao.get(billId);
            if (financeAccountBill!=null) {
                creator = financeAccountBill.getCreator();
                createName = financeAccountBill.getCreateName();
                createDate = financeAccountBill.getCreateDate();
            }
        }
        Map<String,Object> map = new HashMap<>();
        map.put("creator",creator); //创建人id
        map.put("createName",createName); //创建人
        map.put("createDate",createDate);  //创建时间
        return map;
    }

    @Override
    public Map<String, Object> debitTaxCategory(User user, String taxCategorys) {
        Map<String,Object> map = new HashMap<>();
        List taxCategoryList = JSONArray.fromObject(taxCategorys);
        for (int i=0;i<taxCategoryList.size();i++){
            JSONObject taxCategory = JSONObject.fromObject(taxCategoryList.get(i));
            Integer taxId = taxCategory.getInt("taxId");  //税种id
            String taxCategoryName = taxCategory.getString("taxCategoryName");  //税种名称
            Integer businessPeriodBegin = taxCategory.getInt("businessPeriodBegin");  //税款所属时期-开始时间
            Integer businessPeriodEnd = taxCategory.getInt("businessPeriodEnd");   //税款所属时期- 结束时间
            Integer report = taxCategory.getInt("report");  //报表id
            String periodBegin = taxCategory.getString("periodBegin");  //需申报的开始日期 String yyyy-MM-dd
            String periodEnd = taxCategory.getString("periodEnd");   //需申报的结束日期 String yyyy-MM-dd
            String method = taxCategory.getString("method");  //1-现金 5-银行转账
            Double money = taxCategory.getDouble("money");  //金额
            Integer financeAccountId = taxCategory.getInt("financeAccountId");  //银行账户id
            String memo = taxCategory.getString("memo");  //(备注)
            String factDate = taxCategory.getString("factDate");  //实际缴纳日期
            String imgPaths = taxCategory.getString("imgPaths");  //图片
            Integer taxPeriodIdInsert = taxCategory.getInt("taxPeriodIdInsert"); //申报记录id

            FinanceAccount financeAccount = financeAccountDao.get(financeAccountId);
            AccountPeriod accountPeriod = accountService.getAccountPeriodByMonth( financeAccount.getId(), new Date());

            //使用BigDecimal类构造方法传入double类型时，计算的结果也是不精确的。
            //因为不是所有的浮点数都能够被精确的表示成一个double 类型值，因此它会被表示成与它最接近的 double 类型的值。必须改用传入String的构造方法。这一点在BigDecimal类的构造方法注释中有说明。
            BigDecimal me = new BigDecimal(money.toString());
            BigDecimal ba = new BigDecimal(financeAccount.getBalance().toString());
            BigDecimal jieguo = ba.subtract(me);

            if (jieguo.doubleValue() < 0) {
                map.put("status", 4);//余额不足
                map.put("content","余额不足");
                break;
            } else if (0 == financeAccount.getAccountStatus()) {
                map.put("status", 2);//此账户已被冻结，不允许操作
                map.put("content","此账户已被冻结，不允许操作");
                break;
            } else {
                if (accountPeriod == null) {
                    try {
                        FinanceUtils.addPeroid(financeAccount.getId(), user, accountService);
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                }
                accountPeriod = accountService.getAccountPeriodByMonth( financeAccount.getId(), new Date());//再查一遍
                if (accountPeriod.getBuildState() != null) {
                    if (accountPeriod.getBuildState().equals("已生成"))
                        map.put("status", 3);//已生成
                }
                AccountPeriod ap = accountService.getAccountPeriodByDay(financeAccount.getId(), new Date());//找到日结

                AccountDetail accountDetail = new AccountDetail();
                accountDetail.setDebit(new BigDecimal(money));
                accountDetail.setBillAmount(new BigDecimal(money));  //票面金额
                accountDetail.setCreateDate(new Date());
                accountDetail.setCreator(user.getUserID());
                accountDetail.setCreateName(user.getUserName());
                accountDetail.setOrg(user.getOrganization());
                accountDetail.setAccount(accountPeriod.getId().toString());
                accountDetail.setAuditDate(new Date());
//                accountDetail.setAuditorName(auditorName);//经手人
                accountDetail.setMethod(method);
                accountDetail.setMemo(memo);
//                accountDetail.setOppositeCorp(oppositeCorp);//收款单位
                accountDetail.setFid(financeAccount.getId().toString());
                accountDetail.setAccountId(financeAccount);//账户外键
                accountDetail.setType("4");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
                if (financeAccount.getAccount()!=null){
                    accountDetail.setAccountBank(financeAccount.getBankName() + financeAccount.getAccount());
                }else {
                    accountDetail.setAccountBank(financeAccount.getBankName());
                }
                accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择
                accountDetail.setModityStatus("1");  //数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次) 3-关闭账户后不可修改
                accountDetail.setSource("1");
                accountDetail.setSubType("5"); //帐务子类:支出时,1-本人的公务支出（财务费用除外、需入库物品的报销除外）,2-其他同事的公务支出（财务费用除外，需入库物品的报销除外）,3-需入库物品的报销,4-社保/公积金，5-税款,6-汇划费,7-其他财务费用的支出,8-以上类别以外的支出
//                accountDetail.setStakeholderCategory(stakeholderCategory);  //类别:1-供应商,2-员工,3-自行录入
//                accountDetail.setStakeholder(stakeholder); //干系人ID(收款单位的id)
//                accountDetail.setBillQuantity(billQuantity);  //票据数量
                String summary=businessPeriodBegin+"-"+businessPeriodEnd+"期间的税款（"+taxCategoryName+")"; //摘要”展示为“XXXXXXXX-XXXXXXXX期间的税款（XXXXXXXXX）”，其中“日期”取该税款的所属时期，括号内取相应税种的名称
                accountDetail.setSummary(summary);
                accountDetail.setPurpose(summary);//用途
                accountDetail.setBillDate(NewDateUtils.dateFromString(businessPeriodBegin.toString(),"yyyy-MM-dd"));  //票据日期
                accountDetail.setFactDate(NewDateUtils.dateFromString(factDate,"yyyy-MM-dd"));  //实际(付款/支出)日期'
                accountDetail.setBusinessKey(taxId);  //报税id
                accountDetail.setBusinessPeriodBegin(businessPeriodBegin);  //业务期起始(YYYYMMDD)(1.207财务税种新增)
                accountDetail.setBusinessPeriodEnd(businessPeriodEnd);  //业务期截止(YYYYMMDD)(1.207财务税种新增)

                BigDecimal debit = accountPeriod.getDebit().add(accountDetail.getDebit());
                BigDecimal balance = accountPeriod.getBalance().subtract(accountDetail.getDebit());
                financeAccount.setDebit(financeAccount.getDebit().add(accountDetail.getDebit()));//计入账户支出
                financeAccount.setBalance(financeAccount.getBalance().subtract(accountDetail.getDebit()));//从余额减去支出
                accountDetail.setBalance(financeAccount.getBalance());
                accountPeriod.setDebit(debit);
                accountPeriod.setBalance(balance);

//                debit = ap.getDebit().add(accountDetail.getDebit());
//                balance = ap.getBalance().subtract(accountDetail.getDebit());
                ap.setDebit(ap.getDebit().add(accountDetail.getDebit()));
                ap.setBalance(financeAccount.getBalance());

                accountService.updateAccountPeroid(accountPeriod);//月结
                accountService.updateAccountPeroid(ap);//日结
                accountService.updateFinanceAccount(financeAccount);//账户
                accountService.saveAccountDetail(accountDetail);//明细

                //将数据保存进报税事务,报表的详情
                Integer acctTaxDetailId = this.setTaxFactAmount(user, taxId, report, new BigDecimal(money), periodBegin.substring(0, 7), accountDetail.getFactDate(), accountDetail.getId(),businessPeriodBegin,businessPeriodEnd,periodBegin,periodEnd,taxPeriodIdInsert,null);
                accountDetail.setBusiness(acctTaxDetailId);
                accountDetailDao.update(accountDetail);


                if (StringUtils.isNotEmpty(imgPaths)){  //添加图片
                    JSONArray jsonValues = JSONArray.fromObject(imgPaths,new JsonConfig());
                    List imgPathList = (List) JSONArray.toCollection(jsonValues);
                    for (int j=0;j<imgPathList.size();j++){
                        FinanceAccountBillImage financeAccountBillImage = this.addBillImage(user,accountDetail.getId(),null,"1",imgPathList.get(j).toString(),j+1);

                        FinanceBillUsing financeBillUsing = new FinanceBillUsing(financeAccountBillImage.getId(),FinanceAccountBillImage.class);
                        uploadService.addFileUsing(financeBillUsing, imgPathList.get(j).toString(),null, user, "数据录入");//新增引用表
                    }
                }
                map.put("status", 1);//成功
                map.put("content","操作成功");
            }
        }
        return map;
    }

    @Override    //将数据保存进报税事务
    public Integer setTaxFactAmount(User user, Integer taxId, Integer report, BigDecimal money, String period, Date factDate, Integer accountDetail,
        Integer belongsBegin,Integer belongsEnd,String periodBegin,String periodEnd,Integer taxPeriodIdInsert,Integer taxPeriodIdUpdate) {
        AcctTaxDetailEntity detailEntity = new AcctTaxDetailEntity();
        detailEntity.setOrg(user.getOid());
        detailEntity.setTax(taxId);  //税种id
        detailEntity.setReport(report);  //报表id
        detailEntity.setFactAmount(money);  //金额
        detailEntity.setPeriod(period);   //报表需申报的月份，格式yyyy-MM  PC端选择的某条报税记录中的起日期
        detailEntity.setPayTime(factDate);   //缴款时间
        detailEntity.setAccountDetail(accountDetail);   //财务明细账ID
        detailEntity.setBelongsBegin(belongsBegin.toString());   //所属日期开始时间
        detailEntity.setBelongsEnd(belongsEnd.toString());    //所属日期结束时间
        if (StringUtils.isNotEmpty(periodBegin)){
            Date periodBegin1 = NewDateUtils.dateFromString(periodBegin,"yyyy-MM-dd");
            detailEntity.setPeriodBegin(NewDateUtils.dateToString(periodBegin1,"yyyyMMdd"));   //申报记录的开始时间
        }
        if (StringUtils.isNotEmpty(periodEnd)){
            Date periodEnd1 = NewDateUtils.dateFromString(periodEnd,"yyyy-MM-dd");
            detailEntity.setPeriodEnd(NewDateUtils.dateToString(periodEnd1,"yyyyMMdd"));   //申报记录的结束时间
        }
        detailEntity.setUpdateDate(new Date());
        detailEntity.setUpdateName(user.getUserName());
        detailEntity.setUpdator(user.getUserID());
        detailEntity.setTaxPeriodIdInsert(taxPeriodIdInsert); //修改后的申报记录id
        detailEntity.setTaxPeriodIdUpdate(taxPeriodIdUpdate);  //修改前的申报记录id
        Integer detailEntityId = acctReportTaxService.setTaxFactAmount(detailEntity);

        return detailEntityId;
    }

    @Override
    public Map<String,Object> getTaxMethod(User user,Integer financeDetailId) {
        Map<String,Object> map = new HashMap<>();
//        String hql = "from AccountDetailHistory where org=:org";
//        map.put("org",user.getOid());
//        if (financeDetailId!=null){
//            hql+=" and detailId=:detailId";
//            map.put("detailId",financeDetailId);
//        }
//        List<AccountDetailHistory> accountDetailHistories = accountDetailHistoryDao.getListByHQLWithNamedParams(hql,map);
//        List<Map<String,Object>> taxUpdateRecords = new ArrayList<>();
//        for (AccountDetailHistory accountDetailHistory:accountDetailHistories) {
//            Map<String,Object> map1 = new HashMap<>();
//            map1.put("factDate",accountDetailHistory.getFactDate());  //实际缴纳日期
//            map1.put("createDate",accountDetailHistory.getCreateDate()); //修改时间
//            map1.put("createName",accountDetailHistory.getCreateName()); //修改人
//            taxUpdateRecords.add(map1);
//        }
        String method = "";
        List<FinanceAccountBillImage> financeAccountBillImages = new ArrayList<>();
        if (financeDetailId!=null) {
            AccountDetail accountDetail = accountDetailDao.get(financeDetailId);
            method = accountDetail.getMethod();
            financeAccountBillImages = this.getBillImageByIds(accountDetail.getOrg_(),accountDetail.getId(),null);

        }
        map.put("method",method);  //支出方式
        map.put("financeAccountBillImages",financeAccountBillImages);  //图片信息
        return map;
    }
}
