#edit table td{ padding:5px 15px; vertical-align: top; line-height: 30px; word-break: break-word;}
.personCon{ border:1px solid #ddd; padding: 10px; margin-top:15px; border-radius:4px;   }
.person{ padding:2px 0 2px 10px; border:1px solid #4db3ea; color: #0c91e5; margin:2px;border-radius:3px;     }
.person .fa{width:20px; height:20px; color: #c47a71; margin-left:10px;  }
.red{ color:red;  }


.chooseCusBtn{ background: #ddd; width: 40px; display:inline-block; text-align: center; line-height: 30px; height: 30px; margin-left: -5px;  }
.linkBtn{ color: #036bc0; line-height: 30px; cursor: default;font-weight: bold;}
.linkBtn:hover{ color: #0482ea; }
.viewsList { margin:20px auto;   }
.viewsList>li{ padding: 0 10px;list-style: none; background:#fff; line-height:30px;  }
.viewsList>li:nth-child(even){ background:#f5f5f5;  }
.viewsList .fa{ padding-right: 10px;color: #50a9ea;  }
#contactFlag{ line-height:30px;  }

.contactflex{display:flex;padding-left: 30px;}
.sale-con{padding-left:10px;min-width: 142px;line-height:32px;}
.sale_ttl1{ display: inline-block; min-width: 100px; text-align: right; line-height: 32px; vertical-align: top;  }
.see_otherContact{ margin: 20px auto -1px; overflow-y:auto; text-align:center; border:1px solid #ddd; max-height:150px;  }
.see_otherContact li{list-style: none; border:1px solid #ddd; border-left:none; border-top:none; display:inline-flex; width:220px; line-height:30px; float: left;  }
.see_otherContact li span:nth-child(1){ border-right:1px solid #ddd;  flex: 1;   }
.see_otherContact li span{ flex: 2; }
.see_otherContact li:last-child{ clear: both; }
#uploadCard{display:inline-block;}
.bussnessCard{display: inline-block;}
.bussnessCard .filePic{display:inline-block;width: 260px; height: 150px; background-size: contain;  background-repeat: no-repeat;}
.clearLableText{position: absolute;  right: 8px;bottom: 6px;  cursor: pointer;  }
.definedCon{position: relative;margin: auto;text-align: center;  width: 300px; }
.definedCon .ttl{font-weight:600;}
.definedCon input{padding-right:10px;width:100%;}
.clearLableText{position: absolute;  right: 8px;bottom: 6px;  cursor: pointer;  }
.header_query{padding: 20px 0;margin-bottom: 30px;border-bottom: 1px solid #bebebe;}
.clearFt{clear:both; overflow: hidden;}
.elemFlex{  display: flex;justify-content: space-between;align-items: center;}
#edit .recordForm table{width: 96%;}
#edit .recordForm table .purposeCon{display: inline-block;}
.flexRow{display: flex;justify-content: space-between;}
.addInterview .flexRow .linkBtn{white-space:nowrap;}
#edit .recordForm table tr td:first-child{width: 142px;padding-right: 0;}
#scan table tr td:first-child{width: 142px;}
.bounce_Fixed2 , .bounce_Fixed3 {  position: fixed;  display: none;  background: rgba(100, 100, 100, 0.5);  width: 100%;  padding-bottom: 200px;  }
.bounce_Fixed2   { z-index: 10002;  }
.bounce_Fixed3 { z-index: 10003;  }
.keySearch select,.keySearch input{border: 1px solid #dcdfe6;border-radius: 2px;background-color: #fff;display: inline-block;height: 30px;line-height: 30px;min-width: 130px;
    background-image: none;box-sizing: border-box;color: #606266;font-size: inherit;outline: none;padding: 0 8px;transition: border-color .2s cubic-bezier(.645,.045,.355,1);}
.txtTip{margin-right: 10px;}
.addInterview hr, #scan hr{border-top:1px solid #bebebe;}
.careBlue{color: #0070bf;}
.uploadify-queue,.headTip{display: none;}
.posRt{text-align: right;}
.wrapBody{margin: auto;width: 90%;}
.aBody{margin: auto;width: 70%;}
.narrowA{margin-left: 10px;width: 90%;}
.padLt{margin-left: 15px;}
.padBt{margin-bottom: 12px;}
.itemC{margin-bottom: 12px;}
.itemC p{margin-bottom: 4px;line-height: 32px;}
.entryCon textarea, .textMax{width:430px;}
#addOtherTogether .otherContact input{width:350px;}
#chooseFellowUn ul li{overflow: hidden;}
.redLinkBtn{color: red;display: inline-block;vertical-align: middle;font-weight: bold;}
.gapR{margin-right: 50px;}
#participant{float: right;width: 300px;}
.widBt{margin-bottom: 22px;line-height: 30px;}
.interviewerCon{width: 600px;display: inline-block;}
.businessCard .filePic {display: inline-block;width: 260px;height: 150px;background-size: contain;background-repeat: no-repeat;}
.xing:before {content: "*";width: 5px;height: 5px;display: inline-block;color: red;font-weight: bolder;position: relative;}
#interviewList tr td:nth-child(2) {white-space:nowrap;text-overflow: ellipsis;overflow: hidden;}
.ty-table{table-layout: fixed;}
#scan table{table-layout: fixed;word-wrap: break-word;}




