<template>
  <div id='complaint' v-loading.fullscreen.lock='loading'>
    <TY_NavTop title='投诉处理' ></TY_NavTop>
    <div class='container'>
      <div v-if='!result'>
        <p class='blue'>您可逐年查询您提交但被驳回的投诉立案申请</p>
        <el-form :label-position='left' :rules='rules' label-width='110px' ref='ticketInfo'>
          <el-form-item label='' prop='date1'>
            <el-date-picker type='year' placeholder='请选择年份' value-format='yyyy年报' format='yyyy年报'  v-model='time' size='small' ></el-date-picker>
          </el-form-item>
        </el-form>
        <div class='centerBtn'>
          <input type='submit' class='ui-btn ui-btn_info' value='确定' @click='searchOk()'>
        </div>
      </div>
      <div v-if='result'>
        <p class='blue'>
          自{{timeBegin | formatDay('YYYY年MM月DD日')}} - {{ timeEnd | formatDay('YYYY年MM月DD日')}}，
          您提交但被驳回的投诉立案申请共{{num}}个</p>
        <a class='ui-cell' @click='jump(item.id)' v-for='(item, index) in list' v-bind:key='index'>
          <div class='ui-cell__bd'>
            <div class='ui-cell_con'>
              <div class='cusName'>{{item.customName}}</div>
              <div v-if="item.receiptTime"><span class='info'>接到投诉的日期</span><span class='info'>{{item.receiptTime | formatDay('YYYY/MM/DD')}}</span></div>
              <div><span class='info'>录入者</span><span class='info'>{{item.createName}} {{item.createTime | formatDay('YYYY/MM/DD HH:mm:ss')}}</span></div>
            </div>
          </div>
          <div class='ui-cell__ft'></div>
        </a>
      </div>
    </div>
  </div>
</template>
<style lang='less'>
  #complaint{
    .cusName{ margin-bottom:1rem; }
    .centerBtn{ padding-top:2rem;  }
    .info{ margin-left:2rem; }
    .blue{ padding:0.5rem; background:#DBEEF3; }
  }
</style>
<script>
export default {
  name: 'applyComplaintSearch',
  data () {
    return {
      loading: false,
      result: false,
      timeBegin: '',
      timeEnd: '',
      list: [],
      num: 0,
      time: '',
      rules: {
        time: [
          { required: true }
        ]
      }
    }
  },
  created () {
  },
  destroyed: function () {
  },
  methods: {
    searchOk: function () {
      let _this = this
      _this.result = true
      _this.loading = true
      let timeS = _this.time.replace('年报', '')
      console.log('time', timeS)
      let userID = _this.sphdSocket.user.userID
      let requestStr = '?userID=' + userID + '&time=' + timeS
      _this.axios.post('../../../complaint/getComplaintEntry.do' + requestStr).then(data => {
        let res = data.data
        console.log(res)
        let info = res['data']
        _this.list = info['listComplaint']
        _this.num = String(info['num'])
        _this.timeBegin = info['timeBegin']
        _this.timeEnd = info['timeEnd']
        _this.loading = false
      }).catch((err) => {
        console.log('获取数据错误了！', err)
        _this.loading = false
      })
    },
    jump: function (id) {
      window.parent.floatToPage('../../../complaint/complaintInfo.do', { 'id': id, 't': 9 })
    }
  }
}
</script>
