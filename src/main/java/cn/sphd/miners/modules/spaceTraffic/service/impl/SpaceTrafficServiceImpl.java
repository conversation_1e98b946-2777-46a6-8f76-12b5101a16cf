package cn.sphd.miners.modules.spaceTraffic.service.impl;

import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.spaceTraffic.service.SpaceTrafficService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import cn.sphd.miners.modules.uploads.service.UploadService;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by 朱思旭 on 2021/4/19.
 */
@Service("spaceTrafficService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED)
public class SpaceTrafficServiceImpl implements SpaceTrafficService {
    @Autowired
    UploadService uploadService;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    UserService userService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;

    @Override
    public void spaceTrafficEveryDayTask(List<Integer> oids, Map<String, Object> data) {
        final Date callTime = NewDateUtils.joinDateTimeString(NewDateUtils.today(),"10:30");
        final String msgHead = "尊敬的用户：截止到" + NewDateUtils.dateToString(callTime,"yyyy年MM月dd日00点00分");
        final String spaceMemo1=msgHead+"，Wonderss系统里您剩余空间已不足5%。为了不影响您的正常使用，请及时采取措施！如已增加空间可忽略";
        final String spaceMemo2=msgHead+"，Wonderss系统里您剩余空间已经不多了。为了不影响您的正常使用，请及时采取措施！如已增加空间可忽略";
        final String trafficMemo1=msgHead+"，Wonderss系统里您剩余流量已不足5%。为了不影响您的正常使用，请及时采取措施！如已增加流量可忽略";
        final String trafficMemo2=msgHead+"，Wonderss系统里您剩余流量已经不多了。为了不影响您的正常使用，请及时采取措施！如已增加流量可忽略";
        final BigDecimal decimal95 = new BigDecimal(0.95);
        final BigDecimal decimal8 = new BigDecimal(0.8);
        if (oids.size()>0) {
            BigDecimal spaceB = new BigDecimal(data.get("ratedSpace").toString());
            Map<Integer,String> usedTraffic = (Map<Integer, String>) data.get("usedTraffic");
//            System.out.println("usedTraffic size=" + usedTraffic.size() + " : " + JSON.toJSONString(usedTraffic));
            for (Integer oid : oids) {
                BigDecimal usingSpaceB = new BigDecimal(uploadService.uploadFileSize(oid, null, "", null));
                if (usingSpaceB.compareTo(spaceB.multiply(decimal8)) > 0) {
                    SpaceTrafficRemind spaceTrafficRemind = new SpaceTrafficRemind(oid, usingSpaceB.compareTo(spaceB.multiply(decimal95)) > 0 ? spaceMemo1 : spaceMemo2);
                    clusterMessageSendingOperations.delayCall(callTime, spaceTrafficRemind);
                }

                BigDecimal trafficB = new BigDecimal(data.get("ratedTraffic").toString());
                BigDecimal useingTrafficB = new BigDecimal(usedTraffic.getOrDefault(oid, "0"));
                if (useingTrafficB.compareTo(trafficB.multiply(decimal8)) > 0) {
                    SpaceTrafficRemind spaceTrafficRemind = new SpaceTrafficRemind(oid, useingTrafficB.compareTo(trafficB.multiply(decimal95)) > 0 ? trafficMemo1 : trafficMemo2);
                    clusterMessageSendingOperations.delayCall(callTime, spaceTrafficRemind);
                }
            }
        }
    }
//    public void spaceTrafficEveryDayTask() {
//        final String url=System.getProperty("miners.glptApiRoot")+"/st/getSpaceTrafficByOid.do";
//        final Date callTime = NewDateUtils.dateFromString(NewDateUtils.dateToString(System.currentTimeMillis(), "yyyy-MM-dd") + " 10:30:00", "yyyy-MM-dd HH:mm:ss");
//        final String msgHead = "尊敬的用户：截止到" + NewDateUtils.dateToString(System.currentTimeMillis(),"yyyy年MM月dd日")+"00点00分";
//        StringBuffer memo = new StringBuffer(msgHead);
//
//        Integer oid=Integer.MIN_VALUE;
//        Integer readLimit = 500;
//        List<Organization> orgs = orgService.getOrgLimit(oid,readLimit);
//        BigDecimal decimal95 = new BigDecimal(0.95);
//        BigDecimal decimal8 = new BigDecimal(0.8);
//        while (orgs.size()>0){
//            for (Organization o : orgs) {
//                oid = o.getId();
//                Map<String,String> map=new HashMap<String,String>();
//                map.put("oid", oid.toString());
//                JsonResult jsonResult = MinersHttpClientUtils.postJson(url,map);
//                Map<String,Object> result = (Map<String, Object>) jsonResult.getData();
//
//                String ratedSpace = result.get("ratedSpace").toString();
//                Long usingSpace = uploadService.uploadFileSize(oid, null, "", null);
//                BigDecimal spaceB = new BigDecimal(ratedSpace);
//                BigDecimal usingSpaceB = new BigDecimal(usingSpace);
//                int a =   usingSpaceB.compareTo(spaceB.multiply(decimal95));
//                int b = usingSpaceB.compareTo(spaceB.multiply(decimal8));
//                if (b == 1) {
//                    memo.setLength(msgHead.length());
//                    if (a == 1) {
//                        memo.append("，Wonderss系统里您剩余空间已不足5%。为了不影响您的正常使用，请及时采取措施！如已增加空间可忽略");
//                    } else {
//                        memo.append("，Wonderss系统里您剩余空间已经不多了。为了不影响您的正常使用，请及时采取措施！如已增加空间可忽略");
//                    }
//                    SpaceTrafficRemind spaceTrafficRemind = new SpaceTrafficRemind(oid,memo.toString());
//                    clusterMessageSendingOperations.delayCall(callTime,spaceTrafficRemind);
//                }
//
//                String ratedTraffic = result.get("ratedTraffic").toString();
//                String usingTeaffic = result.get("usedTraffic").toString();
//                BigDecimal trafficB = new BigDecimal(ratedTraffic);
//                BigDecimal useingrafficB = new BigDecimal(usingTeaffic);
//                int c = useingrafficB.compareTo(trafficB.multiply(decimal95));
//                int d = useingrafficB.compareTo(trafficB.multiply(decimal8));
//                if (d == 1) {
//                    memo.setLength(msgHead.length());
//                    if (c == 1) {
//                        memo.append("，Wonderss系统里您剩余流量已不足5%。为了不影响您的正常使用，请及时采取措施！如已增加流量可忽略");
//                    } else {
//                        memo.append("，Wonderss系统里您剩余流量已经不多了。为了不影响您的正常使用，请及时采取措施！如已增加流量可忽略");
//
//                    }
//                    SpaceTrafficRemind spaceTrafficRemind = new SpaceTrafficRemind(oid,memo.toString());
//                    clusterMessageSendingOperations.delayCall(callTime,spaceTrafficRemind);
//                }
//            }
//            orgs = orgService.getOrgLimit(oid,readLimit);
//        }
//    }

    @Override
    public void sendSpaceTrafficMessage(Integer oid, String memo) {
        List<User> listUser = this.listThreeRole(oid);
        for(User u : listUser){
            String content = "操作时间 系统 " + NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss");
            userSuspendMsgService.saveUserSuspendMsg(1, memo, content, u.getUserID(), null, null);
        }
    }

    private List<User> listThreeRole(Integer oid){
        List<User> listUser = new ArrayList<>();
        User userSuper = userService.getUserByRoleCode(oid,"super");
        if (userSuper!=null) {
            listUser.add(userSuper);
        }
        User userSmallSuper = userService.getUserByRoleCode(oid,"smallSuper");
        if (userSmallSuper!=null) {
            listUser.add(userSmallSuper);
        }
        User userGeneral = userService.getUserByRoleCode(oid,"general");
        if (userGeneral!=null) {
            listUser.add(userGeneral);
        }
        return listUser;
    }
}