<template>
  <div class="level1">
    <li v-for="(item, index) in folderData" v-bind:key="index">
      <div class="ty-treeItem" :title="item.name" @click="folderManager(item)">
        <i :class="{'fa fa-angle-right': item.childStatus === '1', 'ty-fa': !item.childStatus || item.childStatus !== '1'}"></i>
        <i class="fa fa-folder"></i>
        <span>{{ item.name }}</span>
        <folderList :folderData="childList"></folderList>
      </div>
    </li>
<!--    <p id="tstip"></p>-->
  </div>
</template>

<script>
import * as api from "@/api/processInit"
export default {
  name: "folderChildren",
  data() {
    return {
      childList: []
    };
  },
  props:{
    folderData: { 'type': Array , 'default': [] }
  },
  mounted() {
  },
  methods: {
    folder<PERSON>anager(info) {
      let json = {"categoryId": info.id, "type": 1}
      let fileInfo = {}
      api.getChildrenDoc(json).then(res => {
        this.childList = res.data.data.childFolder || []
        fileInfo = {
          categoryId: info.id,
          pageInfo: res.data.data.pageInfo,
          fileList: res.data.data.list
        }
        this.$emit('fileEvent',fileInfo)
      })
    },
  }
}
</script>
