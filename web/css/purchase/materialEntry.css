.mainCon2,.mainCon3, .hd{ display:none;  }
.mainCon>div{ border:1px solid #ddd;border-radius:2px;  }
div.leCat{ width: 250px; min-height:600px;   float: left; color:#0b94ea; line-height:30px; position: relative;  padding-bottom: 81px;   }
div.leCat:hover{ cursor: default;  }
.leCat>.bottom{position: absolute; bottom:0; width:100%;   }
.leCat>div{ color: #333; padding-left:15px; font-weight: bold; background: #c0d3ea;    }
.leCat>ul>li{ padding-left:8px;font-size:13px; line-height: 25px;  }
.leCat>ul>li>.name{ white-space: nowrap; text-overflow: ellipsis; overflow: hidden; word-break: break-all; width:160px; display:inline-block;     }
.leCat>ul>li:hover{ background: #cdddea; font-weight: bold; color: #0f62ea;   }
.catCtrl{ width:45px; float: right; height: 16px; }
.catCtrl .fa{ width: 20px;  height: 16px; text-align: center;  }
.catCtrl .fa-trash{color:#ed5565;  }
.catCtrl .fa:hover{ font-weight:bold;  }
.btnCat{ padding-right:15px; color: #0f62ea }
.btnCat:hover{ text-decoration:underline; cursor: default; }
.catNum{ padding-right:15px; position: relative;
    top: -5px; }
.lePad{ padding-left:40px;  }
div.riMt{ margin-left:255px; padding:15px; min-height:600px;  }
div.riMt thead{ background: #f7e5baa6;  }
.search{ float:right;  }
.search>input{ height:30px; padding:0 5px; border: 1px solid #ccc; border-right:none;
    border-radius:4px 0 0 4px; width:250px;  }
.search>span{ display:inline-block;height:30px; float:right; border: 1px solid #ccc; cursor: default;
    background: #0b94ea; color:#fff; line-height:25px; padding:0 10px; border-radius:0 4px 4px 0; }

#mtEntry { min-width:700px;   }
#mtScan{ min-width:950px;   }
#catParents{ margin-left:20px;   }
#newCatName{ width:300px;   }
.item{ line-height:35px; }
.red{ color:red; }
.item-title{ display:inline-block; width:75px; text-align:center; }
#mtEntry .item-title, #updateImportMt .item-title{ text-align: left; }
input,select{ width:292px;  }
span.disabled{ color:#999;  }
.go2Cat:hover{ color:#0b94ea; text-decoration:underline; cursor: default;   }
.category{ display: inline-block; margin-left:15px;  }
#mtEntry .category{ margin-left: 0;  }
#mtEntry .faG1 .fa, #updateImportMt .faG1 .fa { color: #0b94ea }
#mtEntry select[disabled], #mtEntry input[disabled]{ background: #eaeaea; }
.exportStep {  padding-left: 40px;  }
.flexRow{  margin-left: 50px;  display: flex;  justify-content:space-between;  width: 356px;  }
.fileFullName{  width: 278px; line-height:26px;background: #fff;text-align: center;}
.ty-btn-middle{padding: 0px 22px;  height: 26px;  line-height: 26px;}
.left{  display: inline-block;  width: 80px;  }
.formItem{  position: relative; margin-top: 8px;  overflow: hidden;  clear: both;  }
.narrowLamp{ min-width: 650px;}
.userForm{width: 300px;  margin: 0 auto 20px;}
.clearBtn{position: absolute;  right: 38px;  top: 4px;  width: 14px;  height: 30px;font-style: normal;  color: #b0b0b0;  cursor: pointer;}
.ty-active-blue{border-bottom: 2px solid #0070c0;  color: #0070c0;}
.ty-threeTab{border-bottom: 1px solid #ccc;  font-size: 14px;  line-height: 50px;  height: 50px;}
.ty-threeTab li.ty-active-blue {  color: #0070c0;  }
.ty-threeTab li {  list-style: none;  display: inline-block;  height: 50px;  padding: 0 20px;  cursor: pointer;  }
.gap-lt{margin-left: 50px;}
.gap-Tp{margin-top: 30px;}
.gap-bt{margin-bottom: 10px;}
.mainCon3 {padding-left: 80px;}
.select0, .select1,.select2{padding: 20px 50px;}
.select1,.select2, .noSave1{display: none;}
.bg-yellow thead td {  border-color: #fff0;  background-color: #fff2cc;  }
.noSaved {padding: 20px 40px;}
.btnDo{font-weight: bold;}

.editTip{ margin:10px;border:1px solid #ccc; border-radius:3px; padding:10px 15px; clear: both; margin-top:15px;   }
.narrowBody{margin: 0 auto;width: 80%;}
.changeDot{margin-bottom: 12px;}
.changeDot span{margin-right: 12px;}
.stepItem {  margin-bottom: 20px;  }
.viewBtn .uploadify-button{ padding: 0 22px; min-width: 80px;  margin: 0; border-radius: 0; height: 26px;  line-height: 26px;background-color: #5d9cec;  color: #fff;border: none;}
.viewBtn .uploadify-button:hover{ background: #5d9cec;}
#kindEditName{ width:300px;  }
.line{margin-bottom: 10px;}
.faG1{ width:80px;  margin-left: 30px; display: inline-block;  }
.faG1 .fa{ margin-right: 10px; }
#importEnteryType select{ width: 112px; }
.maskImport{ width:100%; height:100%; position:absolute; z-index: 1; background: rgba(100,100,100, 0.3); top:0; left: 0;    }
#updateImportMt input[disabled]{ background: #ccc; }
.lenBtn{
    white-space: nowrap!important; /* 不换行 */
    overflow: hidden!important; /* 超出部分隐藏 */
    text-overflow: ellipsis!important; /* 溢出部分显示省略号 */
    width: 150px!important;
    display: inline-block!important;
}

#changeCat1{
    width: 310px; display: inline-block; float: right; text-align: right; line-height: 30px;
}
#curCatexp{
    color: #0b94ea;
    font-size: 14px;
    font-weight: bold;
}



