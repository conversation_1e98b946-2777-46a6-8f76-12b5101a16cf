package cn.sphd.miners.modules.accountantReportTax.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTriflesDetailDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTriflesDetailHistoryDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTriflesHistoryDao;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxDetailHistoryEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTriflesDetailHistoryEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTriflesHistoryEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

@Repository
public class AcctTriflesDetailHistoryDaoImpl extends BaseDao<AcctTriflesDetailHistoryEntity, Serializable> implements AcctTriflesDetailHistoryDao {

    @Autowired
    AcctTriflesDetailHistoryDao triflesDetailHistoryDao;

    @Override
    public List<AcctTriflesDetailHistoryEntity> getTriflesEditRecord(AcctTriflesDetailHistoryEntity triflesDetailHistoryEntity) {
        String hql = "from AcctTriflesDetailHistoryEntity where trifles=:trifles and period=:period and org=:org";

        Integer org = triflesDetailHistoryEntity.getOrg();
        Integer triflesId = triflesDetailHistoryEntity.getTrifles();
        String period = triflesDetailHistoryEntity.getPeriod();

        HashMap<String, Object> param = new HashMap<>();
        param.put("trifles", triflesId);
        param.put("period", period);
        param.put("org", org);
        if (triflesDetailHistoryEntity.getDelareTime() != null) {
            hql += " and DATE_FORMAT(delareTime,'%Y-%m-%d')=:delareTime";
            param.put("delareTime", NewDateUtils.dateToString(triflesDetailHistoryEntity.getDelareTime(),"yyyy-MM-dd"));
        }
        else {
            hql += " group by DATE_FORMAT(delareTime,'%Y-%m-%d') order by createDate desc";
        }
        List<AcctTriflesDetailHistoryEntity> list = triflesDetailHistoryDao.getListByHQLWithNamedParams(hql,param);

        return list;
    }
}
