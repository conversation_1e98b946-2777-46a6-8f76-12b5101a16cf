<!-- 本页面是 新增歌曲完成时使用的 -->
<template>
  <div class="lyricDetails">
    <Pageheader :title="pageTtl" :showBack="true" :runMyBackFun="true" @myBackFun="backFun" />
    <div class="live-container">
      <div class="txtCenter" @click="goPage('lycInfo')">
        <div class="ttl" v-if="pageTtl === '歌曲信息'">{{ playName }}</div>
        <div class="ttl" v-if="pageTtl === '戏曲信息'">{{ playName }} / {{ playXuanduan }} / {{ playLiuPai }}</div>
        <div v-if="songDetails.captionRequired">
          {{ playTime }}
          <el-icon class=" relaRight" v-if="fromAdd != 1">
            <arrow-right-bold color="#53B5A8" class="houseSty8" />
          </el-icon>
        </div>
      </div>
      <div v-if="songDetails.captionRequired" style=" width: 80%; margin: 0 auto;">
        <div class="tr">
          <span>原唱{{ (songDetails.originalPath && songDetails.originalPath.length > 5) ? '已' : '未' }}选择，伴奏{{ (songDetails.bgmPath && songDetails.bgmPath.length > 5 )? '已' : '未' }}选择</span>
          <span class="live-right line-link" @click="goPage('lycEdit')">修改</span>
        </div>
        <hr>
        <div class="tr">
          <span>原唱做背景音，查看效果</span>
          <span class="live-right line-link" @click="playLyric(1)">开始</span>
        </div>
        <div class="tr">
          <span>伴奏做背景音，查看效果</span>
          <span class="live-right line-link" @click="playLyric(2)">开始</span>
        </div>
        <hr>

      </div>
      <div v-else>
        <div class="tr">
          <span>本歌曲只有歌曲名称，无其他数据！</span>
          <span class="live-right line-link" @click="goPage('edutLyricInfo')">修改</span>
        </div>


      </div>
    </div>
    <LyricPlayer v-if="playLyc" :playName="playName" :lyricTxt="lyricTxt" :fileUrl="fileUrl" @closePlayLyc="closePlayLyc"></LyricPlayer>
  </div>
</template>
<script setup>
import { ref, onMounted, reactive } from 'vue';
import { useRouter } from "vue-router";
import { ElMessageBox, ElMessage } from 'element-plus';
import { songMessage } from '@/api/music.js'
import JSONBig from 'json-bigint'


const router = useRouter();
const playName = ref('')
const playXuanduan = ref('')
const playLiuPai = ref('')
const pageTtl = ref('')
const lyricTxt = ref('')
const playLyc = ref(false)
const playTime = ref('')
const fileUrl = ref('')
const fromAdd = ref('')
let songDetails = reactive('')

onMounted(() => {
  getDetails()
});
// onUnmounted(() => {
//   clearInterval(this.timer)
// })
const playLyric = (type) => {
  let uploadUrl = localStorage.getItem('uploadUrl');
  if(type === 1){ // 原唱
    fileUrl.value = uploadUrl + songDetails.originalPath
  }else { // 伴奏
    fileUrl.value = uploadUrl + songDetails.bgmPath
  }
  console.log(' fileUrl.value =',  fileUrl.value )
  playLyc.value = true

}
const getDetails = () => {
  let lyricDetailsItem = JSONBig.parse(localStorage.getItem('lyricDetailsItem'));
  console.log('lyricDetailsItem=', lyricDetailsItem)
  fromAdd.value = lyricDetailsItem.fromAdd
  console.log('fromadd = ', fromAdd)
  pageTtl.value = lyricDetailsItem.type === 2 ? '戏曲信息' :'歌曲信息'
  songMessage({
    'id': lyricDetailsItem.id,
    'type': lyricDetailsItem.seetype,
  }).then( res => {
    console.log('详情返回值=', res)
    const data = res.data
    localStorage.setItem('songMessage', JSONBig.stringify(data))
    const song = data.song
    songDetails = song
    console.log(' songDetails.value ',  songDetails )
    playName.value = song.name
    playXuanduan.value = song.selections
    playLiuPai.value = song.genres
    playTime.value = song.songDuration
    if(data.cmwRhythm){
      const cmwRhythm = data.cmwRhythm && data.cmwRhythm.rhythm
      lyricTxt.value = cmwRhythm

    }

  }).catch(err => {
    console.log('获取小青失败', err)
  })
}
const closePlayLyc = (val)=> {
  console.log('父级的 closePlayLyc')
  playLyc.value = val
  let timer = localStorage.getItem('timer')
  console.log('取消 clearInterval(t', timer)
  clearInterval(timer)
}
// creator：hxz 播放音乐
const playMusic = ()=> {
  playLyc.value = true
}

const goPage = (url) => {
  let lyricDetailsItem = JSONBig.parse(localStorage.getItem('lyricDetailsItem'));
  localStorage.setItem('quType', lyricDetailsItem.type ) // pageTtl.value = lyricDetailsItem.type === 2 ? '戏曲信息' :'歌曲信息'
  router.push({ name: url});
}
const backFun = () => {
  console.log('pageTtl.value ===', pageTtl.value)
  switch (pageTtl.value){
    case '戏曲信息':
      router.push({ name: 'lyricTaskXiQu'});
      break;
    case '歌曲信息':
      router.push({ name: 'lyricTask'});
      break;
    default:
      router.push({ name: 'homeManage'});
      break;

  }
}
</script>
<style lang="scss" scoped>
.lyricDetails{
  .txtCenter{
    text-align: center;
    margin-bottom: 30px;
  }
  .ttl{
    font-weight: bolder; line-height: 30px;
  }
  .tr{
    line-height: 50px;
  }
  .houseSty8{
    position: relative;
    top:3px;
  }
}
</style>
