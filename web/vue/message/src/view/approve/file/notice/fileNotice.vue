<template>
  <div id="notice_sheetRelate">
    <TY_NavTop :title="fileNotice.title"></TY_NavTop>
    <div class="container" v-loading="loading">
      <div class="topTip">
        <span v-html="fileNotice.tips"></span>
        <div v-if="fileNotice.needContactDM">如有异议，请及时联络文管！</div>
        <div v-if="fileNotice.hasBtn">点击“知道了”的数据将进入”即将消失的通知“。</div>
        <div class="text-right btn-group" v-if="fileNotice.hasBtn">
          <span class="link-blue" @click="setKnew(0)">全部设为“知道了”</span>
          <span class="link-blue" @click="willCleanNotice">即将消失的通知</span>
        </div>
      </div>
      <div class="list">
        <!--需批量签收的文件-->
        <div class="ty-fileItem active" v-for="(item ,index) in dataList" :key="index" v-if="fileNotice.type === 1" @click="handleBatchFile(item)">
          <div class="ty-fileRow">
            文件数量 {{item.batchNum}}个
            <div class="ui-cell__ft"></div>
          </div>
          <div class="ty-fileRow ty-fileHandle">
            <div class="ty-fileDetail">
              {{item.updateName || item.createName }}
              {{item.createDate || item.updateDate | formatDay}}
            </div>
          </div>
        </div>
        <!--新获得使用权限的文件-->
        <!--文件换版通知-->
        <!--文件废止通知-->
        <!--废止文件的复用通知-->
        <!--停用文件的复用通知-->
        <div class="ty-fileItem" v-for="(item ,index) in dataList" :key="index" v-if="[2, 3, 4, 5, 8].includes(fileNotice.type)" @click="handleFileDetail(item)">
          <div class="ty-fileRow">
            <div :class="'fileType file_'+item.version"></div>
            <div class="ty-fileInfo">
              <div class="ty-fileRow">
                <div class="ty-fileName" :title="item.fileName">{{item.fileName}}</div>
                <div class="ty-fileNoVersion text-right" :title="item.fileSn">G{{item.changeNum}} 编号 ：{{item.fileSn}}</div>
              </div>
              <div class="ty-fileRow">
                <div class="ty-fileDetail">
                  {{item.updateName || item.createName }}
                  {{item.createDate || item.updateDate | formatDay}}
                </div>
              </div>
            </div>
          </div>
        </div>
        <!--文件停用通知--><!--使用权限被取消的通知-->
        <div class="ty-fileItem" v-for="(item ,index) in dataList" :key="index" v-if="fileNotice.type === 6 || fileNotice.type === 9">
          <div class="ty-fileRow">
            <div :class="'fileType file_'+item.version"></div>
            <div class="ty-fileInfo">
              <div class="ty-fileRow">
                <div class="ty-fileName" :title="item.fileName">{{item.fileName}}</div>
              </div>
              <div class="ty-fileRow">
                <div class="ty-fileNoVersion" :title="item.fileSn">G{{item.changeNum}} {{item.fileSn}}</div>
              </div>
            </div>
          </div>
          <div class="ty-fileRow ty-fileHandle">
            <span class="link-blue" @click="setKnew(item.id)">知道了</span>
            <div class="ty-fileDetail">
              {{item.updateName || item.createName }}
              {{item.createDate || item.updateDate | formatDay}}
            </div>
          </div>
        </div>
        <!--文件夹停用通知-->
        <div class="ty-fileItem active" v-for="(item ,index) in dataList" :key="index" v-if="fileNotice.type === 7" @click="childFolder(item)">
          <div class="ty-fileRow">
            <div class="icon_folder"></div>
            <div class="ty-fileName" :title="item.categoryName">{{item.categoryName}}</div>
            <div class="ui-cell__ft"></div>
          </div>
          <div class="ty-fileRow ty-fileHandle">
            <span class="link-blue" @click="setKnew(item.id)">知道了</span>
            <div class="ty-fileDetail">
              {{item.updateName || item.createName }}
              {{item.createDate || item.updateDate | formatDay}}
            </div>
          </div>
        </div>
        <!--表格关联通知-->
        <div class="ty-fileItem" v-for="(item ,index) in dataList" :key="index" v-if="fileNotice.type === 10">
          <div class="ty-fileRow">
            <div class="item_title">文件编号/文件名称</div>
            <div class="item_content">{{item.allFileName}}</div>
          </div>
          <div class="ty-fileRow">
            <div class="item_title">表格编号/表格名称</div>
            <div class="item_content">{{item.allFormName}}</div>
          </div>
          <div class="ty-fileRow ty-fileHandle">
            <span class="link-blue" @click="setKnew(item.id)">知道了</span>
            <div class="ty-fileDetail">
              {{item.updateName || item.createName }}
              {{item.createDate || item.updateDate | formatDay}}
            </div>
          </div>
        </div>
        <!--表格解除关联通知-->
        <div class="ty-fileItem" v-for="(item ,index) in dataList" :key="index" v-if="fileNotice.type === 11">
          <div class="ty-fileRow">
            <div class="item_title">文件编号/文件名称</div>
            <div class="item_content">{{item.allFileName}}</div>
          </div>
          <div class="ty-fileRow">
            <div class="item_title">表格编号/表格名称</div>
            <div class="item_content">{{item.allFormName}}</div>
          </div>
          <div class="ty-fileRow ty-fileHandle">
            <span class="link-blue" @click="setKnew(item.id)">知道了</span>
            <div class="ty-fileDetail">
              {{item.updateName || item.createName }}
              {{item.createDate || item.updateDate | formatDay}}
            </div>
          </div>
        </div>
        <!--文件位置移动的通知-->
        <div class="ty-fileItem" v-for="(item ,index) in dataList" :key="index" v-if="fileNotice.type === 12">
          <div class="ty-fileRow">
            <div class="item_title">文件编号/名称</div>
            <div class="item_content">{{item.allFileName}}</div>
          </div>
          <div class="ty-fileRow">
            <div class="item_title">原位置</div>
            <div class="item_content">{{item.originalFeature}}</div>
          </div>
          <div class="ty-fileRow">
            <div class="item_title">新位置</div>
            <div class="item_content">{{item.newFeature}}</div>
          </div>
          <div class="ty-fileRow ty-fileHandle">
            <span class="link-blue" @click="setKnew(item.id)">知道了</span>
            <div class="ty-fileDetail">
              {{item.updateName || item.createName }}
              {{item.createDate || item.updateDate | formatDay}}
            </div>
          </div>
        </div>
        <!--文件夹位置移动的通知-->
        <div class="ty-fileItem" v-for="(item ,index) in dataList" :key="index" v-if="fileNotice.type === 13">
          <div class="ty-fileRow">
            <div class="item_title">文件夹名称</div>
            <div class="item_content">{{item.categoryName}}</div>
          </div>
          <div class="ty-fileRow">
            <div class="item_title">原位置</div>
            <div class="item_content">{{item.originalFeature}}</div>
          </div>
          <div class="ty-fileRow">
            <div class="item_title">新位置</div>
            <div class="item_content">{{item.newFeature}}</div>
          </div>
          <div class="ty-fileRow ty-fileHandle">
            <span class="link-blue" @click="setKnew(item.id)">知道了</span>
            <div class="ty-fileDetail">
              {{item.updateName || item.createName }}
              {{item.createDate || item.updateDate | formatDay}}
            </div>
          </div>
        </div>
        <!--文件编号/名称修改的通知-->
        <div class="ty-fileItem" v-for="(item ,index) in dataList" :key="index" v-if="fileNotice.type === 14">
          <div class="ty-fileRow">
            <div class="item_title">原编号/名称</div>
            <div class="item_content">{{item.originalFeature}}</div>
          </div>
          <div class="ty-fileRow">
            <div class="item_title">改后的编号/名称</div>
            <div class="item_content">{{item.newFeature}}</div>
          </div>
          <div class="ty-fileRow ty-fileHandle">
            <span class="link-blue" @click="setKnew(item.id)">知道了</span>
            <div class="ty-fileDetail">
              {{item.updateName || item.createName }}
              {{item.createDate || item.updateDate | formatDay}}
            </div>
          </div>
        </div>
        <!--文件夹名称修改的通知-->
        <div class="ty-fileItem" v-for="(item ,index) in dataList" :key="index" v-if="fileNotice.type === 15">
          <div class="ty-fileRow">
            <div class="item_title">原名称</div>
            <div class="item_content">{{item.originalFeature}}</div>
          </div>
          <div class="ty-fileRow">
            <div class="item_title">改后的名称</div>
            <div class="item_content">{{item.newFeature}}</div>
          </div>
          <div class="ty-fileRow ty-fileHandle">
            <span class="link-blue" @click="setKnew(item.id)">知道了</span>
            <div class="ty-fileDetail">
              {{item.updateName || item.createName }}
              {{item.createDate || item.updateDate | formatDay}}
            </div>
          </div>
        </div>
        <!--表格编号/名称修改的通知-->
        <div class="ty-fileItem" v-for="(item ,index) in dataList" :key="index" v-if="fileNotice.type === 16">
          <div class="ty-fileRow">
            <div class="item_title">原编号/名称</div>
            <div class="item_content">{{item.originalFeature}}</div>
          </div>
          <div class="ty-fileRow">
            <div class="item_title">改后的编号/名称</div>
            <div class="item_content">{{item.newFeature}}</div>
          </div>
          <div class="ty-fileRow ty-fileHandle">
            <span class="link-blue" @click="setKnew(item.id)">知道了</span>
            <div class="ty-fileDetail">
              {{item.updateName || item.createName }}
              {{item.createDate || item.updateDate | formatDay}}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.topTip{
  padding: 8px 15px;
}
.btn-group{
  padding: 4px 0;
  .link-blue{
    margin-left: 16px;
  }
}
.container {
  height: calc(100% - 48px);
}
.list{
  height: calc(100% - 78px);
}
.ty-fileItem{
  background-color: #fff;
  cursor: default;
  position: relative;
  font-size: 13px;
  padding: 8px 12px;
  .ty-fileInfo{ flex: auto }
  .ty-fileName{ width:200px; word-wrap:break-word;  overflow:hidden; text-overflow:ellipsis; white-space: nowrap; flex: none }
  .ty-fileNoVersion{  width:100px; white-space:nowrap; overflow:hidden; text-overflow:ellipsis; font-size:12px; color:#999; flex: auto}
  .ty-fileDetail{  color: #aaa;  font-size: 12px;  font-weight: 300; margin-right: 16px; text-align: right}
  .ty-fileHandle{
    margin-top: 4px;
    .link-blue{
      flex: none;
    }
    .ty-fileDetail{
      flex: auto;
    }
  }
  &.active{
    cursor: pointer;
    &:hover{
      background: #f3f3f3;
    }
  }
}
.ty-fileRow { display: flex }
.item_title{
  width: 120px;
  flex: none;
  color: #999;
}
.item_content{
  flex: auto;
}
</style>

<script>
export default {
  name: 'notice_sheetRelate',
  data () {
    return {
      dataList: [],
      loading: true,
      fileNotice: {
        title: '',
        en: '',
        type: 0,
        tips: '',
        needContactDM: false,
        hasBtn: false
      },
      listenersUid: [],
      code: ''
    }
  },
  created () {
    let name = this.$route.params.name
    this.code = name
    let that = this
    let fileNotice = {}
    console.log(name)
    switch (name) {
      case 'needBatchReceiptFile':
        fileNotice = {
          title: '需批量签收的文件',
          en: 'needBatchReceiptFile',
          type: 1,
          tips: '您获得了一些文件的使用权限，请及时查阅并签收！',
          needContactDM: false,
          hasBtn: false
        }
        break
      case 'newSecuringPowerFile':
        fileNotice = {
          title: '新获得使用权限的文件',
          en: 'newSecuringPowerFile',
          type: 2,
          tips: '您获得了以下文件的使用权限，请及时查阅并签收！',
          needContactDM: false,
          hasBtn: false
        }
        break
      case 'fileVersionChangeNotice':
        fileNotice = {
          title: '文件换版通知',
          en: 'fileVersionChangeNotice',
          type: 3,
          tips: '以下文件已被换版，您需查阅及签收！',
          needContactDM: false,
          hasBtn: false
        }
        break
      case 'fileCancelNotice':
        fileNotice = {
          title: '文件废止通知',
          en: 'fileCancelNotice',
          type: 4,
          tips: '以下文件已被废止，您需知悉，并需签收！',
          needContactDM: false,
          hasBtn: false
        }
        break
      case 'fileReturnNotice':
        fileNotice = {
          title: '废止文件的复用通知',
          en: 'fileReturnNotice',
          type: 5,
          tips: '以下废止的文件已被复用，您需知悉，并需签收！',
          needContactDM: false,
          hasBtn: false
        }
        break
      case 'fileDeadNotice':
        fileNotice = {
          title: '文件停用通知',
          en: 'fileDeadNotice',
          type: 6,
          tips: '以下文件已被停用，您已无法查看，特此通知。',
          needContactDM: true,
          hasBtn: true
        }
        break
      case 'folderDeadNotice':
        fileNotice = {
          title: '文件夹停用通知',
          en: 'folderDeadNotice',
          type: 7,
          tips: '以下文件夹已被停用，您已无法查看，特此通知。',
          needContactDM: true,
          hasBtn: true
        }
        break
      case 'deadFileReturnNotice':
        fileNotice = {
          title: '停止使用文件的复用通知',
          en: 'deadFileReturnNotice',
          type: 8,
          tips: '以下停止使用的文件已复用，您需知悉，并需签收！',
          needContactDM: false,
          hasBtn: false
        }
        break
      case 'revokePowerNotice':
        fileNotice = {
          title: '使用权限被取消的通知',
          en: 'revokePowerNotice',
          type: 9,
          tips: '您已被取消以下文件的使用权限，特此通知。',
          needContactDM: true,
          hasBtn: true
        }
        break
      case 'tableRelationApproval':
        fileNotice = {
          title: '表格关联通知',
          en: 'sheetRelate',
          type: 10,
          tips: '以下文件与表格已关联，特此通知！',
          needContactDM: false,
          hasBtn: true
        }
        break
      case 'tableRelieveRelationApproval':
        fileNotice = {
          title: '表格解除关联通知',
          en: 'sheetRelieveRelate',
          type: 11,
          tips: '以下文件与表格已解除关联，特此通知！',
          needContactDM: false,
          hasBtn: true
        }
        break
      case 'fileLocationMoveNotice':
        fileNotice = {
          title: '文件位置移动的通知',
          en: 'fileLocationMoveNotice',
          type: 12,
          tips: '以下文件已被移动，特此通知！',
          needContactDM: false,
          hasBtn: true
        }
        break
      case 'folderLocationMoveNotice':
        fileNotice = {
          title: '文件夹位置移动的通知',
          en: 'folderLocationMoveNotice',
          type: 13,
          tips: '以下文件夹已被移动，特此通知！',
          needContactDM: false,
          hasBtn: true
        }
        break
      case 'fileNameEditNotice':
        fileNotice = {
          title: '文件编号/名称修改的通知',
          en: 'fileNameEditNotice',
          type: 14,
          tips: '以下文件的编号或名称已被修改，特此通知！',
          needContactDM: false,
          hasBtn: true
        }
        break
      case 'folderNameEditNotice':
        fileNotice = {
          title: '文件夹名称修改的通知',
          en: 'folderNameEditNotice',
          type: 15,
          tips: '以下文件夹的名称已被修改，特此通知！',
          needContactDM: false,
          hasBtn: true
        }
        break
      case 'tableNumberEditNotice':
        fileNotice = {
          title: '表格编号/名称修改的通知',
          en: 'tableNumberEditNotice',
          type: 16,
          tips: '以下表格的编号或名称已被修改，特此通知！',
          needContactDM: false,
          hasBtn: true
        }
        break
    }
    this.fileNotice = fileNotice
    this.loading = true
    this.axios.post('../../../res/listResNoticeByType.do', {type: fileNotice.type})
      .then(function (response) {
        console.log('listResNoticeByType', response)
        let data = response.data.data
        let listResNotice = data.listResNotice
        that.dataList = listResNotice
        that.loading = false
      })
      .catch(function (error) {
        console.log(error)
      })
    this.listenersUid = [
      this.sphdSocket.subscribe(this.code, function (data) {
        let reqData = JSON.parse(data)
        console.log('reqData', reqData)
        let update = reqData.operate
        let newDate = {}
        if (update > 0) { // 增加一条
          newDate = reqData.resNotice
          that.dataList.push(newDate)
        } else if (update < 0) { // 减少一条
          let deletId = reqData.resNotice.id
          that.dataList.forEach(function (item, index) {
            if (deletId === item.id) {
              that.dataList.splice(index, 1)
            }
          })
        }
      }, null, 'user')
    ]
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    willCleanNotice: function () {
      this.$router.push({
        path: `/notice/willCleanNotice/${this.code}`
      })
    },
    setKnew: function (id) {
      let that = this
      let data = {
        type:  this.fileNotice.type
      }
      if (id) {
        data.id = id
      } else {
        console.log(this.dataList.length)
        if (this.dataList.length === 0) {
          that.$kiko_message("暂无通知")
          return
        }
      }
      this.loading = true
      this.axios.post('../../../res/signedResource.do', data)
        .then(function (response) {
          console.log('signedResource', response)
          let data = response.data.data
          let state = data.state
          if (state === 1) {
            that.$kiko_message("设置成功")
            that.loading = false
          } else {
            that.$kiko_message("设置失败")
          }
        })
        .catch(function (error) {
          console.log(error)
        })

    },
    handleFileDetail: function (item) {
      this.$router.push({
        path: `/fileNoticeDetail/${item.resourceHistory}`,
        query: {
          noticeId: item.id,
          type: this.fileNotice.type,
          title: this.fileNotice.title
        }
      })
    },
    handleBatchFile: function (item) {
      this.$router.push({
        path: `/batchFile/${item.id}`
      })
      let batchData = {
        batchUuid: item.batchUuid,
        type: this.fileNotice.type,
        category: item.category
      }
      localStorage.setItem('batch', JSON.stringify(batchData))
    },
    childFolder: function (item) {
      this.$router.push({
        path: `/childFolder/${item.category}`,
        query: {
          title: '文件夹停用通知'
        }
      })
    }
  }
}
</script>
