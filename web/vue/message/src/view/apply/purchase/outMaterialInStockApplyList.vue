<template>
  <div id="outMaterialInStockApply">
    <TY_NavTop title="外购物料入库申请" isSearch="false"></TY_NavTop>
    <div class="origin" v-show="tabValue === '1'">
      <div>以下待检验的物料购于{{query.name}}</div>
      <div>采  购：{{query.create_name}} {{query.create_date | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
    </div>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待检验" name="1" closable='false'>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id, 1)" v-for="(item, index) in applyHandleCheck" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.model}}</div>
                <div>{{item.name}}</div>
                <div>申请入库的数量  {{item.quantity_plan}}{{item.unit}}  到货日期 {{item.arrive_date | formatDay('YYYY-MM-DD')}}</div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="检验通过" name="2" closable='false'>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id, 2)" v-for="(item, index) in applyQualified" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.code}}</div>
                <div>{{item.name}}</div>
                <div>申请入库的数量  {{item.quantity_plan}}{{item.unit}}  到货日期 {{item.arrive_date | formatDay('YYYY-MM-DD')}}</div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="检验不合格" name="3" closable='false'>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id, 3)" v-for="(item, index) in applyUnqualified" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.code}}</div>
                <div>{{item.name}}</div>
                <div>申请入库的数量  {{item.quantity_plan}}{{item.unit}}  到货日期 {{item.arrive_date | formatDay('YYYY-MM-DD')}}</div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>

<style>
  #outMaterialInStockApply .tabs-tab{
    width: 33.3%;
  }
  #outMaterialInStockApply .ui-cell{
    font-size: 12px;
  }
  .origin{
    background: #34a5ae;
    color: #fff;
    padding: 4px 12px;
  }
</style>

<script>
export default {
  name: 'outMaterialInStockApply',
  data () {
    return {
      tabValue: '1',
      applyHandleCheck: [],
      applyQualified: [],
      applyUnqualified: [],
      listenersUid: [],
      query: {}
    }
  },
  computed: {},
  created () {
    this.tabValue = this.$route.params.tabValue || '1'
    let applyId = this.$route.params.id
    let query = JSON.parse(localStorage.getItem('applyItem'))
    this.query = query
    console.log('applyId', applyId)
    console.log('query', query)
    let that = this
    this.axios.post('../../../inStock/mtList', {
      oid: that.sphdSocket.user.oid,
      state: '(2)',
      users: 'zb',
      id: applyId
    }).then(function (response) {
      let list = response.data.data
      that.applyHandleCheck = list
    })
    this.axios.post('../../../inStock/mtList', {
      oid: that.sphdSocket.user.oid,
      state: '(3)',
      users: 'zb',
      id: applyId
    }).then(function (response) {
      let list = response.data.data
      that.applyQualified = list
    })
    this.axios.post('../../../inStock/mtList', {
      oid: that.sphdSocket.user.oid,
      state: '(4)',
      users: 'zb',
      id: applyId
    }).then(function (response) {
      let list = response.data.data
      that.applyUnqualified = list
    })
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    jump: function (id, tab) {
      this.$store.dispatch('setMaterialParam', {
        user: 'zbc',
        tab: tab
      })
      this.$router.push({
        path: `/materialDetail/${id}`
      })
    }
  }
}
</script>
