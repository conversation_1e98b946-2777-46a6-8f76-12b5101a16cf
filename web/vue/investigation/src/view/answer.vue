<template>
  <div class="answer" id="answer">
    <!--<h4 class="title2">企业需求调查表</h4>-->
    <div class="todayTime">
      <span style="display:inline-block; width:40px; height:20px; ">
              <el-button circle v-if="showList" icon="el-icon-arrow-left" @click="showList = !showList"></el-button>
      </span>
      {{ todayTime }}
    </div>

    <div v-show="!showList">
      <div class="main_container">
        <p @click="jump">问卷所属机构：<span v-html="orgName"></span> <span class="right">></span> </p>
        <p>
          您提交的时间：{{ $moment(answerTime).format('YYYY-MM-DD HH:mm:ss') }}
        </p>
        <p>
          问卷可修改的截止时间：提交后{{ modifyLimit }}分钟内
        </p>

      </div>
      <div class="main_container" @click="getList" >
        其他数据  <span class="linkBtn riBtn" >></span>
      </div>

      <div class="btmTip">
        <p>重要说明</p>
        <div>
          <p>注册后，登录Wonderss系统小程序或其他端口，均可稳定地访问已提交数据，并可使用更多功能。注册免费。</p>
          <p>
            未注册的只可通过点击本分享，且只有使用原设备、通过原途径、在未清缓存条件下，才能找到已有数据。
          </p>
        </div>
        <div class="flexDV">
          <div class="linkBtn" >免费注册</div>
          <div class="linkBtn" >登录系统</div>
        </div>


      </div>

    </div>

    <div class="main_container" v-show="showList" >
      <el-table :data="list" stripe style="width:100%" >
        <el-table-column prop="date" label="提交时间" ></el-table-column>
        <el-table-column prop="name" label="操作" >
          <template slot-scope="scope"><span class="linkBtn" @click="jump(scope.row)">查看</span></template>
        </el-table-column>
      </el-table>
    </div>


  </div>
</template>

<style>
  body{ background:#eee;  }
  .el-table .cell {  text-align: center!important;  }
  #answer{ background:#eee; width:100%; box-sizing: border-box; height: 100%;   }
  .answer{ font-size:15px; color: #333; }
  #answer .el-button.is-circle{ padding:8px; }
  .title2{ color: #fff; line-height: 50px; text-align: center; background: #31BFB0;    }
  #answer .main_container{ padding: 15px;  background:#fff; margin-top:15px;  }
  .todayTime{  line-height:40px; height:40px; padding:0 8px; background:#fff;  }
  .riBtn, .right{ float: right;  }
  .linkBtn{ color: #7282ff; display: inline-block;font-weight:bold;  }
  .linkBtn:hover{ font-weight:bolder; cursor:default;   }
  .tip2{ color: #7282ff;  font-size:0.9em; }
  .flexDV{ display: flex; }
  .flexDV>div{ text-align: center;  }

  .btmTip{
    padding:20px;
    line-height: 25px;
    margin-top: 100px;
  }
  .flexDV{ display: flex; }
  .flexDV>div{ flex:1; text-align: center }

</style>

<script>
  export default {
  name: 'home',
  data () {
    return {
      showList: false,
      list: [],
      todayTime: '',
      answerTime: '',
      orgName: '',
      modifyLimit: '',
      hashKey: '',
      params: '',
      publishId: '',
    }
  },
  created () {
    this.params = this.$route.params
    console.log(this.$route.params)
    this.answerTime = this.$route.params.answerTime
    // orgName, modifyLimit,
    let ansData = localStorage.getItem('ansdata')
    if(ansData){
      ansData = JSON.parse(ansData)
    }
    let idByUrl = this.params.id
    let ansList = localStorage.getItem('ansList')
    if(ansList){
      ansList = JSON.parse(ansList)
    }else{
      ansList = []
    }
    ansList.forEach(ansItem => {
      if(ansItem.id === idByUrl){
        console.log('找到相同的id了')
        this.answerTime = ansItem.answerTime
      }
    })
    console.log('读取的ansData=', ansData)
    this.orgName = ansData.orgname
    this.modifyLimit = ansData.modifyLimit
    this.publishId = ansData.publishId
    this.hashKey = this.$route.params.hashKey
    let serverTime = this.params.serverTime
    this.todayTime = `今天是${this.$moment(serverTime).format('YYYY年MM月DD日')} ${this.$moment(serverTime).format('dddd')}` ;
    // wyu：打印base传过来的参数
    console.log(window.parent.$.webRoot)
    if(this.params.isHistory){
      this.getList()
    }
  },
  methods: {
    jump () {

      let params = this.params
      params.goback = true
      params.isHistory = false
      params.hisID = false
      // params.isHistory = true
      params.hisID = this.publishId
      params.hashKey = this.hashKey

      this.$router.push({
        name: 'home',
        params: params
      })
    },
    getList () {
      let _this = this
      let data = {
        'id': this.params.objective, 'hashKey': _this.hashKey
      }
      console.log('更多数据：', data)
      if(data.id && data.hashKey){
        _this.$toast.fail('暂无其他数据可查询！')
        return false
      }
      this.$toast.loading({
        message: '正在查询...',
        forbidClick: true,
        loadingType: 'spinner'
      })
      _this.$http.post('../../../investigationPublish/investigateObjectOtherList.do',data, {
        emulateJSON: true
      }).then((response) => {
        _this.$toast.clear()
        console.log(response.body)
        let res = response.body
        _this.list = res || res.RespInvestigateObject || []
        _this.showList = true
        _this.list.forEach(function (item) {
          item.date =  _this.$moment(item.createDate).format('YYYY-MM-DD HH:mm:ss')
        })
      }).catch(function (err) {
        console.log('跳转错误')
        console.log(err)
        _this.loading = false
        _this.$toast.fail('系统错误，请重试！')
      })
    }
  }
}
</script>
