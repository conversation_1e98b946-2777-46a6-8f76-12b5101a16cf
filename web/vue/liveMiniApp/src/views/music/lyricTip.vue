<template>
  <div class="lyricTip">
    <Pageheader title="提示" :showBack="true" :showClose="true" :runMyBackFun="true" @mycloseFun="cancelAdd" @myBackFun="goback"/>

    <div class="">
      <div class="live-container">
        <el-form label-position="top" :model="formData">
          <div v-if="!alreadyJi">
            <el-form-item label="字幕精灵申请获取您剪切板的内容">
              <el-radio-group v-model="formData.agreeOrNot" size="large">
                <el-radio label="1">同意</el-radio>
                <el-radio label="0">不同意</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item>
              <el-checkbox v-model="formData.checked1" label="保存此项的选择结果，不再询问"></el-checkbox>
            </el-form-item>
          </div>
          <div style="height: 50px;"></div>
          <div v-if="twoDuration && lyricInfoUse.banLyc">
            伴奏时长：{{ lyricInfoUse.banLyc.durationTime2 }}，原唱时长：{{ lyricInfoUse.orlLyc.durationTime2 }}
          </div>
          <el-form-item label="配节奏，使用原唱还是伴奏？" v-if="chooseLyc">
            <el-radio-group v-model="formData.rhythmOption" size="large">
              <el-radio label="1">原唱</el-radio>
              <el-radio label="2">伴奏</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <span @click="goNext()" class="primary live-button">下一步</span>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from "vue-router";
import { ElMessage } from 'element-plus'
import JSONBig from 'json-bigint'
import auth from '@/sys/auth'

const router = useRouter();
let alreadyJi = ref(false)
let chooseLyc = ref(false)
let twoDuration = ref(false)

let lyricInfoUse = reactive({})
const formData = reactive({
  checked1:'',
  agreeOrNot:'',
  rhythmOption:''
})


onMounted(() => {
  let agreeObj = JSONBig.parse(localStorage.getItem('agreeObj') || '{}') ;
  if(agreeObj && agreeObj.checked1){ // 以前记住了
    alreadyJi.value = true
    formData.agreeOrNot = agreeObj.agreeOrNot
    formData.checked1 = agreeObj.checked1
  }

  let lyricInfo = JSONBig.parse(localStorage.getItem('newlycdata'))
  console.log('lyricInfo=', lyricInfo)
  lyricInfoUse = lyricInfo

  if(lyricInfo.orlLyc.filePath && lyricInfo.banLyc.filePath){ // 同时有原唱伴唱时  需要选择
    let userInfo = auth.getUser()
    console.log('userInfo = ', userInfo )
    if(userInfo.roleCode == 'staff'){
    }else{ // 主播才选择，
      chooseLyc.value = true
    }
    if(chooseLyc.value){
      if(lyricInfo.orlLyc.duration !=  lyricInfo.banLyc.duration){// 时长 不同的 给予提示
        twoDuration.value = true
        console.log('lyricInfoUse=', lyricInfoUse)
      }
    }

  }else{
    if(lyricInfo.orlLyc.filePath){
      formData.rhythmOption = 1 // 节奏选项:1-原唱,2-伴奏
    }else if(lyricInfo.banLyc.filePath){
      formData.rhythmOption = 2 // 节奏选项:1-原唱,2-伴奏
    }
  }
  console.log('chooseLyc.value = ', chooseLyc.value)
  console.log('twoDuration.value = ', twoDuration.value)
  console.log('alreadyJi.value = ', alreadyJi.value)

  if(((alreadyJi.value)) && (!(chooseLyc.value)) && (!(twoDuration.value))){
    goNext()
  }

});
// 取消新增
const cancelAdd = () => {
  console.log('cancelAdd')
  let lyricInfo = JSONBig.parse(localStorage.getItem('newlycdata'))
  localStorage.removeItem('newlycdata')
  localStorage.removeItem('patData')
  localStorage.removeItem('agreeObj')
  localStorage.removeItem('lycTxtObj')
  localStorage.removeItem('assignTasks')
  localStorage.removeItem('staffList')
  console.log('lyricInfo.from ===', lyricInfo.from)
  if(lyricInfo.from === 'lycTaskHandel'){
    router.push({ name: 'lycTaskHandel' });
  }else{
    router.push({ name: 'lyricTask', });
  }
}
const goback = () => {
  let lyricInfo = JSONBig.parse(localStorage.getItem('newlycdata'))
  router.push({
    name: lyricInfo.from,
  });
}
const goNext = () => {
  console.log('agreeOrNot=', formData.agreeOrNot)
  console.log('checked1=', formData.checked1)

  if((alreadyJi.value && (chooseLyc.value && formData.rhythmOption === ''))
      || (!alreadyJi.value && (formData.agreeOrNot === '' || formData.checked1 === '' || (chooseLyc.value && formData.rhythmOption === '')))){
    ElMessage.warning({
      message: '请将选项补充完整！',
      type: 'warning',
    })
    return false
  }
  localStorage.setItem('agreeObj', JSONBig.stringify({ 'agreeOrNot': formData.agreeOrNot , 'checked1': formData.checked1 }))
  localStorage.setItem('lycTxtObj', JSONBig.stringify({ 'rhythmOption': formData.rhythmOption }))
  localStorage.setItem('tipPage', 'gequ')
  router.push({
    name: 'lyricTxtPat',
    params: {
    },
  });
}
</script>
<style lang="scss">

</style>
