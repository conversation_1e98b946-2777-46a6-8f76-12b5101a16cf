package cn.sphd.miners.modules.generalAffairs.service.impl;

import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.socket.service.DelayCallback;
import org.springframework.context.ApplicationContext;

import java.util.Date;

/**
 * Created by Administrator on 2018/10/23.
 */
public class OverPunchRecord implements DelayCallback{
    private Integer userId;
    private Date punchTime;
    private Byte punchType;
    private Byte detailPunchType;
    private Integer detailId;
    private Integer overtimeId;
    private Byte type;
//    private Integer personnelAttendanceUserId;

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Date getPunchTime() {
        return punchTime;
    }

    public void setPunchTime(Date punchTime) {
        this.punchTime = punchTime;
    }

    public Byte getPunchType() {
        return punchType;
    }

    public void setPunchType(Byte punchType) {
        this.punchType = punchType;
    }

    public Byte getDetailPunchType() {
        return detailPunchType;
    }

    public void setDetailPunchType(Byte detailPunchType) {
        this.detailPunchType = detailPunchType;
    }

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    public Integer getOvertimeId() {
        return overtimeId;
    }

    public void setOvertimeId(Integer overtimeId) {
        this.overtimeId = overtimeId;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public OverPunchRecord(Integer userId, Date punchTime, Byte punchType, Byte detailPunchType, Integer detailId, Integer overtimeId, Byte type) {
        this.userId = userId;
        this.punchTime = punchTime;
        this.punchType = punchType;
        this.detailPunchType = detailPunchType;
        this.detailId = detailId;
        this.overtimeId = overtimeId;
        this.type = type;
    }

    @Override
    public void delayCall(ClusterMessageSendingOperations clusterMessageSendingOperations, ApplicationContext ac) {
        WorkAttendanceService workAttendanceService = ac.getBean(WorkAttendanceService.class);
        workAttendanceService.overPunchTurn(userId,punchTime,punchType,detailPunchType,detailId,overtimeId,type);
    }
}
