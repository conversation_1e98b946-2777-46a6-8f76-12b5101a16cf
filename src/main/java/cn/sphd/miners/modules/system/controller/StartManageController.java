package cn.sphd.miners.modules.system.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.GetLocalIPUtils;
import cn.sphd.miners.modules.accountant.service.BuildAccountService;
import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.finance.entity.FinanceAccount;
import cn.sphd.miners.modules.finance.entity.FinanceInvoiceSetting;
import cn.sphd.miners.modules.finance.service.FinanceAccountService;
import cn.sphd.miners.modules.finance.service.InvoiceService;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceService;
import cn.sphd.miners.modules.resourceAuthority.service.ResService;
import cn.sphd.miners.modules.sms.service.SendMessageService;
import cn.sphd.miners.modules.sms.service.SmsService;
import cn.sphd.miners.modules.sms.service.TemplateService;
import cn.sphd.miners.modules.system.entity.OrgPopedomItem;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgPopedomItemService;
import cn.sphd.miners.modules.system.service.OrgService;
import cn.sphd.miners.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2020/7/8.
 * 启用管理（lixu）
 */
@Controller
@RequestMapping("/startManage")
public class StartManageController {
    @Autowired
    OrgPopedomItemService orgPopedomItemService;
    @Autowired
    OrgService orgService;
    @Autowired
    FinanceAccountService financeAccountService;
    @Autowired
    InvoiceService invoiceService;
    @Autowired
    BuildAccountService buildAccountService;
    @Autowired
    UserService userService;
    @Autowired
    SendMessageService sendMessageService;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
//    @Autowired
//    SalaryAction salaryAction;
    @Autowired
    WorkAttendanceService workAttendanceService;
    @Autowired
    ResService resService;
    @Autowired
    SmsService smsService;

    @RequestMapping("/toStartManages.do")
    public String toStartManages(){
        return "/openManage/openManage";
    }

    @ResponseBody
    @RequestMapping("/getStartManages.do")
    public JsonResult getStartManages(User user) {
        Integer oid=user.getOid();
        List<OrgPopedomItem> orgPopedomItemList=orgPopedomItemService.getOrgPopedomItemsByOid(oid); // 项目列表
        Organization o = user.getOrganization();

        if (orgPopedomItemList.size()==0) {
            orgPopedomItemService.initialOrgPopedomItems(o);//初始化项目列表
        }
        orgPopedomItemList=orgPopedomItemService.getOrgPopedomItemsByOid(oid); // 项目列表
        User generalUser=userService.getUserByRoleCode(oid,"general");
        User financeUser=userService.getUserByRoleCode(oid,"finance");
        User accountingUser=userService.getUserByRoleCode(oid,"accounting");
        User superUser=userService.getUserByRoleCode(oid,"super");
//        User superUser=userService.getUserByRoleCode(oid,"smallSuper");
//        if (superUser==null) {
//            superUser = userService.getUserByRoleCode(oid, "super");
//        }
        if (accountingUser == null) {
            accountingUser = userService.getUserByRoleCode(oid, "agentAccounting");
        }
        for (OrgPopedomItem orgPopedomItem : orgPopedomItemList) {
            switch (orgPopedomItem.getCode()) {
                case "staffImport": //职工档案
                    if (o.getInitState().equals("1")) {
                        orgPopedomItem.setState("1"); // 0-未设置 1-已设置
                    }
                    if (generalUser != null) {
                        orgPopedomItem.setUid(generalUser.getUserID());
                        orgPopedomItem.setUserName(generalUser.getUserName());
                    }
                    break;
                case "accountEntry": //账户管理
                    List<FinanceAccount> financeAccounts = financeAccountService.getAllAccounts(oid,null, 1, 2);   //正常启用的银行账户 1-现金，2-银行,3-其他
                    if (financeAccounts.size() > 0) {
                        orgPopedomItem.setState("1"); // 0-未设置 1-已设置
                    }
                    if (financeUser != null) {
                        orgPopedomItem.setUid(financeUser.getUserID());
                        orgPopedomItem.setUserName(financeUser.getUserName());
                    }
                    break;
                case "invoiceInstall": //发票设置
                    List<FinanceInvoiceSetting> financeInvoiceSettings = invoiceService.getFinanceInvoiceSettingByOid(oid);
                    if (financeInvoiceSettings.size() > 0) {
                        orgPopedomItem.setState("1"); // 0-未设置 1-已设置
                    }
                    if (financeUser != null) {
                        orgPopedomItem.setUid(financeUser.getUserID());
                        orgPopedomItem.setUserName(financeUser.getUserName());
                    }
                    break;
                case "accountingInstall": //会计科目管理
                    String res = buildAccountService.getBuildAccountState(oid); //3是科目设置完成  4是建帐成功
                    if ("4".equals(res)) {
                        orgPopedomItem.setState("1"); // 0-未设置 1-已设置
                    }
                    if (accountingUser != null) {
                        orgPopedomItem.setUid(accountingUser.getUserID());
                        orgPopedomItem.setUserName(accountingUser.getUserName());
                    }
                    break;
                case "salaryInstall": //薪资宝-系统设置
//                    if (salaryAction.isDisplayRegTime(o.getSuperId())) { //返回true 符合情况，机构已经设置了收益率   返回false 机构没设置收益率
//                        orgPopedomItem.setState("1"); // 0-未设置 1-已设置
//                    }
                    if (superUser != null) {
                        orgPopedomItem.setUid(superUser.getUserID());
                        orgPopedomItem.setUserName(superUser.getUserName());
                    }
                    break;
                case "fileManageInstall": //文件与资料-文件夹管理
                    Integer result = resService.checkResAuth(oid);//返回值 0-没有进行设置权限 1-已经设置权限
                    if (result == 1) {
                        orgPopedomItem.setState("1"); // 0-未设置 1-已设置
                    }
                    if (generalUser != null) {
                        orgPopedomItem.setUid(generalUser.getUserID());
                        orgPopedomItem.setUserName(generalUser.getUserName());
                    }
                    break;
                case "attendanceInstall": //总务管理-考勤管理
                    Date startUsingSystemTime = workAttendanceService.getStartUsingSystemTime(oid);  //获取系统考勤启用时间  值不等于null,则已设置考勤；等于null,则没有设置考勤
                    if (startUsingSystemTime != null) {
                        orgPopedomItem.setState("1"); // 0-未设置 1-已设置
                    }
                    if (generalUser != null) {
                        orgPopedomItem.setUid(generalUser.getUserID());
                        orgPopedomItem.setUserName(generalUser.getUserName());
                    }
                    break;
                case "vehicleWork": //手机端工作-车务管理
                    User vehicle = userService.getUserByCoreCode(oid, "vehicleCore");//汽车核心人物
                    if (vehicle != null) {
                        orgPopedomItem.setState("1"); // 0-未设置 1-已设置
                    }
                    if (generalUser != null) {
                        orgPopedomItem.setUid(generalUser.getUserID());
                        orgPopedomItem.setUserName(generalUser.getUserName());
                    }
                    break;
                case "complaintWork": //投诉管理
                    User tousu = userService.getUserByCoreCode(oid, "core");
                    if (tousu != null) {
                        orgPopedomItem.setState("1"); // 0-未设置 1-已设置
                    }
                    if (generalUser != null) {
                        orgPopedomItem.setUid(generalUser.getUserID());
                        orgPopedomItem.setUserName(generalUser.getUserName());
                    }
                    break;
                case "projectWork": //项目管理-综合管理
                    User project = userService.getUserByCoreCode(oid, "projectCore");
                    if (project != null) {
                        orgPopedomItem.setState("1"); // 0-未设置 1-已设置
                    }
                    if (generalUser != null) {
                        orgPopedomItem.setUid(generalUser.getUserID());
                        orgPopedomItem.setUserName(generalUser.getUserName());
                    }
                    break;
                case "improvementWork": //持续改进-综合管理
                    User improvement = userService.getUserByCoreCode(oid, "improvementCore");
                    if (improvement != null) {
                        orgPopedomItem.setState("1"); // 0-未设置 1-已设置
                    }
                    if (generalUser != null) {
                        orgPopedomItem.setUid(generalUser.getUserID());
                        orgPopedomItem.setUserName(generalUser.getUserName());
                    }
                    break;
            }
            orgPopedomItemService.updateOrgPopedomItem(orgPopedomItem);

        }
        return new JsonResult(1, orgPopedomItemList);

    }

    /**
     * <AUTHOR>
     * @Date 2020/7/13 11:03
     *发送系统消息 给负责人
     *
     * messageType 1- 系统消息  2-手机短信
     */
    @ResponseBody
    @RequestMapping("/sendMessageToCoreUser.do")
    public JsonResult sendMessageToCoreUser(User user, Integer itemId, Integer messageType, HttpServletRequest request, AuthInfoDto authInfo) {
        String cont="参数传的不对";
        Integer state=0;
        if (itemId!=null) {
            User loginUser = userService.getUserByID(user.getUserID());
            OrgPopedomItem item = orgPopedomItemService.getOrgPopedomItemById(itemId);
            if(loginUser.getOrganization().getInitState().equals("0")&&!item.getCode().equals("staffImport")) {
                state=4;
                cont="您的提示无法发出，因为职工导入尚未完成！";
            }else {
                if (item.getUid() == null) {
                    state = 2;
                    cont = "您的提示无法发出，因为该项暂时还没有负责人";
                } else {
                    if (item.getState().equals("1")) {//已设置
                        state = 3;
                        cont = "该项已设置完毕，无需再发出提示";
                    } else {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                        Integer coreUserId = item.getUid();
                        User coreUser = userService.getUserByID(coreUserId);
                        String messageCont = "为使wonderss系统早日正常使用，请尽快完成" + item.getName() + "！" + loginUser.getUserName();
                        switch (messageType) {
                            case 1: //发系统消息
                                userSuspendMsgService.saveUserSuspendMsg(1, messageCont, messageCont, sdf.format(new Date()), coreUserId, "", itemId);//推送我的消息
                                break;
                            case 2: //发手机短信
//                                sendMessageService.sendMessage(coreUserId, coreUser.getOid(), coreUser.getMobile(), messageCont);
                                // 短信重构 2024/5/22 李旭更改
                                String phoneNo=coreUser.getMobile();
                                String url = GetLocalIPUtils.getRootPath(request);
                                Map<String, String> params = new HashMap<String, String>(2) {{
                                    put("itemName", item.getName());
                                    put("userName", loginUser.getUserName());
                                }};
                                smsService.sendMessage(SmsService.SendType.role, phoneNo, authInfo, TemplateService.SmsContentTemplate.startManage, params, null);

                                break;
                        }
                        state = 1;
                        cont = "您的提示已发出！";
                    }
                }
            }

        }
        Map<String,Object> map=new HashMap<>();
        map.put("state",state);
        map.put("cont",cont);
        return new JsonResult(1,map);
    }

}