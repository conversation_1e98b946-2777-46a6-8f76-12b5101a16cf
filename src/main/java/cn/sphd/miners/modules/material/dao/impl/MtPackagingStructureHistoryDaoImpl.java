package cn.sphd.miners.modules.material.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.material.dao.MtPackagingStructureHistoryDao;
import cn.sphd.miners.modules.material.entity.MtPackagingStructure;
import cn.sphd.miners.modules.material.entity.MtPackagingStructureHistory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class MtPackagingStructureHistoryDaoImpl extends BaseDao<MtPackagingStructureHistory, Serializable> implements MtPackagingStructureHistoryDao {
    @Override
    public MtPackagingStructureHistory insert(MtPackagingStructure pdPackagingStructure, Long infoHistoryId) {
        MtPackagingStructureHistory history=new MtPackagingStructureHistory();

        BeanUtils.copyProperties(pdPackagingStructure,history);
        history.setPackaging(infoHistoryId);
        history.setPackagingStruct(pdPackagingStructure.getId());
        history.setId(null);
        save(history);
        return history;
    }
}
