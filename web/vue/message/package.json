{"name": "message", "version": "1.0.0", "description": "A Vue.js project", "author": "sib<PERSON><PERSON> <<EMAIL>>", "private": true, "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "serve": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "unit": "cross-env BABEL_ENV=test karma start test/unit/karma.conf.js --single-run", "test": "npm run unit", "lint": "eslint --ext .js,.vue src test/unit", "build": "node build/build.js"}, "dependencies": {"axios": "^0.18.0", "element-ui": "^2.12.0", "less": "^3.8.1", "less-loader": "^4.1.0", "moment": "^2.22.2", "pyfl": "^1.1.4", "vue": "2.6.14", "vue-router": "^3.5.2", "vuex": "^3.1.1"}, "devDependencies": {"autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-eslint": "^8.2.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.1", "babel-plugin-istanbul": "^4.1.1", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "chai": "^4.1.2", "chalk": "^2.0.1", "copy-webpack-plugin": "^4.0.1", "cross-env": "^5.0.1", "css-loader": "^0.28.0", "dotenv": "^10.0.0", "dotenv-expand": "^5.1.0", "eslint": "^4.19.1", "eslint-config-airbnb-base": "^11.3.0", "eslint-config-standard": "^12.0.0", "eslint-friendly-formatter": "^3.0.0", "eslint-import-resolver-webpack": "^0.8.3", "eslint-loader": "^1.7.1", "eslint-plugin-html": "^4.0.5", "eslint-plugin-import": "^2.14.0", "eslint-plugin-node": "^7.0.1", "eslint-plugin-promise": "^4.0.0", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^4.0.0", "extract-text-webpack-plugin": "^3.0.2", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^2.30.1", "inject-loader": "^3.0.0", "js-base64": "^3.6.0", "js-cookie": "^3.0.1", "karma": "^1.4.1", "karma-coverage": "^1.1.1", "karma-mocha": "^1.3.0", "karma-phantomjs-launcher": "^1.0.2", "karma-phantomjs-shim": "^1.4.0", "karma-sinon-chai": "^1.3.1", "karma-sourcemap-loader": "^0.3.7", "karma-spec-reporter": "0.0.31", "karma-webpack": "^2.0.2", "less": "^3.8.1", "less-loader": "^4.1.0", "mocha": "^3.2.0", "node-notifier": "^5.1.2", "node-sass": "^6.0.1", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "phantomjs-prebuilt": "^2.1.14", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "rimraf": "^2.6.0", "semver": "^5.3.0", "shelljs": "^0.7.6", "sinon": "^4.0.0", "sinon-chai": "^2.8.0", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vue-loader": "^13.3.0", "vue-resource": "^1.5.1", "vue-style-loader": "^3.0.1", "vue-template-compiler": "2.6.14", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-dev-server": "^2.9.1", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}