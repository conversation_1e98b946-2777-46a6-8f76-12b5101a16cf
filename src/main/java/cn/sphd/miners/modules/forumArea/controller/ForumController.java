package cn.sphd.miners.modules.forumArea.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.forumArea.entity.*;
import cn.sphd.miners.modules.forumArea.service.ForumService;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.service.UserMessageService;
import cn.sphd.miners.modules.resourceAuthority.entity.ResEntity;
import cn.sphd.miners.modules.resourceAuthority.service.ResService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.dto.UserDto;
import cn.sphd.miners.modules.system.dto.UserHonePageDto;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgService;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSON;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigInteger;
import java.util.*;

/**
 * Created by 朱思旭 on 2018/2/2.
 */
@Controller
@RequestMapping("/forum")
public class ForumController {

    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    ForumService forumService;
    @Autowired
    UserService userService;
    @Autowired
    UserMessageService userMessageService;
    @Autowired
    OrgService orgService;
    @Autowired
    SWMessageService swMessageService;
    @Autowired
    ResService resService;

    //新增讨论
    @ResponseBody
    @RequestMapping("/insertOneForum.do")
    public JsonResult insertOneForum(User user, ForumPost forumPost, String type, Integer auditor, String auditorName){
       /* type="2";
        auditor = 7016;
        auditorName = "一页书";*/
        List<ForumPostUser> listPostUser = JSON.parseArray(forumPost.getListForumPostUser(), ForumPostUser.class);
        if (forumPost.getCategory() == null) {
            forumPost.setParticipantsNum(listPostUser.size());
            List<ForumPostAttachment> listAtt = JSON.parseArray(forumPost.getListForumPostAttachment(), ForumPostAttachment.class);
            forumService.insertForumStatus(forumPost, user);
            for (ForumPostAttachment fpa : listAtt) {
                //    fpa.setPlace("1");
                //     fpa.setModule("事务讨论");
                fpa.setLocation("1");
                fpa.setPost(forumPost.getId());
                forumService.insertForumattachment(fpa,user);
            }
            //上传人自己就是参与人所以要先把自己新增进去同时参与人数加1
            ForumPostUser postUserByMine  = new ForumPostUser();
            postUserByMine.setPost(forumPost.getId());
            postUserByMine.setUserID(user.getUserID());
            postUserByMine.setUserName(user.getUserName());
            forumService.insertFotrumUser(postUserByMine);
            forumService.upPostStatus(forumPost, null, null, null, null, forumPost.getParticipantsNum()+1, forumPost.getCreateDate());
            //遍历传过来的人员，若其中有上传人就把此人去掉并让参与人数减1
            //forumService.upForumPostParticipantsNum(forumPost);
            for (ForumPostUser fpu : listPostUser) {
                if(fpu.getUserID().equals(user.getUserID())){
                    forumService.upPostStatus(forumPost, null, null, null, null, forumPost.getParticipantsNum()-1, forumPost.getCreateDate());
                }else {
                    fpu.setPost(forumPost.getId());
                    forumService.insertFotrumUser(fpu);
                }
            }
            HashMap<String, Object> map = new HashMap<>();
            map.put("forumPost", forumPost);
            String noticeMessage = user.getUserName()+"在"+ NewDateUtils.dateToString(forumPost.getCreateDate(),"yyyy-MM-dd HH:mm:ss")+"提交了讨论发起申请";
            swMessageService.rejectSend(0,1,map,forumPost.getCreator().toString(),"/applyForumPost",null,null,user,"forumApply");    //给讨论申请人发
            if("1".equals(type)){
                forumService.addApprovalProcessForForum(forumPost.getId(), user, 1, null, null, forumPost.getCreateDate(), "AuthApproval");
                swMessageService.rejectSendToMid(1,1,map,user.getOid(),"AuthApproval","/approvalForumPost","forumApproval", "有一条申请待审批",noticeMessage);   //发给终审人员
            } else {
                forumService.addApprovalProcessForForum(forumPost.getId(), user, 1, auditor, auditorName, forumPost.getCreateDate(),"");
                User userAuditor = userService.getUserByID(auditor);
                swMessageService.rejectSend(1,1,map,auditor.toString(),"/forumLaunchApproval", "有一条申请待审批", noticeMessage, userAuditor,"forumLaunchApproval");   //给z中间人"讨论发起申请"待审批
            }
        }else {
            forumPost.setParticipantsNum(listPostUser.size()+1);
            forumService.twoUserForumPost(user.getOid(), listPostUser, user, forumPost);
        }
        return new JsonResult(1,"新增成功");
    }



    //获取主页右侧讨论区的角标数
    @ResponseBody
    @RequestMapping("/getFormPostNumforSuperscript.do")
    public JsonResult getFormPostNumforSuperscript(User user){
        Integer forumNum = user.getForumCount();
        HashMap<String, Object> map = new HashMap<>();
        map.put("forumNum",forumNum);
        return new JsonResult(1, map);
    }

    //申请者获取讨论
    @ResponseBody
    @RequestMapping("/getApplyForumPost.do")
    public JsonResult getApplyForumPost(User user){
        List<ForumPost> list = forumService.getApplyForum(user.getUserID());
        HashMap<String, Object> map = new HashMap<>();
        map.put("listApplyForum",list);
        return new JsonResult(1, map);
    }

    //中间人获取讨论
    @ResponseBody
    @RequestMapping("/getApproveForum.do")
    public JsonResult getApproveForum(User user){
        HashMap<String, Object> map = forumService.getApproveForum(user.getUserID());
        return new JsonResult(1, map);
    }

    //终审人获取讨论
    @ResponseBody
    @RequestMapping("/getLastApproveForum.do")
    public JsonResult getLastApproveForum(User user){
        List<ForumPost> list = forumService.getLastForum(user.getOid());
        HashMap<String, Object> map = new HashMap<>();
        map.put("listApproveForum",list);
        return new JsonResult(1, map);
    }

    //主题审批中查看讨论
    @ResponseBody
    @RequestMapping("/getForumPostMessage.do")
    public JsonResult getForumPostMessage(Integer id) throws IOException{
        ForumPost forumPost = forumService.forumPostMessageByhead(id);
        List<ApprovalProcess> listAp = forumService.getlistApprovalProcess(id,43);
        HashMap<String, Object> map = new HashMap<>();
        map.put("forumPost", forumPost);
        List<ForumPostAttachment> listPostAtt = forumService.listPostAtt(id);
        List<ForumPostUser> listPostUser = forumService.listPostUser(id);
        if (!listPostUser.isEmpty()) {
            for (ForumPostUser f : listPostUser) {
                UserHonePageDto userDto = userService.getUserHonePageDtoByUserId(f.getUserID());
                f.setImgPath(userDto.getImgPath());
            }
        }
        map.put("forumPostAttachmentHashSer", listPostAtt);
        map.put("forumPostUserHashSer",listPostUser);
        map.put("listAp", listAp);
        return new JsonResult(1, map);
    }

    //讨论区小窗查询功能
    @ResponseBody
    @RequestMapping("/findForumPost.do")
    public JsonResult findForumPost(User user, String eventType, String type, String timeBegin, String timeEnd, String approveStatus, Integer fromUser){
        HashMap<String, Object> param = new HashMap<>();
        HashMap<String, Object> map = new HashMap<>();
        Date dateBegin = null;
        Date dateEnd = null;
        param.put("approveStatus", approveStatus);
        if (type.equals("1")) {
            dateEnd = NewDateUtils.tomorrow();
            dateBegin = NewDateUtils.changeDay(dateEnd, -7);
            param.put("timeBegin", dateBegin);
            param.put("timeEnd", dateEnd);
            dateEnd = new Date();
        } else if (type.equals("2")) {
            dateBegin = NewDateUtils.changeMonth(new Date(),0);
            dateEnd = NewDateUtils.getLastTimeOfMonth(dateBegin);
            param.put("timeBegin", dateBegin);
            param.put("timeEnd", dateEnd);
        } else if (type.equals("3")) {
            dateBegin = NewDateUtils.dateFromString(timeBegin + " 00:00:00","yyyy-MM-dd HH:mm:ss");
            dateEnd = NewDateUtils.dateFromString(timeEnd+ " 23:59:59","yyyy-MM-dd HH:mm:ss");
            param.put("timeBegin", dateBegin);
            param.put("timeEnd", dateEnd);
        }
        if ("3".equals(eventType)) {
            param.put("toMid", "AuthApproval");
            param.put("fromOrg", user.getOid());
        } else {
            param.put("toUser", user.getUserID());
        }
        if (fromUser != 0) {
            UserHonePageDto userHonePageDto = userService.getUserHonePageDtoByUserId(fromUser);
            map.put("fromUserName", userHonePageDto.getUserName());
        }
        map.put("timeBegin", dateBegin);
        map.put("timeEnd", dateEnd);
        List<ForumPost> list = forumService.findForumByPage(param,eventType,fromUser);
        map.put("list", list);
        return new JsonResult(1, map);
    }

    //审批讨轮
    @ResponseBody
    @RequestMapping("/approveForum.do")
    public JsonResult middleApproveForum(User user, Integer postId, Integer processId,
                                         String approveStatus, String approveMemo, Integer toUser, String toUserName,
                                         String type){
       /* postId = 958;
        processId = 13288;
        approveStatus = "2";
        type = "2";*/
        String status = forumService.handlerForumPost(user, postId, processId, approveStatus, approveMemo, toUser, toUserName, type);
        HashMap<String,Object> map =new HashMap<>();
        map.put("status",status);
        return new JsonResult(1,map);
    }

    //根据人来获取最初页面的已通过的讨论(若title不是空就变为了搜索接口)
    @ResponseBody
    @RequestMapping("/getForumByAuth.do")
    public JsonResult getForumByAuth(User user, String title, Integer enabled, String updateDate, Integer stateVersion) throws IOException {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(15);
        List<ForumPost> list = forumService.getForumPost(user.getUserID(), title, enabled, updateDate, pageInfo);
        HashMap<String, Object> map = new HashMap<>();
        if(title != null){
            map.put("findTitle", title);
            map.put("forumNum", pageInfo.getTotalResult());
        }
        map.put("listForumPost",list);
        if(stateVersion != null)
            map.put("stateVersion",stateVersion);    //此值前端要求加入，最终解释权归前端所有
        return new JsonResult(1, map);
    }

    //点击讨论获取主题区信息
    @ResponseBody
    @RequestMapping("/getAllForumPostMessage.do")
    public JsonResult getAllForumPostMessage(User user, Integer id) throws IOException {
        Integer userID = user.getUserID();
        HashMap map = forumService.forumPostMessage(id, userID);
        Object[] object = forumService.getReplyAttNumByType(id);
        map.put("replyAttPictureNum",Integer.valueOf(object[0].toString()));
        map.put("replyAttfileNum", Integer.valueOf(object[1].toString()));
        this.removeLeftPostListNumber(user,id);
        int status = forumService.delMessageForForumPost(id, user.getUserID());   //删除某人讨论区图标上的数字且user表中讨论数量减1
        return new JsonResult(1, map);
    }

    //获取讨论组详情
    @ResponseBody
    @RequestMapping("/getPostMes.do")
    public JsonResult getPostMes(Integer id) throws IOException {
        HashMap<String, Object> map = new HashMap<>();
        ForumPost forumPost = forumService.forumPostMessageByhead(id);
        map.put("forumPost", forumPost);
        return new JsonResult(1, map);
    }

    //回调方法，用于去掉左侧列表的角标
    @ResponseBody
    @RequestMapping("/removeLeftPostListNumber.do")
    public JsonResult removeLeftPostListNumber(User user, Integer id){
        Integer userID = user.getUserID();
        //删除左侧列表上的数字
        forumService.delReplyUserByPostAndUser(id, userID);
        //删除未读数字并把@信息标记成已读
        forumService.delReplyUserState(id, null, userID);
        return new JsonResult(1,"成功");
    }

    //点击加载更多,X条未读消息和定位原文接口
    @ResponseBody
    @RequestMapping("/forumPostLoadingMes.do")
    public JsonResult forumPostLoadingMes(User user, Integer id, Integer replyId, String type, String unreadNum,
                                          Integer parentReplyId){
        HashMap map = forumService.forumReplyMessage(id, user.getUserID(), replyId, type, unreadNum, parentReplyId);
        return new JsonResult(1, map);
    }


    //获取全部消息并加上日历那里的查找方法
    @ResponseBody
    @RequestMapping("/getAllReply.do")
    public void getAllReply(HttpServletResponse response, User user, Integer postId, String type,
                            String time, Integer findUser, String mesType,
                            PageInfo pageInfo) throws IOException {
        HashMap<String, Object> map = new HashMap<>();
        List<ForumReply> list = null;
        if ("1".equals(type)){
            map = forumService.allReplyByType(user, postId, time, findUser, mesType, null, null, pageInfo, type);
            if (time != null && !"".equals(time)) {
                time = NewDateUtils.dateToString(new Date(), "yyyy-MM-dd HH:mm:ss");
            }
            map.put("time", time);
        } else {
            Date endTime = NewDateUtils.changeDay(new Date(),1);
            Date startTime = null;
            switch (type) {
                case "2":
                    startTime = NewDateUtils.changeDay(endTime, -7);
                    break;
                case "3":
                    startTime = NewDateUtils.changeDay(endTime, -30);
                    break;
                case "4":
                    startTime = NewDateUtils.changeDay(endTime, -90);
                    break;
                case "5":
                    startTime = NewDateUtils.changeDay(endTime, -365);
                    break;
            }
            map = forumService.allReplyByType(user, postId, null, findUser, mesType, startTime, endTime, pageInfo, type);
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    //获取日历上哪天有数据
    @ResponseBody
    @RequestMapping("/monthReplyRecord.do")
    public void monthReplyRecord(HttpServletResponse response, Integer id, String startTime, String endTime) throws Exception {
        HashMap<String, Object> map = new HashMap<>();
        Date dateBegin = NewDateUtils.dateFromString(startTime,"yyyy-MM-dd");
        Date dateEnd = NewDateUtils.dateFromString(endTime,"yyyy-MM-dd");
        List<Date> listDate = NewDateUtils.getEveryDate(dateBegin, dateEnd);
        Integer record = 0;
        List<String> listMonthMessage = new ArrayList<>();
        for(Date d : listDate){
            record += 1;
            Date startDay = NewDateUtils.today(d);
            Date endDay = NewDateUtils.tomorrow(d);
            PageInfo pageInfo = new PageInfo();
            pageInfo.setPageSize(1);
            List<ForumReply> forumReply = forumService.getReplyByDay(id,startDay,endDay,pageInfo);
            if (!forumReply.isEmpty()) {
                listMonthMessage.add(record.toString());
            }
        }
        map.put("listMonthMessage",listMonthMessage);
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }


    //获取附件
    @ResponseBody
    @RequestMapping("/findSubsidiaryFile.do")
    public void findSubsidiaryFile(HttpServletResponse response, Integer postId, String type, String attType,PageInfo pageInfo)throws IOException {
        List<ForumReplyAttachment> listReplyAtt = forumService.getSubsidiaryFile(postId,type,attType, null,pageInfo);
        HashMap<String, Object> map = new HashMap<>();
        map.put("listReplyAtt", listReplyAtt);
        ObjectToJson.objectToJson1(map, new String[]{},response);
    }


    //确定新增一条讨论消息
    @ResponseBody
    @RequestMapping("/addOneReply.do")
    public JsonResult addOneReply(User user, Integer postId, Integer reply, String content, String parentContent,
                                  String listforumReplyAttachment, String listUser, String phoneId, String forumAndroidId) throws IOException{
        ForumReply forumReply = forumService.addReplyForPub(user,postId,reply,content,parentContent,listforumReplyAttachment,listUser,forumAndroidId);
        if (phoneId != null) {
            HashMap<String, Object> mapPhone = new HashMap<>();
            mapPhone.put("forumReply", forumReply);
            mapPhone.put("phoneId", phoneId);
            return new JsonResult(1,mapPhone);
        }else {
            return new JsonResult(1,"成功");
        }
    }

    //转发文件消息
    @ResponseBody
    @RequestMapping("/forwardingAttAndReply.do")
    public JsonResult forwardingAttAndReply(User user, String postIds, String content, Integer replyIdByAtt, String listUser) throws IOException{
        Integer state = 1;
        List<Integer> listPostId = JSON.parseArray(postIds,Integer.class);
        if (listPostId.isEmpty()) {
            state = 2;
        } else {
            for (Integer postId : listPostId) {
                HashMap<String, Object> hashMapFile = forumService.insertOneReply(user, postId, null, "", null, null, listUser, null,"2",null,null,null,null,null, replyIdByAtt, null);
                ForumReply forumReplyFile = (ForumReply) hashMapFile.get("addForumReply");
                Integer parentCreator = (Integer) hashMapFile.get("parentCreator");
                ForumPost forumPost = (ForumPost) hashMapFile.get("forumPost");
                HashMap<String, Object> mapFile = new HashMap<>();
                List<ForumReply> listReplyFile = new ArrayList<>();
                listReplyFile.add(forumReplyFile);
                mapFile.put("addForumReply", listReplyFile);
                List<ForumPostUser> listPostUser = forumService.listPostUser(postId);
                forumService.pushNewReplyByType(listPostUser, "2", user.getUserID(), postId, forumReplyFile.getId() , null, parentCreator, listUser, mapFile, forumPost, null,null);
                if (content != "") {
                    HashMap<String, Object> hashMapMes = forumService.insertOneReply(user, postId, null, content, null, null, listUser, null,"1",null,null,null,null,null, null,null);
                    ForumReply forumReplyMes = (ForumReply) hashMapMes.get("addForumReply");
                    HashMap<String, Object> mapFileMes = new HashMap<>();
                    List<ForumReply> listReplyMes = new ArrayList<>();
                    listReplyMes.add(forumReplyMes);
                    mapFileMes.put("addForumReply", listReplyMes);
                    forumService.pushNewReplyByType(listPostUser, "2", user.getUserID(), postId, forumReplyFile.getId() , null, parentCreator, listUser, mapFileMes, forumPost, null, null);
                }
            }
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        return new JsonResult(1,map);
    }

    //换版外部文件
    @ResponseBody
    @RequestMapping("/updateReplyFileVersion.do")
    public JsonResult updateReplyFileVersion(User user, Integer replyId, String type, String path, String size, String module){
        forumService.changeAttVersionSend(user,replyId,type,path,size,module);
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", 1);
        return new JsonResult(1,map);
    }


    /*//确定新增一条讨论消息
    @ResponseBody
    @RequestMapping("/addOneReplyy.do")
    public JsonResult addOneReply(User user, Integer postId, Integer reply, String content, String parentContent,
                                  String listforumReplyAttachment, String listUser, String phoneId, String deviceId, ForumReplyAttachment fraa) throws IOException{
        ForumReply forumReply = null;
        if(content != ""){
            Integer parentCreator = null;
            if(reply != null){
                ForumReply forumReplyParent = null;
                //给父亲的回复次数加1，用在删除时不被允许
                forumReplyParent = forumService.oneForumReply(reply);
                forumReplyParent.setReplyNum(forumReplyParent.getReplyNum() + 1);
                parentCreator = forumReplyParent.getCreator();
            }
            forumReply = this.insertForumReply(postId, user, content, reply, parentContent, parentCreator, listUser, null,deviceId);
        }
        if (fraa.getPath() != null) {
            forumReply = this.insertForumReply(postId,user,null,null,null,null,null,fraa,deviceId);
        }
        if (listforumReplyAttachment != null) {
            List<ForumReplyAttachment> listReplyAtt = JSON.parseArray(listforumReplyAttachment, ForumReplyAttachment.class);
            for (ForumReplyAttachment fra : listReplyAtt) {
                this.addOneReply(user, postId,null,"",null,null,null,phoneId,deviceId,fra);
                if (forumReply == null){
                    ForumReplyAttachment forumReplyAttachment = forumService.forumReplyAttSingle(fra.getPath());
                    forumReply = forumService.oneForumReply(forumReplyAttachment.getReply());
                }
            }
        }
        if (phoneId != null) {
            HashMap<String, Object> mapPhone = new HashMap<>();
            mapPhone.put("forumReply", forumReply);
            mapPhone.put("phoneId", phoneId);
            return new JsonResult(1,mapPhone);
        }else {
            return new JsonResult(1,"成功");
        }

    }

    private ForumReply insertForumReply(Integer postId, User user, String content, Integer reply, String parentContent, Integer parentCreator, String listUser, ForumReplyAttachment fra, String deviceId){
        Integer userID = user.getUserID();
        ForumReply forumReply = new ForumReply();
        forumReply.setPost(postId);
        ForumReply forumReplyParent = null;
        forumReply.setOrg(user.getOid());
        forumReply.setCreator(userID);
        forumReply.setCreateName(user.getUserName());
        forumReply.setCreateDate(new Date());
        forumReply.setValid(1);
        forumReply.setReplyNum(0);
        forumReply.setMsgType("1");
        if(content != null){
            forumReply.setContent(content);
            if(reply != null){
                forumReply.setParent(reply);
                if(parentContent != null){
                    forumReply.setParentContent(parentContent);
                }
            }

        }else {
            forumReply.setContent("");
        }
        forumService.insertForumReply(forumReply);
        Integer replyId = forumReply.getId();
        if (fra != null) {
            fra.setReply(replyId);
            fra.setOrg(user.getOid());
            fra.setCreator(userID);
            fra.setCreateName(user.getUserName());
            fra.setCreateTime(forumReply.getCreateDate());
            fra.setLocation("1");
            forumService.insertReplyAttachment(fra,user);
            List<ForumReplyAttachment> listNewAtt = new ArrayList<>();
            listNewAtt.add(fra);
            forumReply = forumService.oneForumReply(replyId);
            if ("1".equals(fra.getType())) {
                forumReply.setReplyType(2);
            } else {
                forumReply.setReplyType(3);
            }
            forumReply.setForumReplyAttachmentHashSet(new HashSet<>(listNewAtt));
        }
        HashMap<String, Object> map = new HashMap<>();
        forumReply.setPostUserMussage(forumService.backUser(user));
        map.put("addForumReply", forumReply);
        //修改最新回复时间
        ForumPost forumPost = forumService.forumPostMessageByhead(postId);
        forumService.upPostStatus(forumPost, null, null, null, null, null, forumReply.getCreateDate());
        List<ForumPostUser> listPostUser = forumService.listPostUser(postId);
        forumService.pushNewReplyByType(listPostUser, "2", userID, postId, replyId, reply, parentCreator, listUser, map, forumPost,deviceId);
        return forumReply;
    }*/

    //删除回帖
    @ResponseBody
    @RequestMapping("/delOneReply.do")
    public JsonResult delOneReply(User user, Integer replyId) throws IOException {
        /*id = 613;*/
        HashMap<String, Object> map = new HashMap<>();
        String state = "0";
        ForumReply forumReply = forumService.oneForumReply(replyId);
        if (forumReply.getValid() == 1) {
            if(forumReply.getReplyNum() != null){
                if(forumReply.getReplyNum() > 0){
                    state = "3";
                }else {
                    BigInteger millis = BigInteger.valueOf(System.currentTimeMillis());
                    BigInteger createTimeMillis = BigInteger.valueOf(forumReply.getCreateDate().getTime());
                    BigInteger between = millis.subtract(createTimeMillis);
                    if(between.compareTo(new BigInteger("300000")) > 0){
                        state = "2";
                    } else {
                        state = "1";
                        //删除回帖
                        forumReply.setValid(0);
                        forumReply.setMsgType("2");
                        forumReply.setParentContent(forumReply.getContent());
                        forumReply.setContent( user.getUserName() + "撤回了一条消息");
                        forumService.upForumReply(forumReply);
                        //若此消息是回复的消息，要给父亲的回复次数加-1
                        if (forumReply.getParent() != null) {
                            ForumReply forumReplyParent = forumService.oneForumReply(forumReply.getParent());
                            forumReplyParent.setReplyNum(forumReplyParent.getReplyNum() - 1);
                        }
                        // map.put("state", state);
                        forumReply.setPostUserMussage(forumService.backUser(user));
                        map.put("delForumReply", forumReply);
                        ForumPost forumPost = forumService.forumPostMessageByhead(forumReply.getPost());
                        List<ForumPostUser> listPostUser = forumService.listPostUser(forumReply.getPost());
                        //删除讨论_回复用户分析表中的该回帖的参与人员数据
                        int delReplyState = forumService.delReplyUser(replyId);
                        //删除讨论_回复用户统计表中该reply的数据
                        forumService.delReplyUserState(forumReply.getPost(), replyId, null);
                        //回帖里有附件时，删除讨论_回复附件表此回帖的数据
                        forumService.delReplyAttachment(replyId,user);
                        //推送
                        forumService.pushNewReplyByType(listPostUser, "3", user.getUserID(), null, replyId, null, null, null, map, forumPost, null, null);
                    }
                }
            }
        }
        return new JsonResult(1,state);
    }

    //讨论区获取人员。 type 为1获取的是当前讨论所有的参与人，为2获取的是此机构中不是当前讨论的人，3获取的是除登录人之外的参与人，4是当有poseId时获取的是其他的中间人没有时获取的是除自己外的所有人员
    // 5 获取机构的全部人员
    @ResponseBody
    @RequestMapping("/getAllParticipants.do")
    public void getAllParticipants(Integer postId, String type, User user, HttpServletResponse response) throws IOException {
        List<ForumPostUser> listForumPostUser = forumService.listPostUser(postId);
        List<UserHonePageDto> listPostUser = new ArrayList<>();
        List<Integer> list = new ArrayList<>();
        if("5".equals(type)){
            listPostUser = userService.getStaffsOrSuperLocking(user.getOid(), null, null);
        }else if("4".equals(type)){
            if (postId != null) {
                List<ApprovalProcess> listAp = forumService.getlistApprovalProcess(postId,43);
                if (!listAp.isEmpty()) {
                    for (ApprovalProcess ap : listAp) {
                        list.add(ap.getToUser());
                    }
                    list.add(listAp.get(0).getFromUser());
                }
            } else {
                list.add(user.getUserID());
            }
            listPostUser = userService.getStaffsOrSuper(user.getOid(), "2", list);
        }else {
            for(ForumPostUser i : listForumPostUser){
                if ("3".equals(type)) {
                    if(!user.getUserID().equals(i.getUserID())){
                        list.add(i.getUserID());
                    }
                }else {
                    list.add(i.getUserID());
                }
            }
            if ("2".equals(type)) {
                listPostUser = userService.getStaffsOrSuperLocking(user.getOid(), "2", list);
            } else {
                listPostUser = userService.getStaffsOrSuperLocking(user.getOid(), "1", list);
            }
        }
        HashMap<String, Object> map  = new HashMap<>();
        map.put("listPostUser", listPostUser);
        ObjectToJson.objectToJson1(map, new String[]{}, response);
    }

    //删除某个参与人
    @ResponseBody
    @RequestMapping("/delPostUser.do")
    public JsonResult delPostUser(Integer postId, Integer delUser, User user) {
        ForumPost forumPost = forumService.forumPostMessageByhead(postId);
        ForumPostUser forumPostUser = forumService.getOnePostUser(postId, delUser);
        HashMap<String, Object> map = new HashMap<>();
        if (forumPostUser != null) {
            UserDto userDel = userService.getUserDtoByUserId(delUser);
            map.put("delUser", userDel);
            List<ForumPostUser> listPostUser = forumService.listPostUser(postId);
            //查询历史版本号
            Integer versionNo = forumService.checkeForumPostHistory(postId, forumPost, listPostUser, user);
            ForumReply forumReply = forumService.insertSystemReply(user.getUserID(), postId, user.getUserName(), "2", null, null, null, null);
            forumReply.setPostUserMussage(forumService.backUser(user));
            List<ForumReply> listReplyPush = new ArrayList<>();
            listReplyPush.add(forumReply);
            map.put("addForumReply", listReplyPush);
            forumService.upPostStatus(forumPost, null, null, null, null, forumPost.getParticipantsNum()-1, forumReply.getCreateDate());  //给参与人减1
            forumService.pushNewReplyByType(listPostUser, "2", user.getUserID(), postId, forumReply.getId(), null, null, null, map, forumPost, null, null);
            forumService.delPostUser(postId, delUser, forumPostUser); //删除参与人
            forumService.sendForumPostMessage(3, forumPost, delUser);
            List<ForumPostUser> newListPostUser = forumService.listPostUser(postId);
            //新增一版历史
            forumService.insertForumPostHistory(forumPost, versionNo, newListPostUser, user);
            return new JsonResult(1,"成功");
        } else {
            return new JsonResult("失败");
        }

    }

    //新增参与人
    @ResponseBody
    @RequestMapping("/addPostUser.do")
    public JsonResult addPostUser(Integer postId, String listForumPostUser, User user){
        ForumPost forumPost = forumService.forumPostMessageByhead(postId);
        //查询历史版本号
        Integer versionNo = forumService.checkeForumPostHistory(postId, forumPost, null, user);
        //List<String> listPos = JSON.parseArray(listForumPostUser, String.class);
        List<ForumPostUser> listPostUserNew = JSON.parseArray(listForumPostUser, ForumPostUser.class);
        List<UserHonePageDto> newListUser = new ArrayList<>();
        for (ForumPostUser fpu : listPostUserNew) {
            String addState = forumService.addPostUser(postId, fpu.getUserID());
            UserHonePageDto newUser = userService.getUserHonePageDtoByUserId(fpu.getUserID());
            newListUser.add(newUser);
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("newListUser", newListUser);
        ForumReply forumReply = forumService.insertSystemReply(user.getUserID(), postId, user.getUserName(), "2", null, null, null, null);
        forumReply.setPostUserMussage(forumService.backUser(user));
        List<ForumReply> listReplyPush = new ArrayList<>();
        listReplyPush.add(forumReply);
        map.put("addForumReply", listReplyPush);
        forumService.upPostStatus(forumPost, null, null, null, null, forumPost.getParticipantsNum()+newListUser.size(), forumReply.getCreateDate());   //给参与人加1
        List<ForumPostUser> listPostUser = forumService.listPostUser(postId);
        forumService.pushNewReplyByType(listPostUser, "2", user.getUserID(), postId, forumReply.getId(), null, null, null, map, forumPost, null, null);
        //新增一版历史
        forumService.insertForumPostHistory(forumPost, versionNo, listPostUser, user);
        return new JsonResult(1,"成功");
    }

    //修改讨论组详情并生成历史记录
    @ResponseBody
    @RequestMapping("/updateForumPost.do")
    public JsonResult updateForumPost(Integer postId, User user, String title, String content, Integer compere, String compereName){
        ForumPost forumPost = forumService.forumPostMessageByhead(postId);
        List<ForumPostUser> listPostUser = forumService.listPostUser(postId);
        //查询历史版本号
        Integer versionNo = forumService.checkeForumPostHistory(postId, forumPost,listPostUser, user);
        //新增消息
        ForumReply forumReply = forumService.insertSystemReply(user.getUserID(), postId, user.getUserName(), "5", null, title, content, compere);
        //修改讨论组
        forumService.upPostStatus(forumPost, title, content, compere, compereName,null,forumReply.getCreateDate());
        HashMap<String, Object> map = new HashMap<>();
        forumReply.setPostUserMussage(forumService.backUser(user));
        List<ForumReply> listReplyPush = new ArrayList<>();
        listReplyPush.add(forumReply);
        map.put("addForumReply", listReplyPush);
        if (compere != null) {
            UserHonePageDto newCompere = userService.getUserHonePageDtoByUserId(compere);
            map.put("newCompere", newCompere);
        }
        forumService.pushNewReplyByType(listPostUser, "2", user.getUserID(), postId, forumReply.getId(), null, null, null, map, forumPost, null, null);
        //新增一版历史
        forumService.insertForumPostHistory(forumPost, versionNo, listPostUser, user);
        return new JsonResult(1,"成功");
    }

    //获取历史记录
    @ResponseBody
    @RequestMapping("/getForumPostHis.do")
    public JsonResult getForumPostHis(Integer postId){
        List<ForumPostHistory> listPostHis = forumService.listForumPostHistory(postId);
        if (!listPostHis.isEmpty()) {
            for (ForumPostHistory fh : listPostHis) {
                List<ForumPostUserHistory> listUserHis = forumService.listForumPostHistoryUser(postId, fh.getId());
                fh.setListForumPostUserHistory(listUserHis);
            }
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("listForumPostHis",listPostHis);
        return new JsonResult(1,map);
    }

    //获取外部文件的换版记录
    @ResponseBody
    @RequestMapping("/getForumReplyAttHis.do")
    public JsonResult getForumReplyAttHis(Integer replyId, PageInfo pageInfo){
        List<ForumReplyAttHis> list = forumService.listReplyAttHis(replyId,pageInfo);
        HashMap<String, Object> map = new HashMap<>();
        map.put("list",list);
        map.put("pageInfo",pageInfo);
        return new JsonResult(1,map);
    }


    //新增回复时调的公用方法
    private void insertReplyPublicMethod(Integer replyId, Integer postId, Integer userId, String userName, Date now, String listUser){
        //获取所有的参与人
        List<ForumPostUser> listPostUser = forumService.listPostUser(postId);
        for (ForumPostUser fpu : listPostUser) {
            Integer userIdPost = fpu.getUserID();
            if (!userIdPost.equals(userId)) {
                ForumReplyUser replyUser = new ForumReplyUser();   //创建用户回复表对象用于主页每条讨论上的未读数字
                ForumReplyUserStat replyUserStat = new ForumReplyUserStat();    //创建用户统计表对象用于M条数和记录@人的跳转
                if(listUser != null){
                    List<String> userList = JSON.parseArray(listUser, String.class);
                    for(String u : userList){
                        if (u.equals(userIdPost.toString())) {
                            replyUser.setAtMum(1);
                            replyUserStat.setAtMum(1);
                        }
                    }
                }
                replyUser.setUserID(fpu.getUserID());
                replyUser.setPost(postId);
                replyUser.setReply(replyId);
                forumService.insertReplyUser(replyUser);
                replyUserStat.setUserID(fpu.getUserID());
                replyUserStat.setPost(postId);
                replyUserStat.setReply(replyId);
                replyUserStat.setTotalNum(1);
                replyUserStat.setReadMum(0);
                forumService.insertReplyUserStat(replyUserStat);
            }
        }
        //修改最新回复时间
        ForumPost forumPost = new ForumPost();
        forumPost.setId(postId);
        forumPost.setUpdateDate(now);
        forumPost.setLastestReplyTime(now);
        forumPost.setUpdateName(userName);
        forumService.upForumPost(forumPost);
    }


    //用于新增回复时，标出此人是否@人员
    private ForumReplyUserStat insertSpecialSign(ForumReplyUserStat forumReplyUserStat, String listUser, Integer userIdPost){
        List<String> userList = JSON.parseArray(listUser, String.class);
        for(String u : userList){
            if (u.equals(userIdPost.toString())) {
                forumReplyUserStat.setAtMum(1);
            }
        }
        return forumReplyUserStat;
    }

    //跳转页面的接口
    @RequestMapping("/discussionIndex.do")
    public String discussionIndex(User user, Model model ){
        String generalType = userIdentity(user);
        model.addAttribute("generalType", generalType);
        model.addAttribute("user",user);
        return "/discussion/discussion";
    }

    @RequestMapping("/discussionOtherIndex.do")
    public String discussionOtherIndex(User user, Model model ){
        String generalType = userIdentity(user);
        model.addAttribute("generalType", generalType);
        model.addAttribute("user",user);
        return "/discussion/discussHandleIndex";
    }

    //获取当前登录人的身份
    private String userIdentity(User user){
        String generalType = "";
        if (userService.isSuper(user)) {
            generalType = "0";
        }else if (userService.isGeneral(user)){
            generalType = "1";
        }else if (userService.isGeneralSmallManager(user)){
            generalType = "2";
        }
        return  generalType;
    }

    //获取外部文件的换版记录
    @ResponseBody
    @RequestMapping("/getForumReplyAttHistrdy.do")
    public JsonResult getForumReplyAttHistrdy(){
        List<ForumPostAttachment> listPostAtt = forumService.listPostAtt(1227);
        if (!(listPostAtt.isEmpty())) {
            for (ForumPostAttachment att : listPostAtt) {
                JsonArray jsonArray = new JsonArray();
                JsonObject jsonObject = new JsonObject();
                jsonObject.addProperty("path", att.getPath());
                jsonObject.addProperty("size", att.getSize());
                jsonObject.addProperty("title", att.getTitle());
                jsonObject.addProperty("type", att.getType());
                jsonObject.addProperty("module", "事务讨论");
                jsonObject.addProperty("place", att.getPlace());
                jsonArray.add(jsonObject);
                String json = jsonArray.toString();
                System.out.println(json);
            }
        }
        HashMap<String, Object> map = new HashMap<>();
        return new JsonResult(1,map);
    }


}
