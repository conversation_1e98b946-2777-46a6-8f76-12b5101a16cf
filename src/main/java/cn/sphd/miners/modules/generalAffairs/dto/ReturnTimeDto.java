package cn.sphd.miners.modules.generalAffairs.dto;

import cn.sphd.miners.common.persistence.BaseEntity;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.generalAffairs.entity.PersonnelAttendanceConfig;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceService.ConfigAttendancePattern;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * ReturnTimeDto_in_generalAffairs 某天的实际考勤设置
 * @apiNote 取代原returnTime方法返回的Map<String, Object>，主要属性来源于PersonnelAttendanceConfig表，但更新成当天的实际实际。
 * @author: wuyu
 * @since: 4.0
 * @date: 2025-04-28 11:00:33
 **/
public class ReturnTimeDto extends BaseEntity {
    Date now;//创建时间
    Date day;//日期
    Date beginTime;//上班时间
    Date endTime;//下班时间
    Date inputTime;//每日考勤录入时间(如未按时录入,则为旷工)
    Long lateLimit;//迟到时限
    Long earlyLimit;//早退时限
    Boolean middleBreak;//是否中间休息(是否中午考勤),true-是
    Date breakBegin;//午休开始时间
    Date breakEnd;//午休结束时间
    ConfigAttendancePattern cap;
    Boolean leaveWork;//请假到岗是否使用考勤宝:0-不使用(默认),1-使用
    Long p1Before;  //上班赦免(毫秒),null全天可用
    Long p1After;  //下班赦免(毫秒),null全天可用
    Long p2Before;  //请假离岗赦免(毫秒),null全天可用
    Long p2After;  //请假返岗赦免(毫秒),null全天可用
    Integer omitBefore;
    Integer omitAfter;

    public Date getNow() {
        return now;
    }

    public void setNow(Date now) {
        this.now = now;
    }

    public Date getDay() {
        return day;
    }

    public void setDay(Date day) {
        this.day = day;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getInputTime() {
        return inputTime;
    }

    public void setInputTime(Date inputTime) {
        this.inputTime = inputTime;
    }

    public Long getLateLimit() {
        return lateLimit;
    }

    public void setLateLimit(Long lateLimit) {
        this.lateLimit = lateLimit;
    }

    public Long getEarlyLimit() {
        return earlyLimit;
    }

    public void setEarlyLimit(Long earlyLimit) {
        this.earlyLimit = earlyLimit;
    }

    public Boolean getMiddleBreak() {
        return middleBreak;
    }

    public void setMiddleBreak(Boolean middleBreak) {
        this.middleBreak = middleBreak;
    }

    public Date getBreakBegin() {
        return breakBegin;
    }

    public void setBreakBegin(Date breakBegin) {
        this.breakBegin = breakBegin;
    }

    public Date getBreakEnd() {
        return breakEnd;
    }

    public void setBreakEnd(Date breakEnd) {
        this.breakEnd = breakEnd;
    }

    public ConfigAttendancePattern getCap() {
        return cap;
    }

    public void setCap(ConfigAttendancePattern cap) {
        this.cap = cap;
    }

    public Boolean getLeaveWork() {
        return leaveWork;
    }

    public void setLeaveWork(Boolean leaveWork) {
        this.leaveWork = leaveWork;
    }

    public Long getP1Before() {
        return p1Before;
    }

    public void setP1Before(Long p1Before) {
        this.p1Before = p1Before;
    }

    public Long getP1After() {
        return p1After;
    }

    public void setP1After(Long p1After) {
        this.p1After = p1After;
    }

    public Long getP2Before() {
        return p2Before;
    }

    public void setP2Before(Long p2Before) {
        this.p2Before = p2Before;
    }

    public Long getP2After() {
        return p2After;
    }

    public void setP2After(Long p2After) {
        this.p2After = p2After;
    }

    public Integer getOmitBefore() {
        return omitBefore;
    }

    public void setOmitBefore(Integer omitBefore) {
        this.omitBefore = omitBefore;
    }

    public Integer getOmitAfter() {
        return omitAfter;
    }

    public void setOmitAfter(Integer omitAfter) {
        this.omitAfter = omitAfter;
    }

    public ReturnTimeDto(Date day, PersonnelAttendanceConfig config, Date currentWorkday, Date nextWorkday) {
        Assert.notNull(day, "日期不能为空");
        Assert.notNull(config, "考勤设置不能为空");
        now = new Date(System.currentTimeMillis());
        day = NewDateUtils.today(day);
        this.day=day;
        beginTime = NewDateUtils.joinDateTime(day, config.getBeginTime());
        endTime = NewDateUtils.joinDateTime(day, config.getEndTime());
        cap = ConfigAttendancePattern.getByIndex(config.getAttendancePattern());
        if(ConfigAttendancePattern.manualEntry.equals(cap)) {
            inputTime = NewDateUtils.joinDateTime(nextWorkday, config.getInputTime());
        }
        lateLimit = config.getLateLimit()==null ? null : TimeUnit.MINUTES.toMillis(config.getLateLimit());
        earlyLimit = config.getEarlyLimit()==null ? null : TimeUnit.MINUTES.toMillis(config.getEarlyLimit());
        middleBreak = Boolean.TRUE.equals(config.getMiddleBreak());//默认值false
        breakBegin = NewDateUtils.joinDateTime(day, config.getBreakBegin());
        breakEnd = NewDateUtils.joinDateTime(day, config.getBreakEnd());
        leaveWork = Boolean.TRUE.equals(config.getLeaveWork());//默认值false
        p1Before = config.getP1Before()==null ? null : TimeUnit.MINUTES.toMillis(config.getP1Before());
        p1After = config.getP1After()==null ? null : TimeUnit.MINUTES.toMillis(config.getP1After());
        p2Before = config.getP2Before()==null ? null : TimeUnit.MINUTES.toMillis(config.getP2Before());
        p2After = config.getP2After()==null ? null : TimeUnit.MINUTES.toMillis(config.getP2After());
        omitBefore = config.getOmitBefore();
        omitAfter = config.getOmitAfter();
    }
    public Byte getAttendancePattern() {
        return Optional.ofNullable(cap).map(ConfigAttendancePattern::getIndex).orElse(null);
    }
    public Integer getSource() {
        return Optional.ofNullable(cap).map(ConfigAttendancePattern::getSource).orElse(null);
    }
}
