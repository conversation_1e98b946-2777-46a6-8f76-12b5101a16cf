package cn.sphd.miners.modules.dmmSchedule.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

//  日程收藏表
@Entity
@Table(name="t_dmm_favorite")
public class DmmFavorite implements Serializable {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="`acc_id`"  , nullable=true , unique=false)
    private Long accId;//账号id

    @Column(name="`name`"  , length=50 , nullable=true , unique=false)
    private String name;   //标题

    @Column(name="parent"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer parentId;//父ID

    @Column(name="`level`"  , nullable=true , unique=false)
    private Integer level;//层级

    @Column(name="orders"   , nullable=true , unique=false)
    private Integer orders;//排序

    @Column(name="`path`"  , length=255 , nullable=true , unique=false)
    private String path;   //路径,存各级祖先节点，以逗号分隔（实现用的/）

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;   //备注

    @Column(name="schedule_num"  , nullable=true , unique=false)
    private Integer scheduleNum;//备忘个数

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_time"   , nullable=true , unique=false)
    private Date createTime;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_time"   , nullable=true , unique=false)
    private Date updateTime;

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;//操作:1-增,2-删,3-改

    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId;//修改前记录ID

    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo;//版本号,每次修改+1

    @JsonIgnore
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="parent", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private DmmFavorite favorite;//自连接

//    @JsonIgnore
//    @JSONField(serialize = false)
    @OneToMany(targetEntity= DmmFavorite.class, fetch= FetchType.EAGER, mappedBy="favorite", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<DmmFavorite> dmmFavoriteHashSet = new HashSet<DmmFavorite>();

    @JsonIgnore
    @JSONField(serialize = false)
    @OneToMany(targetEntity= DmmFavoriteSchedule.class, fetch= FetchType.EAGER, mappedBy="favorite", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<DmmFavoriteSchedule> dmmFavoriteScheduleHashSet = new HashSet<DmmFavoriteSchedule>();



    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        if(!"/".equals(path.substring(path.length()-1))){
            this.path = path+"/";
        }else {
            this.path=path;
        }
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getScheduleNum() {
        return scheduleNum;
    }

    public void setScheduleNum(Integer scheduleNum) {
        this.scheduleNum = scheduleNum;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public DmmFavorite getFavorite() {
        return favorite;
    }

    public void setFavorite(DmmFavorite favorite) {
        this.favorite = favorite;
    }

    public Set<DmmFavorite> getDmmFavoriteHashSet() {
        return dmmFavoriteHashSet;
    }

    public void setDmmFavoriteHashSet(Set<DmmFavorite> dmmFavoriteHashSet) {
        this.dmmFavoriteHashSet = dmmFavoriteHashSet;
    }

    public Set<DmmFavoriteSchedule> getDmmFavoriteScheduleHashSet() {
        return dmmFavoriteScheduleHashSet;
    }

    public void setDmmFavoriteScheduleHashSet(Set<DmmFavoriteSchedule> dmmFavoriteScheduleHashSet) {
        this.dmmFavoriteScheduleHashSet = dmmFavoriteScheduleHashSet;
    }

    public Long getAccId() {
        return accId;
    }

    public void setAccId(Long accId) {
        this.accId = accId;
    }
}
