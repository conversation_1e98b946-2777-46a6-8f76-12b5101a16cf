<template>
	<div class="uni-container">
		<div v-if="shareMessage && shareMessage.type==1" class="live-panel live-tr">
			本条数据由{{ shareMessage.shareName}}于{{shareMessage.shareTime }} 分享
		</div>
    <div class="live-panel">
      <div class="live-title">“字幕精灵”主要功能</div>
      <div class="live-tr"><span class="dot"></span>发布直播场次预告</div>
      <div class="live-tr"><span class="dot"></span>发布某场次的节目单</div>
      <div class="live-tr"><span class="dot"></span>发动团队进行调研</div>
      <div class="live-tr"><span class="dot"></span>针对调研结果采取营销活动</div>
    </div>
    <div class="live-panel">
      <div class="live-tr">如您已为直播购置话筒等装备、直播事业刚开始或已遭遇瓶颈，请使用“字幕精灵”！</div>
      <div class="live-tr">字幕精灵，将为您的直播事业提供助力！</div>
    </div>
    <div class="live-cells">
      <div class="live-cell" v-for="(item, index) in list" :key="index" @click="jumpPage(item)">
        <el-icon color="#53B5A8" size="18px">
          <component :is="item.icon"/>
        </el-icon>
        <span style="margin-left: 8px">{{item.name}}</span>
        <el-icon class="arrow-right"><ArrowRight /></el-icon>
      </div>
    </div>
    <div class="live-container">
      <span @click="goPage(register2URL)" class="primary live-button">注&nbsp;&nbsp;&nbsp;册</span>
      <span @click="goPage()" class="cancel live-button">退&nbsp;&nbsp;&nbsp;出</span>
    </div>
	</div>
</template>

<script>
import { getMesForShare } from '../../api/api.js'
import auth from '../../sys/auth.js'
import JSONBig from 'json-bigint'

export default {
		data() {
			return {
				list: [],
        register2URL:"register2",
        shareMessage:{}
			}
		},
		created(option){
			console.log('option=', option)
      let localJsonData = JSONBig.parse(localStorage.getItem('jsonData'))
      this.shareMessage = localJsonData && localJsonData.share
      console.log('shareMessage', this.shareMessage)
      if(this.shareMessage && this.shareMessage.type){
        	this.shareMessage.shareTime =  new Date(Number(this.shareMessage.shareTime)).format("yyyy-MM-dd hh:mm")
      }
      getMesForShare().then(res => {
        console.log('res', res)
        let data0 = res.data
        if(data0){
          let data = res.data.list
          const userAgreement = data.find(item => item.name==='关于-服务协议-Z')
          const privacyPolicy = data.find(item => item.name==='关于-隐私条款-Z')
          this.list = [
            {icon: 'Paperclip', name: '服务协议', url: 'aboutShareDetail/userAgreement', param: userAgreement.content},
            {icon: 'Document', name: '隐私条款', url: 'aboutShareDetail/privacyPolicy', param: privacyPolicy.content},
            {icon: 'Coin', name: '价格政策', url: 'pricePolicy', param: ''}
          ]
        }else {
          console.log(res.errorMsg)
        }

      })

      console.log('怎么就没有李斯特呢', this.list)
      // let userInfo = localJsonData.userInfo
		},
		methods: {
			goPage(url) {
				console.log(url);
				let user = auth.getAcc()
				if(url === 'register2' && user.mobile){
				  console.log('user 有没有读出来', user)
          url = 'register3'
				}
				console.log('$router.push goPage=', url)
        this.$router.push({name: url});
			},
      jumpPage(item) {
			  console.log(item)
        this.$router.push({ path: '/' + item.url, query: {content: item.param}});
      },
			tipBtn: function (){
				// uni.showToast({
				// 	title: '您输入的验证码有误！',
				// 	icon: 'none',
				// 	duration: 3000
				// });

			}
		}
	}
</script>

<style lang="scss" scoped>

$dotLen:4px;
$dotLen2:2px;

.dot{
  background: $livePageHeadBg;
  width: $dotLen;
  height: $dotLen;
  border-radius: $dotLen2;
  margin:auto 10px;
  position: relative;
  display: inline-block;
  top:-3px;
}

.relaRight{
  position: relative;
  top:6px;
}
.relaRight2{
  position: relative;
  top:2px;
}

</style>
