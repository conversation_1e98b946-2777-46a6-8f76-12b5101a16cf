package cn.sphd.miners.modules.accountant.controller;

import cn.sphd.miners.common.utils.WriteJSONUtil;
import cn.sphd.miners.modules.accountant.entity.SubjectBalanceSheet;
import cn.sphd.miners.modules.accountant.service.AccountantTrialBalanceService;
import cn.sphd.miners.modules.system.entity.User;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * Created by root on 17-5-22.
 */
@Controller
@RequestMapping("/accountant")
public class AccountantTrialBalanceController {

    private static final String SUCCESS = "1";
    private static final String ERROR = "0";

    @Autowired
    AccountantTrialBalanceService trialBalanceService;

    /*得到试算平衡表的所有数据*/
    @ResponseBody
    @RequestMapping("/getTrialBalanceData.do")
    public void getTrialBalanceData(User user, String period, HttpServletResponse response)
    {

//        User user = (User) session.getAttribute("user");
        int oid = user.getOid();
//        Integer oid = (Integer) session.getAttribute("oid");

        //为peroid设置默认值
        if(period == null)
        {
            Calendar calendar = Calendar.getInstance();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            period = sdf.format(calendar.getTime());
        }

        SubjectBalanceSheet subBalanSheet = new SubjectBalanceSheet();
        subBalanSheet.setOid(oid);
        subBalanSheet.setPeroid(period);

        List<SubjectBalanceSheet> listSubjects = trialBalanceService.getTrialBalanceData(subBalanSheet);
        JSONObject result = new JSONObject();
        result.put("code", 1);
        result.put("msg", "");

        //返回合计
        BigDecimal sumPreviousBalanceCredit = BigDecimal.valueOf(0);
        BigDecimal sumPreviousBalanceDebit = BigDecimal.valueOf(0);
        BigDecimal sumCredit = BigDecimal.valueOf(0);
        BigDecimal sumDebit = BigDecimal.valueOf(0);
        BigDecimal sumBalanceCredit = BigDecimal.valueOf(0);
        BigDecimal sumBalanceDebit = BigDecimal.valueOf(0);
        if(listSubjects != null)
        {
            List<SubjectBalanceSheet> temp = new ArrayList<SubjectBalanceSheet>();
            for (SubjectBalanceSheet sbs : listSubjects)
            {
                //如果期初余额和期末余额都是0的话从列表里删除不需要显示在页面上
                BigDecimal balance = sbs.getBalance() == null ? BigDecimal.valueOf(0) : sbs.getBalance();
                BigDecimal preBalance = sbs.getPreviousBalance() == null ? BigDecimal.valueOf(0) : sbs.getPreviousBalance();
                BigDecimal credit = sbs.getCredit() == null ? BigDecimal.valueOf(0) : sbs.getCredit();
                BigDecimal debit = sbs.getDebit() == null ? BigDecimal.valueOf(0) : sbs.getDebit();
                BigDecimal beginBalanceance = sbs.getBeginningBalance() == null ? BigDecimal.valueOf(0) : sbs.getBeginningBalance();

                if(preBalance.doubleValue() == 0 && balance.doubleValue() == 0 && credit.doubleValue() == 0 && debit.doubleValue() == 0 && beginBalanceance.doubleValue() == 0)
                {
                    temp.add(sbs);
                    continue;
                }
                //统计期初借方金额
                if ("1".equals(sbs.getPreviousDirection()))
                {
                    sumPreviousBalanceCredit = sumPreviousBalanceCredit.add(preBalance);
                }
                //统计期初贷方金额
                if ("2".equals(sbs.getPreviousDirection()))
                {
                    sumPreviousBalanceDebit = sumPreviousBalanceDebit.add(preBalance);
                }
                //统计借方发生额
                sumCredit = sumCredit.add(credit);
                //统计贷方发生额
                sumDebit = sumDebit.add(debit);
                //统计期末借方发生额
                if ("1".equals(sbs.getBalanceDirection()))
                {
                    sumBalanceCredit = sumBalanceCredit.add(sbs.getBalance());
                }
                //统计期末贷方发生额
                if ("2".equals(sbs.getBalanceDirection()))
                {
                    sumBalanceDebit = sumBalanceDebit.add(sbs.getBalance());
                }
            }
            listSubjects.removeAll(temp);
            result.put("data", listSubjects);
            result.put("sumPreviousBalanceCredit", sumPreviousBalanceCredit);//期初借方金额
            result.put("sumPreviousBalanceDebit", sumPreviousBalanceDebit);//期初贷方金额
            result.put("sumCredit", sumCredit);//借方发生额
            result.put("sumDebit", sumDebit);//贷方发生额
            result.put("sumBalanceCredit", sumBalanceCredit);//期末借方发生额
            result.put("sumBalanceDebit", sumBalanceDebit);//期末贷方发生额
        }

        WriteJSONUtil.writeJSON(result.toString(), response);
//        return new Result(SUCCESS,"",listSubjects);
    }

}
