package cn.sphd.miners.modules.schedule.service;

import cn.sphd.miners.modules.schedule.entity.MemoScheduleImage;

import java.util.List;

/**
 * Created by Administrator on 2019/8/13.
 */
public interface MemoScheduleImageService {

    void deleteMemoScheduleImage(MemoScheduleImage memoScheduleImage);

    void  deleteAllMemoScheduleImage(Integer scheduleId);

    List<MemoScheduleImage> getMemoScheduleImageListBySid(Integer scheduleId);

    MemoScheduleImage getMemoScheduleImageById(Integer id);
}
