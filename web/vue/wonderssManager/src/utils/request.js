import axios from "axios"
import auth from "@/sys/auth"
import { ElLoading } from 'element-plus'

auth.init({isGuest: true})


//可以添加多个不同的request
const requests = {
    default: axios.create({
        method: 'POST'
    }),
    noloadingHttp:axios.create({
        method: 'POST'
    })
}


let loading = null
requests.default.interceptors.request.use(
    (config) => {
        let options = { lock: true,  text: config['LOADING_TEXT'] ? config['LOADING_TEXT'] : '正在加载中，请稍后...', /* spinner: 'el-icon-loading', */ background: 'rgba(0, 0, 0, .3)' }
        loading = ElLoading.service(options)
        return config;
    },
    (error) => {
        Promise.reject(error);
    }
)
requests.default.interceptors.response.use(
    (config) => {
        loading?.close()
        return config;
    },
    (error) => {
        loading?.close()
        Promise.reject(error);
    }
)


//所有request都需要添加auth.init中设置的默认拦截器
Object.keys(requests).forEach((name) =>{
    requests[name].interceptors.request.handlers.push(...axios.interceptors.request.handlers)
    requests[name].interceptors.response.handlers.push(...axios.interceptors.response.handlers)
})

export const { default: request, noloadingHttp } = requests

// export default request.normalHttp
// export default request.noloadingHttp
