<template>
  <div class="lyricDetails">
    <Pageheader title="歌词上传者名单" :showBack="true"/>
    <div class="">
      <div class="txtCenter">
        <div class="ttl">{{ playName }}</div>
        <div>
          {{ playTime }}
        </div>
      </div>
      <div>
        <el-table :data="tableData" stripe style="width: 100%">
          <el-table-column align="center" prop="nametime" label="上传"> </el-table-column>
<!--          <el-table-column align="center" prop="sta" label="被使用情况"> </el-table-column>-->
          <el-table-column label="被使用情况" align="center">
            <template #default="scope" >
              <div class="bigGou" v-html="scope.row.sta"></div>
            </template>
          </el-table-column>
          <el-table-column align="center" fixed="right" label="播放" width="140">
            <template #default="scope">
              <el-button @click="playMusic(scope.row, 1)" type="text" size="small">原唱</el-button>
              <el-button @click="playMusic(scope.row, 2)" type="text" size="small">伴奏</el-button>
            </template>
          </el-table-column>
        </el-table>

      </div>
    </div>
    <LyricPlayer v-if="playLyc" :playName="playName" :lyricTxt="lyricTxt" :fileUrl="fileUrl" @closePlayLyc="closePlayLyc"></LyricPlayer>

  </div>
</template>
<script setup>
import { ref, onMounted, reactive } from 'vue';
import { useRouter } from "vue-router";
import { ElMessageBox, ElMessage } from 'element-plus';
import { getAllRhythm } from '@/api/music.js'
import JSONBig from 'json-bigint'
import '@/utils/DateFormat.js'

const router = useRouter();
const playName = ref('')
const lyricTxt = ref('')
const playLyc = ref(false)
const playTime = ref('')
const fileUrl = ref('')
let songDetails = reactive('')
let tableData = reactive([])

onMounted(() => {
  getuploadUser()
});

const getuploadUser = () => {
  let lyricDetailsItem = JSONBig.parse(localStorage.getItem('lyricDetailsItem'));
  console.log('lyricDetailsItem=', lyricDetailsItem)
  getAllRhythm({
    'id': lyricDetailsItem.id,
  }).then( res => {
    console.log('lyriTaskApproveMes 详情返回值=', res)
    const data = res.data.listRhythm
    data.forEach( item => {
      tableData.push({
        nametime:`${ item.createName } ${ new Date(item.createTime).format('yyyy-MM-dd hh:mm:ss') }`,
        sta:`${ item.selected  ? '✓' : '--' }`,
        item
      })
    })
    console.log('tableData', tableData)

  }).catch(err => {
    console.log('获lyriTaskApproveMes 详情 失败', err)
  })
}
const closePlayLyc = (val)=> {
  playLyc.value = val
}
// creator：hxz 播放音乐
const playMusic = (lyc, type)=> {
  let lyricDetailsItem = JSONBig.parse(localStorage.getItem('lyricDetailsItem'));
  console.log('lyricDetailsItem=', lyricDetailsItem)
  console.log('playMusic lyc=', lyc)
  let uploadUrl = localStorage.getItem('uploadUrl');
  if(type === 1){ // 原唱
    fileUrl.value = uploadUrl + lyricDetailsItem.originalPath
  }else { // 伴奏
    fileUrl.value = uploadUrl + lyricDetailsItem.bgmPath
  }
  console.log(' fileUrl.value =',  fileUrl.value )
  playLyc.value = true
  playName.value = lyc.item.name
  lyricTxt.value = lyc.item.rhythm
  // console.log(' playName.value=',  playName.value )
  // console.log(' lyricTxt.value=',  lyricTxt.value )
}

</script>
<style lang="scss" scoped>
.lyricDetails{
  .txtCenter{
    text-align: center;
    margin-bottom: 30px;
  }
  .ttl{
    font-weight: bolder; line-height: 30px;
  }
  .tr{
    line-height: 50px;
  }
  .houseSty8{
    position: relative;
    top:3px;
  }
  .bigGou{
    font-size: 30px;
    color:$livePageHeadBg;
    text-align: center;
  }
}
</style>
