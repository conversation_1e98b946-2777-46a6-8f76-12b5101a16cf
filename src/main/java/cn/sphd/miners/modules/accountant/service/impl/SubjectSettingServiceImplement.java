package cn.sphd.miners.modules.accountant.service.impl;

import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.accountant.entity.*;
import cn.sphd.miners.modules.accountant.mapper.*;
import cn.sphd.miners.modules.accountant.service.Accountant1_209_Service;
import cn.sphd.miners.modules.accountant.service.AccountantSettleService;
import cn.sphd.miners.modules.accountant.service.BuildAccountService;
import cn.sphd.miners.modules.accountant.service.SubjectSettingService;
import cn.sphd.miners.modules.accountant.util.Tools;
import cn.sphd.miners.modules.accountantReportTax.service.AcctReportTaxService;
import cn.sphd.miners.modules.equipment.service.ITEquFixedAssetsService;
import cn.sphd.miners.modules.finance.service.DataService;
import cn.sphd.miners.modules.message.dao.MessageDao;
import cn.sphd.miners.modules.system.entity.User;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by root on 17-1-4.
 */
@Service
@Transactional(propagation= Propagation.REQUIRED)
public class SubjectSettingServiceImplement implements SubjectSettingService {

    @Autowired
    SubjectCategoryMapper subjectCategoryMapper;
    @Autowired
    TAccountSubjectMapper tAccountSubjectMapper;
    @Autowired
    VoucherSubjectMapper voucherSubjectMapper;
    @Autowired
    SettleMapper settleMapper;
    @Autowired
    StatetementDefMapper statetementDefMapper;
    @Autowired
    SubjectPeroidMapper subjectPeroidMapper;
    @Autowired
    TBalanceSheetMapper tBalanceSheetMapper;
    @Autowired
    ProfitStatementMapper profitStatementMapper;
    @Autowired
    CashFlowMapper cashFlowMapper;
    @Autowired
    TAccountantSubjectHistoryMapper tAccountantSubjectHistoryMapper;
    @Autowired
    VoucherMapper voucherMapper;
    @Autowired
    AccountantSettleService accountantSettleService;
    @Autowired
    AccountantSettleService settleService;
    @Autowired
    DetailMapper detailMapper;
    @Autowired
    TAccountantSubjectCashMapper subjectCashMapper;
    @Autowired
    TAccountantCashMapper tAccountantCashMapper;
    @Autowired
    MessageDao messageDao;
    @Autowired
    SubjectSelectMapper subjectSelectMapper;
    @Autowired
    BuildAccountService buildAccountService;
    @Autowired
    TaccountantEstablishEntityMapper establishEntityMapper;
    @Autowired
    BuildAccountMapper buildAccountMapper;
    @Autowired
    TaccountantSubjectRecordMapper recordMapper;
    @Autowired
    VoucherUpdate voucherUpdate;
    @Autowired
    GeneralSubjectRecord generalSubjectRecord;
    @Autowired
    DataService dataService;
    @Autowired
    Accountant1_209_Service accountant1_209_service;
    @Autowired
    AcctReportTaxService taxService;
    @Autowired
    ITEquFixedAssetsService equFixedAssetsService;

    @Override
    public List idesBySubjectCategory(String parent) {
        List<SubjectCategory> list = subjectCategoryMapper.subjectCategoryByList(parent);
        List ides = new ArrayList(list.size());
        for (SubjectCategory s : list  ) {
            ides.add(s.getId());
        }
        return ides;
    }

    /*
     * 获取某类别所有科目
     * */
    @Override
    public List<TAccountantSubjectEntity> listAccountantSubject(HashMap param) {
        //获取最新的建账ID
//        TAccountantSetting link = new TAccountantSetting();
//        link.setOrg((Integer) param.get("org"));
//        link.setKey_("establish_id");
//        link = buildAccountMapper.getSettingByKey(link);
//        param.put("eid",link.getValue_());
        List<TAccountantSubjectEntity> list = tAccountSubjectMapper.listByAccountSubject(param);
        return list;
    }

    @Override
    public String firstSubject(String subject, Integer oid) {
        TAccountantSubjectEntity s = new TAccountantSubjectEntity();
        s.setOrg(oid);
        s.setSubject(subject);
        TAccountantSubjectEntity listFirstSubject = tAccountSubjectMapper.listByFirstSubject(s);
        String list = null;
        if (listFirstSubject != null) {
            Integer maxChildSubjects = listFirstSubject.getMaxChildSubjects();//该科目当前的子科目数
            Integer newMcs = maxChildSubjects + 1;
            //这三个科目是特殊科目，编号不是顺序递增的，而是根据最后一个子科目的顺序号加一产生
            if (subject.equals("5301") || subject.equals("5601") || subject.equals("5711"))
            {
                s.setParent(subject);
                List<TAccountantSubjectEntity> lastSubjectList = tAccountSubjectMapper.lastSubject(s);//该科目下的最后一个子科目
                maxChildSubjects = Integer.parseInt(lastSubjectList.get(0).getSubject().substring(4, 8));//子科目的后四位，就是改科目的当前子科目数
                newMcs = maxChildSubjects + 1;
            }
            //子科目数是4位，最大9999
            if (newMcs < 10000) {
                HashMap<String, Object> param = new HashMap<String,Object>();
                param.put("subject", subject);
                param.put("cs", newMcs);
                String newSubject = tAccountSubjectMapper.newSubject(param);//得到的新科目编号
                //如果新科目编号大于16（科目的最大长度），返回失败
                if (newSubject != null) {
                    if (newSubject.length() > 16)
                        return null;
                }
                String name = listFirstSubject.getName();
                String categoryName = listFirstSubject.getCategoryname();
                String direction = listFirstSubject.getBalanceDirection();
//                list="[{'newSubject':'"+ newSubject +"','subject':'"+subject+"','name':'" + name + "','categoryname':'"+categoryName+"','direction':'"+direction+"' }]";
                JSONArray array = new JSONArray();
                JSONObject object = new JSONObject();
                object.put("newSubject",newSubject);
                object.put("subject",subject);
                object.put("name",name);
                object.put("categoryname",categoryName);
                object.put("direction",direction);
                array.put(object);
                list = array.toString();
            }
        }
        return list;
    }

    /*
     * 新增科目
     * parent                        父科目编号
     * name                          新科目名称
     * measureUnit                   计量单位，如果有数量辅助核算的话有该字段，物料采购那边关联生成科目时有该字段。
     * staffId                       创建人ID
     * staffName                     创建人名称
     * newsubject                    新科目编号
     * quantityAssistingAccounting   "1"-需要数量辅助核算，"0"-不需要
     * relevanceType                 关联类型:1-商品，2-物料,3-供应商,4-客户,5-半成品,6-财务对公户
     * relevanceItem                 关联项的id,根据代码去不同的表中查,商品去找商品表找id，物料去找物料表id
     * */
    @Override
    public net.sf.json.JSONObject  newAccountantSubject(String parent, Integer oid, String name, String measureUnit, Integer staffId, String staffName,
                                                        String newsubject,String quantityAssistingAccounting, String relevanceType, Integer relevanceItem) {

        net.sf.json.JSONObject result = new net.sf.json.JSONObject();
        String existSubject = null;
        //如果是其他模块关联的会计科目的话先判断该科目的名称是不是已经存在，存在的话返回该名称对应的科目编号.如果不存在改科目的话返回null
        //财务关联的科目不是以科目名称来验证是否重复和物料关联不同，所以得把"6"的情况排除出去
        if (relevanceType != null && !"6".equals(relevanceType)) {
            existSubject = checkNameDuplicate(oid,name,parent);
            if (existSubject != null) {
                result.put("newSubjectID" ,0);
                result.put("existSubject" ,existSubject);
                return result;
            }
        }


        TAccountantSubjectEntity s = new TAccountantSubjectEntity();
        s.setOrg(oid);
        s.setSubject(parent);
        TAccountantSubjectEntity listFirstSubject = tAccountSubjectMapper.listByFirstSubject(s);
        TAccountantSubjectEntity parentSubject = null;
        if (listFirstSubject != null) {
            parentSubject = listFirstSubject;
        }
        else {
            return result;
        }
        s.setSubject(newsubject);
        s.setParent(parent);
        s.setName(name);
        s.setMeasureUnit(measureUnit);
        s.setCreator(staffId);
        s.setCreateName(staffName);
        s.setCreateDate(Tools.date2Str(new Date()));
        s.setCategory(parentSubject.getCategory());
        Integer level = parentSubject.getLevel()+1;
        s.setLevel(level);
        s.setState(parentSubject.getState());
        s.setBalanceDirection(parentSubject.getBalanceDirection());
        s.setMaxChildSubjects(0);
        s.setQuantityAssistingAccounting(quantityAssistingAccounting);
        s.setRelevanceType(relevanceType);
        if (relevanceType != null && !"6".equals(relevanceType)) {
            s.setUseable("0");
        }
        else
        {
            s.setUseable("1");
        }
        if (relevanceItem != null) {
            s.setRelevanceItem(relevanceItem);
        }
        int res =tAccountSubjectMapper.insertSubject(s);
        if (res > 0) {
            Integer max = parentSubject.getMaxChildSubjects()+1;
            TAccountantSubjectEntity os = new TAccountantSubjectEntity();
            os.setMaxChildSubjects(max);
            os.setSubject(parent);
            os.setOrg(oid);
            tAccountSubjectMapper.upadteParentSubject(os);//更新父科目的叶子节点数

            //新增科目时要判断是在建账过程中新增的还是建账完成后新增的
            //建账完成后对科目对所有操作都要产生操作记录
            boolean buildState = generalSubjectRecord.checkBuildState(oid);
            //建账完成
            if (buildState) {
                Integer eid = generalSubjectRecord.getEstablishByOrg(oid);
                //检查setting表是否有established
                if (eid == null) {
                    eid = voucherUpdate.establishCompatible(oid,staffId,staffName);
                }

                //建账之后需要新增操作记录，写进record表
                TaccountantSubjectRecord recordDtoParam = new TaccountantSubjectRecord();
                recordDtoParam.setOrg(oid);
                recordDtoParam.setState(1);//1-修改后，0-修改前
                recordDtoParam.setEstablish(eid);
                recordDtoParam.setOperation("1");//操作:1-增,2-删,3-改,4-启用,5-停用,6-建账,7-重新建账
                recordDtoParam.setCreateDate(new Date());
                recordDtoParam.setCreator(staffId);
                recordDtoParam.setCreateName(staffName);
                recordDtoParam.setSubject(newsubject);
                recordDtoParam.setName(name);
                recordDtoParam.setLevel(level);
//                        recordMapper.insert(recordDtoParam);
                // 将科目新增记录写到相关的表，
                // journal表是存储科目变动记录的内容，如果某天科目有变动，需向该表写内容。
                // change表是存储机构截止到某天的所有科目，当科目有操作记录产生时需向该表写内容。
                generalSubjectRecord.insertSubject(recordDtoParam,s,max);
            }

        }
        result.put("newSubjectID" ,res);
        result.put("existSubject" ,existSubject);
        return result;
    }

    /*
     * 关联生成物料相关科目
     * */
    @Override
    public int generateSubjects(User user, MaterialSubjectParam param) {

        String supplierParentSubject = param.getSupplierParentSubject();
        Integer oid = user.getOid();
        String supplier = param.getSupplier();
        Integer userid = user.getUserID();
        String username = user.getUserName();
        String supplierSubject = param.getSupplierSubject();
        String material = param.getMaterial();
        //1-选中数量核算功能（科目选择的时候需要输入数量和单位等信息），0-没有数量核算功能
        // 新增供应商科目的时候不需要这个，可以传0，但是新增物料科目的时候需要传1
        String quantityAssistingAccounting = "1";
        String relevanceSupplierType = param.getRelevanceSupplierType();
        Integer relevanceSupplierItem = param.getRelevanceSupplierItem();
        //新增供应商名称对应的科目
        net.sf.json.JSONObject newSupplier = newAccountantSubject(supplierParentSubject,oid,supplier,null,userid,username,supplierSubject,"0",relevanceSupplierType,relevanceSupplierItem);
        int newSupplierID = newSupplier.getInt("newSubjectID");
//        if (newSupplierID > 0) {
        //生成物料名称对应的科目
        //先生成新编号,如果要新增的供应商已经存在的话就不要新增科目编号了，直接在老的供应商下面新增物料
        if (newSupplierID == 0) {
            supplierSubject = newSupplier.getString("existSubject");
        }
        String list = firstSubject(supplierSubject,oid);
        net.sf.json.JSONArray listArray = net.sf.json.JSONArray.fromObject(list);
        if (listArray.size() > 0) {
            net.sf.json.JSONObject jsonObject = listArray.getJSONObject(0);
            String materialChildSubject = jsonObject.optString("newSubject");//生成的物料编号
            //在刚生成的供应商科目下面新增物料编号
            String relevanceMaterialType = param.getRelevanceMaterialType();
            Integer relevanceMaterialItem = param.getRelevanceMaterialItem();
            String measureUnit = param.getMeasureUnit();
            net.sf.json.JSONObject newMaterial = newAccountantSubject(supplierSubject,oid,material,measureUnit,userid,username,materialChildSubject,"1",relevanceMaterialType,relevanceMaterialItem);
            int materialSubjectid = newMaterial.getInt("newSubjectID");
            if (materialSubjectid > 0) {
                //把消息更新成已读
                int messageid = param.getMessageid();
                return updateMessage(messageid);
            }
            else {
                //科目已存在
                String existSubject = newMaterial.getString("existSubject");
                if (existSubject != null) {
                    int messageid = param.getMessageid();
                    updateMessage(messageid);
                    return 2;
                }
            }
        }
//        }

        return 0;
    }

    /*把消息更新成已读*/
    private int updateMessage(int messageid) {
        String messageHql = "update UserMessage m set m.state=:state where m.id=:id";
        Map<String,Object> msgParams = new HashMap<>();
        msgParams.put("state",2);
        msgParams.put("id",messageid);
        messageDao.queryHQLWithNamedParams(messageHql,msgParams);
        return 1;
    }


    @Override
    public int isDisplayTaxBtn(int oid) {
        TAccountantSettle res = settleMapper.isDisplayTaxBtn(oid);
        if (res == null)
            return 0;
        else
            return 1;
    }

    @Override
    public String checkRepeat(String subject, String name,Integer id) {
        List<TAccountantSubjectEntity> subjects = tAccountSubjectMapper.getRepeatSubjects(subject,name,id);
        if(subjects!=null&&subjects.size()>0){

            return subjects.get(subjects.size()-1).getSubject();
        }
        return "";
    }

    /*
     * 返回低值易耗品1413和固定资产1601下的所有科目
     * */
    @Override
    public List<String> getEquipmentToolsSubjectNames(User user) {
        Integer oid = user.getOid();
        HashMap<String, Object> param = new HashMap<String, Object>();
        param.put("org", oid);
        param.put("subject1", "1413");
        param.put("subject2", "1601");
        List<TAccountantSubjectEntity> list = tAccountSubjectMapper.listBySubjects(param);
        List<String> names = new ArrayList<>();
        TAccountantSubjectEntity sub = new TAccountantSubjectEntity();
        sub.setOrg(oid);
        for (TAccountantSubjectEntity tAccountantSubjectEntity : list) {
            String subject = tAccountantSubjectEntity.getSubject();
            sub.setSubject(subject);
            List<TAccountantSubjectEntity> l = tAccountSubjectMapper.childSubject(sub);
            if (!l.isEmpty()) {
                for (TAccountantSubjectEntity subjectEntity : l) {
                    if (subjectEntity.getMaxChildSubjects().compareTo(0) > 0) {
                        getNames(subjectEntity, names);
                    } else {
                        names.add(subjectEntity.getName());
                    }
                }
            } else {
                names.add(tAccountantSubjectEntity.getName());
            }
        }

        equFixedAssetsService.generateEquipmentTools(names,user);
        return names;
    }

    /*
     * 返回1.307初始化中 会计页面需要的信息
     * 主要是操作者和操作时间
     * */
    @Override
    public TAccountantSetting initKJinfo(User user) {
        Integer org = user.getOid();
        TAccountantSetting link = new TAccountantSetting();
        link.setOrg(org);
        link = buildAccountMapper.getBuildAccountState(link);
        return link;
    }

    private void getNames(TAccountantSubjectEntity subjectEntity,List<String> names) {
        TAccountantSubjectEntity sub = new TAccountantSubjectEntity();
        sub.setOrg(subjectEntity.getOrg());
        sub.setSubject(subjectEntity.getSubject());
        List<TAccountantSubjectEntity> l = tAccountSubjectMapper.childSubject(sub);
        for (TAccountantSubjectEntity tAccountantSubjectEntity : l) {
            if (tAccountantSubjectEntity.getMaxChildSubjects().compareTo(0) > 0) {
                getNames(tAccountantSubjectEntity, names);
            } else {
                names.add(tAccountantSubjectEntity.getName());
            }
        }
    }

    @Override
    public TAccountantSubjectEntity getOneAccountSubject(String subject, Integer oid) {
        TAccountantSubjectEntity s = new TAccountantSubjectEntity();
        s.setSubject(subject);
        s.setOrg(oid);
        TAccountantSubjectEntity list = tAccountSubjectMapper.listByFirstSubject(s);
        return list;
    }

    @Override
    public HashMap subjectMessage(List subjectes, Integer oid, String subject) {
        HashMap<String, Object> param = new HashMap<String, Object>();
        param.put("org", oid);
        param.put("list", subjectes);
        List<TAccountantVoucherSubject> voucherSubjects = voucherSubjectMapper.listByVoucherSubjectes(param);
        HashMap map = new HashMap();
        if (voucherSubjects.isEmpty()) {
            TAccountantSubjectEntity listSon = getOneAccountSubject(subject,oid);
            String parent = listSon.getParent();
            TAccountantSubjectEntity listParent = getOneAccountSubject(parent,oid);
            String nameParent = listParent.getName();
            map.put("data", listSon);
            map.put("parentName", nameParent);
        }
        return map;
    }

    /*
     * 科目修改
     * */
    @Override
    public Integer updateSubjectMessagw(User user,TAccountantSubjectEntity param) {
        Integer org = user.getOid();
        String userName = user.getUserName();
        Integer userId = user.getUserID();
        param.setUpdator(userId);
        param.setUpdateName(userName);
        param.setUpdateDate(Tools.date2Str(new Date()));
        int res = tAccountSubjectMapper.updateSubject(param);
        boolean buildState = generalSubjectRecord.checkBuildState(org);
        //建账完成
        if (buildState) {
            Integer eid = generalSubjectRecord.getEstablishByOrg(org);
            //检查setting表是否有established
            if (eid == null) {
                voucherUpdate.establishCompatible(org,user.getUserID(),user.getUserName());
            }
            //修改科目之后需要在record里生成两条数据，
            generalSubjectRecord.updateSubject(param);

        }

        return res;
    }

    /*
     * 设置科目启禁用
     * */
    @Override
    public String subjectState(List subjectes, Integer oid, String state,User user) {
        HashMap<String, Object> param = new HashMap<String, Object>();
        param.put("org", oid);
        param.put("list", subjectes);
        List<TAccountantVoucherSubject> voucherSubjects = voucherSubjectMapper.listByVoucherSubjectes(param);
        String status = null;
        if(voucherSubjects.isEmpty()){
            for (int i = 0; i < subjectes.size(); i++  ) {
                TAccountantSubjectEntity s = new TAccountantSubjectEntity();
                s.setState(Byte.parseByte(state));
                s.setSubject((String) subjectes.get(i));
                s.setOrg(oid);
                int res = tAccountSubjectMapper.updateStateForSubject(s);
                if (res>0) {
                    status = "1";
                }else{
                    status = "0";
                }
            }
            Integer eid = generalSubjectRecord.getEstablishByOrg(oid);
            //检查setting表是否有established
            if (eid == null) {
                voucherUpdate.establishCompatible(oid,user.getUserID(),user.getUserName());
            }
            //设置科目启禁用时需要在record表生成记录
            generalSubjectRecord.subjectStateRecord(subjectes.get(0).toString(),oid,state,user);
        }else{
            status = "2";
        }
        return status;
    }

    @Override
    public List<TAccountantSubjectEntity> listChildSubject(String subject, Integer oid) {
        TAccountantSubjectEntity s = new TAccountantSubjectEntity();
        s.setOrg(oid);
        s.setSubject(subject);
        List<TAccountantSubjectEntity> list = tAccountSubjectMapper.childSubject(s);
        return list;
    }

    public List<TAccountantSettle> settleMessage(Integer oid, String period) {
        TAccountantSettle settle = new TAccountantSettle();
        settle.setOrg(oid);
        settle.setPeriod(period);
        List<TAccountantSettle> accountantSettle = settleMapper.getSettleByMonth(settle);
        return  accountantSettle;
    }

    @Override
    public HashMap listByTBalanceSheet(Integer oid, String beginDate, String period, Integer code,String state) {
        List<TAccountantSettle> accountantSettle = settleMessage(oid, period);
        HashMap map = new HashMap();
        int status = 1;
        List<TBalanceSheet> list = null;
        //如果已经结完账的话或者报表核查的时候可以查看资产负债表
        if (!accountantSettle.isEmpty() || "4".equals(state)) {
            String endDay = "";
            if ("4".equals(state)) {
                endDay = getPreMonthLastDay(1);
            }
            else
                endDay = accountantSettle.get(0).getEndDate();
            list = balanceSheetComputationalProcess(oid, code, beginDate,endDay , period, state);
            map.put("list", list);
            status = 1;
        }
        map.put("status", status);
        return map;
    }

    @Override
    public List<TBalanceSheet> balanceSheetComputationalProcess(Integer oid, Integer code, String beginDate,
                                                                String endDate, String period, String state) {
        StatementDef sd = new StatementDef();
        sd.setOrg(oid);
        sd.setCode(code);
        List<StatementDef> listStatementDef = statetementDefMapper.listByOrgStatementDef(sd);
        TBalanceSheet sheet = new TBalanceSheet();
        sheet.setOrg(oid);
        sheet.setPeriod(period);
        sheet.setBeginDate(beginDate);
        sheet.setEndDate(endDate);
        BigDecimal beginningBalance = null;
        if("4".equals(state)){
            sheet.setState(state);
            beginningBalance = new BigDecimal(0);
        }
        List<TBalanceSheet> list = tBalanceSheetMapper.listByTBalanceSheet(sheet);
        if (list.isEmpty()) {
            if (listStatementDef != null || listStatementDef.size() > 0) {
                TBalanceSheet lastSheet = new TBalanceSheet();
                Date date = NewDateUtils.dateFromString(period + "-01","yyyy-MM-dd");
                Date changeDate = NewDateUtils.changeMonth(date,-1);
                String lastMonth = NewDateUtils.dateToString(changeDate,"yyyy-MM");
                lastSheet.setPeriod(lastMonth);
//                lastSheet.setPeriod(getPreMonthLastDay(3));
                lastSheet.setOrg(oid);
                TAccountantSubjectEntity sub = new TAccountantSubjectEntity();
                sub.setOrg(oid);
                sub.setCode(code);
                if ("4".equals(state)) {
                    sub.setBuildState(1);
                }
                recordedData data = new recordedData();
//                String[] subs = null;
                for (StatementDef s : listStatementDef) {
                    if("4".equals(state)){
                        beginningBalance = BigDecimal.ZERO;
                    }
                    switch (s.getCellCode()) {
                        case "r2c3":
                            sub.setSubject("1001");
                            sub.setSpecialState(0);
                            BigDecimal balance1001 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            sub.setSubject("1002");
                            sub.setSpecialState(0);
                            BigDecimal balance1002 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            sub.setSubject("1012");
                            sub.setSpecialState(0);
                            BigDecimal balance1012 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            BigDecimal r2c3 = balance1001.add(balance1002).add(balance1012) == null ?
                                    BigDecimal.valueOf(0) : balance1001.add(balance1002).add(balance1012);
                            lastSheet.setCellCode("r2c3");
                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1001","1002","1012"};
                            BigDecimal r2c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);
                            if ("4".equals(state)) {
                                data.setR2c3BeginningBalance(beginningBalance);
                            }
                            sheet.setBeginningBalance(r2c3Balance);
                            data.setR2c3(r2c3);
                            sheet.setCellCode("r2c3");
                            sheet.setBalance(r2c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r3c3":
                            sub.setSubject("1101");
                            sub.setSpecialState(0);
                            BigDecimal balance1101 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR3c3BeginningBalance(beginningBalance);
                            }
                            BigDecimal r3c3 = balance1101;
                            lastSheet.setCellCode("r3c3");
                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1101"};
                            BigDecimal r3c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r3c3Balance);
                            data.setR3c3(r3c3);
                            sheet.setCellCode("r3c3");
                            sheet.setBalance(r3c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r4c3":
                            sub.setSubject("1121");
                            sub.setSpecialState(0);
                            BigDecimal balance1121 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR4c3BeginningBalance(beginningBalance);
                            }
                            BigDecimal r4c3 = balance1121;
                            lastSheet.setCellCode("r4c3");
                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1121"};
                            BigDecimal r4c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r4c3Balance);
                            data.setR4c3(r4c3);
                            sheet.setCellCode("r4c3");
                            sheet.setBalance(r4c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r5c3":
                            sub.setSubject("1122");
                            sub.setSpecialState(1);
                            BigDecimal balance1122 = getLastBalance(sub,0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getLastBalance(sub,1,period));
                            }
                            sub.setSubject("2203");
                            sub.setSpecialState(2);
                            BigDecimal balance2203 = getLastBalance(sub,0,period).multiply(new BigDecimal(-1));
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getLastBalance(sub,1,period).multiply(new BigDecimal(-1)));
                            }
                            BigDecimal r5c3 = balance1122.add(balance2203) == null ?
                                    BigDecimal.valueOf(0) : balance1122.add(balance2203);
                            lastSheet.setCellCode("r5c3");

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1122","2203"};
                            BigDecimal r5c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);
                            if ("4".equals(state)) {
                                data.setR5c3BeginningBalance(beginningBalance);
                            }
                            sheet.setBeginningBalance(r5c3Balance);
                            data.setR5c3(r5c3);
                            sheet.setCellCode("r5c3");
                            sheet.setBalance(r5c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r6c3":
                            sub.setSubject("1123");
                            sub.setSpecialState(1);
                            BigDecimal balance1123 = getLastBalance(sub,0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getLastBalance(sub,1,period));
                            }
                            sub.setSubject("2202");
                            sub.setSpecialState(2);
                            BigDecimal balance2202 = getLastBalance(sub,0,period).multiply(new BigDecimal(-1));
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getLastBalance(sub,1,period).multiply(new BigDecimal(-1)));
                            }
                            BigDecimal r6c3 = balance1123.add(balance2202) == null ?
                                    BigDecimal.valueOf(0) : balance1123.add(balance2202);
                            lastSheet.setCellCode("r6c3");

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1123","2202"};
                            BigDecimal r6c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);
                            if ("4".equals(state)) {
                                data.setR6c3BeginningBalance(beginningBalance);
                            }
                            sheet.setBeginningBalance(r6c3Balance);
                            data.setR6c3(r6c3);
                            sheet.setCellCode("r6c3");
                            sheet.setBalance(r6c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r7c3":
                            sub.setSubject("1131");
                            sub.setSpecialState(0);
                            BigDecimal balance1131 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR7c3BeginningBalance(beginningBalance);
                            }
                            BigDecimal r7c3 = balance1131;
                            lastSheet.setCellCode("r7c3");

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1131"};
                            BigDecimal r7c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r7c3Balance);
                            data.setR7c3(r7c3);
                            sheet.setCellCode("r7c3");
                            sheet.setBalance(r7c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r8c3":
                            sub.setSubject("1132");
                            sub.setSpecialState(0);
                            BigDecimal balance1132 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR8c3BeginningBalance(beginningBalance);
                            }
                            BigDecimal r8c3 = balance1132;
                            lastSheet.setCellCode("r8c3");

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1132"};
                            BigDecimal r8c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r8c3Balance);
                            data.setR8c3(r8c3);
                            sheet.setCellCode("r8c3");
                            sheet.setBalance(r8c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r9c3":
                            sub.setSubject("1221");
                            sub.setSpecialState(0);
                            BigDecimal balance1221 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR9c3BeginningBalance(beginningBalance);
                            }
                            BigDecimal r9c3 = balance1221;
                            lastSheet.setCellCode("r9c3");
                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1221"};
                            BigDecimal r9c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r9c3Balance);
                            data.setR9c3(r9c3);
                            sheet.setCellCode("r9c3");
                            sheet.setBalance(r9c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r10c3":
                            sub.setSubject("1401");
                            sub.setSpecialState(0);
                            BigDecimal balance1401 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            sub.setSubject("1402");
                            sub.setSpecialState(0);
                            BigDecimal balance1402 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            sub.setSubject("1403");
                            sub.setSpecialState(0);
                            BigDecimal balance1403 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            sub.setSubject("1404");
                            sub.setSpecialState(0);
                            BigDecimal balance1404 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            sub.setSubject("1405");
                            sub.setSpecialState(0);
                            BigDecimal balance1405 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            sub.setSubject("1408");
                            sub.setSpecialState(0);
                            BigDecimal balance1408 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            sub.setSubject("1411");
                            sub.setSpecialState(0);
                            BigDecimal balance1411 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            sub.setSubject("1412");
                            sub.setSpecialState(0);
                            BigDecimal balance1412 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            sub.setSubject("1413");
                            sub.setSpecialState(0);
                            BigDecimal balance1413 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            sub.setSubject("1407");
                            sub.setSpecialState(0);
                            BigDecimal balance1407 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            sub.setSubject("4001");
                            sub.setSpecialState(0);
                            BigDecimal balancee4001 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            sub.setSubject("4002");
                            sub.setSpecialState(0);
                            BigDecimal balancee4002 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }

                            BigDecimal r10c3 = balance1401.add(balance1402).add(balance1403).add(balance1404).
                                    add(balance1405).add(balance1408).add(balance1411).add(balance1412).
                                    add(balance1413).subtract(balance1407) == null ? BigDecimal.valueOf(0) :
                                    balance1401.add(balance1402).add(balance1403).add(balance1404).add(balance1405).
                                            add(balance1408).add(balance1411).add(balance1412).add(balance1413).
                                            add(balancee4001).add(balancee4002).subtract(balance1407);
                            lastSheet.setCellCode("r10c3");
//                            TBalanceSheet r10c3Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r10c3Balance = getBuildBeginningBalance(r10c3Sheet,sub,state);//获取年初余额
                            if ("4".equals(state)) {
                                data.setR10c3BeginningBalance(beginningBalance);
                            }
                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1401","1402","1403","1404","1405","1408","1411","1412","1413","1407","4001","4002"};
                            BigDecimal r10c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);
                            sheet.setBeginningBalance(r10c3Balance);
                            data.setR10c3(r10c3);
                            sheet.setCellCode("r10c3");
                            sheet.setBalance(r10c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r11c3":
                            sub.setSubject("1403");
                            sub.setSpecialState(0);
                            BigDecimal balancee1403 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR11c3BeginningBalance(beginningBalance);
                            }
                            BigDecimal r11c3 = balancee1403;
                            lastSheet.setCellCode("r11c3");
//                            TBalanceSheet r11c3Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r11c3Balance = getBuildBeginningBalance(r11c3Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1403"};
                            BigDecimal r11c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);
                            sheet.setBeginningBalance(r11c3Balance);
                            data.setR11c3(r11c3);
                            sheet.setCellCode("r11c3");
                            sheet.setBalance(r11c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r12c3":
                            sub.setSubject("4001");
                            sub.setSpecialState(0);
                            BigDecimal balance4001 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR12c3BeginningBalance(beginningBalance);
                            }
                            BigDecimal r12c3 = balance4001;
                            lastSheet.setCellCode("r12c3");
//                            TBalanceSheet r12c3Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r12c3Balance = getBuildBeginningBalance(r12c3Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"4001"};
                            BigDecimal r12c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r12c3Balance);
                            data.setR12c3(r12c3);
                            sheet.setCellCode("r12c3");
                            sheet.setBalance(r12c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r13c3":
                            sub.setSubject("1405");
                            sub.setSpecialState(0);
                            BigDecimal balancee1405 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR13c3BeginningBalance(beginningBalance);
                            }
                            BigDecimal r13c3 = balancee1405;
                            lastSheet.setCellCode("r13c3");
//                            TBalanceSheet r13c3Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r13c3Balance = getBuildBeginningBalance(r13c3Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1405"};
                            BigDecimal r13c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);
                            sheet.setBeginningBalance(r13c3Balance);
                            data.setR13c3(r13c3);
                            sheet.setCellCode("r13c3");
                            sheet.setBalance(r13c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r14c3":
                            sub.setSubject("1411");
                            sub.setSpecialState(0);
                            BigDecimal balancee1411 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR14c3BeginningBalance(beginningBalance);
                            }
                            BigDecimal r14c3 = balancee1411;
                            lastSheet.setCellCode("r14c3");
//                            TBalanceSheet r14c3Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r14c3Balance = getBuildBeginningBalance(r14c3Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1411"};
                            BigDecimal r14c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r14c3Balance);
                            data.setR14c3(r14c3);
                            sheet.setCellCode("r14c3");
                            sheet.setBalance(r14c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r15c3":
                            sub.setSubject("1421");
                            sub.setSpecialState(0);
                            BigDecimal balance1421 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            sub.setSubject("18010001");
                            sub.setSpecialState(0);
                            BigDecimal balance1801001 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            BigDecimal r15c3 = balance1421.add(balance1801001) == null ?
                                    BigDecimal.valueOf(0) : balance1421.add(balance1801001);
                            lastSheet.setCellCode("r15c3");
//                            TBalanceSheet r15c3Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r15c3Balance = getBuildBeginningBalance(r15c3Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1421","18010001"};
                            BigDecimal r15c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);
                            if ("4".equals(state)) {
                                data.setR15c3BeginningBalance(beginningBalance);
                            }
                            sheet.setBeginningBalance(r15c3Balance);
                            data.setR15c3(r15c3);
                            sheet.setCellCode("r15c3");
                            sheet.setBalance(r15c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r16c3":
                            BigDecimal r16c3 = data.getR2c3().add(data.getR3c3()).add(data.getR4c3()).
                                    add(data.getR5c3()).add(data.getR6c3()).add(data.getR7c3()).add(data.getR8c3()).
                                    add(data.getR9c3()).add(data.getR10c3()).add(data.getR15c3()) == null ?
                                    BigDecimal.valueOf(0) : data.getR2c3().add(data.getR3c3()).add(data.getR4c3()).
                                    add(data.getR5c3()).add(data.getR6c3()).add(data.getR7c3()).add(data.getR8c3()).
                                    add(data.getR9c3()).add(data.getR10c3()).add(data.getR15c3());
                            lastSheet.setCellCode("r16c3");
//                            TBalanceSheet r16c3Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r16c3Balance = getBuildBeginningBalance(r16c3Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{};
                            if ("4".equals(state)) {
                                beginningBalance = data.getR2c3BeginningBalance().add(data.getR3c3BeginningBalance()).add(data.getR4c3BeginningBalance()).
                                        add(data.getR5c3BeginningBalance()).add(data.getR6c3BeginningBalance()).add(data.getR7c3BeginningBalance()).add(data.getR8c3BeginningBalance()).
                                        add(data.getR9c3BeginningBalance()).add(data.getR10c3BeginningBalance()).add(data.getR15c3BeginningBalance()) == null ?
                                        BigDecimal.valueOf(0) : data.getR2c3BeginningBalance().add(data.getR3c3BeginningBalance()).add(data.getR4c3BeginningBalance()).
                                        add(data.getR5c3BeginningBalance()).add(data.getR6c3BeginningBalance()).add(data.getR7c3BeginningBalance()).add(data.getR8c3BeginningBalance()).
                                        add(data.getR9c3BeginningBalance()).add(data.getR10c3BeginningBalance()).add(data.getR15c3BeginningBalance());
                                data.setR16c3BeginningBalance(beginningBalance);
                            }
                            BigDecimal r16c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r16c3Balance);
                            data.setR16c3(r16c3);
                            sheet.setCellCode("r16c3");
                            sheet.setBalance(r16c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r18c3":
                            sub.setSubject("1501");
                            sub.setSpecialState(0);
                            BigDecimal balance1501 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR18c3BeginningBalance(beginningBalance);
                            }
                            BigDecimal r18c3 = balance1501;
                            lastSheet.setCellCode("r18c3");
//                            TBalanceSheet r18c3Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r18c3Balance = getBuildBeginningBalance(r18c3Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1501"};
                            BigDecimal r18c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);
                            sheet.setBeginningBalance(r18c3Balance);
                            data.setR18c3(r18c3);
                            sheet.setCellCode("r18c3");
                            sheet.setBalance(r18c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r19c3":
                            sub.setSubject("1511");
                            sub.setSpecialState(0);
                            BigDecimal balance1511 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR19c3BeginningBalance(beginningBalance);
                            }
                            BigDecimal r19c3 = balance1511;
                            lastSheet.setCellCode("r19c3");
//                            TBalanceSheet r19c3Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r19c3Balance = getBuildBeginningBalance(r19c3Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1511"};
                            BigDecimal r19c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);
                            sheet.setBeginningBalance(r19c3Balance);
                            data.setR19c3(r19c3);
                            sheet.setCellCode("r19c3");
                            sheet.setBalance(r19c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r20c3":
                            sub.setSubject("1601");
                            sub.setSpecialState(0);
                            BigDecimal balance1601 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR20c3BeginningBalance(beginningBalance);
                            }
                            BigDecimal r20c3 = balance1601;
                            lastSheet.setCellCode("r20c3");
//                            TBalanceSheet r20c3Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r20c3Balance = getBuildBeginningBalance(r20c3Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1601"};
                            BigDecimal r20c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r20c3Balance);
                            data.setR20c3(r20c3);
                            sheet.setCellCode("r20c3");
                            sheet.setBalance(r20c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r21c3":
                            sub.setSubject("1602");
                            sub.setSpecialState(0);
                            BigDecimal balance1602 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR21c3BeginningBalance(beginningBalance);
                            }
                            BigDecimal r21c3 = balance1602;
                            lastSheet.setCellCode("r21c3");
//                            TBalanceSheet r21c3Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r21c3Balance = getBuildBeginningBalance(r21c3Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1602"};
                            BigDecimal r21c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r21c3Balance);
                            data.setR21c3(r21c3);
                            sheet.setCellCode("r21c3");
                            sheet.setBalance(r21c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r22c3":
                            BigDecimal r22c3 = data.getR20c3().subtract(data.getR21c3()) == null ?
                                    BigDecimal.valueOf(0) : data.getR20c3().subtract(data.getR21c3());
                            lastSheet.setCellCode("r22c3");
//                            TBalanceSheet r22c3Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r22c3Balance = getBuildBeginningBalance(r22c3Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{};
                            if ("4".equals(state)) {
                                beginningBalance = data.getR20c3BeginningBalance().subtract(data.getR21c3BeginningBalance()) == null ?
                                        BigDecimal.valueOf(0) : data.getR20c3BeginningBalance().subtract(data.getR21c3BeginningBalance());
                                data.setR22c3BeginningBalance(beginningBalance);
                            }
                            BigDecimal r22c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r22c3Balance);
                            data.setR22c3(r22c3);
                            sheet.setCellCode("r22c3");
                            sheet.setBalance(r22c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r23c3":
                            sub.setSubject("1604");
                            sub.setSpecialState(0);
                            BigDecimal balance1604 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR23c3BeginningBalance(beginningBalance);
                            }
                            BigDecimal r23c3 = balance1604;
                            lastSheet.setCellCode("r23c3");
//                            TBalanceSheet r23c3Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r23c3Balance = getBuildBeginningBalance(r23c3Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1604"};
                            BigDecimal r23c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r23c3Balance);
                            data.setR23c3(r23c3);
                            sheet.setCellCode("r23c3");
                            sheet.setBalance(r23c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r24c3":
                            sub.setSubject("1605");
                            sub.setSpecialState(0);
                            BigDecimal balance1605 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR24c3BeginningBalance(beginningBalance);
                            }
                            BigDecimal r24c3 = balance1605;
                            lastSheet.setCellCode("r24c3");
//                            TBalanceSheet r24c3Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r24c3Balance = getBuildBeginningBalance(r24c3Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1605"};
                            BigDecimal r24c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);
                            sheet.setBeginningBalance(r24c3Balance);
                            data.setR24c3(r24c3);
                            sheet.setCellCode("r24c3");
                            sheet.setBalance(r24c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r25c3":
                            sub.setSubject("1606");
                            sub.setSpecialState(0);
                            BigDecimal balance1606 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR25c3BeginningBalance(beginningBalance);
                            }
                            BigDecimal r25c3 = balance1606;
                            lastSheet.setCellCode("r25c3");
//                            TBalanceSheet r25c3Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r25c3Balance = getBuildBeginningBalance(r25c3Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1606"};
                            BigDecimal r25c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);
                            sheet.setBeginningBalance(r25c3Balance);
                            data.setR25c3(r25c3);
                            sheet.setCellCode("r25c3");
                            sheet.setBalance(r25c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r26c3":
                            sub.setSubject("1621");
                            sub.setSpecialState(0);
                            BigDecimal balance1621 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            sub.setSubject("1622");
                            sub.setSpecialState(0);
                            BigDecimal balance1622 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            BigDecimal r26c3 = balance1621.subtract(balance1622) == null ?
                                    BigDecimal.valueOf(0) : balance1621.subtract(balance1622);
                            lastSheet.setCellCode("r26c3");
//                            TBalanceSheet r26c3Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r26c3Balance = getBuildBeginningBalance(r26c3Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1621","1622"};
                            if ("4".equals(state)) {
                                data.setR26c3BeginningBalance(beginningBalance);
                            }
                            BigDecimal r26c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);
                            sheet.setBeginningBalance(r26c3Balance);
                            data.setR26c3(r26c3);
                            sheet.setCellCode("r26c3");
                            sheet.setBalance(r26c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r27c3":
                            sub.setSubject("1701");
                            sub.setSpecialState(0);
                            BigDecimal balance1701 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            sub.setSubject("1702");
                            sub.setSpecialState(0);
                            BigDecimal balance1702 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            BigDecimal r27c3 = balance1701.subtract(balance1702) == null ?
                                    BigDecimal.valueOf(0) : balance1701.subtract(balance1702);
                            lastSheet.setCellCode("r27c3");
//                            TBalanceSheet r27c3Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r27c3Balance = getBuildBeginningBalance(r27c3Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1701","1702"};
                            BigDecimal r27c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);
                            if ("4".equals(state)) {
                                data.setR27c3BeginningBalance(beginningBalance);
                            }
                            sheet.setBeginningBalance(r27c3Balance);
                            data.setR27c3(r27c3);
                            sheet.setCellCode("r27c3");
                            sheet.setBalance(r27c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r28c3":
                            sub.setSubject("4301");
                            sub.setSpecialState(0);
                            BigDecimal balance4301 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            sub.setSubject("43010002");
                            sub.setSpecialState(0);
                            BigDecimal balance4301002 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            BigDecimal r28c3 = balance4301.subtract(balance4301002) == null ?
                                    BigDecimal.valueOf(0) : balance4301.subtract(balance4301002);
                            lastSheet.setCellCode("r28c3");
//                            TBalanceSheet r28c3Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r28c3Balance = getBuildBeginningBalance(r28c3Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"4301","43010002"};
                            BigDecimal r28c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);
                            if ("4".equals(state)) {
                                data.setR28c3BeginningBalance(beginningBalance);
                            }
                            sheet.setBeginningBalance(r28c3Balance);
                            data.setR28c3(r28c3);
                            sheet.setCellCode("r28c3");
                            sheet.setBalance(r28c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r29c3":
                            sub.setSubject("1801");
                            sub.setSpecialState(0);
                            BigDecimal balance1801 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            sub.setSubject("18010001");
                            sub.setSpecialState(0);
                            BigDecimal balances1801001 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            BigDecimal r29c3 = balance1801.subtract(balances1801001) == null ?
                                    BigDecimal.valueOf(0) : balance1801.subtract(balances1801001);
                            lastSheet.setCellCode("r29c3");
//                            TBalanceSheet r29c3Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r29c3Balance = getBuildBeginningBalance(r29c3Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1801","18010001"};
                            BigDecimal r29c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);
                            if ("4".equals(state)) {
                                data.setR29c3BeginningBalance(beginningBalance);
                            }
                            sheet.setBeginningBalance(r29c3Balance);
                            data.setR29c3(r29c3);
                            sheet.setCellCode("r29c3");
                            sheet.setBalance(r29c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r30c3":
                            sub.setSubject("1901");
                            sub.setSpecialState(0);
                            BigDecimal balance1901 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR30c3BeginningBalance(beginningBalance);
                            }
                            BigDecimal r30c3 = balance1901;
                            lastSheet.setCellCode("r30c3");
//                            TBalanceSheet r30c3Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r30c3Balance = getBuildBeginningBalance(r30c3Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1901"};
                            BigDecimal r30c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r30c3Balance);
                            data.setR30c3(r30c3);
                            sheet.setCellCode("r30c3");
                            sheet.setBalance(r30c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r31c3":
                            BigDecimal r31c3 = data.getR18c3().add(data.getR19c3()).add(data.getR22c3()).
                                    add(data.getR23c3()).add(data.getR24c3()).add(data.getR25c3()).
                                    add(data.getR26c3()).add(data.getR27c3()).add(data.getR28c3()).
                                    add(data.getR29c3()).add(data.getR30c3()) == null ?
                                    BigDecimal.valueOf(0) : data.getR18c3().add(data.getR19c3()).add(data.getR22c3()).
                                    add(data.getR23c3()).add(data.getR24c3()).add(data.getR25c3()).
                                    add(data.getR26c3()).add(data.getR27c3()).add(data.getR28c3()).
                                    add(data.getR29c3()).add(data.getR30c3());
                            lastSheet.setCellCode("r31c3");
//                            TBalanceSheet r31c3Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r31c3Balance = getBuildBeginningBalance(r31c3Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{};
                            if ("4".equals(state)) {

                                beginningBalance = data.getR18c3BeginningBalance().add(data.getR19c3BeginningBalance()).add(data.getR22c3BeginningBalance()).
                                        add(data.getR23c3BeginningBalance()).add(data.getR24c3BeginningBalance()).add(data.getR25c3BeginningBalance()).
                                        add(data.getR26c3BeginningBalance()).add(data.getR27c3BeginningBalance()).add(data.getR28c3BeginningBalance()).
                                        add(data.getR29c3BeginningBalance()).add(data.getR30c3BeginningBalance()) == null ?
                                        BigDecimal.valueOf(0) : data.getR18c3BeginningBalance().add(data.getR19c3BeginningBalance()).add(data.getR22c3BeginningBalance()).
                                        add(data.getR23c3BeginningBalance()).add(data.getR24c3BeginningBalance()).add(data.getR25c3BeginningBalance()).
                                        add(data.getR26c3BeginningBalance()).add(data.getR27c3BeginningBalance()).add(data.getR28c3BeginningBalance()).
                                        add(data.getR29c3BeginningBalance()).add(data.getR30c3BeginningBalance());
                                data.setR31c3BeginningBalance(beginningBalance);
                            }
                            BigDecimal r31c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r31c3Balance);
                            data.setR31c3(r31c3);
                            sheet.setCellCode("r31c3");
                            sheet.setBalance(r31c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r32c3":
                            BigDecimal r32c3 = data.getR16c3().add(data.getR31c3()) == null ?
                                    BigDecimal.valueOf(0) : data.getR16c3().add(data.getR31c3());
                            lastSheet.setCellCode("r32c3");
//                            TBalanceSheet r32c3Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r32c3Balance = getBuildBeginningBalance(r32c3Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{};
                            if ("4".equals(state)) {
                                beginningBalance = data.getR16c3BeginningBalance().add(data.getR31c3BeginningBalance()) == null ?
                                        BigDecimal.valueOf(0) : data.getR16c3BeginningBalance().add(data.getR31c3BeginningBalance());
                                data.setR32c3BeginningBalance(beginningBalance);
                            }
                            BigDecimal r32c3Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r32c3Balance);
                            data.setR32c3(r32c3);
                            sheet.setCellCode("r32c3");
                            sheet.setBalance(r32c3);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r2c7":
                            sub.setSubject("2001");
                            sub.setSpecialState(0);
                            BigDecimal balance2001 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR2c7BeginningBalance(beginningBalance);
                            }
                            BigDecimal r2c7 = balance2001;
                            lastSheet.setCellCode("r2c7");
//                            TBalanceSheet r2c7Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r2c7Balance = getBuildBeginningBalance(r2c7Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"2001"};
                            BigDecimal r2c7Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r2c7Balance);
                            data.setR2c7(r2c7);
                            sheet.setCellCode("r2c7");
                            sheet.setBalance(r2c7);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r3c7":
                            sub.setSubject("2201");
                            sub.setSpecialState(0);
                            BigDecimal balance2201 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR3c7BeginningBalance(beginningBalance);
                            }
                            BigDecimal r3c7 = balance2201;
                            lastSheet.setCellCode("r3c7");
//                            TBalanceSheet r3c7Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r3c7Balance = getBuildBeginningBalance(r3c7Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"2201"};
                            BigDecimal r3c7Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r3c7Balance);
                            data.setR3c7(r3c7);
                            sheet.setCellCode("r3c7");
                            sheet.setBalance(r3c7);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r4c7":
                            sub.setSubject("1123");
                            sub.setSpecialState(2);
                            BigDecimal balancee1123 = getLastBalance(sub,0,period).multiply(new BigDecimal(-1));
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getLastBalance(sub,1,period).multiply(new BigDecimal(-1)));
                            }
                            sub.setSubject("2202");
                            sub.setSpecialState(1);
                            BigDecimal balancee2202 = getLastBalance(sub,0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getLastBalance(sub,1,period));
                            }
                            BigDecimal r4c7 = balancee2202.add(balancee1123) == null ?
                                    BigDecimal.valueOf(0) : balancee2202.add(balancee1123);
                            lastSheet.setCellCode("r4c7");
//                            TBalanceSheet r4c7Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r4c7Balance = getBuildBeginningBalance(r4c7Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1123","2202"};
                            if ("4".equals(state)) {
                                data.setR4c7BeginningBalance(beginningBalance);
                            }
                            BigDecimal r4c7Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r4c7Balance);
                            data.setR4c7(r4c7);
                            sheet.setCellCode("r4c7");
                            sheet.setBalance(r4c7);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r5c7":
                            sub.setSubject("1122");
                            sub.setSpecialState(2);
                            BigDecimal balances1122 = getLastBalance(sub,0,period).multiply(new BigDecimal(-1));
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getLastBalance(sub,1,period).multiply(new BigDecimal(-1)));
                            }
                            sub.setSubject("2203");
                            sub.setSpecialState(1);
                            BigDecimal balances2203 = getLastBalance(sub,0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getLastBalance(sub,1,period));
                            }
                            if ("4".equals(state)) {
                                data.setR5c7BeginningBalance(beginningBalance);
                            }
                            BigDecimal r5c7 = balances2203.add(balances1122) == null ?
                                    BigDecimal.valueOf(0) : balances2203.add(balances1122);
                            lastSheet.setCellCode("r5c7");
//                            TBalanceSheet r5c7Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r5c7Balance = getBuildBeginningBalance(r5c7Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"1122","2203"};
                            BigDecimal r5c7Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r5c7Balance);
                            data.setR5c7(r5c7);
                            sheet.setCellCode("r5c7");
                            sheet.setBalance(r5c7);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r6c7":
                            sub.setSubject("2211");
                            sub.setSpecialState(0);
                            BigDecimal balance2211 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR6c7BeginningBalance(beginningBalance);
                            }
                            BigDecimal r6c7 = balance2211;
                            lastSheet.setCellCode("r6c7");
//                            TBalanceSheet r6c7Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r6c7Balance = getBuildBeginningBalance(r6c7Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"2211"};
                            BigDecimal r6c7Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r6c7Balance);
                            data.setR6c7(r6c7);
                            sheet.setCellCode("r6c7");
                            sheet.setBalance(r6c7);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r7c7":
                            sub.setSubject("2221");
                            sub.setSpecialState(0);
                            BigDecimal balance2221 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR7c7BeginningBalance(beginningBalance);
                            }
                            BigDecimal r7c7 = balance2221;
                            lastSheet.setCellCode("r7c7");
//                            TBalanceSheet r7c7Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r7c7Balance = getBuildBeginningBalance(r7c7Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"2221"};
                            BigDecimal r7c7Balance = settingBeginningBalance(beginningBalance,lastSheet,period);
                            sheet.setBeginningBalance(r7c7Balance);
                            data.setR7c7(r7c7);
                            sheet.setCellCode("r7c7");
                            sheet.setBalance(r7c7);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r8c7":
                            sub.setSubject("2231");
                            sub.setSpecialState(0);
                            BigDecimal balance2231 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR8c7BeginningBalance(beginningBalance);
                            }
                            BigDecimal r8c7 = balance2231;
                            lastSheet.setCellCode("r8c7");
//                            TBalanceSheet r8c7Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r8c7Balance = getBuildBeginningBalance(r8c7Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"2231"};
                            BigDecimal r8c7Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r8c7Balance);
                            data.setR8c7(r8c7);
                            sheet.setCellCode("r8c7");
                            sheet.setBalance(r8c7);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r9c7":
                            sub.setSubject("2232");
                            sub.setSpecialState(0);
                            BigDecimal balance2232 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR9c7BeginningBalance(beginningBalance);
                            }
                            BigDecimal r9c7 = balance2232;
                            lastSheet.setCellCode("r9c7");
//                            TBalanceSheet r9c7Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r9c7Balance = getBuildBeginningBalance(r9c7Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"2232"};
                            BigDecimal r9c7Balance = settingBeginningBalance(beginningBalance,lastSheet,period);
                            sheet.setBeginningBalance(r9c7Balance);
                            data.setR9c7(r9c7);
                            sheet.setCellCode("r9c7");
                            sheet.setBalance(r9c7);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r10c7":
                            sub.setSubject("2241");
                            sub.setSpecialState(0);
                            BigDecimal balance2241 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR10c7BeginningBalance(beginningBalance);
                            }
                            BigDecimal r10c7 = balance2241;
                            lastSheet.setCellCode("r10c7");
//                            TBalanceSheet r10c7Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r10c7Balance = getBuildBeginningBalance(r10c7Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"2241"};
                            BigDecimal r10c7Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r10c7Balance);
                            data.setR10c7(r10c7);
                            sheet.setCellCode("r10c7");
                            sheet.setBalance(r10c7);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r12c7":
                            BigDecimal r12c7 = data.getR2c7().add(data.getR3c7()).add(data.getR4c7()).
                                    add(data.getR5c7()).add(data.getR6c7()).add(data.getR7c7()).
                                    add(data.getR8c7()).add(data.getR9c7()).add(data.getR10c7()) == null ?
                                    BigDecimal.valueOf(0) : data.getR2c7().add(data.getR3c7()).add(data.getR4c7()).
                                    add(data.getR5c7()).add(data.getR6c7()).add(data.getR7c7()).
                                    add(data.getR8c7()).add(data.getR9c7()).add(data.getR10c7());
                            lastSheet.setCellCode("r12c7");
//                            TBalanceSheet r12c7Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r12c7Balance = getBuildBeginningBalance(r12c7Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{};
                            if ("4".equals(state)) {
                                beginningBalance = data.getR2c7BeginningBalance().add(data.getR3c7BeginningBalance()).add(data.getR4c7BeginningBalance()).
                                        add(data.getR5c7BeginningBalance()).add(data.getR6c7BeginningBalance()).add(data.getR7c7BeginningBalance()).
                                        add(data.getR8c7BeginningBalance()).add(data.getR9c7BeginningBalance()).add(data.getR10c7BeginningBalance()) == null ?
                                        BigDecimal.valueOf(0) : data.getR2c7BeginningBalance().add(data.getR3c7BeginningBalance()).add(data.getR4c7BeginningBalance()).
                                        add(data.getR5c7BeginningBalance()).add(data.getR6c7BeginningBalance()).add(data.getR7c7BeginningBalance()).
                                        add(data.getR8c7BeginningBalance()).add(data.getR9c7BeginningBalance()).add(data.getR10c7BeginningBalance());
                                data.setR12c7BeginningBalance(beginningBalance);
                            }
                            BigDecimal r12c7Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r12c7Balance);
                            data.setR12c7(r12c7);
                            sheet.setCellCode("r12c7");
                            sheet.setBalance(r12c7);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r14c7":
                            sub.setSubject("2501");
                            sub.setSpecialState(0);
                            BigDecimal balance2501 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR14c7BeginningBalance(beginningBalance);
                            }
                            BigDecimal r14c7 = balance2501;
                            lastSheet.setCellCode("r14c7");
//                            TBalanceSheet r14c7Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r14c7Balance = getBuildBeginningBalance(r14c7Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"2501"};
                            BigDecimal r14c7Balance = settingBeginningBalance(beginningBalance,lastSheet,period);
                            sheet.setBeginningBalance(r14c7Balance);
                            data.setR14c7(r14c7);
                            sheet.setCellCode("r14c7");
                            sheet.setBalance(r14c7);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r15c7":
                            sub.setSubject("2701");
                            sub.setSpecialState(0);
                            BigDecimal balance2701 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR15c7BeginningBalance(beginningBalance);
                            }
                            BigDecimal r15c7 = balance2701;
                            lastSheet.setCellCode("r15c7");
//                            TBalanceSheet r15c7Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r15c7Balance = getBuildBeginningBalance(r15c7Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"2701"};
                            BigDecimal r15c7Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r15c7Balance);
                            data.setR15c7(r15c7);
                            sheet.setCellCode("r15c7");
                            sheet.setBalance(r15c7);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r16c7":
                            sub.setSubject("2401");
                            sub.setSpecialState(0);
                            BigDecimal balance2401 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR16c7BeginningBalance(beginningBalance);
                            }
                            BigDecimal r16c7 = balance2401;
                            lastSheet.setCellCode("r16c7");
//                            TBalanceSheet r16c7Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r16c7Balance = getBuildBeginningBalance(r16c7Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"2401"};
                            BigDecimal r16c7Balance = settingBeginningBalance(beginningBalance,lastSheet,period);
                            sheet.setBeginningBalance(r16c7Balance);
                            data.setR16c7(r16c7);
                            sheet.setCellCode("r16c7");
                            sheet.setBalance(r16c7);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r18c7":
                            BigDecimal r18c7 = data.getR14c7().add(data.getR15c7()).add(data.getR16c7()) == null ?
                                    BigDecimal.valueOf(0) : data.getR14c7().add(data.getR15c7()).add(data.getR16c7());
                            lastSheet.setCellCode("r18c7");
//                            TBalanceSheet r18c7Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r18c7Balance = getBuildBeginningBalance(r18c7Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{};
                            if ("4".equals(state)) {
                                beginningBalance = data.getR14c7BeginningBalance().add(data.getR15c7BeginningBalance()).add(data.getR16c7BeginningBalance()) == null ?
                                        BigDecimal.valueOf(0) : data.getR14c7BeginningBalance().add(data.getR15c7BeginningBalance()).add(data.getR16c7BeginningBalance());
                                data.setR18c7BeginningBalance(beginningBalance);
                            }
                            BigDecimal r18c7Balance = settingBeginningBalance(beginningBalance,lastSheet,period);
                            sheet.setBeginningBalance(r18c7Balance);
                            data.setR18c7(r18c7);
                            sheet.setCellCode("r18c7");
                            sheet.setBalance(r18c7);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r19c7":
                            BigDecimal r19c7 = data.getR12c7().add(data.getR18c7()) == null ?
                                    BigDecimal.valueOf(0) : data.getR12c7().add(data.getR18c7());
                            lastSheet.setCellCode("r19c7");
//                            TBalanceSheet r19c7Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r19c7Balance = getBuildBeginningBalance(r19c7Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{};
                            if ("4".equals(state)) {
                                beginningBalance = data.getR12c7BeginningBalance().add(data.getR18c7BeginningBalance()) == null ?
                                        BigDecimal.valueOf(0) : data.getR12c7BeginningBalance().add(data.getR18c7BeginningBalance());
                                data.setR19c7BeginningBalance(beginningBalance);
                            }
                            BigDecimal r19c7Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r19c7Balance);
                            data.setR19c7(r19c7);
                            sheet.setCellCode("r19c7");
                            sheet.setBalance(r19c7);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r27c7":
                            sub.setSubject("3001");
                            sub.setSpecialState(0);
                            BigDecimal balance3001 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR27c7BeginningBalance(beginningBalance);
                            }
                            BigDecimal r27c7 = balance3001;
                            lastSheet.setCellCode("r27c7");
//                            TBalanceSheet r27c7Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r27c7Balance = getBuildBeginningBalance(r27c7Sheet,sub,state);//获取年初余额
                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"3001"};
                            BigDecimal r27c7Balance = settingBeginningBalance(beginningBalance,lastSheet,period);
                            sheet.setBeginningBalance(r27c7Balance);
                            data.setR27c7(r27c7);
                            sheet.setCellCode("r27c7");
                            sheet.setBalance(r27c7);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r28c7":
                            sub.setSubject("3002");
                            sub.setSpecialState(0);
                            BigDecimal balance3002 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR28c7BeginningBalance(beginningBalance);
                            }
                            BigDecimal r28c7 = balance3002;
                            lastSheet.setCellCode("r28c7");
//                            TBalanceSheet r28c7Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r28c7Balance = getBuildBeginningBalance(r28c7Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"3002"};
                            BigDecimal r28c7Balance = settingBeginningBalance(beginningBalance,lastSheet,period);

                            sheet.setBeginningBalance(r28c7Balance);
                            data.setR28c7(r28c7);
                            sheet.setCellCode("r28c7");
                            sheet.setBalance(r28c7);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r29c7":
                            sub.setSubject("3101");
                            sub.setSpecialState(0);
                            BigDecimal balance3101 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                                data.setR29c7BeginningBalance(beginningBalance);
                            }
                            BigDecimal r29c7 = balance3101;
                            lastSheet.setCellCode("r29c7");
//                            TBalanceSheet r29c7Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r29c7Balance = getBuildBeginningBalance(r29c7Sheet,sub,state);//获取年初余额

                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"3101"};
                            BigDecimal r29c7Balance = settingBeginningBalance(beginningBalance,lastSheet,period);
                            sheet.setBeginningBalance(r29c7Balance);
                            data.setR29c7(r29c7);
                            sheet.setCellCode("r29c7");
                            sheet.setBalance(r29c7);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r30c7":
                            sub.setSubject("3103");
                            sub.setSpecialState(0);
                            BigDecimal balance3103 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            sub.setSubject("3104");
                            sub.setSpecialState(0);
                            BigDecimal balance3104 = getPeriodSubjectBalance(sub, 0,period);
                            if ("4".equals(state)) {
                                beginningBalance = beginningBalance.add(getPeriodSubjectBeginningBalance(sub,0,period));
                            }
                            BigDecimal r30c7 = balance3103.add(balance3104);
                            lastSheet.setCellCode("r30c7");
//                            TBalanceSheet r30c7Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r30c7Balance = getBuildBeginningBalance(r30c7Sheet,sub,state);//获取年初余额
                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{"3103","3104"};
                            if ("4".equals(state)) {
                                data.setR30c7BeginningBalance(beginningBalance);
                            }
                            BigDecimal r30c7Balance = settingBeginningBalance(beginningBalance,lastSheet,period);
                            sheet.setBeginningBalance(r30c7Balance);
                            data.setR30c7(r30c7);
                            sheet.setCellCode("r30c7");
                            sheet.setBalance(r30c7);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r31c7":
                            BigDecimal r31c7 = data.getR27c7().add(data.getR28c7()).add(data.getR29c7()).
                                    add(data.getR30c7()) == null ? BigDecimal.valueOf(0) :
                                    data.getR27c7().add(data.getR28c7()).add(data.getR29c7()).add(data.getR30c7());
                            lastSheet.setCellCode("r31c7");
//                            TBalanceSheet r31c7Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r31c7Balance = getBuildBeginningBalance(r31c7Sheet,sub,state);//获取年初余额
                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{};
                            if ("4".equals(state)) {
                                beginningBalance = data.getR27c7BeginningBalance().add(data.getR28c7BeginningBalance()).add(data.getR29c7BeginningBalance()).
                                        add(data.getR30c7BeginningBalance()) == null ? BigDecimal.valueOf(0) :
                                        data.getR27c7BeginningBalance().add(data.getR28c7BeginningBalance()).add(data.getR29c7BeginningBalance()).add(data.getR30c7BeginningBalance());
                                data.setR31c7BeginningBalance(beginningBalance);
                            }
                            BigDecimal r31c7Balance = settingBeginningBalance(beginningBalance,lastSheet,period);
                            sheet.setBeginningBalance(r31c7Balance);
                            data.setR31c7(r31c7);
                            sheet.setCellCode("r31c7");
                            sheet.setBalance(r31c7);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                        case "r32c7":
                            BigDecimal r32c7 = data.getR19c7().add(data.getR31c7()) == null ?
                                    BigDecimal.valueOf(0) : data.getR19c7().add(data.getR31c7());
                            lastSheet.setCellCode("r32c7");
//                            TBalanceSheet r32c7Sheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//                            BigDecimal r32c7Balance = getBuildBeginningBalance(r32c7Sheet,sub,state);//获取年初余额
                            //建账初始化时年初余额是从录入的科目中来
//                            subs = new String[]{};
                            if ("4".equals(state)) {
                                beginningBalance = data.getR19c7BeginningBalance().add(data.getR31c7BeginningBalance()) == null ?
                                        BigDecimal.valueOf(0) : data.getR19c7BeginningBalance().add(data.getR31c7BeginningBalance());
                                data.setR32c7BeginningBalance(beginningBalance);
                            }
                            BigDecimal r32c7Balance = settingBeginningBalance(beginningBalance,lastSheet,period);
                            sheet.setBeginningBalance(r32c7Balance);
                            sheet.setCellCode("r32c7");
                            sheet.setBalance(r32c7);
                            tBalanceSheetMapper.insert(sheet);
                            break;
                    }
                }
            }
            list = tBalanceSheetMapper.listByTBalanceSheet(sheet);
        }
        return list;

    }

    /*返回建账时资产负债表每项的年初余额*/
    private BigDecimal settingBeginningBalance(BigDecimal beginningBalance, TBalanceSheet lastSheet,String period) {

        BigDecimal result = new BigDecimal(0);//所有贷方本年累计金额之和，用于计算初始化时
        if (beginningBalance != null) {
            result = beginningBalance;
        }
        else {
            lastSheet = tBalanceSheetMapper.getTBalanceSheet(lastSheet);
//            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
            if (period.substring(5, 7).equals("01")) {
                result = lastSheet == null ? BigDecimal.valueOf(0) : lastSheet.getBalance();
            } else {
                result = lastSheet == null ? BigDecimal.valueOf(0) : lastSheet.getBeginningBalance();
            }
        }
        return result;
    }

    /*
     * 得到某科目的所有子科目中的所有借方余额合或者贷方合
     * specialState  1-借                 2-贷
     * balanceType   0-获取期末余额合    1-获取年初余额合
     * */
    public BigDecimal getLastBalance(TAccountantSubjectEntity sub,int balanceType,String period){
        BigDecimal balance = BigDecimal.valueOf(0);
        Integer specialState = sub.getSpecialState();
        TAccountantSubjectEntity s = tAccountSubjectMapper.subjectAllMessage(sub);

        String balanceDirection = s.getBalanceDirection();
        List<TAccountantSubjectEntity> listSecondSubject = tAccountSubjectMapper.childSubject(sub);
        String childDirection = "";
        if (!listSecondSubject.isEmpty()) {
            for (TAccountantSubjectEntity ss : listSecondSubject) {
                List<TAccountantSubjectEntity> listThirdSubject = tAccountSubjectMapper.childSubject(ss);
                if (!listThirdSubject.isEmpty()) {
                    for (TAccountantSubjectEntity ts : listThirdSubject) {

                        List<TAccountantSubjectEntity> listFourthSubject = tAccountSubjectMapper.childSubject(ts);
                        if (!listFourthSubject.isEmpty()) {
                            for (TAccountantSubjectEntity fourth : listFourthSubject) {
                                childDirection = fourth.getBalanceDirection();
                                if(sub.getCode() == 1){
                                    if (balanceType == 0 ) {
                                        if (balanceDirection.equals(childDirection)) {
                                            balance = balance.add(getPeriodSubjectBalance(fourth, specialState,period));
                                        }
                                        else {
                                            balance = balance.subtract(getPeriodSubjectBalance(fourth, specialState,period));
                                        }
                                    }
                                    else {
                                        if (balanceDirection.equals(childDirection)) {
                                            balance = balance.add(getPeriodSubjectBeginningBalance(fourth, specialState,period));
                                        }
                                        else {
                                            balance = balance.subtract(getPeriodSubjectBeginningBalance(fourth, specialState,period));
                                        }
                                    }
                                } else {
                                    if (balanceDirection.equals(childDirection)) {
                                        balance = balance.add(getBalance(fourth,period));
                                    }
                                }
                            }
                        }
                        else {
                            childDirection = ts.getBalanceDirection();
                            if(sub.getCode() == 1){
                                if (balanceType == 0) {
                                    if (balanceDirection.equals(childDirection)) {
                                        balance = balance.add(getPeriodSubjectBalance(ts, specialState,period));
                                    }
                                    else {
                                        balance = balance.subtract(getPeriodSubjectBalance(ts, specialState,period));
                                    }
                                }
                                else {
                                    if (balanceDirection.equals(childDirection)) {
                                        balance = balance.add(getPeriodSubjectBeginningBalance(ts, specialState,period));
                                    }
                                    else {
                                        balance = balance.subtract(getPeriodSubjectBeginningBalance(ts, specialState,period));
                                    }
                                }
                            } else {
                                if (balanceDirection.equals(childDirection)) {
                                    balance = balance.add(getBalance(ts,period));
                                }
                                else {
                                    balance = balance.subtract(getBalance(ts,period));
                                }

                            }
                        }
                    }
                } else {
                    childDirection = ss.getBalanceDirection();
                    if(sub.getCode() == 1){
                        if (balanceType == 0) {
                            if (balanceDirection.equals(childDirection)) {
                                balance = balance.add(getPeriodSubjectBalance(ss, specialState,period));
                            }
                            else {
                                balance = balance.subtract(getPeriodSubjectBalance(ss, specialState,period));
                            }
                        }
                        else {
                            if (balanceDirection.equals(childDirection)) {
                                balance = balance.add(getPeriodSubjectBeginningBalance(ss, specialState,period));
                            }
                            else {
                                balance = balance.subtract(getPeriodSubjectBeginningBalance(ss, specialState,period));
                            }
                        }

                    } else {
                        if (balanceDirection.equals(childDirection)) {
                            balance = balance.add(getBalance(ss,period));
                        }
                        else {
                            balance = balance.subtract(getBalance(ss,period));
                        }

                    }
                }
            }
        } else {
            childDirection = s.getBalanceDirection();
            if(sub.getCode() == 1){
                if (balanceType == 0) {
                    if (balanceDirection.equals(childDirection)) {
                        balance = balance.add(getPeriodSubjectBalance(s, specialState,period));
                    }
                    else {
                        balance = balance.subtract(getPeriodSubjectBalance(s, specialState,period));
                    }
                }
                else {
                    if (balanceDirection.equals(childDirection)) {
                        balance = balance.add(getPeriodSubjectBeginningBalance(s, specialState,period));
                    }
                    else {
                        balance = balance.subtract(getPeriodSubjectBeginningBalance(s, specialState,period));
                    }
                }

            } else {
                if (balanceDirection.equals(childDirection)) {
                    balance = balance.add(getBalance(s,period));
                }
                else {
                    balance = balance.subtract(getBalance(s,period));
                }

            }
        }
        return balance;
    }

    public BigDecimal getBalance(TAccountantSubjectEntity s,String period){
        TAccountantVoucherSubject v = new TAccountantVoucherSubject();
        v.setOrg(s.getOrg());
//        v.setBillDetail(2);
//        String peroid = getPreMonthLastDay(1);

        Date date = NewDateUtils.dateFromString(period + "-01","yyyy-MM-dd");
        date = NewDateUtils.getLastTimeOfMonth(date);
        period = NewDateUtils.dateToString(date,"yyyy-MM-dd");

        v.setCreateDate(period);
        v.setSubject(s.getSubject());
        List<TAccountantVoucherSubject> subjectVoucher = voucherSubjectMapper.listByProfitSubject(v);
        BigDecimal balance = BigDecimal.valueOf(0);
        if (!subjectVoucher.isEmpty()) {
            BigDecimal credit = BigDecimal.valueOf(0);
            BigDecimal debit = BigDecimal.valueOf(0);
            for (TAccountantVoucherSubject vs : subjectVoucher) {
                if(vs.getDirection().equals("1")){
                    credit = credit.add(vs.getAmount());
                }else{
                    debit =  debit.add(vs.getAmount());
                }
            }
            if (s.getBalanceDirection().equals("1")) {
                balance = credit.subtract(debit);
            } else {
                balance = debit.subtract(credit);
            }
        }
        return balance;
    }

    public BigDecimal getPeriodSubjectBalance(TAccountantSubjectEntity s, Integer specialState,String period){
        TAccountantSubjectEntity sub = tAccountSubjectMapper.subjectAllMessage(s);
        TAccountantSubjectPeroid as = new TAccountantSubjectPeroid();
        as.setOrg(s.getOrg());
//        as.setPeriod(getPreMonthLastDay(0));
        as.setPeriod(period);
        as.setSubject(s.getSubject());
        BigDecimal balance = BigDecimal.valueOf(0);
        TAccountantSubjectPeroid per = subjectPeroidMapper.getAnnualperid(as);
        if (per != null) {
            if (per.getBalanceDirection().equals(sub.getBalanceDirection())) {
                balance = per.getBalance();
            } else {
                balance = per.getBalance().multiply(new BigDecimal(-1));
            }
        }
        if (specialState == 1) {
            if (balance.compareTo(new BigDecimal(0)) < 0) {
                balance = BigDecimal.valueOf(0);
            }
        } else if (specialState == 2){
            if (balance.compareTo(new BigDecimal(0)) > 0) {
                balance = BigDecimal.valueOf(0);
            }
        }
        return balance;
    }

    /*
     * 获取子科目中指定的借方年初余额或者贷方年初余额
     * specialState  1-借     2-贷
     * */
    public BigDecimal getPeriodSubjectBeginningBalance(TAccountantSubjectEntity s, Integer specialState,String period){
        TAccountantSubjectEntity sub = tAccountSubjectMapper.subjectAllMessage(s);
        TAccountantSubjectPeroid as = new TAccountantSubjectPeroid();
        as.setOrg(s.getOrg());
//        as.setPeriod(getPreMonthLastDay(0));
        as.setPeriod(period);
        as.setSubject(s.getSubject());
        BigDecimal balance = BigDecimal.valueOf(0);
        TAccountantSubjectPeroid per = subjectPeroidMapper.getAnnualperid(as);
        if (per != null) {
            if (per.getBeginningDirection().equals(sub.getBalanceDirection())) {
                balance = per.getBeginningBalance();
            } else {
                balance = per.getBeginningBalance().multiply(new BigDecimal(-1));
            }
        }
        if (specialState == 1) {
            if (balance.compareTo(new BigDecimal(0)) < 0) {
                balance = BigDecimal.valueOf(0);
            }
        } else if (specialState == 2){
            if (balance.compareTo(new BigDecimal(0)) > 0) {
                balance = BigDecimal.valueOf(0);
            }
        }
        return balance;
    }

    @Override
    public HashMap maoByProfitStatement(Integer oid, String beginDate, String period, Integer code,String state) {
        List<TAccountantSettle> accountantSettle = settleMessage(oid, period);
        HashMap map =new HashMap();
        int status = 1;
        List<ProfitStatement> list = null;
        if (!accountantSettle.isEmpty() || "4".equals(state)) {
            String endDay = "";
            if ("4".equals(state)) {
                endDay = getPreMonthLastDay(1);
            }
            else
                endDay = accountantSettle.get(0).getEndDate();
            list = profitStatementComputationalProcess(oid, code, beginDate, endDay, period, state);
            status = 1;
            map.put("list", list);
        }
        map.put("status", status);
        return map;
    }

    @Override
    public List<ProfitStatement> profitStatementComputationalProcess(Integer oid, Integer code, String beginDate,
                                                                     String endDate, String period, String state) {
        StatementDef sd = new StatementDef();
        sd.setCode(code);
        List<StatementDef> listStatementDef = statetementDefMapper.listByOrgStatementDef(sd);
        ProfitStatement ps = new ProfitStatement();
        ps.setOrg(oid);
        ps.setPeriod(period);
        ps.setBeginDate(beginDate);
        ps.setEndDate(endDate);
        if("4".equals(state)){
            ps.setState(state);
        }
        List<ProfitStatement> list = profitStatementMapper.listByAllProfitStatement(ps);
        if(list.isEmpty()){
            if(listStatementDef != null || listStatementDef.size() > 0) {
                ProfitStatement lastps = new ProfitStatement();

                Date date = NewDateUtils.dateFromString(period + "-01","yyyy-MM-dd");
                Date changeDate = NewDateUtils.changeMonth(date,-1);
                String lastMonth = NewDateUtils.dateToString(changeDate,"yyyy-MM");

                lastps.setPeriod(lastMonth);
                lastps.setOrg(oid);
                TAccountantSubjectEntity sub = new TAccountantSubjectEntity();
                sub.setOrg(oid);
                sub.setCode(code);
                if("4".equals(state)){
                    sub.setBuildState(1);
                }
                recordedData data = new recordedData();
                for (StatementDef s : listStatementDef) {
                    switch (s.getCellCode())
                    {
                        case "r1c2":
                            sub.setSubject("5001");
                            BigDecimal balance5001 = getLastBalance(sub,0,period);
                            sub.setSubject("5051");
                            BigDecimal balance5051 = getLastBalance(sub,0,period);
                            BigDecimal r1c2 = balance5001.add(balance5051) == null ?
                                    BigDecimal.valueOf(0) : balance5001.add(balance5051);
                            lastps.setCellCode("r1c2");
                            ProfitStatement r1c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r1c2Balance = r1c2Profit == null ? BigDecimal.valueOf(0) : r1c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r1c2);
                            }else{
                                BigDecimal r1c2year = r1c2Balance.add(r1c2);
                                ps.setAccumulative(r1c2year);
                            }
                            data.setR1c2(r1c2);
                            ps.setCellCode("r1c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r1c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"5001","5051"};
                                settingProfit(subs,oid,ps);
                                data.setR1c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r2c2":
                            sub.setSubject("5401");
                            BigDecimal balance5401 = getLastBalance(sub,0,period);
                            sub.setSubject("5402");
                            BigDecimal balance5402 = getLastBalance(sub,0,period);
                            BigDecimal r2c2 = balance5401.add(balance5402) == null ?
                                    BigDecimal.valueOf(0) : balance5401.add(balance5402);
                            lastps.setCellCode("r2c2");
                            ProfitStatement r2c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r2c2Balance = r2c2Profit == null ? BigDecimal.valueOf(0) : r2c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r2c2);
                            }else{
                                BigDecimal r2c2year = r2c2Balance.add(r2c2);
                                ps.setAccumulative(r2c2year);
                            }
                            data.setR2c2(r2c2);
                            ps.setCellCode("r2c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r2c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"5401","5402"};
                                settingProfit(subs,oid,ps);
                                data.setR2c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r3c2":
                            sub.setSubject("5403");
                            BigDecimal balance5403 = getLastBalance(sub,0,period);
                            BigDecimal r3c2 = balance5403;
                            lastps.setCellCode("r3c2");
                            ProfitStatement r3c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r3c2Balance = r3c2Profit == null ? BigDecimal.valueOf(0) : r3c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r3c2);
                            }else{
                                BigDecimal r3c2year = r3c2Balance.add(r3c2);
                                ps.setAccumulative(r3c2year);
                            }
                            data.setR3c2(r3c2);
                            ps.setCellCode("r3c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r3c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"5403"};
                                settingProfit(subs,oid,ps);
                                data.setR3c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r4c2":
                            sub.setSubject("54030001");
                            BigDecimal balance5403001 = getLastBalance(sub,0,period);
                            BigDecimal r4c2 = balance5403001;
                            lastps.setCellCode("r4c2");
                            ProfitStatement r4c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r4c2Balance = r4c2Profit == null ? BigDecimal.valueOf(0) : r4c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r4c2);
                            }else{
                                BigDecimal r4c2year = r4c2Balance.add(r4c2);
                                ps.setAccumulative(r4c2year);
                            }
                            data.setR4c2(r4c2);
                            ps.setCellCode("r4c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r4c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"54030001"};
                                settingProfit(subs,oid,ps);
                                data.setR4c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r5c2":
                            sub.setSubject("54030002");
                            BigDecimal balance5403002 = getLastBalance(sub,0,period);
                            BigDecimal r5c2 = balance5403002;
                            lastps.setCellCode("r5c2");
                            ProfitStatement r5c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r5c2Balance = r5c2Profit == null ? BigDecimal.valueOf(0) : r5c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r5c2);
                            }else{
                                BigDecimal r5c2year = r5c2Balance.add(r5c2);
                                ps.setAccumulative(r5c2year);
                            }
                            data.setR5c2(r5c2);
                            ps.setCellCode("r5c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r5c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"54030002"};
                                settingProfit(subs,oid,ps);
                                data.setR5c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r6c2":
                            sub.setSubject("54030003");
                            BigDecimal balance5403003 = getLastBalance(sub,0,period);
                            BigDecimal r6c2 = balance5403003;
                            lastps.setCellCode("r6c2");
                            ProfitStatement r6c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r6c2Balance = r6c2Profit == null ? BigDecimal.valueOf(0) : r6c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r6c2);
                            }else{
                                BigDecimal r6c2year = r6c2Balance.add(r6c2);
                                ps.setAccumulative(r6c2year);
                            }
                            data.setR6c2(r6c2);
                            ps.setCellCode("r6c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r6c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"54030003"};
                                settingProfit(subs,oid,ps);
                                data.setR6c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r7c2":
                            sub.setSubject("54030004");
                            BigDecimal balance5403004 = getLastBalance(sub,0,period);
                            BigDecimal r7c2 = balance5403004;
                            lastps.setCellCode("r7c2");
                            ProfitStatement r7c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r7c2Balance = r7c2Profit == null ? BigDecimal.valueOf(0) : r7c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r7c2);
                            }else{
                                BigDecimal r7c2year = r7c2Balance.add(r7c2);
                                ps.setAccumulative(r7c2year);
                            }
                            data.setR7c2(r7c2);
                            ps.setCellCode("r7c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r7c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"54030004"};
                                settingProfit(subs,oid,ps);
                                data.setR7c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r8c2":
                            sub.setSubject("54030005");
                            BigDecimal balance5403005 = getLastBalance(sub,0,period);
                            BigDecimal r8c2 = balance5403005;
                            lastps.setCellCode("r8c2");
                            ProfitStatement r8c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r8c2Balance = r8c2Profit == null ? BigDecimal.valueOf(0) : r8c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r8c2);
                            }else{
                                BigDecimal r8c2year = r8c2Balance.add(r8c2);
                                ps.setAccumulative(r8c2year);
                            }
                            data.setR8c2(r8c2);
                            ps.setCellCode("r8c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r8c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"54030005"};
                                settingProfit(subs,oid,ps);
                                data.setR8c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r9c2":
                            sub.setSubject("54030006");
                            BigDecimal balance5403006 = getLastBalance(sub,0,period);
                            sub.setSubject("54030007");
                            BigDecimal balance5403007 = getLastBalance(sub,0,period);
                            sub.setSubject("54030008");
                            BigDecimal balance5403008 = getLastBalance(sub,0,period);
                            sub.setSubject("54030009");
                            BigDecimal balance5403009 = getLastBalance(sub,0,period);
                            BigDecimal r9c2 = balance5403006.add(balance5403007).add(balance5403008).add(balance5403009) == null ?
                                    BigDecimal.valueOf(0) : balance5403006.add(balance5403007).add(balance5403008).add(balance5403009);
                            lastps.setCellCode("r9c2");
                            ProfitStatement r9c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r9c2Balance = r9c2Profit == null ? BigDecimal.valueOf(0) : r9c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r9c2);
                            }else{
                                BigDecimal r9c2year = r9c2Balance.add(r9c2);
                                ps.setAccumulative(r9c2year);
                            }
                            data.setR9c2(r9c2);
                            ps.setCellCode("r9c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r9c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"54030006","54030007","54030008","54030009"};
                                settingProfit(subs,oid,ps);
                                data.setR9c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r10c2":
                            sub.setSubject("54030010");
                            BigDecimal balance5403010 = getLastBalance(sub,0,period);
                            sub.setSubject("54030011");
                            BigDecimal balance5403011 = getLastBalance(sub,0,period);
                            sub.setSubject("54030012");
                            BigDecimal balance5403012 = getLastBalance(sub,0,period);
                            BigDecimal r10c2 = balance5403010.add(balance5403011).add(balance5403012) == null ?
                                    BigDecimal.valueOf(0) : balance5403010.add(balance5403011).add(balance5403012);
                            lastps.setCellCode("r10c2");
                            ProfitStatement r10c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r10c2Balance = r10c2Profit == null ? BigDecimal.valueOf(0) : r10c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r10c2);
                            }else{
                                BigDecimal r10c2year = r10c2Balance.add(r10c2);
                                ps.setAccumulative(r10c2year);
                            }
                            data.setR10c2(r10c2);
                            ps.setCellCode("r10c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r10c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"54030010","54030011","54030012"};
                                settingProfit(subs,oid,ps);
                                data.setR10c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r11c2":
                            sub.setSubject("5601");
                            BigDecimal balance5601 = getLastBalance(sub,0,period);
                            BigDecimal r11c2 = balance5601;
                            lastps.setCellCode("r11c2");
                            ProfitStatement r11c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r11c2Balance = r11c2Profit == null ? BigDecimal.valueOf(0) : r11c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r11c2);
                            }else{
                                BigDecimal r11c2year = r11c2Balance.add(r11c2);
                                ps.setAccumulative(r11c2year);
                            }
                            data.setR11c2(r11c2);
                            ps.setCellCode("r11c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r11c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"5601"};
                                settingProfit(subs,oid,ps);
                                data.setR11c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r12c2":
                            sub.setSubject("56010002");
                            BigDecimal balance5601002 = getLastBalance(sub,0,period);
                            BigDecimal r12c2 = balance5601002;
                            lastps.setCellCode("r12c2");
                            ProfitStatement r12c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r12c2Balance = r12c2Profit == null ? BigDecimal.valueOf(0) : r12c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r12c2);
                            }else{
                                BigDecimal r12c2year = r12c2Balance.add(r12c2);
                                ps.setAccumulative(r12c2year);
                            }
                            data.setR12c2(r12c2);
                            ps.setCellCode("r12c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r12c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"56010002"};
                                settingProfit(subs,oid,ps);
                                data.setR12c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r13c2":
                            sub.setSubject("56010007");
                            BigDecimal balance5601007 = getLastBalance(sub,0,period);
                            sub.setSubject("56010008");
                            BigDecimal balance5601008 = getLastBalance(sub,0,period);
                            BigDecimal r13c2 = balance5601007.add(balance5601008) == null ?
                                    BigDecimal.valueOf(0) : balance5601007.add(balance5601008);
                            lastps.setCellCode("r13c2");
                            ProfitStatement r13c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r13c2Balance = r13c2Profit == null ? BigDecimal.valueOf(0) : r13c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r13c2);
                            }else{
                                BigDecimal r13c2year = r13c2Balance.add(r13c2);
                                ps.setAccumulative(r13c2year);
                            }
                            data.setR13c2(r13c2);
                            ps.setCellCode("r13c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r13c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"56010007","56010008"};
                                settingProfit(subs,oid,ps);
                                data.setR13c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r14c2":
                            sub.setSubject("5602");
                            BigDecimal balance5602 = getLastBalance(sub,0,period);
                            BigDecimal r14c2 = balance5602;
                            lastps.setCellCode("r14c2");
                            ProfitStatement r14c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r14c2Balance = r14c2Profit == null ? BigDecimal.valueOf(0) : r14c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r14c2);
                            }else{
                                BigDecimal r14c2year = r14c2Balance.add(r14c2);
                                ps.setAccumulative(r14c2year);
                            }
                            data.setR14c2(r14c2);
                            ps.setCellCode("r14c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r14c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"5602"};
                                settingProfit(subs,oid,ps);
                                data.setR14c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r15c2":
                            sub.setSubject("56020001");
                            BigDecimal balance5602001 = getLastBalance(sub,0,period);
                            BigDecimal r15c2 = balance5602001;
                            lastps.setCellCode("r15c2");
                            ProfitStatement r15c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r15c2Balance = r15c2Profit == null ? BigDecimal.valueOf(0) : r15c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r15c2);
                            }else{
                                BigDecimal r15c2year = r15c2Balance.add(r15c2);
                                ps.setAccumulative(r15c2year);
                            }
                            data.setR15c2(r15c2);
                            ps.setCellCode("r15c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r15c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"56020001"};
                                settingProfit(subs,oid,ps);
                                data.setR15c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r16c2":
                            sub.setSubject("56020008");
                            BigDecimal balance5602008 = getLastBalance(sub,0,period);
                            BigDecimal r16c2 = balance5602008;
                            lastps.setCellCode("r16c2");
                            ProfitStatement r16c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r16c2Balance = r16c2Profit == null ? BigDecimal.valueOf(0) : r16c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r16c2);
                            }else{
                                BigDecimal r16c2year = r16c2Balance.add(r16c2);
                                ps.setAccumulative(r16c2year);
                            }
                            data.setR16c2(r16c2);
                            ps.setCellCode("r16c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r16c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"56020008"};
                                settingProfit(subs,oid,ps);
                                data.setR16c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r17c2":
                            sub.setSubject("56020009");
                            BigDecimal balance5602009 = getLastBalance(sub,0,period);
                            BigDecimal r17c2 = balance5602009;
                            lastps.setCellCode("r17c2");
                            ProfitStatement r17c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r17c2Balance = r17c2Profit == null ? BigDecimal.valueOf(0) : r17c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r17c2);
                            }else{
                                BigDecimal r17c2year = r17c2Balance.add(r17c2);
                                ps.setAccumulative(r17c2year);
                            }
                            data.setR17c2(r17c2);
                            ps.setCellCode("r17c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r17c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"56020009"};
                                settingProfit(subs,oid,ps);
                                data.setR17c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r18c2":
                            sub.setSubject("5603");
                            BigDecimal balance5603 = getLastBalance(sub,0,period);
                            BigDecimal r18c2 = balance5603;
                            lastps.setCellCode("r18c2");
                            ProfitStatement r18c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r18c2Balance = r18c2Profit == null ? BigDecimal.valueOf(0) : r18c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r18c2);
                            }else{
                                BigDecimal r18c2year = r18c2Balance.add(r18c2);
                                ps.setAccumulative(r18c2year);
                            }
                            data.setR18c2(r18c2);
                            ps.setCellCode("r18c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r18c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"5603"};
                                settingProfit(subs,oid,ps);
                                data.setR18c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r19c2":
                            sub.setSubject("56030004");
                            BigDecimal balance5603004 = getLastBalance(sub,0,period);
                            BigDecimal r19c2 = balance5603004;
                            lastps.setCellCode("r19c2");
                            ProfitStatement r19c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r19c2Balance = r19c2Profit == null ? BigDecimal.valueOf(0) : r19c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r19c2);
                            }else{
                                BigDecimal r19c2year = r19c2Balance.add(r19c2);
                                ps.setAccumulative(r19c2year);
                            }
                            data.setR19c2(r19c2);
                            ps.setCellCode("r19c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r19c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"56030004"};
                                settingProfit(subs,oid,ps);
                                data.setR19c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r20c2":
                            sub.setSubject("5111");
                            BigDecimal balance5111 = getLastBalance(sub,0,period);
                            BigDecimal r20c2 = balance5111;
                            lastps.setCellCode("r20c2");
                            ProfitStatement r20c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r20c2Balance = r20c2Profit == null ? BigDecimal.valueOf(0) : r20c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r20c2);
                            }else{
                                BigDecimal r20c2year = r20c2Balance.add(r20c2);
                                ps.setAccumulative(r20c2year);
                            }
                            data.setR20c2(r20c2);
                            ps.setCellCode("r20c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r20c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"5111"};
                                settingProfit(subs,oid,ps);
                                data.setR20c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r21c2":
                            BigDecimal r21c2 = data.getR1c2().subtract(data.getR2c2()).subtract(data.getR3c2()).
                                    subtract(data.getR11c2()).subtract(data.getR14c2()).subtract(data.getR18c2()).
                                    add(data.getR20c2()) == null ? BigDecimal.valueOf(0) : data.getR1c2().
                                    subtract(data.getR2c2()).subtract(data.getR3c2()).subtract(data.getR11c2()).
                                    subtract(data.getR14c2()).subtract(data.getR18c2()).add(data.getR20c2());
                            lastps.setCellCode("r21c2");
                            ProfitStatement r21c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r21c2Balance = r21c2Profit == null ? BigDecimal.valueOf(0) : r21c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r21c2);
                            }else{
                                BigDecimal r21c2year = r21c2Balance.add(r21c2);
                                ps.setAccumulative(r21c2year);
                            }
                            data.setR21c2(r21c2);
                            ps.setCellCode("r21c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r21c2);
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r22c2":
                            sub.setSubject("5301");
                            BigDecimal balance5301 = getLastBalance(sub,0,period);
                            BigDecimal r22c2 = balance5301;
                            lastps.setCellCode("r22c2");
                            ProfitStatement r22c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r22c2Balance = r22c2Profit == null ? BigDecimal.valueOf(0) : r22c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r22c2);
                            }else{
                                BigDecimal r22c2year = r22c2Balance.add(r22c2);
                                ps.setAccumulative(r22c2year);
                            }
                            data.setR22c2(r22c2);
                            ps.setCellCode("r22c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r22c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"5301"};
                                settingProfit(subs,oid,ps);
                                data.setR22c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r23c2":
                            sub.setSubject("53010002");
                            BigDecimal balance5301002 = getLastBalance(sub,0,period);
                            BigDecimal r23c2 = balance5301002;
                            lastps.setCellCode("r23c2");
                            ProfitStatement r23c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r23c2Balance = r23c2Profit == null ? BigDecimal.valueOf(0) : r23c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r23c2);
                            }else{
                                BigDecimal r23c2year = r23c2Balance.add(r23c2);
                                ps.setAccumulative(r23c2year);
                            }
                            data.setR23c2(r23c2);
                            ps.setCellCode("r23c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r23c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"53010002"};
                                settingProfit(subs,oid,ps);
                                data.setR23c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r24c2":
                            sub.setSubject("5711");
                            BigDecimal balance5711 = getLastBalance(sub,0,period);
                            BigDecimal r24c2 = balance5711;
                            lastps.setCellCode("r24c2");
                            ProfitStatement r24c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r24c2Balance = r24c2Profit == null ? BigDecimal.valueOf(0) : r24c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r24c2);
                            }else{
                                BigDecimal r24c2year = r24c2Balance.add(r24c2);
                                ps.setAccumulative(r24c2year);
                            }
                            data.setR24c2(r24c2);
                            ps.setCellCode("r24c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r24c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"5711"};
                                settingProfit(subs,oid,ps);
                                data.setR24c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r25c2":
                            sub.setSubject("57110003");
                            BigDecimal balance5711003 = getLastBalance(sub,0,period);
                            BigDecimal r25c2 = balance5711003;
                            lastps.setCellCode("r25c2");
                            ProfitStatement r25c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r25c2Balance = r25c2Profit == null ? BigDecimal.valueOf(0) : r25c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r25c2);
                            }else{
                                BigDecimal r25c2year = r25c2Balance.add(r25c2);
                                ps.setAccumulative(r25c2year);
                            }
                            data.setR25c2(r25c2);
                            ps.setCellCode("r25c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r25c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"57110003"};
                                settingProfit(subs,oid,ps);
                                data.setR25c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r26c2":
                            sub.setSubject("57110004");
                            BigDecimal balance5711004 = getLastBalance(sub,0,period);
                            BigDecimal r26c2 = balance5711004;
                            lastps.setCellCode("r26c2");
                            ProfitStatement r26c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r26c2Balance = r26c2Profit == null ? BigDecimal.valueOf(0) : r26c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r26c2);
                            }else{
                                BigDecimal r26c2year = r26c2Balance.add(r26c2);
                                ps.setAccumulative(r26c2year);
                            }
                            data.setR26c2(r26c2);
                            ps.setCellCode("r26c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r26c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"57110004"};
                                settingProfit(subs,oid,ps);
                                data.setR26c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r27c2":
                            sub.setSubject("57110005");
                            BigDecimal balance5711005 = getLastBalance(sub,0,period);
                            BigDecimal r27c2 = balance5711005;
                            lastps.setCellCode("r27c2");
                            ProfitStatement r27c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r27c2Balance = r27c2Profit == null ? BigDecimal.valueOf(0) : r27c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r27c2);
                            }else{
                                BigDecimal r27c2year = r27c2Balance.add(r27c2);
                                ps.setAccumulative(r27c2year);
                            }
                            data.setR27c2(r27c2);
                            ps.setCellCode("r27c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r27c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"57110005"};
                                settingProfit(subs,oid,ps);
                                data.setR27c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r28c2":
                            sub.setSubject("57110006");
                            BigDecimal balance5711006 = getLastBalance(sub,0,period);
                            BigDecimal r28c2 = balance5711006;
                            lastps.setCellCode("r28c2");
                            ProfitStatement r28c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r28c2Balance = r28c2Profit == null ? BigDecimal.valueOf(0) : r28c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r28c2);
                            }else{
                                BigDecimal r28c2year = r28c2Balance.add(r28c2);
                                ps.setAccumulative(r28c2year);
                            }
                            data.setR28c2(r28c2);
                            ps.setCellCode("r28c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r28c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"57110006"};
                                settingProfit(subs,oid,ps);
                                data.setR28c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r29c2":
                            sub.setSubject("57110007");
                            BigDecimal balance5711007 = getLastBalance(sub,0,period);
                            BigDecimal r29c2 = balance5711007;
                            lastps.setCellCode("r29c2");
                            ProfitStatement r29c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r29c2Balance = r29c2Profit == null ? BigDecimal.valueOf(0) : r29c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r29c2);
                            }else{
                                BigDecimal r29c2year = r29c2Balance.add(r29c2);
                                ps.setAccumulative(r29c2year);
                            }
                            data.setR29c2(r29c2);
                            ps.setCellCode("r29c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r29c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"57110007"};
                                settingProfit(subs,oid,ps);
                                data.setR29c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r30c2":
                            BigDecimal r30c2 = data.getR21c2().add(data.getR22c2()).subtract(data.getR24c2()) == null ?
                                    BigDecimal.valueOf(0) : data.getR21c2().add(data.getR22c2()).subtract(data.getR24c2());
                            lastps.setCellCode("r30c2");
                            ProfitStatement r30c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r30c2Balance = r30c2Profit == null ? BigDecimal.valueOf(0) : r30c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r30c2);
                            }else{
                                BigDecimal r30c2year = r30c2Balance.add(r30c2);
                                ps.setAccumulative(r30c2year);
                            }
                            data.setR30c2(r30c2);
                            ps.setCellCode("r30c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r30c2);
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r31c2":
                            sub.setSubject("5801");
                            BigDecimal balance5801 = getLastBalance(sub,0,period);
                            BigDecimal r31c2 = balance5801;
                            lastps.setCellCode("r31c2");
                            ProfitStatement r31c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r31c2Balance = r31c2Profit == null ? BigDecimal.valueOf(0) : r31c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r31c2);
                            }else{
                                BigDecimal r31c2year = r31c2Balance.add(r31c2);
                                ps.setAccumulative(r31c2year);
                            }
                            data.setR31c2(r31c2);
                            ps.setCellCode("r31c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r31c2);
                            }
                            else
                            {
                                //当state是4的时候利润表的本年累计用初始化时录入的本年累计金额计算
                                String[] subs = new String[]{"5801"};
                                settingProfit(subs,oid,ps);
                                data.setR31c2(ps.getAccumulative());
                            }
                            profitStatementMapper.insert(ps);
                            break;
                        case "r32c2":
                            BigDecimal r32c2 = data.getR30c2().subtract(data.getR31c2()) == null ?
                                    BigDecimal.valueOf(0) : data.getR30c2().subtract(data.getR31c2());
                            lastps.setCellCode("r32c2");
                            ProfitStatement r32c2Profit = profitStatementMapper.getProfitStatem(lastps);
                            BigDecimal r32c2Balance = r32c2Profit == null ? BigDecimal.valueOf(0) : r32c2Profit.getAccumulative();
//                            if (getPreMonthLastDay(0).substring(5, 7).equals("01")) {
                            if (period.substring(5, 7).equals("01")) {
                                ps.setAccumulative(r32c2);
                            }else{
                                BigDecimal r32c2year = r32c2Balance.add(r32c2);
                                ps.setAccumulative(r32c2year);
                            }
                            data.setR32c2(r32c2);
                            ps.setCellCode("r32c2");
                            if(!"4".equals(state)){
                                ps.setAmount(r32c2);
                            }
                            profitStatementMapper.insert(ps);
                            break;
                    }
                }
            }
            list = profitStatementMapper.listByAllProfitStatement(ps);
        }
        return list;
    }

    /*设置某些指定科目的利润表中的数据，将subs中的科目的贷方金额相加作为建账初始化时的利润表的本年累计金额*/
    private void settingProfit(String[] subs, Integer oid, ProfitStatement ps) {
        TAccountantSubjectEntity subjectEntity = new TAccountantSubjectEntity();
        subjectEntity.setOrg(oid);
        BigDecimal sum = new BigDecimal(0);//所有贷方本年累计金额之和，用于计算初始化时
        for (int i=0;i<subs.length;i++) {
            subjectEntity.setSubject(subs[i]);
            TAccountantSubjectEntity subject = tAccountSubjectMapper.subjectAllMessage(subjectEntity);
            if (subject != null) {
                BigDecimal debit = subject.getDebitAccumulative() == null ? BigDecimal.ZERO : subject.getDebitAccumulative();
                sum = sum.add(debit);
            }
        }
        ps.setAccumulative(sum);
    }

    @Override
    public Integer tentativeConculationstatus(User user) {
        int oid = user.getOid();

        String period = accountant1_209_service.getSettleMonth(oid);//需结账的月份
        period = period.replace("年","-").replace("月","");

        Date date = NewDateUtils.dateFromString(period + "-01","yyyy-MM-dd");
        date = NewDateUtils.getLastTimeOfMonth(date);
        String lastDay = NewDateUtils.dateToString(date,"yyyy-MM-dd");

        List<TAccountantSettle> accountantSettle = settleMessage(oid, period);

        delMessage(oid, period);

        TAccountantSettle settle = new TAccountantSettle();
        settle.setOrg(oid);
        settle.setPeriod(period);
        settle.setBeginDate(period + "-01");
        settle.setEndDate(lastDay);
        settle.setCreateDate(Tools.date2Str(new Date()));
        settle.setCreator(user.getUserID());
        settle.setCreateName(user.getUserName());
        settle.setState("1");
        settle.setNeedTrial("0");
        int status = settleMapper.insert(settle);
        TAccountantSubjectEntity as = new TAccountantSubjectEntity();
        as.setOrg(oid);
        tAccountantSubjectHistoryMapper.insertOrgSubject(as);
        settleService.trial(user);
        return status;
    }

    /*
     * state         1-试算，2-记账，3-结账
     * */
    @Override
    public Integer closeAccountsStatus(User user, String state) {
        int oid = user.getOid();
        String period = accountant1_209_service.getSettleMonth(oid);//需结账的月份
        period = period.replace("年","-").replace("月","");
        //如果是结账，需要先判断是否还有待选择的票据，如果有的话不能结账
        if (state.equals("3")) {

            String settleMonth = accountant1_209_service.getSettleMonth(oid);//需结账的月份
            Date dateFirst = NewDateUtils.dateFromString(settleMonth + "01日 00:00:00","yyyy年MM月dd日 HH:mm:ss");
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(dateFirst);
            int cur = calendar.getActualMaximum(Calendar.DATE);//当月最后一天
            calendar.set(Calendar.DATE, cur);
            Date lastTime = NewDateUtils.getLastTimeOfMonth(calendar.getTime());
            Map<String,Object> map = dataService.getNumTotal(oid,dateFirst,lastTime);//获取财务统计数据
            Integer billQuantity = (Integer) map.get("numSelected");//可做凭证的票据总数，正常待选择+修改待选择
            if (billQuantity > 0) {
                return -1;//如果结账月份还有待选择的票据，不能结账
            }

            TAccountantSubjectHistory ash = new TAccountantSubjectHistory();
            ash.setOrg(oid);
            List<TAccountantSubjectHistory> histories = tAccountantSubjectHistoryMapper.getAllHistorySubject(ash);
            for (TAccountantSubjectHistory s : histories) {
                TAccountantSubjectEntity as = new TAccountantSubjectEntity();
                as.setId(s.getSubjectId());
                as.setPreviousBalance(s.getPreviousBalance());
                as.setPreviousDirection(s.getPreviousDirection());
                as.setBalance(s.getBalance());
                as.setEndDirection(s.getEndDirection());
                as.setCreditAccumulative(s.getCreditAccumulative());
                as.setDebitAccumulative(s.getDebitAccumulative());
                as.setBalanceDirection(s.getBalanceDirection());
                as.setQuantity(s.getQuantity());
                tAccountSubjectMapper.update(as);
            }
        }

        TAccountantSettle settle = new TAccountantSettle();
        settle.setState(state);
        settle.setPeriod(period);
        settle.setOrg(oid);
        List<TAccountantSettle> listSetting = settleMapper.getSettleByMonth(settle);
        Integer flag = 0;
        if (listSetting.get(0).getNeedTrial().equals("1")) {

        }else{
            settleMapper.updateSettleState(settle);
            TAccountantSubjectPeroid sp = new TAccountantSubjectPeroid();
            sp.setOrg(oid);
            sp.setPeriod(period);
            sp.setState(state);
            subjectPeroidMapper.updatePeriodState(sp);
            flag = accountantSettleService.changeVoucherSettleState(user, 1);
        }

        return flag;
    }

    @Override
    public Integer backCloseAccountsStatus(User user) {
        int oid = user.getOid();
        String period = accountant1_209_service.getSettleMonth(oid);//需结账的月份
        period = period.replace("年","-").replace("月","");
        delMessage(oid, period);
        delVouMessage(oid,period);
        Integer flag = accountantSettleService.changeVoucherSettleState(user, 0);
        return flag;
    }

    @Override
    public TAccountantSettle settingStatus(Integer oid) {
        TAccountantSettle settle = new TAccountantSettle();
        String period = accountant1_209_service.getSettleMonth(oid);//需结账的月份
        period = period.replace("年","-").replace("月","");
        List<TAccountantSettle> accountantSettle = settleMessage(oid, period);
        if (accountantSettle.isEmpty()) {
            settle.setState("0");
            settle.setPeriod(period);
        } else {
            settle = accountantSettle.get(0);
        }
        return settle;
    }

    @Override
    public boolean carryOver(User user) {
        int oid = user.getOid();
        String period = accountant1_209_service.getSettleMonth(oid);//需结账的月份
        period = period.replace("年","-").replace("月","");
        delVouMessage(oid,period);

        //新增利润表
        String beginDate = period + "-01";

        maoByProfitStatement(oid, beginDate, period, 2,null);


        Date date = NewDateUtils.dateFromString(period + "-01","yyyy-MM-dd");
        date = NewDateUtils.getLastTimeOfMonth(date);
        String lastDay = NewDateUtils.dateToString(date,"yyyy-MM-dd");

        //获取凭证表中结转的凭证
        TAccountantVoucher vou = new TAccountantVoucher();
        vou.setOrg(oid);
        vou.setCreateDate(lastDay);
        TAccountantVoucher listVoucher = voucherMapper.getCarryOverVoucher(vou);
        if (listVoucher != null) {
            vou.setId(listVoucher.getId());
            TAccountantVoucherSubject param = new TAccountantVoucherSubject();
            param.setVoucherId(listVoucher.getId());
            List<TAccountantVoucherSubject> listCarrySubject = voucherSubjectMapper.getSubjectByVoucher(param);
            for (TAccountantVoucherSubject as : listCarrySubject) {
                TAccountantSubjectDetail detail = new TAccountantSubjectDetail();
                detail.setSubjectId(as.getId());
                detailMapper.deleteDetail(detail);
            }
            voucherSubjectMapper.deleteSubject(param);
            voucherMapper.delete(vou);
        }

        //需要调平的科目
        JSONArray subjectBorrow = new JSONArray();
        JSONArray subjectLoan = new JSONArray();
        BigDecimal creditTotal = BigDecimal.valueOf(0);
        BigDecimal debitTotal = BigDecimal.valueOf(0);

        List<TAccountantSubjectEntity> listProfitSubject = tAccountSubjectMapper.listProfitAndLossSubject(oid);
        for (TAccountantSubjectEntity a : listProfitSubject) {
            TAccountantVoucherSubject v = new TAccountantVoucherSubject();
            v.setOrg(a.getOrg());
//            v.setBillDetail(2);
            String peroid = lastDay;
            v.setCreateDate(peroid);
            v.setSubject(a.getSubject());
            BigDecimal credit = BigDecimal.valueOf(0);
            BigDecimal debit = BigDecimal.valueOf(0);
            List<TAccountantVoucherSubject> subjectVoucher = voucherSubjectMapper.listByProfitSubject(v);
            BigDecimal balance = BigDecimal.valueOf(0);
            if (!subjectVoucher.isEmpty()) {
                String subjectNames = subjectLoop(a);

                for (TAccountantVoucherSubject vs : subjectVoucher) {
                    if (vs.getDirection().equals("1")) {
                        credit = credit.add(vs.getAmount());
                        creditTotal = creditTotal.add(vs.getAmount());
                    } else {
                        debit = debit.add(vs.getAmount());
                        debitTotal = debitTotal.add(vs.getAmount());
                    }
                }
                balance = credit.subtract(debit);
                if (balance.compareTo(new BigDecimal(0)) == 1) {
                    JSONObject object = new JSONObject();
                    object.put("subject", a.getSubject());
                    object.put("price", balance);
                    object.put("subjectNames", subjectNames);
                    object.put("creditQuantity", 0);
                    object.put("debitQuantity", 0);
                    object.put("unitPrice", 0);
                    subjectLoan.put(object);
                } else if (balance.compareTo(new BigDecimal(0)) == -1) {
                    JSONObject object = new JSONObject();
                    object.put("subject", a.getSubject());
                    object.put("price", balance.negate());
                    object.put("subjectNames", subjectNames);
                    object.put("creditQuantity", 0);
                    object.put("debitQuantity", 0);
                    object.put("unitPrice", 0);
                    subjectBorrow.put(object);
                }
            }
        }

        BigDecimal balance = debitTotal.subtract(creditTotal);
        BigDecimal price = BigDecimal.valueOf(0);
        int res = balance.compareTo(new BigDecimal(0));
        boolean status = false;
        SubjectChooseParams param =  new SubjectChooseParams();
        switch (res) {
            case 1:
                JSONObject object1 = new JSONObject();
                object1.put("subject", "3103");
                object1.put("price", balance);
                object1.put("subjectNames", "本年利润");
                object1.put("creditQuantity", 0);
                object1.put("debitQuantity", 0);
                object1.put("unitPrice", 0);
                subjectLoan.put(object1);
                price = debitTotal;
                param.setSummary("结转损益");
                param.setBelong_peroid(2);
                param.setMode(1);
                param.setPrice(price.doubleValue());
                param.setCategory(1);
                param.setSubjectBorrow(subjectBorrow.toString());
                param.setSubjectLoan(subjectLoan.toString());
                param.setIs_account(1);
                param.setBill_detail(0);
                param.setMemo("");
                param.setReason("");
                param.setBill_period(2);
                param.setSource(2);
                param.setOid(oid);
                param.setUser(user);
                status = generateProfitAndLossVoucher(subjectBorrow,subjectLoan,param);
                break;
            case -1:
                JSONObject object2 = new JSONObject();
                object2.put("subject", "3103");
                object2.put("price", balance.negate());
                object2.put("subjectNames", "本年利润");
                object2.put("creditQuantity", 0);
                object2.put("debitQuantity", 0);
                object2.put("unitPrice", 0);
                subjectBorrow.put(object2);
                price = creditTotal;
                param.setSummary("结转损益");
                param.setBelong_peroid(2);
                param.setMode(1);
                param.setPrice(price.doubleValue());
                param.setCategory(1);
                param.setSubjectBorrow(subjectBorrow.toString());
                param.setSubjectLoan(subjectLoan.toString());
                param.setIs_account(1);
                param.setBill_detail(0);
                param.setMemo("");
                param.setReason("");
                param.setBill_period(2);
                param.setSource(2);
                param.setOid(oid);
                param.setUser(user);
                status = generateProfitAndLossVoucher(subjectBorrow,subjectLoan,param);
                break;
            case 0:
                int loanSize = subjectLoan.length();
                int borrowSize = subjectBorrow.length();
                if (loanSize == 0 && borrowSize == 0) {
                    status = true;
                    break;
                }
                price = debitTotal;
                //平的时候生成两个凭证，收入转一次，支出转一次
                //结转收入
                JSONArray subjectB = new JSONArray();
                JSONObject objectB = new JSONObject();
                objectB.put("subject", "3103");
                objectB.put("price", price);
                objectB.put("subjectNames", "本年利润");
                objectB.put("creditQuantity", 0);
                objectB.put("debitQuantity", 0);
                objectB.put("unitPrice", 0);
                subjectB.put(objectB);
                SubjectChooseParams paramBorrow =  new SubjectChooseParams();
                paramBorrow.setSummary("结转损益");
                paramBorrow.setBelong_peroid(2);
                paramBorrow.setMode(1);
                paramBorrow.setPrice(price.doubleValue());
                paramBorrow.setCategory(1);
                paramBorrow.setSubjectBorrow(subjectB.toString());
                paramBorrow.setSubjectLoan(subjectLoan.toString());
                paramBorrow.setIs_account(1);
                paramBorrow.setBill_detail(0);
                paramBorrow.setMemo("");
                paramBorrow.setReason("");
                paramBorrow.setBill_period(2);
                paramBorrow.setSource(2);
                paramBorrow.setOid(oid);
                paramBorrow.setUser(user);
                boolean statusB = generateProfitAndLossVoucher(subjectB,subjectLoan,paramBorrow);

                //结转支出
                JSONArray subjectL = new JSONArray();
                JSONObject objectL = new JSONObject();
                objectL.put("subject", "3103");
                objectL.put("price", price);
                objectL.put("subjectNames", "本年利润");
                objectL.put("creditQuantity", 0);
                objectL.put("debitQuantity", 0);
                objectL.put("unitPrice", 0);
                subjectL.put(objectL);
                SubjectChooseParams paramLoan =  new SubjectChooseParams();
                paramLoan.setSummary("结转损益");
                paramLoan.setBelong_peroid(2);
                paramLoan.setMode(1);
                paramLoan.setPrice(price.doubleValue());
                paramLoan.setCategory(1);
                paramLoan.setSubjectBorrow(subjectBorrow.toString());
                paramLoan.setSubjectLoan(subjectL.toString());
                paramLoan.setIs_account(1);
                paramLoan.setBill_detail(0);
                paramLoan.setMemo("");
                paramLoan.setReason("");
                paramLoan.setBill_period(2);
                paramLoan.setSource(2);
                paramLoan.setOid(oid);
                paramLoan.setUser(user);
                boolean statusL = generateProfitAndLossVoucher(subjectBorrow,subjectL,paramLoan);

                if (statusB && statusL)
                    status = true;
                else
                    status = false;

        }

        return status;
    }

    private boolean generateProfitAndLossVoucher (JSONArray subjectBorrow,JSONArray subjectLoan,SubjectChooseParams param) {
        //调一个会计录入新增凭证的方法 录入借方是3103的情况
        boolean status = voucherUpdate.checkPrice(subjectBorrow.toString(), subjectLoan.toString(),0);
        if (status) {
            JSONObject state = voucherUpdate.insertVoucher(param);
            status = state.length() > 0 ? true : false;
        }
        return status;
    }

    @Override
    public int deleteSubject(int oid, String subject) {
        TAccountantSubjectEntity subjectParam = new TAccountantSubjectEntity();
        subjectParam.setSubject(subject);
        subjectParam.setOrg(oid);
        //先找到他的父科目
        TAccountantSubjectEntity parentSubject = null;
        TAccountantSubjectEntity list = tAccountSubjectMapper.listByFirstSubject(subjectParam);
        if (list != null) {
            parentSubject = list;
        }
        int res = tAccountSubjectMapper.deleteSubject(subjectParam);
        //如果删除成功的话需要把父科目的子科目数减一
        if (res > 0) {
            if (parentSubject != null) {
                tAccountSubjectMapper.reduceChild(parentSubject);
            }
        }
        return res;
    }

    /*
     * result 0  失败，如果父科目是禁止的话不能直接禁止子科目
     * result 1  成功
     * */
    @Override
    public int checkPerentState(String subject, String state,int oid) {
        TAccountantSubjectEntity subjectParam = new TAccountantSubjectEntity();
        subjectParam.setSubject(subject.substring(0,subject.length() - 4));
        subjectParam.setOrg(oid);
        TAccountantSubjectEntity subjectList = tAccountSubjectMapper.listByFirstSubject(subjectParam);
        if (subjectList != null)
        {
            TAccountantSubjectEntity parent = subjectList;
            if(parent.getState() == 0)
            {
                return  0;
            }
            else
                return 1;
        }
        return 0;
    }

    @Override
    public int accountInitialization(Integer org,Integer userId,String userName) {
        Map<String,Object> param = new HashMap<String,Object>();
        param.put("org",org);
        param.put("update_name",userName);
        param.put("update_date",new Date());
        //初始化t_accountant_setting表，主要是关于建账和会计关联相关的初始化状态值
        //重新建账或模块初始化的时候都会调该方法，先判断setting表是否有数据，如果没有新增，如果有初始化值
        TAccountantSetting setting = subjectSelectMapper.checkOrgSettingData(org);
        if (setting != null) {//重新建账，新增建账记录,此时获取不到操作者信息，需要等真正建完帐后回去更新

            param.put("key_","kmgl");
            param.put("value_","N");
            buildAccountMapper.updateSettingByOrg(param);//初始化采购与科目关联-N

            param.put("key_","account_state");
            param.put("value_","0");
            buildAccountMapper.updateSettingByOrg(param);//初始化建账状态-0

            param.put("key_","rebuild_label");
            param.put("value_","N");
            buildAccountMapper.updateSettingByOrg(param);//初始化重新建账状态-N

            param.put("key_","operate_state");
            param.put("value_","0");
            buildAccountMapper.updateSettingByOrg(param);//初始化建账流程-0

            param.put("key_","finance_relevance_state");
            param.put("value_","N");
            buildAccountMapper.updateSettingByOrg(param);//初始化财务关联-N

            TAccountantSetting link = new TAccountantSetting();
            link.setOrg(org);
            link.setKey_("establish_id");
            TAccountantSetting res = buildAccountMapper.checkSettingKey(link);
            if (res == null) {//兼容历史机构，establish_id是新增的，setting表里没有该值的新增，已有的重置为null
                link.setValue_(null);
                buildAccountMapper.insertSettingKey(link);
            }
            else {
                param.put("key_","establish_id");
                param.put("value_",null);
                buildAccountMapper.updateSettingByOrg(param);//初始化建账ID-NULL
            }

        }
        else {//说明之前没建过帐，需要新增数据
            int addSettingStatus = subjectSelectMapper.addInitializationSetting(org);
        }

        //初始化新机构的科目
        int addSubjectStatus = tAccountSubjectMapper.addInitializationSubject(org);
        //初始化现金流量暂存表，用于计算并生成现金流量表
        int addCashStatus = tAccountantCashMapper.addInitializationCash(org);

        //判断机构是否已经存在了系统自带的税种，如果没有需要新增，有的话不作处理
        taxService.initialSystemTax(org,userId,userName);

        return 1;
    }

    /*返回1413和********科目的基本信息*/
    @Override
    public List<TAccountantSubjectEntity> getSpecifySubjectInfo(int oid) {
        List<TAccountantSubjectEntity> list = tAccountSubjectMapper.getSpecifySubjectInfo(oid);
        for (TAccountantSubjectEntity subjectEntity : list) {
            String subject = subjectEntity.getSubject();
            //得到该科目的子科目编号
            String subjectChild = firstSubject(subject,oid);
            subjectEntity.setMemo(subjectChild);
        }
        return list;
    }

    /*判断某科目名称是否重复,重复的话返回该名称对应的科目编号，不重复返回null,得在某个父科目中判断，一个名称可以出现在多个父科目下*/
    @Override
    public String checkNameDuplicate(int oid, String name,String parent) {
        //获取最新的建账ID
        TAccountantSetting link = new TAccountantSetting();
        link.setOrg(oid);
        link.setKey_("establish_id");
        link = buildAccountMapper.getSettingByKey(link);
        TAccountantSubjectEntity param = new TAccountantSubjectEntity();
        param.setOrg(oid);
        param.setName(name);
        param.setParent(parent + "%");
        if (link != null) {
            String eid = link.getValue_();
            if (eid != null) {
                param.setEstablish(Integer.valueOf(eid));
            }
        }

        param = tAccountSubjectMapper.checkNameDuplicate(param);
        if (param == null) {
            return null;
        } else {
            return param.getSubject();
        }
    }

    //获取上个月day = 1是获取山个月最后一天，例如2016-05-31；day = 0是获取到上个月，例如2016-05
    private String getPreMonthLastDay(Integer day)
    {
        String voucherDate = "";
        Calendar calendar = Calendar.getInstance();

        //上月最后一天,先将日期设置为本月的第一天，然后减去一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.DATE, -1);
        if(day == 1) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            voucherDate = sdf.format(calendar.getTime());
        }else if (day == 0) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            voucherDate = sdf.format(calendar.getTime());
        }else{
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.add(Calendar.DATE, -1);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            voucherDate = sdf.format(calendar.getTime());
        }
        return voucherDate;
    }

    //比较时间 TRUE是此时间在现在时间的上个月之后
    private Boolean  isAfter(String period)
    {
        Boolean statue = true;
        if (period == null) {
            statue = false;
        }else {
            Calendar cal = Calendar.getInstance();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            cal.set(Calendar.DAY_OF_MONTH, 1);
            cal.add(Calendar.DATE, -1);
            Calendar cal2 = Calendar.getInstance();
            try {
                Date periodDate = sdf.parse(period);
                cal2.setTime(periodDate);
                if (cal2.after(cal)) {} else {
                    statue = false;
                }
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }

        return statue;
    }

    //清除个表数据
    private void delMessage(Integer oid, String period){
        TAccountantSettle settle = new TAccountantSettle();
        settle.setPeriod(period);
        settle.setOrg(oid);
        settleMapper.delSettle(settle);
        TBalanceSheet s = new TBalanceSheet();
        s.setOrg(oid);
        s.setPeriod(period);
        tBalanceSheetMapper.delSheet(s);
        CashFlow c = new CashFlow();
        c.setOrg(oid);
        c.setPeriod(period);
        cashFlowMapper.delCashFlow(c);
        TAccountantSubjectHistory sh = new TAccountantSubjectHistory();
        sh.setOrg(oid);
        tAccountantSubjectHistoryMapper.delSubjectHistory(sh);
//        TAccountantSubjectPeroid sp = new TAccountantSubjectPeroid();
//        sp.setPeriod(period);
        QueryData qd = new QueryData();
        qd.put("org",oid);
        qd.put("period",period);
        subjectPeroidMapper.delSubjectPeriod(qd);
    }

    //清除结转录入的凭证及相关信息和利润表中数据
    public void delVouMessage(Integer oid,String period){
        //删除利润表中数据
        ProfitStatement ps = new ProfitStatement();
        ps.setOrg(oid);
        ps.setPeriod(period);
        profitStatementMapper.delProfitStatem(ps);

        Date date = NewDateUtils.dateFromString(period + "-01","yyyy-MM-dd");
        date = NewDateUtils.getLastTimeOfMonth(date);
        String lastDay = NewDateUtils.dateToString(date,"yyyy-MM-dd");

        //删除凭证相关信息
        TAccountantVoucher vou = new TAccountantVoucher();
        vou.setOrg(oid);
        vou.setCreateDate(lastDay);
        TAccountantVoucher voucher = voucherMapper.getCarryOverVoucher(vou);
        if(voucher != null){
            TAccountantVoucherSubject paramSubject = new TAccountantVoucherSubject();
            paramSubject.setVoucherId(voucher.getId());
            TAccountantSubjectDetail detailParam = new TAccountantSubjectDetail();
            TAccountantSubjectCash cashParam = new TAccountantSubjectCash();
            List<TAccountantVoucherSubject> subjects = voucherSubjectMapper.getSubjectByVoucher(paramSubject);
            voucherUpdate.deleteSubjectAndDetail(subjects,detailParam,cashParam);
            voucherMapper.delete(voucher);
        }
    }

    //循环科目获取名字
    //在原朱思绪的方法上又加了一层查找，最开始科目最多只有3层，后来改成了最多有4层
    public String subjectLoop(TAccountantSubjectEntity s){
        String subjectName = s.getName();
        if (s.getParent() != null) {
            TAccountantSubjectEntity as = new TAccountantSubjectEntity();
            as.setOrg(s.getOrg());
            as.setSubject(s.getParent());
            TAccountantSubjectEntity ss = tAccountSubjectMapper.subjectAllMessage(as);
            subjectName = ss.getName() + "-" +subjectName;
            if (ss.getParent() != null) {
                as.setSubject(ss.getParent());
                TAccountantSubjectEntity sss = tAccountSubjectMapper.subjectAllMessage(as);
                subjectName = sss.getName() + "-" +subjectName;
                if(sss.getParent() != null) {
                    as.setSubject(sss.getParent());
                    TAccountantSubjectEntity ssss = tAccountSubjectMapper.subjectAllMessage(as);
                    subjectName = ssss.getName() + "-" +subjectName;
                }
            }
        }
        return subjectName;
    }

}
