
.initialSect{margin-top:150px;}
.initialSect p{text-align:center; color: #666}
.default_img{margin:auto;width:98px;}
.default_img img{width:100%;vertical-align:middle;}
.ty-form{
    overflow: hidden;
    margin: auto;
}
.formItem{
    margin-top: 8px;
    overflow: hidden;
}
.formItem .form_label{
    float:left;
    margin-right:6px;
    vertical-align: top;
    width:60px;
    height:34px;
    line-height:34px;
}
.formItem .form_other{
    float:left;
    margin-right:4px;
    line-height:34px;
}
.formItem .small{
    width:100px;
}
.formItem .mid{
    width:150px;
}
.form_content{
    float: left;
    padding-left:12px;
    margin-right:30px;
    border:1px solid #ccc;
    width:383px;
    min-height:32px;
    line-height:32px;
}
.formItem input[type="text"], .formItem textarea{
    width: 100%;
}
.formItem select{}
.formItem input[type="radio"]{
    margin-left:80px;
}
.formItem .text-justify::after {
    display: inline;
}
.formItem pre{
    min-height:50px;
    max-height: 200px;
    overflow-y: auto;
    font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 14px;
    line-height: 1.42857;
    color: #34495e;
    background: inherit;
    word-wrap: break-word;
    white-space: pre-wrap;
}
.tip {
    font-size: 12px;
}
.center{
    margin: auto;
}
.bounce_Fixed2 {
    position: fixed;
    z-index: 10002;
    background: rgba(100, 100, 100, 0.5);
}
.mainBtnGroup, .otherBtn{
    margin-bottom: 16px;
}
.friendlyTip{
    font-size:12px;
    color: #4f81bd;
}
.item_label {
    float:left;
    width:120px;
}
.uploadImgItem{
    min-height:40px;
}
.clear:before,.clear:after{
    display:block;
    clear:both;
    content:"";
    visibility:hidden;
    height:0
}
.uploadImgItem{
    border:1px solid #dcdfe6;
    padding: 12px 20px;
}
.pictureWall{
    position: relative;
    float: left;
    margin-right:10px;
    margin-bottom:4px;
    width:80px;
    height:40px;
    overflow: hidden;
}
.pictureWall .filePic{
    height: 100%;
    background-size: 100%;
    background-position: center center;
 }
.pictureWall span{
    position: absolute;
    right: 0;
    top: -10px;
    padding: 2px;
    color: red;
    background: #eee;
    cursor: pointer;
}
.btnWrap{
    position:relative;
    width: 180px;
    margin: auto;
}
.handleBtn{
    display: none;
    line-height: 24px;
}
.handleBtn .iconfont{
    margin:0 6px;
    font-size: 26px;
    border-radius: 2px;
    line-height:24px;
    cursor: pointer;
    vertical-align: top;
    color: #707070;
}
.handleBtn .iconfont.active{
    background-color: #eee;
}
.handleBtn .iconfont:hover{
    background-color: #eee;
    color: #5d9cec;
}
.handleBtn .iconfont.disabled{
    color: #ccc;
    cursor: default;
}
.handleBtn .iconfont.disabled:hover{
    background-color: inherit;
    color: #ccc;
}

.ty-table tbody td{
    border-left: none;
    border-right: none;
}
.handleBtn ul{
    display:none;
    position:absolute;
    top:30px;
    left: 116px;
    width:120px;
    background-color: #fff;
    box-shadow: 0 2px 3px #ccc;
    text-align: center;
    line-height:30px;
    z-index: 99;
}
.handleBtn ul li{
    border-bottom:1px solid #f2f2f2;
    color: #5d9cec;
    cursor: pointer;
    font-size: 12px;
}
.handleBtn ul li:not(.li-disabled):hover{
    background-color: #f2f2f2;
}
.handleBtn ul li.li-disabled{
    color:#ccc;
    cursor: not-allowed;
}
.additional{
    color: #868686;
    text-align: right;
}
.supplyItem{
    margin-top:20px;
}
.supplyItem .delSupply{
    color:#ed5565;
    margin-right:52px;
}
#newEvent .combinSect{
    position: relative;
}
#newEvent .clearText{
    position: absolute;
    right: 10px;
    bottom: 4px;
    cursor: pointer;
    color: #48cfad;
}
.pictureWall a{
    display: none;
    background-color: rgba(93, 156, 236,.9);
    color: #fff;
    height: 18px;
    line-height:18px;
    width:36px;
    margin: 0 auto 5px;
    font-size: 12px;
    text-align: center;
    border-radius: 2px;
    cursor: pointer;
    position: absolute;
    top: 10px;
    left: 22px;
}
.pictureWall:hover a{
    display: block;
}
#picShow{
    position: fixed;
    display: inline-flex;
    justify-content: center;
    border: 1px solid #aaa;
    background: rgba(7, 7, 7, 0.64);
    z-index: 100009;
    width: 100%;
    height: 100%;
    text-align: center;
    vertical-align: middle;
    top: 0;
    left: 0;
    align-items: center;
}
#picShow img{
    margin-top:30px;
}
.uploadify-button, .uploadDeleteBtn {
    border: none;
    line-height: 12px;
    padding: 3px 6px;
    font-size: 12px;
    color: #1685ea;
    cursor: pointer;
    text-decoration: none;
    background-color: initial;
    font-weight: normal;
}
.uploadify-button:hover{
    background-color: #5d9cec;
    color: #fff;
}
.uploadDeleteBtn:hover{
    background-color: #ed5565;
    color: #fff;
}
.share_avatar{
    display: none;
    position: absolute;
    background-color: #fff;
    box-shadow: 0 2px 3px #ccc;
    z-index: 99;
}
.share_avatar .share_head{
    color: #999;
    line-height: 18px;
    padding: 8px 8px 4px 8px;
    border-bottom: 1px solid #f2f2f2;
    font-size: 12px;
}
ul.sharePerson{
    max-height: 300px;
    overflow-y: auto;
}
ul.sharePerson li{
    padding: 4px 8px;
    color: #666;
    font-size: 12px;
}
.share_avatar .share_foot{
    border-top: 1px solid #f2f2f2;
    padding: 4px 8px;
    text-align: right;
}


@-webkit-keyframes spin {
    from {
        -webkit-transform: rotate(0deg);
    }
    to {
        -webkit-transform: rotate(360deg);
    }
}
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.loading {
    z-index: 100;
    color: #3cb0a7;
    text-align: center;
    padding: 10px;
}
.loading i{
    -webkit-animation: spin 1s linear 1s 5 alternate;
    animation: spin 1s linear infinite;
}
.tblContainer{
    margin-top: 16px;
}


.ty-alert{
    font-size: 14px;
}
.btn-group{
    flex: auto;
}
.kj-table{
    width:100%;
    table-layout: auto;
    border-collapse: separate;
    border-left:1px solid #ebeef5;
    border-top:1px solid #ebeef5;
}
.kj-table.noSide {
    border-right: 1px solid #ebeef5;
}
.kj-table thead{
    background-color: #f2f2f2;
    color: #5a5c60;
    font-weight: 500;
}
.kj-table thead th, .kj-table tbody td{
    padding: 10px;
    min-width: 0;
    box-sizing: border-box;
    text-overflow: ellipsis;
    vertical-align: middle;
    position: relative;
    text-align: center;
    border-bottom: 1px solid #ebeef5;
    border-right: 1px solid #ebeef5;
    background-color: #fff;
    line-height: 24px;
}
.kj-table.noSide thead th, .kj-table.noSide tbody td{
    border-right: 1px solid rgba(0,0,0,0);
}
.kj-table thead th{
    background-color: #f4f6f9;
}
.kj-table tbody{
    font-size: 14px;
    color: #606266;
}
.kj-table tbody td a{
    cursor: pointer;
}
.ty-circle-2{
    border-radius: 2px;
}
.ty-btn{
    font-size: 12px;
}
.ty-btn-big{
    font-size: 14px;
}
.ty-search{
    border:1px solid #ccc;
    overflow: hidden;
    vertical-align: top;
}
.ty-search input{
    border: none;
    height: 30px;
    line-height: 30px;
    padding: 0 8px;
    float: left;
    outline: 0;
    width: 200px;
}
.ty-search i {
    line-height: 30px;
    color: #aaa;
    font-size: 14px;
}
#collect_tree{
    background: #fff;
    min-height: 120px;
    padding: 8px 0;
    overflow: hidden;
}
.ty-colFolderTree{  float: left;  min-width: 270px; max-width: 500px; color: #333;}
.ty-colFolderTree .ty-treeItem{  height: 30px;  line-height:30px;  vertical-align: middle;  padding:0 5px;  cursor: default;  }
.ty-colFolderTree .ty-treeItemActive{  background-color: #5d9ded; color:#fff   }
.ty-colFolderTree .ty-treeItem>i{  vertical-align: top;  color: #666;  margin: 8px 0 0 3px;  width: 15px;  height: 15px;  text-align: center;  }
.ty-colFolderTree .ty-treeItem>i.fa-folder,.ty-colFileTree .ty-treeItem>i.fa-file{  color: #ddc667; margin-right: 5px }
.ty-colFolderTree .ty-treeItem>i.fa-trash{ color: #666; margin-right: 5px}
.ty-colFolderTree .ty-treeItemActive>i.fa{  color: #fff;}

/*.ty-colFileTree .level2,.ty-colFileTree .level3,.ty-colFileTree .level4,.ty-colFileTree .level5{   }*/
.ty-colFolderTree li>div:nth-child(2){  margin-left:20px; }
.ty-rowFolderItem{
    color: #5793de;
    cursor: pointer;
}
.ty-rowFolderItem:hover{
    color: #325580;
}
.ty-rowFolderItem i{
    margin-right: 8px;
    font-size: 18px;
    display: inline-block;
    width: 15px;
    height: 19px;
    text-align: center;
}
.folderRecordList .kj-table thead th,.folderRecordList .kj-table tbody td{
    padding:10px 20px;
    text-align: left;
}

.combinSect .clearInput{
    position: absolute;
    top: 1px;
    right: 2px;
    color: #ccc;
    line-height: 30px;
    font-size: 16px;
    display: none;
}
.combinSect  input:focus + i.clearInput{
    display: block;
}
.share_avatar_row{
    background: #fff;
    border: 1px solid #dcdfe6;
    padding: 8px;
    max-height: 80px;
    overflow-y: auto;
}
.share_avatar_row li{
    display: inline-block;
}
.shareChooseList{
    margin-bottom: 8px;
}
.selected_item{
    display: inline-block;
    height: 26px;
    line-height: 26px;
    padding: 0 12px;
    background-color: #eef1f2;
    border-radius: 2px;
    font-size: 14px;
    margin-right: 2px;
}
.delRole{
    transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    display: inline-block;
    font-size: 18px;
    width: 18px;
    height: 18px;
    text-align: center;
    vertical-align: middle;
    line-height: 18px;
    cursor: pointer;
}
th.sorted.descending:after {
    font-family: "iconfont";
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: "\eb06";
}

th.sorted.ascending:after {
    font-family: "iconfont";
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    content: "\eb08";
}