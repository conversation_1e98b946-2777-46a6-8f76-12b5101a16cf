package cn.sphd.miners.modules.commodity.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.commodity.entity.PdPackagingInfo;
import cn.sphd.miners.modules.commodity.entity.PdPackagingInfoHistory;

import java.io.Serializable;

public interface PdPackagingInfoHistoryDao  extends IBaseDao<PdPackagingInfoHistory, Serializable> {

    //添加历史记录
    PdPackagingInfoHistory insert(PdPackagingInfo pdPackagingInfo);
}
