package cn.sphd.miners.modules.dmmSchedule.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmFavorite;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmFavoriteSchedule;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmSchedule;
import cn.sphd.miners.modules.dmmSchedule.service.DmmFavoriteService;
import cn.sphd.miners.modules.dmmSchedule.service.DmmScheduleService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;

@Controller
@RequestMapping("/dmmScheduleFavorite")
public class DmmScheduleFavoriteController {

    @Autowired
    DmmScheduleService dmmScheduleService;
    @Autowired
    DmmFavoriteService dmmFavoriteService;
    @Autowired
    UserService userService;

    /**
     * <AUTHOR>
     * @Date 2023/3/10
     * 新建 一级收藏夹  或 子级收藏夹
     */
    @ResponseBody
    @RequestMapping("/addDmmScheduleFavorite.do")
    public JsonResult addScheduleFavorite(AuthAcc acc, Integer parentId, String name){
        Map<String,Object> map=new HashMap<>();
        if (name!=null&&!name.isEmpty()) {
            DmmFavorite dmmFavorite = new DmmFavorite();
            List<DmmFavorite> dmmFavoriteList;
            if (parentId!=null&&parentId!=0){
                DmmFavorite parentFavorite=dmmFavoriteService.getDmmFavoriteById(parentId);
                if (parentFavorite.getDmmFavoriteScheduleHashSet().size()>0){
                    map.put("state",3); // 父菜单下有 日程关联，不能新增子级收藏夹
                    return new JsonResult(1,map);
//                    return 3;// 父菜单下有 日程关联，不能新增子级收藏夹
                }
                dmmFavoriteList=dmmFavoriteService.getFavoritesByPid(parentId);
                dmmFavorite.setLevel(parentFavorite.getLevel()+1);
                dmmFavorite.setPath(parentFavorite.getPath()+parentId);
                dmmFavorite.setFavorite(parentFavorite);
            }else {
                dmmFavorite.setLevel(1);
                dmmFavorite.setPath("/");
                dmmFavoriteList=dmmFavoriteService.getFirstFavorites(acc.getId());
            }

            for (DmmFavorite m:dmmFavoriteList){
                if (m.getName().equals(name)){
                    map.put("state",2); // 同层文件夹不能重名
                    return new JsonResult(1,map);
//                    return 2; // 同层文件夹不能重名
                }
            }

            dmmFavorite.setAccId(acc.getId());
            dmmFavorite.setName(name);
//            dmmFavorite.setCreator(user.getUserID());
            dmmFavorite.setCreateName(acc.getName());
            dmmFavorite.setCreateTime(new Date());
            dmmFavorite.setVersionNo(0);
            dmmFavorite.setScheduleNum(0);
            dmmFavoriteService.saveDmmFavorite(dmmFavorite);
//            return 1;
            map.put("state",1);
            map.put("dmmFavorite",dmmFavorite);
            return new JsonResult(1,map);
        }
        map.put("state",0);
        return new JsonResult(1,map);
//        return 0;
    }

    /**
     * <AUTHOR>
     * @Date 2023/3/10
     * 修改收藏夹名称
     */
    @ResponseBody
    @RequestMapping("/updateDmmScheduleFavorite.do")
    public Integer updateScheduleFavorite(AuthAcc acc,Integer id, String name){
//        Map<String,Object> map=new HashMap<>();
        if (name!=null&&!name.isEmpty()&&id!=null) {
            DmmFavorite dmmFavorite=dmmFavoriteService.getDmmFavoriteById(id);
            List<DmmFavorite> dmmFavoriteList;
            if (dmmFavorite.getParentId()!=null){
                dmmFavoriteList=dmmFavoriteService.getFavoritesByPid(dmmFavorite.getParentId());
            }else {
                dmmFavoriteList=dmmFavoriteService.getFirstFavorites(acc.getId());
            }
            for (DmmFavorite m:dmmFavoriteList){
                if (m.getName().equals(name)&&!m.getId().equals(id)){
//                    map.put("state",2); // 同层文件夹不能重名
//                    return new JsonResult(1,new JsonResult());
                    return 2;// 同层文件夹不能重名
                }
            }
            dmmFavorite.setName(name);
//            dmmFavorite.setUpdator(user.getUserID());
            dmmFavorite.setUpdateName(acc.getName());
            dmmFavorite.setUpdateTime(new Date());
            dmmFavorite.setVersionNo(dmmFavorite.getVersionNo()+1);
            dmmFavoriteService.updateDmmFavorite(dmmFavorite);
            return 1;//成功
        }
        return 0;
    }


    /**
     * <AUTHOR>
     * @Date 2023/3/10
     * 删除收藏夹（有备忘不能删除，有子文件夹可以删）
     */
    @ResponseBody
    @RequestMapping("/deleteDmmScheduleFavorite.do")
    public Integer deleteScheduleFavorite(Integer id){
        if (id!=null) {
            DmmFavorite dmmFavorite = dmmFavoriteService.getDmmFavoriteById(id);
            List<Integer> favoriteIds=dmmFavoriteService.getFavoriteIdsByPath("/"+id+"/"); // 子收藏夹 ids
            favoriteIds.add(id);//本收藏夹
            Integer counts= dmmFavoriteService.getCountsByFavoriteIds(favoriteIds);//关联的 备忘数量
            if (counts>0){
                return 2; //收藏夹下 有日程 不能删除
            }
            dmmFavoriteService.deleteDmmFavorite(dmmFavorite);
            return 1;// 成功
        }
        return 0;

    }

    /**
     * <AUTHOR>
     * @Date 2023/3/10
     * 收藏功能获取收藏夹列表接口
     */
    @ResponseBody
    @RequestMapping("/getDmmFirstFavorites.do")
    public JsonResult getFirstFavorites(AuthAcc acc){
        List<DmmFavorite> dmmFavoriteList = new ArrayList<>();
        if (acc!=null) {
            dmmFavoriteList = dmmFavoriteService.getFirstFavorites(acc.getId());
            for (DmmFavorite dmmFavorite:dmmFavoriteList){
                Integer sum= scheduleSum(dmmFavorite.getDmmFavoriteHashSet(),dmmFavorite.getDmmFavoriteScheduleHashSet().size());
                dmmFavorite.setScheduleNum(sum);
            }

        }
        return new JsonResult(1,dmmFavoriteList);
    }

    private Integer scheduleSum(Set<DmmFavorite> dmmFavoriteList, Integer sum){
//        Integer a=0;
        for (DmmFavorite dmmFavorite:dmmFavoriteList){
           if (dmmFavorite.getDmmFavoriteHashSet().size()>0){
               sum+=scheduleSum(dmmFavorite.getDmmFavoriteHashSet(),0);
//               DmmFavorite.setScheduleNum(sum);
           }else {
               dmmFavorite.setScheduleNum(dmmFavorite.getDmmFavoriteScheduleHashSet().size());
               sum+=dmmFavorite.getScheduleNum();
           }
        }
        return sum;
    }

    /**
     * <AUTHOR>
     * @Date 2023/3/10
     * 点击收藏夹 获取子收藏夹列表
     */
    @ResponseBody
    @RequestMapping("/getDmmSonFavorites.do")
    public JsonResult getSonFavorites(AuthAcc acc,Integer id){
        List<DmmFavorite> dmmFavoriteList = new ArrayList<>();
        if (acc!=null) {//验证登陆过
            dmmFavoriteList = dmmFavoriteService.getFavoritesByPid(id);

            for (DmmFavorite dmmFavorite:dmmFavoriteList){
                Integer sum= scheduleSum(dmmFavorite.getDmmFavoriteHashSet(),dmmFavorite.getDmmFavoriteScheduleHashSet().size());
                dmmFavorite.setScheduleNum(sum);
            }
        }
        return new JsonResult(1,dmmFavoriteList);
    }

    /**
     * <AUTHOR>
     * @Date 2023/3/10
     * 点击收藏夹 获取备忘列表
     */
    @ResponseBody
    @RequestMapping("/getDmmFavoriteSchedules.do")
    public JsonResult getDmmFavoriteSchedules(AuthAcc acc,Integer id){
        List<DmmSchedule> dmmFavoriteList = new ArrayList<>();
        if (acc!=null&&id!=null) {//验证登陆过
            dmmFavoriteList = dmmFavoriteService.getDmmSchedulesByFavoriteId(acc.getId(),id);
        }
        return new JsonResult(1,dmmFavoriteList);
    }

    /**
     * <AUTHOR>
     * @Date 2023/3/10
     * 转移至其他收藏夹
     */
    @ResponseBody
    @RequestMapping("/moveToOtherDmmFavorite.do")
    public Integer moveToOtherFavorite(AuthAcc acc,Integer oldFavoriteId,Integer newFavoriteId,Integer scheduleId){
        if (acc!=null&&oldFavoriteId!=null&&newFavoriteId!=null&&scheduleId!=null) {//验证登陆过
            DmmFavoriteSchedule dmmFavoriteSchedule = dmmFavoriteService.getDmmFavoriteScheduleByScheduleId(acc.getId(),oldFavoriteId,scheduleId);
            if (dmmFavoriteSchedule==null){
                return 2;// 该日程在本收藏夹中不存在 无法移动
            }
            DmmFavorite newDmmFavorite=dmmFavoriteService.getDmmFavoriteById(newFavoriteId);
            if (newDmmFavorite==null){
                return 3;// 新收藏夹并不存在，无法移动
            }

            if (newDmmFavorite.getDmmFavoriteHashSet().size()>0){
                return 4;// 新收藏夹下有子收藏夹，无法复制
            }

            dmmFavoriteSchedule.setFavorite(newDmmFavorite);
//            dmmFavoriteSchedule.setUpdator(user.getUserID());
            dmmFavoriteSchedule.setUpdateTime(new Date());
            dmmFavoriteSchedule.setUpdateName(acc.getName());
            dmmFavoriteService.updateDmmFavoriteSchedule(dmmFavoriteSchedule);
            return 1;// 移动成功
        }
        return 0;//失败 缺少传值
    }

    /**
     * <AUTHOR>
     * @Date 2023/3/10
     * 收藏至其他收藏夹
     */
    @ResponseBody
    @RequestMapping("/collectToOtherDmmFavorite.do")
    public Integer collectToOtherFavorite(AuthAcc acc,Integer newFavoriteId,Integer scheduleId) {
        if (acc != null && newFavoriteId != null && scheduleId != null) {//验证登陆过

            DmmFavoriteSchedule dmmFavoriteSchedule = dmmFavoriteService.getDmmFavoriteScheduleByScheduleId(acc.getId(), newFavoriteId, scheduleId);
            if (dmmFavoriteSchedule != null) {
                return 2; // 该收藏夹中已存在，无法再次收藏
            }
            DmmFavorite dmmFavorite = dmmFavoriteService.getDmmFavoriteById(newFavoriteId);
            if (dmmFavorite==null){
                return 3;// 新收藏夹并不存在，无法复制
            }

            if (dmmFavorite.getDmmFavoriteHashSet().size()>0){
                return 4;// 新收藏夹下有子收藏夹，无法复制
            }

            DmmSchedule dmmSchedule = dmmScheduleService.getDmmScheduleById(scheduleId);
            dmmFavoriteSchedule = new DmmFavoriteSchedule();
            dmmFavoriteSchedule.setCreateTime(new Date());
            dmmFavoriteSchedule.setCreateName(acc.getName());
//            dmmFavoriteSchedule.setCreator(user.getUserID());
            dmmFavoriteSchedule.setFavorite(dmmFavorite);
            dmmFavoriteSchedule.setSchedule(dmmSchedule);
            dmmFavoriteSchedule.setTitle(dmmSchedule.getTitle());
            dmmFavoriteService.saveDmmFavoriteSchedule(dmmFavoriteSchedule);
            return 1;//复制成功
        }
        return 0;//失败 缺少传值
    }

    /**
     * <AUTHOR>
     * @Date 2023/3/10
     * 仅从本收藏夹中移除（删掉本条关联）
     */
    @ResponseBody
    @RequestMapping("/deleteDmmFavoriteSchedule.do")
    public Integer deleteDmmFavoriteSchedule(AuthAcc acc,Integer favoriteId,Integer scheduleId){
        if (acc != null && favoriteId != null && scheduleId != null) {//验证登陆过
            DmmFavoriteSchedule dmmFavoriteSchedule = dmmFavoriteService.getDmmFavoriteScheduleByScheduleId(acc.getId(), favoriteId, scheduleId);
            if(dmmFavoriteSchedule!=null){
                dmmFavoriteService.deleteDmmFavoriteSchedule(dmmFavoriteSchedule);
                return 1;
            }
            return 2;// 要移除的不存在
        }
        return 0;//失败 缺少传值
    }

    /**
     * <AUTHOR>
     * @Date 2023/3/10
     * 从全部收藏夹中移除（删掉全部关联）
     */
    @ResponseBody
    @RequestMapping("/deleteAllDmmFavoriteSchedule.do")
    public Integer deleteAllFavoriteSchedule(AuthAcc acc,Integer scheduleId){
        if (acc != null && scheduleId != null) {//验证登陆过
            List<DmmFavoriteSchedule> dmmFavoriteScheduleList=dmmFavoriteService.getDmmFavoriteSchedulesByScheduleId(scheduleId);
            if (dmmFavoriteScheduleList.size()>0) {
                dmmFavoriteService.deleteAllDmmFavoriteSchedule(dmmFavoriteScheduleList);
            }
            return 1;
        }
        return 0;//失败 缺少传值
    }


}
