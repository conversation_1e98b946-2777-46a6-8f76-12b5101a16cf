package cn.sphd.miners.modules.accountant.service;

//import cn.sphd.miners.common.utils.AnyException;

import cn.sphd.miners.modules.accountant.entity.TAccountantSettle;
import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectHistory;
import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectPeroid;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.JSONObject;

import java.util.List;

/**
 * Created by root on 17-2-8.
 */
public interface AccountantSettleService {

    String getLastSettleTime(int oid);

    int changeVoucherSettleState(User user, int state);

    int canselsSettleAccount(Integer oid);

    int initializationKjMonth();

    void trial(User user);

    List<TAccountantSettle> listPage();

    int clickTaxBtn(User user, String taxDate);

    int updateMessage(Integer mid);

    TAccountantSubjectPeroid newParentPeroid(String subLevel2, TAccountantSubjectHistory subjectLevel2, TAccountantSubjectPeroid subjectPeroid, List<Integer> subjectids,String state);

    TAccountantSubjectPeroid accumulationThisPeroid(TAccountantSubjectPeroid level2, TAccountantSubjectPeroid subjectPeroid, TAccountantSubjectHistory subjectLevel2,String state);

    JSONObject getSubjectBalanceRealTime(int org);
}
