/*creator: 李玉婷，2021-10-06 08:36:52*/
.queryItem {  display: inline-block;  margin-left: 4%;  }
.queryItem select{  background: #fff;border: 1px solid #8f8f9d;  border-radius: 4px;  }

.ssTtl{
    margin-right: 10px;
}
@media screen and (max-width: 1367px) {
    .ty-btn-big{
        padding: 2px 12px;
        height: inherit;
        line-height: 1.5;
        font-size: 12px;
    }
    .kj-input, .kj-select{
        height: 22px;
    }
    .ty-alert{
        min-width: inherit;
        font-size: 12px;
    }
    .table-fixed-avatar {
        height: 450px;
        overflow-y: auto;
    }
}
.lineQuery{
    margin-bottom: 20px;
    line-height: 23px;
}
.conon1 {
    padding: 10px 0;
}

.conon1 > a, .concon > a:hover {
    color: #909090;
    font-family: "方正兰亭超细黑简体";
    font-weight: 700;
}

.conon1 > p, .conon1 > p.a:hover {
    display: inline-block;
    padding: 0 5px;
    font-weight: 700;
}

.conon1 > a:nth-child(2), .conon1 > a:nth-child(2):hover {
    color: black;
    font-weight: lighter;
    text-decoration: none;
}
.xing:before {
    content: "*";
    width: 5px;
    height: 5px;
    display: inline-block;
    color: red;
    font-weight: bolder;
    position: relative;
}

.elemFlex {
    display: flex;
    justify-content: space-between;
    padding: 26px 0 8px 0;
}
.bg-yellow thead td {
    border-color: #fff0;
    background-color: #fff2cc;
}

.keywordSearch input,.bonceCon .keywordSearch input {
    padding: 0 10px;
    font-size: 12px;
    min-width: 200px;
    height: 24px;
    line-height: 24px;
    border-radius: 4px 0 0 4px;
    border: 1px solid #bbb;
}

input::placeholder, textarea::placeholder {
    color: #aaa;
}

.ty-color-redOrange {
    color: #c65911;
}

div.Con > p {
    font-weight: 400;
    font-size: 24px;
    padding: 0 15px;
    margin-top: 25px;
}

/*左面*/
.Btop {
    padding: 10px 0 10px 15px;
    background: #bdd7ee;
}

.Btop > span {
    font-weight: bold;
}

.faceul {
    min-height: 400px;
    list-style-type: none;
}

.faceul1:hover {
    background: #eaeaea;
}

.faceul1 {
    height: 35px;
    line-height: 35px;
}

.faceul1 > a {
    color: #0069b4;
    text-decoration: none;
    display: block;
    padding: 0 0 0 26px;
}

.bottomTree .faceul1 > a {
    font-size: 12px;
}

.back-btn, .back-btn:hover {
    font-weight: 700;
    font-size: 14px;
    color: black;
}

.back-b {
    padding: 50px 0 20px 30px;
}

.add-b > a {
    color: #bf8f00;
}

.add-b > a:hover {
    text-decoration: none;
}

.add-b {
    float: left;
    padding: 10px 15px;
}

div.add-b:hover {
    background-color: #e3e3e3 !important;
}

.faceul1 > a > span {
    display: inline-block;
    vertical-align: middle;
}

.faceul1 > a > span:nth-child(1) {
    max-width: 80%;
    overflow: hidden;
    height: 35px;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.faceul1 > a > span:nth-child(2) {
    width: 10%;
    overflow: hidden;
    text-align: center;
}

.faceul1 > a > span:nth-child(3) {
    width: 25%;
    min-width: 50px;
    text-align: right;
}

.mar {
    display: inline-block;
    margin-right: 10px;
}


/*右面*/
.bottom{position: absolute;  bottom: 0; margin-bottom: 15px; width: 100%;}
.left_container {  width: 245px;  min-height: 600px;  float: left;  position: relative;  padding-bottom: 80px; border: 1px solid #ddd;  border-radius: 2px; }
.right_container { margin-left: 255px;  padding: 15px;  min-width: 700px;min-height: 600px; border: 1px solid #ddd;  border-radius: 2px; }
.Right-label, .Left-label {
    padding: 0;
}

.Left-label {
    min-height: 86px;
}

.left-bottom {
    margin-top: 45px;
}

.Right-label {
    padding: 10px 15px 10px 15px;
    min-width: 700px;
}

.conon {
    padding: 10px 0;
    display: inline-block;
    float: left;
    min-width: 300px;
}

.conon > a, .concon > a:hover {
    color: #909090;
    font-family: "方正兰亭超细黑简体";
    font-weight: 700;
}

.conon > p, .conon > p.a:hover {
    display: inline-block;
    padding: 0 5px;
    font-weight: 700;
}

.conon > a:nth-child(2), .conon > a:nth-child(2):hover {
    color: black;
    font-weight: lighter;
    text-decoration: none;
}

.dq > a {
    color: black;
}

.bj > a, .bj > a:hover {
    color: black;
    text-decoration: none;
}
.bonceCon table input, .bonceCon table select, .bonceCon textarea {
    background-color: #fff;
    line-height: inherit;
    text-align: left;
    color: #3f3f3f;
    width: 100%;
}
.ty-table-none td span.ty-color-blue, .ty-table-none td span.ty-color-red{
    padding: 4px 8px;
    border-radius: 2px;
    font-weight: bold;
    cursor: pointer;
    white-space: nowrap;
}
.ty-table tr td span.tb-btn-sm {
    font-size: 12px;
}

.ty-table td .ty-color-red:hover {
    color: #fff;
    background-color: #ed5565;
}

.ty-table .ty-color-blue:hover {
    color: #fff;
    background-color: #5d9cec;
    transition: all .2s
}

.add-block{
    margin: 16px 0;
}
.gapLeft{
    margin-left: 30px;
}
.gapSm{
    margin-right: 10px;
}
.gapLong{
    margin-right: 60px;
}
.bg-yellow tr:nth-child(even) {
    background: #ddebf7;
}
.ty-table tr td.createInfo{
    font-size: 11px;
    white-space: nowrap;
}
.ty-table tbody td.algnL{
    text-align: left;
}
.scan-wrap{
    margin: auto;
}
.scan-wrap hr{
    border-top: 1px solid #d1d1d1;
}
#viewSettings i.ccc{
    color:#ccc;
}
#viewSettings .settingsCon>p {
    padding:5px 20px 5px 0;
}
#viewSettings .settingsCon {
    margin: auto;width: 82%;
}
#viewSettings .leMar {
    padding-top: 12px;
}
#viewSettings .leMar span {
    margin-right: 42px;
    display: inline-block;
    min-width: 78px;
}
#viewSettings .fa{
    margin-right: 14px ;
}
#viewSettings .fa-dot-circle-o{
    color:#48cfad ;
}
.editSourceSect{
    margin-top: 40px;
}
.origin{
    margin-top: 10px;
}
.origin select{
    width: 254px;
}
.origin select[disabled]{
    background: #d9d9d9 !important;
}
.gapRt{
    display: inline-block;
    width: 120px;
}
.scropWrap{
    overflow-x:scroll;
}
.innerElem{
    width: 1000px;
}
.linkBtn{
    color:#5d9cec;
}
.gspT{
    padding-top: 40px;
}
#splitGS, #splitGS2, #partEntry{
    width:970px;
}
#splitGS .bonceCon {
    max-height: 500px;
    overflow-y: auto;
}
#selecGS{
    display: none;
    position: absolute;
    background:#fff;
    width:192px;
    max-height:200px;
    overflow-y:auto;
    border-radius:4px;
    border:1px solid #ccc;
    border-top:none;
}
#selecGS option{
    border-bottom:1px solid #eee;
    background:#f9f8f8;
}
#selecGS option:hover, #selecGS option.active{
    background:#ddd;
    cursor: default;
}
#partEntry td[disabled], #partEntry td[disabled] input, #partEntry td[disabled] select,#partEntry td[disabled] textarea{
    background:#eee;
}
select[disabled],input[disabled]{
    background:#d9d9d9!important;
}
#partEntry input,#partEntry textarea{
    width: 100%;
    border:none;
}
.editSourceSect option[disabled]{
    background:#eee;
    cursor: not-allowed;
}
#searchKeyBase{
    width: 190px;
}
.editSourceSect option.grayOpt{
    background: #eee;
}
.selectDis{
    pointer-events: none;
    background: #d9d9d9 !important;
}
.ty-table tbody td.algnL{
    text-align: left;
}
.double-sample{
    margin-right: 30px;
    width: 400px;
}
.cell-sample {
    padding-left: 20px;
}
.row-sample {
    padding: 6px 0;
}
.row-sample span:last-child{
    padding-right: 20px;
}
.lineThrough td:not(:last-child){
    color: red;
    text-decoration: line-through;
}
.ty-table tbody .trRed td:not(:last-child){
    color: red;
}
.ty-table tbody td.tdRed{
    color: red;
}
#assemblyTip {
    color: #0070c0;
    font-size: 12px;
}
.cmChoose{
    padding: 40px 0;
}
.zsCase select:first-child{
    margin-right: 130px;
}
.blueLinkBtn{
    padding: 4px;
    color: #0070bf;
}
.redLinkBtn{
    padding: 4px;
    color: #f10202;
}
.relatedGoods hr{
    border-bottom: 1px solid #bebebe;
}
.relatedGoods select{
    width: 350px;
}
.cmTip{
    float: left;
    width: 486px;
}
.addProductForm{
    margin: auto;
    width: 900px;
}
.zsCase {
    margin-bottom: 16px;
}


.adden{
    font-weight: bold;
    color: #0b94ea;
    margin-top: 0px;
    background: #FFF;
}

.imagne{
    background-image: url("../content/img/xls.png");
    /*从一个文件跳转到它的父级的父级时是‘../'*/
}

#cpUplond-1 #file_upload_1-button{
    font-weight: bold !important;
    color: #0b94ea !important;
    background-color: white !important;
}
#cpUplond-5 #file_upload_1-button{
    font-weight: bold !important;
    color: #0b94ea !important;
    background-color: white !important;
}
#cpUplond-2 #file_upload_1-button{
    margin-top: 0px;
    color: #0b94ea;
    font-size: 13px;
    background-color: white;
    font-weight: bold;
}

#cpUplond-6 #file_upload_1-button{
    margin-top: 0px;
    color: #0b94ea;
    font-size: 13px;
    background-color: white;
    font-weight: bold;
}

.cpUplond-3 #file_upload_1-button{
    margin: 0px;
}
#cpUplond-3 #file_upload_1-button{
    font-weight: bold;
    color: #0b94ea;
    margin-right: 10px;
    background-color: #F0F8FF;
}
.cpUplond-4 #file_upload_1-button{
    margin: 0px;
}
#cpUplond-4 #file_upload_1-button{
    font-weight: bold;
    color: #0b94ea;
    margin-right: 10px;
    background-color: #F0F8FF;
}

#cpUplond-7 #file_upload_1-button{
    font-weight: bold;
    color: #0b94ea;
    margin-right: 10px;
    background-color: #F0F8FF;
}

#cpUplond-8 #file_upload_1-button{
    font-weight: bold;
    color: #0b94ea;
    margin-right: 10px;
    background-color: #F0F8FF;
}

#picShow{
    position: fixed;  display: inline-flex;  justify-content: center;  border: 1px solid #aaa;
    background: rgba(7, 7, 7, 0.64);  z-index: 100009;  width: 100%;
    height: 100%;  text-align: center;  vertical-align: middle;  top: 0;  left: 0;
    align-items: center;
}

.pend{
    margin-left: 84px;
}
.offen{
    margin-left: 328px;
}
.bigen{
    width: 656px;
}

.sized{width: 35px !important;}
.sizeo{flex: none !important;width: 627px !important;position: relative !important;}
.sizenm{width: 322px !important;}
.sizeno{right: 50px !important;top:0px !important;}
.sizesi{right: 0px !important;top: 0px !important;}
.sizehd{right: -1px !important;bottom: 0px !important;}
.sizeote{width: 300px !important;}
#dettenadd{margin-left: auto;padding-right: 0px;padding-left: 5px;}
.ty-disabled{
    color: #aaa;
    cursor: default;
    pointer-events: none;
}
.oterme{
    width: 889px !important;
}