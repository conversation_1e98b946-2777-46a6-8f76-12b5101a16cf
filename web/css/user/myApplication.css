
.tip{ padding:15px; text-align: center; color: red ;  }

/*审批流程的展示*/
.ok-icon { background: url(icon/ok.png) no-repeat center center;   }
.no-icon { background: url(icon/no.png) no-repeat center center;  }
.wait-icon { background: url(icon/wait.png) no-repeat center center;  }
.ok-icon , .no-icon , .wait-icon { background-size:25px 25px; display:inline-block; width:25px; height:25px;margin-right:15px;    }
.processTr{ padding-top:15px;  }
.touserInfo{ margin-right:30px;    }
.touserInfo , .timeInfo{ position:relative; top:-8px;    }
.noProcess{ color:#E7505A;  }

.applyYes{ background: rgb(224, 235, 249);   }
.applyNo{ background: rgb(254, 231, 232);    }
.dealSee_continer{ margin:20px 0 ; padding:30px; color:#3598dc;   }
.dealSee_continer .ttl { font-size:20px;     }
.dealSee_continer .dealSee_process {  }

/*// 大图的展示*/
#info_img>a{margin-left: 5px}
#info_img>a>img{ height:50px ; max-width: 200px   }
#bigImagcon{ background: #fff ; margin:0 auto; position: relative ;   }
#imgController{ background: #101010; z-index:1;  font-size: 23px; width: 400px; height:45px; position: absolute;  bottom: 100px;  text-align: center ; padding:5px 0 ;    }
#imgController .rotate , #bigImagcon .closeImg{  color:#ddd; margin-right: 25px;  }
#imgController .closeImg{ font-size: 25px;   }
#imgController .closeImg:hover{ color:#ed5565;  }
#imgController .rotate:hover { color:#fff;   }
.bigImagC{ position: relative;  }
#bigImag{  display:block ; box-shadow:0 0 5px #6e6c6c; margin: 0 auto ;   }
.bounce_Fixed{ flex-direction:column; justify-content:center; }
#amountTip{ color:red; font-size:11px;  }


/*报销支票信息填写页面*/

.bonceContainer {
    width: 529px;
    font-size: 13px;
}
.addpayDetails {
    margin: 0 30px 0 0;
}
.addpayDetails p {
    text-align: right;
}
.bigBank {
    margin-top: 0;
}

.feeCat,.billCat,.billDate {
    height: 34px;
    width: 369px;
    color:#999999;
}

.bonceCon input {
    height: 34px;
    width: 369px;
}
.bounce_close{
    cursor:pointer;
}

.bonceFoot {
    padding: 10px 25px 10px 15px;
}
.bonceFoot {
    text-align: right;
}
.btnbd{
    width: 88px;
    height: 36px;
}
.contactTtl{
    font-weight:700;
}


