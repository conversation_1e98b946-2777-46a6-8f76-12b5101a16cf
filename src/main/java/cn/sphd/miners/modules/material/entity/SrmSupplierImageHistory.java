package cn.sphd.miners.modules.material.entity;

import javax.persistence.*;
import java.util.Date;
import java.util.Objects;

@Entity
@Table(name = "t_srm_supplier_image_history")
public class SrmSupplierImageHistory {
    private Integer id;
    private Integer supplier;
    private Integer supplierImage;
    private Integer org;
    private String type;
    private String title;
    private String description;
    private String normal;
    private String large;
    private String medium;
    private String thumbnail;
    private Integer orders;
    private Integer enabled;
    private Date enabledTime;
    private String state;
    private Date beginTime;
    private Date endTime;
    private String memo;
    private Integer creator;
    private String createName;
    private Date createDate;
    private Integer updator;
    private String updateName;
    private Date updateDate;
    private String operation;
    private Integer previousId;
    private Integer versionNo;

    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Basic
    @Column(name = "supplier", nullable = true)
    public Integer getSupplier() {
        return supplier;
    }

    public void setSupplier(Integer supplier) {
        this.supplier = supplier;
    }

    @Basic
    @Column(name = "org", nullable = true)
    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    @Basic
    @Column(name = "type", nullable = true, length = 1)
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Basic
    @Column(name = "title", nullable = true, length = 255)
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Basic
    @Column(name = "description", nullable = true, length = 255)
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Basic
    @Column(name = "normal", nullable = true, length = 255)
    public String getNormal() {
        return normal;
    }

    public void setNormal(String normal) {
        this.normal = normal;
    }

    @Basic
    @Column(name = "large", nullable = true, length = 255)
    public String getLarge() {
        return large;
    }

    public void setLarge(String large) {
        this.large = large;
    }

    @Basic
    @Column(name = "medium", nullable = true, length = 255)
    public String getMedium() {
        return medium;
    }

    public void setMedium(String medium) {
        this.medium = medium;
    }

    @Basic
    @Column(name = "thumbnail", nullable = true, length = 255)
    public String getThumbnail() {
        return thumbnail;
    }

    public void setThumbnail(String thumbnail) {
        this.thumbnail = thumbnail;
    }

    @Basic
    @Column(name = "orders", nullable = true)
    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    @Basic
    @Column(name = "enabled", nullable = true)
    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    @Basic
    @Column(name = "enabled_time", nullable = true)
    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    @Basic
    @Column(name = "state", nullable = true, length = 1)
    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    @Basic
    @Column(name = "begin_time", nullable = true)
    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    @Basic
    @Column(name = "end_time", nullable = true)
    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    @Basic
    @Column(name = "memo", nullable = true, length = 255)
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Basic
    @Column(name = "creator", nullable = true)
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    @Basic
    @Column(name = "create_name", nullable = true, length = 100)
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Basic
    @Column(name = "create_date", nullable = true)
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "updator", nullable = true)
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    @Basic
    @Column(name = "update_name", nullable = true, length = 100)
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Basic
    @Column(name = "update_date", nullable = true)
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Basic
    @Column(name = "operation", nullable = true, length = 1)
    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    @Basic
    @Column(name = "previous_id", nullable = true)
    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    @Basic
    @Column(name = "version_no", nullable = true)
    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
    @Basic
    @Column(name = "supplier_image", nullable = true)
    public Integer getSupplierImage() {
        return supplierImage;
    }

    public void setSupplierImage(Integer supplierImage) {
        this.supplierImage = supplierImage;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SrmSupplierImageHistory that = (SrmSupplierImageHistory) o;
        return id == that.id && Objects.equals(supplier, that.supplier) && Objects.equals(org, that.org) && Objects.equals(type, that.type) && Objects.equals(title, that.title) && Objects.equals(description, that.description) && Objects.equals(normal, that.normal) && Objects.equals(large, that.large) && Objects.equals(medium, that.medium) && Objects.equals(thumbnail, that.thumbnail) && Objects.equals(orders, that.orders) && Objects.equals(enabled, that.enabled) && Objects.equals(enabledTime, that.enabledTime) && Objects.equals(state, that.state) && Objects.equals(beginTime, that.beginTime) && Objects.equals(endTime, that.endTime) && Objects.equals(memo, that.memo) && Objects.equals(creator, that.creator) && Objects.equals(createName, that.createName) && Objects.equals(createDate, that.createDate) && Objects.equals(updator, that.updator) && Objects.equals(updateName, that.updateName) && Objects.equals(updateDate, that.updateDate) && Objects.equals(operation, that.operation) && Objects.equals(previousId, that.previousId) && Objects.equals(versionNo, that.versionNo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, supplier, org, type, title, description, normal, large, medium, thumbnail, orders, enabled, enabledTime, state, beginTime, endTime, memo, creator, createName, createDate, updator, updateName, updateDate, operation, previousId, versionNo);
    }
}
