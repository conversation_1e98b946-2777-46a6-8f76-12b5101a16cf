export default [
  // ----------文件与资料----------
  {
    path: '/releaseApply',
    name: 'releaseApply',
    component: () => import('@/view/apply/file/apply_file'),
    meta: {
      title: '文件发布申请'
    }
  },
  {
    path: '/versionApply',
    name: 'versionApply',
    component: () => import('@/view/apply/file/apply_updateFile'),
    meta: {
      title: '文件换版/废止申请'
    }
  },
  {
    path: '/fileDetailSee/:id',
    name: 'fileDetailSee',
    component: () => import('@/view/apply/file/applyFileDetail'),
    meta: {
      title: '文件发布申请--详情'
    }
  },
  {
    path: '/updateFileDetail/:id',
    name: 'updateFileDetail',
    component: () => import('@/view/apply/file/apply_updateFileDetail'),
    meta: {
      title: '文件换版/废止申请--详情'
    }
  },
  {
    path: '/fileApproval',
    name: 'fileApproval',
    component: () => import('@/view/approve/file/approve_File'),
    meta: {
      title: '文件审批'
    },
    children: [
      {
        path: '',
        name: 'approveFileIssue',
        component: () => import('@/view/approve/file/approveFile'),
        meta: {
          title: '文件审批-待处理'
        }
      },
      {
        path: 'approvedFileIssue',
        name: 'approvedFileIssue',
        component: () => import('@/view/approve/file/approvedFile'),
        meta: {
          title: '文件审批-已批准'
        }
      }
    ]
  },
  {
    path: '/approveFileDetail/:pendId',
    name: 'approveFileDetail',
    component: () => import('@/view/approve/file/approveFileDetail'),
    meta: {
      title: '文件审批--详情'
    }
  },
  {
    path: '/approveHandleFile/:pendId',
    name: 'approveHandleFile',
    component: () => import('@/view/approve/file/handledFileDetail'),
    meta: {
      title: '文件审批--详情'
    }
  },
  {
    path: '/versionApproval',
    name: 'versionApproval',
    component: () => import('@/view/approve/file/approveHistory'),
    meta: {
      title: '文件发布/换版/废止审批'
    }
  },
  {
    path: '/approveHisDetails/:fId',
    name: 'approveHisDetails',
    component: () => import('@/view/approve/file/approveHisDetails'),
    meta: {
      title: '文件发布/换版/废止审批-详情'
    }
  },
  {
    path: '/fileResultDetail/:id',
    name: 'fileResultDetail',
    component: () => import('@/view/approve/file/fileResultDetail'),
    meta: {
      title: '文件（发布/换版/废止）申请--详情'
    }
  },
  {
    path: '/applyFileQuery',
    name: 'applyFileQuery',
    component: () => import('@/view/apply/file/fileScreen'),
    meta: {
      title: '文件发布申请-筛选'
    }
  },
  {
    path: '/applyFileQueryResult',
    name: 'applyFileQueryResult',
    component: () => import('@/view/apply/file/fileScreenResult'),
    meta: {
      title: '文件发布申请--查询结果'
    }
  },
  {
    path: '/approveFileQuery',
    name: 'approveFileQuery',
    component: () => import('@/view/approve/file/fileQuery'),
    meta: {
      title: '文件审批-筛选'
    }
  },
  {
    path: '/approveFileQueryResult',
    name: 'approveFileQueryResult',
    component: () => import('@/view/approve/file/fileQueryResult'),
    meta: {
      title: '文件审批--查询结果'
    }
  },
  {
    path: '/screenDetailSee/:id',
    name: 'screenDetailSee',
    component: () => import('@/view/apply/file/fileScreenResultDetail'),
    meta: {
      title: '文件查询详情'
    }
  },
  {
    path: '/fileMessageDetail/:id',
    name: 'fileMessageDetail',
    component: () => import('@/view/message/fileMessage'),
    meta: {
      title: '文件消息详情'
    }
  },
  {
    path: '/fileChangeMessageDetail/:id',
    name: 'fileChangeMessageDetail',
    component: () => import('@/view/message/fileMessage'),
    meta: {
      title: '文件消息详情'
    }
  },
  // ----------即将消失的文件----------
  {
    path: '/soonDisappearFile/',
    name: 'soonDisappearFile',
    component: () => import('@/view/approve/file/willCleanFile'),
    meta: {
      title: '即将消失的文件'
    }
  },
  {
    path: '/willCleanFileDetail/:id',
    name: 'willCleanFileDetail',
    component: () => import('@/view/approve/file/willCleanFileDetail'),
    meta: {
      title: '即将消失的文件--详情'
    }
  },
  // ----------内容管理----------
  {
    path: '/contentReleaseApply',
    name: 'contentReleaseApply',
    component: () => import('@/view/apply/about/contentApply'),
    meta: {
      title: '内容发布申请'
    }
  },
  {
    path: '/contentVersionApply',
    name: 'contentVersionApply',
    component: () => import('@/view/apply/about/contentUpdate'),
    meta: {
      title: '内容换版申请'
    }
  },
  {
    path: '/contentDetail/:id/:mark',
    name: 'contentDetail',
    component: () => import('@/view/common/about/contentDetail'),
    meta: {
      title: '内容详情（所有）'
    }
  },
  {
    path: '/contentShow/:id',
    name: 'contentShow',
    component: () => import('@/view/common/about/contentShow'),
    meta: {
      title: '内容'
    }
  },
  {
    path: '/contentReleaseVersionApproval',
    name: 'contentReleaseVersionApproval',
    component: () => import('@/view/approve/about/contentApplyOrUpdateApprove'),
    meta: {
      title: '内容发布/换版审批'
    }
  },
  {
    path: '/contentApproval',
    name: 'contentApproval',
    component: () => import('@/view/approve/about/contentApprove'),
    meta: {
      title: '内容审批'
    }
  },
  {
    path: '/contentQuery/:name',
    name: 'contentQuery',
    component: () => import('@/view/query/content_query'),
    meta: {
      title: '内容查询（所有）'
    }
  },
  {
    path: '/contentQueryPer',
    name: 'contentQueryPer',
    component: () => import('@/view/query/content_query_per'),
    meta: {
      title: '内容查询（所有）--结果'
    }
  },
  // ----------阅览室-----------
  {
    path: '/fileBorrowApply',
    name: 'borrowApply',
    component: () => import('@/view/apply/readingRoom/borrowApply'),
    meta: {
      title: '文件借阅申请'
    }
  },
  {
    path: '/approvalFileBorrowApply',
    name: 'borrowApproval',
    component: () => import('@/view/approve/readingRoom/borrowApproval'),
    meta: {
      title: '文件借阅申请'
    }
  },
  {
    path: '/fileBorrowApproval',
    name: 'borrowApprovalLast',
    component: () => import('@/view/approve/readingRoom/borrowApprovalLast'),
    meta: {
      title: '文件借阅审批'
    }
  },
  {
    path: '/borrowFileDetail/:id/:mark',
    name: 'borrowFileDetail',
    component: () => import('@/view/common/readingRoom/borrowDetail'),
    meta: {
      title: '文件借阅--详情'
    }
  },
  {
    path: '/borrowQuery/:name',
    name: 'borrowQuery',
    component: () => import('@/view/common/readingRoom/borrow_query'),
    meta: {
      title: '文件借阅--查询'
    }
  },
  {
    path: '/borrowQueryPer',
    name: 'borrowQueryPer',
    component: () => import('@/view/common/readingRoom/borrow_query_per'),
    meta: {
      title: '文件借阅--查询结果'
    }
  },
  {
    path: '/forumBorrowApply/:id',
    name: 'applyForumBorrowApply',
    component: () => import('@/view/apply/readingRoom/discussionBorrowApply'),
    meta: {
      title: '讨论组阅览申请'
    }
  },
  {
    path: '/approvalForumBorrowApply/:id',
    name: 'discussionBorrowApproval',
    component: () => import('@/view/approve/readingRoom/discussionBorrowApproval'),
    meta: {
      title: '讨论组阅览申请（中间人）'
    }
  },
  {
    path: '/forumBorrowApproval/:id',
    name: 'discussionBorrowApprovalLast',
    component: () => import('@/view/approve/readingRoom/discussionBorrowApprovalLast'),
    meta: {
      title: '讨论组阅览审批'
    }
  },
  {
    path: '/discussionBorrowDetail/:id/:mark',
    name: 'discussionBorrowDetail',
    component: () => import('@/view/common/readingRoom/discussionBorrowDetail'),
    meta: {
      title: '讨论组阅览--详情'
    }
  },
  {
    path: '/discussionBorrowQuery/:name',
    name: 'discussionBorrowQuery',
    component: () => import('@/view/common/readingRoom/discussionBorrow_query'),
    meta: {
      title: '讨论组阅览-查询'
    }
  },
  {
    path: '/discussionBorrowQueryPer',
    name: 'discussionBorrowQueryPer',
    component: () => import('@/view/common/readingRoom/discussionBorrow_query_per'),
    meta: {
      title: '讨论组阅览-查询结果'
    }
  },
  // ----------与文件有关的通知-----------
  {
    path: '/fileNoticeApproval/:pid',
    name: 'fileNoticeApproval',
    component: () => import('@/view/approve/file/fileNoticeApproval'),
    meta: {
      title: '与文件有关的通知'
    }
  },
  {
    path: '/notice/:name',
    name: 'fileNotice',
    component: () => import('@/view/approve/file/notice/fileNotice'),
    meta: {
      title: '与文件有关的通知-各种通知'
    }
  },
  {
    path: '/fileNoticeDetail/:id',
    name: 'fileNoticeDetail',
    component: () => import('@/view/approve/file/notice/fileNoticeDetail'),
    meta: {
      title: '与文件有关的通知-文件详情'
    }
  },
  {
    path: '/batchFile/:id',
    name: 'batchFile',
    component: () => import('@/view/approve/file/notice/batchFile'),
    meta: {
      title: '与文件有关的通知-批量文件'
    }
  },
  {
    path: '/batchFileDetail',
    name: 'batchFileDetail',
    component: () => import('@/view/approve/file/notice/batchFileDetail'),
    meta: {
      title: '与文件有关的通知-批量文件详情'
    }
  },
  {
    path: '/childFolder/:id',
    name: 'childFolder',
    component: () => import('@/view/approve/file/notice/childFolder'),
    meta: {
      title: '与文件有关的通知-下级文件夹内容'
    }
  },
  {
    path: '/notice/willCleanNotice/:name',
    name: 'willCleanNotice',
    component: () => import('@/view/approve/file/notice/willCleanNotice'),
    meta: {
      title: '与文件有关的通知-即将消失的通知'
    }
  }
]
