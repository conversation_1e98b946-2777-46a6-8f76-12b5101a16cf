package cn.sphd.miners.modules.accountant.entity;

import org.apache.ibatis.type.Alias;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  科目操作记录
 * <AUTHOR>
 * @Date 2022-04-22 
 */
@Alias("TaccountantSubjectRecordDto")
public class TaccountantSubjectRecordDto implements Serializable {

	private static final long serialVersionUID =  1L;

	/**
	 * ID
	 */
	private Integer id;

	private Integer org;

	/**
	 * 科目ID
	 */
	private Integer subjectId;

	/**
	 * 科目代码
	 */
	private String subject;

	/**
	 * 父节点
	 */
	private String parent;

	/**
	 * 名称
	 */
	private String name;

	/**
	 * 建账ID
	 */
	private Integer establish;

	/**
	 * 创建人
	 */
	private String createName;
	private Integer creator;

	/**
	 * 创建时间
	 */
	@DateTimeFormat(pattern="yyyy-MM-dd")
	private Date createDate;

	/**
	 * 操作:1-增,2-删,3-改,4-启用,5-停用,6-重新建账
	 */
	private String operation;

	/**
	 * 叶子科目类型
	 */
	private Integer category;

	/**
	 * 叶子科目类型名称
	 */
	private String categoryName;

	/**
	 * 计量单位
	 * 当需要数量辅助核算时显示，否则不显示
	 */
	private String measureUnit;

	/**
	 * 数量辅助核算
	 * 1-需要数量辅助核算，0-不需要
	 */
	private String quantityAssistingAccounting;

	/**
	 * 余额方向:1-借,2-贷,3-平
	 */
	private String balanceDirection;

	/**
	 * 父节点名称
	 */
	private String parentName;

	/**
	 * 修改前记录ID
	 */
	@Column(name = "previous_id" )
	private Integer previousId;

	public Integer getCreator() {
		return creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public Integer getOrg() {
		return org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public String getCategoryName() {
		return categoryName;
	}

	public void setCategoryName(String categoryName) {
		this.categoryName = categoryName;
	}

	public Integer getCategory() {
		return category;
	}

	public void setCategory(Integer category) {
		this.category = category;
	}

	public String getMeasureUnit() {
		return measureUnit;
	}

	public void setMeasureUnit(String measureUnit) {
		this.measureUnit = measureUnit;
	}

	public String getQuantityAssistingAccounting() {
		return quantityAssistingAccounting;
	}

	public void setQuantityAssistingAccounting(String quantityAssistingAccounting) {
		this.quantityAssistingAccounting = quantityAssistingAccounting;
	}

	public String getBalanceDirection() {
		return balanceDirection;
	}

	public void setBalanceDirection(String balanceDirection) {
		this.balanceDirection = balanceDirection;
	}

	public String getParentName() {
		return parentName;
	}

	public void setParentName(String parentName) {
		this.parentName = parentName;
	}

	public String getOperation() {
		return operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getSubjectId() {
		return this.subjectId;
	}

	public void setSubjectId(Integer subjectId) {
		this.subjectId = subjectId;
	}

	public String getSubject() {
		return this.subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public String getParent() {
		return this.parent;
	}

	public void setParent(String parent) {
		this.parent = parent;
	}

	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getEstablish() {
		return this.establish;
	}

	public void setEstablish(Integer establish) {
		this.establish = establish;
	}

	public String getCreateName() {
		return this.createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getPreviousId() {
		return this.previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

}
