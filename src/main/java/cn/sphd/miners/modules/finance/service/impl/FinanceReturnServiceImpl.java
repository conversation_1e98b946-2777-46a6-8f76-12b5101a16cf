package cn.sphd.miners.modules.finance.service.impl;

import cn.sphd.miners.common.service.BaseServiceImpl;
import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.finance.dao.*;
import cn.sphd.miners.modules.finance.entity.*;
import cn.sphd.miners.modules.finance.service.AccountService;
import cn.sphd.miners.modules.finance.service.DataService;
import cn.sphd.miners.modules.finance.service.FinanceAccountService;
import cn.sphd.miners.modules.finance.service.FinanceReturnService;
import cn.sphd.miners.modules.sales.dao.SlCollectApplicationDao;
import cn.sphd.miners.modules.sales.entity.SlCollectApplication;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.JSON;
import com.beust.ah.A;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * Created by Administrator on 2016/12/15.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class FinanceReturnServiceImpl extends BaseServiceImpl implements FinanceReturnService {
    @Autowired
    FinanceReturnDao financeReturnDao;
    @Autowired
    FinanceAccountBillDao financeAccountBillDao;
    @Autowired
    AccountDetailDao accountDetailDao;
    @Autowired
    AccountPeroidDao accountPeroidDao;
    @Autowired
    SlCollectApplicationDao slCollectApplicationDao;
    @Autowired
    FinanceReturnHistoryDao financeReturnHistoryDao;
    @Autowired
    FinanceAccountBillHistoryDao financeAccountBillHistoryDao;
    @Autowired
    FinanceAccountDao financeAccountDao;

    @Autowired
    FinanceAccountService financeAccountService;
    @Autowired
    DataService dataService;
    @Autowired
    AccountService accountService;

    @Override
    public List<FinanceReturn> getAllReturn(Integer oid, Integer type, String state, Date beginDate) {
        Map<String,Object> map = new HashMap<>();
        StringBuffer hql = new StringBuffer("from FinanceReturn where org_=:oid");
        map.put("oid",oid);
        if (type!=null){
            hql.append(" and type=:type");
            map.put("type",type);
        }
        if (!MyStrings.nulltoempty(state).isEmpty()){
            hql.append(" and state=:state");
            map.put("state",state);
        }
        if (beginDate!=null){
            hql.append(" and YEAR(createDate)=YEAR(:beginDate)");
            map.put("beginDate",beginDate);
        }
        if (!MyStrings.nulltoempty(state).isEmpty()&&"4".equals(state))
        hql.append(" order by amount desc");
        List<FinanceReturn> financeReturns = financeReturnDao.getListByHQLWithNamedParams(hql.toString(),map);
        return financeReturns;
    }

    @Override
    public FinanceReturn getReturn(Integer returnId) {
        FinanceReturn financeReturn = financeReturnDao.get(returnId);
        return financeReturn;
    }

    @Override
    public void updateReturn(FinanceReturn financeReturn1) {
        financeReturnDao.update(financeReturn1);
    }

    @Override
    public List<FinanceAccountBill> getAccountBill(Integer oid, String type) {
        String condition = null;
        if (oid!=null && !"".equals(type)){
        condition = " and o.org = " + oid + " and o.type = '" + type + "'";
        }
        List<FinanceAccountBill> financeAccountBills = financeAccountBillDao.findCollectionByConditionNoPage(condition,null,null);
        return financeAccountBills;
    }

    @Override
    public FinanceAccountBill getBill(Integer returnId) {
        String hql = " from FinanceAccountBill o where o.financeReturn = " +returnId;
        FinanceAccountBill financeAccountBill = financeAccountBillDao.getByHQL(hql+" order by id desc");
        return financeAccountBill;
    }

    @Override
    public FinanceAccountBill getBill(Integer returnId,String type) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from FinanceAccountBill where financeReturn.id=:returnId";
        map.put("returnId",returnId);
        if (!MyStrings.nulltoempty(type).isEmpty()){
            hql+=" and type=:type";
            map.put("type",type);
        }
        hql+=" order by id desc";
        FinanceAccountBill financeAccountBill = (FinanceAccountBill) financeAccountBillDao.getByHQLWithNamedParams(hql,map);
        return financeAccountBill;
    }

    @Override
    public Map<String, Object> chooseCheque(Integer oid, String type,Map<String,Object> map) {
        if ("2".equals(type)) { // 外部支票(转账支票)
            List<FinanceReturn> financeReturns = getAllReturn(oid, 1, "1",null);
            map.put("content", financeReturns); //外部支票信息
        } else if ("3".equals(type)) { //承兑汇票
            List<FinanceReturn> financeReturns = getAllReturn(oid, 2, "1",null); //选择有效的承兑汇票
            map.put("content", financeReturns);  //承兑汇票信息
        } else if ("4".equals(type) || "1".equals(type)) { //4-转账银行 1-内部支票
            List<FinanceAccount> financeAccounts = financeAccountService.getAccountByAccountStatus(oid, 1,null);
            map.put("content", financeAccounts);
        }
        return map;
    }

    @Override
    public Map<String, Object> saveBank(FinanceReturn financeReturn, Integer returnId, User user,Map<String,Object> map) {
        FinanceReturn financeReturn1 = financeReturnDao.get(returnId);
        if (financeReturn1!=null){
            FinanceAccount financeAccount = financeAccountService.getAccountById(financeReturn.getAccountId());
            //Debug start ********
            Map<String, String> debug = new HashMap<>();
            debug.put("returnId", returnId.toString());
            debug.put("accountId", financeReturn.getAccountId().toString());
            debug.put("financeAccount0", JSON.toJSONString(financeAccount));
            //Debug finish ********

            financeReturn1.setState("2"); //1-有效,2-存入银行,3-作废,4-已支出
            financeReturn1.setSaveBankName(financeReturn.getBankName()); //存入银行的名称
            financeReturn1.setAccout(financeAccount.getAccount()); //银行账号
            financeReturn1.setDepositDate(financeReturn.getDepositDate()); //存入时间
            financeReturn1.setDepositorName(financeReturn.getDepositorName()); //经手人
            financeReturn1.setReceiveAccountDate(financeReturn.getReceiveAccountDate());  //到账日期
            financeReturn1.setAccountId(financeReturn.getAccountId());
            financeReturn1.setUpdateDate(new Date());
            financeReturn1.setUpdator(user.getUserID());
            financeReturn1.setUpdateName(user.getUserName());
            financeReturnDao.update(financeReturn1);

            /*总账户中*/
            if (financeAccount.getId()!=null){
                financeAccount.setBalance(financeAccount.getBalance().add(financeReturn1.getAmount()));
                financeAccount.setCredit(financeAccount.getCredit().add(financeReturn1.getAmount()));
                financeAccountService.updateFinanceAccount(financeAccount);
            }

            /*添加账务明细账表*/
            AccountDetail accountDetail = new AccountDetail();
            accountDetail.setType("3");  //1-初始金额冲减,2-转帐交易,3-收入,4-支出
            accountDetail.setAccountId(financeAccount);
            accountDetail.setFid(financeReturn.getAccountId().toString());
            accountDetail.setBillDetail(getBill(returnId));
            accountDetail.setOrg(financeReturn1.getOrg());
            if (financeReturn1.getType() == 1){
                accountDetail.setMethod("3");  //1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
            }else {
                accountDetail.setMethod("4");  //1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
            }
            accountDetail.setCredit(financeReturn1.getAmount());
            accountDetail.setBillAmount(financeReturn1.getBillAmount());  //票面金额
            accountDetail.setCreateName(user.getUserName());
            accountDetail.setCreateDate(new Date());
            accountDetail.setCreator(user.getUserID());
            accountDetail.setAccountBank(financeReturn.getBankName()+financeAccount.getAccount());//银行名和账号
            accountDetail.setBalance(financeAccount.getBalance());
            accountDetail.setSummary(financeReturn1.getSummary());
            accountDetail.setAuditorName(financeReturn1.getOperatorName());
            accountDetail.setMemo(financeReturn1.getMemo());
            accountDetail.setPurpose(financeReturn1.getPurpose());
            accountDetail.setAuditDate(new Date());
            accountDetail.setSource("1");
            accountDetail.setBillDate(new Date());
            accountDetail.setReceiveAccountDate(financeReturn.getReceiveAccountDate());  //到账日期   1.290首页财会（承接李亚星项目）  lixu 加
            accountDetail.setAccountantStatus("1"); //会计数据状态  1-未选择  2-已选择

            FinanceAccountBill financeAccountBill = dataService.getAccountBillByReturnId(returnId);
            if (financeAccountBill!=null){
                accountDetail.setSource(financeAccountBill.getSource());
                accountDetail.setBusiness(financeAccountBill.getBusiness());
                accountDetail.setBusinessDate(financeAccountBill.getBusinessDate());
                accountDetail.setBusinessType(financeAccountBill.getBusinessType());
                accountDetail.setBillDetail(financeAccountBill);

                if ("3".equals(financeAccountBill.getSource())){  //来源为回款录入
                    SlCollectApplication slCollectApplication = slCollectApplicationDao.get(financeAccountBill.getBusiness());
                    slCollectApplication.setReceiveBank(financeAccount.getId().toString());  //收款银行（存入银行）
                    slCollectApplicationDao.update(slCollectApplication);
                }
            }
            /*日结*/
            AccountPeriod accountPeriod =accountService.getAccountPeriodByDay(financeAccount.getId(),new Date());
            /*月结*/
            AccountPeriod accountPeriod1 = accountService.getAccountPeriodByMonth(financeAccount.getId(),new Date());
            //Debug start ********
            debug.put("accountPeriodDay0", JSON.toJSONString(accountPeriod));
            debug.put("accountPeriodMonth0", JSON.toJSONString(accountPeriod1));
            //Debug finish ********
            if (accountPeriod!=null){
                accountPeriod.setCredit(accountPeriod.getCredit().add(financeReturn1.getAmount()));  //收入
                accountPeriod.setBalance(accountPeriod.getBalance().add(financeReturn1.getAmount()));  // 本期余额
                accountPeroidDao.update(accountPeriod);

                accountDetail.setAccount(accountPeriod.getId().toString());//期表日结的id(记录属于哪一期)
            }

            accountDetailDao.save(accountDetail);

            if (accountPeriod1!=null){
                accountPeriod1.setCredit(accountPeriod1.getCredit().add(financeReturn1.getAmount()));
                accountPeriod1.setBalance(accountPeriod1.getBalance().add(financeReturn1.getAmount()));
                accountPeroidDao.update(accountPeriod1);
            }else {
                //Debug start ********
                Logger.getLogger(getClass()).error("存入银行日志：accountPeriod1=null" + "\n");
                //Debug finish ********
            }

            map.put("financeReturn",financeReturn1);
            map.put("status",1);  //存入成功
            //Debug start ********
            debug.put("method", accountDetail.getMethod());
            debug.put("balance", financeAccount.getBalance().toString());
            debug.put("financeReturn", JSON.toJSONString(financeReturn1));
            debug.put("financeAccount", JSON.toJSONString(financeAccount));
            debug.put("accountDetail", JSON.toJSONString(accountDetail));
            debug.put("accountPeriodDay", JSON.toJSONString(accountPeriod));
            debug.put("accountPeriodMonth", JSON.toJSONString(accountPeriod1));

            Logger.getLogger(getClass()).error("存入银行日志：" + "\n" + JSON.toJSONString(debug));
            //Debug finish ********
        }else {
            map.put("status",0);   //存入失败
        }
        return map;
    }

    @Override
    public Map<String, Object> chooseChequeOrBank(Integer oid,Integer type, Map<String, Object> map) {
        if (2==type) {
            List<FinanceReturn> financeReturns = getAllReturn(oid, 1, "1",null);  // 外部支票(转账支票)
            map.put("financeReturns", financeReturns); //外部支票信息
        }else if (3==type) {
            List<FinanceReturn> acceptanceBills = getAllReturn(oid, 2, "1",null); //选择有效的承兑汇票
            map.put("acceptanceBills", acceptanceBills);  //承兑汇票信息
        }else if (1==type || 4==type) {
            List<FinanceAccount> financeAccounts = financeAccountService.getAccountByAccountStatus(oid, 1, null);  //4-转账银行(借款中只需要对公户) 1-内部支票
            map.put("financeAccounts", financeAccounts);
        }
        return map;
    }

    @Override
    public Map<String, Object> getReturnNum(Integer oid, Integer type) {
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> available = getReturnNumByConditional(oid,type,null,"1");  //尚在手中的
        Map<String, Object> yearTime = getReturnNumByConditional(oid,type, NewDateUtils.getYear(new Date()),null);  //今年
        Map<String, Object> lastYearTime = getReturnNumByConditional(oid,type, NewDateUtils.getYear(NewDateUtils.changeYear(new Date(),-1)),null);  //去年
        Map<String, Object> beforeYearTime = getReturnNumByConditional(oid,type, NewDateUtils.getYear(NewDateUtils.changeYear(new Date(),-2)),null);  //前年
        map.put("available",available);
        map.put("yearTime",yearTime);
        map.put("lastYearTime",lastYearTime);
        map.put("beforeYearTime",beforeYearTime);
        return map;
    }

    @Override
    public Map<String,Object> getReturnNumByConditional(Integer oid, Integer type,Integer year,String state){
        Map<String, Object> map = new HashMap<>();
        String hql = "select count(id),sum(amount) from FinanceReturn where org_=:org";
        map.put("org",oid);
        if (type!=null){
            hql+=" and type=:type";
            map.put("type",type);
        }
        if (year!=null){
            hql+=" and YEAR(createDate)=:year";
            map.put("year",year);
        }
        if (!MyStrings.nulltoempty(state).isEmpty()){
            hql+=" and state=:state";
            map.put("state",state);
        }
        Object[] objects = (Object[]) financeReturnDao.getByHQLWithNamedParams(hql,map);
        map.clear();
        if (objects.length>0){
            map.put("num",objects[0]);
            map.put("amount",objects[1]);
        }else {
            map.put("num",0);
            map.put("amount",0);
        }
        return map;
    }

    @Override
    public Map<String,Object> getReturnStatistics(Integer oid, Integer type, String state, Date beginDate) {
        Map<String, Object> map = new HashMap<>();
        Integer numAll = null;
        BigDecimal amountAll = new BigDecimal(0);
        List<Map<String,Object>> listMap = new ArrayList<>();
        List<Map<String,Object>> listMap2 = getReturnStatisticsBySource(oid,type,state,beginDate,"1");  //数据录入录入的
        if (listMap2.size()>0) {
            listMap.addAll(listMap2);
        }
        List<Map<String,Object>> listMap1 = getReturnStatisticsBySource(oid,type,state,beginDate,"2");  //常规借款支出的
        if (listMap1.size()>0) {
            listMap.addAll(listMap1);
        }
        if (listMap.size()>0){
            this.sort(listMap,"amount");
            for (Map<String,Object> map1:listMap) {
                Long num = (Long) map1.get("num");
                BigDecimal amount = (BigDecimal) map1.get("amount");
                numAll = numAll==null?num.intValue():numAll+num.intValue();
                amountAll = amountAll.add(amount);
            }
        }
        map.put("numAll",numAll);  //总张数
        map.put("amountAll",amountAll);  //总金额
        map.put("listMap",listMap);  //明细
        return map;
    }

    /**
     * <AUTHOR>
     * @date 2021/4/27 16:06
     * list里套map
     * 根据时间倒序
     */
    public static void sort(List<Map<String, Object>> list, final String args) {
        if (!"".equals(args) && args != null) {
            Collections.sort(list, new Comparator<Map<String, Object>>() {
                public int compare(Map<String, Object> mapOne, Map<String, Object> mapTwo) {
                    int flag = 0;
                    BigDecimal f1 = (BigDecimal) mapOne.get(args);
                    BigDecimal f2 = (BigDecimal) mapTwo.get(args);
                    if ((f1.compareTo(f2)) > 0) {
                        flag = -1;
                    } else if ((f1.compareTo(f2)) < 0) {
                        flag = 1;
                    }
                    return flag;
                };
            });
        }
    }

    public List<Map<String,Object>> getReturnStatisticsBySource(Integer oid, Integer type, String state, Date beginDate,String source){
        Map<String, Object> map = new HashMap<>();
        String hql = "select count(f.id),sum(f.amount),b.oppositeCorp from FinanceReturn f,FinanceAccountBill b where" +
                " f.id=b.financeReturn and b.org_=:org and b.type='2' and b.financeReturn is not null";
        if ("1".equals(source)){
            hql = "select f.id,count(f.id),sum(f.amount),b.oppositeCorp from FinanceReturn f,FinanceAccountBill b where" +
                    " f.id=b.financeReturn and b.org_=:org and b.type='2' and b.financeReturn is not null";
        }
        map.put("org",oid);
        if (type!=null){
            hql+=" and f.type=:type";
            map.put("type",type);
        }
        if (!MyStrings.nulltoempty(state).isEmpty()){
            hql+=" and f.state=:state";
            map.put("state",state);
        }
        if (beginDate!=null){
//            hql+=" and YEAR(f.createDate)=YEAR(:beginDate)";
            hql+=" and f.createDate between :beginDate and :endDate";
            map.put("beginDate",NewDateUtils.getNewYearsDay(beginDate)); //元旦
            map.put("endDate",NewDateUtils.getLastTimeOfYear(beginDate));  //年的最后一秒
        }
        if (!MyStrings.nulltoempty(source).isEmpty()){
            hql+=" and b.source=:source";
            map.put("source",source);
            if (!"1".equals(source)){  //数据来源:1-手工录入(数据录入、以及报销等),2-业务自动生成(暂时只包括借款) 3-销售回款(1.58版本)
                hql+=" group by b.oppositeCorp";
            }else {
//                hql+=" group by b.financeReturn";
                hql+=" group by b.financeReturn, b.stakeholderCategory,b.stakeholder";
            }
        }
        List<Object[]> objects = financeReturnDao.getListByHQLWithNamedParams(hql,map);
        List<Map<String,Object>> listMap = new ArrayList<>();
        for (Object[] ob:objects) {
            Map<String, Object> map1 = new HashMap<>();
            if ("1".equals(source)){
                map1.put("id",ob[0]);  //票据id
                map1.put("num",ob[1]);  //张数
                map1.put("amount",ob[2]);  //总金额
                map1.put("oppositeCorp",ob[3]);  //收款单位
                if (map1.get("id")!=null){
                    listMap.add(map1);
                }
            }else {
                map1.put("id",0);
                map1.put("num",ob[0]);
                map1.put("amount",ob[1]);
                map1.put("oppositeCorp",ob[2]);
                if (!map1.get("num").equals(0)){
                    listMap.add(map1);
                }
            }

        }
        return listMap;
    }

    @Override
    public List<FinanceReturn> getReturnStatisticsList(Integer oid, Integer type, String state, Date beginDate, String oppositeCorp,Integer returnId) {
        Map<String, Object> map = new HashMap<>();
        StringBuffer where = new StringBuffer();
        StringBuffer hql = new StringBuffer("from FinanceReturn");
        if (type!=null){
            where.append(" and type=:type");
            map.put("type",type);
        }
        if (!MyStrings.nulltoempty(state).isEmpty()){
            where.append(" and state=:state");
            map.put("state",state);
        }
        if (beginDate!=null){
            where.append(" and YEAR(createDate)=YEAR(:beginDate)");
            map.put("beginDate",beginDate);
        }
        if (returnId!=null&&returnId!=0){
            where.append(" and id=:id");
            map.put("id",returnId);
        }
        if (!MyStrings.nulltoempty(oppositeCorp).isEmpty()){
            where.append(" and id in (select financeReturn from FinanceAccountBill where org_=:org and type='2' and financeReturn is not null and oppositeCorp=:oppositeCorp)");
            map.put("oppositeCorp",oppositeCorp);
            map.put("org",oid);
        }
        if (where.length() > 0) {
            hql.append(" where ").append(where.substring(5));
        }
        List<FinanceReturn> financeReturns = financeReturnDao.getListByHQLWithNamedParams(hql.toString(),map);
        return financeReturns;
    }

    @Override
    public Map<String, Object> getReturnStatisticState(Integer oid, Integer type, Date beginDate) {
        Map<String, Object> map = new HashMap<>();
        Integer year = null;
        if (beginDate!=null){
            year = NewDateUtils.getYear(beginDate);
        }
        Map<String, Object> available = getReturnNumByConditional(oid,type,year,"1");  //尚在手中的
        Map<String, Object> saveBanks = getReturnNumByConditional(oid,type, year,"2");  //存入银行的
        Map<String, Object> payReturns = getReturnNumByConditional(oid,type,year,"4");  //又付出去的
        Long numAll = (Long) available.get("num")+(Long)saveBanks.get("num")+(Long)payReturns.get("num");
        BigDecimal amountAvailable = (BigDecimal) available.get("amount")!=null?(BigDecimal) available.get("amount"):new BigDecimal(0);
        BigDecimal amountSaveBanks = (BigDecimal) saveBanks.get("amount")!=null?(BigDecimal) saveBanks.get("amount"):new BigDecimal(0);
        BigDecimal amountPayReturns = (BigDecimal) payReturns.get("amount")!=null?(BigDecimal) payReturns.get("amount"):new BigDecimal(0);
        BigDecimal amountAll = amountAvailable.add(amountSaveBanks).add(amountPayReturns);
        map.put("available",available);  //尚在手中的
        map.put("saveBanks",saveBanks);  //存入银行的
        map.put("payReturns",payReturns);  //又付出去的
        map.put("numAll",numAll);  //总张数
        map.put("amountAll",amountAll);  //总金额
        return map;
    }

    @Override
    public Map<String, Object> updateReturnDetail(User user,FinanceReturn financeReturn) {
        Map<String, Object> map = new HashMap<>();
        if (financeReturn.getId()!=null){
            FinanceReturn financeReturnOld = financeReturnDao.get(financeReturn.getId());
            FinanceAccountBill financeAccountBillOld = getBill(financeReturnOld.getId());
            if (financeReturnOld!=null&&financeReturnOld.getId()!=null){
                FinanceReturnHistory financeReturnHistory = getFinanceReturnHistoryByReturnId(financeReturn.getId()); //已有数据历史
                if (financeReturnHistory==null){
                    financeReturnHistory = financeReturnToReturnHistry(user,financeReturnOld,financeAccountBillOld,0); //老数据添加历史数据
                }

                //数据录入的可以改
                if (financeAccountBillOld.getSource().equals("1")){
                    if (!financeReturn.getCategory().equals(financeReturnOld.getCategory())){
                        financeReturnOld.setCategory(financeReturn.getCategory());//种类/数据类别 1-贷款,2-借款,3-投资款,4-废品,5-其他
                    }
                    if (!financeReturn.getCategoryDesc().equals(financeReturnOld.getCategoryDesc())){
                        financeReturnOld.setCategoryDesc(financeReturn.getCategoryDesc());//其他时候的说明
                    }

                    if (!financeReturn.getSummary().equals(financeReturnOld.getSummary())){
                        financeReturnOld.setSummary(financeReturn.getSummary()); //摘要
                        financeAccountBillOld.setSummary(financeReturn.getSummary());
                    }

                    if (!financeReturn.getPurpose().equals(financeReturnOld.getPurpose())){
                        financeReturnOld.setPurpose(financeReturn.getPurpose());//用途
                        financeAccountBillOld.setPurpose(financeReturn.getPurpose());
                    }

                    if (financeReturn.getBillAmount().compareTo(financeReturnOld.getBillAmount())!=0){
                        financeReturnOld.setBillAmount(financeReturn.getBillAmount());//票面金额(所开具发票或收据的金额)
//                        financeAccountBillOld.setBillAmount(financeReturn.getBillAmount()); //票面金额(所开具发票或收据的金额)
                    }

                    if (!financeReturn.getOperatorName().equals(financeReturnOld.getOperatorName())){
                        financeReturnOld.setOperatorName(financeReturn.getOperatorName());//经手人
                        financeAccountBillOld.setOperatorName(financeReturn.getOperatorName());
                    }

                    if (!financeReturn.getMemo().equals(financeReturnOld.getMemo())){
                        financeReturnOld.setMemo(financeReturn.getMemo());//备注
                        financeAccountBillOld.setMemo(financeReturn.getMemo());
                    }
                }

                if (financeReturn.getReceiveDate()!=financeReturnOld.getReceiveDate()){
                    financeReturnOld.setReceiveDate(financeReturn.getReceiveDate());//收到票据日期
                }

                if (!financeReturn.getPayer().equals(financeReturnOld.getPayer())){
                    financeReturnOld.setPayer(financeReturn.getPayer());//付款单位
                }

                if (!financeReturn.getReturnNo().equals(financeReturnOld.getReturnNo())){
                    financeReturnOld.setReturnNo(financeReturn.getReturnNo()); //支票号码
                    financeAccountBillOld.setBillNo(financeReturn.getReturnNo());//支票号码
                }

                if (financeReturn.getAmount().compareTo(financeReturnOld.getAmount())!=0){
                    BigDecimal oldAmount=financeReturnOld.getAmount();
                    financeReturnOld.setAmount(financeReturn.getAmount());//金额

//                    financeAccountBillOld.setAmount(financeReturn.getAmount()); //金额

                    if ("2".equals(financeReturnOld.getState())){   //1-有效,2-存入银行,3-作废，4-已支出
                        AccountDetail accountDetail = dataService.getByBillId(financeAccountBillOld.getId()); //原来的存入银行的详情
                        //修改前的账户信息(老账户)
                        FinanceAccount financeAccount = financeAccountDao.get(financeReturnOld.getAccountId());
                        BigDecimal balance = financeAccount.getBalance().subtract(financeReturn.getAmount()).add(financeReturn.getAmount());
                        if (balance.compareTo(new BigDecimal(0))>0){  //账户余额有剩余
                            if (financeAccount.getId() != null) {
                                financeAccount.setBalance(financeAccount.getBalance().subtract(financeReturn.getAmount()).add(financeReturn.getAmount()));
                                financeAccount.setCredit(financeAccount.getCredit().subtract(financeReturn.getAmount()).add(financeReturn.getAmount()));
                                financeAccountDao.update(financeAccount);
                            }

                            /**生成的负数据(老账户的)*/
                            AccountDetail accountDetail1 = new AccountDetail();
                            BeanUtils.copyPropertiesIgnoreNull(accountDetail,accountDetail1);
                            accountDetail1.setAccountDetailHistories(null);
                            accountDetail1.setId(null);
                            accountDetail1.setType("3");  //1-初始金额冲减,2-转帐交易,3-收入,4-支出
                            accountDetail1.setAccountId(financeAccount);
                            accountDetail1.setFid(financeAccount.getId().toString());  //账户id
                            accountDetail1.setBillDetail(financeAccountBillOld);  //对应的bill表的数据
                            accountDetail1.setOrg(user.getOrganization());
                            accountDetail1.setCredit(oldAmount.multiply(new BigDecimal(-1)));  //负数表示
                            accountDetail1.setBillAmount(financeAccountBillOld.getBillAmount());  //票面金额
                            accountDetail1.setCreateName(user.getUserName());
                            accountDetail1.setCreateDate(new Date());
                            accountDetail1.setCreator(user.getUserID());
                            accountDetail1.setBalance(financeAccount.getBalance());
                            accountDetail1.setModityStatus("2");
                            accountDetail1.setAuditDate(new Date());
                            accountDetail1.setBillDate(new Date());
                            accountDetail1.setPreviousId(accountDetail.getId());
                            accountDetail1.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)

                            //修改前账户的日结
                            AccountPeriod accountPeriodDay = accountService.getAccountPeriodByDay(financeAccount.getId(),new Date());
                            if (accountPeriodDay != null) {
                                accountPeriodDay.setCredit(accountPeriodDay.getCredit().subtract(financeReturn.getAmount()).add(financeReturn.getAmount()));  //收入
                                accountPeriodDay.setBalance(accountPeriodDay.getBalance().subtract(financeReturn.getAmount()).add(financeReturn.getAmount()));  // 本期余额
                                accountDetail1.setAccount(accountPeriodDay.getId().toString());//期表日结的id(记录属于哪一期)
                            }

                            /*修改前账户的月结*/
                            AccountPeriod accountPeriodMonth = accountService.getAccountPeriodByMonth(financeAccount.getId(),new Date());
                            if (accountPeriodMonth != null) {
                                accountPeriodMonth.setCredit(accountPeriodMonth.getCredit().subtract(financeReturn.getAmount()).add(financeReturn.getAmount()));
                                accountPeriodMonth.setBalance(accountPeriodMonth.getBalance().subtract(financeReturn.getAmount()).add(financeReturn.getAmount()));
                            }
                            /**生成的负数据(老账户的)*/
                            FinanceAccountBill financeAccountBillNew=new FinanceAccountBill();
                            BeanUtils.copyPropertiesIgnoreNull(financeAccountBillOld,financeAccountBillNew);
                            financeAccountBillNew.setBillAmount(financeReturn.getBillAmount()); //票面金额(所开具发票或收据的金额)
                            financeAccountBillNew.setAmount(financeReturn.getAmount()); //金额
                            financeAccountBillNew.setFinanceAccountBillHistoryHashSet(null);
                            financeAccountBillNew.setAccountDetailHashSet(null);
                            financeAccountBillDao.save(financeAccountBillNew);

                            /*添加账务明细账表（正确账户）, 如果新添加一条汇款票据，那么bill表里也需要添加一条数据*/
                            AccountDetail accountDetail2 = new AccountDetail();
                            accountDetail2.setType("3");  //1-初始金额冲减,2-转帐交易,3-收入,4-支出
                            accountDetail2.setAccount(accountDetail1.getAccount());
                            accountDetail2.setAccountId(financeAccount);
                            accountDetail2.setFid(financeAccount.getId().toString());  //账户id
                            accountDetail2.setOrg(user.getOrganization());
                            accountDetail2.setMethod(accountDetail.getMethod());  //1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
                            accountDetail2.setCredit(financeReturn.getAmount());
                            accountDetail2.setBillAmount(financeReturn.getBillAmount());//票面金额
                            accountDetail2.setCreateName(user.getUserName());
                            accountDetail2.setCreateDate(new Date());
                            accountDetail2.setCreator(user.getUserID());
                            accountDetail2.setAccountBank(financeReturn.getBankName() + financeReturn.getAccout());//银行名和账号
                            accountDetail2.setBalance(financeAccount.getBalance());
                            accountDetail2.setSummary(financeReturn.getSummary());
                            accountDetail2.setAuditorName(financeReturn.getOperatorName());
                            accountDetail2.setMemo(financeReturn.getMemo());
                            accountDetail2.setPurpose(financeReturn.getPurpose());
                            accountDetail2.setAuditDate(new Date());
                            accountDetail2.setSource(accountDetail.getSource());
                            accountDetail2.setBillDate(new Date());
                            accountDetail2.setPreviousId(accountDetail.getId());
                            accountDetail2.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)
                            accountDetail2.setBillDetail(financeAccountBillNew);

                            accountDetail.setModityStatus("2");  //数据已修改
                            accountDetail.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)

                            accountDetailDao.save(accountDetail1);
                            accountDetailDao.save(accountDetail2);
                            accountPeroidDao.update(accountPeriodDay);
                            accountPeroidDao.update(accountPeriodMonth);
                            accountDetailDao.update(accountDetail);
                        }else {
                            map.put("state",2); //账户余额不足
                            map.put("content","账户余额不足"); //账户余额不足
                            return map;
                        }
                    }
                }

                if (financeReturn.getExpireDate()!=financeReturnOld.getExpireDate()){
                    financeReturnOld.setExpireDate(financeReturn.getExpireDate());//发票到日期
                }

                if (!financeReturn.getBankName().equals(financeReturnOld.getBankName())){
                    financeReturnOld.setBankName(financeReturn.getBankName()); //出具票据银行
                }

                if (!financeReturn.getOriginalCorp().equals(financeReturnOld.getOriginalCorp())){
                    financeReturnOld.setOriginalCorp(financeReturn.getOriginalCorp()); //出具票据单位
                }
                financeReturnOld.setUpdateDate(new Date());
                financeReturnOld.setUpdateName(user.getUserName());
                financeReturnOld.setUpdator(user.getUserID());
                financeReturnOld.setOperation("3"); //操作:1-增,2-删,3-修改(在存入银行之前的修改)
                financeReturnDao.update(financeReturnOld);

                //支票明细
                financeAccountBillOld.setUpdateDate(new Date());
                financeAccountBillOld.setUpdateName(user.getUserName());
                financeAccountBillOld.setUpdator(user.getUserID());
                financeAccountBillDao.update(financeAccountBillOld);

                //将修改后的数据添加到历史中
                financeReturnToReturnHistry(user,financeReturnOld,financeAccountBillOld,financeReturnHistory.getVersionNo()+1);
                map.put("state",1); //修改成功
                map.put("content","修改成功"); //修改成功
            }else {
                map.put("state",0); //传递数据有误
                map.put("content","传递数据有误"); //传递数据有误
            }
        }else {
            map.put("state",0); //传递数据有误
            map.put("content","传递数据有误"); //传递数据有误
        }
        return map;
    }

    public FinanceReturnHistory getFinanceReturnHistoryByReturnId(Integer returnId){
        Map<String, Object> map = new HashMap<>();
        String hql = "from FinanceReturnHistory where financeReturnId=:financeReturnId order by id desc";
        map.put("financeReturnId",returnId);
        FinanceReturnHistory financeReturnHistory = (FinanceReturnHistory) financeReturnHistoryDao.getByHQLWithNamedParams(hql,map);
        return financeReturnHistory;
    }

    private FinanceReturnHistory financeReturnToReturnHistry(User user,FinanceReturn financeReturnOld,FinanceAccountBill financeAccountBillOld,Integer versionNo){
        FinanceReturnHistory financeReturnHistory = new FinanceReturnHistory();
        BeanUtils.copyPropertiesIgnoreNull(financeReturnOld,financeReturnHistory);
        financeReturnHistory.setId(null);
        financeReturnHistory.setVersionNo(versionNo);
        financeReturnHistory.setFinanceReturnId(financeReturnOld.getId());
        financeReturnHistory.setFinanceReturn(financeReturnOld);
        financeReturnHistory.setUpdateDate(new Date());
        financeReturnHistory.setUpdateName(user.getUserName());
        financeReturnHistory.setUpdator(user.getUserID());
        financeReturnHistoryDao.save(financeReturnHistory);

        FinanceAccountBillHistory financeAccountBillHistory = new FinanceAccountBillHistory();
        BeanUtils.copyPropertiesIgnoreNull(financeAccountBillOld,financeAccountBillHistory);
        financeAccountBillHistory.setId(null);
        financeAccountBillHistory.setVersionNo(versionNo);
        financeAccountBillHistory.setBillDetail_(financeAccountBillOld.getId());
        financeAccountBillHistory.setBillDetail(financeAccountBillOld);
        financeAccountBillHistory.setReturnBillHistory(financeReturnHistory.getId());
        financeAccountBillHistory.setUpdateDate(new Date());
        financeAccountBillHistory.setUpdateName(user.getUserName());
        financeAccountBillHistory.setUpdator(user.getUserID());
        financeAccountBillHistoryDao.save(financeAccountBillHistory);
        return financeReturnHistory;
    }

    @Override
    public List<FinanceReturnHistory> updateReturnRecords(Integer returnId) {
        List<FinanceReturnHistory> financeReturnHistories = new ArrayList<>();
        if (returnId!=null){
            Map<String, Object> map = new HashMap<>();
            String hql = "from FinanceReturnHistory where financeReturnId=:financeReturnId order by versionNo desc";
            map.put("financeReturnId",returnId);
            financeReturnHistories = financeReturnHistoryDao.getListByHQLWithNamedParams(hql,map);
        }
        return financeReturnHistories;
    }

    /**
     *统计中的存入银行/又付出去的票据的修改
     * @param user
     * @param financeReturn------------ id:票据id  category:种类/数据类别 1-贷款,2-借款,3-投资款,4-废品,5-其他 categoryDesc:其他时候的说明 summary:摘要 purpose:用途  billAmount：票面金额(所开具发票或收据的金额) receiveDate收到票据日期
     *  operatorName:经手人 memo:备注 payer:付款单位  returnNo:支票号码  amount:金额 expireDate:到期日  bankName:出具的银行 originalCorp:原始出具的单位  accountId:存入银行账户id
     *  accout：存入银行帐号 depositDate：存入银行日期 depositorName：存入经手人姓名  receiveAccountDate：到帐日期  oppositeCorp：收款单位  factDate：实际付款日期
     * @return
     */
    @Override
    public Map<String, Object> updateBankAndPay(User user, FinanceReturn financeReturn) {
        Map<String, Object> map = new HashMap<>();
        FinanceReturn financeReturnOld = financeReturnDao.get(financeReturn.getId());  //原本的票据信息
        FinanceAccount financeAccount = accountService.getFinanceAccountById(financeReturnOld.getAccountId()); //原本的银行账户

        //存入银行的需要冲账，判断账户余额是否够冲的
        if ("2".equals(financeReturnOld.getState())){  //1-有效,2-存入银行,3-作废，4-已支出
            if (financeReturn.getAccountId()!=financeReturnOld.getAccountId()){  //修改了存入银行
                BigDecimal ba = new BigDecimal(financeAccount.getBalance().toString());
                BigDecimal balance = ba.subtract(financeReturn.getAmount());
                if (balance.compareTo(new BigDecimal(0))<0){  //余额不足的
                    map.put("status",0);
                    map.put("content","余额不足");
                    return map;
                }
            }
        }

        BeanUtils.copyPropertiesIgnoreNull(financeReturn,financeReturnOld);
        financeReturnOld.setModityStatus("2");  //数据已修改
        financeReturnOld.setUpdator(user.getUserID());
        financeReturnOld.setUpdateName(user.getUserName());
        financeReturnOld.setUpdateDate(new Date());
        financeReturnDao.update(financeReturnOld);

        FinanceAccountBill financeAccountBillOld = this.getBill(financeReturnOld.getId());  //获取原本的bill里面数据
        if (financeReturn.getReturnNo()!=null&&!financeReturnOld.getReturnNo().equals(financeReturn.getReturnNo())){
            financeAccountBillOld.setBillNo(financeReturn.getReturnNo());
        }
        if (StringUtils.isNotEmpty(financeReturn.getOppositeCorp())&&!financeReturnOld.getOppositeCorp().equals(financeReturn.getOppositeCorp())){  //收款单位
            financeAccountBillOld.setOppositeCorp(financeReturn.getOppositeCorp());
        }
        if (financeReturn.getFactDate()!=null&&!financeReturnOld.getFactDate().equals(financeReturn.getFactDate())){  //实际(付款/支出)日期'
            financeAccountBillOld.setFactDate(financeReturn.getFactDate());
        }
        financeAccountBillOld.setModityStatus("2");  //数据已修改
        financeAccountBillOld.setUpdator(user.getUserID());
        financeAccountBillOld.setUpdateName(user.getUserName());
        financeAccountBillOld.setUpdateDate(new Date());
        financeAccountBillDao.update(financeAccountBillOld);

        FinanceReturnHistory financeReturnHistory = getFinanceReturnHistoryByReturnId(financeReturn.getId()); //已有数据历史
        if (financeReturnHistory==null){
            financeReturnHistory = financeReturnToReturnHistry(user,financeReturnOld,financeAccountBillOld,0); //老数据添加历史数据
        }
        //将修改后的数据添加到历史中
        financeReturnToReturnHistry(user,financeReturnOld,financeAccountBillOld,financeReturnHistory.getVersionNo()+1);

        if("3".equals(financeAccountBillOld.getSource())){   //回款录入/回款处置
            SlCollectApplication slCollectApplication = slCollectApplicationDao.get(financeAccountBillOld.getBusiness());
            if (financeReturn.getReceiveDate()!=null&&financeReturnOld.getReceiveDate()!=financeReturn.getReceiveDate()){
                slCollectApplication.setReceiveDate(financeReturn.getReceiveDate()); //收到票据日期
            }
            if (financeReturn.getReturnNo()!=null&&!financeReturnOld.getReturnNo().equals(financeReturn.getReturnNo())){
                slCollectApplication.setReturnNo(financeReturn.getReturnNo()); //支票号
            }
            if (financeReturn.getPayer()!=null&&!financeReturnOld.getPayer().equals(financeReturn.getPayer())){
                slCollectApplication.setPayer(financeReturn.getPayer()); //付款单位
            }
            if (financeReturn.getOppositeCorp()!=null&&!financeReturnOld.getOppositeCorp().equals(financeReturn.getOppositeCorp())){
                slCollectApplication.setOriginalCorp(financeReturn.getOppositeCorp()); //原始出具票据单位
            }
            if (financeReturn.getBankName()!=null&&!financeReturnOld.getBankName().equals(financeReturn.getBankName())){
                slCollectApplication.setBankName(financeReturn.getBankName()); //出具支票银行(银行转账的收款银行名称)
            }
            if (financeReturn.getExpireDate()!=null&&financeReturnOld.getExpireDate()!=financeReturn.getExpireDate()){
                slCollectApplication.setExpireDate(financeReturn.getExpireDate()); //到期日期(银行转账的到账日期)
            }
            slCollectApplicationDao.update(slCollectApplication);
        }

        //存入银行的换银行账户
        if ("2".equals(financeReturnOld.getState()) && financeReturn.getAccountId()!=financeReturnOld.getAccountId()){
            AccountDetail accountDetail = dataService.getByBillId(financeAccountBillOld.getId()); //原来的存入银行的详情
            //修改前的账户信息(老账户)
            if (financeAccount.getId() != null) {
                financeAccount.setBalance(financeAccount.getBalance().subtract(financeReturn.getAmount()));
                financeAccount.setCredit(financeAccount.getCredit().subtract(financeReturn.getAmount()));
                financeAccountDao.update(financeAccount);
            }

            /**生成的负数据(老账户的)*/
            AccountDetail accountDetail1 = new AccountDetail();
            BeanUtils.copyPropertiesIgnoreNull(accountDetail,accountDetail1);
            accountDetail1.setId(null);
            accountDetail1.setType("3");  //1-初始金额冲减,2-转帐交易,3-收入,4-支出
            accountDetail1.setAccountId(financeAccount);
            accountDetail1.setFid(financeAccount.getId().toString());  //账户id
            accountDetail1.setBillDetail(financeAccountBillOld);  //对应的bill表的数据
            accountDetail1.setOrg(user.getOrganization());
            accountDetail1.setCredit(financeReturn.getAmount().multiply(new BigDecimal(-1)));  //负数表示
            accountDetail1.setBillAmount(financeAccountBillOld.getBillAmount());  //票面金额
            accountDetail1.setCreateName(user.getUserName());
            accountDetail1.setCreateDate(new Date());
            accountDetail1.setCreator(user.getUserID());
            accountDetail1.setBalance(financeAccount.getBalance());
            accountDetail1.setModityStatus("2");
            accountDetail1.setAuditDate(new Date());
            accountDetail1.setBillDate(new Date());
            accountDetail1.setPreviousId(accountDetail.getId());
            accountDetail1.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)

            //修改前账户的日结
            AccountPeriod accountPeriodDay = accountService.getAccountPeriodByDay(financeAccount.getId(),new Date());
            if (accountPeriodDay != null) {
                accountPeriodDay.setCredit(accountPeriodDay.getCredit().subtract(financeReturn.getAmount()));  //收入
                accountPeriodDay.setBalance(accountPeriodDay.getBalance().subtract(financeReturn.getAmount()));  // 本期余额
                accountDetail1.setAccount(accountPeriodDay.getId().toString());//期表日结的id(记录属于哪一期)
            }

            /*修改前账户的月结*/
            AccountPeriod accountPeriodMonth = accountService.getAccountPeriodByMonth(financeReturn.getAccountId(),new Date());
            if (accountPeriodMonth != null) {
                accountPeriodMonth.setCredit(accountPeriodMonth.getCredit().subtract(financeReturn.getAmount()));
                accountPeriodMonth.setBalance(accountPeriodMonth.getBalance().subtract(financeReturn.getAmount()));
            }

                //修改后的账户信息
            FinanceAccount financeAccount1 = financeAccountService.getAccountById(financeReturn.getAccountId());
            if (financeAccount1.getId() != null) {
                financeAccount1.setBalance(financeAccount.getBalance().add(financeReturn.getAmount()));
                financeAccount1.setCredit(financeAccount.getCredit().add(financeReturn.getAmount()));
                financeAccountDao.update(financeAccount1);
            }

                //添加一条和原来的支票信息差不多的信息(正确的存入银行的信息)
//                BeanUtils.copyPropertiesIgnoreNull(financeReturn,financeReturn1);
//                financeReturn1.setId(null);
//                        financeReturn1.setReturnNo(financeReturn.getReturnNo());//支票号码
//                        financeReturn1.setType(financeReturn.getType());//1-转账支票 2-承兑汇票
//                        financeReturn1.setCategory(financeReturn.getCategory());//1-贷款,2-借款,3-投资款,4-废品,5-其他
//                        financeReturn1.setCategoryDesc(financeReturn.getCategoryDesc());//其他时候的说明
//                        financeReturn1.setAmount(financeReturn.getAmount());//金额
//                        financeReturn1.setBillAmount(financeReturn.getBillAmount()); //票面金额
//                        financeReturn1.setSummary(financeReturn.getSummary());//摘要
//                        financeReturn1.setPurpose(financeReturn.getPurpose());//用途
//                        financeReturn1.setOperatorName(financeReturn.getOperatorName());//经手人
//                        financeReturn1.setPayer(financeReturn.getPayer());//付款单位
//                        financeReturn1.setOriginalCorp(financeReturn.getOriginalCorp());//出具票据单位
//                        financeReturn1.setBankName(financeReturn.getBankName());//出具票据银行
//                        financeReturn1.setExpireDate(financeReturn.getExpireDate());//发票到期日期
//                        financeReturn1.setReceiveDate(financeReturn.getReceiveDate());//收到票据日期
//                        financeReturn1.setMemo(financeReturn.getMemo());//备注
//                financeReturn1.setState("2"); //1-有效,2-存入银行,3-作废,4-已支出
//                financeReturn1.setSaveBankName(financeAccountBillHistory.getSaveBankName()); //存入银行的名称
//                financeReturn1.setAccout(financeAccountBillHistory.getAccout()); //银行账号
//                financeReturn1.setDepositDate(financeAccountBillHistory.getDepositDate()); //存入时间
//                financeReturn1.setDepositorName(financeAccountBillHistory.getDepositorName()); //经手人
//                financeReturn1.setReceiveAccountDate(financeAccountBillHistory.getReceiveAccountDate());  //到账日期
//                financeReturn1.setAccountId(financeAccountBillHistory.getAccountId());  //存入银行的id
//                financeReturn1.setCreateDate(new Date());
//                financeReturn1.setCreateName(financeReturn.getCreateName());
//                financeReturn1.setCreator(user.getUserID());
//                financeReturn1.setOrg(financeReturn.getOrg());
//                financeReturn1.setMyselfId(financeReturn.getId());  //对应修改前的支票id

                //bill表中新添加支票的明细
//                BeanUtils.copyPropertiesIgnoreNull(financeAccountBill,financeAccountBill1);
//                financeAccountBill1.setId(null);
//                financeAccountBill1.setCreator(userId);
//                financeAccountBill1.setCreateName(user.getUserName());
//                financeAccountBill1.setCreateDate(new Date());
//                financeAccountBill1.setType("1");//1-收入，2-支出
//                financeAccountBill1.setAmount(financeReturn.getAmount());//金额
//                financeAccountBill1.setBillAmount(financeReturn.getBillAmount()); //票面金额
//                financeAccountBill1.setSummary(financeReturn1.getSummary());//摘要
//                financeAccountBill1.setPurpose(financeReturn1.getPurpose());//用途
//                financeAccountBill1.setBillNo(financeReturn1.getReturnNo());//支票号码
//                financeAccountBill1.setOrg(financeAccountBill.getOrg());//机构
//                financeAccountBill1.setOperatorName(financeReturn1.getOperatorName());//经手人
//                financeAccountBill1.setMemo(financeReturn1.getMemo());//备注
//                financeAccountBill1.setAccountantStatus("3"); //会计数据状态  1-未选择  2-已选择  3-财务修改的时使用(修改后正确的数据添加的bill表数据)
//                financeAccountBill1.setModityStatus("2");
//                financeAccountBill1.setMyselfId(financeAccountBill.getId());  //数据修改时，对应修改前支票所对应的bill表的数据(本表)
//                financeAccountBill1.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)
//                        financeAccountBill1.setSource(financeAccountBill.getSource());

                /*添加账务明细账表（正确账户）, 如果新添加一条汇款票据，那么bill表里也需要添加一条数据*/
            AccountDetail accountDetail2 = new AccountDetail();
            accountDetail2.setType("3");  //1-初始金额冲减,2-转帐交易,3-收入,4-支出
            accountDetail2.setAccountId(financeAccount1);
            accountDetail2.setFid(financeAccount1.getId().toString());  //账户id
            accountDetail2.setOrg(user.getOrganization());
            accountDetail2.setMethod(accountDetail.getMethod());  //1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
            accountDetail2.setCredit(financeReturn.getAmount());
            accountDetail2.setBillAmount(financeReturn.getBillAmount());//票面金额
            accountDetail2.setCreateName(user.getUserName());
            accountDetail2.setCreateDate(new Date());
            accountDetail2.setCreator(user.getUserID());
            accountDetail2.setAccountBank(financeReturn.getBankName() + financeReturn.getAccout());//银行名和账号
            accountDetail2.setBalance(financeAccount1.getBalance());
            accountDetail2.setSummary(financeReturn.getSummary());
            accountDetail2.setAuditorName(financeReturn.getOperatorName());
            accountDetail2.setMemo(financeReturn.getMemo());
            accountDetail2.setPurpose(financeReturn.getPurpose());
            accountDetail2.setAuditDate(new Date());
            accountDetail2.setSource(accountDetail.getSource());
            accountDetail2.setBillDate(new Date());
            accountDetail2.setPreviousId(accountDetail.getId());
            accountDetail2.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)

            /*修改后账户的日结*/
            AccountPeriod accountPeriodDay1 = accountService.getAccountPeriodByDay(financeAccount1.getId(),new Date());
            if (accountPeriodDay1 != null) {
                accountPeriodDay1.setCredit(accountPeriodDay1.getCredit().add(financeReturn.getAmount()));  //收入
                accountPeriodDay1.setBalance(accountPeriodDay1.getBalance().add(financeReturn.getAmount()));  // 本期余额

                accountDetail2.setAccount(accountPeriodDay1.getId().toString());//期表日结的id(记录属于哪一期)
            }

                /*修改后账户的月结*/
            AccountPeriod accountPeriodMonth1 = accountService.getAccountPeriodByMonth(financeAccount1.getId(),new Date());
            if (accountPeriodMonth1 != null) {
                accountPeriodMonth1.setCredit(accountPeriodMonth1.getCredit().add(financeReturn.getAmount()));
                accountPeriodMonth1.setBalance(accountPeriodMonth1.getBalance().add(financeReturn.getAmount()));
            }

            accountDetail.setModityStatus("2");  //数据已修改
            accountDetail.setModify(true);  //是否修改,false-未修改(0),true-已修改(1)

            accountDetailDao.save(accountDetail1);
            accountDetailDao.save(accountDetail2);
            accountDetailDao.update(accountDetail);
            accountPeroidDao.update(accountPeriodDay);
            accountPeroidDao.update(accountPeriodDay1);
            accountPeroidDao.update(accountPeriodMonth);
            accountPeroidDao.update(accountPeriodMonth1);
        }
        map.put("status",1);
        map.put("content","修改成功");
        return map;
    }
}
