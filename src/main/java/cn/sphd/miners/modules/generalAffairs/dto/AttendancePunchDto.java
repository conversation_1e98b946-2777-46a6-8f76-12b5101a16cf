package cn.sphd.miners.modules.generalAffairs.dto;

import cn.sphd.miners.common.persistence.BaseEntity;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.generalAffairs.entity.PersonnelAttendancePunch;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceService.PunchType;

import javax.persistence.Column;
import java.util.Date;

/**
 * AttendancePunchDto 用于考勤扫描
 * <AUTHOR>
 * @since 4.0
 * @date 2025-06-11 12:44:50
 **/
public class AttendancePunchDto extends BaseEntity {
    private Integer id;//人事_员工打卡表id(PersonnelAttendancePunch.id)
    private Integer userID;//员工id
    private Date punchTime;//打卡时间
    private Byte workdayType;//`workday_type` TINYINT DEFAULT NULL COMMENT '到岗类型: 类型:1-假/离岗,2-班/到岗,3-待定或到岗又是离岗'
    private Byte punchType;//打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
    private Byte type;//'考勤类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班,0-待定'
    private String businessService;//`business_service` VARCHAR(255) DEFAULT NULL COMMENT '业务表Service',
    private Integer business;//`business` INTEGER DEFAULT NULL COMMENT '业务表id',
    private Date createTime; //创建时间

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUserID() {
        return userID;
    }

    public void setUserID(Integer userID) {
        this.userID = userID;
    }

    public Date getPunchTime() {
        return punchTime;
    }

    public void setPunchTime(Date punchTime) {
        this.punchTime = punchTime;
    }

    public Byte getWorkdayType() {
        return workdayType;
    }

    public void setWorkdayType(Byte workdayType) {
        this.workdayType = workdayType;
    }

    public Byte getPunchType() {
        return punchType;
    }

    public void setPunchType(Byte punchType) {
        this.punchType = punchType;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public String getBusinessService() {
        return businessService;
    }

    public void setBusinessService(String businessService) {
        this.businessService = businessService;
    }

    public Integer getBusiness() {
        return business;
    }

    public void setBusiness(Integer business) {
        this.business = business;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public AttendancePunchDto() {
    }

    //1.342考勤打卡，本构造函数被 WorkAttendanceService.runAttendance 查询打卡使用，如有变更请同步修改；
    public AttendancePunchDto(Integer id, Integer userID, Date punchTime, Byte workdayType, Byte punchType, Byte type, String businessService, Integer business, Date createTime) {
        this.id = id;
        this.userID = userID;
        this.punchTime = punchTime;
        this.workdayType = workdayType;
        this.punchType = punchType;
        this.type = type;
        this.businessService = businessService;
        this.business = business;
        this.createTime = createTime;
    }
}