<template>
  <div class="home">
    <PageHeader showBack="true" title="登录">
      <span v-if="quesType==='ownSecurity'" @click="nextBtn">确定</span>
    </PageHeader>
    <div class="contentHome">
      <div>
        <div class="line30">
          您已有89天以上没有登录系统了！ <br>
          为了数据安全，继续登录需回答以下安全问题：
        </div>
        <div v-if="quesType==='ownSecurity'">
          <div v-for="(ownQues, index2) in ownQuesList" :key="index2" class="quesItem">
            安全问题{{ ownQues.orders + 1 }}<br>
            {{ ownQues.question }}<br>
            <el-input class="inputA" v-model="ownQues.ans" placeholder="请输入内容"></el-input>
          </div>
        </div>
        <div v-else class="defaultQuestionCC">
          <div v-if="step===1">
            <div>
              <div>
                安全问题1 <br>
                您曾在以下哪个机构中工作过？请选择，之后点击下一步。
              </div>
              <div>
                <div class="flexCon">
                  <div class="flitem" v-for="(ques , index1) in ques1" :key="index1">
                    <el-radio v-model="ans1" :label="ques">{{ ques }}</el-radio>
                  </div>
                  <div style="clear: both" class="ty-clear"></div>
                </div>
              </div>

            </div>
            <div class="ty-center" style="padding: 30px 0;">
              <span class="ty-btn ty-btn-green ty-circle-3 ty-btn-big" @click="nextBtnStep1">下一步</span>
            </div>
          </div>
          <div v-if="step===2">
            <div class="line30">
              安全问题2 <br>
              您曾在 {{ ans1 }} 服务过。请在下列各个空格内输入该机构您同事的姓名，之后点击确定。
              <div class="ty-color-blue" style="line-height: 20px; margin:10px;">
                注：本问题共有三次回答机会，如第三次还是答不对，则您私人领地内的数据将无法再访问！<br>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;您录入的几个姓名必须与相应同事在系统内的姓名完全一致方算作正确。故如无把握，请向该机构总务人员或您的同事寻求协助！
              </div>
              <div style="line-height: 40px">
                <el-input class="inputA" v-model="staffName1" placeholder="请输入"></el-input>
                <br>
                <el-input class="inputA" v-model="staffName2" placeholder="请输入"></el-input>
                <br>
                <el-input v-if="orgStaffNumber>2" class="inputA" v-model="staffName3" placeholder="请输入"></el-input>
              </div>
              <div class="contrlCon">
                <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 marRight20" @click="okBtn">确定</span>
                <span class=""></span>
              </div>
            </div>
          </div>

        </div>

      </div>
    </div>

  </div>
</template>
<script>
import { h5OwnSecurityQuestion, h5AnswerSecurityQuestion, h5DefaultQuestionBankOne, h5VerifyQuestionBankOne, h5VerifyQuestionBankTwo
} from "@/api/actAcc";

import PageHeader from '@/components/PageHeader.vue'

export default {
  data() {
    return {
      quesType:'ownSecurity',
      step:1,
      ownQuesList:[],
      ans1:'',
      staffName1:'',
      staffName2:'',
      staffName3:'',
      orgStaffNumber:'',

    }
  },
  components: {
    PageHeader
  },
  mounted() {
    this.getQuestions()
  },
  methods: {
    okBtn(){
      if(this.staffName1 === ''){ this.$message.error('请先输入同事姓名！'); return false }
      if(this.staffName2 === ''){ this.$message.error('还有同事姓名未录入！'); return false }
      let userNames = [ this.staffName1, this.staffName2 ]
      if(this.orgStaffNumber > 2){
        if(this.staffName3 === ''){ this.$message.error('还有同事姓名未录入！'); return false }
        userNames = [ this.staffName1, this.staffName2,  this.staffName3  ]
      }
      let params = {
        orgName:this.ans1,
        userNames:userNames.join()
      }
      h5VerifyQuestionBankTwo(params).then(res1=>{
        let res = res1.data
        let success = res.success
        if(success === 1){
          this.$router.push('actOldOrNew')
        }else{
          this.$alert(res.errorMsg, '提示', {
            confirmButtonText: '确定',
            type:'error',
            callback: (action) => {
              if(res.errorMsg === '第三次的提示'){
                this.$router.replace('actAcc1')
              }
            },
          })
        }


      }).catch(err=>{
        console.log('err=',err)
      })
    },
    nextBtnStep1(){
      h5VerifyQuestionBankOne({ orgName:this.ans1 }).then(res1=>{
        let res = res1.data
        let success = res.success
        if(success === 1){
          this.step = 2
          this.orgStaffNumber = Number(res.data)
        }else{
          this.$alert(res.errorMsg, '提示', {
            confirmButtonText: '确定',
            type:'error',
            callback: (action) => {
              if(res.errorMsg === '第三次的提示'){
                this.$router.replace('actAcc1')
              }else{
                this.defaultQuestionBankOneFun()
              }
            },
          })
        }

      }).catch(err=>{
        console.log('err=', err)
      })

    },
    getQuestions(){
      h5OwnSecurityQuestion().then(res1=>{
        let res = res1.data
        let success = res.success
        if(success === 1 && res.data.length > 0){
          this.ownQuesList = res.data

        } else{
          h5DefaultQuestionBankOne().then(res1=>{
            let res = res1.data
            let data = res.data
            this.step = 1
            this.ques1 = data
            this.quesType = 'defaultQuestion'

          }).catch(err=>{
            console.log('err=', err)
          })

          /*
          this.$alert(res.errorMsg , '提示', {
            confirmButtonText: '确定',
            type:'error',
            callback: (action) => {
              window.close();
            },
          })
          */

        }
      }).catch(err=>{
        console.log('err=', err)
      })
    },
    nextBtn(){
      let list = [], errNum = 0
      this.ownQuesList.forEach(ques=>{
        if(ques.ans){
          list.push({ id: ques.id , answer: ques.ans })
        }else{
          errNum++
        }
      })
      if(errNum > 0){
        this.$message.error('请将答案补充完整！')
        return false
      }
      let params = { answers: JSON.stringify(list) }
      h5AnswerSecurityQuestion(params).then(res1=>{
        let res = res1.data
        if(res.success===1){
          this.$router.push('actOldOrNew')
        }else{
          this.$message.error(res.errorMsg)
        }

      }).catch(err=>{
        console.log('err=',err)
      })

    },

  },
}
</script>

<style lang="scss" scoped>
.home{
  .contentHome{
    padding:20px 20px 55px 20px;
    .line30{ line-height: 30px; font-size: 15px; }
    .radioC{ line-height: 20px; margin-top: 40px; }
  }
  .quesItem{
    margin: 16px; line-height: 25px;
  }
  .defaultQuestionCC{
    line-height: 30px; margin-top:20px ;
  }
}
</style>
