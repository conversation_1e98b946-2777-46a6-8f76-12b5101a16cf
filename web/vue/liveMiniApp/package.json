{"name": "liveMiniApp", "version": "0.0.0", "scripts": {"serve": "vite --mode development --host 0.0.0.0 --port 443", "dev": "vite --mode development", "build": "vite build --mode production", "preview": "vite preview --port 5050", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"amfe-flexible": "^2.2.1", "core-js": "^3.6.5", "element-plus": "^2.3.8", "moment": "^2.29.4", "vant": "^3.4.3", "vue": "^3.2.33", "vue-class-component": "^8.0.0-0", "vue-router": "^4.0.14", "vuex": "^4.0.0-0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^4.18.0", "@typescript-eslint/parser": "^4.18.0", "@vitejs/plugin-vue": "^2.3.1", "@vue/cli-plugin-babel": "~4.5.15", "@vue/cli-plugin-eslint": "~4.5.15", "@vue/cli-plugin-router": "~4.5.15", "@vue/cli-plugin-typescript": "~4.5.15", "@vue/cli-plugin-vuex": "~4.5.15", "@vue/cli-service": "~4.5.15", "@vue/compiler-sfc": "^3.0.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^7.0.0", "axios": "^0.27.2", "babel-plugin-import": "^1.13.5", "element-plus": "^2.2.2", "eslint": "^8.5.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^8.2.0", "js-base64": "^3.7.2", "js-cookie": "^3.0.1", "json-bigint": "^1.0.0", "postcss": "^8.4.14", "postcss-pxtorem": "^6.0.0", "prettier": "^2.2.1", "qs": "^6.11.2", "sass": "^1.54.9", "sass-loader": "^13.0.2", "typescript": "~4.1.5", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "vite": "^2.9.5", "vite-plugin-style-import": "^2.0.0"}}