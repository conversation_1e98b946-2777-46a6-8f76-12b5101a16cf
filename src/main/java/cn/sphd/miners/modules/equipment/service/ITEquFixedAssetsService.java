package cn.sphd.miners.modules.equipment.service;

import cn.sphd.miners.modules.equipment.entity.TEquFixedAssets;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;

/**
 * Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
public interface ITEquFixedAssetsService 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    public TEquFixedAssets selectTEquFixedAssetsById(Long id);

    /**
     * 查询列表
     * 
     * @param tEquFixedAssets 
     * @return 集合
     */
    public List<TEquFixedAssets> selectTEquFixedAssetsList(TEquFixedAssets tEquFixedAssets);

    /**
     * 新增
     * 
     * @param tEquFixedAssets 
     * @return 结果
     */
    public int insertTEquFixedAssets(TEquFixedAssets tEquFixedAssets);

    /**
     * 修改
     * 
     * @param tEquFixedAssets 
     * @return 结果
     */
    public int updateTEquFixedAssets(TEquFixedAssets tEquFixedAssets);

    /**
     * 批量删除
     * 
     * @param ids 需要删除的主键集合
     * @return 结果
     */
    public int deleteTEquFixedAssetsByIds(String ids);

    /**
     * 删除信息
     * 
     * @param id 主键
     * @return 结果
     */
    public int deleteTEquFixedAssetsById(Long id);

    public boolean generateEquipmentTools(List<String> subjectNames, User user);
}
