package cn.sphd.miners.modules.commodity.service.impl;

import cn.sphd.miners.modules.accountant.util.DateUtil;
import cn.sphd.miners.modules.commodity.dao.*;
import cn.sphd.miners.modules.commodity.dto.ReqPdObject;
import cn.sphd.miners.modules.commodity.dto.RespCommodityImport;
import cn.sphd.miners.modules.commodity.entity.*;
import cn.sphd.miners.modules.commodity.service.CommodityImportService;
import cn.sphd.miners.modules.commodity.service.ProService;
import cn.sphd.miners.modules.material.dao.MtUnitDao;
import cn.sphd.miners.modules.material.entity.*;
import cn.sphd.miners.modules.material.service.UnitService;
import cn.sphd.miners.modules.sales.dao.PdCustomerDao;
import cn.sphd.miners.modules.sales.dao.SlContractBaseHistoryDao;
import cn.sphd.miners.modules.sales.dao.SlContractCommodityDao;
import cn.sphd.miners.modules.sales.dao.SlContractCommodityHistoryDao;
import cn.sphd.miners.modules.sales.entity.PdModelSettings;
import cn.sphd.miners.modules.sales.entity.SlContractBase;
import cn.sphd.miners.modules.sales.entity.SlContractBaseHistory;
import cn.sphd.miners.modules.sales.entity.SlContractCommodity;
import cn.sphd.miners.modules.sales.service.ContractBaseService;
import cn.sphd.miners.modules.system.entity.ApprovalItem;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.RoleService;
import cn.sphd.miners.modules.trainManage.service.impl.TrainingUsing;
import cn.sphd.miners.modules.uploads.service.UploadService;
import com.alibaba.fastjson.JSONArray;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @ClassName ProductImportServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/08/26 11:19
 * @Version 1.0
 */

@Service("commodityImportService")
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class CommodityImportServiceImpl implements CommodityImportService {

    @Autowired
    PdMerchandiseDao pdMerchandiseDao;
    @Autowired
    PdMerchandiseHistoryDao productHistoryDao;
    @Autowired
    PdCommodityImportDao pdCommodityImportDao;
    @Autowired
    PdCommodityImportLogDao pdCommodityImportLogDao;
    @Autowired
    PdCommodityImportAttachDao pdCommodityImportAttachDao;
    @Autowired
    PdCommodityMediaDao pdCommodityMediaDao;
    @Autowired
    PdCommodityMediaHistoryDao pdCommodityMediaHistoryDao;
    @Autowired
    UploadService uploadService;
    @Autowired
    RoleService roleService;
    @Autowired
    PdBaseHistoryDao pdBaseHistoryDao;
    @Autowired
    ProService proService;
    @Autowired
    PdBaseDao pdBaseDao;
    @Autowired
    ContractBaseService contractBaseService;
    @Autowired
    SlContractBaseHistoryDao slContractBaseHistoryDao;
    @Autowired
    SlContractCommodityDao slContractCommodityDao;
    @Autowired
    SlContractCommodityHistoryDao slContractCommodityHistoryDao;
    @Autowired
    UnitService unitService;
    @Autowired
    MtUnitDao mtUnitDao;
    @Autowired
    PdCustomerDao customerDao;
    @Override
    public List<PdMerchandise> getPdByOid(int org) {
        String hql="from PdMerchandise where org="+org;
        List<PdMerchandise> pdMerchandiseList= pdMerchandiseDao.getListByHQLWithNamedParams(hql,null);
        return pdMerchandiseList;
    }

    @Override
    public List<PdCommodityImport> getImportPdByOid(int org) {
        String hql="from PdCommodityImport where state='1' and org="+org;
        List<PdCommodityImport> productImportList= pdCommodityImportDao.getListByHQLWithNamedParams(hql,null);
        return productImportList;
    }

    @Override
    public Integer whetherUnfinishedImport(int org, Integer isPurchased, String produdctCatetory) {
        int status;
        String hql="from PdCommodityImportLog where org="+org+" and state='2' and produdctCatetory='"+produdctCatetory+"' and isPurchased="+isPurchased;
        List<PdCommodityImportLog> productImportLogList= pdCommodityImportLogDao.getListByHQLWithNamedParams(hql,null);
        if(productImportLogList.isEmpty())
            status=1;
        else
            status=0;
        return status;
    }

    @Override
    public RespStatus updateFalsePdEnter(PdCommodityImport pdCommodityImport) {
        RespStatus respStatus = new RespStatus();
        List<MtUnit> mtUnitList=unitService.selectStopUnitList(pdCommodityImport.getOrg());
        String hql="from PdMerchandise where outerSn='"+pdCommodityImport.getCode()+"' and org="+pdCommodityImport.getOrg();
        List<PdMerchandise> pdMerchandiseList= pdMerchandiseDao.getListByHQLWithNamedParams(hql,null);
        if (pdMerchandiseList.size()>0){
            respStatus.setStatus(2);
            respStatus.setName(pdMerchandiseList.get(0).getOuterName());
        }else
            respStatus.setStatus(1);
        if(respStatus.getStatus()==1&&pdCommodityImport.getUnit()!=null){
            for(MtUnit mtUnit:mtUnitList){
                if(pdCommodityImport.getUnit().equals(mtUnit.getName()))
                {
                    respStatus.setStatus(3);
                }
            }
        }
        if(respStatus.getStatus()==1){
            String hql1="from PdCommodityImport where state='1' and code='"+pdCommodityImport.getCode()+"' and org="+pdCommodityImport.getOrg();
            List<PdCommodityImport> productImportList= pdCommodityImportDao.getListByHQLWithNamedParams(hql1,null);
            if (productImportList.size()>0){
                respStatus.setStatus(4);
                respStatus.setName(productImportList.get(0).getName());
            }
        }
        return respStatus;
    }

    @Override
    public ReqPdObject allImportPdEnter(ReqPdObject reqPdObject) {
        ReqPdObject reqPd=new ReqPdObject();
        String hql="from PdMerchandise where org="+reqPdObject.getOrg();
        List<PdCommodityImport> importList=this.getImportPdByOid(reqPdObject.getOrg());;//正在导入的商品
        List<PdMerchandise> pdMerchandiseList= pdMerchandiseDao.getListByHQLWithNamedParams(hql,null);
        List<MtUnit> mtUnitList=unitService.selectStopUnitList(reqPdObject.getOrg());
        int trueSum=0;
        int falseSum=0;
        for(int i=0;i<reqPdObject.getPdProductImportList().size();i++) {
            PdCommodityImport pdCommodityImport=reqPdObject.getPdProductImportList().get(i);
            int status=1;
            //名称或代号未录入
            //计量单位未录入
            if(pdCommodityImport.getName()==null||"".equals(pdCommodityImport.getName())
                    ||pdCommodityImport.getCode()==null||"".equals(pdCommodityImport.getCode())
                    ||pdCommodityImport.getUnit()==null||"".equals(pdCommodityImport.getUnit())
            )
            {
                status=0;
                falseSum++;
            }
            //本次录入的代号互相重复
            if(status==1) {
                for (int j = 0; j <reqPdObject.getPdProductImportList().size(); j++) {
                    if(pdCommodityImport.getCode().equals(reqPdObject.getPdProductImportList().get(j).getCode())&&i!=j){
                        status=0;
                        falseSum++;
                        break;
                    }
                }
            }
            //本次录入的代号,和已有代号重复
            if(status==1){
                for(PdMerchandise pdMerchandise: pdMerchandiseList){
                    if(pdMerchandise.getOuterSn().equals(pdCommodityImport.getCode())){
                        status=0;
                        falseSum++;
                        break;
                    }
                }
            }
            //1.109改，新增判断，已停用计量单位判断
            if (status == 1) {
                for (MtUnit mtUnit : mtUnitList) {
                    if (mtUnit.getName().equals(pdCommodityImport.getUnit())) {
                        status = 0;
                        falseSum++;
                        break;
                    }
                }
            }
            if (status == 1) {
                for (PdCommodityImport productImport : importList) {
                    if (pdCommodityImport.getCode().equals(productImport.getCode())) {
                        status = 0;
                        falseSum++;
                        break;
                    }
                }
            } if (status == 1) {
                if(reqPdObject.getImportOption()==2){
                    if(reqPdObject.getTaxInclusive()==1&&(pdCommodityImport.getUnitPrice()==null||"".equals(pdCommodityImport.getUnitPrice()))){
                        status = 0;
                        falseSum++;

                    }else if(reqPdObject.getTaxInclusive()==0&&(pdCommodityImport.getUnitPriceNotax()==null||"".equals(pdCommodityImport.getUnitPriceNotax()))){
                        status = 0;
                        falseSum++;
                    }
                }else if(reqPdObject.getProdudctCatetory()==2){
                    if(reqPdObject.getImportOption()==3&&(pdCommodityImport.getUnitPriceInvoice()==null||"".equals(pdCommodityImport.getUnitPriceInvoice()))){
                        status = 0;
                        falseSum++;
                    }
                    else if (reqPdObject.getImportOption()==4&&(pdCommodityImport.getUnitPriceNoinvoice()==null||"".equals(pdCommodityImport.getUnitPriceNoinvoice()))){
                        status = 0;
                        falseSum++;
                    }
                }
            }
            if(status==1){
                trueSum++;
            }
        }
        reqPd.setImportSum(reqPdObject.getImportSum());
        reqPd.setFalseImportSum(falseSum);
        reqPd.setTureImportSum(trueSum);
        return reqPd;
    }

    @Override
    public ReqPdObject saveImportPd(ReqPdObject reqPdObject, User user) {
        ReqPdObject reqPd =new ReqPdObject();
        List<RespCommodityImport> truePdList= new ArrayList<>();
        List<PdCommodityImport> falsePdList= new ArrayList<>();
        List<PdMerchandise> pdList = this.getPdByOid(user.getOid());//已有商品
        List<MtUnit> mtUnitList=unitService.selectUpUnitList(user.getOid());
        List<MtUnit> mtUnitStopList=unitService.selectStopUnitList(user.getOid());
        List<PdCommodityImport> importList=this.getImportPdByOid(user.getOid());;//正在导入的商品
        PdCommodityImportLog pdProductImportLog=new PdCommodityImportLog();
        pdProductImportLog.setOrg(user.getOid());
        pdProductImportLog.setIsPurchased(Integer.valueOf(reqPdObject.getIsPurchased()));
        pdProductImportLog.setProdudctCatetory(String.valueOf(reqPdObject.getProdudctCatetory()));
        pdProductImportLog.setCreator(user.getUserID());
        pdProductImportLog.setCreateName(user.getUserName());
        pdProductImportLog.setImportOption(String.valueOf(reqPdObject.getImportOption()));
        pdProductImportLog.setTaxInclusive(reqPdObject.getTaxInclusive());
        pdProductImportLog.setTaxRate(reqPdObject.getTaxRate());
        pdProductImportLog.setState("2");
        pdProductImportLog.setCreateTime(Timestamp.valueOf(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())));
        pdProductImportLog.setOperation("1");
        pdProductImportLog.setVersionNo(1);
        pdCommodityImportLogDao.save(pdProductImportLog);
        for (int i=0;i<reqPdObject.getPdProductImportList().size();i++) {
            RespCommodityImport respCommodityImport=reqPdObject.getPdProductImportList().get(i);
            PdCommodityImport productImport=new PdCommodityImport();
            BeanUtils.copyProperties(respCommodityImport,productImport);
            int status=1;
            //名称或代号未录入
            if (productImport.getName() == null || "".equals(productImport.getName())
                    || productImport.getCode() == null || "".equals(productImport.getCode())
                    || productImport.getUnit() == null || "".equals(productImport.getUnit())
            ) {
                status = 0;
                falsePdList.add(productImport);
            }
            //本次录入的代号互相重复
            if (status == 1) {
                for (int j = 0; j < reqPdObject.getPdProductImportList().size(); j++) {
                    if (productImport.getCode().equals(reqPdObject.getPdProductImportList().get(j).getCode()) && i != j) {
                        status = 0;
                        falsePdList.add(productImport);
                        break;
                    }
                }
            }
            //本次录入的代号,和已有代号重复
            if (status == 1) {
                for (PdMerchandise pdMerchandise : pdList) {
                    if (pdMerchandise.getOuterSn().equals(productImport.getCode())) {
                        status = 0;
                        falsePdList.add(productImport);
                        break;
                    }
                }
            }
            if (status == 1) {
                for (PdCommodityImport pdProductImport : importList) {
                    if (pdProductImport.getCode().equals(productImport.getCode())) {
                        status = 0;
                        falsePdList.add(productImport);
                        break;
                    }
                }
            }
            //1.109改，新增判断，已停用计量单位判断
            if (status == 1) {
                for (MtUnit mtUnit : mtUnitStopList) {
                    if (mtUnit.getName().equals(productImport.getUnit())) {
                        status = 0;
                        falsePdList.add(productImport);
                        break;
                    }
                }
            }
            //1.192新增
            if (status == 1) {
                if(reqPdObject.getImportOption()==2){
                    if(reqPdObject.getTaxInclusive()==1&&(productImport.getUnitPrice()==null||"".equals(productImport.getUnitPrice()))){
                        status = 0;
                        falsePdList.add(productImport);
                    }else if(reqPdObject.getTaxInclusive()==0&&(productImport.getUnitPriceNotax()==null||"".equals(productImport.getUnitPriceNotax()))){
                        status = 0;
                        falsePdList.add(productImport);
                    }
                }else if(reqPdObject.getProdudctCatetory()==2){
                    if(reqPdObject.getImportOption()==3&&(productImport.getUnitPriceInvoice()==null||"".equals(productImport.getUnitPriceInvoice()))){
                        status = 0;
                        falsePdList.add(productImport);
                    }
                    else if (reqPdObject.getImportOption()==4&&(productImport.getUnitPriceNoinvoice()==null||"".equals(productImport.getUnitPriceNoinvoice()))){
                        status = 0;
                        falsePdList.add(productImport);
                    }
                }
            }
            if (status == 1) {
                if(productImport.getUnitId()==null)
                {
                    int type=1;
                    for (MtUnit mtUnit : mtUnitList) {
                        if (mtUnit.getName().equals(productImport.getUnit())) {
                            type = 0;
                            productImport.setUnitId(mtUnit.getId());
                            break;
                        }
                    }
                    if(type==1){
                        MtUnit mtUnit=new MtUnit();
                        mtUnit.setName(productImport.getUnit());
                        mtUnit.setOrg(reqPdObject.getOrg());
                        mtUnit.setCreator(user.getUserID());
                        mtUnit.setCreateDate(new Date());
                        mtUnit.setCreateName(user.getUserName());
                        mtUnit.setOperation("1");
                        mtUnit.setVersionNo(1);
                        mtUnit.setEnalbed(true);
                        mtUnitDao.save(mtUnit);
                        productImport.setUnitId(mtUnit.getId());
                        mtUnitList.add(mtUnit);
                    }
                }
                if(reqPdObject.getCustomer()!=null)
                    productImport.setCustomer(reqPdObject.getCustomer());
                productImport.setOperation("1");
                productImport.setState("1");
                productImport.setCreator(user.getUserID());
                productImport.setCreateName(user.getUserName());
                productImport.setCreateTime(Timestamp.valueOf(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())));
                productImport.setVersionNo(1);
                productImport.setOrg(user.getOid());
                productImport.setImportId(pdProductImportLog.getId());
                pdCommodityImportDao.save(productImport);
                BeanUtils.copyProperties(productImport,respCommodityImport);
                if (respCommodityImport.getPdCommodityImportAttachListJson()!=null&&!"".equals(respCommodityImport.getPdCommodityImportAttachListJson()))
                {
                    List<PdCommodityImportAttach> commodityImportAttachs=new ArrayList<>();
                    if(respCommodityImport.getPdCommodityImportAttachListJson()!=null)
                    {
                        List<PdCommodityImportAttach> commodityImportAttachList = JSONArray.parseArray(respCommodityImport.getPdCommodityImportAttachListJson(), PdCommodityImportAttach.class);
                        for (PdCommodityImportAttach pdCommodityImportAttach : commodityImportAttachList) {
                            pdCommodityImportAttach.setImportCommodity(productImport.getId());
                            pdCommodityImportAttach.setCreateName(user.getUserName());
                            pdCommodityImportAttach.setCreator(user.getUserID());
                            pdCommodityImportAttach.setCreateDate(new Date());
                            pdCommodityImportAttachDao.save(pdCommodityImportAttach);
                            commodityImportAttachs.add(pdCommodityImportAttach);
                            TrainingUsing callback = new TrainingUsing(pdCommodityImportAttach.getId(), pdCommodityImportAttach.getClass());
                            uploadService.addFileUsing(callback, pdCommodityImportAttach.getUplaodPath(), pdCommodityImportAttach.getTitle(), user, "商品管理");
                        }
                        respCommodityImport.setPdCommodityImportAttachList(commodityImportAttachs);
                    }
                }
                truePdList.add(respCommodityImport);
            }
        }
        pdProductImportLog.setProductCount(reqPdObject.getImportSum());
        pdProductImportLog.setProductSuccess(truePdList.size());
        pdCommodityImportLogDao.save(pdProductImportLog);
        reqPd.setImportSum(reqPdObject.getImportSum());
        reqPd.setFalseImportSum(falsePdList.size());
        reqPd.setTureImportSum(truePdList.size());
        reqPd.setPdProductImportList(truePdList);
        reqPd.setTaxRate(reqPdObject.getTaxRate());
        reqPd.setImportOption(reqPdObject.getImportOption());
        reqPd.setTaxInclusive(reqPdObject.getTaxInclusive());
        reqPd.setIsSaled(reqPdObject.getIsSaled());
        return reqPd;
    }

    @Override
    public Integer getNoUnitNumberCountByOrg(Integer org, String isPurchased, Integer type) {
        int count=0;
        String hql="from PdCommodityImportLog where org="+org +" and state='2' and produdctCatetory='"+type+"' and isPurchased="+isPurchased;
        List<PdCommodityImportLog> productImportLogList= pdCommodityImportLogDao.getListByHQLWithNamedParams(hql,null);
        if(productImportLogList.size()>0) {
            String hql1 = "from PdCommodityImport where importId="+productImportLogList.get(0).getId()+" and unitId is null";
            List<PdCommodityImport> productImportList = pdCommodityImportDao.getListByHQL(hql1);
            count = productImportList.size();
        }
        return count;
    }

    @Override
    public ReqPdObject unfinishedImportPd(User user, String isPurchased, Integer type) {
        ReqPdObject reqPdObject=new ReqPdObject();
        PdCommodityImportLog pdProductImportLog=new PdCommodityImportLog();
        List<PdCommodityImport>productImportList=new ArrayList<>();
        String hql="from PdCommodityImportLog where org="+user.getOid() +" and state='2' and produdctCatetory='"+type+"' and isPurchased="+isPurchased;
        List<PdCommodityImportLog> productImportLogList= pdCommodityImportLogDao.getListByHQLWithNamedParams(hql,null);
        if(productImportLogList.size()>0) {
            String hql1="from PdCommodityImport where importId="+productImportLogList.get(0).getId();
            productImportList= pdCommodityImportDao.getListByHQLWithNamedParams(hql1,null);
            pdProductImportLog=productImportLogList.get(0);
            reqPdObject.setImportOption(Integer.valueOf(pdProductImportLog.getImportOption()));
            reqPdObject.setTaxRate(pdProductImportLog.getTaxRate());
            reqPdObject.setTaxInclusive(pdProductImportLog.getTaxInclusive());
            reqPdObject.setIsSaled(pdProductImportLog.getIsPurchased());
        }
        List<RespCommodityImport> respCommodityImportList=new ArrayList<>();
        for(PdCommodityImport commodityImport: productImportList){
            RespCommodityImport respCommodityImport=new RespCommodityImport();
            BeanUtils.copyProperties(commodityImport,respCommodityImport);
            String hql2="from PdCommodityImportAttach where importCommodity="+commodityImport.getId()+" and type='1'";
            List<PdCommodityImportAttach>pdCommodityImportAttachList=pdCommodityImportAttachDao.getListByHQLWithNamedParams(hql2,null);
            respCommodityImport.setPdCommodityImportAttachList(pdCommodityImportAttachList);
//            String hql3="from PdCommodityImportAttach where importCommodity="+commodityImport.getId()+" and type='2'";
//            List<PdCommodityImportAttach>pdCommodityImportAttach=pdCommodityImportAttachDao.getListByHQLWithNamedParams(hql3,null);
//            if(pdCommodityImportAttach.size()>0)
//                respCommodityImport.setVideo(pdCommodityImportAttach.get(0));
            respCommodityImportList.add(respCommodityImport);
        }
        reqPdObject.setPdProductImportList(respCommodityImportList);
        reqPdObject.setImportSum(pdProductImportLog.getProductCount());
        reqPdObject.setTureImportSum(pdProductImportLog.getProductSuccess());
        return reqPdObject;
    }

    @Override
    public Integer finishImportPd(User user, String isPurchased, Integer type, Integer produdctCatetory) {
        int status=0;
        List<PdCommodityImport>productImportList=new ArrayList<>();
        String hql="from PdCommodityImportLog where org="+user.getOid() +" and state='2' and produdctCatetory='"+produdctCatetory+"' and isPurchased="+isPurchased;
        List<PdCommodityImportLog> productImportLogList= pdCommodityImportLogDao.getListByHQLWithNamedParams(hql,null);
        if(productImportLogList.size()>0) {
            PdCommodityImportLog pdProductImportLog=productImportLogList.get(0);

            String hql1="from PdCommodityImport where importId="+productImportLogList.get(0).getId();
            productImportList= pdCommodityImportDao.getListByHQLWithNamedParams(hql1,null);
            if(type==1)
            {
                //进入商品表
                pdProductImportLog.setState("3");
                pdProductImportLog.setUpdator(user.getUserID());
                pdProductImportLog.setUpdateName(user.getUserName());
                pdProductImportLog.setUpdateTime(new Date());
                pdCommodityImportLogDao.update(pdProductImportLog);
                for(PdCommodityImport pdProductImport:productImportList){
                    PdMerchandise pdMerchandise=new PdMerchandise();
                    pdMerchandise.setOrg(pdProductImport.getOrg());
                    pdMerchandise.setModel(pdProductImport.getModel());
                    pdMerchandise.setSpecifications(pdProductImport.getSpecifications());
                    pdMerchandise.setMemo(pdProductImport.getDesc());
                    if(pdProductImport.getMiniStock()==null)
                        pdProductImport.setMiniStock(Double.valueOf(0));
                    pdMerchandise.setMinimumStock(BigDecimal.valueOf(pdProductImport.getMiniStock()));
                    pdMerchandise.setOrg(pdProductImport.getOrg());
                    pdMerchandise.setOuterSn(pdProductImport.getCode());
                    pdMerchandise.setOuterName(pdProductImport.getName());
                    pdMerchandise.setType(produdctCatetory.toString());
                    pdMerchandise.setUnit(pdProductImport.getUnit());
                    pdMerchandise.setUnitId(pdProductImport.getUnitId());
                    //未销售
                    if ("0".equals(isPurchased)) {
                        pdMerchandise.setIsSaled("0");
                        pdMerchandise.setInitialStock(new BigDecimal(0));
                    } else {
                        pdMerchandise.setIsSaled("1");
                    }
                    if(pdProductImport.getInvoiceType()!=null&&!"".equals(pdProductImport.getInvoiceType()))
                        pdMerchandise.setInvoiceCategory(pdProductImport.getInvoiceType());
                    else
                        pdMerchandise.setInvoiceCategory("4");
                    if(pdProductImport.getTaxRate()!=null)
                        pdMerchandise.setTaxRate(BigDecimal.valueOf(pdProductImport.getTaxRate()));
                    if(pdProductImport.getUnitPrice()!=null)
                        pdMerchandise.setUnitPrice(BigDecimal.valueOf(pdProductImport.getUnitPrice()));
                    if(pdProductImport.getUnitPriceNotax()!=null)
                        pdMerchandise.setUnitPriceNotax(BigDecimal.valueOf(pdProductImport.getUnitPriceNotax()));
                    if(pdProductImport.getUnitPriceInvoice()!=null)
                        pdMerchandise.setUnitPriceInvoice(BigDecimal.valueOf(pdProductImport.getUnitPriceInvoice()));
                    if(pdProductImport.getUnitPriceNoinvoice()!=null)
                        pdMerchandise.setUnitPriceNoinvoice(BigDecimal.valueOf(pdProductImport.getUnitPriceNoinvoice()));
                    if(pdProductImport.getUnitPriceReference()!=null)
                        pdMerchandise.setUnitPriceReference(BigDecimal.valueOf(pdProductImport.getUnitPriceReference()));
                    if("2".equals(pdMerchandise.getType())) {
                        if (pdMerchandise.getUnitPrice() == null) {
                            if (pdMerchandise.getUnitPriceReference() != null) {
                                pdMerchandise.setUnitPrice(pdMerchandise.getUnitPriceReference());
                            }
                            if (pdMerchandise.getUnitPriceNotax() != null) {
                                if (pdMerchandise.getTaxRate() != null) {
                                    pdMerchandise.setUnitPrice(pdMerchandise.getUnitPriceNotax().add(pdMerchandise.getUnitPriceNotax().multiply(pdMerchandise.getTaxRate().divide(new BigDecimal(100)))));
                                } else {
                                    pdMerchandise.setUnitPrice(pdMerchandise.getUnitPriceNotax());
                                }

                            }
                            if (pdMerchandise.getUnitPriceInvoice() != null) {
                                pdMerchandise.setUnitPrice(pdMerchandise.getUnitPriceInvoice());
                            }
                            if (pdMerchandise.getUnitPriceNoinvoice() != null) {
                                pdMerchandise.setUnitPrice(pdMerchandise.getUnitPriceNoinvoice());
                            }
                        }
                    }else{
                        pdMerchandise.setCategory("0");
                    }
                    if(pdMerchandise.getUnitPrice()!=null){
                        pdMerchandise.setHasInvoice("1");
                    }
                    if(pdMerchandise.getUnitPriceReference()!=null){
                        pdMerchandise.setHasInvoice("0");
                    }
                    if(pdMerchandise.getUnitPriceNoinvoice()!=null){
                        pdMerchandise.setHasInvoice("0");
                    }
                    if(pdMerchandise.getUnitPriceNotax()!=null){
                        pdMerchandise.setHasInvoice("1");
                    }
                    if(pdMerchandise.getUnitPriceInvoice()!=null){
                        pdMerchandise.setHasInvoice("1");
                    }
                    pdMerchandise.setEnabled("1");
                    pdMerchandise.setUnitDeduction(0);
                    pdMerchandise.setCreator(user.getUserID());
                    pdMerchandise.setCreateName(user.getUserName());
                    pdMerchandise.setCreateDate(new Date());
                    pdMerchandise.setPeroid(Integer.valueOf(DateUtil.getDy(pdMerchandise.getCreateDate())));
                    pdMerchandise.setOperation("1");
                    pdMerchandise.setVersionNo(1);

                    if(pdProductImport.getCustomer()!=null){
                        SlCustomer customer=customerDao.get(pdProductImport.getCustomer());
                        if (customer!=null){
                            pdMerchandise.setCustomerName(customer.getName());
                            pdMerchandise.setCustomer(customer);
                        }
                    }
                    pdMerchandiseDao.save(pdMerchandise);
                    //关联处理
                    Integer pdId=association(user,pdMerchandise);
                    if (pdId!=null&&pdId!=0){
                        pdMerchandise.setProduct_(pdId);
                        pdMerchandiseDao.update(pdMerchandise);
                    }
                    int module;
                    if("1".equals(pdMerchandise.getType())){
                        module=1;
                    }else{
                        module=2;
                    }

                    unitService.selectUnit(pdMerchandise.getUnitId(),module);
                    Integer phId=productHistoryDao.insert(pdMerchandise);
                    productHistoryDao.insert(pdMerchandise,"5");
                    String sqlMedia="from PdCommodityImportAttach where importCommodity="+pdProductImport.getId();
                    List<PdCommodityImportAttach>pdCommodityImportAttachList=pdCommodityImportAttachDao.getListByHQLWithNamedParams(sqlMedia,null);
                    for (PdCommodityImportAttach pdCommodityImportAttach:pdCommodityImportAttachList) {
                        PdCommodityMedia pdCommodityMedia = new PdCommodityMedia();
                        pdCommodityMedia.setOrg(user.getOid());
                        pdCommodityMedia.setCreator(user.getUserID());
                        pdCommodityMedia.setCreateDate(new Date());
                        pdCommodityMedia.setCreateName(user.getUserName());
                        pdCommodityMedia.setUplaodPath(pdCommodityImportAttach.getUplaodPath());
                        pdCommodityMedia.setOrders(pdCommodityImportAttach.getOrders());
                        pdCommodityMedia.setType(pdCommodityImportAttach.getType());
                        pdCommodityMedia.setTitle(pdCommodityImportAttach.getTitle());
                        pdCommodityMedia.setCommodity(pdMerchandise.getId());
                        pdCommodityMedia.setOperation("1");
                        pdCommodityMedia.setVersionNo(0);
                        pdCommodityMediaDao.save(pdCommodityMedia);
                        pdCommodityMediaDao.getSession().flush();
                        PdCommodityMediaHistory cmh = pdCommodityMediaHistoryDao.insert(pdCommodityMedia, phId);
                        //添加文件库
                        ProductUsing cmu = new ProductUsing(pdCommodityMedia.getId(), PdCommodityMedia.class);
                        uploadService.addFileUsing(cmu, pdCommodityMedia.getUplaodPath(), pdCommodityMedia.getTitle(), user, "商品管理");
                        ProductUsing sihu = new ProductUsing(cmh.getId(), PdCommodityMediaHistory.class);
                        uploadService.addFileUsing(sihu, cmh.getUplaodPath(), pdCommodityMedia.getTitle(), user, "商品管理");

                        //删除附件，此处需要新增附件相关修改
                        TrainingUsing callback = new TrainingUsing(pdCommodityImportAttach.getId(),pdCommodityImportAttach.getClass());
                        uploadService.delFileUsing(callback, pdCommodityImportAttach.getUplaodPath(), user);
                    }
                    //判断是否关联合同
                    if(pdMerchandise.getContractId()!=null&&!"0".equals(pdMerchandise.getContractId().toString())){

                        SlContractBase slContractBase=contractBaseService.getContractBase(pdProductImport.getContractId());
                        if(slContractBase!=null){
                            slContractBase.setOperation("3");
                            slContractBase.setVersionNo(slContractBase.getVersionNo()+1);
                            SlContractBaseHistory his=slContractBaseHistoryDao.insert(slContractBase);
                            //查找所有商品,添加歷史
                            List<SlContractCommodity> pros=slContractCommodityDao.getListByHQL("from SlContractCommodity where contract="+slContractBase.getId());
                            for (SlContractCommodity sc:pros) {
                                slContractCommodityHistoryDao.insert(sc,his.getId(),null);
                            }
                            SlContractCommodity scc=new SlContractCommodity();
                            scc.setOrg(user.getOid() );
                            scc.setCreator(user.getUserID());
                            scc.setCreateDate(new Date());
                            scc.setCreateName(user.getUserName());
                            scc.setOrders(0);
                            scc.setContract(pdMerchandise.getContractId());
                            scc.setCommodity(pdMerchandise.getId() );
                            scc.setOperation("1");
                            scc.setVersionNo(0);
                            slContractCommodityDao.save(scc);
                            slContractCommodityHistoryDao.insert(scc,his.getId(),null);
                        }
                    }
                }
                status=1;
            }
            else if(type==0)
            {
                for(PdCommodityImport pdProductImport:productImportList) {
                    String sqlMedia = "from PdCommodityImportAttach where importCommodity=" + pdProductImport.getId();
                    List<PdCommodityImportAttach> pdCommodityImportAttachList = pdCommodityImportAttachDao.getListByHQLWithNamedParams(sqlMedia, null);
                    for (PdCommodityImportAttach pdCommodityImportAttach : pdCommodityImportAttachList) {
                        //删除附件，此处需要新增附件相关修改
                        TrainingUsing callback = new TrainingUsing(pdCommodityImportAttach.getId(),pdCommodityImportAttach.getClass());
                        uploadService.delFileUsing(callback, pdCommodityImportAttach.getUplaodPath(), user);
                        pdCommodityImportAttachDao.delete(pdCommodityImportAttach);
                    }
                }
                pdCommodityImportDao.deleteAll(productImportList);
                pdCommodityImportLogDao.delete(productImportLogList.get(0));
                status=1;
            }
        }
        return status;
    }

    // 关联相关内容
    public Integer association(User user,PdMerchandise product){
        //是否简易模式
        ApprovalItem jyItem = roleService.getCurrentItem(user.getOid(),"finishedProductCheck");
        //是否自动关联
        ApprovalItem approvalItem = roleService.getCurrentItem(user.getOid(), "commodityProduct");
        PdModelSettings pdModelSettings = null;
        //判断是否重复
        if (approvalItem != null||jyItem!=null) {
            pdModelSettings = roleService.getPdModelSettings(approvalItem.getId());
            if (pdModelSettings != null||(jyItem!=null&&jyItem.getStatus()==0)) {
                //业务处理
                PdBase pdBase = pdBaseDao.getByHQL("from PdBase where innerSn='" + product.getOuterSn() + "' and oid=" + user.getOid());
                if (pdBase != null) {
                    return 0; //该图号已存在
                }
            }
        }
        PdBase pdBase = new PdBase();
        //判断是否新增产品
        if (approvalItem != null||jyItem!=null) {
            if (("1".equals(product.getType()) && pdModelSettings != null && pdModelSettings.getGeneralModel() == 2)
                    || ("2".equals(product.getType()) && (pdModelSettings != null && pdModelSettings.getDedicatedModel() == 2))
                    || (jyItem != null && jyItem.getStatus() == 0)) {
                //自动关联产品
                pdBase.setType("1");
                pdBase.setUnit(product.getUnit());
                pdBase.setUnitId(product.getUnitId());
                pdBase.setModel(product.getModel());
                pdBase.setSpecifications(product.getSpecifications());
                pdBase.setInnerSn(product.getOuterSn());
                pdBase.setName(product.getOuterName());
                pdBase.setOid(user.getOid());
                pdBase.setEnabled("1");
                pdBase.setProcessDeptName("未知");
                pdBase.setCreateName("系统");
                pdBase.setCreateDate(new Date());
                pdBase.setOperation("1");
                pdBase.setVersionNo(1);

                if ("".equals(pdBase.getWeightUnit())) {
                    pdBase.setWeightUnit(null);
                }
                if ("".equals(pdBase.getComposition())) {
                    pdBase.setComposition(null);
                }
                pdBaseDao.save(pdBase);
                pdBaseDao.getSession().flush();
                pdBaseHistoryDao.setHistory(pdBase);

                product.setProduct(pdBase);
                //自动关联
                User u = new User();
                u.setUserName("系统");
                proService.confirmAssociation(product.getId(), pdBase.getId(), u);

                if (pdBase.getUnitId() != null) {
                    //更新计量单位
                    unitService.selectUnit(pdBase.getUnitId(), 3);
                }


            }
        }
        return pdBase.getId();
    }
    @Override
    public RespStatus updateImportPd(RespCommodityImport pdProductImport, User user) {
        int status=0;
        List<PdCommodityImport> productImportList=new ArrayList<>();
        RespStatus respStatus=new RespStatus();
        if(pdProductImport.getCode()!=null&&!"".equals(pdProductImport.getCode())) {
            String hql = "from PdMerchandise where outerSn='" + pdProductImport.getCode() + "'and org=" + user.getOid();
            List<PdMerchandise> pdMerchandiseList = pdMerchandiseDao.getListByHQLWithNamedParams(hql, null);
            if (pdMerchandiseList.isEmpty()) {
            } else {
                status = -1;
                respStatus.setStatus(-1);
                respStatus.setName(pdMerchandiseList.get(0).getOuterName());
            }
            if(status==0) {
                String hql1 = "from PdCommodityImport where code='" + pdProductImport.getCode() + "' and id!="+pdProductImport.getId()+" and state='1' and org=" + user.getOid();
                productImportList = pdCommodityImportDao.getListByHQLWithNamedParams(hql1, null);
                if (productImportList.isEmpty()) {
                } else {
                    status = -1;
                    respStatus.setStatus(-1);
                    respStatus.setName(productImportList.get(0).getName());
                }
            }
        }
        if(status==0) {
            respStatus.setStatus(1);
            PdCommodityImport productImport = pdCommodityImportDao.get(pdProductImport.getId());
            productImport.setName(pdProductImport.getName());
            productImport.setUnitId(pdProductImport.getUnitId());
            productImport.setUnit(pdProductImport.getUnit());
            productImport.setCode(pdProductImport.getCode());
            productImport.setSpecifications(pdProductImport.getSpecifications());
            productImport.setModel(pdProductImport.getModel());
            productImport.setMiniStock(pdProductImport.getMiniStock());
            productImport.setDesc(pdProductImport.getDesc());
            if(pdProductImport.getInvoiceType()!=null&&!"".equals(pdProductImport.getInvoiceType()))
                productImport.setInvoiceType(pdProductImport.getInvoiceType());
            productImport.setTaxRate(pdProductImport.getTaxRate());
            productImport.setUnitPrice(pdProductImport.getUnitPrice());
            productImport.setUnitPriceNotax(pdProductImport.getUnitPriceNotax());
            productImport.setUnitPriceInvoice(pdProductImport.getUnitPriceInvoice());
            productImport.setUnitPriceNoinvoice(pdProductImport.getUnitPriceNoinvoice());
            productImport.setUnitPriceReference(pdProductImport.getUnitPriceReference());
            pdCommodityImportDao.update(productImport);
            String hql1 = "from PdCommodityImportAttach where importCommodity=" +productImport.getId();
            List<PdCommodityImportAttach> deleteList =pdCommodityImportAttachDao.getListByHQLWithNamedParams(hql1,null);
            for (PdCommodityImportAttach deleteAttach : deleteList) {
                pdCommodityImportAttachDao.deleteById(deleteAttach.getId());
                //删除附件，此处需要新增附件相关修改
                TrainingUsing callback = new TrainingUsing(deleteAttach.getId(), deleteAttach.getClass());
                uploadService.delFileUsing(callback, deleteAttach.getUplaodPath(), user);
            }
            if(pdProductImport.getAddPdCommodityImportAttachListJson()!=null&&!"".equals(pdProductImport.getAddPdCommodityImportAttachListJson())) {
                List<PdCommodityImportAttach> commodityImportAttachList = JSONArray.parseArray(pdProductImport.getAddPdCommodityImportAttachListJson(), PdCommodityImportAttach.class);
                for (PdCommodityImportAttach addAttach : commodityImportAttachList) {
                    addAttach.setImportCommodity(productImport.getId());
                    addAttach.setCreateName(user.getUserName());
                    addAttach.setCreator(user.getUserID());
                    addAttach.setCreateDate(new Date());
                    pdCommodityImportAttachDao.save(addAttach);
                    TrainingUsing callback = new TrainingUsing(addAttach.getId(), addAttach.getClass());
                    uploadService.addFileUsing(callback, addAttach.getUplaodPath(), addAttach.getTitle(), user, "商品管理");
                }
            }
//            if(pdProductImport.getDeletePdCommodityImportAttachListJson()!=null) {
//                List<PdCommodityImportAttach> deleteList = JSONArray.parseArray(pdProductImport.getDeletePdCommodityImportAttachListJson(), PdCommodityImportAttach.class);
//                for (PdCommodityImportAttach deleteAttach : deleteList) {
//                    pdCommodityImportAttachDao.deleteById(deleteAttach.getId());
//                    //删除附件，此处需要新增附件相关修改
//                    TrainingUsing callback = new TrainingUsing(deleteAttach.getId(), deleteAttach.getClass());
//                    uploadService.delFileUsing(callback, deleteAttach.getUplaodPath(), user);
//                }
//            }
        }
        return respStatus;
    }

    @Override
    public Integer deleteImportPd(Integer id,User user) {
        String hql2="from PdCommodityImportAttach where importCommodity="+id;
        List<PdCommodityImportAttach>pdCommodityImportAttachList=pdCommodityImportAttachDao.getListByHQLWithNamedParams(hql2,null);
        for(PdCommodityImportAttach pdCommodityImportAttach:pdCommodityImportAttachList){
            //删除附件，此处需要新增附件相关修改
            TrainingUsing callback = new TrainingUsing(pdCommodityImportAttach.getId(),pdCommodityImportAttach.getClass());
            uploadService.delFileUsing(callback, pdCommodityImportAttach.getUplaodPath(), user);
        }
        pdCommodityImportAttachDao.deleteAll(pdCommodityImportAttachList);
        PdCommodityImport productImport=pdCommodityImportDao.get(id);
        pdCommodityImportDao.deleteById(id);
        String hql1="from PdCommodityImport where importId="+productImport.getImportId();
        List<PdCommodityImport>productImportList= pdCommodityImportDao.getListByHQLWithNamedParams(hql1,null);
        if(productImportList.isEmpty()){
            pdCommodityImportLogDao.deleteById(productImport.getImportId());
        }
        return 1;
    }

    @Override
    public Integer finishImportPdEnter(User user, String isPurchased, Integer type) {
        int org=user.getOid();
        List<PdCommodityImport> productImportList=new ArrayList<>();
        String hql="from PdCommodityImportLog where org="+org +" and state='2' and produdctCatetory='"+type+"' and isPurchased="+isPurchased;
        List<PdCommodityImportLog> productImportLogList= pdCommodityImportLogDao.getListByHQLWithNamedParams(hql,null);
        if(productImportLogList.size()>0) {
            String hql1 = "from PdCommodityImport where  importId=" + productImportLogList.get(0).getId() + " and unitId is null";
            productImportList = pdCommodityImportDao.getListByHQL(hql1);
        }
        int count=productImportList.size();
        return count;
    }

    @Override
    public PdCommodityImportAttach getPdCommodityImportAttach(int id) {
        return pdCommodityImportAttachDao.get(id);
    }

    @Override
    public PdCommodityImport getPdCommodityImport(int id) {
        return pdCommodityImportDao.get(id);
    }

    @Override
    public int handleImportData(User user) {
        int number=0;
        String hql="from PdCommodityImport where state='3'";
        List<PdCommodityImport>productImportList= pdCommodityImportDao.getListByHQLWithNamedParams(hql,null);
        for(PdCommodityImport pdProductImport:productImportList){
            if((pdProductImport.getModel()!=null&&!"".equals(pdProductImport.getModel()))||(pdProductImport.getSpecifications()!=null&&!"".equals(pdProductImport.getSpecifications()))){
                String hql1="from PdMerchandise where org="+pdProductImport.getOrg()+" and outerSn='"+pdProductImport.getCode()+"'";
                List<PdMerchandise>pdMerchandiseList= pdMerchandiseDao.getListByHQLWithNamedParams(hql1,null);
                if(pdMerchandiseList.size()>0)
                {
                    int x=0;
                    PdMerchandise pdMerchandise=pdMerchandiseList.get(0);
                    if((pdMerchandise.getModel()==null||"".equals(pdMerchandise.getModel()))&&(pdProductImport.getModel()!=null&&!"".equals(pdProductImport.getModel())))
                    {
                        pdMerchandise.setModel(pdProductImport.getModel());
                        x=1;
                    }
                    if((pdMerchandise.getSpecifications()==null||"".equals(pdMerchandise.getSpecifications()))&&(pdProductImport.getSpecifications()!=null&&!"".equals(pdProductImport.getSpecifications())))
                    {
                        pdMerchandise.setSpecifications(pdProductImport.getSpecifications());
                        x=1;
                    }
                    if(x==1) {
                        pdMerchandiseDao.update(pdMerchandise);
                        number++;
                    }
                }
            }
        }
        return number;
    }

}
