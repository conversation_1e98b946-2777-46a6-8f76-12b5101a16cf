package cn.sphd.miners.modules.resourceAuthority.mapper;

import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.accountant.mapper.BaseMapper;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.resourceAuthority.entity.ResCategory;
import cn.sphd.miners.modules.resourceAuthority.entity.ResHistory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

/**
 * Created by 朱思旭 on 2017/10/31.
 */
@Component
public interface ResHistoryMapper extends BaseMapper<ResHistory> {

    //根据权限分页获取换版管理的全部记录
    List<ResHistory> getAllUpdatePublishlistPage(HashMap map);

    //获取某人换版管理的记录
    List<ResHistory> getApplyUpdateFileVersionByUser(Integer userID);

    //获取某个员工在某段时间内换版文件的被批准或驳回的个数
    Integer changeFileVersionNum(HashMap map);

    //获取某个员工30天内被批准或驳回的换版文件
    List<ResHistory> getMonthChangeFile(HashMap map);

    //获取需要某人审批的文件
    List<ResHistory> getApproveFileByUser(HashMap map);

    //统计某人要审批的文件条数
    Integer getNeedApproveFile(HashMap map);

    //统计某人审批过的文件的条数
    Integer getApproveFileByUserNum(HashMap map);

    //获取某人30天内审批的文件
    List<ResHistory> getMonthApproveFile(HashMap map);

    //总务获取终审的文件
    List<ResHistory> getFinalFile(ApprovalProcess ap);

    //统计总务要进行终审的文件
    Integer getFinalFileNum(HashMap map);

    //统计总务要进行终审的文件
    Integer getFinalFileByUserNum(HashMap map);

    //获取某文管30天内终审的文件
    List<ResHistory> getMonthFinalFile(HashMap map);

    //分页获取某个机构所有文件夹下creator不是这个人的文件
    List<ResHistory> getFileByPublishlistPage(HashMap map);

    //获取某个员工申请发布的文件
    List<ResHistory> getPublishFileByUser(Integer userID);

    //获取某个员工在某段时间内发布文件的被批准或驳回的个数
    Integer publishFileNum(HashMap map);

    //获取某个员工30天内被批准或驳回的发布文件
    List<ResHistory> getMonthPublishFile(HashMap map);

    //根据fileId找到第一条历史文件
    ResHistory getFirstFileHistory(ResHistory rh);

    //根据fileId找到最新的历史文件
    ResHistory getNewFileHistory(Integer file);

    //根据fileId找到最新的历史文件（不是禁止复用的版本）
    ResHistory getNewFileHistoryByVersion(Integer file);

    //根据fileId找到所有历史文件
    List<ResHistory> getAllFileHislistPage(HashMap map);

    //分页获取某文件所有的换版记录
    List<ResHistory> getHistoryByFilelistPage(HashMap map);

    //根据file修改第一条历史的数据
    Integer updateHIstoryByfirst(ResHistory rh);

    //根据operation获取移动修改记录
    List<ResHistory> getRecordByOperation(ResHistory rh);

    //获取移动到“回收站”的移动记录
    List<ResHistory> getMovetrashRecord(ResHistory rh);

    //逆向获取移动的记录
    List<ResHistory> getMoveRecordDesc(ResHistory rh);

    //获取最后一条移动记录
    ResHistory getLastMoveRecordlistPage(QueryData query);

    //查看某文件夹下是否有未批准的文件
    List<ResHistory> getHIsFileByCategoryidAndApproveStatus(Integer category);

    //核对编号是否重复
    Integer checckFileSnByHistory(HashMap map);

    //模糊匹配编号前几位一样的文件
    List<ResHistory> searchHisByHeadNum(QueryData query);

    //修改某文件夹下历史文件版本的categoryPath
    Integer updateHisResCategoryPath(HashMap<String, Object> map);

    //修改某次移动历史的categoryPath和名字
    Integer updateHisResCategoryPathAndName(HashMap<String, Object> map);

    //统计某机构全部文件大小或某文件夹下全部文件大小
    Long getHisFileSize(HashMap map);

    //统计某批次上传的文件还没有审批的个数
    Long getNotApproveHisFileNum(HashMap map);

    //获取某批次上传的文件
    List<ResHistory> getListHisFileByBatchUuid(String batchUuid);

    //获取禁用和启用的记录
    List<ResHistory> listHisFileByTemiante(Integer fileId);

}
