<template>
  <div class="lyricTask">
    <Pageheader title="歌曲管理" :showBack="true" :runMyBackFun="true" @myBackFun="myBackFun"/>
    <div class="live-panel live-tr">
      <div>
        <el-button type="text" class="live-right" @click="gopage('newLyric')">新增</el-button>
        系统中歌曲共如下{{ list.length || 0 }}首
        <div class="lit">（带红色！的为歌词尚未上传者）</div>
      </div>
    </div>

    <div class="mainc">
      <div class="lyricList">
        <div class="lyItem" v-for="(item, index) in list" :key="index">
          <div @click="goDetails(item)" class="lyItemC">
            <span class="name"> <span v-if="!item.captionRequired || item.approveStatus === 0" class="tanHao">❗️</span> {{ item.name }}</span>
            <span class="time" v-if="item.captionRequired">{{ item.songDuration  }}
          </span>
            <arrow-right class="arrR" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, toRefs, onMounted } from 'vue';
import { useRouter } from "vue-router";
import { songManagement } from '@/api/music.js'
import JSONBig from 'json-bigint'


const router = useRouter();
let list = ref([])
onMounted(() => {
  localStorage.removeItem('obsNewLyc')
  getList()

});
// 新增歌曲
const gopage = (type) => {
  // 清楚之前新增歌曲的数据
  localStorage.setItem('newlycdata', '')
  let url = 'newLyric'
  console.log('url', url)
  router.push({
    name: url,
    params: {
    },
  });
}
// 获取列表
const getList = () => {
  let data = { 'type': 1 }
  songManagement(data).then(res => {
    console.log('songManagement res',res)
    list.value = res.data.listSongs || []
    console.log('list.value=', list.value)
  }).catch(err => {
    console.log('songManagement',err)
  })
}
// 跳转歌曲详情
const goDetails = (item) => {
  item.seetype = 1 // 1-主播 2- 成员
  if(item.approveStatus === 0){
    localStorage.setItem('obsNewLyc', JSONBig.stringify(item))
    router.push({ name: 'newLyric' });
  }else{
    localStorage.setItem('lyricDetailsItem', JSONBig.stringify(item))
    router.push({ name: 'lyricDetails' });
  }
}
const myBackFun = () => {
  router.push({ name: 'homeManage' });
}
</script>
<style lang="scss">
.lyricTask{
  .mainc{
    color:#333;
  }
  .lit{
     font-size: 0.8em;
     color: $liveRed ;
   }
  .tipc{
    padding:0 10px;
  }
  .tanHao{ font-weight: bolder; color: $liveRed;  }
  .lyricList{
    margin-top: 20px;
    background: #fff;
    .lyItem:nth-child(odd){
      background-color: #eee;
    }
    .lyItemC{
      display: flex;
      padding:0 10px;
      .name{
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 40px;
        flex: 3;
      }
      .arrR{
        width: 16px;
        display: inline-block;
      }
      .time{
        display: inline-block;
        line-height: 43px;
        text-align: right;
        flex: 1;

      }
    }
  }
}
</style>
