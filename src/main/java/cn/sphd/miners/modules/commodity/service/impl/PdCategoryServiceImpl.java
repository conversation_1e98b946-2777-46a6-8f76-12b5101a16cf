package cn.sphd.miners.modules.commodity.service.impl;


import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.commodity.dao.PdCategoryDao;
import cn.sphd.miners.modules.commodity.entity.PdCategory;
import cn.sphd.miners.modules.commodity.service.PdCategoryService;
import cn.sphd.miners.modules.system.entity.User;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class PdCategoryServiceImpl implements PdCategoryService {

    @Autowired
    PdCategoryDao pdCategoryDao;

    @Override
    public List<PdCategory> getFirstCategoryListByOid(Integer oid, PageInfo pageInfo) {
        String hql=" from PdCategory where level=1 and org=:oid";
        Map<String,Object> map=new HashMap<>();
        map.put("oid",oid);
        List<PdCategory> pdCategoryList=pdCategoryDao.getListByHQLWithNamedParams(hql,map,pageInfo);
        for (PdCategory pdCategory:pdCategoryList){
            pdCategory.setChildrenCount(pdCategory.getPdCategoryHashSet().size());
        }
        return pdCategoryList;
    }

    @Override
    public List<PdCategory> getSonCategoryListByPid(Integer parentId,PageInfo pageInfo) {
        String hql=" from PdCategory where parentId=:parentId";
        Map<String,Object> map=new HashMap<>();
        map.put("parentId",parentId);
        List<PdCategory> pdCategoryList=pdCategoryDao.getListByHQLWithNamedParams(hql,map,pageInfo);
        for (PdCategory pdCategory:pdCategoryList){
            pdCategory.setChildrenCount(pdCategory.getPdCategoryHashSet().size());
        }
        return pdCategoryList;
    }


    @Override
    public List<PdCategory> getPathCategoryListById(Integer id) {
        PdCategory pdCategory=pdCategoryDao.get(id);
        String[] str =pdCategory.getPath().split("/");
        List<Integer> idList=new ArrayList<>();
        for (String s:str){
            if (StringUtils.isNotEmpty(s))
                idList.add(Integer.valueOf(s));
        }
        String hql=" from PdCategory where id in (:idList) order by id asc";
//        params.put("id","%/"+id+"/%");
        Map<String,Object> map=new HashMap<>();
        map.put("idList",idList);
        List<PdCategory> pdCategoryList=pdCategoryDao.getListByHQLWithNamedParams(hql,map);
        return pdCategoryList;
    }

    @Override
    public void addPdCategory(User loginUser,Integer pid, String name) {
        PdCategory pCategory=pdCategoryDao.get(pid);
        PdCategory category=new PdCategory();
        category.setOrg(loginUser.getOid());
        category.setLocked(false);  // 不是系统锁定菜单，可以 修改和删除
        category.setFirstGradeId(pCategory.getFirstGradeId());
        category.setLevel(pCategory.getLevel()+1);
        category.setName(name);
        category.setCode("custom"); //自定义
        category.setPdCategory(pCategory);
        category.setCreator(loginUser.getUserID());
        category.setCreateName(loginUser.getUserName());
        category.setCreateTime(new Date());
        pdCategoryDao.save(category);
        category.setPath(pCategory.getPath()+category.getId()+"/");
        pdCategoryDao.update(category);
    }

    @Override
    public JsonResult updatePdCategory(User loginUser, Integer id, String name) {
        PdCategory category=pdCategoryDao.get(id);
        PdCategory pdCategory=getPdCategoryByName(category.getParentId(),name);
        if (pdCategory!=null){
            return new JsonResult(new MyException("这个类别已经有啦！"));
        }
        if (category.getLocked()){
            return new JsonResult(new MyException("操作失败！\n 此类别的名称不可修改！"));
        }else {
            category.setName(name);
            category.setUpdator(loginUser.getUserID());
            category.setUpdateName(loginUser.getUserName());
            pdCategoryDao.update(category);
            return new JsonResult(1,"操作成功！");
        }
    }

    @Override
    public PdCategory getPdCategoryByName(Integer pid, String name) {
        String hql=" from PdCategory where parentId=:pid and name=:name";
        Map<String,Object> map=new HashMap<>();
        map.put("pid",pid);
        map.put("name",name);
        PdCategory pdCategory= (PdCategory) pdCategoryDao.getByHQLWithNamedParams(hql,map);
        return pdCategory;
    }

    @Override
    public JsonResult deletePdCategory(Integer id) {
        PdCategory pdCategory=pdCategoryDao.get(id);
        if (pdCategory.getLocked()){
            return new JsonResult(new MyException("操作失败！\n 此类别不可删除！"));
        }else {
            if (pdCategory.getPdCategoryHashSet().size()>0){
                return new JsonResult(new MyException("操作失败！\n 当前类别下有数据，不可以删除！"));
            }
            pdCategoryDao.delete(pdCategory);
            return new JsonResult(1,"操作成功！");
        }
    }

    @Override
    public void initPdCategory(Integer oid,String name, String code) {
        PdCategory category=new PdCategory();
        category.setOrg(oid);
        category.setLevel(1);
        category.setName(name);
        category.setCode(code);
//        category.setCreator(loginUser.getUserID());
        category.setCreateName("系统");
        category.setCreateTime(new Date());
        pdCategoryDao.save(category);
        category.setFirstGradeId(category.getId());
        category.setPath("/"+category.getId()+"/");
        pdCategoryDao.update(category);

        PdCategory second=new PdCategory();
        second.setPdCategory(category);
        second.setOrg(oid);
        second.setFirstGradeId(category.getFirstGradeId());
        second.setLevel(2);
        second.setName("待分类");
        second.setCode(code);
//        second.setCreator(loginUser.getUserID());
        second.setCreateName("系统");
        second.setCreateTime(new Date());
        pdCategoryDao.save(second);
        second.setPath(category.getPath()+category.getId()+"/");
        pdCategoryDao.update(second);
    }

    @Override
    public PdCategory getPdCategoryById(Integer id) {
        return  pdCategoryDao.get(id);
    }
}
