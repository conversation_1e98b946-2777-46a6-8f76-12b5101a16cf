<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.accountant.mapper.TaccountantSettingHistoryEntityMapper" >
  <resultMap id="BaseResultMap" type="TaccountantSettingHistoryEntity" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="org" property="org" jdbcType="INTEGER" />
    <result column="accountant_setting" property="accountantSetting" jdbcType="INTEGER" />
    <result column="key_" property="key_" jdbcType="VARCHAR" />
    <result column="value_" property="value_" jdbcType="VARCHAR" />
    <result column="enabled" property="enabled" jdbcType="BIT" />
    <result column="enabled_time" property="enabledTime" jdbcType="TIMESTAMP" />
    <result column="operation" property="operation" jdbcType="CHAR" />
    <result column="memo" property="memo" jdbcType="VARCHAR" />
    <result column="creator" property="creator" jdbcType="INTEGER" />
    <result column="create_name" property="createName" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="updator" property="updator" jdbcType="INTEGER" />
    <result column="update_name" property="updateName" jdbcType="VARCHAR" />
    <result column="update_date" property="updateDate" jdbcType="TIMESTAMP" />
    <result column="previous_id" property="previousId" jdbcType="INTEGER" />
    <result column="version_no" property="versionNo" jdbcType="INTEGER" />
  </resultMap>

<!--  <sql id="Base_Column_List" >-->
<!--    id, org, accountant_setting, key_, value_, enabled, enabled_time, operation, memo,-->
<!--    creator, create_name as createName, create_date as createDate, updator, update_name as updateName, update_date as updateDate, previous_id as previousId,-->
<!--    version_no as versionNo-->
<!--  </sql>-->

  <sql id="Base_Column_List" >
    id, org, accountant_setting, key_, value_, enabled, enabled_time, operation, memo,
    creator, create_name , create_date , updator, update_name , update_date , previous_id ,
    version_no
  </sql>

  <select id="listPage" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_accountant_setting_history
  </select>

  <select id="getSingle" resultMap="BaseResultMap" parameterType="TaccountantSettingHistoryEntity" >
    select 
    <include refid="Base_Column_List" />
    from t_accountant_setting_history
    where id = #{id,jdbcType=INTEGER}
  </select>

  <select id="getRecordsByOrg" resultMap="BaseResultMap">

    select
    <include refid="Base_Column_List" />
    from t_accountant_setting_history
    where org = #{oid} order by create_date desc

  </select>

  <select id="getLastShutTime" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_accountant_setting_history
    where org = #{oid} and value_='N' order by create_date desc limit 1
  </select>

  <delete id="delete" parameterType="TaccountantSettingHistoryEntity" >
    delete from t_accountant_setting_history
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <insert id="insert" parameterType="TaccountantSettingHistoryEntity" useGeneratedKeys="true" keyProperty="id">
    insert into t_accountant_setting_history
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="org != null" >
        org,
      </if>
      <if test="accountantSetting != null" >
        accountant_setting,
      </if>
      <if test="key_ != null" >
        key_,
      </if>
      <if test="value_ != null" >
        value_,
      </if>
      <if test="enabled != null" >
        enabled,
      </if>
      <if test="enabledTime != null" >
        enabled_time,
      </if>
      <if test="operation != null" >
        operation,
      </if>
      <if test="memo != null" >
        memo,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="createName != null" >
        create_name,
      </if>
      <if test="createDate != null" >
        create_date,
      </if>
      <if test="updator != null" >
        updator,
      </if>
      <if test="updateName != null" >
        update_name,
      </if>
      <if test="updateDate != null" >
        update_date,
      </if>
      <if test="previousId != null" >
        previous_id,
      </if>
      <if test="versionNo != null" >
        version_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="org != null" >
        #{org,jdbcType=INTEGER},
      </if>
      <if test="accountantSetting != null" >
        #{accountantSetting,jdbcType=INTEGER},
      </if>
      <if test="key_ != null" >
        #{key_,jdbcType=VARCHAR},
      </if>
      <if test="value_ != null" >
        #{value_,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null" >
        #{enabled,jdbcType=BIT},
      </if>
      <if test="enabledTime != null" >
        #{enabledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operation != null" >
        #{operation,jdbcType=CHAR},
      </if>
      <if test="memo != null" >
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createName != null" >
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null" >
        #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateName != null" >
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null" >
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="previousId != null" >
        #{previousId,jdbcType=INTEGER},
      </if>
      <if test="versionNo != null" >
        #{versionNo,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <update id="update" parameterType="TaccountantSettingHistoryEntity" >
    update t_accountant_setting_history
    <set >
      <if test="org != null" >
        org = #{org,jdbcType=INTEGER},
      </if>
      <if test="accountantSetting != null" >
        accountant_setting = #{accountantSetting,jdbcType=INTEGER},
      </if>
      <if test="key_ != null" >
        key = #{key_,jdbcType=VARCHAR},
      </if>
      <if test="value_ != null" >
        value = #{value_,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null" >
        enabled = #{enabled,jdbcType=BIT},
      </if>
      <if test="enabledTime != null" >
        enabled_time = #{enabledTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operation != null" >
        operation = #{operation,jdbcType=CHAR},
      </if>
      <if test="memo != null" >
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createName != null" >
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null" >
        updator = #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateName != null" >
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="previousId != null" >
        previous_id = #{previousId,jdbcType=INTEGER},
      </if>
      <if test="versionNo != null" >
        version_no = #{versionNo,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

</mapper>