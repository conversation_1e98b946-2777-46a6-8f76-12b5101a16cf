<template>
  <div id="approveFileQueryPer" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="查询"></TY_NavTop>
    <div class="container">
      <div class="ui-message">
        <div class="tip tip-success">自 {{beginTime}} 至 {{endTime}}</div>
        <div class="sendContainer overTimeQuery">
          <div v-if="eventType == 3 && approveStatus == 2">
            您经手且最终被批准的文件共{{queryNum}}个
          </div>
          <div v-if="eventType == 3 && approveStatus == 3">
            您经手且最终被驳回的文件共{{queryNum}}个
          </div>
          <div v-if="eventType == 4 && approveStatus == 2">
            已发布/换版/废止的文件共{{queryNum}}个
          </div>
          <div v-if="eventType == 4 && approveStatus == 3">
            驳回的发布/换版/废止申请共{{queryNum}}次
          </div>
        </div>
      </div>
      <div v-if="haveDetail">
        <div class="ui-cells">
          <a class="ui-cell" v-for="(item, index) in hashMapList" :key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <el-row>
                  <el-col :span="12" v-text="item.time"></el-col>
                  <el-col :span="12">{{item.num}}个</el-col>
                </el-row>
              </div>
            </div>
          </a>
        </div>
      </div>
      <div id="monthDetail" v-else>
        <div class="ui-cells">
          <a class="ui-cell" @click="screenJump(item.id)" v-for="(item, index) in queryList" :key="index">
            <div class="fileLists">
              <div class="fileType file_xls"></div>
              <div class="fileInfo">
                <div class="fileName" v-text="item.name" :title="item.name"></div>
                <div class="fileDetail">
                  {{(!item.updateName || item.updateName === null || item.updateName === undefined)?item.createName:item.updateName}}
                  {{(!item.updateDate || item.updateDate === null || item.updateDate === undefined)?item.createDate:item.updateDate}}
                </div>
              </div>
              <div class="fileFormat">
                <span class="fileVersion">G{{item.changeNum}}</span>
                <span class="fileNo">编号：{{item.fileSn}}</span>
              </div>
            </div>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as moment from 'moment'
import 'moment/locale/zh-cn'
export default {
  name: 'approveFileQueryResult',
  data () {
    return {
      queryNum: 0,
      queryList: [],
      queryDetail: {},
      eventType: 3,
      approveStatus: '',
      haveDetail: true,
      hashMapList: [],
      getParams: {},
      beginTime: '',
      endTime: '',
      loading: false
    }
  },
  created: function () {
    this.loading = true
    let paramQuery = this.$route.params.data
    if (!paramQuery) {
      paramQuery = JSON.parse(localStorage.getItem('query'))
    }
    this.getParams = paramQuery
    // eventType :3-审批 4-文管查询
    // type （3- 自定义 ，2 -仅看年报，1- 仅看月报）
    // approveStatus 2-是查已批准的 3-是查驳回的
    // timeBegin  开始时间   查询的时间 此值每次都要传，例如本月时“2019-01”，查本年时“2019”，自定义时传那个设定的开始时间“2019-01-01”
    this.eventType = paramQuery.eventType
    this.approveStatus = paramQuery.approveStatus
    if (this.getParams.type === 1) {
      let tempDate = this.getParams.timeBegin + '-01 12:00:00'
      tempDate = new Date(tempDate)
      let year = tempDate.getFullYear()
      let month = tempDate.getMonth() + 1
      let lastDayOfMonth = new Date(year, month, 0)
      this.beginTime = year + '年' + month + '月01日'
      this.endTime = year + '年' + month + '月' + lastDayOfMonth.getDate() + '日'
    } else if (this.getParams.type === 2) {
      this.beginTime = this.getParams.timeBegin + '年01月01日'
      this.endTime = this.getParams.timeBegin + '年12月31日'
    } else {
      this.beginTime = moment(this.getParams.timeBegin).format('YYYY年MM月DD')
      this.endTime = moment(this.getParams.timeEnd).format('YYYY年MM月DD')
    }
    let _this = this
    this.$http.post('../../../res/threeTypeFindFileByHandleFile.do', paramQuery, {
      emulateJSON: true
    }).then((response) => {
      this.loading = false
      let data = response.body.data
      if (data) {
        this.queryNum = data.sum
        let listResHis = data.listResHis
        if (listResHis) {
          _this.haveDetail = false
          _this.queryList = listResHis
        } else {
          _this.haveDetail = true
          _this.hashMapList = data.timeNumList
        }
      } else {
        console.log('加载失败！')
      }
    }).catch(function () {
      // this.jumpError('系统错误，请重试！')
    })
  },
  methods: {
    screenJump: function (id) {
      this.$router.push({
        path: `/fileResultDetail/${id}`
      })
    }
  }
}
</script>
