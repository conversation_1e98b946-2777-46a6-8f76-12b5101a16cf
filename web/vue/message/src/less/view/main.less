.handle-center{text-align:center;}
.handle-center input,.handle-center a{margin: 0 30px;}
.fc-btn{color: #fff;padding:6px 10px;border:none;cursor: pointer;}
.fd-btn{color: #000;padding:6px 18px;border:none;cursor: pointer;}
.fc-btn-normal{color: #606266;padding:5px 10px;border:1px solid #dcdfe6;}
.fc-btn-green{background:#00ce9b}
.fc-btn-blue{background:#4797f1}
.fc-btn-red{background:#FF0000}
.fc-btn-yellow{background:#FFFF00}
.bg-green{background: #00a87b;}
input:disabled{ background:#ccc;  border:1px solid #ccc;  }
.btn-space{width:42px;text-align-last: justify;}
.hd{
  display: none !important;
}
.right{
  float: right;
}
.ui-input{
  width: 100%;
  border: 0;
  outline: 0;
  -webkit-appearance: none;
  background-color: transparent;
  font-size: inherit;
  color: inherit;
  height: 1.41176471em;
  line-height: 1.41176471;
}
.navigation_wrapper + div{
  margin-top: 48px;
}
/*文件管理*/
.datePane{margin:4px;}
.fileLists{position:relative;width:100%;}
.fileLists:before,.fileLists:after{  clear:both;content:''; display:block; width:0; height:0; visibility:hidden;  }
.clear:before,.clear:after{  clear:both;content:''; display:block; width:0; height:0; visibility:hidden;  }
.fileType{ width: 36px; height: 40px; margin-right:6px; float: left; background: url("../../../../../css/content/img/other.png") no-repeat;background-size:28px; margin-left: -3px }
.fileInfo{float:left;font-size:12px;line-height:20px;}
.fileFormat{position:absolute;top:0;right:0;color:#aaa;;font-size:12px;}
.fileFormat span{display: inline-block;overflow: hidden;}
.fileNo{max-width:80px;text-overflow: ellipsis;white-space: nowrap;}
.file_doc,.file_docx{  background: url("../../../../../css/content/img/doc.png") no-repeat; background-size:36px}
.file_xls,.file_xlsx,.file_et{  background: url("../../../../../css/content/img/xls.png") no-repeat; background-size:36px}
.file_ppt,.file_pptx{  background: url("../../../../../css/content/img/ppt.png") no-repeat; background-size:36px}
.file_rar,.file_zip{  background: url("../../../../../css/content/img/rar.png") no-repeat; background-size:36px}
.file_pdf{  background: url("../../../../../css/content/img/pdf.png") no-repeat; background-size:36px}
.file_md{  background: url("../../../../../css/content/img/md.png") no-repeat; background-size:36px}
.file_jpg,.file_png,.file_gif,.file.jpeg{  background: url("../../../../../css/content/img/jpg.png") no-repeat; background-size:36px}
.file_txt{  background: url("../../../../../css/content/img/txt.png") no-repeat; background-size:36px}
.icon_folder{ width: 30px; height: 24px; margin-right:8px; background: url("../../../../../css/content/img/folder.png") no-repeat; background-size:24px; margin-left: 4px;}

.line-p, .line-s{line-height: 32px; display: flex; flex-direction: row; border-top: 1px solid #f2f2f2;}
.line-p:first-child {border: none}
.line-p:before,.line-p:after{clear:both;content: '';display:block;width:0;height:0;visibility:hidden;}
.line-p .tt-cell{flex: none;width: 150px;}
.line-p .con-cell{flex: auto; text-align: right}
.lineGap{border-top: 1px solid #ddd;}
.script{margin-left:16px;font-size:13px;}
.btn-con{margin-bottom: 4px;padding:4px 10px;border:1px solid #ccc;display: inline-block;font-size: 12px;}

.untreateFile .el-radio + .el-radio {margin-left: 0;}
.untreateFile .el-radio {margin-bottom: 10px;}
.preview {width: 100px; margin: auto;}
.preview .el-radio + .el-radio {margin-left: 0;}
.preview .el-radio {margin-bottom: 10px;}
.approverNext{margin-left:20px;}

.record{padding: 8px 0;}
.record .record_title{ margin-bottom: 8px }
.st-center{margin:auto;text-align:center;}
.kiko-tool-tip{z-index:100010;}
.script span{display: inline-block;}
.script .sm-ttl{width:70px;}
.sm-con{margin-left: 2rem;margin-right:2rem;min-width:3.6rem;max-width:6rem;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;vertical-align: middle;}
.lineOp{border-top:1px solid #ddd}
.def-danger .el-dialog__title{color:red;line-height: 24px;font-size: 16px;}
.def-info .el-dialog__title{font-size: 16px;}
.seeTtl{display:inline-block;width:40%;min-width:130px;margin-right:6px;}
.fc-btn-see{padding:2px 12px; display:inline-block;background: @blue; color:#fff;border:none;font-size:1rem;text-align:center;line-height: 20px;}
.line-btn-groups ul{padding:0;margin:0;}
.line-btn-groups ul li{margin:6px;}
.color-red{color: #ff0000;}
.fileName{max-width: 200px;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}
.handle_button a{text-decoration: none;}
.place{ font-size: 13px; -webkit-box-align: center;  -webkit-align-items: center;  align-items: center; margin-bottom: 8px;
  .placeContainer{
    line-height: 24px; padding: 2px 8px;  background-color: @white;  border-radius: 3px; font-size: 13px;
  }
}
.con-reason{  float:right;  max-width:280px;  }
.fileTtl{  font-size: 15px;  padding: 4px 0 ; font-weight: bold }
.font-orange{  color: #e26b0a;  }
  /*销售回款*/
.tipPane{  text-indent:1.5rem;  line-height:26px;  background: #daeef3;  }
.listCon{  text-indent: 14px;  margin-bottom:4px;  }
.lHead{  border-bottom: 1px solid #ccc;  }
.tt-cl{  display: inline-block;  width:130px;  margin-right:10px;  }
.sub-cl{  display: inline-block;  max-width:200px; vertical-align: top; }
.ellipsisSet{  overflow: hidden;  text-overflow:ellipsis;  white-space: nowrap; vertical-align: middle; }
.inputerInfo{  text-align:right;  }
.hrGap{  border-bottom: 1px solid #ccc;  margin-bottom:18px;  }
.searchPane{  background: #ebf1de;  position:relative;  }
.revokeImg{  position:absolute;  top: 6px;  right: 20px;  width: 74px;  }
.revokeImg img{  width:100%;  }
.checkWay{  margin-left:14px;  }
.listTip{  padding: 10px 0;  text-indent: 26px;  background: #daeef3;  }
.sectCenter {  min-height:120px;  }
.tipH{  color:red  }
.sureTip,.agreeTip{  margin-top:16px;  }
.company{  margin-bottom:4px;  }
.query .el-radio{  display: inline-block;  margin: 0 5px 5px 0;  }
/*潜在客户*/
.ui-col{background-color: #fafafb;  padding: .8rem 1.5rem;  font-size: 1.2rem;  overflow: hidden;  color: inherit;  cursor: pointer;  display: block;}
.col-flex{position: relative;  display: -ms-flexbox;  display: flex;flex-wrap: wrap ;  -ms-flex-align: center;  align-items: center;}
.ui-col__flag{color: #999;  padding-right: 1.3rem;}
.createDetail, .ui-col__flag{text-align: right;}
.ui-col__flag::after {  content: " ";  display: inline-block;  height: .6rem;  width: .6rem;  border-width: .1rem .1rem 0 0;  border-color: #c8c8cd;
  border-style: solid;  transform: matrix(.71,.71,-.71,.71,0,0);  top: -2px;  position: absolute;  top: 50%;  margin-top: -.4rem;  right: 1.2rem;  }
.createDetail {  font-size: 12px;  }
.handleSect a{margin-left: 20px;}
.sect-table thead tr td{  text-align: center;  }
.sect-blue{  padding: 4px 6px;  color: #20a7ff;  cursor:pointer;  white-space: nowrap; }
.sect-blue:hover{  color:#fff;  background: #20a7ff;  }
.sect-red{  padding: 4px 6px;  color: #ff0000;  cursor:pointer; white-space: nowrap;}
.sect-red:hover{  color:#fff;  background: red;  }
.uploadImg .el-upload--picture-card{margin-top: 30px;width: 28px;  height: 28px;border-radius: 50%;line-height: 30px;border: 1px solid #c0ccda;}
.special .el-radio__input, .special .el-radio__input {  display: none;  }
.addMore{text-align:center;}
.gap{margin: 12px 0;}
.definedArea .el-input-group__prepend{  border: none;  padding: 0 0;  }
.el-input-group__prepend .definedInput{  width: 90px;  }
.el-input-group > .el-input__inner {  vertical-align: inherit;  }
.subTtl{display:inline-block;width:92px;}
.uploadBaseImg .el-upload-list--picture-card .el-upload-list__item {height:100px;width:100px;}
.uploadBaseImg .el-upload--picture-card{margin-top: 38px;  width: 30px;  height: 28px;  border-radius: 50%;  line-height: 28px;  border: 1px solid #c0ccda;}
.cus_visitCard{margin:20px auto 0 auto;text-align: center;}
#potentialContact{  font-size: 14px;  }
.def-gap{  margin-bottom:20px;  }
.recorditem{ overflow: hidden;}
.recordDetail { float:right; min-width:200px;}
.editUploadFile .avatar-uploader .el-upload {  border: 1px dashed #d9d9d9;  border-radius: 6px;  cursor: pointer;  position: relative;  overflow: hidden;  }
.editUploadFile .avatar-uploader .el-upload:hover {  border-color: #409EFF;  }
.editUploadFile .avatar-uploader-icon {  font-size: 28px;  color: #8c939d;  width: 178px;  height: 178px;  line-height: 178px;  text-align: center;  }
.editUploadFile .avatar {  width: 178px;  height: 178px;  display: block;  }
.potential .panel-title {  background-color: #daeef3;  font-weight: normal;  }
.potential .el-col {  margin: 6px 0;  }
.put-left {float: left;}
.put-right {float: right;}
.otherInfo {padding:20px 0 0 10px;}
.colorBlue {color: #20a7ff;}
.image-slot{height:200px;}
.image-slot img {max-width: 300px;max-height:200px;}
.defined-radio .el-radio{display:inline-block;margin-right: 0;}
.defined-radio .el-radio__label{font-size:0;}
.defined-radio .el-radio + span{font-size:14px;  cursor: pointer;}
.sureTip{padding: 14px 0;}
.displace{margin-left: 36px;}
.page{padding: 4px 0; text-align: center; background: #fff}
.def-content{background: #fff;  overflow: hidden;}
