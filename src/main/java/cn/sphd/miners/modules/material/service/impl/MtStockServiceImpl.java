package cn.sphd.miners.modules.material.service.impl;

import cn.sphd.miners.common.utils.DateUtils;
import cn.sphd.miners.modules.accountant.util.DateUtil;
import cn.sphd.miners.modules.commodity.dao.PdBaseDao;
import cn.sphd.miners.modules.commodity.dao.PdMerchandiseDao;
import cn.sphd.miners.modules.commodity.entity.PdBase;
import cn.sphd.miners.modules.commodity.entity.PdMerchandise;
import cn.sphd.miners.modules.material.dao.*;
import cn.sphd.miners.modules.material.entity.*;
import cn.sphd.miners.modules.material.service.MtService;
import cn.sphd.miners.modules.material.service.MtStockService;
import cn.sphd.miners.modules.system.entity.User;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

/**
 * Created by Administrator on 2017/8/2.
 */
@Service("stockService")
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class MtStockServiceImpl implements MtStockService {
    @Autowired
    MtStockDao mtStockDao;
    @Autowired
    PdMerchandiseDao pdMerchandiseDao;
    @Autowired
    PdBaseDao pdBaseDao;
    @Autowired
    MtStockInfoDao stockInfoDao;
    @Autowired
    MtInApplicationDao inApplicationDao;
    @Autowired
    MtInApplicationItemDao inApplicationItemDao;
    @Autowired
    MtInApplicationItemCheckDao checkDao;
    @Autowired
    MtInApplicationItemCheckDao itemCheckDao;
    @Autowired
    MtService mtService;

    @Override
    public void saveMtStock(MtStock mtStock) {
        mtStockDao.save(mtStock);
    }

    @Override
    public List<MtStock> getByMtMaterialApply(Integer oid, Integer applyId) {
        String hql = "from MtStock o where o.oid=" + oid + " and o.applyId=" + applyId + " order by o.createDate asc";
        return mtStockDao.getListByHQL(hql);
    }

    @Override
    public MtStock getByIdAndOid(Integer ID, Integer oid) {
        String hql = "from MtStock o where o.id=" + ID + " and o.oid=" + oid;
        return mtStockDao.getByHQL(hql);
    }

    @Override
    public MtStock getById(Integer ID) {
        String hql = "from MtStock o where o.id=" + ID;
        return mtStockDao.getByHQL(hql);
    }

    @Override
    public void updateMtStock(MtStock mtStock) {
        mtStockDao.update(mtStock);
    }

    @Override
    public List<PdMerchandise> getOuterSnByLike(Integer oid, String outerSn, Integer state) {
        String hql = "from PdMerchandise o where o.org=" + oid;
        if (outerSn != null && !"".equals(outerSn)) {
            hql += " and o.outerSn = '" + outerSn + "'";
        }
        if (state != null)
            hql += " and (ifnull(o.currentStock,-1)!=-1 or o.isSaled=0) and o.enabled!=0";
        return pdMerchandiseDao.getListByHQL(hql);
    }

    @Override
    public PdBase getPdBaseById(Integer id) {
        return pdBaseDao.get(id);
    }

    @Override
    public List<MtStock> getMtStockByQualified(Integer oid, String qualified) {
        String hql =
                "from MtStock o where o.applyId in (select t.id from MtMaterialApply t where t.type=1 ) and o.state=2 and o.qualified='"
                        + qualified + "' and o.oid=" + oid + " order by o.createDate asc";
        return mtStockDao.getListByHQL(hql);
    }

    @Override
    public List<MtStock> getMtStockByQualified1(Integer oid, String qualified) {
        String hql =
                "from MtStock o where   o.applyId in (select t.id from MtMaterialApply t where t.type=1 ) and (o.state=4 or o.state=0) and o.qualified='"
                        + qualified + "' and o.oid=" + oid + " order by o.createDate asc";
        return mtStockDao.getListByHQL(hql);
    }

    @Override
    public List<MtStock> getMtStockByQualified2(Integer oid, String qualified) {
        String hql =
                "from MtStock o where   o.applyId in (select t.id from MtMaterialApply t where t.type=1 ) and o.qualified='"
                        + qualified + "' and o.oid=" + oid + " order by o.createDate asc";
        return mtStockDao.getListByHQL(hql);
    }

    @Override
    public List<MtStock> getMtStockByQualifiedByConcession(Integer oid, String qualified) { // 让步单
        String hql =
                "from MtStock m where m.applyId in (select t.id from MtMaterialApply t where t.type=2 )  and m.oid = " + oid
                        + "  and m.qualified ='" + qualified + "'order by m.createDate asc";
        return mtStockDao.getListByHQL(hql);
    }

    @Override
    public List<MtStock> getMtStockAcceptance(Integer oid, String qualified, String state) {
        String hql = "from MtStock o where o.qualified='" + qualified + "' and o.oid=" + oid + " and o.state='" + state
                + "' order by o.createDate asc";
        return mtStockDao.getListByHQL(hql);
    }

    @Override
    public List<MtStock> getMtStockAcceptanceByConcession(Integer oid, String qualified, String state) {
        String hql =
                "from MtStock m where m.applyId in (select t.id from MtMaterialApply t where t.type=2 )  and m.oid = " + oid
                        + "  and m.qualified ='" + qualified + "'  and m.state='" + state + "'order by m.createDate asc";
        return mtStockDao.getListByHQL(hql);
    }

    @Override
    public List<MtStock> getMtStockAcceptance1(Integer oid, String state) {
        String hql =
                "from MtStock o where o.applyId in (select t.id from MtMaterialApply t where t.type=1 )  and  o.oid=" + oid
                        + " and o.state='" + state + "' order by o.createDate asc";
        return mtStockDao.getListByHQL(hql);
    }

    @Override
    public List<MtStock> getMtStockAcceptance1ByConcession(Integer oid, String state) {
        String hql =
                "from MtStock m where m.applyId in (select t.id from MtMaterialApply t where t.type=2 )  and m.oid = " + oid
                        + "  and m.state='" + state + "'order by m.createDate asc";
        return mtStockDao.getListByHQL(hql);
    }

    /**
     * 待让步申请的出入单
     *
     * @param
     * @return
     */
    @Override
    public List<MtStock> getMtStockAcceptanceByConcessionApplication(Integer oid) {
        String hql =
                "from MtStock m where m.applyId in (select t.id from MtMaterialApply t where t.type=1 ) and m.state=4 and m.oid is "
                        + oid + " order by m.createDate asc ";
        return mtStockDao.getListByHQL(hql);
    }

    @Override
    public Map getMtStocksByState(Integer state, Integer qualified, Integer oid, Integer per) {

        String sqlc = "SELECT\n" + "	COUNT(*)\n" + "FROM\n" + "	t_mt_stock ms\n"
                + "LEFT JOIN t_mt_material_apply ma ON ma.id = ms.apply_id\n"
                + "LEFT JOIN t_mt_base mb ON ms.material = mb.id\n" + "LEFT JOIN t_pd_base pb ON mb.product = pb.id\n"
                + "LEFT JOIN t_pd_merchandise pcp ON pb.id = pcp.product\n" + "WHERE ms.oid =" + oid + " AND ms.state ="
                + state + " AND ms.qualified=" + qualified;
        String sql = "SELECT\n" + "	ms.id,\n" + "	ma.create_date,\n" + "	ms.manufacture_date,\n"
                + "	ms.invalid_date,\n" + "	pcp.outer_sn,\n" + "	pcp.outer_name,\n" + "	pb.inner_sn,\n" + "	pb. NAME\n"
                + "FROM\n" + "	t_mt_stock ms\n" + "LEFT JOIN t_mt_material_apply ma ON ma.id = ms.apply_id\n"
                + "LEFT JOIN t_mt_base mb ON ms.material = mb.id\n" + "LEFT JOIN t_pd_base pb ON mb.product = pb.id\n"
                + "LEFT JOIN t_pd_merchandise pcp ON pb.id = pcp.product\n"
                + "WHERE ms.oid=? AND ms.state=? AND ms.qualified=?";

        return mtStockDao.findMapByCondition(sql, sqlc, new String[]{state + "", oid + "", qualified + ""}, per);
    }

    @Override
    public Map getMtStocksByStateAndType(Integer state, Integer type, Integer qualified, Integer oid, Integer per) {

        String sqlc = "SELECT\n" + "	COUNT(*)\n" + "FROM\n" + "	t_mt_stock ms\n"
                + "LEFT JOIN t_mt_material_apply ma ON ma.id = ms.apply_id\n"
                + "LEFT JOIN t_mt_base mb ON ms.material = mb.id\n" + "LEFT JOIN t_pd_base pb ON mb.product = pb.id\n"
                + "LEFT JOIN t_pd_merchandise pcp ON pb.id = pcp.product\n" + "WHERE ms.oid =" + oid + " AND ms.state ="
                + state + " AND ms.qualified=" + qualified + " AND ms.type=" + type;
        String sql = "SELECT\n" + "	ms.id,\n" + "	ma.create_date,\n" + "	ms.manufacture_date,\n"
                + "	ms.invalid_date,\n" + "	pcp.outer_sn,\n" + "	pcp.outer_name,\n" + "	pb.inner_sn,\n" + "	pb. NAME\n"
                + "FROM\n" + "	t_mt_stock ms\n" + "LEFT JOIN t_mt_material_apply ma ON ma.id = ms.apply_id\n"
                + "LEFT JOIN t_mt_base mb ON ms.material = mb.id\n" + "LEFT JOIN t_pd_base pb ON mb.product = pb.id\n"
                + "LEFT JOIN t_pd_merchandise pcp ON pb.id = pcp.product\n"
                + "WHERE ms.oid=? AND ms.state=? AND ms.qualified=? AND ms.type=?";

        return mtStockDao.findMapByCondition(sql, sqlc, new String[]{state + "", oid + "", qualified + "", type + ""},
                per);
    }

    @Override
    public MtStockInfo getMtStockInfoByMId(Integer material) {
        return stockInfoDao.getByHQL(" from MtStockInfo m where m.material_=?0", material);
    }

    @Override
    public void updateMtStockInfo(MtStockInfo stockInfo) {
        stockInfoDao.update(stockInfo);
    }

    @Override
    public MtStockInfo getMtStockInfoByProduct(Integer product) {
        return stockInfoDao
                .getByHQL(" from MtStockInfo m left join MtBase t on m.material= t.id where t.product=" + product);
    }

    @Override
    public Map getMtStockInfoIdByItemId(Integer itemId) {
        return stockInfoDao.findMapByCondition(
                "SELECT m.id,m.current_stock FROM\n" + "	t_mt_stock_info m\n"
                        + "LEFT JOIN t_mt_base mtb ON m.material = mtb.id\n" + "LEFT JOIN t_pd_base pb ON mtb.product = pb.id\n"
                        + "LEFT JOIN t_pd_merchandise pcp ON pb.id = pcp.product\n"
                        + "LEFT JOIN t_sl_orders_item ti ON pcp.id = ti.sales_relationship\n" + "WHERE\n" + "	ti.id = ?0",
                "select count(*) from t_mt_stock_info", new String[]{itemId + ""}, 10);
    }

    @Override
    public MtStockInfo getMtStockInfoById(Integer mtStockInfoId) {
        return stockInfoDao.get(mtStockInfoId);
    }

    @Override
    public void saveMtStockInfo(MtStockInfo mtStockInfo) {
        stockInfoDao.save(mtStockInfo);
    }

    @Override
    public List<MtStock> getMtStocksByOidAndProductId(Integer oid, Integer id) {
        String hql = " from MtStock o where o.oid=?0 and productId=?1";
        return mtStockDao.getListByHQL(hql, oid, id);
    }

    @Override
    public void saveInMtApplication(MtInApplication inApplication) {
        inApplicationDao.save(inApplication);
    }

    @Override
    public void saveInMtApplicationItem(MtInApplicationItem inApplicationItem) {
        inApplicationItemDao.save(inApplicationItem);
    }

    @Override
    public Map getInApplicationList(Integer oid, String state) {

        if (StringUtils.isBlank(state))
            state = "(1,2)";
        String sql =
                """
                        SELECT DISTINCT
                        	mia.id,
                        	mia.state,
                        	mia.create_name,
                        	mia.create_date,
                        	mia.supplier,
                        	ms.`name`
                        FROM
                        	t_mt_in_application mia
                        	LEFT JOIN t_mt_in_application_item mai ON mai.applicant = mia.id
                        	LEFT JOIN t_srm_supplier ms ON mia.supplier = ms.id\s
                        WHERE
                        	ms.org =?0
                        """;
        if (state != null)
            sql += " and mai.state in " + state;

        return inApplicationDao.findMapByConditionNoPage(sql, new Object[]{oid});
    }

    @Override
    public void saveInApplicationItemCheck(MtInApplicationItemCheck inApplicationItemCheck) {
        checkDao.save(inApplicationItemCheck);
    }

    @Override
    public MtInApplicationItem getInApplicationItemById(Integer itemId) {
        return inApplicationItemDao.get(itemId);
    }

    @Override
    public Map getInListDetailByInApplicationId(Integer id) {
        String sql =
                """
                         SELECT DISTINCT
                             	mai.id AS item_id,
                             	iaic.check_name AS stock_name,
                             	iaic.check_time AS stock_date,
                             	iaic.check_quantity AS stock_amount,
                             	tms.create_name AS stock_in_name,
                             	tms.update_date AS stock_in_date,
                             	mb.id AS mb_id,
                             	mb.model,
                             	mu.NAME AS unit,
                             	mb.specifications,
                             	mb.name,
                             	mb.code,
                             	mai.quantity_plan,
                             	mai.quantity_type,
                             	mai.arrive_date,
                             	msm.id AS supplier_material_id,
                             	ms.id AS supplier_id,
                             	ms.NAME AS supplier_name,
                             	ms.full_name AS supplier_full_name,
                             	ms.code_name AS supplier_code_name,
                             	mia.create_name,
                             	mia.create_date,
                             	mia.id AS application_id,
                             	mai.unqualified_handle,
                             	mai.unqualified_info,
                             	mai.state,
                             	mai.reject_reason,
                             	mai.create_name AS mai_create_name,
                             	mai.create_date AS mai_create_date,
                             	mai.check_name,
                             	mai.check_date,
                             	mai.reject_date,
                             	mai.reject_name,
                             	mai.reject_approval_date,
                             	mai.reject_approval_name,
                             	mai.inspector,
                             	mai.inspect_state,
                             	mai.packaging_info,
                             	mai.packaging_result,
                             	mai.inspect_quantity,
                             	mai.inspect_packaging_info,
                             	mai.inspect_packaging_result,
                             	mai.inspect_quality_result,
                             	mai.packaging_count,
                             	mai.location_assigned,
                             	mai.location_count,
                             	mai.expiration_date
                             FROM
                             	t_mt_in_application_item mai
                             	LEFT JOIN t_mt_in_application_item_check iaic ON iaic.application_item = mai.id
                             	LEFT JOIN t_mt_in_application mia ON mai.applicant = mia.id
                             	LEFT JOIN t_srm_supplier ms ON mia.supplier = ms.id
                             	LEFT JOIN t_mt_base mb ON mai.material = mb.id
                             	LEFT JOIN t_mt_supplier_material msm ON msm.material = mb.id
                             	AND msm.supplier = ms.id
                             	LEFT JOIN t_mt_stock tms ON tms.application_in = mai.id
                             	LEFT JOIN t_mt_unit mu ON mb.unit_id = mu.id
                             WHERE
                             	mai.id = ?0
                        """;
        return inApplicationItemDao.findMapByConditionNoPage(sql, new Object[]{id});
    }

    @Override
    public MtInApplication getMtInApplicationById(Integer applicant) {
        return inApplicationDao.get(applicant);
    }

    @Override
    public void saveOrUpdateInApplication(MtInApplication mtInApplication) {
        if (mtInApplication.getId() != null)
            inApplicationDao.update(mtInApplication);
        else
            inApplicationDao.save(mtInApplication);
    }

    @Override
    public void updateInApplicationItem(MtInApplicationItem inApplicationItem) {
        inApplicationItemDao.saveOrUpdate(inApplicationItem);
    }

    @Override
    public List<MtInApplicationItem> getInApplicationItemsByApplicationId(Integer applicant) {
        String hql = " from MtInApplicationItem o where o.applicant=?0";
        return inApplicationItemDao.getListByHQL(hql, applicant);
    }

    @Override
    public Map getQualitySituation(Integer applicationId) {
        String sql =
                "SELECT iai.applicant as application_id,iai.create_name,iai.create_date, iai.update_name as check_name,iai.update_date as check_date , ( SELECT COUNT(DISTINCT material) ) AS size , ( SELECT COUNT(id) FROM t_mt_in_application_item WHERE IFNULL(state, 0) = 3 and applicant=?0 and ifnull(approval_type,0)!=2) AS accept , ( SELECT COUNT(id) FROM t_mt_in_application_item WHERE IFNULL(state, 0) = 4 and applicant = ?1) AS unqualified FROM t_mt_in_application_item iai where iai.applicant=?2";
        return inApplicationDao.findMapByConditionNoPage(sql,
                new Object[]{applicationId, applicationId, applicationId});
    }

    @Override
    public Map getMtList(Integer oid, String state, String approvalType, String user, Integer id) {

        String sql = """
                SELECT DISTINCT
                	miai.create_name,
                	miai.create_date,
                	miai.update_name AS check_name,
                	miai.update_date AS check_date,
                	miai.reject_date,
                	miai.reject_name,
                	mb.model,
                	mb.specifications,
                	mb.name,
                	mu.name AS unit,
                	mu.id AS unit_id,
                	mb.code,
                	miai.quantity_plan,
                	miai.arrive_date,
                	miai.id,miai.expiration_date
                FROM
                	t_mt_in_application_item miai
                	LEFT JOIN t_mt_in_application_item_check iaic ON iaic.application_item = miai.id
                	LEFT JOIN t_mt_in_application mia ON miai.applicant = mia.id
                	LEFT JOIN t_srm_supplier ms ON mia.supplier = ms.id
                	LEFT JOIN t_mt_base mb ON miai.material = mb.id
                	left join t_mt_unit mu on mb.unit_id = mu.id
                WHERE
                	ms.org =?0
                """;
        if (StringUtils.isNotBlank(state))
            sql += " and miai.state in " + state;
        if (id != null)
            sql += " and mia.id=" + id;
        if (StringUtils.isNotBlank(approvalType))
            sql += " and miai.approval_type =" + approvalType;

        //待确认数量列表--按照检验时间排序，越早的在上面
        if ("(3,6)".equals(state)) {
            sql += " ORDER BY miai.update_date ASC";

        }
        if ("(8)".equals(state) || "(9)".equals(state)) {
            //待选择库位和数量异议待处理列表--按照仓库时间排序，越早的在上面
            sql += " ORDER BY iaic.check_time ASC";
        }

        return inApplicationItemDao.findMapByConditionNoPage(sql, new Object[]{oid});
    }

    @Override
    public Map getCountList(Integer applicationId, String state) {
        String sql =
                "SELECT distinct mb.model,mb.specifications,mb.name,mb.unit, mb.code, miai.quantity_plan, miai.arrive_date,miai.id FROM t_mt_in_application_item miai LEFT JOIN t_mt_in_application mia ON miai.applicant = mia.id LEFT JOIN t_srm_supplier ms ON mia.supplier = ms.id LEFT JOIN t_mt_base mb ON miai.material = mb.id  WHERE 1=1";
        if (StringUtils.isNotBlank(state))
            sql += " and miai.state = " + state;
        if (applicationId != null)
            sql += " and mia.id=" + applicationId;
        return inApplicationItemDao.findMapByConditionNoPage(sql, new Object[]{});
    }

    @Override
    public Map getListByCondition(Integer oid, Integer supplierId, String beginDate, String endDate, String dateSection,
                                  String dateType, String resultType, String state, String quantityType) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        String sql;
        Object[] params;
        // 查询近7日
        if ("1".equals(dateSection)) {
            beginDate = DateUtil.getAfterDayDate("-7");
            endDate = dateFormat.format(new Date()) + " 23:59:59";
        }
        // 查询本月
        if ("2".equals(dateSection)) {
            LocalDate today = LocalDate.now();
            // 本月的第一天
            LocalDate firstDay = LocalDate.of(today.getYear(), today.getMonth(), 1);
            // 本月的最后一天
            LocalDate lastDay = today.with(TemporalAdjusters.lastDayOfMonth());

            beginDate = firstDay.toString() + " 00:00:00";
            endDate = lastDay.toString() + " 23:59:59";
        }
        // 展示订单清单
        if ("1".equals(resultType)) {
            sql =
                    "SELECT distinct mia.id,mia.state,mia.create_name, mia.create_date, mia.supplier, ms.`name` FROM t_mt_in_application mia left join t_mt_in_application_item mai on mai.applicant = mia.id LEFT JOIN t_srm_supplier ms ON mia.supplier = ms.id where ms.org=?0";

        } else { // 展示物料清单
            sql =
                    "SELECT distinct mai.id as mai_id,mb.id as mb_id,mb.model,mb.unit,mb.specifications,mb.name, mb.code, mai.quantity_plan, mai.arrive_date,mai.unqualified_info,ms.name as supplier_name,mia.create_name,mia.create_date,mia.id as application_id,mai.unqualified_handle,mai.state,mai.reject_reason,mai.create_name as mai_create_name,mai.create_date as mai_create_date,mai.update_name as check_name,mai.update_date as check_date,mai.reject_date,mai.reject_name,mai.reject_approval_date,mai.reject_approval_name FROM t_mt_in_application_item mai LEFT JOIN t_mt_in_application mia ON mai.applicant = mia.id LEFT JOIN t_srm_supplier ms ON mia.supplier = ms.id LEFT JOIN t_mt_base mb ON mai.material = mb.id WHERE ms.org=?0";
        }
        params = new Object[]{oid, beginDate, endDate};

        switch (dateType) {
            case "1": // 查询入库申请提出时间的申请入库列表
                sql += " and mia.create_date between ?1 and ?2";

                break;
            case "2": // 查询检验时间
                sql += " and mai.update_date between ?1 and ?2";

                break;
            case "3": // 查询让步申请的提出时间
                sql += " and mai.reject_date between ?1 and ?2";
                break;
            case "4": // 查询让步审批时间
                sql += " and mai.reject_approval_date between ?1 and ?2";
                break;
            default: // 库管员的入库处理时间
                sql =
                        " SELECT distinct mai.id as mai_id,mb.id as mb_id, mb.model,mb.unit,mb.specifications,mb.name, mb.code, mai.quantity_plan, mai.arrive_date,mai.unqualified_info,ms.name as supplier_name,mia.create_name,mia.create_date,mia.id as application_id,mai.unqualified_handle,mai.state,mai.reject_reason,mai.create_name as mai_create_name,mai.create_date as mai_create_date,mai.update_name as check_name,mai.update_date as check_date,mai.reject_date,mai.reject_name,mai.reject_approval_date,mai.reject_approval_name FROM t_mt_in_application_item mai LEFT JOIN t_mt_in_application mia ON mai.applicant = mia.id LEFT JOIN t_srm_supplier ms ON mia.supplier = ms.id LEFT JOIN t_mt_base mb ON mai.material = mb.id ";
                sql += " left join t_mt_stock tms on tms.application_in = mai.id";
                sql += " WHERE ms.org=?0";
                sql += " and tms.create_date between ?1 and ?2";
        }

        if (supplierId != null)
            sql += " and ms.id=" + supplierId;
        if (StringUtils.isNotBlank(state))
            sql += " and mai.state in (" + state + ")";
        if (StringUtils.isNotBlank(quantityType))
            sql += " and mai.quantity_type=" + quantityType;

        Map data = inApplicationDao.findMapByConditionNoPage(sql, params);

        data.put("beginDate", beginDate);
        data.put("endDate", endDate);
        data.put("resultType", resultType);
        return data;
    }

    @Override
    public Map getItemChecksByItemId(Integer id) {
        String sql =
                "select ic.check_quantity,mb.unit,ic.create_date from t_mt_in_application_item_check ic left join t_mt_in_application_item iai on ic.application_item = iai.id left join t_mt_base mb on iai.material = mb.id  where application_item = ?0";
        return inApplicationItemDao.findMapByConditionNoPage(sql, new Object[]{id});
    }

    @Override
    public Integer getStockCount(Integer oid, String state, String notState, Integer userId, String approvalType) {
        String sql =
                "SELECT iai.id,";

        if ("(2)".equals(state)) {
            sql += " COUNT(DISTINCT ia.id) AS appCount ";
        } else {
            sql += " COUNT(DISTINCT iai.id) AS appCount ";
        }

        sql += " FROM t_mt_in_application_item iai LEFT JOIN t_mt_in_application ia ON iai.applicant = ia.id LEFT JOIN t_srm_supplier ms ON ia.supplier = ms.id WHERE ia.creator = ?0 AND ms.org = ?1";


        if (StringUtils.isNotBlank(state))
            sql += " and iai.state in" + state;
        if (StringUtils.isNotBlank(notState))
            sql += " and iai.state not in" + notState;
        if (StringUtils.isNotBlank(approvalType))
            sql += " and iai.approval_type =" + approvalType;

        if (!("(2)").equals(state)) {
            sql += " GROUP BY iai.id";
        }
        List list = mtStockDao.getListBySQL(sql, userId, oid);
        if ("(2)".equals(state)) {

            if (list != null && list.size() > 0) {
                Object[] o = (Object[]) list.get(0);
                BigInteger count = (BigInteger) o[1];

                return count.intValue();
            } else {
                return 0;
            }

        }
        return list.size();
    }

    @Override
    public Integer getBadgeNumber(User user, String code) {
        Integer result = null;
        switch (code) {
            case "outMaterialTest": // 外购物料检验
                result = this.getStockCount(user.getOid(), "(2)", "(9)", user.getUserID(), "1");
                break;
            case "outMaterialInStockApproval": // 待入库
                result = this.getStockCount(user.getOid(), "(3,6,8)", "(9)", user.getUserID(), "1");
                break;
            case "outMaterialInStockApply": // 入库申请
                result = this.getStockCount(user.getOid(), "(0,1)", "(9)", user.getUserID(), "1");
                break;
            case "unqualifiedHandlePurchase": // 不合格品处理
                result = this.getStockCount(user.getOid(), "(4,7)", "(9)", user.getUserID(), "1");
                break;
            case "unqualifiedHandleSkill": // 不合格品处理
                result = this.getStockCount(user.getOid(), "(4,7)", "(9)", user.getUserID(), "2");
                break;
        }
        return result;
    }

    @Override
    public MtStock getByApplicationItemId(Integer id) {

        String hql = " from MtStock o where o.applicationIn = ?0";
        return mtStockDao.getByHQL(hql, id);
    }

    @Override
    public Integer updateInitialStock(User user, Integer id, BigDecimal oldStock, BigDecimal newStock) {
        if (oldStock == null)
            oldStock = new BigDecimal(0);
        if (newStock == null)
            newStock = new BigDecimal(0);

        String sql =
                "SELECT ifnull(SUM(pis.out_plan),0) as outPlan from t_pd_inout_stock pis LEFT JOIN t_sl_orders_item soi on pis.orders_item = soi.id LEFT JOIN t_mt_supplier_material msm ON msm.id = soi.sales_relationship LEFT JOIN t_mt_base mb ON mb.id = msm.material where mb.id=?0 and pis.state not in (4)";

        Map outSum = mtStockDao.findMapByConditionNoPage(sql, new Object[]{id});
        ArrayList list = (ArrayList) outSum.get("data");
        BigDecimal outPlan = new BigDecimal(0);
        if (list != null && list.size() > 0) {
            HashMap m = (HashMap) list.get(0);
            Double out_plan = (Double) m.get("out_plan");

            outPlan = BigDecimal.valueOf(out_plan == null ? 0.0 : out_plan);
        }
        MtBase mtBase = mtService.getMtById(id);
        MtStockInfo stockInfo = stockInfoDao.getByHQL(" from MtStockInfo o where o.material_=?0", id);

        if (stockInfo == null) {
            MtStockInfo sk = new MtStockInfo();
            sk.setAvailableStock(new BigDecimal(0));
            sk.setCurrentStock(new BigDecimal(0));
            sk.setInitialStock(new BigDecimal(0));
            sk.setCreateDate(new Date());
            sk.setMaterial_(id);
            sk.setMaterial(mtBase);
            sk.setMinimumStock(new BigDecimal(0));
            sk.setStock(new BigDecimal(0));
            sk.setCreateDate(new Date());
            sk.setCreateName(user.getUserName());
            sk.setCreator(user.getUserID());
            stockInfoDao.save(sk);

            stockInfo = stockInfoDao.getByHQL(" from MtStockInfo o where o.material_=?0", id);
        }

        BigDecimal currentStock = stockInfo.getCurrentStock() == null ? new BigDecimal(0) : stockInfo.getCurrentStock();

        BigDecimal stock = newStock.subtract(oldStock);

        if (stock.compareTo(new BigDecimal(0)) < 0) { // 改少了
            if ((currentStock.add(stock)).compareTo(outPlan == null ? new BigDecimal(0) : outPlan) == -1
                    || currentStock.add(stock).compareTo(new BigDecimal(0)) == -1) {
                return 0;
            }
        }

        stockInfo.setInitialStock(newStock);
        stockInfo.setCurrentStock(currentStock.add(stock));
        stockInfo.setUpdateDate(new Date());
        stockInfo.setUpdator(user.getUserID());
        stockInfo.setUpdateName(user.getUserName());
        stockInfoDao.update(stockInfo);

        // 增加库存变动记录

        mtService.saveStockRecord(stockInfo.getMaterial().getOrg(), stockInfo.getMaterial_(), stockInfo.getCreator(),
                stockInfo.getCreateName(), null, "modify_initial", newStock, currentStock, stockInfo.getCurrentStock(),
                null);
        return 1;
    }

    @Override
    public List<MtInApplicationItemCheck> getItemChecks(Integer itemId) {
        String hql = " from MtInApplicationItemCheck o where o.applicationItem=?0";
        return itemCheckDao.getListByHQL(hql, itemId);
    }

    @Override
    public Map getInList(User user, Integer pageNum, Integer per) {
        String sql =
                "select mb.id, mb.model,ms.name as supplier_name, mb.specifications, mb.name, mb.code, mu.name as unit, iai.quantity_fact, iai.quantity_plan, iaic.check_quantity, iaic.check_name, iaic.check_time as check_date, iai.state, iai.create_date from t_mt_in_application_item iai left join t_mt_base mb on mb.id = iai.material left join t_mt_unit mu on mb.unit_id = mu.id left join t_mt_in_application_item_check iaic on iaic.application_item = iai.id left join t_mt_in_application ia on iai.applicant = ia.id left join t_srm_supplier ms on ia.supplier = ms.id where iai.state in ('8', 'A')";

        sql += " and mb.org=" + user.getOid();

        return mtStockDao.findMapByConditionByPage(sql, "", new Object[]{}, pageNum, per);
    }

    @Override
    public Map getStockChangeRecord(Integer id, String type, String startTime, String endTime, String year,
                                    Integer pageNum, Integer per) {
        ;
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd");
        String sql = "SELECT * from t_mt_stock_detail msd where 1=1 and msd.material=?0";
        if (StringUtils.isNotBlank(type))
            sql += " and msd.event in " + type;
        if (StringUtils.isNotBlank(startTime))
            sql += " and msd.event_time between '" + startTime + " 00:00:00" + "' and '" + endTime + " 23:59:59'";
        else {
            if (!"add".equals(year)) {
                startTime = sd.format(DateUtils.thisMonthFirstDate());
                endTime = sd.format(DateUtils.monthEnd(new Date()));

                sql += " and msd.event_time between '" + startTime + " 00:00:00" + "' and '" + endTime + " 23:59:59'";
            }
        }
        // if (pageNum != null)
        // sql += " limit " + (pageNum - 1) * per + "," + per;
        Map m = mtStockDao.findMapByConditionByPage(sql, null, new Object[]{id}, pageNum, per);
        m.put("startTime", startTime);
        m.put("endTime", endTime);
        return m;
    }

    @Override
    public void saveOrUpdateStockInfo(MtStockInfo stockInfo) {
        stockInfoDao.saveOrUpdate(stockInfo);
    }

    @Override
    public List<MtStockInfo> getMtStockInfoByOrg(Integer oid) {
        return stockInfoDao.getListByHQL(" from MtStockInfo o where o.material.org=?0 order by o.updateDate desc", oid);
    }
}
