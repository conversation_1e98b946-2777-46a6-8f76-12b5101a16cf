<template>
  <div class="ui-cells">
    <div class="searchcon">
      <el-input placeholder="客户名称/订单号" v-model="searchKey" clearable  prefix-icon="el-icon-search" > </el-input>
      <el-button type="primary" @click="geSearchList">搜索</el-button>
      <div class="clr2"></div>
    </div>
    <a class="ui-cell" v-for="(item, index) in applyList5" v-bind:key="index" @click="jump(item.id)">
      <div class="ui-cell__bd">
        <div class="ui-cell_con">
          <div><span class="info">{{item.name}}</span><span class="info">申请开票金额 {{item.invoice_amount | numberFilter(2)}}元</span></div>
          <div><span class="info">{{item.create_name}} {{item.create_date | formatDate}}</span>
            <span class="info">需要发票数量{{item.invoice_count}}
             <span v-if="item.invoice_count !== '--'">张</span>
            </span></div>
        </div>
      </div>
      <div class="ui-cell__ft"></div>
    </a>
  </div>
</template>
<style lang="less">
  .searchcon{
    padding: 10px 5px;
    .el-input{ width: 294px; float: left }
    .el-button{   float: left }
    .clr2{ clear: both; }
  }
</style>
<script>
import { formatDate, numberFilter } from '../../../js/common'
export default {
  name: 'invoiceRegister2',
  filters: {
    formatDate,
    numberFilter
  },
  data () {
    return {
      list: this.$store.getters.getInvoiceList,
      searchKey: "",
      listeners: [],
      list5:[],
      applyList5: [] // 待开票
    }
  },
  created () {
    let _this = this
    this.applyList5 = (this.list && this.list.applyList5) || []
    this.$store.dispatch('setNewInvoiceFinNav', 2)
    _this.listeners = [
      // 已开票未登记
      _this.sphdSocket.subscribe('invoiceRegister', _this.subscribeCallBack, null, 'user')
    ]
  },
  destroyed: function () {
    let _this = this
    _this.listeners.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    geSearchList: function () {
      let that = this
      that.applyList5 = []
      console.log('applyList3', that.applyList3)
      that.list5.forEach(function (item) {
//        客户与订单号
        let customer = item.name
        let orderSn = item.sn
        console.log('customer=' + customer + ', orderSn=' + orderSn +', that.searchKey=' + that.searchKey)
        if(customer.indexOf(that.searchKey)>-1 || orderSn.indexOf(that.searchKey)>-1){
          that.applyList5.push(item)
        }
      })
      console.log('applyList3', that.applyList3)

    },
    jump: function (id) {
      this.$router.push({
        path: `/invoiceRegisterDetail/${id}`
      })
    },
    subscribeCallBack: function (res) {
      console.log('4列表回调：', res)
      this.$store.dispatch('setNewInvoiceList')
    }
  },
  watch: {
    '$store.state.invoiceList': {
      immediate: true,
      deep: true,
      handler (invoiceList) {
        console.log('2 重获列表：', invoiceList)
        this.list = invoiceList
        this.applyList5 = (this.list && this.list.applyList5) || []
        this.list5 = (this.list && this.list.list5) || []
      }
    }
  }
}
</script>
