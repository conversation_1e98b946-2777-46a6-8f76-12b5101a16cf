package cn.sphd.miners.modules.generalAffairs.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by Administrator on 2017/7/11.
 * 固定资产维修记录表
 */
@Entity
@Table(name="t_finance_assets_maintain")
public class FinanceAssetsMaintain implements Serializable {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;


    @Column(name="repair_date"   , nullable=true , unique=false)
    private Date repairDate;


    @Column(name="fault_reason"  , length=255 , nullable=true , unique=false)
    private String faultReason;

    @Column(name="nature"  , length=50 , nullable=true , unique=false)
    private String nature;

    @Column(name="content"  , length=255 , nullable=true , unique=false)
    private String content;

    @Column(name="exist_problem"  , length=255 , nullable=true , unique=false)
    private String existProblem;

    @Column(name="worker"   , nullable=true , unique=false)
    private Integer worker;

    @Column(name="worker_name"  , length=50 , nullable=true , unique=false)
    private String workerName;

    @Column(name="contact_phone"  , length=100 , nullable=true , unique=false)
    private String contactPhone;

    @Column(name="contact_corp"  , length=100 , nullable=true , unique=false)
    private String contactCorp;

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;


    @ManyToOne(fetch= FetchType.LAZY )
    @JoinColumn(name="assets", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private FinanceFixedAssets assets;

    @Column(name="assets"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer assets_;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getRepairDate() {
        return repairDate;
    }

    public void setRepairDate(Date repairDate) {
        this.repairDate = repairDate;
    }

    public String getFaultReason() {
        return faultReason;
    }

    public void setFaultReason(String faultReason) {
        this.faultReason = faultReason;
    }

    public String getNature() {
        return nature;
    }

    public void setNature(String nature) {
        this.nature = nature;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getExistProblem() {
        return existProblem;
    }

    public void setExistProblem(String existProblem) {
        this.existProblem = existProblem;
    }

    public Integer getWorker() {
        return worker;
    }

    public void setWorker(Integer worker) {
        this.worker = worker;
    }

    public String getWorkerName() {
        return workerName;
    }

    public void setWorkerName(String workerName) {
        this.workerName = workerName;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getContactCorp() {
        return contactCorp;
    }

    public void setContactCorp(String contactCorp) {
        this.contactCorp = contactCorp;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public FinanceFixedAssets getAssets() {
        return assets;
    }

    public void setAssets(FinanceFixedAssets assets) {
        this.assets = assets;
    }

    public Integer getAssets_() {
        return assets_;
    }

    public void setAssets_(Integer assets_) {
        this.assets_ = assets_;
    }
}
