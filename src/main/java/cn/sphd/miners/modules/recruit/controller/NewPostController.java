package cn.sphd.miners.modules.recruit.controller;


import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmSchedule;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmScheduleHistory;
import cn.sphd.miners.modules.recruit.entity.*;
import cn.sphd.miners.modules.recruit.service.RdNoticeService;
import cn.sphd.miners.modules.recruit.service.RdPostService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;


/**
 * Created by Administrator on 2023/5/7
 * 新版岗位管理
 */
@Controller
@RequestMapping("/post")
public class NewPostController {

    @Autowired
    RdPostService rdPostService;
    @Autowired
    UserService userService;
    @Autowired
    RdNoticeService rdNoticeService;


    /**
     * <AUTHOR>
     * @Date 2023/5/7
     * 新版 新增岗位
     */
    @ResponseBody
    @RequestMapping("/addPost.do")
    public JsonResult addPost(User user, String name,String synthesis, String dutys, String requirements, String others){
        RdPost rdPost=new RdPost(user.getOid(),name,dutys,requirements,synthesis,others,user.getUserID(),user.getUserName());
        rdPostService.saveRdPost(rdPost);
        return new JsonResult(1,1);
    }


    /**
     * <AUTHOR>
     * @Date 2023/5/7
     * 新版 岗位列表接口
     */
    @ResponseBody
    @RequestMapping("/getPostList.do")
    public JsonResult getPostList(User user){
        rdPostService.rdPostExecuteChanges(user.getOid(),new Date()); //校对到执行更改时间的内容
        List<RdPost> rdPosts=rdPostService.getRdPostsByOid(user.getOid(),true);
        for (RdPost rdPost:rdPosts){
            rdPost.setUserNums(userService.getUserListByPostId(rdPost.getId()).size());
        }
        return new JsonResult(1,rdPosts);
    }

    /**
     * <AUTHOR>
     * @Date 2023/5/7
     * 新版 岗位点击数量 查询人数
     */
    @ResponseBody
    @RequestMapping("/getUserListByPostId.do")
    public JsonResult getUserListByPostId(Long postId){
        List<User> users=userService.getUserListByPostId(postId);
        return new JsonResult(1,users);
    }



    /**
     * <AUTHOR>
     * @Date 2023/5/7
     * 新版 查看岗位详情接口
     */
    @ResponseBody
    @RequestMapping("/getPostInfo.do")
    public JsonResult getPostInfo(User user, Long id){
        RdPost rdPost=rdPostService.getRdPostById(id);
        return new JsonResult(1,rdPost);
    }

    /**
     * <AUTHOR>
     * @Date 2023/5/7
     * 新版  修改内容接口
     */
    @ResponseBody
    @RequestMapping("/updatePostCont.do")
    public JsonResult updatePostCont(User user,Long id,String synthesis, String dutys, String requirements, String others){
        RdPost rdPost=rdPostService.getRdPostById(id);
        rdPost.setSynthesis(synthesis);
        rdPost.setDuty(dutys);
        rdPost.setRequirement(requirements);
        rdPost.setOthers(others);
        rdPostService.updateRdPost(rdPost,user,3);//操作:1-增,2-删,3-改内容,4-启用,5-停用,6-更改名称,7-改错别字
        return new JsonResult(1,1);
    }


    /**
     * <AUTHOR>
     * @Date 2023/5/7
     * 新版 查看修改记录
     */
    @ResponseBody
    @RequestMapping("/getPostHistories.do")
    public JsonResult getPostHistories(User user, Long id){
        List<RdPostHistory> rdPostHistoryList=rdPostService.getRdPostHistoriesByPostId(id,new Integer[]{1,3,6,7});
        RdPost rdPost=rdPostService.getRdPostById(id);
        List<Map<String,Object>> mapList=new ArrayList<>();
        int number=0;
        int count=0;

//        if (pageInfo.getCurrentPageNo()>1){
//            number=(pageInfo.getCurrentPageNo()-1)*pageInfo.getPageSize();
//        }
        Map<String,Object> map=new HashMap<>();

        for (RdPostHistory rdPostHistory:rdPostHistoryList){
            Map<String,Object> sonMap=new HashMap<>();
            if (number==0){
                sonMap.put("dataState","原始信息");
            }else {
                sonMap.put("dataState","第"+number+"次修改后");
            }
            sonMap.put("id",rdPostHistory.getId());
            sonMap.put("updateName",rdPostHistory.getCreateName());//人名
            sonMap.put("updateDate",rdPostHistory.getCreateDate());//修改时间
            sonMap.put("name",rdPostHistory.getName()); // 改后的数据

            String updateNature="--"; //修改性质
            switch (rdPostHistory.getOperation()){   //操作:1-增,2-删,3-改内容,4-启用,5-停用,6-更改名称,7-改错别字
                case 3:  updateNature="内容修改";
                         sonMap.put("name","查看"); // 改后的数据
                         sonMap.put("info",rdPostHistory); // 改后的详情
                    break;
                case 6:  updateNature="岗位改名"; break;
                case 7:  updateNature="改错字"; break;
            }

            String state="已失效";
            if (rdPostHistory.getEnabled()){
                state="有效，";
                if (new Date().getTime()<rdPostHistory.getBeginTime().getTime()){
                    state+="尚未执行";
                }else {
                    state+="正在执行";
                }
            }
            sonMap.put("beginDate",rdPostHistory.getBeginTime()); // 开始时间
            sonMap.put("state",state);
            sonMap.put("updateNature",updateNature); // 修改性质

            mapList.add(sonMap);

            number++;
            if (rdPostHistory.getUpdateDate()!=null){  //修改次数按 生效的修改次数算
                count++;
            }

        }
        map.put("number",count==0?0:count-1);//最新的修改次数
        map.put("updateName",rdPost.getUpdateName());//人名
        map.put("updateDate",rdPost.getUpdateDate());//修改时间
        map.put("historyList",mapList.size()<=1?new ArrayList<>():mapList);
//        map.put("pageInfo",pageInfo);
        return new JsonResult(1,map);
    }


    /**
     * <AUTHOR>
     * @Date 2023/5/8
     * 新版  改错字接口
     */
    @ResponseBody
    @RequestMapping("/updatePostCorrect.do")
    public JsonResult updatePostCorrect(User user,Long id,String name){
        List<RdPostHistory> rdPostHistoryList=rdPostService.getRdPostHistoriesByPostId(id,new Integer[]{6});
        for (RdPostHistory rdPostHistory:rdPostHistoryList){
            if (new Date().getTime()<rdPostHistory.getBeginTime().getTime()){
                return new JsonResult(1,2); // 有未生效新名称的更改 不能再次更改
            }
        }
        RdPost rdPost = rdPostService.getRdPostById(id);
        rdPost.setName(name);
        rdPostService.updateRdPost(rdPost, user, 7);//操作:1-增,2-删,3-改内容,4-启用,5-停用,6-更改名称,7-改错别字
        return new JsonResult(1, 1);
    }

    /**
     * <AUTHOR>
     * @Date 2023/5/8
     * 新版  岗位改名接口
     */
    @ResponseBody
    @RequestMapping("/updatePostName.do")
    public JsonResult updatePostName(User user,Long id,String name,String beginTime){
        RdPost rdPost=rdPostService.getRdPostById(id);

        RdPostHistory rdPostHistory = new RdPostHistory();
        BeanUtils.copyProperties(rdPost, rdPostHistory);
        rdPostHistory.setRdPost(rdPost);
        rdPostHistory.setName(name);
        rdPostHistory.setBeginTime(NewDateUtils.dateFromString(beginTime, "yyyy-MM-dd"));
        rdPostHistory.setOperation(6);//操作:1-增,2-删,3-改内容,4-启用,5-停用,6-更改名称,7-改错别字
        rdPostHistory.setUpdator(user.getUserID());
        rdPostHistory.setUpdateDate(null);// 实际到更改日期再存， 用来区分 此项是否执行了更改
        rdPostHistory.setUpdateName(user.getUserName());
        rdPostService.saveRdPostHistory(rdPostHistory, user);
        return new JsonResult(1,1);

    }

    /**
     * <AUTHOR>
     * @Date 2023/5/8
     * 新版  岗位停用接口
     * 有职工有数据的岗位不能停用
     */
    @ResponseBody
    @RequestMapping("/deactivatePost.do")
    public JsonResult deactivatePost(User user,Long id){
        RdPost rdPost=rdPostService.getRdPostById(id);
        List<User> userList=userService.getUserListByPostId(id);
        if (rdPost.getRdNoticePostHashSet().size()<=0&&userList.size()<=0){
            rdPost.setEnabled(false); // 停用
            rdPost.setEnabledTime(new Date());
            rdPost.setEndTime(new Date());
            rdPostService.updateRdPost(rdPost,user,5);//操作:1-增,2-删,3-改内容,4-启用,5-停用,6-更改名称,7-改错别字
            return new JsonResult(1,1);
        }
        return new JsonResult(1,0);
    }

    /**
     * <AUTHOR>
     * @Date 2023/5/10
     * 新版  岗位删除接口
     * 有职工有数据的岗位不能停用
     */
    @ResponseBody
    @RequestMapping("/deletePost.do")
    public JsonResult deletePost(User user,Long id){
        RdPost rdPost=rdPostService.getRdPostById(id);
        List<User> userList=userService.getUserListByPostId(id);
        if (rdPost.getRdNoticePostHashSet().size()<=0&&userList.size()<=0) {
            rdPostService.deleteRdPost(rdPost);
            return new JsonResult(1,1);
        }else{
            return new JsonResult(1,0);
        }
    }

    /**
     * <AUTHOR>
     * @Date 2023/5/10
     * 新版  已停用岗位列表接口
     */
    @ResponseBody
    @RequestMapping("/deactivatePostList.do")
    public JsonResult deactivatePostList(User user){
        List<RdPost> rdPosts=rdPostService.getRdPostsByOid(user.getOid(),false);
        return new JsonResult(1,rdPosts);
    }

    /**
     * <AUTHOR>
     * @Date 2023/5/10
     * 新版  岗位恢复使用接口
     */
    @ResponseBody
    @RequestMapping("/restorePost.do")
    public JsonResult restorePost(User user,Long id){
        RdPost rdPost=rdPostService.getRdPostById(id);
        rdPost.setEnabled(true); // 恢复使用
        rdPost.setEnabledTime(new Date());
        rdPostService.updateRdPost(rdPost,user,4);//操作:1-增,2-删,3-改内容,4-启用,5-停用,6-更改名称,7-改错别字
        return new JsonResult(1,1);
    }


    /**
     * <AUTHOR>
     * @Date 2023/6/6
     * 新版  岗位综合选项列表   需求更改后加
     */
    @ResponseBody
    @RequestMapping("/getSynthesizes.do")
    public JsonResult getSynthesizes(User user){
        List<RdNoticeItemTmpl> rdNoticeItemTmplList=rdNoticeService.getRdNoticeItemTmplList(3,user.getOid());
        return new JsonResult(1,rdNoticeItemTmplList);
    }


    /**
     * <AUTHOR>
     * @Date 2023/6/6
     * 新版  新增岗位综合管理选项   需求更改后加
     */
    @ResponseBody
    @RequestMapping("/addSynthesize.do")
    public JsonResult addSynthesize(User user, String name){
        RdNoticeItemTmpl rdNoticeItem=new RdNoticeItemTmpl();
        rdNoticeItem.setCreator(user.getUserID());
        rdNoticeItem.setCreateDate(new Date());
        rdNoticeItem.setCreateName(user.getUserName());
        rdNoticeItem.setUpdator(user.getUserID());
        rdNoticeItem.setUpdateName(user.getUserName());
        rdNoticeItem.setUpdateDate(rdNoticeItem.getCreateDate());
        rdNoticeItem.setType(3);
        rdNoticeItem.setItemCode("custom");
        rdNoticeItem.setOrders(100);
        rdNoticeItem.setItemName(name);
        rdNoticeItem.setContent("岗位综合管理选项");
        rdNoticeItem.setOrg(user.getOid());
        rdNoticeItem.setSystem(false);
        rdNoticeService.saveRdNoticeItemTmpl(rdNoticeItem);
        return new JsonResult(1,1);
    }


    /**
     * <AUTHOR>
     * @Date 2025/7/18
     * 岗位职责 重新排序
     */
    @ResponseBody
    @RequestMapping("/orderPostDuty.do")
    public JsonResult orderPostDuty(User user,Long id, String dutys){
        RdPost rdPost=rdPostService.getRdPostById(id);
        rdPost.setDuty(dutys);
        rdPostService.updateRdPost(rdPost);
        return new JsonResult(1,"操作成功");
    }

    /**
     * <AUTHOR>
     * @Date 2025/7/18
     * 岗位要求 重新排序
     */
    @ResponseBody
    @RequestMapping("/orderPostRequirement.do")
    public JsonResult orderPostRequirement(User user,Long id, String requirements){
        RdPost rdPost=rdPostService.getRdPostById(id);
        rdPost.setRequirement(requirements);
        rdPostService.updateRdPost(rdPost);
        return new JsonResult(1,"操作成功");

    }
}
