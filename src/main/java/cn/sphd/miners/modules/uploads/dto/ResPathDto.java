package cn.sphd.miners.modules.uploads.dto;

import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.annotation.JSONField;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.ContextLoader;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.io.File;

/**
 * <AUTHOR>
 * @description 上传文件目录逻辑类
 * @date Create at 2020/3/4 09:41
**/
public class ResPathDto{
    private String loadpath; //upload目录
    private String appendPath; //上传文件目录：org/oid/yyyyMM
    public final static String separator = File.separator;

    public ResPathDto() {
    }

    //获取机构文件路径
    public ResPathDto(Organization org, Long accId) {
        ServletContext sc = ContextLoader.getCurrentWebApplicationContext().getServletContext();
        loadpath=sc.getRealPath("/upload");
        if(StringUtils.isEmpty(loadpath)) {
            loadpath = sc.getRealPath("/").concat("upload");
        }
        if(org != null && org.getId() !=null  && org.getId() >= 0 && "NFS".equalsIgnoreCase(org.getUploadStorageType())) {
            appendPath = "org" + separator + org.getId().toString() + separator + NewDateUtils.dateToString(System.currentTimeMillis(),"yyyyMM");
        } else if(accId != null && accId > 0){
            appendPath = "acc" + separator + accId.toString() + separator + NewDateUtils.dateToString(System.currentTimeMillis(),"yyyyMM");
        } else {
            throw new IllegalArgumentException("机构或用户异常");
        }
    }
    //获取用户文件路径（有机构取机构，无机构取用户领域？）
    public ResPathDto(HttpServletRequest request, User user, AuthAcc acc) {
        if(user == null && !AuthAcc.realAcc(acc)) {
            throw new IllegalArgumentException("机构或用户异常");
        }
        ServletContext sc = request.getServletContext();
        loadpath=sc.getRealPath("/upload");
        if(StringUtils.isEmpty(loadpath)) {
            loadpath = sc.getRealPath("/").concat("upload");
        }
        if(user != null && user.getOid()!=null) {
            appendPath = "org" + separator + user.getOid().toString() + separator + NewDateUtils.dateToString(System.currentTimeMillis(),"yyyyMM");
        } else {
            appendPath = "acc" + separator + acc.getId().toString() + separator + NewDateUtils.dateToString(System.currentTimeMillis(),"yyyyMM");
        }
    }

    public String getLoadpath() {
        return loadpath;
    }

    public void setLoadpath(String loadpath) {
        this.loadpath = loadpath;
    }

    public String getAppendPath() {
        return appendPath;
    }

    public void setAppendPath(String appendPath) {
        this.appendPath = appendPath;
    }

    @JSONField(serialize = false)
    public String getFullPath() {
        return loadpath + separator + appendPath + separator;
    }
    public void mkdir() {
        File dir = new File(getFullPath());
        if(!dir.exists()) {
            dir.mkdirs();
        }
    }
    public static String getRootPath(HttpServletRequest request) {
        String loadpath=request.getServletContext().getRealPath("/upload");
        if(StringUtils.isEmpty(loadpath)) {
            loadpath = request.getServletContext().getRealPath("/").concat("upload");
        }
        return loadpath.replace("/",ResPathDto.separator) + ResPathDto.separator;
    }
    public static String getRootPath() {
        ServletContext sc = ContextLoader.getCurrentWebApplicationContext().getServletContext();
        String loadpath=sc.getRealPath("/upload");
        if(StringUtils.isEmpty(loadpath)) {
            loadpath = sc.getRealPath("/").concat("upload");
        }
        return loadpath.replace("/",ResPathDto.separator) + ResPathDto.separator;
    }
    public static String getTempPath() {
        return getRootPath() + "caches" + separator;
    }
}
