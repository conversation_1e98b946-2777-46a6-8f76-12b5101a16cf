

export function testMobile(val) {
    var pattern =/^(0|86|17951)?(13[0-9]|15[0-9]|16[0-9]|17[0-9]|18[0-9]|14[0-9]|19[0-9])[0-9]{8}$/;
    if (pattern.exec(val)==null) { return false ; 	}
    return true ;
}
/*
* el+ form实时校验手机号格式
*/
export function validateMobile (rule, value, callback) {
    var pattern =/^(0|86|17951)?(13[0-9]|15[0-9]|16[0-9]|17[0-9]|18[0-9]|14[0-9]|19[0-9])[0-9]{8}$/;
    if (pattern.exec(value)==null) {
        callback(new Error('请输入正确的手机号'))
    }else{
        callback()
    }
}
/*
* 邮编校验
*/
export function validatePostCode (postcode) {
    console.log('邮编=', postcode)
    if(postcode === ''){ return true; }
    return /^[0-9][0-9]{5}$/.test(postcode);
}
// 设置金额为默认格式（比如199,231,000.00)
export function formatMoney(number, places, symbol, thousand, decimal) {
    number = number || 0;
    places = !isNaN(places = Math.abs(places)) ? places : 2;
    symbol = symbol !== undefined ? symbol : "";
    thousand = thousand || ",";
    decimal = decimal || ".";
    var negative = number < 0 ? "-" : "",
        i = parseInt(number = Math.abs(+number || 0).toFixed(places), 10) + "",
        j = (j = i.length) > 3 ? j % 3 : 0;
    return symbol + negative + (j ? i.substr(0, j) + thousand : "") + i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + thousand) + (places ? decimal + Math.abs(number - i).toFixed(places).slice(2) : "");
}