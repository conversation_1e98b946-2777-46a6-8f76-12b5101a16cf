package cn.sphd.miners.modules.trainManage.dto;

import java.util.Date;
import java.util.List;

/**
 * @ClassName ExamInfoForUser
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/3/15 14:21
 * @Version 1.0
 */
public class RespExamInfoForUser {
    private Integer status;
    private Integer answerId;
    private Date startTime;
    private Date endTime;
    private Date date;
    private List<RespExamQuestion> respExamQuestionList;

    private RespExamUser respExamUser;

    public RespExamUser getRespExamUser() {
        return respExamUser;
    }

    public void setRespExamUser(RespExamUser respExamUser) {
        this.respExamUser = respExamUser;
    }

    public Integer getAnswerId() {
        return answerId;
    }

    public void setAnswerId(Integer answerId) {
        this.answerId = answerId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public List<RespExamQuestion> getRespExamQuestionList() {
        return respExamQuestionList;
    }

    public void setRespExamQuestionList(List<RespExamQuestion> respExamQuestionList) {
        this.respExamQuestionList = respExamQuestionList;
    }
}
