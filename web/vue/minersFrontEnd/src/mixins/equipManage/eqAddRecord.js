import * as addApi from "@/api/equipMent";
import {addUnitOk, getUnitList} from "@/api/technology";
import equipment_blank_sheet from '@/assets/templateFiles/eqList.xls'
import * as api from "@/api/equipMent";

export default {
    name:'',
    components:{},
    data(){
        return {
            baseForm: {
                amount: 1
            },
            selectedCatItem: {},
            supplierForm: {},
            unitForm: {},
            eqNameList: [],
            unitList: [],
            supplierList: [],
            eqAddList: [{
              "modelName" : '', // 型号
              "modelCode" : '', // 编号
              "unitId" : '', // 单位
              "category" : '', //  类别
              "lifeSpan" : '', //  预期的使用寿命
              "supplier" : '', //  供应商
              "originalValue" : '', //  原值
              "rDate" : '', //  到厂日期
              "conditions" : '', //   到厂时的新旧情况
              "memo" : ''
            }],
            categoryList: [],
            eqName: '',
            selectCatType: '',
            equipment_blank_sheet: equipment_blank_sheet,
            uploadAreaData:{
              uploadBtn1_txt: '浏览',
              filePath: '',
              originalFilename: '',
            },
            importDataOneList: [],
            importingData: {
              importSum:0,
              saveSum:0,
              buttonState:0, // 确定按钮状态1- 变亮 0- 置灰
            },
            importNoSaveData:{
              initAll:0,
              initWrong:0,
              falseList:[],
              userList:[]
            },
            editIcon: 0,
            importPaseForm: {},
            litAddLog: false,
            operDescription: false,
            addSupLog: false,
            addUnitLog: false,
            selectCatLog: false,
            addNameLog: false,
            uploadAddLog: false,
            importFalseVisible: false,
            importEditLog: false,
            abandonImportLog: false,
            uploadDeltipLog: false,
            supplierForRules: {
              fullName: [
                { required: true, message: '', trigger: 'blur' }
              ],
              name: [
                { required: true, message: '', trigger: 'blur' }
              ],
              codeName: [
                { required: true, message: '', trigger: 'blur' }
              ],
            },
            unitFormRules: {
              name: [
                { required: true, message: '', trigger: 'blur' }
              ]
            },
            baseFormRules: {
              equipment: [
                { required: true, message: '', trigger: 'blur' }
              ],
              amount: [
                { required: true, message: '', trigger: 'blur' }
              ],
            },
            importBaseFormRules: {
              equipment: [
                { required: true, message: '', trigger: 'blur' }
              ],
            },
            props: {
                label: 'fullName',
                children: 'zones',
                isLeaf: 'leaf'
            },
            firstFourCate: [],
            equipInitInfo: {
                pageInfo: {
                    currentPageNo: 1,
                    pageSize: 20
                }
            },
        }
    },
    created(){},
    mounted(){},
    computed: {
        redImportNum: function () {
            let list = this.importDataOneList
            let count = 0;
            let that = this
            list.forEach(item => {
                if (!item.equipmentName || !item.modelCode  ||  item.modelCode === '' || (item.rDate !== '' && !(new RegExp('^[0-9a-zA-Z]{6,16}$')).test(item.rDate))
                    || !((item.oLifeSpan && item.oLifeSpan%1 === 0 && item.oLifeSpan>0 ) || item.oLifeSpan === '')
                    || !((item.oValue && (item.oValue).split('.')[1] && (item.oValue).split('.')[1].length === 2) || item.oValue === '')
                    || that.modelCodeCount(item)) {
                    count++
                }
            })
            return count
        },
        modelCodeCount(){
            return function (info) {
                let list = this.importDataOneList.map(item => {
                    if (info.id != item.id)
                        return item.modelCode
                })
                return list.indexOf(info.modelCode) > -1
            }
        }
    },
    methods: {
        litAdd() {
            this.litAddLog = true
            this.eqAddList = []
            this.litAddReset()
            this.getNameList();
            this.getUnit();
            this.getSupList();
            this.getCategoryList()
            this.changeLitSnGroups()
        },
        litAddReset(){
            this.baseForm = {
                'amount': 1
            }
            //this.baseForm.amount = 1
            //this.baseForm.equipment = ''
            //this.baseForm.modelName = ''
            //this.baseForm.supplier = ''
            //this.baseForm.unitId = ''
        },
        changeLitSnGroups() {
            let num = this.baseForm.amount
            let line = this.eqAddList.length
            let item = {
                "modelName" : '', // 型号
                "modelCode" : '', // 编号
                "unitId" : '', // 单位
                "category" : '', //  类别
                "lifeSpan" : '', //  预期的使用寿命
                "supplier" : '', //  供应商
                "originalValue" : '', //  原值
                "rDate" : '', //  到厂日期
                "conditions" : '', //   到厂时的新旧情况
                "memo" : ''
            }

            if (num >= 1) {
                let count = num - line;
                if (count > 0) {
                    for (let i=0;i<count;i++) {
                        let temp = JSON.parse(JSON.stringify(item))
                        this.eqAddList.push(temp)
                    }
                } else if (count < 0){
                    this.eqAddList.splice(num-1, line-num)
                }
            } else {
                this.baseForm.amount = 1
                this.eqAddList.splice(1, line-1)
            }
        },
        hideLitAddLog(){
            this.litAddLog = false
        },
        addSup() {
            this.supplierForm = {
                'fullName': '',
                'name': '',
                'codeName': ''
            }
            this.addSupLog = true
        },
        addSupOk() {
            this.$refs.supplierRef.validate((valid) => {
                if (valid) {
                    let data = {...this.supplierForm}
                    data.type = 2
                    this.addSupLog = false
                    addApi.addSupplierDo(data).then(res => {
                        if(res.data.success === 1){
                            this.$message({
                                'type':'success',
                                'offset':'26',
                                'message': "新增成功"
                            })
                            this.getSupList();
                        }else{
                            this.$message({
                                'type':'error',
                                'offset':'26',
                                'message': "新增失败！"
                            })
                        }
                    })
                } else {
                    console.log("链接超时，请重试！")
                }
            })
        },
        getIndexNum(){
            let param = {
                'pageSize': 20,
                'currentPageNo': 1,
                'category': 0
            }
            api.getEqList(param).then(res => {
                this.equipInitInfo = JSON.parse(JSON.stringify(res.data))
            })
        },
        litAddOk() {
            this.$refs.baseFormRef.validate((valid) => {
                if (valid) {
                    let num = Number(this.baseForm.amount)
                    if (num >= 1) {
                        let addList = [];
                        let equipment = this.baseForm.equipment;
                        let model = this.baseForm.modelName;
                        let sup = this.baseForm.supplier;
                        let unitId = this.baseForm.unitId
                        if (num > 1 && (model === "" || sup === "" || unitId === "")) {
                            this.$message({
                                'type': 'error',
                                'offset': '26',
                                'message': "操作失败！“数量”大于“1”时，系统要求“装备器具名称”、“型号”、“供应商/加工方”及“单位”都需要有数据！"
                            })
                            return false
                        }
                        if (equipment === '') {
                            this.$message({
                                'type': 'error',
                                'offset': '26',
                                'message': "装备器具名称为必填项！"
                            })
                            return false
                        }
                        let len = this.eqAddList.length;
                        if (num === len) {
                            this.eqAddList.forEach(function (item) {
                                let data = {
                                    equipment,
                                    "modelName": model, // 型号
                                    "modelCode": item.modelCode, // 编号
                                    "unitId": unitId, // 单位
                                    "category": item.category, //  类别
                                    "lifeSpan": item.lifeSpan, //  预期的使用寿命
                                    "supplier": sup, //  供应商
                                    "originalValue": item.originalValue, //  原值
                                    "rDate": item.rDate, //  到厂日期
                                    "conditions": item.conditions, //   到厂时的新旧情况
                                    "memo": item.memo
                                }
                                addList.push(data);
                            });
                            this.litAddLog = false
                            addApi.addEquipmentSure(addList)
                                .then(res => {
                                    if (res.data.code === 0) {
                                        this.$message({
                                            'type': 'success',
                                            'offset': '26',
                                            'message': "操作成功！"
                                        })
                                        this.getIndexNum()
                                    } else {
                                        this.$message({
                                            'type': 'error',
                                            'offset': '26',
                                            'message': "操作失败！"
                                        })
                                    }
                                })
                        }
                    } else {
                        this.$message({
                            'type': 'error',
                            'offset': '26',
                            'message': "“数量”需大于“0”"
                        })
                    }
                } else {
                    this.$message({
                        'type':'error',
                        'offset':'26',
                        'message': `装备器具名称为必填项!`
                    })
                }
            })
        },
        filterCatBtn(str, index) {
            if (index || index === 0) {
                this.eqGroupItemIndex = index
            }
            this.selectCatType = str
            this.selectCatLog = true
            this.selectedCatItem = {}
        },
        loadNode(node, resolve) {
            addApi.getchildcat({'enabled': 1, 'parent': node.level === 0? 0 : node.data.id}).then(res => {
                let data = res.data
                if (data) {
                    data.map(item => {
                        item.leaf = !(item.childrens > 0)
                        item.fullName = item.name + ' ' + item.content
                    })
                }
                return resolve(data);
            })
        },
        loadBatch(node, resolve) {
            addApi.getchildcat({'enabled': 1, 'parent': node.level === 0? 0 : node.data.id}).then(res => {
                let data = res.data
                if (data) {
                    data.map(item => {
                        item.leaf = !(item.childrens > 0)
                    })
                }
                return resolve(data);
            })
        },
        selectCatOk() {
            this.selectedCatItem = this.$refs.catTree.currentNode.data
            const catId = this.selectedCatItem.id
            if(!catId){
                this.$message({
                    'type':'error',
                    'offset':'26',
                    'message': "请先选择类别！"
                })
                return false
            }
            let type = this.selectCatType
            if(type === 'importSelectCat'){
                this.importDataOneList[this.eqGroupItemIndex].category = catId
                this.importDataOneList[this.eqGroupItemIndex].categoryName = this.selectedCatItem.name
                this.editImportBtn(this.eqGroupItemIndex,'category')
            }else if(type === 'selectCatBtn'){
                let levArr = []
                if (this.selectedCatItem.descendants) {
                    levArr = this.selectedCatItem.descendants.split('/');
                } else {
                    levArr.push(this.selectedCatItem.id)
                }
                levArr[0] = Number(levArr[0])
                if (!this.firstFourCate.includes(levArr[0])) {
                    this.eqAddList[this.eqGroupItemIndex].category = catId
                    this.eqAddList[this.eqGroupItemIndex].name = this.selectedCatItem.name
                    this.eqAddList[this.eqGroupItemIndex].path = (this.selectedCatItem.descendants || this.selectedCatItem.id) + '/'
                } else {
                    let limit = -1;
                    for (let a=0;a<this.eqAddList.length;a++){
                        if (a !== this.eqGroupItemIndex) {
                            let editInfo = this.eqAddList[a];
                            if (editInfo.path) {
                                let arr = editInfo.path.split('/')
                                if (this.firstFourCate.includes(Number(arr[0])) && Number(arr[0]) !== levArr[0] ) {
                                    limit = a;
                                }
                            }
                        }
                    }
                    if (limit === -1) {
                        this.eqAddList[this.eqGroupItemIndex].category = catId
                        this.eqAddList[this.eqGroupItemIndex].name = this.selectedCatItem.name
                        this.eqAddList[this.eqGroupItemIndex].path = (this.selectedCatItem.descendants || this.selectedCatItem.id) + '/'
                    } else {
                        this.$message({
                            'type':'error',
                            'offset':'26',
                            'message': "操作失败！已有设备选择了一二三四类中的某个类别后，系统不支持其他设备再选择一二三四类中的其他类别。"
                        })
                        return false
                    }
                }
            } else  if(type === 'filterCatBtn') {
                this.filterCat = this.selectedCatItem.path
                this.getEquipList(1, catId, 4)
            }else if(type === 'msgEditSelectCatBtn') {
                let levArr = []
                if (this.selectedCatItem.descendants) {
                    levArr = this.selectedCatItem.descendants.split('/');
                } else {
                    levArr = [this.selectedCatItem.id]
                }
                if (this.eqScanList.length <= 1 || !this.firstFourCate.includes(levArr[0])) {
                    this.baseForm.category = catId
                    this.baseForm.path = this.selectedCatItem.name
                } else {
                    let limitCat = ``;
                    let editInfo = this.eqScanList[this.eqGroupItemIndex];
                    for (let a=0;a<this.eqScanList.length;a++){
                        if (editInfo.id !== this.eqScanList[a].id) {
                            let arr = []
                            if (this.eqScanList[a].descendants) {
                                arr = this.eqScanList[a].descendants.split('/');
                            } else {
                                arr = [this.eqScanList[a].category]
                            }
                            if (this.firstFourCate.includes(arr[0]) ) {
                                limitCat = arr[0].category;
                            }
                        }
                    }
                    if (limitCat === '') {
                        this.baseForm.category = catId
                        this.baseForm.path = this.selectedCatItem.name
                        this.baseForm.descendants = this.selectedCatItem.descendants? this.selectedCatItem.descendants : this.selectedCatItem.id + '/'
                    } else {
                        this.$message({
                            'type':'error',
                            'offset':'26',
                            'message': "操作失败！已有设备选择了一二三四类中的某个类别后，系统不支持其他设备再选择一二三四类中的其他类别。"
                        })
                        return false
                    }
                }
            }else if(type === 'moreSelectPathBtn'){
                let icon = this.equipNoCategoryIndex
                this.equipNoCategory.data[icon].category = this.selectedCatItem.id
                this.equipNoCategory.data[icon].path = this.selectedCatItem.path
                this.selectCatLog = false
            }else if(type === 'selectPathBtn'){
                let levArr = []
                if (this.selectedCatItem.descendants) {
                    levArr = this.selectedCatItem.descendants.split('/');
                } else {
                    levArr = [this.selectedCatItem.id]
                }
                let able = false
                if (this.firstFourCate.includes(levArr[0])) {
                    this.eqGroupList.forEach(item => {
                        if (item.pathIds) {
                            let itemP =  item.pathIds.split('/')
                            if (this.firstFourCate.includes(Number(itemP[0])) && Number(itemP[0]) !== Number(levArr[0])) {
                                if (item.id !== this.eqGroupList[this.eqGroupItemIndex].id) {
                                    able = true
                                }
                            }
                        }
                    })
                    if (able) {
                        this.$message({
                            'type':'error',
                            'offset':'26',
                            'message': "操作失败！已有设备选择了一二三四类中的某个类别后，系统不支持其他设备再选择一二三四类中的其他类别。"
                        })
                        return false
                    }
                }
                this.eqGroupList[this.eqGroupItemIndex].category = this.selectedCatItem.id
                this.eqGroupList[this.eqGroupItemIndex].path = this.selectedCatItem.path
                this.eqGroupList[this.eqGroupItemIndex].pathIds = (this.selectedCatItem.descendants || this.selectedCatItem.id) + '/'
            }else if(type === 'uniformSet') {
                this.uniformCatName = this.selectedCatItem
            }
            this.selectCatLog = false
        },
        getCategoryList(){
            let param = { 'enabled': 1, 'parent':0 }
            addApi.getchildcat(param).then(res => {
                this.categoryList = res.data
            })
        },
        addName() {
            this.addNameLog = true
            this.eqName = ''
        },
        addNameOk() {
            const that = this
            const txt = this.eqName
            if(txt.length === 0){
                this.$message({
                    'type':'error',
                    'offset':'26',
                    'message': "装备器具名称为必填项！"
                })
                return false
            }
            let param = { 'fullName': txt }
            this.addNameLog = false
            addApi.addnamer(param).then(res => {
                if(res.data.code === 0){
                    that.$message({
                        'type':'success',
                        'offset':'26',
                        'message': "新增成功！"
                    })
                    that.getNameList();
                }else{
                    that.$message({
                        'type':'error',
                        'offset':'26',
                        'message': "操作失败，因为系统里已有这个名称了！"
                    })
                }
            })
        },
        getNameList() {
            let param = {'fullName': ''}
            addApi.initCractern(param)
                .then(res => {
                    let list = res.data || []
                    this.eqNameList = list
                })
        },
        getUnit() {
            //1销售管理—通用型商品 2销售管理—专属商品 3技术管理—产品档案 4技术管理—构成管理 5配方管理-配方 6配方管理-材料 7采购-材料录入 8技术管理-材料 11装备器具
            let param = {'module': 11}
            getUnitList(param)
                .then(res => {
                    let list = res.data.list || []
                    this.unitList = list
                })
        },
        getSupList() {
            let param = {
                "currentPageNo": 1,
                "pageSize": 9999,
                'type': 2
            }
            addApi.getSupplierList(param)
                .then(res => {
                    let def = [{id:0, fullName: '整机系自行装配（零部件可能为外购、外加工或自制）'}]
                    let list = res.data.data.list || []
                    this.supplierList = [...def, ...list]
                })
        },
        addUnit() {
            this.unitForm.name = ""
            this.addUnitLog = true
        },
        addUnitOkBtn(module) {
            this.$refs.unitForm.validate((valid) => {
                if (valid) {
                    var name = this.unitForm.name;
                    addUnitOk({module, name}).then(res => {
                        var status = res.data['status'];
                        if (status == 1) {
                            this.$message({
                                'type': 'success',
                                'offset': '30',
                                'message': '新增成功！'
                            })
                            this.addUnitLog = false
                            this.getUnit()
                        } else {
                            var tipStr = '';
                            if (status == 0) {
                                tipStr = '新增失败！系统中已经有这个计量单位了。';
                            } else if (status == 2) {
                                tipStr = "<p>新增失败！这个计量单位在系统中已被停用。</p><p>如需恢复使用，请与计量单位的管理者沟通！</p>";
                            }
                            this.$message({
                                'type': 'error',
                                'offset': '30',
                                'message': tipStr
                            })
                        }
                    })
                }
            })
        },
        uploadAdd() {
            // 先查看有没有上次的暂存
            addApi.getStorageList()
                .then(res => {
                    let list = res.data || []
                    if(list.length > 0){
                        this.renderPreImportList(list)
                    }else{
                        this.uploadAddLog = true
                        this.uploadAreaData = {  uploadBtn1_txt: '浏览', filePath: '', originalFilename: '' }
                    }
                })
        },
        renderPreImportList(list) {
            this.mainNum = 2
            let num = this.redImportNum
            this.importDataOneList = list
            if(num === 0){
                this.uploadNext()
            }
        },
        leadingHide(){
            this.uploadAddLog = false
        },
        handleSuccess (file, files, ofile, totalFiles) {
            this.uploadAreaData.filePath = file.filename
            this.uploadAreaData.originalFilename = file.originalFilename
        },
        importBtnOk() {
            if(this.uploadAreaData.filePath){
                let params = {
                    path: this.uploadAreaData.filePath
                }
                addApi.importEqOk(params).then(res => {
                    this.uploadAddLog = false
                    let list = res.data.data || []
                    this.renderPreImportList(list)
                }).catch(err => {
                    console.log('err=', err)
                })

            }else{
                this.$message.error('您需选择一个文件后才能“导入”！')
            }
        },
        editUpload(temp,icon) {
            let info = JSON.parse(JSON.stringify(temp))
            this.editIcon = icon
            this.importEditLog = true
            // setTimeout(() => {
            //   this.$refs.importPaseForm.resetFields()
            // }, 500)
            // 预备数据
            this.getNameList();
            this.getUnit();
            if(!(info.oValue && (info.oValue).split(".")[1] && (info.oValue).split(".")[1].length === 2 )){
                info.oValue = ''
            }
            if(!(info.oLifeSpan && info.oLifeSpan%1 === 0 && info.oLifeSpan>0) ){
                info.oLifeSpan = ''
            }
            if(!((info.rDate || '').length === 6 && (new RegExp('^[0-9a-zA-Z]{6,16}$')).test(info.rDate)) ){
                info.rDate = ''
            }
            this.importPaseForm = info
        },
        delUpload(icon) {
            this.editIcon = icon
            this.uploadDeltipLog = true
        },
        uploadAddDeltipOk() {
            let info = this.importDataOneList[this.editIcon]
            this.removePreEq(info.id, 'single')
        },
        removePreEq(ids, type) {
            if (type === 'single') {
                this.uploadDeltipLog = false
            } else {
                this.abandonImportLog = false
            }
            let param = { 'ids': ids }
            addApi.importEqRemove(param).then(res => {
                if(res.data.msg === '操作成功'){
                    if(type === 'all'){
                        this.mainNum = 'main'
                    }else if(type === 'single'){
                        let all = this.allImportNum
                        all = all - 1
                        if(all === 0){
                            this.$message({
                                'type':'success',
                                'offset':'26',
                                'message': "本次导入数据已经全部删除！"
                            })
                        } else {
                            this.allImportNum = all
                            this.importDataOneList.splice(this.editIcon, 1)
                        }
                    }
                }
            })
        },
        importEditOk() {
            let that = this
            this.$refs.importPaseFormRef.validate(function (valid) {
                if (valid) {
                    const editInfo = that.importPaseForm
                    const info = that.importDataOneList[that.editIcon]
                    let data = {
                        'id': editInfo.id,
                        'equipment': editInfo.equipment,
                        'modelName': editInfo.modelName,
                        'modelCode': editInfo.modelCode,
                        'unitId': editInfo.unitId || '',
                        'oLifeSpan': editInfo.oLifeSpan,
                        'oValue': editInfo.oValue,
                        'rDate': editInfo.rDate,
                    }
                    if(data.unitId && data.unitId !== ""){
                        let unitItem = that.unitList.find(item=> item.id === editInfo.unitId)
                        data.unit = unitItem.name || ''
                    }
                    if(data.equipment && data.equipment !== ""){
                        let eNameItem = that.eqNameList.find(item=> item.id === editInfo.equipment)
                        data.equipmentName = eNameItem.fullName
                    }
                    if(data.equipment === ""){
                        data.equipmentName = '';
                    }

                    // 就是 code  name specifications model
                    if(data.equipmentName === info.equipmentName){
                        delete data.equipmentName
                    }
                    if(data.modelName === info.modelName){
                        delete data.modelName
                    }
                    let changeRepeat = true
                    if(data.modelCode === info.modelCode){
                        delete data.modelCode
                        changeRepeat = false
                    }
                    addApi.importEqEditOk(data).then(res => {
                        if(res.data.code === 0){
                            that.$message({
                                'type':'success',
                                'offset':'26',
                                'message': "操作成功"
                            })
                            that.importEditLog = false
                            info.equipment = data.equipment === "" ? data.equipment : data.equipment || info.equipment
                            info.equipmentName = data.equipmentName === "" ? data.equipmentName : data.equipmentName || info.equipmentName
                            info.modelName = data.modelName === "" ? data.modelName : data.modelName || info.modelName
                            info.modelCode = data.modelCode === "" ? data.modelCode : data.modelCode || info.modelCode
                            data.unitId && data.unitId !== '' ?  info.unitId = data.unitId : ''
                            data.equipment && data.equipment !== '' ?  info.equipment = data.equipment : ''
                            info.unit = data.unit
                            info.oLifeSpan = data.oLifeSpan
                            info.oValue = data.oValue
                            info.rDate = data.rDate
                            if(changeRepeat){
                                info.repeat = ''
                            }
                            that.importPaseForm[that.editIcon] = info
                            //this.uploadAdd()
                        }else{
                            that.$message({'type':'error', 'offset':'26', 'message': "操作失败"})
                        }
                    })
                } else {
                    this.$message({
                        'type':'error',
                        'offset':'26',
                        'message': `装备器具名称为必填项!`
                    })
                }
            })
        },
        uploadCancel() {
            this.abandonImportLog = true
        },
        delAlltipOk() {
            let sr = []
            this.importDataOneList.forEach(item => {
                sr.push(item.id)
            })
            this.removePreEq(sr.join(), 'all')
        },
        uploadNext() {
            let redNum = this.redImportNum
            if(redNum > 0){
                this.$message({
                    'type':'error',
                    'offset':'26',
                    'message': `以下${ redNum }条被标为红色的部分不符合右侧的要求，无法保存至系统。`
                })
            }else{
                let that = this
                addApi.importEqCheck().then(res => {
                    let list = res.data || [];
                    if(list.length > 0){ // 代号重复的
                        list.forEach(item => {
                            const id = item.id
                            let kind = that.importDataOneList.findIndex(value => value.id == id)
                            if (kind > -1) that.importDataOneList[kind].repeat = true
                        })
                        this.$message({
                            'type':'error',
                            'offset':'26',
                            'message': `${ list.length }条数据的装备器具编号重复`
                        })
                    }else{
                        this.uploadNext2()
                    }
                })
            }
        },
        uploadNext2(){
            this.getSupList();
            this.mainNum = 3
        },
        hideDesFun(){
            this.operDescription = false
        },
        uploadComplete() {
            addApi.importCompleteOk().then(res => {
                if(res.data.code === 0){
                    this.mainNum = 'main'
                    this.$message({'type':'success', 'offset':'26', 'message': '导入成功'})
                }else{
                    this.$message({'type':'error', 'offset':'26', 'message': '导入失败'})
                }
            })
        },
        editImportBtn(icon, type) {
            let item = this.importDataOneList[icon]
            this.editIcon = icon
            let data = {
                'id': item.id,
            }
            data[type] = item[type]
            this.editImport(data,type)
        },
        editImport(data,type){
            if(type === 'category'){
                //var name = data.name
                delete data.name
            }
            addApi.importEqEditOk(data).then(res => {
                if(res.data.code === 0){
                    switch (type){
                        case 'category':
                            this.$message({
                                'type':'success',
                                'offset':'26',
                                'message': "操作成功"
                            })
                            this.selectCatLog = false
                            break;
                        case 'conditions':
                            break;
                        case 'supplier':
                            break;
                        case 'memo':

                            break;
                        default:
                    }
                }else{
                    this.$message({
                        'type':'error',
                        'offset':'26',
                        'message': "操作失败"
                    })
                    switch (type){
                        case 'category':
                            break;
                        case 'conditions':
                        case 'memo':
                            this.importDataOneList[this.editIcon][type] = ''
                            break;
                        case 'supplier':
                            this.importDataOneList[this.editIcon][type] = "0"
                            break;
                        default:
                    }
                }
            })
        },
        hideAddFun(str) {
            switch (str) {
                case 'importEditLog':
                    this.importEditLog = false
                    break
                case 'uploadAddLog':
                    this.uploadAddLog = false
                    break
                case 'selectCatLog':
                    this.selectCatLog = false
                    break
                case 'addNameLog':
                    this.addNameLog = false
                    break
                case 'addSupLog':
                    this.addSupLog = false
                    break
                case 'addUnitLog':
                    this.addUnitLog = false
                    break
                case 'importFalseVisible':
                    this.importFalseVisible = false
                    break
                case 'uploadDeltipLog':
                    this.uploadDeltipLog = false
                    break
                case 'abandonImportLog':
                    this.abandonImportLog = false
                    break
            }
        },
        amountLimit(){
            this.baseForm.amount = this.limitNum(this.baseForm.amount)
        },
        limitNum(newVal){
            let value = ``
            if (newVal && newVal !== '') {
                value = newVal.replace(/[^\d]/g, '');
                if (value === '0' || value === 0) {
                    value = '';
                }
            }
            return value
        },
        limitOnlyInteger(name) {
            let newVal = ''
            switch (name) {
                case 'oLifeSpan':
                    newVal = this.importPaseForm[name]
                    break;
                case 'lifeSpan':
                    newVal = this.baseForm.lifeSpan
                    break;
            }
            let result = this.limitInteger(newVal)
            switch (name) {
                case 'oLifeSpan':
                    this.importPaseForm[name] = result
                    break;
                case 'lifeSpan':
                    this.baseForm.lifeSpan = result
                    break;
            }
        },
        limitInput(val) {
            let value =
                val
                    .replace(/[^\d.]/g, "")
                    .replace(/^0+(\d)/, "$1")
                    .replace(/^\./, "0.")
                    .match(/^\d*(\.?\d{0,2})/g)[0] || "";
            return value
        },
        limitInteger(val) {
            let value = val.replace(/[^\d]/g,"")
            return value
        },
        limitOriginalValue(icon){
            let val = this.eqAddList[icon].originalValue
            this.eqAddList[icon].originalValue = this.limitInput(val)
        },
        limitLifeSpan(icon){
            let val = this.eqAddList[icon].lifeSpan
            this.eqAddList[icon].lifeSpan = this.limitInteger(val)
        },
        getLimitVal(name) {
            let newVal = ''
            switch (name) {
                case 'oValue':
                    newVal = this.importPaseForm[name]
                    break;
                case 'taxRate':
                case 'chargePeriod':
                case 'imprestProportion':
                    newVal = this.addData.base[name]
                    break;
                case 'chargePeriodMg':
                    newVal = this.manageBaseData.chargePeriod
                    break;
                case 'imprestProportionMg':
                    newVal = this.manageBaseData.imprestProportion
                    break;
                case 'originalValue':
                    newVal = this.baseForm.originalValue
                    break;
                case 'taxRateMg':
                    newVal = this.manageInvoiceData.info.taxRate
                    break;
            }
            let result = this.limitInput(newVal)
            switch (name) {
                case 'oValue':
                    this.importPaseForm[name] = result
                    break;
                case 'taxRate':
                case 'chargePeriod':
                case 'imprestProportion':
                    this.addData.base[name] = result
                    break;
                case 'chargePeriodMg':
                    this.manageBaseData.chargePeriod = result
                    break;
                case 'imprestProportionMg':
                    this.manageBaseData.imprestProportion = result
                    break;
                case 'originalValue':
                    this.baseForm.originalValue = result
                    break;
                case 'taxRateMg':
                    this.manageInvoiceData.info.taxRate = result
                    break;
            }
        }
    }
}



/*补录结束*/
