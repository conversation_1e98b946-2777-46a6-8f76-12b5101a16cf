package cn.sphd.miners.modules.equipment.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

import cn.sphd.miners.common.utils.ExcelUtils;
import cn.sphd.miners.modules.equipment.entity.TEquModel;
import cn.sphd.miners.modules.equipment.entity.TEquModelImport;
import cn.sphd.miners.modules.equipment.service.ITEquModelImportService;
import cn.sphd.miners.modules.equipment.service.ITEquModelService;
import cn.sphd.miners.modules.material.entity.MtUnit;
import cn.sphd.miners.modules.material.service.UnitService;
import cn.sphd.miners.modules.mci.utils.AjaxResult;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.uploads.service.UploadService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import static cn.sphd.miners.modules.mci.utils.AjaxResult.success;
import static cn.sphd.miners.modules.mci.utils.AjaxResult.toAjax;

/**
 * Controller
 *
 * <AUTHOR>
 * @date 2023-03-30
 */
@Controller
@RequestMapping("/model/import")
public class TEquModelImportController {
    private String prefix = "system/import";

    @Autowired
    private ITEquModelImportService tEquModelImportService;

    @Autowired
    private UploadService uploadService;

    @Autowired
    private UnitService unitService;

    @Autowired
    private ITEquModelService modelService;

    /**
     * 查询列表
     */
    @PostMapping("/list")
    @ResponseBody
    public List<TEquModelImport> list(TEquModelImport tEquModelImport,User user) {
        tEquModelImport.setOrg(Long.valueOf(user.getOid()));
        tEquModelImport.setState(1L);//仅查询未完成导入的数据
        List<TEquModelImport> list = tEquModelImportService.selectTEquModelImportList(tEquModelImport);
        for (TEquModelImport modelImport : list) {
            //查重
            if(StringUtils.isNotBlank(modelImport.getModelCode())){
                TEquModel m = new TEquModel();
                m.setModelCode(modelImport.getModelCode());
                m.setOrg(Long.valueOf(user.getOid()));
                List<TEquModel> modelList = modelService.selectTEquModelList(m);
                if (modelList!=null&&modelList.size()>0){
                    modelImport.setRepeat(1);
                }
            }
        }
        return list;
    }

    /**
     * 导出列表
     */

    /**
     * 新增
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存
     */
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(TEquModelImport tEquModelImport) {
        return toAjax(tEquModelImportService.insertTEquModelImport(tEquModelImport));
    }

    /**
     * 修改
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        TEquModelImport tEquModelImport =
                tEquModelImportService.selectTEquModelImportById(id);
        mmap.put("tEquModelImport", tEquModelImport);
        return prefix + "/edit";
    }

    /**
     * 修改保存
     */
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(TEquModelImport tEquModelImport,User user) {
        return toAjax(tEquModelImportService.updateTEquModelImport(tEquModelImport,user));
    }

    /**
     * 删除
     */
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(tEquModelImportService.deleteTEquModelImportByIds(ids));
    }

    @PostMapping("/preImport")
    @ResponseBody
    public AjaxResult importData(User user, String path) throws IOException {
        File file = uploadService.copyTempFile(path);
        if (file.exists()) {
            LinkedHashMap<String, String> hashMap = new LinkedHashMap<>();
            hashMap.put("装备器具名称", "equipmentName");
            hashMap.put("型号", "modelName");
            hashMap.put("单位", "unit");
            hashMap.put("装备器具编号", "modelCode");
//            hashMap.put("类别","category");
            hashMap.put("预期的使用寿命", "oLifeSpan");
            hashMap.put("原值", "oValue");
//            hashMap.put("供应商/加工方","supplier");
            hashMap.put("到场日期", "rDate");
//            hashMap.put("到场时的新旧情况","conditions");
//            hashMap.put("其他需要备注的内容","memo");

            List<TEquModelImport> models = null;
            try {
                models = ExcelUtils.excelToList(new FileInputStream(file), "装备清单", TEquModelImport.class, hashMap, new String[]{"装备器具编号"});
            } catch (ExcelUtils.ExcelException e) {
                e.printStackTrace();
                return new AjaxResult(AjaxResult.Type.ERROR, e.getLocalizedMessage());
            }

            for (TEquModelImport model : models) {
                if(StringUtils.isNotBlank(model.getUnit())){
                    MtUnit unit = unitService.selectMtUnitByName(model.getUnit(),user.getOid());
                    if(unit!=null)
                        model.setUnitId(Long.valueOf(unit.getId()));
                }
                model.setCreateName(user.getUserName());
                model.setCreateTime(new Date());
                model.setCreator(Long.valueOf(user.getUserID()));
                model.setState(1L);
                model.setOrg(Long.valueOf(user.getOid()));
                tEquModelImportService.insertTEquModelImport(model);

                //查重
                if(StringUtils.isNotBlank(model.getModelCode())){
                    TEquModel m = new TEquModel();
                    m.setModelCode(model.getModelCode());
                    m.setOrg(Long.valueOf(user.getOid()));
                    List<TEquModel> modelList = modelService.selectTEquModelList(m);
                    if (modelList!=null&&modelList.size()>0){
                        model.setRepeat(1);
                    }
                }

            }
            return new AjaxResult(AjaxResult.Type.SUCCESS, "", models);
        }
        return new AjaxResult(AjaxResult.Type.ERROR, "导入失败");

    }


    @PostMapping("/import")
    @ResponseBody
    public AjaxResult imports(String ids,User user){
        return tEquModelImportService.importData(ids,user);
    }

    @GetMapping("/check")
    @ResponseBody
    public List<TEquModelImport> check(TEquModelImport modelImport, User user){
        return tEquModelImportService.check(modelImport,user);
    }
}
