import auth from '@/sys/auth'
import { createRouter, createWebHashHistory } from 'vue-router'

const routes = [
  {
    path: "/",
    name: "Start",
    component: () => import("../views/Start.vue"),
  },
  {
    path: "/audio2",
    name: "audio2",
    component: () => import("../views/audio2.vue"),
  },
  {
    path: "/live2",
    name: "live2",
    component: () => import("../views/live2.vue"),
  },
  {
    path: "/errPage",
    name: "errPage",
    component: () => import("../views/errPage.vue"),
  },
  {
    path: "/mainPage",
    name: "mainPage",
    component: () => import("../views/mainPage.vue"),
    children: [
      {
        path: '',
        name: 'home',
        component: () => import('../views/home.vue'),
        meta: {
          title: '首页'
        }
      },
      {
        path: '/createLyc',
        name: 'createLyc',
        component: () => import('../views/createLyc.vue'),
        meta: {
          title: '创建'
        }
      },
      {
        path: '/operatInstruction',
        name: 'operatInstruction',
        component: () => import('../views/operatInstruction.vue'),
        meta: {
          title: '操作说明'
        }
      },
      {
        path: '/instruction1',
        name: 'instruction1',
        component: () => import('../views/instruction1.vue'),
        meta: {
          title: '操作说明'
        }
      },
      {
        path: '/instruction2',
        name: 'instruction2',
        component: () => import('../views/instruction2.vue'),
        meta: {
          title: '操作说明'
        }
      }
    ]
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})
//无需登录可前往的页面
const guestPaths = [
    '/',
    '/audio2',
    '/live2',
    ''
]
////领地或选机构页面，本项目不存在这种情况
const manorPaths = []

router.beforeEach((to,from,next)=>{
  if (!auth.isLogged() && guestPaths.length > 0 && !guestPaths.includes(to.path)) {
    next('/')
  } else if (!auth.isLogInOrg() && manorPaths.length > 0 && !manorPaths.includes(to.path)) {
    next('/')
  } else {
    next()
  }
})
export default router
