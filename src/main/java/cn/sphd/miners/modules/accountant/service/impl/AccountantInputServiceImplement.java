package cn.sphd.miners.modules.accountant.service.impl;

import cn.sphd.miners.modules.accountant.entity.*;
import cn.sphd.miners.modules.accountant.mapper.*;
import cn.sphd.miners.modules.accountant.service.AccountantInputService;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * Created by root on 17-1-4.
 */
@Service
@Transactional(readOnly=false)
public class AccountantInputServiceImplement implements AccountantInputService {

    @Autowired
    VoucherMapper voucherMapper;
    @Autowired
    SettleMapper settleMapper;
    @Autowired
    VoucherSubjectMapper voucherSubjectMapper;
    @Autowired
    DetailMapper detailMapper;
    @Autowired
    SubjectHistoryMapper subjectHistoryMapper;
    @Autowired
    TAccountSubjectMapper tAccountSubjectMapper;
    @Autowired
    TAccountantSubjectCashMapper subjectCashMapper;
    @Autowired
    SubjectPeroidMapper subjectPeroidMapper;
    @Autowired
    TAccountantCashMapper cashMapper;
    @Autowired
    VoucherHistoryMapper voucherHistoryMapper;
    @Autowired
    VoucherUpdate voucherUpdate;


    @Override
    public JSONArray getVouchers(int oid) {
        TAccountantVoucher param = new TAccountantVoucher();
        param.setSource("1");
        param.setOrg(oid);
        List<TAccountantVoucher> list = voucherMapper.getInPutVouchers(param);
        JSONArray resultArray = voucherUpdate.getAccountantVouchers(list);
        return resultArray;
    }

    @Override
    public JSONObject insertVoucher(SubjectChooseParams params) {
        int oid = params.getOid();
        String prompt = "";//如果当前有已经记账但未结账的数据的话需要给用户提示
        TAccountantSettle settle = settleMapper.judgeCharge(oid);
        if (settle != null) {
            if ("2".equals(settle.getState())) {
                prompt = settle.getPeriod() + "月未结账，请您在本系统中“结账”！！";
                JSONObject res = new JSONObject();
                res.put("settleType",1);
                res.put("prompt",prompt);
                return res;//如果当前存在记账数据的话，返回空
            }
        }
        JSONObject state = voucherUpdate.insertVoucher(params);

        return state;
    }

    /*如果凭证没有结帐可以删除
    * id    凭证id
    * */
    @Override
    public int deleteVoucher(int id) {

        //判断一下这个凭证是不是有冲洪凭证，有的话不允许删除
        TAccountantVoucher paramVoucher = new TAccountantVoucher();
        paramVoucher.setId(id);
        TAccountantVoucher voucher = voucherMapper.getSingle(paramVoucher);

        int voucherFiled = voucher.getVoucher();
        List<TAccountantVoucher> list = voucherMapper.getVouchersByVoucherField(voucherFiled);

        if(list.size() > 1)
            return 0;
        //如果改凭证已经结帐也不能删除
        int is_settled = voucher.getIsSettled();
        if(is_settled > 0)
            return 0;

        TAccountantVoucherSubject paramSubject = new TAccountantVoucherSubject();
        paramSubject.setVoucherId(id);
        TAccountantSubjectDetail detailParam = new TAccountantSubjectDetail();
        TAccountantSubjectCash cashParam = new TAccountantSubjectCash();
        List<TAccountantVoucherSubject> subjects = voucherSubjectMapper.getSubjectByVoucher(paramSubject);
        voucherUpdate.deleteSubjectAndDetail(subjects,detailParam,cashParam);
        voucherMapper.delete(voucher);
        //删除完凭证后要检查被删除的科目是否在其他凭证中出现过，如果没有的话需要更新userable=1，即，可修改
        TAccountantSubjectEntity param = new TAccountantSubjectEntity();
        param.setOrg(voucher.getOrg());
        String sub = "";
        for (TAccountantVoucherSubject tvs:subjects) {
            List<TAccountantVoucherSubject> listSubjects = voucherSubjectMapper.listByVoucherSubject(tvs);
            if (listSubjects.isEmpty()) {
                //说明没有凭证在使用该科目，可以把userable更新成0
                sub = tvs.getSubject();
                param.setSubject(sub);
                if(sub != null && sub.length() > 4)
                {
                    param.setParent(sub.substring(0,sub.length() - 4));
                }
                else
                {
                    param.setParent(sub);
                }
                param.setUseable("1");
                tAccountSubjectMapper.updateUseable(param);
            }
        }
        //如果要删除的凭证是上月凭证，需要更新need_trial=1
        String belong_peroid = voucher.getBelongPeroid();
        if ("2".equals(belong_peroid))
        {
            voucherUpdate.updateNeedTrialField(voucher.getOrg(),"1");
        }
        return 1;

    }

    /*凭证修改，如果没有结帐可直接修改，否则进行冲洪*/
    @Override
    public JSONObject updateVoucher(SubjectChooseParams params) {

        JSONObject res = voucherUpdate.updateVoucher(params);
        return res;
    }

    @Override
    public JSONObject judgeMinorSettle(int org, UpdateVoucherParams params) {
        /*
        * 如果已经结账的话需要再判断是小结账还是总结账
        * 如果是小结账的话不能走冲红，应该先反结账在修改
        * 如果是总结账的话需要走冲红
        * */
        JSONObject res = new JSONObject();
        TAccountantVoucher voucher = new TAccountantVoucher();
        int vid = 0;
        int hisId = params.getVoucher_id();
        if (hisId > 0){
            TAccountantVoucherHistory voucherHistory = new TAccountantVoucherHistory();
            voucherHistory.setId(params.getId());
            voucherHistory = voucherHistoryMapper.getSingle(voucherHistory);
            voucher.setId(voucherHistory.getVoucherId());
            voucher = voucherMapper.getSingle(voucher);
            vid = voucher.getId();
        }
        else
        {
            vid = params.getId();
        }

        boolean flag = voucherUpdate.isMinorSettle(org,vid);
        res.put("res",flag);
        //如果该凭证没有小结账，则需要判断他能不能改凭证日期，如果当该机构上月大结账或小结账的话，要修改的这个凭证不能修改凭证日期
        if (!flag){
            SimpleDateFormat simpleFormat =  new SimpleDateFormat("yyyy-MM");
            Calendar c = Calendar.getInstance();
            c.set(Calendar.MONTH,c.get(Calendar.MONTH) - 1);
            String premonth = simpleFormat.format(c.getTime());
            TAccountantSettle settle = new TAccountantSettle();
            settle.setOrg(org);
            settle.setPeriod(premonth);
            //得到上月的结账数据
            settle = settleMapper.getSettleDay(settle);
            if (settle != null)
            {
                //说明上月有大结账或小结账，则不能修改凭证日期
                res.put("belong_peroid",false);
            }
            else {
                res.put("belong_peroid",true);//true表示可以修改凭证日期
            }
        }
        return res;
    }

    @Override
    public JSONObject getSubjects(TAccountantSubjectEntity param) {
        JSONObject result = new JSONObject();
        List<TAccountantSubjectEntity> list = tAccountSubjectMapper.getSubjects(param);
        //凭证日期，上月最后一天
        int oid = param.getOrg();
        String voucherDate = voucherUpdate.getPreMonthLastDay(oid);
        //统计所有相关的凭证明细帐
        List<TAccountantSubjectDetail> listDetails = voucherUpdate.getDetailsByVouchers(oid,voucherDate);
        if (list.size() > 1) {
            //如果size大于1说明存在子科目
            String superName = null;//存放一级科目的名称
            String secondName = null;//存放二级科目的名称
            String thirdName = null;//存放三级科目的名称
            List<TAccountantSubjectEntity> listDel = new ArrayList<TAccountantSubjectEntity>();//存放有子科目的科目，因为这些科目不需要返回
            for (TAccountantSubjectEntity subjectEntity :list) {
                int level = subjectEntity.getLevel();
                int maxChild = 0;
                switch (level)
                {
                    case 1:
                        listDel.add(subjectEntity);
                        superName = subjectEntity.getName();
                        continue;
                    case 2:
                        secondName = subjectEntity.getName();
                        maxChild = subjectEntity.getMaxChildSubjects();
                        if (maxChild > 0) {
                            listDel.add(subjectEntity);
                            continue;
                        }
                        else {
                            subjectEntity.setName(superName + "-" + secondName);
                            //获得该科目的余额
                            BigDecimal balance = getBalanceBySubject(subjectEntity,listDetails);
                            //如果该科目的余额为0的话则不予返回
                            if (balance.doubleValue() == 0) {
                                listDel.add(subjectEntity);
                            }
                            else{
                                subjectEntity.setBalance(balance);
                            }
                        }
                        break;
                    case 3:
                        thirdName = subjectEntity.getName();
                        maxChild = subjectEntity.getMaxChildSubjects();
                        if (maxChild > 0) {
                            listDel.add(subjectEntity);
                            continue;
                        }
                        else {
                            subjectEntity.setName(superName + "-" + secondName + "-" + thirdName);
                            //获得该科目的余额
                            BigDecimal balance = getBalanceBySubject(subjectEntity,listDetails);
                            //如果该科目的余额为0的话则不予返回
                            if (balance.doubleValue() == 0) {
                                listDel.add(subjectEntity);
                            }
                            else{
                                subjectEntity.setBalance(balance);
                            }
                        }
                        break;
                    case 4:
                        String name = subjectEntity.getName();
                        maxChild = subjectEntity.getMaxChildSubjects();
                        if (maxChild > 0) {
                            listDel.add(subjectEntity);
                            continue;
                        }
                        else {
                            subjectEntity.setName(superName + "-" + secondName + "-" + thirdName + "-" + name);
                            //获得该科目的余额
                            BigDecimal balance = getBalanceBySubject(subjectEntity,listDetails);
                            //如果该科目的余额为0的话则不予返回
                            if (balance.doubleValue() == 0) {
                                listDel.add(subjectEntity);
                            }
                            else{
                                subjectEntity.setBalance(balance);
                            }
                        }
                        break;
                }


            }
            list.removeAll(listDel);
        }
        if (list.size() == 1) {
            TAccountantSubjectEntity subjectEntity = list.get(0);
            //获得该科目的余额
            BigDecimal balance = getBalanceBySubject(subjectEntity,listDetails);
            //如果该科目的余额为0的话则不予返回
            if (balance.doubleValue() == 0) {
                list.clear();
            }
            else{
                subjectEntity.setBalance(balance);
            }
        }
        result.put("res",list);
        return result;
    }

    /*
    * 返回指定科目的余额，用在会计录入中返回指定科目的所有子科目功能上
    * */
    private BigDecimal getBalanceBySubject(TAccountantSubjectEntity subjectEntity,List<TAccountantSubjectDetail> listDetails) {
        BigDecimal result = null;
        //先判断该机构最后一次结账的时间，如果是上月的话，该科目的余额等于周期表最后一期的月结数据
        //如果上月没有结账的话需要统计上月凭证的余额情况和周期表中最后一期的月结数据
        //总的来说，改科目的余额等于中期表中最后一期的余额加上上月凭证中借方发生额-上月凭证中所有的贷方发生额
        TAccountantSubjectPeroid peroid = new TAccountantSubjectPeroid();
        peroid.setSubjectId(subjectEntity.getId());
//        peroid.setSubject(subjectEntity.getSubject());

        peroid = subjectPeroidMapper.getLastPeroid(peroid);
        if (peroid != null) {
            int year = 0;
            int month = 0;
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.MONTH,calendar.get(Calendar.MONTH) - 1);
            month = calendar.get(Calendar.MONTH) + 1;
            year = calendar.get(Calendar.YEAR);
            String pre = String.format("%02d",month);
            //上个月的，年月
            String preDate = year + "-" + pre;
            String peroidDate = peroid.getPeriod();
            String dir = peroid.getBalanceDirection();
            if ("3".equals(dir)) {
                dir = "1";//方向是平和借的处理方式一样
            }
            BigDecimal peroidBalance = peroid.getBalance();
            if (preDate.equals(peroidDate)) {
                //如果上月已经结账的话，余额只等于周期表中的余额
                result = peroidBalance;
            }
            else
            {
                //如果上月还未结账,改科目余额等于周期表中最后一期的余额加上上月凭证中改科目的借方发放额-贷方发生额
                BigDecimal balanceVoucher = getBalanceByVoucher(subjectEntity,listDetails);//根据上月凭证得到该科目借方发生额-贷方发生额的差
                int flag = balanceVoucher.signum();//-1 说明借方小于贷方，方向为贷方，0 平，1 说明借方大于贷方，方向为借方
                switch (flag){
                    case -1:
                        if ("1".equals(dir)) {
                            //如果上期余额方向是借方的话直接相加
                            return peroidBalance.add(balanceVoucher);
                        }
                        if ("2".equals(dir)) {
                            return peroidBalance.subtract(balanceVoucher).negate();
                        }
                        break;
                    case 0:
                        return  peroidBalance;
                    case 1:
                        if ("1".equals(dir)) {
                            //如果上期余额方向是借方的话直接相加
                            return peroidBalance.add(balanceVoucher);
                        }
                        if ("2".equals(dir)) {
                            return balanceVoucher.subtract(peroidBalance);
                        }
                        break;
                }

            }
        }
        else
        {
            //如果上月还未结账且周期表中还没有数据的话，余额等于上月凭证中改科目的借方发生额-贷方发生额
            BigDecimal balanceVoucher = getBalanceByVoucher(subjectEntity,listDetails);//根据上月凭证得到该科目借方发生额-贷方发生额的差
            return balanceVoucher;
        }
        return BigDecimal.valueOf(0.00);
    }

    /*返回上月凭证中改科目的借方发生额-贷方发生额的差*/
    private BigDecimal getBalanceByVoucher(TAccountantSubjectEntity subjectEntity,List<TAccountantSubjectDetail> listDetails) {
        BigDecimal creditBalance = BigDecimal.valueOf(0.00);
        BigDecimal debitBalance = BigDecimal.valueOf(0.00);
        String subject = subjectEntity.getSubject();

        for (TAccountantSubjectDetail detail : listDetails) {

            String detailSubject = detail.getSubject();
            if ("1".equals(detail.getBalanceDirection()) && subject.equals(detailSubject)) {
                //统计借方发生额
                BigDecimal cbalance = detail.getCredit() == null ? BigDecimal.valueOf(0.00) : detail.getCredit();
                creditBalance = creditBalance.add(cbalance);
            }
            if ("2".equals(detail.getBalanceDirection()) && subject.equals(detailSubject)) {
                //统计贷方发生额
                BigDecimal dbalance = detail.getDebit() == null ? BigDecimal.valueOf(0.00) : detail.getDebit();
                debitBalance = debitBalance.add(dbalance);
            }
        }
        return creditBalance.subtract(debitBalance);

    }


}
