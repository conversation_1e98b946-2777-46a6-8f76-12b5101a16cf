<template>
  <div class="demoSms">
    <div v-if="activeCodeUnlocking"><h4>测试H5激活</h4></div>
    <div v-if="!activeCodeUnlocking && isGuest"><h4>测试验证码登录、激活测试，请在未登录状态访问demoSms，输入在机构中存在的账号</h4></div>
<!--    <div v-if="!activeCodeUnlocking && isGuest && !unlocking">-->
    <div>
      <el-input v-model="mobile" placeholder="测试专用：请输入被锁定/要登陆的账号" style="width: 20em;"></el-input>
    </div>
<!--    <div v-if="!activeCodeUnlocking && isGuest && !unlocking && !onlyTpMember">-->
    <div>
      <el-button class="ty-color-blue" @click="createVerificationCode">验证码 验证登录</el-button>
    </div>
    <div v-if="!activeCodeUnlocking && isGuest && onlyTpMember">
      <el-button class="ty-color-blue" @click="createVerificationCode">(微信)验证码验证激活</el-button>
    </div>
    <div v-if="!activeCodeUnlocking && isGuest && (sended || onlyTpMember)">
      <el-input v-model="code" placeholder="请输入验证码" style="width: 20em;"></el-input>
    </div>
    <div v-if="!activeCodeUnlocking && isGuest && (sended || onlyTpMember)">
      <el-button class="ty-color-blue" @click="verificationCode">验证码验证</el-button>
    </div>
    <div v-if="isLogin && !isLogInOrg && !onlyTpMember"><h4>测试验证码修改密码，在验证码登录后，输入新密码即可</h4></div>
    <div v-if="isLogin && !isLogInOrg && !onlyTpMember">
      <el-input v-model="newPassword" placeholder="请输入新密码" style="width: 20em;"></el-input>
    </div>
    <div v-if="isLogin && !isLogInOrg">
      <el-button class="ty-color-blue" @click="changePassword">验证码改密码(仅短信验证登录有效)</el-button>
    </div>
    <div v-if="!activeCodeUnlocking && onlyTpMember && !isLogin"><h4>测试微信激活，使用锁定账号绑定的微信扫码后，使用手机验证码激活</h4></div>
    <div v-if="unlocking && twiceManor">
      <el-button class="ty-color-blue" @click="selectNewAcc">激活新领地</el-button>
      <el-button class="ty-color-blue" @click="selectOldAcc">激活旧领地</el-button>
    </div>
    <div v-if="unlocking && !twiceManor">
      <el-button class="ty-color-blue" @click="unlockAcc">激活</el-button>
    </div>
    <div  v-if="!activeCodeUnlocking && !unlocking && !onlyTpMember && isGuest && !isLogin">
      <div class="conTtl">微信登录</div>
      <div class="form">
        <wxlogin
            style="height: 416px;width: 100%"
            :appid="app.appId"
            scope="snsapi_login"
            theme='black'
            :redirect_uri="redirectUriStr"
            :href="href"
            :state="state"
            rel="external nofollow"
        ></wxlogin>
      </div>
    </div>
<!--    <div class="info">{{info}}</div>-->
  </div>
</template>
<script>
import { inject } from 'vue'
import auth from '@/sys/auth'
import {createVerificationCode, verificationCode, changePassword, unlockAcc, dupAcc, createUnlockVerificationCode, unlockVerificationCode, unlockGetNewAccId, selectOldAcc, selectNewAcc, selectOldAccByActiveCode, unlockAccByActiveCode, selectNewAccByActiveCode, getUnlockingToken} from '@/api/sendSms'
import qs from 'qs';
import wxlogin from 'vue-next-wxlogin'
import { mpCodeLogin } from '@/api/api'

export default {
  data() {
    return {
      pageName:'demoSms',
      activeCodeUnlocking: false,
      unlocking : false,
      sended: false,
      isGuest: true,
      isLogin: false,
      isLogInOrg: false,
      onlyTpMember: false,
      twiceManor: false,
      phoneNo: '',
      mobile: '',
      code: '',
      newPassword: '',
      unlockNewAccId: null,
      redirectUriStr: '',
      state: '',
      app: {
        tgCode: '',
        appId: '',
      },
      info: `
      demoSms激活Demo使用说明：
      一、Demo激活流程简化跳过了回答安全问题的流程，为了不与后续完整流程的开发起冲突，大部分后台接口目前都放在/test/路径下，后续开发可以直接复制Auth包后继续完善即可。
      二、此Demo主要包括短信激活、微信扫码激活、H5激活三个激活流程，保留了原有3.6分支的短信登录流程，用于演示短信验证后判断是走登录还是激活流程，另外，微信扫码激活的短信验证部分借用了短信登录/激活的按钮，如果实际开发在不同的窗口，可以拆分(auth.onlyTpMember()返回true是微信扫码激活的判断)。
      三、各个流程的操作如下：
      1、测试短信登录：
          与3.6分支的基本相同，此处增加了短信验证账户锁定情况下走短信激活流程的情况。
      2、测试短信激活：
          前置数据准备，需要将AuthAcc数据state设置为0，否则会走短信登录流程。
          涉及的接口：短信发送createVerificationCode、短信验证verificationCode、激活unlockAcc。
          补充说明，由于Demo短信激活和微信扫码后短信验证复用按钮和方法，因此用auth.onlyTpMember()加以判断。
                  同时，按照短信激活逻辑，账密或者短信登录进入短信激活流程不存在有新旧账号（领地）选择问题，可以跳过unlockGetNewAccId接口直接显示unlockAcc按钮即可。
      3、测试微信扫码激活：
          前置数据准备：事先绑定微信（TpMember表对应数据的accId与需要激活的AuthAcc表id一致），需要将AuthAcc数据state设置为0；如果要测试新旧账号选择，需要将另一个账号和需要激活的账号的手机号改成一样，改手机号的账号所属的用户User的手机号mobile也需要一起修改。
          涉及的接口：微信扫码、微信登录mpCodeLogin、短信发送createUnlockVerificationCode、短信验证unlockVerificationCode、判断是否有新领地unlockGetNewAccId、激活unlockAcc、选择旧领地selectOldAcc、选择新领地selectNewAcc。
      4、测试H5（activeCode）激活：
          前置数据准备，需要将AuthAcc数据state设置为0。
          生成激活码（激活链接的参数）：使用其他账号登录机构，访问"/test/getOrCreateActiveCode.do?accId="接口，accId=后面接state设置为0的AuthAcc的id值，模拟异常登录表生成H5激活码的操作；
          涉及的接口：进入h5激活状态getUnlockingToken、判断是否有新领地dupAcc、激活unlockAccByActiveCode、选择旧领地selectOldAccByActiveCode、选择新领地selectNewAccByActiveCode。
          补充说明：除了auth.onlyTpMember()可以判断是否是微信扫码后续外，此处激活按钮需要判断是短信/微信激活还是H5激活，因此在调用getUnlockingToken进入H5激活态后设置activeCodeUnlocking进行区分。
      `,
    }
  },
  components: {
    wxlogin
  },
  // beforeRouteLeave(to, from, next) {
  //     beforeRouteLeave(to, from, next, this)
  // },
  created(){
    // initNav({mid: 'demoSms', name: '测试短信', pageName: this.pageName}, null, this)
  },
  mounted() {
    const globalEnum = inject("globalConstEnum")
    this.app = globalEnum.getDescByKey('WonderssTpApps', 'WxPcLogin')
    this.passportHost = globalEnum.getDescByKey('Passport', 'host')
    let urlParams = location.search.lastIndexOf('?') >= 0 ? location.search : location.hash
    let params = qs.parse(urlParams.substring(urlParams.lastIndexOf('?')), { ignoreQueryPrefix: true })
    if(typeof params.code !== 'undefined' && typeof params.state !== 'undefined' && !auth.isLogged() && params.state == auth.getToken()) {
      this.mpCodeLogin(params)
    } else if(typeof params.activeCode !== 'undefined') {
      this.getUnlockingToken(params.activeCode)
    }
    if (location.href.indexOf('/vue/') > 0) {
      this.redirectUriStr = auth.webRoot.substring( 0, auth.webRoot.indexOf('://') + 3) + this.passportHost + '/' + (auth.getWebRootShort() ?? auth.getDomain(location.href) + '/vue/minersFrontEnd/dist/index.html#' ) + '/demoSms'
    } else {
      this.redirectUriStr = location.protocol + '//' + this.passportHost + '/' + auth.getDomain(location.href) + '/demoSms'
    }
    this.loadData()
  },
  destroyed () {
  },
  methods: {
    loadData() {
      this.isGuest = auth.isGuest()
      this.isLogin = auth.isLogged()
      this.isLogInOrg = auth.isLogInOrg()
      this.unlocking = auth.isUnlocking()
      this.onlyTpMember = auth.onlyTpMember()
      this.state = auth.getToken()
      this.sended = false
    },
    createVerificationCode(){
      if(!this.mobile) {
        this.$message.error("账号不能为空！")
      } else {
        let fun
        if (auth.onlyTpMember()) {
          fun = createUnlockVerificationCode;
        } else {
          fun = createVerificationCode;
        }
        fun({
          phoneNo: this.mobile
        }).then(res => {
          let result = res.data
          if(result.success > 0 ) {
            this.$message.success(result.data)
            this.sended = true
          } else {
            console.log(result.error)
            this.$message.error(result.error.message)
            if(result.error.code=='5') {
              this.sended = true
            }
          }
        })
      }
    },
    verificationCode(){
      if(!this.mobile) {
        this.$message.error("账号不能为空！")
      } else if(!this.code) {
          this.$message.error("验证码不能为空！")
      } else {
        let fun
        if (auth.onlyTpMember()) {
          fun = unlockVerificationCode;
        } else {
          fun = verificationCode;
        }
        fun({
          phoneNo: this.mobile,
          code: this.code
        }).then(res => {
          let result = res.data
          if(result.success > 0) {
            this.$message.success(result.data)
            if (fun == unlockVerificationCode) {
              unlockGetNewAccId({}).then( res => {
                let result = res.data
                if(result.success > 0 ) {
                  this.twiceManor = true
                  // this.$message.success(result.data)
                } else {
                  this.twiceManor = false
                  // console.log(result.error)
                  // this.$message.error(result.error.message)
                }
                this.loadData()
              })
            } else {
              this.loadData()
            }
          } else {
            this.$message.error(result.error.message)
          }
        })
      }
    },
    changePassword(){
      if(!this.newPassword) {
        this.$message.error("密码不能为空！")
      } else {
        changePassword({
          newPassword: this.newPassword
        }).then(res => {
          let result = res.data
          if(result.success > 0 ) {
            this.$message.success(result.data)
          } else {
            console.log(result.error)
            this.$message.error(result.error.message)
          }
        })
      }
    },
    selectOldAcc() {
      console.log('selectOldAcc 0', auth.isGuest(), auth.getSessionid(), auth.getToken())
      let fun
      if (auth.onlyTpMember()) {
        fun = selectOldAcc;
      } else {
        fun = selectOldAccByActiveCode;
      }
      fun().then(res => {
        console.log('selectOldAcc', res)
        let result = res.data
        if (result.success > 0) {
          console.log('selectOldAcc', result.data)
          this.$message.success(result.data)
          this.loadData()
          console.log('selectOldAcc success', auth.isGuest(), auth.getSessionid(), auth.getToken())
        } else {
          console.log(result.error)
          this.$message.error(result.error.message)
        }
      })
    },
    selectNewAcc() {
      let fun
      if (auth.onlyTpMember()) {
        fun = selectNewAcc;
      } else {
        fun = selectNewAccByActiveCode;
      }
      fun().then(res => {
        let result = res.data
        if (result.success > 0) {
          console.log('selectNewAcc', result.data)
          this.$message.success(result.data)
          this.loadData()
        } else {
          console.log(result.error)
          this.$message.error(result.error.message)
        }
      })
    },
    unlockAcc() {
      let fun
      console.log('this.activeCodeUnlocking', this.activeCodeUnlocking)
      if (this.activeCodeUnlocking) {
        fun = unlockAccByActiveCode;
      } else {
        fun = unlockAcc;
      }
      fun().then(res => {
        let result = res.data
        if (result.success > 0) {
          this.$message.success(result.data)
          this.loadData()
        } else {
          console.log(result.error)
          this.$message.error(result.error.message)
        }
      })
    },
    mpCodeLogin (params) {
      let data = {
        appId: this.app.appId,
        tgCode: this.app.tgCode,
        code: params.code
      }
      mpCodeLogin(data).then(res => {
        this.$route.query = {};
        let data = res.data
        console.log(data)
        if (data.success > 0) {
          console.error("怎么登录成功了？")
          this.$message.error("怎么登录成功了？")
        } else {
          let error = data.error
          console.log('登录失败！error msg', error)
          // console.log('startPage check token', auth.getByName('tpMemberId', auth.getToken()), auth.getUserID())
          switch (error.code) {
            case '6': //超过89天，需要短信验证激活
              console.log('需要短信验证激活',error.code, auth.isUnlocking(), auth.onlyTpMember())
              this.loadData()
            case '2': //超过75天，需要短信验证激活
              this.$alert('需要短信验证激活', '提示', {
                confirmButtonText: '确定',
              }).then(() => {
                this.loadData()
              })
              break
            case '3': //需要注册，微信号未与通用框架账号绑定
              // console.log('微信号未与通用框架账号绑定')
              this.$message.error('操作失败，该微信未绑定账号！')
              this.reFrash()
              break
            case '4': //可能是授权或者code问题
              this.$message.error('授权问题，建议更换其他登录方式！')
              this.reFrash()
              break
            case '5': //服务器问题，比如wonderss服务器访问微信/字节服务器异常
              console.log('服务器问题，比如wonderss服务器访问微信服务器异常，建议更换其他登录方式')
              this.$message.error('服务器问题，比如wonderss服务器访问微信服务器异常，建议更换其他登录方式')
              this.reFrash()
              break
          }
        }
      }).catch(err => {
        console.log('err=', err)
      })
    },
    getUnlockingToken (activeCode) {
      let data = {
        activeCode: activeCode
      }
      getUnlockingToken(data).then(res => {
        let result = res.data
        if (result.success > 0) {
          this.activeCodeUnlocking = true
          dupAcc({}).then( res => {
            let result = res.data
            if (result.success > 0) {
              this.twiceManor = true
              // this.$message.success(result.data)
            } else {
              this.twiceManor = false
              // console.log(result.error)
              // this.$message.error(result.error.message)
            }
            this.loadData()
          })
        } else {
          console.log(result.error)
          this.$message.error(result.error.message)
        }
      }).catch(err => {
        console.log('err=', err)
      })
    },
    reFrash(){
      setTimeout(function(){
        auth.cleanToken()
        location.reload()
      },3000)
    },
  }
}

</script>

<style lang="scss" scoped>
.demoSms{
  background-color: #e6e6e6;
  padding: 50px;
  overflow: auto;
  height: 100vh;
  .info {
    white-space: pre-wrap;
  }
}
</style>
