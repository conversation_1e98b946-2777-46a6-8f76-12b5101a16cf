
export default {
    name:'loginRecordMixin',
    components:{},
    data(){
        return {
            pageName:'loginRecord',
            currentDate: new Date().toLocaleDateString(),
            defineSearch:{
                visible: false,
                queryBeginTime: '',
                queryEndTime: ''
            },
            defineBeginTime: '',
            defineEndTime: '',
            loginRecords: [],
            loginHistory: [{
                "type" : 1, //1-第一层查询（及点击本日、本月、本年、自定义查询、按姓名、按时间后的查询）； 2-点击登录查询按钮后的查询
                "flag" : 0, //0-本日、1-本月、2-本年、3-自定义查询
                "sort" : 0  //0-按时间后的查询、1-按姓名
            }],
            loginCount: {},
            stateButtons: [
                { label: '本日', value: 0, active: true },
                { label: '本月', value: 1, active: false },
                { label: '本年', value: 2, active: false },
            ],
            loginDuration: {},
            tabIndex: 0,
            signActive: 0,
            flag: 0,
            sort: 0,
            folderFlag: -2,
            folderSign: 1,
            defineInfoDetailTtl: '',
            yearInfoDetailPage:{},
            dayInfoPage:{},
            loginPageInfo:{}
        }
    },
    created(){},
    mounted(){},
    computed(){},
    methods:{
        setLoginRecordIndex(){
            this.flag = 0
            this.sort = 0
            this.getLoginRecordList(1, 20, 0, 0);
        },
        defaultSearchBtn() {
            this.defineSearch.visible = true
        },
        sureLoginQuery() {
            if (this.defineSearch.queryBeginTime && this.defineSearch.queryBeginTime != '' && this.defineSearch.queryEndTime != '' && this.defineSearch.queryEndTime) {
                let json = {
                    "type" : 1,
                    "flag" : 3,
                    "sort" : this.sort,
                    "beginTime" : this.defineSearch.queryBeginTime,
                    "endTime" : this.defineSearch.queryEndTime
                };
                this.defineBeginTime = this.defineSearch.queryBeginTime
                this.defineEndTime = this.defineSearch.queryEndTime
                this.flag = 3
                this.setLoginState(json);
                this.defineSearch.visible = false
                this.getLoginRecordList(1, 20, 3, this.sort);
            }
        },
        changeState(flag) {
            flag = Number(flag)
            this.flag = flag
            this.stateButtons.forEach((btn) => {
                btn.active = btn.value === flag;
            })
            let json = {
                "type" : 1,
                "flag" : flag,
                "sort" : this.sort
            }
            this.setLoginState(json)
            this.getLoginRecordList(1, 20, flag, this.sort);
        },
        goBack() {
            this.loginHistory.pop();
            let param = this.loginHistory[this.loginHistory.length-1];
            let type = param.type;
            let flag = param.flag;
            let sort = param.sort;
            if(type === 1){
                this.folderFlag = -2
                this.getLoginRecordList(1,20,flag,sort);
            }else{
                this.seeLoginRecordListDetail(1,20,param)
            }
        },
        setLoginState(json){
            this.loginHistory.push(json)
        },
        signTab(type){
            this.signActive = Number(type)
            var param = this.loginHistory[this.loginHistory.length-1];
            param.sort = type
            this.seeLoginRecordListDetail(1,20,param);
        },
        seeLoginRecordListDetailBtn(info) {
            this.seeLoginRecordListDetail(1,20,info.nextJson);
            this.setLoginState(info.nextJson);
        },
        loginRecordsPage(pageItem){
            if (this.folderFlag === -2) {
                this.getLoginRecordList(pageItem.page, 20, this.flag, this.sort)
            } else {
                let param = this.loginHistory[this.loginHistory.length-1];
                this.seeLoginRecordListDetail(pageItem.page, 20, param)
            }
        },
        chargePort(port){
            switch (port){
                case "1":
                    return "手机端";
                case "3":
                    return "电脑端"
            }
        },
        chargeApp(str,nullvalue) {
            if(nullvalue === null  || nullvalue === undefined)
                nullvalue = '--';
            if(str === null  || str === undefined){
                return nullvalue;
            }else{
                return str ;
            }
        }
    }
}



