// 格式化时常
export function formatDurationTime(mss)
{
    mss = mss || 0
    var second = Math.ceil(mss);
    var minute = 0;
    var hour = 0;
    if (second > 0) {
        minute = parseInt(second/60);
        second = parseInt(second%60);
        if (minute > 60) {
            hour = parseInt(minute/60);
            minute = parseInt(minute%60);
        }
    }
    var time = '';
    if (minute == 0) {
        time += "00:" + parseInt(second);
    } else {
        time += parseInt(second);
    }
    if (minute != 0) {
        if (minute > 9) {
            if (time == 0) {
                time = parseInt(minute) + ":" + time + '0';
            } else {
                time = parseInt(minute) + ":" + time;
            }
        } else {
            time = "0" + parseInt(minute) + ":" + time;
        }
    }
    if (hour > 0) {
        time = parseInt(hour) + ":" + time;
    }
    console.log('time=', time)
    // 00:2
    // 00:23
    // 12:12
    // 2:12:12
    // 12:12:12
    let timeR = ''
    if(time.length > 5){ // 小时
        time.replace(':','小时')
        time.replace(':','分')
        time += '秒'
    }else if(time.indexOf("00") === 0){ // 只有秒
        time = time.split(':')[1] + '秒'
    }else{ // 分和秒
        time.replace(':','分')
        time += '秒'
    }
    return time;
}





