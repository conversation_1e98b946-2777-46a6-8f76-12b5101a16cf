package cn.sphd.miners.modules.material.dto;

public class SupplierContractDto {
    protected final long serialVersionUID = 1L;

    private Integer id;
    private String fullName;                  //供应商全称
    private Long numM;
    private Long contractNum;
    private Long inNum;
    private Long otherContractNum;

    public long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public Long getNumM() {
        return numM;
    }

    public void setNumM(Long numM) {
        this.numM = numM;
    }

    public Long getContractNum() {
        return contractNum;
    }

    public void setContractNum(Long contractNum) {
        this.contractNum = contractNum;
    }

    public Long getInNum() {
        return inNum;
    }

    public void setInNum(Long inNum) {
        this.inNum = inNum;
    }

    public Long getOtherContractNum() {
        return otherContractNum;
    }

    public void setOtherContractNum(Long otherContractNum) {
        this.otherContractNum = otherContractNum;
    }

    public SupplierContractDto(){

    }

    public SupplierContractDto(Integer id, String fullName) {
        this.id = id;
        this.fullName = fullName;
    }

    public SupplierContractDto(Integer id, String fullName, Long numM, Long contractNum, Long inNum, Long otherContractNum) {
        this.id = id;
        this.fullName = fullName;
        this.numM = numM;
        this.contractNum = contractNum;
        this.inNum = inNum;
        this.otherContractNum = otherContractNum;
    }
}
