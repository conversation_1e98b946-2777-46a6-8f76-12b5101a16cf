package cn.sphd.miners.modules.personal.entity;

import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by Administrator on 2016/10/25.
 * 加班表
 */
@Entity
@Table(name = "t_personnel_overtime")
public class PersonnelOvertime implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="title"  , length=100 , nullable=true , unique=false)
    private String title;   //标题

    @Column(name="duration"   , nullable=true , unique=false)
    private Double duration;//时长

    @Column(name="begin_time"   , nullable=true , unique=false)
    private Date beginTime;   //起始时间

    @Column(name="end_time"   , nullable=true , unique=false)
    private Date endTime;    //截止时间

    @Column(name="type"  , length=1 , nullable=true , unique=false)
    private String type; //1-工作日加班 2-周末假日加班 3-法定节假日加班'

    @Column(name="kind"   , nullable=true , unique=false)
    private Byte kind;  //类型:1-普通(默认),2-补报

    @Column(name="reason"  , length=255 , nullable=true , unique=false)
    private String reason;   //原因

    @Column(name="actual_type"  , length=1 , nullable=true , unique=false)
    private String actualType; //实际加班阶段:0-未加班(计划加班，默认为0，添加时直接添加),1-已加班（申报加班）,2-实际未加班

    @Column(name="actual_state"  , length=1 , nullable=true , unique=false)
    private String actualState; //加班时段类型:1-工作日加班2-周末假日加班3-法定节假日加班

    @Column(name="actual_duration"   , nullable=true , unique=false)
    private Double actualDuration;//申报时长

    @Column(name="actual_begin_time"   , nullable=true , unique=false)
    private Date actualBeginTime;   //申报起始时间

    @Column(name="actual_end_time "   , nullable=true , unique=false)
    private Date actualEndTime ;    //申报截止时间

    @Column(name="actual_reason"  , length=255 , nullable=true , unique=false)
    private String actualReason;   //申报原因

    @Column(name="approve_duration"   , nullable=true , unique=false)
    private Double approveDuration;//批准时长

    @Column(name="actual_apply_time",nullable=true , unique=false)
    private Date actualApplyTime;// 实际申请时间（申报）

    @Column(name="approve_explain"  , length=255 , nullable=true , unique=false)
    private String approveExplain;   //批准说明

    @Column(name="org"   , nullable=true , unique=false)
    private Integer org;

    @Column(name="depart_id"   , nullable=true , unique=false)
    private Integer departId;

    @Column(name="depart_name"   , nullable=true , unique=false)
    private String departName;

    @Column(name="post_id"   , nullable=true , unique=false)
    private Integer postID;

    @Column(name="postName"   , nullable=true , unique=false)
    private String postName;

    @Column(name="leader"   , nullable=true , unique=false)
    private Integer leader;

    @Column(name="comment"  , length=255 , nullable=true , unique=false)
    private String comment;

    @Column(name="approval_days"   , nullable=true , unique=false)
    private Integer approvalDays;

    @Column(name="state"  , length=1 , nullable=true , unique=false)
    private String state;

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date")
    @CreationTimestamp
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date")
    @UpdateTimestamp
    private Date updateDate;

    @Column(name="approve_Item"   , nullable=true , unique=false)
    private Integer approveItem;  //审批项目id

    @Column(name="approve_status"  , length=1 , nullable=true , unique=false)
    private String approveStatus;  //1-待处理 2-已批准(计划) 3-驳回 4-申报批准 9-撤回

    @Column(name="approve_level"   , nullable=true , unique=false)
    private Integer approveLevel;

    @Column(name="auditor"   , nullable=true , unique=false)
    private Integer auditor;

    @Column(name="auditor_name"  , length=100 , nullable=true , unique=false)
    private String auditorName;

    @Column(name="audit_date"   , nullable=true , unique=false)
    private Date auditDate;//审批日期

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;

    @Column(name="apply_memo"  , length=255 , nullable=true , unique=false)
    private String applyMemo; //申请备注(驳回理由)

    @Column(name="approve_memo"  , length=255 , nullable=true , unique=false)
    private String approveMemo;  //申报备注

    @Column(name="message_id"   , nullable=true , unique=false)
    private Integer messageId;

    @Column(name="final_result"  , length=1 , nullable=true , unique=false)
    private String finalResult;  //事件结果:0-其它,1-已完结的加班,2-未被认可的加班,3-提交了加班申请,但实际未加班,4-无需再填报时长的加班(某日加班未完全审批完时，考勤负责人修改了这日的考勤),5-加班申请被驳回,6-规定时间内未提交实际加班的数据(系统到时默认实际未加班)'

    @Column(name="assign_uuid"  , length=36 , nullable=true , unique=false)
    private String assignUuid;  //指派uuid，同一指派的ID相同

    @Column(name="assigner"   , nullable=true , unique=false)
    private Integer assigner;  //'指派人ID'

    @Column(name="assigner_name"  , length=50 , nullable=true , unique=false)
    private String assignerName;  //指派人姓名

    @Column(name="assigne_time"   , nullable=true , unique=false)
    private Date assigneTime;//'指派时间'

    @Column(name="reply_opinion"   , nullable=true , unique=false)
    private Byte replyOpinion;  //回复意见:1-同意,2-不同意

    @Column(name="reply_reason"  , length=255 , nullable=true , unique=false)
    private String replyReason;  //'回复原因'

    @ManyToOne(fetch= FetchType.EAGER )
    @JsonIgnore@JSONField(serialize = false)
    @JoinColumn(name="user", referencedColumnName = "userID" , nullable=true , unique=false , insertable=true, updatable=true)
    private User user;

    @Column(name="user"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer user_;

    //处理过程
//    @OneToMany(targetEntity=ApprovalProcess.class, fetch= FetchType.LAZY, mappedBy="over")//, cascade=CascadeType.ALL)
//    @Where(clause="business_type in (2, 6)")
//    private Set<ApprovalProcess> approvalProcessSet = new HashSet<ApprovalProcess>();

    @Transient
    private String leaderName;  //直属上级

    @Transient
    private String userName;  //用户名

    @Transient
    private Double userTotalHours;  //此人加班总时长

    @Transient
    private Integer approvalProcessId;  //审批流程id

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Double getDuration() {
        return duration;
    }

    public void setDuration(Double duration) {
        this.duration = duration;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getDepartId() {
        return departId;
    }

    public void setDepartId(Integer departId) {
        this.departId = departId;
    }

    public String getDepartName() {
        return departName;
    }

    public void setDepartName(String departName) {
        this.departName = departName;
    }

    public Integer getPostID() {
        return postID;
    }

    public void setPostID(Integer postID) {
        this.postID = postID;
    }

    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }

    public Integer getLeader() {
        return leader;
    }

    public void setLeader(Integer leader) {
        this.leader = leader;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Integer getApprovalDays() {
        return approvalDays;
    }

    public void setApprovalDays(Integer approvalDays) {
        this.approvalDays = approvalDays;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Integer getUser_() {
        return user_;
    }

    public void setUser_(Integer user_) {
        this.user_ = user_;
    }

    public String getLeaderName() {
        return leaderName;
    }

    public void setLeaderName(String leaderName) {
        this.leaderName = leaderName;
    }

    public String getActualType() {
        return actualType;
    }

    public void setActualType(String actualType) {
        this.actualType = actualType;
    }

    public String getActualState() {
        return actualState;
    }

    public void setActualState(String actualState) {
        this.actualState = actualState;
    }

    public Double getActualDuration() {
        return actualDuration;
    }

    public void setActualDuration(Double actualDuration) {
        this.actualDuration = actualDuration;
    }

    public Date getActualBeginTime() {
        return actualBeginTime;
    }

    public void setActualBeginTime(Date actualBeginTime) {
        this.actualBeginTime = actualBeginTime;
    }

    public Date getActualEndTime() {
        return actualEndTime;
    }

    public void setActualEndTime(Date actualEndTime) {
        this.actualEndTime = actualEndTime;
    }

    public String getActualReason() {
        return actualReason;
    }

    public void setActualReason(String actualReason) {
        this.actualReason = actualReason;
    }

    public Double getApproveDuration() {
        return approveDuration;
    }

    public void setApproveDuration(Double approveDuration) {
        this.approveDuration = approveDuration;
    }

    public String getApproveExplain() {
        return approveExplain;
    }

    public void setApproveExplain(String approveExplain) {
        this.approveExplain = approveExplain;
    }

    public Date getActualApplyTime() {
        return actualApplyTime;
    }

    public void setActualApplyTime(Date actualApplyTime) {
        this.actualApplyTime = actualApplyTime;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Double getUserTotalHours() {
        return userTotalHours;
    }

    public void setUserTotalHours(Double userTotalHours) {
        this.userTotalHours = userTotalHours;
    }

    public Integer getApprovalProcessId() {
        return approvalProcessId;
    }

    public void setApprovalProcessId(Integer approvalProcessId) {
        this.approvalProcessId = approvalProcessId;
    }

    public void setFinalResult(String finalResult) {
        this.finalResult = finalResult;
    }

    public String getFinalResult() {
        return finalResult;
    }

    public Byte getKind() {
        return kind;
    }

    public void setKind(Byte kind) {
        this.kind = kind;
    }

    public String getAssignUuid() {
        return assignUuid;
    }

    public void setAssignUuid(String assignUuid) {
        this.assignUuid = assignUuid;
    }

    public Integer getAssigner() {
        return assigner;
    }

    public void setAssigner(Integer assigner) {
        this.assigner = assigner;
    }

    public String getAssignerName() {
        return assignerName;
    }

    public void setAssignerName(String assignerName) {
        this.assignerName = assignerName;
    }

    public Date getAssigneTime() {
        return assigneTime;
    }

    public void setAssigneTime(Date assigneTime) {
        this.assigneTime = assigneTime;
    }

    public Byte getReplyOpinion() {
        return replyOpinion;
    }

    public void setReplyOpinion(Byte replyOpinion) {
        this.replyOpinion = replyOpinion;
    }

    public String getReplyReason() {
        return replyReason;
    }

    public void setReplyReason(String replyReason) {
        this.replyReason = replyReason;
    }
}
