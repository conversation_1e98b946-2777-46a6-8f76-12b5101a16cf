package cn.sphd.miners.modules.finance.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by lyx on 2021/11/18.
 * 财务_相关者表   1.179财务优化2101
 */
@Entity
@Table(name="t_finance_stakeholder")
public class FinanceStakeholder implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="org"   , nullable=true , unique=false)
    private Integer org;   //机构ID

    @Column(name="name"  , length=255 , nullable=true , unique=false)
    private String name;  //名称

    @Column(name="direction"  , length=1 , nullable=true , unique=false)
    private String direction;  //帐务类别:1-收入,2-支出(默认),3-内部非支出性转账

    @Column(name="enabled"  , length=1 , nullable=true , unique=false)
    private Boolean enabled=true;  //是否启用  0-否(false) 1-启用（true,默认）

    @Column(name="enabled_time"  , nullable=true , unique=false)
    private Date enabledTime;   //启/停用时间

    @Column(name="remark"  , length=100 , nullable=true , unique=false)
    private String remark;   //说明

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;   //备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;  //操作:1-增,2-删,3-修改,4-改名称中的别字,5-改新名,6-修改说明,7-启用,8-停用

    @Column(name="version_no"  , nullable=true , unique=false)
    private Integer versionNo;  //版本号,每次修改+1

    @Column(name="previous_id"  , nullable=true , unique=false)
    private Integer previousId;  //修改前记录ID

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDirection() {
        return direction;
    }

    public void setDirection(String direction) {
        this.direction = direction;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }
}
