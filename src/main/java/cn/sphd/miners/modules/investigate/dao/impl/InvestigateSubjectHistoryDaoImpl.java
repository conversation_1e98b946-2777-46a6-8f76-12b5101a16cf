package cn.sphd.miners.modules.investigate.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.investigate.dao.InvestigateSubjectHistoryDao;
import cn.sphd.miners.modules.investigate.entity.InvestigateSubjectHistory;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

/**
 * @ClassName InvestigateAnswerDaoImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/8/23 17:38
 * @Version 1.0
 */
@Repository
public class InvestigateSubjectHistoryDaoImpl extends BaseDao<InvestigateSubjectHistory,Serializable> implements InvestigateSubjectHistoryDao {

}
