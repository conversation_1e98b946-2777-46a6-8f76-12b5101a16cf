package cn.sphd.miners.modules.accountantReportTax.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctReportEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctReportHistoryEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxPeriodEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxPeriodHistoryEntity;
import cn.sphd.miners.modules.system.entity.User;

import java.io.Serializable;
import java.util.List;

public interface AcctTaxPeriodDao extends IBaseDao<AcctTaxPeriodEntity,Serializable> {
    List<AcctTaxPeriodHistoryEntity> getListByReportId(Integer reportHisId);//根据历史报表ID获取历史period列表

    List<AcctReportEntity> getReportList(User user);

    List<AcctTaxPeriodEntity> getByBeginMonth(AcctTaxPeriodEntity periodEntityParam);

    AcctTaxPeriodEntity getByDate(AcctTaxPeriodEntity periodEntity);

}
