
.mianCon{ display:none;  }
div.mainCon1{ display:block;  }
.ttl{ width: 200px;height:40px; border-bottom:1px solid #ccc; margin-bottom:20px; line-height:40px;  }
.ttl>span{   display:inline-block; height: 40px;   }
.ttl1{height:40px; margin-bottom:20px; line-height:40px;}
.ttl1 .infottl{padding-right: 30px; border-bottom:1px solid #ccc;  display:inline-block; height: 40px;    }
.addAttachCon{ display: flex;  }
.addAttachCon>div:nth-child(1){ flex:6; }
.addAttachCon>div:nth-child(2){ flex:1; }
.pannel,.addAttachCon{ margin:10px 15px;  }
.ty-flex{ display: flex;  }
.grayLink{ color: #aaa!important; margin-left:20px; border-bottom:none;   }
.linkBtn{ color: #0b94ea; cursor: default; line-height:40px;  }
.linkBtn:hover{ text-decoration: underline; font-weight: bold;  }
/*.bonceCon{ max-height:350px; overflow-y: auto;  }*/
.wid100{ width:100px;  }
body hr{ border-top:1px solid #ccc;  }
tr.gray>td{ background:#e6e6e6;  }
.tester{ margin:5px; background: #eee; border:1px solid #ddd; padding:5px; }
.fa-times{ color: #ccc; }
.fa-times:hover{ color: #e5303e; }
#fenList{ width:100%;  }
#fenList td{ text-align: center; line-height:35px;  }
#fenList td:nth-child(even){ text-align:left;  }
#fenList input.form-control{ width:80px; margin-right:10px;  }
#editTest2,#testScan{ width:700px;  }
#usesBanksScan{ width:600px;  }
#testScan{ line-height:30px;   }
#testScan .right{ text-align: right; padding-right:10px;  }
.tip{ text-align: center;  }
.ranking, .makeAgain{width: 800px;}
.ranking_head{margin-bottom: 50px;}
.ranking table td,.againning td,.desc td{line-height: 28px;}
.laydate-time-list>li:nth-child(1) {  width: 50%;  }
.laydate-time-list>li:nth-child(2) {  width: 50%;  }
.questionItem .fa-times { color:#101010;}
.questionItem p>span {  margin: 0 30px;}
.questionItem p>span i{ margin-right: 10px;}
.test-tab td{padding: 4px 0;}
.testEnd{padding: 20px;}
.ty-table-control tbody tr td span.ty-color-blue,.ty-table-control tbody tr td span.ty-color-red{display: inline-block;min-width: 56px;}
.settingMain{margin: auto;width: 80%;}
.settingMain hr{margin-top: 16px;  border-top: 1px solid #ccc;}
.pubSet{padding-top: 10px;}
.pubSet i{padding: 0 40px 0 20px;}
.pubSet i.fa-dot-circle-o{color: #48cfad; }
.areaA{  padding-top: 20px;padding-bottom: 10px; border-bottom: 1px solid #ccc;}
.areaA>div{  padding-right: 20px;}
.cells{margin-right: 16px;}
.cellRow{padding: 6px 0;}
.tdTtl{border-bottom: 1px solid #d7d7d7;}
.querySect{position: relative;}
.querySect input{position: absolute;top: 0;left: 0}
#publicitySetting{width: 600px;}
.recordttl h3{text-align: center;}
#assessmentRecordScan .bonceHead{border-bottom:none;height: 10px;}
#assessmentRecordScan tr.red td{ color: red;  }
.tbTtl{padding-bottom: 6px;margin-bottom: 20px;border-bottom: 1px solid #bfbfbf;}
.limitPy{width: 50%;  margin: auto;}
.clearValBody{position: relative;}
.clearInputVal {position: absolute;  top: 30px;  right: 10px;  color: #0070c0;  line-height: 30px;  font-size: 14px;  display: none;}
input:focus + i.clearInputVal{  display: block;  }
.durUnit{position: absolute;  top: 30px;  right: 10px;}
#passingScore {  padding-right: 44px;  }
#answerDuration {  padding-right: 44px;  }
#answerDuration + i.clearInputVal{  right: 44px;  }
.mainCon5{margin-left: 100px;}
.makeupScan {line-height: normal;}
#tb5{margin-top: 10px;}














