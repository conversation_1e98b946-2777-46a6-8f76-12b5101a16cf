<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sphd.miners.modules.inStockCheck.mapper.TMtInboundItemPackagingMapper">
  <resultMap id="BaseResultMap" type="cn.sphd.miners.modules.inStockCheck.entity.TMtInboundItemPackaging">
    <!--@mbg.generated-->
    <!--@Table t_mt_inbound_item_packaging-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org" jdbcType="INTEGER" property="org" />
    <result column="in_application_item" jdbcType="INTEGER" property="inApplicationItem" />
    <result column="material" jdbcType="INTEGER" property="material" />
    <result column="packaging" jdbcType="BIGINT" property="packaging" />
    <result column="quantity" jdbcType="DECIMAL" property="quantity" />
    <result column="renark" jdbcType="VARCHAR" property="renark" />
    <result column="operation" jdbcType="TINYINT" property="operation" />
    <result column="creator" jdbcType="INTEGER" property="creator" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="updator" jdbcType="INTEGER" property="updator" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="previous_id" jdbcType="INTEGER" property="previousId" />
    <result column="version_no" jdbcType="INTEGER" property="versionNo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, org, in_application_item, material, packaging, quantity, renark, `operation`, 
    creator, create_name, create_date, updator, update_name, update_date, previous_id, 
    version_no
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from t_mt_inbound_item_packaging
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from t_mt_inbound_item_packaging
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="cn.sphd.miners.modules.inStockCheck.entity.TMtInboundItemPackaging" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_mt_inbound_item_packaging (org, in_application_item, material, 
      packaging, quantity, renark, 
      `operation`, creator, create_name, 
      create_date, updator, update_name, 
      update_date, previous_id, version_no
      )
    values (#{org,jdbcType=INTEGER}, #{inApplicationItem,jdbcType=INTEGER}, #{material,jdbcType=INTEGER}, 
      #{packaging,jdbcType=BIGINT}, #{quantity,jdbcType=DECIMAL}, #{renark,jdbcType=VARCHAR}, 
      #{operation,jdbcType=TINYINT}, #{creator,jdbcType=INTEGER}, #{createName,jdbcType=VARCHAR}, 
      #{createDate,jdbcType=TIMESTAMP}, #{updator,jdbcType=INTEGER}, #{updateName,jdbcType=VARCHAR}, 
      #{updateDate,jdbcType=TIMESTAMP}, #{previousId,jdbcType=INTEGER}, #{versionNo,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="cn.sphd.miners.modules.inStockCheck.entity.TMtInboundItemPackaging" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_mt_inbound_item_packaging
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="org != null">
        org,
      </if>
      <if test="inApplicationItem != null">
        in_application_item,
      </if>
      <if test="material != null">
        material,
      </if>
      <if test="packaging != null">
        packaging,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="renark != null">
        renark,
      </if>
      <if test="operation != null">
        `operation`,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="updator != null">
        updator,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateDate != null">
        update_date,
      </if>
      <if test="previousId != null">
        previous_id,
      </if>
      <if test="versionNo != null">
        version_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="org != null">
        #{org,jdbcType=INTEGER},
      </if>
      <if test="inApplicationItem != null">
        #{inApplicationItem,jdbcType=INTEGER},
      </if>
      <if test="material != null">
        #{material,jdbcType=INTEGER},
      </if>
      <if test="packaging != null">
        #{packaging,jdbcType=BIGINT},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=DECIMAL},
      </if>
      <if test="renark != null">
        #{renark,jdbcType=VARCHAR},
      </if>
      <if test="operation != null">
        #{operation,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null">
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="previousId != null">
        #{previousId,jdbcType=INTEGER},
      </if>
      <if test="versionNo != null">
        #{versionNo,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.sphd.miners.modules.inStockCheck.entity.TMtInboundItemPackaging">
    <!--@mbg.generated-->
    update t_mt_inbound_item_packaging
    <set>
      <if test="org != null">
        org = #{org,jdbcType=INTEGER},
      </if>
      <if test="inApplicationItem != null">
        in_application_item = #{inApplicationItem,jdbcType=INTEGER},
      </if>
      <if test="material != null">
        material = #{material,jdbcType=INTEGER},
      </if>
      <if test="packaging != null">
        packaging = #{packaging,jdbcType=BIGINT},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=DECIMAL},
      </if>
      <if test="renark != null">
        renark = #{renark,jdbcType=VARCHAR},
      </if>
      <if test="operation != null">
        `operation` = #{operation,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        updator = #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null">
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="previousId != null">
        previous_id = #{previousId,jdbcType=INTEGER},
      </if>
      <if test="versionNo != null">
        version_no = #{versionNo,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.sphd.miners.modules.inStockCheck.entity.TMtInboundItemPackaging">
    <!--@mbg.generated-->
    update t_mt_inbound_item_packaging
    set org = #{org,jdbcType=INTEGER},
      in_application_item = #{inApplicationItem,jdbcType=INTEGER},
      material = #{material,jdbcType=INTEGER},
      packaging = #{packaging,jdbcType=BIGINT},
      quantity = #{quantity,jdbcType=DECIMAL},
      renark = #{renark,jdbcType=VARCHAR},
      `operation` = #{operation,jdbcType=TINYINT},
      creator = #{creator,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      updator = #{updator,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_date = #{updateDate,jdbcType=TIMESTAMP},
      previous_id = #{previousId,jdbcType=INTEGER},
      version_no = #{versionNo,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_mt_inbound_item_packaging
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.org,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="in_application_item = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.inApplicationItem,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="material = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.material,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="packaging = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.packaging,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="quantity = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.quantity,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="renark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.renark,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`operation` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.operation,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="creator = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="create_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.createName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.createDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="updator = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.updator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="update_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.updateName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="update_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.updateDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="previous_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.previousId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="version_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.versionNo,jdbcType=INTEGER}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update t_mt_inbound_item_packaging
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="org = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.org != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.org,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="in_application_item = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.inApplicationItem != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.inApplicationItem,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="material = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.material != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.material,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="packaging = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.packaging != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.packaging,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="quantity = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.quantity != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.quantity,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="renark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.renark != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.renark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`operation` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.operation != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.operation,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="creator = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createName != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.createName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createDate != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.createDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="updator = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updator != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.updator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateName != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.updateName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateDate != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.updateDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="previous_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.previousId != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.previousId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="version_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.versionNo != null">
            when id = #{item.id,jdbcType=INTEGER} then #{item.versionNo,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_mt_inbound_item_packaging
    (org, in_application_item, material, packaging, quantity, renark, `operation`, creator, 
      create_name, create_date, updator, update_name, update_date, previous_id, version_no
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.org,jdbcType=INTEGER}, #{item.inApplicationItem,jdbcType=INTEGER}, #{item.material,jdbcType=INTEGER}, 
        #{item.packaging,jdbcType=BIGINT}, #{item.quantity,jdbcType=DECIMAL}, #{item.renark,jdbcType=VARCHAR}, 
        #{item.operation,jdbcType=TINYINT}, #{item.creator,jdbcType=INTEGER}, #{item.createName,jdbcType=VARCHAR}, 
        #{item.createDate,jdbcType=TIMESTAMP}, #{item.updator,jdbcType=INTEGER}, #{item.updateName,jdbcType=VARCHAR}, 
        #{item.updateDate,jdbcType=TIMESTAMP}, #{item.previousId,jdbcType=INTEGER}, #{item.versionNo,jdbcType=INTEGER}
        )
    </foreach>
  </insert>
  <delete id="deleteByPrimaryKeyIn">
    <!--@mbg.generated-->
    delete from t_mt_inbound_item_packaging where id in 
    <foreach close=")" collection="list" item="id" open="(" separator=", ">
      #{id,jdbcType=INTEGER}
    </foreach>
  </delete>
  <update id="updateBatchUseMultiQuery" parameterType="java.util.List">
    <!--@mbg.generated-->
    <foreach collection="list" item="item" separator=";">
      update t_mt_inbound_item_packaging
      <set>
        <if test="item.org != null">
          org = #{item.org,jdbcType=INTEGER},
        </if>
        <if test="item.inApplicationItem != null">
          in_application_item = #{item.inApplicationItem,jdbcType=INTEGER},
        </if>
        <if test="item.material != null">
          material = #{item.material,jdbcType=INTEGER},
        </if>
        <if test="item.packaging != null">
          packaging = #{item.packaging,jdbcType=BIGINT},
        </if>
        <if test="item.quantity != null">
          quantity = #{item.quantity,jdbcType=DECIMAL},
        </if>
        <if test="item.renark != null">
          renark = #{item.renark,jdbcType=VARCHAR},
        </if>
        <if test="item.operation != null">
          `operation` = #{item.operation,jdbcType=TINYINT},
        </if>
        <if test="item.creator != null">
          creator = #{item.creator,jdbcType=INTEGER},
        </if>
        <if test="item.createName != null">
          create_name = #{item.createName,jdbcType=VARCHAR},
        </if>
        <if test="item.createDate != null">
          create_date = #{item.createDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.updator != null">
          updator = #{item.updator,jdbcType=INTEGER},
        </if>
        <if test="item.updateName != null">
          update_name = #{item.updateName,jdbcType=VARCHAR},
        </if>
        <if test="item.updateDate != null">
          update_date = #{item.updateDate,jdbcType=TIMESTAMP},
        </if>
        <if test="item.previousId != null">
          previous_id = #{item.previousId,jdbcType=INTEGER},
        </if>
        <if test="item.versionNo != null">
          version_no = #{item.versionNo,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsertSelectiveUseDefaultForNull" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_mt_inbound_item_packaging
    (org, in_application_item, material, packaging, quantity, renark, `operation`, creator, 
      create_name, create_date, updator, update_name, update_date, previous_id, version_no
      )
    values
    <foreach collection="list" item="item" separator=",">
      (
      <choose>
        <when test="item.org != null">
          #{item.org,jdbcType=INTEGER},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.inApplicationItem != null">
          #{item.inApplicationItem,jdbcType=INTEGER},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.material != null">
          #{item.material,jdbcType=INTEGER},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.packaging != null">
          #{item.packaging,jdbcType=BIGINT},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.quantity != null">
          #{item.quantity,jdbcType=DECIMAL},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.renark != null">
          #{item.renark,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.operation != null">
          #{item.operation,jdbcType=TINYINT},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.creator != null">
          #{item.creator,jdbcType=INTEGER},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.createName != null">
          #{item.createName,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.createDate != null">
          #{item.createDate,jdbcType=TIMESTAMP},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.updator != null">
          #{item.updator,jdbcType=INTEGER},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.updateName != null">
          #{item.updateName,jdbcType=VARCHAR},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.updateDate != null">
          #{item.updateDate,jdbcType=TIMESTAMP},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.previousId != null">
          #{item.previousId,jdbcType=INTEGER},
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT,
        </otherwise>
      </choose>
      <choose>
        <when test="item.versionNo != null">
          #{item.versionNo,jdbcType=INTEGER}
        </when>
        <!--@ignoreSql-->
        <otherwise>
          DEFAULT
        </otherwise>
      </choose>
      )
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="cn.sphd.miners.modules.inStockCheck.entity.TMtInboundItemPackaging" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_mt_inbound_item_packaging
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      org,
      in_application_item,
      material,
      packaging,
      quantity,
      renark,
      `operation`,
      creator,
      create_name,
      create_date,
      updator,
      update_name,
      update_date,
      previous_id,
      version_no,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      #{org,jdbcType=INTEGER},
      #{inApplicationItem,jdbcType=INTEGER},
      #{material,jdbcType=INTEGER},
      #{packaging,jdbcType=BIGINT},
      #{quantity,jdbcType=DECIMAL},
      #{renark,jdbcType=VARCHAR},
      #{operation,jdbcType=TINYINT},
      #{creator,jdbcType=INTEGER},
      #{createName,jdbcType=VARCHAR},
      #{createDate,jdbcType=TIMESTAMP},
      #{updator,jdbcType=INTEGER},
      #{updateName,jdbcType=VARCHAR},
      #{updateDate,jdbcType=TIMESTAMP},
      #{previousId,jdbcType=INTEGER},
      #{versionNo,jdbcType=INTEGER},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=INTEGER},
      </if>
      org = #{org,jdbcType=INTEGER},
      in_application_item = #{inApplicationItem,jdbcType=INTEGER},
      material = #{material,jdbcType=INTEGER},
      packaging = #{packaging,jdbcType=BIGINT},
      quantity = #{quantity,jdbcType=DECIMAL},
      renark = #{renark,jdbcType=VARCHAR},
      `operation` = #{operation,jdbcType=TINYINT},
      creator = #{creator,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      updator = #{updator,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_date = #{updateDate,jdbcType=TIMESTAMP},
      previous_id = #{previousId,jdbcType=INTEGER},
      version_no = #{versionNo,jdbcType=INTEGER},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="cn.sphd.miners.modules.inStockCheck.entity.TMtInboundItemPackaging" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into t_mt_inbound_item_packaging
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="org != null">
        org,
      </if>
      <if test="inApplicationItem != null">
        in_application_item,
      </if>
      <if test="material != null">
        material,
      </if>
      <if test="packaging != null">
        packaging,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="renark != null">
        renark,
      </if>
      <if test="operation != null">
        `operation`,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="updator != null">
        updator,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateDate != null">
        update_date,
      </if>
      <if test="previousId != null">
        previous_id,
      </if>
      <if test="versionNo != null">
        version_no,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="org != null">
        #{org,jdbcType=INTEGER},
      </if>
      <if test="inApplicationItem != null">
        #{inApplicationItem,jdbcType=INTEGER},
      </if>
      <if test="material != null">
        #{material,jdbcType=INTEGER},
      </if>
      <if test="packaging != null">
        #{packaging,jdbcType=BIGINT},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=DECIMAL},
      </if>
      <if test="renark != null">
        #{renark,jdbcType=VARCHAR},
      </if>
      <if test="operation != null">
        #{operation,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null">
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="previousId != null">
        #{previousId,jdbcType=INTEGER},
      </if>
      <if test="versionNo != null">
        #{versionNo,jdbcType=INTEGER},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=INTEGER},
      </if>
      <if test="org != null">
        org = #{org,jdbcType=INTEGER},
      </if>
      <if test="inApplicationItem != null">
        in_application_item = #{inApplicationItem,jdbcType=INTEGER},
      </if>
      <if test="material != null">
        material = #{material,jdbcType=INTEGER},
      </if>
      <if test="packaging != null">
        packaging = #{packaging,jdbcType=BIGINT},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=DECIMAL},
      </if>
      <if test="renark != null">
        renark = #{renark,jdbcType=VARCHAR},
      </if>
      <if test="operation != null">
        `operation` = #{operation,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null">
        updator = #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null">
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="previousId != null">
        previous_id = #{previousId,jdbcType=INTEGER},
      </if>
      <if test="versionNo != null">
        version_no = #{versionNo,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

<!--auto generated by MybatisCodeHelper on 2025-01-08-->
  <select id="findByAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from t_mt_inbound_item_packaging
    <where>
      <if test="id != null">
        and id=#{id,jdbcType=INTEGER}
      </if>
      <if test="org != null">
        and org=#{org,jdbcType=INTEGER}
      </if>
      <if test="inApplicationItem != null">
        and in_application_item=#{inApplicationItem,jdbcType=INTEGER}
      </if>
      <if test="material != null">
        and material=#{material,jdbcType=INTEGER}
      </if>
      <if test="packaging != null">
        and packaging=#{packaging,jdbcType=BIGINT}
      </if>
      <if test="quantity != null">
        and quantity=#{quantity,jdbcType=DECIMAL}
      </if>
      <if test="renark != null">
        and renark=#{renark,jdbcType=VARCHAR}
      </if>
      <if test="operation != null">
        and `operation`=#{operation,jdbcType=TINYINT}
      </if>
      <if test="creator != null">
        and creator=#{creator,jdbcType=INTEGER}
      </if>
      <if test="createName != null">
        and create_name=#{createName,jdbcType=VARCHAR}
      </if>
      <if test="createDate != null">
        and create_date=#{createDate,jdbcType=TIMESTAMP}
      </if>
      <if test="updator != null">
        and updator=#{updator,jdbcType=INTEGER}
      </if>
      <if test="updateName != null">
        and update_name=#{updateName,jdbcType=VARCHAR}
      </if>
      <if test="updateDate != null">
        and update_date=#{updateDate,jdbcType=TIMESTAMP}
      </if>
      <if test="previousId != null">
        and previous_id=#{previousId,jdbcType=INTEGER}
      </if>
      <if test="versionNo != null">
        and version_no=#{versionNo,jdbcType=INTEGER}
      </if>
    </where>
  </select>
</mapper>