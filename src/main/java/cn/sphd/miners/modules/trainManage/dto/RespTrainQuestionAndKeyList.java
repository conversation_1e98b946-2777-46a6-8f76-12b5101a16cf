package cn.sphd.miners.modules.trainManage.dto;

import cn.sphd.miners.modules.trainManage.entity.TrainingMaterial;

import java.util.List;

/**
 * @ClassName RespTrainQuestionAndKeyList
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/2/22 9:21
 * @Version 1.0
 */
public class RespTrainQuestionAndKeyList {
    private Integer status;
    private TrainingMaterial trainingMaterial;
    private List<RespTrainQuestionKeyList> trainingQuestionList;
    private Integer yesNum;

    public Integer getYesNum() {
        return yesNum;
    }

    public void setYesNum(Integer yesNum) {
        this.yesNum = yesNum;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public TrainingMaterial getTrainingMaterial() {
        return trainingMaterial;
    }

    public void setTrainingMaterial(TrainingMaterial trainingMaterial) {
        this.trainingMaterial = trainingMaterial;
    }

    public List<RespTrainQuestionKeyList> getTrainingQuestionList() {
        return trainingQuestionList;
    }

    public void setTrainingQuestionList(List<RespTrainQuestionKeyList> trainingQuestionList) {
        this.trainingQuestionList = trainingQuestionList;
    }
}
