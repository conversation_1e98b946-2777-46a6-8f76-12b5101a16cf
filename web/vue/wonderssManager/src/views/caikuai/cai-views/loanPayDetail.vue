
<template>
  <div>
    <div class="head">
      <div style="width: 100%">
        <PageHeader title="财会" :showBack="true" :showClose="true" @mycloseFun="closeFun"></PageHeader>
      </div>
    </div>

    <div v-if="info">
      <ul class=" margin-10-15">
        <li>
          <span>{{ info.createName }}
            {{ new Date(info.createDate).format('yyyy-MM-dd hh:mm:ss') }} 录入
          </span>
          <span class="ty-linkBtn ty-right" @click="editLog"> 修改记录 </span>
        </li>
      </ul>
      <div class="hr_gray"></div>
      <ul class="xiangUlB margin-10-15" v-if="info.loanType === '1'">
        <li><span>付款金额</span><span>{{ formatCurrency(info.repaymentAmount) }}</span></li>
        <li><span>付款日期</span><span>{{ new Date(info.repaymentTime).format('yyyy-MM-dd') }}</span></li>
        <li><span>支付方式</span><span>{{ chargePayMethod(info.repaymentMethod) }}</span></li>
        <li v-if="info.repaymentMethod === '5'"><span>转账银行</span><span>{{ (info.receiveBank) }}</span></li>
        <div v-if="info.repaymentMethod === '3'">
          <li><span></span><span>{{ info.billType == 1 ? '内部': '外部'  }}转账支票</span></li>
          <li><span>银行账户</span><span>{{ info.account   }}</span></li>
          <li><span>支票号</span><span>{{ info.billNo   }}</span></li>
          <li><span>支票到期日</span><span>{{ new Date(info.billEndDate).format('yyyy-MM-dd')  }}</span></li>
          <li><span>接收日期</span><span>{{ new Date(info.billReceiveDate).format('yyyy-MM-dd') }}</span></li>
          <li><span>接收经手人</span><span>{{ info.receiveOperatorName    }}</span></li>
          <li><span>支付经手人</span><span>{{ info.paymentOperatorName == 1    }}</span></li>

        </div>
      </ul>
      <ul class="xiangUlB margin-10-15" v-if="info.loanType === '2'">
        <li><span>收款金额</span><span>{{ formatCurrency(info.repaymentAmount) }}</span></li>
        <li><span>收款日期</span><span>{{ new Date(info.repaymentTime).format('yyyy-MM-dd') }}</span></li>
        <li><span>支付方式</span><span>{{ chargePayMethod(info.repaymentMethod) }}</span></li>
        <li v-if="info.repaymentMethod === '5'"><span>到账日期</span><span>{{ new Date(info.arriveDate).format('yyyy-MM-dd') }}</span></li>
        <li v-if="info.repaymentMethod === '5'"><span>收款银行</span><span>{{ (info.receiveBank) }}</span></li>
        <li v-if="info.repaymentMethod === '1'"><span>收款经手人</span><span>{{ (info.receiveOperatorName) }}</span></li>
      </ul>
      <div class="hr_gray"></div>

    </div>



  </div>

</template>

<script>
import finance from '@/mixins/finance.js'
export default {
  name: "loanDetail",
  data() {
    return {
      info: null,
    }
  },
  mixins: [finance],
  mounted() {
    let id = this.$route.params.id
    this.getOrdRepaymentDetail(id)
  },
  methods: {
    getOrdRepaymentDetail(id){
      this.financeApi.ordRepaymentDetail({ id: id }).then(res1=>{
        let res = res1.data.data
        this.info = res

      }).catch(err=>{

      })
    },
    editLog(){
      this.$message.info('暂无修改记录')
    },

  },
}
</script>

<style scoped lang="scss">
@import "../../../style/account.scss";
@import "../../../style/common.scss";

.turnHang{
  display: inline-block;  white-space:normal; line-height: 20px;
  max-width: 60%;
}
.loan{
  .fa-angle-right{ font-size: 20px;  }

}
</style>