.ty-mainData > div {
    display: none;
}

.flexCon {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
}

.kindChoose {
    width: 40%;
}

.conWt {
    margin: 0 10px;
    font-weight: 600;
}

#addOrderKind {
    padding-left: 10px;
    color: #777;
    height: 32px;
    border: 1px solid #c5c5c5;
    background: #fff;
}

#addOrderKind option[disabled] {
    background: #d9d9d9;
}

.keywordSearch input, .bonceCon .keywordSearch input {
    padding: 0 10px;
    font-size: 12px;
    min-width: 200px;
    height: 30px;
    line-height: 26px;
    border-radius: 4px 0 0 4px;
    border: 1px solid #aeaeae;
}

.searchSect .ssEnsure{
    height: 30px;
    line-height: 30px;
    border-radius: 0 4px 4px 0;
    padding: 0px 16px;
}
.specialCol > div {
    border: 1px solid #ddd;
    border-radius: 2px;
}

div.leCat {
    width: 250px;
    float: left;
    color: #0b94ea;
    line-height: 30px;
}

div.leCat:hover {
    cursor: default;
}

.leCat > div {
    color: #333;
    padding-left: 15px;
    font-weight: bold;
    background: #c0d3ea;
}

.leCat > ul > li {
    padding-left: 25px;
    font-size: 13px;
}

.leCat > ul > li:hover {
    background: #cdddea;
    font-weight: bold;
    color: #0f62ea;
}

.btnCat {
    padding-right: 15px;
    color: #0f62ea
}

.btnCat:hover {
    text-decoration: underline;
    cursor: default;
}

.catNum {
    float: right;
    padding-right: 15px;
}

div.riMt {
    padding: 15px;
}

div.riMt thead {
    background: #f7e5baa6;
}

div.riMt thead td.ty-empty {
    background: #fff;
    border-color: #fff;
}

.stockJump {
    float: left;
    color: #5d9cec;
}

.stockJump .btn-default {
    color: #5d9cec;
}

.quickLine {
    margin-bottom: 20px;
}

.specialTb tbody tr td.br-empty {
    border: none;
}

.setSupplier {
    margin: 50px 0 50px 50px;
}

.existing {
    margin-right: 60px;
}

.existing span {
    margin-right: 20px;
    cursor: pointer;
}

.unExisting span {
    padding: 6px 20px;
    background: #ccc;
}

.supplierInfo {
    margin: 20px 0 10px 0;
}

.supplierInfo .spTl {
    margin: 0 16px 0 30px;
}

.spTb .td-lf td {
    text-align: left;
}

.spTb .td-blue td {
    background: #ddebf7;
    min-width: 90px;
}

.spTb .td-yellow td {
    background: rgba(255, 242, 204, 0.65);
}
.spNm{
    display: inline-block;
}
.spCd{
    margin-right: 80px;
}
.gapR {
    margin-right: 10px;
}

.gapRL {
    margin-right: 50px;
}

.gapBt {
    margin-bottom: 30px;
}

.ftGapT {
    margin-top: 150px;
}

.nextStep {
    margin-top: 50px;
}

.nextBtn {
    margin-top: 20px;
}

.nextBtn span {
    margin: 0 30px;
}
.supplierInfo > div {
    display: inline-block;
}
.bg-yellow thead {
    border-color: #fff0;
    background-color: #fff2cc;
}

.setAdvance > div {
    margin-right: 10px;
}

.setAdvance span {
    margin: 10px 20px 10px 30px;
}

.xing:before {
    content: "*";
    width: 5px;
    height: 5px;
    display: inline-block;
    color: red;
    font-weight: bolder;
    position: relative;
}

.advanceItem {
    padding-left: 30px;
    margin-bottom: 30px;
}

.advanceItem > div {
    margin: 10px 0;
}

.advanceItem span.xing {
    margin-right: 20px;
}

.advanceItem input {
    margin-right: 10px;
}

.advanceItem select{
    width: 162px;
}

.ty-table tr td.createInfo {
    font-size: 11px;
    white-space: nowrap;
}

.advanceMain {
    display: none;
}

.itemPrice {
    position: relative;
}
.itemPrice input{
    padding-right: 12px;
}
.itemPrice input:focus + i{
    display: inline-block;
}
.itemPrice i {
    position: absolute;
    padding: 0 6px;
    right: 6px;
    font-style: normal;
    display: none;
}
.memoItem{
    margin-top: 20px;
    clear: both;
}
.memoItem > div{
    float: left;
    margin-right: 30px;
    min-width: 119px;
    line-height: 30px;
}
.memoItem .fa{
   margin-right: 12px;
}
#matBuyInfo tr td input,#updateOrderList tr td input{
    width: 100%;
    border: 1px solid #ddd;
    line-height: 28px;
    text-align: center;
}
.ty-table tr td span.tb-btn-sm {
    padding: 4px 6px;
    font-size: 12px;
}
.ty-table-control tbody tr td.moveMemo span{
    white-space: normal;
}
.memoArea{
    border: 1px solid #ccc;
    min-height: 30px;
    text-overflow: ellipsis;
    overflow: hidden;
}
.leaveLft {
    margin-left: 50px;
}
.uncertainMemo .memoItem [disabled] {
    background: #ddd;
}
#matBuyInfo tbody tr td.notBreak, table tbody tr td.notBreak{
    word-break:keep-all;
}
hr.hr{ border-bottom: 1px solid #bfbfbf; }
.fds td{ line-height:30px;  }
#invoiceHandleChoice .fa, .inv .fa{ color: #5d9cec; margin-right:10px;  }
#invoiceHandleChoice p, .inv p{ cursor: default;  }
.upperKey{ margin-left: 20px; display: inline-block; width: 310px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;  position: relative; top: 6px; }

.leftSpace{padding: 0 70px 0 50px;}
.leftSpaceM{padding:0 50px;}
.conTtl{font-size: 18px;}
.conTtl > span{margin-right: 20px; }
.yellowTb thead tr td {  border-color: rgba(255, 255, 255, 0);  background: #fff2cc;  }
.yellowTb thead tr td.hdNot {  background: none;  }
.otherControl hr{border-top: 1px solid #624a4a;}
.otherControl .ty-table td{border: none;padding: 0 6px;}
.tabs__header{background: #fff;border-bottom: 1px solid #e4e7ed;}
.tabs__nav {  white-space: nowrap;  position: relative;  transition: transform .3s;  float: left;  z-index: 2;  }
.tabs__item {  padding: 0 20px;  height: 40px;  box-sizing: border-box;  line-height: 40px;  display: inline-block;  list-style: none;  font-size: 14px;  font-weight: 500;
    color: #303133;  position: relative;  }
.tabs__header .tabs__item { margin-bottom: -1px; transition: all .3s cubic-bezier(.645,.045,.355,1);  border: 1px solid transparent; color: #909399;  }
.tabs__header .tabs__item.is-active {  border-left: 1px solid #dcdfe6;border-right: 1px solid #dcdfe6;border-top: 1px solid #dcdfe6;color: #409eff;  background-color: #fff;  box-shadow: 2px 0 2px 0 rgba(0,0,0,.12),-1px 0 0 0 rgba(0,0,0,.12);  }
.tabs__content {  padding: 20px 40px;  border-left: 1px solid #dcdfe6;  border-right: 1px solid #dcdfe6;  border-bottom: 1px solid #dcdfe6; }
.spInt{display: inline-block;background: #d1d1d1;width: 157px;text-align: right;line-height: 26px;}
#paContainer .ty-table td {  padding: 0 4px;  white-space: nowrap;  overflow: hidden;  text-overflow: ellipsis;  word-wrap: normal;  }
#paContainer .ty-table{table-layout: fixed;}
/*#paContainer #replenishList td {  padding: 0 2px 0 0}*/
.mainArea{width: 1678px;}
.replenish {width: 1678px;padding: 0 40px 0 20px;}
.replenishList {width: 1331px;}
.backGroup{margin-left: 20px;}
#phAllOrders td.ty-td-control span{padding: 5px 6px;}
.warningOne{padding-left: 10px;width: 1526px;}
.repStepTwo{position: relative;}
.freeBuy{position: absolute;top: -50px;  right: 10px;}
.repStepTwo .npSupper{width: 70%;}
.elemFlex {  display: flex;  justify-content: space-between;  }
.signDetail{margin-top: 12px;}
table .warn-red{color: red;}
.orderAddress select{background-color: #fff;border: 1px solid #757777;height: 26px;border-radius: 4px;}
.narrowBody{margin: auto;width: 88%;}
.tips{margin: 10px 0 20px 0;}
.conditional>li{list-style: none;background:#fff;line-height:30px;padding:0 15px;}
.conditional>li:nth-child(even){background:#f5f5f5;}
.conditional .fa{margin-right: 20px;}
.conditional i{width: 14px;display: inline-block;}
.taxRateBody{position: relative;display: inline-block;}
.taxRateList{position: absolute;top: 30px;display: none;width: 120px;background:#fff;max-height: 150px;left: 0;border: 1px solid #a8a6a6;}
.taxRateList > span{padding: 4px 10px;display: inline-block;width: 100%;}
.taxRateList > span:hover{background: #3398fe;color: #fff;}
.taxRateList > span >span:first-child{width: 60px;
    display: inline-block;}
.taxRateList > span >span:last-child{color: red;font-size: 12px;}
.taxRateBody input{width: 120px;}
.linkBtn {color: #036bc0;cursor: default;}



