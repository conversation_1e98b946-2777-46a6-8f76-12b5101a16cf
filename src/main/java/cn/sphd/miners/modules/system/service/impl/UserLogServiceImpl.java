package cn.sphd.miners.modules.system.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.*;
import cn.sphd.miners.modules.generalAffairs.controller.LoginRecordController;
import cn.sphd.miners.modules.system.dao.UserLogDao;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.entity.UserLog;
import cn.sphd.miners.modules.system.service.UserLogService;
import cn.sphd.miners.modules.system.service.UserService;
import eu.bitwalker.useragentutils.Browser;
import eu.bitwalker.useragentutils.OperatingSystem;
import eu.bitwalker.useragentutils.UserAgent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Created by Administrator on 2016/8/25 0025.
 */
@Service
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class UserLogServiceImpl implements UserLogService {
    @Autowired
    UserLogDao userLogDao;
    @Autowired
    UserService userService;
    @Autowired
    RedisTemplate redisTemplate;

    private void save(UserLog userLog) {
        userLogDao.save(userLog);
        if (GetLocalIPUtils.checkIp(userLog.getIp())) {
            String ip = userLogDao.getAddressByIp(userLog);
            if (ip != null && !ip.trim().isEmpty()) {
                System.out.println("UserLog save with cached ip:"+ip);
                userLog.setIp(ip);
            }
        }
    }

    @Override
    public void addMobileUserLog(String content, User user, String sessionid, HttpServletRequest request) {
        UserLog userLog = new UserLog();
        userLog.setLogcontent(content);
//        userLog.setUser(user);
        userLog.setUser_(user.getUserID());
        userLog.setOrg(user.getOid());
        String ip = GetLocalIPUtils.getLocalIp(request);
        userLog.setIp(ip);
        userLog.setRealIp(ip);
        //wyu: debug code
//        userLog.setMemo(System.getProperty("ServerIP") == null ? GetLocalIPUtils.getServerIp() : System.getProperty("ServerIP"));
        userLog.setMemo((System.getProperty("ServerIP") == null ? GetLocalIPUtils.getServerIp() : System.getProperty("ServerIP")) + " | " +GetLocalIPUtils.isAjaxOrApp(request));
        //endof debug code
        if (request != null) {
            String ua = request.getHeader("User-Agent");
            UserAgent userAgent = UserAgent.parseUserAgentString(ua);
            userLog.setUserAgent(ua);
            if(!Browser.UNKNOWN.equals(userAgent.getBrowser())) {
                userLog.setBroswer(userAgent.getBrowser().getName());
            } else if(!OperatingSystem.UNKNOWN.equals(userAgent.getOperatingSystem())) {
                userLog.setBroswer(userAgent.getOperatingSystem().toString());
            } else {
                userLog.setBroswer("移动端");
            }
        } else {
            userLog.setUserAgent("移动端");
        }
        userLog.setType("1");
        save(userLog);
        redisTemplate.opsForValue().set("miners:userLogId:" +sessionid,userLog.getId(),35, TimeUnit.MINUTES);//设置缓存35分钟失效
    }

    @Override
    public void addWebUserLog(String content, User user, String sessionid, HttpServletRequest request) {
        UserLog userLog = new UserLog();
        userLog.setLogcontent(content);
//        userLog.setUser(user);
        userLog.setUser_(user.getUserID());
        userLog.setOrg(user.getOid());
        String ip=GetLocalIPUtils.getLocalIp(request);
        userLog.setIp(ip);
        userLog.setRealIp(ip);
        //wyu: debug code
//        userLog.setMemo(System.getProperty("ServerIP")==null? GetLocalIPUtils.getServerIp():System.getProperty("ServerIP"));
        userLog.setMemo((System.getProperty("ServerIP") == null ? GetLocalIPUtils.getServerIp() : System.getProperty("ServerIP")) + " | " +GetLocalIPUtils.isAjaxOrApp(request));
        //endof debug code
        userLog.setType("3");
        if(request!=null) {
            String ua = request.getHeader("User-Agent");
            UserAgent userAgent = UserAgent.parseUserAgentString(ua);
            userLog.setBroswer(userAgent.getBrowser().toString());
            userLog.setUserAgent(ua);
            if(!Browser.UNKNOWN.equals(userAgent.getBrowser())) {
                userLog.setBroswer(userAgent.getBrowser().getName());
            } else if(!OperatingSystem.UNKNOWN.equals(userAgent.getOperatingSystem())) {
                userLog.setBroswer(userAgent.getOperatingSystem().toString());
            } else {
                userLog.setBroswer("未知系统");
            }
        }
        save(userLog);
        //wyu：上次登录没退出？begin
//        Integer userLogId = (Integer) session.getAttribute("userLogId");
//        if(userLogId!=null){
//            userLogService.setDuration(userLogId);
//        }//end
        redisTemplate.opsForValue().set("miners:userLogId:" + sessionid,userLog.getId(),35, TimeUnit.MINUTES);//设置缓存35分钟失效
    }

    @Override
    public void saveAddressByRemote(UserLog userLog) {
        String ip = userLogDao.getAddressByRemote(userLog);
        if (ip != null && !ip.trim().isEmpty()) {
            userLog.setIp(ip);
            userLogDao.update(userLog);
        }
    }

    @Override
    public void setDuration(Integer userLogId) {
        setDuration(userLogId, "D");
    }

    @Override
    public void setDuration(Integer userLogId, String operation) {
        if (userLogId != null) {
            String hql = "update UserLog set updateTime=:updateTime, operation=:operation,duration=timestampdiff(second,operatetime,:updateTime) where id=:id";
            HashMap<String, Object> params = new HashMap<>();
            params.put("id", userLogId);
            params.put("updateTime", new Date(System.currentTimeMillis()));
            params.put("operation", operation);
            userLogDao.queryHQLWithNamedParams(hql, params);
        }
    }

    public List getUserLogByMaxTime(Integer oid, Integer uid) {
        List list = userLogDao.getUserLogByMaxTime(oid, uid);
        return list;
    }

    @Override
    public List<UserLog> getUserLogByMouth(Integer uid, String beginDate, String endDate) {
        List<UserLog> userLogList = userLogDao.getUserLogByMouth(uid, beginDate, endDate);
        return userLogList;
    }

    @Override
    public List<UserLog> getUserLogByDate(Integer userId, String logsDate) {
        List<UserLog> list = userLogDao.getUserLogByDate(userId, logsDate);
        return list;
    }

    @Override
    public List<UserLog> getUserLogByDate1(Integer userId, String logsDate) {
        List<UserLog> list = userLogDao.getUserLogByDate1(userId, logsDate);
        return list;
    }

    @Override
    public UserLog getLastLog(String mobile) {
        String hql = "from UserLog where user.mobile=:mobile order by operatetime desc";
        Map<String, Object> map = new HashMap<>();
        map.put("mobile", mobile);
        UserLog userLog = (UserLog) userLogDao.getByHQLWithNamedParams(hql, map);
        return userLog;
    }

    @Override
    public List<UserLog> getUserLogByOneDay(Integer oid,Integer loginUserId,Integer userId,String date, Integer type) {
        StringBuffer hql = new StringBuffer("from UserLog");
        StringBuffer where = new StringBuffer();
        if (loginUserId!=null){
            Map<String, Object> map = userService.getUserIds(loginUserId, 1);
            String userIds = (String) map.get("userIdsString");
            where.append(" and user_ in ("+userIds+")");
        }
        if (userId!=null){
            where.append(" and user_="+userId+"");
        }
        if (oid!=null){
            where.append(" and org="+oid+"");
        }
        if (!MyStrings.nulltoempty(date).isEmpty()){
            where.append(" and operatetime like '%" + date + "%'");
        }
        if (type!=null){
            where.append(" and type ="+type+"");
        }
        if (where.length() > 0) {
            hql.append(" where ").append(where.substring(4));
        }
        hql.append(" order by operatetime desc");
        List<UserLog> userLogs = userLogDao.getListByHQL(hql.toString());
        return userLogs;
    }

    @Override
    public List<UserLog> getUserLogByOneMounth(Integer oid, String date) {
        String hql = "from UserLog o where o.org=" + oid + " and o.operatetime like '%" + date + "%' order by o.operatetime desc";
        return userLogDao.getListByHQL(hql);
    }

    @Override
    public List<UserLog> getUserLogByFreedom(Integer oid,Integer loginUserId,Integer userId, String beginTime, String endTime, Integer type) {

        StringBuffer hql = new StringBuffer("from UserLog");
        StringBuffer where = new StringBuffer();
        if (loginUserId!=null){
            Map<String, Object> map = userService.getUserIds(loginUserId, 1);
            String userIds = (String) map.get("userIdsString");
            where.append(" and user_ in ("+userIds+")");
        }
        if (userId!=null){
            where.append(" and user_="+userId+"");
        }
        if (oid!=null){
            where.append(" and org="+oid+"");
        }
        if (!MyStrings.nulltoempty(beginTime).isEmpty()&&!MyStrings.nulltoempty(endTime).isEmpty()){
            where.append(" and operatetime>='"+beginTime+" 00:00:00' and operatetime<='"+endTime+" 23:59:59'");
        }
        if (type!=null){
            where.append(" and type ="+type+"");
        }
        if (where.length() > 0) {
            hql.append(" where ").append(where.substring(4));
        }
        hql.append(" order by operatetime desc");
        List<UserLog> userLogs = userLogDao.getListByHQL(hql.toString());
        return userLogs;
    }

    @Override
    public List<UserLog> getDetailUserLogInfoByDay(Integer oid,Integer loginUserId, String date, Integer type, Integer userId) {
        StringBuffer hql = new StringBuffer("from UserLog");
        StringBuffer where = new StringBuffer();
        if (loginUserId!=null){
            Map<String, Object> map = userService.getUserIds(loginUserId, 1);
            String userIds = (String) map.get("userIdsString");
            where.append(" and user_ in ("+userIds+")");
        }
        if (userId!=null){
            where.append(" and user_="+userId+"");
        }
        if (oid!=null){
            where.append(" and org="+oid+"");
        }
        if (!MyStrings.nulltoempty(date).isEmpty()){
            where.append(" and operatetime like '%" + date + "%'");
        }
        if (type!=null){
            where.append(" and type ="+type+"");
        }
        if (where.length() > 0) {
            hql.append(" where ").append(where.substring(4));
        }
        hql.append(" order by operatetime desc");
        List<UserLog> userLogs = userLogDao.getListByHQL(hql.toString());
        return userLogs;
    }

    @Override
    public List<UserLog> getPersonalLoginRecordByYearDetailA(Integer oid,Integer loginUserId,Integer userId, String beginTime, String endTime, Integer type) {
        StringBuffer hql = new StringBuffer("from UserLog");
        StringBuffer where = new StringBuffer();
        if (loginUserId!=null){
            Map<String, Object> map = userService.getUserIds(loginUserId, 1);
            String userIds = (String) map.get("userIdsString");
            where.append(" and user_ in ("+userIds+")");
        }
        if (userId!=null){
            where.append(" and user_="+userId+"");
        }
        if (oid!=null){
            where.append(" and org="+oid+"");
        }
        if (!MyStrings.nulltoempty(beginTime).isEmpty()&&!MyStrings.nulltoempty(endTime).isEmpty()){
            where.append(" and operatetime>='"+beginTime+" 00:00:00' and operatetime<='"+endTime+" 23:59:59'");
        }
        if (type!=null){
            where.append(" and type ="+type+"");
        }
        if (where.length() > 0) {
            hql.append(" where ").append(where.substring(4));
        }
        hql.append(" order by operatetime desc");
        List<UserLog> userLogs = userLogDao.getListByHQL(hql.toString());
        return userLogs;
    }

    @Override
    public List<UserLog> getUsersByUserIdAndType(Integer userId, Integer type, String beginTime, String endTime) {
        String hql = " and o.user.userID= " + userId;
        if (type != null) {
            if (type == 3) {   //PC端
                hql += " and o.type = " + type;
            } else {
                hql += " and o.type = 1 or o.type = 2";   //1-安卓 2-iOS
            }
        }
        if (!"".equals(beginTime) && beginTime != null) {
            hql += " and o.operatetime>='" + beginTime + " 00:00:00'";
        }
        if (!"".equals(endTime) && endTime != null) {
            hql += " and o.operatetime<='" + endTime + " 23:59:59'";
        }
        List<UserLog> userLog1 = userLogDao.findCollectionByConditionNoPage(hql, null, null);
        return userLog1;
    }

    @Override
    public Map<String, Object> getDayUserLogs(Integer oid, Date beginTime, Date endTime, PageInfo pageInfo) {
        Map<String, Object> result = new HashMap<>();
//        String where = " where org=:oid and operatetime between :beginTime and :endTime and user.roleCode!='super'";
        String where = " where ul.org=:oid and ul.operatetime between :beginTime and :endTime";  //需求让添加上董事长，2022/01/10改
        String hql = "select new cn.sphd.miners.modules.system.entity.UserLog(ul.operatetime,ul.ip,ul.duration,user.userName) from UserLog ul left join User user on ul.user_=user.userID";
        Map<String, Object> params = new HashMap<>();
        params.put("oid", oid);
        params.put("beginTime", beginTime);
        params.put("endTime", endTime);
        List<UserLog> userLogList = userLogDao.getListByHQLWithNamedParams(hql + where+" order by ul.id desc", params, pageInfo);
        for (UserLog userLog : userLogList) {
            userLog.setDurationTime2sf(userLog.getDuration() * 1000);
        }
        result.put("userLogs", userLogList);
        hql = "select count(distinct ul.user_) as userNumber,count(ul.id) as number,sum(ul.duration) as duration from UserLog ul";
        Object[] o = (Object[]) userLogDao.getByHQLWithNamedParams(hql + where, params);
        result.put("userCount", Long.valueOf(o[0].toString()));
        result.put("loginCount", Long.valueOf(o[1].toString()));
        result.put("totalDuration", TimeUtils.toTimeString2sf(o[2] == null ? 0 : Long.valueOf(o[2].toString()) * 1000));
        System.out.println(result);
        return result;
    }

    @Override  //logType 端口 0-全部 1-电脑，2- 手机       loginUserId 登录人id(我的团队时使用)    conditionType 1-我的团队
    public Map<String, Object> getUserLogListByOid(Integer oid,Integer loginUserId,Date beginTime, Date endTime,Integer logType,Integer conditionType,PageInfo pageInfo) {
        String hql;
        Map<String, Object> params = new HashMap<>();
//        hql = " from UserLog where org=:oid and operatetime between :beginTime and :endTime and user.roleCode!='super'";
        hql = " from UserLog where org=:oid and operatetime between :beginTime and :endTime";   //需求让添加上董事长，2022/01/10改
        params.put("oid", oid);
        params.put("beginTime", beginTime);
        params.put("endTime", endTime);
        switch (logType){
            case 1:
                hql+=" and type='3'";
                break;
            case 2:
                hql+=" and type!='3'";
                break;
        }
        if (loginUserId!=null && conditionType!=null && 1==conditionType){
            Map<String, Object> map = userService.getUserIds(loginUserId, 1);
            String userIds = (String) map.get("userIdsString");
            hql+=" and user_ in ("+userIds+")";
        }

        Object[] o = (Object[]) userLogDao.getByHQLWithNamedParams("select count(distinct user_),count(id),sum(duration)"+hql, params);
//        params.clear();
        String hql1 = "select u.userName,u.departName,u.postName,count(l.id), sum(l.duration),l.operatetime,l.ip from UserLog l,User u " +
                "where l.org=:oid and l.operatetime between :beginTime and :endTime and l.user_=u.userID group by l.user_ order by convert(u.userName, 'gbk') asc";
//        params.put("userId", o[0]);
        List<Object[]> userLogList1 = userLogDao.getListByHQLWithNamedParams(hql1,params,pageInfo);
        List<UserLog> userLogList = new ArrayList<>();
        for (Object[] object:userLogList1) {
            UserLog userLog = new UserLog();
            userLog.setUserName((String) object[0]);
            userLog.setDepartmentName((String) object[1]);
            userLog.setPostName((String) object[2]);
            Long countLong = (Long) object[3];
            userLog.setCount(countLong.intValue());
            Long durationLong = (Long) object[4];
            userLog.setDuration(durationLong.intValue());
            userLog.setOperatetime((Date) object[5]);
            userLog.setIp((String) object[6]);
            userLogList.add(userLog);
        }
//        userLogList = userLogDao.getListByHQLWithNamedParams(
//                "select new cn.sphd.miners.modules.system.entity.UserLog(user.userName,user.departName,user.postName,count(id), sum(duration),operatetime,ip)"
//                        + hql +" group by user_ order by convert(user.userName, 'gbk') asc",
//                params, pageInfo,Long.valueOf(o[0].toString()).intValue());
//                "select count(distinct user_)" + hql);
        for (UserLog userLog : userLogList) {
            userLog.setDurationTime2sf(userLog.getDuration() * 1000);
        }
//        System.out.println(JSON.toJSONString(userLogList));

        Map<String, Object> result = new HashMap<>();
        result.put("userCount", Long.valueOf(o[0].toString()));
        result.put("loginCount", Long.valueOf(o[1].toString()));
        result.put("totalDuration", TimeUtils.toTimeString2sf(o[2] == null ? 0 : Long.valueOf(o[2].toString()) * 1000));
        result.put("userLogList",userLogList);
        return result;
    }

    @Override
    public Map<String, Object> getUserLogListByUser(Integer userId, Date beginTime, Date endTime,Integer logType, PageInfo pageInfo) {
        String hql;
        Map<String, Object> params = new HashMap<>();
        List<UserLog> userLogList=new ArrayList<>();
        hql = "select count(id),sum(duration) from UserLog where user_=:userId and operatetime between :beginTime and :endTime";
        String typeHql="";
        switch (logType){
            case 1:
                typeHql=" and type='3'";
                break;
            case 2:
                typeHql=" and type!='3'";
                break;
        }
        params.put("beginTime", beginTime);
        params.put("endTime", endTime);
        params.put("userId", userId);
        Object[] o = (Object[]) userLogDao.getByHQLWithNamedParams(hql+typeHql, params);
        Integer count=Integer.valueOf(o[0].toString());
        if (count>0) {
            hql = "from UserLog where operatetime between :beginTime and :endTime and user_=:userId";

            userLogList = userLogDao.getListByHQLWithNamedParams(hql+typeHql, params, pageInfo, count);

            for (UserLog userLog : userLogList) {
                userLog.setDurationTime2sf(userLog.getDuration() * 1000);
            }
        }
//        System.out.println(JSON.toJSONString(userLogList));
        Map<String, Object> result = new HashMap<>();
        result.put("userCount", count>0?1:0);
        result.put("loginCount", count);
        result.put("totalDuration", TimeUtils.toTimeString2sf(o[1] == null ? 0 : Long.valueOf(o[1].toString()) * 1000));
        result.put("userLogList",userLogList);
        return result;
    }

    @Override
    public Map<String,Object> getUserLogsByOid(Integer oid,Date beginTime,Date endTime){
        String hql="from UserLog where org=:oid and operatetime between :beginTime and :endTime";
        Map<String, Object> result = new HashMap<>();
        result.put("oid", oid);
        result.put("beginTime", beginTime);
        result.put("endTime",endTime);
        List<UserLog> userLogList=userLogDao.getListByHQLWithNamedParams(hql,result);

        List<Integer> userIds=new ArrayList<>();
        Integer sumDuration=0; //登录总时长
        Integer sumPc=0; //电脑登录次数
        Integer sumApp=0; //手机登录次数
        for (UserLog userLog:userLogList){
            if (!userIds.contains(userLog.getUser_())){
                userIds.add(userLog.getUser_());
            }
            sumDuration+=userLog.getDuration();
            if (userLog.getType().equals("3")){
                sumPc++;
            }else {
                sumApp++;
            }
        }
        Map<String,Object> map=new HashMap<>();
        map.put("loginDate",beginTime);// 日期
        map.put("sumUser",userIds.size()); // 登录人数
        map.put("sumDuration",TimeUtils.toTimeString(sumDuration*1000));//登录时长
        map.put("sumLogin",sumPc+sumApp);// 登录总次数
        map.put("sumPc",sumPc);// pc登录次数
        map.put("sumApp",sumApp); //手机登录次数
        return map;
    }

    @Override
    public List<Map<String, Object>> getUserLogListByOid(Integer oid, Date beginTime, Date endTime) {
        String hql="SELECT SUM(duration) as duration,COUNT(DISTINCT user_) as sum, SUM(CASE WHEN type='3' THEN 1 ELSE 0 END) AS com, SUM(CASE WHEN type!='3' THEN 1 ELSE 0 END) AS app,operatetime from UserLog WHERE org=:oid and operatetime between :beginTime and :endTime  GROUP BY DATE(operatetime)";

        Map<String, Object> result = new HashMap<>();
        result.put("oid", oid);
        result.put("beginTime", beginTime);
        result.put("endTime",endTime);
        List<Object[]>  userLogList=userLogDao.getListByHQLWithNamedParams(hql,result);

        List<Map<String,Object>> mapList=new ArrayList<>();
        Date end=new Date();
        Date over=endTime;
        Long terminate=beginTime.getTime();
        for (; end.getTime() >= terminate; over = NewDateUtils.changeDay(over, -1), end = NewDateUtils.getLastTimeOfDay(over)) {
            if (over.getTime() < terminate) {
                over = beginTime;
            }

            Integer sumUser=0; //登录人数
            Integer sumDuration=0; //登录总时长
            Integer sumPc=0; //电脑登录次数
            Integer sumApp=0; //手机登录次数

            for (Object[] ob : userLogList) {
                Date date= (Date) ob[4];
                if (NewDateUtils.getDate(over)==NewDateUtils.getDate(date)) {
                    sumUser = Integer.valueOf(ob[1].toString()) ;
                    sumDuration =Integer.valueOf(ob[0].toString());
                    sumPc = Integer.valueOf(ob[2].toString());
                    sumApp = Integer.valueOf(ob[3].toString());
                }
            }

            Map<String,Object> map=new HashMap<>();
            map.put("loginDate",over);// 日期
            map.put("sumUser",sumUser); // 登录人数
            map.put("sumDuration",TimeUtils.toTimeString(sumDuration*1000));//登录时长
            map.put("sumLogin",sumPc+sumApp);// 登录总次数
            map.put("sumPc",sumPc);// pc登录次数
            map.put("sumApp",sumApp); //手机登录次数
            mapList.add(map);
        }
        return mapList;
    }

    @Override      //loginUserId（我的团队中使用的，查本人以及本人的直接和间接下属）  userId（就是查找本人）
    public void loginRecords( Integer oid,Integer loginUserId,Integer userId,String beginTime, String endTime, Integer sort, Integer currPage, Integer flag, Integer pageSize,HttpServletResponse response) throws ParseException, IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        int app = 0;
        int computer = 0;
        int total = 0;
        long durationTotal = 0;
        long duration = 0;
        List<UserLog> resultUserLists=new ArrayList<>();
        if (flag == 0) { //本日
            if (sort == 0) {   //按操作时间
//                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                List<UserLog> userLogList = getUserLogByOneDay(oid,loginUserId,userId,NewDateUtils.dateToString(new Date(),"yyyy-MM-dd"),null);
                List<Integer> userIds=new ArrayList<>();  //取.size获取查询出来的登录人数
                if (userLogList.size() > 0) {
                    for (UserLog ul : userLogList) {
                        User u = userService.getUserByID(ul.getUser_());
                        String userName = MyStrings.nulltoempty(u.getUserName(), u.getMobile());
                        String postName = MyStrings.nulltoempty(u.getPostName());
                        String departName = MyStrings.nulltoempty((u.getDepartName()));
                        if (ul.getType().equals("3")) {
                            computer++;
                        }
                        if (ul.getType().equals("1")) {
                            app++;
                        }
                        duration = ul.getDuration() * 1000;
                        durationTotal += duration;
                        ul.setDurationTime(duration);
                        ul.setUserName(userName);
                        ul.setDepartmentName(departName);//部门名称
                        ul.setPostName(postName);//职位名称

                        resultUserLists.add(ul);
                        if (!userIds.contains(ul.getUser_())) {
                            userIds.add(ul.getUser_());
                        }

//                        if (!ul.getUser().getRoleCode().equals("super")){  //去掉董事长   需要同意展示董事长-2022/01/10改
//                            resultUserLists.add(ul);
//                            if (!userIds.contains(ul.getUser_())) {
//                                userIds.add(ul.getUser_());
//                            }
//                        }
                    }
                }
                map.put("date", new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
                map.put("total", userLogList.size());//登录总次数
                map.put("app", app);//手机端
                map.put("computer", computer);//电脑端
                map.put("duration", TimeUtils.toTimeString(durationTotal));//总时长
                map.put("userNum", userIds.size());//登录的人数
                this.findPage(currPage, pageSize, resultUserLists, map, response);
            } else if (sort == 1) {  //按姓名
                List<UserLog> userLogList = new ArrayList<UserLog>();
                String date = NewDateUtils.dateToString(new Date(),"yyyy-MM-dd");
                List<UserLog> userLogList0 = getUserLogByOneDay(oid,loginUserId,null,date,null);
                List<UserLog> userLogL = getUserLogByOneDay(oid,loginUserId,null,date,3);
                List<UserLog> userLogL1 = getUserLogByOneDay(oid,loginUserId,null,date,1);

                Set<Integer> set = new HashSet<Integer>();
                if (userLogList0.size() > 0) {
                    for (UserLog ul : userLogList0) {
                        set.add(ul.getUser_());
                    }
                    for (Integer a : set) {
                        User u = userService.getUserByID(a);
                        String userName = MyStrings.nulltoempty(u.getUserName(), u.getMobile());
                        String postName = MyStrings.nulltoempty(u.getPostName());
                        String departName = MyStrings.nulltoempty((u.getDepartName()));
                        List<UserLog> list1 = getUserLogByOneDay(oid,loginUserId,a, date,null);
                        list1.get(0).setCount(list1.size());

                        List<UserLog> list2 = getUserLogByOneDay(oid,loginUserId,a,date,3);
                        list1.get(0).setCom(list2.size());
                        List<UserLog> list0 = getUserLogByOneDay(oid,loginUserId,a,date,1);
                        list1.get(0).setApp(list0.size());
                        list1.get(0).setDepartmentName(departName);//部门名称
                        list1.get(0).setPostName(postName);//职位名称
                        list1.get(0).setUserName(userName);
                        duration = 0;
                        for (UserLog l : list1) {
                            duration += l.getDuration() * 1000;
                        }
                        durationTotal += duration;
                        list1.get(0).setDurationTime(duration);
                        userLogList.add(list1.get(0));  //未去董事长的
//                        if (!list1.get(0).getUser().getRoleCode().equals("super")) {  //去掉董事长   需要同意展示董事长-2022/01/10改
//                            userLogList.add(list1.get(0));
//                        }
                    }
                    total = userLogList0.size();
                    computer = userLogL.size();
                    app = userLogL1.size();
                }
                map.put("date", new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
                map.put("total", total);//登录总次数
                map.put("app", app);//手机端
                map.put("computer", computer);//电脑端
                map.put("duration", TimeUtils.toTimeString(durationTotal));//总时长

                Collections.sort(userLogList, new LoginRecordController.ComparatorUser1());
                this.findPage(currPage, pageSize, userLogList, map, response);
            }
        } else if (flag == 1) {  //本月
            if (sort == 0) {  //按时间
                List<UserLog> userLogList = new ArrayList<UserLog>();
                //获取当前月第一天：
                String beginDate = NewDateUtils.dateToString(NewDateUtils.changeMonth(new Date(),0),"yyyy-MM-dd");
                String endDate = NewDateUtils.dateToString(new Date(),"yyyy-MM-dd");
                Date date = NewDateUtils.dateFromString(beginDate,"yyyy-MM-dd");
                Date date1 = NewDateUtils.today();

                while (date.getTime() <= date1.getTime()) {
                    List<UserLog> userLogList1 = getUserLogByOneDay(oid,loginUserId,userId,endDate,null);
                    List<UserLog> userLogList2 = getUserLogByOneDay(oid,loginUserId,userId,endDate,3);
                    List<UserLog> userLogList3 = getUserLogByOneDay(oid,loginUserId,userId,endDate,1);
                    date1 = NewDateUtils.changeDay(date1,-1);//减一天的时间
                    Set<Integer> set = new HashSet<Integer>();

                    if (userLogList1.size() > 0) {
                        userLogList1.get(0).setLoginYear(NewDateUtils.dateToString(userLogList1.get(0).getOperatetime(),"yyyy-MM-dd")); //登录年份
                        userLogList1.get(0).setCount(userLogList1.size());//当日登陆总数
                        userLogList1.get(0).setCom(userLogList2.size());  //电脑端登录次数
                        userLogList1.get(0).setApp(userLogList3.size());  //手机端登录次数
                        duration = 0;
                        for (UserLog ul : userLogList1) {
                            set.add(ul.getUser_());
                            duration += ul.getDuration() * 1000;
                        }
                        durationTotal += duration;
                        userLogList1.get(0).setDurationTime(duration);  //登录时长
                        if (set.size() > 0) {
                            userLogList1.get(0).setSum(set.size()); //登陆总人数
                        }
                        userLogList.add(userLogList1.get(0));
                    } else {
                        UserLog ul = new UserLog();
                        ul.setDurationTime("0");
                        ul.setLoginYear(endDate);
                        userLogList.add(ul);
                    }
                    total = total + userLogList1.size();
                    app = app + userLogList3.size();
                    computer = computer + userLogList2.size();
                    endDate = NewDateUtils.dateToString(date1,"yyyy-MM-dd");
                }
                map.put("firstdate", NewDateUtils.dateToString(NewDateUtils.changeMonth(new Date(),0),"yyyy-MM-dd"));
                map.put("date", NewDateUtils.dateToString(new Date(),"yyyy-MM-dd"));
                map.put("total", total);//登录总次数
                map.put("app", app);//手机端
                map.put("computer", computer);//电脑端
                map.put("duration", TimeUtils.toTimeString(durationTotal));//总时长
                this.findPage(currPage, pageSize, userLogList, map, response);
            }
            if (sort == 1) {   //按姓名
                List<UserLog> userLogList = new ArrayList<UserLog>();
                String nowDate = NewDateUtils.dateToString(new Date(),"yyyy-MM");
                List<UserLog> userLogList1 = getUserLogByOneDay(oid,loginUserId,null,nowDate,null);
                List<UserLog> userL = getUserLogByOneDay(oid,loginUserId,null,nowDate,3);
                List<UserLog> userL1 = getUserLogByOneDay(oid,loginUserId,null,nowDate,1);

                Set<Integer> set = new HashSet<Integer>();
                if (userLogList1.size() > 0) {
                    for (UserLog ul : userLogList1) {
                        set.add(ul.getUser_());
                    }
                    for (Integer a : set) {
                        User u = userService.getUserByID(a);
                        String userName = MyStrings.nulltoempty(u.getUserName(), u.getMobile());
                        String postName = MyStrings.nulltoempty(u.getPostName());
                        String departName = MyStrings.nulltoempty((u.getDepartName()));
                        List<UserLog> list1 = getUserLogByOneDay(oid,loginUserId,a,nowDate,null);
                        list1.get(0).setCount(list1.size());
                        List<UserLog> list2 = getUserLogByOneDay(oid,loginUserId,a,nowDate,3);
                        list1.get(0).setCom(list2.size());
                        List<UserLog> list0 = getUserLogByOneDay(oid,loginUserId,a,nowDate,1);
                        list1.get(0).setApp(list0.size());
                        list1.get(0).setUserName(userName);
                        list1.get(0).setDepartmentName(departName);//部门名称
                        list1.get(0).setPostName(postName);//职位名称
                        duration = 0;
                        for (UserLog l : list1) {
                            duration += l.getDuration() * 1000;
                        }
                        durationTotal += duration;
                        list1.get(0).setDurationTime(duration);
                        userLogList.add(list1.get(0));
//                        if (!list1.get(0).getUser().getRoleCode().equals("super")) {   //去掉董事长   需要同意展示董事长-2022/01/10改
//                            userLogList.add(list1.get(0));
//                        }
                    }
                    total = userLogList1.size();
                    computer = userL.size();
                    app = userL1.size();
                }

                map.put("firstdate", NewDateUtils.dateToString(NewDateUtils.changeMonth(new Date(),0),"yyyy-MM-dd"));
                map.put("date", NewDateUtils.dateToString(new Date(),"yyyy-MM-dd"));
                map.put("total", total);//登录总次数
                map.put("app", app);//手机端
                map.put("computer", computer);//电脑端
                map.put("duration", TimeUtils.toTimeString(durationTotal));//总时长

                Collections.sort(userLogList, new LoginRecordController.ComparatorUser1());
                this.findPage(currPage, pageSize, userLogList, map, response);
            }
        } else if (flag == 2) {  //本年
            if (sort == 0) {//按时间
                List<UserLog> userLogList = new ArrayList<UserLog>();

//                String beginYear = NewDateUtils.dateToString(new Date(),"yyyy-01");
                String endYearDate = NewDateUtils.dateToString(new Date(),"yyyy-MM");
                Date date = NewDateUtils.getNewYearsDay(); //新年元旦
                Date date1 = NewDateUtils.changeMonth(new Date(),0);  //当前月
                while ( date.getTime()<= date1.getTime()) {
                    List<UserLog> userLogList1 = getUserLogByOneDay(oid,loginUserId,userId,endYearDate,null);
                    List<UserLog> userLogList2 = getUserLogByOneDay(oid,loginUserId,userId,endYearDate,3);
                    List<UserLog> userLogList3 = getUserLogByOneDay(oid,loginUserId,userId,endYearDate,1);

                    Set<Integer> set = new HashSet<Integer>();
                    if (userLogList1.size() > 0) {
                        duration = 0;
                        for (UserLog ul : userLogList1) {
                            set.add(ul.getUser_());
                            duration += ul.getDuration() * 1000;
                        }
                        durationTotal += duration;
                        userLogList1.get(0).setDurationTime(duration);
                        if (set.size() > 0) {
                            userLogList1.get(0).setSum(set.size());
                        }
                    }

                    date1 =  NewDateUtils.changeMonth(date1,-1);;//减一月的时间
                    if (userLogList1.size() > 0) {
                        userLogList1.get(0).setLoginYear(NewDateUtils.dateToString(userLogList1.get(0).getOperatetime(),"yyyy-MM"));
                        userLogList1.get(0).setCount(userLogList1.size());//当日登陆总数
                        userLogList1.get(0).setCom(userLogList2.size());
                        userLogList1.get(0).setApp(userLogList3.size());
                        userLogList.add(userLogList1.get(0));
                    } else {
                        UserLog ul = new UserLog();
                        ul.setDurationTime("0");
                        ul.setLoginYear(endYearDate);
                        userLogList.add(ul);
                    }
                    total = total + userLogList1.size();
                    app = app + userLogList3.size();
                    computer = computer + userLogList2.size();
                    endYearDate = NewDateUtils.dateToString(date1,"yyyy-MM");
                }

                map.put("firstdate", NewDateUtils.dateToString(NewDateUtils.getNewYearsDay(new Date()),"yyyy-MM-dd"));
                map.put("date", NewDateUtils.dateToString(new Date(),"yyyy-MM-dd"));
                map.put("total", total);//登录总次数
                map.put("app", app);//手机端
                map.put("computer", computer);//电脑端
                map.put("duration", TimeUtils.toTimeString(durationTotal));//总时长
                this.findPage(currPage, pageSize, userLogList, map, response);
            } else if (sort == 1) {//按姓名
                List<UserLog> userLogList = new ArrayList<UserLog>();

                String nowYear = NewDateUtils.dateToString(new Date(),"yyyy");
                List<UserLog> userLogList1 = getUserLogByOneDay(oid,loginUserId,null,nowYear,null);
                List<UserLog> userL = getUserLogByOneDay(oid,loginUserId,null,nowYear,3);
                List<UserLog> userL1 = getUserLogByOneDay(oid,loginUserId,null,nowYear,1);

                Set<Integer> set = new HashSet<Integer>();
                if (userLogList1.size() > 0) {
                    for (UserLog ul : userLogList1) {
                        set.add(ul.getUser_());
                    }
                    for (Integer a : set) {
                        User u = userService.getUserByID(a);
                        String userName = MyStrings.nulltoempty(u.getUserName(), u.getMobile());
                        String postName = MyStrings.nulltoempty(u.getPostName());
                        String departName = MyStrings.nulltoempty((u.getDepartName()));
                        List<UserLog> list1 = getUserLogByOneDay(oid,loginUserId,a,nowYear,null);
                        list1.get(0).setCount(list1.size());

                        List<UserLog> list2 = getUserLogByOneDay(oid,loginUserId,a,nowYear,3);
                        list1.get(0).setCom(list2.size());
                        List<UserLog> list0 = getUserLogByOneDay(oid,loginUserId,a,nowYear,1);
                        list1.get(0).setApp(list0.size());
                        list1.get(0).setUserName(userName);
                        list1.get(0).setDepartmentName(departName);//部门名称
                        list1.get(0).setPostName(postName);//职位名称
                        duration = 0;
                        for (UserLog l : list1) {
                            duration += l.getDuration() * 1000;
                        }
                        durationTotal += duration;
                        list1.get(0).setDurationTime(duration);
                        userLogList.add(list1.get(0));
//                        if (!list1.get(0).getUser().getRoleCode().equals("super")) {  //去掉董事长   需要同意展示董事长-2022/01/10改
//                            userLogList.add(list1.get(0));
//                        }
                    }
                    total = userLogList1.size();
                    app = userL1.size();
                    computer = userL.size();
                }

                map.put("firstdate", NewDateUtils.dateToString(NewDateUtils.getNewYearsDay(new Date()),"yyyy-MM-dd"));
                map.put("date", NewDateUtils.dateToString(new Date(),"yyyy-MM-dd"));
                map.put("total", total);//登录总次数
                map.put("app", app);//手机端
                map.put("computer", computer);//电脑端
                map.put("duration", TimeUtils.toTimeString(durationTotal));//总时长

                Collections.sort(userLogList, new LoginRecordController.ComparatorUser1());
                map.put("userLogList", userLogList);
                this.findPage(currPage, pageSize, userLogList, map, response);
            }
        } else if (flag == 3) {   //自定义查询
            if (null == beginTime || null == endTime) {//参数不正确
                map.put("status", 0);
                ObjectToJson.objectToJson1(map, new String[]{"user"}, response);
                return;
            }

            int byear = NewDateUtils.getYear(NewDateUtils.dateFromString(beginTime,"yyyy-MM-dd"));
            int eyear = NewDateUtils.getYear(NewDateUtils.dateFromString(endTime,"yyyy-MM-dd"));
            if (sort == 0) {//按年份
                List<UserLog> userLogList = new ArrayList<UserLog>();
                if (byear == eyear) {//不跨年
                    int yue = NewDateUtils.getMonth(NewDateUtils.dateFromString(beginTime,"yyyy-MM-dd"));
                    int yue1 = NewDateUtils.getMonth(NewDateUtils.dateFromString(endTime,"yyyy-MM-dd"));
                    if (yue == yue1) {//跨日
                        Date dt1 = NewDateUtils.dateFromString(beginTime,"yyyy-MM-dd");
                        Date dt2 = NewDateUtils.dateFromString(endTime,"yyyy-MM-dd");
                        String temp = "";
                        if (dt1.getTime() > dt2.getTime()) {
                            temp = beginTime;
                            beginTime = endTime;
                            endTime = temp;
                        }

                        String b = NewDateUtils.dateToString(dt1,"yyyy-MM-dd");
                        String e = NewDateUtils.dateToString(dt2,"yyyy-MM-dd");

                        List<UserLog> userLogList0 = new ArrayList<UserLog>();
                        List<UserLog> userLogList1 = new ArrayList<UserLog>();
                        List<UserLog> userLogList2 = new ArrayList<UserLog>();
                        int d1 = NewDateUtils.getDate(dt1);
                        int d2 = NewDateUtils.getDate(dt2);

                        while (d1 <= d2) {
                            userLogList0 = getUserLogByOneDay(oid,loginUserId,userId,e,null);
                            userLogList1 = getUserLogByOneDay(oid,loginUserId,userId,e,3);
                            userLogList2 = getUserLogByOneDay(oid,loginUserId,userId,e,1);

                            Set<Integer> set = new HashSet<Integer>();
                            if (userLogList0.size() > 0) {
                                duration = 0;
                                for (UserLog ul : userLogList0) {
                                    set.add(ul.getUser_());
                                    duration += ul.getDuration() * 1000;
                                }
                                durationTotal += duration;
                                userLogList0.get(0).setDurationTime(duration);
                                if (set.size() > 0) {
                                    userLogList0.get(0).setSum(set.size());//登陆总人数
                                }
                                userLogList0.get(0).setCom(userLogList1.size());
                                userLogList0.get(0).setApp(userLogList2.size());
                                userLogList0.get(0).setCount(userLogList0.size());//当日登陆总数
                                userLogList0.get(0).setLoginYear(e);
                                userLogList.add(userLogList0.get(0));
//                                if (!userLogList0.get(0).getUser().getRoleCode().equals("super")) {    //去掉董事长   需要同意展示董事长-2022/01/10改
//                                    userLogList.add(userLogList0.get(0));
//                                }
                            } else {
                                UserLog ul = new UserLog();
                                ul.setDurationTime("0");
                                ul.setLoginYear(e);
                                userLogList.add(ul);
                            }
                            total = total + userLogList0.size();
                            computer = computer + userLogList1.size();
                            app = app + userLogList2.size();

                            d2--;//月加一
                            String day = byear + "-";
                            if (yue < 10) {
                                day += "0";
                            }

                            day += (yue + "-");
                            if (d2 < 10) {
                                day += "0";
                            }
                            day += d2;
                            e = day;
                        }

                        map.put("beginTime", beginTime);
                        map.put("endTime", endTime);
                        map.put("total", total);//登录总次数
                        map.put("app", app);//手机端
                        map.put("computer", computer);//电脑端
                        map.put("duration", TimeUtils.toTimeString(durationTotal));//总时长
                        this.findPage1(currPage, pageSize, userLogList, map, response);
                    } else {//跨月

                        Date dt1 = NewDateUtils.dateFromString(beginTime,"yyyy-MM-dd");
                        Date dt2 = NewDateUtils.dateFromString(endTime,"yyyy-MM-dd");
                        String temp = "";
                        if (dt1.getTime() > dt2.getTime()) {
                            temp = beginTime;
                            beginTime = endTime;
                            endTime = temp;
                        }
                        String b = beginTime;
                        String e = endTime;

                        List<UserLog> userLogList0 = new ArrayList<UserLog>();
                        List<UserLog> userLogList1 = new ArrayList<UserLog>();
                        List<UserLog> userLogList2 = new ArrayList<UserLog>();
                        int m = NewDateUtils.getMonth(dt1);
                        int m1 = NewDateUtils.getMonth(dt2);

                        int a = 0;
                        while (m <= m1) {
                            int y1 = NewDateUtils.getYear(dt1);
                            if (a == 0) {
                                String mount = getFirstDayOfMonth(y1, m1);
                                b = mount;
                            }
                            if (a > 0) {
                                String enmount = getLastDayOfMonth(y1, m1);
                                e = enmount;
                            }
                            userLogList0 = getUserLogByFreedom(oid,loginUserId,userId, b, e,null);
                            userLogList1 = getUserLogByFreedom(oid,loginUserId,userId, b, e,3);
                            userLogList2 = getUserLogByFreedom(oid,loginUserId,userId, b, e,1);

                            Set<Integer> set = new HashSet<Integer>();
                            if (userLogList0.size() > 0) {
                                duration = 0;
                                for (UserLog ul : userLogList0) {
                                    set.add(ul.getUser_());
                                    duration += ul.getDuration() * 1000;
                                }
                                durationTotal += duration;
                                userLogList0.get(0).setDurationTime(duration);
                                if (set.size() > 0) {
                                    userLogList0.get(0).setSum(set.size());//登陆总人数
                                }
                                userLogList0.get(0).setCom(userLogList1.size());
                                userLogList0.get(0).setApp(userLogList2.size());
                                userLogList0.get(0).setCount(userLogList0.size());//当日登陆总数
                                userLogList0.get(0).setLoginYear(e.substring(0, 7));
                                userLogList.add(userLogList0.get(0));
//                                if (!userLogList0.get(0).getUser().getRoleCode().equals("super")) {  //去掉董事长   需要同意展示董事长-2022/01/10改
//                                    userLogList.add(userLogList0.get(0));
//                                }
                            } else {
                                UserLog ul = new UserLog();
                                ul.setDurationTime("0");
                                ul.setLoginYear(e.substring(0, 7));
                                userLogList.add(ul);
                            }
                            total = total + userLogList0.size();
                            computer = computer + userLogList1.size();
                            app = app + userLogList2.size();

                            m1--;//月加一
                            e = b;
                            if (m == m1) {
                                b = beginTime;
                            } else {
                                String mount = getFirstDayOfMonth(y1, m1);
                                b = mount;
                            }
                            a++;
                        }
                        map.put("beginTime", beginTime);
                        map.put("endTime", endTime);
                        map.put("total", total);//登录总次数
                        map.put("app", app);//手机端
                        map.put("computer", computer);//电脑端
                        map.put("duration", TimeUtils.toTimeString(durationTotal));//总时长
                        this.findPage1(currPage, pageSize, userLogList, map, response);
                    }
                } else if (byear != eyear) {//跨年
                    int temp;
                    String tempStr;
                    String b;
                    String e;
                    if (byear > eyear) {
                        temp = eyear;
                        eyear = byear;
                        byear = temp;
                        tempStr = endTime;
                        endTime = beginTime;
                        beginTime = tempStr;
                    }
                    b = beginTime;
                    e = endTime;
                    String kaishi = beginTime;
                    String jieshu = endTime;
                    int k = 0;
                    while (byear <= eyear) {

                        if (k == 0) {
                            Date dfirst = getYearFirst(eyear);
                            beginTime = NewDateUtils.dateToString(dfirst,"yyyy-MM-dd");
                        }
                        List<UserLog> userLogList0 = getUserLogByFreedom(oid,loginUserId,userId, beginTime, jieshu,null);
                        List<UserLog> userLogList2 = getUserLogByFreedom(oid,loginUserId,userId, beginTime, jieshu,3);
                        List<UserLog> userLogList3 = getUserLogByFreedom(oid,loginUserId,userId, beginTime, jieshu,1);

                        Set<Integer> set = new HashSet<Integer>();
                        if (userLogList0.size() > 0) {
                            duration = 0;
                            for (UserLog ul : userLogList0) {
                                set.add(ul.getUser_());
                                duration += ul.getDuration() * 1000;
                            }
                            durationTotal += duration;
                            userLogList0.get(0).setDurationTime(duration);
                        }
                        if (userLogList0.size() > 0) {
                            userLogList0.get(0).setSum(set.size());
                            userLogList0.get(0).setCom(userLogList2.size());
                            userLogList0.get(0).setApp(userLogList3.size());
                            userLogList0.get(0).setCount(userLogList0.size());//当日登陆总数
                            userLogList0.get(0).setLoginYear(beginTime.substring(0, 4));
                            userLogList.add(userLogList0.get(0));
//                            if (!userLogList0.get(0).getUser().getRoleCode().equals("super")) {  //去掉董事长   需要同意展示董事长-2022/01/10改
//                                userLogList.add(userLogList0.get(0));
//                            }
                        } else {
                            UserLog ul = new UserLog();
                            ul.setDurationTime("0");
                            ul.setLoginYear(beginTime.substring(0, 4));
                            userLogList.add(ul);
                        }
                        total = total + userLogList0.size();
                        computer = computer + userLogList2.size();
                        app = app + userLogList3.size();

                        eyear--;
                        Date dlast = getYearLast(eyear);
                        jieshu = NewDateUtils.dateToString(dlast,"yyyy-MM-dd");
                        if (byear == eyear) {
                            beginTime = kaishi;
                        } else {
                            Date df = getYearFirst(eyear);
                            beginTime = NewDateUtils.dateToString(df,"yyyy-MM-dd");
                        }
                        k++;
                    }
                    map.put("beginTime", b);
                    map.put("endTime", e);
                    map.put("total", total);//登录总次数
                    map.put("app", app);//手机端
                    map.put("computer", computer);//电脑端
                    map.put("userLogList", userLogList);
                    map.put("duration", TimeUtils.toTimeString(durationTotal));//总时长
                    this.findPage1(currPage, pageSize, userLogList, map, response);
                }
            } else if (sort == 1) {//按姓名
                List<UserLog> userLogList = new ArrayList<UserLog>();
                List<UserLog> userLogList1 = getUserLogByFreedom(oid,loginUserId,null, beginTime, endTime,null);
                List<UserLog> userL = getUserLogByFreedom(oid,loginUserId,null, beginTime, endTime,3);
                List<UserLog> userL1 = getUserLogByFreedom(oid,loginUserId,null, beginTime, endTime,1);

                Set<Integer> set = new HashSet<Integer>();
                if (userLogList1.size() > 0) {
                    for (UserLog ul : userLogList1) {
                        set.add(ul.getUser_());
                    }
                    for (Integer a : set) {
                        User u = userService.getUserByID(a);
                        String userName = MyStrings.nulltoempty(u.getUserName(), u.getMobile());
                        String postName = MyStrings.nulltoempty(u.getPostName());
                        String departName = MyStrings.nulltoempty((u.getDepartName()));
                        List<UserLog> list1 = getUserLogByFreedom(oid,loginUserId,a, beginTime, endTime,null);
                        list1.get(0).setCount(list1.size());
                        List<UserLog> list2 = getUserLogByFreedom(oid,loginUserId,a, beginTime, endTime,3);
                        list1.get(0).setCom(list2.size());
                        List<UserLog> list0 = getUserLogByFreedom(oid,loginUserId,a, beginTime, endTime,1);
                        list1.get(0).setApp(list0.size());
                        list1.get(0).setUserName(userName);
                        list1.get(0).setDepartmentName(departName);//部门名称
                        list1.get(0).setPostName(postName);//职位名称
                        duration = 0;
                        for (UserLog l : list1) {
                            duration += l.getDuration() * 1000;
                        }
                        durationTotal += duration;
                        list1.get(0).setDurationTime(duration);

                        userLogList.add(list1.get(0));
//                        if (!list1.get(0).getUser().getRoleCode().equals("super")){  //去掉董事长   需要同意展示董事长-2022/01/10改
//                            userLogList.add(list1.get(0));
//                        }
                    }
                    total = userLogList1.size();
                    computer = userL.size();
                    app = userL1.size();
                }
                map.put("beginTime", beginTime);
                map.put("endTime", endTime);
                map.put("total", total);//登录总次数
                map.put("app", app);//手机端
                map.put("computer", computer);//电脑端
                map.put("duration", TimeUtils.toTimeString(durationTotal));//总时长

                Collections.sort(userLogList, new LoginRecordController.ComparatorUser1());
                this.findPage(currPage, pageSize, userLogList, map, response);
            }
        }
    }

    private void findPage(Integer currPage, Integer pageSize, List<UserLog> userLogList, Map<String,Object> map, HttpServletResponse response) throws IOException {

        if (currPage == null || "".equals(currPage) || currPage <= 0) {
            currPage = 1;//默认当前页
        }
        int totalPage = 1;  //默认总页数

        if (pageSize == null || "".equals(pageSize) || pageSize <= 0) {
            map.put("currPage", currPage);//当前页
            map.put("totalPage", totalPage);//总页数
            map.put("userLogList", userLogList);//
            ObjectToJson.objectToJson1(map, new String[]{"user", "logcontent"}, response);
            return;
        } else {
            int maxResult = currPage * pageSize;//显示最大结果
            int minResult = (currPage - 1) * pageSize;//初始化结果
            int total0 = userLogList.size();
            /*
             * 计算总页数
             * */
            double total1 = total0;
            double pageSize1 = pageSize;
            double num = total1 / pageSize1;
            double totalPage1 = Math.ceil(num);
            totalPage = (int) totalPage1;
            if (total1 > 0) {
                List<UserLog> list = new ArrayList<UserLog>();
                for (int i = minResult; i < maxResult; i++) {
                    if (i < total1) {
                        list.add(userLogList.get(i));
                    }
                }
                userLogList.clear();
                userLogList.addAll(list);
                map.put("currPage", currPage);//当前页
                map.put("totalPage", totalPage);//总页数
                map.put("userLogList", list);//
                ObjectToJson.objectToJson1(map, new String[]{"user", "logcontent"}, response);
                return;
            } else {
                totalPage = 1;
                map.put("currPage", currPage);//当前页
                map.put("totalPage", totalPage);//总页数
                map.put("userLogList", userLogList);//
                ObjectToJson.objectToJson1(map, new String[]{"user", "logcontent"}, response);
                return;
            }
        }
    }

    private void findPage1(Integer currPage, Integer pageSize, List<UserLog> userLogList, Map map, HttpServletResponse response) throws IOException {
        if (currPage == null || "".equals(currPage) || currPage <= 0) {
            currPage = 1;//默认当前页
        }
        int totalPage = 1;  //默认总页数
        if (pageSize == null || "".equals(pageSize) || pageSize <= 0) {
            map.put("currPage", currPage);//当前页
            map.put("totalPage", totalPage);//总页数
            map.put("userLogList", userLogList);//
            ObjectToJson.objectToJson1(map, new String[]{"user", "postName", "userAgent",
                    "userName", "ip", "memo", "operatetime", "broswer", "logcontent", "type", "departmentName"}, response);
            return;
        } else {
            int maxResult = currPage * pageSize;//显示最大结果
            int minResult = (currPage - 1) * pageSize;//初始化结果
            int total0 = userLogList.size();
            /*
             * 计算总页数
             * */
            double total1 = total0;
            double pageSize1 = pageSize;
            double num = total1 / pageSize1;
            double totalPage1 = Math.ceil(num);
            totalPage = (int) totalPage1;
            if (total1 > 0) {
                List<UserLog> list = new ArrayList<UserLog>();
                for (int i = minResult; i < maxResult; i++) {
                    if (i < total1) {
                        list.add(userLogList.get(i));
                    }
                }
                map.put("currPage", currPage);//当前页
                map.put("totalPage", totalPage);//总页数
                map.put("userLogList", list);//
                ObjectToJson.objectToJson1(map, new String[]{"user", "postName", "userAgent",
                        "userName", "ip", "memo", "operatetime", "broswer", "logcontent", "type", "departmentName"}, response);
                return;
            } else {
                totalPage = 1;
                map.put("currPage", currPage);//当前页
                map.put("totalPage", totalPage);//总页数
                map.put("userLogList", userLogList);//
                ObjectToJson.objectToJson1(map, new String[]{"user", "postName", "userAgent",
                        "userName", "ip", "memo", "operatetime", "broswer", "logcontent", "type", "departmentName"}, response);
                return;
            }
        }
    }

    /**
     * 获得该月第一天
     *
     * @param year
     * @param month
     * @return
     */
    private String getFirstDayOfMonth(int year, int month) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, year);
        //设置月份
        cal.set(Calendar.MONTH, month - 1);
        //获取某月最小天数
        int firstDay = cal.getActualMinimum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最小天数
        cal.set(Calendar.DAY_OF_MONTH, firstDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String firstDayOfMonth = sdf.format(cal.getTime());
        return firstDayOfMonth;
    }

    /**
     * 获得该月最后一天
     *
     * @param year
     * @param month
     * @return
     */
    private String getLastDayOfMonth(int year, int month) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR, year);
        //设置月份
        cal.set(Calendar.MONTH, month - 1);
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String lastDayOfMonth = sdf.format(cal.getTime());
        return lastDayOfMonth;
    }

    /**
     * 获取某年第一天日期
     * @param year 年份
     * @return Date
     */
    private Date getYearFirst(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        Date currYearFirst = calendar.getTime();
        return currYearFirst;
    }

    /**
     * 获取某年最后一天日期
     *
     * @param year 年份
     * @return Date
     */
    private Date getYearLast(int year) {
        Calendar calendar = Calendar.getInstance();
        calendar.clear();
        calendar.set(Calendar.YEAR, year);
        calendar.roll(Calendar.DAY_OF_YEAR, -1);
        Date currYearLast = calendar.getTime();

        return currYearLast;
    }

    @Override
    public void loginRecordPersonal(Integer oid,Integer loginUserId, String loginYear,String mdate, String edate, Integer sort, Integer sign, Integer userID, Integer currPage, Integer flag, Integer pageSize, HttpServletResponse response) throws ParseException, IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        long durationTotal = 0;
        long duration;
        int total = 0;
        int computer = 0;
        int app = 0;

        if (flag == 1) {  //本日
            String date = NewDateUtils.dateToString(NewDateUtils.dateFromString(mdate,"yyyy-MM-dd"),"yyyy-MM-dd");
            User u = userService.getUserByID(userID);
            String userName = MyStrings.nulltoempty(u.getUserName(), u.getMobile());
            String postName = MyStrings.nulltoempty(u.getPostName());
            String departName = MyStrings.nulltoempty((u.getDepartName()));

            if (sort == 0) {//电脑端
                List<UserLog> userLogList = getDetailUserLogInfoByDay(oid,loginUserId, date, 3, userID);
                List<UserLog> userLogList1 = getDetailUserLogInfoByDay(oid,loginUserId, date, 1, userID);
                for (UserLog ul : userLogList) {
                    duration = ul.getDuration() * 1000;
                    durationTotal += duration;
                    ul.setDurationTime(duration);
                }
                for (UserLog ul : userLogList1) {
                    durationTotal += ul.getDuration() * 1000;
                }
                map.put("duration", TimeUtils.toTimeString(durationTotal));//总时长
                map.put("userName", userName);
                map.put("departmentName", departName);//部门名称
                map.put("postName", postName);//职位名称
                map.put("app", userLogList1.size());
                map.put("computer", userLogList.size());
                map.put("total", userLogList.size() + userLogList1.size());
                map.put("date", date);
                this.findPage(currPage, pageSize, userLogList, map, response);
            } else if (sort == 1) {//手机端
                List<UserLog> userLogList = getDetailUserLogInfoByDay(oid,loginUserId, date, 1, userID);
                List<UserLog> userLogList1 = getDetailUserLogInfoByDay(oid,loginUserId, date, 3, userID);
                for (UserLog ul : userLogList) {
                    duration = ul.getDuration() * 1000;
                    durationTotal += duration;
                    ul.setDurationTime(duration);
                }
                for (UserLog ul : userLogList1) {
                    durationTotal += ul.getDuration() * 1000;
                }
                map.put("duration", TimeUtils.toTimeString(durationTotal));//总时长
                map.put("userName", userName);
                map.put("departmentName", departName);//部门名称
                map.put("postName", postName);//职位名称
                map.put("app", userLogList.size());
                map.put("computer", userLogList1.size());
                map.put("total", userLogList.size() + userLogList1.size());
                map.put("date", date);
                this.findPage(currPage, pageSize, userLogList, map, response);
            }
        } else if (flag == 2) {  //本月
            if (sign == 0) {
                List<UserLog> userLogList = getDetailUserLogInfoByDay(oid,loginUserId,loginYear,null,userID);
                List<UserLog> userLogList1 = getDetailUserLogInfoByDay(oid,loginUserId, loginYear, 3,userID);
                List<UserLog> userLogList2 = getDetailUserLogInfoByDay(oid,loginUserId, loginYear, 1,userID);
                map.put("app", userLogList2.size());
                map.put("computer", userLogList1.size());
                map.put("total", userLogList2.size() + userLogList1.size());
                if(edate==null || mdate.equalsIgnoreCase(edate)) {
                    map.put("date", loginYear);
                } else {
                    map.put("date", mdate + "~" + edate);
                }
                if (userLogList.size() > 0) {
//                    List<UserLog> userLogRemove = new ArrayList<>();
                    for (UserLog ul : userLogList) {
                        User u = userService.getUserByID(ul.getUser_());
                        ul.setDepartmentName(MyStrings.nulltoempty(u.getDepartName()));//部门名称
                        ul.setPostName(MyStrings.nulltoempty(u.getPostName()));//职位名称
                        ul.setUserName(MyStrings.nulltoempty(u.getUserName(), u.getMobile()));
                        duration = ul.getDuration() * 1000;
                        durationTotal += duration;
                        ul.setDurationTime(duration);
//                        if ("super".equals(u.getRoleCode())){
//                            userLogRemove.add(ul);  //如果是董事长就将其移除
//                        }
                    }
//                    userLogList.removeAll(userLogRemove);
                }
                map.put("duration", TimeUtils.toTimeString(durationTotal));//总时长
                this.findPage(currPage, pageSize, userLogList, map, response);
            } else if (sign == 1) {  //查询本月个人
                List<UserLog> userLogList = new ArrayList<UserLog>();

                String beginDate = mdate;
                String endDate = edate;

                Date date = NewDateUtils.dateFromString(beginDate,"yyyy-MM-dd");
                Date date1 = NewDateUtils.dateFromString(endDate,"yyyy-MM-dd");
//                int y = NewDateUtils.getYear(date);
//                int m = NewDateUtils.getMonth(date) + 1;
//                int d = NewDateUtils.getDate(date);
//                int s = NewDateUtils.getDate(NewDateUtils.dateFromString(endDate,"yyyy-MM-dd"));
                User u = userService.getUserByID(userID);
                String userName = MyStrings.nulltoempty(u.getUserName(), u.getMobile());
                String postName = MyStrings.nulltoempty(u.getPostName());
                String departName = MyStrings.nulltoempty((u.getDepartName()));
                while (date.getTime() <= date1.getTime()) {
                    List<UserLog> userLogList1 = getDetailUserLogInfoByDay(oid,loginUserId,endDate,null,userID);
                    List<UserLog> userLogList2 = getDetailUserLogInfoByDay(oid,loginUserId,endDate, 3,userID);
                    List<UserLog> userLogList3 = getDetailUserLogInfoByDay(oid,loginUserId, endDate, 1,userID);

                    Set<Integer> set = new HashSet<Integer>();
                    duration = 0;
                    if (userLogList1.size() > 0) {
                        for (UserLog ul : userLogList1) {
                            set.add(ul.getUser_());
                            duration += ul.getDuration() * 1000;
                        }
                        userLogList1.get(0).setDepartmentName(departName);//部门名称
                        userLogList1.get(0).setPostName(postName);//职位名称
                        userLogList1.get(0).setUserName(userName);
                        userLogList1.get(0).setLoginYear(NewDateUtils.dateToString(userLogList1.get(0).getOperatetime(),"yyyy-MM-dd"));
                        userLogList1.get(0).setCount(userLogList1.size());//当日登陆总数
                        userLogList1.get(0).setCom(userLogList2.size());
                        userLogList1.get(0).setApp(userLogList3.size());
                        userLogList1.get(0).setDurationTime(duration);
                        durationTotal += duration;
                        userLogList.add(userLogList1.get(0));
                    } else {
                        UserLog ul = new UserLog();
                        ul.setDurationTime("0");
                        ul.setLoginYear(endDate);
                        ul.setUser_(u.getUserID());
                        ul.setUserName(u.getUserName());
                        ul.setDepartmentName(u.getDepartName());
                        ul.setPostName(u.getPostName());
                        userLogList.add(ul);
                    }
                    total = total + userLogList1.size();
                    app = app + userLogList3.size();
                    computer = computer + userLogList2.size();

                    date1 = NewDateUtils.changeDay(date1,-1);//加一天的时间
                    endDate = NewDateUtils.dateToString(date1,"yyyy-MM-dd");
                }

                map.put("firstdate", mdate);
                map.put("date", edate);
                map.put("total", total);//登录总次数
                map.put("app", app);//手机端
                map.put("computer", computer);//电脑端
                map.put("duration", TimeUtils.toTimeString(durationTotal));//总时长
                this.findPage(currPage, pageSize, userLogList, map, response);
            } else if (sign == 2) {//本月-个人登录明细
                String beginTime = mdate;
                if (sort == 0) { //电脑端
                    List<UserLog> userLogList = getDetailUserLogInfoByDay(oid,loginUserId, mdate, 3,userID);
                    List<UserLog> userLogList1 = getDetailUserLogInfoByDay(oid,loginUserId, mdate, 1,userID);
                    for (UserLog ul : userLogList) {
                        duration = ul.getDuration() * 1000;
                        durationTotal += duration;
                        ul.setDurationTime(duration);
                    }
                    for (UserLog ul : userLogList1) {
                        durationTotal += ul.getDuration() * 1000;
                    }
                    map.put("duration", TimeUtils.toTimeString(durationTotal));//总时长
                    User u = userService.getUserByID(userID);
                    map.put("userName", MyStrings.nulltoempty(u.getUserName(), u.getMobile()));
                    map.put("departmentName", MyStrings.nulltoempty(u.getDepartName()));//部门名称
                    map.put("postName", MyStrings.nulltoempty(u.getPostName()));//职位名称
                    map.put("app", userLogList1.size());
                    map.put("computer", userLogList.size());
                    map.put("total", userLogList.size() + userLogList1.size());
                    map.put("date", beginTime);
                    this.findPage(currPage, pageSize, userLogList, map, response);
                } else if (sort == 1) {//手机
                    List<UserLog> userLogList = new ArrayList<UserLog>();
                    userLogList = getDetailUserLogInfoByDay(oid,loginUserId, mdate, 1,userID);
                    List<UserLog> userLogList1 = getDetailUserLogInfoByDay(oid,loginUserId, mdate, 3,userID);
                    for (UserLog ul : userLogList) {
                        duration = ul.getDuration() * 1000;
                        durationTotal += duration;
                        ul.setDurationTime(duration);
                    }
                    for (UserLog ul : userLogList1) {
                        durationTotal += ul.getDuration() * 1000;
                    }
                    map.put("duration", TimeUtils.toTimeString(durationTotal));//总时长
                    User u = userService.getUserByID(userID);
                    map.put("userName", MyStrings.nulltoempty(u.getUserName(), u.getMobile()));
                    map.put("departmentName", MyStrings.nulltoempty(u.getDepartName()));//部门名称
                    map.put("postName", MyStrings.nulltoempty(u.getPostName()));//职位名称
                    map.put("app", userLogList.size());
                    map.put("computer", userLogList1.size());
                    map.put("total", userLogList.size() + userLogList1.size());
                    map.put("date", beginTime);
                    this.findPage(currPage, pageSize, userLogList, map, response);
                }
            }
        } else if (flag == 3) {  //本年
            if (sign == 1) {
                List<UserLog> userLogList = new ArrayList<UserLog>();

                Date date = NewDateUtils.dateFromString(mdate,"yyyy-MM-dd");
                String mdate1 = mdate;
                if (date==null) {
                    mdate1 = mdate + "-01";
                    date = NewDateUtils.dateFromString(mdate1,"yyyy-MM-dd");
                }

                String fdm = NewDateUtils.dateToString(NewDateUtils.changeMonth(date, 0),"yyyy-MM-dd");  //月的第一天
                Date newDate = new Date();

                String ldm = null;
                if (NewDateUtils.getMonth(date).equals(NewDateUtils.getMonth(newDate))) {
                    ldm = NewDateUtils.dateToString(NewDateUtils.today(newDate),"yyyy-MM-dd");   //月的最后一天
                } else {
                    ldm = NewDateUtils.dateToString(NewDateUtils.getLastTimeOfMonth(date),"yyyy-MM-dd");   //月的最后一天
                }

                Date date1 = NewDateUtils.dateFromString(fdm,"yyyy-MM-dd");
                Date date2 = NewDateUtils.dateFromString(ldm,"yyyy-MM-dd");
                if(edate!=null){
                    Date date3 = NewDateUtils.dateFromString(mdate1,"yyyy-MM-dd");
                    Date date4 = NewDateUtils.dateFromString(edate,"yyyy-MM-dd");
                    date1=date1.after(date3)?date1:date3;
                    date2=date2.before(date4)?date2:date4;
                }

                while (date1.getTime() <= date2.getTime()) {
                    int aa = 0;  //计算总的总登录次数的临时参数
                    List<UserLog> userLogList1 = getPersonalLoginRecordByYearDetailA(oid,loginUserId,userID, ldm, ldm,null);
                    List<UserLog> userLogList2 = new ArrayList<>();   //PC端
                    List<UserLog> userLogList3 = new ArrayList<>();   //手机端
                    duration = 0;
                    Set<Integer> set = new HashSet<Integer>();
                    if (userLogList1.size() > 0) {
                        for (UserLog u : userLogList1) {
                            set.add(u.getUser_());
                            duration += u.getDuration() * 1000;
                            if (!"".equals(u.getType()) && "3".equals(u.getType())) {
                                userLogList2.add(u);
                            } else if (!"".equals(u.getType())) {
                                userLogList3.add(u);
                            }
                        }

                        if (set.size() > 0) {
                            userLogList1.get(0).setSum(set.size());  //登录人数
                            userLogList1.get(0).setDurationTime(duration);
                            userLogList.add(userLogList1.get(0));
                        }
                        if (userLogList2.size() != 0) {
                            userLogList1.get(0).setCom(userLogList2.size());  //电脑端
                            computer = computer + userLogList2.size();   //总的电脑端
                            aa = userLogList2.size();
                        }
                        if (userLogList3.size() != 0) {
                            userLogList1.get(0).setApp(userLogList3.size());   //手机端
                            app = app + userLogList3.size();    //总的手机端
                            aa = aa + userLogList3.size();
                        }
                        if (aa != 0) {
                            userLogList1.get(0).setCount(aa);//登录总次数
                            total = total + userLogList1.get(0).getCount();   //总的登录总次数
                        }
                    } else {
                        UserLog ul = new UserLog();
                        ul.setOperatetime(date2);
                        ul.setDurationTime("0");
                        userLogList.add(ul);
                    }
                    durationTotal += duration;

                    date2 = NewDateUtils.changeDay(date2,-1);
                    ldm = NewDateUtils.dateToString(date2,"yyyy-MM-dd");
                }

                if(edate==null) {
                    map.put("date", mdate);
                } else {
                    map.put("date", mdate + "~" + edate);
                }
                map.put("total", total);//登录总次数
                map.put("app", app);//手机端
                map.put("computer", computer);//电脑端
                map.put("duration", TimeUtils.toTimeString(durationTotal));//总时长

                this.findPage(currPage, pageSize, userLogList, map, response);
            } else if (sign == 2) {//按姓名查看 个人本年
                List<UserLog> userLogList = new ArrayList<UserLog>();
                String b = mdate;
                String e = edate;

//                Date date = NewDateUtils.dateFromString(mdate,"yyyy-MM");
                Date date = NewDateUtils.dateFromString(mdate,"yyyy-MM-dd");
                int y = NewDateUtils.getYear(date);
                int m = NewDateUtils.getMonth(date);
//                Date date1 = NewDateUtils.dateFromString(edate,"yyyy-MM");
                Date date1 = NewDateUtils.dateFromString(edate,"yyyy-MM-dd");
                int m1 = NewDateUtils.getMonth(date1);

                User u = userService.getUserByID(userID);
                String userName = MyStrings.nulltoempty(u.getUserName(), u.getMobile());
                String departName = MyStrings.nulltoempty((u.getDepartName()));
                String postName = MyStrings.nulltoempty(u.getPostName());
                map.put("userName", userName);
                edate = edate.substring(0, 7);
                while (m <= m1) {
                    List<UserLog> userLogList1 = getDetailUserLogInfoByDay(oid,loginUserId,edate,null,userID);
                    List<UserLog> userLogList2 = getDetailUserLogInfoByDay(oid,loginUserId,edate,3,userID);
                    List<UserLog> userLogList3 = getDetailUserLogInfoByDay(oid,loginUserId,edate,1,userID);

                    duration = 0;
                    if (userLogList1.size() > 0) {
                        for (UserLog ul : userLogList1) {
                            duration += ul.getDuration() * 1000;
                        }
                        userLogList1.get(0).setLoginYear(mdate);
                        if (userLogList2.size() > 0) {
                            userLogList1.get(0).setCom(userLogList2.size());
                        }
                        if (userLogList3.size() > 0) {
                            userLogList1.get(0).setApp(userLogList3.size());
                        }
                        userLogList1.get(0).setUserName(userName);
                        userLogList1.get(0).setDepartmentName(departName);
                        userLogList1.get(0).setPostName(postName);
                        total = total + userLogList1.size();
                        app = app + userLogList3.size();
                        computer = computer + userLogList2.size();
                        userLogList1.get(0).setLoginYear(edate);
                        userLogList1.get(0).setCount(userLogList1.size());
                        userLogList1.get(0).setDurationTime(duration);
                        durationTotal += duration;
                        userLogList.add(userLogList1.get(0));
                    } else {
                        UserLog ul = new UserLog();
                        ul.setUser_(u.getUserID());
                        ul.setUserName(u.getUserName());
                        ul.setDurationTime("0");
                        ul.setLoginYear(edate);
                        ul.setUserName(userName);
                        ul.setDepartmentName(departName);
                        ul.setPostName(postName);
                        userLogList.add(ul);
                    }

                    m1--;//日加一天
                    String day = y + "-";
                    if (m1 < 10) {
                        day += "0";
                    }
                    day += m1;
                    edate = day;//加一个月的时间
                }
                map.put("firstdate", b);
                map.put("date", e);
                map.put("total", total);//登录总次数
                map.put("app", app);//手机端
                map.put("computer", computer);//电脑端
                map.put("duration", TimeUtils.toTimeString(durationTotal));//总时长
                this.findPage(currPage, pageSize, userLogList, map, response);
            } else if (sign == 3) {////wyu：not used???
                List<UserLog> userLogList = new ArrayList<UserLog>();
                //获取当前月第一天：
                Date date = NewDateUtils.dateFromString(mdate,"yyyy-MM");
                int y = NewDateUtils.getYear(date);
                int m = NewDateUtils.getMonth(date);
                String beginDate = NewDateUtils.dateToString(NewDateUtils.changeMonth(date,0),"yyyy-MM-dd");  //获取该月第一天
                String endDate =  NewDateUtils.dateToString(NewDateUtils.getLastTimeOfMonth(date),"yyyy-MM-dd");   //获取该月最后一天

                Date date1 = NewDateUtils.dateFromString(beginDate,"yyyy-MM-dd");
                Date date2 = NewDateUtils.dateFromString(endDate,"yyyy-MM-dd");
                int d = NewDateUtils.getDate(date1);
                int s = NewDateUtils.getDate(date2);
                User u = userService.getUserByID(userID);
                String userName = MyStrings.nulltoempty(u.getUserName(), u.getMobile());
                String departName = MyStrings.nulltoempty((u.getDepartName()));
                String postName = MyStrings.nulltoempty(u.getPostName());

                while (d <= s) {
                    List<UserLog> userLogList1 = getDetailUserLogInfoByDay(oid,loginUserId,beginDate,null,userID);;
                    List<UserLog> userLogList2 = getDetailUserLogInfoByDay(oid,loginUserId,beginDate,3,userID);;
                    List<UserLog> userLogList3 = getDetailUserLogInfoByDay(oid,loginUserId,beginDate,1,userID);;

                    if (userLogList1.size() > 0) {
                        userLogList1.get(0).setUserName(userName);
                        userLogList1.get(0).setDepartmentName(departName);//部门名称
                        userLogList1.get(0).setPostName(postName);//职位名称
                        userLogList1.get(0).setCount(userLogList1.size());//当日登陆总数
                        userLogList1.get(0).setCom(userLogList2.size());
                        userLogList1.get(0).setApp(userLogList3.size());
                        userLogList.add(userLogList1.get(0));
                    } else {
                        UserLog ul = new UserLog();
                        ul.setUser_(u.getUserID());
                        ul.setUserName(u.getUserName());
                        ul.setDurationTime("0");
                        ul.setLoginYear(edate);
                        ul.setUserName(userName);
                        ul.setDepartmentName(departName);
                        ul.setPostName(postName);
                        userLogList.add(ul);
                    }
                    total = total + userLogList1.size();
                    app = app + userLogList3.size();
                    computer = computer + userLogList2.size();

                    d += 1;//日加一天
                    String day = y + "-" + m + "-";
                    if (d < 10) {
                        day += "0";
                    }
                    day += d;

                    Date date11 = NewDateUtils.dateFromString(day,"yyyy-MM-dd");//加一天的时间
                    beginDate = NewDateUtils.dateToString(date11,"yyyy-MM-dd");
                }

                map.put("firstdate", NewDateUtils.dateToString(date1,"yyyy-MM-dd"));
                map.put("date", NewDateUtils.dateToString(date2,"yyyy-MM-dd"));
                map.put("total", total);//登录总次数
                map.put("app", app);//手机端
                map.put("computer", computer);//电脑端
                map.put("duration", TimeUtils.toTimeString(durationTotal));//总时长
                this.findPage(currPage, pageSize, userLogList, map, response);
            }
        } else if (flag == 4) {//自定义http://localhost:8888/general/loginRecordPersonal.do?flag=4&currPage=1&pageSize=20&mmdate=2018-04-04&edate=2018-04-09
            if (null == mdate || null == edate) {//参数不正确
                map.put("status", 0);
                ObjectToJson.objectToJson1(map, new String[]{"user"}, response);
                return;
            }
            String beginTime = mdate;
            String endTime = edate;
            int byear = NewDateUtils.getYear(NewDateUtils.dateFromString(beginTime,"yyyy-MM-dd"));
            int eyear = NewDateUtils.getYear(NewDateUtils.dateFromString(endTime,"yyyy-MM-dd"));
            if (sign == 1) {//按年份
                List<UserLog> userLogList = new ArrayList<UserLog>();
                if (byear == eyear) {//不跨年
                    int yue = NewDateUtils.getMonth(NewDateUtils.dateFromString(beginTime,"yyyy-MM-dd"));
                    int yue1 = NewDateUtils.getMonth(NewDateUtils.dateFromString(endTime,"yyyy-MM-dd"));
                    if (yue == yue1) {//跨日

                        Date dt1 = NewDateUtils.dateFromString(beginTime,"yyyy-MM-dd");
                        Date dt2 = NewDateUtils.dateFromString(endTime,"yyyy-MM-dd");
                        String temp = "";
                        if (dt1.getTime() > dt2.getTime()) {
                            temp = beginTime;
                            beginTime = endTime;
                            endTime = temp;
                        }
//                        String b = NewDateUtils.dateToString(dt1,"yyyy-MM-dd");
                        String e = NewDateUtils.dateToString(dt2,"yyyy-MM-dd");

                        List<UserLog> userLogList0 = new ArrayList<UserLog>();
                        List<UserLog> userLogList1 = new ArrayList<UserLog>();
                        List<UserLog> userLogList2 = new ArrayList<UserLog>();
                        int d1 = NewDateUtils.getDate(dt1);
                        int d2 = NewDateUtils.getDate(dt2);

                        while (d1 <= d2) {
                            userLogList0 = getUserLogByOneDay(oid,loginUserId,userID,e,null);
                            userLogList1 = getUserLogByOneDay(oid,loginUserId,userID,e,3);
                            userLogList2 = getUserLogByOneDay(oid,loginUserId,userID,e,1);

                            Set<Integer> set = new HashSet<Integer>();
                            if (userLogList0.size() > 0) {
                                duration = 0;
                                for (UserLog ul : userLogList0) {
                                    set.add(ul.getUser_());
                                    duration += ul.getDuration() * 1000;
                                }
                                durationTotal += duration;
                                userLogList0.get(0).setDurationTime(duration);
                                if (set.size() > 0) {
                                    userLogList0.get(0).setSum(set.size());//登陆总人数
                                }
                                userLogList0.get(0).setCom(userLogList1.size());
                                userLogList0.get(0).setApp(userLogList2.size());
                                userLogList0.get(0).setCount(userLogList0.size());//当日登陆总数
                                userLogList0.get(0).setLoginYear(e);
                                userLogList.add(userLogList0.get(0));
                            } else {
                                UserLog ul = new UserLog();
                                ul.setDurationTime("0");
                                ul.setLoginYear(e);
                                userLogList.add(ul);
                            }
                            total = total + userLogList0.size();
                            computer = computer + userLogList1.size();
                            app = app + userLogList2.size();

                            d2--;//月加一
                            String day = byear + "-";
                            if (yue < 10) {
                                day += "0";
                            }

                            day += (yue + "-");
                            if (d2 < 10) {
                                day += "0";
                            }
                            day += d2;
                            e = day;
                        }

                        map.put("beginTime", beginTime);
                        map.put("endTime", endTime);
                        map.put("total", total);//登录总次数
                        map.put("app", app);//手机端
                        map.put("computer", computer);//电脑端
                        map.put("duration", TimeUtils.toTimeString(durationTotal));//总时长
                        this.findPage1(currPage, pageSize, userLogList, map, response);
                    } else {//跨月
//                        SimpleDateFormat sdff = new SimpleDateFormat("yyyy-MM-dd");
//                        Date dt1 = sdff.parse(beginTime);
//                        Date dt2 = sdff.parse(endTime);
                        Date dt1 = NewDateUtils.dateFromString(beginTime,"yyyy-MM-dd");
                        Date dt2 = NewDateUtils.dateFromString(endTime,"yyyy-MM-dd");
                        String temp = "";
                        if (dt1.getTime() > dt2.getTime()) {
                            temp = beginTime;
                            beginTime = endTime;
                            endTime = temp;
                        }
                        String b = beginTime;
                        String e = endTime;

                        List<UserLog> userLogList0 = new ArrayList<UserLog>();
                        List<UserLog> userLogList1 = new ArrayList<UserLog>();
                        List<UserLog> userLogList2 = new ArrayList<UserLog>();
                        int m = NewDateUtils.getMonth(dt1);
                        int m1 = NewDateUtils.getMonth(dt2);

                        int a = 0;
                        while (m <= m1) {
                            int y1 = NewDateUtils.getYear(dt1);
                            if (a == 0) {
                                String mount = getFirstDayOfMonth(y1, m1);   //获得该月第一天
                                b = mount;
                            }
                            if (a > 0) {
                                String enmount = getLastDayOfMonth(y1, m1);   //获得该月最后一天
                                e = enmount;
                            }
                            userLogList0 = getUserLogByFreedom(oid,loginUserId,userID, b, e,null);
                            userLogList1 = getUserLogByFreedom(oid,loginUserId,userID, b, e,3);
                            userLogList2 = getUserLogByFreedom(oid,loginUserId,userID, b, e,1);

                            Set<Integer> set = new HashSet<Integer>();
                            if (userLogList0.size() > 0) {
                                duration = 0;
                                for (UserLog ul : userLogList0) {
                                    set.add(ul.getUser_());
                                    duration += ul.getDuration() * 1000;
                                }
                                durationTotal += duration;
                                userLogList0.get(0).setDurationTime(duration);
                                if (set.size() > 0) {
                                    userLogList0.get(0).setSum(set.size());//登陆总人数
                                }
                                userLogList0.get(0).setCom(userLogList1.size());
                                userLogList0.get(0).setApp(userLogList2.size());
                                userLogList0.get(0).setCount(userLogList0.size());//当日登陆总数
                                userLogList0.get(0).setLoginYear(e.substring(0, 7));
                                userLogList.add(userLogList0.get(0));
                            } else {
                                UserLog ul = new UserLog();
                                ul.setDurationTime("0");
                                ul.setLoginYear(e.substring(0, 7));
                                userLogList.add(ul);
                            }
                            total = total + userLogList0.size();
                            computer = computer + userLogList1.size();
                            app = app + userLogList2.size();

                            m1--;//月加一
                            e = b;
                            if (m == m1) {
                                b = beginTime;
                            } else {
                                String mount = getFirstDayOfMonth(y1, m1);
                                b = mount;
                            }
                            a++;
                        }
                        map.put("firstdate", beginTime);
                        map.put("date", endTime);
                        map.put("total", total);//登录总次数
                        map.put("app", app);//手机端
                        map.put("computer", computer);//电脑端
                        map.put("duration", TimeUtils.toTimeString(durationTotal));//总时长
                        this.findPage1(currPage, pageSize, userLogList, map, response);
                    }
                } else if (byear != eyear) {//跨年
                    int temp;
                    String tempStr;
                    String b;
                    String e;
                    if (byear > eyear) {
                        temp = eyear;
                        eyear = byear;
                        byear = temp;
                        tempStr = endTime;
                        endTime = beginTime;
                        beginTime = tempStr;
                    }
                    b = beginTime;
                    e = endTime;
                    String kaishi = beginTime;
                    String jieshu = endTime;
                    int k = 0;
                    while (byear <= eyear) {

                        if (k == 0) {
                            Date dfirst = getYearFirst(eyear);
                            beginTime = NewDateUtils.dateToString(dfirst,"yyyy-MM-dd");
                        }
                        List<UserLog> userLogList0 = getUserLogByFreedom(oid,loginUserId,userID, beginTime , jieshu,null);
                        List<UserLog> userLogList2 = getUserLogByFreedom(oid,loginUserId,userID, beginTime, jieshu,3);
                        List<UserLog> userLogList3 = getUserLogByFreedom(oid,loginUserId,userID, beginTime, jieshu,1);

                        Set<Integer> set = new HashSet<Integer>();
                        if (userLogList0.size() > 0) {
                            duration = 0;
                            for (UserLog ul : userLogList0) {
                                set.add(ul.getUser_());
                                duration += ul.getDuration() * 1000;
                            }
                            durationTotal += duration;
                            userLogList0.get(0).setDurationTime(duration);
                        }
                        if (userLogList0.size() > 0) {
                            userLogList0.get(0).setSum(set.size());
                            userLogList0.get(0).setCom(userLogList2.size());
                            userLogList0.get(0).setApp(userLogList3.size());
                            userLogList0.get(0).setCount(userLogList0.size());//当日登陆总数
                            userLogList0.get(0).setLoginYear(beginTime.substring(0, 4));
                            userLogList.add(userLogList0.get(0));
                        } else {
                            UserLog ul = new UserLog();
                            ul.setDurationTime("0");
                            ul.setLoginYear(beginTime.substring(0, 4));
                            userLogList.add(ul);
                        }
                        total = total + userLogList0.size();
                        computer = computer + userLogList2.size();
                        app = app + userLogList3.size();

                        eyear--;
                        Date dlast = getYearLast(eyear);
                        jieshu = NewDateUtils.dateToString(dlast,"yyyy-MM-dd");
                        if (byear == eyear) {
                            beginTime = kaishi;
                        } else {
                            Date df = getYearFirst(eyear);
                            beginTime = NewDateUtils.dateToString(df,"yyyy-MM-dd");
                        }
                        k++;
                    }
                    map.put("firstdate", b);
                    map.put("date", e);
                    map.put("total", total);//登录总次数
                    map.put("app", app);//手机端
                    map.put("computer", computer);//电脑端
                    map.put("userLogList", userLogList);
                    map.put("duration", TimeUtils.toTimeString(durationTotal));//总时长

                    this.findPage1(currPage, pageSize, userLogList, map, response);
                }
            } else if (sign == 2) {//个人
                User u = userService.getUserByID(userID);
                String userName = MyStrings.nulltoempty(u.getUserName(), u.getMobile());
                String departName = MyStrings.nulltoempty((u.getDepartName()));
                String postName = MyStrings.nulltoempty(u.getPostName());
                map.put("userName", userName);
                List<UserLog> userLogList = new ArrayList<UserLog>();
                List<UserLog> userLogList0;
                List<UserLog> userLogList1;
                List<UserLog> userLogList2;
                if (byear == eyear) {//不跨年
                    int yue = NewDateUtils.getMonth(NewDateUtils.dateFromString(beginTime,"yyyy-MM-dd"));
                    int yue1 = NewDateUtils.getMonth(NewDateUtils.dateFromString(endTime,"yyyy-MM-dd"));
                    if (yue == yue1) {//跨日
                        Date dt1 = NewDateUtils.dateFromString(beginTime,"yyyy-MM-dd");
                        Date dt2 = NewDateUtils.dateFromString(endTime,"yyyy-MM-dd");
                        String temp = "";
                        if (dt1.getTime() > dt2.getTime()) {
                            temp = beginTime;
                            beginTime = endTime;
                            endTime = temp;
                        }

                        String b = NewDateUtils.dateToString(dt1,"yyyy-MM-dd");
                        String e = NewDateUtils.dateToString(dt2,"yyyy-MM-dd");
                        int d1 = NewDateUtils.getDate(dt1);
                        int d2 = NewDateUtils.getDate(dt2);

                        while (d1 <= d2) {
                            userLogList0 = getDetailUserLogInfoByDay(oid,loginUserId,e,null,u.getUserID());
                            userLogList1 = getDetailUserLogInfoByDay(oid,loginUserId,e,3,u.getUserID());
                            userLogList2 = getDetailUserLogInfoByDay(oid,loginUserId,e,1,u.getUserID());

                            Set<Integer> set = new HashSet<Integer>();
                            if (userLogList0.size() > 0) {
                                duration = 0;
                                for (UserLog ul : userLogList0) {
                                    set.add(ul.getUser_());
                                    duration += ul.getDuration() * 1000;
                                }
                                durationTotal += duration;
                                userLogList0.get(0).setDurationTime(duration);
                                if (set.size() > 0) {
                                    userLogList0.get(0).setSum(set.size());//登陆总人数
                                }
                                userLogList0.get(0).setCom(userLogList1.size());
                                userLogList0.get(0).setApp(userLogList2.size());
                                userLogList0.get(0).setCount(userLogList0.size());//当日登陆总数
                                userLogList0.get(0).setLoginYear(e);
                                userLogList0.get(0).setUserName(userName);
                                userLogList0.get(0).setDepartmentName(departName);
                                userLogList0.get(0).setPostName(postName);
                                userLogList.add(userLogList0.get(0));
                            } else {
                                UserLog ul = new UserLog();
                                ul.setDurationTime("0");
                                ul.setLoginYear(e);
                                ul.setUser_(u.getUserID());
                                ul.setUserName(userName);
                                ul.setDepartmentName(departName);
                                ul.setPostName(postName);
                                userLogList.add(ul);
                            }
                            total = total + userLogList0.size();
                            computer = computer + userLogList1.size();
                            app = app + userLogList2.size();

                            d2--;//月加一
                            String day = byear + "-";
                            if (yue < 10) {
                                day += "0";
                            }

                            day += (yue + "-");
                            if (d2 < 10) {
                                day += "0";
                            }
                            day += d2;
                            e = day;
                        }

                        map.put("firstdate", beginTime);
                        map.put("date", endTime);
                        map.put("total", total);//登录总次数
                        map.put("app", app);//手机端
                        map.put("computer", computer);//电脑端
                        map.put("duration", TimeUtils.toTimeString(durationTotal));//总时长
                        this.findPage(currPage, pageSize, userLogList, map, response);
                    } else {//跨月
                        Date dt1 = NewDateUtils.dateFromString(beginTime,"yyyy-MM-dd");
                        Date dt2 =  NewDateUtils.dateFromString(endTime,"yyyy-MM-dd");
                        String temp = "";
                        if (dt1.getTime() > dt2.getTime()) {
                            temp = beginTime;
                            beginTime = endTime;
                            endTime = temp;
                        }
                        String b = beginTime;
                        String e = endTime;

//                        int m = DateUtils.getMonth(sdff.parse(beginTime)) + 1;
//                        int m1 = DateUtils.getMonth(sdff.parse(endTime)) + 1;
                        int m = DateUtils.getMonth(dt1) + 1;
                        int m1 = DateUtils.getMonth(dt2) + 1;

                        int a = 0;
                        while (m <= m1) {
                            int y1 = NewDateUtils.getYear(dt1);
                            if (a == 0) {
                                String mount = getFirstDayOfMonth(y1, m1);
                                b = mount;
                            }
                            if (a > 0) {
                                String enmount = getLastDayOfMonth(y1, m1);
                                e = enmount;
                            }
                            userLogList0 = getUserLogByFreedom(oid,loginUserId,u.getUserID(), b, e,null);
                            userLogList1 = getUserLogByFreedom(oid,loginUserId,u.getUserID(), b, e,3);
                            userLogList2 = getUserLogByFreedom(oid,loginUserId,u.getUserID(), b, e,1);

                            Set<Integer> set = new HashSet<Integer>();
                            if (userLogList0.size() > 0) {
                                duration = 0;
                                for (UserLog ul : userLogList0) {
                                    set.add(ul.getUser_());
                                    duration += ul.getDuration() * 1000;
                                }
                                durationTotal += duration;
                                userLogList0.get(0).setDurationTime(duration);
                                if (set.size() > 0) {
                                    userLogList0.get(0).setSum(set.size());//登陆总人数
                                }
                                userLogList0.get(0).setCom(userLogList1.size());
                                userLogList0.get(0).setApp(userLogList2.size());
                                userLogList0.get(0).setCount(userLogList0.size());//当日登陆总数
                                userLogList0.get(0).setLoginYear(e.substring(0, 7));
                                userLogList0.get(0).setUserName(userName);
                                userLogList0.get(0).setDepartmentName(departName);
                                userLogList0.get(0).setPostName(postName);
                                userLogList.add(userLogList0.get(0));
                            } else {
                                UserLog ul = new UserLog();
                                ul.setDurationTime("0");
                                ul.setLoginYear(e.substring(0, 7));
                                ul.setUser_(u.getUserID());
                                ul.setUserName(userName);
                                ul.setDepartmentName(departName);
                                ul.setPostName(postName);
                                userLogList.add(ul);
                            }
                            total = total + userLogList0.size();
                            computer = computer + userLogList1.size();
                            app = app + userLogList2.size();

                            m1--;//月加一
                            e = b;
                            if (m == m1) {
                                b = beginTime;
                            } else {
                                String mount = getFirstDayOfMonth(y1, m1);
                                b = mount;
                            }
                            a++;
                        }
                        map.put("firstdate", beginTime);
                        map.put("date", endTime);
                        map.put("total", total);//登录总次数
                        map.put("app", app);//手机端
                        map.put("computer", computer);//电脑端
                        map.put("duration", TimeUtils.toTimeString(durationTotal));//总时长
                        this.findPage(currPage, pageSize, userLogList, map, response);
                    }
                } else if (byear != eyear) {//跨年
                    int temp;
                    String tempStr;
                    String b;
                    String e;
                    if (byear > eyear) {
                        temp = eyear;
                        eyear = byear;
                        byear = temp;
                        tempStr = endTime;
                        endTime = beginTime;
                        beginTime = tempStr;
                    }
                    b = beginTime;
                    e = endTime;
                    String kaishi = beginTime;
                    String jieshu = endTime;
                    int k = 0;
                    while (byear <= eyear) {
                        if (k == 0) {
                            Date dfirst = getYearFirst(eyear);
                            beginTime = NewDateUtils.dateToString(dfirst,"yyyy-MM-dd");
                        }
                        userLogList0 = getUserLogByFreedom(oid,loginUserId,u.getUserID(), beginTime, jieshu,null);
                        userLogList1 = getUserLogByFreedom(oid,loginUserId,u.getUserID(), beginTime, jieshu,3);
                        userLogList2 = getUserLogByFreedom(oid,loginUserId,u.getUserID(), beginTime, jieshu,1);

                        Set<Integer> set = new HashSet<Integer>();
                        if (userLogList0.size() > 0) {
                            duration = 0;
                            for (UserLog ul : userLogList0) {
                                set.add(ul.getUser_());
                                duration += ul.getDuration() * 1000;
                            }
                            durationTotal += duration;
                            userLogList0.get(0).setDurationTime(duration);
                        }
                        if (userLogList0.size() > 0) {
                            userLogList0.get(0).setSum(set.size());
                            userLogList0.get(0).setCom(userLogList1.size());
                            userLogList0.get(0).setApp(userLogList2.size());
                            userLogList0.get(0).setCount(userLogList0.size());//当日登陆总数
                            userLogList0.get(0).setLoginYear(beginTime.substring(0, 4));
                            userLogList0.get(0).setUserName(userName);
                            userLogList0.get(0).setDepartmentName(departName);
                            userLogList0.get(0).setPostName(postName);
                            userLogList.add(userLogList0.get(0));
                        } else {
                            UserLog ul = new UserLog();
                            ul.setDurationTime("0");
                            ul.setLoginYear(beginTime.substring(0, 4));
                            ul.setUser_(u.getUserID());
                            ul.setUserName(userName);
                            ul.setDepartmentName(departName);
                            ul.setPostName(postName);
                            userLogList.add(ul);
                        }
                        total = total + userLogList0.size();
                        computer = computer + userLogList1.size();
                        app = app + userLogList2.size();

                        eyear--;
                        Date dlast = getYearLast(eyear);
                        jieshu = NewDateUtils.dateToString(dlast,"yyyy-MM-dd");
                        if (byear == eyear) {
                            beginTime = kaishi;
                        } else {
                            Date df = getYearFirst(eyear);
                            beginTime = NewDateUtils.dateToString(df,"yyyy-MM-dd");
                        }
                        k++;
                    }
                    map.put("firstdate", b);
                    map.put("date", e);
                    map.put("total", total);//登录总次数
                    map.put("app", app);//手机端
                    map.put("computer", computer);//电脑端
                    map.put("userLogList", userLogList);
                    map.put("duration", TimeUtils.toTimeString(durationTotal));//总时长

                    this.findPage(currPage, pageSize, userLogList, map, response);
                }
            }
        }
    }

    @Override
    public UserLog getLastUserLogByUserId(Integer userId) {
        String hql=" from UserLog where user_=:userId order by id desc";
        Map<String,Object> map=new HashMap<>();
        map.put("userId",userId);
        UserLog userLog= (UserLog) userLogDao.getByHQLWithNamedParams(hql,map);
        return userLog;
    }

    @Override
    public void deleteUserLogsByUserId(Integer userId) {
        String hql=" delete from UserLog where user_=:userId";
        Map<String,Object> map=new HashMap<>();
        map.put("userId",userId);
        userLogDao.queryHQLWithNamedParams(hql,map);
    }

    @Override
    public Map<String, Long> getUserLoginCountsByOid(Integer oid, Date beginTime, Date endTime,List<Integer> inUserIds) {
//        String hql="select ul from UserLog ul left join User user on ul.user_=user.userID where ul.org=:oid and user.roleCode=:roleCode and ul.operatetime between :beginTime and :endTime";
//        Map<String, Object> params = new HashMap<>();
//        params.put("beginTime", beginTime);
//        params.put("endTime", endTime);
//        params.put("oid", oid);
//        params.put("roleCode",roleCode);
//        List<UserLog> userLogList= (List<UserLog>) userLogDao.getByHQLWithNamedParams(hql,params);


//        List<Integer> userIdList=new ArrayList<>();
//        Integer durations=0;
//        for (UserLog userLog:userLogList){
//            if (!userIdList.contains(userLog.getUser_())){
//                userIdList.add(userLog.getUser_());
//            }
//            durations+=userLog.getDuration();
//        }

        String hql="select new Map(count(distinct user_) as userCount,sum(duration) as userDuration,count(id) as userLoginCount) from UserLog where org=:oid and operatetime between :beginTime and :endTime";
        Map<String, Object> params = new HashMap<>();
        params.put("beginTime", beginTime);
        params.put("endTime", endTime);
        params.put("oid", oid);

        if (inUserIds!=null){
            hql+=" and user_ in(:userIds)";
            params.put("userIds",inUserIds);
        }
        Map<String,Long> resultMap= (Map<String,Long>) userLogDao.getByHQLWithNamedParams(hql, params);
        return resultMap;
    }


    @Override
    public Map<Integer, Date> getUserLastLoginDate(List<Integer> userIds) {
//        String hql=" from UserLog where user_ in(:userIds) group by user_ order by id desc";
        String hql="select e from UserLog e where e.id =(SELECT MAX(e2.id) FROM UserLog e2  WHERE e2.user_ = e.user_) and e.user_ in(:userIds)";

        Map<String, Object> params = new HashMap<>();
        params.put("userIds",userIds);
        List<UserLog> userLogList=  userLogDao.getListByHQLWithNamedParams(hql, params);
        Map<Integer,Date> resultMap=new HashMap<>();
        for (UserLog userLog:userLogList){
            resultMap.put(userLog.getUser_(),userLog.getOperatetime());
        }
        return resultMap;
    }
}
