package cn.sphd.miners.modules.resourceAuthority.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.resourceAuthority.entity.ResCorrelation;
import cn.sphd.miners.modules.resourceAuthority.entity.ResCorrelationHis;
import cn.sphd.miners.modules.system.entity.User;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * Created by 朱思旭 on 2022/1/28.
 */
public interface ResCorrelationService {

    //新增文件与表格的关联
    ResCorrelation addCorrliation(User user, Integer resource, Integer attId, String operation, Date createDate);

    //新增文件与表格的关联历史
    ResCorrelationHis addResCorrelationHis(User user, Integer resource, Integer attId, Integer corrId, String operation, Date createDate);

    //查询某表格是否存在关联
    Integer checkCorrelation(Integer attId, String operation);

    //三种状态下新增关联
    HashMap<String, Object> threeformAddCorrelation(User user, Integer id, Integer resource);

    //解除文件与表格的关联
    HashMap<String, Object> removeCorrelation(User user, Integer id, Integer resource);

    //解除关联时的状态
    HashMap<String, Object> getRemoveCorrelationState(Integer id, Integer resource);

    //通过表格获取关联文件
    List<ResCorrelation> getCorrealtionFile(Integer id, Integer fileId, String type, PageInfo pageInfo);

    //通过表格获取关联文件并在返回值中不带文件信息
    List<ResCorrelation> getCorrFile(Integer id);

    //获取表格的关联记录
    List<ResCorrelationHis> getCorrelationHistory(Integer id, Integer fileId, PageInfo pageInfo);

    //通过文件获取关联的表格
    List<ResCorrelation> getCorrelationResAtt(Integer fileId, PageInfo pageInfo);

    //废止文件时批量解除文件与表格的关联
    void batchRemoveCorrelation(Integer fileId, User user);

    //废止时获取文件与表格的关联情况
    HashMap<String,Object> getCorrelationForAbolishFile(Integer fileId);

    ResCorrelation getSingleResCorrelation(Integer id);

}
