<template>
  <div class="">
    <Pageheader :title="title" :showBack="true"/>
    <div class="live-contain">
      <div class="live-cells">
        <div class="live-cell">{{info.content}}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getAboutFinalDetail, getAboutFileList } from "@/api/api.js";
import { reactive, ref, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";

let title = ref()
let info = ref({content: ''})
let pageInfo = ref({})
const router = useRouter();
const route = useRoute();
console.log('params', route.params)
const name = route.params.name
info.value = { content: route.query.content }

onMounted(() => {
  switch (name) {
    case 'userAgreement':
      title.value = '服务协议'
      break
    case 'privacyPolicy':
      title.value = '隐私条款'
      break
  }
})

</script>

<style lang="scss">
.houseSty8{
  position: relative;
  top:5px
}
</style>



