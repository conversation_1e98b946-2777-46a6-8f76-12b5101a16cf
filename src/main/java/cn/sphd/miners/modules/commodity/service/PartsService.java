package cn.sphd.miners.modules.commodity.service;

import cn.sphd.miners.modules.commodity.entity.PdAssemble;
import cn.sphd.miners.modules.commodity.entity.PdBase;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;
import java.util.Map;

public interface PartsService {

    //获取零组件列表
    Map<String,Object>  getPartsList(Integer oid, String category, String process, String composition,String enabled, String param,Integer pageNo,Integer pageSize);
    //获取暂存系统的零组件
    Map<String,Object>  getPartsListForTemporary(Integer oid,String source, String composition, String param, Integer pageNo,Integer pageSize);
    //获取统计情况
    PdAssemble getPdAssemble(Integer pdBaseId);
    //直接含有本件的产品列表
    List<Map<String,Object>>  getProductListForZhiJie(  Integer id,String type);
    //间接含有本件的产品列表
    List<Map<String,Object>>  getProductListForJianJie( Integer id,String type);
    //曾直接含有本件的产品列表
    List<Map<String,Object>>  getProductListForBeforeZhiJie( Integer id,String type);
    //曾间接含有本件的产品列表
    List<Map<String,Object>>  getProductListForBeforeJianjie( Integer id,String type);
    //查看当前全部的零组件
    List<Map<String,Object>>  getAllPartsByParts( Integer id,Integer level);
    //查看曾全部的零组件
    List<Map<String,Object>>  getAllPartsByBeforeParts( Integer id);
    //获取零组件设置
    Map<String,Object> getPdViewSetting(User user);
    //零组件设置确认
    void setPdViewSetting(User user,String cp, String cc,String cf);
    //构成修改
    void updateComposition(User user,Integer id,String composition,String process );
    //
    String updatePartsBase(User user, PdBase pdBase);
    //获取所绑定的材料
    List<Map<String,Object>> getMaterialList(Integer pdBaseId);
    //获取配方列表
    List<Map<String,Object>> getFormulaList(Integer pdBaseId);

    List<Map<String,Object>> getCompositionRecordBaseList(Integer pdBaseId);
    //获取构成修改记录详情
    Map<String,Object>  getCompositionRecordBaseDetails(Integer id,Integer frontId);
}
