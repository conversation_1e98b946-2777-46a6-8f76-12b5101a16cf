<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.accountant.mapper.DetailHistoryMapper" >

  <select id="getSingle" parameterType="historySubjectDetail" resultType="historySubjectDetail">
    select 
    id, detail_id AS detaialId, subject_id AS subjectId, subject, type, summary, balance, credit, debit, cash_flow_code AS cashFlowCode,
    balance_direction AS balanceDirection, memo, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName,
    update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
    audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId,
    credit_quantity AS creditQuantity,debit_quantity AS debitQuantity,unit_price AS unitPrice
    from t_accountant_subject_detail_history
    where id = #{id}
  </select>

  <select id="listPage" parameterType="hashmap" resultType="historySubjectDetail">
    select
       id, detail_id AS detaialId, subject_id AS subjectId, subject, type, summary, balance, credit, debit, cash_flow_code AS cashFlowCode,
    balance_direction AS balanceDirection, memo, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName,
    update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
    audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId,
    credit_quantity AS creditQuantity,debit_quantity AS debitQuantity,unit_price AS unitPrice
    from t_accountant_subject_detail_history
  </select>
  <select id="getSubjectDetailHistory"
          resultType="historySubjectDetail">

    SELECT  subject_id,subject,type,summary,credit,debit,balance_direction,creator,create_name,create_date FROM
        t_accountant_subject_detail_history WHERE subject_id=#{subjectId} AND balance_direction=#{balanceDirection}

  </select>
  <select id="getSubjectDetail"
          resultType="historySubjectDetail">

        select
    id, detail_id AS detaialId, subject_id AS subjectId, subject, type, summary, balance, credit, debit, cash_flow_code AS cashFlowCode,
    balance_direction AS balanceDirection, memo, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName,
    update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
    audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId,
    credit_quantity AS creditQuantity,debit_quantity AS debitQuantity,unit_price AS unitPrice
    from t_accountant_subject_detail_history
    where subject_id=#{subjectId}

  </select>

  <delete id="delete" parameterType="historySubjectDetail" >
    delete from t_accountant_subject_detail_history
    where id = #{id}
  </delete>

  <delete id="deleteHistoryDetail">
    DELETE FROM t_accountant_subject_detail_history WHERE subject_id=#{subjectId}
  </delete>
  <delete id="deleteDetail">

  DELETE FROM t_accountant_subject_detail_history WHERE subject_id=#{subjectId}

  </delete>

  <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="historySubjectDetail">
    insert into t_accountant_subject_detail_history ( detail_id, subject_id,
      subject, type, summary, 
      balance, credit, debit, 
      cash_flow_code, balance_direction, memo, 
      creator, create_name, create_date, 
      updator, update_name, update_date, 
      approve_Item, approve_status, approve_level, 
      auditor, auditor_name, audit_date, 
      operation, apply_memo, approve_memo, 
      message_id,credit_quantity,debit_quantity,unit_price)
    values ( #{detailId}, #{subjectId},
      #{subject}, #{type}, #{summary},
      #{balance}, #{credit}, #{debit},
      #{cashFlowCode}, #{balanceDirection}, #{memo},
      #{creator}, #{createName}, #{createDate},
      #{updator}, #{updateName}, #{updateDate},
      #{approveItem}, #{approveStatus}, #{approveLevel},
      #{auditor}, #{auditorName}, #{auditDate},
      #{operation}, #{applyMemo}, #{approveMemo},
      #{messageId},#{creditQuantity},#{debitQuantity},#{unitPrice})
  </insert>

  <insert id="insertDetailHistorySwap" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO `t_accountant_subject_detail_history`(subject_id,subject,type,
    summary,credit,debit,balance,balance_direction,creator,create_name,create_date,detail_id,credit_quantity,debit_quantity,unit_price)
    SELECT  #{subjectId},subject,type,summary,credit,debit,balance,#{balanceDirection},creator,create_name,NOW(),#{detailId},
    #{creditQuantity},#{debitQuantity},#{unitPrice} FROM
    t_accountant_subject_detail WHERE subject_id=#{originalDetailID} AND balance_direction=#{balanceDirection}
  </insert>

  <update id="update" parameterType="historySubjectDetail">
    update t_accountant_subject_detail_history
    <set >
      <if test="detailId != null" >
        detail_id = #{detailId},
      </if>
      <if test="subjectId != null" >
        subject_id = #{subjectId},
      </if>
      <if test="subject != null" >
        subject = #{subject},
      </if>
      <if test="type != null" >
        type = #{type},
      </if>
      <if test="summary != null" >
        summary = #{summary},
      </if>
      <if test="balance != null" >
        balance = #{balance},
      </if>
      <if test="credit != null" >
        credit = #{credit},
      </if>
      <if test="debit != null" >
        debit = #{debit},
      </if>
      <if test="cashFlowCode != null" >
        cash_flow_code = #{cashFlowCode},
      </if>
      <if test="balanceDirection != null" >
        balance_direction = #{balanceDirection},
      </if>
      <if test="memo != null" >
        memo = #{memo},
      </if>
      <if test="creator != null" >
        creator = #{creator},
      </if>
      <if test="createName != null" >
        create_name = #{createName},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate},
      </if>
      <if test="updator != null" >
        updator = #{updator},
      </if>
      <if test="updateName != null" >
        update_name = #{updateName},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate},
      </if>
      <if test="approveItem != null" >
        approve_Item = #{approveItem},
      </if>
      <if test="approveStatus != null" >
        approve_status = #{approveStatus},
      </if>
      <if test="approveLevel != null" >
        approve_level = #{approveLevel},
      </if>
      <if test="auditor != null" >
        auditor = #{auditor},
      </if>
      <if test="auditorName != null" >
        auditor_name = #{auditorName},
      </if>
      <if test="auditDate != null" >
        audit_date = #{auditDate},
      </if>
      <if test="operation != null" >
        operation = #{operation},
      </if>
      <if test="applyMemo != null" >
        apply_memo = #{applyMemo},
      </if>
      <if test="approveMemo != null" >
        approve_memo = #{approveMemo},
      </if>
      <if test="messageId != null" >
        message_id = #{messageId},
      </if>
      <if test="originaldetailid != null" >
        originalDetailID = #{originaldetailid},
      </if>
      <if test="creditQuantity != null" >
        credit_quantity = #{creditQuantity},
      </if>
      <if test="debitQuantity != null" >
        debit_quantity = #{debitQuantity},
      </if>
      <if test="unitPrice != null" >
        unit_price = #{unitPrice},
      </if>
    </set>
    where id = #{id}
  </update>
</mapper>