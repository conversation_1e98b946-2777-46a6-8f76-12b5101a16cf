package cn.sphd.miners.modules.thirdPlatform.task;

import cn.sphd.miners.common.utils.GetLocalIPUtils;
import cn.sphd.miners.modules.system.service.DlmService;
import cn.sphd.miners.modules.thirdPlatform.service.ThirdPlatformService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import javax.sql.DataSource;
import java.util.concurrent.TimeUnit;

public class TpThirdPlatformTask {
    @Autowired
    DlmService dlmService;
    @Autowired
    ThirdPlatformService service;
    @Autowired
    DataSource dataSource;
    public void tpAccessToken() {
        if(checkEnableTask()) {
            final Logger logger = Logger.getLogger(getClass());
            logger.warn("tpAccessToken:开始定时获取分布式锁");
            //wyu:获取分布式锁
            String lockKey, methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
            if ((lockKey = dlmService.getLock(methodName, TimeUnit.MINUTES.toMillis(30))) != null) {
                logger.warn("tpAccessToken:获取分布式锁成功");
                service.tpAccessTokenTask();
                logger.warn("tpAccessToken:执行完成，释放分布式锁");
                //wyu:释放分布式锁
                dlmService.releaseLock(methodName, lockKey);
            } else {
                logger.warn("tpAccessToken:未成功获取分布式锁");
            }
        }
    }
    public void tpMerchant() {
        if(checkEnableTask()) {
            System.out.println("开始获取支付/商户Service");
            service.tpMerchantTask();
            System.out.println("完成获取支付/商户Service");
        }
    }
    private boolean checkEnableTask() {//开发环境只有配置文件设置miners.enable_mini_tp_task = TRUE，才执行本定时任务。
        System.out.println("miners.enable_mini_tp_task=" + System.getProperty("miners.enable_mini_tp_task"));
        return "TRUE".equalsIgnoreCase(System.getProperty("miners.enable_mini_tp_task")) || GetLocalIPUtils.isServer(dataSource);
    }
}
