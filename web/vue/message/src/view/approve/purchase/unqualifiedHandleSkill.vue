<template>
  <div id="unqualifiedHandleSkill">
    <TY_NavTop title="不合格品处理" isSearch="true" @toggleList="search" :isBackHome=false></TY_NavTop>
    <div class="container">
      <div class="tip">如下物料被检验判定为不合格，有待处理：</div>
      <div class="ui-cells">
        <a class="ui-cell" v-on:click="jump(item.id, 1)" v-for="(item, index) in waitCompromiseApprove" v-bind:key="index">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <div>{{item.code}}</div>
              <div>{{item.name}}</div>
              <div>申请入库的数量  {{item.quantity_plan}}{{item.unit}}  到货日期 {{item.arrive_date | formatDay('YYYY-MM-DD')}}</div>
            </div>
          </div>
          <div class="ui-cell__ft"></div>
        </a>
      </div>
    </div>
  </div>
</template>

<style>
  #unqualifiedHandleSkill .tabs-tab{
    width: 33%;
  }
  #unqualifiedHandleSkill .ui-cell{
    font-size: 12px;
  }
</style>

<script>
export default {
  name: 'unqualifiedHandleSkill',
  data () {
    return {
      pid: 0,
      isBackHome: false,
      listenersUid: [],
      waitCompromiseApprove: []
    }
  },
  created: function () {
    // 从路由获取父级菜单id
    let that = this
    this.axios.post('../../../inStock/mtList', {
      oid: that.sphdSocket.user.oid,
      state: '(5)',
      users: 'wb'
    }).then(function (response) {
      let list = response.data.data
      that.waitCompromiseApprove = list
    })
  },
  methods: {
    jump: function (id, tab) {
      this.$store.dispatch('setMaterialParam', {
        user: 'wb',
        tab: tab
      })
      this.$router.push({
        path: `/materialDetail/${id}`
      })
    },
    search: function () {
      this.$router.push({
        path: `/purchaseQuery/5`
      })
    }
  }
}
</script>
