package cn.sphd.miners.modules.material.entity;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Date;

/**
 * Created by Administrator on 2016/9/19.
 */
@Entity(name="MtQuality")
@Table(name="t_mt_quality")
public class MtQuality {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="experimental_method"  , length=255 , nullable=true , unique=false)
    private String experimentalMethod;

    @Column(name = "expiration_date", length = 100  , nullable=true , unique=false)
    private String expirationDate;

    @Column(name="expiration"   , nullable=true , unique=false)
    private Date expiration;

    @Column(name="inspection_standard"  , length=100 , nullable=true , unique=false)
    private String inspectionStandard;

    @Column(name="inspection_operation"  , length=255 , nullable=true , unique=false)
    private String inspectionOperation;

    @Column(name="inspection_instructions"  , length=255 , nullable=true , unique=false)
    private String inspectionInstructions;

    @Column(name="protocol_sn"  , length=255 , nullable=true , unique=false)
    private String protocolSn;

    @Column(name="outer_record"  , length=255 , nullable=true , unique=false)
    private String outerRecord;

    @Column(name="sealed_samples_sn"  , length=255 , nullable=true , unique=false)
    private String sealedSamplesSn;

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Timestamp createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Timestamp updateDate;

    @ManyToOne (fetch=FetchType.LAZY , optional=false)
    @JoinColumn(name="material", referencedColumnName = "id" , nullable=false , unique=false , insertable=true, updatable=true)
    private MtBase material;

    @Column(name="material"  , nullable=false , unique=false, insertable=false, updatable=false)
    private Integer material_;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getExperimentalMethod() {
        return experimentalMethod;
    }

    public void setExperimentalMethod(String experimentalMethod) {
        this.experimentalMethod = experimentalMethod;
    }

    public Date getExpiration() {
        return expiration;
    }

    public void setExpiration(Date expiration) {
        this.expiration = expiration;
    }

    public String getInspectionStandard() {
        return inspectionStandard;
    }

    public void setInspectionStandard(String inspectionStandard) {
        this.inspectionStandard = inspectionStandard;
    }

    public String getInspectionOperation() {
        return inspectionOperation;
    }

    public void setInspectionOperation(String inspectionOperation) {
        this.inspectionOperation = inspectionOperation;
    }

    public String getInspectionInstructions() {
        return inspectionInstructions;
    }

    public void setInspectionInstructions(String inspectionInstructions) {
        this.inspectionInstructions = inspectionInstructions;
    }

    public String getProtocolSn() {
        return protocolSn;
    }

    public void setProtocolSn(String protocolSn) {
        this.protocolSn = protocolSn;
    }

    public String getOuterRecord() {
        return outerRecord;
    }

    public void setOuterRecord(String outerRecord) {
        this.outerRecord = outerRecord;
    }

    public String getSealedSamplesSn() {
        return sealedSamplesSn;
    }

    public void setSealedSamplesSn(String sealedSamplesSn) {
        this.sealedSamplesSn = sealedSamplesSn;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Timestamp getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Timestamp updateDate) {
        this.updateDate = updateDate;
    }

    public MtBase getMaterial() {
        return material;
    }

    public void setMaterial(MtBase material) {
        this.material = material;
    }

    public Integer getMaterial_() {
        return material_;
    }

    public void setMaterial_(Integer material_) {
        this.material_ = material_;
    }

    public String getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(String expirationDate) {
        this.expirationDate = expirationDate;
    }
}
