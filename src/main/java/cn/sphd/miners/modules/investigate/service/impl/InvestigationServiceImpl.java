package cn.sphd.miners.modules.investigate.service.impl;


import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.investigate.dao.*;
import cn.sphd.miners.modules.investigate.dto.*;
import cn.sphd.miners.modules.investigate.entity.*;
import cn.sphd.miners.modules.investigate.service.InvestigationService;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.format.DateTimeFormatter;
import java.util.*;


/**
 * @ClassName TrainServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/2/3 8:13
 * @Version 1.0
 */
@Service("InvestigationService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class InvestigationServiceImpl implements InvestigationService{
    @Autowired
    InvestigateSubjectDao investigateSubjectDao;
    @Autowired
    InvestigateSubjectTagDao investigateSubjectTagDao;
    @Autowired
    InvestigateQuestionDao investigateQuestionDao;
    @Autowired
    InvestigateQuestionKeyDao investigateQuestionKeyDao;
    @Autowired
    InvestigatePublishDao investigatePublishDao;

    @Override
    public List<InvestigateSubject> selectInvestigateSubjectList(User user, PageInfo pageInfo, Integer status,String month) {
        Date beginTime=new Date();
        Date endTime=new Date();
        if(!"".equals(month)&& month!=null) {
            month = month + "-01";
            String formatter = "yyyy-MM-dd";
            beginTime = NewDateUtils.dateFromString(month, formatter);
            endTime = NewDateUtils.getLastTimeOfMonth(beginTime);
        }
        String hql="";
        Map<String, Object> params = new HashMap<>();
        params.put("org", user.getOid());
        params.put("status", status);

        if(status==0) {
            hql = "from InvestigateSubject where org=:org and enabled=:status and enabledTime  between :beginTime and :endTime ORDER BY createDate desc";
            params.put("beginTime", beginTime);
            params.put("endTime", endTime);
        }else
            hql = "from InvestigateSubject where org=:org and enabled=:status ORDER BY createDate desc";
        List<InvestigateSubject> list = investigateSubjectDao.getListByHQLWithNamedParams(hql,params,pageInfo);
        return list;
    }

    @Override
    public int countInvestigateSubject(User user, Integer status, String month) {
        Date beginTime=new Date();
        Date endTime=new Date();
        if(!"".equals(month)&& month!=null) {
            month = month + "-01";
            String formatter = "yyyy-MM-dd";
            beginTime = NewDateUtils.dateFromString(month, formatter);
            endTime = NewDateUtils.getLastTimeOfMonth(beginTime);
        }
        String hql="";
        Map<String, Object> params = new HashMap<>();
        params.put("org", user.getOid());
        params.put("status", status);
        if(status==0) {
            hql = "from InvestigateSubject where org=:org and enabled=:status and enabledTime  between :beginTime and :endTime ORDER BY createDate desc";
            params.put("beginTime", beginTime);
            params.put("endTime", endTime);
        }else
            hql = "from InvestigateSubject where org=:org and enabled=:status ORDER BY createDate desc";
        List<InvestigateSubject> list = investigateSubjectDao.getListByHQLWithNamedParams(hql,params);
        return list.size();
    }

    @Override
    public List<InvestigateSubject> selectSubjectByNameList(User user, PageInfo pageInfo, String keyword) {
        String hql = "from InvestigateSubject where org="+user.getOid()+" and enabled=1 and title like '%"+keyword+"%' ORDER BY createDate desc";
        List<InvestigateSubject> list = investigateSubjectDao.getListByHQLWithNamedParams(hql,null,pageInfo);
        return list;
    }

    @Override
    public int countSubjectByKeyword(User user, String keyword) {
        String hql = "from InvestigateSubject where org="+user.getOid()+" and enabled=1 and title like '%"+keyword+"%'";
        List<InvestigateSubject> list = investigateSubjectDao.getListByHQLWithNamedParams(hql,null);
        return list.size();
    }

    @Override
    public RespInvestigateStopByMonthList selectSubjectStopByMonthList(User user, String year) {
        RespInvestigateStopByMonthList respInvestigateStopByMonthList=new RespInvestigateStopByMonthList();
        if(year==null||"".equals(year))
        {
            year=String.valueOf(NewDateUtils.getYear(new Date()));
        }
        String sql="SELECT DATE_FORMAT(enabled_time,'%Y-%m') AS `month`,count(*) AS sum FROM `t_investigate_subject` WHERE LEFT(enabled_time,4)='"+year+"' AND org="+user.getOid()+" AND enabled=0 GROUP BY DATE_FORMAT(enabled_time,'%Y-%m')";
        List<Object[]> objectList= investigateSubjectDao.getObjectListBySQL(sql);
        int sum=0;
        List<RespInvestigateStopByMonth> list=new ArrayList<>();
        if (objectList.size() > 0 && !objectList.isEmpty()) {
            for (int i = 0; i <objectList.size(); i++) {
                RespInvestigateStopByMonth respInvestigateStopByMonth =new RespInvestigateStopByMonth();
                //把每个对象数据通过JSON.toJSONString 转化为 JSON字符串
                String s = JSON.toJSONString(objectList.get(i));
                JSONArray objects = JSONArray.parseArray(s);
                respInvestigateStopByMonth.setMonth(objects.getString(0));
                respInvestigateStopByMonth.setSum(Integer.valueOf(objects.getString(1)));
                list.add(respInvestigateStopByMonth);
                sum=sum+respInvestigateStopByMonth.getSum();
            }
        }
        respInvestigateStopByMonthList.setRespInvestigateStopByMonthList(list);
        respInvestigateStopByMonthList.setSum(sum);
        return respInvestigateStopByMonthList;
    }

    @Override
    public int addSubject(User user, ReqInvestigateSubject reqInvestigateSubject) {
        InvestigateSubject investigateSubject= new InvestigateSubject();
        investigateSubject.setTitle(reqInvestigateSubject.getTitle());
        investigateSubject.setEnabled(1);
        investigateSubject.setHeader(reqInvestigateSubject.getHeader());
        investigateSubject.setPreface(reqInvestigateSubject.getPreface());
        investigateSubject.setOrg(user.getOid());
        investigateSubject.setCreator(user.getUserID());
        investigateSubject.setCreateDate(new Date());
        investigateSubject.setCreateName(user.getUserName());
        investigateSubject.setQuestionsNum(0);
        investigateSubject.setRequiredNum(0);
        investigateSubjectDao.save(investigateSubject);
        List<RespInvestigateQuestion> respInvestigateQuestionList =new ArrayList<>();
        List<RespInvestigateSubjectTag> respInvestigateSubjectTagList=new ArrayList<>();
        if(reqInvestigateSubject.getQuestionListJson()!=null&&!"".equals(reqInvestigateSubject.getQuestionListJson()))
            respInvestigateQuestionList = JSONArray.parseArray(reqInvestigateSubject.getQuestionListJson(), RespInvestigateQuestion.class);
        if(reqInvestigateSubject.getSubjectTagListJson()!=null&&!"".equals(reqInvestigateSubject.getSubjectTagListJson()))
            respInvestigateSubjectTagList = JSONArray.parseArray(reqInvestigateSubject.getSubjectTagListJson(), RespInvestigateSubjectTag.class);
        if(respInvestigateSubjectTagList != null && respInvestigateSubjectTagList.size() > 1){
            for(int i = 0; i < respInvestigateSubjectTagList.size() - 1; i++){
                // 初始化一个布尔值
                boolean flag = true;
                for(int j = 0; j < respInvestigateSubjectTagList.size() - i - 1 ; j++){
                    if(respInvestigateSubjectTagList.get(j).getBeginOrder() > respInvestigateSubjectTagList.get(j+1).getBeginOrder() ){
                        // 调换
                        RespInvestigateSubjectTag respInvestigateSubjectTag = new RespInvestigateSubjectTag();
                        BeanUtils.copyProperties(respInvestigateSubjectTagList.get(j),respInvestigateSubjectTag);
                        BeanUtils.copyProperties(respInvestigateSubjectTagList.get(j+1),respInvestigateSubjectTagList.get(j));
                        BeanUtils.copyProperties(respInvestigateSubjectTag,respInvestigateSubjectTagList.get(j+1));
                        // 改变flag
                        flag = false;
                    }
                }
                if(flag){
                    break;
                }
            }
        }
        if(respInvestigateSubjectTagList.size()==0||respInvestigateSubjectTagList.get(0).getBeginOrder()!=1)
        {
            InvestigateSubjectTag investigateSubjectTag=new InvestigateSubjectTag();
            investigateSubjectTag.setOrg(investigateSubject.getOrg());
            investigateSubjectTag.setSubject(investigateSubject.getId());
            investigateSubjectTag.setBeginOrder(1);
            investigateSubjectTag.setCreator(user.getUserID());
            investigateSubjectTag.setCreateDate(new Date());
            investigateSubjectTag.setCreateName(user.getUserName());
            investigateSubjectTagDao.save(investigateSubjectTag);
        }
        for(int i=0;i<respInvestigateSubjectTagList.size();i++)
        {
            RespInvestigateSubjectTag respInvestigateSubjectTag=respInvestigateSubjectTagList.get(i);
            InvestigateSubjectTag investigateSubjectTag=new InvestigateSubjectTag();
            investigateSubjectTag.setOrg(investigateSubject.getOrg());
            investigateSubjectTag.setSubject(investigateSubject.getId());
            investigateSubjectTag.setName(respInvestigateSubjectTag.getName());
            investigateSubjectTag.setBeginOrder(respInvestigateSubjectTag.getBeginOrder());
            investigateSubjectTag.setCreator(user.getUserID());
            investigateSubjectTag.setCreateDate(new Date());
            investigateSubjectTag.setCreateName(user.getUserName());
            investigateSubjectTagDao.save(investigateSubjectTag);
        }
        String hqlTag = "from InvestigateSubjectTag where subject="+investigateSubject.getId();
        List<InvestigateSubjectTag> investigateSubjectTagList = investigateSubjectTagDao.getListByHQLWithNamedParams(hqlTag,null);
        for(int i=0;i<investigateSubjectTagList.size();i++) {
            if (i <investigateSubjectTagList.size() - 1)
                investigateSubjectTagList.get(i).setEndOrder(investigateSubjectTagList.get(i + 1).getBeginOrder() - 1);
            else
                investigateSubjectTagList.get(i).setEndOrder(respInvestigateQuestionList.size());
            investigateSubjectTagDao.update(investigateSubjectTagList.get(i));
        }
        int requiredNum=0;
        for(RespInvestigateQuestion respInvestigateQuestion:respInvestigateQuestionList) {
            InvestigateQuestion investigateQuestion=new InvestigateQuestion();
            investigateQuestion.setType(respInvestigateQuestion.getType());
            investigateQuestion.setOrg(user.getOid());
            investigateQuestion.setSubject(investigateSubject.getId());
            investigateQuestion.setEnabled(1);
            investigateQuestion.setIsRequired(respInvestigateQuestion.getIsRequired());
            investigateQuestion.setIsShow(respInvestigateQuestion.getIsShow());
            if(respInvestigateQuestion.getIsMultiple()!=null)
            investigateQuestion.setIsMultiple(respInvestigateQuestion.getIsMultiple());
            if(respInvestigateQuestion.getIsAttachment()!=null)
            investigateQuestion.setIsAttachment(respInvestigateQuestion.getIsAttachment());
            if(respInvestigateQuestion.getAttachTab()!=null)
            investigateQuestion.setAttachTab(respInvestigateQuestion.getAttachTab());
            if(respInvestigateQuestion.getSpecialModel()!=null)
            investigateQuestion.setSpecialModel(respInvestigateQuestion.getSpecialModel());
            if(respInvestigateQuestion.getSpecialTab()!=null)
            investigateQuestion.setSpecialTab(respInvestigateQuestion.getSpecialTab());
            investigateQuestion.setContent(respInvestigateQuestion.getContent());
            investigateQuestion.setOrders(respInvestigateQuestion.getOrders());
            investigateQuestion.setCreator(user.getUserID());
            investigateQuestion.setCreateDate(new Date());
            investigateQuestion.setCreateName(user.getUserName());
            if(respInvestigateQuestion.getIsRequired()==1)
                requiredNum++;
            for (InvestigateSubjectTag investigateSubjectTag:investigateSubjectTagList){
                if(investigateQuestion.getOrders()>=investigateSubjectTag.getBeginOrder()&&investigateQuestion.getOrders()<=investigateSubjectTag.getEndOrder())
                {
                    investigateQuestion.setTag(investigateSubjectTag.getId());
                    break;
                }
            }
            investigateQuestionDao.save(investigateQuestion);
            if(!"1".equals(investigateQuestion.getType())&&respInvestigateQuestion.getInvestigateQuestionKeyJson()!=null){
                List<InvestigateQuestionKey> investigateQuestionKeyList = JSONArray.parseArray(respInvestigateQuestion.getInvestigateQuestionKeyJson(), InvestigateQuestionKey.class);
                for(InvestigateQuestionKey investigateQuestionKey:investigateQuestionKeyList){
                    investigateQuestionKey.setOrg(investigateQuestion.getOrg());
                    investigateQuestionKey.setSubject(investigateQuestion.getSubject());
                    investigateQuestionKey.setQuestion(investigateQuestion.getId());
                    investigateQuestionKey.setEnabled(1);
                    investigateQuestionKey.setCreator(user.getUserID());
                    investigateQuestionKey.setCreateDate(new Date());
                    investigateQuestionKey.setCreateName(user.getUserName());
                    investigateQuestionKeyDao.save(investigateQuestionKey);
                }
            }
        }
        investigateSubject.setQuestionsNum(respInvestigateQuestionList.size());
        investigateSubject.setRequiredNum(requiredNum);
        return 1;
    }

    @Override
    public RespInvestigateSubject selectSubject(int id) {
        RespInvestigateSubject respInvestigateSubject=new RespInvestigateSubject();
        InvestigateSubject investigateSubject=investigateSubjectDao.get(id);
        BeanUtils.copyProperties(investigateSubject,respInvestigateSubject);
        List<RespInvestigateSubjectTag> respInvestigateSubjectTagList=new ArrayList<>();
        String hqlTag = "from InvestigateSubjectTag where subject="+investigateSubject.getId();
        List<InvestigateSubjectTag> investigateSubjectTagList = investigateSubjectTagDao.getListByHQLWithNamedParams(hqlTag,null);
        for(InvestigateSubjectTag investigateSubjectTag:investigateSubjectTagList)
        {
            RespInvestigateSubjectTag respInvestigateSubjectTag=new RespInvestigateSubjectTag();
            BeanUtils.copyProperties(investigateSubjectTag,respInvestigateSubjectTag);
            List<RespInvestigateQuestion> respInvestigateQuestionList=new ArrayList<>();
            String hqlQuestion = "from InvestigateQuestion where tag="+investigateSubjectTag.getId();
            List<InvestigateQuestion> investigateQuestionList = investigateQuestionDao.getListByHQLWithNamedParams(hqlQuestion,null);
            for(InvestigateQuestion investigateQuestion:investigateQuestionList)
            {
                RespInvestigateQuestion respInvestigateQuestion =new RespInvestigateQuestion();
                BeanUtils.copyProperties(investigateQuestion,respInvestigateQuestion);
                String hqlQuestionKey = "from InvestigateQuestionKey where question="+investigateQuestion.getId();
                List<InvestigateQuestionKey> investigateQuestionKeyList = investigateQuestionKeyDao.getListByHQLWithNamedParams(hqlQuestionKey,null);
                respInvestigateQuestion.setInvestigateQuestionKeyList(investigateQuestionKeyList);
                respInvestigateQuestionList.add(respInvestigateQuestion);
            }
            respInvestigateSubjectTag.setInvestigateQuestionList(respInvestigateQuestionList);
            respInvestigateSubjectTagList.add(respInvestigateSubjectTag);
        }
        respInvestigateSubject.setRespInvestigateSubjectTagList(respInvestigateSubjectTagList);
        return respInvestigateSubject;
    }

    @Override
    public int updateEnabledSubject(int id,int type,User user) {
        InvestigateSubject investigateSubject=investigateSubjectDao.get(id);
        investigateSubject.setEnabled(type);
        investigateSubject.setEnabledTime(new Date());
        investigateSubject.setUpdator(user.getUserID());
        investigateSubject.setUpdateDate(new Date());
        investigateSubject.setUpdateName(user.getUserName());
        investigateSubjectDao.update(investigateSubject);
        return 1;
    }

    @Override
    public int updateSubjectOrTag(User user, ReqInvestigateSubject reqInvestigateSubject) {
        InvestigateSubject investigateSubject=investigateSubjectDao.get(reqInvestigateSubject.getId());
        investigateSubject.setTitle(reqInvestigateSubject.getTitle());
        investigateSubject.setHeader(reqInvestigateSubject.getHeader());
        investigateSubject.setPreface(reqInvestigateSubject.getPreface());
        investigateSubject.setUpdateDate(user.getUpdateDate());
        investigateSubject.setUpdateName(user.getUpdateName());
        investigateSubject.setUpdator(user.getUpdator());

        List<RespInvestigateQuestion> respInvestigateQuestionList=new ArrayList<>();
        List<RespInvestigateSubjectTag> respInvestigateSubjectTagList=new ArrayList<>();
        if(reqInvestigateSubject.getQuestionListJson()!=null)
            respInvestigateQuestionList = JSONArray.parseArray(reqInvestigateSubject.getQuestionListJson(), RespInvestigateQuestion.class);
        if(reqInvestigateSubject.getTagStatus()==1&&reqInvestigateSubject.getSubjectTagListJson()!=null&&!"".equals(reqInvestigateSubject.getSubjectTagListJson()))
            respInvestigateSubjectTagList = JSONArray.parseArray(reqInvestigateSubject.getSubjectTagListJson(), RespInvestigateSubjectTag.class);
        for(RespInvestigateQuestion respInvestigateQuestion: respInvestigateQuestionList)
        {
            InvestigateQuestion investigateQuestion=investigateQuestionDao.get(respInvestigateQuestion.getId());
            investigateQuestion.setIsRequired(respInvestigateQuestion.getIsRequired());
            investigateQuestionDao.update(investigateQuestion);
        }
        String hqlQuestion = "from InvestigateQuestion where subject=" + reqInvestigateSubject.getId();
        List<InvestigateQuestion> investigateQuestionList = investigateQuestionKeyDao.getListByHQLWithNamedParams(hqlQuestion, null);

        if(reqInvestigateSubject.getTagStatus()==1) {
            String hqlSubjectTag = "from InvestigateSubjectTag where subject=" + reqInvestigateSubject.getId();
            List<InvestigateSubjectTag> investigateSubjectTagDeleteList = investigateQuestionKeyDao.getListByHQLWithNamedParams(hqlSubjectTag, null);
            investigateSubjectTagDao.deleteAll(investigateSubjectTagDeleteList);
            if (respInvestigateSubjectTagList != null && respInvestigateSubjectTagList.size() > 1) {
                for (int i = 0; i < respInvestigateSubjectTagList.size() - 1; i++) {
                    // 初始化一个布尔值
                    boolean flag = true;
                    for (int j = 0; j < respInvestigateSubjectTagList.size() - i - 1; j++) {
                        if (respInvestigateSubjectTagList.get(j).getBeginOrder() > respInvestigateSubjectTagList.get(j + 1).getBeginOrder()) {
                            // 调换
                            RespInvestigateSubjectTag respInvestigateSubjectTag = new RespInvestigateSubjectTag();
                            BeanUtils.copyProperties(respInvestigateSubjectTagList.get(j),respInvestigateSubjectTag);
                            BeanUtils.copyProperties(respInvestigateSubjectTagList.get(j + 1), respInvestigateSubjectTagList.get(j));
                            BeanUtils.copyProperties(respInvestigateSubjectTag, respInvestigateSubjectTagList.get(j + 1));
                            // 改变flag
                            flag = false;
                        }
                    }
                    if (flag) {
                        break;
                    }
                }
            }
            if (respInvestigateSubjectTagList.size() == 0 || respInvestigateSubjectTagList.get(0).getBeginOrder() != 1) {
                InvestigateSubjectTag investigateSubjectTag = new InvestigateSubjectTag();
                investigateSubjectTag.setOrg(investigateSubject.getOrg());
                investigateSubjectTag.setSubject(investigateSubject.getId());
                investigateSubjectTag.setBeginOrder(1);
                investigateSubjectTag.setCreator(user.getUserID());
                investigateSubjectTag.setCreateDate(new Date());
                investigateSubjectTag.setCreateName(user.getUserName());
                investigateSubjectTagDao.save(investigateSubjectTag);
            }
            for (int i = 0; i < respInvestigateSubjectTagList.size(); i++) {
                RespInvestigateSubjectTag respInvestigateSubjectTag = respInvestigateSubjectTagList.get(i);
                InvestigateSubjectTag investigateSubjectTag = new InvestigateSubjectTag();
                investigateSubjectTag.setOrg(investigateSubject.getOrg());
                investigateSubjectTag.setSubject(investigateSubject.getId());
                investigateSubjectTag.setName(respInvestigateSubjectTag.getName());
                investigateSubjectTag.setBeginOrder(respInvestigateSubjectTag.getBeginOrder());
                investigateSubjectTag.setCreator(user.getUserID());
                investigateSubjectTag.setCreateDate(new Date());
                investigateSubjectTag.setCreateName(user.getUserName());
                investigateSubjectTagDao.save(investigateSubjectTag);
            }
            String hqlTag = "from InvestigateSubjectTag where subject=" + investigateSubject.getId();
            List<InvestigateSubjectTag> investigateSubjectTagList = investigateSubjectTagDao.getListByHQLWithNamedParams(hqlTag, null);
            for (int i = 0; i < investigateSubjectTagList.size(); i++) {
                if (i < investigateSubjectTagList.size() - 1)
                    investigateSubjectTagList.get(i).setEndOrder(investigateSubjectTagList.get(i + 1).getBeginOrder() - 1);
                else
                    investigateSubjectTagList.get(i).setEndOrder(respInvestigateQuestionList.size());
                investigateSubjectTagDao.update(investigateSubjectTagList.get(i));
            }
            for (InvestigateQuestion investigateQuestion : investigateQuestionList) {
                for (InvestigateSubjectTag investigateSubjectTag : investigateSubjectTagList) {
                    if (investigateQuestion.getOrders() >= investigateSubjectTag.getBeginOrder() && investigateQuestion.getOrders() <= investigateSubjectTag.getEndOrder()) {
                        investigateQuestion.setTag(investigateSubjectTag.getId());
                        break;
                    }
                }
            }
        }
        int requiredNum = 0;
        for (InvestigateQuestion investigateQuestion : investigateQuestionList) {
            if (investigateQuestion.getIsRequired() == 1)
                requiredNum++;
        }
        investigateSubject.setRequiredNum(requiredNum);
        investigateSubjectDao.save(investigateSubject);
        String publshSql="from InvestigatePublish  where subject="+reqInvestigateSubject.getId();
        List<InvestigatePublish> investigatePublishList=investigatePublishDao.getListByHQLWithNamedParams(publshSql,null);
        for(InvestigatePublish investigatePublish:investigatePublishList){
            investigatePublish.setName(reqInvestigateSubject.getTitle());
            investigatePublishDao.update(investigatePublish);
        }
        return 1;
    }

    @Override
    public int updateSubject(User user, ReqInvestigateSubject reqInvestigateSubject) {
        String hqlQuestionKey="from InvestigateQuestionKey where subject="+reqInvestigateSubject.getId();
        List<InvestigateQuestionKey> investigateQuestionKeydeleteList = investigateQuestionKeyDao.getListByHQLWithNamedParams(hqlQuestionKey,null);
        investigateQuestionKeyDao.deleteAll(investigateQuestionKeydeleteList);
        String hqlQuestion="from InvestigateQuestion where subject="+reqInvestigateSubject.getId();
        List<InvestigateQuestion> investigateQuestiondeleteList = investigateQuestionKeyDao.getListByHQLWithNamedParams(hqlQuestion,null);
        investigateQuestionDao.deleteAll(investigateQuestiondeleteList);
        String hqlSubjectTag="from InvestigateSubjectTag where subject="+reqInvestigateSubject.getId();
        List<InvestigateSubjectTag> investigateSubjectTagdeleteList = investigateQuestionKeyDao.getListByHQLWithNamedParams(hqlSubjectTag,null);
        investigateSubjectTagDao.deleteAll(investigateSubjectTagdeleteList);
        InvestigateSubject investigateSubject=investigateSubjectDao.get(reqInvestigateSubject.getId());
        investigateSubject.setTitle(reqInvestigateSubject.getTitle());
        investigateSubject.setHeader(reqInvestigateSubject.getHeader());
        investigateSubject.setPreface(reqInvestigateSubject.getPreface());
        investigateSubject.setUpdateDate(user.getUpdateDate());
        investigateSubject.setUpdateName(user.getUpdateName());
        investigateSubject.setUpdator(user.getUpdator());
        investigateSubjectDao.update(investigateSubject);
        String publshSql="from InvestigatePublish  where subject="+reqInvestigateSubject.getId();
        List<InvestigatePublish> investigatePublishList=investigatePublishDao.getListByHQLWithNamedParams(publshSql,null);
        for(InvestigatePublish investigatePublish:investigatePublishList){
            investigatePublish.setName(reqInvestigateSubject.getTitle());
            investigatePublishDao.update(investigatePublish);
        }
        List<RespInvestigateQuestion> respInvestigateQuestionList=new ArrayList<>();
        List<RespInvestigateSubjectTag> respInvestigateSubjectTagList=new ArrayList<>();
        if(reqInvestigateSubject.getQuestionListJson()!=null)
            respInvestigateQuestionList = JSONArray.parseArray(reqInvestigateSubject.getQuestionListJson(), RespInvestigateQuestion.class);
        if(reqInvestigateSubject.getSubjectTagListJson()!=null)
        respInvestigateSubjectTagList = JSONArray.parseArray(reqInvestigateSubject.getSubjectTagListJson(), RespInvestigateSubjectTag.class);
        if(respInvestigateSubjectTagList != null && respInvestigateSubjectTagList.size() > 1){
            for(int i = 0; i < respInvestigateSubjectTagList.size() - 1; i++){
                // 初始化一个布尔值
                boolean flag = true;
                for(int j = 0; j < respInvestigateSubjectTagList.size() - i - 1 ; j++){
                    if(respInvestigateSubjectTagList.get(j).getBeginOrder() > respInvestigateSubjectTagList.get(j+1).getBeginOrder() ){
                        // 调换
                        RespInvestigateSubjectTag respInvestigateSubjectTag = new RespInvestigateSubjectTag();
                        BeanUtils.copyProperties(respInvestigateSubjectTagList.get(j),respInvestigateSubjectTag);
                        BeanUtils.copyProperties(respInvestigateSubjectTagList.get(j+1),respInvestigateSubjectTagList.get(j));
                        BeanUtils.copyProperties(respInvestigateSubjectTag,respInvestigateSubjectTagList.get(j+1));
                        // 改变flag
                        flag = false;
                    }
                }
                if(flag){
                    break;
                }
            }
        }
        if(respInvestigateSubjectTagList.size()==0||respInvestigateSubjectTagList.get(0).getBeginOrder()!=1)
        {
            InvestigateSubjectTag investigateSubjectTag=new InvestigateSubjectTag();
            investigateSubjectTag.setOrg(investigateSubject.getOrg());
            investigateSubjectTag.setSubject(investigateSubject.getId());
            investigateSubjectTag.setBeginOrder(1);
            investigateSubjectTag.setCreator(user.getUserID());
            investigateSubjectTag.setCreateDate(new Date());
            investigateSubjectTag.setCreateName(user.getUserName());
            investigateSubjectTagDao.save(investigateSubjectTag);
        }
        for(int i=0;i<respInvestigateSubjectTagList.size();i++)
        {
            RespInvestigateSubjectTag respInvestigateSubjectTag=respInvestigateSubjectTagList.get(i);
            InvestigateSubjectTag investigateSubjectTag=new InvestigateSubjectTag();
            investigateSubjectTag.setOrg(investigateSubject.getOrg());
            investigateSubjectTag.setSubject(investigateSubject.getId());
            investigateSubjectTag.setName(respInvestigateSubjectTag.getName());
            investigateSubjectTag.setBeginOrder(respInvestigateSubjectTag.getBeginOrder());
            investigateSubjectTag.setCreator(user.getUserID());
            investigateSubjectTag.setCreateDate(new Date());
            investigateSubjectTag.setCreateName(user.getUserName());
            investigateSubjectTagDao.save(investigateSubjectTag);
        }
        String hqlTag = "from InvestigateSubjectTag where subject="+investigateSubject.getId();
        List<InvestigateSubjectTag> investigateSubjectTagList = investigateSubjectTagDao.getListByHQLWithNamedParams(hqlTag,null);
        for(int i=0;i<investigateSubjectTagList.size();i++) {
            if (i <investigateSubjectTagList.size() - 1)
                investigateSubjectTagList.get(i).setEndOrder(investigateSubjectTagList.get(i + 1).getBeginOrder() - 1);
            else
                investigateSubjectTagList.get(i).setEndOrder(respInvestigateQuestionList.size());
            investigateSubjectTagDao.update(investigateSubjectTagList.get(i));
        }
        int requiredNum=0;
        for(RespInvestigateQuestion respInvestigateQuestion:respInvestigateQuestionList) {
            InvestigateQuestion investigateQuestion=new InvestigateQuestion();
            investigateQuestion.setType(respInvestigateQuestion.getType());
            investigateQuestion.setOrg(user.getOid());
            investigateQuestion.setSubject(investigateSubject.getId());
            investigateQuestion.setEnabled(1);
            investigateQuestion.setIsRequired(respInvestigateQuestion.getIsRequired());
            investigateQuestion.setIsShow(respInvestigateQuestion.getIsShow());
            if(respInvestigateQuestion.getIsMultiple()!=null)
                investigateQuestion.setIsMultiple(respInvestigateQuestion.getIsMultiple());
            if(respInvestigateQuestion.getIsAttachment()!=null)
                investigateQuestion.setIsAttachment(respInvestigateQuestion.getIsAttachment());
            if(respInvestigateQuestion.getAttachTab()!=null)
                investigateQuestion.setAttachTab(respInvestigateQuestion.getAttachTab());
            if(respInvestigateQuestion.getSpecialModel()!=null)
                investigateQuestion.setSpecialModel(respInvestigateQuestion.getSpecialModel());
            if(respInvestigateQuestion.getSpecialTab()!=null)
                investigateQuestion.setSpecialTab(respInvestigateQuestion.getSpecialTab());
            investigateQuestion.setContent(respInvestigateQuestion.getContent());
            investigateQuestion.setOrders(respInvestigateQuestion.getOrders());
            investigateQuestion.setCreator(user.getUserID());
            investigateQuestion.setCreateDate(new Date());
            investigateQuestion.setCreateName(user.getUserName());
            if(respInvestigateQuestion.getIsRequired()==1)
                requiredNum++;
            for (InvestigateSubjectTag investigateSubjectTag:investigateSubjectTagList){
                if(investigateQuestion.getOrders()>=investigateSubjectTag.getBeginOrder()&&investigateQuestion.getOrders()<=investigateSubjectTag.getEndOrder())
                {
                    investigateQuestion.setTag(investigateSubjectTag.getId());
                    break;
                }
            }
            investigateQuestionDao.save(investigateQuestion);
            if(!"1".equals(investigateQuestion.getType())&&respInvestigateQuestion.getInvestigateQuestionKeyJson()!=null){
                List<InvestigateQuestionKey> investigateQuestionKeyList=new ArrayList<>();
                if(respInvestigateQuestion.getInvestigateQuestionKeyJson()!=null)
                    investigateQuestionKeyList  = JSONArray.parseArray(respInvestigateQuestion.getInvestigateQuestionKeyJson(), InvestigateQuestionKey.class);
                for(InvestigateQuestionKey investigateQuestionKey:investigateQuestionKeyList){
                    investigateQuestionKey.setOrg(investigateQuestion.getOrg());
                    investigateQuestionKey.setSubject(investigateQuestion.getSubject());
                    investigateQuestionKey.setQuestion(investigateQuestion.getId());
                    investigateQuestionKey.setEnabled(1);
                    investigateQuestionKey.setCreator(user.getUserID());
                    investigateQuestionKey.setCreateDate(new Date());
                    investigateQuestionKey.setCreateName(user.getUserName());
                    investigateQuestionKeyDao.save(investigateQuestionKey);
                }
            }
        }
        investigateSubject.setQuestionsNum(respInvestigateQuestionList.size());
        investigateSubject.setRequiredNum(requiredNum);
        return 1;
    }


    @Override
    public int deleteSubjectJudge(int id) {
        int status=0;
        String hql = "from InvestigatePublish where subject="+id;
        List<InvestigatePublish> investigatePublishList= investigatePublishDao.getListByHQLWithNamedParams(hql,null);
        if(investigatePublishList.size()>0)
            status=-1;
        else
            status=1;
        return status;
    }

    @Override
    public int deleteSubject(int id) {
        int status=0;
        String hql = "from InvestigatePublish where subject="+id;
        List<InvestigatePublish> investigatePublishList= investigatePublishDao.getListByHQLWithNamedParams(hql,null);
        if(investigatePublishList.size()>0)
            status=-1;
        else {
            String hqlQuestionKey="from InvestigateQuestionKey where subject="+id;
            List<InvestigateQuestionKey> investigateQuestionKeyList = investigateQuestionKeyDao.getListByHQLWithNamedParams(hqlQuestionKey,null);
            investigateQuestionKeyDao.deleteAll(investigateQuestionKeyList);
            String hqlQuestion="from InvestigateQuestion where subject="+id;
            List<InvestigateQuestion> investigateQuestionList = investigateQuestionKeyDao.getListByHQLWithNamedParams(hqlQuestion,null);
            investigateQuestionDao.deleteAll(investigateQuestionList);
            String hqlSubjectTag="from InvestigateSubjectTag where subject="+id;
            List<InvestigateSubjectTag> investigateSubjectTagList = investigateQuestionKeyDao.getListByHQLWithNamedParams(hqlSubjectTag,null);
            investigateSubjectTagDao.deleteAll(investigateSubjectTagList);
            investigateSubjectDao.deleteById(id);
            status=1;
        }
        return status;
    }
}
