<!--财会首页点击本日按钮后的页面-->
<template>
  <div class="head">
    <div style="width: 100%">
      <PageHeader title="财会" :runMyBackFun="true" @myBackFun="backFun" :showBack="true" :showClose="true" @mycloseFun="closeFun"></PageHeader>
    </div>
  </div>
  <div id="today">
  <p class="margin-10-15">{{ todayTime }}</p>
  <div class="hr_gray"></div>
  <div class="current-balance">
    <p>当前余额<span>{{ allBalance }}</span></p>
  </div>
  <div class="hr_gray"></div>
  <div class="flex">
    <div class="balance1" v-for="(item) in bala_list" :key="item.id">
      <div>
        <img :src="item.sr" alt="">
      </div>
      <div>
        <span :class="item.style">{{ item.da }} <br> {{ item.money }}</span>
      </div>
    </div>
  </div>
  <div class="hr_gray"></div>
  <div class="flex date-account">
    <div @click="ViewB_A(0)" :class="{padding_bottom_green:isActive}">按时间查看</div>
    <div @click="ViewB_A(1)" :class="{padding_bottom_green:isActive==false}">按账户查看</div>
  </div>
  <div class="a1">
    <router-view />
  </div>
  </div>
</template>

<script>
import finance from '@/mixins/finance.js'
export default {
  name: "today",
  mixins: [finance],
  data() {
    return {
      todayTime:'今天是XXXX年X月XX日 星期X',
      allBalance:0,
      bala_list: [],
      isActive: true,
      mes:"本日的流水"
    }
  },
  mounted() {
    let name = this.$route.name
    if(name === 'ViewByTime'){ // 本日按时间
      this.isActive = true
    }else if(name === 'ViewByAccount'){
      this.isActive = false
    }
    this.todayTime = '今天是'+ new Date().format('yyyy年M月d日') + ' ' + this.getWeekday()
    this.getFourData()
  },
  methods: {
    backFun(){
      this.$router.push("/caikuai");
    },
    getFourData(){
      let org = this.auth.getOrg()
      this.financeApi.financeAmount({ oid:org.id , state:1  }).then(res1=>{
        let res = res1.data.data
        console.log('financeAmount res=',res)
        this.allBalance = this.formatCurrency(res.allBalance)
        this.bala_list = [
          {id: 0, da: "昨日余额", money: this.formatCurrency(res.allPreviousBalance) , sr: this.balance1 },
          {id: 1, da: "今日收入", money: this.formatCurrency(res.allCredit) , sr: this.balance2 , style: "ty-color-green"},
          {id: 2, da: "今日支出", money: this.formatCurrency(res.allDebit) , sr: this.balance3 , style: "ty-color-red"},
        ]

      }).catch(err=>{
        this.$message.error('链接错误')
        console.log('err=', err)
      })

    },
    ViewB_A(index) {
      if (index == 0) {
        this.$router.push("/ViewByTime");
        this.isActive=true;
        this.mes="本日的流水"
      } else if (index == 1) {
        this.$router.push("/ViewByAccount");
        this.isActive=false;
        this.mes=""
      }
    },
  },
}
</script>

<style scoped lang="scss">
@import "../../style/account.scss";
.balance1{
  margin: 0;
  width: auto;
}

</style>