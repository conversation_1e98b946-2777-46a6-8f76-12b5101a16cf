package cn.sphd.miners.modules.meeting.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmScheduleHistory;
import cn.sphd.miners.modules.meeting.entity.*;
import cn.sphd.miners.modules.meeting.service.MeetingService;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.system.dto.UserHonePageDto;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSONObject;
import net.sf.json.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;

// 会议管理
@Controller
@RequestMapping("/meeting")
public class MeetingController {

    @Autowired
    MeetingService meetingService;
    @Autowired
    UserService userService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    SWMessageService swMessageService;

    /**
     *<AUTHOR>
     *@date 2023/9/23
     *会议管理主列表接口
     */
    @ResponseBody
    @RequestMapping("/getMeetingList.do")
    public JsonResult getMeetingList(User user){
        meetingService.handleMeetingExpire(user.getOid()); // 处理过期会议
        List<MeetingInfo> meetingInfoList=meetingService.getMeetingInfoListByUserId(user.getUserID());
        return new JsonResult(1,meetingInfoList);
    }

    /**
     *<AUTHOR>
     *@date 2023/9/24
     * 发起会议会议参与者选项接口（不含发起人）   发起会议选择审批者列表接口（不含发起人）
     * 中间审批 审批通过选择审批者继续审批列表(不含当前审批人)
     */
    @ResponseBody
    @RequestMapping("/meetingOptionalUsers.do")
    public JsonResult meetingOptionalUsers(User user){
        List<User> userList=userService.getPresentUsers(user.getOid());
        userList.remove(user);
        return new JsonResult(1,userList);
    }


    /**
     *<AUTHOR>
     *@date 2023/9/24
     * 总务就是 会议提交保存 / 其他人都是 会议提交申请
     */
    @ResponseBody
    @RequestMapping("/addMeetingApply.do")
    public JsonResult addMeetingApply(User user, Integer[] attendees, String questions, Integer recorderUserId, Integer approvalUserId, String topic, Integer planDuration, String planTime, Integer pattern, String matters, String memo, String address){
        net.sf.json.JSONArray jsonArray = net.sf.json.JSONArray.fromObject(questions);
        List<String> questionList = JSONArray.toList(jsonArray);
        if (user.getRoleCode().equals("general")){
            meetingService.saveMeetingInfo(user,attendees,questionList,recorderUserId,topic,planDuration,planTime,pattern,matters);
        }else {
            meetingService.saveMeetingInfoHistory(user,attendees,questionList,recorderUserId,approvalUserId,topic,planDuration,planTime,pattern,matters,null,memo,address);
        }
        return new JsonResult(1,1);
    }

    /**
     *<AUTHOR>
     *@date 2023/9/25
     * 申请人会议发起申请列表
     */
    @ResponseBody
    @RequestMapping("/getMeetingApplyList.do")
    public JsonResult getMeetingApplyList(User user){
        List<MeetingInfoHistory> meetingInfoHistoryList= meetingService.getMeetingInfoHistoriesByCreator(user.getUserID());
        return new JsonResult(1,meetingInfoHistoryList);
    }

    /**
     *<AUTHOR>
     *@date 2023/9/25
     * 申请 和审批里的 会议详情 (正式的详情和 是另一个接口，表不一样)
     */
    @ResponseBody
    @RequestMapping("/getApplyMeetingInfo.do")
    public JsonResult getApplyMeetingInfo(User user,Long id){
        MeetingInfoHistory meetingInfoHistory=meetingService.getMeetingInfoHistoryById(id);
        List<ApprovalProcess> middleApprovalList=approvalProcessService.getApprovalProcessByBusiness(id.intValue(),74,null);  //中间审批
        List<ApprovalProcess> finalApprovalList=approvalProcessService.getApprovalProcessByBusiness(id.intValue(),75,null);  //最终审批

        MeetingParticipantHistory meetingRecorder=meetingService.getMeetingRecorderHistory(id);
        meetingInfoHistory.setRecorderUserId(meetingRecorder.getUser());
        meetingInfoHistory.setRecorderUserName(meetingRecorder.getUserName());

        Map<String,Object> map=new HashMap<>();
        if (meetingInfoHistory.getMeetingInfo()!=null){
//            BeanUtils.copyProperties(meetingInfoHistory.getMeetingInfo(),meetingInfoHistory);
            MeetingInfo meetingInfo=meetingInfoHistory.getMeetingInfo();
//            MeetingInfo meetingInfo1=new MeetingInfo();
//            BeanUtils.copyProperties(meetingInfo,meetingInfo1);
//            meetingInfo1.setApproveStatus(meetingInfoHistory.getApproveStatus());

            List<MeetingParticipant> meetingParticipantList=new ArrayList<>(meetingInfo.getMeetingParticipantHashSet());

            Integer signNum=0; //签收人数
            Integer presentNum=0; //出席人数
            for (MeetingParticipant meetingParticipant:meetingParticipantList){
                if (meetingParticipant.getSigned()){  //签收
                    signNum++;
                }
                if (meetingParticipant.getPresent()){  //出席
                    presentNum++;
                }
                if (meetingParticipant.getType().equals(2)){
                    meetingInfo.setRecorderUserId(meetingParticipant.getUser());
                    meetingInfo.setRecorderUserName(meetingParticipant.getUserName());
                }
            }
            meetingInfo.setSignNum(signNum);
            meetingInfo.setPresentNum(presentNum);
            meetingService.updateMeetingInfo(meetingInfo);

            meetingInfoHistory.setSignNum(meetingInfo.getSignNum());
            meetingInfoHistory.setPresentNum(meetingInfo.getPresentNum());
            meetingInfoHistory.setRecorderUserId(meetingInfo.getRecorderUserId());
            meetingInfoHistory.setRecorderUserName(meetingInfo.getRecorderUserName());

            map.put("meetingInfo",meetingInfoHistory);
//            map.put("meetingParticipantList",meetingParticipantList); //参与人
//            map.put("meetingQuestionList",meetingInfo.getMeetingQuestionHashSet());  //会议问题表
            map.put("meetingParticipantList",meetingInfoHistory.getMeetingParticipantHashSet()); //参与人
            map.put("meetingQuestionList",meetingInfoHistory.getMeetingQuestionHashSet());  //会议问题表
        }else {
            map.put("meetingInfo",meetingInfoHistory);
            map.put("meetingParticipantList",meetingInfoHistory.getMeetingParticipantHashSet()); //参与人
            map.put("meetingQuestionList",meetingInfoHistory.getMeetingQuestionHashSet());  //会议问题表
        }
        map.put("middleApprovalList",middleApprovalList);  //中间审批流程
        map.put("finalApprovalList",finalApprovalList); //最终审批流程

        String loginType=""; //1-创建人, 2-记录者，3-参与人， 4-总务, 5-中间审批人
        if (user.getRoleCode().equals("general")){
            loginType="4";
        }else {
            if (user.getUserID().equals(meetingInfoHistory.getCreator())) {
                loginType = "1";
            } else {
                MeetingParticipantHistory meetingParticipantHistory = meetingService.getMeetingParticipantHistory(user.getUserID(), id);
                if (meetingParticipantHistory!=null) {
                    if (meetingParticipantHistory.getType().equals(2)) {
                        loginType = "2";
                    } else {
                        loginType = "3";
                    }
                }else {
                    loginType= "5";
                }
            }
        }
        map.put("loginType",loginType);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2023/9/26
     * 会议修改记录
     */
    @ResponseBody
    @RequestMapping("/getMeetingHistories.do")
    public JsonResult getMeetingHistories(Long id){
        List<MeetingInfoHistory> meetingInfoHistoryList=meetingService.getMeetingInfoHistoriesByMeetingId(id,2);
        List<Map<String,Object>> mapList=new ArrayList<>();
        if (meetingInfoHistoryList.size()>1) {  //只有一条原始信息时不返回
            int number = 0;
            for (MeetingInfoHistory meetingInfoHistory : meetingInfoHistoryList) {
                Map<String, Object> sonMap = new HashMap<>();
                if (number == 0) {
                    sonMap.put("dataState", "原始信息");
                    sonMap.put("nameState", "发起人");
                } else {
                    sonMap.put("dataState", "第" + number + "次修改后");
                    sonMap.put("nameState", "修改人");
                }
                sonMap.put("meetingInfoHistory", meetingInfoHistory);
                sonMap.put("updateName", meetingInfoHistory.getCreateName());//人名
                sonMap.put("updateDate", meetingInfoHistory.getCreateDate());//修改时间
                mapList.add(sonMap);
                number += 1;
            }
        }
        return new JsonResult(1,mapList);
    }

    /**
     *<AUTHOR>
     *@date 2023/9/26
     * 会议发起撤回 5分钟内可
     */
    @ResponseBody
    @RequestMapping("/withdrawMeetingApply.do")
    public JsonResult withdrawMeetingApply(User user,Long id){
        MeetingInfoHistory meetingInfoHistory=meetingService.getMeetingInfoHistoryById(id);
        List<ApprovalProcess> approvalProcessList=approvalProcessService.getApprovalProcessByBusiness(id.intValue(),74,"2");
        Long cha=new Date().getTime()-meetingInfoHistory.getCreateDate().getTime(); //当前时间与申请时间的查
        Long minute=5*60*1000L;  //5分钟的毫秒值
        if (approvalProcessList.size()==0   //没有进行过 中间审批
                &&1==meetingInfoHistory.getApproveStatus()
                &&cha<minute){  // 限定时间内
            meetingService.withdrawMeetingApply(meetingInfoHistory,user);
            return new JsonResult(1,1);
        }else {
            return new JsonResult(new MyException("无法撤回"));
        }
    }

    /**
     *<AUTHOR>
     *@date 2023/9/27
     * 会议发起审批——中间审批列表接口
     */
    @ResponseBody
    @RequestMapping("/getMeetingMiddleApprovals.do")
    public JsonResult getMeetingMiddleApprovals(User user){
        List<Integer> idList=approvalProcessService.getBusinessByToUserBusinessType(user.getUserID(),74);
        List<MeetingInfoHistory> meetingInfoHistoryList=meetingService.getMeetingInfoHistoriesInIds(idList);
        return new JsonResult(1,meetingInfoHistoryList);
    }

    /**
     *<AUTHOR>
     *@date 2023/9/27
     * 会议发起审批——中间审批确定接口
     */
    @ResponseBody
    @RequestMapping("/approvalMeetingMiddle.do")
    public JsonResult approvalMeetingMiddle(User user,Long id,Integer approvalStatus,Integer approvalUserId,String approveMemo){
        JsonResult jsonResult= meetingService.approvalMeetingMiddle(user,id,approvalStatus,approvalUserId,approveMemo);
        return jsonResult;
    }


    /**
     *<AUTHOR>
     *@date 2023/9/28
     * 会议发起审批——最终审批列表接口
     */
    @ResponseBody
    @RequestMapping("/getMeetingFinalApprovals.do")
    public JsonResult getMeetingFinalApprovals(User user){
        List<Integer> idList=approvalProcessService.getBusinessByToUserBusinessType(user.getUserID(),75);
        List<MeetingInfoHistory> meetingInfoHistoryList=meetingService.getMeetingInfoHistoriesInIds(idList);
        return new JsonResult(1,meetingInfoHistoryList);
    }


    /**
     *<AUTHOR>
     *@date 2023/9/29
     * 会议发起审批——最终审批确定接口（发我的消息，消息有快捷跳转）
     */
    @ResponseBody
    @RequestMapping("/approvalMeetingFinal.do")
    public JsonResult approvalMeetingFinal(User user,Long id,Integer approvalStatus,String approveMemo){
        JsonResult jsonResult=meetingService.approvalMeetingFinal(user,id,approvalStatus,approveMemo);
        return jsonResult;
    }


    /**
     *<AUTHOR>
     *@date 2023/9/29
     * 会议通知签收列表接口
     */
    @ResponseBody
    @RequestMapping("/getMeetingMiddleSigns.do")
    public JsonResult getMeetingMiddleSigns(User user){
        List<Integer> idList=approvalProcessService.getBusinessByToUserBusinessType(user.getUserID(),76);
        List<MeetingInfoHistory> meetingInfoHistoryList=meetingService.getMeetingInfoHistoriesInIds(idList);
        return new JsonResult(1,meetingInfoHistoryList);
    }

    /**
     *<AUTHOR>
     *@date 2023/9/30
     * 会议通知签收 确定接口
     * signType 1-审批里的签收，2- 会议开会里的签收
     */
    @ResponseBody
    @RequestMapping("/meetingMiddleSign.do")
    public JsonResult meetingMiddleSign(User user,Long id,Integer approvalStatus,Integer signType){
        if (id!=null&&signType!=null) {
            JsonResult jsonResult = meetingService.approvalMeetingSign(user, id, approvalStatus, signType);
            return jsonResult;
        }
        return new JsonResult(new MyException("参数错误"));
    }

    /**
     *<AUTHOR>
     *@date 2023/9/30
     * 取消本次会议
     */
    @ResponseBody
    @RequestMapping("/cancelMeeting.do")
    public JsonResult cancelMeeting(User user,Long id){
        if (id!=null) {
            JsonResult jsonResult = meetingService.cancelMeeting(user, id);
            return jsonResult;
        }
        return new JsonResult(new MyException("参数错误"));
    }

    /**
     *<AUTHOR>
     *@date 2023/10/7
     * 实际会议的详情
     */
    @ResponseBody
    @RequestMapping("/getMeetingInfo.do")
    public JsonResult getMeetingInfo(User user,Long id){
        MeetingInfo meetingInfo=meetingService.getMeetingInfoById(id);
        List<MeetingParticipant> meetingParticipantList=new ArrayList<>(meetingInfo.getMeetingParticipantHashSet());

        Integer signNum=0; //签收人数
        Integer presentNum=0; //出席人数
        for (MeetingParticipant meetingParticipant:meetingParticipantList){
            if (meetingParticipant.getSigned()){  //签收
                signNum++;
            }
            if (meetingParticipant.getPresent()){  //出席
                presentNum++;
            }
            if (meetingParticipant.getType().equals(2)){
                meetingInfo.setRecorderUserId(meetingParticipant.getUser());
                meetingInfo.setRecorderUserName(meetingParticipant.getUserName());
            }
        }
        meetingInfo.setSignNum(signNum);
        meetingInfo.setPresentNum(presentNum);
        meetingService.updateMeetingInfo(meetingInfo);

        Map<String,Object> map=new HashMap<>();
        map.put("meetingInfo",meetingInfo);
        map.put("meetingParticipantList",meetingParticipantList); //参与人
        map.put("meetingQuestionList",meetingInfo.getMeetingQuestionHashSet());  //会议问题表
        map.put("meetingImageList",meetingService.getMeetingImagesByMeetingIdAndUserId(id,user.getUserID(),1));  //登陆人 随手拍按次汇总
        map.put("sceneMeetingImageList",meetingService.getMeetingImagesByMeetingId(id,2,null));  //现场照片


        Integer contType=2; // 1- 创建人点会议开始页面，2- 会议进行中，3-参与者能否出席页面， 4-记录者 上述信息已知道页面，5-签到进入会议页面
        if (user.getUserID().equals(meetingInfo.getCreator())){  //当前人是会议创建者
            if (0==meetingInfo.getState()){  //会议未开始
                contType=1;
            }
        }else {
//            ApprovalProcess approvalProcess=approvalProcessService.getApprovalProcessByBusToUser(id.intValue(),76,user.getUserID());
            MeetingParticipant meetingParticipant=meetingService.getMeetingParticipant(user.getUserID(),id);
            if (!meetingParticipant.getSigned()){   //没有进行签收
                if (meetingParticipant.getType().equals(1)){
                    contType=3;
                }else {
                    contType=4;
                }
            }else if (meetingParticipant.getPresentTime()==null) {   //没有进行出席
                contType=5;
            }
        }
        map.put("contType",contType);

        MeetingInfoHistory meetingInfoHistory=meetingService.getMeetingInfoHistoryByMeetingId(id);
        if (meetingInfoHistory!=null) {
            List<ApprovalProcess> middleApprovalList = approvalProcessService.getApprovalProcessByBusiness(meetingInfoHistory.getId().intValue(), 74, null);  //中间审批
            List<ApprovalProcess> finalApprovalList = approvalProcessService.getApprovalProcessByBusiness(meetingInfoHistory.getId().intValue(), 75, null);  //最终审批
            map.put("middleApprovalList", middleApprovalList);  //中间审批流程
            map.put("finalApprovalList", finalApprovalList); //最终审批流程
        }

        return new JsonResult(1,map);
    }



    /**
     *<AUTHOR>
     *@date 2023/10/7
     * 会议的修改申请
     */
    @ResponseBody
    @RequestMapping("/meetingEditApply.do")
    public JsonResult meetingEditApply(User user,Long id,Integer[] attendees,String questions,Integer recorderUserId,Integer approvalUserId,String topic,Integer planDuration, String planTime,Integer pattern,String matters,String memo,String address){
        net.sf.json.JSONArray jsonArray = net.sf.json.JSONArray.fromObject(questions);
        List<String> questionList = JSONArray.toList(jsonArray);
        List<MeetingInfoHistory> meetingInfoHistoryList=meetingService.getMeetingInfoHistoriesByMeetingId(id,1);
        if (meetingInfoHistoryList.size()<=0){
            meetingService.saveMeetingInfoHistory(user,attendees,questionList,recorderUserId,approvalUserId,topic,planDuration,planTime,pattern,matters,id,memo,address);
        }else {
            return new JsonResult(new MyException("本会议有未处理完的修改申请，不能重复提交"));
        }
        return new JsonResult(1,1);
    }

    /**
     *<AUTHOR>
     *@date 2023/10/7
     * 开始会议接口（调整会议状态，下次进入也是开始会议页面）
     */
    @ResponseBody
    @RequestMapping("/beginMeeting.do")
    public JsonResult beginMeeting(User user,Long id){
        if (id!=null) {
            MeetingInfo meetingInfo = meetingService.getMeetingInfoById(id);
            if (0==meetingInfo.getState()) {
                meetingInfo.setState(1);
                meetingInfo.setFactTime(new Date());
                meetingInfo.setUpdator(user.getUserID());
                meetingInfo.setUpdateDate(new Date());
                meetingInfo.setUpdateName(user.getUserName());
                meetingService.updateMeetingInfo(meetingInfo);
                return new JsonResult(1, 1);
            }
            return new JsonResult(new MyException("会议已开始，无需重复开始"));
        }
        return new JsonResult(new MyException("参数错误"));
    }


    /**
     *<AUTHOR>
     *@date 2023/10/8
     * 随手拍上传图片 建立业务关联
     */
    @ResponseBody
    @RequestMapping("/addMeetingImage.do")
    public JsonResult addMeetingImage(User user,Long id,String[] uploadPaths,String memo){
        if (id!=null&&uploadPaths.length>0) {
            meetingService.addMeetingImage(user,id,uploadPaths,memo,1);
            return new JsonResult(1,1);
        }
        return new JsonResult(new MyException("参数错误"));
    }

    /**
     *<AUTHOR>
     *@date 2023/10/8
     * 参与人签到并进入会议接口（有会议开始校验）
     */
    @ResponseBody
    @RequestMapping("/enterMeeting.do")
    public JsonResult enterMeeting(User user,Long id){
        if (id!=null) {
          return meetingService.enterMeeting(user,id);
        }
        return new JsonResult(new MyException("参数错误"));
    }

    /**
     *<AUTHOR>
     *@date 2023/11/28
     * 修改编辑 随手拍内容 重新建立图片 建立业务关联
     */
    @ResponseBody
    @RequestMapping("/editMeetingImage.do")
    public JsonResult editMeetingImage(User user,Long id,String[] uploadPaths,String memo,Integer versionNo){
        if (id!=null&&uploadPaths.length>0) {
            meetingService.updateMeetingImage(user,id,uploadPaths,memo,versionNo);
            return new JsonResult(1,1);
        }
        return new JsonResult(new MyException("参数错误"));
    }


    /**
     *<AUTHOR>
     *@date 2023/11/28
     * 结束会议
     */
    @ResponseBody
    @RequestMapping("/endMeeting.do")
    public JsonResult endMeeting(User user,Long id){
        if (id!=null) {
            MeetingInfo meetingInfo=meetingService.getMeetingInfoById(id);
            if (user.getUserID().equals(meetingInfo.getCreator())) {
                meetingInfo.setState(2);
                meetingInfo.setApproveStatus(0);
                meetingInfo.setFinishTime(new Date());
                meetingService.updateMeetingInfo(meetingInfo);

                //给 会议记录者 发  频道四 会议记录 制作 待制作 +1
                MeetingParticipant meetingParticipant=meetingService.getMeetingRecorder(id);
                HashMap<String,Object> hashMap=new HashMap<>();
                hashMap.put("meetingInfo",meetingInfo);
                User recUser=userService.getUserByID(meetingParticipant.getUser());
                swMessageService.rejectSend(1,1,hashMap,meetingParticipant.getUser().toString(),"/recorderMeetingMakeList",null,null,recUser,"meetingRecordMake");

                //给 会议发起人发  频道五 会记录 审批 待制作+1
                swMessageService.rejectSend(1,1,hashMap,user.getUserID().toString(),"/founderMeetingMakeList",null,null,user,"meetingRecordApproval");


                return new JsonResult(1,1);
            }else {
                return new JsonResult(new MyException("登陆人不是会议发起者，不能结束会议！"));
            }
        }
        return new JsonResult(new MyException("参数错误"));
    }


    /**
     *<AUTHOR>
     *@date 2023/12/9
     * 会议记录-制作 待制作列表接口（申请人）
     */
    @ResponseBody
    @RequestMapping("/recorderMeetingMakeList.do")
    public JsonResult recorderMeetingMakeList(User user){
        List<MeetingInfo> meetingInfoList=meetingService.recorderMeetingMakeList(user.getUserID(),0);
        return new JsonResult(1,meetingInfoList);
    }

    /**
     *<AUTHOR>
     *@date 2023/12/9
     * 会议记录-审批 待制作列表接口（审批人）
     */
    @ResponseBody
    @RequestMapping("/founderMeetingMakeList.do")
    public JsonResult founderMeetingMakeList(User user){
        List<MeetingInfo> meetingInfoList=meetingService.founderMeetingMakeList(user.getUserID(),0);
        return new JsonResult(1,meetingInfoList);
    }

    /**
     *<AUTHOR>
     *@date 2023/12
     * 会议记录制作-本次会议提交是否已召开申请接口
     * 会议发起人直接 成功 / 会议记录者提交申请
     *
     * conveneState 召开状态 1-已召开 0-未召开
     */
    @ResponseBody
    @RequestMapping("/addMeetingRecordApply.do")
    public JsonResult addMeetingRecordApply(User user,Long id,Integer conveneState,String factTime,String finishTime,Integer[] otherAttendees,String questionJsonArray,String resolutionJsonArray,String speakJsonArray,String[] sitePhotos){
        meetingService.addMeetingRecordApply(user,id,conveneState,factTime,finishTime,otherAttendees,questionJsonArray,resolutionJsonArray,speakJsonArray,sitePhotos);
        return new JsonResult(1,1);
    }


    /**
     *<AUTHOR>
     *@date 2023/12/11
     * 会议制作获取已签到人员列表接口  / 签到记录接口
     */
    @ResponseBody
    @RequestMapping("/getSignList.do")
    public JsonResult getSignList(User user,Long id){
        List<MeetingParticipant> meetingParticipantList=meetingService.getMeetingParticipantsByMeetingId(id,true);
        return new JsonResult(1,meetingParticipantList);
    }


    /**
     *<AUTHOR>
     *@date 2023/12/12
     * 会议制作获取要解决的问题列表
     */
    @ResponseBody
    @RequestMapping("/getMeetingQuestionList.do")
    public JsonResult getMeetingQuestionList(User user,Long id){
        List<MeetingQuestion> meetingQuestionList=meetingService.getMeetingQuestionByMeetingId(id);
        return new JsonResult(1,meetingQuestionList);
    }


    /**
     *<AUTHOR>
     *@date 2023/12/12
     *会议制作现场照片获取随手拍图片列表
     */
    @ResponseBody
    @RequestMapping("/getMeetingImageList.do")
    public JsonResult getMeetingImageList(User user,Long id){
        List<MeetingImage> meetingImageList=meetingService.getMeetingImagesByMeetingId(id,1,user.getUserID());
        return new JsonResult(1,meetingImageList);
    }


    /**
     *<AUTHOR>
     *@date 2023/12/13
     * 会议制作在拟参会的X但未签到的XXX人中选择 列表(未签到的人员)
     */
    @ResponseBody
    @RequestMapping("/getNotSignList.do")
    public JsonResult getNotSignList(User user,Long id){
        List<MeetingParticipant> meetingParticipantList=meetingService.getMeetingParticipantsByMeetingId(id,false);
        return new JsonResult(1,meetingParticipantList);
    }

    /**
     *<AUTHOR>
     *@date 2023/12/13
     * 会议制作在在其他同事中选择 列表
     */
    @ResponseBody
    @RequestMapping("/getMeetingOtherUsers.do")
    public JsonResult getMeetingOtherUsers(User user,Long id){
        MeetingInfo meetingInfo=meetingService.getMeetingInfoById(id);
        List<Integer> noUserIdList=new ArrayList<>();
        for (MeetingParticipant meetingParticipant:meetingInfo.getMeetingParticipantHashSet()){
            noUserIdList.add(meetingParticipant.getUser());
        }
        noUserIdList.add(meetingInfo.getCreator());
        List<User> userList=userService.getUserNoGeneralByOid(user.getOid(),noUserIdList,null);
        return new JsonResult(1,userList);
    }


    /**
     *<AUTHOR>
     *@date 2023/12/14
     * 会议记录-制作 待审批列表接口（申请人）
     */
    @ResponseBody
    @RequestMapping("/recorderMeetingApplyList.do")
    public JsonResult recorderMeetingApplyList(User user){
        List<MeetingInfo> meetingInfoList=meetingService.recorderMeetingMakeList(user.getUserID(),1);
        return new JsonResult(1,meetingInfoList);
    }

    /**
     *<AUTHOR>
     *@date 2023/12/14
     * 会议记录-审批 待审批列表接口（审批人）
     */
    @ResponseBody
    @RequestMapping("/founderMeetingApprovalList.do")
    public JsonResult founderMeetingApprovalList(User user){
        List<MeetingInfo> meetingInfoList=meetingService.founderMeetingMakeList(user.getUserID(),1);
        return new JsonResult(1,meetingInfoList);
    }

    /**
     *<AUTHOR>
     *@date 2023/10/14
     * 会议记录详情
     */
    @ResponseBody
    @RequestMapping("/getMeetingRecordInfo.do")
    public JsonResult getMeetingRecordInfo(User user,Long id){
        MeetingInfo meetingInfo=meetingService.getMeetingInfoById(id);
        List<MeetingParticipant> meetingParticipantList=new ArrayList<>(meetingInfo.getMeetingParticipantHashSet());

        Integer signNum=0; //签收人数
        Integer presentNum=0; //出席人数
        for (MeetingParticipant meetingParticipant:meetingParticipantList){
            if (meetingParticipant.getSigned()){  //签收
                signNum++;
            }
            if (meetingParticipant.getPresent()){  //出席
                presentNum++;
            }
            if (meetingParticipant.getType().equals(2)){
                meetingInfo.setRecorderUserId(meetingParticipant.getUser());
                meetingInfo.setRecorderUserName(meetingParticipant.getUserName());
            }
        }
        meetingInfo.setSignNum(signNum);
        meetingInfo.setPresentNum(presentNum);
        meetingService.updateMeetingInfo(meetingInfo);

        Map<String,Object> map=new HashMap<>();
        map.put("meetingInfo",meetingInfo);
        map.put("meetingParticipantList",meetingParticipantList); //参与人
        map.put("meetingQuestionList",meetingInfo.getMeetingQuestionHashSet());  //会议问题表
        map.put("meetingImageList",meetingService.getMeetingImagesByMeetingIdAndUserId(id,user.getUserID(),1));  //登陆人 随手拍按次汇总
        map.put("sceneMeetingImageList",meetingService.getMeetingImagesByMeetingId(id,2,null));  //现场照片

        MeetingInfoHistory meetingInfoHistory=meetingService.getMeetingInfoHistoryByMeetingId(id);
        if (meetingInfoHistory!=null) {
            List<ApprovalProcess> middleApprovalList = approvalProcessService.getApprovalProcessByBusiness(meetingInfoHistory.getId().intValue(), 74, null);  //中间审批
            List<ApprovalProcess> finalApprovalList = approvalProcessService.getApprovalProcessByBusiness(meetingInfoHistory.getId().intValue(), 75, null);  //最终审批
            map.put("middleApprovalList", middleApprovalList);  //中间审批流程
            map.put("finalApprovalList", finalApprovalList); //最终审批流程
        }
        map.put("meetingResolutionList",meetingInfo.getMeetingResolutionHashSet());  //会议决议表
        map.put("meetingSpeakList",meetingInfo.getMeetingSpeakHashSet());  //会议发言表
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2023/12/19
     * 批准/驳回会议记录申请（发我的消息通知，消息要有快捷跳转详情）
     */
    @ResponseBody
    @RequestMapping("/approvalMeetingRecord.do")
    public JsonResult approvalMeetingRecord(User user,Long id,Integer approval,String approveMemo){
        if (id!=null&&approval!=null) {
            meetingService.approvalMeetingRecord(user,id,approval,approveMemo);
            return new JsonResult(1, 1);
        }
        return new JsonResult(new MyException("参数错误"));
    }


    /**
     *<AUTHOR>
     *@date 2023/12/20
     * 会议记录-制作 已驳回列表接口（申请人）
     */
    @ResponseBody
    @RequestMapping("/recorderMeetingRejectList.do")
    public JsonResult recorderMeetingRejectList(User user){
        List<MeetingInfo> meetingInfoList=meetingService.recorderMeetingMakeList(user.getUserID(),3);
        return new JsonResult(1,meetingInfoList);
    }

    /**
     *<AUTHOR>
     *@date 2023/12/21
     * 会议记录-审批 已驳回列表接口（审批人）
     */
    @ResponseBody
    @RequestMapping("/founderMeetingRejectList.do")
    public JsonResult founderMeetingRejectList(User user){
        List<MeetingInfo> meetingInfoList=meetingService.founderMeetingMakeList(user.getUserID(),3);
        return new JsonResult(1,meetingInfoList);
    }


    /**
     *<AUTHOR>
     *@date 2023/12/27
     * 会议记录——签收列表（所有参与人，不含发起人）
     */
    @ResponseBody
    @RequestMapping("/participantsMeetingReceiptList.do")
    public JsonResult participantsMeetingReceiptList(User user){
        List<MeetingInfo> meetingInfoList=meetingService.participantsMeetingReceiptList(user.getUserID());
        return new JsonResult(1,meetingInfoList);
    }


    /**
     *<AUTHOR>
     *@date 2023/12/27
     * 会议记录签收签收确定接口
     * signType 1-审批里的签收，2- 会议开会里的签收
     */
    @ResponseBody
    @RequestMapping("/meetingRecordReceipt.do")
    public JsonResult meetingRecordReceipt(User user,Long id){
        if (id!=null) {
            JsonResult jsonResult = meetingService.meetingRecordReceipt(user, id);
            return jsonResult;
        }
        return new JsonResult(new MyException("参数错误"));
    }


    /**
     *<AUTHOR>
     *@date 2023/12/28
     * 历史会议列表接口
     */
    @ResponseBody
    @RequestMapping("/historyMeetingList.do")
    public JsonResult historyMeetingList(User user){
        List<MeetingInfo> meetingInfoList=meetingService.getHistoryMeetingListByUserId(user.getUserID(),null,null);
        return new JsonResult(1,meetingInfoList);
    }


    /**
     *<AUTHOR>
     *@date 2023/12/28
     * 数据筛选条件
     *  可选发起人列表
     *  可选记录者列表
     */
    @ResponseBody
    @RequestMapping("/getMeetingScreenCondition.do")
    public JsonResult getMeetingPromoters(User user){
        List<MeetingInfo> meetingInfoList=meetingService.getHistoryMeetingListByUserId(user.getUserID(),null,null);
        List<Integer> promoters=new ArrayList<>(); // 发起人列表
        List<Integer> recorders=new ArrayList<>(); // 记录者列表
        for (MeetingInfo meetingInfo:meetingInfoList){
            promoters.add(meetingInfo.getCreator());
            for (MeetingParticipant meetingParticipant:meetingInfo.getMeetingParticipantHashSet()){
                if (meetingParticipant.getType().equals(2)){
                    recorders.add(meetingParticipant.getUser());
                }
            }
        }
        List<UserHonePageDto> promoterList=userService.getStaffsOrSuperLocking(user.getOid(),"1",promoters);
        List<UserHonePageDto> recorderList=userService.getStaffsOrSuperLocking(user.getOid(),"1",recorders);
        Map<String,Object> map=new HashMap<>();
        map.put("promoterList",promoterList);  // 发起人列表
        map.put("recorderList",recorderList); // 记录者列表
        return new JsonResult(1,map);
    }



    /**
     *<AUTHOR>
     *@date 2023/12/29
     * 会议数据筛选确定结果接口
     * promoterId 发起人id
     * recorderId 记录者id
     * state 会议状态   全部， 已结束-(2完成,4取消会议,5实际未召开)， 过期-3
     * approveStatus 会议记录状态 全部， 已完成-2，尚未完成 非2
     */
    @ResponseBody
    @RequestMapping("/screenHistoryMeetingList.do")
    public JsonResult screenHistoryMeetingList(User user,Integer promoterId,Integer recorderId,Integer state,Integer approveStatus){
        List<MeetingInfo> meetingInfoList=meetingService.screenHistoryMeetingList(user.getUserID(),promoterId,recorderId,state,approveStatus);
        return new JsonResult(1,meetingInfoList);
    }


    /**
     *<AUTHOR>
     *@date 2023/12/30
     * 其他时间筛选确定结果接口
     */
    @ResponseBody
    @RequestMapping("/timeScreenHistoryMeetingList.do")
    public JsonResult timeScreenHistoryMeetingList(User user,String beginTime,String endTime){
        Date beginDate= NewDateUtils.dateFromString(beginTime,"yyyy-MM-dd HH:mm:ss");
        Date endDate= NewDateUtils.dateFromString(endTime,"yyyy-MM-dd HH:mm:ss");
        List<MeetingInfo> meetingInfoList=meetingService.getHistoryMeetingListByUserId(user.getUserID(),beginDate,endDate);
        return new JsonResult(1,meetingInfoList);
    }


    @ResponseBody
    @RequestMapping("/text.do")
    public JsonResult text(User user){
        Integer number= meetingService.getBadgeNumber(user,"needRecoveredAmount");
        return new JsonResult(1,number);
    }
}
