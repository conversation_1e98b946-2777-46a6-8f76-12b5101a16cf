	var isAdd = 1; // 标记是否可以添加反馈的输入框
	$(function(){
		App.init(); 
		// 获得反馈意见列表
		getfeedbackList(1,10);
		 

	});
	
	// 调取用户反馈的列表( 当前页数，每页条数 )
	function getfeedbackList(curpage,pernum){
		$.ajax({
			url:"site_showback.action",
			data:{ "curpage":curpage , "pernum":pernum  },
			type:"post",
			success:function(data){
				data = eval('('+ data+')');
				var status = data["status"];//0权限不符，1成功，2.session丢失
				if(status == 2){
					alert("登陆失效，请重新登陆！");
					location.href = "site_loginpage.action";
				}else if(status == 0){
					alert("您的登陆与权限不符，请重新登陆！");
					location.href = "site_loginpage.action";
				}else if(status == 1){
					var list = data["list"];
					var cur = data["curpage"];
					var countall = data["countpage"];
					$("#feedbackCon").children(":eq(1)").children().remove();
					$("#ye").html("");
					setPage($("#ye"),cur,countall,"feedback");
					
					for(var i=0; i<list.length; i++){
						var content = list[i]["content"];
						var org_name = list[i]["org_name"];
						var id = list[i]["id"];
						var create_date = list[i]["create_date"];
						var titile = list[i]["titile"];
						var create_name = list[i]["create_name"];
						var question_num = list[i]["question_num"];
						var reply_num = list[i]["reply_num"];
						var state = list[i]["state"];
						var sta_style = "unsolve";
						if(state == 0){ state = "未解决";  }else{ state = "已解决"; sta_style = "solve";  }
						var org = list[i]["org"];
						var is_stick = list[i]["is_stick"];
						var creator = list[i]["creator"];
						
						var str = "<tr><td>"+ id +"</td><td>" + titile +"</td><td>"+ org_name +"</td><td>"+ create_date +"</td>" +
						"<td>"+ reply_num +"</td><td>"+ question_num +"</td><td class='"+ sta_style +"'>"+ state +"</td><td> <a class='opinionDetails' " +
						" onclick='opinionDetails($(this))'  >[ 查看 ]</a></td></tr>";
						$("#feedbackCon").append(str);
					}
					
				}
				
			},
			error:function(){
				alert("连接错误，请稍后重试！");
			}
		});
	}
	
	// 查看反馈的详情
	function opinionDetails(obj){
		$("#op_addqs").val("");
		$(".op_ttl").html("");
		$(".op_date").html("");
		$(".opId").html("");
		$(".op_question").html("");
		var id = obj.parent().siblings(":eq(0)").html();
		var status = obj.parent().prev().html();
		if (status=="未解决") { $("#status_1").hide(); $("#status_2").show();
		} else if(status=="已解决"){ $("#status_1").show(); $("#status_2").hide(); };
		$.ajax({
			url:" site_lookreply.action",
 			data:{ "id": id },
 			type:"post",
 			dataType:"json",
 			success:function(data){
				bounce.show($("#opinionDetailsCon"))
 				var list1 = data["list1"]; // 问题详情
 				var list2 = data["list2"]; // 问答详情
 				// 更新问题
 				if(list1.length > 0){
 					var title = list1[0]["titile"];
 					var time = list1[0]["create_date"];
 					var content = list1[0]["content"];
 					var quesId = list1[0]["id"];
 					$(".op_ttl").html(title);
 					$(".op_date").html(time);
 					$(".op_question").html(content);
 					$(".opId").html(quesId);
 				}
 				// 更新问答
 				var strDetails = "";	
 				if(list2.length > 0){
 					for(var i = 0; i <list2.length; i++){
 						var det_con = list2[i]["content"];
 						var det_id = list2[i]["id"];
 						var det_category = list2[i]["category"]; // 1 大后台回复,  2用户反馈
 						var det_create_date = list2[i]["create_date"];
 						var det_create_name = list2[i]["create_name"];
 						var det_state = list2[i]["state"];	// 反馈的状态 
 						var det_creator = list2[i]["creator"];
 						
 						if(det_category == 1){
 							strDetails += "<div class='op_item_as'>";
 						}else if(det_category == 2){
 							strDetails += "<div class='op_item_qs'>";
 						}
 						strDetails += "<div class='op_identity'>" ;
 						if(det_category == 1){
 							strDetails += "<span class='op_iden_as'>后台回复</span></div>";
 						}else if(det_category == 2){
 							strDetails += "<span class='op_iden_qs'>用户反馈</span></div>";
 						}
 						strDetails +="<div class='op_con'><div class='op_time'>"+ det_create_date +"</div>" + det_con+"</div></div>";
 					}
 				}else{
 					strDetails = "目前没有提问和回复记录";
 				}
 				
 				$(".op_detl").html(strDetails);
 				
 				
  			},
 			error:function(){
 				alert("连接错误，请稍后重试！");
 			}
		});
		
	}
	
	// 确定回复
 	function addAns(obj){	
 		var op_id = $(".opId").html()  ;		
 		var op_con = obj.parent().prev().val();	
 		if(op_id == null || op_id == ""  ){
 			alert("操作不合法，请稍后重试！");
 		}else if( op_con == "" || op_con == null){
 			alert("请将回复内容补充完整！");
 		}else{
 			if(confirm("确定该问题的回复？")){
 				obj.parent().prev().val("");		
 				$.ajax({
 					url:"site_reply.action",
 					data:{ "id": op_id,  "content":op_con }  ,
 					type:"post",
 					dataType:"json",
 					success:function(data){
 						var status = data["status"];
 						if(status == 1){					
 							var listreply = data["listreply"];
 							var str = "<div class='op_item_as'><div class='op_identity'>" +
 							"<span class='op_iden_as'>后台回复</span></div><div class='op_con'>" +
 							"<div class='op_time'> "+ listreply[0]["create_date"] +" </div>" + op_con + "</div>	</div>" ;
 							$(".op_detl").append(str);
 						}else{
 							alert("回复失败，请重试！");
 						}
 					},
 					
 					error:function(){
 						alert("连接错误，请稍后重试！");
 					}
 				});
 				
 			}
 		}
		 
 	}

  

 	 
