package cn.sphd.miners.modules.generalAffairs.controller;

import cn.sphd.miners.common.utils.GetLocalIPUtils;
import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.generalAffairs.entity.ReqUserObject;
import cn.sphd.miners.modules.generalAffairs.entity.SysUserLock;
import cn.sphd.miners.modules.generalAffairs.entity.UserImport;
import cn.sphd.miners.modules.generalAffairs.service.UserImportService;
import cn.sphd.miners.modules.generalAffairs.service.UserLockService;
import cn.sphd.miners.modules.material.entity.RespStatus;
import cn.sphd.miners.modules.sms.service.SendMessageService;
import cn.sphd.miners.modules.sms.service.SmsService;
import cn.sphd.miners.modules.sms.service.TemplateService;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.*;
import cn.sphd.miners.modules.system.entity.UserHistory;
import com.alibaba.fastjson.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName UserImportController
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/9 9:38
 * @Version 1.0
 */

@Controller
@RequestMapping("/userImport")
public class UserImportController {
    @Autowired
    UserService userService;
    @Autowired
    OrgPopedomService orgPopedomService;
    @Autowired
    UserLockService userLockService;
    @Autowired
    UserHistoryService userHistoryService;
    @Autowired
    UserImportService userImportService;
    @Autowired
    OrgService orgService;
    @Autowired
    UserPopedomService userPopedomService;
    @Autowired
    SmsService smsService;

    //修改错误员工信息确认接口
    //传值 mobile userName
    //返回值 status 1-查重通过  0-与在职相同  2- 与离职相同 3- 与修改记录相同  4-与被冻结相同
    //      name名字
    @ResponseBody
    @RequestMapping("/updateFalseUserEnter.do")
    public RespStatus updateFalseUserEnter(User user,Integer sonOid, User u) throws IOException {
        RespStatus respStatus = new RespStatus();
        Integer oid=user.getOid();
        if (sonOid!=null){ // 1.214多地点 xuzhi改
            oid=orgService.getOidByOrgSonOrg(oid,sonOid);
        }
        Integer status=1;//1-查重通过  0-与在职相同  2- 与离职相同 3- 与修改记录相同
        User userByLogonName = userService.getUserByOrgSonOrgMobile(oid,u.getMobile());  // 查重加 子机构人员  1.214多地点 lixu改
        if(userByLogonName!=null) {
            respStatus.setName(userByLogonName.getUserName());
            status=0;
        }else{
            List<User> userList = userService.getUserByMobileAndIsDuty(oid,u.getMobile(), "2");  //离职人员
            if (userList.size() > 0) {
                respStatus.setName(userList.get(0).getUserName());
                status = 2;
            }else {
                List<UserHistory> userHistoryList=userHistoryService.getUserHistoryListByOidMobile(oid,u.getMobile());
                if (userHistoryList.size()>0){
                    respStatus.setName(userHistoryList.get(0).getUserName());
                    status=3;
                }else{
                    List<SysUserLock> userLockList=userImportService.getUserLockListByOidMobile(oid,u.getMobile());
                    if (userLockList.size()>0){
                        respStatus.setName(userLockList.get(0).getLockedName());
                        status = 4;//冻结
                    }
                }
            }
        }
        respStatus.setStatus(status);
        return respStatus;
    }

    //确认全部导入数据有多少不合法
    //传值 List<User> userList  员工列表
    //      importSum;//导入总数
    //返回值 falseImportSum 无法保存的数量
    //      tureImportSum 可以保存的数量
    //      importSum;//导入总数
    @ResponseBody
    @RequestMapping("/allImportUserEnter.do")
    public ReqUserObject allImportUserEnter(User user,Integer sonOid,ReqUserObject reqMtObject) throws IOException {
        Integer oid=user.getOid();
        if (sonOid!=null){ // 1.214多地点 xuzhi改
            oid=orgService.getOidByOrgSonOrg(oid,sonOid);
        }
        ReqUserObject reqUserObject=new ReqUserObject();
        List<User> trueUserList= new ArrayList<>();
        List<User> users = JSONArray.parseArray(reqMtObject.getUsersList(),User.class);
        reqMtObject.setUserList(users);
        List<User> falseUserList= new ArrayList<>();
        List<User> userList = userService.getUserListByOrgSonOrg(oid);  // 查重加 子机构人员  1.214多地点 lixu改
        List<User> userLockList = userImportService.getAllLockUsersByOrganization(oid);

        for (int i=0;i<users.size();i++) {
            User u=users.get(i);
            int status=1;
            //1.手机号位数不为11位的， 姓名，手机号为空的
            if(u.getMobile()==null||"".equals(u.getMobile())||u.getUserName()==null||"".equals(u.getUserName()))
            {
                status=0;
                falseUserList.add(u);
            }else if(u.getMobile().length()!=11){
                status=0;
                falseUserList.add(u);
            }
            //2.手机号自身重复的
            if(status==1) {
                for (int j = 0; j < users.size(); j++) {
                    if(u.getMobile().equals(users.get(j).getMobile())&&i!=j){
                        status=0;
                        falseUserList.add(u);
                        break;
                    }
                }
            }
            //3.手机号和系统内存在的出现重复的
            if(status==1){
                for(User user1: userList){
                    if(user1.getMobile().equals(u.getMobile())){
                        status=0;
                        falseUserList.add(u);
                        break;
                    }
                }
            }
            //4.手机号和被冻结的手机号重复的
            if(status==1){
                for(User user1: userLockList){
                    if(user1.getMobile().equals(u.getMobile())){
                        status=0;
                        falseUserList.add(u);
                        break;
                    }
                }
            }
            if(status==1){
                trueUserList.add(u);
            }
        }
        reqUserObject.setFalseImportSum(falseUserList.size());
        reqUserObject.setTureImportSum(trueUserList.size());
        reqUserObject.setImportSum(reqMtObject.getImportSum());
        return reqUserObject;
    }


    //点击确定，提交所有导入员工，不可导入数据删除，可导入数据入数据库
    //传值 List<User> userList  员工列表
    //     importSum;//导入总数
    //返回值
    //      tureImportSum 可以保存的数量
    //      importSum;//导入总数
    //      List<User> userList  员工列表
    @ResponseBody
    @RequestMapping("/saveImportUser.do")
    public ReqUserObject saveImportUser(User user,Integer sonOid, ReqUserObject reqUserObject) throws IOException {
        Integer oid = user.getOid();
        if (sonOid!=null){ // 1.214多地点 lixu加
            oid=orgService.getOidByOrgSonOrg(oid,sonOid);
        }
        reqUserObject.setOrg(oid);
        User superUser=userService.getUserByRoleCode(oid,"super");
        List<User> users = JSONArray.parseArray(reqUserObject.getUsersList(),User.class);
        reqUserObject.setUserList(users);
        int importId=userImportService.getImportId(oid);
        List<User> userList = userService.getUserListByOrgSonOrg(oid);  // 查重加 子机构人员  1.214多地点 lixu改
        ReqUserObject reqMt=userImportService.saveImportUser(reqUserObject,user,userList,oid);
        List<UserImport> userImportList=new ArrayList<>();
        for(UserImport user1:reqMt.getUserImportList())
        {
            user1.setSuperId(orgService.getByOid(user1.getOrg(),true,false).getSuperId());
            user1.setLeader(superUser.getUserID().toString());
            user1.setLeaderSource(2);
            user1.setLeaderName(superUser.getUserName());
            user1.setRankUrl("/"+superUser.getUserID());// 直系下级路径 lixu 改 2020/10/29 9:40
            User general = userService.getUserByRoleCode(oid, "general");//总务
            if (general!=null) {
                user1.setManager(general.getUserID());
                user1.setManagerCode("general");
            }else{
                user1.setManagerCode("general");
            }
            UserImport userImport=userImportService.addUserImport(user1,importId);
            userImportList.add(userImport);
           // userService.addUser(user);
        }
        reqMt.setUserImportList(userImportList);
        List<User> manageList=new ArrayList<>();
        if (orgPopedomService.getOrgPopedomByMid(oid,"kb")){
            User general = userService.getUserByRoleCode(oid, "general");//总务
            if (general!=null) {
                manageList.add(general);
            }else{
                User managerUser=new User();
                managerUser.setRoleCode("general");
                manageList.add(managerUser);
            }
        }
        if (orgPopedomService.getOrgPopedomByMid(oid,"ld")){
            User finance = userService.getUserByRoleCode(oid, "finance");//财务
            if (finance!=null) {
                manageList.add(finance);
            }else{
                User managerUser=new User();
                managerUser.setRoleCode("finance");
                manageList.add(managerUser);
            }
        }
        if (orgPopedomService.getOrgPopedomByMid(oid,"qb")){
            User sale = userService.getUserByRoleCode(oid, "sale");//销售
            if (sale!=null) {
                manageList.add(sale);
            }else{
                User managerUser=new User();
                managerUser.setRoleCode("sale");
                manageList.add(managerUser);
            }
        }
     //
        if (orgPopedomService.getOrgPopedomByMid(oid,"sb")){
            User accounting = userService.getUserByRoleCode(oid, "accounting");//机构会计
            if (accounting!=null) {
                manageList.add(accounting);
            }else{
                User managerUser=new User();
                managerUser.setRoleCode("accounting");
                manageList.add(managerUser);
            }
        }
        reqMt.setManageList(manageList);
        Integer number=userImportService.getNoLeaderUserCountByOid(oid);
        Integer noManageNumber=userImportService.getNoManageUserCountByOid(oid);
        if (number==0&&noManageNumber==0){
            reqMt.setButtonState(1);//  确定按钮状态1- 变亮 0- 置灰
        }else {
            reqMt.setButtonState(0);//  确定按钮状态1- 变亮 0- 置灰
        }
        return reqMt;
    }

    //未完成批量导入列表接口
    //返回值 falseImportSum 无法保存的数量
    //       tureImportSum 可以保存的数量
    //       importSum;//导入总数
    //       List<User> userList 列表 改为userImportList

    @ResponseBody
    @RequestMapping("/unfinishedImportUser.do")
    public ReqUserObject unfinishedImportUser(User user,Integer sonOid, Model model) throws IOException {
        Integer oid = user.getOid();
        if (sonOid!=null){ // 1.214多地点 lixu加
            oid=orgService.getOidByOrgSonOrg(oid,sonOid);
        }
        ReqUserObject reqUserObject=userImportService.unfinishedImportUser(oid);
        Integer number=userImportService.getNoLeaderUserCountByOid(oid);
        Integer noManageNumber=userImportService.getNoManageUserCountByOid(oid);
        if (number==0&&noManageNumber==0){
            reqUserObject.setButtonState(1);//  确定按钮状态1- 变亮 0- 置灰
        }else {
            reqUserObject.setButtonState(0);//  确定按钮状态1- 变亮 0- 置灰
        }
        List<User> manageList=new ArrayList<>();
        if (orgPopedomService.getOrgPopedomByMid(oid,"kb")){
            User general = userService.getUserByRoleCode(oid, "general");//总务
            if (general!=null) {
                manageList.add(general);
            }else{
                User managerUser=new User();
                managerUser.setRoleCode("general");
                manageList.add(managerUser);
            }
        }
        if (orgPopedomService.getOrgPopedomByMid(oid,"ld")){
            User finance = userService.getUserByRoleCode(oid, "finance");//财务
            if (finance!=null) {
                manageList.add(finance);
            }else{
                User managerUser=new User();
                managerUser.setRoleCode("finance");
                manageList.add(managerUser);
            }
        }
        if (orgPopedomService.getOrgPopedomByMid(oid,"qb")){
            User sale = userService.getUserByRoleCode(oid, "sale");//销售
            if (sale!=null) {
                manageList.add(sale);
            }else{
                User managerUser=new User();
                managerUser.setRoleCode("sale");
                manageList.add(managerUser);
            }
        }
        if (orgPopedomService.getOrgPopedomByMid(oid,"sb")){
            User accounting = userService.getUserByRoleCode(oid, "accounting");//机构会计
            if (accounting!=null) {
                manageList.add(accounting);
            }else{
                User managerUser=new User();
                managerUser.setRoleCode("accounting");
                manageList.add(managerUser);
            }
        }
        reqUserObject.setManageList(manageList);
        return  reqUserObject;
    }
    //删除
    //传值 id
    //返回值 status 1成功，0失败
    @ResponseBody
    @RequestMapping("/deleteImportUser.do")
    public RespStatus deleteImportUser(int id,Integer sonOid,User user) throws IOException {
        RespStatus respStatus=new RespStatus();
        Integer oid = user.getOid();
        if (sonOid!=null){ // 1.214多地点 lixu加
            oid=orgService.getOidByOrgSonOrg(oid,sonOid);
        }
        int status=userImportService.deleteImportUser(id);
        Integer number=userImportService.getNoLeaderUserCountByOid(oid);
        Integer noManageNumber=userImportService.getNoManageUserCountByOid(oid);
        if (number==0&&noManageNumber==0){
            respStatus.setButtonState(1);//  确定按钮状态1- 变亮 0- 置灰
        }else {
            respStatus.setButtonState(0);//  确定按钮状态1- 变亮 0- 置灰
        }
        respStatus.setStatus(status);
        return respStatus;
    }
    //修改
    //传值 id
    //返回值 status 1成功，0失败
    @ResponseBody
    @RequestMapping("/updateImportUser.do")
    public RespStatus updateImportUser(Integer userId,Integer sonOid, Integer ordinaryEmployees, Integer leaderId, Integer leaderSorce,String manageCode,Integer manageId, HttpServletResponse response,User user) throws IOException {
        RespStatus respStatus=new RespStatus();
        Map<String,Object> map=new HashMap<String,Object>();
        Integer mapvalue=1;
        Integer oid =user.getOid();
        if (sonOid!=null){ // 1.214多地点 lixu加
            oid=orgService.getOidByOrgSonOrg(oid,sonOid);
        }
        UserImport userImport=userImportService.getUserImportByID(userId);
        if (ordinaryEmployees!=null){
            if (ordinaryEmployees==1) {
                userImport.setOrdinaryEmployees(ordinaryEmployees);
                userImportService.updateImportLeader(userId);
            }else if (ordinaryEmployees==0){
                userImport.setOrdinaryEmployees(ordinaryEmployees);
            }
        }
        String rankUrl ="";
        String oldUrl = userImport.getRankUrl();//修改直接上级前，其下属的从属关系
        if (leaderId!=null){

            if(leaderSorce==1)//本表
            {
                UserImport leader=userImportService.getUserImportByID(leaderId);//直接上级
                userImport.setLeaderName(leader.getUserName());
                if (leader.getRankUrl()!=null){
                    rankUrl+=leader.getRankUrl();//改成的上级从属关系
                }
            }
            else{//user表
                User leader=userService.getUserByID(leaderId);//直接上级
                userImport.setLeaderName(leader.getUserName());
                if (leader.getRankUrl()!=null){
                    rankUrl+=leader.getRankUrl();//改成的上级从属关系
                }
            }
            userImport.setLeader(leaderId.toString());
            userImport.setLeaderSource(leaderSorce);
            rankUrl+=userImport.getLeader();//更改后的从属关系
            userImport.setRankUrl(rankUrl);//从属关系
        }
        if (manageId!=null){
            if(manageId!=0) {
                User manage = userService.getUserByID(manageId);
                userImport.setManager(manage.getUserID());   //高管id
                userImport.setManagerCode(manage.getRoleCode());  //高管code
            }else {
                userImport.setManager(null);
                userImport.setManagerCode(null);
            }
        }
        if(manageCode!=null)
        {
            userImport.setManagerCode(manageCode);
        }
        userImportService.updateImportUser(userImport);

        Integer number=userImportService.getNoLeaderUserCountByOid(oid);
        Integer noManageNumber=userImportService.getNoManageUserCountByOid(oid);
        if (number==0&&noManageNumber==0){
            respStatus.setButtonState(1);//  确定按钮状态1- 变亮 0- 置灰
        }else {
            respStatus.setButtonState(0);//  确定按钮状态1- 变亮 0- 置灰
        }
        respStatus.setStatus(mapvalue);
        return respStatus;
    }

    //放弃导入
    //返回值 status 1成功，0失败
    @ResponseBody
    @RequestMapping("/giveUpImportUser.do")
    public RespStatus giveUpImportUser(User user,Integer sonOid) throws IOException {
        RespStatus respStatus=new RespStatus();
        Integer oid =user.getOid();
        if (sonOid!=null){ // 1.214多地点 lixu加
            oid=orgService.getOidByOrgSonOrg(oid,sonOid);
        }
        int status=userImportService.giveUpImportUser(oid);
        respStatus.setStatus(status);
        return respStatus;
    }


    //完成导入
    //返回值 status 1成功，0失败
    @ResponseBody
    @RequestMapping("/completeImportUser.do")
    public RespStatus completeImportUser(User user, Integer sonOid, HttpServletRequest request, AuthInfoDto authInfo) throws IOException {
        Integer oid=user.getOid();
        if (sonOid!=null){ // 1.214多地点 lixu加
           oid=orgService.getOidByOrgSonOrg(oid,sonOid);
        }
        RespStatus respStatus=new RespStatus();
        int status=1;
        User superUser=userService.getUserByRoleCode(oid,"super");
        Organization o = orgService.getByOid(oid,true,false);
        List <User> userList=userImportService.completeImportUser(request,authInfo,user,oid);
        if (o.getInitState().equals("0")) {//没正式启用
            user=userService.getUserByID(user.getUserID());
//            orgService.confirmOrg(o.getId(),user,request);//确定使用机构     1.139中枢管控2  lixu改  导入完成不再直接启用机构了，需董事长在中枢管控操作后才可 启用机构
            if (!"super".equals(user.getRoleCode())&&!"general".equals(user.getRoleCode())){
                userPopedomService.deleteUserPopedomByRoleCode(user.getUserID(),user.getRoleCode());// 清除临时管理员 的 操作权限  此员工需要 等正式启用机构才可登录
                if (o.getOrgType()==4&&userService.getUserByLogon(null,user.getMobile(),user.getOrganization().getPid())!=null){
                    user.setIsDuty("6");
                    user.setRoleCode("see"); //1.214多地点 新建子机构 选择的总机构人员 作为临时管理员， 导入完成把临时管理员变成 过期的可见人员
                    userPopedomService.saveUserPopedomByCode("see",user);
                }else {
                    user.setRoleCode("staff");// 临时管理员变为普通员工
                }
                userService.updateUser(user);

//                String cont="职工已导入至wonderss系统。请您及时登录，并按提示继续操作";
//                sendMessageService.sendMessage(superUser.getUserID(),o.getId(),superUser.getMobile(),cont);//给董事长发短信
                // 短信重构 2024/5/22 李旭更改
                String phoneNo=superUser.getMobile();
                String url = GetLocalIPUtils.getRootPath(request);
                Map<String, String> params = new HashMap<String, String>(2) {{
                    put("phoneNo", phoneNo);
                    put("url", url);
                }};
                smsService.sendMessage(SmsService.SendType.sys, phoneNo, authInfo, TemplateService.SmsContentTemplate.noticeSuper, params, null);

            }

        }else{
            for(User user1:userList) {
                userPopedomService.saveUserPopedomByCode("staff",user1);//给普通员工附上权限
//                salaryAction.addSalaryUser(user1);
                if ("staff".equals(user1.getRoleCode())&&1!=user1.getOrdinaryEmployees()){  // 非普通员工   2020/11/2 我的团队版  lixu加
                    userPopedomService.saveUserPopedomByCode("seniorStaff",user1); //添加非普通员工权限
                }
            }
        }
        respStatus.setStatus(status);
        return respStatus;
    }
    //修改姓名手机号
    //传值  mobile userName id
    //返回值 status 1成功，-1和本次导入的其他手机号重复
    @ResponseBody
    @RequestMapping("/updateImportUserMobile.do")
    public RespStatus updateImportUserMobile(UserImport userImport,User user,Integer sonOid) throws IOException {
        RespStatus respStatus=new RespStatus();
        Integer oid=user.getOid();
        if (sonOid!=null){ // 1.214多地点 lixu加
            oid=orgService.getOidByOrgSonOrg(oid,sonOid);
        }
        userImport.setOrg(oid);
        int status=userImportService.updateImportUserMobile(userImport);
        respStatus.setStatus(status);
        return respStatus;
    }
    //处理批量导入没有初始化薪资宝的问题
    @ResponseBody
    @RequestMapping("/addSalaryUserForImportUser.do")
    public RespStatus addSalaryUserForImportUser(User user, HttpServletRequest request) throws IOException {
        Organization o = orgService.getByOid(user.getOid(),true,false);
        List <User> userList=userImportService.addSalaryUserForImportUser(o.getId());
//        for(User user1:userList) {
//            salaryAction.addSalaryUser(user1);
//        }
        RespStatus respStatus=new RespStatus();
        respStatus.setStatus(1);
        return respStatus;
    }
    //处理批量导入没有初始化薪资宝的问题
    @ResponseBody
    @RequestMapping("/updateUserForE.do")
    public RespStatus updateUserForE(User user, HttpServletRequest request) throws IOException {
        userImportService.updateUserForE();
        return null;
    }

    //可选择直接上级获取接口 id
    @ResponseBody
    @RequestMapping("/selectOptionalLeader.do")
    public List<UserImport> getOptionalUser(User user,Integer id,Integer sonOid) throws IOException {
        int oid = user.getOid();
        if (sonOid!=null){ // 1.214多地点 lixu加
            oid=orgService.getOidByOrgSonOrg(oid,sonOid);
        }
        List<User> userList = new ArrayList<User>();
        User superUser = userService.getUserByRoleCode(oid, "super");
        userList.add(superUser);
        List<User> users = userImportService.getNotDirectLowerGrade(oid);
        for (User u : users) {
            if ((u.getOrdinaryEmployees() == null || u.getOrdinaryEmployees() == 0)){
                userList.add(u);
            }
        }
        List<UserImport> userImportList=userImportService.getOptionalImportUser(oid,id);
        for(UserImport userImport:userImportList){
            userImport.setStatus("1");
        }
        for(User user1:userList)
        {
                UserImport userImport = new UserImport();
                userImport.setId(user1.getUserID());
                userImport.setMobile(user1.getMobile());
                userImport.setUserName(user1.getUserName());
                userImport.setStatus("2");
                userImportList.add(userImport);
        }
        return userImportList;
    }
}
