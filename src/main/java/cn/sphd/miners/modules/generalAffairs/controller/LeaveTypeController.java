package cn.sphd.miners.modules.generalAffairs.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.modules.generalAffairs.entity.PersonnelLeaveType;
import cn.sphd.miners.modules.generalAffairs.service.LeaveTypeService;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2021/4/29.
 */
@Controller
@RequestMapping("/leaveType")
public class LeaveTypeController {

    @Autowired
    LeaveTypeService leaveTypeService;

    /**
     *<AUTHOR>
     *@date 2021/4/29 20:04
     *获取所有请假类型
    */
    @ResponseBody
    @RequestMapping("/getAllLeaveType.do")
    public JsonResult getAllLeaveType(User user){
        Map<String,Object> map = new HashMap<>();
        List<PersonnelLeaveType> leaveTypes = leaveTypeService.getAllLeaveType(user.getOid(),null);
        map.put("leaveTypes",leaveTypes);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2021/4/29 20:53
     *暂停/恢复使用
     * enabled 1-恢复 0-暂停
    */
    @ResponseBody
    @RequestMapping("/pauseResumeUse.do")
    public JsonResult pauseResumeUse(Integer leaveTypeId,Integer enabled,User user){
        Map<String,Object> map = new HashMap<>();
        map = leaveTypeService.pauseResumeUse(leaveTypeId,enabled,user.getUserID());
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2021/5/8 11:32
     *删除请假类型
    */
    @ResponseBody
    @RequestMapping("/deleteLeaveType.do")
    public JsonResult deleteLeaveType(Integer leaveTypeId){
        Map<String,Object> map = new HashMap<>();
        map = leaveTypeService.deleteLeaveType(leaveTypeId);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2021/5/8 15:59
     *新增请假类型
    */
    @ResponseBody
    @RequestMapping("/addLeaveType.do")
    public JsonResult addLeaveType(String name,User user){
        Map<String,Object> map = new HashMap<>();
        map = leaveTypeService.addLeaveType(name,user.getUserID());
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2021/4/29 20:04
     *获取系统中可用的请假类型
     */
    @ResponseBody
    @RequestMapping("/getLeaveTypeUsable.do")
    public JsonResult getLeaveTypeUsable(User user){
        Map<String,Object> map = new HashMap<>();
        List<PersonnelLeaveType> leaveTypes = leaveTypeService.getAllLeaveType(user.getOid(),1);
        map.put("leaveTypes",leaveTypes);
        return new JsonResult(1,map);
    }
}
