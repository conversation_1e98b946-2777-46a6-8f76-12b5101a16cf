package cn.sphd.miners.modules.forumArea.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.forumArea.entity.ForumPost;
import cn.sphd.miners.modules.forumArea.entity.ForumReplyAttachment;
import cn.sphd.miners.modules.forumArea.service.ForumService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.List;

/**
 * Created by 朱思旭 on 2020/12/14.
 */
@Controller
@RequestMapping("/forum")
public class ForumArchiveController {

    @Autowired
    UserService userService;
    @Autowired
    ForumService forumService;

    //分页获取可以归档的附件
    @ResponseBody
    @RequestMapping("/getSmallArchiveFile.do")
    public JsonResult getSmallArchiveFile(Integer postId, String attType, PageInfo pageInfo){
        List<ForumReplyAttachment> listReplyAtt = forumService.getSubsidiaryFile(postId,null,attType, "1",pageInfo);
        HashMap<String, Object> map = new HashMap<>();
        map.put("listReplyAtt",listReplyAtt);
        map.put("pageInfo",pageInfo);
        return new JsonResult(1, map);
    }

    //开发或不开放讨论组
    @ResponseBody
    @RequestMapping("/openForumPost.do")
    public JsonResult openForumPost(Integer postId, Integer isOpen){
        Integer status = forumService.forumPostByIsOpen(postId, isOpen);
        QueryData query = new QueryData();
        query.put("isOpen", status);
        return new JsonResult(1, query);
    }

    //删除讨论组
    @ResponseBody
    @RequestMapping("/delForumPost.do")
    public JsonResult delForumPost(Integer postId, User user){
        HashMap<String, Object> map = forumService.delForumPost(user, postId);
        return new JsonResult(1,map);
    }

    //管理的讨论组列表
    @ResponseBody
    @RequestMapping("/getManagerForumPost.do")
    public JsonResult getManagerForumPost(User user){
        List<ForumPost> list = forumService.getManagerPostList(user.getUserID());
        QueryData query = new QueryData();
        query.put("managerList", list);
        return new JsonResult(1,query);
    }

    //删除讨论还原接口
    @ResponseBody
    @RequestMapping("/restoreForumPost.do")
    public JsonResult restoreForumPost(Integer postId){
        Integer state = forumService.restoreForum(postId);
        QueryData query = new QueryData();
        query.put("status", state);
        return new JsonResult(1,query);
    }

}
