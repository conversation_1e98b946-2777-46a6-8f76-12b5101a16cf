<template>
<!--  标签效果图-->
  <div class="effect_main_avatar">
    <div class="effect_main">
      <div class="effect_qrCode" v-show="qrPosition === 1">
        <svg t="1715417929629" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1696" width="48" height="48">
          <path d="M48.384 45.376l412.224 0 0 84.992-412.224 0 0-84.992ZM565.44 45.376l238.656 0 0 84.992-238.656 0 0-84.992ZM891.584 45.376l86.72 0 0 86.72-86.72 0 0-86.72ZM48.384 374.4l412.224 0 0 85.056-412.224 0 0-85.056ZM45.696 45.376l84.992 0 0 412.224-84.992 0 0-412.224ZM375.616 45.376l84.992 0 0 412.224-84.992 0 0-412.224ZM189.376 200.832l115.712 0 0 115.712-115.712 0 0-115.712ZM565.44 130.368l82.752 0 0 87.232-82.752 0 0-87.232ZM804.032 130.368l90.752 0 0 160.512-90.752 0 0-160.512ZM891.584 217.6l86.72 0 0 151.872-86.72 0 0-151.872ZM651.328 282.688l152.768 0 0 109.312-152.768 0 0-109.312ZM736.832 369.408l155.392 0 0 88.128-155.392 0 0-88.128ZM565.952 372.16l85.376 0 0 85.376-85.376 0 0-85.376ZM45.696 542.784l84.992 0 0 192.576-84.992 0 0-192.576ZM130.88 717.248l91.968 0 0 92.032-91.968 0 0-92.032ZM45.696 805.632l85.184 0 0 173.056-85.184 0 0-173.056ZM217.664 542.784l261.696 0 0 105.728-261.696 0 0-105.728ZM281.344 639.104l109.184 0 0 100.48-109.184 0 0-100.48ZM370.176 717.248l109.184 0 0 174.848-109.184 0 0-174.848ZM285.44 805.632l105.088 0 0 173.056-105.088 0 0-173.056ZM197.952 869.12l102.016 0 0 109.568-102.016 0 0-109.568ZM629.184 542.784l195.264 0 0 174.464-195.264 0 0-174.464ZM871.168 542.784l107.136 0 0 107.136-107.136 0 0-107.136ZM545.088 630.016l107.136 0 0 194.304-107.136 0 0-194.304ZM716.672 692.8l107.776 0 0 111.872-107.776 0 0-111.872ZM545.088 869.12l107.136 0 0 109.568-107.136 0 0-109.568ZM802.816 892.096l175.488 0 0 86.592-175.488 0 0-86.592ZM890.56 804.672l87.744 0 0 105.728-87.744 0 0-105.728Z" fill="#2c3e50" p-id="1697"></path>
        </svg>
      </div>
      <div class="effect_code" v-show="codeShowable === 1 && codePosition === 1">
        <el-tooltip
            class="box-item"
            effect="dark"
            content="仓库代号"
            placement="bottom"
        >
          <h2 class="effect_code_main">CP</h2>
        </el-tooltip>
      </div>
      <div class="effect_shelfNum">
        <el-tooltip
            class="box-item"
            effect="dark"
            content="货架号"
            placement="bottom"
        >
          <h2 class="effect_code_main">12</h2>
        </el-tooltip>
      </div>
      <div class="effect_">
        <el-icon><SemiSelect /></el-icon>
      </div>
      <div class="effect_floorNum">
        <el-tooltip
            class="box-item"
            effect="dark"
            content="层号"
            placement="bottom"
        >
          <h2 class="effect_code_main">3</h2>
        </el-tooltip>
      </div>
      <div class="effect_code" v-show="codeShowable === 1 && codePosition === 2">
        <el-tooltip
            class="box-item"
            effect="dark"
            content="仓库代号"
            placement="bottom"
        >
          <h2 class="effect_code_main">CP</h2>
        </el-tooltip>
      </div>
      <div class="effect_qrCode" v-show="qrPosition === 2">
        <svg t="1715417929629" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1696" width="48" height="48">
          <path d="M48.384 45.376l412.224 0 0 84.992-412.224 0 0-84.992ZM565.44 45.376l238.656 0 0 84.992-238.656 0 0-84.992ZM891.584 45.376l86.72 0 0 86.72-86.72 0 0-86.72ZM48.384 374.4l412.224 0 0 85.056-412.224 0 0-85.056ZM45.696 45.376l84.992 0 0 412.224-84.992 0 0-412.224ZM375.616 45.376l84.992 0 0 412.224-84.992 0 0-412.224ZM189.376 200.832l115.712 0 0 115.712-115.712 0 0-115.712ZM565.44 130.368l82.752 0 0 87.232-82.752 0 0-87.232ZM804.032 130.368l90.752 0 0 160.512-90.752 0 0-160.512ZM891.584 217.6l86.72 0 0 151.872-86.72 0 0-151.872ZM651.328 282.688l152.768 0 0 109.312-152.768 0 0-109.312ZM736.832 369.408l155.392 0 0 88.128-155.392 0 0-88.128ZM565.952 372.16l85.376 0 0 85.376-85.376 0 0-85.376ZM45.696 542.784l84.992 0 0 192.576-84.992 0 0-192.576ZM130.88 717.248l91.968 0 0 92.032-91.968 0 0-92.032ZM45.696 805.632l85.184 0 0 173.056-85.184 0 0-173.056ZM217.664 542.784l261.696 0 0 105.728-261.696 0 0-105.728ZM281.344 639.104l109.184 0 0 100.48-109.184 0 0-100.48ZM370.176 717.248l109.184 0 0 174.848-109.184 0 0-174.848ZM285.44 805.632l105.088 0 0 173.056-105.088 0 0-173.056ZM197.952 869.12l102.016 0 0 109.568-102.016 0 0-109.568ZM629.184 542.784l195.264 0 0 174.464-195.264 0 0-174.464ZM871.168 542.784l107.136 0 0 107.136-107.136 0 0-107.136ZM545.088 630.016l107.136 0 0 194.304-107.136 0 0-194.304ZM716.672 692.8l107.776 0 0 111.872-107.776 0 0-111.872ZM545.088 869.12l107.136 0 0 109.568-107.136 0 0-109.568ZM802.816 892.096l175.488 0 0 86.592-175.488 0 0-86.592ZM890.56 804.672l87.744 0 0 105.728-87.744 0 0-105.728Z" fill="#2c3e50" p-id="1697"></path>
        </svg>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.effect_main_avatar{
  margin-top: 16px;
  margin-bottom: 4px;
  .effect_main{
    display: flex;
    background: #f5f5f5;
    padding: 16px;
    text-align: center;
    border-radius: 5px;
    font-size: 24px;
    align-items: center;
    .effect_code{
      flex: auto;
    }
    .effect_shelfNum{
      flex: auto;
    }
    .effect_floorNum{
      flex: auto;
    }
    .effect_qrCode{
      width: 100px;
      flex: none;
      height: 48px;
    }
    .effect_des{
      color: #666;
    }
    h2{
      font-family: Helvetica;
    }
  }
}
</style>
<script>
export default {
  props: {
    qrPosition: Number, // 二维码位置
    codeShowable: Number, // 仓库代号是否显示
    codePosition: Number // 仓库代号位置
  }
}
</script>