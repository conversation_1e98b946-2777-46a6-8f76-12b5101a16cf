package cn.sphd.miners.modules.finance.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by Administrator on 2019/1/11.
 */
@Entity
@Table(name="t_finance_return_history")
public class FinanceReturnHistory implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="org"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer org;//机构id

    @Column(name="finance_return"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer financeReturnId;//回款票据id

    @Column(name="return_no"  , length=100 , nullable=true , unique=false)
    private String returnNo;   //支票号码

    @Column(name="type"   , nullable=true , unique=false)
    private Integer type;   //1-转帐支票，2-承兑汇票

    @Column(name="category"  , length=1 , nullable=true , unique=false)
    private String category;   //种类:1-贷款,2-借款,3-投资款,4-废品,5-其他

    @Column(name="category_desc"  , length=100 , nullable=true , unique=false)
    private String categoryDesc;  //类别说明,当类别为5-其他时,录入文字补充性说明

    @Column(name="amount"   , nullable=true , unique=false)
    private BigDecimal amount;  //金额

    @Column(name="bill_amount"   , nullable=true , unique=false)
    private BigDecimal billAmount;  //票面金额

    @Column(name="summary"  , length=255 , nullable=true , unique=false)
    private String summary;   //摘要

    @Column(name="purpose"  , length=255 , nullable=true , unique=false)
    private String purpose;  //用途

    @Column(name="operator"   , nullable=true , unique=false)
    private Integer operator;   //经手人

    @Column(name="operator_name"  , length=100 , nullable=true , unique=false)
    private String operatorName;   //经手人姓名

    @Column(name="partner_name"  , length=100 , nullable=true , unique=false)
    private String partnerName;//合作方经手人姓名

    @Column(name="receive_date"   , nullable=true , unique=false)
    @Temporal(TemporalType.DATE)
    private Date receiveDate;   //收到票据日期

    @Column(name="payer"  , length=100 , nullable=true , unique=false)
    private String payer;   //付款单位

    @Column(name="original_corp"  , length=100 , nullable=true , unique=false)
    private String originalCorp;   //原始出具票据单位

    @Column(name="bank_name"  , length=100 , nullable=true , unique=false)
    private String bankName;  // 出具支票/汇票银行(出具支票银行)

    @Column(name="expire_date"   , nullable=true , unique=false)
    private Date expireDate;   //支票到期日期

    @Column(name="state"  , length=1 , nullable=true , unique=false)
    private String state;  //1-有效,2-存入银行,3-作废，4-已支出

    @Column(name="accout"  , length=100 , nullable=true , unique=false)
    private String accout;   //存入银行帐号

    @Column(name="deposit_date"   , nullable=true , unique=false)
    private Date depositDate;   //存入银行日期

    @Column(name="depositor"   , nullable=true , unique=false)
    private Integer depositor;   //存入经手人ID

    @Column(name="depositor_name"  , length=100 , nullable=true , unique=false)
    private String depositorName;   //存入经手人姓名

    @Column(name="receive_account_date"   , nullable=true , unique=false)
    private Date receiveAccountDate;   //到帐日期

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;   //说明

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="approve_Item"   , nullable=true , unique=false)
    private Integer approveItem;

    @Column(name="approve_status"  , length=1 , nullable=true , unique=false)
    private String approveStatus;

    @Column(name="approve_level"   , nullable=true , unique=false)
    private Integer approveLevel;

    @Column(name="auditor"   , nullable=true , unique=false)
    private Integer auditor;

    @Column(name="auditor_name"  , length=100 , nullable=true , unique=false)
    private String auditorName;

    @Column(name="audit_date"   , nullable=true , unique=false)
    private Date auditDate;

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;

    @Column(name="apply_memo"  , length=255 , nullable=true , unique=false)
    private String applyMemo;

    @Column(name="approve_memo"  , length=255 , nullable=true , unique=false)
    private String approveMemo;

    @Column(name="message_id"   , nullable=true , unique=false)
    private Integer messageId;

    @Column(name="save_bank_name"  , length=100 , nullable=true , unique=false)
    private String saveBankName;  // 存入支票/汇票的银行名称

    @Column(name="account_id"   , nullable=true , unique=false)
    private Integer accountId;  //存入存入银行的id

    @Column(name = "modity_status")
    private String modityStatus;  //数据修改状态  null/1-未修改  2-已修改(每条数据仅能修改一次)

//    @Column(name="myself_id"   , nullable=true , unique=false)
//    private Integer myselfId;  //数据修改时，对应修改前支票的id(本表)

    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId;//修改前记录ID

    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo;//版本号,每次修改+1

    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.LAZY )
    @JoinColumn(name="finance_return", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private FinanceReturn financeReturn;//回款票据关联

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getFinanceReturnId() {
        return financeReturnId;
    }

    public void setFinanceReturnId(Integer financeReturnId) {
        this.financeReturnId = financeReturnId;
    }

    public String getReturnNo() {
        return returnNo;
    }

    public void setReturnNo(String returnNo) {
        this.returnNo = returnNo;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCategoryDesc() {
        return categoryDesc;
    }

    public void setCategoryDesc(String categoryDesc) {
        this.categoryDesc = categoryDesc;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getBillAmount() {
        return billAmount;
    }

    public void setBillAmount(BigDecimal billAmount) {
        this.billAmount = billAmount;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public Integer getOperator() {
        return operator;
    }

    public void setOperator(Integer operator) {
        this.operator = operator;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Date getReceiveDate() {
        return receiveDate;
    }

    public void setReceiveDate(Date receiveDate) {
        this.receiveDate = receiveDate;
    }

    public String getPayer() {
        return payer;
    }

    public void setPayer(String payer) {
        this.payer = payer;
    }

    public String getOriginalCorp() {
        return originalCorp;
    }

    public void setOriginalCorp(String originalCorp) {
        this.originalCorp = originalCorp;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public Date getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Date expireDate) {
        this.expireDate = expireDate;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getAccout() {
        return accout;
    }

    public void setAccout(String accout) {
        this.accout = accout;
    }

    public Date getDepositDate() {
        return depositDate;
    }

    public void setDepositDate(Date depositDate) {
        this.depositDate = depositDate;
    }

    public Integer getDepositor() {
        return depositor;
    }

    public void setDepositor(Integer depositor) {
        this.depositor = depositor;
    }

    public String getDepositorName() {
        return depositorName;
    }

    public void setDepositorName(String depositorName) {
        this.depositorName = depositorName;
    }

    public Date getReceiveAccountDate() {
        return receiveAccountDate;
    }

    public void setReceiveAccountDate(Date receiveAccountDate) {
        this.receiveAccountDate = receiveAccountDate;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    public String getSaveBankName() {
        return saveBankName;
    }

    public void setSaveBankName(String saveBankName) {
        this.saveBankName = saveBankName;
    }

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public String getModityStatus() {
        return modityStatus;
    }

    public void setModityStatus(String modityStatus) {
        this.modityStatus = modityStatus;
    }

//    public Integer getMyselfId() {
//        return myselfId;
//    }

//    public void setMyselfId(Integer myselfId) {
//        this.myselfId = myselfId;
//    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public FinanceReturn getFinanceReturn() {
        return financeReturn;
    }

    public void setFinanceReturn(FinanceReturn financeReturn) {
        this.financeReturn = financeReturn;
    }

    public String getPartnerName() {
        return partnerName;
    }

    public void setPartnerName(String partnerName) {
        this.partnerName = partnerName;
    }
}
