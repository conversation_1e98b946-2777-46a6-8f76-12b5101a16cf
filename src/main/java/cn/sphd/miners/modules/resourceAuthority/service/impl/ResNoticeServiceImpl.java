package cn.sphd.miners.modules.resourceAuthority.service.impl;

import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.common.utils.TimeUtils;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.resourceAuthority.dao.ResNoticeDao;
import cn.sphd.miners.modules.resourceAuthority.entity.*;
import cn.sphd.miners.modules.resourceAuthority.mapper.ResAclMapper;
import cn.sphd.miners.modules.resourceAuthority.service.*;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Service("resNoticeService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED)
public class ResNoticeServiceImpl implements ResNoticeService {

    @Autowired
    ResNoticeDao resNoticeDao;


    @Autowired
    ResService resService;
    @Autowired
    ResCorrelationService resCorrelationService;
    @Autowired
    ResAttService resAttService;
    @Autowired
    SWMessageService swMessageService;
    @Autowired
    UserService userService;
    @Autowired
    ResNoticeService resNoticeService;
    @Autowired
    ResCategoryService resCategoryService;

    @Autowired
    ResAclMapper resAclMapper;

    @Override
    public Integer getBadgeNumber(User user, String code) {
        String type = null;
        switch (code){
            case "needBatchReceiptFile":                                      //需批量签收的文件
                type = "1";
                break;
            case "newSecuringPowerFile":                                      //新获得使用权限的文件
                type = "2";
                break;
            case "fileVersionChangeNotice":                                   //文件换版通知
                type = "3";
                break;
            case "fileCancelNotice":                                          //文件废止通知
                type = "4";
                break;
            case "fileReturnNotice":                                          //文件废止复用通知
                type = "5";
                break;
            case "fileDeadNotice":                                            //文件停用通知
                type = "6";
                break;
            case "folderDeadNotice":                                          //文件夹停用通知
                type = "7";
                break;
            case "deadFileReturnNotice":                                      //停用文件的复用通知
                type = "8";
                break;
            case "revokePowerNotice":                                         //使用权限被取消的通知
                type = "9";
                break;
            case "tableRelationApproval":                                     //表格关联通知
                type = "10";
                break;
            case "tableRelieveRelationApproval":                              //解除表格关联通知
                type = "11";
                break;
            case "fileLocationMoveNotice":                                    //文件位置移动的通知
                type = "12";
                break;
            case "folderLocationMoveNotice":                                  //文件夹位置移动的通知
                type = "13";
                break;
            case "fileNameEditNotice":                                        //解除表格关联通知
                type = "14";
                break;
            case "folderNameEditNotice":                                      //解除表格关联通知
                type = "15";
                break;
            case "tableNumberEditNotice":                                     //表格编号/名称修改的通知
                type = "16";
                break;
        }
        Integer number= this.resNoticeNumByType(user.getUserID(), type);
        return number;
    }


    /**
     *
     * @param user        登陆人
     * @param resId       文件id
     * @param resHisId    历史文件id
     * @param corrId      文件与表格关联id
     * @param userId      签收人id
     * @param type        签收类型
     * @return
     */
    @Override
    public ResNotice insertResNotice(User user, Integer resId, Integer resHisId, String bathUuid, Long batchNum,
                                     Integer corrId, Integer category, Integer userId, Byte type, String originalPosition,
                                     String newPosition) {
        ResNotice resNotice = new ResNotice();
        if (resId != null) {
            resNotice.setResource(resId);
        }
        if(resHisId != null){
            resNotice.setResourceHistory(resHisId);
        }
        if(bathUuid != null){
            resNotice.setBatchUuid(bathUuid);
        }
        if (batchNum != null) {
            resNotice.setBatchNum(BigInteger.valueOf(batchNum));
        }
        if (corrId != null) {
            resNotice.setResourceCorrelation(corrId);
        }
        if (category != null) {
            resNotice.setCategory(category);
        }
        if (originalPosition != null) {
            resNotice.setOriginalFeature(originalPosition);
        }
        if (newPosition != null) {
            resNotice.setNewFeature(newPosition);
        }
        resNotice.setType(type);
        resNotice.setUser(userId);
        resNotice.setIsSigned(Byte.valueOf("0"));
        resNotice.setOrg(user.getOid());
        resNotice.setCreator(user.getUserID());
        resNotice.setCreateName(user.getUserName());
        resNotice.setCreateDate(new Date());
        resNoticeDao.save(resNotice);
        return resNotice;
    }

    @Override
    public List<ResNotice> listResNoticeByType(User user, String type) {
        List<ResNotice> list = this.getListNotice(user.getUserID(), null, null, type, "0");
        if (!list.isEmpty()) {
            switch (type) {
                case "2":
                case "3":
                case "4":
                case "5":
                case "6":
                case "8":
                case "9":
                case "12":
                    for (ResNotice rn : list) {
                        ResHistory rh = new ResHistory();
                        rh.setId(rn.getResourceHistory());
                        ResHistory resHistory = resService.gethisSingle(rh);
                        if ("12".equals(type)) {
                            rn.setAllFileName(resHistory.getFileSn() + "/" + resHistory.getName());
                        }else {
                            rn.setFileName(resHistory.getName());
                            rn.setFileSn(resHistory.getFileSn());
                            rn.setChangeNum(resHistory.getChangeNum());
                            rn.setVersion(resHistory.getVersion());
                        }
                    }
                    break;
                case "7":
                case "13":
                    for (ResNotice rn : list) {
                        ResCategory resCategory = resCategoryService.getSingle(rn.getCategory());
                        rn.setCategoryName(resCategory.getName());
                    }
                    break;
                case "10":
                case "11":
                    for (ResNotice rn : list) {
                        ResCorrelation r = resCorrelationService.getSingleResCorrelation(rn.getResourceCorrelation());
                        ResEntity res = resService.getSingle(r.getResource());
                        ResAtt resAtt = resAttService.getResAttSingle(r.getAttachment());
                        rn.setAllFileName(res.getFileSn() + "/" + res.getName());
                        rn.setAllFormName(resAtt.getSn() + "/" + resAtt.getName());
                    }
                    break;
            }
        }
        return list;
    }

    @Override
    public Integer signedResourceByType(User user, Integer id, String type) {
        Integer state = 1;
        if (id != null) {
            ResNotice resNotice = resNoticeDao.get(id);
            if (Byte.valueOf("1").equals(resNotice.getIsSigned())) {
                state = 2;
            } else {
                resNotice.setIsSigned(Byte.valueOf("1"));
                resNotice.setExtinctionTime(new Date());
                HashMap<String,Object> corrMap =new HashMap<>();
                corrMap.put("resNotice", resNotice);
                if ("1".equals(type)){
                    swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/needBatchReceiptFile", null, null,user,"needBatchReceiptFile");
                } else if ("2".equals(type)) {
                    swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/newSecuringPowerFile", null, null,user,"newSecuringPowerFile");
                } else if ("3".equals(type)) {
                    swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/fileVersionChangeNotice", null, null,user,"fileVersionChangeNotice");
                } else if ("4".equals(type)) {
                    swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/fileCancelNotice", null, null,user,"fileCancelNotice");
                } else if ("5".equals(type)) {
                    swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/fileReturnNotice", null, null,user,"fileReturnNotice");
                } else if ("6".equals(type)) {
                    swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/fileDeadNotice", null, null,user,"fileDeadNotice");
                }else if ("7".equals(type)) {
                    swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/folderDeadNotice", null, null,user,"folderDeadNotice");
                }else if ("8".equals(type)) {
                    swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/deadFileReturnNotice", null, null,user,"deadFileReturnNotice");
                }else if ("9".equals(type)) {
                    swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/revokePowerNotice", null, null,user,"revokePowerNotice");
                }else if ("10".equals(type)) {
                    swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/tableRelationApproval", null, null,user,"tableRelationApproval");
                } else if ("11".equals(type)) {
                    swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/tableRelieveRelationApproval", null, null,user,"tableRelieveRelationApproval");
                }else if ("12".equals(type)) {
                    swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/fileLocationMoveNotice", null, null,user,"fileLocationMoveNotice");
                }else if ("13".equals(type)) {
                    swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/folderLocationMoveNotice", null, null,user,"folderLocationMoveNotice");
                }else if ("14".equals(type)) {
                    swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/fileNameEditNotice", null, null,user,"fileNameEditNotice");
                }else if ("15".equals(type)) {
                    swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/folderNameEditNotice", null, null,user,"folderNameEditNotice");
                }else if ("16".equals(type)) {
                    swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/tableNumberEditNotice", null, null,user,"tableNumberEditNotice");
                }
            }
        } else {
            List<ResNotice> list = this.getListNotice(user.getUserID(), null, null, type, "0");
            if (!list.isEmpty()) {
                for (ResNotice rn : list) {
                    rn.setIsSigned(Byte.valueOf("1"));
                    rn.setExtinctionTime(new Date());
                    resNoticeDao.saveOrUpdate(rn);
                    HashMap<String,Object> corrMap =new HashMap<>();
                    corrMap.put("resNotice", rn);
                    if ("6".equals(type)) {
                        swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/fileDeadNotice", null, null,user,"fileDeadNotice");
                    }else if ("7".equals(type)) {
                        swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/folderDeadNotice", null, null,user,"folderDeadNotice");
                    }else if ("9".equals(type)) {
                        swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/revokePowerNotice", null, null,user,"revokePowerNotice");
                    } else if ("10".equals(type)) {
                        swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/tableRelationApproval", null, null,user,"tableRelationApproval");
                    } else if ("11".equals(type)) {
                        swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/tableRelieveRelationApproval", null, null,user,"tableRelieveRelationApproval");
                    }else if ("12".equals(type)) {
                        swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/fileLocationMoveNotice", null, null,user,"fileLocationMoveNotice");
                    }else if ("13".equals(type)) {
                        swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/folderLocationMoveNotice", null, null,user,"folderLocationMoveNotice");
                    }else if ("14".equals(type)) {
                        swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/fileNameEditNotice", null, null,user,"fileNameEditNotice");
                    }else if ("15".equals(type)) {
                        swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/folderNameEditNotice", null, null,user,"folderNameEditNotice");
                    }else if ("16".equals(type)) {
                        swMessageService.rejectSend(-1,-1,corrMap,user.getUserID().toString(),"/tableNumberEditNotice", null, null,user,"tableNumberEditNotice");
                    }
                }
            }
        }
        return state;
    }

    @Override
    public List<ResNotice> listDisappearResNoticeByType(User user, String type) {
        String hql = " from ResNotice where type = :type and isSigned = :isSigned and user = :user and extinctionTime >= :extinctionTime";
        HashMap<String,Object> param = new HashMap<>();
        param.put("type", Byte.valueOf(type));
        param.put("isSigned", Byte.valueOf("1"));
        param.put("user", user.getUserID());
        Date ThreeDaysAgo = new Date(new Date().getTime() - 3 * 24 * 60 * 60 * 1000);// 当前时间 72小时前
        param.put("extinctionTime", ThreeDaysAgo);
        List<ResNotice> list = resNoticeDao.getListByHQLWithNamedParams(hql,param);
        if (!list.isEmpty()) {
            for (ResNotice rn : list) {
                ResEntity res = null;
                switch (type) {
                    case "6":
                    case "9":
                    case "12":
                        ResHistory rh = new ResHistory();
                        rh.setId(rn.getResourceHistory());
                        ResHistory resHistory = resService.gethisSingle(rh);
                        if ("12".equals(type)) {
                            rn.setAllFileName(resHistory.getFileSn() + "/" + resHistory.getName());
                        } else {
                            rn.setFileName(resHistory.getName());
                            rn.setFileSn(resHistory.getFileSn());
                            rn.setChangeNum(resHistory.getChangeNum());
                            rn.setVersion(resHistory.getVersion());
                        }
                        break;
                    case "7":
                    case "13":
                        ResCategory resCategory = resCategoryService.getSingle(rn.getCategory());
                        rn.setCategoryName(resCategory.getName());
                        break;
                    case "10":
                    case "11":
                        ResCorrelation r = resCorrelationService.getSingleResCorrelation(rn.getResourceCorrelation());
                        res = resService.getSingle(r.getResource());
                        ResAtt resAtt = resAttService.getResAttSingle(r.getAttachment());
                        rn.setAllFileName(res.getFileSn() + "/" + res.getName());
                        rn.setAllFormName(resAtt.getSn() + "/" + resAtt.getName());
                        break;
                }
                String noticeDisappear= TimeUtils.toTimeString2sf(rn.getExtinctionTime().getTime()-ThreeDaysAgo.getTime());
                rn.setNoticeDisappear("将于" + noticeDisappear + "后消失");
            }
        }
        return list;
    }

    @Override
    public void addSpecialAuthUserId(List<Integer> listUserIDs, User user) {
        User userSuper = userService.getUserByRoleCode(user.getOid(),"super");
        User userSmallSuper = userService.getUserByRoleCode(user.getOid(), "smallSuper");
        if (userSmallSuper != null) {
            listUserIDs.add(userSmallSuper.getUserID());
        }
        listUserIDs.add(userSuper.getUserID());
    }

    @Override
    public void sendResFileNotice(User user, Integer resId, ResAtt resAtt, Integer corrId, Integer resHisId,
                                  String batchUuid, Long batchNum, Integer category, String type,
                                  List<Integer> listUserIDs, String originalPosition, String newPosition) {
        if (!listUserIDs.isEmpty()) {
            for (Integer userId : listUserIDs) {
                HashMap<String, Object> corrMap = new HashMap<>();
                ResNotice resNotice = null;
                ResEntity res = null;
                ResCategory resCategory = null;
                User sendUser = userService.getUserByID(userId);
                switch (type) {
                    case "1":    //批量签收文件
                        resNotice = this.insertResNotice(user,null,null, batchUuid, batchNum,null,category,userId,Byte.valueOf(type),null,null);
                        corrMap.put("resNotice", resNotice);
                        swMessageService.rejectSend(1,1,corrMap,userId.toString(),"/needBatchReceiptFile", null, null,sendUser,"needBatchReceiptFile");
                        break;
                    case "2":   //新获得使用权限的文件
                    case "3":   //文件换版
                    case "4":   //文件废止
                    case "5":   //废止文件复用
                    case "6":   //文件停用
                    case "8":   //停用文件复用
                    case "9":   //使用权限被取消
                    case "12":  //文件位置移动
                        resNotice = this.insertResNotice(user, null, resHisId, null, null,null, null, userId, Byte.valueOf(type), originalPosition, newPosition);
                        ResHistory rh = new ResHistory();
                        rh.setId(resHisId);
                        ResHistory resHistory = resService.gethisSingle(rh);
                        if ("12".equals(type)) {
                            resNotice.setAllFileName(resHistory.getFileSn() + "/" + resHistory.getName());
                        }else {
                            resNotice.setFileName(resHistory.getName());
                            resNotice.setFileSn(resHistory.getFileSn());
                            resNotice.setChangeNum(resHistory.getChangeNum());
                            resNotice.setVersion(resHistory.getVersion());
                        }
                        corrMap.put("resNotice", resNotice);
                        if ("2".equals(type)) {
                            swMessageService.rejectSend(1,1,corrMap,userId.toString(),"/newSecuringPowerFile", null, null,sendUser,"newSecuringPowerFile");
                        } else if ("3".equals(type)) {
                            swMessageService.rejectSend(1,1,corrMap,userId.toString(),"/fileVersionChangeNotice",  null, null,sendUser,"fileVersionChangeNotice");
                        }else if ("4".equals(type)) {
                            swMessageService.rejectSend(1,1,corrMap,userId.toString(),"/fileCancelNotice", null, null,sendUser,"fileCancelNotice");
                        }else if ("5".equals(type)) {
                            swMessageService.rejectSend(1,1,corrMap,userId.toString(),"/fileReturnNotice", null, null,sendUser,"fileReturnNotice");
                        }else if ("6".equals(type)) {
                            swMessageService.rejectSend(1,1,corrMap,userId.toString(),"/fileDeadNotice", null, null,sendUser,"fileDeadNotice");
                        }else if ("8".equals(type)) {
                            swMessageService.rejectSend(1,1,corrMap,userId.toString(),"/deadFileReturnNotice", null, null,sendUser,"deadFileReturnNotice");
                        }else if ("9".equals(type)) {
                            swMessageService.rejectSend(1,1,corrMap,userId.toString(),"/revokePowerNotice", null, null,sendUser,"revokePowerNotice");
                        }else if ("12".equals(type)) {
                            swMessageService.rejectSend(1,1,corrMap,userId.toString(),"/fileLocationMoveNotice", null, null,sendUser,"fileLocationMoveNotice");
                        }
                        break;
                    case "10":    //表格关联通知
                    case "11":    //解除表格关联通知
                        if ("10".equals(type)) {
                            resNotice = this.insertResNotice(user,null,null, null, null, corrId, null, userId, Byte.valueOf(type), null, null);
                        } else if ("11".equals(type)) {
                            resNotice = this.insertResNotice(user,null,null, null, null, corrId, null, userId, Byte.valueOf(type), null, null);
                        }
                        res = resService.getSingle(resId);
                        resNotice.setAllFileName(res.getFileSn() + "/" + res.getName());
                        resNotice.setAllFormName(resAtt.getSn() + "/" + resAtt.getName());
                        corrMap.put("resNotice", resNotice);
                        if ("10".equals(type)) {
                            swMessageService.rejectSend(1,1,corrMap,userId.toString(),"/tableRelationApproval", null, null,sendUser,"tableRelationApproval");
                        } else if ("11".equals(type)) {
                            swMessageService.rejectSend(1,1,corrMap,userId.toString(),"/tableRelieveRelationApproval", null, null,sendUser,"tableRelieveRelationApproval");
                        }
                        break;
                    case "7":     //文件夹停用
                    case "13":    //文件夹位置移动
                        resNotice = this.insertResNotice(user, null, null, null, null, null, category, userId, Byte.valueOf(type),originalPosition,newPosition);
                        resCategory = resCategoryService.getSingle(category);
                        resNotice.setCategoryName(resCategory.getName());
                        corrMap.put("resNotice", resNotice);
                        if ("7".equals(type)) {
                            swMessageService.rejectSend(1,1,corrMap,userId.toString(),"/folderDeadNotice", null, null,sendUser,"folderDeadNotice");
                        } else {
                            swMessageService.rejectSend(1,1,corrMap,userId.toString(),"/folderLocationMoveNotice", null, null,sendUser,"folderLocationMoveNotice");
                        }
                        break;
                    case "14":
                    case "15":
                    case "16":
                        resNotice = this.insertResNotice(user,null,null,null,null,null,null,userId,Byte.valueOf(type),originalPosition,newPosition);
                        corrMap.put("resNotice", resNotice);
                        if ("14".equals(type)) {
                            swMessageService.rejectSend(1,1,corrMap,userId.toString(),"/fileNameEditNotice", null, null,sendUser,"fileNameEditNotice");
                        } else if ("15".equals(type)) {
                            swMessageService.rejectSend(1,1,corrMap,userId.toString(),"/folderNameEditNotice", null, null,sendUser,"folderNameEditNotice");
                        } else if ("16".equals(type)) {
                            swMessageService.rejectSend(1,1,corrMap,userId.toString(),"/tableNumberEditNotice", null, null,sendUser,"tableNumberEditNotice");
                        }
                        break;
                }
            }
        }

    }

    private int resNoticeNumByType(Integer userId, String type){
        String hql = "SELECT count(id) FROM ResNotice where type = :type and isSigned = :isSigned and user = :user";
        HashMap<String,Object> param = new HashMap<>();
        param.put("type", Byte.valueOf(type));
        param.put("isSigned", Byte.valueOf("0"));
        param.put("user", userId);
        Long num = (Long) resNoticeDao.getByHQLWithNamedParams(hql,param);
        return num.intValue();
    }

    public List<ResNotice> getListNotice(Integer userId, Integer resHisId, String batchUuid, String type, String isSigned){
        String hql = " from ResNotice where type = :type";
        HashMap<String,Object> param = new HashMap<>();
        param.put("type", Byte.valueOf(type));
        hql = this.resNoticeHql(hql, param, userId, resHisId, batchUuid, isSigned);
        List<ResNotice> list = resNoticeDao.getListByHQLWithNamedParams(hql,param);
        return list;
    }

    public int resNoticeNumByType(Integer userId, Integer resHisId, String batchUuid, String type, String isSigned){
        String hql = "SELECT count(id) FROM ResNotice where type = :type";
        HashMap<String,Object> param = new HashMap<>();
        param.put("type", Byte.valueOf(type));
        hql = this.resNoticeHql(hql, param, userId, resHisId, batchUuid, isSigned);
        Long num = (Long) resNoticeDao.getByHQLWithNamedParams(hql,param);
        return num.intValue();
    }

    private String resNoticeHql(String hql, HashMap<String,Object> param, Integer userId, Integer resHisId, String batchUuid, String isSigned){
        if (userId != null) {
            hql = hql + " and user = :user";
            param.put("user", userId);
        }
        if (resHisId != null) {
            hql = hql + " and resourceHistory = :resourceHistory";
            param.put("resourceHistory", resHisId);
        }
        if (batchUuid != null) {
            hql = hql + " and batchUuid = :batchUuid";
            param.put("batchUuid", batchUuid);
        }
        if (isSigned != null) {
            hql = hql + " and isSigned = :isSigned";
            param.put("isSigned", Byte.valueOf(isSigned));
        }
        return hql;
    }


}
