package cn.sphd.miners.modules.resourceAuthority.task;

import cn.sphd.miners.modules.resourceAuthority.service.ReadRoomService;
import cn.sphd.miners.modules.system.service.DlmService;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.ParseException;
import java.text.SimpleDateFormat;

/**
 * Created by 朱思旭 on 2021/4/6.
 */
public class ReadRoomTask {

    @Autowired
    DlmService dlmService;
    @Autowired
    ReadRoomService readRoomService;



    public void stopUsingReadRoomTaskDay() throws ParseException {
        //获取分布式锁
        String lockKey, methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        if ((lockKey=dlmService.getLock(methodName)) != null) {
            System.out.println("阅览时定时任务开始：" + new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
            readRoomService.cleanedReadFileByDaysAgo();
            System.out.println("阅览时定时任务结束：" + new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
            dlmService.releaseLock(methodName, lockKey);
        }
    }

}
