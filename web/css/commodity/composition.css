input,select{ border:1px solid #ccc; display:inline-block; padding:0 5px; background:#fff;   }
input[disabled]{ background:#eee;  }
.goods_update,.goods_scan{ display:none;  }

/*  多层弹框   */
.compotionBounce{ display:none; position: absolute; width:100%; z-index:22; min-height:100%; background:rgba(0,0,0,0.3);  overflow: hidden; padding-bottom:50px;    }
.compotionBounce>div{background:#fff; position: absolute; margin-top:200px; margin-left:10%; width: 80%;  min-height: 300px; padding-bottom:50px; border:1px solid #ccc; box-shadow:0 0 15px 5px #a8a8a8; border-radius:4px;   }
.keep{ display:none ;  width:100% ; height:100%; margin-left:0!important; position:absolute;    }
.bounceCompotion{ padding-bottom: 50px;  }
.bounceCompotion>div.bounceCompotion_titl{  margin-left:0;  }
.bounceCompotion>div{ margin:0 0 5px 50px;   }
.bounceCompotion>div.bounceCompotion_con{ margin:0 30px 30px 50px;   }
.bounceCompotion_titl{ background: #F2F2F2; padding:10px 0 ;    }
.bounceCompotion_titl>span{ float:left; font-size:23px; color:#333;}
.bounceCompotion_titl>span:nth-child(2){ margin-top:10px;  }
.bounceCompotion_titl>a{  position:relative; top:7px;  }
.bounceCompotion_titl>a{ float:right }
.bounceCompotion_head{  font-size:16px; }

.bounceCompotion_con table tr td:last-child>span{ cursor:pointer;   }
.bounce{ position:absolute!important; height:100%!important;  }
.bounceCompotion_head>div:first-child{  line-height:40px;   }
.bounceH_ttl, .bounceH_con{  font-size:18px;   }
.bounceH_ttl{ color:#666;  }
.bounceH_con{ color:#ccc; padding-right:40px;   }
.bounceH_4{ width:24%; float:left; margin-bottom: 15px; }
.bounceH_4>select, .bounceH_4>input{  min-width:150px; width:70%; height:40px;  }
.bounceH_4>select>option{ }
.addBtnContainer{ margin-bottom:15px; border-bottom:1px solid #0a6aa1; display: inline-block;color: #0a6aa1; font-size:16px;     }
.addBtnContainer span{   }
.addBtnContainer:hover{ color: #00AEED; cursor:pointer; border-bottom:1px solid #00AEED; }
.addBtnContainer>span>span{ padding:0 0 0 20px!important;  }
.bonceCon-center>div{ margin:10px 0 ; }
.bonceCon-center input{ height:35px ; width:300px;   }

.bounce_con{  width:300px; display: inline-block;position: relative; left:-3px; }
.mt_conainer,#gs_conainer{ display:none; width:100%; max-height:200px; overflow-y:scroll; position:absolute; z-index: 1;
    background:#fff;border:1px solid #ccc; left:0; top:34px;   }
.val{ display: none;  }
.chargeItem, .chargeItem>span, .chargeItemCode, .chargeItemCode>span{ cursor:pointer; padding:5px 0;  }
.chargeItem:hover,.chargeItemCode:hover{ background: #eef4f4!important;  }
.chargeItem ,.chargeItemCode { height:30px;   }
.mt_conainerGoods{
    display: none ;
    position: absolute;
    z-index: 9999; background: #fff; border: 1px solid #ccc;
    width: 300px;
}

.bonceCon-center input{
    width: 299px!important;
}













