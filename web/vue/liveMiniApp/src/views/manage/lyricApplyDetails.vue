<template>
  <div class="lyricApplyDetails">
    <Pageheader title="歌词任务" :showBack="true"/>
    <div class="" v-if="songInfo.song">
      <div class="txtCenter">
        <div class="ttl">{{ songInfo.song.name }}</div>
        <div>{{ songInfo.song.songDuration }}</div>
      </div>
      <div class="trAll">
        <div class="tr"><span class="t1">说明：</span><span class="t2">{{ songInfo.song.inllustration }}</span></div>
        <div class="tr"><span class="t1">原唱：</span><span class="t2">已选择</span><span class="line-link" @click="playMusic(1)">播放</span></div>
        <div class="tr"><span class="t1">伴奏：</span><span class="t2">已选择</span><span class="line-link" @click="playMusic(2)">播放</span></div>
        <div class="tr"><span class="t1">第一句：</span><span class="t2">{{ songInfo.song.firstSentence }}</span></div>
        <div class="tr"><span class="t1">最后一句：</span><span class="t2">{{ songInfo.song.lastSentence }}</span></div>
        <div class="tr"><span class="t1">任务发布：</span><span class="t2">{{ songInfo.song.createName }} {{ new Date(songInfo.song.createTime).format('yyyy-MM-dd hh:mm:ss')  }}</span></div>
        <div class="tr"><span class="t1">接收对象：</span>
          <span class="t2">{{ songInfo.song.taskListAllUser }} </span>
        </div>
      </div>

    </div>
    <div class="live-container">
      <span @click="cancelTask()" class="primary live-button">撤销任务</span>
    </div>

    <AudioPlayer v-if="playLyc" :playName="playName" :fileUrl="playUrl" @closePlayLyc="closePlayLyc"></AudioPlayer>

  </div>
</template>
<script setup>
import { ref, onMounted, reactive } from 'vue';
import { useRouter } from "vue-router";
import { ElMessageBox, ElMessage } from 'element-plus';
import JSONBig from 'json-bigint'
import '@/utils/DateFormat.js'
import { getLyriTaskMes, antorCancelTask } from '@/api/music.js'

const router = useRouter();
const songInfo = ref({})
const playLyc = ref(false)
const playName = ref('')
const playUrl = ref('')
const uploadUrl = localStorage.getItem('uploadUrl');
const lyricDetailsItem = JSONBig.parse(localStorage.getItem('lyricDetailsItem'));
console.log('lyricDetailsItem', lyricDetailsItem)
const songID = lyricDetailsItem.id
onMounted(() => {
  getDetails()
});

const closePlayLyc = (val)=> {
  playLyc.value = val
}
const playMusic = (type) => {
  playLyc.value = true
  console.log('songInfo', songInfo)
  let info = songInfo.value.song
  playName.value = info.name
  if(type === 1){ // y原唱
    playUrl.value = uploadUrl + info.originalPath
  }else {
    playUrl.value = uploadUrl + info.bgmPath
  }
  console.log('playUrl.value=', playUrl)
  console.log(' playName.value=',  playName)
}
const getDetails = () => {
  getLyriTaskMes({ id: songID }).then(res => {
    console.log('getLyriTaskMes', res)
    songInfo.value = res.data

  }).catch(err => {
    conole.log('getDetails err', err)
  })
}
const goPage = (url) => {
  router.push({ name: url});
}
const cancelTask = () => {
  ElMessageBox.confirm('<p>确定后，相关数据将全部消失。</p>确定撤销吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    center: true,
    dangerouslyUseHTMLString:true,
  })
  .then(() => {
    antorCancelTask({ id: songID }).then(res => {
      console.log('antorCancelTask res=', res)
      let state = res.data.state
      if(state === 1){
        ElMessage.success('撤销成功')
        goPage('lycTaskApply')
      }else if(state === 2) {
        ElMessage.error('已上传节奏不能撤回')

      }else{
        ElMessage.error('其他原因撤回失败')

      }
    }).catch(err => {
      console.log('err', err)
    })
  })
  .catch(() => {
    // ElMessage.error('其他原因撤回失败')
  })


}
</script>
<style lang="scss" scoped>
.lyricApplyDetails{
  .txtCenter{
    text-align: center;
    margin-bottom: 30px;
    margin-top: 30px;
  }
  .houseSty8{
    position: relative;
    top:5px;
  }
  .ttl{
    font-weight: bolder; line-height: 30px;
  }
  .trAll{
    margin-bottom: 30px;
    padding:20px;
    background: #fff;
    .tr{
      line-height: 30px;
      .t1{
        width: 80px; display: inline-block;
      }
      .t2{
        margin-right: 30px;
        margin-left: 20px;
        display: inline-block;
      }
      .t3{

      }
    }
  }
}
</style>
