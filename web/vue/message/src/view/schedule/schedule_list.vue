<template>
  <div id="payList">
    <TY_NavTop title="待处理的提醒" :isBackHome="isBackHome"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="tipPane">您的日程产生了以下待处理的提醒</div>
      <div class="ui-cells">
        <a class="ui-cell" v-on:click="jumpDetail(item.schedule, item.id, item.job)" v-for="(item, index) in scheduleList" v-bind:key="index">
          <div class="ui-cell__hd"></div>
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <div class="ui-row">
                {{item.content}}
              </div>
              <div class="ui-row">
                <small>{{item.memo}}</small>
              </div>
            </div>
          </div>
          <div class="ui-cell__ft"></div>
        </a>
      </div>
    </div>
  </div>
</template>

<script>
import { incomeType, formatDate } from '../../js/common'
export default {
  name: 'memoJobMsg',
  filters: {
    incomeType,
    formatDate
  },
  data () {
    return {
      loading: true,
      scheduleList: [],
      listenersUid: []
    }
  },
  created () {
    let _this = this
    _this.$http.post('../../../schedule/getMemoJobMsgList.do', {
      userId: _this.sphdSocket.user.userID
    }, {
      emulateJSON: true
    }).then((response) => {
      let data = response.body.data
      console.log(JSON.stringify(data))
      _this.loading = false
      if (data) {
        // 赋值导航列表（根据pid=0获取一级导航）
        _this.scheduleList = data
      } else {
        _this.$message({
          type: 'error',
          message: '获取详情失败！'
        })
      }
    }).catch(function () {
      _this.$message({
        type: 'error',
        message: '系统错误，请重试！'
      })
    })
    _this.scheduleList = [
      this.sphdSocket.subscribe('memoJobMsg', function (data) {
        let reqData = JSON.parse(data)
        let update = reqData.operate
        let newDate = {}
        if (update > 0) { // 增加一条
          newDate = reqData.memoJobMsg
          _this.scheduleList.push(newDate)
        } else if (update < 0) { // 减少一条
          let deletId = reqData.memoJobMsg.schedule
          _this.scheduleList.forEach(function (item, index) {
            if (deletId === item.schedule) {
              _this.scheduleList.splice(index, 1)
            }
          })
        }
      }, null, 'user')
    ]
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    jumpDetail: function (schedule, id, jobId) {
      this.$router.push({
        path: `/scheduleDetail/${schedule}/${id}/${jobId}`
      })
    },
    screen: function () {
      let type = 1
      this.$router.push({
        path: `/payApplyScreen/${type}`
      })
    }
  }
}
</script>
