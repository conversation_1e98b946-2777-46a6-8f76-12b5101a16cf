<template>
  <div class="home" id="home">
    <div class="nav-bar">
      <el-button style="position: absolute;left: 8px;top: 10px;" type="" circle v-if="goback" icon="el-icon-arrow-left" @click="backAnswer"></el-button>
      <div class="nav-bar__title">{{ title }}</div>
    </div>
    <div class="tip" style="white-space: pre-wrap;">
      {{ preface }}
    </div>
    <div v-if="!answerInfo">
      <div class="itemCon" v-for="(item, index) in questionList" v-bind:key="index">
        <h4>{{item.name}}</h4>
        <div style="margin-top:25px; " v-for="(question, qindex) in item.investigateQuestionList" v-bind:key="qindex">
          <div v-if="question.type === '3' || question.type === '2' "> <!--单选， 判断-->
            <div>
              <div class="quest van-cell__title van-field__label">
                <span class="opZ"><i class="red" v-if="question.isRequired === 1">*</i></span>
                {{question.no }}. {{question.content}}
              </div>
              <div v-for="(option, opindex) in question.investigateQuestionKeyList" v-bind:key="opindex">
                <div>
                   <span class="ty-radio" @click="clickitem(option.id, question, $event)">
                    <span :class="['radio-icon', question.ans == option.id ? 'ra-check': '']"></span>
                    <span :class="[ question.ans == option.id ? 'txt-check': '']">{{option.content}}</span>
                  </span>
                  <div v-if="option.isOher">
                    <el-input type="text" v-show="question.otherTextShow" v-model="question.otherText" :placeholder="option.memo"></el-input>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else-if="question.type === '4'" > <!--多选-->
            <div>
              <div class="quest van-cell__title van-field__label">
                <span class="opZ"><i class="red" v-if="question.isRequired === 1">*</i></span>
                {{question.no}}. {{question.content}}
              </div>
              <div class="checkArea">
                <el-checkbox-group v-model="question.ans">
                  <el-checkbox v-for="(option, opindex) in question.investigateQuestionKeyList"
                               v-bind:key="opindex" v-model="question.ans" :label="option.id">{{ option.content }}</el-checkbox>
                </el-checkbox-group>
              </div>
            </div>
          </div>
          <div v-else-if="question.type === '1'"> <!--填空-->
            <div class="quest van-cell__title van-field__label">
              <span class="opZ"><i class="red" v-if="question.isRequired === 1">*</i></span>
              {{question.no}}. {{question.content}}
            </div>
            <div class="porelatv">
              <div v-if="question.ans.length > 0" class="poAbs" @click="clearTxt(question, 'ans')"></div>
              <van-field border v-model="question.ans" :placeholder="question.memo" :required = "question.isRequired === '1'" :right-icon="question.ans.length > 0? 'close' : ''" />
            </div>
          </div>
          <div v-else-if="question.type === '9'"> <!-- 特殊题型 -->
            <div v-if="question.isMultiple === 8" > <!-- 可以多次输入的题 -->
              <div class="quest van-cell__title van-field__label">
                <span class="opZ"><i class="red" v-if="question.isRequired === 1">*</i></span>
                {{question.no}}. {{question.content}}
              </div>
              <div>
                <p>1</p>
                <div class="pannel">
<!--                  {{question.content}}-->
                  <div class="porelatv">
                    <div v-if="question.ans.length > 0" class="poAbs" @click="clearTxt(question, 'ans')"></div>
                    <van-field  border v-model="question.ans" :placeholder="question.memo" :required = "question.isRequired === '1' " :right-icon="question.ans.length > 0? 'close' : ''" />
                  </div>
                </div>
              </div>
              <div v-for="(otherAns, aindex) in question.otherAnsList" v-bind:key="aindex">
                <p>
                  {{ aindex+2 }}
                </p>
                <div class="pannel">
                  {{question.content}}
                  <span class="delInput" @click="delInput(question,aindex)" >
                    -
                  </span>
                  <div class="porelatv">
                    <div v-if="otherAns.ans.length > 0" class="poAbs" @click="clearTxt(otherAns, 'ans')"></div>
                    <van-field  border v-model="otherAns.ans" :placeholder="question.memo" :right-icon="otherAns.ans.length > 0? 'close' : ''" />
                  </div>
                </div>

              </div>
              <div @click="addInput(question)" v-if="question.otherAnsList.length<8">
                <van-button block icon="plus" type="default">增加</van-button>
              </div>
            </div>
            <div v-else-if="question.specialModel === '1'"> <!--录入必须为电子邮箱格式的问答题-->
              <div class="quest van-cell__title van-field__label">
                <span class="opZ"><i class="red" v-if="question.isRequired === 1">*</i></span>
                {{question.no}}. {{question.content}}
              </div>
              <div class="porelatv">
                <div v-if="question.ans.length > 0" class="poAbs" @click="clearTxt(question, 'ans')"></div>
                <van-field  border
                            v-model="question.ans"
                            :placeholder="question.memo"
                            :error-message="question.errorText"
                            @change="formatMailAndPhone(question)"
                            :right-icon="question.ans.length > 0? 'close' : ''"
                            :required = "question.isRequired === '1'"/>
              </div>

            </div>
            <div v-else-if="question.specialModel === '2'"> <!--录入必须为11位的手机号的问答题-->
              <div class="quest van-cell__title van-field__label">
                <span class="opZ"><i class="red" v-if="question.isRequired === 1">*</i></span>
                {{question.no}}. {{question.content}}
              </div>
              <div class="porelatv">
                <div v-if="question.ans.length > 0" class="poAbs" @click="clearTxt(question, 'ans')"></div>
                <van-field border error-message="手机号格式错误"
                           v-model="question.ans"
                           :placeholder="question.memo"
                           :error-message="question.errorText"
                           @change="formatMailAndPhone(question)"
                           :right-icon="question.ans.length > 0? 'close' : ''"
                           :required = "question.isRequired === '1'"/>
              </div>

            </div>
            <div v-else-if="question.specialModel === '3'"> <!--省、市、地区或地址的题-->
              <div v-if="['1','2','3','4'].indexOf(question.specialTab) > -1">
                <div class="quest van-cell__title van-field__label">
                  <span class="opZ"><i class="red" v-if="question.isRequired === 1">*</i></span>
                  {{question.no}}. {{question.content}}
                </div>
                <div class="porelatv">
                  <div v-if="question.ans0.length > 0" class="poAbs" @click="clearTxt(question, 'ans0')"></div>
                  <van-field readonly clickable :value="question.ans0" placeholder="请选择地址" @click="openPicker(question,question.specialTab)" :right-icon="question.ans0.length > 0? 'close' : ''"/>
                </div>

                <div v-if="question.specialTab ==='4'"><!-- 填写具体地址-->
                  <div class="porelatv">
                    <div v-if="question.ans.length > 0" class="poAbs" @click="clearTxt(question, 'ans')"></div>
                    <van-field autosize border v-model="question.ans" :placeholder="question.memo" :required = "question.isRequired === '1'" :right-icon="question.ans.length > 0? 'close' : ''"/>
                  </div>
                </div>
              </div>
            </div>
            <div v-else-if="question.specialModel === '4'"> <!--年、月或日期的题-->
              <div class="quest van-cell__title van-field__label">
                <span class="opZ"><i class="red" v-if="question.isRequired === 1">*</i></span>
                {{question.no}}. {{question.content}}
              </div>
              <!--<p>年、月或日期的题</p>-->
              <div class="porelatv">
                <div v-if="question.ans.length > 0" class="poAbs" @click="clearTxt(question, 'ans')"></div>

                <div v-if="question.specialTab ==='1'"><!-- 选项为年历 -->
                  <van-field readonly clickable :value="question.ans" placeholder="选择年份" @click="openDateDialog(question)" :right-icon="question.ans.length > 0? 'close' : ''"/>
                </div>
                <div v-if="question.specialTab ==='2'"><!-- 选项为月历 -->
                  <van-field readonly clickable :value="question.ans" placeholder="选择年月" @click="openDateDialog(question)" :right-icon="question.ans.length > 0? 'close' : ''"/>
                </div>
                <div v-if="question.specialTab ==='3'"><!-- 选项为日历 -->
                  <van-field readonly clickable :value="question.ans" placeholder="选择年月日" @click="openDateDialog(question)" :right-icon="question.ans.length > 0? 'close' : ''"/>
                </div>

              </div>


            </div>
          </div>
          <div v-else>
            <h5>无法识别的题型</h5>
          </div>
        </div>
      </div>
      <van-button v-show="showBtn" class="submit" type="primary" round block color="linear-gradient(to right, #83b7f1, #4084e1)" @click="submit2">提交</van-button>
    </div>
    <div v-if="answerInfo" style="background: #fff; padding: 15px;">
      <div class="itemCon" v-for="(item, index) in questionList" v-bind:key="index">
        <div style="margin-top:25px; " v-for="(question, qindex) in item.investigateQuestionList" v-bind:key="qindex">
          <div class="questinfottl">
            <span class="opZ"><i class="red" v-if="question.isRequired === 1">*</i></span>
            {{question.no}}{{question.content}}
          </div>
          <div v-for="(as, aIndex) in question.investigateAnswerList" v-bind="aIndex">
            <div class="textCon">{{ as.content || "" }}</div>
          </div>
        </div>
      </div>
    </div>
    <van-popup v-model="showPicker" position="bottom">
      <van-picker v-if="showPicker" show-toolbar :columns="columns" @cancel="showPicker = false"
                  @confirm="onConfirm" @change="onChange"/>
      <!--:default-index="defaltIndex"-->
    </van-popup>
   <!-- <van-calendar v-model="showDate"
                  :title="placeholder"
                  @cancel="showDate = false"
                  @confirm="confirmDate" />-->

    <van-dialog v-model="showDate" :title="placeholder" :show-confirm-button="false">
      <van-datetime-picker v-model="selectDate" :type="dateType" :title="placeholder"
                           :min-date="minDate"
                           :max-date="maxDate"
                           :show-confirm="false"
                           @confirm="confirmDate" @cancel="showDate = false"/>

    </van-dialog>

  </div>
</template>

<style>
  #home .delInput{     display: inline-block;
    width: 20px;
    height: 20px;
    border-radius: 10px;
    background: rgba(242,74,53,.71);
    line-height: 15px;
    text-align: center;
    color: #fff;
    font-size: 24px;
    margin-bottom: 10px;
    float: right;  }
  #home .pannel{ background: #fff; padding:5px 10px;  }
  #home .van-dialog__footer{ display: none!important; }
  #home .van-field{border: 1px solid #ddd;  border-radius: 4px; margin-top: 5px; }
  #home{ background:#fff;  }
  #home .checkArea{ word-break: break-all; }
  .ty-radio{ display: block; line-height:30px; color: #606266; font-size:14px; margin:10px;  }
  .radio-icon{ display: inline-block; border:1px solid #dcdfe6; width:14px; height:14px; margin-right:5px; border-radius:7px; position: relative; top: 3px; }
  .ra-check{  border:6px solid #409eff!important; width: 4px; height: 4px; }
  .txt-check{ color:#409eff ;  }
  body{ padding:0; margin: 0; }
  /*.el-checkbox__label{ line-height: 30px; position: relative;  top: -26px; margin-left: 20px;}*/
  .opZ{ display:inline-block; width:8px; text-align: center; margin-left:-25px;   }
  .red{ color:red; }
  .el-input{ min-width: 300px!important; }
  .el-radio__input:visited, .el-radio__input:active,el-radio__inner:active{ box-shadow:none!important; border-color:#ddd;   }
  input:focus,el-radio__inner:after { border:none!important; box-shadow:none!important;   }
  .el-radio__inner:hover{ border-color: #dcdfe6!important; }
  .van-field__control{ border: 0; border-bottom: 1px solid #ddd; }
  .van-field__control{ border-bottom:1px solid #ddd;  }
  #home .submit{ margin:40px auto!important;  width: 80%;  }
  #home .el-radio-group{ display:block;   }
  #home .el-radio-group{ width:70px;   }
  #home .el-button.is-circle{ padding:8px;  }
  #home .questinfottl{ color:#333; margin-top:10px; font-size:15px;   }
  #home .textCon{ color:#df7234; font-size:15px; }
  .van-cell{ display: block!important; }
  .van-radio-group{ margin-left:30px;  }
  .itemCon{ padding: 0 15px; }
  .quest{ font-size: 16px; color: #000; margin-left: 20px; }
  .van-radio,.van-checkbox { font-size: 14px; margin: 10px; }
  .van-field__label { width:90%!important;  }
  .box{ padding:8px 0; border-bottom: 8px solid #eee; }
  .right{  text-align: right;  }
  #app .van-button{  margin: 4px 0;  }
  .nav-bar{ position: relative; height: 46px; line-height: 46px; text-align: center; background-color: #fff; -webkit-user-select: none; user-select: none; }
  .nav-bar__left{ position: absolute; left: 16px; bottom: 0 }
  .nav-bar__title{ margin: 0 auto; color: #323233; font-weight: 500; font-size: 18px; padding-top:20px;    }
  .nav-bar__right{ position: absolute; bottom: 0; right: 16px; }
  .el-radio, .el-checkbox{ line-height: 30px!important; }
  #home .el-checkbox{ white-space: normal; display: block; margin:10px;  }
  .tip{ color: #df7234; font-size: 14px; padding:15px; text-indent:2em; background: #fff; }
  .porelatv{
    position: relative; margin-top: 10px;
  }
  .poAbs{
    /*background: rgba(0,0,0,0.3);*/
    width: 42px; height: 45px; position: absolute; right: 0; z-index: 1;
  }
</style>

<script>
import JsEncrypt from 'jsencrypt/bin/jsencrypt'
import area from '../assets/area.json'
Date.prototype.format = function(format) {

    var o = {
      "M+": this.getMonth() + 1,
      "d+": this.getDate(),
      "h+": this.getHours(),
      "m+": this.getMinutes(),
      "s+": this.getSeconds(),
      "q+": Math.floor((this.getMonth() + 3) / 3),
      "S": this.getMilliseconds()
    };
    if (/(y+)/.test(format)) {
      format = format.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    }
    for (var k in o) {
      if (new RegExp("(" + k + ")").test(format)) {
        format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
      }
    }

  return format;
};
export default {
  name: 'home',
  data () {
    return {
      title: '',
      preface: '',
      defaltIndex: 1,
      pickerDateOrArea: 'date',
      showPicker: false,
      showDate: false,
      placeholder:'',
      selectDate:'',
      maxDate:'',
      minDate:'',
      dateType:'',
      editObj: null,
      description: '',
      area: area,
      status: '',
      Objective: '',
      goback: false,
      showBtn: false,
      answerInfo: false,
      questionList: [], // 问题列表
      ansList: [], // 选项集合
      columns: []
    }
  },
  created () {
    this.$toast.loading({
      message: '正在加载...',
      forbidClick: true,
      loadingType: 'spinner'
    })
    // wyu：打印base传过来的参数
    console.log(this.$route.params)
    this.goback = this.$route.params.goback || false
    this.getQuestion2()
  },
  mounted(){
    this.initAreaData()
  },
  watch:{
    // questionList:{
    //   handler(newList, oldVal) {
    //     console.log('newVal', newList)
    //     console.log('oldVal', oldVal)
        // let newAnsList = []
        // let list = JSON.parse(JSON.stringify(newList))
        // list.forEach(function (item, index) {
        //   let questions = item.investigateQuestionList
        //   questions.forEach(function (ques, qindex) {
        //     if (ques.type === '1') { // 填空
        //     } else if (ques.type === '5') { // 城市多选
        //     } else if(ques.type === '9'){
        //
        //     } else {
        //       if(ques.ans.length > 0){
        //         if(ques.type === '3' || ques.type === '2'){ // 单选
        //           newAnsList.push(ques.ans)
        //         }else{
        //           newAnsList.push(...ques.ans)
        //         }
        //       }
        //     }
        //   })
        // })
        // this.ansList = newList
        // console.log(newAnsList)
      // },
      // deep: true,
      // immediate: false
    // }
  },
  methods: {
    formatMailAndPhone:function(ques){
      let pattern = ''
      let tip = ''
      let val = ques.ans
      if(val.length == 0){
        ques.errorText =  '';
      }else if(ques.specialModel === '1'|| ques.specialModel === '2'){
        if(ques.specialModel === '2'){ // 手机号
          pattern = /(^((\(\d{2,3}\))|(\d{3}\-))?(\(0\d{2,3}\)|0\d{2,3}-)?[1-9]\d{6,7}(\-\d{1,4})?$)|(^(0|86|17951)?(13[0-9]|15[012356789]|16[012356789]|17[0135678]|18[0-9]|14[57])[0-9]{8}$)/;
          if (pattern.exec(val)==null) {
            ques.errorText = '请输入正确的手机号'
          }else {
            ques.errorText =  '';
          }
        }else { // 邮箱
          pattern = /\w[-\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\.)+[A-Za-z]{2,14}/;
          if (pattern.exec(val)==null){
            ques.errorText = '请输入正确的邮箱'
          }else {
            ques.errorText =  '';
          }
        }
      }
    },
    delInput:function(question, ansIndex){
      question.otherAnsList.splice(ansIndex,1);
    },
    addInput:function (question) {
      let lastAns = question.ans
      if(question.otherAnsList.length > 0){
        question.otherAnsList.forEach(function(otherAns){
          lastAns = otherAns.ans
        })
      }
      if(lastAns.length > 0){
        question.otherAnsList.push({ 'ans':'' })
      }else{
        this.$toast.fail('当前项不能为空！')
      }
    },
    clickitem:function (op, question, event) {
      op === question.ans ? question.ans = '' : question.ans = op
    },
    submit2 () {
      let _this = this
      console.log(this.questionList)
      let data = {
        'publish': this.$route.params.id ,
        'hashKey': this.$route.params.hashKey ,
      }
      let surveyAnswers = []
      let count = 0
      let coun2 = 0 // 多个输入框的 多出来的必填
      let list = JSON.parse(JSON.stringify(this.questionList))
      let errorCount = 0
      list.forEach(function (item, index) {
        let questions = item.investigateQuestionList
        // 检查必填项
        questions.forEach(function (ques, qindex) {
          if( ques['errorText'].length > 0){
            errorCount++
          }
          let caseType = 0 ; // 1- 直接判断的
          if ( ques.type === '1' ) { // 问答
            caseType = 1
          } else if( ['4','3','2'].indexOf(ques.type) > -1 ){ // 多选题， 判断题，单选题
            ques.ans = String(ques.ans)
            caseType = 11
          } else if(ques.type === '9'){
            if(Number(ques.isMultiple) === 8){ // 添加多个输入框
              caseType = 1
              if(ques.otherAnsList.length > 0){
                caseType = 2
                ques.otherAnsList.forEach(function (otherAns) {
                  if(otherAns.ans.length === 0){
                    coun2++
                  }
                })
              }
            } else if(ques.specialModel === '1' || ques.specialModel === '2'   ){ // 邮箱 手机号
              caseType = 1
            } else if(ques.specialModel === '4'){ // 年月日
              caseType = 1
            } else if(ques.specialModel === '3'){ // 城市
              caseType = 4
              if(ques.specialTab === '4'){
                caseType = 3
              }
            }
          }
          if(caseType < 3 || caseType === 11){
            if (ques.ans.length === 0 ) {
              if (Number(ques.isRequired) == 1) {
                count++
              }
              console.log(ques)
            }
            if(caseType === 1){ // 只有输入框的

            }else if(caseType === 2){ // 输入框 还有 多个输入框的

            }
          } else {
            if (ques.ans0.length === 0 ) {
              if (Number(ques.isRequired) == 1) {
                count++
              }
              console.log(ques)
            }
            else if(caseType === 3){ // 地址的且可以填详细地址的
              if (ques.ans.length === 0 ) {
                if (Number(ques.isRequired) == 1) {
                  count++
                }
                console.log(ques)
              }
            }
          }
          ques.caseType = caseType


        })
      })
      if (count > 0) {
        _this.$toast.fail('请将必填项补充完整！')
      } else if (coun2 > 0) {
        _this.$toast.fail('请将多个输入框的内容项补充完整！')
      } else if(errorCount > 0){
        _this.$toast.fail('提交失败，注意标红处！')
      } else { // 开始搞传参
        list.forEach(function (item, index) {
          let questions = item.investigateQuestionList
          questions.forEach(function (ques, qindex) {
            if(ques.ans){
              let ansList = ques.ans.split(',')
              ansList.sort((a, b) => a - b);
              ques.ans = ansList.join()
            }
            let caseType = ques.caseType
            if(caseType === 1){ // 只有输入框的
              surveyAnswers.push({ 'question': ques.id, 'content': ques.ans })
            }
            else if(caseType === 2){ // 输入框 还有 多个输入框的
              surveyAnswers.push({ 'question': ques.id, 'content': ques.ans })
              ques.otherAnsList.forEach(function(otherAns){
                surveyAnswers.push({ 'question': ques.id, 'content': otherAns.ans })
              })
            }
            else if(caseType === 3){ // 地址的且可以填详细地址的
              let areaContxt = ''
              if(ques.ans0.length > 0 && ques.ans.length > 0){
                areaContxt = ques.ans0 + '--' + ques.ans;
              }
              else if(ques.ans0.length == 0 && ques.ans.length == 0){
                areaContxt = ""
              }
              else{
                if(ques.ans0.length > 0 ){
                  areaContxt = ques.ans0 ;
                }
                if(ques.ans.length > 0){
                  areaContxt += '--' + ques.ans
                }
              }

              surveyAnswers.push({ 'question': ques.id, 'content': areaContxt })
            }
            else if(caseType === 4){ // 只选择省市区
              surveyAnswers.push({ 'question': ques.id, 'content': ques.ans0 })
            }
            else if(caseType === 11){ // 单选 多选 判断
              let con = ''
              ques.investigateQuestionKeyList.forEach(function (op) { // 遍历选项
                if (ques.type === '3' || ques.type === '2') { // 单选、判断
                  if (ques.ans === String(op.id)) {
                    con = op.content
                  }
                } else if (ques.type === '4'){ // 多选
                  let i = ques.ans.indexOf(op.id)
                  if (i > -1) {
                    con += op.content + ','
                  }
                }
              })
              surveyAnswers.push({ 'question': ques.id, 'content': con, 'answer': ques.ans })
            }
          })
        })
        _this.showBtn = false
        this.$toast.loading({
          message: '提交中...',
          forbidClick: true,
          loadingType: 'spinner'
        })
        data['investigateAnswerJson'] = JSON.stringify(surveyAnswers)
        let url = window.parent.$.webRoot + '/investigationPublish/addInvestigateObject.do'
        if(_this.status === 1 ){

          url = window.parent.$.webRoot + '/investigationPublish/updateInvestigateObject.do'
          data['objective'] = _this.$route.params.objective
        }
        console.log('就要掉接口了')
        console.log(data)
        this.$http.post(url, data, {
          emulateJSON: true
        }).then((response) => {
          _this.$toast.clear()
          console.log(response.body)
          let res = response.body
          let success = res.success
          console.log(success)
          if (Number(success) === 1) {
            ///// res.data
            let wonderssInvestigationObject = res.data
            if (typeof wonderssInvestigationObject === 'string' && wonderssInvestigationObject.length > 40) {
              let shaVal = wonderssInvestigationObject.substr(0, 40)
              let data = wonderssInvestigationObject.substr(40)
              try {
                let keys = JSON.parse(this.decrypt(decodeURIComponent(data)))
                console.log('答完题 wonderssInvestigationObject 解出来的数据=', keys)
                let ansList = localStorage.getItem('ansList')
                if(ansList){
                  ansList = JSON.parse(ansList)
                }else{
                  ansList = []
                }
                let havSame = false
                ansList.forEach(ansItem => {
                  if(ansItem.id === keys.id){
                    havSame = true
                    ansItem.answerTime = keys.answerTime
                    ansItem.hashKey = keys.hashKey
                    ansItem.objective = keys.objective
                    ansItem.org = keys.org
                  }
                })
                if(havSame){ // 之前答过题，有记录

                }else{ // 新答得问卷
                  ansList.push(keys)
                }
                localStorage.setItem('ansList', JSON.stringify(ansList))

              } catch (e) {
                console.log('数据错误 ！', e)
              }
            }



            /////


            window.localStorage.setItem('wonderssInvestigationObject', res.data)
            console.log("save wonderssInvestigationObject",window.localStorage.getItem('wonderssInvestigationObject'))
            console.log(window.localStorage.getItem('wonderssInvestigationObject').length)
            this.$toast.clear()
            this.$router.replace({name: 'success'})
          } else {
            this.jumpError('没有提交成功哦，<br>请重新扫码提交！')
          }
        }).catch(function (err) {
          console.log(err)
          this.jumpError('提交失败，请重新扫码提交！')
          _this.loading = false
          _this.$toast.fail('提交失败，请重新扫码提交！')
        })
      }
    },
    decrypt (data) {
      let jse = new JsEncrypt()
      jse.setPrivateKey(`**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`)
      return jse.decrypt(data)
    },
    jumpError (message) {
      this.$toast.clear()
      this.$router.replace({name: 'error', params: {message: message}})
    },
    backAnswer () {
      let params = this.$route.params
      this.$router.replace({ name: 'answer',  params: params })
    },
    getQuestion2 () {
      let _this = this
      let id = this.$route.params.id
      let isHistory = this.$route.params.isHistory
      let hisID = this.$route.params.hisID
      let hashKey = this.$route.params.hashKey
      console.log('查询id:' + id)
      let url = '../../../investigationPublish/investigateSubjectDetails.do'
      let data = { 'id': id, 'hashKey':hashKey }
      if(isHistory){
        // url = '../../../investigationPublish/investigateSubjectDetails.do'
        data = { 'id': hisID, 'hashKey':hashKey }
      }
      this.$http.post(url, data, {
        emulateJSON: true
      }).then((response) => {
        _this.$toast.clear()
        console.log(response.body)
        let res = response.body
        _this.title = res.title
        _this.preface = res.preface
        _this.Objective = res.objective
        _this.header = res.header
        document.title = res.header
        _this.description = res.memo
        _this.status = res.status
        let publishEnabled = res.publishEnabled
        console.log('status',  _this.status )
        console.log(' publishEnabled', publishEnabled)
        let List = res.respInvestigateSubjectTagList
        List.forEach(function (item) {
          let quesArr = item.investigateQuestionList
          quesArr.forEach(function (ques) {
            // 1- 单选 3 多选 4-问答
            ques['no'] = ques.orders
            ques['errorText'] =""
            ques['memo'] ="请输入"
            ques['ans'] = ''
            ques['ans0'] = ''
            ques['otherAnsList'] = []
            let ansList = ques.investigateAnswerList || []
            let caseType = 0 ;
            if(ques.type){
              if ( ques.type === '1' ) { // 问答
                caseType = 1
              } else if( ['3','2'].indexOf(ques.type) > -1 ){ // 判断题，单选题
                caseType = 12
              } else if( ['4'].indexOf(ques.type) > -1 ){ // 多选题，
                caseType = 13
                ques['ans'] = []
              } else if(ques.type === '9'){
                if(Number(ques.isMultiple) === 8){ // 添加多个输入框
                  caseType = 1
                  if(ansList.length > 1){
                    caseType = 2
                  }
                } else if(ques.specialModel === '1' || ques.specialModel === '2'   ){ // 邮箱 手机号
                  caseType = 1
                } else if(ques.specialModel === '4'){ // 年月日
                  caseType = 1
                } else if(ques.specialModel === '3'){ // 城市
                  caseType = 4
                  if(ques.specialTab === '4'){
                    caseType = 3
                  }
                }
              }
            }
            if(caseType > 0 && _this.status === 1 && ansList.length > 0){ // 答过题的 给赋值
              let ansaa = ansList[0]
              if(caseType < 3){
                ques['ans'] = ansaa['content']
                if(caseType === 1){ // 只有输入框的
                }else if(caseType === 2){ // 输入框 还有 多个输入框的
                  if(ansList.length > 1){
                    for(let f = 1 ; f < ansList.length ; f++){
                      let oAns = ansList[f]
                      ques['otherAnsList'].push({ 'ans':oAns.content })
                    }
                  }
                }
              } else {
                if(caseType === 3  ){ // 只选择 省市区 + 详细地址
                  console.log('dayin:', ques)
                  console.log('ansaa。content:', ansaa['content'])

                  ques['ans'] = ansaa['content'].split("--")[1] || ''
                  ques['ans0'] = ansaa['content'].split("--")[0] || ''
                }else if(caseType === 4){  // 只选择 省市区
                  ques['ans0'] = ansaa['content']
                }else if(caseType === 12){ // 单选 判断
                  console.log('ansaa。answer：', ansaa['answer'])

                  ques['ans'] = ansaa['answer']||''
                }else if(caseType === 13){ //  多选
                  let ansr = ansaa.answer && ansaa.answer.split(',') || []
                  if(ansr.length > 0){
                    ansr.forEach(function(a){
                      ques['ans'].push(Number(a))
                    })
                  }
                }
              }
            }
          })
        })

        if(publishEnabled === 0){ // 停用了问卷
          if(_this.status === 0){ // 未曾答过题
            _this.jumpError('本次调查已结束，<br>欢迎下次再参与！')
          }else{
            _this.showBtn = false
            _this.answerInfo = true
          }
        }else if(_this.status === 0){ // 未曾答过题
          _this.showBtn = true
        }else if(_this.status === 1){ // 答过题且可修改
          _this.showBtn = true
        }else if(_this.status === 2 ){ // 答过题且不可修改
          _this.showBtn = false
          _this.answerInfo = true
        }
        if (_this.$route.params.isHistory){
          _this.answerInfo = true
        }

        _this.questionList = List
        console.log('answerInfo', _this.answerInfo)
        console.log('初始化之后的list')
        console.log(_this.questionList)
      }).catch(function (err) {
        console.log('跳转错误')
        console.log(err)
        _this.loading = false
        _this.$toast.fail('系统错误，请重试！')
      })
    },
    initAreaData (specialTab) {
      let that = this
      this.pickerDateOrArea = 'area'
      console.log('初始化')
      if(specialTab === 'year'){
        this.pickerDateOrArea = 'year'
        let yearList = []
        for(let y = 1900 ; y < 3000; y++){
          yearList.push(y)
        }
        let curYear = Number(new Date().format("yyyy"))
        let defaltIndex = curYear - 1900
        console.log(this.editObj)
        if(this.editObj.ans){
          let selectYear = this.editObj.ans.substr(0,4)
          defaltIndex = selectYear - 1900
        }
        that.columns = [
          {values: yearList, defaultIndex: defaltIndex, className: 'year'},
        ]
      }else if(specialTab === 'yearMonth'){
        this.pickerDateOrArea = 'yearMonth'
        let yearList = []
        for(let y = 1900 ; y < 3000; y++){
          yearList.push(y)
        }
        let curYear = Number(new Date().format("yyyy"))
        let defaltIndex = curYear - 1900
        let defaltIndex2 = 0
        let monthL = this.getMonthList()
        console.log(this.editObj)
        if(this.editObj.ans){
          let selectYear = this.editObj.ans.substr(0,4)
          defaltIndex = selectYear - 1900
          let str = this.editObj.ans.split('年')[1]
          let selectMonth = str.split('月')[0]
          defaltIndex2 = selectMonth - 1
        }
        that.columns = [
          {values: yearList, defaultIndex: defaltIndex, className: 'year'},
          {values: monthL, defaultIndex:defaltIndex2, className: 'month'},
        ]
      }
      else if(specialTab === 'date'){
        this.pickerDateOrArea = 'date'
        let yearList = []
        for(let y = 1900 ; y < 3000; y++){
          yearList.push(y)
        }
        let curYear = Number(new Date().format("yyyy"))
        let defaltIndex = curYear - 1900
        let defaltIndex2 = 0
        let defaltIndex3 = 0
        let monthL = this.getMonthList()
        let datL = this.getDayList(curYear,1)
        console.log(this.editObj)
        if(this.editObj.ans){
          let selectYear = this.editObj.ans.substr(0,4)
          defaltIndex = selectYear - 1900
          let ans = this.editObj.ans
          let str = ans.split('年')[1]
          let selectMonth = str.split('月')[0]
          let str3 = str.split('月')[1]
          let selectDay = str3.split('日')[0]
          defaltIndex2 = selectMonth - 1
          defaltIndex3 = selectDay -1
        }
        that.columns = [
          {values: yearList, defaultIndex: defaltIndex, className: 'year'},
          {values: monthL, defaultIndex: defaltIndex2, className: 'month'},
          {values: datL, defaultIndex: defaltIndex3, className: 'day'}
        ]
      }
      else {
        let column1 = [
//          {'code': 0, 'text':'请选择'}
          ]
        for (let key in that.area) {
          column1.push({
            'code': key,
            'text':that.area[key]['name'],
            'kids': that.area[key]['child']
          })
        }
        console.log('第一级')
        console.log(column1)

        let type = Number(specialTab)
        switch (type){
          case 1:
            that.columns = [
              {values: column1, defaultIndex: 0, className: '省'},
            ]
            break
          case 2:
            let column2 = []
            let data2 = column1[0]['kids']
            let scdData = []
            for (let key21 in data2) {
              scdData = data2[key21]['child']
            }
            for (let key2 in scdData) {
              column2.push({
                'code': key2,
                'text':scdData[key2]
              })
            }
            that.columns = [
              {values: column1, defaultIndex: 0, className: '省'},
              {values: column2, defaultIndex: 0, className: '市'}
            ]
            break
          case 3:
          case 4:
            let columns2 = []
            let columns3 = []
            let data3_3 = column1[0]['kids']; // 2级数据
            for (let key212 in data3_3) {
              columns2.push({
                'code': key212,
                'text':data3_3[key212]['name'],
                'kids': data3_3[key212]['child']
              })
            }
            let thrdData = columns2[0]['kids'];
            for (let key23 in thrdData) {
              columns3.push({
                'code': key23,
                'text':thrdData[key23]
              })
            }
            that.columns = [
              {values: column1, defaultIndex: 0, className: '省'},
              {values: columns2, defaultIndex: 0, className: '市'},
              {values: columns3, defaultIndex: 0, className: '区'}
            ]
            break
        }
      }


    },
    openPicker(question, specialTab) {
      this.showPicker = true
      this.editObj = question
      if(specialTab === 'year'|| specialTab === 'yearMonth'|| specialTab === 'date'){
        this.editObj.selectYear = true
      }
      this.initAreaData(specialTab)
    },
    clearTxt(question, key){
      question[key] = ""
    },
    confirmDate(date) {
      console.log(this.selectDate)
      let ty = this.editObj.specialTab
      let reg = ''
      if(ty === '1'){
        reg = "yyyy年"
      }else if(ty === '2'){
        reg = "yyyy年MM月"
      }else if(ty === '3'){
        reg = "yyyy年MM月dd日"
      }
      this.editObj.ans = new Date(date).format(reg)
      this.showDate = false
    },
    openDateDialog (question) {
      let ty = question.specialTab
      if(ty === '1'){
        this.openPicker(question, 'year')
      }else{
        if(ty === '2'){
          this.openPicker(question, 'yearMonth')
        }else if(ty === '3'){
          this.openPicker(question, 'date')
        }
      }

    },
    onChange(picker, value, index) {
      console.log('改变值')
      console.log(picker)
      console.log(value)
      console.log(index)
      if(this.pickerDateOrArea == 'date' || this.pickerDateOrArea == 'yearMonth'){
        if (index == 0) { // 改变第一列的值
          let list = this.getMonthList()
          let year = Number(value[0])
          let list2 = this.getDayList(year,1)
          picker.setColumnValues(1, list)
          picker.setColumnValues(2, list2)
          value[1]= {}
          value[2]= {}
        } else if (index == 1) { // 改变第二列的值
          let year = Number(value[0])
          let month = Number(value[1])
          let list2 = this.getDayList(year,month)
          picker.setColumnValues(2, list2)
          value[2]= 1
        }
      }else{
        if (index == 0) { // 改变第一列的值
          let i = value[0]['code'];
          let data = value[0]['kids']
          let list = []
          let list2 = []
          let name0 = value[0]['text']
          if( ['北京市','天津市','上海市','重庆市'].indexOf(name0) > -1) {
            if(value.length === 2 ){
              let scdData = []
              for (let key21 in data) {
                scdData = data[key21]['child']
              }
              for (let key2 in scdData) {
                list.push({
                  'code': key2,
                  'text':scdData[key2]
                })
              }
              console.log('实际', list)
            }else{ // 三级选择的
              for (let key212 in data) {
                list.push({
                  'code': key212,
                  'text':data[key212]['name'],
                  'kids': data[key212]['child']
                })
              }
              console.log('实际2', list)
              let thrdData = list[0]['kids'];
              for (let key23 in thrdData) {
                list2.push({
                  'code': key23,
                  'text':thrdData[key23]
                })
              }

            }
          }
          else{
            console.log('正常的')
            if (data) {
              for (let key in data) {
                list.push({
                  'code': key,
                  'text':data[key]['name'],
                  'kids': data[key]['child']
                })
              }
              let thrdData2 = list[0]['kids'];
              for (let key232 in thrdData2) {
                list2.push({
                  'code': key232,
                  'text':thrdData2[key232]
                })
              }
            }
          }

          picker.setColumnValues(1, list)
          picker.setColumnValues(2, list2)
          value[1]= {}
          value[2]= {}
          console.log(list)
        } else if (index == 1) {
          let data2 = value[1]['kids']
          let list2 = []
          if (data2) {
            for (let key2 in data2) {
              list2.push({
                'code': key2,
                'text':data2[key2]
              })
            }
          }
          if (list2.length === 1) {
            list2 = []
          }
          picker.setColumnValues(2, list2)
          console.log(list2)
          value[2]= {}
        }
      }
    },
    onConfirm (value, index) {
      console.log('确定',value,index)
      if(this.pickerDateOrArea === 'area'){
        console.log(index.indexOf(0))
        let content = value[0]['text']
        for(let j = 1 ; j < value.length; j++){
          content += ' / ' + value[j]['text']
        }
        this.editObj.ans0 = content
        this.showPicker = false
      }else {
        if(this.pickerDateOrArea === 'year'){
          this.editObj.ans = new Date(String(value[0])).format('yyyy年')
        } else if(this.pickerDateOrArea === 'yearMonth'){
          this.editObj.ans = String(value[0]) + '年' + String(value[1]) + '月'
        } else if(this.pickerDateOrArea === 'date'){
          this.editObj.ans = String(value[0]) + '年' + String(value[1]) + '月' + String(value[2]) + '日'
        }
        this.showPicker = false
      }

    },
    getMonthList(){
      let monthList = []
      for(let y = 1 ; y < 13; y++){
        monthList.push(y)
      }
      return monthList
    },
    getDayList(year,month){
      let dayList = []
      let endDay = 30
      if(Number(month) === 2){
        endDay = 28
        // 1.能被4整除且不能被100整除；2.能被400整除
        if(year % 400 === 0){
          endDay = 29
        }else if(year % 100 !== 0 && year % 4 === 0){
          endDay = 29
        }
      }else if([1,3,5,7,8,10,12].indexOf(Number(month)) > -1){
        endDay = 31
      }
      for(let y = 1 ; y <= endDay; y++){
        dayList.push(y)
      }
      return dayList
    },
  }
}
</script>
