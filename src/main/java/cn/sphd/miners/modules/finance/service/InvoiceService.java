package cn.sphd.miners.modules.finance.service;

import cn.sphd.miners.modules.finance.entity.*;
import cn.sphd.miners.modules.sales.entity.SlInvoiceApplicationDetail;
import cn.sphd.miners.modules.system.entity.User;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2017/7/10.
 */
public interface InvoiceService {

    FinanceInvoiceSetting getFinanceInvoiceSetting(Integer oid,String category,Integer invoiceSettineId);

    List<FinanceInvoiceSetting> getFinanceInvoiceSettingByOid(Integer oid);

    void updateFinanceInvoiceSetting(FinanceInvoiceSetting financeInvoiceSetting);

    void addFinanceInvoiceSetting(FinanceInvoiceSetting financeInvoiceSetting);

    void addFinanceInvoice(FinanceInvoice financeInvoice);

    void addFinanceInvoiceDetail(FinanceInvoiceDetail financeInvoiceDetail);

    List<Map<String,Object>> getAllInvoices(Integer oid,Date beginDate,Date endDate,String type,Integer orderBy);

    List<FinanceInvoice> getAllInvoicesByOid(Integer oid,Date beginDate,Date endDate,String type,Integer orderBy);

    List<FinanceInvoiceDetail> getAllInvoiceDetailsByInvoiceId(Integer invoiceId,Date beginDate,Date endDate,String type,Integer oid);

    FinanceInvoiceDetail getFinanceInvoiceDetailById(Integer invoiceDetailId);

    void updateFinanceInvoiceDetail(FinanceInvoiceDetail financeInvoiceDetail);

    List<FinanceInvoiceSettingHistory> getFinanceInvoiceSettingHistoryByInvoiceSettingId(Integer invoiceSettineId);

    FinanceInvoiceSettingHistory getFinanceInvoiceSettingHistoryById(Integer invoiceSettingHistoryId,Integer previousId);

    FinanceInvoiceSettingHistory getPreviousInvoiceSetting(Integer invoiceSettineId);

    void addFinanceInvoiceRateHistory(FinanceInvoiceRateHistory financeInvoiceRateHistory);

    void addFinanceInvoiceSettingHistory(FinanceInvoiceSettingHistory financeInvoiceSettingHistory);

    List<FinanceInvoiceRateHistory> getFinanceInvoiceRateHistoryBySettingId(Integer settingId,Integer settingHistoryId,BigDecimal rate,Integer maxTaxRateLogId,Integer type);

    void updateFinanceInvoiceSettingHistory(FinanceInvoiceSettingHistory financeInvoiceSettingHistory);

    Integer getMaxTaxRateHistoryId(Integer settingId,Integer settingHistoryId);  //根据设置id和设置历史id，查找最大的税率历史id

    //1.56销售之开票
    List<FinanceInvoiceDetail> getInvoiveDetailByOrgAndType(Integer oid,String type);

    void chooseInvoice(Integer invoiceDetailId,Integer lines, Date operateDate,BigDecimal amount,String customterName,String applicantName, Integer applicant,Date applicationTime,Integer customterId,User user,String source);

    void getEnable(Integer invoiceDetailId,User user);

    void cancelInvoice(Integer invoiceDetailId,String reason,String state,User user);

    Map<String,Object> addInvoiceDetail(FinanceInvoiceDetail financeInvoiceDetail,User user,Map<String,Object> map);  //开票录入

    Map<String,Object> addInvoice(FinanceInvoice financeInvoice,User user,Map<String,Object> map);  //新增发票

    Map<String,Object> updateInvoiceDetail(FinanceInvoiceDetail invoiceDetail,String operation,Map<String,Object> map,User user);

    Map<String,Object> getRecords(Integer invoiceDetailId,Map<String,Object> map);  //查找发票管理模块的操作记录

    FinanceInvoiceDetailHistory getInvoiceDetailHistoryDetail(Integer invoiceDetailHistoryId);

    Map<String,Object> getRemind(Integer oid,User user,Map<String,Object> map);

    Map<String,Object> updateEnbale(Integer oid,Boolean enable,Map<String,Object> map);

    Integer getInvoiceSettingNumByOid(Integer oid);

    void terminationApply(Integer invoiceDetailId);  //销售终止申请,true-终止  0-false 1-true

    FinanceInvoice getInvoiceById(Integer invoiceId);

    Integer getBlackInvoiceNum(Integer oid,String type);

    Integer getInvoiceNum(Integer oid,Integer year);

    Map<String,Object> getBlackInvoiceDetails(Integer oid,String type,String state);

    Map getinvoices(Integer oid,Integer customerId, String startTime, String endTime, String rule, String orderBy);

    Map<String,Object> getInvoiceUsed(Integer oid,Date beginDate,String state,String type,Integer dateType);

//    List<FinanceInvoiceDetail> getInvoiceIdUsed(Integer oid,Date beginDate,String state,String type,Integer dateType);

    List<Map> getInvoiceMonthUsed(Integer oid,Date beginDate,Date endTime,String state,String type,Integer dateType);

    FinanceInvoiceDetailHistory getInvoiceDetailHistoryLast(Integer invoiceDetailId);  //获取最后一次的修改记录

    void deleteUpdateById(Integer invoiceDetailId); //1.212中删除某条票据(实际将票据中的数据清空)

    void saveOrUpdateInvoiceDetail(SlInvoiceApplicationDetail detail);

    void deleteInvoiceRateHistory(FinanceInvoiceRateHistory financeInvoiceRateHistory);
}
