package cn.sphd.miners.modules.thirdPlatform.service.impl;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.utils.GetLocalIPUtils;
import cn.sphd.miners.common.utils.HttpClientUtils;
import cn.sphd.miners.common.utils.XmlUtils;
import cn.sphd.miners.modules.ec.entity.EcOrder;
import cn.sphd.miners.modules.ec.entity.EcPayLog;
import cn.sphd.miners.modules.ec.entity.EcRefund;
import cn.sphd.miners.modules.ec.entity.EcRefundLog;
import cn.sphd.miners.modules.ec.service.EcService;
import cn.sphd.miners.modules.rpcLog.entity.RpcLog;
import cn.sphd.miners.modules.system.service.DlmService;
import cn.sphd.miners.modules.thirdPlatform.entity.*;
import cn.sphd.miners.modules.thirdPlatform.service.TgService;
import cn.sphd.miners.modules.thirdPlatform.service.ThirdPlatformService;
import cn.sphd.miners.modules.thirdPlatform.utils.WXBizMsgCrypt;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.exception.HttpException;
import com.wechat.pay.java.core.exception.MalformedMessageException;
import com.wechat.pay.java.core.exception.ServiceException;
import com.wechat.pay.java.core.notification.NotificationConfig;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.core.util.GsonUtil;
import com.wechat.pay.java.service.payments.app.AppServiceExtension;
import com.wechat.pay.java.service.payments.jsapi.JsapiServiceExtension;
import com.wechat.pay.java.service.payments.model.Transaction;
import com.wechat.pay.java.service.refund.RefundService;
import com.wechat.pay.java.service.refund.model.AmountReq;
import com.wechat.pay.java.service.refund.model.CreateRequest;
import com.wechat.pay.java.service.refund.model.Refund;
import com.wechat.pay.java.service.refund.model.RefundNotification;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022年06月07日 16:44
 **/
@Service("wxTgService")
public class WxTgServiceImpl implements TgService {
    @Autowired
    ThirdPlatformService platformService;
    @Autowired
    EcService ecService;
    @Autowired
    DlmService dlmService;

    private final ConcurrentHashMap<Integer, Boolean> wxTpCheckMap;
    private final Map<String, RSAAutoCertificateConfig> jsapiConfigs;
    private final ThirdPlatformService.TgCode tgCode;

    public WxTgServiceImpl() {
        wxTpCheckMap = new ConcurrentHashMap<>();
        jsapiConfigs = new ConcurrentHashMap<>();
        tgCode = ThirdPlatformService.TgCode.weChat;
    }

    private TpPlatform getWxPlatformByAppId(String appId) {
        return platformService.getPlatformByAppId(tgCode, appId, Boolean.TRUE);
    }

    @Override
    public void getTpAccessTokenToRedis(TpPlatform platform) {
        String ticket = platformService.getTpVerifyTicket(platform);
        if(StringUtils.isNotEmpty(ticket)) {
            String url = "https://api.weixin.qq.com/cgi-bin/component/api_component_token";
            Map<String, String> headers = new HashMap<>(1);
            headers.put("content", "application/json");
            Map<String, String> bodyMap = new HashMap<>(3);
            bodyMap.put("component_appid", platform.getAppId());
            bodyMap.put("component_appsecret", platform.getAppSecret());
            bodyMap.put("component_verify_ticket", ticket);
            HttpClientUtils client = new HttpClientUtils(url, true);
            Map<String, Object> response = client.jsonResponseToT(client.doPost(null, url, headers, null, JSON.toJSONString(bodyMap)),Map.class);
            String accessToken = null;
            Long expiresIn = null;
            if (null != response
                    && StringUtils.isNotEmpty(accessToken = String.valueOf(response.getOrDefault("component_access_token",null)))
                    && 0L < (expiresIn = Long.valueOf(String.valueOf(response.getOrDefault("expires_in","0L"))))) {
                Map<String, Object> tokenMap = new HashMap<>(2);
                tokenMap.put("accessToken", accessToken);
                tokenMap.put("expiresIn", expiresIn);
                platformService.saveTpAccessTokenToRedis(platform, tokenMap);
            } else {
                Logger.getLogger(getClass()).warn("getTpAccessTokenToRedis error, response != null ? "
                        + (null != response) + ", accessToken != null ? "
                        + (null != accessToken) + ", expiresIn = " + expiresIn);
            }
        } else {
            Logger.getLogger(getClass()).info("getTpAccessTokenToRedis error, ticket = " + ticket);
        }
    }

    @Override
    public void getAppAccessTokenToRedis(TpApp app) {
        //"https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=&secret="
        String url = "https://api.weixin.qq.com/cgi-bin/token";
        Map<String, String> queryMap = new HashMap<>(3);
        queryMap.put("grant_type", "client_credential");
        queryMap.put("appid", app.getAppId());
        queryMap.put("secret", app.getAppSecret());
        HttpClientUtils client = new HttpClientUtils(url);
        Map<String, Object> response = client.jsonResponseToT(client.doGet(null, url, null, queryMap),Map.class);
        String accessToken;
        Long expiresIn;
        if (null != response
                && StringUtils.isNotEmpty(accessToken = String.valueOf(response.getOrDefault("access_token",null)))
                && 0L < (expiresIn = Long.valueOf(String.valueOf(response.getOrDefault("expires_in",0L))))) {
            Map<String, Object> tokenMap = new HashMap<>(3);
            tokenMap.put("accessToken",accessToken);
            tokenMap.put("expiresIn", expiresIn);
            if(JsapiTicket(tokenMap, app)) {
                platformService.saveAppAccessTokenToRedis(app, tokenMap);
            } else {
                Logger.getLogger(getClass()).error("获取JsapiTicket失败，是不是服务器ip地址变了，检查公众号白名单，AppId="+app.getAppId());
            }
        } else { //出错处理
            StringBuffer msg = new StringBuffer("wxTgService.getAppAccessToken");
            if(response == null) {
                msg.append("获取token失败  appid：" + app.getAppId() + "请检查是否是白名单的问题。");
            } else {
                msg.append("获取token失败  appid：" + app.getAppId() + " errcode:{} errmsg:{} \n" + JSON.toJSONString(response) + "\n请检查是否是白名单的问题。");
            }
            Logger.getLogger(getClass()).error(msg.append("\n是不是服务器ip地址变了，检查白名单，AppId="+app.getAppId()).toString());
        }
    }
    private boolean JsapiTicket(Map<String, Object> tokenMap, TpApp app) {
        String accessToken = (String) tokenMap.get("accessToken");
        Long expiresIn = (Long) tokenMap.get("expiresIn");
        //"https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token="+accessToken.getToken()+"&type=jsapi";
        String url = "https://api.weixin.qq.com/cgi-bin/ticket/getticket";
        HttpClientUtils client = new HttpClientUtils(url);
        Map<String, String> queryMap = new HashMap<>(3);
        queryMap.put("access_token", accessToken);
        queryMap.put("type", "jsapi");
        Map<String, Object> response = client.jsonResponseToT(client.doGet(null, url, null, queryMap),Map.class);
        // 如果请求成功
        String jsapiTicket;
        Long newExpiresIn;
        if (null != response
                && StringUtils.isNotEmpty(jsapiTicket = String.valueOf(response.getOrDefault("ticket",null)))
                && 0L < (newExpiresIn = Long.valueOf(String.valueOf(response.getOrDefault("expires_in",0L))))) {
            // wyu：accessToken和jsapiTicket都获取成功才更新
            tokenMap.put("jsapiTicket", jsapiTicket);
            tokenMap.put("expiresIn", Math.min(expiresIn, newExpiresIn));
            return true;
        } else {
            String msg;
            if(response == null) {
                msg = "获取JsapiTicket失败  appid：" + app.getAppId() + "，请检查是否是公众号白名单的问题。";
            } else {
                msg = "获取JsapiTicket失败  appid：" + app.getAppId() + ", errcode:{} errmsg:{}" + response.get("errcode") + response.get("errmsg") + "请检查是否是公众号白名单的问题。";
            }
            Logger.getLogger(getClass()).error(msg);
        }
        return false;
    }

    @Override
    public void receiveAuthEvent(String postData, String signature, String timeStamp, String nonce, String encryptType, String msgSignature) {
        Map<String, Object> postMap = XmlUtils.xmlStringToMapRemoveRoot(postData);
        //判断并设置ComponentVerifyTicket推送
        if(verifyAndSetTicket(postMap, timeStamp, nonce, msgSignature)) {
            System.out.println("wxReceiveAuthEvent 解析 ComponentVerifyTicket 成功！");
//        } else if() {//ToDo: 添加其它推送消息解析
//            System.out.println("wxReceiveAuthEvent 解析  成功！");
        }
    }
    private boolean verifyAndSetTicket(Map<String, Object> postMap, String timeStamp, String nonce, String msgSignature) {
        boolean result = false;
        String appId = (String) postMap.get("AppId");
        if(StringUtils.isNotEmpty(appId)) {
            TpPlatform platform = getWxPlatformByAppId(appId);
            String encrypt = (String) postMap.get("Encrypt");
            WXBizMsgCrypt pc = new WXBizMsgCrypt(platform.getEdKey());
            if (platform != null && encrypt != null && pc.verifyMsgSignature(platform.getVerifToken(), timeStamp, nonce, encrypt, msgSignature)) {
                result = setPlatfromTicket(platform, encrypt, pc);
            } else {
                Logger.getLogger(getClass()).warn("verifyAndSetTicket verify error");
            }
        }
        return result;
    }
    private boolean checkTpAccessToken(TpPlatform platform) {
        boolean result = true;
        if (!Boolean.TRUE.equals(wxTpCheckMap.get(platform.getId()))) {
            //本地Map中找不到或者为false，查询redis
            result = StringUtils.isEmpty(platformService.getTpAccessToken(platform));
            //把查询结果保存到本地Map
            wxTpCheckMap.put(platform.getId(), Boolean.valueOf(result));
        }
        return result;
    }
    private boolean setPlatfromTicket(TpPlatform platform, String encrypt, WXBizMsgCrypt pc) {
        boolean result = false;
        Map<String, String> original = pc.decrypt(encrypt);
        if(platform.getAppId().equalsIgnoreCase(original.get("appId"))) {
            String xml = original.get("xml");
            Map<String, Object> ticketMap = XmlUtils.xmlStringToMapRemoveRoot(xml);
            String ticket = (String) ticketMap.get("ComponentVerifyTicket");
            result = StringUtils.isNotEmpty(ticket);//成功解析出ComponentVerifyTicket
            Long timeStamp = Long.valueOf((String) ticketMap.get("CreateTime"));
            if(result && timeStamp != null
                    && platformService.saveTpVerifyTicketToRedis(platform, ticket, timeStamp)
                    && checkTpAccessToken(platform)) {
                getTpAccessTokenToRedis(platform);//首次设置成功ComponentVerifyTicket，调用获取平台AccessToken。
            } else {
                Logger.getLogger(getClass()).warn("verifyAndSetTicket verify error, xml: \n" + xml);
            }
        } else {
            Logger.getLogger(getClass()).error("verifyAndSetTicket verify error, platform appId = " + platform.getAppId() + ", decrypt appId = " + original.get("appId"));
        }
        return result;
    }

    @Override
    public void savePayNotify(Map<String, Object> params) {
        if(params==null) {
            String jsonStr = "{\"refund_id\":\"50302506062023052534881245421\",\"out_refund_no\":\"E9C2385A6B604F9CA7D9754FA422943B\",\"transaction_id\":\"4200001886202305258120739189\",\"out_trade_no\":\"WDS2023052514465925266921CABCA4C\",\"user_received_account\":\"招商银行信用卡3773\",\"success_time\":\"2023-05-25T15:43:20+08:00\",\"promotion_detail\":[],\"amount\":{\"total\":1,\"refund\":1,\"payer_total\":1,\"payer_refund\":1},\"refund_status\":\"SUCCESS\"}";
            RefundNotification notification = GsonUtil.getGson().fromJson(jsonStr, RefundNotification.class);
            setRefund(notification);
            return;
        }
        StringBuffer error = new StringBuffer();
        String mchid = (String) params.get("mchid");
        String serialNumber = (String) params.get("serialNumber");
        String signature = (String) params.get("signature");
        String nonce = (String) params.get("nonce");
        String timestamp = (String) params.get("timestamp");
        String signType = (String) params.get("signType");
        String body = (String) params.get("body");
        RpcLog rpcLog = (RpcLog) params.get("rpcLog");
        RequestParam requestParam = new RequestParam.Builder()
                .serialNumber(serialNumber)
                .nonce(nonce)
                .signature(signature)
                .timestamp(timestamp)
                // 若未设置signType，默认值为 WECHATPAY2-SHA256-RSA2048
                .signType(signType)
                .body(body)
                .build();
        System.out.println("WX receivePayEvent requestParam: "+ requestParam);
        JSONObject bodyJson = JSON.parseObject(requestParam.getBody());
        System.out.println("OK");
        Pair<Object, ThirdPlatformService.NotifyStatus> notifyResult = setParams(requestParam, mchid, error);
        System.out.println("setParams result:\n"+JSON.toJSONString(notifyResult));
        TpPayNotify tpPayNotify;
        platformService.savePayNotify(tpPayNotify =new TpPayNotify(
                rpcLog.getId(),
                tgCode.getIndex(),
                bodyJson.getString("id"),
                mchid,
                serialNumber,
                signature,
                nonce,
                timestamp,
                signType,
                body,
                bodyJson.getJSONObject("resource").getString("original_type"),
                JSON.toJSONString(notifyResult.getLeft()),
                error.toString(),
                notifyResult.getRight()));
        System.out.println("OK\n"+JSON.toJSONString(tpPayNotify));
    }

    public Pair<Object, ThirdPlatformService.NotifyStatus> setParams(RequestParam requestParam, String mchid, StringBuffer error) {
        ThirdPlatformService.NotifyStatus status = ThirdPlatformService.NotifyStatus.unkonw;
        TpMerchant merchant = platformService.getMerchantByMchid(mchid);
        NotificationConfig config = getMerchantService(merchant);
        NotificationParser notificationParser = new NotificationParser(config);
        String message = requestParam.getMessage();
        System.out.println("receivePayEvent message:"+message);
        Object result = new JSONObject();
        try {
            System.out.println("receivePayEvent requestParam body:"+requestParam.getBody());
            JSONObject body = JSON.parseObject(requestParam.getBody());
            if(ObjectUtils.isEmpty(platformService.getPayNotifyListByNotifyId(body.getString("id"), ThirdPlatformService.NotifyStatus.success))) {
                System.out.println("wxReceivePayNotify body:\n" + body.toJSONString());
                try {
                    switch (body.getJSONObject("resource").getString("original_type")) {
                        case "transaction":
                            result = notificationParser.parse(requestParam, Transaction.class);
                            status = setPay((Transaction) result);
                            System.out.println("receivePayEvent transaction setPay:"+JSON.toJSONString(result));
                            break;
                        case "refund":
                            result = notificationParser.parse(requestParam, RefundNotification.class);
                            status = setRefund((RefundNotification) result);
                            break;
                        default:
                            result = notificationParser.parse(requestParam, Map.class);
                            String errorMsg = "未知微信支付通知类型：\n" + JSON.toJSONString(result);
                            Logger.getLogger(getClass()).error(errorMsg);
                            error.append(errorMsg).append('\n').append('\n');
                    }
                } catch (Exception e) {
                    String errorMsg = "notificationParser.parse or setPay | setRefund throw Exception: " + e.getMessage();
                    Logger.getLogger(getClass()).error(errorMsg, e);
                    error.append(errorMsg).append('\n').append('\n');
                    throw new RuntimeException("notificationParser.parse or setPay | setRefund throw Exception: " + e.getMessage(), e);
                }
            } else {
                status = ThirdPlatformService.NotifyStatus.duplicateNotify;
            }
        } catch (JSONException e) {
            String errorTitle = "FastJson error";
            Logger.getLogger(getClass()).error(errorTitle, e);
            error.append(errorTitle).append('\n').append(e).append('\n').append('\n');
        }
        System.out.println("OK");
        return Pair.of(result, status);
    }
    private ThirdPlatformService.NotifyStatus setPay(Transaction transaction) {
        System.out.println("wxReceivePayNotify Transaction:\n"+transaction.toString());
        EcOrder order = ecService.getOrderBySn(transaction.getOutTradeNo());
        if(order==null) {
            return ThirdPlatformService.NotifyStatus.notFound;
        } else if(EcService.PayStatus.isPaid(order.getPayStatus())) {
            return ThirdPlatformService.NotifyStatus.duplicateOpration;
        } else {
            TpApp app = platformService.getTpApp(tgCode, transaction.getAppid(), null);
            System.out.println("wxReceivePayNotify setPay app:"+JSON.toJSONString(app));
            TpMember member = platformService.getMemberByOpenId(app, transaction.getPayer().getOpenid());
            System.out.println("wxReceivePayNotify setPay member:"+JSON.toJSONString(member));
            EcPayLog newPayLog = new EcPayLog(tgCode, member, transaction);
            System.out.println("wxReceivePayNotify setPay newPayLog:"+JSON.toJSONString(newPayLog));
            JsonResult result = ecService.payOrder(order, newPayLog);
            System.out.println("wxReceivePayNotify setPay ecService.payOrder:"+JSON.toJSONString(result));
            return ThirdPlatformService.NotifyStatus.fromJsonResult(result);
        }
    }
    private ThirdPlatformService.NotifyStatus setRefund(RefundNotification notification) {
        System.out.println("wxReceivePayNotify RefundNotification:\n"+notification.toString());
        EcRefund refund = ecService.getEcRefundByRefundNo(notification.getOutRefundNo());
        if(refund==null) {
            return ThirdPlatformService.NotifyStatus.notFound;
        } else if(Boolean.TRUE.equals(refund.getRefunded())) {
            return ThirdPlatformService.NotifyStatus.duplicateOpration;
        } else {
            EcRefundLog log = new EcRefundLog(notification, refund);
            JsonResult result = ecService.updateRefund(refund, log);
            return ThirdPlatformService.NotifyStatus.fromJsonResult(result);
        }
    }

    @Override
    public Pair<TpMember, MyException> code2Member(TpApp app, String code, Map<String, String> userInfo) {
        String lockKey, methodName = Thread.currentThread().getStackTrace()[1].getMethodName() + "|" + app.getTgCode() + "|" + code;
        while ((lockKey=dlmService.getLock(methodName, TimeUnit.MINUTES.toMillis(1)))== null) {//wyu:防止重复调用，加锁1分钟（HttpClientUtils超时时间是1分钟）
            try {
                Thread.sleep(300);
            } catch (InterruptedException e) {
                Logger.getLogger(getClass()).info("WX Thread.sleep error", e);
                Thread.currentThread().interrupt();
                return null;
            }
        }
        Map<String, Object> dataMap;
        TpMember member;
        Long memberId;
        Pair<TpMember, MyException> result;
        //从缓存和数据库读取
        if (ObjectUtils.isNotEmpty(dataMap = platformService.getMemberSessionKeyRedis(app, code))
                && (memberId = (Long) dataMap.get("memberId"))!=null
                && (member = platformService.getMemberById(memberId)) != null ) {
            platformService.fillMemberUserInfoWithMap(member, userInfo);
            result = Pair.of(member, null);
        } else if(ThirdPlatformService.TpType.isMiniApp(app)){//小程序第三方接口调用
            String url = "https://api.weixin.qq.com/sns/jscode2session";
            Map<String, String> headers = new HashMap<>(1);
            headers.put("content", "application/json");
            Map<String, String> bodyMap = new HashMap<>(4);
            bodyMap.put("appid", app.getAppId());
            bodyMap.put("secret", app.getAppSecret());
            bodyMap.put("js_code", code);
            bodyMap.put("grant_type", "authorization_code");
            HttpClientUtils client = new HttpClientUtils(url, true);
            Map<String, Object> response = client.jsonResponseToT(client.doPost(null, url, headers, null, bodyMap), Map.class);
            dataMap = new HashMap<>(4);
            String sessionKey, openId, unionId;
            if (null != response) {
                if (StringUtils.isNotEmpty(sessionKey = (String) response.get("session_key"))
                        && StringUtils.isNotEmpty(openId = (String) response.get("openid"))
                        && StringUtils.isNotEmpty(unionId = (String) response.get("unionid"))) {
                    dataMap.put("session_key", sessionKey);
                    dataMap.put("openid", openId);
                    dataMap.put("unionid", unionId);
                    member = platformService.getByAppUnionId(app, unionId, openId);
                    if (member != null && member.getId() != null) {
                        platformService.fillMemberUserInfoWithMap(member, userInfo);
                        dataMap.put("memberId", member.getId());
                        //保存session_key到redis
                        platformService.saveMemberSessionKeyRedis(app, code, dataMap);
                        System.out.println("code2SessionMember member: \n" + JSON.toJSONString(member) + "\n" + JSON.toJSONString(response));
                        result = Pair.of(member, null);
                    } else {//返回错误信息
                        Logger.getLogger(getClass()).error("生成TpMember失败：" + JSON.toJSONString(response));
                        result = Pair.of(null, new MyException("5", "创建对象失败, 可能是数据库问题"));
                    }
                } else {//返回错误信息
                    Logger.getLogger(getClass()).error("获取code2Session失败，第三方服务器返回错误信息：" + JSON.toJSONString(response));
                    result = Pair.of(null, new MyException("4", "login error, 可能是授权或者code问题，建议重试login"));
                }
            } else { //网络出错处理
                StringBuffer msg = new StringBuffer("wxTgService.code2SessionMember");
                msg.append("获取code2Session失败  appid：" + app.getAppId() + "请检查是否是公众号白名单的问题。");
                Logger.getLogger(getClass()).error(msg.append("\n是不是服务器ip地址变了"));
                result = Pair.of(null, new MyException("5", msg.insert(0, "可能是小程序后台配置问题, 错误信息：").toString()));
            }
        } else {//3-移动应用,4-网站应用
            String url = "https://api.weixin.qq.com/sns/oauth2/access_token";
            Map<String, String> headers = new HashMap<>(1);
            headers.put("content", "application/json");
            Map<String, String> bodyMap = new HashMap<>(4);
            bodyMap.put("appid", app.getAppId());
            bodyMap.put("secret", app.getAppSecret());
            bodyMap.put("code", code);
            bodyMap.put("grant_type", "authorization_code");
            HttpClientUtils client = new HttpClientUtils(url, true);
            Map<String, Object> response = client.jsonResponseToT(client.doPost(null, url, headers, null, bodyMap), Map.class);
            dataMap = new HashMap<>(4);
            String accessToken, openId, unionId, scope;
            if (null != response) {
                if (StringUtils.isNotEmpty(accessToken = (String) response.get("access_token"))//response.get("refresh_token")
                        && StringUtils.isNotEmpty(openId = (String) response.get("openid"))
                        && StringUtils.isNotEmpty(unionId = (String) response.get("unionid"))
                        && StringUtils.isNotEmpty(scope = (String) response.get("scope"))) {
                    dataMap.put("access_token", accessToken);
                    dataMap.put("openid", openId);
                    dataMap.put("unionid", unionId);
                    System.out.println(scope);
                    member = platformService.getByAppUnionId(app, unionId, openId);
                    if (member != null && member.getId() != null) {
                        platformService.fillMemberUserInfoWithMap(member, userInfo);
                        dataMap.put("memberId", member.getId());
                        //保存session_key到redis
                        platformService.saveMemberSessionKeyRedis(app, code, dataMap);
                        System.out.println("code2SessionMember member:" + JSON.toJSONString(member));
//                        if(scope.contains("snsapi_userinfo")) {
                            fillMemberUserInfo(member, accessToken);
//                        }
                        result = Pair.of(member, null);
                    } else {//返回错误信息
                        Logger.getLogger(getClass()).error("生成TpMember失败：" + JSON.toJSONString(response));
                        result = Pair.of(null, new MyException("5", "创建对象失败, 可能是数据库问题"));
                    }
                } else {//返回错误信息
                    Logger.getLogger(getClass()).error("获取code2Session失败，第三方服务器返回错误信息：" + JSON.toJSONString(response));
                    result = Pair.of(null, new MyException("4", "login error, 可能是授权或者code问题，建议重试login"));
                }
            } else { //网络出错处理
                StringBuffer msg = new StringBuffer("wxTgService.code2SessionMember");
                msg.append("获取code2Session失败  appid：" + app.getAppId() + "请检查是否是公众号白名单的问题。");
                Logger.getLogger(getClass()).error(msg.append("\n是不是服务器ip地址变了"));
                result = Pair.of(null, new MyException("5", msg.insert(0, "可能是小程序后台配置问题, 错误信息：").toString()));
            }
        }
        dlmService.releaseLock(methodName, lockKey);
        return result;
    }
    protected void fillMemberUserInfo(TpMember member, String access_token) {
        String url = "https://api.weixin.qq.com//sns/userinfo";
        Map<String, String> headers = new HashMap<>(1);
        headers.put("content", "application/json");
        Map<String, String> bodyMap = new HashMap<>(3);
        bodyMap.put("openid", member.getOpenId());
        bodyMap.put("access_token", access_token);
        bodyMap.put("lang", "zh_CN");
        HttpClientUtils client = new HttpClientUtils(url, true);
        Map<String, Object> response = client.jsonResponseToT(client.doPost(null, url, headers, null, bodyMap), Map.class);
        if (null != response) {
            String nickName = (String) response.get("nickname");
            String avatar = (String) response.get("headimgurl");
            System.out.println("fillMemberUserInfo" + JSON.toJSONString(response));
            boolean changed = false;
            if (StringUtils.isNotEmpty(nickName)) {
                member.setNickName(nickName);
                changed = true;
            }
            if (StringUtils.isNotEmpty(avatar)) {
                member.setAvatar(avatar);
                changed = true;
            }
            if (!changed) {
                Logger.getLogger(getClass()).error("获取微信用户信息失败，第三方服务器返回信息：" + JSON.toJSONString(response));
            }
        } else { //网络出错处理
            StringBuffer msg = new StringBuffer("wxTgService.fillMemberUserInfo");
            Logger.getLogger(getClass()).error(msg.append("\n是不是服务器ip地址变了"));
        }
    }

    @Override
    public void initMerchantService(TpMerchant merchant) {
        getMerchantService(merchant);
    }
    private synchronized RSAAutoCertificateConfig getMerchantService(TpMerchant merchant) {
        RSAAutoCertificateConfig result;
        if((result=jsapiConfigs.get(merchant.getMchid()))!=null) {
            return result;
        }
        String apiV3Key;
        if(merchant.getApiNewVersion()==3) {
            apiV3Key = merchant.getApiVNewKey();
        } else if(merchant.getApiNewVersion()>3) {
            apiV3Key = merchant.getApiVOldKey();
        } else {
            return null;
        }
        result = new RSAAutoCertificateConfig.Builder()
                        .merchantId(merchant.getMchid())
                        .privateKey(merchant.getPrivateKey())
                        .merchantSerialNumber(merchant.getMerchantSerialNumber())
                        .apiV3Key(apiV3Key)
                        .build();
        jsapiConfigs.put(merchant.getMchid(), result);
        return result;
    }

    /**
     * 小程序支付，JSAPI下单//https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_5_1.shtml
     * @param request
     * @param order
     * @return
     */
    @Override
    public JsonResult preOrder(HttpServletRequest request, EcOrder order) {
        JsonResult result = new JsonResult();
        TpMerchant merchant = platformService.getMerchantByMchid(order.getMchid());
        if(merchant==null || StringUtils.isBlank(merchant.getMchid())) {
            result.setError(new MyException("-1", "商户未找到，请联系开发人员检查代码及商户表！"));
        }
        Config config = getMerchantService(merchant);
        if(config==null) {
            result.setError(new MyException("-1", "商户未找到，请联系运营人员检查商户表！"));
        } else {
            if(EcService.Platform.MIN.getName().equalsIgnoreCase(order.getPlatform())) {
                com.wechat.pay.java.service.payments.jsapi.model.PrepayRequest payRequest = new com.wechat.pay.java.service.payments.jsapi.model.PrepayRequest();
//            StringBuffer description = new StringBuffer();
//            for (EcOrderDetail detail : order.getDetails()) {
//                description.append(detail.getSkuName()).append(",");
//            }
                com.wechat.pay.java.service.payments.jsapi.model.Amount amount = new com.wechat.pay.java.service.payments.jsapi.model.Amount();
                if(!GetLocalIPUtils.isProServer(request)) {//开发环境，支付改1分。
                    order.setOrderTotal(EcService.oneCent);
                    order.caculatePayable();
                }
                amount.setTotal(order.getOrderTotal().multiply(BigDecimal.valueOf(100)).intValue());
                com.wechat.pay.java.service.payments.jsapi.model.Payer payer = new com.wechat.pay.java.service.payments.jsapi.model.Payer();
                payer.setOpenid(order.getOpenId());
                payRequest.setAmount(amount);
                payRequest.setAppid(order.getAppId());
                payRequest.setMchid(order.getMchid());
//            payRequest.setDescription(description.length()>0 ? description.substring(0,description.length()-1) : "");
                payRequest.setDescription(order.getDescription());
                payRequest.setNotifyUrl(GetLocalIPUtils.getRootPath(request) + "/tp/mchid/" + order.getMchid() + "/wxReceivePayNotify.do");
                payRequest.setOutTradeNo(order.getSn());
                payRequest.setPayer(payer);
//            JsapiService jsapiService = new JsapiService.Builder().config(config).build();
//            PrepayResponse response = jsapiService.prepay(payRequest);
//            System.out.println(response);
                JsapiServiceExtension jsapiService = new JsapiServiceExtension.Builder().config(config).build();
                try {
                    com.wechat.pay.java.service.payments.jsapi.model.PrepayWithRequestPaymentResponse response = jsapiService.prepayWithRequestPayment(payRequest);
                    result.setSuccess(1);
                    JSONObject payData = JSON.parseObject(GsonUtil.toJson(response));//用GsonUtile处理SerializedName注解转为JSONString，解决对象属性和SerializedName不一致的问题，然后再转为JSON对象返回。
                    System.out.println("payData:\n" + payData.toJSONString());
                    System.out.println("payRequest:\n" + payRequest.toString());
                    order.setOnlinePaidData(JSON.toJSONString(new HashMap<String, Object>(2) {{
                        put("payData", payData);
                        put("payRequest", JSON.parseObject(GsonUtil.toJson(payRequest)));
                    }}));
                    result.setData(payData);
                } catch (ServiceException e) {
                    result.setError(new MyException(e.getErrorCode(), e.getErrorMessage()));
                    Logger.getLogger(getClass()).error("访问微信后端保存，返回错误信息:" + e.getMessage(), e);
                }
            } else if(EcService.Platform.APP.getName().equalsIgnoreCase(order.getPlatform())) {
                com.wechat.pay.java.service.payments.app.model.PrepayRequest payRequest = new com.wechat.pay.java.service.payments.app.model.PrepayRequest();
                com.wechat.pay.java.service.payments.app.model.Amount amount = new com.wechat.pay.java.service.payments.app.model.Amount();
                if(!GetLocalIPUtils.isProServer(request)) {//开发环境，支付改1分。
                    order.setOrderTotal(EcService.oneCent);
                    order.caculatePayable();
                }
                amount.setTotal(order.getOrderTotal().multiply(BigDecimal.valueOf(100)).intValue());
                payRequest.setAmount(amount);
                payRequest.setAppid(order.getAppId());
                payRequest.setMchid(order.getMchid());
//            payRequest.setDescription(description.length()>0 ? description.substring(0,description.length()-1) : "");
                payRequest.setDescription(order.getDescription());
                payRequest.setNotifyUrl(GetLocalIPUtils.getRootPath(request) + "/tp/mchid/" + order.getMchid() + "/wxReceivePayNotify.do");
                payRequest.setOutTradeNo(order.getSn());
                AppServiceExtension appService = new AppServiceExtension.Builder().config(config).build();
                try {
                    com.wechat.pay.java.service.payments.app.model.PrepayWithRequestPaymentResponse response = appService.prepayWithRequestPayment(payRequest);
                    result.setSuccess(1);
                    JSONObject payData = JSON.parseObject(GsonUtil.toJson(response));//用GsonUtile处理SerializedName注解转为JSONString，解决对象属性和SerializedName不一致的问题，然后再转为JSON对象返回。
                    System.out.println("payData:\n" + payData.toJSONString());
                    System.out.println("payRequest:\n" + payRequest.toString());
                    order.setOnlinePaidData(JSON.toJSONString(new HashMap<String, Object>(2) {{
                        put("payData", payData);
                        put("payRequest", JSON.parseObject(GsonUtil.toJson(payRequest)));
                    }}));
                    result.setData(payData);
                } catch (ServiceException e) {
                    result.setError(new MyException(e.getErrorCode(), e.getErrorMessage()));
                    Logger.getLogger(getClass()).error("访问微信后端保存，返回错误信息:" + e.getMessage(), e);
                }
            }
            System.out.println("OK");
        }
        return result;
    }

    @Override
    public JsonResult refundOrder(HttpServletRequest request, EcOrder order) {
        JsonResult result = new JsonResult();
        EcPayLog payLog = ecService.getPayLogByOrderSn(order.getSn(), Boolean.TRUE);
        TpMerchant merchant = platformService.getMerchantByMchid(order.getMchid());
        if(merchant==null || StringUtils.isBlank(merchant.getMchid())) {
            result.setError(new MyException("-1", "商户未找到，请联系开发人员检查代码及商户表！"));
        }
        Config jsapiConfig = getMerchantService(merchant);
        if(jsapiConfig==null) {
            result.setError(new MyException("-1", "商户未找到，请联系运营人员检查商户表！"));
        } else {
            CreateRequest refundRequest = new CreateRequest();
            refundRequest.setTransactionId(payLog.getTgOrderSn());
            refundRequest.setOutTradeNo(order.getSn());
            refundRequest.setOutRefundNo(UUID.randomUUID().toString().replace("-", "").toUpperCase());
            refundRequest.setNotifyUrl(GetLocalIPUtils.getRootPath(request)+"/tp/mchid/"+order.getMchid()+"/wxReceivePayNotify.do");
//            refundRequest.setGoodsDetail();
            AmountReq amountReq = new AmountReq();
            amountReq.setTotal(Long.valueOf(payLog.getPayerTotal()));
            amountReq.setRefund(Long.valueOf(payLog.getPayerTotal()));
//            FundsFromItem fromItem = new FundsFromItem();
//            fromItem.setAmount(Long.valueOf(payLog.getPayerTotal()));
//            fromItem.setAccount(Account.AVAILABLE);
//            amountReq.setFrom(new ArrayList<FundsFromItem>(1){{add(fromItem);}});
            amountReq.setCurrency(payLog.getCurrency());
            refundRequest.setAmount(amountReq);
            RefundService service = new RefundService.Builder().config(jsapiConfig).build();
            try {
                Refund refundResponse = service.create(refundRequest);
                System.out.println("Refund \n" + JSON.toJSONString(JSON.parseObject(GsonUtil.toJson(refundResponse))));
                ecService.saveRefund(refundResponse, refundRequest, order);
                System.out.println("OK");
            } catch (HttpException e) { // 发送HTTP请求失败
                Logger.getLogger(getClass()).error("refundService.create error", e);
                // 调用e.getHttpRequest()获取请求打印日志或上报监控，更多方法见HttpException定义
            } catch (ServiceException e) { // 服务返回状态小于200或大于等于300，例如500
                Logger.getLogger(getClass()).warn("refundService.create error"+e.getMessage(), e);
                String responseBody = e.getResponseBody();
                JSONObject jsonObject = JSON.parseObject(responseBody);
                result = new JsonResult(new MyException(jsonObject.getString("code"), jsonObject.getString("message")));
                // 调用e.getResponseBody()获取返回体打印日志或上报监控，更多方法见ServiceException定义
            } catch (MalformedMessageException e) { // 服务返回成功，返回体类型不合法，或者解析返回体失败
                Logger.getLogger(getClass()).error("refundService.create error", e);
                // 调用e.getMessage()获取信息打印日志或上报监控，更多方法见MalformedMessageException定义
            }
        }
        return result;
    }
}