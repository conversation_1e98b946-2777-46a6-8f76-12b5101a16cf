import auth from "@/sys/auth"
import { Request } from "@/utils/request";
import qs from 'qs'

/**
 * 权限验证
 * @param params
 * @returns  
 */
export function authInit() {
  console.log('authInit0', location.search, auth.isDevEnv() && auth.getToken(), auth.isDevEnv(), auth.getToken())
  let params = qs.parse(location.search, {ignoreQueryPrefix: true})
  console.log('authInit params', params)
  if (params.code && params.tgCode && params.appId) {
    console.log('authInit navigationBarTitleText', params.navigationBarTitleText)
    console.log('code', params.code)
    console.log('tgCode', params.tgCode)
    console.log('appId', params.appId)
    console.log('authInit mpCodeLogin.do')
    return Request({
      url: '/tp/mpCodeLogin.do',
      method: 'post',
      data: params
    })
  } else if (auth.isLogged() && params.url) {
    console.log('params.url', params.url)
    return new Promise(resolve => {
      console.log('authInit2')
      localStorage.setItem('webViewParams', JSON.stringify(params))
      resolve({success: 301, url: params.url, params: params})
    })
  } else if (auth.isDevEnv() && auth.isLogged()) {
    console.log('authInit dev')
    if (params.url) {
      return new Promise(resolve => {
        console.log('authInit2')
        resolve({success: 301, url: params.url})
      })
    } else {
      return new Promise(resolve => {
        console.log('authInit2')
        resolve({success: 200})
      })
    }
  }
}
/**
 * demo
 * @param params
 * @returns {AxiosPromise}
 */
export function getList(folerid) {
  return Request({
    url: "/user",
    method: "post",
    data: { id: folerid },
  })
}
/**
 * 获取子文件夹 和 子文件
 * @param folerid
 * @returns {AxiosPromise}
 */
 export function getWxHomePage() {
  return Request({
    url: "mpRes/getWeChatHome.do",
    method: "post",
  })
}


/**
 * 获取子文件夹 和 子文件
 * @param folerid
 * @returns {AxiosPromise}
 */
export function getWxParentAndChildDir(folerid) {
  return Request({
    url: "mpRes/getWeChatParentAndChildDir.do",
    method: "post",
    data: { category: folerid },
  })
}

/**
 * 搜索文件
 * @param searchtxt
 * @returns {AxiosPromise}
 * 入参说明：
 *  1 findName String 文件名字
 *  2 pageSize Integer 条数
 *  3 currentPageNo Integer 页码
 */
export function getSearchFile(searchtxt) {
  return Request({
    url: "mpRes/weChatFindFile.do",
    method: "post",
    data: { findName: searchtxt, pageSize: 200, currentPageNo: 1 },
  })
}

/**
 * 文件详情
 * @param searchtxt
 * @returns {AxiosPromise}
 */
export function getFileDetails(fileID) {
  return Request({
    url: "mpRes/getWeChatFileMes.do",
    method: "post",
    data: { file: fileID },
  })
}

export function newOrder(skuIds, module) {
  return Request({
    url: "ec/newOrder.do",
    method: "post",
    data: {
      skuIds: skuIds,
      module: module
    }
  })
}
export function createOrder(orderJson) {
  return Request({
    url: "ec/createOrder.do",
    method: "post",
    data:{
      orderJson: orderJson
    }
  })
}
export function payResult(orderId, payRes) {
  return Request({
    url: "ec/payResult.do",
    method: "post",
    data:{
      orderId: orderId,
      payRes: payRes
    }
  })
}
export function myOrders(module, cateActive,search) {
    return Request({
        url: "ec/myOrders.do",
        method: "post",
        data:{
          module: module,
          catelog: cateActive,
          search: search
        }
    })
}
export function cancelOrder(orderId) {
  return Request({
    url: "ec/cancelOrder.do",
    method: "post",
    data:{
      orderId: orderId
    }
  })
}
export function deleteOrder(orderId) {
  return Request({
    url: "ec/deleteOrder.do",
    method: "post",
    data:{
      orderId: orderId
    }
  })
}
export function refundOrder(orderId) {
  return Request({
    url: "ec/refundOrder.do",
    method: "post",
    data:{
      orderId: orderId
    }
  })
}