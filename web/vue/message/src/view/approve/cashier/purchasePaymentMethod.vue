<template>
  <div id="purchasePaymentMethod">
    <TY_NavTop :title="ttl" :isButton="true" @toggleSubmit="selectInvoiceOk"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="tip2">{{tip}}</div>
      <div class="list">
        <ul v-if="type == 2 || type == 3">
          <li v-for="(item, index) in list" v-bind:key="index">
            <div><el-radio v-model="selectItem"  :label="item">{{''}}</el-radio></div>
            <div>
              <p>所开具发票或收据的金额 {{ item.billAmount.toFixed(2) }}</p>
              <span>{{ type == 2 ? '支票号':'汇票号' }}  {{ item.returnNo }}</span>
              <span class="marL50">到期日 {{ item.expireDate | formatDay('YYYY-MM-DD')}}</span>
            </div>
            <div>
              <span class="linkBtn" @click="invoiceInfo(item)">查看</span>
            </div>
          </li>
        </ul>
        <ul class="ul6" v-if="type == 6">
          <li v-for="(item, index) in list" v-bind:key="index" v-if="item.isPublic== 1">
            <div><el-radio v-model="selectItem"  :label="item">{{''}}</el-radio></div>
            <div>
              <span>{{ item.isPublic== 1 ? '对公户': '非对公户' }}</span>
              <span>{{ item.bankName }}</span>
              <span class="marL50"> {{ item.account }}</span>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>
<style lang="less">
#purchasePaymentMethod{
  .tip2{ background: #fff; line-height: 40px; padding:0 10px; }
  .linkBtn{ color:#5d9cec; cursor:default; margin-right:20px; }
  .linkBtn:hover{ text-decoration:underline;  }
  ul>li:nth-child(odd){ background: #fff; }
  ul>li:nth-child(even){ background: #ddd; }
  ul>li{ display: flex; margin-left:-40px; padding: 7px 0 0 0; }
  ul>li>div:nth-child(1){ flex:1; }
  ul>li>div:nth-child(1) label{ position: relative; top: 6px;  }
  ul.ul6>li>div:nth-child(1) label{ position: relative; top:-4px;  }
  ul>li>div:nth-child(2){ flex:6; }
  ul>li>div:nth-child(2) .marL50{ display: inline-block; margin-left:14px;  }
  ul>li>div:nth-child(3){ flex:1; line-height: 50px }
}
</style>
<script>
export default {
  name: 'purchasePaymentMethod',
  data () {
    return {
      selectItem: {},
      selectInvoice: {},
      tip: '',
      ttl: '',
      loading: true,
      type: 1 // 2 -外部转账支票 3-外部承兑汇票
    }
  },
  created () {
    let that = this
    that.type = this.$route.params.type
    let parampun = Number(this.$route.params.pund)
    let planAmount = this.$route.params.mount
    that.planamount = planAmount
    that.pund = Number(parampun)
    if (Number(that.type) === 2) {
      that.tip = '以下为可供选择的外部转账支票'
    } else if (Number(that.type) === 3) {
      that.tip = '以下为可供选择的外部承兑汇票'
    } else if (Number(that.type) === 6) {
      that.tip = '请选择开户行'
    }
    if (that.pund === 3 || that.pund === '3') {
      that.ttl = '多收来的款'
    } else {
      that.ttl = '采购部门的付款'
    }
    that.getInvoiceList()
    that.selectInvoice = this.$store.getters.getPurchaseInvoice
  },
  destroyed: function () {
  },
  methods: {
    getInvoiceList: function () {
      let that = this
      if (Number(that.type) === 2 || Number(that.type) === 3) {
        this.axios.post('../../../data/chooseReturn.do', { 'type': that.type })
          .then(function (response) {
            that.loading = false
            let res = response.data.data
            that.list = res.content || []
            that.point = res// 票据
          })
          .catch(function (error) {
            console.log(error)
          })
      } else {
        this.axios.post('../../../account/getAccountKinds.do', { 'accountType': 2, 'accountStatus': 1 })
          .then(function (response) {
            that.loading = false
            let res = response.data.data
            that.list = res.financeAccounts || []
            that.bank = res
          })
          .catch(function (error) {
            console.log(error)
          })
      }
    },
    invoiceInfo: function (item2) {
      let that = this
      let item = item2
      item.type = that.type
      item.pund = that.pund
      console.log(item)
      localStorage.setItem('invoiceTicketInfo', JSON.stringify(item))
      that.$router.push({
        path: `/purchasePaymentInvoiceInfo`
      })
    },
    selectInvoiceOk: function () {
      let that = this
      if (that.selectItem.id) {
        that.selectInvoice.selectItem = that.selectItem
        this.$store.dispatch('setPurchaseInvoice', that.selectInvoice)
        if (Number(that.type) === 2 || Number(that.type) === 3) {
          if (Number(that.pund) === 3) {
            this.$router.push({
              path: `/overreceivedMoneyApprove1/${that.selectInvoice.id}/${that.selectInvoice.updatePay}/1`
            })
          } else if (that.pund !== 1) {
            this.$router.push({
              path: `/purchasePaymentFinanceFu/${that.selectInvoice.id}/${that.selectInvoice.updatePay}/1/0/1`
            })
          } else {
            this.$router.push({
              path: `/purchasePaymentFinanceFu/${that.selectInvoice.id}/${that.selectInvoice.updatePay}/1/1/1`
            })
          }
        } else {
          this.$router.push({
            path: `/purchasePaymentMethod2/${that.pund}`
          })
        }
      } else {
        that.$kiko_message('请先选择')
      }
    }
  }
}
</script>
