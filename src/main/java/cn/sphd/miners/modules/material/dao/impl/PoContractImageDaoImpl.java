package cn.sphd.miners.modules.material.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.material.dao.PoContractImageDao;
import cn.sphd.miners.modules.material.entity.PoContractImage;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
@Repository
public class PoContractImageDaoImpl extends BaseDao<PoContractImage, Serializable> implements PoContractImageDao {
}
