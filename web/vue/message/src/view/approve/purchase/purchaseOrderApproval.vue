<template>
  <div id="purchaseOrderApproval">
    <TY_NavTop title="采购订单审批" isSearch="true" @toggleList="search"></TY_NavTop>
    <TY_Tabs :value="tabValue" v-loading.fullscreen.lock="loading">
      <TY_TabPane label="待处理" name="1" closable='false' :msgCount="applyCount">
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.purchaseOrder.id)" v-for="(item, index) in handleList" v-bind:key="index">
            <div class="orderItem">
              <div><div>{{item.purchaseOrder.supplierName}}</div><div>{{item.purchaseOrder.type | formatType}}</div></div>
              <div><div>订单内的材料：{{item.typeKind}}种</div><div>{{item.purchaseOrder.createName}} {{item.purchaseOrder.createDate | formatDay}}</div></div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="已批准" name="2" closable='false'>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.purchaseOrder.id)" v-for="(item, index) in approvalList" v-bind:key="index">
            <div class="orderItem">
              <div><div>{{item.purchaseOrder.supplierName}}</div><div>{{item.purchaseOrder.type | formatType}}</div></div>
              <div><div>订单内的材料：{{item.typeKind}}种</div><div>{{item.purchaseOrder.createName}} {{item.purchaseOrder.createDate | formatDay}}</div></div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>
<style>
  #purchaseOrderApproval .container>div>a{  background: #fff; }
  #purchaseOrderApproval .container>div>a:nth-child(odd){ background:#f3f3f3;  }
  .orderItem{  width: 100%; font-size:1em;   }
  .orderItem:nth-child(even){ background: #eee; }
  .orderItem>div{ display: flex; }
  .orderItem>div>div{ flex:1; }
  .orderItem>div>div:nth-child(2){ text-align: right; margin-left: -30px;  font-size: 0.7em; }
</style>
<script>
export default {
  name: 'purchaseOrderApproval',
  data () {
    return {
      handleList: [],
      approvalList: [],
      listenersUid: [],
      nowTime: 0,
      tabValue: '1',
      loading: false
    }
  },
  computed: {
    applyCount: function () {
      return this.handleList.length
    }
  },
  created () {
    let that = this
    this.loading = true
    this.axios.get('../../../reimburseWindow/getCurrentTime.do')
      .then(function (response) {
        let time = response.data.time
        that.nowTime = time
      })
      .catch(function (error) {
        console.log(error)
      })
    this.listenersUid = [
      this.sphdSocket.subscribe('purchaseOrderApprovalHandle', function (data) {
        that.loading = false
        let listData = JSON.parse(data)
        that.handleList = listData.listMap
        console.log('personnelReimbursePend session Socket received OK:' + data)
      }),
      this.sphdSocket.subscribe('purchaseOrderApprovalApproval', function (data) {
        that.loading = false
        let listData = JSON.parse(data)
        that.approvalList = listData.listMap
        console.log('personnelReimbursePend session Socket received OK:' + data)
      }),
      this.sphdSocket.subscribe('purchaseOrderApprovalHandle', function (data) {
        console.log('审批人订阅返回值:')
        let getData = JSON.parse(data)
        console.log(getData)
        let poOrders = getData.poOrders
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.handleList.unshift(poOrders)
        } else if (operate < 0) {
          let reimburseId = poOrders.purchaseOrder.id
          let _index = -1
          that.handleList.forEach(function (item, index) {
            if (item.purchaseOrder.id === reimburseId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.handleList.splice(_index, 1)
          }
        }
      }, null, 'user'),
      this.sphdSocket.subscribe('purchaseOrderApprovalApproval', function (data) {
        console.log('审批人订阅返回值:')
        let getData = JSON.parse(data)
        console.log(getData)
        let poOrders = getData.poOrders
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.approvalList.unshift(poOrders)
        } else if (operate < 0) {
          let reimburseId = poOrders.purchaseOrder.id
          let _index = -1
          that.approvalList.forEach(function (item, index) {
            if (item.purchaseOrder.id === reimburseId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.approvalList.splice(_index, 1)
          }
        }
      }, null, 'user')
    ]
    let userId = this.sphdSocket.user.userID
    let session = this.sphdSocket.sessionid
    console.log(userId)
    console.log(session)
    let json = { userId: userId, session: session }
    this.sphdSocket.send('getPurchaseOrderApproval', json)
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  filters: {
    formatType: function (type) {
      let str = ''
      switch (type) {
        case '1':
        case 1:
          str = '新单'
          break
        case '2':
        case 2:
          str = '预警'
          break
        case '3':
        case 3:
          str = '个人申购的新材料'
          break
        case '4':
        case 4:
          str = '补货'
          break
        case '5':
        case 5:
          str = '零星采购'
          break
        default:
      }
      return str
    }
  },
  methods: {
    jump: function (id) {
      window.parent.floatToPage('../../../../purchaseOrderApproval/purchasePage.do', { 'id': id, 't': 1 })
    },
    search: function () {
      this.$router.push({
        path: `/purchaseOrderSearch/1`
      })
    }
  }
}
</script>
