package cn.sphd.miners.modules.finance.service;

import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.finance.dto.*;
import cn.sphd.miners.modules.finance.entity.PersonnelPay;
import cn.sphd.miners.modules.finance.entity.PersonnelPayHistory;
import cn.sphd.miners.modules.finance.entity.PersonnelPayPeroid;
import cn.sphd.miners.modules.finance.entity.PersonnelSalary;
import cn.sphd.miners.modules.system.entity.User;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @ClassName SalaryService
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/6/7 8:17
 * @Version 1.0
 */
public interface SalaryService {
    //type 类型:1-工资,2-所得税,3-社保,4-公积金【t_personnel_pay_peroid，t_personnel_pay对应的历史表均适用】
    enum type {
        salary("salary", (byte)1),
        incomeTax("incomeTax", (byte)2),
        socialSecurity("socialSecurity", (byte)3),
        accumulationFund("accumulationFund", (byte)4);
        private String name;
        private Byte index;
        type(String name, Byte index) {
            this.name = name;
            this.index = index;
        }
        public String getName() {
            return name;
        }
        public Byte getIndex() {
            return index;
        }
    }

    //payWay 发放方式:1-现金,2-转帐
    enum payWay {
        cash("cash", (byte)1),
        bankTransfer("bankTransfer", (byte)2);
        private String name;
        private Byte index;
        payWay(String name, Byte index) {
            this.name = name;
            this.index = index;
        }
        public String getName() {
            return name;
        }
        public Byte getIndex() {
            return index;
        }
    }


    void salaryIndexInitialization(int org, String year);
    void salaryByYearInitialization(int org, String year);

    SalaryIndex annualSalary(int org, String year);
    MonthSalary monthSalaryInfo(int org, String month);
    int addMonthSalary(User user,List<PersonnelSalary> personnelSalaryList,int type);
    int updateMonthSalary(User user,List<PersonnelSalary> personnelSalaryList,int type);
    UpdateSalaryInfo updateMonthSalaryInfo(User user, String month, int type);
    MonthSalary updateMonthSalaryDetails(User user, String month, int type,int updateNo);
    List<SalaryTotalByYear> salaryTotalGroupYear(User user);

    List<String> selectYearListByOrg(int org, String year);

    SalaryAllUser selectSalaryAllUser(Integer org);
    SalaryByUser selectSalaryByUser(Integer userId,String year);
    List<PersonnelSalary> selectOrgBySalary();

    void addPersonnelPayPeroid(User user);

    //typeSource 1-pc 2-手机端
    List<PersonnelPayPeroid> getgetSalaryList(User user, Integer yearDate,Integer sort,Integer typeSource);

    Map<String,Object> manualEntry(User user, String userList, Date payTime, byte payWay, BigDecimal pay, byte type, String period,Integer accountId,String summary,Date periodDate);

    PersonnelPayPeroid getPeriod(Integer oid,String yearMonth,Byte type,Integer sort);  //获取职工某月薪资的情况

    List<PersonnelPay> getPersonnelPays(Integer oid,Byte type,String period,Integer userId,Integer versionNo,Integer groupType,List<Integer> userQuit,Integer typeQuit);

    Map<String,Object> updateManual(User user, String userList, Date payTime, Integer payWay, BigDecimal pay, Integer type, String period,Integer accountId,Integer versionNo,String operation);

    PersonnelPayHistory getPersonnelPayHistory(Integer oid, Integer payHistoryId, Byte type, String period,Integer versionNo,Integer payId,Integer detailType);

    Map<String,Object> getPayByUserList(User user, String period, Byte type,Integer versionNo,Integer groupType);

    List<PersonnelPayHistory> getRecords(Integer oid,Byte type,String period,Integer versionNo,Integer subVersionNo,Integer groupType);

    Byte IntegerToByte(Integer type,Integer kind);

    Map<String,Object> getPersonnelPayMonth(Integer oid,Integer userId, Byte type, String period,Integer groupType);

    PersonnelPay getPersonnelPay(Integer oid,String period,Byte typeByte,Integer versionNo);

    Map<String,Object> getRecordDetails(Integer oid,Byte type,String period,Integer versionNo,Integer subVersionNo);

    Map<String, Object> getPayHistoryBySubversionNo(User user,String period,Integer versionNo,Integer subVersionNo,Byte type);

    Map<String, Object> getSalaryList(Integer org,String beginDate,String endDate,Byte type);

    Map<String, Object> getSalaryByUser(Integer org,Byte type,Integer isDuty);

    Map<String, Object> getSalaryByYear(Integer org,Integer userId,Byte type);

    Map<String, Object> getSalaryByUserYear(Integer org,Integer userId,Integer yearDate,Byte type);

    Map<String,Object> getRecordDetailList(Integer oid,Byte type,String period,Integer versionNo,Integer subVersionNo);


}
