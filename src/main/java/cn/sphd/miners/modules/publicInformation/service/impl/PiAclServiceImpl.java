package cn.sphd.miners.modules.publicInformation.service.impl;

import cn.sphd.miners.modules.publicInformation.dao.PiAclDetailDao;
import cn.sphd.miners.modules.publicInformation.dao.PiAclLogDao;
import cn.sphd.miners.modules.publicInformation.dao.PiInfoAclDao;
import cn.sphd.miners.modules.publicInformation.entity.PiAclDetail;
import cn.sphd.miners.modules.publicInformation.entity.PiAclLog;
import cn.sphd.miners.modules.publicInformation.entity.PiInfo;
import cn.sphd.miners.modules.publicInformation.entity.PiInfoAcl;
import cn.sphd.miners.modules.publicInformation.service.PiAclService;
import cn.sphd.miners.modules.publicInformation.service.PiInfoServies;
import cn.sphd.miners.modules.system.dto.OrganizationDto;
import cn.sphd.miners.modules.system.dto.UserHonePageDto;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgService;
import cn.sphd.miners.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Service("piAclService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED)
public class PiAclServiceImpl implements PiAclService {

    @Autowired
    PiInfoAclDao piInfoAclDao;
    @Autowired
    PiAclLogDao piAclLogDao;
    @Autowired
    PiAclDetailDao piAclDetailDao;


    @Autowired
    OrgService orgService;
    @Autowired
    UserService userService;
    @Autowired
    PiInfoServies piInfoServies;


    @Override
    public Long piAclNumByFile(Integer fileId) {
        String hql = "select count(id) from PiInfoAcl where file = :file";
        HashMap<String, Object> param = new HashMap<>();
        param.put("file", fileId);
        Long num = (Long) piInfoAclDao.getByHQLWithNamedParams(hql,param);
        return num;
    }

    @Override
    public List<PiInfoAcl> listPiAcl(Integer userId, Integer fileId) {
        String hql = " from PiInfoAcl where";
        List<PiInfoAcl> listAcl = null;
        HashMap<String, Object> param = new HashMap<>();
        if (fileId != null) {
            hql = hql + " file = :file";
            param.put("file", fileId);
            listAcl = piInfoAclDao.getListByHQLWithNamedParams(hql,param);
            if (!listAcl.isEmpty()) {
                for (PiInfoAcl p : listAcl) {
                    UserHonePageDto userHonePageDto = userService.getUserHonePageDtoByUserId(p.getUser());
                    p.setUserHonePageDto(userHonePageDto);
                }
            }
        }
        if (userId != null) {
            hql = hql + " user = :user";
            param.put("user", userId);
            listAcl = piInfoAclDao.getListByHQLWithNamedParams(hql,param);
            if (!listAcl.isEmpty()) {
                for (PiInfoAcl p : listAcl) {
                    PiInfo piInfo = piInfoServies.piInfoSingle(p.getFile());
                    p.setPiInfo(piInfo);
                }
            }
        }
        return listAcl;
    }

    @Override
    public List<OrganizationDto> getPiInfoAuthTree(User user, Integer fileId) {
        String hql = "select user from PiInfoAcl where file = "+ fileId;
        List<Integer> listAcl = piInfoAclDao.getListByHQLWithNamedParams(hql,null);
        List<OrganizationDto> list = orgService.getOrgWithAuthUserTreeLocking(user.getOid(), listAcl);
        return list;
    }

    @Override
    public void addPiInfoAuth(User user, Integer fileId, List<PiInfoAcl> listPiAcl) {
        Date date = new Date();
        PiAclLog piAclLog = this.addPiLog(user, fileId, listPiAcl.size(), null, Byte.valueOf("1"), date);
        for (PiInfoAcl pa : listPiAcl) {
            this.addPiDetail(user, pa.getUser(), fileId, piAclLog.getId(), Byte.valueOf("1"), date);
            pa.setFile(fileId);
            pa.setOrg(user.getOid());
            pa.setCreator(user.getUserID());
            pa.setCreateName(user.getUserName());
            pa.setCreateDate(date);
            piInfoAclDao.save(pa);
        }
    }

    @Override
    public void delPiInfoAuth(User user, Integer fileId, List<PiInfoAcl> listPiAc) {
        Date date = new Date();
        PiAclLog piAclLog = this.addPiLog(user, fileId, null, listPiAc.size(), Byte.valueOf("3"), date);
        for (PiInfoAcl pa : listPiAc) {
            this.addPiDetail(user, pa.getUser(), fileId, piAclLog.getId(), Byte.valueOf("2"), date);
            String hql = "delete from PiInfoAcl where file = :file and user = :user";
            HashMap<String, Object> param = new HashMap<>();
            param.put("file", fileId);
            param.put("user", pa.getUser());
            int state = piInfoAclDao.queryHQLWithNamedParams(hql,param);
        }
    }

    @Override
    public List<UserHonePageDto> listAuthAllUser(User user) {
        String hql = "select user from PiAclDetail where org = :org group by user";
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", user.getOid());
        List<Integer> list = piAclDetailDao.getListByHQLWithNamedParams(hql,param);
        List<UserHonePageDto> listUser = new ArrayList<>();
        for (Integer u : list) {
            UserHonePageDto authUser = userService.getUserHonePageDtoByUserId(u);
            listUser.add(authUser);
        }
        return listUser;
    }

    @Override
    public List<PiAclLog> listAclLog(User user, Date beginDate, Date endDate) {
        String hql = " from PiAclLog where org = :org and createDate >= :beginDate and createDate <= :endDate order by createDate desc";
        HashMap<String, Object> param = new HashMap<>();
        param.put("org", user.getOid());
        param.put("beginDate", beginDate);
        param.put("endDate", endDate);
        List<PiAclLog> listAclLog = piAclLogDao.getListByHQLWithNamedParams(hql,param);
        for (PiAclLog p : listAclLog){
            PiInfo piInfo = piInfoServies.piInfoSingle(p.getFile());
            p.setFileName(piInfo.getName());
        }
        return listAclLog;
    }

    @Override
    public List<PiAclDetail> listAclDetail(Integer userId, Integer aclLog) {
        String hql = " from PiAclDetail where ";
        HashMap<String, Object> param = new HashMap<>();
        List<PiAclDetail> list = null;
        if (userId != null) {
            hql = hql + " user = :user order by createDate desc";
            param.put("user", userId);
            list = piAclDetailDao.getListByHQLWithNamedParams(hql,param);
            if (!list.isEmpty()) {
                for (PiAclDetail p : list) {
                    PiInfo piInfo = piInfoServies.piInfoSingle(p.getFile());
                    p.setFileName(piInfo.getName());
                }
            }
        }
        if (aclLog != null) {
            hql = hql + " aclLog = :aclLog order by createDate desc";
            param.put("aclLog", aclLog);
            list = piAclDetailDao.getListByHQLWithNamedParams(hql, param);
            if (!list.isEmpty()) {
                for (PiAclDetail p : list) {
                    UserHonePageDto userHonePageDto = userService.getUserHonePageDtoByUserId(p.getUser());
                    p.setUserHonePageDto(userHonePageDto);
                }
            }
        }
        return list;
    }

    //新增使用权限操作
    private PiAclLog addPiLog(User user, Integer fileId, Integer increasedNum, Integer decreasedNum, Byte operateType, Date date){
        PiAclLog piAclLog = new PiAclLog();
        piAclLog.setFile(fileId);
        piAclLog.setOperateType(operateType);
        if (increasedNum != null) {
            piAclLog.setIncreasedNum(increasedNum);
        }
        if (decreasedNum != null) {
            piAclLog.setDecreasedNum(decreasedNum);
        }
        piAclLog.setOrg(user.getOid());
        piAclLog.setCreator(user.getUserID());
        piAclLog.setCreateName(user.getUserName());
        piAclLog.setCreateDate(date);
        piAclLogDao.save(piAclLog);
        return piAclLog;
    }

    //新增使用权限操作的明细记录
    private PiAclDetail addPiDetail(User user, Integer userId, Integer fileId, Integer piAclLogId, Byte aclType, Date date){
        PiAclDetail piAclDetail = new PiAclDetail();
        piAclDetail.setAclLog(piAclLogId);
        piAclDetail.setFile(fileId);
        piAclDetail.setAclType(aclType);
        piAclDetail.setUser(userId);
        piAclDetail.setOrg(user.getOid());
        piAclDetail.setCreator(user.getUserID());
        piAclDetail.setCreateName(user.getUserName());
        piAclDetail.setCreateDate(date);
        piAclDetailDao.save(piAclDetail);
        return piAclDetail;
    }


}
