<template>
  <div id="addService">
    <TY_NavTop title="增值服务" isSearch="true" @toggleList="search"></TY_NavTop>
    <div class="container">
      <div class="nowTime">以下为有待审批的增值服务</div>
      <div>
        <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in toApply" v-bind:key="index">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <div>{{item.fullName}}</div>
              <div style="margin-left: 20px">{{item.createName}} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            </div>
          </div>
          <div class="ui-cell__ft"></div>
        </a>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'addService',
    data () {
      return {
        toApply: [],
        listenersUid: []
      }
    },
    created () {
      let that = this
      this.axios.post('../../../special/getIncrementApplies.do', {
        userId: this.sphdSocket.user.userID
      })
        .then(function (response) {
          console.log(response)
          let getData = response.data.data
          that.toApply = getData
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    methods: {
      jump: function (id) {
        parent.location.href = '../../../special/toTryApplyInfo.do?type=31&id=' + id
      },
      search: function () {
        this.$router.push({
          path: `/addServiceApplyQuery`
        })
      }
    }
  }
</script>
