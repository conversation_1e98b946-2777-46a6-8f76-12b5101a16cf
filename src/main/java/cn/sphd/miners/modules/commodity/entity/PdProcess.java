/**
 * Copyright (c) minuteproject, <EMAIL>
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License")
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * More information on minuteproject:
 * twitter @minuteproject
 * wiki http://minuteproject.wikispaces.com
 * blog http://minuteproject.blogspot.net
 *
 */
/**
 * template reference :
 * - Minuteproject version : 0.9.5
 * - name      : DomainEntityJPA2Annotation
 * - file name : DomainEntityJPA2Annotation.vm
 * - time      : 2016/09/12 ��Ԫ at 14:29:06 GMT+08:00
 */
package cn.sphd.miners.modules.commodity.entity;

//MP-MANAGED-ADDED-AREA-BEGINNING @import@
//MP-MANAGED-ADDED-AREA-ENDING @import@

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 *
 * <p>Title: TPdProcess</p>
 *
 * <p>Description: Domain Object describing a TPdProcess entity</p>
 *
 */
@Entity (name="PdProcess")
@Table (name="t_pd_process")
public class PdProcess implements Serializable {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="inner_sn"  , length=50 , nullable=true , unique=false)
    private String innerSn;

    @Column(name="sn"  , length=50 , nullable=true , unique=false)
    private String sn;

    @Column(name="name"  , length=100 , nullable=true , unique=false)
    private String name;

    @Column(name="composition"   , nullable=true , unique=false)
    private Integer composition;

    @Column(name="comp_desc"  , length=255 , nullable=true , unique=false)
    private String compDesc;

    @Column(name="cavity_per_model"   , nullable=true , unique=false)
    private Integer cavityPerModel;

    @Column(name="unit_consumption"   , nullable=true , unique=false)
    private BigDecimal unitConsumption;

    @Column(name="materia_utilization"   , nullable=true , unique=false)
    private BigDecimal materiaUtilization;

    @Column(name="loss_quato"   , nullable=true , unique=false)
    private BigDecimal lossQuato;

    @Column(name="rejection_rate_quato"   , nullable=true , unique=false)
    private BigDecimal rejectionRateQuato;

    @Column(name="craft_instructor"  , length=100 , nullable=true , unique=false)
    private String craftInstructor;

    @Column(name="process_instructor"  , length=100 , nullable=true , unique=false)
    private String processInstructor;

    @Column(name="packaging_instructor"  , length=100 , nullable=true , unique=false)
    private String packagingInstructor;

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Timestamp createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Timestamp updateDate;



    @ManyToOne (fetch=FetchType.LAZY )
    @JoinColumn(name="product", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private PdBase product;

    @Column(name="product"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer product_;

//    @ManyToOne (fetch=FetchType.LAZY )
//    @JoinColumn(name="pdComposition", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
//    private PdComposition pdComposition;
//
//    @Column(name="pdComposition"  , nullable=true , unique=false, insertable=false, updatable=false)
//    private Integer pdComposition_;



    @Transient
    private BigDecimal netWeight; //产品重量

    @Transient
    private String productName;
    @Transient
    private String proComposition;//1-购买,2-制造,3-装配

    public BigDecimal getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }
    public String getProComposition() {
        return proComposition;
    }

    public void setProComposition(String proComposition) {
        this.proComposition = proComposition;
    }






    //MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-inner_sn@
    public String getInnerSn() {
        return innerSn;
    }

    public void setInnerSn (String innerSn) {
        this.innerSn =  innerSn;
    }

//MP-MANAGED-UPDATABLE-ENDING

    //MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-sn@
    public String getSn() {
        return sn;
    }

    public void setSn (String sn) {
        this.sn =  sn;
    }

//MP-MANAGED-UPDATABLE-ENDING

    //MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-name@
    public String getName() {
        return name;
    }

    public void setName (String name) {
        this.name =  name;
    }

//MP-MANAGED-UPDATABLE-ENDING

    //MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-composition@
    public Integer getComposition() {
        return composition;
    }

    public void setComposition (Integer composition) {
        this.composition =  composition;
    }

//MP-MANAGED-UPDATABLE-ENDING

    //MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-comp_desc@
    public String getCompDesc() {
        return compDesc;
    }

    public void setCompDesc (String compDesc) {
        this.compDesc =  compDesc;
    }

//MP-MANAGED-UPDATABLE-ENDING

    //MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-cavity_per_model@
    public Integer getCavityPerModel() {
        return cavityPerModel;
    }

    public void setCavityPerModel (Integer cavityPerModel) {
        this.cavityPerModel =  cavityPerModel;
    }

//MP-MANAGED-UPDATABLE-ENDING

    //MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-unit_consumption@
    public BigDecimal getUnitConsumption() {
        return unitConsumption;
    }

    public void setUnitConsumption (BigDecimal unitConsumption) {
        this.unitConsumption =  unitConsumption;
    }

//MP-MANAGED-UPDATABLE-ENDING

    //MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-materia_utilization@
    public BigDecimal getMateriaUtilization() {
        return materiaUtilization;
    }

    public void setMateriaUtilization (BigDecimal materiaUtilization) {
        this.materiaUtilization =  materiaUtilization;
    }

//MP-MANAGED-UPDATABLE-ENDING

    //MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-loss_quato@
    public BigDecimal getLossQuato() {
        return lossQuato;
    }

    public void setLossQuato (BigDecimal lossQuato) {
        this.lossQuato =  lossQuato;
    }

//MP-MANAGED-UPDATABLE-ENDING

    //MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-rejection_rate_quato@
    public BigDecimal getRejectionRateQuato() {
        return rejectionRateQuato;
    }

    public void setRejectionRateQuato (BigDecimal rejectionRateQuato) {
        this.rejectionRateQuato =  rejectionRateQuato;
    }

//MP-MANAGED-UPDATABLE-ENDING

    //MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-craft_instructor@
    public String getCraftInstructor() {
        return craftInstructor;
    }

    public void setCraftInstructor (String craftInstructor) {
        this.craftInstructor =  craftInstructor;
    }

//MP-MANAGED-UPDATABLE-ENDING

    //MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-process_instructor@
    public String getProcessInstructor() {
        return processInstructor;
    }

    public void setProcessInstructor (String processInstructor) {
        this.processInstructor =  processInstructor;
    }

//MP-MANAGED-UPDATABLE-ENDING

    //MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-packaging_instructor@
    public String getPackagingInstructor() {
        return packagingInstructor;
    }

    public void setPackagingInstructor (String packagingInstructor) {
        this.packagingInstructor =  packagingInstructor;
    }

//MP-MANAGED-UPDATABLE-ENDING

    //MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-memo@
    public String getMemo() {
        return memo;
    }

    public void setMemo (String memo) {
        this.memo =  memo;
    }

//MP-MANAGED-UPDATABLE-ENDING

    //MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-creator@
    public Integer getCreator() {
        return creator;
    }

    public void setCreator (Integer creator) {
        this.creator =  creator;
    }

//MP-MANAGED-UPDATABLE-ENDING

    //MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-create_name@
    public String getCreateName() {
        return createName;
    }

    public void setCreateName (String createName) {
        this.createName =  createName;
    }

//MP-MANAGED-UPDATABLE-ENDING

    //MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-create_date@
    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate (Timestamp createDate) {
        this.createDate =  createDate;
    }

//MP-MANAGED-UPDATABLE-ENDING

    //MP-MANAGED-UPDATABLE-BEGINNING-DISABLE @GETTER-SETTER-updator@
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator (Integer updator) {
        this.updator =  updator;
    }


    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName (String updateName) {
        this.updateName =  updateName;
    }


    public Timestamp getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate (Timestamp updateDate) {
        this.updateDate =  updateDate;
    }



    public PdBase getProduct () {
        return product;
    }

    public void setProduct (PdBase product) {
        this.product = product;
    }

    public Integer getProduct_() {
        return product_;
    }

    public void setProduct_ (Integer product) {
        this.product_ =  product;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }



//MP-MANAGED-ADDED-AREA-BEGINNING @implementation@
//MP-MANAGED-ADDED-AREA-ENDING @implementation@

}
