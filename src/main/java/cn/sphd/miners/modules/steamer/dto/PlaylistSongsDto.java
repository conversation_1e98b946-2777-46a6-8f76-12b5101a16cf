package cn.sphd.miners.modules.steamer.dto;

public class PlaylistSongsDto {

    private Long id;
    private Long playlist;
    private Long songs;
    private String name;
    private Boolean captionRequired;
    private String originalPath;
    private String trackDuration;
    private String bgmPath;
    private String bgmDuration;
    private String songDuration;
    private String rhythm;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPlaylist() {
        return playlist;
    }

    public void setPlaylist(Long playlist) {
        this.playlist = playlist;
    }

    public Long getSongs() {
        return songs;
    }

    public void setSongs(Long songs) {
        this.songs = songs;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getCaptionRequired() {
        return captionRequired;
    }

    public void setCaptionRequired(Boolean captionRequired) {
        this.captionRequired = captionRequired;
    }

    public String getOriginalPath() {
        return originalPath;
    }

    public void setOriginalPath(String originalPath) {
        this.originalPath = originalPath;
    }

    public String getTrackDuration() {
        return trackDuration;
    }

    public void setTrackDuration(String trackDuration) {
        this.trackDuration = trackDuration;
    }

    public String getBgmPath() {
        return bgmPath;
    }

    public void setBgmPath(String bgmPath) {
        this.bgmPath = bgmPath;
    }

    public String getBgmDuration() {
        return bgmDuration;
    }

    public void setBgmDuration(String bgmDuration) {
        this.bgmDuration = bgmDuration;
    }

    public String getSongDuration() {
        return songDuration;
    }

    public void setSongDuration(String songDuration) {
        this.songDuration = songDuration;
    }

    public String getRhythm() {
        return rhythm;
    }

    public void setRhythm(String rhythm) {
        this.rhythm = rhythm;
    }

    public PlaylistSongsDto() {

    }

    public PlaylistSongsDto(Long id, Long playlist, Long songs, String name, Boolean captionRequired, String originalPath, String trackDuration, String bgmPath, String bgmDuration, String songDuration) {
        this.id = id;
        this.playlist = playlist;
        this.songs = songs;
        this.name = name;
        this.captionRequired = captionRequired;
        this.originalPath = originalPath;
        this.trackDuration = trackDuration;
        this.bgmPath = bgmPath;
        this.bgmDuration = bgmDuration;
        this.songDuration = songDuration;
    }

    public PlaylistSongsDto(Long id, Long playlist, Long songs, String name, Boolean captionRequired, String originalPath, String trackDuration, String bgmPath, String bgmDuration, String songDuration, String rhythm) {
        this.id = id;
        this.playlist = playlist;
        this.songs = songs;
        this.name = name;
        this.captionRequired = captionRequired;
        this.originalPath = originalPath;
        this.trackDuration = trackDuration;
        this.bgmPath = bgmPath;
        this.bgmDuration = bgmDuration;
        this.songDuration = songDuration;
        this.rhythm = rhythm;
    }
}
