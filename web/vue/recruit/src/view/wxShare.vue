<template id="wxShare">
  <div class="wxShare">
    <van-overlay :show="loading">
      <div class="wrapper">
        <van-loading />
      </div>
    </van-overlay>
    <div v-show="page === 0">
      <van-empty :description="errorMsg" />
    </div>
    <div v-show="page === 1">
      <van-nav-bar
        title="招聘启事"
      />
      <div class="main_container">
        <div class="company" v-if="noticeInfo.introduceShow">
          {{noticeInfo.introduce}}
        </div>
        <div class="company">
          本公司现诚聘如下人才：
        </div>
        <van-cell-group>
          <van-cell>
            <van-row gutter="8">
              <van-col span="8"><b>岗位名称</b></van-col>
              <van-col span="8"><b>招聘人数</b></van-col>
              <van-col span="8"><b>操作</b></van-col>
            </van-row>
          </van-cell>
          <van-cell v-for="(item,index) in rdNoticePostList" v-bind:key="index">
            <van-row gutter="8">
              <van-col span="8">{{item.postName}}</van-col>
              <van-col span="8">{{item.planCount + '人'}}</van-col>
              <van-col span="8">
                <span class="link-blue" @click="seeDetail(item)">查看详情</span>
                <span class="link-blue" @click="accept(item)">应聘</span>
              </van-col>
            </van-row>
          </van-cell>
        </van-cell-group>
        <van-cell-group style="margin-top: 8px">
          <van-cell title="作息时间：" v-if="noticeInfo.workRestShow" :value="noticeInfo.workRest" />
          <van-cell title="工作地点：" v-if="noticeInfo.addressShow" :value="noticeInfo.address" />
          <van-cell title="补充说明：" v-if="noticeInfo.descriptionShow" :value="noticeInfo.description" />
          <van-cell title="提交截止日期：" :value="noticeInfo.deadline" />
          <van-cell title="联系人：" v-if="noticeInfo.contacterShow" :value="noticeInfo.contacter" />
          <van-cell title="电话：" v-if="noticeInfo.telephoneShow" :value="noticeInfo.telephone" />
          <van-cell title="邮箱：" v-if="noticeInfo.emailShow" :value="noticeInfo.email" />
          <van-cell :title="item.itemName + '：'" v-for="(item, index) in getContent(rdNoticeItemList, 'custom')" :value="item.content" v-bind:key="index"/>
        </van-cell-group>
      </div>
    </div>
  </div>
</template>

<style scoped>
body{
  padding:0;
  margin: 0;
}
.link-blue{
  display: inline-block;
  font-size: 13px;
  border: none;
  background: inherit;
  border-radius: 2px;
  font-weight: bold;
  cursor: pointer;
  margin-right: 4px;
}
.link-blue{
  color: #5d9cec;
}
.link-blue:hover {
  color: #558ed7;
}
.company{
  margin: 8px 16px;
  overflow: hidden;
  color: #515151;
  font-size: 14px;
  line-height: 21px;
  text-indent: 28px;
}
.wrapper{
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
</style>

<script>
// wyu：引相关的库
export default {
  name: 'wxShare',
  data () {
    return {
      rdNoticeItemList: [],
      rdNoticePostList: [],
      noticeInfo: {},
      loading: true,
      page: 1,
      errorMsg: ''
    }
  },
  created () {
    let noticeId = this.$route.params.id
    let userId = this.$route.params.userid
    let oid = this.$route.params.oid

    let that = this
    this.$http.get(window.parent.$.webRoot + '/resume/getRdnoticeInfo.do?id=' + noticeId).then(res => {
      console.log(res)
      let data = res.body.data
      let error = res.body.error
      if (error) {
        that.loading = false
        this.page = 0
        this.errorMsg = error.message
        return false
      } else {
        this.page = 1
      }
      let rdNotice = data.rdNotice
      that.$store.dispatch('setRecruit', {
        noticeId: noticeId,
        userId: userId,
        oid: oid,
        intervals: rdNotice.intervals
      })
      // let rdNoticeItemList = data.rdNoticeItemList
      let rdNoticeItemList = data.rdNoticeItemList
      let rdNoticePostList = data.rdNoticePostList
      // let rdNoticeAdditionalList = data.rdNoticeAdditionalList
      let beginTime = that.getContent(rdNoticeItemList, 'workRest', 'beginTime')
      let endTime = that.getContent(rdNoticeItemList, 'workRest', 'endTime')
      let address = that.getContent(rdNoticeItemList, 'address', 'address')
      let introduce = that.getContent(rdNoticeItemList, 'introduce')
      let description = that.getContent(rdNoticeItemList, 'description')
      let deadline = that.getContent(rdNoticeItemList, 'deadline')
      let contacter = that.getContent(rdNoticeItemList, 'contact', 'contacter')
      let telephone = that.getContent(rdNoticeItemList, 'contact', 'telephone')
      let email = that.getContent(rdNoticeItemList, 'contact', 'email')
      let workRestShow = that.getShow(rdNoticeItemList, 'workRest')
      let addressShow = that.getShow(rdNoticeItemList, 'address')
      let descriptionShow = that.getShow(rdNoticeItemList, 'description')
      let introduceShow = that.getShow(rdNoticeItemList, 'introduce')
      let contactShow = that.getShow(rdNoticeItemList, 'contact')
      let noticeInfo = {
        workRest: beginTime + ' - ' + endTime,
        address: address,
        introduce: introduce,
        description: description,
        deadline: deadline,
        contacter: contacter,
        telephone: telephone,
        email: email,
        workRestShow: workRestShow && beginTime,
        addressShow: addressShow && address,
        introduceShow: introduceShow && introduce,
        descriptionShow: descriptionShow && description,
        contacterShow: contactShow && contacter,
        telephoneShow: contactShow && telephone,
        emailShow: contactShow && email
      }
      that.noticeInfo = noticeInfo
      that.rdNoticeItemList = rdNoticeItemList
      that.rdNoticePostList = rdNoticePostList
      that.loading = false
    }).catch(err => {
      console.log(err)
    })
  },
  methods: {
    onClickLeft () {
      this.$router.go(-1)
    },
    seeDetail (item) {
      let recruit = this.$store.getters.getRecruit
      recruit.postId = item.id
      recruit.postName = item.postName
      this.$store.dispatch('setRecruit', recruit)
      this.$router.push({
        path: `/wxShareDetail/${item.id}`
      })
    },
    accept (item) {
      let recruit = this.$store.getters.getRecruit
      recruit.postId = item.id
      recruit.postName = item.postName
      this.$store.dispatch('setRecruit', recruit)

      let wxrecord = localStorage.getItem('wxrecord')

      if (wxrecord) {
        let wx = JSON.parse(wxrecord)
        console.log('recruit', recruit)
        let hasInput = wx.data.findIndex(item => item.pid === recruit.postId && item.nid === recruit.noticeId)
        if (hasInput !== -1) {
          // 启事和岗位和之前录入的相同，进入简历提交记录页面
          this.$router.push({path: `/resumeRecord`})
        } else {
          this.$router.push({name: 'home'})
        }
      } else {
        this.$router.push({name: 'home'})
      }
    },
    getContent (data, code, nextCode) {
      if (code === 'custom') {
        let customData = []
        data.forEach(item => {
          if (item.itemCode === 'custom' && item.show && item.content) {
            customData.push({
              itemName: item.itemName,
              content: item.content
            })
          }
        })
        return customData
      } else {
        let index = data.findIndex(it => it.itemCode === code)
        if (index !== -1) {
          if (nextCode) {
            if (data[index].content) {
              let content = JSON.parse(data[index].content)
              return content[nextCode]
            } else {
              return ''
            }
          } else {
            return data[index].content
          }
        } else {
          return ''
        }
      }
    },
    getShow (data, code) {
      let index = data.findIndex(it => it.itemCode === code)
      if (index !== -1) {
        return data[index].show
      } else {
        return false
      }
    }
  }
}
</script>
