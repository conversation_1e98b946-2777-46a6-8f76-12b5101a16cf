package cn.sphd.miners.modules.material.service.impl;

import cn.sphd.miners.modules.material.service.MtPurchaseApplyService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by Administrator on 2017/8/2.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class MtPurchaseApplyServiceImpl implements MtPurchaseApplyService {
}
