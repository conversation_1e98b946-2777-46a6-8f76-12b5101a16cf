package cn.sphd.miners.modules.material.entity;

import java.util.Date;

/**
 * 供应商管理_供应商导入日志
20231108 1.267供应商之批量导入对象 t_srm_supplier_import_log
 * 
 * <AUTHOR>
 * @date 2023-12-11
 */
public class TSrmSupplierImportLog
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 机构ID */
    private Long org;

    /** 导入总数 */
    private Long supplierCount;

    /** 导入成功数 */
    private Long supplierSuccess;

    /** 上传文件路径 */
    private String uploadFile;

    /** 状态:1-上传成功,2-导入临时表,3-完成 */
    private Long state;

    /** 备注 */
    private String memo;

    /** 创建人id */
    private Long creator;

    /** 创建人 */
    private String createName;

    /** 修改人id */
    private Long updator;

    /** 修改人 */
    private String updateName;

    /** 操作:1-增,2-删,3-修改 */
    private String operation;

    /** 修改前记录ID */
    private Long previousId;

    /** 版本号,每次修改+1 */
    private Long versionNo;

    private Date createTime;

    private Date updateTime;


    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOrg(Long org) 
    {
        this.org = org;
    }

    public Long getOrg() 
    {
        return org;
    }
    public void setSupplierCount(Long supplierCount) 
    {
        this.supplierCount = supplierCount;
    }

    public Long getSupplierCount() 
    {
        return supplierCount;
    }
    public void setSupplierSuccess(Long supplierSuccess) 
    {
        this.supplierSuccess = supplierSuccess;
    }

    public Long getSupplierSuccess() 
    {
        return supplierSuccess;
    }
    public void setUploadFile(String uploadFile) 
    {
        this.uploadFile = uploadFile;
    }

    public String getUploadFile() 
    {
        return uploadFile;
    }
    public void setState(Long state) 
    {
        this.state = state;
    }

    public Long getState() 
    {
        return state;
    }
    public void setMemo(String memo) 
    {
        this.memo = memo;
    }

    public String getMemo() 
    {
        return memo;
    }
    public void setCreator(Long creator) 
    {
        this.creator = creator;
    }

    public Long getCreator() 
    {
        return creator;
    }
    public void setCreateName(String createName) 
    {
        this.createName = createName;
    }

    public String getCreateName() 
    {
        return createName;
    }
    public void setUpdator(Long updator) 
    {
        this.updator = updator;
    }

    public Long getUpdator() 
    {
        return updator;
    }
    public void setUpdateName(String updateName) 
    {
        this.updateName = updateName;
    }

    public String getUpdateName() 
    {
        return updateName;
    }
    public void setOperation(String operation) 
    {
        this.operation = operation;
    }

    public String getOperation() 
    {
        return operation;
    }
    public void setPreviousId(Long previousId) 
    {
        this.previousId = previousId;
    }

    public Long getPreviousId() 
    {
        return previousId;
    }
    public void setVersionNo(Long versionNo) 
    {
        this.versionNo = versionNo;
    }

    public Long getVersionNo() 
    {
        return versionNo;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
