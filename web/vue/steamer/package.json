{"name": "steamer", "version": "1.0.0", "private": true, "scripts": {"serve": "vite --host 0.0.0.0 --port 443", "dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@rushstack/eslint-patch": "^1.3.3", "@vitejs/plugin-vue": "^4.3.4", "@vue/eslint-config-prettier": "^8.0.0", "axios": "^1.5.0", "element-plus": "^2.4.4", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "js-base64": "^3.7.5", "js-cookie": "^3.0.5", "json-bigint": "^1.0.0", "prettier": "^3.0.3", "qs": "^6.11.2", "sass": "^1.67.0", "vite": "^4.4.9"}}