.ty-container{
    display: flex;
}
.section_left{
    min-width: 270px;
    flex: none;
}
.section_left nav{
    width: 100%;
    background-color: #f2f2f2;
    min-height: 600px;
}
.section_right{
    margin-left: 20px;
    flex: auto;
}
.nowFolder{overflow: hidden;margin-bottom: 70px; display: flex}
.headPanel{ margin-right:30px; color: #5d9ded; min-width:250px; flex: none}
.headPanel h3 {display: block;  height:35px; line-height: 35px; font-size: 18px;  font-weight: 500;  margin: 0; color: #5d9ded;}
.headPanel .operableBtn { line-height: 35px; border-top:2px solid #5d9ded;}
.table_folderInfo{flex: auto}
.def-table{width: 100%; max-width: 100%; margin-bottom: 20px;}
.def-table thead tr td{ color:#777;border-bottom: 1px solid #ddd;}
.def-table tr td{ padding: 8px; line-height: 1.42857;  vertical-align: top;text-align: center; color: #333; }
.ty-colFileTree{  float: left;  min-width: 270px; max-width: 500px; color: #333;}
.ty-colFileTree .ty-treeItem{  height: 30px;  line-height:30px;  vertical-align: middle;  padding:0 5px;  cursor: default;  }
.ty-colFileTree .ty-treeItemActive{  background-color: #5d9ded; color:#fff   }
.ty-colFileTree .ty-treeItem>i{  display: inline-block; vertical-align: top;  color: #666;  margin: 8px 0 0 3px;  width: 15px;  height: 15px;  text-align: center;  }
.ty-colFileTree .ty-treeItem>i.fa-folder,.ty-colFileTree .ty-treeItem>i.fa-file{  color: #ddc667; margin-right: 5px }
.ty-colFileTree .ty-treeItem>i.fa-trash{ color: #666; margin-right: 5px}
.ty-colFileTree .ty-treeItemActive>i.fa{  color: #fff;}

/*.ty-colFileTree .level2,.ty-colFileTree .level3,.ty-colFileTree .level4,.ty-colFileTree .level5{   }*/
.ty-colFileTree li>ul{  margin-left:20px; }
.operableBtn .fa{
    margin-right: 26px;
    cursor: pointer;
    font-size: 15px;
}

header{
    margin-bottom: 8px;
}
.section_body{
    display: flex;
}
.section_left{
    min-width: 270px;
    flex: none;
}
.section_left nav{
    width: 100%;
    background-color: #f2f2f2;
    min-height: 600px;
    padding: 16px;
}
.section_right{
    background-color: #f9f9f9;
    width: 1000px;
    padding: 16px;
    flex: none;
}
.ty-fileHandle{
    flex: auto;
    text-align: right;
    position: relative;
}
.page{
    width: 1270px;
}
.ty-pre{
    white-space: normal;
    border: 1px solid #eee;
}
.ty-fileNull{
    height: 128px;
    padding-top: 120px;
    text-align: center;
    min-height: 593px;
    color: #aaa;
    font-size: 14px;
}
.ty-fileNull img{
    width: 96px;
}
#editDataContent .item-title{
    width: 150px;
}
#seeDataContent .item-title{
    width: 150px;
}
#editDataContent .item-content input{
    width: 100%;
}
.item_see{
    width: 100%;
    border: 1px solid #dcdfe6;
    border-radius: 2px;
    background-color: #fafafa;
    display: inline-block;
    height: 30px;
    line-height: 30px;
    box-sizing: border-box;
    color: #606266;
    font-size: inherit;
    padding: 0 8px;
}