<!--子路由 按时间查看-->
<template>
  <ul class="val_list">
    <li v-for="(item, index) in accountList" :key="index" class="ta-list">
      <div @click="goDay(item)">
        <span>{{ item.name }}</span>
        <div class="current-balance-1">
          <div class="ty-color-green">
            <p>收入</p>
            <p>{{ item.income }}</p>
          </div>
        </div>
        <div class="current-balance-1 ty-color-red">
          <div>
            <p>支出</p>
            <p>{{ item.expend }}</p>
          </div>
        </div>
        <span>
          <el-icon><arrow-right /></el-icon>
        </span>
      </div>

    </li>
  </ul>
</template>

<script>

import finance from '@/mixins/finance.js'
export default {
  data() {
    return {
      accountList: []
    }
  },
  mixins: [finance],
  mounted() {
    this.getAccountList()
  },
  methods: {
    getAccountList(){
      let org = this.auth.getOrg()
      let beginDate = localStorage.getItem('searchYearDur_monthBegin')
      let endDate = localStorage.getItem('searchYearDur_monthEnd')
      if(beginDate && endDate){
        this.todayTime = beginDate + ' ~ ' + endDate
        let params = { oid:org.id , state:5 , beginDate:beginDate, endDate:endDate }
        this.financeApi.getAllAccountDataByTimeApp(params).then(res1=>{
          let res = res1.data.data
          console.log('getAccountMonthOrDayApp res=', res)
          this.accountList = []
          let accountPeriodList = res.accountPeriodList || []
          accountPeriodList.forEach(item=>{
            let iihk = {
              name: (new Date(item.beginDate).format('yyyy-MM')) ,
              income: this.formatCurrency(item.credit) ,
              expend: this.formatCurrency(item.debit) ,
              ...item
            }
            this.accountList.push(iihk)
          })

        }).catch(err=>{
          console.log('err=', err)
          this.$message.error('链接失败，请重试！')
        })

      }else{
        this.$message.error('没有具体日期哦！')
      }

    },
    goDay(item){
      if(item.name){
        let param = {
          beginDate:new Date(item.beginDate).format('yyyy-MM-dd'),
          endDate: new Date(item.endDate).format('yyyy-MM-dd') ,
        }
        localStorage.setItem('searchDayList', JSON.stringify(param) )

        let path = `/searchDayList/null`
        this.$router.push({ path:path })
      }else{
        this.$message.error('没有具体日期哦！')
      }
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../../style/account.scss";
@import "../../../style/common.scss";

</style>