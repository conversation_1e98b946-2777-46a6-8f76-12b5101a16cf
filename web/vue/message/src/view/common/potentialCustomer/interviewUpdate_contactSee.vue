<template>
  <div id="potentialContactView" class="potential" v-show="view">
    <TY_NavTop title="潜在客户申请" :isReBack="true" @reback="closeShow"></TY_NavTop>
    <div class="container">
      <div class="panel-content">
        <el-row>
          <el-col :span="6">{{details.name}}</el-col>
          <el-col :span="18">{{details.post}}</el-col>
        </el-row>
        <el-row>
          <el-col :span="8">联系人标签</el-col>
          <el-col :span="16">潜在客户期间录入</el-col>
        </el-row>
        <el-row>
          <el-col :span="24" class="createDetail">创建者：{{details.createName}}  {{details.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</el-col>
        </el-row>
      </div>
      <div class="gapR"></div>
      <div class="panel-content">
        <div class="contactList">
          <el-row v-if="details.mobile !== ''">
            <el-col class="icon-mobile" :span="24">{{details.mobile}}</el-col>
          </el-row>
          <el-row v-for="(social, index) in details.socialList.mobile" v-bind:key="index">
            <el-col :span="24" :class="details.mobile === '' && index === 0?'icon-mobile':''">{{social.code}}</el-col>
          </el-row>
          <el-row v-for="(social, index) in details.socialList.qq" v-bind:key="index">
            <el-col :span="24" :class="index === 0?'icon-qq':''">{{social.code}}</el-col>
          </el-row>
          <el-row v-for="(social, index) in details.socialList.email" v-bind:key="index">
            <el-col :span="24" :class="index === 0?'icon-email':''">{{social.code}}</el-col>
          </el-row>
          <el-row v-for="(social, index) in details.socialList.weixin" v-bind:key="index">
            <el-col :span="24" :class="index === 0?'icon-weixin':''">{{social.code}}</el-col>
          </el-row>
          <el-row v-for="(social, index) in details.socialList.weibo" v-bind:key="index">
            <el-col :span="24" :class="index === 0?'icon-weibo':''">{{social.code}}</el-col>
          </el-row>
          <el-row v-for="(social, index) in details.socialList.defined" v-bind:key="index">
            <el-col :span="24" v-if="index === 0" class="icon-defined">自定义  {{social.name}}   {{social.code}}</el-col>
            <el-col :span="24" v-else>{{social.name}}   {{social.code}}</el-col>
          </el-row>
        </div>
      </div>
      <div class="cus_visitCard" v-if="details.visitCard !== null && details.visitCard !== ''">
        <el-image
          style="width: 300px; height: 200px"
          :src="fileUrl + details.visitCard"
          fit="contain">
          <div slot="error" class="image-slot">
            <img :src="fileUrl + details.visitCard" />
          </div>
        </el-image>
      </div>
    </div>
  </div>
</template>
<style lang="less">
  #potentialContactView {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1100;
    width: 100%;
    height: 100%;
    background: #fff;
  }
</style>
<script>
import { formatDate } from '../../../js/common'
export default {
  name: 'interviewContact',
  props: {
    details: Object,
    view: Boolean
  },
  filters: {
    formatDate
  },
  data () {
    return {
      fileUrl: window.parent.$.fileUrl
    }
  },
  methods: {
    oprationFile: function (event) {
      let el = event.currentTarget
      window.parent.previewOrDownload(el, this.browser)
    },
    closeShow: function () {
      this.$emit('update:view', false)
    }
  }
}
</script>
