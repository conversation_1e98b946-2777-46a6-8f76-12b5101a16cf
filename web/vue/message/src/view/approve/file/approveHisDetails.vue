<template>
  <div id="finalTreateFile">
    <TY_NavTop title="文件发布/换版/废止审批" :isBackHome="isBackHome"></TY_NavTop>
    <div class="container" v-loading="loading">
      <div class="place" v-if="isAbolishFile">
        <div class="placeContainer">
          <p>这是一条<span class="color-red">文件废止的申请</span>，请谨慎操作！</p>
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          <div class="record">
            <div class="record_title">审批记录</div>
            <div class="script">
              <div v-for="(item ,index) in approveRecord" :key="index">
                <div v-if="index === 0">
                  <span class="sm-ttl">申请人：</span>
                  <span class="sm-con" v-text="item.userName"></span>
                  <span>{{item.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                </div>
                <div v-if="index != 0">
                  <span class="sm-ttl">审批人：</span>
                  <span class="sm-con" v-text="item.userName"></span>
                  <span>{{item.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="handle_button">
            <button class="ui-btn ui-btn_error" :disdabled="usNot" @click="approveConfirm(3)">驳回</button>
            <button class="ui-btn ui-btn_info" :disdabled="usNot" @click="approveConfirm(2)">批准</button>
          </div>
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          <div class="line-p">
            <div class="tt-cell">保存位置</div>
            <div class="con-cell" v-html="filePosition"></div>
          </div>
          <div class="line-p">
            <div class="tt-cell">文件名称</div>
            <div class="con-cell" v-text="fileDetails.details.name" :title="fileDetails.details.name"></div>
          </div>
          <div class="line-p">
            <div class="tt-cell">文件编号</div>
            <div class="con-cell" v-text="fileDetails.details.fileSn"></div>
          </div>
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          <div class="line-btn-groups" v-if="isAbolishFile">
            <div class="line-p">
              <div class="tt-cell">将废止的文件</div>
              <div class="con-cell">
                <span class="fc-btn-see" @click="showDialog(2)">查看</span>
              </div>
            </div>
          </div>
          <div class="line-btn-groups" v-else>
            <div class="line-p">
              <div class="tt-cell">待审批的文件</div>
              <div class="con-cell">
                <span class="fc-btn-see" @click="showDialog(0)">查看</span>
              </div>
            </div>
            <div class="line-p" v-if="fileDetails.details.changeNum">
              <div class="tt-cell">当前版本</div>
              <div class="con-cell">
                <span class="fc-btn-see" @click="showDialog(1)">查看</span>
              </div>
            </div>
            <div class="line-p" v-if="fileDetails.details.changeNum">
              <div class="tt-cell">历史版本</div>
              <div class="con-cell">
                <span class="fc-btn-see">查看</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="pendingDialog"
      width="70%">
      <el-form class="preview">
        <el-form-item>
          <el-radio-group v-model="browser">
            <el-radio :disabled="seeOnlineDisabled" :label="1">在线预览</el-radio>
            <el-radio :label="2">下载</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button handle-center">
          <a class="fc-btn-normal" @click="pendingDialog = false">取消</a>
          <span class="fc-btn ui-btn_info" ref="operate" :path="fileHandleDetail.path" :download="fileHandleDetail.name + '.' + fileHandleDetail.version" @click="oprationFile($event)">确定</span>
        </div>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { formatDate, canSeeOnline } from '@/js/common'
export default {
  name: 'approveHisDetails',
  filters: {
    formatDate
  },
  data () {
    return {
      isBackHome: true,
      loading: true,
      labelPosition: 'top',
      pendingDialog: false,
      browser: 1,
      fileDetails: {
        categoryName: '',
        details: {},
        filePath: '',
        fileVersion: ''
      },
      approveRecord: [],
      lastApprover: {},
      opposeForm: {
        reason: ''
      },
      listenersUid: [],
      usNot: false,
      fileHandleDetail: {},
      dialogTitle: '',
      seeOnlineDisabled: true,
      titleName: '', // 确定标题到底时哪一项（发布、换版、废止）
      isAbolishFile: false // 是否是废止的文件详情（默认不是）
    }
  },
  computed: {
    filePosition () {
      let pos = this.fileDetails.categoryName
      let posArr = pos.split('/')
      let posStr = ''
      for (let a = 0; a < posArr.length; a++) {
        if (a === posArr.length - 1) {
          posStr += '<span class="font-orange">' + posArr[a] + '</span>'
        } else {
          posStr += posArr[a] + '/'
        }
      }
      return posStr
    }
  },
  created: function () {
    let _this = this
    let fileId = this.$route.params.fId
    this.sphdSocket.send('onePublishFileMessage', { 'session': this.sphdSocket.sessionid, 'hisId': fileId, 'userID': this.sphdSocket.user.userID })
    this.listenersUid = [
      this.sphdSocket.subscribe('onePublishFileMessage', function (data) {
        let fileData = JSON.parse(data)
        console.log('文件发布/换版/废止审批-文件详情', fileData)
        // 提取数据
        _this.fileDetails.categoryName = fileData.categoryName
        _this.fileDetails.details = fileData.resHis
        // 赋值
        if (fileData.resHis.approveStatus !== '1') {
          // 已经被审批，不展示按钮
          _this.usNot = true
        }
        let teminateState = fileData.resHis.teminateState
        let filePath = ''
        if (teminateState === '1') {
          filePath = fileData.resHis.path
          _this.titleName = '废止'
        } else {
          if (fileData.resHis.changeNum > 0) {
            filePath = fileData.validFilePath
            _this.titleName = '换版'
          } else {
            _this.titleName = '发布'
          }
        }

        let pathArr = filePath.split('.')
        let version = pathArr[pathArr.length - 1]
        _this.fileDetails.filePath = filePath
        _this.fileDetails.fileVersion = version
        _this.isAbolishFile = teminateState === '1'

        if (fileData.listAp.length > 0) {
          _this.approveRecord = fileData.listAp
          let len = fileData.listAp.length - 1
          _this.lastApprover = {
            'processId': fileData.listAp[len].id,
            'level': fileData.listAp[len].level
          }
        }
        _this.loading = false
      }, null, 'session', fileId),
      this.sphdSocket.subscribe('fileUpdateState', function (data) {
        let val = JSON.parse(data)
        setTimeout(function () {
          if (val === 2) {
            // 审批流程多级
            let tips = '申请者已终止该文件的' + this.titleName
            _this.$alert(tips, '！提示', {
              confirmButtonText: '确定'
            });
            _this.usNot = true
          } else {
            _this.$message({
              type: 'success',
              message: '操作成功！'
            })
            _this.refreshFile()
          }
        }, 3000)
      }, null, 'custom', fileId)
    ]
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    approveConfirm (option) {
      let that = this
      if (option === 3) {
        this.$prompt('驳回理由', '！提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(({ value }) => {
          that.approveIssueFile (3, value)
        }).catch(() => {});
      } else {
        if (this.approveRecord.length > 1) {
          // 审批流程多级
          let tips = '您确定批准本次文件' + this.titleName + '申请吗？'
          this.$confirm(tips, '！提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            that.approveIssueFile(2)
          }).catch(() => {})
        } else {
          // 直接提给文管
          const h = this.$createElement;
          this.$msgbox({
            title: '！提示',
            message: h('div', null, [
              h('span', null, '本次文件'+this.titleName+'申请'),
              h('span', { style: 'color: red' }, '未经任何人审批。'),
              h('p', null, '您确定批准吗?')
            ]),
            showCancelButton: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
          }).then(() => {
            that.approveIssueFile(2)
          }).catch(() => {});
        }
      }
    },
    approveIssueFile (option, reason) { // option:2-同意 3-驳回
      this.loading = true
      let fileData = {
        'userID': this.sphdSocket.user.userID,
        'hisId': this.$route.params.fId,
        'processId': this.lastApprover.processId,
        'level': this.lastApprover.level,
        'approveStatus': option,
        'reason': '',
        'module': '文件与资料',
        'type': '3' // 1代表继续选择下级审批人，2代表选择下级审批人是总务后小总务，3代表进行最终审批
      }
      if (option === 3) {
        fileData.reason = reason
      }
      this.sphdSocket.send('handleFile', fileData)
    },
    opposeIssueFile () {
      this.opposeMsg = true
    },
    oprationFile: function (event) {
      let el = event.currentTarget
      window.parent.previewOrDownload(el, this.browser)
    },
    showDialog: function (type) {
      let fileHandleDetail = {}
      if (type === 0) {
        this.dialogTitle = '待审批的文件'
        fileHandleDetail = {
          path: this.fileDetails.details.path,
          name: this.fileDetails.details.name,
          version: this.fileDetails.details.version
        }
      } else if (type === 1) {
        this.dialogTitle = '当前的有效文件'
        fileHandleDetail = {
          path: this.fileDetails.filePath,
          name: this.fileDetails.details.name,
          version: this.fileDetails.fileVersion
        }
      } else if (type === 2) {
        this.dialogTitle = '将废止的文件'
        fileHandleDetail = {
          path: this.fileDetails.details.path,
          name: this.fileDetails.details.name,
          version: this.fileDetails.details.version
        }
      }
      this.seeOnlineDisabled = !canSeeOnline(fileHandleDetail.version)
      this.fileHandleDetail = fileHandleDetail
      this.pendingDialog = true
      if (this.seeOnlineDisabled) {
        this.browser = 2
      } else {
        this.browser = 1
      }
    },
    refreshFile: function () {
      this.$router.push({
        path: '/versionApproval'
      })
    }
  }
}
</script>
