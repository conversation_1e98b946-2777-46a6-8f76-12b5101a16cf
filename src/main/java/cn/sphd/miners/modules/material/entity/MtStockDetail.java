package cn.sphd.miners.modules.material.entity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

@Entity(name="MtStockDetail")
@Table(name="t_mt_stock_detail")
public class MtStockDetail {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;//	id	int	Y		Y		ID
    @Column(name="org"   , nullable=true , unique=false)
    private Integer org;//	org	int			Y		机构ID
    @Column(name="material"   , nullable=true , unique=false)
    private Integer material;//	material	int			Y		物料ID
    @Column(name="supplier"   , nullable=true , unique=false)
    private Integer supplier;//	supplier	int			Y		供应商ID
    @Column(name="event"   , nullable=true , unique=false)
    private String event;//	event	varchar(50)			Y		事件:in-入库,out-出库,new_initial-录入初始库存,modify_initial-修改初始库存,inventory-盘点, wastage-损耗
    @Column(name="event_time"   , nullable=true , unique=false)
    private  Date eventTime;//	datetime			Y		事发时间
    @Column(name="quantity"   , nullable=true , unique=false)
    private BigDecimal	quantity;//	decimal(20,10)			Y	默认值:0    发生数量:正数表示增,负数表示减
    @Column(name="end_stock"   , nullable=true , unique=false)
    private BigDecimal endStock;//	decimal(20,10)			Y		事后库存
    @Column(name="business"   , nullable=true , unique=false)
    private Integer business;//	business	int			Y		对应的业务ID
    @Column(name="memo"   , nullable=true , unique=false)
    private String memo;//	varchar(255)			Y		备注
    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;//	creator	int			Y		创建人id
    @Column(name="create_name"   , nullable=true , unique=false)
    private String createName;//	create_name	varchar(100)			Y		创建人
    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;//	create_date;//	datetime			Y		创建时间
    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;//	updator	int			Y		修改人id
    @Column(name="update_name"   , nullable=true , unique=false)
    private  String updateName;//	varchar(100)			Y		修改人
    @Column(name="update_date"   , nullable=true , unique=false)
    private  Date updateDate;//	datetime			Y		修改时间
    @Column(name="operation"   , nullable=true , unique=false)
    private String operation;//	operation	char(1)			Y		操作:1-增,2-删,3-改
    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId;//	previous_id	int			Y		修改前记录ID
    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo;//	version_no	int			Y		版本号,每次修改+1


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getMaterial() {
        return material;
    }

    public void setMaterial(Integer material) {
        this.material = material;
    }

    public Integer getSupplier() {
        return supplier;
    }

    public void setSupplier(Integer supplier) {
        this.supplier = supplier;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public Date getEventTime() {
        return eventTime;
    }

    public void setEventTime(Date eventTime) {
        this.eventTime = eventTime;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getEndStock() {
        return endStock;
    }

    public void setEndStock(BigDecimal endStock) {
        this.endStock = endStock;
    }

    public Integer getBusiness() {
        return business;
    }

    public void setBusiness(Integer business) {
        this.business = business;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
}
