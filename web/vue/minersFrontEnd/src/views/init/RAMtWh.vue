<template>
  <div class="RAMtWh">
    <TyDialog v-if="dialog_visible_editInitialInventory" width="900" dialogTitle="编辑初始库存" color="blue" :dialogHide="dialogHide">

      <template #dialogBody>
        <div class="mainCon" style="width: 800px; margin: 0 auto">
          <el-table :data="[form_editInitialInventory.mtInfo]" border style="width: 100%; margin-top: 16px">
            <el-table-column label="材料名称" prop="name"></el-table-column>
            <el-table-column label="材料代号" prop="code"></el-table-column>
            <el-table-column label="型号" prop="model"></el-table-column>
            <el-table-column label="规格" prop="specifications"></el-table-column>
            <el-table-column label="计量单位" prop="unit"></el-table-column>
            <el-table-column label="材料的创建" width="250">
              <template #default="scope">
                {{scope.row.create_name	}} {{$filter.format(scope.row.create_date	)}}
              </template>
            </el-table-column>
          </el-table>
          <el-row :gutter="20" style="margin-top: 16px; align-items: center">
            <el-col :span="16">
              是否区分供应商 <span class="color-red">*</span>
            </el-col>
            <el-col :span="8">
              <el-radio-group v-model="radios.isDistinguishSupplier">
                <el-radio :value="1" size="large">区分</el-radio>
                <el-radio :value="2" size="large">不区分</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="align-items: center">
            <el-col :span="16">
              是否区分包装 <span class="color-red">*</span>
            </el-col>
            <el-col :span="8">
              <el-radio-group v-model="radios.isDistinguishPacking">
                <el-radio :value="1" size="large">区分</el-radio>
                <el-radio :value="2" size="large">不区分</el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <el-row style="margin-top: 16px" v-show="radios.isDistinguishSupplier === 1">
            已录入总计：{{compCount}} {{form_editInitialInventory.mtInfo.unit}}
          </el-row>
          <div class="part_11" v-show="radios.isDistinguishSupplier === 1 && radios.isDistinguishPacking === 1">
            <div v-for="(item, index) in form_editInitialInventory.edit_supplier_pack" :key="index">
              <div>
                <div class="ty-hr"></div>
              </div>
              <div v-if="item.id">
                <div class="row">
                  <b>{{item.full_name}}</b>
                </div>
                <div class="row" style="margin-top: 8px">
                  <el-text>代号 {{item.code_name}} 简称 {{item.name}}</el-text>
                </div>
              </div>
              <div v-if="!item.id">
                <div class="row">
                  <b>难以区分供应商的本材料</b>
                </div>
              </div>
              <el-table
                  :data="item.packagingInfos"
                  border
                  show-summary
                  :summary-method="getSummaries"
                  style="width: 100%; margin-top: 16px"
              >
                <el-table-column label="最外层使用的主要包装物">
                  <template #default="scope">
                    {{scope.row.packaging?scope.row.packaging:'没有包装'}}
                  </template>
                </el-table-column>
                <el-table-column label="单个包装内材料的数量">
                  <template #default="scope">
                    {{scope.row.packaging?scope.row.packaging:''}}
                  </template>
                </el-table-column>
                <el-table-column label="包装的个数" width="250">
                  <template #default="scope">
                    <ElInputPlus v-model="scope.row.packagingCount"
                              style="width: 180px"
                              :placeholder="scope.row.packaging?'请录入':'此处需录入为材料数量'"
                    ></ElInputPlus>
                  </template>
                </el-table-column>
                <el-table-column label="材料数量小计">
                  <template #default="scope">
                    {{edit_supplier_pack_smallCount(scope.row)}}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="part_12" v-show="radios.isDistinguishSupplier === 1 && radios.isDistinguishPacking === 2">
            <div v-for="(item, index) in form_editInitialInventory.edit_supplier" :key="index">
              <div>
                <div class="ty-hr"></div>
              </div>
              <div class="row" v-if="item.id">
                <b>{{item.full_name}}</b>
              </div>
              <div class="row">
                <span v-if="item.id">代号 {{item.code_name}} 简称 {{item.name}}</span>
                <span v-if="!item.id"><b>难以区分供应商的本材料</b></span>
                <div class="rightBtn">
                  初始库存 <ElInputPlus v-model="item.amount" style="width: 200px; margin-left: 36px" placeholder="请录入"><template v-slot:append>{{form_editInitialInventory.mtInfo.unit}}</template></ElInputPlus>
                </div>
              </div>
            </div>
          </div>
          <div class="part_21" v-show="radios.isDistinguishSupplier === 2 && radios.isDistinguishPacking === 1">
            <el-table
                :data="form_editInitialInventory.edit_pack"
                border
                show-summary
                :summary-method="getSummaries"
                style="width: 100%; margin-top: 16px"
            >
              <el-table-column label="最外层使用的主要包装物">
                <template #default="scope">
                  {{scope.row.packaging?scope.row.packaging:'没有包装'}}
                </template>
              </el-table-column>
              <el-table-column label="单个包装内材料的数量">
                <template #default="scope">
                  {{scope.row.packaging?scope.row.packaging:''}}
                </template>
              </el-table-column>
              <el-table-column label="包装的个数" width="250">
                <template #default="scope">
                  <ElInputPlus v-model="scope.row.packagingCount"
                            style="width: 180px"
                            :placeholder="scope.row.packaging?'请录入':'此处需录入为材料数量'"
                  ></ElInputPlus>
                </template>
              </el-table-column>
              <el-table-column label="材料数量小计">
                <template #default="scope">
                  {{edit_pack_smallCount(scope.row)}}
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="part_22" v-show="radios.isDistinguishSupplier === 2 && radios.isDistinguishPacking === 2">
            <el-row :gutter="20" style="margin-top: 16px; align-items: center">
              <el-col :span="16">
                初始库存 <span class="color-red">*</span>
              </el-col>
              <el-col :span="8">
                <ElInputPlus v-model="form_editInitialInventory.edit_none.amount"
                             :placeNum="8"
                          style="width: 180px"
                          placeholder="请录入"
                ><template #append>{{form_editInitialInventory.mtInfo.unit}}</template></ElInputPlus>
              </el-col>
            </el-row>
          </div>
        </div>
      </template>
      <template #dialogFooter>
        <el-button @click="dialog_visible_editInitialInventory = false">取消</el-button>
        <el-button type="primary" @click="mt_waitEdit_edit_submit">确定</el-button>
      </template>
    </TyDialog>
    <div class="ty-container" id="home">
      <div class="page">
        <div v-show="page === 8" style="display: flex; font-size: 18px"><el-icon color="#67c23a"><SuccessFilled /></el-icon><span style="margin-left: 8px">初始化已完成</span></div>
        <el-page-header @back="goBack" v-show="page > 0" style="margin-bottom: 16px">
          <template #content>
            <el-text size="large" @click="page = 0; part = 0"> 主页 </el-text>
          </template>
        </el-page-header>
        <section v-show="page === -1">
          <el-alert
              type="warning"
              show-icon
              :closable="false"
          >
            <p>上步初始化尚未完成，故本模块初始化暂无法开始！</p>
            <p style="margin-top: 4px">上步初始化：{{initData.name}}</p>
            <p style="margin-top: 4px">负责人：{{initData.userName}}</p>
          </el-alert>
        </section>
        <section v-show="page === -2">
          <el-alert
              type="warning"
              show-icon
              :closable="false"
          >
            <p>当前是智能库模式，需要到手机端操作</p>
          </el-alert>
        </section>
        <section v-show="page === 0">
          <div class="pagePart" v-show="part === 0">
            <div class="row head">
              如必要，可对初始库存编辑页的默认型式进行设置
              <el-button type="primary" @click="toSet" style="margin-left: 16px">去设置</el-button>
              <div class="rightBtn">
                <el-radio value="1" size="large" v-model="radios.stepOk">原辅材料的初始库存已全部编辑完成，初始化结束！</el-radio>
                <el-button type="primary" @click="allComplete">确定</el-button>
              </div>
            </div>
            <div class="ty-hr"></div>
            <div class="row">
              初始库存有待编辑的原辅材料共如下 {{mt_waitEdit.list?mt_waitEdit.list.length:0}} 条，请给予编辑！
              <div class="rightBtn">
                初始库存已编辑完成的原辅材料共 {{mt_waitEdit.inited}} 条
                <el-button type="primary" @click="edited_see" style="margin-left: 32px">查看</el-button>
              </div>
            </div>
            <el-table :data="mt_waitEdit.list" border style="width: 100%; margin-top: 16px">
              <el-table-column label="材料名称" prop="name"></el-table-column>
              <el-table-column label="材料代号" prop="code"></el-table-column>
              <el-table-column label="型号" prop="model"></el-table-column>
              <el-table-column label="规格" prop="specifications"></el-table-column>
              <el-table-column label="计量单位" prop="unit"></el-table-column>
              <el-table-column label="创建" width="250">
                <template #default="scope">
                  {{scope.row.create_name	}} {{$filter.format(scope.row.create_date	)}}
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template #default="scope">
                  <el-button type="primary" link @click="mt_waitEdit_edit(scope.row)"><b>编辑</b></el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="pagePart" v-show="part === 1">
              “采购”模块的初始化已完成，完成时间：<b class="purchaseCompleteTime"></b>。
          </div>
        </section>
        <section v-show="page === 1">
          <div class="row head">
            点击初始库存各条数据“编辑”按钮后所见页的型式，请在本页面进行设置
            <div class="rightBtn">
              <el-button type="primary" @click="updateSetType">确定</el-button>
            </div>
          </div>
          <el-radio-group v-model="form_editSetType">
            <el-table :data="setTypeList" border style="width: 100%; margin-top: 16px">
              <el-table-column prop="name" width="100" align="center">
                <template #default="scope">
                  <el-radio :value="scope.row.id"></el-radio>
                </template>
              </el-table-column>
              <el-table-column label="型式简称" prop="key" width="100"></el-table-column>
              <el-table-column label="内容" prop="memo" width="500"></el-table-column>
              <el-table-column label="操作">
                <template #default="scope">
                  <el-button type="primary" link disabled><b>查看</b></el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-radio-group>
        </section>
        <section v-show="page === 2">
          初始库存已确定的原辅材料共如下{{mt_edited.list?.length}}条
          <el-table :data="mt_edited.list" border style="width: 100%; margin-top: 16px">w
            <el-table-column label="材料名称" prop="name"></el-table-column>
            <el-table-column label="材料代号" prop="code"></el-table-column>
            <el-table-column label="型号" prop="model"></el-table-column>
            <el-table-column label="规格" prop="specifications"></el-table-column>
            <el-table-column label="计量单位" prop="unit"></el-table-column>
            <el-table-column label="初始库存" prop="initial_stock"></el-table-column>
            <el-table-column label="最新一次的编辑" width="250">
              <template #default="scope">
                {{scope.row.si_update_name	}} {{$filter.format(scope.row.si_update_date)}}
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <el-button type="primary" link @click="mt_edited_edit(scope.row)"><b>修改</b></el-button>
              </template>
            </el-table-column>
          </el-table>
        </section>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.ty-container{
  margin-top:35px; padding:10px;
}
.RAMtWh{
  font-size: 14px;
}
.color-red {
  color: #ff0000;
}
.ty-page-header{
  display: flex;
  line-height: 24px;
  padding: 0 0 0 70px;
  color: #5d9cec;
  margin-top: 16px;
  .page-header__left{
    display: flex;
    cursor: pointer;
    margin-right: 40px;
    position: relative;
    &::after {
      content: "";
      position: absolute;
      width: 1px;
      height: 16px;
      right: -20px;
      top: 50%;
      transform: translateY(-50%);
      background-color: #dcdfe6;
    }
    .icon-back {
      font-size: 18px;
      margin-right: 6px;
      align-self: center;
      position: relative;
      top: 1px;
    }
  }
}
.page{
  padding: 8px 0;
  max-width: 1200px;
  position: relative;
  margin: 0 70px;
  .panel-box{
    border-top: 1px solid #ddd;
    padding-top: 8px;
    &:first-child{
      border: none
    }
  }
}
.ty-hr{
  margin: 16px 0;
  width: 100%;
  height: 1px;
  background: #eee;
}
.effect_avatar{
  background: rgb(64, 158, 255);
  padding: 24px 64px;
  border-radius: 3px;
  .effect_title{
    color: #fff;
  }
  .effect_main_avatar{
    .effect_main{
      background: #fff;
    }
  }
}
.row{
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .rightBtn{
    flex: auto;
    text-align: right;
  }
  &.head{
    margin-bottom: 16px;
  }
}
:deep(.el-form--label-top) {
  .el-form-item__label{
    padding: 0;
  }
}
.text-center{
  text-align: center;
}
.des{
  color: #333;
}


.section_title{
  margin: 8px 0;
}
.section_title span.title{
  font-size: 14px;
  color: #666;
  font-weight: bold;
  padding: 0 8px;
  line-height: 1;
  border-left: 4px solid #bcdaff;
  display: inline-block;
  width: 200px;
}
.section_content {
  padding-left: 32px;
}
:deep(.el-upload--picture-card){
  width: 64px;
  height: 64px;
  &>i{
    font-size: 16px;
  }
}
:deep(.el-upload-list--picture-card .el-upload-list__item){
  width: 64px;
  height: 64px;
}
</style>
<script>
import {beforeRouteLeave, initNav} from "@/utils/routeChange"
import auth from '@/sys/auth'
import * as api from "@/api/init.js"
import ElInputPlus from "@/components/ELInputNum.vue"
import operaMixin from '@/mixins/mainOperating.js'
export default {
  mixins: [operaMixin],
  data() {
    return {
      loading: false,
      pageName: 'init_RAMtWh',
      mode: 0,
      pageShow: false,
      initData: {},
      page: 0,
      part: 0,
      stepAllId: 0,
      setTypeList: [],
      selectSetType: {},
      form_editSetType: '',
      mt_waitEdit: {
        inited: 0,
        list: [],
        pageInfo: {}
      },
      mt_edited: {
        list: [],
        pageInfo: {}
      },
      form_editInitialInventory: {
        oldState: '',
        mtInfo: {},
        suppliers: [],
        list: [],
        edit_mts: [],
        edit_supplier_pack: [],
        edit_supplier: [],
        edit_pack: [{packaging: 0}],
        edit_none: {
          amount: 0
        }
      },
      dialog_visible_editInitialInventory: false,
      radios: {
        isDistinguishSupplier: '',
        isDistinguishPacking: ''
      }
    }
  },
  components: {
    ElInputPlus
  },
  computed: {
    compCount: function () {
      let isDistinguishSupplier = this.radios.isDistinguishSupplier
      let isDistinguishPacking = this.radios.isDistinguishPacking
      let count = 0
      if (isDistinguishSupplier === 1 && isDistinguishPacking === 1) {
        let edit_supplier_pack = this.form_editInitialInventory.edit_supplier_pack
        edit_supplier_pack.forEach(function (item, index) {
          let packingInfos = item.packagingInfos
          packingInfos.forEach(function (packingInfo) {
            count += Number(packingInfo.amount || 0)
          })
        })
      } else if (isDistinguishSupplier === 1 && isDistinguishPacking === 2) {
        let edit_supplier = this.form_editInitialInventory.edit_supplier
        edit_supplier.forEach(function (item, index) {
          count += Number(item.amount || 0)
        })
      } else if (isDistinguishSupplier === 2 && isDistinguishPacking === 1) {
        let edit_pack = this.form_editInitialInventory.edit_pack
        edit_pack.forEach(function (item, index) {
          count += Number(item.amount || 0)
        })
      } else if (isDistinguishSupplier === 2 && isDistinguishPacking === 2) {
        count = this.form_editInitialInventory.edit_none.amount || 0
      }

      return count
    }
  },
  beforeRouteLeave(to, from, next) {
    beforeRouteLeave(to, from, next, this)
  },
  mounted() {
    initNav({mid: 'vf', name: '原辅材料库', pageName: this.pageName}, this.created, this)
  },
  methods: {
    created () {
      api.getStockMode()
      .then(res => {
        let mode = res.data.status //0-非智能 1-智能
        this.mode = mode
        if (mode === 1) {
          this.page = -2
        } else {
          api.getStepList()
              .then(res => {
                let data = res.data.data

                if (data[1]?.id) {
                  this.stepAllId = data[1].id
                }
                console.log(this.stepAllId)
              })
          api.mustAllocation()
              .then(res => {
                let data = res.data.data
                let purchaseData = data[0]
                let RAMtWhData = data[1]
                if (purchaseData.state === 1) {
                  this.page = 0
                } else {
                  this.page = -1
                  this.initData = purchaseData
                }
              })
          this.getWaitingForLocationList()

          api.getCurrentSetType()
              .then(res => {
                if (!res.data) {
                  this.$message({
                    type: 'error',
                    message: '未获取到默认型式！'
                  })
                } else {
                  this.selectSetType = res.data
                }
              })
        }
      })
    },
    getWaitingForLocationList(option = {}) {
      const oid  = auth.getOrg().id
      let state = 3
      if (this.page === 2) {
        state = 5
      }
      let defaultOption = {
        oid: oid,
        state: state,
        pageNum: 1,
        per: 20
      }
      let params = {...defaultOption, ...option}
      api.getWaitingForLocationList(params)
        .then(res => {
          let data = res.data
          if (this.page === 2) {
            this.mt_edited.list = data.data || []
            this.mt_edited.pageInfo = {
              per: data.pageNum,
              pageNum: data.pageNum
            }
          } else {
            this.mt_waitEdit.list = data.data
            this.mt_waitEdit.inited = data.inited
            this.mt_waitEdit.pageInfo = {
              per: data.pageNum,
              pageNum: data.pageNum
            }
          }

        })
    },
    mt_waitEdit_edit(item) {
      this.dialog_visible_editInitialInventory = true
      let key = this.selectSetType.key
      this.radios.isDistinguishSupplier = Number(key.charAt(0))
      this.radios.isDistinguishPacking = Number(key.charAt(1))
      this.form_editInitialInventory.mtInfo = item
      this.form_editInitialInventory.oldState = null
      api.getSuppliers(item.id)
      .then(res => {
        let list = res.data.data || []
        this.form_editInitialInventory.origin_suppliers = list

        // 现阶段初始化没有包装，所以不知道有什么数据，先不做包装部分数据处理

        let edit_supplier_pack = [...list, ...[{id: 0, packagingInfos: []}]].map(it => {
          return {
            ...it,
            packagingInfos: [...it.packagingInfos, ...[{packaging: 0}]].map(ite => {
              return {...ite, ...{
                  material: it.id,
                  supplierMaterial: it.material_supplier_id || 0,
                  packagingCount: '',
                  amount: ''
                }}
            })
          }
        })
        this.form_editInitialInventory.edit_supplier_pack = edit_supplier_pack

        console.log('form_editInitialInventory.edit_supplier_pack ', edit_supplier_pack)

        // 区分供应商 不区分包装
        let edit_supplier = [...list, ...[{}]].map(it => {
          return {...it, ...{
              material: it.id,
              supplierMaterial: it.material_supplier_id || 0,
              amount: ''
            }}
        })
        this.form_editInitialInventory.edit_supplier = edit_supplier

        // 区分包装 不区分供应商
        let edit_pack = [{
          supplierMaterial: 0,
          packaging: 0,
          packagingCount: '',
          amount: ''
        }]
        this.form_editInitialInventory.edit_pack = edit_pack

        this.form_editInitialInventory.edit_none = {
          amount: 0
        }
      })
    },
    edit_supplier_pack_smallCount(item) {
      item.amount =  item.packagingCount
      return item.packagingCount
    },
    edit_pack_smallCount(item) {
      item.amount =  item.packagingCount
      return item.packagingCount
    },
    edited_see() {
      this.page = 2
      this.getWaitingForLocationList()
    },
    mt_edited_edit(item) {
      api.currentStock(item.id)
      .then(res => {
        let data = res.data.data
        let state = data.state
        const storageData = data.map
        this.radios.isDistinguishSupplier = Number(state.charAt(0))
        this.radios.isDistinguishPacking = Number(state.charAt(1))
        this.dialog_visible_editInitialInventory = true
        this.form_editInitialInventory.mtInfo = item
        this.form_editInitialInventory.oldState = state

        api.getSuppliers(item.id)
            .then(res => {
              let list = res.data.data || []
              this.form_editInitialInventory.origin_suppliers = list

              // 现阶段初始化没有包装，所以不知道有什么数据，先不做包装部分数据处理

              let edit_supplier_pack = [...list, ...[{id: 0, packagingInfos: []}]].map(it => {
                return {
                  ...it,
                  packagingInfos: [...it.packagingInfos, ...[{packaging: 0}]].map(ite => {
                    let findData;
                    if (state === '11') {
                      findData = storageData.find(its => its.supplier_material === it.material_supplier_id && its.packaging === 0)
                    }
                    return {...ite, ...{
                        material: it.id,
                        supplierMaterial: it.material_supplier_id || 0,
                        packagingCount: findData?findData.amount: 0,
                        amount: '',
                        id: findData?findData.id: ''
                      }}
                  })
                }
              })
              console.log('edit_supplier_pack' , edit_supplier_pack)
              this.form_editInitialInventory.edit_supplier_pack = edit_supplier_pack

              // 区分供应商 不区分包装
              let edit_supplier = [...list, ...[{}]].map(it => {
                let findData;
                if (state === '12') {
                  findData = storageData.find(its => its.supplier_material === it.material_supplier_id)
                }
                return {...it, ...{
                    material: it.id,
                    supplierMaterial: it.material_supplier_id || 0,
                    amount: findData?findData.amount: 0
                  }}
              })
              this.form_editInitialInventory.edit_supplier = edit_supplier

              // 区分包装 不区分供应商
              let findData;
              if (state === '21') {
                findData = storageData.find(its => its.packaging === 0)
              }
              let edit_pack = [{
                packaging: 0,
                supplierMaterial: 0,
                packagingCount: findData?findData.amount: 0,
                amount: '',
                id: findData?findData.id: ''
              }]
              this.form_editInitialInventory.edit_pack = edit_pack

              if (state === '22') {
                findData = data.currentStock
              }
              this.form_editInitialInventory.edit_none = {
                amount: findData || 0
              }
            })
      })
    },
    mt_waitEdit_edit_submit() {
      let mtId =  this.form_editInitialInventory.mtInfo.id
      let isDistinguishSupplier = this.radios.isDistinguishSupplier
      let isDistinguishPacking = this.radios.isDistinguishPacking
      let material = this.form_editInitialInventory.mtInfo.id
      let mts = []
      if (!isDistinguishSupplier) {
        this.$message({
          type: 'error',
          message: '请选择是否区分供应商！'
        })
      }
      if(!isDistinguishPacking) {
        this.$message({
          type: 'error',
          message: '请选择是否区分包装！'
        })
      }
      let state = `${isDistinguishSupplier}${isDistinguishPacking}`
      let oldState = this.form_editInitialInventory.oldState
      let paramData = {}

      if (state === '11') {
        const edit_supplier_pack = this.form_editInitialInventory.edit_supplier_pack
        for (let item of edit_supplier_pack) {
          let packagingInfos = item.packagingInfos
          for (let packItem of packagingInfos) {
            mts.push({
              material: mtId,
              supplierMaterial: packItem.supplierMaterial,
              packaging: packItem.packaging,
              packagingCount: packItem.packagingCount || 0,
              amount: packItem.amount || 0,
              id: packItem.id || ''
            })
          }
        }
        console.log('mts', mts)
        paramData = {json: JSON.stringify(mts)}
        if (oldState && state !== oldState) {
          paramData.previous = oldState
        }
        api.initialStock11(paramData)
        .then(res => {
            this.msg(res)
            if (res.data.code === 0) {
              this.dialog_visible_editInitialInventory = false
              this.getWaitingForLocationList()
            }
          })
      } else if (isDistinguishSupplier === 1 && isDistinguishPacking === 2) {
        const edit_supplier = this.form_editInitialInventory.edit_supplier
        for (let item of edit_supplier) {
          mts.push({
            material: mtId,
            supplierMaterial: item.supplierMaterial,
            amount: item.amount || 0
          })
        }
        paramData = {json: JSON.stringify(mts)}
        if (state !== oldState) {
          paramData.previous = oldState
        }
        api.initialStock12(paramData)
            .then(res => {
              this.msg(res)
              if (res.data.code === 0) {
                this.dialog_visible_editInitialInventory = false
                this.getWaitingForLocationList()
              }
            })
      } else if (isDistinguishSupplier === 2 && isDistinguishPacking === 1) {
        const edit_pack = this.form_editInitialInventory.edit_pack
        for (let item of edit_pack) {
          mts.push({
            material: mtId,
            supplierMaterial: item.supplierMaterial,
            packaging: item.packaging,
            packagingCount: item.packagingCount || 0,
            amount: item.amount || 0,
            id: item.id || ''
          })
        }
        paramData = {json: JSON.stringify(mts)}
        if (state !== oldState) {
          paramData.previous = oldState
        }
        api.initialStock21(paramData)
            .then(res => {
              this.msg(res)
              if (res.data.code === 0) {
                this.dialog_visible_editInitialInventory = false
                this.getWaitingForLocationList()
              }
            })
      } else if (isDistinguishSupplier === 2 && isDistinguishPacking === 2) {
        const initialStock = this.form_editInitialInventory.edit_none.amount || 0
        paramData = {
          material: mtId,
          initialStock: initialStock
        }
        if (state !== oldState) {
          paramData.previous = oldState
        }
        api.initialStock22(paramData)
            .then(res => {
              this.msg(res)
              if (res.data.code === 0) {
                this.dialog_visible_editInitialInventory = false
                this.getWaitingForLocationList()
              }
            })
      }
    },
    getSummaries(param) {
      let isDistinguishSupplier = this.radios.isDistinguishSupplier
      let isDistinguishPacking = this.radios.isDistinguishPacking
      const { columns, data } = param;
      console.log('dataaaaaaa' , data)
      const sums = [];

      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
        } else if (index === 3){
          const values = data.map(item => Number(item.packagingCount));
          if (!values.every(value => isNaN(value))) {
            sums[index] = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
          } else {
            sums[index] = 'N/A';
          }
        }
      });
      return sums
    },
    dialogHide() {
      this.dialog_visible_editInitialInventory = false
    },
    toSet() {
      this.page = 1
      api.getSetTypeList()
          .then(res => {
            this.setTypeList = res.data
            this.form_editSetType = res.data.id
            api.getCurrentSetType()
                .then(res => {
                  this.selectSetType = res.data
                  this.form_editSetType = res.data.id
                })
          })
    },
    updateSetType() {
      api.updateSetType(this.form_editSetType)
      .then(res => {
        this.msg(res)
        if (res.data.code === 0) {
          api.getCurrentSetType()
              .then(res => {
                this.selectSetType = res.data
                this.form_editSetType = res.data.id
              })
        }
      })
    },
    allComplete() {
      if (this.mt_waitEdit.list) {
        this.$message({
          type: 'error',
          message: '还有材料尚未录入初始库存！'
        })
      } else {
        api.allComplete(this.stepAllId)
        .then(res => {
            let data = res.data.data
            if (data === 1) {
              this.$message({
                type: 'success',
                message: '操作成功！'
              })
              setTimeout(()=>{
                this.logout()
              }, 2000)
            }
        })
      }
    },
    goBack() {
      this.page = 0
    },
    msg(res) {
      let code = res.data.code
      let msg = res.data.msg
      let type = ''
      switch (code) {
        case 0:
          type = 'success'
          break
        case 301:
          type = 'warning'
          break
        case 500:
          type = 'error'
          break
      }
      this.$message({
        type: type,
        message: msg,
        plain: true
      })
    }
  }
}


</script>
