<template>
  <div class="cateagent" v-show="catgory === 0">
    <div>
      <div class="main rolor">
        <el-row>
          <el-col :span="8"><div class="grid-content bg-purple plence">
            装备器具现有如下<span>{{ listc1.length }}</span>个一级类别。您可“新增一级类别”，也可管理各直属子类别。
            <br />
            此外如必要，还可“停用”或“删除”某类别，或修改各类别的基本信息。
          </div> </el-col>
          <el-col :span="8"><div class="grid-content bg-purple-light"></div> </el-col>
          <el-col :span="8"><div class="grid-content bg-purple-light">
            <el-row>
              <el-col :span="8"><div class="grid-content bg-purple catadd">
                <el-button type="text" @click="stopcatfi()">被停用的类别</el-button>
              </div></el-col>
              <el-col :span="8"><div class="grid-content bg-purple-light catadd">
                <el-button type="text" @click="openonecat()">新增一级类别</el-button>
              </div></el-col>
              <el-col :span="8"><div class="grid-content bg-purple catadd">
                <el-button type="text" @click="morecatlook()">更多操作说明</el-button>
              </div></el-col>
            </el-row>
          </div> </el-col>
        </el-row>
        <div class="equipcatdata upolence">
          <table class="ty-table ty-table-control">
            <thead>
            <tr>
              <td>类别名称</td>
              <td>所含内容/使用的主要场合</td>
              <td>直属的子类别</td>
              <td>操作</td>
            </tr>
            </thead>
            <tbody>
            <tr v-for="(item,index) in tableData1" :key="index">
              <td>{{ item.name }}</td>
              <td>{{ item.content }}</td>
              <td class="ty-td-control">
                <span class="ty-color-blue" @click="lookchilden(item,1)">{{ item.childrens }}个</span>
              </td>
              <td class="ty-td-control">
                <span class="ty-color-blue diffenten" @click="upbscination(item)">修改基本信息</span>
                <span class="ty-color-red diffentenb" @click="stopchose(item)">停用</span>
                <span class="ty-color-red diffentenb" @click="dettlen(item,1)">删除</span>
              </td>
            </tr>
            </tbody>
          </table>
          <!--          <el-table :data="tableData1" border class="ty-table-control" style="width: 100%;">-->
          <!--            <el-table-column fixed prop="name" label="类别名称" width="230" align="center"></el-table-column>-->
          <!--            <el-table-column prop="content" label="所含内容/使用的主要场合" width="" align="center"></el-table-column>-->
          <!--            <el-table-column prop="childrens" label="直属的子类别" width="131" align="center">-->
          <!--              <template #default="scope">-->
          <!--                <div class="ty-td-control">-->
          <!--                  <span class="ty-color-blue" @click="lookchilden(scope.row,1)">{{ scope.row.childrens }}个</span>-->
          <!--                </div>-->
          <!--&lt;!&ndash;                <el-button class="impont-hover diffenten" type="typcolor" @click="lookchilden(scope.row,1)">{{ scope.row.childrens }}个</el-button>&ndash;&gt;-->
          <!--              </template>-->
          <!--            </el-table-column>-->
          <!--            <el-table-column label="操作" width="260" align="center">-->
          <!--              <template #default="scope">-->
          <!--                <div class="ty-td-control">-->
          <!--                  <span class="ty-color-blue diffenten" @click="upbscination(scope.row)">修改基本信息</span>-->
          <!--                  <span class="ty-color-red diffentenb" @click="stopchose(scope.row)">停用</span>-->
          <!--                  <span class="ty-color-red diffentenb" @click="dettlen(scope.row,1)">删除</span>-->
          <!--                </div>-->
          <!--&lt;!&ndash;                <el-button class="impont-hover diffenten" @click="upbscination(scope.row)" type="typcolor" size="small">修改基本信息</el-button>&ndash;&gt;-->
          <!--&lt;!&ndash;                <el-button class="impont-hover2 diffentenb" @click="stopchose(scope.row)" type="typcolor" size="small">停用</el-button>&ndash;&gt;-->
          <!--&lt;!&ndash;                <el-button class="impont-hover2 diffentenb" @click="dettlen(scope.row,1)" type="typcolor" size="small">删除</el-button>&ndash;&gt;-->
          <!--              </template>-->
          <!--            </el-table-column>-->
          <!--          </el-table>-->
        </div>
      </div>
      <!--    一级弹窗层-->
      <TyDialog  v-if="delVisiblenc1" width="500" dialogTitle="新增一级类别" color="blue" :dialogHide="hideFunc1">
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="hideFunc1">取消</el-button>
          <el-button class="bounce-ok" :plain="true" @click="tipOkc1()" type="primary">确定</el-button>
        </template>
        <template #dialogBody>
          <div>
            <el-row class="plencen">
              <el-col :span="24"><div class="grid-content bg-purple-dark">
                <span>一级类别名称</span>
              </div> </el-col>
            </el-row>
            <el-input
                type="text"
                v-model="text1"
                maxlength="10"
                show-word-limit
                ref="unitern1"
            ></el-input>
            <el-row class="plencen">
              <el-col :span="24"><div class="grid-content bg-purple-dark">
                <span>所含内容/使用的主要场合</span>
              </div> </el-col>
            </el-row>
            <el-input
                type="text"
                v-model="text2"
                maxlength="30"
                show-word-limit
                ref="unitern2">
            </el-input>
          </div>
        </template>
      </TyDialog>
      <TyDialog v-if="delVisiblenc2" width="600" dialogTitle="更多操作说明" color="blue" :dialogHide="hideFunc2">
        <template #dialogFooter>
          <el-button class="bounce-ok" @click="hideFunc2">关闭</el-button>
        </template>
        <template #dialogBody>
          <div>
            <el-row>
              <el-col :span="1"><div class="grid-content bg-purple">1</div></el-col>
              <el-col :span="1"><div class="grid-content bg-purple-light"></div></el-col>
              <el-col :span="22"><div class="grid-content bg-purple">
                <span>系统自带9各装备器具的一级类别</span>
                <br />
                <span>如需要，可创建各类别的同级类别或子类别，或修改各类别的基本信息。</span>
              </div></el-col>
              <el-col :span="0"><div class="grid-content bg-purple-light"></div></el-col>
            </el-row>
            <br />
            <el-row>
              <el-col :span="1"><div class="grid-content bg-purple">2</div></el-col>
              <el-col :span="1"><div class="grid-content bg-purple-light"></div> </el-col>
              <el-col :span="22"><div class="grid-content bg-purple">
                <span>贵公司如已启用“工序管理”功能，一下内容需继续阅读，否则请忽略。</span>
              </div> </el-col>
              <el-col :span="0"><div class="grid-content bg-purple-light"></div> </el-col>
            </el-row>
            <br />
            <el-row>
              <el-col :span="1"><div class="grid-content bg-purple">3</div> </el-col>
              <el-col :span="1"><div class="grid-content bg-purple-light"></div> </el-col>
              <el-col :span="22"><div class="grid-content bg-purple">
                <span>“工序管理”单元下</span>
              </div> </el-col>
              <el-col :span="0"><div class="grid-content bg-purple-light"></div> </el-col>
            </el-row>
            <br />
            <el-row>
              <el-col :span="1"><div class="grid-content bg-purple">3.1</div> </el-col>
              <el-col :span="1"><div class="grid-content bg-purple-light"></div> </el-col>
              <el-col :span="22"><div class="grid-content bg-purple">
                <span>含有“制造用的机器/设备”“制造用的夹具、模具”与“检测用的设备/手段”三</span>
                <br />
                <span>个栏目。这三个栏目均需选择选项；</span>
              </div> </el-col>
              <el-col :span="0"><div class="grid-content bg-purple-light"></div> </el-col>
            </el-row>
            <br />
            <el-row>
              <el-col :span="1"><div class="grid-content bg-purple">3.2</div> </el-col>
              <el-col :span="1"><div class="grid-content bg-purple-light"></div> </el-col>
              <el-col :span="22"><div class="grid-content bg-purple">
                <span>“制造用的机器/设备”的选项，默认为一类装备中的各装备器具；</span>
              </div> </el-col>
              <el-col :span="0"><div class="grid-content bg-purple-light"></div> </el-col>
            </el-row>
            <br />
            <el-row>
              <el-col :span="1"><div class="grid-content bg-purple">3.3</div> </el-col>
              <el-col :span="1"><div class="grid-content bg-purple-light"></div> </el-col>
              <el-col :span="22"><div class="grid-content bg-purple">
                <span>“制造用的夹具、模具”的选项，默认为二类装备中的各装备器具；</span>
              </div> </el-col>
              <el-col :span="0"><div class="grid-content bg-purple-light"></div> </el-col>
            </el-row>
            <br />
            <el-row>
              <el-col :span="1"><div class="grid-content bg-purple">3.4</div> </el-col>
              <el-col :span="1"><div class="grid-content bg-purple-light"></div> </el-col>
              <el-col :span="22"><div class="grid-content bg-purple">
                <span>“检测用的设备/手段”的选项，默认为三类装备与四类装备中的各装备器具；</span>
              </div> </el-col>
              <el-col :span="0"><div class="grid-content bg-purple-light"></div> </el-col>
            </el-row>
            <br />
            <el-row>
              <el-col :span="1"><div class="grid-content bg-purple">3.5</div> </el-col>
              <el-col :span="1"><div class="grid-content bg-purple-light"></div> </el-col>
              <el-col :span="22"><div class="grid-content bg-purple">
                <span>如需要，可修改上述选项的范围，但需在“工序管理”中操作。</span>
              </div> </el-col>
              <el-col :span="0"><div class="grid-content bg-purple-light"></div> </el-col>
            </el-row>
          </div>
        </template>
      </TyDialog>
      <TyDialog v-if="delVisiblenc3" width="600" dialogTitle="修改类别的基本信息" color="blue" :dialogHide="hideFunc3">
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="hideFunc3">取消</el-button>
          <el-button class="bounce-ok parent" @click="uponemadsun(1)" type="primary" v-show="updent === 1">确定</el-button>
          <el-button class="bounce-ok child" @click="" type="primary" v-show="updent === 2">确定</el-button>
        </template>
        <template #dialogBody>
          <div>
            <el-row class="plencen">
              <el-col :span="8"><div class="grid-content bg-purple modev">
                <span>当前类别名称</span>
              </div> </el-col>
              <el-col :span="8"><div class="grid-content bg-purple-light"></div> </el-col>
              <el-col :span="8"><div class="grid-content bg-purple">
                <el-row>
                  <el-col :span="8"><div class="grid-content bg-purple"></div></el-col>
                  <el-col :span="8"><div class="grid-content bg-purple-light"></div></el-col>
                  <el-col :span="8"><div class="grid-content bg-purple">
                    <el-button type="text" @click="upcatlank(1)">修改记录</el-button>
                  </div></el-col>
                </el-row>
              </div></el-col>
            </el-row>
            <el-input v-model="input1" :disabled="true" class="diffencolor"> </el-input>
            <el-row class="plencen">
              <el-col :span="24"><div class="grid-content bg-purple-dark">
                <span>当前所含内容/使用的主要场合</span>
              </div></el-col>
            </el-row>
            <el-input v-model="input2" :disabled="true"></el-input>
            <el-row class="plencen">
              <el-col :span="24"><div class="grid-content bg-purple-dark">
                <span>新的类别名称</span>
              </div> </el-col>
            </el-row>
            <el-input type="text" placeholder="请输入内容" v-model="text3" maxlength="10" show-word-limit></el-input>
            <el-row class="plencen">
              <el-col :span="24"><div class="grid-content bg-purple-dark">
                <span>新的所含内容/使用的主要场合</span>
              </div> </el-col>
            </el-row>
            <el-input type="text" placeholder="请输入内容" v-model="text4" maxlength="30" show-word-limit></el-input>
            <el-row class="plend1">
              <el-col :span="24"><div class="grid-content bg-purple-dark">
                <span class="colorn">注：修改哪项填写哪项，不修改的无需理会！</span>
              </div></el-col>
            </el-row>
          </div>
        </template>
      </TyDialog>
      <TyDialog v-if="delVisiblenc5" width="500" dialogTitle="!提示" color="red" :dialogHide="hideFunc5">
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="hideFunc5">取消</el-button>
          <el-button class="bounce-ok parent" @click="stopcat" type="primary" v-show="stoptent === 1">确定</el-button>
          <el-button class="bounce-ok child" @click="stopcat2" type="primary" v-show="stoptent === 2">确定</el-button>
          <el-button class="bounce-ok detparent" @click="detsure(1)" type="primary" v-show="detpont === 1">确定</el-button>
          <el-button class="bounce-ok detchild" @click="detsure2(1)" type="primary" v-show="detpont === 2">确定</el-button>
        </template>
        <template #dialogBody>
          <div class="only">
            <el-row>
              <el-col :span="8"><div class="grid-content bg-purple"></div></el-col>
              <el-col :span="8"><div class="grid-content bg-purple-light">
                <span v-show="kept === 1">确定停用该类别吗？</span>
                <span v-show="kept === 2">确定删除该类别吗？</span>
              </div></el-col>
              <el-col :span="8"><div class="grid-content bg-purple"></div></el-col>
            </el-row>
          </div>
        </template>
      </TyDialog>
      <!--    二级弹窗层-->
      <TyDialog v-if="delVisiblenc4" width="1000" dialogTitle="修改记录" color="blue" :dialogHide="hideFunc4">
        <template #dialogFooter>
          <el-button class="bounce-ok" @click="hideFunc4">关闭</el-button>
        </template>
        <template #dialogBody>
          <div>
            <el-table :data="tableData4" border>
              <el-table-column fixed prop="msgmade" label="事件" width="140" align="center"></el-table-column>
              <el-table-column label="操作" width="230" align="center">
                <template #default="scope">
                  {{scope.row.createName}}&nbsp;{{scope.row.createDate}}
                </template>
              </el-table-column>
              <el-table-column prop="name" label="操作后的类别名称" width="175" align="center"></el-table-column>
              <el-table-column prop="content" label="操作后的所含内容/使用的主要场合" width="425" align="center"></el-table-column>
            </el-table>
          </div>
        </template>
      </TyDialog>
    </div>
  </div>
  <div class="stopcatoney" v-show="catgory === 1">
    <div class="plencew">
      <el-row>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="gotunback2">返回</span>
      </el-row>
      <el-row class="upolence">
        <el-col :span="24"><div class="grid-content bg-purple-dark">以下为被停用的类别：</div></el-col>
      </el-row>
      <div class="upolence">
        <table class="ty-table ty-table-control">
          <thead>
          <tr>
            <td>类别名称</td>
            <td>所含内容/使用的主要场合</td>
            <td>停用时间</td>
            <td>操作</td>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item2,index2) in tableData2" :key="index2">
            <td>{{ item2.name }}</td>
            <td>{{ item2.content }}</td>
            <td>{{ item2.createName }}&nbsp;{{ item2.createDate }}</td>
            <td class="ty-td-control">
              <span class="ty-color-blue diffenten" @click="restoreuse(item2)">恢复使用</span>
              <span class="ty-color-red diffentenb" @click="dettlen(item2,2)">删除</span>
            </td>
          </tr>
          </tbody>
        </table>
        <!--        <el-table :data="tableData2" border class="ty-table-control" style="width: 100%;">-->
        <!--          <el-table-column fixed prop="name" label="类别名称" width="230" align="center"></el-table-column>-->
        <!--          <el-table-column prop="content" label="所含内容/使用的主要场合" width="" align="center"></el-table-column>-->
        <!--          <el-table-column label="停用时间" width="220" align="center">-->
        <!--            <template #default="scope">-->
        <!--&lt;!&ndash;              {{ scope.row.updateName }}&nbsp;{{ scope.row.enabledTime }}&ndash;&gt;-->
        <!--              {{ scope.row.createName }}&nbsp;{{ scope.row.createDate }}-->
        <!--            </template>-->
        <!--          </el-table-column>-->
        <!--          <el-table-column label="操作" width="180" align="center">-->
        <!--            <template #default="scope">-->
        <!--              <div class="ty-td-control">-->
        <!--                <span class="ty-color-blue diffenten" @click="restoreuse(scope.row)">恢复使用</span>-->
        <!--                <span class="ty-color-red diffentenb" @click="dettlen(scope.row,2)">删除</span>-->
        <!--              </div>-->
        <!--&lt;!&ndash;              <el-button class="impont-hover diffenten" @click="restoreuse(scope.row)" type="typcolor" size="small">恢复使用</el-button>&ndash;&gt;-->
        <!--&lt;!&ndash;              <el-button class="impont-hover2 diffentenb" @click="dettlen(scope.row,2)" type="typcolor" size="small">删除</el-button>&ndash;&gt;-->
        <!--            </template>-->
        <!--          </el-table-column>-->
        <!--        </el-table>-->
      </div>
    </div>
    <!-- 一级弹窗层 -->
    <TyDialog v-if="delVisiblenc6" width="500" dialogTitle="!提示" color="red" :dialogHide="hideFunc6">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFunc6">取消</el-button>
        <el-button class="bounce-ok parent" @click="restoredat()" type="primary" v-show="prestrt === 1">确定</el-button>
        <el-button class="bounce-ok child" @click="" type="primary" v-show="prestrt === 2">确定</el-button>
        <el-button class="bounce-ok detpar" @click="detsure(1)" type="primary" v-show="detprt == 1">确定</el-button>
        <el-button class="bounce-ok detchild" @click="" type="primary" v-show="detprt == 2">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="only">
          <el-row>
            <el-col :span="7"><div class="grid-content bg-purple"></div> </el-col>
            <el-col :span="10"><div class="grid-content bg-purple-light">
              <span v-show="hfpt === 1">确定恢复使用该类别吗？</span>
              <span v-show="hfpt === 2" style="margin-left: 32px;">确定删除该类别吗？</span>
            </div> </el-col>
            <el-col :span="7"><div class="grid-content bg-purple"></div> </el-col>
          </el-row>
        </div>
      </template>
    </TyDialog>
  </div>
  <div class="diretsbaeory" v-show="catgory === 2">
    <div class="plencew">
      <el-row>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5"
              @click="gotunback(1)">返回</span>
      </el-row>
      <el-row class="upolence">
        <el-col :span="10"><div class="grid-content bg-purple upolence2" v-for="(itemd,indexd) in listc2" :key="indexd">
          直属于<span>{{ itemd.catname }}</span>的子类别现有<span>{{ itemd.longer }}</span>个。您可“新增同级子类别”，也可管理各直属的子类别。
          <br />
          此外如必要，还可“停用”或“删除”某类别，或修改各类别的基本信息。
        </div> </el-col>
        <el-col :span="6"><div class="grid-content bg-purple-light"></div> </el-col>
        <el-col :span="8"><div class="grid-content bg-purple">
          <el-row>
            <el-col :span="8"><div class="grid-content bg-purple"></div> </el-col>
            <el-col :span="8"><div class="grid-content bg-purple-light"></div> </el-col>
            <el-col :span="8"><div class="grid-content bg-purple">
              <el-row>
                <el-col :span="9"><div class="grid-content bg-purple"></div> </el-col>
                <el-col :span="15"><div class="grid-content bg-purple-light">
                  <el-button type="text" @click="addchidcat">新增同级子类别</el-button>
                </div> </el-col>
              </el-row>
            </div> </el-col>
          </el-row>
        </div> </el-col>
      </el-row>
      <div class="tatle upolence">
        <table class="ty-table ty-table-control">
          <thead>
          <tr>
            <td>类别名称</td>
            <td>所含内容/使用的主要场合</td>
            <td>直属的子类别</td>
            <td>操作</td>
          </tr>
          </thead>
          <tbody>
          <tr v-for="(item,index) in tableData3" :key="index">
            <td>{{ item.name }}</td>
            <td>{{ item.content }}</td>
            <td class="ty-td-control">
              <span class="ty-color-blue diffenten" @click="lookchilden(item,2)">{{ item.childrens }}</span>
            </td>
            <td class="ty-td-control">
              <span class="ty-color-blue diffenten" @click="upfoundation(item)">修改基本信息</span>
              <span class="ty-color-red diffentenb" @click="stopuse(item)">停用</span>
              <span class="ty-color-red diffentenb" @click="detaleune(item,1)">删除</span>
            </td>
          </tr>
          </tbody>
        </table>
        <!--        <el-table :data="tableData3" border class="ty-table-control" style="width: 100%;">-->
        <!--          <el-table-column fixed prop="name" label="类别名称" width="230" align="center"></el-table-column>-->
        <!--          <el-table-column prop="content" label="所含内容/使用的主要场合" width="" align="center"></el-table-column>-->
        <!--          <el-table-column prop="childrens" label="直属的子类别" width="131" align="center">-->
        <!--            <template #default="scope">-->
        <!--              <div class="ty-td-control">-->
        <!--                <span class="ty-color-blue diffenten" @click="lookchilden(scope.row,2)">{{ scope.row.childrens }}</span>-->
        <!--              </div>-->
        <!--&lt;!&ndash;              <el-button class="impont-hover diffenten" type="typcolor" @click="lookchilden(scope.row,2)">{{ scope.row.childrens }}</el-button>&ndash;&gt;-->
        <!--            </template>-->
        <!--          </el-table-column>-->
        <!--          <el-table-column fixed="right" label="操作" width="260" align="center">-->
        <!--            <template #default="scope">-->
        <!--              <div class="ty-td-control">-->
        <!--                <span class="ty-color-blue diffenten" @click="upfoundation(scope.row)">修改基本信息</span>-->
        <!--                <span class="ty-color-red diffentenb" @click="stopuse(scope.row)">停用</span>-->
        <!--                <span class="ty-color-red diffentenb" @click="detaleune(scope.row,1)">删除</span>-->
        <!--              </div>-->
        <!--&lt;!&ndash;              <el-button class="impont-hover diffenten" @click="upfoundation(scope.row)" type="typcolor" size="small">修改基本信息</el-button>&ndash;&gt;-->
        <!--&lt;!&ndash;              <el-button class="impont-hover2 diffentenb" @click="stopuse(scope.row)" type="typcolor" size="small">停用</el-button>&ndash;&gt;-->
        <!--&lt;!&ndash;              <el-button class="impont-hover2 diffentenb" @click="detaleune(scope.row,1)" type="typcolor" size="small">删除</el-button>&ndash;&gt;-->
        <!--            </template>-->
        <!--          </el-table-column>-->
        <!--        </el-table>-->
      </div>
    </div>
    <TyDialog v-if="delVisiblenc7" width="500" dialogTitle="新增同级子类别" color="blue" :dialogHide="hideFunc7">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFunc7">取消</el-button>
        <el-button class="bounce-ok" @click="tipOkc2()">确定</el-button>
      </template>
      <template #dialogBody>
        <div>新增<span class="simbcat">{{ catname }}</span>的同级子类别</div>
        <div>
          <el-row class="plencen">
            <el-col :span="24"><div class="grid-content bg-purple-dark">
              <span>子类别名称</span>
            </div> </el-col>
          </el-row>
          <el-input type="text" v-model="text5" maxlength="10" show-word-limit ref="unitern5"></el-input>
          <el-row class="plencen">
            <el-col :span="24"><div class="grid-content bg-purple-dark">
              <span>所含内容/使用的主要场合</span>
            </div> </el-col>
          </el-row>
          <el-input type="text" v-model="text6" maxlength="30" show-word-limit ref="unitern6"></el-input>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="delVisiblenc8" width="600" dialogTitle="修改类别的基本信息" color="blue" :dialogHide="hideFunc8">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFunc8">取消</el-button>
        <!--        <el-button class="bounce-ok parent" @click="uponemadsun" type="primary" v-show="updent === 1">确定</el-button>-->
        <el-button class="bounce-ok child" @click="uponemadsun(2)" type="primary" v-show="updent2 === 2">确定</el-button>
      </template>
      <template #dialogBody>
        <div>
          <el-row class="plencen">
            <el-col :span="8"><div class="grid-content bg-purple modev">
              <span>当前类别名称</span>
            </div> </el-col>
            <el-col :span="8"><div class="grid-content bg-purple-light"></div> </el-col>
            <el-col :span="8"><div class="grid-content bg-purple">
              <el-row>
                <el-col :span="8"><div class="grid-content bg-purple"></div></el-col>
                <el-col :span="8"><div class="grid-content bg-purple-light"></div></el-col>
                <el-col :span="8"><div class="grid-content bg-purple">
                  <el-button type="text" @click="upcatlank(2)">修改记录</el-button>
                </div></el-col>
              </el-row>
            </div></el-col>
          </el-row>
          <el-input v-model="input3" :disabled="true" class="diffencolor"> </el-input>
          <el-row class="plencen">
            <el-col :span="24"><div class="grid-content bg-purple-dark">
              <span>当前所含内容/使用的主要场合</span>
            </div></el-col>
          </el-row>
          <el-input v-model="input4" :disabled="true"></el-input>
          <el-row class="plencen">
            <el-col :span="24"><div class="grid-content bg-purple-dark">
              <span>新的类别名称</span>
            </div> </el-col>
          </el-row>
          <el-input type="text" placeholder="请输入内容" v-model="text7" maxlength="10" show-word-limit></el-input>
          <el-row class="plencen">
            <el-col :span="24"><div class="grid-content bg-purple-dark">
              <span>新的所含内容/使用的主要场合</span>
            </div> </el-col>
          </el-row>
          <el-input type="text" placeholder="请输入内容" v-model="text8" maxlength="30" show-word-limit></el-input>
          <el-row class="plend1">
            <el-col :span="24"><div class="grid-content bg-purple-dark">
              <span class="colorn">注：修改哪项填写哪项，不修改的无需理会！</span>
            </div></el-col>
          </el-row>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="delVisiblenc9" width="500" dialogTitle="!提示" color="red" :dialogHide="hideFunc9">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFunc9">取消</el-button>
        <el-button class="bounce-ok parent" @click="stopcat" type="primary" v-show="stoptent === 1">确定</el-button>
        <el-button class="bounce-ok child" @click="stopcat2" type="primary" v-show="stoptent === 2">确定</el-button>
        <el-button class="bounce-ok detparent" @click="detsure(1)" type="primary" v-show="detpont === 1">确定</el-button>
        <el-button class="bounce-ok detchild" @click="detsure2(1)" type="primary" v-show="detpont === 2">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="only">
          <el-row>
            <el-col :span="8"><div class="grid-content bg-purple"></div></el-col>
            <el-col :span="8"><div class="grid-content bg-purple-light">
              <span v-show="kept === 1">确定停用该类别吗？</span>
              <span v-show="kept === 2">确定删除该类别吗？</span>
            </div></el-col>
            <el-col :span="8"><div class="grid-content bg-purple"></div></el-col>
          </el-row>
        </div>
      </template>
    </TyDialog>
    <!--    二级弹窗层-->
    <TyDialog v-if="delVisiblenc10" width="1000" dialogTitle="修改记录" color="blue" :dialogHide="hideFunc10">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="hideFunc10">关闭</el-button>
      </template>
      <template #dialogBody>
        <div>
          <el-table :data="tableData4" border>
            <el-table-column fixed prop="msgmade" label="事件" width="140" align="center"></el-table-column>
            <el-table-column label="操作" width="230" align="center">
              <template #default="scope">
                {{scope.row.createName}}&nbsp;{{scope.row.createDate}}
              </template>
            </el-table-column>
            <el-table-column prop="name" label="操作后的类别名称" width="175" align="center"></el-table-column>
            <el-table-column prop="content" label="操作后的所含内容/使用的主要场合" width="425" align="center"></el-table-column>
          </el-table>
        </div>
      </template>
    </TyDialog>
  </div>
</template>

<script>
import { defineComponent } from 'vue';
import { beforeRouteLeave,initNav } from "@/utils/routeChange";
import equitmentCategory from "@/mixins/equipManage/equitmentCategory";

export default defineComponent ({
  //components: {TyDialog},
  // components: {TyDialog},
  //name: "categoryManageMent"
  mixins: [equitmentCategory],
  data(){
    return{
      pageName: "categoryManageMent",
    }
  },
  beforeRouteLeave(to, from, next) {
    beforeRouteLeave(to, from, next,this)
  },
  created(){
    initNav({mid: 'qs',name: '类别管理', pageName: this.pageName}, this.initCreateda, this)
  },
  methods: {}
})
</script>

<style lang="scss">
@use "@/style/equiptCommon.scss";

.cateagent{
  div .rolor{
    margin: 57px 96px 0 96px;
  }
}
.stopcatoney{
  .plencew{
    margin: 48px 107px 20px;
  }
}
.diretsbaeory{
  .plencew{
    margin: 48px 107px 20px;
  }
}
.stopcatoney{
  .plencew {
    margin: 48px 107px 20px;
  }
}
.diretsbaeory{
  .plencew{
    margin: 48px 107px 20px;
  }
}
</style>