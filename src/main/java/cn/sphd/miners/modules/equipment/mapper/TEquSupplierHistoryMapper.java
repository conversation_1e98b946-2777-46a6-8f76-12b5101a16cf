package cn.sphd.miners.modules.equipment.mapper;

import cn.sphd.miners.modules.equipment.entity.TEquSupplierHistory;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 装备管理_供应商名录历史
 * 20230209 1.247装备器具新增Mapper接口
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Repository
public interface TEquSupplierHistoryMapper {
    /**
     * 查询装备管理_供应商名录历史
     * 20230209 1.247装备器具新增
     *
     * @param id 装备管理_供应商名录历史
     *           20230209 1.247装备器具新增主键
     * @return 装备管理_供应商名录历史
     * 20230209 1.247装备器具新增
     */
    public TEquSupplierHistory selectTEquSupplierHistoryById(Long id);

    /**
     * 查询装备管理_供应商名录历史
     * 20230209 1.247装备器具新增列表
     *
     * @param tEquSupplierHistory 装备管理_供应商名录历史
     *                            20230209 1.247装备器具新增
     * @return 装备管理_供应商名录历史
     * 20230209 1.247装备器具新增集合
     */
    public List<TEquSupplierHistory> selectTEquSupplierHistoryList(TEquSupplierHistory tEquSupplierHistory);

    /**
     * 新增装备管理_供应商名录历史
     * 20230209 1.247装备器具新增
     *
     * @param tEquSupplierHistory 装备管理_供应商名录历史
     *                            20230209 1.247装备器具新增
     * @return 结果
     */
    public int insertTEquSupplierHistory(TEquSupplierHistory tEquSupplierHistory);

    /**
     * 修改装备管理_供应商名录历史
     * 20230209 1.247装备器具新增
     *
     * @param tEquSupplierHistory 装备管理_供应商名录历史
     *                            20230209 1.247装备器具新增
     * @return 结果
     */
    public int updateTEquSupplierHistory(TEquSupplierHistory tEquSupplierHistory);

    /**
     * 删除装备管理_供应商名录历史
     * 20230209 1.247装备器具新增
     *
     * @param id 装备管理_供应商名录历史
     *           20230209 1.247装备器具新增主键
     * @return 结果
     */
    public int deleteTEquSupplierHistoryById(Long id);

    /**
     * 批量删除装备管理_供应商名录历史
     * 20230209 1.247装备器具新增
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTEquSupplierHistoryByIds(String[] ids);
}
