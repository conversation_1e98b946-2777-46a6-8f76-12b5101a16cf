package cn.sphd.miners.modules.equipment.service.impl;

import java.util.List;

import cn.sphd.miners.modules.equipment.entity.TEquModelImportLog;
import cn.sphd.miners.modules.equipment.mapper.TEquModelImportLogMapper;
import cn.sphd.miners.modules.equipment.service.ITEquModelImportLogService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-03-30
 */
@Service
@Transactional
public class TEquModelImportLogServiceImpl implements ITEquModelImportLogService
{
    @Autowired
    private TEquModelImportLogMapper tEquModelImportLogMapper;

    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    @Override
    public TEquModelImportLog selectTEquModelImportLogById(Long id)
    {
        return tEquModelImportLogMapper.selectTEquModelImportLogById(id);
    }

    /**
     * 查询列表
     * 
     * @param tEquModelImportLog 
     * @return 
     */
    @Override
    public List<TEquModelImportLog> selectTEquModelImportLogList(TEquModelImportLog tEquModelImportLog)
    {
        return tEquModelImportLogMapper.selectTEquModelImportLogList(tEquModelImportLog);
    }

    /**
     * 新增
     * 
     * @param tEquModelImportLog 
     * @return 结果
     */
    @Override
    public int insertTEquModelImportLog(TEquModelImportLog tEquModelImportLog)
    {
        return tEquModelImportLogMapper.insertTEquModelImportLog(tEquModelImportLog);
    }

    /**
     * 修改
     * 
     * @param tEquModelImportLog 
     * @return 结果
     */
    @Override
    public int updateTEquModelImportLog(TEquModelImportLog tEquModelImportLog)
    {
        return tEquModelImportLogMapper.updateTEquModelImportLog(tEquModelImportLog);
    }

    /**
     * 批量删除
     * 
     * @param ids 需要删除的主键
     * @return 结果
     */
    @Override
    public int deleteTEquModelImportLogByIds(String ids)
    {
        return tEquModelImportLogMapper.deleteTEquModelImportLogByIds(ids.split(","));
    }

    /**
     * 删除信息
     * 
     * @param id 主键
     * @return 结果
     */
    @Override
    public int deleteTEquModelImportLogById(Long id)
    {
        return tEquModelImportLogMapper.deleteTEquModelImportLogById(id);
    }
}
