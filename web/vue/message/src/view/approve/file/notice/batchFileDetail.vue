n<template>
  <div id="batchFileDetail">
    <TY_NavTop title="需批量签收的文件"></TY_NavTop>
    <div class="container" v-loading="loading">
      <div class="list" v-if="!isCategory">
        <!--需批量签收的文件-->
        <div class="ty-fileItem active" v-for="(item ,index) in childFold" :key="index" @click="childFolder(item)">
          <div class="ty-fileRow">
            <div class="icon_folder"></div>
            <div class="ty-fileName" :title="item.name">{{item.name}}</div>
            <div class="ui-cell__ft" v-if="item.children > 0"></div>
          </div>
          <div class="ty-fileRow ty-fileHandle">
            <div class="ty-fileDetail">
              {{item.updateName || item.createName }}
              {{item.createDate || item.updateDate | formatDay}}
            </div>
          </div>
        </div>
        <div class="ty-fileItem" v-for="(item ,index) in dataList" :key="index">
          <div class="ty-fileRow">
            <div class="fileType file_png"></div>
            <div class="ty-fileInfo">
              <div class="ty-fileRow">
                <div class="ty-fileName" :title="item.name">{{item.name}}</div>
                <div class="ty-fileNoVersion text-right"  :title="item.fileSn">G{{item.changeNum}} 编号 ：{{item.fileSn}}</div>
              </div>
              <div class="ty-fileRow">
                <div class="ty-fileDetail">
                  {{item.updateName || item.createName }}
                  {{item.createDate || item.updateDate | formatDay}}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.topTip{
  padding: 8px 15px;
}
.btn-group{
  padding: 4px 0;
  .link-blue{
    margin-left: 16px;
  }
}
.container {
  height: calc(100% - 48px);
}
.list{
  height: calc(100% - 78px);
}
.ty-fileItem{
  background-color: #fff;
  cursor: default;
  position: relative;
  font-size: 13px;
  padding: 8px 12px;
  .ty-fileInfo{ flex: auto }
  .ty-fileName{ width:200px; word-wrap:break-word;  overflow:hidden; text-overflow:ellipsis; white-space: nowrap; flex: none }
  .ty-fileNoVersion{  width:100px; white-space:nowrap; overflow:hidden; text-overflow:ellipsis; font-size:12px; color:#999; flex: auto}
  .ty-fileDetail{  color: #aaa;  font-size: 12px;  font-weight: 300; margin-right: 16px; text-align: right}
  .ty-fileHandle{
    margin-top: 4px;
    .link-blue{
      flex: none;
    }
    .ty-fileDetail{
      flex: auto;
    }
  }
  &.active{
    cursor: pointer;
    &:hover{
      background: #f3f3f3;
    }
  }
}
.ty-fileRow { display: flex }
.item_title{
  width: 120px;
  flex: none;
  color: #999;
}
.item_content{
  flex: auto;
}
</style>

<script>
export default {
  name: 'approveFileDetail',
  data () {
    return {
      loading: true,
      dataList: [],
      childFold: [],
      isCategory: false
    }
  },
  created: function () {
    let batchData = localStorage.getItem('batch')
    batchData = JSON.parse(batchData)
    let that = this
    if (batchData.batchUuid) {
      this.axios.post(this.auth.webRoot + '/res/listBatchRes.do', {
        batchUuid: batchData.batchUuid
      })
        .then(function (response) {
          that.loading = false
          console.log(response)
          let fileData = response.data.data
          that.dataList = fileData.listHis
        })
        .catch(function (error) {
          console.log(error)
        })
    } else {
      this.isCategory = false
      this.axios.post(this.auth.webRoot + '/res/getFolderAndChildFolderManager.do', {
        categoryId: batchData.category,
        type: 1
      })
        .then(function (response) {
          that.loading = false
          console.log(response)
          let data = response.data.data
          let childFold= data.childFolder
          let list = data.list
          that.childFold = childFold
          that.dataList = list
        })
        .catch(function (error) {
          console.log(error)
        })
    }
  },
  methods: {
    childFolder: function (item) {
      console.log("跳转了", item.children)
      this.$router.push({
        path: `/childFolder/${item.id}`,
        query: {
          title: '需批量签收的文件'
        }
      })
    }
  }
}
</script>
