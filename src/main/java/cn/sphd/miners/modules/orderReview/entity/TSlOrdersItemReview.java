package cn.sphd.miners.modules.orderReview.entity;

import java.math.BigDecimal;
import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 对象 t_sl_orders_item_review
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
public class TSlOrdersItemReview
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** $column.columnComment */
    private Long org;

    /** $column.columnComment */
    private Long orders;

    /** $column.columnComment */
    private Long ordersItem;

    /** $column.columnComment */
    private Long ordersReview;

    /** $column.columnComment */
    private Date deliveryNum;

    /** $column.columnComment */
    private Long state;

    /** $column.columnComment */
    private Long isScheduled;

    /** $column.columnComment */
    private BigDecimal scheduledAmount;

    /** $column.columnComment */
    private Date deliveryDate;

    /** $column.columnComment */
    private Long deliveryAddressId;

    /** $column.columnComment */
    private String deliveryAddress;

    /** $column.columnComment */
    private Date surplusDate;

    /** $column.columnComment */
    private Long reviewer;

    /** $column.columnComment */
    private String reviewerName;

    /** $column.columnComment */
    private Date reviewDate;

    /** $column.columnComment */
    private BigDecimal newAmount;

    /** $column.columnComment */
    private Date newDate;

    /** $column.columnComment */
    private Long newAddressId;

    /** $column.columnComment */
    private String newAddress;

    /** $column.columnComment */
    private String keywords;

    /** $column.columnComment */
    private String memo;

    /** $column.columnComment */
    private Long creator;

    /** $column.columnComment */
    private String createName;

    /** $column.columnComment */
    private Date createDate;

    /** $column.columnComment */
    private Long updator;

    /** $column.columnComment */
    private String updateName;

    /** $column.columnComment */
    private Date updateDate;

    /** $column.columnComment */
    private Long operation;

    /** $column.columnComment */
    private Long previousId;

    /** $column.columnComment */
    private Long versionNo;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOrg(Long org) 
    {
        this.org = org;
    }

    public Long getOrg() 
    {
        return org;
    }
    public void setOrders(Long orders) 
    {
        this.orders = orders;
    }

    public Long getOrders() 
    {
        return orders;
    }
    public void setOrdersItem(Long ordersItem) 
    {
        this.ordersItem = ordersItem;
    }

    public Long getOrdersItem() 
    {
        return ordersItem;
    }
    public void setOrdersReview(Long ordersReview) 
    {
        this.ordersReview = ordersReview;
    }

    public Long getOrdersReview() 
    {
        return ordersReview;
    }
    public void setDeliveryNum(Date deliveryNum) 
    {
        this.deliveryNum = deliveryNum;
    }

    public Date getDeliveryNum() 
    {
        return deliveryNum;
    }
    public void setState(Long state) 
    {
        this.state = state;
    }

    public Long getState() 
    {
        return state;
    }
    public void setIsScheduled(Long isScheduled) 
    {
        this.isScheduled = isScheduled;
    }

    public Long getIsScheduled() 
    {
        return isScheduled;
    }
    public void setScheduledAmount(BigDecimal scheduledAmount) 
    {
        this.scheduledAmount = scheduledAmount;
    }

    public BigDecimal getScheduledAmount() 
    {
        return scheduledAmount;
    }
    public void setDeliveryDate(Date deliveryDate) 
    {
        this.deliveryDate = deliveryDate;
    }

    public Date getDeliveryDate() 
    {
        return deliveryDate;
    }
    public void setDeliveryAddressId(Long deliveryAddressId) 
    {
        this.deliveryAddressId = deliveryAddressId;
    }

    public Long getDeliveryAddressId() 
    {
        return deliveryAddressId;
    }
    public void setDeliveryAddress(String deliveryAddress) 
    {
        this.deliveryAddress = deliveryAddress;
    }

    public String getDeliveryAddress() 
    {
        return deliveryAddress;
    }
    public void setSurplusDate(Date surplusDate) 
    {
        this.surplusDate = surplusDate;
    }

    public Date getSurplusDate() 
    {
        return surplusDate;
    }
    public void setReviewer(Long reviewer) 
    {
        this.reviewer = reviewer;
    }

    public Long getReviewer() 
    {
        return reviewer;
    }
    public void setReviewerName(String reviewerName) 
    {
        this.reviewerName = reviewerName;
    }

    public String getReviewerName() 
    {
        return reviewerName;
    }
    public void setReviewDate(Date reviewDate) 
    {
        this.reviewDate = reviewDate;
    }

    public Date getReviewDate() 
    {
        return reviewDate;
    }
    public void setNewAmount(BigDecimal newAmount) 
    {
        this.newAmount = newAmount;
    }

    public BigDecimal getNewAmount() 
    {
        return newAmount;
    }
    public void setNewDate(Date newDate) 
    {
        this.newDate = newDate;
    }

    public Date getNewDate() 
    {
        return newDate;
    }
    public void setNewAddressId(Long newAddressId) 
    {
        this.newAddressId = newAddressId;
    }

    public Long getNewAddressId() 
    {
        return newAddressId;
    }
    public void setNewAddress(String newAddress) 
    {
        this.newAddress = newAddress;
    }

    public String getNewAddress() 
    {
        return newAddress;
    }
    public void setKeywords(String keywords) 
    {
        this.keywords = keywords;
    }

    public String getKeywords() 
    {
        return keywords;
    }
    public void setMemo(String memo) 
    {
        this.memo = memo;
    }

    public String getMemo() 
    {
        return memo;
    }
    public void setCreator(Long creator) 
    {
        this.creator = creator;
    }

    public Long getCreator() 
    {
        return creator;
    }
    public void setCreateName(String createName) 
    {
        this.createName = createName;
    }

    public String getCreateName() 
    {
        return createName;
    }
    public void setCreateDate(Date createDate) 
    {
        this.createDate = createDate;
    }

    public Date getCreateDate() 
    {
        return createDate;
    }
    public void setUpdator(Long updator) 
    {
        this.updator = updator;
    }

    public Long getUpdator() 
    {
        return updator;
    }
    public void setUpdateName(String updateName) 
    {
        this.updateName = updateName;
    }

    public String getUpdateName() 
    {
        return updateName;
    }
    public void setUpdateDate(Date updateDate) 
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() 
    {
        return updateDate;
    }
    public void setOperation(Long operation) 
    {
        this.operation = operation;
    }

    public Long getOperation() 
    {
        return operation;
    }
    public void setPreviousId(Long previousId) 
    {
        this.previousId = previousId;
    }

    public Long getPreviousId() 
    {
        return previousId;
    }
    public void setVersionNo(Long versionNo) 
    {
        this.versionNo = versionNo;
    }

    public Long getVersionNo() 
    {
        return versionNo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("org", getOrg())
            .append("orders", getOrders())
            .append("ordersItem", getOrdersItem())
            .append("ordersReview", getOrdersReview())
            .append("deliveryNum", getDeliveryNum())
            .append("state", getState())
            .append("isScheduled", getIsScheduled())
            .append("scheduledAmount", getScheduledAmount())
            .append("deliveryDate", getDeliveryDate())
            .append("deliveryAddressId", getDeliveryAddressId())
            .append("deliveryAddress", getDeliveryAddress())
            .append("surplusDate", getSurplusDate())
            .append("reviewer", getReviewer())
            .append("reviewerName", getReviewerName())
            .append("reviewDate", getReviewDate())
            .append("newAmount", getNewAmount())
            .append("newDate", getNewDate())
            .append("newAddressId", getNewAddressId())
            .append("newAddress", getNewAddress())
            .append("keywords", getKeywords())
            .append("memo", getMemo())
            .append("creator", getCreator())
            .append("createName", getCreateName())
            .append("createDate", getCreateDate())
            .append("updator", getUpdator())
            .append("updateName", getUpdateName())
            .append("updateDate", getUpdateDate())
            .append("operation", getOperation())
            .append("previousId", getPreviousId())
            .append("versionNo", getVersionNo())
            .toString();
    }
}
