package cn.sphd.miners.modules.equipment.entity;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.persistence.Transient;


/**
 * 装备管理_控制点
20240407 1.292机器设备之初始设置 新增对象 t_equ_control_point
 * 
 * <AUTHOR>
 * @date 2024-04-13
 */
public class TEquControlPoint
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 机构ID */

    private Long org;

    /** 名称 */

    private Long controlOption;

    /** 型号ID */

    private Long model;

    /** 计量单位,指向t_mt_unit表ID */

    private Long unitId;

    /** 下限 */

    private BigDecimal lowerLimit;

    /** 上限 */

    private BigDecimal upperLimit;

    /** 状态:0-不启用,1-启用 */

    private Long enabled;

    /** 启停用时间 */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date enabledTime;

    /** 同级排序 */

    private Long orders;

    /** 描述 */

    private String memo;

    /** 创建人id */

    private Long creator;

    /** 创建人 */

    private String createName;

    /** 创建时间 */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createDate;

    /** 修改人id */

    private Long updator;

    /** 修改人 */

    private String updateName;

    /** 修改时间 */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateDate;

    /** 操作:1-增,2-删,3-改,4-启用,5-停用 */

    private Long operation;

    /** 修改前记录ID */

    private Long previousId;

    /** 版本号,每次修改+1 */

    private Long versionNo;

    @Transient
    private String pics;

    @Transient
    private String optionName;

    @Transient
    private String unit;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOrg(Long org) 
    {
        this.org = org;
    }

    public Long getOrg() 
    {
        return org;
    }
    public void setControlOption(Long controlOption) 
    {
        this.controlOption = controlOption;
    }

    public Long getControlOption() 
    {
        return controlOption;
    }
    public void setModel(Long model) 
    {
        this.model = model;
    }

    public Long getModel() 
    {
        return model;
    }
    public void setUnitId(Long unitId) 
    {
        this.unitId = unitId;
    }

    public Long getUnitId() 
    {
        return unitId;
    }
    public void setLowerLimit(BigDecimal lowerLimit) 
    {
        this.lowerLimit = lowerLimit;
    }

    public BigDecimal getLowerLimit() 
    {
        return lowerLimit;
    }
    public void setUpperLimit(BigDecimal upperLimit) 
    {
        this.upperLimit = upperLimit;
    }

    public BigDecimal getUpperLimit() 
    {
        return upperLimit;
    }
    public void setEnabled(Long enabled) 
    {
        this.enabled = enabled;
    }

    public Long getEnabled() 
    {
        return enabled;
    }
    public void setEnabledTime(Date enabledTime) 
    {
        this.enabledTime = enabledTime;
    }

    public Date getEnabledTime() 
    {
        return enabledTime;
    }
    public void setOrders(Long orders) 
    {
        this.orders = orders;
    }

    public String getOptionName() {
        return optionName;
    }

    public void setOptionName(String optionName) {
        this.optionName = optionName;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Long getOrders()
    {
        return orders;
    }
    public void setMemo(String memo) 
    {
        this.memo = memo;
    }

    public String getMemo() 
    {
        return memo;
    }
    public void setCreator(Long creator) 
    {
        this.creator = creator;
    }

    public Long getCreator() 
    {
        return creator;
    }
    public void setCreateName(String createName) 
    {
        this.createName = createName;
    }

    public String getCreateName() 
    {
        return createName;
    }
    public void setCreateDate(Date createDate) 
    {
        this.createDate = createDate;
    }

    public Date getCreateDate() 
    {
        return createDate;
    }
    public void setUpdator(Long updator) 
    {
        this.updator = updator;
    }

    public Long getUpdator() 
    {
        return updator;
    }
    public void setUpdateName(String updateName) 
    {
        this.updateName = updateName;
    }

    public String getUpdateName() 
    {
        return updateName;
    }
    public void setUpdateDate(Date updateDate) 
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() 
    {
        return updateDate;
    }
    public void setOperation(Long operation) 
    {
        this.operation = operation;
    }

    public Long getOperation() 
    {
        return operation;
    }
    public void setPreviousId(Long previousId) 
    {
        this.previousId = previousId;
    }

    public Long getPreviousId() 
    {
        return previousId;
    }
    public void setVersionNo(Long versionNo) 
    {
        this.versionNo = versionNo;
    }

    public Long getVersionNo() 
    {
        return versionNo;
    }

    public String getPics() {
        return pics;
    }

    public void setPics(String pics) {
        this.pics = pics;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("org", getOrg())
            .append("controlOption", getControlOption())
            .append("model", getModel())
            .append("unitId", getUnitId())
            .append("lowerLimit", getLowerLimit())
            .append("upperLimit", getUpperLimit())
            .append("enabled", getEnabled())
            .append("enabledTime", getEnabledTime())
            .append("orders", getOrders())
            .append("memo", getMemo())
            .append("creator", getCreator())
            .append("createName", getCreateName())
            .append("createDate", getCreateDate())
            .append("updator", getUpdator())
            .append("updateName", getUpdateName())
            .append("updateDate", getUpdateDate())
            .append("operation", getOperation())
            .append("previousId", getPreviousId())
            .append("versionNo", getVersionNo())
            .toString();
    }
}
