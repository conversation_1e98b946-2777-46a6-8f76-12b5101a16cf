package cn.sphd.miners.modules.commodity.service;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.commodity.entity.PdCategory;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;

public interface PdCategoryService {

    List<PdCategory> getFirstCategoryListByOid(Integer oid, PageInfo pageInfo);

    List<PdCategory> getSonCategoryListByPid(Integer parentId,PageInfo pageInfo);

    List<PdCategory> getPathCategoryListById(Integer id);

    void addPdCategory(User loginUser, Integer pid, String name);

    JsonResult updatePdCategory(User loginUser, Integer id, String name);

    PdCategory getPdCategoryByName(Integer pid,String name); //  同级 是否存在相同名称

    JsonResult deletePdCategory(Integer id);

    void initPdCategory(Integer oid,String name,String code);

    PdCategory getPdCategoryById(Integer id);
}
