package cn.sphd.miners.modules.commodity.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.commodity.entity.PdMerchandiseInvoice;
import cn.sphd.miners.modules.commodity.entity.PdMerchandiseInvoiceHistory;

import java.io.Serializable;

public interface PdMerchandiseInvoiceHistoryDao  extends IBaseDao<PdMerchandiseInvoiceHistory, Serializable> {

    PdMerchandiseInvoiceHistory insert(PdMerchandiseInvoice merchandiseInvoice);
}

