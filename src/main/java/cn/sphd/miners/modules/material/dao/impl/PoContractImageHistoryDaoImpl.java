package cn.sphd.miners.modules.material.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.material.dao.PoContractImageHistoryDao;
import cn.sphd.miners.modules.material.entity.PoContractImage;
import cn.sphd.miners.modules.material.entity.PoContractImageHistory;
import cn.sphd.miners.modules.sales.entity.SlContractImageHistory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
@Repository
public class PoContractImageHistoryDaoImpl extends BaseDao<PoContractImageHistory, Serializable>implements PoContractImageHistoryDao {
    @Override
    public PoContractImageHistory insert(PoContractImage poContractImage, Integer hisId) {
        PoContractImageHistory history=new PoContractImageHistory();

        BeanUtils.copyProperties(poContractImage,history);

        history.setContractHistory(hisId);
        history.setContractImage(poContractImage.getId());
        history.setId(null);
        save(history);
        return history;
    }
}
