package cn.sphd.miners.modules.recruit.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.util.Date;


/**
 * 岗位历史表
 * <AUTHOR>
 * @date 20230506
 **/
@Entity
@Table(name = "t_rd_post_history")
public class RdPostHistory {


    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;//`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',

    @Column
    private Integer org;//机构ID

    @Column(name="post" ,nullable=true , unique=false, insertable=false, updatable=false)
    private Long postId;//岗位ID

    @Column(length = 50)
    private String name; //名称

    @Column(length = 255)
    private String duty; //职责,多个以json数组方式存储

    @Column(length = 255)
    private String requirement; //要求,多个以json数组方式存储

    @Column(length = 255)
    private String synthesis; //综合,多个以json数组方式存储代码

    @Column(length = 255)
    private String others; //其它,多个以json数组方式存储

    @Column(length = 255)
    private String memo; //备注

    @Column(length = 100)
    private String keywords; //关键字

    @Column
    private Boolean enabled=true;//启用状态:0-不启用,1-启用

    @Column(name = "enabled_time")
    private Date enabledTime;//'启停用时间'

    @Column(name = "enabled_user")
    private Integer enabled_user;//启停用用户ID

    @Column(name = "enabled_name",length = 50)
    private String enabled_name; //启停用用户名称

    @Column(name = "begin_time")
    private Date beginTime;//生效时间

    @Column(name = "end_time")
    private Date endTime;//失效时间

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="operation"   , nullable=true , unique=false)
    private Integer operation; //操作:1-增,2-删,3-改内容,4-启用,5-停用,6-更改名称,7-改错别字

    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId; //修改前记录ID

    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo; //版本号,每次修改+1

    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="post", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private RdPost rdPost ;//启事主表


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Long getPostId() {
        return postId;
    }

    public void setPostId(Long postId) {
        this.postId = postId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDuty() {
        return duty;
    }

    public void setDuty(String duty) {
        this.duty = duty;
    }

    public String getRequirement() {
        return requirement;
    }

    public void setRequirement(String requirement) {
        this.requirement = requirement;
    }

    public String getSynthesis() {
        return synthesis;
    }

    public void setSynthesis(String synthesis) {
        this.synthesis = synthesis;
    }

    public String getOthers() {
        return others;
    }

    public void setOthers(String others) {
        this.others = others;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    public Integer getEnabled_user() {
        return enabled_user;
    }

    public void setEnabled_user(Integer enabled_user) {
        this.enabled_user = enabled_user;
    }

    public String getEnabled_name() {
        return enabled_name;
    }

    public void setEnabled_name(String enabled_name) {
        this.enabled_name = enabled_name;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getOperation() {
        return operation;
    }

    public void setOperation(Integer operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public RdPost getRdPost() {
        return rdPost;
    }

    public void setRdPost(RdPost rdPost) {
        this.rdPost = rdPost;
    }
}
