package cn.sphd.miners.modules.trainManage.dto;

import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.trainManage.entity.TrainingExamUser;

import java.util.Date;
import java.util.List;

public class RespExamUserInfo {
    private String userName;
    private String mobile;
    private String departName;//部门
    private String postName;//职位
    private Integer userId;
    private Integer examTotal;//参考总数
    private Integer examPassing;//一次通过总数
    private Integer answerTimes;//补考次数
    private Double meanScore;//'平均分',
    private Double highestScore;// '最高分',
    private Double lowestScore;//'最低分',
    private Integer highestRanking;// '最高名次',
    private Integer lowestRanking;//'最低名次',
    private Date startTime;
    private Date endTime;
    private List<TrainingExamUser> trainingExamUserList;

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getDepartName() {
        return departName;
    }

    public void setDepartName(String departName) {
        this.departName = departName;
    }

    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getExamTotal() {
        return examTotal;
    }

    public void setExamTotal(Integer examTotal) {
        this.examTotal = examTotal;
    }

    public Integer getExamPassing() {
        return examPassing;
    }

    public void setExamPassing(Integer examPassing) {
        this.examPassing = examPassing;
    }

    public Integer getAnswerTimes() {
        return answerTimes;
    }

    public void setAnswerTimes(Integer answerTimes) {
        this.answerTimes = answerTimes;
    }

    public Double getMeanScore() {
        return meanScore;
    }

    public void setMeanScore(Double meanScore) {
        this.meanScore = meanScore;
    }

    public Double getHighestScore() {
        return highestScore;
    }

    public void setHighestScore(Double highestScore) {
        this.highestScore = highestScore;
    }

    public Double getLowestScore() {
        return lowestScore;
    }

    public void setLowestScore(Double lowestScore) {
        this.lowestScore = lowestScore;
    }

    public Integer getHighestRanking() {
        return highestRanking;
    }

    public void setHighestRanking(Integer highestRanking) {
        this.highestRanking = highestRanking;
    }

    public Integer getLowestRanking() {
        return lowestRanking;
    }

    public void setLowestRanking(Integer lowestRanking) {
        this.lowestRanking = lowestRanking;
    }

    public List<TrainingExamUser> getTrainingExamUserList() {
        return trainingExamUserList;
    }

    public void setTrainingExamUserList(List<TrainingExamUser> trainingExamUserList) {
        this.trainingExamUserList = trainingExamUserList;
    }
}
