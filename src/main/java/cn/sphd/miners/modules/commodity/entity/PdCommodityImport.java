package cn.sphd.miners.modules.commodity.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2021-08-29 
 */

@Entity ( name ="PdCommodityImport" )
@Table ( name ="t_pd_commodity_import" )
public class PdCommodityImport  implements Serializable {

	private static final long serialVersionUID =  8371356142509527830L;

	/**
	 * ID
	 */
	@Id
	@Column(name="id" )
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * 机构ID
	 */
   	@Column(name = "org" )
	private Integer org;

	/**
	 * 导入ID
	 */
   	@Column(name = "import" )
	private Integer importId;

	/**
	 * 状态:1-导入临时表,2-录入信息,3-完成
	 */
   	@Column(name = "state" )
	private String state;

	/**
	 * 客户代号
	 */
   	@Column(name = "code" )
	private String code;

	/**
	 * 名称
	 */
   	@Column(name = "name" )
	private String name;

	/**
	 * 全称
	 */
   	@Column(name = "full_name" )
	private String fullName;

	/**
	 * 型号
	 */
   	@Column(name = "model" )
	private String model;

	/**
	 * 规格
	 */
   	@Column(name = "specifications" )
	private String specifications;

	/**
	 * 计量单位
	 */
   	@Column(name = "unit" )
	private String unit;
	/**
	 * 计量单位ID
	 */

	@Column(name = "unit_id" )
	private Integer unitId;

	/**
	 * 发票类型
	 */
   	@Column(name = "invoice_type" )
	private String invoiceType;

	/**
	 * 税率(增专)
	 */
   	@Column(name = "tax_rate" )
	private Double taxRate;

	/**
	 * 含税单价(增专)
	 */
   	@Column(name = "unit_price" )
	private Double unitPrice;

	/**
	 * 不含税单价(增专)
	 */
   	@Column(name = "unit_price_notax" )
	private Double unitPriceNotax;

	/**
	 * 开票单价(增通)
	 */
   	@Column(name = "unit_price_invoice" )
	private Double unitPriceInvoice;

	/**
	 * 不开票单价
	 */
   	@Column(name = "unit_price_noinvoice" )
	private Double unitPriceNoinvoice;

	/**
	 * 参考价格
	 */
   	@Column(name = "unit_price_reference" )
	private Double unitPriceReference;

	/**
	 * 最低库存
	 */
   	@Column(name = "mini_stock" )
	private Double miniStock;

	/**
	 * 顾客ID
	 */
	@Column(name = "customer" )
	private Integer customer;

	/**
	 * 客户名称
	 */
	@Column(name = "customer_name" )
	private String customerName;
	/**
	 * 是否有合同
	 */
	@Column(name = "`has_contract`" )
	private Integer hasContract;
	/**
	 * 合同id
	 */
	@Column(name = "`contract_id`" )
	private Integer contractId;
	/**
	 * 合同号
	 */
	@Column(name = "`contract_sn`" )
	private String contractSn;
	/**
	 * 价格说明
	 */
	@Column(name = "`price_desc`" )
	private String priceDesc;
	/**
	 * 商品说明
	 */
   	@Column(name = "`commodity_desc`" )
	private String desc;

	/**
	 * 备注
	 */
   	@Column(name = "memo" )
	private String memo;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_time" )
	private Date createTime;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_time" )
	private Date updateTime;

	/**
	 * 操作:1-增,2-删,3-修改
	 */
   	@Column(name = "operation" )
	private String operation;

	/**
	 * 修改前记录ID
	 */
   	@Column(name = "previous_id" )
	private Integer previousId;

	/**
	 * 版本号,每次修改+1
	 */
   	@Column(name = "version_no" )
	private Integer versionNo;

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOrg() {
		return this.org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getImportId() {
		return importId;
	}

	public void setImportId(Integer importId) {
		this.importId = importId;
	}

	public String getState() {
		return this.state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getCode() {
		return this.code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getFullName() {
		return this.fullName;
	}

	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	public String getModel() {
		return this.model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getSpecifications() {
		return this.specifications;
	}

	public void setSpecifications(String specifications) {
		this.specifications = specifications;
	}

	public String getUnit() {
		return this.unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	public String getInvoiceType() {
		return this.invoiceType;
	}

	public void setInvoiceType(String invoiceType) {
		this.invoiceType = invoiceType;
	}

	public Double getTaxRate() {
		return this.taxRate;
	}

	public void setTaxRate(Double taxRate) {
		this.taxRate = taxRate;
	}

	public Double getUnitPrice() {
		return this.unitPrice;
	}

	public void setUnitPrice(Double unitPrice) {
		this.unitPrice = unitPrice;
	}

	public Double getUnitPriceNotax() {
		return this.unitPriceNotax;
	}

	public void setUnitPriceNotax(Double unitPriceNotax) {
		this.unitPriceNotax = unitPriceNotax;
	}

	public Double getUnitPriceInvoice() {
		return this.unitPriceInvoice;
	}

	public void setUnitPriceInvoice(Double unitPriceInvoice) {
		this.unitPriceInvoice = unitPriceInvoice;
	}

	public Double getUnitPriceNoinvoice() {
		return this.unitPriceNoinvoice;
	}

	public void setUnitPriceNoinvoice(Double unitPriceNoinvoice) {
		this.unitPriceNoinvoice = unitPriceNoinvoice;
	}

	public Double getUnitPriceReference() {
		return this.unitPriceReference;
	}

	public void setUnitPriceReference(Double unitPriceReference) {
		this.unitPriceReference = unitPriceReference;
	}

	public Double getMiniStock() {
		return this.miniStock;
	}

	public void setMiniStock(Double miniStock) {
		this.miniStock = miniStock;
	}

	public Integer getCustomer() {
		return customer;
	}

	public void setCustomer(Integer customer) {
		this.customer = customer;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public Integer getHasContract() {
		return hasContract;
	}

	public void setHasContract(Integer hasContract) {
		this.hasContract = hasContract;
	}

	public Integer getContractId() {
		return contractId;
	}

	public void setContractId(Integer contractId) {
		this.contractId = contractId;
	}

	public String getContractSn() {
		return contractSn;
	}

	public void setContractSn(String contractSn) {
		this.contractSn = contractSn;
	}

	public String getPriceDesc() {
		return priceDesc;
	}

	public void setPriceDesc(String priceDesc) {
		this.priceDesc = priceDesc;
	}

	public String getDesc() {
		return this.desc;
	}

	public void setDesc(String desc) {
		this.desc = desc;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getCreator() {
		return this.creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return this.createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Integer getUpdator() {
		return this.updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return this.updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateTime() {
		return this.updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getOperation() {
		return this.operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public Integer getPreviousId() {
		return this.previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return this.versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}

}
