import {request} from "@/utils/request";
/* ----------productFeatures----------- */
// creator: lyt，2024-06-17 08:07:33， 待产品特性初始化的产品
export function getProductList(data) {
    return request({
        url: '/feature/getProductFeatureList.do',
        data: data
    })
}
// creator: lyt，2024-06-17 11:34:31， 待产品特性初始化的产品
export function getYszNum(data) {
    return request({
        url: '/feature/getYszNum.do',
        data: data
    })
}

// creator: lyt，2024-06-17 11:34:31， 获取已设置产品特性的列表
export function getFeatureItemList(id) {
    return request({
        url: '/feature/getFeatureItemList.do',
        data: {
            product: id
        }
    })
}

// creator: lyt，2024-06-17 09:57:25， 增加产品特性确定
export function addPtFeatures(data, url) {
    return request({
        url: url,
        data: data
    })
}
// creator: lyt，2024-06-18 08:07:21， 增加产品特性确定
export function getRankList() {
    return request({
        url: '/feature/getRankList.do',
        data: {}
    })
}
// creator: lyt，2024-06-18 10:23:15， 等级设置提交
export function addRank(data) {
    return request({
        url: '/feature/getRankSetting.do',
        data: data
    })
}
// creator: lyt，2024-06-18 13:05:15， 设置记录
export function getRankRecord() {
    return request({
        url: '/feature/getRankRecord.do',
        data: {}
    })
}
// creator: lyt，2024-06-18 13:23:15， 设置记录-特性等级查看
export function getRankRecordDetail(id) {
    return request({
        url: '/feature/getRankRecordDetail.do',
        data: {
            id: id
        }
    })
}
// creator: lyt，2024-06-19 08:05:22， .获取设备列表
export function getEquEquipmentList(data) {
    return request({
        url: '/feature/getEquEquipmentList.do',
        data: data
    })
}
// creator: lyt，2024-06-19 08:05:22， .获取设备列表
export function getEquModel(data) {
    return request({
        url: '/feature/getEquModelListByEquipment.do',
        data: data
    })
}
// creator: lyt，2024-06-19 08:05:22， 获取控制方法列表 （设备/工具以外的方法）
export function getCheckControlList(enabled ) {
    return request({
        url: '/feature/getCheckControlList.do',
        data: { 'enabled': enabled } ////1=正常的 0=停止的
    })
}
// creator: lyt，2024-06-20 10:12:33， 增加/修改控制方法
export function addCheckControl(data) {
    return request({
        url: '/feature/addCheckControl.do',
        data: data
    })
}
// creator: lyt，2024-06-20 10:12:33， 增加控制方法
export function updateCheckControl(data) {
    return request({
        url: '/feature/updateCheckControl.do',
        data: data
    })
}
// creator: lyt，2024-06-20 13:55:41， 启停控制方法
export function startOrStopCheckControl(data) {
    return request({
        url: '/feature/startOrStopCheckControl.do',
        data: data
    })
}
// creator: lyt，2024-06-20 14:22:36， 获取控制方法记录列表
export function getCheckControlRecord(id) {
    return request({
        url: '/feature/getCheckControlRecord.do',
        data: {
            id: id
        }
    })
}
// creator: lyt，2024-06-20 14:22:36， 获取控制方法录详情
export function getCheckControlRecordDetail(id) {
    return request({
        url: '/feature/getCheckControlRecordDetail.do',
        data: {
            id: id
        }
    })
}
// creator: lyt，2024-06-20 11:22:02， 查看产品信息
export function getPdBaseOne(id) {
    return request({
        url: '/product/getPdBaseOne.do',
        data: {
            pdBaseId: id
        }
    })
}
// creator: lyt，2024-06-25 15:12:33， 查看产品信息
export function savePdBaseImage(data) {
    return request({
        url: '/product/saveOrUpdatePdBaseImage.do',
        data: data
    })
}
// creator: lyt，2024-06-25 16:10:36， 移除产品下的图纸
export function removePdBaseImage(data) {
    return request({
        url: '/product/removePdBaseImage.do',
        data: data
    })
}
// creator: lyt，2024-06-19 08:05:22， 单位
export function getUnitList() {
    return request({
        url: '/unit/selectUnitListForModule.do',
        data: { 'module': 3 }
    })
}
/*creator:lyt 2024/5/27  08:05
* 新增计量单位
* */
export function addUnitOk (data) {
    return request({
        url: '../unit/addUnit.do',
        data: data
    })
}
// creator: lyt，2024-07-2 08:10:36， 移除产品下的图纸
export function getPdBaseImageRecord(data, url) {
    return request({
        url: url,
        data: data
    })
}
//creator:lyt 2024/7/2 15:52 获取一级文件夹列表
export function getFirstDoc(){
    return request({
        url: '../res/getInitialFolder.do',
        data: { "type": 1  }
    })
}
//creator:lyt 2024/7/2 15:52 获取文件
export function getFile(data){
    return request({
        url: '../res/getFile.do',
        data: data
    })
}
//creator:lyt 2024/7/2 16:10 获取文件夹
export function getChildrenDoc(data){
    return request({
        url: '../res/getFolderAndChildFolder.do',
        data: data
    })
}
//creator:lyt 2024/7/2 17:10 文件功能 - 基本信息
export function getBasicMessage(data, url){
    return request({
        url: url,
        data: data
    })
}
//creator:lyt 2024/7/2 19:02 文件功能 - 修改记录
export function getRecordByShow(data){
    return request({
        url: '../res/getRecordByShow.do',
        data: data
    })
}
//creator:lyt 2024/7/2 19:02 文件功能 - 基本信息:移动记录/文件名称修改记录/文件编号修改记录
export function getMoveRecord(data){
    return request({
        url: '../res/getRecordByUpAndMove.do',
        data: data
    })
}
//creator:lyt 2024/7/3 11:11 请选择内部文件
export function chooseFolderSure(data){
    return request({
        url: '../product/saveOrUpdatePdResource.do',
        data: data
    })
}
//creator:lyt 2024/7/4 08:32 获取编码
export function getCodeNumber(){
    return request({
        url: '../feature/getCodeNumber.do',
        data: {}
    })
}
//creator:lyt 2024/7/8 08:25 删除控制方法
export function deleteCheckControl(id){
    return request({
        url: '../feature/deleteCheckControl.do',
        data: {
            id: id
        }
    })
}
//creator:lyt 2024/7/9 09:08 删除该条产品特性
export function deleteFeature(id){
    return request({
        url: '../feature/deleteFeatureItem.do',
        data: {
            id: id
        }
    })
}
//creator:lyt 2024/7/9 11:42 初始化设置提交
export function productFeatureSetting(id){
    return request({
        url: '../feature/productFeatureSetting.do',
        data: {
            product: id
        }
    })
}
//creator:lyt 2024/7/9 13:35 删除等级设置提交
export function deleteRankSetting(id){
    return request({
        url: '../feature/deleteRankSetting.do',
        data: {
            id: id
        }
    })
}
//creator:lyt 2024/7/18 14:45 解除关联确定按钮
export function securePdResource(json){
    return request({
        url: '../product/securePdResource.do',
        data: json
    })
}
//creator:lyt 2024/7/18 16:12 与文件关联-修改记录-点击‘查看’展示文件内容
export function getResForProduct(json){
    return request({
        url: '../res/getResForProduct.do',
        data: json
    })
}


//creator:lyt 2024/7/18 16:12 与文件关联-修改记录-点击‘查看’展示文件内容
export function changeVersionRecord(json){
    return request({
        url: '../res/getUpdateFile.do',
        data: json
    })
}
//creator:lyt 2024/10/24 11:10 删除等级设置判断
export function delRankUse(json){
    return request({
        url: '../feature/rankUse.do',
        data: json
    })
}

