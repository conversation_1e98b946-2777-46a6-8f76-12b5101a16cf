package fr.noop.subtitle.lrc;

import fr.noop.subtitle.model.SubtitleCue;
import fr.noop.subtitle.model.SubtitleLine;
import fr.noop.subtitle.model.SubtitleObject;
import fr.noop.subtitle.model.SubtitleWriter;
import fr.noop.subtitle.util.SubtitleTimeCode;

import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.util.Iterator;

/**
 * <AUTHOR>
 * @date 2022年12月07日 09:56
 **/
public class LrcWrite implements SubtitleWriter {
    private String charset;

    public LrcWrite(String charset) {
        this.charset = charset;
    }

    public void write(SubtitleObject subtitleObject, OutputStream os) throws IOException {
        Iterator var4 = subtitleObject.getCues().iterator();
        SubtitleTimeCode lastTimeCode = null;
        String line;

        try {
            while(var4.hasNext()) {
                SubtitleCue cue = (SubtitleCue)var4.next();
                if(lastTimeCode != null && cue.getStartTime().compareTo(lastTimeCode) > 0) {
                    line = String.format("[%s]\n", this.formatTimeCode(lastTimeCode));
                    os.write(line.getBytes(this.charset));
                }
                for(SubtitleLine subTitleLine :cue.getLines()) {
                    line = String.format("[%s]%s\n", this.formatTimeCode(cue.getStartTime()), subTitleLine.toString());
                    os.write(line.getBytes(this.charset));
                }
                lastTimeCode = cue.getEndTime();
            }
        } catch (UnsupportedEncodingException var9) {
            throw new IOException("Encoding error in input subtitle");
        }
    }

    private String formatTimeCode(SubtitleTimeCode timeCode) {
        return String.format("%02d:%02d.%02d", timeCode.getHour() * 60 + timeCode.getMinute(), timeCode.getSecond(), timeCode.getMillisecond() / 10);
    }
}