package cn.sphd.miners.modules.generalAffairs.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.generalAffairs.entity.ApprovalInstance;
import cn.sphd.miners.modules.generalAffairs.entity.FinanceAssetsMaintain;

import java.io.Serializable;

/**
 * @ClassName ApprovalInstanceDao
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/1/9 10:10
 * @Version 1.0
 */
public interface ApprovalInstanceDao extends IBaseDao<ApprovalInstance,Serializable> {
}
