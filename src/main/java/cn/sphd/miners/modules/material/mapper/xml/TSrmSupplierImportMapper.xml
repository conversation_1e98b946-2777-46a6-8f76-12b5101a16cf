<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sphd.miners.modules.material.mapper.TSrmSupplierImportMapper">
    
    <resultMap type="TSrmSupplierImport" id="TSrmSupplierImportResult">
        <result property="id"    column="id"    />
        <result property="org"    column="org"    />
        <result property="import"    column="import"    />
        <result property="state"    column="state"    />
        <result property="name"    column="name"    />
        <result property="fullName"    column="full_name"    />
        <result property="codeName"    column="code_name"    />
        <result property="chargeAcceptable"    column="charge_acceptable"    />
        <result property="isImprest"    column="is_imprest"    />
        <result property="memo"    column="memo"    />
        <result property="creator"    column="creator"    />
        <result property="createName"    column="create_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="updator"    column="updator"    />
        <result property="updateName"    column="update_name"    />
        <result property="updateTime"    column="update_time"    />
        <result property="operation"    column="operation"    />
        <result property="previousId"    column="previous_id"    />
        <result property="versionNo"    column="version_no"    />
    </resultMap>

    <sql id="selectTSrmSupplierImportVo">
        select id, org, import, state, name, full_name, code_name, charge_acceptable, is_imprest, memo, creator, create_name, create_time, updator, update_name, update_time, operation, previous_id, version_no from t_srm_supplier_import
    </sql>

    <select id="selectTSrmSupplierImportList" parameterType="TSrmSupplierImport" resultMap="TSrmSupplierImportResult">
        <include refid="selectTSrmSupplierImportVo"/>
        <where>  
            <if test="org != null "> and org = #{org}</if>
            <if test="import != null "> and import = #{import}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="fullName != null  and fullName != ''"> and full_name like concat('%', #{fullName}, '%')</if>
            <if test="codeName != null  and codeName != ''"> and code_name like concat('%', #{codeName}, '%')</if>
            <if test="chargeAcceptable != null "> and charge_acceptable = #{chargeAcceptable}</if>
            <if test="isImprest != null  and isImprest != ''"> and is_imprest = #{isImprest}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
            <if test="creator != null "> and creator = #{creator}</if>
            <if test="createName != null  and createName != ''"> and create_name like concat('%', #{createName}, '%')</if>
            <if test="updator != null "> and updator = #{updator}</if>
            <if test="updateName != null  and updateName != ''"> and update_name like concat('%', #{updateName}, '%')</if>
            <if test="operation != null  and operation != ''"> and operation = #{operation}</if>
            <if test="previousId != null "> and previous_id = #{previousId}</if>
            <if test="versionNo != null "> and version_no = #{versionNo}</if>
        </where>
    </select>
    
    <select id="selectTSrmSupplierImportById" parameterType="Long" resultMap="TSrmSupplierImportResult">
        <include refid="selectTSrmSupplierImportVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTSrmSupplierImport" parameterType="TSrmSupplierImport" useGeneratedKeys="true" keyProperty="id">
        insert into t_srm_supplier_import
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="org != null">org,</if>
            <if test="import != null">import,</if>
            <if test="state != null">state,</if>
            <if test="name != null">name,</if>
            <if test="fullName != null">full_name,</if>
            <if test="codeName != null">code_name,</if>
            <if test="chargeAcceptable != null">charge_acceptable,</if>
            <if test="isImprest != null">is_imprest,</if>
            <if test="memo != null">memo,</if>
            <if test="creator != null">creator,</if>
            <if test="createName != null">create_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updator != null">updator,</if>
            <if test="updateName != null">update_name,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="operation != null">operation,</if>
            <if test="previousId != null">previous_id,</if>
            <if test="versionNo != null">version_no,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="org != null">#{org},</if>
            <if test="import != null">#{import},</if>
            <if test="state != null">#{state},</if>
            <if test="name != null">#{name},</if>
            <if test="fullName != null">#{fullName},</if>
            <if test="codeName != null">#{codeName},</if>
            <if test="chargeAcceptable != null">#{chargeAcceptable},</if>
            <if test="isImprest != null">#{isImprest},</if>
            <if test="memo != null">#{memo},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createName != null">#{createName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updator != null">#{updator},</if>
            <if test="updateName != null">#{updateName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="operation != null">#{operation},</if>
            <if test="previousId != null">#{previousId},</if>
            <if test="versionNo != null">#{versionNo},</if>
         </trim>
    </insert>

    <update id="updateTSrmSupplierImport" parameterType="TSrmSupplierImport">
        update t_srm_supplier_import
        <trim prefix="SET" suffixOverrides=",">
            <if test="org != null">org = #{org},</if>
            <if test="import != null">import = #{import},</if>
            <if test="state != null">state = #{state},</if>
            <if test="name != null">name = #{name},</if>
            <if test="fullName != null">full_name = #{fullName},</if>
            <if test="codeName != null">code_name = #{codeName},</if>
            <if test="chargeAcceptable != null">charge_acceptable = #{chargeAcceptable},</if>
            <if test="isImprest != null">is_imprest = #{isImprest},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="createName != null">create_name = #{createName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updator != null">updator = #{updator},</if>
            <if test="updateName != null">update_name = #{updateName},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="operation != null">operation = #{operation},</if>
            <if test="previousId != null">previous_id = #{previousId},</if>
            <if test="versionNo != null">version_no = #{versionNo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTSrmSupplierImportById" parameterType="Long">
        delete from t_srm_supplier_import where id = #{id}
    </delete>

    <delete id="deleteTSrmSupplierImportByIds" parameterType="String">
        delete from t_srm_supplier_import where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>