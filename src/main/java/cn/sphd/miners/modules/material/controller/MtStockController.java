package cn.sphd.miners.modules.material.controller;

import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.commodity.entity.PdBase;
import cn.sphd.miners.modules.commodity.entity.PdMerchandise;
import cn.sphd.miners.modules.commodity.entity.PdPack;
import cn.sphd.miners.modules.commodity.service.PdPackService;
import cn.sphd.miners.modules.commodity.service.ProductService;
import cn.sphd.miners.modules.material.entity.MtMaterialApply;
import cn.sphd.miners.modules.material.entity.MtMaterialStock;
import cn.sphd.miners.modules.material.entity.MtStock;
import cn.sphd.miners.modules.material.entity.MtStorage;
import cn.sphd.miners.modules.material.service.*;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.entity.UserMessage;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.personal.service.UserMessageService;
import cn.sphd.miners.modules.sales.service.SlOrdersService;
import cn.sphd.miners.modules.system.entity.ApprovalItem;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.RoleService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by Administrator on 2017/8/2.
 */
@Controller
@RequestMapping("/mtStock")
public class MtStockController {
    @Autowired
    MtStockService mtStockService;
    @Autowired
    SlOrdersService slOrdersService;
    @Autowired
    ProductService productService;
    @Autowired
    MaterielService materielService;
    @Autowired
    MtMaterialStockService mtMaterialStockService;
    @Autowired
    MtStorageService mtStorageService;
    @Autowired
    MtMaterialApplyService mtMaterialApplyService;
    @Autowired
    UserMessageService userMessageService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    PdPackService packService;
    @Autowired
    RoleService roleService;

    @Autowired
    cn.sphd.miners.modules.sales.service.PercentUtil percentUtil;
    //入库申请主页
    @RequestMapping("/mtStockIndex.do")
    public String mtStockIndex() {

        return "/production/mtStockIndex";
    }

    //让步申请1主页
    @RequestMapping("/mtStockGiveInOne.do")
    public String mtStockGiveInOne() {

        return "/production/consessionApply";
    }

    //入库检验主页
    @RequestMapping("/MtStockQuality.do")
    public String MtStockQuality() {

        return "/quality/storageTest";
    }

    //让步申请2主页
    @RequestMapping("/mtStockGiveInTwo.do")
    public String mtStockGiveInTwo() {

        return "/technology/consessionAccept";
    }

    // 构成管理
    @RequestMapping("/constituteManage.do")
    public String constituteManage() {
        return "/technology/constituteManage";
    }

    // 构成管理
    @RequestMapping("/material.do")
    public String material() {
        return "/technology/material";
    }

    //入库受理主页
    @RequestMapping("/MtStockApproval.do")
    public String MtStockApproval() {

        return "/storage/accept";
    }
    // 库存查询-原辅
    @RequestMapping("/MtStockSearchRaw.do")
    public String MtStockSearch() {

        return "/storageSearch/rawMt";
    }

    // 库存查询-成品
    @RequestMapping("/MtStockSearchAccept.do")
    public String MtStockSearchAccept() {

        return "/storageSearch/accept";
    }

    /**
     * sales/getOuterSnLike.do销售-订单管理-检索商品外部图号
     * http://localhost:8888/mtStock/getOuterSnLike.do
     */
    @ResponseBody
    @RequestMapping("/getOuterSnLike.do")
    public void getOuterSnLike(HttpServletResponse response, User user, String outerSn,Integer state) throws IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        Integer oid =user.getOid();
        List<PdMerchandise> pdMerchandise = mtStockService.getOuterSnByLike(oid, outerSn,state);
        List<PdMerchandise> pdMerchandises = new ArrayList<>();
        if (pdMerchandise.size() > 0) {
            for (PdMerchandise pdMerchandise0 : pdMerchandise) {
                pdMerchandise0.setcInnerSn(pdMerchandise0.getcInnerSn());

                PdBase pdBase = productService.getById(pdMerchandise0.getProduct_());
                if (pdBase != null) {
                    pdMerchandise0.setcInnerSnName(pdBase.getName());
                    pdMerchandise0.setcInnerSn(pdBase.getInnerSn());
                }

                if (pdMerchandise0.getOuterSn() != null) {


                    List<PdPack> pdPacks = packService.getPdPackListByPdBaseId(pdMerchandise0.getId());
                    Double pack = 0D;
                    for (PdPack p : pdPacks) {
                        if (p.getAmount() != null)
                            pack += p.getAmount();
                    }
                    pdMerchandise0.setPack(pack);
                }
                if(pdMerchandise0.getProduct_()!=null){
                    pdMerchandises.add(pdMerchandise0);
                }


            }
        }
        map.put("pdCustomerProductOuterSn", pdMerchandises);

        ObjectToJson.objectToJson1(map, new String[]{"pdPackHashSet", "model", "unitPrice", "netWeight", "mtStock", "productName", "specifications", "minimumiStock", "pdPackHashSet", "innerSn", "customerName", "minimumStock", "miniPackingMode", "miniPackingMaterial", "miniPackingAmount", "outerPackingMode"
                , "outerPackingMaterial", "outerPackingAmount", "taxRate", "unitPriceNotax", "contractSn", "memo", "creator", "createName"
                , "createDate", "updator", "updateName", "updateDate", "product", "product_", "customer", "slOrdersHashSet"
                , "customer_", "TPdMerchandiseHistoryPdMerchandiseViaProductCustomer", "slOrdersItemHashSet"}, response);

    }

    /**
     * @Author:林成
     * @Date:2017/8/2 14:44
     * @description:新增入库申请
     */
    @ResponseBody
    @RequestMapping("/addMtStock.do")
    public void addMtStock(HttpServletRequest request, User user, HttpServletResponse response) throws ParseException, IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        //判断是否需要校验
        ApprovalItem approvalItem = roleService.getCurrentItem(user.getOid(),"productInCheck");
        String pdProductList = request.getParameter("save_pdProduct");
        if (pdProductList != null) {

            JSONArray pdProduct = JSONArray.fromObject(pdProductList);
            MtMaterialApply mtMaterialApply = new MtMaterialApply();
            mtMaterialApply.setApplicant(user.getUserID());//申请人ID
            if (user.getUserName() != null) {
                mtMaterialApply.setApplicantName(user.getUserName());//申请人姓名
            }
            mtMaterialApply.setApplyDate(new Date());//申请日期
            mtMaterialApply.setState("1");//入库状态：1-录入，2-提交，3审批通过，4-审批否决（待处理/待入库/已入库/已驳回）
            mtMaterialApply.setType("1");//类型:1-正常审批,2-让步审批,3-重复审批
            mtMaterialApply.setCreateDate(new Date());
            mtMaterialApply.setCreateName(user.getUserName());
            mtMaterialApply.setCreator(user.getUserID());
            mtMaterialApply.setOid(user.getOid());//组织机构

            if(approvalItem!=null&&approvalItem.isEnabled()&&approvalItem.getStatus()==0){
                mtMaterialApply.setState("2");//待入库
            }
            mtMaterialApplyService.addMtMaterialApply(mtMaterialApply);
            for (int i = 0; i < pdProduct.size(); i++) {
                JSONObject jo = JSONObject.fromObject(pdProduct.get(i));
                MtStock mtStock = new MtStock();
                mtStock.setCreateDate(new Date());//创建时间
                mtStock.setCreator(user.getUserID());//创建人id
                mtStock.setCreateName(user.getUserName());//创建人名称
                mtStock.setOperator(user.getUserID());//经手人id
                mtStock.setOperatorName(user.getUserName());//经手人名称
                mtStock.setOid(user.getOid());
                if (StringUtils.isNotBlank(jo.getString("inPlan"))) {
                    mtStock.setInPlan(new BigDecimal(jo.optString("inPlan","0")));//计划入数量
                    mtStock.setInFact(new BigDecimal(jo.optString("inPlan","0")));//实际入数量
                }
                //生产日期
                if (jo.getString("manufactureDate") != null && !jo.getString("manufactureDate").equals("")) {
                    mtStock.setManufactureDate(new SimpleDateFormat("yyyy-MM-dd").parse(jo.getString("manufactureDate")));
                }
                //失效日期
                if (jo.getString("invalidDate") != null && !jo.getString("invalidDate").equals("")) {
                    mtStock.setInvalidDate(new SimpleDateFormat("yyyy-MM-dd").parse(jo.getString("invalidDate")));
                }
                mtStock.setState(approvalItem.getStatus()==1?"1":"2");//入库状态：1-录入，2-提交，3审批通过，4-审批否决（待处理/待入库/已入库/已驳回）
                mtStock.setQualified(approvalItem.getStatus()==0?"1":"");//无需检验时 直接设置为检验通过

                Integer pdProId = jo.getInt("id");
                PdMerchandise pdCus = productService.getByPdCusId(pdProId);

                MtStorage mtStorage = new MtStorage();
                mtStorage.setOrg(user.getOid());
                mtStorage.setCreateTime(new Date());
                mtStorage.setName(pdCus.getOuterName());//名称
                if (pdCus.getMinimumStock() != null) {
                    mtStorage.setVolume(pdCus.getMinimumStock().doubleValue());//容量
                } else {
                    mtStorage.setVolume(0.0D);
                }
                mtStorageService.addMtStorage(mtStorage);

                MtMaterialStock mtMaterialStock = new MtMaterialStock();
                mtMaterialStock.setCreateTime(new Date());
                mtMaterialStock.setMaterial(pdCus.getId());//商品ID
                //mtMaterialStock.setMemo(pdCus.getMemo());
                mtMaterialStock.setMaterial(pdCus.getId());//产品ID
                mtMaterialStock.setStorage(mtStorage.getId());//库位ID
                mtStock.setApplyId(mtMaterialApply.getId());
                mtStock.setProductId(pdCus.getId());
                // pdCus.setMtStock(mtStock.getId());
                //productService.updatePdMerchandise(pdCus);
                mtMaterialStock.setMtStockId(mtStock.getId());//mtStockID
                mtMaterialStockService.addMtMaterialStock(mtMaterialStock);


                if(approvalItem!=null&&approvalItem.isEnabled()&&approvalItem.getStatus()==0){
                    mtStock.setQualified("1");
                    mtStock.setState("2");

                }else{
                    //审批过程
                    ApprovalProcess ap = new ApprovalProcess();
                    ap.setBusiness(mtStock.getId());
                    ap.setLevel(0);
                    ap.setDescription("入库申请");
                    ap.setBusinessType(4);//业务类型  1-财务修改，2- 加班，3-请假 4-入库
                    ap.setUserName(user.getUserName());//审批人
                    ap.setCreateDate(new Date());
                    ap.setToUser(user.getUserID());
                    ap.setToOrg(user.getUserID());
                    ap.setToUserName("申请人");
                    ap.setHandleTime(new Date());
                    ap.setApproveStatus("1");//1 - 合格，4-驳回
                    ap.setApproveMemo("生产");
                    approvalProcessService.saveApprovalProcess(ap);
                }
                mtStockService.saveMtStock(mtStock);

                map.put("mtStock", mtStock);
            }

            map.put("status", 1);
        } else {
            map.put("status", 0);

        }





        ObjectToJson.objectToJson1(map, new String[]{""}, response);

    }

    /**
     * @Author:林成
     * @Date:2017/8/3 10:28
     * @description:入库申请-flag==0,列表-1待处理/2待入库/已入库/已驳回 入库检验列表-flag==1-1待处理/2已合格/4已驳回
     * http://localhost:8888/mtStock/addMtStockIndex.do?currPage=1&pageSize=10&flag=0&state=1
     */
    @ResponseBody
    @RequestMapping("/addMtStockIndex.do")
    public void addMtStockIndex(Integer currPage, Integer flag, Integer pageSize, Integer state, User user, HttpServletResponse response) throws IOException {

        Map<String, Object> map = new HashMap<String, Object>();
        Integer oid = user.getOid();
        
        List<MtMaterialApply> mtMaterialApply = new ArrayList<MtMaterialApply>();
        List<MtStock> mtStockList = new ArrayList<MtStock>();
        if (flag == 0) {//入库申请标志

            if (state == 1) {//1待处理
                mtMaterialApply = mtMaterialApplyService.getMtMaterialApplyLists(oid, state);
            }
            if (state != 1) {
                //2待入库/3已入库/4已驳回

                if (state == 2) {
                    mtStockList = mtStockService.getMtStockByQualified(oid, "1");
                }
                if (state == 3) {
                    mtStockList = mtStockService.getMtStockAcceptance(oid, "1", "3");
                }
                if (state == 4) {
                    mtStockList = mtStockService.getMtStockByQualified1(oid, "0");//入库申请已驳回显示 不合格
                }


                if (mtStockList.size() > 0) {
                    for (MtStock mtStock : mtStockList) {
                        PdMerchandise pdMerchandise = productService.getByMtStock0(mtStock.getProductId());
                        if (pdMerchandise != null) {
                            PdBase pdBase = mtStockService.getPdBaseById(pdMerchandise.getProduct_());
                            mtStock.setInnerSn(pdBase.getInnerSn());
                            mtStock.setInnerSnName(pdBase.getName());
                            mtStock.setUnit(pdBase.getUnit());
                            mtStock.setOuterSn(pdMerchandise.getOuterSn());
                            mtStock.setOuterName(pdMerchandise.getOuterName());
                        }
                        List<ApprovalProcess> approvalProcesses = approvalProcessService.getApprovalProcessByBusiness(mtStock.getId(), 4, null);
                        mtStock.setApprocessList(approvalProcesses);


                    }
                }


            }

        }

        if (flag == 1) {//入库检查标志

            if (state == 1) {//待处理
                mtMaterialApply = mtMaterialApplyService.getMtMaterialApplyLists(oid, 1);
            }

            if (state == 2) {//已合格
                mtStockList = mtStockService.getMtStockByQualified2(oid, "1");
                if (mtStockList.size() > 0) {
                    for (MtStock mtStock : mtStockList) {
                        PdMerchandise pdMerchandise = productService.getByMtStock0(mtStock.getProductId());
                        if (pdMerchandise != null) {
                            PdBase pdBase = mtStockService.getPdBaseById(pdMerchandise.getProduct_());
                            mtStock.setInnerSn(pdBase.getInnerSn());
                            mtStock.setInnerSnName(pdBase.getName());
                            mtStock.setUnit(pdBase.getUnit());
                            mtStock.setOuterSn(pdMerchandise.getOuterSn());
                            mtStock.setOuterName(pdMerchandise.getOuterName());
                        }
                        List<ApprovalProcess> approvalProcesses = approvalProcessService.getApprovalProcessByBusiness(mtStock.getId(), 4, null);
                        mtStock.setApprocessList(approvalProcesses);
                    }
                }

            }
            if (state == 4) {//已驳回

                mtStockList = mtStockService.getMtStockByQualified1(oid, "0");
                if (mtStockList.size() > 0) {
                    for (MtStock mtStock : mtStockList) {
                        PdMerchandise pdMerchandise = productService.getByMtStock0(mtStock.getProductId());
                        if (pdMerchandise != null) {
                            PdBase pdBase = mtStockService.getPdBaseById(pdMerchandise.getProduct_());
                            mtStock.setInnerSn(pdBase.getInnerSn());
                            mtStock.setInnerSnName(pdBase.getName());
                            mtStock.setUnit(pdBase.getUnit());
                            mtStock.setOuterSn(pdMerchandise.getOuterSn());
                            mtStock.setOuterName(pdMerchandise.getOuterName());

                        }
                        List<ApprovalProcess> approvalProcesses = approvalProcessService.getApprovalProcessByBusiness(mtStock.getId(), 4, null);
                        mtStock.setApprocessList(approvalProcesses);


                    }
                }

            }
        }


        if (currPage == null || "".equals(currPage) || currPage <= 0) {
            currPage = 1;//默认当前页
        }
        int totalPage = 1;//默认总页数
        if (pageSize == null || "".equals(pageSize) || pageSize <= 0) {
            if (flag == 1) {
                if (state != 1) {
                    map.put("mtMaterialApply", mtStockList);

                }
                if (state == 1) {
                    map.put("mtMaterialApply", mtMaterialApply);
                }

            }
            if (flag == 0) {
                if (state == 1) {
                    map.put("mtMaterialApply", mtMaterialApply);
                }


                if (state == 2 || state == 4) {
                    map.put("mtMaterialApply", mtStockList);
                }

            }

            map.put("currPage", currPage);
            map.put("totalPage", totalPage);
            ObjectToJson.objectToJson1(map, new String[]{"fromOrg", "fromUser", "toOrg", "toRole", "toPost", "toUser", "description", "isMessage", "messageTemplate", "handleTime", "business", "oldId", "businessType"
                    , "newId", "finishId", "updateDes", "askName", "type", "approvalFlow", "approvalFlow_", "reimburse", "reimburse_", "personnelLeave", "personnelOvertime", "applyId", "outPlan", "inspectionNo", "material", "materialName", "operator", "operatorName", "orderId", "orderNo", "outFact", "source", "storageLocation", "storageNo", "unitPrice", "destination", "billNo", "accountNo", "instance", "type", "applicant", "parent", "action", "applyType", "quantityPlan"
                    , "quantityFact", "memo", "description", "creator", "updator", "updateName", "updateDate"}, response);


            return;
        }

        int maxResult = currPage * pageSize;//显示最大结果
        int minResult = (currPage - 1) * pageSize;//初始化结果
        int total = 0;
        if (flag == 0) {
            if (state != 1) {
                total = mtStockList.size();
            }
            if (state == 1) {
                total = mtMaterialApply.size();
            }
        }
        if (flag == 1) {
            if (state == 1) {
                total = mtMaterialApply.size();
            }
            if (state == 2 || state == 4) {
                total = mtStockList.size();
            }
        }
        /*
         * 计算总页数
         * */
        double total1 = total;
        double pageSize1 = pageSize;
        double num = total1 / pageSize1;
        double totalPage1 = Math.ceil(num);
        totalPage = (int) totalPage1;
        if (total > 0) {
            List list = new ArrayList();
            for (int i = minResult; i < maxResult; i++) {
                if (i < total) {
                    if (flag == 0) {

                        if (state != 1) {

                            list.add(mtStockList.get(i));
                        }
                        if (state == 1) {
                            list.add(mtMaterialApply.get(i));
                        }

                    }
                    if (flag == 1) {
                        if (state == 1) {
                            list.add(mtMaterialApply.get(i));
                        }
                        if (state == 2 || state == 4) {
                            list.add(mtStockList.get(i));
                        }

                    }
                }
            }

            map.put("mtMaterialApply", list);
            map.put("currPage", currPage);
            map.put("totalPage", totalPage);
            ObjectToJson.objectToJson1(map, new String[]{"fromOrg", "fromUser", "toOrg", "toRole", "toPost", "toUser", "description", "isMessage", "messageTemplate", "handleTime", "business", "oldId", "businessType"
                    , "newId", "finishId", "updateDes", "askName", "type", "approvalFlow", "approvalFlow_", "reimburse", "reimburse_", "personnelLeave", "personnelOvertime", "applyId", "outPlan", "inspectionNo", "material", "materialName", "operator", "operatorName", "orderId", "orderNo", "outFact", "source", "storageLocation", "storageNo", "unitPrice", "destination", "billNo", "accountNo", "instance", "type", "applicant", "parent", "action", "applyType", "quantityPlan"
                    , "quantityFact", "memo", "description", "creator", "updator", "updateName", "updateDate"}, response);

            return;
        } else {
            totalPage = 1;
            if (flag == 1) {
                if (state != 1) {
                    map.put("mtMaterialApply", mtStockList);

                }
                if (state == 1) {
                    map.put("mtMaterialApply", mtMaterialApply);
                }

            }
            if (flag == 0) {
                if (state == 1) {
                    map.put("mtMaterialApply", mtMaterialApply);
                }


                if (state == 2 || state == 4) {
                    map.put("mtMaterialApply", mtStockList);
                }

            }
            map.put("currPage", currPage);
            map.put("totalPage", totalPage);
            ObjectToJson.objectToJson1(map, new String[]{"fromOrg", "fromUser", "toOrg", "toRole", "toPost", "toUser", "description", "isMessage", "messageTemplate", "handleTime", "business", "oldId", "businessType"
                    , "newId", "finishId", "updateDes", "askName", "type", "approvalFlow", "approvalFlow_", "reimburse", "reimburse_", "personnelLeave", "personnelOvertime", "applyId", "outPlan", "inspectionNo", "material", "materialName", "operator", "operatorName", "orderId", "orderNo", "outFact", "source", "storageLocation", "storageNo", "unitPrice", "destination", "billNo", "accountNo", "instance", "type", "applicant", "parent", "action", "applyType", "quantityPlan"
                    , "quantityFact", "memo", "description", "creator", "updator", "updateName", "updateDate"}, response);

            return;
        }

    }

    /**
     * @Author:林成
     * @Date:2017/8/3 11:17
     * @description:入库申请查看/入库检查查看(待处理)
     */

    @ResponseBody
    @RequestMapping("/getMtStockDetail.do")
    public void getMtStockDetail(Integer ID, User user, HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        Integer oid = user.getOid();
        
        //MtMaterialApply mtMaterialApply = mtMaterialApplyService.getById(ID, oid);
        List<MtStock> mtStockList = mtStockService.getByMtMaterialApply(oid, ID);
        PdMerchandise pdMerchandises = null;
        if (mtStockList.size() > 0) {

            for (MtStock mtStock : mtStockList) {
                pdMerchandises = productService.getByMtStock0(mtStock.getProductId());
                if (pdMerchandises != null) {
                    PdBase pdBase = mtStockService.getPdBaseById(pdMerchandises.getProduct_());
                    mtStock.setInnerSn(pdBase.getInnerSn());
                    mtStock.setInnerSnName(pdBase.getName());
                    mtStock.setUnit(pdBase.getUnit());
                    mtStock.setOuterSn(pdMerchandises.getOuterSn());
                    mtStock.setOuterName(pdMerchandises.getOuterName());
                    List<ApprovalProcess> approvalProcesses = approvalProcessService.getApprovalProcessByBusiness(mtStock.getId(), 4, null);
                    mtStock.setApprocessList(approvalProcesses);
                }
            }

            map.put("mtStockList", mtStockList);
        }


        ObjectToJson.objectToJson1(map, new String[]{"fromOrg", "fromUser", "toOrg", "toRole", "toPost", "toUser", "description", "isMessage", "messageTemplate", "handleTime", "business", "oldId", "businessType"
                , "newId", "finishId", "updateDes", "askName", "type", "approvalFlow", "approvalFlow_", "reimburse", "reimburse_", "personnelLeave", "personnelOvertime", "applyId", "outPlan", "inspectionNo", "material", "materialName", "operator", "operatorName", "orderId", "orderNo", "outFact", "source", "storageLocation", "storageNo", "unitPrice", "destination", "billNo", "accountNo", "instance", "type", "applicant", "parent", "reason", "action", "applyType", "quantityPlan"
                , "quantityFact", "memo", "description", "creator", "updator", "updateName", "updateDate"}, response);
    }


    /**
     * @Author:林成
     * @Date:2017/8/3 14:43
     * @description:入库检验列表-合格、不合格、驳回理由
     */
    @ResponseBody
    @RequestMapping("/mtStockCheckout.do")
    public void mtStockCheckout(HttpServletRequest request, User user, HttpServletResponse response) throws IOException {

        Map<String, Object> map = new HashMap<String, Object>();
        Integer oid = user.getOid();
        
        String qualifiedList = request.getParameter("save_qualified");
        String applyId = request.getParameter("query_applyId");
        JSONArray jApplyId = JSONArray.fromObject(applyId);
        JSONObject joid = JSONObject.fromObject(jApplyId.get(0));
        MtMaterialApply mtMaterialApply = mtMaterialApplyService.getById(Integer.valueOf(joid.getString("ID")), oid);
        if (qualifiedList != null) {
            JSONArray jsonQualifiedList = JSONArray.fromObject(qualifiedList);
            for (int i = 0; i < jsonQualifiedList.size(); i++) {
                JSONObject jo = JSONObject.fromObject(jsonQualifiedList.get(i));

                MtStock mtStock = mtStockService.getByIdAndOid(Integer.valueOf(jo.getString("mtStockId")), oid);
                if (jo.getString("qualified") != null && !jo.getString("qualified").equals("")) {
                    mtStock.setQualified(jo.getString("qualified"));//合格:0-不合格,1-合格
                }

                if (jo.getString("qualified").equals("0")) {//不合格
                    mtStock.setState("4");//入库状态：1-录入，2-提交，3审批通过，4-审批否决（待处理/待入库/已入库/已驳回）

                }
                if (jo.getString("qualified").equals("1")) {//合格
                    mtStock.setState("2");
                }

                mtStockService.updateMtStock(mtStock);

                //审批过程
                ApprovalProcess ap = new ApprovalProcess();
                ap.setBusiness(mtStock.getId());
                ap.setLevel(1);
                ap.setDescription("入库检验");
                ap.setBusinessType(4);//业务类型  1-财务修改，2- 加班，3-请假 4-入库
                ap.setUserName(user.getUserName());//审批人
                ap.setCreateDate(new Date());
                ap.setToUser(user.getUserID());
                ap.setToOrg(oid);
                ap.setToUserName("质检人");
                ap.setApproveMemo("检验");
                ap.setHandleTime(new Date());

                if (jo.getString("qualified").equals("0")) {//不合格
                    ap.setApproveStatus("4");//1 - 合格，4-驳回
                    if (jo.getString("reason") != null && !jo.getString("reason").equals("")) {
                        ap.setReason(jo.getString("reason"));
                    }
                }
                if (jo.getString("qualified").equals("1")) {//合格
                    ap.setApproveStatus("1");//1 - 合格，4-驳回
                }
                approvalProcessService.saveApprovalProcess(ap);


            }


        }
        mtMaterialApply.setState("2");//待入库
        mtMaterialApplyService.updateMtMaterialApply(mtMaterialApply);


        map.put("status", 1);
        ObjectToJson.objectToJson1(map, new String[]{""}, response);
    }

    /**
     * @Author:林成
     * @Date:2017/8/14 11:20
     * @description:入库受理--列表
     */
    @ResponseBody
    @RequestMapping("/mtStockAcceptance.do")
    public void mtStockAcceptance(Integer currPage, Integer flag, Integer pageSize, User user, HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        Integer oid = user.getOid();
        
        List<MtStock> mtStockList = new ArrayList<MtStock>();
        if (flag == 0) {//入库受理待处理
            mtStockList = mtStockService.getMtStockAcceptance(oid, "1", "2");//入库受理待处理
        }
        if (flag == 1) {//入库受理--已入库
            mtStockList = mtStockService.getMtStockAcceptance(oid, "1", "3");
        }

        if (mtStockList.size() > 0) {
            for (MtStock mtStock : mtStockList) {
                PdMerchandise pdMerchandise = productService.getByMtStock0(mtStock.getProductId());
                if (pdMerchandise != null) {
                    PdBase pdBase = mtStockService.getPdBaseById(pdMerchandise.getProduct_());
                    mtStock.setInnerSn(pdBase.getInnerSn());
                    mtStock.setInnerSnName(pdBase.getName());
                    mtStock.setUnit(pdBase.getUnit());
                    mtStock.setOuterSn(pdMerchandise.getOuterSn());
                    mtStock.setOuterName(pdMerchandise.getOuterName());

                }
                List<ApprovalProcess> approvalProcesses = approvalProcessService.getApprovalProcessByBusiness(mtStock.getId(), 4, null);
                mtStock.setApprocessList(approvalProcesses);
            }
        }

        if (currPage == null || "".equals(currPage) || currPage <= 0) {
            currPage = 1;//默认当前页
        }
        int totalPage = 1;//默认总页数
        if (pageSize == null || "".equals(pageSize) || pageSize <= 0) {

            map.put("mtStockList", mtStockList);
            map.put("currPage", currPage);
            map.put("totalPage", totalPage);
            ObjectToJson.objectToJson1(map, new String[]{"fromOrg", "fromUser", "toOrg", "toRole", "toPost", "toUser", "description", "isMessage", "messageTemplate", "handleTime", "business", "oldId", "businessType"
                    , "newId", "finishId", "updateDes", "askName", "type", "approvalFlow", "approvalFlow_", "reimburse", "reimburse_", "personnelLeave", "personnelOvertime", "applyId", "outPlan", "inspectionNo", "material", "materialName", "operator", "operatorName", "orderId", "orderNo", "outFact", "source", "storageLocation", "storageNo", "unitPrice", "destination", "billNo", "accountNo", "instance", "type", "applicant", "parent", "reason", "action", "applyType", "quantityPlan"
                    , "quantityFact", "memo", "description", "creator", "createName", "updator", "updateName", "updateDate", "", "", "", ""}, response);


        }

        int maxResult = currPage * pageSize;//显示最大结果
        int minResult = (currPage - 1) * pageSize;//初始化结果
        int total = 0;
        total = mtStockList.size();
        double total1 = total;
        double pageSize1 = pageSize;
        // double num = total1 / pageSize1;
        double totalPage1 = Math.ceil(total1 / pageSize1);
        totalPage = (int) totalPage1;
        if (total > 0) {
            List list = new ArrayList();
            for (int i = minResult; i < maxResult; i++) {
                if (i < total) {
                    list.add(mtStockList.get(i));
                }
            }

            map.put("mtStockList", list);
            map.put("currPage", currPage);
            map.put("totalPage", totalPage);
            ObjectToJson.objectToJson1(map, new String[]{"fromOrg", "fromUser", "toOrg", "toRole", "toPost", "toUser", "description", "isMessage", "messageTemplate", "handleTime", "business", "oldId", "businessType"
                    , "newId", "finishId", "updateDes", "askName", "type", "approvalFlow", "approvalFlow_", "reimburse", "reimburse_", "personnelLeave", "personnelOvertime", "applyId", "outPlan", "inspectionNo", "material", "materialName", "operator", "operatorName", "orderId", "orderNo", "outFact", "source", "storageLocation", "storageNo", "unitPrice", "destination", "billNo", "accountNo", "instance", "type", "applicant", "parent", "reason", "action", "applyType", "quantityPlan"
                    , "quantityFact", "memo", "description", "creator", "createName", "updator", "updateName", "updateDate"}, response);

            return;
        } else {
            totalPage = 1;
            map.put("mtStockList", mtStockList);
        }
        map.put("currPage", currPage);
        map.put("totalPage", totalPage);
        ObjectToJson.objectToJson1(map, new String[]{"fromOrg", "fromUser", "toOrg", "toRole", "toPost", "toUser", "description", "isMessage", "messageTemplate", "handleTime", "business", "oldId", "businessType"
                , "newId", "finishId", "updateDes", "askName", "type", "approvalFlow", "approvalFlow_", "reimburse", "reimburse_", "personnelLeave", "personnelOvertime", "applyId", "outPlan", "inspectionNo", "material", "materialName", "operator", "operatorName", "orderId", "orderNo", "outFact", "source", "storageLocation", "storageNo", "unitPrice", "destination", "billNo", "accountNo", "instance", "type", "applicant", "parent", "reason", "action", "applyType", "quantityPlan"
                , "quantityFact", "memo", "description", "creator", "createName", "updator", "updateName", "updateDate"}, response);

        return;

    }

    /**
     * @Author:林成
     * @Date:2017/8/14 15:23
     * @description:确认修改--入库数量 http://localhost:8888/message/getAllMessage.do?status=1
     * http://localhost:8888/mtStock/updateMtStockCount.do?ID=6&flag=1&InFact=2
     * http://localhost:8888/mtStock/mtStockAcceptance.do?currPage=1&pageSize=10&flag=1
     * http://localhost:8888/mtStock/getMtStockDetail.do?ID=1
     * http://localhost:8888/mtStock/addMtStockIndex.do?currPage=1&pageSize=10&flag=1&state=2
     */
    @ResponseBody
    @RequestMapping("/updateMtStockCount.do")
    public void updateMtStockCount(User user, Integer flag, HttpServletResponse response, Integer InFact, Integer ID) throws IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        Integer oid = user.getOid();
        
        MtStock mtStock = mtStockService.getByIdAndOid(ID, oid);
        if (mtStock != null) {
            List<UserMessage> userMessageList = userMessageService.getManyMessageByMtStock(mtStock.getId());


            if (flag == 0) {//数量无误
                if (userMessageList.size() > 0) {//已经提交修改
                    for (UserMessage userMessage : userMessageList) {
                        userMessageService.deleteMessage(userMessage);
                    }
                }
                mtStock.setState("3");//3已入库
                mtStock.setUpdateName(user.getUserName());
                mtStock.setUpdateDate(new Date());
                mtStock.setUpdator(user.getUserID());
                mtStockService.updateMtStock(mtStock);

                //增加可选库存

                PdMerchandise product = productService.getByPdCusId(mtStock.getProductId());



                if (product != null) {
                    if(product.getAvailableStock()==null){
                        product.setAvailableStock(mtStock.getInFact());
                    }else{
                        product.setAvailableStock(product.getAvailableStock().add(mtStock.getInFact()));
                    }

                    if(product.getCurrentStock()==null){
                        product.setCurrentStock(mtStock.getInFact());
                    }else{
                        product.setCurrentStock(product.getCurrentStock().add(mtStock.getInFact()));
                    }

                    productService.updatePdMerchandise(product);
                }

                //增加可选库存后重新计算可发货比率
                percentUtil.percent(null, oid);
                //更新商品入库实际数量--暂时不做
                //  mtStock.getInFact();//实际数量
                //审批过程
                ApprovalProcess ap = new ApprovalProcess();
                ap.setBusiness(mtStock.getId());
                ap.setLevel(2);
                ap.setDescription("入库受理");
                ap.setBusinessType(4);//业务类型  1-财务修改，2- 加班，3-请假 4-入库
                ap.setUserName(user.getUserName());//审批人
                ap.setCreateDate(new Date());
                ap.setToUser(user.getUserID());
                ap.setToOrg(oid);
                ap.setToUserName("库管员");
                ap.setHandleTime(new Date());
                ap.setApproveStatus("1");//1 - 合格，4-驳回
                ap.setApproveMemo("库管");
                approvalProcessService.saveApprovalProcess(ap);
            }

            if (flag == 1) {//数量不对，发送消息
                UserMessage userMessage = new UserMessage();
                userMessage.setUser(user);
                userMessage.setApprovalStatus(1);//审批状态：0-未申请 ，1-申请提交，2-通过审核，3-否决审核
                userMessage.setHandleId(user.getUserID().toString());
                userMessage.setEventType("仓库修正");
                userMessage.setIllustrate("入库商品数量修正");//操作说明（申请事件）
                userMessage.setMessageType("5");////消息类型  1-财务  2-加班  3-请假   4-报销  5-仓库修正
                userMessage.setState(1);//1-未处理  2-已处理
                userMessage.setReceiveUserId(mtStock.getCreator());  //接收消息人
                userMessage.setCreateDate(new Date());
                userMessage.setMtStockId(mtStock.getId());
                userMessage.setTitle(InFact.toString());
                userMessageService.addUserMassage(userMessage);
            }
            map.put("status", 1);
            //map.put("mtStock",mtStock);
            ObjectToJson.objectToJson1(map, new String[]{""}, response);
            return;
        } else {
            map.put("status", 0);
            ObjectToJson.objectToJson1(map, new String[]{""}, response);
            return;
        }

    }

    /**
     * @Author:林成
     * @Date:2017/8/15 16:47
     * @description:入库之消息确定
     */
    @ResponseBody
    @RequestMapping("/messageEnter.do")
        public void messageEnter(Integer userMessageId, User user, BigDecimal InFact, HttpServletResponse response, Integer ID) throws IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        Integer oid = user.getOid();
        
        MtStock mtStock = mtStockService.getById(ID);
        UserMessage userMessage = userMessageService.getUserMessageById(userMessageId);
        userMessage.setState(2);  //对消息的处理  1-未处理 2-已处理
        userMessage.setApprovalStatus(2);//审批状态：0-未申请 ，1-申请提交，2-通过审核，3-否决审核
        userMessageService.updateUserMassage(userMessage);  //更新消息提醒的状态

        List<UserMessage> userMessageList = userMessageService.getManyMessageByMtStock(mtStock.getId());
        if (userMessageList.size() > 0) {
            for (UserMessage um : userMessageList) {
                UserMessage userMessage1 = userMessageService.getUserMessageById(um.getId());
                userMessage1.setState(2);
                userMessage.setApprovalStatus(3);//审批状态：0-未申请 ，1-申请提交，2-通过审核，3-否决审核
                userMessageService.updateUserMassage(userMessage);  //更新消息提醒的状态
            }
        }

        mtStock.setState("3");//3已入库
        mtStock.setInFact(InFact);//实际数量
        mtStock.setUpdateName(user.getUserName());
        mtStock.setUpdateDate(new Date());
        mtStock.setUpdator(user.getUserID());
        mtStockService.updateMtStock(mtStock);


        //增加可选库存

        PdMerchandise product = productService.getByPdCusId(mtStock.getProductId());
        if (product != null) {
            if(product.getAvailableStock()==null){
                product.setAvailableStock(mtStock.getInFact());
            }else{
                product.setAvailableStock(product.getAvailableStock().add(mtStock.getInFact()));
            }

            if(product.getCurrentStock()==null){
                product.setCurrentStock(mtStock.getInFact());
            }else{
                product.setCurrentStock(product.getCurrentStock().add(mtStock.getInFact()));
            }

            productService.updatePdMerchandise(product);
        }

        //增加可选库存后重新计算可发货比率
        percentUtil.percent(null, oid);

        //审批过程
        ApprovalProcess ap = new ApprovalProcess();
        ap.setBusiness(mtStock.getId());
        ap.setLevel(2);
        ap.setDescription("入库受理");
        ap.setBusinessType(4);//业务类型  1-财务修改，2- 加班，3-请假 4-入库
        ap.setUserName(user.getUserName());//审批人
        ap.setCreateDate(new Date());
        ap.setToUser(user.getUserID());
        ap.setToOrg(oid);
        ap.setToUserName("库管员");
        ap.setHandleTime(new Date());
        ap.setApproveStatus("1");//1 - 合格，4-驳回
        ap.setApproveMemo("库管");
        approvalProcessService.saveApprovalProcess(ap);
        //更新商品入库实际数量--暂时不做

        map.put("status", 1);//确认入库
        ObjectToJson.objectToJson1(map, new String[]{""}, response);
        return;
    }

    /**
     * @Author:林成
     * @Date:2017/8/24 17:06
     * @description:查询入库流程（备用）
     */
    @ResponseBody
    @RequestMapping("/queryApproveProcess.do")
    public void queryApproveProcess(User user, Integer ID, Integer flag, HttpServletResponse response) throws IOException {

        Map<String, Object> map = new HashMap<String, Object>();
        Integer oid = user.getOid();
        
        MtStock mtStock = mtStockService.getByIdAndOid(ID, oid);
        List<ApprovalProcess> approvalProcesses = approvalProcessService.getApprovalProcessByBusiness(mtStock.getId(), 4, null);
        map.put("approvalProcesses", approvalProcesses);
        ObjectToJson.objectToJson1(map, new String[]{"fromOrg", "fromUser", "toOrg", "toRole", "toPost", "toUser", "description", "isMessage", "messageTemplate", "handleTime", "business", "oldId", "businessType"
                , "newId", "finishId", "updateDes", "askName", "type", "approvalFlow", "approvalFlow_", "reimburse", "reimburse_", "personnelLeave", "personnelOvertime", "", "", "", "", ""}, response);

    }
}
