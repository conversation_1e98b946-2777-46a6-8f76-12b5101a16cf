package cn.sphd.miners.modules.personal.service;

import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.PopedomTaskService;
import cn.sphd.miners.modules.system.service.UserService;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Date;

/**
 * Created by Administrator on 2018/9/22.
 */
public interface LeaveOutTimeUtilsService {
    enum ApprovalStatus {//1-待处理 2-已批准(计划) 3-驳回 4-申报批准 9-撤回
        pending("1", (byte)1),
        approved("2", (byte)2),
        rejected("3", (byte)3),
        declaration ("4", (byte)9),
        withdraw ("9", (byte)9);
        private String name;
        private Byte index;

        ApprovalStatus(String name, Byte index) {
            this.name = name;
            this.index = index;
        }

        public String getName() {
            return name;
        }

        public Byte getIndex() {
            return index;
        }
    }
    default Pair<Date, Date>sortDatePair(Pair<Date, Date> source) {
        if(source.getLeft().getTime()>source.getRight().getTime()) {
            source = Pair.of(source.getRight(), source.getLeft());
        }
        return source;
    }
    default void updateApprovalProcessUsersBadgeNumber(PopedomTaskService popedomTaskService, UserService userService, ApprovalProcess approvalProcess) {
        User u;
        if(approvalProcess.getToUser()!=null && (u = userService.getUserByID(approvalProcess.getToUser()))!=null) {
            popedomTaskService.updateUserBadgeNumber(u);
        }
        if(approvalProcess.getFromUser()!=null && (u = userService.getUserByID(approvalProcess.getFromUser()))!=null) {
            popedomTaskService.updateUserBadgeNumber(u);
        }
    }
}
