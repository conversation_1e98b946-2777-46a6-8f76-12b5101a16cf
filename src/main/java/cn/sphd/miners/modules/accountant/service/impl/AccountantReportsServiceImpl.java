package cn.sphd.miners.modules.accountant.service.impl;

import cn.sphd.miners.modules.accountant.entity.SubjectBalanceSheet;
import cn.sphd.miners.modules.accountant.entity.SubjectBalanceStatEntity;
import cn.sphd.miners.modules.accountant.mapper.SubjectBalanceSheetMapper;
import cn.sphd.miners.modules.accountant.service.AccountantReportsService;
import cn.sphd.miners.modules.system.entity.User;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;

/**
 * Created by 刘洪涛 on 17-5-18.
 */
@Service
@Transactional(readOnly=false)
public class AccountantReportsServiceImpl implements AccountantReportsService {

    @Autowired
    SubjectBalanceSheetMapper balanceSheetMapper;

    /*
    * 科目余额表
    * */
    @Override
    public JSONObject getSubBalanSheetByPeroid(User user, String period) {

        SubjectBalanceStatEntity statEntity = new SubjectBalanceStatEntity();

        int oid = user.getOid();
        //为peroid设置默认值
        if(period == null)
        {
            Calendar calendar = Calendar.getInstance();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            period = sdf.format(calendar.getTime());
        }

        SubjectBalanceSheet subBalanSheet = new SubjectBalanceSheet();
        subBalanSheet.setOid(oid);
        subBalanSheet.setPeroid(period);
        List<SubjectBalanceSheet> list = balanceSheetMapper.getSubBalanSheetByPeroid(subBalanSheet);
        for (SubjectBalanceSheet sbs : list)
        {
            String subject = sbs.getSubject();
            if(subject.length() == 4 && !subject.startsWith("2221"))
            {
                statEntity = sumBalance(sbs,statEntity);
            }
            else if (subject.startsWith("2221") && sbs.getMaxChildSubjects() == 0) {
                statEntity = sumBalance(sbs,statEntity);
            }
        }

        JSONObject result = new JSONObject();
        result.put("code", 1);
        result.put("msg", "");

        result.put("sumBeginningBalanceCredit", statEntity.getSumBeginningBalanceCredit());//年初累计借
        result.put("sumBeginningBalanceDebit", statEntity.getSumBeginningBalanceDebit());//年初累计贷
        result.put("sumPreviousBalanceCredit", statEntity.getSumPreviousBalanceCredit());//期初累计借
        result.put("sumPreviousBalanceDebit", statEntity.getSumPreviousBalanceDebit());//期初累计贷
        result.put("sumBalanceCredit", statEntity.getSumBalanceCredit());//期末累计借
        result.put("sumBalanceDebit", statEntity.getSumBalanceDebit());//期末累计贷

        result.put("sumAccumulativeCredit", statEntity.getSumAccumulativeCredit());//本年累计借
        result.put("sumAccumulativeDebit", statEntity.getSumAccumulativeDebit());//本年累计贷
        result.put("sumCredit", statEntity.getSumCredit());//本期累计借
        result.put("sumDebit", statEntity.getSumDebit());//本期累计贷
        result.put("data", list);

        return result;
    }

    /*
     * 计算科目余额表的统计值
     * */
    private SubjectBalanceStatEntity sumBalance(SubjectBalanceSheet sbs,SubjectBalanceStatEntity statEntity) {

        BigDecimal sumBeginningBalanceCredit = statEntity.getSumBeginningBalanceCredit();//年初借
        BigDecimal sumBeginningBalanceDebit = statEntity.getSumBeginningBalanceDebit();//年初贷
        BigDecimal sumPreviousBalanceCredit = statEntity.getSumPreviousBalanceCredit();//期初借
        BigDecimal sumPreviousBalanceDebit = statEntity.getSumPreviousBalanceDebit();//期初贷
        BigDecimal sumCredit = statEntity.getSumCredit();//本期借
        BigDecimal sumDebit = statEntity.getSumDebit();//本期贷
        BigDecimal sumAccumulativeCredit = statEntity.getSumAccumulativeCredit();//本年累计借
        BigDecimal sumAccumulativeDebit = statEntity.getSumAccumulativeDebit();//本年累计贷
        BigDecimal sumBalanceCredit = statEntity.getSumBalanceCredit();//期末借
        BigDecimal sumBalanceDebit = statEntity.getSumBalanceDebit();//期末贷

        String beginningDir = sbs.getBeginningDirection();
        String preDir = sbs.getPreviousDirection();
        String balanceDir = sbs.getBalanceDirection();

        BigDecimal beginBalance = sbs.getBeginningBalance() == null ? BigDecimal.valueOf(0) : sbs.getBeginningBalance();
        BigDecimal preBalance = sbs.getPreviousBalance() == null ? BigDecimal.valueOf(0) : sbs.getPreviousBalance();
        BigDecimal balance = sbs.getBalance() == null ? BigDecimal.valueOf(0) : sbs.getBalance();
        BigDecimal creditAccumulative = sbs.getCreditAccumulative() == null ? BigDecimal.valueOf(0) : sbs.getCreditAccumulative();
        BigDecimal debitAccumulative = sbs.getDebitAccumulative() == null ? BigDecimal.valueOf(0) : sbs.getDebitAccumulative();
        BigDecimal credit = sbs.getCredit() == null ? BigDecimal.valueOf(0) : sbs.getCredit();
        BigDecimal debit = sbs.getDebit() == null ? BigDecimal.valueOf(0) : sbs.getDebit();

        if("1".equals(beginningDir))
        {
            sumBeginningBalanceCredit = sumBeginningBalanceCredit.add(beginBalance);
            statEntity.setSumBeginningBalanceCredit(sumBeginningBalanceCredit);
        }
        if("2".equals(beginningDir))
        {
            sumBeginningBalanceDebit = sumBeginningBalanceDebit.add(beginBalance);
            statEntity.setSumBalanceDebit(sumBeginningBalanceDebit);
        }

        if ("1".equals(preDir))
        {
            sumPreviousBalanceCredit = sumPreviousBalanceCredit.add(preBalance);
            statEntity.setSumPreviousBalanceCredit(sumPreviousBalanceCredit);
        }
        if ("2".equals(preDir))
        {
            sumPreviousBalanceDebit = sumPreviousBalanceDebit.add(preBalance);
            statEntity.setSumPreviousBalanceDebit(sumPreviousBalanceDebit);
        }

        if("1".equals(balanceDir))
        {
            sumBalanceCredit = sumBalanceCredit.add(balance);
            statEntity.setSumBalanceCredit(sumBalanceCredit);
        }
        if ("2".equals(balanceDir))
        {
            sumBalanceDebit = sumBalanceDebit.add(balance);
            statEntity.setSumBalanceDebit(sumBalanceDebit);
        }
        sumAccumulativeCredit = sumAccumulativeCredit.add(creditAccumulative);
        statEntity.setSumAccumulativeCredit(sumAccumulativeCredit);
        sumAccumulativeDebit = sumAccumulativeDebit.add(debitAccumulative);
        statEntity.setSumAccumulativeDebit(sumAccumulativeDebit);
        sumCredit = sumCredit.add(credit);
        statEntity.setSumCredit(sumCredit);
        sumDebit = sumDebit.add(debit);
        statEntity.setSumDebit(sumDebit);

        return statEntity;
    }


}
