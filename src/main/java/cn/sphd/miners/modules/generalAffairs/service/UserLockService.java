package cn.sphd.miners.modules.generalAffairs.service;


import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.generalAffairs.entity.ReqUserLock;
import cn.sphd.miners.modules.generalAffairs.entity.ReqUserObject;
import cn.sphd.miners.modules.generalAffairs.entity.SysUserLock;
import cn.sphd.miners.modules.generalAffairs.entity.UserLock;
import cn.sphd.miners.modules.system.entity.User;

import javax.servlet.http.HttpSession;
import java.util.List;

/**
 * @ClassName UserImportService
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/2811:20
 * @Version 1.0
 */
public interface UserLockService {
    List<User> selectStaffList(int org,PageInfo pageInfo);
    List<User> userLockCurrent(int org);
    List<User> substituteList(int org);
    List<User> selectAllUserForUserId(int userId);
    int determineRoleCode(User user,List<User> list);
    int userLockEnter(User user, ReqUserLock reqUserLock);
    List<UserLock> selectUserLock(int userId);
    List<UserLock> userLockAll(int org,PageInfo pageInfo);
    int updateSubstitute(User user, ReqUserLock reqUserLock);
    int selectMasterUserId(int userId);
    int clearUserLock(User user, int userId);
    void updateLockUserNameByOidMobile(Integer oid, String mobile,String newUserName);
}
