import { createApp } from 'vue'
import App from './App.vue'
import router from './router'

import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import zhCn from 'element-plus/es/locale/lang/zh-cn'

import * as ElementPlusIconsVue from '@element-plus/icons-vue'


import sphdSocket from '@/sys/sphd'

const app=createApp(App).use(router).use(ElementPlus, { locale: zhCn })
parent.sphdSocket = sphdSocket

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}
app.mount('#app')

