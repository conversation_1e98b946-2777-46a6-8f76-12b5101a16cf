package cn.sphd.miners.modules.accountant.service;


import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectRelevance;
import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectRelevanceHistory;
import cn.sphd.miners.modules.finance.entity.AccountDetail;
import cn.sphd.miners.modules.finance.entity.FinanceAccount;
import cn.sphd.miners.modules.finance.entity.FinanceReimburseBill;
import cn.sphd.miners.modules.personal.entity.PersonnelReimburse;
import cn.sphd.miners.modules.system.entity.User;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * Created by lht on 17-1-4.
 */
public interface ReimburseRelevanceService {

    Map<String,Object> getRelevances(Integer org);//返回关联页面的数据

    int setReimburseRelevance(User user, List<TAccountantSubjectRelevance> list);//保存关联页面设置的数据

    List<TAccountantSubjectRelevanceHistory> getCategoryRelevanceHistory(TAccountantSubjectRelevanceHistory param);//返回关联的历史数据

    int autoMatchSubject(Integer oid, PersonnelReimburse personnelReimburse, FinanceAccount financeAccount);//报销两讫后掉用此方法来自动给票据选择科目

    boolean judgeRelevance(Integer org);

    //当查看待选择中报销过来的数据时，返回给前端需要的必要数据，包括已关联的票据和科目、固定的借方科目和固定的贷方科目等
    Map getRelevanceDebitSubject(Integer oid,PersonnelReimburse personnelReimburse);
}
