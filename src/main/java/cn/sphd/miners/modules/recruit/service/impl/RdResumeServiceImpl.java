package cn.sphd.miners.modules.recruit.service.impl;

import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.personal.service.PersonnelInterviewService;
import cn.sphd.miners.modules.recruit.dao.RdInterviewNotificationDao;
import cn.sphd.miners.modules.recruit.dao.RdInterviewerDao;
import cn.sphd.miners.modules.recruit.dao.RdResumeDao;
import cn.sphd.miners.modules.recruit.entity.RdInterviewNotification;
import cn.sphd.miners.modules.recruit.entity.RdInterviewer;
import cn.sphd.miners.modules.recruit.entity.RdNotice;
import cn.sphd.miners.modules.recruit.entity.RdResume;
import cn.sphd.miners.modules.recruit.service.RdResumeService;
import cn.sphd.miners.modules.system.entity.Offer;
import cn.sphd.miners.modules.system.service.OfferService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service("rdResumeService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class RdResumeServiceImpl implements RdResumeService {

    @Autowired
    RdResumeDao rdResumeDao;
    @Autowired
    RdInterviewNotificationDao rdInterviewNotificationDao;
    @Autowired
    RdInterviewerDao rdInterviewerDao;
    @Autowired
    OfferService offerService;
    @Autowired
    RdResumeService rdResumeService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    PersonnelInterviewService personnelInterviewService;

    @Override
    public RdResume getRdResumeByOfferId(Integer offerId) {
        String hql="from RdResume where offer=:offerId";
        Map<String,Object> map=new HashMap<>();
        map.put("offerId",offerId);
        RdResume rdResume= (RdResume) rdResumeDao.getByHQLWithNamedParams(hql,map);
        return rdResume;
    }

    @Override
    public List<RdInterviewNotification> getRdInterviewNotificationsByResumeId(Long resumeId) {
        String hql="from RdInterviewNotification where resumeId=:resumeId";
        Map<String,Object> map=new HashMap<>();
        map.put("resumeId",resumeId);
        List<RdInterviewNotification> rdInterviewNotificationList=rdInterviewNotificationDao.getListByHQLWithNamedParams(hql,map);
        return rdInterviewNotificationList;
    }

    @Override
    public List<RdInterviewer> getRdInterviewersByResumeId(Long resumeId) {
        String hql="from RdInterviewer where resumeId=:resumeId";
        Map<String,Object> map=new HashMap<>();
        map.put("resumeId",resumeId);
        List<RdInterviewer> rdInterviewerList=rdInterviewerDao.getListByHQLWithNamedParams(hql,map);
        return rdInterviewerList;
    }

    @Override
    public void saveRdResume(RdResume rdResume) {
        rdResumeDao.save(rdResume);
    }

    @Override
    public RdResume getRdResumeById(Long id) {
        return rdResumeDao.get(id);
    }

    @Override
    public void saveRdInterviewNotification(RdInterviewNotification rdInterviewNotification) {
        rdInterviewNotificationDao.save(rdInterviewNotification);
    }

    @Override
    public void updateRdInterviewNotification(RdInterviewNotification rdInterviewNotification) {
        rdInterviewNotificationDao.update(rdInterviewNotification);
    }

    @Override
    public RdInterviewNotification getRdInterviewNotificationById(Long id) {
        return rdInterviewNotificationDao.get(id);
    }

    @Override
    public List<RdResume> getRdResumesByNoticeId(Long noticeId,Integer state) {
        String hql="from RdResume where noticeId=:noticeId";
        Map<String,Object> map=new HashMap<>();
        map.put("noticeId",noticeId);
        if (state!=null){
            hql+=" and state>=:state";
            map.put("state",state);//状态:1-已收,2-已通知,3-已面试,4-已中止,5-已入职
        }
        List<RdResume> rdResumeList= rdResumeDao.getListByHQLWithNamedParams(hql, map);

        List<Integer>  offerIdList=new ArrayList<>();

        for (RdResume rdResume:rdResumeList){
           offerIdList.add(rdResume.getOffer());
        }
        Map<Integer,String> imagePathMap=new HashMap<>();
        List<Offer> offerList=offerService.getOfferListByIds(offerIdList,null);
        for (Offer offer:offerList){
            imagePathMap.put(offer.getUserID(),offer.getImgPath());
        }

        for (RdResume rdResume:rdResumeList){
            rdResume.setImgPath(imagePathMap.get(rdResume.getOffer()));

            List<RdInterviewNotification> rdInterviewNotificationList=rdResumeService.getRdInterviewNotificationsByResumeId(rdResume.getId()); //面试通知列表
            List<Integer> userIds=approvalProcessService.getUserIdsByBusiness(rdResume.getOffer(),16);//已经给此应聘者选择过的面试官id
            Integer number=personnelInterviewService.getNumberBySuggestion(rdResume.getOffer(),"1");// 建议入职 的面试官人数
            rdResume.setNotificationNum(rdInterviewNotificationList.size());
            rdResume.setInterviewerNum(userIds.size()>0?userIds.size()-1:0);
            rdResume.setPassNum(number);
        }
        return rdResumeList;
    }

    @Override
    public RdResume getRdResume(Long noticeId,Long noticePostId, String hashKey) {
        String hql="from RdResume where noticeId=:noticeId and hashKey=:hashKey and noticePostId=:noticePostId";
        Map<String,Object> map=new HashMap<>();
        map.put("noticeId",noticeId);
        map.put("hashKey",hashKey);
        map.put("noticePostId",noticePostId);
        RdResume rdResume= (RdResume) rdResumeDao.getByHQLWithNamedParams(hql, map);
        return rdResume;
    }

    @Override
    public List<RdResume> getRdResumesByHashKey(String hashKey) {
        String hql="from RdResume where hashKey=:hashKey";
        Map<String,Object> map=new HashMap<>();
        map.put("hashKey",hashKey);
        List<RdResume> rdResumeList= rdResumeDao.getListByHQLWithNamedParams(hql, map);
        return rdResumeList;
    }

    @Override
    public RdNotice getSumRdResumeByNoticeId(Long noticeId) {
        //状态:1-已收,2-已通知,3-已面试,4-已中止,5-已入职
        String hql="select COALESCE(SUM(CASE WHEN state>=1 THEN 1 ELSE 0 END),0) AS recievedCount,COALESCE(SUM(CASE WHEN state>=2 THEN 1 ELSE 0 END),0) AS noticedCount ,COALESCE(SUM(CASE WHEN state>=3 THEN 1 ELSE 0 END),0) AS interviewedCount ,COALESCE(SUM(CASE WHEN state>=5 THEN 1 ELSE 0 END),0) AS enteredCount FROM RdResume where noticeId=:noticeId and state is not null";
//        hql="select COALESCE(SUM(CASE WHEN state>=1 THEN 1 ELSE 0 END),0) AS recievedCount FROM RdResume where noticeId=:noticeId and state is not null";

        Map<String,Object> map=new HashMap<>();
        map.put("noticeId",noticeId);
        Object[] sum= (Object[]) rdResumeDao.getByHQLWithNamedParams(hql,map);
        RdNotice rdNotice = new RdNotice();
        rdNotice.setRecievedCount(Integer.valueOf(sum[0].toString()));
        rdNotice.setNoticedCount(Integer.valueOf(sum[1].toString()));
        rdNotice.setInterviewedCount(Integer.valueOf(sum[2].toString()));
        rdNotice.setEnteredCount(Integer.valueOf(sum[3].toString()));
        return rdNotice;
    }

    @Override
    public void deleteRdResume(RdResume rdResume) {
        rdResumeDao.delete(rdResume);
    }

    @Override
    public void updateRdResume(RdResume rdResume) {
        rdResume.setUpdateDate(new Date());
        rdResumeDao.update(rdResume);
    }
}
