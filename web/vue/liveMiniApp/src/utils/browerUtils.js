/**
 * 获取浏览器、手机操作系统等方法
 */

export function fullScreenElement(doc) {
    doc = doc ?? document
    console.log('fullScreenElement',doc)
    return doc.fullscreenElement
        || doc.webkitFullscreenElement
        || doc.mozFullScreenElement
        || doc.webkitCurrentFullScreenElement
}
export function getHtmlFontSize(obj) {
    let htmlFontSize = getComputedStyle(obj)['font-size']
    return parseInt(htmlFontSize.slice(0, htmlFontSize.indexOf('px')))
}

export function windowsNtVersion() {
    let ua = navigator.userAgent.toLowerCase();
    const regex = /windows nt (\d+(\.\d+)?)/; // 定义正则表达式，\d+ 表示匹配连续的数字
    let result = ua.match(regex); // match() 函数返回与正则表达式匹配的结果数组
    // console.log(ua,result,result[1]); // 输出第一个匹配到的数字    return /windows/.test(ua) ? true : false;
    return parseFloat(result[1])
}

export function isIe() {
    if(window.navigator.msSaveOrOpenBlob) {
        return true
    }
    let ua = navigator.userAgent.toLowerCase();
    return /ie/.test(ua) ? true : false;
}
export function  isWindows() {
    console.log('navigator.userAgent', navigator.userAgent)
    let ua = navigator.userAgent.toLowerCase();
    return /windows/.test(ua) ? true : false;
}

export function  isFirefox() {
    console.log('navigator.userAgent', navigator.userAgent)
    let ua = navigator.userAgent.toLowerCase();
    return /firefox/.test(ua) ? true : false;
}

export function isWeixinBrowser() {// 判断是否为微信浏览器
    console.log('navigator.userAgent', navigator.userAgent)
    let ua = navigator.userAgent.toLowerCase();
    return /micromessenger/.test(ua) ? true : false;
}

export function isByteDanceBrowser () {
    console.log('navigator.userAgent', navigator.userAgent)
    let ua = navigator.userAgent.toLowerCase();
    return /douyinwebview/.test(ua) || /bytedance/.test(ua) ? true : false;
}

export function isAliBrowser () {
    console.log('navigator.userAgent', navigator.userAgent)
    let ua = navigator.userAgent.toLowerCase();
    return /ali/.test(ua) ? true : false;
}

export function isMeituanBrowser () {
    console.log('navigator.userAgent', navigator.userAgent)
    let ua = navigator.userAgent.toLowerCase();
    return /meituan/.test(ua) ? true : false;
}

export function getDeviceType(){
    console.log("getDeviceType", navigator.userAgent)
    if (isAppleDevice()) {
        console.log('getDeviceType is IOS')
        return "IOS";
    } else if (isHarmonyOS()) {
        console.log('getDeviceType is HarmonyOS')
        return "HarmonyOS";
    } else if (isAndroidDevice()){
        console.log('getDeviceType is Andriod')
        return "Andriod";
    } else {
        console.log('getDeviceType is unkown')
        return "Andriod";
    }
}
// js判断是否是苹果设备
function isAppleDevice() {
    var u = navigator.userAgent
    var ios = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
    var iPad = u.indexOf("iPad") > -1
    var iPhone = u.indexOf("iPhone") > -1 || u.indexOf("Mac") > -1
    if (ios || iPad || iPhone) {
        return true
    } else {
        return false
    }
}
//js判断是否为Android设备
function isAndroidDevice() {
    var u = navigator.userAgent
    if (u.indexOf("Android") > -1 || u.indexOf("Adr") > -1) {
        return true
    } else {
        return false
    }
}
//js判断是否为鸿蒙系统 chos是鸿蒙webview的标识
function isHarmonyOS() {
    var u = navigator.userAgent
    if (u.indexOf("ohos") > -1) {
        return true
    } else {
        return false
    }
}

//判断浏览器类型
export function myBrowser() {
    var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
    var isOpera = userAgent.indexOf("OPR") > -1;
    if (isOpera) {
        return "Opera"
    } //判断是否Opera浏览器 OPR/43.0.2442.991
    //if (userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1 && !isOpera) { return "IE"; }; //判断是否IE浏览器
    if (userAgent.indexOf("Firefox") > -1) { return "FF"; } //判断是否Firefox浏览器  Firefox/51.0
    if (userAgent.indexOf("Trident") > -1 || userAgent.indexOf("MSIE") > -1) { return "IE"; } //判断是否IE浏览器  Trident/7.0; rv:11.0
    if (userAgent.indexOf("Edge") > -1) { return "Edge"; } //判断是否Edge浏览器  Edge/14.14393
    if (userAgent.indexOf("Chrome") > -1) { return "Chrome"; }// Chrome/56.0.2924.87
    if (userAgent.indexOf("Safari") > -1) { return "Safari"; } //判断是否Safari浏览器 AppleWebKit/534.57.2 Version/5.1.7 Safari/534.57.2
}

export function isMiniBrowserOrWebview() {
    return isWeixinBrowser() || isByteDanceBrowser() || isAliBrowser() || isMeituanBrowser()
}