.lev1 .linkBtn{
    float: right;
}
.mainc{display: none}
.lev1{
    padding:20px 0 0 20px;
    line-height: 30px;

}
.mainc0{
    width: 440px;
}
.lev2{
    padding:15px 15px 0 40px;
}
.linkBtn{
    color: #0b94ea;
    cursor: pointer;
}
.linkBtn:hover{
    text-decoration: underline;
}
hr{
    border-top:1px solid #ccc
}


/*minac1*/

.mainc12 td > div:last-child{
     border: none;
 }
.mainc12 .ty-table td {
    padding: 0;
}
.mainc12 .importantTip{
    width: 638px;
    height: 128px;
    border:2px dashed #0099FF;
    padding:20px 40px;
    text-align: center;
    background-color: #fff;
    margin-bottom: 8px;
}
.mainc12 .ty-table .ty-head li{
    border:none;
}
.mainc12 .ty-table .ty-body{
    background: #fff;
}
.mainc12 .ty-table ul{
    width: 100%;
    overflow: hidden;
}
.mainc12 .ty-table ul li{
    float: left;
    border:1px solid #efefef;
    min-height: 40px;
    line-height:40px;
    text-align: center;
    margin-right: -1px;
    height:100%;
    overflow:hidden;
    padding-bottom:9999px;
    margin-bottom:-9999px
}
.mainc12 .ty-table .ty-head li{
    border:none;
}
.mainc12 .ty-table .ty-body{
    background: #fff;
}
.mainc12 .ty-table ul{
    width: 100%;
    overflow: hidden;
}
.mainc12 .ty-table ul li{
    float: left;
    border:1px solid #efefef;
    min-height: 40px;
    line-height:40px;
    text-align: center;
    margin-right: -1px;
    height:100%;
    overflow:hidden;
    padding-bottom:9999px;
    margin-bottom:-9999px
}

/*main3*/
.search > input {
    height: 30px;
    margin-left: 15px;
}
#authList_1_1 tr:nth-child(1) td{ height: 80px; }
#auth_1_1 , #auth_1_2{ float: left;  }
#auth_1_1{ width:10%;   }
#auth_1_2{ min-width:800px; overflow-x: auto; width:82%; border-right:1px solid #ddd;  }
#details{ width: 800px; }
#details .fa-check{ color: #5d9cec;  }
#details .fa-times{ color: #ed5565;  }
#biao{ position: relative; padding: 0; }
.biaotou{  border-top: 35px rgba(0,0,0,0) solid; border-left: 20px rgba(200,200,200,0.5) solid;
    width: 100%;  position: absolute ; top:0 ; ;left:0;  }
.tt1, .tt2{ position: absolute;  }
.tt1{ top:5px; right:5px; }
.tt2{ bottom:5px; left: 5px;    }

.ttl{ width:30%!important; }
.con{ width:50%!important; }

.tiph{ position: relative; margin-top:6px;  }
.tiph>span{ position: absolute; }
.tiph>div{ margin-left: 40px; }

 .bonceCon input,.bonceCon select,.bonceCon textarea {
     border:1px solid #dee8f0;
     background-color: #fff;
     line-height: 36px;
     text-align: left;
     padding: 0 8px;
     color: #3f3f3f;
     width: 180px;
 }
.bonceCon textarea{
    line-height: 20px;
    padding:5px;
}
.bonceCon input{
    height: 36px;
}
.bonceCon input:disabled{
    background-color: #dff0ff;
}
.bonceCon input:focus,.bonceCon select:focus,.bonceCon textarea:focus{
    border:1px solid #5d9cec;
}
.importIntro{
    float: left;
    padding:20px;
    color: #646c74;
    border: 1px dotted #5d9cec;
    width: 32%;
    margin-top: 10px;
    background-color: #fff;
    min-width: 405px ;
    height: 280px;
    margin-right: 8px;
    display: none;
}
.importIntro h3{
    font-weight: bold;
    font-size: 16px;
    color: #5d9cec;
    margin: 5px 0 8px 0;
}
.importIntro p{
    margin: 5px;
    font-size: 14px;
}
.handleTip{
    height: 180px;
}
.userRole{
    font-weight: bold;
    font-size: 16px;
    margin-left: 16px;
}
.ty-colFileTree>ul ul{display: none ; padding-left:15px }
.ty-colFileTree .ty-treeItemActive>i.fa-file{  color: #fff;}
.planIndent{text-indent: 2em;}





