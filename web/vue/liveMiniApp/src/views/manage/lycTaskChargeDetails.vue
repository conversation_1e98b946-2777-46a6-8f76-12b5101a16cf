<template>
  <div class="lycTaskChargeDetails">
    <Pageheader title="歌词任务审批" :showBack="true"/>
    <div class="live-container">
      <div class="txtCenter">
        <div class="ttl">{{ playName }}</div>
        <div>
          {{ playTime }}
        </div>
      </div>
      <div>
        <div class="tr"><span>说明:</span><span>{{ songDetails.inllustration }}</span></div>
        <div class="tr"><span>原唱:</span><span>{{ songDetails.originalPath ? '已选择':'未选择' }}</span></div>
        <div class="tr"><span>伴奏:</span><span>{{ songDetails.bgmPath ? '已选择':'未选择' }}</span></div>
        <div class="tr"><span>第一句:</span><span>{{ songDetails.firstSentence }}</span></div>
        <div class="tr"><span>最后一句:</span><span>{{ songDetails.lastSentence }}</span></div>
        <div class="tr"><span>任务发布:</span><span>{{ songDetails.createName }} {{ new Date(songDetails.createTime).format('yyyy-MM-dd hh:mm:ss') }}</span></div>
      </div>
    </div>
    <div class="summary">
      <p>已有如下{{ listRhythm.length }}份数据</p>
      <div class="bgee">
        请选择一条符合期望的数据，选定后本条任务将消失不见。<br/>
        选定前本条任务不会消失，因为团队成员可能会上传更多数据。
      </div>
    </div>
    <div class="listC">
      <div class="flexC bgc">
        <div>上传</div>
        <div>播放</div>
      </div>
      <div class="flexC" v-for="(item , index) in listRhythm" :key="index">
        <div>{{ item.createName }} {{ new Date(item.createTime).format('yyy-MM-dd hh:mm:ss') }}</div>
        <div>
          <span class="line-link" @click="playLyric(1, item)">原唱</span>
          <span class="line-link" @click="playLyric(2, item)">伴奏</span>
        </div>
      </div>
    </div>

    <LyricPlayer v-if="playLyc" :playName="playName" :lyricTxt="lyricTxt" :fileUrl="fileUrl" @closePlayLyc="closePlayLyc">
      <div class="btmCtrl">
        <p>选用此条数据请在本页操作，否则可关闭本页，以查看其他数据。</p>
        <el-checkbox v-model="checked1" label="选用此条数据！	"></el-checkbox>
        <span class="line-link live-right" @click="selectLyricOk">确定</span>
      </div>
    </LyricPlayer>
  </div>
</template>
<script setup>
import { ref, onMounted, reactive } from 'vue';
import { useRouter } from "vue-router";
import { ElMessageBox, ElMessage } from 'element-plus';
import { lyriTaskApproveMes, selectionRhythm } from '@/api/music.js'
import JSONBig from 'json-bigint'


const router = useRouter();
const playName = ref('')
const lyricTxt = ref('')
const playLyc = ref(false)
const checked1 = ref(false)
const playTime = ref('')
const fileUrl = ref('')
let songDetails = reactive('')
var lockData = {}
let listRhythm = reactive('')
let activeLyc = reactive('')

onMounted(() => {
  getDetails()
});
const selectLyricOk = () => {
  if(checked1.value){
    let data = { rhythmId: activeLyc.id , 'lock': lockData.lock }
    selectionRhythm(data).then(res => {
      console.log('selectionRhythm res=', res)
      // state 1-成功 2-歌词任务已经撤销
      let state = res.data && res.data.state
      if(state === 1){
        ElMessage.success('操作成功')
        router.push({ name: 'lycTaskCharge'});
      }else if(state === 2){
        ElMessage.error('歌词任务已经撤销！')
        router.push({ name: 'lycTaskCharge'});
      }else{
        ElMessage.error('未知错误！')
      }
    }).catch(err => {
      console.log('selectionRhythm err=', err)
      // ElMessage.error('未知错误！')
    })
  }else{
    ElMessage.warning('请先勾选"选用此条数据"！')
  }
}
const playLyric = (type, lycItem) => {
  activeLyc = lycItem
  lyricTxt.value = lycItem.rhythm
  let uploadUrl = localStorage.getItem('uploadUrl');
  if(type === 1){ // 原唱
    fileUrl.value = uploadUrl + songDetails.originalPath
  }else { // 伴奏
    fileUrl.value = uploadUrl + songDetails.bgmPath
  }
  console.log(' fileUrl.value =',  fileUrl.value )
  playLyc.value = true
}
const getDetails = () => {
  let lyricDetailsItem = JSONBig.parse(localStorage.getItem('lycTaskChargeItem'));
  console.log('lyricDetailsItem=', lyricDetailsItem)
  lyriTaskApproveMes({
    'id': lyricDetailsItem.id,
  }).then( res => {
    console.log('详情返回值=', res)
    const data = res.data
    localStorage.setItem('songMessage', JSONBig.stringify(data))
    const song = data.song
    songDetails = song
    console.log(' songDetails.value ',  songDetails )
    listRhythm = data.listRhythm
    playName.value = song.name
    playTime.value = song.songDuration

  }).catch(err => {
    console.log('获取小青失败', err)
  })
}
const getLockData = () => {
  let lyricDetailsItem = JSONBig.parse(localStorage.getItem('lycTaskChargeItem'));
  console.log('lyricDetailsItem=', lyricDetailsItem)
  getSongLock({
    'id': lyricDetailsItem.id,
  }).then( res => {
    console.log('getSongLock=', res)
    lockData = res.data

  }).catch(err => {
    console.log('getSongLock 失败', err)
  })
}
const closePlayLyc = (val)=> {
  console.log('父级的 closePlayLyc')
  playLyc.value = val
  let timer = localStorage.getItem('timer')
  console.log('取消 clearInterval(t', timer)
  clearInterval(timer)
}
// creator：hxz 播放音乐
const playMusic = ()=> {
  playLyc.value = true
}

</script>
<style lang="scss" scoped>
.lycTaskChargeDetails{
  .txtCenter{
    text-align: center;
    margin-bottom: 30px;
  }
  .ttl{
    font-weight: bolder; line-height: 30px;
  }
  .tr{
    line-height: 30px;
    >span:nth-child(1){
      width: 80px;
      display: inline-block;
    }
    >span:nth-child(2){
      font-size: 0.9em;
    }
  }
  .houseSty8{
    position: relative;
    top:3px;
  }
  .bgee{
    background: #eee;
    padding:5px 16px;
    font-size: 0.7em;
    margin-bottom: 10px;
  }
  .summary>p{
    padding:0 16px 1px 16px;
    margin-bottom: 3px;
  }
  .bgc{
    background: #ccc;
  }
  .flexC{
    display: flex;
    text-align: center;

    >div{
      flex: 1;
      line-height: 40px;
    }
    .line-link{
      padding:2px 10px;
    }
  }

  .btmCtrl{ background: #fff;padding:20px 20px 40px 20px; }




}

</style>
<style>
.btmCtrl{}
</style>
