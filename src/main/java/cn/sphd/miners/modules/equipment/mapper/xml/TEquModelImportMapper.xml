<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sphd.miners.modules.equipment.mapper.TEquModelImportMapper">

        <resultMap type="TEquModelImport" id="TEquModelImportResult">
            <result property="id"    column="id"    />
            <result property="org"    column="org"    />
            <result property="imports"    column="imports"    />
            <result property="state"    column="state"    />
            <result property="equipment"    column="equipment"    />
            <result property="equipmentName"    column="equipment_name"    />
            <result property="modelName"    column="model_name"    />
            <result property="modelCode"    column="model_code"    />
            <result property="unitId"    column="unit_id"    />
            <result property="unit"    column="unit"    />
            <result property="category"    column="category"    />
            <result property="supplier"    column="supplier"    />
            <result property="lifeSpan"    column="life_span"    />
            <result property="oLifeSpan"    column="o_life_span"    />
            <result property="originalValue"    column="original_value"    />
            <result property="oValue"    column="o_value"    />
            <result property="receiveDate"    column="receive_date"    />
            <result property="rDate"    column="r_date"    />
            <result property="conditions"    column="conditions"    />
            <result property="memo"    column="memo"    />
            <result property="creator"    column="creator"    />
            <result property="createName"    column="create_name"    />
            <result property="createTime"    column="create_time"    />
            <result property="updator"    column="updator"    />
            <result property="updateName"    column="update_name"    />
            <result property="updateTime"    column="update_time"    />
            <result property="operation"    column="operation"    />
            <result property="previousId"    column="previous_id"    />
            <result property="versionNo"    column="version_no"    />
        </resultMap>

        <sql id="selectTEquModelImportVo">
            select id, org, imports, state, equipment, equipment_name, model_name, model_code, unit_id, unit, category, supplier, life_span, o_life_span, original_value, o_value, receive_date, r_date, conditions, memo, creator, create_name, create_time, updator, update_name, update_time, operation, previous_id, version_no from t_equ_model_import
        </sql>

        <select id="selectTEquModelImportList" parameterType="TEquModelImport" resultMap="TEquModelImportResult">
            <include refid="selectTEquModelImportVo"/>
            <where>
                <if test="org != null "> and org = #{org}</if>
                <if test="imports != null "> and imports = #{imports}</if>
                <if test="state != null "> and state = #{state}</if>
                <if test="equipment != null "> and equipment = #{equipment}</if>
                <if test="equipmentName != null  and equipmentName != ''"> and equipment_name like concat('%', #{equipmentName}, '%')</if>
                <if test="modelName != null  and modelName != ''"> and model_name like concat('%', #{modelName}, '%')</if>
                <if test="modelCode != null  and modelCode != ''"> and model_code = #{modelCode}</if>
                <if test="unitId != null "> and unit_id = #{unitId}</if>
                <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
                <if test="category != null "> and category = #{category}</if>
                <if test="supplier != null "> and supplier = #{supplier}</if>
                <if test="lifeSpan != null "> and life_span = #{lifeSpan}</if>
                <if test="oLifeSpan != null  and oLifeSpan != ''"> and o_life_span = #{oLifeSpan}</if>
                <if test="originalValue != null "> and original_value = #{originalValue}</if>
                <if test="oValue != null  and oValue != ''"> and o_value = #{oValue}</if>
                <if test="receiveDate != null "> and receive_date = #{receiveDate}</if>
                <if test="rDate != null "> and r_date = #{rDate}</if>
                <if test="conditions != null "> and conditions = #{conditions}</if>
                <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
                <if test="creator != null "> and creator = #{creator}</if>
                <if test="createName != null  and createName != ''"> and create_name like concat('%', #{createName}, '%')</if>
                <if test="updator != null "> and updator = #{updator}</if>
                <if test="updateName != null  and updateName != ''"> and update_name like concat('%', #{updateName}, '%')</if>
                <if test="operation != null  and operation != ''"> and operation = #{operation}</if>
                <if test="previousId != null "> and previous_id = #{previousId}</if>
                <if test="versionNo != null "> and version_no = #{versionNo}</if>
            </where>
        </select>

        <select id="selectTEquModelImportById" parameterType="Long" resultMap="TEquModelImportResult">
            <include refid="selectTEquModelImportVo"/>
            where id = #{id}
        </select>

        <insert id="insertTEquModelImport" parameterType="TEquModelImport" useGeneratedKeys="true" keyProperty="id">
            insert into t_equ_model_import
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="org != null">org,</if>
                <if test="imports != null">imports,</if>
                <if test="state != null">state,</if>
                <if test="equipment != null">equipment,</if>
                <if test="equipmentName != null">equipment_name,</if>
                <if test="modelName != null">model_name,</if>
                <if test="modelCode != null">model_code,</if>
                <if test="unitId != null">unit_id,</if>
                <if test="unit != null">unit,</if>
                <if test="category != null">category,</if>
                <if test="supplier != null">supplier,</if>
                <if test="lifeSpan != null">life_span,</if>
                <if test="oLifeSpan != null">o_life_span,</if>
                <if test="originalValue != null">original_value,</if>
                <if test="oValue != null">o_value,</if>
                <if test="receiveDate != null">receive_date,</if>
                <if test="rDate != null">r_date,</if>
                <if test="conditions != null">conditions,</if>
                <if test="memo != null">memo,</if>
                <if test="creator != null">creator,</if>
                <if test="createName != null">create_name,</if>
                <if test="createTime != null">create_time,</if>
                <if test="updator != null">updator,</if>
                <if test="updateName != null">update_name,</if>
                <if test="updateTime != null">update_time,</if>
                <if test="operation != null">operation,</if>
                <if test="previousId != null">previous_id,</if>
                <if test="versionNo != null">version_no,</if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="org != null">#{org},</if>
                <if test="imports != null">#{imports},</if>
                <if test="state != null">#{state},</if>
                <if test="equipment != null">#{equipment},</if>
                <if test="equipmentName != null">#{equipmentName},</if>
                <if test="modelName != null">#{modelName},</if>
                <if test="modelCode != null">#{modelCode},</if>
                <if test="unitId != null">#{unitId},</if>
                <if test="unit != null">#{unit},</if>
                <if test="category != null">#{category},</if>
                <if test="supplier != null">#{supplier},</if>
                <if test="lifeSpan != null">#{lifeSpan},</if>
                <if test="oLifeSpan != null">#{oLifeSpan},</if>
                <if test="originalValue != null">#{originalValue},</if>
                <if test="oValue != null">#{oValue},</if>
                <if test="receiveDate != null">#{receiveDate},</if>
                <if test="rDate != null">#{rDate},</if>
                <if test="conditions != null">#{conditions},</if>
                <if test="memo != null">#{memo},</if>
                <if test="creator != null">#{creator},</if>
                <if test="createName != null">#{createName},</if>
                <if test="createTime != null">#{createTime},</if>
                <if test="updator != null">#{updator},</if>
                <if test="updateName != null">#{updateName},</if>
                <if test="updateTime != null">#{updateTime},</if>
                <if test="operation != null">#{operation},</if>
                <if test="previousId != null">#{previousId},</if>
                <if test="versionNo != null">#{versionNo},</if>
            </trim>
        </insert>

        <update id="updateTEquModelImport" parameterType="TEquModelImport">
            update t_equ_model_import
            <trim prefix="SET" suffixOverrides=",">
                <if test="org != null">org = #{org},</if>
                <if test="imports != null">imports = #{imports},</if>
                <if test="state != null">state = #{state},</if>
                <if test="equipment != null">equipment = #{equipment},</if>
                <if test="equipmentName != null">equipment_name = #{equipmentName},</if>
                <if test="modelName != null">model_name = #{modelName},</if>
                <if test="modelCode != null">model_code = #{modelCode},</if>
                <if test="unitId != null">unit_id = #{unitId},</if>
                <if test="unit != null">unit = #{unit},</if>
                <if test="category != null">category = #{category},</if>
                <if test="supplier != null">supplier = #{supplier},</if>
                <if test="lifeSpan != null">life_span = #{lifeSpan},</if>
                <if test="oLifeSpan != null">o_life_span = #{oLifeSpan},</if>
                <if test="originalValue != null">original_value = #{originalValue},</if>
                <if test="oValue != null">o_value = #{oValue},</if>
                <if test="receiveDate != null">receive_date = #{receiveDate},</if>
                <if test="rDate != null">r_date = #{rDate},</if>
                <if test="conditions != null">conditions = #{conditions},</if>
                <if test="memo != null">memo = #{memo},</if>
                <if test="creator != null">creator = #{creator},</if>
                <if test="createName != null">create_name = #{createName},</if>
                <if test="createTime != null">create_time = #{createTime},</if>
                <if test="updator != null">updator = #{updator},</if>
                <if test="updateName != null">update_name = #{updateName},</if>
                <if test="updateTime != null">update_time = #{updateTime},</if>
                <if test="operation != null">operation = #{operation},</if>
                <if test="previousId != null">previous_id = #{previousId},</if>
                <if test="versionNo != null">version_no = #{versionNo},</if>
            </trim>
            where id = #{id}
        </update>

        <delete id="deleteTEquModelImportById" parameterType="Long">
            delete from t_equ_model_import where id = #{id}
        </delete>

        <delete id="deleteTEquModelImportByIds" parameterType="String">
            delete from t_equ_model_import where id in
            <foreach item="id" collection="array" open="(" separator="," close=")">
                #{id}
            </foreach>
        </delete>
</mapper>