package cn.sphd.miners.modules.material.entity;

import javax.persistence.*;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Entity
@Table(name = "t_po_contract_base")
public class PoContractBase {
    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    @Basic
    @Column(name = "org", nullable = true)
    private Integer org;
    @Basic
    @Column(name = "supplier", nullable = true)
    private Integer supplier;
    @Basic
    @Column(name = "primary_cont", nullable = true)
    private Integer primaryCont;
    @Basic
    @Column(name = "sn", nullable = true, length = 100)
    private String sn;
    @Basic
    @Column(name = "sign_time", nullable = true)
    private Date signTime;
    @Basic
    @Column(name = "valid_start", nullable = true)
    private Date validStart;
    @Basic
    @Column(name = "valid_end", nullable = true)
    private Date validEnd;
    @Basic
    @Column(name = "is_expired", nullable = true)
    private Integer isExpired;
    @Basic
    @Column(name = "file_name", nullable = true, length = 50)
    private String fileName;
    @Basic
    @Column(name = "file_path", nullable = true, length = 255)
    private String filePath;
    @Basic
    @Column(name = "level", nullable = true)
    private Integer level;
    @Basic
    @Column(name = "trace_path", nullable = true, length = -1)
    private String tracePath;
    @Basic
    @Column(name = "material_count", nullable = true)
    private Integer materialCount;
    @Basic
    @Column(name = "memo", nullable = true, length = 255)
    private String memo;
    @Basic
    @Column(name = "keywords", nullable = true, length = 100)
    private String keywords;
    @Basic
    @Column(name = "enabled", nullable = true)
    private Integer enabled;
    @Basic
    @Column(name = "enabled_time", nullable = true)
    private Date enabledTime;
    @Basic
    @Column(name = "creator", nullable = true)
    private Integer creator;
    @Basic
    @Column(name = "create_name", nullable = true, length = 100)
    private String createName;
    @Basic
    @Column(name = "create_date", nullable = true)
    private Date createDate;
    @Basic
    @Column(name = "updator", nullable = true)
    private Integer updator;
    @Basic
    @Column(name = "update_name", nullable = true, length = 100)
    private String updateName;
    @Basic
    @Column(name = "update_date", nullable = true)
    private Date updateDate;
    @Basic
    @Column(name = "operation", nullable = true, length = 1)
    private String operation;
    @Basic
    @Column(name = "previous_id", nullable = true)
    private Integer previousId;
    @Basic
    @Column(name = "version_no", nullable = true)
    private Integer versionNo;
    @Basic
    @Column(name = "is_suspend", nullable = true)
    private Boolean isSuspend;               //暂停履约
    @Basic
    @Column(name = "suspend_time", nullable = true)
    private Date suspendTime;               //收到时间

    //文件列表
    @Transient
    private List contractBaseImages;
    //商品列表
    @Transient
    private List mtList;
    //启停记录
    @Transient
    private List enabledList;
    //启停记录
    @Transient
    private String supplierName;  //供应商全称


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }


    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }



    public Integer getPrimaryCont() {
        return primaryCont;
    }

    public void setPrimaryCont(Integer primaryCont) {
        this.primaryCont = primaryCont;
    }



    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }


    public Date getSignTime() {
        return signTime;
    }

    public void setSignTime(Date signTime) {
        this.signTime = signTime;
    }


    public Date getValidStart() {
        return validStart;
    }

    public void setValidStart(Date validStart) {
        this.validStart = validStart;
    }


    public Date getValidEnd() {
        return validEnd;
    }

    public void setValidEnd(Date validEnd) {
        this.validEnd = validEnd;
    }


    public Integer getIsExpired() {
        return isExpired;
    }

    public void setIsExpired(Integer isExpired) {
        this.isExpired = isExpired;
    }


    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }


    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }


    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }


    public String getTracePath() {
        return tracePath;
    }

    public void setTracePath(String tracePath) {
        this.tracePath = tracePath;
    }


    public Integer getMaterialCount() {
        return materialCount;
    }

    public void setMaterialCount(Integer materialCount) {
        this.materialCount = materialCount;
    }


    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }


    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }


    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }


    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }


    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }


    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }


    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }


    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }


    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }


    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }


    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }


    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }


    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public Integer getSupplier() {
        return supplier;
    }

    public void setSupplier(Integer supplier) {
        this.supplier = supplier;
    }


    public Boolean getSuspend() { return isSuspend;}

    public void setSuspend(Boolean suspend) { isSuspend = suspend; }


    public Date getSuspendTime() { return suspendTime; }

    public void setSuspendTime(Date suspendTime) { this.suspendTime = suspendTime; }

    public List getContractBaseImages() {
        return contractBaseImages;
    }

    public void setContractBaseImages(List contractBaseImages) {
        this.contractBaseImages = contractBaseImages;
    }

    public List getMtList() {
        return mtList;
    }

    public void setMtList(List mtList) {
        this.mtList = mtList;
    }

    public List getEnabledList() {
        return enabledList;
    }

    public void setEnabledList(List enabledList) {
        this.enabledList = enabledList;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PoContractBase that = (PoContractBase) o;
        return id == that.id && Objects.equals(org, that.org) && Objects.equals(sn, that.sn) && Objects.equals(signTime, that.signTime) && Objects.equals(validStart, that.validStart) && Objects.equals(validEnd, that.validEnd) && Objects.equals(isExpired, that.isExpired) && Objects.equals(fileName, that.fileName) && Objects.equals(filePath, that.filePath) && Objects.equals(level, that.level) && Objects.equals(tracePath, that.tracePath) && Objects.equals(materialCount, that.materialCount) && Objects.equals(memo, that.memo) && Objects.equals(keywords, that.keywords) && Objects.equals(enabled, that.enabled) && Objects.equals(enabledTime, that.enabledTime) && Objects.equals(creator, that.creator) && Objects.equals(createName, that.createName) && Objects.equals(createDate, that.createDate) && Objects.equals(updator, that.updator) && Objects.equals(updateName, that.updateName) && Objects.equals(updateDate, that.updateDate) && Objects.equals(operation, that.operation) && Objects.equals(previousId, that.previousId) && Objects.equals(versionNo, that.versionNo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, org, sn, signTime, validStart, validEnd, isExpired, fileName, filePath, level, tracePath, materialCount, memo, keywords, enabled, enabledTime, creator, createName, createDate, updator, updateName, updateDate, operation, previousId, versionNo);
    }
}
