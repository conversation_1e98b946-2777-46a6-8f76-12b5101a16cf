package cn.sphd.miners.modules.finance.service.impl;

import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.finance.dao.FinanceReimburseBillItemDao;
import cn.sphd.miners.modules.finance.entity.FinanceReimburseBillItem;
import cn.sphd.miners.modules.finance.service.FinanceReimburseBillItemService;
import cn.sphd.miners.modules.personal.dao.PersonnelReimburseDao;
import cn.sphd.miners.modules.personal.entity.PersonnelReimburse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by Administrator on 2019/5/13.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class FinanceReimburseBillItemServiceImpl implements FinanceReimburseBillItemService {
    @Autowired
    FinanceReimburseBillItemDao financeReimburseBillItemDao;
    @Autowired
    PersonnelReimburseDao personnelReimburseDao;

    @Override
    public List<FinanceReimburseBillItem> getFinanceReimburseBillItemListByBillId(Integer billId) {
        String hql=" from FinanceReimburseBillItem where billId=:billId";
        Map<String,Object> map=new HashMap<>();
        map.put("billId",billId);
        List<FinanceReimburseBillItem> financeReimburseBillItemList= financeReimburseBillItemDao.getListByHQLWithNamedParams(hql,map);
        return financeReimburseBillItemList;
    }

    @Override
    public List<PersonnelReimburse> myReimburseQueryList(Integer userId,Date begin, Date end, Integer approveStatus, Integer feeCat, Integer billCat,HashMap<String,Object> hashMap,Integer applyId) {
        String hql =" select f.reimburseBill.reimburse from FinanceReimburseBillItem f where f.reimburseBill.reimburse.approveStatus=:approveStatus and :begin<=f.createDate and f.createDate<=:end";
        Map<String,Object> map=new HashMap<>();
        map.put("approveStatus",approveStatus.toString());
        if (approveStatus==5)
            map.put("approveStatus","2");
        if (approveStatus==8)
            map.put("approveStatus","3");

        if (feeCat!=null&&feeCat!=0){
            hql+=" and f.feeCatPath like:feeCat";
            map.put("feeCat","%/"+feeCat+"/%");
        }
        if (billCat!=null&&billCat!=0){
            hql+=" and f.reimburseBill.billCat=:billCat";
            map.put("billCat",billCat);
        }
        if (applyId!=null&&applyId!=0){
            hql+=" and f.reimburseBill.reimburse.user_=:applyId";
            map.put("applyId",applyId);
        }else {
            hql+=" and f.reimburseBill.reimburseId in (select reimburse_ from ApprovalProcess where reimburse_ is not null and toUser=:userId";
            map.put("userId",userId);
            if (approveStatus==5||approveStatus==8){
                hql+=" and approveStatus=:status";
                map.put("status",approveStatus.toString());
            }
            hql+=" )";
        }
        hql+=" group by f.reimburseBill.reimburseId";
        map.put("begin",begin);
        map.put("end",end);
//        String hql="select id from PersonnelReimburse  where approveStatus=:approveStatus and :begin<=createDate and createDate<=:end";
        List<PersonnelReimburse> personnelReimburseList=(List<PersonnelReimburse>)personnelReimburseDao.getListByHQLWithNamedParams(hql,map);
        BigDecimal money=new BigDecimal(0);
        for (PersonnelReimburse personnelReimburse:personnelReimburseList){
            money=money.add(personnelReimburse.getAmount());
        }
        hashMap.put("number",personnelReimburseList.size());
        hashMap.put("money",money);
        return personnelReimburseList;
    }

    @Override    //type 1-报销本流程的查询(申请人/审批人)  2-出纳的查询
    public void myReimburseQuery(Integer userId,Date begin, Date end, Integer approveStatus, Integer feeCat, Integer billCat, HashMap<String, Object> hashMap,Integer applyId,Integer type) {
//        HashMap<String,Object> map=new HashMap<>();
        Date over= NewDateUtils.changeMonth(end, 0);
        over = over.getTime()>begin.getTime() ? over : begin;
        BigDecimal money=new BigDecimal(0);

        Integer number=0;//总条数
        List<HashMap> hashMapList=new ArrayList<>();

        Long terminate=begin.getTime();
        for (;end.getTime()>=terminate;over=NewDateUtils.changeMonth(over, -1),end=NewDateUtils.getLastTimeOfMonth(over)) {
            HashMap<String, Object> map = new HashMap<>();
            if(over.getTime()<terminate){
                over=begin;
            }
            if (1==type) {
                this.myReimburseQueryList(userId, over, end, approveStatus, feeCat, billCat, map, applyId);
            }else if (2==type){
                this.getCashierQuery(userId, over, end, approveStatus, feeCat, billCat, map, applyId);  //两者的区别是有申请人查询时
            }
            map.put("applyDate",new SimpleDateFormat("yyyy年MM月").format(over));//申请时间
            hashMapList.add(map);//每月的统计放到list中
            money=money.add((BigDecimal) map.get("money"));
            number+=(int)map.get("number");
        }
        hashMap.put("number",number);
        hashMap.put("money",money);
        hashMap.put("monthlyList",hashMapList);
    }

    @Override
    public List<PersonnelReimburse> getCashierQuery(Integer userId, Date begin, Date end, Integer approveStatus, Integer feeCat, Integer billCat,HashMap<String,Object> hashMap, Integer applyId) {
        String hql =" select f.reimburseBill.reimburse from FinanceReimburseBillItem f where f.reimburseBill.reimburse.approveStatus=:approveStatus and :begin<=f.createDate and f.createDate<=:end";
        Map<String,Object> map=new HashMap<>();
        map.put("approveStatus",approveStatus.toString());
        if (approveStatus==5)
            map.put("approveStatus","2");
        if (approveStatus==8)
            map.put("approveStatus","3");

        if (feeCat!=null&&feeCat!=0){
            hql+=" and f.feeCatPath like:feeCat";
            map.put("feeCat","%/"+feeCat+"/%");
        }
        if (billCat!=null&&billCat!=0){
            hql+=" and f.reimburseBill.billCat=:billCat";
            map.put("billCat",billCat);
        }
        if (applyId!=null&&applyId!=0){
            hql+=" and f.reimburseBill.reimburse.user_=:applyId";
            map.put("applyId",applyId);
        }
        if (userId!=null&&userId!=0){
//            hql+=" and f.reimburseBill.reimburseId in (select reimburse_ from ApprovalProcess where reimburse_ is not null and businessType is null and toUser=:userId";
            hql+=" and f.reimburseBill.reimburseId in (select reimburse_ from ApprovalProcess where (businessType in(20,21) or (reimburse_ is not null and businessType is null)) and toUser=:userId";
            map.put("userId",userId);
            if (approveStatus==5||approveStatus==8){
                hql+=" and approveStatus=:status";
                map.put("status",approveStatus.toString());
            }
            hql+=" )";
        }
        hql+=" group by f.reimburseBill.reimburseId";
        map.put("begin",begin);
        map.put("end",end);
        List<PersonnelReimburse> personnelReimburseList=(List<PersonnelReimburse>)personnelReimburseDao.getListByHQLWithNamedParams(hql,map);
        BigDecimal money=new BigDecimal(0);
        for (PersonnelReimburse personnelReimburse:personnelReimburseList){
            money=money.add(personnelReimburse.getAmount());
        }
        hashMap.put("number",personnelReimburseList.size());
        hashMap.put("money",money);
        return personnelReimburseList;
    }

}
