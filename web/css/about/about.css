/*扫描下载*/
.scanCon {
    width: 600px;
    text-align: center;
    padding: 30px;
}
.scanCon h3{
    color: #333;
}
.qrCode{
    padding: 20px;
}

.item-row {
    display: flex;
    flex-direction: row;
    padding: 4px 0;
    margin-bottom: 4px;

}
.item-row .item-title{
    margin-right: 8px;
    line-height: 34px;
    min-width: 90px;
}
.item-row .item-content{
    min-width: 350px;
    position: relative;
}
.item-row input, .item-row select {
    border: 1px solid #dcdfe6;
    padding: 6px 12px;
}
.item-row textarea {
    border: 1px solid #dcdfe6;
    line-height: 1.2;
    padding: 6px 12px;
}
.txtApply .item-row .ty-inputText,.txtApply .item-row .ty-inputSelect {
    min-width: 320px;
}
.txtApply .item-row textarea{
    width: 768px;
}

section.txtApply, section.txtContent, section.fileHistory{
    padding: 8px 16px;
    margin-top: 8px;
}
.item-content .clearInput{
    position: absolute;
    top: 2px;
    left: 300px;
    color: #ccc;
    line-height: 30px;
    font-size: 16px;
}
.savePlace{
    color: #444;
    font-size: 13px;
    background-color: #e8e8e8;
    border-radius: 2px;
    padding: 6px 12px;
    border: 1px solid #dcdfe6;
    width: 770px;
}
.savePlace i.fa {
    margin-left: 8px;
    color: #ddc667;
    vertical-align: middle;
}
ul.hel{
    position: absolute;
    width: 84px;
    right: 0;
    top: 25px;
    padding: 2px 0;
    background-color: #fff;
    box-shadow: 0 0 1px #5d9ded;
    z-index: 2;
}
/* 文件查看基本信息 */
.infoCon>div{
    width:385px;
}
.trItem{
    padding:5px 8px;
}
.trItem .ttl, .trItem .ttl2, .trItem .ttl3{
    display: inline-block;
    color: #6e7c8a;
    vertical-align: top;
}
.trItem .ttl{
    width:80px;
    text-align: right;
}
.trItem .ttl2{
    width:70px;
    text-align: right;
}
.trItem .ttl3{
    width:150px;
    text-align: left;
}
.trItem .con{
    display: inline-block;
    word-wrap: break-word;
    word-spacing:normal;
    max-width: 280px;
}
.currentFileName,.currentFileNo{
    word-wrap: break-word;
    word-break:break-all;
}
#docInfoScan {
    width: 690px;
}
.item-header{
    font-size: 14px;
    font-weight: 700;
    padding-left: 5px;
    border-left: 3px solid #ccc;
    margin: 16px 0;
    line-height: 1;
    color: #606266;
}
.info_title{
    font-weight: bold;
    color: #576471;
    font-size: 16px;
}
.censusInfo .times{
    width: 60px;
}
.info_name{
    display: inline-block;
    width: 144px;
    overflow: hidden;
    vertical-align: top;
    height: 20px;
}
.ty-fileNull{
    height: 128px;
    padding-top: 120px;
    text-align: center;
    min-height: 593px;
    color: #aaa;
    font-size: 14px;
}
.ty-fileNull img{
    width: 96px;
}