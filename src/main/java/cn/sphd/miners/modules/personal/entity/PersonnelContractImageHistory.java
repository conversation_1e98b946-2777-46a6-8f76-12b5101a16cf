package cn.sphd.miners.modules.personal.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "t_personnel_contract_image_history")
public class PersonnelContractImageHistory extends BaseEntity {
    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;//id                            bigint not null auto_increment  comment 'ID',
    @Column(name = "org")
    private Integer org;                           //int  comment '机构ID',
    @Column(name = "contract")
    private Long contract;                         //bigint  comment '合同ID',
    @Column(name = "contract_history")
    private Long contractHistory;                  //bigint  comment '合同历史ID',
    @Column(name = "image")
    private Long image;
    @Column(name = "title")
    private String title;                          //varchar(255)  comment '标题',
    @Column(name = "type")
    private Byte type;                             //tinyint  comment '类型:1-图片,2-视频,3-文档',
    @Column(name = "uplaod_path")
    private String uplaodPath;                     //varchar(255)  comment '文件上传路径',
    @Column(name = "orders")
    private Integer orders;                           //int  comment '排序',
    @Column(name = "remark")
    private String remark;                         //varchar(255)  comment '备注',
    @Column(name = "creator")
    private Integer creator;                       //int  comment '创建人id',
    @Column(name = "create_name",length = 100)
    private String createName;                   //varchar(100)  comment '创建人',
    @Column(name = "create_date")
    @CreationTimestamp
    private Date createDate;                   //datetime(3) default null  comment '创建时间',
    @Column(name = "updator")
    private Integer updator;                       //int  comment '修改人id',
    @Column(name = "update_name",length = 100)
    private String updateName;                     //varchar(100)  comment '修改人',
    @Column(name = "update_date")
    @UpdateTimestamp
    private Date updateDate;                   //datetime(3) default null  comment '修改时间',
    @Column(name = "operation")
    private Byte operation;
    @Column(name = "previous_id")
    private Integer previousId;
    @Column(name = "version_no")
    private  Integer versionNo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Long getContract() {
        return contract;
    }

    public void setContract(Long contract) {
        this.contract = contract;
    }

    public Long getContractHistory() {
        return contractHistory;
    }

    public void setContractHistory(Long contractHistory) {
        this.contractHistory = contractHistory;
    }

    public Long getImage() {
        return image;
    }

    public void setImage(Long image) {
        this.image = image;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public String getUplaodPath() {
        return uplaodPath;
    }

    public void setUplaodPath(String uplaodPath) {
        this.uplaodPath = uplaodPath;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Byte getOperation() {
        return operation;
    }

    public void setOperation(Byte operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
}
