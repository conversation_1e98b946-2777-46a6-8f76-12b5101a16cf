<template>
  <div id="openInvoice" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="已终止的申请"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <p class="tipR">本系统仅予保留近30日内的被终止的开票申请，超过者不予保存。</p>
      <div class="ui-cells">
        <a class="ui-cell" v-for="(item, index) in list" v-bind:key="index" @click="jump(item.id)">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <div><span class="info">{{item.name}}</span><span class="info"><span class="wid"></span>申请开票金额 {{item.invoice_amount | numberFilter(2)}}元</span></div>
              <div class="end">终止</div>
              <div><span class="info">{{item.create_name}} {{item.create_date | formatDate}}</span>
                <span class="info"><span class="wid"></span>需要发票数量{{item.invoice_count}}<span v-if="item.invoice_count !== '--'">张</span></span></div>
            </div>
          </div>
          <div class="ui-cell__ft"></div>
        </a>
      </div>
    </div>
  </div>
</template>
<style lang="less">
  #openInvoice{
    .tipR{ color:#ed5565; padding:1rem; }
    .ui-cell_con { position: relative;  }
    .info { display: inline-block;  width: 50%; text-overflow: ellipsis;  white-space: nowrap; font-size:1rem; }
    .wid{ display: inline-block; width: 2rem; }
    .end {
      display: inline-block;
      border:3px solid #B87237;
      background:#F79646;
      border-radius:50%;
      position: absolute;
      left: 46%;
      top:0.7rem;
      color: #fff;
      font-size: 1rem;
      padding: 0.5rem;
    }
  }
</style>
<script>
import { formatDate, numberFilter } from '../../../js/common'
export default {
  name: 'invoiceEnd',
  filters: {
    formatDate,
    numberFilter
  },
  data () {
    return {
      loading: true,
      list: [] // 已终止列表
    }
  },
  computed: {
  },
  created () {
    let _this = this
    _this.listeners = [
      // 终止的开票申请
      _this.sphdSocket.subscribe('endInvoice', _this.subscribeCallBack, null, 'user')
    ]
    _this.getEndList()
  },
  destroyed: function () {
    let _this = this
    _this.listeners.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    jump: function (id) {
      this.$router.push({
        name: `invoiceEndInfo`,
        params: {
          id: id
        }
      })
    },
    subscribeCallBack: function (res) {
      console.log('终止列表回调：', res)
      this.getEndList()
    },
    beforeTab: function (name) {
      this.tabValue = name
    },
    getEndList: function () {
      let _this = this
      // 获取列表
      _this.$http.post('../../../sale/getTerminateApplication.do', {
      }, {
        emulateJSON: true
      }).then((response) => {
        let res = response.body
        _this.list = res.data
        _this.loading = false
      }).catch(function () {
        _this.$message({
          type: 'error',
          message: '系统错误，请重试！'
        })
        this.$router.push({
          path: `/invoiceApply`
        })
      })
    }
  }
}
</script>
