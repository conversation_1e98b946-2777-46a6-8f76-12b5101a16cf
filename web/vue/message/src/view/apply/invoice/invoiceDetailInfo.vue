<template>
  <div id="openInvoice">
    <TY_NavTop title="开票申请" @toggleList="search"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="ui-message">
        <div class="sendContainer">
          <div class="tRi" v-if="info.state==5">
            <p>&nbsp;</p>
            <div><p> {{info.invoice_no}}</p><p> {{info.operate_date | formatDate('yyyy-MM-dd')}}</p></div>
            <div style="text-align: right"><p>发票号码：</p>实际开票日期：</div>
            <p class="clr"></p>
          </div>
          <div><span>客户名称：{{info.name}}</span><span class="wid"></span><br>
            <span> 金额：{{info.invoice_amount | numberFilter(2)}}元</span></div>
          <div><span>发票种类：{{ filterInvoiceType(info.invoice_category) }}</span><br>
            <span v-if="info.invoice_category==1"> 税率：{{list[0]['tax_rate']}}%</span></div>
          <div>开票申请获得批准时，应缴税的参考金额：</div>
          <div>增值税：XXXX.XX元，企业所得税：XXXX.XX元</div>
          <div class="yard gapTop"><span>申请人：{{info.create_name}}</span><span> {{info.create_date | formatDate}}</span></div>
          <div class="yard" v-if="info.state==3||info.state==5||info.state==7"><span>审批人：{{info.update_name}}</span><span> {{info.create_date | formatDate}}</span></div>
          <div class="yard" v-if="info.state==5"><span>开票人：{{info.auditor_name}}</span><span> {{info.audit_date | formatDate}}</span></div>
        </div>
      </div>
      <p style="height: 10px"></p>
      <table class="table">
        <thead>
        <td>商品</td>
        <td>单位</td>
        <td>数量</td>
        <td>单价</td>
        <td>金额</td>
        <td v-if="info.invoice_category==1">税额</td>
        </thead>
        <tbody>
        <tr v-for="(item, index) in list" v-bind:key="index">
          <td>{{item.name}}</td>
          <td>{{item.unit}}</td>
          <td>{{item.item_quantity}}</td>
          <td>{{item.item_price}}</td>
          <td>{{item.item_amount | numberFilter(2)}}</td>
          <td v-if="info.invoice_category==1">{{item.invoice_amount | numberFilter(2)}}</td>
        </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
<style>
  .wid{ display: inline-block; width: 30px; }
  .tRi>div{ float: right; }
  .clr{ clear: both; border-top:1px solid #333;  }
  .gapTop{padding-top: 10px;  }
  .yard{text-align: right;  }
  .yard>span:first-child{display: inline-block;width: 120px;}
</style>
<script>
import { formatDate, numberFilter } from '../../../js/common'
export default {
  name: 'applyInvoiceDetailInfo',
  data () {
    return {
      loading: true,
      list: [],
      listeners: [],
      info: {}
    }
  },
  filters: {
    formatDate,
    numberFilter
  },
  created () {
    let _this = this
    _this.invoiceId = this.$route.params.invoiceId
    _this.applicationId = this.$route.params.id
    _this.listeners = [
      // 开票待审批
      _this.sphdSocket.subscribe('invoiceApply', _this.subscribeCallBack, null, 'user'),
      // 暂缓开票
      _this.sphdSocket.subscribe('reprieve', _this.subscribeCallBack, null, 'user'),
      // 已批准待开票
      _this.sphdSocket.subscribe('applyInvoice', _this.subscribeCallBack, null, 'user'),
      // 已开票未登记
      _this.sphdSocket.subscribe('invoiceRegister', _this.subscribeCallBack, null, 'user'),
      // 填完发票号的回调
      _this.sphdSocket.subscribe('invoiceHandle', _this.handleCallback, null, 'user')
    ]
    // 获取发票详情
    this.getDeatilInfo()
  },
  destroyed: function () {
    let _this = this
    _this.listeners.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    filterInvoiceType:function (value) {
      let type = ''
      switch (value) {
        case '1':
          type = '增值税专用发票'
          break
        case '2':
          type = '增值税普通票/其他普通票'
          break
        case '3':
          type = '增值税普通票/其他普通票'
          break
        default:
          type = ''
      }
      return type
    },
    getDeatilInfo: function () {
      let _this = this
      let way = this.$route.params.way
      let fid = this.$route.params.fid
      let data  = {
        'itemId': _this.invoiceId, 'applicationId': _this.applicationId
      }
      if (fid && fid !== "undefined" && (way === '2' || way === 2)) data.fid = fid
      _this.$http.post('../../../sale/getItemDetailsById.do', data, {
        emulateJSON: true
      }).then((response) => {
        let res = response.body
        _this.list = res.data
        _this.info = res.base[0]
////        _this.info['invoice_amount'] = 0
//        _this.list.forEach(function (item) {
//          _this.info['invoice_amount'] += Number(item['amount'])
//        })
        _this.loading = false
      }).catch(function () {
        _this.$message({
          type: 'error',
          message: '链接错误，请重试！'
        })
        this.$router.push({
          path: `/applyInvoiceDetail/${_this.applicationId}`
        })
      })
    },
    subscribeCallBack: function (res) {
      console.log('回调详情：', res)
    },
    handleCallback: function (response) {
      // 选择完发票号的回调
      let _this = this
      _this.loading = false
      let res = JSON.parse(response)
      console.log('开票登记回调', res)
      if (res['state'] === '5') { // 前面确定开票
        _this.$message({
          type: 'success',
          message: '财务人员开票登记了此发票的申请！'
        })
      }
      _this.getDeatilInfo()
    }
  }
}
</script>
