<template>
  <div id="potentialDel" class="potentialCom" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="潜在客户申请"></TY_NavTop>
    <div class="container" v-if="type === 2 || type === 22">
      <div class="panel-list">
        <p class="del-tip">已被删除的联系人</p>
        <div class="del-line">
          <el-row type="flex" justify="space-between" v-for="(item, index) in list" v-bind:key="index">
            <el-col :span="20"><span class="wid"></span>{{item.name}}{{item.post| getStr(10)}} </el-col>
            <el-col :span="6"><span class="sect-blue" @click="jumpRecordList(item.id)">修改记录</span><span class="wid"></span></el-col>
          </el-row>
        </div>
      </div>
    </div>
    <div class="container" v-if="type === 3 || type === 33">
      <div class="panel-list">
        <p class="del-tip">已被删除的访谈记录</p>
        <div class="del-line">
          <el-row type="flex" justify="space-between" v-for="(item, index) in list" v-bind:key="index">
            <el-col :span="20"><span class="wid"></span>{{item.interviewerNames}} {{item.post| getStr(10)}} <span v-if="item.interviewDate">{{ item.interviewDate | formatDate('yyyy年MM月dd日')}}</span></el-col>
            <el-col :span="6"><span class="sect-blue" @click="jumpRecordList(item.id)">修改记录</span><span class="wid"></span></el-col>
          </el-row>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less">
  #potentialDel {
    .wid{ display: inline-block; width:20px;  }
    .panel-list{
      background: #fff;
      .del-tip{ padding: .8rem 1rem; border-bottom: 1px solid #ccc;}
      .el-row{ padding: .8rem 0 .8rem 1.2rem; border-bottom: 1px solid #ccc;}
    }
  }
</style>
<script>
import { formatDate } from '../../../js/common'
export default {
  name: 'potentialDelList',
  data () {
    return {
      loading: true,
      list: [],
      type: 0
    }
  },
  filters: {
    formatDate
  },
  created () {
    let _this = this
    _this.type = Number(_this.$route.params.type)
    _this.detailId = _this.$route.params.detailId
    let url = '../../../sales/getDeleteContactsList.do'
    if (_this.type === 3) {
      url = '../../../sales/getCustomerInterviewRemoveList.do'
    } else if (_this.type === 22) {
      url = '../../../sale/getSelectDeleteContactsList.do'
    } else if (_this.type === 33) {
      url = '../../../sale/getSelectCustomerInterviewRemoveList.do'
    }
    let data = {
      customerId: _this.detailId
    }
    this.axios.post(url, data).then((response) => {
      _this.loading = false
      _this.list = response.data.data
    }).catch(function (res) {
      console.log(res)
    })
  },
  methods: {
    getStr: function (str, len) {
      return str.substr(0, len - 1)
    },
    jumpRecordList: function (id) {
      let detailId = this.$route.params.detailId
      let type = this.$route.params.type
      this.$router.push({
        path: `/potentialRecord/${detailId}/${id}/${type}`
      })
    }
  }
}
</script>
