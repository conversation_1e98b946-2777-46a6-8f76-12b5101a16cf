package cn.sphd.miners.modules.finance.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class FinanceAccountHistoryDto implements Serializable {

    private Integer id;//账户修改历史id
    private String updateName; //修改人名称
    private Date updateDate; //修改时间
    private BigDecimal initialAmount;   //修改后金额
    private BigDecimal revisedBeforeAmount;   //修改前金额
    private String recordState; //第几次修改后

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public BigDecimal getRevisedBeforeAmount() {
        return revisedBeforeAmount;
    }

    public void setRevisedBeforeAmount(BigDecimal revisedBeforeAmount) {
        this.revisedBeforeAmount = revisedBeforeAmount;
    }

    public BigDecimal getInitialAmount() {
        return initialAmount;
    }

    public void setInitialAmount(BigDecimal initialAmount) {
        this.initialAmount = initialAmount;
    }

    public FinanceAccountHistoryDto(){}

    public FinanceAccountHistoryDto(Integer id,String updateName,Date updateDate,BigDecimal initialAmount){
        this.id=id;
        this.updateName=updateName;
        this.updateDate=updateDate;
        this.initialAmount=initialAmount;
    }

    public String getRecordState() {
        return recordState;
    }

    public void setRecordState(String recordState) {
        this.recordState = recordState;
    }
}
