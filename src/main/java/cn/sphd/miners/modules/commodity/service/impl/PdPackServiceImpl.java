package cn.sphd.miners.modules.commodity.service.impl;

import cn.sphd.miners.modules.commodity.dao.PdMerchandiseDao;
import cn.sphd.miners.modules.commodity.dao.PdPackDao;
import cn.sphd.miners.modules.commodity.entity.PdMerchandise;
import cn.sphd.miners.modules.commodity.entity.PdPack;
import cn.sphd.miners.modules.commodity.service.PdPackService;
import cn.sphd.miners.modules.material.dao.MtBaseDao;
import cn.sphd.miners.modules.material.entity.MtBase;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2017/7/24.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class PdPackServiceImpl implements PdPackService{

    @Autowired
    PdPackDao pdPackDao;

    @Autowired
    PdMerchandiseDao pdMerchandiseDao;
    @Autowired
    MtBaseDao mtBaseDao;

    @Override
    public List<PdPack> getPdPackListByPdBaseId(Integer pdCustomerId) {
        String hql=" and o.productCustomer="+pdCustomerId;
        List<PdPack> pdPacks=pdPackDao.findCollectionByConditionNoPage(hql,null,null);
        return pdPacks;
    }

    @Override
    public PdPack getPdPackById(Integer id) {
        return pdPackDao.get(id);
    }

    @Override
    public void savePdPack(PdPack pdPack) {
        pdPackDao.save(pdPack);
    }

    @Override
    public void updatePdPack(PdPack pdPack) {
        pdPackDao.update(pdPack);
    }

    @Override
    public PdMerchandise getPdMerchandiseById(Integer id) {
        return pdMerchandiseDao.get(id);
    }

    @Override
    public List<PdMerchandise> getPdMerchandiseByOid(Integer oid) {
        String hql=" and o.product.oid="+oid;
        Map<String,String> map = new HashMap<String, String>();
        map.put("id","desc");
        return pdMerchandiseDao.findCollectionByConditionNoPage(hql,null,map);
    }

    @Override
    public void deletePdPack(PdPack pdPack) {
        pdPackDao.delete(pdPack);
    }

    @Override
    public List<MtBase> getPackListByCIdCode(Integer categoryId, String code) {
        String hql=" and o.category="+categoryId;
        if (code!=null&&!"".equals(code)){
            hql+=" and o.code like '%"+code+"%'";
        }
        List<MtBase> mtBaseList=mtBaseDao.findCollectionByConditionNoPage(hql,null,null);
        return mtBaseList;
    }
}
