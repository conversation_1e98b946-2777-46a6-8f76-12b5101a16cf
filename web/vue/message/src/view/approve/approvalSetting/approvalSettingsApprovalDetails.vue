<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
  <div id="approvalSettingsApprovalDetails" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="审批设置的修改审批" :isBackHome="isBackHome"></TY_NavTop>
    <div class="container approvalSettingsDetails">
      <div class="ttl1">{{tips}}</div>
      <div v-if="info.approvalItem.code!=='purchaseApprovalSettings'">
        <div class="ttl2" v-if="info.approvalProcessList.length > 0">
          <p>审批记录</p>
          <p>申请人：{{info.approvalItem.createName || ''}} {{info.approvalItem.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</p>
          <div>
            <p v-if="item.approveStatus==2" v-for='(item, index) in info.approvalProcessList' v-bind:key='index'>审批人：{{item.userName}} {{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</p>
          </div>
          <div class="clr2"></div>
        </div>
      </div>
      <div class="ttl2" v-if="info.approvalItem.code==='purchaseApprovalSettings'">
        <p>审批记录</p>
        <p>申请人：{{info.createName || ''}} {{info.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</p>
        <div>
          <p>等待 <span class="red">{{info.userName}}</span> 审批</p>
        </div>
        <div class="clr2"></div>
      </div>
      <div class="ttl3">
        <div :class="{active: seeType === 1}" @click="toggle(1)">修改后</div>
        <div :class="{active: seeType === 0}" @click="toggle(0)">修改前</div>
      </div>
      <div class="con">
        <!-- 修改后 -->
        <!--加班-->
        <div class="approveItem" v-if="seeType && info.approvalItem.code==='overTimeApply'" v-for='(item, index) in info.approvalFlows' v-bind:key='index'>
          <p>不高于{{item.amountCeiling}}小时的加班，需{{item.level | Upper}}级审批</p>
          <p class="right"> <span>{{item.level | Upper}}级审批者</span> <span>{{item.userName}}</span> <span>{{item.mobile}}</span></p>
          <div class="clr2"></div>
        </div>
        <!--付款-->
        <div class="whiteBox fup" v-if="seeType && info.approvalItem.code==='paymentApproval'"  >
          <!--<p> <span>{{item.level | Upper}}级审批者</span> <span>{{item.userName}}</span> <span>{{item.mobile}}</span></p>-->
          <p>付款审批者：{{ info.approvalItem.status === 0 ? "无需审批" : `${ info.approvalFlows[0]["userName"]} ${info.approvalFlows[0]["mobile"]}`}}</p>
          <p>付款复核者：{{ info.approvalItemAudit.status === 0 ? "不需要" : `${ info.paymentAuditUser.userName } ${ info.paymentAuditUser.mobile }`}}</p>
          <div class="clr2"></div>
        </div>
        <!--报销-->
        <div class="approveItem" v-if="seeType && info.approvalItem.code==='reimburseApply'" v-for='(item, index) in info.approvalFlows' v-bind:key='index'>
          <p v-if="item.amountCeiling>0">不高于{{item.amountCeiling}}元的报销，需{{item.level | Upper}}级审批</p>
          <p v-if="item.amountCeiling == -1 && item.level == 1">报销需{{item.level | Upper}}级审批</p>
          <p v-if="item.amountCeiling == -1 && item.level != 1">高于{{info.top}}元的报销，需{{item.level | Upper}}级审批</p>
          <p class="right"> <span>{{item.level | Upper}}级审批者</span> <span>{{item.userName || item.toUser}}</span> <span>{{item.mobile}}</span></p>
          <div class="clr2"></div>
        </div>
        <!--请假-->
        <div class="approveItem" v-if="seeType && info.approvalItem.code==='leaveApply'" v-for='(item, index) in info.approvalFlows' v-bind:key='index'>
          <p v-if="index === 0 && item.amountCeiling === -1 || !item.amountCeiling">请假需{{item.level | Upper}}级审批</p>
          <p v-else-if="item.amountCeiling > 0 && item.amountCeiling <= 12">当日不高于{{item.amountCeiling}}小时的请假，需{{item.level | Upper}}级审批</p>
          <p v-else-if="item.amountCeiling > 12">不高于{{item.amountCeiling / 24}}天的请假，需{{item.level | Upper}}级审批</p>
          <p v-else>高于{{info.approvalFlows[index - 1].amountCeiling | changeDay}}的请假，需{{item.level | Upper}}级审批</p>
          <p class="right"> <span>{{item.level | Upper}}级审批者</span> <span>{{item.userName || item.toUser}}</span> <span>{{item.mobile}}</span></p>
          <div
            class="clr2"></div>
        </div>
        <!--来自客户订单的评审-->
        <div class="approveItem fatter" v-if="seeType && (info.approvalItem.code ==='ordersReview' || info.approvalItem.code ==='materialInCheck' || info.approvalItem.code ==='productInCheck' || info.approvalItem.code ==='productLogisticsCheck' || info.approvalItem.code ==='finishedProductCheck')">
          <p v-if="info.approvalItem.code ==='ordersReview' && info.approvalItem.status === 1">对于客户发来订单中的数量与交期，需要公司各部门评审</p>
          <p v-else-if="info.approvalItem.code ==='ordersReview' && info.approvalItem.status === 0">对于客户发来订单中的数量与交期，无需公司各部门评审</p>
          <p v-else-if="info.approvalItem.code ==='materialInCheck' && info.approvalItem.status === 0">对于采购来的材料，入库前不需要检验</p>
          <p v-else-if="info.approvalItem.code ==='materialInCheck' && info.approvalItem.status === 1">对于采购来的材料，入库前需要检验</p>
          <p v-else-if="info.approvalItem.code ==='productInCheck' && info.approvalItem.status === 0">生产出的货物入成品库前不需要检验</p>
          <p v-else-if="info.approvalItem.code ==='productInCheck' && info.approvalItem.status === 1">生产出的货物入成品库前需要检验</p>
          <p v-else-if="info.approvalItem.code ==='productLogisticsCheck' && info.approvalItem.status === 0">成品出库时物流人员的复核的模式：无需物流复核</p>
          <p v-else-if="info.approvalItem.code ==='productLogisticsCheck' && info.approvalItem.status === 1">成品出库时物流人员的复核的模式：需物流复核</p>
          <p v-else-if="info.approvalItem.code ==='finishedProductCheck' && info.approvalItem.status === 0">成品库的模式：不启用成品库</p>
          <p v-else-if="info.approvalItem.code ==='finishedProductCheck' && info.approvalItem.status === 1">成品库的模式：启用成品库</p>
        </div>
        <!--采购审批流程的修改审批-->
        <div class="approveItem fatter" v-if="seeType && info.approvalItem.code ==='purchaseApprovalSettings'">
          <p v-if="info.after.status === 0">采购无需审批</p>
          <p v-if="info.after.status === 1">采购需{{info.after.level | Upper}}级审批</p>
          <p>开始执行的时间: {{info.after.openDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</p>
          <div v-if="info.after.status === 1">
            <p v-for='(item, index) in info.after.userList' v-bind:key='index'>
              {{item.level | Upper}}级审批者： {{item.userName}}  {{item.mobile}}
            </p>
          </div>
        </div>
        <!--商品与产品名称代号关系的修改申请-->
        <div class="approveItem fatter" v-if="seeType && info.approvalItem.code ==='commodityProduct'">
          <div class="ptMode">
            <div>当前产品图号与名称的创建模式：</div>
            <div>与专属商品相关的产品为模式{{info.pdModelSettingsNew.dedicatedModel}}</div>
            <div>与通用型商品相关的产品为模式{{info.pdModelSettingsNew.generalModel}}</div>
            <div v-if="info.pdModelSettingsNew.generalModel !== 1">产品信息{{info.pdModelSettingsNew.reviseModel === 1 ? `由有权限创建商品的职工修改`:`由有产品操作权限的职工修改`}}</div>
          </div>
        </div>
        <!--仓库的模式修改审批-->
        <div class="approveItem fatter" v-if="seeType && info.approvalItem.code ==='stockModeChange'">
          <p v-if="info.approvalItem.status === 0">仓库的模式：非智能仓库</p>
          <p v-if="info.approvalItem.status === 1">仓库的模式：智能仓库</p>
        </div>
        <!--外购物料入库时，包装完好情况的检查者-->
        <div class="approveItem fatter" v-if="seeType && info.approvalItem.code ==='inStockCheckerSet'">
          <p v-if="info.approvalItem.status === 0">外购物料入库时，需由库管检查包装的完好情况 </p>
          <p v-if="info.approvalItem.status === 1">外购物料入库时，需由检验员检查包装的完好情况</p>
          <p v-if="info.approvalItem.status === 2">外购物料入库时，库管与检验员均需检查包装的完好情况</p>
          <p v-if="info.approvalItem.status === 3">外购物料入库时，不需检查包装的完好情况</p>
        </div>
        <!-- 修改前 -->
        <!--加班-->
        <div class="approveItem" v-if="!seeType && info.approvalItem.code==='overTimeApply'" v-for='(item, index) in info.approvalFlowsOld' v-bind:key='index'>
          <p>不高于{{item.amountCeiling}}小时的加班，需{{item.level | Upper}}级审批</p>
          <p class="right"> <span>{{item.level | Upper}}级审批者</span> <span>{{item.userName}}</span> <span>{{item.mobile}}</span></p>
          <div class="clr2"></div>
        </div>
        <!--付款-->
        <div class="whiteBox fup" v-if="!seeType && info.approvalItem.code==='paymentApproval'"  >
          <!--<p> <span>{{item.level | Upper}}级审批者</span> <span>{{item.userName}}</span> <span>{{item.mobile}}</span></p>-->
          <p>付款审批者：{{ info.approvalItemOld.status === 0 ? "无需审批" : `${ info.approvalFlowsOld[0]["userName"]} ${ info.approvalFlowsOld[0]["mobile"]}`}}</p>
          <p>付款复核者：{{ info.approvalItemOldAudit.status === 0 ? "不需要" : `${ info.paymentAuditUser.userName } ${ info.paymentAuditUser.mobile }`}}</p>
          <div class="clr2"></div>
        </div>
        <!--报销-->
        <div class="approveItem" v-if="!seeType && info.approvalItem.code==='reimburseApply'" v-for='(item, index) in info.approvalFlowsOld' v-bind:key='index'>
          <p v-if="item.amountCeiling>0">不高于{{item.amountCeiling}}元的报销，需{{item.level | Upper}}级审批</p>
          <p v-if="item.amountCeiling == -1 && item.level == 1">报销需{{item.level | Upper}}级审批</p>
          <p v-if="item.amountCeiling == -1 && item.level != 1">高于{{info.topOld}}元的报销，需{{item.level | Upper}}级审批</p>
          <p class="right"> <span>{{item.level | Upper}}级审批者</span> <span>{{item.userName || item.toUser}}</span> <span>{{item.mobile}}</span></p>
          <div class="clr2"></div>
        </div>
        <!--请假-->
        <div class="approveItem" v-if="!seeType && info.approvalItem.code==='leaveApply'" v-for='(item, index) in info.approvalFlowsOld' v-bind:key='index'>
          <p v-if="index === 0 && item.amountCeiling === -1 || !item.amountCeiling">请假需{{item.level | Upper}}级审批</p>
          <p v-else-if="item.amountCeiling > 0 && item.amountCeiling <= 12">当日不高于{{item.amountCeiling}}小时的请假，需{{item.level | Upper}}级审批</p>
          <p v-else-if="item.amountCeiling > 12">不高于{{item.amountCeiling / 24}}天的请假，需{{item.level | Upper}}级审批</p>
          <p v-else>高于{{info.approvalFlows[index - 1].amountCeiling | changeDay}}的请假，需{{item.level | Upper}}级审批</p>
          <p class="right"> <span>{{item.level | Upper}}级审批者</span> <span>{{item.userName || item.toUser}}</span> <span>{{item.mobile}}</span></p>
          <div class="clr2"></div>
        </div>
        <!--来自客户订单的评审-->
        <div class="approveItem fatter" v-if="!seeType && (info.approvalItem.code ==='ordersReview' || info.approvalItem.code ==='materialInCheck' || info.approvalItem.code ==='productInCheck' || info.approvalItem.code ==='productLogisticsCheck' || info.approvalItem.code ==='finishedProductCheck')">
          <p v-if="info.approvalItemOld.code ==='ordersReview' && info.approvalItemOld.status === 1">对于客户发来订单中的数量与交期，需要公司各部门评审</p>
          <p v-else-if="info.approvalItemOld.code ==='ordersReview' && info.approvalItemOld.status === 0">对于客户发来订单中的数量与交期，无需公司各部门评审</p>
          <p v-else-if="info.approvalItemOld.code ==='materialInCheck' && info.approvalItemOld.status === 0">对于采购来的材料，入库前不需要检验</p>
          <p v-else-if="info.approvalItemOld.code ==='materialInCheck' && info.approvalItemOld.status === 1">对于采购来的材料，入库前需要检验</p>
          <p v-else-if="info.approvalItemOld.code ==='productInCheck' && info.approvalItemOld.status === 0">生产出的货物入成品库前不需要检验</p>
          <p v-else-if="info.approvalItemOld.code ==='productInCheck' && info.approvalItemOld.status === 1">生产出的货物入成品库前需要检验</p>
          <p v-else-if="info.approvalItemOld.code ==='productLogisticsCheck' && info.approvalItemOld.status === 0">成品出库时物流人员的复核的模式：无需物流复核</p>
          <p v-else-if="info.approvalItemOld.code ==='productLogisticsCheck' && info.approvalItemOld.status === 1">成品出库时物流人员的复核的模式：需物流复核</p>
          <p v-else-if="info.approvalItemOld.code ==='finishedProductCheck' && info.approvalItemOld.status === 0">成品库的模式：不启用成品库</p>
          <p v-else-if="info.approvalItemOld.code ==='finishedProductCheck' && info.approvalItemOld.status === 1">成品库的模式：启用成品库</p>
        </div>
        <!--采购审批流程的修改审批-->
        <div class="approveItem fatter" v-if="!seeType && info.approvalItem.code ==='purchaseApprovalSettings'">
          <p v-if="info.before.status === 0">采购无需审批</p>
          <p v-if="info.before.status === 1">采购需{{info.before.level | Upper}}级审批</p>
          <p>开始执行的时间: {{info.before.openDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</p>
          <div v-if="info.before.status === 1">
            <p v-for='(item, index) in info.before.userList' v-bind:key='index'>
              {{item.level | Upper}}级审批者： {{item.userName}}  {{item.mobile}}
            </p>
          </div>
        </div>
        <!--商品与产品名称代号关系的修改申请-->
        <div class="approveItem fatter" v-if="!seeType && info.approvalItem.code ==='commodityProduct'">
          <div class="ptMode">
            <div>当前产品图号与名称的创建模式：</div>
            <div>与专属商品相关的产品为模式{{info.pdModelSettingsOld.dedicatedModel}}</div>
            <div>与通用型商品相关的产品为模式{{info.pdModelSettingsOld.generalModel}}</div>
            <div v-if="info.pdModelSettingsOld.generalModel !== 1">产品信息{{info.pdModelSettingsOld.reviseModel === 1 ? `由有权限创建商品的职工修改`:`由有产品操作权限的职工修改`}}</div>
          </div>
        </div>
        <!--仓库的模式修改审批-->
        <div class="approveItem fatter" v-if="!seeType && info.approvalItem.code ==='stockModeChange'">
          <p v-if="info.approvalItemOld.status === 0">仓库的模式：非智能仓库</p>
          <p v-if="info.approvalItemOld.status === 1">仓库的模式：智能仓库</p>
        </div>
        <!--外购物料入库时，包装完好情况的检查者-->
        <div class="approveItem fatter" v-if="!seeType && info.approvalItem.code ==='inStockCheckerSet'">
          <p v-if="info.approvalItemOld.status === 0">外购物料入库时，需由库管检查包装的完好情况 </p>
          <p v-if="info.approvalItemOld.status === 1">外购物料入库时，需由检验员检查包装的完好情况</p>
          <p v-if="info.approvalItemOld.status === 2">外购物料入库时，库管与检验员均需检查包装的完好情况</p>
          <p v-if="info.approvalItemOld.status === 3">外购物料入库时，不需检查包装的完好情况</p>
        </div>
      </div>
      <div class="handle_button chargeApplyBtn" v-if="showBtn">
        <input type="submit" class="ui-btn ui-btn_info" value="批准" @click="chargeApply(1)">
        <input type="submit" class="ui-btn ui-btn_error" value="驳回"  @click="chargeApply(0)">
      </div>
    </div>

    <el-dialog title="!提示" :visible.sync="tipDialog1" width="80%">
      <div class="handle-center">确定批准此申请？</div>
      <span slot="footer" class="dialog-footer">
          <div class="handle_button handle-center">
            <input type="submit" class="ui-btn" value="取消" @click="tipDialog1 = false">
            <input type="submit" class="ui-btn ui-btn_info" value="确定" @click="okCharge(1)">
          </div>
        </span>
    </el-dialog>

    <el-dialog title="!提示" :visible.sync="tipDialog0" width="80%">
      <el-form ref="form" label-width="80px" label-position="top">
        <el-form-item label="请输入驳回理由"><span style="color:red; ">{{wordsTip}}</span>
          <el-input type="textarea" v-model="reason" @change="chargeWords()" placeholder="最多可录入50字"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
          <div class="handle_button handle-center">
             <input type="submit" class="ui-btn" value="取消" @click="tipDialog0 = false">
            <input type="submit" class="ui-btn ui-btn_info" value="确定" @click="okCharge(0)">
          </div>
        </span>
    </el-dialog>
  </div>
</template>

<script>
import { gohistory } from '@/js/common'
export default {
  name: 'approvalSettingsApprovalDetails',
  data () {
    return {
      info: {
        approvalItem: {},
        approvalFlows: [],
        approvalFlowsOld: [],
        approvalProcessList: [],
        before: {
          userList: []
        },
        after: {
          userList: []
        },
        userName: '',
        createName: '',
        createDate: '',
        approveStatus: 0,
        approvalId: 0
      },
      seeType: 1,
      reason: '',
      wordsTip: '',
      showBtn: false,
      chargeType: 1,
      tipDialog1: false,
      tipDialog0: false,
      loading: true,
      approvalProcessId: this.$route.params.id,
      isBackHome: false,
      listeners: []
    }
  },
  filters: {
    Upper: function (num) {
      switch (Number(num)) {
        case 1: return '一'
        case 2: return '二'
        case 3: return '三'
        case 4: return '四'
        case 5: return '五'
        case 6: return '六'
        case 7: return '七'
        case 8: return '八'
        default:
      }
    },
    toType: function (code) {
      switch (code) {
        case 'paymentApproval':return '新付款审批流程'
        case 'overTimeApply': return '新加班审批流程'
        case 'leaveApply': return '新请假审批流程'
        case 'reimburseApply': return '新报销审批流程'
        case 'ordersReview': return '客户订单评审新流程'
        case 'materialInCheck': return '外购材料检验新流程'
        case 'productInCheck': return '成品入库新流程'
        case 'commodityProduct': return '通用型商品与产品名称代号关系的修改申请'
        default: return ''
      }
    },
    changeDay: function (hour) {
      var str = ''
      if (hour >= 24) {
        str = hour / 24 + '天'
      } else {
        str = hour + '小时'
      }
      return str
    }
  },
  created: function () {
    let _this = this
    _this.applyDetail()
    _this.listeners = [
      // 审批人待处理
      _this.sphdSocket.subscribe('handleApproval', function (data) {
        console.log('获取订阅审批结果:' + data)
        _this.applyDetail()
      }, function () {
        console.log('审批人待处理订阅 失败！')
      }, 'user'),
      // 审批人已批准
      _this.sphdSocket.subscribe('approvalApproval', function (data) {
        console.log('审批人已批准结果:' + data)
        _this.applyDetail()
      }, function () {
        console.log('审批人已批准订阅 Error:')
      }, 'user'),
      // 自己的审批结果
      _this.sphdSocket.subscribe('updateOutTimeApproval', function (data) {
        console.log('给审批人已批准推送:' + data)
        let res = JSON.parse(data)
        console.log(res)
        // 0-修改失败 1-修改成功 2-此修改申请正在申请中，不能再次提交！ 3-修改的不是加班 4-此申请已审批，不可重复操作
        let state = res['state']
        let content = res['content']
        _this.loading = false
        _this.$message({
          type: 'info',
          message: content
        })
        if (Number(state) === 1) {
          _this.showBtn = false
          _this.tipDialog1 = false
          _this.tipDialog0 = false
          _this.$router.push({
            path: `/approvalSettingsApproval`
          })
        }
      }, function () {
        console.log('审批 加班审批流程修改申请 失败')
        _this.$message({
          type: 'error',
          message: '审批 加班审批流程修改申请 失败'
        })
      })
    ]
  },
  methods: {
    chargeWords: function () {
      if (this.reason.length > 50) {
        this.reason = this.reason.substr(0, 50)
        this.wordsTip = '驳回理由最多可录入50字!'
      } else {
        this.wordsTip = ''
      }
    },
    applyDetail: function () {
      let _this = this
      _this.axios.post('../../../popedom/updateItemDetail.do', {
        'approvalProcessId': _this.approvalProcessId}
      ).then(function (response) {
        console.log('详情', response.data.data)
        _this.info = response.data.data
        _this.loading = false
        if (_this.info.approvalItem.code === 'reimburseApply') {
          let top = ''
          let topOld = ''
          _this.info.approvalFlowsOld.forEach(function (item) {
            if (item.amountCeiling !== -1) {
              topOld = item.amountCeiling
            }
          })
          _this.info.approvalFlows.forEach(function (item) {
            if (item.amountCeiling !== -1) {
              top = item.amountCeiling
            }
          })
          _this.info['top'] = top
          _this.info['topOld'] = topOld
        }
        let arr = _this.info.approvalProcessList
        console.log('arr', arr)
        if (arr && arr.length > 0) {
          arr.forEach(function (item) {
            if (Number(item.approveStatus) === 1) {
              let uID = item.toUser
              let curID = _this.sphdSocket.user.userID
              if (uID === curID) {
                _this.showBtn = true
              }
            }
          })
        }
        if (_this.info.approveStatus === '1') {
          _this.showBtn = true
        }
      }).catch(function (error) {
        console.log(error)
        _this.$message({
          type: 'error',
          message: '链接错误啦，请重试！'
        })
      })
    },
    toggle: function (num) {
      this.seeType = num
    },
    chargeApply: function (type) {
      console.log('tipDialog' + type)
      this['tipDialog' + type] = 1
    },
    okCharge: function (type) {
      let that = this
      if (this.info.approvalItem.code==='purchaseApprovalSettings') {
        this.loading = true
        let approveStatus = type === 1?2: 3
        let data = {
          approvalId: this.info.approvalId,  //审批id
          approveStatus: approveStatus, //审批结果 2批准 3驳回
          reason: this.reason   //驳回原因
        }
        if (data.reason.length > 50) {
          this.$kiko_message('驳回理由太长啦！')
          return false
        }
        this.axios.post('../../../purchaseApproval/purchaseApprovalApproval.do', {
          json: JSON.stringify(data)
        })
          .then(function (response) {
            that.loading = false
            console.log(response)
            let status = response.data.success
            if (status === 1) {
              that.$kiko_message('操作成功')
              gohistory(that)
              that.tipDialog1 = false
              that.tipDialog0 = false
            } else {
              that.$kiko_message('操作失败')
            }
          })
          .catch(function (error) {
            that.$kiko_message('系统错误，请重试！')
          })
      } else {
        let Process = this.info.approvalProcessList
        let id = Process[Process.length - 1]['id']
        let data = {
          'session': this.sphdSocket.sessionid,
          'userId': this.sphdSocket.user.userID,
          'approvalProcessId': id,
          'approveStatus': type,
          'reason': this.reason
        }
        if (data.reason.length > 50) {
          this.$kiko_message('驳回理由太长啦！')
          return false
        }
        console.log(data)
        this.loading = true
        this.tipDialog1 = false
        this.tipDialog0 = false
        this.showBtn = false
        this.sphdSocket.send('updateOutTimeApproval', data)
      }
    }
  },
  computed: {
    tips() {
      let RootFilters = this.$root.$options.filters
      let filters = this.$options.filters
      let code = this.info.approvalItem.code
      let tip = ''
      if (this.info.approvalItem) {
        if ( code==='purchaseApprovalSettings') {
          tip = `采购审批流程已被修改，计划于${RootFilters.formatDay(this.info.after.openDate, 'YYYY/MM/DD')}生效，有待审批`
        } else if (code === 'stockModeChange') {
          tip = `现提请修改仓库的模式，修改计划生效的日期：${RootFilters.formatDay(this.info.approvalItem.openDate, 'YYYY/MM/DD')}\n请予审批！`
        } else if (code === 'productLogisticsCheck') {
          tip = `现提请修改成品出库时物流人员的复核的模式，修改计划生效的日期：${RootFilters.formatDay(this.info.approvalItem.openDate, 'YYYY-MM-DD')}\n请予审批！`
        } else if (code === 'finishedProductCheck') {
          tip = `现提请修改成品库的模式，修改计划生效的日期：${RootFilters.formatDay(this.info.approvalItem.openDate, 'YYYY-MM-DD')}\n请予审批！`
        } else if (code === 'inStockCheckerSet') {
          tip = `现提请修改外购物料入库时包装完好情况的检查者，此项修改计划于：${RootFilters.formatDay(this.info.approvalItem.openDate, 'YYYY-MM-DD')}生效。请予审批！`
        } else {
          tip = `计划于${RootFilters.formatDay(this.info.approvalItem.openDate, 'YYYY/MM/DD')}生效的${filters.toType(this.info.approvalItem.code)}有待审批`
        }
      }
      return tip
    }
  },
  destroyed: function () {
    let _this = this
    console.log('destroyed')
    _this.listeners.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  }
}
</script>
