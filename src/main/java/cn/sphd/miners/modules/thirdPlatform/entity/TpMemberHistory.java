package cn.sphd.miners.modules.thirdPlatform.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

/**
 * Created by HIAPAD on 2022/12/2.
 */
@Entity
@Table( name ="t_tp_member_history" )
public class TpMemberHistory extends BaseEntity {

    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;//`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT 'ID',
    @Column(name = "tg_code")
//    @JsonIgnore @JSONField(serialize = false)
    private Byte tgCode;//`tg_code` TINYINT DEFAULT NULL COMMENT '第三方大厂(1-weChat,2-byteDance)',
    @Column(name = "app_id", length = 34)
//    @JsonIgnore @JSONField(serialize = false)
    private String appId;//`app_id` VARCHAR(34) CHARACTER SET latin1 COLLATE latin1_bin DEFAULT NULL COMMENT 'appId',
    @Column(name = "union_id", length = 128)
//    @JsonIgnore @JSONField(serialize = false)
    private String unionId;//`union_id` VARCHAR(128) CHARACTER SET latin1 COLLATE latin1_bin DEFAULT NULL COMMENT '会员union_id',
    @Column(name = "open_id", length = 128)
//    @JsonIgnore @JSONField(serialize = false)
    private String openId;//`open_id` VARCHAR(128) CHARACTER SET latin1 COLLATE latin1_bin DEFAULT NULL COMMENT '会员openid',
    @Column(name = "acc_id")
    private Long accId;//`acc_id` BIGINT DEFAULT NULL COMMENT '账号accId',
    @Column(name = "member")
    private Long member;//`第三方用户ID
    @Column
    private String avatar;//`avatar` VARCHAR(255) DEFAULT NULL COMMENT '第三方用户头像',
    @Column(length = 20)
    private String mobile;//`mobile` VARCHAR(20) DEFAULT NULL COMMENT '第三方用户手机号',
    @Column(name = "nick_name", length = 20)
    private String nickName;//`nick_name` VARCHAR(20) DEFAULT NULL COMMENT '第三方用户昵称',
    @CreationTimestamp
    @Column(name = "create_time")
//    @JsonIgnore
//    @JSONField(serialize = false)
    private Date createTime;//`create_time` DATETIME(3) DEFAULT NULL COMMENT '创建时间',
    @UpdateTimestamp
    @Column(name = "update_time")
    @JsonIgnore @JSONField(serialize = false)
    private Date updateTime;//`update_time` DATETIME(3) DEFAULT NULL COMMENT '修改时间',
    @Column(name = "creator")
    private Integer creator;  // 存accid
    @Column(name = "create_name")
    private String createName;
    @Column(name = "updator")
    private Integer updator;
    @Column(name = "update_name")
    private String updateName;
    @Column(name = "operation")
    private String operation;  //操作:1-绑定,2-解绑
    @Column(name = "previous_id")
    private Integer previousId; //修改前记录ID
    @Column(name = "version_no")
    private Integer versionNo; //版本号,每次修改+1

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Byte getTgCode() {
        return tgCode;
    }

    public void setTgCode(Byte tgCode) {
        this.tgCode = tgCode;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public Long getAccId() {
        return accId;
    }

    public void setAccId(Long accId) {
        this.accId = accId;
    }

    public Long getMember() {
        return member;
    }

    public void setMember(Long member) {
        this.member = member;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
}
