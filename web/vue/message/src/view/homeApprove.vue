<template>
  <div class="ui-cells" id="myApprove">
    <a class="ui-cell" v-on:click="approveTo(item)" v-if="item.pid == 3" v-for="(item, index) in $store.state.menu" v-bind:key="index">
      <div class="ui-cell__bd">
        <div class="ui-cell_title">
          <span class="approve_name" v-html="item.name"></span>
          <span :class="!item.corner? 'hd': 'message_count'" v-html="item.corner"></span>
        </div>
      </div>
      <div class="ui-cell__ft"></div>
    </a>
  </div>
</template>
<script>
export default {
  name: 'homeApprove',
  data () {
    return {}
  },
  created () {
    this.$store.dispatch('setNewHomeNav', 2)
    window.localStorage.setItem('floating', '/homeApprove')
  },
  methods: {
    approveTo: function (item) {
      let str = 'fileApproval' + 'versionApproval' + 'invoiceApproval' + 'invoiceRegister' + 'returnInAccount' + 'returnHandle' + 'complaintHandle' +
        'potentialCustomerApproval' + 'potentialCustomerApprovalDuty' + 'approvalSettingsApproval' + 'memoJobMsg' + 'tryApproval' +
        'editOrgPopedomApproval' + 'ticketAuthentication' + 'approvalFileBorrowApply' + 'fileBorrowApproval' + 'formulaEditApproval' + 'testPaperApproval' +
        'incrementApproval' + 'incrementEditApproval' + 'soonDisappearFile' + 'contentApproval' + 'contentReleaseVersionApproval' + 'needRecoveredAmount' + 'overreceivedFundsApproval'
        + 'productChangePlanApproval' + "productChangePlanSendApproval"
      console.log(' menu: ', item)
      // code":"ticketAuthentication","description":"财务报销受理模块","id":68,"isMenu":0,"isView":"3","level":3,"mid":"lp","name":"增票认证","orders":60,"path":"/handle/approval/ticketAuthentication/","pid":3,"serviceClass":"","target":"finance","corner":""},
      if (str.indexOf(item.code) > -1) {
        this.$router.push({
          path: '/' + item.code
        })
      } else {
        this.$router.push({
          path: `/${item.code}/${item.id}`
        })
      }
      if(item.code === 'overreceivedFundsApproval'){
        localStorage.navNum = 1
      }
    }
  }
}
</script>
