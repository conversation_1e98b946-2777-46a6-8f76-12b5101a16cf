package cn.sphd.miners.modules.finance.service.impl;

import cn.sphd.miners.common.service.BaseServiceImpl;
import cn.sphd.miners.common.utils.DateUtils;
import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.accountant.service.FinanceRelevanceService;
import cn.sphd.miners.modules.finance.dao.*;
import cn.sphd.miners.modules.finance.dto.FinanceAccountHistoryDto;
import cn.sphd.miners.modules.finance.entity.*;
import cn.sphd.miners.modules.finance.service.AccountService;
import cn.sphd.miners.modules.finance.service.DataService;
import cn.sphd.miners.modules.finance.service.FinanceAccountService;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2016/12/12.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED)
public class FinanceAccountServiceImpl extends BaseServiceImpl implements FinanceAccountService {
    @Autowired
    FinanceAccountDao financeAccountDao;
    @Autowired
    AccountPeroidDao accountPeriodDao;
    @Autowired
    AccountDetailDao accountDetailDao;
    @Autowired
    FinanceAccountRecordDao financeAccountRecordDao;
    @Autowired
    FinanceAccountHistoryDao financeAccountHistoryDao;

    @Autowired
    UserService userService;
    @Autowired
    AccountService accountService;
    @Autowired
    DataService dataService;
    @Autowired
    FinanceRelevanceService financeRelevanceService;   //会计科目

    @Override
    public List<FinanceAccount> getAccount(Integer oid,List<Integer> orgIntegerList) {
        String hql = "from FinanceAccount where";
        Map<String,Object> params = new HashMap<>();
        if (oid!=null){
            hql+=" org_=:org";
            params.put("org",oid);
        }
       if (orgIntegerList!=null&&orgIntegerList.size()>0){
           hql+=" org_ in (:orgIntegerList)";
           params.put("orgIntegerList",orgIntegerList);
       }
        List<FinanceAccount> financeAccountList = financeAccountDao.getListByHQLWithNamedParams(hql,params);
//        String condition = " and o.org = " + oid;
//        List<FinanceAccount> financeAccountList = financeAccountDao.findCollectionByConditionNoPage(condition,null,null);
        return financeAccountList;
    }

    @Override
    public FinanceAccount addAccount(FinanceAccount financeAccount, User user, Organization organization) {
//        FinanceAccount financeAccount1 = new FinanceAccount();
        financeAccount.setCreateDate(new Date());
        financeAccount.setCreator(user.getUserID());
//        financeAccount1.setAccount(financeAccount.getAccount());
//        financeAccount1.setBankName(financeAccount.getBankName());
//        financeAccount1.setCashable(financeAccount.getCashable());
//        financeAccount1.setName(financeAccount.getName());
        financeAccount.setPreviousBalance(new BigDecimal(0));
        financeAccount.setMemo(financeAccount.getMemo());
        financeAccount.setOrg(organization);
        financeAccount.setAccountStatus(1);   //账户状态 0:冻结 1：正常
        financeAccount.setApproveStatus("1");
        financeAccount.setCreateName(user.getUserName());
        financeAccount.setBalance(financeAccount.getInitialAmount()==null? new BigDecimal(0):financeAccount.getInitialAmount());
        financeAccount.setDebit(financeAccount.getDebit()==null? new BigDecimal(0):financeAccount.getDebit());
        financeAccount.setCredit(financeAccount.getInitialAmount()==null? new BigDecimal(0):financeAccount.getInitialAmount());
        financeAccount.setInitialAmount(financeAccount.getInitialAmount()==null? new BigDecimal(0):financeAccount.getInitialAmount());//初始金额
        financeAccount.setAccountType(2);  // 1-现金，2-银行,3-其他
//        financeAccount1.setIsPublic(financeAccount.getIsPublic());
        financeAccount.setEndDate(new Date());
        financeAccountDao.save(financeAccount);    //新增银行账户

        this.updateFinanceAccountToFinanceAccountHistory(financeAccount,user,0,null);  //添加到历史中

        if (!MyStrings.nulltoempty(financeAccount.getIsPublic()).isEmpty() && "1".equals(financeAccount.getIsPublic())){   // 对公户关联会计
            financeRelevanceService.generateSubjectsByFinance(user.getOid(),financeAccount.getId(),financeAccount.getBankName(),financeAccount.getAccount(),user);
        }
        this.addPeroid(financeAccount, 2, organization);  //生成日结
        this.addPeroid(financeAccount, 1, organization);  //生成月结
        this.saveAccountDetail(user,organization,financeAccount,financeAccount.getBalance(),financeAccount.getInitialAmount(),"1","初始资金原始数据","0",user.getUserName(),"1",null,"1",null,null,null,"1",null,null,null,new Date(),new Date(),null,null);  //添加明细

        return financeAccount;
    }

    @Override
    public FinanceAccount getAccountById(Integer accountId) {
        return financeAccountDao.get(accountId);
    }

    @Override
    public void updateFinanceAccount(FinanceAccount financeAccount) {
         financeAccountDao.update(financeAccount);
    }

    @Override
    public List<FinanceAccount> getAccountByAccountStatus(Integer oid, Integer accountStatus,String isPublic) {
        Map<String,Object> params = new HashMap<>();
        String hql = "from FinanceAccount where org_=:org and accountStatus=:accountStatus and accountType=2";
        params.put("org",oid);
        params.put("accountStatus",accountStatus);
        if (!MyStrings.nulltoempty(isPublic).isEmpty()){
            hql+=" and isPublic=:isPublic";
            params.put("isPublic",isPublic);
        }
        List<FinanceAccount> financeAccounts = financeAccountDao.getListByHQLWithNamedParams(hql,params);
        return financeAccounts;
    }

    @Override
    public FinanceAccount getCashAccount(Integer oid, Integer accountType) {
        String hql = " from FinanceAccount o where o.org = " + oid + " and o.accountType = " + accountType;
        FinanceAccount financeAccount = financeAccountDao.getByHQL(hql);
        return financeAccount;
    }

    @Override
    public FinanceAccount getAccountByIdAndIsPublic(Integer accountId, String isPublic) {
        Map<String,Object> params = new HashMap<>();
        String hql = " from FinanceAccount where id=:accountId and isPublic=:isPublic";
        params.put("accountId",accountId);
        params.put("isPublic",isPublic);
        return (FinanceAccount) financeAccountDao.getByHQLWithNamedParams(hql,params);
    }

    @Override
    public List<FinanceAccount> getPublicAccount(Integer org) {
        Map<String,Object> params = new HashMap<>();
        String hql = " from FinanceAccount where org_=:org and isPublic=1";
        params.put("org",org);
        return financeAccountDao.getListByHQLWithNamedParams(hql,params);
    }

    @Override
    public FinanceAccount getBasicAccount(Integer oid) {
        HashMap<String,Object> params = new HashMap<>();
        String hql = "from FinanceAccount where isBasic=1 and org_=:oid";
        params.put("oid",oid);
        return (FinanceAccount) financeAccountDao.getByHQLWithNamedParams(hql,params);
    }

    @Override    //periodType 1-月结  2-日结
    public AccountPeriod addPeroid(FinanceAccount financeAccount,Integer periodType,Organization organization) {
        AccountPeriod accountPeriod = new AccountPeriod();
        accountPeriod.setFid(financeAccount.getId());
        accountPeriod.setAccount(String.valueOf(financeAccount.getId()));
        accountPeriod.setPreviousBalance(financeAccount.getPreviousBalance());
        accountPeriod.setCredit(financeAccount.getBalance()==null?BigDecimal.valueOf(0):financeAccount.getBalance());
        accountPeriod.setDebit(BigDecimal.valueOf(0));
        accountPeriod.setOrg(organization);
        accountPeriod.setBalance(financeAccount.getBalance());
        accountPeriod.setAccountType(2);
        accountPeriod.setBuildState("未生成");
        accountPeriod.setCreateDate(new Date());
//        accountPeriod.setAccountId(financeAccount);
        accountPeriod.setAccountId(financeAccount.getId());
        if (periodType==1){   //月结
            accountPeriod.setBeginDate(NewDateUtils.changeMonth(new Date(),0));
            accountPeriod.setEndDate(DateUtils.monthEnd(DateUtils.thisMonthFirstDate()));
            accountPeriod.setPeriodType(1);
        }else {   //日结
            accountPeriod.setBeginDate(NewDateUtils.today());
            accountPeriod.setPeriodType(2);
        }
        accountPeriodDao.save(accountPeriod);
        return accountPeriod;
    }

    @Override   // 添加明细
    public AccountDetail saveAccountDetail(User user,Organization organization,FinanceAccount financeAccount,BigDecimal balance,BigDecimal credit,String type,String summary,String method,String auditorName,String modityStatus,Integer business,String source,BigDecimal debit,String genre,String categoryDesc,String accountantStatus,String memo,String businessType,String partnerName, Date paymentDate,Date billDate,Integer previousId,Boolean isModify) {
        AccountDetail accountDetail = new AccountDetail();
        accountDetail.setCreateDate(new Date());
        accountDetail.setCreator(user.getUserID());
        accountDetail.setCreateName(user.getUserName());
        accountDetail.setOrg(organization);
//        accountDetail.setAccount(dailyId.toString());
        accountDetail.setAuditDate(new Date());
        accountDetail.setFid(financeAccount.getId().toString());
        accountDetail.setCredit(credit);//收入
        accountDetail.setDebit(debit);  //支出
        if (credit!=null){
            accountDetail.setBillAmount(credit.abs());  //票面金额
        }else if (debit!=null){
            accountDetail.setBillAmount(debit.abs());  //票面金额
        }
        accountDetail.setAccountBank(financeAccount.getAccount()==null?financeAccount.getBankName():financeAccount.getBankName()+financeAccount.getAccount());
        accountDetail.setBalance(balance);
        accountDetail.setType(type);//类型 1-初始资金
        accountDetail.setSummary(summary);
        accountDetail.setPurpose(summary);
        accountDetail.setAuditDate(new Date());
        accountDetail.setModityStatus(modityStatus);//数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次、包括借款的收入与支出) 3-关闭账户后不可修改
        accountDetail.setMethod(method);
        accountDetail.setAccountId(financeAccount);
        accountDetail.setGenre(genre);   //类别 1-贷款，2-借款，3-投资款，4-废品，5-其他
        accountDetail.setCategoryDesc(categoryDesc);
        accountDetail.setAccountantStatus(accountantStatus);//会计数据状态  1-未选择  2-已选择
        accountDetail.setBusinessType(businessType);   //0101-常规借款  0102-还款
        if ("0101".equals(businessType)){
            accountDetail.setReceiveAccountDate(paymentDate);
        }
        accountDetail.setBusiness(business);
        accountDetail.setBusinessDate(paymentDate);  //付款日期
        accountDetail.setSource(source);  //数据来源:1-手工录入(数据录入、以及报销等),2-业务自动生成(暂时只包括借款)
        accountDetail.setMemo(memo);
        accountDetail.setAuditorName(auditorName);  //经手人
        accountDetail.setPartnerName(partnerName);  //合作方经手人
        accountDetail.setBillDate(billDate);  //票据日期
        accountDetail.setPreviousId(previousId);
        accountDetail.setModify(isModify);  //是否修改,false-未修改(0),true-已修改(1)
        accountDetailDao.save(accountDetail);   //添加明细
        return accountDetail;
    }

    @Override
    public List<FinanceAccount> getAllAccounts(Integer oid,List<Integer> orgIntegerList,Integer accountStatus,Integer accountType) {
        HashMap<String,Object> params = new HashMap<>();
        String hql = "from FinanceAccount where";
        if (oid!=null){
            hql+=" org_=:org";
            params.put("org",oid);
        }
        if (orgIntegerList!=null&&orgIntegerList.size()>0){ //此里面包含上面的oid，所以此有值，oid已在前面赋值为null
            hql+=" org_ in (:orgIntegerList)";
            params.put("orgIntegerList",orgIntegerList);
        }
        if (accountType!=null){
            hql+=" and accountType=:accountType";
            params.put("accountType",accountType);
        }
        switch (accountStatus){
            case 1: hql+= " and accountStatus=:accountStatus order by createDate asc";    //正常的根据创建时间正序排列
               break;
            case 0:  hql+=" and accountStatus=:accountStatus order by endDate desc";
               break;
        }
        params.put("accountStatus",accountStatus);
        return (List<FinanceAccount>)financeAccountDao.getListByHQLWithNamedParams(hql,params);
    }

    @Override
    public void addAccountRecord(FinanceAccount financeAccountId, Integer accountStatus,User user) {
        FinanceAccountRecord financeAccountRecord = new FinanceAccountRecord();
        financeAccountRecord.setFinanceAccount(financeAccountId);
        if (accountStatus==1){
            financeAccountRecord.setState(true);
        }else {
            financeAccountRecord.setState(false);
        }
        financeAccountRecord.setCreator(user.getUserID());
        financeAccountRecord.setCreateName(user.getUserName());
        financeAccountRecord.setCreateDate(new Date());
        financeAccountRecord.setUpdator(user.getUserID());
        financeAccountRecord.setUpdateName(user.getUserName());
        financeAccountRecord.setUpdateDate(new Date());
        financeAccountRecordDao.save(financeAccountRecord);
    }

    @Override
    public List<FinanceAccountHistoryDto> getAccountHistoryDtosByAccountId(Integer accountId) {
        HashMap<String,Object> params = new HashMap<>();
        String hql = "select new cn.sphd.miners.modules.finance.dto.FinanceAccountHistoryDto(id,updateName,updateDate,initialAmount) from FinanceAccountHistory where accountId_=:accountId";
        params.put("accountId",accountId);
        List<FinanceAccountHistoryDto> financeAccountHistoryDtos = (List<FinanceAccountHistoryDto>)financeAccountHistoryDao.getListByHQLWithNamedParams(hql,params);
        return financeAccountHistoryDtos;
    }

    @Override
    public void updateAccount(FinanceAccount financeAccount, Integer isBasic, String name, String bankName, String account, Double initialAmount, String memo,User user,Organization org) {
//        FinanceAccount financeAccount = this.getAccountById(accountId);
        if (financeAccount!=null){
            BigDecimal oldInitialAmount = financeAccount.getInitialAmount();  //原来的初始金额
            FinanceAccountHistory financeAccountHistoryNext =  this.getFinanceAccountHistoryNext(financeAccount.getId());    //取上一次的修改记录信息,若没值则是添加账户时，未添加历史表
            if (financeAccountHistoryNext==null){
                financeAccountHistoryNext = this.updateFinanceAccountToFinanceAccountHistory(financeAccount,user,0,null);
            }
            if (isBasic!=null){
                financeAccount.setIsBasic(isBasic);
            }
            if (!MyStrings.nulltoempty(name).isEmpty()){
                financeAccount.setName(name);
            }
            if (!MyStrings.nulltoempty(bankName).isEmpty()){
                financeAccount.setBankName(bankName);
            }
            if (!MyStrings.nulltoempty(account).isEmpty()){
                financeAccount.setAccount(account);
            }
            if (oldInitialAmount.doubleValue()!=initialAmount) {  //初始金额进行了修改

                //现在账户的余额=账户原来的余额-修改前的初始金额+修改后的初始金额
                BigDecimal newBanlance = financeAccount.getBalance().subtract(oldInitialAmount).add(BigDecimal.valueOf(initialAmount));
                AccountPeriod accountPeriod = accountService.getAccountPeriodByMonth(financeAccount.getId(), new Date());  //账户的月结
                AccountPeriod accountPeriodDay = accountService.getAccountPeriodByDay(financeAccount.getId(),new Date());  //账户的日结

                //修改后的月结
                accountPeriod.setCredit(accountPeriod.getCredit().subtract(oldInitialAmount).add(BigDecimal.valueOf(initialAmount)));
//                accountPeriod.setDebit(accountPeriod.getDebit().add(financeAccount.getInitialAmount()));
                accountPeriod.setBalance(newBanlance);

                //修改后的日结
                accountPeriodDay.setCredit(accountPeriodDay.getCredit().subtract(oldInitialAmount).add(BigDecimal.valueOf(initialAmount)));
//                accountPeriodDay.setDebit(accountPeriodDay.getDebit().add(financeAccount.getInitialAmount()));
                accountPeriodDay.setBalance(newBanlance);

                //修改后的账户余额
                financeAccount.setCredit(financeAccount.getCredit().subtract(oldInitialAmount).add(BigDecimal.valueOf(initialAmount)));
//                financeAccount.setDebit(financeAccount.getDebit().subtract(financeAccount.getInitialAmount()).add(BigDecimal.valueOf(initialAmount)));
                financeAccount.setBalance(newBanlance);

                accountService.updateAccountPeroid(accountPeriod);  //账户额月结
                accountService.updateAccountPeroid(accountPeriodDay);  //账户的日结

                Integer preId = null;  //要修改的accountDetail中的id
                AccountDetail accountDetail = dataService.getAccountDetailByMethod(financeAccount.getOrg_(),financeAccount.getId(),"0");
                if (accountDetail!=null){
                    preId = accountDetail.getId();
                    accountDetail.setModify(true);
                }
                //账务明细表里添加一条新数据(金额为负)
                this.saveAccountDetail(user,org,financeAccount,newBanlance,oldInitialAmount.multiply(new BigDecimal(-1)),"1","初始资金原始数据","0",user.getUserName(),"2",null,"1",null,null,null,"1",null,null,null,new Date(),new Date(),preId,true);

                //账务明细表里添加一条新数据(正确)
                this.saveAccountDetail(user,org,financeAccount,newBanlance,BigDecimal.valueOf(initialAmount),"1","初始资金原始数据","0",user.getUserName(),"2",null,"1",null,null,null,"1",null,null,null,new Date(),new Date(),preId,true);
                financeAccount.setInitialAmount(BigDecimal.valueOf(initialAmount));
            }
            financeAccount.setMemo(memo);
            financeAccount.setUpdateName(user.getUserName());
            financeAccount.setUpdator(user.getUserID());
            financeAccount.setUpdateDate(new Date());
            financeAccountDao.update(financeAccount);

            //将最新修改的更新到账户历史表中
            this.updateFinanceAccountToFinanceAccountHistory(financeAccount,user,financeAccountHistoryNext.getVersionNo()+1,financeAccountHistoryNext.getId());
        }
    }

    public FinanceAccountHistory getFinanceAccountHistoryNext(Integer accountId){
        HashMap<String,Object> params = new HashMap<>();
        String hql = "from FinanceAccountHistory where accountId_=:accountId order by id desc";
        params.put("accountId",accountId);
        FinanceAccountHistory financeAccountHistory = (FinanceAccountHistory) financeAccountHistoryDao.getByHQLWithNamedParams(hql,params);  //取上一次修改的记录
        return financeAccountHistory;
    }

    public FinanceAccountHistory updateFinanceAccountToFinanceAccountHistory(FinanceAccount financeAccount,User user,Integer versionNo,Integer previousId){
        FinanceAccountHistory financeAccountHistory = new FinanceAccountHistory();
        BeanUtils.copyProperties(financeAccount,financeAccountHistory);
        financeAccountHistory.setUpdateDate(new Date());
        financeAccountHistory.setVersionNo(versionNo);
        financeAccountHistory.setPreviousId(previousId);  //上一次修改的id
        financeAccountHistory.setAccountId(financeAccount);
        if (user!=null){
            financeAccountHistory.setUpdator(user.getUserID());
            financeAccountHistory.setUpdateName(user.getUserName());
        }
        financeAccountHistoryDao.save(financeAccountHistory);  //与刚添加账户的信息一样
        return financeAccountHistory;
    }

    @Override     //type 1-根据id查找   2-根据修改前id查找
    public FinanceAccountHistory getAccountHistoryById(Integer accountHistoryDetailId,Integer type) {
        HashMap<String,Object> params = new HashMap<>();
        String hql = " from FinanceAccountHistory where";
        if (type==1){
            hql+=" id=:accountHistoryDetailId";
        }else if (type==2){
            hql+=" previousId=:accountHistoryDetailId";
        }
        params.put("accountHistoryDetailId",accountHistoryDetailId);

        return (FinanceAccountHistory) financeAccountHistoryDao.getByHQLWithNamedParams(hql,params);
    }

    @Override    //accountStatus 账户状态 0-关闭 1-启用
    public void updateAccountDetails(Integer accountId, Integer accountStatus) {
        HashMap<String,Object> params = new HashMap<>();
        String hql = "update AccountDetail set modityStatus=:modityStatus where (oppositeId = :financeAccountId or accountId_ = :financeAccountId)";
        if (accountStatus==1){   //账户由关闭到启用 ，accountStatus由3变1
            hql+=" and modityStatus=3";
            params.put("modityStatus","1"); //modityStatus 数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次) 3-关闭账户后不可修改
        }else if (accountStatus==0){   //账户由启用到关闭，accountStatus由1/null变3
            hql+=" and (modityStatus=1 or modityStatus is null)";
            params.put("modityStatus","3"); //modityStatus 数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次) 3-关闭账户后不可修改
        }
        params.put("financeAccountId",accountId);
        accountDetailDao.queryHQLWithNamedParams(hql,params);
    }

    @Override
    public List<FinanceAccountRecord> getAccountRecordsByAccountId(Integer accountId) {
        HashMap<String,Object> params = new HashMap<>();
        String hql = "from FinanceAccountRecord where accountId=:accountId";
        params.put("accountId",accountId);
        return financeAccountRecordDao.getListByHQLWithNamedParams(hql,params);
    }

    @Override
    public FinanceAccountBill getAccountBillByBusiness(Integer business, String source,String businessType) {
        Map<String,Object> params = new HashMap<>();
        String hql = "from FinanceAccountBill where business=:business and source=:source";
        params.put("business",business);
        params.put("source",source);
        if (!MyStrings.nulltoempty(businessType).isEmpty()){
            hql+=" and businessType=:businessType";
            params.put("businessType",businessType);
        }
        hql+=" and type='1' order by createDate desc";
        return (FinanceAccountBill) accountDetailDao.getByHQLWithNamedParams(hql,params);
    }

    @Override
    public FinanceAccountBill getAccountBillByBusiness(Integer business, String source,String businessType,String type) {
        Map<String,Object> params = new HashMap<>();
        String hql = "from FinanceAccountBill where business=:business and source=:source";
        params.put("business",business);
        params.put("source",source);
        if (!MyStrings.nulltoempty(businessType).isEmpty()){
            hql+=" and businessType=:businessType";
            params.put("businessType",businessType);
        }
        if (!MyStrings.nulltoempty(type).isEmpty()){
            hql+=" and type=:type";
            params.put("type",type);
        }
        hql+=" order by createDate desc";
        return (FinanceAccountBill) accountDetailDao.getByHQLWithNamedParams(hql,params);
    }

    @Override
    public List<FinanceAccount> getAccountKinds(Integer oid, Integer accountType, String isPublic, Integer isBasic,Integer accountStatus) {
        HashMap<String,Object> params = new HashMap<>();
        String hql = "from FinanceAccount where org_=:oid";
        params.put("oid",oid);
        if (accountType!=null){
            hql+=" and accountType=:accountType";
            params.put("accountType",accountType);
        }
        if (!MyStrings.nulltoempty(isPublic).isEmpty()){
            hql+=" and isPublic=:isPublic";
            params.put("isPublic",isPublic);
        }
        if (isBasic!=null){
            hql+=" and isBasic=:isBasic";
            params.put("isBasic",isBasic);
        }
        if (accountStatus!=null){
            hql+= " and accountStatus=:accountStatus";
            params.put("accountStatus",accountStatus);
        }
        List<FinanceAccount> financeAccounts = financeAccountDao.getListByHQLWithNamedParams(hql,params);
        return financeAccounts;
    }

    /**
     *
     * @param user
     * @param updateAddAccounts 修改或添加的内容{
     * Integer accountId：账户id(修改的传，新增的不传), String account：帐户
     * 	String summary：摘要, String memo：备注 ,String isPublic：是否为对公帐号:1-是 0-否
     * 	String bankName：开户行(银行名称) ,String name：账户名称 ,Integer cashable：是否可取现  1：可取现  2：不可取现
     * 	Integer isBasic：是否是 基本户 1-是，0-否}
     * @return
     */
    @Override
    public Map<String, Object> updateAndAddAccount(User user, String updateAddAccounts) {
        Map<String,Object> map = new HashMap<>();
        JSONArray updateAddAccountList = JSONArray.fromObject(updateAddAccounts);
        for(int i = 0; i < updateAddAccountList.size(); i++){
            JSONObject jo = JSONObject.fromObject(updateAddAccountList.get(i));
            Integer accountId = jo.getInt("accountId"); //账户id
            if (accountId!=null){  //在原有的账户上进行修改，但是金额不可以修改，其他的内容修改后不留修改记录
                FinanceAccount financeAccount = financeAccountDao.get(accountId);
                Integer isBasic = jo.getInt("isBasic");//是否是基本户 1-是，0-否
                String name = jo.getString("name"); //账户名称
                String bankName = jo.getString("bankName"); //开户行
                String account=jo.getString("account"); //帐户
//                Double initialAmount：初始金额（金额不修改）
                String memo = jo.getString("memo"); //备注

                FinanceAccountHistory financeAccountHistory =  this.getFinanceAccountHistoryNext(financeAccount.getId());    //取上一次的修改记录信息,若没值则是添加账户时，未添加历史表
                if (financeAccountHistory==null){
                    financeAccountHistory = this.updateFinanceAccountToFinanceAccountHistory(financeAccount,user,0,null);
                }
                if (isBasic!=null){
                    financeAccount.setIsBasic(isBasic);
                    financeAccountHistory.setIsBasic(isBasic);
                }
                if (StringUtils.isNotEmpty(name)){
                    financeAccount.setName(name);
                    financeAccountHistory.setName(name);
                }
                if (StringUtils.isNotEmpty(bankName)){
                    financeAccount.setBankName(bankName);
                    financeAccountHistory.setBankName(bankName);
                }
                if (StringUtils.isNotEmpty(account)){
                    financeAccount.setAccount(account);
                    financeAccountHistory.setAccount(account);
                }
                financeAccount.setMemo(memo);
                financeAccount.setUpdateName(user.getUserName());
                financeAccount.setUpdator(user.getUserID());
                financeAccount.setUpdateDate(new Date());
                financeAccountDao.update(financeAccount);

                financeAccountHistory.setMemo(memo);
                financeAccountHistory.setUpdateName(user.getUserName());
                financeAccountHistory.setUpdator(user.getUserID());
                financeAccountHistory.setUpdateDate(new Date());
                financeAccountHistoryDao.update(financeAccountHistory);
            }else {  //新增的银行账户
                String account=jo.getString("account"); //帐户
                String summary=jo.getString("summary"); //摘要
                String memo = jo.getString("memo"); //备注
                String isPublic = jo.getString("isPublic"); //是否为对公帐号:1-是 0-否
                String name = jo.getString("name"); //账户名称
                String bankName = jo.getString("bankName"); //开户行
                Integer cashable = jo.getInt("cashable");//是否可取现  1：可取现  2：不可取现
                Integer isBasic = jo.getInt("isBasic");//是否是基本户 1-是，0-否

                FinanceAccount financeAccount = new FinanceAccount();
                financeAccount.setAccount(account);
                financeAccount.setSummary(summary);
                financeAccount.setMemo(memo);
                financeAccount.setIsPublic(isPublic);
                financeAccount.setName(name);
                financeAccount.setBankName(bankName);
                financeAccount.setCashable(cashable);
                financeAccount.setIsBasic(isBasic);

                this.addAccount(financeAccount,user,user.getOrganization());
            }
        }
        map.put("status",1);
        map.put("content","操作成功");
        return map;
    }

    @Override
    public void deleteFinanceAccount(FinanceAccount financeAccount) {
        String hql="delete from AccountPeriod where accountId=:fid";
        Map<String,Object> map=new HashMap<>();
        map.put("fid",financeAccount.getId());
        accountPeriodDao.queryHQLWithNamedParams(hql,map);
        financeAccountDao.delete(financeAccount);
    }
}
