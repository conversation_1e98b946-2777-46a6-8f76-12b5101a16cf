package cn.sphd.miners.modules.equipment.controller;

import java.util.List;

import cn.sphd.miners.modules.equipment.entity.TEquModelHistory;
import cn.sphd.miners.modules.equipment.service.ITEquModelHistoryService;
import cn.sphd.miners.modules.mci.utils.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import static cn.sphd.miners.modules.mci.utils.AjaxResult.toAjax;

/**
 * 装备管理_型号历史
 * 20230209 1.247装备器具新增
 * Controller
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Controller
@RequestMapping("/equipment/model/history")
public class EquModelHistoryController {
    private String prefix = "system/history";

    @Autowired
    private ITEquModelHistoryService tEquModelHistoryService;

    @GetMapping()
    public String history() {
        return prefix + "/history";
    }

    /**
     * 查询装备管理_型号历史
     * 20230209 1.247装备器具新增
     * 列表
     */
    @PostMapping("/list")
    @ResponseBody
    public List list(TEquModelHistory tEquModelHistory) {
        List<TEquModelHistory> list = tEquModelHistoryService.selectTEquModelHistoryList(tEquModelHistory);
        return list;
    }


    /**
     * 新增装备管理_型号历史
     * 20230209 1.247装备器具新增
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add";
    }

    /**
     * 新增保存装备管理_型号历史
     * 20230209 1.247装备器具新增
     */

    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(TEquModelHistory tEquModelHistory) {
        return toAjax(tEquModelHistoryService.insertTEquModelHistory(tEquModelHistory));
    }

    /**
     * 修改装备管理_型号历史
     * 20230209 1.247装备器具新增
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        TEquModelHistory tEquModelHistory = tEquModelHistoryService.selectTEquModelHistoryById(id);
        mmap.put("tEquModelHistory", tEquModelHistory);
        return prefix + "/edit";
    }

    /**
     * 修改保存装备管理_型号历史
     * 20230209 1.247装备器具新增
     */

    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(TEquModelHistory tEquModelHistory) {
        return toAjax(tEquModelHistoryService.updateTEquModelHistory(tEquModelHistory));
    }

    /**
     * 删除装备管理_型号历史
     * 20230209 1.247装备器具新增
     */

    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(tEquModelHistoryService.deleteTEquModelHistoryByIds(ids));
    }

    @GetMapping("/histories")
    @ResponseBody
    public List histories(Long id,Long operation){
        return tEquModelHistoryService.histories(id,operation);
    }

    @GetMapping("/historyDetail")
    @ResponseBody
    public List historyDetail(Long id,Long operation){
        return tEquModelHistoryService.histories(id, operation);
    }
}
