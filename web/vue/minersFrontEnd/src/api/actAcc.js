
import {request, noloadingHttp} from "@/utils/request";

/**
 * <AUTHOR>
 * @description h5 连接页面 被冻结的数据与我无关，因为这个手机号是本人新购买的！
 * @date Create at 2024/9/5 08:04
 * @returns   success 1 操作成功 / success 0 验证失败 抛出失败信息 ”您已操作完成，无需再操作了！
 */
export function unlockAccByActiveCode() {
    return request({
        url: '/auth/unlockAccByActiveCode.do',
    })
}


/**
 * <AUTHOR>
 * @description 把激活码转换成账号信息（进入h5直接调用）
 * @date Create at 2024/9/26 08:04
 * @params: activeCode 激活码
 * @returns    data 中 是一个待激活账号的 token
 */
export function getUnlockingToken(data) {
    return request({
        url: '/auth/getUnlockingToken.do',
        data
    })
}



/**
 * <AUTHOR>
 * @description 自定义的安全问题 不含答案 （h5验证时用）
 * @date Create at 2024/9/5 08:04
 * @returns data中 [{question问题内容 ，key 对应答案},{},{}]
 */
export function h5OwnSecurityQuestion() {
    return request({
        url: '/authQuestion/h5OwnSecurityQuestion.do',
    })
}



/**
 * <AUTHOR>
 * @description 回答自定义的问题答案验证 （h5验证时用）
 * @date Create at 2024/9/29 08:04
 * @params： answers（传值字段名）= [{id 问题id ，answer 回答内容},{id 问题id ，answer 回答内容}] (格式为 json 转String的字符串)
 * @returns  success 1 认证通过 / success 0 验证失败 抛出失败信息 “对不起，您的答案不对”。
 */
export function h5AnswerSecurityQuestion(data) {
    return request({
        url: '/authQuestion/h5AnswerSecurityQuestion.do',
        data
    })
}


/**
 * <AUTHOR>
 * @description  给选择领地列表前，判断是否有新领地 ，给新旧领地选择列表 （h5验证时用）
 * @date Create at 2024/10/10 08:04
 * @returns  data中 true 有新领地， false 没有新领地
 */
export function dupAcc(data) {
    return request({
        url: '/auth/dupAcc.do',
        data
    })
}

/**
 * <AUTHOR>
 * @description h5 连接 页面 安全问题验证成功后 选择 使用被冻结的原领地
 * @date Create at 2024/10/10 08:04
 * @returns   success 1 操作成功 / success 0 验证失败 抛出失败信息 ”新账号不存在“
 */
export function selectOldAccByActiveCode(data) {
    return request({
        url: '/auth/selectOldAccByActiveCode.do',
        data
    })
}

/**
 * <AUTHOR>
 * @description  h5 连接 页面 安全问题验证成功后 选择 使用新领地
 * @date Create at 2024/10/10 08:04
 * @returns   success 1 操作成功 / success 0 验证失败 抛出失败信息 ”新账号不存在“
 *  {"page":null,"success":1,"data":"新账号不存在","error":null,"others":null}
 */
export function selectNewAccByActiveCode(data) {
    return request({
        url: '/auth/selectNewAccByActiveCode.do',
        data
    })
}

/**
 * <AUTHOR>
 * @description  默认生成安全问题 第一题 （h5验证时用）
 * @date Create at 2024/10/10 08:04
 * @returns   data中 机构名列表 [机构名1，机构名2，机构名3，机构名4]
 */
export function h5DefaultQuestionBankOne(data) {
    return request({
        url: '/authQuestion/h5DefaultQuestionBankOne.do',
        data
    })
}

/**
 * <AUTHOR>
 * @description   验证 默认第一题 回答是否正确 （h5验证时用）
 * @date Create at 2024/10/10 08:04
 * @params  orgName 选择的机构
 * @returns  返回值：
 *      success 1 回答正确 ,data 返回机构人员数量 /
 *      success 0 验证失败 抛出失败信息
 *          “对不起，您的答案不对！再选错"+number+"次，您私人领地内的数据将无法再访问！”，
 *          “对不起，您没能答对安全问题，您私人领地内的数据已无法再访问！您依旧可登录系统，但需请公司总务协助操作！”。
 */
export function h5VerifyQuestionBankOne(data) {
    return request({
        url: '/authQuestion/h5VerifyQuestionBankOne.do',
        data
    })
}

/**
 * <AUTHOR>
 * @description  验证 默认第二题 回答是否正确 （h5验证时用）
 * @date Create at 2024/10/10 08:04
 * @params  orgName 选择的机构， userNames 输入的人名 （传值格式 userNames=姓名1,姓名2，姓名3 ）
 * @returns  success 1 本次认证通过 / success 0 验证失败 抛出失败信息 “对不起，您的答案不对！再选错"+number+"次，您私人领地内的数据将无法再访问！”，“对不起，您没能答对安全问题，您私人领地内的数据已无法再访问！您依旧可登录系统，但需请公司总务协助操作！”。
 */
export function h5VerifyQuestionBankTwo(data) {
    return request({
        url: '/authQuestion/h5VerifyQuestionBankTwo.do',
        data
    })
}






