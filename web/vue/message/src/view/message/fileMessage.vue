<template>
  <div id="issueFile">
    <TY_NavTop :title="fileTitle"></TY_NavTop>
    <div class="container" v-loading="loading">
      <div class="place" v-if="fileDetails.teminateState === '1'">
        <div class="placeContainer">
          <p>这是一条<span class="color-red">文件废止的申请</span></p>
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          <p class="fileTtl" v-text="fileDetails.name"></p>
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          <div class="line-p">
            <div class="tt-cell">文件编号</div>
            <div class="con-cell" v-text="fileDetails.fileSn"></div>
          </div>
          <div class="line-p">
            <div class="tt-cell">保存位置</div>
            <div class="con-cell" v-html="filePosition"></div>
          </div>
          <div class="line-p">
            <div class="tt-cell">文件类型</div>
            <div class="con-cell" v-text="fileDetails.version">xls</div>
          </div>
          <div class="line-p">
            <div class="tt-cell">文件大小</div>
            <div class="con-cell">{{fileDetails.size | formatByte}}</div>
          </div>
          <div class="line-p" v-if="fileDetails.changeNum">
            <div class="tt-cell">版本号</div>
            <div class="con-cell">G{{fileDetails.changeNum}}</div>
          </div>
          <div class="line-p" v-if="!fileDetails.changeNum">
            <div class="tt-cell">说明</div>
            <div class="con-cell" v-text="fileDetails.content" :title="fileDetails.content"></div>
          </div>
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          <div class="line-btn-groups" v-if="fileDetails.teminateState !== '1'">
            <ul>
              <li>
                <span class="seeTtl">待审批的文件</span>
                <span class="fc-btn-see" @click="showDialog(0)">查看</span>
              </li>
              <li v-if="fileDetails.changeNum">
                <span class="seeTtl">历史版本</span>
                <span class="fc-btn-see">查看</span>
              </li>
            </ul>
          </div>
          <div class="line-btn-groups" v-if="fileDetails.teminateState === '1'">
            <ul>
              <li>
                <span class="seeTtl">将废止的文件</span>
                <span class="fc-btn-see" @click="showDialog(2)">查看</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div class="place">
        <div class="placeContainer">
          <div class="record">
            <span>审批记录</span>
            <div class="script">
              <div v-for="(item ,index) in approveRecord" :key="index">
                <div v-if="index === 0">
                  <span class="sm-ttl">申请人:</span>
                  <span class="sm-con" v-text="item.userName"></span>
                  <span>{{item.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                </div>
                <div v-if="index != 0">
                  <span class="sm-ttl">审批人:</span>
                  <span class="sm-con" v-text="item.userName"></span>
                  <span>{{item.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                </div>
                <div v-if="index === approveRecord.length-1">
                  <span class="sm-ttl" v-if="!item.toMid">审批人:</span>
                  <span class="sm-ttl" v-else>{{titleName}}审批人:</span>
                  <span class="sm-con" v-text="item.toUserName"></span>
                  <span>{{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="placeContainer lineOp" v-if="approveStatus == '3'">
          <div class="record">
            <span>驳回理由</span>
            <div class="script">
              <p v-text="fileDetails.applyMemo"></p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      title="待审批的文件"
      :visible.sync="pendingDialog"
      width="70%">
      <el-form class="preview">
        <el-form-item>
          <el-radio-group v-model="browser">
            <el-radio :disabled="seeOnlineDisabled" :label="1">在线预览</el-radio>
            <el-radio :label="2">下载</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button handle-center">
          <a class="fc-btn-normal" @click="pendingDialog = false">取消</a>
          <span class="fc-btn ui-btn_info" ref="operate" :path="fileDetails.path" :download="fileDetails.name + '.' + fileDetails.version" @click="oprationFile($event)">确定</span>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { canSeeOnline } from '@/js/common'
export default {
  name: 'fileMessageDetail',
  data () {

    return {
      loading: true,
      pendingDialog: false,
      categoryName: '',
      fileDetails: [],
      approveRecord: [],
      listenersUid: [],
      browser: 1,
      fileTitle: '',
      approveStatus: 2,
      seeOnlineDisabled: true,
      titleName: ''
    }
  },
  computed: {
    bytesToSize () {
      let sizeStr = ''
      let fileSize = this.fileDetails.size
      sizeStr = fileSize < 102400 ? parseFloat(fileSize / 1024).toFixed(2) + 'KB' : parseFloat(fileSize / 1048576).toFixed(2) + 'MB'
      return sizeStr
    },
    filePosition () {
      let pos = this.categoryName
      let posArr = pos.split('/')
      let posStr = ''
      for (let a = 0; a < posArr.length; a++) {
        if (a === posArr.length - 1) {
          posStr += '<span class="font-orange">' + posArr[a] + '</span>'
        } else {
          posStr += posArr[a] + '/'
        }
      }
      return posStr
    }
  },
  created: function () {
    var _this = this
    var fileId = this.$route.params.id
    this.sphdSocket.send('onePublishFileMessage', { 'session': this.sphdSocket.sessionid, 'hisId': fileId, 'userID': this.sphdSocket.user.userID })
    this.listenersUid = [
      this.sphdSocket.subscribe('onePublishFileMessage', function (data) {
        var fileData = JSON.parse(data)
        _this.categoryName = fileData.categoryName
        _this.fileDetails = fileData.resHis
        _this.approveRecord = fileData.listAp
        _this.approveStatus = fileData.resHis.approveStatus

        let teminateState = fileData.resHis.teminateState
        if (teminateState === '1') {
          _this.titleName = '废止'
        } else {
          if (fileData.resHis.changeNum > 0) {
            _this.titleName = '换版'
          } else {
            _this.titleName = '发布'
          }
        }
        _this.fileTitle = '文件'+_this.titleName+'申请'

        _this.loading = false
      }, null, 'session', this.sphdSocket.sessionid),
      this.sphdSocket.subscribe('fileUpdateState', function () {
        _this.$router.push({
          path: '/releaseApply',
          name: 'releaseApply'
        })
      }, null, 'custom', fileId)
    ]
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    stopFileProcess () {
      var userId = this.sphdSocket.user.userID
      var fileId = this.$route.params.id
      var processLength = this.approveRecord.length * 1 - 1
      var processId = this.approveRecord[processLength].id
      this.sphdSocket.send('stopFileProcess', { 'userID': userId, 'hisId': fileId, 'processId': processId })
    },
    oprationFile (event) {
      let el = event.currentTarget
      window.parent.previewOrDownload(el, this.browser)
    },
    showDialog: function (type) {
      if (this.fileDetails.isDeleted === true) {
        this.$kiko_message('该文件已被删除，您已无法继续查看！')
        return false
      }
      if (this.fileDetails.enabled === false) {
        this.$kiko_message('该文件已被禁用，您已无法继续查看！')
        return false
      }
      let fileHandleDetail = {}
      if (type === 0) {
        this.dialogTitle = '待审批的文件'
        fileHandleDetail = {
          path: this.fileDetails.path,
          name: this.fileDetails.name,
          version: this.fileDetails.version
        }
      }  else if (type === 1) {
        this.dialogTitle = '当前的有效文件'
        fileHandleDetail = {
          path: this.filePath,
          name: this.fileDetails.name,
          version: this.fileVersion
        }
      } else if (type === 2) {
        this.dialogTitle = '将废止的文件'
        fileHandleDetail = {
          path: this.fileDetails.path,
          name: this.fileDetails.name,
          version: this.fileDetails.version
        }
      }
      this.seeOnlineDisabled = !canSeeOnline(fileHandleDetail.version)
      this.fileHandleDetail = fileHandleDetail
      this.pendingDialog = true
      if (this.seeOnlineDisabled) {
        this.browser = 2
      } else {
        this.browser = 1
      }
    }
  }
}
</script>
