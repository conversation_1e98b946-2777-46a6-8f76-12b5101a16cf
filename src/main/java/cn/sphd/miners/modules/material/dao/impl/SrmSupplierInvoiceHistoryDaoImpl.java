package cn.sphd.miners.modules.material.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.material.dao.SrmSupplierInvoiceHistoryDao;
import cn.sphd.miners.modules.material.entity.SrmSupplierHistory;
import cn.sphd.miners.modules.material.entity.SrmSupplierInvoice;
import cn.sphd.miners.modules.material.entity.SrmSupplierInvoiceHistory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
@Repository
public class SrmSupplierInvoiceHistoryDaoImpl extends BaseDao<SrmSupplierInvoiceHistory, Serializable> implements SrmSupplierInvoiceHistoryDao {
    @Override
    public Integer saveHis(SrmSupplierInvoice srmSupplierInvoice,Integer hisId) {
        SrmSupplierInvoiceHistory srmSupplierInvoiceHistory=new SrmSupplierInvoiceHistory();
        BeanUtils.copyProperties(srmSupplierInvoice,srmSupplierInvoiceHistory);
        srmSupplierInvoiceHistory.setSupplierInvoice(srmSupplierInvoice.getId());
        srmSupplierInvoiceHistory.setSupplier( hisId);
        save(srmSupplierInvoiceHistory);
        return srmSupplierInvoiceHistory.getId();
    }
}
