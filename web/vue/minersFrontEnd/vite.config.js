import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import path from 'path';

// https://vitejs.dev/config/
//export default defineConfig({
//  plugins: [
//    vue(),
//  ],
//  resolve: {
//    alias: {
//      '@': fileURLToPath(new URL('./src', import.meta.url))
//    }
//  }
//})
export default ({command, mode}) => {
  const envConfig = loadEnv(mode, './', ['VITE_', 'VUE_'])
  console.log('envConfig',envConfig)
  let config = {
    assetsInclude:["**/*.xls", "**/*.xlsx", ],
    plugins: [
      vue(),
      createSvgIconsPlugin({
        // 指定需要缓存的图标文件夹
        iconDirs: [path.resolve(process.cwd(), 'src/icons/svg')],
        // 指定symbolId格式
        symbolId: 'icon-[dir]-[name]',
        // 自定义插入位置
        inject: 'body-last',
        // 自定义DOM id
        customDomId: '__svg__icons__dom__'
      })
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },

    css: {
      preprocessorOptions: {
        scss: {
          silenceDeprecations: ['legacy-js-api'],
          api: 'modern',
          additionalData: `@use "@/style/variable.scss" as * ;`, // 此处全局的scss文件
        }
      }
    },
    define: {
      'process.env': envConfig
    },
    base: './',
    publicPath: './',
    transpileDependencies: true,
    build: {
      chunkSizeWarningLimit: 65536,
      target: [ 'es2022' ]
    },
    performance: {
      hints: 'warning',
      maxEntrypointSize: 1048576,
      maxAssetSize: 33554432
    }
  }
  return defineConfig(config)
}
