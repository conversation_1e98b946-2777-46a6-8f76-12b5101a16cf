package cn.sphd.miners.modules.accountantReportTax.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 报表dto
 * */
public class AcctReportDetailDto implements Serializable {

    private Integer id;
    private Integer reportId;//报表ID
    private Integer reportHisId;//历史报表ID
    private Integer detailId;//报税ID
    private String reportName;//报表名称
    private String createName;//报税人
//    private String frequency;//频次:0,-随机,1-日,2-周,3-旬,4-半月,5-月,6-季,7-半年,8-年
    private String latestDeclare;//最后编辑的日期
    private Date factReportDate;//实际申报的日期
    private List<AcctTaxDto> taxList;//税种列表
    private String startPeriod;//申报的开始日期
    private String endPeriod;//申报的结束日期
    private Date createDate;//数据创建时间

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Integer getReportHisId() {
        return reportHisId;
    }

    public void setReportHisId(Integer reportHisId) {
        this.reportHisId = reportHisId;
    }

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getReportId() {
        return reportId;
    }

    public void setReportId(Integer reportId) {
        this.reportId = reportId;
    }

    public String getReportName() {
        return reportName;
    }

    public void setReportName(String reportName) {
        this.reportName = reportName;
    }

//    public String getFrequency() {
//        return frequency;
//    }
//
//    public void setFrequency(String frequency) {
//        this.frequency = frequency;
//    }

    public String getLatestDeclare() {
        return latestDeclare;
    }

    public void setLatestDeclare(String latestDeclare) {
        this.latestDeclare = latestDeclare;
    }

    public List<AcctTaxDto> getTaxList() {
        return taxList;
    }

    public void setTaxList(List<AcctTaxDto> taxList) {
        this.taxList = taxList;
    }

    public String getStartPeriod() {
        return startPeriod;
    }

    public void setStartPeriod(String startPeriod) {
        this.startPeriod = startPeriod;
    }

    public String getEndPeriod() {
        return endPeriod;
    }

    public void setEndPeriod(String endPeriod) {
        this.endPeriod = endPeriod;
    }

    public Date getFactReportDate() {
        return factReportDate;
    }

    public void setFactReportDate(Date factReportDate) {
        this.factReportDate = factReportDate;
    }
}
