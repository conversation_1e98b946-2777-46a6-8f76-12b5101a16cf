<template>
  <div class="ui-cells">
    <a class="ui-cell" v-for="(item, index) in applyList3" v-bind:key="index" @click="jump(item.id)">
      <div class="ui-cell__bd">
        <div class="ui-cell_con">
          <div><span class="info">{{item.name}}</span><span class="info">申请开票金额 {{item.invoice_amount | numberFilter(2)}}元</span></div>
          <div><span class="info">{{item.create_name}} {{item.create_date | formatDate}}</span>
            <span class="info">需要发票数量{{item.invoice_count}}
                            <span v-if="item.invoice_count !== '--'">张</span>
            </span></div>
        </div>
      </div>
      <div class="ui-cell__ft"></div>
    </a>
  </div>
</template>

<script>
import { formatDate, numberFilter } from '../../../js/common'
export default {
  name: 'invoiceApply3',
  filters: {
    formatDate,
    numberFilter
  },
  data () {
    return {
      list: this.$store.getters.getInvoiceList,
      listeners: [],
      applyList3: [] // 待开票
    }
  },
  created () {
    let _this = this
    _this.applyList3 = (this.list && this.list.applyList3) || []
    _this.$store.dispatch('setNewInvoiceNav', 3)
    _this.listeners = [
      // 已批准待开票
      _this.sphdSocket.subscribe('applyInvoice', _this.subscribeCallBack, null, 'user')
    ]
  },
  destroyed: function () {
    let _this = this
    _this.listeners.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    jump: function (id) {
      this.$router.push({
        name: `applyInvoiceDetail`,
        params: {
          id: id
        }
      })
    },
    subscribeCallBack: function (res) {
      console.log('3列表回调：', res)
      this.$store.dispatch('setNewInvoiceList')
    }
  },
  watch: {
    '$store.state.invoiceList': {
      immediate: true,
      deep: true,
      handler (invoiceList) {
        this.list = invoiceList
        this.applyList3 = (this.list && this.list.applyList3) || []
      }
    }
  }
}
</script>
