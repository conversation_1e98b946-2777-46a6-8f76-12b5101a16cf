<template>
  <div class="box">
    <div class="head">
      <PageHeader :title="tit" :showClose=true :showBack=true
                  @mycloseFun="handleCustomClose">
      </PageHeader>
    </div>
    <div class="sticky spaceBetween padding-0-15 h35f14 ty-fff" v-if="financeReturn">
      <span>{{financeReturn.type == 1 ? '转账支票' : financeReturn.type == 2 ? '承兑汇票' : 'err'}}详情</span>
      <span v-if="financeReturn.state==1" style="color: #f58410">此{{financeReturn.type == 1 ? '支' : financeReturn.type == 2 ? '汇' : 'err'}}票尚在手中</span>
      <span v-else-if="financeReturn.state==4" style="color: #f58410">此{{financeReturn.type == 1 ? '支' : financeReturn.type == 2 ? '汇' : 'err'}}票已支付出去</span>
      <span v-else-if="financeReturn.state==2" style="color: #f58410">此{{financeReturn.type == 1 ? '支' : financeReturn.type == 2 ? '汇' : 'err'}}票已存入银行</span>
    </div>
    <div class="re margin_top_16" v-if="financeReturn">
      <div class="ty-fff sp">
        <p>
          <span>{{financeReturn.type == 1 ? '支' : financeReturn.type == 2 ? '汇' : 'err'}}票号</span>
          <span>{{ financeReturn.returnNo }}</span>
        </p>
        <p>
          <span>票据金额</span>
          <span>{{ financeReturn.amount }}</span>
        </p>
        <p>
          <span>{{financeReturn.type == 1 ? '支' : financeReturn.type == 2 ? '汇' : 'err'}}票到期日</span>
          <span>{{ new
          Date(financeReturn.expireDate).format('yyyy-MM-dd') }}</span>
        </p>
        <p>
          <span>出具{{financeReturn.type == 1 ? '支' : financeReturn.type == 2 ? '汇' : 'err'}}票银行</span>
          <span>{{ financeReturn.bankName }}</span>
        </p>
        <p>
          <span>付款单位</span>
          <span>{{ financeReturn.payer }}</span>
        </p>
        <p>
          <span>{{financeReturn.type == 1 ? '出具支' : financeReturn.type == 2 ? '原始出具汇' : 'err'}}票单位</span>
          <span>{{ financeReturn.originalCorp }}</span>
        </p>
        <p>
          <span>备注</span>
          <span class="hH">{{ financeReturn.memo }}</span>
        </p>
      </div>
      <div class="sp">
        <p class="margin_top_16 ty-fff">
          <span>收到{{financeReturn.type == 1 ? '支' : financeReturn.type == 2 ? '汇' : 'err'}}票日期</span>
          <span>{{ new
          Date(financeReturn.receiveDate).format('yyyy-MM-dd') }}</span>
        </p>
      </div>
      <p class="end-text ty-fff" style="line-height: 25px;padding: 5px 15px">
        录入者
        <span class="padding-0-10">{{ financeReturn.createName }}</span>
        <span>{{ new
        Date(financeReturn.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
      </p>
      <div v-if="financeReturn.state==4" class="margin_top_16 ty-fff sp">
        <p>
          <span>收款单位</span>
          <span>{{ financeReturn.oppositeCorp }}</span>
        </p>
        <p>
          <span>实际付款日期</span>
          <span>{{ new
          Date(financeReturn.receiveDate).format('yyyy-MM-dd') }}</span>
        </p>
        <div>
          <p class="end-text ty-fff" style="line-height: 25px;padding: 5px 15px">
            出纳
            <span class="padding-0-10">{{ financeReturn.updateName }}</span>
            <span>{{ new
            Date(financeReturn.updateDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
          </p>
        </div>
      </div>
      <div v-if="financeReturn.state==2" class="ty-fff margin_top_16 sp">
        <p class="spaceBetween">
          <span>存入银行时间</span>
          <span>{{ new
          Date(financeReturn.depositDate).format('yyyy-MM-dd') }}</span>
        </p>
        <p class="spaceBetween">
          <span>所存入的账户</span>
          <span class="hH">{{financeReturn.saveBankName}}{{ financeReturn.accout }}</span>
        </p>
        <p class="spaceBetween">
          <span>到账时间</span>
          <span>{{ new
          Date(financeReturn.receiveAccountDate).format('yyyy-MM-dd') }}</span>
        </p>
        <p class="spaceBetween">
          <span>存入经手人</span>
          <span>{{ financeReturn.depositorName }}</span>
        </p>
      </div>
      <p class="end-text ty-fff" style="line-height: 25px;padding: 5px 15px" v-if="financeReturn.state==2">
        录入者
        <span class="padding-0-10">{{ financeReturn.createName }}</span>
        <span>{{ new
        Date(financeReturn.updateDate).format('yyyy-MM-dd hh:mm:ss') ? new Date(financeReturn.updateDate).format('yyyy-MM-dd hh:mm:ss')
        :
        new
        Date(financeReturn.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
      </p>
    </div>

  </div>
</template>

<script>
import {getReturnDetail} from '@/api/financeBill'

export default {
  name: "moneyOrderDetail",
  data() {
    return {
      tit: "财会",
      financeReturn: null
    }
  },
  computed: {
    billId() {
      return this.$route.query.billId
    }
  },
  methods: {
    handleCustomClose() {
      this.$router.push("/caikuai")
    },
    getDetail() {
      const params = {returnId: this.billId}
      getReturnDetail(params).then(res1 => {
        this.financeReturn = res1.data.data.financeReturn
      }).catch(error => {
        console.error('Error:', error);
      })
    }
  },
  mounted() {
    this.getDetail();
  },
}
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";

.head {
  position: fixed;
  width: 100%;
  z-index: 999;
}

.re {
  position: relative;
  top: 40px;

  //> :nth-child(1), > :nth-child(2), > :nth-child(4), > :nth-child(6) {
  .sp, > p {
    > p {
      padding: 4px 15px;
      display: flex;
      justify-content: space-between;
      line-height: 24px;

      > :nth-child(1) {
        width: 40%;
      }

      > :nth-child(2) {
        width: 60%;
      }
    }
  }
}
</style>