<template>
  <div id="applyFileQueryPer" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="查询"></TY_NavTop>
    <div class="container">
      <div class="ui-message">
        <div class="tip tip-success">自 {{beginTime | formatDay('YYYY-MM-DD')}} 至 {{endTime | formatDay('YYYY-MM-DD')}}</div>
        <div class="sendContainer overTimeQuery">
          <div v-if="eventType == 1 && approveStatus == 2">
            发布文件共{{queryNum}}个
          </div>
          <div v-if="eventType == 1 && approveStatus == 3">
            被驳回的文件共{{queryNum}}个
          </div>
          <div v-if="eventType == 2 && approveStatus == 2">
            换版文件共{{queryNum}}个
          </div>
          <div v-if="eventType == 2 && approveStatus == 3">
            被驳回的文件共{{queryNum}}个
          </div>
          <div v-if="eventType == 3 && approveStatus == 2">
            您经手且最终被批准的文件共{{queryNum}}个
          </div>
          <div v-if="eventType == 3 && approveStatus == 3">
            您经手且最终被驳回的文件共{{queryNum}}个
          </div>
          <div v-if="eventType == 4 && approveStatus == 2">
            已发布或换版的文件共{{queryNum}}个
          </div>
          <div v-if="eventType == 4 && approveStatus == 3">
            驳回的发布或换版申请共{{queryNum}}次
          </div>
        </div>
      </div>
      <div>
        <div class="ui-cells">
          <a class="ui-cell" @click="screenJump(item.id)" v-for="(item, index) in queryList" :key="index">
            <div class="fileLists">
              <div class="fileType file_txt"></div>
              <div class="fileInfo">
                <div class="fileName" v-text="item.name" :title="item.name"></div>
                <div class="fileDetail">
                  <div class="fileDetail">{{item.updateName || item.createName}} {{(item.updateTime || item.createTime) |formatDay('YYYY-MM-DD HH:mm:ss') }}</div>
                </div>
              </div>
              <div class="fileFormat">
                <span class="fileVersion">G{{item.versionNo}}</span>
                <span class="fileNo">编号：{{item.fileSn}}</span>
              </div>
            </div>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import * as moment from 'moment'
  import 'moment/locale/zh-cn'
  export default {
    name: 'applyFileQueryResult',
    data () {
      return {
        queryNum: 0,
        queryList: [],
        eventType: 1,
        approveStatus: '',
        haveDetail: true,
        hashMapList: [],
        beginTime: '',
        endTime: '',
        getParams: {},
        loading: false
      }
    },
    created: function () {
      this.loading = true
      let paramQuery = this.$route.params.data
      if (!paramQuery) {
        paramQuery = JSON.parse(localStorage.getItem('query'))
      }
      this.getParams = paramQuery
      // eventType :3-审批 4-文管查询
      // type （3- 自定义 ，2 -仅看年报，1- 仅看月报）
      // approveStatus 2-是查已批准的 3-是查驳回的
      // timeBegin  开始时间   查询的时间 此值每次都要传，例如本月时“2019-01”，查本年时“2019”，自定义时传那个设定的开始时间“2019-01-01”
      this.eventType = paramQuery.eventType
      this.approveStatus = paramQuery.approveStatus
      let _this = this
      this.$http.post('../../../about/aboutFindingFileByVue.do', paramQuery, {
        emulateJSON: true
      }).then((response) => {
        _this.loading = false
        let data = response.body.data
        if (data) {

          let list = data.list
          _this.queryList = list
          _this.queryNum = list.length
          _this.beginTime = data.dateBegin
          _this.endTime = data.dateEnd
          // if (listResHis) {
          //   _this.haveDetail = false
          //   _this.queryList = listResHis
          // } else {
          //   _this.haveDetail = true
          //   _this.hashMapList = data.timeNumList
          // }
        } else {
          console.log('加载失败！')
        }
      }).catch(function () {
        // this.jumpError('系统错误，请重试！')
      })
    },
    methods: {
      screenJump: function (id) {
        this.$router.push({
          path: `/contentDetail/${id}/query${this.getParams.eventType}`
        })
      }
    }
  }
</script>
