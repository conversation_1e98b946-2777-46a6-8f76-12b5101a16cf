<template>
  <div>
    <div class="homer" v-show="materlon === 0">
      <div>
        <div class="main rolor">
          <div class="unSection">
            <div class="ty-left">待设置包装信息的材料</div>
            <span>共{{listun}}种</span>
            <el-button type="text" @click="mateWszChn">去设置</el-button>
          </div>
          <hr />
          <div class="lineQuery ty-clear">
            <div class="ty-left">以下为包装信息已设置的材料</div>
            <div class="ty-right searchSect">
              <div class="ty-left keywordSearch">
                <span class="searchIcon"></span>
                <input placeholder="请输入要查找商品的代号或名称" id="searchKeyBaset1" v-model="searchKeyHome" />
              </div>
              <el-button type="primary" @click="searchsure">确定</el-button>
            </div>
          </div>
          <div>
            <el-table :data="list1.data" border class="ty-table-control ty-table ty-table-headerLine">
              <el-table-column prop="code" label="材料代号" width="" align="center"></el-table-column>
              <el-table-column label="材料名称/规格/型号" width="" align="center">
                <template #default="scope">
                  {{scope.row.name}}/{{scope.row.specifications}}/{{scope.row.model}}
                </template>
              </el-table-column>
              <el-table-column prop="unit" label="计量单位" width="" align="center"></el-table-column>
              <el-table-column label="供应商" width="" align="center">
                <template #default="scope">{{scope.row.supplierNum}}个</template>
              </el-table-column>
              <el-table-column prop="gs" label="包装概述" width="" align="center"></el-table-column>
              <el-table-column label="操作" width="" align="center">
                <template #default="scope">
                  <span class="ty-color-blue" @click="havelooker(scope.row)">查看</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>

      <!--分页功能-->
      <TyPage v-if="list1.data"
              :curPage="list1.page.currentPageNo" :pageSize="list1.page.pageSize"
              :allPage="list1.page.totalPage" :pageClickFun="pageClick">
      </TyPage>
    </div>
    <div class="useprot" v-show="materlon === 1">
      <div class="rolor">
        <div class="backPage">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="gobacktun(1)">返回</span>
        </div>
        <div class="lineQuery ty-clear">
          <div class="ty-left">以下为包装信息有待设置的材料</div>
          <div class="ty-right searchSect">
            <div class="ty-left keywordSearch">
              <span class="searchIcon"></span>
              <input placeholder="请输入要查找商品的代号或名称" v-model="searchKeyUse"/>
            </div>
            <el-button type="primary" @click="searchsure2">确定</el-button>
          </div>
        </div>
        <div>
          <el-table :data="list2.data" border class="ty-table-control ty-table ty-table-headerLine">
            <el-table-column prop="code" label="材料代号" width="" align="center"></el-table-column>
            <el-table-column label="材料名称/规格/型号" width="" align="center">
              <template #default="scope">
                {{ scope.row.name }}/{{ scope.row.specifications }}/{{ scope.row.model }}
              </template>
            </el-table-column>
            <el-table-column prop="unit" label="计量单位" width="" align="center"></el-table-column>
            <el-table-column label="供应商" width="" align="center">
              <template #default="scope">{{ scope.row.supplierNum }}个</template>
            </el-table-column>
            <el-table-column label="创建" width="" align="center">
              <template #default="scope">{{ scope.row.createName }}&nbsp;{{ new Date(scope.row.createDate).format("yyyy-MM-dd hh:mm:ss") }}</template>
            </el-table-column>
            <el-table-column label="操作" width="" align="center">
              <template #default="scope">
                <span class="ty-color-blue" @click="actprocess(scope.row)" v-show="scope.row.settet === true">设置</span>
                <span class="ty-color-gray endy" v-show="scope.row.settet === false"
                      @click.stop style="cursor:not-allowed;">设置</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <!--分页功能-->
      <TyPage v-if="list2.data"
              :curPage="list2.page.currentPageNo" :pageSize="list2.page.pageSize"
              :allPage="list2.page.totalPage" :pageClickFun="pageClick2">
      </TyPage>
    </div>
    <div class="usepro2" v-show="materlon === 2">
      <div class="rolor">
        <div class="backPage ty-clear">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 litten" @click="gobacktun(1)">返回首页</span>
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="gobacktun(2)">返回上一页</span>
          <el-button class="ty-right" type="info" @click="" v-show="catok === 0" :disabled="true">编辑完成</el-button>
          <el-button class="ty-right" type="primary" @click="editcomple" v-show="catok === 1">编辑完成</el-button>
          <!--        <el-button class="ty-right" type="primary" @click="">编辑完成</el-button>Editing completed-->
        </div>
        <div class="topName">{{catlist2}}</div>
        <div class="lineQuery ty-clear">
          <div class="ty-left" style="margin-top: 7px;">本材料共<span>{{list3.length}}</span>个供应商。</div>
        </div>
        <div class="lineQuery ty-clear">
          <div class="ty-left" style="margin-top: 17px;">
            <span>请确认哪些供应商供应的本材料需要包装，并对需要包装的进行“编辑”！</span>
          </div>
          <div class="ty-right" style="margin-top: 11px;">
            <el-button type="text" @click="tunbuitedit(catunit)">批量编辑</el-button>
          </div>
        </div>
        <div>
          <el-table :data="list3" border class="ty-table-control ty-table ty-table-headerLine">
            <el-table-column prop="" label="供应商" width="" align="center">
              <template #default="scope">
                {{scope.row.codeName}}/{{scope.row.fullName}}
              </template>
            </el-table-column>
            <el-table-column prop="enabled" label="备注" width="" align="center"></el-table-column>
            <el-table-column label="供应的本材料是否需要包装" width="" align="center">
              <template #default="scope">
                <el-radio-group v-model="scope.row.outSpace" @change="choseonpont(scope.$index,scope.row.outSpace,scope.row)">
                  <el-radio v-for="option in options" :key="option.value" :label="option.value" v-show="option.isVisible"
                            @click.native="clentendk(option.value,scope.row.outSpace,scope.$index,scope.row)">{{ option.label }}</el-radio>
                  <!--                <el-radio @click.native.prevent="choseonpont(0)" :label="0">需要</el-radio>-->
                  <!--                <el-radio @click.native.prevent="choseonpont(1)" :label="1">不需要</el-radio>-->
                  <!--                <el-radio @click.native.prevent="choseonpont(2)" :label="2" v-show="optun === 1">暂不编辑</el-radio>-->
                </el-radio-group>
              </template>
            </el-table-column>
            <el-table-column label="状态" width="" align="center">
              <template #default="scope">
                <span v-show="scope.row.outSpace2 === 0 || scope.row.outSpace2 === null">尚未编辑</span>
                <span v-show="scope.row.outSpace2 === 1">已编辑</span>
                <span v-show="scope.row.outSpace2 === 2">暂不编辑</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="" align="center">
              <template #default="scope">
                <span class="ty-color-gray specinal" v-show="scope.row.outSpace3 !== 0">编辑</span>
                <span class="ty-color-blue" v-show="scope.row.outSpace3 === 0" @click="nextpict(scope.row,'',catunit)">编辑</span>
                <!--              <span class="ty-color-gray specinal" @click="" v-show="scope.$index !== scope.row.outSpace">编辑</span>-->
                <!--              <span class="ty-color-blue" @click="" v-show="scope.$index === scope.row.outSpace">编辑</span>-->
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="buctedit" v-show="materlon === 3">
      <div class="rolor">
        <div class="backPage ty-clear">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 litten" @click="gobacktun(1)">返回首页</span>
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="gobacktun(3)">返回上一页</span>
          <el-button class="ty-right" type="info" @click.stop v-show="madesue === 1" :disabled="true">确定</el-button>
          <el-button class="ty-right" type="primary" @click="morechose(catunit)" v-show="madesue === 2">确定</el-button>
        </div>
        <div class="topName">要批量编辑哪些供应商？请选择。</div>
        <div class="lineQuery">
          <span>注：已编辑过的供应商也可选择，但再次编辑后，数据将刷新！</span>
        </div>
        <div>
          <el-table
              :data="list4" border
              ref="multipleTable"
              tooltip-effect="dark"
              @selection-change="handleSelectionmat"
              class="ty-table-control ty-table ty-table-headerLine">
            <el-table-column type="selection" width="100" align="center" label="选择" class-name="custom-selection-column">
            </el-table-column>
            <el-table-column label="供应商" width="" align="center">
              <template #default="scope">
                {{scope.row.codeName}}/{{scope.row.fullName}}
              </template>
            </el-table-column>
            <el-table-column prop="enabled" label="备注" width="" align="center"></el-table-column>
            <el-table-column label="状态" width="" align="center">尚未编辑</el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="nextpick" v-show="materlon === 4">
      <div class="rolor">
        <div class="backPage ty-clear">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 litten" @click="gobacktun(1)">返回首页</span>
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="gobacktun(3)" v-show="gobck2 === 0">返回上一页</span>
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="gobacktun(3,1)" v-show="gobck2 === 1">返回上一页</span>
          <el-button class="ty-right" type="primary" @click="mudesure">确定</el-button>
        </div>
        <div class="topName" v-show="gobck === 0">{{catlist}}</div>
        <div class="lineQuery ty-clear">
          <div class="ty-left" style="margin-top: 7px;">请编辑最外层包装。如有时领料需要拆包装，则请点击“录入下一层包装”以进一步编辑。</div>
          <div class="ty-right">
            <el-button type="text" class="nexte" @click="writnext">录入下一层包装</el-button>
          </div>
        </div>
        <div class="lineQuery ty-clear">
          <span>注：具体录入到哪一层包装，请根据实际自行决定。本页面只要求编辑最外层包装！</span>
        </div>
        <div>
          <el-table :data="list5" border class="ty-table-control ty-table ty-table-headerLine">
            <el-table-column prop="newer" label="包装级别" width="" align="center"></el-table-column>
            <el-table-column label="主包装物" width="210" align="center">
              <!--              <template #default="scope" v-if="looker === 0">-->
              <!--                {{ scope.row.mainpack.codeid }}/{{ scope.row.mainpack.name }}/{{ scope.row.model }}-->
              <!--              </template>-->
              <!--              <p>{{ name ?? nape ?? defaultText }}</p>-->
              <template #default="scope" v-if="looker === 0">
                {{ scope.row.zyPackaging?.code ?? scope.row.zyPackaging?.codeid ?? scope.row.mainpack?.code ?? scope.row.mainpack?.codeid}}/
                {{ scope.row.zyPackaging?.name ?? scope.row.mainpack?.name}}/{{ scope.row.zyPackaging?.model ?? scope.row.mainpack?.model}}
                <!--                {{ scope.row.mainpack?.code ?? scope.row.mainpack?.codeid ?? scope.row.zyPackaging?.code ?? scope.row.zyPackaging?.codeid}}/-->
                <!--                {{ scope.row.mainpack?.name ?? scope.row.zyPackaging?.name }}/{{ scope.row.mainpack?.model ?? scope.row.zyPackaging?.model }}-->
              </template>
              <template #default="scope" v-if="looker === 2">
                <!--这位置需要知道list5的值是什么样的，是否含有对应的字段-->
                <!--这一行提示scope.row.mainpack is undefined-->
                {{ scope.row.zyPackaging.code }}/ {{scope.row.zyPackaging.name }}/ {{ scope.row.zyPackaging.model }}
              </template>
              <template #default="scope" v-if="looker === 1"></template>
            </el-table-column>
            <el-table-column label="辅助包装物" width="100" align="center">
              <template #default="scope" v-if="looker === 0">
                <span class="ty-color-blue" @click="numbelook(scope.row)">{{ scope.row.itemList?.length ?? scope.row.auxiliary?.length}}种</span>
              </template>
              <template #default="scope" v-if="looker === 2">
                <span class="ty-color-blue" @click="numbelook(scope.row)">{{ scope.row.itemList?.length ?? scope.row.auxiliary?.length}}种</span>
              </template>
              <template #default="scope" v-if="looker === 1"></template>
            </el-table-column>
            <el-table-column prop="eveuppacnum" label="每个上层包装内有几个本层包装" width="130" align="center"></el-table-column>
            <el-table-column prop="onlypack" label="单个包装内材料的数量" width="" align="center"></el-table-column>
            <el-table-column prop="onlynetwei" label="单个包装的净重" width="" align="center"></el-table-column>
            <el-table-column prop="onlygrosswei" label="单个包装的毛重" width="" align="center"></el-table-column>
            <el-table-column label="操作" width="" align="center">
              <template #default="scope">
                <span class="ty-color-blue" @click="acternnext(scope.row,'',catunit)" v-show="gobck2 === '' || gobck2 === 0">编辑</span>
                <span class="ty-color-blue" @click="acternnext(scope.row,1,catunit)" v-show="gobck2 === 1">编辑</span>
                <span class="ty-color-red" @click="dettthtap(scope.row)" v-show="scope.$index === list5.length - 1 && list5.length > 1">删除本层</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="supalltak" v-show="materlon === 5">
      <div class="rolor">
        <div class="backPage">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 litten" @click="gobacktun(1)">返回首页</span>
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="gobacktun(1)">返回上一页</span>
        </div>
        <div class="topName">{{ catlist }}</div>
        <div class="lineQuery ty-clear">
          <div class="ty-left" style="margin-top: 7px;margin-bottom: 21px;">本材料共<span>{{list7.length}}</span>个供应商。</div>
        </div>
        <div>
          <el-table :data="list7" border class="ty-table-control ty-table ty-table-headerLine">
            <el-table-column label="供应商" width="" align="center">
              <template #default="scope">
                {{scope.row.codeName}}/{{scope.row.fullName}}
              </template>
            </el-table-column>
            <el-table-column prop="enabled" label="备注" width="" align="center"></el-table-column>
            <el-table-column label="包装概述" width="" align="center">
              <template #default="scope">
                <div v-if="scope.row.flagn === 1">{{  scope.row.gs }}</div>
                <div v-if="scope.row.flagn === 0">
                  <el-radio-group v-model="scope.row.outSpace4" @change="neeornonee(scope.$index,scope.row.outSpace4,scope.row)">
                    <el-radio v-for="option in options4" :key="option.value" :label="option.value" v-show="option.isVisible"
                              @click.native="neeoendk(option.value,scope.row.outSpace4,scope.$index,scope.row)">{{ option.label }}</el-radio>
                  </el-radio-group>
                </div>
              </template>
            </el-table-column>
            <!--            <el-table-column prop="gs" label="包装概述" width="" align="center" v-if="scope.row.flagn === 1"></el-table-column>-->
            <!--            <el-table-column label="包装概述" width="" align="center" v-if="scope.row.flagn === 0">-->
            <!--              <template #default="scope">-->
            <!--                <el-radio-group v-model="scope.row.outSpace4" @change="neeornonee(scope.$index,scope.row.outSpace,scope.row)">-->
            <!--                  <el-radio v-for="option in options4" :key="option.value" :label="option.value" v-show="option.isVisible"-->
            <!--                    @click.native="neeoendk(option.value,scope.row.outSpace4,scope.$index,scope.row)">{{ option.label }}</el-radio>-->
            <!--                </el-radio-group>-->
            <!--              </template>-->
            <!--            </el-table-column>-->
            <el-table-column label="操作" width="" align="center">
              <template #default="scope">
                <span class="ty-color-blue" @click="lookhnve(scope.row,catunit)" v-show="scope.row.looket === 1">查看</span>
                <span class="ty-color-gray specinal" v-show="scope.row.looket === 2 && scope.row.outSpace4 === 1 || scope.row.looket === 2 &&
                  scope.row.outSpace4 === undefined || scope.row.looket === 2 && scope.row.outSpace4 === ''">编辑</span>
                <span class="ty-color-blue" @click="nextpict(scope.row,'',catunit,'kt')"
                      v-show="scope.row.looket === 2 && scope.row.outSpace4 === 0">编辑</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="allspecil" v-show="materlon === 6">
      <div class="rolor">
        <div class="backPage">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="gobacktun(5)">返回</span>
        </div>
        <div class="topName">{{catlist3}}</div>
        <div class="lineQuery ty-clear">
          <div class="ty-left" style="margin-top: 17px;">
            <span>本供应商供应本材料的包装方式中，在用的有{{nowlank.length}}种，已停用的{{stoplank.length}}种。</span>
          </div>
          <div class="ty-right" style="margin-top: 11px;">
            <el-button type="primary" @click="bendeactivd(catunit)">已停用的数据</el-button>
            <el-button type="primary" @click="changnoact(catunit)">改为无需包装</el-button>
            <el-button type="primary" @click="addpactd(catunit)">新增包装方式</el-button>
          </div>
          <!--<div class="ty-right" style="margin-top: 11px;" v-for="(itemd2,indexd2) in listb" :key="indexd2" v-if="hasjsont === 2">
            <el-button type="primary" @click="bendeactivd(itemd2)">已停用的数据</el-button>
            <el-button type="primary" @click="changnoact(itemd2.id,itemd2)">改为无需包装</el-button>
            <el-button type="primary" @click="addpactd">新增包装方式</el-button>
          </div>-->
        </div>
        <div v-for="(item,index) in list81" :key="index" style="padding-bottom: 0;">
          <div class="lineQuery ty-clear" v-show="hasjson === 1">
            <div class="ty-left" style="margin-top: 17px;">
              <div class="down1">
                <div class="ty-left litt1">
                  <span>以下为{{ createName1 }}&nbsp;{{ new Date(createDate1).format("yyyy-MM-dd hh:mm:ss") }}创建的包装方式。</span>
                </div>
                <div class="ty-right litt2">
                  <!--下面这行提示需要将@click改为onclick-->
                  <el-button type="text" @click="editbnk(item,'one',catunit)">编辑</el-button>
                  <el-button type="text" @click="stopuser(item.id,catunit)">停用</el-button>
                  <el-button type="text" @click="actlanktesk(item.id,catunit)">操作记录</el-button>
                </div>
              </div>
            </div>
            <div class="ty-right" style="margin-top: 11px;">
              <el-button type="text" @click="lookmoreactr">查看更多的包装方式</el-button>
            </div>
          </div>
        </div>
        <div v-show="hasjson === 1">
          <el-table :data="list81" border class="ty-table-control ty-table ty-table-headerLine">
            <el-table-column label="长宽高" width="" align="center">
              <template #default="scope">
                {{ scope.row.outerLength }}*{{ scope.row.outerWidth }}*{{ scope.row.outerHeight }}
              </template>
            </el-table-column>
            <el-table-column prop="outSpace" label="形状" width="" align="center"></el-table-column>
            <el-table-column label="装有本材料" width="" align="center">
              <template #default="scope">
                {{ scope.row.materialCount }}
              </template>
            </el-table-column>
            <el-table-column label="净重" width="" align="center">
              <template #default="scope">
                {{ scope.row.netWeight }}{{ scope.row.netUnit }}
              </template>
            </el-table-column>
            <el-table-column label="毛重" width="" align="center">
              <template #default="scope">
                {{ scope.row.grossWeight }}{{ scope.row.grossUnit }}
              </template>
            </el-table-column>
            <el-table-column label="录入共几层" width="" align="center">
              <template #default="scope">
                {{ scope.row.structureList.length }}层
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="lineQuery ty-clear" v-show="oven === 1">
          <div class="ty-left" style="margin-top: 17px;">
            <div class="down1">
              <div class="ty-left litt1">
                <span>最外层</span>
              </div>
              <div class="ty-right">
                <span>主要使用XXX包装，辅助包装物共XX种</span>
              </div>
            </div>
          </div>
          <div class="ty-right" style="margin-top: 11px;">
            <el-button type="text" @click="paclooket">查看</el-button>
          </div>
        </div>
        <div class="lineQuery ty-clear" v-show="oven === 2">
          <div class="ty-left" style="margin-top: 17px;">
            <div class="down1">
              <div class="ty-left litt1">
                <span>最外层的下一层</span>
              </div>
              <div class="ty-right">
                <span>主要使用XXX包装，辅助包装物共XX种</span>
              </div>
            </div>
          </div>
          <div class="ty-right" style="margin-top: 11px;">
            <el-button type="text" @click="">查看</el-button>
          </div>
        </div>
        <div class="lineQuery ty-clear" v-show="oven === 3">
          <div class="ty-left" style="margin-top: 17px;">
            <div class="down1">
              <div class="ty-left litt1">
                <span>最外层的下两层</span>
              </div>
              <div class="ty-right">
                <span>主要使用XXX包装，辅助包装物共XX种</span>
              </div>
            </div>
          </div>
          <div class="ty-right" style="margin-top: 11px;">
            <el-button type="text" @click="">查看</el-button>
          </div>
        </div>
        <div class="lineQuery ty-clear" v-show="oven === 4">
          <div class="ty-left" style="margin-top: 17px;">
            <div class="down1">
              <div class="ty-left litt1">
                <span>最外层的下三层</span>
              </div>
              <div class="ty-right">
                <span>主要使用XXX包装，辅助包装物共XX种</span>
              </div>
            </div>
          </div>
          <div class="ty-right" style="margin-top: 11px;">
            <el-button type="text" @click="">查看</el-button>
          </div>
        </div>
        <div v-show="oven === 5" v-for="(item,index) in list81" :key="index">
          <div class="lineQuery ty-clear" style="display: flex;flex-direction: column;">
            <div v-for="(item2,index2) in item.structureList" :key="index2">
              <div class="ty-left" style="margin-top: 17px;">
                <div class="down1">
                  <div class="ty-left litt1" style="margin-right: 210px;">
                    <span v-show="index2+1 === 1" style="margin-right: 64px;">最外层</span>
                    <span v-show="index2+1 > 1">最外层的下{{ numberToChinese1(index2) }}层</span>
                  </div>
                  <div class="ty-right">
                    <span>主要使用{{ item2.zyPackaging?.name }}包装，辅助包装物共{{ item2.itemList?.length }}种</span>
                  </div>
                </div>
              </div>
              <div class="ty-right wrote" style="margin-top: 11px;">
                <el-button type="text" @click="paclooket(item2)">查看</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--<div class="addpactad" v-if="materlon === 7">
      <div class="rolor">
        <div class="backPage ty-clear">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 litten" @click="gobacktun(1)">返回首页</span>
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="gobacktun(6)">返回上一页</span>
          <el-button class="ty-right" type="primary" @click="mudesure">确定</el-button>
        </div>
        <div class="lineQuery ty-clear">
          <div class="ty-left" style="margin-top: 7px;">请编辑最外层包装。如有时领料需要拆包装，则请点击“录入下一层包装”以进一步编辑。</div>
          <div class="ty-right">
            <el-button type="text" class="nexte" @click="writnext">录入下一层包装</el-button>
          </div>
        </div>
        <div class="lineQuery ty-clear">
          <span>注：具体录入到哪一层包装，请根据实际自行决定。本页面只要求编辑最外层包装！</span>
        </div>
        <div>
          <el-table :data="list9" border class="ty-table-control ty-table ty-table-headerLine">
            <el-table-column prop="newer" label="包装级别" width="" align="center"></el-table-column>
            <el-table-column  label="主包装物" width="210" align="center">
              <template #default="scope" v-if="looker2 === 0">
                {{ scope.row.mainpack.codeid }}/{{ scope.row.mainpack.name }}/{{ scope.row.mainpack.model }}
              </template>
              <template #default="scope" v-if="looker2 === 1"></template>
            </el-table-column>
            <el-table-column label="辅助包装物" width="100" align="center">
              <template #default="scope" v-if="looker2 === 0">
                <span class="ty-color-blue" @click="numbelook(scope.row)">{{ list9.auxiliary.length }}种</span>
              </template>
              <template #default="scope" v-if="looker2 === 1"></template>
            </el-table-column>
            <el-table-column prop="eveuppacnum" label="每个上层包装内有几个本层包装" width="130" align="center"></el-table-column>
            <el-table-column prop="onlypack" label="单个包装内材料的数量" width="" align="center"></el-table-column>
            <el-table-column prop="onlynetwei" label="单个包装的净重" width="" align="center"></el-table-column>
            <el-table-column prop="onlygrosswei" label="单个包装的毛重" width="" align="center"></el-table-column>
            <el-table-column label="操作" width="" align="center">
              <template #default="scope">
                <span class="ty-color-blue" @click="acternnext(scope.row)">编辑</span>
                <span class="ty-color-red" @click="dettthtap(scope.row)" v-if="scope.$index === list5.length - 1 && list5.length > 1">删除本层</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>-->
    <div class="stopund" v-show="materlon === 8">
      <div class="rolor">
        <div class="backPage">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 litten" @click="gobacktun(1)">返回首页</span>
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="gobacktun(6)">返回上一页</span>
        </div>
        <div class="topName">{{ catlist3 }}</div>
        <div class="lineQuery ty-clear">
          <div class="ty-left" style="margin-top: 17px;">
            <span>本供应商供应的本材料的包装方式中，已停用的有{{ stoplank.length }}种。</span>
          </div>
        </div>
        <div class="scrollable-div">
          <div v-for="(item1,index1) in stoplank" :key="index1">
            <div class="lineQuery ty-clear" style="border-bottom: none;">
              <div class="ty-left">
                <span>以下为{{ item1.createName }}&nbsp;{{ new Date(item1.createDate).format("yyyy-MM-dd hh:mm:ss") }}创建的包装方式。</span>
              </div>
              <div class="ty-right">
                <el-button type="text" @click="editbnk(item1,'all')">编辑</el-button>
                <el-button type="text" @click="startusen(item1.id,item1)">启用</el-button>
                <el-button type="text" @click="actlanktesk(item1.id,item1)">操作记录</el-button>
              </div>
            </div>
            <div>
              <el-table :data="[item1]" border class="ty-table-control ty-table ty-table-headerLine">
                <el-table-column label="长宽高" width="" align="center">
                  <template #default="scope">
                    {{ scope.row.outerLength }}*{{ scope.row.outerWidth }}*{{ scope.row.outerHeight }}
                  </template>
                </el-table-column>
                <el-table-column prop="outSpace" label="形状" width="" align="center"></el-table-column>
                <el-table-column label="装有本材料" width="" align="center">
                  <template #default="scope">
                    {{ scope.row.totalWeight }}{{ scope.row.totalUnit }}
                  </template>
                </el-table-column>
                <el-table-column label="净重" width="" align="center">
                  <template #default="scope">
                    {{ scope.row.netWeight }}{{ scope.row.netUnit }}
                  </template>
                </el-table-column>
                <el-table-column label="毛重" width="" align="center">
                  <template #default="scope">
                    {{ scope.row.grossWeight }}{{ scope.row.grossUnit }}
                  </template>
                </el-table-column>
                <el-table-column prop="layers" label="录入共几层" width="" align="center"></el-table-column>
              </el-table>
            </div>
            <div>
              <div class="lineQuery ty-clear" style="display: flex;flex-direction: column;">
                <div v-for="(item2,index2) in item1.structureList" :key="index2">
                  <div class="ty-left" style="margin-top: 17px;">
                    <div class="down1">
                      <div class="ty-left litt1" style="margin-right: 210px;">
                        <!--                      <span>{{ item2.level }}</span>-->
                        <span v-show="index2+1 === 1" style="margin-right: 64px;">最外层</span>
                        <span v-show="index2+1 > 1">最外层的下{{ numberToChinese1(index2) }}层</span>
                      </div>
                      <div class="ty-right">
                        <span>主要使用{{ item2.zyPackaging.name }}包装，辅助包装物共{{ item2.itemList.length }}种</span>
                      </div>
                    </div>
                  </div>
                  <div class="ty-right" style="margin-top: 11px;">
                    <el-button type="text" @click="paclooket(item1.structureList)">查看</el-button>
                  </div>
                </div>
              </div>
            </div>
            <!--<div class="lineQuery ty-clear">
              <div class="ty-left" style="margin-top: 17px;">
                <div class="down1">
                  <div class="ty-left litt1">
                    <span>最外层</span>
                  </div>
                  <div class="ty-right">
                    <span>主要使用XXX包装，辅助包装物共XX种</span>
                  </div>
                </div>
              </div>
              <div class="ty-right" style="margin-top: 11px;">
                <el-button type="text" @click="">查看</el-button>
              </div>
            </div>
            <div class="lineQuery ty-clear">
              <div class="ty-left" style="margin-top: 17px;">
                <div class="down1">
                  <div class="ty-left litt1">
                    <span>最外层的下一层</span>
                  </div>
                  <div class="ty-right">
                    <span>主要使用XXX包装，辅助包装物共XX种</span>
                  </div>
                </div>
              </div>
              <div class="ty-right" style="margin-top: 11px;">
                <el-button type="text" @click="">查看</el-button>
              </div>
            </div>
            <div class="lineQuery ty-clear">
              <div class="ty-left" style="margin-top: 17px;">
                <div class="down1">
                  <div class="ty-left litt1">
                    <span>最外层的下两层</span>
                  </div>
                  <div class="ty-right">
                    <span>主要使用XXX包装，辅助包装物共XX种</span>
                  </div>
                </div>
              </div>
              <div class="ty-right" style="margin-top: 11px;">
                <el-button type="text" @click="">查看</el-button>
              </div>
            </div>
            <div class="lineQuery ty-clear">
              <div class="ty-left" style="margin-top: 17px;">
                <div class="down1">
                  <div class="ty-left litt1">
                    <span>最外层的下三层</span>
                  </div>
                  <div class="ty-right">
                    <span>主要使用XXX包装，辅助包装物共XX种</span>
                  </div>
                </div>
              </div>
              <div class="ty-right" style="margin-top: 11px;">
                <el-button type="text" @click="">查看</el-button>
              </div>
            </div>-->
          </div>
        </div>
        <!--下面是循环的下一组了
                <div class="lineQuery ty-clear">
                  <div class="ty-left">
                    <span>以下为XXX XXXX-XX-XX XX:XX:XX创建的包装方式，停用的操作：XXX XXXX-XX-XX XX:XX:XX</span>
                  </div>
                  <div class="ty-right">
                    <el-button type="text" @click="editbnk">编辑</el-button>
                    <el-button type="text" @click="startusen">启用</el-button>
                    <el-button type="text" @click="actlanktesk">操作记录</el-button>
                  </div>
                </div>-->
      </div>
    </div>
    <div class="actlankst" v-show="materlon === 9">
      <div class="rolor">
        <div class="backPage">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 litten" @click="gobacktun(1)">返回首页</span>
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="gobacktun(7)">返回上一页</span>
        </div>
        <div class="lineQuery ty-clear">
          <div class="ty-left" style="margin-top: 17px;">
            <span>包装方式的操作记录</span>
          </div>
        </div>
        <div class="lineQuery ty-clear">
          <div class="ty-left" style="margin-top: 17px;">
            <span>商品：{{ catlist3 }}</span>
          </div>
        </div>
        <div class="lineQuery ty-clear">
          <div class="ty-left" style="margin-top: 17px;">
            <span>客户：{{ fullNamet }}</span>
          </div>
        </div>
        <div>
          <el-table :data="list10" border class="ty-table-control ty-table ty-table-headerLine">
            <el-table-column prop="operation" label="属性" width="" align="center"></el-table-column>
            <el-table-column label="操作者及时间" width="" align="center">
              <template #default="scope">
                {{ scope.row.updateName }}&nbsp;{{ new Date(scope.row.updateDate).format("yyyy-MM-dd hh:mm:ss") }}
              </template>
            </el-table-column>
            <el-table-column label="生效日期" width="" align="center">
              <template #default="scope">
                {{ new Date(scope.row.effectTime).format("yyyy-MM-dd hh:mm:ss") }}
              </template>
            </el-table-column>
            <el-table-column label="详细查看" width="" align="center">
              <template #default="scope">
                <span class="ty-color-blue" @click="havelkerd(scope.row)">查看</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="baksoutr" v-show="materlon === 10">
      <div class="rolor">
        <div class="backPage">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 litten" @click="gobacktun(1)">返回首页</span>
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="gobacktun(8)">返回上一页</span>
        </div>
        <div class="lineQuery ty-clear">
          <div class="ty-left" style="margin-top: 17px;">
            <span>以下为{{ createNameg }}&nbsp;{{ new Date(createDateg).format("yyyy-MM-dd hh:mm:ss") }}创建的包装方式。</span>
          </div>
        </div>
        <div>
          <el-table :data="list82" border class="ty-table-control ty-table ty-table-headerLine">
            <el-table-column label="长宽高" width="" align="center">
              <template #default="scope">
                {{ scope.row.outerLength }}*{{ scope.row.outerWidth }}*{{ scope.row.outerHeight }}
              </template>
            </el-table-column>
            <el-table-column prop="outerShape" label="形状" width="" align="center"></el-table-column>
            <el-table-column prop="materialCount" label="装有本材料" width="" align="center"></el-table-column>
            <el-table-column label="净重" width="" align="center">
              <template #default="scope">
                {{ scope.row.netWeight }}{{ scope.row.netUnit }}
              </template>
            </el-table-column>
            <el-table-column label="毛重" width="" align="center">
              <template #default="scope">
                {{ scope.row.grossWeight }}{{ scope.row.grossUnit }}
              </template>
            </el-table-column>
            <el-table-column label="录入共几层" width="" align="center">
              <template #default="scope">
                {{ scope.row.structureList.length }}层
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="lineQuery ty-clear" v-show="oven === 1">
          <div class="ty-left" style="margin-top: 17px;">
            <div class="down1">
              <div class="ty-left litt1">
                <span>最外层</span>
              </div>
              <div class="ty-right">
                <span>主要使用XXX包装，辅助包装物共XX种</span>
              </div>
            </div>
          </div>
          <div class="ty-right" style="margin-top: 11px;">
            <el-button type="text" @click="">查看</el-button>
          </div>
        </div>
        <div class="lineQuery ty-clear" v-show="oven === 2">
          <div class="ty-left" style="margin-top: 17px;">
            <div class="down1">
              <div class="ty-left litt1">
                <span>最外层的下一层</span>
              </div>
              <div class="ty-right">
                <span>主要使用XXX包装，辅助包装物共XX种</span>
              </div>
            </div>
          </div>
          <div class="ty-right" style="margin-top: 11px;">
            <el-button type="text" @click="">查看</el-button>
          </div>
        </div>
        <div class="lineQuery ty-clear" v-show="oven === 3">
          <div class="ty-left" style="margin-top: 17px;">
            <div class="down1">
              <div class="ty-left litt1">
                <span>最外层的下两层</span>
              </div>
              <div class="ty-right">
                <span>主要使用XXX包装，辅助包装物共XX种</span>
              </div>
            </div>
          </div>
          <div class="ty-right" style="margin-top: 11px;">
            <el-button type="text" @click="">查看</el-button>
          </div>
        </div>
        <div class="lineQuery ty-clear" v-show="oven === 4">
          <div class="ty-left" style="margin-top: 17px;">
            <div class="down1">
              <div class="ty-left litt1">
                <span>最外层的下三层</span>
              </div>
              <div class="ty-right">
                <span>主要使用XXX包装，辅助包装物共XX种</span>
              </div>
            </div>
          </div>
          <div class="ty-right" style="margin-top: 11px;">
            <el-button type="text" @click="">查看</el-button>
          </div>
        </div>
        <div v-show="oven === 5" v-for="(item,index) in list82" :key="index">
          <div class="lineQuery ty-clear" style="display: flex;flex-direction: column;">
            <div v-for="(item2,index2) in item.structureList" :key="index2">
              <div class="ty-left" style="margin-top: 17px;">
                <div class="down1">
                  <div class="ty-left litt1" style="margin-right: 210px;">
                    <span v-show="index2+1 === 1" style="margin-right: 64px;">最外层</span>
                    <span v-show="index2+1 > 1">最外层的下{{ numberToChinese1(index2) }}层</span>
                  </div>
                  <div class="ty-right">
                    <span>主要使用{{ item2.zyPackaging.name }}包装，辅助包装物共{{ item2.itemList.length }}种</span>
                  </div>
                </div>
              </div>
              <div class="ty-right" style="margin-top: 11px;">
                <el-button type="text" @click="paclooket(item2)">查看</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="morepic" v-show="materlon === 11">
      <div class="rolor">
        <div class="backPage">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 litten" @click="gobacktun(1)">返回首页</span>
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="gobacktun(6)">返回上一页</span>
        </div>
        <div class="topName">{{catlist3}}</div>
        <div class="lineQuery ty-clear">
          <div class="ty-left" style="margin-top: 17px;">
            <span>本供应商供应的本材料有{{ nowlank.length }}种在用的包装方式。
              对各种包装方式，可进行编辑，也可停用。</span>
          </div>
        </div>
        <div v-for="(itemt,indext) in list8" :key="indext">
          <div class="lineQuery ty-clear" style="border-bottom: 0;">
            <div class="ty-left">
              <span>以下为{{ itemt.createName }}&nbsp;{{ new Date(itemt.createDate).format("yyyy-MM-dd hh:mm:ss") }}创建的包装方式。</span>
            </div>
            <div class="ty-right litt2">
              <el-button type="text" @click="editbnk(itemt,'all')">编辑</el-button>
              <el-button type="text" @click="stopuser(itemt.id,itemt)">停用</el-button>
              <el-button type="text" @click="actlanktesk(itemt.id,itemt)">操作记录</el-button>
            </div>
          </div>
          <div>
            <el-table border :data="itemt.box" class="ty-table-control ty-table ty-table-headerLine">
              <el-table-column label="长宽高" width="" align="center">
                <template #default="scope">
                  {{ scope.row.outerLength }}*{{ scope.row.outerWidth }}*{{ scope.row.outerHeight }}
                </template>
              </el-table-column>
              <el-table-column prop="outSpace" label="形状" width="" align="center"></el-table-column>
              <el-table-column label="装有本材料" width="" align="center">
                <template #default="scope">
                  {{ scope.row.materialCount }}
                </template>
              </el-table-column>
              <el-table-column label="净重" width="" align="center">
                <template #default="scope">
                  {{ scope.row.netWeight }}{{ scope.row.netUnit }}
                </template>
              </el-table-column>
              <el-table-column label="毛重" width="" align="center">
                <template #default="scope">
                  {{ scope.row.grossWeight }}{{ scope.row.grossUnit }}
                </template>
              </el-table-column>
              <el-table-column label="录入共几层" width="" align="center">{{ itemt.structureList.length }}层</el-table-column>
            </el-table>
          </div>
          <div  v-show="oven === 0" class="lineQuery ty-clear">
            <div class="ty-left" style="margin-top: 17px;">
              <div class="down1">
                <div class="ty-left litt1">
                  <span>最外层</span>
                </div>
                <div class="ty-right">
                  <span>主要使用XXX包装，辅助包装物共XX种</span>
                </div>
              </div>
            </div>
            <div class="ty-right" style="margin-top: 11px;">
              <el-button type="text" @click="">查看</el-button>
            </div>
          </div>
          <div v-show="oven === 0" class="lineQuery ty-clear">
            <div class="ty-left" style="margin-top: 17px;">
              <div class="down1">
                <div class="ty-left litt1">
                  <span>最外层的下一层</span>
                </div>
                <div class="ty-right">
                  <span>主要使用XXX包装，辅助包装物共XX种</span>
                </div>
              </div>
            </div>
            <div class="ty-right" style="margin-top: 11px;">
              <el-button type="text" @click="">查看</el-button>
            </div>
          </div>
          <div v-show="oven === 0" class="lineQuery ty-clear">
            <div class="ty-left" style="margin-top: 17px;">
              <div class="down1">
                <div class="ty-left litt1">
                  <span>最外层的下两层</span>
                </div>
                <div class="ty-right">
                  <span>主要使用XXX包装，辅助包装物共XX种</span>
                </div>
              </div>
            </div>
            <div class="ty-right" style="margin-top: 11px;">
              <el-button type="text" @click="">查看</el-button>
            </div>
          </div>
          <div v-show="oven === 0" class="lineQuery ty-clear">
            <div class="ty-left" style="margin-top: 17px;">
              <div class="down1">
                <div class="ty-left litt1">
                  <span>最外层的下三层</span>
                </div>
                <div class="ty-right">
                  <span>主要使用XXX包装，辅助包装物共XX种</span>
                </div>
              </div>
            </div>
            <div class="ty-right" style="margin-top: 11px;">
              <el-button type="text" @click="">查看</el-button>
            </div>
          </div>
          <div v-show="oven === 5">
            <div class="lineQuery ty-clear" style="display: flex;flex-direction: column;">
              <div v-for="(item2,index2) in itemt.structureList" :key="index2">
                <div class="ty-left" style="margin-top: 17px;">
                  <div class="down1">
                    <div class="ty-left litt1" style="margin-right: 210px;">
                      <!--                    <span v-show="indext+1 === 1" style="margin-right: 64px;">最外层</span>-->
                      <!--                    <span v-show="indext+1 > 1">最外层的下{{ numberToChinese1(indext) }}层</span>-->
                      <span v-show="index2+1 === 1" style="margin-right: 64px;">最外层</span>
                      <span v-show="index2+1 > 1">最外层的下{{ numberToChinese1(index2) }}层</span>
                    </div>
                    <div class="ty-right">
                      <span>主要使用{{ item2.zyPackaging?.name }}包装，辅助包装物共{{ item2.itemList?.length }}种</span>
                    </div>
                  </div>
                </div>
                <div class="ty-right" style="margin-top: 11px;">
                  <el-button type="text" @click="paclooket(item2)">查看</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--弹窗盒子（注：越往下层级越高，展示时matVisible4会展示在matVisible2的上面）-->
    <TyDialog v-show="matVisible" width="745" dialogTitle="本层包装的信息" color="blue" :dialogHide="hideFun1">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun1">取消</el-button>
        <el-button class="bounce-ok" @click="tipOk1(1)" v-show="madestune === 1">确定</el-button>
        <el-button class="bounce-ok" @click="tipOk1(2)" v-show="madestune === 2">确定</el-button>
        <el-button type="info" @click="" :disabled="true" v-show="madestune === 0" class="bounce-info"
                   :class="{'disabled-button':!isFromCompleter}">确定</el-button>
      </template>
      <template #dialogBody>
        <el-form ref="packagingbox" :model="packagingbox" label-width="660px">
          <div class="modInput">
            <table class="pannelTab">
              <tbody>
              <tr>
                <td>
                  <div class="tbTtl">
                    <div class="ty-left">主要包装物<i class="red">*</i></div>
                    <div class="ty-right">
                      <span class="ty-left" @click="" v-if="addprck1 === 2 && this.respectively === 1">
                        {{packaging.mainpack.codeid  }}/{{packaging.mainpack.name }}/{{packaging.mainpack.model }}/
                        {{packaging.mainpack.specifications }}/{{packaging.mainpack.weimingt }}
                      </span>
                      <span class="ty-left" @click="" v-if="addprck1 === 2 && this.respectively === 2">
                        {{ packaging.mainpack.code || packaging.mainpack.codeid }}/
                        {{ packaging.mainpack.name || packaging.mainpack.name }}/
                        {{ packaging.mainpack.model || packaging.mainpack.model }}/
                        {{ packaging.mainpack.specifications || packaging.mainpack.specifications }}/
                        {{ packaging.mainpack.weight || packaging.mainpack.weimingt}}
                      </span>
                      <span class="ty-left" @click="" v-if="addprck1 === 1">尚未选择</span>
                      <span class="linkBtn ty-right" @click="addpackag(1)">选择</span>
                    </div>
                  </div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="tbTtl">
                    <div class="ty-left">辅助包装物</div>
                    <div class="ty-right">
                      <span class="ty-left" v-if="addprck2 === 2">已选{{ packaging.auxiliary.length }}种</span>
                      <span class="ty-left" v-else>尚未选择</span>
                      <div class="ty-right">
                        <span class="linkBtn ty-left" @click="lookquen">查看/管理</span>
                        <span class="linkBtn ty-right" @click="addpackag(2)">增加</span>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
              <tr v-show="pickbig === 2" class="packige">
                <td>
                  <div class="tbTtl">
                    <div class="ty-left">每个上层包装内有几个本层包装<i class="red">*</i> </div>
                    <div class="ty-right">
                    <span class="ty-left">
                      <el-input v-model="sizeForm.name4" placeholder="请录入数字" type="text" class="inputd" @input="formatNumb1(sizeForm.name4)"></el-input>
                    </span>
                      <span class="ty-right">个</span>
                    </div>
                  </div>
                </td>
              </tr>
              <tr v-show="pickbig === 1" class="packige">
                <td>
                  <div class="tbTtl">
                    <div class="ty-left">单个包装内材料的数量<i class="red">*</i> </div>
                    <div class="ty-right">
                    <span class="ty-left">
                       <el-input v-model="sizeForm.name1" placeholder="请录入数字" type="text" class="inputd" @input="formatNumb1(sizeForm.name1)"></el-input>
                    </span>
                      <span class="ty-right">{{ sizeForm.unit }}</span>
                    </div>
                  </div>
                </td>
              </tr>
              <tr v-show="pickbig === 2" class="packige">
                <td>
                  <div class="tbTtl">
                    <div class="ty-left">单个包装内材料的数量</div>
                    <div class="ty-right">
                    <span class="ty-left">
                      <el-input v-model="sizeForm.name1" type="text" class="inputd" :disabled="true"></el-input>
                    </span>
                      <span class="ty-right">{{ sizeForm.unit }}</span>
                    </div>
                  </div>
                </td>
              </tr>
              <tr class="packige">
                <td>
                  <div class="tbTtl">
                    <div class="ty-left">单个包装的净重</div>
                    <div class="ty-right">
                    <span class="ty-left">
                      <el-input type="text" class="inputd" :disabled="true"></el-input>
                    </span>
                      <span class="ty-right"></span>
                    </div>
                  </div>
                </td>
              </tr>
              <tr class="packige">
                <td>
                  <div class="tbTtl">
                    <div class="ty-left">单个包装的毛重</div>
                    <div class="ty-right">
                    <span class="ty-left">
                      <el-input type="text" class="inputd" :disabled="true"></el-input>
                    </span>
                      <span class="ty-right"></span>
                    </div>
                  </div>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </el-form>
      </template>
    </TyDialog>
    <TyDialog v-show="matVisible4" width="745" dialogTitle="查看/管理本层包装的辅助包装物" color="blue" :dialogHide="hideFun4">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun4">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="sectCon">
          <span v-show="linkfo === 0">本层包装的辅助包装物已有如下{{ list6.length }}种</span>
          <span v-show="linkfo === 1">本层包装的辅助包装物已有如下{{ packaging.auxiliary.length }}种</span>
          <span v-show="linkfo === 3">本层包装的辅助包装物已有如下{{ detw.length }}种</span>
          <span v-show="linkfo === 2">本层包装的包装物已有如下</span>
          <span class="linkBtn ty-right" @click="addpackag(2)" v-show="yesorno === 1">增加辅助包装物</span>
        </div>
        <el-table :data="list6" border class="ty-table-control ty-table ty-table-headerLine" v-show="anoter === 1">
          <el-table-column label="包装物" width="" align="center">
            <template #default="scope">
              {{ scope.row.aucoid }}/{{ scope.row.auname }}/{{ scope.row.auspecfication }}/{{ scope.row.aumodel }}
            </template>
          </el-table-column>
          <el-table-column label="单重" width="" align="center">
            <template #default="scope">{{ scope.row.auonlywei }}{{ scope.row.auweimin }}</template>
            <!--            <template #default="scope">{{ scope.row.auonlywei }}</template>-->
          </el-table-column>
          <el-table-column label="操作" width="" align="center" v-show="yesorno === 1">
            <template #default="scope">
              <span class="ty-color-blue" @click="upteer(scope.row)">修改</span>
              <span class="ty-color-red" @click="detter(scope.row)">删除</span>
            </template>
          </el-table-column>
        </el-table>
        <el-table :data="packaging.auxiliary" border class="ty-table-control ty-table ty-table-headerLine" v-show="anoter === 0">
          <el-table-column label="包装物" width="" align="center">
            <template #default="scope">
              {{ scope.row.aucoid }}/{{ scope.row.auname }}/{{ scope.row.auspecfication }}/{{ scope.row.aumodel }}
            </template>
          </el-table-column>
          <el-table-column label="单重" width="" align="center">
            <template #default="scope">{{ scope.row.auonlywei }}</template>
          </el-table-column>
        </el-table>
        <el-table :data="newlast" border class="ty-table-control ty-table ty-table-headerLine" v-show="anoter === 2">
          <el-table-column label="包装物" width="" align="center">
            <template #default="scope">
              {{ scope.row.code }}/{{ scope.row.name }}/{{ scope.row.specifications }}/{{ scope.row.model }}
            </template>
          </el-table-column>
          <el-table-column label="单重" width="" align="center">
            <template #default="scope">{{ scope.row.weight }}{{ scope.row.weightUnit }}</template>
          </el-table-column>
        </el-table>
        <el-table :data="detw" border class="ty-table-control ty-table ty-table-headerLine" v-show="anoter === 3">
          <el-table-column label="包装物" width="" align="center">
            <template #default="scope">
              {{ scope.row.aucoid }}/{{ scope.row.auname }}/{{ scope.row.auspecfication }} /{{ scope.row.aumodel }}
            </template>
          </el-table-column>
          <el-table-column label="单重" width="" align="center">
            <template #default="scope">{{ scope.row.auonlywei }}{{ scope.row.auweimin}}</template>
          </el-table-column>
        </el-table>
      </template>
    </TyDialog>
    <TyDialog v-show="matVisible33" width="745" dialogTitle="增加包装物" color="blue" :dialogHide="hideFun33">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun33">取消</el-button>
        <el-button class="bounce-ok" @click="tipOk33(1)" v-show="maksune === 'main'">确定</el-button>
        <el-button class="bounce-ok" @click="tipOk33(2)" v-show="maksune === 'auxiliary'">确定</el-button>
        <el-button class="bounce-ok" @click="tipOk33(3)" v-show="maksune === 'takesure'">确定</el-button>
        <el-button class="bounce-ok upent" @click="tipOk33(4)" v-show="maksune === 'upent'">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="fmWrapper">
          <div v-show="addunten === 2">
            <div>请录入本材料最外层包装的外廓信息</div>
            <div class="bTip">注：各尺寸的单位均默认为m，如不适用，请更换！</div>
          </div>
          <el-form ref="addPackForm" :model="addPackForm" lable-width="80px" label-position="top">
            <ul class="outer_form">
              <li v-show="addunten === 1">
                <div>包装物代号</div>
                <el-input class="ty-inputText" placeholder="请录入" v-model="packagbox.numb" require />
              </li>
              <li v-show="addunten === 1">
                <div>包装物名称</div>
                <el-input class="ty-inputText" placeholder="请录入" v-model="packagbox.name" require />
              </li>
              <li v-show="addunten === 1">
                <div>规格</div>
                <el-input class="ty-inputText" placeholder="请录入" v-model="packagbox.sizer" require />
              </li>
              <li v-show="addunten === 1">
                <div>型号</div>
                <el-input class="ty-inputText" placeholder="请录入" v-model="packagbox.model" require />
              </li>
              <li v-show="addunten === 1">
                <div>重量单位</div>
                <el-select v-model="packagbox.widthUnit" require filterable placeholder="请录入" class="ty-inputText">
                  <el-option v-for="item in packagbox.widthlist" :key="item.id" :label="item.value" :value="item.id"></el-option>
                </el-select>
              </li>
              <!--                            {{ this.packagbox.widthlist }}-->
              <li v-show="addunten === 1">
                <div>单重</div>
                <el-input class="ty-inputText" placeholder="如需要，请录入" v-model="packagbox.onlhei" require @input="formatNumb2"></el-input>
              </li>
              <li v-show="addunten === 2" class="allen">
                <div>摆放于货架之后长度方向的尺寸
                  <span class="ty-color-gray ty-right">查看图例</span>
                </div>
                <el-input class="ty-inputText" placeholder="请录入数字" v-model="packagbox.outLegth" require />
                <el-select v-model="packagbox.lengthUnitid" class="ty-selectText">
                  <el-option v-for="item in packagbox.Uintlist" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </li>
              <li v-show="addunten === 2" class="allen">
                <div>
                  摆放于货架之后宽度方向的尺寸
                  <span class="ty-color-gray ty-right">查看图例</span>
                </div>
                <el-input class="ty-inputText" placeholder="请录入数字" v-model="packagbox.outWidth" require />
                <el-select v-model="packagbox.widthUnitid" class="ty-selectText">
                  <el-option v-for="item in packagbox.widlist" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </li>
              <li v-show="addunten === 2" class="allen">
                <div>
                  摆放于货架之后高度方向的尺寸
                  <span class="ty-color-gray ty-right">查看图例</span>
                </div>
                <el-input class="ty-inputText" placeholder="请录入数字" v-model="packagbox.outHeight" require />
                <el-select v-model="packagbox.heightUnitid" class="ty-selectText">
                  <el-option v-for="item in packagbox.heilist" :key="item.id" :label="item.name" :value="item.id"></el-option>
                </el-select>
              </li>
              <li v-show="addunten === 2">
                <div class="shapeCon">
                  <p>本材料最外层包装是什么形状？请选择：</p>
                  <div class="outerShape">
                    <div><input type="radio" v-model="packagbox.outerShape" value="1" @click="" />长方体</div>
                    <div><input type="radio" v-model="packagbox.outerShape" value="2" @click="" />圆柱体</div>
                    <div><input type="radio" v-model="packagbox.outerShape" value="3" @click="" />其他形状</div>
                  </div>
                </div>
              </li>
            </ul>
          </el-form>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-show="matVisible22" width="500" dialogTitle="！提示" color="red" :dialogHide="hideFun22">
      <template #dialogFooter>
        <el-button class="bounc-cancel" @click="hideFun22">取消</el-button>
        <el-button class="bounce-ok" @click="tipOk22(1)" v-show="desto === 1">确定</el-button>
        <el-button class="bounce-ok" @click="tipOk22(2)" v-show="desto === 2">确定</el-button>
        <el-button class="bounce-ok" @click="tipOk22(3)" v-show="desto === 3">确定</el-button>
        <el-button class="bounce-ok" @click="tipOk22(4)" v-show="desto === 4">确定</el-button>
        <el-button class="bounce-ok" @click="tipOk22(5)" v-show="desto === 5">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center" v-show="desto === 1">确定删除这种包装方式吗？</div>
        <div class="ty-center" v-show="desto === 2">确定停用这种包装方式吗？</div>
        <div class="ty-center" v-show="desto === 3">确定改为无需包装吗？确定后，在用的包装方式均将停用！</div>
        <div class="ty-center" v-show="desto === 4">确定启用这种包装方式吗？</div>
        <div class="ty-center" v-show="desto === 5">确定删除这层数据吗？</div>
      </template>
    </TyDialog>
  </div>
</template>

<script>
import {beforeRouteLeave, initNav} from "../../utils/routeChange";
import {
  addnewpiket, getpiclist, getsetlink, getsuplink, getunset, getunsetlink, seapackinfon,
  addPacking, stuppact, getcatlink, madesett, getlistisg, updatePacking
} from "../../api/procure";
import { ElMessage } from 'element-plus'
// import TyDialog from "../../components/TyDialog";

export default {
  //name: "materalPakaging"
  data(){
    return{
      pageName: 'materalPakaging',//可以跟文件名相同
      pageShow: true,
      pageInfo: { 'currentPageNo': 1, 'pageSize': 10, 'totalPage': 20},
      materlon: 0,//区分需要展示的是哪个页面
      searchKeyHome: '',
      searchKeyUse: '',//搜索框中输入的内容
      //outSpace: null,
      //acter: 0,
      //optun: 1,
      list1: {},//材料数据
      listun: '',//首页列表上面待设置的包装信息个数
      a: [],//定义的数组
      //list1es:{},//材料数据分页功能
      list2: [],//未设置的材料数据
      catliststa: '',//用于防止最初的样子被覆盖的问题
      catlist: '',//用于存储单条材料数据,根据点击的随时赋值
      catlist2: '',//用于在特殊情况下不和catlist的值发生冲突
      catlist3: '',//页面中头部展示的一行包含供应商名称+供应商id的文字内容
      catfullname: '',//代表供应商的名称
      list3: [{outSpace: null},{outSpace: null},{outSpace: null}],//第一条数据索引值是0，第二条数据索引值是1
      options: [//第一条数据-0：0，第二条数据就是1：0
        { value: 0, label: '需要',isVisible: true},
        { value: 1,label: '不需要',isVisible: true},
        //{value: 2,label: '暂不编辑',isVisible: false}
      ],
      options4: [
        { value: 0, label: '需要',isVisible: true },
        { value: 1, label: '不需要',isVisible: true }
      ],//包装概述下拉框供选择的内容
      flagn: '',//1-调用接口能获取到gs值的时候，0-调用接口获取不到gs的时候
      listb: [],//保存停用数据的数组
      gobck: 0,//1-在查看部分点击’新增包装方式‘,0-页面展示的内容
      gobck2: 0,//区分’返回上一页‘按钮点击后跳转的页面
      gobck22: '',//在首页从’查看‘部分点击的
      doiffet: 0,//用于区分是已设置的材料还是未设置的材料
      //gobck3: 0,
      outSpace4: '',//选择的包装概述内容
      uptlist: '',///???
      catid: '',//材料id
      suppMaterial: '',//供应关系id
      supplier: '',//供应商id
      purjson: {},//临时存储数据的json数据
      list4: [ {
        supplie: "XXXXX/XXX",
        answer: "--",
        acten: "XXXXXX"
      }],
      list5: [
        {newer: '最外层包装'},{newer: '最外层包装的下一层包装'}
      ],
      id5: '',//需要删除的数据的Id
      list6: [
        {bagen: '代号/名称/规格/型号/计量单位1', lagen: 'XXXXX重量单位'}
      ],
      anoter: 0,//用于区分 不同数据在页面渲染时的字段
      list7: [
        {supplie: 'XXXXX/XXX', answer: '--', pacwrite: '无包装'},
        {supplie: 'XXXXX/XXX', answer: '--', pacwrite: 'X种包装方式'},
        {supplie: 'XXXXX/XXX', answer: '已停用', pacwrite: 'X种包装方式'},
        {supplie: 'XXXXX/XXX', answer: '已停用', pacwrite: '--'},
        {supplie: 'XXXXX/XXX', answer: '--',}
      ],
      ketcs: '',
      list8: [],
      list81: [],
      list82: [],
      list9: [
        {newer: '最外层包装'},
        {newer: '最外层包装的下一层包装'},
        {newer: '最外层包装的下二层包装'},
        {newer: '最外层包装的下三层包装'}
      ],
      list10: [
        {attribute: '创建',createNmae: 'XX', createTime: 1686289962216,startTime: 1686289962216},
        {attribute: '修改',createNmae: 'XX', createTime: 1686289962216,startTime: 1686289962216},
        {attribute: '停用',createNmae: 'XX', createTime: 1686289962216,startTime: 1686289962216},
        {attribute: '启用',createNmae: 'XX', createTime: 1686289962216,startTime: 1686289962216},
      ],
      multipleSelection:[],//批量启用弹窗中选中的需要启用的数据
      choslist: {},//选中的数据
      madesue: 1,//判断确定按钮是否展示为可点击和颜色是否变亮
      matVisible: null,//'本层包装的信息‘弹窗的显示隐藏
      //matVisible2: null,
      matVisible22: null,//提示弹窗的显示和隐藏
      detk: '',//点击‘查看/管理本层包装的辅助包装物’弹窗中的表格里的一条数据对应的删除按钮获得的数据
      //matVisible3: null,
      matVisible33: null,//’增加包装物‘显示隐藏
      matVisible4: null,//’查看/管理本层包装的辅助包装物"弹窗显示隐藏
      //包装物盒子
      packagingbox: [],
      addPackForm:[],
      sizeForm:{
        name1:'',
        //name2:'',净重
        //name3:'',毛重
        name4:'',
        unit: '',//计量单位
      },
      pickbig: 0,//1-点击‘编辑’打开的弹窗，2-点击‘录入下一层包装’打开的弹窗
      addprck1: 1,//1-未进行主包装物增加，2-已进行主包装物增加
      addprck2: 1,//1-未进行辅助包装物增加，2-已进行辅助包装物增加
      dettetap: 0,//1-最后一条数据显示‘删除本层’按钮
      //因为有主要包装物和辅助包装物，是不是该将这两种数据分开存呢
      //下面的三个json数据是练习的，用于尝试下方法是否好用
      json1: {},//存放弹窗中每个输入框中的值
      all1: {},//存放第一种情况使用弹窗时的值
      all2: {},//存放第二种情况使用弹窗时的值
      packagbox: {//增加包装物弹窗中的数据内容
        numb: '',//代号
        name: '',//名称
        sizer: '',//规格
        model: '',//型号
        //heiweight: '',
        widthUnit: '',//重量单位
        widthlist: [
          { id: 1, value:'毫克(mg)' },{ id: 2, value: '克(g)' },{ id: 3, value: '千克(kg)' },{ id: 4, value: '吨(T)' }
        ],
        onlhei: '',//单重
        outLegth: '',
        lengthUnitid: '',
        Uintlist: [
          { id: 3, name: 'm' },{ id: 2, name: 'cm' },{ id: 1, name: 'mm' }
        ],
        outWidth: '',
        widthUnitid: '',
        widlist: [
          { id: 3, name: 'm' },{ id: 2, name: 'cm' },{ id: 1, name: 'mm' }
        ],
        outHeight: '',
        heightUnitid: '',
        heilist: [
          { id: 3, name: 'm' },{ id: 2, name: 'cm' },{ id: 1, name: 'mm' }
        ],
        outerShape: ''
      },
      allpack: [],//所有的包装数据（包装类别所在表格所在页面，用于点击右上角确定按钮时传值时获取相应数据）
      allpack2:[],//用于在已设置部分进行数据存储
      //allpack是要存储所有包装数据的！！！
      aupacks: [],//用于存储每次点击‘辅助包装物’那列的数字按钮时存储相应的辅助包装物数据
      //本层包装信息
      nowid: '',
      linkfo: 0,//用于区分辅助包装物数据的展示样式
      packaging: {//packaging代表‘本层包装的信息’弹窗中的数据
        pid: '',//单条包装数据的Id
        typeCeng:0,//判断层级
        //主要包装物
        mainpack: {
          codeid: '',//代号
          name: '',//名称
          specifications: '',//规格
          model: '',//型号
          weimingt: '',//重量单位
          onlyweit: '',//单重
        },
        //辅助包装物
        auxiliary: [
          // {
          //   aucoid: '',//代号
          //   auname: '',//名称
          //   auspecfication: '',//规格
          //   aumodel: '',//型号
          //   auweimin: '',//重量单位
          //   auonlywei: '',//单重
          // }
        ],
        //每个上层包装内有几个本层包装
        eveuppacnum: '',
        //单个包装内材料的数量
        onlypack: '',
        //单个包装的净重
        onlynetwei: '',
        //单个包装的毛重
        onlygrosswei: '',
        // //摆放于货架之后长度方向的尺寸
        // outLegth: '',
        // //摆放于货架之后宽度方向的尺寸
        // outWidth: '',
        // //摆放于货架之后高度方向的尺寸
        // outHeight: '',
        // outerShape: ''
      },
      //items: [],
      //modalData: {},
      addunten: 0,//1-点击增加按钮，2-点击确定按钮
      looket: 1,//区分是展示查看按钮还是展示编辑按钮
      desto: 0,//1-删除，2-停用
      desid: '',//停用需要用的包装id
      //deslist: '',
      yesorno: 0,//1-需要展示修改删除按钮，0-不展示
      maksune: '',//main-主要包装物，auxiliary-辅助包装物
      heiweight: '',//单重
      catok: '',//判断是否令编辑完成按钮可以点击
      potint: 0,//用于判断是从未设置过的材料，还是已经设置过的材料
      outSpace2: 0,//判断‘状态’一列展示的内容
      outSpace3: '',//判断‘编辑’按钮是否能够点击
      nowlank: [],//正在使用的包装数据
      stoplank: [],//停用使用的包装数据
      box: [],//用于专门存储表格中长、宽、高数据的数组
      createName1: '',//创建人
      createDate1: '',//创建时间
      //createName2: '',
      itemList: [],//辅助包装物
      stopid: '',//停用的辅助包装物的包装id
      fullNamet: '',//供应商名称
      createNameg: '',//创建人
      createDateg: '',//创建时间
      oven: '',//展示列表中的包装数据
      //lister: [],
      //zyname: '',
      //temleng: '',
      //structureList: [],
      structureList2: [],//调用接口获取到的包装物相关的全部数据
      newlast: [],//辅助包装物数据
      looker: 1,//区分表格中数据展示的格式
      looker2: 1,//???
      madestune: 0,//0-确定按钮变灰且不可点击，1-确定按钮变亮且可以点击调用函数(未设置），2-已设置
      //tryen: [],
      hasjson: '',//1-有数据，2-无数据
      hasjsont: '',//1-现在正在使用的，2-停用的数据
      alladdmig: '',//用于存储单个材料下的所有供应商设置的包装数据，用于提交时获取
      //settet: '',//用于判断设置按钮是否可点击
      respectively: '',//1-从’去设置‘开始的，2-从’查看‘开始的
      morebak: '',//1-是从‘查看更多包装方式’过来的,2-从‘新增包装方式’开始的,3-点击‘编辑’按钮过来的
      //4-从‘已停用的数据’过来的
      pacallmag: [],//用于存储每条供应商数据分别都设置了哪些包装数据，主要用来作为一个仓库存储数据，方面在渲染
      //或读取或提交数据时能区分开哪些数据对应哪些供应商数据，防止出现A的包装数据反而展示在B的下面。
      strid: '',//供应商id
      stall: '',
      detw: [],
      catunit: '',//材料计量单位
      setdiftruck: '',//1-未编辑，2-已编辑
      nextpic: '',//1-录入下一层,2-修改已有的
    }
  },
  watch: {
    widthlist(newVal, oldVal){
      console.log('someData changed from', oldVal, 'to', newVal);
    }
  },
  //页面刷新后立即执行的函数
  mounted(){
    this.startsetted();
    this.getundset();
  },
  computed: {
    // 使用计算属性来过滤出应该显示的选项
    filteredOptions() {
      return this.options.filter(item => item.visible);
    },
    isFromCompleter(){
    }
  },
  components: {},
  beforeRouteLeave(to, from, next){
    beforeRouteLeave(to,from,next,this)
  },
  created(){
    // 页面初始化方法，写在 created 里或者 mounted 里
    // 用于初始化页面数据
    initNav({mid: 'qv', name: '材料包装', pageName: this.pageName}, this.mateCreated, this)
  },
  destroyed(){},
  methods: {
    // saveData() {
    //   // 保存数据逻辑，比如更新allData中的相应对象
    //   const updatedData = this.allData.find(item => item.id === this.currentData.id);
    //   if (updatedData) {
    //     Object.assign(updatedData, this.currentData); // 更新数据
    //   }
    // },
    startsetted(){
      const startjson = { 'pageSize': 20,'currentPageNo': 1 };
      this.getsetlinkt(startjson);
    },
    mateCreated(){
      this.materlon = 0;
    },
    //分页
    pageClick(pageItem){
      let data = {pageSize: 20,currentPageNo: pageItem.page};
      this.matCreated(data);
    },
    pageClick2(pageItem2){
      let data2 = {pageSize: 20,currentPageNo: pageItem2.page};
      this.getunsetenk(data2);
    },
    matCreated(data){
      const json = {currentPageNo: data.currentPageNo,pageSize:data.pageSize,param: ''};
      this.getsetlinkt(json);
    },
    getsetlinkt(jsont){
      getsetlink(jsont).then(res => {
        this.list1 = res.data;//现在list1是个对象
        if(this.list1.data.length == 0){
          // this.list1.data = [{id:1,outerSn: 2,outerName:'图号1',model:2,specifications:4,unit:'个',num:1,supplierNum:2}];
        }
        //this.list1.page是有关分页功能的数据，this.list1.data是有关表格列表的数据
        let lank1 = this.list1.data;
        for(let a in lank1){
          let num = lank1[a].num;
          if(num === 0){lank1[a].num = "无包装";}else{lank1[a].num = lank1[a].num+"种包装方式";}
          // const jsong = {mtBaseId: lank1[a].id};
          // getsetlink(jsong).then(res => {
          //   const lankt = res.data.data;
          //   //假设这个材料设置了2条供应商，lankt中的关系id就有两个
          // })
          lank1[a].unit = lank1[a].unit === null ? "——" : lank1[a].unit;
        }
        this.list1.data = lank1;
      })
    },
    //获取首页列表上面的待设置包装信息的材料数量
    getundset(){
      getunset().then(res=> {
        this.listun = res.data.data;
      })
    },
    //主页搜索框功能
    searchsure(){
      const searjson = {currentPageNo: 1,pageSize: 20,param: this.searchKeyHome};
      this.getsetlinkt(searjson);
    },
    //点击右上角的‘编辑完成’按钮
    editcomple(){
      //debugger;
      //在这里需要获取该材料下的所有供应商的设置情况及数据，一并提交
      let alle = [];
      let ant = [];
      let json2 = {};
      //let bnd = [];
      //debugger;
      if(this.alladdmig === ""){
        const list3 = this.list3;
        for(let s in list3){
          if(s === 'unit'){}else{
            const jsone = {
              material: this.catid,//材料id
              supplierMaterial: list3[s].supplierMaterial,//供应关系id
              supplier: list3[s].supplier,//供应商id
            };
            alle.push(jsone);
            this.alladdmig = alle;
          }
        }
        // const jsone = {
        //   material: this.catid,//材料id
        //   // supplierMaterial: this.suppMaterial,//供应关系id
        //   // supplier: this.supplier,//供应商id
        // };
        // alle.push(jsone);
        // this.alladdmig = alle;
      }else{
        //debugger;
        console.log('1',this.alladdmig);//这里是有最新的数据的
        console.log('2',this.pacallmag);//这里只是一个空盒子，没有最新数据
        // this.pacallmag.forEach(item => {
        //   if(item.megbox.length === 0){
        //     this.alladdmig.forEach(item2 => {
        //       item.lengthUnitId = item2.lengthUnitId;
        //       item.lengthUnit = item2.lengthUnit;
        //       item.outerLength = item2.outerLength;
        //       item.widthUnitId = item2.widthUnitId;
        //       item.widthUnit = item2.widthUnit;
        //       item.outerWidth = item2.outerWidth;
        //       item.heightUnitId = item2.heightUnitId;
        //       item.heightUnit = item2.heightUnit;
        //       item.outerHeight = item2.outerHeight;
        //
        //     })
        //   }
        // })
        // this.alladdmig.forEach((item,index) => {
        //   this.pacallmag.forEach((item2,index2)=> {
        //     item2.lengthUnitId = item.lengthUnitId;
        //   })
        // })
        // console.log(this.list3);

        for(let p = 0;p<this.pacallmag.length;p++){
          let strid = this.pacallmag[p].strid;
          for(let s = 0;s<this.list3.length;s++){
            let sid = this.list3[s].supplier;
            if(strid === sid){
              this.pacallmag[p].material = this.catid;
              this.pacallmag[p].supplierMaterial = this.list3[s].supplierMaterial;
              //supplier: this.list3[p].supplier;//供应商id
            }
          }
        }
        console.log('3',this.list3);
        // console.log(this.allpack);
        // console.log(this.alladdmig);//这里的它不一定是正确的
        //return false;
        for(let j in this.list3){
          if(j === "unit"){}else{
            if(this.list3[j].outSpace === undefined ){}else{
              if(this.list3[j].outSpace === 0){//代表这个供应商数据需要进行包装物数据的设置
                //debugger;
                let json = {};
                for(let p = 0;p<this.pacallmag.length;p++){
                  let supplier = this.list3[j].supplier;
                  let strid = this.pacallmag[p].strid;
                  if(supplier === strid){
                    json = {
                      lengthUnitId: this.pacallmag[p].lengthUnitId,
                      lengthUnit: this.pacallmag[p].lengthUnit,
                      outerLength: this.pacallmag[p].outerLength,
                      widthUnitId: this.pacallmag[p].widthUnitId,
                      widthUnit: this.pacallmag[p].widthUnit,
                      outerWidth: this.pacallmag[p].outerWidth,
                      heightUnitId: this.pacallmag[p].heightUnitId,
                      heightUnit: this.pacallmag[p].heightUnit,
                      outerHeight: Number(this.pacallmag[p].outerHeight),
                      //outerShape: Number(this.pacallmag[p].outerShape),
                      material: this.pacallmag[p].material,
                      supplierMaterial: this.pacallmag[p].supplierMaterial,
                      supplier: this.pacallmag[p].strid,
                      list: []
                    };
                    let megbox = this.pacallmag[p].megbox;
                    for(let m = 0;m<megbox.length;m++){
                      const json2 = {
                        level: megbox[m].level,
                        materialCount: Number(megbox[m].materialCount),
                        itemList: [],
                        zyPackaging: {}
                      };
                      let auxiliary = megbox[m].auxiliary;
                      for(let u = 0;u<auxiliary.length;u++){
                        const aujson = {
                          wrappage: auxiliary[u].wrappage,
                          weight: auxiliary[u].weight,
                          weightUnit: auxiliary[u].weightUnit,
                        };
                        json2.itemList.push(aujson);
                      }
                      json2.zyPackaging.wrappage = megbox[m].mainpack.wrappage;
                      json2.zyPackaging.weight = megbox[m].mainpack.weight;
                      json2.zyPackaging.weightUnit = megbox[m].mainpack.weightUnit;
                      json.list.push(json2);
                      json.outerShape = megbox[m].outerShape;
                    }
                  }else{}
                }
                ant.push(JSON.parse(JSON.stringify(json)));//为了防止新加入数组的数据覆盖前面的数据
                //ant.push(JSON.parse(JSON.stringify(json)));
                // for(let k in this.alladdmig){
                //   if(Number(k) === 0){//k=0
                //     json = {
                //       lengthUnitId: this.alladdmig[k].lengthUnitId,
                //       lengthUnit: this.alladdmig[k].lengthUnit,
                //       outerLength: this.alladdmig[k].outerLength,
                //       widthUnitId: this.alladdmig[k].widthUnitId,
                //       widthUnit: this.alladdmig[k].widthUnit,
                //       outerWidth: Number(this.alladdmig[k].outerWidth),
                //       heightUnitId: this.alladdmig[k].heightUnitId,
                //       heightUnit: this.alladdmig[k].lengthUnit,
                //       outerHeight: Number(this.alladdmig[k].outerHeight),
                //       outerShape: Number(this.alladdmig[k].outerShape),
                //       material: this.alladdmig[k].material,
                //       supplierMaterial: this.list3[j].supplierMaterial,
                //       supplier: this.list3[j].supplier,
                //       list: this.alladdmig[k].list
                //     };
                //     json2 = {
                //       //material: this.alladdmig[k].material,
                //       // supplierMaterial: this.list3[j].supplierMaterial,
                //       // supplier: this.list3[j].supplier
                //     };
                //     //此时json.list中没有supplier那三个属性，而一开始k=0时是可以直接赋值的。
                //     json.list.forEach(items => {
                //      // items.material = json2.material;
                //       //items.supplierMaterial = json2.supplierMaterial;
                //       //items.supplier = json2.supplier;
                //       items.materialCount = Number(items.materialCount);
                //     });
                //     // for(let s in json.list){
                //     //   json.list[s].material = this.alladdmig[k].material;
                //     //   json.list[s].supplierMaterial = this.list3[j].supplierMaterial;
                //     //   json.list[s].supplier = this.list3[j].supplier;
                //     // }
                //     //ant.push(json);
                //     //console.log(json);
                //     // json = {
                //     //   heightUnit: this.alladdmig[k].heightUnit,
                //     //   heightUnitId: this.alladdmig[k].heightUnitId,
                //     //   lengthUnit: this.alladdmig[k].lengthUnit,
                //     //   lengthUnitId: this.alladdmig[k].lengthUnitId,
                //     //   list: this.alladdmig[k].list,//若设置了两个包装数据的话，现在list中只有一个包装数据
                //     //   material: this.alladdmig[k].material,
                //     //   outerHeight: this.alladdmig[k].outerHeight,
                //     //   outerLength: this.alladdmig[k].outerLength,
                //     //   outerShape: this.alladdmig[k].outerShape,
                //     //   outerWidth: this.alladdmig[k].outerWidth,
                //     //   supplier: this.list3[j].supplier,
                //     //   supplierMaterial: this.list3[j].supplierMaterial,
                //     //   widthUnit: this.alladdmig[k].widthUnit,
                //     //   widthUnitId: this.alladdmig[k].widthUnitId
                //     // };
                //     // ant.push(json);
                //   }else{//k>0
                //     //此时需要在json.list的基础上增加新的json数据
                //     //console.log(json);//这里包含第一条数据和两条数据共用的数据部分
                //     //console.log(this.alladdmig[k]);
                //     let cne = this.alladdmig[k].list;//这里包含第二条数据原有的list中的内容
                //     for(let c in cne){
                //       //cne[c].material = this.alladdmig[k].material;
                //       // cne[c].supplierMaterial = this.list3[j].supplierMaterial;
                //       // cne[c].supplier = this.list3[j].supplier;
                //     }
                //     json.list = json.list.concat(cne);
                //     //bnd = ant.concat(this.alladdmig[k].list);
                //   }
                // }
                //console.log('现在的数据正确么？',json);
                //console.log('现在的数据',ant);
                //arr.push(Object.assign({}, obj))
                // this.info.push(JSON.parse(JSON.stringify(e)))
              }else{
                //j=2
                //console.log('现在的数据',ant);
                //debugger;
                //console.log(this.list3[j]);
                //需要将未设置的供应商数据中的enabled的值改成数字
                let jsons = {};//这里其实应该不需要从pacallmag盒子里取物件吧？毕竟outSpace!==0的根本没设置那些包装数据，自然很多字段都不会有值。
                jsons = {
                  heightUnit: '',
                  heightUnitId: '',
                  lengthUnit: '',
                  lengthUnitId: '',
                  list: [],
                  material: '',
                  outerHeight: '',
                  outerLength: '',
                  outerShape: '',
                  outerWidth: '',
                  supplier: this.list3[j].supplier,
                  supplierMaterial: this.list3[j].supplierMaterial,
                  widthUnit: '',
                  widthUnitId: ''
                };
                for(let p = 0;p<this.pacallmag.length;p++){
                  let supplier = this.list3[j].supplier;
                  let strid = this.pacallmag[p].strid;
                  if(supplier === strid){
                    jsons.material = this.pacallmag[p].material;
                  }
                }
                ant.push(JSON.parse(JSON.stringify(jsons)));
                // if(this.list3[j].enabled === "——"){this.list3[j].enabled = 1;}
                // ant.push(JSON.parse(JSON.stringify(this.list3[j])));

                //ant = ant.concat(this.list3[j]);//突然觉得这里真的对么？this.list3[j]不该是一个json数据么？
                //不过也是，既然上面那行数据能够成功运用数组合二为一的方式将ant整合成一个完整的数组
                //那代表还是对的。
              }
            }
          }
        }
        //for(let a in this.alladdmig){//alladdmig中的数据是本次编辑中设置的全部包装数据，它们是一个包装方式
        //而如果是在批量编辑中进行设置的，则需要将alladdmig中两个包装都赋值上同一个供应商数据的id
        //for(let b in this.list3){
        // if(a === b){
        //   this.alladdmig[a].supplier = this.list3[b].supplier;
        //   this.alladdmig[a].supplierMaterial = this.list3[b].supplierMaterial;
        // }
        //}
        //}
      }
      //console.log(ant);
      // debugger;
      //console.log('3.25-14:10',ant);
      //return false;
      this.alladdmig = ant;
      this.alladdmig = JSON.stringify(this.alladdmig);
      if(typeof this.alladdmig === 'string'){
      }
      const keyjson = { data: this.alladdmig };
      addPacking(keyjson).then(res => {
        if(res.data.success === 1){
          this.materlon = 1;
          const json = {pageSize: 20,currentPageNo: 1};
          this.getunsetenk(json);
        }
      })
      // const edt = {mtBaseId: this.catid};
      // madesett(edt).then(res => {
      //   if(res.data.success === 1){//操作成功
      //     this.materlon = 1;
      //     const json = {pageSize: 20,currentPageNo: 1};
      //     this.getunsetenk(json);
      //   }
      // })
    },
    //未设置材料列表搜索框功能
    searchsure2(){
      const searjson2 = {currentPageNo: 1,pageSize: 20,param: this.searchKeyUse};
      this.getunsetenk(searjson2);
    },
    //去设置
    mateWszChn(){
      this.morebak = '';
      this.materlon = 1;
      this.respectively = 1;
      this.searchKeyUse = "";
      let json2 = {pageSize: 20,currentPageNo: 1};
      this.getunsetenk(json2);
    },
    //获取未设置材料列表
    getunsetenk(data){
      getunsetlink(data).then(res => {
        //debugger;
        this.list2.data = res.data.data;
        this.list2.page = res.data.page;
        const lank2 = this.list2.data;
        for(let b in lank2){
          lank2[b].specifications = lank2[b].specifications === "" ? "——" : lank2[b].specifications;
          lank2[b].model = lank2[b].model === "" ? "——" : lank2[b].model;
          lank2[b].unit = lank2[b].unit === null ?  "——" : lank2[b].unit;
          if(lank2[b].supplierNum === 0){lank2[b].settet = false;}else{lank2[b].settet = true;}
        }
      })
    },
    //返回
    gobacktun(t,d){
      //debugger;
      //如若让‘查看’和‘编辑‘都共用几个页面的话， 返回按钮就可以仿照工序优化中的返回写法，根据区分不同属性值判断是查看还是设置
      if(t === 1){
        this.materlon = 0;
        this.allpack = [];
        //console.log('2025.2.8-1',this.allpack);
        const json1 = { 'pageSize': 20, 'currentPageNo': 1};
        this.getsetlinkt(json1);
        this.getundset();
      }else if(t === 2){
        //debugger;
        this.materlon = 1;
        this.allpack = [];
        //console.log('2025.2.8-1',this.allpack);
        const json2 = { pageSize: 20, currentPageNo: 1};
        this.getunsetenk(json2);
      }else if(t === 3){
        //debugger;
        if(this.respectively === 2){d = 1;}else if(this.respectively === 1){d = 0;}
        if(d === 1){//下面是从‘查看’中执行到这里时
          //debugger;
          if(this.morebak === 1){
            this.materlon = 11;this.morebak = '';
          }else if(this.morebak === 2 || this.morebak === 3){
            this.materlon = 6;
          }else if(this.morebak === 4){
            this.materlon = 8;
          }else{
            this.materlon = 5;
            //这里需要返回上一页的同时让上一页中勾选的效果清空
            //console.log('5.16-8:59',this.list7);
            //console.log('5.16-8:52',this.list1.data);
            //console.log('5.16-9:02',this.ketcs);
            this.ketcs.outSpace4 = '';
            //console.log('5.21-16:51',this.catunit);
            const supjson = {mtBaseId: this.catid};
            this.looknexten(supjson,this.catunit);
          }
          //console.log('乙巳年正月十六-申拾伍',this.allpack);
        }else{//下面是从‘去管理’执行到这里
          this.materlon = 2;
          //感觉应该不需要清空allpack，或者是在不同情况下判断是否需要清空allpack
          //this.allpack = [];
          //当给一个供应商数据编辑，但并未点击右上角的确定按钮时，返回上一页后再对那条数据进行编辑，则需要清空表格中的缓存数据
          //该如何判断是否点击过右上角的确定按钮呢
          //console.log('乙巳年正月十六-申拾陆',this.allpack);
          // if(this.allpack.mdasune === true){}else{
          //   this.allpack = [];
          // }
          // this.packaging.auxiliary = [];
          //this.packaging.mainpack = {};
          const json3 = {mtBaseId: this.catid};
          //console.log('5.15-9:58',this.list3.unit);
          //console.log('5.22-8:28',this.catunit);
          this.getsuptent(json3,this.catunit,'back');
          this.catlist = this.catlist2;
        }
        //this.allpack = [];
      }else if(t === 4){
        this.materlon = 3;
      }else if(t === 5){
        this.materlon = 5;
        // debugger;
        // if(this.morebak === 1){  this.materlon = 11;}else if(this.morebak === 3){this.materlon = 4;}
        //console.log('5.21-16:52',this.catunit);
        const supjson = {mtBaseId: this.catid};
        this.looknexten(supjson,this.catunit);
      }else if(t === 6){
        //debugger;
        //console.log(this.morebak);
        this.morebak = '';
        this.materlon = 6;
        //console.log('3.21',this.desid);
        // let json = {id: this.desid,enabled: 1};
        const json = {mtBaseId: this.catid,supplierMaterial: this.suppMaterial};
        seapackinfon(json).then(res => {
          //debugger;
          let nowlink = this.nowlank = res.data.data.list || [];
          let stoplink = this.stoplank = res.data.data.suspendList || [];//暂停使用的包装
          //console.log('5.21-16:39',this.catunit);
          for(let n in nowlink){
            let createName = this.createName1 = nowlink[n].createName;//创建人
            let createDate = this.createDate1 = nowlink[n].createDate;//创建时间
            if(nowlink[n].outerShape === 1){nowlink[n].outSpace = "长方体";}else if(nowlink[n].outerShape === 2){nowlink[n].outSpace = "圆柱体"}
            else if(nowlink[n].outerShape === 3){nowlink[n].outSpace = "其他形状"}
          }
          this.list8 = this.nowlank = nowlink || [];
          //console.log('4.2-14:51',this.pacallmag);
          this.listb = this.stoplink = stoplink || [];
          if(this.list81.length !== 0){this.list81 = [];}
          if(this.list8.length === 0){
            this.oven = '';
            this.hasjson = 2;
          }else{
            for(let s in this.list8){
              //debugger;
              //this.list81 = [];
              s = Number(s);
              if(s === 0){
                const json81 = this.list8[s];
                this.list81.push(json81);
              }
              // this.list8[s].box = [];
              // const json8 = {
              //   outerLength: this.list8[s].outerLength,outerWidth: this.list8[s].outerWidth,
              //   outerHeight: this.list8[s].outerHeight,outSpace: this.list8[s].outSpace,
              //   materialCount: this.list8[s].materialCount,netWeight: this.list8[s].netWeight,
              //   netUnit: this.list8[s].netUnit,grossWeight: this.list8[s].netUnit,
              //   grossUnit: this.list8[s].grossUnit
              // };
              // this.list8.box.push(json8);
            }
            this.hasjson = 1;
            this.oven = 5;
          }
          // console.log('4.2-15:08',this.list8);
          // console.log('4.2-15:09',this.list81);
        })
      }else if(t === 7){
        //debugger;
        switch (this.morebak) {
          case 1:
            return this.materlon = 11;
          case 3:
            return this.materlon = 6;
          case 4:
            return this.materlon = 8;
        }
        //if(this.morebak === 1){this.materlon = 11;}else if(this.morebak === 2){}
        //if(this.morebak === 1){this.materlon = 11;this.morebak = '';}else if(this.morebak === 3){this.materlon = 6;}
        //this.materlon = 6;
        //this.materlon = 8;
      }else if(t === 8){
        this.materlon = 9;
      }
    },
    //设置
    actprocess(objs){
      //console.log('2.13-1',this.allpack);
      //debugger;
      this.pacallmag = [];//这里需要将存全部数据的pacallmag清空，方便后面进行新数据的加入
      this.respectively = 1;//从首页的’去设置‘开始的
      this.catok = 0;
      this.materlon = 2;
      const id = objs.id;//材料id
      // this.suppMaterial = objs.supplierMaterial;//供应关系Id
      // this.supplier = objs.supplier;//供应商id
      //if(objs.fullName === undefined){objs.fullName = "——";}
      if(objs.specifications === "——" && objs.model !== "——") {
        this.catlist = objs.code + '/' + objs.name + '/' + objs.model + '/' + objs.unit;
      }else if(objs.specifications !== "——" && objs.model === "——"){
        this.catlist = objs.code + '/' + objs.name + '/' + objs.specifications + '/' + objs.unit;
      }else if(objs.specifications === "——" && objs.model === "——") {
        this.catlist = objs.code + '/' + objs.name + '/' + objs.unit;
      }else{
        this.catlist = objs.code + '/' + objs.name + '/' + objs.specifications + '/' + objs.model + '/'+ objs.unit;
      }
      //如果要展示‘供应商代号’和‘供应商名称’，就需要在能获取到供应商数据的时候读取。
      this.catliststa = this.catlist;
      this.catlist2 = this.catlist;
      this.potint = 0;
      // this.catlist = {
      //   id: objs.id,name: objs.name,specifications: objs.specifications,
      //   model: objs.model,unit: objs.unit
      // };
      const jsona = {mtBaseId: id};
      this.catid = id;
      //objs是一条json数据
      //this.list3 = [];不能在这里清空this.list3
      let unit = objs.unit;//计量单位
      this.catunit = objs.unit;
      this.getsuptent(jsona,this.catunit);
    },
    getsuptent(jsona,unit,bck){//unit代表点击‘编辑’获取的计量单位
      //console.log('2.13-2',this.allpack);
      getsuplink(jsona).then(res => {
        //debugger;
        let list3 = res.data.data;//这个位置获取到的数据确实是正确的数量
        //console.log('4.3-15:20',list3);
        //console.log('4.3-15:21',this.list3);//不是每次都有问题，但似乎在对单条数据进行编辑后再编辑多条数据的就会出现这个问题。
        // this.list3.unit = unit;
        //console.log('5.15-9:42',this.list3);
        //在页面渲染是用到this.list3，且每次跳转到下个页面时都是调用接口获取了新数据，是不是可以理解成this.list3其实真的可以直接对等list3呢？
        //this.list3 = list3;
        //console.log(this.list3);
        //[{outSpace:null},{outSpace:null},{outSpace:null}]
        //这里需要将list3的数据和this.list3的数据整合成一个
        //现在出现了一个重复性的错误：当供应商列表数据只有一条时，点击设置却出现了三条
        for(let a = 0;a<list3.length;a++){
          for(let b = 0;b<this.list3.length;b++){
            if(this.list3[b].outSpace === null){
              this.list3 = list3;
              break;
            }else if(this.list3[b].supplier){
              if(this.list3[b].outSpace === undefined){}else{break;}
              //a=1,b=0 break;
            }else{
              if(a === b){
                this.list3[b].supplier = list3[a].supplier;
                this.list3[b].num = list3[a].num;
                this.list3[b].flag = list3[a].flag;
                this.list3[b].fullName = list3[a].fullName;
                this.list3[b].supplierMaterial = list3[a].supplierMaterial;
                this.list3[b].gs = list3[a].gs;
                this.list3[b].hnum = list3[a].hnum;
                this.list3[b].enabled = list3[a].enabled;
                // console.log('5.12-14:00',this.catlist2);
                // //codeName-供应商代号
                // this.catlist2 = this.catlist2 + '/' + list3[a].codeName + '/' + list3[a].fullName;
              }
            }
          }
        }
        //for(let c = 0;c<this.list3.length;c++){
        //console.log('5.12-14:00',this.catlist2);
        //codeName-供应商代号
        //this.catlist2 = this.catlist2 + '/' + list3[c].codeName + '/' + list3[c].fullName;
        //}
        //console.log('5.12-14:05',this.catlist2);
        if(this.list3.length === 0){
          this.list3 = [{supplierMaterial: 1, supplier: 1,fullName: '供应商1',enabled: '已停用',outSpace2: 0}];
          this.options = [
            { value: 0, label: '需要',isVisible: true},
            { value: 1,label: '不需要',isVisible: true},
            {value: 2,label: '暂不编辑',isVisible: true}
          ];
        }else{
          // 使用 filter 方法过滤掉没有 'supplier' 属性的对象
          this.list3 = this.list3.filter(item => 'supplier' in item);
          let lank3 = this.list3;
          for(let c in lank3){//这里是渲染数据到表格中
            // lank3[c].enabled = lank3[c].enabled === 1 ? "——" : "已停用";
            //flag: 0-未设置的
            if(c === "unit" || c > lank3.length){}else{
              if(lank3[c].enabled === 1 || lank3[c].enabled === '1' || lank3[c].enabled === '——'){
                lank3[c].enabled = "——";
                if(lank3[c].fullName === undefined){lank3[c].fullName = "——";}
                this.catfullname = lank3[c].fullName || "——";
                this.options = [
                  { value: 0, label: '需要',isVisible: true},
                  { value: 1,label: '不需要',isVisible: true}
                ];
              }else{
                lank3[c].enabled = "已停用";
                if(lank3[c].fullName === undefined){lank3[c].fullName = "——";}
                this.catfullname = lank3[c].fullName || "——";
                this.options = [
                  { value: 0, label: '需要',isVisible: true},
                  { value: 1,label: '不需要',isVisible: true},
                  {value: 2,label: '暂不编辑',isVisible: true}
                ];
              }
            }
          }
        }
        //应该定义一个变量用于区分是未设置过的材料还是已经至少设置了一遍的材料
        if(this.potint === 0){
          this.list3.forEach((selectItem,indexItem) => {
            selectItem.outSpace = null;//将数组中每条数据对应的选项都默认不选中  outSpace对应’需要‘、’不需要‘、’暂不需要‘
            selectItem.outSpace2 = 0;//outSpace2对应’尚未编辑‘、’已编辑‘、’暂不编辑‘
            selectItem.outSpace3 = '';//outSpace3对应’编辑‘按钮的可点击和不可点击两个状态
          });
          this.list3.unit = unit;
        }else{
          //debugger;
          let lank32 = this.list3;
          //现在需要知道的是方才是对哪条数据进行设置了
          //let a = {};let n = 1;
          //let clonse = '';
          let kewed = true;
          for(let t in lank32){
            //debugger;
            //console.log('5.23-10:16',this.supplier);//this.supplier只能代表方才设置的是哪条数据
            let id = lank32[t].supplier;
            if(id === this.supplier){
              //这里需要尤为注意下：在编辑页若未进行包装编辑，而是点击的‘返回上一页’则不可以给这条供应商数据算作是编辑了
              if(bck === 'back'){//代表是点击了“返回上一页”的
                lank32[t].outSpace = 3;
                lank32[t].outSpace2 = 0;
                lank32[t].outSpace3  = 1;
              }else{
                lank32[t].outSpace = 0;
                lank32[t].outSpace2 = 1;
                lank32[t].outSpace3 = 0;
              }
            }else{
              lank32[t].outSpace = lank32[t].outSpace === undefined ? lank32[t].outSpace = null : lank32[t].outSpace;
              lank32[t].outSpace2 = lank32[t].outSpace2 === undefined ? lank32[t].outSpace2 = 0 : lank32[t].outSpace2;
              lank32[t].outSpace3 = lank32[t].outSpace3 === undefined ? lank32[t].outSpace3 = null : lank32[t].outSpace3;
            }
            if(lank32[t].outSpace2 === 1 || lank32[t].outSpace2 === 2){
              //t = Number(t);
              //这里遇到一个问题：因为可以一次性编辑多条供应商数据，故不能因为一条数据的outSpace2 = 1就认为列表中所有的供应商数据
              //都已经编辑完毕，故需要判断下是否所有的供应商数据都已经编辑完毕。
              //不该仅仅因为一条数据的clonse= true了就让按钮能点击
              //lank32[t].clonse = true;
              //this.catok = 1;
            }else{
              //lank32[t].clonse = false;
              //this.catok = 0;
              kewed = false;
            }
          }
          if(kewed === false){this.catok = 0;}else{this.catok = 1;}
          this.list3.unit = unit;
          if(unit === undefined){
            this.list3.unit = this.sizeForm.unit;
          }
          this.list3 = lank32;
        }
        //假设A=list3,B=this.pacallmag
        // 遍历 A 数组// 遍历 A 数组
        for (let i = 0; i < list3.length; i++) {
          // 定义一个新的 JSON 对象
          let json = { strid: list3[i].supplier,megbox: [] };
          // 检查 B 中是否已经存在具有相同 strid 的对象
          let found = false;
          for (let j = 0; j < this.pacallmag.length; j++) {
            if (this.pacallmag[j].strid === json.strid) {
              found = true;break;
            }
          }
          // 如果没有找到匹配项，则将新的 JSON 对象添加到 B 中
          if (!found) {
            this.pacallmag.push(json);
          }
        }
        // if (this.pacallmag.length === 0) {
        //   // 如果 B 是空数组，只将 A 中每个对象的 id 添加到 B 中
        //   for (let i = 0; i < list3.length; i++) {
        //     let json3 = {strid: list3[i].supplier};
        //     this.pacallmag.push(json3);
        //   }
        // }else{
        //   // 遍历 A 数组，检查 B 中是否已存在相应的对象
        //   for (let i = 0; i < list3.length; i++) {
        //     let found = false; // 标志变量，用于标记是否在 B 中找到了匹配项
        //     // 遍历 B 数组，寻找与 A[i] 相同的对象
        //     for (let j = 0; j < this.pacallmag.length; j++) {
        //       // 这里假设 B 中存储的是完整对象
        //       if (list3[i].supplier === this.pacallmag[j].id) {
        //         found = true; // 找到匹配项
        //         break; // 退出内层循环
        //       }
        //     }
        //     // 如果没有找到匹配项，则将 A[i] 添加到 B 中
        //     if (!found) {
        //       let json3 = {strid: list3[i].supplier};
        //       this.pacallmag.push(json3);
        //     }
        //   }
        // }
        //console.log('这个数据是从接口中获取到的么?',list3);
        // for(let s3 in list3){
        //   s3 = Number(s3);
        //   let trktrue = false;// 标志变量，用于标记是否在 B 中找到了匹配项
        //   for(let )
        // if(this.pacallmag.length === 0){
        //   let json3 = {
        //     strid: list3[s3].supplier
        //   };
        //   this.pacallmag.push(json3);
        // }else{
        // for(let p1 in this.pacallmag){
        //   if(list3[s3].supplier === this.pacallmag[p1].strid){break;}else{
        //     let json3 = { strid: list3[s3].supplier };
        //     this.pacallmag.push(json3);break;
        //   }
        // }
        //}
        // for(let p1 in this.pacallmag){
        //   if(list3[s3].supplier === this.pacallmag[p1].strid){this.pacallmag[p1].trktrue = true;}else{
        //     this.pacallmag[p1].trktrue = false;
        //   }
        // }
        // for(let p2 in this.pacallmag){
        //   if(this.pacallmag[p2].trktrue === false){
        //     let json3 = {
        //       strid: list3[s3].supplier
        //     };
        //     this.pacallmag.push(json3);
        //   }
        // }
        //}
        //console.log('数据成功存储了么',this.pacallmag);
      })
    },
    //选择
    choseonpont(index,outSpace,outs){
      // this.suppMaterial = outs.supplierMaterial;//供应关系id
      // this.supplier = outs.supplier;//供应商id
      //需要实现下面的效果：1.第一次点击一个选项展示为选中状态，第二次点击该选项的时候该选项变成未选中状态
      //应该是需要在表格中状态那栏内容都变成‘已编辑’或‘暂不编辑’后右上角按钮能点击吧
      //debugger;
      if(outSpace === 0){//需要
        outs.outSpace2 = 0;
        outs.outSpace3 = 0;
        this.a = this.list3.map(item => item.outSpace2);
        for(let d in this.a){
          if(this.a[d] === 0){this.catok = 0;return false;}else{this.catok = 1;}
        }
      }else if(outSpace === 1){//不需要
        outs.outSpace2 = 1;
        outs.outSpace3 = '';
        // this.suppMaterial = outs.supplierMaterial;//供应关系id
        // this.supplier = outs.supplier;//供应商id
        //this.idArray = this.originalArray.map(item => item.id);
        this.a = this.list3.map(item => item.outSpace2);
        for(let d in this.a){if(this.a[d] === 0){this.catok = 0;return false;}else{this.catok = 1;}}
      }else if(outSpace === 2){//暂不编辑
        outs.outSpace2 = 2;
        outs.outSpace3 = '';
        this.a = this.list3.map(item => item.outSpace2);
        for(let d in this.a){if(this.a[d] === 0){this.catok = 0;return false;}else{this.catok = 1;}}
      }
    },
    //取消选中效果
    clentendk(e,outSpace,index,outs){//index是改变选中状态的按钮所在的那条数据的索引值
      //console.log('2.13-4',this.allpack);
      //e=option.value,outSpace: 0-需要、1-不需要、2暂不编辑,index=这条数据在整个数组中的索引值，outs=这条数据的全部数值
      for(let s in this.list3){
        if(s === index){
          if(e === outSpace){//outSpace = null;
          }else{//outSpace = e;//会不会是因为值的变化只在方法里，而未渲染到页面上的缘故呢
          }
        }
      }
    },
    neeoendk(e,outSpace,index,outs){
      for(let s in this.list7){
        if(s === index){
          if(e === outSpace){
          }else{}
        }
      }
    },
    //批量编辑
    tunbuitedit(unit){
      //this.respectively = 1;
      //console.log('2025.2.8-从哪里来？',this.respectively);
      this.materlon = 3;
      this.madesue = 1;
      //console.log('5.15-9:45',unit);
      const jsont = {mtBaseId: this.catid};
      getsuplink(jsont).then(res => {
        //debugger;
        this.list4 = res.data.data;
        if(this.list4.length === 0){
          this.list4 = [{suppliermaterial: 2, supplier: 2, fullName: '供应商2',enabled: 1}];
        }else{
          let lank4 = this.list4;
          for(let d in lank4){
            if(lank4[d].enabled === 1 || lank4[d].enabled === '1'){
              lank4[d].enabled = "——";
            }else {
              lank4[d].enabled = "已停用";
            }
          }
        }
      })
    },
    //点击批量编辑列表页面的确定按钮跳转页面
    morechose(unit){
      //debugger;
      this.materlon = 4;
      //console.log('4.10-11:29',this.allpack);
      this.allpack = [];
      //console.log("2025.2.8-从哪里来",this.respectively);
      // if(this.catfullname === null || this.catfullname === ""){
      //   this.catfullname = "供应商1";
      // }
      //debugger;
      //console.log(this.list4);
      //console.log(this.multipleSelection);//数组中包含‘批量编辑’列表中选中的数据
      //console.log(this.list3);
      //console.log(this.potint);
      //console.log('5.15-8:29',this.multipleSelection);
      this.multipleSelection.forEach(itemm => {
        const mid = itemm.supplier;
        this.list3.forEach(itemt => {
          const sid = itemt.supplier;
          if(mid === sid){
            itemt.outSpace = 0;//需要
            itemt.outSpace2 = 1;//已编辑
            itemt.outSpace3 = 0;//编辑可以点击
          }
        })
        this.catlist = this.catliststa + '/' + itemm.codeName + '/' + itemm.fullName;
      })
      //console.log('5.8-15:34',this.catlist);
      //this.catlist = this.catlist + '/' + this.catfullname;
      this.looker = 1;
      //console.log('5.8-15:56',unit);
      this.list5 = [{newer: '最外层包装',eveuppacnum: '——',onlypack: '',onlynetwei: '',onlygrosswei: ''}];//这里缺一个unit
      //this.list5.unit = unit;
      // this.list5 = this.allpack;
      // let lank5 = this.list5;
      // for(let e in lank5){
      //   if(lank5[e].eveuppacnum === undefined){}else{
      //     e = Number(e);
      //     lank5[e].id = e;
      //     if(e === 0){
      //       lank5[e].newer = "最外层包装";
      //     }else{
      //       const chineseNumbers = ['零','一', '二', '三', '四', '五', '六', '七', '八', '九','十'];
      //       lank5[e].newer = "最外层包装的下"+chineseNumbers[e]+"层包装";
      //     }
      //     lank5[e].onlynetwei = lank5[e].onlypack*0;
      //     //辅助包装物重量指的是？辅助包装物的所有单重之和？
      //     lank5[e].onlynetwei = Number(lank5[e].onlynetwei);
      //     lank5[e].mainpack.onlyweit = Number(lank5[e].mainpack.onlyweit);
      //     //lank5[e].onlygrosswei = lank5[e].onlynetwei+lank5[e].mainpack.onlyweit;
      //     const lanka1 = lank5[e].auxiliary;//辅助包装物数据
      //     let all = lanka1.reduce((sum, item) => sum + item.auonlywei, 0);
      //     all = Number(all);
      //     lank5[e].onlygrosswei = lank5[e].onlynetwei+lank5[e].mainpack.onlyweit+all;
      //   }
      // }
      // this.looker = 0;
      // this.allpack = this.list5 = lank5;
      // console.log(this.multipleSelection);
      // this.doiffet = 1;
      // this.nextpict(0,this.doiffet);
      //this.allpack = this.allpack === [] ? this.list5 = [] : this.list5 = this.allpack;
    },
    //多选框
    handleSelectionmat(val){
      // console.log('2025.2.8-从哪里来',this.respectively);
      this.multipleSelection = val;//val是个数组
      let lend = this.multipleSelection.length;
      lend = lend === 0 ? this.madesue = 1 : this.madesue = 2;
      this.choslist = this.multipleSelection.map(item => (item.id));//用于将数组中某一个属性的值取出来
      // //保存在一个新的数组中
    },

    nextpict(objn,t2,unit,kt){//这里是先选中的‘需要’还是‘不需要’之后点击的‘编辑’按钮
      // console.log('5.16-9:12',unit);
      // console.log('3.24-1',this.pacallmag);//这里现在有四条供应商数据各自的id
      // console.log('3.24-2',this.allpack);//这里面现在是点击’编辑‘按钮所在的那条数据的id
      this.doiffet = 0;
      this.ketcs = objn;
      //console.log('5.16-10:51',objn);
      //为了防止出现给一条供应商数据设置包装数据后，设置第二条供应商数据时出现给第一条供应商设置的包装数据赋值在了第二条数据上，则可以用下面的方法进行尝试：
      //在点击表格中的‘编辑’按钮时将该行数据的id存入allpack中用于区分存入allpack中的包装数据是哪一条供应商的。
      if(objn === 0){//在查看设置中点击；新增包装方式‘
        this.looker2 = 1;
        this.gobck22 = 1;
        this.gobck2 = 1;
        this.doiffet = t2;
        this.gobck = 1;
        this.looker = 1;
        this.makesune = 'takesure';
        this.list5 = [{newer: '最外层包装',eveuppacnum: '——',onlypack: '',onlynetwei: '',onlygrosswei: ''}];
        //console.log('2.13-5',this.allpack);
      }else{
        this.setdiftruck = 1;
        this.madestune = 1;
        if(kt === 'kt'){//所以有kt的是查看喽？
          this.gobck = 0;
          this.setdiftruck = 2;
        }else{
          this.gobck = 0;
          this.setdiftruck = 1;
        }
        this.gobck22 = '';
        this.gobck2 = 0;
        this.makesune = 'takesure';
        this.materlon = 4;
        //this.gobck2 = '';
        this.suppMaterial = objn.supplierMaterial;//供应关系id
        this.supplier = objn.supplier;//供应商id
        if(objn.fullName === undefined){objn.fullName = "——";}
        this.catlist = this.catliststa+'/'+objn.codeName + '/' + objn.fullName;
        this.looker = 1;
        this.list5 = [{newer: '最外层包装',eveuppacnum: '——',onlypack: '',onlynetwei: '',onlygrosswei: ''}];
        //this.allpack.length = 0;
        if(objn.c === 0 || objn.outSpace2 === null){
          if(this.setdiftruck === 1){
            this.allpack.length = 0;
          }else if(this.setdiftruck === 2){
            this.allpack2.length = 0;
          }
        }else{
          //debugger;
          // console.log(this.allpack);
          // console.log('4.10-11:41',this.pacallmag);
          if(this.setdiftruck === 1){this.allpack = [];}else if(this.setdiftruck === 2){this.allpack2 = [];}
          //this.allpack = [];
          //准确来讲就是可以这样理解：1.假设在获取供应商数据时自定义一个数组a用于存储每个供应商数据的id,并用json数据的格式进行存储；
          //2.在点击供应商数据列表中单行的‘编辑’时将该行供应商数据的id存入this.allpack中，然后正常进行包装数据的存储；
          //3.待点击右上角‘确定’按钮返回到供应商列表页后，根据每条json数据对应的id同数组a进行比较，并将对应的数据存入数组a中，
          // 待再次点击列表中某一行供应商,数据的编辑时，就根据获取到的单行供应商id同数组a中存入的供应商id进行比对，
          // 相同就将相应的json数据在页面表格中渲染，
          //渲染时可以将需要渲染的数据赋值给已经清空后的this.allpack中，然后正常进行包装数据的修改及新增，以此类推。
          for(let a =0;a<this.pacallmag.length;a++){//这里其实可以将pacallmag中对应的数据直接对等allpack即可
            if(this.pacallmag[a].strid === objn.supplier){
              let megbox = this.pacallmag[a].megbox;
              for(let m = 0;m<megbox.length;m++){
                if(megbox.length === 0){
                  if(this.setdiftruck === 1){this.allpack.length = 0;}
                  else if(this.setdiftruck === 2){this.allpack2.length = 0;}
                }else{
                  let json = {auxiliary: [],eveuppacnum: "",id:"",isExists: '',mainpack: '',newer: '',onlygrosswei: '',onlynetwei: '',onlypack: '',choid: ''};
                  json.auxiliary = megbox[m].auxiliary;
                  json.eveuppacnum = megbox[m].eveuppacnum;
                  json.id = megbox[m].id;
                  json.isExists = megbox[m].isExists;
                  json.mainpack = megbox[m].mainpack;
                  json.newer = megbox[m].newer;
                  json.onlygrosswei = megbox[m].onlygrosswei;
                  json.onlynetwei = megbox[m].onlynetwei;
                  json.onlypack = megbox[m].onlypack;
                  json.choid = megbox[m].choid;
                  if(this.setdiftruck === 1){
                    this.allpack.outHeight = megbox[m].outHeight;
                    this.allpack.outLegth = megbox[m].outLegth;
                    this.allpack.outWidth = megbox[m].outWidth;
                    this.allpack.outerShape = megbox[m].outerShape;
                    this.allpack.push(json);
                  }else if(this.setdiftruck === 2){
                    this.allpack2.outHeight = megbox[m].outHeight;
                    this.allpack2.outLegth = megbox[m].outLegth;
                    this.allpack2.outWidth = megbox[m].outWidth;
                    this.allpack2.outerShape = megbox[m].outerShape;
                    this.allpack2.push(json);
                  }
                  // if(this.allpack.length === 0){
                  //   this.allpack.push(json);
                  // }else{
                  //   for(let p = 0;p<this.allpack.length;p++){//这里可能之前就有两条数据，故如果直接覆盖的话会出现两条相同的数据
                  //     this.allpack[p].auxiliary = json.auxiliary;
                  //     this.allpack[p].eveuppacnum = json.eveuppacnum;
                  //     this.allpack[p].id = json.id;
                  //     this.allpack[p].isExists = json.isExists;
                  //     this.allpack[p].mainpack = json.mainpack;
                  //     this.allpack[p].newer = json.newer;
                  //     this.allpack[p].onlygrosswei = json.onlygrosswei;
                  //     this.allpack[p].onlynetwei = json.onlynetwei;
                  //     this.allpack[p].onlypack = json.onlypack;
                  //     this.allpack[p].choid = json.choid;
                  //   }
                  // }
                }
              }
              // if(this.pacallmag[a].id){//可以将pacallmag中的这条数据赋值给allpack
              //   let json = {auxiliary: [],eveuppacnum: "",id:"",isExists: '',mainpack: '',newer: '',onlygrosswei: '',onlynetwei: '',onlypack: '',choid: ''};
              //   let megbox = this.pacallmag[a].megbox;
              //   for(let m = 0;m<megbox.length;m++){
              //     json.auxiliary = megbox[m].auxiliary;
              //     json.eveuppacnum = megbox[m].eveuppacnum;
              //     json.id = megbox[m].id;
              //     json.isExists = megbox[m].isExists;
              //     json.mainpack = megbox[m].mainpack;
              //     json.newer = megbox[m].newer;
              //     json.onlygrosswei = megbox[m].onlygrosswei;
              //     json.onlynetwei = megbox[m].onlynetwei;
              //     json.onlypack = megbox[m].onlypack;
              //     json.choid = megbox[m].choid;
              //     this.allpack.outHeight = megbox[m].outHeight;
              //     this.allpack.outLegth = megbox[m].outLegth;
              //     this.allpack.outWidth = megbox[m].outWidth;
              //     this.allpack.outerShape = megbox[m].outerShape;
              //     if(this.allpack.length === 0){
              //       this.allpack.push(json);
              //     }else{
              //       for(let p = 0;p<this.allpack.length;p++){
              //         this.allpack[p].auxiliary = json.auxiliary;
              //         this.allpack[p].eveuppacnum = json.eveuppacnum;
              //         this.allpack[p].id = json.id;
              //         this.allpack[p].isExists = json.isExists;
              //         this.allpack[p].mainpack = json.mainpack;
              //         this.allpack[p].newer = json.newer;
              //         this.allpack[p].onlygrosswei = json.onlygrosswei;
              //         this.allpack[p].onlynetwei = json.onlynetwei;
              //         this.allpack[p].onlypack = json.onlypack;
              //         this.allpack[p].choid = json.choid;
              //       }
              //     }
              //   }
              //   //json.auxiliary = this.pacallmag[a].megbox.auxiliary;
              //   //json.eveuppacnum = this.pacallmag[a].megbox.eveuppacnum;
              //   //json.id = this.pacallmag[a].megbox.id;
              //   //json.isExists = this.pacallmag[a].megbox.isExists;
              //   //json.mainpack = this.pacallmag[a].megbox.mainpack;
              //   //json.newer = this.pacallmag[a].megbox.newer;
              //   //json.onlygrosswei = this.pacallmag[a].megbox.onlygrosswei;
              //   //json.onlynetwei = this.pacallmag[a].megbox.onlynetwei;
              //   //json.onlypack = this.pacallmag[a].megbox.onlypack;
              //   //json.choid = this.pacallmag[a].megbox.choid;
              // }else{
              //   this.allpack.length = 0;
              // }
            }
          }
          if(this.setdiftruck === 1){
            if(this.allpack.length === 0){
              let json1 = {};
              json1.choid = objn.supplier;
              this.allpack.push(json1);
            }else{
              //debugger;
              this.list5 = this.allpack;
              let lank5 = this.list5;
              for(let e in lank5){
                // if(lank5[e].choid === objn.supplier){
                //是不是应该是先判断供应商数据对应的id是否相同，再判断是否将数据进行渲染呢？
                if(!isNaN(Number(e))){
                  if(lank5[e].eveuppacnum === undefined){}else{
                    e = Number(e);
                    lank5[e].id = e;
                    if(e === 0){
                      lank5[e].newer = "最外层包装";
                    }else{
                      const chineseNumbers = ['零','一', '二', '三', '四', '五', '六', '七', '八', '九','十'];
                      lank5[e].newer = "最外层包装的下"+chineseNumbers[e]+"层包装";
                    }
                    if(lank5[e].onlypack !== 0){//是最底层包装
                    }else{//此时时非最底层包装
                      e = Number(e);
                      const t = e - 1;
                      //lank5[e].onlypack = lank5[e].eveuppacnum*lank5[t].onlypack;//
                      lank5[t].onlypack = Number(lank5[t].onlypack);
                      lank5[e].onlypack = lank5[t].onlypack/lank5[e].eveuppacnum;
                      lank5[e].onlypack = this.normalizeDivisionResult(lank5[e].onlypack);
                    }
                    if(lank5[e].newer === '最外层包装'){}else{
                      e = Number(e);
                      const t = e - 1;
                      lank5[t].onlypack = Number(lank5[t].onlypack);
                      lank5[e].onlypack = lank5[t].onlypack/lank5[e].eveuppacnum;
                      lank5[e].onlypack = this.normalizeDivisionResult(lank5[e].onlypack);
                    }
                    lank5[e].onlynetwei = lank5[e].onlypack*0;
                    //辅助包装物重量指的是？辅助包装物的所有单重之和？
                    lank5[e].onlynetwei = Number(lank5[e].onlynetwei);
                    lank5[e].mainpack.onlyweit = Number(lank5[e].mainpack.onlyweit);
                    //lank5[e].onlygrosswei = lank5[e].onlynetwei+lank5[e].mainpack.onlyweit;
                    const lanka1 = lank5[e].auxiliary;//辅助包装物数据
                    let all = lanka1.reduce((sum, item) => sum + item.auonlywei, 0);
                    all = Number(all);
                    lank5[e].onlygrosswei = lank5[e].onlynetwei+lank5[e].mainpack.onlyweit+all;
                  }
                }else{}
                if(e === "outHeight" || e === "outLegth" || e === "outWidth" || e === "outerShape" || e === "unit"){}else{
                  //debugger;
                  if(lank5[e].choid){}else{
                    lank5[e].choid = objn.supplier;
                  }
                }
                // if(lank5[e].choid){}else{
                //   if(e === "outHeight" || e === "outLegth" || e === "outWidth" || e === "outerShape"){}else{
                //     lank5[e].choid = objn.supplier;
                //   }
                // }
                // }else{//而对于id不相同的数据，例如allpack中那条数据的id=1,objn中的id=2,
                //
                // }
              }
              // for(let a in this.allpack){
              //   if(this.allpack[a].choid === objn.supplier){
              //     //是不是应该是先判断供应商数据对应的id是否相同，再判断是否将数据进行渲染呢？
              //
              //   }else{
              //     let json1 = {};
              //     json1.choid = objn.supplier;
              //     this.allpack.push(json1);
              //   }
              // }
              // for(let e in lank5){
              //   if(!isNaN(Number(e))){
              //     if(lank5[e].eveuppacnum === undefined){}else{
              //       e = Number(e);
              //       lank5[e].id = e;
              //       if(e === 0){
              //         lank5[e].newer = "最外层包装";
              //       }else{
              //         const chineseNumbers = ['零','一', '二', '三', '四', '五', '六', '七', '八', '九','十'];
              //         lank5[e].newer = "最外层包装的下"+chineseNumbers[e]+"层包装";
              //       }
              //       lank5[e].onlynetwei = lank5[e].onlypack*0;
              //       //辅助包装物重量指的是？辅助包装物的所有单重之和？
              //       lank5[e].onlynetwei = Number(lank5[e].onlynetwei);
              //       lank5[e].mainpack.onlyweit = Number(lank5[e].mainpack.onlyweit);
              //       //lank5[e].onlygrosswei = lank5[e].onlynetwei+lank5[e].mainpack.onlyweit;
              //       const lanka1 = lank5[e].auxiliary;//辅助包装物数据
              //       let all = lanka1.reduce((sum, item) => sum + item.auonlywei, 0);
              //       all = Number(all);
              //       lank5[e].onlygrosswei = lank5[e].onlynetwei+lank5[e].mainpack.onlyweit+all;
              //     }
              //   }else{}
              // }
              this.looker = 0;
              this.allpack = this.list5 = lank5;
            }
          }else if(this.setdiftruck === 2){
            if(this.allpack2.length === 0){
              let json1 = {};
              json1.choid = objn.supplier;
              this.allpack2.push(json1);
            }else{
              //debugger;
              this.list5 = this.allpack2;
              let lank5 = this.list5;
              for(let e in lank5){
                // if(lank5[e].choid === objn.supplier){
                //是不是应该是先判断供应商数据对应的id是否相同，再判断是否将数据进行渲染呢？
                if(!isNaN(Number(e))){
                  if(lank5[e].eveuppacnum === undefined){}else{
                    e = Number(e);
                    lank5[e].id = e;
                    if(e === 0){
                      lank5[e].newer = "最外层包装";
                    }else{
                      const chineseNumbers = ['零','一', '二', '三', '四', '五', '六', '七', '八', '九','十'];
                      lank5[e].newer = "最外层包装的下"+chineseNumbers[e]+"层包装";
                    }
                    if(lank5[e].onlypack !== 0){//是最底层包装
                    }else{//此时时非最底层包装
                      e = Number(e);
                      const t = e - 1;
                      //lank5[e].onlypack = lank5[e].eveuppacnum*lank5[t].onlypack;//
                      lank5[t].onlypack = Number(lank5[t].onlypack);
                      lank5[e].onlypack = lank5[t].onlypack/lank5[e].eveuppacnum;
                      lank5[e].onlypack = this.normalizeDivisionResult(lank5[e].onlypack);
                    }
                    if(lank5[e].newer === '最外层包装'){}else{
                      e = Number(e);
                      const t = e - 1;
                      lank5[t].onlypack = Number(lank5[t].onlypack);
                      lank5[e].onlypack = lank5[t].onlypack/lank5[e].eveuppacnum;
                      lank5[e].onlypack = this.normalizeDivisionResult(lank5[e].onlypack);
                    }
                    lank5[e].onlynetwei = lank5[e].onlypack*0;
                    //辅助包装物重量指的是？辅助包装物的所有单重之和？
                    lank5[e].onlynetwei = Number(lank5[e].onlynetwei);
                    lank5[e].mainpack.onlyweit = Number(lank5[e].mainpack.onlyweit);
                    //lank5[e].onlygrosswei = lank5[e].onlynetwei+lank5[e].mainpack.onlyweit;
                    const lanka1 = lank5[e].auxiliary;//辅助包装物数据
                    let all = lanka1.reduce((sum, item) => sum + item.auonlywei, 0);
                    all = Number(all);
                    lank5[e].onlygrosswei = lank5[e].onlynetwei+lank5[e].mainpack.onlyweit+all;
                  }
                }else{}
                if(e === "outHeight" || e === "outLegth" || e === "outWidth" || e === "outerShape" || e === "unit"){}else{
                  //debugger;
                  if(lank5[e].choid){}else{
                    lank5[e].choid = objn.supplier;
                  }
                }
                // if(lank5[e].choid){}else{
                //   if(e === "outHeight" || e === "outLegth" || e === "outWidth" || e === "outerShape"){}else{
                //     lank5[e].choid = objn.supplier;
                //   }
                // }
                // }else{//而对于id不相同的数据，例如allpack中那条数据的id=1,objn中的id=2,
                //
                // }
              }
              // for(let a in this.allpack){
              //   if(this.allpack[a].choid === objn.supplier){
              //     //是不是应该是先判断供应商数据对应的id是否相同，再判断是否将数据进行渲染呢？
              //
              //   }else{
              //     let json1 = {};
              //     json1.choid = objn.supplier;
              //     this.allpack.push(json1);
              //   }
              // }
              // for(let e in lank5){
              //   if(!isNaN(Number(e))){
              //     if(lank5[e].eveuppacnum === undefined){}else{
              //       e = Number(e);
              //       lank5[e].id = e;
              //       if(e === 0){
              //         lank5[e].newer = "最外层包装";
              //       }else{
              //         const chineseNumbers = ['零','一', '二', '三', '四', '五', '六', '七', '八', '九','十'];
              //         lank5[e].newer = "最外层包装的下"+chineseNumbers[e]+"层包装";
              //       }
              //       lank5[e].onlynetwei = lank5[e].onlypack*0;
              //       //辅助包装物重量指的是？辅助包装物的所有单重之和？
              //       lank5[e].onlynetwei = Number(lank5[e].onlynetwei);
              //       lank5[e].mainpack.onlyweit = Number(lank5[e].mainpack.onlyweit);
              //       //lank5[e].onlygrosswei = lank5[e].onlynetwei+lank5[e].mainpack.onlyweit;
              //       const lanka1 = lank5[e].auxiliary;//辅助包装物数据
              //       let all = lanka1.reduce((sum, item) => sum + item.auonlywei, 0);
              //       all = Number(all);
              //       lank5[e].onlygrosswei = lank5[e].onlynetwei+lank5[e].mainpack.onlyweit+all;
              //     }
              //   }else{}
              // }
              this.looker = 0;
              this.allpack2 = this.list5 = lank5;
            }
          }
        }
        //console.log('2.13-6',this.allpack);
        //需要将objn中的id存入allpack中，便于在对其他供应商数据设置包装数据时，可以有效的区分开是给哪条供应商数据设置的包装数据，
        //减少因为覆盖的问题导致的错误
        //debugger;
        //objn.supplier是供应商数据的id，需要存入allpack中用作区分所属不同供应商数据的包装数据的
      }
      //this.list5.unit = unit;
      //console.log('2.13-7',this.allpack);
      //let json = {newer: '最外层包装',eveuppacnum: '',onlypack: '',onlynetwei: '',onlygrosswei: ''};
      // this.list5.push(json);
      // if(this.allpack.length === 0){
      // }else{
      //   this.looker = 0;
      //   let allpack = this.allpack;
      //   for(let a in allpack){
      //     this.list5.push(allpack[a]);
      //   }
      // }
      //this.allpack = this.allpack === [] ? this.list5 = [] : this.list5 = this.allpack;
    },
    //点击取消关闭弹窗
    hideFun1(){
      this.matVisible = false;
      if(this.doiffet === 1){
        this.gobck2 = 1;
      }else{
        this.gobck2 = 0;
      }
      //console.log('5.15-8:54',this.packaging);
      if(this.packaging.mainpack){
        this.packaging.mainpack.codeid = '';
      }
      //console.log('5.15-8:58',this.packaging);
      //console.log('5.15-9:52',this.list3.unit);
      // this.packaging.mainpack = {
      //   codeid: '', name: '', specifications: '', model: '', weimingt: '', onlyweit: '',
      // };
    },
    hideFun22(){
      this.matVisible22 = false;
    },
    hideFun33(){
      this.matVisible33 = false;//好奇怪这一行到底有什么问题呀？
      //console.log(this.packagbox);
      // if(this.packaging.mainpack !== {}){
      //   this.packagbox.numb = this.packaging.mainpack.codeid;
      //   this.packagbox.name = this.packaging.mainpack.name;
      //   this.packagbox.model = this.packaging.mainpack.model;
      //   this.packagbox.sizer = this.packaging.mainpack.specifications;
      //   this.packagbox.widthUnit = this.packaging.mainpack.weimingt;
      // }else{}
    },
    hideFun4(){
      this.matVisible4 = false;
    },
    //点击确定进行跳转
    tipOk1(tpo){//tpo:1-未设置，2已设置
      // let cengItem = {
      //   main: JSON.parse(JSON.stringify(this.packaging.mainpack)),
      //   fu:  JSON.parse(JSON.stringify(this.packaging.auxiliary)),
      //   onlypack: this.packaging. onlypack,
      //   onlynetwei: this.packaging. onlynetwei,
      //   onlygrosswei: this.packaging. onlygrosswei,
      // }
      // // 判断 是不是第一次
      // let trInfo={
      //   newer: '',
      // }
      // this.allpack.push(cengItem)
      //
      // if( this.packaging.typeCeng  === 2){ // 编辑第一层
      //
      // }else if(this.packaging.typeCeng  === 1){ // 新增下一层
      //   this.list5.push(cengItem)
      //
      // }
      //编辑最外层的时候allpack中直接就有值了，所以不走第一次设置
      this.matVisible = false;
      //debugger;
      // this.packaging.eveuppacnum = this.sizeForm.name4 === '' ? 0 : this.packaging.eveuppacnum = this.sizeForm.name4;
      // this.packaging.onlypack = this.sizeForm.name1 === '' ? 0 : this.packaging.onlypack = this.sizeForm.name1;
      // this.packaging.onlynetwei = this.sizeForm.name2 === '' ? 0 : this.packaging.onlynetwei = this.sizeForm.name2;
      // this.packaging.onlygrosswei = this.sizeForm.name3 === '' ? 0 : this.packaging.onlygrosswei = this.sizeForm.name3;
      const n = 1;
      this.looker = 0;
      //console.log(tpo);
      //let onethe = false;//区分新数据和旧数据
      //或许下面这部分该区分出是从‘未设置‘过来的 ，还是从’已设置’过来的。
      console.log('7.21-9:26',this.setdiftruck);
      if(this.setdiftruck === 1){//未设置的数据
        console.log('未设置区域-1',this.allpack);
        console.log('未设置区域-2',this.list5);
        console.log('7.18-15:27',this.nowid);
        let newket = '';
        // this.list5.forEach((item,index) => {
        //   if(item.id === this.nowid){
        //     this.allpack.push(item);
        //   }
        // })
        // if(this.allpack.length === 0){}else{
        //   this.allpack.forEach((itema,indexa) => {
        //     this.list5.forEach((item,index) => {
        //       if(itema.id === item.id){}else{
        //         if(item.id === this.nowid){
        //           this.allpack.push(item);
        //         }
        //       }
        //     })
        //   })
        // }
        if(this.allpack.length === 0){//第一次设置包装物信息
          this.packaging.pid = n;
          this.dettetap = 0;
          this.packaging.eveuppacnum = this.sizeForm.name4 === '' ? 0 : this.packaging.eveuppacnum = this.sizeForm.name4;
          this.packaging.onlypack = this.sizeForm.name1 === '' ? 0 : this.packaging.onlypack = this.sizeForm.name1;
          this.packaging.onlynetwei = this.sizeForm.name2 === '' ? 0 : this.packaging.onlynetwei = this.sizeForm.name2;
          this.packaging.onlygrosswei = this.sizeForm.name3 === '' ? 0 : this.packaging.onlygrosswei = this.sizeForm.name3;
          //诶还真需要直接存入，那应该就是第二次设置或修改的位置有问题了
          this.packaging.mainpack = JSON.parse(JSON.stringify(this.packaging.mainpack));
          this.packaging.auxiliary = JSON.parse(JSON.stringify(this.packaging.auxiliary));
          this.allpack.push(JSON.parse(JSON.stringify(this.packaging)));//保证存入数组后新的数据不会覆盖旧的数据
          newket = 1;
          //this.allpack2 = this.allpack;
          //onethe = true;
        }else{//第二次设置新的包装物信息，或者修改第一次设置的包装物信息
          //可以自定义一个id进行比较
          //现在的问题是，对已有某一条数据进行修改，点击确定按钮后列表中的数据并没有变成最新的数据，极有可能是在修改后点击确定按钮时未将原有
          //的数据变成修改后的数据。
          //let modified = false;
          //现在是第二条数据的辅助包装物添加时这一条数据的主要包装物数据有几个属性值被覆盖了
          // 检查是否存在相同的 id
          //debugger;
          let idExists = false;
          //if(this.allpack.length > 1){return false;}
          console.log('未设置区域-3',this.allpack);
          //👆截止到这里allpack的数据都还对-13：34
          for(let a in this.allpack){
            //判断a是否为数字
            if(!isNaN(Number(a))){
              const aid = this.allpack[a].id;
              // //是不是可以在新增数据的时候将该数据的id存下来，然后在编辑的时候获取呢
              // //if(this.packaging.id){this.nowid = this.packaging.id;}
              // // let uid = "";
              // // if(this.nowid === undefined){uid = this.packaging.id;}else{uid = this.nowid;}
              // //aid=this.nowid时到底是修改还是新增数据呢？
              console.log('未设置区域-4',this.nowid);//nowid
              if(aid === this.nowid){
                idExists = true;
                this.allpack[a].isExists = true;
                break;
              }else{
                //debugger;
                this.allpack[a].isExists = false;
              }
            }else{}
            // if(aid === this.nowid){//修改--主要包装物数据和辅助包装物数据未用新数据覆盖旧数据
            //   //修改这里需要注意：只修改之前设置的数据，而不是在修改的同时新增加一条新数据
            //   this.allpack[a].auxiliary = this.packaging.auxiliary;
            //   this.allpack[a].mainpack = this.packaging.mainpack;
            //   if(this.allpack[a].mainpack !== {}){
            //     this.packagbox.numb = this.allpack[a].mainpack.codeid;
            //     this.packagbox.name = this.allpack[a].mainpack.name;
            //     this.packagbox.model = this.allpack[a].mainpack.model;
            //     this.packagbox.sizer = this.allpack[a].mainpack.specifications;
            //     this.packagbox.widthUnit = this.allpack[a].mainpack.weimingt;
            //   }
            //   this.allpack[a].eveuppacnum = this.sizeForm.name4 === '' ? 0 : this.sizeForm.name4;
            //   this.allpack[a].onlypack = this.sizeForm.name1 === '' ? 0 : this.sizeForm.name1;
            //   this.allpack[a].onlynetwei = this.sizeForm.name2 === '' ? 0 : this.sizeForm.name2;
            //   this.allpack[a].onlygrosswei = this.sizeForm.name3 === '' ? 0 : this.sizeForm.name3;
            //   modified = true;
            //   //那就是在修改完之后它又继续循环了，然后就出现了先修改之后又新增一条的问题
            //   //感觉应该是id=0修改后又继续循环了，然后id=1就认为是需要增加的，
            // }else{//新增新的数据
            //   //this.packaging.pid = aid + 1;
            //   //aid会依次递增，
            //   // this.packaging.id = Number(a);
            //   // this.packaging.pid = Number(a + 1);//this.packaging.id==undefined
            //   //当是新增加的数据时，this.nowid找不到
            //   this.packaging.eveuppacnum = this.sizeForm.name4 === '' ? 0 : this.packaging.eveuppacnum = this.sizeForm.name4;
            //   this.packaging.onlypack = this.sizeForm.name1 === '' ? 0 : this.packaging.onlypack = this.sizeForm.name1;
            //   this.packaging.onlynetwei = this.sizeForm.name2 === '' ? 0 : this.packaging.onlynetwei = this.sizeForm.name2;
            //   this.packaging.onlygrosswei = this.sizeForm.name3 === '' ? 0 : this.packaging.onlygrosswei = this.sizeForm.name3;
            //   this.allpack.push(this.packaging);//啊不是，感情只用最简单的push就解决了加入新数据时旧数据被覆盖的问题？？？？？？？？？
            //   //this.allpack.push(JSON.parse(JSON.stringify(this.packaging)));//这里出现了旧数据个别属性的值被新数据覆盖的问题
            //   //检查下现在的主要包装物和辅助包装物数据设置这里是否能正确展示
            //   //不过新数据没有id，该如何判断是哪条数据呢？
            //   //因为修改之后它又继续进行循环了，所以当作是增加执行了，所以需要判断
            //   // if(aid === this.allpack[a].pid){}else{
            //   //   if(modified === true){//代表前一秒已经执行了修改效果
            //   //     return false;
            //   //   }else{
            //   //     this.allpack[a].pid = aid;
            //   //     //现在是旧数据的onethe=true
            //   //     if(onethe === true){}else{
            //   //
            //   //     }
            //   //     this.packaging.eveuppacnum = this.sizeForm.name4 === '' ? 0 : this.packaging.eveuppacnum = this.sizeForm.name4;
            //   //     this.packaging.onlypack = this.sizeForm.name1 === '' ? 0 : this.packaging.onlypack = this.sizeForm.name1;
            //   //     this.packaging.onlynetwei = this.sizeForm.name2 === '' ? 0 : this.packaging.onlynetwei = this.sizeForm.name2;
            //   //     this.packaging.onlygrosswei = this.sizeForm.name3 === '' ? 0 : this.packaging.onlygrosswei = this.sizeForm.name3;
            //   //     let keyedn = JSON.parse(JSON.stringify(this.packaging));
            //   //     this.allpack.push(keyedn);
            //   //   }
            //   //   //this.allpack.push(JSON.parse(JSON.stringify(this.packaging)));//保证存入数组后新的数据不会覆盖旧的数据
            //   //   // }
            //   //   // const tryen = this.tryen = [1,2];
            //   //   // const newItem = 'someValue';
            //   //   // if (!this.tryen.includes(newItem)) {
            //   //   //   this.tryen.push(newItem);
            //   //   // }
            //   //   //if (!this.myArray.includes(newItem)) {
            //   //   //   this.myArray.push(newItem);
            //   //   // }
            //   //   // for(let a in this.allpack){
            //   //   //   if(this.allpack[a].id === this.packaging.id){}else{
            //   //   //   }
            //   //   // }
            //   // }
            //   //this.packaging.pid = aid;
            //   //this.packaging.id = this.allpack.length;
            //   //this.packaging.pid = aid + 1;
            //   //重复添加的问题该怎么解决呢？
            //   //是不是应该在push入allpack前就去重呢？可是pid该怎么用上啊?
            //   // if(aid === this.packaging.pid){
            //   //   a = Number(a);
            //   //   this.allpack.splice(a,1);
            //   // }else{
            //   //   this.packaging.pid = aid + 1;
            //   // }
            // }//如何解决重复循环的问题呢？2,3,4连续走了两遍
            // // if(!this.allpack.includes(keyedn)){
            // //   this.allpack.push(keyedn);
            // // }
            // // if(!this.allpack.includes(JSON.parse(JSON.stringify(this.packaging)))){
            // //let newItemCopy = JSON.parse(JSON.stringify(newItem));
            // //this.items.push(newItemCopy);
          }
          //if(this.allpack.length > 1){return false;}
          // if(aid === this.nowid){//修改--主要包装物数据和辅助包装物数据未用新数据覆盖旧数据
          //修改这里需要注意：只修改之前设置的数据，而不是在修改的同时新增加一条新数据
          // this.allpack[a].auxiliary = this.packaging.auxiliary;
          // this.allpack[a].mainpack = this.packaging.mainpack;
          // if(this.allpack[a].mainpack !== {}){
          //   this.packagbox.numb = this.allpack[a].mainpack.codeid;
          //   this.packagbox.name = this.allpack[a].mainpack.name;
          //   this.packagbox.model = this.allpack[a].mainpack.model;
          //   this.packagbox.sizer = this.allpack[a].mainpack.specifications;
          //   this.packagbox.widthUnit = this.allpack[a].mainpack.weimingt;
          // }
          if(idExists === true){
            for(let d in this.allpack){
              //debugger;
              if(d === "outHeight" || d === "outLegth" || d === "outWidth" || d === "outerShape" || d === "unit"){}else{
                if(this.allpack[d].isExists === true) {//修改
                  this.allpack[d].auxiliary = this.packaging.auxiliary;
                  this.allpack[d].mainpack = this.packaging.mainpack;//哭了到底为什么啊！！！！同一个方法初始数据就是对的，第二条数据就是错的，错的位置还是最开始
                  //的那个问题
                  if(this.allpack[d].mainpack !== {}){
                    //if(this.allpack[d].mainpack.codeid){this.allpack[d].mainpack.code = this.allpack[d].mainpack.codeid;}
                    if(this.respectively === 2){this.packagbox.numb = this.allpack[d].mainpack.code;}
                    else if(this.respectively === 1){this.packagbox.numb = this.allpack[d].mainpack.codeid;}
                    this.packagbox.name = this.allpack[d].mainpack.name;
                    this.packagbox.model = this.allpack[d].mainpack.model;
                    this.packagbox.sizer = this.allpack[d].mainpack.specifications;
                    this.packagbox.widthUnit = this.allpack[d].mainpack.weimingt;
                  }
                  this.allpack[d].eveuppacnum = this.sizeForm.name4 === '' ? 0 : this.sizeForm.name4;
                  console.log('7.22-13:37',this.sizeForm.name1);
                  this.allpack[d].onlypack = this.sizeForm.name1 === '' ? 0 : this.sizeForm.name1;//这里原来没有问题
                  console.log('7.22-13:37',this.allpack[d].onlypack);//这里的数据也是正确的
                  //this.allpack[d].onlygrosswei = this.allpack[d].mainpack.weight;
                  this.allpack[d].onlynetwei = this.sizeForm.name2 === '' ? 0 : this.sizeForm.name2;
                  this.allpack[d].onlygrosswei = this.sizeForm.name3 === '' ? 0 : this.sizeForm.name3;
                }
              }
            }
            // this.allpack.forEach((itema,indexa) => {
            //   if(itema.isExists === true){//修改
            //     //if(this.allpack.length > 1){return false;}
            //     itema.auxiliary = this.packaging.auxiliary;
            //     // this.packagingbox.forEach((itemb,indexb) => {
            //     //   let mainpack = itemb.mainpack;
            //     //   if(itema.id === itemb.id){
            //     //     itema.mainpack = mainpack;
            //     //   }
            //     // })
            //     itema.mainpack = this.packaging.mainpack;
            //     if(itema.mainpack !=={}){
            //       this.packagbox.numb = itema.mainpack.codeid;
            //       this.packagbox.name = itema.mainpack.name;
            //       this.packagbox.model = itema.mainpack.model;
            //       this.packagbox.sizer = itema.mainpack.specifications;
            //       this.packagbox.widthUnit = itema.mainpack.weimingt;
            //     }
            //     itema.eveuppacnum = this.sizeForm.name4 === '' ? 0 : this.sizeForm.name4;
            //     itema.onlypack = this.sizeForm.name1 === '' ? 0 : this.sizeForm.name1;
            //     itema.onlynetwei = this.sizeForm.name2 === '' ? 0 : this.sizeForm.name2;
            //     itema.onlygrosswei = this.sizeForm.name3 === '' ? 0 : this.sizeForm.name3;
            //   }
            // })
          }else{//增加
            this.packaging.eveuppacnum = this.sizeForm.name4 === '' ? 0 : this.packaging.eveuppacnum = this.sizeForm.name4;
            this.packaging.onlypack = this.sizeForm.name1 === '' ? 0 : this.packaging.onlypack = this.sizeForm.name1;
            this.packaging.onlynetwei = this.sizeForm.name2 === '' ? 0 : this.packaging.onlynetwei = this.sizeForm.name2;
            this.packaging.onlygrosswei = this.sizeForm.name3 === '' ? 0 : this.packaging.onlygrosswei = this.sizeForm.name3;
            this.allpack.push(this.packaging);
          }
          newket = 2;
          //对this.allpack进行去重处理
          //去重的问题还得再想想
          //重复的两条数据目前的情况是：id不同，层级不同，但其他数据都相同；若只比对其他数据，就显得不那么准确，毕竟谁知道正式修改的时候
          //会不会正好赶上判断的那个属性是正好变化的啊。
          //是不是因为pid的值的缘故？要么不去重要么就全删了。
          // for(let i = 0;i<this.allpack.length;i++){//存入时变成了四条数据
          //   for(let j = i+1;j<this.allpack.length;j++){
          //     if(this.allpack[i].pid === this.allpack[j].pid){
          //       this.allpack.splice(j,1);
          //       j--;
          //     }
          //   }
          // }
        }
        this.allpack.forEach(item => {
          if(this.multipleSelection.length === 0){//此时不是从批量编辑过来的
            let choid = item.choid;
            if(choid){}else{
              item.choid = this.supplier;
            }
          }else{//此时是从批量编辑过来的
            item.choid = "";
          }
        })
        if(this.list5.length === 0){
          this.list5 = this.allpack;
        }else{
          this.list5.forEach((item,index) => {
            this.allpack.forEach((item2,index2) => {
              if(item.id === item2.id){
                if(item.id === undefined && item2.id === undefined){
                  item.onlypack = item2.onlypack;
                  item.onlynetwei = item2.onlynetwei;
                  item.onlygrosswei = item2.onlygrosswei;
                  item.choid = item2.choid;
                }else{
                  item = item2;
                }
              }
            })
          })
          console.log('数据这里有么',this.list5);
        }
        let lank5 = this.list5;
        //console.log('为何上面都有下面就没有呢，赋值失败',lank5);
        console.log('未设置区域-5',this.list5);//这里没有choid
        console.log('未设置区域-6',this.allpack);//这里还有choid的值
        //allpack中没有和list5对上的id，

        for(let e in lank5){
          if(!isNaN(e)){
            e = Number(e);
            lank5[e].id = e;
            if(e === 0){
              lank5[e].newer = "最外层包装";
            }else{
              const chineseNumbers = ['零','一', '二', '三', '四', '五', '六', '七', '八', '九','十'];
              //// 检查数字是否在映射表范围内
              // if (num >= 0 && num < chineseNumbers.length) {
              //   return chineseNumbers[num];
              // } else {
              //   // 如果数字超出范围，可以返回一个错误消息或进行其他处理
              //   return '数字超出范围';
              // }
              lank5[e].newer = "最外层包装的下"+chineseNumbers[e]+"层包装";
              // e = Number(e);
              // lank5.length = Number(lank5.length);
              // if(lank5.length - 1 === e){this.detteap = 1;}
              // const indextd = 0;
              // if(lank5.length > indextd){
              //
              // }
              // //索引值=length-1
              // const lasid = lank5[e].id;
            }
            //debugger;
            if(lank5[e].onlypack !== 0){//是最底层包装
            }else{//此时时非最底层包装
              e = Number(e);
              const t = e - 1;
              //lank5[e].onlypack = lank5[e].eveuppacnum*lank5[t].onlypack;//
              lank5[t].onlypack = Number(lank5[t].onlypack);
              lank5[e].onlypack = lank5[t].onlypack/lank5[e].eveuppacnum;
              lank5[e].onlypack = this.normalizeDivisionResult(lank5[e].onlypack);
            }
            if(lank5[e].newer === '最外层包装'){}else{
              e = Number(e);
              const t = e - 1;
              lank5[t].onlypack = Number(lank5[t].onlypack);
              lank5[e].onlypack = lank5[t].onlypack/lank5[e].eveuppacnum;
              lank5[e].onlypack = this.normalizeDivisionResult(lank5[e].onlypack);
            }
            lank5[e].onlynetwei = lank5[e].onlypack*0;
            //辅助包装物重量指的是？辅助包装物的所有单重之和？
            if(lank5[e].onlynetwei === undefined){lank5[e].onlynetwei = this.sizeForm.name2;}
            if(lank5[e].onlygrosswei === undefined){lank5[e].onlygrosswei = this.sizeForm.name3;}
            lank5[e].onlynetwei = Number(lank5[e].onlynetwei);
            //if(lank5[e].mainpack.onlyweit === undefined){lank5[e].mainpack.onlyweit = 0;}
            // lank5[e].mainpack.onlyweit = lank5[e].mainpack.onlyweit === undefined ? 0 : lank5[e].mainpack.onlyweit;
            lank5[e].onlyweit = Number(lank5[e].onlyweit);//单重
            //lank5[e].onlygrosswei = lank5[e].onlynetwei+lank5[e].mainpack.onlyweit;
            const lanka1 = lank5[e].auxiliary;//辅助包装物数据
            let all = lanka1.reduce((sum, item) => sum + item.auonlywei, 0);
            all = Number(all);
            //lank5[e].onlygrosswei = lank5[e].onlynetwei+lank5[e].mainpack.onlyweit+all;
            //this.jsonArray.reduce((sum, item) => sum + item.id, 0);
          }else{}
        }
        //这里需要整合下
        //allpack需要newer,lank5需要choid
        //this.allpack = this.list5 = lank5;
        this.list5 = lank5;
        this.list9 = this.list5;
        let list5 = this.list5;
        for(let s in list5){//还是没看懂为何List5的数据会出现第二条覆盖第一条的情况
          if(s === 'outLegth' || s === 'outWidth' || s === 'outHeight' || s === 'outerShape' || s === 'unit'){}else{
            if(list5[s].mainpack.codeid){
              if(list5[s].mainpack.code === undefined){list5[s].mainpack.code = list5[s].mainpack.codeid;}
            }
            if(list5[s].mainpack.code){
              if(list5[s].mainpack.codeid === undefined){list5[s].mainpack.codeid = list5[s].mainpack.code;}
            }
            // if(list5[s].mainpack === undefined){
            //   list5[s].mainpack = list5[s].zyPackaging;
            // }
            // if(list5[s].mainpack === undefined){
            //   list5[s].mainpack = list5[s].zyPackaging;
            //   if(list5[s].auxiliary === undefined || list5[s].auxiliary.length === 0){list5[s].auxiliary = list5[s].itemList;}else{}
            //   //list5[s].auxiliary = list5[s].itemList;
            //   //this.looker = 2;
            //   if(list5[s].zyPackaging.codeid === undefined){
            //     if(list5[s].zyPackaging.code === undefined){}else{list5[s].zyPackaging.codeid = list5[s].zyPackaging.code;}
            //   }else{
            //     if(list5[s].zyPackaging.code === undefined){list5[s].zyPackaging.code = list5[s].zyPackaging.codeid}else{}
            //   }
            // }else{
            //   //this.looker = 0;
            //   list5[s].zyPackaging = list5[s].mainpack;
            //   if(list5[s].itemList === undefined || list5[s].itemList.length === 0){list5[s].auxiliary = list5[s].zyPackaging;}
            //   //list5[s].itemList = list5[s].auxiliary;
            //   if(list5[s].mainpack.codeid === undefined){
            //     if(list5[s].mainpack.code === undefined){}else{list5[s].mainpack.codeid = list5[s].mainpack.code;}
            //   }else{
            //     if(list5[s].mainpack.code === undefined){list5[s].mainpack.code = list5[s].mainpack.codeid}else{}
            //   }
            // }
            // if(list5[s].mainpack.codeid){
            //   if(list5[s].mainpack.code === undefined){list5[s].mainpack.code = list5[s].mainpack.codeid;}
            // }else{
            //
            // }
            // if(list5[s].mainpack.code){
            //   if(list5[s].mainpack.codeid === undefined){list5[s].mainpack.codeid = list5[s].mainpack.code;}
            // }
          }
          // if(list5[s].mainpack.codeid){list5[s].mainpack.code = list5[s].mainpack.codeid;}
        }

      }else if(this.setdiftruck === 2){//已设置的数据
        console.log('7.18-11:25',this.list5);//这个数据是对的  这里也变得好诡异，明明最底层数据执行的时候就是对的，可执行到第二层的时候明明什么都没做
        //数据确是错的
        console.log('已设置区域-0',this.nowid);//这里是编辑按钮所在的那行数据的id
        console.log('已设置区域-1',this.allpack2);//13:24-现在这个Json是个空的
        //👆截止到这里一切正确,14:02-此时allpack的数据就不太对了，变成了辅助包装物的数据
        //故此时需要根据list5将allpack的数据调整正确
        this.allpack2 = [];
        //this.allpack = [];
        this.list5.forEach((item5,index5) => {
          if(item5.id === this.nowid){
            this.allpack2.push(item5);
          }
        })
        console.log('已设置区域-2',this.allpack2);
        let newket = '';//1-新录入的数据，2-修改已有数据
        if(this.allpack2.length === 0){
          this.packaging.pid = n;
          this.dettetap = 0;
          this.packaging.eveuppacnum = this.sizeForm.name4 === '' ? 0 : this.packaging.eveuppacnum = this.sizeForm.name4;
          this.packaging.onlypack = this.sizeForm.name1 === '' ? 0 : this.packaging.onlypack = this.sizeForm.name1;
          this.packaging.onlynetwei = this.sizeForm.name2 === '' ? 0 : this.packaging.onlynetwei = this.sizeForm.name2;
          this.packaging.onlygrosswei = this.sizeForm.name3 === '' ? 0 : this.packaging.onlygrosswei = this.sizeForm.name3;
          this.packaging.mainpack = JSON.parse(JSON.stringify(this.packaging.mainpack));
          this.packaging.auxiliary = JSON.parse(JSON.stringify(this.packaging.auxiliary));
          this.allpack2.push(JSON.parse(JSON.stringify(this.packaging)));
          newket = 1;
        }else{
          let idExists = false;
          console.log('已设置区域-3',this.allpack2);
          for(let a in this.allpack2){
            if(!isNaN(Number(a))){
              const aid = this.allpack2[a].id;
              console.log('已设置区域-4',this.nowid);//nowid
              if(aid === this.nowid){
                idExists = true;
                this.allpack2[a].isExists = true;
                break;
              }else{
                this.allpack2[a].isExists = false;
              }
            }else{}
          }
          if(idExists === true){
            for(let d in this.allpack2){
              if(d === "outHeight" || d === "outLegth" || d === "outWidth" || d === "outerShape" || d === "unit"){}else{
                if(this.allpack2[d].isExists === true) {//修改JSON.parse(JSON.stringify(this.items[index]));
                  this.allpack2[d].auxiliary = this.packaging.auxiliary;
                  this.allpack2[d].mainpack = this.packaging.mainpack;
                  if(this.allpack2[d].mainpack !== {}){
                    if(this.respectively === 2){this.packagbox.numb = this.allpack2[d].mainpack.code;}
                    else if(this.respectively === 1){this.packagbox.numb = this.allpack2[d].mainpack.codeid;}
                    this.packagbox.name = this.allpack2[d].mainpack.name;
                    this.packagbox.model = this.allpack2[d].mainpack.model;
                    this.packagbox.sizer = this.allpack2[d].mainpack.specifications;
                    this.packagbox.widthUnit = this.allpack2[d].mainpack.weimingt;
                  }
                  this.allpack2[d].eveuppacnum = this.sizeForm.name4 === '' ? 0 : this.sizeForm.name4;
                  this.allpack2[d].onlypack = this.sizeForm.name1 === '' ? 0 : this.sizeForm.name1;//这里原来没有问题
                  this.allpack2[d].onlynetwei = this.sizeForm.name2 === '' ? 0 : this.sizeForm.name2;
                  this.allpack2[d].onlygrosswei = this.sizeForm.name3 === '' ? 0 : this.sizeForm.name3;
                }
              }
            }
          }else{
            this.packaging.eveuppacnum = this.sizeForm.name4 === '' ? 0 : this.packaging.eveuppacnum = this.sizeForm.name4;
            this.packaging.onlypack = this.sizeForm.name1 === '' ? 0 : this.packaging.onlypack = this.sizeForm.name1;
            this.packaging.onlynetwei = this.sizeForm.name2 === '' ? 0 : this.packaging.onlynetwei = this.sizeForm.name2;
            this.packaging.onlygrosswei = this.sizeForm.name3 === '' ? 0 : this.packaging.onlygrosswei = this.sizeForm.name3;
            this.allpack2.push(JSON.parse(JSON.stringify(this.packaging)));
          }
          newket = 2;
        }
        if(this.list5.length === 0){
          this.list5 = this.allpack2;
        }else{
          this.list5.forEach((item,index) => {
            this.allpack2.forEach((item2,index2) => {
              if(item.id === item2.id){
                if(item.id === undefined && item2.id === undefined){
                  item.onlypack = item2.onlypack;
                  item.onlynetwei = item2.onlynetwei;
                  item.onlygrosswei = item2.onlygrosswei;
                  item.choid = item2.choid;
                }else{
                  item = item2;
                }
              }
            })
          })
          console.log('数据这里有么',this.list5);//这里也没有值
        }
        let lank5 = this.list5;
        //console.log('已设置区域-5',this.list5);//这里的数据中zyPackaging数组中的数据反而是正确的
        //mainpack反而是错误的诶
        console.log('已设置区域-6',this.allpack2);
        //在新加新的数据时需要将allpack2中的新数据存入list5中
        //two = [...two, ...one];
        //lank5 = [...lank5,...this.allpack2];
        if(newket === 1){
          this.allpack2.forEach(item => {
            lank5.push(item);
          })
        }else if(newket === 2){}
        for(let e in lank5){
          if(!isNaN(e)){
            e = Number(e);
            lank5[e].id = e;
            if(e === 0){
              lank5[e].newer = "最外层包装";
            }else{
              const chineseNumbers = ['零','一', '二', '三', '四', '五', '六', '七', '八', '九','十'];
              lank5[e].newer = "最外层包装的下"+chineseNumbers[e]+"层包装";
            }
            if(lank5[e].onlypack !== 0){//是最底层包装
            }else{//此时时非最底层包装
              e = Number(e);
              const t = e - 1;
              lank5[t].onlypack = Number(lank5[t].onlypack);
              lank5[e].onlypack = lank5[t].onlypack/lank5[e].eveuppacnum;
              lank5[e].onlypack = this.normalizeDivisionResult(lank5[e].onlypack);
            }
            if(lank5[e].newer === '最外层包装'){}else{
              e = Number(e);
              const t = e - 1;
              lank5[t].onlypack = Number(lank5[t].onlypack);
              lank5[e].onlypack = lank5[t].onlypack/lank5[e].eveuppacnum;
              lank5[e].onlypack = this.normalizeDivisionResult(lank5[e].onlypack);
            }
            //lank5[e].onlypack = Number(lank5[e].onlypack);
            lank5[e].onlynetwei = lank5[e].onlypack*0;
            //辅助包装物重量指的是？辅助包装物的所有单重之和？
            if(lank5[e].onlynetwei === undefined){lank5[e].onlynetwei = this.sizeForm.name2;}
            if(lank5[e].onlygrosswei === undefined){lank5[e].onlygrosswei = this.sizeForm.name3;}
            lank5[e].onlynetwei = Number(lank5[e].onlynetwei);
            lank5[e].onlyweit = Number(lank5[e].onlyweit);//单重
            //const lanka1 = lank5[e].auxiliary;//辅助包装物数据
            // let all = lanka1.reduce((sum, item) => sum + item.auonlywei, 0);
            // all = Number(all);
            //lank5[e].onlygrosswei = lank5[e].onlynetwei+lank5[e].mainpack.onlyweit+all;
            //this.jsonArray.reduce((sum, item) => sum + item.id, 0);
          }else{}
        }
        this.list5 = lank5;
        this.list9 = this.list5;
        let list5 = this.list5;
        for(let s in list5){
          if(s === 'outLegth' || s === 'outWidth' || s === 'outHeight' || s === 'outerShape' || s === 'unit'){}else{
            if(list5[s].zyPackaging === undefined){
              if(list5[s].mainpack.codeid){
                if(list5[s].mainpack.code === undefined){list5[s].mainpack.code = list5[s].mainpack.codeid;}
              }
              if(list5[s].mainpack.code){
                if(list5[s].mainpack.codeid === undefined){list5[s].mainpack.codeid = list5[s].mainpack.code;}
              }
            }else{
              if(list5[s].zyPackaging.codeid){
                if(list5[s].zyPackaging.code === undefined){list5[s].zyPackaging.code = list5[s].zyPackaging.codeid;}
              }
              if(list5[s].zyPackaging.code){
                if(list5[s].zyPackaging.codeid === undefined){list5[s].zyPackaging.codeid = list5[s].zyPackaging.code;}
              }
            }
            // if(list5[s].mainpack === undefined){
            //   if(list5[s].zyPackaging.codeid){
            //     if(list5[s].zyPackaging.code === undefined){list5[s].zyPackaging.code = list5[s].zyPackaging.codeid;}
            //   }
            //   if(list5[s].zyPackaging.code){
            //     if(list5[s].zyPackaging.codeid === undefined){list5[s].zyPackaging.codeid = list5[s].zyPackaging.code;}
            //   }
            // }else{
            //   if(list5[s].mainpack.codeid){
            //     if(list5[s].mainpack.code === undefined){list5[s].mainpack.code = list5[s].mainpack.codeid;}
            //   }
            //   if(list5[s].mainpack.code){
            //     if(list5[s].mainpack.codeid === undefined){list5[s].mainpack.codeid = list5[s].mainpack.code;}
            //   }
            // }
          }
        }
        // if(this.nextpic === 1){//录入下一层
        //   this.allpack = [];
        // }else{
        //   if(this.allpack.length === 0){
        //     if(this.list5.length === 0){}else{
        //       this.list5.forEach((item5,index5) => {
        //         if(item5.id === this.nowid){
        //           this.allpack.push(item5);
        //         }
        //       })
        //     }
        //   }else{
        //     // this.allpack.forEach((itema,indexa) => {//allpack=[1,2],list5 =[1.2],nowid = 1;
        //     //   if(itema.id === this.nowid){}else{
        //     //     this.list5.forEach((item,index) => {
        //     //       if(itema.id === item.id){}else{
        //     //         if(item.id === this.nowid){
        //     //           this.allpack.push(item);
        //     //         }
        //     //       }
        //     //     })
        //     //   }
        //     // })
        //   }
        // }
        // console.log('7.18-11:21',this.allpack);
      }
      // this.allpack.forEach((itema,indexa) => {
      //   let pid = itema.pid;
      //   if(pid === ''){this.allpack.splice(itema,1);}
      // })
      //console.log(this.multipleSelection);//代表前一步是在批量编辑表格中勾选了数据

      //console.log(this.allpack);
      //console.log('7.22-15:09',this.list5);//到这里看着也没什么问题

      //console.log('7.22-13:59',this.allpack);
      //console.log('7.22-14:00',this.list5);//明明在这里的时候辅助包装物数据是没问题的
      //为何会同时增了两条呢
    },
    // addClass({row,column,rowIndex,columnIndex}){
    //   if(rowIndex >= this.list5.length-1){
    //     this.dettetap = 1;
    //     return 'addBorder';
    //   }
    // },v-if="scope.$index === tableData.length - 1" type="primary"
    tipOk22(tem){//2-停用
      this.matVisible22 = false;
      //Integer id       //包装id
      //Integer enabled //1启用 0停用
      if(tem === 1){
        //debugger;
        let lank6 = this.list6;
        for(let f in lank6){
          f = Number(f);
          let sixid = lank6[f].aucoid;
          let detid = this.detk.aucoid;
          //sixid = Number(sixid);
          //detid = Number(detid);
          if(sixid === detid){
            lank6.splice(f,1);
          }
        }
        this.list6 = lank6;
      }else if(tem === 2){
        const jsonst = {id: this.desid,enabled: 0};
        stuppact(jsonst).then(res => {
          //console.log('4.2-13:43',res);
          //debugger;
          //这里需要重新获取一遍‘supalltak’页面中的表格中的查看按钮点击时调用的接口获取的数据
          //console.log(res);
          const jsonh = {mtBaseId: this.catid,supplierMaterial: this.suppMaterial};
          seapackinfon(jsonh).then(res => {
            //debugger;
            let nowlink = this.nowlank = res.data.data.list || [];//正在使用的包装
            // if(nowlink.length === 0){//此时既可能是真的没有数据，也可能是暂时未获取到数据
            //   //下面是自己写的假数据，用于先将页面功能展示出来，后续再根据真数据优化页面效果
            //   nowlink = [
            //     {
            //       createName: '张三',createDate: 1693795219074,outerShape: 1,id: 139,material: '189',
            //       packagingCount: 2,materialCount: 3,netWeight: 1,netUnitId: '739',netUnit: '千克',grossWeight: 1,
            //       grossUnitId: '132',grossUnit: '克',totalWeight: 100,totalUnit: '吨',outerLength: 2,lengthUnitId: 12,
            //       lengthUnit: 'cm',outerWidth: 3,widthUnitId: 20,widthUnit: 'm',outerHeight: 4,heightUnitId: 23,
            //       heightUnit: 'm',enabled: 1,enabledTime: '',
            //       structureList: [
            //         {
            //           level: 1,materialCount: 3,lengthUnitId: 12,lengthUnit: 'cm',outerLength: 2,widthUnitId: 20,widthUnit: 'm',
            //           outerWidth: 3,heightUnitId: 23,heightUnit: 'm',outerHeight: 4,outerShape: '1'
            //         },
            //         {
            //           level: 2,materialCount: 31,lengthUnitId: 12,lengthUnit: 'cm',outerLength: 21,widthUnitId: 20,widthUnit: 'm',
            //           outerWidth: 31,heightUnitId: 21,heightUnit: 'm',outerHeight: 3,outerShape: '2'
            //         }
            //       ],
            //       itemList: [
            //         {wrappage: '1',code: '3730',name: '主要包装物1',model: '2',specifications: '1',weightUnit: '千克(kg)',
            //           weight: 111,memo: ''}
            //       ],
            //       zyPackaging: [
            //         {wrappage: '2',code: '3730',name: '辅助包装物1',model: '3',specifications: '2',weightUnit: '吨(T)',
            //           weight: 222,memo: ''},
            //         {wrappage: '3',code: '3730',name: '辅助包装物2',model: '4',specifications: '3',weightUnit: '千克(kg)',
            //           weight: 333,memo: ''}
            //       ]
            //     }
            //   ]
            // }
            let stoplink = this.stoplank = res.data.data.suspendList || [];//暂停使用的包装
            //console.log('5.21-16:40',this.catunit);
            // if(nowlink.length === 0){}else{nowlink.unit = unit;}
            // if(stoplink.length === 0){}else{stoplink.unit = unit;}
            for(let n in nowlink){
              let createName = this.createName1 = nowlink[n].createName;//创建人
              let createDate = this.createDate1 = nowlink[n].createDate;//创建时间
              if(nowlink[n].outerShape === 1){nowlink[n].outSpace = "长方体";}else if(nowlink[n].outerShape === 2){nowlink[n].outSpace = "圆柱体"}
              else if(nowlink[n].outerShape === 3){nowlink[n].outSpace = "其他形状"}
              //this.list81.push(nowlink[0]);
              //let list = this.lister = nowlink[n].list;
              // for(let i in list){
              //   let structureList = this.structureList = list[i].structureList;
              //   // for(let s in structureList){
              //   //   this.zyname = structureList[s].zyPackaging.name;
              //   //   this.itemleng = structureList[s].itemList.length;
              //   // }
              // }
              // for(let i in list){
              //   let zyPackaging = list[i].zyPackaging;//主要包装物
              //   this.zyname = zyPackaging.name;
              //   let itemList = list[i].itemList;//辅助包装物
              // }
              // let itemList = this.itemList = nowlink[n].itemList;//辅助包装物信息
              // let zyPackaging = nowlink[n].zyPackaging;//
              // let structureList = nowlink[n].structureList;//包装信息详情集合
              // for(let s in structureList){
              //   if(structureList[s].outerShape === "1"){structureList[s].outSpace = "长方体";}
              //   else if(structureList[s].outerShape === "2"){structureList[s].outSpace = "圆柱体"}
              //   else if(structureList[s].outerShape === "3"){structureList[s].outSpace = "其他形状"}
              // }
              //this.list8 = this.nowlank = structureList;
            }
            this.list8 = this.nowlank = nowlink || [];//这个位置this.list81包含包装数据第一条，页面只展示第一条数据
            this.listb = this.stoplink = stoplink || [];
            if(this.list81.length !== 0){this.list81 = [];}
            if(this.list8.length === 0){
              this.oven = '';
              this.hasjson = 2;
            }else{
              for(let s in this.list8){
                //debugger;
                //this.list81 = [];
                s = Number(s);
                if(s === 0){
                  const json81 = this.list8[s];
                  this.list81.push(json81);
                }
                // this.list8[s].box = [];
                // const json8 = {
                //   outerLength: this.list8[s].outerLength,outerWidth: this.list8[s].outerWidth,
                //   outerHeight: this.list8[s].outerHeight,outSpace: this.list8[s].outSpace,
                //   materialCount: this.list8[s].materialCount,netWeight: this.list8[s].netWeight,
                //   netUnit: this.list8[s].netUnit,grossWeight: this.list8[s].netUnit,
                //   grossUnit: this.list8[s].grossUnit
                // };
                // this.list8.box.push(json8);
              }
              this.hasjson = 1;
              this.oven = 5;
            }
            //console.log('4.2-15:01',this.list8);
            // console.log('4.2-15:02',this.list81);//数据好像也不对，这里应该是一条数据的
            //this.list81 = this.list8;
          })
        })
      }else if(tem === 3){
        //是在这里调用接口
        let all = [];
        let json1 = {
          material: this.catid,//材料id
          supplierMaterial: this.suppMaterial,//供应关系id
          supplier: this.supplier,//供应商id
        };
        all.push(json1);
        all = JSON.stringify(all);
        if(typeof all === 'string'){
        }
        const keyjson2 = {data: all};
        addPacking(keyjson2).then(res => {
          if(res.data.success === 1){
            this.materlon = 6;
            this.lookhnve(this.purjson,this.catunit);
          }
        })
        // let all =[];
        // const json1 = {
        //   material: this.catid,//材料id
        //   supplierMaterial: this.suppMaterial,//供应关系id
        //   supplier: this.supplier,//供应商id
        //   lengthUnitId: this.deslist.lengthUnitId,//外廓长度单位ID
        //   lengthUnit:this.deslist.lengthUnit,//外廓长度单位
        //   outerLength:this.deslist.outerLength,//外廓长度
        //   widthUnitId:this.deslist.widthUnitId,//外廓宽度单位ID
        //   widthUnit: this.deslist.widthUnit,//外廓宽度单位
        //   outerWidth: this.deslist.outerWidth,//外廓宽度
        //   heightUnitId: this.deslist.heightUnitId,//外廓高度单位ID
        //   heightUnit: this.deslist.heightUnit,//外廓高度单位
        //   outerHeight: this.deslist.outerHeight,//外廓高度
        //   outerShape: this.deslist.outerShape,//外廓形状：1-长方体,2-圆柱体,3-其它形状
        //   list: [
        //     // jsondown:{
        //     //   itemList: [],//辅助包装物信息集合
        //     //   zyPackaging: {}//主要包装物
        //     // }
        //   ],
        // };
        // const json2 = {
        //   level: 0,//包装层级
        //   materialCount: this.deslist.materialCount,//单个包装内材料的数量
        //   itemList: [],//辅助包装物数据
        //   zyPackaging: {},//主要包装物
        // };
        // let structureList = this.deslist.structureList;
        // for(let s in structureList){
        //   let itemList = structureList[s].itemList;//辅助包装物数据
        //   let zyPackaging = structureList[s].zyPackaging;//主要包装物数据
        //   for(let i in itemList){
        //     const itemjson = {};
        //     itemjson.wrappage = itemList[i].wrappage;//包装物Id
        //     itemjson.weight = itemList[i].weight;//单重
        //     itemjson.weightUnit = itemList[i].weightUnit;//重量单位
        //     json2.itemList.push(itemjson);
        //   }
        //   json2.zyPackaging.wrappage = zyPackaging.wrappage;//包装物id
        //   json2.zyPackaging.weight = zyPackaging.weight;//单重
        //   json2.zyPackaging.weightUnit = zyPackaging.weightUnit;//重量单位
        //   json1.list.push(json2);
        //   all.push(json1);
        // }
        // all = JSON.stringify(all);
        // if(typeof all === 'string'){
        // }
        // const keyjson2 = {data:all};
        // addPacking(keyjson2).then(res => {
        //   if(res.data.success === 1){
        //     this.materlon = 6;
        //     this.lookhnve(this.purjson);
        //   }
        // })
        // const jsont = {id: this.desid,enabled: 0};
        // stuppact(jsont).then(res => {
        // })
      }else if(tem === 4){//启用数据
        const jsond = {id: this.desid,enabled: 1};
        stuppact(jsond).then(res => {
          //console.log(res);
          const jsont = {mtBaseId: this.catid,supplierMaterial: this.suppMaterial};
          seapackinfon(jsont).then(res1 => {
            let nowlink = res1.data.data.list || [];
            let stoplink = res1.data.data.suspendList || [];//停用的数据
            //console.log('5.21-16:41',this.catunit);
            // if(nowlink.length === 0){}else{nowlink.unit = unit;}
            // if(stoplink.length === 0){}else{stoplink.unit = unit;}
            for(let s in stoplink){
              let id = this.stopid = stoplink[s].id;//包装id
              if(stoplink[s].outerShape === 1){stoplink[s].outSpace = "长方体";}
              else if(stoplink[s].outerShape === 2){stoplink[s].outSpace = "圆柱体";}
              else if(stoplink[s].outerShape === 3){stoplink[s].outSpace = "其他形状";}
              stoplink[s].layers = stoplink.length + '层';//录入多少层
            }
            this.stoplank = stoplink;
            this.morebak = 4;
          })
        })
      }else if(tem === 5){
        const lank5 = this.list5;
        const id = this.id5;
        for(let e in lank5){
          //debugger;
          e = Number(e);
          if(lank5[e].id === id){
            lank5.splice(e,1);
          }
        }
      }
    },
    tipOk33(tpnumb){
      //debugger;
      if(tpnumb < 3){
        //console.log('2.13-10',this.allpack);
        // this.packagbox.numb = this.packagbox.numb.toString();
        // this.packagbox.name = this.packagbox.name.toString();
        // this.packagbox.model = this.packagbox.model.toString();
        // this.packagbox.sizer = this.packagbox.sizer.toString();
        //console.log(this.list.find(i => i.value === value).label)//此处打印出来的值就是label的值
        // let valuet = this.packagbox.widthUnit;
        // valuet = this.packagbox.widthlist.find(i => i.id === valuet).value;
        // this.packagbox.widthUnit = valuet;
        //this.packagbox.widthUnit = this.packagbox.widthUnit.toString();
        //      // 使用一元加号操作符
        //this.double = +this.integer;
        //this.packagbox.widthUnit = +this.packagbox.widthUnit;
        //console.log('现在的数据是个数字么9.11',this.packagbox.widthUnit);
        //console.log('11.28数据',this.packagbox.)
        //debugger;
        //debugger;
        const jsonok3 = {
          code: this.packagbox.numb.toString(),
          name: this.packagbox.name.toString(),
          model: this.packagbox.model.toString(),
          specifications: this.packagbox.sizer.toString(),
          weightUnit: this.packagbox.widthUnit,//重量单位
          pieceWeight: Number(this.packagbox.onlhei)//重量=单重
        };
        addnewpiket(jsonok3).then(res => {
          this.matVisible33 = false;
          this.packagbox.onlhei = Number(this.packagbox.onlhei) || 0;
          if(this.packagbox.widthUnit === 2){this.packagbox.widthUnitName = '克(g)';}else if(this.packagbox.widthUnit === 1){this.packagbox.widthUnitName = '毫克(mg)';}
          else if(this.packagbox.widthUnit === 3){this.packagbox.widthUnitName = '千克(kg)';}else if(this.packagbox.widthUnit === 4){this.packagbox.widthUnitName = '吨(T)';}
          //console.log('5.15-9:20',this.packagbox);//到这里是有name的
          //console.log('5.9-13:34',this.sizeForm.name1);
          if(tpnumb === 1){ //主要包装物
            //console.log('5.15-9:23',this.packagbox.widthUnitName);
            let json1 = {//现在弹窗中输入的数据
              codeid: this.packagbox.numb,
              name: this.packagbox.name,
              specifications: this.packagbox.sizer,
              model: this.packagbox.model,
              weimingt: this.packagbox.widthUnitName,//weimingt-这里需要展示对应的文字
              onlyweit: this.packagbox.onlhei,
              wrappage: res.data.data.id
            };
            this.addprck1 = 2;
            this.packaging.mainpack = json1;
            //console.log('主的变化了吗',  this.packaging.mainpack )
            //debugger;
            if(this.packaging.mainpack.codeid === ''){
              if(this.pickbig === 1){
                if(this.sizeForm.name1 === ''){
                  this.madestune = 0;
                }else{
                  this.madestune = 1;
                }
              }else if(this.pickbig === 2){
                if(this.sizeForm.name4 === ''){
                  this.madestune = 0;
                }else{
                  this.madestune = 1;
                }
              }
            }else{
              if(this.pickbig === 1){
                if(this.sizeForm.name1 === ''){this.madestune = 0;}else{
                  this.madestune = 1;
                }
              }else if(this.pickbig === 2){
                if(this.sizeForm.name4 === ''){this.madestune = 0;}else{
                  this.madestune = 1;
                }
              }
            }
          }else if(tpnumb === 2){ //辅助包装物
            this.addprck2 = 2;
            //debugger;
            //console.log('5.23-8:46',this.packagbox);
            if(this.packagbox.widthUnit === 2){this.packagbox.widthUnitName = '克(g)';}else if(this.packagbox.widthUnit === 1){this.packagbox.widthUnitName = '毫克(mg)';}
            else if(this.packagbox.widthUnit === 3){this.packagbox.widthUnitName = '千克(kg)';}else if(this.packagbox.widthUnit === 4){this.packagbox.widthUnitName = '吨(T)';}
            //console.log('5.23-8:58',this.packagbox);//为何这里无法存储widthUnitName这个值呢
            const json2 = {
              aucoid: this.packagbox.numb,
              auname:this.packagbox.name,
              auspecfication: this.packagbox.sizer,
              aumodel: this.packagbox.model,
              auweimin: this.packagbox.widthUnitName,//auweimin也需要展示成文字的样子
              auonlywei: this.packagbox.onlhei,
              wrappage: res.data.data.id,
              weightUnit: this.packagbox.widthUnit,
              weight:  this.packagbox.onlhei
            };//可能就是因为这个位置没有将weight和weightUnit写上，所以才会出现后面获取不到数据的情况
            this.packaging.auxiliary.push(json2);
            //console.log('辅助的变化了吗',  this.packaging.mainpack )
          }
        })
        // console.log('3.24-5',this.pacallmag);
        // console.log('3.24-6',this.allpack);
        // console.log('5.9-13:33',this.packaging.mainpack);
      }
      else if(tpnumb === 3){
        console.log('1',this.setdiftruck);
        console.log('2',this.allpack2);
        //debugger;
        //console.log('现在的值9.18',this.allpack);
        this.allpack.outLegth = this.packagbox.outLegth;
        this.allpack.outWidth = this.packagbox.outWidth;
        this.allpack.outHeight = this.packagbox.outHeight;
        // this.packaging.outLegth = this.packagbox.outLegth;
        // this.packaging.outWidth = this.packagbox.outWidth;
        // this.packaging.outHeight = this.packagbox.outHeight;
        //console.log('现在的值是多少',this.packagbox.outerShape);
        this.allpack.outerShape = this.packagbox.outerShape;
        //console.log('4.1-16:02',this.pacallmag);
        let lankt = this.allpack;
        //let alljson = {};
        let all = [];
        for(let a in lankt){
          //console.log('现在的值1',a);
          if(isNaN(a)){
            //console.log('走到这里了么2');
          }else{
            //console.log('走到这里了么');
            a = Number(a);
            let jsonup = {};
            console.log('测试7.18-13:48-测试这个4',this.catid);
            if(all.length === 0){
              jsonup = {
                material: this.catid,//材料id
                supplierMaterial: this.suppMaterial,//供应关系id
                supplier: this.supplier,//供应商id
                // level: a+1,//包装层级,例如是最外层
                // materialCount: lankt[a].onlypack,//单个包装内材料的数量
                lengthUnitId: this.packagbox.lengthUnitid,//外廓长度单位ID
                lengthUnit: '',//外廓长度单位
                outerLength: lankt.outLegth,//外廓长度
                widthUnitId: this.packagbox.widthUnitid,//外廓宽度单位ID
                widthUnit: '',//外廓宽度单位
                outerWidth: lankt.outWidth,//外廓宽度
                heightUnitId: this.packagbox.heightUnitid,//外廓高度单位ID
                heightUnit: '',//外廓高度单位
                outerHeight: lankt.outHeight,//外廓高度
                outerShape: lankt.outerShape,//外廓形状:1-长方体,2-圆柱体,3-其它形状
                list: [
                  // jsondown:{
                  //   itemList: [],//辅助包装物信息集合
                  //   zyPackaging: {}//主要包装物
                  // }
                ],
              }
              //debugger;
              //console.log(this.packagbox.Uintlist.find(i => i.id === this.packagbox.lengthUnitid).name);
              jsonup.lengthUnit = this.packagbox.Uintlist.find(i => i.id === this.packagbox.lengthUnitid).name;
              jsonup.widthUnit = this.packagbox.widlist.find(j => j.id === this.packagbox.widthUnitid).name;
              jsonup.heightUnit = this.packagbox.heilist.find(k => k.id === this.packagbox.heightUnitid).name;
              const jsondown = {
                // material: this.catid,//材料id
                // supplierMaterial: this.suppMaterial,//供应关系id
                // supplier: this.supplier,//供应商id
                level: a+1,//包装层级,例如是最外层
                materialCount: lankt[a].onlypack,//单个包装内材料的数量
                itemList: [],
                zyPackaging: {}
              };
              let auxiliary = lankt[a].auxiliary;//辅助包装物数据
              // console.log('1',auxiliary);
              // console.log('2',this.packagbox);
              // console.log('3',this.packaging);
              for(let u in auxiliary){
                const itemjson = {};//辅助包装物信息
                itemjson.wrappage = auxiliary[u].wrappage;//包装物id
                itemjson.weight = auxiliary[u].auonlywei;//单重
                itemjson.weightUnit = auxiliary[u].auweimin;//重量单位id
                jsondown.itemList.push(itemjson);//脑壳痛，itemList一直提示是undefined
              }
              jsondown.zyPackaging.wrappage = lankt[a].mainpack.wrappage;//包装物id
              jsondown.zyPackaging.weight = lankt[a].mainpack.onlyweit;//单重
              jsondown.zyPackaging.weightUnit = lankt[a].mainpack.weimingt;//重量单位id
              // jsondown.material = this.catid;
              // jsondown.supplierMaterial = this.suppMaterial;
              // jsondown.supplier = this.supplier;
              // jsondown.level = a+1;
              // jsondown.materialCount = lankt[a].onlypack;
              //console.log('现在的值12',jsonup);
              jsonup.list.push(jsondown);
              all.push(jsonup);
            }else{
              // console.log('4.1-16:12',all);//需要在这里判断all中的数据的供应商id是否有与循环中的数据的供应商id相同的
              for(let b in all){
                let aid = all[b].supplier;
                let kid = lankt[a].choid;
                if(aid === kid){
                  const jsondown = {
                    // material: this.catid,//材料id
                    // supplierMaterial: this.suppMaterial,//供应关系id
                    // supplier: this.supplier,//供应商id
                    level: a+1,//包装层级,例如是最外层
                    materialCount: lankt[a].onlypack,//单个包装内材料的数量
                    itemList: [],
                    zyPackaging: {}
                  };
                  let auxiliary = lankt[a].auxiliary;//辅助包装物数据
                  // console.log('1',auxiliary);
                  // console.log('2',this.packagbox);
                  // console.log('3',this.packaging);
                  for(let u in auxiliary){
                    const itemjson = {};//辅助包装物信息
                    itemjson.wrappage = auxiliary[u].wrappage;//包装物id
                    itemjson.weight = auxiliary[u].auonlywei;//单重
                    itemjson.weightUnit = auxiliary[u].auweimin;//重量单位id
                    jsondown.itemList.push(itemjson);//脑壳痛，itemList一直提示是undefined
                  }
                  jsondown.zyPackaging.wrappage = lankt[a].mainpack.wrappage;//包装物id
                  jsondown.zyPackaging.weight = lankt[a].mainpack.onlyweit;//单重
                  jsondown.zyPackaging.weightUnit = lankt[a].mainpack.weimingt;//重量单位id
                  // jsondown.material = this.catid;
                  // jsondown.supplierMaterial = this.suppMaterial;
                  // jsondown.supplier = this.supplier;
                  // jsondown.level = a+1;
                  // jsondown.materialCount = lankt[a].onlypack;
                  //console.log('现在的值12',jsonup);
                  all[b].list.push(jsondown);
                  // let list = all[b].list;
                  // list.push(jsondown);
                  // all[b].list = list;
                }
              }
            }
            //console.log('现在的值20',list);
          }
          //if(a === NaN){a = undefined}
          //console.log('现在没有值么？',lankt[a]);
          // if(a === outLegth){}else{}
        }
        //console.log('现在的值9.20',list);
        //现在需要将数组转换成字符串
        //console.log('9.23',all);
        this.allpack = lankt;//lankt没有需要的值
        this.alladdmig = all;//all里面有需要的值
        //console.log('2024.11.1万圣节-6.4-this.alladdmig',this.alladdmig);
        //console.log('this.alladdmig现在的数据',this.alladdmig);
        //console.log('4.1-16:24',all);
        this.matVisible33 = false;
        //感觉是需要针对于单条数据
        //this.allpack.mdasune = true;
        //this.materlon = 2;
        //this.catok = 1;
        //console.log(this.a);
        //console.log('现在不是1么',this.gobck2);
        if(this.respectively === 2){this.gobck2 = 1;}else if(this.respectively === 1){this.gobck2 = 0;}
        if(this.gobck2 === 1){//gobck2 = 1是从查看过来的
          this.alladdmig = JSON.stringify(this.alladdmig);
          const keyjson = { data: this.alladdmig};
          addPacking(keyjson).then(res => {
            if(res.data.success === 1){
              ///console.log('1',this.ketcs);
              if(this.ketcs === 0){
                this.materlon = 6;
                this.lookhnve(this.purjson,this.catunit);
              }else{
                //console.log('5.16-11:01','三月三月最可爱！');
                this.materlon = 5;
                const supjson = {mtBaseId: this.catid};
                this.looknexten(supjson,this.catunit);
              }
            }
          })
        }else if(this.gobck2 === 0){
          this.materlon = 2;
          //this.catok = 1;
          this.potint = 'take';
          const jsong = {mtBaseId: this.catid};
          // console.log(this.catunit);
          this.getsuptent(jsong,this.catunit);
        }
        // this.potint = 'take';
        // const jsong = {mtBaseId: this.catid};
        // this.getsuptent(jsong);
        // all = JSON.stringify(all);
        // if(typeof all === 'string'){
        // }
        //需要将上面的all数组存一下，在点击右上角的编写完成按钮时获取。
        //const keyjson = {data: all};
        // addPacking(keyjson).then(res => {
        //   //当返回操作成功时，跳转页面
        //   if(res.data.success === 1){
        //     this.matVisible33 = false;
        //     this.materlon = 2;
        //     this.catok = 1;
        //     this.potint = 'take';
        //     const jsong = {mtBaseId: this.catid};
        //     this.getsuptent(jsong);
        //   }
        // })
        //debugger;
        //console.log('2.17现在的数据是包含全部包装数据么',this.allpack);
        //this.allpack = lankt;
        //console.log('1',this.alladdmig);
        //console.log('2',this.allpack);
        //console.log('2.1',lankt);
        //console.log('3',this.multipleSelection);//批量编辑时勾选的全部数据
        //console.log('4',this.pacallmag);
        //multipleSelection中的supplier=strid
        //debugger;
        console.log('7.25-11:07',this.allpack);
        for(let b in this.pacallmag){
          let pacid = this.pacallmag[b].strid;//782
          for(let d in this.alladdmig){
            for(let a in this.allpack){
              if(a === "outHeight" || a === "outLegth" || a === "outWidth" || a === "outerShape" || a === "unit"){}else{
                a = Number(a);
                let chonid = this.allpack[a].choid;//这里在批量编辑时，可能出现没有值的情况
                if(chonid === ""){//从批量编辑过来的
                  for(let m = 0;m<this.multipleSelection.length;m++) {
                    let suid = this.multipleSelection[m].supplier;
                    if(suid === pacid){//782
                      this.pacallmag[b].heightUnit = this.alladdmig[d].heightUnit;
                      this.pacallmag[b].heightUnitId = this.alladdmig[d].heightUnitId;
                      this.pacallmag[b].lengthUnit = this.alladdmig[d].lengthUnit;
                      this.pacallmag[b].lengthUnitId = this.alladdmig[d].lengthUnitId;
                      this.pacallmag[b].widthUnit = this.alladdmig[d].widthUnit;
                      this.pacallmag[b].widthUnitId = this.alladdmig[d].widthUnitId;
                      this.pacallmag[b].outerLength = this.alladdmig[d].outerLength;
                      this.pacallmag[b].outerWidth = this.alladdmig[d].outerWidth;
                      this.pacallmag[b].outerHeight = this.alladdmig[d].outerHeight;
                      let jsons = {
                        auxiliary: this.allpack[a].auxiliary,
                        eveuppacnum: this.allpack[a].eveuppacnum,
                        id: this.allpack[a].id,
                        isExists: this.allpack[a].isExists,
                        level: a+1,
                        mainpack: this.allpack[a].mainpack,
                        materialCount: this.allpack[a].onlypack,
                        newer: this.allpack[a].newer,
                        onlygrosswei: this.allpack[a].onlygrosswei,
                        onlynetwei: this.allpack[a].onlynetwei,
                        onlypack: this.allpack[a].onlypack,
                        outHeight: this.allpack.outHeight,
                        outLegth: this.allpack.outLegth,
                        outWidth: this.allpack.outWidth,
                        outerShape: this.allpack.outerShape,
                        choid: this.allpack[a].choid
                      };
                      jsons.mainpack.wrappage = this.allpack[a].mainpack.wrappage;
                      jsons.mainpack.weight = this.allpack[a].mainpack.onlyweit;
                      jsons.mainpack.weightUnit = this.allpack[a].mainpack.weimingt
                      const aink = this.alladdmig[d].list;
                      for(let i in aink){
                        let itemst = aink[i].itemList;
                        for(let t in itemst){
                          let auxiliary = jsons.auxiliary;
                          for(let u in auxiliary){
                            auxiliary[u].weight = itemst[t].weight;
                            auxiliary[u].weightUnit = itemst[t].weightUnit || "";
                          }
                        }
                      }
                      if(this.pacallmag[b].megbox.length === 0){
                        this.pacallmag[b].megbox.push(jsons);
                      }else{
                        let megbox = this.pacallmag[b].megbox;
                        for(let m  = 0;m<megbox.length;m++){
                          if(megbox[m].id === jsons.id){break;}else{this.pacallmag[b].megbox.push(jsons); }
                        }
                      }
                    }
                  }
                }else{//从表格中的编辑按钮过来的
                  console.log('7.25-2-chonid',chonid);
                  if(chonid === pacid){
                    this.pacallmag[b].heightUnit = this.alladdmig[d].heightUnit;
                    this.pacallmag[b].heightUnitId = this.alladdmig[d].heightUnitId;
                    this.pacallmag[b].lengthUnit = this.alladdmig[d].lengthUnit;
                    this.pacallmag[b].lengthUnitId = this.alladdmig[d].lengthUnitId;
                    this.pacallmag[b].widthUnit = this.alladdmig[d].widthUnit;
                    this.pacallmag[b].widthUnitId = this.alladdmig[d].widthUnitId;
                    this.pacallmag[b].outerLength = this.alladdmig[d].outerLength;
                    this.pacallmag[b].outerWidth = this.alladdmig[d].outerWidth;
                    this.pacallmag[b].outerHeight = this.alladdmig[d].outerHeight;
                    let jsonp = {
                      auxiliary: this.allpack[a].auxiliary,
                      eveuppacnum: this.allpack[a].eveuppacnum,
                      id: this.allpack[a].id,
                      isExists: this.allpack[a].isExists,
                      level: a+1,
                      mainpack: this.allpack[a].mainpack,
                      materialCount: this.allpack[a].onlypack,
                      newer: this.allpack[a].newer,
                      onlygrosswei: this.allpack[a].onlygrosswei,
                      onlynetwei: this.allpack[a].onlynetwei,
                      onlypack: this.allpack[a].onlypack,
                      outHeight: this.allpack.outHeight,
                      outLegth: this.allpack.outLegth,
                      outWidth: this.allpack.outWidth,
                      outerShape: this.allpack.outerShape,
                      choid: this.allpack[a].choid
                    };
                    const aink = this.alladdmig[d].list;
                    for(let i in aink){
                      let itemst = aink[i].itemList;
                      for(let t in itemst){
                        let auxiliary = jsonp.auxiliary;
                        for(let u in auxiliary){
                          auxiliary[u].weight = itemst[t].weight;
                          auxiliary[u].weightUnit = itemst[t].weightUnit || "";
                        }
                      }
                    }
                    jsonp.mainpack.wrappage = this.allpack[a].mainpack.wrappage;
                    jsonp.mainpack.weight = this.allpack[a].mainpack.onlyweit;
                    jsonp.mainpack.weightUnit = this.allpack[a].mainpack.weimingt
                    //这里需要判断pacallmag的megbox中是否已经包含将要加入megbox中的那条json数据，如果已有就不重复增加了
                    if(this.pacallmag[b].megbox.length === 0){
                      this.pacallmag[b].megbox.push(jsonp);
                    }else{
                      let megbox = this.pacallmag[b].megbox;
                      for(let m  = 0;m<megbox.length;m++){
                        if(megbox[m].id === jsonp.id){break;}else{this.pacallmag[b].megbox.push(jsonp); }
                      }
                    }
                  }
                }
                //return false;
              }
            }
          }
        }
        console.log('7.25-1',this.pacallmag);
        // for(let a in this.allpack){
        //   if(a === "outHeight" || a === "outLegth" || a === "outWidth" || a === "outerShape" || a === "unit"){}else{
        //     debugger;
        //     let chonid = this.allpack[a].choid;
        //     for(let b in this.pacallmag){
        //       let pacid = this.pacallmag[b].strid;
        //       if(chonid === pacid){//当allpack中的某一条json数据的choid=pacallmag中的某一条json数据的strid时
        //         //需要进行赋值
        //         //this.pacallmag[b].megbox = [];
        //         let jsonp = {
        //           auxiliary: this.allpack[a].auxiliary,
        //           eveuppacnum: this.allpack[a].eveuppacnum,
        //           id: this.allpack[a].id,
        //           isExists: this.allpack[a].isExists,
        //           mainpack: this.allpack[a].mainpack,
        //           newer: this.allpack[a].newer,
        //           onlygrosswei: this.allpack[a].onlygrosswei,
        //           onlynetwei: this.allpack[a].onlynetwei,
        //           onlypack: this.allpack[a].onlypack,
        //           outHeight: this.allpack.outHeight,
        //           outLegth: this.allpack.outLegth,
        //           outWidth: this.allpack.outWidth,
        //           outerShape: this.allpack.outerShape,
        //           choid: this.allpack[a].choid
        //         };
        //         this.pacallmag[b].megbox.push(jsonp);
        //         //json= {strid: 1,megbox: {}};
        //         // this.pacallmag[b].auxiliary = this.allpack[a].auxiliary;
        //         // this.pacallmag[b].eveuppacnum = this.allpack[a].eveuppacnum;
        //         // this.pacallmag[b].id = this.allpack[a].id;
        //         // this.pacallmag[b].isExists = this.allpack[a].isExists;
        //         // this.pacallmag[b].mainpack = this.allpack[a].mainpack;
        //         // this.pacallmag[b].newer = this.allpack[a].newer;
        //         // this.pacallmag[b].onlygrosswei = this.allpack[a].onlygrosswei;
        //         // this.pacallmag[b].onlynetwei = this.allpack[a].onlynetwei;
        //         // this.pacallmag[b].onlypack = this.allpack[a].onlypack;
        //         // this.pacallmag[b].outHeight = this.allpack.outHeight;
        //         // this.pacallmag[b].outLegth = this.allpack.outLegth;
        //         // this.pacallmag[b].outWidth = this.allpack.outWidth;
        //         // this.pacallmag[b].outerShape = this.allpack.outerShape;
        //         // this.pacallmag[b].choid = this.allpack[a].choid;
        //       }
        //     }
        //   }
        // }
        //console.log('3.24-7',this.pacallmag);//这里包含了新设置的数据
        //console.log('3.24-8',this.allpack);
        //console.log('4.10-11:14',this.list3);
      }
      else{
        //修改包装物数据
        //原本走tpnumb<3的时候是在原基础上增加了一条新的数据，现在需要判断新的那条数据跟所有旧数据
        //中哪条数据的Id相同，并将id相同的旧数据与新数据不同的位置改成新数据的数据
        //this.packagbox中的numb代表代号
        //1.{a:1,b:2},2.{a:2,b:2}
        //debugger;
        //debugger;
        //console.log('3.21',this.morebak);
        if(this.morebak === ""){
          //console.log('乙巳年正月十六-拾叁',this.allpack);
          const lank6 = this.list6;//this.list6中的aucoid代表代号
          //list6是需要展示在表格中的数据，packagbox是输入框中的数据
          this.uptlist.aucoid = this.packagbox.numb;
          this.uptlist.auname = this.packagbox.name;
          this.uptlist.auspecfication = this.packagbox.sizer;
          this.uptlist.aumodel = this.packagbox.model;
          this.uptlist.auonlywei = this.packagbox.onlhei;
          //console.log('现在是数字',this.packagbox.widthUnit);//这里的数据是变的，那就是下面的赋值有问题了
          if(this.packagbox.widthUnit === 1){this.uptlist.auweimin = '毫克(mg)'}
          else if(this.packagbox.widthUnit === 2){this.uptlist.auweimin = '克(g)'}
          else if(this.packagbox.widthUnit === 3){this.uptlist.auweimin = '千克(kg)'}
          else if(this.packagbox.widthUnit === 4){this.uptlist.auweimin = '吨(T)'}
          for(let f in lank6){
            let auid = lank6[f].aucoid;
            if(auid === this.uptlist.keyd){
              lank6[f].aucoid = this.uptlist.aucoid;
              lank6[f].auname = this.uptlist.auname;
              lank6[f].aumodel = this.uptlist.aumodel;
              lank6[f].auspecfication = this.uptlist.auspecfication;
              if(this.uptlist.auweimin === '1'){lank6[f].auweimin = '毫克(mg)';}else if(this.uptlist.auweimin === '2'){lank6[f].auweimin = '克(g)';}
              else if(this.uptlist.auweimin === '3'){lank6[f].auweimin = '千克(kg)';}else if(this.uptlist.auweimin === '4'){lank6[f].auweimin = '吨(T)';}
              //lank6[f].auweimin = this.uptlist.auweimin;//为何1和2可以，3和4就不行呢？
              lank6[f].auonlywei = this.uptlist.auonlywei;
            }
            //   if(auid === this.packagbox.numb){
            //     //修改的数据跟查看的数组中某一条数据的id相同
            //     // lank6[f].aucoid = this.packagbox.numb;
            //     // lank6[f].aumodel = this.packagbox.model;
            //     // lank6[f].auname = this.packagbox.name;
            //     // lank6[f].auweimin = this.packagbox.widthUnit;
            //     // lank6[f].auspecfication = this.packagbox.sizer;
            //     // lank6[f].auonlywei = this.packagbox.onlhei;
            //   }
          }
          this.list6 = lank6;
          this.matVisible33 = false;
        }else if(this.morebak === 3){//需要调用‘修改包装方式’接口
          //debugger;
          //console.log('3.26-11:29',this.pacallmag);//这里也有wrappage那几个属性
          let updatmag = {};
          //console.log('3.31-15:55',this.packagbox);//需要将这里面的长宽高赋值下
          // if(this.packagbox.outerShape === '1'){this.packagbox.outerShape = '长方体';}
          // else if(this.packagbox.outerShape === '2'){this.packagbox.outerShape = '圆柱体';}
          // else if(this.packagbox.outerShape === '3'){this.packagbox.outerShape = '其他形状';}
          //if(this.packagbox.lengthUnitid === 3){this.packagbox.}
          //console.log('3.31-15:48',this.allpack);
          //console.log('3.26-14:29',this.strid);
          //console.log('3.27-10:40',this.stall);//stall是个json  这里也有wrappage了
          for(let p = 0;p<this.pacallmag.length;p++){
            let pid = this.pacallmag[p].id;
            let sid = this.stall.id;//heightUnitid
            if(this.packagbox.lengthUnitid === 1){this.stall.lengthUnit = 'mm';}else if(this.packagbox.lengthUnitid === 2){this.stall.lengthUnit = 'cm';}
            else if(this.packagbox.lengthUnitid === 3){this.stall.lengthUnit = 'm';}
            if(this.packagbox.widthUnitid === 1){this.stall.widthUnit = 'mm';}else if(this.packagbox.widthUnitid === 2){this.stall.widthUnit = 'cm';}
            else if(this.packagbox.widthUnitid === 3){this.stall.widthUnit = 'm';}
            if(this.packagbox.heightUnitid === 1){this.stall.heightUnit = 'mm';}else if(this.packagbox.heightUnitid === 2){this.stall.heightUnit = 'cm';}
            else if(this.packagbox.heightUnitid === 3){this.stall.heightUnit = 'm';}
            if(pid === sid){
              let jsonp1 = {
                lengthUnitId: this.packagbox.lengthUnitid,
                lengthUnit: this.stall.lengthUnit,
                outerLength: this.packagbox.outLegth,
                widthUnitId: this.packagbox.widthUnitid,
                widthUnit: this.stall.widthUnit,
                outerWidth: this.packagbox.outWidth,
                heightUnitId: this.packagbox.heightUnitid,
                heightUnit: this.stall.heightUnit,
                outerHeight: this.packagbox.outHeight,
                outerShape: this.packagbox.outerShape,
                id: this.stall.id,
                material: this.stall.material,
                supplierMaterial: this.stall.supplierMaterial,
                supplier: this.stall.supplier,
                list: []
                // level: '',//到底这个字段该取哪个值呢？pacallmag中的值？还是stall中的值？
                // materialCount: this.stall.materialCount,
                // itemList: [],
                // zyPackaging: {}
              };
              let structureList = this.stall.structureList;
              for(let s = 0;s<structureList.length;s++){
                let jsonp2 = {
                  level: structureList[s].level,
                  materialCount: structureList[s].materialCount,
                  itemList: [],
                  zyPackaging: {}
                };
                let itemList = structureList[s].auxiliary || structureList[s].itemList;
                for(let t = 0;t<itemList.length;t++){
                  let jsonp3 = {
                    wrappage: itemList[t].wrappage,
                    weight: itemList[t].weight,
                    weightUnit: itemList[t].weightUnit
                  };
                  jsonp2.itemList.push(jsonp3);
                }
                let zyPackaging = structureList[s].mainpack || structureList[s].zyPackaging;
                jsonp2.zyPackaging.wrappage = zyPackaging.wrappage;
                jsonp2.zyPackaging.weight = zyPackaging.weight;
                jsonp2.zyPackaging.weightUnit = zyPackaging.weightUnit;
                jsonp1.list.push(jsonp2);
              }
              updatmag = jsonp1;
              //updatmag.push(jsonp1);
              // let structureList = this.stall.structureList;
              // for(let s = 0;s<structureList.length;s++){
              //   jsonp1.level = structureList[s].level;
              //   let itemList = structureList[s].auxiliary;
              //   for(let t = 0;t<itemList.length;t++){
              //     let jsonp2 = {
              //       wrappage: itemList[t].wrappage,
              //       weight: itemList[t].weight,
              //       weightUnit: itemList[t].weightUnit
              //     };
              //     jsonp1.itemList.push(jsonp2);
              //   }
              //   let zyPackaging = structureList[s].mainpack;
              //   jsonp1.zyPackaging.wrappage = zyPackaging.wrappage;
              //   jsonp1.zyPackaging.weight = zyPackaging.weight;
              //   jsonp1.zyPackaging.weightUnit = zyPackaging.weightUnit;
              //   updatmag.push(jsonp1);
              // }
            }
            // if(this.pacallmag[p].strid === this.strid){//尴尬了，似乎所有的数据的strid都一样，该如何分别出编辑修改的是哪条数据呢？
            //   let jsonp1 = {
            //     material: this.pacallmag[p].material,
            //     level: '',
            //     materialCount: '',
            //     itemList: [],
            //     zyPackaging: {}
            //   };
            //   let megbox = this.pacallmag[p].megbox;
            //   for(let m = 0;m<megbox.length;m++){
            //     jsonp1.level = megbox[m].level;
            //     jsonp1.materialCount = megbox[m].materialCount;
            //     let itemList = megbox[m].auxiliary;
            //     for(let i =0;i<itemList.length;i++){
            //       let jsonp2 = {
            //         wrappage: itemList[i].wrappage,
            //         weight: itemList[i].weight,
            //         weightUnit: itemList[i].weightUnit
            //       };
            //       jsonp1.itemList.push(jsonp2);
            //     }
            //     let zyPackaging = megbox[m].mainpack;
            //     jsonp1.zyPackaging.wrappage = zyPackaging.wrappage;
            //     jsonp1.zyPackaging.weight = zyPackaging.weight;
            //     jsonp1.zyPackaging.weightUnit = zyPackaging.weightUnit;
            //     updatmag.push(jsonp1);
            //   }
            // }else{}
          }
          //console.log('3.26-13:19',updatmag);
          // return false;
          updatmag = JSON.stringify(updatmag);
          if(typeof updatmag === "string"){}
          const keyjson = { data: updatmag};
          updatePacking(keyjson).then(res => {
            if(res.data.success === 1){
              this.matVisible33 = false;
              this.materlon = 6;
              this.lookhnve(this.purjson,this.catunit);
            }
          })
        }
      }
    },
    numberToChinese1(number){
      const chineseNumbers = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
      const units = ['', '十', '百', '千', '万'];
      let result = '';
      if (number === 0) {
        return chineseNumbers[0];
      }
      const digits = number.toString().split('');
      for (let i = 0; i < digits.length; i++) {
        const digit = parseInt(digits[i], 10);
        result += chineseNumbers[digit] + (units[digits.length - i - 1] || '');
      }
      return result;
    },
    //编辑本层包装信息
    acternnext(prectice,t,unit){//unit代表计量单位
      //这里其实都可以只或许一开始存储的那一个 计量单位数据即可，这样无需担心会因为重复的读取和存取导致
      //出现问题。
      //console.log('5.21-20:37',unit);
      //debugger;
      //当this.pacallmag中有值时，就需要将pacallmag中的值在页面中展示了,prectice的id可以用来和pacallmag中的id进行比较，如果相同
      //则需要将获取到的数据展示在页面中
      //console.log('2.13-7',this.allpack);
      console.log('测试7.18-13:48-测试这个3',this.catid);
      this.nextpic = 2;
      //if(this.setdiftruck === 1){this.madestune = 2;}
      if(prectice.id){this.madestune = 1;}else{//precice.id = 0的时候不算id有值么？？？？
        if(prectice.id === 0){this.madestune = 1;}else{this.madestune = 0;}}
      this.nowid = prectice.id;//好吧，看来这一行只能放在这儿，放入函数中就要出错。
      console.log('7.22-12:01',this.nowid);
      //console.log('乙巳年正月十六-申拾祁',this.allpack);
      this.packaging.typeCeng = 2;
      if(this.gobck22 === 1){t=2;}//this.gobck22=1的时候是在首页从查看按钮执行过来的
      else{if(this.looker === 1){t=2;}else{t=1;}}
      //console.log('乙巳年正月十六-申拾捌',this.allpack);
      //console.log('3.13',this.packaging);
      if(t === 1){
        this.gobck2 = 1;
        this.addprck1 = 2;
        this.addprck2 = 2;
        // this.packagbox = prectice.zyPackaging || prectice.mainpack;//主要包装物
        //原来prectice这里就可以获取主要包装物的数据呀，那可以将这部分数据保存下来，用于后面进行获取
        // this.packagbox.numb = this.packagbox.code || this.packagbox.codeid;
        // this.packagbox.sizer = this.packagbox.specifications;
        // this.packagbox.widthUnit = this.packagbox.weightUnit || this.packagbox.weimingt;
        // this.packagbox.widthlist =  [{ id: 1, value:'毫克(mg)' },{ id: 2, value: '克(g)' },{ id: 3, value: '千克(kg)' },{ id: 4, value: '吨(T)' }];
        //console.log('乙巳年正月十六-申贰肆',this.allpack);
        if(this.respectively === 2){
          this.packaging.auxiliary = prectice.itemList || prectice.auxiliary;//辅助包装物
          if(prectice.zyPackaging === undefined){prectice.zyPackaging = prectice.mainpack;}
          this.packaging.mainpack = prectice.zyPackaging;//主要包装物
          //console.log('3.13没有值么？',this.packaging.mainpack);
        }else if(this.respectively === 1){
          if(this.packaging.id === prectice.id){
            this.packaging.auxiliary = prectice.itemList || prectice.auxiliary;//辅助包装物
            //console.log('4.1-11:02',this.packaging.auxiliary);
            this.packaging.mainpack = prectice.zyPackaging || prectice.mainpack;//主要包装物
          }else{}
        }
        //console.log('乙巳年正月十六-申贰叁',this.allpack);
        //console.log('乙巳年正月十六-申拾玖',this.allpack);//执行到这里就出现了第二条数据被赋值了错误的辅助包装物数据
      }else{//设置包装材料数据的部分
        //1.此时是并未设置任何数据
        this.gobck2 = 0;
        if(this.setdiftruck === 1){
          if(this.list5.length === 1 && this.allpack.length === 1){//是这样的，因为this.allpack是存储所有设置的包装数据的，当一次跳转到设置包装物
            //这个界面时，this.allpack可以正常存储数据；可当第二次进入这个页面后再设置包装数据，就会出现从数组中原有已经存储的数据基础上
            //进行增加，这是不对的。故需要在存储时进行区分。
            //console.log('5.8-15:31',this.list5);
            //console.log('4.10-11:26',this.allpack);
            for(let p in this.allpack){
              if(this.allpack[p].auxiliary){
                this.addprck1 = 2;this.addprck2 = 2;
              }else{
                this.addprck1 = 1;this.addprck2 = 1;this.sizeForm.name4 = '';
                this.sizeForm.name1 = '';
              }
            }
            this.addprck1 = 1;this.addprck2 = 1;this.sizeForm.name4 = '';
            this.sizeForm.name1 = '';
            //this.sizeForm.name2 = '';this.sizeForm.name3 = '';
          }else{
            //console.log('5.8-15:26',this.list5);
            //console.log('5.8-15:28',this.allpack);
            if(this.allpack.length === 0){//此时是在‘查看’部分，故allpack中没有值.
              this.addprck1 = 1;this.addprck2 = 1;
            }else{
              this.addprck1 = 2;this.addprck2 = 2;
            }
          }
        }else if(this.setdiftruck === 2){
          if(this.list5.length === 1 && this.allpack2.length === 1){//是这样的，因为this.allpack是存储所有设置的包装数据的，当一次跳转到设置包装物
            //这个界面时，this.allpack可以正常存储数据；可当第二次进入这个页面后再设置包装数据，就会出现从数组中原有已经存储的数据基础上
            //进行增加，这是不对的。故需要在存储时进行区分。
            //console.log('5.8-15:31',this.list5);
            //console.log('4.10-11:26',this.allpack);
            for(let p in this.allpack2){
              if(this.allpack2[p].auxiliary){
                this.addprck1 = 2;this.addprck2 = 2;
              }else{
                this.addprck1 = 1;this.addprck2 = 1;this.sizeForm.name4 = '';
                this.sizeForm.name1 = '';
              }
            }
            this.addprck1 = 1;this.addprck2 = 1;this.sizeForm.name4 = '';
            this.sizeForm.name1 = '';
            //this.sizeForm.name2 = '';this.sizeForm.name3 = '';
          }else{
            //console.log('5.8-15:26',this.list5);
            //console.log('5.8-15:28',this.allpack);
            if(this.allpack2.length === 0){//此时是在‘查看’部分，故allpack中没有值.
              this.addprck1 = 1;this.addprck2 = 1;
            }else{
              this.addprck1 = 2;this.addprck2 = 2;
            }
          }
        }
      }
      this.matVisible = true;
      if(prectice.newer === '最外层包装'){
        this.pickbig = 1;
      }else{this.pickbig = 2;}
      this.packaging = prectice;
      this.packaging.auxiliary = prectice.auxiliary || prectice.itemList || [];
      this.sizeForm.name4 = prectice.eveuppacnum || '';
      this.sizeForm.name1 = prectice.onlypack || '';
      this.sizeForm.name2 = prectice.onlynetwei || '';
      this.sizeForm.name3 = prectice.onlygrosswei || '';
      this.sizeForm.unit = unit;
      // for(let a in alllank){
      //   if(alllank[a].id === prid){
      //     if(alllank[a].mainpack !== {}){
      //       this.packagbox.numb = alllank[a].mainpack.codeid;
      //       this.packagbox.name = alllank[a].mainpack.name;
      //       this.packagbox.model = alllank[a].mainpack.model;
      //       this.packagbox.sizer = alllank[a].mainpack.specifications;
      //       this.packagbox.widthUnit = alllank[a].mainpack.weimingt;
      //     }
      //     this.packaging.auxiliary = alllank[a].auxiliary || [];
      //     //this.packagbox = alllank[a].mainpack;
      //     //this.packaging.auxiliary = alllank[a].auxiliary;
      //     this.sizeForm.name4 = alllank[a].eveuppacnum || 0;
      //     this.sizeForm.name1 = alllank[a].onlypack || 0;
      //     this.sizeForm.name2 = alllank[a].onlynetwei || 0;
      //     this.sizeForm.name3 = alllank[a].onlygrosswei || 0;
      //   }
      // }
      //感觉这个位置似乎写乱了，因为有很多种情况，所以需要细致分别下
      //prectice此时是有辅助包装物数据的，所以需要渲染
      //console.log('乙巳年正月十六-申贰拾',this.allpack);
      this.list6 = prectice.auxiliary;
      if(t === 1){}else{
        this.anoter = 1;
      }
      //console.log('乙巳年正月十六-申贰壹',this.allpack);
      let afern = false;
      //console.log(this.packaging.mainpack);
      if(this.respectively === 2){
        if(this.packaging.zyPackaging === undefined){}else{
          this.packaging.mainpack = this.packaging.zyPackaging;
        }
      }else if(this.respectively === 1){}
      if(this.packaging.mainpack === undefined){
        afern = false;
        //this.respectively = '';
      }else{
        if(this.respectively === 2){
          if(this.packaging.mainpack.code === undefined){afern = false;}else{afern = true;}
        }else if(this.respectively === 1){
          if(this.packaging.mainpack.codeid === undefined){afern = false;}else{afern = true;}
        }
      }
      //console.log('乙巳年正月十六-申贰贰',this.allpack);
      if(this.sizeForm.name1 && afern === true){this.madestune = 1;}else{this.madestune = 0;}
      //console.log('3.24-3',this.pacallmag);//这里也有wrappage属性
      //console.log('3.24-4',this.allpack);
      if(this.morebak === 3){this.maksune = 'upent'}
      if(this.setdiftruck === 1){//未设置
        this.madestune = 1;
      }else if(this.setdiftruck === 2){
        this.madestune = 2;
      }
      //console.log('2.13-8',this.allpack);
      //console.log('3.5',this.pickbig);
      //console.log('3.5-2',this.respectively);
      //console.log('3.5-3',this.addprck1);
    },
    //录入下一层包装信息
    writnext(){
      this.madestune = 0;
      this.packaging.typeCeng = 1;
      //debugger;
      //console.log('4.3-10:02',this.list5);
      //console.log('4.3-10:03',this.allpack);
      let kedatun = '';
      if(this.setdiftruck === 1){
        if(this.allpack.length === 0){kedatun = true;}else{
          for(let p in this.allpack){
            if(p === 'outLegth' || p === 'outWidth' || p === 'outHeight' || p === 'outerShape' || p === 'unit'){}else{
              if(this.allpack[p].onlypack === '' || this.allpack[p].mainpack.length === 0){kedatun = true;}else{kedatun = false;}
            }
          }
        }
      }else if(this.setdiftruck === 2){
        if(this.allpack2.length === 0){kedatun = true;}else{
          for(let p in this.allpack2){
            if(p === 'outLegth' || p === 'outWidth' || p === 'outHeight' || p === 'outerShape' || p === 'unit'){}else{
              if(this.allpack2[p].onlypack === '' || this.allpack2[p].mainpack.length === 0){kedatun = true;}else{kedatun = false;}
            }
          }
        }
      }
      // if(this.allpack.length === 0){kedatun = true;}else{
      //   for(let p in this.allpack){
      //     if(p === 'outLegth' || p === 'outWidth' || p === 'outHeight' || p === 'outerShape' || p === 'unit'){}else{
      //       if(this.allpack[p].newer === undefined){kedatun = true;}else{kedatun = false;}
      //     }
      //   }
      // }
      this.list5.forEach(item => {
        if(item.id){kedatun = false;}
      })
      //this.list5默认是有条数据的，但是因为它是最外层包装，
      if(this.list5.length === 1 && kedatun === true){
        ElMessage({
          dangerouslyUseHTMLString: true,
          message: '<div style="text-align: center;">操作失败！<br /><br />点击“录入下一层包装”时，系统要求最外层的包装需编辑需完！</div>',
          type: 'error'
        })
        for(let s in this.list5){
          if(this.list5.onlypack === ''){//好怪哦，算了只要能正常展示就好
          }else{}
        }
      }else{
        //this.nowid = 0;//让在点击“录入下一次包装”时this.nowid===0;
        this.matVisible = true;
        this.pickbig = 2;
        this.addprck1 = 1;
        this.addprck2 = 1;
        this.sizeForm.name4 = "";
        this.sizeForm.name1 = "";
        //this.sizeForm.name2 = "";
        //this.sizeForm.name3 = "";
        this.packaging = {
          pid: '',
          mainpack: {
            codeid: '',//代号
            name: '',//名称
            specifications: '',//规格
            model: '',//型号
            weimingt: '',//重量单位
            onlyweit: '',//单重
          },
          auxiliary: [],
          eveuppacnum: '',
          onlypack: '',
          onlynetwei: '',
          onlygrosswei: ''
        }
        this.nowid = '';//所以这个位置到底该不该清空呢？？？或者这里清空，后面再赋值？
      }
      this.nextpic = 1;
      //如何知道最外层包装是否编辑完成呢？
    },
    formatNumb2(val2){
      let temp2 = val2.toString();
      //清除除了数字，小数点和负号意外的所有字符
      temp2 = temp2.replace(/[^\d.]/g, '');
      //console.log('11.28',this.packagbox.onlhei);
      this.packagbox.onlhei = temp2;
    },
    //限制输入框可以输入的内容
    formatNumb1(val){
      let temp = val.toString();
      //清除除了数字，小数点和负号意外的所有字符
      temp = temp.replace(/[^\d.]/g, '');
      let borund = '';
      //debugger;
      console.log('5.9-13:18',this.packaging);
      if(this.packaging.mainpack?.codeid === ''){this.packaging.mainpack.codeid = this.packaging.mainpack.code;}
      if(this.packaging.mainpack === undefined || this.packaging.mainpack.codeid === ''){borund = '';}else{
        if(this.respectively === 2){//从‘查看’过来的
          //console.log(this.packaging.mainpack.codeid);
          if(this.packaging.mainpack.code === undefined){
            if(this.packaging.mainpack.codeid === undefined || this.packaging.mainpack.codeid === ''){ borund = '';}else{borund = true;}
          }else{borund = true;}
        }else if(this.respectively === 1){//从‘去设置’过来的
          if(this.packaging.mainpack.codeid === undefined || this.packaging.mainpack.codeid === ''){borund = '';}else{borund = true;}
        }
      }
      //let borund = this.packaging.mainpack.codeid || '';//先输入输入框中内容，后设置mainpack时
      if(val !== '' && borund !== ''){
        this.madestune = 1;
      }else{
        this.madestune = 0;
      }
    },
    //增加包装物
    addpackag(diffnum){
      //console.log('为何又突然不展示了呢？感觉是电脑的问题大一些',this.packagbox);
      //this.refreshKey = Math.random()
      //this.packagbox.widthlist = [{ id: 1, value:'毫克(mg)' },{ id: 2, value: '克(g)' },{ id: 3, value: '千克(kg)' },{ id: 4, value: '吨(T)' }];
      //console.log('2025.2.7-有数据么',this.packagbox.widthlist);
      //console.log('2.13-9',this.allpack);
      this.packagbox.widthlist = [{ id: 1, value:'毫克(mg)' },{ id: 2, value: '克(g)' },{ id: 3, value: '千克(kg)' },{ id: 4, value: '吨(T)' }];
      this.matVisible33 = true;
      //console.log('4.7-11:48',this.packagbox.widthlist);//好诡异，这个位置能成功打印出值，但是下面就是获取不到………………
      this.addunten = 1;
      //this.packagbox.widthUnit = '';
      //this.packagbox.widthlist = [{ id: 1, value:'毫克(mg)' },{ id: 2, value: '克(g)' },{ id: 3, value: '千克(kg)' },{ id: 4, value: '吨(T)' }];
      this.packagbox.onlhei = '';
      //diffnum可能是1也可能是2，1-主要、2-辅助
      this.packagbox.numb = '';
      this.packagbox.name = '';
      this.packagbox.sizer = '';
      this.packagbox.model = '';
      this.packagbox.widthUnit = '';
      //console.log('4.7-13:40',this.packagbox.widthlist);//这里也是有数据的，但是弹窗中的下拉框点击后不展示供选择的部分欸.
      if(diffnum === 1){
        this.maksune = 'main'
      }else{
        this.maksune = 'auxiliary';
      }
    },
    // //增加重量单位
    // addheigunit(){},
    //点击确定按钮打开弹窗
    mudesure(){
      console.log('4.3-10:13',this.list5);
      console.log('4.3-10:14',this.allpack);//这里就没有choid了
      console.log('7.24-1',this.setdiftruck);
      let kedatun = '';
      if(this.setdiftruck === 1){
        if(this.allpack.length === 0){kedatun = true;}else{
          for(let k in this.allpack){
            if(k === 'outLegth' || k === 'outWidth' || k === 'outHeight' || k === 'outerShape' || k === 'unit'){}else{
              if(this.allpack[k].onlypack === '' || this.allpack[k].mainpack.length === 0){kedatun = true;}else{kedatun = false;}
            }
          }
        }
        this.list5.forEach(item => {
          if(item.id){kedatun = false;}
        })
        if(this.list5.length === 1 && kedatun === true){
          ElMessage({
            dangerouslyUseHTMLString: true,
            message: '<div style="text-align:center;">操作失败！<br /><br />点击"确定"时，系统要求最外层的包装需编辑需完！</div>',
            type: 'error'
          })
          for(let s in this.list5){
            if(this.list5.onlypack === ''){
            }else{}
          }
          //this.allpack.mdasune = false;
        }else{
          this.matVisible33 = true;
          this.packagbox.lengthUnitid = 3;
          this.packagbox.widthUnitid = 3;
          this.packagbox.heightUnitid = 3;
          this.addunten = 2;
          //debugger;
          if(this.morebak === 3){this.maksune = 'upent';}else{
            this.maksune = 'takesure';
          }
          //console.log('乙巳年正月十六-申拾肆',this.allpack);
          //this.allpack.mdasune = false;//用于判断是否点击过右上角的确定按钮
          //好怪，主干上这个筛选框的部分是我写的，可为何主干上的效果是怎么写的跟本地就不一样呢
          //this.packagbox.lengthUnitid = 'm';//packagbox中已经有Uintlist了，但好奇怪诶，就是不渲染在页面上。
          //等01.08咨询下侯姐，还有每次修改完vue文件，浏览器中运行的内容都不自动更新，然后控制台就报一个js文件的错误，莫名其妙诶
          //那个js文件我根本没动过诶。
        }
        this.packagbox.outLegth = '';
        this.packagbox.outWidth = '';
        this.packagbox.outHeight = '';
        this.packagbox.outerShape = '';
      }else if(this.setdiftruck === 2){
        if(this.allpack2.length === 0){kedatun = true;}else{
          for(let k in this.allpack2){
            if(k === 'outLegth' || k === 'outWidth' || k === 'outHeight' || k === 'outerShape' || k === 'unit'){}else{
              if(this.allpack2[k].onlypack === '' || this.allpack2[k].mainpack.length === 0){kedatun = true;}else{kedatun = false;}
            }
          }
        }
        this.list5.forEach(item => {
          if(item.id){kedatun = false;}
        })
        if(this.list5.length === 1 && kedatun === true){
          ElMessage({
            dangerouslyUseHTMLString: true,
            message: '<div style="text-align:center;">操作失败！<br /><br />点击"确定"时，系统要求最外层的包装需编辑需完！</div>',
            type: 'error'
          })
          for(let s in this.list5){
            if(this.list5.onlypack === ''){
            }else{}
          }
          //this.allpack.mdasune = false;
        }else{
          this.matVisible33 = true;
          this.packagbox.lengthUnitid = 3;
          this.packagbox.widthUnitid = 3;
          this.packagbox.heightUnitid = 3;
          this.addunten = 2;
          //debugger;
          if(this.morebak === 3){this.maksune = 'upent';}else{
            this.maksune = 'takesure';
          }
          //console.log('乙巳年正月十六-申拾肆',this.allpack);
          //this.allpack.mdasune = false;//用于判断是否点击过右上角的确定按钮
          //好怪，主干上这个筛选框的部分是我写的，可为何主干上的效果是怎么写的跟本地就不一样呢
          //this.packagbox.lengthUnitid = 'm';//packagbox中已经有Uintlist了，但好奇怪诶，就是不渲染在页面上。
          //等01.08咨询下侯姐，还有每次修改完vue文件，浏览器中运行的内容都不自动更新，然后控制台就报一个js文件的错误，莫名其妙诶
          //那个js文件我根本没动过诶。
        }
        this.packagbox.outLegth = '';
        this.packagbox.outWidth = '';
        this.packagbox.outHeight = '';
        this.packagbox.outerShape = '';
      }
      // this.packaging.outLegth = this.packagbox.outLegth;
      // this.packaging.outWidth = this.packagbox.outWidth;
      // this.packaging.outHeight = this.packagbox.outHeight;
      // ElMessage({
      //   dangerouslyUseHTMLString: true,
      //   message: '<div style="text-align: center;">操作失败！<br /><br />点击“确定”时，系统要求最外层的包装需编辑需完！</div>',
      //   type: 'error'
      // })
      //console.log('3.24-7',this.pacallmag);
      //console.log('3.24-8',this.allpack);
    },
    //查看/管理
    lookquen(){
      //debugger;
      //console.log('2.13-11',this.allpack);
      this.matVisible4 = true;
      this.yesorno = 1;
      this.linkfo = 1;
      this.anoter = 1;
      //这里需要获取新增的辅助包装物
      this.list6 = this.packaging.auxiliary;
      const lank6 = this.list6;
      //console.log('7.18-9:57',lank6);
      for(let f in lank6){
        lank6[f].aucoid = lank6[f].aucoid ? lank6[f].aucoid : lank6[f].code;
        lank6[f].auname = lank6[f].auname ? lank6[f].auname : lank6[f].name;
        lank6[f].auspecfication = lank6[f].auspecfication ? lank6[f].auspecfication : lank6[f].specifications;
        lank6[f].aumodel = lank6[f].aumodel ? lank6[f].aumodel : lank6[f].model;
        lank6[f].auonlywei = lank6[f].auonlywei ? lank6[f].auonlywei : lank6[f].weight + lank6[f].weightUnit;
        lank6[f].auweimin = lank6[f].auweimin ? lank6[f].auweimin : lank6[f].weightUnit;
        if(lank6[f].auweimin === 1 || lank6[f].auweimin === '1'){
          lank6[f].auweimin = '毫克(mg)';
        }else if(lank6[f].auweimin === 2 || lank6[f].auweimin === '2'){
          lank6[f].auweimin = '克(g)';
        }else if(lank6[f].auweimin === 3 || lank6[f].auweimin === '3'){
          lank6[f].auweimin = '千克(kg)';
        }else if(lank6[f].auweimin === 4 || lank6[f].auweimin === '4'){
          lank6[f].auweimin = '吨(T)';
        }
        //这里的问题是：auonlywei会出现只有数字或加上了单位的情况。
        //需要判断auonlywei是不是数字
        let kedb = this.isNumeric(lank6[f].auonlywei);
        if(kedb === false){
          lank6[f].auweimin = '';
        }else if(kedb === true){}
      }
      this.list6 = lank6;
      // getpiclist().then(res => {
      //   this.list6 = res.data.data;
      //   let lank6 = this.list6;
      //   for(let f in lank6){
      //     let valuet = lank6[f].unit;
      //     valuet = Number(valuet);
      //     // lank6[f].unitname = this.packagbox.widthlist.find(i => i.id === valuet).value;
      //     let widthlist= this.packagbox.widthlist;
      //     for(let w in widthlist){
      //       widthlist[w].id = Number(widthlist[w].id);
      //       if(widthlist[w].id === valuet){
      //         lank6[f].unitname = widthlist[w].value;
      //       }
      //     }
      //   }
      //   this.list6 = lank6;
      // })
    },
    isNumeric(value) {
      return !isNaN(parseFloat(value)) && isFinite(value);
    },
    numbelook(ntum){
      //debugger;
      this.matVisible4 = true;
      this.yesorno = 0;//控制修改删除按钮是否展示
      this.linkfo = 3;//用于区分辅助包装物数据的展示样式
      //console.log('乙巳年正月廿九-现在this.looker的值是多少',this.looker);
      //debugger;
      if(this.looker === 2){
        this.anoter = 3;//用于区分不同数据在页面渲染时的字段
      }
      const tid = ntum.id;
      //如果allpack中是空数组，那么就需要将ntum中的数据存入allpack中
      //debugger
      //console.log(this.allpack);//是因为一开始的时候allpack是个空[]?
      console.log(ntum);//ntum≠[]
      console.log('7.22-9:02',this.packaging.auxiliary);//[]
      if(this.setdiftruck === 1){
        // if(ntum.itemList?.length === 0){}else{
        //   if(ntum.itemList === undefined){
        //     this.allpack = ntum.auxiliary;
        //   }else{
        //     this.allpack = ntum.itemList;
        //   }
        // }
        if(this.allpack.length === 0 && this.packaging.auxiliary.length === 0){//当点击最外层数据的辅助包装物按钮时，此时allpack中有两条数据
          this.allpack.push(ntum);
          //console.log('乙巳年正月廿九-有数据么？',this.anoter);
          //debugger;
          for(let a in this.allpack){
            //console.log(this.allpack[a].id);
            if(a === "outLegth" || a === "outWidth" || a === "outHeight" || a === "outerShape" || a === "unit"){}else{
              if(this.allpack[a].id === tid){
                this.packaging.auxiliary = this.allpack[a].auxiliary = this.allpack[a].itemList || [];
                if(this.anoter === 3){
                  if(this.aupacks.length === 0){
                    for(let b in this.allpack[a].auxiliary){
                      let bank = this.allpack[a].auxiliary;
                      let json = {
                        aucoid: bank[b].code,auname: bank[b].name,auspecfication:bank[b].specifications,
                        aumodel: bank[b].model,auonlywei: bank[b].weight+bank[b].weightUnit
                      };
                      this.aupacks.push(json);
                    }
                  }else{
                    for(let p in this.allpack[a].auxiliary){
                      let xnk = this.allpack[a].auxiliary;
                      for(let u in this.aupacks){
                        this.aupacks[u].aucoid = xnk[p].code;this.aupacks[u].auname = xnk[p].name;
                        this.aupacks[u].auspecfication = xnk[p].specifications;this.aupacks[u].aumodel = xnk[p].model;
                        this.aupacks[u].auonlywei = xnk[p].weight+xnk[p].weightUnit;
                      }
                    }
                  }
                }else if(this.anoter === 0){
                  for(let p in this.packaging.auxiliary){
                    this.packaging.auxiliary[p].aucoid = this.packaging.auxiliary[p].code;
                    this.packaging.auxiliary[p].auname = this.packaging.auxiliary[p].name;
                    this.packaging.auxiliary[p].auspecfication = this.packaging.auxiliary[p].specifications;
                    this.packaging.auxiliary[p].aumodel = this.packaging.auxiliary[p].model;
                    this.packaging.auxiliary[p].auonlywei = this.packaging.auxiliary[p].weight+this.packaging.auxiliary[p].weightUnit;
                  }
                }
              }
            }
          }
          //console.log('乙巳年正月廿九-this.aupacks',this.aupacks);
        }else{
          //console.log(this.allpack);
          let isEdintx = false;
          //console.log('3.28-1',this.allpack);
          for(let a in this.allpack){
            //console.log(this.allpack[a]);
            if(a === "outLegth" || a === "outWidth" || a === "outHeight" || a === "outerShape" || a === "unit"){}else{
              if(this.allpack[a].id === tid){//tid是按钮所在那行数据的Id
                //console.log(this.allpack[a].id);
                isEdintx = true;
                this.allpack[a].isEdintx = true;
                //就是说，this.packaging的数据是新设置的新数据，而点击旧数据的1种按钮后，因为旧数据也用this.packaging渲染数据
                //this.packaging.auxiliary = ntum.auxiliary;
                // console.log(this.packaging.auxiliary);
                // console.log(this.allpack[a].auxiliary);
                // if(ntum.auxiliary.length === 0){
                //   this.packaging.auxiliary = [];
                // }else{
                //   this.packaging.auxiliary = ntum.auxiliary = this.allpack[a].auxiliary;
                // }
                // if(this.packaging.id === tid){//假设id=1,tid=0，但是点击按钮调用该函数时点击的是id=tid=0的那条数据
                //   //所以说，packaging代表刚设置的那条数据（可能是新增的一条数据），所以用它跟数字按钮所在的那行的数据进行比较是无用的
                //   //因为packaging的数据是单次设置过程中是不会变的。
                //   this.packaging.auxiliary = this.allpack[a].auxiliary;
                // }else{}
                //this.packaging.auxiliary = this.allpack[a].auxiliary;
              }else{
                //isEdintx = false;
                this.allpack[a].isEdintx = false;//还是没看懂，this怎么了？？？？？
              }
            }
            // if(this.allpack[a].id === tid){
            //   this.packaging.auxiliary = this.allpack[a].auxiliary;
            // }
          }
          if(isEdintx === true){
            //debugger;
            var xnk = [];
            for(let c in this.allpack){
              if(c === "unit"){}else{
                if(this.allpack[c].isEdintx === true){//这句话哪里有问题了？明明成功循环完了呀，为何控制台还提示有错呢?
                  //this.aupacks = this.allpack[c].auxiliary;
                  //for (let i = 0; i < newData.length; i++) {
                  xnk = this.allpack[c].auxiliary || this.allpack[c].itemList;//好怪，在这里都还数量对的，怎么到了下面一循环就错了呢？？？？？？？？？？
                  xnk.forEach(itemk => {
                    this.aupacks.push({...itemk});
                  })
                  // for(let p = 0;p<xnk.length;p++){
                  // //for(let p in this.allpack[c].auxiliary){
                  //   // this.items.push({ ...newData[i] });
                  //   this.aupacks.push({ ...xnk[p] });
                  //   //for(let u in this.aupacks){
                  //     //this.items.push({ ...newData[i] });
                  //     // this.aupacks[u].aucoid = xnk[p].code;this.aupacks[u].auname = xnk[p].name;
                  //     // this.aupacks[u].auspecfication = xnk[p].specifications;this.aupacks[u].aumodel = xnk[p].model;
                  //     // this.aupacks[u].auonlywei = xnk[p].weight+xnk[p].weightUnit;
                  //   //}
                  // }
                  for(let u in this.aupacks){
                    if(this.aupacks[u].id === undefined){
                      if(this.aupacks[u].aucoid === undefined){
                        u = Number(u);
                        this.aupacks.splice(u,1);
                      }else{}
                    }else{
                      if(this.aupacks[u].aucoid){
                        this.aupacks[u].code = this.aupacks[u].aucoid;
                      }else{
                        this.aupacks[u].aucoid = this.aupacks[u].code;
                        this.aupacks[u].auname = this.aupacks[u].name;
                        this.aupacks[u].auspecfication = this.aupacks[u].specifications;
                        this.aupacks[u].aumodel = this.aupacks[u].model;
                        this.aupacks[u].auonlywei = this.aupacks[u].weight+this.aupacks[u].weightUnit;
                      }
                    }
                    // if(this.aupacks[u].id === undefined || this.aupacks[u].aucoid === undefined){
                    //   u = Number(u);
                    //   this.aupacks.splice(u,1);
                    // }else{
                    // }
                  }
                  for(let c = 0;c<this.aupacks.length;c++){
                    for(let k = c + 1;k<this.aupacks.length;k++){
                      if(this.aupacks[c].aucoid === this.aupacks[k].aucoid){
                        this.aupacks.splice(k,1);
                        k--;
                      }
                    }
                    //for(let a = 0;a<opertial2.length;a++){
                    //   for(let a1 = a + 1;a1<opertial2.length;a1++){
                    //     if(opertial2[a].id == opertial2[a1].id){
                    //       opertial2.splice(a1,1);
                    //       a1--;
                    //     }
                    //   }
                  }
                  //此时this.aupacks是个数组，包含两条数据，而此时是需要只展示对应的数据的
                  //debugger;
                  //console.log('4.2-8:32',ntum);
                  //console.log('4.2-8:33',this.aupacks);
                  //console.log('4.2-8:34',this.allpack);
                  // if(ntum.auxiliary === undefined || ntum.auxiliary.length === 0){this.detw = ntum.itemList;}else{
                  //   this.detw = ntum.auxiliary;
                  // }
                  if(ntum.itemList === undefined){this.detw = ntum.auxiliary;}else{
                    this.detw = ntum.itemList;
                  }
                  console.log('15:57',ntum);
                  //this.detw = ntum.itemList || ntum.auxiliary;
                  console.log('15:58',this.detw);
                  this.detw.forEach((item,index) => {
                    if(item.weightUnit === '1' || item.weightUnit === 1){item.weightUnit2 = '毫克(mg)';}else if(item.weightUnit === '2' || item.weightUnit === 2){item.weightUnit2 = '克(g)';}
                    else if(item.weightUnit === '3' || item.weightUnit === 3){item.weightUnit2 = '千克(kg)';}else if(item.weightUnit === '4' || item.weightUnit === 4){item.weightUnit2 = '吨(T)';}
                    else{item.weightUnit2 = item.weightUnit;}
                    item.aucoid = item.aucoid === undefined ? item.code : item.aucoid;
                    item.auname = item.auname === undefined ? item.name : item.auname;
                    item.auspecfication = item.auspecfication === undefined ? item.specifications : item.auspecfication;
                    item.aumodel = item.aumodel === undefined ? item.model : item.aumodel;
                    item.auonlywei = item.weight;
                    //Number.isFinite(item.weightUnit);
                    item.auweimin = item.weightUnit2;
                  })
                  //console.log('4.2-8:47',this.detw);
                  //console.log('4.2-8:55',this.looker);
                  //console.log('4.2-8:56',this.anoter);
                  this.anoter = 3;
                  // for(let u in this.detw){
                  //   if(this.detw[u].id === undefined){
                  //     if(this.detw[u].aucoid === undefined){
                  //       u = Number(u);
                  //       this.detw.splice(u,1);
                  //     }else{}
                  //   }else{
                  //     if(this.detw[u].aucoid){}else{
                  //       this.detw[u].aucoid = this.detw[u].code;
                  //       this.detw[u].auname = this.detw[u].name;
                  //       this.detw[u].auspecfication = this.detw[u].specifications;
                  //       this.detw[u].aumodel = this.detw[u].model;
                  //       this.detw[u].auonlywei = this.detw[u].weight+this.detw[u].weightUnit;
                  //     }
                  //   }
                  //   // if(this.aupacks[u].id === undefined || this.aupacks[u].aucoid === undefined){
                  //   //   u = Number(u);
                  //   //   this.aupacks.splice(u,1);
                  //   // }else{
                  //   // }
                  // }
                  // if(this.packaging.id === this.allpack[c].id){
                  //   this.packaging.auxiliary = this.allpack[c].auxiliary;
                  // }
                  //aucoid: "002"
                  // aumodel: ""
                  // auname: ""
                  // auonlywei: 0
                  // auspecfication: ""
                  // auweimin: ""
                  // wrappage: 2163
                }else{}
              }
            }
          }else if(isEdintx === false){
            //console.log('4.1-11:22',this.looker);
            //console.log('4.1-11:27',ntum);
            if(this.looker === 2){
              if(this.linkfo === 3){
                this.aupacks = ntum.itemList;
                for(let u in this.aupacks){
                  if(this.aupacks[u].aucoid){}else{
                    this.aupacks[u].aucoid = this.aupacks[u].code;
                    this.aupacks[u].auname = this.aupacks[u].name;
                    this.aupacks[u].auspecfication = this.aupacks[u].specifications;
                    this.aupacks[u].aumodel = this.aupacks[u].model;
                    this.aupacks[u].auonlywei = this.aupacks[u].weight+this.aupacks[u].weightUnit;
                  }
                }
                // if(ntum.auxiliary === undefined || ntum.auxiliary.length === 0){this.detw = ntum.itemList;}else{
                //   this.detw = ntum.auxiliary;
                // }
                if(ntum.itemList === undefined){this.detw = ntum.auxiliary;}else{
                  this.detw = ntum.itemList;
                }
                //this.detw = ntum.itemList || ntum.auxiliary;
                this.detw.forEach((item,index) => {
                  if(item.weightUnit === '1' || item.weightUnit === 1){item.weightUnit2 = '毫克(mg)';}else if(item.weightUnit === '2' || item.weightUnit === 2){item.weightUnit2 = '克(g)';}
                  else if(item.weightUnit === '3' || item.weightUnit === 3){item.weightUnit2 = '千克(kg)';}else if(item.weightUnit === '4' || item.weightUnit === 4){item.weightUnit2 = '吨(T)';}
                  else{item.weightUnit2 = item.weightUnit;}
                  item.aucoid = item.aucoid === undefined ? item.code : item.aucoid;
                  item.auname = item.auname === undefined ? item.name : item.auname;
                  item.auspecfication = item.auspecfication === undefined ? item.specifications : item.auspecfication;
                  item.aumodel = item.aumodel === undefined ? item.model : item.aumodel;
                  item.auonlywei = item.weight;
                  //Number.isFinite(item.weightUnit);
                  item.auweimin = item.weightUnit2;
                })
                //console.log('7.18-9:38',this.detw);
              }
            }
            //console.log('4.1-11:34',this.aupacks);
          }
        }
      }else if(this.setdiftruck === 2){
        // if(ntum.itemList?.length === 0){}else{
        //   if(ntum.itemList === undefined){
        //     this.allpack2 = ntum.auxiliary;
        //   }else{
        //     this.allpack2 = ntum.itemList;
        //   }
        // }
        if(this.allpack2?.length === 0 && this.packaging.auxiliary.length === 0){//当点击最外层数据的辅助包装物按钮时，此时allpack中有两条数据
          this.allpack2.push(ntum);
          //console.log('乙巳年正月廿九-有数据么？',this.anoter);
          //debugger;
          for(let a in this.allpack2){
            //console.log(this.allpack[a].id);
            if(a === "outLegth" || a === "outWidth" || a === "outHeight" || a === "outerShape" || a === "unit"){}else{
              if(this.allpack2[a].id === tid){
                this.packaging.auxiliary = this.allpack2[a].auxiliary = this.allpack2[a].itemList || [];
                if(this.anoter === 3){
                  if(this.aupacks.length === 0){
                    for(let b in this.allpack2[a].auxiliary){
                      let bank = this.allpack2[a].auxiliary;
                      let json = {
                        aucoid: bank[b].code,auname: bank[b].name,auspecfication:bank[b].specifications,
                        aumodel: bank[b].model,auonlywei: bank[b].weight+bank[b].weightUnit
                      };
                      this.aupacks.push(json);
                    }
                  }else{
                    for(let p in this.allpack2[a].auxiliary){
                      let xnk = this.allpack2[a].auxiliary;
                      for(let u in this.aupacks){
                        this.aupacks[u].aucoid = xnk[p].code;this.aupacks[u].auname = xnk[p].name;
                        this.aupacks[u].auspecfication = xnk[p].specifications;this.aupacks[u].aumodel = xnk[p].model;
                        this.aupacks[u].auonlywei = xnk[p].weight+xnk[p].weightUnit;
                      }
                    }
                  }
                }else if(this.anoter === 0){
                  for(let p in this.packaging.auxiliary){
                    this.packaging.auxiliary[p].aucoid = this.packaging.auxiliary[p].code;
                    this.packaging.auxiliary[p].auname = this.packaging.auxiliary[p].name;
                    this.packaging.auxiliary[p].auspecfication = this.packaging.auxiliary[p].specifications;
                    this.packaging.auxiliary[p].aumodel = this.packaging.auxiliary[p].model;
                    this.packaging.auxiliary[p].auonlywei = this.packaging.auxiliary[p].weight+this.packaging.auxiliary[p].weightUnit;
                  }
                }
              }
            }
          }
          //console.log('乙巳年正月廿九-this.aupacks',this.aupacks);
        }else{
          //console.log(this.allpack);
          let isEdintx = false;
          console.log('3.28-1',this.allpack2);
          for(let a in this.allpack2){
            //console.log(this.allpack[a]);
            if(a === "outLegth" || a === "outWidth" || a === "outHeight" || a === "outerShape" || a === "unit"){}else{
              if(this.allpack2[a].id === tid){//tid是按钮所在那行数据的Id
                //console.log(this.allpack[a].id);
                isEdintx = true;
                this.allpack2[a].isEdintx = true;
                //就是说，this.packaging的数据是新设置的新数据，而点击旧数据的1种按钮后，因为旧数据也用this.packaging渲染数据
                //this.packaging.auxiliary = ntum.auxiliary;
                // console.log(this.packaging.auxiliary);
                // console.log(this.allpack[a].auxiliary);
                // if(ntum.auxiliary.length === 0){
                //   this.packaging.auxiliary = [];
                // }else{
                //   this.packaging.auxiliary = ntum.auxiliary = this.allpack[a].auxiliary;
                // }
                // if(this.packaging.id === tid){//假设id=1,tid=0，但是点击按钮调用该函数时点击的是id=tid=0的那条数据
                //   //所以说，packaging代表刚设置的那条数据（可能是新增的一条数据），所以用它跟数字按钮所在的那行的数据进行比较是无用的
                //   //因为packaging的数据是单次设置过程中是不会变的。
                //   this.packaging.auxiliary = this.allpack[a].auxiliary;
                // }else{}
                //this.packaging.auxiliary = this.allpack[a].auxiliary;
              }else{
                //isEdintx = false;
                this.allpack2[a].isEdintx = false;//还是没看懂，this怎么了？？？？？
              }
            }
            // if(this.allpack[a].id === tid){
            //   this.packaging.auxiliary = this.allpack[a].auxiliary;
            // }
          }
          if(isEdintx === true){
            //debugger;
            var xnk = [];
            for(let c in this.allpack2){
              if(c === "unit"){}else{
                if(this.allpack2[c].isEdintx === true){//这句话哪里有问题了？明明成功循环完了呀，为何控制台还提示有错呢?
                  //this.aupacks = this.allpack[c].auxiliary;
                  //for (let i = 0; i < newData.length; i++) {
                  console.log('7.24疑惑',this.allpack2);
                  xnk = this.allpack2[c].itemList || this.allpack2[c].auxiliary;//好怪，在这里都还数量对的，怎么到了下面一循环就错了呢？？？？？？？？？？
                  xnk.forEach(itemk => {
                    this.aupacks.push({...itemk});
                  })
                  // for(let p = 0;p<xnk.length;p++){
                  // //for(let p in this.allpack[c].auxiliary){
                  //   // this.items.push({ ...newData[i] });
                  //   this.aupacks.push({ ...xnk[p] });
                  //   //for(let u in this.aupacks){
                  //     //this.items.push({ ...newData[i] });
                  //     // this.aupacks[u].aucoid = xnk[p].code;this.aupacks[u].auname = xnk[p].name;
                  //     // this.aupacks[u].auspecfication = xnk[p].specifications;this.aupacks[u].aumodel = xnk[p].model;
                  //     // this.aupacks[u].auonlywei = xnk[p].weight+xnk[p].weightUnit;
                  //   //}
                  // }
                  for(let u in this.aupacks){
                    if(this.aupacks[u].id === undefined){
                      if(this.aupacks[u].aucoid === undefined){
                        u = Number(u);
                        this.aupacks.splice(u,1);
                      }else{}
                    }else{
                      if(this.aupacks[u].aucoid){
                        this.aupacks[u].code = this.aupacks[u].aucoid;
                      }else{
                        this.aupacks[u].aucoid = this.aupacks[u].code;
                        this.aupacks[u].auname = this.aupacks[u].name;
                        this.aupacks[u].auspecfication = this.aupacks[u].specifications;
                        this.aupacks[u].aumodel = this.aupacks[u].model;
                        this.aupacks[u].auonlywei = this.aupacks[u].weight+this.aupacks[u].weightUnit;
                      }
                    }
                    // if(this.aupacks[u].id === undefined || this.aupacks[u].aucoid === undefined){
                    //   u = Number(u);
                    //   this.aupacks.splice(u,1);
                    // }else{
                    // }
                  }
                  for(let c = 0;c<this.aupacks.length;c++){
                    for(let k = c + 1;k<this.aupacks.length;k++){
                      if(this.aupacks[c].aucoid === this.aupacks[k].aucoid){
                        this.aupacks.splice(k,1);
                        k--;
                      }
                    }
                    //for(let a = 0;a<opertial2.length;a++){
                    //   for(let a1 = a + 1;a1<opertial2.length;a1++){
                    //     if(opertial2[a].id == opertial2[a1].id){
                    //       opertial2.splice(a1,1);
                    //       a1--;
                    //     }
                    //   }
                  }
                  //此时this.aupacks是个数组，包含两条数据，而此时是需要只展示对应的数据的
                  //debugger;
                  //console.log('4.2-8:32',ntum);
                  //console.log('4.2-8:33',this.aupacks);
                  //console.log('4.2-8:34',this.allpack);
                  if(ntum.itemList === undefined){this.detw = ntum.auxiliary;}else{
                    this.detw = ntum.itemList;
                  }
                  // if(ntum.auxiliary === undefined || ntum.auxiliary.length === 0){this.detw = ntum.itemList;}else{
                  //   this.detw = ntum.auxiliary;
                  // }
                  console.log('15:57',ntum);
                  //this.detw = ntum.itemList || ntum.auxiliary;
                  console.log('15:58',this.detw);
                  this.detw.forEach((item,index) => {
                    if(item.weightUnit === '1' || item.weightUnit === 1){item.weightUnit2 = '毫克(mg)';}else if(item.weightUnit === '2' || item.weightUnit === 2){item.weightUnit2 = '克(g)';}
                    else if(item.weightUnit === '3' || item.weightUnit === 3){item.weightUnit2 = '千克(kg)';}else if(item.weightUnit === '4' || item.weightUnit === 4){item.weightUnit2 = '吨(T)';}
                    else{item.weightUnit2 = item.weightUnit;}
                    item.aucoid = item.aucoid === undefined ? item.code : item.aucoid;
                    item.auname = item.auname === undefined ? item.name : item.auname;
                    item.auspecfication = item.auspecfication === undefined ? item.specifications : item.auspecfication;
                    item.aumodel = item.aumodel === undefined ? item.model : item.aumodel;
                    item.auonlywei = item.weight;
                    //Number.isFinite(item.weightUnit);
                    item.auweimin = item.weightUnit2;
                  })
                  //console.log('4.2-8:47',this.detw);
                  //console.log('4.2-8:55',this.looker);
                  //console.log('4.2-8:56',this.anoter);
                  this.anoter = 3;
                  // for(let u in this.detw){
                  //   if(this.detw[u].id === undefined){
                  //     if(this.detw[u].aucoid === undefined){
                  //       u = Number(u);
                  //       this.detw.splice(u,1);
                  //     }else{}
                  //   }else{
                  //     if(this.detw[u].aucoid){}else{
                  //       this.detw[u].aucoid = this.detw[u].code;
                  //       this.detw[u].auname = this.detw[u].name;
                  //       this.detw[u].auspecfication = this.detw[u].specifications;
                  //       this.detw[u].aumodel = this.detw[u].model;
                  //       this.detw[u].auonlywei = this.detw[u].weight+this.detw[u].weightUnit;
                  //     }
                  //   }
                  //   // if(this.aupacks[u].id === undefined || this.aupacks[u].aucoid === undefined){
                  //   //   u = Number(u);
                  //   //   this.aupacks.splice(u,1);
                  //   // }else{
                  //   // }
                  // }
                  // if(this.packaging.id === this.allpack[c].id){
                  //   this.packaging.auxiliary = this.allpack[c].auxiliary;
                  // }
                  //aucoid: "002"
                  // aumodel: ""
                  // auname: ""
                  // auonlywei: 0
                  // auspecfication: ""
                  // auweimin: ""
                  // wrappage: 2163
                }else{}
              }
            }
          }else if(isEdintx === false){
            //console.log('4.1-11:22',this.looker);
            //console.log('4.1-11:27',ntum);
            if(this.looker === 2){
              if(this.linkfo === 3){
                this.aupacks = ntum.itemList;
                for(let u in this.aupacks){
                  if(this.aupacks[u].aucoid){}else{
                    this.aupacks[u].aucoid = this.aupacks[u].code;
                    this.aupacks[u].auname = this.aupacks[u].name;
                    this.aupacks[u].auspecfication = this.aupacks[u].specifications;
                    this.aupacks[u].aumodel = this.aupacks[u].model;
                    this.aupacks[u].auonlywei = this.aupacks[u].weight+this.aupacks[u].weightUnit;
                  }
                }
                // if(ntum.auxiliary === undefined || ntum.auxiliary.length === 0){this.detw = ntum.itemList;}else{
                //   this.detw = ntum.auxiliary;
                // }
                if(ntum.itemList === undefined){this.detw = ntum.auxiliary;}else{
                  this.detw = ntum.itemList;
                }
                //this.detw = ntum.itemList || ntum.auxiliary;
                this.detw.forEach((item,index) => {
                  if(item.weightUnit === '1' || item.weightUnit === 1){item.weightUnit2 = '毫克(mg)';}else if(item.weightUnit === '2' || item.weightUnit === 2){item.weightUnit2 = '克(g)';}
                  else if(item.weightUnit === '3' || item.weightUnit === 3){item.weightUnit2 = '千克(kg)';}else if(item.weightUnit === '4' || item.weightUnit === 4){item.weightUnit2 = '吨(T)';}
                  else{item.weightUnit2 = item.weightUnit;}
                  item.aucoid = item.aucoid === undefined ? item.code : item.aucoid;
                  item.auname = item.auname === undefined ? item.name : item.auname;
                  item.auspecfication = item.auspecfication === undefined ? item.specifications : item.auspecfication;
                  item.aumodel = item.aumodel === undefined ? item.model : item.aumodel;
                  item.auonlywei = item.weight;
                  //Number.isFinite(item.weightUnit);
                  item.auweimin = item.weightUnit2;
                })
                //console.log('7.18-9:38',this.detw);
              }
            }
            //console.log('4.1-11:34',this.aupacks);
          }
        }
      }
      if(this.setdiftruck === 1){
        if(this.allpack?.length === 0 && this.packaging.auxiliary.length === 0){//当点击最外层数据的辅助包装物按钮时，此时allpack中有两条数据
          this.allpack.push(ntum);
          //console.log('乙巳年正月廿九-有数据么？',this.anoter);
          //debugger;
          for(let a in this.allpack){
            //console.log(this.allpack[a].id);
            if(a === "outLegth" || a === "outWidth" || a === "outHeight" || a === "outerShape" || a === "unit"){}else{
              if(this.allpack[a].id === tid){
                this.packaging.auxiliary = this.allpack[a].auxiliary = this.allpack[a].itemList || [];
                if(this.anoter === 3){
                  if(this.aupacks.length === 0){
                    for(let b in this.allpack[a].auxiliary){
                      let bank = this.allpack[a].auxiliary;
                      let json = {
                        aucoid: bank[b].code,auname: bank[b].name,auspecfication:bank[b].specifications,
                        aumodel: bank[b].model,auonlywei: bank[b].weight+bank[b].weightUnit
                      };
                      this.aupacks.push(json);
                    }
                  }else{
                    for(let p in this.allpack[a].auxiliary){
                      let xnk = this.allpack[a].auxiliary;
                      for(let u in this.aupacks){
                        this.aupacks[u].aucoid = xnk[p].code;this.aupacks[u].auname = xnk[p].name;
                        this.aupacks[u].auspecfication = xnk[p].specifications;this.aupacks[u].aumodel = xnk[p].model;
                        this.aupacks[u].auonlywei = xnk[p].weight+xnk[p].weightUnit;
                      }
                    }
                  }
                }else if(this.anoter === 0){
                  for(let p in this.packaging.auxiliary){
                    this.packaging.auxiliary[p].aucoid = this.packaging.auxiliary[p].code;
                    this.packaging.auxiliary[p].auname = this.packaging.auxiliary[p].name;
                    this.packaging.auxiliary[p].auspecfication = this.packaging.auxiliary[p].specifications;
                    this.packaging.auxiliary[p].aumodel = this.packaging.auxiliary[p].model;
                    this.packaging.auxiliary[p].auonlywei = this.packaging.auxiliary[p].weight+this.packaging.auxiliary[p].weightUnit;
                  }
                }
              }
            }
          }
          //console.log('乙巳年正月廿九-this.aupacks',this.aupacks);
        }else{
          //console.log(this.allpack);
          let isEdintx = false;
          //console.log('3.28-1',this.allpack);
          for(let a in this.allpack){
            //console.log(this.allpack[a]);
            if(a === "outLegth" || a === "outWidth" || a === "outHeight" || a === "outerShape" || a === "unit"){}else{
              if(this.allpack[a].id === tid){//tid是按钮所在那行数据的Id
                //console.log(this.allpack[a].id);
                isEdintx = true;
                this.allpack[a].isEdintx = true;
                //就是说，this.packaging的数据是新设置的新数据，而点击旧数据的1种按钮后，因为旧数据也用this.packaging渲染数据
                //this.packaging.auxiliary = ntum.auxiliary;
                // console.log(this.packaging.auxiliary);
                // console.log(this.allpack[a].auxiliary);
                // if(ntum.auxiliary.length === 0){
                //   this.packaging.auxiliary = [];
                // }else{
                //   this.packaging.auxiliary = ntum.auxiliary = this.allpack[a].auxiliary;
                // }
                // if(this.packaging.id === tid){//假设id=1,tid=0，但是点击按钮调用该函数时点击的是id=tid=0的那条数据
                //   //所以说，packaging代表刚设置的那条数据（可能是新增的一条数据），所以用它跟数字按钮所在的那行的数据进行比较是无用的
                //   //因为packaging的数据是单次设置过程中是不会变的。
                //   this.packaging.auxiliary = this.allpack[a].auxiliary;
                // }else{}
                //this.packaging.auxiliary = this.allpack[a].auxiliary;
              }else{
                //isEdintx = false;
                this.allpack[a].isEdintx = false;//还是没看懂，this怎么了？？？？？
              }
            }
            // if(this.allpack[a].id === tid){
            //   this.packaging.auxiliary = this.allpack[a].auxiliary;
            // }
          }
          if(isEdintx === true){
            //debugger;
            var xnk = [];
            for(let c in this.allpack){
              if(c === "unit"){}else{
                if(this.allpack[c].isEdintx === true){//这句话哪里有问题了？明明成功循环完了呀，为何控制台还提示有错呢?
                  //this.aupacks = this.allpack[c].auxiliary;
                  //for (let i = 0; i < newData.length; i++) {
                  xnk = this.allpack[c].auxiliary || this.allpack[c].itemList;//好怪，在这里都还数量对的，怎么到了下面一循环就错了呢？？？？？？？？？？
                  xnk.forEach(itemk => {
                    this.aupacks.push({...itemk});
                  })
                  // for(let p = 0;p<xnk.length;p++){
                  // //for(let p in this.allpack[c].auxiliary){
                  //   // this.items.push({ ...newData[i] });
                  //   this.aupacks.push({ ...xnk[p] });
                  //   //for(let u in this.aupacks){
                  //     //this.items.push({ ...newData[i] });
                  //     // this.aupacks[u].aucoid = xnk[p].code;this.aupacks[u].auname = xnk[p].name;
                  //     // this.aupacks[u].auspecfication = xnk[p].specifications;this.aupacks[u].aumodel = xnk[p].model;
                  //     // this.aupacks[u].auonlywei = xnk[p].weight+xnk[p].weightUnit;
                  //   //}
                  // }
                  for(let u in this.aupacks){
                    if(this.aupacks[u].id === undefined){
                      if(this.aupacks[u].aucoid === undefined){
                        u = Number(u);
                        this.aupacks.splice(u,1);
                      }else{}
                    }else{
                      if(this.aupacks[u].aucoid){
                        this.aupacks[u].code = this.aupacks[u].aucoid;
                      }else{
                        this.aupacks[u].aucoid = this.aupacks[u].code;
                        this.aupacks[u].auname = this.aupacks[u].name;
                        this.aupacks[u].auspecfication = this.aupacks[u].specifications;
                        this.aupacks[u].aumodel = this.aupacks[u].model;
                        this.aupacks[u].auonlywei = this.aupacks[u].weight+this.aupacks[u].weightUnit;
                      }
                    }
                    // if(this.aupacks[u].id === undefined || this.aupacks[u].aucoid === undefined){
                    //   u = Number(u);
                    //   this.aupacks.splice(u,1);
                    // }else{
                    // }
                  }
                  for(let c = 0;c<this.aupacks.length;c++){
                    for(let k = c + 1;k<this.aupacks.length;k++){
                      if(this.aupacks[c].aucoid === this.aupacks[k].aucoid){
                        this.aupacks.splice(k,1);
                        k--;
                      }
                    }
                    //for(let a = 0;a<opertial2.length;a++){
                    //   for(let a1 = a + 1;a1<opertial2.length;a1++){
                    //     if(opertial2[a].id == opertial2[a1].id){
                    //       opertial2.splice(a1,1);
                    //       a1--;
                    //     }
                    //   }
                  }
                  //此时this.aupacks是个数组，包含两条数据，而此时是需要只展示对应的数据的
                  //debugger;
                  //console.log('4.2-8:32',ntum);
                  //console.log('4.2-8:33',this.aupacks);
                  //console.log('4.2-8:34',this.allpack);
                  // if(ntum.auxiliary === undefined || ntum.auxiliary.length === 0){this.detw = ntum.itemList;}else{
                  //   this.detw = ntum.auxiliary;
                  // }
                  if(ntum.itemList === undefined){this.detw = ntum.auxiliary;}else{
                    this.detw = ntum.itemList;
                  }
                  console.log('15:57',ntum);
                  //this.detw = ntum.itemList || ntum.auxiliary;
                  console.log('15:58',this.detw);
                  this.detw.forEach((item,index) => {
                    if(item.weightUnit === '1' || item.weightUnit === 1){item.weightUnit2 = '毫克(mg)';}else if(item.weightUnit === '2' || item.weightUnit === 2){item.weightUnit2 = '克(g)';}
                    else if(item.weightUnit === '3' || item.weightUnit === 3){item.weightUnit2 = '千克(kg)';}else if(item.weightUnit === '4' || item.weightUnit === 4){item.weightUnit2 = '吨(T)';}
                    else{item.weightUnit2 = item.weightUnit;}
                    item.aucoid = item.aucoid === undefined ? item.code : item.aucoid;
                    item.auname = item.auname === undefined ? item.name : item.auname;
                    item.auspecfication = item.auspecfication === undefined ? item.specifications : item.auspecfication;
                    item.aumodel = item.aumodel === undefined ? item.model : item.aumodel;
                    item.auonlywei = item.weight;
                    //Number.isFinite(item.weightUnit);
                    item.auweimin = item.weightUnit2;
                  })
                  //console.log('4.2-8:47',this.detw);
                  //console.log('4.2-8:55',this.looker);
                  //console.log('4.2-8:56',this.anoter);
                  this.anoter = 3;
                  // for(let u in this.detw){
                  //   if(this.detw[u].id === undefined){
                  //     if(this.detw[u].aucoid === undefined){
                  //       u = Number(u);
                  //       this.detw.splice(u,1);
                  //     }else{}
                  //   }else{
                  //     if(this.detw[u].aucoid){}else{
                  //       this.detw[u].aucoid = this.detw[u].code;
                  //       this.detw[u].auname = this.detw[u].name;
                  //       this.detw[u].auspecfication = this.detw[u].specifications;
                  //       this.detw[u].aumodel = this.detw[u].model;
                  //       this.detw[u].auonlywei = this.detw[u].weight+this.detw[u].weightUnit;
                  //     }
                  //   }
                  //   // if(this.aupacks[u].id === undefined || this.aupacks[u].aucoid === undefined){
                  //   //   u = Number(u);
                  //   //   this.aupacks.splice(u,1);
                  //   // }else{
                  //   // }
                  // }
                  // if(this.packaging.id === this.allpack[c].id){
                  //   this.packaging.auxiliary = this.allpack[c].auxiliary;
                  // }
                  //aucoid: "002"
                  // aumodel: ""
                  // auname: ""
                  // auonlywei: 0
                  // auspecfication: ""
                  // auweimin: ""
                  // wrappage: 2163
                }else{}
              }
            }
          }else if(isEdintx === false){
            //console.log('4.1-11:22',this.looker);
            //console.log('4.1-11:27',ntum);
            if(this.looker === 2){
              if(this.linkfo === 3){
                this.aupacks = ntum.itemList;
                for(let u in this.aupacks){
                  if(this.aupacks[u].aucoid){}else{
                    this.aupacks[u].aucoid = this.aupacks[u].code;
                    this.aupacks[u].auname = this.aupacks[u].name;
                    this.aupacks[u].auspecfication = this.aupacks[u].specifications;
                    this.aupacks[u].aumodel = this.aupacks[u].model;
                    this.aupacks[u].auonlywei = this.aupacks[u].weight+this.aupacks[u].weightUnit;
                  }
                }
                // if(ntum.auxiliary === undefined || ntum.auxiliary.length === 0){this.detw = ntum.itemList;}else{
                //   this.detw = ntum.auxiliary;
                // }
                if(ntum.itemList === undefined){this.detw = ntum.auxiliary;}else{
                  this.detw = ntum.itemList;
                }
                //this.detw = ntum.itemList || ntum.auxiliary;
                this.detw.forEach((item,index) => {
                  if(item.weightUnit === '1' || item.weightUnit === 1){item.weightUnit2 = '毫克(mg)';}else if(item.weightUnit === '2' || item.weightUnit === 2){item.weightUnit2 = '克(g)';}
                  else if(item.weightUnit === '3' || item.weightUnit === 3){item.weightUnit2 = '千克(kg)';}else if(item.weightUnit === '4' || item.weightUnit === 4){item.weightUnit2 = '吨(T)';}
                  else{item.weightUnit2 = item.weightUnit;}
                  item.aucoid = item.aucoid === undefined ? item.code : item.aucoid;
                  item.auname = item.auname === undefined ? item.name : item.auname;
                  item.auspecfication = item.auspecfication === undefined ? item.specifications : item.auspecfication;
                  item.aumodel = item.aumodel === undefined ? item.model : item.aumodel;
                  item.auonlywei = item.weight;
                  //Number.isFinite(item.weightUnit);
                  item.auweimin = item.weightUnit2;
                })
                //console.log('7.18-9:38',this.detw);
              }
            }
            //console.log('4.1-11:34',this.aupacks);
          }
        }
      }else if(this.setdiftruck === 2){
        if(this.allpack2?.length === 0 && this.packaging.auxiliary.length === 0){//当点击最外层数据的辅助包装物按钮时，此时allpack中有两条数据
          this.allpack2.push(ntum);
          //console.log('乙巳年正月廿九-有数据么？',this.anoter);
          //debugger;
          for(let a in this.allpack2){
            //console.log(this.allpack[a].id);
            if(a === "outLegth" || a === "outWidth" || a === "outHeight" || a === "outerShape" || a === "unit"){}else{
              if(this.allpack2[a].id === tid){
                this.packaging.auxiliary = this.allpack2[a].auxiliary = this.allpack2[a].itemList || [];
                if(this.anoter === 3){
                  if(this.aupacks.length === 0){
                    for(let b in this.allpack2[a].auxiliary){
                      let bank = this.allpack2[a].auxiliary;
                      let json = {
                        aucoid: bank[b].code,auname: bank[b].name,auspecfication:bank[b].specifications,
                        aumodel: bank[b].model,auonlywei: bank[b].weight+bank[b].weightUnit
                      };
                      this.aupacks.push(json);
                    }
                  }else{
                    for(let p in this.allpack2[a].auxiliary){
                      let xnk = this.allpack2[a].auxiliary;
                      for(let u in this.aupacks){
                        this.aupacks[u].aucoid = xnk[p].code;this.aupacks[u].auname = xnk[p].name;
                        this.aupacks[u].auspecfication = xnk[p].specifications;this.aupacks[u].aumodel = xnk[p].model;
                        this.aupacks[u].auonlywei = xnk[p].weight+xnk[p].weightUnit;
                      }
                    }
                  }
                }else if(this.anoter === 0){
                  for(let p in this.packaging.auxiliary){
                    this.packaging.auxiliary[p].aucoid = this.packaging.auxiliary[p].code;
                    this.packaging.auxiliary[p].auname = this.packaging.auxiliary[p].name;
                    this.packaging.auxiliary[p].auspecfication = this.packaging.auxiliary[p].specifications;
                    this.packaging.auxiliary[p].aumodel = this.packaging.auxiliary[p].model;
                    this.packaging.auxiliary[p].auonlywei = this.packaging.auxiliary[p].weight+this.packaging.auxiliary[p].weightUnit;
                  }
                }
              }
            }
          }
          //console.log('乙巳年正月廿九-this.aupacks',this.aupacks);
        }else{
          //console.log(this.allpack);
          let isEdintx = false;
          //console.log('3.28-1',this.allpack);
          for(let a in this.allpack2){
            //console.log(this.allpack[a]);
            if(a === "outLegth" || a === "outWidth" || a === "outHeight" || a === "outerShape" || a === "unit"){}else{
              if(this.allpack2[a].id === tid){//tid是按钮所在那行数据的Id
                //console.log(this.allpack[a].id);
                isEdintx = true;
                this.allpack2[a].isEdintx = true;
                //就是说，this.packaging的数据是新设置的新数据，而点击旧数据的1种按钮后，因为旧数据也用this.packaging渲染数据
                //this.packaging.auxiliary = ntum.auxiliary;
                // console.log(this.packaging.auxiliary);
                // console.log(this.allpack[a].auxiliary);
                // if(ntum.auxiliary.length === 0){
                //   this.packaging.auxiliary = [];
                // }else{
                //   this.packaging.auxiliary = ntum.auxiliary = this.allpack[a].auxiliary;
                // }
                // if(this.packaging.id === tid){//假设id=1,tid=0，但是点击按钮调用该函数时点击的是id=tid=0的那条数据
                //   //所以说，packaging代表刚设置的那条数据（可能是新增的一条数据），所以用它跟数字按钮所在的那行的数据进行比较是无用的
                //   //因为packaging的数据是单次设置过程中是不会变的。
                //   this.packaging.auxiliary = this.allpack[a].auxiliary;
                // }else{}
                //this.packaging.auxiliary = this.allpack[a].auxiliary;
              }else{
                //isEdintx = false;
                this.allpack2[a].isEdintx = false;//还是没看懂，this怎么了？？？？？
              }
            }
            // if(this.allpack[a].id === tid){
            //   this.packaging.auxiliary = this.allpack[a].auxiliary;
            // }
          }
          if(isEdintx === true){
            //debugger;
            var xnk = [];
            for(let c in this.allpack2){
              if(c === "unit"){}else{
                if(this.allpack2[c].isEdintx === true){//这句话哪里有问题了？明明成功循环完了呀，为何控制台还提示有错呢?
                  //this.aupacks = this.allpack[c].auxiliary;
                  //for (let i = 0; i < newData.length; i++) {
                  xnk = this.allpack2[c].itemList || this.allpack2[c].auxiliary;//好怪，在这里都还数量对的，怎么到了下面一循环就错了呢？？？？？？？？？？
                  xnk.forEach(itemk => {
                    this.aupacks.push({...itemk});
                  })
                  // for(let p = 0;p<xnk.length;p++){
                  // //for(let p in this.allpack[c].auxiliary){
                  //   // this.items.push({ ...newData[i] });
                  //   this.aupacks.push({ ...xnk[p] });
                  //   //for(let u in this.aupacks){
                  //     //this.items.push({ ...newData[i] });
                  //     // this.aupacks[u].aucoid = xnk[p].code;this.aupacks[u].auname = xnk[p].name;
                  //     // this.aupacks[u].auspecfication = xnk[p].specifications;this.aupacks[u].aumodel = xnk[p].model;
                  //     // this.aupacks[u].auonlywei = xnk[p].weight+xnk[p].weightUnit;
                  //   //}
                  // }
                  for(let u in this.aupacks){
                    if(this.aupacks[u].id === undefined){
                      if(this.aupacks[u].aucoid === undefined){
                        u = Number(u);
                        this.aupacks.splice(u,1);
                      }else{}
                    }else{
                      if(this.aupacks[u].aucoid){
                        this.aupacks[u].code = this.aupacks[u].aucoid;
                      }else{
                        this.aupacks[u].aucoid = this.aupacks[u].code;
                        this.aupacks[u].auname = this.aupacks[u].name;
                        this.aupacks[u].auspecfication = this.aupacks[u].specifications;
                        this.aupacks[u].aumodel = this.aupacks[u].model;
                        this.aupacks[u].auonlywei = this.aupacks[u].weight+this.aupacks[u].weightUnit;
                      }
                    }
                    // if(this.aupacks[u].id === undefined || this.aupacks[u].aucoid === undefined){
                    //   u = Number(u);
                    //   this.aupacks.splice(u,1);
                    // }else{
                    // }
                  }
                  for(let c = 0;c<this.aupacks.length;c++){
                    for(let k = c + 1;k<this.aupacks.length;k++){
                      if(this.aupacks[c].aucoid === this.aupacks[k].aucoid){
                        this.aupacks.splice(k,1);
                        k--;
                      }
                    }
                    //for(let a = 0;a<opertial2.length;a++){
                    //   for(let a1 = a + 1;a1<opertial2.length;a1++){
                    //     if(opertial2[a].id == opertial2[a1].id){
                    //       opertial2.splice(a1,1);
                    //       a1--;
                    //     }
                    //   }
                  }
                  //此时this.aupacks是个数组，包含两条数据，而此时是需要只展示对应的数据的
                  //debugger;
                  //console.log('4.2-8:32',ntum);
                  //console.log('4.2-8:33',this.aupacks);
                  //console.log('4.2-8:34',this.allpack);
                  // if(ntum.auxiliary === undefined || ntum.auxiliary.length === 0){this.detw = ntum.itemList;}else{
                  //   this.detw = ntum.auxiliary;
                  // }
                  if(ntum.itemList === undefined){this.detw = ntum.auxiliary;}else{
                    this.detw = ntum.itemList;
                  }
                  console.log('15:57',ntum);
                  //this.detw = ntum.itemList || ntum.auxiliary;
                  console.log('15:58',this.detw);
                  this.detw.forEach((item,index) => {
                    if(item.weightUnit === '1' || item.weightUnit === 1){item.weightUnit2 = '毫克(mg)';}else if(item.weightUnit === '2' || item.weightUnit === 2){item.weightUnit2 = '克(g)';}
                    else if(item.weightUnit === '3' || item.weightUnit === 3){item.weightUnit2 = '千克(kg)';}else if(item.weightUnit === '4' || item.weightUnit === 4){item.weightUnit2 = '吨(T)';}
                    else{item.weightUnit2 = item.weightUnit;}
                    item.aucoid = item.aucoid === undefined ? item.code : item.aucoid;
                    item.auname = item.auname === undefined ? item.name : item.auname;
                    item.auspecfication = item.auspecfication === undefined ? item.specifications : item.auspecfication;
                    item.aumodel = item.aumodel === undefined ? item.model : item.aumodel;
                    item.auonlywei = item.weight;
                    //Number.isFinite(item.weightUnit);
                    item.auweimin = item.weightUnit2;
                  })
                  //console.log('4.2-8:47',this.detw);
                  //console.log('4.2-8:55',this.looker);
                  //console.log('4.2-8:56',this.anoter);
                  this.anoter = 3;
                  // for(let u in this.detw){
                  //   if(this.detw[u].id === undefined){
                  //     if(this.detw[u].aucoid === undefined){
                  //       u = Number(u);
                  //       this.detw.splice(u,1);
                  //     }else{}
                  //   }else{
                  //     if(this.detw[u].aucoid){}else{
                  //       this.detw[u].aucoid = this.detw[u].code;
                  //       this.detw[u].auname = this.detw[u].name;
                  //       this.detw[u].auspecfication = this.detw[u].specifications;
                  //       this.detw[u].aumodel = this.detw[u].model;
                  //       this.detw[u].auonlywei = this.detw[u].weight+this.detw[u].weightUnit;
                  //     }
                  //   }
                  //   // if(this.aupacks[u].id === undefined || this.aupacks[u].aucoid === undefined){
                  //   //   u = Number(u);
                  //   //   this.aupacks.splice(u,1);
                  //   // }else{
                  //   // }
                  // }
                  // if(this.packaging.id === this.allpack[c].id){
                  //   this.packaging.auxiliary = this.allpack[c].auxiliary;
                  // }
                  //aucoid: "002"
                  // aumodel: ""
                  // auname: ""
                  // auonlywei: 0
                  // auspecfication: ""
                  // auweimin: ""
                  // wrappage: 2163
                }else{}
              }
            }
          }else if(isEdintx === false){
            //console.log('4.1-11:22',this.looker);
            //console.log('4.1-11:27',ntum);
            if(this.looker === 2){
              if(this.linkfo === 3){
                this.aupacks = ntum.itemList;
                for(let u in this.aupacks){
                  if(this.aupacks[u].aucoid){}else{
                    this.aupacks[u].aucoid = this.aupacks[u].code;
                    this.aupacks[u].auname = this.aupacks[u].name;
                    this.aupacks[u].auspecfication = this.aupacks[u].specifications;
                    this.aupacks[u].aumodel = this.aupacks[u].model;
                    this.aupacks[u].auonlywei = this.aupacks[u].weight+this.aupacks[u].weightUnit;
                  }
                }
                // if(ntum.auxiliary === undefined || ntum.auxiliary.length === 0){this.detw = ntum.itemList;}else{
                //   this.detw = ntum.auxiliary;
                // }
                if(ntum.itemList === undefined){this.detw = ntum.auxiliary;}else{
                  this.detw = ntum.itemList;
                }
                //this.detw = ntum.itemList || ntum.auxiliary;
                this.detw.forEach((item,index) => {
                  if(item.weightUnit === '1' || item.weightUnit === 1){item.weightUnit2 = '毫克(mg)';}else if(item.weightUnit === '2' || item.weightUnit === 2){item.weightUnit2 = '克(g)';}
                  else if(item.weightUnit === '3' || item.weightUnit === 3){item.weightUnit2 = '千克(kg)';}else if(item.weightUnit === '4' || item.weightUnit === 4){item.weightUnit2 = '吨(T)';}
                  else{item.weightUnit2 = item.weightUnit;}
                  item.aucoid = item.aucoid === undefined ? item.code : item.aucoid;
                  item.auname = item.auname === undefined ? item.name : item.auname;
                  item.auspecfication = item.auspecfication === undefined ? item.specifications : item.auspecfication;
                  item.aumodel = item.aumodel === undefined ? item.model : item.aumodel;
                  item.auonlywei = item.weight;
                  //Number.isFinite(item.weightUnit);
                  item.auweimin = item.weightUnit2;
                })
                //console.log('7.18-9:38',this.detw);
              }
            }
            //console.log('4.1-11:34',this.aupacks);
          }
        }
      }
    },
    //查看
    havelooker(sea){
      this.respectively = 2;
      this.materlon = 5;
      this.looket = 1;
      if(sea.unit === null){sea.unit = "——"}
      //sea.unit = sea.unit === null ? "——" : sea.unit;
      if(sea.specifications === "——" && sea.model !== "——"){
        this.catlist = sea.code + '/' + sea.name + '/' + sea.model + '/' + sea.unit;
      }else if(sea.specifications !== "——" && sea.model === "——"){
        this.catlist = sea.code + '/' + sea.name + '/' + sea.specifications + '/' + sea.unit;
      }else if(sea.specifications === "——" && sea.model === "——"){
        this.catlist = sea.code + '/' + sea.name + '/' + sea.unit;
      }else{
        this.catlist = sea.code + '/' + sea.name + '/' + sea.specifications + '/' + sea.model + '/'+ sea.unit;
      }
      this.catunit = sea.unit;
      this.catliststa = this.catlist;//catliststa保持不动
      const supjson = {mtBaseId: sea.id};
      this.catid = sea.id;
      this.looknexten(supjson,this.catunit);
      // const jsonc = {
      //   mtBaseId: sea.id,//材料id
      //   supplierMaterial: ''//供应关系id    一条材料数据下可能包含多条供应商数据，每条供应数据对应的供应关系id还不同，
      //   //那该传哪个呢？
      // };
      // const jsong = {mtBaseId: sea.id};
      // getsuplink(jsong).then(res => {
      //   let lank8 = res.data.data;
      //   for(let h in lank8){
      //     jsonc.supplierMaterial = lank8[h].supplierMaterial;
      //   }
      // })
      // seapackinfon(jsonc).then(res => {
      //   this.list7 = res.data.data.list || [];
      //   if(this.list7.length === 0){
      //     this.list7 = [
      //       {supplier: 1,fullName: '供应商1',enabled: '——',pacwrite: '无包装'},
      //       {supplier: 2,fullName: '供应商2',enabled: '——',pacwrite: '1种包装方式'},
      //       {supplier: 3,fullName: '供应商3',enabled: '已停用',pacwrite: '2种包装方式'},
      //       {supplier: 4,fullName: '供应商4',enabled: '已停用',pacwrite: '——'}
      //     ]
      //   }else{
      //     const lank7 = this.list7;
      //     for(let g in lank7){
      //       const enabled = lank7[g].enabled;//1-正常，0-停用
      //       if(enabled === 1){
      //         lank7[g].enabled = '——';
      //       }else if(enabled === 0){
      //         lank7[g].enabled = '已停用';
      //       }
      //     }
      //   }
      //   const lank8 = res.data.data.suspendList || [];//已停用的数据
      // })
    },
    //点击查看按钮跳转页面展示的页面
    looknexten(supjson,unit){
      //console.log('5.21-16:25',unit);
      getsuplink(supjson).then(res => {
        this.list7 = res.data.data || [];
        let lank7 = this.list7;
        for(let g in lank7){
          //lank7[g].unit = unit;
          if(lank7[g].enabled === 1 || lank7[g].enabled === '1'){
            lank7[g].enabled = "——";
          }else{
            lank7[g].enabled = "已停用";
          }
          if(lank7[g].flag  === 1){//已设置
            lank7[g].flagn = 1;
            lank7[g].looket = 1;
          }else if(lank7[g].flag === 0){//未设置
            lank7[g].flagn = 0;
            lank7[g].looket = 2;
            // if(lank7[g].gs === ''){
            //   this.flagn = 0;
            // }else{
            //   this.flagn = 1;
            // }
          }
        }
        this.list7 = lank7;
        //console.log('5.16-9:28',this.list7);
      })
      //console.log('5.21-19:37',this.catliststa);//这个是初始状态下的文字内容
      //console.log('5.21-19:38',this.catlist);//这个会因为多次进行后续内容的增加导致出现数据错乱的问题
      this.catlist = this.catliststa;
      //最好是能保证每次在末尾新加新的内容时，前面的部分都是初始状态的数据，这样就不会出现A+B变成A+B+C的了
    },
    neeornonee(index,outSpace,outs){
      //console.log('2025.5.16-8:36',outs);
      if(outs.looket === 2){
        if(outSpace === 0){//需要
        }else if(outSpace === 1){//不需要
        }
      }
    },
    havelkerd(look){
      this.materlon = 10;
      const json = {recordId: look.id};//记录id
      getlistisg(json).then(res => {
        //debugger;
        let lankg = res.data.data;
        //console.log('4.2-16:26',lankg);
        this.createNameg = lankg.createName;//创建人
        this.createDateg = lankg.createDate;//创建时间
        let structureList = this.structureList2 = lankg.structureList;
        for(let s in structureList){
          if(structureList[s].outerShape === '1'){structureList[s].outSpace = '长方体';}
          else if(structureList[s].outerShape === '2'){structureList[s].outSpace = '圆柱体';}
          else if(structureList[s].outerShape === '3'){structureList[s].outSpace = '其他形状';}
        }
        lankg.structureList = structureList;
        //this.list82 = structureList;//this.list82需要是个数组,this.list82需要的数据跟structureList并排
        if(lankg.outerShape === 1){lankg.outerShape = '长方体';}
        else if(lankg.outerShape === 2){lankg.outerShape = '圆柱体';}
        else if(lankg.outerShape === 3){lankg.outerShape = '其他形状';}
        let lank = [];
        lank.push(lankg);
        this.list82 = lank;
        //console.log('4.2-16:27',this.list82);
        this.oven = 5;
        // if(lankg.outerShape === '1'){lankg.outSpace = '长方体';}
        // else if(lankg.outerShape === '2'){lankg.outSpace = '圆柱体';}
        // else if(lankg.outerShape === '3'){lankg.outSpace = '其他形状';}
        // this.list82 = [];
        // let json82 = {
        //   outerLength: lankg.outerLength,outerWidth: lankg.outerWidth,outerHeight: lankg.outerHeight,
        //   outSpace: lankg.outerShape,netWeight: lankg.netWeight,netUnit: lankg.netUnit,grossWeight: lankg.grossWeight,
        //   grossUnit: lankg.grossUnit
        // };
        // this.list82.push(json82);
        // for(let g in res.data.data){
        //   this.createNameg = res.data.data[g].createName;//创建人
        //   this.createDateg = res.data.data[g].createDate;//创建时间
        //   if(res.data.data[g].outerShape === '1'){res.data.data[g].outSpace = '长方体';}
        //   else if(res.data.data[g].outerShape === '2'){res.data.data[g].outSpace = '圆柱体'}
        //   else if(res.data.data[g].outerShape === '3'){res.data.data[g].outSpace = '其他形状'};
        // }
        // this.list82 = res.data.data;
      })
    },
    lookhnve(hnv,unit){
      //console.log('2025.2.8-从哪里来',this.respectively);
      //console.log('5.21-16:22',this.list7);
      //console.log('5.16-11:44',unit);
      this.morebak = '';//清空属性值，便于后续进行赋值
      this.list81 = [];
      this.materlon = 6;
      this.materlon = 6;
      this.gobck2 = 1;
      if(hnv.fullName === undefined){hnv.fullName = "——";}
      //this.catlist = this.catlist + '/'+ hnv.supplier + '/' + hnv.fullName;
      //console.log('5.16-14:00',this.catlist);
      //console.log('5.16-14:01',this.catliststa);
      this.catlist3 = this.catliststa + '/'+ hnv.codeName + '/' + hnv.fullName;
      this.purjson = hnv;
      //Integer mtBaseId            //材料id
      //Integer supplierMaterial    //供应关系id
      this.fullNamet = hnv.fullName;//供应商名称
      this.suppMaterial = hnv.supplierMaterial;//供应关系Id
      this.supplier = hnv.supplier;//供应商id
      console.log('测试7.18-13:45-测试这个1',this.catid);
      const seatjson = {mtBaseId: this.catid,supplierMaterial: hnv.supplierMaterial};
      seapackinfon(seatjson).then(res => {
        let nowlink = this.nowlank = res.data.data.list || [];//正在使用的包装
        // if(nowlink.length === 0){//此时既可能是真的没有数据，也可能是暂时未获取到数据
        //   //下面是自己写的假数据，用于先将页面功能展示出来，后续再根据真数据优化页面效果
        //   nowlink = [
        //     {
        //       createName: '张三',createDate: 1693795219074,outerShape: 1,id: 139,material: '189',
        //       packagingCount: 2,materialCount: 3,netWeight: 1,netUnitId: '739',netUnit: '千克',grossWeight: 1,
        //       grossUnitId: '132',grossUnit: '克',totalWeight: 100,totalUnit: '吨',outerLength: 2,lengthUnitId: 12,
        //       lengthUnit: 'cm',outerWidth: 3,widthUnitId: 20,widthUnit: 'm',outerHeight: 4,heightUnitId: 23,
        //       heightUnit: 'm',enabled: 1,enabledTime: '',
        //       structureList: [
        //         {
        //           level: 1,materialCount: 3,lengthUnitId: 12,lengthUnit: 'cm',outerLength: 2,widthUnitId: 20,widthUnit: 'm',
        //           outerWidth: 3,heightUnitId: 23,heightUnit: 'm',outerHeight: 4,outerShape: '1'
        //         },
        //         {
        //           level: 2,materialCount: 31,lengthUnitId: 12,lengthUnit: 'cm',outerLength: 21,widthUnitId: 20,widthUnit: 'm',
        //           outerWidth: 31,heightUnitId: 21,heightUnit: 'm',outerHeight: 3,outerShape: '2'
        //         }
        //       ],
        //       itemList: [
        //         {wrappage: '1',code: '3730',name: '主要包装物1',model: '2',specifications: '1',weightUnit: '千克(kg)',
        //           weight: 111,memo: ''}
        //       ],
        //       zyPackaging: [
        //         {wrappage: '2',code: '3730',name: '辅助包装物1',model: '3',specifications: '2',weightUnit: '吨(T)',
        //           weight: 222,memo: ''},
        //         {wrappage: '3',code: '3730',name: '辅助包装物2',model: '4',specifications: '3',weightUnit: '千克(kg)',
        //           weight: 333,memo: ''}
        //       ]
        //     }
        //   ]
        // }
        let stoplink = this.stoplank = res.data.data.suspendList || [];//暂停使用的包装
        //console.log('5.21-16:31',nowlink);
        //console.log('5.21-16:32',stoplink);
        //this.catunit = unit;
        //console.log('5.21-16:39',this.catunit);
        // if(nowlink.length === 0){}else{nowlink.unit = unit;}
        // if(stoplink.length === 0){}else{stoplink.unit = unit;}
        //这里因为stoplink=[]所以 如果给它 赋值unit就会出错
        for(let n in nowlink){
          let createName = this.createName1 = nowlink[n].createName;//创建人
          let createDate = this.createDate1 = nowlink[n].createDate;//创建时间
          if(nowlink[n].outerShape === 1){nowlink[n].outSpace = "长方体";}else if(nowlink[n].outerShape === 2){nowlink[n].outSpace = "圆柱体"}
          else if(nowlink[n].outerShape === 3){nowlink[n].outSpace = "其他形状"}
          //this.list81.push(nowlink[0]);
          //let list = this.lister = nowlink[n].list;
          // for(let i in list){
          //   let structureList = this.structureList = list[i].structureList;
          //   // for(let s in structureList){
          //   //   this.zyname = structureList[s].zyPackaging.name;
          //   //   this.itemleng = structureList[s].itemList.length;
          //   // }
          // }
          // for(let i in list){
          //   let zyPackaging = list[i].zyPackaging;//主要包装物
          //   this.zyname = zyPackaging.name;
          //   let itemList = list[i].itemList;//辅助包装物
          // }
          // let itemList = this.itemList = nowlink[n].itemList;//辅助包装物信息
          // let zyPackaging = nowlink[n].zyPackaging;//
          // let structureList = nowlink[n].structureList;//包装信息详情集合
          // for(let s in structureList){
          //   if(structureList[s].outerShape === "1"){structureList[s].outSpace = "长方体";}
          //   else if(structureList[s].outerShape === "2"){structureList[s].outSpace = "圆柱体"}
          //   else if(structureList[s].outerShape === "3"){structureList[s].outSpace = "其他形状"}
          // }
          //this.list8 = this.nowlank = structureList;
        }
        this.list8 = this.nowlank = nowlink || [];//这个位置this.list81包含包装数据第一条，页面只展示第一条数据
        //console.log('5.21-20:31',this.list8);
        //debugger;
        //console.log(this.pacallmag);
        this.pacallmag = [];
        //感觉既得用pacalllmag，但又要区分开新增和修改，如果重新设定一个数组盒子，又舀面临新增时会用到。
        //console.log(this.list8);
        for(let s = 0; s<this.list8.length;s++){
          let json1 = {
            id: this.list8[s].id,
            heightUnit: this.list8[s].heightUnit,
            heightUnitId: this.list8[s].heightUnitId,
            lengthUnit: this.list8[s].lengthUnit,
            lengthUnitId: this.list8[s].lengthUnitId,
            material: this.list8[s].material,
            megbox: [],
            outerLength: this.list8[s].outerHeight,
            supplierMaterial: this.list8[s].supplierMaterial,
            strid: this.list8[s].supplier,//供应商id
            widthUnit: this.list8[s].widthUnit,
            widthUnitId: this.list8[s].widthUnitId
          };
          let structureList = this.list8[s].structureList;
          for(let t = 0;t<structureList.length;t++){
            let json2 = {
              auxiliary: structureList[t].itemList,
              eveuppacnum: '',
              id: structureList[t].id,
              isExists: '',
              level: structureList[t].level,
              mainpack: structureList[t].zyPackaging,
              materialCount: structureList[t].materialCount,
              newer: '',
              onlygrosswei: '',
              onlynetwei: '',
              onlypack: '',
              outHeight: '',
              outLegth: '',
              outWidth: '',
              outerShape: this.list8[s].outerShape
            };
            json1.megbox.push(json2);
          }
          this.pacallmag.push(json1);
        }
        //console.log('3.26-10:01',this.pacallmag);//执行到这里的时候是有wrappage那三个属性的
        this.listb = this.stoplink = stoplink || [];
        if(this.list8.length === 0){
          this.oven = '';
          this.hasjson = 2;
        }else{
          //debugger;
          for(let s in this.list8){
            //this.list81 = [];
            s = Number(s);
            if(s === 0){
              const json81 = this.list8[s];
              this.list81.push(json81);
            }
            // this.list8[s].box = [];
            // const json8 = {
            //   outerLength: this.list8[s].outerLength,outerWidth: this.list8[s].outerWidth,
            //   outerHeight: this.list8[s].outerHeight,outSpace: this.list8[s].outSpace,
            //   materialCount: this.list8[s].materialCount,netWeight: this.list8[s].netWeight,
            //   netUnit: this.list8[s].netUnit,grossWeight: this.list8[s].netUnit,
            //   grossUnit: this.list8[s].grossUnit
            // };
            // this.list8.box.push(json8);
          }
          //console.log('4.2-15:40',this.list81);
          this.hasjson = 1;
          this.oven = 5;
        }
        //const lengtd = this.nowlank.length;
      })
    },
    //查看更多的包装方式
    lookmoreactr(){
      //debugger;
      this.morebak = 1;
      this.materlon = 11;
      this.oven = 5;
      let list8 = this.list8;
      for(let d in list8){
        list8[d].box = [];
      }
      for(let t in list8){
        const json8 = {
          outerLength: list8[t].outerLength,outerWidth: list8[t].outerWidth,
          outerHeight: list8[t].outerHeight,outSpace: list8[t].outSpace,
          materialCount: list8[t].materialCount,netWeight: list8[t].netWeight,
          netUnit: list8[t].netUnit,grossWeight: list8[t].grossWeight,
          grossUnit: list8[t].grossUnit
        };
        list8[t].box.push(json8);
      }
      this.list8 = list8;
      //console.log('4.2-13:35',this.list8);
    },
    stopuser(stoid){
      this.matVisible22 = true;
      this.desto = 2;
      this.desid = stoid;
    },
    //新增包装方式
    addpactd(unit){
      // this.looker2 = 1;
      // this.list9 = [{newer: '最外层包装',eveuppacnum: '——',onlypack: '',onlynetwei: '',onlygrosswei: ''}];
      //debugger;
      console.log('测试7.18-13:46-测试这个2',this.catid);
      this.morebak = 2;
      this.materlon = 4;
      this.allpack = [];
      //console.log('2025.2.8-现在的值',this.respectively);
      // this.looker2 = 1;
      if(this.respectively === 1){this.gobck2 = 1;}else if(this.respectively === 2){this.gobck2 = 2;}
      //this.gobck2 = 1;
      // this.list9 = [{newer: '最外层包装',eveuppacnum: '——',onlypack: '',onlynetwei: '',onlygrosswei: ''}];
      this.doiffet = 1;
      this.nextpict(0,this.doiffet,unit);
    },
    //编辑
    editbnk(objen,kedn){
      //debugger;
      //console.log('2025.2.8-一探究竟',this.morebak);
      //if(this.morebak === 1){}else{this.morebak = 3;}
      this.setdiftruck = 2;
      //this.madestune = 2;
      this.materlon = 4;//又是同理的问题
      //console.log('5.12-15:34',this.catlist3);
      this.catlist = this.catlist3;
      //debugger;
      if(kedn === 'one'){//点击页面中间的‘编辑’按钮
        this.gobck = 0;
        this.gobck2 = 1;
        this.gobck3 = 0;
        let structureList = objen.structureList;
        this.looker = 2;
        for(let s in structureList){
          let level = structureList[s].level;
          if(level === 1){
            structureList[s].newer = "最外层包装";
          }else{
            const chineseNumbers = ['零','一', '二', '三', '四', '五', '六', '七', '八', '九','十'];
            structureList[s].newer = "最外层包装的下"+chineseNumbers[s]+"层包装";
          }
          structureList[s].eveuppacnum = 0;//实在不知道这个字段的值是什么了
          structureList[s].onlypack = structureList[s].materialCount;//单个包装内材料的数量
          structureList[s].onlynetwei = Number(objen.netWeight);//单个包装的净重
          structureList[s].zyPackaging.weight = Number(structureList[s].zyPackaging?.weight);//主要包装物的重量
          //单个包装的毛重=单个包装的净重+主要包装物重量+辅助包装物重量
          let itemList = structureList[s].itemList;//辅助包装物数据
          let all = itemList?.reduce((sum,item) => sum + item.itemList, 0) || 0;
          all = Number(all);
          structureList[s].onlygrosswei = structureList[s].onlynetwei+structureList[s].zyPackaging.weight+all;
        }
        this.list5 = structureList;
        //this.list5.unit = unit;
        this.maksune = 'upent';//代表是需要修改包装数据的
        this.morebak = 3;//代表是从查看-设置-编辑过来的
        // this.list9 = structureList;
        // this.looker2 = 0;
        // for(let t in this.list81){
        //   let structureList = this.list81[t].structureList;
        //   this.list9 = structureList;
        // }
        //console.log('3.26-14:33',objen);//执行到这里也是有wrappage属性的
        this.stall = objen;
        this.strid = objen.supplier;
        //console.log('3.26-14:31',this.strid);
      }else if(kedn === 'all'){//structureList
        let structureList2 = objen.structureList;
        // this.list5 = this.list9 = structureList2;
        // console.log(this.list5);
        this.looker = 2;//虽然不是很明白为何之前是looker=0，但先这样吧。
        for(let t in structureList2){
          let level2 = structureList2[t].level;
          if(level2 === 1){
            structureList2[t].newer = "最外层包装";
          }else{
            const chineseNumbers = ['零','一', '二', '三', '四', '五', '六', '七', '八', '九','十'];
            structureList2[t].newer =  "最外层包装的下"+chineseNumbers[t]+"层包装";
          }
          structureList2[t].eveuppacnum = 0;
          structureList2[t].onlypack = structureList2[t].materialCount;//单个包装内材料的数量
          structureList2[t].onlynetwei = Number(objen.netWeight);//单个包装的净重
          structureList2[t].zyPackaging.weight = Number(structureList2[t].zyPackaging.weightUnit);//主要包装物的重量
          //单个包装的毛重=单个包装的净重+主要包装物重量+辅助包装物重量
          let itemList = structureList2[t].itemList;//辅助包装物数据
          let all = itemList.reduce((sum,item) => sum + item.itemList, 0) || 0;
          all = Number(all);
          structureList2[t].onlygrosswei = structureList2[t].onlynetwei+structureList2[t].zyPackaging.weight+all;
          //console.log('1',structureList2[t].zyPackaging);
          //console.log('2',structureList2[t].itemList);
        }
        this.list5 = this.list9 = structureList2;
        // this.list5.unit = unit;
        // this.list9.unit = unit;
        //debugger;
        //console.log('3.21',this.morebak);
        // if(this.morebak === 1){}else{
        //   this.morebak = 4;
        // }
      }
    },
    //停用的数据
    bendeactivd(){
      this.materlon = 8;
      let stoplank = this.stoplank;//停用的数据
      //这里需要注意的是：stoplank是全部的停用数据，而获取到的数据中不仅包含表格中的，还有主要包装物+辅助包装物的
      //数据，所以按理来说，在渲染时不该将一个json数据拆分成多个部分进行渲染。
      //let box = [];
      for(let s in stoplank){//stoplank下可能有多个json数据
        let id = this.stopid = stoplank[s].id;//包装id
        //let json = {};
        if(stoplank[s].outerShape === 1){stoplank[s].outSpace = "长方体";}
        else if(stoplank[s].outerShape === 2){stoplank[s].outSpace = "圆柱体";}
        else if(stoplank[s].outerShape === 3){stoplank[s].outSpace = "其他形状";}
        stoplank[s].layers = stoplank[s].structureList.length + '层';//录入多少层

        //stoplank[s].totalWeight =
        // json.outerLength = stoplank[s].outerLength;//长
        // json.outerWidth = stoplank[s].outerWidth;//宽
        // json.outerHeight = stoplank[s].outerHeight;//高
        // json.outSpace = stoplank[s].outerShape;//形状
        // json.totalWeight = stoplank[s].totalWeight;//包装后每包总重量
        // json.totalUnit = stoplank[s].totalUnit;//总重量单位
        // json.netWeight = stoplank[s].netWeight;//净重重量
        // json.netUnit = stoplank[s].netUnit;//净重单位
        // json.grossWeight = stoplank[s].grossWeight;//毛重重量
        // json.grossUnit = stoplank[s].grossUnit;//毛重单位
        // json.layers = stoplank.length + '层';//录入多少层
        // this.box.push(json);
        //let structureList = stoplank[s].structureList || [];//包装详情集合
        // for(let u in structureList){
        //   let level = structureList[u].level;//包装级次,1-最小包装,1-二级包装,以此递增
        //   if(level === "1"){structureList[u].level = "最外层";}else if(level === '2'){structureList[u].level = "最外层的下一层";}
        //   else if(level === '3'){structureList[u].level = "最外层的下两层";}else if(level === '4'){structureList[u].level = "最外层的下三层";}
        // }
        //console.log('3.11-1',this.list81);
        // for(let r in structureList){
        //   let itemList = structureList[r].itemList;//辅助包装物
        //   let zyPackaging = structureList[r].zyPackaging;//主要包装物
        // }
        // let itemList = stoplank[s].itemList;//辅助包装物
        // let zyPackaging = stoplank[s].zyPackaging;//主要包装物
      }
      this.stoplank = stoplank;
      this.morebak = 4;
    },
    //操作记录
    actlanktesk(uid){
      //debugger;
      this.materlon = 9;
      if(this.morebak === 1 || this.morebak === 4){}else{this.morebak = 3;}
      // if(this.morebak === 1){}else if(this.morebak === 4){
      //   this.materlon = 8;
      // }else{this.morebak = 3;}
      //if(this.morebak === 1){}else{this.morebak = 3;}
      //突然发现不对啊，就算非停用和停用的数据都是同一个包装方式id，但是非停用和停用都是数组啊。
      const jsong = {id:uid};       //包装id}
      getcatlink(jsong).then(res => {
        let lankd = res.data.data || [];
        for(let t in lankd){
          let operate = lankd[t].operation; //操作:1-增,2-删,3-改,4-启用,5-停用
          if(operate === 1){
            lankd[t].operation = '创建';
          }else if(operate === 3){
            lankd[t].operation = '修改';
          }else if(operate === 4){
            lankd[t].operation = '启用';
          }else if(operate === 5){
            lankd[t].operation = '停用';
          }
          if(Number(t) === 0){
            lankd[t].updateDate = lankd[t].createDate;
            lankd[t].updateName = lankd[t].createName;
          }
        }
        this.list10 = lankd;
      })
    },
    //转换时间戳数据
    // formatTimestamp(timestamp){
    //   const date = new Date(timestamp);
    //   const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以加1，并用0填充
    //   const daten = String(date.getDate()).padStart(2,'0');//日从0开始，所以加1，并用0补充
    //   const hours = String(date.getHours()).padStart(2,'0');//时从0开始，所以加1，并用0补充
    //   const minutes = String(date.getMinutes()).padStart(2,'0');//分从0开始，所以加1，并用0补充
    //   const seconds = String(date.getSeconds() + 1).padStart(2,'0');//分从0开始，所以加1，并用0补充
    //   return `${date.getFullYear()}-${month}-${daten} ${hours}:${minutes}:${seconds}`;
    // },
    // //不展示时分秒的时间戳转换
    // formatTimestan(timestan){
    //   const date = new Date(timestan);
    //   const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以加1，并用0填充
    //   const daten = String(date.getDate()).padStart(2,'0');//日从0开始，所以加1，并用0补充
    //   return `${date.getFullYear()}-${month}-${daten}`;
    // },
    changnoact(unid,unlist){
      this.matVisible22 = true;
      this.desto = 3;
      this.desid = unid;//包装Id
      this.deslist = unlist;
      //现在需要的根据之前的数据将传值写出来用于调用接口
    },
    //启用
    startusen(itid){
      this.matVisible22 = true;
      this.desto = 4;
      this.desid = itid;
    },
    //修改
    upteer(upt){
      //点击修改按钮展示增加的那个弹窗，并运行进行各输入框的修改，故需要先获取相应数据，并展示在
      //弹窗中
      this.matVisible33 = true;
      //debugger;
      //console.log('乙巳年正月十六-拾贰',this.allpack);
      // if(this.addunten === 1){//代表此时是有关‘主要包装’或’辅助包装‘的修改
      // }else if(this.addunten === 2){
      //   this.maksune = 'upent';
      // }
      this.maksune = 'upent';
      upt.keyd = upt.aucoid;//用于判断修改的是哪条数据
      this.uptlist = upt;
      this.packagbox.numb = upt.aucoid;
      this.packagbox.name = upt.auname;
      this.packagbox.sizer = upt.auspecfication;
      this.packagbox.model = upt.aumodel;
      this.packagbox.widthUnit = upt.auweimin;
      this.addunten = 1;
      this.morebak = '';
      // const listw = this.packagbox.widthlist;
      // for(let s in listw){
      //   const value = listw[s].value;
      //   if(value === upt.auweimin){
      //     this.packagbox.widthUinit = listw[s].value;
      //   }
      // }
      // this.packagbox.onlhei =Number(upt.auonlywei);
    },
    //删除
    detter(detk){
      this.matVisible22 = true;
      this.desto = 1;
      this.detk = detk;
      // let lank6 = this.list6;
      // for(let f in lank6){
      //   let sixid = lank6[f].aucoid;
      //   let detid = detk.aucoid;
      //   if(sixid === detid){
      //
      //   }
      // }
    },
    normalizeDivisionResult(value, defaultValue = 0){
      return Number.isFinite(value) ? value : defaultValue;
    },
    //删除本层
    dettthtap(det){
      const id = det.id;
      this.id5 = id;
      this.matVisible22 = true;
      //const lank5 = this.list5;
      this.desto = 5;
      // for(let e in lank5){
      //   e = Number(e);
      //   if(lank5[e].id === id){
      //     lank5.splice(e,1);
      //   }
      // }
    },
    paclooket(list){
      this.matVisible4 = true;
      this.yesorno = 0;
      this.linkfo = 2;
      this.anoter = 2;
      let newlast = [];
      //console.log('4.2-11:03',list);//list现在是个json
      newlast.push(list.zyPackaging);
      let weightUnit = list.zyPackaging.weightUnit;
      if(weightUnit === '1'){list.zyPackaging.weightUnit = '毫克(mg)'}
      else if(weightUnit === '2'){list.zyPackaging.weightUnit = '克(g)'}
      else if(weightUnit === '3'){list.zyPackaging.weightUnit = '千克(kg)'}
      else if(weightUnit === '4'){list.zyPackaging.weightUnit = '吨(T)'}
      let itemList = list.itemList;
      for(let t in itemList){
        let weightUnit2 = itemList[t].weightUnit;
        if(weightUnit2 === '1'){itemList[t].weightUnit = '毫克(mg)'}
        else if(weightUnit2 === '2'){itemList[t].weightUnit = '克(g)'}
        else if(weightUnit2 === '3'){itemList[t].weightUnit = '千克(kg)'}
        else if(weightUnit2 === '4'){itemList[t].weightUnit = '吨(T)'}
        newlast.push(itemList[t]);
      }
      //console.log('7.18-10:554',newlast);
      newlast.forEach((item,index)=> {
        item.code = item.aucoid === undefined ? item.code : item.aucoid;
        item.name = item.auname === undefined ? item.name : item.auname;
        item.specifications = item.auspecfication === undefined ? item.specifications : item.auspecfication;
        item.model = item.aumodel === undefined ? item.model : item.aumodel;
      })
      this.newlast = newlast ;
      //console.log('4.2-11:12',this.newlast);
    },
    // getnewname(){
    //   let pack = this.packaging;
    //   if(pack.mainpack.codeid !== '' && pack.eveuppacnum !== ''){
    //     this.madestune = 1;
    //   }else{
    //     this.madestune = 0;
    //   }
    //   // if(this.sizeForm.name4 !== '' && this.addprck1 === 2){
    //   //   this.madestune = 1;
    //   // }else{
    //   //   this.madestune = 0;
    //   // }
    // }
    //用于将数据进行深度拷贝
    deepClone(obj) {
      return JSON.parse(JSON.stringify(obj));
      // 或者使用 lodash: return _.cloneDeep(obj);
    },
    saveToAll1() {
      this.all1 = this.deepClone(this.json1);
    },
    // 方法：保存第二种情况的数据
    saveToAll2() {
      this.all2 = this.deepClone(this.json1);
    },
  }
}
</script>

<style lang="scss">
.el-checkbox__inner{
  border-radius: 50%;
}
$sizer1: 100%;
$sizer2: 92px;
$sizer3: 20px;
$distance: 11px;
$distance2: 17px;
.homer{
  padding: 50px;
  .rolor{
    margin: 0 96px;
    .unSection{
      margin-bottom: 30px;display: flex;align-items: center;
      > span:nth-child(2){
        margin-left: 200px;
      }
      .el-button {
        margin-left: 100px;padding: 0;
        span{font-weight: bold;}
      }
    }
    .lineQuery{
      margin-bottom: 20px;margin-top: 30px;
      .searchSect{
        display: flex;align-items: center;
        .keywordSearch{
          .searchIcon{
            float: left;background: url(../../assets/img/search.png) no-repeat center;background-size: 22px;
            height: 28px;width: 28px;cursor:pointer;
          }
          input{
            padding: 0 10px;font-size: 12px; min-width: 200px; height: 28px;line-height: 28px;border: 1px solid #b6b6b6;
          }
        }
      }
    }
    > div:nth-child(4){
      width: $sizer1;margin-top: $sizer2;
    }
  }
}
.useprot{
  padding: 50px;
  .rolor{
    margin: 0 96px;
    .backPage{
      margin-bottom: 20px;
    }
    .lineQuery{
      margin-bottom: 20px;
      .searchSect{
        display: flex;align-items: center;
        .keywordSearch{
          .searchIcon{
            float: left;background: url(../../assets/img/search.png) no-repeat center;background-size: 22px;
            height: 28px;width: 28px;cursor:pointer;
          }
          input{
            padding: 0 10px;font-size: 12px; min-width: 200px; height: 28px;line-height: 28px;border: 1px solid #b6b6b6;
          }
        }
      }
    }
    > div:nth-child(3){
      width: $sizer1;margin-top: $sizer2;
      .endy{color: #ccc;background-color: #ffffff;}
    }
  }
}
.usepro2{
  padding: 50px;
  .rolor{
    margin: 0 96px;
    .backPage{
      margin-bottom: 20px;
      .litten{margin-right: 10px;}
    }
    .topName{
      padding: 20px 0; border-bottom: 1px solid #c2c1c1;margin-bottom: 20px;
    }
    .lineQuery{
      margin-bottom: 20px;margin-right: 15px;
    }
    > div:nth-child(4){
      > div:nth-child(2){
        .el-button span{font-weight: bold;}
      }
    }
    > div:nth-child(5){
      width: $sizer1;margin-top: $sizer2;
      .specinal{pointer-events: none;}
    }
  }
}
.buctedit{
  padding: 50px;
  .rolor{
    margin: 0 96px;
    .backPage{
      margin-bottom: 20px;
      .litten{margin-right: 10px;}
    }
    .topName{
      padding-top: 20px;padding-bottom: 5px;
    }
    .lineQuery{
      margin-bottom: 20px;
      span{color:#0070C0}
    }
    > div:nth-child(4){
      width: $sizer1;
      .el-table--fit{
        .el-table__inner-wrapper{
          .el-table__header-wrapper{
            .el-table__header{
              thead{
                tr{
                  > th:nth-child(1){
                    .cell .el-checkbox .el-checkbox__input .el-checkbox__inner{
                      display: none !important;
                    }
                  }
                }
              }
              .custom-selection-column{
                .cell{
                  .el-checkbox{
                    position: relative;
                    width: 42px;
                    ::before{
                      content: "选择";
                      position: absolute;
                      left: 8px;
                      top: 50%;
                      transform: translateY(-50%);
                      font-size: 14px;
                      color: #606266;
                      white-space: nowrap;
                      pointer-events: none; /* 防止文字阻挡点击 */
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
.nextpick{
  padding: 50px;
  .rolor{
    margin: 0 96px;
    .backPage{
      margin-bottom: 20px;
      .litten{margin-right: 10px;}
    }
    .topName{
      padding: 20px 0; border-bottom: 1px solid #c2c1c1;margin-bottom: 20px;
    }
    .lineQuery{
      span{color:#0070C0};
    }
    > div:nth-child(3){
      .ty-right .nexte span{
        margin-top: 28px;font-size: 15px;font-weight: bold;
      }
    }
    > div:nth-child(4){
      border-bottom: 1px solid #c2c1c1;padding-bottom: 20px;
    }
    > div:nth-child(5){
      width: $sizer1;margin-top: $sizer3;
    }
  }
}
.supalltak{
  padding: 50px;
  .rolor{
    margin: 0 96px;
    .backPage{
      margin-bottom: 20px;
      .litten{margin-right: 10px;}
    }
    .topName{
      padding: 20px 0;border-bottom: 1px solid #c2c1c1;margin-bottom: 20px;
    }
    .lineQuery{
      margin-bottom: 20px;margin-right: 15px;
    }
    > div:nth-child(4){
      .specinal{pointer-events: none;}
    }
  }
}
.allspecil{
  padding: 50px;
  .rolor{
    margin: 0 96px;
    .backPage{
      margin-bottom: 20px;
    }
    .topName{
      padding: 20px 0;border-bottom: 1px solid #c2c1c1;margin-bottom: 20px;
    }
    .lineQuery{margin-bottom: 20px;padding-bottom: 67px;}
    > div:nth-child(4){
      margin-bottom: 20px;padding-bottom: 67px;border: none;
      div .down1{
        display: flex;align-items: center;
        .litt1{margin-right: 56px;}
        .litt2{
          > .el-button{
            span{font-weight: bold;}
          }
          > .el-button:nth-child(2){
            color: #ff0000;
          }
        }
      }
      > div:nth-child(2){
        > .el-button span{
          font-weight: bold;
        }
      }
    }
    > div:nth-child(6){
      margin-bottom: -19px;padding-bottom: 67px;border: none;
      div .down1{
        display: flex;align-items: center;
        .litt1{margin-right: 144px;}
      }
      > div:nth-child(2){
        > .el-button span{
          font-weight: bold;
        }
      }
    }
    > div:nth-child(7), > div:nth-child(8), > div:nth-child(9){
      margin-bottom: -19px;padding-bottom: 67px;border: none;
      div .down1{
        display: flex;align-items: center;
        .litt1{margin-right: 80px;}
      }
      > div:nth-child(2){
        > .el-button span{
          font-weight: bold;
        }
      }
    }
    > div:nth-child(10){
      margin-bottom: -19px;padding-bottom: 67px;border: none;
      div .down1{
        display: flex;align-items: center;
        .litt1{margin-right: 144px;}
      }
      div .wrote{
        > .el-button>span{font-weight: bold;}
      }
    }
  }
}
.addpactad{
  padding: 50px;
  .rolor{
    margin: 0 96px;
    .backPage{
      margin-bottom: 20px;
      .litten{margin-right: 10px;}
    }
    .lineQuery{
      span{color:#0070C0;};
    }
    > div:nth-child(2){
      .ty-right .nexte span{
        margin-top: 28px;font-size: 15px;font-weight: bold;
      }
    }
    > div:nth-child(3){
      border-bottom: 1px solid #c2c1c1;padding-bottom: 20px;
    }
    > div:nth-child(4){
      width: $sizer1;margin-top: $sizer3;
    }
  }
}
.stopund{
  padding: 50px;
  .rolor{
    padding: 0 96px;
    .backPage{
      margin-bottom: 20px;
      .litten{margin-right: 10px;}
    }
    .topName{
      padding: 20px 0;border-bottom: 1px solid #c2c1c1;margin-bottom: 20px;
    }
    .lineQuery{margin-bottom: 20px;border-bottom: 1px solid #c2c1c1;padding-bottom: 67px;}
    .scrollable-div{
      width: 100%;//设置宽度
      height: 425px;//设置高度
      overflow: auto;//当内容超出盒子尺寸时显示滚动条
      div > div:nth-child(1){
        > div:nth-child(2){
          > .el-button span{font-weight: bold;}
        }
      }
    }
  }
}
.actlankst{
  padding: 50px;
  .rolor{
    padding: 0 96px;
    .backPage{
      margin-bottom: 20px;
      .litten{margin-right: 10px;}
    }
    .lineQuery{margin-bottom: 20px;}
    > div:nth-child(2){
      padding-bottom: 42px;
    }
    > div:nth-child(4){
      div{margin-bottom: 32px;}
    }
  }
}
.baksoutr{
  padding: 50px;
  .rolor{
    padding: 0 96px;
    .backPage{
      margin-bottom: 20px;
      .litten{margin-right: 10px;}
    }
    .lineQuery{margin-bottom: 20px;}
    > div:nth-child(2){
      padding-bottom: 42px;
    }
  }
}
.morepic{
  padding: 50px;
  .rolor{
    padding: 0 96px;
    .backPage{
      margin-bottom: 20px;
      .litten{margin-right: 10px;}
    }
    .topName{
      padding: 20px 0;border-bottom: 1px solid #c2c1c1;margin-bottom: 20px;
    }
    .lineQuery{margin-bottom: 20px;border-bottom: 1px solid #c2c1c1;padding-bottom: 67px;}
    //> div:nth-child(4), > div:nth-child(10){
    > div{
      margin-bottom: 20px;padding-bottom: 0;border: none;
      div .down1{
        display: flex;align-items: center;
        .litt1{margin-right: 56px;}
        .litt2{
          > .el-button{
            span{font-weight: bold;}
          }
          > .el-button:nth-child(2){
            color: #ff0000;
          }
        }
      }
      > div:nth-child(2){
        > .el-button span{
          font-weight: bold;
        }
        > .el-button:nth-child(2){color:#ff0000;}
      }
      .lineQuery{
        .litt2{
          > .el-button{
            span{font-weight: bold;}
          }
          > .el-button:nth-child(2){color: #ff0000;}
        }
      }
      div{
        .lineQuery{
          div:nth-child(2){
            > .el-button{span{font-weight: bold;}}
          }
        }
      }
    }
    > div:nth-child(6){
      margin-bottom: -19px;padding-bottom: 67px;border: none;
      div .down1{
        display: flex;align-items: center;
        .litt1{margin-right: 144px;}
      }
      > div:nth-child(2){
        > .el-button span{
          font-weight: bold;
        }
      }
    }
    > div:nth-child(7), > div:nth-child(8), > div:nth-child(9){
      margin-bottom: -19px;padding-bottom: 67px;border: none;
      div .down1{
        display: flex;align-items: center;
        .litt1{margin-right: 80px;}
      }
      > div:nth-child(2){
        > .el-button span{
          font-weight: bold;
        }
      }
    }
    //> div:nth-child(9){border-bottom: 1px solid #c2c1c1;padding-bottom: 67px;}
    > div:nth-child(10){margin-top:40px;}
  }
}
.tyDialog{
  form{
    margin: 50px;
    .modInput{
      .pannelTab{
        td{
          .tbTtl{
            padding-left: 2px;padding-bottom: 5px;width: 548px;margin-bottom: 42px;
            .red{color: #f80202;}
            div{
              width: 50%;
              .linkBtn{color: #036bc0;currsor: default;font-weight: 550;}
            }
          }
        }
        .packige{
          td .tbTtl{
            display: flex;align-items: center;width: 613px;margin-bottom: 23px;
            .ty-left{width: 45%;}
            .ty-right{
              display: flex;align-items: center;width: 316px;
              .ty-left{
                width: 1920px;
                .inputd{width: $sizer1;background-color: #D9D9D9;}
              }
            }
          }
        }
      }
    }
  }
  .fmWrapper{
    margin: auto;
    > div:nth-child(1){
      margin: 50px;
    }
    .bTip{ color: #366092; };
    .outer_form{
      li{
        padding: 6px 0;
        >div{ margin-bottom: 6px;}
        .ty-inputText{ width: $sizer1;height: 35px;}
      }
      .allen{
        .ty-inputText{ width: 500px; }
        .ty-selectText{ margin-left: 4px;width: 94px; }
      }
    }
    .shapeCon{
      margin-top: 20px;padding-top: 20px;border-top: 1px solid #c2c1c1;
      p{ margin-bottom: 10px; }
      .outerShape{
        display: flex;justify-content: space-between;
        input{ margin-right: 14px;}
      }
    }
  }
  .sectCon{
    padding: 28px 0;
    .linkBtn{
      color: #036bc0;cursor: default;font-weight: 550;
    }
  }
  //总务：18010478540，密码：a123456y
  //开发：18010478541，密码：cs8541cy
  .ty-center{margin-top: 25px;}
}
</style>