<template>
  <div>
    <div class="demoPage" v-show="condition === 0">
      <div>
        <div class="main rolor">
          <div class="biggenr">
            <div class="ty-alert">
              <div class="" style="width: 100%;">
                <div class="ty-right" style="margin-left: auto;display: flex;width: 200px;">
                  <span style="width: 33px;padding: 8px;">筛选</span><!--之后页面布局的时候响应式效果都用ty-alert这个，然后筛选框输入框那些用el-element组件-->
                  <el-select v-model="value" placeholder="请选择" @change="getLabel">
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </div>
                <div v-for="(item,index) in listsg" :key="index">
                  <div id="thelist">本页列表为系统内的一类装备与五类装备，共<span class="number">{{item.lenth}}</span>条数据。 </div>
                </div>
              </div>
            </div>
            <div>
              <div class="word pleeup">
                <div class="line"></div>
                表格内蓝色字可点击，点击后所见为相应数据，其中“操作技能的要求”与“已设置的控制点”下还有管理功能。
                <br />
                初始状态下，请逐条点击各设备“操作技能的要求”与“已设置的控制点”下的蓝色字，以进行相应设置
              </div>
            </div>
            <!--<el-row>
              <el-col :lg="12" :md="13" :sm="12" :xm="24"><div class="grid-content bg-purple" v-for="(item,index) in listsg" :key="index">
                <div id="thelist">本页列表为系统内的一类装备，共<span class="number">{{item.lenth}}</span> 条数据。</div>
              </div></el-col>
              <el-col :lg="0" :md="0" :sm="0" :xm="24"><div class="grid-content bg-purple-light"></div></el-col>
              <el-col :lg="12" :md="11" :sm="12" :xm="24"><div class="grid-content bg-purple">
                <el-row>
                  <el-col :lg="5" :md="8" :sm="11" :xm="12"><div class="grid-content bg-purple" style="padding: 7px 13px;"></div></el-col>
                  <el-col :lg="19" :md="16" :sm="13" :xm="12"><div class="grid-content bg-purple-light">
                    <el-row>
                      <el-col><div class="grid-content bg-purple">
                        <span>筛选</span>
                      </div> </el-col>
                      <el-col><div class="grid-content bg-purple-light">
                        <el-select v-model="value" placeholder="请选择" @change="getLabel">
                          <el-option
                              v-for="item in options"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                          >
                          </el-option>
                        </el-select>
                      </div> </el-col>
                    </el-row>
                  </div></el-col>
                </el-row>
              </div></el-col>
            </el-row>-->
          </div>
          <div class="equipdatein upolence">
            <el-table :data="list" border class="eltable ty-table-control ty-table ty-table-headerLine">
              <el-table-column fixed label="装备器具名称/型号" width="" align="center">
                <template #default="scope">
                  <el-button class="impont-hover" @click="jumptoanoter(scope.row,1)" type="typcolor">
                    {{ scope.row.equipmentName }}/{{ scope.row.modelName }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column prop="supplierName" label="来源" width="" align="center"></el-table-column>
              <el-table-column prop="units" label="单位" width="100" align="center"></el-table-column>
              <el-table-column prop="emCount" label="数量" width="100" align="center"></el-table-column>
              <el-table-column label="操作技能的要求" width="" align="center">——</el-table-column>
              <el-table-column label="有资质的操作者" width="" align="center">——</el-table-column>
              <el-table-column label="已设置的控制点" width="220" align="center">
                <template #default="scope">
                  <el-button class="impont-hover" @click="jumptoanoter(scope.row,2)" type="typcolor" size="small">
                    {{ scope.row.point_state }}
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column label="配套使用的装备" width="" align="center">——</el-table-column>
            </el-table>
          </div>
        </div>
        <!--<TyDemo firstName="一级菜单" secondName="二级菜单" />

        <table class="ty-table ty-table-control">
          <tr>
            <td>功能</td>
            <td>功能描述</td>
            <td>操作</td>
          </tr>
          <tr v-for="(item, index) in list" :key="index">
            <td v-html="item.name"></td>
            <td>{{ item.fullName }} </td>
            <td>
              <span class="ty-color-blue" @click="delBtn(item)">删除</span>
            </td>
          </tr>
        </table>-->

        <!--分页功能-->
        <TyPage v-if="list"
                :curPage="list.curPage" :pageSize="list.pageSize"
                :allPage="list.totalPage" :pageClickFun="pageClick">
        </TyPage>

      </div>


      <!--弹窗的高低顺序 是按照代码的前后顺序
        <TyDialog v-if="delVisible" width="800" dialogTitle="个人信息" color="green" :dialogHide="hideFun">
          <template #dialogFooter>
            <el-button class="bounce-cancel" @click="hideFun">取消</el-button>
            <el-button class="bounce-ok" @click="tipOk">确定</el-button>
          </template>
          <template #dialogBody>
            <div class="ty-center">
              当前的数据：{{ editObj.name }} - {{ editObj.fullName }} <br/>
              确定需要删除吗？
            </div>
          </template>
        </TyDialog>-->

    </div>

    <div class="cateogont" v-show="condition === 1">
      <div>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 gbbefor" @click="jumptoanoter(0,0)" id="backbeg">返回</span>
        <div class="main rolor">
          <el-row>
            <el-col :span="12"><div class="grid-content bg-purple" v-for="(itemy,indexy) in list6" :key="indexy">
              以下<span>{{ itemy.higger }}</span>台设备的名称均为<span>{{ itemy.equname }}</span>，型号均为
              <span>{{ itemy.model }}</span>，来源均为<span>{{ itemy.supplierName }}</span>。
            </div></el-col>
            <el-col :span="12"><div class="grid-content bg-purple-light"></div></el-col>
          </el-row>
          <!--<div class="equmemont upolence">
            <table class="ty-table ty-table-control">
              <tr>
                <td>系统赋予的编码</td>
                <td>装备器具编号</td>
                <td>所属类别</td>
                <td>创建</td>
              </tr>
              <tr v-for="(item2,index2) in list2" :key="index2">
                <td>XXXXXXX</td>
                <td>XXXXXXX</td>
                <td>XXXXXXXXXXXX>XXXXXX>XXXX>XXXXX>XXXXXXXXXXXXXXXXXXXXXXXXXXXX…</td>
                <td>XXX XXXX-XX-XX XX:XX:XX</td>
              </tr>
            </table>
          </div>-->
          <div class="equmemont upolence">
            <el-table :data="list2" border style="width: 100%">
              <el-table-column fixed prop="lpadId" label="系统赋予的编码" width="195" align="center"></el-table-column>
              <el-table-column prop="modelCode" label="装备器具编号" width="175" align="center"></el-table-column>
              <el-table-column prop="path" label="所属类别" width="780" align="center"></el-table-column>
              <el-table-column fixed="right" label="创建" width="293" align="center">
                <template #default="scope">
                  {{ scope.row.createName }}&nbsp;{{ scope.row.createDate }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <div class="pointsetr" v-show="condition === 2">
      <div>
        <div class="ty-alert">
          <div class="" style="width: 100%;">
            <div class="ty-right" style="margin-left: auto;">
              <input type="hidden" class="setchoeprocs">
              <el-radio-group v-model="radio1" style="margin-right: 26px;">
                <el-radio @click.native.prevent="closend(1)" :label="1" v-show="conhide === 0" name="getbont" id="unsett1">尚未设置</el-radio>
                <el-radio @click.native.prevent="closend(2)" :label="2" v-show="conhide === 1" name="getbont" id="unsett2">已设置控制点0个，设置完毕！</el-radio>
                <el-radio @click.native.prevent="closend(3)" :label="3" v-show="conhide === 2" name="getbont" id="unsett3">不在此处设置</el-radio>
                <el-radio @click.native.prevent="closend(4)" :label="4" v-show="conhide === 3" name="getbont" id="unsett4">
                  已设置控制点<span>{{ this.action }}</span>个，设置完毕
                </el-radio>
              </el-radio-group>
              <!--            <el-button type="primary" id="madesune1" class="memal" v-show="mdeune === 0" style="margin-top: -7px;">确定</el-button>-->
              <el-button type="info" id="madesune2" class="memal" disabled v-show="mdeune === 1" style="margin-top: -7px;">确定</el-button>
              <el-button type="primary" id="madesune3" class="memal" v-show="mdeune === 2" style="margin-top: -7px;" @click="madesurebck">
                确定</el-button>
            </div>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" style=""
                  @click="jumptoanoter(0,0)" id="backerd">返回</span>
          </div>
        </div>
        <div class="companyDetails pocom">
          <div class="ty-alert">
            <div class="" style="width: 100%;" v-for="(itemd,indexc) in listd" :key="indexc">
              <div class="ty-right" style="margin-left: auto;">
                <div style="font-size: 16px;">创建：<span>{{ itemd.createName }}&nbsp;{{ itemd.createDate }}</span></div>
              </div>
              <div style="display: flex;">
                <div style="font-size: 16px;">系统赋予的编号:<span>{{ itemd.lpadId}}</span></div>
                <div class="wrtdboad" style="font-size: 16px;">{{ itemd.modelCode }}/{{ itemd.equipmentName}}/{{ itemd.modelName }}</div>
              </div>
            </div>
          </div>
          <div class="line"></div>
          <div class="ty-alert">
            <div class="" style="width: 100%;">
              <div class="ty-right" style="margin-left: auto;">
                <span class="actpont" style="font-size: 16px;">什么是“装备器具的控制点”？</span>
                <span type="primary" class="mema ty-btn ty-btn-blue ty-btn-big ty-circle-5"
                      @click="havelooker()">查看</span>
              </div>
              <span class="com_address actone" v-show="actacon === 0"
                    style="font-size: 16px;">请设置本台设备的控制点！</span>
              <span class="com_address acttwo" v-show="actacon === 1"
                    style="font-size: 16px;">已设置控制点<span>{{ this.action }}</span>个</span>
              <span class="com_address actthree" v-show="actacon === 2"
                    style="font-size: 16px;">已设置控制点0个</span>
              <span class="com_address actfore" v-show="actacon === 3"
                    style="font-size: 16px;">不在此处设置</span>
            </div>
          </div>
          <div class="ty-alert">
            <div class="" style="width: 100%;">
              <div class="ty-right" style="font-size: 16px;">
                <el-button type="primary" @click="addcaten()">增加控制点</el-button>
              </div>
              <el-radio-group v-model="radio2" v-show="choosen === 0">
                <el-radio @click.native.prevent="concalpoint(1)" :label="1">本设备暂无控制点</el-radio>
                <el-radio @click.native.prevent="concalpoint(2)" :label="2">配置生产线或制造规范时再设置控制点，在此暂不设置</el-radio>
              </el-radio-group>
            </div>
          </div>
        </div>
      </div>
      <div class="companyDetails pocom">
        <!--<el-row>
          <el-col :span="6"><div class="grid-content bg-purple">
            <div>系统赋予的编号:<span>XXXXXX</span></div>
          </div></el-col>
          <el-col :span="6"><div class="grid-content bg-purple-light">
            <div>装备器具编号/名称/型号</div>
          </div></el-col>
          <el-col :span="6"><div class="grid-content bg-purple"></div></el-col>
          <el-col :span="6"><div class="grid-content bg-purple-light">
            <el-row>
              <el-col :span="6"><div class="grid-content bg-purple">
                <div></div>
              </div></el-col>
              <el-col :span="18"><div class="grid-content bg-purple-light">
                <div>创建：<span>XXX XXXX-XX-XX XX:XX:XX</span></div>
              </div></el-col>
            </el-row>
          </div></el-col>
        </el-row>
        <div class="eltop">
          <div class="line"></div>
          <el-row>
            <el-col :span="12"><div class="grid-content bg-purple">
              <el-row>
                <el-col :span="12"><div class="grid-content bg-purple">
                  <span class="com_address actone" v-show="actacon === 0">请设置本台设备的控制点！</span>
                  <span class="com_address acttwo" v-show="actacon === 1">已设置控制点<span>X</span>个</span>
                </div></el-col>
                <el-col :span="12"><div class="grid-content bg-purple-light"></div></el-col>
              </el-row>
            </div></el-col>
            <el-col :span="12"><div class="grid-content bg-purple-right">
              <el-row>
                <el-col :span="9"><div class="grid-content bg-purple"></div></el-col>
                <el-col :span="15"><div class="grid-content bg-purple-light">
                  <el-row>
                    <el-col :span="19"><div class="grid-content bg-purple choopont">
                      <el-row>
                        <el-col :span="8"><div class="grid-content bg-purple"></div></el-col>
                        <el-col :span="16"><div class="grid-content bg-purple-light">
                          <span class="actpont">什么是“装备器具的控制点”？</span>
                        </div></el-col>
                      </el-row>
                    </div> </el-col>
                    <el-col :span="5"><div class="grid-content bg-purple-light">
                      <el-row>
                        <el-col :span="2"><div class="grid-content bg-purple"></div></el-col>
                        <el-col :span="22"><div class="grid-content bg-purple-light">
                          <el-button type="primary" class="memal" @click="havelooker()">查看</el-button>
                        </div></el-col>
                      </el-row>
                    </div> </el-col>
                  </el-row>
                </div></el-col>
              </el-row>
            </div></el-col>
          </el-row>
        </div>-->
        <div class="eltop">
          <!--<el-row>
            <el-col :span="12"><div class="grid-content bg-purple">
              <el-row>
                <el-col :span="12"><div class="grid-content bg-purple">
                  <el-radio v-model="radio2" label="1">本设备暂无控制点</el-radio>
                </div></el-col>
                <el-col :span="12"><div class="grid-content bg-purple-light">
                  <el-radio v-model="radio2" label="2">配置生产线或制造规范时再设置控制点，在此暂不设置</el-radio>
                </div></el-col>
              </el-row>
            </div></el-col>
            <el-col :span="12"><div class="grid-content bg-purple-light">
              <el-row>
                <el-col :span="12"><div class="grid-content bg-purple"></div></el-col>
                <el-col :span="12"><div class="grid-content bg-purple-light">
                  <el-row>
                    <el-col :span="12"><div class="grid-content bg-purple"></div></el-col>
                    <el-col :span="12" class="addacter"><div class="grid-content bg-purple-light">
                      <el-button type="primary" @click="addcaten()">增加控制点</el-button>
                    </div></el-col>
                  </el-row>
                </div></el-col>
              </el-row>
            </div></el-col>
          </el-row>-->
        </div>
        <div class="equmemot upolence">
          <el-table :data="list3" style="width: 100%" class="ty-table-control ty-table">
            <el-table-column prop="optionName" label="名称" width="" align="center"></el-table-column>
            <el-table-column prop="unit" label="单位" width="" align="center"></el-table-column>
            <el-table-column label="值的可设置范围" width="" align="center">
              <template #default="scope">
                {{ scope.row.lowerLimit }}~{{ scope.row.upperLimit }}
              </template>
            </el-table-column>
            <el-table-column label="最后的编辑" width="" align="center">
              <template #default="scope">
                <!--              {{ scope.row.updateName }}&nbsp;{{ formatTimestamp(scope.row.updateDate) }}-->
                {{ scope.row.updateName }}&nbsp;{{ scope.row.updateDate }}
              </template>
            </el-table-column>
            <el-table-column label="图片" width="" align="center">
              <template #default="scope">
                <span class="ty-color-blue" :class="{ picte: isActive2}" v-for="(item, index) in scope.row.pics" :key="index" @click="PreviewImg(item)">图片{{index+1}}</span>
                <!--              <span class="ty-color-blue" :class="{ picte: isActive2}" @click="PreviewImg(scope.row)" v-if="pic1 === 1">图片1</span>-->
                <!--              &lt;!&ndash;              <img v-if="showImage" :src="imageUrl1" alt="预览图片" style="width: 100%;">&ndash;&gt;-->
                <!--              &lt;!&ndash;              <el-image ref="elImage" style="width: 0;height: 0;" :src="bigImageUrl"&ndash;&gt;-->
                <!--              &lt;!&ndash;                        :preview-src-list="logicImageList"></el-image>&ndash;&gt;-->
                <!--              <span class="ty-color-blue" :class="{ picte: isActive2}" v-if="pic1 === 2">图片2</span>-->
                <!--              <img v-if="showImage2" :src="imageUrl2" alt="预览图片" style="width: 100%;">-->
                <!--              <el-image-viewer-->
                <!--                  style="width:100px;height: 100px;display: none;" v-if="state.imgViewerVisible"-->
                <!--                  @close="closeImgViewer" :url-list="state.srcList"-->
                <!--              ></el-image-viewer>-->
                <!--              <span class="ty-color-blue" @click="" :class="{ picte: isActive2}" v-if="pic1 === 1">图片1</span>-->
                <!--              <span class="ty-color-blue" @click="" :class="{ picte: isActive2}" v-if="pic2 === 2">图片2</span>-->
              </template>
            </el-table-column>
            <el-table-column label="操作" width="" align="center">
              <template #default="scope">
                <span class="ty-color-blue" @click="updateAct(scope.row)">修改</span>
                <span class="ty-color-red" @click="detteAct(scope.row)">删除</span>
              </template>
            </el-table-column>
          </el-table>
          <!--<table class="ty-table ty-table-control">
            <tr>
              <td>名称</td>
              <td>单位</td>
              <td>值的可设置范围</td>
              <td>最后编辑</td>
              <td>图片</td>
              <td>操作</td>
            </tr>
                                <tr v-for="(item3,index3) in list3" :key="index3">
                                  <td>{{ item3.option_name }}</td>
                                  <td>{{ item3.unit }}</td>
                                  <td>{{ item3.lower_limit }}-{{ item3.upper_limit }}</td>
                                  <td>{{ item3.update_date }}</td>
                                  <td></td>
                                  <td>
                                    <span class="ty-color-blue funBtn" @click="">修改</span>
                                    <span class="ty-color-red funBtn" @click="">删除</span>
                                  </td>
                                </tr>
          </table>-->
        </div>
      </div>


      <TyDialog v-if="delVisible" width="800" dialogTitle="什么是“装备器具的控制点”？" color="blue" :dialogHide="hideFun">
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="hideFun">知道了</el-button>
        </template>
        <template #dialogBody>
          <div>
            <p style="text-align: left;padding: 12px 57px;">
            <span>
              装备器具上往往带有仪表或类似需设置参数的设施，在Wonderss系统内，这些仪表或类似设施称为“控制点”。
            </span>
              <br />
              <br />
              <span>
              1 向Wonderss系统录入这些控制点时，建议将参数的单位与可设定范围也录入进来。
            </span>
              <br />
              <br />
              <span>
              2 对某参数而言，不同的产品往往需要不同的设定区间。在Wonderss系统内编制制造规范时，选择了某装备器具的产品需确定该装备各项参数的设定区间。
            </span>
              <br />
              <br />
              <span>
              3 有些参数设定后，用户可以读取其实际的值。对于能读取实际值的参数，管理者可要求操作者操作时在一些场景下按一定的频次读取并记录参数的实际值。
            </span>
              <br />
              <br />
              <span>
              4 需记录参数实际值的常见场景：
              <br />
              ——最初开始生产时；
              <br />
              ——换班后；
              <br />
              ——生产过程中设备/模具更换或调整参数后；
              <br />
              ——更换操作人员时。
            </span>
            </p>
          </div>
        </template>
      </TyDialog>
      <TyDialog v-if="delVisible2" width="642" :dialogTitle="dialogTitle" color="blue" :dialogHide="hideFun">
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="hideFun">取消</el-button>
          <el-button class="bounce-ok" @click="tipOk2(1)" type="primary" v-if="actior === 1" id="adda">确定</el-button>
          <el-button class="bounce-ok" @click="tipOk2(0)" type="primary" v-if="actior === 0" id="upa">确定</el-button>
        </template>
        <template #dialogBody>
          <div>
            <el-row>
              <el-col :span="14"><div class="grid-content bg-purple">
                控制点<span class="ty-color-red">*</span>
              </div></el-col>
              <el-col :span="10"><div class="grid-content bg-purple-light">
                <el-row>
                  <el-col :span="8"><div class="grid-content bg-purple"></div></el-col>
                  <el-col :span="8"><div class="grid-content bg-purple-light"></div></el-col>
                  <el-col :span="8"><div class="grid-content bg-purple">
                    <el-row>
                      <el-col :span="12"><div class="grid-content bg-purple"></div></el-col>
                      <el-col :span="12"><div class="grid-content bg-purple-light cent">
                        <el-button type="text" @click="addcatmn()" class="choone">增加</el-button>
                      </div></el-col>
                    </el-row>
                  </div></el-col>
                </el-row>
              </div></el-col>
            </el-row>
            <el-select v-model="value2" placeholder="请选择">
              <el-option
                  v-for="item in options2"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
          <br />
          <div>
            <el-row>
              <el-col :span="14"><div class="grid-content bg-purple">
                （需在仪表上或类似设施上设置的）参数的单位
              </div></el-col>
              <el-col :span="10"><div class="grid-content bg-purple-light">
                <el-row>
                  <el-col :span="8"><div class="grid-content bg-purple"></div></el-col>
                  <el-col :span="8"><div class="grid-content bg-purple-light"></div></el-col>
                  <el-col :span="8"><div class="grid-content bg-purple">
                    <el-row>
                      <el-col :span="12"><div class="grid-content bg-purple"></div></el-col>
                      <el-col :span="12"><div class="grid-content bg-purple-light cent">
                        <el-button type="text" @click="additionalunit" class="choone">增加</el-button>
                      </div></el-col>
                    </el-row>
                  </div></el-col>
                </el-row>
              </div></el-col>
            </el-row>
            <el-select v-model="value3" placeholder="请选择">
              <el-option
                  v-for="item in options3"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
          <br />
          <div>
            <el-row style="margin-bottom: 10px;">
              <el-col :span="16"><div class="grid-content bg-purple">
                （需在仪表上或类似设施上设置的）参数的可设置范围
              </div></el-col>
              <el-col :span="8"><div class="grid-content bg-purple-light"></div></el-col>
            </el-row>
            <el-row>
              <el-col :span="8"><div class="grid-content bg-purple">
                <el-input
                    placeholder="请输入内容"
                    v-model="minAmount"
                    @input="formatNum"
                    type="text"
                >
                </el-input>
              </div></el-col>
              <el-col :span="8"><div class="grid-content bg-purple-light">
                <el-row>
                  <el-col :span="8"><div class="grid-content bg-purple"></div></el-col>
                  <el-col :span="8"><div class="grid-content bg-purple-light">
                    <el-row>
                      <el-col :span="12"><div class="grid-content bg-purple"></div></el-col>
                      <el-col :span="12"><div class="grid-content bg-purple-light">至</div></el-col>
                    </el-row>
                  </div></el-col>
                  <el-col :span="8"><div class="grid-content bg-purple"></div></el-col>
                </el-row>
              </div></el-col>
              <el-col :span="8"><div class="grid-content bg-purple">
                <el-input
                    placeholder="请输入内容"
                    v-model="text2"
                    @input="formatNum2"
                    type="text"
                >
                </el-input>
              </div></el-col>
            </el-row>
          </div>
          <br />
          <div>
            <el-row>
              <el-col :span="14"><div class="grid-content bg-purple">
                图片
              </div> </el-col>
              <el-col :span="10"><div class="grid-content bg-purple-right"></div></el-col>
            </el-row>
            <el-row style="margin-bottom: 20px;margin-top: 10px;">
              <el-col :span="24"><div class="grid-content bg-purple-dark ty-color-blue" id="httpen">
                注：可上传两张，内容可为该控制点的实际样式，或该控制点在设备上的位置，等等
              </div></el-col>
            </el-row>
            <div class="usent">
              <el-upload :headers="imgUpload1.uploadHeaders" :action="imgUpload1.uploadAction" :accept="imgUpload1.uploadLyc"
                         :data="imgUpload1.postData" :on-success="stepImgSuccess" :on-preview="setpImgPreview" list-type="picture-card"
                         class="usent2" :limit="2" :on-exceed="handleExceed" :on-remove="stepImgRemve" :file-list="fileList">
                <div class="wrodern" :class="{ active: isActive }">
                  <el-button type="text" class="choone">上传</el-button>
                </div>
              </el-upload>
            </div>
          </div>
        </template>
      </TyDialog>
      <TyDialog v-if="delVisible8" width="500" dialogTitle="！提示" color="red" :dialogHide="hideFun8">
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="hideFun8">取消</el-button>
          <el-button class="bounce-ok" @click="tipOk8(1)" type="primary" v-if="speshall === 0">确定</el-button>
          <el-button class="bounce-ok" @click="tipOk8(2)" type="primary" v-if="speshall === 1">确定</el-button>
          <el-button class="bounce-ok" @click="tipOk8(3)" type="primary" v-if="speshall === 2">确定</el-button>
        </template>
        <template #dialogBody>
          <div class="ty-center" style="margin-top: 25px;">{{ messagend }}</div>
        </template>
      </TyDialog>
      <TyDialog v-if="delVisible3" width="600" dialogTitle="增加一个控制点选项" color="blue" :dialogHide="hideFun2">
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="hideFun2">取消</el-button>
          <el-button class="bounce-ok" @click="addsuren" type="primary">确定</el-button>
        </template>
        <template #dialogBody>
          <div>
            <el-row>
              <el-col :span="12"><div class="grid-content bg-purple"></div></el-col>
              <el-col :span="12"><div class="grid-content bg-purple-light">
                <el-row>
                  <el-col :span="8"><div class="grid-content bg-purple"></div></el-col>
                  <el-col :span="8"><div class="grid-content bg-purple-light"></div></el-col>
                  <el-col :span="8"><div class="grid-content bg-purple">
                    <el-row>
                      <el-col :span="10"><div class="grid-content bg-purple"></div></el-col>
                      <el-col :span="14"><div class="grid-content bg-purple-light">
                        <el-button type="text" class="choone" @click="initSttarn(1)">选项管理</el-button>
                      </div></el-col>
                    </el-row>
                  </div></el-col>
                </el-row>
              </div></el-col>
            </el-row>
            <div class="line addpaccho"></div>
            <el-row class="addpactwo">
              <el-col :span="12"><div class="grid-content bg-purple">
                要增加的选项：
              </div></el-col>
              <el-col :span="12"><div class="grid-content bg-purple-light"></div></el-col>
            </el-row>
            <el-input v-model="inputCon" placeholder="-请录入-" :maxlength="20"></el-input>
            <p style="display: none;" ref="answern">{{ inputCon }}</p>
          </div>
        </template>
      </TyDialog>
      <TyDialog v-if="delVisible4" width="640" dialogTitle="控制点选项管理" color="blue" :dialogHide="hideFun4">
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="hideFun4">关闭</el-button>
        </template>
        <template #dialogBody>
          <div>
            <el-row>
              <el-col :span="12"><div class="grid-content bg-purple choopont">
                可选用的选项如下：
              </div></el-col>
              <el-col :span="12"><div class="grid-content bg-purple-light">
                <el-row>
                  <el-col :span="8"><div class="grid-content bg-purple"></div></el-col>
                  <el-col :span="8"><div class="grid-content bg-purple-light"></div></el-col>
                  <el-col :span="8"><div class="grid-content bg-purple">
                    <el-row>
                      <el-col :span="6"><div class="grid-content bg-purple"></div></el-col>
                      <el-col :span="18"><div class="grid-content bg-purple-light">
                        <el-button type="text" class="choone" @click="stopchone(0)">停用的选项</el-button>
                      </div></el-col>
                    </el-row>
                  </div></el-col>
                </el-row>
              </div></el-col>
            </el-row>
            <div>
              <!--createName createDate是创建数据，updateName enabledTime是启用数据-->
              <el-table :data="list4" style="width: 100%;" class="ty-table-control ty-table ty-table-headerLine">
                <el-table-column prop="name" label="选项" width="" align="center"></el-table-column>
                <el-table-column label="启用/创建" width="" align="center">
                  <template #default="scope">
                    {{ scope.row.createName }}&nbsp;{{ formatTimestamp(scope.row.createDate) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="" align="center">
                  <template #default="scope">
                    <span class="ty-color-blue" @click="stopuse(scope.row,'stop')">停用</span>
                  </template>
                </el-table-column>
              </el-table>
              <!--<table class="ty-table ty-table-control">
                <tr>
                  <td>选项</td>
                  <td>启用/创建</td>
                  <td>操作</td>
                </tr>
                <tr v-for="(item4,index4) in list4" :key="index4">
                  <td>--</td>
                  <td>--</td>
                  <td>
                    <span class="ty-color-blue funBtn" @click="">停用</span>
                  </td>
                </tr>
              </table>-->
            </div>
          </div>
        </template>
      </TyDialog>
      <TyDialog v-if="delVisible5" width="640" dialogTitle="停用的选项" color="blue" :dialogHide="hideFun5">
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="hideFun5">关闭</el-button>
        </template>
        <template #dialogBody>
          <div>
            <el-row>
              <el-col :span="12"><div class="grid-content bg-purple choopont">
                停用的选项如下
              </div> </el-col>
              <el-col :span="12"><div class="grid-content bg-purple-light">
                <el-row>
                  <el-col :span="8"><div class="grid-content bg-purple"></div> </el-col>
                  <el-col :span="8"><div class="grid-content bg-purple-light"></div></el-col>
                  <el-col :span="8"><div class="grid-content bg-purple">
                    <el-row>
                      <el-col :span="6"><div class="grid-content bg-purple"></div></el-col>
                      <el-col :span="18"><div class="grid-content bg-purple-light">
                        <el-button type="text" class="choone" @click="batchactivation">批量启用</el-button>
                      </div></el-col>
                    </el-row>
                  </div></el-col>
                </el-row>
              </div> </el-col>
            </el-row>
            <div>
              <el-table :data="list5" style="width:100%;" class="ty-table-control ty-table ty-table-headerLine">
                <el-table-column prop="name" label="选项" width="" align="center"></el-table-column>
                <el-table-column prop="" label="停用" width="" align="center">
                  <template #default="scope">
                    <!--                  {{ scope.row.updateName }}&nbsp;{{ formatTimestamp(scope.row.enabledTime) }}-->
                    {{ scope.row.updateName }}&nbsp;{{ scope.row.enabledTime }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="" align="center">
                  <template #default="scope">
                    <span class="ty-color-blue" @click="stopuse(scope.row,'str')">启用</span>
                  </template>
                </el-table-column>
              </el-table>
              <!--<table class="ty-table ty-table-control">
                <tr>
                  <td>选项</td>
                  <td>停用</td>
                  <td>操作</td>
                </tr>
                <tr v-for="(item5,index5) in list5" :key="index5">
                  <td>--</td>
                  <td>--</td>
                  <td>
                    <span class="ty-color-blue funBtn" @click="">启用</span>
                  </td>
                </tr>
              </table>-->
            </div>
          </div>
        </template>
      </TyDialog>
      <TyDialog v-if="delVisible6" width="607" dialogTitle="批量启用" color="blue" :dialogHide="hideFun6">
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="hideFun6">取消</el-button>
          <el-button class="bounce-ok" @click="tipOk6" type="primary">确定</el-button>
        </template>
        <template #dialogBody>
          <div>
            <el-row style="align-items: center;">
              <el-col :span="14"><div class="grid-content bg-purple">
                请选择要启用的控制点，之后点击“确定”。
              </div> </el-col>
              <el-col :span="10"><div class="grid-content bg-purple-light">
                <el-row>
                  <el-col :span="8"><div class="grid-content bg-purple"></div> </el-col>
                  <el-col :span="6"><div class="grid-content bg-purple-light"></div> </el-col>
                  <el-col :span="10"><div class="grid-content bg-purple">
                    已选：<span>{{ multipleSelection.length }}</span>个
                  </div> </el-col>
                </el-row>
              </div> </el-col>
            </el-row>
            <div class="addmore">
              <el-table
                  ref="multipleTable"
                  :data="tableData"
                  tooltip-effect="dark"
                  style="width: 100%"
                  @selection-change="handleSelectionChange"
                  class="ty-table-control"
              >
                <el-table-column type="selection" width="55" align="center"> </el-table-column>
                <el-table-column prop="name" label="选项" width="250" align="center">
                </el-table-column>
                <el-table-column label="停用" width="250" align="center">
                  <template #default="scope">
                    {{ scope.row.updateName }}&nbsp;{{ formatTimestamp(scope.row.enabledTime) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </template>
      </TyDialog>
      <TyDialog v-if="delVisible7" width="450" dialogTitle="新增计量单位" color="green" :dialogHide="hideFun7">
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="hideFun7">取消</el-button>
          <el-button class="bounce-ok" @click="tipOk7" type="success">确定</el-button>
        </template>
        <template #dialogBody>
          <div>
            <p class="ty-center" style="margin-bottom: 15px;">计量单位</p>
            <el-input v-model="input4" placeholder="--请录入计量单位的名称--" id="unitName" style="padding: 0 119px;"
                      :maxlength="4"></el-input>
            <p style="display: none;" ref="unitern">{{ input4 }}</p>
          </div>
        </template>
      </TyDialog>
      <TyDialog v-if="delVisible9" width="845" dialogTitle="预览图片" color="green" :dialogHide="hideFun9">
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="hideFun9">关闭</el-button>
        </template>
        <template #dialogBody>
          <div>
            <img :src="realPath(picsd)" style="width: 800px;height: 600px;">
          </div>
        </template>
      </TyDialog>
    </div>
  </div>
</template>
<script>
import auth from '@/sys/auth'
import demoMixin from'@/mixins/demoMixin.js'
import { beforeRouteLeave, getActiveData, initNav } from "@/utils/routeChange"
import {
  addcontentd,
  addUnatOk, chankemadse, dettentd,
  getUnitlank,
  havelookern,
  initActtionarn,
  initCractern2,
  stopChcent,
  stopxxest, upcated
} from "../../api/machineEquipment";
import {initStatton} from "../../api/machineEquipment";
import {addname} from "../../api/machineEquipment";
//import TyDemo from "@/components/TyDemo.vue"
import { defineComponent, ref } from 'vue'
// import TyDialog from "../../components/TyDialog";


export default {
  mixins: [demoMixin],
  data() {
    return {
      pageName:'initialSettings',//可以跟文件名相同
      pageShow: true,
      pageInfo:{ 'currentPageNo': 1, 'pageSize': 20, 'totalPage': 15  },
      // showImagePreview: false,
      // previewList: [],
      listsg:[{}],//首页一类装备器具的数据
      isActive: false,//判断是否给增加控制点弹窗中的图片上传按钮增加active样式
      isActive2: false,//判断是否给控制点列表中的图片按钮增加picte样式
      isActive3: false,
      dialogTitle: "",//判断是弹窗的标题是’增加控制点‘还是’修改控制点‘
      actior: '',//1-增加控制点，0-修改控制点
      actid: '',//设备id
      detenid: '',//需要删除的那条控制点的控制点id
      uptid: '',//需要修改的那条控制点的控制点Id
      mind: '',//型号id
      list: [{}],//首页的装备器具数据
      listd:[{}],//’已设置的控制点‘页面中顶部部分
      list2: [],//装备器具详情页的表格数据
      list3: [{
        option_name: '器具1',
        unit: 2,
        lower_limit: 0,
        upper_limit: 2,
        update_date: null
      }],//’已设置的控制点‘页面中表格里的数据
      list4: [{}],//控制点选项管理数据
      list5: [{}],//停用的选项数据
      list6: [{}],//装备器具详情页顶部部分
      choslist: {},//批量启用弹窗中选择的多个停用数据
      // editObj: null ,
      delVisible: null ,//控制’什么是“装备器具的控制点”？‘是否展示
      delVisible2: null,//控制’增加控制点‘和’修改控制点‘弹窗是否显示
      delVisible3: null,//控制’增加一个控制点选项‘弹窗是否显示
      delVisible4: null,//控制’控制点选项管理‘弹窗是否显示
      delVisible5: null,//控制’停用的选项‘弹窗是否显示
      delVisible6: null,//控制’批量启用‘弹窗是否显示
      delVisible7: null,//控制’新增计量单位‘弹窗是否显示
      delVisible8: null,//控诉’提示‘弹窗是否显示
      delVisible9: null,
      options: [],//首页筛选框的全部数据
      options2: [
        // {value: 1, label: '温度1'},//默认启用状态
        // {value: 2,label: '温度2'},//默认启用状态
        // {value: 3,label: '温度3'},
        // {value: 4,label: '压力1'},//默认启用状态
        // {value: 5,label: '压力2'},
        // {value: 6,label: '压力3'},
        // {value: 7,label: '速度1'},
        // {value: 8,label: '速度2'},
        // {value: 9,label: '速度3'},
        // {value: 10,label: '时间1'},
        // {value: 11,label: '时间2'},
        // {value: 12,label: '时间3'},
        // {value: 13,label: '湿度1'},
        // {value: 14,label: '湿度2'}
      ],//’增加控制点‘和’修改控制点‘弹窗中的’控制点‘筛选框中的全部数据
      options3: [
        // {
        //   value: '选项1',
        //   label: '全部'
        // }
      ],//'计量单位’全部数据
      pageInfo2d : {},//分页数据
      value: null,//首页筛选框选中的value值
      value2: '',//’增加控制点‘和’修改控制点‘弹窗中的’控制点‘筛选框中的value值
      // choseKondlist: {},
      value3: '',//计量单位筛选框的value值
      input4: '',//’请录入计量单位的名称‘输入框中输入的值
      condition: 0,//用于实现多个div的隐藏和显示
      radio1: null,//判断现在勾选的是1，2，3，4哪一个
      radio2: null,//判断勾选的是1还是2
      conhide: 0,//控制1，2，3，4哪个显示哪个隐藏
      mdeune: 1,//控制’已设置的控制点‘页面中右上角的确定按钮是否可以点击及按钮的颜色
      actacon: 0,//控制提示文字是显示0还是1
      labelr: 0,//首页筛选框中选中的数据的值
      text: ref(''),//代表输入框或按钮的类型，这个类型是文字
      minAmount: ref(''),//设置的下限数据
      text2: ref(''),//设置的上限数据
      // value1: 0,
      fileList: [],//点击上传按钮手动选择的图片
      inputCon: '',//'要增加的选项’输入框中填写的数据
      name: "",
      action: '',//已设置的控制点数量
      tableData: [
        // {
        //   date: '选项1',
        //   name: 'XXX 2024-04-22 10:20:30'
        // }
      ],//批量启用表格中的数据
      multipleSelection: [],//批量启用弹窗中选中的需要启用的数据
      imgUpload1:{
        uploadHeaders:{ 'Token': auth.getToken() },
        uploadAction: auth.webRoot + '/uploads/uploadfyByFile.do',
        uploadLyc: '.png,.jpeg,.jpg,.gif',
        postData: {
          module:'控制点图片上传',userId: auth.getUserID()
        }
      },//上传图片
      pic1: 0,
      pic2: 0,
      // showImage: false,//控制图片是否显示
      // imageUrl1: '',//图片url
      // showImage2: false,
      // imageUrl2: '',
      // bigImageUrl: '',
      // logicImageList: []
      imgViewerVisible: false,//判断图片是否显示
      // state: {
      //   srcList:[],//图片1
      //   srcList2: []//图片2
      // },//包含图片地址的数组
      picsd : null,
      pics2: null,
      messagend: '',
      speshall: '',
      choosen: '',
      //diftent:0,//用于区分是初次设置还是第二次及以后设置，便于区分是什么时候进行的设置
      point1: '',
      point2: '',
    }
  },
  mounted(){// 页面刷新后立即执行的函数
    this.starten();
  },
  computed: {
    list(){//用于对展示list数据的表格进行筛选
      if(!this.value){
        //如果没有选择筛选条件，返回全部数据
        return this.list;
      }
      //根据筛选条件返回过滤后的数据
      console.log('this.value的值',this.value);
      console.log('获取到值了么',this.labelr);
      return this.list.filter(item => item.category === this.value);//对比值，将list数组中的数据进行筛选
    }
  },
  components: {
    // TyDialog
    //TyDemo, // 需要在 子组件 里注明 当前页面 引用了此组件
  },
  beforeRouteLeave(to, from, next) {
    beforeRouteLeave(to, from, next,this)
  },
  created(){
    // 页面初始化方法，写在 created 里或者 mounted 里
    // 用于初始化页面数据
    // params：
    // ------ menuInfo : {mid: '菜单的mid', name: 'tab栏显示的名称', pageName: '一般与data里的pageName一致，最好是vue的名字，见名知意'}
    // ------ loadDataFun : 所有的首次进入需要初始化的数据都写在这个方法里
    // ------ scopeThis : 当前作用域
    initNav({mid: 'qv', name: '初始设置', pageName: this.pageName}, this.initCreated, this)

    // console.log('auth', auth)
  },
  destroyed () {

  },
  methods: {// 这里编写你的刷新后立即执行的代码
    //返回首页
    madesurebck(){
      //console.log('现在有值么',this.mind);
      const keyone = this.conhide;
      const jsonm = {
        id: this.mind,
        state: ''
      };
      if(keyone === 0){jsonm.state = 1;}else if(keyone === 1){jsonm.state = 2;}
      else if(keyone === 2){jsonm.state = 3;}else if(keyone === 3){jsonm.state = 4;}
      console.log(jsonm);
      if(keyone === null){}else{
        this.chankense(jsonm);
      }
    },
    chankense(jsonm){
      chankemadse(jsonm).then(res => {
        let msg = res.data.msg;
        msg = msg === '操作成功' ? this.condition = 0 : this.condition = 2;
        const json = {
              currentPageNo: 1,
              pageSize: 20,
              category: 0
            };
        this.initCreatact(json);
        // if(this.point1 !== -1){
        //   const json = {
        //     currentPageNo: 1,
        //     pageSize: 20,
        //     category: 0
        //   };
        //   this.initCreatact(json);
        // }
        //点击右上角的确定按钮成功传值后给diftent赋值，但是得判断不同的id吧
        //this.diftent = null;
      })
    },
    //修改控制点
    updateAct(scprn){
      const orn3 = "修改控制点";
      this.dialogTitle = orn3;
      this.actior = 0;//0代表修改控制点
      //scprn代表修改按钮对应那行的数据
      this.uptid = scprn.id;//修改那行的控制点id
      const optionName = scprn.optionName;//控制点名称
      this.value2 = optionName;
      const unit = scprn.unit;//单位
      this.value3 = unit;
      scprn.lowerLimit = scprn.lowerLimit === ref('') ? 0 : scprn.lowerLimit;
      scprn.upperLimit = scprn.upperLimit === ref('') ? 0 : scprn.upperLimit;
      if(scprn.lowerLimit > scprn.upperLimit){
        const lowerLimit = scprn.lowerLimit;
        const upperLimit = scprn.upperLimit;
        this.minAmount = upperLimit;
        this.text2 = lowerLimit;
      }else if(scprn.lowerLimit < scprn.upperLimit){
        const lowerLimit = scprn.lowerLimit;
        const upperLimit = scprn.upperLimit;
        this.minAmount = lowerLimit;
        this.text2 = upperLimit;
      }
      // const lowerLimit = scprn.lowerLimit;//下限数据
      // this.minAmount = lowerLimit;
      // const upperLimit = scprn.upperLimit;//上限数据
      // this.text2 = upperLimit;
      const jsont = {
        enabled: 1
      };
      this.initStation(jsont,2);
      const jsond = {
        module: 12
      };
      this.getUnitlist(jsond);
      this.isActive = false;
      const piclink = scprn.pics === null ? "" : scprn.pics;//图片
      console.log('8.15',piclink);//piclink是个数组，是没改变图片地址的
      for(let p in piclink){
        piclink[p] = {
          name: 1,
          url: this.realPath(piclink[p]),
          normalPath: piclink[p]
        };
      }
      this.fileList = piclink === "" ? [] : piclink;
      //1,2
      // console.log(typeof piclink);//piclink是字符串
      // const boxlank = piclink.split(",");
      // console.log(boxlank);
      //this.fileList = piclink;//获取到了调用接口获取的图片地址，下面该将获取到的图片展示在弹窗中
      //piclink现在是包含需要展示的图片地址的数组
      this.delVisible2 = true;
    },
    //删除控制点
    detteAct(det){
      this.delVisible8 = true;
      this.messagend = '确定删除这个控制点吗？';
      const did = det.id;//需要删除的控制点的Id
      this.detenid = did;
      this.speshall = 2;
    },
    setpImgPreview(file){//这里是涉及到图片上传的功能的部分
      this.setpImgUrl = file.url;
    },
    stepImgRemve(resp,file,fileList){//图片移除时调用的函数
      fileList = fileList === undefined ? [] : fileList;//从fileList中将当前选择的图片删掉
      console.log('现在的值是多少',fileList.length);
      if(fileList.length == 0){
        this.isActive = false;
      }else{
        this.isActive = true;
      }
      this.fileList = fileList;
    },
    stepImgSuccess(resp,file,fileList){//这里是涉及到图片上传的功能的部分
      resp.url = auth.webRoot + '/upload/' + resp.filename;//将获取到的图片地址转换为可以展示的地址
      resp.normalPath = resp.filename;
      fileList[fileList.length - 1] = resp;
      console.log('fileList现在的数据8.15',fileList);
      this.fileList = fileList;
      this.isActive = true;
    },
    handleExceed(){//图片大于2张时给予提示
      //提示最多只能上传2个
      this.$message.error('图片数量最多为2张');
    },
    // beforeUpload(file){
    //   return new Promise(async (resolve,reject) => {
    //     if(this.fileList.length >= 3){
    //       this.$message.error("最多上传三张图片");
    //       return false;
    //     }
    //   })
    // },
    starten(){
      const starjson = {
        'pageSize': 20,
        'currentPageNo': 1,
        'category': 0
      };
      this.initCreatact(starjson);
    },
    getLabel(value){//用于实时监听el-select的值的改变，并获取相应的label，用于其他函数进行读取
      console.log('7.12-1',value);
      let obj = {};
      obj = this.options.find((item) => {
        return item.value === value;//取出分类名称对应的id
      });
      console.log('现在获取的值7.12',obj.label);
      this.labelr = obj.label;
    },
    havelooktc(){

    },
    pageClick(pageItem){
      // pageItem 参考值如下：
      // pageItem = { 'name': '尾页', 'page': 30 , 'klass':'ye', 'type':'last' }
      // pageItem = { 'name': i, 'page': 3 , 'klass':'yecur', 'type':'curPage' }
      // pageItem = { 'name': i, 'page': 4 , 'klass':'ye', 'type':'normal' }
      // pageItem = { 'name': '首页', 'page':1, 'klass':'ye', 'type':'first' }
      //pageInfo
      let data = {//用于进行分页功能实现
        pageSize: 20 ,
        currentPageNo: pageItem.currentPageNo
      }
      console.log('8.27-1',data);
      this.initCreated(data);
    },
    initCreated(data){//获取调用接口时需要传入的数值
      // let params = { 'currentPageNo': 1 , 'pageSize': 20 , 'category': 0  }
      //params.category = 0;
      console.log('8.27',data);
      if(data === undefined){}else{
        console.log('8.27',data);
        data.category = 0;
        console.log('8.27',data);
        this.initCreatact(data);
      }
      // let json1 = { 'model': 1};
      // this.initActionact(json1);
      // let json2 = { 'enable': 1};
      // this.initStation(json2);
    },
    initCreatact(params){//用于调用接口传值
      params.init = 1;
      console.log('params现在的数据8.27',params);
      initCractern2(params).then(res => {
        console.log('现在有值么7.10',res);
        console.log(res.data.data);
        this.listsg = [];
        const json1 ={
          'lenth': '',
          'catment': res.data.data
        };
        this.listsg.push(json1);
        console.log(this.listsg);
        this.list = res.data.data;
        this.pageInfo2d = res.data.pageInfo;
        const lank1 = this.list;
        for(let a = 0; a<lank1.length;a++){
          const equipmentName = lank1[a].equipmentName;
          if(equipmentName == null){
            lank1[a].equipmentName = "--";
          }
          const modelName = lank1[a].modelName;
          if(modelName == null){
            lank1[a].modelName = "--";
          }
          const supplierName = lank1[a].supplierName;
          if(supplierName == null){
            lank1[a].supplierName = "--";
          }
          const units = lank1[a].units;
          if(units == null){
            lank1[a].units = "--";
          }
          const emCount = lank1[a].emCount && lank1[a].emCount !== "" ? lank1[a].emCount.split(","): "--";
          lank1[a].emCount = emCount.length;
          const point_state = lank1[a].point_state;
          console.log('point_state的数据9.5',point_state);
          if(point_state === -1){
            lank1[a].point_state = '尚未设置';
            this.point1 = -1;
            //console.log('9.5现在的数据',this.diftent);
            //this.diftent = 0;//代表这条数据是初次设置
            //console.log('9.5现在的数据',this.diftent);
          }else if(point_state === 0){
            lank1[a].point_state = '已设置控制点0个，设置完毕！';
            //this.point1 = '';
            //this.diftent = 1;//代表这条数据是第二次及以后设置了
          }else if(point_state === -2){
            lank1[a].point_state = '不在此处设置';
            //this.diftent = 1;//代表这条数据是第二次及以后设置了
          }else{
            lank1[a].point_state = '已设置控制点'+lank1[a].point_state+'个，设置完毕！';
            //this.point1 = '';
            //this.diftent = 1;//代表这条数据是第二次及以后设置了
          }
          // if(point_count === 0){
          //   lank1[a].point_count = '尚未设置';
          // }else if(point_count >= 1){
          //   lank1[a].point_count = '已设置控制点'+lank1[a].point_count+'个，设置完毕！';
          // }else if(point_count === null){
          //   lank1[a].point_count = '不在此处设置';
          // }
        }
        this.options = [];
        if(lank1.length == 0){
          //const jsonk = {
            // equipmentName: "装备器具一",
            // modelName: 3,
            // supplierName: "未知",
            // units: 2,
            // emCount: 2,
            // point_count: 8,
            // id: 12
          //};
          this.list = [];
          // const jsonw = {
          //   value: '选项1',
          //   label: '全部'
          // };
          // this.options.push(jsonw);
        }else{
          this.list = lank1;
          const lank2 = lank1.map(item =>({ name: item.categoryName,id: item.category}));
          const json1 = {
            value: 0,
            label: '全部'
          };
          this.options.push(json1);
          console.log('lank2的顺序',lank2);//lank2中的数据是按照时间顺序倒序排的，最新的一条数据索引值是0
          //const newbank = lank2.slice().reverse();//将数组调整成倒序顺序
          const newbank = lank2;
          console.log('newbank的值是倒序成功的么',newbank);
          for(let b in newbank){//感觉应该把它倒序下
            b = Number(b);
            const json2 = {
              value: newbank[b].id,
              label: newbank[b].name
            };
            this.options.push(json2);
          }
          console.log('this.options的值9.4',this.options);
          //console.log('9.5现在的数据',this.diftent);
          //这个位置需要对options中的数据进行去重
          let uniqueArray = Array.from(new Map(this.options.map(item => [item.value, item])).values());
          console.log('现在的数据9.4',uniqueArray);
          this.options = uniqueArray;
        }
        console.log('this.list现在的数据',this.list);
        this.list.curPage = res.data.pageInfo.currentPageNo;
        this.list.pageSize = res.data.pageInfo.pageSize;
        this.list.totalPage = res.data.pageInfo.totalPage;
        json1.lenth = this.list.length;
        // res = [
        // { name:'张三1', num: 12, fullName: '我就是张三11'  } ,
        // { name:'张三2', num: 12, fullName: '我就是张三22'  } ,
        // { name:'张三3', num: 12, fullName: '我就是张三33'  } ,
        // { name:'张三4', num: 12, fullName: '我就是张三444'  } ,
        // { name:'张三5', num: 12, fullName: '我就是张三555'  }
        //]
        // this.list = newbox;
        // const lank1 = this.list;
        // for(let a1 = 0;a1<lank1.length;a1++){
        //   const modelCount = lank1[a1].modelCount;
        //   if(modelCount == null){
        //     lank1[a1].modelCount = "--";
        //   }else{
        //     lank1[a1].modelCount = lank1[a1].modelCount+"个型号";
        //   }
        //   const supplierCount = lank1[a1].supplierCount;
        //   if(supplierCount == null){
        //     lank1[a1].supplierCount = "--";
        //   }else{
        //     lank1[a1].supplierCount = lank1[a1].supplierCount+"个来源";
        //   }
        //   const units = lank1[a1].units;
        //   if(units == null){
        //     lank1[a1].units = "--";
        //   }
        //   const quantity = lank1[a1].quantity;
        //   if(quantity == null){
        //     lank1[a1].quantity = "--";
        //   }else{
        //     lank1[a1].quantity = lank1[a1].quantity+"种";
        //   }
        // }
        // this.list = lank1;
        //this.extractedField = this.items.map(item => item.name);
        //console.log('this.extractedField现在的值',this.extractedField);
        // this.options = newbox.map(item => item.fullName);
        // console.log('this.options的格式',this.options);
        //   this.pageInfo = { 'currentPageNo': params.currentPageNo , 'pageSize': 20 , 'totalPage': 15  }
        // }).catch(err => {
        //   console.log('err=', err)
      })
    },
    initActionact(oabj1){
      console.log(oabj1);
      initActtionarn(oabj1).then(res => {
        console.log('8.28',res.data);
        if(res.data == undefined || res.data == [] || res.data.length == 0){
          //this.conhide = 0;
          this.list3 = [];
          if(this.choosen === null){}else{
            this.actacon = 0;
          }
          if(this.radio2 === 1){
            this.conhide = 1;
          }else if(this.radio2 === 2){
           this.conhide = 2;
          }else{
            this.point1 === -1 ? this.conhide = 0 : this.conhide = null;
          }
         // this.point1 === -1 ? this.actacon = 0 : this.actacon = 2;
         //  console.log('现在的值9.13',this.actacon);
         //  console.log('现在的值9.13',this.point1);
         //  console.log(this.radio2);
          // if(this.radio2 === 1){this.conhide = 1;}
          if(this.point1 === -1){
            this.actacon = 0;
          }else if(this.point1 === -2){
            this.actacon = 3;
          }else if(this.point1 === -3){
            this.actacon = 2;
          }else{
            this.actacon = 2;
          }
          //this.point2 === -2 ? this.conhide = null : this.conhide = 0;
        }else{
          this.list3 = res.data || [];
          if(this.list3.length > 0){
            //this.conhide = 3;
            this.actacon = 1;
            this.conhide = 3;
            this.action = this.list3.length;
            this.isActive2 = true;
            this.pic1 = 1;
            this.point1 === -1 ? this.conhide = 3 : this.conhide = null;
            this.radio1 = null;
            this.radio2 = '';
            // if(this.list3.length > 1){
            //   this.pic2 = 2;
            // }else if(this.list3.length === 1){
            //   this.pic2 = 0;
            // }
          }else{
            this.pic1 = 0;
            this.pic2 = 0;
            this.actacon = 0;
            this.isActive2 = false;
            this.point1 === -1 ? this.conhide = 0 : this.conhide = null;
          }
          const lank4 = this.list3;
          for(let k in lank4){
            let pics = lank4[k].pics;
            console.log('走到这里了么');
            if(pics ===  null){}else{
              lank4[k].pics = pics.split(',');
              console.log('8.15',pics);
            }
          }
          // let middle = this.state.pics;
          // console.log('8.13',middle);//它现在是个字符串
          // this.state.srcList = middle.split(',');

          //console.log('8.15',this.state.srcList);
          console.log('现在的数据',this.list3);
          let lank3 = this.list3;
          for(let k in lank3){
            let updateName = lank3[k].updateName;
            if(updateName === null){
              updateName = lank3[k].createName;
            }
            lank3[k].updateName = updateName;
            console.log('8.28名字',lank3[k].updateName);
            let updateDate = lank3[k].updateDate;
            if(updateDate === null){
              updateDate = lank3[k].createDate;
            }
            lank3[k].updateDate = updateDate;
            console.log('8.28时间',lank3[k].updateDate);
            const pict3 = lank3[k].pics;
            console.log(pict3);
          }
          //将获取到的数组倒序
          // lank3 = lank3.slice().reverse();
          console.log('lank3现在的格式',lank3);
          this.list3 = lank3;
          const lanks = this.list3;
          console.log(lanks);
          for(let a in lanks){
            if(lanks[a].pics === null){}else{
              const list = lanks.map(item => ({ pics: item.pics}));
              console.log('list现在的格式',list);
              for(let a in list){
                if(a === '0'){
                  this.imageUrl1 = list[a].pics;
                  console.log('this.imageUrl1的值',this.imageUrl1);
                }else if(a === '1'){
                  this.imageUrl2 = list[a].pics;
                  console.log('this.imageUrl2的值',this.imageUrl2);
                }
              }
              console.log('8.15',this.list3);
            }
          }
        }
      }).catch(err => {
        console.log('err=', err);
      })
    },
    addsuren(){
      const content = this.$refs.answern.textContent;//输入框中输入的名称
      console.log(content); // 输出: 这是一个段落。
      console.log(typeof content);//检测数据现在是不是字符串，或者是整型或其他类型
      let stopcon = {enabled: 0};
      let starten = {id: '',enabled: ''};
      initStatton(stopcon).then(res => {//获取停用的数据
        const stoplist = res.data;//停用的数据
        console.log('停用的数据',stoplist);
        for(let s in stoplist){
          if(content === stoplist[s].name){//有重复数据
            starten.id = stoplist[s].id;
            starten.enabled = 1;
            console.log('starten的值n',starten);//只有在里面才能获取到starten的值
            this.useunter(starten,1);
          }else{//没有重复数据
          }
        }
        //     //这时应该将重复的数据启用
        //     let starten ={
        //       id: stoplist[s].id,
        //       enabled: 1
        //     };
        //     stopxxest(starten).then(res => {
        //       const msg = res.data.msg;
        //       if(msg === "操作成功"){
        //         this.delVisible3 = false;//关闭‘增加一个控制点选项’弹窗
        //         this.addcaten();
        //       }else{}
        //     })
        // let obj2 = { 'name': content};
        // console.log(obj2);
        //     addname(obj2).then(res => {
        //       const msg = res.data.msg;
        //       if(msg === "操作成功"){
        //         this.delVisible3 = false;//关闭‘增加一个控制点选项’弹窗
        //       }
        //     })
        //     this.addcaten();
        // if(content === stoplist){//有重复数据
        //   //这时应该先将重复的数据启用
        //
        // }else{//没有重复数据
        //
        // }
      })
      if(starten.id === ''){//没有跟停用的数据重复
        let obj2 = { name: content };
        addname(obj2).then(res => {
          const msg = res.data.msg;
          if(msg === "操作成功"){
            this.delVisible3 = false;//关闭‘增加一个控制点选项’弹窗
            this.addcaten();
          }else{
            this.$message.error(msg);
          }
        })
      }
      // addname(obj2).then(res => {
      //   const msg = res.data.msg;
      //   if(msg === "操作成功"){
      //     this.delVisible3 = false;//关闭‘增加一个控制点选项’弹窗
      //     //这个位置应该有个判断，若新增的选项是停用的选项中的数据，则需要将停用中相应的数据设置成
      //     //启用状态，相当于调用一次启用接口
      //     let stopcon = {
      //       enable: 0
      //     };
      //     initStatton(stopcon).then(res => {//获取停用的数据
      //       const stoplist = res.data;//停用的数据
      //       for(let s in stoplist){
      //         const ustname = stoplist[s].name;
      //         if(content === ustname){//输入框中的名称同‘停用的数据’进行比较，若有重复的，则将重复的数据的Id记下来
      //           //
      //         }
      //       }
      //     })
      //     this.addcaten();
      //   }else{}
      // }).catch(err => {})
    },
    initSttarn(nume){
      if (typeof nume === 'number') {
        console.log(true);
        // nume 是数字
      } else {
        console.log(false);
        // nume 不是数字
      }
      let json2 = { 'enabled': nume};//nume是数字
      this.initStation(json2,1);//控制点选项管理列表数据
      this.delVisible4 = true;
    },
    initStation(obj2,type,starte){
      console.log('obj2的值',obj2);
      initStatton(obj2).then(res => {
        const lank = res.data || [];
        console.log('现在的值',res.data);
        if(type === 1){//控制点选项管理列表数据
          for(let n in lank){
            let updateName = lank[n].updateName;
            if(updateName === null){
              updateName = lank[n].createName;
            }
            lank[n].updateName = updateName;
            let enabledTime = lank[n].enabledTime;
            if(enabledTime === null){
              enabledTime = lank[n].createDate;
            }
            lank[n].enabledTime = enabledTime;
          }
          this.list4 = lank;
          console.log(this.list4);
        }else if(type === 2){//点击增加控制点后获取控制点数据
          console.log('8.2',lank);//新增加的数据
          // 使用reverse()方法 将数组中的元素顺序反转
          //lank.reverse();
          // const tunlink = [
          //     {value: 6,label: '温度1',id: 6,name:'温度1'},
          //     {value: 7,label:'温度2',id: 7,name: '温度2'},
          //     {value:65,label: '压力1',id: 65,name: '压力1'}];
          // console.log('为何无法正常点击选择',tunlink);
          // for(let a in lank){
          //   lank[a].value = lank[a].id;
          //   lank[a].label = lank[a].name;
          // }
          // console.log(lank);
          // if(starte === 0){
          //   this.options2 = tunlink;
          //   for(let b in lank){
          //     b = Number(b);
          //     if(lank[b].id === 6 || lank[b].id === 7 || lank[b].id === 65){
          //       lank.splice(b,1);
          //     }else{
          //       const jsond = { id: lank[b].id};
          //       this.stopuse(jsond,'stop','small');
          //     }
          //   }
          // }else{
          //   this.options2 = lank;
          // }
          this.options2 = lank;
          console.log('8.19.this.options2现在的数据',this.options2);
          // if(starte === 0){
          //   const chose2 = this.options2;
          //   for(let c in chose2){
          //     const id = chose2[c].id;
          //     if(id !== 65 && id !== 7 && id !== 6){
          //       let json = {
          //         id: id,
          //         enabled:0
          //       };
          //       stopxxest(json).then(res => {
          //         if(res.data.msg === "操作成功"){
          //           const json3 = { 'enabled': 1};
          //           this.initStation(json3,3);
          //         }
          //       })
          //     }
          //   }
          // }
        }else if(type === 3){//在成功停用某条数据后获取现启用的数据
          //console.log('停用后的启用数据',lank);
          this.list4 = lank;
          console.log(this.list4);
        }
      }).catch(err => {
        console.log('err=',err);
      })
    },
    stopchone(nume){
      let json3 = { 'enabled': nume};
      this.stopChert(json3);
      this.delVisible5 = true;
    },
    stopChert(obj4){
      initStatton(obj4).then(res => {
        const lanks = res.data;
        console.log('现在的值',res.data);
        this.list5 = lanks;
        for(let n in lanks){
          let updateName = lanks[n].updateName;
          if(updateName === null){
            updateName = lanks[n].createName;
          }
          lanks[n].updateName = updateName;
          let enabledTime = lanks[n].enabledTime;
          if(enabledTime === null){
            enabledTime = lanks[n].createDate;
          }
          lanks[n].enabledTime = enabledTime;
        }
        console.log('8.28',this.list5);
      }).catch(err => {
        console.log('err=',err);
      })
    },
    jumptoanoter(obj1,nemb) {//实现多个div的隐藏和显示
      if(nemb == 0){//返回首页
        this.condition = nemb;
        // if(this.point1 !== -1){
        //   console.log('现在的值9.5',this.mind);
        //   const jsont = {id:this.mind,state:4};
        //   this.chankense(jsont);
        // }
        const jsonc = {
          currentPageNo: 1,
          pageSize: 20,
          category: 0
        };
        this.initCreatact(jsonc);
      }else if(nemb === 1){//点击装备器具名称下面的按钮跳转页面
        this.condition = nemb;
        console.log('obj1的值',obj1);//可以直接读取相应数据
        const jsond = {
          modelName: obj1.modelName,//型号
          equipment: obj1.equipmentId
        };
        console.log('jsond的值',jsond);
        havelookern(jsond).then(res1 => {
          console.log('res1',res1);
          const lankh = res1.data || [];
          this.list6 = [];
          const jsond = {
            higger: "",
            model: obj1.modelName,//型号
            equname: obj1.equipmentName,//名称
            supplierName: obj1.supplierName//来源
          };
          if(lankh.length == 0){
            jsond.higger = 0;
            this.list6.push(jsond);
          }else{//能获取到数据
            jsond.higger = lankh.length;
            this.list6.push(jsond);
          }
          console.log('8.28',lankh);
          this.list2 = lankh;
        })
      }
      else if(nemb === 2){//跳转至’已设置的控制点‘数据展示页面
        //console.log('9.5现在的数据',this.diftent);
        this.condition = nemb;
        console.log('obj1现在的值',obj1);
        this.radio2 = "";
        this.radio1 = "";
        const listbox = [];
        const lpadId = obj1.lpadId;//系统赋予的编号
        const equipmentName = obj1.equipmentName;//装备器具名称
        const modelName = obj1.modelName;//装备器具型号
        const modelCode = obj1.modelCode;//装备器具编号
        const createName = obj1.createName;//创建数据的用户名称
        const createDate = obj1.createDate;//创建数据的日期
        this.actid = obj1.id;//设备id
        let point_state = obj1.point_state	;//已设置的控制点数量
        //console.log('数据现在对么',this.diftent);
        // if(this.diftent === 0){point_count = 0;}
        //point_count默认后台返回0
        //现在有个需要注意的：后台返回了默认状态是null,但是需求区分是第一次进行删除控制点还是第二次及以后删除控制点
        if(point_state	 === '尚未设置'){
          this.conhide = 0;this.mdeune = 1;this.actacon = 0;this.choosen = 0;this.point1 = -1;
          //console.log('9.5现在的数据',this.diftent);
        }
        else if(point_state	 === '已设置控制点0个，设置完毕！') {
          //if(this.diftent === 0){//初始状态
            //this.conhide = 0;this.mdeune = 1;this.actacon = 0;
          //}else{
            this.conhide = null;this.radio2 = null;this.radio1 = null;this.mdeune = null;this.actacon = 2;this.choosen = null;this.point1 = -3;
          //}
          // if(this.diftent === 0){
            //   this.conhide = null;this.radio2 = null;this.radio1 = null;this.mdeune = null;this.actacon = 2;this.choosen = null;
            // }else{
            //   this.conhide = 0;this.mdeune = 1;this.actacon = 0;
            // }
        }else if(point_state === '不在此处设置'){
          this.conhide = null;this.radio2 = null;this.radio1 = null;this.mdeune = null;this.actacon = 3;this.choosen = null;this.point1 = -2;
        }else{this.conhide = null;this.mdeune = null;this.radio1 = null;this.actacon = 1;this.choosen = null;}
        //this.action = point_count;
        //this.conhide = 0;
        //this.mdeune = 1;
        const mid = Number(obj1.mid);//型号id
        this.mind = mid;
        const jsond = {
          lpadId:lpadId,
          equipmentName: equipmentName,
          modelName: modelName,
          modelCode: modelCode,
          createName: createName,
          createDate: createDate
        };
        listbox.push(jsond);
        this.listd = listbox;
        const jsont = {
          model: mid
        };
        this.initActionact(jsont);
        // if(point_state === '不在此处设置'){this.conhide = null;}else if(point_state === '已设置控制点0个，设置完毕！'){this.conhide = null;}
      }
    },
    // delBtn(item){
    //   this.editObj = item
    //   this.delVisible = true
    // },
    hideFun(){
      this.delVisible = false;
      this.delVisible2 = false;
    },
    hideFun2(){
      this.delVisible3 = false;
      //调用‘增加控制点’相应接口
      this.addcaten();
    },
    hideFun4(){
      this.delVisible4 = false;
    },
    hideFun5(){
      this.delVisible5 = false;
      //停用列表弹窗关闭后，应该展示的是包含启用数据的列表的弹窗
      const json3 = { 'enabled': 1};
      this.initStation(json3,3);
    },
    hideFun6(){
      this.delVisible6 = false;
    },
    hideFun7(){
      this.delVisible7 = false;
    },
    hideFun8(){
      this.delVisible8 = false;
      this.radio2 = '';
    },
    hideFun9(){
      this.delVisible9 = false;
    },
    tipOk2(actio){
      //console.log('现在的数据是什么',this.diftent);
      if(this.value2 === '' || this.value2 === null){
        this.$message.error("选择控制点！");
        return false;
      }else{
        const choseactid = this.value2;//控制点选择框选中的数据对应的选项id
        const unitid = this.value3;//单位id
        let lowerlimit = '';
        let upperlimit = '';
       this.minAmount = this.minAmount === ref('') ? 0 : this.minAmount;
       this.text2 = this.text2 === ref('') ? 0 : this.text2;
        if(this.minAmount > this.text2){
          lowerlimit = Number(this.text2);
          upperlimit = Number(this.minAmount);
        }else if(this.minAmount < this.text2){
          lowerlimit = Number(this.minAmount);
          upperlimit = Number(this.text2);
        }
        // const lowerlimit = Number(this.minAmount);//下限数据
        // const upperlimit = Number(this.text2);//上限数据
        const list = [];//将获取到的图片地址存入一个新的数组中
        const lankd = this.fileList;//现在包含调用接口获取的+新上传的
        //上一次上传的图片格式是转换完的，这次新上传的图片数据是未转换的
        for(let a in lankd){
          list.push(lankd[a].normalPath);
        }
        console.log('存储图片地址的数组',list);
        const json2 = list.join(',');
        console.log('json2现在的数据',json2);//上传的图片地址
        const postjson = {
          controlOption: choseactid,
          unitId: unitid,
          lowerLimit: lowerlimit,
          upperLimit:upperlimit,
          pics: json2,
          equipment:Number(this.actid),
          model:Number(this.mind)//型号id
        };
        console.log('postjson现在的值8.8',postjson);
        //这个位置需要自定义个属性进行判断，例如1-增加控制点，0-修改控制点
        if(actio === 1){
          //增加控制点
          addcontentd(postjson).then(res => {
            const msg = res.data.msg;
            if(msg === "操作成功"){
              this.delVisible2 = false;
              //console.log('9.5获取到了么',this.diftent);
              //this.diftent === 0 ? this.conhide = 3 : this.conhide = null;
              //this.point1 === -1 ? this.conhide = 3 : this.conhide = null;
              //this.conhide = 3;
              this.mdeune = 1;
              const mind = this.mind;
              if(this.choosen === null){this.conhide = null;this.mdeune = null;}
              console.log(this.point1);
              if(this.point1 !== -1){
                console.log('现在的值9.5',this.mind);
                const jsont = {id:this.mind,state:4};
                chankemadse(jsont).then(res => {
                  let msg = res.data.msg;
                })
              }
              // if(this.point1 === -1){}else{
              //   console.log('现在的值9.5',this.mind);
              //   // if(this.action > 0){
              //   //   const jsont = {id: this.mind,state:4};
              //   //   this.chankense(jsont);
              //   // }
              // }
              const jsond = {
                model: mind
              };
              this.initActionact(jsond);
            }else{}
          })
        }else if(actio === 0){
          //修改控制点
          console.log(json2);
          let choid = '';
          //console.log('232',this.options2);
          for(let a2 in this.options2){
            //console.log('2024.8.28-name的值不对么？',this.value2);
            if(this.options2[a2].id === this.value2){
              //console.log('2024.10.28-this.options2',this.options2[a2].id);
              choid = this.options2[a2].id;
            }
          }
          let unid = '';
          //console.log('28.2',this.options3);
          for(let a3 in this.options3){
            //console.log('2024.08.28-this.value3的值',this.value3);
            if(this.options3[a3].id === this.value3){
              unid = this.options3[a3].id;
            }
          }
          const jsonu = {
            id: Number(this.uptid),//控制点
            controlOption:choid,//选项id
            unitId:unid,//单位id
            lowerLimit: lowerlimit,//下限
            upperLimit:upperlimit,//上限数据
            pics:json2//图片集合
          };
          // const pictlink = [];
          // if(pictlink.length == 0){
          //   jsonu.pics = json2;
          // }
          upcated(jsonu).then(res => {
            const msg = res.data.msg;
            if(msg === "操作成功"){
              this.delVisible2 = false;
              //this.diftent === 0 ? this.conhide = 3 : this.conhide = null;
              //this.point1 = -1 ? this.conhide = 3 : this.conhide = null;
              //this.conhide = 3;
              this.mdeune = 1;
              const mind = this.mind;
              const jsond = {
                model: mind
              };
              this.initActionact(jsond);
            }
          })
        }
      }
    },
    tipOk6(){
      console.log('this.choslist现在的值',this.choslist);
      this.choslist = this.choslist.join(',');//vue中将数组转换成字符串
      console.log('this.choslist现在的值',this.choslist);
      const json = {
        ids: this.choslist,
        enabled:1
      }
      stopChcent(json).then(res => {
        console.log(res.data.msg);
        const content1 = res.data.msg;
        if(content1 === "操作成功"){
          const conte = "操作成功！ 再选择控制点名称时，这些选项将会出现！";
          this.$message.success(conte);
          this.delVisible6 = false;
          const json2 = { 'enabled': 0};
          this.stopChert(json2);
        }else{}
      })
    },
    tipOk7(){
      this.delVisible7 = false;
      const content7 = this.$.refs.unitern.textContent;
      console.log(content7);
      let json7 = {
        'name': content7,
        'module': 12
      };
      addUnatOk(json7).then(res => {
        //需要调用另一个获取列表数据的接口将获取的数据展示在专门的筛选框中
        let json72 = { 'module': 12 };
        this.getUnitlist(json72);
        // getUnitlank(json72).then(res => {
        //   console.log(res);
        //   this.options3 = res.list;
        //   console.log('没获取到么？',this.options3);
        // }).catch(err => {
        //   console.log('err=',err);
        // })
      }).catch(err => {
        console.log('err=',err);
      })
    },
    tipOk8(difft){
      if(difft === 1){
        console.log('现在的数据9.5',this.point1);
        this.point1 === -1 ? this.conhide = 1 : this.conhide = null;
        //this.conhide = 1;
      }else if(difft === 2){
        this.point1 === -1 ? this.conhide = 2 : this.conhide = null;
        //this.conhide = 2;
      }else if(difft === 3){
        console.log('现在的数据9.5',this.point1);
        this.point1 === -1 ? this.conhide = 3 : this.conhide = null;
        //this.conhide = 3;
      }
      const detid = this.detenid;//需要删除的控制点的id
      const json8 = {ids: detid};
      dettentd(json8).then(res => {
        const msg = res.data.msg;
        if(msg === "操作成功"){
          this.delVisible8 = false;
          console.log('现在list3不是个空数组么？8.28',this.list3);
          // if(this.list3.length == 0){
          //   //console.log('9.5',this.diftent);
          //   this.point1 === -1 ? this.conhide = 0 : this.conhide = null;
          //   //this.conhide = 0;
          // }else{
          //   this.point1 === -1 ? this.conhide = 3 : this.conhide = null;
          //   //this.conhide = 3;
          // }
          console.log('型号id正确么',this.mind);
          const jsontd = {
            model: this.mind
          };
          this.initActionact(jsontd);
        }
      })
    },
    getUnitlist(obj7){
      getUnitlank(obj7).then(res => {
        console.log('到这里了么?');
        console.log(res.data.list);
        this.options3 = res.data.list;
      }).catch(err => {
        console.log('err =',err);
      })
    },
    havelooker(){
      this.delVisible = true;
    },
    addcaten(){
      //console.log('现在的数据是什么',this.diftent);
      this.delVisible2 = true;
      const orn2 = "增加控制点";
      this.dialogTitle = orn2;
      this.actior = 1;//1代表增加控制点
      this.value3 = "";
      this.value2 = "";
      this.minAmount = "";
      this.text2 = "";
      this.isActive = false;
      const lank3 = this.list3;
      const json7 = { 'enabled': 1 };
      if(lank3.length == 0){
        this.initStation(json7,2,0);//点击‘增加控制点’获取控制点数据
      }else{
        this.initStation(json7,2);//点击‘增加控制点’获取控制点数据
      }
      // const lank3 = this.list3;
      //console.log('这里能获取到么',this.options2);
      //const chose2 = this.options2;
      //console.log('执行到这里了么?');//需要让没有this.list3数据的装备在选择控制点的时候默认只展示三个数据
      // if(lank3.length == 0){
      //   //需要将除去默认启用的数据外其余数据都停用
      //   console.log('8.19.1');
      //   for(let c in chose2){
      //     console.log('1.1');
      //     const id = chose2[c].id;
      //     if(id !== 67 && id !==7 && id !== 6){
      //       console.log('2');
      //       //进行停用
      //       let json = {
      //         id: id,
      //         enabled: 0
      //       };
      //       stopxxest(json).then(res => {
      //         console.log('现在的值',res.data.msg);
      //         console.log('3');
      //         if(res.data.msg === "操作成功"){
      //           // const content2en = "操作成功！"+"\n" +
      //           //     "再选择控制点时，该选项将会不出现！";
      //           // this.$message.success(content2en);
      //           const json3 = { 'enabled': 1};
      //           this.initStation(json3,3);
      //         }
      //       })
      //     }
      //   }
      // }
      const json8 = { 'module': 12 };
      this.getUnitlist(json8);
      this.fileList = [];
      //console.log('现在的数据是什么',this.diftent);
      //initCUpload($("#cpUpcatlod-1"),'img');
    },
    addcatmn(){
      this.delVisible3 = true;
      this.inputCon = "";
    },
    additionalunit(){
      this.delVisible7 = true;
      this.input4 = "";
    },
    //给input设置对数字的控制，小数点前最多11位，小数点后最多7位，可以输入负数
    formatNum(val){
      let temp = val.toString();
      // 清除除了数字、小数点和负号以外的所有字符
      temp = temp.replace(/[^\d.-]/g, '');
      // 截取小数点前最多11位，小数点后最多7位
      const parts = temp.split('.')
      if (parts[0].length > 11) {
        parts[0] = parts[0].slice(0, 11)
      }
      if (parts[1] && parts[1].length > 7) {
        parts[1] = parts[1].slice(0, 7)
      }
      // 合并处理后的数值
      temp = parts.join('.');
      // 验证第一个字符是数字或负号，如果第一个字符是小数点，则保留前面的负号
      temp = temp.replace(/^(-?)\./, '$1');
      // 清除以小数点开头的情况，例如 ".123" 改为 "0.123"
      temp = temp.replace(/^\./g, '');
      // 清除连续多个小数点的情况，例如 "1..23" 改为 "1.23"
      temp = temp.replace(/\.{7,}/g, '');
      // 将第一个小数点替换为占位符 "$#$"，然后清除其他小数点，最后再将占位符替换回小数点。这样可以保证只保留第一个小数点。
      temp = temp.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.');
      // 限制只能输入两位小数，超出部分会被截断
      temp = temp.replace(/^(-?)(\d+)\.(\d\d\d\d\d\d\d).*$/, '$1$2.$3');
      // 最多输入18位数
      if (temp.length > 19) {
        temp = temp.slice(0, 19);
      }
      // 将处理好的数值赋值给 queryForm.minAmount
      this.minAmount = temp;
    },
    formatNum2(val){
      let temp = val.toString();
      // 清除除了数字、小数点和负号以外的所有字符
      temp = temp.replace(/[^\d.-]/g, '');
      // 截取小数点前最多11位，小数点后最多7位
      const parts = temp.split('.')
      if (parts[0].length > 11) {
        parts[0] = parts[0].slice(0, 11)
      }
      if (parts[1] && parts[1].length > 7) {
        parts[1] = parts[1].slice(0, 7)
      }
      // 合并处理后的数值
      temp = parts.join('.');
      // 验证第一个字符是数字或负号，如果第一个字符是小数点，则保留前面的负号
      temp = temp.replace(/^(-?)\./, '$1');
      // 清除以小数点开头的情况，例如 ".123" 改为 "0.123"
      temp = temp.replace(/^\./g, '');
      // 清除连续多个小数点的情况，例如 "1..23" 改为 "1.23"
      temp = temp.replace(/\.{7,}/g, '');
      // 将第一个小数点替换为占位符 "$#$"，然后清除其他小数点，最后再将占位符替换回小数点。这样可以保证只保留第一个小数点。
      temp = temp.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.');
      // 限制只能输入两位小数，超出部分会被截断
      temp = temp.replace(/^(-?)(\d+)\.(\d\d\d\d\d\d\d).*$/, '$1$2.$3');
      // 最多输入18位数
      if (temp.length > 19) {
        temp = temp.slice(0, 19);
      }
      // 将处理好的数值赋值给 queryForm.minAmount
      this.text2 = temp;
    },
    //转换时间戳数据
    formatTimestamp(timestamp){
      const date = new Date(timestamp);
      const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以加1，并用0填充
      const daten = String(date.getDate()).padStart(2,'0');//日从0开始，所以加1，并用0补充
      const hours = String(date.getHours()).padStart(2,'0');//时从0开始，所以加1，并用0补充
      const minutes = String(date.getMinutes()).padStart(2,'0');//分从0开始，所以加1，并用0补充
      const seconds = String(date.getSeconds() + 1).padStart(2,'0');//秒从0开始，所以加1，并用0补充
      return `${date.getFullYear()}-${month}-${daten} ${hours}:${minutes}:${seconds}`;
    },
    //图片上传
    // initCUpload(obj,type){
    //   let fileTypeExtsStr = '';
    //   let multi = true;
    //   let groupUuid = sphdSocket.uuid();
    //   if(type == "img"){
    //     fileTypeExtsStr = '*.gif,*.png;*.jpg;*.jpeg;'
    //   }
    //   let itemTemplate = ``;
    //   obj.html("");
    //   obj.Huploadify({
    //     auto:true,
    //     fileTypeExts:fileTypeExtsStr,
    //     itemTemplate:itemTemplate,
    //     multi:multi,
    //     buttonText:"上传",
    //     formData:{
    //       module:'产品档案',
    //       userId:sphdSocket.user.userID,
    //       groupUuid:groupUuid
    //     },
    //     fileSizeLimit:40960,
    //     showUploadedPercent:false,
    //     showUploadedSize:false,
    //     removeTimeout:99999999,
    //     uploader:$.webRoot+"/uploads/uploadfyByFile.do",
    //     onUploadSuccess:function(file,data){
    //       data = JSON.parse(data);
    //       if(type == "img"){
    //         var imgs = $("#addacttble").data("img") || [];//获取到全本图片
    //         imgs.push(data);//将新添加的图片存入数组
    //         $("#addacttble").data("img",imgs);//将新的数组赋值给data("img")，用于在其他地方进行数据获取
    //       }
    //       var len = obj.parent().parent().find("div.imgWalbl").find(".imgsthumb").length;
    //       if(len < 2){
    //         var path = data.filename;
    //         obj.parent().attr("groupUuid",data.groupUuid)
    //         var imgStr =
    //             '<div class="imgsthumb">'+
    //             '   <div class="filePic" data-path="' + path + '" style="background-image: url(' + $.uploadUrl + path +')"></div>'+
    //             '   <span fileUid="' + data.fileUid + '" onclick="cancelThis($(this))">×</span>' +
    //             '   <a path="' + path + '" onclick="imgViewer($(this))">预览</a>'+
    //             '</div>';
    //         // obj.parent().parent().find("div.imgWalbl").append(imgStr);
    //         $("#httpen").parent().parent().find("imgWalbl").append(imgStr);
    //       }else{
    //         var fileUid = data.fileUid;
    //         cancelFileDel([{'type': 'fileId', 'fileId': fileUid}], true);
    //       }
    //     }
    //   })
    // }
    //多选框
    handleSelectionChange(val) {
      this.multipleSelection = val;
      console.log('现在选中的数据',this.multipleSelection);
      console.log('数据的长短',this.multipleSelection.length);
      this.choslist = this.multipleSelection.map(item => (item.id));//用于将数组中某一个属性的值取出来
      //保存在一个新的数组中
      console.log('this.choslist现在的值',this.choslist);
    },
    //批量启用
    batchactivation(){
      this.delVisible6 = true;
      this.multipleSelection = [];
      let json6 = { 'enabled': 0 };
      this.batcation(json6);
    },
    batcation(obj6){
      initStatton(obj6).then(res => {
        const stopulist = res.data;//被停用的数据
        console.log('8.6停用数据',stopulist);
        this.tableData = stopulist;
        for(let s in stopulist){
          let updateName = stopulist[s].updateName;
          if(updateName === null){
            updateName = stopulist[s].createName || "--";
          }
          let enabledTime = stopulist[s].enabledTime;
          if(enabledTime === null){
            enabledTime = stopulist[s].createDate;
          }
        }
      })
    },

    closend(e){//实现一个单选框第一次选中，第二次点击后取消选中
      // 当radio的值为null时，手动设置为第一个label的值
      //e === this.radio1 ? this.radio1 = '' : this.radio1 = e;
      console.log('8.14',e);
      console.log('this.radio1的值8.14',this.radio1);
      if(e === this.radio1){
        console.log('现在的值',this.radio1);
        this.radio1 = '';
        this.mdeune = 1;
      }else{
        console.log('是执行到这里了？',this.radio1);
        console.log(e);
        this.radio1 = e;
        this.mdeune = 2;
      }
    },
    concalpoint(e2){//a-1,b-2
      e2 === this.radio2 ? this.radio2 = e2 = '' : this.radio2 = e2;
      console.log(e2);
      console.log(this.radio2);
      console.log('e2现在的值8.28',e2);
      if(e2 === 1 && this.radio2 === e2){//选中‘暂无控制点’
        // this.conhide = 1;
        if(this.list3.length > 0){
          this.delVisible8 = true;
          this.messagend = ' 您确认吗，确认后控制点将被清空。';
          this.speshall = 0;
          let lankt = this.list3.map(item => (item.id));
          console.log(lankt);
          lankt.join(', ');
          this.detenid = lankt;
          //this.diftent = null;
          //this.choslist = this.multipleSelection.map(item => (item.id));
        }else{
          //this.diffent === 0 ? this.conhide = 1 : this.conhide = null;
          this.conhide = 1;
        }
      }else if(e2 === 2 && this.radio2 === e2){//选中“配置生产线或制造规范时再设置，在此暂不设置”
        //this.conhide = 2;
        if(this.list3.length > 0){
          this.delVisible8 = true;
          this.messagend = ' 您确认吗，确认后控制点将被清空。';
          this.speshall = 1;
          let lankt = this.list3.map(item => (item.id));
          console.log(lankt);
          lankt.join(', ');//数组转换为字符串
          this.detenid = lankt;
          //this.diftent = null;
        }else{
          //this.diffent === 0 ? this.conhide = 2 : this.conhide = null;
          this.conhide = 2;
        }
      }else if(e2 === ''){
        //this.diffent === 0 ? this.conhide = 0 : this.conhide = null;
        this.conhide = 0;
      }else{
        //this.diffent === 0 ? this.conhide = 3 : this.conhide = null;
        this.conhide = 3;
      }
    },
    stopuse(objs,diff,fuml){
      const xid = objs.id;//需要停用的选项的id
      if(diff === 'stop'){//对选中的数据进行停用
        let jsons = {
          id: xid,
          enabled: 0//0肯定是数字
        };
        stopxxest(jsons).then(res => {
          console.log('现在的值',res.data.msg);
          if(res.data.msg === "操作成功"){
            if(fuml === 'small'){//不需要给予提示的情况
            }else{
              const content2en = "操作成功！"+"\n" +
                  "再选择控制点时，该选项将会不出现！";
              this.$message.success(content2en);
              const json3 = { 'enabled': 1};
              this.initStation(json3,3);
            }
          }
        })
      }else if(diff === 'str'){//对选中的数据进行启用
        let jsons = {
          id: xid,
          enabled: 1//1也肯定是数字
        };
        stopxxest(jsons).then(res => {
          console.log('现在的值',res.data.msg);
          if(res.data.msg === "操作成功"){
            const content2en = "操作成功！"+"\n" +
                "再选择控制点时，该选项将会出现！";
            this.$message.success(content2en);
            const json3 = { 'enabled': 0};
            this.stopChert(json3);
          }
        })
      }
    },
    //停用启用
    useunter(typeu,tou){
      stopxxest(typeu).then(res => {
        if(res.data.msg === "操作成功"){
          if(tou === 1){//启用
            const content3 = "增加的控制点为系统中已被停用的数据。"+"\n"+"现已启用！";
            this.$message.success(content3);
            this.delVisible3 = false;//关闭‘增加一个控制点选项’弹窗
            this.addcaten();
          }else{}//不确定是否有停用的情况，先搁置在这儿
        }
      })
    },
    //图片预览
    PreviewImg(dipen){
      this.picsd = dipen;
      this.delVisible9 = true;
      console.log(this.picsd);
      //调用接口后获取图片数据
      // state.srcList = res.data.map((item) => { return item.accessUrl})
      // state.imgViewerVisible = true;
      // const lanks = this.list3;//lanks是包含所有控制点数据的，而这里是只需要一条控制点数据的
      // const id = dipen.id;
      // let stalist = '';
      // for(let a in lanks){
      //   if(id === lanks[a].id){
      //     stalist = lanks[a];
      //   }
      // }
      // console.log('是不是只有一条数据',stalist);
      //lanks下的json数据中pics对应的值就是图片的地址，但是需要将字符串格式的地址转换成数组形式
      // const list = lanks.map(item => ({ pics: item.pics}));
      // console.log('list现在的格式',list);
      // let tryent = '';
      // for(let i in list){
      //   tryent = list[i].pics;
      // }
      // console.log('tryen现在的值',tryent);
      // this.logicImageList = tryent.split(',').map(img => img.trim());
      // console.log('现在的数据格式',this.logicImageList);
      // this.$nextTick(() =>{
      //   this.$refs.elImage.clickHandler();
      // })
      // this.state = stalist;
      // console.log(this.state);
      // // let middle = this.state.pics;
      // // console.log('8.13',middle);//它现在是个字符串
      // // this.state.srcList = middle.split(',');
      // // console.log('8.15',this.state.srcList);
      // // console.log(middle);
      // // for(let m in middle){
      // //   if(m === '0'){
      // //     console.log(middle[m]);
      // //     this.state.srcList = [];
      // //     this.state.srcList = this.state.srcList === undefined ? [] : this.state.srcList;
      // //     this.state.srcList.push(middle[m]);
      // //     console.log('srcList现在的数据',this.state.srcList);
      // //   }else if(m === '1'){
      // //     this.state.srcList2 = this.state.srcList2 === undefined ? [] : this.state.srcList2;
      // //     this.state.srcList2.push(middle[m]);
      // //   }
      // // }
      // //为何数据获取到了，但是无法在页面上展示出来呢
      // //const lank1 = [];
      // // for(let a in this.state.srcList){
      // //   if(a === '0'){
      // //     lank1.push(this.state.srcList[a].name);
      // //   }
      // // }
      // // this.state.srcList = lank1;
      // // console.log('现在的数据8.13',this.state.srcList);
      // this.state.imgViewerVisible = true;
    },
    //将获取到的图片地址转换为可以在页面上展示的图片地址格式
    realPath(path){
      return auth.webRoot + '/upload/' + path;
    },
    // closeImgViewer(){
    //   this.state.imgViewerVisible = false;
    // }
  },
}
</script>

<style lang="scss">
.el-checkbox__inner{
  border-radius: 50%;
}
.el-upload--picture-card{
  --el-upload-picture-card-size: 148px;
  height: 0;
  width: 0;
  border: 0;
}
.el-button--typcolor{
  font-size: 12px;
  padding: 5px 15px 5px;
  border-radius: 3px;
  font-weight: bold;
  cursor: pointer;
  color: #5d9cec;
  white-space: nowrap;
  text-align: center;
  word-wrap: break-word;
  word-spacing: normal;
  word-break: break-all;
  box-sizing: border-box;
  border: none;
}
//当需要给某个特定的el-button设定特定的hover属性时，可以像下面这样自定义设定，然后再将:hover前面的部分赋值给需要设定该
//样式的el-button的class,这样就能正常展示了。
.impont-hover:hover{
  background-color: var(--el-color-primary);
  color: #ffffff;
}

.addpaccho{
  margin-top: 10px;
}
.addmore{
  margin-top: 25px;
}
.addpactwo{
  margin-bottom: 10px;
}
//.boxr{
//  position: relative;
//}
.cent{
  text-align: center;
}
//.centered{
//  text-align: center; /* 文字水平居中 */
//  display: flex;
//  align-items: center; /* 文字垂直居中 */
//  justify-content: center; /* 当需要水平垂直居中时使用 */
//}
.choone{
  font-weight: bold;
}
.choopont{
  margin-top: 7px;
}
//.lettpic{
//  padding-top: 3px;
//}
.line {
  width: 100%; /* 横线的宽度 */
  height: 1px; /* 横线的高度 */
  background-color: black; /* 横线的颜色 */
  margin: 0 auto; /* 横线居中 */
  margin-bottom: 22px;
}
//.maden{
//  margin-left: 132px;
//}
//.plence{
//  position: absolute;top: 5px;
//}
//.ploncon{
//  padding-left: 84px;
//}
//.plpen{
//  padding: 5px 13px;
//}
.demoPage,.cateogont,.pointsetr{
  padding: 50px;
}
.demoPage{
  .rolor{
    margin: 0 66px;
    .biggenr{
      height: 160px;
      .ty-alert{
        $myClear: both; clear: $myClear;
        color: #101010;justify-content: space-between;
        width: 100%;
        padding: 8px 0;
        box-sizing: border-box;
        border-radius: 2px;
        position: relative;
        font-size: 13px;
        overflow: hidden;
        opacity: 1;
        display: flex;
        align-items: center;
        transition: opacity .2s;
        margin-bottom: 4px;
      }
      div .pleeup{
        margin-top: 36px;
        width: 100%;
      }
    }
    .upolence{
      margin-top: 20px;
    }
    .equipdatein{
      .eltable{
        width: 1486px;border-collapse:collapse;border: 1px solid #ddd; overflow: hidden;height: 100%;min-width: 1000px;
      }
    }
  }
}
.cateogont{
  .gbbefor{
    //  margin: 0 96px;width: 76px;margin-bottom: 20px;
    margin-left: 95px;margin-bottom: 10px;
  }
  .rolor{
    margin: 0 96px;
    .upolence{
      margin-top: 20px;
    }
  }
}
.pointsetr{
  .ty-alert{
    $myClear: both; clear: $myClear;
    color: #101010;justify-content: space-between;
    width: 100%;
    padding: 8px 0;
    box-sizing: border-box;
    border-radius: 2px;
    position: relative;
    font-size: 13px;
    overflow: hidden;
    opacity: 1;
    display: flex;
    align-items: center;
    transition: opacity .2s;
    margin-bottom: 4px;
    #unsett1,#unsett2,#unsett3,#unsett4{
      margin-left: 83px;
    }
    .memal{
      padding: 0 28px;
    }
  }
  .companyDetails{
    .eltop{
      margin-top: 25px;
      .addacter{
        padding-left: 78px;
      }
    }
    .pocom{
      margin-left: 96px;margin-right: 82px;
    }
    .ty-alert{
      .wrtdboad{
        margin-left: 121px;
      }
    }
    .upolence{
      margin-top: 20px;
    }
    .actpont{
      padding-left: 12px;
    }
    .equmemot{
      .picte{
        font-size: 12px;font-family: 宋体;padding: 5px 15px 5px;border-radius: 3px;font-weight: bold;
        cursor: pointer;
      }
    }
  }
  .wrodern{
    position: absolute;top: -89px;left: 575px;
  }
  .active{
    margin-top: 15px;
  }
}

</style>
