package cn.sphd.miners.modules.trainManage.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.material.entity.RespStatus;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.trainManage.dao.*;
import cn.sphd.miners.modules.trainManage.dto.*;
import cn.sphd.miners.modules.trainManage.entity.*;
import cn.sphd.miners.modules.trainManage.service.TrainService;
import cn.sphd.miners.modules.uploads.service.UploadService;
import com.alibaba.fastjson.JSONArray;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.DecimalFormat;
import java.util.*;

/**
 * @ClassName TrainServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/2/3 8:13
 * @Version 1.0
 */
@Service("trainService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class TrainServiceImpl implements TrainService {
    @Autowired
    TrainingQuestionBankDao trainingQuestionBankDao;
    @Autowired
    TrainingExamUserDao trainingExamUserDao;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    TrainingExamDao trainingExamDao;
    @Autowired
    TrainingExamUserAnswerDao trainingExamUserAnswerDao;
    @Autowired
    TrainingQuestionBankAttachmentDao trainingQuestionBankAttachmentDao;
    @Autowired
    TrainingQuestionBankAttachmentHistoryDao trainingQuestionBankAttachmentHistoryDao;
    @Autowired
    TrainingExamBankDao trainingExamBankDao;
    @Autowired
    TrainingMaterialDao trainingMaterialDao;
    @Autowired
    TrainingQuestionDao trainingQuestionDao;
    @Autowired
    TrainingQuestionKeyDao trainingQuestionKeyDao;
    @Autowired
    TrainingExamQuestionDao trainingExamQuestionDao;
    @Autowired
    TrainingExamKeyDao trainingExamKeyDao;
    @Autowired
    TrainingQuestionBankHistoryDao trainingQuestionBankHistoryDao;
    @Autowired
    UploadService uploadService;
    @Override
    public List<TrainingQuestionBank> selectTrainingQuestionBankList(User user, Integer type, PageInfo pageInfo,Integer status) {
        String hql = "from TrainingQuestionBank where creator = "+ user.getUserID()+" and enabled="+status;
        if(type==1)//1代号降序排序
            hql=hql+" ORDER BY code desc";
        else if(type==2)//2代号升序排序
            hql=hql+" ORDER BY code ASC";
        else if(type==3)//3创建时间降序排序
            hql=hql+" ORDER BY createDate desc";
        else if(type==4)//4创建时间升序排序
            hql=hql+" ORDER BY createDate ASC";
        else if(type==5)//5素材数量降序排序
            hql=hql+" ORDER BY materialNum desc";
        else if(type==6)//6素材数量升序排序
            hql=hql+" ORDER BY materialNum ASC";
        List<TrainingQuestionBank> list = trainingQuestionBankDao.getListByHQLWithNamedParams(hql,null,pageInfo);
        return list;
    }

    @Override
    public RespTrainMaterialListPage selectQuestionBankDetailById(PageInfo pageInfo, Integer id, Integer type,Integer status) {
        RespTrainMaterialListPage respTrainMaterialListPage=new RespTrainMaterialListPage();
        TrainingQuestionBank trainingQuestionBank= trainingQuestionBankDao.get(id);
        if(trainingQuestionBank.getEnabled()==1)
            respTrainMaterialListPage.setStatus(1);
        else
            respTrainMaterialListPage.setStatus(0);
        String hql = "from TrainingMaterial where bank="+id+" and enabled="+status;
        if(type==1)//1代号降序排序
            hql=hql+" ORDER BY code desc";
        else if(type==2)//2代号升序排序
            hql=hql+" ORDER BY code ASC";
        else if(type==3)//3创建时间降序排序
            hql=hql+" ORDER BY createDate desc";
        else if(type==4)//4创建时间升序排序
            hql=hql+" ORDER BY createDate ASC";
        else if(type==5)//5题数量降序排序
            hql=hql+" ORDER BY (tfNum+choiceNum) desc";
        else if(type==6)//6素题量升序排序
            hql=hql+" ORDER BY (tfNum+choiceNum) ASC";
        List<TrainingMaterial> list = trainingMaterialDao.getListByHQLWithNamedParams(hql,null,pageInfo);
        respTrainMaterialListPage.setTrainingMaterialList(list);
        respTrainMaterialListPage.setTrainingQuestionBank(trainingQuestionBank);
        return respTrainMaterialListPage;
    }

    @Override
    public RespTrainQuestionList selectTrainQuestionDetailById(Integer id, Integer status) {
        RespTrainQuestionList respTrainQuestionList=new RespTrainQuestionList();
        TrainingMaterial trainingMaterial= trainingMaterialDao.get(id);
        TrainingQuestionBank trainingQuestionBank= trainingQuestionBankDao.get(trainingMaterial.getBank());
        if(trainingQuestionBank.getEnabled()==1&&trainingMaterial.getEnabled()==1)
            respTrainQuestionList.setStatus(1);
        else
            respTrainQuestionList.setStatus(0);

        String hql = "from TrainingQuestion where material="+id+" and enabled="+status;
        List<TrainingQuestion> list = trainingQuestionDao.getListByHQLWithNamedParams(hql, null);
        respTrainQuestionList.setTrainingQuestionList(list);
        respTrainQuestionList.setTrainingMaterial(trainingMaterial);
        return respTrainQuestionList;
    }

    @Override
    public RespTrainQuestionKeyList selectTrainQuestionDetail(Integer id, Integer status) {
        RespTrainQuestionKeyList respTrainQuestionKeyList=new RespTrainQuestionKeyList();
        TrainingQuestion trainingQuestion=trainingQuestionDao.get(id);
        TrainingMaterial trainingMaterial= trainingMaterialDao.get(trainingQuestion.getMaterial());
        TrainingQuestionBank trainingQuestionBank= trainingQuestionBankDao.get(trainingQuestion.getBank());
        if(trainingQuestionBank.getEnabled()==1&&trainingMaterial.getEnabled()==1&&trainingQuestion.getEnabled()==1)
            respTrainQuestionKeyList.setStatus(1);
        else
            respTrainQuestionKeyList.setStatus(0);

        String hql = "from TrainingQuestionKey where question="+id+" and enabled="+status;
        List<TrainingQuestionKey> list = trainingQuestionKeyDao.getListByHQLWithNamedParams(hql, null);
        respTrainQuestionKeyList.setTrainingQuestionKeyList(list);
        respTrainQuestionKeyList.setTrainingQuestion(trainingQuestion);
        return respTrainQuestionKeyList;
    }

    @Override
    public String selectQuestionBankCode(int org) {
        int year= NewDateUtils.getYear(new Date());
        DecimalFormat df = new DecimalFormat("00000");
        int number=0;
        Map<String, Object> params = new HashMap<>();
        String hql = "from TrainingQuestionBank where org =:org and createDate>=:beginDate and createDate<=:endDate";
        params.put("org",org);
        params.put("beginDate",NewDateUtils.getNewYearsDay());
        params.put("endDate",new Date());
        List<TrainingQuestionBank> list = trainingQuestionBankDao.getListByHQLWithNamedParams(hql, params);

        String hql1 = "from TrainingQuestionBankHistory where org =:org and createDate>=:beginDate and createDate<=:endDate";
        params.put("org",org);
        params.put("beginDate",NewDateUtils.getNewYearsDay());
        params.put("endDate",new Date());
        List<TrainingQuestionBank> list1 = trainingQuestionBankHistoryDao.getListByHQLWithNamedParams(hql1, params);
        number=list.size()+list1.size()+1;
        String code=year+df.format(number);
        return code;
    }
    @Override
    public int addQuestionBank(User user, TrainingQuestionBank trainingQuestionBank,String trainingQuestionBankAttachmentJson,String module) {
        String code=this.selectQuestionBankCode(user.getOid());
        trainingQuestionBank.setCode(code);
        trainingQuestionBank.setCreator(user.getUserID());
        trainingQuestionBank.setOrg(user.getOid());
        trainingQuestionBank.setEnabled(1);
        trainingQuestionBank.setMaterialNum(0);
        trainingQuestionBank.setChoiceNum(0);
        trainingQuestionBank.setTfNum(0);
        trainingQuestionBank.setDisabledNum(0);
        trainingQuestionBank.setQuestionUpperLimit(4);
        trainingQuestionBank.setChoiceLowerLimit(3);
        trainingQuestionBank.setChoiceUpperLimit(5);
        trainingQuestionBank.setCreateName(user.getUserName());
        trainingQuestionBank.setCreateDate(new Date());
        trainingQuestionBankDao.save(trainingQuestionBank);
        //path 路径  title名称 size大小
        List<TrainingQuestionBankAttachment> list = JSONArray.parseArray(trainingQuestionBankAttachmentJson, TrainingQuestionBankAttachment.class);
        if(list!=null)
        for (TrainingQuestionBankAttachment trainingQuestionBankAttachment:list)
        {
            trainingQuestionBankAttachment.setBank(trainingQuestionBank.getId());
            trainingQuestionBankAttachment.setOrg(user.getOid());
            trainingQuestionBankAttachment.setCreateName(user.getUserName());
            trainingQuestionBankAttachment.setCreator(user.getUserID());
            trainingQuestionBankAttachment.setCreateTime(new Date());
            trainingQuestionBankAttachmentDao.save(trainingQuestionBankAttachment);
            TrainingUsing callback = new TrainingUsing(trainingQuestionBankAttachment.getId(),trainingQuestionBankAttachment.getClass());
            uploadService.addFileUsing(callback,trainingQuestionBankAttachment.getPath(),trainingQuestionBankAttachment.getTitle(),user,module);
        }
        return 1;
    }

    @Override
    public String selectTrainingMaterialCode(int id) {
        TrainingQuestionBank trainingQuestionBank= trainingQuestionBankDao.get(id);
        DecimalFormat df = new DecimalFormat("0000");
        int number=0;
        String hql = "from TrainingMaterial where bank="+id;
        List<TrainingMaterial> list = trainingMaterialDao.getListByHQLWithNamedParams(hql, null);
        if(list.size()>0) {
            String str = list.get(list.size() - 1).getCode();
            String str1 = str.substring(0, str.indexOf("-"));
            String str2 = str.substring(str1.length() + 1, str.length());
            number = Integer.parseInt(str2) + 1;
        }else number=1;
        String code="SC"+trainingQuestionBank.getCode()+"-"+df.format(number);
        return code;
    }

    @Override
    public String selectTrainingQuestionCode(int id) {
        TrainingMaterial trainingMaterial= trainingMaterialDao.get(id);
        int number=0;
        String hql = "from TrainingQuestion where material="+id;
        List<TrainingQuestion> list = trainingQuestionDao.getListByHQLWithNamedParams(hql, null);
        if(list.size()>0) {
            String str = list.get(list.size() - 1).getCode();
            String str1 = str.substring(str.indexOf("-")+1,str.length());
            String str2 = str1.substring(str1.indexOf("-")+1,str1.length());
            number = Integer.parseInt(str2) + 1;
        }else number=1;
        String code=trainingMaterial.getCode()+"-"+number;
        return code;
    }

    @Override
    public RespStatus addTrainingMaterial(User user, TrainingMaterial trainingMaterial) {
        RespStatus respStatus =new RespStatus();
        trainingMaterial.setOrg(user.getOid());
        trainingMaterial.setCode(this.selectTrainingMaterialCode(trainingMaterial.getBank()));
        trainingMaterial.setEnabled(1);
        trainingMaterial.setChoiceNum(0);
        trainingMaterial.setTfNum(0);
        trainingMaterial.setDisabledNum(0);
        trainingMaterial.setCreateDate(new Date());
        trainingMaterial.setCreateName(user.getUserName());
        trainingMaterial.setCreator(user.getUserID());
        trainingMaterialDao.save(trainingMaterial);
        TrainingQuestionBank trainingQuestionBank=trainingQuestionBankDao.get(trainingMaterial.getBank());
        trainingQuestionBank.setMaterialNum(trainingQuestionBank.getMaterialNum()+1);
        trainingQuestionBankDao.update(trainingQuestionBank);
        respStatus.setStatus(1);
        respStatus.setId(trainingMaterial.getId());
        respStatus.setCode(trainingMaterial.getCode());
        return respStatus;
    }

    @Override
    public int updateQuestionBank(User user,int id,int enabled) {
        TrainingQuestionBank trainingQuestionBank=trainingQuestionBankDao.get(id);
        trainingQuestionBank.setEnabled(enabled);
        trainingQuestionBank.setEnabledTime(new Date());
        trainingQuestionBank.setUpdateDate(new Date());
        trainingQuestionBank.setUpdator(user.getUserID());
        trainingQuestionBank.setUpdateName(user.getUserName());
        trainingQuestionBankDao.update(trainingQuestionBank);
        return 1;
    }

    @Override
    public int updateTrainingMaterial(User user, int id, int enabled) {
        TrainingMaterial trainingMaterial=trainingMaterialDao.get(id);
        trainingMaterial.setEnabled(enabled);
        trainingMaterial.setEnabledTime(new Date());
        trainingMaterial.setUpdateDate(new Date());
        trainingMaterial.setUpdator(user.getUserID());
        trainingMaterial.setUpdateName(user.getUserName());
        trainingMaterialDao.update(trainingMaterial);
        countMaterialByBank(trainingMaterial.getBank());
        return 1;
    }

    @Override
    public int updateTrainingQuestion(User user, int id, int enabled) {
        TrainingQuestion trainingQuestion=trainingQuestionDao.get(id);
        int status=0;
        if(enabled==1){
            String hql = "from TrainingQuestion where material=" +trainingQuestion.getMaterial()  + " and enabled=1 and type='"+trainingQuestion.getType()+"'";
            List<TrainingQuestion> trainingQuestionList = trainingQuestionDao.getListByHQLWithNamedParams(hql, null);
            if (trainingQuestionList.size() >= 4 &&"1".equals(trainingQuestion.getType())) {
                status = -1;
            }else if(trainingQuestionList.size() >= 4 &&"2".equals(trainingQuestion.getType())) {
                status = -2;
            }
            if("2".equals(trainingQuestion.getType())&&status==0)
            {
                String hql1="from TrainingQuestionKey where question="+id+" and isKey=1 and enabled=1";
                List<TrainingQuestionKey>trainingQuestionKey=trainingQuestionKeyDao.getListByHQLWithNamedParams(hql1, null);
                if(trainingQuestionKey.size()>0)
                {
                    String hql2 = "from TrainingQuestion where material=" +trainingQuestion.getMaterial()  + " and enabled=1 and type=2";
                    List<TrainingQuestion> trainingQuestionList2 = trainingQuestionDao.getListByHQLWithNamedParams(hql2, null);
                    for(TrainingQuestion trainingQuestion1: trainingQuestionList2){
                        String hql3="from TrainingQuestionKey where question="+trainingQuestion1.getId()+" and isKey=1 and enabled=1";
                        List<TrainingQuestionKey>trainingQuestionKey3=trainingQuestionKeyDao.getListByHQLWithNamedParams(hql3, null);
                        if(trainingQuestionKey3.size()>0)
                        {
                            status=-3;
                        }
                    }

                }
            }
        }
      if(status==0) {
          trainingQuestion.setEnabled(enabled);
          trainingQuestion.setEnabledTime(new Date());
          trainingQuestion.setUpdateDate(new Date());
          trainingQuestion.setUpdator(user.getUserID());
          trainingQuestion.setUpdateName(user.getUserName());
          trainingQuestionDao.update(trainingQuestion);
          status = 1;
          this.countQuestionByMaterial(trainingQuestion.getMaterial());
      }
        return status;
    }

    @Override
    public int updateTrainingQuestionKey(User user, int id, int enabled) {
        TrainingQuestionKey trainingQuestionKey=trainingQuestionKeyDao.get(id);
        TrainingQuestion trainingQuestion=trainingQuestionDao.get(trainingQuestionKey.getQuestion());
        String hql = "from TrainingQuestionKey where question=" +trainingQuestionKey.getQuestion()  + " and enabled=1 ";
        List<TrainingQuestionKey> trainingQuestionKeyList = trainingQuestionKeyDao.getListByHQLWithNamedParams(hql, null);
        int status=0;
        if(enabled==1)//启用
        {
            if(trainingQuestionKeyList.size()>=6)
                status=-1;
            else{
                trainingQuestionKey.setEnabled(enabled);
                trainingQuestionKey.setEnabledTime(new Date());
                trainingQuestionKey.setUpdateDate(new Date());
                trainingQuestionKey.setUpdator(user.getUserID());
                trainingQuestionKey.setUpdateName(user.getUserName());
                trainingQuestionKeyDao.update(trainingQuestionKey);
                status = 1;
                trainingQuestion.setDistracterNum(trainingQuestion.getDistracterNum()+1);
                trainingQuestionDao.update(trainingQuestion);
            }
        }else{
            if(trainingQuestionKeyList.size()<=4)
                status=-2;
            else{
                trainingQuestionKey.setEnabled(enabled);
                trainingQuestionKey.setEnabledTime(new Date());
                trainingQuestionKey.setUpdateDate(new Date());
                trainingQuestionKey.setUpdator(user.getUserID());
                trainingQuestionKey.setUpdateName(user.getUserName());
                trainingQuestionKeyDao.update(trainingQuestionKey);
                status = 1;
                trainingQuestion.setDistracterNum(trainingQuestion.getDistracterNum()-1);
                trainingQuestionDao.update(trainingQuestion);
            }
        }
        return status;
    }

    @Override
    public int updateTrainingQuestionBank(User user, int id, String name, String deleteAttachmentList,String addAttachmentList,String module) {
        List<TrainingQuestionBankAttachment> deleteList = JSONArray.parseArray(deleteAttachmentList,TrainingQuestionBankAttachment.class);
        List<TrainingQuestionBankAttachment> addList = JSONArray.parseArray(addAttachmentList,TrainingQuestionBankAttachment.class);
        TrainingQuestionBank trainingQuestionBank=trainingQuestionBankDao.get(id);
        String hqlHistory="from TrainingQuestionBankHistory where bank="+id;
        List<TrainingQuestionBankHistory> Historylist=trainingQuestionBankHistoryDao.getListByHQLWithNamedParams(hqlHistory, null);
        if(Historylist.isEmpty())//第一次修改，需要将原内容，和修改内容，都加入历史表
        {
            TrainingQuestionBankHistory trainingQuestionBankHistory= new TrainingQuestionBankHistory();
            BeanUtils.copyProperties(trainingQuestionBank,trainingQuestionBankHistory);
            trainingQuestionBankHistory.setId(null);
            trainingQuestionBankHistory.setBank(id);
            trainingQuestionBankHistory.setUpdateDate(new Date());
            trainingQuestionBankHistory.setUpdator(user.getUserID());
            trainingQuestionBankHistory.setUpdateName(user.getUserName());
            if (trainingQuestionBank.getVersionNo()==null)
            {
                trainingQuestionBankHistory.setVersionNo(0);
            }
            trainingQuestionBankHistoryDao.save(trainingQuestionBankHistory);
            String hql="from TrainingQuestionBankAttachment where bank="+id;
            List<TrainingQuestionBankAttachment> list=trainingQuestionBankAttachmentDao.getListByHQLWithNamedParams(hql, null);
            for(TrainingQuestionBankAttachment s:list){
                TrainingQuestionBankAttachmentHistory trainingQuestionBankAttachmentHistory=new TrainingQuestionBankAttachmentHistory();
                BeanUtils.copyProperties(s,trainingQuestionBankAttachmentHistory);
                trainingQuestionBankAttachmentHistory.setId(null);
                trainingQuestionBankAttachmentHistory.setBank(id);
                trainingQuestionBankAttachmentHistory.setUpdateTime(new Date());
                trainingQuestionBankAttachmentHistory.setUpdator(user.getUserID());
                trainingQuestionBankAttachmentHistory.setUpdateName(user.getUserName());
                trainingQuestionBankAttachmentHistory.setBankAttachment(s.getId());
                trainingQuestionBankAttachmentHistory.setBankHistory(trainingQuestionBankHistory.getId());
                trainingQuestionBankAttachmentHistoryDao.save(trainingQuestionBankAttachmentHistory);
                TrainingUsing callback = new TrainingUsing(trainingQuestionBankAttachmentHistory.getId(),trainingQuestionBankAttachmentHistory.getClass());
                uploadService.addFileUsing(callback,trainingQuestionBankAttachmentHistory.getPath(),trainingQuestionBankAttachmentHistory.getTitle(),user,module);
            }
        }
        if (deleteList!=null)
        for(TrainingQuestionBankAttachment trainingQuestionBankAttachment:deleteList)
        {
            trainingQuestionBankAttachment=trainingQuestionBankAttachmentDao.get(trainingQuestionBankAttachment.getId());
            trainingQuestionBankAttachmentDao.deleteById(trainingQuestionBankAttachment.getId());
            //删除附件，此处需要新增附件相关修改
            TrainingUsing callback = new TrainingUsing(trainingQuestionBankAttachment.getId(),trainingQuestionBankAttachment.getClass());
            uploadService.delFileUsing(callback, trainingQuestionBankAttachment.getPath(), user);
        }
        if (addList!=null)
        for(TrainingQuestionBankAttachment trainingQuestionBankAttachment:addList){//新增附件上传，此处预留
                trainingQuestionBankAttachment.setBank(trainingQuestionBank.getId());
                trainingQuestionBankAttachment.setOrg(user.getOid());
                trainingQuestionBankAttachment.setCreateName(user.getUserName());
                trainingQuestionBankAttachment.setCreator(user.getUserID());
                trainingQuestionBankAttachment.setCreateTime(new Date());
                trainingQuestionBankAttachmentDao.save(trainingQuestionBankAttachment);
                TrainingUsing callback = new TrainingUsing(trainingQuestionBankAttachment.getId(),trainingQuestionBankAttachment.getClass());
                uploadService.addFileUsing(callback,trainingQuestionBankAttachment.getPath(),trainingQuestionBankAttachment.getTitle(),user,module);
        }
        if (trainingQuestionBank.getVersionNo()==null)
        {
            trainingQuestionBank.setVersionNo(1);
        }else {
            trainingQuestionBank.setVersionNo(trainingQuestionBank.getVersionNo()+1);
        }
        if (name!=null&&!"".equals(name))
        trainingQuestionBank.setName(name);
        trainingQuestionBankDao.update(trainingQuestionBank);
        TrainingQuestionBankHistory trainingQuestionBankHistory= new TrainingQuestionBankHistory();
        BeanUtils.copyProperties(trainingQuestionBank,trainingQuestionBankHistory);
        trainingQuestionBankHistory.setId(null);
        trainingQuestionBankHistory.setBank(id);
        trainingQuestionBankHistory.setUpdateDate(new Date());
        trainingQuestionBankHistory.setUpdator(user.getUserID());
        trainingQuestionBankHistory.setUpdateName(user.getUserName());
        if (trainingQuestionBank.getVersionNo()==null)
        {
            trainingQuestionBankHistory.setVersionNo(1);
        }
        trainingQuestionBankHistoryDao.save(trainingQuestionBankHistory);
        String hql="from TrainingQuestionBankAttachment where bank="+id;
        List<TrainingQuestionBankAttachment> list=trainingQuestionBankAttachmentDao.getListByHQLWithNamedParams(hql, null);
        for(TrainingQuestionBankAttachment s:list){
            TrainingQuestionBankAttachmentHistory trainingQuestionBankAttachmentHistory=new TrainingQuestionBankAttachmentHistory();
            BeanUtils.copyProperties(s,trainingQuestionBankAttachmentHistory);
            trainingQuestionBankAttachmentHistory.setId(null);
            trainingQuestionBankAttachmentHistory.setBank(id);
            trainingQuestionBankAttachmentHistory.setUpdateTime(new Date());
            trainingQuestionBankAttachmentHistory.setUpdator(user.getUserID());
            trainingQuestionBankAttachmentHistory.setUpdateName(user.getUserName());
            trainingQuestionBankAttachmentHistory.setBankAttachment(s.getId());
            trainingQuestionBankAttachmentHistory.setBankHistory(trainingQuestionBankHistory.getId());
            trainingQuestionBankAttachmentHistoryDao.save(trainingQuestionBankAttachmentHistory);
            TrainingUsing callback = new TrainingUsing(trainingQuestionBankAttachmentHistory.getId(),trainingQuestionBankAttachmentHistory.getClass());
            uploadService.addFileUsing(callback,trainingQuestionBankAttachmentHistory.getPath(),trainingQuestionBankAttachmentHistory.getTitle(),user,module);
        }
        return 1;
    }

    @Override
    public int deleteQuestionBankJudge(User user, int id) {
        int status =0;
        String hql = "from TrainingExamBank where bank="+id;
        List<TrainingExamBank>trainingExamBankList=trainingExamBankDao.getListByHQLWithNamedParams(hql, null);
        if(trainingExamBankList.isEmpty())
            status=1;
        return status;
    }

    @Override
    public int deleteTrainingMaterialJudge(User user, int id) {
        int status =1;
        String hql = "from TrainingQuestion where material="+id;
        List<TrainingQuestion> trainingQuestionList=trainingQuestionDao.getListByHQLWithNamedParams(hql, null);
        for(TrainingQuestion trainingQuestion:trainingQuestionList)
        {
            String hql1 = "from TrainingExamQuestion where question="+trainingQuestion.getId();
            List<TrainingExamQuestion>trainingExamQuestionList=trainingExamQuestionDao.getListByHQLWithNamedParams(hql1, null);
            if(trainingExamQuestionList.size()>0)
                status=0;
        }
        return status;
    }

    @Override
    public int deleteTrainingQuestionJudge(User user, int id) {
        int status =1;
        String hql1 = "from TrainingExamQuestion where question="+id;
        List<TrainingExamQuestion>trainingExamQuestionList=trainingExamQuestionDao.getListByHQLWithNamedParams(hql1, null);
        if(trainingExamQuestionList.size()>0)
            status=0;
        return status;
    }

    @Override
    public int deleteDisableKeyJudge(User user, int id) {
        TrainingQuestionKey trainingQuestionKey=trainingQuestionKeyDao.get(id);
        int status =1;
        String hql1 = "from TrainingExamKey where questionKey="+id;
        List<TrainingExamKey>trainingExamKeyList=trainingExamKeyDao.getListByHQLWithNamedParams(hql1, null);
        if(trainingExamKeyList.size()>0)
            status=0;
        if(status==1&&trainingQuestionKey.getEnabled()==1){
            String hql = "from TrainingQuestionKey where question=" +trainingQuestionKey.getQuestion() + " and enabled=1 ";
            List<TrainingQuestionKey> trainingQuestionKeyList = trainingQuestionKeyDao.getListByHQLWithNamedParams(hql, null);
            if(trainingQuestionKeyList.size()<=4)
                status=-2;
        }
        return status;
    }

    @Override
    public int deleteQuestionBank(User user, int id) {
        int status =0;
        String hql = "from TrainingExamBank where bank="+id;
        List<TrainingExamBank>trainingExamBankList=trainingExamBankDao.getListByHQLWithNamedParams(hql, null);
        if(trainingExamBankList.isEmpty())
        {
            TrainingQuestionBank trainingQuestionBank=trainingQuestionBankDao.get(id);
            status=1;
            //附件
            String hql1 = "from TrainingQuestionBankAttachment where bank="+id;
            List<TrainingQuestionBankAttachment>deleteList=trainingQuestionBankAttachmentDao.getListByHQLWithNamedParams(hql1,null);
            for(TrainingQuestionBankAttachment trainingQuestionBankAttachment:deleteList)
            {
                trainingQuestionBankAttachmentDao.deleteById(trainingQuestionBankAttachment.getId());
                //删除附件，此处需要新增附件相关修改
                TrainingUsing callback = new TrainingUsing(trainingQuestionBankAttachment.getId(),trainingQuestionBankAttachment.getClass());
                uploadService.delFileUsing(callback, trainingQuestionBankAttachment.getPath(), user);
            }
            //试题答案
            String hql2 = "from TrainingQuestionKey where bank="+id;
            List<TrainingQuestionKey>questionKeyList=trainingQuestionKeyDao.getListByHQLWithNamedParams(hql2,null);
            trainingQuestionKeyDao.deleteAll(questionKeyList);
            //试题
            String hql3 = "from TrainingQuestion where bank="+id;
            List<TrainingQuestion>questionList=trainingQuestionDao.getListByHQLWithNamedParams(hql3,null);
            trainingQuestionDao.deleteAll(questionList);
            //素材
            String hql4 = "from TrainingMaterial where bank="+id;
            List<TrainingMaterial>materialList=trainingMaterialDao.getListByHQLWithNamedParams(hql4,null);
            trainingMaterialDao.deleteAll(materialList);
            //题库历史附件
            String hql6 = "from TrainingQuestionBankAttachmentHistory where bank="+id;
            List<TrainingQuestionBankAttachmentHistory>trainingAttachmentHistory=trainingQuestionBankAttachmentHistoryDao.getListByHQLWithNamedParams(hql6,null);
            for(TrainingQuestionBankAttachmentHistory trainingQuestionBankAttachmentHistory:trainingAttachmentHistory)
            {
                trainingQuestionBankAttachmentHistoryDao.deleteById(trainingQuestionBankAttachmentHistory.getId());
                //删除附件，此处需要新增附件相关修改
                TrainingUsing callback = new TrainingUsing(trainingQuestionBankAttachmentHistory.getId(),trainingQuestionBankAttachmentHistory.getClass());
                uploadService.delFileUsing(callback, trainingQuestionBankAttachmentHistory.getPath(), user);
            }
            //题库历史
            String hql5 = "from TrainingQuestionBankHistory where bank="+id;
            List<TrainingQuestionBankHistory>trainingQuestionBankHistoryList=trainingQuestionBankHistoryDao.getListByHQLWithNamedParams(hql5,null);
            trainingQuestionBankHistoryDao.deleteAll(trainingQuestionBankHistoryList);

            trainingQuestionBankDao.delete(trainingQuestionBank);
        }
        return status;
    }

    @Override
    public int deleteTrainingMaterial(User user, int id) {
        int status =1;
        String hql = "from TrainingQuestion where material="+id;
        List<TrainingQuestion> trainingQuestionList=trainingQuestionDao.getListByHQLWithNamedParams(hql, null);
        for(TrainingQuestion trainingQuestion:trainingQuestionList)
        {
            String hql1 = "from TrainingExamQuestion where question="+trainingQuestion.getId();
            List<TrainingExamQuestion>trainingExamQuestionList=trainingExamQuestionDao.getListByHQLWithNamedParams(hql1, null);
            if(trainingExamQuestionList.size()>0)
                status=0;
        }
        if(status==1)
        {
            TrainingMaterial trainingMaterial= trainingMaterialDao.get(id);
            trainingMaterialDao.delete(trainingMaterial);
            countMaterialByBank(trainingMaterial.getBank());
        }
        return status;
    }

    @Override
    public int deleteTrainingQuestion(User user, int id) {
        int status =1;
        String hql1 = "from TrainingExamQuestion where question="+id;
        List<TrainingExamQuestion>trainingExamQuestionList=trainingExamQuestionDao.getListByHQLWithNamedParams(hql1, null);
        if(trainingExamQuestionList.size()>0)
            status=0;
        TrainingQuestion trainingQuestion=trainingQuestionDao.get(id);
        if(status==1)
        {
            trainingQuestionDao.delete(trainingQuestion);
            this.countQuestionByMaterial(trainingQuestion.getMaterial());
        }
        return status;
    }

    @Override
    public int deleteDisableKey(User user, int id) {
        int status =1;
        TrainingQuestionKey trainingQuestionKey= trainingQuestionKeyDao.get(id);
        String hql1 = "from TrainingExamKey where questionKey="+id;
        List<TrainingExamKey>trainingExamKeyList=trainingExamKeyDao.getListByHQLWithNamedParams(hql1, null);
        if(trainingExamKeyList.size()>0)
            status=0;
        if(status==1&&trainingQuestionKey.getEnabled()==1){
            String hql = "from TrainingQuestionKey where question=" +trainingQuestionKey.getQuestion() + " and enabled=1 ";
            List<TrainingQuestionKey> trainingQuestionKeyList = trainingQuestionKeyDao.getListByHQLWithNamedParams(hql, null);
            if(trainingQuestionKeyList.size()<=4)
                status=-2;
        }
        if(status==1)
        {
            trainingQuestionKeyDao.deleteById(id);
        }
        return status;
    }

    @Override
    public int addChoiceQuestion(User user, int material, String content, String optionList) {
        int status=0;
        List<TrainingQuestionKey> list = JSONArray.parseArray(optionList, TrainingQuestionKey.class);
        for(int i=0;i<list.size();i++)
        {
            for(int j=1;j<list.size();j++)
            {
                if(i!=j&&list.get(i).getContent().equals(list.get(j).getContent()))
                {
                    status=-2;//选项重复
                }
            }
        }
        if(status!=-2) {
            String hql = "from TrainingQuestion where material=" + material + " and enabled=1 and type='1'";
            List<TrainingQuestion> trainingQuestionList = trainingQuestionDao.getListByHQLWithNamedParams(hql, null);
            if (trainingQuestionList.size() >= 4) {
                status = -1;
            } else {
                TrainingMaterial trainingMaterial = trainingMaterialDao.get(material);
                TrainingQuestion trainingQuestion = new TrainingQuestion();
                trainingQuestion.setOrg(trainingMaterial.getOrg());
                trainingQuestion.setCode(this.selectTrainingQuestionCode(material));
                trainingQuestion.setBank(trainingMaterial.getBank());
                trainingQuestion.setMaterial(trainingMaterial.getId());
                trainingQuestion.setType("1");
                trainingQuestion.setEnabled(1);
                trainingQuestion.setContent(content);
                trainingQuestion.setDistracterNum(list.size() - 1);
                trainingQuestion.setCreateDate(new Date());
                trainingQuestion.setCreateName(user.getUserName());
                trainingQuestion.setCreator(user.getUserID());
                trainingQuestionDao.save(trainingQuestion);
                trainingMaterial.setChoiceNum(trainingMaterial.getChoiceNum()+1);
                trainingMaterialDao.update(trainingMaterial);
                TrainingQuestionBank trainingQuestionBank=trainingQuestionBankDao.get(trainingMaterial.getBank());
                trainingQuestionBank.setChoiceNum(trainingQuestionBank.getChoiceNum()+1);
                trainingQuestionBankDao.update(trainingQuestionBank);
                for (TrainingQuestionKey trainingQuestionKey : list) {
                    trainingQuestionKey.setOrg(trainingQuestion.getOrg());
                    trainingQuestionKey.setBank(trainingQuestion.getBank());
                    trainingQuestionKey.setMaterial(trainingQuestion.getMaterial());
                    trainingQuestionKey.setQuestion(trainingQuestion.getId());
                    trainingQuestionKey.setEnabled(1);
                    trainingQuestionKey.setCreateDate(new Date());
                    trainingQuestionKey.setCreateName(user.getUserName());
                    trainingQuestionKey.setCreator(user.getUserID());
                    trainingQuestionKeyDao.save(trainingQuestionKey);
                }
                status = 1;
            }
        }
        return status;
    }

    @Override
    public int addTfQuestion(User user, int material, String contentList) {
        int status=0;
        String hql = "from TrainingQuestion where material="+material+" and enabled=1 and type='2'";
        List<TrainingQuestion> trainingQuestionList=trainingQuestionDao.getListByHQLWithNamedParams(hql, null);
        if(trainingQuestionList.size()>=4)
        {
            status=-1;
        }
        else{
            TrainingMaterial trainingMaterial = trainingMaterialDao.get(material);
            List<TrainingQuestionKey> list = JSONArray.parseArray(contentList, TrainingQuestionKey.class);
            for(TrainingQuestion trainingQuestion:trainingQuestionList)
            {
                String hql1="from TrainingQuestionKey where question="+trainingQuestion.getId()+" and isKey=1 and enabled=1";
                List<TrainingQuestionKey>trainingQuestionKeyList=trainingQuestionKeyDao.getListByHQLWithNamedParams(hql1, null);
                if(trainingQuestionKeyList.size()>0)
                {
                    for(TrainingQuestionKey trainingQuestionKey:list)
                    {
                        if(trainingQuestionKey.getIsKey()==1){
                            status=-3;
                        }
                    }
                }
            }
            if(status!=-3){
                for (TrainingQuestionKey trainingQuestionKey : list) {
                    TrainingQuestion trainingQuestion = new TrainingQuestion();
                    trainingQuestion.setOrg(trainingMaterial.getOrg());
                    trainingQuestion.setCode(this.selectTrainingQuestionCode(material));
                    trainingQuestion.setBank(trainingMaterial.getBank());
                    trainingQuestion.setMaterial(trainingMaterial.getId());
                    trainingQuestion.setType("2");
                    trainingQuestion.setEnabled(1);
                    trainingQuestion.setContent(trainingQuestionKey.getContent());
                    trainingQuestion.setCreateDate(new Date());
                    trainingQuestion.setCreateName(user.getUserName());
                    trainingQuestion.setCreator(user.getUserID());
                    trainingQuestionDao.save(trainingQuestion);

                    trainingQuestionKey.setOrg(trainingQuestion.getOrg());
                    trainingQuestionKey.setBank(trainingQuestion.getBank());
                    trainingQuestionKey.setMaterial(trainingQuestion.getMaterial());
                    trainingQuestionKey.setQuestion(trainingQuestion.getId());
                    trainingQuestionKey.setEnabled(1);
                    trainingQuestionKey.setCreateDate(new Date());
                    trainingQuestionKey.setCreateName(user.getUserName());
                    trainingQuestionKey.setCreator(user.getUserID());
                    trainingQuestionKeyDao.save(trainingQuestionKey);
                }
                trainingMaterial.setTfNum(trainingMaterial.getTfNum() + list.size());
                trainingMaterialDao.update(trainingMaterial);
                TrainingQuestionBank trainingQuestionBank = trainingQuestionBankDao.get(trainingMaterial.getBank());
                trainingQuestionBank.setTfNum(trainingQuestionBank.getTfNum() + list.size());
                trainingQuestionBankDao.update(trainingQuestionBank);
                status = 1;
            }
        }
        return status;
    }

    @Override
    public int addDisableKey(User user, int question, String disableKey) {
        TrainingQuestion trainingQuestion=trainingQuestionDao.get(question);
        String hql = "from TrainingQuestionKey where question=" +question + " and enabled=1 ";
        List<TrainingQuestionKey> trainingQuestionKeyList = trainingQuestionKeyDao.getListByHQLWithNamedParams(hql, null);
        int status=0;
        if(trainingQuestionKeyList.size()>=6)
        {
            status=-1;
        }else{
            TrainingQuestionKey trainingQuestionKey=new TrainingQuestionKey();
            trainingQuestionKey.setOrg(trainingQuestion.getOrg());
            trainingQuestionKey.setBank(trainingQuestion.getBank());
            trainingQuestionKey.setMaterial(trainingQuestion.getMaterial());
            trainingQuestionKey.setQuestion(trainingQuestion.getId());
            trainingQuestionKey.setContent(disableKey);
            trainingQuestionKey.setIsKey(0);
            trainingQuestionKey.setEnabled(1);
            trainingQuestionKey.setCreateDate(new Date());
            trainingQuestionKey.setCreateName(user.getUserName());
            trainingQuestionKey.setCreator(user.getUserID());
            trainingQuestionKeyDao.save(trainingQuestionKey);
            status=1;
        }
        return status;
    }

    @Override
    public RespTrainQuestionAndKeyList addQuestionJudge(int type, int id) {
        int yesNum=0;
        RespTrainQuestionAndKeyList respTrainQuestionList=new RespTrainQuestionAndKeyList();
        TrainingMaterial trainingMaterial=trainingMaterialDao.get(id);
        int status=1;
        String hql = "from TrainingQuestion where material=" +id  + " and enabled=1 and type='"+type+"'";
        List<TrainingQuestion> trainingQuestionList = trainingQuestionDao.getListByHQLWithNamedParams(hql, null);
        if (trainingQuestionList.size() >= 4) {
            status = -1;
        }else{
            if (type==2&trainingQuestionList.size()>0)
            {
                status=2;
            }else
            status = 1;
        }
        if(trainingQuestionList.size()<4 &&type==2) {
            List<RespTrainQuestionKeyList> respTrainQuestionKeyList=new ArrayList<>();
            for(TrainingQuestion trainingQuestion:trainingQuestionList)
            {
                RespTrainQuestionKeyList respTrainQuestionKeyList1=new RespTrainQuestionKeyList();
                respTrainQuestionKeyList1.setTrainingQuestion(trainingQuestion);
                String hql1 = "from TrainingQuestionKey where question="+trainingQuestion.getId()+" and enabled=1 and isKey=1";
                List<TrainingQuestionKey> list = trainingQuestionKeyDao.getListByHQLWithNamedParams(hql1, null);
                if(list.size()>0)
                    yesNum=1;
            }
        }
        respTrainQuestionList.setYesNum(yesNum);
        respTrainQuestionList.setStatus(status);
        respTrainQuestionList.setTrainingMaterial(trainingMaterial);
        return respTrainQuestionList;
    }

    @Override
    public List<TrainingQuestionBank> selectTrainingQuestionBankListByKeyword(User user, Integer type, PageInfo pageInfo, String keyword,Integer status) {
        String hql = "from TrainingQuestionBank where creator = "+ user.getUserID()+" and enabled="+status+" and (code like '%"+keyword+"%' or name like '%"+keyword+"%')" ;
        if(type==1)//1代号降序排序
            hql=hql+" ORDER BY code desc";
        else if(type==2)//2代号升序排序
            hql=hql+" ORDER BY code ASC";
        else if(type==3)//3创建时间降序排序
            hql=hql+" ORDER BY createDate desc";
        else if(type==4)//4创建时间升序排序
            hql=hql+" ORDER BY createDate ASC";
        else if(type==5)//5素材数量降序排序
            hql=hql+" ORDER BY materialNum desc";
        else if(type==6)//6素材数量升序排序
            hql=hql+" ORDER BY materialNum ASC";
        List<TrainingQuestionBank> list = trainingQuestionBankDao.getListByHQLWithNamedParams(hql,null,pageInfo);
        return list;
    }

    @Override
    public RespTrainMaterialListPage selectQuestionBankDetailByKeyword(PageInfo pageInfo, int id, Integer type, Integer status, String keyword) {
        RespTrainMaterialListPage respTrainMaterialListPage=new RespTrainMaterialListPage();
        TrainingQuestionBank trainingQuestionBank= trainingQuestionBankDao.get(id);
        String hql = "from TrainingMaterial where bank="+id+" and enabled="+status+"  and (code like '%"+keyword+"%' or content like '%"+keyword+"%')";
        if(type==1)//1代号降序排序
            hql=hql+" ORDER BY code desc";
        else if(type==2)//2代号升序排序
            hql=hql+" ORDER BY code ASC";
        else if(type==3)//3创建时间降序排序
            hql=hql+" ORDER BY createDate desc";
        else if(type==4)//4创建时间升序排序
            hql=hql+" ORDER BY createDate ASC";
        else if(type==5)//5题数量降序排序
            hql=hql+" ORDER BY (tfNum+choiceNum) desc";
        else if(type==6)//6素题量升序排序
            hql=hql+" ORDER BY (tfNum+choiceNum) ASC";
        List<TrainingMaterial> list = trainingMaterialDao.getListByHQLWithNamedParams(hql,null,pageInfo);
        respTrainMaterialListPage.setTrainingMaterialList(list);
        respTrainMaterialListPage.setTrainingQuestionBank(trainingQuestionBank);
        return respTrainMaterialListPage;
    }

    @Override
    public RespTrainMaterialListPage selectQuestionBankInfo(int id) {
        RespTrainMaterialListPage respTrainMaterialListPage=new RespTrainMaterialListPage();
        TrainingQuestionBank trainingQuestionBank= trainingQuestionBankDao.get(id);
        String hql = "from TrainingQuestionBankAttachment where bank="+id;
        List<TrainingQuestionBankAttachment> list = trainingQuestionBankAttachmentDao.getListByHQLWithNamedParams(hql,null);
        respTrainMaterialListPage.setTrainingQuestionBankAttachmentList(list);
        respTrainMaterialListPage.setTrainingQuestionBank(trainingQuestionBank);
        return respTrainMaterialListPage;
    }

    @Override
    public RespTrainBankHistoryList selectTrainBankHistoryList(int id) {
        RespTrainBankHistoryList respTrainBankHistory=new RespTrainBankHistoryList();
        TrainingQuestionBank trainingQuestionBank=trainingQuestionBankDao.get(id);
        respTrainBankHistory.setTrainingQuestionBank(trainingQuestionBank);
        String hql = "from TrainingQuestionBankHistory where bank="+id;
        List<TrainingQuestionBankHistory> list = trainingQuestionBankHistoryDao.getListByHQLWithNamedParams(hql,null);
        respTrainBankHistory.setTrainingQuestionBankHistoryList(list);
        return respTrainBankHistory;
    }

    @Override
    public RespTrainBankAttachmentHistoryList selectTrainBankHistoryInfo(int id) {
        RespTrainBankAttachmentHistoryList respTrainBankAttachmentHistoryList=new RespTrainBankAttachmentHistoryList();
        TrainingQuestionBankHistory trainingQuestionBankHistory= trainingQuestionBankHistoryDao.get(id);
        respTrainBankAttachmentHistoryList.setTrainingQuestionBankHistory(trainingQuestionBankHistory);
        String hql = "from TrainingQuestionBankAttachmentHistory where bankHistory="+id;
        List<TrainingQuestionBankAttachmentHistory> list = trainingQuestionBankAttachmentHistoryDao.getListByHQLWithNamedParams(hql,null);
        respTrainBankAttachmentHistoryList.setTrainingQuestionBankAttachmentHistoryList(list);
        return respTrainBankAttachmentHistoryList;
    }

    @Override
    public void countQuestionByMaterial(int id) {
        TrainingMaterial trainingMaterial=trainingMaterialDao.get(id);
        String hql = "from TrainingQuestion where material="+id+" and enabled=1 and type='2'";
        List<TrainingQuestion> trainingQuestionList=trainingQuestionDao.getListByHQLWithNamedParams(hql, null);
        trainingMaterial.setTfNum(trainingQuestionList.size());
        String hql1 = "from TrainingQuestion where material="+id+" and enabled=1 and type='1'";
        List<TrainingQuestion> trainingQuestionList1=trainingQuestionDao.getListByHQLWithNamedParams(hql1, null);
        trainingMaterial.setChoiceNum(trainingQuestionList1.size());
        String hql2 = "from TrainingQuestion where material="+id+" and enabled=0";
        List<TrainingQuestion> trainingQuestionList2=trainingQuestionDao.getListByHQLWithNamedParams(hql2, null);
        trainingMaterial.setDisabledNum(trainingQuestionList2.size());
        trainingMaterialDao.update(trainingMaterial);

        int tfNum=0;
        int choiceNum=0;
        int disabledNum=0;
        TrainingQuestionBank trainingQuestionBank=trainingQuestionBankDao.get(trainingMaterial.getBank());
        String hqlM="from TrainingMaterial where bank="+trainingMaterial.getBank()+" and enabled=1";
        List<TrainingMaterial> trainingMaterialList=trainingQuestionDao.getListByHQLWithNamedParams(hqlM, null);
        for(TrainingMaterial trainingMaterial1:trainingMaterialList) {
            String hqlb = "from TrainingQuestion where material=" + trainingMaterial1.getId() + " and enabled=1 and type='2'";
            List<TrainingQuestion> trainingQuestionListb = trainingQuestionDao.getListByHQLWithNamedParams(hqlb, null);
            tfNum=tfNum+trainingQuestionListb.size();
            String hql1b = "from TrainingQuestion where material=" + trainingMaterial1.getId()+ " and enabled=1 and type='1'";
            List<TrainingQuestion> trainingQuestionList1b = trainingQuestionDao.getListByHQLWithNamedParams(hql1b, null);
            choiceNum=choiceNum+trainingQuestionList1b.size();
            String hql2b = "from TrainingQuestion where material=" + trainingMaterial1.getId() + " and enabled=0";
            List<TrainingQuestion> trainingQuestionList2b = trainingQuestionDao.getListByHQLWithNamedParams(hql2b, null);
            disabledNum=disabledNum+trainingQuestionList2b.size();
        }
        trainingQuestionBank.setTfNum(tfNum);
        trainingQuestionBank.setChoiceNum(choiceNum);
        trainingQuestionBank.setDisabledNum(disabledNum);
        trainingQuestionBankDao.update(trainingQuestionBank);

    }
    @Override
    public void countMaterialByBank(int id) {
        TrainingQuestionBank trainingQuestionBank=trainingQuestionBankDao.get(id);
        String hql = "from TrainingMaterial where bank="+id+" and enabled=1";
        List<TrainingMaterial> trainingMaterialList=trainingMaterialDao.getListByHQLWithNamedParams(hql, null);
        trainingQuestionBank.setMaterialNum(trainingMaterialList.size());
        int tfNum=0;
        int choiceNum=0;
        int disabledNum=0;
        for(TrainingMaterial trainingMaterial1:trainingMaterialList) {
            String hqlb = "from TrainingQuestion where material=" + trainingMaterial1.getId() + " and enabled=1 and type='2'";
            List<TrainingQuestion> trainingQuestionListb = trainingQuestionDao.getListByHQLWithNamedParams(hqlb, null);
            tfNum=tfNum+trainingQuestionListb.size();
            String hql1b = "from TrainingQuestion where material=" + trainingMaterial1.getId()+ " and enabled=1 and type='1'";
            List<TrainingQuestion> trainingQuestionList1b = trainingQuestionDao.getListByHQLWithNamedParams(hql1b, null);
            choiceNum=choiceNum+trainingQuestionList1b.size();
            String hql2b = "from TrainingQuestion where material=" + trainingMaterial1.getId() + " and enabled=0";
            List<TrainingQuestion> trainingQuestionList2b = trainingQuestionDao.getListByHQLWithNamedParams(hql2b, null);
            disabledNum=disabledNum+trainingQuestionList2b.size();
        }
        trainingQuestionBank.setTfNum(tfNum);
        trainingQuestionBank.setChoiceNum(choiceNum);
        trainingQuestionBank.setDisabledNum(disabledNum);
        trainingQuestionBankDao.update(trainingQuestionBank);
    }

    @Override
    public TrainingQuestionBankAttachment getTrainingQuestionBankAttachment(int id) {
        return trainingQuestionBankAttachmentDao.get(id);
    }
    @Override
    public TrainingQuestionBankAttachmentHistory getTrainingQuestionBankAttachmentHistory(int id) {
        return trainingQuestionBankAttachmentHistoryDao.get(id);
    }
    @Override
    public TrainingQuestionBankHistory getTrainingQuestionBankHistory(int id) {
        return trainingQuestionBankHistoryDao.get(id);
    }
    @Override
    public TrainingQuestionBank getTrainingQuestionBank(int id) {
        return trainingQuestionBankDao.get(id);
    }


}
