package cn.sphd.miners.modules.trainManage.dto;

import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.trainManage.entity.TrainingExam;
import cn.sphd.miners.modules.trainManage.entity.TrainingExamUser;

/**
 * @ClassName RespExamForUser
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/3/3 15:33
 * @Version 1.0
 */
public class RespExamForUser {
    private TrainingExam trainingExam;
    private TrainingExamUser trainingExamUser;
    private User user;

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public TrainingExam getTrainingExam() {
        return trainingExam;
    }

    public void setTrainingExam(TrainingExam trainingExam) {
        this.trainingExam = trainingExam;
    }

    public TrainingExamUser getTrainingExamUser() {
        return trainingExamUser;
    }

    public void setTrainingExamUser(TrainingExamUser trainingExamUser) {
        this.trainingExamUser = trainingExamUser;
    }
}
