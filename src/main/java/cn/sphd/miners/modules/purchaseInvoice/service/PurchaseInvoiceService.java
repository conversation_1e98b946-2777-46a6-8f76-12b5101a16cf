package cn.sphd.miners.modules.purchaseInvoice.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.finance.entity.FinancePayment;
import cn.sphd.miners.modules.loan.entity.LoanBiz;
import cn.sphd.miners.modules.material.entity.SrmSupplier;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.purchaseInvoice.entity.PoPaymentApplication;
import cn.sphd.miners.modules.purchaseInvoice.entity.PoPaymentInvoice;
import cn.sphd.miners.modules.purchaseInvoice.entity.PoPaymentInvoiceAttachment;
import cn.sphd.miners.modules.purchaseInvoice.entity.PoPaymentInvoiceItem;
import cn.sphd.miners.modules.sales.model.PoOrders;
import cn.sphd.miners.modules.sales.model.PoOrdersPrepayment;
import cn.sphd.miners.modules.system.entity.User;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * lyx on 2021/6/16
 */
public interface PurchaseInvoiceService {
    Map<String,Object> purchaseInvoiceApply(User user,String type,Double amountTotal,Double billAmountTotal,Integer orders,String applicationReason,String applicationDesc,String purchaseInvoice);

    List<Map<String,Object>> getPurchaseInvoices(Integer org,Integer toUser,Integer fromUser,String approveStatus,String billState,String payState,String approveStatusApplication,Integer type,Integer businessTypePayment,String approveStatusPayment,Integer... businessType);

    List<Map<String,Object>> getPurchaseInvoicesByState(Integer userId,Integer org,String approveStatus,String type, String billState,String payState,Integer billStateType);

    Map<String,Object> paymentOnlineApproval(User user,Integer approvalProcessId,String approveStatus,String reason,String approveSelect);

    Map<String,Object> purchaseOnlineApproval(User user,Integer approvalProcessId,String approveStatus,String reason);

    Map<String,Object> purchasePaymentApproval(User user,Integer approvalProcessId,String approveStatus,String reason,String approveSelect);

    Map<String,Object> purchaseOfflineApproval(User user,Integer approvalProcessId,String approveStatus);

    Map<String,Object> paymentApprovalApproval(User user,Integer approvalProcessId);

    Map<String,Object> payableFinanceApproval(User user, Integer approvalProcessId, String method, Date planDate, Date factDate, Integer accountId, String summary, Integer invoiceId, String expireDate, String receiveDate, String receiver, String operator, String oppositeCorp, String operatorName, String factAmount);

    Map<String,Object> reviewedApprovalApproval(User user,Integer approvalProcessId);

    Map<String,Object> payFinanceApproval(User user,Integer approvalProcessId,Date factDate, String summary,String operatorName);

    Map<String,Object> getPurchaseInvoiceDetail(Integer applicationId,Integer financePaymentId);

    Map<String,Object> getFinancePayment(Integer applicationId,String businessTypePH);

    Map<String,Object> cancelPurchaseInvoice(User user,Integer applicationId);

    List<PoPaymentInvoice> getInvoicesByApplicationId(Integer applicationId);

    List<PoPaymentInvoiceItem> getInvoicesItemById(Integer applicationId, Integer invoiceId);

    Map<String,Object> getUpdateFinancePayment(User user, Integer type, Integer approvalProcessId, String method, Date planDate, Date factDate, Integer accountId,
                                               String summary, Integer invoiceId, String expireDate, String receiveDate, String receiver, String operator, String oppositeCorp, String factAmount);

    PoPaymentInvoiceAttachment getInvoicePictureById(Integer invoiceId);
    List<PoPaymentInvoiceAttachment> getInvoicePicturesById(Integer invoiceId);

    Map<String,Object> getInvoiceItemDetail(Integer applicationId,String invoiceCategory,String feeCategory);

    PoPaymentInvoiceAttachment getInvoiceAttachment(Integer id);

    PoPaymentApplication getApplicationById(Integer applicationId);

    void purchaseInvoiceRejectSend(int loginNum, int operate, HashMap<String,Object> hashMap, Integer toUserId, String pass, String title, String content, String code);

    HashMap<String,Object> rejectSendType(PoPaymentApplication poPaymentApplication, PoOrders poOrders, Integer orderId, PoOrdersPrepayment poOrdersPrepayment);

    List<Map<String, Object>> getAdvancePayments(Integer org,Integer toUser, Integer fromUser,Integer businessType,String approveStatus,String prepaymentStatus,Integer type);

    Map<String,Object> advancePaymentDetail(Integer orderPrepaymentId,Integer financePaymentId);

    Map<String,Object> advancePaymentPayApproved(User user,Integer approvalProcessId);

    PoOrdersPrepayment getPrepaymentById(Long prepaymentId);

//    Integer getApplicationNum(Integer business,String payStatus,String businessType);

    Map<String,Object> payDetail(Integer applicationId,Integer orderPrepaymentId,String status,String businessType);

    Map<String,Object> getMostDebitLoan(User user,String yearMonth,Integer finishType, PageInfo pageInfo);

    SrmSupplier getSupplier(Integer orderId);  //获取供应商

    void loanBizRejectSend(int loginNum, int operate, LoanBiz loanBiz, Integer toUserId, String pass, String title, String content, String code);

    Map<String,Object> cashPurchase(User user, PoPaymentApplication poPaymentApplication, PoOrders poOrders, PoOrdersPrepayment poOrdersPrepayment, LoanBiz loanBiz,
          BigDecimal amount, BigDecimal billAmount, Integer business, String source, String method, Integer accountId, String summary, Date factDate, FinancePayment financePayment);

    Map<String,Object> invoicePurchase(User user,BigDecimal amount,BigDecimal billAmount,Integer business,String source,FinancePayment financePayment,
          String method,Integer accountId,String summary,Integer invoiceId,String expireDate,String receiveDate,String receiver,String operator,String oppositeCorp);

    Map<String,Object> financeCommonContent(PoPaymentApplication poPaymentApplication,PoOrders poOrders,PoOrdersPrepayment poOrdersPrepayment,FinancePayment financePayment,BigDecimal amount,BigDecimal billAmount,
                                            String source,User user,ApprovalProcess approvalProcess,Map<String,Object> map,Date factDate,String summary,String operatorName,Integer type);

    void addLoanBizAll(User user,Integer paymentApply, FinancePayment financePayment, Integer ordersPrepayment,Byte bizType,Byte type,BigDecimal loanAmount, PoOrders poOrders,String tracePath,Integer supplier, String supplierName,Integer origBiz);
}
