<template>
  <div id="purchasePaymentApproval">
    <TY_NavTop title="采购部门的付款" isSearch="true" @toggleList="search"></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待处理" name="1" closable='false' :msgCount="onlineAuditHandle.length">
        <div class="tip">
          <p>下列付款申请系采购部门提交，您审批后方可付款！</p>
        </div>
        <div>
          <a class="ui-cell" v-on:click="jump('43' + item.poPaymentApplication.id)" v-for="(item, index) in onlineAuditHandle" v-bind:key="index">
            <div class="item_fn">
              <div class="supName">{{item.fullName}} <span class="right">{{ item.codeName }}</span></div>
              <div class="oInfo">
                <p> {{ item.poPaymentApplication.type | formatType(item.poPaymentApplication.amount) }}</p>
                <p v-if="Number(item.poPaymentApplication.billCount) > 0">票据 {{item.poPaymentApplication.billCount }} 张，共 {{item.poPaymentApplication.billAmount.toFixed(2) }} 元</p>
                <p v-else>本次付款申请没有附带的票据</p>
              </div>
            </div>
            <div class="txt_right">申请人：{{ item.poPaymentApplication.createName  }}
              {{item.poPaymentApplication.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="已批准" name="2" closable='false'>
        <div>
          <a class="ui-cell" v-on:click="jump('44' + item.poPaymentApplication.id )" v-for="(item, index) in onlineAuditOK" v-bind:key="index">
            <div class="item_fn">
              <div class="supName">{{item.fullName}} <span class="right">{{ item.codeName }}</span></div>
              <div class="oInfo">
                <p> {{ item.poPaymentApplication.type | formatType(item.poPaymentApplication.amount) }}</p>
                <p v-if="Number(item.poPaymentApplication.billCount) > 0">票据 {{item.poPaymentApplication.billCount }} 张，共 {{item.poPaymentApplication.billAmount.toFixed(2) }} 元</p>
                <p v-else>本次付款申请没有附带的票据</p>
              </div>
            </div>
            <div class="txt_right">申请人：{{ item.poPaymentApplication.createName  }}
              {{item.poPaymentApplication.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>
<style lang="less">
#purchasePaymentApproval{
  overflow: auto;
  .right{ float: right;  }
  .ui-cell{ display: block;  }
  .oInfo{ padding:5px 20px; }
  .txt_right{ text-align: right;  }
  .tabCount{
    top:0;
    right:0 ;
    left:auto;
  }
  .tabs-tab{
    width:50%;
  }
  .tip{
    padding:5px 12px;
    background-color: #E2EFDA;
    display: block;
  }
}
</style>

<script>
export default {
  name: 'reimburseBillVerification',
  data () {
    return {
      onlineAuditHandle: [],
      onlineAuditOK: [],
      offlineAuditHandle: [],
      listenersUid: [],
      tabValue: '1'
    }
  },
  filters: {
    formatType: function (num, amount) {
      switch (Number(num)){
        case 1:
          return "本次仅提交票据，不付款";
          break;
        case 2:
          return `申请付款${amount.toFixed(2)}元`;
          break;
        case 3:
          return `申请付款${amount.toFixed(2)}元`;
          break;
      }
    },
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  created () {
    this.tabValue = localStorage.navNum || 1
    let that = this
    this.getList()
    this.listenersUid = [
      // 采购审批者-采购部门的付款-待处理
      this.sphdSocket.subscribe('paymentApprovalHandle', function (data) {
        let getData = JSON.parse(data)
        console.log(getData)
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.onlineAuditHandle.unshift(getData)
        } else if (operate < 0) {
          let id = getData.poPaymentApplication.id
          let _index = -1
          that.onlineAuditHandle.forEach(function (item, index) {
            if (item.poPaymentApplication.id === id) {
              _index = index
            }
          })
          if (_index > -1) {
            that.onlineAuditHandle.splice(_index, 1)
          }
        }
      }, null, 'user'),
      this.sphdSocket.subscribe('paymentApprovalApproved', function (data) {
        let getData = JSON.parse(data)
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.onlineAuditOK.unshift(getData)
        } else if (operate < 0) {
          let id = getData.poPaymentApplication.id
          let _index = -1
          that.onlineAuditOK.forEach(function (item, index) {
            if (item.poPaymentApplication.id === id) {
              _index = index
            }
          })
          if (_index > -1) {
            that.onlineAuditOK.splice(_index, 1)
          }
        }
      }, null, 'user'),
    ]
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    getList: function () {
      let that = this
      this.axios.get('../../../purchaseInvoice/getPurchasePaymentList.do')
        .then(function (response) {
          let res = response.data.data;
          let list = res.purchasePaymentHandle || [] // 待处理
          let list2 = res.purchasePaymentApproved || [] // 已批准
          that.onlineAuditHandle = list;
          that.onlineAuditOK = list2;
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    jump: function (id) {
      this.$router.push({
        path: `/purchaseInvoiceDetails2/${id}`
      })
    },
    search: function () {
      this.$router.push({
        path: `/reimburseVerificationBillsQuery`
      })
    }
  }
}
</script>
