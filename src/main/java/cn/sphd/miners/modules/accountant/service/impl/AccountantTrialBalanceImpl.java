package cn.sphd.miners.modules.accountant.service.impl;

import cn.sphd.miners.modules.accountant.entity.DirAndBalance;
import cn.sphd.miners.modules.accountant.entity.SubjectBalanceSheet;
import cn.sphd.miners.modules.accountant.mapper.SubjectBalanceSheetMapper;
import cn.sphd.miners.modules.accountant.service.AccountantTrialBalanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by root on 17-5-22.
 */
@Service
@Transactional(readOnly=false)
public class AccountantTrialBalanceImpl implements AccountantTrialBalanceService {

    @Autowired
    SubjectBalanceSheetMapper balanceSheetMapper;
    @Autowired
    VoucherUpdate voucherUpdate;

    @Override
    public List<SubjectBalanceSheet> getTrialBalanceData(SubjectBalanceSheet subBalanSheet) {
        ReportsUtils reportsUtils = new ReportsUtils();
        List<SubjectBalanceSheet> trialBalances = balanceSheetMapper.getTopSubject(subBalanSheet);
//        if(trialBalances != null)
//        {
//            for (SubjectBalanceSheet trial : trialBalances)
//            {
//                String subcode = trial.getSubject();
//                trial.setPeroid(subBalanSheet.getPeroid());
                //统计一级科目的余额和发生额，把他下面的二级和三级科目的金额统计起来
//                statisticsBalance(trial);
                //确定余额统计后该科目的方向
//                reportsUtils.settingDirection(trial);
                //把所有余额变成正数
//                reportsUtils.settingSymbolAbs(trial);
//            }
//        }
        return trialBalances;
    }

    private void statisticsBalance(SubjectBalanceSheet trial) {
        //把余额根据方向变成正负数，统计的时候只算加法即可
//        ReportsUtils reportsUtils = new ReportsUtils();
//        reportsUtils.settingSybol(trial);
        String subject = trial.getSubject();

        int level = trial.getLevel();
        if (level > 0 && level < 3)
        {
            SubjectBalanceSheet childParam = new SubjectBalanceSheet();
            childParam.setOid(trial.getOid());
            childParam.setPeroid(trial.getPeroid());
            childParam.setParent(trial.getSubject());
            List<SubjectBalanceSheet> children = balanceSheetMapper.getChildrenBalanceSheet(childParam);
            if(children != null)
            {
                for (SubjectBalanceSheet child : children)
                {
                    //把子科目的余额加到父科目上
                    BigDecimal CpreviousBalance = child.getPreviousBalance() == null ? BigDecimal.valueOf(0) : child.getPreviousBalance(); //子科目期初余额
                    BigDecimal Cbalance = child.getBalance() == null ? BigDecimal.valueOf(0) : child.getBalance();                 //子科目期末余额
                    BigDecimal Ccredit = child.getCredit() == null ? BigDecimal.valueOf(0) : child.getCredit();                   //子科目的本期借方发生额
                    BigDecimal Cdebit = child.getDebit() == null ? BigDecimal.valueOf(0) : child.getDebit();                     //子科目的本期贷方发生额

                    String childDirPerent = child.getPreviousDirection() == null ? "3" : child.getPreviousDirection();
                    String childBalanceDir = child.getBalanceDirection() == null ? "3" : child.getBalanceDirection();

                    BigDecimal PpreviousBalance = trial.getPreviousBalance() == null ? BigDecimal.valueOf(0) : trial.getPreviousBalance(); //父科目期初余额
                    BigDecimal Pbalance = trial.getBalance() == null ? BigDecimal.valueOf(0) : trial.getBalance();                 //父科目期末余额
                    BigDecimal Pcredit = trial.getCredit() == null ? BigDecimal.valueOf(0) : trial.getCredit();                   //父科目的本期借方发生额
                    BigDecimal Pdebit = trial.getDebit() == null ? BigDecimal.valueOf(0) : trial.getDebit();                     //父科目的本期贷方发生额

                    String preDirPerent = trial.getPreviousDirection() == null ?  "3" : trial.getPreviousDirection();
                    String preBalanceDir = trial.getBalanceDirection() == null ?  "3" : trial.getBalanceDirection();

                    DirAndBalance dab = voucherUpdate.getBalanceAndDirection(preDirPerent,PpreviousBalance,childDirPerent,CpreviousBalance);
                    trial.setPreviousBalance(dab.getBalance());
                    trial.setPreviousDirection(dab.getDir());
                    dab = voucherUpdate.getBalanceAndDirection(preBalanceDir,Pbalance,childBalanceDir,Cbalance);
                    trial.setBalance(dab.getBalance());
                    trial.setBalanceDirection(dab.getDir());

                    trial.setCredit(Pcredit.add(Ccredit));
                    trial.setDebit(Pdebit.add(Cdebit));

                }
            }
        }
    }





}
