<template>
  <div id="purchaseInvoiceDetails6">
    <TY_NavTop :title="ttl" ></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="rejectPng" v-if="isReject">
        <img :src="rejectPng">
      </div>
      <div>
        <!--        <a class="ui-cell" v-on:click="jump('25' + item.poOrders.id, 0)" v-for="(item, index) in cashierPaymentHandle" v-bind:key="index">-->
        <div class="con1">
          <div class="oInfo" v-if="details.poOrdersPrepayment" style="padding-left: 1px;padding-right: 1px">
            <p v-if="details.poOrdersPrepayment"> {{ details.poOrdersPrepayment.type | formatType(details.poOrdersPrepayment.amount) }}</p>
            <p v-if="details.poOrdersPrepayment.type !== '3'">
              计划付款金额为{{details.poOrdersPrepayment.planAmount}}元
            </p>
            <div style="display: flex">
              <p v-if="details.poOrdersPrepayment.type !== '3'">
                计划付款时间{{details.poOrdersPrepayment.planDate |formatDay('YYYY-MM-DD HH:mm:ss')}}
              </p>
              <p v-if="details.poOrdersPrepayment.type !== '3'" style="margin-left: 16px;">计划付款方式{{isture}}</p>
            </div>
            <p v-else>本次付款申请没有附带的票据</p>
          </div>
          <p class="txt_right" v-if="details.poOrders">
            申请人：{{ details.poOrders.createName  }} {{details.poOrders.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}
          </p>
        </div>
        <div class="panel-content">
          <div>
            <table style="width:100%;" v-if="details.poOrders">
              <tr v-if="details.srmSupplier">
                <td>供应商：{{ details.srmSupplier.fullName  }}</td>
                <td>{{ details.srmSupplier.codeName }}</td>
              </tr>
              <tr>
                <td>订单总额：{{ details.poOrders.amount && details.poOrders.amount.toFixed(2) }}元</td>
                <td>订单号：{{ details.poOrders.sn }} </td>
              </tr>
              <tr>
                <td>订单日期：{{ details.poOrders.createDate | formatDay('YYYY-MM-DD') }}</td>
                <td></td>
              </tr>
            </table>
          </div>
          <div class="panel-content">
            <div class="text-right">
              <el-link style="color: #409eff; margin-right: 20px;" @click="orderProgressRade">订单查看</el-link>
              <el-link v-on:click="toggle = -toggle">审批记录
                <i :class="toggle === -1?'el-icon-arrow-down':'el-icon-arrow-up'"></i>
              </el-link>
            </div>
            <div v-show="toggle > 0">
              <div class="item-content">
                <div class="processItem" v-if="details.poOrders">
                  <div>提交报销申请</div>
                  <div>申请人</div>
                  <div>{{details.poOrders.createName | stringSplit(4)}} {{ details.poOrders.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-for="it in details.approvalProcessList" v-bind:key="it.id">
                  <div v-if="it.approveStatus === '2' && it.businessType == 23" class="processItem"> <!--采购订单审批-->
                    <div>在线审核通过</div>
                    <div>审批人</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 45" class="processItem">
                    <div>在线审核通过</div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 46" class="processItem">
                    <div>在线审批通过</div>
                    <div>审批人</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 47" class="processItem">
                    <div>线下审核通过</div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 48" class="processItem">
                    <div>采购审批通过</div>
                    <div>审批人</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 49" class="processItem">
                    <div>付款审批通过</div>
                    <div>审批人</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 50" class="processItem">
                    <div>付款方式确认</div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 51" class="processItem">
                    <div>付款复核通过</div>
                    <div>审批人</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 53" class="processItem">
                    <div>付款方式修改</div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 63" class="processItem">  <!--待付款审批-->
                    <div>付款审批通过</div>
                    <div>审批人</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 64" class="processItem">   <!--可付款-->
                    <div>付款方式确认</div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 65" class="processItem">  <!--待复核-->
                    <div>付款复核通过</div>
                    <div>审批人</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 66" class="processItem">  <!--待付款-->
                    <div v-if="details.financePayment.method === '1'">现金付讫</div>
                    <div v-if="details.financePayment.method === '3'">报销款已转支票</div>
                    <div v-if="details.financePayment.method === '4'">报销款已转承兑汇票</div>
                    <div v-if="details.financePayment.method === '5'">报销款已转账</div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                  <div v-if="it.approveStatus === '2' && it.businessType == 67" class="processItem">  <!--付款方式修改-->
                    <div>付款方式修改</div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>

                  <div v-if="it.approveStatus === '5'" class="processItem">
                    <div>
                      <span v-if="it.businessType == '1'">现金付讫</span>
                      <span v-if="it.businessType == '2'">报销款已转支票</span>
                      <span v-if="it.businessType == '3'">报销款已转支票</span>
                      <span v-if="it.businessType == '4'">报销款已转承兑汇票</span>
                      <span v-if="it.businessType == '5'">报销款已转账</span>
                    </div>
                    <div>出纳员</div>
                    <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                </div>
                <!-- 最后一条审批记录展示 -->
                <div class="color-red" v-if="details.approvalProcessList[0].approveStatus === '3' && details.approvalProcessList[0].businessType===46">
                  <div style="margin-top:15px;"> 票据在线审核未通过！</div>
                  <div><span style="float: left">理由：</span> <div style="margin-left: 50px;white-space: pre-wrap;">{{ details.approvalProcessList[0].reason }}</div></div>
                  <br>
                  <div class="processItem">
                    <div></div>
                    <div>审批人</div>
                    <div>{{details.approvalProcessList[0].userName | stringSplit(4)}} {{details.approvalProcessList[0].handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                </div>
                <div class="color-red" v-if="details.approvalProcessList[0].approveStatus === '3' && details.approvalProcessList[0].businessType=== 45">
                  <div style="margin-top:15px;">报销申请被驳回！</div>
                  <div>驳回理由： {{details.approvalProcessList[0].reason || ''}}</div>
                  <div class="processItem">
                    <div></div>
                    <div>审批人</div>
                    <div>{{details.approvalProcessList[0].userName | stringSplit(4)}} {{details.approvalProcessList[0].handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                </div>
                <div class="color-red" v-if="details.approvalProcessList[0].approveStatus === '3' && details.approvalProcessList[0].businessType=== 47">
                  <div style="margin-top:15px;">  线下审核未通过！</div>
                  <div>驳回理由: 实际票据与在线审核通过的不一致。</div>
                  <div class="processItem">
                    <div></div>
                    <div>出纳员</div>
                    <div>{{details.approvalProcessList[0].userName | stringSplit(4)}} {{details.approvalProcessList[0].handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                  </div>
                </div>
                <div class="color-blue">
                  <span v-else-if="nowApproveItem.approveStatus === '1' && nowApproveItem.userName !== 0 && nowApproveItem.userName !== null">{{nowApproveItem.userName}}为下一个审批人</span>
                  <span v-if="details.approvalProcessList[0].approveStatus === '5'">本次报销已完结！</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="panel-content">
          <div style="background: #fff; padding:10px 15px;">
            <span>付款事由：</span><span style="color:red">采购的预付款</span>
          </div>
        </div>
        <div class="panel-content" v-if="code === 21">
          <div style="margin-top: 50px;">
            <el-radio-group v-model="approveStaPayment" size="small" style="margin-left: 20px;">
              <el-radio label="2">同意付款！</el-radio><br/>
            </el-radio-group>
            <div style="color:#0a9bee; font-size: 12px; line-height: 30px; margin-left: 29px;">注：对付款有异议，需进行调查时可暂不操作。</div>
          </div>
          <div class="handle_button" style="text-align: center;">
            <input type="submit" style="background: #0a9bee; color:#fff; width: 100px; text-align: center;" class="ui-btn" value="确定" @click="approvePayment">
          </div>
        </div>
        <!--        </a>-->
      </div>
    </div>
  </div>
</template>

<script>
import rejectPng from '../../../images/reject.png'
export default {
  name: "purchaseInvoiceDeails6",
  data(){
    return {
      ttl:"",
      isReject: false, // 判断是否驳回来显示驳回图片
      isRevoke: false, // 判断是否显示撤销
      isNowApprover: false,
      rejectPng: rejectPng, // 驳回图片
      dialogVisible: false,
      loading: true,
      toggle: -1, // 审批记录控制显隐
      listenersUid: [],
      approveSelect: [],
      approveSta: '',
      approveStaPayment: '',
      approveStatus: '0',
      reason: "",
      title: '采购的预付款',
      invoiceID: "",
      activeName: "bill",
      // orderProgressRade: false,
      code: 0,
      details: {},
      cashierPaymentHandle: []
    }
  },
  filters: {
    formReason: function (num, applicationDesc) {
      switch (Number(num)){
        case 1:
          return "符合合同约定";
          break;
        case 2:
          return applicationDesc;
          break;
      }
    },
    formatCat: function (num) {
      switch (Number(num)){
        case 1:
          return "增值税专用发票";
          break;
        case 2:
          return "增值税普通发票";
          break;
        case 3:
          return "收据";
        case 4:
          return "其他发票";
          break;
      }
    },
    formatType: function (num, amount) {
      switch (Number(num)){
        case 1:
          return "本次仅提交票据，不付款";
          break;
        case 2:
          return `申请付款：${ amount.toFixed(2) }元`;
          break;
        case 3:
          return `申请付款：${ amount.toFixed(2) }元`;
          break;
      }
    },
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  created () {
    let that = this;
    let paramCode = this.$route.params.id
    that.code = Number(paramCode.substring(0, 2)) // 41-待处理; 42-已批准
    console.log('code:', that.code )
    if(that.code === 44 || that.code === 43){
      that.ttl = "采购的预付款"
    }
    that.invoiceID = paramCode.substring(2, paramCode.length);
    console.log('这是id',that.invoiceID);

    //预付款id
    let unid = localStorage.getItem('yfk');
    console.log('预付款id',unid);
    that.invoiceID = unid;

    //订阅部分
    this.listenersUid = [
      //待付款审批
      this.sphdSocket.subscribe('advancePaymentPayHandle',function(data){
        let gotdata = JSON.parse(data);
        console.log("待付款审批:");
        console.log(gotdata);
        let one = Number(gotdata.operate);
        if(one < 0){
          that.cashierPaymentHandle.unshift(gotdata);  //cashierPaymentHandle
        }else if(one < 0){
          let id = getData.poOrdersPrepayment.id;
          let _index = -1;
          that.cashierPaymentHandle.forEach(function (item, index) { //cashierPaymentHandle
            if (item.poOrdersPrepayment.id === id) {
              _index = index;
            }
          })
          if (_index > -1) {
            that.cashierPaymentHandle.splice(_index, 1); //cashierPaymentHandle
          }
        }
      },function(){console.log('Socket check Error:')},'user'),

      //已批准
      this.sphdSocket.subscribe('advancePaymentPayApproved',function(data){
        let gotdata2 = JSON.parse(data);
        console.log("待付款审批:");
        console.log(gotdata2);
        let one = Number(gotdata2.operate);
        if(one < 0){
          that.cashierPaymentHandle.unshift(gotdata2); //cashierPaymentHandle
        }else if(one < 0){
          let id = getData.poOrdersPrepayment.id;
          let _index = -1;
          that.cashierPaymentHandle.forEach(function (item, index) { //cashierPaymentHandle
            if (item.poOrdersPrepayment.id === id) {
              _index = index;
            }
          })
          if (_index > -1) {
            that.cashierPaymentHandle.splice(_index, 1); //cashierPaymentHandle
          }
        }
      },function(){console.log('Socket check Error:')},'user')
    ]
    that.getDetail();
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item);
    })
  },
  methods:{
    strSub: function () {
      let reasonLen = this.reason.length
      if (reasonLen > 40) {
        this.reason = this.reason.substr(0, 40)
      }
    },
    getDetail:function(){
      let that = this;
      console.log("invoiceID", that.invoiceID);
      this.axios.post('../../../purchaseInvoice/advancePaymentDetail.do', { "orderPrepaymentId": that.invoiceID } )
        .then(function (response) {
          let res = response.data.data;
          console.log('数据：', res);
          that.details = res;
          console.log('盒子尝试',that.details.poOrders);
          if(that.details.approvalProcessList[0]){
            console.log('9.23',that.details.approvalProcessList[0]);
            console.log('9.27',that.details.approvalProcessList[0].id);
          }

          that.loading = false;

          // let poOrders = res.poOrders
          // let rate1 = '- -'
          // let rate2 = '- -'
          // poOrders.invoicedAmount = poOrders.invoicedAmount || 0
          // poOrders.payedAmount = poOrders.payedAmount || 0
          // let rate3 = (poOrders.invoicedAmount / poOrders.amount * 100).toFixed(2)
          // let rate4 = (poOrders.payedAmount / poOrders.amount * 100).toFixed(2)
          // that.details.orderProgress = [
          //   { 'name': '检验已合格', 'amount': `${  poOrders.checkedAmount ? poOrders.checkedAmount.toFixed(2) :"" }` , 'rate': rate1 },
          //   { 'name': '已入库', 'amount': `${  poOrders.storedAmount ? poOrders.storedAmount.toFixed(2) :"" }` , 'rate': rate2 },
          //   { 'name': '票据已提交', 'amount': poOrders.invoicedAmount.toFixed(2) , 'rate': rate3 + '%'},
          //   { 'name': '已付款', 'amount': poOrders.payedAmount.toFixed(2) , 'rate': rate4 + '%'}
          // ]
          // that.loading = false
          // let ap = res.approvalProcessList[0] || []
          // if (!lastToUser || lastToUser === that.sphdSocket.uuid()) {
          //   that.isNowApprover = true
          // }
        })
        .catch(function (error) {
          console.log(error);
        })
    },
    search: function () {
      this.$router.push({
        path: `/applyReimburseQuery`
      })
    },
    goBillInfo: function (item) {
      let params = {
        'applicationId': this.$route.params.id,
        'name': 'purchaseInvoice',
        'type': '1',
        'num': item.num,
        'totalAmount': item.totalBillAmount.toFixed(2),
        'billCat': item.invoiceCategory
      }
      this.$store.dispatch('setBillParam', params)
      this.$router.push({
        path: 'bill',
        name: 'bill'
      })
    },
    approve: function (state) {
      let that = this
      if (state === 3) {
        this.$prompt('驳回理由', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(({ value }) => {
          let reason = value || ''
          if(reason.length === 0){
            that.$kiko_message('请录入驳回理由')
            return false
          }
          that.loading = true
          that.approveOk(3, reason)
        })
      } else {
        this. dialogVisible = true
      }
    },
    approveOk: function (state, reason) {
      let that = this;
      let approveInfo = {
        approvalProcessId: that.details.approvalProcessList[0].id,
        approveStatus: state,
        reason: reason
      }
      console.log('传参：', approveInfo);
      that.dialogVisible = false;
      this.axios.post('../../../purchaseInvoice/purchaseOnlineApproval.do', approveInfo )
        .then(function (response) {
          let res = response.data.data;
          let content = res.content;
          console.log('审批返回值：', res);
          that.$kiko_message(content);
          if(content === '操作成功'){
            if(approveInfo.approvalStatus == 1){ // 通过
              localStorage.setItem("navNum", "2");
            }else{ // 驳回
              localStorage.setItem("navNum", "1");
            }
            that.$router.push({
              path: `/purchaseBillApproval`
            })
          }
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    approvePay:function () {
      let that = this;
      let approveInfo = {
        approvalProcessId: this.details.approvalProcessList[0].id,
        approveStatus: this.approveSta,
      }
      if (Number(approveInfo.approveStatus) > 0) {
      } else {
        that.$kiko_message('请先选择审批结果');
        return false;
      }
      approveInfo['reason'] = this.reason;
      approveInfo['approveSelect'] = this.approveSelect.toString();
      if (approveInfo.approveSelect.length === 0 && (approveInfo.approveStatus) === '3') {
        that.$kiko_message('请先选择在线审批未通过的原因');
        return false;
      }
      if ((approveInfo.approveStatus) === '3'&& approveInfo.approveSelect === '5'
        && approveInfo.reason.length ===0 ) {
        that.$kiko_message('请输入未通过的原因')
        return false
      }
      console.log(approveInfo);
      that.loading = true;
      this.axios.post('../../../purchaseInvoice/purchasePaymentApproval.do', approveInfo )
        .then(function (response) {
          console.log('详情：', response);
          let res = response.data.data;
          that.$kiko_message(res.content);
          that.loading =false;
          if(res.content === '操作成功'){
            if(that.approveSta == 2){ // 批准
              localStorage.setItem("navNum", "2");
            }else{ // 驳回
              localStorage.setItem("navNum", "1");
            }
            that.$router.push({
              path: `/purchasePaymentApproval`
            })
          }
        })
        .catch(function (error) {
          console.log(error);
        })
    },
    approvePayment:function () {
      let that = this;
      let approveInfo = {
        approvalProcessId: that.details.approvalProcessList[0].id  //目前id获取不到
      }
      // if (Number(this.approveStaPayment) > 0) {
      // } else {
      //   that.$kiko_message('请先选择审批结果')
      //   return false
      // }
      console.log(approveInfo);
      that.loading = true;
      this.axios.post('../../../purchaseInvoice/advancePaymentPayApproved.do', approveInfo )
        .then(function (response) {
          let res = response.data.data;
          console.log('付款审批者返回值：', res.state);
          console.log('对state的描述',res.content);
          //     that.$kiko_message(res.content)
          //     that.loading =false;
          if(res.state === 1){//state的值是数字，content的值才是描述
            console.log('走这里了么?',res.state);  //(10.13查看有值)
            localStorage.setItem("navNum", "2");
            this.$router.push({  //若成功则跳转到哪个页面
              path: `/paymentAdvanceApproval/2`   //这个位置现在跳转有问题，不是同一个页面
            })
          }
        })
        .catch(function (error) {
          console.log(error);
        })
    },
    orderProgressRade(){
      this.$router.push({
        path: `/purchaseInvoiceMoreadd`
      })
    }
  },
  computed:{
    isture: function () {
      let one = Number(this.details.poOrdersPrepayment.planMethod)
      let str = ''
      switch (one){
        case 1: str = '现金'; break;
        case 3: str = '转账支票'; break;
        case 4: str = '承兑汇票'; break;
        case 5: str = '银行转账'; break;
        case 6: str = '非公户银行转账'; break;
        default: str = `未识别-${ one }`
      }
      return str
    }
  }
}
</script>

<style lang="less">
.processItem{
  display: flex;
  font-size: 11px;
  margin-left:-5px;
  div:nth-child(1){ text-align: left; flex: 1 }
  div:nth-child(2){ text-align: center; flex: 1 }
  div:nth-child(3) { text-align: right; flex: 2 }
}
.rejectPng{
  position: absolute;
  top: calc( 50% - 60px);
  left: calc( 50% - 60px);
  z-index: 50;
  filter:alpha(opacity=60);
  opacity:0.6;
  -moz-opacity:0.6;
  -khtml-opacity: 0.6
}
.right{ float: right;  }
.ui-cells_none{ display: block;  }
.con1{ background:#fff; margin-bottom:10px; padding:5px 15px; font-size:14px;  }
.con1>p{text-align: right; font-size:0.8em;   }
.oInfo{ padding:5px 20px; }
.txt_right{ text-align: right;  }
</style>
