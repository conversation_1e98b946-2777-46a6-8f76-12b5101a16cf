package cn.sphd.miners.modules.personal.service;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.dailyAffairs.entity.CommonInvoiceCertification;
import cn.sphd.miners.modules.finance.entity.FinanceReimburseBill;
import cn.sphd.miners.modules.finance.entity.FinanceReimburseBillAttachment;
import cn.sphd.miners.modules.personal.entity.*;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.BadgeNumberCallback;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/*
**
*<AUTHOR>
*@Date 2016/12/7 16:41
*/
public interface PersonnelReimburseService extends BadgeNumberCallback{
    void updatePersonnelReimburse(PersonnelReimburse personnelReimburse);

    PersonnelReimburse personnelReimburseById(Integer id);

    List<PersonnelReimburse> getPersonnelReimburseByUser(Integer id, String approveStatus);

    void addPersonnelReimburseHistory(PersonnelReimburseHistory prh);

    void addPersonnelReimburseAttachmentHistory(PersonnelReimbursetAttachmentHistory prah);

    PersonnelReimburseHistory getPersonnelReimburseHistoryById(Integer id);

    void deletePersonnelReimburseAttachment(PersonnelReimbursetAttachment personnelReimburseAttachment);

    List<PersonnelReimburse> getMyPersonnelReimburseList(Integer userId, Integer approveStatus, PageInfo pageInfo);

    boolean approvalReimburse(User user,Integer approvalProcessId, Integer approvalStatus,String approveMemo);//逐级审批报销申请接口

    //  -----以下为1.59报销之会计关联方法
    String reimburseApply(Integer userId, String reimburse, String commonBills,Integer factUser) throws ParseException;

    Map<String,Object> getReimburseDetail(Integer reimburseId);

    Map<String,Object> cancel(Integer reimburseId,Map<String,Object> map);

    List<PersonnelReimburse> getPersonnelReimburseByApprover(Integer userId,String approvalStatus);  //根据审批人id查询报销列表

    boolean approvalReimburse1(Integer userId,Integer approvalProcessId, String approvalStatus,String reason);//逐级审批报销申请接口

    List<PersonnelReimburse> cashier(Integer userId,Integer oid,String approvalStatus);  //财务待处理

    List<Map<String,Object>> cashierAuthentication(Integer oid);  //财务出纳待认证

    Map<String,Object> cashierTwoApproval(Integer userId,Integer approvalProcessId, Date factDate,String summary);

    Map<String,Object> getReimburseBillDetail(Integer reimburseId,Integer billCat,Integer feeCat,Integer secondFeeCat,Map<String,Object> map);

    List<FinanceReimburseBill> authInvoiceDetail(Integer reimburseId, Integer billCat,String certificationState);

    Map<String,Object> authBillItemDetail(Integer billId,Map<String,Object> map);

    Map<String,Object> authApproval(String approvalStatus,String bills);

    List<FinanceReimburseBill> getReimburseBillByReimburseId(Integer reimburseId,Integer primaryBill);

    Integer getPersonnelReimburseNum(Integer userId, String approvalStatus,Integer oid,String mid);  //报销审批/出纳-待处理、待两讫和的角标

    Integer getPersonnelReimburseAuthNum(Integer oid);  //出纳-待认证的角标

    void reimburseRejectSend(int loginNum,int operate,PersonnelReimburse personnelReimburse,Integer toUserId,String pass, String title, String content, String code);

    Map<String,Object> getReimburseInfo(Integer reimburseId);

    //报销最后如财务帐的接口（1.103整理的）
    Map<String,Object> reimbursementEntry(PersonnelReimburse personnelReimburse,User user,String payMethod,String summary,Date factDate,Integer financeAccountId,ApprovalProcess approvalProcess,Integer type);

    List<FinanceReimburseBill> getAuthenticationQuery(Integer oid,Date beginDate,Date endDate,String certificationState);

    Map<String,Object> onlineAuditApproval(Integer userId,Integer approvalProcessId,String approveStatus,String reason,String approveSelect);  //待在线审核的审批

    Map<String,Object> offlineAuditApproval(Integer userId,Integer approvalProcessId,String approveStatus);

    List<PersonnelReimburse> verificationBillsQuery(Integer userId,Date begin,Date end,String approveStatus,Integer applyId);

    List<PersonnelReimburse> getOnlineAuditOK(Integer oid,String approveStatus);

    List<FinanceReimburseBillAttachment> getFinanceReimburseBillAttachmentByBillId(Integer billId);

    FinanceReimburseBillAttachment getFinanceReimburseBillAttachmentByBill(Integer billId);

    CommonInvoiceCertification getCertificationByCode(String qrINfo);

    CommonInvoiceCertification addInvoiceCertification(String qrINfo,Map<String,Object> jsonResult);

    String pushContent(PersonnelReimburse personnelReimburse,Integer type);
}
