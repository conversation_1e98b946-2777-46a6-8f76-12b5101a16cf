<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.resourceAuthority.mapper.ResCategoryAclMapper">

    <select id="getSingle" parameterType="resCatAcl" resultType="resCatAcl" >
        select
        id, category, user, view, operate, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName,
        update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
        audit_date AS auditDate, operation, apply_memo AS applyMemo, apply_memo AS applyMemo, message_id AS messageId
        from t_resource_category_acl
        where id = #{id}
    </select>

    <select id="listPage" parameterType="hashmap" resultType="resCatAcl" >
        select
        id, category, user, view, operate, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName,
        update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
        audit_date AS auditDate, operation, apply_memo AS applyMemo, apply_memo AS applyMemo, message_id AS messageId
        from t_resource_category_acl
        where id = #{id}
    </select>

    <select id="getAllAuthByCategory" resultType="resCatAcl">
        select
        id, category, user, view, operate, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName,
        update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
        audit_date AS auditDate, operation, apply_memo AS applyMemo, apply_memo AS applyMemo, message_id AS messageId
        from t_resource_category_acl
        where category = #{categotyId}
    </select>

    <select id="count" resultType="Integer">
        select count(*)
        from t_resource_category_acl
        where category = #{category} and user = #{user}
    </select>

    <select id="countAuth" resultType="Integer">
        select count(id)
        from t_resource_category_acl
        where category = #{categotyId}
    </select>

    <select id="getAllUserIdsByCategory" resultType="Integer">
        select user
        from t_resource_category_acl
        where category = #{categotyId}
    </select>


    <delete id="delete" parameterType="resCatAcl" >
        delete from t_resource_category_acl
        where id = #{id}
    </delete>

    <delete id="delAuthByCategory">
        delete from t_resource_category_acl
        where category = #{categoryId} and creator != 0
    </delete>

    <delete id="delAuthByUserAndCategory">
        delete from t_resource_category_acl
        where category = #{category} and user = #{user}
    </delete>

    <delete id="delAllAuthByCategory">
        delete from t_resource_category_acl
        where category = #{categoryId}
    </delete>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="resCatAcl" >
        insert into t_resource_category_acl (category, user,
        view, operate, creator,
        create_name, create_date, updator,
        update_name, update_date, approve_Item,
        approve_status, approve_level, auditor,
        auditor_name, audit_date, operation,
        apply_memo, approve_memo, message_id)
        values (#{category}, #{user}, #{view}, #{operate}, #{creator}, #{createName}, #{createDate},
        #{updator}, #{updateName}, #{updateDate}, #{approveItem}, #{approveStatus}, #{approveLevel},
        #{auditor}, #{auditorName}, #{auditDate}, #{operation}, #{applyMemo}, #{approveMemo}, #{messageId})
    </insert>

    <update id="update" parameterType="resCatAcl" >
        update t_resource_category_acl
        <set >
            <if test="category != null" >
                category = #{category},
            </if>
            <if test="user != null" >
                user = #{user},
            </if>
            <if test="view != null" >
                view = #{view},
            </if>
            <if test="operate != null" >
                operate = #{operate},
            </if>
            <if test="creator != null" >
                creator = #{creator},
            </if>
            <if test="createName != null" >
                create_name = #{createName},
            </if>
            <if test="createDate != null" >
                create_date = #{createDate},
            </if>
            <if test="updator != null" >
                updator = #{updator},
            </if>
            <if test="updateName != null" >
                update_name = #{updateName},
            </if>
            <if test="updateDate != null" >
                update_date = #{updateDate},
            </if>
            <if test="approveItem != null" >
                approve_Item = #{approveItem},
            </if>
            <if test="approveStatus != null" >
                approve_status = #{approveStatus},
            </if>
            <if test="approveLevel != null" >
                approve_level = #{approveLevel},
            </if>
            <if test="auditor != null" >
                auditor = #{auditor},
            </if>
            <if test="auditorName != null" >
                auditor_name = #{auditorName},
            </if>
            <if test="auditDate != null" >
                audit_date = #{auditDate},
            </if>
            <if test="operation != null" >
                operation = #{operation},
            </if>
            <if test="applyMemo != null" >
                apply_memo = #{applyMemo},
            </if>
            <if test="approveMemo != null" >
                approve_memo = #{approveMemo},
            </if>
            <if test="messageId != null" >
                message_id = #{messageId},
            </if>
        </set>
        where id = #{id}
    </update>

</mapper>