package cn.sphd.miners.modules.generalAffairs.entity;

import cn.sphd.miners.modules.system.entity.Organization;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by Administrator on 2017/7/11.
 * 固定资产表
 */
@Entity
@Table(name="t_finance_fixed_assets")
public class FinanceFixedAssets implements Serializable {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="sn_prefix"  , length=10 , nullable=true , unique=false)
    private String snPrefix;


    @Column(name="sn"  , length=10 , nullable=true , unique=false)
    private String sn;

    @Column(name="sn_name"  , length=255 , nullable=true , unique=false)
    private String snName;//固定资产序号

    @Column(name="use_state"  , length=1 , nullable=true , unique=false)
    private String useState;

    @Column(name="receive_state"  , length=1 , nullable=true , unique=false)
    private String receiveState;

    @Column(name="name"  , length=100 , nullable=true , unique=false)
    private String name;

    @Column(name="mode_number"  , length=50 , nullable=true , unique=false)
    private String modeNumber;

    @Column(name="buyer"   , nullable=true , unique=false)
    private Integer buyer;

    @Column(name="buyer_name"  , length=100 , nullable=true , unique=false)
    private String buyerName;

    @Column(name="buy_date"   , nullable=true , unique=false)
    private Date buyDate;

    @Column(name="storage_date"   , nullable=true , unique=false)
    private Date storageDate;

    @Column(name="original_value"   , nullable=true , unique=false)
    private BigDecimal originalValue;

    @Column(name="retirement_date"   , nullable=true , unique=false)
    private Date retirementDate;

    @Column(name="summary"  , length=255 , nullable=true , unique=false)
    private String summary;

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;

    @Column(name="reason"  , length=255 , nullable=true , unique=false)
    private String reason;//报废原因

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="approve_Item"   , nullable=true , unique=false)
    private Integer approveItem;

    @Column(name="approve_status"  , length=1 , nullable=true , unique=false)
    private String approveStatus;

    @Column(name="approve_level"   , nullable=true , unique=false)
    private Integer approveLevel;

    @Column(name="auditor"   , nullable=true , unique=false)
    private Integer auditor;

    @Column(name="auditor_name"  , length=100 , nullable=true , unique=false)
    private String auditorName;

    @Column(name="audit_date"   , nullable=true , unique=false)
    private Date auditDate;

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;

    @Column(name="apply_memo"  , length=255 , nullable=true , unique=false)
    private String applyMemo;

    @Column(name="approve_memo"  , length=255 , nullable=true , unique=false)
    private String approveMemo;

    @Column(name="message_id"   , nullable=true , unique=false)
    private Integer messageId;


    @JsonIgnore
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="org", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private Organization org;

    @Column(name="org"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer org_;

    @OneToMany(targetEntity=FinanceAssetsMaintain.class, fetch= FetchType.LAZY, mappedBy="assets", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<FinanceAssetsMaintain> financeAssetsMaintainHashSet = new HashSet<FinanceAssetsMaintain>();

    @OneToMany(targetEntity=FinanceAssetsReceive.class, fetch= FetchType.LAZY, mappedBy="assets", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set <FinanceAssetsReceive> financeAssetsReceiveHashSet = new HashSet<FinanceAssetsReceive>();

    @Transient
    private String receiverName;//领用者
    @Transient
    private Date receiveDate;//领用日期
    @Transient
    private Integer farId;//领用归还表ID
    @Transient
    private Integer lyghRecord;//领用、归还记录
    @Transient
    private Integer wxRecord;//维修记录

    public Integer getLyghRecord() {
        return lyghRecord;
    }

    public void setLyghRecord(Integer lyghRecord) {
        this.lyghRecord = lyghRecord;
    }

    public Integer getWxRecord() {
        return wxRecord;
    }

    public void setWxRecord(Integer wxRecord) {
        this.wxRecord = wxRecord;
    }

    public Integer getFarId() {
        return farId;
    }

    public void setFarId(Integer farId) {
        this.farId = farId;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public Date getReceiveDate() {
        return receiveDate;
    }

    public void setReceiveDate(Date receiveDate) {
        this.receiveDate = receiveDate;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getSnName() {
        return snName;
    }

    public void setSnName(String snName) {
        this.snName = snName;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getSnPrefix() {
        return snPrefix;
    }

    public void setSnPrefix(String snPrefix) {
        this.snPrefix = snPrefix;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getUseState() {
        return useState;
    }

    public void setUseState(String useState) {
        this.useState = useState;
    }

    public String getReceiveState() {
        return receiveState;
    }

    public void setReceiveState(String receiveState) {
        this.receiveState = receiveState;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getModeNumber() {
        return modeNumber;
    }

    public void setModeNumber(String modeNumber) {
        this.modeNumber = modeNumber;
    }

    public Integer getBuyer() {
        return buyer;
    }

    public void setBuyer(Integer buyer) {
        this.buyer = buyer;
    }

    public String getBuyerName() {
        return buyerName;
    }

    public void setBuyerName(String buyerName) {
        this.buyerName = buyerName;
    }

    public Date getBuyDate() {
        return buyDate;
    }

    public void setBuyDate(Date buyDate) {
        this.buyDate = buyDate;
    }

    public Date getStorageDate() {
        return storageDate;
    }

    public void setStorageDate(Date storageDate) {
        this.storageDate = storageDate;
    }

    public BigDecimal getOriginalValue() {
        return originalValue;
    }

    public void setOriginalValue(BigDecimal originalValue) {
        this.originalValue = originalValue;
    }

    public Date getRetirementDate() {
        return retirementDate;
    }

    public void setRetirementDate(Date retirementDate) {
        this.retirementDate = retirementDate;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    public Organization getOrg() {
        return org;
    }

    public void setOrg(Organization org) {
        this.org = org;
    }

    public Integer getOrg_() {
        return org_;
    }

    public void setOrg_(Integer org_) {
        this.org_ = org_;
    }

    public Set<FinanceAssetsMaintain> getFinanceAssetsMaintainHashSet() {
        return financeAssetsMaintainHashSet;
    }

    public void setFinanceAssetsMaintainHashSet(Set<FinanceAssetsMaintain> financeAssetsMaintainHashSet) {
        this.financeAssetsMaintainHashSet = financeAssetsMaintainHashSet;
    }

    public Set<FinanceAssetsReceive> getFinanceAssetsReceiveHashSet() {
        return financeAssetsReceiveHashSet;
    }

    public void setFinanceAssetsReceiveHashSet(Set<FinanceAssetsReceive> financeAssetsReceiveHashSet) {
        this.financeAssetsReceiveHashSet = financeAssetsReceiveHashSet;
    }
}
