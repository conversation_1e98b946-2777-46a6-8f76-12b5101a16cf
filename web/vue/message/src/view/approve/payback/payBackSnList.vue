<template>
  <div id="payList">
    <TY_NavTop title="回款处置" :isBackHome="isBackHome"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="tipPane" v-if="(ssMethod === '3' || ssMethod === '4') && ssUser === ''">{{ssDate}}共收到{{ssMethod | incomeType}}{{ssCount}}次，金额共{{ssAmount}}元</div>
      <div class="tipPane" v-if="(ssMethod === '3' || ssMethod === '4') && ssUser !== ''">{{ssDate}}共收到{{ssUser}}经手的{{ssMethod | incomeType}}{{ssCount}}次，金额共{{ssAmount}}元</div>
      <div class="tipPane" v-if="(ssMethod === '1' || ssMethod === '5' || ssMethod === '') && ssUser !== ''">{{ssDate}}共收到{{ssUser}}经手的回款{{ssCount}}次，金额共{{ssAmount}}元</div>
      <div class="tipPane" v-if="(ssMethod === '1' || ssMethod === '5' || ssMethod === '') && ssUser === ''">{{ssDate}}共收到回款{{ssCount}}次，金额共{{ssAmount}}元</div>
      <div class="ui-cells">
        <a class="ui-cell searchPane" v-on:click="applyTo(item.id)" v-for="(item, index) in screenList" :key="index">
          <div class="backApplyLists">
            <p class="company">{{item.customerName}}</p>
            <p class="listCon"> 本次回款 {{ item.method | incomeType}} {{item.amount}}元</p>
            <p class="listCon">{{item.createName}} {{item.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</p>
          </div>
        </a>
      </div>
    </div>
  </div>
</template>
<script>
import { incomeType, formatDate } from '../../../js/common'
export default {
  name: 'collectShList',
  filters: {
    incomeType,
    formatDate
  },
  data () {
    return {
      ssUser: '',
      ssDate: '',
      ssCount: 0,
      ssAmount: 0,
      ssMethod: '',
      loading: true,
      screenList: []
    }
  },
  created () {
    let json = localStorage.getItem('collectParams')
    json = JSON.parse(json)
    this.ssMethod = json.method
    this.ssUser = json.applyUserName
    this.ssDate = json.applyDate ? json.applyDate : json.approvalDate
    let ssArr = this.ssDate.split('-')
    this.ssDate = ssArr[0] + '年' + ssArr[1] + '月'
    let resJson = json
    delete resJson.applyUserName
    this.$http.post('../../../salesBack/collectQueries.do', resJson, {
      emulateJSON: true
    }).then((response) => {
      let result = response.body.list
      let temp = 0
      this.screenList = result
      this.ssCount = result.length
      for (let t in result) {
        temp += Number(result[t].amount)
      }
      this.ssAmount = temp.toFixed(2)
      this.loading = false
    }).catch(function () {
      this.$message({
        type: 'error',
        message: '系统错误，请重试！'
      })
    })
  },
  methods: {
    applyTo: function (id) {
      this.$router.push({
        path: `/collectShDetail/${id}`
      })
    }
  }
}
</script>
