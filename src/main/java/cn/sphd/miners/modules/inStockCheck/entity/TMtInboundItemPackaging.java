package cn.sphd.miners.modules.inStockCheck.entity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 材料_入库申请单明细包装表20241015 1.316入库 新增
 */
public class TMtInboundItemPackaging {
    /**
    * ID
    */
    private Integer id;

    /**
    * 机构ID
    */
    private Integer org;

    /**
    * 入库申请明细ID
    */
    private Integer inApplicationItem;

    /**
    * 材料ID
    */
    private Integer material;

    /**
    * 包装ID
    */
    private Long packaging;

    /**
    * 数量
    */
    private BigDecimal quantity;

    /**
    * 备注
    */
    private String renark;

    /**
    * 操作:1-增,2-删,3-修改
    */
    private Byte operation;

    /**
    * 创建人id,实际为采购入库人
    */
    private Integer creator;

    /**
    * 创建人
    */
    private String createName;

    /**
    * 创建时间
    */
    private Date createDate;

    /**
    * 修改人id
    */
    private Integer updator;

    /**
    * 修改人
    */
    private String updateName;

    /**
    * 修改时间
    */
    private Date updateDate;

    /**
    * 修改前记录ID
    */
    private Integer previousId;

    /**
    * 版本号,每次修改+1
    */
    private Integer versionNo;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getInApplicationItem() {
        return inApplicationItem;
    }

    public void setInApplicationItem(Integer inApplicationItem) {
        this.inApplicationItem = inApplicationItem;
    }

    public Integer getMaterial() {
        return material;
    }

    public void setMaterial(Integer material) {
        this.material = material;
    }

    public Long getPackaging() {
        return packaging;
    }

    public void setPackaging(Long packaging) {
        this.packaging = packaging;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public String getRenark() {
        return renark;
    }

    public void setRenark(String renark) {
        this.renark = renark;
    }

    public Byte getOperation() {
        return operation;
    }

    public void setOperation(Byte operation) {
        this.operation = operation;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
}