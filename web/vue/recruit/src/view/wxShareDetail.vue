<template id="wxShare">
  <div class="wxShareDetail">
    <div v-show="page === 0">
      <van-empty :description="errorMsg" />
    </div>
    <div v-show="page === 1">
      <van-nav-bar
        title="招聘启事"
        left-arrow
        @click-left="onClickLeft"
      />
      <van-overlay :show="loading">
        <div class="wrapper">
          <van-loading />
        </div>
      </van-overlay>
      <div class="main_container">
        <van-cell-group>
          <van-cell title="岗位名称：" :value="postData.postName" />
          <van-cell title="招聘人数：" :value="postData.recruitingNumbers + '人'"/>
          <van-cell title="岗位职责：" v-if="postData.duty.length > 0">
            <template #label>
              <p v-for="(item, index) in postData.duty" v-bind:key="index">{{index + 1 + '、' + item}}</p>
            </template>
          </van-cell>
          <van-cell title="岗位要求：" v-if="postData.requirement.length > 0">
            <template #label>
              <p v-for="(item, index) in postData.requirement" v-bind:key="index">{{index + 1 + '、' + item}}</p>
            </template>
          </van-cell>
          <van-cell title="综合：" v-if="postData.synthesis.length > 0">
            <template #label>
              <p v-for="(item, index) in postData.synthesis" v-bind:key="index">{{index + 1 + '、' + item}}</p>
            </template>
          </van-cell>
          <van-cell title="其他条件与要求：" v-if="postData.otherAsk.length > 0">
            <template #label>
              <p v-for="(item, index) in postData.otherAsk" v-bind:key="index">{{index + 1 + '、' + item}}</p>
            </template>
          </van-cell>
          <van-cell title="薪资待遇：" :value="postData.salary" v-if="postData.salaryShow"/>
          <van-cell title="作息时间：" :value="postData.beginEndTime" v-if="postData.workRestShow"/>
          <van-cell title="工作地点：" :value="postData.address"  v-if="postData.addressShow"/>
          <van-cell :title="item.itemName + '：'" v-for="(item, index) in postData.custom" :value="item.content" v-bind:key="index"/>
        </van-cell-group>
      </div>
      <div class="footerFixed">
        <van-row gutter="8">
          <van-button type="primary" block @click="application">应聘此岗位，填写简历</van-button>
        </van-row>
      </div>
    </div>
  </div>
</template>

<style scoped>
body{
  padding:0;
  margin: 0;
}
.van-cell__label p{
  font-size: 14px;
  line-height: 16px;
}
.main_container {
  height: calc(100vh - 143px);
}
.footerFixed{
  position: fixed;
  bottom: 0;
  padding: 16px 8px;
  background: #f0f0f0;
  border-top: 1px solid #eee;
  width: calc(100% - 16px);
}
.wrapper{
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
</style>

<script>
// wyu：引相关的库
export default {
  name: 'wxShareDetail',
  data () {
    return {
      loading: true,
      rdPost: {},
      rdNoticePost: [],
      rdNoticeItemList: [],
      postData: {
        postName: '',
        recruitingNumbers: 0,
        duty: [],
        requirement: [],
        synthesis: [],
        otherAskShow: false,
        otherAsk: [],
        workRestShow: false,
        beginEndTime: '',
        addressShow: false,
        address: '',
        salaryShow: false,
        salary: 0
      },
      page: 1,
      errorMsg: ''
    }
  },
  created () {
    let postId = this.$route.params.id
    let that = this
    this.$http.get(window.parent.$.webRoot + '/resume/getRdNoticePostInfo.do?id=' + postId).then(res => {
      let data = res.body.data
      let error = res.body.error
      if (error) {
        that.loading = false
        this.page = 0
        this.errorMsg = error.message
        return false
      } else {
        this.page = 1
      }
      let rdPost = data.rdPost
      let rdNoticeItemList = data.rdNoticeItemList

      let duty = that.getPostInfoStr(rdPost.duty)
      let requirement = that.getPostInfoStr(rdPost.requirement)
      let synthesis = that.getPostInfoStr(rdPost.synthesis)
      let workRestShow = that.getShow(rdNoticeItemList, 'workRest')
      let addressShow = that.getShow(rdNoticeItemList, 'address')
      let salaryShow = that.getShow(rdNoticeItemList, 'salary')
      let otherAskShow = that.getShow(rdNoticeItemList, 'otherAskShow')
      let beginTime = that.getContent(rdNoticeItemList, 'workRest', 'beginTime')
      let endTime = that.getContent(rdNoticeItemList, 'workRest', 'endTime')
      let address = that.getContent(rdNoticeItemList, 'address', 'address')
      let salary = that.getContent(rdNoticeItemList, 'salary')
      let otherAsk = that.getContent(rdNoticeItemList, 'otherAsk')
      let custom = that.getContent(rdNoticeItemList, 'custom')
      that.postData = {
        postName: that.getContent(rdNoticeItemList, 'postName', 'postName'),
        recruitingNumbers: that.getContent(rdNoticeItemList, 'recruitingNumbers'),
        duty: duty,
        requirement: requirement,
        synthesis: synthesis,
        otherAskShow: otherAskShow && otherAsk,
        otherAsk: otherAsk,
        workRestShow: workRestShow && beginTime,
        beginEndTime: beginTime + ' - ' + endTime,
        addressShow: addressShow && address,
        address: address,
        salaryShow: salaryShow && salary,
        salary: salary,
        custom: custom
      }
      that.loading = false
      console.log('postData', that.postData)
    }).catch(err => {
      console.log(err)
    })
  },
  methods: {
    onClickLeft () {
      this.$router.go(-1)
    },
    getContent (data, code, nextCode) {
      if (code === 'custom') {
        let customData = []
        data.forEach(item => {
          if (item.itemCode === 'custom' && item.show && item.content) {
            customData.push({
              itemName: item.itemName,
              content: item.content
            })
          }
        })
        return customData
      } else {
        let index = data.findIndex(it => it.itemCode === code)
        if (index !== -1) {
          if (nextCode) {
            if (data[index].content) {
              var content = JSON.parse(data[index].content)
              return content[nextCode]
            } else {
              return ''
            }
          } else {
            if (code === 'otherAsk') {
              let content = data[index].content ? JSON.parse(data[index].content) : {}
              let itemArr = []
              if (content.age) {
                let ageThresholdStr = content.ageThreshold ? (content.ageThreshold === '1' ? '或以上' : '或以下') : ''
                itemArr.push('要求年龄在' + content.age + '周岁' + ageThresholdStr)
              }
              if (content.gender) {
                itemArr.push('要求性别为' + (content.gender === '1' ? '女' : '男'))
              }
              if (content.education) {
                const eduArr = ['硕士', '本科', '大专', '高中或中专']
                let educationThresholdStr = content.educationThreshold ? (content.educationThreshold === '1' ? '或以上' : '或以下') : ''
                itemArr.push('要求学历在' + eduArr[content.education - 1] + educationThresholdStr)
              }
              if (content.residence) {
                itemArr.push('要求户籍在' + (content.residence === '1' ? '本地户籍' : '非本地户籍'))
              }
              if (content.experiences) {
                itemArr.push('要求工作经验为' + content.experiences)
              }
              if (content.characters) {
                itemArr.push('要求性格为' + content.characters)
              }
              return itemArr
            } else {
              return data[index].content
            }
          }
        } else {
          return ''
        }
      }
    },
    getShow (data, code) {
      let index = data.findIndex(it => it.itemCode === code)
      if (index !== -1) {
        return data[index].show
      } else {
        return false
      }
    },
    getPostInfoStr (str) {
      if (str) {
        return JSON.parse(str) || []
      } else {
        return []
      }
    },
    application () {
      let recruit = this.$store.getters.getRecruit
      let wxrecord = localStorage.getItem('wxrecord')
      if (wxrecord) {
        let wx = JSON.parse(wxrecord)
        let hasInput = wx.data.findIndex(item => item.pid === recruit.postId && item.nid === recruit.noticeId)
        if (hasInput !== -1) {
          // 启事和岗位和之前录入的相同，进入简历提交记录页面
          this.$router.push({path: `/resumeRecord`})
        } else {
          this.$router.push({name: 'home'})
        }
      } else {
        this.$router.push({name: 'home'})
      }
    }
  }
}
</script>
