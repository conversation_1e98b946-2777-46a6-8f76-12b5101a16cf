<template>
  <div id="outMaterialInStock">
    <TY_NavTop title="外购物料入库" isSearch="true" @toggleList="search" :isBackHome=false></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待入库" name="1" closable='false' :msgCount="waitStorageCount">
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id, 1)" v-for="(item, index) in waitStorage" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.code}}</div>
                <div>{{item.name}}</div>
                <div>申请入库的数量  {{item.quantity_plan}}{{item.unit}}  到货日期 {{item.arrive_date | formatDay('YYYY-MM-DD')}}</div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="数量异议待确认" name="2" closable='false'>
        <div class="tip">经清点，发现如下物料数量与申请入库的数量不符</div>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id, 2)" v-for="(item, index) in waitHandle" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.code}}</div>
                <div>{{item.name}}</div>
                <div>申请入库的数量  {{item.quantity_plan}}{{item.unit}}  到货日期 {{item.arrive_date | formatDay('YYYY-MM-DD')}}</div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>

<style>
  #outMaterialInStock .ui-cell{
    font-size: 12px;
  }
</style>

<script>
export default {
  name: 'outMaterialInStock',
  data () {
    return {
      tabValue: '1',
      isBackHome: false,
      listenersUid: [],
      waitStorage: [],
      waitHandle: []
    }
  },
  computed: {
    waitStorageCount: function () {
      return this.waitStorage.length
    }
  },
  created: function () {
    // 从路由获取父级菜单id
    let that = this
    this.axios.post('../../../inStock/mtList', {
      oid: that.sphdSocket.user.oid,
      state: '(3, 6)',
      users: 'xb'
    }).then(function (response) {
      let list = response.data.data
      console.log('list', list)
      if (list) {
        that.waitStorage = list
      }
    })
    this.axios.post('../../../inStock/mtList', {
      oid: that.sphdSocket.user.oid,
      state: '(9)',
      users: 'xb'
    }).then(function (response) {
      let list = response.data.data
      if (list) {
        that.waitHandle = list
      }
    })
  },
  methods: {
    jump: function (id, tab) {
      this.$store.dispatch('setMaterialParam', {
        user: 'xb',
        tab: tab
      })
      this.$router.push({
        path: `/materialDetail/${id}`
      })
    },
    search: function () {
      this.$router.push({
        path: `/purchaseQuery/3`
      })
    }
  }
}
</script>
