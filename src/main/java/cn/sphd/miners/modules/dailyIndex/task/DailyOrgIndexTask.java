package cn.sphd.miners.modules.dailyIndex.task;

import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.dailyIndex.entity.DailyOrg;
import cn.sphd.miners.modules.dailyIndex.service.DailyOrgService;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by HIAPAD on 2022/6/27.
 * 统计 机构昨日人数 登录次数 定时任务
 */
public class DailyOrgIndexTask {

    @Autowired
    DailyOrgService dailyOrgService;
    @Autowired
    DlmService dlmService;


    public void dailyOrgIndexDay(){
        //wyu:获取分布式锁
        String lockKey, methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        if ((lockKey=dlmService.getLock(methodName)) != null) {
            System.out.println("机构统计日任务开始：");
            dailyOrgService.dailyAllOrgIndexDay();
            System.out.println("机构统计日任务结束：");
            //wyu:释放分布式锁
            dlmService.releaseLock(methodName, lockKey);
        }
    }
}
