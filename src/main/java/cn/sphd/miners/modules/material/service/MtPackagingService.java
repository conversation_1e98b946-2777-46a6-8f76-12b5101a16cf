package cn.sphd.miners.modules.material.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.material.entity.MtPackagingInfo;
import cn.sphd.miners.modules.material.entity.MtPackagingInfoHistory;
import cn.sphd.miners.modules.material.entity.MtPackagingStructure;
import cn.sphd.miners.modules.material.entity.MtWrappage;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;
import java.util.Map;

public interface MtPackagingService {


    List<Map<String, Object>> getSupplierByMtBase(Integer mtBaseId,Integer oid);

    List<Map<String, Object>> getPackagingList(Integer oid, String param, int type, PageInfo pageInfo);

    Object getWszNum(Integer oid);

    List<MtWrappage> getWrappageList(Integer oid);

    void deletePackaging(Long id);
    //添加包装
    String addPackaging(List<MtPackagingInfo> list, User user);
    //修改包装
    String updatePackaging(MtPackagingInfo info, List<MtPackagingStructure> pdPackagingStructureList, User user);

    String updatePackagingInfo(MtPackagingInfo info);
    //获取包装详情
    MtPackagingInfo getPackagingDetail(Long id);
    //获取包装详情
    MtPackagingInfo getPackaging(Long id);
    //获取所有包装信息
    List<MtPackagingInfoHistory> getPackagingList(Integer mtBaseId,Integer supplierMaterial);

    //获取操作记录
    List<MtPackagingInfoHistory> getPackagingRecordList(Long id);
    //获取操作记录详情
    MtPackagingInfoHistory getPackagingRecordDetail(Long recordId);

    //获取无需包装
    List<MtPackagingInfo>  getWxPackagingList(Integer mtBaseId,Integer supplierMaterial);
    //新增包装物
    String addBzw(MtWrappage wrappage);
}
