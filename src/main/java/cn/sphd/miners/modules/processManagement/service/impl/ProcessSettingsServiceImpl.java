package cn.sphd.miners.modules.processManagement.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.processManagement.dao.*;
import cn.sphd.miners.modules.processManagement.dto.ProcessesDto;
import cn.sphd.miners.modules.processManagement.dto.SonProcessesDto;
import cn.sphd.miners.modules.processManagement.entity.*;
import cn.sphd.miners.modules.processManagement.service.ProcessSettingsService;
import cn.sphd.miners.modules.system.entity.User;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.jdbc.Null;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * Created by Administrator on 2023/5/10
 * 1.251工序1
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class ProcessSettingsServiceImpl implements ProcessSettingsService {

    @Autowired
    PpmProcessDao ppmProcessDao;
    @Autowired
    PpmActivityDao ppmActivityDao;
    @Autowired
    PpmActivityCategoryDao ppmActivityCategoryDao;
    @Autowired
    PpmActivityCyclicityDao ppmActivityCyclicityDao;
    @Autowired
    PdProductProcessDao pdProductProcessDao;
    @Autowired
    PpmProcessStatDao ppmProcessStatDao;
    @Autowired
    PpmProductionStatDao ppmProductionStatDao;
    @Autowired
    PpmFlowDao ppmFlowDao;

    @Override
    public Integer getProcessNum(Integer org,Boolean enabled) {
        Map<String,Object> map = new HashMap<>();
        String hql = " select count(id) from PpmProcess where org=:org and enabled=:enabled";
        map.put("org",org);
        map.put("enabled",enabled);
        Long num = (Long) ppmProcessDao.getByHQLWithNamedParams(hql,map);
        return num!=null?num.intValue():0;
    }

    @Override
    public Integer getActivityNum(Integer org,Boolean enabled,Byte type) {
        Map<String,Object> map = new HashMap<>();
        String hql = " select count(id) from PpmActivity where org=:org";
        map.put("org",org);
        if (enabled!=null){
            hql+=" and enabled=:enabled";
            map.put("enabled",enabled);
        }
        if (type!=null){

            hql+=" and type=:type";
            map.put("type",type);
        }
        Long num = (Long) ppmActivityDao.getByHQLWithNamedParams(hql,map);
        return num!=null?num.intValue():0;
    }

    //查询是否有相同的代号或名称
    @Override
    public List<PpmProcess> getPpmProcessByRepeatName(Integer org,String code, String name){
        Map<String,Object> map = new HashMap<>();
        String hql = "from PpmProcess where org=:org";
        map.put("org",org);
        if (StringUtils.isNotEmpty(code)||StringUtils.isNotEmpty(name)) {
            if (StringUtils.isNotEmpty(code)&&StringUtils.isEmpty(name)) {
                hql += " and code=:code";
                map.put("code", code);
            }else if (StringUtils.isNotEmpty(name)&&StringUtils.isEmpty(code)) {
                hql += " and name=:name";
                map.put("name", name);
            }else {
                hql += " and (code=:code or name=:name)";
                map.put("code", code);
                map.put("name", name);
            }
        }
        List<PpmProcess> ppmProcessList = ppmProcessDao.getListByHQLWithNamedParams(hql,map);
        return ppmProcessList;
    }

    @Override
    public Map<String, Object> createProcess(User user, PpmProcess ppmProcess, PpmActivityCyclicity ppmActivityCyclicity) {
        Map<String,Object> map = new HashMap<>();
        List<PpmProcess> ppmProcessList = getPpmProcessByRepeatName(user.getOid(),ppmProcess.getCode(),ppmProcess.getName());
        if (ppmProcessList.size()>0){
            map.put("content","代号或名称重复");
        }else {
            ppmProcess.setOrg(user.getOid());
            ppmProcess.setOrders(1);
            ppmProcess.setEnabled(true);
            ppmProcess.setCreator(user.getUserID());
            ppmProcess.setCreateName(user.getUserName());
            ppmProcess.setCreateTime(new Date());
            ppmProcessDao.save(ppmProcess);
            if (ppmProcess.getIsCyclicity() !=null && ppmProcess.getIsCyclicity()) {
                this.addActivityCyclicity(ppmActivityCyclicity,null,ppmProcess,user);
            }
            map.put("content","操作成功");
        }
        return map;
    }

    //查询是否有相同名称的类别
    @Override
    public PpmActivityCategory getPpmActivityCategory(Integer org, String name,Byte type){
        Map<String,Object> map = new HashMap<>();
        String hql = "from PpmActivityCategory where org=:org";
        map.put("org",org);
        if (StringUtils.isNotEmpty(name)){
            hql+=" and name=:name";
            map.put("name",name);
        }
        if (type!=null){
            hql+=" and type=:type";
            map.put("type",type);
        }
        return (PpmActivityCategory) ppmActivityCategoryDao.getByHQLWithNamedParams(hql,map);
    }

    @Override
    public Map<String, Object> createCategory(User user, String name,Integer type) {
        Map<String,Object> map = new HashMap<>();
        Byte typeByte = ActivityCategoryType.process.getIndex();  //默认工序吧
        if (2==type){
            typeByte = ActivityCategoryType.sonProcess.getIndex();
        }else if (3==type){
            typeByte = ActivityCategoryType.operate.getIndex();
        }else if (4==type){
            typeByte = ActivityCategoryType.thail.getIndex();
        }
        PpmActivityCategory ppmActivityCategory1 = getPpmActivityCategory(user.getOid(),name,typeByte);
        if (ppmActivityCategory1!=null){
            map.put("content","名称重复");
        }else {
            PpmActivityCategory ppmActivityCategory = new PpmActivityCategory();
            ppmActivityCategory.setOrg(user.getOid());
            ppmActivityCategory.setName(name);
            ppmActivityCategory.setEnabled(true);
            ppmActivityCategory.setType(typeByte);
            ppmActivityCategory.setCreateName(user.getUserName());
            ppmActivityCategory.setCreator(user.getUserID());
            ppmActivityCategory.setCreateTime(new Date());
            ppmActivityCategoryDao.save(ppmActivityCategory);
            map.put("content","操作成功");
        }
        return map;
    }

    @Override
    public List<PpmActivityCategory> getCategoryList(Integer org, Byte type, Integer enabled) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from PpmActivityCategory where org=:org";
        map.put("org",org);
        if (type!=null){
            hql+=" and type=:type";
            map.put("type",type);
        }
        if (enabled!=null){
            switch (enabled){
                case 0:
                    hql+=" and enabled=false";
                    break;
                case 1:
                    hql+=" and enabled=true";
                break;
            }
        }
        List<PpmActivityCategory> ppmActivityCategories = ppmActivityCategoryDao.getListByHQLWithNamedParams(hql,map);
        return ppmActivityCategories;
    }

    @Override
    public List<PpmProcess> getProcessList(Integer org,Boolean enabled,Integer category,List<Long> processIds) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from PpmProcess where org=:org";
        map.put("org",org);
        if (enabled!=null){
            hql+=" and enabled=:enabled";
            map.put("enabled",enabled);
        }
        if (category!=null){
            hql+=" and category=:category";
            map.put("category",category);
        }
        if (processIds!=null&&processIds.size()>0){
            hql+=" and id not in (:processIds)";
            map.put("processIds",processIds);
        }
        hql+= " order by timing";
        List<PpmProcess> ppmProcessList = ppmProcessDao.getListByHQLWithNamedParams(hql,map);
        for (PpmProcess p : ppmProcessList) {
            if (p.getIsCyclicity() !=null && p.getIsCyclicity()) {
                String periodicity = this.getPeriodicity(p.getId(), null);
                p.setPeriodicity(periodicity);
            }
        }
        return ppmProcessList;
    }

    @Override
    public List<PpmActivity> getActivityList(Integer org,Boolean enabled,Integer category,Byte type,Byte timing,List<Long> activityIds) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from PpmActivity where org=:org";
        map.put("org",org);
        if (enabled!=null){
            hql+=" and enabled=:enabled";
            map.put("enabled",enabled);
        }
        if (category!=null){
            hql+=" and category=:category";
            map.put("category",category);
        }
        if (type!=null){
            hql+=" and type=:type";
            map.put("type",type);
        }
        if (timing!=null){
            hql+=" and timing=:timing";
            map.put("timing",timing);
        }
        if (activityIds!=null&&activityIds.size()>0){
            hql+=" and id not in (:activityIds)";
            map.put("processIds",activityIds);
        }
        hql+= " order by timing";
        List<PpmActivity> ppmActivityList = ppmActivityDao.getListByHQLWithNamedParams(hql,map);
        for (PpmActivity p : ppmActivityList) {
            if (p.getIsCyclicity() !=null && p.getIsCyclicity()) {
                String periodicity = this.getPeriodicity(null, p.getId());
                p.setPeriodicity(periodicity);
            }
        }
        return ppmActivityList;
    }

    @Override
    public List<Map<String,Object>> getActivityByCategoryList(Integer org,Boolean enabled,Integer category,Byte type) {
        Map<String,Object> map = new HashMap<>();
        String hql = "select pa.id as id,pa.code as code,pa.name as name, pa.category as category,pac.name as categoryName,pa.timing as timing," +
                "pa.isCyclicity as isCyclicity,pa.creator as creator,pa.createName as createName,pa.createTime as createTime, pa.kind as kind from PpmActivity pa,PpmActivityCategory pac where (pa.category=pac.id or pa.category is null) and pa.org=:org";
        map.put("org",org);
        if (enabled!=null){
            hql+=" and pa.enabled=:enabled";
            map.put("enabled",enabled);
        }
        if (category!=null){
            hql+=" and pa.category=:category";
            map.put("category",category);
        }
        if (type!=null){
            hql+=" and pa.type=:type";
            map.put("type",type);
        }
        hql+=" order by pa.timing";
        List<Object[]> ppmActivityList = ppmActivityDao.getListByHQLWithNamedParams(hql,map);
        List<Map<String,Object>> mapList = new ArrayList<>();
        for (Object[] obj:ppmActivityList) {
            Map<String,Object> map2 = new HashMap<>();
            Long id = (Long) obj[0];
            map2.put("id",obj[0]);
            map2.put("code",obj[1]);
            map2.put("name",obj[2]);
            Integer category1 = (Integer) obj[3];
            map2.put("category",category1);
            if (category1!=null) {
                map2.put("categoryName", obj[4]);
            }else {
                map2.put("categoryName", "");
            }
            map2.put("timing",obj[5]);
            /*Boolean isCyclicity = (Boolean) obj[6];
            map2.put("isCyclicity",isCyclicity);
            if (isCyclicity) {
                String periodicity = this.getPeriodicity(null, id);
                map2.put("periodicity",periodicity);
            }*/
            map2.put("creator",obj[7]);
            map2.put("createName",obj[8]);
            map2.put("createTime",obj[9]);
            map2.put("kind",obj[10]);
            mapList.add(map2);
        }
        return mapList;
    }

    @Override
    public Map<String, Object> updateCategoryEnabled(User user,Integer categoryId, Integer enabled) {
        Map<String,Object> map = new HashMap<>();
        PpmActivityCategory ppmActivityCategory = ppmActivityCategoryDao.get(categoryId);
        Boolean enabled1 = true;
        if (0==enabled){
            enabled1=false;
        }
        if (enabled1.equals(ppmActivityCategory.getEnabled())){
            map.put("content","与原状态相同");
        }else {
            List<PpmProcess> ppmProcessList = new ArrayList<>();
            List<PpmActivity> ppmActivityList = new ArrayList<>();
            if(enabled1.equals(false)){
                if (ActivityCategoryType.process.getIndex().equals(ppmActivityCategory.getType())){  //工序的
                    ppmProcessList = getProcessList(user.getOid(),null,categoryId,null);
                }else {
                    ppmActivityList = getActivityList(user.getOid(),null,categoryId,ppmActivityCategory.getType(),null,null);
                }
            }
            if (ppmProcessList.size()>0||ppmActivityList.size()>0){
                map.put("content","操作失败！因为此类别正在被使用！");
            }else {
                ppmActivityCategory.setEnabled(enabled1);
                ppmActivityCategory.setUpdateName(user.getUserName());
                ppmActivityCategory.setUpdator(user.getUserID());
                ppmActivityCategory.setUpdateTime(new Date());
                ppmActivityCategoryDao.update(ppmActivityCategory);
                map.put("content", "操作成功");
            }
        }
        return map;
    }

    //查询是否有相同的代号或名称
    @Override
    public List<PpmActivity> getPpmActivityByRepeatName(Integer org,String code, String name,Byte type){
        Map<String,Object> map = new HashMap<>();
        String hql = "from PpmActivity where org=:org";
        map.put("org",org);
        if (type!=null){
            hql += " and type=:type";
            map.put("type", type);
        }
        if (StringUtils.isNotEmpty(code)||StringUtils.isNotEmpty(name)) {
            if (StringUtils.isNotEmpty(code)&&StringUtils.isEmpty(name)) {
                hql += " and code=:code";
                map.put("code", code);
            }else if (StringUtils.isNotEmpty(name)&&StringUtils.isEmpty(code)) {
                hql += " and name=:name";
                map.put("name", name);
            }else {
                hql += " and (code=:code or name=:name)";
                map.put("code", code);
                map.put("name", name);
            }
        }
        List<PpmActivity> ppmActivityList = ppmActivityDao.getListByHQLWithNamedParams(hql,map);
        return ppmActivityList;
    }

    @Override
    public Map<String, Object> createActivity(User user, PpmActivity ppmActivity , PpmActivityCyclicity ppmActivityCyclicity) {
        Map<String,Object> map = new HashMap<>();
        List<PpmActivity> ppmActivityList = getPpmActivityByRepeatName(user.getOid(),ppmActivity.getCode(),ppmActivity.getName(),ppmActivity.getType());
        if (ppmActivityList.size()>0){
            map.put("content","代号或名称重复");
        }else {
            Integer state = 1;
            if (ppmActivity.getIsLeaf() != null) {
                if (ppmActivity.getIsLeaf()) {
                    state = 2;
                }
            }else {
                ppmActivity.setIsLeaf(false);
            }
            ppmActivity.setEnabled(true);
            ppmActivity.setOrg(user.getOid());
            ppmActivity.setCreateName(user.getUserName());
            ppmActivity.setCreator(user.getUserID());
            ppmActivity.setCreateTime(new Date());
            ppmActivityDao.save(ppmActivity);
            if (state == 1) {
                if (ppmActivity.getIsCyclicity() !=null && ppmActivity.getIsCyclicity()) {
                    this.addActivityCyclicity(ppmActivityCyclicity,ppmActivity,null,user);
                }
            }
            map.put("content","操作成功");
        }
        return map;
    }

    public List<ProcessesDto> getProcessByOidAndProductList(Integer org, Integer product) {
        Map<String,Object> map = new HashMap<>();
        String hql = "select new cn.sphd.miners.modules.processManagement.dto.ProcessesDto(ppp.id,ppp.product,ppp.org,ppp.process,pp.code,pp.name,ppp.orders,pp.isLeaf,pp.timing,pp.kind) from PdProductProcess ppp,PpmProcess pp where ppp.process=pp.id";
        if (org!=null){
            hql += " and ppp.org=:org";
            map.put("org", org);
        }
        if (product!=null){
            hql += " and ppp.product=:product";
            map.put("product", product);
        }
        hql+=" order by ppp.orders";
        List<ProcessesDto> processes = ppmProcessDao.getListByHQLWithNamedParams(hql,map);
        return processes;
    }

    //加上了类别名称，自己查看下
    @Override
    public List<SonProcessesDto> getActivityByProductAndProcessId(Integer org, Integer product, Long processId, Long parent) {
        Map<String,Object> map = new HashMap<>();
        String hql = "select new cn.sphd.miners.modules.processManagement.dto.SonProcessesDto(pf.id,pf.product,pf.process,pf.activity,pf.parent,pa.code,pa.name,pf.orders,pa.category,pac.name,pa.type,pa.isCyclicity,pa.isLeaf,pa.timing,pa.kind) from PpmFlow pf,PpmActivity pa,PpmActivityCategory pac where pf.activity=pa.id and (pa.category=pac.id or pa.category is null) and pf.org=:org";
        map.put("org", org);
        if (product!=null){
            hql += " and pf.product=:product";
            map.put("product", product);
        }
        if (product!=null){
            hql += " and pf.process=:process";
            map.put("process", processId);
        }
        if (parent!=null){
            hql += " and pf.parent=:parent";
            map.put("parent", parent);
        }else {
            hql += " and pf.parent is null";
        }
        hql+=" group by pf.id order by pf.orders";
        List<SonProcessesDto> sonProcesses = ppmActivityDao.getListByHQLWithNamedParams(hql,map);
        if (sonProcesses != null) {
            for (SonProcessesDto p : sonProcesses) {
                if (ActivityType.operate.getIndex().equals(p.getType())) {
                    if (p.getIsCyclicity() !=null && p.getIsCyclicity()) {
                        String periodicity = this.getPeriodicity(null, p.getActivity());
                        p.setPeriodicity(periodicity);
                    }
                }
            }
        }
        return sonProcesses;
    }

    @Override
    public Map<String, Object> getSettingsList(User user,Integer product) {
        Map<String,Object> map = new HashMap<>();
        List<ProcessesDto> processesList = this.getProcessByOidAndProductList(user.getOid(),product);//获取某产品下的工序
        for (ProcessesDto p:processesList) {
            if (p.getIsLeaf()) {
                List<SonProcessesDto>  sonProcesses= this.getActivityByProductAndProcessId(user.getOid(),product,p.getProcess(),null);  //子工序或者操作
                if (sonProcesses.size()>0&&2==sonProcesses.get(0).getType().intValue()){  //判断是否是子工序，是的话继续查子工序下的操作
                    for (SonProcessesDto sonProcess:sonProcesses) {
                        if (sonProcess.getIsLeaf()) {
                            List<SonProcessesDto>  sonOperateList= this.getActivityByProductAndProcessId(user.getOid(),product,p.getProcess(),sonProcess.getActivity());  //子工序或者操作
                            sonProcess.setSonOperateList(sonOperateList);
                        }
                    }
                    p.setSonProcesses(sonProcesses);  //子工序
                }else {
                    p.setOperateList(sonProcesses);  //操作
                }
            }
        }
        map.put("settingsList",processesList);
        List<PpmProcessStat> settingsList = this.getProcessSettingsList(user);
        map.put("configurable", settingsList.get(0).getConfigurable());
        return map;
    }

    @Override
    public List<Long> getProcessIds(Integer org,Integer product) {
        Map<String,Object> map = new HashMap<>();
        String hql = "select process from PdProductProcess where org=:org";
        if (product!=null){
            hql += " and product=:product";
            map.put("product", product);
        }
        List<Long> processIds = pdProductProcessDao.getListByHQLWithNamedParams(hql,map);
        return processIds;
    }

    @Override
    public List<PpmProcess> getProcessByProductList(Integer org,Integer product) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from PpmProcess pp, PdProductProcess ppp where ppp.org=:org and ppp.process=pp.id";
        if (product!=null){
            hql += " and ppp.product=:product";
            map.put("product", product);
        }
        List<PpmProcess> ppmProcessList = ppmProcessDao.getListByHQLWithNamedParams(hql,map);
        return ppmProcessList;
    }

    @Override
    public Map<String, Object> chooseProcess(User user,Integer product, String processSJson) {
        Map<String,Object> map = new HashMap<>();
        JSONArray jsonArray = JSONArray.fromObject(processSJson);
        Integer processNumAll = 0;  //工序总数
        Integer sonProcessNumAll = 0;  //子工序总数
        Integer operateNumAll = 0;  //操作总数
        Integer status = 1;  //判断最后是否操作成功的，1-操作成功 0-操作失败
        if (jsonArray.size()>0) {

            //将原本的数据删除，用新的数据新增
            List<PdProductProcess> pdProductProcessList = this.getPdProductProcessList(product); //产品下的所有工序
            for (PdProductProcess pdProductProcess:pdProductProcessList) {
                List<PpmFlow> ppmFlowList = this.getPpmFlowAll(product, pdProductProcess.getProcess(), null); //产品某工序下所有的子工序或操作，以及子工序下的操作
                if (ppmFlowList.size() > 0) {
                    ppmFlowDao.deleteAll(ppmFlowList);
                }
                pdProductProcessDao.delete(pdProductProcess); //删除此工序
            }

            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject process = jsonArray.getJSONObject(i);
//                String productProcessId = process.getString("productProcessId"); //产品工序id 有值就是已设置过的，没值是再次设置时新添加的
                Long processId = process.getLong("processId");  //工序id
                Integer orders = process.getInt("order");  //排序
                String sonProcessString = process.getString("sonProcess"); //子工序的内容
                String operateString = process.getString("operate"); //工序下的操作内容

                PdProductProcess pdProductProcess = new PdProductProcess();
                pdProductProcess.setOrg(user.getOid());
                pdProductProcess.setProduct(product);
                pdProductProcess.setProcess(processId);
                pdProductProcess.setOrders(orders);
                pdProductProcess.setCreateName(user.getUserName());
                pdProductProcess.setCreator(user.getUserID());
                pdProductProcess.setCreateTime(new Date());

                PpmProcess ppmProcessSingle = this.getPpmProcessSingle(processId);
                if(ppmProcessSingle.getIsLeaf()) {
                    if(!"[]".equals(sonProcessString)||!"[]".equals(operateString)) {
                        pdProductProcessDao.save(pdProductProcess);
                        processNumAll = processNumAll+1;  //工序总数
                    }else {
                        status = 0;
                        break;
                    }
                    Integer sonProcessHaving = process.getInt("sonProcessHaving"); //1-有子工序 0-无子工序
                    JSONArray sonProcessJsonArray = JSONArray.fromObject(sonProcessString);
                    JSONArray operateJsonArray = JSONArray.fromObject(operateString);
                    //工序下的子工序和操作只能二选一,但必须有
                    if (sonProcessHaving != null && 1 == sonProcessHaving && sonProcessJsonArray.size()>0) {
                        for (int j = 0; j < sonProcessJsonArray.size(); j++) {
                            JSONObject sonProcess = sonProcessJsonArray.getJSONObject(j);
//                        String sonFlowId = sonProcess.getString("sonFlowId");  //子工序流程id 有值就是已设置过的，没值是再次设置时新添加的
                            Long sonProcessId = sonProcess.getLong("sonProcessId");  //子工序id
                            Integer sonProcessOrders = sonProcess.getInt("sonProcessOrders");  //子工序排序
                            String path = "";
                            if (StringUtils.isEmpty(path)){
                                path = sonProcessId.toString();
                            }else {
                                path = path+","+sonProcessId.toString();
                            }
                            this.addPpmFlow(user, processId, product, sonProcessId, null, sonProcessOrders, path);
                            sonProcessNumAll = sonProcessNumAll + 1;  //子工序总数
                            //获取子工序实体 看看是否有操作
                            PpmActivity activitySingle = this.getPpmActivitySingle(sonProcessId);
                            if (activitySingle.getIsLeaf()) {
                                String sonOperateString = sonProcess.getString("sonOperate"); //子工序中的操作内容
                                JSONArray sonOperateJsonArray = JSONArray.fromObject(sonOperateString);
                                if (sonOperateJsonArray.size()>0) {
                                    for (int k = 0; k < sonOperateJsonArray.size(); k++) {
                                        JSONObject sonOperate = sonOperateJsonArray.getJSONObject(k);
//                                String sonOperateFlowId = sonOperate.getString("sonOperateFlowId");  //子工序下的操作流程id 有值就是已设置过的，没值是再次设置时新添加的
                                        Long sonOperateId = sonOperate.getLong("sonOperateId");  //子工序的操作id
                                        Integer sonOperateOrders = sonOperate.getInt("sonOperateOrders");  //子工序的操作排序
                                        path = path+","+sonOperateId.toString();
                                        this.addPpmFlow(user, processId, product, sonOperateId, sonProcessId, sonOperateOrders, path);

                                        operateNumAll = operateNumAll + 1; //操作总数
                                    }
                                }else {
                                    status=0;
                                    break;
                                }
                            }
                        }
                    } else if (operateJsonArray.size() > 0) {
                        for (int z = 0; z < operateJsonArray.size(); z++) {
                            JSONObject operate = operateJsonArray.getJSONObject(z);
//                        String operateFlowId = operate.getString("operateFlowId");  //工序下的操作流程id 有值就是已设置过的，没值是再次设置时新添加的
                            Long operateId = operate.getLong("operateId");  //工序的操作id
                            Integer operateOrders = operate.getInt("operateOrders");  //工序的操作排序
                            String path = "";
                            if (StringUtils.isEmpty(path)) {
                                path = operateId.toString();
                            }else {
                                path = path+","+operateId.toString();
                            }
                            this.addPpmFlow(user, processId, product, operateId, null, operateOrders, path);
                            operateNumAll = operateNumAll + 1; //操作总数
                        }
                    } else {
                        status = 0;
                        break;
                    }
                }else {
                    pdProductProcessDao.save(pdProductProcess);
                    processNumAll = processNumAll+1;  //工序总数
                }
            }
        }else {
            status = 0;
        }
        if (1==status){
            //将产品下的工序、子工序、操作的统计数量更新
            PpmProductionStat ppmProductionStat = this.getPpmProductionStat(user.getOid(),product,null);
            Integer processCount = ppmProductionStat.getProcessCount();  //原来的工序数量
            ppmProductionStat.setProcessCount(processNumAll); //工序数量
            ppmProductionStat.setSubProcessCount(sonProcessNumAll); //子工序数量
            ppmProductionStat.setOperateCount(operateNumAll); //操作数量
            ppmProductionStatDao.update(ppmProductionStat);

            if (processCount==0) {   //如果原本是未设置的，那么已设置数量需要加1；若本来就是已设置的，那么数据不用改变
                PpmProcessStat ppmProcessStat = this.getPpmProcessStat(user.getOid(), ppmProductionStat.getType(), ProcessStatAssembly.manufacturing.getIndex());
//                ppmProcessStat.setTbcAmount(ppmProcessStat.getTbcAmount()-1); //应设置数量 减1
                ppmProcessStat.setAbdAmount(ppmProcessStat.getAbdAmount()+1>ppmProcessStat.getTbcAmount()?ppmProcessStat.getTbcAmount():ppmProcessStat.getAbdAmount()+1);  //已设置数量 加1
                ppmProcessStatDao.update(ppmProcessStat);
            }
            map.put("content","操作成功");
        }else {
            map.put("content", "操作失败！一个产品至少需有一道工序，每道工序下至少需有一项操作！");
        }
        return map;
    }

    public PpmFlow addPpmFlow(User user,Long processId,Integer product,Long activity,Long parent,Integer orders,String path){
        PpmFlow ppmFlow = new PpmFlow();
        ppmFlow.setOrg(user.getOid());
        ppmFlow.setProcess(processId);
        ppmFlow.setProduct(product);
        ppmFlow.setActivity(activity); //活动id
        ppmFlow.setParent(parent);  //父活动id
        ppmFlow.setOrders(orders);
        ppmFlow.setPath(path);
        ppmFlow.setEnabled(true);
        ppmFlow.setCreateName(user.getUserName());
        ppmFlow.setCreator(user.getUserID());
        ppmFlow.setCreateTime(new Date());
        ppmFlowDao.save(ppmFlow);
        return ppmFlow;
    }

    @Override
    public List<Map<String, Object>> getPdBaseList(User user,String type) {
        String hql = "SELECT\n" +
                "new Map (p.id AS id,\n" +
                "p.innerSn as innerSn,\n" +
                "p.name AS name,\n" +
                "p.model AS model,\n" +
                "p.specifications AS specifications,\n" +
                "p.unit AS unit,\n" +
                "p.unitId AS unitId,\n" +
                "p.source AS source,\n" +
                "p.composition AS composition,\n" +
                "p.netWeight AS netWeight,\n" +
                "p.weightUnit as weightUnit,\n" +
                "p.createName AS createName,\n" +
                "p.createDate AS createDate )\n" +
                "FROM\n" +
                "PdBase p\n" +
                "WHERE\n" +
                " p.oid =:oid and p.enabled=1 and p.type=:type and p.process!=1";
        Map<String,Object> map = new HashMap<>();
        map.put("oid",user.getOid());
        map.put("type",type);
        return pdProductProcessDao.getListByHQLWithNamedParams(hql,map);
    }

    @Override
    public List<Map<String, Object>> getProductionStatList(User user,String type, PageInfo pageInfo) {
//        String hql = "SELECT\n" +
//                "new Map (p.id AS id,\n" +
//                "p.innerSn as innerSn,\n" +
//                "p.name AS name,\n" +
//                "p.model AS model,\n" +
//                "p.specifications AS specifications,\n" +
//                "p.unit AS unit,\n" +
//                "p.unitId AS unitId,\n" +
//                "p.source AS source,\n" +
//                "p.composition AS composition,\n" +
//                "p.netWeight AS netWeight,\n" +
//                "p.weightUnit as weightUnit,\n" +
//                "pps.processCount AS processCount,\n" +
//                "pps.subProcessCount AS subProcessCount,\n" +
//                "pps.operateCount AS operateCount )\n" +
//                " FROM\n" +
//                " PdBase p,\n" +
//                "PpmProductionStat pps\n" +
//                " WHERE\n" +
//                " p.oid =:oid and p.enabled=1 and p.type=:type and p.process!=1 and p.id=pps.product";
//        Map<String,Object> map = new HashMap<>();
//        map.put("oid",user.getOid());
//        map.put("type",type);
//        List<Map<String, Object>> productionStatList = pdProductProcessDao.getListByHQLWithNamedParams(hql,map,pageInfo);String hql = "SELECT\n" +
        String hql = "select p.id AS id,p.innerSn as innerSn,p.name AS name,p.model AS model,p.specifications AS specifications,p.unit AS unit,p.unitId AS unitId,p.source AS source,"+
                "p.composition AS composition,p.netWeight AS netWeight,p.weightUnit as weightUnit,pps.processCount AS processCount,pps.subProcessCount AS subProcessCount,pps.operateCount AS operateCount"+
                " from PdBase p,PpmProductionStat pps where p.oid =:oid and p.enabled=1 and p.type=:type and p.process!=1 and p.id=pps.product";
        Map<String,Object> map = new HashMap<>();
        map.put("oid",user.getOid());
        map.put("type",type);
        List<Object[]> productionStatList1 = pdProductProcessDao.getListByHQLWithNamedParams(hql,map,pageInfo);
        List<Map<String,Object>> productionStatList = new ArrayList<>();
        for (Object[] ob:productionStatList1) {
            Map<String,Object> map1 = new HashMap<>();
            map1.put("id",ob[0]);
            map1.put("innerSn",ob[1]);
            map1.put("name",ob[2]);
            map1.put("model",ob[3]);
            map1.put("specifications",ob[4]);
            map1.put("unit",ob[5]);
            map1.put("unitId",ob[6]);
            map1.put("source",ob[7]);
            map1.put("composition",ob[8]);
            map1.put("netWeight",ob[9]);
            map1.put("weightUnit",ob[10]);
            map1.put("processCount",ob[11]);
            map1.put("subProcessCount",ob[12]);
            map1.put("operateCount",ob[13]);
            productionStatList.add(map1);
        }
        return productionStatList;
    }

    @Override
    public List<Map<String, Object>> getPdBaseProcessList(User user) {
        //后续追加字段
        String hql = "SELECT\n" +
                "new Map (p.id AS id,\n" +
                "p.innerSn as innerSn,\n" +
                "p.name AS name,\n" +
                "p.model AS model,\n" +
                "p.specifications AS specifications,\n" +
                "p.unit AS unit,\n" +
                "p.unitId AS unitId,\n" +
                "p.source AS source,\n" +
                "p.composition AS composition,\n" +
                "p.netWeight AS netWeight,\n" +
                "p.weightUnit as weightUnit,\n" +
                "p.createName AS createName,\n" +
                "p.createDate AS createDate )\n" +
                "FROM\n" +
                "PdBase p\n" +
                "WHERE\n" +
                " p.oid =:oid and p.enabled=1 and p.type=1 and p.process!=1";
        Map<String,Object> map = new HashMap<>();
        map.put("oid",user.getOid());
        return pdProductProcessDao.getListByHQLWithNamedParams(hql,map);
    }

    @Override
    public List<PpmProcessStat> getPpmProcessStatList(Integer org,Byte type,Byte assembly) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from PpmProcessStat where org=:org";
        map.put("org", org);
        if (type!=null){
            hql += " and type=:type";
            map.put("type", type);
        }
        if (assembly!=null){
            hql += " and assembly=:assembly";
            map.put("assembly", assembly);
        }
        List<PpmProcessStat> ppmProcessStatList = ppmProcessStatDao.getListByHQLWithNamedParams(hql,map);
        return ppmProcessStatList;
    }

    @Override
    public PpmProcessStat getPpmProcessStat(Integer org,Byte type,Byte assembly) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from PpmProcessStat where org=:org";
        map.put("org", org);
        if (type!=null){
            hql += " and type=:type";
            map.put("type", type);
        }
        if (assembly!=null){
            hql += " and assembly=:assembly";
            map.put("assembly", assembly);
        }
        PpmProcessStat ppmProcessStat = (PpmProcessStat) ppmProcessStatDao.getByHQLWithNamedParams(hql,map);
        return ppmProcessStat;
    }

    //新增生产工序的统计
    public PpmProcessStat addPpmProcessStat(User user,Byte type,Byte assembly,Integer tbcAmount){
        PpmProcessStat ppmProcessStat = new PpmProcessStat();
        ppmProcessStat.setOrg(user.getOid());
        ppmProcessStat.setType(type);//类型:0-全部,1-产品,2-零组件
        ppmProcessStat.setAssembly(assembly);//操作:1-制造/装配,2-包装
        ppmProcessStat.setTbcAmount(tbcAmount); //应设置数量
        ppmProcessStat.setAbdAmount(0); //已设置数量
        ppmProcessStat.setConfigurable(true);
        ppmProcessStat.setCreateName(user.getUserName());
        ppmProcessStat.setCreator(user.getUserID());
        ppmProcessStat.setCreateTime(new Date());
        ppmProcessStatDao.save(ppmProcessStat);

        return ppmProcessStat;
    }

    @Override
    public List<PpmProcessStat> getProcessSettingsList(User user) {
        List<PpmProcessStat> ppmProcessStatList = getPpmProcessStatList(user.getOid(),null, ProcessStatAssembly.manufacturing.getIndex());
        if (ppmProcessStatList.size()!=2){
            for (int i=1;i<3;i++){
                String ii = i+"";
                List<Map<String, Object>> pdBaseProcessList = getPdBaseList(user,ii);  //type 1产品 2零组件
                PpmProcessStat ppmProcessStat = new PpmProcessStat();
                if (1==i) {
                    ppmProcessStat = addPpmProcessStat(user, ProcessStatType.product.getIndex(), ProcessStatAssembly.manufacturing.getIndex(), pdBaseProcessList.size());
                }else if (2==i){
                    ppmProcessStat = addPpmProcessStat(user, ProcessStatType.spareParts.getIndex(), ProcessStatAssembly.manufacturing.getIndex(), pdBaseProcessList.size());
                }
                ppmProcessStatList.add(ppmProcessStat);
            }
        }
        return ppmProcessStatList;
    }

    public List<PpmProductionStat> getPpmProductionStatList(Integer org,Byte type) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from PpmProductionStat where org=:org";
        map.put("org", org);
        if (type!=null){
            hql += " and type=:type";
            map.put("type", type);
        }
        List<PpmProductionStat> ppmProductionStatList = ppmProductionStatDao.getListByHQLWithNamedParams(hql,map);
        return ppmProductionStatList;
    }

    //新增生产的统计
    public PpmProductionStat addPpmProductionStat(User user,Integer product,Byte type){
        PpmProductionStat ppmProductionStat = new PpmProductionStat();
        ppmProductionStat.setOrg(user.getOid());
        ppmProductionStat.setProduct(product);//产品ID
        ppmProductionStat.setType(type);//类型:0-全部,1-产品,2-零组件
        ppmProductionStat.setProcessCount(0); //工序数量
        ppmProductionStat.setSubProcessCount(0); //子工序数量
        ppmProductionStat.setOperateCount(0); //操作数量
        ppmProductionStat.setPackageCount(0); //套餐数量
        ppmProductionStat.setCreateName(user.getUserName());
        ppmProductionStat.setCreator(user.getUserID());
        ppmProductionStat.setCreateTime(new Date());
        ppmProductionStatDao.save(ppmProductionStat);

        return ppmProductionStat;
    }

    @Override   //判断是否有工序(生产)的统计,没有就添加  type 类型:0-全部,1-产品,2-零组件    product(新增的产品id)
    public Integer havingPpmProductionStatList(User user, Byte type,Integer product) {
        Integer state = 0; //0-应设置数量不需要加，1-应设置数量需要加(重复的零组件不需要加)
        List<PpmProductionStat> ppmProductionStatList = getPpmProductionStatList(user.getOid(),type);
        if (ppmProductionStatList.size()==0){
            List<Map<String,Object>> mapList = getPdBaseList(user,type.toString());
            for (Map<String,Object> map:mapList){
                Integer product1 = (Integer) map.get("id");
                //新增产品工序
                addPpmProductionStat(user,product1,type);
            }
            state=1;
        }
        if (product!=null) { //新增的产品需要再此添加
            PpmProductionStat ppmProductionStat = getPpmProductionStat(user.getOid(),product,type); //系统中已经添加了工序的统计，新增的产品需要再此添加
            if (ppmProductionStat == null) {
                addPpmProductionStat(user, product, type);
                state=1;
            }
        }
        return state;
    }

    @Override   //type 类型:0-全部,1-产品,2-零组件
    public Map<String,Object> getToManageList(User user, Byte type, PageInfo pageInfo) {
        this.havingPpmProductionStatList(user,type,null);//判断是否有工序(生产)的统计,没有就添加
        Map<String,Object> map = new HashMap<>();
        List<Map<String,Object>> mapList = this.getProductionStatList(user,type.toString(),pageInfo); //获取产品以及对应的统计
        map.put("manageList",mapList);
        map.put("pageInfo",pageInfo);
        return map;
    }

    @Override
    public PpmProductionStat getPpmProductionStat(Integer org,Integer product,Byte type) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from PpmProductionStat where org=:org";
        map.put("org", org);
        if (product!=null){
            hql += " and product=:product";
            map.put("product", product);
        }
        if (type!=null){
            hql += " and type=:type";
            map.put("type", type);
        }
        PpmProductionStat ppmProductionStat = (PpmProductionStat) ppmProductionStatDao.getByHQLWithNamedParams(hql,map);
        return ppmProductionStat;
    }

    @Override
    public List<PpmActivity> getActivityByProduct(Integer org,Integer product,Long processId,Long parent) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from PpmActivity pa,PpmFlow pf where pf.org=:org and pf.activity=pa.id";
        map.put("org", org);
        if (product!=null){
            hql += " and pf.product=:product";
            map.put("product", product);
        }
        if (product!=null){
            hql += " and pf.process=:process";
            map.put("process", processId);
        }
        if (parent!=null){
            hql += " and pf.parent=:parent";
            map.put("parent", parent);
        }else {
            hql += " and pf.parent is null";
        }
        List<PpmActivity> ppmActivityList = ppmActivityDao.getListByHQLWithNamedParams(hql,map);
        return ppmActivityList;
    }

    @Override
    public List<PdProductProcess> getPdProductProcessList(Integer product) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from PdProductProcess where product=:product order by orders";
        map.put("product",product);
        List<PdProductProcess> pdProductProcessList = pdProductProcessDao.getListByHQLWithNamedParams(hql,map);
        return pdProductProcessList;
    }

    @Override
    public PdProductProcess getPdProductProcess(Integer product,Long process) {
        HashMap<String, Object> params = new HashMap<>();
        StringBuffer where = new StringBuffer();
        StringBuffer hql = new StringBuffer("from PdProductProcess");
        if (product!=null){
            where.append(" and product=:product");
            params.put("product",product);
        }
        if (process!=null){
            where.append(" and process=:process");
            params.put("process",process);
        }
        if (where.length() > 0) {
            hql.append(" where ").append(where.substring(4)).append(" order by orders");
        }
        PdProductProcess pdProductProcess = (PdProductProcess) pdProductProcessDao.getByHQLWithNamedParams(hql.toString(),params);
        return pdProductProcess;
    }

    @Override   //processId：修改的工序id processIdAfter:要排到这个工序后面的id order：1-拍到最前面 0-拍到某个工序后面
    public Map<String, Object> updateProcessOrders(User user, Integer product, Long processId, Long processIdAfter, Integer orderStatus) {
        Map<String,Object> map = new HashMap<>();
        List<PdProductProcess> pdProductProcessList = this.getPdProductProcessList(product);
        PdProductProcess pdProductProcess = this.getPdProductProcess(product,processId);
        Integer orderOld = pdProductProcess.getOrders(); //要修改工序的原本排序
        if (orderStatus!=null&&1==orderStatus){
            if (1==pdProductProcess.getOrders()){
                map.put("content","排序未变动");
            }else {
                for (PdProductProcess pdProductProcess1:pdProductProcessList) {
                    if (processId.equals(pdProductProcess1.getProcess())){  //要修改的工序
                        pdProductProcess1.setOrders(1);  //排序改为1
                    }else if (pdProductProcess1.getOrders()<orderOld){
                        pdProductProcess1.setOrders(pdProductProcess1.getOrders()+1);  //将小于要修改工序排序的工序排序加1，往后移一位。大于要修改工序排序的工序排序不用动
                    }
                    pdProductProcessDao.update(pdProductProcess1);
                }
                map.put("content","操作成功");
            }
        }else if (processIdAfter!=null){   //移到某工序后面的
            PdProductProcess pdProductProcessAfter = this.getPdProductProcess(product,processIdAfter);
            Integer orderNew = pdProductProcessAfter.getOrders()+1; //要修改工序的新排序
            if (orderOld.equals(orderNew)){  //要修改的工序本就在这个工序的后面
                map.put("content","排序未变动");
            }else {
                for (PdProductProcess pdProductProcess1:pdProductProcessList) {
                    if (processId.equals(pdProductProcess1.getProcess())){  //要修改的工序
                        pdProductProcess1.setOrders(orderNew);  //改为新排序
                    }else if (orderOld<orderNew){  //修改的排序往后排了,比新排序orderNew大的和比老排序orderOld小的排序不用动
                        if (orderOld<pdProductProcess1.getOrders() && pdProductProcess1.getOrders()<=orderNew){
                            pdProductProcess1.setOrders(pdProductProcess1.getOrders()-1);  //在旧排下与新排序之间的工序排序往前移一位，减1(原本新排序orderNew位置的排序也往前移一位)
                        }
                    }else { //修改的排序往前排了(orderNew<orderOld),比新排序orderNew小的和比老排序orderOld大的排序不用动
                        if (orderNew<=pdProductProcess1.getOrders()&&pdProductProcess1.getOrders()<orderOld){
                            pdProductProcess1.setOrders(pdProductProcess1.getOrders()+1);  //在旧排下与新排序之间的工序排序往后移一位，加1(原本新排序orderNew位置的排序也往后移一位)
                        }
                    }
                    pdProductProcessDao.update(pdProductProcess1);
                }
                map.put("content","操作成功");
            }
        }else {
            map.put("content","操作失败，参数错误！");
        }

        return map;
    }

    @Override
    public List<PpmFlow> getPpmFlowList(Integer product,Long process,Long parent) {
        HashMap<String, Object> params = new HashMap<>();
        StringBuffer where = new StringBuffer();
        //from PpmFlow and parent is null where  product=:product and process=:process order by orders
        StringBuffer hql = new StringBuffer("from PpmFlow");
        if (product!=null){
            where.append(" and product=:product");
            params.put("product",product);
        }
        if (process!=null){
            where.append(" and process=:process");
            params.put("process",process);
        }
        if (parent!=null){
            where.append(" and parent=:parent");
            params.put("parent",parent);
        }else {
            where.append(" and parent is null");
        }
        if (where.length() > 0) {
            hql.append(" where").append(where.substring(4)).append(" order by orders");
        }
        List<PpmFlow> ppmFlowList = pdProductProcessDao.getListByHQLWithNamedParams(hql.toString(),params);
        return ppmFlowList;
    }

    @Override    //查询产品下的某工序中的所有子工序和操作，以及子工序中的操作
    public List<PpmFlow> getPpmFlowAll(Integer product,Long process,Long parent) {
        HashMap<String, Object> params = new HashMap<>();
        StringBuffer where = new StringBuffer();
        StringBuffer hql = new StringBuffer("from PpmFlow");
        if (product!=null){
            where.append(" and product=:product");
            params.put("product",product);
        }
        if (process!=null){
            where.append(" and process=:process");
            params.put("process",process);
        }
        if (parent!=null){
            where.append(" and parent=:parent");
            params.put("parent",parent);
        }
        if (where.length() > 0) {
            hql.append(" where ").append(where.substring(4));
        }
        List<PpmFlow> ppmFlowList = pdProductProcessDao.getListByHQLWithNamedParams(hql.toString(),params);
        return ppmFlowList;
    }

    @Override
    public PpmFlow getPpmFlow(Integer product,Long process,Long parent,Long activity) {
        HashMap<String, Object> params = new HashMap<>();
        StringBuffer where = new StringBuffer();
        StringBuffer hql = new StringBuffer("from PpmFlow");
        if (product!=null){
            where.append(" and product=:product");
            params.put("product",product);
        }
        if (process!=null){
            where.append(" and process=:process");
            params.put("process",process);
        }
        if (parent!=null){
            where.append(" and parent=:parent");
            params.put("parent",parent);
        }
        if (activity!=null){
            where.append(" and activity=:activity");
            params.put("activity",activity);
        }
        if (where.length() > 0) {
            hql.append(" where ").append(where.substring(4)).append(" order by orders");
        }
        PpmFlow ppmFlow = (PpmFlow) pdProductProcessDao.getByHQLWithNamedParams(hql.toString(),params);
        return ppmFlow;
    }

    /**
     * 工序下的子工序或操作排序/子工序下的操作排序
     * @param user
     * @param product
     * @param processId
     * @param parent 子工序下的操作排序时的子工序id
     * @param sonProcessId 修改的子工序或操作id
     * @param sonProcessIdAfter 修改的工序要排到此工序id后面
     * @param orderStatus 1-排到最前面 0-排到某个子工序/操作后面
     * @return
     */
    @Override
    public Map<String, Object> updateSonProcessOrders(User user, Integer product, Long processId,Long parent, Long sonProcessId, Long sonProcessIdAfter, Integer orderStatus) {
        Map<String,Object> map = new HashMap<>();
        List<PpmFlow> ppmFlowList = this.getPpmFlowList(product,processId,parent);
        PpmFlow ppmFlow = this.getPpmFlow(product,processId,parent,sonProcessId);
        Integer orderOld = ppmFlow.getOrders(); //要修改子工序/操作/子工序下的操作的原本排序
        if (orderStatus!=null&&1==orderStatus){
            if (1==ppmFlow.getOrders()){
                map.put("content","排序未变动");
            }else {
                for (PpmFlow ppmFlow1:ppmFlowList) {
                    if (sonProcessId.equals(ppmFlow1.getActivity())){  //要修改的子工序/操作/子工序下的操作
                        ppmFlow1.setOrders(1);  //排序改为1
                    }else if (ppmFlow1.getOrders()<orderOld){
                        ppmFlow1.setOrders(ppmFlow1.getOrders()+1);  //将小于要修改子工序排序的子工序排序加1，往后移一位。大于要修改子工序排序的子工序排序不用动
                    }
                    ppmFlowDao.update(ppmFlow1);
                }
                map.put("content","操作成功");
            }
        }else if (sonProcessIdAfter!=null){   //移到某工序后面的
            PpmFlow ppmFlowAfter = this.getPpmFlow(product,processId,parent,sonProcessIdAfter);
            Integer orderNew = ppmFlowAfter.getOrders()+1; //要修改工序的新排序
            if (orderOld.equals(orderNew)){  //要修改的工序本就在这个工序的后面
                map.put("content","排序未变动");
            }else {
                for (PpmFlow ppmFlow1:ppmFlowList) {
                    if (sonProcessId.equals(ppmFlow1.getActivity())){  //要修改的工序
                        ppmFlow1.setOrders(orderNew);  //改为新排序
                    }else if (orderOld<orderNew){  //修改的排序往后排了,比新排序orderNew大的和比老排序orderOld小的排序不用动
                        if (orderOld<ppmFlow1.getOrders() && ppmFlow1.getOrders()<=orderNew){
                            ppmFlow1.setOrders(ppmFlow1.getOrders()-1);  //在旧排下与新排序之间的工序排序往前移一位，减1(原本新排序orderNew位置的排序也往前移一位)
                        }
                    }else { //修改的排序往前排了(orderNew<orderOld),比新排序orderNew小的和比老排序orderOld大的排序不用动
                        if (orderNew<=ppmFlow1.getOrders()&&ppmFlow1.getOrders()<orderOld){
                            ppmFlow1.setOrders(ppmFlow1.getOrders()+1);  //在旧排下与新排序之间的工序排序往后移一位，加1(原本新排序orderNew位置的排序也往后移一位)
                        }
                    }
                    ppmFlowDao.update(ppmFlow1);
                }
                map.put("content","操作成功");
            }
        }else {
            map.put("content","操作失败，参数错误！");
        }
        return map;
    }

    @Override
    public Map<String, Object> deleteProcess(Integer product, Long processId) {
        Map<String,Object> map = new HashMap<>();
        PdProductProcess pdProductProcess = this.getPdProductProcess(product,processId);  //要删除的产品下的工序
        List<PdProductProcess> pdProductProcessList = this.getPdProductProcessList(product); //产品下的所有工序
        if (pdProductProcess!=null&&pdProductProcessList.size()>1) {
            Integer sonProcessNum = 0;
            Integer operateNum = 0;
            List<PpmFlow> ppmFlowList = this.getPpmFlowAll(product,processId,null); //产品某工序下所有的子工序或操作，以及子工序下的操作
            if (ppmFlowList.size()>0){
                sonProcessNum = this.getPpmFlowNumAll(product,processId, ActivityType.sonProcess.getIndex());  //子工序的数量
                operateNum = this.getPpmFlowNumAll(product,processId, ActivityType.operate.getIndex());  //所有操作的数量
                ppmFlowDao.deleteAll(ppmFlowList);
            }
            for (PdProductProcess pp:pdProductProcessList) {
                if (pdProductProcess.getOrders()<pp.getOrders()){
                    pp.setOrders(pp.getOrders()-1); //比要删除工序排序大的工序，排序都往前移一位
                    pdProductProcessDao.update(pp);
                }
            }
            pdProductProcessDao.delete(pdProductProcess); //删除此工序

            PpmProductionStat ppmProductionStat = this.getPpmProductionStat(pdProductProcess.getOrg(),product,null);
            ppmProductionStat.setProcessCount(ppmProductionStat.getProcessCount()-1<0?0:ppmProductionStat.getProcessCount()-1);
            ppmProductionStat.setSubProcessCount(ppmProductionStat.getSubProcessCount()-sonProcessNum<0?0:ppmProductionStat.getSubProcessCount()-sonProcessNum);
            ppmProductionStat.setOperateCount(ppmProductionStat.getOperateCount()-operateNum<0?0:ppmProductionStat.getOperateCount()-operateNum);
            ppmProductionStatDao.update(ppmProductionStat);

            if (0==ppmProductionStat.getProcessCount()){  //此产品下已经没有了工序，也就没有子工序或操作,已设置的数量减1
                PpmProcessStat ppmProcessStat = this.getPpmProcessStat(pdProductProcess.getOrg(),ppmProductionStat.getType(), ProcessStatAssembly.manufacturing.getIndex());
//                ppmProcessStat.setTbcAmount(ppmProcessStat.getTbcAmount()+1);   //应设置数量 加1
                ppmProcessStat.setAbdAmount(ppmProcessStat.getAbdAmount()-1);   //已设置数量 减1
                ppmProcessStatDao.update(ppmProcessStat);
            }
            map.put("content","操作成功");
        }else {
            map.put("content","操作失败！一个产品至少需有一道工序，每道工序下至少需有一项操作！");
        }
        return map;
    }

    @Override    //查询产品下的某工序中的所有子工序和操作，以及子工序中的操作
    public Integer getPpmFlowNumAll(Integer product,Long process,Byte type) {
        HashMap<String, Object> params = new HashMap<>();
        StringBuffer hql = new StringBuffer("select count(pf.id) from PpmFlow pf,PpmActivity pa where pf.activity=pa.id");
        if (product!=null){
            hql.append(" and pf.product=:product");
            params.put("product",product);
        }
        if (process!=null){
            hql.append(" and pf.process=:process");
            params.put("process",process);
        }
        if (type!=null){
            hql.append(" and pa.type=:type");
            params.put("type",type);
        }
        Long num = (Long) ppmFlowDao.getByHQLWithNamedParams(hql.toString(),params);
        return num==null?0:num.intValue();
    }

    @Override
    public Map<String, Object> deleteSonProcess(Integer product, Long processId, Long parent, Long activityId) {
        Map<String,Object> map = new HashMap<>();
        Integer sonProcessNum = 0;
        Integer operateNum = 0;
        PpmFlow ppmFlow = this.getPpmFlow(product, processId, parent, activityId);  //要删除的子工序或操作
        PpmActivity ppmActivity = ppmActivityDao.get(ppmFlow.getActivity());  //查询活动类型
        List<PpmFlow> ppmFlowList = this.getPpmFlowList(product,processId,parent); //产品某工序下的子工序或操作，判断删除后是否没有了子工序或操作
        if (ppmFlowList.size()>1) { //产品某工序下的子工序或操作只有一个，则不能删除
            if (ActivityType.sonProcess.getIndex().equals(ppmActivity.getType())) {  //子工序
                sonProcessNum = 1; //要删除的是子工序，子工序数量加1
                List<PpmFlow> sonOperateList = this.getPpmFlowList(product, processId, activityId); //产品某工序下的子工序中的操作
                operateNum = sonOperateList.size();
                ppmFlowDao.deleteAll(sonOperateList); //将子工序中的操作删除
            } else {   //暂时的，上面是子工序，这个就是工序下的操作
                operateNum = 1; //删除的操作
            }

            for (PpmFlow pf : ppmFlowList) {
                if (ppmFlow.getOrders() < pf.getOrders()) {
                    pf.setOrders(pf.getOrders() - 1); //比要删除子工序/操作排序大的子工序/操作，排序都往前移一位
                }
            }
            ppmFlowDao.delete(ppmFlow);//删除此子工序/操作

            PpmProductionStat ppmProductionStat = this.getPpmProductionStat(ppmActivity.getOrg(), product, null);
//        ppmProductionStat.setProcessCount(ppmProductionStat.getProcessCount()-1);
            ppmProductionStat.setSubProcessCount(ppmProductionStat.getSubProcessCount()-sonProcessNum<0?0:ppmProductionStat.getSubProcessCount() - sonProcessNum);
            ppmProductionStat.setOperateCount(ppmProductionStat.getOperateCount()-operateNum<0?0:ppmProductionStat.getOperateCount()-operateNum);
            ppmProductionStatDao.update(ppmProductionStat);

            map.put("content", "操作成功");
        }else {
            map.put("content","操作失败！一个产品至少需有一道工序，每道工序下至少需有一项操作！");
        }
        return map;
    }

    @Override
    public List<PpmProcessStat> updateProcessSettingsSonProcess(User user) {
        List<PpmProcessStat> list = this.getProcessSettingsList(user);
        for (PpmProcessStat p : list) {
            if (p.getConfigurable()) {
                p.setConfigurable(false);
            }else {
                p.setConfigurable(true);
            }
        }
        return list;
    }

    @Override
    public PpmProcess getPpmProcessSingle(Long id) {
        PpmProcess ppmProcess = ppmProcessDao.get(id);
        return ppmProcess;
    }

    @Override
    public PpmActivity getPpmActivitySingle(Long id) {
        PpmActivity ppmActivity = ppmActivityDao.get(id);
        return ppmActivity;
    }

    //公用新增周期方法
    private void addActivityCyclicity(PpmActivityCyclicity ppmActivityCyclicity, PpmActivity ppmActivity, PpmProcess ppmProcess, User user) {
        if (ppmProcess != null) {
            ppmActivityCyclicity.setProcess(ppmProcess.getId());
        } else if (ppmActivity != null){
            ppmActivityCyclicity.setActivity(ppmActivity.getId());
        }
        ppmActivityCyclicity.setOrg(user.getOid());
        ppmActivityCyclicity.setEnabled(true);
        ppmActivityCyclicity.setCreateName(user.getUserName());
        ppmActivityCyclicity.setCreator(user.getUserID());
        ppmActivityCyclicity.setCreateDate(new Date());
        ppmActivityCyclicityDao.save(ppmActivityCyclicity);
    }

    //公用获取周期性，返回“3/3秒”
    private String getPeriodicity(Long process, Long activity){
        StringBuffer hql = new StringBuffer(" from PpmActivityCyclicity");
        HashMap<String, Object> params = new HashMap<>();
        List<String> where = new ArrayList<>();
        if (process!=null){
            where.add("process = :process");
            params.put("process",process);
        }
        if (activity!=null){
            where.add("activity = :activity");
            params.put("activity",activity);
        }
        hql.append(" where ").append(StringUtils.join(where, " "));
        PpmActivityCyclicity pac = (PpmActivityCyclicity) ppmActivityCyclicityDao.getByHQLWithNamedParams(hql.toString(),params);
        String periodicity = pac.getExecuteTimes()+ "/" +pac.getFrequency();
        if ((Timeunit.second.getIndex()).equals(pac.getTimeUnit())) {
            periodicity += "秒";
        } else if ((Timeunit.point.getIndex()).equals(pac.getTimeUnit())) {
            periodicity += "分";
        } else if ((Timeunit.hour.getIndex()).equals(pac.getTimeUnit())) {
            periodicity += "小时";
        } else if ((Timeunit.day.getIndex()).equals(pac.getTimeUnit())) {
            periodicity += "天";
        }
        return periodicity;
    }
}
