<template>
  <div id="pruchaseAdvancecation">
    <TY_NavTop title="采购预付款的申请" isSearch="true" @toggleList="search"></TY_NavTop>
    <div class="container">
      <div class="nowTime">以下各条预付款尚未支付，如需要，请及时提醒财务！</div>
      <div>
        <a class="ui-cell" v-on:click="jump('11' + item.poOrderPrepayment.id)" v-for="(item, index) in toHandle" v-bind:key="index">
          <div class="item_fn">
            <div class="supName">供应商:{{item.fullName}} <span class="right">{{ item.codeName }}</span></div>
            <div class="oInfo">
              <p>计划付款金额&nbsp;{{item.poOrderPrepayment.planAmount.toFixed(2)}}元</p>
              <p>计划付款时间&nbsp;{{item.poOrderPrepayment.planDate |formatDay('YYYY-MM-DD')}}</p>
            </div>
          </div>
          <div class="txt_right">申请人：{{ item.poOrders.createName  }}
            {{item.poOrders.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
          <div class="ui-cell__ft"></div>
        </a>
      </div>
    </div>
  </div>
</template>
<style lang="less">
#pruchaseAdvancecation{
  .right{ float: right;  }
  .ui-cell{ display: block;  }
  .oInfo{ padding:5px 20px; }
  .txt_right{ text-align: right;  }
}
</style>
<script>
export default {
  name: 'pruchaseAdvancecation',
  data () {
    return {
      toHandle:[]
    }
  },
  computed: {
  },
  created () {
    let that = this;
    this.getList();
  },
  filters: {
    formatType: function (num, amount) {
      switch (Number(num)){
        case 1:
          return "本次仅提交票据，不付款";
          break;
        case 2:
          return `申请付款${amount.toFixed(2)}元`;
          break;
        case 3:
          return `申请付款${amount.toFixed(2)}元`;
          break;
      }
    },
  },
  methods: {
    getList: function () {
      let that = this
      this.axios.get('../../../purchaseInvoice/getAdvancePaymentApply.do')
        .then(function (response) {
          var deta = response.data.data;
          var list = deta.advancePaymentApplyHandle;
          if(list && list.length >0){
            that.toHandle = list;
          }
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    jump: function (id) {
      this.$router.push({//0代表采购预付款的申请页
        path: `/purchaseInvoiceDetails4/${id}/0`
      })
    },
    search: function () {
      this.$router.push({
        path: `/applyReimburseQuery`
      })
    }
  }
}
</script>
