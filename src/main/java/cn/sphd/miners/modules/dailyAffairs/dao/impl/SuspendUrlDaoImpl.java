package cn.sphd.miners.modules.dailyAffairs.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.dailyAffairs.dao.SuspendUrlDao;
import cn.sphd.miners.modules.dailyAffairs.entity.SuspendUrl;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

/**
 * Created by Administrator on 2019/5/8.
 */
@Repository
public class SuspendUrlDaoImpl extends BaseDao<SuspendUrl,Serializable> implements SuspendUrlDao {
}
