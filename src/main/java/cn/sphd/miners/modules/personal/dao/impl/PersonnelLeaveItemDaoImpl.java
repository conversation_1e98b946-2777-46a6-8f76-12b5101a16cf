package cn.sphd.miners.modules.personal.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.personal.dao.PersonnelLeaveItemDao;
import cn.sphd.miners.modules.personal.entity.PersonnelLeaveItem;

import org.hibernate.Query;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.List;

/**
 * Created by Administrator on 2018/3/28.
 */
@Repository
public class PersonnelLeaveItemDaoImpl extends BaseDao<PersonnelLeaveItem,Serializable> implements PersonnelLeaveItemDao{


    @Override
    public List<Integer> getIntegerListBySql(String sql) {
        Query query = this.getSession().createSQLQuery(sql);
        return query.list();
    }

}
