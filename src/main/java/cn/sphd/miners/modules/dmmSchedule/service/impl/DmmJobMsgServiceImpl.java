package cn.sphd.miners.modules.dmmSchedule.service.impl;

import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.dmmSchedule.dao.DmmJobMsgDao;
import cn.sphd.miners.modules.dmmSchedule.entity.DmmJobMsg;
import cn.sphd.miners.modules.dmmSchedule.service.DmmJobMsgService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.JSON;
import io.netty.util.internal.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2020/1/2.
 */
@Service("dmmJobMsgService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class DmmJobMsgServiceImpl implements DmmJobMsgService {
    @Autowired
    DmmJobMsgDao dmmJobMsgDao;
    @Autowired
    SWMessageService swMessageService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;

    @Override
    public DmmJobMsg getDmmJobMsgById(Integer id) {
        return dmmJobMsgDao.get(id);
    }

    @Override
    public DmmJobMsg saveDmmJobMsg(Integer superscript, String title, String content, String memo, Long toAccId, Integer jobId, Integer scheduleId) {
        DmmJobMsg dmmJobMsg=new DmmJobMsg();
        dmmJobMsg.setCreateDate(new Date());
        dmmJobMsg.setContent(content);
        dmmJobMsg.setMemo(memo);
        dmmJobMsg.setState("1");
        dmmJobMsg.setSendTime(new Date());
        dmmJobMsg.setJob(jobId);
        dmmJobMsg.setSchedule(scheduleId);
        dmmJobMsgDao.save(dmmJobMsg);//新增

//        HashMap<String,Object> hashMap=new HashMap<>();
//        hashMap.put("dmmJobMsg", dmmJobMsg);
//        hashMap.put("colHeadNum",superscript); // 大于 0 数据加 角标加， 小于0 数据减 角标减
//        swMessageService.rejectSend(superscript,superscript,hashMap,toUserId.toString(),"dmmJobMsg",title,content,user,"dmmJobMsg");// 长连接推送
        clusterMessageSendingOperations.convertAndSendToUser(toAccId.toString(),"/memoJobMsgAcc",title,content,null,null, JSON.toJSONString(dmmJobMsg));

        return dmmJobMsg;
    }

    @Override
    public void updateDmmJobMsg(DmmJobMsg dmmJobMsg, String state, Long accId) {
        if (dmmJobMsg.getState().equals("1")) {
            dmmJobMsg.setState(state);// 2- 已读  4-删除
            dmmJobMsg.setUpdateDate(new Date());
            dmmJobMsgDao.update(dmmJobMsg);

//            HashMap<String,Object> hashMap=new HashMap<>();
//            hashMap.put("DmmJobMsg", dmmJobMsg);
//            hashMap.put("colHeadNum",-1); // 大于 0 数据加 角标加， 小于0 数据减 角标减
//            swMessageService.rejectSend(-1,-1,hashMap,toUserId.toString(),"dmmJobMsg",null,null,user,"dmmJobMsg");// 长连接推送
        }
    }



    @Override
    public List<DmmJobMsg> getDmmJobMsgByJobId(Integer jobId, String state) {
        String hql="from DmmJobMsg where job=:jobId";

        Map<String,Object> params=new HashMap<>();
        params.put("jobId",jobId);
        if (!StringUtil.isNullOrEmpty(state)){
            hql+=" and state=:state";
            params.put("state",state);
        }
        List<DmmJobMsg> DmmJobMsgList=dmmJobMsgDao.getListByHQLWithNamedParams(hql,params);
        return DmmJobMsgList;
    }

    @Override
    public List<DmmJobMsg> getDmmJobMsgByScheduleId(Integer scheduleId, String state) {
        String hql="from DmmJobMsg where schedule=:scheduleId";

        Map<String,Object> params=new HashMap<>();
        params.put("scheduleId",scheduleId);
        if (!StringUtil.isNullOrEmpty(state)){
            hql+=" and state=:state";
            params.put("state",state);
        }
        List<DmmJobMsg> DmmJobMsgList=dmmJobMsgDao.getListByHQLWithNamedParams(hql,params);
        return DmmJobMsgList;

    }

    @Override
    public List<DmmJobMsg> getDmmJobMsgBuUserId(Long accId, String state) {
        String hql="from DmmJobMsg where accId=:accId";
        Map<String,Object> params=new HashMap<>();
        params.put("accId",accId);
        if (!StringUtil.isNullOrEmpty(state)){
            hql+=" and state=:state";
            params.put("state",state);
        }
        List<DmmJobMsg> DmmJobMsgList=dmmJobMsgDao.getListByHQLWithNamedParams(hql,params);
        return DmmJobMsgList;
    }

//    @Override
//    public Integer getBadgeNumber(User user, String code) {
//        Integer result = null;
//        switch (code) {
//            case "dmmJobMsg": //日程处理
//                result = this.getDmmJobMsgBuUserId(user,null,"1").size();
//        }
//        return result;
//    }

    @Override
    public void deleteAllDmmJobMsg(List<DmmJobMsg> DmmJobMsgList) {
        dmmJobMsgDao.deleteAll(DmmJobMsgList);
    }
}
