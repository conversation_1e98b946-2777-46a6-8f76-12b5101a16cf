<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sphd.miners.modules.orderReview.mapper.TSlReviewItemDeliveryHistoryMapper">
    
    <resultMap type="TSlReviewItemDeliveryHistory" id="TSlReviewItemDeliveryHistoryResult">
        <result property="id"    column="id"    />
        <result property="org"    column="org"    />
        <result property="itemDelivery"    column="item_delivery"    />
        <result property="ordersItem"    column="orders_item"    />
        <result property="reviewItem"    column="review_item"    />
        <result property="ordersReview"    column="orders_review"    />
        <result property="deliveryNo"    column="delivery_no"    />
        <result property="deliveryDate"    column="delivery_date"    />
        <result property="deliveryAddressId"    column="delivery_address_id"    />
        <result property="deliveryAddress"    column="delivery_address"    />
        <result property="memo"    column="memo"    />
        <result property="creator"    column="creator"    />
        <result property="createName"    column="create_name"    />
        <result property="createDate"    column="create_date"    />
        <result property="updator"    column="updator"    />
        <result property="updateName"    column="update_name"    />
        <result property="updateDate"    column="update_date"    />
        <result property="operation"    column="operation"    />
        <result property="previousId"    column="previous_id"    />
        <result property="versionNo"    column="version_no"    />
    </resultMap>

    <sql id="selectTSlReviewItemDeliveryHistoryVo">
        select id, org, item_delivery, orders_item, review_item, orders_review, delivery_no, delivery_date, delivery_address_id, delivery_address, memo, creator, create_name, create_date, updator, update_name, update_date, operation, previous_id, version_no from t_sl_review_item_delivery_history
    </sql>

    <select id="selectTSlReviewItemDeliveryHistoryList" parameterType="TSlReviewItemDeliveryHistory" resultMap="TSlReviewItemDeliveryHistoryResult">
        <include refid="selectTSlReviewItemDeliveryHistoryVo"/>
        <where>  
            <if test="org != null "> and org = #{org}</if>
            <if test="itemDelivery != null "> and item_delivery = #{itemDelivery}</if>
            <if test="ordersItem != null "> and orders_item = #{ordersItem}</if>
            <if test="reviewItem != null "> and review_item = #{reviewItem}</if>
            <if test="ordersReview != null "> and orders_review = #{ordersReview}</if>
            <if test="deliveryNo != null "> and delivery_no = #{deliveryNo}</if>
            <if test="deliveryDate != null "> and delivery_date = #{deliveryDate}</if>
            <if test="deliveryAddressId != null "> and delivery_address_id = #{deliveryAddressId}</if>
            <if test="deliveryAddress != null  and deliveryAddress != ''"> and delivery_address = #{deliveryAddress}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
            <if test="creator != null "> and creator = #{creator}</if>
            <if test="createName != null  and createName != ''"> and create_name like concat('%', #{createName}, '%')</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updator != null "> and updator = #{updator}</if>
            <if test="updateName != null  and updateName != ''"> and update_name like concat('%', #{updateName}, '%')</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="operation != null "> and operation = #{operation}</if>
            <if test="previousId != null "> and previous_id = #{previousId}</if>
            <if test="versionNo != null "> and version_no = #{versionNo}</if>
        </where>
    </select>
    
    <select id="selectTSlReviewItemDeliveryHistoryById" parameterType="Long" resultMap="TSlReviewItemDeliveryHistoryResult">
        <include refid="selectTSlReviewItemDeliveryHistoryVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTSlReviewItemDeliveryHistory" parameterType="TSlReviewItemDeliveryHistory">
        insert into t_sl_review_item_delivery_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="org != null">org,</if>
            <if test="itemDelivery != null">item_delivery,</if>
            <if test="ordersItem != null">orders_item,</if>
            <if test="reviewItem != null">review_item,</if>
            <if test="ordersReview != null">orders_review,</if>
            <if test="deliveryNo != null">delivery_no,</if>
            <if test="deliveryDate != null">delivery_date,</if>
            <if test="deliveryAddressId != null">delivery_address_id,</if>
            <if test="deliveryAddress != null">delivery_address,</if>
            <if test="memo != null">memo,</if>
            <if test="creator != null">creator,</if>
            <if test="createName != null">create_name,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updator != null">updator,</if>
            <if test="updateName != null">update_name,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="operation != null">operation,</if>
            <if test="previousId != null">previous_id,</if>
            <if test="versionNo != null">version_no,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="org != null">#{org},</if>
            <if test="itemDelivery != null">#{itemDelivery},</if>
            <if test="ordersItem != null">#{ordersItem},</if>
            <if test="reviewItem != null">#{reviewItem},</if>
            <if test="ordersReview != null">#{ordersReview},</if>
            <if test="deliveryNo != null">#{deliveryNo},</if>
            <if test="deliveryDate != null">#{deliveryDate},</if>
            <if test="deliveryAddressId != null">#{deliveryAddressId},</if>
            <if test="deliveryAddress != null">#{deliveryAddress},</if>
            <if test="memo != null">#{memo},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createName != null">#{createName},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updator != null">#{updator},</if>
            <if test="updateName != null">#{updateName},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="operation != null">#{operation},</if>
            <if test="previousId != null">#{previousId},</if>
            <if test="versionNo != null">#{versionNo},</if>
         </trim>
    </insert>

    <update id="updateTSlReviewItemDeliveryHistory" parameterType="TSlReviewItemDeliveryHistory">
        update t_sl_review_item_delivery_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="org != null">org = #{org},</if>
            <if test="itemDelivery != null">item_delivery = #{itemDelivery},</if>
            <if test="ordersItem != null">orders_item = #{ordersItem},</if>
            <if test="reviewItem != null">review_item = #{reviewItem},</if>
            <if test="ordersReview != null">orders_review = #{ordersReview},</if>
            <if test="deliveryNo != null">delivery_no = #{deliveryNo},</if>
            <if test="deliveryDate != null">delivery_date = #{deliveryDate},</if>
            <if test="deliveryAddressId != null">delivery_address_id = #{deliveryAddressId},</if>
            <if test="deliveryAddress != null">delivery_address = #{deliveryAddress},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="createName != null">create_name = #{createName},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updator != null">updator = #{updator},</if>
            <if test="updateName != null">update_name = #{updateName},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="operation != null">operation = #{operation},</if>
            <if test="previousId != null">previous_id = #{previousId},</if>
            <if test="versionNo != null">version_no = #{versionNo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTSlReviewItemDeliveryHistoryById" parameterType="Long">
        delete from t_sl_review_item_delivery_history where id = #{id}
    </delete>

    <delete id="deleteTSlReviewItemDeliveryHistoryByIds" parameterType="String">
        delete from t_sl_review_item_delivery_history where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>