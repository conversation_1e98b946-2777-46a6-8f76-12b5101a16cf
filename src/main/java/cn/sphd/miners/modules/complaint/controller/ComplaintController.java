package cn.sphd.miners.modules.complaint.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.commodity.entity.PdMerchandise;
import cn.sphd.miners.modules.commodity.entity.SlCustomer;
import cn.sphd.miners.modules.complaint.dto.MessageUserDto;
import cn.sphd.miners.modules.complaint.dto.ProductMessageByComplaint;
import cn.sphd.miners.modules.complaint.entity.ComplaintBase;
import cn.sphd.miners.modules.complaint.entity.ComplaintContact;
import cn.sphd.miners.modules.complaint.entity.ComplaintContactHistory;
import cn.sphd.miners.modules.complaint.entity.ComplaintSupplement;
import cn.sphd.miners.modules.complaint.service.ComplaintAttchmentService;
import cn.sphd.miners.modules.complaint.service.ComplaintService;
import cn.sphd.miners.modules.sales.service.PdCustomerService;
import cn.sphd.miners.modules.sales.service.SlOrdersService;
import cn.sphd.miners.modules.system.dto.UserDto;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

;

/**
 * <AUTHOR>
 * @create 2018-01-15 11:28
 * @description
 **/
@Controller
@RequestMapping("/complaint")
public class ComplaintController {

    @Autowired
    ComplaintService complaintService;
    @Autowired
    UserService userService;
    @Autowired
    SlOrdersService slOrdersService;
    @Autowired
    ComplaintAttchmentService complaintAttchmentService;
    @Autowired
    PdCustomerService pdCustomerService;

    /**
     *获取系统的客户
     */
    @ResponseBody
    @RequestMapping("/getAllCustomerForApp.do")
    public void getAllCustomerForApp(User user, HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        List<SlCustomer> slCustomers = pdCustomerService.getPdCustomers1(user.getOid(),"",null);
        map.put("Customers", slCustomers);
        ObjectToJson.objectToJson1(map,new String[]{"TPdCustomerHistoryPdCustomerViaCustomer","TPdMerchandisePdCustomerViaCustomer",
                "slOrdersHashSet","updateDate","updateName","updator"},response);
        //return new JsonResult(1,map);
    }

    //获取内外部图号和名字
    @RequestMapping("/getCustomerProduntByComplaint.do")
    @ResponseBody
    public JsonResult getCustomerProduntByComplaint(User user, Integer customer){
        List<PdMerchandise> list = slOrdersService.getPdMerchandiselist1(user.getOid(), customer,null);
        List<ProductMessageByComplaint> listProduct = new ArrayList<>();
        if (!list.isEmpty()) {
            for (PdMerchandise pdc : list) {
                ProductMessageByComplaint p = new ProductMessageByComplaint();
                p.setCustomerProduct(pdc.getId());
                p.setInnerSn(pdc.getProduct().getInnerSn());
                p.setInnerName(pdc.getProduct().getName());
                p.setOuterSn(pdc.getOuterSn());
                p.setOuterName(pdc.getOuterName());
                p.setProduct(pdc.getProduct().getId());
                listProduct.add(p);
            }
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("listProduct", listProduct);
        return new JsonResult(1, map);
    }

    //获取某商品签收时间
    @RequestMapping("/getOutPutTime.do")
    @ResponseBody
    public JsonResult getOutPutTime(Integer product){
        List<ProductMessageByComplaint> listProduct = complaintService.listOutPutTime(product);
        HashMap<String, Object> map = new HashMap<>();
        map.put("listProduct", listProduct);
        return new JsonResult(1, map);
    }


    /*新增投诉*/
    @RequestMapping("/saveComplaint.do")
    @MessageMapping("/saveComplaint")
    @ResponseBody
    public JsonResult saveComplaint(String json, User user) throws IOException {
        Integer userID = user.getUserID();
        JSONObject jsonObject = JSONObject.parseObject(json);
        //把信息填到基本信息表中
        ComplaintBase complaintBase = new ComplaintBase();
        ComplaintContact complaintContact = new ComplaintContact();
        complaintBase.setCustomer(jsonObject.getInteger("customer"));
        String receiptTime = jsonObject.getString("receiptTime");
        String peroidString = null;
        if (!"".equals(receiptTime)) {
            complaintBase.setReceiptTime(NewDateUtils.dateFromString(receiptTime, "yyyy-MM-dd"));
            peroidString = NewDateUtils.dateToString(complaintBase.getReceiptTime(), "yyyyMM");
            complaintBase.setPeroid(Integer.valueOf(peroidString));
        }
        if (!"".equals(jsonObject.getString("contactName"))) {
            complaintBase.setContactName(jsonObject.getString("contactName"));
            complaintContact.setContactName(jsonObject.getString("contactName"));
        }
        if (!"".equals(jsonObject.getString("contactPhone"))) {
            complaintBase.setContactPhone(jsonObject.getString("contactPhone"));
            complaintContact.setContactPhone(jsonObject.getString("contactPhone"));
        }
        if (!"".equals(jsonObject.getString("contact"))) {
            complaintBase.setContact(jsonObject.getString("contact"));
            complaintContact.setContact(jsonObject.getString("contact"));
        }
        if (!"".equals(jsonObject.getString("post"))) {
            complaintBase.setPost(jsonObject.getString("post"));
            complaintContact.setPost(jsonObject.getString("post"));
        }
        complaintBase.setCreator(userID);
        complaintBase.setCreateName(user.getUserName());
        complaintBase.setOrg(user.getOid());
        complaintBase.setCreateTime(new Date());
        if(peroidString == null){
            peroidString = NewDateUtils.dateToString(complaintBase.getCreateTime(), "yyyyMM");
            complaintBase.setPeroid(Integer.valueOf(peroidString));
        }
        complaintBase.setType(jsonObject.getString("type"));   //投诉类型
        complaintBase.setTerminateState(jsonObject.getString("terminateState"));

        String state = jsonObject.getString("state");
        complaintBase.setState(state);
        //如果是核心人物创建的投诉，无需审批直接进入立案通过状态
        if ("3".equals(state)) {
            complaintBase.setRegisterApprover(userID);
            complaintBase.setRegisterApproveTime(new Date());
            complaintBase.setProcessor(jsonObject.getInteger("processor"));
        }
        String listComplaintDetail = jsonObject.getString("listComplaintDetail");
        complaintService.saveComplaint(complaintBase, complaintContact, listComplaintDetail, user,jsonObject.getString("module"));
        return new JsonResult(1,"新增成功");
    }

    //获取投诉录入列表
    @RequestMapping("/getComplaintEntry.do")
    @ResponseBody
    public JsonResult getComplaintEntry(User user, String time){
        HashMap<String,Object> map = complaintService.ComplaintEntryList(user,time,user.getOid());
        return new JsonResult(1,map);
    }

    //获取投诉处理列表
    @RequestMapping("/getHandingComplaint.do")
    @ResponseBody
    public JsonResult getHandingComplaint(User user, String role){
        HashMap<String, Object> map = new HashMap<>();
        if("1".equals(role)){
            List<ComplaintBase> listApproveComplaint = complaintService.getComplaintByStateAndRole(user, "1", role);  //立案待审批列表
            map.put("listApproveComplaint", listApproveComplaint);
        }
        List<ComplaintBase> listPendingComplaintEnd = complaintService.getComplaintByStateAndRole(user, "3", role); //待结案列表
        List<ComplaintBase> listApproveComplaintEnd = complaintService.getComplaintByStateAndRole(user, "4", role); //结案待审批列表
        map.put("listPendingComplaintEnd", listPendingComplaintEnd);
        map.put("listApproveComplaintEnd",listApproveComplaintEnd);
        return new JsonResult(1, map);
    }

    //查询通过的结案
    @RequestMapping("/completeComplaintBase.do")
    @ResponseBody
    public JsonResult completeComplaintBase(User user, String role, String time, String settleType, Integer customerId){
        HashMap map = complaintService.listCompleteBase(user,role,time,settleType,customerId);
        return new JsonResult(1, map);
    }

    //核心人物审批立案
    @RequestMapping("/approvePutOnRecordComplaint.do")
    @MessageMapping("/approvePutOnRecordComplaint")
    @ResponseBody
    public JsonResult approvePutOnRecordComplaint(String json, User user){
        JSONObject jsonObject = JSONObject.parseObject(json);
        Integer processId = jsonObject.getInteger("processId");
        ComplaintBase complaintBase = new ComplaintBase();
        complaintBase.setId(jsonObject.getInteger("baseId"));
        String state = jsonObject.getString("state");
        complaintBase.setState(state);
        if ("3".equals(state)) {
            complaintBase.setProcessor(jsonObject.getInteger("processer"));
        } else {
            String registerRejectReason = jsonObject.getString("registerRejectReason");
            if(!"".equals(registerRejectReason )){
                complaintBase.setRegisterRejectReason(registerRejectReason);
            }
        }
        complaintService.userCentreApproveComplaint(user, complaintBase, processId);
        return new JsonResult(1,"成功");
    }

    //申请结案
    @RequestMapping("/applyFinishComplaint.do")
    @MessageMapping("/applyFinishComplaint")
    @ResponseBody
    public JsonResult applyFinishComplaint(String json, User user){
        JSONObject jsonObject = JSONObject.parseObject(json);
        Integer processId = jsonObject.getInteger("processId");
        ComplaintBase complaintBase = new ComplaintBase();
        complaintBase.setId(jsonObject.getInteger("baseId"));
        complaintBase.setSettleType(jsonObject.getString("settleType"));
        String settleOpinion = jsonObject.getString("settleOpinion");
        if (!"".equals(settleOpinion)) {
            complaintBase.setSettleOpinion(settleOpinion);
        }
        String role = jsonObject.getString("role");
        complaintService.applyFinishComplaintByRole(user, complaintBase, role, processId);
        return new JsonResult(1, "成功");
    }

    //核心人物审批结案
    @RequestMapping("/approveComploaintEnd.do")
    @MessageMapping("/approveComploaintEnd")
    @ResponseBody
    public JsonResult approveComploaintEnd(String json, User user){
        JSONObject jsonObject = JSONObject.parseObject(json);
        Integer processId = jsonObject.getInteger("processId");
        ComplaintBase complaintBase = new ComplaintBase();
        complaintBase.setId(jsonObject.getInteger("baseId"));
        complaintBase.setState(jsonObject.getString("state"));
        String settleRejectReason = jsonObject.getString("settleRejectReason");
        if (!"".equals(settleRejectReason)) {
            complaintBase.setSettleRejectReason(settleRejectReason);
        }
        complaintService.userCentreFinishComplaint(user,complaintBase,processId);
        return new JsonResult(1, "成功");
    }

    //核心人物停权立案者
    @RequestMapping("/terminationAuthority.do")
    @MessageMapping("/terminationAuthority")
    @ResponseBody
    public JsonResult terminationAuthority(String json, User user){
        JSONObject jsonObject = JSONObject.parseObject(json);
        Integer userID = user.getUserID();
        Integer baseId = jsonObject.getInteger("baseId");
        complaintService.userCentreStoppedPermission(userID, baseId);
        return new JsonResult(1,"成功");
    }

    //核心人物更换处理者
    @RequestMapping("/changeComplaintHandler.do")
    @ResponseBody
    public JsonResult changeComplaintHandler(ComplaintBase complaintBase){
        complaintBase.setUpdateTime(new Date());
        complaintService.userCentreChangeHandler(complaintBase);
        return new JsonResult(1,"成功");
    }

    //查看处理者更换记录
    @RequestMapping("/getChangeComplaintHandler.do")
    @ResponseBody
    public JsonResult getChangeComplaintHandler(Integer baseId){
        List<ProductMessageByComplaint> list = complaintService.listConplaintBaseHistory(baseId);
        HashMap<String, Object> map = new HashMap<>();
        map.put("lsitProcessor", list);
        return new JsonResult(1, map);
    }

    //查看联系人
    @RequestMapping("/getConmplaintContact.do")
    @ResponseBody
    public JsonResult getConmplaintContact(Integer baseId){
        List<ComplaintContact> list = complaintService.listComplaintContact(baseId);
        HashMap<String, Object> map = new HashMap<>();
        map.put("lsitContact", list);
        return new JsonResult(1, map);
    }

    //新增客户联系人
    @RequestMapping("/insertComplaintContact.do")
    @ResponseBody
    public JsonResult insertComplaintContact(User user, ComplaintContact complaintContact){
        ComplaintContact contact = new ComplaintContact();
        if (!("".equals(complaintContact.getContactName())) & complaintContact.getContactName() != null) {
            contact.setContactName(complaintContact.getContactName());
        }
        if (!("".equals(complaintContact.getContact())) & complaintContact.getContact() != null) {
            contact.setContact(complaintContact.getContact());
        }
        if (!("".equals(complaintContact.getContactPhone())) & complaintContact.getContactPhone() != null) {
            contact.setContactPhone(complaintContact.getContactPhone());
        }
        if (!("".equals(complaintContact.getPost())) & complaintContact.getPost() != null) {
            contact.setPost(complaintContact.getPost());
        }
        contact.setComplaintBase(complaintContact.getComplaintBase());
        contact.setCreateName(user.getUserName());
        contact.setCreator(user.getUserID());
        contact.setCreateTime(new Date());
        contact.setVersionNo(0);
        ComplaintContact complaintContactNew = complaintService.addCustomerContact(contact);
        HashMap<String, Object> map = new HashMap<>();
        map.put("complaintContact", complaintContactNew);
        return new JsonResult(1,map);
    }

    //修改客户联系人
    @RequestMapping("/updateComplaintContact.do")
    @ResponseBody
    public JsonResult updateComplaintContact(User user, ComplaintContact complaintContact){
        ComplaintContact contact = complaintService.upContact(user, complaintContact);
        HashMap<String, Object> map = new HashMap<>();
        map.put("complaintContact", contact);
        return new JsonResult(1,map);
    }

    //查看联系人修改记录
    @RequestMapping("/getComplaintContactHistory.do")
    @ResponseBody
    public JsonResult getComplaintContactHistory(Integer contanctId){
        List<ComplaintContactHistory> list = complaintService.listContactHistory(contanctId);
        HashMap<String, Object> map = new HashMap<>();
        map.put("lsitContactHistory", list);
        return new JsonResult(1, map);
    }

    //补充材料接口
    @RequestMapping("/saveSupplement.do")
    @ResponseBody
    public JsonResult saveSupplement(User user, ComplaintSupplement complaintSupplement, String module){
        complaintSupplement = complaintService.addSupplement(complaintSupplement,user);
        String listAttachment = complaintSupplement.getListAttachment();
        complaintAttchmentService.addComplaintAttachment(listAttachment,complaintSupplement.getComplaint(),0,complaintSupplement.getId(),user,complaintSupplement.getCreateTime(), complaintSupplement,module);
        complaintService.sendComplaintMessage(complaintSupplement.getComplaint(), "8"); //给查看当前投诉详情的人推送消息，提示进行了补充材料。
        return new JsonResult(1,"成功");
    }

    //投诉详情接口
    @RequestMapping("/complaintBaseMessage.do")
    @ResponseBody
    public void complaintBaseMessage(Integer baseId,HttpServletResponse response) throws Exception {
        HashMap<String, Object> map = complaintService.getComplaintBase(baseId);
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    //获取可选人员
    @ResponseBody
    @RequestMapping("/getCoreSettingUserListByComplaint.do")
    public JsonResult getCoreSettingUserListByComplaint(User user){
        List<UserDto>  list=complaintAttchmentService.getCoreSettingUserListByComplaint(user.getOid());
        JsonResult result=new JsonResult(1,list);
        return result;
    }

    /**
     *app获取立案者、处理者、 查看着信息列表
     * coreCode filing-立案者 handle-处理者 see-查看者
     */
    @ResponseBody
    @RequestMapping("/getThreeUserByComplaintApp.do")
    public JsonResult getThreeUser(String coreCode,User user) throws IOException {
        Map<String,Object> map = new HashMap<>();
        if (coreCode!=null && !"".equals(coreCode)){
            List<User> users = userService.getUserListByCoreCodeLocking(user.getOid(),coreCode);
            List<MessageUserDto> codeUsers = new ArrayList<>();
            for (User u : users) {
                MessageUserDto mu = new MessageUserDto();
                mu.setUserID(u.getUserID());
                mu.setUserName(u.getUserName());
                mu.setDepartName(u.getDepartName());
                mu.setCreateTime(u.getCreateTime());
                mu.setGender(u.getGender());
                mu.setImgPath(u.getImgPath());
                mu.setIsDuty(u.getIsDuty());
                mu.setMobile(u.getMobile());
                mu.setRoleCode(u.getRoleCode());
                codeUsers.add(mu);
            }
            map.put("codeUsers",codeUsers);
            map.put("status",1);  //成功
        }else {
            map.put("status",0);  //失败
        }
        return new JsonResult(1,map);
    }

    //查看当前结构的核心人物
    @ResponseBody
    @RequestMapping("/getCoreUserForApp.do")
    public JsonResult getCoreUserForApp(User user){
        User userCore=userService.getUserByCoreCode(user.getOid(),"core");
        Map<String,Object> map=new HashMap<String,Object>();
        if (userCore!=null) {
            map.put("status",1);
            map.put("coreUser",userCore);
        }else {
            map.put("status",0);
        }
        return new JsonResult(1, map);
    }

    /**
     *1.136公司总览之投诉
     */

    //总览中查询正在处理的投诉
    @RequestMapping("/unfinishedComplaintBase.do")
    @ResponseBody
    public JsonResult unfinishedComplaintBase(User user, String type, Integer customer, String year, String month, PageInfo pageInfo, String state){
        HashMap map = complaintService.listOverviewCompleteBase(user.getOid(),type,customer,year,month,pageInfo,state,"");
        return new JsonResult(1,map);
    }

    //总览中查询已结案的投诉
    @RequestMapping("/finishedComplaintBase.do")
    @ResponseBody
    public JsonResult finishedComplaintBase(User user, String type, Integer customer, String year, String month, PageInfo pageInfo, String settleType){
        HashMap map = complaintService.listOverviewCompleteBase(user.getOid(),type,customer,year,month,pageInfo,"6",settleType);
        return new JsonResult(1,map);
    }

    /**
     * 1.189 根据客户获取三种状态的案子
     * @param user
     * @param customer
     * @return
     */
    @ResponseBody
    @RequestMapping("/unFinishedComplaintByCustomer.do")
    public JsonResult unFinishedComplaintByCustomer(User user, Integer customer){
        HashMap<String, Object> map = new HashMap<>();
        List<ComplaintBase> listApproveCom = complaintService.overviewComplaintByCustomerAndState(user,"1",customer);      //立案待审批
        List<ComplaintBase> listPendingComEnd = complaintService.overviewComplaintByCustomerAndState(user,"3",customer);   //待结案
        List<ComplaintBase> listApproveComEnd = complaintService.overviewComplaintByCustomerAndState(user,"4",customer);   //结案待审批
        map.put("listApproveComplaint", listApproveCom);
        map.put("listPendingComplaintEnd", listPendingComEnd);
        map.put("listApproveComplaintEnd",listApproveComEnd);
        return new JsonResult(1,map);
    }

    /**
     * 1.190获取公司某个月的投诉情况
     * @param user
     * @return
     */
    @ResponseBody
    @RequestMapping("/complaintByOrg.do")
    public JsonResult complaintByOrg(User user, Integer peroid, PageInfo pageInfo){
        HashMap<String, Object> map= complaintService.getAllComplaintByOid(user,peroid,pageInfo);
        return new JsonResult(1,map);
    }


    //查看投诉详情的跳转页面
    @RequestMapping("/complaintInfo.do")
    public String complaintInfo(User user, Model model){
        model.addAttribute("user",user);
        return "/complaint/complaintInfo";
    }

    @RequestMapping("/companyOverviewComplaint.do")
    public String companyOverviewComplaint(User user, Model model){
        model.addAttribute("user",user);
        Date complaintBaseCteateDate = complaintService.getFirstComplaintBaseDate(user.getOid());
        model.addAttribute("orgCreateDate", complaintBaseCteateDate.getTime());
        Date nowDate = new Date();
        model.addAttribute("nowDate", nowDate.getTime());
        return "/generalCatalogue/complaint";
    }



}
