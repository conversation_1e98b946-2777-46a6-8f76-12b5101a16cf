package cn.sphd.miners.modules.system.service;

import cn.sphd.miners.modules.system.entity.RolePopedom;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.entity.UserRole;

import java.util.List;

/**
 * Created by Administrator on 2018/1/30.
 */
public interface UserRoleService {

    UserRole getUserRoleByUserIdRoleCode(Integer userId,String code);

    void deleteUserRoleByUserIdRoleCode(Integer userId,String code);

    void saveUserRole(Integer userId,String code ,User loginUser);

    String getUserRoleCodeByType(Integer type);

    List<String> getRolePopedomList();
}
