package cn.sphd.miners.modules.accountant.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.accountant.dao.AccountantSettleDao;
import cn.sphd.miners.modules.accountant.entity.TAccountantSettleEntity;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

/**
 * Created by root on 17-2-8.
 */
@Repository
public class AccountantSettleDaoImpl extends BaseDao<TAccountantSettleEntity,Serializable> implements AccountantSettleDao{
}
