<template>
  <div class="demoPage">
    <div>
      <TyDemo firstName="一级菜单" secondName="二级菜单" />

      <table class="ty-table ty-table-control">
        <tr>
          <td>功能</td>
          <td>功能描述</td>
          <td>操作</td>
        </tr>
        <tr v-for="(item, index) in list" :key="index">
          <td v-html="item.name"></td>
          <td>{{ item.fullName }} </td>
          <td>
            <span class="ty-color-blue" @click="delBtn(item)">删除</span>
          </td>
        </tr>
      </table>

      <TyPage v-if="pageShow"
              :curPage="pageInfo.currentPageNo" :pageSize="pageInfo.pageSize"
              :allPage="pageInfo.totalPage" :pageClickFun="pageClick"></TyPage>

    </div>


    <!--  弹窗的高低顺序 是按照代码的前后顺序   -->
    <TyDialog v-if="delVisible" width="800" dialogTitle="个人信息" color="green" :dialogHide="hideFun">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun">取消</el-button>
        <el-button class="bounce-ok" @click="tipOk">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center">
          当前的数据：{{ editObj.name }} - {{ editObj.fullName }} <br/>
          确定需要删除吗？
        </div>
      </template>
    </TyDialog>

    <!--  svg图标使用 上传svg图标 到src/icons/svg 中，文件名字 组件 name 保持一致 （主页svg文件位置和命名）   -->
    <!--  示例 文件位置  src/icons/svg/message.svg  -->
    <SvgIcon name="message"/>

  </div>
</template>
<script>
import demoMixin from'@/mixins/demoMixin.js'
import { beforeRouteLeave, initNav } from "@/utils/routeChange"
import { getDemoList } from "@/api/api.js"
import TyDemo from "@/components/TyDemo.vue"

export default {
  mixins: [demoMixin],
  data() {
    return {
      pageName:'demoPage',
      pageShow: true,
      pageInfo:{ 'currentPageNo': 3, 'pageSize': 10 , 'totalPage': 15  },
      list: [],
      editObj: null ,
      delVisible: null ,
    }
  },
  components: {
    TyDemo, // 需要在 子组件 里注明 当前页面 引用了此组件
  },
  beforeRouteLeave(to, from, next) {
      beforeRouteLeave(to, from, next,this)
  },
  created(){
    // 页面初始化方法，写在 created 里或者 mounted 里
    // 用于初始化页面数据
    // params：
    // ------ menuInfo : {mid: '菜单的mid', name: 'tab栏显示的名称', pageName: '一般与data里的pageName一致，最好是vue的名字，见名知意'}
    // ------ loadDataFun : 所有的首次进入需要初始化的数据都写在这个方法里
    // ------ scopeThis : 当前作用域
    initNav({mid: 'demo', name: '实例页面', pageName: this.pageName}, this.getList, this)


  },
  mounted() {

  },
  destroyed () {

  },
  methods: {
    pageClick(pageItem){
      // pageItem 参考值如下：
      // pageItem = { 'name': '尾页', 'page': 30 , 'klass':'ye', 'type':'last' }
      // pageItem = { 'name': i, 'page': 3 , 'klass':'yecur', 'type':'curPage' }
      // pageItem = { 'name': i, 'page': 4 , 'klass':'ye', 'type':'normal' }
      // pageItem = { 'name': '首页', 'page':1, 'klass':'ye', 'type':'first' }
      let data = {
        pageSize: 20 ,
        currentPageNo: pageItem.page
      }
      this.getList(data)

    },
    getList(){
      let params = { 'currentPageNo': 1 , 'pageSize': 20 , 'totalPage': 15  }
      this.getDemoListFun(params)
    },
    getDemoListFun(params){
      getDemoList(params).then(res => {
        res = [
          { name:'张三1', num: 12, fullName: '我就是张三11'  } ,
          { name:'张三2', num: 12, fullName: '我就是张三22'  } ,
          { name:'张三3', num: 12, fullName: '我就是张三33'  } ,
          { name:'张三4', num: 12, fullName: '我就是张三444'  } ,
          { name:'张三5', num: 12, fullName: '我就是张三555'  }
        ]
        this.list = res
        this.pageInfo = { 'currentPageNo': params.currentPageNo , 'pageSize': 20 , 'totalPage': 15  }
      }).catch(err => {
        console.log('err=', err)
      })
    },
    delBtn(item){
      this.editObj = item
      this.delVisible = true
    },
    hideFun(){
      this.delVisible = false
    },
    tipOk(){
      this.delVisible = false
    },

  }
}

</script>

<style lang="scss" scoped>
.demoPage{
  padding: 50px;

}

</style>
