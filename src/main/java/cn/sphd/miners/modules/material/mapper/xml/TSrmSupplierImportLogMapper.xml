<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sphd.miners.modules.material.mapper.TSrmSupplierImportLogMapper">
    
    <resultMap type="TSrmSupplierImportLog" id="TSrmSupplierImportLogResult">
        <result property="id"    column="id"    />
        <result property="org"    column="org"    />
        <result property="supplierCount"    column="supplier_count"    />
        <result property="supplierSuccess"    column="supplier_success"    />
        <result property="uploadFile"    column="upload_file"    />
        <result property="state"    column="state"    />
        <result property="memo"    column="memo"    />
        <result property="creator"    column="creator"    />
        <result property="createName"    column="create_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="updator"    column="updator"    />
        <result property="updateName"    column="update_name"    />
        <result property="updateTime"    column="update_time"    />
        <result property="operation"    column="operation"    />
        <result property="previousId"    column="previous_id"    />
        <result property="versionNo"    column="version_no"    />
    </resultMap>

    <sql id="selectTSrmSupplierImportLogVo">
        select id, org, supplier_count, supplier_success, upload_file, state, memo, creator, create_name, create_time, updator, update_name, update_time, operation, previous_id, version_no from t_srm_supplier_import_log
    </sql>

    <select id="selectTSrmSupplierImportLogList" parameterType="TSrmSupplierImportLog" resultMap="TSrmSupplierImportLogResult">
        <include refid="selectTSrmSupplierImportLogVo"/>
        <where>  
            <if test="org != null "> and org = #{org}</if>
            <if test="supplierCount != null "> and supplier_count = #{supplierCount}</if>
            <if test="supplierSuccess != null "> and supplier_success = #{supplierSuccess}</if>
            <if test="uploadFile != null  and uploadFile != ''"> and upload_file = #{uploadFile}</if>
            <if test="state != null "> and state = #{state}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
            <if test="creator != null "> and creator = #{creator}</if>
            <if test="createName != null  and createName != ''"> and create_name like concat('%', #{createName}, '%')</if>
            <if test="updator != null "> and updator = #{updator}</if>
            <if test="updateName != null  and updateName != ''"> and update_name like concat('%', #{updateName}, '%')</if>
            <if test="operation != null  and operation != ''"> and operation = #{operation}</if>
            <if test="previousId != null "> and previous_id = #{previousId}</if>
            <if test="versionNo != null "> and version_no = #{versionNo}</if>
        </where>
    </select>
    
    <select id="selectTSrmSupplierImportLogById" parameterType="Long" resultMap="TSrmSupplierImportLogResult">
        <include refid="selectTSrmSupplierImportLogVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTSrmSupplierImportLog" parameterType="TSrmSupplierImportLog" useGeneratedKeys="true" keyProperty="id">
        insert into t_srm_supplier_import_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="org != null">org,</if>
            <if test="supplierCount != null">supplier_count,</if>
            <if test="supplierSuccess != null">supplier_success,</if>
            <if test="uploadFile != null">upload_file,</if>
            <if test="state != null">state,</if>
            <if test="memo != null">memo,</if>
            <if test="creator != null">creator,</if>
            <if test="createName != null">create_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updator != null">updator,</if>
            <if test="updateName != null">update_name,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="operation != null">operation,</if>
            <if test="previousId != null">previous_id,</if>
            <if test="versionNo != null">version_no,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="org != null">#{org},</if>
            <if test="supplierCount != null">#{supplierCount},</if>
            <if test="supplierSuccess != null">#{supplierSuccess},</if>
            <if test="uploadFile != null">#{uploadFile},</if>
            <if test="state != null">#{state},</if>
            <if test="memo != null">#{memo},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createName != null">#{createName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updator != null">#{updator},</if>
            <if test="updateName != null">#{updateName},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="operation != null">#{operation},</if>
            <if test="previousId != null">#{previousId},</if>
            <if test="versionNo != null">#{versionNo},</if>
         </trim>
    </insert>

    <update id="updateTSrmSupplierImportLog" parameterType="TSrmSupplierImportLog">
        update t_srm_supplier_import_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="org != null">org = #{org},</if>
            <if test="supplierCount != null">supplier_count = #{supplierCount},</if>
            <if test="supplierSuccess != null">supplier_success = #{supplierSuccess},</if>
            <if test="uploadFile != null">upload_file = #{uploadFile},</if>
            <if test="state != null">state = #{state},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="createName != null">create_name = #{createName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updator != null">updator = #{updator},</if>
            <if test="updateName != null">update_name = #{updateName},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="operation != null">operation = #{operation},</if>
            <if test="previousId != null">previous_id = #{previousId},</if>
            <if test="versionNo != null">version_no = #{versionNo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTSrmSupplierImportLogById" parameterType="Long">
        delete from t_srm_supplier_import_log where id = #{id}
    </delete>

    <delete id="deleteTSrmSupplierImportLogByIds" parameterType="String">
        delete from t_srm_supplier_import_log where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>