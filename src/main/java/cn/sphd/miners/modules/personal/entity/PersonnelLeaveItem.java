package cn.sphd.miners.modules.personal.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created by Administrator on 2018/3/28.
 */
@Entity
@Table(name = "t_personnel_leave_item")
public class PersonnelLeaveItem implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "leave_id",nullable=true , unique=false, insertable=false, updatable=false)
    private Integer leaveId;

    @Column(name = "actual_state",length = 1)
    private String actualState;//实际请假状态:0-按计划完成,1-提前消假

    @Column(name = "actual_type",length = 1)
    private String actualType;//请假类型:1-事假,2-病假,3-年假,4-调休,5-婚假,6-产假,7-陪产假,8-路途假,9-其他

    @Column(name = "actual_duration")
    private Double actualDuration;//实际时长

    @Column(name = "actual_begin_time")
    private Date actualBeginTime;//实际起始时间

    @Column(name = "actual_end_time")
    private Date actualEndTime;//实际截止时间(计划上班时间)

    @Column(name = "actual_reason",length = 255)
    private String actualReason;//'实际原因'(提前结束请假的说明)

    @Column(name = "approve_duration")
    private Double approveDuration;//批准时长

    @Column(name = "approve_explain",length = 255)
    private String approveExplain;//批准说明

    @Column(name = "leader")
    private Integer leader;//领导ID

    @Column(name = "comment",length = 255)
    private String comment;//审批意见

    @Column(name = "approval_days")
    private Integer approvalDays;//批准天数

    @Column(name = "state",length = 1)
    private String state;//0-申请，1-通过，2-否决

    @Column(name = "creator")
    private Integer creator;//创建人id

    @Column(name = "create_name",length = 100)
    private String createName;//创建人

    @Column(name = "create_date")
    private Date createDate;//创建时间

    @Column(name = "updator")
    private Integer updator;//修改人id

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;//修改人

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;//修改时间

    @Column(name="approve_Item"   , nullable=true , unique=false)
    private Integer approveItem;//申批项目

    @Column(name="approve_status"  , length=1 , nullable=true , unique=false)
    private String approveStatus;//审批状态：0-未申请 ，1-申请提交，2-通过审核，3-否决审核

    @Column(name="approve_level"   , nullable=true , unique=false)
    private Integer approveLevel;//审批次级

    @Column(name="auditor"   , nullable=true , unique=false)
    private Integer auditor;//审批者ID

    @Column(name="auditor_name"  , length=100 , nullable=true , unique=false)
    private String auditorName;//审批者

    @Column(name="audit_date"   , nullable=true , unique=false)
    private Date auditDate;//最后的审批日期

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;//操作：1-增，2—删，3-—改

    @Column(name="apply_memo"  , length=255 , nullable=true , unique=false)
    private String applyMemo;//申请备注(驳回原因)

    @Column(name="approve_memo"  , length=255 , nullable=true , unique=false)
    private String approveMemo;//审批备注

    @Column(name="message_id"   , nullable=true , unique=false)
    private Integer messageId;//对应消息表的id

    @ManyToOne(fetch= FetchType.EAGER )
    @JsonIgnore@JSONField(serialize = false)
    @JoinColumn(name="leave_id", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private PersonnelLeave leave;//请假主表关联

    @Transient
    private List<ApprovalProcess> processList;//存请假结束的审批流程

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getLeaveId() {
        return leaveId;
    }

    public void setLeaveId(Integer leaveId) {
        this.leaveId = leaveId;
    }

    public String getActualState() {
        return actualState;
    }

    public void setActualState(String actualState) {
        this.actualState = actualState;
    }

    public String getActualType() {
        return actualType;
    }

    public void setActualType(String actualType) {
        this.actualType = actualType;
    }



    public Date getActualBeginTime() {
        return actualBeginTime;
    }

    public void setActualBeginTime(Date actualBeginTime) {
        this.actualBeginTime = actualBeginTime;
    }

    public Date getActualEndTime() {
        return actualEndTime;
    }

    public void setActualEndTime(Date actualEndTime) {
        this.actualEndTime = actualEndTime;
    }

    public String getActualReason() {
        return actualReason;
    }

    public void setActualReason(String actualReason) {
        this.actualReason = actualReason;
    }

    public Double getActualDuration() {
        return actualDuration;
    }

    public void setActualDuration(Double actualDuration) {
        this.actualDuration = actualDuration;
    }

    public Double getApproveDuration() {
        return approveDuration;
    }

    public void setApproveDuration(Double approveDuration) {
        this.approveDuration = approveDuration;
    }

    public String getApproveExplain() {
        return approveExplain;
    }

    public void setApproveExplain(String approveExplain) {
        this.approveExplain = approveExplain;
    }

    public Integer getLeader() {
        return leader;
    }

    public void setLeader(Integer leader) {
        this.leader = leader;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Integer getApprovalDays() {
        return approvalDays;
    }

    public void setApprovalDays(Integer approvalDays) {
        this.approvalDays = approvalDays;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    public PersonnelLeave getLeave() {
        return leave;
    }

    public void setLeave(PersonnelLeave leave) {
        this.leave = leave;
    }

    public List<ApprovalProcess> getProcessList() {
        return processList;
    }

    public void setProcessList(List<ApprovalProcess> processList) {
        this.processList = processList;
    }
}
