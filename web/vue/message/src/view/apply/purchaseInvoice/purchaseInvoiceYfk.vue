<template>
  <div id="purchaseInvoiceYfk">
    <TY_NavTop :title="title" ></TY_NavTop>
    <div class="container" v-loading.fullscreeen.lock="loading" v-if="details2.poOrdersPrepayment">
      <div class="rejectPng" v-if="isReject">
        <img :src="rejectPng">
      </div>
      <div class="con1">
        <div class="oInfo" v-if="details2.poOrdersPrepayment">
          <p style="margin-left: -21px;">
            计划付款金额&nbsp;{{details2.poOrdersPrepayment.planAmount && details2.poOrdersPrepayment.planAmount.toFixed(2)}}元
          </p>
          <p>
            <span style="margin-left: -21px;">
              计划付款时间&nbsp;{{details2.poOrdersPrepayment.planDate | formatDay('YYYY-MM-DD')}}
            </span>
            <span style="margin-left: 16px;">计划付款方式&nbsp;{{isture}}</span>
          </p>
        </div>
        <p class="txt_right" v-if="details2.poOrders">
          申请人:{{details2.poOrders.createName}}{{details2.poOrders.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}
        </p>
      </div>
      <div class="panel-content" style="margin-bottom: 12px">
        <div>
          <table style="width: 100%">
            <tr>
              <td>供应商:{{details2.srmSupplier.fullName}}</td>
              <td style="text-align: right;">{{details2.srmSupplier.codeName}}</td>
            </tr>
            <tr>
              <td>订单总额:{{details2.poOrders.amount && details2.poOrders.amount.toFixed(2)}}元</td>
              <td style="text-align: right;">订单号:{{details2.poOrders.sn}}</td>
            </tr>
            <tr>
              <td>订单日期:{{details2.poOrders.createDate | formatDay('YYYY-MM-DD')}}</td>
              <td></td>
            </tr>
          </table>
        </div>
        <div class="panel-content">
          <div class="text-right">
            <el-link style="color: #409eff;margin-right: 20px;" @click="orderProgressRade()">订单查看</el-link>
            <el-link v-on:click="toggle = -toggle">审批记录<i :class="toggle === -1?'el-icon-arrow-down':'el-icon-arrow-up'"></i></el-link>
          </div>
          <div v-show="toggle > 0">
            <div class="item-content">
              <div class="processItem" v-if="details2.poOrders">
                <div>提交报销申请</div>
                <div>申请人</div>
                <div>{{details2.poOrders.createName | stringSplit(4)}} {{details2.poOrders.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
              <div v-for="it in details2.approvalProcessList" v-bind:key="it.id">
                <div v-if="it.approveStatus === '2' && it.businessType == 23" class="processItem">
                  <div>采购审批通过</div>
                  <div>审批人</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 63" class="processItem">
                  <div>付款审批通过</div>
                  <div>审批人</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 64" class="processItem">
                  <div>付款方式确认</div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 65" class="processItem">
                  <div>付款复核通过</div>
                  <div>审批人</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 66" class="processItem">
                  <div v-if="details2.financePayment.method === '1'">现金付讫</div>
                  <div v-if="details2.financePayment.method === '3'">报销款已转支票</div>
                  <div v-if="details2.financePayment.method === '4'">报销款已转承兑汇票</div>
                  <div v-if="details2.financePayment.method === '5'">报销款已转账</div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 67" class="processItem">
                  <div>付款方式修改</div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <!-- 最后一条审批记录展示 -->
              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType ===46">
                <div style="margin-top:15px;"> 票据在线审核未通过！</div>
                <div><span style="float: left">理由：</span> <div style="margin-left: 50px;white-space: pre-wrap;">{{ nowApproveItem.reason }}</div></div>
                <br>
                <div class="processItem">
                  <div></div>
                  <div>审批人</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType=== 45">
                <div style="margin-top:15px;">报销申请被驳回！</div>
                <div>驳回理由： {{nowApproveItem.reason || ''}}</div>
                <div class="processItem">
                  <div></div>
                  <div>审批人</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType=== 47">
                <div style="margin-top:15px;">  线下审核未通过！</div>
                <div>驳回理由: 实际票据与在线审核通过的不一致。</div>
                <div class="processItem">
                  <div></div>
                  <div>出纳员</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-blue" v-if="nowApproveItem.approveStatus">
                <span v-if="nowApproveItem.approveStatus === '1' && nowApproveItem.userName !== 0 && nowApproveItem.userName !== null">{{nowApproveItem.userName}}为下一个审批人</span>
                <span v-if="nowApproveItem.approveStatus == '2'">本次报销已完结!</span>
              </div>
            </div>
          </div>
        </div>
        <div class="panel-content">
          <div style="background: #fff; padding:10px 15px;">
            <span>付款事由：</span><span style="color:red">采购的预付款</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import rejectPng from "../../../images/reject.png";

export default {
  name: "purchaseInvoiceYfk",
  data(){
    return{
      title:'采购的预付款',
      isReject:false,
      rejectPng: rejectPng,
      details2:{
        srmSupplier:{},
        poOrdersPrepayment:{},
        poOrders:{},
        approvalProcessList:[]
      },
      nowApproveItem: {},
      toggle: -1,
      loading: true,
      approveStatus: ''
    }
  },
  created() {
    // 37+预付款id是预付款没有付款方式的；
    // 38+付款方式id是预付款有付款方式的
    let that = this;
    let parammesid = this.$route.params.id;
    const code = parammesid.substr(0,2)
    that.mesid = parammesid.substr(2,parammesid.length)
    let param = { 'orderPrepaymentId':that.mesid }
    if(code === '38'){
      param = { 'financePaymentId':that.mesid }
    }

    that.getDetail(param);
  },
  methods:{
    getDetail:function(param){
      let that = this;
      this.axios.post('../../../purchaseInvoice/advancePaymentDetail.do', param)
        .then(function(response){
          let res = response.data.data;
          that.details2 = res;
          that.loading = false;
          let ap = res.approvalProcessList || [];
          that.nowApproveItem = ap[ap.length - 1];
        })
        .catch(function(error){
          console.log(error);
        })
    },
    orderProgressRade(){
      let id = this.details2.poOrders.id;
      this.$router.push({
        path: `/purchaseInvoiceMore/${id}`
      })
    },
  },
  computed:{
    isture: function () {
      let one = Number(this.details2.poOrdersPrepayment.planMethod)
      let str = ''
      switch (one){
        case 1: str = '现金'; break;
        case 3: str = '转账支票'; break;
        case 4: str = '承兑汇票'; break;
        case 5: str = '银行转账'; break;
        case 6: str = '非公户银行转账'; break;
        default: str = `未识别-${ one }`
      }
      return str
    }
  }
}
</script>

<style  lang="less">
#purchaseInvoiceYfk{
  .processItem{
    display: flex;
    font-size: 11px;
    margin-left:-5px;
  }
  .con1{ background:#fff; margin-bottom:10px; padding:5px 15px; font-size:14px;  }
  .con1>p{text-align: right; font-size:0.8em;   }
  .oInfo{ padding:5px 20px; }
  .txt_right{ text-align: right;  }
}
</style>
