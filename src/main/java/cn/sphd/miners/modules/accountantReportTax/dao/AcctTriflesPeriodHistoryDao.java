package cn.sphd.miners.modules.accountantReportTax.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTriflesPeriodEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTriflesPeriodHistoryEntity;

import java.io.Serializable;
import java.util.List;

public interface AcctTriflesPeriodHistoryDao extends IBaseDao<AcctTriflesPeriodHistoryEntity,Serializable> {
    List<AcctTriflesPeriodHistoryEntity> getListbytriflesId(Integer hisId);
}
