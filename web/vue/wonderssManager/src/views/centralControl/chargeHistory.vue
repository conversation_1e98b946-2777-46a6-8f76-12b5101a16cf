<template>
  <Pageheader :title="title" :showBack="true"></Pageheader>
  <div class="container_main">
    <el-row>
      <el-col :span="24">
        {{sysInfo.orgName}}
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        地址：{{sysInfo.address || '--'}}
      </el-col>
    </el-row>
    <div class="mid_title">
      <span v-if="paramsInfo.code === 'smallSuper'">全权负责人的历任负责人</span>
      <span v-if="paramsInfo.code === 'general'">总务权限的历任负责人</span>
      <span v-if="paramsInfo.code === 'finance'">财务权限的历任负责人</span>
      <span v-if="paramsInfo.code === 'sale'">销售权限的历任负责人</span>
      <span v-if="paramsInfo.code === 'accounting'">会计事务的历任负责人</span>
    </div>
    <div class="item_history" v-for="(item, index) in rolePrincipalHistories">
      <el-row>
        <el-col :span="24">
          <div class="item_history_title">{{item.newUserName || '--'}}  {{item.newUserMobile || '--'}}</div>
          上任的操作记录 {{item.createName || ''}} {{$filter.format(item.createDate)}} <br>
          卸任的操作记录 {{item.updateName || ''}} {{$filter.format(item.updateDate)}}
        </el-col>
      </el-row>
    </div>
  </div>

</template>
<script>
import * as api from "@/api/centralControl";
import auth from '@/sys/auth'
export default {
  data() {
    return {
      title: '',
      paramsInfo: {
        userID: 0,
        userName: '',
        mobile: '',
        agentType: 0,
        isHasCharge: false,
        manageName: '',
        code: ''
      },
      sysInfo: {
        orgName: '',
        orgAddress: '--'
      },
      rolePrincipalHistories: []
    }
  },
  created() {
    this.title = auth.getUser().roleCode === 'super'?'核心人物管控':'高管管理'
    this.paramsInfo = this.$store.getters.getManageParam
    this.paramsInfo.code = this.$store.getters.getManageCode
    api.getChargeHistory(this.paramsInfo.userID)
        .then(res => {
          let data = res.data.data
          let manageInfo = data.manageInfo
          this.sysInfo.orgName = data.orgName
          this.sysInfo.orgAddress = data.orgAddress || ''
          this.rolePrincipalHistories = data.rolePrincipalHistories
        })
  },
  methods: {
    jump: function (path, query) {
      this.$router.push({
        path: path
      })
    },
    coreControl: function () {
      let manageState = this.sysInfo.manageState
      switch (manageState) {
        case 0:
          // 0 正式启用前，且没有设置临时管理员
          this.jump('/coreInput/set')
          break;
        case 1:
          // 1- 正式启用前， 设置了临时管理员，但未导入完成 跳转至查看临时管理员页面
          this.jump('/coreRole/set')
          break;
        case 2:
          // 2- 正式启用前， 导入完成 进入高管管理页面 或者 启用前
          this.jump('/coreManage/set')
          break;
        case 3:
          // 2- 正式启用后
          this.jump('/coreManage/see')
          break;
        case 4:
          // 4- 批量导入过程中
          this.jump('/coreRole/reset')
          break
      }
    }
  }
}

</script>

<style lang="scss" scoped>
.mid_title{
  margin-left: -16px;
  margin-right: -16px;
  text-align: center;
  background: #f2f2f2;
  padding: 12px 16px;
}
.item_history{
  padding: 12px 16px;
  font-size: 13px;
  margin-left: -16px;
  margin-right: -16px;
  color: #555;
  &:nth-child(odd){
    background: #fff;
  }
  &:nth-child(even){
    background: #fafafa;
  }
  .item_history_title{
    font-size: 14px;
    color: #333;
    margin-bottom: 4px;
  }
}
.el-row {
  margin-bottom: 16px;
}
.el-row:last-child {
  margin-bottom: 0;
}
</style>
