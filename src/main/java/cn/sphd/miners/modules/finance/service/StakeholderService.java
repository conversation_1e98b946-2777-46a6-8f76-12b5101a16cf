package cn.sphd.miners.modules.finance.service;

import cn.sphd.miners.modules.finance.entity.FinanceStakeholder;
import cn.sphd.miners.modules.finance.entity.FinanceStakeholderHistory;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;

/*
 *Created by lyx on 2021/11/19.
 */
public interface StakeholderService {
    List<FinanceStakeholder> getStakeholderList(Integer oid,Integer enabledType,String name);

    FinanceStakeholder addStakeholder(User user,String name,String remark);

    FinanceStakeholder getStakeholderById(Integer stakeholderId);

    void deactivate(User user,Integer stakeholderId,Integer enable);

    void updateStakeholder(User user,Integer stakeholderId,String name,String remark,Integer type);

    List<FinanceStakeholderHistory> getStakeholderHistory(Integer stakeholderId);
}

