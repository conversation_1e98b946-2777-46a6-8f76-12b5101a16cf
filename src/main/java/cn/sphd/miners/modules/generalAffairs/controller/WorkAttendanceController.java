package cn.sphd.miners.modules.generalAffairs.controller;

import cn.sphd.miners.common.controller.BaseController;
import cn.sphd.miners.common.initializer.AuthPassport;
import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.*;
import cn.sphd.miners.common.utils.NewDateUtils.Period;
import cn.sphd.miners.modules.generalAffairs.dto.PersonnelAttendanceMonthlyDto;
import cn.sphd.miners.modules.generalAffairs.dto.PersonnelAttendanceUserDto;
import cn.sphd.miners.modules.generalAffairs.dto.PunchDto;
import cn.sphd.miners.modules.generalAffairs.entity.*;
import cn.sphd.miners.modules.generalAffairs.service.PersonnelAttendanceMonthlyService;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceOldService;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceService;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceService.AttendanceTerminaType;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceService.ConfigAttendancePattern;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceService.PunchType;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceService.WorkdayType;
import cn.sphd.miners.modules.material.entity.RespStatus;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.entity.PersonnelLeave;
import cn.sphd.miners.modules.personal.entity.PersonnelOvertime;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.personal.service.LeaveService;
import cn.sphd.miners.modules.personal.service.OvertimeService;
import cn.sphd.miners.modules.system.dto.OrganizationDto;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgService;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSON;
import net.sf.json.JSONArray;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.IntStream;

/**
 * Created by Administrator on 2018/4/11.
 * 考勤管理
 */
@Controller
@RequestMapping("/workAttendance")
public class WorkAttendanceController extends BaseController {
    @Autowired
    WorkAttendanceService workAttendanceService;
    @Autowired
    WorkAttendanceOldService workAttendanceOldService;
    @Autowired
    OrgService orgService;
    @Autowired
    UserService userService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    LeaveService leaveService;
    @Autowired
    OvertimeService overtimeService;
    @Autowired
    PersonnelAttendanceMonthlyService personnelAttendanceMonthlyService;

    public WorkAttendanceController() {
        //关闭父类参数自动转换
        setInitBinderConfig(InitBinderConfig.DisableXSSDefence);
    }

    // 跳转总务考勤管理页面
    @RequestMapping("/attendance.do")
    public String attendance(User user, Model model){
        workAttendanceService.attendance(model,user);
//        Integer oid = user.getOid();
//        List<PersonnelAttendanceRecord> personnelAttendanceRecords = workAttendanceOldService.getPersonnelAttendanceRecordByOid(oid,"4",new Date(),null); //查询要生效的修改记录
//        for (PersonnelAttendanceRecord personnelAttendanceRecord:personnelAttendanceRecords) {
//            personnelAttendanceRecord.setOperation("3");
//            workAttendanceOldService.updatePersonnelAttendanceRecord(personnelAttendanceRecord);
//        }
//
//        Date startUsingSystemTime = workAttendanceService.getStartUsingSystemTime(oid); //获取系统开始使用时间
//        model.addAttribute("startUsingSystemTime",NewDateUtils.dateToString(startUsingSystemTime, "yyyy-MM-dd"));  //系统开始使用时间
//
//        List<PersonnelAttendanceConfig> personnelAttendanceConfigs = workAttendanceOldService.getPersonnelAttendanceConfigList(oid,null,null);
//        if (!personnelAttendanceConfigs.isEmpty()){
//            model.addAttribute("status",1);  //已进行考勤设置
//        }else {
//            model.addAttribute("status",0);  //未进行考勤设置
//        }
        return "/generalAffairs/attendance";
    }

    //我的考勤跳转页面
    @RequestMapping("/toMyAttendence.do")
    public String toMyAttendence(User user,Model model) {
        workAttendanceService.attendance(model,user);
//        //添加上考勤设置的系统开始时间和是否
//        Integer oid = user.getOid();
//        Date startUsingSystemTime = workAttendanceService.getStartUsingSystemTime(oid); //获取系统开始使用时间
//        model.addAttribute("startUsingSystemTime",NewDateUtils.dateToString(startUsingSystemTime, "yyyy-MM-dd"));  //系统开始使用时间
//
//        List<PersonnelAttendanceConfig> personnelAttendanceConfigs = workAttendanceOldService.getPersonnelAttendanceConfigList(oid, null, null);
//        if (!personnelAttendanceConfigs.isEmpty()) {
//            model.addAttribute("status", 1);  //已进行考勤设置
//        } else {
//            model.addAttribute("status", 0);  //未进行考勤设置
//        }
        return "home/myAttendence";
    }

    // 跳转总务考勤记录页面
    @RequestMapping("/attendanceLog.do")
    public String attendanceLog(){
        return "/generalAffairs/attendanceLog";
    }

    // 跳转公司总览下的职工-考勤页面
    @RequestMapping("/companyOverviewAttendance.do")
    public String companyOverviewAttendance(User user, Model model){
//        Integer oid = user.getOid();
//        List<PersonnelAttendanceRecord> personnelAttendanceRecords = workAttendanceOldService.getPersonnelAttendanceRecordByOid(oid,"4",new Date(),null); //查询要生效的修改记录
//        for (PersonnelAttendanceRecord personnelAttendanceRecord:personnelAttendanceRecords) {
//            personnelAttendanceRecord.setOperation("3");
//            workAttendanceOldService.updatePersonnelAttendanceRecord(personnelAttendanceRecord);
//        }
//
//        Date startUsingSystemTime = workAttendanceService.getStartUsingSystemTime(oid); //获取系统开始使用时间
//        model.addAttribute("startUsingSystemTime",NewDateUtils.dateToString(startUsingSystemTime, "yyyy-MM-dd"));  //系统开始使用时间
//
//        List<PersonnelAttendanceConfig> personnelAttendanceConfigs = workAttendanceOldService.getPersonnelAttendanceConfigList(oid,null,null);
//        if (personnelAttendanceConfigs.size()>0){
//            model.addAttribute("status",1);  //已进行考勤设置
//        }else {
//            model.addAttribute("status",0);  //未进行考勤设置
//        }
        workAttendanceService.attendance(model,user);
        return "/generalCatalogue/employeeAttendance";
    }

    /**
     *<AUTHOR>
     *@date 2018/4/12 14:52
     *@date 2023/9/26 1.268考勤打卡改 (修改下返回值格式，添加内容)
     *考勤时间设置(只设置考勤规则)
     * departmentIds 部门id（拼接以逗号","隔开），其中部门显示为“其他”则代表没有部门的人员，部门id传0；
     *effectDate //生效日期  attendancePattern(考勤模式:1-考勤宝,2-手工录入(默认)) lateLimit(迟到时限(分钟)) earlyLimit(早退时限(分钟))
     * leaveWork(请假到岗是否使用考勤宝:0-不使用(默认,false),1-使用(true))
     */
    @ResponseBody
    @RequestMapping("/attendanceTimeSetting.do")
    public JsonResult attendanceTimeSetting(String effectDate,Integer attendancePattern,Short lateLimit,Short earlyLimit,Boolean leaveWork,HttpServletRequest request,User user) {
        Map<String, Object> map = new HashMap<>();
        String personnelAttendanceConfigs = request.getParameter("personnelAttendanceConfigs");
        if (!"".equals(effectDate) && effectDate != null && new Date().before(NewDateUtils.dateFromString(effectDate,"yyyy-MM-dd"))){
            if (personnelAttendanceConfigs!=null && !personnelAttendanceConfigs.isEmpty()){
//                workAttendanceOldService.attendanceTimeSetting(effectDate,attendancePattern,lateLimit,earlyLimit,leaveWork,personnelAttendanceConfigs,user);
                workAttendanceService.attendanceTimeSetting(effectDate,attendancePattern,lateLimit,earlyLimit,leaveWork,personnelAttendanceConfigs,user);
                //TODO： wuyu 初始化考勤监控表。/updateAttendanceSetting.do/getAttendanceTime.do
                map.put("status", 1);  //设置成功
            }else {
                map.put("status",0); // 设置失败
            }
        }else {
            map.put("status",2); // 生效时间为今天或者是今天之前
        }
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2018/4/28 15:25
     *@date 2023/9/26 1.268考勤打卡改 (修改下返回值格式，添加内容)
     *设置作息时间(假与班)
     */
    @ResponseBody
    @RequestMapping("/setTimeTable.do")
    public JsonResult setTimeTable(User user, HttpServletRequest request) {
        Map<String,Object> map = new HashMap<>();
        String jsonArray = request.getParameter("time");
        if (jsonArray!=null) {
            JSONArray jsonArray1 = JSONArray.fromObject(jsonArray);
            List timeTable = JSONArray.toList(jsonArray1);
            map = workAttendanceService.setTimeTable(user,timeTable);
            return new JsonResult(1,map);
        }else {
            map.put("status",0); //设置失败
        }
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2018/5/3 10:19
     *删除设置的考勤规则
     */
    @ResponseBody
    @RequestMapping("/deleteSetAttendance.do")
    public void deleteSetAttendance(String[] attendanceIds,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        for (String attendanceId:attendanceIds) {
            if (!"".equals(attendanceId)){
                PersonnelAttendanceConfig personnelAttendanceConfig = workAttendanceOldService.getPersonnelAttendanceConfigById(Integer.parseInt(attendanceId));
                if (personnelAttendanceConfig!=null){
                    workAttendanceOldService.deletePersonnelAttendanceConfig(personnelAttendanceConfig);
                }
            }
        }
        map.put("status",1);//成功
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    /**
     *<AUTHOR>
     *@date 2018/4/28 16:42
     *@date 2023/9/27 1.268考勤打卡改 (修改下返回值格式，添加传参)
     *查询作息时间
     */
    @ResponseBody
    @RequestMapping("/getTimeTable.do")
    public JsonResult getTimeTable(User user,Date timeTable) {
        Map<String,Object> map = new HashMap<>();
        Integer oid = user.getOid();
        map = workAttendanceService.getTimeTable(oid,timeTable);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2018/4/16 17:54
     *@date 2023/9/27 1.268考勤打卡改 (修改下返回值格式)
     *展示系统中的所有部门（“其他”部门已拼接上）
     */
    @ResponseBody
    @RequestMapping("/getCheckDepartment.do")
    public JsonResult getCheckDepartment(Integer enabled,User user) {
        Map<String,Object> map = new HashMap<>();
        List<OrganizationDto> organizations = orgService.getOrgWithDeptTreeByOid(user.getOid(),enabled);
        map.put("organizations",organizations);//所有的部门(树形结构)
        return new JsonResult(1,map);
    }


    /**
     *<AUTHOR>
     *@date 2018/4/13 15:20
     *查询考勤时间设置
     *@date 2023/10/7 1.268考勤打卡改 (修改下返回值格式,添加作息日期月状态)
     * type 1-正常班,2-倒班
     */
    @ResponseBody
    @RequestMapping("/getAttendanceTime.do")
    public JsonResult getAttendanceTime(String type,User user) {
        JsonResult result = workAttendanceService.getAttendanceTime(user,type);
        return  result;
    }

     /**
     *@Description 获取作息日期状态
     *@auther 李娅星
     *@date 2023/10/8 1.268考勤打卡
     *@param
     */
     @ResponseBody
     @RequestMapping("/getTimeState.do")
     public JsonResult getTimeState(User user) {
         Map<String, Object> map = new HashMap<>();
         //上月
         boolean lastMonthState = workAttendanceService.hasPersonnelAttendanceExceptionByMonth(user.getOid(),NewDateUtils.changeMonth(new Date(),-1),NewDateUtils.getLastTimeOfMonth(NewDateUtils.changeMonth(new Date(),-1)));
         //判断系统中是否设置了作息时间
         if (lastMonthState){
             map.put("lastMonthState",1);  //已设置了作息时间
         }else {
             map.put("lastMonthState",0);  //未设置作息时间
         }
         //本月
         boolean thisMonthState = workAttendanceService.hasPersonnelAttendanceExceptionByMonth(user.getOid(),NewDateUtils.changeMonth(new Date(),0),NewDateUtils.getLastTimeOfMonth(NewDateUtils.changeMonth(new Date(),0)));
         if (thisMonthState){
             map.put("thisMonthState",1);  //已设置了作息时间
         }else {
             map.put("thisMonthState",0);  //未设置作息时间
         }
         //下月
         boolean nextMonthState = workAttendanceService.hasPersonnelAttendanceExceptionByMonth(user.getOid(),NewDateUtils.changeMonth(new Date(),1),NewDateUtils.getLastTimeOfMonth(NewDateUtils.changeMonth(new Date(),1)));
         if (nextMonthState){
             map.put("nextMonthState",1);  //已设置了作息时间
         }else {
             map.put("nextMonthState",0);  //未设置作息时间
         }

         return new JsonResult(1,map);
     }


    /**
     *<AUTHOR>
     *@date 20181127
     *考勤录入列表
     */
    @ResponseBody
    @RequestMapping("/attendanceList.do")
    public JsonResult attendanceList(PageInfo pageInfo,User user) {
        Map<String, Object> map = new HashMap<>();
        Integer oid = user.getOid();
//        Date today = workAttendanceOldService.getDateByWork(NewDateUtils.today(), oid);   //系统考勤的当天
        Date today = workAttendanceService.getPreWorkDate(NewDateUtils.today(), oid);   //系统考勤的当天
        Date startUsingSystemTime = workAttendanceService.getStartUsingSystemTime(oid); //获取系统开始使用时间
        if (today != null && startUsingSystemTime != null) {
            if (startUsingSystemTime.getTime() <= today.getTime()) {
//                Date yesterday = workAttendanceOldService.getDateByWork(NewDateUtils.yesterday(today), oid);  //昨天的时间格式为年月日-时分秒
                Date yesterday = workAttendanceService.getPreWorkDate(NewDateUtils.yesterday(today), oid);  //昨天的时间格式为年月日-时分秒
//                Date inputTime = workAttendanceOldService.getInputTime(oid, today);
                Date inputTime = workAttendanceService.getInputTime(oid, today);
                map.put("inputTime", inputTime!=null?NewDateUtils.dateToString(inputTime, "HH:ss"):null);
                List<PersonnelAttendanceUserDto> personnelAttendanceUserDtos = workAttendanceService.getAttendanceList(oid, today, yesterday,pageInfo);
                map.put("userList", personnelAttendanceUserDtos); //今天考勤人员
                map.put("today",today);
                map.put("yesterday", yesterday);
                map.put("startUsingSystemTime", NewDateUtils.dateToString(startUsingSystemTime,"yyyy-MM-dd"));
                map.put("status", 1);  //系统中未设置作息时间
            } else {
                map.put("status", 2);  //系统中未设置作息时间
            }
        } else {
            map.put("status", 0);  //系统中未设置作息时间
        }
        return new JsonResult(1,map,pageInfo);
    }

    /**
     *<AUTHOR>
     *@date 2018/5/30 15:28
     *查看某职工的考勤详情
     */
    @ResponseBody
    @RequestMapping("/getAttendanceDetail.do")
    public void getAttendanceDetail(Integer attendanceId,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        PersonnelAttendanceUser personnelAttendanceUser = workAttendanceService.getPersonnelAttendanceUserByUserIdAndId(attendanceId,null,null,null);
        if (personnelAttendanceUser!=null){
            List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetails = workAttendanceService.getPersonnelAttendanceUserDetailByBusiness(personnelAttendanceUser.getId(),"7","4",null,null);
            List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetailLeaves = workAttendanceService.getPersonnelAttendanceUserDetailByBusiness(personnelAttendanceUser.getId(),"5","4",null,null);
            for (PersonnelAttendanceUserDetail p:personnelAttendanceUserDetailLeaves) {
                p.setLeaveTypeName(leaveService.getLeaveTypeName(p.getLeaveType(),p.getBusinessType()));
            }
            map.put("personnelAttendanceUserDetails",personnelAttendanceUserDetails);//旷工列表
            map.put("personnelAttendanceUserDetailLeaves",personnelAttendanceUserDetailLeaves);//请假列表
        }
        map.put("personnelAttendanceUser",personnelAttendanceUser);//考勤详情
        ObjectToJson.objectToJson1(map,new String[]{"personnelAttendanceUserHistoryHashSet","personnelAttendanceUserDetailHashSet","personnelAttendanceUserDetailHistoryHashSet"},response);
    }

    /**
     *<AUTHOR>
     *@date 2018/4/16 10:10
     *编写考勤录入接口
     * dtype 1-今天上班考勤 2-昨天下班考勤       date(考勤日期)
     * time旷工时间{begin，end}（json数据）
     */
    @ResponseBody
    @RequestMapping("/getAttendanceRecord.do")
    public String getAttendanceRecord(PersonnelAttendanceUser personnelAttendanceUser,Date date,Integer dtype, String time,User user) {
//        Map<String,Object> map = new HashMap<>();
        if (dtype!=null){
            workAttendanceService.addOrUpdatePersonnelAttendanceManual(personnelAttendanceUser,date,dtype,time,user);  //考勤录入的处理接口
            return JSON.toJSONString(Map.of("status", 1));
//            map.put("status",1);  //成功
        }else {
            return JSON.toJSONString(Map.of("status", 0));
//            map.put("status",0);  //失败
        }
//        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

//    /**
//     *<AUTHOR>
//     *@date 2018/4/13 15:48
//     *查询考勤管理主页接口
//     * deptId 部门id
//     */
//    @ResponseBody
//    @RequestMapping("/getAttendance.do")
//    public JsonResult getAttendance(Integer deptId,User user) throws ParseException {
//        Map<String,Object> newMap = new HashMap<>();
//        List<Map<String,Object>> userList1 = new ArrayList<>();  //考勤人员以及各种考勤人数
//
//        Integer oid = user.getOid();
//        String startUsingSystemTime = workAttendanceOldService.returnStartUsingSystemTime(oid); //获取系统开始使用时间
//        newMap.put("startUsingSystemTime",startUsingSystemTime);  //系统开始使用时间
//        Date today = NewDateUtils.today();
//        if ( startUsingSystemTime!=null && NewDateUtils.dateToString(today,"yyyy-MM-dd").compareTo(startUsingSystemTime)>=0) {  //当前时间在系统开始时间之后，可用
//            workAttendanceOldService.setDefaultWorkAttendance(oid); //系统默认添加今天的人员且默认添加考勤人员旷工
//            Date[] days = new Date[]{today, NewDateUtils.yesterday(today), NewDateUtils.changeDay(today,-2) };
//            for (int i=0; i<days.length; i++) {
//                Date currentTime = days[i];
//                if (currentTime != null && currentTime.getTime() >= NewDateUtils.dateFromString(startUsingSystemTime,"yyyy-MM-dd").getTime()) {
//                    Map<String, Object> map = new HashMap<>();
//                    //获取各种类型的考勤信息
//                    workAttendanceOldService.getAllTypeAttendance(oid,deptId,null,currentTime,map);
//                    map.put("date", currentTime);
//                    map.put("timeType", i);  //0-今天 1-昨天 2-前天
//                    userList1.add(map);
//                }
//            }
//        }
//        newMap.put("userList1",userList1);
//        return new JsonResult(1,newMap);
//
//    }
    @ResponseBody
    @RequestMapping("/getAttendance.do")
    public JsonResult getAttendance(Integer deptId,User user){
        String sdf = "yyyy-MM-dd";
        Map<String,Object> result = new HashMap<>();
        List<Map<String,Object>> attendances = new ArrayList<>();  //考勤人员以及各种考勤人数
        Date startUsingSystemTime = workAttendanceService.getStartUsingSystemTime(user.getOid());
        result.put("startUsingSystemTime",startUsingSystemTime==null?startUsingSystemTime:NewDateUtils.dateToString(startUsingSystemTime, sdf));  //系统开始使用时间
        Date today = NewDateUtils.today();
        if ( startUsingSystemTime!=null && today.after(startUsingSystemTime)) {  //当前时间在系统开始时间之后，可用
            List<Date> days = NewDateUtils.getEveryDate(NewDateUtils.changeDay(today, -2), today).stream().sorted(Collections.reverseOrder()).toList();//按今天，昨天，前天倒序排列
            IntStream.range(0, days.size())
                    .mapToObj(i -> new AbstractMap.SimpleEntry<>(i, days.get(i)))//转成map，加上索引
                    .filter(day -> !day.getValue().before(startUsingSystemTime))//考勤开始以后
                    .forEach(entry -> {
                        //获取各种类型的考勤信息
//                        Map<String, Object> map = new HashMap<>();
//                        workAttendanceOldService.getAllTypeAttendance(user.getOid(),deptId,null,entry.getValue(),map);
                        Map<String, Object> map = workAttendanceService.getAllTypeAttendance(user.getOid(),deptId,null,entry.getValue());
                        map.put("timeType", entry.getKey());  //0-今天 1-昨天 2-前天
                        map.put("date", entry.getValue());
                        attendances.add(map);
                    });
        }
        result.put("userList1",attendances);
        return new JsonResult(1,result);
    }

    /**
     *<AUTHOR>
     *@date 2018/4/24 17:34
     *获取设置考勤的部门
     */
    @ResponseBody
    @RequestMapping("/getAttendanceDepartments.do")
    public void getAttendanceDepartments(String exceptionDate,HttpServletResponse response,User user) throws IOException{
        Map<String,Object> map = new HashMap<>();
        Integer oid = user.getOid();
        List<PersonnelAttendanceDepartmentConfig> deptConfigs = new ArrayList<>();

        List<PersonnelAttendanceConfig> personnelAttendanceConfigs = workAttendanceService.getPersonnelAttendanceConfigByOpenDate(oid,1,NewDateUtils.dateFromString(exceptionDate,"yyyy-MM-dd HH:mm:ss"));
        if (!personnelAttendanceConfigs.isEmpty()){
            List<PersonnelAttendanceConfig> personnelAttendanceConfigList = workAttendanceService.getPersonnelAttendanceConfigList(oid,"1",personnelAttendanceConfigs.get(0).getOpenDate());
            for (PersonnelAttendanceConfig pp:personnelAttendanceConfigList) {
                deptConfigs.addAll(pp.getPersonnelAttendanceDepartmentConfigHashSet());
            }

            for (PersonnelAttendanceDepartmentConfig p:deptConfigs) {
                if (p.getDept()!=null){
                    if (p.getDept()!=0){
                        Organization o = orgService.getOrgByOid(p.getDept(), OrgService.OrgType.department);
                        p.setDeptName(o.getName());
                    }else {
                        p.setDeptName("其他");
                    }
                }

            }
        }
        map.put("personnelAttendanceDepartmentConfigList",deptConfigs);
        ObjectToJson.objectToJson1(map,new String[]{"rule"},response);
    }

    /**
     *<AUTHOR>
     *@date 20181102
     *考勤管理月查询明细
     * @Date 2021/3/1  1.143项目进行修改
     *  userId 可能不是登录人id
     */
    @ResponseBody
    @RequestMapping("/getAttendanceMonthly.do")
    public JsonResult getAttendanceMonthly(Integer yearMonth, Integer deptId,Integer userId,User user, PageInfo pageInfo) {
        if(yearMonth==null || yearMonth.equals(0)) { //年月未传，取服务器当前时间年月
//            yearMonth = Integer.valueOf(new SimpleDateFormat("yyyyMM").format(new Date(System.currentTimeMillis())));
            yearMonth = Integer.valueOf(NewDateUtils.dateToString(new Date(System.currentTimeMillis()),"yyyyMM"));
        }
        List<PersonnelAttendanceMonthly> result = personnelAttendanceMonthlyService.getAttendanceMonthliesOrderbyName(user.getOid(), yearMonth, deptId,null,userId,null,pageInfo);
        Date systemTime = workAttendanceService.getStartUsingSystemTime(user.getOid());  //获取系统时间
        return new JsonResult(1, result, pageInfo, systemTime);
    }

    /**
    * <AUTHOR>
    * @Description 考勤管理月查询统计
    * @Date 2020/7/14
    * @Date 2021/3/1  1.143项目进行修改
     * userId 可能不是登录人id
    */
    @ResponseBody
    @RequestMapping("/getAttendanceMonthlyStatistics.do")
    public JsonResult getAttendanceMonthlyStatistics(Integer yearMonth,Integer type, Integer deptId, Integer userId,User user, PageInfo pageInfo) throws ParseException {
//        if (type!=null&&1==type){  //我的考勤使用
//            workAttendanceService.setDefaultWorkAttendance(user.getOid()); //系统默认添加今天的人员且默认添加考勤人员旷工
//        }
        if(yearMonth==null || yearMonth.equals(0)) { //年月未传，取服务器当前时间年月
//            yearMonth = Integer.valueOf(new SimpleDateFormat("yyyyMM").format(new Date(System.currentTimeMillis())));
            yearMonth = Integer.valueOf(NewDateUtils.dateToString(new Date(System.currentTimeMillis()),"yyyyMM"));
        }
        List<PersonnelAttendanceMonthlyDto> result = personnelAttendanceMonthlyService.getAttendanceMonthlyStatistics(user.getOid(), yearMonth, deptId,null,userId, null, pageInfo);
        Date systemTime = workAttendanceService.getStartUsingSystemTime(user.getOid());  //获取系统时间
        return new JsonResult(1, result, pageInfo, systemTime);
    }

    /**
    * <AUTHOR>
    * @Description 获取机构中的考勤人员
    * @Date 2020/7/15
    */
    @ResponseBody
    @RequestMapping("/getAttendanceUsers.do")
    public JsonResult getAttendanceUsers(Integer yearMonth, User user){
        Integer oid = user.getOid();
        if(yearMonth==null || yearMonth.equals(0)) { //年月未传，取服务器当前时间年月
            yearMonth = Integer.valueOf(NewDateUtils.dateToString(new Date(System.currentTimeMillis()),"yyyyMM"));
        }
        //1.142 徐智新增初始化，查看本机构之前修改考勤是否需要审批，如为不需要，初始化为需要超管审批（目前需求为只能一级审批，如后续可修改，此处需要干掉）
        workAttendanceService.initializeWorkAttendanceApply(oid);

        List<Map<String, Object>> listMap = personnelAttendanceMonthlyService.getAttendanceUsers(oid, yearMonth,null);
        Date systemTime = workAttendanceService.getStartUsingSystemTime(oid);  //获取系统时间
        Map<String, Object> map = new HashMap<>();
        map.put("listMap",listMap);
        map.put("systemTime",systemTime);
        return new JsonResult(1, map);
    }

    /**
     * <AUTHOR>
     * @Date 2018/4/25 17:03
     * 考勤修改【最早的接口，下面有现在使用的接口，此接口暂时停用】
     * 2020.1.28徐智 1.142考勤不可直接修改，需要审批，此接口停用
     * 20210723李娅星 修改徐智 1.142考勤不可直接修改，需要审批，此接口停用
     */
//    @ResponseBody
//    @RequestMapping("/attendanceUserHistory.do")
//    public void attendanceUserHistory(User user,HttpServletRequest request,HttpServletResponse response) throws ParseException, IOException {
//        Map<String,Object> map=new HashMap<>();
////        User user= (User) session.getAttribute("user");
//        user=userService.getUserByID(user.getUserID());//登录人
//
//        String attendance = request.getParameter("attendance");
//        JSONObject attendanceJson = JSONObject.fromObject(attendance);
//
//        map = workAttendanceService.attendanceUserHistory(user,attendanceJson,map,request);  //考勤修改
//        ObjectToJson.objectToJson1(map,new String[]{"detail","personnelAttendanceUser","personnelAttendanceUserDetailHistoryHashSet","personnelAttendanceUserHistoryHashSet","personnelAttendanceUserDetailHashSet"},response);
//
//    }

    /**
     *<AUTHOR>
     *@date 2018/4/26 17:38
     *查询某月/某天考勤情况的列表【手机端2.78/2.79职工动态使用此接口，2021/4/2】
     * type 1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他（请假类型）
     *ttType 1-某天的考勤情况  2-某月的考勤情况
     */
    @ResponseBody
    @RequestMapping("/getPersonnelLeave.do")
    public void getPersonnelLeave(Integer ttType,String type,String beginTime,Integer userId,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        if (!MyStrings.nulltoempty(beginTime).isEmpty()) {
            Date beginDay = NewDateUtils.dateFromString(beginTime,"yyyy-MM-dd");
            if (beginDay==null){
                beginDay = NewDateUtils.dateFromString(beginTime,"yyyy-MM-dd HH:mm:ss");
            }
            map = workAttendanceService.getAttendanceMonthOrDay(ttType, type, beginDay, userId);
        }
        ObjectToJson.objectToJson1(map,new String[]{"personnelAttendanceUser","personnelAttendanceUserHistoryHashSet","personnelAttendanceUserDetailHistoryHashSet","personnelAttendanceUserDetailHashSet"},response);
    }

    /**
     *<AUTHOR>
     *@date 2018/4/26 17:38
     *查询某月/某天考勤情况的列表【手机端2.78/2.79职工动态使用此接口，2021/4/2】
     * 此接口与上面的/getPersonnelLeave.do接口一样，只是返回值和传值稍微不同，以后有此功能时，使用此接口
     * type 1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他（请假类型）
     *ttType 1-某天的考勤情况  2-某月的考勤情况
     */
    @ResponseBody
    @RequestMapping("/getAttendanceMonthOrDay.do")
    public JsonResult getAttendanceMonthOrDay(Integer ttType,String type,String beginTime,Integer userId) {
        Map<String,Object> map = new HashMap<>();
        if (!MyStrings.nulltoempty(beginTime).isEmpty()) {
            Date beginDay = NewDateUtils.dateFromString(beginTime,"yyyy-MM-dd");
            map = workAttendanceService.getAttendanceMonthOrDay(ttType, type, beginDay, userId);
        }
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2018/4/27 16:54
     *获取请假详情【手机端2.78/2.79职工动态使用此接口，2021/4/2】
     * source 来源 1-审批 2-录入
     *  historyType 1-查询历史详情中录入的加班（userDetailHistory表里的）
     */
    @ResponseBody
    @RequestMapping("/getLeaveDetail.do")
    public JsonResult getLeaveDetail(Integer leaveId,String source,Integer historyType) {
        Map<String,Object> map = new HashMap<>();
        if (!"".equals(source) && source!=null){
            map = workAttendanceOldService.getLeaveDetail(leaveId,source,historyType);
        }
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2018/4/28 9:57
     *获取加班的详情【手机端2.78/2.79职工动态使用此接口，2021/4/2】
     * source 来源 1-审批 2-录入
     *  historyType 1-查询历史详情中录入的加班（userDetailHistory表里的）
     */
    @ResponseBody
    @RequestMapping("/getOverTimeDetail.do")
    public JsonResult getOverTimeDetail(Integer overTimeId,String source,Integer historyType) {
        Map<String,Object> map = new HashMap<>();
        if (!"".equals(source) && source!=null){
            map = workAttendanceOldService.getOverTimeDetail(overTimeId,source,historyType);
        }
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2018/8/3 15:39
     *查某职工某天的加班详细情况【月查询中，点击加班总时长时，使用的接口】
     * type;//类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班，9-其他
     */
    @ResponseBody
    @RequestMapping("/getAttendanceDayDetail.do")
    public void getAttendanceDayDetail(Integer userId,String attendanceDate,String type,HttpServletResponse response) throws ParseException, IOException {
        Map<String,Object> map = new HashMap<>();
        map = workAttendanceOldService.getAttendanceDayDetail(userId,attendanceDate,type);
        ObjectToJson.objectToJson1(map,new String[]{"user","personnelAttendanceUser","personnelAttendanceUserDetailHistoryHashSet",
                "leaderName","userTotalHours","approvalFlow","reimburse","approvalFlow_","reimburse_","personnelLeave"},response);
    }

    /**
     *<AUTHOR>
     *@date 2018/4/27 16:28
     *获取明天和下个月的最后一天
     */
    @ResponseBody
    @RequestMapping("/getTomorrowAndMonth.do")
    public JsonResult getTomorrowAndMonth(HttpServletResponse response) {
        Map<String,Object> map = new HashMap<>();
        Date tomorrow = NewDateUtils.tomorrow();
        Date nextFirst = NewDateUtils.changeMonth(new Date(),1);  //获取下个月的一号
        Date nextLastDay = NewDateUtils.getLastTimeOfMonth(nextFirst);  //获取下个月的最后一天
        map.put("tomorrow",tomorrow);
        map.put("nextLastDay",nextLastDay);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2018/5/5 17:09
     *修改考勤录入时间
     */
    @ResponseBody
    @RequestMapping("/updateInputTime.do")
    public void updateInputTime(String inputTime,HttpServletResponse response,User user,String... attendanceIds) throws IOException, ParseException {
        Map<String,Object> map = new HashMap<>();
        if (attendanceIds!=null && inputTime!=null){
            for (String attendanceId:attendanceIds) {
                workAttendanceOldService.updateInputTime(Integer.parseInt(attendanceId),inputTime,user);
            }
            map.put("status",1);  //修改成功
        }else {
            map.put("status",0);  //修改失败
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    /**
     *<AUTHOR>
     *@date 2018/5/10 9:37
     *@date 2023/9/25 1.268考勤打卡改
     *修改考勤设置  修改后的考勤规则都是未生效的。
     * effectDate 规则生效时间（规则启用时间）
     */
    @ResponseBody
    @RequestMapping("/updateAttendanceSetting.do")
    public JsonResult updateAttendanceSetting(String effectDate,Integer attendancePattern,Short lateLimit,Short earlyLimit,Boolean leaveWork,HttpServletRequest request,User user,Boolean patternType) throws ParseException {
        Map<String, Object> map = new HashMap<>();
        if (!MyStrings.nulltoempty(effectDate).isEmpty() && new Date().before(NewDateUtils.dateFromString(effectDate,"yyyy-MM-dd"))) {
//            String startUsingSystemTime = workAttendanceOldService.returnStartUsingSystemTime(user.getOid()); //获取系统开始使用时间
            Date startUsingSystemTime = workAttendanceService.getStartUsingSystemTime(user.getOid()); //获取系统开始使用时间
            if (startUsingSystemTime!=null && startUsingSystemTime.before(NewDateUtils.dateFromString(effectDate,"yyyy-MM-dd"))){  //未生效的规则
                //添加修改记录
                String personnelAttendanceConfigs = request.getParameter("personnelAttendanceConfigs");  //需要修改或者新添加的考勤设置规则
                if (personnelAttendanceConfigs != null && !personnelAttendanceConfigs.isEmpty()) {
                    String deleteAttendanceSetIds = request.getParameter("deleteAttendanceSetIds");  //需要删除考勤设置的id
                    JSONArray jsonArray = JSONArray.fromObject(personnelAttendanceConfigs);
                    List personnelAttendanceConfigList = JSONArray.toList(jsonArray);
                    workAttendanceOldService.updateAttendanceSetting(deleteAttendanceSetIds,personnelAttendanceConfigList,user,effectDate,attendancePattern,lateLimit,earlyLimit,leaveWork,patternType);
                    map.put("status", 1);  //设置成功
                } else {
                    map.put("status", 0); // 设置失败
                }
            }else {
                map.put("status",2);  //生效时间在系统开始时间之前
            }
        }else {
            map.put("status",2);  //生效时间为当天或者是当前时间之前
        }
        return new JsonResult(1,map);
    }

    /**
    *@Description 修改迟到、早退、请假的设置
    *@auther 李娅星
    *@date 2023/11/14
    *@param configId:当前修改的考勤中的某一个规则id  lateLimit：迟到时限(分钟)  earlyLimit：早退时限(分钟)   leaveWork：请假到岗是否使用考勤宝:0-不使用(默认),1-使用
    */
    @ResponseBody
    @RequestMapping("/updateLimit.do")
    public JsonResult updateLimit(User user,Short lateLimit,Short earlyLimit,Boolean leaveWork,Integer configId) throws ParseException{
        Map<String, Object> map = new HashMap<>();
        if (lateLimit!=null&&earlyLimit!=null&&leaveWork!=null){
            map = workAttendanceOldService.updateLimit(user,lateLimit,earlyLimit,leaveWork,configId);
            return new JsonResult(1,map);
        }else {
            return new JsonResult(new MyException("400","传参有误"));
        }
    }

    /**
     *<AUTHOR>
     *@date 2024/1/30 1.268考勤打卡
     *修改过去的作息时间(假与班)
     * type：1-修改上月
     */
    @ResponseBody
    @RequestMapping("/updateTimeTable.do")
    public JsonResult updateTimeTable(User user,String time,Integer type) {
        Map<String,Object> map = new HashMap<>();
        if (StringUtils.isNotEmpty(time)) {
            if (type!=null&&1==type){
               map = workAttendanceOldService.checkAttendanceUpdate(user.getOid());
                Integer status = (Integer) map.get("status");
                if (0==status){
                    map.put("status",0); //不可修改上月考勤
                    map.put("content","不可修改上月考勤"); //不可修改上月考勤
                    return new JsonResult(1,map);
                }
            }
            map = workAttendanceOldService.updateTimeTable(user,time);
            return new JsonResult(1,map);
        }else {
            return new JsonResult(new MyException("400","传参有误"));
        }

    }

    /**
     *<AUTHOR>
     *@date 2018/5/10 11:12
     *@date 2023/10/8 1.268考勤打卡改
     *查询修改考勤设置列表
     */
    @ResponseBody
    @RequestMapping("/getUpdateAttendanceSetting.do")
    public JsonResult getUpdateAttendanceSetting(User user) {
        Map<String,Object> map = new HashMap<>();
        Integer oid = user.getOid();
        List<PersonnelAttendanceRecord> personnelAttendanceRecords = workAttendanceService.getPersonnelAttendanceRecordByOid(oid,null,null,null); //查询考勤设置的修改记录
        map.put("personnelAttendanceRecords",personnelAttendanceRecords);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2018/5/10 14:09
     *@date 2023/10/8 1.268考勤打卡改
     *查看修改考勤设置的详情
     */
    @ResponseBody
    @RequestMapping("/getUpdateAttendanceSettingDetail.do")
    public JsonResult getUpdateAttendanceSettingDetail(Integer attendanceRecordId) {
        Map<String,Object> map = new HashMap<>();
        if (attendanceRecordId!=null){
            PersonnelAttendanceConfig personnelAttendanceConfig = new PersonnelAttendanceConfig();
            List<PersonnelAttendanceDepartmentConfig> personnelAttendanceDepartmentConfigList = new ArrayList<>();
            PersonnelAttendanceRecord personnelAttendanceRecord = workAttendanceOldService.getPersonnelAttendanceRecordById(attendanceRecordId);  //修改记录
            List<PersonnelAttendanceConfigHistory> personnelAttendanceConfigHistories = workAttendanceOldService.getPersonnelAttendanceConfigHistoryByRecordId(attendanceRecordId,null,null,null);
            for (PersonnelAttendanceConfigHistory ph:personnelAttendanceConfigHistories) {
                ph.setBeginTimeString(NewDateUtils.dateToString(ph.getBeginTime(),"HH:mm"));
                ph.setEndTimeString(NewDateUtils.dateToString(ph.getEndTime(),"HH:mm"));
                ph.setBreakBeginString(NewDateUtils.dateToString(ph.getBreakBegin(),"HH:mm"));
                ph.setBreakEndString(NewDateUtils.dateToString(ph.getBreakEnd(),"HH:mm"));
                List<PersonnelAttendanceDepartmentConfigHistory> personnelAttendanceDepartmentConfigHistories = workAttendanceOldService.getPerAttendanceDeptConfigHistory(ph.getId());
                ph.setPersonnelAttendanceDepartmentConfigHistoryList(personnelAttendanceDepartmentConfigHistories);
                for (PersonnelAttendanceDepartmentConfigHistory personnelAttendanceDepartmentConfigHistory:personnelAttendanceDepartmentConfigHistories) {
                    if (personnelAttendanceDepartmentConfigHistory.getDept()!=null && personnelAttendanceDepartmentConfigHistory.getDept()==0){
                        personnelAttendanceDepartmentConfigHistory.setDeptName("其他");
                    }else {
                        Organization org = orgService.getOrgByOid(personnelAttendanceDepartmentConfigHistory.getDept(), OrgService.OrgType.department);
                        if (org!=null){
                            if (!MyStrings.nulltoempty(org.getName()).isEmpty()){
                                personnelAttendanceDepartmentConfigHistory.setDeptName(org.getName());
                            }
                        }
                    }
                }

                personnelAttendanceConfig = ph.getRule();
                personnelAttendanceConfig.setBeginTimeString(NewDateUtils.dateToString(personnelAttendanceConfig.getBeginTime(),"HH:mm"));
                personnelAttendanceConfig.setEndTimeString(NewDateUtils.dateToString(personnelAttendanceConfig.getEndTime(),"HH:mm"));
                personnelAttendanceConfig.setBreakBeginString(NewDateUtils.dateToString(personnelAttendanceConfig.getBreakBegin(),"HH:mm"));
                personnelAttendanceConfig.setBreakEndString(NewDateUtils.dateToString(personnelAttendanceConfig.getBreakEnd(),"HH:mm"));
                personnelAttendanceDepartmentConfigList = workAttendanceOldService.getAttendanceDeptConfigByConfigId(personnelAttendanceConfig.getId());
                personnelAttendanceConfig.setPersonnelAttendanceDepartmentConfigList(personnelAttendanceDepartmentConfigList);
                for (PersonnelAttendanceDepartmentConfig personnelAttendanceDepartmentConfig:personnelAttendanceDepartmentConfigList) {
                    if (personnelAttendanceDepartmentConfig.getDept()!=null && personnelAttendanceDepartmentConfig.getDept()==0){
                        personnelAttendanceDepartmentConfig.setDeptName("其他");
                    }else {
                        Organization org = orgService.getOrgByOid(personnelAttendanceDepartmentConfig.getDept(), OrgService.OrgType.department);
                        if (org!=null){
                            personnelAttendanceDepartmentConfig.setDeptName(org.getName());
                        }
                    }
                }
                map.put("rule",personnelAttendanceConfig); //前端可能取值不同，所以保留
            }
            map.put("personnelAttendanceRecord",personnelAttendanceRecord);//操作：0-正常,1-增,2-删,3-改 4-未到生效日期的考勤
            map.put("personnelAttendanceConfigHistorys",personnelAttendanceConfigHistories);  //修改前的考勤设置
            map.put("personnelAttendanceConfig",personnelAttendanceConfig);//操作：0-正常,1-增,2-删,3-改 4-未到生效日期的考勤
            map.put("personnelAttendanceDepartmentConfigList",personnelAttendanceDepartmentConfigList);  //修改前的考勤设置
            map.put("status",1); //成功
        }else {
            map.put("status",0);  //失败
        }
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2018/6/12 10:29
     *职工考勤修改记录（总务-修改记录使用）
     */
    @ResponseBody
    @RequestMapping("/getUpdateAttendanceLog.do")
    public void getUpdateAttendanceLog(User user,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        Integer oid = user.getOid();
        List<PersonnelAttendanceUserHistory>  personnelAttendanceUserHistorys = workAttendanceOldService.getPersonnelAttendanceUserHistoryList(oid,null,null,"2",null,null,54);
        map.put("personnelAttendanceUserHistorys",personnelAttendanceUserHistorys);
        ObjectToJson.objectToJson1(map,new String[]{"personnelAttendanceUser"},response);
    }

    /**
     *<AUTHOR>
     *@date 2018/6/12 13:46
     *查看职工考勤修改的详情（总务-修改记录使用）
     */
    @ResponseBody
    @RequestMapping("/updateAttendanceLogDetail.do")
    public void updateAttendanceLogDetail(Integer attendanceLogId, HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        if (attendanceLogId!=null){
            PersonnelAttendanceUserHistory personnelAttendanceUserHistory = workAttendanceOldService.getPersonnelAttendanceUserHistoryById(attendanceLogId);  //修改前的职工考勤
            List<PersonnelAttendanceUserDetailHistory> personnelAttendanceUserDetailHistory = workAttendanceOldService.getPersonnelAttendanceUserDetailHistoryByPHId(personnelAttendanceUserHistory.getId(),null,null);  //修改前的考勤明细列表
            PersonnelAttendanceUser personnelAttendanceUser = personnelAttendanceUserHistory.getPersonnelAttendanceUser();//修改后的职工考勤

            List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetails = new ArrayList<>();
            if (personnelAttendanceUser!=null){
                personnelAttendanceUserDetails = workAttendanceService.getPersonnelAttendanceUserDetailByBusiness(personnelAttendanceUser.getId(),null,null,null,null);

                User user = userService.getUserByID(personnelAttendanceUser.getUser());
                personnelAttendanceUser.setUserName(user.getUserName());
                personnelAttendanceUserHistory.setUserName(user.getUserName());

                if (!MyStrings.nulltoempty(personnelAttendanceUserHistory.getType()).isEmpty() && !"10".equals(personnelAttendanceUserHistory.getType())){
                    map.put("personnelAttendanceUserHistory",personnelAttendanceUserHistory);//修改前的职工考勤
                    map.put("personnelAttendanceDepartmentConfigHistorys",personnelAttendanceUserDetailHistory);//修改前的职工考勤明细
                }
            }
            map.put("status",1);
            map.put("personnelAttendanceUser1",personnelAttendanceUser);//修改后的职工考勤
            map.put("personnelAttendanceUserDetails",personnelAttendanceUserDetails);//修改后的职工考勤明细
            map.put("updateDesc",personnelAttendanceUserHistory.getUpdateDesc()); //修改理由
        }else {
            map.put("status",0);
        }
        ObjectToJson.objectToJson1(map,new String[]{"personnelAttendanceUser","detail","personnelAttendanceUserHistoryHashSet","personnelAttendanceUserDetailList",
                "personnelAttendanceUserDetailHashSet","personnelAttendanceUserDetailHistoryHashSet"},response);
    }

    /**
     *<AUTHOR>
     *@date 2018/6/12 15:25
     *无需考勤接口（1.143时已改，此接口暂时停用）
     */
//    @ResponseBody
//    @RequestMapping("/noNeedAttendance.do")
//    public void noNeedAttendance(HttpSession session,HttpServletRequest request,HttpServletResponse response) throws ParseException, IOException {
//        Map<String,Object> map=new HashMap<>();
//
//        User user= (User) session.getAttribute("user");
//        User loginUser = userService.getUserByID(user.getUserID());
//        String attendance = request.getParameter("attendance");
//        JSONObject attendanceJson = JSONObject.fromObject(attendance);
//        map = workAttendanceService.noNeedAttendance(loginUser,attendanceJson,map,request);   //无需考勤接口
//        ObjectToJson.objectToJson1(map,new String[]{"detail","personnelAttendanceUser","personnelAttendanceUserDetailList","personnelAttendanceUserHistoryHashSet","personnelAttendanceUserDetailHashSet"},response);
//    }

    /**
     *<AUTHOR>
     *@date 2020/11/7 10:22
     *系统是否可以查询上月考勤
     * belong_peroid 传2
    */
    @ResponseBody
    @RequestMapping("/checkAttendanceUpdate.do")
    public JsonResult checkAttendanceUpdate(User user) {
        Map<String,Object> map=new HashMap<>();
        map = workAttendanceOldService.checkAttendanceUpdate(user.getOid());
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2020/12/8 14:57
     *获取考勤系统里的加班或请假
     * type 类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班，9-其他
     * 刘洪涛-1.145我的工作记录  使用
    */
    @ResponseBody
    @RequestMapping("/getLeaveOrOverTime.do")
    public JsonResult getLeaveOrOverTime(Integer userId,String attendanceDate,String type) throws ParseException {
        Map<String,Object> map=new HashMap<>();
        if (userId!=null&&!MyStrings.nulltoempty(attendanceDate).isEmpty()&&!MyStrings.nulltoempty(type).isEmpty()) {
            map = workAttendanceOldService.getLeaveOrOverTime(userId, new SimpleDateFormat("yyyy-MM-dd").parse(attendanceDate), type);
        }
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description  总务的考勤修改申请接口{修改上面的/applyUpdateAttendanceUser.do接口}
     * @Date 2021/7/22
     * @param  noNeedType 1-无需考勤   0-有考勤状态
     * @return
     **/
    @ResponseBody
    @RequestMapping("/applyUpdateAttendanceUser.do")
    public JsonResult attendanceUserHistory(HttpServletRequest request,User user,Integer noNeedType,Integer attendanceId,String attendanceDate,Integer attendanceUserId,String upIsNormal,String isAbsenteeism,String isLeave,String downIsNormal,String isOverTime,String updateDesc) {
        String absenteeism = request.getParameter("absenteeism");
        String leaveList = request.getParameter("leaveList");
        String overList = request.getParameter("overList");
        Map<String,Object> map = workAttendanceOldService.attendanceUserHistory(user,noNeedType,absenteeism,leaveList,overList,attendanceId,attendanceDate,attendanceUserId,upIsNormal,isAbsenteeism,isLeave,downIsNormal,isOverTime,updateDesc);  //考勤修改
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 总务考勤修改-我的申请(修改的上面相同的接口)
     * @Date 2021/7/23
     * @param
     * @return
     **/
     @ResponseBody
     @RequestMapping("/applyApprovalInstanceList.do")
     public JsonResult applyApprovalInstanceList(User user){
        Map<String,Object> map=new HashMap<>();
        List<ApprovalProcess> approvalProcesses=approvalProcessService.getApprovalProcessByFromUser(user.getUserID(),"1",54,null,null,null);
        map.put("approvalProcesses",approvalProcesses);
        return new JsonResult(1,map);
     }

    /**
     * <AUTHOR>
     * @Description 总务考勤修改-审批人待处理列表
     * @Date 2021/7/23
     * @param
     * @return
     **/
    @ResponseBody
    @RequestMapping("/approveApprovalInstanceList.do")
    public JsonResult approveApprovalInstanceList(User user) {
        Map<String,Object> map=new HashMap<>();
        List<ApprovalProcess> approvalProcesses=approvalProcessService.getApprovalProcessByBusinessType(user.getUserID(),"1",54,null,null,null,null);
        map.put("approvalProcesses",approvalProcesses);
        return  new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2021/2/28 20:10(2021/3/31改)
     *总务提交的考勤修改的审批（原handleApprovalInstance.do接口）
     * approvalProcessId:审批流程id   approveStatus 2通过，3驳回 approveMemo 理由
     */
    @ResponseBody
    @RequestMapping("/handleApprovalInstance.do")
    public JsonResult handleApprovalInstance(User user,Integer approvalProcessId,String approveMemo,String approveStatus) {
        Map<String,Object> map= workAttendanceOldService.approvalAttendance(user,approvalProcessId,approveMemo,approveStatus,1);
        return new JsonResult(1,map);
    }


    //修改判断接口
    //id    返回值status 1可以修改，0不可以修改 【此接口判断的是系统中有没有未审批完的修改申请，现已在修改申请的接口中进行判断，此接口暂时不用 lyx/20210727】
    @ResponseBody
    @RequestMapping("/judgeUpdateAttendanceUser.do")
    public RespStatus judgeUpdateAttendanceUser(User user,Integer id,Integer attendanceUserId,String attendanceDate) throws ParseException{
        RespStatus respStatus=new RespStatus();
        int status= workAttendanceOldService.judgeUpdateAttendanceUser(user,id,attendanceUserId,attendanceDate);
        respStatus.setStatus(status);
        return  respStatus;
    }
//    //自定义查询
//    //考勤修改-我的申请
//    //传值 userId
//    //source来源 ，1申请人，2审批人
//    //type 1通过，0驳回   status 1，七日，2本月，3自定义
//    //startTime endTime
//    @ResponseBody
//    @RequestMapping("/approvalInstanceByCriterionList.do")
//    public ApprovalDto approvalInstanceByCriterionList(User user,SelectCriterionDto selectCriterionDto) throws ParseException {
////        User user= (User) session.getAttribute("user");
//        if(selectCriterionDto.getUserId()==null)
//            selectCriterionDto.setUserId(user.getUserID());
//        ApprovalDto approvalDto=workAttendanceService.approvalInstanceByCriterionList(selectCriterionDto);
//        return  approvalDto;
//    }

    /**
     * <AUTHOR>
     * @Description 总务考勤修改的查询
     * @Date 2021/7/26
     * source来源-1申请人，2审批人  approveStatus-2通过，3驳回   status-1七日，2本月，3自定义
     * @return
     **/
    @ResponseBody
    @RequestMapping("/approvalInstanceByCriterionList.do")
    public JsonResult approvalInstanceByCriterionList(User user,Integer source,String approveStatus,Integer status,String beginDate,String endDate) {
        Map<String,Object> map = new HashMap<>();
        Date beginTime = null;
        Date endTime = null;
        if (1==status){
            beginTime = NewDateUtils.changeDay(new Date(),-6);
            endTime = new Date();
        }else if (2==status){
            beginTime = NewDateUtils.changeMonth(new Date(),0);
            endTime = new Date();
        }else {
            beginTime = NewDateUtils.today(NewDateUtils.dateFromString(beginDate,"yyyy-MM-dd"));
            endTime = NewDateUtils.getLastTimeOfDay(NewDateUtils.dateFromString(endDate,"yyyy-MM-dd"));
            if (endTime.getTime()>new Date().getTime()){
                endTime = new Date();
            }
        }
        List<ApprovalProcess> approvalProcesses=new ArrayList<>();
        if (1==source){
            approvalProcesses=approvalProcessService.getApprovalProcessByFromUser(user.getUserID(),approveStatus,54,beginTime,endTime,null);
        }else {
            approvalProcesses=approvalProcessService.getApprovalProcessByBusinessType(user.getUserID(),approveStatus,54,beginTime,endTime,null,null);
        }
        map.put("approvalProcesses",approvalProcesses);
        map.put("beginDate",NewDateUtils.dateToString(beginTime,"yyyy-MM-dd"));
        map.put("endDate",NewDateUtils.dateToString(endTime,"yyyy-MM-dd"));
        return new JsonResult(1,map);
    }

    //---------------------1.143桌面之考勤与登录记录(以下为不可共用的接口)-+-------------------

    /**
     * 考勤修改的申请
     *<AUTHOR>
     *@date 2021/2/27(2021/3/30改)
     * noNeedType:1-无需考勤   0-有考勤状态
     */
    @ResponseBody
    @RequestMapping("/applyAttendance.do")
    public JsonResult applyAttendance(HttpServletRequest request,Integer userId,Integer noNeedType) {
        Map<String,Object> map=new HashMap<>();
        User user=userService.getUserByID(userId);//登录人
        String attendance = request.getParameter("attendance");
        String absenteeism = request.getParameter("absenteeism");
        String leaveList = request.getParameter("leaveList");
        String overList = request.getParameter("overList");
        map = workAttendanceOldService.applyAttendance(user,noNeedType,attendance,absenteeism,leaveList,overList);//考勤修改
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2021/2/27(2021/3/30改)
     * 我的考勤-申请人的考勤修改列表
     * Integer userId：登录人id
    */
    @ResponseBody
    @RequestMapping("/attendanceApplyList.do")
    public JsonResult attendanceApplyList(User user) {
        Map<String,Object> map=new HashMap<>();
        List<ApprovalProcess> approvalProcesses=approvalProcessService.getApprovalProcessByFromUser(user.getUserID(),"1",36,null,null,null);
        map.put("approvalProcesses",approvalProcesses);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2021/2/27 21:57(2021/3/30改)
     *我的考勤-审批人的考勤修改列表（个人提交的考勤修改列表）
     * Integer userId：登录人id
    */
    @ResponseBody
    @RequestMapping("/attendanceApprovalList.do")
    public JsonResult attendanceApprovalList(User user) {
        Map<String,Object> map=new HashMap<>();
        List<ApprovalProcess> approvalProcesses=approvalProcessService.getApprovalProcessByBusinessType(user.getUserID(),"1",36,null,null,null,null);
        map.put("approvalProcesses",approvalProcesses);
        return  new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2021/2/28 20:10(2021/3/31改)
     *个人提交的考勤修改的审批（原handleApprovalInstance.do接口）
     * id:审批流程id   type 2通过，3驳回 approveMemo 理由
    */
    @ResponseBody
    @RequestMapping("/approvalAttendance.do")
    public JsonResult approvalAttendance(User user,Integer approvalProcessId,String approveMemo,String approveStatus) {
        Map<String,Object> map= workAttendanceOldService.approvalAttendance(user,approvalProcessId,approveMemo,approveStatus,2);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2021/3/31 11:56
     *获取前后的考勤修改详情
     * aUHistoryId：审批流程中的business，考勤用户历史id
    */
    @ResponseBody
    @RequestMapping("/updateAttendanceDetail.do")
    public JsonResult updateAttendanceDetail(Integer aUHistoryId,Integer businessType){
        Map<String,Object> map = new HashMap<>();
        PersonnelAttendanceUserHistory personnelAttendanceUserHistory = new PersonnelAttendanceUserHistory();//修改后的职工考勤
        List<PersonnelAttendanceUserDetailHistory> personnelAttendanceUserDetailHistorys = new ArrayList<>();//修改后的考勤明细列表
        PersonnelAttendanceUserHistory previousAttendanceUserHistory = new PersonnelAttendanceUserHistory();//修改前的职工考勤
        List<PersonnelAttendanceUserDetailHistory> previousAttendanceUserDetailHistorys = new ArrayList<>();//修改前的考勤明细列表
        if (null==businessType){
            businessType=36;
        }
        ApprovalProcess approvalProcess = approvalProcessService.getApprovalProcessByBusToUser(aUHistoryId,businessType,null);
        if (aUHistoryId!=null){
            personnelAttendanceUserHistory = workAttendanceOldService.getPersonnelAttendanceUserHistoryById(aUHistoryId);  //修改后的职工考勤
            personnelAttendanceUserDetailHistorys = workAttendanceOldService.getPersonnelAttendanceUserDetailHistoryByPHId(aUHistoryId,null,null);  //修改后的考勤明细列表
            for (PersonnelAttendanceUserDetailHistory pdh:personnelAttendanceUserDetailHistorys) {
                if ("5".equals(pdh.getType())){
                    pdh.setLeaveTypeName(leaveService.getLeaveTypeName(pdh.getLeaveType(),pdh.getBusinessType()));
                }
            }
            if (null!=personnelAttendanceUserHistory.getAttendanceId() && !"10".equals(personnelAttendanceUserHistory.getType())){
                previousAttendanceUserHistory = workAttendanceOldService.getPersonnelAttendanceUserHistoryById(personnelAttendanceUserHistory.getPreviousId());
                previousAttendanceUserDetailHistorys = workAttendanceOldService.getPersonnelAttendanceUserDetailHistoryByPHId(personnelAttendanceUserHistory.getPreviousId(),null,null);
                for (PersonnelAttendanceUserDetailHistory pdh:previousAttendanceUserDetailHistorys) {
                    if ("5".equals(pdh.getType())){
                        pdh.setLeaveTypeName(leaveService.getLeaveTypeName(pdh.getLeaveType(),pdh.getBusinessType()));
                    }
                }
                map.put("noNeedType",0); //修改前有数据的
            }else {
                //无需考勤的第一次修改，修改前无数据
                map.put("noNeedType",1); //无需考勤的修改前无数据的
            }
        }
        map.put("attendanceUserHistory",personnelAttendanceUserHistory);//修改后的职工考勤
        map.put("attendanceUserDetailHistorys",personnelAttendanceUserDetailHistorys);//修改后的职工考勤明细
        map.put("previousAttendanceUserHistory",previousAttendanceUserHistory);//修改前的职工考勤
        map.put("previousAttendanceUserDetailHistorys",previousAttendanceUserDetailHistorys);//修改前的职工考勤明细
        map.put("approvalProcess",approvalProcess); //审批流程
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2021/3/1(2021/3/31改)
     *我的考勤的考勤修改查询
     * 传值-userId   source来源-1申请人，2审批人  approveStatus-2通过，3驳回   status-1七日，2本月，3自定义
    */
    @ResponseBody
    @RequestMapping("/approvalAttendanceList.do")
    public JsonResult approvalAttendanceList(User user,Integer source,String approveStatus,Integer status,String beginDate,String endDate) {
        Map<String,Object> map = new HashMap<>();
        Date beginTime = null;
        Date endTime = null;
        if (1==status){
            beginTime = NewDateUtils.changeDay(new Date(),-6);
            endTime = new Date();
        }else if (2==status){
            beginTime = NewDateUtils.changeMonth(new Date(),0);
            endTime = new Date();
        }else {
            beginTime = NewDateUtils.today(NewDateUtils.dateFromString(beginDate,"yyyy-MM-dd"));
            endTime = NewDateUtils.getLastTimeOfDay(NewDateUtils.dateFromString(endDate,"yyyy-MM-dd"));
            if (endTime.getTime()>new Date().getTime()){
                endTime = new Date();
            }
        }
        List<ApprovalProcess> approvalProcesses=new ArrayList<>();
        if (1==source){
            approvalProcesses=approvalProcessService.getApprovalProcessByFromUser(user.getUserID(),approveStatus,36,beginTime,endTime,null);
        }else {
            approvalProcesses=approvalProcessService.getApprovalProcessByBusinessType(user.getUserID(),approveStatus,36,beginTime,endTime,null,null);
        }
        map.put("approvalProcesses",approvalProcesses);
        map.put("beginDate",NewDateUtils.dateToString(beginTime,"yyyy-MM-dd"));
        map.put("endDate",NewDateUtils.dateToString(endTime,"yyyy-MM-dd"));
        return new JsonResult(1,map);
    }

    //--------------------------1.153/2.100/2.101请假加班时间限制-------------------------------
    /**
     *<AUTHOR>
     *@date 2021/4/28 9:47
     *获取系统中某月的作息时间[beginDate 时间格式yyyy-MM-dd ]
     */
    @ResponseBody
    @RequestMapping("/getTimeMonth.do")
    public JsonResult getTimeMonth(User user,String beginDate){
        Map<String,Object> map = new HashMap<>();
        Date date = NewDateUtils.dateFromString(beginDate,"yyyy-MM-dd");
        List<PersonnelAttendanceException> personnelAttendanceExceptions = workAttendanceOldService.getPersonnelAttendanceExceptionByMonth(user.getOid(),NewDateUtils.changeMonth(date,0),NewDateUtils.getLastTimeOfMonth(date));
        map.put("personnelAttendanceExceptions",personnelAttendanceExceptions);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2021/4/28 9:47
     *获取系统中某一天的作息时间[beginDate 时间格式yyyy-MM-dd ]
     */
    @ResponseBody
    @RequestMapping("/getTimeDay.do")
    public JsonResult getTimeDay(User user,String beginDate){
        Map<String,Object> map = new HashMap<>();
        PersonnelAttendanceException personnelAttendanceException= workAttendanceService.getPersonnelAttendanceExceptionByExceptionDate(user.getOid(),NewDateUtils.dateFromString(beginDate,"yyyy-MM-dd"));
        map.put("personnelAttendanceException",personnelAttendanceException);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2021/4/29 18:37
     *获取系统中的上班开始时间和结束时间
     */
    @ResponseBody
    @RequestMapping("/getWorkTime.do")
    public JsonResult getWorkTime(User user,String attendanceDate){
        Map<String,Object> map = new HashMap<>();
        Integer dept = null;
        if (!MyStrings.nulltoempty(user.getDepartment()).isEmpty()){
            dept = Integer.getInteger(user.getDepartment());
        }
        Date openDate = NewDateUtils.dateFromString(attendanceDate,"yyyy-MM-dd");
        workAttendanceService.returnTime(user.getOid(),dept,openDate,map);
        Date systemTime = workAttendanceService.getStartUsingSystemTime(user.getOid());  //获取系统考勤开始时间
        if (systemTime!=null&&NewDateUtils.today(systemTime).getTime()<=NewDateUtils.today(openDate).getTime()) {
            PersonnelAttendanceException personnelAttendanceException = workAttendanceService.getPersonnelAttendanceExceptionByExceptionDate(user.getOid(), openDate);
            if (personnelAttendanceException != null) {
                map.put("workOrNo", personnelAttendanceException.getType());
            } else {
                map.put("workOrNo", null);  //没有设置考勤的
            }
        }else {
            map.put("workOrNo", null);  //没有设置考勤的
        }
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Description 工作记录中获取的加班天数、其他天数
     * @Date 2021/12/31
     * @param  beginDate:某月/年中的某一天（年月日） sourceType:来源类型 1-我的团队 2-总览-职工下的 3-工作记录点评  dateType:1-月报 2-年报  onlyUser:仅仅查询此职工
     * @return
     **/
    @ResponseBody
    @RequestMapping("/getAttendanceDays.do")
    public JsonResult getAttendanceDays(User user,Integer deptId,String beginDate,Integer sourceType,Integer dateType,Integer onlyUser,PageInfo pageInfo){
        Date beginTime = NewDateUtils.today(NewDateUtils.dateFromString(beginDate,"yyyy-MM-dd"));
        Map<String,Object> map = workAttendanceOldService.newGetAttendanceDays(user,deptId,beginTime,sourceType,dateType,onlyUser,pageInfo);
        return new JsonResult(1,map,pageInfo);
    }

    /**
     * <AUTHOR>
     * @Description 工作记录中获取的当前登陆人是不是某userid的上级的方法
     * @Date 2022/6/21
     * beginDate:某月中的某一天（年月日）   sourceType:来源类型 1-我的团队 2-总览-职工下的
     * @return
     **/
    @ResponseBody
    @RequestMapping("/directSuperior.do")
    public JsonResult directSuperior(User user,Integer userId){
        Map<String,Object> map = workAttendanceOldService.directSuperior(user.getUserID(),userId);
        return new JsonResult(1,map);
    }

    //--------------------------以下是1.268考勤打卡部分接口-----------------------------------
    /**
     *@Description 点击考勤打卡
     *@auther 李娅星
     *@date 2023/10/10
     *@param
     */
    @ResponseBody
    @RequestMapping("/attendanceClock.do")
    public JsonResult attendanceClock(User user) {
        Map<String, Object> map = new HashMap<>();
        Date startUsingSystemTime = workAttendanceService.getStartUsingSystemTime(user.getOid()); //获取系统开始使用时间
        Date today = NewDateUtils.today();
        if ( startUsingSystemTime!=null && today.compareTo(startUsingSystemTime)>=0) {  //当前时间在系统开始时间之后，可用
//            workAttendanceService.setDefaultWorkAttendance(user.getOid()); //系统默认添加今天的人员且默认添加考勤人员旷工
            Integer dept = 0;  //部门其他的
            if (StringUtils.isNotEmpty(user.getDepartment())) {
                dept = Integer.parseInt(user.getDepartment());
            }

            List<Map<String, Object>> attendanceClockList = new ArrayList<>();
            PersonnelAttendanceConfig personnelAttendanceConfig = workAttendanceOldService.getPersonnelAttendanceConfigByDept(user.getOid(),dept,new Date());
            if (personnelAttendanceConfig!=null&&personnelAttendanceConfig.getAttendancePattern().equals(WorkAttendanceOldService.ConfigAttendancePattern.attendanceTreasure.getIndex())){
//            if (personnelAttendanceConfig!=null) {
                map.put("lateLimit", personnelAttendanceConfig.getLateLimit());   //迟到时限(分钟)
                map.put("earlyLimit", personnelAttendanceConfig.getEarlyLimit());  //早退时限(分钟)
                for (int i = 1; i <= 2; i++) {
                    Map<String, Object> map1 = new HashMap<>();
                    if (1 == i) {
                        Date beginTime = NewDateUtils.joinDateTimeString(today, NewDateUtils.dateToString(personnelAttendanceConfig.getBeginTime(), "HH:mm:ss")); //上班时间，拼接上年月日
                        Date endTime = NewDateUtils.joinDateTimeString(today, NewDateUtils.dateToString(personnelAttendanceConfig.getEndTime(), "HH:mm:ss")); //下班时间，拼接上年月日
                        map1.put("type", i); //1-上班 2-午休
                        map1.put("beginTime", beginTime); //开始时间
                        map1.put("endTime", endTime); //结束时间
                        attendanceClockList.add(map1);
                    } else {
                        if (Boolean.TRUE.equals(personnelAttendanceConfig.getMiddleBreak())) { //是否中间休息(是否中午考勤),true-是
                            Date breakBegin = NewDateUtils.joinDateTimeString(today, NewDateUtils.dateToString(personnelAttendanceConfig.getBreakBegin(), "HH:mm:ss")); //上班时间，拼接上年月日
                            Date breakEnd = NewDateUtils.joinDateTimeString(today, NewDateUtils.dateToString(personnelAttendanceConfig.getBreakEnd(), "HH:mm:ss")); //下班时间，拼接上年月日
                            map1.put("type", i); //1-上班 2-午休
                            map1.put("beginTime", breakBegin); //开始时间
                            map1.put("endTime", breakEnd); //结束时间
                            attendanceClockList.add(map1);
                        }
                    }
                }

                if (personnelAttendanceConfig.getLeaveWork() != null && personnelAttendanceConfig.getLeaveWork()) {
                    List<PersonnelLeave> personnelLeaves = leaveService.getPersonnelLeaveListByTime(user.getUserID(),today, NewDateUtils.getLastTimeOfDay(today));
                    for (PersonnelLeave pl : personnelLeaves) {
                        Map<String, Object> map1 = new HashMap<>();
                        map1.put("type", 3); //1-上班 2-午休 3-请假
                        map1.put("id", pl.getId()); //请假id
                        map1.put("beginTime", pl.getActualBeginTime()); //请假实际开始时间
                        map1.put("endTime", pl.getActualEndTime()); //请假实际结束时间
                        attendanceClockList.add(map1);
                    }
                }

                List<PersonnelOvertime> personnelOvertimes = overtimeService.getPersonnelOvertimeListByBeginTime(user.getUserID(), 2, today, NewDateUtils.getLastTimeOfDay(today), "0", null, null);
                for (PersonnelOvertime po : personnelOvertimes) {
                    Map<String, Object> map1 = new HashMap<>();
                    map1.put("type", 4); //1-上班 2-午休 3-请假 4-加班
                    map1.put("id", po.getId()); //请假id
                    map1.put("beginTime", po.getBeginTime()); //开始时间
                    map1.put("endTime", po.getEndTime()); //结束时间
                    attendanceClockList.add(map1);
                }
                map.put("attendancePattern", 1); // 1-考勤宝 2-手动录入(用不用都可以)
            }else {
                map.put("attendancePattern", 2); // 1-考勤宝 2-手动录入
            }
//            }
            map.put("attendanceClockList", attendanceClockList); //列表信息(手机端需要的列表格式)
            map.put("workOrNo", 1); //有设置考勤的

            PersonnelAttendanceException personnelAttendanceException = workAttendanceService.getPersonnelAttendanceExceptionByExceptionDate(user.getOid(), today); //查询当天是否上班
            if (personnelAttendanceException != null) { //1-假 2-班
               map.put("exceptionType",personnelAttendanceException.getType());  //1-假 2-班
            }else {
                map.put("exceptionType","");  //1-假 2-班
            }

        } else {
            map.put("workOrNo", 0);  //没有设置考勤的
        }
        return new JsonResult(1,map);
    }

//    /**
//     *@Description 打卡
//     *@auther 李娅星
//     *@date 2023/10/10
//     *cardNo:打卡卡号(String cardNo,这个暂时不传了),sysDevice:加密过的有deviceUuid的数据 punchType:打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
//     *@param image:快照图片内容   terminalUuid:(人事员工打卡的)唯一标识/webchat union_id   brand:品牌/昵称    model型号
//     */
//    @ResponseBody
//    @RequestMapping("/clock.do")
//    public JsonResult clock(User user, AuthInfoDto authInfo, Integer leaveId, Integer overTimeId, String sysDevice, Byte punchType, String image,String terminalUuid,String brand,String model) {
//        Date punchTime = new Date(System.currentTimeMillis()); //打卡时间
//        System.out.println("考勤打卡开始:clock.do，punchType="+punchType+"开始时间："+new Date());
//        Map<String, Object> map = new HashMap<>();
//        if(StringUtils.isNotEmpty(sysDevice)&&punchType!=null) {
//            //解密
//            String deviceUuid = "";
//            Integer iotTerminalId = authInfo.getTerminalId();  //打卡设备id
//            String plaintext = new JsencryptUtils().decryptIot(sysDevice);
//            if (StringUtils.isNotEmpty(plaintext) && plaintext.contains(",") && "qH&_^0%4".equals(plaintext.substring(0, 8))) {
//                int index = plaintext.indexOf(',');
//                deviceUuid = plaintext.substring(8, index);
//                int index1 = plaintext.lastIndexOf(',');
//                if (iotTerminalId==null) {
//                    iotTerminalId = Integer.parseInt(plaintext.substring(index + 1, index1));
//                }
//                int index2 = plaintext.lastIndexOf(',')+1;
//                //打卡码生成时间
//                Long punchCreate1 = Long.valueOf(plaintext.substring(index2, plaintext.length()));
//                long limitDate = 15*1000;  //将限制的15s转换成毫秒(打卡码每15秒刷新一次)
//                if (new Date().getTime()-punchCreate1>limitDate){
//                    map.put("status", "0");
//                    map.put("content", "打卡码已失效");
//                    return new JsonResult(1, map);
//                }else {
//                    map = workAttendanceOldService.clock(user, punchTime,leaveId, overTimeId, iotTerminalId, punchType,image,terminalUuid,brand,model);
//                    System.out.println("考勤打卡结束:clock.do，punchType="+punchType+"结束时间："+new Date());
//                    return new JsonResult(1, map);
//                }
//            }else {
//                return new JsonResult(new MyException("400","传参有误"));
//            }
//        }else {
//            return new JsonResult(new MyException("400","传参有误"));
//        }
//    }

    @ResponseBody
    @RequestMapping("/clock.do")
    public JsonResult clock(User user, String sysDevice, Byte terminaType, Byte workdayType, Byte punchType, String terminalUuid, String brand, String model, Integer leaveId, Integer overTimeId, String image) {
        JsonResult result;
        PunchType type = PunchType.getByIndex(punchType);
        if(StringUtils.isNotEmpty(sysDevice) && type!=null) {
            long now = System.currentTimeMillis(); //打卡时间
            WorkdayType wt = workdayType!=null ? WorkdayType.getByIndex(workdayType) : type.getWorkdayType();
            AttendanceTerminaType attendanceTerminaType = AttendanceTerminaType.getByIndex(terminaType);
            if(attendanceTerminaType==null) {//没有传的话用默认值
                attendanceTerminaType = AttendanceTerminaType.device;
            }
            //解密
            String plaintext = new JsencryptUtils().decryptIot(sysDevice);
            String[] strs;
            strs=plaintext.split(",");
            if (StringUtils.isNotEmpty(plaintext) && (strs=plaintext.split(",")).length==3 && "qH&_^0%4".equals(strs[0].substring(0,8))) {
                String deviceUuid = strs[0].substring(8);
                //打卡码生成时间
                long codeTime;
                try {
                    codeTime = Long.parseLong(strs[2]);
                } catch (Exception e) {
                    logger.warn("-3 传参有误", e);
                    return new JsonResult(new MyException("-3","传参有误"));
                }
                if (Math.abs(now - codeTime) >  TimeUnit.SECONDS.toMillis(15)/* + TimeUnit.DAYS.toMillis(1)*/){//测试打卡码时效是一天+15秒
                    return new JsonResult(new MyException("-3","打卡码已失效"));
                } else {
                    result = workAttendanceService.clock(user, wt, new Date(now), deviceUuid, attendanceTerminaType, terminalUuid, brand, model, type, leaveId, overTimeId, image);
                    Date finish = new Date(System.currentTimeMillis());
                    logger.warn("考勤打卡:clock.do，punchType=" + punchType + "，开始时间：" + new Date(now) + "，结束时间：" + finish + "用时(毫秒)：" + (finish.getTime() - now));
                    return result;
                }
            } else {
                return new JsonResult(new MyException("-2","传参有误"));
            }
        } else {
            return new JsonResult(new MyException("-1","传参有误"));
        }
    }

    /**
     *@Description 获取某天的打卡记录(返回了按照打卡类别区分的打卡记录)
     *@auther 李娅星
     *@date 2023/10/11
     *@param attendanceDate 要查询的某天
     */
    @ResponseBody
    @RequestMapping("/clockRecord.do")
    public JsonResult clockRecord(User user,String attendanceDate) {
        Map<String, Object> map = new HashMap<>();
        map = workAttendanceOldService.clockRecord(user,attendanceDate);
        return new JsonResult(1,map);
    }

    /**
     * 1.337首页之打卡记录
     *@Description 获取某天的打卡记录(只返回了打卡记录的整体列表)
     *@auther 李娅星
     *@date 2025/3/19
     *@param attendanceDate 要查询的某天(年-月-日)
     */
    @ResponseBody
    @RequestMapping("/clockRecordDate.do")
    public JsonResult clockRecordDate(User user,Date attendanceDate, PunchDto search, PageInfo pageInfo) {
        Map<String, Object> map = new HashMap<>();
        if (attendanceDate==null){
            attendanceDate = NewDateUtils.today();
        }
        List<PunchDto> punchList = workAttendanceService.getPersonnelAttendancePunches(attendanceDate,Period.day , null, search, user, pageInfo);
        Collections.reverse(punchList);
        map.put("punchList",punchList);
        return new JsonResult(1,map, PageInfo.checkValid(pageInfo) ? pageInfo : null);
    }

    /**
     *@Description 获取打卡记录的详情
     *@auther 李娅星
     *@date 2023/10/12
     *@param punchId 打卡id
     */
    @ResponseBody
    @RequestMapping("/clockRecordDetail.do")
    public JsonResult clockRecord(User user,Integer punchId) {
        Map<String, Object> map = new HashMap<>();
        if (punchId!=null) {
            map = workAttendanceOldService.clockRecordDetail(user, punchId);
            return new JsonResult(1,map);
        }else {
            return new JsonResult(new MyException("400","传参有误"));
        }
    }

    /**
     *@Description 获取模式更改记录
     *@auther 李娅星
     *@date 2023/10/12
     *@param
     */
    @ResponseBody
    @RequestMapping("/modelRecord.do")
    public JsonResult modelRecord(User user) {
        Map<String, Object> map = new HashMap<>();
        map = workAttendanceOldService.modelRecord(user);
        return new JsonResult(1,map);
    }

    /**
    *@Description 获取模式更改记录的详情
    *@auther 李娅星
    *@date 2023/11/13
    *@param
    */
    @ResponseBody
    @RequestMapping("/modelRecordDetail.do")
    public JsonResult modelRecordDetail(User user,Integer historyId) {
        Map<String, Object> map = new HashMap<>();
        if (historyId!=null){
            map = workAttendanceOldService.modelRecordDetail(user,historyId);
            return new JsonResult(1,map);
        }else {
            return new JsonResult(new MyException("400","传参有误"));
        }
    }

    /**
     * 1.323 考勤之打卡记录 考勤记录查询接口
     * @param start 开始时间
     * @param end 结束时间
     * @param search 模糊查询条件
     * @return
     */
    @ResponseBody
    @RequestMapping("/getPersonnelAttendancePunches.do")
    public JsonResult getPersonnelAttendancePunches (Date start, Byte period, Date end, PunchDto search, User user, PageInfo pageInfo) {
        Period p = Period.getByIndex(period);
        return new JsonResult(1, workAttendanceService.getPersonnelAttendancePunches(start, p, end, search, user, pageInfo), PageInfo.checkValid(pageInfo) ? pageInfo : null);
    }

    /**
     * 1.323 考勤模式查询接口 是否考勤宝模式
     */
    @ResponseBody
    @RequestMapping("/getAttendancePattern.do")
    public JsonResult getAttendancePattern(User user) {
//        PersonnelAttendanceConfig config = workAttendanceOldService.getPersonnelAttendanceConfigByDept(user.getOid(), null, new Date(System.currentTimeMillis()));
//        Byte result = config==null?null:config.getAttendancePattern();
//        if(result==null) {
//            return new JsonResult(new MyException("-1", "机构未启用考勤"));
//        }
//        return new JsonResult(result.intValue(), JSON.parseObject(ConfigAttendancePattern.getByIndex(result).toString()));
        ConfigAttendancePattern cap;
        Byte result;
        PersonnelAttendanceConfig config = workAttendanceService.getOnePersonnelAttendanceConfigByOpenDate(user.getOid(), new Date(System.currentTimeMillis()));
        if(config!=null && (result=config.getAttendancePattern())!=null && (cap=ConfigAttendancePattern.getByIndex(result))!=null) {
            return new JsonResult(result.intValue(), JSON.parseObject(cap.toString()));
        } else {
            return new JsonResult(new MyException("-1", "机构未启用考勤"));
        }
    }
    @AuthPassport(validate = false)
    @ResponseBody
    @RequestMapping("/testRun.do")
    public JsonResult testRun(Date now, Integer oid) {
        workAttendanceService.debugRunAttendanceTask(now, oid);
        return new JsonResult(1, "操作成功");
    }
    @AuthPassport(validate = false)
    @ResponseBody
    @RequestMapping("/testEvent.do")
    public JsonResult testEvent(Date now, Integer uid) {
        User user = userService.getUserByID(uid);
        workAttendanceService.setAttendanceUserEvent(user, now, WorkAttendanceService.AttendanceType.leave);
        workAttendanceService.setAttendanceUserEvent(user, now, WorkAttendanceService.AttendanceType.leave);
        workAttendanceService.updateAttendanceUserEvents();
        return new JsonResult(1, "操作成功");
    }
}