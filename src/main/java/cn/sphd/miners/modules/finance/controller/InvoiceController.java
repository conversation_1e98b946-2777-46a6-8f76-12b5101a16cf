package cn.sphd.miners.modules.finance.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.commodity.entity.SlCustomer;
import cn.sphd.miners.modules.finance.entity.FinanceInvoice;
import cn.sphd.miners.modules.finance.entity.FinanceInvoiceDetail;
import cn.sphd.miners.modules.finance.entity.FinanceInvoiceDetailHistory;
import cn.sphd.miners.modules.finance.entity.FinanceInvoiceSetting;
import cn.sphd.miners.modules.finance.service.InvoiceService;
import cn.sphd.miners.modules.sales.service.PdCustomerService;
import cn.sphd.miners.modules.sales.service.SaleService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
 * Created by Administrator on 2017/7/4.
 * 发票管理
 * 通用框架1.56销售之开票，财务-开票登记部分的角标
 */
@Controller
@RequestMapping("/invoice")
public class InvoiceController {

    @Autowired
    InvoiceService invoiceService;
    @Autowired
    PdCustomerService pdCustomerService;
    @Autowired
    UserService userService;
    @Autowired
    SaleService saleService;

    @RequestMapping("/toPage.do")
    public String toPage(){
        return "/finance/invoice";
    }

    /**
     *<AUTHOR>
     *@date 2017/7/10 11:07
     *查询机构中的发票设置
     */
    @ResponseBody
    @RequestMapping("/getFinanceInvoiceSetting.do")
    public void getFinanceInvoiceSetting(User user, HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        Integer oid = user.getOid();
        List<FinanceInvoiceSetting> financeInvoiceSettings = invoiceService.getFinanceInvoiceSettingByOid(oid);
        if (financeInvoiceSettings.size()>0){
            map.put("financeInvoiceSetting",financeInvoiceSettings);
        }else {
            map.put("financeInvoiceSetting","0");
        }

        ObjectToJson.objectToJson1(map,new String[]{"organization","financeInvoiceSettingHistoryHashSet"},response);
    }

    /**
     *<AUTHOR>
     *@date 2017/7/11 9:53
     *新增发票(此接口已改为1.56版)
    */
    @ResponseBody
    @RequestMapping("/addInvoice.do")
    public void addInvoice(User user,FinanceInvoice financeInvoice, HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        invoiceService.addInvoice(financeInvoice,user,map);  //新增发票
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    /**
     *<AUTHOR>
     *@date 2017/7/11 15:30
     *发票管理列表
     * 1.182发票管理优化2101(2021/8/27 改)
    */
    @ResponseBody
    @RequestMapping("/getAllInvoices.do")
    public JsonResult getAllInvoices(User user) {
        Map<String,Object> map = new HashMap<>();
        Integer oid = user.getOid();
        Integer invoiceSettingNum = invoiceService.getInvoiceSettingNumByOid(oid); //是否录入了发票设置
        map.put("invoiceSettingNum",invoiceSettingNum);
        if (oid!=null){
            List<Map<String,Object>> financeInvoices = invoiceService.getAllInvoices(oid,null,null,null,1);
            map.put("financeInvoices",financeInvoices);
        }else {
            map.put("financeInvoices","");
        }
        return new JsonResult(1,map);
//        ObjectToJson.objectToJson1(map,new String[]{"financeInvoiceDetailHashSet","org"},response);
    }

    /**
     *<AUTHOR>
     *@date 2017/7/12 9:47
     *起止号码显示
    */
    @ResponseBody
    @RequestMapping("/getEndNo.do")
    public void getEndNo(String beginNo,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        String beginNo2 = "";
        String beginNo22 = "";
        Integer endNo = null;
        String endNo1 = null;
        if (beginNo.substring(0,1).equals("0")){
            for (int i=0;i<beginNo.length();i++){
                if (beginNo.substring(i,i+1).equals("0")){
                    beginNo2 = beginNo.substring(0,i+1).trim();
                    beginNo22 = beginNo.substring(0,i).trim();
                }
            }
            endNo = Integer.parseInt(beginNo)+19;
            endNo1 = beginNo2+endNo.toString();
            if (endNo1.length()>beginNo.length()){
                endNo1 = beginNo22+endNo.toString();
            }
        }else {
            endNo = Integer.parseInt(beginNo)+19;
            endNo1 = endNo.toString();
        }
        map.put("endNo",endNo1);
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    /**
     *<AUTHOR>
     *@date 2017/7/12 14:30
     *发票详情的查询列表
    */
    @ResponseBody
    @RequestMapping("/getAllInvoiceDetails.do")
    public void getAllInvoiceDetails(User user,Integer invoiceId,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        if (invoiceId!=null){
            List<FinanceInvoiceDetail> financeInvoiceDetails = invoiceService.getAllInvoiceDetailsByInvoiceId(invoiceId,null,null,null,user.getOid());
            map.put("financeInvoiceDetails",financeInvoiceDetails);
        }else {
            map.put("financeInvoiceDetails","");
        }
        ObjectToJson.objectToJson1(map,new String[]{"invoiceReg","financeInvoiceDetailHistoryHashSet"},response);
    }

    /**
     *<AUTHOR>
     *@date 2019/1/22 9:41
     *发票作废(此接口已改为1.56版)
     * state 空或1-未使用,2-已使用,3-作废
    */
    @ResponseBody
    @RequestMapping("/cancelInvoice.do")
    public void cancelInvoice(User user,Integer invoiceDetailId, String reason, String state, HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        if (invoiceDetailId!=null && state!=null){
            invoiceService.cancelInvoice(invoiceDetailId,reason,state,user);
            map.put("state",1);  //修改成功
        }else {
            map.put("state",0);  //修改失败
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    /**
     *<AUTHOR>
     *@date 2019/1/23 11:24
     *开票录入(已修改1.56版本，只将方法移到service层了)
    */
    @ResponseBody
    @RequestMapping("/addInvoiceDetail.do")
    public void addInvoiceDetail(User user,FinanceInvoiceDetail financeInvoiceDetail, HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        if (financeInvoiceDetail.getId()!=null){
            map = invoiceService.addInvoiceDetail(financeInvoiceDetail,user,map);  //开票录入
        }else {
            map.put("state",0);  //失败
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    /**
     *<AUTHOR>
     *@date 2017/7/14 13:49
     *获取系统的客户
    */
    @ResponseBody
    @RequestMapping("/getAllCustomer.do")
    public void getAllCustomer(User user,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        Integer oid = user.getOid();
        List<SlCustomer> slCustomers = pdCustomerService.getPdCustomers1(oid,"",null);

        map.put("Customers", slCustomers);
        ObjectToJson.objectToJson1(map,new String[]{"TPdCustomerHistoryPdCustomerViaCustomer","TPdMerchandisePdCustomerViaCustomer",
                                                     "slOrdersHashSet","updateDate","updateName","updator"},response);
    }

    /**
     *<AUTHOR>
     *@date 2017/7/18 9:37
     *获取系统的财务人员
     */
    @ResponseBody
    @RequestMapping("/getAllFinanceers.do")
    public void getAllFinanceers(HttpServletResponse response, User user) throws IOException {
        Map<String,Object> map = new HashMap<>();
        Integer oid = user.getOid();
        User userFinance = userService.getUserByRoleCode(oid,"finance");  //查找机构下的财务
//        List<User> users = userService.getUserByOidAndUserTypeList(oid,1,"1");  //查找机构下的财务
        map.put("user",userFinance);
        ObjectToJson.objectToJson1(map,new String[]{"parent","userMessages","personnelOccupations","personalEducations","personnelSalaryLogUser","personnelFolksHashSet",
                "personalRewardPunishments","personalAssessments","personnelLeaveUser","personnelOvertimeUser",
                "userLogs","personnelReimburseHashSet","userPopedomHashSet","personnelInterviewHashSet","roles","organization","submit","volume","lv","money","submitM",
                "volumeM","lvM","moneym","submitY","volumeY","lvY","moneyY","userLogType","userLogOperateTime","userLogIp",
                "handleTime","tokenId","leaderName","ordinaryEmployees","LoginStatus","userType","isNew","admins","fingerID",
                "actualAddress","homeAddress","transferReason","transferTime","path","imgPath","imgName","creator","createTime"},response);
    }


    /**
     *<AUTHOR>
     *@date 2019/1/23 11:54
     *启用
    */
    @ResponseBody
    @RequestMapping("/enable.do")
    public void enable(Integer invoiceDetailId,HttpServletResponse response,User user) throws IOException {
        Map<String,Object> map = new HashMap<>();
        if (invoiceDetailId!=null) {
            user = userService.getUserByID(user.getUserID());
            invoiceService.getEnable(invoiceDetailId, user);
            map.put("state", 1);
        }else {
            map.put("state", 0);
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    /**
     *<AUTHOR>
     *@date 2019/1/25 10:54
     *发票管理模块的发票修改
     * operation 1-之前操作失误，该发票实际尚未开具 2-修改已开发票里面的内容 3-作废这张发票
    */
    @ResponseBody
    @RequestMapping("/updateInvoice.do")
    public void updateInvoice(FinanceInvoiceDetail invoiceDetail,String operation,HttpServletResponse response,User user) throws IOException {
        Map<String,Object> map = new HashMap<>();
        user = userService.getUserByID(user.getUserID());
        invoiceService.updateInvoiceDetail(invoiceDetail,operation,map,user);
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    /**
     *<AUTHOR>
     *@date 2019/1/25 11:33
     *查找发票操作记录列表
    */
    @ResponseBody
    @RequestMapping("/getRecords.do")
    public void getRecords(Integer invoiceDetailId,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        map = invoiceService.getRecords(invoiceDetailId,map);
       ObjectToJson.objectToJson1(map,new String[]{"invoiceDetail"},response);
    }

    /**
     *<AUTHOR>
     *@date 2019/1/25 14:19
     *查看历史详情
    */
    @ResponseBody
    @RequestMapping("/getInvoiceDetailHistoryDetail.do")
    public void getInvoiceDetailHistoryDetail(Integer invoiceDetailHistoryId,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        FinanceInvoiceDetailHistory financeInvoiceDetailHistory = invoiceService.getInvoiceDetailHistoryDetail(invoiceDetailHistoryId);
        map.put("financeInvoiceDetailHistory",financeInvoiceDetailHistory);
        ObjectToJson.objectToJson1(map,new String[]{"invoiceDetail"},response);
    }

    /**
     *<AUTHOR>
     *@date 2019/1/21 14:53
     *查找对应类型下的所有可用支票
     * type 1-专用票,2-普通票,3-其他发票 4-普通票和其他发票
     */
    @ResponseBody
    @RequestMapping("/getInvoices.do")
    public void getInvoices(String type,User user,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        Integer oid = user.getOid();
        List<FinanceInvoiceDetail> invoiceDetails = invoiceService.getInvoiveDetailByOrgAndType(oid,type);  //接口没写完
        map.put("invoiceDetails",invoiceDetails);
        ObjectToJson.objectToJson1(map,new String[]{"invoiceReg","financeInvoiceDetailHistoryHashSet"},response);
    }

    /**
     *<AUTHOR>
     *@date 2019/1/25 17:40
     *查询某一张发票(使用service方法)
    */
    @ResponseBody
    @RequestMapping("/getInvoiceDetail.do")
    public void getInvoiceDetail(Integer invoiceDetailId,HttpServletResponse response) throws IOException {
        FinanceInvoiceDetail invoiceDetail = invoiceService.getFinanceInvoiceDetailById(invoiceDetailId);
        Map<String,Object> map = new HashMap<>();
        map.put("invoiceDetail",invoiceDetail);
        ObjectToJson.objectToJson1(map,new String[]{"invoiceReg","financeInvoiceDetailHistoryHashSet"},response);
    }

    /**
     *<AUTHOR>
     *@date 2019/1/29 16:25
     *查询开票申请的开票设置提醒
    */
    @ResponseBody
    @RequestMapping("/getRemind.do")
    public void getRemind(HttpServletResponse response,User user) throws IOException {
        Map<String,Object> map = new HashMap<>();
        Integer oid = user.getOid();
        user = userService.getUserByID(user.getUserID());
        map = invoiceService.getRemind(oid,user,map);
        ObjectToJson.objectToJson1(map,new String[]{"organization","financeInvoiceSettingHistoryHashSet"},response);
    }

    /**
     *<AUTHOR>
     *@date 2019/1/29 17:22
     *开票申请是否还要提示
     * enable 1-以后继续提醒  0-不再提醒
    */
    @ResponseBody
    @RequestMapping("/updateEnbale.do")
    public void updateEnbale(Boolean enable,HttpServletResponse response,User user) throws IOException {
        Map<String,Object> map = new HashMap<>();
        Integer oid = user.getOid();
        if (enable!=null) {
            map = invoiceService.updateEnbale(oid, enable, map);
        }else {
            map.put("state",0);
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }


    /**
     * <AUTHOR>
     * 根据登录人获取授权客户
     */
    @ResponseBody
    @RequestMapping("/getWarrantCustomerByUser.do")
    public JsonResult getWarrantCustomerByUser(User user){

        List<Map<String ,Object>> list=pdCustomerService.getWarrantCustomerList(user,"hklr");
        return new JsonResult(1,list);
    }

    //-------------------------以下是1.155I/A首页之财会2的“申领来的发票”部分---------------------------------
    /**
     *<AUTHOR>
     *@date 2021/3/10
     *获取申领来的空白发票列表【和申领记录】
    */
    @ResponseBody
    @RequestMapping("/getAllInvoiceNum.do")
    public JsonResult getAllInvoiceNum(User user){
        Map<String ,Object> map = new HashMap<>();
        Integer oid = user.getOid();
        Integer specialNum = invoiceService.getBlackInvoiceNum(oid,"1");  //1-增值税专用票
        Integer ordinaryNum = invoiceService.getBlackInvoiceNum(oid,"2");  //2-增值税普通票
        Integer otherNum = invoiceService.getBlackInvoiceNum(oid,"3");  //3-其它普通票
        Integer yearTime = invoiceService.getInvoiceNum(oid,NewDateUtils.getYear(new Date()));  //今年申领次数
        Integer lastYearTime = invoiceService.getInvoiceNum(oid,NewDateUtils.getYear(NewDateUtils.changeYear(new Date(),-1)));  //去年申领次数
        Integer beforeYearTime = invoiceService.getInvoiceNum(oid,NewDateUtils.getYear(NewDateUtils.changeYear(new Date(),-2)));  //前年申领次数
        map.put("specialNum",specialNum);
        map.put("ordinaryNum",ordinaryNum);
        map.put("otherNum",otherNum);
        map.put("yearTime",yearTime);
        map.put("lastYearTime",lastYearTime);
        map.put("beforeYearTime",beforeYearTime);
        return new JsonResult(1, map);
    }

    /**
     *<AUTHOR>
     *@date 2021/3/10 18:53
     *查询某种发票的空白发票列表
     * type 1-增值税专用票  2-增值税普通票 3-其它普通票
    */
    @ResponseBody
    @RequestMapping("getBlackInvoiceDetails.do")
    public JsonResult getBlackInvoiceDetails(User user,String type) {
        Map<String,Object> map = new HashMap<>();
        if (user!=null&&type!=null) {
            map = invoiceService.getBlackInvoiceDetails(user.getOid(), type, "1");
        }
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2021/3/10 15:30
     *某年申领的所有发票列表
     * yearDate-年月日（某年中的具体某天）
     */
    @ResponseBody
    @RequestMapping("/getInvoiceList.do")
    public JsonResult getInvoiceList(User user,String yearDate){
        Map<String,Object> map = new HashMap<>();
        Date beginDate = NewDateUtils.getNewYearsDay(NewDateUtils.dateFromString(yearDate,"yyyy-MM-dd"));
        Date endDate = NewDateUtils.getLastTimeOfYear(NewDateUtils.dateFromString(yearDate,"yyyy-MM-dd"));
        List<FinanceInvoice> financeInvoices = new ArrayList<>();
        if (user.getOid()!=null){
            financeInvoices = invoiceService.getAllInvoicesByOid(user.getOid(),beginDate,endDate,null,null);
        }
        map.put("financeInvoices",financeInvoices);
        map.put("num",financeInvoices.size());
        map.put("beginDate",beginDate);  //开始时间
        map.put("endDate",endDate);   //结束时间
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2021/3/9 20:47
     *某次申领的发票的详情列表
    */
    @ResponseBody
    @RequestMapping("/getInvoiceDetails.do")
    public JsonResult getInvoiceDetails(User user,Integer invoiceId) {
        Map<String,Object> map = new HashMap<>();
        FinanceInvoice financeInvoice = invoiceService.getInvoiceById(invoiceId);
        List<FinanceInvoiceDetail> financeInvoiceDetails = invoiceService.getAllInvoiceDetailsByInvoiceId(invoiceId,null,null,null,user.getOid());
        map.put("financeInvoice",financeInvoice);
        map.put("financeInvoiceDetails",financeInvoiceDetails);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2022/1/27
     *发票的使用记录(按年)
     */
    @ResponseBody
    @RequestMapping("/getInvoiceUsed.do")
    public JsonResult getInvoiceUsed(User user) {
        Map<String,Object> map = new HashMap<>();
        Date today = NewDateUtils.today();
        Date lastYear = NewDateUtils.changeYear(today,-1);
        Date  beforeLastYear = NewDateUtils.changeYear(today,-2);
        Map<String,Object> newYearUsed = invoiceService.getInvoiceUsed(user.getOid(),today,"2",null,1);  //今年
        Map<String,Object> lastYearUsed = invoiceService.getInvoiceUsed(user.getOid(),lastYear,"2",null,1);  //去年
        Map<String,Object> beforeLastYearUsed = invoiceService.getInvoiceUsed(user.getOid(),beforeLastYear,"2",null,1);  //前年
        map.put("newYearUsed",newYearUsed);
        map.put("lastYearUsed",lastYearUsed);
        map.put("beforeLastYearUsed",beforeLastYearUsed);
        return new JsonResult(1,map);
    }

    /**
    *使用记录-获取各种发票统计(有月列表的)【月列表中查看所有种类的发票】
    *@auther 李娅星
    *@date 2022/1/28
    * beginDate：某年的某一天(年月日)    typeMonth：1-有月列表 2-没有月列表    dateType 1-按年统计 2-按月统计
    */
    @ResponseBody
    @RequestMapping("getInvoiceStatistics.do")
    public JsonResult getInvoiceStatistics(User user,String beginDate,Integer typeMonth,Integer dateType){
        Map<String,Object> map = new HashMap<>();
        Date beginTime = NewDateUtils.dateFromString(beginDate,"yyyy-MM-dd");
        Date endTime = new Date();
        if (1==dateType) {   //按年
            if (NewDateUtils.getYear(beginTime) < NewDateUtils.getYear(new Date())) {  //查询往年的
                beginTime = NewDateUtils.getNewYearsDay(beginTime);  //获取元旦当天的0点
                endTime = NewDateUtils.getLastTimeOfYear(beginTime);  //指定日期那年的最后1毫秒
            }
        }else if (2==dateType){   //按月
            beginTime = NewDateUtils.changeMonth(beginTime,0);
            if (!NewDateUtils.getYearMonth(beginTime).equals(NewDateUtils.getYearMonth(endTime))){
                endTime = NewDateUtils.getLastTimeOfMonth(beginTime);
            }
        }
        Map<String,Object> specialInvoice = invoiceService.getInvoiceUsed(user.getOid(),beginTime,"2","1",dateType);  //增值税专用票
        Map<String,Object> ordinaryInvoice = invoiceService.getInvoiceUsed(user.getOid(),beginTime,"2","2",dateType);  //增值税普通票
        Map<String,Object> otherInvoice = invoiceService.getInvoiceUsed(user.getOid(),beginTime,"2","3",dateType);  //其他普通票

        Long specialNum = (Long) specialInvoice.get("num");
        BigDecimal specialAmount = (BigDecimal) specialInvoice.get("totalAmount")!=null?(BigDecimal) specialInvoice.get("totalAmount"):new BigDecimal(0);
        Long ordinaryNum = (Long) ordinaryInvoice.get("num");
        BigDecimal ordinaryAmount = (BigDecimal) ordinaryInvoice.get("totalAmount")!=null?(BigDecimal) ordinaryInvoice.get("totalAmount"):new BigDecimal(0);
        Long otherNum = (Long) otherInvoice.get("num");
        BigDecimal otherAmount = (BigDecimal) otherInvoice.get("totalAmount")!=null?(BigDecimal) otherInvoice.get("totalAmount"):new BigDecimal(0);
        Integer totalNum = specialNum.intValue()+ordinaryNum.intValue()+otherNum.intValue();
        BigDecimal totalAmount = specialAmount.add(ordinaryAmount).add(otherAmount);

        if (1==typeMonth){  //有月列表
            List<Map> mapList = invoiceService.getInvoiceMonthUsed(user.getOid(),beginTime,endTime,"2",null,2);
            map.put("monthList",mapList);  //月列表
        }
        map.put("totalNum",totalNum);  //总数
        map.put("totalAmount",totalAmount); //总金额
        map.put("specialInvoice",specialInvoice);
        map.put("ordinaryInvoice",ordinaryInvoice);
        map.put("otherInvoice",otherInvoice);
        map.put("beginTime",beginTime);
        map.put("endTime",endTime);
        return new JsonResult(1,map);
    }
    
    /**
    *统计某类型发票某年(月列表)
    *@auther 李娅星
    *@date 2022/1/29
    *type 1-增值税专用票,2-增值税普通票,3-其它普通票
    */
    @ResponseBody
    @RequestMapping("/getInvoiceType.do")
    public JsonResult getInvoiceType(User user,String type,String beginDate,String endDate){
        Map<String,Object> map = new HashMap<>();
        Date beginTime = NewDateUtils.dateFromString(beginDate,"yyyy-MM-dd");
        Date endTime = NewDateUtils.dateFromString(endDate,"yyyy-MM-dd");
        List<Map> mapList = invoiceService.getInvoiceMonthUsed(user.getOid(),beginTime,endTime,"2",type,2);
        map.put("monthList",mapList);  //月列表
        return new JsonResult(1,map);
    }

    /**
    *某类型某月的发票列表
    *@auther 李娅星
    *@date 2022/1/29
    */
    @ResponseBody
    @RequestMapping("/getInvoiceListMonth.do")
    public JsonResult getInvoiceListMonth(User user,String type,String beginDate,String endDate){
        Map<String,Object> map = new HashMap<>();
        Date beginTime = NewDateUtils.dateFromString(beginDate,"yyyy-MM-dd");
        Date endTime = NewDateUtils.getLastTimeOfDay(NewDateUtils.dateFromString(endDate,"yyyy-MM-dd"));
        List<FinanceInvoiceDetail> financeInvoiceDetails = invoiceService.getAllInvoiceDetailsByInvoiceId(null,beginTime,endTime,type,user.getOid());
        Map<String,Object> monthStatistics = invoiceService.getInvoiceUsed(user.getOid(),beginTime,"2",type,2);  //其他普通票
        map.put("financeInvoiceDetails",financeInvoiceDetails);  //详情列表
        map.put("monthStatistics",monthStatistics);  //总结的金额和张数
        map.put("beginTime",beginTime);  //开始时间
        map.put("endTime",endTime);  //结束时间
        return new JsonResult(1,map);
    }

    /**
    *获取发票详情
    *@auther 李娅星
    *@date 2022/2/10
    */
    @ResponseBody
    @RequestMapping("/getInvoiceDetailAll.do")
    public JsonResult getInvoiceDetail(Integer invoiceDetailId,User user){
        Map<String,Object> map = new HashMap<>();
        FinanceInvoiceDetail financeInvoiceDetail = invoiceService.getFinanceInvoiceDetailById(invoiceDetailId);
        Map invoiceDetail = saleService.getApplicationGoodsDetails(invoiceDetailId,null, user.getOid());
        List data = (List) invoiceDetail.get("data");
        map.put("invoiceDetail",data);  //销售使用详情
        map.put("financeInvoiceDetail",financeInvoiceDetail);  //财务-开票录入的
        return new JsonResult(1,map);
    }

}
