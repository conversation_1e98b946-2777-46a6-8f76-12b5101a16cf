<template>
	<div class="privateSpace">
    <router-view />
    <component :is="TabBar" :activeIndex0="activeIndex" :isShowData="isShowData" :mainTab="2"></component>

  </div>
</template>

<script setup>
  import TabBar from '../../../components/tabBar.vue'
  const goPage = function(url){
    this.$router.push({name: url});
  }
  const accountDetails = "/pages/accountDetails/accountDetails"
  let activeIndex = 1

</script>

<style lang="scss">
	.linehei{
		line-height: 60px;
	}
	.mar30 { 
		margin-top: 30px;
	}
	.user{
		width: 75%;
		.txt{
			font-size: 0.9em;
		} 
		.uni-icon{
			float: right;
			position: relative;
			top:16px;
		}
	}
	.img{ 
		width:120px;
		height: 120px;
		overflow: hidden;
		background-color: red;
		border-radius: 60px;
		.image{
			width:120px;
			height:120px;
		}
	}
	
</style>
