<!--按月查看按钮下的按日期查看-->
<template>
  <ul class="val_list">
    <li v-for="(item, index) in accountList" :key="index" class="ta-list">
      <div @click="rou(item)">
        <span>{{ item.name }}</span>
        <div class="current-balance-1">
          <div class="ty-color-green">
            <p>收入</p>
            <p>{{ item.income }}</p>
          </div>
        </div>
        <div class="current-balance-1 ty-color-red">
          <div>
            <p>支出</p>
            <p>{{ item.expend }}</p>
          </div>
        </div>
        <span>
          <el-icon><arrow-right /></el-icon>
        </span>
      </div>
    </li>
  </ul>
</template>

<script>
import finance from '@/mixins/finance.js'
export default {
  name: "timeBudget",
  mixins: [finance],
  data() {
    return {
      accountList: [
        {id: 0, name: "XXXX-XX-XX", income: "XX.XX", expend: "XX.XX", },
        {id: 1, name: "XXXX-XX-XX", income: "XX.XX", expend: "XX.XX", },
        {id: 0, name: "XXXX-XX-XX", income: "XX.XX", expend: "XX.XX", },
        {id: 0, name: "XXXX-XX-XX", income: "XX.XX", expend: "XX.XX", }
      ],
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList(){
      let org = this.auth.getOrg()
      this.accountList = []
      let params = { beginDate : "", endDate : "", state : 2 , oid: org.id }
      this.financeApi.getAllAccountDataByTimeApp(params).then(res1=>{
        let res = res1.data.data
        let accountPeriodList = res.accountPeriodList || []
        accountPeriodList.forEach(item=>{
          let iihk = { name: (new Date(item.beginDate).format('yyyy-MM-dd')) ,
            income: this.formatCurrency(item.credit) , expend: this.formatCurrency(item.debit) }
          this.accountList.push(iihk)
        })

      }).catch(err=>{
        console.log('err=', err)
        this.$message.error('链接失败，请重试！')
      })
    },
    rou(item) {
      if(item.name){
        localStorage.setItem('currentAccountDate_beginDate', item.name )
        let path = `/currentAccountDateDur/null`
        this.$router.push({ path:path })
      }else{
        this.$message.error('没有具体日期哦！')
      }
    },

  }
}
</script>

<style scoped lang="scss">
@import "../../../style/account.scss";
@import "../../../style/common.scss";

.ta-list {

}
.page{
  z-index: 88;
}
</style>