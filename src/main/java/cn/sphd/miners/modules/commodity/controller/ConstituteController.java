package cn.sphd.miners.modules.commodity.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.modules.commodity.entity.PdBase;
import cn.sphd.miners.modules.commodity.service.ConstituteService;
import cn.sphd.miners.modules.commodity.service.ProductService;
import cn.sphd.miners.modules.system.entity.User;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 构成管理
 */
@Controller
@RequestMapping("/constitute")
public class ConstituteController {

    @Autowired
    ConstituteService constituteService;

    @Autowired
    ProductService productService;


    /**
     * 主页跳转
     */
    @RequestMapping("/getHome.do")
    public String getHome(){

        return "" ;
    }

    /**
     * 获取主页数据
     */
    @ResponseBody
    @RequestMapping("/getHomeData.do")
    public Map<String,Object> getHomeData(User user){
        Integer oid= user.getOid();
        Map<String, Object> data=new HashMap<>();

        Integer b1=0;
        Integer b2=0;
        Integer b3=0;

        Integer g1=0;
        Integer g2=0;
        Integer g3=0;

        Integer c1=0;
        Integer c2=0;
        Integer c3=0;

        Integer h1=0;
        Integer h2=0;
        Integer h3=0;

        Integer daichuli=0;
        //获取所有产品
        List<PdBase> cp=productService.getListByType(oid,"1");

        for (PdBase b:cp){
            if("2".equals(b.getProcess())){
                b1++;
            }
            if("1".equals(b.getProcess())){
                b2++;
            }

            if("3".equals(b.getProcess())){
                b3++;
            }

            if("2".equals(b.getComposition())){
                c1++;
            }
            if("4".equals(b.getComposition())){
                c2++;
            }
            if("3".equals(b.getComposition())){
                c3++;
            }

            if("1".equals(b.getState())){
                daichuli++;
            }

            if("2".equals(b.getState())&&"2".equals(b.getStage())){
                daichuli++;
            }

            if("2".equals(b.getState())&&"3".equals(b.getStage())){
                daichuli++;
            }

        }

        //获取所有零件
        List<PdBase> lj=productService.getListByType(oid,"2");

        for (PdBase b:lj) {
            if("2".equals(b.getProcess())){
                g1++;
            }
            if("1".equals(b.getProcess())){
                g2++;
            }

            if("3".equals(b.getProcess())){
                g3++;
            }

            if("2".equals(b.getComposition())){
                h1++;
            }
            if("4".equals(b.getComposition())){
                h2++;
            }
            if("3".equals(b.getComposition())){
                h3++;
            }
        }
        data.put("b1",b1);
        data.put("b2",b2);
        data.put("b3",b3);
        data.put("g1",g1);
        data.put("g2",g2);
        data.put("g3",g3);
        data.put("c1",c1);
        data.put("c2",c2);
        data.put("c3",c3);
        data.put("h1",h1);
        data.put("h2",h2);
        data.put("h3",h3);



        data.put("daichuli",daichuli);

        Map<String,Object> map=new HashMap<>();
        map.put("code",200);
        map.put("data",data);
        return map ;
    }

    /**
     * 获取构成首页数据
     */
    @ResponseBody
    @RequestMapping("/getCompositionHomeData.do")
    public JsonResult getCompositionHomeData(User user){
        Integer oid= user.getOid();

        return new JsonResult(1,constituteService.getDataNumber(oid)) ;
    }


    /**
     * 获取待确认加工列表
     */
    @ResponseBody
    @RequestMapping("/getConfirmedList.do")
    public Map<String,Object> getConfirmedList(User user,Integer currPage, Integer pageSize){
        Integer oid= user.getOid();
        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }

        Map<String,Object> map=constituteService.getConfirmedList(oid,currPage,pageSize);


        map.put("currPage",currPage);

        return map ;
    }

    /**
     * 产品信息查看
     */
    @ResponseBody
    @RequestMapping("/getPdBase.do")
    public Map<String,Object> getPdBase(Integer id){
        Map<String,Object> map=new HashMap<>();

        PdBase pdBase=constituteService.getPdBase(id);

        map.put("code",200);
        map.put("data",pdBase);
        return map ;
    }
    /**
     * 待确认加工处理确认
     */
    @ResponseBody
    @RequestMapping("/processingConfirmation.do")
    public Map<String,Object> processingConfirmation(User user,String baseList, String process){
        Map<String,Object> map=new HashMap<>();
        

        String res=constituteService.processingConfirmation(baseList,process,user);

        if("success".equals(res)){
            map.put("code",200);
        }else{
            map.put("code",400);
        }


        return map ;
    }

    /**
     * 获取待确认构成列表
     */
    @ResponseBody
    @RequestMapping("/getCompositionToBeConfirmedList.do")
    public Map<String,Object> getCompositionToBeConfirmedList(User user,Integer currPage, Integer pageSize){
        Integer oid= user.getOid();
        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }

        Map<String,Object> map=constituteService.getCompositionToBeConfirmedList(oid,currPage,pageSize);


        map.put("currPage",currPage);

        return map ;
    }

    /**
     * 待确认构成处理确认
     */
    @ResponseBody
    @RequestMapping("/compositionToBeConfirmed.do")
    public Map<String,Object> compositionToBeConfirmed(String baseList, String composition){
        Map<String,Object> map=new HashMap<>();
        String res=constituteService.compositionToBeConfirmed(baseList,composition);

        if("success".equals(res)){
            map.put("code",200);
        }else{
            map.put("code",400);
        }


        return map ;
    }

    /**
     * 获取待拆分列表
     */
    @ResponseBody
    @RequestMapping("/getSplitList.do")
    public Map<String,Object> getSplitList(User user,Integer currPage, Integer pageSize){
        Integer oid= user.getOid();
        if (currPage == null || currPage== 0) {
            currPage = 1;//默认当前页
        }
        if(pageSize==null||pageSize==0){
            pageSize=20;
        }


        Map<String,Object> map=constituteService.getSplitList(oid,currPage,pageSize);


        map.put("currPage",currPage);

        return map ;
    }

    /**
     * 零组件录入
     * pdBaseId,产品，或零组件id
     */
    @ResponseBody
    @RequestMapping("/addPdBaseLingJian.do")
    public Map<String,Object> addPdBaseLingJian(User user, PdBase pdBase,Integer pdBaseId,Integer oldId,Integer  amount){
        Map<String,Object> map=new HashMap<String,Object>();
        
        if(StringUtils.isEmpty(pdBase.getInnerSn())){
            map.put("code",400);
            map.put("msg","请填写图号");
            return map;
        }
        if(StringUtils.isEmpty(pdBase.getName())){
            map.put("code",400);
            map.put("msg","请填写名称");
            return map;
        }
        if(StringUtils.isEmpty(pdBase.getUnit())){
            map.put("code",400);
            map.put("msg","请填写计量单位");
            return map;
        }

        if(amount==null||"0".equals(amount)){
            map.put("code",400);
            map.put("msg","请填装备数量");
            return map;
        }

        String res=constituteService.addPdBaseLingJian(pdBase,pdBaseId,oldId,user,amount);

        if("success".equals(res)){
            map.put("code",200);
        }else{
            map.put("code",400);
            map.put("msg",res);
        }

        return map;
    }

    /**
     * 根据产品，或零组件id 获取零组件列表
     */
    @ResponseBody
    @RequestMapping("/getPdOrLingJianList.do")
    public Map<String,Object> getPdOrLingJianList(Integer id){


        Map<String,Object> map=constituteService.getPdOrLingJianList(id);


        map.put("currPage",1);

        return map ;
    }

    /**
     * 拆分完成操作
     */
    @ResponseBody
    @RequestMapping("/splitComplete.do")
    public Map<String,Object> splitComplete(Integer id,User user){

        Map<String,Object> map=new HashMap<>();
        String res=constituteService.splitComplete(id,user);

        if("success".equals(res)){
            map.put("code",200);
        }else{
            map.put("code",400);
            map.put("msg",res);
        }

        return map;
    }

    /**
     * 零组件删除
     */
    @ResponseBody
    @RequestMapping("/lingJianDelete.do")
    public Map<String,Object> lingJianDelete(Integer id,Integer parentId,User user){


        Map<String,Object> map=new HashMap<>();



        String res=constituteService.lingJianDelete(id,parentId,user);

        if("success".equals(res)){
            map.put("code",200);
        }else{
            map.put("code",400);
            map.put("msg",res);
        }

        return map;
    }


    /**
     * 获取尚未选择的零组件
     */
    @ResponseBody
    @RequestMapping("/selectLingJianListByParent.do")
    public Map<String,Object> selectLingJianListByParent(User user,Integer id){
        Integer oid= user.getOid();
        Map<String,Object> map=new HashMap<>();



        List<Map<String,Object>> res=constituteService.selectLingJianListByParent(oid,id);


        map.put("data",res);
            map.put("code",200);


        return map;
    }
}
