<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.accountant.mapper.ReimburseRelevanceMapper" >

    <sql id="Base_Column_List" >
        id, org, module, subject_id AS subjectId, subject, fee_cat AS feeCat, sub_fee_cat AS subFeeCat, code, sub_code AS subCode, name,
        enabled, memo, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName, update_date AS updateDate
    </sql>
    <select id="getSingle" parameterType="TAccountantSubjectRelevance" >
        select
        <include refid="Base_Column_List" />
        from t_accountant_subject_relevance
        where id = #{id,jdbcType=INTEGER}
    </select>
    <select id="getRelevances" resultType="TAccountantSubjectRelevance">

        SELECT <include refid="Base_Column_List" />
         FROM `t_accountant_subject_relevance` WHERE org=#{org};
    </select>

    <select id="getByFeeCat" resultType="TAccountantSubjectRelevance">
        select
        <include refid="Base_Column_List" />
        from t_accountant_subject_relevance
        where fee_cat = #{feeCat} and code=#{code} and org=#{org}
    </select>
    <select id="getListByFeeCat"
            resultType="TAccountantSubjectRelevance">
        select
        <include refid="Base_Column_List" />
        from t_accountant_subject_relevance
        where fee_cat = #{feeCat} and org=#{org}
    </select>

    <delete id="delete" parameterType="TAccountantSubjectRelevance" >
        delete from t_accountant_subject_relevance
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <delete id="deleteDisable">
        delete from t_accountant_subject_relevance
        where fee_cat = #{feeCat} and code=#{code}
    </delete>
    <delete id="deleteByOrg">
        delete from t_accountant_subject_relevance
        where org = #{org}
    </delete>

    <insert id="insert" parameterType="TAccountantSubjectRelevance" useGeneratedKeys="true" keyProperty="id">
        insert into t_accountant_subject_relevance
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="org != null" >
                org,
            </if>
            <if test="module != null" >
                module,
            </if>
            <if test="subjectId != null" >
                subject_id,
            </if>
            <if test="subject != null" >
                subject,
            </if>
            <if test="feeCat != null" >
                fee_cat,
            </if>
            <if test="subFeeCat != null" >
                sub_fee_cat,
            </if>
            <if test="code != null" >
                code,
            </if>
            <if test="subCode != null" >
                sub_code,
            </if>
            <if test="name != null" >
                name,
            </if>
            <if test="enabled != null" >
                enabled,
            </if>
            <if test="memo != null" >
                memo,
            </if>
            <if test="creator != null" >
                creator,
            </if>
            <if test="createName != null" >
                create_name,
            </if>
            <if test="createDate != null" >
                create_date,
            </if>
            <if test="updator != null" >
                updator,
            </if>
            <if test="updateName != null" >
                update_name,
            </if>
            <if test="updateDate != null" >
                update_date,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=INTEGER},
            </if>
            <if test="org != null" >
                #{org,jdbcType=INTEGER},
            </if>
            <if test="module != null" >
                #{module,jdbcType=VARCHAR},
            </if>
            <if test="subjectId != null" >
                #{subjectId,jdbcType=INTEGER},
            </if>
            <if test="subject != null" >
                #{subject,jdbcType=VARCHAR},
            </if>
            <if test="feeCat != null" >
                #{feeCat,jdbcType=INTEGER},
            </if>
            <if test="subFeeCat != null" >
                #{subFeeCat,jdbcType=INTEGER},
            </if>
            <if test="code != null" >
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="subCode != null" >
                #{subCode,jdbcType=VARCHAR},
            </if>
            <if test="name != null" >
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="enabled != null" >
                #{enabled,jdbcType=BIT},
            </if>
            <if test="memo != null" >
                #{memo,jdbcType=VARCHAR},
            </if>
            <if test="creator != null" >
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="createName != null" >
                #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null" >
                #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updator != null" >
                #{updator,jdbcType=INTEGER},
            </if>
            <if test="updateName != null" >
                #{updateName,jdbcType=VARCHAR},
            </if>
            <if test="updateDate != null" >
                #{updateDate,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="update" parameterType="TAccountantSubjectRelevance" >
        update t_accountant_subject_relevance
        <set >
            <if test="org != null" >
                org = #{org,jdbcType=INTEGER},
            </if>
            <if test="module != null" >
                module = #{module,jdbcType=VARCHAR},
            </if>
            <if test="subjectId != null" >
                subject_id = #{subjectId,jdbcType=INTEGER},
            </if>
            <if test="subject != null" >
                subject = #{subject,jdbcType=VARCHAR},
            </if>
            <if test="feeCat != null" >
                fee_cat = #{feeCat,jdbcType=INTEGER},
            </if>
            <if test="subFeeCat != null" >
                sub_fee_cat = #{subFeeCat,jdbcType=INTEGER},
            </if>
            <if test="code != null" >
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="subCode != null" >
                sub_code = #{subCode,jdbcType=VARCHAR},
            </if>
            <if test="name != null" >
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="enabled != null" >
                enabled = #{enabled,jdbcType=BIT},
            </if>
            <if test="memo != null" >
                memo = #{memo,jdbcType=VARCHAR},
            </if>
            <if test="creator != null" >
                creator = #{creator,jdbcType=INTEGER},
            </if>
            <if test="createName != null" >
                create_name = #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null" >
                create_date = #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updator != null" >
                updator = #{updator,jdbcType=INTEGER},
            </if>
            <if test="updateName != null" >
                update_name = #{updateName,jdbcType=VARCHAR},
            </if>
            <if test="updateDate != null" >
                update_date = #{updateDate,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByFeeCat">
        update t_accountant_subject_relevance
        <set >
            <if test="org != null" >
                org = #{org,jdbcType=INTEGER},
            </if>
            <if test="module != null" >
                module = #{module,jdbcType=VARCHAR},
            </if>
            <if test="subjectId != null" >
                subject_id = #{subjectId,jdbcType=INTEGER},
            </if>
            <if test="subject != null" >
                subject = #{subject,jdbcType=VARCHAR},
            </if>
            <if test="feeCat != null" >
                fee_cat = #{feeCat,jdbcType=INTEGER},
            </if>
            <if test="subFeeCat != null" >
                sub_fee_cat = #{subFeeCat,jdbcType=INTEGER},
            </if>
            <if test="code != null" >
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="subCode != null" >
                sub_code = #{subCode,jdbcType=VARCHAR},
            </if>
            <if test="name != null" >
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="enabled != null" >
                enabled = #{enabled,jdbcType=BIT},
            </if>
            <if test="memo != null" >
                memo = #{memo,jdbcType=VARCHAR},
            </if>
            <if test="creator != null" >
                creator = #{creator,jdbcType=INTEGER},
            </if>
            <if test="createName != null" >
                create_name = #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createDate != null" >
                create_date = #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="updator != null" >
                updator = #{updator,jdbcType=INTEGER},
            </if>
            <if test="updateName != null" >
                update_name = #{updateName,jdbcType=VARCHAR},
            </if>
            <if test="updateDate != null" >
                update_date = #{updateDate,jdbcType=TIMESTAMP},
            </if>
        </set>
        where fee_cat = #{feeCat} and code=#{code}
    </update>

</mapper>