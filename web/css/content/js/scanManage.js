 
$(function(){
	App.init(); 
	getScanList(1,5);

});
// 动态插入控件
function insertUploadyfy(){
	var str = "<div id='upload'></div>";
	$("#scanUpload").html(str);
	// init Uploadfy
	$('#upload').Huploadify({
		auto:false,
		fileTypeExts:'*.jpg;*.png;*.jpeg',
		multi:true,
		formData:{"addName":$("#addName").val()},
		fileSizeLimit:99999999,
		showUploadedPercent:true, 
		showUploadedSize:true,
		removeTimeout:9999999,
		uploader:"site_uploadPic.action",
		onUploadStart:function(){ },
		onInit:function(){ },
		onUploadComplete:function(fileObj){
		},
		onDelete:function(file){
			console.log(''+file);
			console.log(file);
		}
	});
}

// 显示扫描列表
function getScanList(cur , perNum){
	$.ajax({
		url:"site_showpic.action",
		data:{"pernum":perNum , "curpage":cur },
		type:"post",
		timeout:5000 ,
		beforeSend:function(){ loading.open() ; ajaxArr[1] = 1 ; },
		success:function(data){
			data = eval('('+ data+')');
			var status = data["status"];//0权限不符，1成功，2.session丢失
			if(status == 2){
				alert("登陆失效，请重新登陆！");
				location.href = "site_loginpage.action";
			}else if(status == 0){
				alert("您的登陆与权限不符，请重新登陆！");
				location.href = "site_loginpage.action";
			}else if(status == 1){
				var list = data["list"];
				var cur = data["curpage"];
				var countall = data["countpage"];
				$("#scanConList").children(":eq(1)").html("");
				$("#ye").html("");
				setPage($("#ye"),cur,countall,"scan");
				if(list.length > 0){
					for(var i=0 ; i<list.length ; i++){						
						var id = list[i]["id"];
						var valid = list[i]["valid"];
						var title = list[i]["title"]; // (内容)类型:1-普通内容,2-logo,3-二维码	
						var content_id = list[i]["content_id"];
						var create_date = list[i]["create_date"].substr(0,10);
						var reason = list[i]["reason"];
						var image = list[i]["image"];
						var creator = list[i]["creator"]; 
						var create_name = list[i]["create_name"];
						var type = list[i]["type"]; 
						var is_stick = list[i]["is_stick"];
						var chargeTime = chargeUndefined(list[i]["operate_time"]).substr(0,10); // 审批时间
						var chargeName = chargeUndefined(list[i]["operator_name"]); // 审批人
						var chargeTip = chargeUndefined(list[i]["approve_desc"]);  // 审批意见
						var status = list[i]["approve_status"]; // 审批状态
						id = getNull(id);
						valid = getNull(valid);
						image = getNull(image);
						title = getNull(title);
						content_id = getNull(content_id);
						content_id = getNull(create_date);
						reason = getNull(reason);
						create_name = getNull(create_name);
						type = getNull(type);
						chargeTime = getNull(chargeTime);
						chargeName = getNull(chargeName);
						chargeTip = getNull(chargeTip);
						var sta = "";
						if(status == 0 ){sta= "nosolve"; status = "待审批";  }
						else if(status == 1 ){sta= "solve"; status = "已批准";  } 
						else if(status == 2 ){sta= "unsolve"; status = "已驳回";  } 
						var str = "<tr><td>"+ create_date +"</td><td>"+ create_name +"</td><td>"+ title +"</td>" +
							"<td>"+ reason +"</td><td>"+ chargeTime +"</td><td>"+ chargeName +"</td><td>"+ 
							chargeTip +"</td><td class='"+ sta +"'>"+ status +"</td><td> " +
							"<span onclick='opinionDetails($(this))' " +
							" >[ 查看 ]</span>" +
							"<span class='hd'>"+ image +"</span></td></tr>";
						
						$("#scanConList").append(str);
					}
				}
				
			}
			
			
		},
		error:function(){ alert("连接错误，请稍后重试！"); } ,
		complete:function(){ chargeClose(1) ;  }
	});
}

// 点击上传
function addScan(num){
	bounce.show($("#addScan"));
	deleteCancel();
	insertUploadyfy();
	var type = "上传Logo";
	if(num == 12){ type = "上传二维码";  }
	$("#addSc_Type").html(type);
	$.ajax({
		url:"site_upPicrecord.action",
		data:{ "titleid": num },
		type:"post",
		timeout:5000 ,
		beforeSend:function(){ loading.open() ;  } ,
		success:function(data){
			data = eval('('+ data +')');
			var status = data["status"];
			if(status == 1){
				var list = data["list"];
				var id = list[0]["id"]; // 获得新增的记录的id 
				isIn_add = { is_add:true , addId:id };
			}else{
				alert("新增失败！");
			}
		},
		error:function(){
			alert("新建错误，请稍后重试");
		} ,
		complete:function(){ loading.close() ;  }
	});
}
// 查看详情
function opinionDetails(obj){
	deleteCancel();
	bounce.show($("#scanDetailsCon"));
	var status = obj.parent().prev().html();
	var type = obj.parent().siblings(":eq(2)").html();
	var reson = obj.parent().siblings(":eq(3)").html();
	var explain = obj.parent().siblings(":eq(6)").html();
	var img = obj.siblings(".hd").html();
	var image = "../../../../" + img ;
	$("#detailsType").html(type);
	$("#det_status").html(status);
	$("#det_reason").html(reson);
	$("#det_explain").html(explain);
	$("#det_img").attr("src",image);
}
 // 确定上传图片
 function addSc_ok(){
	 var type = $("#addSc_Type").html(); // 上传LOGO / 上传二维码
	 var addSc_reason = $("#add_reason").val();
	 if(addSc_reason == "" || addSc_reason == null){
		 alert("请将上传理由补充完整！");
	 }else if(type == "" || type == null){
		 alert("系统错误，请稍后重试！");
	 }else{
		 bounce.cancel();
		 if(confirm("确定提交该上传？")){
			 $.ajax({
				 url:"site_intPic.action",
				 data:{ "up_reason": addSc_reason },
				 type:"post",
				 timeout:5000 ,
				 beforeSend:function(){ loading.open() ;  } ,
				 success:function(data){
					 data = eval('('+ data +')');
					 var status = data["status"];
					 if(status == 1){
						 isIn_add = { is_add:false , addId:null };
						 var listhis = data["listhis"];
						 var listapp = data["listapp"];
						 
						 var title = listhis[0]["title"];
						 var create_date = (listhis[0]["create_date"]).substr(0,10);
						 var reason = listhis[0]["reason"];
						 var create_name = listhis[0]["create_name"];
						 var approve_status = listapp[0]["approve_status"];
						 var chargeTime = chargeUndefined(listapp[0]["operate_time"]).substr(0,10);     // 审批时间
						 var chargeName = chargeUndefined(listapp[0]["operator_name"]);    // 审批人
						 var chargeTip = chargeUndefined(listapp[0]["approve_desc"]);      // 审批意见
						 var image = listhis[0]["image"];
						 
						 chargeName = getNull(chargeName);
						 image = getNull(image);
						 approve_status = getNull(approve_status);
						 chargeTime = getNull(chargeTime);
						 chargeTip = getNull(chargeTip);
						 var sta = "";
						 if(approve_status == 0 ){sta= "nosolve"; approve_status = "待审批";  }
						 else if(approve_status == 1 ){sta= "solve"; approve_status = "已批准";  } 
						 else if(approve_status == 2 ){sta= "unsolve"; approve_status = "已驳回";  } 
						 
						 var str = "<tr><td>"+ create_date +"</td><td>"+ create_name +"</td><td>"+ type.substr(2) +"</td>" +
						 "<td>"+ reason +"</td><td>"+ chargeTime +"</td><td>"+ chargeName +"</td><td>"+ 
						 chargeTip +"</td><td class='"+ sta +"'>"+ approve_status +"</td><td> " +
						 "<span onclick='opinionDetails($(this))' >[ 查看 ]</span>" +
						 "<span class='hd'>"+ image +"</span></td></tr>";
						 
						 $("#scanConList").children(":eq(1)").children(":gt(3)").remove();
						 $("#scanConList").prepend(str);
					 }else{
						 alert("上传失败，请稍后重试！");
					 }
				 },
				 error:function(){
					 alert("新建错误，请稍后重试");
				 } ,
				 complete:function(){ loading.close() ;  }
			 });
			 $("#add_reason").val(""); 
		 }
	 }
		 
 }
// 非空方法
function chargeUndefined( val ){
	if(val == undefined || val == null){
		return "";
	}
	return val ;
}