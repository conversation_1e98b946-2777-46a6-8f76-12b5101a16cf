package cn.sphd.miners.modules.dailyAffairs.service;

import cn.sphd.miners.modules.sales.entity.SlCollectApplication;
import cn.sphd.miners.modules.system.service.BadgeNumberCallback;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2019/5/16.
 */
public interface CollectWindowService extends BadgeNumberCallback {

    List<Map<String,Object>> collectPaymentList(Integer oid);  //回款入账列表查询

    SlCollectApplication getSlCollectDetail(Integer collectId);

    Map<String,Object> collectPayment(Integer collectId, Integer financeAccountId, Date receiveDate,Integer type,Integer userId,String customerName,Map<String,Object> map);

    Map<String,Object> getCollectChecks(Date createDate,Date approvalDate,String method,Integer userIdLogin,Integer userId,Map<String,Object> map);

    Integer collectPaymentNumber(Integer oid);  //回款入账应展示的角标数

    void collectRejectSend(int loginNum,int operate,SlCollectApplication slCollectApplication,Integer toUserId,String pass,String code);

    List<SlCollectApplication> getCollectFinance(Integer oid,Date beginDate,Date endDate,Integer customer);

    Map<String,Object> getDetermine(Integer collectId,Integer userId,Integer collectPayId);
}
