package cn.sphd.miners.modules.commodity.controller;

import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.commodity.entity.PdMerchandise;
import cn.sphd.miners.modules.commodity.entity.PdPack;
import cn.sphd.miners.modules.commodity.service.PdPackService;
import cn.sphd.miners.modules.commodity.service.ProductService;
import cn.sphd.miners.modules.material.entity.MtBase;
import cn.sphd.miners.modules.material.entity.MtCategory;
import cn.sphd.miners.modules.material.service.MaterielService;
import cn.sphd.miners.modules.sales.service.PdCustomerService;
import cn.sphd.miners.modules.system.entity.User;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2017/7/24.
 * 商品包装模块
 */
@Controller
@RequestMapping("/pack")
public class PackController {

    @Autowired
    PdPackService pdPackService;
    @Autowired
    PdCustomerService pdCustomerService;
    @Autowired
    ProductService productService;
    @Autowired
    MaterielService materielService;

    @RequestMapping("/packIndex.do")
    public String packIndex(){
        return "/commodity/bpmessage";
    }

    /**
    * <AUTHOR>
    * @Date 2017/7/24 11:16
    * 获取机构所有外部图号列表
    */
    @ResponseBody
    @RequestMapping("/getCustomerList.do")
    public void  getCustomerList(User user, Integer pageNumber, Integer quantum, HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();
        Integer oid=user.getOid();
        if (oid!=null) {
            List<PdMerchandise> pdMerchandiseList=pdPackService.getPdMerchandiseByOid(oid);


            List<PdMerchandise> list=new ArrayList<PdMerchandise>();
            if (pageNumber!=null&&pdMerchandiseList.size()>0) {
                int max = pageNumber * quantum;
                int min = pageNumber * quantum - quantum;
                int total = pdMerchandiseList.size();
                for (int i = min; i < max; i++) {
                    if (i < total)
                        list.add(pdMerchandiseList.get(i));
                }

                int totalPage = (total + quantum -1) / quantum;//计算总页数

                map.put("cur",pageNumber);//第几页
                map.put("countall",totalPage);//总页数
            }else {
                map.put("cur",1);//第几页
                map.put("countall",1);//总页数
            }
            map.put("pdCustomerProductList",list);

            map.put("status",1);

        }else {
            map.put("status",0);
        }
        ObjectToJson.objectToJson1(map,new String[]{"product","customer","TPdMerchandiseHistoryPdMerchandiseViaProductCustomer","slOrdersItemHashSet","productCustomer","material","parent"},response);
    }


    /**
    * <AUTHOR>
    * @Date 2017/7/24 10:51
    * 获取某个商品的包装详情
    */
    @ResponseBody
    @RequestMapping("/getPdPackListByPdBaseId.do")
    public void  getPdPackList(Integer pdCustomerId, HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();
        if (pdCustomerId!=null) {
            PdMerchandise p=pdPackService.getPdMerchandiseById(pdCustomerId);

            List<PdPack> pdPackList = pdPackService.getPdPackListByPdBaseId(pdCustomerId);

            map.put("pdBase",p.getProduct());
            map.put("outerName",p.getOuterName());//外部名称
            map.put("outerSn",p.getOuterSn());//外部图号
            map.put("pdCustomerId",pdCustomerId);
            map.put("pdPackList",pdPackList);
            map.put("status",1);
        }else {
            map.put("status",0);
        }
        ObjectToJson.objectToJson1(map,new String[]{"TPdBaseHistoryPdBaseViaProduct","TPdCompositionPdBaseViaProduct","TPdMerchandisePdBaseViaProduct","TPdProcessPdBaseViaProduct","TMtBaseViaProduct","TPdCompositionViaParent","pdPackHashSet","productCustomer","parent","product","category","mtQualityMtBaseViaId","mtStockInfoHashSet","mtSupplierHistoryHashSet","mtSupplierMaterialHashSet","PdCompositionMaterialHashSet",""},response);

    }


    /**
    * <AUTHOR>
    * @Date 2017/7/24 12:39
    * 编辑包装信息接口
    */
    @ResponseBody
    @RequestMapping("/editPack.do")
    public void editPack(HttpServletRequest request, User user, HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();

        Integer oid= user.getOid();
        String pdCustomerId=request.getParameter("pdCustomerId");//商品外部图号id
        String packString=request.getParameter("packList");//包装list

        if (oid!=null&&pdCustomerId!=null&&packString!=null) {
            PdMerchandise pdMerchandise = pdPackService.getPdMerchandiseById(Integer.valueOf(pdCustomerId));

            List<PdPack> pdPackList = pdPackService.getPdPackListByPdBaseId(Integer.valueOf(pdCustomerId));
            for (PdPack p : pdPackList) {
                pdPackService.deletePdPack(p);
            }

            JSONArray packListJsonArray = JSONArray.fromObject(packString);//转jsonArray
            List packList = JSONArray.toList(packListJsonArray);
            for (int i = 0; i < packList.size(); i++) {
                JSONObject packJson = JSONObject.fromObject(packListJsonArray.get(i));

                PdPack pack = new PdPack();//包装
                MtBase mtBase = new MtBase();//物料
                Integer mtId = 0;
                String mtName = null;
                String mtCode = null;
                Double mtWeight = 0.0;
                if (packJson.getString("level") != null)
                    pack.setLevel(packJson.getInt("level"));//包装级别
                if (packJson.getString("amount") != null)
                    pack.setAmount(packJson.getInt("amount"));//包装内商品数量
                if (packJson.getString("manner") != null)
                    pack.setManner(packJson.getString("manner"));//包装方式
                if (packJson.getString("mtId")!= null&&!"".equals(packJson.getString("mtId")))
                    mtId = packJson.getInt("mtId");//物料id  检索出来的mtid不为null
                if (packJson.getString("mtName") != null)
                    mtName = packJson.getString("mtName");//物料名称
                if (packJson.getString("mtCode") != null)
                    mtCode = packJson.getString("mtCode");//物料代号
                if (packJson.getString("mtWeight") != null)
                    mtWeight = packJson.getDouble("mtWeight");//包装材料重量
                if (packJson.getString("totalWeight") != null)
                    pack.setTotalWeight(new BigDecimal(packJson.getDouble("totalWeight")));//包装后总重量


                if (mtId != 0) {
                    mtBase = materielService.getMtBaseById(mtId);

                    map.put("status",1);

                } else {
                    List<MtBase> mbs=materielService.getMtBaseListByCode(oid,packJson.getString("mtCode"));
                    if(mbs.size()<=0){
                        MtCategory category = materielService.getMtCategoryByOidCName(oid, "商品的包装物");
                        for (MtCategory mc : category.getMtCategoryHashSet()) {
                            if ("待分类".equals(mc.getName())) {
                                mtBase.setCategory(mc);//分类外键
                            }
                        }
                        mtBase.setName(mtName);
                        mtBase.setCode(mtCode);
                        mtBase.setNetWeight(new BigDecimal(mtWeight));
                        materielService.saveMtBase(mtBase);

                        map.put("status",1);

                    }else {
                        map.put("status",2);//材料代号已存在

                    }
                }
                if (map.get("status").equals(1)){
                    pack.setProductCustomer(pdMerchandise);
                    pack.setMaterial(mtBase);
                    pdPackService.savePdPack(pack);
                }
            }
        }else {
            map.put("status",0);

        }
        ObjectToJson.objectToJson1(map,new String[]{"TPdBaseHistoryPdBaseViaProduct","TPdCompositionPdBaseViaProduct","TPdMerchandisePdBaseViaProduct","TPdProcessPdBaseViaProduct","TMtBaseViaProduct","TPdCompositionViaParent","pdPackHashSet"},response);

    }


    /**
    * <AUTHOR>
    * @Date 2017/7/25 15:01
    * 材料代号检索包装材料接口
    */
    @ResponseBody
    @RequestMapping("/getPackBaseByCode.do")
    public void  getPackBaseByCode(User user,String code,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();
        Integer oid= user.getOid();
        MtCategory m=materielService.getMtCategoryByOidCName(oid,"商品的包装物");

        List<MtCategory> mtCategorys=materielService.getMtCategoriesById(m.getId());
        List<MtBase> mtBaseList=new ArrayList<MtBase>();
        for (MtCategory mc:mtCategorys){
            List<MtBase> mtBases=pdPackService.getPackListByCIdCode(mc.getId(),code);
            mtBaseList.addAll(mtBases);
        }
        map.put("mtBaseList",mtBaseList);
        ObjectToJson.objectToJson1(map, new String[]{"parent", "org", "mtBaseHashSet", "mtCategoryHashSet", "category", "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet", "TPdCompositionViaParent", "TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct", "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct", "product", "material", "parent", "TPdCompositionPdCompositionViaTPId", "TPdProcessPdCompositionViaId", "TPdCompositionViaParent"
                , "TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct"
                , "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct", "TPdCompositionViaParent", "product", "category", "mtQualityMtBaseViaId"
                , "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet", "PdCompositionMaterialHashSet", "pdCompositionMaterialHashSet","pdPackHashSet"}, response);
    }




}
