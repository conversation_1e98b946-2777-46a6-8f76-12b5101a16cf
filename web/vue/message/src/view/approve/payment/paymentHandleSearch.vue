<template>
  <div id="reimburseApproveQuery" class="reimburseQuery">
    <TY_NavTop title="个人报销付款审批"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div style="padding: 0 4px;">
        <div class="tip tip-success">您可更换查询条件，以得到其他查询结果。</div>
      </div>
      <div class="ui-message">
        <div class="sendContainer">
          <el-form :model="queryForm" ref="queryForm" label-width="70px">
            <div>
              <h4 class="item-header">提交时间</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.type" size="small">
                  <el-radio label="1" border>近七日</el-radio>
                  <el-radio label="2" border>本月</el-radio>
                  <el-radio label="3" border>自定义</el-radio>
                </el-radio-group>
              </div>
              <div class="el-form-item" v-show="queryForm.type === '3'">
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.beginDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker> -
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.endDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker>
              </div>
            </div>
            <div>
              <h4 class="item-header">申请人</h4>
              <div class="el-form-item">
                <span :class="queryForm.applier === 0?'btn_all btn_active':'btn_all'" v-on:click="chooseApplier(0)">全部</span>
                <span class="chooseApplier" v-on:click="chooseApplier(1)">{{queryForm.applyName}} <i class="el-icon-arrow-right"></i></span>
              </div>
            </div>
            <div class="handle_button" style="margin-top: 20px">
              <input type="submit" class="ui-btn ui-btn_info" value="确定" @click="submit" v-loading.fullscreen.lock="loading">
            </div>
          </el-form>
        </div>
      </div>
    </div>
    <TY_AddressBook :rolelist="roleList" :title="选择申请人" v-bind:show.sync="bookVisible" @select_role="setRole"></TY_AddressBook>
  </div>
</template>
<style>
  .btn_all{
    display: inline-block;
    padding: 5px 20px;
    border-radius: 3px;
    font-size: 12px;
    border: 1px solid #dcdfe6;
    margin-left: 10px;
  }
  .btn_active{
    border: 1px solid #409eff;
    color: #409eff;
  }
  #reimburseApproveQuery .el-radio__input, #reimburseApplyQuery .el-radio__input {
    display: none;
  }
  .chooseApplier{
    margin-left: 110px;
    cursor: pointer;
  }
</style>

<script>
export default {
  name: 'paymentHandleSearch',
  data () {
    return {
      loading: true,
      queryForm: {
        type: '1',
        beginDate: '',
        endDate: '',
        applier: 0,
        applyName: '选择申请人'
      },
      roleList: [],
      bookVisible: false,
      type: 0
    }
  },
  created: function () {
    let that = this
    let type = this.$route.params.type
    this.type = type
    this.$http.post('../../../reimburseWindow/getReimburseApplyUserList.do', { userId: this.sphdSocket.user.userID, userName: '', 'businessType': 17 }, {
      emulateJSON: true
    }).then((response) => {
      that.loading = false
      let body = response.body
      let data = body.data
      if (data) {
        let list = data
        that.roleList = list
      } else {
        console.log('加载失败！')
      }
    })
  },
  methods: {
    submit: function () {
      let _this = this
      this.$store.dispatch('setApprovalSettingSearchInfo', {
        'type': _this.queryForm.type, // type 1-近七日，2-本月，3-自定义
        'beginDate': _this.queryForm.beginDate,
        'endDate': _this.queryForm.endDate,
        'applyId': _this.queryForm.applier,
        'applyUserName': _this.queryForm.applyName
      })
      this.$router.push({
        name: `paymentHandleSearchList`
      })
    },
    chooseApplier: function (type) {
      if (type === 0) {
        this.queryForm.applier = 0
        this.queryForm.applyName = '请选择申请人'
      } else {
        this.bookVisible = true
      }
    },
    setRole: function (data) {
      this.queryForm.applier = data.userID
      this.queryForm.applyName = data.userName
    }
  }
}
</script>
