package cn.sphd.miners.modules.forumArea.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2020-12-14 
 */

@Entity ( name ="ResourceForumAttachment" )
@Table ( name ="t_resource_forum_attachment" )
public class ResourceForumAttachment  implements Serializable {

	private static final long serialVersionUID =  47520708767299243L;

	@Id
	@Column
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

   	@Column(name = "org" )
	private Integer org;

	/**
	 * 资源ID
	 */
   	@Column(name = "resource" )
	private Integer resource;

	/**
	 * 资源ID
	 */
   	@Column(name = "resource_forum" )
	private Integer resourceForum;

	/**
	 * 论坛主题附件ID
	 */
   	@Column(name = "post_attachment" )
	private Integer postAttachment;

	/**
	 * 论坛回帖/回复附件ID
	 */
   	@Column(name = "reply_attachment" )
	private Integer replyAttachment;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_date" )
	private Date createDate;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_date" )
	private Date updateDate;

	/**
	 * 对应消息表的id
	 */
   	@Column(name = "message_id" )
	private Integer messageId;

	/**
	 * 操作:1-增,2-删,3-改
	 */
   	@Column(name = "operation" )
	private String operation;

	/**
	 * 修改前记录ID
	 */
   	@Column(name = "previous_id" )
	private Integer previousId;

	/**
	 * 版本号,每次修改+1
	 */
   	@Column(name = "version_no" )
	private Integer versionNo;

	public static long getSerialVersionUID() {
		return serialVersionUID;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOrg() {
		return org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getResource() {
		return resource;
	}

	public void setResource(Integer resource) {
		this.resource = resource;
	}

	public Integer getResourceForum() {
		return resourceForum;
	}

	public void setResourceForum(Integer resourceForum) {
		this.resourceForum = resourceForum;
	}

	public Integer getPostAttachment() {
		return postAttachment;
	}

	public void setPostAttachment(Integer postAttachment) {
		this.postAttachment = postAttachment;
	}

	public Integer getReplyAttachment() {
		return replyAttachment;
	}

	public void setReplyAttachment(Integer replyAttachment) {
		this.replyAttachment = replyAttachment;
	}

	public Integer getCreator() {
		return creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getUpdator() {
		return updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Integer getMessageId() {
		return messageId;
	}

	public void setMessageId(Integer messageId) {
		this.messageId = messageId;
	}

	public String getOperation() {
		return operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public Integer getPreviousId() {
		return previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}
}
