<template>
  <div id="success">
    <img :src="okImg" alt="提交成功" @click="close"/>
  </div>
</template>
<style>
  #success{ margin-top: 30px; position: relative;  }
  #success .round{ margin-top:200px;  color: #36b93c; font-size:20px;  text-align: center;   }
  #success img{ display:block; width:60%; margin:100px auto;  }
  .el-button--primary { margin: 100px auto!important; display: block!important; }
  .close{ position: absolute; top:150px; height:100px;}
</style>
<script>
import okImg from '../assets/ok.png'
export default {
  name: 'success',
  data () {
    return {
      okImg: okImg,
    }
  },
  methods: {
    close () {
      if (typeof (window.WeixinJSBridge) !== 'undefined') {
        window.WeixinJSBridge.call('closeWindow')
      } else if (navigator.userAgent.indexOf('MSIE') > 0) {
        if (navigator.userAgent.indexOf('MSIE 6.0') > 0) {
          window.opener = null
          window.close()
        } else {
          window.open('', '_top')
          window.top.close()
        }
      } else {
        try {
          this.focus()
          self.opener = null
          self.close()
        } catch (e) {
        }
        try {
          window.opener = null
          window.open('', '_self', '')
          window.close()
        } catch (e) {
        }
        try {
          window.open(window.location, '_self').close()
        } catch (e) {
        }
        window.location.replace('about:blank')
      }
    }
  }
}
</script>

<style scoped>
  h1, h2 {
    font-weight: normal;
  }
  .success{
    background-color: #f1f1f2;
    border-radius: 4px;
    text-align: center;
    padding: 20px;
  }
  .success i{
    color: #67C23A;
  }
  .tip {
    margin-left:8px;
  }
  </style>
