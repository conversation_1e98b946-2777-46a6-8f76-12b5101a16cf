.opt, .rad1 {
    display: none;
}

.bounce_Fixed2 {
    position: fixed;
    z-index: 10002;
    background: rgba(100, 100, 100, 0.5);
}
.list thead tr,.timeLineCon, .backCtrl{display: none}
#ordLoanAddEdit, #generalScan, #borrowInfo, #inOrOut {
    width: 1120px;
}

#payImport, #payInfo {
    /*width: 615px;*/
}

#generalScan span.mar10 {
    margin-top: 5px;
}

#addBtn {
    display: inline-block;
    margin-right: 20px;
}
/*.item {*/
    /*display: inline-block;*/
    /*width: 350px;*/
/*}*/

.endCon {
    display: none;
}


/*.item > span, .item input, .item select {*/
    /*display: inline-block;*/
    /*height: 30px;*/
    /*line-height: 30px;*/
    /*width: 170px;*/
    /*margin-left: 10px;*/
/*}*/

/*.item > span {*/
    /*display: inline-block;*/
    /*margin-left: 10px;*/
    /*width: 150px;*/
/*}*/

/*.item > span:nth-child(1) {*/
    /*background: #C6D9F1;*/
    /*text-align: center;*/
    /*border-radius: 3px;*/
/*}*/

/*.item > span.radCon {*/
    /*width: 518px;*/
/*}*/

/*.item > span .rad {*/
    /*display: inline-block;*/
    /*margin-right: 25px;*/
/*}*/

/*.item > span.radCon i {*/
    /*color: #48cfad;*/
    /*margin-right: 10px;*/
/*}*/

/*.item input.memo {*/
    /*width: 866px;*/
/*}*/

.item2 {
    padding-left: 50px;
}

.item2 > span:nth-child(1) {
    display: inline-block;
    width: 150px;
    text-align: right;
}

.item2 > span:nth-child(2), .item2 > input, .item2 > select {
    display: inline-block;
    width: 200px;
    height: 30px;
    border: 1px solid #ccc;
    line-height: 30px;
    margin-left: 15px;
    background: #fff;
    border-radius: 3px;
    padding-left: 10px;
}
.createInfo {
    margin: 30px 0;
}

.createInfo > span {
    display: inline-block;
    width: 32%;
}

.item {
    overflow: hidden;
    clear: both;
    padding:3px 0;
}
.item>.col-md-4, .item>.col-md-8, .item>.col-md-6{
    padding-left:0 ;
    padding-right:0 ;
}
.item>.col-md-3{ text-align: center;  }
.item input{
    color: #7e7e7e;
}

.item-title{
    display: inline-block;
    width:130px;
    text-align: right;
    margin-right: 5px;
    vertical-align: top;
    line-height:30px;
    color: #5a5e6c;
}

.item-content{
    display: inline-block;
    width:160px;
    height: 30px;
    line-height:30px;
    border-radius: 2px;
    padding: 0 8px;
    color: #7e7e7e;
    word-break: keep-all;
}
.ty-table thead td {
    border-color: #d7d7d7 #d7d7d7 #d7d7d7 #d7d7d7;
    background-color: #eee;
    font-weight: bold;
    color: #666;
 }
.bonceCon input:disabled,.bonceCon select:disabled{
    background-color: #dcdfe6;
}

.memo{
    width: 866px;
    height:60px;
    text-overflow:ellipsis;
    line-height:20px;
    padding: 6px;
    word-break: break-all;
}
.tansCommon{ display:none;  }
#ordLoanAddEdit input[disabled]{ background:#ddd;  }
#ordRepaymentAdd{ width: 500px; }
.faXian{   text-align: center; line-height: 30px;  }

.tunbac{
    float: left;
    position: absolute;
    top: 126px;
}

.tbhone{
    margin-top: 61px;
}

#boxchoo{
    display: none;
    text-align: right;
    padding-bottom: 30px;
}

#boxchoo select{
    width: 93px;
    height: 34px;
    padding: 6px 12px;
    display: inline-block;
    text-align: center;
}
.purchaseCon{
    display: none;
}

.timeLineCon{
    margin:40px auto;
    width: 90%;
}
#approveItem>.apItem:last-child:after{
    display: block;
    content: '';
    clear: both;
}
.apItem{
    margin-right: 10px;
    float: left;
    width: 200px;
    margin-top: 30px;
}
.apTtl{
    position: relative;
}
.apCon{
    margin-top: 5px;
    padding-left: 25px;
}
.apTtl .line{
    position: absolute;
    top:10px;
    right: 10px;
    width: 166px;
    border-bottom: 1px solid #ccc;
}
.apTtl .no{
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    display: inline-block;
    border:1px solid #666;
    border-radius: 10px;
}