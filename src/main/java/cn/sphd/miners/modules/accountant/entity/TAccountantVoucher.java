package cn.sphd.miners.modules.accountant.entity;

import org.apache.ibatis.type.Alias;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by 刘洪涛 on 17-3-23.
 */
@Alias("voucher")
public class TAccountantVoucher implements Serializable {
    private static final long serialVersionUID = 1L;

    public TAccountantVoucher() { }

    private Integer id;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }


    private Integer accountMonth;//凭证月份
    private Integer establish;//建账ID
    private Integer previousId;//修改前记录ID
    private Integer versionNo;//版本号,每次修改+1
    private Date billDate;//票据日期

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public Date getBillDate() {
        return billDate;
    }

    public void setBillDate(Date billDate) {
        this.billDate = billDate;
    }

    public Integer getEstablish() {
        return establish;
    }

    public void setEstablish(Integer establish) {
        this.establish = establish;
    }

    public void setAccountMonth(Integer accountMonth) {
        this.accountMonth = accountMonth;
    }

    public Integer getAccountMonth() {
        return accountMonth;
    }

    private Integer org;

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    private Integer voucher;

    public Integer getVoucher() {
        return voucher;
    }

    public void setVoucher(Integer voucher) {
        this.voucher = voucher;
    }

    private String belongPeroid;

    public String getBelongPeroid() {
        return belongPeroid;
    }

    public void setBelongPeroid(String belongPeroid) {
        this.belongPeroid = belongPeroid;
    }

    private String bookDate;

    public String getBookDate() {
        return bookDate;
    }

    public void setBookDate(String bookDate) {
        this.bookDate = bookDate;
    }

    private String mode;

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    private String type;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    private String category;

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    private Integer sn;

    public Integer getSn() {
        return sn;
    }

    public void setSn(Integer sn) {
        this.sn = sn;
    }

    private String settlement;

    public String getSettlement() {
        return settlement;
    }

    public void setSettlement(String settlement) {
        this.settlement = settlement;
    }

    private String checkNo;

    public String getCheckNo() {
        return checkNo;
    }

    public void setCheckNo(String checkNo) {
        this.checkNo = checkNo;
    }

    private Byte isSettled;

    public Byte getIsSettled() {
        return isSettled;
    }

    public void setIsSettled(Byte isSettled) {
        this.isSettled = isSettled;
    }

    private Byte isAccount;

    public Byte getIsAccount() {
        return isAccount;
    }

    public void setIsAccount(Byte isAccount) {
        this.isAccount = isAccount;
    }

    private String reason;

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    private Integer billDetail;

    public Integer getBillDetail() {
        return billDetail;
    }

    public void setBillDetail(Integer billDetail) {
        this.billDetail = billDetail;
    }

    private String summary;

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    private Integer operator;

    public Integer getOperator() {
        return operator;
    }

    public void setOperator(Integer operator) {
        this.operator = operator;
    }

    private String operatorName;

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    private String operatorFinance;

    public String getOperatorFinance() {
        return operatorFinance;
    }

    public void setOperatorFinance(String operatorFinance) {
        this.operatorFinance = operatorFinance;
    }

    private String memo;

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    private Integer creator;

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    private String createName;

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @DateTimeFormat(pattern="yyyy-MM-dd")
    private String createDate;

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    private Integer updator;

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    private String updateName;

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    private String updateDate;

    public String getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(String updateDate) {
        this.updateDate = updateDate;
    }

    private Integer approveItem;

    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    private String approveStatus;

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    private Integer approveLevel;

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    private Integer auditor;

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    private String auditorName;

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    private Date auditDate;

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    private String operation;

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    private String applyMemo;

    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    private String approveMemo;

    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    private Integer messageId;

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    private String source;

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    private String pricetype;

    public String getPricetype() {
        return pricetype;
    }

    public void setPricetype(String pricetype) {
        this.pricetype = pricetype;
    }

    private String purpose;

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    private Integer billQuantity;

    public Integer getBillQuantity() {
        return billQuantity;
    }

    public void setBillQuantity(Integer billQuantity) {
        this.billQuantity = billQuantity;
    }

    private Integer billPeriod;

    public Integer getBillPeriod() {
        return billPeriod;
    }

    public void setBillPeriod(Integer billPeriod) {
        this.billPeriod = billPeriod;
    }

    private Integer detailId;//对应财务帐务明细id

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    private String ftype;

    public String getFtype() {
        return ftype;
    }

    public void setFtype(String ftype) {
        this.ftype = ftype;
    }

    private String kind;//财务类型:1-detail 2-bill

    public String getKind() {
        return kind;
    }

    public void setKind(String kind) {
        this.kind = kind;
    }

    private String addtime;

    public String getAddtime() {
        return addtime;
    }

    public void setAddtime(String addtime) {
        this.addtime = addtime;
    }

//    private String accountMonth;//建账及重新建账的月份，如果该字段不是null的话就能修改
//
//    public String getAccountMonth() {
//        return accountMonth;
//    }
//
//    public void setAccountMonth(String accountMonth) {
//        this.accountMonth = accountMonth;
//    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        TAccountantVoucher that = (TAccountantVoucher) o;

        if (id != null ? !id.equals(that.id) : that.id != null) return false;
        if (org != null ? !org.equals(that.org) : that.org != null) return false;
        if (voucher != null ? !voucher.equals(that.voucher) : that.voucher != null) return false;
        if (belongPeroid != null ? !belongPeroid.equals(that.belongPeroid) : that.belongPeroid != null) return false;
        if (bookDate != null ? !bookDate.equals(that.bookDate) : that.bookDate != null) return false;
        if (mode != null ? !mode.equals(that.mode) : that.mode != null) return false;
        if (type != null ? !type.equals(that.type) : that.type != null) return false;
        if (category != null ? !category.equals(that.category) : that.category != null) return false;
        if (sn != null ? !sn.equals(that.sn) : that.sn != null) return false;
        if (settlement != null ? !settlement.equals(that.settlement) : that.settlement != null) return false;
        if (checkNo != null ? !checkNo.equals(that.checkNo) : that.checkNo != null) return false;
        if (isSettled != null ? !isSettled.equals(that.isSettled) : that.isSettled != null) return false;
        if (isAccount != null ? !isAccount.equals(that.isAccount) : that.isAccount != null) return false;
        if (reason != null ? !reason.equals(that.reason) : that.reason != null) return false;
        if (billDetail != null ? !billDetail.equals(that.billDetail) : that.billDetail != null) return false;
        if (summary != null ? !summary.equals(that.summary) : that.summary != null) return false;
        if (operator != null ? !operator.equals(that.operator) : that.operator != null) return false;
        if (operatorName != null ? !operatorName.equals(that.operatorName) : that.operatorName != null) return false;
        if (operatorFinance != null ? !operatorFinance.equals(that.operatorFinance) : that.operatorFinance != null)
            return false;
        if (memo != null ? !memo.equals(that.memo) : that.memo != null) return false;
        if (creator != null ? !creator.equals(that.creator) : that.creator != null) return false;
        if (createName != null ? !createName.equals(that.createName) : that.createName != null) return false;
        if (createDate != null ? !createDate.equals(that.createDate) : that.createDate != null) return false;
        if (updator != null ? !updator.equals(that.updator) : that.updator != null) return false;
        if (updateName != null ? !updateName.equals(that.updateName) : that.updateName != null) return false;
        if (updateDate != null ? !updateDate.equals(that.updateDate) : that.updateDate != null) return false;
        if (approveItem != null ? !approveItem.equals(that.approveItem) : that.approveItem != null) return false;
        if (approveStatus != null ? !approveStatus.equals(that.approveStatus) : that.approveStatus != null)
            return false;
        if (approveLevel != null ? !approveLevel.equals(that.approveLevel) : that.approveLevel != null) return false;
        if (auditor != null ? !auditor.equals(that.auditor) : that.auditor != null) return false;
        if (auditorName != null ? !auditorName.equals(that.auditorName) : that.auditorName != null) return false;
        if (auditDate != null ? !auditDate.equals(that.auditDate) : that.auditDate != null) return false;
        if (operation != null ? !operation.equals(that.operation) : that.operation != null) return false;
        if (applyMemo != null ? !applyMemo.equals(that.applyMemo) : that.applyMemo != null) return false;
        if (approveMemo != null ? !approveMemo.equals(that.approveMemo) : that.approveMemo != null) return false;
        if (messageId != null ? !messageId.equals(that.messageId) : that.messageId != null) return false;
        if (source != null ? !source.equals(that.source) : that.source != null) return false;
        if (pricetype != null ? !pricetype.equals(that.pricetype) : that.pricetype != null) return false;
        if (purpose != null ? !purpose.equals(that.purpose) : that.purpose != null) return false;
        if (billQuantity != null ? !billQuantity.equals(that.billQuantity) : that.billQuantity != null) return false;
        if (billPeriod != null ? !billPeriod.equals(that.billPeriod) : that.billPeriod != null) return false;
        if (detailId != null ? !detailId.equals(that.detailId) : that.detailId != null) return false;
        if (ftype != null ? !ftype.equals(that.ftype) : that.ftype != null) return false;
        if (kind != null ? !kind.equals(that.kind) : that.kind != null) return false;
        if (addtime != null ? !addtime.equals(that.addtime) : that.addtime != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = id != null ? id.hashCode() : 0;
        result = 31 * result + (org != null ? org.hashCode() : 0);
        result = 31 * result + (voucher != null ? voucher.hashCode() : 0);
        result = 31 * result + (belongPeroid != null ? belongPeroid.hashCode() : 0);
        result = 31 * result + (bookDate != null ? bookDate.hashCode() : 0);
        result = 31 * result + (mode != null ? mode.hashCode() : 0);
        result = 31 * result + (type != null ? type.hashCode() : 0);
        result = 31 * result + (category != null ? category.hashCode() : 0);
        result = 31 * result + (sn != null ? sn.hashCode() : 0);
        result = 31 * result + (settlement != null ? settlement.hashCode() : 0);
        result = 31 * result + (checkNo != null ? checkNo.hashCode() : 0);
        result = 31 * result + (isSettled != null ? isSettled.hashCode() : 0);
        result = 31 * result + (isAccount != null ? isAccount.hashCode() : 0);
        result = 31 * result + (reason != null ? reason.hashCode() : 0);
        result = 31 * result + (billDetail != null ? billDetail.hashCode() : 0);
        result = 31 * result + (summary != null ? summary.hashCode() : 0);
        result = 31 * result + (operator != null ? operator.hashCode() : 0);
        result = 31 * result + (operatorName != null ? operatorName.hashCode() : 0);
        result = 31 * result + (operatorFinance != null ? operatorFinance.hashCode() : 0);
        result = 31 * result + (memo != null ? memo.hashCode() : 0);
        result = 31 * result + (creator != null ? creator.hashCode() : 0);
        result = 31 * result + (createName != null ? createName.hashCode() : 0);
        result = 31 * result + (createDate != null ? createDate.hashCode() : 0);
        result = 31 * result + (updator != null ? updator.hashCode() : 0);
        result = 31 * result + (updateName != null ? updateName.hashCode() : 0);
        result = 31 * result + (updateDate != null ? updateDate.hashCode() : 0);
        result = 31 * result + (approveItem != null ? approveItem.hashCode() : 0);
        result = 31 * result + (approveStatus != null ? approveStatus.hashCode() : 0);
        result = 31 * result + (approveLevel != null ? approveLevel.hashCode() : 0);
        result = 31 * result + (auditor != null ? auditor.hashCode() : 0);
        result = 31 * result + (auditorName != null ? auditorName.hashCode() : 0);
        result = 31 * result + (auditDate != null ? auditDate.hashCode() : 0);
        result = 31 * result + (operation != null ? operation.hashCode() : 0);
        result = 31 * result + (applyMemo != null ? applyMemo.hashCode() : 0);
        result = 31 * result + (approveMemo != null ? approveMemo.hashCode() : 0);
        result = 31 * result + (messageId != null ? messageId.hashCode() : 0);
        result = 31 * result + (source != null ? source.hashCode() : 0);
        result = 31 * result + (pricetype != null ? pricetype.hashCode() : 0);
        result = 31 * result + (purpose != null ? purpose.hashCode() : 0);
        result = 31 * result + (billQuantity != null ? billQuantity.hashCode() : 0);
        result = 31 * result + (billPeriod != null ? billPeriod.hashCode() : 0);
        result = 31 * result + (detailId != null ? detailId.hashCode() : 0);
        result = 31 * result + (ftype != null ? ftype.hashCode() : 0);
        result = 31 * result + (kind != null ? kind.hashCode() : 0);
        result = 31 * result + (addtime != null ? addtime.hashCode() : 0);
        return result;
    }
}
