package cn.sphd.miners.modules.personal.controller;

import cn.sphd.miners.common.utils.ExcelUtils;
import cn.sphd.miners.modules.accountant.util.DateUtil;
import cn.sphd.miners.modules.commodity.controller.CommodityController;
import cn.sphd.miners.modules.commodity.entity.PdCommodityImport;
import cn.sphd.miners.modules.commodity.entity.SlCustomer;
import cn.sphd.miners.modules.commodity.entity.PdMerchandise;
import cn.sphd.miners.modules.commodity.service.CommodityImportService;
import cn.sphd.miners.modules.commodity.service.ProductService;
import cn.sphd.miners.modules.generalAffairs.entity.*;
import cn.sphd.miners.modules.generalAffairs.service.UserImportService;
import cn.sphd.miners.modules.material.entity.MtBase;
import cn.sphd.miners.modules.material.entity.MtUnit;
import cn.sphd.miners.modules.material.service.MaterielService;
import cn.sphd.miners.modules.material.service.MtImportService;
import cn.sphd.miners.modules.material.service.UnitService;
import cn.sphd.miners.modules.personal.dto.ImportRespProduct;
import cn.sphd.miners.modules.personal.entity.ImportRespObject;
import cn.sphd.miners.modules.sales.service.PdCustomerService;
import cn.sphd.miners.modules.system.entity.*;
import cn.sphd.miners.modules.system.service.OrgService;
import cn.sphd.miners.modules.system.service.RoleService;
import cn.sphd.miners.modules.system.service.UserPopedomService;
import cn.sphd.miners.modules.system.service.UserService;
import cn.sphd.miners.modules.uploads.entity.UploadFile;
import cn.sphd.miners.modules.uploads.service.UploadService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.text.DecimalFormat;
import java.util.*;

/**
 * Created by Administrator on 2017/2/21.
 */
@RequestMapping("export")
@Controller
public class ExportController {


    @RequestMapping("/test.do")
    public String test() {
        return "/test/test";
    }


    @Autowired
    PdCustomerService pdCustomerService;
    @Autowired
    UnitService unitService;
    @Autowired
    CommodityImportService commodityImportService;
    @Autowired
    MtImportService mtImportService;
    @Autowired
    ProductService productService;
    @Autowired
    UserImportService userImportService;

    @Autowired
    UploadService uploadService;

    @Autowired
    UserService userService;

    @Autowired
    MaterielService materielService;

    @Autowired
    OrgService orgService;

    @Autowired
    RoleService roleService;

    @Autowired
    UserPopedomService userPopedomService;


    @RequestMapping("/exportAllCustomer.do")
    @ResponseBody
    public void exportAllUsers(HttpServletResponse response, User user) throws ExcelUtils.ExcelException {


        List<SlCustomer> customers = pdCustomerService.getPdCustomers1(user.getOid(), "", null);
        LinkedHashMap<String, String> fileMap = new LinkedHashMap<>();
        fileMap.put("code", "客户代号");
        fileMap.put("category", "客户类别(1:新客户 2:老客户)");
        fileMap.put("name", "客户名称");
        fileMap.put("postAddress", "邮寄地址");
        fileMap.put("postCode", "邮政编码");
        fileMap.put("fullName", "公司名称");
        fileMap.put("taxpayerID", "税号");
        fileMap.put("address", "地址");
        fileMap.put("telephone", "电话");
        fileMap.put("bankName", "开户行");
        fileMap.put("bankNo", "账号");
        fileMap.put("memo", "备注");


        ExcelUtils.listToExcel(customers, fileMap, "客户", response);
    }

    @RequestMapping("/importCustomer.do")
    @ResponseBody
    public String importCustomer(User user, String file) throws IOException, ExcelUtils.ExcelException {
        Map map = new HashMap();
//        String path=session.getServletContext().getRealPath("/upload/")+file;
//        File f = new File(path);
        File f = uploadService.copyTempFile(file);
        if (f.length() > ********) {
//            map.put("code","2");
//            map.put("data",null);
            return "2";
        }
        Integer oid = user.getOid();
        LinkedHashMap<String, String> fileMap = new LinkedHashMap<>();
        fileMap.put("客户代号", "code");
        //fileMap.put("客户类别(1:新客户 2:老客户)","category");
        fileMap.put("客户名称", "fullName");
        //fileMap.put("邮寄地址","postAddress");
        //fileMap.put("邮政编码","postCode");
        //fileMap.put("公司名称","fullName");
        //fileMap.put("税号","taxpayerID");
        fileMap.put("地址", "address");
        //fileMap.put("电话","telephone");
        //fileMap.put("开户行","bankName");
        //fileMap.put("账号","bankNo");
        //fileMap.put("备注","memo");

        try {
            List<SlCustomer> customers = ExcelUtils.excelToList(Files.newInputStream(f.toPath()), "客户清单", SlCustomer.class, fileMap, new String[]{"客户名称", "客户代号", "地址"});
            List<String> dus = new ArrayList<>();
            List<SlCustomer> slCustomers;
            for (SlCustomer pd : customers) {
                slCustomers = pdCustomerService.getPdCustomers2(oid, null);
                //先查重复
                if (slCustomers != null && slCustomers.size() > 0) {
                    for (SlCustomer sc : slCustomers) {
                        if (StringUtils.isNotEmpty(sc.getFullName()))
                            if (sc.getFullName().equals(pd.getFullName())) {
                                dus.add(pd.getFullName());

                            } else {
                                this.addPd(pd, oid, user);
                            }
                    }
                } else {
                    this.addPd(pd, oid, user);
                }
                map.put("data", dus);
            }

        }catch (Exception e){
            return "0";
        }
        return "1";
    }

    public void addPd(SlCustomer pd, Integer oid, User user) {
        if (StringUtils.isNotBlank(pd.getFullName()) && StringUtils.isNotBlank(pd.getCode())) {
            pd.setOid(oid);
            pd.setCreateName(user.getUserName());
            pd.setName(pd.getFullName().length() > 6 ? pd.getFullName().substring(0, 6) : pd.getFullName());
            pd.setCreateDate(new Date());
            pd.setPeroid(Integer.valueOf(DateUtil.getDy(pd.getCreateDate())));
            pd.setCreator(user.getUserID());
            pd.setPrincipal(user.getUserID());
            pd.setPrincipalName(user.getUserName());
            pd.setType("1");
            pd.setVersionNo(0);
            pdCustomerService.addPdCustomer(pd);
        }
    }

    @RequestMapping("/expertAllProduct.do")
    @ResponseBody
    public void expertAllProduct(User user, HttpServletResponse response) throws ExcelUtils.ExcelException {
        Integer oid = user.getOid();

        List<PdMerchandise> products = productService.getByNameInnerSn(oid, "", "", "");
        LinkedHashMap<String, String> fileMap = new LinkedHashMap<>();

        fileMap.put("product.innerSn", "内部图号");
        fileMap.put("product.name", "内部名称");
        fileMap.put("outerSn", "外部图号");
        fileMap.put("outerName", "外部名称");
        fileMap.put("customerName", "客户名称");
        fileMap.put("product.unit", "单位");
        fileMap.put("product.netWeight", "产品重量");
        fileMap.put("product.model", "型号");
        fileMap.put("product.specifications", "规格");
        fileMap.put("unitPrice", "含税单价");
        fileMap.put("taxRate", "税率");
        fileMap.put("unitPriceNotax", "不含税单价");
        fileMap.put("product.minimumiStock", "最低库存");
        fileMap.put("outerPackingMode", "外包装方式");
        fileMap.put("outerPackingMaterial", "外包装材料");
        fileMap.put("outerPackingAmount", "外包装数量");
        fileMap.put("miniPackingMode", "小包装方式");
        fileMap.put("miniPackingAmount", "小包装数量");
        fileMap.put("miniPackingMaterial", "小包装材料");
        fileMap.put("product.memo", "备注");

        ExcelUtils.listToExcel(products, fileMap, "商品", response);

    }

    //根据1.159 更换获取user方式
    @RequestMapping("/importProduct.do")
    @ResponseBody
    public String importProduct(User user, @RequestParam(value = "file", required = false) MultipartFile file) throws IOException, ExcelUtils.ExcelException {
        Integer oid = user.getOid();
        LinkedHashMap<String, String> fileMap = new LinkedHashMap<>();

        fileMap.put("内部图号", "innerSn");
        fileMap.put("内部名称", "cInnerSnName");
        fileMap.put("外部图号", "outerSn");
        fileMap.put("外部名称", "outerName");
        fileMap.put("客户名称", "customerName");
        fileMap.put("单位", "unit");
        fileMap.put("产品重量", "netWeight");
        fileMap.put("型号", "model");
        fileMap.put("规格", "specifications");
        fileMap.put("含税单价", "unitPrice");
        fileMap.put("税率", "taxRate");
        fileMap.put("不含税单价", "unitPriceNotax");
        fileMap.put("最低库存", "minimumStock");
        fileMap.put("外包装方式", "outerPackingMode");
        fileMap.put("外包装材料", "outerPackingMaterial");
        fileMap.put("外包装数量", "outerPackingAmount");
        fileMap.put("小包装方式", "miniPackingMode");
        fileMap.put("小包装数量", "miniPackingAmount");
        fileMap.put("小包装材料", "miniPackingMaterial");
        fileMap.put("备注", "memo");

        try {
            List<PdMerchandise> customerProducts = ExcelUtils.excelToList(file.getInputStream(), "商品",
                    PdMerchandise.class, fileMap, new String[]{"内部图号"});

            for (PdMerchandise pd : customerProducts) {
                if ("".equals(pd.getOuterSn()) || pd.getOuterSn() == null) {
                    return "外部图号不能为空!";
                }
            }

            CommodityController.addBatchProduct(customerProducts, user, productService, materielService, pdCustomerService);
        } catch (Exception e) {
            return "0";
        }

        return "1";
    }


//    @RequestMapping("/exportAllUser.do")
//    @ResponseBody
//    public void exportAllUser(User user, HttpServletResponse response) throws ExcelUtils.ExcelException {
//        Integer oid = user.getOid();
//        List<User> users = userService.getUserByOid(oid, "");
//
//        for (User u : users) {
//
//            if ("1".equals(u.getIsDuty())) {
//                Set<PersonnelOccupation> occupations = u.getPersonnelOccupations();
//                Set<PersonalRewardPunishment> punishments = u.getPersonalRewardPunishments();
//                Set<PersonalEducation> educations = u.getPersonalEducations();
//                Set<PersonnelSalaryLog> salaryLogs = u.getPersonnelSalaryLogUser();
//                Set<PersonalAssessment> assessments = u.getPersonalAssessments();
//
//                List<PersonnelOccupation> occupationList = new ArrayList<>(occupations);
//                List<PersonalRewardPunishment> punishmentArrayList = new ArrayList<>(punishments);
//                List<PersonalEducation> educationArrayList = new ArrayList<>(educations);
//                List<PersonnelSalaryLog> salaryLogArrayList = new ArrayList<>(salaryLogs);
//                List<PersonalAssessment> assessmentArrayList = new ArrayList<>(assessments);
//
//
//                if (occupationList.size() > 0) {
//                    if (occupationList.size() < 3) {//若不足3个则补位
//                        for (int i = 0; i <= 3 - occupationList.size(); i++) {
//                            occupationList.add(new PersonnelOccupation());
//                        }
//                    }
//                    u.setCorpName1(occupationList.get(0).getCorpName());
//                    u.setCorpName2(occupationList.get(1).getCorpName());
//                    u.setCorpName3(occupationList.get(2).getCorpName());
//                    //u.setBeginTime1(occupationList.get(0).getBeginTime());
//                    //u.setBeginTime2(occupationList.get(1).getBeginTime());
//                    //u.setBeginTime3(occupationList.get(2).getBeginTime());
//                    //u.setEndTime1(occupationList.get(0).getEndTime());
//                    //u.setEndTime2(occupationList.get(1).getEndTime());
//                    //u.setEndTime3(occupationList.get(2).getEndTime());
//                    u.setCsalary1(occupationList.get(0).getSalary());
//                    u.setCsalary2(occupationList.get(1).getSalary());
//                    u.setCsalary3(occupationList.get(2).getSalary());
//                    u.setPost1(occupationList.get(0).getPost());
//                    u.setPost2(occupationList.get(1).getPost());
//                    u.setPost3(occupationList.get(2).getPost());
//                    u.setDmemo1(occupationList.get(0).getMemo());
//                    u.setDmemo2(occupationList.get(1).getMemo());
//                    u.setDmemo3(occupationList.get(2).getMemo());
//                    u.setOperatingDuty1(occupationList.get(0).getOperatingDuty());
//                    u.setOperatingDuty2(occupationList.get(1).getOperatingDuty());
//                    u.setOperatingDuty3(occupationList.get(2).getOperatingDuty());
//                }
//                if (punishmentArrayList.size() > 0) {
//                    if (punishmentArrayList.size() < 3) {//若不足3个则补位
//                        for (int i = 0; i <= 3 - punishmentArrayList.size(); i++) {
//                            punishmentArrayList.add(new PersonalRewardPunishment());
//                        }
//                    }
//                    //u.setOccurDate1(punishmentArrayList.get(0).getOccurDate());
//                    //u.setOccurDate2(punishmentArrayList.get(1).getOccurDate());
//                    //u.setOccurDate3(punishmentArrayList.get(2).getOccurDate());
//                    u.setOcontent1(punishmentArrayList.get(0).getContent());
//                    u.setOcontent2(punishmentArrayList.get(1).getContent());
//                    u.setOcontent3(punishmentArrayList.get(2).getContent());
//                    u.setOoperatorName1(punishmentArrayList.get(0).getOperatorName());
//                    u.setOoperatorName2(punishmentArrayList.get(1).getOperatorName());
//                    u.setOoperatorName3(punishmentArrayList.get(2).getOperatorName());
//                    u.setOmemo1(punishmentArrayList.get(0).getMemo());
//                    u.setOmemo2(punishmentArrayList.get(1).getMemo());
//                    u.setOmemo3(punishmentArrayList.get(2).getMemo());
//                }
//                if (educationArrayList.size() > 0) {
//                    if (educationArrayList.size() < 3) {//若不足3个则补位
//                        for (int i = 0; i <= 3 - educationArrayList.size(); i++) {
//                            educationArrayList.add(new PersonalEducation());
//                        }
//                    }
//                    u.setCollegeName1(educationArrayList.get(0).getCollegeName());
//                    u.setCollegeName2(educationArrayList.get(1).getCollegeName());
//                    u.setCollegeName3(educationArrayList.get(2).getCollegeName());
//                    //u.setEbeginTime1(educationArrayList.get(0).getBeginTime());
//                    //u.setEbeginTime2(educationArrayList.get(1).getBeginTime());
//                    //u.setEbeginTime3(educationArrayList.get(2).getBeginTime());
//                    //u.setEendTime1(educationArrayList.get(0).getEndTime());
//                    //u.setEendTime2(educationArrayList.get(1).getEndTime());
//                    //u.setEendTime3(educationArrayList.get(2).getEndTime());
//                    u.setMajor1(educationArrayList.get(0).getMajor());
//                    u.setMajor2(educationArrayList.get(1).getMajor());
//                    u.setMajor3(educationArrayList.get(2).getMajor());
//                    u.setDegree1(educationArrayList.get(0).getDegree());
//                    u.setDegree2(educationArrayList.get(1).getDegree());
//                    u.setDegree3(educationArrayList.get(2).getDegree());
//                    u.setEmemo1(educationArrayList.get(0).getMemo());
//                    u.setEmemo2(educationArrayList.get(1).getMemo());
//                    u.setEmemo3(educationArrayList.get(2).getMemo());
//                }
//                if (salaryLogArrayList.size() > 0) {
//                    if (salaryLogArrayList.size() < 3) {//若不足3个则补位
//                        for (int i = 0; i <= 3 - salaryLogArrayList.size(); i++) {
//                            salaryLogArrayList.add(new PersonnelSalaryLog());
//                        }
//                    }
//                    u.setSalary1(salaryLogArrayList.get(0).getSalary());
//                    u.setSalary2(salaryLogArrayList.get(1).getSalary());
//                    u.setSalary3(salaryLogArrayList.get(2).getSalary());
//                    //u.setAdjustDate1(salaryLogArrayList.get(0).getAdjustDate());
//                    //u.setAdjustDate2(salaryLogArrayList.get(1).getAdjustDate());
//                    //u.setAdjustDate3(salaryLogArrayList.get(2).getAdjustDate());
//                    u.setAdmustResaon1(salaryLogArrayList.get(0).getAdmustResaon());
//                    u.setAdmustResaon2(salaryLogArrayList.get(1).getAdmustResaon());
//                    u.setAdmustResaon3(salaryLogArrayList.get(2).getAdmustResaon());
//                    u.setOperatorName1(salaryLogArrayList.get(0).getOperatorName());
//                    u.setOperatorName2(salaryLogArrayList.get(1).getOperatorName());
//                    u.setOperatorName3(salaryLogArrayList.get(2).getOperatorName());
//                    u.setSmemo1(salaryLogArrayList.get(0).getMemo());
//                    u.setSmemo2(salaryLogArrayList.get(1).getMemo());
//                    u.setSmemo3(salaryLogArrayList.get(2).getMemo());
//
//                }
//                if (assessmentArrayList.size() > 0) {
//                    if (assessmentArrayList.size() < 3) {//若不足3个则补位
//                        for (int i = 0; i <= 3 - assessmentArrayList.size(); i++) {
//                            assessmentArrayList.add(new PersonalAssessment());
//                        }
//                    }
//                    //u.setAssessDate1(assessmentArrayList.get(0).getAssessDate());
//                    //u.setAssessDate2(assessmentArrayList.get(1).getAssessDate());
//                    //u.setAssessDate3(assessmentArrayList.get(2).getAssessDate());
//                    u.setContent1(assessmentArrayList.get(0).getContent());
//                    u.setContent2(assessmentArrayList.get(1).getContent());
//                    u.setContent3(assessmentArrayList.get(2).getContent());
//                    u.setAssessUserName1(assessmentArrayList.get(0).getAssessUserName());
//                    u.setAssessUserName2(assessmentArrayList.get(1).getAssessUserName());
//                    u.setAssessUserName3(assessmentArrayList.get(2).getAssessUserName());
//                    u.setAmemo1(assessmentArrayList.get(0).getMemo());
//                    u.setAmemo2(assessmentArrayList.get(1).getMemo());
//                    u.setAmemo3(assessmentArrayList.get(2).getMemo());
//                }
//
//                User leader = userService.getUserByID(Integer.valueOf(u.getLeader() == null ? "0" : u.getLeader()));
//                if (leader != null)
//                    u.setCreateName(leader.getUserName());
////                else {
////                    leader = userService.getUserByLogonName(u.getLeader());
////                    if(leader!=null)
////                        u.setCreateName(leader.getUserName());
////                }
////                if("0".equals(u.getMarry())||"未婚".equals(u.getMarry())){
////                    u.setMarry("未婚");
////                }else if ("1".equals(u.getMarry())||"已婚".equals(u.getMarry()))
////                    u.setMarry("已婚");
//            }
//        }
//
//
//        LinkedHashMap<String, String> fileMap = new LinkedHashMap<>();
//        fileMap.put("userName", "姓名");
//        //fileMap.put("gender","性别");
//        //fileMap.put("birthday","出生年月");
//        fileMap.put("mobile", "手机号");
//        fileMap.put("email", "email");
//        fileMap.put("qq", "qq");
//        fileMap.put("politicalStatus", "政治面貌");
//        //fileMap.put("marry","婚姻状况");
//        fileMap.put("nation", "民族");
//        fileMap.put("idCard", "身份证号");
//        fileMap.put("address", "家庭住址");
//        //fileMap.put("onDutyDate","入职时间");
//        //fileMap.put("departName","部门");
//        //fileMap.put("postName","职位");
//        //fileMap.put("userType","是否普通员工");
//        //fileMap.put("createName","直属上级");
//
//        //工作经历
//        fileMap.put("corpName1", "公司名称1");
//        //fileMap.put("beginTime1","开始时间");
//        //fileMap.put("endTime1","结束时间");
//        fileMap.put("csalary1", "薪资水平");
//        fileMap.put("post1", "在职职位");
//        fileMap.put("dmemo1", "离职原因");
//        fileMap.put("operatingDuty1", "工作职责");
//
//        fileMap.put("corpName2", "公司名称2");
//        //fileMap.put("beginTime2","开始时间");
//        //fileMap.put("endTime2","结束时间");
//        fileMap.put("csalary2", "薪资水平");
//        fileMap.put("post2", "在职职位");
//        fileMap.put("dmemo2", "离职原因");
//        fileMap.put("operatingDuty2", "工作职责");
//
//        fileMap.put("corpName3", "公司名称3");
//        //fileMap.put("beginTime3","开始时间");
//        //fileMap.put("endTime3","结束时间");
//        fileMap.put("csalary3", "薪资水平");
//        fileMap.put("post3", "在职职位");
//        fileMap.put("dmemo3", "离职原因");
//        fileMap.put("operatingDuty3", "工作职责");
//        //教育
//        fileMap.put("collegeName1", "学校名称1");
//        //fileMap.put("ebeginTime1","开始时间1");
//        //fileMap.put("eendTime1","结束时间1");
//        fileMap.put("major1", "专业1");
//        fileMap.put("degree1", "学历1");
//        fileMap.put("ememo1", "说明1");
//
//        fileMap.put("collegeName2", "学校名称2");
//        //fileMap.put("ebeginTime2","开始时间2");
//        //fileMap.put("eendTime2","结束时间2");
//        fileMap.put("major2", "专业2");
//        fileMap.put("degree2", "学历2");
//        fileMap.put("ememo2", "说明2");
//
//        fileMap.put("collegeName3", "学校名称3");
//        //fileMap.put("ebeginTime3","开始时间3");
//        //fileMap.put("eendTime3","结束时间3");
//        fileMap.put("major3", "专业3");
//        fileMap.put("degree3", "学历3");
//        fileMap.put("ememo3", "说明3");
//
//        //薪资情况
//        //fileMap.put("operateTime1","薪资修改时间1");
//        fileMap.put("admustResaon1", "薪资修改原因1");
//        fileMap.put("salary1", "薪资");
//        //fileMap.put("operatorName1","变更人");
//        fileMap.put("smemo1", "备注");
//
//        //fileMap.put("operateTime2","薪资修改时间2");
//        fileMap.put("admustResaon2", "薪资修改原因2");
//        fileMap.put("salary2", "薪资");
//        //fileMap.put("operatorName2","变更人");
//        fileMap.put("smemo2", "备注");
//
//        //fileMap.put("operateTime3","薪资修改时间3");
//        fileMap.put("admustResaon3", "薪资修改原因3");
//        fileMap.put("salary3", "薪资");
//        //fileMap.put("operatorName3","变更人");
//        fileMap.put("smemo3", "备注");
//
//        //奖惩情况
//        //fileMap.put("occurDate1","奖惩修改时间1");
//        fileMap.put("ocontent1", "奖惩内容1");
//        fileMap.put("ooperatorName1", "变更人");
//        fileMap.put("omemo1", "备注");
//
//        //fileMap.put("occurDate2","奖惩修改时间2");
//        fileMap.put("ocontent2", "奖惩内容2");
//        //fileMap.put("ooperatorName2","变更人");
//        fileMap.put("omemo2", "备注");
//
//        //fileMap.put("occurDate3","奖惩修改时间3");
//        fileMap.put("ocontent3", "奖惩内容3");
//        //fileMap.put("ooperatorName3","变更人");
//        fileMap.put("omemo3", "备注");
//
//
//        //评价情况
//        //fileMap.put("assessDate1","评价修改时间1");
//        fileMap.put("content1", "评价内容");
//        //fileMap.put("assessUserName1","变更人");
//        fileMap.put("amemo1", "备注");
//
//        //fileMap.put("assessDate2","评价修改时间2");
//        fileMap.put("content2", "评价内容");
//        //fileMap.put("assessUserName2","变更人");
//        fileMap.put("amemo2", "备注");
//
//        //fileMap.put("assessDate3","评价修改时间3");
//        fileMap.put("content3", "评价内容");
//        //fileMap.put("assessUserName3","变更人");
//        fileMap.put("amemo3", "备注");
//
//        ExcelUtils.listToExcel(users, fileMap, "职工档案", response);
//    }


//    @RequestMapping(value = "/importUser.do",produces = "application/json;charset=utf-8")
//    public String importUser(Model model, HttpSession session, @RequestParam(value = "file", required = false) MultipartFile file) throws IOException, ExcelUtils.ExcelException {
//        LinkedHashMap<String,String> fileMap = new LinkedHashMap<>();
//        fileMap.put("姓名","userName");
//        //fileMap.put("性别","gender");
//        //fileMap.put("出生年月","birthday");
//        fileMap.put("手机号","mobile");
//        fileMap.put("email","email");
//        fileMap.put("qq","qq");
//        fileMap.put("政治面貌","politicalStatus");
//        //fileMap.put("婚姻状况","marry");
//        fileMap.put("民族","nation");
//        fileMap.put("身份证号","idCard");
//        fileMap.put("家庭住址","address");
//        fileMap.put("学历","edu");
//        fileMap.put("籍贯","nativePlace");
//        //fileMap.put("入职时间","onDutyDate");
//        //fileMap.put("部门","departName");
//        //fileMap.put("职位","postName");
//        //fileMap.put("是否普通员工","userType");
//        //fileMap.put("直属上级","createName");
//
//        //工作经历
//        fileMap.put("公司名称1","corpName1");
//        //fileMap.put("开始时间","beginTime1");
//        //fileMap.put("结束时间","endTime1");
//        fileMap.put("薪资水平","csalary1");
//        fileMap.put("在职职位","post1");
//        fileMap.put("离职原因","dmemo1");
//        fileMap.put("工作职责","operatingDuty1");
//
//        fileMap.put("公司名称2","corpName2");
//        //fileMap.put("开始时间","beginTime2");
//        //fileMap.put("结束时间","endTime2");
//        fileMap.put("薪资水平","csalary2");
//        fileMap.put("在职职位","post2");
//        fileMap.put("离职原因","dmemo2");
//        fileMap.put("工作职责","operatingDuty2");
//
//        fileMap.put("公司名称3","corpName3");
//        //fileMap.put("开始时间","beginTime3");
//        //fileMap.put("结束时间","endTime3");
//        fileMap.put("薪资水平","csalary3");
//        fileMap.put("在职职位","post3");
//        fileMap.put("离职原因","dmemo3");
//        fileMap.put("工作职责","operatingDuty3");
//        //教育
//        fileMap.put("学校名称1","collegeName1");
//        //fileMap.put("开始时间1","ebeginTime1");
//        //fileMap.put("结束时间1","eendTime1");
//        fileMap.put("专业1","major1");
//        fileMap.put("学历1","degree1");
//        fileMap.put("说明1","ememo1");
//
//        fileMap.put("学校名称2","collegeName2");
//        //fileMap.put("开始时间2","ebeginTime2");
//        //fileMap.put("结束时间2","eendTime2");
//        fileMap.put("专业2","major2");
//        fileMap.put("学历2","degree2");
//        fileMap.put("说明2","ememo2");
//
//        fileMap.put("学校名称3","collegeName3");
//        //fileMap.put("开始时间3","ebeginTime3");
//        //fileMap.put("结束时间3","eendTime3");
//        fileMap.put("专业3","major3");
//        fileMap.put("学历3","degree3");
//        fileMap.put("说明3","ememo3");
//
//        //薪资情况
//        //fileMap.put("薪资修改时间1","operateTime1");
//        fileMap.put("薪资修改原因1","admustResaon1");
//        fileMap.put("薪资","salary1");
//        //fileMap.put("变更人","operatorName1");
//        fileMap.put("备注","smemo1");
//
//        //fileMap.put("薪资修改时间2","operateTime2");
//        fileMap.put("薪资修改原因2","admustResaon2");
//        fileMap.put("薪资","salary2");
//        //fileMap.put("变更人","operatorName2");
//        fileMap.put("备注","smemo2");
//
//        //fileMap.put("薪资修改时间3","operateTime3");
//        fileMap.put("薪资修改原因3","admustResaon3");
//        fileMap.put("薪资","salary3");
//        //fileMap.put("变更人","operatorName3");
//        fileMap.put("备注","smemo3");
//
//        //奖惩情况
//        // fileMap.put("奖惩修改时间1","occurDate1");
//        fileMap.put("奖惩修改内容1","ocontent1");
//        //fileMap.put("变更人","ooperatorName1");
//        fileMap.put("备注","omemo1");
//
//        //fileMap.put("奖惩修改时间2","occurDate2");
//        fileMap.put("奖惩修改内容2","ocontent2");
//        //fileMap.put("变更人","ooperatorName2");
//        fileMap.put("备注","omemo2");
//
//        //fileMap.put("奖惩修改时间3","occurDate3");
//        fileMap.put("奖惩修改内容3","ocontent3");
//        //fileMap.put("变更人","ooperatorName3");
//        fileMap.put("备注","omemo3");
//
//
//        //评价情况
//        //fileMap.put("评价修改时间1","assessDate1");
//        fileMap.put("评价修改内容1","content1");
//        //fileMap.put("变更人","assessUserName1");
//        fileMap.put("备注","amemo1");
//
//        //fileMap.put("评价修改时间2","assessDate2");
//        fileMap.put("评价修改内容2","content2");
//        //fileMap.put("变更人","assessUserName2");
//        fileMap.put("备注","amemo2");
//
//        //fileMap.put("评价修改时间3","assessDate3");
//        fileMap.put("评价修改内容3","content3");
//        //fileMap.put("变更人","assessUserName3");
//        fileMap.put("备注","amemo3");
//        List<User> users;
//        try {
//            users= ExcelUtils.excelToList(file.getInputStream(),"标准模板",
//                    User.class,fileMap,new String[]{"手机号"});
//        }catch (Exception e){
//
//            model.addAttribute("error",e.getMessage());
//            return "redirect:/general/employeeIndex.do";
//        }
//
//
//
//        List<User> userList = userService.getAllUsersByOrganization((Integer) session.getAttribute("oid"),null);
//
//        List<PersonnelOccupation> occupations = new ArrayList<>();
//        List<PersonalRewardPunishment> punishments = new ArrayList<>();
//        List<PersonalEducation> educations = new ArrayList<>();
//        List<PersonnelSalaryLog> salaryLogs = new ArrayList<>();
//        List<PersonalAssessment> assessments = new ArrayList<>();
//
//        //查重复手机号
//        for(User u: userList){
//            if(u.getMobile()!=null&&!"".equals(u.getMobile())){
//
//                for(User s : users){
//                    if(s.getMobile()!=null)
//                        if(s.getMobile().equals(u.getMobile())){
//
//                            model.addAttribute("error","有重复的手机号:"+s.getMobile()+"请确认");
//                            return "redirect:/general/employeeIndex.do";
//                        }
//                }
//            }else {
//                model.addAttribute("error","请确认手机号是否正确");
//                return "redirect:/general/employeeIndex.do";
//            }
//        }
//
//
//        for (User u : users){
//
////            if("".equals(u.getMarry())||u.getMarry()==null){
////                u.setMarry("未婚");
////            }
////            else if("0".equals(u.getMarry())||"未婚".equals(u.getMarry())){
////                u.setMarry("0");
////            }
////            else if ("1".equals(u.getMarry())||"已婚".equals(u.getMarry()))
////                u.setMarry("1");
////
////            if("".equals(u.getGender())||u.getGender()==null){
////                u.setGender("1");
////            }
////            else if("1".equals(u.getGender())||"男".equals(u.getGender())){
////                u.setGender("1");
////            }
////            else if ("0".equals(u.getGender())||"女".equals(u.getGender()))
////                u.setGender("0");
//            if(u.getMobile()!=null&&!"".equals(u.getMobile())){
//                if(u.getMobile().length()!=11){
//                    model.addAttribute("error","请确认手机号是否正确");
//                    return "redirect:/general/employeeIndex.do";
//                }
//            }else {
//                model.addAttribute("error","请确认手机号是否正确");
//                return "redirect:/general/employeeIndex.do";
//            }
//
//
//            if("".equals(u.getUserName())||u.getUserName()==null){
//                model.addAttribute("error","请填写姓名");
//                return "redirect:/general/employeeIndex.do";
//            }
//
//            PersonalEducation education = new PersonalEducation();
//            //教育1
////            education.setBeginTime(u.getEbeginTime1());
////            education.setEndTime(u.getEendTime1());
//            education.setCollegeName(u.getCollegeName1());
//            education.setMajor(u.getMajor1());
//            education.setDegree(u.getDegree1());
//            education.setMemo(u.getEmemo1());
//
//            educations.add(education);
//
//            //教育2
//            education = new PersonalEducation();
//
////            education.setBeginTime(u.getEbeginTime2());
////            education.setEndTime(u.getEendTime2());
//            education.setCollegeName(u.getCollegeName2());
//            education.setMajor(u.getMajor2());
//            education.setDegree(u.getDegree2());
//            education.setMemo(u.getEmemo2());
//
//            educations.add(education);
//
//            //教育3
//            education = new PersonalEducation();
//
////            education.setBeginTime(u.getEbeginTime3());
////            education.setEndTime(u.getEendTime3());
//            education.setCollegeName(u.getCollegeName3());
//            education.setMajor(u.getMajor3());
//            education.setDegree(u.getDegree3());
//            education.setMemo(u.getEmemo3());
//
//            educations.add(education);
//
//            //工作1
//            PersonnelOccupation occupation = new PersonnelOccupation();
//
////            occupation.setBeginTime(u.getBeginTime1());
////            occupation.setEndTime(u.getEndTime1());
//            occupation.setCorpName(u.getCorpName1());
//            occupation.setSalary(u.getCsalary1());
//            occupation.setPost(u.getPost1());
//            occupation.setOperatingDuty(u.getOperatingDuty1());
//            occupation.setMemo(u.getDmemo1());
//
//            occupations.add(occupation);
//
//            //工作2
//            occupation = new PersonnelOccupation();
//
////            occupation.setBeginTime(u.getBeginTime2());
////            occupation.setEndTime(u.getEndTime2());
//            occupation.setCorpName(u.getCorpName2());
//            occupation.setSalary(u.getCsalary2());
//            occupation.setPost(u.getPost2());
//            occupation.setOperatingDuty(u.getOperatingDuty2());
//            occupation.setMemo(u.getDmemo2());
//            occupations.add(occupation);
//
//            //工作3
//            occupation = new PersonnelOccupation();
//
////            occupation.setBeginTime(u.getBeginTime3());
////            occupation.setEndTime(u.getEndTime3());
//            occupation.setCorpName(u.getCorpName3());
//            occupation.setSalary(u.getCsalary3());
//            occupation.setPost(u.getPost3());
//            occupation.setOperatingDuty(u.getOperatingDuty3());
//            occupation.setMemo(u.getDmemo3());
//            occupations.add(occupation);
//
//
//            //奖惩1
//            PersonalRewardPunishment punishment = new PersonalRewardPunishment();
//            //punishment.setOccurDate(u.getOccurDate1());
//            punishment.setContent(u.getOcontent1());
//            //punishment.setOperatorName(u.getOoperatorName1());
//            punishment.setMemo(u.getOmemo1());
//
//            punishments.add(punishment);
//
//            //奖惩2
//            punishment = new PersonalRewardPunishment();
//            //punishment.setOccurDate(u.getOccurDate2());
//            punishment.setContent(u.getOcontent2());
//            //punishment.setOperatorName(u.getOoperatorName2());
//            punishment.setMemo(u.getOmemo2());
//
//            punishments.add(punishment);
//
//            //奖惩3
//            punishment = new PersonalRewardPunishment();
//            //punishment.setOccurDate(u.getOccurDate2());
//            punishment.setContent(u.getOcontent2());
//            //punishment.setOperatorName(u.getOoperatorName2());
//            punishment.setMemo(u.getOmemo2());
//
//            punishments.add(punishment);
//
//            //薪资1
//            PersonnelSalaryLog salaryLog = new PersonnelSalaryLog();
//
//            //salaryLog.setOperateTime(u.getOperateTime1());
//            salaryLog.setAdmustResaon(u.getAdmustResaon1());
//            salaryLog.setSalary(u.getSalary1());
//            //salaryLog.setOperatorName(u.getOoperatorName1());
//            salaryLog.setMemo(u.getSmemo1());
//
//            salaryLogs.add(salaryLog);
//
//            //薪资2
//            salaryLog = new PersonnelSalaryLog();
//
//            //salaryLog.setOperateTime(u.getOperateTime2());
//            salaryLog.setAdmustResaon(u.getAdmustResaon2());
//            salaryLog.setSalary(u.getSalary2());
//            //salaryLog.setOperatorName(u.getOoperatorName2());
//            salaryLog.setMemo(u.getSmemo2());
//
//            salaryLogs.add(salaryLog);
//
//            //薪资3
//            salaryLog = new PersonnelSalaryLog();
//
//            //salaryLog.setOperateTime(u.getOperateTime3());
//            salaryLog.setAdmustResaon(u.getAdmustResaon3());
//            salaryLog.setSalary(u.getSalary3());
//            //salaryLog.setOperatorName(u.getOoperatorName3());
//            salaryLog.setMemo(u.getSmemo3());
//
//            salaryLogs.add(salaryLog);
//
//
//            //评价1
//            PersonalAssessment assessment = new PersonalAssessment();
//            //assessment.setAssessDate(u.getAssessDate1());
//            assessment.setContent(u.getContent1());
//            //assessment.setAssessUserName(u.getAssessUserName1());
//            assessment.setMemo(u.getAmemo1());
//
//            assessments.add(assessment);
//
//            //评价2
//            assessment = new PersonalAssessment();
//            //assessment.setAssessDate(u.getAssessDate2());
//            assessment.setContent(u.getContent2());
//            //assessment.setAssessUserName(u.getAssessUserName2());
//            assessment.setMemo(u.getAmemo2());
//
//            assessments.add(assessment);
//
//            //评价3
//            assessment = new PersonalAssessment();
//            //assessment.setAssessDate(u.getAssessDate3());
//            assessment.setContent(u.getContent3());
//            //assessment.setAssessUserName(u.getAssessUserName3());
//            assessment.setMemo(u.getAmemo3());
//
//            assessments.add(assessment);
//
//
//            u.setPersonalAssessments(new HashSet<>(assessments));
//            u.setPersonalRewardPunishments(new HashSet<>(punishments));
//            u.setPersonalEducations(new HashSet<>(educations));
//            u.setPersonnelOccupations(new HashSet<>(occupations));
//            u.setPersonnelSalaryLogUser(new HashSet<>(salaryLogs));
//
//            Integer oid = (Integer)session.getAttribute("oid");
//            User user = (User) session.getAttribute("user");
//
//            if(u.getCreateName()!=null&&!"".equals(u.getCreateName())){
//                User leader = userService.getUserByUserName(oid,u.getCreateName());
//                if (leader!=null){
//                    u.setLeader(String.valueOf(leader.getUserID()));
//                    u.setParent(leader);
//                }
//            }
//            if(u.getDepartName()!=null&&!"".equals(u.getDepartName())){
//                Organization o = orgService.getDepartmentByIdAndName(oid,u.getDepartName());
//                if (o!=null)
//                    u.setDepartment(String.valueOf(o.getId()));
//            }
//
//
//            u.setOid(oid);
//            u.setOrganization((Organization) session.getAttribute("organization"));
//            u.setIsDuty("1");
//            u.setLogonName(u.getMobile());
//            u.setLogonState(0l);
//            u.setStatus("2");
//            u.setCreator(user.getUserID());
//            u.setCreateName(user.getUserName());
//            u.setCreateTime(new Date());
////            u.setUserType(10);
//            u.setSuperId(orgService.getOrgByOid(oid).getSuperId());
//            u.setOrdinaryEmployees(0);
//            u.setLogonPwd(userService.getLogonPwdByPhone(u.getMobile()));//获取密码
//            u.setRoleCode("staff");
//            try {
//                userService.addUser(u);
//
//            }catch (Exception e){
//                model.addAttribute("error",e.getMessage());
//                return "redirect:/general/employeeIndex.do";
//            }
//
////            userPopedomService.saveUserPopedomByCode("staff",oid,u.getUserID());//给普通员工附上权限
////            //如果不是普通员工，附上请求处理模块权限
////            userPopedomService.saveUserPopedomMc(u.getUserID(),oid);//非普通员工多加请求处理模块
//        }
//        model.addAttribute("success","成功");
//        return "redirect:/general/employeeIndex.do";
//    }

    //批量导入优化接口
    //返回值 status;//0失败，1成功
//    @RequestParam(value = "file", required = false) MultipartFile file
    //    private List<User> trueUserList;//成功导入员工列表
    //    private List<User> falseUserList;//无法导入员工列表
    //    private Integer importSum;//导入总数
    //    private Integer falseImportSum;//无法导入总数
    @ResponseBody
    @RequestMapping(value = "/newImportUser.do")
    public ImportRespObject newImportUser(User user, Integer sonOid, String filePath) throws IOException, ExcelUtils.ExcelException {
        Integer oid = user.getOid();
        if (sonOid != null) { // 1.214多地点 lixu加
            oid = orgService.getOidByOrgSonOrg(oid, sonOid);
        }
        ImportRespObject importRespObject = new ImportRespObject();
        File file = uploadService.copyTempFile(filePath);
        if (file.exists()) {
            LinkedHashMap<String, String> fileMap = new LinkedHashMap<>();
            fileMap.put("姓名", "userName");
            fileMap.put("手机号", "mobile");
            List<User> userLists;
            try {
                userLists = ExcelUtils.excelToList(new FileInputStream(file), "标准模板",
                        User.class, fileMap, new String[]{"姓名", "手机号"});//new String[]{"手机号"}主键为手机号，不可重复，此处取消限制
                importRespObject.setStatus(1);
            } catch (Exception e) {
                //model.addAttribute("error",e.getMessage());
                importRespObject.setStatus(0);
                return importRespObject;
            }
            List<User> users = new ArrayList<>();
            for (User u : userLists) {
                int number = 0;
                if (!"".equals(u.getUserName()) && u.getUserName() != null) {
                    number = 1;
                }
                if (!"".equals(u.getMobile()) && u.getMobile() != null) {
                    number = 1;
                }
                if (number > 0) {
                    users.add(u);
                }
            }
            if (users.size() == 0) {
                importRespObject.setStatus(0);
                return importRespObject;
            }
            List<User> trueUserList = new ArrayList<>();
            List<User> falseUserList = new ArrayList<>();
            List<User> userList = userService.getUserListByOrgSonOrg(oid);  // 查重加 子机构人员  1.214多地点 lixu改
            List<User> userLockList = userImportService.getAllLockUsersByOrganization(oid);
            for (int i = 0; i < users.size(); i++) {
                User u = users.get(i);
                int status = 1;
                //1.手机号位数不为11位的， 姓名，手机号为空的
                if (u.getMobile() == null || "".equals(u.getMobile()) || u.getUserName() == null || "".equals(u.getUserName())) {
                    status = 0;
                    falseUserList.add(u);
                } else if (u.getMobile().length() != 11) {
                    status = 0;
                    falseUserList.add(u);
                }
                //2.手机号自身重复的
                if (status == 1) {
                    for (int j = 0; j < users.size(); j++) {
                        if (u.getMobile().equals(users.get(j).getMobile()) && i != j) {
                            status = 0;
                            falseUserList.add(u);
                            break;
                        }
                    }
                }
                //3.手机号和系统内存在的出现重复的
                if (status == 1) {
                    for (User user1 : userList) {
                        if (user1.getMobile().equals(u.getMobile())) {
                            status = 0;
                            falseUserList.add(u);
                            break;
                        }
                    }
                }
                //4.手机号和被冻结的手机号重复的
                if (status == 1) {
                    for (User user1 : userLockList) {
                        if (user1.getMobile().equals(u.getMobile())) {
                            status = 0;
                            falseUserList.add(u);
                            break;
                        }
                    }
                }
                if (status == 1) {
                    trueUserList.add(u);
                }
            }
            importRespObject.setFalseImportSum(falseUserList.size());
            importRespObject.setFalseUserList(falseUserList);
            importRespObject.setTrueUserList(trueUserList);
            importRespObject.setImportSum(users.size());
            return importRespObject;
        } else {
            return importRespObject;
        }
        //            新增用户
//            Integer oid = (Integer)session.getAttribute("oid");
//            User user = (User) session.getAttribute("user");
//
//            u.setOid(oid);
//            u.setOrganization((Organization) session.getAttribute("organization"));
//            u.setIsDuty("1");
//            u.setLogonName(u.getMobile());
//            u.setLogonState(0l);
//            u.setStatus("2");
//            u.setCreator(user.getUserID());
//            u.setCreateName(user.getUserName());
//            u.setCreateTime(new Date());
////            u.setUserType(10);
//            u.setSuperId(orgService.getOrgByOid(oid).getSuperId());
//            u.setOrdinaryEmployees(0);
//            u.setLogonPwd(userService.getLogonPwdByPhone(u.getMobile()));//获取密码
//            u.setRoleCode("staff");
//            try {
//                userService.addUser(u);
//
//            }catch (Exception e){
//                model.addAttribute("error",e.getMessage());
//                return "redirect:/general/employeeIndex.do";
//            }

//            userPopedomService.saveUserPopedomByCode("staff",oid,u.getUserID());//给普通员工附上权限
//            //如果不是普通员工，附上请求处理模块权限
//            userPopedomService.saveUserPopedomMc(u.getUserID(),oid);//非普通员工多加请求处理模块
//        }
    }

    //type=1为通用商品 =2为专属商品
    //importOption;//导入选项:1-全部,2-开增值税专用发票的,3-开普通发票的,4-不开发票的,5-通用商品,不开增值税专用发票的
    //taxRate;//税率
    //是否含税价:1-含税,0不含税
    // isSaled;//是否销售过,1销售过,0未销售过
    @ResponseBody
    @RequestMapping(value = "/newImportProduct.do")
    public ImportRespProduct newImportProduct(User user, String filePath, ImportRespProduct proInfo) throws IOException, ExcelUtils.ExcelException {
        ImportRespProduct importRespProduct = new ImportRespProduct();
        importRespProduct.setType(proInfo.getType());
        File file = uploadService.copyTempFile(filePath);
        if (file.exists()) {
            LinkedHashMap<String, String> fileMap = new LinkedHashMap<>();
            fileMap.put("商品名称", "name");
            fileMap.put("商品代号", "code");
            fileMap.put("规格", "specifications");
            fileMap.put("型号", "model");
            fileMap.put("计量单位", "unit");
            fileMap.put("最低库存", "miniStock");
            if (proInfo.getImportOption() != 1 && (proInfo.getImportOption() == 2 && proInfo.getTaxInclusive() != null && proInfo.getTaxInclusive() == 1))
                fileMap.put("增值税专用发票含税单价", "unitPrice");
            if (proInfo.getImportOption() != 1 && (proInfo.getImportOption() == 2 && proInfo.getTaxInclusive() != null && proInfo.getTaxInclusive() == 0))
                fileMap.put("增值税专用发票不含税单价", "unitPriceNotax");
            if (proInfo.getImportOption() != 1 && (proInfo.getImportOption() == 3 || proInfo.getType() == 1))
                fileMap.put("开普通发票时的开票单价", "unitPriceInvoice");
            if (proInfo.getImportOption() != 1 && (proInfo.getImportOption() == 4 || proInfo.getType() == 1))
                fileMap.put("不开发票时的单价", "unitPriceNoinvoice");
            fileMap.put("商品说明", "desc");
            List<PdCommodityImport> productImportList;
            try {
                productImportList = ExcelUtils.excelToList(new FileInputStream(file), "标准模板",
                        PdCommodityImport.class, fileMap, new String[]{"商品名称", "商品代号"});//new String[]{"手机号"}主键为手机号，不可重复，此处取消限制
                importRespProduct.setStatus(1);
            } catch (Exception e) {
                importRespProduct.setStatus(0);
                return importRespProduct;
            }
            List<PdCommodityImport> productImports = new ArrayList<>();
            for (PdCommodityImport pdProductImport : productImportList) {
                int number = 0;
                if (!"".equals(pdProductImport.getCode()) && pdProductImport.getCode() != null) {
                    number = 1;
                } else if (!"".equals(pdProductImport.getName()) && pdProductImport.getName() != null) {
                    number = 1;
                } else if (!"".equals(pdProductImport.getSpecifications()) && pdProductImport.getSpecifications() != null) {
                    number = 1;
                } else if (!"".equals(pdProductImport.getModel()) && pdProductImport.getModel() != null) {
                    number = 1;
                } else if (!"".equals(pdProductImport.getUnit()) && pdProductImport.getUnit() != null) {
                    number = 1;
                } else if (!"".equals(pdProductImport.getMiniStock()) && pdProductImport.getMiniStock() != null && pdProductImport.getMiniStock().intValue() != 0) {
                    number = 1;
                } else if (!"".equals(pdProductImport.getDesc()) && pdProductImport.getDesc() != null) {
                    number = 1;
                } else if (proInfo.getImportOption() != 1 && (proInfo.getImportOption() == 2 && proInfo.getTaxInclusive() == 1) && !"".equals(pdProductImport.getUnitPrice()) && pdProductImport.getUnitPrice() != null && pdProductImport.getUnitPrice() != 0) {
                    number = 1;
                } else if (proInfo.getImportOption() != 1 && proInfo.getImportOption() == 2 && proInfo.getTaxInclusive() == 0 && !"".equals(pdProductImport.getUnitPriceNotax()) && pdProductImport.getUnitPriceNotax() != null && pdProductImport.getUnitPriceNotax() != 0) {
                    number = 1;
                } else if (proInfo.getImportOption() != 1 && (proInfo.getImportOption() == 3 || proInfo.getType() == 1) && !"".equals(pdProductImport.getUnitPriceInvoice()) && pdProductImport.getUnitPriceInvoice() != null && pdProductImport.getUnitPriceInvoice() != 0) {
                    number = 1;
                } else if (proInfo.getImportOption() != 1 && (proInfo.getImportOption() == 4 || proInfo.getType() == 1) && !"".equals(pdProductImport.getUnitPriceNoinvoice()) && pdProductImport.getUnitPriceNoinvoice() != null && pdProductImport.getUnitPriceNoinvoice() != 0) {
                    number = 1;
                }
                if (number > 0) {
                    productImports.add(pdProductImport);
                }
            }
            if (productImports.size() == 0) {
                importRespProduct.setStatus(0);
                return importRespProduct;
            }
            List<PdCommodityImport> truePdList = new ArrayList<>();
            List<PdCommodityImport> falsePdList = new ArrayList<>();
            List<PdMerchandise> pdList = commodityImportService.getPdByOid(user.getOid());//已有商品
            List<MtUnit> mtUnitList = unitService.selectStopUnitList(user.getOid());
            List<PdCommodityImport> importList = commodityImportService.getImportPdByOid(user.getOid());
            ;//正在导入的商品
            for (int i = 0; i < productImports.size(); i++) {
                PdCommodityImport productImport = productImports.get(i);
                if (proInfo.getCustomer() != null)
                    productImport.setCustomer(proInfo.getCustomer());
                if (proInfo.getImportOption() == 2) {
                    productImport.setInvoiceType("1");
                    productImport.setTaxRate(proInfo.getTaxRate().doubleValue());
                    DecimalFormat df = new DecimalFormat("0.00");
                    if (productImport.getUnitPrice() != null && productImport.getUnitPrice() != 0) {
                        String x = df.format(productImport.getUnitPrice());
                        productImport.setUnitPrice(Double.valueOf(x).doubleValue());
                        ;
                        productImport.setUnitPriceNotax(productImport.getUnitPrice() * (1 - (proInfo.getTaxRate().doubleValue() / 100)));
                    }
                    if (productImport.getUnitPriceNotax() != null && productImport.getUnitPriceNotax() != 0) {
                        String x = df.format(productImport.getUnitPriceNotax());
                        productImport.setUnitPriceNotax(Double.valueOf(x).doubleValue());
                        ;
                        productImport.setUnitPrice(productImport.getUnitPriceNotax() / (1 - (proInfo.getTaxRate().doubleValue() / 100)));
                    }

                } else if (proInfo.getImportOption() == 3) {
                    productImport.setInvoiceType("2");
                } else if (proInfo.getImportOption() == 4 || proInfo.getImportOption() == 5) {
                    productImport.setInvoiceType("4");
                }
                int status = 1;
                //名称或代号未录入
                if (productImport.getName() == null || "".equals(productImport.getName()) || productImport.getCode() == null || "".equals(productImport.getCode()) || productImport.getUnit() == null || "".equals(productImport.getUnit())) {
                    status = 0;
                    falsePdList.add(productImport);
                }
                //本次录入的代号互相重复
                if (status == 1) {
                    for (int j = 0; j < productImports.size(); j++) {
                        if (productImport.getCode().equals(productImports.get(j).getCode()) && i != j) {
                            status = 0;
                            falsePdList.add(productImport);
                            break;
                        }
                    }
                }
                //1.109改，新增判断，已停用计量单位判断
                if (status == 1) {
                    for (MtUnit mtUnit : mtUnitList) {
                        if (mtUnit.getName().equals(productImport.getUnit())) {
                            status = 0;
                            falsePdList.add(productImport);
                            break;
                        }
                    }
                } //本次录入的代号,和已有代号重复
                if (status == 1) {
                    for (PdMerchandise pdMerchandise : pdList) {
                        if (pdMerchandise.getOuterSn().equals(productImport.getCode())) {
                            status = 0;
                            falsePdList.add(productImport);
                            break;
                        }
                    }
                }
                if (status == 1) {
                    for (PdCommodityImport pdProductImport : importList) {
                        if (pdProductImport.getCode().equals(productImport.getCode())) {
                            status = 0;
                            falsePdList.add(productImport);
                            break;
                        }
                    }
                }
                if (status == 1) {
                    if (proInfo.getImportOption() == 2) {
                        if (proInfo.getTaxInclusive() == 1 && (productImport.getUnitPrice() == null || "".equals(productImport.getUnitPrice()) || productImport.getUnitPrice() == 0)) {
                            status = 0;
                            productImport.setUnitPrice(null);
                            falsePdList.add(productImport);
                        } else if (proInfo.getTaxInclusive() == 0 && (productImport.getUnitPriceNotax() == null || "".equals(productImport.getUnitPriceNotax()) || productImport.getUnitPriceNotax() == 0)) {
                            status = 0;
                            productImport.setUnitPriceNotax(null);
                            falsePdList.add(productImport);
                        }
                    } else if (proInfo.getType() == 2) {
                        if (proInfo.getImportOption() == 3) {
                            if (productImport.getUnitPriceInvoice() == null || "".equals(productImport.getUnitPriceInvoice()) || productImport.getUnitPriceInvoice() == 0) {
                                status = 0;
                                productImport.setUnitPriceInvoice(null);
                                falsePdList.add(productImport);
                            }
                        } else if (proInfo.getImportOption() == 4) {
                            if (productImport.getUnitPriceNoinvoice() == null || "".equals(productImport.getUnitPriceNoinvoice()) || productImport.getUnitPriceNoinvoice() == 0) {
                                status = 0;
                                productImport.setUnitPriceNoinvoice(null);
                                falsePdList.add(productImport);
                            }
                        }
                    }
                }
                if (status == 1) {
                    truePdList.add(productImport);
                }
            }
            for (PdCommodityImport falsePd : falsePdList) {
                if (proInfo.getImportOption() == 2) {
                    if (proInfo.getTaxInclusive() == 1 && (falsePd.getUnitPrice() == null || "".equals(falsePd.getUnitPrice()) || falsePd.getUnitPrice() == 0)) {
                        falsePd.setUnitPrice(null);
                    } else if (proInfo.getTaxInclusive() == 0 && (falsePd.getUnitPriceNotax() == null || "".equals(falsePd.getUnitPriceNotax()) || falsePd.getUnitPriceNotax() == 0)) {
                        falsePd.setUnitPriceNotax(null);
                    }
                } else if (proInfo.getType() == 2) {
                    if (proInfo.getImportOption() == 3 && (falsePd.getUnitPriceInvoice() == null || "".equals(falsePd.getUnitPriceInvoice()) || falsePd.getUnitPriceInvoice() == 0)) {
                        falsePd.setUnitPriceInvoice(null);
                    } else if (proInfo.getImportOption() == 4 && (falsePd.getUnitPriceNoinvoice() == null || "".equals(falsePd.getUnitPriceNoinvoice()) || falsePd.getUnitPriceNoinvoice() == 0)) {
                        falsePd.setUnitPriceNoinvoice(null);
                    }
                }
            }
            importRespProduct.setFalseImportSum(falsePdList.size());
            importRespProduct.setFalsePdProducList(falsePdList);
            importRespProduct.setTruePdProductList(truePdList);
            importRespProduct.setImportSum(productImports.size());
            importRespProduct.setIsSaled(proInfo.getIsSaled());
            importRespProduct.setTaxRate(proInfo.getTaxRate());
            importRespProduct.setTaxInclusive(proInfo.getTaxInclusive());
            importRespProduct.setImportOption(proInfo.getImportOption());
            return importRespProduct;
        } else {
            return importRespProduct;
        }
    }

    //批量导入物料接口
    @ResponseBody
    @RequestMapping(value = "/ImportMaterial.do")
    public ImportRespObject ImportMaterial(User user, String filePath) throws IOException, ExcelUtils.ExcelException {
        ImportRespObject importRespObject = new ImportRespObject();
        File file = uploadService.copyTempFile(filePath);
        if (file.exists()) {
            LinkedHashMap<String, String> fileMap = new LinkedHashMap<>();
            fileMap.put("材料名称", "name");
            fileMap.put("材料代号", "code");
            fileMap.put("规格", "specifications");
            fileMap.put("型号", "model");
            fileMap.put("计量单位", "unit");
            fileMap.put("备注", "memo");
            List<MtBase> mtBaseLists;
            try {
                mtBaseLists = ExcelUtils.excelToList(new FileInputStream(file), "标准模板",
                        MtBase.class, fileMap, new String[]{"材料名称", "材料代号"});
                importRespObject.setStatus(1);
            } catch (Exception e) {
//             model.addAttribute("error",e.getMessage());
                importRespObject.setStatus(0);
                return importRespObject;
            }
            List<MtBase> mtBases = new ArrayList<>();
            for (MtBase u : mtBaseLists) {
                int number = 0;
                if (!"".equals(u.getName()) && u.getName() != null) {
                    number = 1;
                } else if (!"".equals(u.getCode()) && u.getCode() != null) {
                    number = 1;
                } else if (!"".equals(u.getSpecifications()) && u.getSpecifications() != null) {
                    number = 1;
                } else if (!"".equals(u.getModel()) && u.getModel() != null) {
                    number = 1;
                } else if (!"".equals(u.getUnit()) && u.getUnit() != null) {
                    number = 1;
                } else if (!"".equals(u.getMemo()) && u.getMemo() != null) {
                    number = 1;
                }
                if (number > 0) {
                    mtBases.add(u);
                }
            }
            if (mtBases.size() == 0) {
                importRespObject.setStatus(0);
                return importRespObject;
            }
            List<MtBase> trueMtList = new ArrayList<>();
            List<MtBase> falseMtList = new ArrayList<>();
            List<MtUnit> mtUnitList = unitService.selectStopUnitList(user.getOid());
            List<MtBase> mtBaseList = mtImportService.getMtByOid(user.getOid());
            for (int i = 0; i < mtBases.size(); i++) {
                MtBase m = mtBases.get(i);
                int status = 1;
                //名称或代号未录入
                if (m.getName() == null || "".equals(m.getName()) || m.getCode() == null || "".equals(m.getCode()) || m.getUnit() == null || "".equals(m.getUnit())) {
                    status = 0;
                    falseMtList.add(m);
                }
                //本次录入的代号互相重复
                if (status == 1) {
                    for (int j = 0; j < mtBases.size(); j++) {
                        if (m.getCode().equals(mtBases.get(j).getCode()) && i != j) {
                            status = 0;
                            falseMtList.add(m);
                            break;
                        }
                    }
                }
                //1.109改，新增判断，已停用计量单位判断
                if (status == 1) {
                    for (MtUnit mtUnit : mtUnitList) {
                        if (mtUnit.getName().equals(m.getUnit())) {
                            status = 0;
                            falseMtList.add(m);
                            break;
                        }
                    }
                }
                //本次录入的代号,和已有代号重复
                if (status == 1) {
                    for (MtBase mtBase : mtBaseList) {
                        if (mtBase.getCode().equals(m.getCode())) {
                            status = 0;
                            falseMtList.add(m);
                            break;
                        }
                    }
                }
                if (status == 1) {
                    trueMtList.add(m);
                }
            }
            importRespObject.setFalseMtBaseList(falseMtList);
            importRespObject.setTrueMtBaseList(trueMtList);
            importRespObject.setFalseImportSum(falseMtList.size());
            importRespObject.setImportSum(mtBases.size());
            return importRespObject;
        } else {
            return importRespObject;
        }
    }
}
