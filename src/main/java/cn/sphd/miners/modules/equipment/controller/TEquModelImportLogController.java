package cn.sphd.miners.modules.equipment.controller;

import java.util.List;

import cn.sphd.miners.modules.equipment.entity.TEquModelImportLog;
import cn.sphd.miners.modules.equipment.service.ITEquModelImportLogService;
import cn.sphd.miners.modules.mci.utils.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import static cn.sphd.miners.modules.mci.utils.AjaxResult.toAjax;

/**
 * Controller
 *
 * <AUTHOR>
 * @date 2023-03-30
 */
@Controller
@RequestMapping("/system/log")
public class TEquModelImportLogController {
    private String prefix = "system/log" ;

    @Autowired
    private ITEquModelImportLogService tEquModelImportLogService;

    @GetMapping()
    public String log() {
        return prefix + "/log" ;
    }

    /**
     * 查询列表
     */
    @PostMapping("/list")
    @ResponseBody
    public List<TEquModelImportLog> list(TEquModelImportLog tEquModelImportLog) {
        List<TEquModelImportLog> list = tEquModelImportLogService.selectTEquModelImportLogList(tEquModelImportLog);
        return list;
    }

    /**
     * 导出列表
     */

    /**
     * 新增
     */
    @GetMapping("/add")
    public String add() {
        return prefix + "/add" ;
    }

    /**
     * 新增保存
     */
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(TEquModelImportLog tEquModelImportLog) {
        return toAjax(tEquModelImportLogService.insertTEquModelImportLog(tEquModelImportLog));
    }

    /**
     * 修改
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap) {
        TEquModelImportLog tEquModelImportLog =
                tEquModelImportLogService.selectTEquModelImportLogById(id);
        mmap.put("tEquModelImportLog" , tEquModelImportLog);
        return prefix + "/edit" ;
    }

    /**
     * 修改保存
     */
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(TEquModelImportLog tEquModelImportLog) {
        return toAjax(tEquModelImportLogService.updateTEquModelImportLog(tEquModelImportLog));
    }

    /**
     * 删除
     */
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(tEquModelImportLogService.deleteTEquModelImportLogByIds(ids));
    }
}
