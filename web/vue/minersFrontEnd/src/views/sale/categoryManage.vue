<template>
  <div class="contact">
    <div class="dialog">
      <el-dialog
          v-model="dialog_visible_manageNextLevelCategory"
          title="类别管理"
          width="1100"
          :close-on-click-modal="false"
          :draggable="true"
      >
        <div class="mainCon" style="width: 1000px; margin: 0 auto; min-height: 300px">
          <div class="row">
            <b class="ty-color-blue"> {{table_category.cateInfo.name}} </b>  下共有如下 {{table_category.data?.length}} 个直属子类别：
            <div class="rightBtn">
              <el-button type="primary" @click="newCategory()">新增类别</el-button>
            </div>
          </div>
          <el-table :data="table_category.data" border style="margin-top: 16px">
            <el-table-column label="商品类别的名称" width="200">
              <template #default="scope">
                {{ scope.row.name }}
              </template>
            </el-table-column>
            <el-table-column label="级别">
              <template #default="scope">
                {{scope.row.level}} 级
              </template>
            </el-table-column>
            <el-table-column label="包含商品">
              <template #default="scope">
                {{scope.row.mdseCount || 0}} 种
              </template>
            </el-table-column>
            <el-table-column label="直属子类别">
              <template #default="scope">
                {{scope.row.childrenCount || 0}} 个
              </template>
            </el-table-column>
            <el-table-column label="类别的创建" width="250">
              <template #default="scope">
                {{scope.row.createName}}
                {{$filter.format(scope.row.createTime)}}
              </template>
            </el-table-column>
            <el-table-column label="管理" width="250">
              <template #default="scope">
                <el-button type="primary" link @click="editCategory(scope.row)"><b>修改名称</b></el-button>
                <el-button type="danger" link @click="deleteCategory(scope.row)"><b>删除</b></el-button>
                <el-button type="primary" link @click="mange_nextLevelCate_dialog(scope.row)"><b>管理下一级类别</b></el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination
                layout="prev, pager, next"
                :page-count="table_category.pageInfo.totalPage"
                background
                hide-on-single-page
                @current-change="pagination_category_change"
                style="justify-content: flex-end"
            />
          </div>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="backPrevCate()" v-show="table_category.cateInfo.parentId">返回上一级</el-button>
            <el-button @click="dialog_visible_manageNextLevelCategory = false">关闭</el-button>
          </div>
        </template>
      </el-dialog>
      <el-dialog
          v-model="dialog_visible_newCategory"
          title="新增类别"
          width="500"
          :close-on-click-modal="false"
          :draggable="true"
      >
        <div class="mainCon" style="width: 400px; margin: 0 auto">
          <div class="row">
            新增的类别属于：
          </div>
          <div class="row">
            <b class="ty-color-orange" style="padding: 16px 8px">{{ form_editCate.new.path }}</b>
          </div>
          <div class="row">
            <el-form-item label="请录入类别的新名称" label-position="top" style="width: 100%">
              <el-input v-model="form_editCate.new.name" placeholder="请录入" show-word-limit maxlength="15" clearable/>
            </el-form-item>
          </div>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="dialog_visible_newCategory = false">取消</el-button>
            <el-button type="primary" @click="submit_newCategory">确定</el-button>
          </div>
        </template>
      </el-dialog>
      <el-dialog
          v-model="dialog_visible_editCategory"
          title="修改类别名称"
          width="500"
          :close-on-click-modal="false"
          :draggable="true"
      >
        <div class="mainCon" style="width: 400px; margin: 0 auto">
          <div class="row">
            类别当前的名称：
          </div>
          <div class="row">
            <b class="ty-color-blue" style="padding: 16px 8px">{{ form_editCate.edit.cateInfo.name }}</b>
          </div>
          <div class="row">
            <el-form-item label="请录入类别的新名称" label-position="top" style="width: 100%">
              <el-input v-model="form_editCate.edit.name" placeholder="请录入" show-word-limit maxlength="15" clearable/>
            </el-form-item>
          </div>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="dialog_visible_editCategory = false">取消</el-button>
            <el-button type="primary" @click="submit_editCategory">确定</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
    <div class="ty-container" id="home">
      <div class="page">
        <section>
          <div class="row head" style="display: flex; align-items: center">
            系统中商品的一级类别如下：
          </div>
          <el-table :data="table_firstCategory.data" border style="width: 100%; margin-top: 16px">
            <el-table-column label="商品类别的名称">
              <template #default="scope">
                {{ scope.row.name }}
              </template>
            </el-table-column>
            <el-table-column label="级别">
              <template #default="scope">
                {{scope.row.level}} 级
              </template>
            </el-table-column>
            <el-table-column label="包含商品">
              <template #default="scope">
                {{scope.row.mdseCount || 0}} 种
              </template>
            </el-table-column>
            <el-table-column label="直属子类别">
              <template #default="scope">
                {{scope.row.childrenCount || 0}} 个
              </template>
            </el-table-column>
            <el-table-column label="类别的创建">
              <template #default="scope">
                {{scope.row.createName}}
                {{$filter.format(scope.row.createTime)}}
              </template>
            </el-table-column>
            <el-table-column label="管理">
              <template #default="scope">
                <el-button type="primary" link @click="mange_nextLevelCate(scope.row)"><b>管理下一级类别</b></el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination
                layout="prev, pager, next"
                :page-count="table_firstCategory.pageInfo.totalPage"
                background
                hide-on-single-page
                @current-change="pagination_firstCategory_change"
                style="justify-content: flex-end"
            />
          </div>
        </section>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.ty-container{
  margin-top:35px; padding:10px;
}
.ty-page-header{
  display: flex;
  line-height: 24px;
  padding: 0 0 0 70px;
  color: #5d9cec;
  margin-top: 16px;
  .page-header__left{
    display: flex;
    cursor: pointer;
    margin-right: 40px;
    position: relative;
    &::after {
      content: "";
      position: absolute;
      width: 1px;
      height: 16px;
      right: -20px;
      top: 50%;
      transform: translateY(-50%);
      background-color: #dcdfe6;
    }
    .icon-back {
      font-size: 18px;
      margin-right: 6px;
      align-self: center;
      position: relative;
      top: 1px;
    }
  }
}
.page{
  padding: 8px 0;
  position: relative;
  margin: 0 70px;
  .panel-box{
    border-top: 1px solid #ddd;
    padding-top: 8px;
    &:first-child{
      border: none
    }
  }
}
.ty-hr{
  margin: 16px 0;
  width: 100%;
  height: 1px;
  background: #eee;
}
.row{
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  .rightBtn{
    flex: auto;
    text-align: right;
  }
  &.head{
    margin-bottom: 16px;
  }
}
.text-center{
  text-align: center;
}
:deep(.el-upload--picture-card){
  width: 64px;
  height: 64px;
  &>i{
    font-size: 16px;
  }
}
:deep(.el-upload-list--picture-card .el-upload-list__item){
  width: 64px;
  height: 64px;
}
:root{
  --el-upload-list-picture-card-size: 48px
}
.fileBox{
  border-radius: 3px;
  width: 100%;
  border: 1px solid rgb(228, 231, 237);
  min-height: 14px;
  padding: 8px;
}
:deep(.el-upload){
  display: none;
}
.pagination{
  text-align: right;
  margin-top: 16px;
}
</style>
<script>
import {beforeRouteLeave, initNav} from "@/utils/routeChange"
import * as api from "@/api/sale.js"
import {getPathCategoryListById} from "@/api/sale.js";
export default {
  data() {
    return {
      pageName: 'sale_category',
      table_firstCategory: {
        data: [],
        pageInfo: {
          currentPageNo: 1,
          pageSize: 20 ,
          totalPage: 1
        }
      },
      table_category: {
        data: [],
        cateInfo: {},
        pageInfo: {
          currentPageNo: 1,
          pageSize: 20 ,
          totalPage: 1
        }
      },
      form_editCate: {
        cateInfo: {},
        new: {
          name: ''
        },
        edit: {
          cateInfo: '',
          path: '',
          name: ''
        }
      },
      dialog_visible_manageNextLevelCategory: false,
      dialog_visible_newCategory: false,
      dialog_visible_editCategory: false
    }
  },
  beforeRouteLeave(to, from, next) {
    beforeRouteLeave(to, from, next, this)
  },
  mounted() {
    initNav({mid: 'qa1', name: '类别管理', pageName: this.pageName}, this.created, this)
  },
  methods: {
    created () {
      console.log('rootPath', this.rootPath)
      this.getFirstCategory()
    },
    getFirstCategory(currentPageNo = 1) {
      api.getFirstCategoryListByOid({currentPageNo: currentPageNo, pageSize: 15})
          .then(res => {
            let data = res.data.data
            this.table_firstCategory = {
              data: data.pdCategoryList,
              pageInfo: data.pageInfo
            }
          })
    },
    mange_nextLevelCate(item) {
      this.dialog_visible_manageNextLevelCategory = true
      this.form_editCate.cateInfo = item
      this.getLevelCate(item.id)
    },
    mange_nextLevelCate_dialog(item) {
      this.form_editCate.cateInfo = item
      this.getLevelCate(item.id)
    },
    newCategory() {
      this.dialog_visible_newCategory = true
      this.form_editCate.new.name = ''
      let that = this
      api.getPathCategoryListById(this.table_category.cateInfo.id)
          .then(res => {
            let data = res.data.data
            let pathArr = data.map(item => item.name)
            that.form_editCate.new.path = pathArr.join(' > ')
          })
    },
    editCategory(item) {
      if (item.locked) {
        this.$message({
          type: 'error',
          message: `操作失败！此类别的名称不可修改！`
        })
        return false
      }
      this.dialog_visible_editCategory = true
      this.form_editCate.edit.cateInfo = item
      this.form_editCate.edit.name = ''
    },
    deleteCategory(item) {
      if (item.locked) {
        this.$message({
          type: 'error',
          message: `操作失败！此类别不可删除！`
        })
        return false
      }
      this.$messageBox.confirm(
          `确定删除该类别？`,
          '！提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
      )
          .then(() => {
            api.deletePdCategory(item.id)
                .then(res => {
                  let success = res.data.success
                  let error = res.data.error
                  if (success === 1) {
                    this.$message({
                      type: 'success',
                      message: '操作成功！'
                    })
                    this.refreshCateList()
                    this.getFirstCategory()
                  } else {
                    this.$message({
                      type: 'error',
                      message: error.code
                    })
                  }
                })
          })
    },
    submit_newCategory() {
      let params = {
        id: this.table_category.cateInfo.id, //（被修改的类别id）
        name: this.form_editCate.new.name // 分类名称
      }
      if (!params.name) {
        this.$message({
          type: 'error',
          message: '请录入类别的名称！'
        })
        return false
      }
      let that = this
      api.addPdCategory(params)
          .then(res => {
            let success = res.data.success
            let error = res.data.error
            if (success === 1) {
              this.$message({
                type: 'success',
                message: '操作成功！'
              })
              that.dialog_visible_newCategory = false
              that.refreshCateList()
              that.getFirstCategory()
            } else {
              this.$message({
                type: 'error',
                message: error.code
              })
            }
          })
    },
    submit_editCategory() {
      let params = {
        id: this.form_editCate.edit.cateInfo.id, //（被修改的类别id）
        name: this.form_editCate.edit.name // 分类名称
      }
      if (!params.name) {
        this.$message({
          type: 'error',
          message: '请录入类别的名称！'
        })
        return false
      }
      api.editPdCategory(params)
          .then(res => {
            let success = res.data.success
            let error = res.data.error
            if (success === 1) {
              this.$message({
                type: 'success',
                message: '操作成功！'
              })
              this.dialog_visible_editCategory = false
              this.refreshCateList()
            } else {
              this.$message({
                type: 'error',
                message: error.code
              })
            }
          })
    },
    backPrevCate() {
      let parentId = this.table_category.cateInfo.parentId
      this.getLevelCate(parentId)
    },
    refreshCateList() {
      console.log('refreshCateList chufa')
      let id = this.table_category.cateInfo.id
      this.getLevelCate(id)
    },
    getLevelCate(id, currentPageNo = 1) {
      api.getSonCategoryListByPid({id: id, currentPageNo: currentPageNo, pageSize: 15})
          .then(res => {
            let response = res.data
            let success = response.success
            let data = response.data
            if (success === 0) {
              this.$message({
                type: 'error',
                message: response.errorCode
              })
            } else {
              this.table_category = {
                data: data.pdCategoryList,
                pageInfo: data.pageInfo,
                cateInfo: data.categoryInfo
              }
            }
          })
    },
    pagination_category_change(currentPageNo) {
      let id = this.table_category.cateInfo.id
      this.getLevelCate(id, currentPageNo)
    },
    pagination_firstCategory_change(currentPageNo) {
      this.getFirstCategory(currentPageNo)
    }
  }
}


</script>