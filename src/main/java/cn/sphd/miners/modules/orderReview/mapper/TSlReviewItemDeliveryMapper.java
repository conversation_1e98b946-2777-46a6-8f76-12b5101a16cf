package cn.sphd.miners.modules.orderReview.mapper;

import cn.sphd.miners.modules.orderReview.entity.TSlReviewItemDelivery;

import java.util.List;

/**
 * Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
public interface TSlReviewItemDeliveryMapper 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    public TSlReviewItemDelivery selectTSlReviewItemDeliveryById(Long id);

    /**
     * 查询列表
     * 
     * @param tSlReviewItemDelivery 
     * @return 集合
     */
    public List<TSlReviewItemDelivery> selectTSlReviewItemDeliveryList(TSlReviewItemDelivery tSlReviewItemDelivery);

    /**
     * 新增
     * 
     * @param tSlReviewItemDelivery 
     * @return 结果
     */
    public int insertTSlReviewItemDelivery(TSlReviewItemDelivery tSlReviewItemDelivery);

    /**
     * 修改
     * 
     * @param tSlReviewItemDelivery 
     * @return 结果
     */
    public int updateTSlReviewItemDelivery(TSlReviewItemDelivery tSlReviewItemDelivery);

    /**
     * 删除
     * 
     * @param id 主键
     * @return 结果
     */
    public int deleteTSlReviewItemDeliveryById(Long id);

    /**
     * 批量删除
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTSlReviewItemDeliveryByIds(Long[] ids);
}
