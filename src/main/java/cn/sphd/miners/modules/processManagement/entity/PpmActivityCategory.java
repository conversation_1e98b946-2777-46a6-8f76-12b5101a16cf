package cn.sphd.miners.modules.processManagement.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by lyx on 2023/5/9
 * 生产_活动类别表  1.251工序1
 */
@Entity
@Table(name="t_ppm_activity_category")
public class PpmActivityCategory implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;   //ID

    @Column(name="org"   , nullable=true , unique=false)
    private Integer org;   //机构ID

    @Column(name="name"  , length=50 , nullable=true , unique=false)
    private String name;   //名称

    @Column(name="type"  , nullable=true , unique=false)
    private Byte type;   //类型:1-工序,2-子工序,3-操作,4-套餐

    @Column(name="orders"   , nullable=true , unique=false)
    private Integer orders;   //同级排序

    @Column(name="enabled")
    private Boolean enabled; //启用标志:true-启用(默认),false-停用

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;   //备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;   //创建人id

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;  //创建人

    @Column(name="create_time"   , nullable=true , unique=false)
    private Date createTime;   //创建时间

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;   //修改人id

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;   //修改人

    @Column(name="update_time"   , nullable=true , unique=false)
    private Date updateTime;   //修改时间

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private Byte operation;  //操作:1-增,2-删,3-改,4-暂停,5-恢复

    @Column(name="previous_id"  , nullable=true , unique=false)
    private Integer previousId;  //修改前记录ID

    @Column(name="version_no"  , nullable=true , unique=false)
    private Integer versionNo;  //版本号,每次修改+1

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Byte getOperation() {
        return operation;
    }

    public void setOperation(Byte operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
}
