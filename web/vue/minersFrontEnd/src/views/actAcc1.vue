<template>
  <div class="home">
    <PageHeader title="登录"></PageHeader>
    <div class="contentHome">
      <div>
        <div class="line30">
          这个账号有被冻结的数据，所以请确认以下事项：
          <div class="ty-color-blue">
            注：操作结果不可逆，请谨慎操作！
          </div>
        </div>
        <div class="radioC">
          <el-radio v-model="radio1" label="1">被冻结的数据是我的，<br>去回答安全问题以便验证</el-radio>
          <br>
          <br>
          <el-radio v-model="radio1" label="2">被冻结的数据与我无关，<br>因为这个手机号是本人新购买的！</el-radio>
        </div>
        <div class="ty-center" style="padding: 30px 0;">
          <span class="ty-btn ty-btn-green ty-circle-3 ty-btn-big" @click="nextBtn">下一步</span>
        </div>
      </div>
    </div>

  </div>
</template>
<script>
import { unlockAccByActiveCode, getUnlockingToken,  } from "@/api/actAcc";
import PageHeader from '@/components/PageHeader.vue'

export default {
  data() {
    return {
      radio1:''
    }
  },
  components: {
    PageHeader
  },
  mounted() {
    this.getUnlockingTokenFun()
  },
  methods: {
    getUnlockingTokenFun(){
      let localUrl = window.location.href

      let activeCode = localUrl.split('?activeCode=')[1]
      console.log('activeCode=', activeCode)
      getUnlockingToken({ activeCode: activeCode }).then(res1=>{
        let res = res1.data
        let data = res.data
        let success = res.success
        console.log('getUnlockingToken res=', res)
        if(success == 0){
          this.$message.error(res.errorMsg)
          setTimeout(function () {
            console.log('www')
            window.location.replace("about:blank");
          },3000)
        }

      }).catch(err=>{
        console.log('err=', err)
      })
    },
    nextBtn(){
      if(this.radio1 === '1'){
        this.$router.push('actAcc2')
      }else if(this.radio1 === '2'){
        // 新买的
        unlockAccByActiveCode().then(res1=>{
          let res = res1.data
          let success = res.success
          if(success === 1){
            this.$alert('操作成功，请关闭本页面！', '提示', {
              confirmButtonText: '确定',
              type:'error',
              callback: (action) => {
                window.close();
              },
            })

          }else{
            this.$alert(res.errorMsg, '提示', {
              confirmButtonText: '确定',
              type:'error',
              callback: (action) => {
                window.close();
              },
            })

          }
        }).catch(err=>{
          console.log('err=',err)
        })

      }else{
        this.$message.error('请先选择')
      }
    },

  },
}
</script>

<style lang="scss" scoped>
#nav{ background: #fff; }
.home{
  .contentHome{
    padding:20px 20px 55px 20px;
    .line30{ line-height: 30px; font-size: 15px; }
    .radioC{ line-height: 20px; margin-top: 40px; }
  }

}
</style>
