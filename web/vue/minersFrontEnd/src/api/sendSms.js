import {request} from "@/utils/request";

export function createVerificationCode(data) {
    return request({
        url: '/test/createVerificationCode.do',
        data: data ?? {}
    })
}
export function verificationCode(data) {
    return request({
        url: '/test/verificationCode.do',
        data: data ?? {}
    })
}
export function dupAcc(data) {
    return request({
        url: '/test/dupAcc.do',
        data: data ?? {}
    })
}
export function createUnlockVerificationCode(data) {
    return request({
        url: '/test/createUnlockVerificationCode.do',
        data: data ?? {}
    })
}
export function unlockVerificationCode(data) {
    return request({
        url: '/test/unlockVerificationCode.do',
        data: data ?? {}
    })
}
export function unlockGetNewAccId(data) {
    return request({
        url: '/test/unlockGetNewAccId.do',
        data: data ?? {}
    })
}
export function selectOldAcc(data) {
    return request({
        url: '/test/selectOldAcc.do',
        data: data ?? {}
    })
}
export function selectNewAcc(data) {
    return request({
        url: '/test/selectNewAcc.do',
        data: data ?? {}
    })
}
export function selectOldAccByActiveCode(data) {
    return request({
        url: '/test/selectOldAccByActiveCode.do',
        data: data ?? {}
    })
}
export function selectNewAccByActiveCode(data) {
    return request({
        url: '/test/selectNewAccByActiveCode.do',
        data: data ?? {}
    })
}
export function getUnlockingToken(data) {
    return request({
        url: '/auth/getUnlockingToken.do',
        data: data ?? {}
    })
}
export function unlockAcc(data) {
    return request({
        url: '/test/unlockAcc.do',
        data: data ?? {}
    })
}
export function unlockAccByActiveCode(data) {
    return request({
        url: '/test/unlockAccByActiveCode.do',
        data: data ?? {}
    })
}
export function changePassword(data) {
    return request({
        url: '/test/changePassword.do',
        data: data ?? {}
    })
}