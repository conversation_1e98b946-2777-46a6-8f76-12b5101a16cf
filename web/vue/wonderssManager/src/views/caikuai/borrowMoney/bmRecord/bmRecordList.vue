<template>
  <div class="box">
    <div class="head">
      <PageHeader :title="tit" :showClose=true
                  @mycloseFun="handleCustomClose" :showBack=true>
      </PageHeader>
    </div>
    <div class="sticky  ty-fff" v-if="list&&loanDetail">
      <p style="text-align: center;font-size: 18px;padding: 4px 0" class="h35f14 padding-0-15">修改记录</p>
      <div v-if="list.length>0" class="padding-0-15">
        <p>当前资料为第{{ list.length - 1 }}次修改后的结果</p>
        <p>修改人：{{ list[list.length - 1]?.createName }}<span class="padding-0-10">{{ new
        Date(list[list.length - 1]?.createDate).format('yyyy-MM-dd hh:mm:ss')}}</span>
        </p>
      </div>
      <div v-else class="padding-0-15">
        <p>当前资料尚未经修改。</p>
        <p>创建人：{{ loanDetail.createName }} <span class="padding-0-10">{{ new
        Date(loanDetail.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span></p>
      </div>
      <div style="height: 8px; background-color: #eeeeee" v-if="list.length > 0"></div>
    </div>
    <div v-if="list.length>0" class="re">
      <div v-for="(item,index) in list" :key="item.id">
        <p @click="rou(item,index)">
          <span v-if="index>0">第{{ index }}次修改后</span>
          <span v-else>原始信息</span>
        </p>
        <p class="end-text">
          <span v-if="index>0"> 修改人：</span>
          <span v-else> 创建人：</span>
          <span class="padding-0-10">{{ item.createName }}</span>
          <span>{{ new
          Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')}}</span>
        </p>
      </div>
    </div>

  </div>
</template>

<script>
import finance from '@/mixins/finance.js'

export default {
  name: "bmRecordList",
  mixins: [finance],
  computed: {
    id() {
      return this.$route.query.id
    },
    type() {
      return this.$route.query.type
    },
  },
  mounted() {
    this.getLoansDetails()
  },
  data() {
    return {
      tit: "财会",
      loanDetail: [],
      recordList: [],
      list: []
    }
  },
  methods: {
    getLoansDetails() {
      let params = {id: this.id, sourceType: 2}
      this.financeApi.ordLoanDetail(params).then(res1 => {
        this.loanDetail = res1.data.data.loanDetail
        this.list = res1.data.data.borrorModList
      }).catch(err => {
        console.log('err=', err)
        this.$message.error('链接失败，请重试！')
      })
    },
    rou(item, index) {
      item.itemIndex = index
      localStorage.setItem('bmRecordDetail', JSON.stringify(item))
      this.$router.push({
        path: '/bmRecordDetail',
        query: {}
      })

    },
    handleCustomClose() {
      this.$router.push("/caikuai")
    }
  }
}
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";


.head {
  position: fixed;
  width: 100%;
  z-index: 999;
}

.sticky {
  > div {
    padding: 4px 15px;
    line-height: 22px;
  }
}

.box {
  background-color: #ffffff;

  .re {
    position: relative;
    top: 40px;

    > div {
      padding: 6px 15px;
      background-color: #ffffff;
      border-bottom: 1px solid $tyBounceColorGray;
      line-height: 25px;

      > p:first-child {
        > span {
          width: 50%;
          font-size: 16px;
          color: $tyColorBlue;
        }

      }
    }
  }
}
</style>