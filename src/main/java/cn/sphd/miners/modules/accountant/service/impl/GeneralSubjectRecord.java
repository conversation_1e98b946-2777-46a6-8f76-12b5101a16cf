package cn.sphd.miners.modules.accountant.service.impl;

import cn.sphd.miners.modules.accountant.entity.*;
import cn.sphd.miners.modules.accountant.mapper.*;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * created by lht 2022-05-06 10:26
 * 该类用于生成科目操作记录
 * 建账完成后对科目所有操作都要生成操作记录，新增、修改、启用、禁用
 *
 * */
@Service
@Transactional(readOnly=false)
public class GeneralSubjectRecord {

    @Autowired
    TaccountantSubjectJournalMapper subjectJournalMapper;
    @Autowired
    TaccountantSubjectRecordMapper subjectRecordMapper;
    @Autowired
    TaccountantSubjectChangeMapper subjectChangeMapper;
    @Autowired
    TAccountSubjectMapper subjectMapper;
    @Autowired
    BuildAccountMapper buildAccountMapper;

    // 将科目新增记录写到相关的表，
    // journal表是存储科目变动记录的内容，如果某天科目有变动，需向该表写内容。
    // change表是存储机构截止到某天的所有科目，当科目有操作记录产生时需向该表写内容。
    //recordDtoParam    新增record表的参数
    //newSubject        新增的科目
    //childNum          子科目数量
    public void insertSubject(TaccountantSubjectRecord recordDtoParam,TAccountantSubjectEntity newSubject,Integer childNum) {
        //先判断journal表是否有今天的数据，没有的话新增，有的话更新
        Integer org = recordDtoParam.getOrg();
        Date date = new Date();
        TaccountantSubjectJournal journal = subjectJournalMapper.getByCurDate(org);
        if (journal == null) {//没有今天的数据，需要新增
            Integer eid = recordDtoParam.getEstablish();
            Integer userId = recordDtoParam.getCreator();
            String userName = recordDtoParam.getCreateName();
            insertJournal(org,eid,userId,userName,date,true);//新增journal表和change表
        }
        else {
            //journal和change表已经有了今天的数据，新增科目的时候要更新这俩表是数据
            //journal表统计的是机构各级科目的数量，新增科目后要增加新科目所在级别数量和其父科目级别的数量
            int level = recordDtoParam.getLevel();//新科目级别，不可能是一级科目，因为只能新增子科目，所以其父科目levelP = level - 1
            switch (level) {
                case 2:
                    journal.setSubject2(journal.getSubject2() + 1);
                    journal.setSubject1(journal.getSubject1() + 1);
                    break;
                case 3:
                    journal.setSubject3(journal.getSubject3() + 1);
                    journal.setSubject2(journal.getSubject2() + 1);
                    break;
                case 4:
                    journal.setSubject4(journal.getSubject4() + 1);
                    journal.setSubject3(journal.getSubject3() + 1);
                    break;
            }
            subjectJournalMapper.update(journal);
            //更新完journal表还需要更新change表，
            TaccountantSubjectChange change = new TaccountantSubjectChange();
            change.setOrg(org);
            change.setSubject(newSubject.getSubject());
            change.setEstablish(recordDtoParam.getEstablish());
            change.setChangeDate(date);
            change.setIsChanged(true);//isChanged false-建账后，true-变动后
            change.setName(newSubject.getName());
            change.setParent(newSubject.getParent());
            change.setMeasureUnit(newSubject.getMeasureUnit());
            change.setCreator(newSubject.getCreator());
            change.setCreateName(newSubject.getCreateName());
            change.setCreateDate(date);
            change.setCategory(newSubject.getCategory());
            change.setLevel(newSubject.getLevel());
            change.setBalanceDirection(newSubject.getBalanceDirection());
            change.setState(newSubject.getState());
            change.setMaxChildSubjects(0);
            change.setQuantityAssistingAccounting(newSubject.getQuantityAssistingAccounting());
            change.setRelevanceItem(newSubject.getRelevanceItem());
            change.setRelevanceType(newSubject.getRelevanceType());
            subjectChangeMapper.insert(change);
            //更新其父科目，增加其子科目数量
            TaccountantSubjectChange parentChange = new TaccountantSubjectChange();
            parentChange.setOrg(org);
            parentChange.setSubject(newSubject.getParent());
            parentChange.setEstablish(recordDtoParam.getEstablish());
            parentChange.setMaxChildSubjects(childNum);
            parentChange.setIsChanged(true);
            parentChange.setChangeDate(date);
            subjectChangeMapper.updateParent(parentChange);
        }
        //新增record表，所有对科目的操作都需要在record表产生记录
        subjectRecordMapper.insert(recordDtoParam);
    }

    /**
     * 新增journal表数据,某天的科目变动记录
     * 并且需要把所有科目写到change表
     * journal表里的每一条数据都对应着该机构当时的所有科目，所以新增journal表的时候也需要新增change表
     * 统计各级科目数量
     * isChange     true-变动后，false-建账后
     * 如果isChange=false,要写两遍科目到change表，一遍是变动后的，一个是建账后，如果isChange=true不需要写建账后的科目
     * */
    public void insertJournal(Integer org,Integer eid,Integer userId,String userName,Date date,boolean isChange) {
        //统计一级、二级、三级、四级科目数量
        TaccountantSubjectJournal subjectJournalParam = new TaccountantSubjectJournal();
        subjectJournalParam.setEstablish(eid);
        subjectJournalParam.setOrg(org);
        subjectJournalParam.setChangeDate(date);
        subjectJournalParam.setCreator(userId);
        subjectJournalParam.setCreateName(userName);
        subjectJournalParam.setCreateDate(date);
        TAccountantSubjectEntity subjectEntityParam = new TAccountantSubjectEntity();
        subjectEntityParam.setOrg(org);
//        subjectEntityParam.setEstablish(eid);
        subjectEntityParam.setLevel(1);
        int lv1Count = subjectMapper.getSubjectCount(subjectEntityParam);
        subjectEntityParam.setLevel(2);
        int lv2Count = subjectMapper.getSubjectCount(subjectEntityParam);
        subjectEntityParam.setLevel(3);
        int lv3Count = subjectMapper.getSubjectCount(subjectEntityParam);
        subjectEntityParam.setLevel(4);
        int lv4Count = subjectMapper.getSubjectCount(subjectEntityParam);
        subjectJournalParam.setSubject1(lv1Count);
        subjectJournalParam.setSubject2(lv2Count);
        subjectJournalParam.setSubject3(lv3Count);
        subjectJournalParam.setSubject4(lv4Count);
        subjectJournalMapper.insert(subjectJournalParam);

        //新增change表
        TaccountantSubjectChangeDto changeDtoParam = new TaccountantSubjectChangeDto();
        changeDtoParam.setOrg(org);
        changeDtoParam.setIsChanged(true);//isChanged false-建账后，true-变动后
        changeDtoParam.setChangeDate(date);
        changeDtoParam.setEstablish(eid);
        changeDtoParam.setCreator(userId);
        changeDtoParam.setCreateName(userName);
        changeDtoParam.setCreateDate(date);
        subjectChangeMapper.copySubjectToChangeTable(changeDtoParam);//复制subject表内容到change表
        if (!isChange) {//如果isChange=false需要写两遍科目，一遍是变动后的，一遍是建账后的
            changeDtoParam.setIsChanged(false);
            subjectChangeMapper.copySubjectToChangeTable(changeDtoParam);//写建账后的科目
        }
    }


    /*
    * 修改科目的时候需要产生两条record记录，修改前和修改后
    * 和新增逻辑差不多，先判断journal表是否有数据，没有新增，然后再新增一遍所有科目在change表
    * 如果journal表和change表已有数据，直接该change表对应的科目信息，journal表不用动，因为修改科目不会引起科目数量变化
    * s     修改后的科目信息
    *
    * */
    public void updateSubject(TAccountantSubjectEntity s) {
        Integer org = s.getOrg();
        Date date = new Date();
        TaccountantSubjectJournal journal = subjectJournalMapper.getByCurDate(org);
        Integer eid = getEstablishByOrg(org);//建账完成后建账ID一定存在
        if (journal == null) {//没有今天的数据，需要新增
            Integer userId = s.getUpdator();
            String userName = s.getUpdateName();
            insertJournal(org,eid,userId,userName,date,true);//新增journal表和change表

        }
        else {
            //journal和change表已经有了今天的数据，修改科目的时候要修改change表的数据。journal表的数据不会变
            TaccountantSubjectChange change = new TaccountantSubjectChange();
            change.setOrg(org);
            change.setSubject(s.getSubject());
            change.setChangeDate(date);
            change.setIsChanged(true);//isChanged false-建账后，true-变动后
            change.setName(s.getName());
            change.setMeasureUnit(s.getMeasureUnit());
            change.setQuantityAssistingAccounting(s.getQuantityAssistingAccounting());
            subjectChangeMapper.updateSubjectInfo(change);
        }
        //写record，两条数据，一条修改前的一条修改后的
        TaccountantSubjectRecord recordDtoParam = new TaccountantSubjectRecord();
        recordDtoParam.setOrg(org);
        recordDtoParam.setState(0);//1-修改后，0-修改前
        recordDtoParam.setEstablish(eid);
        recordDtoParam.setOperation("3");//操作:1-增,2-删,3-改,4-启用,5-停用,6-建账,7-重新建账
        recordDtoParam.setCreateDate(date);
        recordDtoParam.setCreator(s.getUpdator());
        recordDtoParam.setCreateName(s.getUpdateName());
        recordDtoParam.setSubject(s.getSubject());
        recordDtoParam.setName(s.getGqName());
        recordDtoParam.setParent(s.getParent());
        recordDtoParam.setLevel(s.getLevel());
        recordDtoParam.setBalanceDirection(s.getBalanceDirection());
        recordDtoParam.setCategory(s.getCategory());
        recordDtoParam.setMeasureUnit(s.getMeasureUnit());
        recordDtoParam.setQuantityAssistingAccounting(s.getQuantityAssistingAccounting());
        subjectRecordMapper.insert(recordDtoParam);//修改前
        recordDtoParam.setState(1);//修改后
        recordDtoParam.setPreviousId(recordDtoParam.getId());
        recordDtoParam.setId(null);
        recordDtoParam.setName(s.getName());
        subjectRecordMapper.insert(recordDtoParam);
    }

    /*
     * 设置科目启禁用后需要在record表生成操作记录
     * 先判断journal表是否有数据，没有新增，然后再新增一遍所有科目在change表
     * 如果journal表和change表已有数据，重新计算journal表的数据，更新change表的科目，生成record表记录
     * subject      当前要修改的科目编号
     * state        1-设置为启用，0-设置为禁用
     * */
    public void subjectStateRecord(String subject, Integer org, String state, User user) {
        Date date = new Date();
        TaccountantSubjectJournal journal = subjectJournalMapper.getByCurDate(org);
        Integer eid = getEstablishByOrg(org);//建账完成后建账ID一定存在
        Integer userId = user.getUserID();
        String userName = user.getUserName();

        if (journal == null) {//没有今天的数据，需要新增
            insertJournal(org,eid,userId,userName,date,true);//新增journal表和change表
        }
        else {
            //journal和change表已经有了今天的数据，修改change表的数据,重新计算journal表的数据
//            String subject = subjects.get(0);//当前要修改的科目编号
            //修改change表的数据，把当前科目及其子科目都修改成state
            TaccountantSubjectChange change = new TaccountantSubjectChange();
            change.setOrg(org);
            change.setState(Byte.parseByte(state));
            change.setSubject(subject);
            change.setChangeDate(date);
            change.setIsChanged(true);//isChanged false-建账后，true-变动后
            subjectChangeMapper.updateState(change);
            //重新计算journal表的数据
            TAccountantSubjectEntity subjectEntityParam = new TAccountantSubjectEntity();
            subjectEntityParam.setOrg(org);
            subjectEntityParam.setLevel(1);
            int lv1Count = subjectMapper.getSubjectCount(subjectEntityParam);
            subjectEntityParam.setLevel(2);
            int lv2Count = subjectMapper.getSubjectCount(subjectEntityParam);
            subjectEntityParam.setLevel(3);
            int lv3Count = subjectMapper.getSubjectCount(subjectEntityParam);
            subjectEntityParam.setLevel(4);
            int lv4Count = subjectMapper.getSubjectCount(subjectEntityParam);
            journal.setSubject1(lv1Count);
            journal.setSubject2(lv2Count);
            journal.setSubject3(lv3Count);
            journal.setSubject4(lv4Count);
            subjectJournalMapper.update(journal);
        }
        //循环生成record科目操作记录数据
        //查询所有需要更新的科目，包含自身及其子科目
        TAccountantSubjectEntity param = new TAccountantSubjectEntity();
        param.setOrg(org);
        param.setSubject(subject);
        List<TAccountantSubjectEntity> list = subjectMapper.getSubjectAndChildren(param);
        for (TAccountantSubjectEntity subjectEntity : list) {
            TaccountantSubjectRecord record = new TaccountantSubjectRecord();
            record.setOrg(org);
            record.setState(1);//1-修改后，0-修改前
            record.setEstablish(eid);
            record.setLevel(subjectEntity.getLevel());
            if ("1".equals(state)) {
                record.setOperation("4");//操作:1-增,2-删,3-改,4-启用,5-停用,6-建账,7-重新建账
            }
            else if ("0".equals(state)) {
                record.setOperation("5");//操作:1-增,2-删,3-改,4-启用,5-停用,6-建账,7-重新建账
            }
            record.setCreateDate(date);
            record.setCreator(userId);
            record.setCreateName(userName);
            record.setSubject(subjectEntity.getSubject());
            record.setName(subjectEntity.getName());
            subjectRecordMapper.insert(record);
        }

    }

    /*
    * 检查机构建账状态
    * true-已完成建账
    * false-未完成建账
    * */
    public boolean checkBuildState(Integer org) {
        TAccountantSetting link = new TAccountantSetting();
        link.setOrg(org);
        link.setKey_("operate_state");
        link = buildAccountMapper.getSettingByKey(link);
        if (link != null) {
            String buildState = link.getValue_();
            if ("4".equals(buildState)) {
                return true;
            }
        }
        return false;
    }

    /*
    * 获取机构最新的建账ID
    * */
    public Integer getEstablishByOrg(Integer org) {
        TAccountantSetting link = new TAccountantSetting();
        link.setOrg(org);
        link.setKey_("establish_id");
        link = buildAccountMapper.getSettingByKey(link);
        if (link != null) {
            String val = link.getValue_();
            return Integer.parseInt(val);
        }
        return null;
    }

}
