package cn.sphd.miners.modules.finance.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;

/**
 * Created by Administrator on 2019/5/5.
 */
@Entity
@Table(name = "t_finance_reimburse_bill_attachment")
public class FinanceReimburseBillAttachment implements Serializable{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "reimburse_bill", insertable=false, updatable=false)
    private Integer reimburseBillId;

    @Column(name="title"  , length=100 , nullable=true , unique=false)
    private String title;

    @Column(name="description"  , length=255 , nullable=true , unique=false)
    private String description;

    @Column(name="path"  , length=255 , nullable=true , unique=false)
    private String path;

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;

    @JsonIgnore @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.LAZY )
    @JoinColumn(name="reimburse_bill", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private FinanceReimburseBill financeReimburseBill;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getReimburseBillId() {
        return reimburseBillId;
    }

    public void setReimburseBillId(Integer reimburseBillId) {
        this.reimburseBillId = reimburseBillId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public FinanceReimburseBill getFinanceReimburseBill() {
        return financeReimburseBill;
    }

    public void setFinanceReimburseBill(FinanceReimburseBill financeReimburseBill) {
        this.financeReimburseBill = financeReimburseBill;
    }
}
