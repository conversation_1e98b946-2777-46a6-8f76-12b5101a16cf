package cn.sphd.miners.modules.commodity.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.commodity.entity.PdBase;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * Created by Administrator on 2016/10/12.
 */
public interface PdBaseDao extends IBaseDao<PdBase,Serializable>{

    //单重 重量单位
    void updateUnitAndProcess(Integer id, BigDecimal netWeight, String weightUnit, String process);

    //单重 重量单位，构成
    void updateUnitAndComposition(Integer id,BigDecimal netWeight,String weightUnit,String composition,String stage);

    //获取直接组成产品或零组件 1产品 2零组件
    Long directComposition(Integer oid,Integer pdBaseId,String type);
    //间接参与组装的产品的种数
    Long indirectPartProduct(Integer oid,Integer pdBaseId);
    //曾经直接参与组装的产品,组件的种数 1产品 2零组件
    Long formerdirectPartComposition(Integer oid,Integer pdBaseId,String type);
    //曾经间接参与组装的产品的种数
    Long formerIndirectPartProduct(Integer oid,Integer pdBaseId);
}
