<template>
  <div id="payList">
    <TY_NavTop title="回款入账" :isBackHome="isBackHome" isSearch="true" @toggleList="screen"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="tipPane">请确认以下{{inAccountInList.length}}笔款项是否已收到。</div>
      <div class="ui-cells">
        <a class="ui-cell" v-for="(item, index) in inAccountInList" v-on:click="jumpTo(item.collectId)" :key="index">
          <div class="backApplyLists">
            <p class="company" v-text="item.name"></p>
            <p class="listCon"> 本次回款 {{item.method | incomeType}} {{item.amount}}元</p>
            <p class="listCon">{{item.createName}} {{item.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</p>
          </div>
        </a>
      </div>
    </div>
  </div>
</template>
<script>
import { incomeType, formatDate } from '../../../js/common'
export default {
  name: 'returnInAccount',
  filters: {
    incomeType,
    formatDate
  },
  data () {
    return {
      loading: true,
      listenersUid: [],
      inAccountInList: []
    }
  },
  created () {
    let _this = this
    _this.listenersUid = [
      _this.sphdSocket.subscribe('collectPaymentList', function (data) {
        let resData = JSON.parse(data)
        _this.inAccountInList = resData.collectApplications
        _this.loading = false
      }),
      _this.sphdSocket.subscribe('collectPaymentList', function (data) {
        let resData = JSON.parse(data)
        let update = resData.personnelReimburse
        if (resData.operate > 0) {
          this.inAccountInList.push(update)
        } else if (resData.operate < 0) {
          _this.inAccountInList.forEach(function (item, index) {
            let deletId = update.id
            if (deletId === item.collectId) {
              _this.inAccountInList.splice(index, 1)
            }
          })
        }
      }, function () {
        console.log('Socket check Error:')
      }, 'user')
    ]
    _this.sphdSocket.send('collectPaymentList', {
      'oid': this.sphdSocket.user.oid,
      'session': this.sphdSocket.sessionid
    })
  },
  destroyed: function () {
    let _this = this
    _this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    jumpTo: function (id) {
      this.$router.push({
        path: `/payApproveDetail/${id}`
      })
    },
    screen: function () {
      let type = 2
      this.$router.push({
        path: `/payApplyScreen/${type}`
      })
    }
  }
}
</script>
