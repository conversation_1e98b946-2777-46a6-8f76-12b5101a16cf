package cn.sphd.miners.modules.material.service.impl;

import cn.sphd.miners.modules.material.dao.MtMaterialStockDao;
import cn.sphd.miners.modules.material.entity.MtMaterialStock;
import cn.sphd.miners.modules.material.service.MtMaterialStockService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by Administrator on 2017/8/2.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class MtMaterialStockServiceImpl implements MtMaterialStockService {
    @Autowired
    MtMaterialStockDao mtMaterialStockDao;

    @Override
    public void addMtMaterialStock(MtMaterialStock mtMaterialStock) {
        mtMaterialStockDao.save(mtMaterialStock);
    }
}
