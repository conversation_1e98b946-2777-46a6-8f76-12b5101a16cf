package cn.sphd.miners.modules.material.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.material.entity.MtPackagingStructure;
import cn.sphd.miners.modules.material.entity.MtPackagingStructureHistory;

import java.io.Serializable;

public interface MtPackagingStructureHistoryDao extends IBaseDao<MtPackagingStructureHistory, Serializable> {

    //添加历史记录
    MtPackagingStructureHistory insert(MtPackagingStructure pdPackagingStructure, Long infoHistoryId);
}
