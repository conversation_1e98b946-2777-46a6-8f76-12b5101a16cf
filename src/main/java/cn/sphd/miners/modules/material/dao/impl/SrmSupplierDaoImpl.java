package cn.sphd.miners.modules.material.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.material.dao.SrmSupplierDao;
import cn.sphd.miners.modules.material.entity.SrmSupplier;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2016/9/19.
 */
@Repository
public class SrmSupplierDaoImpl extends BaseDao<SrmSupplier,Serializable> implements SrmSupplierDao {
    @Override
    public List<SrmSupplier> getAll() {
        String hql = "from SrmSupplier";
        return getListByHQL(hql,null);
    }

    @Override
    public List<SrmSupplier> getSuppliersByDate(Date date, Integer oid) {
        String hql = "from SrmSupplier o where o.createDate>?0 and ifnull(o.type,0)=1 and o.org=?1 and ifnull(o.operation,0) not in (2,'B','D')";
        return getListByHQL(hql,date,oid);
    }

    @Override
    public List<SrmSupplier>  getChangeSuppliers(Date date, Integer oid) {
        String hql = " from SrmSupplier o where o.updateDate>?0 and o.org=?1 and operation not in (2,'B','D')";
        return getListByHQL(hql,date,oid);
    }
}