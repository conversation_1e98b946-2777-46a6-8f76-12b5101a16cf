<template>
  <div id="ticketAuthSearchList" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="增票认证"></TY_NavTop>
    <div class="container">
      <div class="ui-message">
        <div class="ttl11">
          <p>您要查询：</p>
          <p>{{ beginTimeShow | formatDay('YYYY年MM月DD日')}} - {{ endTimeShow | formatDay('YYYY年MM月DD日')}}期间</p>
          <p>
            {{ paramQuery.approvalStatus | formatApprovalStatus }}</p>
          <p></p>
        </div>
        <div class="ttl1">符合查询条件的数据共如下{{list.length}}条。</div>
      </div>
      <div>
        <div class="p1">
          <table>
            <tr>
              <td>发票号码</td>
              <td>价税合计</td>
              <td>查看</td>
            </tr>
            <tr v-for="(item, index) in list" v-bind:key="index">
              <td>{{item.billNo}}</td>
              <td>{{item.itemAmount | toFix2}}</td>
              <td><span class="linkBtn" @click="getAuthBillItemDetail(item)">票据信息</span></td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less">
  #ticketAuthSearchList{
    .ttl1{ background:#DEEBF6; padding:10px;     }
    .ttl11{ background: #edf3c6; padding:10px;     }
    .container>div>a{  background: #fff; }
    .container>div>a:nth-child(odd){ background:#f3f3f3;  }
    .orderItem{ width: 100%; font-size:1em;   }
    .orderItem:nth-child(even){ background: #eee; }
    .orderItem>div{ display: flex; }
    .orderItem>div>div{ flex:1; }
    .orderItem>div>div:nth-child(2){ text-align: right; margin-left: -30px;  font-size: 0.7em; }
    .queryCondition{
      background: #fffaeb;
      font-size: 12px;
      padding: 8px;
      margin-bottom: 5px;
    }
    .p1{ background:#fff;margin-bottom:10px; padding:15px;  }
    .linkBtn{ color: #0b9df9; cursor:default;  }
    .linkBtn:hover{ text-decoration:underline;   }
    table{
      width:100%;
      td{text-align:center; height:30px; }
      tr:nth-child(even)>td{ background:#eaeaea;  }
    }
  }

</style>
<script>
export default {
  name: 'ticketAuthSearchList',
  data () {
    return {
      beginTimeShow: '',
      loading: true,
      endTimeShow: '',
      list: [],
      num: true,
      paramQuery: {},
      queryData: {
        data: [],
        beginDate: '',
        endDate: ''
      }
    }
  },
  created: function () {
    let paramQuery = JSON.parse(localStorage.getItem('query'))
    this.paramQuery = paramQuery
    let that = this
    let url = '../../../reimburseWindow/getAuthenticationQuery.do'
    let data = {
      approvalStatus: paramQuery.approvalStatus,
      beginDate: paramQuery.beginDate,
      endDate: paramQuery.endDate,
      oid: this.sphdSocket.org.id,
      type: paramQuery.type
    }
    console.log('url：', url)
    console.log('打印传参：')
    console.log(data)
    that.axios.post(url, data).then((response) => {
      that.loading = false
      let data = response.data.data
      console.log('返回值')
      console.log(data)
      if (data) {
        that.list = data.financeReimburseBills || []
        that.beginTimeShow = data.beginDate
        that.endTimeShow = data.endDate
      } else {
        that.$kiko_message('加载失败')
      }
    }).catch(function (res) {
      console.log(res)
    })
  },
  filters: {
    formatApprovalStatus: function (ApprovalStatus) {
      let str = ''
      switch (Number(ApprovalStatus)) {
        case 1:
          str = '认证已通过的发票'
          break
        case 2:
          str = '认证未通过、不用来抵扣，只用作报销的发票'
          break
        case 3:
          str = '认证未通过、换了新发票的发票'
          break
        default:
          str = '全部的发票'
      }
      return str
    },
    toFix2: function (val) {
      return Number(val).toFixed(2)
    }
  },
  methods: {
    jump: function (id) {
      window.parent.floatToPage('../../../purchaseOrderApproval/purchasePage.do', { 'id': id, 't': 2 })
    },
    getAuthBillItemDetail: function (item) {
      let params = {
        'name': 'auth',
        'id': item.id,
        'createName': '',
        'reimburseId': '',
        'billCat': item.billCat,
        'feeCat': '',
        'secondFeeCat': '',
        'billCatName': '增值税专用发票',
        'feeCatName': '',
        'secondFeeCatName': '',
        'totalAmount': item.itemAmount,
        'num': '',
        'session': this.sphdSocket.sessionid
      }
      this.$store.dispatch('setBillParam', params)
      this.$router.push({
        path: 'bill',
        name: 'bill'
      })
    }
  }
}
</script>
