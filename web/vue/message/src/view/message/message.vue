<template>
  <div id="dailyAffairs" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="我的消息" v-bind:isReBack="disappearShow" @reback="reback">
      <div class="nav_btn" @click="edit">
        <img src="../../images/edit.png" alt="编辑" v-show="!disappearShow">
        <img src="../../images/delete.png" alt="删除" v-show="disappearShow">
      </div>
    </TY_NavTop>
    <div class="container">
      <div v-show="!disappearShow">
        <div style="padding: 0 4px;">
          <div class="tip tip-success">您查看后的消息，系统还将保存72小时！ <span class="disBtn" @click="disappearingMsg">即将消失的消息</span></div>
        </div>
        <div class="ui-cells" style="height: 462px">
          <a class="ui-cell" v-on:click="jump(item.id, item.uri)" v-for="(item, index) in userSuspendMsgs" v-bind:key="index">
            <div class="ui-cell__hd">{{item.createName}}</div>
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div class="ui-row">
                  {{item.content}}
                </div>
                <div class="approveTime">
                  <small class="timeContent">{{item.memo | timeSplit2}}</small><small class="timeTitle">{{item.memo | timeSplit1}}</small>
                </div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </div>
      <div v-show="disappearShow">
        <div style="padding: 0 4px;">
          <div class="tip tip-success">下列消息即将消失。</div>
        </div>
        <div class="ui-cells" style="height: 462px">
          <a class="ui-cell" v-on:click="jump(item.id, item.uri, true)" v-for="(item, index) in disappearList" v-bind:key="index">
            <div class="ui-cell__hd">{{item.createName}}</div>
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div class="ui-row">
                  {{item.content}}
                </div>
                <div class="approveTime">
                  <small class="timeContent">{{item.memo | timeSplit2}}</small>
                  <small class="timeTitle">{{item.memo | timeSplit1}}</small>
                  <small class="blue" v-show="disappearShow">{{item.disappear}}</small>
                  <div style="clear: both; "></div>
                </div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </div>
    </div>
    <TY_Edit :res="disappearShow?disappearList:userSuspendMsgs" v-bind:show.sync="bookVisible" :isDisappear="disappearShow" @updateList="updateList"></TY_Edit>
  </div>
</template>

<style scoped lang="less">
  .disBtn{
    float: right;
    flex-grow: 1;
    text-align: right;
    color: #4797f1;
    cursor: pointer;
    &:hover{
      text-decoration: underline;
    }
  }
  .approveTime{
    /*display: flex;*/
    /*flex-direction: row-reverse;*/
    display: block;
    font-size: 12px;
    small{
      float:right;
      /*flex-shrink: 0;*/
      /*flex-grow: 0;*/
    }
  }
  .approveTime .timeTitle{
    margin-right:30px ;
    width: 50px;
  }
  .approveTime .timeContent{
    margin-right: 12px;
  }
  .approveTime small.blue{
    color: #4797f1;
    flex-grow: 1;
  }
</style>

<script>
export default {
  name: 'myMessage',
  filters: {
    'timeSplit1': function (value) {
      let len = value.split(' ').length
      if (len > 2) {
        return value.substring(0, value.indexOf(' '))
      } else {
        return ''
      }
    },
    'timeSplit2': function (value) {
      let len = value.split(' ').length
      if (len > 2) {
        return value.substring(value.indexOf(' '), value.length)
      } else {
        return value
      }
    }
  },
  data () {
    return {
      loading: true,
      userSuspendMsgs: {},
      isBackHome: false,
      listenersUid: [],
      disappearList: [],
      bookVisible: false,
      disappearShow: this.$store.state.messageDisappear
    }
  },
  created: function () {
    let that = this
    this.axios.post('../../../daily/myMessages.do')
      .then(function (response) {
        that.loading = false
        console.log(response)
        let data = response.data.data
        that.userSuspendMsgs = data.userSuspendMsgs
      })
      .catch(function (error) {
        console.log(error)
      })
    this.listenersUid = [
      this.sphdSocket.subscribe('message', function (data) {
        console.log('messageInfo:' + data)
        let getData = JSON.parse(data)
        let messageInfo = getData.messageInfo
        let operate = getData.operate
        let userSuspendMsgs =  that.userSuspendMsgs
        if (operate > 0) {
          that.userSuspendMsgs.unshift(messageInfo)
        } else {
          let newArray = userSuspendMsgs.filter((item) => !messageInfo.some((ele) => ele.id === item.id))
          that.userSuspendMsgs = newArray
        }
        that.loading = false
      }, function () {
        this.loading = false
        console.log('message user Socket Error:')
      }, 'user')
    ]
    // let userId = auth.getUserID()
    // let session = auth.getSessionid()
    // this.sphdSocket.send('myMessages', { 'userId': userId, 'session': session })
    if (this.$store.state.messageDisappear) {
      this.disappearingMsg()
    }
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
      console.log(item)
    })
  },
  methods: {
    jump: function (messageId, path, isDisappear) {
      console.log('messageId=', messageId);
      console.log('path=', path);
      let params = {
        'userId': this.sphdSocket.user.userID, 'messageId': messageId
      }

      if (path) {
        console.log('path', path)
        let pathList = path.split('/') // path: eg: '/financeUpdateDetail/7014'
        let code = pathList[1]
        let id = pathList[2]
        console.log('id是多少',id);
        console.log('code', code);
        if (code !== 'scheduleDetail') {
          this.sphdSocket.send('updateUserSuspendMsg', params)
        }
        // 不需要url挂传值的
        switch (code) {
          case 'invoiceSignFor':
            break
          case 'purchaseOrderDetail':
            window.parent.floatToPage('../../../purchaseOrderApproval/purchasePage.do', { 'id': id, 't': 3 })
            break
          case 'complaintInfo':
            window.parent.floatToPage('../../../complaint/complaintInfo.do', { 'id': id, 't': 10 })
            break
          case 'invoiceEndInfo':
            this.$router.push({
              name: `invoiceEndInfoForMsg`,
              params: {
                messageId: messageId
              }
            })
            break
          case 'financeUpdateDetail':
            parent.location.href = '../../../update/toUpdatePage.do?id=' + id + '&mid=' + messageId
            break
          case 'ItemDetail':
            this.$router.push({
              path: '/approvalSettingsSearchDetails/' + id + '/2'
            })
            break
          case 'purchaseInvoiceDetails':
            this.$router.push({
              path: path
            })
            break
          case 'advancePaymentDetail':
            this.$router.push({
              path:`/purchaseInvoiceYfk/${id}`
            })
            break;
          case 'trailHandle':
            parent.location.href = '../../../special/toTryApplyInfo.do?type=11&id=' + id + '&mid=' + messageId
            break
          case 'editOrgPopedomApply':
            parent.location.href = '../../../special/toModuleChangeHandle.do?type=0&id=' + id + '&mid=' + messageId
            break
          case 'addService':
            parent.location.href = '../../../special/toTryApplyInfo.do?type=31&id=' + id + '&mid=' + messageId
            break
          case 'addServiceChange':
            parent.location.href = '../../../special/toTryApplyInfo.do?type=41&id=' + id + '&mid=' + messageId
            break
          case 'orgOutOfServiceApply':
            parent.location.href = '../../../special/toTryApplyInfo.do?type=51&id=' + id + '&mid=' + messageId
            break
          case 'orgRestoreServiceApply':
            parent.location.href = '../../../special/toTryApplyInfo.do?type=61&id=' + id + '&mid=' + messageId
            break
          case 'materialDetail':
            this.$store.dispatch('setMaterialParam', {
              user: 'msg'
            })
            this.$router.push({
              path: `${path}`,
              query: {
                messageId: messageId
              }
            })
            break
          case 'borrowFileDetail':
            this.$router.push({
              path: `${path}/5`
            })
            break
          case 'attendanceChangeDetail':
            this.$router.push({
              path: `${path}/4/2`
            })
            break
          case 'myAttendanceChangeDetail':
            this.$router.push({
              path: `${path}/4/1`
            })
            break
          case 'applyDiscussDetail':
            this.$router.push({
              path: '/discussDetail/' + id + '/query2'
            })
            break
          case 'templateFoundApply':
          case 'templateFoundApproval':
          case 'templateEditApply':
          case 'templateEditApproval':
          case 'productFoundApply':
          case 'productFoundApproval':
          case 'productEditApply':
          case 'productEditApproval':
          case 'productRecoveryApply':
          case 'productRecoveryApproval':
            this.$router.push({
              path: `/templateOrProjectDetails/${code}/${id}/1`
            })
            break
          case 'contentDetailApply':
            this.$router.push({
              path: '/contentDetail/' + id + '/query1'
            })
              break;
          case 'contentDetailUpdate':
            this.$router.push({
              path: '/contentDetail/' + id + '/query2'
            })
            break;
          case 'overtimeAssignDetail':
            this.$router.push({
              path: '/overtimeAssignDetail/' + id + '/message'
            })
            break
          default:
            console.log('跳转其他详情');

            this.$router.push({
              path: `${path}`,
              query: {
                messageId: messageId
              }
            })
        }
      } else {
        if (!isDisappear) {
          this.sphdSocket.send('updateUserSuspendMsg', params)
        }
      }
    },
    edit: function () {
      this.bookVisible = true
    },
    disappearingMsg: function () {
      let that = this
      this.loading = true
      this.axios.post('../../../daily/willDisappearMessages.do', {
        userId: that.sphdSocket.user.userID
      })
        .then(function (response) {
          that.loading = false
          that.disappearList = response.data.data;

        })
        .catch(function (error) {
          console.log(error)
        })
      this.disappearShow = true
      this.$store.dispatch('setMsgDisappear', true)
    },
    reback: function () {
      this.disappearShow = false
      this.$store.dispatch('setMsgDisappear', false)
    },
    updateList: function (data) {
      let res = data
      console.log('updateList', data)
      console.log('userSuspendMsgs', this.userSuspendMsgs)
      for (let i in res) {
        if (!this.disappearShow) {
          let _index = this.userSuspendMsgs.findIndex(value => value.id === res[i])
          if (_index !== -1) {
            this.userSuspendMsgs.splice(_index, 1)
          }
        } else {
          let _index = this.disappearList.findIndex(value => value.id === res[i])
          if (_index !== -1) {
            this.disappearList.splice(_index, 1)
          }
        }
      }
    }
  }
}
</script>
