package cn.sphd.miners.modules.thirdPlatform.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.thirdPlatform.dao.TpRegInfoDao;
import cn.sphd.miners.modules.thirdPlatform.entity.TpRegInfo;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class TpRegInfoDaoImpl extends BaseDao<TpRegInfo, Serializable> implements TpRegInfoDao {
}
