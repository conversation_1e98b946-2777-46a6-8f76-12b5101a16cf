package cn.sphd.miners.modules.accountant.mapper;


import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.accountant.entity.SubjectTemp;
import cn.sphd.miners.modules.accountant.entity.TAccountantVoucherSubjectHistory;
import org.springframework.stereotype.Component;

import java.util.List;
@Component
public interface SubjectHistoryMapper extends BaseMapper<TAccountantVoucherSubjectHistory> {

    /*只更新历史科目表的科目编号*/
    int updateHistorySubjectNo(TAccountantVoucherSubjectHistory param);

    /*得到凭证所对应的科目代码、名称和金额,从凭证历史表里*/
    List<SubjectTemp> getSubjectHistoryInfo(QueryData qd);

    /*得到某凭证的科目金额*/
    List<TAccountantVoucherSubjectHistory> getSubjectByDirection(TAccountantVoucherSubjectHistory param);

    int deleteHistorySubject(TAccountantVoucherSubjectHistory param);

    List<TAccountantVoucherSubjectHistory> getSubjectByVoucher(TAccountantVoucherSubjectHistory param);


    void deleteByOrg(int org);
}