package cn.sphd.miners.modules.commodity.entity;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

@Entity
@Table(name = "t_pd_composition_material_history")
public class PdCompositionMaterialHistory {
    private Integer id;
    private Integer org;
    private Integer compositionMaterial;
    private Integer product;
    private String innerSn;
    private Integer material;
    private Integer materialHistory;
    private String materialMemo;
    private Byte enalbed;
    private Integer amount;
    private BigDecimal percent;
    private String memo;
    private Integer creator;
    private String createName;
    private Date createDate;
    private Integer updator;
    private String updateName;
    private Date updateDate;
    private String operation;
    private Integer previousId;
    private Integer versionNo;
    private Integer formula;
    private String ingredients;


    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Basic
    @Column(name = "org")
    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    @Basic
    @Column(name = "composition_material")
    public Integer getCompositionMaterial() {
        return compositionMaterial;
    }

    public void setCompositionMaterial(Integer compositionMaterial) {
        this.compositionMaterial = compositionMaterial;
    }

    @Basic
    @Column(name = "product")
    public Integer getProduct() {
        return product;
    }

    public void setProduct(Integer product) {
        this.product = product;
    }

    @Basic
    @Column(name = "inner_sn")
    public String getInnerSn() {
        return innerSn;
    }

    public void setInnerSn(String innerSn) {
        this.innerSn = innerSn;
    }

    @Basic
    @Column(name = "material")
    public Integer getMaterial() {
        return material;
    }

    public void setMaterial(Integer material) {
        this.material = material;
    }

    @Basic
    @Column(name = "material_history")
    public Integer getMaterialHistory() {
        return materialHistory;
    }

    public void setMaterialHistory(Integer materialHistory) {
        this.materialHistory = materialHistory;
    }

    @Basic
    @Column(name = "material_memo")
    public String getMaterialMemo() {
        return materialMemo;
    }

    public void setMaterialMemo(String materialMemo) {
        this.materialMemo = materialMemo;
    }

    @Basic
    @Column(name = "enalbed")
    public Byte getEnalbed() {
        return enalbed;
    }

    public void setEnalbed(Byte enalbed) {
        this.enalbed = enalbed;
    }

    @Basic
    @Column(name = "amount")
    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    @Basic
    @Column(name = "percent")
    public BigDecimal getPercent() {
        return percent;
    }

    public void setPercent(BigDecimal percent) {
        this.percent = percent;
    }

    @Basic
    @Column(name = "memo")
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Basic
    @Column(name = "creator")
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    @Basic
    @Column(name = "create_name")
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Basic
    @Column(name = "create_date")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Basic
    @Column(name = "updator")
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    @Basic
    @Column(name = "update_name")
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Basic
    @Column(name = "update_date")
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Basic
    @Column(name = "operation")
    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    @Basic
    @Column(name = "previous_id")
    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    @Basic
    @Column(name = "version_no")
    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    @Basic
    @Column(name = "formula")
    public Integer getFormula() {
        return formula;
    }

    public void setFormula(Integer formula) {
        this.formula = formula;
    }

    @Basic
    @Column(name = "ingredients")
    public String getIngredients() {
        return ingredients;
    }

    public void setIngredients(String ingredients) {
        this.ingredients = ingredients;
    }
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PdCompositionMaterialHistory that = (PdCompositionMaterialHistory) o;
        return id == that.id &&
                Objects.equals(org, that.org) &&
                Objects.equals(compositionMaterial, that.compositionMaterial) &&
                Objects.equals(product, that.product) &&
                Objects.equals(innerSn, that.innerSn) &&
                Objects.equals(material, that.material) &&
                Objects.equals(materialHistory, that.materialHistory) &&
                Objects.equals(materialMemo, that.materialMemo) &&
                Objects.equals(enalbed, that.enalbed) &&
                Objects.equals(amount, that.amount) &&
                Objects.equals(percent, that.percent) &&
                Objects.equals(memo, that.memo) &&
                Objects.equals(creator, that.creator) &&
                Objects.equals(createName, that.createName) &&
                Objects.equals(createDate, that.createDate) &&
                Objects.equals(updator, that.updator) &&
                Objects.equals(updateName, that.updateName) &&
                Objects.equals(updateDate, that.updateDate) &&
                Objects.equals(operation, that.operation) &&
                Objects.equals(previousId, that.previousId) &&
                Objects.equals(versionNo, that.versionNo);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, org, compositionMaterial, product, innerSn, material, materialHistory, materialMemo, enalbed, amount, percent, memo, creator, createName, createDate, updator, updateName, updateDate, operation, previousId, versionNo);
    }
}
