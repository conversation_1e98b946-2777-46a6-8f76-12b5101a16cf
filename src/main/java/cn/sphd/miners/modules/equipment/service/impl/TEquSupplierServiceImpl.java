package cn.sphd.miners.modules.equipment.service.impl;

import java.util.List;

import cn.sphd.miners.modules.equipment.entity.TEquSupplier;
import cn.sphd.miners.modules.equipment.mapper.TEquSupplierMapper;
import cn.sphd.miners.modules.equipment.service.ITEquSupplierService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 装备管理_供应商名录
 * 20230209 1.247装备器具新增Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Service
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class TEquSupplierServiceImpl implements ITEquSupplierService {
    @Autowired
    private TEquSupplierMapper tEquSupplierMapper;

    /**
     * 查询装备管理_供应商名录
     * 20230209 1.247装备器具新增
     *
     * @param id 装备管理_供应商名录
     *           20230209 1.247装备器具新增主键
     * @return 装备管理_供应商名录
     * 20230209 1.247装备器具新增
     */
    @Override
    public TEquSupplier selectTEquSupplierById(Long id) {
        return tEquSupplierMapper.selectTEquSupplierById(id);
    }

    /**
     * 查询装备管理_供应商名录
     * 20230209 1.247装备器具新增列表
     *
     * @param tEquSupplier 装备管理_供应商名录
     *                     20230209 1.247装备器具新增
     * @return 装备管理_供应商名录
     * 20230209 1.247装备器具新增
     */
    @Override
    public List<TEquSupplier> selectTEquSupplierList(TEquSupplier tEquSupplier) {
        return tEquSupplierMapper.selectTEquSupplierList(tEquSupplier);
    }

    /**
     * 新增装备管理_供应商名录
     * 20230209 1.247装备器具新增
     *
     * @param tEquSupplier 装备管理_供应商名录
     *                     20230209 1.247装备器具新增
     * @return 结果
     */
    @Override
    public int insertTEquSupplier(TEquSupplier tEquSupplier) {
        TEquSupplier s = new TEquSupplier();
        s.setName(tEquSupplier.getFullName());
        List<TEquSupplier> tEquSuppliers = tEquSupplierMapper.selectTEquSupplierList(s);
        for (TEquSupplier equSupplier : tEquSuppliers) {
            if(equSupplier.getFullName().equals(tEquSupplier.getFullName())){
                return 0;
            }
        }
        return tEquSupplierMapper.insertTEquSupplier(tEquSupplier);
    }

    /**
     * 修改装备管理_供应商名录
     * 20230209 1.247装备器具新增
     *
     * @param tEquSupplier 装备管理_供应商名录
     *                     20230209 1.247装备器具新增
     * @return 结果
     */
    @Override
    public int updateTEquSupplier(TEquSupplier tEquSupplier) {
        return tEquSupplierMapper.updateTEquSupplier(tEquSupplier);
    }

    /**
     * 批量删除装备管理_供应商名录
     * 20230209 1.247装备器具新增
     *
     * @param ids 需要删除的装备管理_供应商名录
     *            20230209 1.247装备器具新增主键
     * @return 结果
     */
    @Override
    public int deleteTEquSupplierByIds(String... ids) {
        return tEquSupplierMapper.deleteTEquSupplierByIds(ids);
    }

    /**
     * 删除装备管理_供应商名录
     * 20230209 1.247装备器具新增信息
     *
     * @param id 装备管理_供应商名录
     *           20230209 1.247装备器具新增主键
     * @return 结果
     */
    @Override
    public int deleteTEquSupplierById(Long id) {
        return tEquSupplierMapper.deleteTEquSupplierById(id);
    }
}
