package cn.sphd.miners.modules.generalAffairs.controller;

import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by Administrator on 2017/7/24.
 * 企业管理
 */
@Controller
@RequestMapping("/business")
public class BusinessController {

    @Autowired
    OrgService orgService;

    /**
     *<AUTHOR>
     *@date 2017/7/24 15:15
     *调转企业管理页面
    */
    @RequestMapping("/toPage.do")
    public String toPage(){
        return "/generalAffairs/companyMessage";
    }

    /**
     *<AUTHOR>
     *@date 2017/7/24 15:36
     *查询企业信息
    */
    @ResponseBody
    @RequestMapping("/getBusiness.do")
    public void getBusiness(HttpServletResponse response, User user) throws IOException {
        Map<String,Object> map = new HashMap<>();
        Organization org = user.getOrganization();
        if (org.getSuperId()!=null){
//            String mainPhoneSql = "SELECT mobile FROM t_sales_customer WHERE super_id = "+org.getSuperId()+" order by id asc";
//            String mainPhone = springJdbcDao.queryForString(mainPhoneSql);
            org.setMainPhone(org.getPhone());
        }
        map.put("org",org);
        ObjectToJson.objectToJson1(map,new String[]{"roles","user","area","financeAccounts","accountDetails","accountPeriods",
                                                     "mtCategoryOrg","financeReturnHashSet","financeChequeHashSet","orgPopedomHashSet"},response);
    }
}
