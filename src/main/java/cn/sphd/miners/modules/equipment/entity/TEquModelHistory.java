package cn.sphd.miners.modules.equipment.entity;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.persistence.Transient;

/**
 * 装备管理_型号历史
20230209 1.247装备器具新增
对象 t_equ_model_history
 * 
 * <AUTHOR>
 * @date 2023-02-10
 */
public class TEquModelHistory
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 组织机构ID */

    private Long org;

    /** 型号ID */

    private Long model;

    /** 装备ID */

    private Long equipment;

    @Transient
    private String equipmentName;

    /** 装备历史ID */

    private Long equipmentHistory;

    /** 型号,为空代表未知 */

    private String modelName;

    /** 编号 */

    private String modelCode;

    /** 计量单位,指向t_mt_unit表ID */

    private Long unitId;

    @Transient
    private String unitName;

    /** 所属类别,为空时代表未分类 */

    private Long category;

    @Transient
    private String categoryName;

    /** 供应商ID,为空时代表自行装配 */

    private Long supplier;

    @Transient
    private String supplierName;

    /** 使用寿命 */

    private Long lifeSpan;

    /** 原值 */

    private BigDecimal originalValue;

    /** 到厂日期,统一加01,返回前端时返YYYYMM */

    private Date receiveDate;

    /** 新旧情况:1-新,2-旧 */

    private Long conditions;

    /** 状态:0-不启用,1-启用 */

    private Long enabled;

    /** 启停用时间 */

    private Date enabledTime;

    /** 参与产品数 */

    private Long productCount;

    /** 备注 */

    private String memo;

    /** 创建人id */

    private Long creator;

    /** 创建人 */

    private String createName;

    /** 创建时间 */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createDate;

    /** 修改人id */

    private Long updator;

    /** 修改人 */

    private String updateName;

    /** 修改时间 */

    private Date updateDate;

    /** 操作:1-增,2-删,3-更改,4-启用,5-停用 6-修改公共信息*/

    private Long operation;

    private Integer quantity;

    /** 修改前记录ID */

    private Long previousId;

    /** 版本号,每次修改+1 */

    private Long versionNo;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOrg(Long org) 
    {
        this.org = org;
    }

    public Long getOrg() 
    {
        return org;
    }
    public void setModel(Long model) 
    {
        this.model = model;
    }

    public Long getModel() 
    {
        return model;
    }

    public Long getEquipment() {
        return equipment;
    }

    public void setEquipment(Long equipment) {
        this.equipment = equipment;
    }

    public Long getEquipmentHistory() {
        return equipmentHistory;
    }

    public void setEquipmentHistory(Long equipmentHistory) {
        this.equipmentHistory = equipmentHistory;
    }

    public void setModelName(String modelName)
    {
        this.modelName = modelName;
    }

    public String getModelName() 
    {
        return modelName;
    }
    public void setModelCode(String modelCode) 
    {
        this.modelCode = modelCode;
    }

    public String getModelCode() 
    {
        return modelCode;
    }
    public void setUnitId(Long unitId) 
    {
        this.unitId = unitId;
    }

    public Long getUnitId() 
    {
        return unitId;
    }
    public void setCategory(Long category) 
    {
        this.category = category;
    }

    public Long getCategory() 
    {
        return category;
    }
    public void setSupplier(Long supplier) 
    {
        this.supplier = supplier;
    }

    public Long getSupplier() 
    {
        return supplier;
    }
    public void setLifeSpan(Long lifeSpan) 
    {
        this.lifeSpan = lifeSpan;
    }

    public Long getLifeSpan() 
    {
        return lifeSpan;
    }
    public void setOriginalValue(BigDecimal originalValue) 
    {
        this.originalValue = originalValue;
    }

    public BigDecimal getOriginalValue() 
    {
        return originalValue;
    }
    public void setReceiveDate(Date receiveDate) 
    {
        this.receiveDate = receiveDate;
    }

    public Date getReceiveDate() 
    {
        return receiveDate;
    }
    public void setConditions(Long conditions) 
    {
        this.conditions = conditions;
    }

    public Long getConditions() 
    {
        return conditions;
    }
    public void setEnabled(Long enabled) 
    {
        this.enabled = enabled;
    }

    public Long getEnabled() 
    {
        return enabled;
    }
    public void setEnabledTime(Date enabledTime) 
    {
        this.enabledTime = enabledTime;
    }

    public Date getEnabledTime() 
    {
        return enabledTime;
    }
    public void setProductCount(Long productCount) 
    {
        this.productCount = productCount;
    }

    public Long getProductCount() 
    {
        return productCount;
    }
    public void setMemo(String memo) 
    {
        this.memo = memo;
    }

    public String getMemo() 
    {
        return memo;
    }
    public void setCreator(Long creator) 
    {
        this.creator = creator;
    }

    public Long getCreator() 
    {
        return creator;
    }
    public void setCreateName(String createName) 
    {
        this.createName = createName;
    }

    public String getCreateName() 
    {
        return createName;
    }
    public void setCreateDate(Date createDate) 
    {
        this.createDate = createDate;
    }

    public Date getCreateDate() 
    {
        return createDate;
    }
    public void setUpdator(Long updator) 
    {
        this.updator = updator;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Long getUpdator()
    {
        return updator;
    }
    public void setUpdateName(String updateName) 
    {
        this.updateName = updateName;
    }

    public String getUpdateName() 
    {
        return updateName;
    }
    public void setUpdateDate(Date updateDate) 
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() 
    {
        return updateDate;
    }
    public void setOperation(Long operation) 
    {
        this.operation = operation;
    }

    public Long getOperation() 
    {
        return operation;
    }
    public void setPreviousId(Long previousId) 
    {
        this.previousId = previousId;
    }

    public Long getPreviousId() 
    {
        return previousId;
    }
    public void setVersionNo(Long versionNo) 
    {
        this.versionNo = versionNo;
    }

    public Long getVersionNo() 
    {
        return versionNo;
    }

    public String getEquipmentName() {
        return equipmentName;
    }

    public void setEquipmentName(String equipmentName) {
        this.equipmentName = equipmentName;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getSupplierName() {
        return supplierName;
    }

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName;
    }
}
