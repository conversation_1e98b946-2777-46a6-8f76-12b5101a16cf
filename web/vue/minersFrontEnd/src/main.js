import { createApp } from 'vue'
import App from '@/App.vue'
import router from '@/router'
import store from '@/store/index'
import ElementPlus from "element-plus";
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// import "@/style/variable.scss";

import "element-plus/dist/index.css";
import zhCn from 'element-plus/es/locale/lang/zh-cn'
import "font-awesome/css/font-awesome.min.css"
import moment from 'moment'
import 'moment/dist/locale/zh-cn'
// 引入常量
import Enum from '@/constant/enum';
import EnumModule from '@/constant/module';

import "@/style/common.scss"

import TyDialog from "@/components/TyDialog.vue"
import TyPage from "@/components/TyPage.vue"
import SvgIcon from '@/components/SvgIcon.vue';


import "@/utils/formatDate.js"
import 'virtual:svg-icons-register'

moment.defaultFormat = "YYYY-MM-DD HH:mm:ss.SSS"
const app = createApp(App)
    .use(router).use(store)
    .use(ElementPlus, { locale: zhCn })
    .use(Enum, EnumModule)

app.component('TyDialog',TyDialog)
app.component('TyPage',TyPage)
app.component('SvgIcon', SvgIcon);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
}
app.mount('#app')
app.config.globalProperties.$filter = {
    format(value, type) {
        let format = "YYYY-MM-DD HH:mm:ss"
        if (type === 'day') {
            format = "YYYY-MM-DD"
        }
        if (value === null) {
            return ''
        } else {
            return moment(value).format(format)
        }
    }
}





