<template>
  <div class="privateSpace">
    <div class="contentHome">
      <router-view :userInfo = "userInfo" :fileUrl="fileUrl"/>
    </div>
    <div class="navBar">

      <div @click="navIndex(0)" :class="{ 'navActive': activeIndex === 0  }"><span class="icon" :class="activeIndex === 0 ? 'icon-life-active' :'icon-life'"></span><div>生活</div></div>
      <div @click="navIndex(1)" :class="{ 'navActive': activeIndex === 1  }"><span class="icon" :class="activeIndex === 1 ? 'icon-tongxunlu-active' :'icon-tongxunlu'"></span><div>通讯录</div></div>
      <div @click="navIndex(2)" :class="{ 'navActive': activeIndex === 2  }"><span class="icon" :class="activeIndex === 2 ? 'icon-gongzuo-active' :'icon-gongzuo'"></span><div>管理</div></div>
      <div @click="navIndex(3)" :class="{ 'navActive': activeIndex === 3  }"><span class="icon" :class="activeIndex === 3 ? 'icon-remind-active' :'icon-remind'"></span><div>提醒</div></div>
      <div @click="navIndex(4)" :class="{ 'navActive': activeIndex === 4  }"><span class="icon" :class="activeIndex === 4 ? 'icon-wode-active' :'icon-wode'"></span><div>我的</div></div>
    </div>

  </div>
</template>
<script>
import { updateTokenRemoveUser, getTerritoryAuthInfo, sureLogin,getRootPathFile,  closeAcc } from "@/api/api2.js"

export default {
  data() {
    return {
      activeIndex:4,
      fileUrl: '',
      userInfo: null
    }
  },

  mounted() {
    this.changeToken()
  },
  methods: {
    changeToken()  {
      updateTokenRemoveUser().then((res1)=>{
        let res = res1.data
        if(res.length > 0){
          localStorage.setItem('isChangeToken','0')
          this.getUserDetails();
        }
      })
    },
    getUserDetails()  {  
      getTerritoryAuthInfo().then((res1) => {
        let res = res1.data
        this.userInfo = res.data
        this.getFileRoot()
      }).catch((res)=>{
        console.log('bug了，还是换一token下吧')
        console.log(res)

      })
    },
    getFileRoot() {
      getRootPathFile().then((res) =>{
        // fileUrl:"https://hxz-t.frp.btransmission.commonDatanData/upload/"
        // ow365url:"https://ow365.cn/?i=13867&ssl=1&furl=https://hxz-t.frp.btransmission.com/upload/"
        // uploadUrl:"https://hxz-t.frp.btransmission.com/upload/"
        // webRoot:"https://hxz-t.frp.btransmission.com"

        console.log('getRootPathFile =' , res)
        this.fileUrl = res.fileUrl

      })
    },
    navIndex(indexNum){
      this.activeIndex = indexNum
      let path = 'homeMy'
      switch (indexNum){
        case 0: path = 'life'; break;
        case 1: path = 'mailList'; break;
        case 2: path = 'manage'; break;
        case 3: path = 'remind'; break;
        case 4: path = 'mine'; break;
      }
      this.$router.push(path)
    },

  },
}
</script>
<style lang="scss">
// 敬请期待
.wating{ display: block; height: 200px; width: 200px; margin: 200px auto 40px; background-repeat: no-repeat!important; opacity: 0.4; background-position:center center;   }
.watTxt{ color: #ccc; font-size: 36px; text-align: center;  }
.icon-life{ background: url("@/assets/tabBar/life.png");   background-size: 200px; }
.icon-mailList{ background: url("@/assets/tabBar/mailList.png");   background-size: 200px; }
.icon-manage1{  background: url("@/assets/tabBar/manage1.png");   background-size: 200px;  }
.icon-remind{ background: url("@/assets/tabBar/remind.png");   background-size: 200px; }

</style>
<style lang="scss" scoped>
.privateSpace{
  .contentHome{
    padding-bottom: 55px;
  }
  .navBar{
    display: flex;  bottom:0;  position:fixed; width: 100%; background-color: #fff; border-top: 1px solid #efefef;
    &>div{ flex: 1; text-align: center;  padding: 5px 0; font-size:12px;
    }
    &>div:last-child{  border-right:none;  }
    &>div.navActive{ color:$tyColorGreen; font-weight: bold;}

    .icon{ display: block; width: 20px; height: 20px; background-color: #fff; margin:3px auto;
      background-size: 100%; background-size: 100% 100%;
    }

    .icon-life{ background-image: url("@/assets/tabBar/life.png");  }
    .icon-life-active{ background-image: url("@/assets/tabBar/life.png");  }
    .icon-tongxunlu{ background-image: url("@/assets/tabBar/tongxunlu0.png");  }
    .icon-tongxunlu-active{ background-image: url("@/assets/tabBar/tongxunlu0.png");  }
    .icon-gongzuo{ background-image: url("@/assets/tabBar/gongzuo0.png");  }
    .icon-gongzuo-active{ background-image: url("@/assets/tabBar/gongzuo0.png");  }
    .icon-remind{ background-image: url("@/assets/tabBar/remind.png");  }
    .icon-remind-active{ background-image: url("@/assets/tabBar/remind.png");  }
    .icon-wode{ background-image: url("@/assets/tabBar/wode0.png");  }
    .icon-wode-active{ background-image: url("@/assets/tabBar/wode0.png");  }



  }

}
</style>
