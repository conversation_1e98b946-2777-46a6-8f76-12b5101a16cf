import {request} from "@/utils/request";

export function getStockMode() {
    return request({
        url: '/skl/getStockMode'
    })
}

/* ----------init_RAMtWh----------- */
// creator: 张旭博，2024-10-28 02:01:15，是否允许初始化

export function getStepList() {
    return request({
        url: '/initialize/getMaterialInitList.do'
    })
}
export function mustAllocation() {
    return request({
        url: '/initialize/mustAllocationList.do'
    })
}
// creator: 张旭博，2024-10-28 02:01:15，获取当前默认类型
export function getCurrentSetType() {
    return request({
        url: '/material/setting/state'
    })
}

// creator: 张旭博，2024-10-28 02:01:15， 获取所有类型列表
// updater: 张旭博，2025-05-06 10:32:18， 加上传值
export function getSetTypeList() {
    return request({
        url: '/material/setting/list',
        data: {
            category: 2
        }
    })
}

// creator: 张旭博，2025-05-06 10:32:36， 更新默认类型
export function updateSetType(id) {
    return request({
        url: '/material/setting/updateSet',
        data: { id: id }
    })
}

// creator: 张旭博，2024-11-13 08:31:51， 获取待处理材料
export function getWaitingForLocationList(data) {
    return request({
        url: '/mt/list',
        data: data
    })
}

// creator: 张旭博，2024-11-13 08:32:01， 获取材料下的供应商
export function getSuppliers(id, enabled=1) {
    return request({
        url: '/mt/suppliers',
        data: {
            id: id, // 材料id
            enabled: enabled
        }
    })
}

// creator: 张旭博，2024-11-15 08:56:35， 编辑初始库存-都不区分
export function initialStock22(data) {
    return request({
        url: '/material/setting/initialStock22',
        data: data
    })
}

// creator: 张旭博，2024-11-15 08:56:35， 编辑初始库存-区分供应商 不区分包装
export function initialStock12(data) {
    return request({
        url: '/material/setting/initialStock12',
        data: data
    })
}

// creator: 张旭博，2024-11-15 08:56:35， 编辑初始库存-不区分供应商 区分包装
export function initialStock21(data) {
    return request({
        url: '/material/setting/initialStock21',
        data: data
    })
}

// creator: 张旭博，2024-11-15 08:56:35， 编辑初始库存-都区分
export function initialStock11(data) {
    return request({
        url: '/material/setting/initialStock11',
        data: data
    })
}

// creator: 张旭博，2024-11-15 08:56:35， 获取当前库存
export function currentStock(material) {
    return request({
        url: '/material/setting/currentStock',
        data: {
            material: material
        }
    })
}

// creator: 张旭博，2024-11-15 08:56:35， 查看
export function currentStockDetail(material) {
    return request({
        url: '/material/setting/currentStockDetail',
        data: {
            material: material
        }
    })
}

// creator: 张旭博，2024-11-15 08:56:35， 步骤（完成）
export function allComplete(stepId) {
    return request({
        url: '/initialize/completeAllot.do',
        data: {
            id: stepId
        }
    })
}

/* ----------init_shelvesWarehouse----------- */
// creator: 张旭博，2024-05-28 09:57:25， 步骤列表
export function stepList() {
    return request({
        url: '/initialize/getInitListByCode.do',
        data: {
            code: 'shelves'
        }
    })
}

// creator: 张旭博，2024-05-28 09:57:25， 保存步骤
export function saveStep(data) {
    return request({
        url: '/initialize/completeAllot.do',
        data: data
    })
}

// creator: 张旭博，2024-05-28 09:57:25， 步骤状态清理
export function clearStep(data) {
    return request({
        url: '/initialize/unInitListByCode.do',
        data: data
    })
}


// creator: 张旭博，2024-05-28 09:57:25， 获取位数
export function getPlaceNum() {
    return request({
        url: '/iws/warehouse/shelf/setting/getPlaceNumByShelfId'
    })
}

// creator: 张旭博，2024-04-28 11:27:31， 新增货架
export function newShelf(obj) {
    return request({
        url: '/iws/shelf/base/add',
        data: obj
    })
    // obj = {
    //     shelfCode 代号
    //     shelfName 代号
    //     layers 层数
    //     amount 数量
    //     unitId 单位id
    //     unit 单位名
    //     length 长
    //     width 宽
    //     height 高
    // }
}

// creator: 张旭博，2024-04-28 11:27:31， 货架列表
export function shelvesList() {
    return request({
        url: '/iws/shelf/base/list'
    })
}

// creator: 张旭博，2024-04-28 11:27:31， 删除货架
export function deleteShelf(ids) {
    return request({
        url: '/iws/shelf/base/remove',
        data: {
            ids: ids // 货架id，多个的话用逗号分割
        }
    })
}

// creator: 张旭博，2024-04-28 11:27:31， 修改货架
export function editShelf(data) {
    return request({
        url: '/iws/shelf/base/edit',
        data: data
    })
    // id: 货架id
}

// creator: 张旭博，2024-04-28 11:27:31， 仓库列表
export function warehouseList() {
    return request({
        url: '/iws/warehouse/base/list'
    })
}

// creator: 张旭博，2024-04-28 11:27:31， 修改仓库-编辑所放置的货架
export function changeWh_editBase(data) {
    return request({
        url: '/iws/warehouse/shelf/add',
        data: data
    })
    // obj = {
    //     id 仓库id
    //     shelfNum 合计货架数量 可选
    //     shelfCategory 货架种类数 可选
    // }
}

// creator: 张旭博，2024-05-07 04:59:52， 仓库内的货架列表
export function shelvesListOfWh(data) {
    return request({
        url: '/iws/warehouse/shelf/list',
        data: {
            warehouse: data
        }
    })
    // data = {
    //     id 仓库id
    //     shelfNum 合计货架数量 可选
    //     shelfCategory 货架种类数 可选
    // }
}
// creator: 张旭博，2024-04-28 11:27:31， 修改仓库-修改名称或代号
export function changeWarehouseBase(data) {
    return request({
        url: '/iws/warehouse/base/edit',
        data: data
    })
    // data = {
    //     id 仓库id
    //     warehouseCode 代号 可选
    //     warehouseName 名称 可选
    // }
}

// creator: 张旭博，2024-04-28 11:27:31， 设置某仓库内某种货架的货架号
export function submit_setShelfNo_edit(data) {
    return request({
        url: '/iws/warehouse/shelf/setting/add',
        data: data
    })
}

// creator: 张旭博，2024-04-28 11:27:31， 新增保存智能仓库_仓库图片库
export function submit_uploadShelfImg(data) {
    return request({
        url: '/iws/warehouse/image/add',
        data: data
    })
}

// creator: 张旭博，2024-04-28 11:27:31， 标签设置列表
export function labelOfWh(data) {
    return request({
        url: '/iws/tags/setting/list',
        data: data
    })
}

// creator: 张旭博，2024-04-28 11:27:31， 修改保存智能仓库_标签设置
export function editLabelOfWh(data) {
    return request({
        url: '/iws/tags/setting/edit',
        data: data
    })
}

// creator: 张旭博，2024-04-28 11:27:31， 查询库位信息列表
export function locationOfWh(data) {
    return request({
        url: '/iws/warehouse/location/list',
        data: data
    })
}

// creator: 张旭博，2024-04-28 11:27:31， 修改库位信息-箭头朝向
export function submit_locationOfWh_edit(data) {
    return request({
        url: '/iws/warehouse/location/edit',
        data: data
    })
}

// creator: 张旭博，2024-05-15 08:24:15，已有“库位代号”中的“货架号”列表
export function shelfNoList(data) {
    return request({
        url: '/iws/warehouse/location/shelfCodeList',
        data: data
    })
}

// creator: 张旭博，2024-05-15 08:24:15，修改已有“库位代号”中的“货架号”
export function submit_changeShelfNo(data) {
    return request({
        url: '/iws/warehouse/location/shelfCodeEdit',
        data: data
    })
}

// creator: 张旭博，2024-05-15 08:24:15，清除所有数据
export function clearAllData() {
    return request({
        url: '/iws/warehouse/base/init'
    })
}

// creator: 张旭博，2024-05-15 08:24:15，删除货架号
export function deleteShelfCodes(data) {
    return request({
        url: '/iws/warehouse/location/deleteShelfCodes',
        data: data
    })
}

// creator: 张旭博，2024-05-15 08:24:15，增加货架
export function addShelfCodes(data) {
    return request({
        url: '/iws/warehouse/shelf/setting/addShelf',
        data: data
    })
}


// creator: 张旭博，2024-05-15 08:24:15，统计货架数与库位号数
export function countShelfNumAndLocationNum() {
    return request({
        url: '/iws/shelf/base/countByShelf'
    })
}


//-----------------财务-------------------------
// creator: 李玉婷，2024-09-13 08:24:15，获取首页发票列表
export function getInvoiceList() {
    return request({
        url: "../invoiceSetting/getFinanceInvoiceSetting.do"
    })
}
// creator: 李玉婷，2024-09-13 13:14:30，发票设置确定
export function addInvoiceTj(data, url) {
    return request({
        url: url,
        data: data
    })
}
// creator: 李玉婷，2024-09-13 13:32:10，发票设置确定
export function getAccounts(data) {
    return request({
        url: "../account/getAllAccounts.do",
        data: data
    })
}
// creator: 李玉婷，2024-09-30 13:12:45，启用“财务”功能-确定
export function initStartSure(code) {
    return request({
        url: '/initialize/getInitListByCode.do',
        data: {
            code: code
        }
    })
}

// creator: 李玉婷，2024-09-30 09:57:25， 保存步骤
export function financeSaveStep(data) {
    return request({
        url: '/initialize/completeAllot.do',
        data: data
    })
}
// creator: 李玉婷，2024-09-30 14:15:05， 新增银行账户确定
export function saveBankSure(url, data) {
    return request({
        url: url,
        data: data
    })
}
// creator: 李玉婷，2024-10-08 08:12:32， 新增银行账户确定
export function getStopLvs(data) {
    return request({
        url: '../invoiceSetting/deactivatedTaxRate.do',
        data: data
    })
}
// creator: 李玉婷，2024-10-09 10:02:51， 银行账户删除
export function delBankSure(data) {
    return request({
        url: '../account/deleteAccount.do',
        data: data
    })
}
// creator: 李玉婷，2024-10-09 11:45:05， 有没有基本户
export function getBaseAccount(data) {
    return request({
        url: '../account/getBaseAccount.do',
        data: data
    })
}








//-----------------财务结束-------------------------

/*会计*/
// creator: 李玉婷，2024-12-17 08:42:11， 返回当前建账状态
export function getBuildAccountState() {
    return request({
        url: "../accountant/getBuildAccountState.do",
        data: {}
    })
}
// creator: 李玉婷，2024-12-20 15:13:20， 返回当前建账状态
export function initKJinfo() {
    return request({
        url: "../accountant/initKJinfo.do",
        data: {}
    })
}


//-----------------销售1-------------------------
export function getUniversalPt() {
    return request({
        url: "../product/getTYProductListByCategory.do",
        data: {
            "pageSize": 20,
            "currPage":1,
            "category": "",
            "param": ""
        }
    })
}

export function addTYProductSure(data) {
    return request({
        url: "../product/addTYProduct.do",
        data: data
    })
}