.modBill span{margin-right: 50px;}
.timeBtn{position: relative;display: inline-block;}
.timeBtn> span{padding: 8px 16px;border: 1px solid #bfbfbf;}
.timeBtn .fa{margin-left: 10px;  font-size: 16px;  vertical-align: top;}
.monthTb,.yearTb{position: absolute;  top: 30px;  border-left: 1px solid #ccc;  border-right: 1px solid #ccc;  width: 109px;}
.monthTb>div,.yearTb>div{padding: 6px 0 6px 16px;border-bottom: 1px solid #ccc; font-size: 12px; }
.taskFinished{position: relative;}
#purchaseCompleted{position: absolute;top: -50px;  right: 10px;width: 180px;}
.headCon{margin-bottom: 30px;}
.gapB{margin-bottom: 20px;}
.mainCon1{margin-left: 30px;}
.mainCon2,.mainCon3,.mainCon4,.mainCon5{display: none;margin-left: 30px;}
.log{font-weight: bold;}
.mainCon3 .ty-table td input{border: none;}
.middleGap{margin-bottom: 50px;}
.bg-yellow thead td {  border-color: #fff0;  background-color: #fff2cc;  }
.nextStep{margin-top: 80px;}
.fa-dot-circle-o {    color: #48cfad;  }
.mainCon3 input[disabled] {background: #bfbfbf;}
.narrowSize{width: 80%;}
.mdFont {font-size: 18px;}
.reviewLogBtn{font-size: 16px;  font-weight: bold;text-align: right;}
.conTtl {  margin-bottom: 8px;}
.conTtl span{  margin-right: 20px;}
.fontH5{font-size: 18px;}
.otherControl hr{border-top: 1px solid #ccc;}
.yellowTb thead tr td {  border-color: rgba(255, 255, 255, 0);  background: #fff2cc;  }
.yellowTb thead tr td.hdNot {  background: none;  }
.arrowBtn i{margin-left: 2px;}
