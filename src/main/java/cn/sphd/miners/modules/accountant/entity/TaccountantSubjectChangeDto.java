package cn.sphd.miners.modules.accountant.entity;

import org.apache.ibatis.type.Alias;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Alias("TaccountantSubjectChangeDto")
public class TaccountantSubjectChangeDto implements Serializable {

    private static final long serialVersionUID =  7675884414599401232L;

    private Integer id;

    private Integer org;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date changeDate;

    private Boolean isChanged;//0-建账后，1-变动后。建账后所有科目要保存一遍，如果某天对科目有操作，还要为这天整体保存一遍所有科目

    private String subject;

    private String parent;

    private String name;

    private Integer category;

    private String categoryname;

    private Integer level;

    private Integer orders;

    private Boolean state;

    private Integer maxChildSubjects;

    private String accountMonth;

    private String balanceDirection;

    private String measureUnit;

    private String quantityAssistingAccounting;

    private Integer establish;

    private Integer creator;

    private String createName;

    private Date createDate;

    public Boolean getChanged() {
        return isChanged;
    }

    public void setChanged(Boolean changed) {
        isChanged = changed;
    }

    public String getCategoryname() {
        return categoryname;
    }

    public void setCategoryname(String categoryname) {
        this.categoryname = categoryname;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Date getChangeDate() {
        return changeDate;
    }

    public void setChangeDate(Date changeDate) {
        this.changeDate = changeDate;
    }

    public Boolean getIsChanged() {
        return isChanged;
    }

    public void setIsChanged(Boolean isChanged) {
        this.isChanged = isChanged;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject == null ? null : subject.trim();
    }

    public String getParent() {
        return parent;
    }

    public void setParent(String parent) {
        this.parent = parent == null ? null : parent.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public Boolean getState() {
        return state;
    }

    public void setState(Boolean state) {
        this.state = state;
    }

    public Integer getMaxChildSubjects() {
        return maxChildSubjects;
    }

    public void setMaxChildSubjects(Integer maxChildSubjects) {
        this.maxChildSubjects = maxChildSubjects;
    }

    public String getAccountMonth() {
        return accountMonth;
    }

    public void setAccountMonth(String accountMonth) {
        this.accountMonth = accountMonth == null ? null : accountMonth.trim();
    }

    public String getBalanceDirection() {
        return balanceDirection;
    }

    public void setBalanceDirection(String balanceDirection) {
        this.balanceDirection = balanceDirection == null ? null : balanceDirection.trim();
    }

    public String getMeasureUnit() {
        return measureUnit;
    }

    public void setMeasureUnit(String measureUnit) {
        this.measureUnit = measureUnit == null ? null : measureUnit.trim();
    }

    public String getQuantityAssistingAccounting() {
        return quantityAssistingAccounting;
    }

    public void setQuantityAssistingAccounting(String quantityAssistingAccounting) {
        this.quantityAssistingAccounting = quantityAssistingAccounting == null ? null : quantityAssistingAccounting.trim();
    }

    public Integer getEstablish() {
        return establish;
    }

    public void setEstablish(Integer establish) {
        this.establish = establish;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName == null ? null : createName.trim();
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

}