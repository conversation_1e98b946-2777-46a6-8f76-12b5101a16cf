package cn.sphd.miners.modules.generalAffairs.dto;

import cn.sphd.miners.common.utils.MyStrings;

import java.io.Serializable;

public class AddressBookDto implements Serializable {

    private Integer userID;
    private String userName;
    private String departName;
    private String mobile;
    private String isDuty;
    private String gender;		//性别  1-男 0-女
    private String imgPath;
    private String roleCode;



    public Integer getUserID() {
        return userID;
    }

    public void setUserID(Integer userID) {
        this.userID = userID;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getDepartName() {
        return departName;
    }

    public void setDepartName(String departName) {
        this.departName = departName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getIsDuty() {
        return isDuty;
    }

    public void setIsDuty(String isDuty) {
        this.isDuty = isDuty;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getImgPath() {
        return imgPath;
    }

    public void setImgPath(String imgPath) {
        this.imgPath = imgPath;
    }

    public String getRoleCode() {
        return roleCode;
    }

    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    public AddressBookDto() {
    }


    public AddressBookDto(Integer userID, String userName, String departName, String mobile,String isDuty,String gender,String imgPath,String roleCode) {
        this.userID = userID;
        this.userName = userName;
        if(isDuty.equalsIgnoreCase("4")) {
            this.departName = "外部浏览";
//        } else if (MyStrings.nulltoempty(departName).isEmpty()){
//            this.departName = "其他";
        } else {
            this.departName = MyStrings.nulltoempty(departName);
        }
        this.mobile = mobile;
        this.isDuty=isDuty;
        this.gender=gender;
        this.imgPath=imgPath;
        this.roleCode=roleCode;

    }

}
