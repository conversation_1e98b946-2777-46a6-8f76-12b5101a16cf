<template>
  <div id="potentialApply" class="potential">
    <TY_NavTop title="潜在客户申请"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="panel-content def-gap">
        <el-row>
          <el-col :span="6">{{recordTitle}}</el-col>
          <el-col v-if="editIndex === 0" :span="18" class="createDetail">创建人：{{details.updateName}} {{details.updateDate}}</el-col>
          <el-col v-else :span="18" class="createDetail">修改人：{{details.updateName}} {{details.updateDate}}</el-col>
        </el-row>
      </div>
      <div class="panel-content sect" v-if="recordType === '1'">
        <el-row>
          <el-col :span="6">客户名称</el-col>
          <el-col :span="18" :class="{'color-red':flagControl('fullName')}">{{details.fullName}}</el-col>
        </el-row>
        <el-row>
          <el-col :span="6">地    址</el-col>
          <el-col :span="18" :class="{'color-red':flagControl('address')}">{{details.address}}</el-col>
        </el-row>
        <div v-if="sphdSocket.user.oid === 0">
          <el-row>
            <el-col :span="6">最高负责人</el-col>
            <el-col :span="18" :class="{'color-red':flagControl('supervisorName')}">{{details.supervisorName}}</el-col>
          </el-row>
          <el-row>
            <el-col :span="6">手 机</el-col>
            <el-col :span="18" :class="{'color-red':flagControl('supervisorMobile')}">{{details.supervisorMobile}}</el-col>
          </el-row>
        </div>
        <el-row>
          <el-col :span="6">全景图片</el-col>
          <el-col :span="18">
            <div class="col-flex">
              <div class="block" v-for="(img, index) in details.qImages" :key="index">
                <el-image
                  style="width: 80px; height: 80px"
                  :src="fileUrl + img.normal"
                  fit="contain" @click="preview(1, img.normal)"></el-image>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">产品图片</el-col>
          <el-col :span="18">
            <div class="col-flex">
              <div class="block" v-for="(img, index) in details.pImages" :key="index">
                <el-image
                  style="width: 80px; height: 80px"
                  :src="fileUrl + img.normal"
                  fit="contain" @click="preview(2, img.normal)"></el-image>
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">首次接触日期</el-col>
          <el-col :span="18" :class="{'color-red':flagControl('firstContactTime')}">{{details.firstContactTime | formatDate('yyyy/MM/dd')}}</el-col>
        </el-row>
        <el-row>
          <el-col :span="6">首次接触地点</el-col>
          <el-col :span="18" :class="{'color-red':flagControl('firstContactAddress')}">{{details.firstContactAddress}}</el-col>
        </el-row>
        <el-row>
          <el-col :span="6">信息获取渠道</el-col>
          <el-col :span="18" :class="{'color-red':flagControl('infoSource')}">{{details.infoSource}}</el-col>
        </el-row>
      </div>
      <div class="panel-content" v-if="recordType === '2' || recordType === '22'">
        <div class="contactList">
          <el-row>
            <el-col :span="8" :class="{'color-red':flagControl('name')}">{{details.name}}</el-col>
            <el-col :span="16" :class="{'color-red':flagControl('post')}">{{details.post}}</el-col>
          </el-row>
          <el-row v-if="details.mobile !== ''">
            <el-col :class="{'icon-mobile': details.mobile !== '','color-red':flagControl('mobile')}" :span="24">{{details.mobile}}</el-col>
          </el-row>
          <el-row v-for="(social, index) in socialList.mobile" v-bind:key="index">
            <el-col :span="24" :class="{'icon-mobile': details.mobile === '' && index === 0, 'color-red': flagContact(social.code)}">{{social.code}}</el-col>
          </el-row>
          <el-row v-for="(social, index) in socialList.qq" v-bind:key="index">
            <el-col :span="24" :class="{'icon-qq':index === 0, 'color-red': flagContact(social.code)}">{{social.code}}</el-col>
          </el-row>
          <el-row v-for="(social, index) in socialList.email" v-bind:key="index">
            <el-col :span="24" :class="{'icon-email':index === 0, 'color-red': flagContact(social.code)}">{{social.code}}</el-col>
          </el-row>
          <el-row v-for="(social, index) in socialList.weixin" v-bind:key="index">
            <el-col :span="24" :class="{'icon-weixin':index === 0, 'color-red': flagContact(social.code)}">{{social.code}}</el-col>
          </el-row>
          <el-row v-for="(social, index) in socialList.weibo" v-bind:key="index">
            <el-col :span="24" :class="{'icon-weibo':index === 0, 'color-red': flagContact(social.code)}">{{social.code}}</el-col>
          </el-row>
          <el-row v-for="(social, index) in socialList.defined" v-bind:key="index">
            <el-col :span="24" v-if="index === 0" class="icon-defined">自定义  <span style="margin-left:10px;" :class="{'color-red': flagContact(social.code) || flagName(social.name)}">{{social.name}}   {{social.code}}</span></el-col>
            <el-col :span="24" v-else :class="{'color-red': flagContact(social.code) || flagName(social.name)}">{{social.name}}   {{social.code}}</el-col>
          </el-row>
        </div>
        <div class="cus_visitCard block" v-if="details.visitCard !== null && details.visitCard !== ''">
          <el-image
            style="width: 300px; height: 200px"
            :src="fileUrl + details.visitCard"
            fit="contain">
            <div slot="error" class="image-slot">
              <img :src="fileUrl + details.visitCard" />
            </div>
          </el-image>
        </div>
      </div>
      <div class="panel-content" v-if="recordType === '3' || recordType === '33'">
        <div class="sect-con def-gap">
          <el-row>
            <el-col :span="8">访谈对象</el-col>
            <el-col :span="14" :class="{'color-red':flagControl('interviewer')}">共{{details.interviewerList.length}}人：{{details.interviewerNames | handleNull | formatName}}</el-col>
          </el-row>
          <el-row>
            <el-col :span="8">访谈日期</el-col>
            <el-col :span="14" :class="{'color-red':flagControl('interviewDate')}">{{details.interviewDate | formatDate('yyyy-MM-dd')}}</el-col>
          </el-row>
          <el-row>
            <el-col :span="8">同行者（同事）</el-col>
            <el-col :span="14" :class="{'color-red':flagControl('tsNames')}">共{{details.tsList.length}}人：{{details.tsNames}}</el-col>
          </el-row>
          <el-row>
            <el-col :span="8">同行者（非同事）</el-col>
            <el-col :span="14" :class="{'color-red':flagControl('ftxNames')}">共{{details.ftsList.length}}人：{{details.ftxNames}}</el-col>
          </el-row>
        </div>
        <div class="def-gap">
          <el-row>
            <el-col :span="24">访谈目标</el-col>
          </el-row>
          <el-row><el-col :span="24" :class="{'color-red':flagControl('mbContent')}">{{ details.mbContent }}</el-col></el-row>
        </div>
        <div class="def-gap">
          <el-row>
            <el-col :span="24">谈话要点</el-col>
          </el-row>
          <el-row v-for="(participat, index) in details.thyd" :key="index">
            <el-col :span="6" :class="{'color-red':flagTalks('name', index)}">{{ participat.participatorName }}</el-col>
            <el-col :span="18" :class="{'color-red':flagTalks('content', index)}">{{ participat.content }}</el-col>
          </el-row>
        </div>
        <div class="def-gap">
          <el-row>
            <el-col :span="24">成果/结论</el-col>
          </el-row>
          <el-row :class="{'color-red':flagCg('cgContent', index)}" v-for="(cgContent, index) in details.cgContent" :key="index">
            <el-col :span="24">{{ cgContent }}</el-col>
          </el-row>
        </div>
      </div>
    </div>
    <el-dialog
      :title="imgTitle"
      :visible.sync="previewDialog"
      width="70%">
      <el-form class="preview">
        <el-form-item>
          <el-radio-group v-model="browser">
            <el-radio :label="1">在线预览</el-radio>
            <el-radio :label="2">下载</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button handle-center">
          <a class="fc-btn-normal" @click="previewDialog = false">取消</a>
          <span class="fc-btn ui-btn_info" ref="operate" :path="previewPath" :download="previewPath" @click="oprationFile($event)">确定</span>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {formatDate, formatName, countNum, handleNull} from '../../../js/common'
export default {
  name: 'recordDetail',
  filters: {
    formatDate,
    formatName,
    countNum,
    handleNull
  },
  data () {
    return {
      loading: true,
      details: {
        interviewer: '',
        tsIds: '',
        ftxIds: '',
        interviewDate: '',
        content: '',
        mbContent: '',
        tsList: [],
        interviewerList: [],
        thyd: [],
        cgContent: [],
        ftsList: []
      },
      front: {
        interviewer: '',
        tsIds: '',
        ftxIds: '',
        interviewDate: '',
        content: '',
        mbContent: '',
        tsList: [],
        interviewerList: [],
        thyd: [],
        cgContent: [],
        ftsList: []
      },
      editIndex: 0,
      recordTitle: '',
      recordType: '',
      socialList: {},
      browser: 1,
      imgTitle: '',
      previewPath: '',
      previewDialog: false,
      fileUrl: window.parent.$.fileUrl
    }
  },
  computed: {
    flagControl () {
      return function (value) {
        return (this.front !== null && this.front[value] !== this.details[value])
      }
    },
    flagContact () {
      return function (value) {
        if (this.front !== null) {
          let frontList = this.front.socialList
          let arr = frontList.find(item => item.code === value)
          if (arr === undefined) {
            return true
          } else {
            return false
          }
        } else {
          return false
        }
      }
    },
    flagName () {
      return function (value) {
        if (this.front !== null) {
          let frontList = this.front.socialList
          let arr = frontList.find(item => item.name === value)
          if (arr === undefined) {
            return true
          } else {
            return false
          }
        } else {
          return false
        }
      }
    }
  },
  created () {
    let _this = this
    let recordType = _this.$route.params.type
    let recordDetailId = _this.$route.params.detailId
    let recordFrontId = _this.$route.params.frontId
    let recordNum = _this.$route.params.num
    let urlStr = ''
    _this.recordType = recordType
    if (recordType === '1') {
      urlStr = '../../../sales/getRecordBaseDetails.do'
    } else if (recordType === '2' || recordType === '22') {
      urlStr = '../../../sales/getRecordContactDetails.do'
    } else if (recordType === '3' || recordType === '33') {
      urlStr = '../../../sales/getRecordInterviewDetails.do'
    }
    if (recordNum === '0') {
      _this.editIndex = 0
      _this.recordTitle = '原始信息'
    } else {
      _this.editIndex = recordNum
      _this.recordTitle = '第' + recordNum + '次修改后'
    }
    this.axios.post(urlStr, {
      id: recordDetailId,
      frontId: recordFrontId
    }).then(function (response) {
      _this.loading = false
      if (recordType === '2' || recordType === '22') {
        let list = response.data.data.now.socialList
        let socialList = {
          mobile: [],
          qq: [],
          email: [],
          weixin: [],
          weibo: [],
          defined: []
        }
        for (let i in list) {
          let type = list[i].type
          switch (type) {
            case '1':
              socialList.mobile.push(list[i])
              break
            case '2':
              socialList.qq.push(list[i])
              break
            case '3':
              socialList.email.push(list[i])
              break
            case '4':
              socialList.weixin.push(list[i])
              break
            case '5':
              socialList.weibo.push(list[i])
              break
            case '9':
              socialList.defined.push(list[i])
              break
          }
        }
        _this.socialList = socialList
      } else if (recordType === '3' || recordType === '33') {
        _this.front = response.data.data.front
        _this.details = response.data.data.now
        if (_this.front === null) {
          _this.front = response.data.data.now
        } else {
          _this.front.cgContent = _this.front.cgContent !== null ? _this.front.cgContent.split("$a$") : []
        }
        _this.details.cgContent = _this.details.cgContent !== null ? _this.details.cgContent.split("$a$") : []
        _this.details.updateName = _this.$route.params.updateName
        _this.details.updateDate = formatDate(parseInt(_this.$route.params.updateDate), 'yyyy/MM/dd hh:mm:ss')
      }
    }).catch(function (error) {
      console.log(error)
    })
  },
  methods: {
    preview: function (tab, path) {
      this.previewDialog = true
      if (tab === 1) {
        this.imgTitle = '全景图片'
      } else {
        this.imgTitle = '产品图片'
      }
      this.previewPath = path
    },
    oprationFile: function (event) {
      let el = event.currentTarget
      window.parent.previewOrDownload(el, this.browser)
    },
    flagTalks: function (val, index) {
      let item = this.details.thyd[index]
      var frontItem = this.front.thyd.find(value => value.id === item.id);
      return (frontItem && frontItem[val] === item[val])
    },
    flagCg: function (val, index) {
      let con = this.details.cgContent[index]
      let flag = this.front.cgContent.findIndex(value => value === con);
      return (flag === -1)
    }
  }
}
</script>
