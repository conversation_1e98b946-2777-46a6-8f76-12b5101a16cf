package cn.sphd.miners.modules.recruit.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * 招聘启事岗位表
 * <AUTHOR>
 * @date 20230506
 **/


@Entity
@Table(name = "t_rd_notice_post")
public class RdNoticePost {

    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;//`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',

    @Column
    private Integer org;//机构ID

    @Column(name="notice" ,nullable=true , unique=false, insertable=false, updatable=false)
    private Long noticeId;//启事ID

    @Column(name="post" ,nullable=true , unique=false, insertable=false, updatable=false)
    private Long postId;//岗位ID

    @Column(name = "post_name",length = 100)
    private String postName;//岗位名

    @Column(name = "plan_count")
    private Integer planCount;//计划人数

    @Column(name = "recieved_count")
    private Integer recievedCount;//已收简历份数

    @Column(name = "noticed_count")
    private Integer noticedCount;//已通知面试人数

    @Column(name = "interviewing_count")
    private Integer interviewingCount;//面试中人数

    @Column(name = "interviewed_count")
    private Integer interviewedCount;//已面试人数

    @Column(name = "entered_count")
    private Integer enteredCount;//已入职人数

    @Column(length = 255)
    private String memo; //备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="operation"   , nullable=true , unique=false)
    private Integer operation; //操作:1-增,2-删,3-改,4-启用,5-停用,6-更改名称,7-改错别字

    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId; //修改前记录ID

    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo; //版本号,每次修改+1

    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="post", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private RdPost rdPost ;//岗位主表

    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="notice", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private RdNotice rdNotice ;//启事主表


    //启事项目表
    @JsonIgnore
    @JSONField(serialize = false)
    @OneToMany(targetEntity= RdNoticeItem.class, fetch= FetchType.EAGER, mappedBy="rdNoticePost", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<RdNoticeItem> rdNoticeItemHashSet = new HashSet<RdNoticeItem>();

    //启事其他事项表
    @JsonIgnore
    @JSONField(serialize = false)
    @OneToMany(targetEntity= RdNoticeAdditional.class, fetch= FetchType.EAGER, mappedBy="rdNoticePost", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<RdNoticeAdditional> rdNoticeAdditionalHashSet = new HashSet<RdNoticeAdditional>();

    //招聘简历表
    @JsonIgnore
    @JSONField(serialize = false)
    @OneToMany(targetEntity= RdResume.class, fetch= FetchType.EAGER, mappedBy="rdNoticePost", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<RdResume> rdResumeHashSet = new HashSet<RdResume>();

    //招聘面试通知表
    @JsonIgnore
    @JSONField(serialize = false)
    @OneToMany(targetEntity= RdInterviewNotification.class, fetch= FetchType.EAGER, mappedBy="rdNoticePost", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<RdInterviewNotification> rdInterviewNotificationHashSet = new HashSet<RdInterviewNotification>();



    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Long getNoticeId() {
        return noticeId;
    }

    public void setNoticeId(Long noticeId) {
        this.noticeId = noticeId;
    }

    public Long getPostId() {
        return postId;
    }

    public void setPostId(Long postId) {
        this.postId = postId;
    }

    public Integer getPlanCount() {
        return planCount;
    }

    public void setPlanCount(Integer planCount) {
        this.planCount = planCount;
    }

    public Integer getRecievedCount() {
        return recievedCount;
    }

    public void setRecievedCount(Integer recievedCount) {
        this.recievedCount = recievedCount;
    }

    public Integer getNoticedCount() {
        return noticedCount;
    }

    public void setNoticedCount(Integer noticedCount) {
        this.noticedCount = noticedCount;
    }

    public Integer getInterviewingCount() {
        return interviewingCount;
    }

    public void setInterviewingCount(Integer interviewingCount) {
        this.interviewingCount = interviewingCount;
    }

    public Integer getInterviewedCount() {
        return interviewedCount;
    }

    public void setInterviewedCount(Integer interviewedCount) {
        this.interviewedCount = interviewedCount;
    }

    public Integer getEnteredCount() {
        return enteredCount;
    }

    public void setEnteredCount(Integer enteredCount) {
        this.enteredCount = enteredCount;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getOperation() {
        return operation;
    }

    public void setOperation(Integer operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public RdPost getRdPost() {
        return rdPost;
    }

    public void setRdPost(RdPost rdPost) {
        this.rdPost = rdPost;
    }

    public RdNotice getRdNotice() {
        return rdNotice;
    }

    public void setRdNotice(RdNotice rdNotice) {
        this.rdNotice = rdNotice;
    }

    public Set<RdNoticeItem> getRdNoticeItemHashSet() {
        return rdNoticeItemHashSet;
    }

    public void setRdNoticeItemHashSet(Set<RdNoticeItem> rdNoticeItemHashSet) {
        this.rdNoticeItemHashSet = rdNoticeItemHashSet;
    }

    public Set<RdNoticeAdditional> getRdNoticeAdditionalHashSet() {
        return rdNoticeAdditionalHashSet;
    }

    public void setRdNoticeAdditionalHashSet(Set<RdNoticeAdditional> rdNoticeAdditionalHashSet) {
        this.rdNoticeAdditionalHashSet = rdNoticeAdditionalHashSet;
    }

    public Set<RdResume> getRdResumeHashSet() {
        return rdResumeHashSet;
    }

    public void setRdResumeHashSet(Set<RdResume> rdResumeHashSet) {
        this.rdResumeHashSet = rdResumeHashSet;
    }

    public Set<RdInterviewNotification> getRdInterviewNotificationHashSet() {
        return rdInterviewNotificationHashSet;
    }

    public void setRdInterviewNotificationHashSet(Set<RdInterviewNotification> rdInterviewNotificationHashSet) {
        this.rdInterviewNotificationHashSet = rdInterviewNotificationHashSet;
    }

    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }
}
