@-webkit-keyframes twink {
    from {
        opacity: 1.0;
    }
    50% {
        opacity: 0.4;
    }
    to {
        opacity: 1.0;
    }
}
@keyframes twink {
    from {
        opacity: 1.0;
    }
    50% {
        opacity: 0.2;
    }
    to {
        opacity: 1.0;
    }
}
html, body{
    height: 100%;
}

.minersLogo{ color:#eaeaea; font-size:25px; position: relative; cursor: default; font-family: 'Arial' }
.blue{ background:#00AEED; color:#fff; border:1px solid #168eba  }
.blue:hover{ background:#00b7ee; color:#fff;   }
.green_logo{ color:#3CB0A7;   }


/*二级导航*/
.scdNav{ border-bottom: 2px solid #36C6D3; margin-bottom:20px;   }
.scdNav h3{ font-family:Microsoft YaHei;       }
.nav_{ font-family: 仿宋; font-size: 14px;  }
.navTxt{ color:#3CB0A7; font-weight:normal; font-size:14px; font-family:Microsoft YaHei;      }
.bg-green{ background-color:#36C6D3!important; padding:3px;  color:#3CB0A7 ;      }
.navTxt a{ color:#3CB0A7;}
.navTxt a:hover{ color:#36C6D3; text-decoration:underline; cursor:default;  }

/*顶部导航*/
.top-menu .navbar-nav li.navbar-navItem{  height: 46px;  padding: 0 16px;  color: #b4bcc8;  line-height:46px; }
.left-menu .navbar-nav li.navbar-navItem{  height: 46px;  padding: 0 16px;  color: #b4bcc8;  line-height:46px; }
.top-menu .navbar-nav li.navbar-navItem:hover{  background-color: #3f4f62;  cursor: pointer; }
.left-menu .navbar-nav li.navbar-navItem:hover{  background-color: #3f4f62;  cursor: pointer; }
.navbar-nav-blog .navbar-navItem-title{  font-size: 20px;  line-height:46px; }
.navbar-nav-blog .twink{
    color: #fff;
    -webkit-animation: twink 0.8s linear 1s 5 alternate;
    animation: twink 0.8s linear infinite;
}
.navbar-nav-msg i{  line-height:46px;  font-size:20px; }
.navbar-nav-home i{  line-height:46px;  font-size:24px; }
.badge {  position: absolute; right: 5px; top: 5px; background-color: #36c6d3; font-weight: normal;  }
#loginUserIMG{  width: 30px;  height: 30px;}



.affair{ margin-right: 20px; padding: 20px; margin-top: 10px;  }
.affair li{ float:left; margin: 15px 13px; width: 100px; height: 105px; text-align: center; position: relative; overflow: visible; border: 1px dashed transparent; border-radius: 2px; background-color: #f4f6fa; cursor: pointer; }
.affair li:hover{  background-color: #f1f3f7;}
.affair li i{ color: #5d9cec; font-size:40px; margin-top:18px;    }
.affair li .icon-name{ display: block; width: 100%; height: 20px; line-height: 20px; color: #666; font-size: 12px; text-align: center; margin-top: 15px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; }
.affair li.disabled { cursor: default; box-shadow: none; opacity:0.7;  }
.affair li.disabled .icon-name{ color: #aaa; }

.page-header-inner{
    display: flex;
    justify-content: space-between;
}
.middle-menu{
    flex: none;
}
.principal_avatar{
    line-height: 26px;
    color: #b4bcc8;
    display: flex;
    height: 100%;
}
.principal_avatar .principal_tip{
    padding:10px 0;
    margin: 0 8px;
}
.principal_avatar .principal_list{
    display: flex;
}
.principal_list .principal_item, .principal_list .dropDown{
    padding:10px 12px;
    cursor: default;
    position: relative;
    flex: none;
}
.principal_list .principal_item:hover{
    background: #364150;
}
.dropDown:hover .dropDownMenu{
    display: block;
}
.dropDownMenu{
    display: none;
    position: absolute;
    right: 15px;
    top:46px;
    width: 190px;
    background: #364150;
    border-radius: 2px;
    padding: 8px;
    box-shadow: 0 1px 3px #2B364354;
}
.dropDownMenu:before{
    content: '';
    width: 0;
    height: 0;
    position: absolute;
    top: -10px;
    right: 0;
    border: 10px solid transparent;
    border-right-color: #364150;

}
.dropDownMenu .principal_item{
    padding:4px 8px;
}
.dropDownMenu .principal_item:hover{
    background-color: #2b3643;
}

.dbIcon{ position: absolute;
    font-size: 12px!important;
    left: 36px;
    color: #fff!important;
    top: 2px; }
.principal_recent_tip{
    padding: 10px 0;
    margin-right: 30px;
}






