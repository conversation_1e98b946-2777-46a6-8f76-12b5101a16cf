<template>
  <div class="userAgreement">
    <Pageheader :title="title" :showBack="true"/>
    <div class="live-contain">
      <div class="live-cells">
        <div class="live-cell">{{info.content}}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getAboutFinalDetail, getAboutFileList } from "@/api/api.js";
import { reactive, ref, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";

let title = ref()
let info = ref({})
let pageInfo = ref({})
const router = useRouter();
const route = useRoute();
console.log('params', route.params)
let fileId = route.params.id
let module = route.params.module

onMounted(() => {
  switch (module) {
    case 'release':
      title.value = '版本说明'
      getFinalDetail(fileId)
      break
    case 'usingHelp':
      title.value = '使用帮助'
      getFinalDetail(fileId)
      break
    case 'userAgreement':
      title.value = '服务协议'
      getNextModuleList()
      break
    case 'privacyPolicy':
      title.value = '隐私条款'
      getNextModuleList()
      break
  }
  console.log('info', info)
})

const getFinalDetail = (fileId) => {
  getAboutFinalDetail(fileId).then(res => {
    let data = res.data.aboutFile
    info.value = data
  })
}

const getNextModuleList = () => {
  let categoryId = route.params.id
  let data = {
    category: categoryId,
    currentPageNo: 1,
    pageSize: 20
  }
  getAboutFileList(data).then(res => {
    let files = res.data.list
    if (files.length > 0) {
      info.value = files[0]
    }
  })
}

</script>

<style lang="scss">
.houseSty8{
  position: relative;
  top:5px
}
</style>



