//
//package cn.sphd.miners.modules.material.entity;
//
////MP-MANAGED-ADDED-AREA-BEGINNING @import@
////MP-MANAGED-ADDED-AREA-ENDING @import@
//
//import javax.persistence.*;
//import java.io.Serializable;
//import java.util.Date;
//import java.util.HashSet;
//import java.util.Set;
//
///**
// *
// * <p>Title: TMtSupplier</p>
// *
// * <p>Description: Domain Object describing a TMtSupplier entity</p>
// *
// */
//@Entity (name="SrmSupplier")
//@Table (name="t_srm_supplier")
//
//public class SrmSupplier1 implements Serializable {
//
//    @Id @Column(name="id" )
//    @GeneratedValue(strategy = GenerationType.IDENTITY)
//    private Integer id;
//
//    @Column(name="name"  , length=100 , nullable=true , unique=false)
//    private String name;
//
//    @Column(name="full_name"  , length=255 , nullable=true , unique=false)
//    private String fullName;
//
//    @Column(name="code_name"  , length=255 , nullable=true , unique=false)
//    private String codeName;
//
//    @Column(name="contact"  , length=100 , nullable=true , unique=false)
//    private String contact;
//
//    @Column(name="mobile"  , length=100 , nullable=true , unique=false)
//    private String mobile;
//
//    @Column(name="telephone"  , length=100 , nullable=true , unique=false)
//    private String telephone;
//
//    @Column(name="fax"  , length=100 , nullable=true , unique=false)
//    private String fax;
//
//    @Column(name="email"  , length=100 , nullable=true , unique=false)
//    private String email;
//
//    @Column(name="address"  , length=100 , nullable=true , unique=false)
//    private String address;
//
//    @Column(name="payment_type"  , length=1 , nullable=true , unique=false)
//    private String paymentType;
//
//    @Column(name="payment_method"  , length=1 , nullable=true , unique=false)
//    private String paymentMethod;
//
//    @Column(name="bank_code"  , length=100 , nullable=true , unique=false)
//    private String bankCode;
//
//    @Column(name="bank_name"  , length=100 , nullable=true , unique=false)
//    private String bankName;
//
//    @Column(name="bank_no"  , length=100 , nullable=true , unique=false)
//    private String bankNo;
//
//    @Column(name="keywords"  , length=100 , nullable=true , unique=false)
//    private String keywords;
//
//    @Column(name="creator"   , nullable=true , unique=false)
//    private Integer creator;
//
//    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
//    private String createName;
//
//    @Column(name="create_date"   , nullable=true , unique=false)
//    private Date createDate;
//
//    @Column(name="updator"   , nullable=true , unique=false)
//    private Integer updator;
//
//    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
//    private String updateName;
//
//    @Column(name="update_date"   , nullable=true , unique=false)
//    private Date updateDate;
//
//    @Column(name="oid", nullable=true , unique=false)
//    private Integer oid;
//
//    @OneToMany (targetEntity=MtSupplierMaterial.class, fetch=FetchType.EAGER, mappedBy="supplier")//, cascade=CascadeType.ALL)
//    private Set <MtSupplierMaterial> mtSupplierMaterialHashSet = new HashSet<MtSupplierMaterial>();
//
//
//    @OneToMany (targetEntity= SrmSupplierContact.class, fetch=FetchType.EAGER, mappedBy="supplier")//, cascade=CascadeType.ALL)
//    private Set <SrmSupplierContact> mtSupplierContactHashSet = new HashSet<SrmSupplierContact>();
//
//    @Transient
//    MtSupplierMaterial supplierMaterial;
//
//    public Integer getId() {
//        return id;
//    }
//
//    public void setId(Integer id) {
//        this.id = id;
//    }
//
//    public String getName() {
//        return name;
//    }
//
//    public void setName(String name) {
//        this.name = name;
//    }
//
//    public String getFullName() {
//        return fullName;
//    }
//
//    public void setFullName(String fullName) {
//        this.fullName = fullName;
//    }
//
//    public String getContact() {
//        return contact;
//    }
//
//    public void setContact(String contact) {
//        this.contact = contact;
//    }
//
//    public String getMobile() {
//        return mobile;
//    }
//
//    public void setMobile(String mobile) {
//        this.mobile = mobile;
//    }
//
//    public String getTelephone() {
//        return telephone;
//    }
//
//    public void setTelephone(String telephone) {
//        this.telephone = telephone;
//    }
//
//    public String getFax() {
//        return fax;
//    }
//
//    public void setFax(String fax) {
//        this.fax = fax;
//    }
//
//    public String getEmail() {
//        return email;
//    }
//
//    public void setEmail(String email) {
//        this.email = email;
//    }
//
//    public String getAddress() {
//        return address;
//    }
//
//    public void setAddress(String address) {
//        this.address = address;
//    }
//
//    public String getPaymentType() {
//        return paymentType;
//    }
//
//    public void setPaymentType(String paymentType) {
//        this.paymentType = paymentType;
//    }
//
//    public String getPaymentMethod() {
//        return paymentMethod;
//    }
//
//    public void setPaymentMethod(String paymentMethod) {
//        this.paymentMethod = paymentMethod;
//    }
//
//    public String getBankCode() {
//        return bankCode;
//    }
//
//    public void setBankCode(String bankCode) {
//        this.bankCode = bankCode;
//    }
//
//    public String getBankName() {
//        return bankName;
//    }
//
//    public void setBankName(String bankName) {
//        this.bankName = bankName;
//    }
//
//    public String getBankNo() {
//        return bankNo;
//    }
//
//    public void setBankNo(String bankNo) {
//        this.bankNo = bankNo;
//    }
//
//    public String getKeywords() {
//        return keywords;
//    }
//
//    public void setKeywords(String keywords) {
//        this.keywords = keywords;
//    }
//
//    public Integer getCreator() {
//        return creator;
//    }
//
//    public void setCreator(Integer creator) {
//        this.creator = creator;
//    }
//
//    public String getCreateName() {
//        return createName;
//    }
//
//    public void setCreateName(String createName) {
//        this.createName = createName;
//    }
//
//    public Date getCreateDate() {
//        return createDate;
//    }
//
//    public void setCreateDate(Date createDate) {
//        this.createDate = createDate;
//    }
//
//    public Integer getUpdator() {
//        return updator;
//    }
//
//    public void setUpdator(Integer updator) {
//        this.updator = updator;
//    }
//
//    public String getUpdateName() {
//        return updateName;
//    }
//
//    public void setUpdateName(String updateName) {
//        this.updateName = updateName;
//    }
//
//
//    public Set<MtSupplierMaterial> getMtSupplierMaterialHashSet() {
//        return mtSupplierMaterialHashSet;
//    }
//
//    public void setMtSupplierMaterialHashSet(Set<MtSupplierMaterial> mtSupplierMaterialHashSet) {
//        this.mtSupplierMaterialHashSet = mtSupplierMaterialHashSet;
//    }
//
//    public Date getUpdateDate() {
//        return updateDate;
//    }
//
//    public void setUpdateDate(Date updateDate) {
//        this.updateDate = updateDate;
//    }
//
//    public Integer getOid() {
//        return oid;
//    }
//
//    public void setOid(Integer oid) {
//        this.oid = oid;
//    }
//
//    public Set<SrmSupplierContact> getMtSupplierContactHashSet() {
//        return mtSupplierContactHashSet;
//    }
//
//    public void setMtSupplierContactHashSet(Set<SrmSupplierContact> mtSupplierContactHashSet) {
//        this.mtSupplierContactHashSet = mtSupplierContactHashSet;
//    }
//
//    public String getCodeName() {
//        return codeName;
//    }
//
//    public void setCodeName(String codeName) {
//        this.codeName = codeName;
//    }
//
//    public MtSupplierMaterial getSupplierMaterial() {
//        return supplierMaterial;
//    }
//
//    public void setSupplierMaterial(MtSupplierMaterial supplierMaterial) {
//        this.supplierMaterial = supplierMaterial;
//    }
//}
