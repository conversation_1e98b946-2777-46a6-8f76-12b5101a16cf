package cn.sphd.miners.modules.mpWeChatRes.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.ec.service.EcService;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

/**
 * 电商sku生成器表
 * <AUTHOR>
 * @date 2023年03月28日 09:03
 **/
@Entity
@Table(name = "t_wx_resource_valid_period")
public class WxResourceValidPeriod extends BaseEntity {
    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;//`id` INTEGER NOT NULL AUTO_INCREMENT COMMENT '主键id',
    @Column(name = "order_detail_id")
    private Long orderDetailId;//`order_detail_id` BIGINT DEFAULT NULL COMMENT '订单明细主键',//退货需要
    @Column(name = "acc_id")
    private Long accId;//`acc_id` BIGINT DEFAULT NULL COMMENT '账号id',
    @Column(name = "user_id")
    private Integer userId;//`user_id` INTEGER DEFAULT NULL COMMENT '用户id',
    @Column(name = "member_id")
    private Long memberId;//`member_id` BIGINT DEFAULT NULL COMMENT '第三方平台用户id',
    @Column(name = "wx_resource_id")
    private Integer wxResourceId;//`wx_resource_id` INTEGER DEFAULT NULL COMMENT '微信_资源信息表id',
    @Column(name = "ctype_expires")
    private Integer ctypeExpires;//`ctype_expires` INTEGER DEFAULT NULL COMMENT '期限',
    @Column(name = "ctype_id")
    private Short ctypeId;//`ctype_id` SMALLINT DEFAULT NULL COMMENT '期限单位（ctype_id）',
    @Column(name = "valid_from")
    private Date validFrom;//`valid_from` DATETIME(3) DEFAULT NULL COMMENT '有效期开始时间',
    @Column(name = "valid_thru")
    private Date validThru;//`valid_thru` DATETIME(3) DEFAULT NULL COMMENT '有效期截止时间',
    @Column
    private Byte status;//`status` TINYINT DEFAULT NULL COMMENT '卡状态1有效2已退',
    @JsonIgnore @JSONField(serialize = false)
    @Column(name = "create_time")
    @CreationTimestamp
    private Date createTime;// `create_time` DATETIME(3) DEFAULT NULL COMMENT '创建时间',
    @JsonIgnore @JSONField(serialize = false)
    @Column(name = "update_time")
    @UpdateTimestamp
    private Date updateTime;//`update_time` DATETIME(3) DEFAULT NULL COMMENT '更新时间',

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getOrderDetailId() {
        return orderDetailId;
    }

    public void setOrderDetailId(Long orderDetailId) {
        this.orderDetailId = orderDetailId;
    }

    public Long getAccId() {
        return accId;
    }

    public void setAccId(Long accId) {
        this.accId = accId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Integer getWxResourceId() {
        return wxResourceId;
    }

    public void setWxResourceId(Integer wxResourceId) {
        this.wxResourceId = wxResourceId;
    }

    public Integer getCtypeExpires() {
        return ctypeExpires;
    }

    public void setCtypeExpires(Integer ctypeExpires) {
        this.ctypeExpires = ctypeExpires;
    }

    public Short getCtypeId() {
        return ctypeId;
    }

    public void setCtypeId(Short ctypeId) {
        this.ctypeId = ctypeId;
    }

    public Date getValidFrom() {
        return validFrom;
    }

    public void setValidFrom(Date validFrom) {
        this.validFrom = validFrom;
    }

    public Date getValidThru() {
        return validThru;
    }

    public void setValidThru(Date validThru) {
        this.validThru = validThru;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public WxResourceValidPeriod() {
    }

    public WxResourceValidPeriod(Long orderDetailId, AuthInfoDto authInfo, WxResource resource, Integer ctypeExpires, EcService.CTpye ctype, Date validFrom, Date validThru, Byte status) {
        this.orderDetailId = orderDetailId;
        this.accId = authInfo.getAccId();
        this.userId = authInfo.getUserId();
        this.memberId = authInfo.getMemberId();
        this.wxResourceId = resource.getId();
        this.ctypeExpires = ctypeExpires;
        this.ctypeId = ctype.getIndex();
        this.validFrom = validFrom;
        this.validThru = validThru;
        this.status = status;
    }
}