<!--
demo子组件
----desc：该子组件不是全局子组件，此处只为说明子组件的开发规范
----pages（用到的页面）：['views/demoPage.vue', 'views/demoPage.vue']
-->
<template>
 <div class="TyDemo">
   <div class="ttl">
     <span v-html="firstName"></span> /
     <span v-html="secondName"></span>
   </div>
 </div>
</template>
<script>

export default {
  name: "TyDemo",
  data() {
    return {

    };
  },
  props:{
    firstName: { 'type': String, 'default':'' },
    secondName: { 'type': String, 'default':'' },

  },
  watch: {
    list(newValue) {
      this.setPageNum()
    },
  },
  mounted() {
    this.setPageNum()
  },
  methods: {
    setPageNum(){

    },
    showPage(pageInfo){
      this.pageClickFun(pageInfo)
    }

  },
}
</script>

<style lang="scss" scoped>
.TyDemo{
  .ttl{
    background: linear-gradient(to right, #ff7e5f, #feb47b);
    color: #fff;
    padding: 0 20px;
    line-height: 50px;
    margin-bottom: 10px;
    font-weight: bold;
  }
}
</style>