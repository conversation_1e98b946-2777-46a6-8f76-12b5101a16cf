package cn.sphd.miners.modules.dailyAffairs.service.impl;

import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.modules.dailyAffairs.dao.SalaryTreasureWithdrawalDao;
import cn.sphd.miners.modules.dailyAffairs.entity.SalaryTreasureWithdrawal;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.dailyAffairs.service.SalaryTreasureWithdrawalService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.finance.dao.FinanceAccountDao;
import cn.sphd.miners.modules.finance.dao.FinancePaymentDao;
import cn.sphd.miners.modules.finance.dao.FinancePaymentHistoryDao;
import cn.sphd.miners.modules.finance.entity.*;
import cn.sphd.miners.modules.finance.service.AccountService;
import cn.sphd.miners.modules.personal.dao.ApprovalProcessDao;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.dto.UserHonePageDto;
import cn.sphd.miners.modules.system.entity.ApprovalItem;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.entity.UserPopedom;
import cn.sphd.miners.modules.system.service.RoleService;
import cn.sphd.miners.modules.system.service.UserPopedomService;
import cn.sphd.miners.modules.system.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by 19614 on 2020/9/23.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class SalaryTreasureWithdrawalServiceImpl implements SalaryTreasureWithdrawalService {

    @Autowired
    SalaryTreasureWithdrawalDao salaryTreasureWithdrawalDao;
    @Autowired
    UserDao userDao;
    @Autowired
    ApprovalProcessDao approvalProcessDao;
    @Autowired
    FinancePaymentDao financePaymentDao;
    @Autowired
    FinanceAccountDao financeAccountDao;
    @Autowired
    FinancePaymentHistoryDao financePaymentHistoryDao;

//    @Autowired
//    SalaryAction salaryAction;

    @Autowired
    RoleService roleService;
    @Autowired
    SWMessageService swMessageService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    UserPopedomService userPopedomService;
    @Autowired
    UserService userService;
    @Autowired
    AccountService accountService;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;

    @Override
    public Map<String, Object> addSalaryTreasure(Integer oid, Integer userId,Integer salaryId, BigDecimal amount) {
        Map<String,Object> map = new HashMap<>();
        if (oid!=null && userId!=null && salaryId!=null && amount!=null) {
            User user = userDao.get(userId);
            SalaryTreasureWithdrawal salaryTreasureWithdrawal = new SalaryTreasureWithdrawal();
            salaryTreasureWithdrawal.setOrg(oid);
            salaryTreasureWithdrawal.setUser(user.getUserID()); //申请人
            salaryTreasureWithdrawal.setBusinessNo(salaryId);  //刘洪涛薪资宝那块的id
            salaryTreasureWithdrawal.setAmount(amount);  //申请金额
            salaryTreasureWithdrawal.setPlanAmount(amount);  //计划金额
            salaryTreasureWithdrawal.setFactAmount(amount);  //实际金额
            salaryTreasureWithdrawal.setStatus("2");  //状态:0-撤回,1-待申请,2-申请(待处理),3-申请通过,4-申请驳回,5-付款方式确认 6-付款复核通过,7-付款方式修改,8-付款修改复核通过,9-付款完成
            salaryTreasureWithdrawal.setCreator(user.getUserID());
            salaryTreasureWithdrawal.setCreateName(user.getUserName());
            salaryTreasureWithdrawal.setCreateDate(new Date());
            salaryTreasureWithdrawalDao.save(salaryTreasureWithdrawal);

            ApprovalItem approvalItem = roleService.getCurrentItem(user.getOid(), "paymentApproval");  //查询当前使用的付款审批(暂时默认一级)
            ApprovalProcess process = new ApprovalProcess();
            process.setApproveStatus("1");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
            process.setLevel(1);
            process.setToUser(approvalItem.getApprovalFlowHashSet().get(0).getToUserId());  //审批人id
            process.setToUserName(approvalItem.getApprovalFlowHashSet().get(0).getToUser());  //审批人总称
            process.setUserName(approvalItem.getApprovalFlowHashSet().get(0).getUserName()); //审批人名称
            process.setCreateDate(new Date());
            process.setOrg(user.getOid());
            process.setFromUser(userId);
            process.setAskName(user.getUserName());
            process.setBusiness(salaryTreasureWithdrawal.getId());
            process.setBusinessType(24);  //薪资宝转出的付款审批
            approvalProcessDao.save(process);

            //申请人待处理数据+1
            this.salaryTreasureRejectSend(0,1,salaryTreasureWithdrawal, userId, "/salaryTreasureApply",null,null,"salaryOutApply");

            //付款审批人待处理+1
            this.salaryTreasureRejectSend(1,1,salaryTreasureWithdrawal, process.getToUser(), "/salaryTreasurePaymentHandle","有一条申请待审批",user.getUserName()+"申请转出薪资宝"+amount+"元","salaryOutApproval");

            map.put("status",1);
            map.put("content","操作成功");
        }else {
            map.put("status",0);
            map.put("content","操作失败");
        }
        return map;
    }

    //报销 撤销 长连接推送   pass 通道  superscript 角标

    private void salaryTreasureRejectSend(int loginNum,int operate,SalaryTreasureWithdrawal salaryTreasureWithdrawal,Integer toUserId,String pass, String title, String content, String code){
        System.out.println("薪资宝转出申请推送开始:"+new Date());
        System.out.println("薪资宝转出申请id："+salaryTreasureWithdrawal.getId()+" userId: "+toUserId);
        HashMap<String,Object> hashMap=new HashMap<>();
        hashMap.put("salaryTreasureWithdrawal", salaryTreasureWithdrawal);
        User user = userDao.get(toUserId);  // 推送人
        swMessageService.rejectSend(loginNum,operate,hashMap,toUserId.toString(),pass,title,content,user,code);
        System.out.println("薪资宝转出申请推送结束:"+new Date());
    }

    @Override    //type 1-顺序  2-倒叙
    public List<SalaryTreasureWithdrawal> getSalaryApply(Integer userId, String approveStatus, Date beginDate, Date endDate,Integer type) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from SalaryTreasureWithdrawal where";
        if ("10".equals(approveStatus)){
            hql+=" status in ('2','3','5','6','7','8')";
        }else {
            hql+=" status=:status";
            map.put("status",approveStatus);
        }
        if (userId!=null){
            hql+=" and creator=:creator";
            map.put("creator",userId);
        }
        if (beginDate!=null&&endDate!=null){
            hql+=" and createDate between :beginDate and :endDate";
            map.put("beginDate",beginDate);
            map.put("endDate",endDate);
        }
        if (1==type) {
            hql += " order by createDate";
        }else if (2==type){
            hql += " order by createDate desc";
        }
        List<SalaryTreasureWithdrawal> salaryTreasureWithdrawals = salaryTreasureWithdrawalDao.getListByHQLWithNamedParams(hql,map);
        return salaryTreasureWithdrawals;
    }

    @Override   //type 1-顺序 2-倒叙
    public List<SalaryTreasureWithdrawal> getSalaryApproval(Integer oid, Integer toUser, Integer fromUser,String mid,String approveStatusTreasure, String approveStatus,Integer businessType, Date beginDate, Date endDate,Integer type) {
        HashMap<String,Object> params = new HashMap<>();
        StringBuffer hql = new StringBuffer("from SalaryTreasureWithdrawal");
        if (!MyStrings.nulltoempty(approveStatusTreasure).isEmpty()){
            if ("10".equals(approveStatusTreasure)){  //传10的时候，判断不等于9的
                hql.append(" where status!='9' and id in (select business from ApprovalProcess");
            }else {
                hql.append(" where status=:status and id in (select business from ApprovalProcess");
                params.put("status", approveStatusTreasure);
            }

        }else {
            hql.append(" where id in (select business from ApprovalProcess");
        }

        StringBuffer where = new StringBuffer();
        if (oid!=null) {
            where.append(" and org=:org");
            params.put("org", oid);
        }
        if (toUser!=null) {
            where.append(" and toUser=:toUser");
            params.put("toUser", toUser);
        }
        if (fromUser!=null){
            where.append(" and fromUser=:fromUser");
            params.put("fromUser",fromUser);
        }
        if (!MyStrings.nulltoempty(mid).isEmpty()){
            where.append(" and toMid=:toMid");
            params.put("toMid",mid);
        }
        if (!MyStrings.nulltoempty(approveStatus).isEmpty()){
            where.append(" and approveStatus=:approveStatus");
            params.put("approveStatus",approveStatus);
        }
        if (businessType!=null){
            where.append(" and businessType=:businessType");
            params.put("businessType",businessType);
        }
        if (beginDate!=null&&endDate!=null){
            where.append(" and createDate between :beginDate and :endDate");
            params.put("beginDate",beginDate);
            params.put("endDate",endDate);
        }
        if (where.length() > 0) {
            hql.append(" where ").append(where.substring(4));
            hql.append(")");
        }
//        if (1==type){
//            hql.append(" order by createDate");
//        }else if (2==type){
//            hql.append(" order by createDate desc");
//        }

        List<SalaryTreasureWithdrawal> salaryTreasureWithdrawals = salaryTreasureWithdrawalDao.getListByHQLWithNamedParams(hql.toString(),params);
        return salaryTreasureWithdrawals;
    }

    private FinancePayment getPaymentByBusinessNo(Integer salaryTreasureId,String businessType){
        Map<String,Object> map = new HashMap<>();
        String hql = "from FinancePayment where business=:business and businessType=:businessType";
        map.put("business",salaryTreasureId);
        map.put("businessType",businessType);
        return (FinancePayment) financePaymentDao.getByHQLWithNamedParams(hql,map);
    }

    @Override
    public Map<String,Object> getSalaryTreasureDetail(Integer salaryTreasureId) {
        Map<String,Object> map = new HashMap<>();
        SalaryTreasureWithdrawal salaryTreasureWithdrawal = salaryTreasureWithdrawalDao.get(salaryTreasureId);  //基本信息
        FinancePayment financePayment = getPaymentByBusinessNo(salaryTreasureId,"treasure");
        FinanceAccount financeAccount = new FinanceAccount();
        if (financePayment!=null){
            financeAccount = financeAccountDao.get(financePayment.getAccountId());
        }
        List<ApprovalProcess> approvalProcess = approvalProcessService.getApprovalProcessAll(null,salaryTreasureId,null,24,25,26,27,28);  //没写完呢，现在需要查询审批流程
        map.put("salaryTreasureWithdrawal",salaryTreasureWithdrawal); //基本信息
        map.put("financePayment",financePayment);  //支付信息
        map.put("financeAccount",financeAccount);  //账户信息
        map.put("approvalProcess",approvalProcess);  //审批流程
        return map;
    }

    @Override
    public Map<String, Object> getSalaryPaymentApproval(Integer userId,Integer approvalProcessId) {
        Map<String,Object> map = new HashMap<>();
        if (userId!=null&&approvalProcessId!=null){
            User user = userDao.get(userId);
            ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);
            if (approvalProcess!=null && approvalProcess.getBusinessType()==24) {
                if ("2".equals(approvalProcess.getApproveStatus())) {
                    map.put("status", 2);
                    map.put("content", "不可重复操作");
                } else {
                    SalaryTreasureWithdrawal salaryTreasureWithdrawal = salaryTreasureWithdrawalDao.get(approvalProcess.getBusiness());

                    salaryTreasureWithdrawal.setStatus("3");//0-撤回,1-待申请,2-申请,3-申请通过(付款审批),4-申请驳回,5-付款方式确认 6-付款复核通过,7-付款方式修改,8-付款修改复核通过,9-付款完成
                    salaryTreasureWithdrawalDao.update(salaryTreasureWithdrawal);

                    approvalProcess.setApproveStatus("2");  // 批准
                    approvalProcess.setHandleTime(new Date());
                    approvalProcess.setUserName(user.getUserName());
                    approvalProcessDao.update(approvalProcess);

                    //财务-可付款的流程（未确定付款方式时）
                    ApprovalProcess process = new ApprovalProcess();
                    process.setApproveStatus("1");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
                    process.setLevel(approvalProcess.getLevel());
                    process.setToUserName("财务处理者");
                    process.setToMid("at");  //薪资宝的账户列表
                    process.setReimburse(approvalProcess.getReimburse());
                    process.setCreateDate(new Date());
                    process.setOrg(user.getOid());
                    process.setUserName("财务处理者");
                    process.setFromUser(salaryTreasureWithdrawal.getUser());
                    process.setHandleTime(new Date());
                    process.setBusiness(salaryTreasureWithdrawal.getId());
                    process.setBusinessType(25);  //薪资宝转出的可付款(财务的付款方式确定)
                    approvalProcessDao.save(process);

                    List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "at");  //财务为权限审批
                    for (UserPopedom u : userPopedomList) {
                        //出纳审批人-薪资宝转出可付款+1
                        this.salaryTreasureRejectSend(1, 1, salaryTreasureWithdrawal, u.getUserId(), "/salaryTreasurePayable", "有一条申请待审批", salaryTreasureWithdrawal.getCreateName() + "付款申请可付款", "cashierSalaryOutApproval");
                    }

                    //付款审批人待处理-1
                    this.salaryTreasureRejectSend(-1, -1, salaryTreasureWithdrawal, userId, "/salaryTreasurePaymentHandle", null, null, "salaryOutApproval");
                    //付款审批人已批准加数据+1
                    this.salaryTreasureRejectSend(0, 1, salaryTreasureWithdrawal, userId, "/salaryTreasurePaymentApproval", null, null, "salaryOutApproval");

                    map.put("status", 1);
                    map.put("content", "操作成功");
                }
            }else {
                map.put("status",0);
                map.put("content","操作失败");
            }
        }else {
            map.put("status",0);
            map.put("content","操作失败");
        }
        return map;
    }

    @Override
    public Map<String, Object> treasurePayableApproval(Integer userId, Integer approvalProcessId, String method, Date planDate, Date factDate, Integer accountId,String summary) throws Exception {
        Map<String,Object> map = new HashMap<>();
        if (userId!=null && approvalProcessId!=null && !MyStrings.nulltoempty(method).isEmpty()){
            User user = userDao.get(userId);
            ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);
            if (approvalProcess!=null && approvalProcess.getBusinessType()==25) {
                if ("2".equals(approvalProcess.getApproveStatus())) {
                    map.put("status", 2);//已批准 ，不与重复批准
                    map.put("content", "不可重复操作");
                } else {
                    SalaryTreasureWithdrawal salaryTreasureWithdrawal = salaryTreasureWithdrawalDao.get(approvalProcess.getBusiness());
                    FinanceAccount financeAccount = new FinanceAccount();
                    if ("1".equals(method)){  //现金的
                        financeAccount = accountService.getFinanceAccountByOidAndType(user.getOid(), 1);//  现金/备用金
                    }else {
                        financeAccount = financeAccountDao.get(accountId);  //获取账户信息
                    }
                    //账户减去金额后的余额
                    BigDecimal balance = financeAccount.getBalance().subtract(salaryTreasureWithdrawal.getAmount());
                    if (balance.compareTo(new BigDecimal(0)) < 0) {
                        map.put("status", 3);
                        if ("1".equals(method)) {
                            map.put("content", "系统中现金余额不足，请确认");
                        }else {
                            map.put("content", "系统中该账户的余额不足，请确认。");
                        }
                    } else {
                        salaryTreasureWithdrawal.setStatus("5");//0-撤回,1-待申请,2-申请,3-申请通过(付款审批),4-申请驳回,5-付款方式确认 6-付款复核通过,7-付款方式修改,8-付款修改复核通过,9-付款完成
                        salaryTreasureWithdrawal.setPlanDate(planDate);
                        salaryTreasureWithdrawal.setPlanPayMethod(method);
                        salaryTreasureWithdrawal.setFactPayMethod(method);
                        salaryTreasureWithdrawalDao.update(salaryTreasureWithdrawal);

                        //新增上的可付款方式
                        this.addFinancePayment(salaryTreasureWithdrawal.getId(), salaryTreasureWithdrawal.getAmount(), user, method, planDate, factDate, financeAccount.getId());

//                    approvalProcess.setApproveStatus("2");  // 批准
//                    approvalProcess.setHandleTime(new Date());
//                    approvalProcess.setUserName(user.getUserName());
//                    approvalProcessDao.update(approvalProcess);

                        if ("5".equals(method)) {  //银行转账
                            approvalProcess.setApproveStatus("2");
                            approvalProcess.setHandleTime(new Date());
                            approvalProcess.setUserName(user.getUserName());
                            approvalProcess.setToUser(user.getUserID());
                            approvalProcessDao.update(approvalProcess);

                            //待复核的流程
                            User user1 = userService.getUserByRoleCode(user.getOid(), "finance");  //查询有付款复核权限的审批人员(有权限管理-权限设置的财务)
                            ApprovalProcess process = new ApprovalProcess();
                            process.setApproveStatus("1");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
                            process.setLevel(1);
                            process.setToUser(user1.getUserID());  //审批人id
                            process.setToUserName(approvalProcess.getToUserName());  //审批人总称
                            process.setUserName(user1.getUserName()); //审批人名称
                            process.setCreateDate(new Date());
                            process.setOrg(user.getOid());
                            process.setFromUser(salaryTreasureWithdrawal.getCreator());
                            process.setBusiness(salaryTreasureWithdrawal.getId());
                            process.setBusinessType(26);  //薪资宝的待复核
                            approvalProcessDao.save(process);  //结束

                            //付款复核的审批人+1
                            this.salaryTreasureRejectSend(1, 1, salaryTreasureWithdrawal, userId, "/salaryTreasureReviewHandle", "有一条申请待审批", salaryTreasureWithdrawal.getCreateName() + "付款申请等待复核", "reviewSalaryOutApproval");

                            List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "at");  //财务为权限审批
                            for (UserPopedom u : userPopedomList) {
                                //财务可付款-1
                                this.salaryTreasureRejectSend(-1, -1, salaryTreasureWithdrawal, u.getUserId(), "/salaryTreasurePayable", null, null, "cashierSalaryOutApproval");

                                //财务待复核角标不加，数据+1
                                this.salaryTreasureRejectSend(0, 1, salaryTreasureWithdrawal, u.getUserId(), "/salaryTreasureReview", null, null, "cashierSalaryOutApproval");
                            }
                            map.put("status", 1);
                            map.put("content", "操作成功");
                        } else if ("1".equals(method)) {  //直接现金结束的
                            if (MyStrings.nulltoempty(summary).isEmpty() || factDate==null){
                                map.put("status", 4);
                                map.put("content", "实际付款日期或者摘要不能为空");
                            }else {
                                map = this.accountIncome(salaryTreasureWithdrawal, approvalProcess, financeAccount.getId(), method, user, factDate, summary, 4);
                            }
                        }
                    }
                }
            }else {
                map.put("status",0);
                map.put("content","操作失败");
            }
        }else {
            map.put("status",0);
            map.put("content","操作失败");
        }
        return map;
    }

    private FinancePayment addFinancePayment(Integer business,BigDecimal amount,User user,String method,Date planDate,Date factDate,Integer accountId){
        //新增上的可付款方式
        FinancePayment financePayment = getPaymentByBusinessNo(business,"treasure");
        if (financePayment==null){
            financePayment = new FinancePayment();
        }
        financePayment.setOrg(user.getOid());
        financePayment.setBusinessType("treasure");
        financePayment.setBusiness(business);
        financePayment.setBusinessNo(business);
        financePayment.setAmount(amount);
        financePayment.setMethod(method);
        financePayment.setAccountId(accountId);
        if (planDate != null) {
            financePayment.setPlanDate(planDate);
        }
        financePayment.setPlanAmount(amount);
        financePayment.setPlanMethod(method);
        if (factDate != null) {
            financePayment.setFactDate(factDate);
        }
        financePayment.setFactAmount(amount);
        financePayment.setFactMethod(method);
        financePayment.setStatus("5");
        financePayment.setCreator(user.getUserID());
        financePayment.setCreateName(user.getUserName());
        financePayment.setCreateDate(new Date());
        financePayment.setOperation("1");
        financePayment.setPreviousId(0);
        financePayment.setVersionNo(0);
        financePaymentDao.saveOrUpdate(financePayment);

        this.addPaymentHistory(financePayment,user); //添加历史信息
        return financePayment;
    }

    @Override
    public FinancePaymentHistory getPaymentHistoryLast(Integer financePaymentId){  //查找最后一次的记录
        Map<String,Object> map = new HashMap<>();
        String hql = "from FinancePaymentHistory where financePayment=:financePayment order by createDate desc";
        map.put("financePayment",financePaymentId);
        return (FinancePaymentHistory) financePaymentHistoryDao.getByHQLWithNamedParams(hql,map);
    }

    @Override
    public List<FinancePaymentHistory> getPaymentHistoryByPaymentId(Integer financePaymentId){  //查找最后一次的记录
        Map<String,Object> map = new HashMap<>();
        String hql = "from FinancePaymentHistory where financePayment=:financePayment";
        map.put("financePayment",financePaymentId);
        return financePaymentHistoryDao.getListByHQLWithNamedParams(hql,map);
    }

    private FinancePaymentHistory addPaymentHistory(FinancePayment financePayment,User user){
//        FinancePaymentHistory financePaymentHistory = getPaymentHistoryLast(financePayment.getId());   //查找最后一次的记录
//        if (financePaymentHistory!=null){
        FinancePaymentHistory financePaymentHistory1 = new FinancePaymentHistory();
        BeanUtils.copyProperties(financePayment,financePaymentHistory1);
        financePaymentHistory1.setBusiness(financePayment.getBusiness());
        financePaymentHistory1.setBusinessNo(financePayment.getBusinessNo());
        financePaymentHistory1.setCreateDate(new Date());
        financePaymentHistory1.setCreator(user.getUserID());
        financePaymentHistory1.setCreateName(user.getUserName());
        financePaymentHistory1.setFinancePayment(financePayment.getId());
        financePaymentHistoryDao.save(financePaymentHistory1);
//            financePaymentHistory = financePaymentHistory1;  //将新增的数据返给
//        }
        return financePaymentHistory1;
    }

    @Override
    public Map<String, Object> treasureRevoke(Integer approvalProcessId) throws Exception {
        Map<String,Object> map = new HashMap<>();
        if (approvalProcessId!=null){
            ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);
            if (!"1".equals(approvalProcess.getApproveStatus())){
                map.put("status",2);
                map.put("content","已进行审批，不可撤销");
            }else {
                Integer toUser = approvalProcess.getToUser();  //审批人
                SalaryTreasureWithdrawal salaryTreasureWithdrawal = salaryTreasureWithdrawalDao.get(approvalProcess.getBusiness());
//                int state = salaryAction.salary_cancelTurnOut(salaryTreasureWithdrawal.getBusinessNo(),0);//参数接收转出ID，返回值：大于0成功，否则失败(因财务这边撤回后直接删除，所以财务这边的转出申请的id存0，占位符)
//                if (state>0){   摘除薪资宝的时候注释的代码
                    SalaryTreasureWithdrawal salaryTreasureWithdrawal1 = salaryTreasureWithdrawal;
                    List<ApprovalProcess> approvalProcesses = approvalProcessService.getApprovalProcessAll(null, salaryTreasureWithdrawal.getId(),null, 24);
                    for (ApprovalProcess a : approvalProcesses) {
                        approvalProcessDao.delete(a);
                    }

                    salaryTreasureWithdrawalDao.delete(salaryTreasureWithdrawal);

                    //申请人待处理数据-1
                    this.salaryTreasureRejectSend(0,-1,salaryTreasureWithdrawal1, salaryTreasureWithdrawal1.getCreator(), "/salaryTreasureApply",null,null,"salaryOutApply");

                    //付款审批人待处理-1
                    this.salaryTreasureRejectSend(-1,-1,salaryTreasureWithdrawal1, toUser, "/salaryTreasurePaymentHandle",null,null,"salaryOutApproval");

                    map.put("status",1);
                    map.put("content","操作成功");
//                }else {
//                    map.put("status",0);
//                    map.put("content","操作失败");
//                }
            }
        }else {
            map.put("status",0);
            map.put("content","操作失败");
        }
        return map;
    }

    @Override
    public Map<String, Object> getSalaryReviewApproval(Integer userId, Integer approvalProcessId) {
        Map<String,Object> map = new HashMap<>();
        if (userId!=null&&approvalProcessId!=null){
            User user = userDao.get(userId);
            ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);
            if (approvalProcess!=null && approvalProcess.getBusinessType()==26) {
                if ("2".equals(approvalProcess.getApproveStatus())) {
                    map.put("status", 2);
                    map.put("content", "不可重复操作");
                } else {
                    SalaryTreasureWithdrawal salaryTreasureWithdrawal = salaryTreasureWithdrawalDao.get(approvalProcess.getBusiness());

                    salaryTreasureWithdrawal.setStatus("6");//0-撤回,1-待申请,2-申请,3-申请通过(付款审批),4-申请驳回,5-付款方式确认 6-付款复核通过,7-付款方式修改,8-付款修改复核通过,9-付款完成
                    salaryTreasureWithdrawalDao.update(salaryTreasureWithdrawal);

                    approvalProcess.setApproveStatus("2");  // 批准
                    approvalProcess.setHandleTime(new Date());
                    approvalProcess.setUserName(user.getUserName());
                    approvalProcessDao.update(approvalProcess);

                    //财务-可付款的流程（未确定付款方式时）
                    ApprovalProcess process = new ApprovalProcess();
                    process.setApproveStatus("1");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
                    process.setLevel(approvalProcess.getLevel());
                    process.setToUserName("财务处理者");
                    process.setToMid("at");  //报销受理模块
                    process.setReimburse(approvalProcess.getReimburse());
                    process.setCreateDate(new Date());
                    process.setOrg(user.getOid());
                    process.setUserName("财务处理者");
                    process.setFromUser(salaryTreasureWithdrawal.getUser());
                    process.setHandleTime(new Date());
                    process.setBusiness(salaryTreasureWithdrawal.getId());
                    process.setBusinessType(27);  //薪资宝转出的待付款(财务的付款方式确定)
                    approvalProcessDao.save(process);

                    List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "at");  //财务为权限审批
                    for (UserPopedom u : userPopedomList) {
                        //出纳审批人-薪资宝转出待付款+1
                        this.salaryTreasureRejectSend(1, 1, salaryTreasureWithdrawal, u.getUserId(), "/salaryTreasurePaid", "有一条申请待审批", salaryTreasureWithdrawal.getCreateName() + "付款申请等待付款", "cashierSalaryOutApproval");

                        //出纳审批人-薪资宝转出待复核-1
                        this.salaryTreasureRejectSend(0, -1, salaryTreasureWithdrawal, u.getUserId(), "/salaryTreasureReview", null, null, "cashierSalaryOutApproval");
                    }

                    //付款复核-审批人待处理-1
                    this.salaryTreasureRejectSend(-1, -1, salaryTreasureWithdrawal, userId, "/salaryTreasureReviewHandle", null, null, "reviewSalaryOutApproval");
                    //付款复核-审批人已批准加数据+1
                    this.salaryTreasureRejectSend(0, 1, salaryTreasureWithdrawal, userId, "/salaryTreasureReviewApproval", null, null, "reviewSalaryOutApproval");

                    map.put("status", 1);
                    map.put("content", "操作成功");
                }
            }else {
                map.put("status", 0);
                map.put("content", "操作失败");
            }
        }else {
            map.put("status",0);
            map.put("content","操作失败");
        }
        return map;
    }

    @Override
    public Map<String, Object> getSalaryPaidApproval(Integer userId, Integer approvalProcessId, Date factDate, String summary) throws Exception {
        Map<String,Object> map = new HashMap<>();
        if (userId!=null&&approvalProcessId!=null){
            User user = userDao.get(userId);
            ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);
            if (approvalProcess!=null && approvalProcess.getBusinessType()==27) {
                if ("2".equals(approvalProcess.getApproveStatus())) {
                    map.put("status", 2);
                    map.put("content", "不可重复操作");
                } else {
                    SalaryTreasureWithdrawal salaryTreasureWithdrawal = salaryTreasureWithdrawalDao.get(approvalProcess.getBusiness());
                    FinancePayment financePayment = this.getPaymentByBusinessNo(salaryTreasureWithdrawal.getId(), "treasure");
                    if (MyStrings.nulltoempty(summary).isEmpty() || factDate==null){
                        map.put("status", 4);
                        map.put("content", "实际付款日期或者摘要不能为空");
                    }else {
                        map = this.accountIncome(salaryTreasureWithdrawal, approvalProcess, financePayment.getAccountId(), financePayment.getPlanMethod(), user, factDate, summary, 3);
                    }
                }
            }else {
                map.put("status",0);
                map.put("content","操作失败");
            }
        }else {
            map.put("status",0);
            map.put("content","操作失败");
        }
        return map;
    }

    //进财务帐的操作（现金和银行的）  type 1-待复核的修改 2-待付款的修改 3-待付款的最后确定 4-可付款直接现金结算
    private Map<String,Object> accountIncome(SalaryTreasureWithdrawal salaryTreasureWithdrawal,ApprovalProcess approvalProcess,Integer accountId,String method,User user,Date factDate,String summary,Integer type) throws Exception {
        Map<String,Object> map = new HashMap<>();
        FinanceAccount f = new FinanceAccount();
        f = financeAccountDao.get(accountId);  //银行转账的账户
        Double newBalance = f.getBalance().subtract(salaryTreasureWithdrawal.getAmount()).doubleValue(); //新的余额
        if (newBalance < 0) {
            map.put("status",3);//账户余额不足
            if ("1".equals(method)) {
                map.put("content", "系统中现金余额不足，请确认");
            }else {
                map.put("content", "系统中该账户的余额不足，请确认。");
            }
        } else if ("9".equals(salaryTreasureWithdrawal.getStatus()) || "2".equals(approvalProcess.getApproveStatus())) {
            map.put("status",2);//已批准 ，不与重复批准
            map.put("content","不可重复操作");
        } else {
            Integer approvalUserId = approvalProcess.getToUser();  //原本的审批人（修改时-推送角标用）

            approvalProcess.setApproveStatus("2");  // 批准
            approvalProcess.setHandleTime(new Date());
            approvalProcess.setUserName(user.getUserName());
            approvalProcess.setToUser(user.getUserID());
            approvalProcess.setBusinessType(27);  //薪资宝转出的待付款，状态完成后就是已完成的状态
            if (3!=type){
                approvalProcess.setCreateDate(new Date());
            }
            approvalProcessDao.update(approvalProcess);

            //生成财务明细
            AccountDetail accountDetail = new AccountDetail();
            accountDetail.setDebit(salaryTreasureWithdrawal.getAmount());//合计报销金额 录入支出
            accountDetail.setBalance(new BigDecimal(newBalance));
            accountDetail.setCreator(user.getUserID());
            accountDetail.setCreateName(user.getUserName());
            accountDetail.setCreateDate(new Date());
            accountDetail.setType("4");//1-初始金额冲减,2-转帐交易,3-收入,4-支出
            accountDetail.setOrg(user.getOrganization());
            accountDetail.setSummary(summary);
//            accountDetail.setPurpose(personnelReimburse.getPurpose());
//            accountDetail.setMemo(personnelReimburse.getMemo());
            accountDetail.setAuditorName(user.getUserName());
            accountDetail.setBusiness(salaryTreasureWithdrawal.getId());
            accountDetail.setBusinessDate(new Date());
            accountDetail.setBillAmount(salaryTreasureWithdrawal.getAmount());
            accountDetail.setModityStatus("2");//数据修改状态  null/1-未修改  2-已修改(数据修改已进行冲账后不能再修改一次、包括借款的收入与支出) 3-关闭账户后不可修改
            accountDetail.setMethod(method);//1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐
            accountDetail.setAccountantStatus("1");//会计数据状态  1-未选择  2-已选择

            //更新账户
            f.setDebit(f.getDebit().add(salaryTreasureWithdrawal.getAmount()));//总支出加上
            f.setBalance(new BigDecimal(newBalance));//在余额中减去
            financeAccountDao.update(f);

            //月结
            AccountPeriod yue = accountService.getAccountPeriodByMonth(f.getId(), new Date());
            yue.setDebit(yue.getDebit().add(salaryTreasureWithdrawal.getAmount()));
            yue.setBalance(yue.getBalance().subtract(salaryTreasureWithdrawal.getAmount()));
            accountService.updateAccountPeroid(yue);

            //日结
            AccountPeriod ri = accountService.getAccountPeriodByDay(f.getId(), new Date());
            ri.setDebit(ri.getDebit().add(salaryTreasureWithdrawal.getAmount()));
            ri.setBalance(ri.getBalance().subtract(salaryTreasureWithdrawal.getAmount()));
            accountService.updateAccountPeroid(ri);

            accountDetail.setFid(f.getId().toString());
            accountDetail.setAccountId(f);
            accountDetail.setAccount(ri.getId().toString());
            accountDetail.setSource("4");  //数据来源:1-手工录入(数据录入、以及报销等),2-业务自动生成(只包括借款) 3-销售回款(1.58版本) 4-薪资宝转出(2.68/2.69)
            accountService.saveAccountDetail(accountDetail);

            //转出表本身
            salaryTreasureWithdrawal.setStatus("9");//0-撤回,1-待申请,2-申请,3-申请通过(付款审批),4-申请驳回,5-付款方式确认 6-付款复核通过,7-付款方式修改,8-付款修改复核通过,9-付款完成
            salaryTreasureWithdrawal.setFactPayDate(factDate);
            salaryTreasureWithdrawal.setSummary(summary);  //摘要
            salaryTreasureWithdrawal.setAccountDetail(accountDetail.getId());
            salaryTreasureWithdrawal.setFactPayMethod(method);
            salaryTreasureWithdrawal.setPlanPayMethod(method);
            salaryTreasureWithdrawalDao.update(salaryTreasureWithdrawal);

            //支付表
            FinancePayment financePayment = getPaymentByBusinessNo(salaryTreasureWithdrawal.getId(),"treasure");
            financePayment.setFactMethod(method);
            financePayment.setPlanMethod(method);
            financePayment.setMethod(method);
            financePayment.setFactDate(factDate);
            financePayment.setAccountId(accountId);
            financePayment.setFactAccount(f.getAccount());
            financePayment.setStatus("9");
            financePayment.setSummary(summary);
            financePayment.setAccountDetail(accountDetail.getId());
            financePaymentDao.update(financePayment);

            //支付历史表
            FinancePaymentHistory financePaymentHistory = getPaymentHistoryLast(financePayment.getId());
            financePaymentHistory.setFactMethod(method);
            financePaymentHistory.setMethod(method);
            financePaymentHistory.setPlanMethod(method);
            financePaymentHistory.setFactDate(factDate);
            financePaymentHistory.setAccountId(accountId);
            financePaymentHistory.setFactAccount(f.getAccount());
            financePaymentHistory.setStatus("9");
            financePaymentHistory.setSummary(summary);
            financePaymentHistory.setAccountDetail(accountDetail.getId());
            financePaymentHistoryDao.update(financePaymentHistory);

            //刘洪涛薪资宝部分--参数接收转出ID和财务ID，返回值：大于0成功，否则失败【摘除薪资宝时注释】
//            int state = salaryAction.salary_completeTurnOut(salaryTreasureWithdrawal.getBusinessNo(),salaryTreasureWithdrawal.getId());

//            if (state>0) {

                String description = "您在" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(salaryTreasureWithdrawal.getCreateDate()) + "的申请转出" + salaryTreasureWithdrawal.getAmount() + "元已于" + new SimpleDateFormat("yyyy-MM-dd").format(factDate) + "付款完毕，请查收！";

                // 批准给申请人发消息
                userSuspendMsgService.saveUserSuspendMsg(1, description, "操作时间 " + user.getUserName() + " " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()), salaryTreasureWithdrawal.getCreator(), "salaryTreasureDetail", salaryTreasureWithdrawal.getId());  //给前端要查看详情的链接

                //无论那阶段审批通过，都需给历史审批人已批准中减数据
                // 25-可付款(如果在此用现金，那么直接使用type判断的推送) 27-待付款(在此不管是否进行过付款方式修改，审批完后均完成，那么直接使用type判断的推送)  28-付款方式修改(只是一个流程，在这里不产生推送)
                List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcessAll(null, salaryTreasureWithdrawal.getId(),null, 24, 26);
                for (ApprovalProcess appProcess : approvalProcessList) {
                    if (!appProcess.getId().equals(approvalProcess.getId())) {
                        //报销审批历史审批人已批准中减数据
                        if (24 == appProcess.getBusinessType() && "2".equals(approvalProcess.getApproveStatus())) {  //付款审批的已批准减数据
                            this.salaryTreasureRejectSend(0, -1, salaryTreasureWithdrawal, appProcess.getToUser(), "/salaryTreasurePaymentApproval", null, null, "salaryOutApproval");
                        } else if (26 == appProcess.getBusinessType() && "2".equals(approvalProcess.getApproveStatus())) {   //待复核
                            //付款复核-已批准
                            this.salaryTreasureRejectSend(0, -1, salaryTreasureWithdrawal, appProcess.getToUser(), "/salaryTreasureReviewApproval", null, null, "reviewSalaryOutApproval");
                        }
                    }
                }

                //type 1-待复核的修改 2-待付款的修改 3-待付款的最后确定 4-可付款直接现金结算
                if (1 == type) {
                    //付款复核-审批人待处理-1
                    this.salaryTreasureRejectSend(-1, -1, salaryTreasureWithdrawal, approvalUserId, "/salaryTreasureReviewHandle", null, null, "reviewSalaryOutApproval");
                } else if (2 == type || 3 == type) {
                    //出纳-薪资宝转出-待付款 角标数据均-1
                    List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "at");  //财务为权限审批
                    for (UserPopedom userPopedom:userPopedomList) {
                        this.salaryTreasureRejectSend(-1, -1, salaryTreasureWithdrawal, userPopedom.getUserId(), "/salaryTreasurePaid", null, null, "cashierSalaryOutApproval");
                    }
                } else if (4 == type) {
                    //财务可付款-1
                    List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "at");  //财务为权限审批
                    for (UserPopedom userPopedom:userPopedomList) {
                        this.salaryTreasureRejectSend(-1, -1, salaryTreasureWithdrawal, userPopedom.getUserId(), "/salaryTreasurePayable", null, null, "cashierSalaryOutApproval");
                    }
                }

                //申请人待处理-1
                this.salaryTreasureRejectSend(0, -1, salaryTreasureWithdrawal, salaryTreasureWithdrawal.getCreator(), "/salaryTreasureApply", null, null, "salaryOutApply");
                map.put("status",1);
                map.put("content","操作成功");
//            }else {
//                map.put("status",0);
//                map.put("content","操作失败");
//            }
        }
        return map;
    }

    @Override
    public List<UserHonePageDto> getTreasureApplyUserList(Integer toUserId, String userName, Integer businessType) {
        Map<String,Object> map = new HashMap<>();

        String hql="select new cn.sphd.miners.modules.system.dto.UserHonePageDto(userID,userName,departName,mobile,isDuty) from User where userID in(select fromUser from ApprovalProcess where approveStatus='2' and toUser=:userId";
        map.put("userId",toUserId);
        if (businessType!=null){
            hql+=" and businessType=:businessType )";
            map.put("businessType",businessType);
        }
        if (!StringUtils.isNotEmpty(userName)){
            hql+=" and userName like:userName";
            map.put("userName","%"+userName+"%");
        }
        hql+=" order by convert(userName, 'gbk') asc";
        List<UserHonePageDto> userHonePageDtoList=userDao.getListByHQLWithNamedParams(hql,map);
        return userHonePageDtoList;
    }

    @Override   // type 1-待复核的修改  2-待付款的修改
    public Map<String, Object> updateTreasureMethod(Integer userId, Integer approvalProcessId, String method, Date planDate, Date factDate, Integer accountId, Integer type, String summary) {
        Map<String,Object> map = new HashMap<String,Object>();
        if (userId!=null && approvalProcessId!=null && !MyStrings.nulltoempty(method).isEmpty()){
            User user = userDao.get(userId);
            ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);
            if (approvalProcess!=null && "2".equals(approvalProcess.getApproveStatus())){
                map.put("status",2);
                map.put("content", "不可重复操作");
            }else {
                FinanceAccount financeAccount = new FinanceAccount();
                if ("1".equals(method)){  //改为现金的
                    financeAccount = accountService.getFinanceAccountByOidAndType(user.getOid(), 1);//  现金/备用金
                    accountId = financeAccount.getId();
                }else {
                    financeAccount = accountService.getFinanceAccountById(accountId);
                }
                SalaryTreasureWithdrawal salaryTreasureWithdrawal = salaryTreasureWithdrawalDao.get(approvalProcess.getBusiness());
                FinancePayment financePayment = this.getPaymentByBusinessNo(salaryTreasureWithdrawal.getId(),"treasure");
                if (financePayment.getAccountId().equals(accountId)){
                    map.put("status",4);
                    map.put("content", "修改的账户与原账户相同");
                }else {
                    //账户减去金额后的余额
                    BigDecimal balance = financeAccount.getBalance().subtract(salaryTreasureWithdrawal.getAmount());
                    if (balance.compareTo(new BigDecimal(0)) < 0) {
                        map.put("status", 3);
                        if ("1".equals(method)) {
                            map.put("content", "系统中现金余额不足，请确认");
                        }else {
                            map.put("content", "系统中该账户的余额不足，请确认。");
                        }
                    }else {
                        financePayment.setAccountId(accountId);
                        financePayment.setMethod(method);
                        financePayment.setFactMethod(method);
                        financePayment.setPlanMethod(method);
                        financePayment.setSummary(summary);
                        financePayment.setFactDate(factDate);
                        financePayment.setPlanDate(planDate);
                        financePaymentDao.update(financePayment);



                        List<FinancePaymentHistory> financePaymentHistories = getPaymentHistoryByPaymentId(financePayment.getId());
                        if (financePaymentHistories.size() == 1) {
                            this.addPaymentHistory(financePayment, user);  //直接从历史里新增
                        } else if (financePaymentHistories.size() == 2) {
                            FinancePaymentHistory financePaymentHistory1 = financePaymentHistories.get(0);
                            FinancePaymentHistory financePaymentHistory2 = financePaymentHistories.get(1);

                            financePaymentHistory1.setAccountId(financePaymentHistory2.getAccountId());
                            financePaymentHistory1.setMethod(financePaymentHistory2.getMethod());
                            financePaymentHistory1.setFactMethod(financePaymentHistory2.getFactMethod());
                            financePaymentHistory1.setPlanMethod(financePaymentHistory2.getPlanMethod());
                            financePaymentHistory1.setPlanDate(financePaymentHistory2.getPlanDate());
                            financePaymentHistory1.setFactDate(financePaymentHistory2.getFactDate());
                            financePaymentHistory1.setSummary(financePaymentHistory2.getSummary());
                            financePaymentHistory1.setPurpose(financePaymentHistory2.getPurpose());
                            financePaymentHistory1.setCreateDate(financePaymentHistory2.getCreateDate());
                            financePaymentHistory1.setCreateName(financePaymentHistory2.getCreateName());
                            financePaymentHistory1.setCreator(financePaymentHistory2.getCreator());
                            financePaymentHistory1.setUpdateDate(new Date());
                            financePaymentHistory1.setUpdateName(user.getUserName());
                            financePaymentHistory1.setUpdator(userId);
                            financePaymentHistoryDao.update(financePaymentHistory1);

                            financePaymentHistory2.setAccountId(accountId);
                            financePaymentHistory2.setMethod(method);
                            financePaymentHistory2.setFactMethod(method);
                            financePaymentHistory2.setPlanMethod(method);
                            financePaymentHistory2.setPlanDate(planDate);
                            financePaymentHistory2.setFactDate(factDate);
                            financePaymentHistory2.setSummary(summary);
                            financePaymentHistory2.setCreateDate(new Date());
                            financePaymentHistory2.setCreateName(user.getUserName());
                            financePaymentHistory2.setCreator(userId);
                            financePaymentHistory2.setUpdateDate(new Date());
                            financePaymentHistory2.setUpdateName(user.getUserName());
                            financePaymentHistory2.setUpdator(userId);
                            financePaymentHistoryDao.update(financePaymentHistory2);
                        }

                        //付款方式修改的流程
                        ApprovalProcess process1 = new ApprovalProcess();
                        process1.setApproveStatus("2");// 1 - 提交 2-批准，3-驳回 4-待两讫 5-已报销 6-待接收 7-已接收 8-财务驳回
                        process1.setLevel(1);
                        process1.setToUser(user.getUserID());  //审批人id
                        process1.setToUserName(approvalProcess.getToUserName());  //审批人总称
                        process1.setUserName(user.getUserName()); //审批人名称
//                process1.setReimburse(personnelReimburse);
                        process1.setCreateDate(new Date());
                        process1.setOrg(user.getOid());
                        process1.setFromUser(salaryTreasureWithdrawal.getUser());
                        process1.setBusiness(salaryTreasureWithdrawal.getId());
                        process1.setBusinessType(28);
                        process1.setHandleTime(new Date());
                        approvalProcessDao.save(process1);  //结束

                        if ("1".equals(method)) {
                            try {   //直接改的现金  ---里面有返回执行的状态
                                if (MyStrings.nulltoempty(summary).isEmpty() || factDate==null){
                                    map.put("status", 4);
                                    map.put("content", "实际付款日期或者摘要不能为空");
                                }else {
                                    map = this.accountIncome(salaryTreasureWithdrawal, approvalProcess, accountId, method, user, factDate, summary, type);
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        } else if (2 == type) {  //待付款进行的修改，需要将原本的待付款流程改为待复核流程
                            //待复核的流程
                            User user1 = userService.getUserByRoleCode(user.getOid(), "finance");  //查询有付款复核权限的审批人员(有权限管理-权限设置的财务)
                            approvalProcess.setToUser(user1.getUserID());  //审批人id
                            approvalProcess.setUserName(user1.getUserName()); //审批人名称
                            approvalProcess.setCreateDate(new Date());
                            approvalProcess.setBusinessType(26);  //薪资宝的待复核
                            approvalProcessDao.save(approvalProcess);  //结束

                            List<UserPopedom> userPopedomList = userPopedomService.getAllUserPopedomByMid(user.getOid(), "at");  //财务为权限审批
                            for (UserPopedom u : userPopedomList) {
                                //出纳审批人-薪资宝转出待付款-1
                                this.salaryTreasureRejectSend(-1, -1, salaryTreasureWithdrawal, u.getUserId(), "/salaryTreasurePaid", null, null, "cashierSalaryOutApproval");

                                //出纳审批人-薪资宝转出待复核+1
                                this.salaryTreasureRejectSend(0, 1, salaryTreasureWithdrawal, u.getUserId(), "/salaryTreasureReview", null, null, "cashierSalaryOutApproval");
                            }

                            //付款复核-审批人待处理+1
                            this.salaryTreasureRejectSend(1, 1, salaryTreasureWithdrawal, userId, "/salaryTreasureReviewHandle", "有一条申请待审批", salaryTreasureWithdrawal.getCreateName() + "付款申请等待复核", "reviewSalaryOutApproval");

                        }
                        map.put("status", 1);
                        map.put("content", "修改成功");
                    }
                }
            }
        }else {
            map.put("status", 0);
            map.put("content", "修改失败");
        }
        return map;
    }

    @Override
    public Map<String, Object> getTreasureMethod(Integer salaryTreasureId) {
        Map<String,Object> map = new HashMap<>();
        FinancePayment financePayment = this.getPaymentByBusinessNo(salaryTreasureId,"treasure");
        List<FinancePaymentHistory> financePaymentHistories = getPaymentHistoryByPaymentId(financePayment.getId());
        if (financePaymentHistories.size()==2){  //如果是1的话，就只有一个不用展示
            FinanceAccount financeAccount = accountService.getFinanceAccountById(financePaymentHistories.get(0).getAccountId());
            map.put("financePayment",financePaymentHistories.get(0));
            map.put("financeAccount",financeAccount);
        }
        return map;
    }
}
