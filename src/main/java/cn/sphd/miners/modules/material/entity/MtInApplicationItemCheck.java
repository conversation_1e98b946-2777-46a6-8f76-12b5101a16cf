package cn.sphd.miners.modules.material.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2019-10-17 
 */

@Entity ( name ="MtInApplicationItemCheck" )
@Table ( name ="t_mt_in_application_item_check" )
public class MtInApplicationItemCheck  implements Serializable {

	private static final long serialVersionUID =  3233554323218493202L;

	@Id
	@Column(name = "id" )
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * 申请明细项ID
	 */
   	@Column(name = "application_item" )
	private Integer applicationItem;

	/**
	 * 申请人ID
	 */
   	@Column(name = "applicant" )
	private Integer applicant;

	/**
	 * 申请人姓名
	 */
   	@Column(name = "applicant_name" )
	private String applicantName;

	/**
	 * 仓库清点时间
	 */
   	@Column(name = "check_time" )
	private Date checkTime;


   	@Column(name = "check_name")
	private String checkName;

	/**
	 * 仓库清点数量
	 */
   	@Column(name = "check_quantity" )
	private BigDecimal checkQuantity;

	/**
	 * 备注
	 */
   	@Column(name = "memo" )
	private String memo;

	/**
	 * 描述
	 */
   	@Column(name = "description" )
	private String description;

	/**
	 * 创建人id,实际为采购入库人
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_date" )
	private Date createDate;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_date" )
	private Date updateDate;


	public static long getSerialVersionUID() {
		return serialVersionUID;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}


	public String getApplicantName() {
		return applicantName;
	}

	public void setApplicantName(String applicantName) {
		this.applicantName = applicantName;
	}

	public Date getCheckTime() {
		return checkTime;
	}

	public void setCheckTime(Date checkTime) {
		this.checkTime = checkTime;
	}

	public BigDecimal getCheckQuantity() {
		return checkQuantity;
	}

	public void setCheckQuantity(BigDecimal checkQuantity) {
		this.checkQuantity = checkQuantity;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getCheckName() {
		return checkName;
	}

	public void setCheckName(String checkName) {
		this.checkName = checkName;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Integer getCreator() {
		return creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getUpdator() {
		return updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Integer getApplicationItem() {
		return applicationItem;
	}

	public void setApplicationItem(Integer applicationItem) {
		this.applicationItem = applicationItem;
	}

	public Integer getApplicant() {
		return applicant;
	}

	public void setApplicant(Integer applicant) {
		this.applicant = applicant;
	}

}
