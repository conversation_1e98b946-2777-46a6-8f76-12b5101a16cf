.tip{ text-align:center; color:#ed5565 ;  }
#detail{ width:90%; min-width:800px;  }
.ordInfo_item{ float:left; width:170px;    }
.ordInfo_item>p{ color:#aaa;  }
.ordInfo_item>span{ font-size:13px; display:block; padding:5px 10px; text-overflow:ellipsis;overflow:hidden; background:#fff; border-radius:5px; margin-right:15px; height:30px;   }
#editGood{ width: 600px; margin-top:250px;   }
.orderItem{ width:240px; font-size: 14px; float: left; margin: 0 10px; position: relative;   }
.orderItemMiddle{width: 264px;}
#inputTip{ font-size:12px; color:#ed5565; }
.ty-optionCon{
    display: none;
    width: 182px;
    height: 150px;
    position: absolute;
    left: 0;
    z-index: 10;
    background-color: #fff;
    overflow-y: auto;
}
.ty-optionCon .ty-option{
    width: 100%;
    height: 30px;
    background-color: #f6f6f6;
    color: #666;
    padding-left: 8px;
    line-height: 30px;
}
/*.ty-optionCon .ty-option:first-child{ border:none; }*/
.ty-optionCon .ty-option:hover{
    background-color: #5d9beb;
    color: #fff;
}
.ty-optionCon .ty-optionActive{
    background-color: #75abf1;
    color: #fff;
}
.ty-inputSelect + .ty-optionCon{
    box-shadow: 0 3px 5px #d2e2f6;
    background-color: #fefefe;
}
#outSnCon{ border:1px solid #5d9beb ; border-top:none;  }
#addGoods{ margin-top:20px;  }

.orderItemTiny i{ color:#5d9beb ; }
#roleCon_update{ width:455px;   }
.orderItemTiny{
    cursor:pointer;
    display:inline-block;
    width: 100px;
    height: 30px;
    line-height: 30px;
    border-radius: 2px;
    margin-left: 5px;
    margin-top:8px;
    text-align: left;
}
.orderItemTiny i{
    font-size: 14px;
    margin-right:5px;
    width: 14px;
    height: 14px;
}
.ty-dropdownCon>div:nth-child(2){ padding:0 0 5px 15px ; border-bottom:1px solid #eaeaea;    }
.ty-dropdownCon>div:nth-child(3){ padding: 0 0 15px 15px  ;  }
.ty-dropdownCon>div:nth-child(4){ text-align:right; padding: 0 15px 15px;  }
.tipitemCon{ padding: 10px; display:inline-block;  }

.linkOrds {
    min-width: 182px;
    text-align: center
}

.xing {
    color: #ed5565;
}

.bounce_Fixed2 {
    position: fixed;
    z-index: 10002;
}

#linkOrd {
    width: 800px;
}

.ysGs {
    display: none;
}
#dateGoods{
    width:182px;
}
.scanGoods{
    margin-left: 20px;
}
.ordInfoElem > div{
    float: left;
    margin: 10px 20px 10px 0;
    width: 330px;
}
.ordInfoElem > div p{
    padding: 10px 20px 10px 4px;
    margin-top: 10px;
    background: #d8d8d8;
    min-height: 40px;
}
.ordInfoElem > div.ordInfo_l{
    width: 680px;
}
.ordInfoElem > div.noGap{
    margin-right: 0;
}
.gapR{
    margin-right: 20px;
}
.acceptDot{
    margin-bottom: 20px;
}
.middCon{
    padding: 10px 0;
    margin-bottom: 20px;
    border-top: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
}
.blueLinkBtn{
    color: #0070bf;
    cursor: pointer;
}
.redLinkBtn{
    color: #fd0001;
    cursor: pointer;
}
.rtSect{
    width: 330px;
}
#saleOrderReview .bonceCon{
    padding: 10px 30px;
}
.mainCon{display: none;}
.cell1{
    width: 80px;
}
.cell2{
    width: 120px;
}
.gapBt{
    padding-bottom: 50px;
}
.gapBtm{
    margin-bottom: 30px;
}
.ty-table.goReviewList thead td{
    border-color: #d7d7d7;
}
.ty-table.goReviewList td{
    padding: 0;
}
.itemCn{
    width:264px;
    font-size: 14px;
    float: left;
}
.itemCn>.orderItemTitle{
    margin: 8px 0;
    text-align: left;
}
.goodsInfo hr{
    border-color: #bebebe;
}
.goodsInfo{
    margin: auto;
    width: 570px;
}
.goodsInfo input[disabled]{
    background-color: #bebebe;
}
.itemGap {
    margin-left: 40px;
}
.grayBg{
    padding: 8px 0;
    background-color: #d8d8d8;
}
.checkBtn .fa{
    margin-right: 14px;
}
.addMore{
    font-weight: bold;
    padding: 20px 0;
}
.changeDot{
    margin-bottom: 12px;
}
.changeDot span{
    margin-right: 12px;
}
.narrowBody{
    margin: 0 auto;
    width: 220px;
}
.flexcon{
    display: flex;
    margin-top: 15px;
}
.flexcon>div{
    flex:1;
    margin: 0 20px;
}
.flexcon input[ disabled], .flexcon select[ disabled] {
    background: #d8d8d8 !important;
}
.flexcon .dbWrap {
    flex-shrink: 0
}
.flexcon .dbWrap input{
    width: 300px;
}
.red {
    color: #ff6431;
}
.orderItemMiddle_l{
    width: 540px;
    margin: 0 10px;
}
.orderItemMiddle_l select{
    width: 100%;
}
.orderItem>.orderItemTitle{
    color: #999;
    margin: 8px 0;
    text-align: left;
}
.orderItem>.orderItemCon{
    width: 240px;
    height: 32px;
    background-color: #fff;
    border-radius: 5px;
    line-height: 31px;
    text-align: center;
    color: #333;
}
.orderItem .ty-inputText, .orderItem .ty-inputSelect {
    width: 256px;
}
.ty-inputText:disabled, .ty-inputSelect:disabled {
    background: #eee !important;
}
#recentAmount[disabled] {
    background: #d8d8d8 !important;
}
#newGood .orderItemMiddle_l select {
    width: 100%;
}
.modifyPrice {
    color: #5a94ff;
    display: inline-block;
    padding: 0 10px;
    cursor: default;
}
.addressInfo{
    width: 120px;
}
.setList1{
    display: none;
}
#newGood .blueLinkBtn{
    margin-right: 10px;
}
.goReviewList tbody td button.ty-color-blue {
    border: none;
    font-size: 12px;
    font-family: 宋体;
    padding: 5px 15px 5px;
    border-radius: 3px;
    font-weight: bold;
    cursor: pointer;
    background-color: rgba(93, 156, 236, 0);
}
.goReviewList tbody td button.ty-color-blue:hover {
    color:#fff;
    background-color:#5d9cec;
    transition: all .2s
}
.goReviewList tbody td button.ty-color-blue[disabled] {
    color:#aaa;
}
.goReviewList tbody td button.ty-color-blue[disabled]:hover {
    color:#fff;
    background-color:#aaa;
    transition: all .2s
}
.bonceCon .goReviewList tbody td input[disabled],
.bonceCon .goReviewList tbody td select[disabled] {
    background-color: #bebebe;
}
















