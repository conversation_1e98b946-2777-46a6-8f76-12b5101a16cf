<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
  <div id="approvalSettingsSearchDetails" v-loading.fullscreen.lock="loading">
    <TY_NavTop :title="ttl" :isBackHome="isBackHome"></TY_NavTop>
    <div class="container approvalSettingsDetails">
      <div class="ttl1">{{tips}}</div>
      <div class="ttl2" v-if="info.approvalItem.code !== 'purchaseApprovalSettings'">
        <p>审批记录</p>
        <p>申请人：{{info.approvalItem.createName || ''}} {{info.approvalItem.createDate | formatDay}}</p>
        <div>
          <p v-if="item.approveStatus > 1" v-for='(item, index) in info.approvalProcessList' v-bind:key='index'>审批人：{{item.userName}} {{item.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</p>
          <p class="reason clr2" v-if="item.approveStatus == 3" v-for='(item, index) in info.approvalProcessList' v-bind:key='index'><span class="red">驳回理由：</span>{{item.reason}} </p>
        </div>
        <div class="clr2"></div>
      </div>
      <div class="ttl2" v-if="info.approvalItem.code === 'purchaseApprovalSettings'">
        <p>审批记录</p>
        <p>申请人：{{info.createName || ''}} {{info.createDate | formatDay}}</p>
        <div>
          <p>审批人：{{info.userName}} {{info.approvalDate | formatDay}}</p>
          <p class="reason clr2" v-if="info.approveStatus === '3'"><span class="red">驳回理由：</span>{{info.reason}} </p>
        </div>
        <div class="clr2"></div>
      </div>
      <div class="ttl3">
        <div :class="{active: seeType === 1}" @click="toggle(1)">修改后</div>
        <div :class="{active: seeType === 0}" @click="toggle(0)">修改前</div>
      </div>
      <div class="con">
        <!-- 修改后 -->
        <!--加班-->
        <div class="approveItem" v-if="seeType && info.approvalItem.code==='overTimeApply'" v-for='(item, index) in info.approvalFlows' v-bind:key='index'>
          <p>不高于{{item.amountCeiling}}小时的加班，需{{item.level | Upper}}级审批</p>
          <p class="right"> <span>{{item.level | Upper}}级审批者</span> <span>{{item.userName}}</span> <span>{{item.mobile}}</span></p>
          <div class="clr2"></div>
        </div>
        <!--付款-->
        <div class="whiteBox fup" v-if="seeType && info.approvalItem.code==='paymentApproval'" >
          <p>付款审批者：{{ info.approvalItem.status === 0 ? "无需审批" : `${ info.approvalFlows[0]["userName"]} ${info.approvalFlows[0]["mobile"]}`}}</p>
          <p>付款复核者：{{ info.approvalItemAudit.status === 0 ? "不需要" : `${ info.paymentAuditUser.userName } ${ info.paymentAuditUser.mobile }`}}</p>
          <div class="clr2"></div>
        </div>
        <!--请假-->
        <div class="approveItem" v-if="seeType && info.approvalItem.code==='leaveApply'" v-for='(item, index) in info.approvalFlows' v-bind:key='index'>
          <p v-if="index === 0 && item.amountCeiling === -1 || !item.amountCeiling">请假需{{item.level | Upper}}级审批</p>
          <p v-else-if="item.amountCeiling > 0 && item.amountCeiling <= 12">当日不高于{{item.amountCeiling}}小时的请假，需{{item.level | Upper}}级审批</p>
          <p v-else-if="item.amountCeiling > 12">不高于{{item.amountCeiling / 24}}天的请假，需{{item.level | Upper}}级审批</p>
          <p v-else>高于{{info.approvalFlows[index - 1].amountCeiling | changeDay}}的请假，需{{item.level | Upper}}级审批</p>
          <p class="right"> <span>{{item.level | Upper}}级审批者</span> <span>{{item.userName || item.toUser}}</span> <span>{{item.mobile}}</span></p>
          <div class="clr2"></div>
        </div>
        <!--报销-->
        <div class="approveItem" v-if="seeType && info.approvalItem.code==='reimburseApply'" v-for='(item, index) in info.approvalFlows' v-bind:key='index'>
          <p v-if="item.amountCeiling>0">不高于{{item.amountCeiling}}元的报销，需{{item.level | Upper}}级审批</p>
          <p v-if="item.amountCeiling == -1 && item.level == 1">报销需{{item.level | Upper}}级审批</p>
          <p v-if="item.amountCeiling == -1 && item.level != 1">高于{{info.top}}元的报销，需{{item.level | Upper}}级审批</p>
          <p class="right"> <span>{{item.level | Upper}}级审批者</span> <span>{{item.userName || item.toUser}}</span> <span>{{item.mobile}}</span></p>
          <div class="clr2"></div>
        </div>
        <!--来自客户订单的评审-->
        <div class="approveItem fatter" v-if="seeType && (info.approvalItem.code ==='ordersReview' || info.approvalItem.code ==='materialInCheck' || info.approvalItem.code ==='productInCheck' || info.approvalItem.code ==='productLogisticsCheck' || info.approvalItem.code ==='finishedProductCheck')">
          <p v-if="info.approvalItem.code ==='ordersReview' && info.approvalItem.status === 1">对于客户发来订单中的数量与交期，需要公司各部门评审</p>
          <p v-else-if="info.approvalItem.code ==='ordersReview' && info.approvalItem.status === 0">对于客户发来订单中的数量与交期，无需公司各部门评审</p>
          <p v-else-if="info.approvalItem.code ==='materialInCheck' && info.approvalItem.status === 0">对于采购来的材料，入库前不需要检验</p>
          <p v-else-if="info.approvalItem.code ==='materialInCheck' && info.approvalItem.status === 1">对于采购来的材料，入库前需要检验</p>
          <p v-else-if="info.approvalItem.code ==='productInCheck' && info.approvalItem.status === 0">生产出的货物入成品库前不需要检验</p>
          <p v-else-if="info.approvalItem.code ==='productInCheck' && info.approvalItem.status === 1">生产出的货物入成品库前需要检验</p>
          <p v-else-if="info.approvalItem.code ==='productLogisticsCheck' && info.approvalItem.status === 0">成品出库时物流人员的复核的模式：无需物流复核</p>
          <p v-else-if="info.approvalItem.code ==='productLogisticsCheck' && info.approvalItem.status === 1">成品出库时物流人员的复核的模式：需物流复核</p>
          <p v-else-if="info.approvalItem.code ==='finishedProductCheck' && info.approvalItem.status === 0">成品库的模式：不启用成品库</p>
          <p v-else-if="info.approvalItem.code ==='finishedProductCheck' && info.approvalItem.status === 1">成品库的模式：启用成品库</p>
        </div>
        <!--采购审批流程的修改审批-->
        <div class="approveItem fatter" v-if="seeType && info.approvalItem.code ==='purchaseApprovalSettings'">
          <p v-if="info.after.status === 0">采购无需审批</p>
          <p v-if="info.after.status === 1">采购需{{info.after.level | Upper}}级审批</p>
          <p>开始执行的时间: {{info.after.openDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</p>
          <div v-if="info.after.status === 1">
            <p v-for='(item, index) in info.after.userList' v-bind:key='index'>
              {{item.level | Upper}}级审批者： {{item.userName}}  {{item.mobile}}
            </p>
          </div>
        </div>
        <!--商品与产品名称代号关系的修改申请-->
        <div class="approveItem fatter" v-if="seeType && info.approvalItem.code ==='commodityProduct'">
          <div class="ptMode">
            <div>当前产品图号与名称的创建模式：</div>
            <div>与专属商品相关的产品为模式{{info.pdModelSettingsNew.dedicatedModel}}</div>
            <div>与通用型商品相关的产品为模式{{info.pdModelSettingsNew.generalModel}}</div>
            <div v-if="info.pdModelSettingsNew.generalModel !== 1">产品信息{{info.pdModelSettingsNew.reviseModel === 1 ? `由有权限创建商品的职工修改`:`由有产品操作权限的职工修改`}}</div>
          </div>
        </div>
        <!--仓库的模式修改审批-->
        <div class="approveItem fatter" v-if="seeType && info.approvalItem.code ==='stockModeChange'">
          <p v-if="info.approvalItem.status === 0">仓库的模式：非智能仓库</p>
          <p v-if="info.approvalItem.status === 1">仓库的模式：智能仓库</p>
        </div>
        <!--外购物料入库时，包装完好情况的检查者-->
        <div class="approveItem fatter" v-if="seeType && info.approvalItem.code ==='inStockCheckerSet'">
          <p v-if="info.approvalItem.status === 0">外购物料入库时，需由库管检查包装的完好情况 </p>
          <p v-if="info.approvalItem.status === 1">外购物料入库时，需由检验员检查包装的完好情况</p>
          <p v-if="info.approvalItem.status === 2">外购物料入库时，库管与检验员均需检查包装的完好情况</p>
          <p v-if="info.approvalItem.status === 3">外购物料入库时，不需检查包装的完好情况</p>
        </div>
        <!-- 修改前 -->
        <!--加班-->
        <div class="approveItem" v-if="!seeType && info.approvalItem.code==='overTimeApply'" v-for='(item, index) in info.approvalFlowsOld' v-bind:key='index'>
          <p>不高于{{item.amountCeiling}}小时的加班，需{{item.level | Upper}}级审批</p>
          <p class="right"> <span>{{item.level | Upper}}级审批者</span> <span>{{item.userName}}</span> <span>{{item.mobile}}</span></p>
          <div class="clr2"></div>
        </div>
        <!--付款-->
        <div class="whiteBox fup" v-if="!seeType && info.approvalItem.code==='paymentApproval'" >
          <p>付款审批者：{{ info.approvalItemOld.status === 0 ? "无需审批" : `${ info.approvalFlowsOld[0]["userName"]} ${ info.approvalFlowsOld[0]["mobile"]}`}}</p>
          <p>付款复核者：{{ info.approvalItemOldAudit.status === 0 ? "不需要" : `${ info.paymentAuditUser.userName } ${ info.paymentAuditUser.mobile }`}}</p>
          <div class="clr2"></div>
        </div>
        <!--报销-->
        <div class="approveItem" v-if="!seeType && info.approvalItem.code==='reimburseApply'" v-for='(item, index) in info.approvalFlowsOld' v-bind:key='index'>
          <p v-if="item.amountCeiling>0">不高于{{item.amountCeiling}}元的报销，需{{item.level | Upper}}级审批</p>
          <p v-if="item.amountCeiling == -1 && item.level == 1">报销需{{item.level | Upper}}级审批</p>
          <p v-if="item.amountCeiling == -1 && item.level != 1">高于{{info.topOld}}元的报销，需{{item.level | Upper}}级审批</p>
          <p class="right"> <span>{{item.level | Upper}}级审批者</span> <span>{{item.userName || item.toUser}}</span> <span>{{item.mobile}}</span></p>
          <div class="clr2"></div>
        </div>
        <!--请假-->
        <div class="approveItem" v-if="!seeType && info.approvalItem.code==='leaveApply'" v-for='(item, index) in info.approvalFlowsOld' v-bind:key='index'>
          <p v-if="index === 0 && item.amountCeiling === -1 || !item.amountCeiling">请假需{{item.level | Upper}}级审批</p>
          <p v-else-if="item.amountCeiling > 0 && item.amountCeiling <= 12">当日不高于{{item.amountCeiling}}小时的请假，需{{item.level | Upper}}级审批</p>
          <p v-else-if="item.amountCeiling > 12">不高于{{item.amountCeiling / 24}}天的请假，需{{item.level | Upper}}级审批</p>
          <p v-else>高于{{info.approvalFlows[index - 1].amountCeiling | changeDay}}的请假，需{{item.level | Upper}}级审批</p>
          <p class="right"> <span>{{item.level | Upper}}级审批者</span> <span>{{item.userName || item.toUser}}</span> <span>{{item.mobile}}</span></p>
          <div class="clr2"></div>
        </div>
        <!--来自客户订单的评审-->
        <div class="approveItem fatter" v-if="!seeType && (info.approvalItem.code ==='ordersReview' || info.approvalItem.code ==='materialInCheck' || info.approvalItem.code ==='productInCheck' || info.approvalItem.code ==='productLogisticsCheck' || info.approvalItem.code ==='finishedProductCheck')">
          <p v-if="info.approvalItemOld.code ==='ordersReview' && info.approvalItemOld.status === 1">对于客户发来订单中的数量与交期，需要公司各部门评审</p>
          <p v-else-if="info.approvalItemOld.code ==='ordersReview' && info.approvalItemOld.status === 0">对于客户发来订单中的数量与交期，无需公司各部门评审</p>
          <p v-else-if="info.approvalItemOld.code ==='materialInCheck' && info.approvalItemOld.status === 0">对于采购来的材料，入库前不需要检验</p>
          <p v-else-if="info.approvalItemOld.code ==='materialInCheck' && info.approvalItemOld.status === 1">对于采购来的材料，入库前需要检验</p>
          <p v-else-if="info.approvalItemOld.code ==='productInCheck' && info.approvalItemOld.status === 0">生产出的货物入成品库前不需要检验</p>
          <p v-else-if="info.approvalItemOld.code ==='productInCheck' && info.approvalItemOld.status === 1">生产出的货物入成品库前需要检验</p>
          <p v-else-if="info.approvalItemOld.code ==='productLogisticsCheck' && info.approvalItemOld.status === 0">成品出库时物流人员的复核的模式：无需物流复核</p>
          <p v-else-if="info.approvalItemOld.code ==='productLogisticsCheck' && info.approvalItemOld.status === 1">成品出库时物流人员的复核的模式：需物流复核</p>
          <p v-else-if="info.approvalItemOld.code ==='finishedProductCheck' && info.approvalItemOld.status === 0">成品库的模式：不启用成品库</p>
          <p v-else-if="info.approvalItemOld.code ==='finishedProductCheck' && info.approvalItemOld.status === 1">成品库的模式：启用成品库</p>
        </div>
        <!--采购审批流程的修改审批-->
        <div class="approveItem fatter" v-if="!seeType && info.approvalItem.code ==='purchaseApprovalSettings'">
          <p v-if="info.before.status === 0">采购无需审批</p>
          <p v-if="info.before.status === 1">采购需{{info.before.level | Upper}}级审批</p>
          <p>开始执行的时间: {{info.before.openDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</p>
          <div v-if="info.before.status === 1">
            <p v-for='(item, index) in info.before.userList' v-bind:key='index'>
              {{item.level | Upper}}级审批者： {{item.userName}}  {{item.mobile}}
            </p>
          </div>
        </div>
        <!--商品与产品名称代号关系的修改申请-->
        <div class="approveItem fatter" v-if="!seeType && info.approvalItem.code ==='commodityProduct'">
          <div class="ptMode">
            <div>当前产品图号与名称的创建模式：</div>
            <div>与专属商品相关的产品为模式{{info.pdModelSettingsOld.dedicatedModel}}</div>
            <div>与通用型商品相关的产品为模式{{info.pdModelSettingsOld.generalModel}}</div>
            <div v-if="info.pdModelSettingsOld.generalModel !== 1">产品信息{{info.pdModelSettingsOld.reviseModel === 1 ? `由有权限创建商品的职工修改`:`由有产品操作权限的职工修改`}}</div>
          </div>
        </div>
        <!--仓库的模式修改审批-->
        <div class="approveItem fatter" v-if="!seeType && info.approvalItem.code ==='stockModeChange'">
          <p v-if="info.approvalItemOld.status === 0">仓库的模式：非智能仓库</p>
          <p v-if="info.approvalItemOld.status === 1">仓库的模式：智能仓库</p>
        </div>
        <!--外购物料入库时，包装完好情况的检查者-->
        <div class="approveItem fatter" v-if="!seeType && info.approvalItem.code ==='inStockCheckerSet'">
          <p v-if="info.approvalItemOld.status === 0">外购物料入库时，需由库管检查包装的完好情况 </p>
          <p v-if="info.approvalItemOld.status === 1">外购物料入库时，需由检验员检查包装的完好情况</p>
          <p v-if="info.approvalItemOld.status === 2">外购物料入库时，库管与检验员均需检查包装的完好情况</p>
          <p v-if="info.approvalItemOld.status === 3">外购物料入库时，不需检查包装的完好情况</p>
        </div>
      </div>

    </div>
  </div>
</template>
<script>
export default {
  name: 'approvalSettingsSearchDetails',
  data () {
    return {
      info: {
        state: 0, // 最终的状态
        approvalItem: {},
        approvalFlows: [],
        approvalFlowsOld: [],
        approvalProcessList: [],
        before: {
          userList: []
        },
        after: {
          userList: []
        },
        userName: '',
        createName: '',
        createDate: '',
        approveStatus: 0,
        approvalId: 0
      },
      seeType: 1,
      ttl: '审批设置的修改申请',
      reason: '',
      showBtn: true,
      chargeType: 1,
      tipDialog1: false,
      tipDialog0: false,
      preColor: '#ddd',
      loading: true,
      nextColor: 'rgb(54, 198, 211)',
      approvalProcessId: this.$route.params.id,
      searchType: Number(this.$route.params.type),
      isBackHome: false,
      listeners: []
    }
  },
  filters: {
    Upper: function (num) {
      switch (Number(num)) {
        case 1: return '一'
        case 2: return '二'
        case 3: return '三'
        case 4: return '四'
        case 5: return '五'
        case 6: return '六'
        case 7: return '七'
        case 8: return '八'
        default:
      }
    },
    toSate: function (num) {
      switch (Number(num)) {
        case 1: return '有待审批'
        default: return ''
      }
    },
    toType: function (code) {
      switch (code) {
        case 'paymentApproval':return '新付款审批流程'
        case 'overTimeApply': return '新加班审批流程'
        case 'leaveApply': return '新请假审批流程'
        case 'reimburseApply': return '新报销审批流程'
        case 'ordersReview': return '客户订单评审新流程'
        case 'materialInCheck': return '外购材料检验新流程'
        case 'productInCheck': return '成品入库新流程'
        case 'commodityProduct': return '通用型商品与产品名称代号关系的修改申请'
        case 'purchaseApprovalSettings': return '新采购设置审批流程'
        case 'productLogisticsCheck': return '成品出库时物流人员复核的模式'
        case 'finishedProductCheck': return '成品库的模式'
        case 'inStockCheckerSet': return '外购物料入库时包装完好情况的检查者的修改申请'
        default: return ''
      }
    },
    changeDay: function (hour) {
      var str = ''
      if (hour >= 24) {
        str = hour / 24 + '天'
      } else {
        str = hour + '小时'
      }
      return <strong></strong>
    }
  },
  created: function () {
    let _this = this
    _this.applyDetail()
    console.log('详情searchType=', _this.searchType)
    if (_this.searchType === 2) {
      _this.ttl = '审批设置的修改审批'
    }
  },
  methods: {
    applyDetail: function () {
      let _this = this
      _this.axios.post('../../../popedom/updateItemDetail.do', {
        'json': '{ approvalProcessId: ' + _this.approvalProcessId + '}'
      }).then(function (response) {
        console.log('详情', response.data.data)
        _this.info = response.data.data
        _this.loading = false
        if (_this.info.approvalItem.code === 'reimburseApply') {
          let top = ''
          let topOld = ''
          _this.info.approvalFlowsOld.forEach(function (item) {
            if (item.amountCeiling !== -1) {
              topOld = item.amountCeiling
            }
          })
          _this.info.approvalFlows.forEach(function (item) {
            if (item.amountCeiling !== -1) {
              top = item.amountCeiling
            }
          })
          _this.info['top'] = top
          _this.info['topOld'] = topOld
        }
        if (_this.info.approvalItem.code==='purchaseApprovalSettings') {
          _this.info['state'] = _this.info.approveStatus
        } else {
          let process = _this.info.approvalProcessList
          _this.info['state'] = process[process.length - 1]['approveStatus']
        }

      }).catch(function (error) {
        console.log(error)
        _this.$message({
          type: 'error',
          message: '链接错误啦，请重试！'
        })
      })
    },
    toggle: function (num) {
      this.seeType = num
    },
  },
  computed: {
    tips() {
      let RootFilters = this.$root.$options.filters
      let filters = this.$options.filters
      let code = this.info.approvalItem.code
      let tip = ''
      if (this.info.approvalItem) {
        if ( code==='purchaseApprovalSettings') {
          tip = `采购审批流程已被修改，计划于${RootFilters.formatDay(this.info.after.openDate, 'YYYY/MM/DD')}生效，有待审批`
        } else if (code === 'stockModeChange') {
          tip = `现提请修改仓库的模式，修改计划生效的日期：${RootFilters.formatDay(this.info.approvalItem.openDate, 'YYYY/MM/DD')}\n请予审批！`
        } else {
          tip = `计划于${RootFilters.formatDay(this.info.approvalItem.openDate, 'YYYY/MM/DD')}生效的${filters.toType(this.info.approvalItem.code)}${filters.toSate(this.info.state)}`
        }
      }
      return tip
    }
  }
}
</script>
