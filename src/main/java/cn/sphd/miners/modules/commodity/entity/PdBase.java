package cn.sphd.miners.modules.commodity.entity;


import cn.sphd.miners.modules.material.entity.MtBase;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;


@Entity (name="PdBase")
@Table (name="t_pd_base")

public class PdBase implements Serializable {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "inner_sn", length = 50, nullable = true, unique = false)
    private String innerSn;   //内部图号

    @Column(name = "name", length = 100, nullable = true, unique = false)
    private String name;  //商品名称

    @Column(name = "code", length = 20, nullable = true, unique = false)
    private String code;   //条码

    @Column(name = "type", length = 1, nullable = true, unique = false)
    private String type; //1-产品,2-零组件

    @Column(name = "model", length = 100, nullable = true, unique = false)
    private String model;    //型号

    @Column(name = "specifications", length = 255, nullable = true, unique = false)
    private String specifications;  //规格

    @Column(name = "memo", length = 255, nullable = true, unique = false)
    private String memo;  //备注

    @Column(name = "enabled", length = 1, nullable = true, unique = false)
    private String enabled;  //是否在产:true-在产,false-停产

    @Column(name = "unit", length = 100, nullable = true, unique = false)
    private String unit;   //计量单位

    @Column(name = "net_weight", nullable = true, unique = false)
    private BigDecimal netWeight;  //净重(单重)

    @Column(name = "weight_unit", nullable = true, unique = false)
    private String weightUnit;  //重量单位:1-毫克(mg),2-克(g),3-千克(kg),4-吨(T)

    @Column(name = "minimumi_stock", nullable = true, unique = false)
    private BigDecimal minimumiStock;   //最小库存

    @Column(name = "current_stock", nullable = true, unique = false)
    private BigDecimal currentStock;        //bigint comment '当前库存',

    @Column(name = "available_stock", nullable = true, unique = false)
    private BigDecimal availableStock;      //bigint comment '可用库存',

    @Column(name = "initial_stock", nullable = true, unique = false)
    private BigDecimal initialStock;      //bigint comment '初始库存',

    @Column(name = "reserve_stock", nullable = true, unique = false)
    private BigDecimal reserveStock;        //bigint comment '每日预设库存',

//    @Column(name="composition"  , length=255 , nullable=true , unique=false)
//    private String composition;

    @Column(name = "creator", nullable = true, unique = false)
    private Integer creator;  //创建人id

    @Column(name = "create_name", length = 100, nullable = true, unique = false)
    private String createName;   //创建人

    @Column(name = "create_date", nullable = true, unique = false)
    private Date createDate;   //创建时间

    @Column(name = "updator", nullable = true, unique = false)
    private Integer updator;

    @Column(name = "update_name", length = 100, nullable = true, unique = false)
    private String updateName;

    @Column(name = "update_date", nullable = true, unique = false)
    private Date updateDate;

    @Column(name = "approve_Item", nullable = true, unique = false)
    private Integer approveItem;   //申批项目

    @Column(name = "approve_status", length = 1, nullable = true, unique = false)
    private String approveStatus;   //审批状态：0-未申请 ，1-申请提交，2-通过审核，3-否决审核

    @Column(name = "approve_level", nullable = true, unique = false)
    private Integer approveLevel;   //审批次级

    @Column(name = "auditor", nullable = true, unique = false)
    private Integer auditor;    //审批者ID

    @Column(name = "auditor_name", length = 100, nullable = true, unique = false)
    private String auditorName;   //审批者

    @Column(name = "audit_date", nullable = true, unique = false)
    private Date auditDate;  //审批日期

    @Column(name = "operation", length = 1, nullable = true, unique = false)
    private String operation;  //操作:1-增,2-删,3-修改基本信息,4-修改初始库存,5-修改最小库存,6-启停用(保留),7-修改来源,8-修改构成,

    @Column(name = "apply_memo", length = 255, nullable = true, unique = false)
    private String applyMemo;   //申请备注

    @Column(name = "approve_memo", length = 255, nullable = true, unique = false)
    private String approveMemo;   //审批备注

    @Column(name = "message_id", nullable = true, unique = false)
    private Integer messageId;   //消息ID

    @Column(name = "oid", nullable = true, unique = false)
    private Integer oid;//机构id

    @Column(name = "source", length = 1, nullable = true, unique = false)
    private String source;// 来源 1-外购,2-自制

    @Column(name = "composition", length = 1, nullable = true, unique = false)
    private String composition;//构成 1-成品,2-装配,3-多原料混合制造,4-单原料制造

    @Column(name = "stuff", length = 255, nullable = true, unique = false)
    private String stuff;//材料

    @Column(name = "assembly_quantity", nullable = true, unique = false)
    private Integer assemblyQuantity;//装配数量

    @Column(name = "version_no", nullable = true, unique = false)
    private Integer versionNo;//版本号

    @Column(name="process_dept"   , nullable=true , unique=false)
    private Integer processDept;
    @Column(name="process_dept_name"   , nullable=true , unique=false)
    private String processDeptName;
    @Column(name="phrase"   , nullable=true , unique=false)
    private String phrase;

    //构成path;
    @Basic
    @Column(name="path"  )
    private String path;
    //构成json
    @Basic
    @Column(name="json"   )
    private String json;


    @Basic
    @Column(name = "stage")
    private String stage;


    @Basic
    @Column(name = "state")
    private String state;


    @Basic
    @Column(name = "process")
    private String process;


    @Basic
    @Column(name = "split")
    private String split;

    @Basic
    @Column(name = "resource_num")
    private Integer resourceNum; //关联内部文件个数

    @Basic
    @Column(name = "init_time")
    private Date initTime; //初始化完成时间
    @Basic
    @Column(name = "initiator")
    private Integer initiator; //初始化完成人id
    @Basic
    @Column(name = "initiator_name")
    private String  initiatorName ; //初始化完成人姓名

    @JsonIgnore
    @JSONField(serialize = false)
    @OneToMany(targetEntity = PdBaseHistory.class, fetch = FetchType.LAZY, mappedBy = "product", cascade = CascadeType.REMOVE)
    private Set<PdBaseHistory> TPdBaseHistoryPdBaseViaProduct = new HashSet<PdBaseHistory>();

    @JsonIgnore @JSONField(serialize = false)
    @OneToMany(targetEntity = PdComposition.class, fetch = FetchType.LAZY, mappedBy = "product", cascade = CascadeType.REMOVE)
    private Set<PdComposition> TPdCompositionPdBaseViaProduct = new HashSet<PdComposition>();

    @JsonIgnore @JSONField(serialize = false)
    @OneToMany(targetEntity = PdMerchandise.class, fetch = FetchType.LAZY, mappedBy = "product")
    private Set<PdMerchandise> TPdMerchandisePdBaseViaProduct = new HashSet<PdMerchandise>();

    @JsonIgnore @JSONField(serialize = false)
    @OneToMany(targetEntity = PdProcess.class, fetch = FetchType.LAZY, mappedBy = "product")
//, cascade=CascadeType.ALL)
    private Set<PdProcess> TPdProcessPdBaseViaProduct = new HashSet<PdProcess>();

    @JsonIgnore @JSONField(serialize = false)
    @OneToMany(targetEntity = MtBase.class, fetch = FetchType.LAZY, mappedBy = "product", cascade = CascadeType.REMOVE)
//, cascade=CascadeType.ALL)
    private Set<MtBase> TMtBaseViaProduct = new HashSet<MtBase>();

    @JsonIgnore @JSONField(serialize = false)
    @OneToMany(targetEntity = PdComposition.class, fetch = FetchType.LAZY, mappedBy = "parent", cascade = CascadeType.REMOVE)
//, cascade=CascadeType.ALL)
    private Set<PdComposition> TPdCompositionViaParent = new HashSet<PdComposition>();

    @Transient
    private String stockPosition;//库存位

    @Transient
    private String categoryName;//分类名称
    @Transient
    private String orderNum;//dingdan num
    //添加权限  1商品 2产品
    @Transient
    private String addType;

    //图片—图片格式的产品图纸 信息
    @Transient
    private PdImage  cptzImage;

    //控制点序号图
    @Transient
    private PdImage xhImage;

    //资源文件
    @Transient
    private List resourceList;

    //1.119新增字段 unit_id
    @Column(name="unit_id"  , nullable=true , unique=false)
    private Integer unitId;

    /** 初始化状态:0-未设,1-进行中,2-已完成 */
    @Column(name = "init_state")
    private Integer initState;

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }


    public String getInnerSn() {
        return innerSn;
    }

    public void setInnerSn(String innerSn) {
        this.innerSn = innerSn;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }


    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }


    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }


    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }


    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }


    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }


    public BigDecimal getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }

    public BigDecimal getMinimumiStock() {
        return minimumiStock;
    }

    public void setMinimumiStock(BigDecimal minimumiStock) {
        this.minimumiStock = minimumiStock;
    }


    public String getComposition() {
        return composition;
    }

    public void setComposition(String composition) {
        this.composition = composition;
    }//MP-MANAGED-UPDATABLE-ENDING

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }


    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }


    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }


    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }


    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }


    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }


    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }


    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }


    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }


    public Set<PdBaseHistory> getTPdBaseHistoryPdBaseViaProduct() {
        if (TPdBaseHistoryPdBaseViaProduct == null) {
            TPdBaseHistoryPdBaseViaProduct = new HashSet<PdBaseHistory>();
        }
        return TPdBaseHistoryPdBaseViaProduct;
    }

    public void setTPdBaseHistoryPdBaseViaProduct(Set<PdBaseHistory> TPdBaseHistoryPdBaseViaProduct) {
        this.TPdBaseHistoryPdBaseViaProduct = TPdBaseHistoryPdBaseViaProduct;
    }

    public void addTPdBaseHistoryTPdBaseViaProduct(PdBaseHistory element) {
        getTPdBaseHistoryPdBaseViaProduct().add(element);
    }

    public Set<PdComposition> getTPdCompositionPdBaseViaProduct() {
        if (TPdCompositionPdBaseViaProduct == null) {
            TPdCompositionPdBaseViaProduct = new HashSet<PdComposition>();
        }
        return TPdCompositionPdBaseViaProduct;
    }

    public void setTPdCompositionPdBaseViaProduct(Set<PdComposition> TPdCompositionPdBaseViaProduct) {
        this.TPdCompositionPdBaseViaProduct = TPdCompositionPdBaseViaProduct;
    }

    public void addTPdCompositionTPdBaseViaProduct(PdComposition element) {
        getTPdCompositionPdBaseViaProduct().add(element);
    }


    public Set<PdMerchandise> getTPdMerchandisePdBaseViaProduct() {
        if (TPdMerchandisePdBaseViaProduct == null) {
            TPdMerchandisePdBaseViaProduct = new HashSet<PdMerchandise>();
        }
        return TPdMerchandisePdBaseViaProduct;
    }

    public void setTPdMerchandisePdBaseViaProduct(Set<PdMerchandise> TPdMerchandisePdBaseViaProduct) {
        this.TPdMerchandisePdBaseViaProduct = TPdMerchandisePdBaseViaProduct;
    }

    public void addTPdMerchandiseTPdBaseViaProduct(PdMerchandise element) {
        getTPdMerchandisePdBaseViaProduct().add(element);
    }

    public Set<PdProcess> getTPdProcessPdBaseViaProduct() {
        if (TPdProcessPdBaseViaProduct == null) {
            TPdProcessPdBaseViaProduct = new HashSet<PdProcess>();
        }
        return TPdProcessPdBaseViaProduct;
    }

    public void setTPdProcessPdBaseViaProduct(Set<PdProcess> TPdProcessPdBaseViaProduct) {
        this.TPdProcessPdBaseViaProduct = TPdProcessPdBaseViaProduct;
    }

    public void addTPdProcessTPdBaseViaProduct(PdProcess element) {
        getTPdProcessPdBaseViaProduct().add(element);
    }

    public Integer getOid() {
        return oid;
    }

    public void setOid(Integer oid) {
        this.oid = oid;
    }

    public Set<MtBase> getTMtBaseViaProduct() {
        return TMtBaseViaProduct;
    }

    public void setTMtBaseViaProduct(Set<MtBase> TMtBaseViaProduct) {
        this.TMtBaseViaProduct = TMtBaseViaProduct;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getStuff() {
        return stuff;
    }

    public void setStuff(String stuff) {
        this.stuff = stuff;
    }

    public Set<PdComposition> getTPdCompositionViaParent() {
        return TPdCompositionViaParent;
    }

    public void setTPdCompositionViaParent(Set<PdComposition> TPdCompositionViaParent) {
        this.TPdCompositionViaParent = TPdCompositionViaParent;
    }

    public Integer getAssemblyQuantity() {
        return assemblyQuantity;
    }

    public void setAssemblyQuantity(Integer assemblyQuantity) {
        this.assemblyQuantity = assemblyQuantity;
    }

    public String getStockPosition() {
        return stockPosition;
    }

    public void setStockPosition(String stockPosition) {
        this.stockPosition = stockPosition;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public BigDecimal getCurrentStock() {
        return currentStock;
    }

    public void setCurrentStock(BigDecimal currentStock) {
        this.currentStock = currentStock;
    }

    public BigDecimal getAvailableStock() {
        return availableStock;
    }

    public void setAvailableStock(BigDecimal availableStock) {
        this.availableStock = availableStock;
    }

    public BigDecimal getReserveStock() {
        return reserveStock;
    }

    public void setReserveStock(BigDecimal reserveStock) {
        this.reserveStock = reserveStock;
    }

    public BigDecimal getInitialStock() {
        return initialStock;
    }

    public void setInitialStock(BigDecimal initialStock) {
        this.initialStock = initialStock;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public String getWeightUnit() {
        return weightUnit;
    }

    public void setWeightUnit(String weightUnit) {
        this.weightUnit = weightUnit;
    }

    public Integer getProcessDept() {
        return processDept;
    }

    public void setProcessDept(Integer processDept) {
        this.processDept = processDept;
    }

    public String getProcessDeptName() {
        return processDeptName;
    }

    public void setProcessDeptName(String processDeptName) {
        this.processDeptName = processDeptName;
    }

    public String getPhrase() {
        return phrase;
    }

    public void setPhrase(String phrase) {
        this.phrase = phrase;
    }

    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    public String getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(String orderNum) {
        this.orderNum = orderNum;
    }

    public String getStage() {
        return stage;
    }

    public void setStage(String stage) {
        this.stage = stage;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getProcess() {
        return process;
    }

    public void setProcess(String process) {
        this.process = process;
    }

    public String getSplit() {
        return split;
    }

    public void setSplit(String split) {
        this.split = split;
    }

    public String getJson() {
        return json;
    }

    public void setJson(String json) {
        this.json = json;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getAddType() {
        return addType;
    }

    public void setAddType(String addType) {
        this.addType = addType;
    }

    public Integer getResourceNum() {
        return resourceNum;
    }

    public void setResourceNum(Integer resourceNum) {
        this.resourceNum = resourceNum;
    }


    public PdImage getCptzImage() {
        return cptzImage;
    }

    public void setCptzImage(PdImage cptzImage) {
        this.cptzImage = cptzImage;
    }

    public PdImage getXhImage() {
        return xhImage;
    }

    public void setXhImage(PdImage xhImage) {
        this.xhImage = xhImage;
    }

    public List getResourceList() {
        return resourceList;
    }

    public void setResourceList(List resourceList) {
        this.resourceList = resourceList;
    }

    public Integer getInitState() {
        return initState;
    }

    public void setInitState(Integer initState) {
        this.initState = initState;
    }

    public Date getInitTime() {
        return initTime;
    }

    public void setInitTime(Date initTime) {
        this.initTime = initTime;
    }

    public Integer getInitiator() {
        return initiator;
    }

    public void setInitiator(Integer initiator) {
        this.initiator = initiator;
    }

    public String getInitiatorName() {
        return initiatorName;
    }

    public void setInitiatorName(String initiatorName) {
        this.initiatorName = initiatorName;
    }
}
