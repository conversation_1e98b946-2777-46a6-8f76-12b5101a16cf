package cn.sphd.miners.modules.generalAffairs.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import cn.sphd.miners.modules.generalAffairs.dto.WorkOffBlockDto;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceService.AttendanceType;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceService.WorkdayType;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.tuple.Pair;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;
import org.springframework.util.Assert;

import javax.persistence.*;
import java.io.Serializable;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Created by Administrator on 2018/4/11.
 * 员工考勤明细表
 */
@Entity
@Table(name = "t_personnel_attendance_user_detail")
public class PersonnelAttendanceUserDetail extends BaseEntity implements Serializable {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "user")
    private Integer user;//用户id ，考虑到以前好多接口，暂时未加集联关系

    @Column(name = "org")
    private Integer org;//机构id ，考虑到以前好多接口，暂时未加集联关系

    @Column(name = "attendance_date")
    private Date attendanceDate;//考勤日期

    @Column(name = "workday_type")
    private Byte workdayType;//`workday_type` TINYINT DEFAULT NULL COMMENT '到岗类型: 类型:1-假/离岗,2-班/到岗'

    @Column(name = "type", length = 1)
    private String type;//类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班，9-其他，

    @Column(name = "state", length = 1)
    private String state;//（覆盖上下班时间的）状态:1-上班,2-下班,3-涵盖上下班时间

    @Column(name = "source", length = 1)
    private String source;//来源:1-审批,2-录入，3-系统默认旷工

    @Column(name = "begin_time")
    private Date beginTime;//上班时间

    @Column(name = "end_time")
    private Date endTime;//下班时间
    @JsonIgnore @JSONField(serialize = false)
    @Column(name = "default_begin_time")
    private Date defaultBeginTime;//`default_begin_time` DATETIME(3) DEFAULT NULL COMMENT '原始开始时间',
    @JsonIgnore @JSONField(serialize = false)
    @Column(name = "default_end_time")
    private Date defaultEndTime;//`default_end_time` DATETIME(3) DEFAULT NULL COMMENT '原始结束时间'
    @JsonIgnore @JSONField(serialize = false)
    @Column(name = "prev_id")
    private Integer prevId;//`prev_id` INTEGER DEFAULT NULL COMMENT '前节点id',
    @JsonIgnore @JSONField(serialize = false)
    @Column(name = "next_id")
    private Integer nextId;//`next_id` INTEGER DEFAULT NULL COMMENT '后节点id',
    @JsonIgnore @JSONField(serialize = false)
    @Column(name="scan_start_time")
    private Date scanStartTime; //`scan_start_time` DATETIME(3) COMMENT '考勤事件开始时间'
    @JsonIgnore @JSONField(serialize = false)
    @Column(name="scan_end_time")
    private Date scanEndTime; //`scan_end_time` DATETIME(3) COMMENT '考勤事件结束时间' AFTER `scan_start_time`
    @Column(name = "start_punch_id")
    private Integer startPunchId;//`start_punch_id` INTEGER DEFAULT NULL COMMENT '开始打卡id'
    @Column(name = "end_punch_id")
    private Integer endPunchId;//``end_punch_id` INTEGER DEFAULT NULL COMMENT '结束打卡id'
    @Column(name = "start_punch_time")
    private Date startPunchTime;//`start_punch_time` DATETIME(3) DEFAULT NULL COMMENT '开始打卡时间',
    @Column(name = "end_punch_time")
    private Date endPunchTime;//`end_punch_time` DATETIME(3) DEFAULT NULL COMMENT '结束打卡时间',
    @JsonIgnore @JSONField(serialize = false)
    @Column(name = "middle_break")
    private Boolean middleBreak;//`middle_break` BOOLEAN DEFAULT NULL COMMENT '是否中午考勤',
    @Column(name = "break_begin")
    private Date breakBegin;//`break_begin` DATETIME(3) DEFAULT NULL COMMENT '午休开始时间',
    @Column(name = "break_end")
    private Date breakEnd;//`break_end` DATETIME(3) DEFAULT NULL COMMENT '午休结束时间',
    @Column(name = "break_begin_state")
    private Byte breakBeginState;//`break_begin_state` TINYINT DEFAULT NULL COMMENT '午休开始状态',
    @Column(name = "break_end_state")
    private Byte breakEndState;//`break_end_state` TINYINT NULL DEFAULT NULL COMMENT '午休结束状态',
    @Column(name = "break_start_punch_id")
    private Integer breakStartPunchId;//`break_start_punch_id` INTEGER DEFAULT NULL COMMENT '午休开始打卡id',
    @Column(name = "break_end_punch_id")
    private Integer breakEndPunchId;//`break_end_punch_id` INTEGER DEFAULT NULL COMMENT '午休结束打卡id',

    @JsonIgnore @JSONField(serialize = false)
    @Column(name = "business_service")
    private String businessService;//`business_service` VARCHAR(255) DEFAULT NULL COMMENT '业务表Service',

    @Column(name = "business")
    private Integer business;//业务ID 加班和请假分别对应于t_personnel_overtime、t_personnel_leave的主键ID',

    @Column(name = "duration", scale = 1)
    private Double duration;//时长

    @Column(name = "reason")
    private String reason;//事由

    @JsonIgnore @JSONField(serialize = false)
    @Column(name = "business_type", length = 2)
    private String businessType;//业务类型:其中 请假类型:【1-事假(工作日加班),2-病假(周末假日加班),3-年假(法定节假日加班),4-调休,5-婚假,6-产假,7-陪产假,8-路途假,9-丧假】、
    // 加班类型【1-工作日加班(事假),2-周末假日加班(病假),3-法定节假日加班(年假)】

    @Column(name = "memo")
    private String memo;//备注

    @Column(name = "creator")
    private Integer creator;

    @Column(name = "create_name", length = 100)
    private String createName;

    @Column(name = "create_date")
    @CreationTimestamp
    private Date createDate;

    @Column(name = "updator")
    private Integer updator;

    @Column(name = "update_name", length = 100)
    private String updateName;

    @Column(name = "update_date")
    @UpdateTimestamp
    private Date updateDate;

    @Column(name = "leave_type")
    private Integer leaveType;  //请假类型id
    @Column(name = "leave_type_name")
    private String leaveTypeName;  //`leave_type_name` VARCHAR(100) DEFAULT NULL COMMENT '请假类型名称'

    @Column(name = "attendance")
    private Integer attendanceId;//考勤id

    //前节点
    @JsonIgnore
    @JSONField(serialize = false)
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "prev_id", referencedColumnName = "id", insertable = false, updatable = false)
    private PersonnelAttendanceUserDetail prev;

    //后节点
    @JsonIgnore
    @JSONField(serialize = false)
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "next_id", referencedColumnName = "id", insertable = false, updatable = false)
    private PersonnelAttendanceUserDetail next;

    //与员工考勤表
    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "attendance", referencedColumnName = "id", insertable = false, updatable = false)
    private PersonnelAttendanceUser personnelAttendanceUser;

    //考勤明细历史表
    @JsonIgnore
    @JSONField(serialize = false)
    @OneToMany(targetEntity = PersonnelAttendanceUserDetailHistory.class, fetch = FetchType.LAZY, mappedBy = "detail", cascade = CascadeType.REMOVE)
//, cascade=CascadeType.ALL)
    private Set<PersonnelAttendanceUserDetailHistory> personnelAttendanceUserDetailHistoryHashSet = new HashSet<PersonnelAttendanceUserDetailHistory>();

    @Transient
    private String userName;  //职工姓名

    @Transient
    private Boolean discard;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUser() {
        return user;
    }

    public void setUser(Integer user) {
        this.user = user;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Date getAttendanceDate() {
        return attendanceDate;
    }

    public void setAttendanceDate(Date attendanceDate) {
        this.attendanceDate = attendanceDate;
    }

    public Byte getWorkdayType() {
        return workdayType;
    }

    public void setWorkdayType(Byte workdayType) {
        this.workdayType = workdayType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getDefaultBeginTime() {
        return defaultBeginTime;
    }

    public void setDefaultBeginTime(Date defaultBeginTime) {
        this.defaultBeginTime = defaultBeginTime;
    }

    public Date getDefaultEndTime() {
        return defaultEndTime;
    }

    public void setDefaultEndTime(Date defaultEndTime) {
        this.defaultEndTime = defaultEndTime;
    }

    public Integer getPrevId() {
        return prevId;
    }

    public void setPrevId(Integer prevId) {
        this.prevId = prevId;
    }

    public Integer getNextId() {
        return nextId;
    }

    public void setNextId(Integer nextId) {
        this.nextId = nextId;
    }

    public Date getScanStartTime() {
        return scanStartTime;
    }

    public void setScanStartTime(Date scanStartTime) {
        this.scanStartTime = scanStartTime;
    }

    public Date getScanEndTime() {
        return scanEndTime;
    }

    public void setScanEndTime(Date scanEndTime) {
        this.scanEndTime = scanEndTime;
    }

    public Integer getStartPunchId() {
        return startPunchId;
    }

    public void setStartPunchId(Integer startPunchId) {
        this.startPunchId = startPunchId;
    }

    public Integer getEndPunchId() {
        return endPunchId;
    }

    public void setEndPunchId(Integer endPunchId) {
        this.endPunchId = endPunchId;
    }

    public Date getStartPunchTime() {
        return startPunchTime;
    }

    public void setStartPunchTime(Date startPunchTime) {
        this.startPunchTime = startPunchTime;
    }

    public Date getEndPunchTime() {
        return endPunchTime;
    }

    public void setEndPunchTime(Date endPunchTime) {
        this.endPunchTime = endPunchTime;
    }

    public Boolean getMiddleBreak() {
        return middleBreak;
    }

    public void setMiddleBreak(Boolean middleBreak) {
        this.middleBreak = middleBreak;
    }

    public Date getBreakBegin() {
        return breakBegin;
    }

    public void setBreakBegin(Date breakBegin) {
        this.breakBegin = breakBegin;
    }

    public Date getBreakEnd() {
        return breakEnd;
    }

    public void setBreakEnd(Date breakEnd) {
        this.breakEnd = breakEnd;
    }

    public Byte getBreakBeginState() {
        return breakBeginState;
    }

    public void setBreakBeginState(Byte breakBeginState) {
        this.breakBeginState = breakBeginState;
    }

    public Byte getBreakEndState() {
        return breakEndState;
    }

    public void setBreakEndState(Byte breakEndState) {
        this.breakEndState = breakEndState;
    }

    public Integer getBreakStartPunchId() {
        return breakStartPunchId;
    }

    public void setBreakStartPunchId(Integer breakStartPunchId) {
        this.breakStartPunchId = breakStartPunchId;
    }

    public Integer getBreakEndPunchId() {
        return breakEndPunchId;
    }

    public void setBreakEndPunchId(Integer breakEndPunchId) {
        this.breakEndPunchId = breakEndPunchId;
    }

    public String getBusinessService() {
        return businessService;
    }

    public void setBusinessService(String businessService) {
        this.businessService = businessService;
    }

    public Integer getBusiness() {
        return business;
    }

    public void setBusiness(Integer business) {
        this.business = business;
    }

    public Double getDuration() {
        return duration;
    }

    public void setDuration(Double duration) {
        this.duration = duration;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getLeaveType() {
        return leaveType;
    }

    public void setLeaveType(Integer leaveType) {
        this.leaveType = leaveType;
    }

    public String getLeaveTypeName() {
        return leaveTypeName;
    }

    public void setLeaveTypeName(String leaveTypeName) {
        this.leaveTypeName = leaveTypeName;
    }

    public Integer getAttendanceId() {
        return attendanceId;
    }

    public void setAttendanceId(Integer attendanceId) {
        this.attendanceId = attendanceId;
    }

    public PersonnelAttendanceUserDetail getPrev() {
        return prev;
    }

    public void setPrev(PersonnelAttendanceUserDetail prev) {
        this.prev = prev;
    }

    public PersonnelAttendanceUserDetail getNext() {
        return next;
    }

    public void setNext(PersonnelAttendanceUserDetail next) {
        this.next = next;
    }

    public PersonnelAttendanceUser getPersonnelAttendanceUser() {
        return personnelAttendanceUser;
    }

    public void setPersonnelAttendanceUser(PersonnelAttendanceUser personnelAttendanceUser) {
        this.personnelAttendanceUser = personnelAttendanceUser;
    }

    public Set<PersonnelAttendanceUserDetailHistory> getPersonnelAttendanceUserDetailHistoryHashSet() {
        return personnelAttendanceUserDetailHistoryHashSet;
    }

    public void setPersonnelAttendanceUserDetailHistoryHashSet(Set<PersonnelAttendanceUserDetailHistory> personnelAttendanceUserDetailHistoryHashSet) {
        this.personnelAttendanceUserDetailHistoryHashSet = personnelAttendanceUserDetailHistoryHashSet;
    }
    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Boolean getDiscard() {
        return discard;
    }

    public void setDiscard(Boolean discard) {
        this.discard = discard;
    }

    public void sliceDuration(Pair<Map<Long, Long>, Map<Long, Long>> durationSlices) {
        Map<Long, Long> newItems = new HashMap<>();
        Iterator<Map.Entry<Long, Long>> iterator = durationSlices.getRight().entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Long, Long> set = iterator.next();
            if (set.getKey() < endTime.getTime() || set.getValue() > beginTime.getTime()) {//工时时间片段与实际请假时间相交
                if (set.getKey() >= beginTime.getTime() && set.getValue() <= endTime.getTime()) {//工时片段被包含
                    durationSlices.getLeft().put(set.getKey(), set.getValue());//添加时长片
                    iterator.remove();//删除工时片
//                    removeKeys.add(set.getKey());//删除工时片
                } else if (set.getKey() >= beginTime.getTime()) {//工时片段左侧相同或被包含，右侧超出请假时间
                    durationSlices.getLeft().put(set.getKey(), endTime.getTime());//添加时长片段
                    newItems.put(endTime.getTime(), set.getValue());//重新添加右侧超出请假时间的工时片段
                    iterator.remove();//删除原工时片
                } else if (set.getValue() <= endTime.getTime()) {//工时片段右侧相同或被包含，左侧超出请假时间
                    durationSlices.getLeft().put(beginTime.getTime(), set.getValue());//添加右侧相交部分时长片段
                    set.setValue(endTime.getTime());//将工时片段右侧改为请假开始时间
                } else {//请假被时间片包含
                    durationSlices.getLeft().put(beginTime.getTime(), endTime.getTime());//添加请假时间为时长片段
                    newItems.put(endTime.getTime(), set.getValue());//新加请假结束到工时片段结束为新的工时片段
                    set.setValue(beginTime.getTime());//将工时片段右侧改为请假开始时间
                }
            }
        }
        ;
        durationSlices.getRight().putAll(newItems);
    }

    public PersonnelAttendanceUserDetail() {
    }

    public PersonnelAttendanceUserDetail(User[] operators) {
        this();
        switch (operators.length) {
            case 1:
                this.creator = this.updator = operators[0].getUserID();
                this.createName = this.updateName = operators[0].getUserName();
                break;
            case 2:
                this.creator = operators[0].getUserID();
                this.createName = operators[0].getUserName();
                this.updator = operators[1].getUserID();
                this.updateName = operators[1].getUserName();
                break;
            default:
                this.createName = this.updateName = "系统";
        }
    }

    public PersonnelAttendanceUserDetail(PersonnelAttendanceUser pau, WorkOffBlockDto wob, User... operators) {
        this(operators);
        Assert.isTrue(pau.getNoNeed() || pau.getBeginTime()!=null, "考勤日表工作日上班时间不能为空！\n" + JSON.toJSONString(pau));
        Assert.isTrue(pau.getNoNeed() || pau.getEndTime()!=null, "考勤日表工作日下班时间不能为空！\n" + JSON.toJSONString(pau));
        Assert.notNull(wob.getBegin(), "申请表开始时间不能为空！\n" + JSON.toJSONString(wob));
        Assert.notNull(wob.getEnd(), "申请表结束时间不能为空！\n" + JSON.toJSONString(wob));
        this.attendanceId = pau.getId();
        this.user = pau.getUser();
        this.org = pau.getOrg();
        this.attendanceDate = pau.getAttendanceDate();
        this.workdayType = wob.getType().getIndex();
        this.businessService = wob.getBusinessService();
        this.business = wob.getBusiness();
        this.type = wob.getAttendanceType().getName();
        if (!pau.getNoNeed() && wob.getType().noNeed()) {//班且离岗申请
            this.beginTime = new Date(Math.max(pau.getBeginTime().getTime(), wob.getBegin().getTime()));
            this.endTime = new Date(Math.min(pau.getEndTime().getTime(), wob.getEnd().getTime()));
            if (!pau.getEndTime().after(wob.getEnd()) && !pau.getEndTime().before(wob.getBegin())) {
                this.state = WorkdayType.DayOff.getState();
                pau.setEndState(wob.getAttendanceType().getName());
            }
            if (!pau.getBeginTime().after(wob.getEnd()) && !pau.getBeginTime().before(wob.getBegin())) {
                this.state = this.state == null ? WorkdayType.Workday.getState() : WorkdayType.Multiple.getState();//当请假全天的时候state='3'
                pau.setBeginState(wob.getAttendanceType().getName());
            }
//            this.setScanStartTime(this.beginTime);
//            this.setScanEndTime(this.endTime);
        } else {
            this.beginTime = wob.getBegin();
            this.endTime = wob.getEnd();
//            if(pau.getP1Before()!=null) {
//                this.setScanStartTime(new Date(this.beginTime.getTime()-pau.getP1Before()));
//            } else {
//                this.setScanStartTime(pau.getScanStartTime());
//            }
//            if(pau.getP1After()!=null) {
//                this.setScanEndTime(new Date(this.endTime.getTime()-pau.getP1After()));
//            } else {
//                this.setScanEndTime(pau.getScanEndTime());
//            }
        }
        this.middleBreak=Boolean.FALSE;//目前加班请假暂不支持午休打卡
        if(wob.getType().noNeed() && pau.getBreakBegin()!=null && pau.getBreakEnd()!=null && this.beginTime.before(pau.getBreakEnd()) && this.endTime.after(pau.getBreakBegin())) {
            //离岗请假扣除午休时间
            this.breakBegin = new Date(Math.max(pau.getBreakBegin().getTime(), this.beginTime.getTime()));
            this.breakEnd = new Date(Math.min(pau.getBreakEnd().getTime(), this.endTime.getTime()));
        }
        initScan(pau);
//        this.duration = (double) (this.getEndTime().getTime() - this.getBeginTime().getTime()) / TimeUnit.HOURS.toMillis(1);
        this.autoSetDuration();
        this.source = "1";//来源:1-审批,2-录入，3-系统默认旷工
    }

    public PersonnelAttendanceUserDetail(PersonnelAttendanceUser pau, WorkdayType type, User... operators) {
        this(operators);
        this.attendanceId = pau.getId();
        this.user = pau.getUser();
        this.org = pau.getOrg();
        this.attendanceDate = pau.getAttendanceDate();
        this.workdayType = type.getIndex();
        this.type = AttendanceType.pending.getName();
        this.beginTime = pau.getBeginTime();
        this.endTime = pau.getEndTime();
        this.middleBreak = pau.getMiddleBreak();
        this.breakBegin = pau.getBreakBegin();
        this.breakEnd = pau.getBreakEnd();
        this.breakBeginState = pau.getBreakBeginState();
        this.breakEndState = pau.getBreakEndState();
//        if(pau.getP1Before()!=null) {
//            this.setScanStartTime(new Date(this.beginTime.getTime()-pau.getP1Before()));
//        } else {
//            this.setScanStartTime(pau.getScanStartTime());
//        }
//        if(pau.getP1After()!=null) {
//            this.setScanEndTime(new Date(this.endTime.getTime()-pau.getP1After()));
//        } else {
//            this.setScanEndTime(pau.getScanEndTime());
//        }
        initScan(pau);
        this.autoSetDuration();
        this.source = "2";//来源:1-审批,2-录入，3-系统默认旷工
    }

    public void autoSetDuration() {
        Assert.notNull(beginTime, "考勤明细开始时间不能为空！");
        Assert.notNull(endTime, "考勤明细结束时间不能为空！");
        if(this.breakBegin!=null && this.breakEnd!=null && this.breakBegin.getTime()<this.endTime.getTime() && this.breakEnd.getTime()>this.beginTime.getTime()) {
            this.breakBegin = new Date(Math.max(this.breakBegin.getTime(), this.beginTime.getTime()));
            this.breakEnd = new Date(Math.min(this.breakEnd.getTime(), this.endTime.getTime()));
        } else {
            this.breakBegin = null;
            this.breakEnd = null;
        }
        if (this.breakBegin!=null && this.breakEnd!=null) {
            this.duration = (double)(this.endTime.getTime() - this.beginTime.getTime() - this.breakEnd.getTime() + this.breakBegin.getTime()) / TimeUnit.HOURS.toMillis(1);
        } else {
            this.duration = (double)(this.endTime.getTime() - this.beginTime.getTime()) / TimeUnit.HOURS.toMillis(1);
        }
    }

    public void initScan(PersonnelAttendanceUser pau) {
        if(WorkdayType.Workday.getIndex().equals(this.workdayType)) {
            if(pau.getP1Before()!=null) {
                scanStartTime=new Date(beginTime.getTime()-pau.getP1Before());
            } else {
                scanStartTime=pau.getScanStartTime();
            }
            if(pau.getP2After()!=null) {
                scanEndTime=new Date(endTime.getTime()+pau.getP1After());
            } else {
                scanEndTime=pau.getScanEndTime();
            }
        } else {
            if(pau.getP2Before()!=null) {
                scanStartTime=new Date(beginTime.getTime()+pau.getP2Before());
            } else {
                scanStartTime=endTime;
            }
            if(pau.getP2After()!=null) {
                scanEndTime=new Date(endTime.getTime()-pau.getP2After());
            } else {
                scanEndTime=beginTime;
            }
        }
    }
}
