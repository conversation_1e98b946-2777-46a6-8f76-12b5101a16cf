package cn.sphd.miners.modules.material.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.material.dao.MtPackagingStructureDao;
import cn.sphd.miners.modules.material.entity.MtPackagingStructure;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class MtPackagingStructureDaoImpl extends BaseDao<MtPackagingStructure, Serializable> implements MtPackagingStructureDao {
}
