package cn.sphd.miners.modules.material.service.impl;

import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.material.dao.*;
import cn.sphd.miners.modules.material.dto.BatchPoContractDto;
import cn.sphd.miners.modules.material.dto.MaterialContractDto;
import cn.sphd.miners.modules.material.dto.SupplierContractDto;
import cn.sphd.miners.modules.material.entity.*;
import cn.sphd.miners.modules.material.service.MaterielService;
import cn.sphd.miners.modules.material.service.PoContractNewService;
import cn.sphd.miners.modules.system.dao.LocalSettingDao;
import cn.sphd.miners.modules.system.entity.LocalSetting;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.uploads.service.UploadService;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED)
public class PoContractNewServiceImpl implements PoContractNewService {

    @Autowired
    PoContractBaseDao poContractBaseDao;
    @Autowired
    PoContractBaseHistoryDao poContractBaseHistoryDao;
    @Autowired
    PoContractImageDao poContractImageDao;
    @Autowired
    PoContractImageHistoryDao poContractImageHistoryDao;
    @Autowired
    PoContractMaterialDao poContractMaterialDao;
    @Autowired
    PoContractMaterialHistoryDao poContractMaterialHistoryDao;
    @Autowired
    MtBaseDao mtBaseDao;
    @Autowired
    LocalSettingDao localSettingDao;
    @Autowired
    SrmSupplierDao srmSupplierDao;


    @Autowired
    UploadService uploadService;
    @Autowired
    MaterielService materielService;

    @Override
    public void insertBatchPoContract(String contractBaseList, Integer supplierId, User user) {
        List<BatchPoContractDto> list = JSON.parseArray(contractBaseList, BatchPoContractDto.class);
        if (list != null && !list.isEmpty())
            for (BatchPoContractDto dto : list) {
                PoContractBase po = new PoContractBase();
                po.setSupplier(supplierId);
                po.setSn(dto.getSn());
                po.setFilePath(dto.getFilePath());
                po.setFileName(dto.getFileName());
                po.setMemo(dto.getMemo());
                this.insertSupplierCon(user, po, dto.getContractSignTime(), dto.getContractStartTime(), dto.getContractEndTime(), dto.getContractBaseImages(), dto.getMtList());
            }
    }

    @Override
    public PoContractBase insertSupplierCon(User user, PoContractBase poContractBase, String contractSignTime, String contractStartTime, String contractEndTime, String contractBaseImages, String mtList) {
        PoContractBase primaryPoCon = this.insertPoContract(user, poContractBase, contractSignTime, contractStartTime, contractEndTime, 0);
        PoContractBase conSon = new PoContractBase();
        BeanUtils.copyPropertiesIgnoreNull(primaryPoCon, conSon);
        conSon.setId(null);
        conSon.setPrimaryCont(primaryPoCon.getId());
        conSon.setLevel(1);
        poContractBaseDao.save(conSon);
        //新增改的版本
        PoContractBaseHistory poConHis = this.addPoContractHis(conSon, "3");
        if (StringUtils.isNotBlank(conSon.getFilePath())) {
            SrmSupplierUsing sb = new SrmSupplierUsing(conSon.getId(), PoContractBase.class);
            uploadService.addFileUsing(sb, conSon.getFilePath(), conSon.getFileName(), user, "供应商");
            SrmSupplierUsing sbh = new SrmSupplierUsing(poConHis.getId(), PoContractBaseHistory.class);
            uploadService.addFileUsing(sbh, poConHis.getFilePath(), poConHis.getFileName(), user, "供应商");
        }
        if (StringUtils.isNotBlank(contractBaseImages)) {
            this.addPoContractImage(contractBaseImages, user, conSon, poConHis);
        }
        if (StringUtils.isNotBlank(mtList)) {
            Integer num = this.insertContractMaterial(mtList, user, conSon, poConHis);
            conSon.setMaterialCount(num);
            poConHis.setMaterialCount(num);
        }
        return conSon;
    }

    @Override
    public Integer upSupplierCon(User user, PoContractBase poContractBase, String contractSignTime, String contractStartTime, String contractEndTime, String contractBaseImages, String mtList) {
        PoContractBase oldBase = poContractBaseDao.get(poContractBase.getId());
        Integer state = 1;
        PoContractBase newBase = this.getPoContractByPreviousOrLast(oldBase.getPrimaryCont(), oldBase.getLevel(), "2");
        if (newBase != null) {
            state = 2;
        } else {
            if (StringUtils.isNotBlank(contractStartTime) && StringUtils.isNotBlank(contractEndTime)) {
                PoContractBase previousBase = this.getPoContractByPreviousOrLast(oldBase.getPrimaryCont(), oldBase.getLevel(), "1");
                Date validStart = NewDateUtils.dateFromString(contractStartTime, "yyyy-MM-dd");
                if (previousBase != null) {
                    if (validStart.getTime() > previousBase.getValidEnd().getTime()) {
                        oldBase.setValidStart(validStart);
                        oldBase.setValidEnd(NewDateUtils.dateFromString(contractEndTime, "yyyy-MM-dd"));
                    } else {
                        state = 3;
                    }
                } else {
                    oldBase.setValidStart(validStart);
                    oldBase.setValidEnd(NewDateUtils.dateFromString(contractEndTime, "yyyy-MM-dd"));
                }
            }
            if (state.equals(1)) {
                if (StringUtils.isNotBlank(poContractBase.getSn())) {
                    oldBase.setSn(poContractBase.getSn());
                }
                if (StringUtils.isNotBlank(contractSignTime)) {
                    oldBase.setSignTime(NewDateUtils.dateFromString(contractSignTime, "yyyy-MM-dd"));
                }
                if (StringUtils.isNotBlank(poContractBase.getMemo())) {
                    oldBase.setMemo(poContractBase.getMemo());
                }
                SrmSupplierUsing sb = new SrmSupplierUsing(oldBase.getId(), PoContractBase.class);
                if (StringUtils.isNotBlank(poContractBase.getFilePath()) && StringUtils.isNotBlank(poContractBase.getFileName())) {
                    if (StringUtils.isNotBlank(oldBase.getFilePath())) {
                        if (!oldBase.getFilePath().equals(poContractBase.getFilePath())) {
                            uploadService.delFileUsing(sb, oldBase.getFilePath(), user);
                            oldBase.setFilePath(poContractBase.getFilePath());
                            oldBase.setFileName(poContractBase.getFileName());
                            uploadService.addFileUsing(sb, oldBase.getFilePath(), oldBase.getFileName(), user, "供应商");
                        }
                    } else {
                        oldBase.setFilePath(poContractBase.getFilePath());
                        oldBase.setFileName(poContractBase.getFileName());
                        uploadService.addFileUsing(sb, oldBase.getFilePath(), oldBase.getFileName(), user, "供应商");
                    }
                } else {
                    if (StringUtils.isNotBlank(oldBase.getFilePath())) {
                        uploadService.delFileUsing(sb, oldBase.getFilePath(), user);
                    }
                    oldBase.setFilePath(null);
                    oldBase.setFileName(null);
                }
                oldBase.setUpdator(user.getUserID());
                oldBase.setUpdateName(user.getUserName());
                oldBase.setUpdateDate(new Date());
                oldBase.setVersionNo(oldBase.getVersionNo() + 1);
                //新增修改的历史
                PoContractBaseHistory poContractBaseHistory = this.addPoContractHis(oldBase, "3");
                //合同可编辑版历史的引用
                if (StringUtils.isNotBlank(poContractBaseHistory.getFilePath()) && StringUtils.isNotBlank(poContractBaseHistory.getFileName())) {
                    SrmSupplierUsing sbh = new SrmSupplierUsing(poContractBaseHistory.getId(), PoContractBaseHistory.class);
                    uploadService.addFileUsing(sbh, poContractBaseHistory.getFilePath(), poContractBaseHistory.getFileName(), user, "供应商");
                }
                //新增修改或删除图片
                if (StringUtils.isNotBlank(contractBaseImages)) {
                    this.addPoContractImage(contractBaseImages, user, oldBase, poContractBaseHistory);
                }
                if (StringUtils.isNotBlank(mtList)) {
                    this.delContractMaterial(oldBase.getId());
                    Integer num = this.insertContractMaterial(mtList, user, oldBase, poContractBaseHistory);
                    oldBase.setMaterialCount(num);
                    poContractBaseHistory.setMaterialCount(num);
                }
            }
        }
        return state;
    }

    @Override
    public Integer renewalPoContract(User user, PoContractBase poContractBase, String contractSignTime, String contractStartTime, String contractEndTime, String contractBaseImages, String mtList, String type) {
        PoContractBase oldBase = poContractBaseDao.get(poContractBase.getId());
        Integer state = 1;
        PoContractBase newBase = this.getPoContractByPreviousOrLast(oldBase.getPrimaryCont(), oldBase.getLevel(), "2");
        if (newBase == null) {
            Date validStart = NewDateUtils.dateFromString(contractStartTime, "yyyy-MM-dd");
            if (oldBase.getValidEnd().getTime() > validStart.getTime()) {
                state = 3;
            } else {
                if ("2".equals(type) || "3".equals(type)) {
                    PoContractBase primaryContract = poContractBaseDao.get(oldBase.getPrimaryCont());
                    if ("2".equals(type)) {
                        primaryContract.setSuspend(false);
                        primaryContract.setSuspendTime(new Date());
                        PoContractBaseHistory poContractBaseHistory = this.addPoContractHis(primaryContract, "4");
                    } else {
                        primaryContract.setIsExpired(0);
                    }
                }
                poContractBase.setId(null);
                PoContractBase newContract = this.insertPoContract(user, poContractBase, contractSignTime, contractStartTime, contractEndTime, oldBase.getLevel() + 1);
                newContract.setPrimaryCont(oldBase.getPrimaryCont());
                //新增改的版本
                PoContractBaseHistory contractBaseHistory = this.addPoContractHis(newContract, "3");
                //给合同可编辑版加引用
                if (StringUtils.isNotBlank(newContract.getFilePath())) {
                    SrmSupplierUsing sb = new SrmSupplierUsing(newContract.getId(), PoContractBase.class);
                    uploadService.addFileUsing(sb, newContract.getFilePath(), newContract.getFileName(), user, "供应商");
                    SrmSupplierUsing sbh = new SrmSupplierUsing(contractBaseHistory.getId(), PoContractBaseHistory.class);
                    uploadService.addFileUsing(sbh, contractBaseHistory.getFilePath(), contractBaseHistory.getFileName(), user, "供应商");
                }
                if (StringUtils.isNotBlank(contractBaseImages)) {
                    this.addPoContractImage(contractBaseImages, user, newContract, contractBaseHistory);
                }
                if (StringUtils.isNotBlank(mtList)) {
                    Integer num = this.insertContractMaterial(mtList, user, newContract, contractBaseHistory);
                    newContract.setMaterialCount(num);
                    contractBaseHistory.setMaterialCount(num);
                }
                PoContractBase primaryBase = poContractBaseDao.get(oldBase.getPrimaryCont());
                if (primaryBase.getIsExpired() == 1) {
                    primaryBase.setIsExpired(0);
                }
            }
        } else {
            state = 2;
        }
        return state;
    }

    @Override
    public Integer terminatePoContract(Integer id) {
        PoContractBase base = poContractBaseDao.get(id);
        PoContractBase primaryContract = poContractBaseDao.get(base.getPrimaryCont());
        Integer state = 1;
        if (primaryContract.getSuspend()) {
            state = 0;   //不要重复暂停
        } else {
            primaryContract.setSuspend(true);
            primaryContract.setSuspendTime(new Date());
            //新增暂停的版本
            PoContractBaseHistory poContractBaseHistory = this.addPoContractHis(primaryContract, "4");
        }
        return state;
    }

    @Override
    public HashMap<String, Object> checkReStartPoContractState(Integer id) {
        PoContractBase base = poContractBaseDao.get(id);
        PoContractBase primaryContract = poContractBaseDao.get(base.getPrimaryCont());
        Integer state = 0; //不要重复启用
        HashMap<String, Object> map = new HashMap<>();
        if (primaryContract.getSuspend()) {
            state = 1; //没有过期直接恢复
            Date now = NewDateUtils.today();
            PoContractBase newBase = this.getPoContractByPreviousOrLast(base.getPrimaryCont(), base.getLevel(), "2");
            if (newBase == null) {
                if (now.getTime() > base.getValidEnd().getTime()) {
                    state = 2; //已经过期了
                    map.put("vaildEnd", base.getValidEnd());
                }
            } else {
                if (now.getTime() > newBase.getValidEnd().getTime()) {
                    state = 2; //已经过期了
                    map.put("vaildEnd", base.getValidEnd());
                }
            }
        }
        map.put("state", state);
        return map;
    }

    @Override
    public Integer reStartPoContract(Integer id, String type) {
        PoContractBase base = poContractBaseDao.get(id);
        PoContractBase primaryContract = poContractBaseDao.get(base.getPrimaryCont());
        Integer state = 0;  //不要重复启用
        HashMap<String, Object> map = new HashMap<>();
        if (primaryContract.getSuspend()) {
            state = 1;
            primaryContract.setSuspend(false);
            primaryContract.setSuspendTime(new Date());
            if ("2".equals(type)) {
                primaryContract.setIsExpired(1);
            }
            //新增暂停的版本
            PoContractBaseHistory poContractBaseHistory = this.addPoContractHis(primaryContract, "4");
        }
        return state;
    }

    @Override
    public List<MaterialContractDto> getMaterialContract(User user, String type, Integer supplier, List<Integer> idsList) {
        StringBuffer hql = null;
        Map<String, Object> param = new HashMap<>();
        if ("1".equals(type)) {
            hql = new StringBuffer("select new cn.sphd.miners.modules.material.dto.MaterialContractDto(tmb.id, tmb.code, tmb.name, tmb.model, tmb.specifications, tmu.name as unit, tmu.id as unitId) from MtSupplierMaterial tmsm left join MtBase tmb on tmsm.material = tmb.id left join MtUnit tmu on tmb.unitId = tmu.id where tmsm.supplier = :supplier and tmsm.enabled = 1 AND tmb.name IS NOT NULL AND tmb.org = :org");
            param.put("supplier", supplier);
            param.put("org", user.getOid());
        } else {
            hql = new StringBuffer("select new cn.sphd.miners.modules.material.dto.MaterialContractDto(p.id, p.code, p.name, p.model, p.specifications, u.name as unit) from MtBase p left join MtUnit u on  u.id=p.unitId where p.id in :list");
            param.put("list", idsList);
        }
        List<MaterialContractDto> list = mtBaseDao.getListByHQLWithNamedParams(hql.toString(), param);
        for (MaterialContractDto m : list) {
            List<PoContractBase> listContract = this.listContractByMaterielId(m.getId());
            if (!listContract.isEmpty()) {
                m.setContractNum(Long.valueOf(listContract.size()));
            } else {
                m.setContractNum(Long.valueOf(0));
            }
            m.setListPoContract(listContract);
        }
        return list;
    }

    @Override
    public List<PoContractBase> listPoContract(Integer supplierId, String type, String state, List<Integer> listConIds) {
        String hql = "from PoContractBase where";
        HashMap<String, Object> param = new HashMap<>();
        if ("4".equals(type)) {
            hql = hql + " isExpired = :isExpired and isSuspend = :isSuspend and primaryCont is null";
        } else {
            hql = hql + " supplier = :supplier and isExpired = :isExpired and isSuspend = :isSuspend and primaryCont is null";
            param.put("supplier", supplierId);
        }
        if ("2".equals(type)) {
            param.put("isExpired", 0);
            param.put("isSuspend", true);
        } else if ("3".equals(type)) {
            param.put("isExpired", 1);
            param.put("isSuspend", false);
        } else {
            param.put("isExpired", 0);
            param.put("isSuspend", false);
        }
        List<PoContractBase> listPrimary = poContractBaseDao.getListByHQLWithNamedParams(hql, param);
        List<PoContractBase> listContract = this.getValidSupplierContract(listPrimary,type,state,listConIds);
        return listContract;
    }

    @Override
    public List<PoContractBase> listAllPoContract(User user, String type) {
        StringBuffer hql = new StringBuffer("from PoContractBase where org = :org and isExpired = :isExpired and isSuspend = :isSuspend and primaryCont is null");
        Map<String, Object> param = new HashMap(){{
            put("org", user.getOid());
        }};
        if ("2".equals(type)) {
            param.put("isExpired", 0);
            param.put("isSuspend", true);
        } else if ("3".equals(type)) {
            param.put("isExpired", 1);
            param.put("isSuspend", false);
        } else {
            param.put("isExpired", 0);
            param.put("isSuspend", false);
        }
        List<PoContractBase> listPrimary = poContractBaseDao.getListByHQLWithNamedParams(hql.toString(), param);
        List<PoContractBase> listContract = this.getValidSupplierContract(listPrimary,type,null,null);
        return listContract;
    }

    @Override
    public List<PoContractBaseHistory> getListPoContractHisByoperation(Integer id, String operation) {
        String hql = "from PoContractBaseHistory where contract = :contract and operation = :operation order by id asc";
        Map<String, Object> param = new HashMap(2) {
            {
                put("contract", id);
                put("operation", operation);
            }
        };
        List<PoContractBaseHistory> list = poContractBaseHistoryDao.getListByHQLWithNamedParams(hql, param);
        return list;
    }

    @Override
    public List<PoContractBase> getPoContractSignRecord(Integer primaryId) {
        String hql = " from PoContractBase where primaryCont = :primaryCont order by level asc";
        Map<String, Object> param = new HashMap(1) {
            {
                put("primaryCont", primaryId);
            }
        };
        List<PoContractBase> list = poContractBaseDao.getListByHQLWithNamedParams(hql, param);
        return list;
    }

    @Override
    public HashMap<String, Object> getPoContractMes(User user, Integer id) {
        PoContractBase base = poContractBaseDao.get(id);
        String hql = " from PoContractImage where contract = :contract and operation != :operation order by id asc";
        Map<String, Object> param = new HashMap(2) {{
            put("contract", id);
            put("operation", "2");
        }};
        List<PoContractImage> listImage = poContractImageDao.getListByHQLWithNamedParams(hql, param);
        String hqlM = "select material from PoContractMaterial where contract = :contract";
        Map<String, Object> paramM = new HashMap(1) {
            {
                put("contract", id);
            }
        };
        List<Integer> listMtId = poContractMaterialDao.getListByHQLWithNamedParams(hqlM, paramM);
        List<MaterialContractDto> listMt = null;
        if (!listMtId.isEmpty()) {
            listMt = this.getMaterialContract(user, "2", null, listMtId);
        }
        List<PoContractBaseHistory> listHis = this.getListPoContractHisByoperation(base.getPrimaryCont(), "4");
        HashMap<String, Object> map = new HashMap<>();
        map.put("listImage", listImage);
        map.put("listMt", listMt);
        map.put("listHis", listHis);
        map.put("contractBase", base);
        return map;
    }

    @Override
    public HashMap<String, Object> getPoContractHisMes(User user, Integer contractHisId) {
        PoContractBaseHistory baseHis = poContractBaseHistoryDao.get(contractHisId);
        String hqlHisImage = " from PoContractImageHistory where contractHistory = :contractHistory order by id asc";
        Map<String, Object> paramImage = new HashMap(1) {{
            put("contractHistory", contractHisId);
        }};
        List<PoContractImageHistory> listHisImage = poContractImageHistoryDao.getListByHQLWithNamedParams(hqlHisImage, paramImage);
        String hqlMtHis = "select material from PoContractMaterialHistory where contractHistory = :contractHistory";
        Map<String, Object> paramMtHis = new HashMap(1) {
            {
                put("contractHistory", contractHisId);
            }
        };
        List<Integer> listMtHisIds = poContractMaterialHistoryDao.getListByHQLWithNamedParams(hqlMtHis, paramMtHis);
        List<MaterialContractDto> listMtHis = null;
        if (!listMtHisIds.isEmpty()) {
            listMtHis = this.getMaterialContract(user, "2", null, listMtHisIds);
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("contracthistory", baseHis);
        map.put("listHisImage", listHisImage);
        map.put("listMtHis", listMtHis);
        return map;
    }

    @Override
    public LocalSetting getContractRemindByType(User user, String type) {
        StringBuffer hql = new StringBuffer(" from LocalSetting where creator = :creator and key = :key");
        String k = null;
        if ("1".equals(type)) {
            k = "SupplierContract";
        } else {
            k = "SalesContract";
        }
        Map<String, Object> param = new HashMap<>(2);
        param.put("creator", user.getUserID());
        param.put("key", k);
        LocalSetting ls = (LocalSetting) localSettingDao.getByHQLWithNamedParams(hql.toString(), param);
        if (ls==null){
            ls = new LocalSetting();
            ls.setCreateDate(new Date());
            ls.setCreateName(user.getUserName());
            ls.setCreator(user.getUserID());
            ls.setKey(k);
            ls.setValue("1");
            ls.setEnabled(true); //需提醒
            localSettingDao.save(ls);
        }
        return ls;
    }

    @Override
    public void upContractRemind(User user, String type) {
        LocalSetting ls = this.getContractRemindByType(user,type);
        ls.setEnabled(false);
    }

    @Override
    public List<SupplierContractDto> procurementContractList(User user) {
        List<SupplierContractDto> list = this.getAllSupplierForContract(user);
        for (SupplierContractDto s : list) {
            StringBuffer hql = new StringBuffer("select count(tmb.id) from MtSupplierMaterial tmsm left join MtBase tmb on tmsm.material = tmb.id left join MtUnit tmu on tmb.unitId = tmu.id where tmsm.supplier = :supplier and tmsm.enabled = 1 AND tmb.name IS NOT NULL AND tmb.org = :org");
            Map<String, Object> param = new HashMap<String, Object>(){{
                put("supplier", s.getId());
                put("org", user.getOid());
            }};
            Long numM = (Long) mtBaseDao.getByHQLWithNamedParams(hql.toString(), param);
            List<Integer> conIds = new ArrayList<>();
            this.listPoContract(s.getId(),"1", "1", conIds);
            Long contractNum = Long.valueOf(conIds.size());
            Long inNum = this.getMtNumBySupplierContract(conIds);
            if (inNum == null) {
                inNum = Long.valueOf(0);
            }
            List<Integer> otherConIds = new ArrayList<>();
            this.listPoContract(s.getId(),"2","1",otherConIds);
            Long otherContractNum = Long.valueOf(otherConIds.size());
            s.setNumM(numM);
            s.setInNum(inNum);
            s.setContractNum(contractNum);
            s.setOtherContractNum(otherContractNum);
        }
        return list;
    }

    @Override
    public List<SupplierContractDto> getAllSupplierForContract(User user) {
        StringBuffer hql = new StringBuffer("select new cn.sphd.miners.modules.material.dto.SupplierContractDto(id, fullName) from SrmSupplier where org = :oid and enabled = :enabled and type = :type order by createDate desc");
        Map<String, Object> param = new HashMap<String, Object>(3){{
            put("oid", user.getOid());
            put("enabled", 1);
            put("type", 1);
        }};
        List<SupplierContractDto> list = srmSupplierDao.getListByHQLWithNamedParams(hql.toString(), param);
        return list;
    }

    //新增采购合同表
    private PoContractBase insertPoContract(User user, PoContractBase poContractBase, String contractSignTime, String contractStartTime, String contractEndTime, Integer level) {
        poContractBase.setSignTime(NewDateUtils.dateFromString(contractSignTime, "yyyy-MM-dd"));
        poContractBase.setValidStart(NewDateUtils.dateFromString(contractStartTime, "yyyy-MM-dd"));
        poContractBase.setValidEnd(NewDateUtils.dateFromString(contractEndTime, "yyyy-MM-dd"));
        if (StringUtils.isBlank(poContractBase.getFilePath())) {
            poContractBase.setFileName(null);
            poContractBase.setFilePath(null);
        }
        poContractBase.setOrg(user.getOid());
        poContractBase.setCreator(user.getUserID());
        poContractBase.setCreateDate(new Date());
        poContractBase.setCreateName(user.getUserName());
        poContractBase.setOperation("1");
        poContractBase.setVersionNo(0);
        poContractBase.setLevel(level);
        poContractBase.setIsExpired(0);
        poContractBase.setSuspend(false);
        poContractBaseDao.save(poContractBase);
        return poContractBase;
    }

    //新增采购合同历史表
    private PoContractBaseHistory addPoContractHis(PoContractBase poContractBase, String operation) {
        PoContractBaseHistory poContractBaseHistory = new PoContractBaseHistory();
        BeanUtils.copyPropertiesIgnoreNull(poContractBase, poContractBaseHistory);
        poContractBaseHistory.setId(null);
        poContractBaseHistory.setContract(poContractBase.getId());
        poContractBaseHistory.setPrimaryCont(poContractBase.getPrimaryCont());
        poContractBaseHistory.setOperation(operation);
        if ("4".equals(operation)) {
            poContractBaseHistory.setFileName(null);
            poContractBaseHistory.setFilePath(null);
        }
        poContractBaseHistoryDao.save(poContractBaseHistory);
        return poContractBaseHistory;
    }

    //新增采购合同图片表
    private void addPoContractImage(String contractBaseImages, User user, PoContractBase poCon, PoContractBaseHistory poConHis) {
        List<PoContractImage> list = JSON.parseArray(contractBaseImages, PoContractImage.class);
        for (PoContractImage i : list) {
            if ("1".equals(i.getOperation())) {
                i.setOrg(user.getOid());
                i.setContract(poCon.getId());
                i.setOrders(0);
                i.setCreator(user.getUserID());
                i.setCreateDate(new Date());
                i.setUpdateDate(i.getCreateDate());
                i.setCreateName(user.getUserName());
                i.setOperation("1");
                i.setVersionNo(0);
                poContractImageDao.save(i);
                SrmSupplierUsing siu = new SrmSupplierUsing(i.getId(), PoContractImage.class);
                uploadService.addFileUsing(siu, i.getUplaodPath(), i.getTitle(), user, "供应商");
                this.addPoContractImageHis(user, i, poConHis.getId());
            } else {
                PoContractImage image = poContractImageDao.get(i.getId());
                if (!("4".equals(i.getOperation()))) {
                    SrmSupplierUsing siu = new SrmSupplierUsing(i.getId(), PoContractImage.class);
                    uploadService.delFileUsing(siu, image.getUplaodPath(), user);
                    if ("3".equals(i.getOperation())) {
                        image.setTitle(i.getTitle());
                        image.setType(i.getType());
                        image.setUplaodPath(i.getUplaodPath());
                        image.setOperation(i.getOperation());
                        image.setUpdateDate(new Date());
                        uploadService.addFileUsing(siu, image.getUplaodPath(), image.getTitle(), user, "供应商");
                        this.addPoContractImageHis(user, image, poConHis.getId());
                    } else {
                        image.setUplaodPath(null);
                        image.setUpdateDate(new Date());
                        image.setOperation(i.getOperation());
                    }
                } else {
                    this.addPoContractImageHis(user, image, poConHis.getId());
                }
            }
        }
    }

    //新增采购合同图片历史表
    private void addPoContractImageHis(User user, PoContractImage pi, Integer hisId) {
        PoContractImageHistory imageHis = new PoContractImageHistory();
        BeanUtils.copyPropertiesIgnoreNull(pi, imageHis);
        imageHis.setId(null);
        imageHis.setContractImage(pi.getId());
        imageHis.setContractHistory(hisId);
        poContractImageHistoryDao.save(imageHis);
        SrmSupplierUsing sihu = new SrmSupplierUsing(imageHis.getId(), PoContractImageHistory.class);
        uploadService.addFileUsing(sihu, imageHis.getUplaodPath(), imageHis.getTitle(), user, "供应商");
    }

    //新增合同材料对应表
    private Integer insertContractMaterial(String mtList, User user, PoContractBase conSon, PoContractBaseHistory poConHis) {
        List<PoContractMaterial> list = JSON.parseArray(mtList, PoContractMaterial.class);
        for (PoContractMaterial cm : list) {
            cm.setOrg(user.getOid());
            cm.setCreator(user.getUserID());
            cm.setCreateDate(new Date());
            cm.setCreateName(user.getUserName());
            cm.setContract(conSon.getId());
            cm.setOperation("1");
            cm.setVersionNo(0);
            poContractMaterialDao.save(cm);
            this.insertContractMaterialHis(cm, poConHis);
        }
        return list.size();
    }

    //新增合同材料历史对应表
    private void insertContractMaterialHis(PoContractMaterial poContractMaterial, PoContractBaseHistory poConHis) {
        PoContractMaterialHistory poContractMaterialHistory = new PoContractMaterialHistory();
        BeanUtils.copyPropertiesIgnoreNull(poContractMaterial, poContractMaterialHistory);
        poContractMaterialHistory.setId(null);
        poContractMaterialHistory.setContractHistory(poConHis.getId());
        poContractMaterialHistoryDao.save(poContractMaterialHistory);
    }

    //删除商品表商品
    private void delContractMaterial(Integer id) {
        String hql = "delete from PoContractMaterial where contract = :contract";
        Map<String, Object> param = new HashMap(1) {{
            put("contract", id);
        }};
        int state = poContractMaterialDao.queryHQLWithNamedParams(hql, param);
    }

    //获取包含此材料的有效合同
    private List<PoContractBase> listContractByMaterielId(Integer id) {
        String hqlCom = "select id from PoContractBase where isExpired = :isExpired and isSuspend = :isSuspend and primaryCont is null and id in (select primaryCont from PoContractBase where id in (select contract from PoContractMaterial where material = :material) GROUP BY primaryCont)";
        Map<String, Object> paramCom = new HashMap(3) {
            {
                put("isExpired", 0);
                put("isSuspend", false);
                put("material", id);
            }
        };
        List<Integer> listIds = poContractBaseDao.getListByHQLWithNamedParams(hqlCom, paramCom);
        Date today = NewDateUtils.today();
        List<PoContractBase> listPoContract = new ArrayList<>();
        for (Integer i : listIds) {
            PoContractBase newContract = this.getContractNewRecord(i);
            String hql = "from PoContractMaterial where material = :material and contract = :contract";
            Map<String, Object> param = new HashMap() {
            };
            param.put("material", id);
            PoContractMaterial poContractMaterial = null;
            if (newContract.getValidStart().compareTo(today) > 0) {
                PoContractBase contractPrevious = this.getPoContractByPreviousOrLast(i, newContract.getLevel(), "1");
                if (contractPrevious != null) {
                    if (contractPrevious.getValidEnd().compareTo(today) > 0) {
                        param.put("contract", contractPrevious.getId());
                        poContractMaterial = (PoContractMaterial) poContractMaterialDao.getByHQLWithNamedParams(hql, param);
                        if (poContractMaterial != null) {
                            listPoContract.add(contractPrevious);
                        }
                    } else {
                        param.put("contract", newContract.getId());
                        poContractMaterial = (PoContractMaterial) poContractMaterialDao.getByHQLWithNamedParams(hql, param);
                        if (poContractMaterial != null) {
                            listPoContract.add(newContract);
                        }
                    }
                } else {
                    param.put("contract", newContract.getId());
                    poContractMaterial = (PoContractMaterial) poContractMaterialDao.getByHQLWithNamedParams(hql, param);
                    if (poContractMaterial != null) {
                        listPoContract.add(newContract);
                    }
                }
            } else {
                param.put("contract", newContract.getId());
                poContractMaterial = (PoContractMaterial) poContractMaterialDao.getByHQLWithNamedParams(hql, param);
                if (poContractMaterial != null) {
                    listPoContract.add(newContract);
                }
            }
        }
        return listPoContract;
    }

    //获取某个材料合同最新的签约记录
    private PoContractBase getContractNewRecord(Integer primaryCont) {
        String hql = " from PoContractBase where primaryCont = :primaryCont order by level desc";
        HashMap<String, Object> param = new HashMap<>();
        param.put("primaryCont", primaryCont);
        PoContractBase newContract = (PoContractBase) poContractBaseDao.getByHQLWithNamedParams(hql, param);
        return newContract;
    }

    //根据level获取其上一条或下一条合同
    private PoContractBase getPoContractByPreviousOrLast(Integer primaryCont, Integer level, String type) {
        String hql = " from PoContractBase where primaryCont = :primaryCont and level = :level";
        HashMap<String, Object> param = new HashMap<>();
        param.put("primaryCont", primaryCont);
        if ("1".equals(type)) {
            level = level - 1;
        } else {
            level = level + 1;
        }
        param.put("level", level);
        PoContractBase contract = null;
        if (!level.equals(0)) {
            contract = (PoContractBase) poContractBaseDao.getByHQLWithNamedParams(hql, param);
        }
        return contract;
    }

    //公用方法，主要用于根据当前时间来判断显示哪个合同
    private List<PoContractBase> getValidSupplierContract(List<PoContractBase> listPrimary, String type, String state, List<Integer> listConIds){
        Date now = NewDateUtils.today();
        List<PoContractBase> listContract = new ArrayList<>();
        for (PoContractBase base : listPrimary) {
            PoContractBase baseNew = this.getContractNewRecord(base.getId());
            SrmSupplier supplier = materielService.getSrmSupplierById(base.getSupplier());
            if ("4".equals(type)) {
                if (now.compareTo(baseNew.getValidEnd()) > 0) {
                    base.setIsExpired(1);
                }
            } else {
                if (baseNew.getValidStart().compareTo(now) > 0) {
                    PoContractBase basePrevious = this.getPoContractByPreviousOrLast(baseNew.getPrimaryCont(), baseNew.getLevel(), "1");
                    if (basePrevious != null) {
                        if ("2".equals(type)) {
                            basePrevious.setSuspendTime(base.getSuspendTime());
                        }
                        if (supplier != null) {
                            basePrevious.setSupplierName(supplier.getFullName());
                        }
                        listContract.add(basePrevious);
                        if (state != null) {
                            listConIds.add(basePrevious.getId());
                        }
                    } else {
                        if ("2".equals(type)) {
                            baseNew.setSuspendTime(base.getSuspendTime());
                        }
                        if (supplier != null) {
                            baseNew.setSupplierName(supplier.getFullName());
                        }
                        listContract.add(baseNew);
                        if (state != null) {
                            listConIds.add(baseNew.getId());
                        }
                    }
                } else {
                    if ("2".equals(type)) {
                        baseNew.setSuspendTime(base.getSuspendTime());
                    }
                    if (supplier != null) {
                        baseNew.setSupplierName(supplier.getFullName());
                    }
                    listContract.add(baseNew);
                    if (state != null) {
                        listConIds.add(baseNew.getId());
                    }
                }
            }
        }
        return listContract;
    }

    //统计所有合同使用的材料数
    private Long getMtNumBySupplierContract(List<Integer> conIds){
        StringBuffer hql = new StringBuffer("select count(distinct material) from PoContractMaterial where contract in :list");
        Map<String, Object> param = new HashMap<String, Object>(1){{
            put("list", conIds);
        }};
        Long num = (Long) poContractMaterialDao.getByHQLWithNamedParams(hql.toString(),param);
        return num;
    }
}
