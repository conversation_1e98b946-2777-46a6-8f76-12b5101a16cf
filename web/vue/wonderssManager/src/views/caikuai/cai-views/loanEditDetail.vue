
<template>
  <div>
    <div class="head">
      <div style="width: 100%">
        <PageHeader title="财会" :showBack="true" :showClose="true" @mycloseFun="closeFun"></PageHeader>
      </div>
    </div>

    <div v-if="info && loanEditDetail">
      <div class="hr_gray"></div>
      <ul class=" margin-10-15">
        <li v-if="loanEditDetail.indexCC > 0">
            当前为第 {{ loanEditDetail.indexCC }}次修改后的数据，其中本次修改的标记为<span class="ty-color-red">红色</span>
          <br>
          {{ loanEditDetail.createName }} {{ new Date(loanEditDetail.createDate).format('yyyy-MM-dd')  }}修改
        </li>
        <li v-else>
          以下为原始信息
          <br>
          {{ loanEditDetail.createName }} {{ new Date(loanEditDetail.createDate).format('yyyy-MM-dd')  }} 创建
        </li>
      </ul>
      <div class="hr_gray"></div>
      <ul class="xiangUlB margin-10-15">
        <li><span>本金金额</span><span v-html="info.principalAmount"></span></li>
        <div v-if="info.loanType === '1'">
          <!--  借入-->
          <li><span>出资方</span><span v-html="info.lender"></span></li>
          <li><span>本金型式</span><span v-html="info.incomeMethod"></span></li>
          <li><span>收款日期</span><span v-html="info.paymentDate"></span></li>
          <li><span>收款经手人</span><span v-html="info.partnerName"></span></li>
        </div>
        <div v-else>
          <!--  借出 -->
          <li><span>收款方</span><span v-html="info.borrower"></span></li>
          <li><span>本金型式</span><span v-html="info.incomeMethod"></span></li>
          <li><span>付款日期</span><span v-html="info.paymentDate"></span></li>
          <li><span>付款经手人</span><span v-html="info.operatorName"></span></li>
          <li><span>收款经手人</span><span v-html="info.partnerName"></span></li>
        </div>

        <li><span>应归还本金的日期</span><span v-html="info.repaymentDate"></span></li>
        <li><span>名义利率</span><span v-html="info.nominalRate"></span></li>
        <li><span>利息的支付方式</span><span v-html="info.interestMethodStr"></span></li>
        <div v-if="info.interestMethod != 0">
          <li><span>开始计息日期</span><span v-html="info.interestAccrualDate"></span></li>
          <li><span>每月还款日</span><span v-html="info.periodRepaymentDate "></span></li>
          <li><span>每次应付款金额</span><span v-html="info.periodRepaymentAmount "></span></li>
        </div>
        <li><span>备注</span><span class="turnHang" v-html="info.memo"></span></li>
      </ul>
      <div class="hr_gray"></div>
    </div>

  </div>

</template>

<script>
import finance from '@/mixins/finance.js'
export default {
  name: "loanDetail",
  data() {
    return {
      info: null,
      loanEditDetail:{}
    }
  },
  mixins: [finance],
  mounted() {
    let loanEditDetail = localStorage.getItem('loanEditDetail')
    this.loanEditDetail = JSON.parse(loanEditDetail)
    console.log('loanEditDetail=', this.loanEditDetail)
    this.getEditHisDetails(this.loanEditDetail.id)
  },
  methods: {
    getEditHisDetails(id){
      let params = {  tBorrowCommonHistoryId : id }
      this.financeApi.getLoanHistoryDetailCompare(params).then(res1=>{
        console.log('res1 data', res1.data)
        let res = res1.data.data
        let loanHistoryDetailCompare = res.loanHistoryDetailCompare
        let after = loanHistoryDetailCompare.after
        let before = loanHistoryDetailCompare.before
        let newInfo = {}
        if(before){
          newInfo = {
            principalAmount: this.compareTxt(this.formatCurrency(before.principalAmount) , this.formatCurrency(after.principalAmount) ),
            borrower:  this.compareTxt(before.borrower ,after.borrower ) ,
            incomeMethod: this.chargePayMethod( after.incomeMethod) ,
            paymentDate:this.compareTxt((new Date(before.paymentDate).format('yyyy-MM-dd') ) , (new Date(after.paymentDate).format('yyyy-MM-dd') ) )  ,
            operatorName: this.compareTxt(before.operatorName ,after.operatorName ) ,
            partnerName: this.compareTxt(before.partnerName ,after.partnerName ) ,
            repaymentDate: this.compareTxt((before.repaymentDate ? (new Date(before.repaymentDate).format('yyyy-MM-dd') )  : '未约定具体日期') ,(after.repaymentDate ? (new Date(after.repaymentDate).format('yyyy-MM-dd') )  : '未约定具体日期') ) ,
            nominalRate: this.compareTxt(( (before.nominalRate *100).toFixed(2) + '%' ) ,(( after.nominalRate *100 ).toFixed(2) + '%' ) ) ,
            interestMethodStr: this.compareTxt(this.chargeInterestMethod(before.interestMethod )  , this.chargeInterestMethod(after.interestMethod )  )  ,
            interestMethod: after.interestMethod  ,
            memo: this.compareTxt(before.memo ,after.memo ) ,
            lender:this.compareTxt(before.lender ,after.lender ),
            interestAccrualDate: this.compareTxt(new Date(before.interestAccrualDate).format('yyyy-MM-dd')  , new Date(after.interestAccrualDate).format('yyyy-MM-dd') ),
            periodRepaymentDate: this.compareTxt((before.periodRepaymentDate) , (after.periodRepaymentDate) ),
            periodRepaymentAmount: this.compareTxt((before.periodRepaymentAmount) , (after.periodRepaymentAmount) ),
          }

        }else{
          newInfo = {
            principalAmount: this.formatCurrency(after.principalAmount) ,
            borrower: after.borrower ,
            incomeMethod: this.chargePayMethod( after.incomeMethod) ,
            paymentDate: new Date(after.paymentDate).format('yyyy-MM-dd') ,
            operatorName: after.operatorName,
            partnerName: after.partnerName,
            repaymentDate: after.repaymentDate ? (new Date(after.repaymentDate).format('yyyy-MM-dd') )  : '未约定具体日期',
            nominalRate: after.nominalRate *100 + '%' ,
            interestMethodStr: this.chargeInterestMethod(after.interestMethod ),
            interestMethod: after.interestMethod,
            memo: after.memo,
            lender: after.lender,
            interestAccrualDate:  new Date(after.interestAccrualDate).format('yyyy-MM-dd') ,
            periodRepaymentDate: after.periodRepaymentDate ,
            periodRepaymentAmount: after.periodRepaymentAmount
          }
        }
        newInfo.loanType = after.loanType
        this.info = newInfo

      }).catch(err=>{
        console.log('err=', err)
        this.$message.error('')
      })
    },


  }
}
</script>

<style scoped lang="scss">
@import "../../../style/account.scss";
@import "../../../style/common.scss";

.turnHang{
  display: inline-block;  white-space:normal; line-height: 20px;
  max-width: 60%;
}
.loan{
  .fa-angle-right{ font-size: 20px;  }
}
</style>