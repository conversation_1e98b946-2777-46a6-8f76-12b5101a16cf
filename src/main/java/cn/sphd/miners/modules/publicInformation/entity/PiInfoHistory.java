package cn.sphd.miners.modules.publicInformation.entity;


import cn.sphd.miners.common.persistence.BaseEntity;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table ( name ="t_pi_info_history")
public class PiInfoHistory extends BaseEntity {

    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;//`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    @Column(name = "org")
    private Integer org;
    @Column(name = "file")
    private Integer file;
    @Column(name = "category")
    private Integer category;
    @Column(name = "category_name")
    private String categoryName;
    @Column(name = "category_path")
    private String categoryPath;
    @Column(name = "type")
    private Byte type;
    @Column(name = "enabled")
    private Boolean enabled;
    @Column(name = "enabled_time")
    private Date enabledTime;
    @Column(name = "name")
    private String name;
    @Column(name = "file_sn")
    private String fileSn;
    @Column(name = "content")
    private String content;
    @Column(name = "reason")
    private String reason;
    @Column(name = "path")
    private String path;
    @Column(name = "size")
    private long size;
    @Column(name = "total_size")
    private long totalSize;
    @Column(name = "change_num")
    private long changeNum;
    @Column(name = "download_num")
    private long downloadNum;
    @Column(name = "view_num")
    private long viewNum;
    @Column(name = "move_num")
    private long moveNum;
    @Column(name = "modify_num")
    private long modifyNum;
    @Column(name = "is_trash")
    private Boolean isTrash;
    @Column(name = "is_deleted")
    private Boolean isDeleted;
    @Column(name = "delete_time")
    private Date deleteTime;
    @Column(name = "version")
    private String version;
    @Column(name = "keyword")
    private String keyword;
    @Column(name = "valid")
    private Boolean valid;
    @Column(name = "upload_type")
    private Byte uploadType;
    @Column(name = "is_stick")
    private Boolean isStick;
    @Column(name = "company_name")
    private String companyName;
    @Column(name = "tax_id_number")
    private String taxIdNumber;
    @Column(name = "address")
    private String address;
    @Column(name = "telephone")
    private String telephone;
    @Column(name = "bank_code")
    private String bankCode;
    @Column(name = "bank_name")
    private String bankName;
    @Column(name = "bank_no")
    private String bankNo;
    @Column(name = "operator")
    private Integer operator;
    @Column(name = "opertator_name")
    private String opertatorName;
    @Column(name = "operate_date")
    private Date operateDate;
    @Column(name = "approval_instance")
    private Integer approvalInstance;
    @Column(name = "teminate_state")
    private Byte teminateState;
    @Column(name = "teminate_time")
    private Date teminateTime;
    @Column(name = "teminate_reason")
    private String teminateReason;
    @Column(name = "teminater")
    private Integer teminater;
    @Column(name = "teminater_name")
    private String teminaterName;
    @Column(name = "verifier")
    private Integer verifier;
    @Column(name = "verifier_name")
    private String verifierName;
    @Column(name = "verify_date")
    private Date verifyDate;
    @Column(name = "auditor")
    private Integer auditor;
    @Column(name = "audit_name")
    private String auditName;
    @Column(name = "audit_date")
    private Date auditDate;
    @Column(name = "approve_status")
    private Byte approveStatus;
    @Column(name = "approver")
    private Integer approver;
    @Column(name = "approver_name")
    private String approverName;
    @Column(name = "approve_date")
    private Date approveDate;
    @Column(name = "operation")
    private Byte operation;
    @Column(name = "apply_memo")
    private String applyMemo;
    @Column(name = "approve_memo")
    private String approveMemo;
    @Column(name = "memo")
    private String memo;
    @Column(name = "creator")
    private Integer creator;
    @Column(name = "create_name")
    private String createName;
    @Column(name = "create_date")
    private Date createDate;
    @Column(name = "updator")
    private Integer updator;
    @Column(name = "update_name")
    private String updateName;
    @Column(name = "update_date")
    private Date updateDate;
    @Column(name = "message_id")
    private Integer messageId;

    public Integer getId() {
      return id;
    }

    public void setId(Integer id) {
      this.id = id;
    }

    public Integer getOrg() {
      return org;
    }

    public void setOrg(Integer org) {
      this.org = org;
    }

    public Integer getFile() {
      return file;
    }

    public void setFile(Integer file) {
      this.file = file;
    }

    public Integer getCategory() {
      return category;
    }

    public void setCategory(Integer category) {
      this.category = category;
    }

    public String getCategoryName() {
      return categoryName;
    }

    public void setCategoryName(String categoryName) {
      this.categoryName = categoryName;
    }

    public String getCategoryPath() {
      return categoryPath;
    }

    public void setCategoryPath(String categoryPath) {
      this.categoryPath = categoryPath;
    }

    public Byte getType() {
      return type;
    }

    public void setType(Byte type) {
      this.type = type;
    }

    public Boolean getEnabled() {
      return enabled;
    }

    public void setEnabled(Boolean enabled) {
      this.enabled = enabled;
    }

    public Date getEnabledTime() {
      return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
      this.enabledTime = enabledTime;
    }

    public String getName() {
      return name;
    }

    public void setName(String name) {
      this.name = name;
    }

    public String getFileSn() {
      return fileSn;
    }

    public void setFileSn(String fileSn) {
      this.fileSn = fileSn;
    }

    public String getContent() {
      return content;
    }

    public void setContent(String content) {
      this.content = content;
    }

    public String getReason() {
      return reason;
    }

    public void setReason(String reason) {
      this.reason = reason;
    }

    public String getPath() {
      return path;
    }

    public void setPath(String path) {
      this.path = path;
    }

    public long getSize() {
      return size;
    }

    public void setSize(long size) {
      this.size = size;
    }

    public long getTotalSize() {
      return totalSize;
    }

    public void setTotalSize(long totalSize) {
      this.totalSize = totalSize;
    }

    public long getChangeNum() {
      return changeNum;
    }

    public void setChangeNum(long changeNum) {
      this.changeNum = changeNum;
    }

    public long getDownloadNum() {
      return downloadNum;
    }

    public void setDownloadNum(long downloadNum) {
      this.downloadNum = downloadNum;
    }

    public long getViewNum() {
      return viewNum;
    }

    public void setViewNum(long viewNum) {
      this.viewNum = viewNum;
    }

    public long getMoveNum() {
      return moveNum;
    }

    public void setMoveNum(long moveNum) {
      this.moveNum = moveNum;
    }

    public long getModifyNum() {
      return modifyNum;
    }

    public void setModifyNum(long modifyNum) {
      this.modifyNum = modifyNum;
    }

    public Boolean getTrash() {
      return isTrash;
    }

    public void setTrash(Boolean trash) {
      isTrash = trash;
    }

    public Boolean getDeleted() {
      return isDeleted;
    }

    public void setDeleted(Boolean deleted) {
      isDeleted = deleted;
    }

    public Date getDeleteTime() {
      return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
      this.deleteTime = deleteTime;
    }

    public String getVersion() {
      return version;
    }

    public void setVersion(String version) {
      this.version = version;
    }

    public String getKeyword() {
      return keyword;
    }

    public void setKeyword(String keyword) {
      this.keyword = keyword;
    }

    public Boolean getValid() {
      return valid;
    }

    public void setValid(Boolean valid) {
      this.valid = valid;
    }

    public Byte getUploadType() {
      return uploadType;
    }

    public void setUploadType(Byte uploadType) {
      this.uploadType = uploadType;
    }

    public Boolean getStick() {
      return isStick;
    }

    public void setStick(Boolean stick) {
      isStick = stick;
    }

    public String getCompanyName() {
      return companyName;
    }

    public void setCompanyName(String companyName) {
      this.companyName = companyName;
    }

    public String getTaxIdNumber() {
      return taxIdNumber;
    }

    public void setTaxIdNumber(String taxIdNumber) {
      this.taxIdNumber = taxIdNumber;
    }

    public String getAddress() {
      return address;
    }

    public void setAddress(String address) {
      this.address = address;
    }

    public String getTelephone() {
      return telephone;
    }

    public void setTelephone(String telephone) {
      this.telephone = telephone;
    }

    public String getBankCode() {
      return bankCode;
    }

    public void setBankCode(String bankCode) {
      this.bankCode = bankCode;
    }

    public String getBankName() {
      return bankName;
    }

    public void setBankName(String bankName) {
      this.bankName = bankName;
    }

    public String getBankNo() {
      return bankNo;
    }

    public void setBankNo(String bankNo) {
      this.bankNo = bankNo;
    }

    public Integer getOperator() {
      return operator;
    }

    public void setOperator(Integer operator) {
      this.operator = operator;
    }

    public String getOpertatorName() {
      return opertatorName;
    }

    public void setOpertatorName(String opertatorName) {
      this.opertatorName = opertatorName;
    }

    public Date getOperateDate() {
      return operateDate;
    }

    public void setOperateDate(Date operateDate) {
      this.operateDate = operateDate;
    }

    public Integer getApprovalInstance() {
      return approvalInstance;
    }

    public void setApprovalInstance(Integer approvalInstance) {
      this.approvalInstance = approvalInstance;
    }

    public Byte getTeminateState() {
      return teminateState;
    }

    public void setTeminateState(Byte teminateState) {
      this.teminateState = teminateState;
    }

    public Date getTeminateTime() {
      return teminateTime;
    }

    public void setTeminateTime(Date teminateTime) {
      this.teminateTime = teminateTime;
    }

    public String getTeminateReason() {
      return teminateReason;
    }

    public void setTeminateReason(String teminateReason) {
      this.teminateReason = teminateReason;
    }

    public Integer getTeminater() {
      return teminater;
    }

    public void setTeminater(Integer teminater) {
      this.teminater = teminater;
    }

    public String getTeminaterName() {
      return teminaterName;
    }

    public void setTeminaterName(String teminaterName) {
      this.teminaterName = teminaterName;
    }

    public Integer getVerifier() {
      return verifier;
    }

    public void setVerifier(Integer verifier) {
      this.verifier = verifier;
    }

    public String getVerifierName() {
      return verifierName;
    }

    public void setVerifierName(String verifierName) {
      this.verifierName = verifierName;
    }

    public Date getVerifyDate() {
      return verifyDate;
    }

    public void setVerifyDate(Date verifyDate) {
      this.verifyDate = verifyDate;
    }

    public Integer getAuditor() {
      return auditor;
    }

    public void setAuditor(Integer auditor) {
      this.auditor = auditor;
    }

    public String getAuditName() {
      return auditName;
    }

    public void setAuditName(String auditName) {
      this.auditName = auditName;
    }

    public Date getAuditDate() {
      return auditDate;
    }

    public void setAuditDate(Date auditDate) {
      this.auditDate = auditDate;
    }

    public Byte getApproveStatus() {
      return approveStatus;
    }

    public void setApproveStatus(Byte approveStatus) {
      this.approveStatus = approveStatus;
    }

    public Integer getApprover() {
      return approver;
    }

    public void setApprover(Integer approver) {
      this.approver = approver;
    }

    public String getApproverName() {
      return approverName;
    }

    public void setApproverName(String approverName) {
      this.approverName = approverName;
    }

    public Date getApproveDate() {
      return approveDate;
    }

    public void setApproveDate(Date approveDate) {
      this.approveDate = approveDate;
    }

    public Byte getOperation() {
      return operation;
    }

    public void setOperation(Byte operation) {
      this.operation = operation;
    }

    public String getApplyMemo() {
      return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
      this.applyMemo = applyMemo;
    }

    public String getApproveMemo() {
      return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
      this.approveMemo = approveMemo;
    }

    public String getMemo() {
      return memo;
    }

    public void setMemo(String memo) {
      this.memo = memo;
    }

    public Integer getCreator() {
      return creator;
    }

    public void setCreator(Integer creator) {
      this.creator = creator;
    }

    public String getCreateName() {
      return createName;
    }

    public void setCreateName(String createName) {
      this.createName = createName;
    }

    public Date getCreateDate() {
      return createDate;
    }

    public void setCreateDate(Date createDate) {
      this.createDate = createDate;
    }

    public Integer getUpdator() {
      return updator;
    }

    public void setUpdator(Integer updator) {
      this.updator = updator;
    }

    public String getUpdateName() {
      return updateName;
    }

    public void setUpdateName(String updateName) {
      this.updateName = updateName;
    }

    public Date getUpdateDate() {
      return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
      this.updateDate = updateDate;
    }

    public Integer getMessageId() {
      return messageId;
    }

    public void setMessageId(Integer messageId) {
      this.messageId = messageId;
    }
}
