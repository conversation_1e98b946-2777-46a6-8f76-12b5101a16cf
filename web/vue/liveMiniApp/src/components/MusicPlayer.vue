<template>
  <div class="music">
    <!-- 占位 -->
    <div class="m_hold">

    </div>
    <div class="m_img">
      <img :src="this.$parent.songNames[this.$parent.index].png" width="90px" :class="this.$parent.isRun">
    </div>
    <!-- 歌曲信息 -->
    <div class="m_text">
      {{ this.$parent.songNames[this.$parent.index].name }}
      <div class="block" style="margin-top:5px">
        <el-slider :v-model="value1"></el-slider>
      </div>
    </div>
    <!-- 按钮 -->
    <div class="m_btn">
      <a href="#" class="m_prev" @click="playLastSong()"></a>
      <a href="#" class="m_play" @click="changeState()" v-show="this.$parent.isShow"></a>
      <a href="#" class="m_pause" @click="changeState()" v-show="!this.$parent.isShow"></a>
      <a href="#" class="m_next" @click="playNextSong()"></a>
    </div>
    <!-- 折叠功能 -->
    <div class="m_close" @click="changeCloseState()">
      <a href=""></a>
    </div>

  </div>
</template>

<script>
export default {
  name: 'MusicPlayer',
  data() {
    return {
      songName: '',
      value1:0

    }
  },
  methods: {
    changeState() {

      this.$emit("play")
    },
    changeCloseState() {
      this.$emit("hello");
    },
    playNextSong() {
      this.$emit("nextSongs");
      this.songName = this.$parent.songNames[this.$parent.index].name
    },
    playLastSong() {
      this.$emit("lastSongs");
      this.songName = this.$parent.songNames[this.$parent.index].name
    }
  },
  watch:{

  },
  mounted() {
    this.songName = this.$parent.songNames[this.$parent.index].name
  }

}
</script>

<style scoped>
/* 关于播放器的样式 */
.music {
  width: 100%;
  height: 120px;
  background: black;
  /* 相对浏览器定位 */
  position: absolute;
  left: 0px;
  bottom: 100px;
  border-bottom: 50px;
  /* 透明度 */
  opacity: 0.8;
  /* 阴影值 */
  box-shadow: 10px 15px 15px 1px black
}

.music .m_hold {
  float: left;
  width: 90px;
  height: 90px;
}

/* 调整音乐盒图片 */
.music .m_img {
  margin-top: 15px;
  margin-left: 10px;
  margin-right: 10px;
  /* 左浮动 */
  float: left;
  width: 90px;
  height: 90px;
  border-radius: 50%;
  overflow: hidden;

}

/* 修改文字 */
.music .m_text {
  /* 左浮动 */
  float: left;
  color: white;
  font-size: 20px;
  /* 字体加粗 */
  font-weight: bold;
  margin-top: 25px;
  margin-left: 20px;
  margin-bottom: 10px;
  width: 25%;

}

/* 使得所有a标签一起移动 */
.music .m_btn {
  float: left;
  position: absolute;
  /* 绝对定位：防止歌曲名称过长，挤出div */
  left: 40%;
}

/* 修改a标签 */
.music .m_btn a {
  width: 32px;
  height: 32px;
  float: left;
  margin-top: 50px;
  margin-left: 20px;
  background: url(@/assets/player_bg.png);

}

.music .m_btn .m_prev {
  background-position: -69px 0px;
}

.music .m_btn .m_next {
  background-position: -150px 0px;
}

.music .m_btn .m_play {
  background-position: -107px -5px;
}

.music .m_btn .m_prev:hover {
  background-position: -69px -32px;
}

.music .m_btn .m_next:hover {
  background-position: -150px -32px;
}

.music .m_btn .m_play:hover {
  background-position: -107px -47px;
}

.music .m_btn .m_pause {
  background-position: -292px -94px;
}

.music .m_btn .m_pause:hover {
  background-position: -334px -94px;
}

/* 还有一个悬停 没写 */
/* 设置最右边的关闭样式 */
.music .m_close {
  float: right;
  background: white;
  cursor: pointer;
  width: 23px;
  height: 100px;
  margin-top: 10px;
  background: url(@/assets/player_bg.png);

}

/* 设置最右边的关闭样式 */
.music_hide {
  float: left;
  background: white;
  cursor: pointer;
  width: 23px;
  height: 100px;
  margin-top: 2px;
}

.go {
  animation: bounce-in 2s linear infinite;
}

.come {
  animation: none;
}

@keyframes bounce-in {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.open-enter-active {
  animation: slide-in linear 0.5s;
}

.open-leave-active {
  animation: slide-in reverse linear 0.5s;
}

@keyframes slide-in {
  from {
    transform: translateX(-100%);
  }

  to {
    transform: translateX(0%);
  }
}
</style>

