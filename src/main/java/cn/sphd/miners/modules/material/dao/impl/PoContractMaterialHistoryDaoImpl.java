package cn.sphd.miners.modules.material.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.material.dao.PoContractMaterialHistoryDao;
import cn.sphd.miners.modules.material.entity.PoContractMaterial;
import cn.sphd.miners.modules.material.entity.PoContractMaterialHistory;
import cn.sphd.miners.modules.material.entity.SrmSupplier;
import cn.sphd.miners.modules.material.entity.SrmSupplierHistory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Repository
public class PoContractMaterialHistoryDaoImpl extends BaseDao<PoContractMaterialHistory, Serializable> implements PoContractMaterialHistoryDao {
    @Override
    public PoContractMaterialHistory insert(PoContractMaterial poContractMaterial,Integer chId,Integer mtHisId) {
        PoContractMaterialHistory history=new PoContractMaterialHistory();
        BeanUtils.copyProperties(poContractMaterial,history);
        history.setId(null);
        history.setContractMaterial(poContractMaterial.getId());
        history.setContractHistory(chId);
        history.setMaterialHistory(mtHisId);
        save(history);
        return history;
    }

    @Override
    public void deleteByCommodity(Integer contractMaterial) {
        String hql = " delete from PoContractMaterialHistory where contractMaterial=:contractMaterial";
        Map<String, Object> params = new HashMap<>();
        params.put("contractMaterial", contractMaterial);
        queryHQLWithNamedParams(hql, params);
    }
}
