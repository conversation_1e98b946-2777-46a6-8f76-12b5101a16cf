package cn.sphd.miners.modules.publicInformation.entity;


import cn.sphd.miners.common.persistence.BaseEntity;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table ( name ="t_pi_acl_log")
public class PiAclLog extends BaseEntity {

    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;//`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    @Column(name = "org")
    private Integer org;
    @Column(name = "category")
    private Integer category;
    @Column(name = "file")
    private Integer file;
    @Column(name = "operate_type")
    private Byte operateType;
    @Column(name = "grant_user_num")
    private Integer grantUserNum;
    @Column(name = "increased_num")
    private Integer increasedNum;
    @Column(name = "decreased_num")
    private Integer decreasedNum;
    @Column(name = "increased_users")
    private String increasedUsers;
    @Column(name = "decreased_users")
    private String decreasedUsers;
    @Column(name = "is_latest")
    private Boolean isLatest;
    @Column(name = "path")
    private String path;
    @Column(name = "memo")
    private String memo;
    @Column(name = "creator")
    private Integer creator;
    @Column(name = "create_name")
    private String createName;
    @Column(name = "create_date")
    private Date createDate;
    @Column(name = "updator")
    private Integer updator;
    @Column(name = "update_name")
    private String updateName;
    @Column(name = "update_date")
    private Date updateDate;
    @Column(name = "message_id")
    private Integer messageId;
    @Column(name = "operation")
    private Byte operation;
    @Column(name = "previous_id")
    private Integer previousId;
    @Column(name = "version_no")
    private Integer versionNo;

    @Transient
    private String fileName;  //文件名字


    public Integer getId() {
      return id;
    }

    public void setId(Integer id) {
      this.id = id;
    }

    public Integer getOrg() {
      return org;
    }

    public void setOrg(Integer org) {
      this.org = org;
    }

    public Integer getCategory() {
      return category;
    }

    public void setCategory(Integer category) {
      this.category = category;
    }

    public Integer getFile() {
      return file;
    }

    public void setFile(Integer file) {
      this.file = file;
    }

    public Byte getOperateType() {
      return operateType;
    }

    public void setOperateType(Byte operateType) {
      this.operateType = operateType;
    }

    public Integer getGrantUserNum() {
      return grantUserNum;
    }

    public void setGrantUserNum(Integer grantUserNum) {
      this.grantUserNum = grantUserNum;
    }

    public Integer getIncreasedNum() {
      return increasedNum;
    }

    public void setIncreasedNum(Integer increasedNum) {
      this.increasedNum = increasedNum;
    }

    public Integer getDecreasedNum() {
      return decreasedNum;
    }

    public void setDecreasedNum(Integer decreasedNum) {
      this.decreasedNum = decreasedNum;
    }

    public String getIncreasedUsers() {
      return increasedUsers;
    }

    public void setIncreasedUsers(String increasedUsers) {
      this.increasedUsers = increasedUsers;
    }

    public String getDecreasedUsers() {
      return decreasedUsers;
    }

    public void setDecreasedUsers(String decreasedUsers) {
      this.decreasedUsers = decreasedUsers;
    }

    public Boolean getLatest() {
      return isLatest;
    }

    public void setLatest(Boolean latest) {
      isLatest = latest;
    }

    public String getPath() {
      return path;
    }

    public void setPath(String path) {
      this.path = path;
    }

    public String getMemo() {
      return memo;
    }

    public void setMemo(String memo) {
      this.memo = memo;
    }

    public Integer getCreator() {
      return creator;
    }

    public void setCreator(Integer creator) {
      this.creator = creator;
    }

    public String getCreateName() {
      return createName;
    }

    public void setCreateName(String createName) {
      this.createName = createName;
    }

    public Date getCreateDate() {
      return createDate;
    }

    public void setCreateDate(Date createDate) {
      this.createDate = createDate;
    }

    public Integer getUpdator() {
      return updator;
    }

    public void setUpdator(Integer updator) {
      this.updator = updator;
    }

    public String getUpdateName() {
      return updateName;
    }

    public void setUpdateName(String updateName) {
      this.updateName = updateName;
    }

    public Date getUpdateDate() {
      return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
      this.updateDate = updateDate;
    }

    public Integer getMessageId() {
      return messageId;
    }

    public void setMessageId(Integer messageId) {
      this.messageId = messageId;
    }

    public Byte getOperation() {
      return operation;
    }

    public void setOperation(Byte operation) {
      this.operation = operation;
    }

    public Integer getPreviousId() {
      return previousId;
    }

    public void setPreviousId(Integer previousId) {
      this.previousId = previousId;
    }

    public Integer getVersionNo() {
      return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
      this.versionNo = versionNo;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
}
