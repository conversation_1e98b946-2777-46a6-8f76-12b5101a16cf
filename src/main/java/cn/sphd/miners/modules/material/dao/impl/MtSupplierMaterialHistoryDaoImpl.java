package cn.sphd.miners.modules.material.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.material.dao.MtSupplierMaterialHistoryDao;
import cn.sphd.miners.modules.material.entity.MtSupplierMaterial;
import cn.sphd.miners.modules.material.entity.MtSupplierMaterialHistory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;
import java.io.Serializable;

/**
 * Created by Administrator on 2016/9/19.
 */
@Repository
public class MtSupplierMaterialHistoryDaoImpl extends BaseDao<MtSupplierMaterialHistory,Serializable> implements MtSupplierMaterialHistoryDao {
    @Override
    public MtSupplierMaterialHistory saveHis(MtSupplierMaterial mtSupplierMaterial,MtSupplierMaterialHistory mtSupplierHistory) {

        MtSupplierMaterialHistory history=new MtSupplierMaterialHistory();
        BeanUtils.copyProperties(mtSupplierMaterial,history);
        history.setId(null);
        history.setSupplierMaterial(mtSupplierMaterial.getId());
        history.setMaterial(mtSupplierMaterial.getMaterial_());
        history.setSupplier(mtSupplierMaterial.getSupplier());
        history.setMaterialHistory(mtSupplierHistory.getMaterialHistory());
        history.setInstance(mtSupplierHistory.getInstance());
        history.setInstanceChain(mtSupplierHistory.getInstanceChain());
        history.setMaterialInvoicable(mtSupplierMaterial.getMaterialInvoicable());
        save(history);

        getSession().flush();

        return history;

    }
}
