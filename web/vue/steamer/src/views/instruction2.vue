<template>
  <div class="instruction2">
    <div>
      <el-button @click="goCaoZuo">&lt; 返回上一页</el-button>
      <el-button @click="backMain">&lt; 返回主页</el-button>
    </div>
    <div>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="grayCon grayCon2">
            <div class="ttl">
              字幕配置指南
            </div>
            1.网络同步
            <br>
            1.1 进入OBS主页
            <br>
            1.2 点击“来源”框内的“+”，点击“浏览器”，所见为“创建或选择源”页
            <br>
            1.3 在“创建或选择源”页，默认选中“新建”选项，下方有默认的名字，可修改（注：这个名字即是后面页面要显示的名字），点击“确定”，所见为‘设置“XXX”’页（XXX为上方的名字）
            <br>
            1.4 点击本网站的“获取服务地址”按钮，弹出带有地址的弹窗，点击弹窗上的复制按钮，自动复制弹窗中的地址
            <br>
            <el-popover placement="bottom" title="" :width="200" trigger="click" >
              {{ content1 }}
              <el-button type="text" class="fright" @click="copyUrl">复制</el-button>
              <template #reference>
                <el-button type="text" class="fright">获取服务地址</el-button>
              </template>
            </el-popover>
            <br>

            <br>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="grayCon" style="height:360px; ">
            1.5 将‘设置“XXX”’页上的“URL”修改为刚刚复制的地址，把“高度”调整为“200”，其余使用默认值不用修改，点击“确定”后，页面上会出现带有黄色“字幕精灵”的视频。
            <br>
            2 位置调整
            <br>
            2.1 鼠标移至字幕视频后，点击鼠标右键-“排序”-“移至顶层”，之后用鼠标将字幕拖动到想要的位置
            <br>
            2.2 点击“清除测试字幕”按钮，配置完成
            <el-button type="text" class="fright">清除测试字幕</el-button>
            <br>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script>
import auth from '@/sys/auth.js'
import { getLyricLivePath } from '@/api/api.js'

export default {
  data() {
    return {
      content1:'',
      url:''
    }
  },
  components: {
  },
  mounted() {
    this.getLocalUrl()
  },
  methods: {
    copyUrl(){
      var textarea = document.createElement('textarea');
      textarea.value = this.url;
      document.body.appendChild(textarea);
      textarea.select();
      document.execCommand('copy');
      document.body.removeChild(textarea);
    },
    getLocalUrl(){
      getLyricLivePath().then(res => {
        let data = res.data.data
        let baseUrl = data.lyricLivePath
        console.log('res=',res)
        let acc = auth.getAcc()
        let url2 = baseUrl + '#/live2?accId=' + acc.id
        this.url = url2
        this.content1 = '以下为所获取的,歌词地址：' + url2
      }).catch(err => {

      })
    },
    goCaoZuo(){
      this.$router.push('operatInstruction')
    },
    backMain(type) {
      this.$router.push('mainPage')
    },
    getServe() {
      this.$alert('请先选择"直播日期"', '信息', {
        confirmButtonText: '确定',
      })
    }

  }
}
</script>

<style lang="scss" scoped>
.instruction2{
  padding: 40px;
  .fright{ float: right }
  .ttl{ text-align: center; font-weight: bold; margin-bottom: 20px; }
  .grayCon{ padding: 20px; margin: 40px; background: #eee; border-radius: 4px; line-height: 30px;  }
  .grayCon2{ padding-bottom: 28px; }
}
</style>
