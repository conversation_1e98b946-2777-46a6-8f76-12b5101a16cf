package cn.sphd.miners.modules.commodity.service.impl;

import cn.sphd.miners.modules.commodity.entity.PdImage;
import cn.sphd.miners.modules.commodity.entity.PdImageHistory;
import cn.sphd.miners.modules.commodity.service.ProductImageService;
import cn.sphd.miners.modules.uploads.service.FileUsingCallback;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContext;

public class PdBaseImageUsing implements FileUsingCallback {
    private static final long serialVersionUID = 1L;
    Long id;
    String entityClass;
    @Override
    public boolean checkUsing(String filename) {
        if(StringUtils.isNotEmpty(filename)) {
            ServletContext sc = ContextLoader.getCurrentWebApplicationContext().getServletContext();
            ApplicationContext ac = WebApplicationContextUtils.getRequiredWebApplicationContext(sc);
            ProductImageService service = ac.getBean(ProductImageService.class, "productImageService");

            switch (entityClass) {
                case "PdImage":
                    PdImage entity = service.getPdImage(id);
                    if (entity != null&&service.getPdBase(entity.getProduct())!=null) {
                        return filename.equals(entity.getUplaodPath());
                    }
                    break;
                case "PdImageHistory":

                    PdImageHistory historyEntity = service.getPdImageHistory(id);
                    if(historyEntity != null) {
                        PdImage sb = service.getPdImage(historyEntity.getPdImage());
                        if(sb!=null && service.getPdBase(sb.getProduct())!=null){
                            return  filename.equals(historyEntity.getUplaodPath());
                        }
                    }
                    break;

            }
        }
        return false;
    }

    @Override
    @JsonIgnore @JSONField(serialize = false)
    public String getKey() {
        /* 由于本回调类是多实体使用，需要使用id+实体类名称作为唯一key；
         * 被UploadFileUsing.callbackKey保存数据库用于删除文件引用。 */
        return id+entityClass;
    }

    public PdBaseImageUsing(Long id, Class entityClass) {
        this.id = id;
        String className = entityClass.getName();
        //实体类判断，去掉包路径，方便后续包改名或者改路径无需修改代码。
        this.entityClass = className.substring(className.lastIndexOf('.')+1);
    }


    public Long getId() {
        return id;
    }

    public String getEntityClass() {
        return entityClass;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setEntityClass(String entityClass) {
        this.entityClass = entityClass;
    }

    public PdBaseImageUsing() {
    }
}
