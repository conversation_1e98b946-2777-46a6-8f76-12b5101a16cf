package cn.sphd.miners.modules.equipment.service;

import cn.sphd.miners.modules.equipment.entity.TEquCategory;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;

/**
 * 装备管理_分类
 * 20230209 1.247装备器具新增Service接口
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
public interface ITEquCategoryService {
    /**
     * 查询装备管理_分类
     * 20230209 1.247装备器具新增
     *
     * @param id 装备管理_分类
     *           20230209 1.247装备器具新增主键
     * @return 装备管理_分类
     * 20230209 1.247装备器具新增
     */
    public TEquCategory selectTEquCategoryById(Long id);

    /**
     * 查询装备管理_分类
     * 20230209 1.247装备器具新增列表
     *
     * @param tEquCategory 装备管理_分类
     *                     20230209 1.247装备器具新增
     * @return 装备管理_分类
     * 20230209 1.247装备器具新增集合
     */
    public List<TEquCategory> selectTEquCategoryList(TEquCategory tEquCategory);

    /**
     * 新增装备管理_分类
     * 20230209 1.247装备器具新增
     *
     * @param tEquCategory 装备管理_分类
     *                     20230209 1.247装备器具新增
     * @param user
     * @return 结果
     */
    public int insertTEquCategory(TEquCategory tEquCategory, User user);

    /**
     * 修改装备管理_分类
     * 20230209 1.247装备器具新增
     *
     * @param tEquCategory 装备管理_分类
     *                     20230209 1.247装备器具新增
     * @param user
     * @return 结果
     */
    public int updateTEquCategory(TEquCategory tEquCategory, User user);

    /**
     * 批量删除装备管理_分类
     * 20230209 1.247装备器具新增
     *
     * @param ids 需要删除的装备管理_分类
     *            20230209 1.247装备器具新增主键集合
     * @return 结果
     */
    public int deleteTEquCategoryByIds(String... ids);

    /**
     * 删除装备管理_分类
     * 20230209 1.247装备器具新增信息
     *
     * @param id 装备管理_分类
     *           20230209 1.247装备器具新增主键
     * @return 结果
     */
    public int deleteTEquCategoryById(Long id);

    void initCategories(User user);
}
