package cn.sphd.miners.modules.accountant.entity;

import cn.sphd.miners.modules.system.entity.User;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * Created by root on 17-5-31.
 */
public class SubjectChooseParams {

    private String summary;//摘要
    private int belong_peroid;//所属期,1-本月凭证,2-上月凭证(非本月)
    private int mode;//1-一借一贷,2-一借多贷,3-多借一贷，4-多借多贷（目前是这种）
    private double price;//借方发生额总计，用来检测借方发生额是否等于贷方发生额
    private int category;//凭证字,1-转 （目前写死的1）
    private String subjectBorrow;//借方科目集合，每个元素都包含以下几个元素：subject科目编号、subjectNames科目名称、price发生额、（creditQuantity借方数量、debitQuantity贷方数量、unitPrice单价）当选择了数量辅助核算时会用
    private String subjectLoan;//同上
    private int is_account;//是否下账,false-不予下账,true-下帐
    private String reason;//不予下帐理由
    private Integer bill_detail;//票据明细ID
    private Integer detailId;//财务明细ID
    private String memo;
    private int bill_period;//1 本月票据 2 非本月票据
    private int approve_status;//审批状态
    private int pricetype;//0 收入 1 支出
    private String purpose;//用途 同财务里的用途
    private int bill_quantity;//票据数量
    private String kind;//1-数据来自财务detail表 2-数据来自财务bill表
    private String operatorName;//经手人
    private String operatorFinance;//创建人
    private String cashitem;
    private double cash;
    private int source;//1来自会计录入,0-财务，2-结转损益,3-个人报销
    private int oid;
    private User user;
    private String voucherDate;
    private String cashjson;
    private int id;//voucher id

    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date billDate;//票据日期

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    public Date getBillDate() {
        return billDate;
    }

    public void setBillDate(Date billDate) {
        this.billDate = billDate;
    }

    public String getCashjson() {
        return cashjson;
    }

    public void setCashjson(String cashjson) {
        this.cashjson = cashjson;
    }


    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getVoucherDate() {
        return voucherDate;
    }

    public void setVoucherDate(String voucherDate) {
        this.voucherDate = voucherDate;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public int getOid() {
        return oid;
    }

    public void setOid(int oid) {
        this.oid = oid;
    }

    public int getSource() {
        return source;
    }

    public void setSource(int source) {
        this.source = source;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public int getBelong_peroid() {
        return belong_peroid;
    }

    public void setBelong_peroid(int belong_peroid) {
        this.belong_peroid = belong_peroid;
    }

    public int getMode() {
        return mode;
    }

    public void setMode(int mode) {
        this.mode = mode;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public int getCategory() {
        return category;
    }

    public void setCategory(int category) {
        this.category = category;
    }

    public String getSubjectBorrow() {
        return subjectBorrow;
    }

    public void setSubjectBorrow(String subjectBorrow) {
        this.subjectBorrow = subjectBorrow;
    }

    public String getSubjectLoan() {
        return subjectLoan;
    }

    public void setSubjectLoan(String subjectLoan) {
        this.subjectLoan = subjectLoan;
    }

    public int getIs_account() {
        return is_account;
    }

    public void setIs_account(int is_account) {
        this.is_account = is_account;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Integer getBill_detail() {
        return bill_detail;
    }

    public void setBill_detail(Integer bill_detail) {
        this.bill_detail = bill_detail;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public int getBill_period() {
        return bill_period;
    }

    public void setBill_period(int bill_period) {
        this.bill_period = bill_period;
    }

    public int getApprove_status() {
        return approve_status;
    }

    public void setApprove_status(int approve_status) {
        this.approve_status = approve_status;
    }

    public int getPricetype() {
        return pricetype;
    }

    public void setPricetype(int pricetype) {
        this.pricetype = pricetype;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public int getBill_quantity() {
        return bill_quantity;
    }

    public void setBill_quantity(int bill_quantity) {
        this.bill_quantity = bill_quantity;
    }

    public String getKind() {
        return kind;
    }

    public void setKind(String kind) {
        this.kind = kind;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getOperatorFinance() {
        return operatorFinance;
    }

    public void setOperatorFinance(String operatorFinance) {
        this.operatorFinance = operatorFinance;
    }

    public String getCashitem() {
        return cashitem;
    }

    public void setCashitem(String cashitem) {
        this.cashitem = cashitem;
    }

    public double getCash() {
        return cash;
    }

    public void setCash(double cash) {
        this.cash = cash;
    }
}
