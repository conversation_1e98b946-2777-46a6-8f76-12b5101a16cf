package cn.sphd.miners.modules.equipment.entity;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 装备管理_供应商名录
 * 20230209 1.247装备器具新增对象 t_equ_supplier
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
public class TEquSupplier {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 机构ID
     */

    private Long org;

    /**
     * 简称
     */

    private String name;

    /**
     * 全称
     */

    private String fullName;

    /**
     * 编号
     */

    private String code;

    /**
     * 关键字
     */

    private String keywords;

    /**
     * 已到位的供应数
     */

    private Long supplyCount;

    /**
     * 独家供应数
     */

    private Long exclusiveCount;

    /**
     * 不再供应的材料数
     */

    private Long cutCount;

    /**
     * 状态:0-停用,1-启用
     */

    private Long enabled;

    /**
     * 启停用时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date enabledTime;

    /**
     * 备注
     */

    private String memo;

    /**
     * 创建人id
     */

    private Long creator;

    /**
     * 创建人
     */

    private String createName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createDate;

    /**
     * 修改人id
     */

    private Long updator;

    /**
     * 修改人
     */

    private String updateName;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateDate;

    /**
     * 操作:1-增,2-删,3-改,4-开启合作,5-暂停合作
     */

    private Long operation;

    /**
     * 修改前记录ID
     */

    private Long previousId;

    /**
     * 版本号,每次修改+1
     */

    private Long versionNo;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setOrg(Long org) {
        this.org = org;
    }

    public Long getOrg() {
        return org;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getFullName() {
        return fullName;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setSupplyCount(Long supplyCount) {
        this.supplyCount = supplyCount;
    }

    public Long getSupplyCount() {
        return supplyCount;
    }

    public void setExclusiveCount(Long exclusiveCount) {
        this.exclusiveCount = exclusiveCount;
    }

    public Long getExclusiveCount() {
        return exclusiveCount;
    }

    public void setCutCount(Long cutCount) {
        this.cutCount = cutCount;
    }

    public Long getCutCount() {
        return cutCount;
    }

    public void setEnabled(Long enabled) {
        this.enabled = enabled;
    }

    public Long getEnabled() {
        return enabled;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getMemo() {
        return memo;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Long getCreator() {
        return creator;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setUpdator(Long updator) {
        this.updator = updator;
    }

    public Long getUpdator() {
        return updator;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setOperation(Long operation) {
        this.operation = operation;
    }

    public Long getOperation() {
        return operation;
    }

    public void setPreviousId(Long previousId) {
        this.previousId = previousId;
    }

    public Long getPreviousId() {
        return previousId;
    }

    public void setVersionNo(Long versionNo) {
        this.versionNo = versionNo;
    }

    public Long getVersionNo() {
        return versionNo;
    }
}
