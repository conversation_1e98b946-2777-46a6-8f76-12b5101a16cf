<template>
  <div id="equipmentInit" class="equipMentSupplier">
    <div v-if="mainNum === 'main'">
      <div>
        <div class="rowGap">
          <el-row>
            <el-col :span="19">欢迎启用“装备器具”！</el-col>
            <el-col :span="5">
              <span class='fa fa-blue gapRt' :class="{'fa-dot-circle-o': nextPattern, ' fa-circle-o': !nextPattern}" @click="checkNextStep"></span>
              <span>本页项目已设置完毕！</span>
              <el-button class="ty-right" type="primary" @click="nextStep">下一步</el-button>
            </el-col>
          </el-row>
        </div>
        <div class="hrLine"></div>
        <div>
          <el-row class="rowCon">
            <el-col :span="24">
              会计录入的固定资产与低值易耗品，及本页补录的装备器具，均进入本页的待处理。处理内容：确认是否属于装备器具，并对属于装备器具的进行分类。
            </el-col>
          </el-row>
          <div class="flexCol">
            <div>目前，待处理的数据共{{equipInitInfo.noCategory || 0}}条。</div>
            <el-link type="primary" @click="initJudgeState(1)">去处理</el-link>
          </div>
          <el-row class="rowCon">
            <el-col :span="20">
              <p>如存在会计录入至系统的固定资产或低值易耗品以外的装备器具，请“补录”！</p>
              <p class="ty-color-blue">强烈建议：对于新购买或新制作的装备器具，请走申购流程，请不要补录！</p>
            </el-col>
            <el-col :span="4" class="txtRight">
              <el-link type="primary" class="btnGap" @click="initJudgeState(2)">零星补录</el-link>
              <el-link type="primary" @click="initJudgeState(3)">批量补录</el-link>
            </el-col>
          </el-row>
          <div class="hrLine"></div>
          <div class="flexCol">
            <div>经确认：</div>
          </div>
          <div class="flexCol">
            <div>不属于装备器具的数据共{{equipInitInfo.noc || 0}}条</div>
            <el-link type="info" class="gapLt">去查看</el-link>
          </div>
          <div class="flexCol">
            <div>属于装备器具且已分类的数据共{{equipInitInfo.yetCategory || 0}}条</div>
            <el-link type="primary" @click="mainNum = 'page0';nameditio = 'hide';catgory = 'hide'">去查看</el-link>
          </div>
          <div class="hrLine"></div>
          <div class="flexCol">
            <div>装备器具现有{{listn1.length}}种</div>
            <el-link type="primary" @click="nameditio = 0;mainNum = 'hide';catgory = 'hide'">去管理</el-link>
          </div>
          <div class="flexCol">
            <div>装备器具现有{{listc1.length}}个一级类别</div>
            <el-link type="primary" @click="nameditio = 'hide';mainNum = 'hide';catgory = 0">去管理</el-link>
          </div>
          <div class="hrLine"></div>
          <div class="flexCol">
            <div>装备器具的供应商</div>
            <el-link type="primary" @click="addSupplierInfo()">新增</el-link>
          </div>
          <div class="flexCol">
            <div>处于正常状态的现有{{equipInitInfo.supplierCount}}个</div>
            <el-link type="primary" @click="initIndex()">去管理</el-link>
          </div>
          <div class="flexCol">
            <div>处于暂停采购状态的现有{{equipInitInfo.suspendSupplierCount}}个</div>
            <el-link type="primary" @click="suspendedSupplier()">去管理</el-link>
          </div>
        </div>
      </div>
    </div>
    <div class="mainCon mainCon2" v-if="mainNum === 2">
      <table class="preTab">
        <tr>
          <td>
            <p>您刚刚操作导入的装备器具共<span id="allImportNum">{{importDataOneList.length}}</span>条。</p>
            <p>以下<span id="redImportNum">{{redImportNum}}</span>条被标为红色的部分不符合右侧的要求，<span class="ty-color-red">无法保存至系统。</span></p>
            <p>“修改”无误后，方可点击“下一步”。"</p>
          </td>
          <td>
            <p>系统对表格各列数据的要求：</p>
            <p>“装备器具编号”不能重号；</p>
            <p>“装备器具名称”是必填项；</p>
            <p>“到厂日期”型式需为六位数字；</p>
            <p>“预期的使用寿命”中需填入正整数；</p>
            <p>“原值”中填入数据的小数点后需带有两位有效数字。</p>
          </td>
          <td>
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-3 gapRt" @click="uploadCancel">放弃</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" @click="uploadNext">下一步</span>
          </td>
        </tr>
      </table>
      <table class="ty-table ty-table-control">
        <thead>
        <tr>
          <td>装备器具编号</td>
          <td>装备器具名称</td>
          <td>型号</td>
          <td>单位</td>
          <td>到厂日期</td>
          <td>预期的使用寿命</td>
          <td>原值</td>
          <td>操作</td>
        </tr>
        </thead>
        <tbody id="import1">
        <tr v-for="(item, index) in importDataOneList" :key="index">
          <td :class="{ 'color-red': modelCodeCount(item) || item.modelCode === '' }">{{ item.modelCode || '' }}</td>
          <td :class="{ 'color-red': !item.equipmentName }">{{ item.equipmentName || '--' }}</td>
          <td>{{ item.modelName || '' }}</td>
          <td>{{ item.unit || '' }}</td>
          <td :class="{ 'color-red': !((item.rDate || '').length === 6 && (new RegExp('^[0-9a-zA-Z]{6,16}$')).test(item.rDate)) || item.rDate === '' }">{{ item.rDate || '' }}</td>
          <td :class="{ 'color-red': !((item.oLifeSpan && item.oLifeSpan%1 === 0 && item.oLifeSpan>0 ) && item.oLifeSpan !== '') }"> {{item.oLifeSpan || ''}}</td>
          <td :class="{ 'color-red': !((item.oValue && (item.oValue).split('.')[1] && (item.oValue).split('.')[1].length === 2) || item.oValue === '') }">{{ item.oValue }}</td>
          <td>
            <span class="ty-color-blue funBtn" @click="editUpload(item,index, 1)">修改</span>
            <span class="ty-color-red funBtn" @click="delUpload(index)">删除</span>
          </td>
        </tr>
        </tbody>
      </table>
    </div>
    <div class="mainCon mainCon3" v-if="mainNum === 3">
      <p style="margin-top: 20px">
        本页各项如不需要可不操作，可直接点击“完成”。
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-right" @click="uploadComplete">完成</span>
        <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-3 ty-right gapRt" @click="uploadCancel">放弃</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-right" data-suptype="2" @click="addSup" style="margin-right: 50px;">增加供应商/加工方的选项</span>
      </p>
      <table class="ty-table ty-table-control" id="tab2">
        <tr>
          <td>装备器具编号/名称/型号</td>
          <td>类别</td>
          <td>到厂时的新旧情况</td>
          <td>供应商/加工方</td>
          <td>其他需要备注的内容</td>
          <td>操作</td>
        </tr>
        <tr v-for="(item, index) in importDataOneList" :key="index">
          <td>{{ item.modelCode }}/{{ item.equipmentName }}/{{ item.modelName }}</td>
          <td>
            <el-form-item>
              <el-input v-model="item.categoryName" placeholder="请录入" @click="filterCatBtn('importSelectCat', index)"/>
            </el-form-item>
          </td>
          <td>
            <el-form-item>
              <el-select  v-model="item.conditions" id="conditions" @change="editImportBtn(index,'conditions')">
                <el-option value="">请选择</el-option>
                <el-option :value=1 label="新"/>
                <el-option :value=2 label="旧"/>
              </el-select>
            </el-form-item>
          </td>
          <td>
            <el-form-item prop="supplier">
              <el-select v-model="item.supplier" placeholder="请选择" @change="editImportBtn(index,'supplier')">
                <el-option v-for="(item, index) in supplierList" :key="index" :value="item.id" :label="item['fullName']"/>
              </el-select>
            </el-form-item>
          </td>
          <td>
            <el-form-item>
              <el-input v-model="item.memo" placeholder="可录入不超过50字"  maxlength="50" @change="editImportBtn( index,'memo')"/>
            </el-form-item>
          </td>
          <td>
            <span class="ty-color-blue" @click="editUpload(item,index, 2)">修改</span>
            <span class="ty-color-red" @click="delUpload(index)">删除</span>
          </td>
        </tr>
      </table>
    </div>
    <div class="mainCon mainCon0" v-if="mainNum === 'page0'">
      <div class="gapBr">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainNum = 'main'">返 回</span>
      </div>
      <div class="rowGap">
        <el-row>
          <el-col :span="12">
            待处理的数据共{{equipInfo.noCategory || 0}}条
            <el-link type="primary" class="gapLt" @click="equipUnHandle()">去处理</el-link>
          </el-col>
          <el-col :span="12" class="txtRight">
            经处理，已确认不属于装备器具的数据共{{equipInfo.noc || 0}}条
            <el-link type="info" class="gapLt">去查看</el-link>
          </el-col>
        </el-row>
      </div>
      <div class="hrLine"></div>
      <el-row class="rowGap">
        <el-col :span="6">以下数据共{{equipInfo.yetCategory || 0}}条</el-col>
        <el-col :span="18">
          <div class="filter">
            <span class="gapRt">筛选</span>
            <input type="text" v-model="filterCat" readonly class="form-control funBtn" @click="filterCatBtn('filterCatBtn')"/>
            <i class="fa fa-angle-down"></i>
          </div>
        </el-col>
      </el-row>
      <div>
        <table class="ty-table ty-table-control">
          <thead>
          <tr>
            <td>装备器具名称/型号</td>
            <td>来源</td>
            <td>单位</td>
            <td>数量</td>
            <td>所属类别</td>
            <td>参与加工的产品</td>
            <td>操作</td>
          </tr>
          </thead>
          <tbody id="allList">
          <tr  v-for="(item, index) in equipInfo.data" :key="index">
            <td>{{ item.equipmentName || '' }}/{{ item.modelName }}</td>
            <td>{{ item.supplierName || '未知' }}</td>
            <td>{{ item.units || '' }}</td>
            <td>{{ item.length }}</td>
            <td>{{ item.path }}</td>
            <td class="ty-td-control"><span class="ty-color-blue">{{item.productCount || '0' }} 种</span></td>
            <td>
              <span class="ty-color-blue" @click="manageBtn(item)">管理</span>
            </td>
          </tr>
          </tbody>

        </table>
        <TyPage
            :curPage="equipInfo.pageInfo.currentPageNo" :pageSize="equipInfo.pageInfo.pageSize"
            :allPage="equipInfo.pageInfo.totalPage" :pageClickFun="pageInfoControl"></TyPage>
      </div>
    </div>
    <div class="mainCon mainCon1" v-if="mainNum === 'page1'">
      <div class="allWidth">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainNum = 'main'">返 回</span>
        <div class="ty-right alltip">
          <i class="fa fa-circle-o" :class="{'fa-dot-circle-o': sflag}" @click="toogeCircle" id="sflag"></i>
          待处理数据共{{equipNoCategory.noCategory || 0}}条，本次操作确认了{{readySetNum}}条。本次操作已完成！
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 gapLt" @click="selectCatSure()">确  定</span>
        </div>
      </div>
      <div>
        <div class="clear">
          <div class="ty-left">
            <div>以下为待处理的数据，请确认哪些属于装备器具，并对属于装备器具的选定类别。</div>
            <div>需分类的数据较多时，可“批量处理”。</div>
          </div>
          <span class="ty-right linkBtn" @click="batchClass">批量处理</span>
        </div>
        <table class="ty-table ty-table-control">
          <thead>
          <tr>
            <td>系统赋予的编码</td>
            <td>装备器具编号/名称/型号</td>
            <td>来源</td>
            <td colspan="2">所属类别</td>
          </tr>
          </thead>
          <tbody id="tbContent">
          <tr v-for="(item, index) in equipNoCategory.data" :class="{'isMore' : item.size > 1 , 'isOne':item.size === 1 }" :key="index">
            <td>{{ item.lpadId }}</td>
            <td>{{ item.modelCode || '' }}/{{ item.equipmentName || '' }}/{{ item.modelName }}</td>
            <td>{{ item.supplierName || '未知' }}</td>
            <td>{{ item.path === '--' ? '尚未选择':item.path  }}</td>
            <td class="grayBg" data-align="1" @click="getSameEqList(item, index);backPrePage = 'page1'"><i class="fa fa-sort-down"></i>
            </td>
          </tr>
          </tbody>
        </table>
        <TyPage
            :curPage="equipNoCategory.pageInfo.currentPageNo" :pageSize="equipNoCategory.pageInfo.pageSize"
            :allPage="equipNoCategory.pageInfo.totalPage" :pageClickFun="pageInfoUnHandle"></TyPage>
      </div>
    </div>
    <div class="mainCon mainCon2" v-if="mainNum === 'page2'">
      <div class="allWidth">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainNum = 'main'">返回主页</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 gapNr" @click="mainNum = 'page1'">返回上一页</span>
        <div class="ty-right">
          <i class="fa" :class="{'fa-circle-o': !dotState, 'fa-circle': dotState}" @click="toogeCircle2" id="toogeCircle"></i>
          待处理数据共<span id="weiNum">{{equipNoCategory.noCategory || 0}}</span>条，本次操作确认了<span id="selectNum">{{batchSelectNum}}</span>条。本次操作已完成！
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 gapLt" @click="saveBatchCat">确定</span>
        </div>
      </div>
      <div class="pa40">
        <p>点击类别右侧的“<span class="fa fa-angle-right"></span>”（进入符号）后，可在随后所见的页上选择属于该类别的装备器具。</p>
        <p>带有子类别的类别右侧无“<span class="fa fa-angle-right"></span>”，但左侧带有“<span class="fa fa-angle-double-down"></span>”（展开符号）。点击该符号后，将展示该类别下的子类别。</p>
      </div>
      <div id="batchClassCon">
        <el-tree :props="categoryProps" :load="loadBatch" lazy>
          <template #default="{ node, data }">
            <div>
              <span class="fa fa-angle-right angleBtn" @click="selectEqBtn(data)" v-if="data.childrens === 0"></span>
              <div class="catItemName funBtn">
                {{ data['name'] }}：{{ data['content'] }} （已选：<span class="selectNum">0</span>项）---- 子级类别 有 {{ data.childrens}}个
              </div>
            </div>
          </template>
        </el-tree>
      </div>
    </div>
    <div class="mainCon mainCon3" v-if="mainNum === 'page3'">
      <div class="gapPage">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainNum='main'">返回主页</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 gapNr" @click="mainNum= 'page2'">返回上一页</span>
        <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="selectEQOK" data-num="2">确 定</span>
      </div>
      <div style="padding:20px 0">
        <p>
          请选择属于<span id="catPath">{{selectedCatItem.name}}</span>的装备器具。
          <span class="ty-right selectCount">已选：<span id="cSelectNum">{{checkedNum}}</span>项</span>
        </p>
        <table class="ty-table" id="selectTab">
          <tbody>
          <tr>
            <td>系统赋予的编码</td>
            <td>固定资产编号</td>
            <td>固定资产名称</td>
            <td>型号</td>
            <td>创建</td>
          </tr>
          <tr v-for="(item, index) in batchEqData.list" :class="{'isMore' : item.size > 1 , 'isOne':item.size === 1 }" :key="index">
            <td>
              <div class="tdRela">
                <i class="fa" :class="{'fa-square-o': !item.checked, 'fa-check-square-o': item.checked}" @click="checkBtn(item, index)"></i>
                {{ item.lpadId || '' }}
              </div>
            </td>
            <td>{{ item.modelCode || '' }}</td>
            <td>{{ item.equipmentName || '' }}</td>
            <td>{{ item.modelName }}</td>
            <td>{{ item.createName }}{{ new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')}}</td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
    <div class="mainCon mainCon4" v-if="mainNum === 'page4'">
      <div class="gapPage">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="filterCatBack()">返回</span>
      </div>
      <div style="padding:20px 40px;">
        <p class="filterTxt">系统内的装备器具中，<span class="ty-color-blue">{{filterCat }}</span> 共 {{ filterEqListData.data.length }}台（套），具体如下：</p>
        <table class="ty-table ty-table-control">
          <thead>
          <tr>
            <td>装备器具名称/型号</td>
            <td>来源</td>
            <td>单位</td>
            <td>数量</td>
            <td>所属类别</td>
            <td>参与加工的产品</td>
            <td>操作</td>
          </tr>
          </thead>
          <tbody id="tbContent2">
          <tr v-for="(item, index) in filterEqListData.data" :key="index">
            <td>{{ item.equipmentName || '' }}/{{ item.modelName }}</td>
            <td>{{ item.supplierName || '未知' }}</td>
            <td>{{ item.units || '' }}</td>
            <td>{{ item.length }}</td>
            <td>{{ item.path }}</td>
            <td class="ty-td-control"><span class="ty-color-blue">{{ item.productCount || '0' }}种</span></td>
            <td>
              <span class="ty-color-blue" @click="manageBtn(item)">管理</span>
            </td>
          </tr>
          </tbody>
        </table>
        <TyPage
            :curPage="filterEqListData.pageInfo.currentPageNo" :pageSize="filterEqListData.pageInfo.pageSize"
            :allPage="filterEqListData.pageInfo.totalPage" :pageClickFun="pageInfoFilter"></TyPage>

      </div>

    </div>
    <div class="mainCon mainCon5" v-if="mainNum === 'page5'">
      <div class="gapPage">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainNum='main'">返回主页</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5 gapNr" @click="mainNum= backPrePage" id="subPage">返回上一页</span>
        <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="sameEqComplete" data-num="1">确 定</span>
        <div class="ty-right setTip"><i class="fa" :class="{'fa-circle-o': !finishState, 'fa-circle': finishState}" @click="toogeCircle3"></i>  设置完成！</div>
      </div>
      <div style="padding:40px 0;">
        <p><span class="sameTip">以下{{eqGroupList.length}}台设备的名称均为{{equipmentInfo.equipmentName}}，型号均为{{equipmentInfo.modelName}}，来源均为{{(equipmentInfo.supplierName == 0 ? '整机系自行装配（零部件可能为外购、外加工或自制）' : (equipmentInfo.supplierName || '未知'))}}。</span>为防遗漏，特一并列出，请为这些设备设置类别。</p>
        <div class="clear">
          <div class="ty-left">
            <div class="ty-color-blue">注1 设置时，可统一设置，也可分别设置。</div>
            <div class="ty-color-blue">注2 设置规则为，如选择了一二三四类中的某个类别，则不可以在选择一二三四类中的其他类别。</div>
          </div>
          <span class="ty-right ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="initUniform()">统一设置</span>
        </div>
        <table class="ty-table" id="selectCatPath">
          <tbody>
          <tr>
            <td>系统赋予的编码</td>
            <td>装备器具编号</td>
            <td>创建</td>
            <td colspan="2">所属类别</td>
          </tr>
          <tr v-for="(item, index) in eqGroupList" :key="index">
            <td>{{ item.lpadId }}</td>
            <td>{{ item.modelCode || '' }}</td>
            <td>{{ item.createName }} {{ new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss') }}</td>
            <td>{{ item.path === '--' ? '尚未选择':item.path }}</td>
            <td class="grayBg funBtn" @click="selectPathBtn(index)">
              <i class="fa fa-sort-down"></i>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="main" v-if="mainNum === 'sup1'">
      <div class="gapPage">
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainNum = 'main'">返回</span>
      </div>
      <div class="opinionCon">
        <div class="gapLg">本公司处于正常状态的供应商共<span>{{indexPage.totalResult}}</span>个，具体如下：</div>
        <el-table stripe class="ty-table-control" :data="supplierUsingList" max-height="700" border style="width: 100%">
          <el-table-column label="供应商" width="260">
            <template #default="scope">
              {{(scope.row.name || '')}} / {{ (scope.row.codeName || '')}}
            </template>
          </el-table-column>
          <el-table-column label="创建人" width="280">
            <template #default="scope">
              {{(scope.row.createName || '')}} {{new Date(scope.row.createDate).format('yyyy-MM-dd hh:mm:ss')}}
            </template>
          </el-table-column>
          <el-table-column label="已到位的装备器具">
            <template #default="scope">
              <div class="ty-td-control">
                <span class="ty-color-blue" @click="showEqMsg(scope.row)">{{scope.row["supplyCount"] || '0'}}种</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <span class="ty-color-blue" @click="lookSupplier(scope.row)">查看</span>
              <span class="ty-color-blue" @click="supplierManage(scope.row)">管理</span>
              <span class="ty-color-red" @click="delSupplier(scope.row)">删除</span>
              <span class="ty-color-blue" @click="stopSupplier(scope.row, 1)">暂停采购</span>
            </template>
          </el-table-column>
        </el-table>
        <div id="ye_suppliecontract"></div>
      </div>
      <div class="clr"></div>
    </div>
    <!-- 已暂停采购的供应商 -->
    <div class="ty-container" id="procurement" v-if="mainNum === 'sup2'">
      <div class="gapPage">
        <el-button type="primary" @click="mainNum = 'main'">返回上一页</el-button>
      </div>
      <div class="pageStyle container_item">
        <div class="initBody">
          <div class="title" style="margin-top: 42px;">
            本公司处于暂停采购状态的供应商共<span>{{supplierSuspendedList.length}}</span>个，具体如下：
          </div>
          <el-table stripe class="ty-table-control" :data="supplierSuspendedList" border style="width: 100%; margin-top: 20px;">
            <el-table-column prop="fullName" label="供应商"> </el-table-column>
            <el-table-column prop="codeName" label="代号"> </el-table-column>
            <el-table-column label="创建人">
              <template #default="scope">
                {{scope.row.createName}} {{new Date(scope.row["createDate"]).format('yyyy-MM-dd hh:mm:ss')}}
              </template>
            </el-table-column>
            <el-table-column label="“暂停采购”的操作者">
              <template #default="scope">
                {{scope.row.updateName}} {{new Date(scope.row["updateDate"]).format('yyyy-MM-dd hh:mm:ss')}}
              </template>
            </el-table-column>
            <el-table-column label="已到位的装备器具">
              <template #default="scope">
                <div class="ty-td-control">
                  <span class="ty-color-blue" @click="showEqMsg(scope.row)">{{scope.row.supplyCount}}项</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <span class="ty-color-blue" @click="lookSupplier(scope.row)">查看</span>
                <span class="ty-color-blue" @click="stopSupplier(scope.row, 2)">恢复采购</span>
              </template>
            </el-table-column>
          </el-table>
          <TyPage v-if="susPage"
                  :curPage="susPage.currentPageNo" :pageSize="susPage.pageSize"
                  :allPage="susPage.totalPage" :pageClickFun="susPageClick"></TyPage>
        </div>
      </div>
    </div>
    <!-- 搜索后显示的 -->
    <div class="ty-container" v-if="mainNum === 'sup3'">
      <div class="gapPage">
        <el-button type="primary" @click="mainNum = 'sup1'">返回上一页</el-button>
      </div>
      <div class="pageStyle container_item">
        <div class="initBody" id="chose">
          <div class="title" style="margin-top: 42px;">
            符合条件的数据共<span>XX</span>条，具体如下：
          </div>
          <table class="ty-table ty-table-control" id="message">
            <thead style="background: none;">
            <td>供应商</td>
            <td>创建人</td>
            <td>已到位的装备器具</td>
            <td>操作</td>
            </thead>
            <tbody></tbody>
            <tr v-for="(item, index) in supplierSearchList" :key="index">
              <td>{{item.fullName}}/{{item.codeName}}</td>
              <td>{{item.createName}} {{new Date(item["createDate"]).format('yyyy-MM-dd hh:mm:ss')}}</td>
              <td class="ty-td-control">
                <span class="ty-color-blue" @click="showEqMsg(item)">{{item.supplyCount}}种</span>
              </td>
              <td>
                <span class="ty-color-blue" @click="lookSupplier(item)">查看</span>
                <span class="ty-color-blue" @click="supplierManage(item)">修改</span>
                <span class="ty-color-red"  @click="delSupplier(item)">删除</span>
                <span class="ty-color-blue" @click="stopSupplier(item, 1)">暂停采购</span>
              </td>
            </tr>
          </table>
        </div>
      </div>
    </div>
    <!--  装备器具已到位 -->
    <div class="ty-container" v-if="mainNum === 'sup4'">
      <div class="gapPage">
        <span class="ty-btn ty-btn-big ty-btn-blue ty-circle-5" @click="mainNum = supBack">返回</span>
      </div>
      <div class="pageGap">
        <p class="eqMsg1 gapLg">以下装备器具已到位，共{{eqListCount.length}}项，系由{{supplierItem.fullName}}提供</p>
        <el-table stripe class="ty-table-control" :data="eqListCount" border style="width: 100%;">
          <el-table-column prop="lpadId" label="系统赋予的编码"> </el-table-column>
          <el-table-column prop="no" label="装备器具编号">
            <template #default="scope">
              {{scope.row.modelCode || ''}}
            </template>
          </el-table-column>
          <el-table-column label="装备器具名称">
            <template #default="scope">
              {{scope.row.equipmentName || ''}}
            </template>
          </el-table-column>
          <el-table-column label="型号">
            <template #default="scope">
              {{scope.row.modelName || ''}}
            </template>
          </el-table-column>
          <el-table-column prop="path" label="所属类别">
          </el-table-column>
          <el-table-column prop="no" label="创建">
            <template #default="scope">
              {{scope.row.createName}} {{new Date(scope.row.createDate).format('yyyy-MM-dd hh:mm:ss')}}
            </template>
          </el-table-column>
          <el-table-column label="参与加工的产品">
            <template #default="scope">
              <div class="ty-td-control"><span class="ty-color-blue">{{scope.row.productCount || '0'}}种</span></div>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <span class="ty-color-blue" @click="manageBtn(scope.row)">查看</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="nameagent" v-show="nameditio === 0">
      <div>
        <el-row class="gapPage">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainNum = 'main'">返回</span>
        </el-row>
        <div class="main">
          <el-row>
            <el-col :span="8">
              <div class="grid-content bg-purple plence" id="describe">
                装备器具现有<span class="number">{{listn1.length}}</span>种：具体如下
              </div>
            </el-col>
            <el-col :span="7"><div class="grid-content bg-purple-light"></div> </el-col>
            <el-col :span="6"><div class="grid-content bg-purple">
              <div>
                <el-row>
                  <el-col :span="4" class="catch">
                    <span>查找</span>
                  </el-col>
                  <el-col :span="16"><div class="grid-content bg-purple">
                    <el-input placeholder="请输入名称中的关键字" v-model="input3" class="input-with-select">
                      <!--                        <template #append>-->
                      <!--                          <el-button @click="" id="seachlook" type="primary" class="ty-btn-blue">确定</el-button>-->
                      <!--                        </template>-->
                    </el-input>
                  </div> </el-col>
                  <el-col :span="4"><div class="grid-content bg-purple-light">
                    <el-button @click="getsecher()" id="seachlook" type="primary">确定</el-button>
                  </div> </el-col>
                </el-row>
              </div>
            </div> </el-col>
            <el-col :span="1"><div class="grid-content bg-purple-light"></div> </el-col>
            <el-col :span="2"><div class="grid-content bg-purple">
              <el-button @click="addname()" id="addnewna" type="primary">新增名称</el-button>
            </div> </el-col>
          </el-row>
          <div class="equipmentdata upolence" style="margin-top: 20px;">
            <el-table :data="tableData" border>
              <el-table-column fixed prop="fullName" label="装备器具名称" width="200" align="center"></el-table-column>
              <el-table-column prop="modelCount" label="型号" width="170" align="center"></el-table-column>
              <el-table-column prop="supplierCount" label="来源" width="170" align="center"></el-table-column>
              <el-table-column prop="units" label="单位" width="160" align="center"></el-table-column>
              <el-table-column prop="quantity" label="数量" width="164" align="center">
                <template #default="scope">
                  <el-button class="impont-hover" type="typcolor" @click="quantitymess(scope.row,1)">{{ scope.row.quantity }}</el-button>
                </template>
              </el-table-column>
              <el-table-column prop="createDate" label="创建" width="320" align="center"></el-table-column>
              <el-table-column fixed="right" label="操作" width="342" align="center">
                <template #default="scope">
                  <el-button class="impont-hover" @click="upeqname(scope.row,1)" type="typcolor" size="small">修改</el-button>
                  <el-button class="impont-hover" @click="upnamerecord(scope.row,1)" type="typcolor"
                             size="small">名称修改记录</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!--          <table class="ty-table ty-table-control">-->
            <!--              <tr>-->
            <!--              <td>装备器具名称</td>-->
            <!--              <td>型号</td>-->
            <!--              <td>来源</td>-->
            <!--              <td>单位</td>-->
            <!--              <td>数量</td>-->
            <!--              <td>创建</td>-->
            <!--              <td>操作</td>-->
            <!--            </tr>-->
            <!--              <tr v-for="(itemn1,indexn1) in listn1" :key="indexn1">-->
            <!--                <td>{{ itemn1.fullName }}</td>-->
            <!--                <td>-->
            <!--                  <span class="chox2">{{ itemn1.modelCount }}</span>-->
            <!--                </td>-->
            <!--                <td>-->
            <!--                  <span class="choc2">{{ itemn1.supplierCount }}</span>-->
            <!--                </td>-->
            <!--                <td>{{ itemn1.units }}</td>-->
            <!--                <td>-->
            <!--                  <span class="tunch2 ty-color-blue funBtn" @click="">{{ itemn1.quantity }}</span>-->
            <!--                </td>-->
            <!--                <td>{{ itemn1.createDate }}</td>-->
            <!--                <td>-->
            <!--                  <span class="ty-color-blue fuBtn upeqname" @click="upeqname()">修改名称</span>-->
            <!--                  <span class="ty-color-blue funBtn" @click="">名称修个记录</span>-->
            <!--                  <span class="hd">{{ JSON.stringify(itemn1) }}</span>-->
            <!--                </td>-->
            <!--              </tr>-->
            <!--          </table>-->
          </div>
        </div>
        <TyPage v-if="pageShow"
                :curPage="pageInfo.currentPageNo" :pageSize="pageInfo.pageSize"
                :allPage="pageInfo.totalPage" :pageClickFun="pageClick"></TyPage>
      </div>
      <TyDialog v-if="delVisiblen1" width="500" :dialogTitle="dialogTitle" color="blue" :dialogHide="hideFun1">
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="hideFun1">取消</el-button>
          <el-button class="bounce-ok" @click="tipOk1(1,1)" type="primary" v-show="addorupe === 1" id="addsubmit">确定</el-button>
          <el-button class="bounce-ok" @click="tipOk1(2,1)" type="primary" v-show="addorupe === 2" id="upmodify">确定</el-button>
        </template>
        <template #dialogBody>
          <div>
            <el-row class="plencen">
              <el-col :span="24"><div class="grid-content bg-purple-dark">
                <span>装备器具的名称</span>
              </div></el-col>
            </el-row>
            <el-input
                type="text"
                placeholder="请输入内容"
                v-model="text"
                maxlength="8"
                show-word-limit
                ref="unitern1"
            >
            </el-input>
          </div>
        </template>
      </TyDialog>
      <TyDialog v-if="delVisiblen2" width="500" dialogTitle="装备器具名称的修改记录" color="blue" :dialogHide="hideFun2">
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="hideFun2" type="primary">关闭</el-button>
        </template>
        <template #dialogBody>
          <div>
            <el-table :data="tableData2" border>
              <el-table-column prop="msgmade" label="事件" width="140" align="center"> </el-table-column>
              <el-table-column label="操作" width="210" align="center">
                <template #default="scope">
                  {{scope.row.createName}}&nbsp;{{scope.row.createDate}}
                </template>
              </el-table-column>
              <el-table-column prop="fullName" label="操作后的名称" align="center"> </el-table-column>
            </el-table>
          </div>
        </template>
      </TyDialog>
    </div>
    <div class="quantitydetails" v-show="nameditio === 1">
      <div class="plencew">
        <el-row class="gapPage">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="gobcken(1)">返回</span>
        </el-row>
        <el-row class="upolence">
          <el-col :span="8"><div class="grid-content bg-purple upolence2" v-for="(itemt,indext) in listt" :key="indext">
            <span>{{ itemt.name }}</span>共<span>{{ itemt.numb }}</span>台（套），按型号展示如下：
          </div> </el-col>
          <el-col :span="8"><div class="grid-content bg-purple-light"></div> </el-col>
          <el-col :span="8"><div class="grid-content bg-purple-light">
            <el-row>
              <el-col :span="8"><div class="grid-content bg-purple"></div> </el-col>
              <el-col :span="8"><div class="grid-content bg-purple-light"></div> </el-col>
              <el-col :span="8"><div class="grid-content bg-purple">
                <el-row>
                  <el-col :span="12"><div class="grid-content bg-purple"></div> </el-col>
                  <el-col :span="12"><div class="grid-content bg-purple-light">
                    <el-button type="text" @click="getlookall()">直接查看全部</el-button>
                  </div> </el-col>
                </el-row>
              </div> </el-col>
            </el-row>
          </div> </el-col>
        </el-row>
        <div class="tatle upolence">
          <el-table :data="tableData3" border>
            <el-table-column prop="modelName" label="型号" align="center"></el-table-column>
            <el-table-column prop="count" label="数量（台/套）" align="center"></el-table-column>
            <el-table-column prop="supplierCount" label="来源" align="center"></el-table-column>
            <el-table-column label="操作" align="center">
              <template #default="scope">
                <el-button class="impont-hover" @click="lookmore(scope.row)" type="typcolor" size="small">查看</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="screenboder" v-show="nameditio === 2">
      <div class="plencew">
        <el-row class="gapPage">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="gobcken(2)">返回</span>
        </el-row>
        <el-row class="upolence">
          <el-col :span="8"><div class="grid-content bg-purple upolence2" v-for="(item4,index4) in listr4" :key="index4">
            <span>{{ item4.modelName }}</span>的<span>{{ item4.name }}</span>共<sapn>{{ item4.longr }}</sapn>台（套），具体如下：
          </div> </el-col>
          <el-col :span="8"><div class="grid-content bg-purple-light"></div> </el-col>
          <el-col :span="8"><div class="grid-content bg-purple">
            <el-row>
              <el-col :span="4"><div class="grid-content bg-purple"></div> </el-col>
              <el-col :span="4" class="plpen"><div class="grid-content bg-purple-light">
                <span class="screen">筛选</span>
              </div> </el-col>
              <el-col :span="16"><div class="grid-content bg-purple">
                <el-select v-model="value" placeholder="请选择">
                  <el-option
                      v-for="item in options"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                  >
                  </el-option>
                </el-select>
              </div> </el-col>
            </el-row>
          </div> </el-col>
        </el-row>
        <div class="table4 upolence">
          <el-table :data="tableData4" border>
            <el-table-column fixed prop="lpadId" label="系统赋予的编码" width="230" align="center"></el-table-column>
            <el-table-column prop="modelCode" label="装备器具编号" width="190" align="center"></el-table-column>
            <el-table-column prop="supplierName" label="来源" width="390" align="center"></el-table-column>
            <el-table-column prop="path" label="所属类别" width="200" align="center"></el-table-column>
            <el-table-column label="创建" width="260" align="center">
              <template #default="scope">
                {{ scope.row.createName }}&nbsp;{{ scope.row.createDate }}
              </template>
            </el-table-column>
            <el-table-column prop="productCount" label="参与加工的产品" width="250" align="center">
              <template #default="scope">
                <el-button class="impont-hover" type="typcolor" @click="lookallmore(scope.row,1)">{{ scope.row.productCount }}</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="drawingnumber" v-show="nameditio === 3">
      <div class="plencew">
        <el-row class="gapPage">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5"  @click="gobcken(3)">返回</span>
        </el-row>
        <el-row class="upolence">
          <el-col :span="24"><div class="grid-content bg-purple-dark">
            <span>装备信息：{{ setbox.lpadId }}/{{ setbox.modelCode }}/{{ setbox.equipmentName }}/{{ setbox.modelName }}</span>
            <br />
            <span>本装备参与加工的产品：</span>
          </div></el-col>
        </el-row>
        <div class="table5 upolence">
          <el-table :data="tableData5" border>
            <el-table-column fixed prop="" label="图号（代号）" width="200" align="center"></el-table-column>
            <el-table-column prop="" label="名称" width="200" align="center"></el-table-column>
            <el-table-column prop="" label="规格" width="200" align="center"></el-table-column>
            <el-table-column prop="" label="型号" width="200" align="center"></el-table-column>
            <el-table-column prop="" label="计量单位" width="200" align="center"></el-table-column>
            <el-table-column prop="" label="本装备用于本产品的操作时间" width="350" align="center"></el-table-column>
            <el-table-column prop="" label="操作" width="170" align="center"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="categorycode" v-show="nameditio === 4">
      <div class="plencew">
        <el-row class="gapPage">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="gobcken(4)">返回</span>
        </el-row>
        <el-row class="upolence">
          <el-col :span="24"><div class="grid-content bg-purple-dark" v-for="(item5,index5) in listr5" :key="index5">
            <span>{{ item5.modelName }}</span>共<span>{{ item5.longr }}</span>台（套），具体如下:
          </div></el-col>
        </el-row>
        <div class="table6 upolence">
          <el-table :data="tableData6" border>
            <el-table-column fixed prop="lpadId" label="系统赋予的编码" width="210" align="center"></el-table-column>
            <el-table-column prop="modelCode" label="装备器具编号" width="210" align="center"></el-table-column>
            <el-table-column prop="modelName" label="型号" width="210" align="center"></el-table-column>
            <el-table-column prop="supplierName" label="来源" width="210" align="center"></el-table-column>
            <el-table-column prop="path" label="所属类别" width="210" align="center"></el-table-column>
            <el-table-column label="创建" width="260" align="center">
              <template #default="scope">
                {{ scope.row.createName }}&nbsp;{{ scope.row.createDate }}
              </template>
            </el-table-column>
            <el-table-column prop="productCount" label="参与加工的产品" width="210" align="center">
              <template #default="scope">
                <el-button class="impont-hover" type="typcolor" @click="lookallmore(scope.row,2)">{{ scope.row.productCount }}</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div class="choseagent" v-show="nameditio === 5">
      <div class="plencew">
        <el-row class="gapPage">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="gobcken(5)">返回</span>
        </el-row>
        <el-row class="upolence">
          <el-col :span="24"><div class="grid-content bg-purple-dark" v-for="(itemk5,indexk5) in listk5" :key="indexk5">
            装备器具现有<span>{{ itemk5.kd }}</span>种：具体如下
          </div></el-col>
        </el-row>
        <div class="equipmentdata upolence">
          <el-table :data="tableData7" border style="width: 100%">
            <el-table-column fixed prop="fullName" label="装备器具名称" width="200" align="center"></el-table-column>
            <el-table-column prop="modelCount" label="型号" width="170" align="center"></el-table-column>
            <el-table-column prop="supplierCount" label="来源" width="170" align="center"></el-table-column>
            <el-table-column prop="units" label="单位" width="160" align="center"></el-table-column>
            <el-table-column prop="quantity" label="数量" width="164" align="center">
              <template #default="scope">
                <el-button class="impont-hover" type="typcolor" @click="quantitymess(scope.row,2)">{{ scope.row.quantity }}</el-button>
              </template>
            </el-table-column>
            <el-table-column prop="createDate" label="创建" width="315" align="center"></el-table-column>
            <el-table-column fixed="right" label="操作" width="342" align="center">
              <template #default="scope">
                <el-button class="impont-hover" @click="upeqname(scope.row,2)" type="typcolor" size="small">修改</el-button>
                <el-button class="impont-hover" @click="upnamerecord(scope.row,2)" type="typcolor"
                           size="small">名称修改记录</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <TyDialog v-if="delVisiblen3" width="500" :dialogTitle="dialogTitle" color="blue" :dialogHide="hideFun3">
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="hideFun3">取消</el-button>
          <el-button class="bounce-ok" @click="tipOk1(1,2)" type="primary" v-show="addorupe === 1" id="addsubmit">确定</el-button>
          <el-button class="bounce-ok" @click="tipOk1(2,2)" type="primary" v-show="addorupe === 2" id="upmodify">确定</el-button>
        </template>
        <template #dialogBody>
          <div>
            <el-row class="plencen">
              <el-col :span="24"><div class="grid-content bg-purple-dark">
                <span>装备器具的名称</span>
              </div></el-col>
            </el-row>
            <el-input
                type="text"
                placeholder="请输入内容"
                v-model="text"
                maxlength="8"
                show-word-limit
                ref="unitern1"
            >
            </el-input>
          </div>
        </template>
      </TyDialog>
      <TyDialog v-if="delVisiblen4" width="500" dialogTitle="装备器具名称的修改记录" color="blue" :dialogHide="hideFun4">
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="hideFun4" type="primary">关闭</el-button>
        </template>
        <template #dialogBody>
          <div>
            <el-table :data="tableData2" border>
              <el-table-column prop="msgmade" label="事件" width="140" align="center"> </el-table-column>
              <el-table-column label="操作" width="210" align="center">
                <template #default="scope">
                  {{scope.row.createName}}&nbsp;{{scope.row.createDate}}
                </template>
              </el-table-column>
              <el-table-column prop="fullName" label="操作后的名称" align="center"> </el-table-column>
            </el-table>
          </div>
        </template>
      </TyDialog>
    </div>
    <div class="cateagent" v-show="catgory === 0">
      <div>
        <el-row class="gapPage">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainNum = 'main'">返回</span>
        </el-row>
        <div class="main rolor">
          <el-row>
            <el-col :span="8"><div class="grid-content bg-purple plence">
              装备器具现有如下<span>{{ listc1.length }}</span>个一级类别。您可“新增一级类别”，也可管理各直属子类别。
              <br />
              此外如必要，还可“停用”或“删除”某类别，或修改各类别的基本信息。
            </div> </el-col>
            <el-col :span="8"><div class="grid-content bg-purple-light"></div> </el-col>
            <el-col :span="8"><div class="grid-content bg-purple-light">
              <el-row>
                <el-col :span="8"><div class="grid-content bg-purple catadd">
                  <el-button type="text" @click="stopcatfi()">被停用的类别</el-button>
                </div></el-col>
                <el-col :span="8"><div class="grid-content bg-purple-light catadd">
                  <el-button type="text" @click="openonecat()">新增一级类别</el-button>
                </div></el-col>
                <el-col :span="8"><div class="grid-content bg-purple catadd">
                  <el-button type="text" @click="morecatlook()">更多操作说明</el-button>
                </div></el-col>
              </el-row>
            </div> </el-col>
          </el-row>
          <div class="equipcatdata upolence">
            <el-table :data="tableData1" border>
              <el-table-column fixed prop="name" label="类别名称" width="354" align="center"></el-table-column>
              <el-table-column prop="content" label="所含内容/使用的主要场合" width="494" align="center"></el-table-column>
              <el-table-column prop="childrens" label="直属的子类别" width="240" align="center">
                <template #default="scope">
                  <el-button class="impont-hover diffenten" type="typcolor" @click="lookchilden(scope.row,1)">{{ scope.row.childrens }}个</el-button>
                </template>
              </el-table-column>
              <el-table-column fixed="right" label="操作" width="455" align="center">
                <template #default="scope">
                  <el-button class="impont-hover diffenten" @click="upbscination(scope.row)" type="typcolor" size="small">修改基本信息</el-button>
                  <el-button class="impont-hover2 diffentenb" @click="stopchose(scope.row)" type="typcolor" size="small">停用</el-button>
                  <el-button class="impont-hover2 diffentenb" @click="dettlen(scope.row,1)" type="typcolor" size="small">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <!--    一级弹窗层-->
        <TyDialog  v-if="delVisiblenc1" width="500" dialogTitle="新增一级类别" color="blue" :dialogHide="hideFunc1">
          <template #dialogFooter>
            <el-button class="bounce-cancel" @click="hideFunc1">取消</el-button>
            <el-button class="bounce-ok" :plain="true" @click="tipOkc1()" type="primary">确定</el-button>
          </template>
          <template #dialogBody>
            <div>
              <el-row class="plencen">
                <el-col :span="24"><div class="grid-content bg-purple-dark">
                  <span>一级类别名称</span>
                </div> </el-col>
              </el-row>
              <el-input
                  type="text"
                  v-model="text1"
                  maxlength="10"
                  show-word-limit
                  ref="unitern1"
              ></el-input>
              <el-row class="plencen">
                <el-col :span="24"><div class="grid-content bg-purple-dark">
                  <span>所含内容/使用的主要场合</span>
                </div> </el-col>
              </el-row>
              <el-input
                  type="text"
                  v-model="text2"
                  maxlength="30"
                  show-word-limit
                  ref="unitern2">
              </el-input>
            </div>
          </template>
        </TyDialog>
        <TyDialog v-if="delVisiblenc2" width="600" dialogTitle="更多操作说明" color="blue" :dialogHide="hideFunc2">
          <template #dialogFooter>
            <el-button class="bounce-ok" @click="hideFunc2">关闭</el-button>
          </template>
          <template #dialogBody>
            <div>
              <el-row>
                <el-col :span="1"><div class="grid-content bg-purple">1</div></el-col>
                <el-col :span="1"><div class="grid-content bg-purple-light"></div></el-col>
                <el-col :span="22"><div class="grid-content bg-purple">
                  <span>系统自带9各装备器具的一级类别</span>
                  <br />
                  <span>如需要，可创建各类别的同级类别或子类别，或修改各类别的基本信息。</span>
                </div></el-col>
                <el-col :span="0"><div class="grid-content bg-purple-light"></div></el-col>
              </el-row>
              <br />
              <el-row>
                <el-col :span="1"><div class="grid-content bg-purple">2</div></el-col>
                <el-col :span="1"><div class="grid-content bg-purple-light"></div> </el-col>
                <el-col :span="22"><div class="grid-content bg-purple">
                  <span>贵公司如已启用“工序管理”功能，一下内容需继续阅读，否则请忽略。</span>
                </div> </el-col>
                <el-col :span="0"><div class="grid-content bg-purple-light"></div> </el-col>
              </el-row>
              <br />
              <el-row>
                <el-col :span="1"><div class="grid-content bg-purple">3</div> </el-col>
                <el-col :span="1"><div class="grid-content bg-purple-light"></div> </el-col>
                <el-col :span="22"><div class="grid-content bg-purple">
                  <span>“工序管理”单元下</span>
                </div> </el-col>
                <el-col :span="0"><div class="grid-content bg-purple-light"></div> </el-col>
              </el-row>
              <br />
              <el-row>
                <el-col :span="1"><div class="grid-content bg-purple">3.1</div> </el-col>
                <el-col :span="1"><div class="grid-content bg-purple-light"></div> </el-col>
                <el-col :span="22"><div class="grid-content bg-purple">
                  <span>含有“制造用的机器/设备”“制造用的夹具、模具”与“检测用的设备/手段”三</span>
                  <br />
                  <span>个栏目。这三个栏目均需选择选项；</span>
                </div> </el-col>
                <el-col :span="0"><div class="grid-content bg-purple-light"></div> </el-col>
              </el-row>
              <br />
              <el-row>
                <el-col :span="1"><div class="grid-content bg-purple">3.2</div> </el-col>
                <el-col :span="1"><div class="grid-content bg-purple-light"></div> </el-col>
                <el-col :span="22"><div class="grid-content bg-purple">
                  <span>“制造用的机器/设备”的选项，默认为一类装备中的各装备器具；</span>
                </div> </el-col>
                <el-col :span="0"><div class="grid-content bg-purple-light"></div> </el-col>
              </el-row>
              <br />
              <el-row>
                <el-col :span="1"><div class="grid-content bg-purple">3.3</div> </el-col>
                <el-col :span="1"><div class="grid-content bg-purple-light"></div> </el-col>
                <el-col :span="22"><div class="grid-content bg-purple">
                  <span>“制造用的夹具、模具”的选项，默认为二类装备中的各装备器具；</span>
                </div> </el-col>
                <el-col :span="0"><div class="grid-content bg-purple-light"></div> </el-col>
              </el-row>
              <br />
              <el-row>
                <el-col :span="1"><div class="grid-content bg-purple">3.4</div> </el-col>
                <el-col :span="1"><div class="grid-content bg-purple-light"></div> </el-col>
                <el-col :span="22"><div class="grid-content bg-purple">
                  <span>“检测用的设备/手段”的选项，默认为三类装备与四类装备中的各装备器具；</span>
                </div> </el-col>
                <el-col :span="0"><div class="grid-content bg-purple-light"></div> </el-col>
              </el-row>
              <br />
              <el-row>
                <el-col :span="1"><div class="grid-content bg-purple">3.5</div> </el-col>
                <el-col :span="1"><div class="grid-content bg-purple-light"></div> </el-col>
                <el-col :span="22"><div class="grid-content bg-purple">
                  <span>如需要，可修改上述选项的范围，但需在“工序管理”中操作。</span>
                </div> </el-col>
                <el-col :span="0"><div class="grid-content bg-purple-light"></div> </el-col>
              </el-row>
            </div>
          </template>
        </TyDialog>
        <TyDialog v-if="delVisiblenc3" width="600" dialogTitle="修改类别的基本信息" color="blue" :dialogHide="hideFunc3">
          <template #dialogFooter>
            <el-button class="bounce-cancel" @click="hideFunc3">取消</el-button>
            <el-button class="bounce-ok parent" @click="uponemadsun(1)" type="primary" v-show="updent === 1">确定</el-button>
            <el-button class="bounce-ok child" @click="" type="primary" v-show="updent === 2">确定</el-button>
          </template>
          <template #dialogBody>
            <div>
              <el-row class="plencen">
                <el-col :span="8"><div class="grid-content bg-purple modev">
                  <span>当前类别名称</span>
                </div> </el-col>
                <el-col :span="8"><div class="grid-content bg-purple-light"></div> </el-col>
                <el-col :span="8"><div class="grid-content bg-purple">
                  <el-row>
                    <el-col :span="8"><div class="grid-content bg-purple"></div></el-col>
                    <el-col :span="8"><div class="grid-content bg-purple-light"></div></el-col>
                    <el-col :span="8"><div class="grid-content bg-purple">
                      <el-button type="text" @click="upcatlank">修改记录</el-button>
                    </div></el-col>
                  </el-row>
                </div></el-col>
              </el-row>
              <el-input v-model="input1" :disabled="true" class="diffencolor"> </el-input>
              <el-row class="plencen">
                <el-col :span="24"><div class="grid-content bg-purple-dark">
                  <span>当前所含内容/使用的主要场合</span>
                </div></el-col>
              </el-row>
              <el-input v-model="input2" :disabled="true"></el-input>
              <el-row class="plencen">
                <el-col :span="24"><div class="grid-content bg-purple-dark">
                  <span>新的类别名称</span>
                </div> </el-col>
              </el-row>
              <el-input type="text" placeholder="请输入内容" v-model="text3" maxlength="10" show-word-limit></el-input>
              <el-row class="plencen">
                <el-col :span="24"><div class="grid-content bg-purple-dark">
                  <span>新的所含内容/使用的主要场合</span>
                </div> </el-col>
              </el-row>
              <el-input type="text" placeholder="请输入内容" v-model="text4" maxlength="30" show-word-limit></el-input>
              <el-row class="plend1">
                <el-col :span="24"><div class="grid-content bg-purple-dark">
                  <span class="colorn">注：修改哪项填写哪项，不修改的无需理会！</span>
                </div></el-col>
              </el-row>
            </div>
          </template>
        </TyDialog>
        <TyDialog v-if="delVisiblenc5" width="500" dialogTitle="!提示" color="red" :dialogHide="hideFunc5">
          <template #dialogFooter>
            <el-button class="bounce-cancel" @click="hideFunc5">取消</el-button>
            <el-button class="bounce-ok parent" @click="stopcat" type="primary" v-show="stoptent === 1">确定</el-button>
            <el-button class="bounce-ok child" @click="stopcat2" type="primary" v-show="stoptent === 2">确定</el-button>
            <el-button class="bounce-ok detparent" @click="detsure(1)" type="primary" v-show="detpont === 1">确定</el-button>
            <el-button class="bounce-ok detchild" @click="detsure2(1)" type="primary" v-show="detpont === 2">确定</el-button>
          </template>
          <template #dialogBody>
            <div class="only">
              <el-row>
                <el-col :span="8"><div class="grid-content bg-purple"></div></el-col>
                <el-col :span="8"><div class="grid-content bg-purple-light">
                  <span v-show="kept === 1">确定停用该类别吗？</span>
                  <span v-show="kept === 2">确定删除该类别吗？</span>
                </div></el-col>
                <el-col :span="8"><div class="grid-content bg-purple"></div></el-col>
              </el-row>
            </div>
          </template>
        </TyDialog>
        <!--    二级弹窗层-->
        <TyDialog v-if="delVisiblenc4" width="1000" dialogTitle="修改记录" color="blue" :dialogHide="hideFunc4">
          <template #dialogFooter>
            <el-button class="bounce-ok" @click="hideFunc4">关闭</el-button>
          </template>
          <template #dialogBody>
            <div>
              <el-table :data="tableData4" border>
                <el-table-column fixed prop="msgmade" label="事件" width="140" align="center"></el-table-column>
                <el-table-column label="操作" width="230" align="center">
                  <template #default="scope">
                    {{scope.row.createName}}&nbsp;{{scope.row.createDate}}
                  </template>
                </el-table-column>
                <el-table-column prop="name" label="操作后的类别名称" width="175" align="center"></el-table-column>
                <el-table-column prop="content" label="操作后的所含内容/使用的主要场合" width="425" align="center"></el-table-column>
              </el-table>
            </div>
          </template>
        </TyDialog>
      </div>
    </div>
    <div class="stopcatoney" v-show="catgory === 1">
      <div class="plencew">
        <el-row class="gapPage">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="gotunback2">返回</span>
        </el-row>
        <el-row class="upolence">
          <el-col :span="24"><div class="grid-content bg-purple-dark">以下为被停用的类别：</div></el-col>
        </el-row>
        <div class="upolence">
          <el-table :data="tableData2" border>
            <el-table-column fixed prop="name" label="类别名称" width="360" align="center"></el-table-column>
            <el-table-column prop="content" label="所含内容/使用的主要场合" width="420" align="center"></el-table-column>
            <el-table-column label="停用时间" width="340" align="center">
              <template #default="scope">
                <!--              {{ scope.row.updateName }}&nbsp;{{ scope.row.enabledTime }}-->
                {{ scope.row.createName }}&nbsp;{{ scope.row.createDate }}
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="401" align="center">
              <template #default="scope">
                <el-button class="impont-hover diffenten" @click="restoreuse(scope.row)" type="typcolor" size="small">恢复使用</el-button>
                <el-button class="impont-hover2 diffentenb" @click="dettlen(scope.row,2)" type="typcolor" size="small">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <!-- 一级弹窗层 -->
      <TyDialog v-if="delVisiblenc6" width="500" dialogTitle="!提示" color="red" :dialogHide="hideFunc6">
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="hideFunc6">取消</el-button>
          <el-button class="bounce-ok parent" @click="restoredat()" type="primary" v-show="prestrt === 1">确定</el-button>
          <el-button class="bounce-ok child" @click="" type="primary" v-show="prestrt === 2">确定</el-button>
          <el-button class="bounce-ok detpar" @click="detsure(1)" type="primary" v-show="detprt == 1">确定</el-button>
          <el-button class="bounce-ok detchild" @click="" type="primary" v-show="detprt == 2">确定</el-button>
        </template>
        <template #dialogBody>
          <div class="only">
            <el-row>
              <el-col :span="7"><div class="grid-content bg-purple"></div> </el-col>
              <el-col :span="10"><div class="grid-content bg-purple-light">
                <span v-show="hfpt === 1">确定恢复使用该类别吗？</span>
                <span v-show="hfpt === 2" style="margin-left: 32px;">确定删除该类别吗？</span>
              </div> </el-col>
              <el-col :span="7"><div class="grid-content bg-purple"></div> </el-col>
            </el-row>
          </div>
        </template>
      </TyDialog>
    </div>
    <div class="diretsbaeory" v-show="catgory === 2">
      <div class="plencew">
        <el-row>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5"
              @click="gotunback(1)">返回</span>
        </el-row>
        <el-row class="upolence">
          <el-col :span="10"><div class="grid-content bg-purple upolence2" v-for="(itemd,indexd) in listc2" :key="indexd">
            直属于<span>{{ itemd.catname }}</span>的子类别现有<span>{{ itemd.longer }}</span>个。您可“新增同级子类别”，也可管理各直属的子类别。
            <br />
            此外如必要，还可“停用”或“删除”某类别，或修改各类别的基本信息。
          </div> </el-col>
          <el-col :span="6"><div class="grid-content bg-purple-light"></div> </el-col>
          <el-col :span="8"><div class="grid-content bg-purple">
            <el-row>
              <el-col :span="8"><div class="grid-content bg-purple"></div> </el-col>
              <el-col :span="8"><div class="grid-content bg-purple-light"></div> </el-col>
              <el-col :span="8"><div class="grid-content bg-purple">
                <el-row>
                  <el-col :span="9"><div class="grid-content bg-purple"></div> </el-col>
                  <el-col :span="15"><div class="grid-content bg-purple-light">
                    <el-button type="text" @click="addchidcat">新增同级子类别</el-button>
                  </div> </el-col>
                </el-row>
              </div> </el-col>
            </el-row>
          </div> </el-col>
        </el-row>
        <div class="tatle upolence">
          <el-table :data="tableData3" border>
            <el-table-column fixed prop="name" label="类别名称" width="" align="center"></el-table-column>
            <el-table-column prop="content" label="所含内容/使用的主要场合" width="" align="center"></el-table-column>
            <el-table-column prop="childrens" label="直属的子类别" width="" align="center">
              <template #default="scope">
                <el-button class="impont-hover diffenten" type="typcolor" @click="lookchilden(scope.row,2)">{{ scope.row.childrens }}</el-button>
              </template>
            </el-table-column>
            <el-table-column fixed="right" label="操作" width="" align="center">
              <template #default="scope">
                <el-button class="impont-hover diffenten" @click="upfoundation(scope.row)" type="typcolor" size="small">修改基本信息</el-button>
                <el-button class="impont-hover2 diffentenb" @click="stopuse(scope.row)" type="typcolor" size="small">停用</el-button>
                <el-button class="impont-hover2 diffentenb" @click="detaleune(scope.row,1)" type="typcolor" size="small">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <TyDialog v-if="delVisiblenc7" width="500" dialogTitle="新增同级子类别" color="blue" :dialogHide="hideFunc7">
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="hideFunc7">取消</el-button>
          <el-button class="bounce-ok" @click="tipOkc2()">确定</el-button>
        </template>
        <template #dialogBody>
          <div>新增<span class="simbcat">{{ catname }}</span>的同级子类别</div>
          <div>
            <el-row class="plencen">
              <el-col :span="24"><div class="grid-content bg-purple-dark">
                <span>子类别名称</span>
              </div> </el-col>
            </el-row>
            <el-input type="text" v-model="text5" maxlength="10" show-word-limit ref="unitern5"></el-input>
            <el-row class="plencen">
              <el-col :span="24"><div class="grid-content bg-purple-dark">
                <span>所含内容/使用的主要场合</span>
              </div> </el-col>
            </el-row>
            <el-input type="text" v-model="text6" maxlength="30" show-word-limit ref="unitern6"></el-input>
          </div>
        </template>
      </TyDialog>
      <TyDialog v-if="delVisiblenc8" width="600" dialogTitle="修改类别的基本信息" color="blue" :dialogHide="hideFunc8">
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="hideFunc8">取消</el-button>
          <!--        <el-button class="bounce-ok parent" @click="uponemadsun" type="primary" v-show="updent === 1">确定</el-button>-->
          <el-button class="bounce-ok child" @click="uponemadsun(2)" type="primary" v-show="updent2 === 2">确定</el-button>
        </template>
        <template #dialogBody>
          <div>
            <el-row class="plencen">
              <el-col :span="8"><div class="grid-content bg-purple modev">
                <span>当前类别名称</span>
              </div> </el-col>
              <el-col :span="8"><div class="grid-content bg-purple-light"></div> </el-col>
              <el-col :span="8"><div class="grid-content bg-purple">
                <el-row>
                  <el-col :span="8"><div class="grid-content bg-purple"></div></el-col>
                  <el-col :span="8"><div class="grid-content bg-purple-light"></div></el-col>
                  <el-col :span="8"><div class="grid-content bg-purple">
                    <el-button type="text" @click="upcatlank">修改记录</el-button>
                  </div></el-col>
                </el-row>
              </div></el-col>
            </el-row>
            <el-input v-model="input3" :disabled="true" class="diffencolor"> </el-input>
            <el-row class="plencen">
              <el-col :span="24"><div class="grid-content bg-purple-dark">
                <span>当前所含内容/使用的主要场合</span>
              </div></el-col>
            </el-row>
            <el-input v-model="input4" :disabled="true"></el-input>
            <el-row class="plencen">
              <el-col :span="24"><div class="grid-content bg-purple-dark">
                <span>新的类别名称</span>
              </div> </el-col>
            </el-row>
            <el-input type="text" placeholder="请输入内容" v-model="text7" maxlength="10" show-word-limit></el-input>
            <el-row class="plencen">
              <el-col :span="24"><div class="grid-content bg-purple-dark">
                <span>新的所含内容/使用的主要场合</span>
              </div> </el-col>
            </el-row>
            <el-input type="text" placeholder="请输入内容" v-model="text8" maxlength="30" show-word-limit></el-input>
            <el-row class="plend1">
              <el-col :span="24"><div class="grid-content bg-purple-dark">
                <span class="colorn">注：修改哪项填写哪项，不修改的无需理会！</span>
              </div></el-col>
            </el-row>
          </div>
        </template>
      </TyDialog>
      <TyDialog v-if="delVisiblenc9" width="500" dialogTitle="!提示" color="red" :dialogHide="hideFunc9">
        <template #dialogFooter>
          <el-button class="bounce-cancel" @click="hideFunc9">取消</el-button>
          <el-button class="bounce-ok parent" @click="stopcat" type="primary" v-show="stoptent === 1">确定</el-button>
          <el-button class="bounce-ok child" @click="stopcat2" type="primary" v-show="stoptent === 2">确定</el-button>
          <el-button class="bounce-ok detparent" @click="detsure(1)" type="primary" v-show="detpont === 1">确定</el-button>
          <el-button class="bounce-ok detchild" @click="detsure2(1)" type="primary" v-show="detpont === 2">确定</el-button>
        </template>
        <template #dialogBody>
          <div class="only">
            <el-row>
              <el-col :span="8"><div class="grid-content bg-purple"></div></el-col>
              <el-col :span="8"><div class="grid-content bg-purple-light">
                <span v-show="kept === 1">确定停用该类别吗？</span>
                <span v-show="kept === 2">确定删除该类别吗？</span>
              </div></el-col>
              <el-col :span="8"><div class="grid-content bg-purple"></div></el-col>
            </el-row>
          </div>
        </template>
      </TyDialog>
      <!--    二级弹窗层-->
      <TyDialog v-if="delVisiblenc10" width="1000" dialogTitle="修改记录" color="blue" :dialogHide="hideFunc10">
        <template #dialogFooter>
          <el-button class="bounce-ok" @click="hideFunc10">关闭</el-button>
        </template>
        <template #dialogBody>
          <div>
            <el-table :data="tableData4" border>
              <el-table-column fixed prop="msgmade" label="事件" width="140" align="center"></el-table-column>
              <el-table-column label="操作" width="230" align="center">
                <template #default="scope">
                  {{scope.row.createName}}&nbsp;{{scope.row.createDate}}
                </template>
              </el-table-column>
              <el-table-column prop="name" label="操作后的类别名称" width="175" align="center"></el-table-column>
              <el-table-column prop="content" label="操作后的所含内容/使用的主要场合" width="425" align="center"></el-table-column>
            </el-table>
          </div>
        </template>
      </TyDialog>
    </div>

    <TyDialog v-if="litAddLog" width="1100" dialogTitle="装备器具的零星补录" color="blue" :dialogHide="hideLitAddLog">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideLitAddLog('litAddLog')">取消</el-button>
        <el-button class="bounce-ok" @click="litAddOk()">确定</el-button>
      </template>
      <template #dialogBody>
        <el-form ref="baseFormRef"
                 :model="baseForm"
                 :rules="baseFormRules"
                 id="litAdd"
        >
          <table class="new">
            <tr class="noBr"><td colspan="5"><div class="linkBtn ty-right" @click="operDescription = true">关于数量的操作说明</div></td></tr>
            <tr>
              <td width="180px">
                <div>装备器具名称<span class="ty-color-red">*</span><span class="linkBtn ty-right" @click="addName">新增</span></div>
              </td>
              <td width="160px">
                <div>型号</div>
              </td>
              <td>
                <div>供应商/加工方<span class="linkBtn ty-right" @click="addSup">新增</span></div>
              </td>
              <td width="160px">
                <div>单位<span class="linkBtn ty-right" @click="addUnit">新增</span></div>
              </td>
              <td width="160px">
                <div>数量<span class="ty-color-red">*</span></div>
              </td>
            </tr>
            <tr>
              <td>
                <el-form-item prop="equipment">
                  <el-select v-model="baseForm.equipment" placeholder="请选择">
                    <el-option v-for="(item, index) in eqNameList" :key="index" :value="item.id" :label="item['fullName']"/>
                  </el-select>
                </el-form-item>
              </td>
              <td>
                <el-form-item prop="modelName">
                  <el-input v-model="baseForm.modelName" placeholder="请录入" />
                </el-form-item>
              </td>
              <td>
                <el-form-item prop="supplier">
                  <el-select v-model="baseForm.supplier" placeholder="请选择">
                    <el-option v-for="(item, index) in supplierList" :key="index" :value="item.id" :label="item['fullName']"/>
                  </el-select>
                </el-form-item>
              </td>
              <td>
                <el-form-item prop="unitId">
                  <el-select v-model="baseForm.unitId" placeholder="请选择">
                    <el-option v-for="(item, index) in unitList" :key="index" :value="item.id" :label="item['name']"/>
                  </el-select>
                </el-form-item>
              </td>
              <td>
                <el-form-item prop="amount">
                  <el-input :value="baseForm.amount" v-model="baseForm.amount" placeholder="请录入" @change="changeLitSnGroups()" @input="amountLimit" />
                </el-form-item>
              </td>
            </tr>
          </table>
          <table class="new eqOtherItems">
            <tr>
              <td>
                <div>装备器具编号</div>
              </td>
              <td>
                <div>原值</div>
              </td>
              <td>
                <div>预期的使用寿命</div>
              </td>
              <td>
                <div>到厂日期</div>
              </td>
              <td>
                <div>到厂时的新旧情况</div>
              </td>
              <td>
                <div>类别</div>
              </td>
              <td>
                <div>其他需要备注的内容</div>
              </td>
            </tr>
            <tr v-for="(item, index) in eqAddList" :key="index">
              <td>
                <el-form-item>
                  <el-input v-model="item.modelCode" placeholder="请录入" />
                </el-form-item>
              </td>
              <td>
                <el-form-item>
                  <el-input v-model="item.originalValue" placeholder="请录入" @input="limitOriginalValue(index)">
                    <template #append>元</template>
                  </el-input>
                </el-form-item>
              </td>
              <td>
                <el-form-item>
                  <el-input v-model="item.lifeSpan" placeholder="请录入" @input="limitLifeSpan(index)"  >
                    <template #append>年</template>
                  </el-input>
                </el-form-item>
              </td>
              <td>
                <el-form-item style="width: 150px;">
                  <el-date-picker
                      v-model="item.rDate"
                      type="month"
                      placeholder="请选择"
                      value-format="YYYYMM"
                  />
                </el-form-item>
              </td>
              <td>
                <el-form-item style="width: 130px;">
                  <el-select  v-model="item.conditions" id="conditions">
                    <el-option value="" label="请选择" />
                    <el-option value="1" label="新"/>
                    <el-option value="2" label="旧"/>
                  </el-select>
                </el-form-item>
              </td>
              <td>
                <el-form-item>
                  <el-input v-model="item.name" placeholder="请录入" @click="filterCatBtn('selectCatBtn', index)"/>
                </el-form-item>
              </td>
              <td>
                <el-form-item style="width: 140px;">
                  <el-input v-model="item.memo" placeholder="可录入不超过50字"  maxlength="50" />
                </el-form-item>
              </td>
            </tr>
          </table>
        </el-form>
      </template>
    </TyDialog>

    <TyDialog v-if="operDescription" width="800" dialogTitle="关于“数量”的操作说明" color="blue" :dialogHide="hideDesFun">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="hideDesFun()">知道了</el-button>
      </template>
      <template #dialogBody>
        <div id="msgCon">
          <p>1 “数量”大于“1”时，“装备器具名称”、“型号”、“供应商/加工方”及“单位”都需要有数据，否则无法成功点击“确定”按钮。</p>
          <p>2 “数量”大于“1”并成功点击“确定”后，数据给予保存。保存后，对列表中某装备器具的“装备器具名称”、“型号”、“供应商/加工方”或“单位”进行修改后，列表中其他“装备器具”的数据将跟随变动。</p>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="importEditLog" width="800" dialogTitle="装备器具基本信息的修改" color="blue" :dialogHide="hideAddFun" :dialogName="'importEditLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideAddFun('importEditLog')">取消</el-button>
        <el-button class="bounce-ok" @click="importEditOk">确定</el-button>
      </template>
      <template #dialogBody>
        <el-form ref="importPaseFormRef"
                 :model="importPaseForm"
                 :rules="importBaseFormRules"
                 label-position="top"
        >
          <table class="impotEdit">
            <tr>
              <td>
                <el-form-item label="装备器具名称" prop="equipment">
                  <el-select v-model="importPaseForm.equipment" placeholder="请选择">
                    <el-option v-for="(item, index) in eqNameList" :key="index" :value="item.id" :label="item['fullName']"/>
                  </el-select>
                </el-form-item>
              </td>
              <td>
                <el-form-item label="型号">
                  <el-input v-model="importPaseForm.modelName" placeholder="请录入" />
                </el-form-item>
              </td>
            </tr>
            <tr>
              <td>
                <el-form-item label="单位" prop="unitId">
                  <el-select v-model="importPaseForm.unitId" placeholder="请选择">
                    <el-option v-for="(item, index) in unitList" :key="index" :value="item.id" :label="item['name']"/>
                  </el-select>
                </el-form-item>
              </td>
              <td>
                <el-form-item label="装备器具编号">
                  <el-input v-model="importPaseForm.modelCode" placeholder="请录入" />
                </el-form-item>
              </td>
            </tr>
            <tr>
              <td>
                <el-form-item label="预期的使用寿命">
                  <el-input v-model="importPaseForm.oLifeSpan" placeholder="请录入" @input="limitOnlyInteger('oLifeSpan')" >
                    <template #append>年</template>
                  </el-input>
                </el-form-item>
              </td>
              <td>
                <el-form-item label="原值">
                  <el-input v-model="importPaseForm.oValue" placeholder="请录入" @input="getLimitVal('oValue')">
                    <template #append>元</template>
                  </el-input>
                </el-form-item>
              </td>
            </tr>
            <tr>
              <td>
                <el-form-item label="到厂日期">
                  <el-date-picker
                      v-model="importPaseForm.rDate"
                      type="month"
                      placeholder="请选择"
                      value-format="YYYYMM"
                  />
                </el-form-item>
              </td>
              <td>
              </td>
            </tr>
          </table>
        </el-form>
      </template>
    </TyDialog>
    <TyDialog v-if="uploadAddLog" width="600" dialogTitle="装备器具的批量补录" color="blue" :dialogHide="hideAddFun" :dialogName="'uploadAddLog'">
      <template #dialogFooter>
      </template>
      <template #dialogBody>
        <div>
          <div class="exportStep">
            <div class="stepItem">
              <p>第一步：点击“下载”，以下载空白的“装备器具清单”。</p>
              <div class="flexRow">
                <span>装备器具清单</span>
                <a :href="equipment_blank_sheet" download="装备器具清单.xls" class="ty-btn ty-btn-blue ty-btn-middle">下 载</a>
              </div>
            </div>
            <div class="stepItem">
              第二步：在空白的“装备器具清单”中填写内容，并存至电脑。
            </div>
            <div class="stepItem">
              <p>第三步：点击“浏览”，之后选择所保存的文件并上传。</p>
              <div class="flexRow">
                <div class="upload_sect viewBtn">
                  <uploadFile ref="uploadFile"
                              module="装备器具"
                              committed.async="false"
                              :showSelect="false"
                              :showFileList="false"
                              :autoUpload="true"
                              ext='.xls,.xlsx'
                              :successFun="handleSuccess"
                  >
                    <template #btnArea>
                      <span class="uploadify-button">{{ uploadAreaData.uploadBtn1_txt }}</span>
                    </template>
                  </uploadFile>
                </div>
                <div class="fileFullName">{{ uploadAreaData.originalFilename || '尚未选择文件' }}</div>
              </div>
            </div>
            <div class="stepItem">
              <p>第四步：点击“导入”。</p>
              <div class="flexRow">
                <span class="ty-btn ty-btn-yellow ty-btn-middle" @click="leadingHide">取 消</span>
                <span class="ty-btn ty-btn-blue ty-btn-middle" @click="importBtnOk">导 入</span>
              </div>
            </div>
            <div class="importIntro stepItem">
              <div style="text-align:left;color:red;"><span>导入说明：</span></div>
              <div style="text-align:left; font-size: 14px; line-height: 25px; ">
                <p>1、请勿增加或删除“装备器具清单”空白表的“列”，也不要修改修改各列的名字，否则上传会失败。</p>
                <p>2、“装备器具清单”“另存为”至电脑上时，可使用新的文件名，但点击本页面的“浏览”时如选错文件，上传可能失败。</p>
              </div>
            </div>
          </div>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="addNameLog" dialogTitle="新增名称" color="blue" :dialogHide="hideAddNameLog">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideAddNameLog">取消</el-button>
        <el-button class="bounce-ok" @click="addNameOk()">确定</el-button>
      </template>
      <template #dialogBody>
        <div>
          <el-form-item label="装备器具的名称" prop="eqName" style="display: block">
            <el-input v-model="eqName" placeholder="请录入" />
          </el-form-item>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="addSupLog" width="500" dialogTitle="增加供应商/加工方的选项" color="blue" :dialogHide="hideAddSupLog">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideAddSupLog">取消</el-button>
        <el-button class="bounce-ok" @click="addSupOk()">确定</el-button>
      </template>
      <template #dialogBody>
        <el-form
            ref="supplierRef"
            label-position="top"
            :model="supplierForm"
            :rules="supplierForRules"
            label-width="120px"
        >
          <el-form-item label="供应商/加工方名称" prop="fullName" placeholder="请录入">
            <el-input v-model="supplierForm.fullName" />
          </el-form-item>
          <el-form-item label="供应商/加工方简称" prop="name" placeholder="请录入">
            <el-input v-model="supplierForm.name" />
          </el-form-item>
          <el-form-item label="供应商/加工方代号" prop="codeName" placeholder="请录入">
            <el-input v-model="supplierForm.codeName" />
          </el-form-item>
          <span class="ty-color-blue">
                    注：对于供应商代号，系统仅要求不能重复，具体如何编号，请与采购部门沟通。如无规则请随意录入。
          </span>
        </el-form>
      </template>
    </TyDialog>
    <!--  新增计量单位   -->
    <TyDialog v-if="addUnitLog" width="500" dialogTitle="新增计量单位" color="blue" :dialogHide="hideAddUnitLog">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideAddUnitLog">取消</el-button>
        <el-button class="bounce-ok" @click="addUnitOkBtn(11)">确定</el-button>
      </template>
      <template #dialogBody>
        <el-form ref="unitForm" :model="unitForm" :rules="unitFormRules" label-width="80px" label-position="top">
          <el-form-item label="计量单位" prop="name">
            <el-input type="text" v-model="unitForm.name" placeholder="请录入计量单位的名称"></el-input>
          </el-form-item>
        </el-form>
      </template>
    </TyDialog>
    <!-- 导入失败 -->
    <TyDialog v-if="importFalseVisible" width="400" dialogTitle="！提示" color="red" :dialogHide="hideAddFun" :dialogName="'importFalseVisible'">
      <template #dialogFooter>
      </template>
      <template #dialogBody>
        <div>
          <div class="narrowBody">
            <h4>导入失败！</h4>
            <div>
              <div>原因可能为：</div>
              <div>1、修改了所下载表格中的“列”。</div>
              <div>2、选错了文件。</div>
              <div>3、文件太大，或里面含有图片等。</div>
            </div>
          </div>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="uploadDeltipLog" width="400" dialogTitle="！提示" color="red" :dialogHide="hideAddFun" :dialogName="'uploadDeltipLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideAddFun('uploadDeltipLog')">取消</el-button>
        <el-button class="bounce-ok" @click="uploadAddDeltipOk()">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center">
          确定删除这条数据吗？
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="abandonImportLog" width="400" dialogTitle="！提示" color="red" :dialogHide="hideAddFun" :dialogName="'abandonImportLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideAddFun('abandonImportLog')">取消</el-button>
        <el-button class="bounce-ok" @click="delAlltipOk()">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center">
          确定放弃全部数据吗？
        </div>
      </template>
    </TyDialog>
    <!-- 新增供应商-->
    <TyDialog v-if="addData.visible" width="1060" dialogTitle="新增供应商" color="blue" :dialogHide="dialogHideFun" :dialogName="'addData'">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="dialogHideFun('addData')">取消</span>
        <span class="ty-btn ty-btn-big ty-circle-5" :class="addDataOkBtn ? 'bounce-ok' : 'bounce-cancel'" @click="addSupplierDataOk">提交</span>
      </template>
      <template #dialogBody>
        <div class="supplierAddCC">
          <table>
            <tbody>
            <tr>
              <td colspan="6" class="ttl">基本信息</td>
            </tr>
            <tr>
              <td width="140px;"><span class="marL30"><span class="ty-color-red">*</span>供应商名称：</span></td>
              <td colspan="5"><el-input v-model="addData.base.fullName" placeholder="请录入"></el-input></td>
            </tr>
            <tr>
              <td><span class="marL30"><span class="ty-color-red">*</span>供应商简称：</span></td>
              <td colspan="3"><el-input v-model="addData.base.name" placeholder="请录入"></el-input></td>
              <td><span class="marL10"><span class="ty-color-red">*</span>供应商代号：</span></td>
              <td><el-input v-model="addData.base.codeName" placeholder="请录入"></el-input></td>
            </tr>
            <tr>
              <td><span class="marL30">全景照片：</span></td>
              <td colspan="5">
                <div class="pa20" >
                  <uploadFile ref="uploadFile" class="ty-right"
                              module="供应商"
                              committed.async="false"
                              :showSelect="false"
                              :showFileList="false"
                              :autoUpload="true"
                              ext='.png,.jpg,.jpeg,'
                              extMsg='请上传照片！'
                              :multiple="true"
                              :successFun="qjPicSuccess">
                    <template #btnArea>
                      <span class="ty-btn ty-btn-blue ">上传</span>
                    </template>
                  </uploadFile>
                </div>
                <span>(共可上传9张)</span>
              </td>
            </tr>
            <tr v-if="addData.base.quanImg.length > 0">
              <td></td>
              <td colspan="5">
                <div class="imgList">
                      <span class="imgI" v-for="(img, indexC2) in addData.base.quanImg" :key="indexC2">
                        <el-image class="img" :src="img.src" fit="cover" :preview-src-list="[img.src]"></el-image>
                        <span class="delBtn ty-color-red" @click="delImg(indexC2, 'quanImg')"><i class="fa fa-close"></i></span>
                      </span>
                </div>
              </td>
            </tr>
            <tr>
              <td colspan="2"><span class="marL30">是否接受挂账？</span></td>
              <td colspan="2">
                <span class="marL30">
                  <el-radio v-model="addData.base.chargeAcceptable" :label='1'>接受</el-radio>
                  <el-radio class="marL30" v-model="addData.base.chargeAcceptable" :label='0'>不接受</el-radio>
                </span>
              </td>
              <td><span class="marL30" v-if="addData.base.chargeAcceptable == 1">请录入已约定的账期</span></td>
              <td>
                <div v-if="addData.base.chargeAcceptable == 1">
                  <div class="lineRow"><el-input type="text" id="spcontwek" v-model="addData.base.chargePeriod" @input="getLimitVal('chargePeriod')" /></div>天
                </div>
              </td>
            </tr>
            <tr v-if="addData.base.chargeAcceptable == 1">
              <td colspan="2">
                <span class="marL30">请选择从何时开始计算账期？</span>
              </td>
              <td colspan="4">
                <span class="marL30">
                      <el-radio v-model="addData.base.chargeBegin" label="1">自货入库之日起</el-radio>
                      <el-radio  class="marL30" v-model="addData.base.chargeBegin" label="2">自发票入账之日起</el-radio>
                    </span>
              </td>
            </tr>
            <tr>
              <td colspan="2">
                <span class="marL30">是否需要预付款？</span>
              </td>
              <td colspan="5">
                <span class="marL30">
                      <el-radio v-model="addData.base.isImprest" label="1">需要</el-radio>
                      <el-radio  class="marL30" v-model="addData.base.isImprest" label="2">不需要</el-radio>
                      <el-radio  class="marL30" v-model="addData.base.isImprest" label="0">不确定</el-radio>
                    </span>
              </td>
            </tr>
            <tr v-if="addData.base.isImprest === '1'">
              <td colspan="2"><span class="marL30">请录入需预付的比例</span></td>
              <td colspan="2">
                <div class="lineRow marL30"><el-input v-model="addData.base.imprestProportion" label="0" @input="getLimitVal('imprestProportion')"></el-input></div>
                %
              </td>
              <td>
                <el-radio  class="marL30" v-model="addData.base.uncertainty" label="1" @click="uncertainty">比例不确定</el-radio>
              </td>
            </tr>
            <tr>
              <td colspan="6" class="ttl">开票信息</td>
            </tr>
            <tr>
              <td colspan="2"><span class="marL30"><span class="ty-color-red">*</span>该供应商是否能开发票？</span></td>
              <td colspan="4">
                <span class="marL30">
                  <el-radio v-model="addData.base.invoicable" label="1">是</el-radio>
                  <el-radio  class="marL30" v-model="addData.base.invoicable" label="2">否</el-radio>
                </span>
              </td>
            </tr>
            <tr v-if="addData.base.invoicable === '1'">
              <td colspan="2"><span class="marL30">是否能开增值税专用发票？</span></td>
              <td colspan="2">
                <span class="marL30">
                      <el-radio v-model="addData.base.vatsPayable" label="1">是</el-radio>
                      <el-radio  class="marL30" v-model="addData.base.vatsPayable" label="2">否</el-radio>
                    </span>
              </td>
              <td colspan="2">
                <div v-if="addData.base.vatsPayable === '1'">
                  <span class="marL30">
                  <span>请录入税率</span>
                </span>
                  <div class="lineRow marL30">
                    <el-input v-model="addData.base.taxRate" placeholder="请输入" @input="getLimitVal('taxRate')"></el-input>
                  </div>%
                </div>
              </td>
            </tr>
            <tr v-if="addData.base.invoicable === '1'">
              <td colspan="2"><span class="marL30">是否可接受汇票？</span></td>
              <td colspan="4">
                <span class="marL30">
                      <el-radio v-model="addData.base.draftAcceptable" label="1">可接受</el-radio>
                      <el-radio  class="marL30" v-model="addData.base.draftAcceptable" label="0">不确定</el-radio>
                    </span>
              </td>
            </tr>
            <tr>
              <td class=" ttl">合同信息</td>
              <td colspan="5">
                <span class="ty-btn ty-btn-blue marL50" @click="addContractInfo('add')"> 新增</span>
              </td>
            </tr>
            <tr v-if="addData.contract.tableData.length >0">
              <td colspan="6">
                <el-table :data="addData.contract.tableData" stripe border style="width: 100%">
                  <el-table-column prop="sn" label="合同编号" width="180"> </el-table-column>
                  <el-table-column label="签署日期" width="180">
                    <template #default="scope">
                      {{new Date(scope.row.signTime).format('yyyy-MM-dd')}}
                    </template>
                  </el-table-column>
                  <el-table-column prop="validTime" label="合同的有效期" width="220">
                    <template #default="scope">
                      {{$filter.format(scope.row.validTime?.[0], 'day')}} 至 {{$filter.format(scope.row.validTime?.[1], 'day')}}
                    </template>
                  </el-table-column>
                  <el-table-column label="操作">
                    <template #default="scope">
                      <el-button  @click="updateContract(scope.row,scope.$index)" type="text" size="small">修改</el-button>
                      <el-button @click="delContract(scope.row,scope.$index)" type="text" size="small">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </td>
            </tr>
            <tr>
              <td class=" ttl">邮寄信息</td>
              <td colspan="5">
                <span class="ty-btn ty-btn-blue marL50" @click="addMailBtn">新增</span>
              </td>
            </tr>
            <tr v-if="addData.mail.tableData.length > 0">
              <td colspan="6">
                <el-table :data="addData.mail.tableData" stripe border style="width: 100%">
                  <el-table-column prop="address" label="邮寄地址" width="180"> </el-table-column>
                  <el-table-column prop="recivePerson" label="联系人" width="180"> </el-table-column>
                  <el-table-column prop="mailNo" label="邮政编码" width="180"> </el-table-column>
                  <el-table-column prop="tel" label="手机" width="180"> </el-table-column>
                  <el-table-column label="操作">
                    <template #default="scope">
                      <el-button  @click="updateMail(scope.row, scope.$index)" type="text" size="small">修改</el-button>
                      <el-button @click="delMail(scope.row, scope.$index)" type="text" size="small">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </td>
            </tr>
            <tr>
              <td class=" ttl">联系人</td>
              <td colspan="5">
                <span class="ty-btn ty-btn-blue marL50" @click="addContact('contact')">新增</span>
              </td>
            </tr>
            <tr v-if="addData.contactLianxi.tableData.length > 0">
              <td colspan="6">
                <el-table v-if="addData.contactLianxi.tableData.length <= 1" :data="contactLianxiTwo" stripe border style="width: 50%">
                  <el-table-column prop="name" label="姓名" width="100"> </el-table-column>
                  <el-table-column prop="post" label="职位" width="200"> </el-table-column>
                  <el-table-column label="操作">
                    <template #default="scope">
                      <el-button @click="updateContact(scope.row, 1)" type="text" size="small">修改</el-button>
                      <el-button @click="delContact(scope.row, 1)" type="text" size="small">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <el-table v-else :data="contactLianxiTwo" stripe border style="width: 100%">
                  <el-table-column prop="name" label="姓名" width="80"> </el-table-column>
                  <el-table-column prop="post" label="职位" width="100"> </el-table-column>
                  <el-table-column label="操作">
                    <template #default="scope">
                      <el-button @click="updateContact(scope.row, 1)" type="text" size="small">修改</el-button>
                      <el-button @click="delContact(scope.row, 1)" type="text" size="small">删除</el-button>
                    </template>
                  </el-table-column>
                  <el-table-column prop="name2" label="姓名" width="80"> </el-table-column>
                  <el-table-column prop="post2" label="职位" width="100"> </el-table-column>
                  <el-table-column label="操作" >
                    <template #default="scope">
                          <span v-if="scope.row.name2">
                            <el-button @click="updateContact(scope.row, 2)" type="text" size="small">修改</el-button>
                            <el-button @click="delContact(scope.row, 2)" type="text" size="small">删除</el-button>
                          </span>
                    </template>
                  </el-table-column>
                </el-table>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="scanSupplierLog" width="1200" dialogTitle="供应商信息查看" color="blue" :dialogHide="dialogHideFun" :dialogName="'scanSupplierLog'">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="dialogHideFun('scanSupplierLog')">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="scanSupplierCon" style="margin: 43px;">
          <div class="part">
            <div class="flexItem">
              <span class="item_title">供应商名称:</span>
              <span class="item_content" style="width: 600px" id="see_name">{{seeSupplierData.info.fullName}}</span>
            </div>
            <div class="flexItem">
              <div class="flexItem">
                <span class="item_title">供应商简称:</span>
                <span class="item_content" id="see_fullname" style="width: 300px">{{seeSupplierData.info.fullName}}</span></div>
              <div class="flexItem" style="margin-left: 348px;">
                <span class="item_title" style="margin-right: 0;">供应商代号:</span>
                <span class="item_content" id="see_codde" style="width: 200px">{{seeSupplierData.info.codeName}}</span>
              </div>
            </div>
            <div class="flexItem">
              <div class="flexItem">
                <span class="item_title">创建者:</span>
                <span class="item_content">
                  <span id="see_createName">{{seeSupplierData.info.createName}}</span>
                  <span id="see_createDate">{{new Date(seeSupplierData.info.createDate).format('yyyy-MM-dd hh:mm:ss')}}</span>
                </span>
              </div>
              <div class="right_btn">
                <button class="ty-btn ty-btn-blue ty-circle-3" @click="getSuspendRecordList">
                  暂停采购/恢复采购的操作记录
                </button>
              </div>
              <div class="right_btn">
                <button class="ty-btn ty-btn-blue ty-circle-3" data-type="spbaserecord"
                        data-obj="see" @click="getRecordList('scanBase')">修改记录
                </button>
              </div>
            </div>
            <div class="item">
              <div class="item" id="prient" v-if="false">
                <span class="item_title" style="text-align: left;">暂停采购:</span>
                <span class="item_content" style="width: 300px"></span>
              </div>
            </div>
          </div>
          <div class="part">
            <div class="flexItem">
              <div class="item_title">全景照片：</div>
              <div class="item_content" id="overallImgUpload">
                <div class="imgsthumb">
                  <span class="imgI" v-for="(img, indexC2) in seeSupplierData.info.qImages" :key="indexC2">
                    <el-image class="img" :src="rootPath.fileUrl + img.normal" fit="contain" :preview-src-list="[rootPath.fileUrl + img.normal]" style="width: 100px; height: 80px"></el-image>
                  </span>
                </div>
              </div>
            </div>
            <div id="gotpose">
              <p style="margin: 0 0 0;">{{acceptable}}</p>
            </div>
          </div>
          <div class="part">
            <div class="flexItem" style="justify-content: space-between">
              <span class="item_title">开票信息</span>
              <button class="ty-btn ty-btn-blue ty-circle-3" data-type="spinvoiceRecords"
                      data-obj="see" @click="getRecordList('invoice')">
                修改记录
              </button>
            </div>
            <div id="shuil" style="display: flex">
              <p style="margin: 0 0 0;">{{invoicableInfo}}</p>
            </div>
          </div>
          <div class="addOtherInfo">
            <span class="par_ttl" style="margin-left: 8px;">合同信息</span>
            <span style="color: #5399c2;font-size: 0.8em;margin-left: 50px;"></span>
            <div class="dataList contractList" v-if="seeSupplierData.contractBaseList.length >0">
              <el-table :data="seeSupplierData.contractBaseList" stripe border width="100%">
                <el-table-column prop="sn" label="合同编号"> </el-table-column>
                <el-table-column label="签署日期">
                  <template #default="scope">
                    {{new Date(scope.row.signTime).format('yyyy-MM-dd')}}
                  </template>
                </el-table-column>
                <el-table-column label="合同的有效期">
                  <template #default="scope">
                    {{$filter.format(scope.row.validStart, 'day')}} 至 {{$filter.format(scope.row.validEnd, 'day')}}
                  </template>
                </el-table-column>
                <el-table-column label="操作">
                  <template #default="scope">
                          <span class="ty-td-control">
                            <span class="ty-color-blue" @click="contractIScan(scope.row,'scan')">查看</span>
                          </span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="addOtherInfo">
            <span class="par_ttl" style="margin-left: 8px;">邮寄信息</span>
            <div class="dataList receiveList" v-if="seeSupplierData.info.yjAddressList.length >0">
              <el-table :data="seeSupplierData.info.yjAddressList" stripe border style="width: 100%">
                <el-table-column prop="address" label="邮寄地址" width="340"> </el-table-column>
                <el-table-column prop="name" label="联系人" width="180"> </el-table-column>
                <el-table-column prop="postcode" label="邮政编码" width="180"> </el-table-column>
                <el-table-column prop="mobile" label="手机" width="180"> </el-table-column>
                <el-table-column label="操作">
                  <template #default="scope">
                          <span class="ty-td-control">
                            <span class="ty-color-blue" @click="getRecordList('mail',scope.row, scope.$index)" >修改记录</span>
                          </span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="addOtherInfo">
            <span class="par_ttl" style="margin-left: 8px;margin-right: 38px;">联系人</span>
            该供应商现共有如下<span class="contactNum">{{seeSupplierData.info.contactsList.length}}</span>位联系人
            <div class="dataList receiveListo">
              <el-table v-if="seeSupplierData.info.contactsList.length === 1" :data="seeSupplierData.info.contactLianxiTwo" stripe border style="width:55%">
                <el-table-column prop="name" label="姓名" width="100"> </el-table-column>
                <el-table-column prop="post" label="职位" width="100"> </el-table-column>
                <el-table-column prop="mobile" label="手机" width="110"> </el-table-column>
                <el-table-column label="操作">
                  <template #default="scope">
                          <span class="ty-td-control">
                            <span class="ty-color-blue" @click="getContactWay(scope.row, 1)">联系方式</span>
                            <span class="ty-color-blue" @click="seeNameID(scope.row, 0)">查看名片</span>
                            <span class="ty-color-blue" @click="getRecordList('contact',scope.row)">修改记录</span>
                          </span>
                  </template>
                </el-table-column>
              </el-table>
              <el-table v-if="seeSupplierData.info.contactsList.length > 1" :data="seeSupplierData.info.contactLianxiTwo" stripe border style="width: 100%">
                <el-table-column prop="name" label="姓名" width="80"> </el-table-column>
                <el-table-column prop="post" label="职位" width="100"> </el-table-column>
                <el-table-column prop="mobile" label="手机" width="100"> </el-table-column>
                <el-table-column label="操作">
                  <template #default="scope">
                           <span class="ty-td-control">
                            <span class="ty-color-blue" @click="getContactWay(scope.row, 1)">联系方式</span>
                            <span class="ty-color-blue" @click="seeNameID(scope.row, 1)">查看名片</span>
                            <span class="ty-color-blue" @click="getRecordList('contact',scope.row)">修改记录</span>
                          </span>
                  </template>
                </el-table-column>
                <el-table-column prop="name2" label="姓名" width="80"> </el-table-column>
                <el-table-column prop="post2" label="职位" width="100"> </el-table-column>
                <el-table-column prop="mobile2" label="手机" width="100"> </el-table-column>
                <el-table-column label="操作" >
                  <template #default="scope">
                          <span class="ty-td-control" v-if="scope.row.name2">
                            <span class="ty-color-blue" @click="getContactWay(scope.row, 2)">联系方式</span>
                            <span class="ty-color-blue" @click="seeNameID(scope.row, 2)">查看名片</span>
                            <span class="ty-color-blue" @click="getRecordList('contact',scope.row)">修改记录</span>
                          </span>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </template>
    </TyDialog>
    <!-- 联系人 名片-->
    <TyDialog class="receiveInfo" v-if="contactCard.visible" width="600" dialogTitle="名片" color="blue" :dialogHide="hideCrD">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideCrD">关闭</span>
      </template>
      <template #dialogBody>
        <div>
          <el-image
              style=" width: 550px; margin: 10px auto; height: 280px;"
              :src="contactCard.visitCard"
              fit="contain"
          >
            <template #placeholder>
              <div style="font-size: 28px; text-align: center; color: #666; line-height:100px; ">
                <i class="fa fa-spinner rotating"></i> 加载中 ...
              </div>
            </template>
            <template #error>
              <div style="font-size: 28px; text-align: center; color: #666; line-height:100px; ">
                <i class="fa fa-file-image-o"></i> 加载失败！
              </div>
            </template>
          </el-image>
        </div>
      </template>
    </TyDialog>

    <!-- 管理-->
    <TyDialog class="updateCustomerPanel" v-if="supplierManageData.visible" width="800" dialogTitle="供应商常规信息管理" color="blue" :dialogHide="hideUpdateCustomerFun">
      <template #dialogFooter>
        <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="hideUpdateCustomerFun">确定</span>
      </template>
      <template #dialogBody>
        <div class="" style="max-height:400px; ">
          <div class="cusName">{{ supplierManageData.editOrg.name }}</div>
          <table class="ty-table ty-table-control ty-table-txtLeft">
            <tbody>
            <tr class="manageTtl">
              <td>名称</td>
              <td>操作</td>
            </tr>
            <tr>
              <td>基本信息</td><td>
              <span class="ty-color-blue" @click="baseUpdate">修改</span>
              <span class="ty-color-blue" @click="getRecordList('scanBase')">修改记录</span>
            </td>
            </tr>
            <tr>
              <td>开票信息</td>
              <td>
                <span class="ty-color-blue" @click="invoiceManageUpdate">修改</span>
                <span class="ty-color-blue" @click="getRecordList('invoice')">修改记录</span>
              </td>
            </tr>
            <tr>
              <td>合同信息</td>
              <td>
                <span class="ty-color-blue" @click="addContractInfo('add')">新增</span>
                <span class="ty-color-blue" @click="seeExpireContract">已到期的合同</span>
                <span class="ty-color-blue" @click="seeStopContract">已暂停/终止的合同</span>
              </td>
            </tr>
            <tr v-for="(contractItem, contractIndex) in supplierManageData.contractBaseList" :key="contractIndex">
              <td class="pdL40" v-html="contractItem.sn"></td>
              <td>
                <span class="ty-color-blue" @click="contractIScan(contractItem)">查看</span>
                <span class="ty-color-blue" @click="manage_change(contractItem, contractIndex)">修改合同信息</span>
                <span class="ty-color-blue" @click="manage_renew(contractItem,1, contractIndex)">续约</span>
                <span class="ty-color-red" @click="manage_stop(contractItem, contractIndex)">暂停履约/合同终止</span>
              </td>
            </tr>
            <tr>
              <td>邮寄信息</td>
              <td>
                <span class="ty-color-blue" @click="fpAdd">新增</span>
                <span class="ty-color-blue" @click="adressStopList">已被停用的数据</span>
              </td>
            </tr>
            <tr v-for="(fpItem, fpIndex) in supplierManageData.info.yjAddressList" :key="fpIndex">
              <td class="pdL40" v-html="fpItem.address"></td>
              <td>
                <div v-if="fpItem.enabled">
                  <span class="ty-color-blue" @click="fpAdressUpdate(fpItem, fpIndex)">修改</span>
                  <span class="ty-color-blue" @click="getRecordList('mail',fpItem, fpIndex)">修改记录</span>
                  <span class="ty-color-red" @click="addressStopBtn(fpItem, fpIndex)">停用</span>
                </div>
              </td>
            </tr>
            <tr>
              <td>联系人</td>
              <td>
                <span class="ty-color-blue" @click="addContact('manageAdd')">新增</span>
                <span class="ty-color-blue" @click="addContactList">已被删除的数据</span>
              </td>
            </tr>
            <tr v-for="(contactItem, contactIndex) in supplierManageData.info.contactsList" :key="contactIndex">
              <td class="pdL40">
                  <span v-if="contactItem.enabled">
                    {{ contactItem.name }}  {{ contactItem.post }}  {{ contactItem.mobile }}
                  </span>
              </td>
              <td>
                <div v-if="contactItem.enabled">
                  <span class="ty-color-blue" @click="contactUpdate(contactItem, contactIndex)">修改</span>
                  <span class="ty-color-blue" @click="getRecordList('contactManage',contactItem)">修改记录</span>
                  <span class="ty-color-red" @click="contactDel(contactItem, contactIndex)">删除</span>
                </div>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>

    <!-- 供应商 基本信息 修改-->
    <TyDialog v-if="manageBaseDataLog" width="1060" dialogTitle="修改基本信息" color="blue" :dialogHide="dialogHideFun" :dialogName="'manageBaseDataLog'">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="dialogHideFun('manageBaseDataLog')">取消</span>
        <span class="ty-btn ty-btn-big ty-circle-5" :class="manageBaseOkBtn ? 'bounce-ok' : 'bounce-cancel'" @click="manageBaseOk">提交</span>
      </template>
      <template #dialogBody>
        <div class="supplierAddCC">
          <table>
            <tbody>
            <tr>
              <td colspan="6" class="ttl">基本信息</td>
            </tr>
            <tr>
              <td width="140px;"><span class="marL30"><span class="ty-color-red">*</span>供应商名称：</span></td>
              <td colspan="5"><el-input v-model="manageBaseData.fullName" placeholder="请录入"></el-input></td>
            </tr>
            <tr>
              <td><span class="marL30"><span class="ty-color-red">*</span>供应商简称：</span></td>
              <td colspan="3"><el-input v-model="manageBaseData.name" placeholder="请录入"></el-input></td>
              <td><span class="marL10"><span class="ty-color-red">*</span>供应商代号：</span></td>
              <td><el-input v-model="manageBaseData.codeName" placeholder="请录入"></el-input></td>
            </tr>
            <tr>
              <td><span class="marL30">全景照片：</span></td>
              <td colspan="5">
                <div class="pa20" >
                  <uploadFile ref="uploadFile" class="ty-right"
                              module="供应商"
                              committed.async="false"
                              :showSelect="false"
                              :showFileList="false"
                              :autoUpload="true"
                              ext='.png,.jpg,.jpeg,'
                              extMsg='请上传照片！'
                              :multiple="true"
                              :successFun="qjPicSuccess_manage">
                    <template #btnArea>
                      <span class="ty-btn ty-btn-blue ">上传</span>
                    </template>
                  </uploadFile>
                </div>
                <span>(共可上传9张)</span>
                <div class="imgList" v-if="manageBaseData.quanImg.length > 0">
                   <span class="imgI" v-for="(img, indexC2) in manageBaseData.quanImg" :key="indexC2">
                        <el-image class="img" :src="img.src" fit="cover" :preview-src-list="[img.src]"></el-image>
                        <span class="delBtn ty-color-red" @click="delImg(indexC2, 'quanImg', 'manage')"><i class="fa fa-close"></i></span>
                      </span>
                </div>
              </td>
            </tr>
            <tr>
              <td colspan="2"><span class="marL30">是否接受挂账？</span></td>
              <td colspan="2">
                <span class="marL30">
                  <el-radio v-model="manageBaseData.chargeAcceptable" :label='1'>接受</el-radio>
                  <el-radio class="marL30" v-model="manageBaseData.chargeAcceptable" :label='0'>不接受</el-radio>
                </span>
              </td>
              <td><span class="marL30" v-if="manageBaseData.chargeAcceptable == 1">请录入已约定的账期</span></td>
              <td>
                <div v-if="manageBaseData.chargeAcceptable == 1">
                  <div class="lineRow"><el-input type="text" id="spcontwek" v-model="manageBaseData.chargePeriod"  @input="getLimitVal('chargePeriodMg')" /></div>天
                </div>
              </td>
            </tr>
            <tr v-if="manageBaseData.chargeAcceptable == 1">
              <td colspan="2">
                <span class="marL30">请选择从何时开始计算账期？</span>
              </td>
              <td colspan="4">
                <span class="marL30">
                      <el-radio v-model="manageBaseData.chargeBegin" label="1">自货入库之日起</el-radio>
                      <el-radio  class="marL30" v-model="manageBaseData.chargeBegin" label="2">自发票入账之日起</el-radio>
                    </span>
              </td>
            </tr>
            <tr>
              <td colspan="2">
                <span class="marL30">是否需要预付款？</span>
              </td>
              <td colspan="5">
                <span class="marL30">
                      <el-radio v-model="manageBaseData.isImprest" label="1">需要</el-radio>
                      <el-radio  class="marL30" v-model="manageBaseData.isImprest" label="2">不需要</el-radio>
                      <el-radio  class="marL30" v-model="manageBaseData.isImprest" label="0">不确定</el-radio>
                    </span>
              </td>
            </tr>
            <tr v-if="manageBaseData.isImprest == '1'">
              <td colspan="2"><span class="marL30">请录入需预付的比例</span></td>
              <td colspan="2">
                <div class="lineRow marL30"><el-input v-model="manageBaseData.imprestProportion" @input="getLimitVal('imprestProportionMg')"></el-input></div>
                %
              </td>
              <td>
                <el-radio  class="marL30" v-model="manageBaseData.uncertainty" label="1" @click="uncertainty">比例不确定</el-radio>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>

    <!-- 修改发票-->
    <TyDialog v-if="manageInvoiceData.visible" width="1060" dialogTitle="修改开票信息" color="blue" :dialogHide="dialogHideFun" :dialogName="'manageInvoiceData'">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="dialogHideFun('manageInvoiceData')">取消</span>
        <span class="ty-btn ty-btn-big ty-circle-5 bounce-ok" @click="manageInvoiceOk">提交</span>
      </template>
      <template #dialogBody>
        <div class="supplierAddCC">
          <table>
            <tbody>
            <tr>
              <td colspan="2"><span class="marL30">该供应商是否能开发票？</span></td>
              <td colspan="4">
                <span class="marL30">
                  <el-radio v-model="manageInvoiceData.info.invoicable" label="1">是</el-radio>
                  <el-radio  class="marL30" v-model="manageInvoiceData.info.invoicable" label="2">否</el-radio>
                </span>
              </td>
            </tr>
            <tr v-if="manageInvoiceData.info.invoicable === '1'">
              <td colspan="2"><span class="marL30">是否能开增值税专用发票？</span></td>
              <td colspan="2">
                <span class="marL30">
                      <el-radio v-model="manageInvoiceData.info.vatsPayable" label='1'>是</el-radio>
                      <el-radio  class="marL30" v-model="manageInvoiceData.info.vatsPayable" label='2'>否</el-radio>
                    </span>
              </td>
              <td colspan="2">
                <div v-if="manageInvoiceData.info.vatsPayable == '1'">
                  <span class="marL30">
                  <span>请录入税率</span>
                </span>
                  <div class="lineRow marL30">
                    <el-input v-model="manageInvoiceData.info.taxRate" placeholder="请输入" @input="getLimitVal('taxRateMg')"></el-input>
                  </div>%
                </div>
              </td>
            </tr>
            <tr v-if="manageInvoiceData.info.invoicable == '1'">
              <td colspan="2"><span class="marL30">是否可接受汇票？</span></td>
              <td colspan="4">
                <span class="marL30">
                      <el-radio v-model="manageInvoiceData.info.draftAcceptable" label="1">可接受</el-radio>
                      <el-radio  class="marL30" v-model="manageInvoiceData.info.draftAcceptable" label="0">不确定</el-radio>
                    </span>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>
    <!-- 新增邮寄地址 -->
    <TyDialog v-if="mailInfoData.visible" width="460" :dialogTitle="mailInfoData.title" color="green" :dialogHide="dialogHideFun" :dialogName="'mailInfoData'">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="dialogHideFun('mailInfoData')">取消</span>
        <span class="ty-btn ty-btn-big ty-circle-5" :class="mailInfoOkBtn ? 'bounce-ok' : 'bounce-cancel'" @click="mailInfoOk">提交</span>
      </template>
      <template #dialogBody>
        <div class="newMailInfo">
          <table>
            <tbody>
            <tr>
              <td class="txtRight">邮寄地址：</td>
              <td><el-input v-model="mailInfoData.address" placeholder="请输入内容" clearable></el-input></td>
              <td></td>
            </tr>
            <tr>
              <td class="txtRight">邮寄编码：</td>
              <td><el-input v-model="mailInfoData.code" @change="chargeCode" placeholder="请输入内容" clearable></el-input>
              </td>
              <td></td>
            </tr>
            <tr v-if="!mailInfoData.mailValidSta" >
              <td></td>
              <td class="ty-color-red" style="font-size: 14px; line-height: 20px;">请输入正确的邮寄编码！</td>
              <td></td>
            </tr>
            <tr>
              <td class="txtRight">联系人：</td>
              <td>
                <div class="mask" @click="showContact('mailInfo')"></div>
                <el-input v-model="mailInfoData.contact.name" placeholder="请选择">
                  <template #append>
                    <i class="fa fa-angle-down"></i>
                  </template>
                </el-input></td>
              <td>
                <span class="ty-linkBtn" @click="addContact('mailInfo')">新增</span>
              </td>
            </tr>
            </tbody>
          </table>

        </div>
      </template>
    </TyDialog>

    <!-- 选择联系人-->
    <TyDialog class="chooseCusContact" v-if="chooseCusContactData.visible" width="460" dialogTitle="选择联系人" color="blue" :dialogHide="dialogHideFun" :dialogName="'chooseCusContact'">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="dialogHideFun('chooseCusContact')">取消</span>
        <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="chooseCusContactOk">确定</span>
      </template>
      <template #dialogBody>
        <div class="chooseCusContactcc">
          <div>以下为可供选择的客户联系人</div>
          <div class="table">
            <table>
              <tr v-for="(Conac, indexConac) in chooseCusContactData.contactList" :key="indexConac">
                <td width="80%">
                    <span @click="clickItem(Conac)" class="clickArea">
                      <i class="fa" :class="chooseCusContactData.selectConact && chooseCusContactData.selectConact.id == Conac.id ? 'fa-dot-circle-o' : 'fa-circle-o' "></i>
                      <span>{{ Conac.name }}</span>
                      <span>{{ Conac.post }}</span>
                      <span>{{ (Conac.info && Conac.info.tel ? Conac.info.tel : Conac.mobile)}}</span>
                    </span>
                </td>
                <td class="txtRight"><span class="ty-linkBtn" @click="detailsConac(Conac)">查看</span></td>
              </tr>
            </table>
          </div>
        </div>
      </template>
    </TyDialog>

    <!-- 新增联系人-->
    <TyDialog class="editContactLianXi" v-if="editContactLianXiData.visible" width="750" :dialogTitle="editContactLianXiData.title" color="green" :dialogHide="dialogHideFun" :dialogName="'contactLianXi'">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="dialogHideFun('contactLianXi')">取消</span>
        <span class="ty-btn ty-btn-big ty-circle-5" :class="editContactLianXiOkStatus ? 'bounce-ok':'bounce-cancel'" @click="editContactLianXiOk">确定</span>
      </template>
      <template #dialogBody>
        <div class="editContactLianXicc">
          <table>
            <tr>
              <td class="txtRight">联系人标签：</td>
              <td colspan="3">{{ editContactLianXiData.tag }}</td>
            </tr>
            <tr>
              <td class="txtRight">姓名：</td>
              <td><el-input v-model="editContactLianXiData.name" placeholder="请录入"></el-input></td>
              <td class="txtRight">职位：</td>
              <td><el-input v-model="editContactLianXiData.postName" placeholder="请录入"></el-input></td>
            </tr>
            <tr>
              <td class="txtRight">手机：</td>
              <td><el-input v-model="editContactLianXiData.tel" placeholder="请录入"></el-input></td>
              <td><span class="ty-linkBtn" @click="showOptionsFun">添加更多联系方式</span></td>
              <td>
                <el-select v-if="editContactLianXiData.showOptions" v-model="editContactLianXiData.defineType" placeholder="请选择" @change="setLink">
                  <el-option
                      v-for="(item, indexI) in editContactLianXiData.typeList"
                      :key="item.value"
                      :label="item.label"
                      :value="indexI"
                      :disabled="item.disabled"
                  >
                  </el-option>
                </el-select>
              </td>
            </tr>
            <tr v-for="(contact, index) in editContactLianXiData.contactList" :key="index">
              <td class="txtRight">{{ contact.name }}：</td>
              <td><el-input v-model="contact.val" @input="addContactVal(contact.val, index * 2)"></el-input><span class="ty-linkBtn-red" @click="delLink(contact, 1)">删除</span></td>
              <td class="txtRight"><span v-if="contact.name2">{{ contact.name2 }}：</span></td>
              <td>
                <div v-if="contact.name2" >
                  <el-input v-model="contact.val2" @input="addContactVal(contact.val2, index * 2+1)"></el-input><span class="ty-linkBtn-red" @click="delLink(contact, 2)">删除</span>
                </div>
              </td>
            </tr>
            <tr>
              <td class="txtRight">名片：</td>
              <td colspan="3">
              </td>
            </tr>
            <tr >
              <td class="txtRight"></td>
              <td colspan="3">
                <div v-if="editContactLianXiData.picInfo.src" class="imgcc">
                  <img :src="editContactLianXiData.picInfo.src" alt="名片">
                  <span class="ty-linkBtn-red" @click="delPic">删除</span>
                </div>
                <div v-else
                     v-loading="editContactLianXiData.loading"
                     element-loading-background="rgba(100, 100, 100, 0.1)"
                     element-loading-text="正在上传...."
                     style="width: 200px; min-height: 40px; max-height: 150px;">
                  <uploadFile ref="uploadFile"
                              module="初始化"
                              committed.async="false"
                              :showSelect="false"
                              :showFileList="false"
                              :autoUpload="true"
                              ext='.png,.jpg,.jpeg'
                              extMsg='请上传名片！'
                              :multiple="false"
                              :beforeUploadFun="beforeUploadLianXi"
                              :successFun="fileSuccessLianXi">
                    <template #btnArea>
                      <span class="ty-linkBtn lix">{{ editContactLianXiData.loadingBtn }}</span>
                    </template>
                  </uploadFile>
                </div>

              </td>

            </tr>
          </table>

        </div>
      </template>
    </TyDialog>

    <!-- 自定义标签-->
    <TyDialog class="useDefinedLabel" v-if="useDefinedLabelData.visible" width="450" dialogTitle="自定义标签" color="blue" :dialogHide="dialogHideFun" :dialogName="'useDefined'">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="dialogHideFun('useDefined')">取消</span>
        <span class="ty-btn ty-btn-big ty-circle-5" :class="useDefinedLabelData.val?'bounce-ok':'ty-btn-gray'" @click="useDefinedLabelOk">使用</span>
      </template>
      <template #dialogBody>
        <div class="useDefinedLabelcc">
          <span>自定义标签</span><br>
          <el-input v-model="useDefinedLabelData.val"></el-input>
        </div>
      </template>
    </TyDialog>

    <!-- 收货地址adress 停用 提示 -->
    <TyDialog v-if="stopAdresstData.visible" dialogTitle="提示" color="red" :dialogHide="hideShAdress">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideShAdress">关闭</span>
        <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="shAdressStopOK">确定</span>
      </template>
      <template #dialogBody>
        <div style="text-align: center; line-height: 50px;">
          确定停用吗？
        </div>
      </template>
    </TyDialog>

    <!-- 删除联系人提示 -->
    <TyDialog v-if="stopContactData.visible" dialogTitle="提示" color="red" :dialogHide="hideStopCt">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideStopCt">关闭</span>
        <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="stopCtOK">确定</span>
      </template>
      <template #dialogBody>
        <div style="text-align: center; line-height: 50px;">
          删除后，本联系人依旧显示在查看中，但仅可查看。
        </div>
      </template>
    </TyDialog>
    <!-- 已被停用的邮寄信息-->
    <TyDialog v-if="stopShAdressData.visible" width="600" dialogTitle="已被停用的邮寄信息" color="red" :dialogHide="hideStopShAdress">
      <template #dialogFooter>
        <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="hideStopShAdress">关闭</span>
      </template>
      <template #dialogBody>
        <div style="text-align: center; line-height: 30px; font-size:15px; padding:20px 0; ">
          <table class="ty-table ty-table-control ">
            <tbody>
            <tr><td class="txtLeft">名称</td><td class="txtLeft" style="padding-left:30px;">操作</td></tr>
            <tr v-for="(adressItem, adItm) in stopShAdressData.list" :key="adItm">
              <td class="txtLeft">{{ adressItem.address }}</td>
              <td class="txtLeft">
                <span class="ty-color-red" @click="startAdress(adressItem)">启用</span>
                <span class="ty-color-blue" @click="getRecordList('mail',adressItem)">修改记录</span>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>

    <!-- 已被删除的联系人数据-->
    <TyDialog v-if="contactStopData.visible" width="600" dialogTitle="已被删除的联系人" color="red" :dialogHide="hideContactStop">
      <template #dialogFooter>
        <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="hideContactStop">关闭</span>
      </template>
      <template #dialogBody>
        <div style="text-align: center; line-height: 30px; font-size:15px; padding:20px 0; ">
          <table class="ty-table ty-table-control ">
            <tbody>
            <tr><td class="txtLeft">名称</td><td class="txtLeft" style="padding-left:30px;">操作</td></tr>
            <tr v-for="(contactItem, ccii) in contactStopData.list" :key="ccii">
              <td class="txtLeft">
                {{ contactItem.name }}
                <span class="ty-right" v-html="(contactItem.post && contactItem.post.substr(0,8)) || ''"></span>
              </td>
              <td class="txtLeft">
                <span class="ty-color-blue" @click="getRecordList('contactManage',contactItem)">修改记录</span>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>

    <!-- 修改记录 -->
    <TyDialog class="updateRecords" v-if="updateRecordsData.visible" width="600" :dialogTitle="updateRecordsData.title" color="blue" :dialogHide="dialogHideFun" :dialogName="'recordsData'">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="dialogHideFun('recordsData')">关闭</span>
      </template>
      <template #dialogBody>
        <div class="recInfo">
          <span>{{ updateRecordsData.recordTip }}</span>
          <span v-if="this.updateRecordsData.type !== 'mail' && this.updateRecordsData.type !== 'contact'" class="ty-right" v-html="updateRecordsData.recordEditer"></span>
        </div>
        <div v-if="updateRecordsData.list.length > 0">
          <el-table :data="updateRecordsData.list"  border style="width: 100%">
            <el-table-column prop="infoTxt" label="记录" width="160"></el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                  <span class="ty-td-control">
                    <span class="ty-color-blue" @click="cus_recordDetail(scope.row,scope.$index)">查看</span>
                  </span>
              </template>
            </el-table-column>
            <el-table-column prop="handler" label="创建者/修改者"> </el-table-column>
          </el-table>

        </div>
      </template>
    </TyDialog>


    <!-- 已到期的合同 -->
    <TyDialog class="updateRecords" v-if="contractEndData.visible" width="600" dialogTitle="已到期的合同" color="blue" :dialogHide="dialogHideFun" :dialogName="'contractEndData'">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="dialogHideFun('contractEndData')">关闭</span>
      </template>
      <template #dialogBody>
        <div>
          <el-table :data="contractEndData.contractBaseList"  border style="width: 100%">
            <el-table-column prop="infoTxt" label="合同编号"></el-table-column>
            <el-table-column prop="infoTxt" label="到期日"></el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                  <span class="ty-td-control">
                    <span class="ty-color-blue" @click="contractIScan(scope.row, 'scan')">查看</span>
                    <span class="ty-color-blue" @click="manage_renew(scope.row)">续约</span>
                  </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </TyDialog>
    <!-- 已暂停/终止的合同 -->
    <TyDialog class="updateRecords" v-if="contractStopData.visible" width="600" dialogTitle="已暂停履约/终止的合同" color="blue" :dialogHide="dialogHideFun" :dialogName="'contractStopData'">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="dialogHideFun('contractStopData')">关闭</span>
      </template>
      <template #dialogBody>
        <div>
          <el-table :data="contractStopData.contractBaseList"  border style="width: 100%">
            <el-table-column prop="sn" label="合同编号" width="110"></el-table-column>
            <el-table-column label="暂停履约/终止的时间" width="230">
              <template #default="scope">
                {{scope.row.createName}} {{$filter.format(scope.row.createDate)}}
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                  <span class="ty-td-control">
                    <span class="ty-color-blue" @click="contractIScan(scope.row, 'scan')">查看</span>
                    <span class="ty-color-blue" @click="manage_recovery(scope.row)">恢复履约/重启合作</span>
                  </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </TyDialog>

    <!-- 删除供应商 -->
    <TyDialog v-if="delCusData.visible" width="600" dialogTitle="!提示" color="red" :dialogHide="hideDelCus">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideDelCus">取消</span>
        <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="delSupplierOK">确定</span>
      </template>
      <template #dialogBody>
        <div style="text-align: center; line-height: 50px;">
          确定删除吗?
        </div>
      </template>
    </TyDialog>
    <!-- 暂停合作 供应商 -->
    <TyDialog v-if="paulsCusData.visible" width="600" dialogTitle="！提示" color="blue" :dialogHide="hidePaulsCus">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hidePaulsCus">取消</span>
        <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="paulsCusOK">确定</span>
      </template>
      <template #dialogBody>
        <div style="text-align: left;padding: 12px 57px;" v-if="paulsCusData.type === 1">
          <p>
            对某供应商“暂停采购”意味着暂停其供应资格。点击本页面上的“确定”后：
          </p>
          <br />
          <p>
            1、为材料选择定点供应商时，该供应商不再出现于选项中。
          </p>
          <br />
          <p>
            2、再下订单时，为要采购的材料选择供应商时，该供应商不再出现于选项中。
          </p>
          <br />
          <br />
          <p>
            需要时，可在系统中对其“恢复采购”，但可购买其什么材料，需另行设置。
          </p>
          <br />
          <br />
          <p>
            确定暂停该供应商的供应资格吗？
          </p>
        </div>
        <div style="text-align: center; line-height: 50px;" v-if="paulsCusData.type === 2">
          确定恢复该供应商的供应资格吗？
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="baseRecordScanData.visible" width="900" dialogTitle="基本信息查看" color="blue" :dialogHide="dialogHideFun" :dialogName="'baseRecordScanLog'">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="dialogHideFun('baseRecordScanLog')">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="scanSupplierCon" style="margin: 43px;">
          <div class="part">
            <div class="flexItem">
              <span class="item_title">供应商名称:</span>
              <span class="item_content" style="width: 600px" id="see_name">{{baseRecordScanData.info.fullName}}</span>
            </div>
            <div class="flexItem">
              <div class="flexItem">
                <span class="item_title">供应商简称:</span>
                <span class="item_content" id="see_fullname" style="width: 300px">{{baseRecordScanData.info.name}}</span></div>
              <div class="flexItem">
                <span class="item_title" style="margin-right: 0;">供应商代号:</span>
                <span class="item_content" id="see_codde" style="width: 200px">{{baseRecordScanData.info.codeName}}</span>
              </div>
            </div>
          </div>
          <div class="part">
            <div class="flexItem">
              <div class="item_title">全景照片：</div>
              <div class="item_content" id="overallImgUpload">
                <div class="imgsthumb">
                  <span class="imgI" v-for="(img, indexC2) in baseRecordScanData.info.qImages" :key="indexC2">
                    <el-image class="img" :src="rootPath.fileUrl + img.normal" fit="contain" :preview-src-list="[rootPath.fileUrl + img.normal]" style="width: 100px; height: 80px"></el-image>
                  </span>
                </div>
              </div>
            </div>
            <div id="gotpose">
              <p>{{record_acceptable}}</p>
            </div>
          </div>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="invoiceRecordScanData.visible" width="800" dialogTitle="开票信息查看" color="blue" :dialogHide="dialogHideFun" :dialogName="'invoiceRecordScanLog'">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="dialogHideFun('invoiceRecordScanLog')">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="scanSupplierCon" style="margin: 43px;">
          <div class="part">
            <div class="flexItem">
              <span class="item_title">供应商名称:</span>
              <span class="item_content" style="width: 600px" id="see_name">{{invoiceRecordScanData.info.fullName}}</span>
            </div>
            <div class="flexItem">
              <div class="flexItem">
                <span class="item_title">供应商简称:</span>
                <span class="item_content" id="see_fullname" style="width: 300px">{{invoiceRecordScanData.info.fullName}}</span></div>
              <div class="flexItem">
                <span class="item_title" style="margin-right: 0;">供应商代号:</span>
                <span class="item_content" id="see_codde" style="width: 200px">{{invoiceRecordScanData.info.codeName}}</span>
              </div>
            </div>
          </div>
          <div class="part">
            <div id="shuil" style="display: flex">
              <p style="margin: 0 0 0;">{{record_invoicableInfo}}</p>
            </div>
          </div>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="mailRecordScanData.visible" width="800" dialogTitle="开票信息查看" color="blue" :dialogHide="dialogHideFun" :dialogName="'mailRecordScanLog'">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="dialogHideFun('mailRecordScanLog')">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="recordMain">
          <p class="fpDisUse ty-color-red" v-if="mailRecordScanData.info.operation == '2'">
            本地址于{{new Date(mailRecordScanData.info.enabledTime).format('yyyy/MM/dd hh:mm:ss')}}被{{mailRecordScanData.info.updateName}}{{mailRecordScanData.info.enabled == '1'?'启用。':'停用。'}}</p>
          <p>
            <span class="sale_ttl1">邮寄地址：</span>
            <span class="sale-con" id="fpAddress" :class="{'redFlag':compareRed(mailRecordScanData.front.address, mailRecordScanData.info.address)}">{{mailRecordScanData.info.address}}</span>
          </p>
          <p>
            <span class="sale_ttl1">发票接收人：</span>
            <span class="sale-con" id="fpName">{{compareD(mailRecordScanData.front.contact, mailRecordScanData.info.contact)}}</span>
          </p>
          <p>
            <span class="sale_ttl1">联系电话：</span>
            <span class="sale-con" id="fpMobile">{{compareD(mailRecordScanData.front.mobile, mailRecordScanData.info.mobile)}}</span>
          </p>
          <p>
            <span class="sale_ttl1">邮寄编码：</span>
            <span class="sale-con" id="fpNumber" :class="{'redFlag':compareRed(mailRecordScanData.front.postcode, mailRecordScanData.info.postcode)}">{{mailRecordScanData.info.postcode}}</span>
          </p>
        </div>
      </template>
    </TyDialog>

    <!-- 联系人修改记录 详情 -->
    <TyDialog class="editContactLianXi" v-if="contactUpdateLogDetailsData.visible" width="600" :dialogTitle="contactUpdateLogDetailsData.title" color="blue" :dialogHide="hidetitleFun">
      <template #dialogFooter>
        <span class="ty-btn ty-btn-big ty-circle-5 bounce-ok"  @click="hidetitleFun">关闭</span>
      </template>
      <template #dialogBody>
        <div class="editContactLianXicc contactUpdateLogDetails">
          <table>
            <tr>
              <td width="80">姓名：</td>
              <td v-html="contactUpdateLogDetailsData.name"></td>
              <td width="80">职位：</td>
              <td width="200" v-html="contactUpdateLogDetailsData.postName"></td>
            </tr>
            <tr>
              <td>手机：</td>
              <td v-html="contactUpdateLogDetailsData.tel"></td>
              <td></td>
              <td></td>
            </tr>
            <tr v-for="(contact, index) in contactUpdateLogDetailsData.contactList" :key="index">
              <td v-html="contact.name + ':'"></td>
              <td v-html="contact.val"></td>
              <td><span v-if="contact.name2" v-html="contact.name2 + ':'"></span></td>
              <td >
                <div v-if="contact.name2" v-html="contact.val2">
                </div>
              </td>
            </tr>
            <tr>
              <td>名片：</td>
              <td colspan="3"></td>
            </tr>
            <tr >
              <td></td>
              <td colspan="3">
                <div v-if="contactUpdateLogDetailsData.picSrc" class="imgcc">
                  <img :src="contactUpdateLogDetailsData.picSrc" alt="名片">
                </div>
              </td>
            </tr>
          </table>

        </div>
      </template>
    </TyDialog>
    <!-- 暂停合作/恢复合作的操作记录-->
    <TyDialog class="suspendRecord" v-if="suspendRecordData.visible" width="600" dialogTitle="暂停合作/恢复合作的操作记录" color="blue" :dialogHide="hideSuspendRecord">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideSuspendRecord">关闭</span>
      </template>
      <template #dialogBody>
        <div class="">
          <el-table :data="suspendRecordData.list" border style="width: 100%">
            <el-table-column prop="handleTxt" label="操作性质"> </el-table-column>
            <el-table-column prop="handler" label="操作者"> </el-table-column>
          </el-table>

        </div>
      </template>
    </TyDialog>
    <!-- 联系人联系方式 详情 -->
    <TyDialog class="editContactLianXi" v-if="contactDetailsData.visible" width="600" dialogTitle="供应商联系人" color="blue" :dialogHide="hideLianXiFun">
      <template #dialogFooter>
        <span class="ty-btn ty-btn-big ty-circle-5 bounce-ok"  @click="hideLianXiFun">关闭</span>
      </template>
      <template #dialogBody>
        <div class="contactFlex">
          <div style="display: inline-block;margin-left: 29px;">
            <span>姓名:</span>
            <span class="contact-con" id="con-contactName" v-html="contactDetailsData.name"></span>
          </div>
          <div style="display:inline-block;">
            <span class="sale_ttl1">职位:</span>
            <span class="contact-con" id="con-contactpost" v-html="contactDetailsData.postName"></span>
          </div>
        </div>
        <div class="contactflex">
          <div style="margin-left: 29px;">
            <span>联系人标签:</span>
            <span class="contact-con" id="con-contacttag">{{contactDetailsData.tags? contactDetailsData.tags: ''}}</span>
          </div>
        </div>
        <table class="see-otherContact ty-table" style="width: 442px;margin: 20px auto;">
          <tbody>
          <tr v-for="(contact, index) in contactDetailsData.contactList" :key="index">
            <td>{{contact.name}}</td>
            <td>{{contact.val}}</td>
            <td>{{contact.name2}}</td>
            <td>{{contact.val2}}</td>
          </tr>
          </tbody>
        </table>
      </template>
    </TyDialog>

    <!--  弹窗的高低顺序 是按照代码的前后顺序   -->
    <TyDialog v-if="addPurContractLog" width="600" :dialogTitle="editContractTitle" :dialogHide="hideFun" :dialogName="'addPurContractLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('addPurContractLog')">取消</el-button>
        <el-button class="bounce-ok" @click="addPurContractMid">确定</el-button>
      </template>
      <template #dialogBody>
        <div>
          <el-form
              :rules="editContractRules"
              label-position="top"
              ref="editContractForm"
              class="editContract"
              :model="form_editContract"
              label-width="110"
          >
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="供应商名称" prop="fullName" v-if="(rootCusBtn === 'update')">
                  <el-input v-model="form_editContract.supInfo.fullName" placeholder="请录入" readonly disabled/>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="合同编号" prop="sn">
                  <el-input v-model="form_editContract.sn" placeholder="请录入"/>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="签署日期" prop="signTime">
                  <el-date-picker
                      v-model="form_editContract.signTime"
                      type="date"
                      placeholder="请录入"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="合同的有效期" prop="validTime">
                  <el-date-picker
                      v-model="form_editContract.validTime"
                      type="daterange"
                      range-separator="到"
                      start-placeholder="请选择"
                      end-placeholder="请选择"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                      :unlink-panels="true"
                      :disabled-date="disabledDate"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="合同的扫描件或照片(共可上传9张)" prop="contractBaseImages" class="widthLimit">
              <template #label>
                合同的扫描件或照片(共可上传9张)
                <div style="float: right">
                  <el-button type="primary" link @click="$refs.uploadImgBtn.click()">上传</el-button>
                </div>
              </template>
              <div class="fileBox">
                <el-upload
                    v-model:file-list="form_editContract.contractBaseImages"
                    :headers="imgUpload.uploadHeaders"
                    :action="imgUpload.uploadAction"
                    :accept="imgUpload.uploadLyc"
                    :data="imgUpload.postData"
                    :limit="9"
                    :multiple="true"
                    :on-success="editContract_imgSuccess"
                    :on-preview="editContract_imgPreview"
                    :on-remove="editContract_imgRemove"
                    :on-exceed="editContract_imgHandleExceed"
                    list-type="picture-card"
                >
                  <span style="display: none" ref="uploadImgBtn">上传</span>
                </el-upload>
              </div>
            </el-form-item>
            <el-form-item label="合同的可编辑版" prop="fileList" class="widthLimit" width="300">
              <template #label>
                合同的可编辑版
                <div style="float: right">
                  <el-button type="primary" link @click="$refs.uploadFileBtn.click()">上传</el-button>
                </div>
              </template>
              <div class="fileBox">
                <el-upload
                    class="upload_contract-file"
                    v-model:file-list="form_editContract.fileList"
                    :headers="fileUpload.uploadHeaders"
                    :action="fileUpload.uploadAction"
                    :accept="fileUpload.uploadLyc"
                    :data="fileUpload.postData"
                    ref="upload"
                    :limit="1"
                    :on-exceed="editContract_fileHandleExceed"
                    :on-success="editContract_fileSuccess"
                >
                  <span style="display: none" ref="uploadFileBtn">上传</span>
                </el-upload>
              </div>
            </el-form-item>
            <el-form-item label="备注" prop="memo">
              <el-input v-model="form_editContract.memo" placeholder="请录入" show-word-limit maxlength="255"/>
            </el-form-item>
          </el-form>
        </div>
      </template>
    </TyDialog>
    <el-dialog v-model="dialog_visible_imgShow">
      <div class="text-center">
        <img w-full :src="imgShow" alt="Preview Image" style="width: 100%"/>
      </div>
    </el-dialog>
    <TyDialog v-if="dialog_visible_manage_img" width="700" dialogTitle="合同的扫描件或照片" :dialogHide="hideFun" :dialogName="'dialog_visible_manage_img'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('dialog_visible_manage_img')">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="mainCon" style="width: 600px; margin: 0 auto">
          <el-image
              v-for="(item, index) in list_seeContractImg.list"
              :key= index
              style="width: 80px; height: 80px"
              :src="item.url"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="list_seeContractImg.preview"
              :initial-index="index"
              fit="cover"
          />
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="dialog_visible_recoveryExpire" width="500" dialogTitle="！提示" :dialogHide="hideFun" :dialogName="'dialog_visible_recoveryExpire'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('dialog_visible_recoveryExpire')">关闭</el-button>
        <el-button type="primary" @click="manage_recoveryExpire_submit()">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="mainCon" style="width: 400px; margin: 0 auto">
          本合同已于{{recoveryExpire.validEnd}}到期。请确定具体要进行哪项操作：
          <el-radio-group v-model="recoveryExpire.radio" style="flex-direction: column; align-items: flex-start; margin: 16px 0; padding-left: 46px">
            <el-radio :value="1">不再执行，转入“已到期的合同”</el-radio>
            <el-radio :value="2">续约</el-radio>
          </el-radio-group>
        </div>
      </template>
    </TyDialog>

    <!-- 查看合同详情 -->
    <TyDialog v-if="contractScanData.visible" width="800" dialogTitle="查看合同" color="blue" :dialogHide="hideCScan">
      <template #dialogFooter>
        <span class="ty-btn ty-btn-big ty-circle-5 bounce-ok" @click="hideCScan">关闭</span>
      </template>
      <template #dialogBody>
        <div class="contractScan">
          <div style="line-height:40px; ">本版本合同的创建 {{ contractScanData.contractBase.createName }} {{ new Date( contractScanData.contractBase.createDate).format('yyyy-MM-dd hh:mm:ss') }}</div>
          <table class="ty-table ty-table-control ty-table-txtLeft" v-if="contractScanData.contractBase.sn">
            <tbody>
            <tr><td width="200px;">合同编号</td><td>{{ contractScanData.contractBase.sn }}</td></tr>
            <tr><td>签署日期</td><td>{{ new Date(contractScanData.contractBase.signTime).format('yyyy-MM-dd')   }}</td></tr>
            <tr><td>合同的有效期</td><td>{{ new Date(contractScanData.contractBase.validStart).format('yyyy-MM-dd') }} 至 {{ new Date(contractScanData.contractBase.validEnd).format('yyyy-MM-dd') }}</td></tr>
            <tr><td>合同的扫描件或照片</td><td>
              <el-image
                  v-for="(item, index) in contractScanData.listImage"
                  :key= index
                  style="width: 40px; height: 40px"
                  :src="item.src"
                  :zoom-rate="1.2"
                  :max-scale="7"
                  :min-scale="0.2"
                  :preview-src-list="contractScanData.listImage.previewList"
                  :initial-index="index"
                  fit="cover"
                  :preview-teleported="true"
              />
            </td></tr>
            <tr><td>合同的可编辑版</td><td><span class="ty-color-blue" @click="preview_File(contractScanData.contractBase.filePath)" v-if="contractScanData.contractBase.filePath">查看</span></td></tr>
            <tr><td>备注</td><td>{{ contractScanData.contractBase.memo }}</td></tr>
            <tr><td>本版本合同的修改记录</td><td><span class="ty-color-blue" @click="manage_changeHis(contractScanData.contractBase)">查看</span></td></tr>
            <tr><td>本合同的续约记录</td><td><span class="ty-color-blue" @click="manage_renewHis(contractScanData.contractBase)">查看</span></td></tr>
            </tbody>
          </table>
          <div class="txtRight hisList">
            <div v-for="(enIm, index) in contractScanData.listHis" :key="index">
              <span class="warpHis"> {{ enIm.suspend ? "暂停履约/终止合作" : "恢复履约/重启合作" }} </span>
              <span> {{ enIm.createName }} </span>
              <span> {{ new Date(enIm.suspendTime).format('yyyy-MM-dd hh:mm:ss')  }} </span>
            </div>
          </div>
        </div>
      </template>
    </TyDialog>

    <TyDialog v-if="dialog_visible_manage_renewHis" width="700" dialogTitle="本合同的续约记录" :dialogHide="hideFun" :dialogName="'dialog_visible_manage_renewHis'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('dialog_visible_manage_renewHis')">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="mainCon" style="width: 600px; margin: 0 auto">
          <el-table :data="seeContractRenewHis.list" border style="width: 100%; margin-top: 16px">
            <el-table-column label="版本" width="200">
              <template #default="scope">
                {{scope.$index === 0?'第1版（原始版本）':'第' + (scope.$index + 1) + '版（第' + scope.$index + '次续约后）'}}
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <el-button type="primary" link @click="manage_seeContractDetail(scope.row, 'renew')"><b>查看</b></el-button>
              </template>
            </el-table-column>
            <el-table-column label="创建（时间为各版本合同的续约时间）" width="300">
              <template #default="scope">
                {{scope.row.updateName || scope.row.createName}}
                {{$filter.format(scope.row.updateDate || scope.row.createDate)}}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="dialog_visible_manage_changeHis" width="700" dialogTitle="本版本合同的修改记录" :dialogHide="hideFun" :dialogName="'dialog_visible_manage_changeHis'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('dialog_visible_manage_changeHis')">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="mainCon" style="width: 600px; margin: 0 auto">
          <div class="row">
            当前数据为本版本合同第{{seeContractChangeHis.list.length - 1}}次修改后的结果。
            <div class="rightBtn">
              修改时间：{{$filter.format(seeContractChangeHis.list.at(-1)?.updateDate || seeContractChangeHis.list.at(-1)?.createDate)}}
            </div>
          </div>
          <el-table :data="seeContractChangeHis.list" border style="width: 100%; margin-top: 16px">
            <el-table-column label="记录">
              <template #default="scope">
                {{scope.$index === 0?'本版本合同的原始信息':'第' +scope.$index+ '次修改后'}}
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <el-button type="primary" link @click="manage_seeContractDetail(scope.row, 'change')"><b>查看</b></el-button>
              </template>
            </el-table-column>
            <el-table-column label="创建者/修改者" width="250">
              <template #default="scope">
                {{scope.row.updateName || scope.row.createName}}
                {{$filter.format(scope.row.updateDate || scope.row.createDate)}}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </template>
    </TyDialog>

    <TyDialog v-if="dialog_visible_seeContract" width="700" dialogTitle="查看合同" :dialogHide="hideFun" :dialogName="'dialog_visible_seeContract'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideFun('dialog_visible_seeContract')">关闭</el-button>
      </template>
      <template #dialogBody>
        <div class="mainCon" style="width: 600px; margin: 0 auto">
          <el-descriptions
              class="margin-top"
              :column="1"
              border
          >
            <el-descriptions-item label="合同编号" width="200">{{seeContractDetail.info.sn}}</el-descriptions-item>
            <el-descriptions-item label="签署日期">{{seeContractDetail.info.signTime}}</el-descriptions-item>
            <el-descriptions-item label="合同的有效期">
              {{seeContractDetail.info.validStart}} 至 {{seeContractDetail.info.validEnd}}
            </el-descriptions-item>
            <el-descriptions-item label="合同的扫描件或照片">
              <el-image
                  v-for="(item, index) in seeContractDetail.info.imgList"
                  :key= index
                  style="width: 40px; height: 40px"
                  :src="item.url"
                  :zoom-rate="1.2"
                  :max-scale="7"
                  :min-scale="0.2"
                  :preview-src-list="seeContractDetail.info.previewList"
                  :initial-index="index"
                  fit="cover"
                  :preview-teleported="true"
              />
            </el-descriptions-item>
            <el-descriptions-item label="合同的可编辑版">
              <el-button type="primary" link @click="preview_File(seeContractDetail.info.filePath)" v-if="seeContractDetail.info.filePath"><b>查看</b></el-button>
            </el-descriptions-item>
            <el-descriptions-item label="备注">{{seeContractDetail.info.memo}}</el-descriptions-item>
            <el-descriptions-item label="本版本合同的修改记录" v-show="seeContractDetail.info.origin === 'renew'">
              <el-button type="primary" link @click="manage_changeHis(seeContractDetail.info)"><b>查看</b></el-button>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="eqScanLog" width="1000" dialogTitle="装备器具管理" color="blue" :dialogHide="hideEqScanLog">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="hideEqScanLog">关 闭</el-button>
      </template>
      <template #dialogBody>
        <div class="singleSect" v-if="eqScanList.length === 1">
          <span>基本信息</span>
        </div>
        <table class="ty-table ty-table-control">
          <tbody>
          <tr>
            <td>
              <div>装备器具名称</div>
            </td>
            <td>
              <div>型号</div>
            </td>
            <td>
              <div>供应商/加工方</div>
            </td>
            <td>
              <div>单位</div>
            </td>
            <td>
              <div>数量</div>
            </td>
            <td>
              <div>操作</div>
            </td>
          </tr>
          <tr>
            <td>
              <div class="name">{{(eqScanDetail.equipmentName || '')}}</div>
            </td>
            <td>
              <div class="model">{{eqScanDetail.modelName || ''}}</div>
            </td>
            <td>
              <div class="sup">{{eqScanDetail.supplierName || '整机系自行装配（零部件可能为外购、外加工或自制）'}}</div>
            </td>
            <td>
              <div class="unit">{{eqScanDetail.unitName || ''}}</div>
            </td>
            <td>
              <div class="eqCount">{{eqScanDetail.length}}</div>
            </td>
            <td>
              <span class="ty-color-blue" @click="updateBtn()">修改</span>
              <span class="ty-color-blue" @click="editLog(eqScanDetail,1)">修改记录</span>
            </td>
          </tr>
          </tbody>
        </table>
        <div class="moreBd" v-if="eqScanList.length > 1"></div>
        <div class="singleSect gapTp" v-if="eqScanList.length === 1">
          <span>其他信息</span>
        </div>
        <div class="eqDetails">
          <div v-for="(item, index) in eqScanList" :key="index">
            <div>
              <span>创建： {{item.createName}} {{new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')}}</span>
              <span>系统赋予的编码：{{item.lpadId}}</span>
              <span>装备器具的编号：{{item.modelCode}}</span>
              <span class="ty-right cateCon" title="${item.path}">类别：{{item.path}}</span>
            </div>
            <table class="ty-table ty-table-control">
              <tbody>
              <tr>
                <td>
                  <div>到厂日期</div>
                </td>
                <td>
                  <div>到厂时的新旧情况</div>
                </td>
                <td>
                  <div>原值</div>
                </td>
                <td>
                  <div>预期的使用寿命</div>
                </td>
                <td>
                  <div>备注</div>
                </td>
                <td>
                  <div>操作</div>
                </td>
              </tr>
              <tr>
                <td>
                  <div class="factDate">{{new Date(item.receive_date).format('yyyyMM')}}</div>
                </td>
                <td>
                  <div class="newOrOld"> {{item.conditions == 1 ? '新' : '' }}{{item.conditions == 2 ? '旧' : '' }}</div>
                </td>
                <td>
                  <div class="oral">{{(item.original_value ? item.original_value + '元' : '')}}</div>
                </td>
                <td>
                  <div class="expireYear">{{item.life_span || ''}}</div>
                </td>
                <td>
                  <div class="memo" :title="(item.memo || '')">{{item.memo || ""}}</div>
                </td>
                <td>
                  <span class="ty-color-blue" @click="updateEqBtn(item, index)">修改</span>
                  <span class="ty-color-blue" @click="editLog(item, 2)">修改记录</span>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="msgEditLog" width="1200" :dialogTitle="msgEditTtl" color="blue" :dialogHide="hideMsgEditLog">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideMsgEditLog">取消</el-button>
        <el-button class="bounce-ok" @click="editMsgOk()">确定</el-button>
      </template>
      <template #dialogBody>
        <el-form ref="baseFormRef"
                 :model="baseForm"
                 :rules="baseFormRules">
          <div class="ty-color-blue publicInfor" v-if="editSource === 1 && eqScanDetail.length > 1">注：本处的修改对于页面上的{{eqScanDetail.length}}台装备共同生效！</div>
          <div class="ty-color-blue morePartTip" v-if="editSource === 2 && eqScanDetail.length > 1">注：如修改装备器具的名称、型号或供应商/加工方，说明本台装备与其他{{ eqScanDetail.length-1 }}台装备不是相同的设备，修改后本台设备将不再展示于这些设备的列表中！</div>
          <table class="ty-table infoEdit">
            <tbody>
            <tr>
              <td>
                <div>装备器具名称<span class="ty-color-red">*</span><span class="linkBtn ty-right" @click="addName">新增</span></div>
              </td>
              <td width="150">
                <div>型号</div>
              </td>
              <td>
                <div>供应商/加工方<span class="linkBtn ty-right" @click="addSup">新增</span></div>
              </td>
              <td class="singlePart" v-if="editSource === 1 || editSource === 2 && baseForm.length === 1">
                <div>单位<span class="linkBtn ty-right" @click="addUnit">新增</span></div>
              </td>
              <td class="publicInfor" v-if="editSource === 1 && baseForm.length > 1">
                <div>数量</div>
              </td>
            </tr>
            <tr>
              <td>
                <el-form-item prop="equipment">
                  <el-select v-model="baseForm.equipment" placeholder="请选择">
                    <el-option v-for="item in eqNameList" :key="item.id" :value="item.id" :label="item['fullName']"/>
                  </el-select>
                </el-form-item>
              </td>
              <td>
                <el-form-item prop="modelName">
                  <el-input v-model="baseForm.modelName" placeholder="请录入" />
                </el-form-item>
              </td>
              <td>
                <el-form-item prop="supplier">
                  <el-select v-model="baseForm.supplier" placeholder="请选择">
                    <el-option v-for="item in supplierList" :key="item.id" :value="item.id" :label="item['fullName']"/>
                  </el-select>
                </el-form-item>
              </td>
              <td class="singlePart" v-if="editSource === 1 || editSource === 2 && baseForm.length === 1">
                <el-form-item prop="unitId">
                  <el-select v-model="baseForm.unitId" placeholder="请选择">
                    <el-option v-for="item in unitList" :key="item.id" :value="item.id" :label="item['name']"/>
                  </el-select>
                </el-form-item>
              </td>
              <td class="publicInfor" v-if="editSource === 1 && baseForm.length > 1">
                <el-form-item>
                  <el-input :value="baseForm.length" readonly @click="amountChangeTipLog = true" />
                </el-form-item>
              </td>
            </tr>
            </tbody>
          </table>
          <div class="equipEdit" v-if="editSource === 2">
            <div class="ty-hr"></div>
            <table class="ty-table">
              <tbody>
              <tr>
                <td width="15%"><div>装备器具编号</div></td>
                <td width="15%"><div>原值</div></td>
                <td width="15%"><div>预期的使用寿命</div></td>
                <td width="10%"><div>到厂日期</div></td>
                <td width="15%"><div>到厂时的新旧情况</div></td>
                <td width="10%"><div>类别</div></td>
                <td width="20%"><div>其他需要备注的内容</div></td>
              </tr>
              <tr>
                <td>
                  <el-form-item prop="modelCode">
                    <el-input v-model="baseForm.modelCode" placeholder="请录入" />
                  </el-form-item>
                </td>
                <td>
                  <div class="limitPos">
                    <el-form-item>
                      <el-input v-model="baseForm.originalValue" placeholder="请录入" @input="getLimitVal('originalValue')">
                        <template #append>元</template>
                      </el-input>
                    </el-form-item>
                  </div>
                </td>
                <td>
                  <div class="limitPos">
                    <el-form-item>
                      <el-input v-model="baseForm.lifeSpan" placeholder="请录入" @input="limitOnlyInteger('lifeSpan')">
                        <template #append>年</template>
                      </el-input>
                    </el-form-item>
                  </div>
                </td>
                <td>
                  <el-form-item>
                    <el-date-picker
                        v-model="baseForm.rDate"
                        type="month"
                        placeholder="请选择"
                        value-format="YYYYMM"
                    />
                  </el-form-item>
                </td>
                <td>
                  <el-select v-model="baseForm.conditions" class="form-control" id="conditions">
                    <el-option :value=1 label="新" />
                    <el-option :value=2 label="旧" />
                  </el-select>
                </td>
                <td>
                  <el-form-item>
                    <el-input v-model="baseForm.path" placeholder="请录入" @click="filterCatBtn('msgEditSelectCatBtn')"/>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item>
                    <el-input v-model="baseForm.memo" placeholder="可录入不超过50字"  maxlength="50" show-word-limit />
                  </el-form-item>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </el-form>
      </template>
    </TyDialog>
    <TyDialog v-if="amountChangeTipLog" width="500" dialogTitle="！提示" color="blue" :dialogHide="hideAmountChangeTipLog">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="hideAmountChangeTipLog">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center">
          <div>增加装备的数量，可通过”补录“的方式！</div>
          <div>减少装备的数量，请进行”停用“的操作！</div>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="editRecordLog" width="700" dialogTitle="修改记录" color="blue" :dialogHide="hideEditRecordLog">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="hideEditRecordLog">关 闭</el-button>
      </template>
      <template #dialogBody>
        <p v-if="editRecordList.length <= 0"><span>当前资料尚未经修改。</span><span class="ty-right"> 创建人：{{ eqScanDetail.createName }} {{ new Date(eqScanDetail.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span></p>
        <div v-if="editRecordList.length > 0">
          <p>
            <span>当前资料为第{{ editRecordList.length-1 }}次修改后的结果。</span><span class="ty-right"> 修改人：{{ editRecordList[editRecordList.length-1].createName }} {{ new Date(editRecordList[editRecordList.length-1].createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
          </p>
          <table class="ty-table" id="editLogTab">
            <tbody>
            <tr>
              <td>资料状态</td>
              <td>操作</td>
              <td>创建人/修改人</td>
            </tr>
            <tr v-for="(item, index) in editRecordList" :key="index">
              <td>{{ (index === 0 ? '原始信息': '第' + index +'次修改后') }}</td>
              <td class="ty-td-control">
                <span class="ty-color-blue" @click="logScan(item)">查看</span>
              </td>
              <td>{{ item.createName }} {{ new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss') }}</td>
            </tr>
            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="uniformSettingsLog" width="600" dialogTitle="统一设置" color="blue" :dialogHide="hideUniformSettingsLog">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideUniformSettingsLog">取消</el-button>
        <el-button class="bounce-ok" @click="uniformSettingsSure()">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="catBody">
          <div>要统一设置为哪个类别？请选择！</div>
          <div class="ty-color-blue">注：“确定”后，各行均将加载为统一设置的类别，无论之前是否已单选！</div>
          <div class="uniformCatName">
            <el-input v-model="uniformCatName.id" type="hidden" />
            <input id="uniformCatName" v-model="uniformCatName.name" type="text" @click="filterCatBtn('uniformSet')" /><span class="downBtn"><i class="fa fa-sort-down"></i></span>
          </div>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="selectCatLog" width="600" dialogTitle="请选择类别" color="blue" :dialogHide="hideAddFun" :dialogName="'selectCatLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideAddFun('selectCatLog')">取消</el-button>
        <el-button class="bounce-ok" @click="selectCatOk()">确定</el-button>
      </template>
      <template #dialogBody>
        <div id="catContainer">
          <el-tree
              :props="props"
              :load="loadNode"
              lazy
              ref="catTree"
          />
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="recordScanLog" width="1000" dialogTitle="装备器具信息查看" color="blue" :dialogHide="hideRecordScanLog">
      <template #dialogFooter>
      </template>
      <template #dialogBody>
        <table class="ty-table">
          <tr>
            <td>
              <div>装备器具名称</div>
            </td>
            <td>
              <div>型号</div>
            </td>
            <td>
              <div>供应商/加工方</div>
            </td>
            <td>
              <div>单位</div>
            </td>
            <td class="baseLogScan" v-if="recordNum === 1">
              <div>数量</div>
            </td>
          </tr>
          <tr>
            <td>{{recordScan.equipmentName}}</td>
            <td>{{recordScan.modelName}}</td>
            <td>{{recordScan.unitName}}</td>
            <td>{{recordScan.supplierName || '整机系自行装配（零部件可能为外购、外加工或自制）'}}</td>
            <td class="baseLogScan" v-if="recordNum === 1">
              <div class="amountLog">{{recordScan.quantity || 1}}</div>
            </td>
          </tr>
        </table>
        <table class="ty-table subLogScan" v-if="recordNum === 2">
          <tr>
            <td>
              <div>装备器具编号</div>
            </td>
            <td>
              <div>原值</div>
            </td>
            <td>
              <div>预期的使用寿命</div>
            </td>
            <td>
              <div>到厂日期</div>
            </td>
            <td>
              <div>到厂时的新旧情况</div>
            </td>
            <td>
              <div>类别</div>
            </td>
            <td>
              <div>备注</div>
            </td>
          </tr>
          <tr>
            <td>{{recordScan.modelCode}}</td>
            <td>{{recordScan.originalValue ? recordScan.originalValue +` 元` : ''}} </td>
            <td>{{recordScan.lifeSpan}} </td>
            <td>{{new Date(recordScan.receiveDate).format('yyyyMM')}} </td>
            <td>{{recordScan.conditions == 1 ? '新' : ''}}{{recordScan.conditions == 2 ? '旧' : ''}} </td>
            <td>{{recordScan.categoryName}} </td>
            <td>{{recordScan.memo}} </td>
          </tr>
        </table>
      </template>
    </TyDialog>
    <TyDialog v-if="iKnowLog" width="500" dialogTitle="！提示" color="red" :dialogHide="hideIKnowLog">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="hideIKnowLog">我知道了</el-button>
      </template>
      <template #dialogBody>
        <div class="iKnowWord">
          <p>上步初始化尚未完成，故本模块初始化暂无法开始！</p>
          <p>上步初始化：{{judgeMessage.previousInit}}</p>
          <p>负责人：{{judgeMessage.userName}}</p>
        </div>
      </template>
    </TyDialog>
  </div>
</template>

<script>
import {initNav} from "@/utils/routeChange";
import uploadFile from "@/components/uploadFile.vue";
import addMixin from "@/mixins/equipManage/eqAddRecord";
import eqListMixin from "@/mixins/equipManage/eqListMixin";
import eqSupplierMixin from "@/mixins/equipManage/eqSupplierMixin";
import equipNames from "@/mixins/equipManage/equipNames";
import equitmentCategory from "@/mixins/equipManage/equitmentCategory";
import contractMixin from "@/mixins/equipManage/contractMixin";

import * as initApi from "@/api/init";
import {judgeAccountingRes, logout} from "@/api/api";

export default {
  mixins: [addMixin, eqListMixin, equipNames, equitmentCategory, eqSupplierMixin, contractMixin],

  data() {
    return {
      tedrt: '',
      pageName: 'equipmentInit',
      mainNum: 'main',
      nextPattern: false,
      judgeState: false,
      iKnowLog: false,
      judgeMessage: {},
    }
  },
  components: {
    uploadFile,
  },
  mounted() {
    initNav({mid: 'vh', name: '装备器具', pageName: this.pageName}, this.getInitData, this)
  },
  watch: {
    'indexPage.totalResult'() {
      this.getIndexNum()
    }
  },
  methods: {
    getInitData() {
      this.mainNum = 'main'
      this.nameditio = -1
      this.catgory = -1

      let rootPath = localStorage.getItem('rootPath')
      this.rootPath = JSON.parse(rootPath)
      this.getIndexNum()
      this.initCreateda()
      this.getCatList('')
      judgeAccountingRes()
          .then(res => {
            this.judgeState = res.data.data.operate //operate ture 是可以编辑， false 不能编辑
            this.judgeMessage = res.data.data
          })
    },
    checkNextStep() {
      if (!this.judgeState) {
        this.iKnowLog = true
        return false
      }
      if (Number(this.equipInitInfo.noCategory) === 0) {
        this.nextPattern = !this.nextPattern
      } else {
        this.$message({
          showClose: true,
          type: 'warning',
          message: "操作失败！本页必须编辑的项都编辑完后，才可勾选本选项！"
        })
        this.nextPattern = false
      }
    },
    nextStep() {
      if (!this.judgeState) {
        this.iKnowLog = true
        return false
      }
      initApi.initStartSure('equipmentTools').then(res => {
        let stepList = res.data.data
        if (stepList) {
          let param = {
            id: stepList[0].id
          }
          initApi.financeSaveStep(param).then(() => {
            this.$message({
              showClose: true,
              type: 'success',
              message: "装备器具的初始化已完成！\n" +
                  "\n" +
                  "请及时登录并使用Wonderss系统！"
            })
            logout().then(res => {
              console.log('logOut res=', res)
              let status = res.status
              if(status === 200){
                location.reload(true)
              }else{
                this.$message.error('退出失败！')
              }
            }).catch(err => {
              this.$message.error('退出失败！')
            })
          })
        }
      })
    },
    initJudgeState(source) {
      if (!this.judgeState) {
        this.iKnowLog = true
        return false
      }
      if (source === 1) {
        this.equipUnHandle()
        this.nameditio = 'hide'
        this.catgory = -1
      } else if (source === 2) {
        this.litAdd()
      } else if (source === 3) {
        this.uploadAdd()
      }
    },
    hideIKnowLog() {
      this.iKnowLog = false
    }
  }
}
</script>
<style scoped lang="scss">
@use "@/style/equiptCommon.scss";
@use "@/style/contrct.scss";

#equipmentInit {
  padding: 0 100px;
  font-size: 14px;

  .hrLine {
    border-top: 1px solid #d7d7d7;
    margin-top: 20px;
  }

  .rowGap {
    padding: 24px 0;

    .el-row {
      line-height: 34px
    }
  }

  .flexCol {
    padding-top: 20px;
    display: flex;
    justify-content: space-between;
  }

  .btnGap {
    margin-right: 30px;
  }

  .rowCon {
    padding: 24px 0;
  }

  .txtRight {
    text-align: right;
  }

  .el-link {
    font-weight: 500
  }

  .gapRt {
    margin-right: 10px;
  }

  .iKnowWord {
    width: 80%;
    margin: 0 auto;

    p {
      margin-bottom: 14px;
    }
  }
}
</style>