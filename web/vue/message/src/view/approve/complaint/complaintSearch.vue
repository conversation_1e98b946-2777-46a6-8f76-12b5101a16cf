<template>
  <div id='complaint' v-loading.fullscreen.lock='loading'>
    <TY_NavTop title='投诉处理' ></TY_NavTop>
    <div class='container'>
      <div v-if='!result'>
        <p class='blue'>您可通过确定查询条件，查询您结案报告已通过的投诉</p>
        <el-form :label-position='left' :rules='rules' label-width='110px' ref='ticketInfo'>
          <el-form-item label='' prop='date1'>
            <el-date-picker type='year' placeholder='请选择年份' value-format='yyyy年报' format='yyyy年报'  v-model='time' size='small' ></el-date-picker>
          </el-form-item>
          <div class='typeC'>
            <el-form-item label='' prop='radio'>
              <el-radio-group v-model='settleType'>
                <el-radio aria-checked="true" label='1'>真实投诉</el-radio><br>
                <el-radio label='2'>不属于我公司问题的投诉</el-radio><br>
                <el-radio label='4'>重复的投诉</el-radio>
              </el-radio-group>
            </el-form-item>
          </div>
          <el-form-item label='' prop='customer1'>
            <el-select v-model='customer' placeholder='请选择' size='small'>
              <el-option
                v-for='item in customerList'
                :key='item.id'
                :label='item.name'
                :value='item.id'>
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div class='centerBtn'>
          <input type='submit' class='ui-btn ui-btn_info' value='确定' @click='searchOk()'>
        </div>
      </div>
      <div v-if='result'>
        <p class='blue'>
          自{{timeBegin | formatDay('YYYY年MM月DD日')}} - {{ timeEnd | formatDay('YYYY年MM月DD日')}}，
          您经手的{{ customerName }}已结案的投诉中，{{settleType | chargeType }} 共{{num}}个</p>
        <a class='ui-cell' @click='jump(item.id)' v-for='(item, index) in list' v-bind:key='index'>
          <div class='ui-cell__bd'>
            <div class='ui-cell_con'>
              <div class='cusName'>{{item.customName}}</div>
              <div v-if="item.receiptTime"><span class='info'>接到投诉的日期</span><span class='info'>{{item.receiptTime | formatDay('YYYY/MM/DD')}}</span></div>
              <div><span class='info'>录入者</span><span class='info'>{{item.createName}} {{item.createTime | formatDay('YYYY/MM/DD HH:mm:ss')}}</span></div>
            </div>
          </div>
          <div class='ui-cell__ft'></div>
        </a>
      </div>
    </div>
  </div>
</template>
<style lang='less'>
  #complaint{
    .cusName{ margin-bottom:1rem; }
    .centerBtn{ padding-top:2rem;  }
    .info{ margin-left:2rem; }
    .blue{ padding:0.5rem; background:#DBEEF3; }
    .typeC{ border:1px solid #ccc; border-left:none; border-right:none;   }
  }
</style>
<script>
export default {
  name: 'approveComplaintSearch',
  data () {
    return {
      loading: false,
      result: false,
      time: '',
      timeBegin: '',
      timeEnd: '',
      list: [],
      num: 0,
      customer: '',
      customerName: '',
      settleType: '1',
      customerList: [],
      rules: {
        time: [
          { required: false }
        ],
        customer: [
          { required: true }
        ],
        settleType: [
          { required: true }
        ]
      }
    }
  },
  filters: {
    chargeType: function (type) {
      console.log(type)
      let str = ''
      switch (String(type)) {
        case '1' :
          str = '真实投诉'
          break
        case '2' :
          str = '不属于我公司问题的投诉'
          break
        case '3' :
          str = '重复的投诉'
          break
        default:
      }
      return str
    }
  },
  created () {
    let _this = this
    console.log('执行力33')
    // 获取客户列表
    _this.axios.post('../../../invoice/getAllCustomer.do').then(data => {
      let res = data.data
      console.log(res)
      _this.customerList = [{ 'id': '', 'name': '全部客户' }].concat(res['Customers'])
    }).catch((err) => {
      console.log('获取数据错误了！', err)
    })
  },
  destroyed: function () {
  },
  methods: {
    searchOk: function () {
      let _this = this
      _this.loading = true
      _this.result = true
      let timeS = _this.time.replace('年报', '')
      _this.customerList.forEach(function (cus) {
        if (cus.id === _this.customer) {
          _this.customerName = cus.name
        }
      })
      let userID = _this.sphdSocket.user.userID
      let role = _this.$store.getters.getComplaintHe
      let responseStr = '?userID=' + userID + '&role=' + role + '&time=' + timeS + '&customerId=' + _this.customer + '&settleType=' + _this.settleType
      _this.axios.post('../../../complaint/completeComplaintBase.do' + responseStr).then(data => {
        let res = data.data
        let info = res['data']
        _this.list = info['listComplaint']
        _this.num = info['num']
        _this.timeBegin = info['timeBegin']
        _this.timeEnd = info['timeEnd']
        _this.loading = false
      }).catch((err) => {
        console.log('获取数据错误了！', err)
        _this.loading = false
      })
    },
    jump: function (id) {
      window.parent.floatToPage('../../../complaint/complaintInfo.do', { 'id': id, 't': 10 })
    }
  },
  mounted () {
  }
}
</script>
