<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.accountant.mapper.TAccountantCashMapper" >

  <select id="getSingle" parameterType="tempcash" resultType="tempcash" >
    select 
    id, org, cash_flow_code AS cashFlowCode, cash_flow_name AS cashFlowName, accumulative, amount, balance_direction AS balanceDirection,
    memo, creator, create_name as createName, create_date AS createDate, updator, update_name as updateName, update_date AS updateDate, approve_Item AS approveItem,
    approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName, audit_date AS auditDate, operation, apply_memo AS applyMemo,
    approve_memo AS approveMemo, message_id AS messageId
    from t_accountant_cash
    where id = #{id}
  </select>

  <select id="listPage" parameterType="hashmap" resultType="tempcash" >
    select
    id, org, cash_flow_code AS cashFlowCode, cash_flow_name AS cashFlowName, accumulative, amount, balance_direction AS balanceDirection,
    memo, creator, create_name as createName, create_date AS createDate, updator, update_name as updateName, update_date AS updateDate, approve_Item AS approveItem,
    approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName, audit_date AS auditDate, operation, apply_memo AS applyMemo,
    approve_memo AS approveMemo, message_id AS messageId
    from t_accountant_cash
  </select>

  <select id="getCashItems" resultType="tempcash">

SELECT code AS cashFlowCode,name AS cashFlowName FROM t_accountant_cashflow_category WHERE enabled=1 AND org IS NULL;

  </select>
  <select id="getSingleByCashFlowCode" resultType="tempcash">

        select
    id, org, cash_flow_code AS cashFlowCode, cash_flow_name AS cashFlowName, accumulative, amount, balance_direction AS balanceDirection,
    memo, creator, create_name as createName, create_date AS createDate, updator, update_name as updateName, update_date AS updateDate, approve_Item AS approveItem,
    approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName, audit_date AS auditDate, operation, apply_memo AS applyMemo,
    approve_memo AS approveMemo, message_id AS messageId
    from t_accountant_cash
    where cash_flow_code = #{cashFlowCode} and org=#{org}

  </select>
  <select id="getCashTempList" resultType="tempcash">

            select
    id, org, cash_flow_code AS cashFlowCode, cash_flow_name AS cashFlowName, accumulative, amount, balance_direction AS balanceDirection,
    memo, creator, create_name as createName, create_date AS createDate, updator, update_name as updateName, update_date AS updateDate, approve_Item AS approveItem,
    approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName, audit_date AS auditDate, operation, apply_memo AS applyMemo,
    approve_memo AS approveMemo, message_id AS messageId
    from t_accountant_cash
    where org = #{org}

  </select>

  <delete id="delete" parameterType="tempcash" >
    delete from t_accountant_cash
    where id = #{id}
  </delete>
  <insert id="insert" useGeneratedKeys="true" keyProperty="id" >
    insert into t_accountant_cash (id, org, cash_flow_code, 
      cash_flow_name, accumulative, amount, 
      balance_direction, memo, creator, 
      create_name, create_date, updator, 
      update_name, update_date, approve_Item, 
      approve_status, approve_level, auditor, 
      auditor_name, audit_date, operation, 
      apply_memo, approve_memo, message_id
      )
    values (#{id}, #{org}, #{cashFlowCode},
      #{cashFlowName}, #{accumulative}, #{amount},
      #{balanceDirection}, #{memo}, #{creator},
      #{createName}, #{createDate}, #{updator},
      #{updateName}, #{updateDate}, #{approveItem},
      #{approveStatus}, #{approveLevel}, #{auditor},
      #{auditorName}, #{auditDate}, #{operation},
      #{applyMemo}, #{approveMemo}, #{messageId}
      )
  </insert>

  <insert id="addInitializationCash">
    insert into `t_accountant_cash` (org,cash_flow_code,cash_flow_name,accumulative,amount) select #{org},
    code, name, 0.00,0.00 from t_accountant_cashflow_category
  </insert>

  <update id="update" parameterType="tempcash" >
    update t_accountant_cash
    <set >
      <if test="org != null" >
        org = #{org},
      </if>
      <if test="cashFlowCode != null" >
        cash_flow_code = #{cashFlowCode},
      </if>
      <if test="cashFlowName != null" >
        cash_flow_name = #{cashFlowName},
      </if>
      <if test="accumulative != null" >
        accumulative = #{accumulative},
      </if>
      <if test="amount != null" >
        amount = #{amount},
      </if>
      <if test="balanceDirection != null" >
        balance_direction = #{balanceDirection},
      </if>
      <if test="memo != null" >
        memo = #{memo},
      </if>
      <if test="creator != null" >
        creator = #{creator},
      </if>
      <if test="createName != null" >
        create_name = #{createName},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate},
      </if>
      <if test="updator != null" >
        updator = #{updator},
      </if>
      <if test="updateName != null" >
        update_name = #{updateName},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate},
      </if>
      <if test="approveItem != null" >
        approve_Item = #{approveItem},
      </if>
      <if test="approveStatus != null" >
        approve_status = #{approveStatus},
      </if>
      <if test="approveLevel != null" >
        approve_level = #{approveLevel},
      </if>
      <if test="auditor != null" >
        auditor = #{auditor},
      </if>
      <if test="auditorName != null" >
        auditor_name = #{auditorName},
      </if>
      <if test="auditDate != null" >
        audit_date = #{auditDate},
      </if>
      <if test="operation != null" >
        operation = #{operation},
      </if>
      <if test="applyMemo != null" >
        apply_memo = #{applyMemo},
      </if>
      <if test="approveMemo != null" >
        approve_memo = #{approveMemo},
      </if>
      <if test="messageId != null" >
        message_id = #{messageId},
      </if>
    </set>
    where id = #{id}
  </update>
  <update id="plus27to26">

    UPDATE `t_accountant_cash` a INNER JOIN (SELECT SUM(accumulative) AS annual,SUM(amount) AS amount FROM t_accountant_cash WHERE org=#{org} and
    (cash_flow_code='r26c4' OR cash_flow_code='r27c4')) AS b SET a.accumulative=b.annual,a.amount=b.amount WHERE a.org=#{org} AND a.cash_flow_code='r26c4';


  </update>
  <update id="plus33to32">

        UPDATE `t_accountant_cash` a INNER JOIN (SELECT SUM(accumulative) AS annual,SUM(amount) AS amount FROM t_accountant_cash WHERE org=#{org} and
    (cash_flow_code='r33c4' OR cash_flow_code='r32c4')) AS b SET a.accumulative=b.annual,a.amount=b.amount WHERE a.org=#{org} AND a.cash_flow_code='r32c4';

  </update>
  <update id="statistic5">

    UPDATE `t_accountant_cash` a INNER JOIN (SELECT SUM(accumulative) AS annual,SUM(amount) AS amount FROM t_accountant_cash WHERE org=#{org} and
    (cash_flow_code='r2c4' OR cash_flow_code='r3c4' OR cash_flow_code='r4c4')) AS b SET a.accumulative=b.annual,a.amount=b.amount WHERE a.org=#{org} AND a.cash_flow_code='r5c4';

  </update>

  <update id="statistic10">

    UPDATE `t_accountant_cash` a INNER JOIN (SELECT SUM(accumulative) AS annual,SUM(amount) AS amount FROM t_accountant_cash WHERE org=#{org} and
    (cash_flow_code='r6c4' OR cash_flow_code='r7c4' OR cash_flow_code='r8c4' OR cash_flow_code='r9c4')) AS b
    SET a.accumulative=b.annual,a.amount=b.amount WHERE a.org=#{org} AND a.cash_flow_code='r10c4';

  </update>

<!--  <update id="statistic11">-->

<!--    UPDATE `t_accountant_cash` a INNER JOIN (SELECT SUM(accumulative) AS annual,SUM(amount) AS amount FROM t_accountant_cash WHERE org=#{org} and-->
<!--    (cash_flow_code='r6c4' OR cash_flow_code='r7c4' OR cash_flow_code='r8c4' OR cash_flow_code='r9c4')) AS b-->
<!--    SET a.accumulative=b.annual,a.amount=b.amount WHERE a.org=#{org} AND a.cash_flow_code='r11c4';-->

<!--  </update>-->

  <update id="updateBycode">

     UPDATE `t_accountant_cash` SET accumulative=#{accumulative},amount=#{amount} where org=#{org} and cash_flow_code=#{cashFlowCode}
  </update>
  <update id="statistic18">

        UPDATE `t_accountant_cash` a INNER JOIN (SELECT SUM(accumulative) AS annual,SUM(amount) AS amount FROM t_accountant_cash WHERE org=#{org} and
    (cash_flow_code='r13c4' OR cash_flow_code='r14c4' OR cash_flow_code='r15c4' OR cash_flow_code='r16c4' OR cash_flow_code='r17c4')) AS b
    SET a.accumulative=b.annual,a.amount=b.amount WHERE a.org=#{org} AND a.cash_flow_code='r18c4';

  </update>
  <update id="statistic23">

            UPDATE `t_accountant_cash` a INNER JOIN (SELECT SUM(accumulative) AS annual,SUM(amount) AS amount FROM t_accountant_cash WHERE org=#{org} and
    (cash_flow_code='r19c4' OR cash_flow_code='r20c4' OR cash_flow_code='r21c4' OR cash_flow_code='r22c4')) AS b
    SET a.accumulative=b.annual,a.amount=b.amount WHERE a.org=#{org} AND a.cash_flow_code='r23c4';

  </update>

  <update id="statistic30">

    UPDATE `t_accountant_cash` a INNER JOIN (SELECT SUM(accumulative) AS annual,SUM(amount) AS amount FROM t_accountant_cash WHERE org=#{org} and
    (cash_flow_code='r26c4' OR cash_flow_code='r28c4' OR cash_flow_code='r29c4')) AS b SET a.accumulative=b.annual,a.amount=b.amount
     WHERE a.org=#{org} AND a.cash_flow_code='r30c4';

  </update>
  <update id="statistic35">

    UPDATE `t_accountant_cash` a INNER JOIN (SELECT SUM(accumulative) AS annual,SUM(amount) AS amount FROM t_accountant_cash WHERE org=#{org} and
    (cash_flow_code='r31c4' OR cash_flow_code='r32c4' OR cash_flow_code='r34c4')) AS b SET a.accumulative=b.annual,a.amount=b.amount
     WHERE a.org=#{org} AND a.cash_flow_code='r35c4';

  </update>
  <update id="statistic38">

    UPDATE `t_accountant_cash` a INNER JOIN (SELECT SUM(accumulative) AS annual,SUM(amount) AS amount FROM t_accountant_cash WHERE org=#{org} and
    (cash_flow_code='r11c4' OR cash_flow_code='r24c4' OR cash_flow_code='r36c4' OR cash_flow_code='r37c4')) AS b
    SET a.accumulative=b.annual,a.amount=b.amount WHERE a.org=#{org} AND a.cash_flow_code='r38c4';

  </update>
  <update id="statistic40">

        UPDATE `t_accountant_cash` a INNER JOIN (SELECT SUM(accumulative) AS annual,SUM(amount) AS amount FROM t_accountant_cash WHERE org=#{org} and
    (cash_flow_code='r38c4' OR cash_flow_code='r39c4')) AS b
    SET a.accumulative=b.annual,a.amount=b.annual WHERE a.org=#{org} AND a.cash_flow_code='r40c4';

  </update>
  <update id="cleanData">

    UPDATE `t_accountant_cash` SET amount=0,accumulative=0 WHERE org=#{org}

  </update>
  <update id="updateAccumulative">

    UPDATE `t_accountant_cash` cash,t_accountant_cash_flow flow SET cash.accumulative=flow.accumulative WHERE cash.org=#{org} and cash.org=flow.org and flow.period=#{peroid} AND cash.cash_flow_code=flow.cell_code;

  </update>

  <update id="updateCash">

    UPDATE `t_accountant_cash` SET amount=IFNULL(amount,0)+#{amount},accumulative=IFNULL(accumulative,0)+#{accumulative} WHERE org=#{org} and cash_flow_code=#{cashFlowCode}

  </update>
  <update id="statistic39">

    UPDATE `t_accountant_cash`
    <set>
      <if test="amount != null" >
        amount = #{amount},
      </if>
      <if test="accumulative != null" >
        accumulative = #{accumulative},
      </if>
    </set>
    WHERE cash_flow_code=#{cashFlowCode} AND org=#{org};

  </update>
  <update id="updateBuildAccumulative">
    UPDATE `t_accountant_cash` SET accumulative=#{accumulative} WHERE org=#{org} and cash_flow_code=#{cashFlowCode}
  </update>

</mapper>