package cn.sphd.miners.modules.dailyAffairs.service;

import cn.sphd.miners.modules.system.entity.User;

import java.util.HashMap;

/**
 * Created by Administrator on 2019/1/8.
 */
public interface SWMessageService {

     void rejectSend(int loginNum,int operate, HashMap<String, Object> hashMap, String u, String destination,String title, String content, User user,String tCode);

     void rejectSendToMid(int loginNum,int operate,HashMap<String, Object> hashMap,Integer oid, String pCode, String destination,String tCode,String title, String content);

     void rejectSendToMidManageCode(int loginNum,int operate,HashMap<String, Object> hashMap,Integer oid, String pCode, String destination,String tCode,String title, String content,String manageCode);

}
