<template>
  <div id="supplementaryRecording">
    <div class="mainCon mainCon1" v-if="mainNum === 'main'">
      <div class="maintip">
        <p>补录的装备器具，需为已有但尚未录入至系统的装备器具。</p>
        <p>强烈建议：新购买或新制作的装备器具请走申购流程，不要在此补录！</p>
        <p>
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" @click="litAdd">零星补录</span>
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" @click="uploadAdd">批量补录</span>
        </p>
      </div>
    </div>
    <div class="mainCon mainCon2" v-if="mainNum === 2">
      <table class="preTab">
        <tr>
          <td>
            <p>您刚刚操作导入的装备器具共<span id="allImportNum">{{importDataOneList.length}}</span>条。</p>
            <p>以下<span id="redImportNum">{{redImportNum}}</span>条被标为红色的部分不符合右侧的要求，<span class="ty-color-red">无法保存至系统。</span></p>
            <p>“修改”无误后，方可点击“下一步”。"</p>
          </td>
          <td>
            <p>系统对表格各列数据的要求：</p>
            <p>“装备器具编号”不能重号；</p>
            <p>“装备器具名称”是必填项；</p>
            <p>“到厂日期”型式需为六位数字；</p>
            <p>“预期的使用寿命”中需填入正整数；</p>
            <p>“原值”中填入数据的小数点后需带有两位有效数字。</p>
          </td>
          <td>
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-3" @click="uploadCancel">放弃</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" @click="uploadNext">下一步</span>

          </td>
        </tr>
      </table>
      <table class="ty-table ty-table-control">
        <thead>
        <tr>
          <td>装备器具编号</td>
          <td>装备器具名称</td>
          <td>型号</td>
          <td>单位</td>
          <td>到厂日期</td>
          <td>预期的使用寿命</td>
          <td>原值</td>
          <td>操作</td>
        </tr>
        </thead>
        <tbody id="import1">
        <tr v-for="(item, index) in importDataOneList" :key="index">
          <td :class="{ 'color-red': modelCodeCount(item) || item.modelCode === '' }">{{ item.modelCode || '' }}</td>
          <td :class="{ 'color-red': !item.equipmentName }">{{ item.equipmentName || '--' }}</td>
          <td>{{ item.modelName || '' }}</td>
          <td>{{ item.unit || '' }}</td>
          <td :class="{ 'color-red': !((item.rDate || '').length === 6 && (new RegExp('^[0-9a-zA-Z]{6,16}$')).test(item.rDate)) || item.rDate === '' }">{{ item.rDate || '' }}</td>
          <td :class="{ 'color-red': !((item.oLifeSpan && item.oLifeSpan%1 === 0 && item.oLifeSpan>0 ) && item.oLifeSpan !== '') }"> {{item.oLifeSpan || ''}}</td>
          <td :class="{ 'color-red': !((item.oValue && (item.oValue).split('.')[1] && (item.oValue).split('.')[1].length === 2) || item.oValue === '') }">{{ item.oValue }}</td>
          <td>
            <span class="ty-color-blue funBtn" @click="editUpload(item,index, 1)">修改</span>
            <span class="ty-color-red funBtn" @click="delUpload(index)">删除</span>
          </td>
        </tr>
        </tbody>
      </table>
    </div>
    <div class="mainCon mainCon3" v-if="mainNum === 3">
      <p>
        本页各项如不需要可不操作，可直接点击“完成”。
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-right" @click="uploadComplete">完成</span>
        <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-3 ty-right gapRt" @click="uploadCancel">放弃</span>
        <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 ty-right" data-suptype="2" @click="addSup" style="margin-right: 50px;">增加供应商/加工方的选项</span>
      </p>
      <table class="ty-table ty-table-control" id="tab2">
        <tr>
          <td>装备器具编号/名称/型号</td>
          <td>类别</td>
          <td>到厂时的新旧情况</td>
          <td>供应商/加工方</td>
          <td>其他需要备注的内容</td>
          <td>操作</td>
        </tr>
        <tr v-for="(item, index) in importDataOneList" :key="index">
          <td>{{ item.modelCode }}/{{ item.equipmentName }}/{{ item.modelName }}</td>
          <td>
            <el-form-item>
              <el-input v-model="item.categoryName" placeholder="请录入" @click="filterCatBtn('importSelectCat', index)"/>
            </el-form-item>
          </td>
          <td>
            <el-form-item>
              <el-select  v-model="item.conditions" id="conditions" @change="editImportBtn(index,'conditions')">
                <el-option value="">请选择</el-option>
                <el-option value="1">新</el-option>
                <el-option value="2">旧</el-option>
              </el-select>
            </el-form-item>
          </td>
          <td>
            <el-form-item prop="supplier">
              <el-select v-model="item.supplier" placeholder="请选择" @change="editImportBtn(index,'supplier')">
                <el-option v-for="(item, index) in supplierList" :key="index" :value="item.id" :label="item['fullName']"/>
              </el-select>
            </el-form-item>
          </td>
          <td>
            <el-form-item>
              <el-input v-model="item.memo" placeholder="可录入不超过50字"  maxlength="50" show-word-limit @change="editImportBtn(index,'memo')"/>
            </el-form-item>
          </td>
          <td>
            <span class="ty-color-blue" @click="editUpload(item,index, 2)">修改</span>
            <span class="ty-color-red" @click="delUpload(index)">删除</span>
          </td>
        </tr>
      </table>
    </div>
    <TyDialog v-show="litAddLog" width="1100" dialogTitle="装备器具的零星补录" color="blue" :dialogHide="hideLitAddLog">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideLitAddLog">取消</el-button>
        <el-button class="bounce-ok" @click="litAddOk()">确定</el-button>
      </template>
      <template #dialogBody>
        <el-form ref="baseFormRef"
                 :model="baseForm"
                 :rules="baseFormRules"
                 id="litAdd"
        >
          <table class="ty-table new">
            <tr class="noBr"><td colspan="5"><div class="linkBtn ty-right" @click="operDescription = true">关于数量的操作说明</div></td></tr>
            <tr>
              <td width="180px">
                <div>装备器具名称<span class="ty-color-red">*</span><span class="linkBtn ty-right" @click="addName">新增</span></div>
              </td>
              <td width="160px">
                <div>型号</div>
              </td>
              <td>
                <div>供应商/加工方<span class="linkBtn ty-right" @click="addSup">新增</span></div>
              </td>
              <td width="160px">
                <div>单位<span class="linkBtn ty-right" @click="addUnit">新增</span></div>
              </td>
              <td width="160px">
                <div>数量<span class="ty-color-red">*</span></div>
              </td>
            </tr>
            <tr>
              <td>
                <el-form-item prop="equipment">
                  <el-select v-model="baseForm.equipment" placeholder="请选择">
                    <el-option v-for="(item, index) in eqNameList" :key="index" :value="item.id" :label="item['fullName']"/>
                  </el-select>
                </el-form-item>
              </td>
              <td>
                <el-form-item prop="modelName">
                  <el-input v-model="baseForm.modelName" placeholder="请录入" />
                </el-form-item>
              </td>
              <td>
                <el-form-item prop="supplier">
                  <el-select v-model="baseForm.supplier" placeholder="请选择">
                    <el-option v-for="(item, index) in supplierList" :key="index" :value="item.id" :label="item['fullName']"/>
                  </el-select>
                </el-form-item>
              </td>
              <td>
                <el-form-item prop="unitId">
                  <el-select v-model="baseForm.unitId" placeholder="请选择">
                    <el-option v-for="(item, index) in unitList" :key="index" :value="item.id" :label="item['name']"/>
                  </el-select>
                </el-form-item>
              </td>
              <td>
                <el-form-item prop="amount">
                  <el-input :value="baseForm.amount" v-model="baseForm.amount" placeholder="请录入" @change="changeLitSnGroups()" v-onlyInteger />
                </el-form-item>
              </td>
            </tr>
          </table>
          <table class="ty-table new eqOtherItems">
            <tr>
              <td>
                <div>装备器具编号</div>
              </td>
              <td>
                <div>原值</div>
              </td>
              <td>
                <div>预期的使用寿命</div>
              </td>
              <td>
                <div>到厂日期</div>
              </td>
              <td>
                <div>到厂时的新旧情况</div>
              </td>
              <td>
                <div>类别</div>
              </td>
              <td colspan="2">
                <div>其他需要备注的内容</div>
              </td>
            </tr>
            <tr v-for="(item, index) in eqAddList" :key="index">
              <td>
                <el-form-item>
                  <el-input v-model="item.modelCode" placeholder="请录入" />
                </el-form-item>
              </td>
              <td>
                <el-form-item>
                  <el-input v-model="item.originalValue" placeholder="请录入" @input="limitOriginalValue(index)">
                    <template #append>元</template>
                  </el-input>
                </el-form-item>
              </td>
              <td>
                <el-form-item>
                  <el-input v-model="item.lifeSpan" placeholder="请录入" @input="limitLifeSpan(index)">
                    <template #append>年</template>
                  </el-input>
                </el-form-item>
              </td>
              <td>
                <el-form-item style="width: 150px;">
                  <el-date-picker
                      v-model="item.rDate"
                      type="month"
                      placeholder="请选择"
                      value-format="YYYYMM"    />
                </el-form-item>
              </td>
              <td>
                <el-form-item style="width: 130px;">
                  <el-select  v-model="item.conditions" id="conditions">
                    <el-option value="">请选择</el-option>
                    <el-option value="1">新</el-option>
                    <el-option value="2">旧</el-option>
                  </el-select>
                </el-form-item>
              </td>
              <td>
                <el-form-item>
                  <el-input v-model="item.name" placeholder="请录入" @click="filterCatBtn('selectCatBtn', index)"/>
                </el-form-item>
              </td>
              <td colspan="2">
                <el-form-item>
                  <el-input v-model="item.memo" placeholder="可录入不超过50字"  maxlength="50" show-word-limit />
                </el-form-item>
              </td>
            </tr>
          </table>
        </el-form>
      </template>
    </TyDialog>
    <TyDialog v-if="importEditLog" width="800" dialogTitle="装备器具基本信息的修改" color="blue" :dialogHide="hideAddFun" :dialogName="'importEditLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideAddFun('importEditLog')">取消</el-button>
        <el-button class="bounce-ok" @click="importEditOk">确定</el-button>
      </template>
      <template #dialogBody>
        <el-form ref="importPaseFormRef"
                 :model="importPaseForm"
                 :rules="importBaseFormRules"
                 label-position="top"
        >
          <table class="impotEdit">
            <tr>
              <td>
                <el-form-item label="装备器具名称" prop="equipment">
                  <el-select v-model="importPaseForm.equipment" placeholder="请选择">
                    <el-option v-for="(item, index) in eqNameList" :key="index" :value="item.id" :label="item['fullName']"/>
                  </el-select>
                </el-form-item>
              </td>
              <td>
                <el-form-item label="型号">
                  <el-input v-model="importPaseForm.modelName" placeholder="请录入" />
                </el-form-item>
              </td>
            </tr>
            <tr>
              <td>
                <el-form-item label="单位" prop="unitId">
                  <el-select v-model="importPaseForm.unitId" placeholder="请选择">
                    <el-option v-for="(item, index) in unitList" :key="index" :value="item.id" :label="item['name']"/>
                  </el-select>
                </el-form-item>
              </td>
              <td>
                <el-form-item label="装备器具编号">
                  <el-input v-model="importPaseForm.modelCode" placeholder="请录入" />
                </el-form-item>
              </td>
            </tr>
            <tr>
              <td>
                <el-form-item label="预期的使用寿命">
                  <el-input v-model="importPaseForm.oLifeSpan" placeholder="请录入"  @input="limitOnlyInteger('oLifeSpan')">
                    <template #append>年</template>
                  </el-input>
                </el-form-item>
              </td>
              <td>
                <el-form-item label="原值">
                  <el-input v-model="importPaseForm.oValue" placeholder="请录入"  @input="getLimitVal('oValue')">
                    <template #append>元</template>
                  </el-input>
                </el-form-item>
              </td>
            </tr>
            <tr>
              <td>
                <el-form-item label="到厂日期">
                  <el-date-picker
                      v-model="importPaseForm.rDate"
                      type="month"
                      placeholder="请选择"
                      value-format="YYYYMM"
                  />
                </el-form-item>
              </td>
              <td>
              </td>
            </tr>
          </table>
        </el-form>
      </template>
    </TyDialog>
    <TyDialog v-if="uploadAddLog" width="600" dialogTitle="装备器具的批量补录" color="blue" :dialogHide="hideAddFun" :dialogName="'uploadAddLog'">
      <template #dialogFooter>
      </template>
      <template #dialogBody>
        <div>
          <div class="exportStep">
            <div class="stepItem">
              <p>第一步：点击“下载”，以下载空白的“装备器具清单”。</p>
              <div class="flexRow">
                <span>装备器具清单</span>
                <a :href="equipment_blank_sheet" download="装备器具清单.xls" class="ty-btn ty-btn-blue ty-btn-middle">下 载</a>
              </div>
            </div>
            <div class="stepItem">
              第二步：在空白的“装备器具清单”中填写内容，并存至电脑。
            </div>
            <div class="stepItem">
              <p>第三步：点击“浏览”，之后选择所保存的文件并上传。</p>
              <div class="flexRow">
                <div class="upload_sect viewBtn">
                  <uploadFile ref="uploadFile"
                              module="装备器具"
                              committed.async="false"
                              :showSelect="false"
                              :showFileList="false"
                              :autoUpload="true"
                              ext='.xls,.xlsx'
                              :successFun="handleSuccess"
                  >
                    <template #btnArea>
                      <span class="uploadify-button">{{ uploadAreaData.uploadBtn1_txt }}</span>
                    </template>
                  </uploadFile>
                </div>
                <div class="fileFullName">{{ uploadAreaData.originalFilename || '尚未选择文件' }}</div>
              </div>
            </div>
            <div class="stepItem">
              <p>第四步：点击“导入”。</p>
              <div class="flexRow">
                <span class="ty-btn ty-btn-yellow ty-btn-middle" @click="leadingHide">取 消</span>
                <span class="ty-btn ty-btn-blue ty-btn-middle" @click="importBtnOk">导 入</span>
              </div>
            </div>
            <div class="importIntro stepItem">
              <div style="text-align:left;color:red;"><span>导入说明：</span></div>
              <div style="text-align:left; font-size: 14px; line-height: 25px; ">
                <p>1、请勿增加或删除“装备器具清单”空白表的“列”，也不要修改修改各列的名字，否则上传会失败。</p>
                <p>2、“装备器具清单”“另存为”至电脑上时，可使用新的文件名，但点击本页面的“浏览”时如选错文件，上传可能失败。</p>
              </div>
            </div>
          </div>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="selectCatLog" width="600" dialogTitle="请选择类别" color="blue" :dialogHide="hideAddFun" :dialogName="'selectCatLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideAddFun('selectCatLog')">取消</el-button>
        <el-button class="bounce-ok" @click="selectCatOk()">确定</el-button>
      </template>
      <template #dialogBody>
        <div id="catContainer">
<!--          <cateTree :cateData="categoryList" @selectCatEvent="setCatItem"></cateTree>-->
          <el-tree
              :props="props"
              :load="loadNode"
              lazy
              ref="catTree"
          >
          </el-tree>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="addNameLog" dialogTitle="新增名称" color="blue" :dialogHide="hideAddFun" :dialogName="'addNameLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideAddFun('addNameLog')">取消</el-button>
        <el-button class="bounce-ok" @click="addNameOk()">确定</el-button>
      </template>
      <template #dialogBody>
        <div>
          <el-form-item label="装备器具的名称" prop="eqName" style="display: block">
            <el-input v-model="eqName" placeholder="请录入" />
          </el-form-item>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="operDescription" width="800" dialogTitle="关于“数量”的操作说明" color="blue" :dialogHide="hideDesFun">
      <template #dialogFooter>
        <el-button class="bounce-ok" @click="hideDesFun()">知道了</el-button>
      </template>
      <template #dialogBody>
        <div id="msgCon">
          <p>1 “数量”大于“1”时，“装备器具名称”、“型号”、“供应商/加工方”及“单位”都需要有数据，否则无法成功点击“确定”按钮。</p>
          <p>2 “数量”大于“1”并成功点击“确定”后，数据给予保存。保存后，对列表中某装备器具的“装备器具名称”、“型号”、“供应商/加工方”或“单位”进行修改后，列表中其他“装备器具”的数据将跟随变动。</p>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="addNameLog" dialogTitle="新增名称" color="blue" :dialogHide="hideAddFun" :dialogName="'addNameLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideAddFun('addNameLog')">取消</el-button>
        <el-button class="bounce-ok" @click="addNameOk()">确定</el-button>
      </template>
      <template #dialogBody>
        <div>
          <el-form-item label="装备器具的名称" prop="eqName" style="display: block">
            <el-input v-model="eqName" placeholder="请录入" />
          </el-form-item>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="addSupLog" width="500" dialogTitle="增加供应商/加工方的选项" color="blue" :dialogHide="hideAddFun" :dialogName="'addSupLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideAddFun('addSupLog')">取消</el-button>
        <el-button class="bounce-ok" @click="addSupOk()">确定</el-button>
      </template>
      <template #dialogBody>
        <el-form
            ref="supplierRef"
            label-position="top"
            :model="supplierForm"
            :rules="supplierForRules"
            label-width="120px"
        >
          <el-form-item label="供应商/加工方名称" prop="fullName" placeholder="请录入">
            <el-input v-model="supplierForm.fullName" />
          </el-form-item>
          <el-form-item label="供应商/加工方简称" prop="name" placeholder="请录入">
            <el-input v-model="supplierForm.name" />
          </el-form-item>
          <el-form-item label="供应商/加工方代号" prop="codeName" placeholder="请录入">
            <el-input v-model="supplierForm.codeName" />
          </el-form-item>
          <span class="ty-color-blue">
                    注：对于供应商代号，系统仅要求不能重复，具体如何编号，请与采购部门沟通。如无规则请随意录入。
          </span>
        </el-form>
      </template>
    </TyDialog>
    <!--  新增计量单位   -->
    <TyDialog v-if="addUnitLog" width="500" dialogTitle="新增计量单位" color="blue" :dialogHide="hideAddFun" :dialogName="'addUnitLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideAddFun('addUnitLog')">取消</el-button>
        <el-button class="bounce-ok" @click="addUnitOkBtn(11)">确定</el-button>
      </template>
      <template #dialogBody>
        <el-form ref="unitForm" :model="unitForm" :rules="unitFormRules" label-width="80px" label-position="top">
          <el-form-item label="计量单位" prop="name">
            <el-input type="text" v-model="unitForm.name" placeholder="请录入计量单位的名称"></el-input>
          </el-form-item>
        </el-form>
      </template>
    </TyDialog>
    <!-- 导入失败 -->
    <TyDialog v-if="importFalseVisible" width="400" dialogTitle="！提示" color="red" :dialogHide="hideAddFun" :dialogName="'importFalseVisible'">
      <template #dialogFooter>
      </template>
      <template #dialogBody>
        <div>
          <div class="narrowBody">
            <h4>导入失败！</h4>
            <div>
              <div>原因可能为：</div>
              <div>1、修改了所下载表格中的“列”。</div>
              <div>2、选错了文件。</div>
              <div>3、文件太大，或里面含有图片等。</div>
            </div>
          </div>
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="uploadDeltipLog" width="400" dialogTitle="！提示" color="red" :dialogHide="hideAddFun" :dialogName="'uploadDeltipLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideAddFun('uploadDeltipLog')">取消</el-button>
        <el-button class="bounce-ok" @click="uploadAddDeltipOk()">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center">
          确定删除这条数据吗？
        </div>
      </template>
    </TyDialog>
    <TyDialog v-if="abandonImportLog" width="400" dialogTitle="！提示" color="red" :dialogHide="hideAddFun" :dialogName="'abandonImportLog'">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="hideAddFun('abandonImportLog')">取消</el-button>
        <el-button class="bounce-ok" @click="delAlltipOk()">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center">
          确定放弃全部数据吗？
        </div>
      </template>
    </TyDialog>
  </div>
</template>

<script>

import {initNav} from "@/utils/routeChange";
import uploadFile from "@/components/uploadFile.vue";
import addMixin from "@/mixins/equipManage/eqAddRecord";


export default {
  mixins: [addMixin],
  data() {
    return {
      pageName: 'equitmentAddRe',
      mainNum: 'main'
    }
  },
  components: {
    uploadFile,
  },
  mounted() {
    initNav({mid: 'qu', name: '补录', pageName: this.pageName}, this.getCreatData, this)
  },
  methods:{
    getCreatData() {
      console.log('nnnnnnnnnnnn',this.$refs.catTree)
      this.mainNum = 'main'
    }
  },
  directives: {
    // 在模板中启用 v-focus
    onlyInteger: (el) => {
      // 这会在 `mounted` 和 `updated` 时都调用
      let ele = el.querySelector('input')
      ele.addEventListener('input', () => {
        // 获取输入框的当前值
        let value = ele.value;
        // 使用正则表达式匹配非正整数的部分，并将其替换为空字符串
        value = value.replace(/[^\d]/g, '');
        // 如果值为0，则直接设置为空字符串，因为0不是正整数
        if (value === '0') {
          value = '';
        }
        // 更新输入框的值
        ele.value = value;
      });
    }
  }
}
</script>
<style scoped lang="scss">
@use "@/style/equiptCommon.scss";
#supplementaryRecording {
  padding: 20px 100px;
  font-size: 14px;
}
</style>