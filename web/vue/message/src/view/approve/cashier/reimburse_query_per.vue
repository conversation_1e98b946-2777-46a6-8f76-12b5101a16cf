<template>
  <div id="approvalReimburseQueryPer">
    <TY_NavTop :title="tittle"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="ui-message">
        <div class="ui-tip">
          <div class="queryCondition">
            <div>您要查询：</div>
            <div class="queryContent">{{queryData.beginDate | formatDay('YYYY年MM月DD日')}}-{{queryData.endDate | formatDay('YYYY年MM月DD日')}}期间</div>
            <div class="queryContent">
              <span v-if="!paramQuery.applyId">您经手且最终</span><span v-if="paramQuery.applyId">{{paramQuery.applyUserName}}</span>被<span v-if="queryData.approveStatus === 2 || queryData.approveStatus === 5">审批通过</span><span v-if="queryData.approveStatus === 3 || queryData.approveStatus === 8">驳回</span>的报销申请
            </div>
            <div class="queryContent" v-if="queryData.feeCatName && !queryData.billCatName">
              申请中需含有费用类别为“{{queryData.feeCatName}}”的票据
            </div>
            <div class="queryContent" v-if="!queryData.feeCatName && queryData.billCatName">
              申请中需含有{{queryData.billCatName}}
            </div>
            <div class="queryContent" v-if="queryData.feeCatName && queryData.billCatName">
              申请中需含有费用类别为“{{queryData.feeCatName}}”的{{queryData.billCatName}}
            </div>
          </div>
        </div>
        <div class="sendContainer overTimeQuery">
          <div>
            符合查询条件的报销申请共如下{{queryData.number}}条 <span v-if="queryData.approveStatus !== 3 && queryData.approveStatus !== 8">，共{{queryData.money}}元。</span>
          </div>
        </div>
      </div>
      <div>
        <div v-if="state == 0">
          <el-table
            :data="queryData.monthlyList"
            fit
            border
          >
            <el-table-column
              prop="applyDate"
              label="月份"
              width="120"
            >
            </el-table-column>
            <el-table-column
              prop="number"
              label="票据数量"
            >
            </el-table-column>
            <el-table-column
              prop="money"
              label="金额"
            >
            </el-table-column>
          </el-table>
        </div>
        <div v-if="state == 1">
          <div class="ui-cells">
            <a class="ui-cell" v-on:click="jump(code + item.id, 0)" v-for="(item, index) in queryData.personnelReimburseList" v-bind:key="index">
              <div class="ui-cell__hd">{{item.createName | stringSplit(4)}} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              <div class="ui-cell__bd">
                <div class="ui-cell_con">
                  <div>申请报销 {{item.amount}} 元</div>
                  <div>票据 {{item.billQuantity}} 张，共 {{item.billAmount}} 元</div>
                </div>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
  .queryCondition{
    background: #fffaeb;
    font-size: 12px;
    padding: 8px;
    margin-bottom: 5px;
  }
</style>

<script>
export default {
  name: 'approveReimburseQueryPer',
  data () {
    return {
      loading: true,
      titleType: '',
      code: '35',
      tittle: '报销审批',
      paramQuery: {},
      queryData: {
        beginDate: 1560408078000,
        endDate: 1560408078000,
        approveStatus: 2,
        number: 0,
        money: 0,
        personnelReimburseList: [],
        monthlyList: []
      },
      state: ''
    }
  },
  created: function () {
    let paramQuery = this.$route.params.data
    // state （1- 自定义 ，2 -仅看年报，3- 仅看月报）
    // type 1-已完成的加班 2-未被认可的加班 3-实际未加班 4-加班申请被驳回
    if (!paramQuery) {
      paramQuery = JSON.parse(localStorage.getItem('query'))
    }
    console.log(paramQuery)
    this.paramQuery = paramQuery
    this.titleType = Number(paramQuery.titleType) // 1-报销审批人 2-财务
    if (this.titleType === 2) {
      this.tittle = '个人报销的付款'
      this.code = '36'
    }
    let that = this
    this.$http.post('../../../reimburseWindow/cashierQuery.do', paramQuery, {
      emulateJSON: true
    }).then((response) => {
      this.loading = false
      let data = response.body
      if (data) {
        let data = response.data.data
        let newMonthlyList = []
        for (let i in data.monthlyList) {
          if (data.monthlyList[i].number) {
            newMonthlyList.push(data.monthlyList[i])
          }
        }
        data.monthlyList = newMonthlyList
        that.state = data.state // 1-有明细 0-没有明细
        that.queryData = data
      } else {
        console.log('加载失败！')
      }
    }).catch(function () {
      // this.jumpError('系统错误，请重试！')
    })
  },
  filters: {
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  methods: {
    jump: function (id, param) {
      console.log('param : ' + param)
      this.$router.push({
        path: `/approveReimburseDetail/${id}/${param}`
      })
    }
  }
}
</script>
