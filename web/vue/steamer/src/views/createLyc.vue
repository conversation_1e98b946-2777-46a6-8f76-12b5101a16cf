<template>
  <div class="createLyc">
    <div>
      <el-button @click="backMain">&lt; 返回</el-button>
      <el-button type="primary" class="fright" @click="EditLycOk">确定</el-button>
    </div>
    <div class="">
      <p>
        选完直播时间，并选择不少于一首歌曲后，请点击右上角的“确定”！
      </p>
      <div>
        <span>
           <span>直播日期<i class="redXing">* </i></span>
            <el-date-picker v-model="formData.date" type="date" format="YYYY-MM-DD" placeholder="选择日期">
            </el-date-picker>
        </span>
        <span class="lemar">
          <span>具体时间<i class="redXing">* </i></span>
              <el-time-picker v-model="formData.time" placeholder="任意时间点" format="HH:mm:ss">
              </el-time-picker>
        </span>
      </div>
      <div class="tabcon">
        <el-table ref="multipleTable" :data="tableData" tooltip-effect="dark" style="width: 100%" :header-cell-style="tableHeaderColor" :cell-style="celStyle" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column prop="name" label="歌曲名称" width="200">
          </el-table-column>
          <el-table-column prop="durationTime" label="时长" ></el-table-column>
          <el-table-column prop="havBgmPath" label="伴奏"></el-table-column>
          <el-table-column prop="havOrigPath" label="原唱"> </el-table-column>
          <el-table-column prop="captionRequired" label="歌词"> </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import { ElMessage } from 'element-plus'
// import { ArrowLeftBold } from '@element-plus/icons'
import { steamerGetSongs, addPlayList, upPlayList } from '@/api/api.js'
export default {
  data() {
    return {
      tableData: [],
      editObj:{},
      multipleSelection: [],
      formData: {
        date:'',
        time:'',
      }
    }
  },
  components: {
    // ArrowLeftBold
  },
  mounted() {
    let item = this.$route.params.id;
    let editLit = localStorage.getItem('editLit')
    console.log('item', item )
    console.log('editLit', editLit )
    if(editLit && editLit.length>10){
      this.editObj = JSON.parse(editLit)
      let liveTime = this.editObj.liveTime
      this.formData = {
        date: new Date(liveTime),
        time: new Date(liveTime),
      }
      console.log('this.formData =', this.formData )
    }
    this.getList()

  },
  methods: {
    backMain() {
      console.log('mainPage')
      this.$router.push('mainPage')
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
      console.log('val=', val)
    },
    EditLycOk(){
      if(this.formData.date.length == ''){
        this.$alert('请先选择"直播日期"', '提示', {
          confirmButtonText: '确定',
        })
      }
      else if(this.formData.time.length == ''){
        this.$alert('请先选择"具体时间"', '提示', {
          confirmButtonText: '确定',
        })
      }
      else {
        if(this.multipleSelection.length === 0){
          this.$alert('请选择不少于一首歌曲', '提示', {
            confirmButtonText: '确定',
          })
        }else{

          let date = new Date(this.formData.date).format('yyyy-MM-dd')
          let time = new Date(this.formData.time).format('hh:mm:ss')
          let liveDate = date+ ' ' + time
          let songIds = []
          this.multipleSelection.forEach(selectIm => {
            songIds.push(selectIm.id)
          })
          songIds = songIds.toString()
          let data = {
            liveDate: liveDate,
            songIds: songIds
          }
          if(this.editObj.id){ // update
            data.id = this.editObj.id
            upPlayList(data).then(res => {
              let data = res.data.data
              console.log('res=', data)
              let state = data.state
              if(state == 1){
                ElMessage.success({
                  message: '修改成功！',
                  type: 'success',
                })
                this.$router.push('mainPage')
              }else{
                ElMessage.error('修改失败！')
              }

            }).catch(err => {
              console.log('err=', err)
            })


          }else{ // add
            addPlayList(data).then(res => {
              console.log('res=', res)
              let data = res.data.data
              let state = data.state
              if(state == 1){
                ElMessage.success({
                  message: '新歌单创建成功！',
                  type: 'success',
                })
                this.$router.push('mainPage')
              }else{
                ElMessage.error('新歌单创建失败！')
              }


            }).catch(err => {
              console.log('err=', err)
            })
          }


          console.log('data=', data)



        }


      }
    },
    getList(){
      this.tableData = []
      let data = {}
      if(this.editObj.id){
        data.id = this.editObj.id
      }
      steamerGetSongs(data).then(res => {
        console.log(res)
        let list = res.data.data.listAllSongs || []
        let selectList = []
        list.forEach(lyc => {
          let item = {
            id: lyc.id,
            name: lyc.name,
            havOrigPath: lyc.originalPath ? '有':'无',
            havBgmPath: lyc.bgmPath ? '有':'无',
            originalPath: lyc.originalPath,
            bgmPath: lyc.bgmPath,
            durationTime: lyc.durationTime || '',
            captionRequired: lyc.captionRequired ? '有' :'无',
          }
          this.tableData.push(item)
          if(this.editObj.id){
            if(lyc.playListTag == 1){
              selectList.push(item)
            }
          }
        })
        this.toggleSelection(selectList)

      }).catch(err => {
        console.log(err)
      })
    },
    toggleSelection(rows) {
      if (rows) {
        rows.forEach((row) => {
          this.$refs.multipleTable.toggleRowSelection(row)
        })
      } else {
        this.$refs.multipleTable.clearSelection()
      }
    },
    handleCheckedChange(value) {
      let checkedCount = value.length
      console.log('handleCheckedChange value=', value)

    },
    tableHeaderColor({rowIndex, columnIndex}) {
      if (rowIndex === 0 ) {
        return {background: '#eee', 'text-align':'center' }
      }
    },
    celStyle({rowIndex, columnIndex}) {
      return { 'text-align':'center'}
    }


  }
}
</script>

<style lang="scss" scoped>
.createLyc{
  text-align: left;
  .lemar{ display: inline-block; margin-left: 20px;  }
  .tabcon{ margin-top: 20px; }
}
</style>
