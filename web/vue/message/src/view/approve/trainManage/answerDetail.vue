<template>
  <div id="answerDetail" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="试卷"></TY_NavTop>
    <div class="container">
      <div class="top">
        <el-row>
          <el-col :span="6">标准答案</el-col>
          <el-col :span="18" class="red">{{ info.trainingExamUserAnswer.codeOk }}</el-col>
        </el-row>
        <el-row>
          <el-col :span="6">学员答题</el-col>
          <el-col :span="18" class="red">{{ info.trainingExamUserAnswer.memoStr }}</el-col>
        </el-row>
      </div>
      <div>
        <div class="title" style="padding: 10px 20px;"><span>{{info.trainingQuestion.type == 1 ? '单选题' : '判断题' }} {{info.index + 1}}(本题{{info.trainingExamQuestion.score}}分)</span>
          <span class="turnRight">本题得分
          <span style="margin-left:20px;">{{info.trainingExamUserAnswer.score || 0}}分</span></span>
        </div>
        <div style="background: #fff;padding: 10px 20px;">
          <p style="margin-bottom: 20px; font-size: 14px;" v-html="info.trainingQuestion.content"></p>
          <div v-if="info.trainingQuestion.type == 1">
            <el-radio-group size="small">
              <el-radio v-for="(item, index) in info.trainingExamKeyList" v-bind:key="index" :label="item.id" ><span class="orderCon">{{ item.code }}</span> {{ item.content }}</el-radio>
            </el-radio-group>
          </div>
          <div v-if="info.trainingQuestion.type == 2">
            <el-radio-group size="small">
              <el-radio :label="1" >√</el-radio>
              <el-radio :label="0" >×</el-radio>
            </el-radio-group>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less">
#answerDetail{
  color: #000001; overflow: auto;font-size: 14px;
  .red{ color:red;font-weight: 600;  }
  .top{ padding:10px 20px; background: #fff;
    .el-row{padding:12px 0;}
  }
  .el-radio {white-space: inherit;}
  .orderCon{ margin: 0 10px;}
  .turnRight{float: right;}
}
</style>
<script>

export default {
  name: 'answerDetail',
  data () {
    return {
      info: {}
    }
  },
  created () {
    let res = JSON.parse(localStorage.getItem('res'))
    this.info = res.ques
    let Ans = this.info.trainingExamUserAnswer || {}
    this.info.trainingExamUserAnswer = Ans
    let memo = Ans.memo
    if (memo) {
    } else {
      Ans.memoStr = ''
    }
    let type = this.info.trainingQuestion.type
    if (Number(type) === 1) {
      if (memo) { Ans.memoStr = memo }
    } else {
      if (memo === '1' || memo === 1) {
        Ans.memoStr = '√'
      } else if (memo === '0' || memo === 0) {
        Ans.memoStr = '×'
      }
    }
  },
  methods: {
  }
}
</script>
