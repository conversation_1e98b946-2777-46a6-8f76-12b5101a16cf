package cn.sphd.miners.modules.commodity.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.commodity.dao.PdMerchandiseInvoiceHistoryDao;

import cn.sphd.miners.modules.commodity.entity.PdMerchandiseInvoice;
import cn.sphd.miners.modules.commodity.entity.PdMerchandiseInvoiceHistory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
@Repository
public class PdMerchandiseInvoiceHistoryDaoImpl extends BaseDao<PdMerchandiseInvoiceHistory, Serializable> implements PdMerchandiseInvoiceHistoryDao {

    @Override
    public PdMerchandiseInvoiceHistory insert(PdMerchandiseInvoice merchandiseInvoice) {
        PdMerchandiseInvoiceHistory history=new PdMerchandiseInvoiceHistory();
        BeanUtils.copyProperties(merchandiseInvoice,history);
        history.setId(null);
        history.setMerchandiseInvoice(merchandiseInvoice.getId());;
        save(history);
        getSession().flush();
        return history;
    }
}
