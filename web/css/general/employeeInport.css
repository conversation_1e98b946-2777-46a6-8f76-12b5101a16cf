td > select, td > .z {  display: inline-block;  width: 80%;  min-width: 100px;  height: 30px;  }

.z {  position: relative;  }

.z > input {  width: 100%;  height: 30px;  display: inline-block;  padding-left: 20px;  }

.red {  border: 1px solid red;  }
.options {  display: none;  position: absolute;  top: 31px;  border: 1px solid #ccc;  width: 100%;  z-index: 1;  background: #fff;
    text-align: left;  max-height: 130px;  overflow: auto;  }

.options option {  padding-left: 20px;  }

.options option:hover {  background: #eaeaea;  }
.exportStep {  padding-left: 40px;  }
.stepItem {  margin-top: 20px;  }
.flexRow{  margin-left: 50px;  display: flex;  justify-content:space-between;  width: 356px;  }
.fileFullName{  width: 278px; line-height:26px;background: #fff;text-align: center;}
.ty-btn-middle{padding: 0px 22px;  height: 26px;  line-height: 26px;}
.left{  display: inline-block;  width: 80px;  }
.formItem{  position: relative; margin-top: 8px;  overflow: hidden;  clear: both;  }
.narrowLamp{width: 70%;min-width: 650px;}
.userForm{width: 300px;  margin: 0 auto 20px;}
.clearBtn{position: absolute;  right: 38px;  top: 4px;  width: 14px;  height: 30px;font-style: normal;  color: #b0b0b0;  cursor: pointer;}

.viewBtn .uploadify-button{ padding: 0 22px; min-width: 80px;  margin: 0; border-radius: 0; height: 26px;  line-height: 26px;background-color: #5d9cec;  color: #fff;border: none;}
.viewBtn .uploadify-button:hover{ background: #5d9cec;}
.changeDot{margin-bottom: 12px;}
.changeDot span{margin-right: 12px;}
.narrowBody{margin: 0 auto;width: 80%;}
.importNoSave thead td,.importing thead td  {  border-color: #fff0;  background-color: #fff2cc;  }





