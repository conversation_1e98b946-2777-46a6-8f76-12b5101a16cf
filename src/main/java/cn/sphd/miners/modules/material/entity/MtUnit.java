package cn.sphd.miners.modules.material.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 材料_计量单位定义表
 * <p>
 * 20200731 1.119计量单位 新增
 * (t_mt_unit)表实体类
 * @date 2020-08-23 17:22:29
 */
@Entity(name = "MtUnit")
@Table(name = "t_mt_unit")
public class MtUnit implements Serializable {

    //ID
    private Integer id;

    //机构ID
    private Integer org;

    //代号
    private String code;

    //名称
    private String name;

    //分类(预留)
    private Integer category;

    //在用状态:TRUE-在用,FALSE-停用
    private Boolean enalbed;

    //同级排序
    private Integer orders;

    //操作模块代码:01-商品录入,02-构成管理,03-配方管理,04-材料录入 11-装备器具
    private String module;

    //备注
    private String memo;

    //创建人id
    private Integer creator;

    //创建人
    private String createName;

    //创建时间
    private Date createDate;

    //修改人id
    private Integer updator;

    //修改人
    private String updateName;

    //修改时间
    private Date updateDate;

    //操作:1-增,2-删,3-修改
    private String operation;

    //修改前记录ID
    private Integer previousId;

    //版本号,每次修改+1
    private Integer versionNo;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "org")
    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    @Column(name = "code")
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Column(name = "name")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Column(name = "category")
    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    @Column(name = "enalbed")
    public Boolean getEnalbed() {
        return enalbed;
    }

    public void setEnalbed(Boolean enalbed) {
        this.enalbed = enalbed;
    }

    @Column(name = "orders")
    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    @Column(name = "module")
    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    @Column(name = "memo")
    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    @Column(name = "creator")
    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    @Column(name = "create_name")
    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    @Column(name = "create_date")
    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    @Column(name = "updator")
    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    @Column(name = "update_name")
    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    @Column(name = "update_date")
    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    @Column(name = "operation")
    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    @Column(name = "previous_id")
    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    @Column(name = "version_no")
    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

}