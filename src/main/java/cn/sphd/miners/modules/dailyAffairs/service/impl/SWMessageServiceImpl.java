package cn.sphd.miners.modules.dailyAffairs.service.impl;

import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.dao.OrgDao;
import cn.sphd.miners.modules.system.dao.PopedomDao;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.entity.UserPopedom;
import cn.sphd.miners.modules.system.service.PopedomTaskService;
import cn.sphd.miners.modules.system.service.UserPopedomService;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;

/**
 * Created by Administrator on 2019/1/8.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class SWMessageServiceImpl implements SWMessageService{
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;

    @Autowired
    UserDao userDao;
    @Autowired
    PopedomDao popedomDao;
    @Autowired
    OrgDao orgDao;

    @Autowired
    UserPopedomService userPopedomService;
    @Autowired
    UserService userService;
    @Autowired
    PopedomTaskService popedomTaskService;


    /**
    * <AUTHOR>
    * @Date 2019/7/11 16:44
     * @param loginNum  接收推送的人在该机构的角标变化 >0加角标   <0 减去角标  等于0 不变 。数据对应变化数。
     * @param operate   数据变化 >0列表中加数据   <0 列表中减数据  等于0 不变 。 数据对应变化条数。
     * @param params  业务数据，这里的key和value 由具体业务后台与前端自行约定。
     * @param u  按什么订阅就是什么 （如果按人推送订阅通道必须是按userId订阅）
     * @param destination 要推送到的 通道名
     * @param title 标题
     * @param content 内容
     * @param user 人员信息
    */
    @Override
    public void rejectSend(int loginNum,int operate,HashMap<String, Object> params, String u, String destination,String title, String content, User user,String tCode) {
//        System.out.println("rejectSend开始");
        params.put("operate",operate);
//        System.out.println("loginNum："+loginNum+" operate: "+operate+" toUserId: "+u+" user.id: "+user.getUserID()+" destination: "+destination);
        if (loginNum!=0){
//            System.out.println("loginNum!=0 IS true");
            popedomTaskService.updateUserBadgeNumberWithCode(user,tCode,loginNum);
        }
        clusterMessageSendingOperations.convertAndSendToUser(u,destination, title,content,user.getOid(),user.getOrganization().getName(),JSON.toJSONString(params));
//        System.out.println("rejectSend结束");

    }

     /**
     * <AUTHOR>
     * @Date 2019/1/21 10:10
      * @param loginNum  接收推送人在该机构的角标变化 >0加角标   <0 减去角标  等于0 不变 。数据对应变化数。
      * @param operate   数据变化 >0列表中加数据   <0 列表中减数据  等于0 不变 。 数据对应变化条数。
      * @param params  业务数据，这里的key和value 由具体业务后台与前端自行约定。
     * @param oid  机构id
     * @param pCode 权限code
     * @param destination 要推送到的 通道名
     * @param title 标题
      * @param content 内容
      * 这个接口需要前端判断满足条件的进行订阅，按需订阅
     */
    @Override
    public void rejectSendToMid(int loginNum, int operate,HashMap<String, Object> params, Integer oid,String pCode, String destination,String tCode,String title, String content) {
        System.out.println("rejectSendToMid开始");
        params.put("operate",operate);
        System.out.println("loginNum："+loginNum+" operate: "+operate+" oid: "+oid+" code:"+pCode+" destination: "+destination);
        String orgName=orgDao.get(oid).getName();
        updateUserBadgeNumbe(loginNum,oid,pCode,orgName,tCode);
        clusterMessageSendingOperations.convertAndSendToUser(oid+pCode,destination, title, content, oid, orgName,  JSON.toJSONString(params));
        System.out.println("rejectSendToMid结束");
    }

    @Override
    public void rejectSendToMidManageCode(int loginNum, int operate, HashMap<String, Object> params, Integer oid, String pCode, String destination, String tCode, String title, String content,String manageCode) {
        System.out.println("rejectSendToMidManageCode开始");
        params.put("operate",operate);
        System.out.println("loginNum："+loginNum+" operate: "+operate+" oid: "+oid+" code:"+pCode+" destination: "+destination);
        String orgName=orgDao.get(oid).getName();
        updateUserBadgeNumbe(loginNum,oid,pCode,orgName,tCode);
        clusterMessageSendingOperations.convertAndSendToUser(oid+pCode+manageCode,destination, title, content, oid, orgName,  JSON.toJSONString(params));
        System.out.println("rejectSendToMidManageCode结束");
    }
    private void updateUserBadgeNumbe(int loginNum, Integer oid, String pCode, String orgName, String tCode) {
        if (loginNum!=0){
            String mid=popedomDao.getMidByCode(pCode);
            List<UserPopedom> userPopedomList=userPopedomService.getAllUserPopedomByMid(oid,mid);
            for (UserPopedom userPopedom:userPopedomList){
                User user=userPopedom.getUser();
                if (!"super".equals(user.getRoleCode())&&"1".equals(user.getIsDuty())) {
                    popedomTaskService.updateUserBadgeNumberWithCode(user,tCode,loginNum);
                }
            }
        }
    }
}
