package cn.sphd.miners.modules.finance.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.finance.dto.FinanceAccountHistoryDto;
import cn.sphd.miners.modules.finance.entity.*;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2016/12/12.
 */
public interface FinanceAccountService {
    List<FinanceAccount> getAccount(Integer oid,List<Integer> orgIntegerList);

    FinanceAccount addAccount(FinanceAccount financeAccount, User user, Organization organization);

    FinanceAccount getAccountById(Integer accountId);

    void updateFinanceAccount(FinanceAccount financeAccount);

    List<FinanceAccount> getAccountByAccountStatus(Integer oid, Integer accountStatus,String isPublic);

    FinanceAccount getCashAccount(Integer oid, Integer accountType);

    FinanceAccount getAccountByIdAndIsPublic(Integer accountId,String isPublic);

    List<FinanceAccount> getPublicAccount(Integer org);

    FinanceAccount getBasicAccount(Integer oid);

    AccountPeriod addPeroid(FinanceAccount financeAccount,Integer periodType,Organization organization);

    AccountDetail saveAccountDetail(User user, Organization organization, FinanceAccount financeAccount, BigDecimal balance, BigDecimal credit, String type, String summary,
                                    String method, String auditorName, String modityStatus, Integer business, String source, BigDecimal debit, String genre, String categoryDesc,
                                    String accountantStatus, String memo, String businessType, String partnerName, Date paymentDate,Date billDate,Integer previousId,Boolean isModify);  //添加明细

    List<FinanceAccount> getAllAccounts(Integer oid,List<Integer> orgIntegerList,Integer accountStatus,Integer accountType);  //获取机构中的所有账户

    void addAccountRecord(FinanceAccount financeAccountId,Integer accountStatus,User user);  //添加账户启用记录

    List<FinanceAccountHistoryDto> getAccountHistoryDtosByAccountId(Integer accountId);  //查找这个账户下所有修改记录

    void updateAccount(FinanceAccount accountId, Integer isBasic, String name, String bankName, String account, Double initialAmount, String memo,User user,Organization org);

    FinanceAccountHistory getAccountHistoryById(Integer accountHistoryDetailId,Integer type);

    void updateAccountDetails(Integer accountId,Integer accountStatus);   //将数据查看中的数据修改为不可修改，或者修改为可修改

    List<FinanceAccountRecord> getAccountRecordsByAccountId(Integer accountId);

    FinanceAccountBill getAccountBillByBusiness(Integer business,String source,String businessType);
    FinanceAccountBill getAccountBillByBusiness(Integer business,String source,String businessType,String type);

    List<FinanceAccount> getAccountKinds(Integer oid, Integer accountType,String isPublic,Integer isBasic,Integer accountStatus);

    Map<String,Object> updateAndAddAccount(User user, String updateAddAccounts);

    void deleteFinanceAccount(FinanceAccount financeAccount);
}
