package cn.sphd.miners.modules.mpWeChatRes.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2021-12-21 
 */

@Entity
@Table ( name ="t_wx_resource_acl" )
public class WxResourceAcl  implements Serializable {

	/**
	 * ID
	 */
	@Id
	@Column(name="id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * 微信应用账号ID
	 */
   	@Column(name = "app", nullable=true , unique=false )
	private Integer app;

	/**
	 * 微信应用账号ID
	 */
   	@Column(name = "app_id", length=128, nullable=true , unique=false )
	private String appId;

	/**
	 * 机构ID
	 */
   	@Column(name = "org", nullable=true , unique=false )
	private Integer org;

	/**
	 * 文件ID
	 */
   	@Column(name = "file", nullable=true , unique=false )
	private Integer file;

	/**
	 * 用户ID
	 */
   	@Column(name = "app_user", nullable=true , unique=false )
	private Integer appUser;

	/**
	 * 用户openid
	 */
   	@Column(name = "open_id", length=128, nullable=true , unique=false )
	private String openId;

	/**
	 * 浏览权限:1-浏览,2-打印
	 */
   	@Column(name = "view", length=1, nullable=true , unique=false )
	private String view;

	/**
	 * 操作权限:新增-C,删-D,改-U,追加-A,审核-T
	 */
   	@Column(name = "operate", length=50, nullable=true , unique=false )
	private String operate;

	/**
	 * 授权:分配-A,共享-S
	 */
   	@Column(name = "granted", length=50, nullable=true , unique=false )
	private String granted;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator", nullable=true , unique=false )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name", length=100, nullable=true , unique=false )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_date", nullable=true , unique=false )
	private Date createDate;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator", nullable=true , unique=false )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name", length=100, nullable=true , unique=false )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_date", nullable=true , unique=false )
	private Date updateDate;

	/**
	 * 申批项目
	 */
   	@Column(name = "approve_item", nullable=true , unique=false )
	private Integer approveItem;

	/**
	 * 审批状态:0-未申请 ,1-申请提交,2-通过审核,3-否决审核
	 */
   	@Column(name = "approve_status", length=1, nullable=true , unique=false )
	private String approveStatus;

	/**
	 * 审批次级
	 */
   	@Column(name = "approve_level", nullable=true , unique=false )
	private Integer approveLevel;

	/**
	 * 审批者ID
	 */
   	@Column(name = "auditor", nullable=true , unique=false )
	private Integer auditor;

	/**
	 * 审批者
	 */
   	@Column(name = "auditor_name", length=100, nullable=true , unique=false )
	private String auditorName;

	/**
	 * 审批日期
	 */
   	@Column(name = "audit_date", nullable=true , unique=false )
	private Date auditDate;

	/**
	 * 操作:1-增,2-删,3-改
	 */
   	@Column(name = "operation", length=1, nullable=true , unique=false )
	private String operation;

	/**
	 * 申请备注
	 */
   	@Column(name = "apply_memo", length=255, nullable=true , unique=false )
	private String applyMemo;

	/**
	 * 审批备注
	 */
   	@Column(name = "approve_memo", length=255, nullable=true , unique=false )
	private String approveMemo;

	/**
	 * 对应消息表的id
	 */
   	@Column(name = "message_id", nullable=true , unique=false )
	private Integer messageId;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getApp() {
		return app;
	}

	public void setApp(Integer app) {
		this.app = app;
	}

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public Integer getOrg() {
		return org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getFile() {
		return file;
	}

	public void setFile(Integer file) {
		this.file = file;
	}

	public Integer getAppUser() {
		return appUser;
	}

	public void setAppUser(Integer appUser) {
		this.appUser = appUser;
	}

	public String getOpenId() {
		return openId;
	}

	public void setOpenId(String openId) {
		this.openId = openId;
	}

	public String getView() {
		return view;
	}

	public void setView(String view) {
		this.view = view;
	}

	public String getOperate() {
		return operate;
	}

	public void setOperate(String operate) {
		this.operate = operate;
	}

	public String getGranted() {
		return granted;
	}

	public void setGranted(String granted) {
		this.granted = granted;
	}

	public Integer getCreator() {
		return creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getUpdator() {
		return updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Integer getApproveItem() {
		return approveItem;
	}

	public void setApproveItem(Integer approveItem) {
		this.approveItem = approveItem;
	}

	public String getApproveStatus() {
		return approveStatus;
	}

	public void setApproveStatus(String approveStatus) {
		this.approveStatus = approveStatus;
	}

	public Integer getApproveLevel() {
		return approveLevel;
	}

	public void setApproveLevel(Integer approveLevel) {
		this.approveLevel = approveLevel;
	}

	public Integer getAuditor() {
		return auditor;
	}

	public void setAuditor(Integer auditor) {
		this.auditor = auditor;
	}

	public String getAuditorName() {
		return auditorName;
	}

	public void setAuditorName(String auditorName) {
		this.auditorName = auditorName;
	}

	public Date getAuditDate() {
		return auditDate;
	}

	public void setAuditDate(Date auditDate) {
		this.auditDate = auditDate;
	}

	public String getOperation() {
		return operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public String getApplyMemo() {
		return applyMemo;
	}

	public void setApplyMemo(String applyMemo) {
		this.applyMemo = applyMemo;
	}

	public String getApproveMemo() {
		return approveMemo;
	}

	public void setApproveMemo(String approveMemo) {
		this.approveMemo = approveMemo;
	}

	public Integer getMessageId() {
		return messageId;
	}

	public void setMessageId(Integer messageId) {
		this.messageId = messageId;
	}
}
