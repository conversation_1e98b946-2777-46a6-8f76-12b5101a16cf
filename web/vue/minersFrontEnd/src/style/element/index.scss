// styles/element/index.scss
/* 只需要重写你需要的即可 */
@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  $colors: (
    'primary': (
      'base': #488fea,
    ),
    'success': (
      'base': #48cfad,
    ),
  ),
);

// 如果只是按需导入，则可以忽略以下内容。
// 如果你想导入所有样式:
// @use "element-plus/theme-chalk/src/index.scss" as *;

:root {
  --el-color-primary: green;
}

$ty-color-green:#3cb0a7;
$ty-color-blue:#337ab7;
$ty-color-red:#ed5565;
$ty-color-cyan:#a0d468;
$ty-color-orange: #f58410;
$ty-color-darkBlue:#3f5266;
$ty-color-gray:#ccc;
$ty-color-gray-hover:#ddd;

// bounce
$tyBounce-color-blue: #488fea;
$tyBounce-color-blue-hover: #3f89e7;
$tyBounce-color-green:#48cfad;
$tyBounce-color-gray:#e8e8e8;
$tyBounce-color-red:$ty-color-red;
$tyBounce-color-orange:$ty-color-orange;
$tyBounce-color-cyan:$ty-color-cyan;
