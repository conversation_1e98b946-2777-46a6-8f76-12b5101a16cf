package cn.sphd.miners.modules.material.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.material.entity.MtSupplierMaterial;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2016/9/19.
 */
public interface MtSupplierMaterialDao extends IBaseDao<MtSupplierMaterial,Serializable>{

    List<MtSupplierMaterial> getMtSupplierMaterialByNameAndCodeName(Integer oid, String codeName, String name);

    MtSupplierMaterial getMtSupplierMaterialByMIdANDSID(int sbId, int id);

    Map getLastPurchasedData(Integer oid);

    List<MtSupplierMaterial> getBySupplierId(Integer supplierId);

    MtSupplierMaterial getById(Integer supplierMaterial);
}
