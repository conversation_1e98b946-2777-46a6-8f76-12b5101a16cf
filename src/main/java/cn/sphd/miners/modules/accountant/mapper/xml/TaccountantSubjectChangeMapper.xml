<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.accountant.mapper.TaccountantSubjectChangeMapper" >
  <resultMap id="BaseResultMap" type="TaccountantSubjectChange" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="org" property="org" jdbcType="INTEGER" />
    <result column="change_date" property="changeDate" jdbcType="DATE" />
    <result column="is_changed" property="isChanged" jdbcType="BIT" />
    <result column="subject" property="subject" jdbcType="VARCHAR" />
    <result column="parent" property="parent" jdbcType="VARCHAR" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="category" property="category" jdbcType="INTEGER" />
    <result column="level" property="level" jdbcType="INTEGER" />
    <result column="orders" property="orders" jdbcType="INTEGER" />
    <result column="state" property="state" jdbcType="BIT" />
    <result column="max_child_subjects" property="maxChildSubjects" jdbcType="INTEGER" />
    <result column="account_month" property="accountMonth" jdbcType="CHAR" />
    <result column="initial_amount" property="initialAmount" jdbcType="DECIMAL" />
    <result column="initial_quantity" property="initialQuantity" jdbcType="DOUBLE" />
    <result column="beginning_balance" property="beginningBalance" jdbcType="DECIMAL" />
    <result column="previous_balance" property="previousBalance" jdbcType="DECIMAL" />
    <result column="beginning_quantity" property="beginningQuantity" jdbcType="DOUBLE" />
    <result column="previous_quantity" property="previousQuantity" jdbcType="DOUBLE" />
    <result column="carried_forward_date" property="carriedForwardDate" jdbcType="DATE" />
    <result column="balance" property="balance" jdbcType="DECIMAL" />
    <result column="quantity" property="quantity" jdbcType="DOUBLE" />
    <result column="summary" property="summary" jdbcType="VARCHAR" />
    <result column="credit" property="credit" jdbcType="DECIMAL" />
    <result column="debit" property="debit" jdbcType="DECIMAL" />
    <result column="credit_accumulative" property="creditAccumulative" jdbcType="DECIMAL" />
    <result column="credit_accumulative_quantity" property="creditAccumulativeQuantity" jdbcType="DOUBLE" />
    <result column="debit_accumulative" property="debitAccumulative" jdbcType="DECIMAL" />
    <result column="debit_accumulative_quantity" property="debitAccumulativeQuantity" jdbcType="DOUBLE" />
    <result column="beginning_direction" property="beginningDirection" jdbcType="CHAR" />
    <result column="previous_direction" property="previousDirection" jdbcType="CHAR" />
    <result column="end_direction" property="endDirection" jdbcType="CHAR" />
    <result column="balance_direction" property="balanceDirection" jdbcType="CHAR" />
    <result column="measure_unit" property="measureUnit" jdbcType="VARCHAR" />
    <result column="quantity_assisting_accounting" property="quantityAssistingAccounting" jdbcType="CHAR" />
    <result column="label" property="label" jdbcType="CHAR" />
    <result column="memo" property="memo" jdbcType="VARCHAR" />
    <result column="relevance_type" property="relevanceType" jdbcType="CHAR" />
    <result column="relevance_item" property="relevanceItem" jdbcType="INTEGER" />
    <result column="establish" property="establish" jdbcType="INTEGER" />
    <result column="creator" property="creator" jdbcType="INTEGER" />
    <result column="create_name" property="createName" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="updator" property="updator" jdbcType="INTEGER" />
    <result column="update_name" property="updateName" jdbcType="VARCHAR" />
    <result column="update_date" property="updateDate" jdbcType="TIMESTAMP" />
    <result column="approve_Item" property="approveItem" jdbcType="INTEGER" />
    <result column="approve_status" property="approveStatus" jdbcType="CHAR" />
    <result column="approve_level" property="approveLevel" jdbcType="INTEGER" />
    <result column="auditor" property="auditor" jdbcType="INTEGER" />
    <result column="auditor_name" property="auditorName" jdbcType="VARCHAR" />
    <result column="audit_date" property="auditDate" jdbcType="TIMESTAMP" />
    <result column="operation" property="operation" jdbcType="CHAR" />
    <result column="apply_memo" property="applyMemo" jdbcType="VARCHAR" />
    <result column="approve_memo" property="approveMemo" jdbcType="VARCHAR" />
    <result column="message_id" property="messageId" jdbcType="INTEGER" />
    <result column="previous_id" property="previousId" jdbcType="INTEGER" />
    <result column="version_no" property="versionNo" jdbcType="INTEGER" />
  </resultMap>

  <resultMap id="BaseSubjectMap" type="TaccountantSubjectChangeDto" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="org" property="org" jdbcType="INTEGER" />
    <result column="change_date" property="changeDate" jdbcType="DATE" />
    <result column="is_changed" property="isChanged" jdbcType="BIT" />
    <result column="subject" property="subject" jdbcType="VARCHAR" />
    <result column="parent" property="parent" jdbcType="VARCHAR" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="category" property="category" jdbcType="INTEGER" />
    <result column="categoryname" property="categoryname" jdbcType="VARCHAR" />
    <result column="level" property="level" jdbcType="INTEGER" />
    <result column="orders" property="orders" jdbcType="INTEGER" />
    <result column="state" property="state" jdbcType="BIT" />
    <result column="max_child_subjects" property="maxChildSubjects" jdbcType="INTEGER" />
    <result column="account_month" property="accountMonth" jdbcType="CHAR" />
    <result column="balance_direction" property="balanceDirection" jdbcType="CHAR" />
    <result column="measure_unit" property="measureUnit" jdbcType="VARCHAR" />
    <result column="quantity_assisting_accounting" property="quantityAssistingAccounting" jdbcType="CHAR" />
    <result column="establish" property="establish" jdbcType="INTEGER" />
    <result column="creator" property="creator" jdbcType="INTEGER" />
    <result column="create_name" property="createName" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, org, change_date, is_changed, subject, parent, name, category, level, orders, 
    state, max_child_subjects, account_month, initial_amount, initial_quantity, beginning_balance, 
    previous_balance, beginning_quantity, previous_quantity, carried_forward_date, balance, 
    quantity, summary, credit, debit, credit_accumulative, credit_accumulative_quantity, 
    debit_accumulative, debit_accumulative_quantity, beginning_direction, previous_direction, 
    end_direction, balance_direction, measure_unit, quantity_assisting_accounting, label, 
    memo, relevance_type, relevance_item, establish, creator, create_name, create_date, 
    updator, update_name, update_date, approve_Item, approve_status, approve_level, auditor, 
    auditor_name, audit_date, operation, apply_memo, approve_memo, message_id, previous_id, 
    version_no
  </sql>

  <sql id="Base_subject_Column" >
    id, org, change_date, is_changed, subject, parent, name, category, level, orders,
    state, max_child_subjects, account_month, balance_direction, measure_unit, quantity_assisting_accounting, label,
    memo, establish, creator, create_name, create_date
  </sql>

  <select id="getSingle" resultMap="BaseResultMap" parameterType="TaccountantSubjectChange" >
    select 
    <include refid="Base_Column_List" />
    from t_accountant_subject_change
    where id = #{id,jdbcType=INTEGER}
  </select>

  <select id="listPage" resultMap="BaseResultMap" parameterType="TaccountantSubjectChange" >
    select
    <include refid="Base_Column_List" />
    from t_accountant_subject_change
  </select>


    <select id="getChangeSubjects" resultMap="BaseSubjectMap">
      select
      a.subject, a.parent, a.name, a.category, a.level, a.state, a.balance_direction AS balanceDirection,
      a.creator, a.max_child_subjects AS maxChildSubjects, s.name AS categoryname,a.end_direction AS  endDirection,
      a.quantity_assisting_accounting AS  quantityAssistingAccounting,a.measure_unit AS measureUnit
      from t_accountant_subject_change a, t_accountant_subject_category s
      <where>
        a.category = s.id and a.org=#{org} and a.is_changed = #{isChanged} and a.establish=#{establish}
        <if test="isChanged==1">
          and DATE_FORMAT(a.change_date, '%Y-%m-%d') = DATE_FORMAT(#{changeDate}, '%Y-%m-%d')
        </if>

        AND a.category IN
        <foreach item="item" index="index" collection="categoryList" open="(" separator="," close=")">
          #{item}
        </foreach>

      </where>
      order by a.subject asc
    </select>


    <delete id="delete" parameterType="TaccountantSubjectChange" >
    delete from t_accountant_subject_change
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <insert id="copySubjectToChangeTable">
    insert into t_accountant_subject_change
    (change_date,is_changed,subject, org, parent, name, category, establish,level, state,max_child_subjects, creator,create_name,create_date, balance_direction, quantity_assisting_accounting,measure_unit)
    select #{changeDate},#{isChanged},subject, #{org}, parent, name, category, #{establish},level,state,max_child_subjects,#{creator},#{createName},#{createDate}, balance_direction, quantity_assisting_accounting,measure_unit from t_accountant_subject where org=#{org}
  </insert>

  <insert id="insert" parameterType="TaccountantSubjectChange" useGeneratedKeys="true" keyProperty="id">
    insert into t_accountant_subject_change
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="org != null" >
        org,
      </if>
      <if test="changeDate != null" >
        change_date,
      </if>
      <if test="isChanged != null" >
        is_changed,
      </if>
      <if test="subject != null" >
        subject,
      </if>
      <if test="parent != null" >
        parent,
      </if>
      <if test="name != null" >
        name,
      </if>
      <if test="category != null" >
        category,
      </if>
      <if test="level != null" >
        level,
      </if>
      <if test="orders != null" >
        orders,
      </if>
      <if test="state != null" >
        state,
      </if>
      <if test="maxChildSubjects != null" >
        max_child_subjects,
      </if>
      <if test="accountMonth != null" >
        account_month,
      </if>
      <if test="initialAmount != null" >
        initial_amount,
      </if>
      <if test="initialQuantity != null" >
        initial_quantity,
      </if>
      <if test="beginningBalance != null" >
        beginning_balance,
      </if>
      <if test="previousBalance != null" >
        previous_balance,
      </if>
      <if test="beginningQuantity != null" >
        beginning_quantity,
      </if>
      <if test="previousQuantity != null" >
        previous_quantity,
      </if>
      <if test="carriedForwardDate != null" >
        carried_forward_date,
      </if>
      <if test="balance != null" >
        balance,
      </if>
      <if test="quantity != null" >
        quantity,
      </if>
      <if test="summary != null" >
        summary,
      </if>
      <if test="credit != null" >
        credit,
      </if>
      <if test="debit != null" >
        debit,
      </if>
      <if test="creditAccumulative != null" >
        credit_accumulative,
      </if>
      <if test="creditAccumulativeQuantity != null" >
        credit_accumulative_quantity,
      </if>
      <if test="debitAccumulative != null" >
        debit_accumulative,
      </if>
      <if test="debitAccumulativeQuantity != null" >
        debit_accumulative_quantity,
      </if>
      <if test="beginningDirection != null" >
        beginning_direction,
      </if>
      <if test="previousDirection != null" >
        previous_direction,
      </if>
      <if test="endDirection != null" >
        end_direction,
      </if>
      <if test="balanceDirection != null" >
        balance_direction,
      </if>
      <if test="measureUnit != null" >
        measure_unit,
      </if>
      <if test="quantityAssistingAccounting != null" >
        quantity_assisting_accounting,
      </if>
      <if test="label != null" >
        label,
      </if>
      <if test="memo != null" >
        memo,
      </if>
      <if test="relevanceType != null" >
        relevance_type,
      </if>
      <if test="relevanceItem != null" >
        relevance_item,
      </if>
      <if test="establish != null" >
        establish,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="createName != null" >
        create_name,
      </if>
      <if test="createDate != null" >
        create_date,
      </if>
      <if test="updator != null" >
        updator,
      </if>
      <if test="updateName != null" >
        update_name,
      </if>
      <if test="updateDate != null" >
        update_date,
      </if>
      <if test="approveItem != null" >
        approve_Item,
      </if>
      <if test="approveStatus != null" >
        approve_status,
      </if>
      <if test="approveLevel != null" >
        approve_level,
      </if>
      <if test="auditor != null" >
        auditor,
      </if>
      <if test="auditorName != null" >
        auditor_name,
      </if>
      <if test="auditDate != null" >
        audit_date,
      </if>
      <if test="operation != null" >
        operation,
      </if>
      <if test="applyMemo != null" >
        apply_memo,
      </if>
      <if test="approveMemo != null" >
        approve_memo,
      </if>
      <if test="messageId != null" >
        message_id,
      </if>
      <if test="previousId != null" >
        previous_id,
      </if>
      <if test="versionNo != null" >
        version_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="org != null" >
        #{org,jdbcType=INTEGER},
      </if>
      <if test="changeDate != null" >
        #{changeDate,jdbcType=DATE},
      </if>
      <if test="isChanged != null" >
        #{isChanged,jdbcType=BIT},
      </if>
      <if test="subject != null" >
        #{subject,jdbcType=VARCHAR},
      </if>
      <if test="parent != null" >
        #{parent,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="category != null" >
        #{category,jdbcType=INTEGER},
      </if>
      <if test="level != null" >
        #{level,jdbcType=INTEGER},
      </if>
      <if test="orders != null" >
        #{orders,jdbcType=INTEGER},
      </if>
      <if test="state != null" >
        #{state,jdbcType=BIT},
      </if>
      <if test="maxChildSubjects != null" >
        #{maxChildSubjects,jdbcType=INTEGER},
      </if>
      <if test="accountMonth != null" >
        #{accountMonth,jdbcType=CHAR},
      </if>
      <if test="initialAmount != null" >
        #{initialAmount,jdbcType=DECIMAL},
      </if>
      <if test="initialQuantity != null" >
        #{initialQuantity,jdbcType=DOUBLE},
      </if>
      <if test="beginningBalance != null" >
        #{beginningBalance,jdbcType=DECIMAL},
      </if>
      <if test="previousBalance != null" >
        #{previousBalance,jdbcType=DECIMAL},
      </if>
      <if test="beginningQuantity != null" >
        #{beginningQuantity,jdbcType=DOUBLE},
      </if>
      <if test="previousQuantity != null" >
        #{previousQuantity,jdbcType=DOUBLE},
      </if>
      <if test="carriedForwardDate != null" >
        #{carriedForwardDate,jdbcType=DATE},
      </if>
      <if test="balance != null" >
        #{balance,jdbcType=DECIMAL},
      </if>
      <if test="quantity != null" >
        #{quantity,jdbcType=DOUBLE},
      </if>
      <if test="summary != null" >
        #{summary,jdbcType=VARCHAR},
      </if>
      <if test="credit != null" >
        #{credit,jdbcType=DECIMAL},
      </if>
      <if test="debit != null" >
        #{debit,jdbcType=DECIMAL},
      </if>
      <if test="creditAccumulative != null" >
        #{creditAccumulative,jdbcType=DECIMAL},
      </if>
      <if test="creditAccumulativeQuantity != null" >
        #{creditAccumulativeQuantity,jdbcType=DOUBLE},
      </if>
      <if test="debitAccumulative != null" >
        #{debitAccumulative,jdbcType=DECIMAL},
      </if>
      <if test="debitAccumulativeQuantity != null" >
        #{debitAccumulativeQuantity,jdbcType=DOUBLE},
      </if>
      <if test="beginningDirection != null" >
        #{beginningDirection,jdbcType=CHAR},
      </if>
      <if test="previousDirection != null" >
        #{previousDirection,jdbcType=CHAR},
      </if>
      <if test="endDirection != null" >
        #{endDirection,jdbcType=CHAR},
      </if>
      <if test="balanceDirection != null" >
        #{balanceDirection,jdbcType=CHAR},
      </if>
      <if test="measureUnit != null" >
        #{measureUnit,jdbcType=VARCHAR},
      </if>
      <if test="quantityAssistingAccounting != null" >
        #{quantityAssistingAccounting,jdbcType=CHAR},
      </if>
      <if test="label != null" >
        #{label,jdbcType=CHAR},
      </if>
      <if test="memo != null" >
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="relevanceType != null" >
        #{relevanceType,jdbcType=CHAR},
      </if>
      <if test="relevanceItem != null" >
        #{relevanceItem,jdbcType=INTEGER},
      </if>
      <if test="establish != null" >
        #{establish,jdbcType=INTEGER},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createName != null" >
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null" >
        #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateName != null" >
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null" >
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="approveItem != null" >
        #{approveItem,jdbcType=INTEGER},
      </if>
      <if test="approveStatus != null" >
        #{approveStatus,jdbcType=CHAR},
      </if>
      <if test="approveLevel != null" >
        #{approveLevel,jdbcType=INTEGER},
      </if>
      <if test="auditor != null" >
        #{auditor,jdbcType=INTEGER},
      </if>
      <if test="auditorName != null" >
        #{auditorName,jdbcType=VARCHAR},
      </if>
      <if test="auditDate != null" >
        #{auditDate,jdbcType=TIMESTAMP},
      </if>
      <if test="operation != null" >
        #{operation,jdbcType=CHAR},
      </if>
      <if test="applyMemo != null" >
        #{applyMemo,jdbcType=VARCHAR},
      </if>
      <if test="approveMemo != null" >
        #{approveMemo,jdbcType=VARCHAR},
      </if>
      <if test="messageId != null" >
        #{messageId,jdbcType=INTEGER},
      </if>
      <if test="previousId != null" >
        #{previousId,jdbcType=INTEGER},
      </if>
      <if test="versionNo != null" >
        #{versionNo,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <update id="update" parameterType="TaccountantSubjectChange" >
    update t_accountant_subject_change
    <set >
      <if test="org != null" >
        org = #{org,jdbcType=INTEGER},
      </if>
      <if test="changeDate != null" >
        change_date = #{changeDate,jdbcType=DATE},
      </if>
      <if test="isChanged != null" >
        is_changed = #{isChanged,jdbcType=BIT},
      </if>
      <if test="subject != null" >
        subject = #{subject,jdbcType=VARCHAR},
      </if>
      <if test="parent != null" >
        parent = #{parent,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="category != null" >
        category = #{category,jdbcType=INTEGER},
      </if>
      <if test="level != null" >
        level = #{level,jdbcType=INTEGER},
      </if>
      <if test="orders != null" >
        orders = #{orders,jdbcType=INTEGER},
      </if>
      <if test="state != null" >
        state = #{state,jdbcType=BIT},
      </if>
      <if test="maxChildSubjects != null" >
        max_child_subjects = #{maxChildSubjects,jdbcType=INTEGER},
      </if>
      <if test="accountMonth != null" >
        account_month = #{accountMonth,jdbcType=CHAR},
      </if>
      <if test="initialAmount != null" >
        initial_amount = #{initialAmount,jdbcType=DECIMAL},
      </if>
      <if test="initialQuantity != null" >
        initial_quantity = #{initialQuantity,jdbcType=DOUBLE},
      </if>
      <if test="beginningBalance != null" >
        beginning_balance = #{beginningBalance,jdbcType=DECIMAL},
      </if>
      <if test="previousBalance != null" >
        previous_balance = #{previousBalance,jdbcType=DECIMAL},
      </if>
      <if test="beginningQuantity != null" >
        beginning_quantity = #{beginningQuantity,jdbcType=DOUBLE},
      </if>
      <if test="previousQuantity != null" >
        previous_quantity = #{previousQuantity,jdbcType=DOUBLE},
      </if>
      <if test="carriedForwardDate != null" >
        carried_forward_date = #{carriedForwardDate,jdbcType=DATE},
      </if>
      <if test="balance != null" >
        balance = #{balance,jdbcType=DECIMAL},
      </if>
      <if test="quantity != null" >
        quantity = #{quantity,jdbcType=DOUBLE},
      </if>
      <if test="summary != null" >
        summary = #{summary,jdbcType=VARCHAR},
      </if>
      <if test="credit != null" >
        credit = #{credit,jdbcType=DECIMAL},
      </if>
      <if test="debit != null" >
        debit = #{debit,jdbcType=DECIMAL},
      </if>
      <if test="creditAccumulative != null" >
        credit_accumulative = #{creditAccumulative,jdbcType=DECIMAL},
      </if>
      <if test="creditAccumulativeQuantity != null" >
        credit_accumulative_quantity = #{creditAccumulativeQuantity,jdbcType=DOUBLE},
      </if>
      <if test="debitAccumulative != null" >
        debit_accumulative = #{debitAccumulative,jdbcType=DECIMAL},
      </if>
      <if test="debitAccumulativeQuantity != null" >
        debit_accumulative_quantity = #{debitAccumulativeQuantity,jdbcType=DOUBLE},
      </if>
      <if test="beginningDirection != null" >
        beginning_direction = #{beginningDirection,jdbcType=CHAR},
      </if>
      <if test="previousDirection != null" >
        previous_direction = #{previousDirection,jdbcType=CHAR},
      </if>
      <if test="endDirection != null" >
        end_direction = #{endDirection,jdbcType=CHAR},
      </if>
      <if test="balanceDirection != null" >
        balance_direction = #{balanceDirection,jdbcType=CHAR},
      </if>
      <if test="measureUnit != null" >
        measure_unit = #{measureUnit,jdbcType=VARCHAR},
      </if>
      <if test="quantityAssistingAccounting != null" >
        quantity_assisting_accounting = #{quantityAssistingAccounting,jdbcType=CHAR},
      </if>
      <if test="label != null" >
        label = #{label,jdbcType=CHAR},
      </if>
      <if test="memo != null" >
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="relevanceType != null" >
        relevance_type = #{relevanceType,jdbcType=CHAR},
      </if>
      <if test="relevanceItem != null" >
        relevance_item = #{relevanceItem,jdbcType=INTEGER},
      </if>
      <if test="establish != null" >
        establish = #{establish,jdbcType=INTEGER},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createName != null" >
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null" >
        updator = #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateName != null" >
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="approveItem != null" >
        approve_Item = #{approveItem,jdbcType=INTEGER},
      </if>
      <if test="approveStatus != null" >
        approve_status = #{approveStatus,jdbcType=CHAR},
      </if>
      <if test="approveLevel != null" >
        approve_level = #{approveLevel,jdbcType=INTEGER},
      </if>
      <if test="auditor != null" >
        auditor = #{auditor,jdbcType=INTEGER},
      </if>
      <if test="auditorName != null" >
        auditor_name = #{auditorName,jdbcType=VARCHAR},
      </if>
      <if test="auditDate != null" >
        audit_date = #{auditDate,jdbcType=TIMESTAMP},
      </if>
      <if test="operation != null" >
        operation = #{operation,jdbcType=CHAR},
      </if>
      <if test="applyMemo != null" >
        apply_memo = #{applyMemo,jdbcType=VARCHAR},
      </if>
      <if test="approveMemo != null" >
        approve_memo = #{approveMemo,jdbcType=VARCHAR},
      </if>
      <if test="messageId != null" >
        message_id = #{messageId,jdbcType=INTEGER},
      </if>
      <if test="previousId != null" >
        previous_id = #{previousId,jdbcType=INTEGER},
      </if>
      <if test="versionNo != null" >
        version_no = #{versionNo,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateParent">
    update t_accountant_subject_change set max_child_subjects=#{maxChildSubjects} where org=#{org} and DATE_FORMAT(change_date, '%Y-%m-%d')=DATE_FORMAT(#{changeDate}, '%Y-%m-%d') and is_changed = #{isChanged} and establish=#{establish} and subject=#{subject}
  </update>
  <update id="updateSubjectInfo">
    update t_accountant_subject_change set quantity_assisting_accounting = #{quantityAssistingAccounting},measure_unit = #{measureUnit},name=#{name} where org=#{org} and DATE_FORMAT(change_date, '%Y-%m-%d')=DATE_FORMAT(#{changeDate}, '%Y-%m-%d') and subject=#{subject}
  </update>
  <update id="updateState">

    update t_accountant_subject_change set state = #{state} where org=#{org} and DATE_FORMAT(change_date, '%Y-%m-%d')=DATE_FORMAT(#{changeDate}, '%Y-%m-%d') and is_changed = #{isChanged} and subject like CONCAT(#{subject},'%')

  </update>
</mapper>