package cn.sphd.miners.modules.thirdPlatform.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table ( name ="t_tp_merchant" )
public class TpMerchant extends BaseEntity {
    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;//`id` INTEGER NOT NULL AUTO_INCREMENT COMMENT '主键id',
    @Column(name = "tg_code")
    private Byte tgCode;//`tg_code` TINYINT DEFAULT NULL COMMENT '第三方大厂(1-weChat,2-byteDance)',
    @Column(length=32)
    private String mchid;//`mchid` VARCHAR(32) CHARACTER SET latin1 COLLATE latin1_bin  DEFAULT NULL COMMENT '商户号',
    @Column(name = "api_new_version")
    private Byte apiNewVersion;//`api_new_version` TINYINT DEFAULT NULL COMMENT '最新Api版本',
    @Column(name = "api_v_old_key", length = 32)
    private String apiVOldKey;//`api_v_old_key` VARCHAR(32) CHARACTER SET latin1 COLLATE latin1_bin  DEFAULT NULL COMMENT '商户API旧版密钥',
    @Column(name = "api_v_new_key", length = 32)
    private String apiVNewKey;//`api_v_new_key` VARCHAR(32) CHARACTER SET latin1 COLLATE latin1_bin  DEFAULT NULL COMMENT '商户API新版密钥',
    @Column(name = "merchant_serial_number", length = 40)
    private String merchantSerialNumber;//`merchant_serial_number` VARCHAR(40) CHARACTER SET latin1 COLLATE latin1_bin  DEFAULT NULL COMMENT '商户证书序列号',
    @Column(name = "private_key")
    private String privateKey;//`private_key` TEXT  CHARACTER SET latin1 COLLATE latin1_bin  DEFAULT NULL COMMENT '商户证书密钥',
    @Column(name = "create_time")
    @CreationTimestamp
    private Date createTime;//`create_time` DATETIME(3) DEFAULT NULL COMMENT '创建时间',
    @Column(name = "update_time")
    @UpdateTimestamp
    private Date updateTime;//`update_time` DATETIME(3) DEFAULT NULL COMMENT '更新时间',

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Byte getTgCode() {
        return tgCode;
    }

    public void setTgCode(Byte tgCode) {
        this.tgCode = tgCode;
    }

    public String getMchid() {
        return mchid;
    }

    public void setMchid(String mchid) {
        this.mchid = mchid;
    }

    public Byte getApiNewVersion() {
        return apiNewVersion;
    }

    public void setApiNewVersion(Byte apiNewVersion) {
        this.apiNewVersion = apiNewVersion;
    }

    public String getApiVOldKey() {
        return apiVOldKey;
    }

    public void setApiVOldKey(String apiVOldKey) {
        this.apiVOldKey = apiVOldKey;
    }

    public String getApiVNewKey() {
        return apiVNewKey;
    }

    public void setApiVNewKey(String apiVNewKey) {
        this.apiVNewKey = apiVNewKey;
    }

    public String getMerchantSerialNumber() {
        return merchantSerialNumber;
    }

    public void setMerchantSerialNumber(String merchantSerialNumber) {
        this.merchantSerialNumber = merchantSerialNumber;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}