package cn.sphd.miners.modules.generalAffairs.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.generalAffairs.dao.ApprovalInstanceDao;
import cn.sphd.miners.modules.generalAffairs.dao.FinanceAssetsReceiveDao;
import cn.sphd.miners.modules.generalAffairs.entity.ApprovalInstance;
import cn.sphd.miners.modules.generalAffairs.entity.FinanceAssetsReceive;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

/**
 * @ClassName ApprovalInstanceDaoImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/1/9 10:10
 * @Version 1.0
 */
@Repository
public class ApprovalInstanceDaoImpl extends BaseDao<ApprovalInstance,Serializable> implements ApprovalInstanceDao {
}
