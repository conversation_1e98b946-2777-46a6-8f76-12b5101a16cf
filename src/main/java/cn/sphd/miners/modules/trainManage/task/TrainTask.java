package cn.sphd.miners.modules.trainManage.task;

import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.system.service.DlmService;
import cn.sphd.miners.modules.trainManage.service.ExamService;
import org.springframework.beans.factory.annotation.Autowired;

public class TrainTask {
    @Autowired
    DlmService dlmService;
    @Autowired
    ExamService examService;
    public void trainDay(){
        //wyu:获取分布式锁
        String lockKey, methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        if ((lockKey=dlmService.getLock(methodName)) != null) {
            System.out.println("培训每日定时任务开始："  + NewDateUtils.dateToString(System.currentTimeMillis(),"yyyy/MM/dd-HH:mm:ss:SSS"));
            examService.trainTaskDay();
            System.out.println("培训每日定时任务结束："  + NewDateUtils.dateToString(System.currentTimeMillis(),"yyyy/MM/dd-HH:mm:ss:SSS"));
            dlmService.releaseLock(methodName, lockKey);
        }
    }
}
