package cn.sphd.miners.modules.dailyAffairs.service.impl;

import cn.sphd.miners.modules.personal.service.LeaveService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.socket.service.DelayCallback;
import org.springframework.context.ApplicationContext;

/**
 * Created by Administrator on 2018/10/23.
 * 到时间自动驳回请假申请
 */
public class OverturnLeave implements DelayCallback {
    private Integer id;

    public Integer getId() {
        return id;
    }

    public OverturnLeave(Integer id) {
        this.id = id;
    }

    @Override
    public void delayCall(ClusterMessageSendingOperations clusterMessageSendingOperations, ApplicationContext ac) {
        LeaveService leaveService = ac.getBean(LeaveService.class);
        leaveService.overturn(id);
    }
}
