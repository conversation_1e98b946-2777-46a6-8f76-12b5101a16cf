<template>
  <div class="answer" id="answer">
    <!--<h4 class="title2">企业需求调查表</h4>-->
    <div class="todayTime">
      <span style="display:inline-block; width:40px; height:20px; ">
              <el-button circle v-if="showList" icon="el-icon-arrow-left" @click="showList = !showList"></el-button>
      </span>
      {{ todayTime }}
    </div>
    <div class="main_container" v-show="!showList" >
      <p>本二维码内容您已填写并提交过了！</p>
      <p> 最后一版的提交时间：{{ $moment(answerTime).format('YYYY-MM-DD HH:mm:ss') }}
        <span class="linkBtn riBtn" @click="jump()">查看</span></p>
      <p class="tip2">注：调查如尚未终止，您还可在查看页修改后重新提交。</p>
    </div>
    <div class="main_container" v-show="!showList">
      本二维码历次提交的内容  <span class="linkBtn riBtn" @click="getList">查看</span>
    </div>
    <div class="main_container" v-show="showList" >
      <el-table :data="list" stripe style="width:100%" >
        <el-table-column prop="date" label="提交时间" ></el-table-column>
        <el-table-column prop="name" label="操作" >
          <template slot-scope="scope"><span class="linkBtn" @click="jump(scope.row)">查看</span></template>
        </el-table-column>
      </el-table>
    </div>

  </div>
</template>

<style>
  body{ background:#eee;  }
  .el-table .cell {  text-align: center!important;  }
  #answer{ background:#eee; width:100%; box-sizing: border-box; height: 100%;   }
  .answer{ font-size:15px; color: #333; }
  #answer .el-button.is-circle{ padding:8px; }
  .title2{ color: #fff; line-height: 50px; text-align: center; background: #31BFB0;    }
  #answer .main_container{ padding: 15px;  background:#fff; margin-top:15px;  }
  .todayTime{  line-height:40px; height:40px; padding:0 8px; background:#fff;  }
  .riBtn{ float: right;  }
  .linkBtn{ color: #7282ff; display: inline-block;font-weight:bold;  }
  .linkBtn:hover{ font-weight:bolder; cursor:default;   }
  .tip2{ color: #7282ff;  font-size:0.9em; }
</style>

<script>
  export default {
  name: 'home',
  data () {
    return {
      showList: false,
      list: [],
      todayTime: '',
      answerTime: '',
      params: '',
    }
  },
  created () {
    this.params = this.$route.params
    this.answerTime = this.$route.params.answerTime
    let serverTime = this.params.serverTime
    this.todayTime = `今天是${this.$moment(serverTime).format('YYYY年MM月DD日')} ${this.$moment(serverTime).format('dddd')}` ;
    // wyu：打印base传过来的参数
    console.log(this.$route.params)
    console.log(window.parent.$.webRoot)
    if(this.params.isHistory){
      this.getList()
    }
  },
  methods: {
    jump (item) {
      console.log(item)
      let params = this.params
      params.goback = true
      params.isHistory = false
      params.hisID = false
      if(item){
        params.isHistory = true
        params.hisID = item.id
      }
      this.$router.push({
        name: 'home',
        params: params
      })
    },
    getList () {
      let _this = this
      this.$toast.loading({
        message: '正在查询...',
        forbidClick: true,
        loadingType: 'spinner'
      })
      _this.$http.post('../../../survey/surveyObjectHistoryList.do', { 'id': this.params.objective }, {
        emulateJSON: true
      }).then((response) => {
        _this.$toast.clear()
        console.log(response.body)
        let res = response.body
        _this.list = res || []
        _this.showList = true
        _this.list.forEach(function (item) {
          item.date =  _this.$moment(item.createDate).format('YYYY-MM-DD HH:mm:ss')
        })
      }).catch(function (err) {
        console.log('跳转错误')
        console.log(err)
        _this.loading = false
        _this.$toast.fail('系统错误，请重试！')
      })
    }
  }
}
</script>
