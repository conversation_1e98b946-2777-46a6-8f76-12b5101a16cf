package cn.sphd.miners.modules.generalAffairs.entity;

import cn.sphd.miners.modules.system.entity.User;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by Administrator on 2015/12/18.
 * 奖惩表
 */
@Entity
@Table(name="t_personal_reward_punishment")
public class PersonalRewardPunishment implements Serializable {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;  //奖惩id

    @Column(name="occur_date"   , nullable=true , unique=false)
    private Date occurDate;//发生日期

    @Column(name="content"  , length=255 , nullable=true , unique=false)
    private String content;//内容

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;//备注

    @Column(name="operator"   , nullable=true , unique=false)
    private Integer operator;//������
    @Column(name="operator_name"  , nullable=true , unique=false)
    private String operatorName;

    //��user��Ķ��һ��ϵ
    @ManyToOne(fetch= FetchType.LAZY )
    @JoinColumn(name="user", referencedColumnName = "userID" , nullable=true , unique=false , insertable=true, updatable=true)
    private User user;

    @Column(name="user"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer user_;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getOccurDate() {
        return occurDate;
    }

    public void setOccurDate(Date occurDate) {
        this.occurDate = occurDate;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Integer getUser_() {
        return user_;
    }

    public void setUser_(Integer user_) {
        this.user_ = user_;
    }

    public Integer getOperator() {
        return operator;
    }

    public void setOperator(Integer operator) {
        this.operator = operator;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

}
