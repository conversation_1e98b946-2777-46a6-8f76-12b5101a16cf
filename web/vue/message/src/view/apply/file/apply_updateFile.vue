<template>
  <div id="issueFile">
    <TY_NavTop title="文件换版/废止申请" :isBackHome="isBackHome" isSearch="true" @toggleList="screen"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="datePane">今天是{{date_today}}  {{weekDay}}</div>
      <div class="ui-cells">
        <a class="ui-cell" v-on:click="applyTo(item.id)" v-for="(item, index) in listHis" :key="index">
          <div class="fileLists">
            <div class="fileType file_xls"></div>
            <div class="fileInfo">
              <div class="fileName" v-text="item.name" :title="item.name"></div>
              <div class="fileDetail">
                {{(!item.updateName || item.updateName === null || item.updateName === undefined)?item.createName:item.updateName}}
                {{(!item.updateDate || item.updateDate === null || item.updateDate === undefined)?item.createDate:item.updateDate}}
              </div>
            </div>
            <div class="fileFormat">
              <span class="fileVersion">G{{item.changeNum}}</span>
              <span class="fileNo">编号：{{item.fileSn}}</span>
            </div>
          </div>
        </a>
      </div>
    </div>
  </div>
</template>

<script>
import * as moment from 'moment'
import 'moment/locale/zh-cn'
export default {
  name: 'applyFileUpdate',
  data () {
    return {
      loading: true,
      isBackHome: true,
      weekDay: '',
      date_today: moment(new Date()).format('YYYY/MM/DD'),
      listHis: [],
      applyFileList: {},
      listenersUid: []
    }
  },
  created: function () {
    let today = new Date()
    let week = today.getDay()
    let weekArr = ['日', '一', '二', '三', '四', '五', '六']
    this.weekDay = '周' + weekArr[week]
    let _this = this
    _this.sphdSocket.send('applyChangeFileVersion', { 'session': _this.sphdSocket.sessionid, 'userID': _this.sphdSocket.user.userID })
    _this.$http.post('../../../res/applyChangeFileVersion.do', { 'userID': _this.sphdSocket.user.userID }, {
      emulateJSON: true
    }).then((response) => {
      let reqData = response.body.data
      _this.listHis = reqData.resHis
      _this.loading = false
    }).catch(function () {
      _this.loading = false
      _this.$message({
        type: 'error',
        message: '系统错误，请重试！'
      })
    })
    this.listenersUid = [
      _this.sphdSocket.subscribe('applyChangeFileVersion', function (data) { // 新申请文件换版、 终止换版时用
        let reqData = JSON.parse(data)
        let update = reqData.operate
        let newDate = {}
        if (update > 0) { // 增加一条
          newDate = reqData.resHis
          _this.listHis.push(newDate)
        } else if (update < 0) { // 减少一条
          let deletId = reqData.resHis.id
          _this.listHis.forEach(function (item, index) {
            if (deletId === item.id) {
              _this.listHis.splice(index, 1)
            }
          })
        }
      }, null, 'user')
    ]
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    applyTo: function (id) {
      this.$router.push({
        path: `/updateFileDetail/${id}`
      })
    },
    screen: function () {
      this.$router.push({
        path: '/applyFileQuery',
        name: 'applyFileQuery',
        params: {
          event: 2
        }
      })
    }
  }
}
</script>
