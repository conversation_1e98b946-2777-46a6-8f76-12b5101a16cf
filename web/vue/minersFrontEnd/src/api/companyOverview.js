import {request} from "@/utils/request";

// creator: 李玉婷，2024-09-18 08:24:15，点击承兑汇款台账
export function getAccept(data) {
    return request({
        url: '../return/getAllReturn.do',
        data: data
    })
}
// creator: 李玉婷，2024-09-18 13:40:05，点击承兑汇款台账查看
export function getAcceptDetail(data) {
    return request({
        url: '../return/getReturnDetail.do',
        data: data
    })
}
// creator: 李玉婷，2024-09-18 15:21:52，修改记录
export function getReturnRecords(data) {
    return request({
        url: '../return/updateReturnRecords.do',
        data: data
    })
}
// creator: 李玉婷，2024-09-18 14:05:38，修改记录查看
export function getHistoryDetail(data) {
    return request({
        url: '../return/updateReturnRecords.do',
        data: data
    })
}
// creator: 李玉婷，2024-09-18 17:42:16，更多数据
export function getMoreData(data) {
    return request({
        url: '../return/getReturnNum.do',
        data: data
    })
}
// creator: 李玉婷，2024-09-18 18:50:24，更多数据-查  看
export function getMoreDataList(data) {
    return request({
        url: '../return/getReturnList.do',
        data: data
    })
}
// creator: 李玉婷，2024-09-19 09:25:35，统  计
export function getCountData(data) {
    return request({
        url: '../return/getReturnStatisticState.do',
        data: data
    })
}
// creator: 李玉婷，2024-09-19 09:25:35，统  计 查看
export function getByMethod(data) {
    return request({
        url: '../return/getReturnList.do',
        data: data
    })
}
//--------------------资金-------------------
// creator: 李玉婷，2024-09-19 15:32:52，主页面统计的显示
export function getAllAccountData(data) {
    return request({
        url: '../data/getAllAccountData.do',
        data: data
    })
}
// creator: 李玉婷，2024-09-19 17:25:14，按时间查询
export function getDataByTime(data) {
    return request({
        url: '../data/getAllAccountDataByTime.do',
        data: data
    })
}
// creator: 李玉婷，2024-09-19 16:18:55，按账户查询
export function getDataByAccount(data) {
    return request({
        url: '../data/getDataByAccount.do',
        data: data
    })
}
// creator: 李玉婷，2024-09-20 07:36:11，按流水查询
export function getDataByFlow(data) {
    return request({
        url: '../data/getDetailByMonth.do',
        data: data
    })
}
// creator: 李玉婷，2024-09-20 07:36:11，按流水查询
export function getDataDetail(data) {
    return request({
        url: '../data/getOneDetail.do',
        data: data
    })
}
// creator: 李玉婷，2024-09-21 11:30:25，综合查看
export function getOrdLoanDetail(data) {
    return request({
        url: '../loan/ordLoanDetail.do',
        data: data
    })
}
// creator: 李玉婷，2024-09-22 10:14:35，查看回款详情
export function getCollectDetail(data) {
    return request({
        url: '../collectWindow/getSlCollectDetailPC.do',
        data: data
    })
}
// creator: 李玉婷，2024-09-23 14:08:47，查看工资管理的
export function getPayHistory(data) {
    return request({
        url: '../salary/getPayHistory.do',
        data: data
    })
}
// creator: 李玉婷，2024-09-24 09:11:22，获取报销详情
export function getReimburseInfo(data) {
    return request({
        url: '../reimburseWindow/getReimburseInfo.do',
        data: data
    })
}
// creator: 李玉婷，2024-09-24 11:21:36，付款查看 - 按钮
export function getRepaymentDetail(data) {
    return request({
        url: '../loan/ordRepaymentDetail.do',
        data: data
    })
}
// creator: 李玉婷，2024-09-24 13:02:06，借款信息修改记录
export function ordLoanRecordModList(data) {
    return request({
        url: '../loan/ordLoanRecordModList.do',
        data: data
    })
}
// creator: 李玉婷，2024-09-24 13:02:06，付款记录修改记录
export function ordRecordModList(data) {
    return request({
        url: '../loan/ordRecordModList.do',
        data: data
    })
}

// creator: 李玉婷，2024-09-26 10:50:13，表格中的查看，点击后跳转查询页面
export function getAccountMonthOrDay(data) {
    return request({
        url: '../data/getAccountMonthOrDay.do',
        data: data
    })
}
// creator: 李玉婷，2024-09-26 10:50:13，表格中的查看，点击后跳转查询页面
export function getOrgSonOrgs() {
    return request({
        url: '../sonOrg/getOrgSonOrgs.do',
        data: {}
    })
}


/*登录记录*/
// creator: 李玉婷，2025-02-26 14:26:13，获取登录记录列表

export function loginrecord(data) {
    return request({
        url: '../loginrecord/loginRecords.do',
        data: data
    })
}
// creator: 李玉婷，2025-02-26 14:26:13，点击列表中登录查询链接，包括本日、本月、本年和自定义查询结果列表中的“登录查询”
export function loginrecordDetail(data) {
    return request({
        url: '../loginrecord/loginRecordPersonal.do',
        data: data
    })
}
// creator: 李玉婷，2025-02-26 14:26:13，点击列表中登录查询链接，包括本日、本月、本年和自定义查询结果列表中的“登录查询”
export function teamLoginRecordDetail(data) {
    return request({
        url: "../loginTeam/loginRecordPersonalTeam.do",
        data: data
    })
}

// updater：张旭博，2017-09-20 13:42:23，获取登录记录列表
export function loginRecordsTeam(data) {
    return request({
        url: "../loginTeam/loginRecordsTeam.do",
        data: data
    })
}











