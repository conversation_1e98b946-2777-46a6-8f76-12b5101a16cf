package cn.sphd.miners.modules.uploads.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.hibernate.annotations.CreationTimestamp;

import javax.persistence.*;
import java.util.Date;

/**
 * 垃圾文件清理实体类
 * <AUTHOR>
 * @date 2021年03月26日 10:16
 **/
@Entity
@Table(name = "t_upload_junk_file_clean")
public class UploadJunkFileClean extends BaseEntity {
    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;//`id` INTEGER NOT NULL AUTO_INCREMENT,

    @Column
    private Integer org;//`org` INTEGER DEFAULT NULL COMMENT '机构ID',

    @Column(name = "acc_id")
    @JsonIgnore@JSONField(serialize = false)
    private Long accId;

    @Column
    private Long size;//`size` BIGINT DEFAULT NULL COMMENT '文件大小(字节数)',

    @Column(name = "user_id")
    private Integer userId;//`user_id` INTEGER DEFAULT NULL COMMENT '操作人id',

    @Column(name = "user_name")
    private String userName;//`user_name` VARCHAR(100) DEFAULT NULL COMMENT '操作人',

    @Column(name = "cleaned_date")
    @CreationTimestamp
    private Date cleanedDate;//`cleaned_date` DATETIME(3) DEFAULT NULL COMMENT '操作完成时间',

    @Transient
    private String sizeH;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Long getAccId() {
        return accId;
    }

    public void setAccId(Long accId) {
        this.accId = accId;
    }

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Date getCleanedDate() {
        return cleanedDate;
    }

    public void setCleanedDate(Date cleanedDate) {
        this.cleanedDate = cleanedDate;
    }

    public String getSizeH() {
        return sizeH;
    }

    public void setSizeH(String sizeH) {
        this.sizeH = sizeH;
    }
}
