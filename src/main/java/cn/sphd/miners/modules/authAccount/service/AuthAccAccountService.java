package cn.sphd.miners.modules.authAccount.service;

import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.authAccount.entity.AuthAccAccount;
import cn.sphd.miners.modules.authAccount.entity.AuthAccMdse;
import cn.sphd.miners.modules.authAccount.entity.AuthAccValidPeriod;
import cn.sphd.miners.modules.system.entity.User;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface AuthAccAccountService {
    String module = "1.291";
    String defaultMchid = "**********";
    String serviceName = "authAccAccountService";

    //获取私人领地商品
    AuthAccMdse getAuthAccMdse();

    //新增私人领地商品
    AuthAccMdse addAuthAccMdse(User user, AuthAccMdse authAccMdse);

    //修改私人领地商品
    AuthAccMdse upAuthAccMdse(AuthAccMdse authAccMdse);

    //点击模块时获取开通信息等
    Map<String, Object> getOpenAccountMes(AuthInfoDto authInfo);

    //开通一个试用账户
    AuthAccAccount addAuthAccAccount(AuthInfoDto authInfo);

    //计算实际付款金额
    AuthAccAccount countFactFee(AuthInfoDto authInfo);

    //获取有效期表数据
    List<AuthAccValidPeriod> getAuthAccValidPeriodList(AuthInfoDto authInfo);

    AuthAccAccount getAccountSingle(Long accId);

    //获取当前用户（AuthInfoDto）的当前有效的有效期列表中总的有效期，不存在返回null。（计算有效期）
    Date getAuthExp(AuthInfoDto authInfo, Date now);

}
