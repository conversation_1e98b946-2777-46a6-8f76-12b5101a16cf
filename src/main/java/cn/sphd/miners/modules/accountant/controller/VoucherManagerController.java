package cn.sphd.miners.modules.accountant.controller;

import cn.sphd.miners.common.utils.WriteJSONUtil;
import cn.sphd.miners.modules.accountant.service.VoucherManagerService;
import cn.sphd.miners.modules.system.entity.User;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletResponse;

/**
 * Created by root on 17-1-4.
 */
@Controller
@RequestMapping("/accountant")
public class VoucherManagerController {

    @Autowired
    VoucherManagerService voucherManagerService;

    @RequestMapping("/getVoucherManager.do")
    public void getVoucherManager(HttpServletResponse response, User user){
        JSONObject result=new JSONObject();
//        User user=(User)session.getAttribute("user");
        int oid=user.getOid();
//        Integer oid = (Integer) session.getAttribute("oid");
        JSONArray arrVoucherManager=voucherManagerService.arrVoucherManager(oid);
        result.put("arrVoucherManager",arrVoucherManager);
        result.put("status" , 1);
        WriteJSONUtil.writeJSON(result.toString(),response);
    }

}
