package cn.sphd.miners.modules.personal.service;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.generalAffairs.service.AttendanceEventSerivce;
import cn.sphd.miners.modules.personal.dto.LeaveQuery;
import cn.sphd.miners.modules.personal.entity.*;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.BadgeNumberCallback;

import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2018/9/22.
 */
public interface LeaveService extends BadgeNumberCallback, LeaveOutTimeUtilsService, AttendanceEventSerivce {
    //Kind 1-普通(默认),2-补报
    enum Kind {
        ordinary("ordinary", (byte)1),
        supplementary("supplementary", (byte)2);
        private String name;
        private Byte index;
        Kind(String name, Byte index) {
            this.name = name;
            this.index = index;
        }
        public String getName() {
            return name;
        }
        public Byte getIndex() {
            return index;
        }
    }


    void  overturn(Integer id);

    List<PersonnelLeave> getPersonnelLeaveListByUser(Integer userId,Integer oid, String approvalStatus, String actualState, String beginTime, String endTime,Integer type) throws ParseException;

    Double getDuration(Date begin,Date end);

    List<PersonnelLeaveItem> getPersonnelLeaveItemByUserId(Integer userId, String approvalStatus, String actualState, String beginTime, String endTime);

    List<PersonnelLeaveItem> getPersonnelLeaveItemByleaveId(Integer leaveId, String approvalStatus);

    List<PersonnelLeave> getPersonnelLeaveListByTime(Integer userId,Date beginTime, Date endTime);

    //type(1-加班 2-计划请假 3-提前结束请假)  approveStatus(3-驳回)
//    void defaultRejectsApplicant(Integer userId,Integer type,PersonnelLeave personnelLeave,PersonnelLeaveItem personnelLeaveItem,PersonnelOvertime personnelOvertime,String approveStatus);  //申请人系统默认驳回加班/请假

    List<PersonnelLeave> getPersonnelLeaveListByBeginTime(Integer userId,String approvalStatus,String actualState,String beginTime,String endTime); //根据请假的实际开始时间和实际结束时间，查询请假数据

    List<PersonnelLeave> getPersonnelLeaveListByApproverId(Integer leaveUserId,Integer toUserId, String approvalStatus, Integer businessType, String beginTime,String endTime,PageInfo page);   //根据请假实际开始时间、实际结束时间和审批人id，查询审批人审批的所有完成的请假

    //以下为长链接时，修改的加班、请假service

    List<PersonnelLeave> getPersonnelLeaveByApproval(Integer oid,Integer toUserId, String approvalStatus, Integer businessType);  //根据审批人id、状态等查询请假

    PersonnelLeave addPersonnelLeave(String beginTime, String endTime,Integer leaveType, String reason,Byte kind, User user,Integer itemId,Double duration);  //计划请假提交，返回状态

    public void leaveRejectSend(int loginNum,int operate,PersonnelLeave personnelLeave,Integer toUserId,String pass,String title, String content,String tCode);

    Integer getLeaveCounts(Integer userId,String approveStatus);

    HashMap<String,Object> myLeaveInfoById(Integer userId,Integer id,Integer numType,HashMap<String,Object> map);

    PersonnelLeaveItem addPersonnelLeaveItem(PersonnelLeave personnelLeave,String endTime1,String actualReason,User user) throws ParseException;

    void delayCallPersonnelLeaveItem(Integer id);

    //type 1-计划请假申请 2-提前结束请假
    void approvalLeaveApproval(ApprovalProcess approvalProcess, User user, PersonnelLeave leave, Integer type, PersonnelLeaveItem personnelLeaveItem, Integer id);   //审批请假批准

    void approvalLeaveDisapproval(ApprovalProcess approvalProcess,User user,Integer type,PersonnelLeave leave,PersonnelLeaveItem personnelLeaveItem,String reason);

    List<PersonnelLeave> getPersonnelLeaveByApprovalState(Integer userId);  //已批准

    //审批人 提前结束请假的待处理
    List<PersonnelLeave> getPersonnelLeaveByApprovalAdvance(Integer toUserId, String approvalStatus, Integer businessType);

    List<PersonnelLeave> getPersonnelLeaveListQuery(Integer userId,Integer approveStatus,Date beginDate,Date endDate);

    List<LeaveQuery> getLeaveStatistics(List<PersonnelLeave> personnelLeaveList,Integer oid);

    List<LeaveQuery> getLeaveNum(User user, Integer approveStatus, Date beginDate, Date endDate);

    List<PersonnelLeave> getApprovalLeaveListQuery(Integer userId,Integer approveStatus,Date beginDate,Date endDate,Integer applyUserId);

    List<LeaveQuery>  getApprovalNum(User user,Integer approveStatus,Date beginDate,Date endDate,Integer applyUserId);

    void incomingQueryLeave(Integer leaveId);

    Integer getPersonnelLeaveCountsByOid(Integer oid);

    //kind 1-普通 2-补报
    Map<String,Object> addLeave(String beginTime, String endTime,Integer leaveType, String reason,Byte kind,User user,Double ruleTime,Map<String, Object> map);

    String getLeaveTypeName(Integer leaveTypeId,String type);

    JsonResult progressiveApprovalLeave(Integer approvalProcessId, String approvalStatus, Integer type, String reason,User user, String sdf);
}
