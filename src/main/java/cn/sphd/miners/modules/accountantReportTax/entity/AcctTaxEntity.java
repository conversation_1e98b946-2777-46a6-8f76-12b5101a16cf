package cn.sphd.miners.modules.accountantReportTax.entity;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description  税种表
 * <AUTHOR>
 * @Date 2021-09-06 
 */
@Entity ( name ="AcctTaxEntity" )
@Table ( name ="t_accountant_tax" )
public class AcctTaxEntity implements Serializable {

	private static final long serialVersionUID =  8229803983400681106L;

	/**
	 * ID
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
   	@Column(name = "id" )
	private Integer id;

	/**
	 * 税种历史ID
	 */
	@Transient
	private Integer taxHisId;

	/**
	 * 报表ID
	 */
	@Transient
	private Integer report;

	/**
	 * 报表名称
	 */
	@Transient
	private String reportName;

	/**
	 * 频次:0,-随机,1-日,2-周,3-旬,4-半月,5-月,6-季,7-半年,8-年
	 */
	@Transient
	private String frequency;

	/**
	 * 时限集合
	 */
	@Transient
	private List<AcctTaxPeriodEntity> periodList;

//	/**
//	 * 报表
//	 */
//	@Transient
//	private AcctReportEntity reportEntity;

	/**
	 * tax report ID
	 */
	@Transient
	private Integer taxReportId;


   	@Column(name = "org" )
	private Integer org;

	@Column(name = "category" )
	private Integer category;//类别:1-税,2-社保,3-公积金

	@Column(name = "is_system" )
	private Integer isSystem;//是否系统自带,0-不是，1-是

	/**
	 * 名称
	 */
   	@Column(name = "name" )
	private String name;

	/**
	 * 修改记录里的名称
	 */
	@Column(name = "old_name" )
	private String oldName;

	/**
	 * 代码
	 */
   	@Column(name = "code" )
	private String code;

	/**
	 * 开始时间
	 */
   	@Column(name = "begin_time" )
	private Date beginTime;

	/**
	 * 截止时间
	 */
   	@Column(name = "end_time" )
	private Date endTime;

	/**
	 * 是否启用
	 */
   	@Column(name = "enabled" )
	private Integer enabled;

	/**
	 * 启/停用时间
	 */
   	@Column(name = "enabled_time" )
	private Date enabledTime;

	/**
	 * 最后申报期YYYYMMDD
	 */
   	@Column(name = "latest_declare" )
	private String latestDeclare;

	/**
	 * 终止申报期YYYYMMDD
	 */
   	@Column(name = "terminate_period" )
	private String terminatePeriod;

	/**
	 * 终止日期
	 */
   	@Column(name = "terminate_date" )
	private Date terminateDate;

	/**
	 * 排序
	 */
   	@Column(name = "orders" )
	private Integer orders;

	/**
	 * 备注
	 */
   	@Column(name = "memo" )
	private String memo;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_date" )
	private Date createDate;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_date" )
	private Date updateDate;

	/**
	 * 操作:1-增,2-删,3-改,4-改错别字 ,5-改名字,6-变更报表
	 */
   	@Column(name = "operation" )
	private String operation;

	/**
	 * 修改前记录ID
	 */
   	@Column(name = "previous_id" )
	private Integer previousId;

	/**
	 * 版本号,每次修改+1
	 */
   	@Column(name = "version_no" )
	private Integer versionNo;

	/**
	 * 实缴金额
	 */
	@Transient
	private BigDecimal factAmount;

	public Integer getTaxHisId() {
		return taxHisId;
	}

	public void setTaxHisId(Integer taxHisId) {
		this.taxHisId = taxHisId;
	}

	public Integer getCategory() {
		return category;
	}

	public void setCategory(Integer category) {
		this.category = category;
	}

	public Integer getIsSystem() {
		return isSystem;
	}

	public void setIsSystem(Integer isSystem) {
		this.isSystem = isSystem;
	}

	public List<AcctTaxPeriodEntity> getPeriodList() {
		return periodList;
	}

	public void setPeriodList(List<AcctTaxPeriodEntity> periodList) {
		this.periodList = periodList;
	}

	public String getFrequency() {
		return frequency;
	}

	public void setFrequency(String frequency) {
		this.frequency = frequency;
	}

	public String getReportName() {
		return reportName;
	}

	public void setReportName(String reportName) {
		this.reportName = reportName;
	}

//	public AcctReportEntity getReportEntity() {
//		return reportEntity;
//	}
//
//	public void setReportEntity(AcctReportEntity reportEntity) {
//		this.reportEntity = reportEntity;
//	}

	public String getOldName() {
		return oldName;
	}

	public void setOldName(String oldName) {
		this.oldName = oldName;
	}

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getReport() {
		return report;
	}

	public void setReport(Integer report) {
		this.report = report;
	}

	public Integer getOrg() {
		return this.org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public String getName() {
		return this.name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCode() {
		return this.code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public Date getBeginTime() {
		return this.beginTime;
	}

	public void setBeginTime(Date beginTime) {
		this.beginTime = beginTime;
	}

	public Date getEndTime() {
		return this.endTime;
	}

	public void setEndTime(Date endTime) {
		this.endTime = endTime;
	}

	public Integer getEnabled() {
		return this.enabled;
	}

	public void setEnabled(Integer enabled) {
		this.enabled = enabled;
	}

	public Date getEnabledTime() {
		return this.enabledTime;
	}

	public void setEnabledTime(Date enabledTime) {
		this.enabledTime = enabledTime;
	}

	public String getLatestDeclare() {
		return this.latestDeclare;
	}

	public void setLatestDeclare(String latestDeclare) {
		this.latestDeclare = latestDeclare;
	}

	public String getTerminatePeriod() {
		return this.terminatePeriod;
	}

	public void setTerminatePeriod(String terminatePeriod) {
		this.terminatePeriod = terminatePeriod;
	}

	public Date getTerminateDate() {
		return this.terminateDate;
	}

	public void setTerminateDate(Date terminateDate) {
		this.terminateDate = terminateDate;
	}

	public Integer getOrders() {
		return this.orders;
	}

	public void setOrders(Integer orders) {
		this.orders = orders;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getCreator() {
		return this.creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return this.createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getUpdator() {
		return this.updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return this.updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return this.updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public String getOperation() {
		return this.operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public Integer getPreviousId() {
		return this.previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return this.versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}

	public BigDecimal getFactAmount() {
		return factAmount;
	}

	public void setFactAmount(BigDecimal factAmount) {
		this.factAmount = factAmount;
	}

	public Integer getTaxReportId() {
		return taxReportId;
	}

	public void setTaxReportId(Integer taxReportId) {
		this.taxReportId = taxReportId;
	}
}
