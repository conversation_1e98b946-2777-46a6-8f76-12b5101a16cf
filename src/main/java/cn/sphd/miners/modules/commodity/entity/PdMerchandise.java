/**
 * Copyright (c) minuteproject, <EMAIL>
 * All rights reserved.
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License")
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * <p>
 * More information on minuteproject:
 * twitter @minuteproject
 * wiki http://minuteproject.wikispaces.com
 * blog http://minuteproject.blogspot.net
 * <p>
 * <p>
 * template reference :
 * - Minuteproject version : 0.9.5
 * - name      : DomainEntityJPA2Annotation
 * - file name : DomainEntityJPA2Annotation.vm
 * - time      : 2016/09/12 ��Ԫ at 14:29:06 GMT+08:00
 */
/**
 * template reference :
 * - Minuteproject version : 0.9.5
 * - name      : DomainEntityJPA2Annotation
 * - file name : DomainEntityJPA2Annotation.vm
 * - time      : 2016/09/12 ��Ԫ at 14:29:06 GMT+08:00
 */
package cn.sphd.miners.modules.commodity.entity;

import cn.sphd.miners.modules.sales.entity.SlOrdersItem;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;

/**
 *
 * <p>Title: TPdMerchandise</p>
 *
 * <p>Description: Domain Object describing a TPdMerchandise entity</p>
 *
 */
@Entity(name = "PdMerchandise")
@Table(name = "t_pd_merchandise")
public class PdMerchandise implements Serializable {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;


    @Column(name = "org", nullable = true, unique = false)
    private Integer org;   //机构

    @Column(name = "peroid", nullable = true, unique = false)
    private Integer peroid;   //机构

    @Column(name = "inner_sn", length = 100, nullable = true, unique = false)
    private String innerSn;   //内部图号

    @Column(name = "outer_sn", length = 100, nullable = true, unique = false)
    private String outerSn;   //商品图号

    @Column(name = "outer_name", length = 100, nullable = true, unique = false)
    private String outerName;   //商品名称

    @Column(name = "customer_name", nullable = true, unique = false)
    private String customerName;   //客户名称

    @Column(name = "minimum_stock", nullable = true, unique = false)
    private BigDecimal minimumStock;   //最低库存

    @Column(name = "mini_packing_mode", length = 100, nullable = true, unique = false)
    private String miniPackingMode;   //小包装方式

    @Column(name = "mini_packing_material", length = 100, nullable = true, unique = false)
    private String miniPackingMaterial;   //小包装材料

    @Column(name = "mini_packing_amount", nullable = true, unique = false)
    private Long miniPackingAmount;   //小包装数量

    @Column(name = "outer_packing_mode", length = 100, nullable = true, unique = false)
    private String outerPackingMode;    //外包装方式

    @Column(name = "outer_packing_material", length = 100, nullable = true, unique = false)
    private String outerPackingMaterial;   //外包装材料

    @Column(name = "outer_packing_amount", nullable = true, unique = false)
    private Long outerPackingAmount;   //外包装数量

    @Column(name = "unit_price", nullable = true, unique = false)
    private BigDecimal unitPrice;   //含税单价(增专)

    @Column(name = "tax_rate", nullable = true, unique = false)
    private BigDecimal taxRate;  //税率

    @Column(name = "unit_price_notax", nullable = true, unique = false)
    private BigDecimal unitPriceNotax;   //不含税单价(增专)


    @Column(name = "unit_price_invoice", nullable = true, unique = false)
    private BigDecimal unitPriceInvoice;//开票单价(增通)


    @Column(name = "unit_price_noinvoice", nullable = true, unique = false)
    private BigDecimal unitPriceNoinvoice;//不开票单价

    @Column(name = "unit_price_reference", nullable = true, unique = false)
    private BigDecimal unitPriceReference;//参考单价

    @Column(name = "contract_sn", length = 100, nullable = true, unique = false)
    private String contractSn;    //供货合同编号


    @Column(name = "operation", length = 1, nullable = true, unique = false)
    private String operation;  //操作:操作:1-增,2-删,3-修改,4-修改初始库存,5-修改最小库存,6-关联,7-暂停销售/恢复销售,8-修改关联商品,9-修改图片,A-修改视频

    @Column(name = "technical_principal", nullable = true, unique = false)
    private String technicalPrincipal;
    @Column(name = "technical_principal_name", nullable = true, unique = false)
    private String technicalPrincipalName;
    @Column(name = "process_dept", nullable = true, unique = false)
    private Integer processDept;
    @Column(name = "process_dept_name", nullable = true, unique = false)
    private String processDeptName;
    @Column(name = "phrase", nullable = true, unique = false)
    private String phrase;
    @Column(name = "is_contract", nullable = true, unique = false)
    private String isContract;
    @Column(name = "expiration_date", nullable = true, unique = false)
    private Date expirationDate;
    @Column(name = "signature_date", nullable = true, unique = false)
    private Date signatureDate;
    @Column(name = "is_contain", nullable = true, unique = false)
    private String isContain;
    @Column(name = "has_invoice", nullable = true, unique = false)
    private String hasInvoice;
    @Column(name = "invoice_category", nullable = true, unique = false)
    private String invoiceCategory;
    @Column(name = "is_freight", nullable = true, unique = false)
    private String isFreight;


    @Column(name = "memo", length = 255, nullable = true, unique = false)
    private String memo;    //备注

    @Column(name = "creator", nullable = true, unique = false)
    private Integer creator;

    @Column(name = "create_name", length = 100, nullable = true, unique = false)
    private String createName;

    @Column(name = "create_date", nullable = true, unique = false)
    private Date createDate;

    @Column(name = "updator", nullable = true, unique = false)
    private Integer updator;

    @Column(name = "update_name", length = 100, nullable = true, unique = false)
    private String updateName;

    @Column(name = "update_date", nullable = true, unique = false)
    private Date updateDate;

    //是否在售
    @Column(name = "enabled", length = 1, nullable = true, unique = false)
    private String enabled;
    //是否销售过
    @Column(name = "is_saled", length = 1, nullable = true, unique = false)
    private String isSaled;
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "product", referencedColumnName = "id", nullable = true, unique = false, insertable = true, updatable = true)
    private PdBase product;

    @Column(name = "product", nullable = true, unique = false, insertable = false, updatable = false)
    private Integer product_;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "customer", referencedColumnName = "id", nullable = true, unique = false, insertable = true, updatable = true)
    private SlCustomer customer;

    @Column(name = "customer", nullable = true, unique = false, insertable = false, updatable = false)
    private Integer customer_;

    @Column(name = "type", length = 100, nullable = true, unique = false)
    private String type; //类型:1-通用,2-专属

    @Column(name = "model", length = 100, nullable = true, unique = false)
    private String model;//型号

    @Column(name = "specifications", length = 100, nullable = true, unique = false)
    private String specifications;  //规格

    @Column(name = "unit", length = 100, nullable = true, unique = false)
    private String unit; //单位

    //关联人id
    @Column(name = "correlater", nullable = true, unique = false)
    private Integer correlater;

    //关联人姓名
    @Column(name = "correlater_name", length = 100, nullable = true, unique = false)
    private String correlaterName;

    @Column(name = "correlate_date", nullable = true, unique = false)
    private Date correlateDate; //关联时间

    @Column(name = "previous_id", nullable = true, unique = false)
    private Integer previousId;

    @Column(name = "version_no", nullable = true, unique = false)
    private Integer versionNo;


    @Column(name = "price_desc", nullable = true, unique = false)
    private String priceDesc; //价格说明

    @Column(name = "category", length = 100, nullable = true, unique = false)
    private String category;
    //名称是否需要修改
    @Column(name = "name_linkable", nullable = true, unique = false)
    private String nameLinkable;
    //规格型号是否需要修改
    @Column(name = "model_linkable", nullable = true, unique = false)
    private String modelLinkable;
    //修改基本信息原因
    @Column(name = "basic_reason", nullable = true, unique = false)
    private String basicReason;
    //修改价格元素原因
    @Column(name = "price_reason", nullable = true, unique = false)
    private String priceReason;
    //生效选项:1-立即,2-择期
    @Column(name = "effective_option", nullable = true, unique = false)
    private String effectiveOption;
    //价格生效日期
    @Column(name = "effective_date", nullable = true, unique = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date effectiveDate;



    //1.254
    @Column(name = "financer")
    private Integer financer;

    @Column(name = "financer_name")
    private String financerName;

    @Column(name = "financer_time")
    private Date financerTime;

    @Column(name = "unit_deduction")
    private Integer unitDeduction; //单位扣减量 1.326


    //与销售订单明细表的
    @JsonIgnore @JSONField(serialize = false)
    @OneToMany(targetEntity = SlOrdersItem.class, fetch = FetchType.LAZY, mappedBy = "salesRelationship", cascade = CascadeType.REMOVE)
//, cascade=CascadeType.ALL)
    private Set<SlOrdersItem> slOrdersItemHashSet = new HashSet<SlOrdersItem>();


    //商品包装方式表
//    @JsonIgnore @JSONField(serialize = false)
    @OneToMany(targetEntity = PdPack.class, fetch = FetchType.LAZY, mappedBy = "productCustomer", cascade = CascadeType.REMOVE)
    private Set<PdPack> pdPackHashSet = new HashSet<PdPack>();


    @Transient
    private BigDecimal netWeight; //产品重量


    @Column(name = "current_stock", nullable = true, unique = false)
    private BigDecimal currentStock;        //bigint comment '当前库存',

    @Column(name = "available_stock", nullable = true, unique = false)
    private BigDecimal availableStock;      //bigint comment '可用库存',

    @Column(name = "initial_stock", nullable = true, unique = false)
    private BigDecimal initialStock;      //bigint comment '初始库存',


    @Column(name = "packaging_state", nullable = true, unique = false)
    private Integer packagingState;      //包装设置状态:0-未设置,1-设置中,2-设置完成,

    @Column(name = "exclusive_time", nullable = true, unique = false)
    private Integer exclusiveTime;      //成为专属时间:1-今年,2-去年,3-更久之前
    @Column(name = "exclusive_month", nullable = true, unique = false)
    private String exclusiveMonth;

    @Transient
    private double pack;

    @Transient
    private String productName;

    @Transient
    private String cInnerSn;//内部图号(在此表中暂时使用上面的innerSn)

    @Transient
    private String cInnerSnName;//内部名称

    @Transient
    private String position;

    @Transient
    private Integer stockEdited;

    //1.119新增字段 unit_id
    @Column(name = "unit_id")
    private Integer unitId;
    //合同ids
    @Transient
    private Integer contractId;
    //订单数量
    @Transient
    private Integer orderNum;
    //合同编号
    @Transient
    private String contractNumber;


    //添加权限  1商品 2产品
    @Transient
    private String addType;
    //文件列表
    @Transient
    private List fileList;
    //启停记录
    @Transient
    private List startAndStopList;

    @Transient
    List<Map> prices;
    //地址列表
    @Transient
    private List<Map<String, Object>> addressList;

    //订单创建时的商品开票资料
    @Transient
    String oldInvoice;

    //修改后的开票资料
    @Transient
    String newInvoice;

    public static String getInvoiceByType(Integer type, PdMerchandise merchandise, List<PdMerchandiseInvoice> merchandiseInvoices) {

        if (merchandiseInvoices != null && !merchandiseInvoices.isEmpty())
            for (PdMerchandiseInvoice merchandiseInvoice : merchandiseInvoices) {
                    if(type==1){
                        switch (merchandiseInvoice.getPattern()) {
                            case 1:
                                return merchandise.getOuterSn();
                            case 2:
                                return merchandise.getOuterName();
                            case 3:
                                return merchandise.getOuterSn() + merchandise.getOuterName();
                            case 4:
                                return merchandise.getOuterName() + merchandise.getOuterSn();
                            case 5:
                                return merchandiseInvoice.getUserDefined();
                        }
                    }
                    else if (type==2){
                        switch (merchandiseInvoice.getPattern()){
                            case 1 : return "";
                            case 2 : return merchandise.getSpecifications();
                            case 3 : return merchandise.getModel();
                            case 4 : return merchandise.getSpecifications()+merchandise.getModel();
                            case 5 : return merchandise.getModel()+merchandise.getSpecifications();
                            case 6 : return merchandiseInvoice.getUserDefined();
                        }
                    }

            }
        return "";
    }

    public static String getInvoiceByTypeHistory(Integer type, PdMerchandise merchandise, List<PdMerchandiseInvoiceHistory> merchandiseInvoiceHistories) {

        if (merchandiseInvoiceHistories != null && !merchandiseInvoiceHistories.isEmpty())
            for (PdMerchandiseInvoiceHistory merchandiseInvoice : merchandiseInvoiceHistories) {
                if(type==1){
                    switch (merchandiseInvoice.getPattern()) {
                        case 1:
                            return merchandise.getOuterSn();
                        case 2:
                            return merchandise.getOuterName();
                        case 3:
                            return merchandise.getOuterSn() + merchandise.getOuterName();
                        case 4:
                            return merchandise.getOuterName() + merchandise.getOuterSn();
                        case 5:
                            return merchandiseInvoice.getUserDefined();
                    }
                }
                else if (type==2){
                    switch (merchandiseInvoice.getPattern()){
                        case 1 : return "";
                        case 2 : return merchandise.getSpecifications();
                        case 3 : return merchandise.getModel();
                        case 4 : return merchandise.getSpecifications()+merchandise.getModel();
                        case 5 : return merchandise.getModel()+merchandise.getSpecifications();
                        case 6 : return merchandiseInvoice.getUserDefined();
                    }
                }

            }
        return "";
    }

    @Transient
    private String addressListString;

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getcInnerSn() {
        return cInnerSn;
    }

    public void setcInnerSn(String cInnerSn) {
        this.cInnerSn = cInnerSn;
    }

    public String getcInnerSnName() {
        return cInnerSnName;
    }

    public void setcInnerSnName(String cInnerSnName) {
        this.cInnerSnName = cInnerSnName;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getNetWeight() {
        return netWeight;
    }

    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getInnerSn() {
        return innerSn;
    }

    public void setInnerSn(String innerSn) {
        this.innerSn = innerSn;
    }

    public String getOuterSn() {
        return outerSn;
    }

    public void setOuterSn(String outerSn) {
        this.outerSn = outerSn;
    }

    public String getOuterName() {
        return outerName;
    }

    public void setOuterName(String outerName) {
        this.outerName = outerName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public BigDecimal getMinimumStock() {
        return minimumStock;
    }

    public void setMinimumStock(BigDecimal minimumStock) {
        this.minimumStock = minimumStock;
    }

    public String getMiniPackingMode() {
        return miniPackingMode;
    }

    public void setMiniPackingMode(String miniPackingMode) {
        this.miniPackingMode = miniPackingMode;
    }

    public String getMiniPackingMaterial() {
        return miniPackingMaterial;
    }

    public void setMiniPackingMaterial(String miniPackingMaterial) {
        this.miniPackingMaterial = miniPackingMaterial;
    }

    public Long getMiniPackingAmount() {
        return miniPackingAmount;
    }

    public void setMiniPackingAmount(Long miniPackingAmount) {
        this.miniPackingAmount = miniPackingAmount;
    }

    public String getOuterPackingMode() {
        return outerPackingMode;
    }

    public void setOuterPackingMode(String outerPackingMode) {
        this.outerPackingMode = outerPackingMode;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getOuterPackingMaterial() {
        return outerPackingMaterial;
    }

    public void setOuterPackingMaterial(String outerPackingMaterial) {
        this.outerPackingMaterial = outerPackingMaterial;
    }

    public Long getOuterPackingAmount() {
        return outerPackingAmount;
    }

    public void setOuterPackingAmount(Long outerPackingAmount) {
        this.outerPackingAmount = outerPackingAmount;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public Integer getFinancer() {
        return financer;
    }

    public void setFinancer(Integer financer) {
        this.financer = financer;
    }

    public String getFinancerName() {
        return financerName;
    }

    public void setFinancerName(String financerName) {
        this.financerName = financerName;
    }

    public Date getFinancerTime() {
        return financerTime;
    }

    public void setFinancerTime(Date financerTime) {
        this.financerTime = financerTime;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public BigDecimal getUnitPriceNotax() {
        return unitPriceNotax;
    }

    public void setUnitPriceNotax(BigDecimal unitPriceNotax) {
        this.unitPriceNotax = unitPriceNotax;
    }

    public String getContractSn() {
        return contractSn;
    }

    public List<Map> getPrices() {
        return prices;
    }

    public void setPrices(List<Map> prices) {
        this.prices = prices;
    }

    public void setContractSn(String contractSn) {
        this.contractSn = contractSn;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }


    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public PdBase getProduct() {
        return product;
    }

    public void setProduct(PdBase product) {
        this.product = product;
    }

    public Integer getProduct_() {
        return product_;
    }

    public void setProduct_(Integer product) {
        this.product_ = product;
    }

    public SlCustomer getCustomer() {
        return customer;
    }

    public void setCustomer(SlCustomer customer) {
        this.customer = customer;
    }

    public Integer getCustomer_() {
        return customer_;
    }

    public void setCustomer_(Integer customer) {
        this.customer_ = customer;
    }


    public Set<SlOrdersItem> getSlOrdersItemHashSet() {
        return slOrdersItemHashSet;
    }

    public void setSlOrdersItemHashSet(Set<SlOrdersItem> slOrdersItemHashSet) {
        this.slOrdersItemHashSet = slOrdersItemHashSet;
    }

    public Set<PdPack> getPdPackHashSet() {
        return pdPackHashSet;
    }

    public void setPdPackHashSet(Set<PdPack> pdPackHashSet) {
        this.pdPackHashSet = pdPackHashSet;
    }

    public BigDecimal getCurrentStock() {
        return currentStock;
    }

    public void setCurrentStock(BigDecimal currentStock) {
        this.currentStock = currentStock;
    }

    public BigDecimal getAvailableStock() {
        return availableStock;
    }

    public void setAvailableStock(BigDecimal availableStock) {
        this.availableStock = availableStock;
    }

    public double getPack() {
        return pack;
    }

    public void setPack(double pack) {
        this.pack = pack;
    }

    public String getTechnicalPrincipal() {
        return technicalPrincipal;
    }

    public void setTechnicalPrincipal(String technicalPrincipal) {
        this.technicalPrincipal = technicalPrincipal;
    }

    public String getTechnicalPrincipalName() {
        return technicalPrincipalName;
    }

    public void setTechnicalPrincipalName(String technicalPrincipalName) {
        this.technicalPrincipalName = technicalPrincipalName;
    }

    public Integer getProcessDept() {
        return processDept;
    }

    public void setProcessDept(Integer processDept) {
        this.processDept = processDept;
    }

    public String getProcessDeptName() {
        return processDeptName;
    }

    public void setProcessDeptName(String processDeptName) {
        this.processDeptName = processDeptName;
    }

    public String getPhrase() {
        return phrase;
    }

    public void setPhrase(String phrase) {
        this.phrase = phrase;
    }


    public Date getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }

    public Date getSignatureDate() {
        return signatureDate;
    }

    public void setSignatureDate(Date signatureDate) {
        this.signatureDate = signatureDate;
    }

    public String getIsContract() {
        return isContract;
    }

    public void setIsContract(String isContract) {
        this.isContract = isContract;
    }

    public String getIsContain() {
        return isContain;
    }

    public void setIsContain(String isContain) {
        this.isContain = isContain;
    }

    public String getHasInvoice() {
        return hasInvoice;
    }

    public void setHasInvoice(String hasInvoice) {
        this.hasInvoice = hasInvoice;
    }

    public String getInvoiceCategory() {
        return invoiceCategory;
    }

    public void setInvoiceCategory(String invoiceCategory) {
        this.invoiceCategory = invoiceCategory;
    }

    public String getIsFreight() {
        return isFreight;
    }

    public void setIsFreight(String isFreight) {
        this.isFreight = isFreight;
    }


    public Integer getStockEdited() {
        return stockEdited;
    }

    public void setStockEdited(Integer stockEdited) {
        this.stockEdited = stockEdited;
    }


    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public BigDecimal getInitialStock() {
        return initialStock;
    }

    public void setInitialStock(BigDecimal initialStock) {
        this.initialStock = initialStock;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getIsSaled() {
        return isSaled;
    }

    public void setIsSaled(String isSaled) {
        this.isSaled = isSaled;
    }

    public BigDecimal getUnitPriceInvoice() {
        return unitPriceInvoice;
    }

    public void setUnitPriceInvoice(BigDecimal unitPriceInvoice) {
        this.unitPriceInvoice = unitPriceInvoice;
    }

    public BigDecimal getUnitPriceNoinvoice() {
        return unitPriceNoinvoice;
    }

    public void setUnitPriceNoinvoice(BigDecimal unitPriceNoinvoice) {
        this.unitPriceNoinvoice = unitPriceNoinvoice;
    }

    public BigDecimal getUnitPriceReference() {
        return unitPriceReference;
    }

    public void setUnitPriceReference(BigDecimal unitPriceReference) {
        this.unitPriceReference = unitPriceReference;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getCorrelater() {
        return correlater;
    }

    public void setCorrelater(Integer correlater) {
        this.correlater = correlater;
    }

    public String getCorrelaterName() {
        return correlaterName;
    }

    public void setCorrelaterName(String correlaterName) {
        this.correlaterName = correlaterName;
    }

    public Date getCorrelateDate() {
        return correlateDate;
    }

    public void setCorrelateDate(Date correlateDate) {
        this.correlateDate = correlateDate;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    public String getPriceDesc() {
        return priceDesc;
    }

    public void setPriceDesc(String priceDesc) {
        this.priceDesc = priceDesc;
    }

    public Integer getContractId() {
        return contractId;
    }

    public void setContractId(Integer contractId) {
        this.contractId = contractId;
    }

    public String getContractNumber() {
        return contractNumber;
    }

    public String getOldInvoice() {
        return oldInvoice;
    }

    public void setOldInvoice(String oldInvoice) {
        this.oldInvoice = oldInvoice;
    }

    public String getNewInvoice() {
        return newInvoice;
    }

    public void setNewInvoice(String newInvoice) {
        this.newInvoice = newInvoice;
    }

    public void setContractNumber(String contractNumber) {
        this.contractNumber = contractNumber;
    }

    public List getFileList() {
        return fileList;
    }

    public void setFileList(List fileList) {
        this.fileList = fileList;
    }

    public List getStartAndStopList() {
        return startAndStopList;
    }

    public void setStartAndStopList(List startAndStopList) {
        this.startAndStopList = startAndStopList;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getPeroid() {
        return peroid;
    }

    public void setPeroid(Integer peroid) {
        this.peroid = peroid;
    }

    public List<Map<String, Object>> getAddressList() {
        return addressList;
    }

    public void setAddressList(List<Map<String, Object>> addressList) {
        this.addressList = addressList;
    }

    public String getAddressListString() {
        return addressListString;
    }

    public void setAddressListString(String addressListString) {
        this.addressListString = addressListString;
    }

    public String getAddType() {
        return addType;
    }

    public void setAddType(String addType) {
        this.addType = addType;
    }

    public String getNameLinkable() {
        return nameLinkable;
    }

    public void setNameLinkable(String nameLinkable) {
        this.nameLinkable = nameLinkable;
    }

    public String getModelLinkable() {
        return modelLinkable;
    }

    public void setModelLinkable(String modelLinkable) {
        this.modelLinkable = modelLinkable;
    }

    public String getBasicReason() {
        return basicReason;
    }

    public void setBasicReason(String basicReason) {
        this.basicReason = basicReason;
    }

    public String getPriceReason() {
        return priceReason;
    }

    public void setPriceReason(String priceReason) {
        this.priceReason = priceReason;
    }

    public String getEffectiveOption() {
        return effectiveOption;
    }

    public void setEffectiveOption(String effectiveOption) {
        this.effectiveOption = effectiveOption;
    }

    public Date getEffectiveDate() {
        return effectiveDate;
    }

    public void setEffectiveDate(Date effectiveDate) {
        this.effectiveDate = effectiveDate;
    }

    public Integer getPackagingState() {
        return packagingState;
    }

    public void setPackagingState(Integer packagingState) {
        this.packagingState = packagingState;
    }

    public Integer getExclusiveTime() {
        return exclusiveTime;
    }

    public void setExclusiveTime(Integer exclusiveTime) {
        this.exclusiveTime = exclusiveTime;
    }

    public String getExclusiveMonth() {
        return exclusiveMonth;
    }

    public void setExclusiveMonth(String exclusiveMonth) {
        this.exclusiveMonth = exclusiveMonth;
    }

    public Integer getUnitDeduction() {
        return unitDeduction;
    }

    public void setUnitDeduction(Integer unitDeduction) {
        this.unitDeduction = unitDeduction;
    }
}
