<template>
  <div id="financeChangeQueryPer">
    <TY_NavTop title="财务修改"></TY_NavTop>
    <div class="container">
      <div class="ui-message">
        <div class="ui-tip">
          <div class="queryCondition">
            <div>您要查询：</div>
            <div class="queryContent">{{queryInfo.beginDate | formatDay('YYYY年MM月DD日')}}-{{queryInfo.endDate | formatDay('YYYY年MM月DD日')}}期间</div>
            <div class="queryContent">
              <span v-if="paramQuery.approvalStatus === '2'">已批准</span><span v-if="paramQuery.approvalStatus === '3'">已驳回</span>的财务修改申请
            </div>
          </div>
        </div>
        <div class="sendContainer overTimeQuery">
          <div>
            符合查询条件的数据共如下{{approvalProcessList.length}}条
          </div>
        </div>
      </div>
      <div>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in approvalProcessList" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.description}}</div>
                <div class="right">{{item.askName}} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
  .queryCondition{
    background: #fffaeb;
    font-size: 12px;
    padding: 8px;
    margin-bottom: 5px;
  }
</style>

<script>
export default {
  name: 'financeChangeQueryPer',
  data () {
    return {
      loading: true,
      paramQuery: {},
      queryInfo: {},
      approvalProcessList: [],
      state: ''
    }
  },
  created: function () {
    let paramQuery = this.$route.params.data
    if (!paramQuery) {
      paramQuery = JSON.parse(localStorage.getItem('query'))
    }
    this.paramQuery = paramQuery
    let that = this
    this.listenersUid = [
      this.sphdSocket.subscribe('getAllUpdateApply', function (data) {
        that.loading = false
        let getData = JSON.parse(data)
        that.queryInfo.beginDate = getData.beginTime
        that.queryInfo.endDate = getData.endTime
        that.approvalProcessList = getData.approvalProcessList
        console.log('getAllUpdateApply session Socket received OK:' + data)
      }),
      this.sphdSocket.subscribe('getAllUpdateApproval', function (data) {
        that.loading = false
        let getData = JSON.parse(data)
        that.queryInfo.beginDate = getData.beginTime
        that.queryInfo.endDate = getData.endTime
        that.approvalProcessList = getData.approvalProcessList
        console.log('getAllUpdateApproval session Socket received OK:' + data)
      })
    ]
    if (paramQuery.name === 'apply') {
      this.sphdSocket.send('getAllUpdateApply', paramQuery)
    } else {
      this.sphdSocket.send('getAllUpdateApproval', paramQuery)
    }
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    jump: function (id) {
      parent.location.href = '../../../update/toUpdatePage.do?id=' + id
    }
  }
}
</script>
