package cn.sphd.miners.modules.material.controller;

import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.commodity.entity.PdBase;
import cn.sphd.miners.modules.commodity.entity.PdMerchandise;
import cn.sphd.miners.modules.commodity.service.ProductService;
import cn.sphd.miners.modules.material.entity.MtMaterialApply;
import cn.sphd.miners.modules.material.entity.MtMaterialStock;
import cn.sphd.miners.modules.material.entity.MtStock;
import cn.sphd.miners.modules.material.entity.MtStorage;
import cn.sphd.miners.modules.material.service.*;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.text.ParseException;
import java.util.*;

/**
 * Created by 孙文 on 2017/8/15.
 */
@Controller
@RequestMapping("/concession")
public class MtConcessionCtrl {

    @Autowired
    MtMaterialStockService mtMaterialStockService;
    @Autowired
    MtMaterialApplyService mtMaterialApplyService;

    @Autowired
    MtStockService mtStockService;

    @Autowired
    ProductService productService;

    @Autowired
    MaterielService materielService;

    @Autowired
    MtStorageService mtStorageService;

    @Autowired
    ApprovalProcessService approvalProcessService;
      /**
       *@Author:孙文
       *@Date:2017/8/15 14:36
       *@description:让步申请
       */
    @ResponseBody
    @RequestMapping("/application.do")
    public Map CCapplication(User user,HttpServletRequest request)throws ParseException, IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        Integer id=Integer.parseInt(request.getParameter("id"));
        Integer oid=user.getOid();
        
        MtStock mtStock=mtStockService.getByIdAndOid(id,oid);
        if("0".equals(mtStock.getState())){
            map.put("status",0);
            return  map;
        }
        if(mtStock!=null){
            MtMaterialApply mtMaterialApply=new MtMaterialApply();
            mtMaterialApply.setApplicant(mtStock.getCreator());//申请人ID
            mtMaterialApply.setApplicantName(mtStock.getCreateName());//申请人姓名
            mtMaterialApply.setApplyDate(new Date());//申请日期
            mtMaterialApply.setState("1");//入库状态：1-录入，2-提交，3审批通过，4-审批否决（待处理/待入库/已入库/已驳回）
            mtMaterialApply.setType("2");//类型:1-正常审批,2-让步审批,3-重复审批
            mtMaterialApply.setCreateDate(new Date());
            mtMaterialApply.setCreateName(user.getUserName());
            mtMaterialApply.setCreator(user.getUserID());
            mtMaterialApply.setOid(oid);//组织机构
            mtMaterialApply.setParent(mtStock.getApplyId());//父申请id
            mtMaterialApplyService.addMtMaterialApply(mtMaterialApply);//保存出入库申请单

            MtStock mt=new MtStock();
            mt.setCreateDate(new Date());//创建时间
            mt.setCreator(user.getUserID());//创建人id
            mt.setCreateName(user.getUserName());//创建人名称
            mt.setOperator(user.getUserID());//经手人id
            mt.setOperatorName(user.getUserName());//经手人名称
            mt.setOid(oid);
            mt.setInPlan(mtStock.getInPlan());//计划入数量
            mt.setInFact(mtStock.getInFact());//实际数量
            mt.setManufactureDate(mtStock.getManufactureDate()); //生产日期
            mt.setInvalidDate(mtStock.getInvalidDate());  //失效日期
            mt.setApplyId(mtMaterialApply.getId());
            mt.setState("1");//入库状态：1-录入，2-提交，3审批通过，4-审批否决（待处理/待入库/已入库/已驳回）

            PdMerchandise pdCus=productService.getByMtStock0(mtStock.getProductId());

            PdBase pdBase=productService.getById(pdCus.getProduct_());

            MtStorage mtStorage=new MtStorage();
            mtStorage.setOrg(oid);
            mtStorage.setCreateTime(new Date());
            mtStorage.setName(pdBase.getName());//名称

            if (pdBase.getMinimumiStock()!=null){
                mtStorage.setVolume(pdBase.getMinimumiStock().doubleValue());//容量
            }else {
                mtStorage.setVolume(0.0D);
            }
            mtStorageService.addMtStorage(mtStorage);

            MtMaterialStock mtMaterialStock=new MtMaterialStock();
            mtMaterialStock.setCreateTime(new Date());
            mtMaterialStock.setMaterial(pdBase.getId());//商品id
            mtMaterialStock.setMemo(pdBase.getMemo());
            mtMaterialStock.setMaterial(pdBase.getId());//产品ID
            mtMaterialStock.setStorage(mtStorage.getId());//库位ID
            mt.setApplyId(mtMaterialApply.getId());
            mt.setProductId(pdCus.getId());
            mtStockService.saveMtStock(mt);
            mtMaterialStock.setMtStockId(mt.getId());//mtStockID
            mtMaterialStockService.addMtMaterialStock(mtMaterialStock);

            mtStock.setState("0");
            mtStockService.updateMtStock(mtStock);
            //审批过程
            ApprovalProcess ap = new ApprovalProcess();
            ap.setBusiness(mt.getId());
            ap.setLevel(2);
            ap.setDescription("让步申请");
            ap.setBusinessType(4);//业务类型  1-财务修改，2- 加班，3-请假 4-入库
            ap.setUserName(user.getUserName());//审批人
            ap.setCreateDate(new Date());
            ap.setToUser(user.getUserID());
            ap.setToOrg(oid);
            ap.setToUserName("申请让步者");
            ap.setHandleTime(new Date());
            ap.setApproveStatus("1");//1 - 合格，4-驳回
            ap.setApproveMemo("生产");
            approvalProcessService.saveApprovalProcess(ap);
            map.put("mtStock",mt);
            map.put("status",1);
        }else{
            map.put("status",0);
        }

        return map;
    }

      /**
       *@Author:孙文
       *@Date:2017/8/15 14:47
       *@description:让步申请-flag==0,列表-0待让步申请/1待处理/2待入库/3已入库/4已驳回
       *让步受理列表-flag==1-1待处理/2已合格/4已驳回
       */
    @ResponseBody
    @RequestMapping("/concessionList.do")
    public void  concessionList(Integer currPage,Integer pageSize,Integer flag,Integer lastflag,Integer state, User user,HttpServletResponse response)throws IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        if (pageSize == null || "".equals(pageSize) || pageSize <= 0) {
             map.put("status",0);
            ObjectToJson.objectToJson1(map,null, response);
            return ;
        }


        if(flag!=1){
            if(flag!=2) {
                map.put("status", 0);
                ObjectToJson.objectToJson1(map, null, response);
                return;
            }
        }
        if(flag!=lastflag){
            currPage = 1;//默认当前页

        }
        if (currPage == null || "".equals(currPage) || currPage <= 0) {
            currPage = 1;//默认当前页
        }

       // int page=10; //每页最大数量
        int totalPage = 1;//默认总页数
        int first=0;//查询开始
        int end=pageSize;//查询结束


        Integer oid =user.getOid();
        
        List<MtMaterialApply> mtMaterialApply = new ArrayList<MtMaterialApply>();
        List<MtStock> mtStockList = new ArrayList<MtStock>();
        if(flag==1) {
            if (state == 1) {//1待处理

                mtStockList = mtStockService.getMtStockAcceptance1ByConcession(oid, "1");

            }
            if (state == 0) {//待申请

                mtStockList = mtStockService.getMtStockAcceptanceByConcessionApplication(oid);

            }
            if (state == 2) {
                mtStockList = mtStockService.getMtStockAcceptanceByConcession(oid, "1","2");


            }
            if (state == 3) {
                mtStockList = mtStockService.getMtStockAcceptanceByConcession(oid, "1", "3");

            }
            if (state == 4) {
                mtStockList = mtStockService.getMtStockAcceptance1ByConcession(oid, "4");

            }

            double total=mtStockList.size();
            double pageSize1 = pageSize;
            double totalPage1 = Math.ceil(total / pageSize1);
            totalPage =  (int) totalPage1;
            if((pageSize * currPage)>total){
                end=(int)total; //截取终止
            }else{
                end = pageSize * currPage; //截取终止
            }
            first = (currPage - 1) * pageSize;//截取开始
            if(currPage > totalPage){
                mtStockList.clear();
            }else{
                mtStockList = mtStockList.subList(first, end);
            }


            if (mtStockList.size() > 0) {
                for (MtStock mtStock : mtStockList) {
                    PdMerchandise pdMerchandise = productService.getByMtStock0(mtStock.getProductId());
                    if (pdMerchandise != null) {
                        PdBase pdBase = mtStockService.getPdBaseById(pdMerchandise.getProduct_());
                        mtStock.setInnerSn(pdBase.getInnerSn());
                        mtStock.setInnerSnName(pdBase.getName());
                        mtStock.setUnit(pdBase.getUnit());
                        mtStock.setOuterSn(pdMerchandise.getOuterSn());
                        mtStock.setOuterName(pdMerchandise.getOuterName());
                    }
                    List<ApprovalProcess> approvalProcesses=approvalProcessService.getApprovalProcessByBusiness(mtStock.getId(),4,null);
                    mtStock.setApprocessList(approvalProcesses);
                }
            }
        }
            if(flag==2){//入库检查标志

                if(state==1){//待处理
                    mtStockList = mtStockService.getMtStockAcceptance1ByConcession(oid, "1");

                }

                if(state==2){//已合格
                    mtStockList = mtStockService.getMtStockByQualifiedByConcession(oid, "1");


                }
                if(state==4){//已驳回

                    mtStockList = mtStockService.getMtStockAcceptance1ByConcession(oid,"4");


                }

                double total=mtStockList.size();
                double pageSize1 = pageSize;
                double totalPage1 = Math.ceil(total / pageSize1);
                totalPage =  (int) totalPage1;
                if((pageSize * currPage)>total){
                    end=(int)total; //截取终止
                }else{
                    end = pageSize * currPage; //截取终止
                }
                first = (currPage - 1) * pageSize;//截取开始
                if(currPage > totalPage){
                    mtStockList.clear();
                }else{
                    mtStockList = mtStockList.subList(first, end);
                }


                if(mtStockList.size()>0){
                    for(MtStock mtStock:mtStockList){
                        PdMerchandise pdMerchandise=productService.getByMtStock0(mtStock.getProductId());
                        if(pdMerchandise!=null){
                            PdBase pdBase=mtStockService.getPdBaseById(pdMerchandise.getProduct_());
                            mtStock.setInnerSn(pdBase.getInnerSn());
                            mtStock.setInnerSnName(pdBase.getName());
                            mtStock.setUnit(pdBase.getUnit());
                            mtStock.setOuterSn(pdMerchandise.getOuterSn());
                            mtStock.setOuterName(pdMerchandise.getOuterName());
                        }
                        List<ApprovalProcess> approvalProcesses=approvalProcessService.getApprovalProcessByBusiness(mtStock.getId(),4,null);
                        mtStock.setApprocessList(approvalProcesses);
                    }
                }
            }

            map.put("status",1);
            map.put("currPage",currPage);
            map.put("totalPage",totalPage);
            map.put("mtStockList",mtStockList);
            map.put("lastflag",flag);

        ObjectToJson.objectToJson1(map, new String[]{"fromOrg","fromUser","toOrg","toRole","toPost","toUser","description","isMessage","messageTemplate","handleTime","business","oldId","businessType"
                ,"newId","finishId","updateDes","askName","type","approvalFlow","approvalFlow_","reimburse","reimburse_","personnelLeave","personnelOvertime","outPlan","inspectionNo","material","materialName","operator","operatorName","orderId","orderNo","outFact","source","storageLocation","storageNo","unitPrice","destination","billNo","accountNo","instance","type","applicant","parent","action","applyType","quantityPlan"
                ,"quantityFact","memo","description","creator","updator","updateName","updateDate"}, response);
    }


      /**
         *@Author:孙文
         *@Date:2017/8/21 10:24
         *@description: 让步检测合格与不合格
       */
    @ResponseBody
    @RequestMapping("/concessioncheckout.do")
    public Map concessionCheckout  (HttpServletRequest request,User user, HttpServletResponse response){
        Map<String, Object> map = new HashMap<String, Object>();
        Integer oid=user.getOid();
        

        String mtStockId=request.getParameter("mtStockId");//出入库表ID
        String applyId=request.getParameter("applyId");//申请单id
        String qualified=request.getParameter("qualified");//1，合格；0.不合格
        String reason=request.getParameter("reason");//驳回理由
        if (mtStockId == null || "".equals(mtStockId) || applyId == null || "".equals(applyId) ||qualified == null || "".equals(qualified) ) {
            map.put("status",0);

            return map;
        }
        MtMaterialApply mtMaterialApply=mtMaterialApplyService.getById(Integer.valueOf(applyId),oid);

        MtStock mtStock=mtStockService.getByIdAndOid(Integer.valueOf(mtStockId),oid);
        mtStock.setQualified(qualified);//合格:0-不合格,1-合格
        if(qualified.equals("0")){//不合格
            mtStock.setState("4");////入库状态：1-录入，2-提交，3审批通过，4-审批否决（待处理/待入库/已入库/已驳回）
        }
        if(qualified.equals("1")){//合格
            mtStock.setState("2");
        }
        mtStockService.updateMtStock(mtStock);

        ApprovalProcess ap=new ApprovalProcess();
        ap.setBusiness(mtStock.getId());
        ap.setLevel(1);
        ap.setDescription("让步受理");
        ap.setBusinessType(4);//业务类型  1-财务修改，2- 加班，3-请假 4-入库
        ap.setUserName(user.getUserName());//审批人
        ap.setCreateDate(new Date());
        ap.setToUser(user.getUserID());
        ap.setToOrg(oid);
        ap.setToUserName("评审人");
        ap.setApproveMemo("技术");
        ap.setHandleTime(new Date());
        if(qualified.equals("0")){//不合格
            ap.setApproveStatus("4");//1 - 合格，4-驳回
            if(reason!=null&&!reason.equals("")){
                ap.setReason(reason);
            }
        }
        if(qualified.equals("1")){//合格
            ap.setApproveStatus("1");//1 - 合格，4-驳回
        }

        approvalProcessService.saveApprovalProcess(ap);

        mtMaterialApply.setState("2");//待入库
        mtMaterialApplyService.updateMtMaterialApply(mtMaterialApply);


        map.put("status",1);
        return map;
    }



   /* @ResponseBody  //入库流程查看，没写完
    @RequestMapping("/getConcessionFlow.do")
    public void getConcessionFlow(Integer mtStockId, User user, HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        Integer oid=user.getOid();
        
        if(mtStockId==null||"".equals(mtStockId)){
            map.put("state",0);
            return;
        }
        MtStock mtStock=mtStockService.getByIdAndOid(Integer.valueOf(mtStockId),oid);

        if(mtStock!=null){
            List<ApprovalProcess> ap=approvalProcessService.getApprovalProcessByBusiness(Integer.valueOf(mtStockId),4);

        }
    }*/
}
