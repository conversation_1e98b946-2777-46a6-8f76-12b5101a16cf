package cn.sphd.miners.modules.dailyAffairs.service.impl;

import cn.sphd.miners.modules.dailyAffairs.service.InvoiceRegisterService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * Created by Administrator on 2019/1/21.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class InvoiceRegisterServiceImpl implements InvoiceRegisterService {
}
