<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.accountant.mapper.VoucherMapper" >

  <select id="getSingle" parameterType="voucher" resultType="voucher">
    select 
    id, org, voucher, belong_peroid AS belongPeroid, book_date AS  bookDate, mode, type, category, sn, settlement,
    check_no AS  checkNo, is_settled AS  isSettled, is_account AS  isAccount, reason, bill_detail AS  billDetail, summary, operator, operator_name AS  operatorName,
    operator_finance AS  operatorFinance, memo, creator, create_name AS createName, create_date AS  createDate, updator, update_name AS updateName,
    update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
    audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId, source, pricetype, purpose,
    bill_quantity AS billQuantity, bill_period AS billPeriod,bill_date as billDate, detail_id AS detailId, ftype, kind, addtime
    from t_accountant_voucher
    where id = #{id}
  </select>

  <select id="listPage" parameterType="hashmap" resultType="voucher">
    select
    id, org, voucher, belong_peroid AS belongPeroid, book_date AS  bookDate, mode, type, category, sn, settlement,
    check_no AS  checkNo, is_settled AS  isSettled, is_account AS  isAccount, reason, bill_detail AS  billDetail, summary, operator, operator_name AS  operatorName,
    operator_finance AS  operatorFinance, memo, creator, create_name AS createName, create_date AS  createDate, updator, update_name AS updateName,
    update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
    audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId, source, pricetype, purpose,
    bill_quantity AS billQuantity, bill_period AS billPeriod, bill_date as billDate,detail_id AS detailId, ftype, kind, addtime
    from t_accountant_voucher
  </select>

  <!-- 从原凭证表得到凭证 -->
  <select id="getVouchersFormOriginal" parameterType="voucher" resultType="voucher">
    SELECT id,org,
    DATE_FORMAT(create_date,'%Y年%m月%d日') AS createDate,
    summary,
    bill_detail AS  billDetail,
      detail_id as detailId,
    pricetype,
    purpose,
    bill_quantity AS  billQuantity,
    bill_period AS  billPeriod,
      bill_date as billDate,
    belong_peroid AS belongPeroid,
    create_name AS  createName,
    memo,
    type,
    source,
    kind,reason,
    DATE_FORMAT(addtime,'%Y年%m月%d日') AS addtime,
    operator_name as operatorName,is_settled AS isSettled FROM `t_accountant_voucher`
    WHERE org=#{org} AND approve_status=#{approveStatus} and source = '0' order by id desc
  </select>

  <select id="getVouchersByVoucherField" resultType="voucher">
    SELECT
      id, org, voucher, belong_peroid AS belongPeroid, book_date AS  bookDate, mode, type, category, sn, settlement,
    check_no AS  checkNo, is_settled AS  isSettled, is_account AS  isAccount, reason, bill_detail AS  billDetail, summary, operator, operator_name AS  operatorName,
    operator_finance AS  operatorFinance, memo, creator, create_name AS createName, create_date AS  createDate, updator, update_name AS updateName,
    update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
    audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId, source, pricetype, purpose,
    bill_quantity AS billQuantity, bill_period AS billPeriod,bill_date as billDate, detail_id AS detailId, ftype, kind, addtime
     FROM `t_accountant_voucher` WHERE voucher=#{voucherID}
  </select>

  <select id="getRedVoucher" resultType="voucher">
    SELECT id,DATE_FORMAT(create_date,'%Y年%m月%d日') AS createDate,summary,type,is_settled AS isSettled,voucher,bill_date as billDate FROM `t_accountant_voucher`
    WHERE id=#{redid} OR id=#{newid}
  </select>

<!--  <select id="getVouchersByVoucherFiled" resultType="voucher" parameterType="voucher">
    SELECT
      id, org, voucher, belong_peroid AS belongPeroid, book_date AS  bookDate, mode, type, category, sn, settlement,
    check_no AS  checkNo, is_settled AS  isSettled, is_account AS  isAccount, reason, bill_detail AS  billDetail, summary, operator, operator_name AS  operatorName,
    operator_finance AS  operatorFinance, memo, creator, create_name AS createName, create_date AS  createDate, updator, update_name AS updateName,
    update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
    audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId, source, pricetype, purpose,
    bill_quantity AS billQuantity, bill_period AS billPeriod, detail_id AS detailId, ftype, kind, addtime
     FROM `t_accountant_voucher` WHERE voucher=#{voucherID}
  </select>-->

  <select id="getInPutVouchers" resultType="voucher">
    SELECT id,org,DATE_FORMAT(create_date,'%Y年%m月%d日') AS createDate,summary,type,is_settled AS isSettled,voucher,type FROM `t_accountant_voucher`
    WHERE org=#{org} AND source=#{source} order by addtime desc
  </select>

  <select id="getSettledVouchers" resultType="voucher">
  SELECT id, org, voucher, belong_peroid AS belongPeroid, book_date AS  bookDate, mode, type, category, sn, settlement,
    check_no AS  checkNo, is_settled AS  isSettled, is_account AS  isAccount, reason, bill_detail AS  billDetail, summary, operator, operator_name AS  operatorName,
    operator_finance AS  operatorFinance, memo, creator, create_name AS createName, create_date AS  createDate, updator, update_name AS updateName,
    update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
    audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId, source, pricetype, purpose,
    bill_quantity AS billQuantity, bill_period AS billPeriod, bill_date as billDate ,detail_id AS detailId, ftype, kind, addtime FROM `t_accountant_voucher`
    WHERE is_settled=1 and create_date=#{createDate} AND org=#{org}

  </select>

    <!--<select id="listByUpdatorNull" resultType="voucher">
      SELECT
      id, voucher
      FROM
      t_accountant_voucher
      WHERE
      is_settled = 0
    </select>-->

  <select id="getApproveVouchers" resultType="voucher">

    SELECT id, org, voucher, belong_peroid AS belongPeroid, book_date AS  bookDate, mode, type, category, sn, settlement,
    check_no AS  checkNo, is_settled AS  isSettled, is_account AS  isAccount, reason, bill_detail AS  billDetail, summary, operator, operator_name AS  operatorName,
    operator_finance AS  operatorFinance, memo, creator, create_name AS createName, create_date AS  createDate, updator, update_name AS updateName,
    update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
    audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId, source, pricetype, purpose,
    bill_quantity AS billQuantity, bill_period AS billPeriod,bill_date as billDate, detail_id AS detailId, ftype, kind, addtime FROM `t_accountant_voucher`
    WHERE (approve_status=2 OR approve_status=7 OR source=1 OR source=2 ) and create_date=#{createDate} AND org=#{org} order by addtime desc

  </select>

  <select id="likeVoucher" resultType="voucher">
    select
    id, org, voucher, belong_peroid AS belongPeroid, book_date AS  bookDate, mode, type, category, sn, settlement,
    check_no AS  checkNo, is_settled AS  isSettled, is_account AS  isAccount, reason, bill_detail AS  billDetail, bill_date as billDate,summary, operator, operator_name AS  operatorName,
    operator_finance AS  operatorFinance, memo, creator, create_name AS createName, create_date AS  createDate, updator, update_name AS updateName,
    update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
    audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId, source, pricetype, purpose,
    bill_quantity AS billQuantity, bill_period AS billPeriod, detail_id AS detailId, ftype, kind, addtime
    from t_accountant_voucher
    where create_date like CONCAT('%',#{createDate},'%') and CHAR_LENGTH(create_date) &gt;= 7 and org = #{org}

  </select>

  <select id="getAllVoucher" resultType="voucher">
    SELECT
    id, org, voucher, belong_peroid AS belongPeroid, book_date AS  bookDate, mode, type, category, sn, settlement,
    check_no AS  checkNo, is_settled AS  isSettled, is_account AS  isAccount, reason, bill_detail AS  billDetail, summary, operator, operator_name AS  operatorName,
    operator_finance AS  operatorFinance, memo, creator, create_name AS createName, create_date AS  createDate, updator, update_name AS updateName,
    update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
    audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId, source, pricetype, purpose,
    bill_quantity AS billQuantity, bill_period AS billPeriod, bill_date as billDate,detail_id AS detailId, ftype, kind, addtime
    FROM t_accountant_voucher
    WHERE (approve_status = 2 OR source = 1 OR source = 2 OR approve_status= 7) AND org= #{org}
    ORDER BY addtime DESC
  </select>

  <select id="getCarryOverVoucher" resultType="voucher">
    SELECT
      id, org, voucher, belong_peroid AS belongPeroid, book_date AS  bookDate, mode, type, category, sn, settlement,
      check_no AS  checkNo, is_settled AS  isSettled, is_account AS  isAccount, reason, bill_detail AS  billDetail, summary, operator, operator_name AS  operatorName,
      operator_finance AS  operatorFinance, memo, creator, create_name AS createName, create_date AS  createDate, updator, update_name AS updateName,
      update_date AS updateDate, approve_Item AS approveItem, approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
      audit_date AS auditDate, operation, apply_memo AS applyMemo, approve_memo AS approveMemo, message_id AS messageId, source, pricetype, purpose,
      bill_quantity AS billQuantity, bill_period AS billPeriod,bill_date as billDate, detail_id AS detailId, ftype, kind, addtime
      FROM t_accountant_voucher
      WHERE source = 2  AND org= #{org} AND create_date = #{createDate} order by addtime limit 1
  </select>

  <select id="getOrgVouchers" resultType="voucher">
     SELECT
    id, org, voucher
    FROM t_accountant_voucher
    WHERE org= #{org}
  </select>

  <select id="getVoucherBySource" resultType="voucherDto">
    select id,operator_name as operatorName,operator_finance as operatorFinance,create_name as createName,update_name as updateName,create_date as createDate,bill_date as billDate,detail_id as detailId,summary,addtime,auditor, auditor_name AS auditorName,
    audit_date AS auditDate,kind,bill_detail as billDetail,source from t_accountant_voucher

    <where>
      org= #{org} and (approve_status='1' or approve_status='2')
      <if test="createDate!=null">
        and DATE_FORMAT(create_date, '%Y-%m')=#{createDate,jdbcType=DATE}
      </if>
      <if test="source!='3'.toString() and source!=null">
        and source=#{source}
      </if>
      <if test="source=='3'.toString()">
        and (source = '3' or source = '2')
      </if>
<!--      <if test="source==null">-->
<!--        and source is NULL-->
<!--      </if>-->

    </where>
    order by addtime desc
  </select>

  <select id="getVoucherBySearcherCon" resultType="voucherDto">
    select v.id,
           v.operator_name as operatorName,
           v.operator_finance as operatorFinance,
           v.create_name as createName,
           v.update_name as updateName,
           v.create_date as createDate,
           v.bill_date as billDate,
           v.detail_id as detailId,
           v.summary,
           v.addtime,
           v.auditor,
           v.auditor_name AS auditorName,
           v.audit_date AS auditDate,
           v.source from t_accountant_voucher_subject s ,t_accountant_voucher v

    where v.org=#{org}
            and s.`voucher_id`=v.id
            AND Date_format(s.acount_date, '%Y-%m') = #{acountDate}
            and (v.operator_name like CONCAT('%',#{operatorName},'%') or v.summary like CONCAT('%',#{summary},'%'))
            group by v.id order by v.addtime desc
  </select>

  <select id="getDtoById" resultType="voucherDto">
    select id,operator_name as operatorName,operator_finance as operatorFinance,create_name as createName,update_name as updateName,create_date as createDate,bill_date as billDate,detail_id as detailId,summary,addtime,auditor, auditor_name AS auditorName,
           audit_date AS auditDate from t_accountant_voucher where id=#{voucherId}
  </select>

  <delete id="delete" parameterType="voucher" >
    delete from t_accountant_voucher
    where id = #{id}
  </delete>

  <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="voucher">
    insert into t_accountant_voucher ( org, voucher,
      belong_peroid, book_date, mode, 
      type, category, sn, settlement, 
      check_no, is_settled, is_account, 
      reason, bill_detail, summary, 
      operator, operator_name, operator_finance, 
      memo, creator, create_name, 
      create_date, updator, update_name, 
      update_date, approve_Item, approve_status, 
      approve_level, auditor, auditor_name, 
      audit_date, operation, apply_memo, 
      approve_memo, message_id, source, 
      pricetype, purpose, bill_quantity, 
      bill_period, bill_date,detail_id, ftype,
      kind, addtime)
    values ( #{org}, #{voucher},
      #{belongPeroid}, #{bookDate}, #{mode},
      #{type}, #{category}, #{sn}, #{settlement},
      #{checkNo}, #{isSettled}, #{isAccount},
      #{reason}, #{billDetail}, #{summary},
      #{operator}, #{operatorName}, #{operatorFinance},
      #{memo}, #{creator}, #{createName},
      #{createDate}, #{updator}, #{updateName},
      #{updateDate}, #{approveItem}, #{approveStatus},
      #{approveLevel}, #{auditor}, #{auditorName},
      #{auditDate}, #{operation}, #{applyMemo},
      #{approveMemo}, #{messageId}, #{source},
      #{pricetype}, #{purpose}, #{billQuantity},
      #{billPeriod}, #{billDate},#{detailId}, #{ftype},
      #{kind}, #{addtime})
  </insert>

  <update id="updateOriginalSubjectAndDetail">
    UPDATE `t_accountant_voucher_subject` sub,t_accountant_subject_detail detail SET
    sub.subject=#{subSubject},sub.amount=#{amount},detail.subject=#{detailSubject},detail.credit=#{credit},
    detail.debit=#{debit}  WHERE sub.voucher_id=#{voucherId} AND sub.direction=#{direction} AND sub.id=detail.subject_id
  </update>

  <update id="updateVoucherAfterRed" parameterType="hashmap">
    UPDATE `t_accountant_voucher` SET type='2' WHERE id=#{oldid} OR id=#{redid}
  </update>

  <update id="updateSettle">
    UPDATE `t_accountant_voucher` SET is_settled=#{isSettled}  where create_date=#{createDate} AND org=#{org}
  </update>

  <update id="updateVoucherByDate">
    UPDATE `t_accountant_voucher` SET is_settled=0  where is_settled=1 and create_date=#{createDate} AND org=#{org}
  </update>

  <update id="update" parameterType="voucher" >
    update t_accountant_voucher
    <set >
      <if test="org != null" >
        org = #{org},
      </if>
      <if test="voucher != null" >
        voucher = #{voucher},
      </if>
      <if test="belongPeroid != null" >
        belong_peroid = #{belongPeroid},
      </if>
      <if test="bookDate != null" >
        book_date = #{bookDate},
      </if>
      <if test="mode != null" >
        mode = #{mode},
      </if>
      <if test="type != null" >
        type = #{type},
      </if>
      <if test="category != null" >
        category = #{category},
      </if>
      <if test="sn != null" >
        sn = #{sn},
      </if>
      <if test="settlement != null" >
        settlement = #{settlement},
      </if>
      <if test="checkNo != null" >
        check_no = #{checkNo},
      </if>
      <if test="isSettled != null" >
        is_settled = #{isSettled},
      </if>
      <if test="isAccount != null" >
        is_account = #{isAccount},
      </if>
      <if test="reason != null" >
        reason = #{reason},
      </if>
      <if test="billDetail != null" >
        bill_detail = #{billDetail},
      </if>
      <if test="summary != null" >
        summary = #{summary},
      </if>
      <if test="operator != null" >
        operator = #{operator},
      </if>
      <if test="operatorName != null" >
        operator_name = #{operatorName},
      </if>
      <if test="operatorFinance != null" >
        operator_finance = #{operatorFinance},
      </if>
      <if test="memo != null" >
        memo = #{memo},
      </if>
      <if test="creator != null" >
        creator = #{creator},
      </if>
      <if test="createName != null" >
        create_name = #{createName},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate},
      </if>
      <if test="updator != null" >
        updator = #{updator},
      </if>
      <if test="updateName != null" >
        update_name = #{updateName},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate},
      </if>
      <if test="approveItem != null" >
        approve_Item = #{approveItem},
      </if>
      <if test="approveStatus != null" >
        approve_status = #{approveStatus},
      </if>
      <if test="approveLevel != null" >
        approve_level = #{approveLevel},
      </if>
      <if test="auditor != null" >
        auditor = #{auditor},
      </if>
      <if test="auditorName != null" >
        auditor_name = #{auditorName},
      </if>
      <if test="auditDate != null" >
        audit_date = #{auditDate},
      </if>
      <if test="operation != null" >
        operation = #{operation},
      </if>
      <if test="applyMemo != null" >
        apply_memo = #{applyMemo},
      </if>
      <if test="approveMemo != null" >
        approve_memo = #{approveMemo},
      </if>
      <if test="messageId != null" >
        message_id = #{messageId},
      </if>
      <if test="source != null" >
        source = #{source},
      </if>
      <if test="pricetype != null" >
        pricetype = #{pricetype},
      </if>
      <if test="purpose != null" >
        purpose = #{purpose},
      </if>
      <if test="billQuantity != null" >
        bill_quantity = #{billQuantity},
      </if>
      <if test="billPeriod != null" >
        bill_period = #{billPeriod},
      </if>
      <if test="detailId != null" >
        detail_id = #{detailId},
      </if>
      <if test="ftype != null" >
        ftype = #{ftype},
      </if>
      <if test="kind != null" >
        kind = #{kind},
      </if>
      <if test="addtime != null" >
        addtime = #{addtime},
      </if>
    </set>
    where id = #{id}
  </update>

  <update id="updateVoucherByIsSettled">
    UPDATE t_accountant_voucher
    SET bill_period = 2, belong_peroid = 2
  </update>


</mapper>