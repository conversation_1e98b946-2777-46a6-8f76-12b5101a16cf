package cn.sphd.miners.modules.resourceAuthority.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.resourceAuthority.entity.ResAtt;
import cn.sphd.miners.modules.resourceAuthority.entity.ResAttHis;
import cn.sphd.miners.modules.resourceAuthority.entity.ResCorrelation;
import cn.sphd.miners.modules.resourceAuthority.entity.ResCorrelationHis;
import cn.sphd.miners.modules.resourceAuthority.service.ResAttService;
import cn.sphd.miners.modules.resourceAuthority.service.ResCorrelationService;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.List;

/**
 * Created by 朱思旭 on 2022/2/7.
 */
@Controller
@RequestMapping("/res")
public class ResFormController {

    @Autowired
    ResAttService resAttService;
    @Autowired
    ResCorrelationService resCorrelationService;


    //新增表格
    @ResponseBody
    @RequestMapping("/addResRorm.do")
    public JsonResult addResRorm(User user, String sn, String name, String type, String path, String size, String module, Integer resource){
        HashMap<String, Object> map = resAttService.insertForm(user,sn,name,type,path,size,module,resource);
        return new JsonResult(1, map);
    }

    //获取三种状态的表格
    @ResponseBody
    @RequestMapping("/getResRormByType.do")
    public JsonResult getResRormByType(User user, String type, PageInfo pageInfo, String name){
        HashMap<String, Object> map = resAttService.getResAttByType(user,type,pageInfo,name);
        return new JsonResult(1, map);
    }

    //删除表格
    @ResponseBody
    @RequestMapping("/deleteResRorm.do")
    public JsonResult deleteResRorm(User user, Integer id){
        Integer state = resAttService.delResAtt(user,id);
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        return new JsonResult(1, map);
    }

    //修改表格
    @ResponseBody
    @RequestMapping("/updateResRorm.do")
    public JsonResult updateResRorm(User user, Integer id, String sn, String name){
        HashMap<String, Object> map = resAttService.updateResAtt(user,id,sn,name);
        return new JsonResult(1, map);
    }

    //换版表格
    @ResponseBody
    @RequestMapping("/changeResRormVersion.do")
    public JsonResult changeResRormVersion(User user, Integer id, String type, String path, String size,String module){
        ResAtt resAtt = resAttService.changeResAttVersion(user,id,type,path,size,module);
        HashMap<String, Object> map = new HashMap<>();
        map.put("resAtt", resAtt);
        return new JsonResult(1, map);
    }

    //获取表格的操作记录
    @ResponseBody
    @RequestMapping("/getResRormHis.do")
    public JsonResult getResRormHis(Integer id, PageInfo pageInfo){
        HashMap<String, Object> map = resAttService.getResAttHisRecord(id,pageInfo);
        return new JsonResult(1, map);
    }

    //获取某一条表格的操作记录详情
    @ResponseBody
    @RequestMapping("/getResRormHisMes.do")
    public JsonResult getResRormHisMes(Integer id){
        ResAttHis resAttHis = resAttService.getResAttHisSingle(id);
        HashMap<String, Object> map = new HashMap<>();
        map.put("resAttHis", resAttHis);
        return new JsonResult(1, map);
    }

    //新增三种表格状态的关联
    @ResponseBody
    @RequestMapping("/addResFormCorrelation.do")
    public JsonResult addResFormCorrelation(User user, Integer id, Integer resource){
        HashMap<String, Object> map = resCorrelationService.threeformAddCorrelation(user,id,resource);
        return new JsonResult(1, map);
    }

    //解除文件与表格的关联
    @ResponseBody
    @RequestMapping("/removeCorrelationByResForm.do")
    public JsonResult removeCorrelationByResForm(User user, Integer id, Integer resource){
        HashMap<String, Object> map = resCorrelationService.removeCorrelation(user,id,resource);
        return new JsonResult(1, map);
    }

    //点击解除关联按钮返回当前表格状态
    @ResponseBody
    @RequestMapping("/getRemoveCorrelationStateAndFile.do")
    public JsonResult getRemoveCorrelationStateAndFile(Integer id, Integer resource){
        HashMap<String, Object> map = resCorrelationService.getRemoveCorrelationState(id,resource);
        return new JsonResult(1, map);
    }


    /**
     * 通过表格获取关联文件和通过文件获取关联表格
     * @param attId
     * @param resource
     * @param type 1是获取表格正在关联的文件 2是获取表格曾经关联的文件 3是获取文件关联的表格
     * @param pageInfo
     * @return
     */
    @ResponseBody
    @RequestMapping("/getResFormAssociatedFile.do")
    public JsonResult getResFormAssociatedFile(Integer attId, Integer resource, String type, PageInfo pageInfo){
        List<ResCorrelation> list = resCorrelationService.getCorrealtionFile(attId, resource, type, pageInfo);
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", list);
        return new JsonResult(1, map);
    }

    //通过表格获取关联文件的操作记录和通过文件获取关联表格的操作记录
    @ResponseBody
    @RequestMapping("/getResFormCorrelationRecord.do")
    public JsonResult getResFormCorrelationRecord(Integer attId, Integer resource, PageInfo pageInfo){
        List<ResCorrelationHis> list = resCorrelationService.getCorrelationHistory(attId, resource, pageInfo);
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", list);
        return new JsonResult(1, map);
    }

    //获取文件正在关联的表格
    @ResponseBody
    @RequestMapping("/getFileAssociatedResForm.do")
    public JsonResult getFileAssociatedResForm(Integer fileId, PageInfo pageInfo){
        List<ResCorrelation> list = resCorrelationService.getCorrelationResAtt(fileId,pageInfo);
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", list);
        return new JsonResult(1, map);
    }

    //文件废止时获取文件正在关联的表格并分为2部分返回
    @ResponseBody
    @RequestMapping("/getCorrelationForAbolishFile.do")
    public JsonResult getCorrelationForAbolishFile(Integer fileId){
        HashMap<String, Object> map = resCorrelationService.getCorrelationForAbolishFile(fileId);
        return new JsonResult(1, map);
    }

    @RequestMapping("/resFormManage.do")
    public String formManage(User user, Model model ){
        model.addAttribute("user",user);
        return "/resourceCentre/sheet";
    }

}
