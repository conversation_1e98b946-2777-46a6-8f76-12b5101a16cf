package cn.sphd.miners.modules.accountant.controller;

import cn.sphd.miners.common.utils.WriteJSONUtil;
import cn.sphd.miners.modules.accountant.entity.CashFlow;
import cn.sphd.miners.modules.accountant.entity.TAccountantCash;
import cn.sphd.miners.modules.accountant.service.AccountantCashFlowService;
import cn.sphd.miners.modules.accountant.service.AccountantCashService;
import cn.sphd.miners.modules.accountant.service.AccountantSubjectCashService;
import cn.sphd.miners.modules.system.entity.User;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;

/**
 * Created by root on 17-5-22.
 * 现金流量表
 */
@Controller
@RequestMapping("/accountant")
public class AccountantSubjectCashController {

    private static final String SUCCESS = "1";
    private static final String ERROR = "0";

    @Autowired
    AccountantSubjectCashService subjectCashService;
    @Autowired
    AccountantCashService cashService;
    @Autowired
    AccountantCashFlowService cashFlowService;

    /*得到现金流量表的所有数据*/
    @ResponseBody
    @RequestMapping("/getCashFlowData.do")
    public void getCashFlowData(User user, String period, HttpServletResponse response)
    {
//        User user = (User) session.getAttribute("user");
//        int oid = user.getOid();
//        Integer oid = (Integer) session.getAttribute("oid");
        Integer oid = user.getOid();
        //为peroid设置默认值
        if(period == null)
        {
            Calendar calendar = Calendar.getInstance();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
            period = sdf.format(calendar.getTime());
        }

        CashFlow cashFlow = new CashFlow();
        cashFlow.setOrg(oid);
        cashFlow.setPeriod(period);

        List<CashFlow> listCash = cashFlowService.getCashDataByPeriod(cashFlow);
        JSONObject result = new JSONObject();
        result.put("code", 1);
        result.put("msg", "");
        result.put("data", listCash);
        WriteJSONUtil.writeJSON(result.toString(), response);
//        return new Result(SUCCESS,"",listCash);
    }

    /*返回所有项目供用户选择，当需要写到现金流量表的时候*/
    @ResponseBody
    @RequestMapping("/getCashItems.do")
    public void getCashItems(HttpServletResponse response)
    {
        List<TAccountantCash> items = cashService.getCashItems();
        JSONObject result = new JSONObject();
        result.put("code", 1);
        result.put("msg", "");
        result.put("data", items);
        WriteJSONUtil.writeJSON(result.toString(), response);
//        return new Result(SUCCESS,"",items);
    }

}
