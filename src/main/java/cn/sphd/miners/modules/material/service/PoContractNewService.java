package cn.sphd.miners.modules.material.service;

import cn.sphd.miners.modules.material.dto.MaterialContractDto;
import cn.sphd.miners.modules.material.dto.SupplierContractDto;
import cn.sphd.miners.modules.material.entity.PoContractBase;
import cn.sphd.miners.modules.material.entity.PoContractBaseHistory;
import cn.sphd.miners.modules.system.entity.LocalSetting;
import cn.sphd.miners.modules.system.entity.User;

import java.util.HashMap;
import java.util.List;

public interface PoContractNewService {

    //批量新增合同
    void insertBatchPoContract(String contractBaseList,Integer supplierId, User user);

    //新增采购合同
    PoContractBase insertSupplierCon(User user, PoContractBase poContractBase, String contractSignTime,
                                     String contractStartTime, String contractEndTime, String contractBaseImages,
                                     String mtList);

    //修改合同
    Integer upSupplierCon(User user, PoContractBase poContractBase, String contractSignTime,
                           String contractStartTime, String contractEndTime, String contractBaseImages,
                           String mtList);

    //续约合同
    Integer renewalPoContract(User user, PoContractBase poContractBase, String contractSignTime,
                              String contractStartTime, String contractEndTime, String contractBaseImages,
                              String mtList, String type);

    //暂停合同
    Integer terminatePoContract(Integer id);

    //启用合同时判断合同的状态
    HashMap<String, Object> checkReStartPoContractState(Integer id);

    //确定启用合同
    Integer reStartPoContract(Integer id, String type);

    //获取全部材料
    List<MaterialContractDto> getMaterialContract(User user, String type, Integer supplier, List<Integer> idsList);

    //获取合同列表
    List<PoContractBase> listPoContract(Integer supplierId, String type, String state, List<Integer> listConIds);

    //获取全部合同
    List<PoContractBase> listAllPoContract(User user, String type);

    //根据operation获取历史记录
    List<PoContractBaseHistory> getListPoContractHisByoperation(Integer id, String operation);

    //获取全部的签约记录
    List<PoContractBase> getPoContractSignRecord(Integer primaryId);

    //查看合同详情
    HashMap<String,Object> getPoContractMes(User user, Integer id);

    //获取历史合同获取详情
    HashMap<String,Object> getPoContractHisMes(User user, Integer contractHisId);

    //获取新增合同提示
    LocalSetting getContractRemindByType(User user, String type);

    //修改新增合同提示
    void upContractRemind(User user, String type);

    //点击采购合同
    List<SupplierContractDto> procurementContractList(User user);

    //获取某人的全部供应商只返回id和全称
    List<SupplierContractDto> getAllSupplierForContract(User user);

}
