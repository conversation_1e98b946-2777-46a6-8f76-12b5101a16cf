//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.icbc.api;

import com.icbc.api.internal.apache.http.s;
import com.icbc.api.internal.util.StringUtils;
import com.icbc.api.internal.util.internal.util.fastjson.JSON;
import com.icbc.api.internal.util.internal.util.fastjson.JSONObject;
import com.icbc.api.utils.HttpClientUtils;
import com.icbc.api.utils.IcbcEncrypt;
import com.icbc.api.utils.IcbcHashMap;
import com.icbc.api.utils.IcbcSignature;
import com.icbc.api.utils.WebUtils;
import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;


public class DefaultIcbcClient implements IcbcClient {
    private static final Log logger = LogFactory.getLog(DefaultIcbcClient.class);
    protected String appId;
    protected String privateKey;
    protected String signType;
    protected String charset;
    protected String format;
    protected String icbcPulicKey;
    protected String encryptKey;
    protected String encryptType;
    protected String ca;
    protected String icbc_ca;
    protected boolean icbcCaConsistentChkFlag;
    protected String password;
    protected String emSignIp;
    protected String emSignPort;
    protected String emProduct;
    private String zoneNo;
    private String refineInfo;
    private Map<String, String> headerParams;

    public DefaultIcbcClient(String var1, String var2, String var3, String var4, String var5, String var6, String var7, String var8, String var9, String var10) {
        this.signType = "RSA";
        this.charset = "UTF-8";
        this.format = "json";
        this.icbcCaConsistentChkFlag = false;
        this.zoneNo = null;
        this.refineInfo = null;
        this.headerParams = new HashMap();
        this.appId = var1;
        this.signType = var2;
        this.privateKey = var3;
        this.charset = var4;
        this.format = var5;
        this.icbcPulicKey = var6;
        this.encryptType = var7;
        this.encryptKey = var8;
        this.password = var10;
        this.emSignIp = null;
        this.emSignPort = null;
        this.emProduct = null;
        if (var9 != null && !var9.equals("")) {
            Pattern var11 = Pattern.compile("\\s*|\t");
            Matcher var12 = var11.matcher(var9);
            var9 = var12.replaceAll("");
        }

        this.ca = var9;
    }

    public DefaultIcbcClient(String var1, String var2, String var3, String var4, String var5, String var6, String var7, String var8, String var9, String var10, String var11, String var12, String var13) {
        this.signType = "RSA";
        this.charset = "UTF-8";
        this.format = "json";
        this.icbcCaConsistentChkFlag = false;
        this.zoneNo = null;
        this.refineInfo = null;
        this.headerParams = new HashMap();
        this.appId = var1;
        this.signType = var2;
        this.privateKey = var3;
        this.charset = var4;
        this.format = var5;
        this.icbcPulicKey = var6;
        this.encryptType = var7;
        this.encryptKey = var8;
        this.password = var10;
        this.emSignIp = var11;
        this.emSignPort = var12;
        this.emProduct = var13;
        if (var9 != null && !var9.equals("")) {
            Pattern var14 = Pattern.compile("\\s*|\t");
            Matcher var15 = var14.matcher(var9);
            var9 = var15.replaceAll("");
        }

        this.ca = var9;
    }

    public DefaultIcbcClient(String var1, String var2, String var3) {
        this(var1, "RSA", var2, "UTF-8", "json", var3, (String)null, (String)null, (String)null, (String)null, (String)null, (String)null, (String)null);
    }

    public DefaultIcbcClient(String var1, String var2, String var3, String var4) {
        this(var1, var2, var3, "UTF-8", "json", var4, (String)null, (String)null, (String)null, (String)null, (String)null, (String)null, (String)null);
    }

    public DefaultIcbcClient(String var1, String var2, String var3, String var4, String var5) {
        this(var1, "CA", var2, "UTF-8", "json", var3, (String)null, (String)null, var4, var5, (String)null, (String)null, (String)null);
    }

    public DefaultIcbcClient(String var1, String var2, String var3, String var4, String var5, String var6) {
        this(var1, "EM", (String)null, "UTF-8", "json", var5, (String)null, (String)null, (String)null, (String)null, var3, var4, var6);
    }

    public <T extends IcbcResponse> T execute(IcbcRequest<T> var1) throws IcbcApiException {
        UUID var2 = UUID.randomUUID();
        String var3 = var2.toString().replace("-", "");
        return this.execute(var1, var3);
    }

    public <T extends IcbcResponse> T execute(IcbcRequest<T> var1, String var2) throws IcbcApiException {
        return this.execute(var1, var2, "");
    }

    public <T extends IcbcResponse> T execute(IcbcRequest<T> var1, String var2, String var3) throws IcbcApiException {
        IcbcHashMap var4 = this.prepareParams(var1, var2, var3);
        String var5 = null;
        if (var1.getMethod().equals("GET")) {
            if (this.zoneNo != null) {
                this.headerParams.put("Zone-No", this.zoneNo);
            }

            if (this.refineInfo != null) {
                this.headerParams.put("Apirefined-Info", this.refineInfo);
            }

            var5 = WebUtils.doGet(var1.getServiceUrl(), var4, this.charset, this.headerParams);
        } else {
            if (!var1.getMethod().equals("POST")) {
                logger.error("only support GET or POST, method: " + var1.getMethod());
                throw new IcbcApiException("only support GET or POST, method: " + var1.getMethod());
            }

            if (this.zoneNo != null) {
                this.headerParams.put("Zone-No", this.zoneNo);
            }

            if (this.refineInfo != null) {
                this.headerParams.put("Apirefined-Info", this.refineInfo);
            }

            var5 = WebUtils.doPost(var1.getServiceUrl(), var4, this.charset, this.headerParams);
        }

        IcbcResponse var6 = this.parse(var1, var5);
        if (var6 == null) {
            logger.error("response is null.");
            throw new IcbcApiException("response is null.");
        } else {
            return (T) var6;
        }
    }

    public <T extends IcbcResponse> void execute(IcbcRequest<T> var1, WebUtils.IcbcApiFutureCallback<T> var2) throws IcbcApiException {
        UUID var3 = UUID.randomUUID();
        String var4 = var3.toString().replace("-", "");
        this.execute(var1, var4, var2);
    }

    public <T extends IcbcResponse> void execute(IcbcRequest<T> var1, String var2, WebUtils.IcbcApiFutureCallback<T> var3) throws IcbcApiException {
        this.execute(var1, var2, (String)null, var3);
    }

    public <T extends IcbcResponse> void execute(final IcbcRequest<T> var1, String var2, String var3, final WebUtils.IcbcApiFutureCallback<T> var4) throws IcbcApiException {
        IcbcHashMap var5 = this.prepareParams(var1, var2, var3);
        WebUtils.IcbcApiFutureCallback var6 = new WebUtils.IcbcApiFutureCallback<String>() {
            @Override
            public void completed(String var1x) {
                try {
                    var4.completed(DefaultIcbcClient.this.parse(var1, var1x));
                } catch (IcbcApiException var3) {
                    var4.failed(var3);
                }
            }

//            public void a(String var1x) {
//                try {
//                    var4.completed(DefaultIcbcClient.this.parse(var1, var1x));
//                } catch (IcbcApiException var3) {
//                    var4.failed(var3);
//                }
//
//            }

            @Override
            public void failed(IcbcApiException var1x) {
                var4.failed(var1x);
            }
        };
        if (var1.getMethod().equals("GET")) {
            WebUtils.doGet(var1.getServiceUrl(), var5, this.charset, var6);
        } else {
            if (!var1.getMethod().equals("POST")) {
                logger.error("only support GET or POST, method: " + var1.getMethod());
                throw new IcbcApiException("only support GET or POST, method: " + var1.getMethod());
            }

            WebUtils.doPost(var1.getServiceUrl(), var5, this.charset, var6);
        }

    }

    protected <T extends IcbcResponse> T parse(IcbcRequest<T> var1, String var2) throws IcbcApiException {
        return this.parseJsonWithIcbcSign(var1, var2);
    }

    private <T extends IcbcResponse> T parseJsonWithIcbcSign(IcbcRequest<T> var1, String var2) throws IcbcApiException {
        String var3 = null;
        String var4 = null;
        JSONObject var5 = null;
        String var6 = null;

        try {
            var5 = JSON.parseObject(var2);
            var6 = var5.getString("icbc_ca");
            int var7 = var2.indexOf("response_biz_content") + "response_biz_content".length() + 2;
            int var8 = var2.lastIndexOf(",\"sign\":\"");
            var3 = var2.substring(var7, var8);
            var4 = var5.getString("sign");
        } catch (Exception var10) {
            logger.error("response is not format json. respStr :\n" + var2, var10);
            throw new IcbcApiException("response is not format json. respStr :\n" + var2, var10);
        }

        boolean var11 = false;
        if ("SM2".equals(this.signType)) {
            var11 = IcbcSignature.verify(var3, this.signType, this.icbcPulicKey, this.charset, var4);
        } else if (!"CA-SM-TP".equals(this.signType) && !"CA-SM-ICBC".equals(this.signType)) {
            var11 = IcbcSignature.verify(var3, "RSA", this.icbcPulicKey, this.charset, var4);
        } else {
            var6 = var5.getString("icbc_ca");
            if (var6 == null || "".equals(var6)) {
                logger.error("icbc ca info not set.");
            }

            if (var6 != null && !var6.equals(this.icbc_ca)) {
                logger.error("icbc ca info is not consistent with the gateway.");
                if (this.icbcCaConsistentChkFlag) {
                    throw new IcbcApiException("icbc ca info is not consistent with the gateway. local:" + this.icbc_ca + ",gateway:" + var6);
                }
            }

            var11 = IcbcSignature.verify(var3, this.signType, this.icbcPulicKey, this.charset, var4, this.icbc_ca);
        }

        if (!var11) {
            logger.error("icbc sign verify not passed.");
            throw new IcbcApiException("icbc sign verify not passed.");
        } else {
            if (var1.isNeedEncrypt()) {
                var3 = IcbcEncrypt.decryptContent(var3.substring(1, var3.length() - 1), this.encryptType, this.encryptKey, this.charset);
            }

            try {
                return (T) JSON.parseObject(var3, var1.getResponseClass());
            } catch (Exception var9) {
                logger.error("response can not transform to defined class. response: " + var2 + " defined class name: " + var1.getResponseClass().getName(), var9);
                throw new IcbcApiException("response can not transform to defined class. response: " + var2 + " defined class name: " + var1.getResponseClass().getName(), var9);
            }
        }
    }

    protected IcbcHashMap prepareParams(IcbcRequest<?> var1, String var2, String var3) throws IcbcApiException {
        String var4 = this.buildBizContentStr(var1);

        String var5;
        try {
            var5 = (new URL(var1.getServiceUrl())).getPath();
        } catch (MalformedURLException var13) {
            logger.error("url is not valid. url: " + var1.getServiceUrl(), var13);
            throw new IcbcApiException("url is not valid. url: " + var1.getServiceUrl(), var13);
        }

        IcbcHashMap var6 = new IcbcHashMap();
        Map var7 = var1.getExtraParameters();
        if (var7 != null) {
            var6.putAll(var7);
        }

        var6.put("app_id", this.appId);
        var6.put("sign_type", this.signType);
        var6.put("charset", this.charset);
        var6.put("format", this.format);
        var6.put("ca", this.ca);
        var6.put("app_auth_token", var3);
        var6.put("msg_id", var2);
        Long var8 = System.currentTimeMillis();
        SimpleDateFormat var9 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        var9.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        var6.put("timestamp", var9.format(new Date(var8)));
        if (var1.isNeedEncrypt()) {
            if (!StringUtils.areNotEmpty(new String[]{this.encryptType, this.encryptKey})) {
                logger.error("request need be encrypted, encrypt type and encrypt key can not be null.");
                throw new IcbcApiException("request need be encrypted, encrypt type and encrypt key can not be null.");
            }

            if (var4 != null) {
                var6.put("encrypt_type", this.encryptType);
                var6.put("biz_content", IcbcEncrypt.encryptContent(var4, this.encryptType, this.encryptKey, this.charset));
            }
        } else {
            var6.put("biz_content", var4);
        }

        String var10 = WebUtils.buildOrderedSignStr(var5, var6);
        String var11;
        if (!this.signType.equals("CA") && !this.signType.equals("RSA") && !this.signType.equals("RSA2") && !this.signType.equals("SM2") && !this.signType.equals("CA-SM-TP") && !this.signType.equals("CA-SM-ICBC")) {
            if (this.signType.equals("EM")) {
                try {
                    var11 = this.getNCSSign(var10, this.emSignIp, this.emSignPort, this.emProduct);
                    var6.put("sign", var11);
                } catch (IOException var12) {
                    logger.error("NC签名过程发生错误.");
                    throw new IcbcApiException("sign failed. emProduct: " + this.emProduct, var12);
                }
            } else {
                logger.error("NsignType is not supported.");
            }
        } else {
            var11 = IcbcSignature.sign(var10, this.signType, this.privateKey, this.charset, this.password);
            var6.put("sign", var11);
        }

        return var6;
    }

    protected String getNCSSign(String var1, String var2, String var3, String var4) throws IOException {
        String var5 = null;
        String var6 = "UTF-8";
        InputStreamReader var8 = null;
        BufferedOutputStream var9 = null;
        BufferedReader var10 = null;

        try {
            URL var7 = new URL("http://" + var2 + ":" + var3);
            HttpURLConnection var11 = (HttpURLConnection)var7.openConnection();
            var11.setRequestMethod("POST");
            var11.setDoInput(true);
            var11.setDoOutput(true);
            var11.setUseCaches(false);
            if (var4.equals("CFCA")) {
                var11.setRequestProperty("Request-Type", "1");
            }

            var11.setRequestProperty("Content-Length", String.valueOf(var1.getBytes(var6).length));
            var11.setRequestProperty("Content-Type", "INFOSEC_SIGN/1.0");
            var9 = new BufferedOutputStream(var11.getOutputStream());
            var9.write(var1.getBytes(var6));
            var9.flush();
            int var12 = var11.getResponseCode();
            if (var12 != 200) {
                logger.error("NC发送失败.");
            }

            String var13 = var11.getResponseMessage();
            StringBuffer var14 = new StringBuffer("");
            var8 = new InputStreamReader(var11.getInputStream());
            var10 = new BufferedReader(var8);
            String var15 = null;

            while((var15 = var10.readLine()) != null) {
                var14.append(var15);
            }

            var11.disconnect();
            int var16 = 0;
            int var17 = 0;

            try {
                var16 = var14.indexOf("<sign>") + 6;
                var17 = var14.indexOf("</sign>");
            } catch (Exception var23) {
                logger.error("NC返回失败.");
            }

            String var18 = var14.substring(var16, var17);
            var5 = var18;
        } catch (MalformedURLException var24) {
            logger.error("NC通信和签名过程发生错误,或者emport类型不是CFCA或者NC.");
            var24.printStackTrace();
        } finally {
            var9.close();
            var8.close();
            var10.close();
        }

        return var5;
    }

    protected String buildBizContentStr(IcbcRequest<?> var1) throws IcbcApiException {
        if (var1.getBizContent() == null) {
            return null;
        } else if (this.format.equals("json")) {
            return JSON.toJSONString(var1.getBizContent());
        } else {
            logger.error("only support json format, current format is not supported. format: " + this.format);
            throw new IcbcApiException("only support json format, current format is not supported. format: " + this.format);
        }
    }

    /** @deprecated */
    @Deprecated
    public String getZoneNo() {
        return this.zoneNo;
    }

    /** @deprecated */
    @Deprecated
    public void setZoneNo(String var1) {
        this.zoneNo = var1;
    }

    public void setProxy(String var1, int var2) {
        HttpClientUtils.setProxy(var1, var2);
    }

    public void setHttpsProxy(String var1, int var2) {
        HttpClientUtils.setHttpsProxy(var1, var2);
    }

    public void setProxy(s var1) {
        HttpClientUtils.setProxy(var1);
    }

    public String getRefineInfo() {
        return this.refineInfo;
    }

    public void setRefineInfo(String var1) {
        this.refineInfo = var1;
    }

    public void setHeaderParam(String var1, String var2) {
        this.headerParams.put(var1, var2);
    }

    public void setFovaRegion(String var1) {
        this.headerParams.put("X-Fova-Region", var1);
    }

    public String getIcbc_ca() {
        return this.icbc_ca;
    }

    public void setIcbc_ca(String var1) {
        this.icbc_ca = var1;
    }

    public boolean isIcbcCaConsistentChkFlag() {
        return this.icbcCaConsistentChkFlag;
    }

    public void setIcbcCaConsistentChkFlag(boolean var1) {
        this.icbcCaConsistentChkFlag = var1;
    }
}