package cn.sphd.miners.modules.material.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.accountant.util.DateUtil;
import cn.sphd.miners.modules.commodity.service.ProductService;
import cn.sphd.miners.modules.material.entity.*;
import cn.sphd.miners.modules.material.service.MaterielService;
import cn.sphd.miners.modules.material.service.MtPackagingService;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.*;

@Controller
@RequestMapping("/mtPacking")
public class MtPackagingController {

    @Autowired
    private MtPackagingService mtPackagingService;

    @Autowired
    private ProductService productService;
    @Autowired
    private MaterielService materielService;



    /**
     * 获取供应商列表
     */
    @RequestMapping("/getSupplierByMtBase.do")
    @ResponseBody
    public JsonResult getSupplierByMtBase(User user, Integer mtBaseId) {
        List<Map<String, Object>> list = mtPackagingService.getSupplierByMtBase(mtBaseId,user.getOid());

        for (Map<String, Object> m : list
        ) {
            Long num = (Long) m.get("num");
            if (num == 0) {
                m.put("gs", "无需包装");
            } else {
                m.put("gs", num + "种包装方式");
            }
            //判断是否设置过
            Long hnum = (Long) m.get("hnum");
            if (hnum == 0) {
                m.put("flag",0);
            } else {
                m.put("flag",1);
            }
        }
        return new JsonResult(1, list);
    }

    /**
     * 获取已设置列表
     */
    @RequestMapping("/getPackagingList.do")
    @ResponseBody
    public JsonResult getPackagingList(User user, String param, PageInfo pageInfo) {
        List<Map<String, Object>> list = mtPackagingService.getPackagingList(user.getOid(), param, 1, pageInfo);
        for (Map<String, Object> m : list
        ) {
            Long num = (Long) m.get("num");
            if (num == 0) {
                m.put("gs", "无需包装");
            } else {
                m.put("gs", num + "种包装方式");
            }
        }
        return new JsonResult(1, list, pageInfo);
    }

    /**
     * 获取未设置数量
     */
    @RequestMapping("/getWszNum.do")
    @ResponseBody
    public JsonResult getWszNum(User user) {

        Object num = mtPackagingService.getWszNum(user.getOid());
        return new JsonResult(1, num);
    }


    /**
     * 未设置列表
     */
    @RequestMapping("/getWszPackagingList.do")
    @ResponseBody
    public JsonResult getWszPackagingList(User user, String param, PageInfo pageInfo) {

        List<Map<String, Object>> list = mtPackagingService.getPackagingList(user.getOid(), param, 0, pageInfo);
        return new JsonResult(1, list, pageInfo);
    }

    /**
     * 新增包装物,
     */
    @RequestMapping("/addBzw.do")
    @ResponseBody
    public JsonResult addBzw(User user, MtWrappage wrappage) {
        wrappage.setCreator(user.getUserID());
        wrappage.setCreateName(user.getUserName());
        wrappage.setCreateDate(DateUtil.fomatDate(DateUtil.getDay()));
        wrappage.setEnabled(1);
        wrappage.setEnabledTime(DateUtil.fomatDate(DateUtil.getDay()));
        wrappage.setOperation(1);
        wrappage.setOrg(user.getOid());
        String res = mtPackagingService.addBzw(wrappage);

        if (!res.equals("success")) {
            return new JsonResult(0, "", "400",res);
        }
        return new JsonResult(1, wrappage);
    }

    /**
     * 获取包装物列表
     */
    @RequestMapping("/getBzwList.do")
    @ResponseBody
    public JsonResult getBzwList(User user) {
        List<MtWrappage> list = mtPackagingService.getWrappageList(user.getOid());
        return new JsonResult(1, list);
    }


    /**
     * 新增包装
     */
    @RequestMapping("/addPackaging.do")
    @ResponseBody
    public JsonResult addPackaging(User user, String data) {

        System.out.println(data);
        List<MtPackagingInfo> list= JSONArray.parseArray(data,MtPackagingInfo.class);

        String res = mtPackagingService.addPackaging(list, user);
        if ("success".equals(res)) {
            return new JsonResult(1, null);
        } else {
            return new JsonResult(0, "", "400", res);
        }
    }

    /**
     * 包装查看
     */
    @RequestMapping("/getPackagingDetail.do")
    @ResponseBody
    public JsonResult getPackagingDetail(User user, Integer mtBaseId,Integer supplierMaterial) {
        Map<String, Object> map = new HashMap<>();

        //先查询是否无需包装
        List<MtPackagingInfo> wx = mtPackagingService.getWxPackagingList(mtBaseId,supplierMaterial);

        if (wx.size() > 0) {
            map.put("isPacking", "0");
            map.put("list", new ArrayList<>());


            //暂停的包装信息
            List<MtPackagingInfoHistory> suspendList = new ArrayList<>();

            List<MtPackagingInfoHistory> allList = mtPackagingService.getPackagingList(mtBaseId,supplierMaterial);

            for (MtPackagingInfoHistory info : allList) {
                if (info.getLayersCount().intValue() != 0) {
                    if (info.getEnabled() == 0) {

                        suspendList.add(info);
                    }
                }
            }
            map.put("suspendList", suspendList);
            return new JsonResult(1, map);
        }
        //正常的包装信息
        List<MtPackagingInfoHistory> list = new ArrayList<>();
        //暂停的包装信息
        List<MtPackagingInfoHistory> suspendList = new ArrayList<>();

        List<MtPackagingInfoHistory> allList = mtPackagingService.getPackagingList(mtBaseId,supplierMaterial);

        for (MtPackagingInfoHistory info : allList) {
            if (info.getLayersCount().intValue() != 0) {
                if (info.getEnabled() == 1) {

                    list.add(info);
                } else {
                    suspendList.add(info);
                }
            }
        }
        if (allList.size() == 1) {
            if (allList.get(0).getLayersCount().intValue() == 0) {
                map.put("isPacking", "0");
            } else {
                map.put("isPacking", "1");
            }
        } else {
            map.put("isPacking", "1");
        }


        map.put("list", list);
        map.put("suspendList", suspendList);
        return new JsonResult(1, map);
    }


    /**
     * 包装修改
     */
    @RequestMapping("/updatePackaging.do")
    @ResponseBody
    public JsonResult updatePackaging(User user,String data) {

        MtPackagingInfo info= JSONObject.parseObject(data,MtPackagingInfo.class);

        MtPackagingInfoHistory mtPackagingInfoHistory = mtPackagingService.getPackagingRecordDetail(info.getId());

        MtPackagingInfo mtPackagingInfo = mtPackagingService.getPackaging(mtPackagingInfoHistory.getPackaging());

        mtPackagingInfo.setMaterial(info.getMaterial());
        List<MtPackagingStructure> mtPackagingStructureList = JSONObject.parseArray(info.getList(), MtPackagingStructure.class);


        mtPackagingInfo.setOperation(3);
        mtPackagingInfo.setEnabledTime(new Date());
        mtPackagingInfo.setEnabled(1);
        mtPackagingInfo.setUpdateDate(new Date());
        mtPackagingInfo.setUpdateName(user.getUserName());
        mtPackagingInfo.setUpdator(user.getUserID());
        mtPackagingInfo.setOuterLength(info.getOuterLength());
        mtPackagingInfo.setOuterWidth(info.getOuterWidth());
        mtPackagingInfo.setOuterHeight(info.getOuterHeight());
        mtPackagingInfo.setLengthUnitId(info.getLengthUnitId());
        mtPackagingInfo.setLengthUnit(info.getLengthUnit());
        mtPackagingInfo.setWidthUnit(info.getWidthUnit());
        mtPackagingInfo.setWidthUnitId(info.getWidthUnitId());
        mtPackagingInfo.setHeightUnit(info.getHeightUnit());
        mtPackagingInfo.setHeightUnitId(info.getHeightUnitId());
        mtPackagingInfo.setOuterShape(info.getOuterShape());

        if (info.getEffectTime() == null) {
            mtPackagingInfo.setEffectTime(DateUtil.fomatDate(DateUtil.getDay()));
        } else {
            mtPackagingInfo.setEffectTime(info.getEffectTime());
        }

        String res = mtPackagingService.updatePackaging(mtPackagingInfo, mtPackagingStructureList, user);

        if ("success".equals(res)) {
            return new JsonResult(1, null);
        } else {
            return new JsonResult(0, "", "400", res);
        }
    }


    /**
     * 暂停或启动包装
     */
    @RequestMapping("/stopOrStartPackaging.do")
    @ResponseBody
    public JsonResult stopOrStartPackaging(User user, Long id, int enabled) {
        MtPackagingInfoHistory mtPackagingInfoHistory = mtPackagingService.getPackagingRecordDetail(id);
        MtPackagingInfo info = mtPackagingService.getPackagingDetail(mtPackagingInfoHistory.getPackaging());
        info.setEnabled(enabled);
        info.setEnabledTime(new Date());
        info.setUpdateDate(new Date());
        info.setUpdateName(user.getUserName());
        info.setUpdator(user.getUserID());
        info.setEffectTime(DateUtil.fomatDate(DateUtil.getDay()));
        if (enabled == 1) {
            info.setOperation(4);
        } else {
            info.setOperation(5);
        }
        List<MtPackagingStructure> mtPackagingStructureList = info.getStructureList();

        String res = mtPackagingService.updatePackaging(info, mtPackagingStructureList, user);

        if ("success".equals(res)) {
            return new JsonResult(1, null);
        } else {
            return new JsonResult(0, "", "400", res);
        }
    }

    /**
     * 确定设置
     */
    @RequestMapping("/submitSettings.do")
    @ResponseBody
    public JsonResult submitSettings(User user, Integer mtBaseId) {


        MtBase base = materielService.getMtBaseById(mtBaseId);
        base.setPackagingState(2);
        materielService.updateMtBase(base);
        return new JsonResult(1, null);
    }

    /**
     * 获取操作记录
     */
    @RequestMapping("/getPackagingRecordList.do")
    @ResponseBody
    public JsonResult getPackagingRecordList(User user, Long id) {
        MtPackagingInfoHistory mtPackagingInfoHistory = mtPackagingService.getPackagingRecordDetail(id);
        List<MtPackagingInfoHistory> list = mtPackagingService.getPackagingRecordList(mtPackagingInfoHistory.getPackaging());
        return new JsonResult(1, list);
    }

    /**
     * 获取操作记录详情
     */
    @RequestMapping("/getPackagingRecordDetail.do")
    @ResponseBody
    public JsonResult getPackagingRecordDetail(User user, Long recordId) {
        MtPackagingInfoHistory infoHistory = mtPackagingService.getPackagingRecordDetail(recordId);
        return new JsonResult(1, infoHistory);
    }

    /**
     * 包装删除
     */
    @RequestMapping("/deletePackaging.do")
    @ResponseBody
    public JsonResult deletePackaging(User user, Long id) {
        MtPackagingInfoHistory mtPackagingInfoHistory = mtPackagingService.getPackagingRecordDetail(id);
        mtPackagingService.deletePackaging(mtPackagingInfoHistory.getPackaging());
        return new JsonResult(1, null);
    }
}
