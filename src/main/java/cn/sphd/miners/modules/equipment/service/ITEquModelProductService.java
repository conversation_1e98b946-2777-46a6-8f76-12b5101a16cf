package cn.sphd.miners.modules.equipment.service;

import cn.sphd.miners.modules.equipment.entity.TEquModelProduct;

import java.util.List;

/**
 * 装备管理_型号产品参与Service接口
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
public interface ITEquModelProductService {
    /**
     * 查询装备管理_型号产品参与
     *
     * @param id 装备管理_型号产品参与主键
     * @return 装备管理_型号产品参与
     */
    public TEquModelProduct selectTEquModelProductById(Long id);

    /**
     * 查询装备管理_型号产品参与列表
     *
     * @param tEquModelProduct 装备管理_型号产品参与
     * @return 装备管理_型号产品参与集合
     */
    public List<TEquModelProduct> selectTEquModelProductList(TEquModelProduct tEquModelProduct);

    /**
     * 新增装备管理_型号产品参与
     *
     * @param tEquModelProduct 装备管理_型号产品参与
     * @return 结果
     */
    public int insertTEquModelProduct(TEquModelProduct tEquModelProduct);

    /**
     * 修改装备管理_型号产品参与
     *
     * @param tEquModelProduct 装备管理_型号产品参与
     * @return 结果
     */
    public int updateTEquModelProduct(TEquModelProduct tEquModelProduct);

    /**
     * 批量删除装备管理_型号产品参与
     *
     * @param ids 需要删除的装备管理_型号产品参与主键集合
     * @return 结果
     */
    public int deleteTEquModelProductByIds(String... ids);

    /**
     * 删除装备管理_型号产品参与信息
     *
     * @param id 装备管理_型号产品参与主键
     * @return 结果
     */
    public int deleteTEquModelProductById(Long id);
}
