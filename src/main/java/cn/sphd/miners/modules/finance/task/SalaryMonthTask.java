package cn.sphd.miners.modules.finance.task;

import cn.sphd.miners.modules.finance.entity.PersonnelSalary;
import cn.sphd.miners.modules.finance.service.SalaryService;
import cn.sphd.miners.modules.system.service.DlmService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.concurrent.TimeUnit;

public class SalaryMonthTask {
    @Autowired
    DlmService dlmService;
    @Autowired
    SalaryService salaryService;
    public void salaryTriggerMonth() throws Exception {
        System.out.println("开始定时获取员工列表");
        //wyu:获取分布式锁
        String lockKey, methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        if ((lockKey=dlmService.getLock(methodName))!= null) {
            List<PersonnelSalary> personnelSalaryList=salaryService.selectOrgBySalary();
            for (PersonnelSalary personnelSalary: personnelSalaryList)
                salaryService.salaryIndexInitialization(personnelSalary.getOrg(),null);
            dlmService.releaseLock(methodName, lockKey);
        }
    }
}
