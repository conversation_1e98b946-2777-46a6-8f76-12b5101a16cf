package cn.sphd.miners.modules.material.entity;

import javax.persistence.*;
import java.util.Date;

@Entity( name ="MtMaterialImport" )
@Table( name ="t_mt_material_import" )
public class MtMaterialImport {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;//                   int not null auto_increment comment 'ID',
    @Column(name = "org")
    private Integer org;//                  int comment '机构ID',
    @Column(name = "import")
    private Integer importID;//               int comment '导入ID',
    @Column(name = "state")
    private Integer state;//                tinyint default 1 comment '状态:1-导入临时表,2-录入信息,3-完成',
    @Column(name = "code")
    private String code;  //               varchar(100) comment '客户代号',
    @Column(name = "name")
    private String name;//                 varchar(100) comment '名称',
    @Column(name = "model")
    private String model;//                varchar(100) comment '型号',
    @Column(name = "specifications")
    private String specifications;//       varchar(255) comment '规格',
    @Column(name = "unit")
    private String unit;//                 varchar(100) comment '计量单位',
    @Column(name = "category")
    private  Integer category;//             int comment '类别',
    @Column(name = "exp_required")
    private Integer expRequired;//         boolean comment '是否有保质期方面的要求',
    @Column(name = "openDuration")
    private Integer open_duration;//        int comment '开封/开瓶后可使用天数',
    @Column(name = "relatedItem")
    private Integer related_item;//         tinyint comment '相关的数据:1-截止日期,2-生产日期',
    @Column(name = "sameExpiration")
    private Integer same_expiration;//      boolean comment '保质期是否相同',
    @Column(name = "expirationDays")
    private Integer expiration_days;//      int comment '保质期天数',
    @Column(name = "memo")
    private String memo;//                 varchar(255) comment '备注',
    @Column(name = "creator")
    private Integer creator;//              int comment '创建人id',
    @Column(name = "createName")
    private String create_name;//          national varchar(100) comment '创建人',
    @Column(name = "createTime")
    private Date create_time;//          datetime comment '创建时间',
    @Column(name = "updator")
    private Integer updator;//              int comment '修改人id',
    @Column(name = "update_name")
    private String updateName;//          national varchar(100) comment '修改人',
    @Column(name = "updateTime")
    private Date update_time;//          datetime comment '修改时间',
    @Column(name = "operation")
    private String operation;//            char(1) comment '操作:1-增,2-删,3-修改',
    @Column(name = "previousId")
    private Integer previous_id;//          int comment '修改前记录ID',
    @Column(name = "versionNo")
    private Integer version_no;//           int comment '版本号,每次修改+1',

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getImportID() {
        return importID;
    }

    public void setImportID(Integer importID) {
        this.importID = importID;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getSpecifications() {
        return specifications;
    }

    public void setSpecifications(String specifications) {
        this.specifications = specifications;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public Integer getExpRequired() {
        return expRequired;
    }

    public void setExpRequired(Integer expRequired) {
        this.expRequired = expRequired;
    }

    public Integer getOpen_duration() {
        return open_duration;
    }

    public void setOpen_duration(Integer open_duration) {
        this.open_duration = open_duration;
    }

    public Integer getRelated_item() {
        return related_item;
    }

    public void setRelated_item(Integer related_item) {
        this.related_item = related_item;
    }

    public Integer getSame_expiration() {
        return same_expiration;
    }

    public void setSame_expiration(Integer same_expiration) {
        this.same_expiration = same_expiration;
    }

    public Integer getExpiration_days() {
        return expiration_days;
    }

    public void setExpiration_days(Integer expiration_days) {
        this.expiration_days = expiration_days;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreate_name() {
        return create_name;
    }

    public void setCreate_name(String create_name) {
        this.create_name = create_name;
    }

    public Date getCreate_time() {
        return create_time;
    }

    public void setCreate_time(Date create_time) {
        this.create_time = create_time;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(Date update_time) {
        this.update_time = update_time;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPrevious_id() {
        return previous_id;
    }

    public void setPrevious_id(Integer previous_id) {
        this.previous_id = previous_id;
    }

    public Integer getVersion_no() {
        return version_no;
    }

    public void setVersion_no(Integer version_no) {
        this.version_no = version_no;
    }
}
