<template>
  <div id="approveReimburseFinance">
    <TY_NavTop title="个人报销的付款" isSearch="true" @toggleList="search"></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待付款审批" name="1" closable='false'>
        <!--<div class="nowTime">今天是{{nowTime | formatDay('YYYY-MM-DD dddd')}}</div>-->
        <div>
          <a class="ui-cell" v-on:click="jump('22' + item.id, 0, '')" v-for="(item, index) in cashierPaymentHandle" v-bind:key="index">
            <div class="item_fn">
              <div>{{item.createName | stringSplit(4)}}</div>
              <div>
                <p>申请报销 {{item.amount}} 元</p>
                <p>票据 {{item.billQuantity}} 张，共 {{item.billAmount}} 元</p>
              </div>
            </div>
            <div class="txt_right">{{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="可付款" name="2" closable='false' :msgCount="financePayable.length">
        <div class="tip">
          <p> 下列付款申请已获审批通过，有待付款。</p>
        </div>
        <div>
          <a class="ui-cell" v-on:click="jump('22' + item.id, 0 , 3 , 1)" v-for="(item, index) in financePayable" v-bind:key="index">
            <div class="item_fn">
              <div>{{item.createName | stringSplit(4)}}</div>
              <div>
                <p>申请报销 {{item.amount}} 元</p>
                <p>票据 {{item.billQuantity}} 张，共 {{item.billAmount}} 元</p>
              </div>
            </div>
            <div class="txt_right">{{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="待复核" name="3" closable='false'>
        <div class="tip">
          <p>下列待付款项有待复核。</p>
        </div>
        <div>
          <a class="ui-cell" v-on:click="jump('22' + item.id, 0, '4', 2)" v-for="(item, index) in financeReview" v-bind:key="index">
            <div class="item_fn">
              <div>{{item.createName | stringSplit(4)}}</div>
              <div>
                <p>申请报销 {{item.amount}} 元</p>
                <p>票据 {{item.billQuantity}} 张，共 {{item.billAmount}} 元</p>
              </div>
            </div>
            <div class="txt_right">{{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="待付款" name="4" closable='false' :msgCount="cashierTwoSettled.length">
        <div class="tip">
          <p>下列待付款项复核通过，请予付款。</p>
        </div>
        <div>
          <a class="ui-cell" v-on:click="jump('22' + item.id, 0, '5', 2)" v-for="(item, index) in cashierTwoSettled" v-bind:key="index">
            <div class="item_fn">
              <div>{{item.createName | stringSplit(4)}}</div>
              <div>
                <p>申请报销 {{item.amount}} 元</p>
                <p>票据 {{item.billQuantity}} 张，共 {{item.billAmount}} 元</p>
              </div>
            </div>
            <div class="txt_right">{{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>
<style lang="less">
  #approveReimburseFinance{
    overflow: auto;
    .ui-cell{ display: block; }
    .item_fn{
      display: flex;
      div:nth-child(1){ flex:1;  }
      div:nth-child(2){ flex:3;  }
    }
    .tabCount{
      top:0;
      right:0 ;
      left:auto;
    }
    .tabs-tab{
      width:25%;
      font-size:12px ;
    }
    .txt_right{
      text-align: right;
      font-size:11px;
      padding-right:15px;
    }
    .tip{
       padding:5px 12px;
       background-color: #E2EFDA;
       display: block;
     }
  }
</style>
<script>
export default {
  name: 'approveReimburseFinance',
  data () {
    return {
      cashierHandle: [],
      cashierTwoSettled: [],
      cashierPaymentHandle: [],
      financePayable: [],
      financeReview: [],
      listenersUid: [],
      tabValue: '1',
      nowTime: 0
    }
  },
  filters: {
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  created () {
    let that = this
    this.axios.get('../../../reimburseWindow/getCurrentTime.do')
      .then(function (response) {
        let time = response.data.time
        that.nowTime = time
      })
      .catch(function (error) {
        console.log(error)
      })
    this.listenersUid = [
      this.sphdSocket.subscribe('cashierHandle', function (data) {
        // 待票据审核
        that.cashierHandle = JSON.parse(data)
        console.log('cashierHandle session Socket received OK:' + JSON.parse(data))
        console.log(JSON.parse(data))
      }),
      this.sphdSocket.subscribe('cashierHandle', function (data) {
        console.log('cashierHandle session Socket received OK:' + data)
        let getData = JSON.parse(data)
        let personnelReimburse = getData.personnelReimburse
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.cashierHandle.unshift(personnelReimburse)
        } else if (operate < 0) {
          let reimburseId = personnelReimburse.id
          let _index = -1
          that.cashierHandle.forEach(function (item, index) {
            if (item.id === reimburseId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.cashierHandle.splice(_index, 1)
          }
        }
      }, null, 'user'),
      this.sphdSocket.subscribe('cashierPaymentHandle', function (data) {
        // 待付款审批
        console.log('待付款审批' + JSON.parse(data))
        console.log(JSON.parse(data))
        that.cashierPaymentHandle = JSON.parse(data)
      }),
      this.sphdSocket.subscribe('cashierPaymentHandle', function (data) {
        console.log('cashierPaymentHandle session Socket received OK:' + data)
        let getData = JSON.parse(data)
        let personnelReimburse = getData.personnelReimburse
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.cashierPaymentHandle.unshift(personnelReimburse)
        } else if (operate < 0) {
          let reimburseId = personnelReimburse.id
          let _index = -1
          that.cashierPaymentHandle.forEach(function (item, index) {
            if (item.id === reimburseId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.cashierPaymentHandle.splice(_index, 1)
          }
        }
      }, null, 'user'),
      this.sphdSocket.subscribe('financePayable', function (data) {
        // 可付款
        console.log('financePayable session Socket received OK:' + data)
        console.log(JSON.parse(data))
        that.financePayable = JSON.parse(data)
      }),
      this.sphdSocket.subscribe('financePayable', function (data) {
        console.log('financePayable session Socket received OK:' + data)
        let getData = JSON.parse(data)
        let personnelReimburse = getData.personnelReimburse
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.financePayable.unshift(personnelReimburse)
        } else if (operate < 0) {
          let reimburseId = personnelReimburse.id
          let _index = -1
          that.financePayable.forEach(function (item, index) {
            if (item.id === reimburseId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.financePayable.splice(_index, 1)
          }
        }
      }, null, 'user'),
      this.sphdSocket.subscribe('financeReview', function (data) {
        // 待复核
        console.log('financeReview session Socket received OK:' + data)
        console.log(JSON.parse(data))
        that.financeReview = JSON.parse(data)
      }),
      this.sphdSocket.subscribe('financeReview', function (data) {
        console.log('financeReview session Socket received OK:' + data)
        let getData = JSON.parse(data)
        let personnelReimburse = getData.personnelReimburse
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.financeReview.unshift(personnelReimburse)
        } else if (operate < 0) {
          let reimburseId = personnelReimburse.id
          let _index = -1
          that.financeReview.forEach(function (item, index) {
            if (item.id === reimburseId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.financeReview.splice(_index, 1)
          }
        }
      }, null, 'user'),
      this.sphdSocket.subscribe('cashierTwoSettled', function (data) {
        // 待付款
        that.cashierTwoSettled = JSON.parse(data)
        console.log('cashierTwoSettled session Socket received OK:' + data)
        console.log(JSON.parse(data))
      }),
      this.sphdSocket.subscribe('cashierTwoSettled', function (data) {
        console.log('cashierTwoSettled session Socket received OK:' + data)
        let getData = JSON.parse(data)
        let personnelReimburse = getData.personnelReimburse
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.cashierTwoSettled.unshift(personnelReimburse)
        } else if (operate < 0) {
          let reimburseId = personnelReimburse.id
          let _index = -1
          that.cashierTwoSettled.forEach(function (item, index) {
            if (item.id === reimburseId) {
              _index = index
            }
          })
          if (_index > -1) {
            that.cashierTwoSettled.splice(_index, 1)
          }
        }
      }, null, 'user'),
      this.sphdSocket.subscribe('notice', function (data) {
        console.log('notice Socket received OK:' + data)
        if (data === '1') {
          that.$router.push({
            path: '/approveReimburse'
          })
        }
      })
    ]
    let oid = this.sphdSocket.user.oid
    let userId = this.sphdSocket.user.userID
    let session = this.sphdSocket.sessionid
    this.sphdSocket.send('cashier', { oid: oid, session: session, 'userId': userId })
    this.sphdSocket.send('paymentReviewApprover', { 'session': session, 'userId': userId })
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    jump: function (id, param, tab, num) {
      this.$store.dispatch('setFinReimburseNav', tab)
      if (num === 1) {
        this.$router.push({
          path: `/approveReimburseDetailFu/${id}/${param}/0`
        })
      } else if (num === 2) {
        console.log('待复核,待付款,')
        this.$router.push({
          path: `/approveReimburseDetailFu2/${id}/${param}`
        })
      } else {
        this.$router.push({
          path: `/approveReimburseDetail/${id}/${param}`
        })
      }
    },
    search: function () {
      this.$router.push({
        path: `/approveReimburseQuery/2`
      })
    }
  }
}
</script>
