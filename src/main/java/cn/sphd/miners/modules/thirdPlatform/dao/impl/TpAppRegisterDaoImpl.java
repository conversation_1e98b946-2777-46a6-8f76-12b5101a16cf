package cn.sphd.miners.modules.thirdPlatform.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.thirdPlatform.dao.TpAppRegisterDao;
import cn.sphd.miners.modules.thirdPlatform.entity.TpAppRegister;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class TpAppRegisterDaoImpl extends BaseDao<TpAppRegister, Serializable> implements TpAppRegisterDao {
}
