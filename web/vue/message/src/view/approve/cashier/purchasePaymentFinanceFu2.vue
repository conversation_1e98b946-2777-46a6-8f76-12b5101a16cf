<template>
  <div id="purchasePaymentFinanceFu2"  v-loading.fullscreen.lock="loading">
    <TY_NavTop title="采购部门的付款"></TY_NavTop>
    <div class="detailInfo">
       <!--票据-->
       <div v-if="pund === 0">
        <div @click="jumpDetail" >
          <span><span class="ttl1">申请付款金额 </span><span> {{detailInfo.amount && detailInfo.amount.toFixed(2)}} 元 </span></span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div>
          <span class="ttl1">收款单位</span><span>  {{details.fullName }}</span>
        </div>
        <div @click="getAlreadyPayDetails">
          <span class="ttl1">已付款</span><span> {{detailInfo.payNum || 0 }}笔，共{{(detailInfo.paidAmount && detailInfo.paidAmount.toFixed(2))|| 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div @click="getCheckDetails">
          <span class="ttl1">待复核的付款</span><span> {{details.numApplication || 0 }}笔，共{{(detailInfo.lockedAmount && detailInfo.lockedAmount.toFixed(2)) || 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
      </div>
      <!--预付款-->
      <div v-if="pund === 1">
        <div @click="jumpDetail" >
          <span><span class="ttl1">申请付款金额 </span><span> {{detailInfo.planAmount && detailInfo.planAmount.toFixed(2)}} 元 </span></span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div>
          <span class="ttl1">收款单位</span><span> {{details.srmSupplier.fullName }} </span>
        </div>
        <div @click="getAlreadyPayDetails">
          <span class="ttl1">已付款</span><span> {{detailInfo.payNum || 0 }}笔，共{{(detailInfo.paidAmount && detailInfo.paidAmount.toFixed(2))|| 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div @click="getCheckDetails">
          <span class="ttl1">待复核的付款</span><span> {{details.num || 0 }}笔，共{{(detailInfo.lockedAmount && detailInfo.lockedAmount.toFixed(2)) || 0 }}元 </span>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div>
          <span class="ttl1">计划付款金额</span>{{detailInfo.planAmount && detailInfo.planAmount.toFixed(2)}}元
        </div>
        <div>
          <span class="ttl1">计划付款时间</span>{{detailInfo.planDate |formatDay('YYYY-MM-DD')}}
          <span class="redTxt"><span class="ttl1">计划付款方式</span>{{isture}}</span>
        </div>
        <div class="martop10">
          <span class="ttl1">付款事由</span>采购的预付款
        </div>
      </div>
    </div>
    <div>
      <div class="handle_button">
        <span v-on:click="jumpFu3()" class="btncat" style="margin-right:50px; font-size:15px; ">原定的付款方式</span>
      </div>
      <el-form :label-position="left" label-width="140px">
        <div v-if="details.financePayment.method === '5'" >
          <el-form-item label="拟付款方式"><span>银行转账</span></el-form-item>
          <el-form-item label="银行账户"><span>{{ details.financeAccount.name }}元</span></el-form-item>
          <el-form-item label="账户类型"><span>{{ details.financeAccount.isPublic == '1' ? '对公户 ': '非对公户' }} </span></el-form-item>
          <el-form-item label="开户行"><span>{{details.financeAccount.bankName}}</span></el-form-item>
          <el-form-item label="账号"><span>{{details.financeAccount.account}}</span></el-form-item>
          <el-form-item label="拟付款金额"><span>{{details.financePayment.factAmount.toFixed(2)}}</span></el-form-item>
          <el-form-item label="拟付款日期"><span>{{ details.financePayment.planDate | formatDay('YYYY-MM-DD') }}</span></el-form-item>
        </div>
        <div v-if="details.financePayment.method === '4'">
          <el-form-item label="拟付款方式"><span>承兑汇票（外）</span></el-form-item>
          <el-form-item label="汇票号"><span>{{ details.financeReturn.returnNo  }}</span></el-form-item>
          <el-form-item label="票据所开具发票或收据的金额"><span>{{ details.financeReturn.billAmount.toFixed(2) }}</span></el-form-item>
          <el-form-item label="到期日"><span>{{ details.financeReturn.expireDate  | formatDay('YYYY-MM-DD')  }} </span></el-form-item>
          <el-form-item label="出具的银行"><span>{{ details.financeReturn.bankName }}</span></el-form-item>
          <p class="orangeTip" v-if="Number(detailInfo.balanceAmount) > 0">！本次将多支付{{ detailInfo.balanceAmount && detailInfo.balanceAmount.toFixed(2) }}元！该差额将进入“借款管理”模块，请跟踪管理。</p>
          <div class="handle_button">
            <span @click="seeticket()" class="btncat" style="margin-right:50px; font-size:15px; ">查看汇票</span>
          </div>
        </div>
        <div v-if="details.financePayment.method === '3'">
          <div v-if="details.invoiceType == '2'" style="margin-left: 30px;">  <!--(1-其他付款方式  2-内部转账支票 3-外部转账支票)-->
            <el-form-item label="拟付款方式"><span>转账支票（内）</span></el-form-item>
            <el-form-item label="转帐支票的开户行"><span>{{ details.financeAccount.bankName  }}</span></el-form-item>
            <el-form-item label="转帐支票的支票号"><span>{{ details.financeChequeDetail.chequeNo }}</span></el-form-item>
            <el-form-item label="支票金额"><span>{{ details.financeChequeDetail.amount.toFixed(2) }}</span></el-form-item>
            <el-form-item label="到期日"><span>{{ details.financeChequeDetail.expireDate | formatDay('YYYY-MM-DD')  }} </span></el-form-item>
          </div>
          <div v-if="details.invoiceType == '3'">
            <el-form-item label="拟付款方式"><span>转账支票（外）</span></el-form-item>
            <el-form-item label="支票号"><span>{{ details.financeReturn.returnNo  }}</span></el-form-item>
            <el-form-item label="所开具发票或收据的金额"><span>{{ details.financeReturn.billAmount.toFixed(2) }}</span></el-form-item>
            <el-form-item label="到期日"><span>{{ details.financeReturn.expireDate  | formatDay('YYYY-MM-DD')  }} </span></el-form-item>
            <el-form-item label="出具的银行"><span>{{ details.financeReturn.bankName}}</span></el-form-item>
            <p class="orangeTip" v-if="Number(detailInfo.balanceAmount) > 0">！本次将多支付{{ detailInfo.balanceAmount && detailInfo.balanceAmount.toFixed(2) }}元！该差额将进入“借款管理”模块，请跟踪管理。</p>
            <div class="handle_button">
              <span @click="seeticket(1)" class="btncat" style="margin-right:50px; font-size:15px; ">查看支票</span>
            </div>
          </div>
        </div>
        <div style="padding-left: 100px;">
          <el-radio-group v-model="alreadyPay">
            <div v-if="details.financePayment">
              <el-radio v-if="code === 25" :label="1">已按此方式付款完毕</el-radio>
              <el-radio v-if="code === 64" :label="1">已按此方式付款完毕</el-radio>
              <div v-if="alreadyPay == '1'">
                <el-form :label-position="left" :rules="rules" label-width="140px" :model="square" ref="square" style="margin-left:-100px; ">
                  <el-form-item style="margin-top:15px;" label='实际付款日期' prop="factDate" v-if="details.financePayment.method === '5'">
                    <el-date-picker type="date" placeholder="请选择" v-model="square.factDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker>
                  </el-form-item>
                  <el-form-item style="margin-top:15px;" label='收款单位接收日期' prop="factDate" v-if="details.financePayment.method !== '5'">
                    <el-date-picker type="date" placeholder="请选择" v-model="square.factDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker>
                  </el-form-item>
                  <el-form-item style="margin-top:20px;" label="收款单位经手人" prop="operatorName" v-if="details.financePayment.method !== '5'">
                    <el-input style="width:250px; " v-model="square.operatorName" placeholder="请录入经手人姓名"></el-input>
                  </el-form-item>
                  <el-form-item style="margin-top:20px;" label="摘要" prop="summary">
                    <el-input type="textarea" style="width:250px; " v-model="square.summary" placeholder="请输入摘要"></el-input>
                  </el-form-item>
                </el-form>
              </div>
            </div>
            <el-radio style="margin-top:12px;" :label="2">修改付款方式</el-radio>
          </el-radio-group>
          <div class="handle_button" style="text-align: center; margin-left:-100px; ">
            <span style="width:80%; " class="ui-btn ui-btn_info"  @click="updatePayBtn()">确 定</span>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>
<style lang="less">
#purchasePaymentFinanceFu2{
  .orangeTip{
    margin-top: 20px;
    color: #ff9304;
    font-size: 12px;
    padding: 0 35px;
  }
  .el-form-item{ width:364px; }
  .el-textarea{ width: 232px!important; margin-top: 5px; }
  .el-input{ width: 232px!important; }
  .el-icon-arrow-right{ float: right; font-size: 16px; }
  .el-form-item__error{ position: relative }
  background: #fff;
  overflow: auto!important;
  .processItem{
    display: flex;
    font-size: 11px;
    margin-left:-5px;
    div:nth-child(1){ text-align: left; flex: 1 }
    div:nth-child(2){ text-align: center; flex: 1 }
    div:nth-child(3) { text-align: right; flex: 2 }
  }
  .tipBlue{
    color:#0a4d85; text-align: center;
  }
  .con1{ background:#fff; margin-bottom:10px; padding:5px 15px; font-size:14px;  }
  .con1>p{text-align: right; font-size:0.8em;   }
  .oInfo{ padding:5px 20px; }
  .txt_right{ text-align: right;  }
  .detailInfo{
    padding:10px 20px 0px 20px;
    background:#fff; font-size:14px;
    border-bottom:1px solid #ddd ;
    line-height: 26px;
    .ttl1{
      display: inline-block;
      width: 86px;
      text-align: right;
      margin-right: 10px;
    }
  }
  .mar30{ margin-right:30px;  }
  .btncat{ color:#0b9df9; font-size:1.1em; cursor: default;  }
  .btncat:hover{ color: #0883f9; text-decoration:underline;font-weight:bold;   }
  .flexC{
    display: flex;margin-bottom:15px ;
    &>span{ display:block; flex: 1;  }
  }
}
</style>
<script>
export default {
  name: 'purchasePaymentFinanceFu2',
  data () {
    return {
      loading: true,
      toggle: -1,
      detailInfo: {},
      details: {
        poPaymentApplication: {
          amount: ''
        },
        listMapBillCat: [],
        listMapFeeCat: [],
        srmSupplier: {},
        poOrdersPrepayment: {},
        poOrders: {
          amount: ''
        },
        approvalProcessList: {},
        financePaymentHistory: {},
        financeReturnHistory: {},
        financeAccount: {},
        financePayment: {},
        financeChequeDetail: {},
        financeReturn: {}
      },
      isSpecialBill: false,
      alreadyPay: false,
      nowApproveItem: {},
      isNowApprover: false,
      square: { 'factDate': '', 'summary': '', 'operatorName': '' },
      rules: {
        factDate: [
          // details.financePayment.method === '5'? '实际付款日期' : '收款单位接收日期'
          { required: true, message: '该项不能为空' }
        ],
        summary: [
          { required: true, message: '该项不能为空' }
        ],
        operatorName: [
          { required: true, message: '该项不能为空' }
        ]
      },
      pund: 0,
      code: 0,
      busyid: ''
    }
  },
  filters: {
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  created () {
    let that = this
    let paramCode = this.$route.params.id
    this.busyid = this.$route.params.busyid
    let parampun = Number(this.$route.params.pun)
    // let choose = Number(this.$route.params.choose)
    console.log(this.$route.params)
    that.code = Number(paramCode.substring(0, 2)) // 24-待复核; 25-待付款
    that.invoiceID = paramCode.substring(2, paramCode.length)
    that.pund = Number(parampun)
    that.getDetail()
  },
  destroyed: function () {
  },
  methods: {
    getAlreadyPayDetails () {
      console.log('getAlreadyPayDetails ')
      this.details.pund = this.pund
      this.$store.dispatch('setPurchaseFinanceInfo', this.details)
      this.$router.push({
        path: `/purchasePaymentFinanceAlready/${this.invoiceID}`
      })
    },
    getCheckDetails () {
      console.log('getCheckDetails ')
      this.details.pund = this.pund
      this.$store.dispatch('setPurchaseFinanceInfo', this.details)
      this.$router.push({
        path: `/purchasePaymentFinanceCheck/${this.invoiceID}`
      })
    },
    seeticket: function () {
      let that = this
      let item = this.details.financeReturn
      item.pund = that.pund
      if (this.details.financePayment.method === '4') {
        item.type = 3
      } else if (this.details.financePayment.method === '3') {
        item.type = 2
      }
      console.log(item)
      localStorage.setItem('invoiceTicketInfo', JSON.stringify(item))
      that.$router.push({
        path: `/purchasePaymentInvoiceInfo`
      })
    },
    jumpDetail: function () {
      let that = this
      that.$router.push({
        path: `/purchaseInvoiceDetails3/34${that.invoiceID}/${this.pund}/${that.busyid}`
      })
    },
    getDetail: function () {
      let that = this
      if (that.pund === 0) {
        this.axios.post('../../../purchaseInvoice/getPurchaseInvoiceDetail.do', { 'applicationId': that.invoiceID, 'financePaymentId': that.busyid })
          .then(function (response) {
            let res = response.data.data
            that.details = res
            that.detailInfo = res.poPaymentApplication
            that.loading = false
            let ap = res.approvalProcessList || []
            that.nowApproveItem = ap[ap.length - 1]
            let bills = res.listMapBillCat || []
            bills.forEach(function (item2) {
              if (item2.invoiceCategory === '1') {
                that.isSpecialBill = 1
              }
            })
          })
          .catch(function (error) {
            console.log(error)
          })
      } else {
        this.axios.post('../../../purchaseInvoice/advancePaymentDetail.do', { 'orderPrepaymentId': that.invoiceID, 'financePaymentId': that.busyid })
          .then(function (response) {
            let res = response.data.data
            that.details = res
            that.detailInfo = res.poOrdersPrepayment
            that.loading = false
            let ap = res.approvalProcessList || []
            that.nowApproveItem = ap[ap.length - 1]
            let bills = res.listMapBillCat || []
            bills.forEach(function (item2) {
              if (item2.invoiceCategory === '1') {
                that.isSpecialBill = 1
              }
            })
          })
          .catch(function (error) {
            console.log(error)
          })
      }
    },
    updatePayBtn: function () { // 点击修改付款方式下面的确定按钮后调用的方法
      let that = this
      if (that.alreadyPay === '1' || that.alreadyPay === 1) {
        let factDate = that.square.factDate
        if (factDate) {
          let tips = ''
          let operatorName = that.square.operatorName
          let summary = that.square.summary
          if (summary.length === 0) {
            that.$kiko_message('请输入摘要！')
            return false
          }
          let isok = that.details.financePayment.method === '4' ||
            (that.details.financePayment.method === '3' &&
              that.details.financePayment.invoiceType === '3')
          if (isok) {
            if (operatorName.length === 0) {
              that.$kiko_message('请输入收款单位经手人！')
              return false
            }
          }
          if (that.isSpecialBill === 1) {
            tips = '本次报销中含有增值税专用发票。<span class="color-red">您需在系统的“增票认证”中确认认证情况后，</span>报销数据才会进入会计模块。'
          } else if (that.isSpecialBill === 2 && that.selectFin.isPublic === '1') {
            tips = '会计规定：<span class="color-red">收据不宜使用对公户报销</span><p>再遇收据报销时请使用非对公户转账或现金</p>'
          }
          if (tips) {
            this.$alert(tips, '！！提示', {
              dangerouslyUseHTMLString: true,
              confirmButtonText: '我知道了',
              callback: action => {
                that.submit()
              }
            })
          } else {
            that.submit()
          }
        } else {
          that.$kiko_message('请将必填项补充完整！')
        }
      }
      else if (that.alreadyPay === '2' || that.alreadyPay === 2) {
        var choose = 0
        localStorage.payid = this.busyid
        that.$router.push({
          path: `/purchasePaymentFinanceFu/${that.$route.params.id}/1/0/${that.$route.params.pun}/${choose}`
        })
      } else {
        that.$kiko_message('勾选付款方式，确定才能生效')
      }
    },
    submit: function () {
      let that = this
      that.loading = true
      let approvalProcessId = that.nowApproveItem.id
      let data = {
        'approvalProcessId': approvalProcessId,
        'summary': that.square.summary,
        'operatorName': that.square.operatorName,
        'factDate': that.square.factDate
      }
      this.axios.post('../../../purchaseInvoice/payFinanceApproval.do', data)
        .then(function (response) {
          let res = response.data.data
          that.$kiko_message(res.content)
          if (res.content === '操作成功') {
            localStorage.navNum = 4
            that.$router.push({
              path: `/purchasePaymentFinanceApproval`
            })
          } else {
            that.loading = false
          }
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    jumpFu3: function (bus, pre) {
      let that = this
      if (that.details.status === 0) {
        that.$kiko_message('原来计划的付款方式没有修改过！')
        return false
      }
      this.$router.push({
        path: `/purchasePaymentFinanceFu3/${this.$route.params.id}/${this.$route.params.pun}/${that.busyid}`
      })
    }

  },
  computed: {
    isture: function () {
      let one = Number(this.detailInfo.planMethod)
      let str = ''
      switch (one){
        case 1: str = '现金'; break;
        case 3: str = '转账支票'; break;
        case 4: str = '承兑汇票'; break;
        case 5: str = '银行转账'; break;
        case 6: str = '非公户银行转账'; break;
        default: str = `未识别-${ one }`
      }
      return str
    }
  }
}
</script>
