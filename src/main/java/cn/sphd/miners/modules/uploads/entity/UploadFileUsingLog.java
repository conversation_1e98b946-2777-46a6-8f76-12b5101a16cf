package cn.sphd.miners.modules.uploads.entity;

import cn.sphd.miners.common.persistence.BaseEntity;
import org.hibernate.annotations.CreationTimestamp;
import javax.persistence.*;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2021-01-29 
 */

@Entity
@Table ( name ="t_upload_file_using_log" )
public class UploadFileUsingLog extends BaseEntity {
	@Id
	@Column
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Long id;

	@Column
	private Integer org;

	@Column(name = "acc_id")
	private Long accId;

	@Column
	private String source;

	@Column(name = "calling_function")
	private String callingFunction;

	@Column(name = "called_function")
	private String calledFunction;

	@Column
	private String filename;

   	@Column(name = "upload_file_using_id" )
	private Long uploadFileUsingId;

   	@Column(name = "display_name")
	private String displayName;

   	@Column(name = "original_filename")
	private String originalFilename;

   	@Column(name = "last_name")
	private String lastName;

   	@Column
	private Long size;

	@Column(length = 128)
	private String hash;//SHA512 of file

	@Column(length = 50)
	private String module;

	@Column(name = "user_id")
	private Integer userId;

	@Column(name = "user_name",length = 100)
	private String userName;

	@Column(name = "callback_class")
	private String callbackClass;

	@Column(name = "callback_json")
	private String callbackJson;

	@Column(name = "callback_key")
	private String callbackKey;

	@Column
	private String info;

	@CreationTimestamp
	@Column(name = "create_date")
	private Date createDate;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getOrg() {
		return org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Long getAccId() {
		return accId;
	}

	public void setAccId(Long accId) {
		this.accId = accId;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public String getCallingFunction() {
		return callingFunction;
	}

	public void setCallingFunction(String callingFunction) {
		this.callingFunction = callingFunction;
	}

	public String getCalledFunction() {
		return calledFunction;
	}

	public void setCalledFunction(String calledFunction) {
		this.calledFunction = calledFunction;
	}

	public String getFilename() {
		return filename;
	}

	public void setFilename(String filename) {
		this.filename = filename;
	}

	public Long getUploadFileUsingId() {
		return uploadFileUsingId;
	}

	public void setUploadFileUsingId(Long uploadFileUsingId) {
		this.uploadFileUsingId = uploadFileUsingId;
	}

	public String getDisplayName() {
		return displayName;
	}

	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}

	public String getOriginalFilename() {
		return originalFilename;
	}

	public void setOriginalFilename(String originalFilename) {
		this.originalFilename = originalFilename;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public Long getSize() {
		return size;
	}

	public void setSize(Long size) {
		this.size = size;
	}

	public String getHash() {
		return hash;
	}

	public void setHash(String hash) {
		this.hash = hash;
	}

	public String getModule() {
		return module;
	}

	public void setModule(String module) {
		this.module = module;
	}

	public Integer getUserId() {
		return userId;
	}

	public void setUserId(Integer userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getCallbackClass() {
		return callbackClass;
	}

	public void setCallbackClass(String callbackClass) {
		this.callbackClass = callbackClass;
	}

	public String getCallbackJson() {
		return callbackJson;
	}

	public void setCallbackJson(String callbackJson) {
		this.callbackJson = callbackJson;
	}

	public String getCallbackKey() {
		return callbackKey;
	}

	public void setCallbackKey(String callbackKey) {
		this.callbackKey = callbackKey;
	}

	public String getInfo() {
		return info;
	}

	public void setInfo(String info) {
		this.info = info;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
}
