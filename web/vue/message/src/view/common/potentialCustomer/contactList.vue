<template>
  <div id="contactList" class="potential" v-show="show">
    <TY_NavTop title="潜在客户申请" :isReBack="true" isButton="true" @reback="closeShow" @toggleSubmit="setContact"></TY_NavTop>
    <div class="container">
      <div class="panel-content">
        <el-row type="flex" justify="space-between">
          <el-col :span="18" v-if="source === 1">请选择本次访谈的访谈对象（可多选）</el-col>
          <el-col :span="24" v-else-if="source === 2">请选择本次访谈的同行者（同事）（可多选）</el-col>
          <el-col :span="16" v-else-if="source === 3">本次访谈的同行者（非同事）共0人</el-col>
          <el-col :span="6" v-if="source === 1"><el-link type="primary" class="bold" @click="jumpAddContact(1, '')">新增访谈对象</el-link></el-col>
          <el-col :span="8" v-if="source === 3"><el-link type="primary" class="bold" @click="jumpAddContact(1, '')">录入同事外的人</el-link></el-col>
        </el-row>
      </div>
      <div class="gapR"></div>
      <div class="panel-content specialCheck">
        <el-checkbox-group v-model="checks" v-if="source === 1">
          <el-checkbox v-for="(contact, index) in personnelList" :label="contact.id" :key="index">
            <div class="ui-row">
              {{contact.name}} {{(contact.post).substr(0,8)}} {{contact.mobile}}
              <el-link class="posRt" type="primary" @click.stop.native.prevent="contactSee($event,potentialId, contact.id)">查看</el-link>
            </div>
          </el-checkbox>
        </el-checkbox-group>
        <el-checkbox-group v-model="colleague" v-else-if="source === 2">
          <el-checkbox v-for="(contact, index) in personnelList" :label="contact.userID" :key="index">
            <div class="ui-row">
              {{contact.userName}} {{contact.mobile}}
            </div>
          </el-checkbox>
        </el-checkbox-group>
        <el-row type="flex" justify="space-between" v-for="(contact, index) in personnelList" v-else :key="index">
          <el-col :span="18">{{contact.name}} {{contact.mobile}}</el-col>
          <el-col :span="3"><el-link type="primary" @click="jumpAddContact(2, contact)">修改</el-link></el-col>
          <el-col :span="3"><el-link type="danger" @click="deleteUnColleague(contact.id)">删除</el-link></el-col>
        </el-row>
      </div>
    </div>
    <contactView :details="contact" v-bind:view.sync="contactView"></contactView>
    <AddContact :details="contact" :potentialId="potentialId" :editType="editType" v-bind:show.sync="addVisible" @add_contact="setAddContact"></AddContact>
  </div>
</template>
<style lang="less">
  .potential{
    .gapR{
      height: 20px;
      background: #eee;
    }
    .specialCheck .el-checkbox{
      margin-bottom: 6px;
      margin-right: 0;
      display: block;
      .el-checkbox__label{
        width: 88%;
      }
    }
  }
  #contactList {
    position: fixed;
    top: 0;
    left: 0;
    z-index:1000;
    width: 100%;
    height: 100%;
    background: #fff;
    .el-row{
      margin-left: 10px;
    }
    .posRt{
      float: right;
    }
  }
</style>
<script>
import contactSee from '../potentialCustomer/interviewUpdate_contactSee'
import addContact from '../potentialCustomer/interviewUpdate_contact'
export default {
  name: 'contactList',
  props: {
    source: Number,
    potentialId: String,
    personnelList: Array,
    show: Boolean
  },
  data () {
    return {
      contactView: false,
      checks: [],
      colleague: [],
      editType: {},
      contact: {
        socialList: []
      },
      addVisible: false,
      addUnColVisible: false
    }
  },
  components: {
    'contactView': contactSee,
    'AddContact': addContact
  },
  methods: {
    contactSee: function (e, potentialId, id) {
      e.stopPropagation();
      let _this = this
      _this.loading = true
      _this.axios.post('../../../sale/gettPotentialContactsSocial.do', {
        customerId: potentialId,
        contactId: id
      }).then(function (response) {
        _this.contact = response.data.data
        let list = response.data.data.socialList
        let socialList = {
          mobile: [],
          qq: [],
          email: [],
          weixin: [],
          weibo: [],
          defined: []
        }
        for (let i in list) {
          let type = list[i].type
          switch (type) {
            case '1':
              socialList.mobile.push(list[i])
              break
            case '2':
              socialList.qq.push(list[i])
              break
            case '3':
              socialList.email.push(list[i])
              break
            case '4':
              socialList.weixin.push(list[i])
              break
            case '5':
              socialList.weibo.push(list[i])
              break
            case '9':
              socialList.defined.push(list[i])
              break
          }
        }
        _this.contact.socialList = socialList
        _this.loading = false
        _this.contactView = true
      }).catch(function (error) {
        console.log(error)
      })
    },
    setContact: function () {
      let that = this
      let data = ``
      if (that.source === 1) {
        data = that.checks
        if (data.length === 0) {
          this.$kiko_message('请至少选择一个对象！')
          return false
        }
      } else if (that.source === 2) {
        data = that.colleague
      } else {
        data = that.personnelList
      }
      this.$emit('select_contact', data)
      this.closeShow()
    },
    jumpAddContact: function (type, contact) {
      let _this = this
      _this.addVisible = true
      _this.editType.type = type //1:add 2=update
      _this.editType.source = _this.source
      if (contact === ""){
        contact = {
          name: '',
          post: '',
          mobile: '',
          socialList: {
            mobile: [],
            qq: [],
            email: [],
            weixin: [],
            weibo: [],
            defined: []
          },
          visitCard: ''
        }
        if (_this.source === 3) {
          contact.cardPath = ''
        }
      } else {
        contact.visitCard = contact.cardPath
      }
      _this.contact = contact
    },
    closeShow: function () {
      this.$emit('update:show', false)
    },
    setAddContact: function (contact) {
      if (this.editType.type === 1) {
        this.personnelList.push(contact);
      } else{
        let index = this.personnelList.find(value => value.participator === contact.participator);
        this.personnelList[index] = contact
      }
    },
    deleteUnColleague: function (id) {
      let _this = this
      let index = _this.personnelList.find(value => value.participator === id);
      _this.personnelList.splice(index, 1)
    }
  }
}
</script>
