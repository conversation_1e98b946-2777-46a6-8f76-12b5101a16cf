<template>
  <div class="box">
    <div class="head">
      <PageHeader title="财会" @mycloseFun="handleCustomClose" :showClose=true :showBack=true>
      </PageHeader>
    </div>
    <div class="sticky f4">
      <p v-if="list.financeInvoiceDetail">
        {{ invoiceDetailsMonthly.type == 1 ? '增值税专用发票' : invoiceDetailsMonthly.type == 2 ? '增值税普通发票' : invoiceDetailsMonthly.type == 3 ? '其他发票' : 'err'}}<span
          class="padding-0-10">{{ list.financeInvoiceDetail.invoiceNo
        }}</span>
      </p>
    </div>
    <div class="re">
      <div class="ty-fff" v-if="list.financeInvoiceDetail">
        <p>客户名称<span class="padding-0-10">{{ list.financeInvoiceDetail.receiveCorp }}</span></p>
        <p>发票金额<span class="padding-0-10">{{ list.financeInvoiceDetail.amount }}元</span></p>
      </div>
      <div class="flex" v-if="list.financeInvoiceDetail">
        <p v-if="list.invoiceDetail">发票内容</p>
        <p v-else><span class="padding-r-5">开票申请日期</span>{{ new
        Date(list.financeInvoiceDetail.applicationTime).format('yyyy-MM-dd') }}</p>
        <p>开票日期<span class="padding-0-10">{{ new
        Date(list.financeInvoiceDetail.operateDate).format('yyyy-MM-dd') }}</span></p>
      </div>
      <el-table
          :data="list.invoiceDetail"
          border
          style="width: 100%"
          stripe
          :header-cell-style="{background: '#fcf2d0',color:'#2c3e50',fontWeight:'400'}"
          v-if="list.invoiceDetail"
      >
        <el-table-column prop="name" label="商品名称" min-width="22%" align="center"></el-table-column>
        <el-table-column prop="item_quantity" label="数量" align="center" min-width="18%"
        ></el-table-column>
        <el-table-column prop="unit_price_c" label="单价" align="center" min-width="25%"
        ></el-table-column>
        <el-table-column prop="item_amount" label="金额" align="center" min-width="25%"
        ></el-table-column>
        <el-table-column prop="invoice_amount" label="税额" align="center" min-width="25%"
        ></el-table-column>
      </el-table>
      <p class="padding-0-15 h35f14" style="text-align: end" v-if="list.invoiceDetail">开票申请<span
          class="padding-0-10">{{ list.financeInvoiceDetail.applicantName }}</span>{{ new
      Date(list.financeInvoiceDetail.applicationTime).format('yyyy-MM-dd hh:mm:ss') }}</p>
    </div>
  </div>
</template>

<script>
import {getInvoiceDetailAll} from '@/api/financeBill'

export default {
  name: "invoiceDetails",
  data() {
    return {
      list: {},
      invoiceDetailsList: {},
    }
  },
  methods: {
    handleCustomClose() {
      this.$router.push("/caikuai")
    },
    getList() {
      const params = {invoiceDetailId: this.id}
      getInvoiceDetailAll(params).then(res1 => {
        console.log(res1.data.data, 'res1.data.data')
        this.list = res1.data.data
      }).catch(error => {
        console.error('Error:', error);
      })
    }
  },
  computed: {
    id() {
      return this.$route.query.id
    }
  },
  mounted() {
    this.getList()
    let invoiceDetailsMonthly = localStorage.getItem('invoiceDetailsMonthly');
    this.invoiceDetailsMonthly = JSON.parse(invoiceDetailsMonthly);
  },
}
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";

.sticky {
  line-height: 30px;
  padding: 2px 15px;
}

.re {
  position: relative;
  top: 40px;

  > :nth-child(1), > :nth-child(2) {
    line-height: 28px;
    padding: 4px 15px;
  }
}

.head {
  position: fixed;
  width: 100%;
  z-index: 999;
}

.h35f14 {
  font-size: 14px;
  line-height: 30px;
}

.flex {
  padding: 5px 15px;
  line-height: 30px;
  background-color: #dae0f0;
}

::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background: $tyBounceColorGray;
}

::v-deep .el-table .cell {
  padding: 0 5px;
}

//非斑马纹颜色
::v-deep .el-table tr {
  background: #ffffff;
}

//表头文字
::v-deep .el_title th {
  color: #2c3e50;
}
</style>