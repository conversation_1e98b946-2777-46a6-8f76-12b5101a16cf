<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.accountant.mapper.TaccountantEstablishEntityMapper" >
  <resultMap id="BaseResultMap" type="TaccountantEstablishEntity" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="org" property="org" jdbcType="INTEGER" />
    <result column="type" property="type" jdbcType="CHAR" />
    <result column="memo" property="memo" jdbcType="VARCHAR" />
    <result column="creator" property="creator" jdbcType="INTEGER" />
    <result column="create_name" property="createName" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="updator" property="updator" jdbcType="INTEGER" />
    <result column="update_name" property="updateName" jdbcType="VARCHAR" />
    <result column="update_date" property="updateDate" jdbcType="TIMESTAMP" />
    <result column="approve_Item" property="approveItem" jdbcType="INTEGER" />
    <result column="approve_status" property="approveStatus" jdbcType="CHAR" />
    <result column="approve_level" property="approveLevel" jdbcType="INTEGER" />
    <result column="auditor" property="auditor" jdbcType="INTEGER" />
    <result column="auditor_name" property="auditorName" jdbcType="VARCHAR" />
    <result column="audit_date" property="auditDate" jdbcType="TIMESTAMP" />
    <result column="operation" property="operation" jdbcType="CHAR" />
    <result column="apply_memo" property="applyMemo" jdbcType="VARCHAR" />
    <result column="approve_memo" property="approveMemo" jdbcType="VARCHAR" />
    <result column="message_id" property="messageId" jdbcType="INTEGER" />
    <result column="previous_id" property="previousId" jdbcType="INTEGER" />
    <result column="version_no" property="versionNo" jdbcType="INTEGER" />
  </resultMap>

<!--  <sql id="Base_Column_List" >-->
<!--    id, org, type, memo, creator, create_name as createName, create_date as createDate, updator, update_name as updateName, update_date as updateDate,-->
<!--    approve_Item as approveItem, approve_status as approveStatus , approve_level as approveLevel, auditor, auditor_name as auditorName, audit_date as auditDate, operation,-->
<!--    apply_memo as applyMemo , approve_memo as approveMemo, message_id as messageId, previous_id as previousId, version_no as versionNo-->
<!--  </sql>-->

  <sql id="Base_Column_List" >
    id, org, type, memo, creator, create_name, create_date, updator, update_name, update_date,
    approve_Item, approve_status , approve_level , auditor, auditor_name , audit_date , operation,
    apply_memo , approve_memo , message_id , previous_id , version_no
  </sql>

  <select id="listPage" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_accountant_establish
  </select>

  <select id="getSingle" parameterType="TaccountantEstablishEntity" resultMap="BaseResultMap" >
    select 
    <include refid="Base_Column_List" />
    from t_accountant_establish
    where id = #{id,jdbcType=INTEGER}
  </select>

  <select id="getEstablishRecords" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_accountant_establish
    where org = #{oid} order by create_date desc
  </select>

  <delete id="delete" parameterType="TaccountantEstablishEntity" >
    delete from t_accountant_establish
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <insert id="insert" parameterType="TaccountantEstablishEntity" useGeneratedKeys="true" keyProperty="id" >
    insert into t_accountant_establish
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="org != null" >
        org,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="memo != null" >
        memo,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="createName != null" >
        create_name,
      </if>
      <if test="createDate != null" >
        create_date,
      </if>
      <if test="updator != null" >
        updator,
      </if>
      <if test="updateName != null" >
        update_name,
      </if>
      <if test="updateDate != null" >
        update_date,
      </if>
      <if test="approveItem != null" >
        approve_Item,
      </if>
      <if test="approveStatus != null" >
        approve_status,
      </if>
      <if test="approveLevel != null" >
        approve_level,
      </if>
      <if test="auditor != null" >
        auditor,
      </if>
      <if test="auditorName != null" >
        auditor_name,
      </if>
      <if test="auditDate != null" >
        audit_date,
      </if>
      <if test="operation != null" >
        operation,
      </if>
      <if test="applyMemo != null" >
        apply_memo,
      </if>
      <if test="approveMemo != null" >
        approve_memo,
      </if>
      <if test="messageId != null" >
        message_id,
      </if>
      <if test="previousId != null" >
        previous_id,
      </if>
      <if test="versionNo != null" >
        version_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="org != null" >
        #{org,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        #{type,jdbcType=CHAR},
      </if>
      <if test="memo != null" >
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createName != null" >
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null" >
        #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateName != null" >
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null" >
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="approveItem != null" >
        #{approveItem,jdbcType=INTEGER},
      </if>
      <if test="approveStatus != null" >
        #{approveStatus,jdbcType=CHAR},
      </if>
      <if test="approveLevel != null" >
        #{approveLevel,jdbcType=INTEGER},
      </if>
      <if test="auditor != null" >
        #{auditor,jdbcType=INTEGER},
      </if>
      <if test="auditorName != null" >
        #{auditorName,jdbcType=VARCHAR},
      </if>
      <if test="auditDate != null" >
        #{auditDate,jdbcType=TIMESTAMP},
      </if>
      <if test="operation != null" >
        #{operation,jdbcType=CHAR},
      </if>
      <if test="applyMemo != null" >
        #{applyMemo,jdbcType=VARCHAR},
      </if>
      <if test="approveMemo != null" >
        #{approveMemo,jdbcType=VARCHAR},
      </if>
      <if test="messageId != null" >
        #{messageId,jdbcType=INTEGER},
      </if>
      <if test="previousId != null" >
        #{previousId,jdbcType=INTEGER},
      </if>
      <if test="versionNo != null" >
        #{versionNo,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <update id="update" parameterType="TaccountantEstablishEntity" >
    update t_accountant_establish
    <set >
      <if test="org != null" >
        org = #{org,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=CHAR},
      </if>
      <if test="memo != null" >
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createName != null" >
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null" >
        updator = #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateName != null" >
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="approveItem != null" >
        approve_Item = #{approveItem,jdbcType=INTEGER},
      </if>
      <if test="approveStatus != null" >
        approve_status = #{approveStatus,jdbcType=CHAR},
      </if>
      <if test="approveLevel != null" >
        approve_level = #{approveLevel,jdbcType=INTEGER},
      </if>
      <if test="auditor != null" >
        auditor = #{auditor,jdbcType=INTEGER},
      </if>
      <if test="auditorName != null" >
        auditor_name = #{auditorName,jdbcType=VARCHAR},
      </if>
      <if test="auditDate != null" >
        audit_date = #{auditDate,jdbcType=TIMESTAMP},
      </if>
      <if test="operation != null" >
        operation = #{operation,jdbcType=CHAR},
      </if>
      <if test="applyMemo != null" >
        apply_memo = #{applyMemo,jdbcType=VARCHAR},
      </if>
      <if test="approveMemo != null" >
        approve_memo = #{approveMemo,jdbcType=VARCHAR},
      </if>
      <if test="messageId != null" >
        message_id = #{messageId,jdbcType=INTEGER},
      </if>
      <if test="previousId != null" >
        previous_id = #{previousId,jdbcType=INTEGER},
      </if>
      <if test="versionNo != null" >
        version_no = #{versionNo,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

</mapper>