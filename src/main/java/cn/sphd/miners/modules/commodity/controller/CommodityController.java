package cn.sphd.miners.modules.commodity.controller;

import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.accountant.service.SubjectSelectService;
import cn.sphd.miners.modules.accountant.service.SubjectSettingService;
import cn.sphd.miners.modules.accountant.util.DateUtil;
import cn.sphd.miners.modules.commodity.entity.*;
import cn.sphd.miners.modules.commodity.service.PdCompositionService;
import cn.sphd.miners.modules.commodity.service.PdProcessService;
import cn.sphd.miners.modules.commodity.service.ProductService;
import cn.sphd.miners.modules.commodity.service.impl.UnitUnit;
import cn.sphd.miners.modules.material.entity.MtBase;
import cn.sphd.miners.modules.material.entity.MtCategory;
import cn.sphd.miners.modules.material.entity.MtStockInfo;
import cn.sphd.miners.modules.material.entity.MtStockInfoHistory;
import cn.sphd.miners.modules.material.service.MaterielService;
import cn.sphd.miners.modules.material.service.MtStockInfoHistoryService;
import cn.sphd.miners.modules.material.service.MtStockService;
import cn.sphd.miners.modules.material.utils.SubjectUtils;
import cn.sphd.miners.modules.personal.service.UserMessageService;
import cn.sphd.miners.modules.sales.service.PdCustomerService;
import cn.sphd.miners.modules.sales.service.SaleService;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by Administrator on 2016/10/8.
 */
@Controller
@RequestMapping("/commodity")
public class CommodityController {
    @Autowired
    PdProcessService pdProcessService;
    @Autowired
    ProductService productService;
    @Autowired
    PdCompositionService pdCompositionService;
    @Autowired
    MaterielService materielService;
    @Autowired
    PdCustomerService pdCustomerService;
    @Autowired
    SubjectSettingService subjectSettingService;

    @Autowired
    SubjectSelectService subjectSelectService;
    @Autowired
    UserMessageService userMessageService;
    @Autowired
    UserService userService;
    @Autowired
    MtStockService mtStockService;
    @Autowired
    MtStockInfoHistoryService stockInfoHistoryService;

    @Autowired
    SaleService saleService;
    //跳转商品基本信息主页
    @RequestMapping("/basic.do")
    public String basic() {
        return "/commodity/basic";
    }

    //跳转构成信息主页
    @RequestMapping("/composition.do")
    public String composition() {
        return "technology/productArchives";
    }

    //跳转工序信息主页
    @RequestMapping("/process.do")
    public String process() {
        return "/commodity/process";
    }



    /**
     * 通讯型商品页面路由
     */
    @RequestMapping("/commonGoods.do")
    public String commonGoods(){
        return "/commodity/commonGoods";
    }

    /**
     * 关联管理路由
     */
    @RequestMapping("/relatedCommodities.do")
    public String relatedCommodities(){
        return "/technology/relatedCommodities";
    }

    /**
     * <AUTHOR>
     * @date 2016/10/12 9:36
     * 获得商品管理基本信息
     */
    @ResponseBody
    @RequestMapping("/getProduct.do")
    public void getProduct(String customerName, String innerSn,String outerSn, Integer pageNumber, Integer quantum, HttpServletResponse response, User user) throws IOException {
        Integer oid = user.getOid();
        Map<String, Object> map = new HashMap<String, Object>();
        List<PdMerchandise> pdMerchandises = productService.getByNameInnerSn(oid, customerName, innerSn,outerSn);
        for (PdMerchandise pdMerchandise : pdMerchandises) {
            Integer productId = pdMerchandise.getProduct_();
            if (productId != null) {
                PdBase pdBase = productService.getById(productId);
                pdMerchandise.setUnit(pdBase.getUnit());
                pdMerchandise.setNetWeight(pdBase.getNetWeight());
                pdMerchandise.setProductName(pdBase.getName());
                pdMerchandise.setCurrentStock(pdBase.getCurrentStock());
                pdMerchandise.setMinimumStock(pdBase.getMinimumiStock());
                MtBase mtBase = materielService.getMtBaseByProduct(pdMerchandise.getProduct_());
                MtStockInfo mtStockInfo = mtStockService.getMtStockInfoByMId(mtBase.getId());
                if(mtStockInfo!=null)
                pdMerchandise.setPosition(mtStockInfo.getStockPosition());

//                if (pdBase.getMinimumiStock()!=null){
//                    pdMerchandise.setMinimumStock(pdBase.getMinimumiStock().intValue());
//                }else {
//                    pdMerchandise.setMinimumStock(0);
//
                List<MtStockInfoHistory> stockInfoHistories = stockInfoHistoryService.getMtStockInfoHistoryListByMaterial(mtBase.getId());
                if(stockInfoHistories!=null&&stockInfoHistories.size()>=0)
                    pdMerchandise.setStockEdited(1);
                productService.updatePdMerchandise(pdMerchandise);
            }
        }
        List<PdMerchandise> pdMerchandiseList = new ArrayList<>();
        int total = 0;
        if (pdMerchandises.size() > 0) {
            total = pdMerchandises.size();
        }
        int totalPage;
        if (total != 0) {
            if (total % quantum == 0) {
                totalPage = total / quantum;
            } else {
                totalPage = total / quantum + 1;
            }
        } else {
            totalPage = 1;
        }
        if (pageNumber != 0 && quantum != 0 && total != 0) {
            int max = pageNumber * quantum;
            int min = pageNumber * quantum - quantum;
            for (int i = min; i < max; i++) {
                if (i < total) {
                    pdMerchandiseList.add(pdMerchandises.get(i));
                }
            }
        }
        map.put("pageNumber", pageNumber);
        map.put("totalPage", totalPage);
        map.put("pdCustomerProductList", pdMerchandiseList);
        ObjectToJson.objectToJson1(map, new String[]{"customer", "product", "TPdMerchandiseHistoryPdMerchandiseViaProductCustomer","slOrdersItemHashSet",
                "TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct",
                "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct","TPdCompositionViaParent","pdPackHashSet"}, response);
    }

    /**
     * <AUTHOR>
     * @date 2016/10/11 17:23
     * 基本信息查看功能
     */
    @ResponseBody
    @RequestMapping("/checkProduct.do")
    public void checkProduct(HttpServletResponse response, Integer productId, Integer cusProId) throws IOException {
        //  cusProId  销售对照id
        Map<String, Object> map = new HashMap<>();
        PdBase pdBases = productService.getById(productId);
        List<PdMerchandise> pdMerchandises = productService.getByProductId(productId, cusProId);
        map.put("pdBases", pdBases);
        map.put("pdCustomerProducts", pdMerchandises);
        ObjectToJson.objectToJson1(map, new String[]{"customer", "product","slOrdersItemHashSet","TPdMerchandiseHistoryPdMerchandiseViaProductCustomer",
                "TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct",
                "TPdMerchandisePdBaseViaProduct", "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct","TPdCompositionViaParent"}, response);
    }

    /**
     * <AUTHOR>
     * @date 2016/10/24 9:30
     * 共用信息(基本信息)查看
     */
    @ResponseBody
    @RequestMapping("/checkBase.do")
    public void checkBase(HttpServletResponse response, Integer productId) throws IOException {
        Map<String, Object> map = new HashMap<>();
        PdBase pdBases = productService.getById(productId);
        map.put("pdBases", pdBases);
        ObjectToJson.objectToJson1(map, new String[]{"TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct",
                "TPdMerchandisePdBaseViaProduct", "TPdProcessPdBaseViaProduct",
                "TMtBaseViaProduct", "TPdCompositionViaParent"}, response);
    }

    /**
     * <AUTHOR>
     * @date 2016/10/24 9:56
     * 修改共用信息（基本信息）
     */
    @ResponseBody
    @RequestMapping("/updateBase.do")
    public void updateBase(PdBase pdBase, HttpServletResponse response, User user) throws IOException {
        Map<String, Object> map = new HashMap<>();
        
        if (pdBase.getId() != null) {
            PdBase pdBase1 = productService.getById(pdBase.getId()); //要修改的数据
            PdBase pdBase2 = productService.getByInnerSn(user.getOid(), pdBase.getInnerSn()); //通过内部图号查询
            List<PdMerchandise> pdMerchandises = productService.getCusProByProductId(pdBase.getId());
            if (pdBase2 == null || pdBase1.getId() == pdBase2.getId()) {
                pdBase1.setInnerSn(pdBase.getInnerSn());
                for (PdMerchandise pdMerchandise : pdMerchandises) {
                    pdMerchandise.setInnerSn(pdBase.getInnerSn());
                    productService.updatePdMerchandise(pdMerchandise);
                }
                pdBase1.setName(pdBase.getName());
                pdBase1.setUnit(pdBase.getUnit());
                if(pdBase.getNetWeight()!=null&&StringUtils.isNotEmpty(pdBase.getWeightUnit())){
                    BigDecimal a= UnitUnit.toMilligram(pdBase.getNetWeight(),pdBase.getWeightUnit());
                    pdBase1.setNetWeight(UnitUnit.milligramToBest(a));
                    pdBase1.setWeightUnit(UnitUnit.milligramToUnit(a));
                }

                pdBase1.setModel(pdBase.getModel());
                pdBase1.setSpecifications(pdBase.getSpecifications());
                pdBase1.setMinimumiStock(pdBase.getMinimumiStock());
                pdBase1.setUpdator(user.getUserID());
                pdBase1.setUpdateName(user.getUserName());
                pdBase1.setUpdateDate(new Date());
                productService.updatePdBase(pdBase1);
                map.put("status", 1);
                map.put("pdBase", pdBase1);
            } else {
                map.put("status", 2);
            }
        } else {
            map.put("status", 0);
        }
        ObjectToJson.objectToJson1(map, new String[]{"TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct","slOrdersItemHashSet",
                "TPdMerchandisePdBaseViaProduct", "TPdProcessPdBaseViaProduct",
                "TMtBaseViaProduct", "TPdCompositionViaParent"}, response);
    }

    /**
     * <AUTHOR>
     * @date 2016/10/24 9:37
     * 查看客户信息
     */
    @ResponseBody
    @RequestMapping("/checkCustomerProduct.do")
    public void checkCustomerProduct(Integer cusProId, Integer productId, HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<>();
        PdMerchandise pdMerchandises = productService.getByCusProIdAndProId(cusProId, productId);
        map.put("pdCustomerProducts", pdMerchandises);
        ObjectToJson.objectToJson1(map, new String[]{"slOrdersItemHashSet","pdPackHashSet","product", "customer", "TPdMerchandiseHistoryPdMerchandiseViaProductCustomer","slOrdersItemHashSet"}, response);
    }

    /**
     * <AUTHOR>
     * @date 2016/10/24 10:07
     * 修改客户信息
     */
    @ResponseBody
    @RequestMapping("/updateCustomerProduct.do")
    public void updateCustomerProduct(HttpServletRequest request, HttpServletResponse response, User user) throws IOException {
        Map<String, Object> map = new HashMap<>();
        
        Integer oid=user.getOid();
        String pdMerchandise = request.getParameter("pdCustomerProduct");
        JSONObject jsonCustomerProduct = JSONObject.fromObject(pdMerchandise);
        if (pdMerchandise != null) {
            Integer cusProId = jsonCustomerProduct.getInt("id");
            Integer productId = jsonCustomerProduct.getInt("product_");
            PdMerchandise pdMerchandise1 = productService.getByCusProIdAndProId(cusProId, productId);

            new SubjectUtils().updateCustomerProduct(pdMerchandise1,jsonCustomerProduct,subjectSettingService,oid,user,subjectSelectService);

            pdMerchandise1.setOuterSn(jsonCustomerProduct.optString("outerSn"));
            pdMerchandise1.setOuterName(jsonCustomerProduct.optString("outerName"));

                if(jsonCustomerProduct.getInt("customerId")==0) {//customerId=0 未选择下拉框 否则已选择
                    pdMerchandise1.setCustomerName(jsonCustomerProduct.optString("customerName"));
                    SlCustomer slCustomer =new SlCustomer();
                    slCustomer.setOid(oid);
                    slCustomer.setCategory("2");//2为新用户
                    slCustomer.setCreateDate(new Date());
                    slCustomer.setPeroid(Integer.valueOf(DateUtil.getDy(slCustomer.getCreateDate())));
                    slCustomer.setCreateName(user.getUserName());
                    slCustomer.setCreator(user.getUserID());
                    slCustomer.setName(jsonCustomerProduct.optString("customerName"));
                    slCustomer.setType("1");
                    pdCustomerService.addPdCustomer(slCustomer);
                    pdMerchandise1.setCustomer(slCustomer);

                }else{
                    SlCustomer slCustomer =pdCustomerService.getPdCustomerByIdAndOid(oid,jsonCustomerProduct.getInt("customerId"));
                    pdMerchandise1.setCustomerName(slCustomer.getName());
                    pdMerchandise1.setCustomer(slCustomer);
                }

            pdMerchandise1.setUnitPrice(BigDecimal.valueOf(jsonCustomerProduct.getDouble("unitPrice")));
            pdMerchandise1.setTaxRate(BigDecimal.valueOf(jsonCustomerProduct.getDouble("taxRate")));
            pdMerchandise1.setUnitPriceNotax(BigDecimal.valueOf(jsonCustomerProduct.getDouble("unitPriceNotax")));
            pdMerchandise1.setMemo(jsonCustomerProduct.optString("memo"));
            pdMerchandise1.setUpdateName(user.getUserName());
            pdMerchandise1.setUpdator(user.getUserID());
            Timestamp date = new Timestamp(System.currentTimeMillis());
            pdMerchandise1.setUpdateDate(date);
            productService.updatePdMerchandise(pdMerchandise1);
            map.put("pdCustomerProduct", pdMerchandise1);

            map.put("status", 1);



        } else {
            map.put("status", 0);
        }
        ObjectToJson.objectToJson1(map, new String[]{"product", "customer","slOrdersItemHashSet","TPdMerchandiseHistoryPdMerchandiseViaProductCustomer","slOrdersItemHashSet","pdPackHashSet"}, response);
    }

    /**
     * <AUTHOR>
     * @date 2016/10/13 9:50
     * 删除商品基本信息
     *
     * @date 2018-9-15  update by zy
     *
     */
//    @ResponseBody
//    @RequestMapping("/deleteProduct.do")
//    public void deleteProduct(Integer cusProId, HttpServletResponse response) throws IOException {
//        Map<String, Object> map = new HashMap<String, Object>();
//        if (cusProId != null && cusProId != 0) {
//            PdMerchandise pdMerchandise = productService.getPdByCusProId(cusProId);
//
//            SlCustomer slCustomer =pdCustomerService.getPdCustomerById(pdMerchandise.getCustomer().getId());
//
//            PdBase pdBase=pdMerchandise.getProduct();
//
//            if (pdMerchandise.getSlOrdersItemHashSet().size()<=0&&pdMerchandise.getPdPackHashSet().size()<=0){
//
//                if (pdBase.getTPdMerchandisePdBaseViaProduct().size() < 1 && pdBase.getTPdBaseHistoryPdBaseViaProduct().size() <= 0 && pdBase.getTPdCompositionPdBaseViaProduct().size() <= 0 && pdBase.getTPdCompositionViaParent().size() <= 0 && pdBase.getTPdProcessPdBaseViaProduct().size() <= 0) {
//                    productService.deletePdBase(pdBase);
//                }
//
//                productService.deleteByCusProId(pdMerchandise);
//
//                List<SlOrdersCollect> slOrdersCollects = saleService.getOrdersCollectsByCustomerId(slCustomer.getId());
//                List<SlCollectApplication> slCollectApplications = saleService.getSlCollectApplicationsByCustomerId(slCustomer.getId());
//
//                if (slCustomer.getSlOrdersHashSet().size()<=0&& slCustomer.getTPdMerchandisePdCustomerViaCustomer().size()<=1&&slOrdersCollects.size()==0&&slCollectApplications.size()==0){
//                    try {
//                        pdCustomerService.deletePdCustomer(slCustomer);
//                    }catch (Exception e){
//                        System.out.println("slCustomer未删除");
//                        e.printStackTrace();
//                    }
//                }
//
//                map.put("status", 1);  //删除成功
//
//                List<PdMerchandise> customerProducts = pdCustomerService.getByInnerSn(pdMerchandise.getProduct().getOid(),pdMerchandise.getInnerSn());
//                if(customerProducts!=null&&customerProducts.size()<=0){
//                    if(pdMerchandise.getProduct_()!=null)
//                        productService.deleteById(pdMerchandise.getProduct_());
//                }
//            }else {
//                map.put("status",2);  //已生成订单，不可以删除
//            }
//        } else {
//            map.put("status", 0); //删除失败
//        }
//        ObjectToJson.objectToJson1(map, new String[]{"product","customer","TPdMerchandiseHistoryPdMerchandiseViaProductCustomer","slOrdersItemHashSet",
//                                                      "TPdBaseHistoryPdBaseViaProduct","TPdCompositionPdBaseViaProduct","TPdMerchandisePdBaseViaProduct","TPdProcessPdBaseViaProduct",
//                                                       "TMtBaseViaProduct","TPdCompositionViaParent"}, response);
//    }

    /**
     * <AUTHOR>
     * @date 2016/10/14 16:44
     * 根据内部图号模糊查询商品信息
     */
    @ResponseBody
    @RequestMapping("/getProductByInnerSn.do")
    public void getProductByInnerSn(String innerSn, HttpServletResponse response, User user) throws IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        Integer oid = user.getOid();
        List<PdBase> pdBase = productService.getByInnerSnVague(oid, innerSn);
        map.put("pdBase", pdBase);
        ObjectToJson.objectToJson1(map, new String[]{"TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct", "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct","TPdCompositionViaParent"}, response);
    }

    /**
     * <AUTHOR>
     * @date 2016/10/13 14:33
     * 添加基本信息(销售对照)
     */
    @ResponseBody
    @RequestMapping("/addProduct.do")
    public void addProduct(User user, HttpServletResponse response, HttpServletRequest request) throws IOException, ParseException {
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd");
        Integer oid = user.getOid();
        
        Map<String, Object> map = new HashMap<String, Object>();
        String pdBase = request.getParameter("pdBase");
        String pdCusPro = request.getParameter("pdCusPro");
        JSONObject jsonBase = JSONObject.fromObject(pdBase);
        JSONObject jsonCustomerProduct = JSONObject.fromObject(pdCusPro);
        MtCategory category = materielService.getMtCategoryByOidCName(oid, "商品");
        SlCustomer slCustomer;

        if (!"".equals(pdBase) && !pdBase.equals("") || !"".equals(pdCusPro) && !pdCusPro.equals("")) {
            PdBase pdBase1 = productService.getByInnerSn(user.getOid(), jsonBase.optString("innerSn"));
            PdMerchandise pdMerchandise = new PdMerchandise();
            PdBase pdBase2 = new PdBase();
            if (pdBase1 == null) {
                pdBase2.setInnerSn(jsonBase.optString("innerSn"));
                pdBase2.setName(jsonBase.optString("name"));
                pdBase2.setUnit(jsonBase.optString("unit"));
                if (!jsonBase.optString("netWeight").equals("")&&!jsonBase.optString("weightUnit").equals("")) {
                    BigDecimal a=UnitUnit.toMilligram(new BigDecimal(jsonBase.get("netWeight")==null?"0":jsonBase.getString("netWeight")),jsonBase.optString("weightUnit"));

                    pdBase2.setNetWeight(UnitUnit.milligramToBest(a));
                    pdBase2.setWeightUnit(UnitUnit.milligramToUnit(a));
                }
                pdBase2.setModel(jsonBase.optString("model"));
                pdBase2.setSpecifications(jsonBase.optString("specifications"));
                pdBase2.setOid(user.getOid());
                pdBase2.setCreateName(user.getUserName());
                pdBase2.setCreator(user.getUserID());
                pdBase2.setCreateDate(new Date());
                pdBase2.setType("1");
                pdBase2.setMinimumiStock(new BigDecimal(jsonCustomerProduct.optString("minimumStock")));
                productService.addPdBase(pdBase2);
                pdMerchandise.setProduct(pdBase2);
                map.put("pdBase", pdBase2);

                //初始化物料
                MtBase mtbase = new MtBase();
                mtbase.setProduct(pdBase2);
                for (MtCategory mc : category.getMtCategoryHashSet()) {
                    if ("待分类".equals(mc.getName())) {
                        mtbase.setCategory(mc);//分类外键
                    }
                }
                materielService.saveMtBase(mtbase);

                MtBase mtBase = new MtBase();
                mtBase.setProduct(pdBase2);
                mtBase.setName(pdBase2.getName());
                mtBase.setCode(pdBase2.getInnerSn());
                MtCategory category1 = materielService.getMtCategoryByOidCName(oid, "半成品");
                for (MtCategory mc : category1.getMtCategoryHashSet()) {
                    if ("待分类".equals(mc.getName())) {
                        mtBase.setCategory(mc);//分类外键
                    }
                }
                materielService.saveMtBase(mtBase);
            } else {
                pdBase1.setMinimumiStock(new BigDecimal(jsonCustomerProduct.optString("minimumStock")));
                productService.updatePdBase(pdBase1);
                pdMerchandise.setProduct(pdBase1);
                map.put("pdBase", pdBase1);
            }
            //1.48新字段

            pdMerchandise.setProcessDept(jsonCustomerProduct.optInt("processDept"));
            pdMerchandise.setProcessDeptName(jsonCustomerProduct.optString("processDeptName"));
            pdMerchandise.setTechnicalPrincipal(jsonCustomerProduct.optString("technicalPrincipal"));
            pdMerchandise.setTechnicalPrincipalName(jsonCustomerProduct.optString("technicalPrincipalName"));
            pdMerchandise.setPhrase(jsonCustomerProduct.optString("phrase"));
            pdMerchandise.setContractSn(jsonCustomerProduct.optString("contractSn"));
            pdMerchandise.setIsContract(jsonCustomerProduct.optString("isContract"));
            pdMerchandise.setIsContain(jsonCustomerProduct.optString("isContain"));

            if(StringUtils.isNotBlank(jsonCustomerProduct.optString("expirationDate")))
                pdMerchandise.setExpirationDate(sd.parse(jsonCustomerProduct.optString("expirationDate")));
            if(StringUtils.isNotBlank(jsonCustomerProduct.optString("signatureDate")))
                pdMerchandise.setSignatureDate(sd.parse(jsonCustomerProduct.optString("signatureDate")));
            pdMerchandise.setHasInvoice(jsonCustomerProduct.optString("hasInvoice"));
            pdMerchandise.setInvoiceCategory(jsonCustomerProduct.optString("invoiceCategory"));
            pdMerchandise.setIsFreight(jsonCustomerProduct.optString("isFreight"));
            pdMerchandise.setMinimumStock(new BigDecimal(jsonCustomerProduct.optString("minimumStock")));

            pdMerchandise.setOuterSn(jsonCustomerProduct.optString("outerSn"));
            pdMerchandise.setInnerSn(jsonBase.optString("innerSn"));
            pdMerchandise.setOuterName(jsonCustomerProduct.optString("outerName"));
            if(jsonCustomerProduct.optInt("customerId")==0) {//customerId=0 未选择下拉框 否则已选择
                pdMerchandise.setCustomerName(jsonCustomerProduct.optString("customerName"));
                slCustomer =new SlCustomer();
                slCustomer.setOid(oid);
                slCustomer.setCategory("2");//2为新用户
                slCustomer.setCreateDate(new Date());
                slCustomer.setPeroid(Integer.valueOf(DateUtil.getDy(slCustomer.getCreateDate())));
                slCustomer.setCreateName(user.getUserName());
                slCustomer.setCreator(user.getUserID());
                slCustomer.setName(jsonCustomerProduct.optString("customerName"));
                slCustomer.setCode(jsonCustomerProduct.optString("code"));
                slCustomer.setType("1");
                slCustomer.setPrincipal(user.getUserID());
                slCustomer.setPrincipalName(user.getUserName());
                pdCustomerService.addPdCustomer(slCustomer);
                pdMerchandise.setCustomer(slCustomer);

            }else{
                slCustomer =pdCustomerService.getPdCustomerByIdAndOid(oid,jsonCustomerProduct.optInt("customerId"));
                if(slCustomer!=null){
                    pdMerchandise.setCustomerName(slCustomer.getName());
                    pdMerchandise.setCustomer(slCustomer);
                }

            }

            pdMerchandise.setMinimumStock(new BigDecimal(jsonCustomerProduct.optString("minimumStock")));

            if (!jsonCustomerProduct.optString("unitPrice").equals("")) {
                pdMerchandise.setUnitPrice(BigDecimal.valueOf(jsonCustomerProduct.getDouble("unitPrice")));
            }
            if (!jsonCustomerProduct.optString("taxRate").equals("")) {
                pdMerchandise.setTaxRate(BigDecimal.valueOf(jsonCustomerProduct.getDouble("taxRate")));
            }
            if (!jsonCustomerProduct.optString("unitPriceNotax").equals("")) {
                pdMerchandise.setUnitPriceNotax(BigDecimal.valueOf(jsonCustomerProduct.getDouble("unitPriceNotax")));
            }
            pdMerchandise.setMemo(jsonCustomerProduct.optString("memo"));
            pdMerchandise.setCreateName(user.getUserName());
            pdMerchandise.setCreator(user.getUserID());
            Timestamp date = new Timestamp(System.currentTimeMillis());
            pdMerchandise.setCreateDate(date);
            pdMerchandise.setPeroid(Integer.valueOf(DateUtil.getDy(pdMerchandise.getCreateDate())));
            productService.addPdMerchandise(pdMerchandise);

            map.put("pdCustomerProduct", pdMerchandise);
            map.put("status", 1);


            new SubjectUtils().addSubject(user,null,category,"",subjectSelectService,subjectSettingService,oid,null,pdBase1==null?pdBase2:pdBase1,userMessageService,userService,slCustomer,productService);
        } else {
            map.put("status", 0);
        }
        ObjectToJson.objectToJson1(map, new String[]{"customer", "product", "TPdMerchandiseHistoryPdMerchandiseViaProductCustomer","slOrdersItemHashSet",
                "TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct",
                "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct", "TPdCompositionViaParent"}, response);
    }

    /**
     *<AUTHOR>
     *@date 2017/3/1 11:38
     *批量添加商品基本信息
    */
    public static void addBatchProduct(List<PdMerchandise> ps,User user,
                                       ProductService productService,MaterielService materielService,
                                       PdCustomerService pdCustomerService
    ) throws IOException {
        Map<String,Object> map = new HashMap<>();
        Integer oid = user.getOid();
        
        if (ps.size()>0){
            for (PdMerchandise pdMerchandise:ps) {
                if (pdMerchandise!=null){
                    PdBase pdBase1 = productService.getByInnerSn(user.getOid(), pdMerchandise.getInnerSn());
                    PdMerchandise pdMerchandise1 = new PdMerchandise();
                    PdBase pdBase2 = new PdBase();
                    if (pdBase1==null){
                        pdBase2.setInnerSn(pdMerchandise.getInnerSn());
                        pdBase2.setName(pdMerchandise.getcInnerSnName());  //内部名称
                        pdBase2.setUnit(pdMerchandise.getUnit());
                        if (pdMerchandise.getNetWeight()!=null) {
                            pdBase2.setNetWeight(pdMerchandise.getNetWeight());
                        }
                        pdBase2.setModel(pdMerchandise.getModel());
                        pdBase2.setSpecifications(pdMerchandise.getSpecifications());
                        pdBase2.setOid(user.getOid());
                        pdBase2.setCreateName(user.getUserName());
                        pdBase2.setCreator(user.getUserID());
                        pdBase2.setCreateDate(new Date());
                        pdBase2.setType("1");
                        pdBase2.setMinimumiStock(pdMerchandise.getMinimumStock());
                        productService.addPdBase(pdBase2);
                        pdMerchandise.setProduct(pdBase2);
                        map.put("pdBase", pdBase2);

                        //初始化物料
                        MtBase mtbase = new MtBase();
                        mtbase.setProduct(pdBase2);
                        MtCategory category = materielService.getMtCategoryByOidCName(oid, "商品");
                        for (MtCategory mc : category.getMtCategoryHashSet()) {
                            if ("待分类".equals(mc.getName())) {
                                mtbase.setCategory(mc);//分类外键
                            }
                        }
                        materielService.saveMtBase(mtbase);

                        MtBase mtBase = new MtBase();
                        mtBase.setProduct(pdBase2);
                        MtCategory category1 = materielService.getMtCategoryByOidCName(oid, "半成品");
                        for (MtCategory mc : category1.getMtCategoryHashSet()) {
                            if ("待分类".equals(mc.getName())) {
                                mtBase.setCategory(mc);//分类外键
                            }
                        }
                        materielService.saveMtBase(mtBase);
                    }else {
                        pdBase1.setMinimumiStock(pdMerchandise.getMinimumStock());
                        productService.updatePdBase(pdBase1);
                        pdMerchandise.setProduct(pdBase1);
                        map.put("pdBase", pdBase1);
                    }
                    pdMerchandise1.setOuterSn(pdMerchandise.getOuterSn());
                    pdMerchandise1.setInnerSn(pdMerchandise.getInnerSn());
                    pdMerchandise1.setOuterName(pdMerchandise.getOuterName());
                    pdMerchandise1.setProduct(pdMerchandise.getProduct());
                    if(pdMerchandise.getCustomer()==null) { //customerId=0 未选择下拉框 否则已选择
                        pdMerchandise1.setCustomerName(pdMerchandise.getCustomerName());
                        SlCustomer slCustomer =new SlCustomer();
                        slCustomer.setOid(oid);
                        slCustomer.setCategory("2");//2为新用户
                        slCustomer.setCreateDate(new Date());
                        slCustomer.setPeroid(Integer.valueOf(DateUtil.getDy(slCustomer.getCreateDate())));
                        slCustomer.setCreateName(user.getUserName());
                        slCustomer.setCreator(user.getUserID());
                        slCustomer.setName(pdMerchandise.getCustomerName());
                        pdCustomerService.addPdCustomer(slCustomer);
                        pdMerchandise.setCustomer(slCustomer);
                }else {
                        SlCustomer slCustomer =pdCustomerService.getPdCustomerByIdAndOid(oid,pdMerchandise.getCustomer_());
                        pdMerchandise1.setCustomerName(slCustomer.getName());
                        pdMerchandise1.setCustomer(slCustomer);
                    }
                    pdMerchandise1.setMinimumStock(pdMerchandise.getMinimumStock());
                    pdMerchandise1.setOuterPackingMode(pdMerchandise.getOuterPackingMode());
                    pdMerchandise1.setOuterPackingMaterial(pdMerchandise.getOuterPackingMaterial());
                    if (pdMerchandise.getOuterPackingAmount()!=null) {
                        pdMerchandise1.setOuterPackingAmount(pdMerchandise.getOuterPackingAmount());
                    }
                    if (pdMerchandise.getUnitPrice()!=null) {
                        pdMerchandise1.setUnitPrice(pdMerchandise.getUnitPrice());
                    }
                    if (pdMerchandise.getTaxRate()!=null) {
                        pdMerchandise1.setTaxRate(pdMerchandise.getTaxRate());
                    }
                    if (pdMerchandise.getUnitPriceNotax()!=null) {
                        pdMerchandise1.setUnitPriceNotax(pdMerchandise.getUnitPriceNotax());
                    }
                    pdMerchandise1.setMiniPackingMode(pdMerchandise.getMiniPackingMode());
                    pdMerchandise1.setMiniPackingMaterial(pdMerchandise.getMiniPackingMaterial());
                    if (pdMerchandise.getMiniPackingAmount()!=null) {
                        pdMerchandise1.setMiniPackingAmount(pdMerchandise.getMiniPackingAmount());
                    }
                    pdMerchandise1.setMemo(pdMerchandise.getMemo());
                    pdMerchandise1.setCreateName(user.getUserName());
                    pdMerchandise1.setCreator(user.getUserID());
                    Timestamp date = new Timestamp(System.currentTimeMillis());
                    pdMerchandise1.setCreateDate(date);
                    pdMerchandise1.setPeroid(Integer.valueOf(DateUtil.getDy(pdMerchandise1.getCreateDate())));
                    productService.addPdMerchandise(pdMerchandise1);

                    map.put("pdCustomerProduct", pdMerchandise1);
                    map.put("status", 1);
                }
            }
        }else {
            map.put("status",0);
        }
    }

    /**
     * <AUTHOR>
     * @date 2016/10/13 16:15
     * 更新基本信息
     */
    @ResponseBody
    @RequestMapping("/updateProduct.do")
    public void updateProduct(HttpServletResponse response, HttpServletRequest request, User user) throws IOException, ParseException {
        SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd");
        String pdBase = request.getParameter("pdBase");
        String pdMerchandise = request.getParameter("pdCusPro");
        JSONObject jsonBase = JSONObject.fromObject(pdBase);
        JSONObject jsonCustomerProduct = JSONObject.fromObject(pdMerchandise);
        Map<String, Object> map = new HashMap<String, Object>();
        
        Integer oid = user.getOid();
        if (pdBase != null || pdMerchandise != null) {
            PdBase pdBase1 = productService.getByInnerSn(user.getOid(), jsonBase.optString("innerSn"));
            PdBase pdBase2 = productService.getById(jsonBase.optInt("pdId"));
            Integer cusProId = jsonCustomerProduct.optInt("customerId");

            PdMerchandise pdMerchandise1 = new PdMerchandise();
            if(pdBase1!=null)
                    pdMerchandise1 = productService.getByCusProIdAndProId(jsonBase.optInt("id"), pdBase1.getId());




            if (pdBase1 == null) {
                pdBase2.setInnerSn(jsonBase.optString("innerSn"));
                pdBase2.setName(jsonBase.optString("name"));
                pdBase2.setUnit(jsonBase.optString("unit"));
                if (!jsonBase.optString("netWeight").equals("")&&!jsonBase.optString("weightUnit").equals("")) {
                    BigDecimal a=UnitUnit.toMilligram(new BigDecimal(jsonBase.get("netWeight")==null?"0":jsonBase.getString("netWeight")),jsonBase.optString("weightUnit"));

                    pdBase2.setNetWeight(UnitUnit.milligramToBest(a));
                    pdBase2.setWeightUnit(UnitUnit.milligramToUnit(a));
                }
                pdBase2.setModel(jsonBase.optString("model"));
                pdBase2.setSpecifications(jsonBase.optString("specifications"));
                pdBase2.setUpdateName(user.getUserName());
                pdBase2.setUpdateDate(new Date());
                pdBase2.setUpdator(user.getUserID());
                pdBase2.setMinimumiStock(BigDecimal.valueOf(jsonCustomerProduct.optDouble("minimumStock")));
                productService.updatePdBase(pdBase2);
                //map.put("pdBase", pdBase2);

                pdMerchandise1 = productService.getByCusProIdAndProId(jsonBase.optInt("id"), pdBase2.getId());
            } else {
                pdBase1.setInnerSn(jsonBase.optString("innerSn"));
                pdBase1.setName(jsonBase.optString("name"));
                pdBase1.setUnit(jsonBase.optString("unit"));
                if (!jsonBase.optString("netWeight").equals("")&&!jsonBase.optString("weightUnit").equals("")) {
                    BigDecimal a=UnitUnit.toMilligram(new BigDecimal(jsonBase.get("netWeight")==null?"0":jsonBase.getString("netWeight")),jsonBase.optString("weightUnit"));

                    pdBase1.setNetWeight(UnitUnit.milligramToBest(a));
                    pdBase1.setWeightUnit(UnitUnit.milligramToUnit(a));
                }
                pdBase1.setModel(jsonBase.optString("model"));
                pdBase1.setSpecifications(jsonBase.optString("specifications"));
                pdBase1.setUpdateName(user.getUserName());
                pdBase1.setUpdateDate(new Date());
                pdBase1.setUpdator(user.getUserID());
                pdBase1.setMinimumiStock(BigDecimal.valueOf(jsonCustomerProduct.optDouble("minimumStock")));
                productService.updatePdBase(pdBase1);
                //map.put("pdBase", pdBase1);
                pdMerchandise1.setProduct(pdBase1);
            }
            pdMerchandise1.setInnerSn(jsonCustomerProduct.optString("innerSn"));
            pdMerchandise1.setOuterSn(jsonCustomerProduct.optString("outerSn"));

            pdMerchandise1.setContractSn(jsonCustomerProduct.optString("contractSn"));
            //添加修改历史
            PdMerchandiseHistory pcph=new PdMerchandiseHistory();
            pcph.setProductCustomer(pdMerchandise1.getId());
            pcph.setProduct(pdMerchandise1.getProduct_());
            pcph.setInnerSn(pdMerchandise1.getInnerSn());
            pcph.setOuterSn(pdMerchandise1.getOuterSn());
            pcph.setOuterName(pdMerchandise1.getOuterName());
            pcph.setCustomer(pdMerchandise1.getCustomer_());
            pcph.setCustomerName(pdMerchandise1.getCustomerName());
            pcph.setMiniPackingMode(pdMerchandise1.getMiniPackingMode());
            pcph.setMiniPackingMaterial(pdMerchandise1.getMiniPackingMaterial());
            pcph.setMiniPackingAmount(pdMerchandise1.getMiniPackingAmount());
            pcph.setOuterPackingMode(pdMerchandise1.getOuterPackingMode());
            pcph.setOuterPackingMaterial(pdMerchandise1.getOuterPackingMaterial());
            pcph.setOuterPackingAmount(pdMerchandise1.getOuterPackingAmount());
            pcph.setUnitPrice(pdMerchandise1.getUnitPrice());
            pcph.setTaxRate(pdMerchandise1.getTaxRate());
            pcph.setUnitPriceNotax(pdMerchandise1.getUnitPriceNotax());
            pcph.setContractSn(pdMerchandise1.getContractSn());
            pcph.setMemo(pdMerchandise1.getMemo());
            pcph.setCreator(user.getUserID());
            pcph.setCreateName(user.getUserName());
            pcph.setCreateDate(new Date());
            pcph.setMinimumStock(pdMerchandise1.getMinimumStock());

            productService.addPdMerchandiseHistory(pcph);


            if(jsonCustomerProduct.optDouble("minimumStock")!=pdMerchandise1.getMinimumStock().doubleValue()){
                MtBase mtBase = materielService.getMtBaseByProduct(pdMerchandise1.getProduct_());

                MtStockInfo mtStockInfo = mtStockService.getMtStockInfoByMId(mtBase.getId());
                MtStockInfoHistory mtSH=new MtStockInfoHistory();
                if(mtStockInfo!=null){
                    mtSH.setStockInfo(mtStockInfo.getId());
                }
                mtSH.setMaterial(pdMerchandise1.getId());
                mtSH.setInitialStock(new BigDecimal(0));
                mtSH.setMinimumStock(pdMerchandise1.getMinimumStock());
                mtSH.setAfterMinimumStock(new BigDecimal(jsonCustomerProduct.optString("minimumStock","0")));
                mtSH.setAfterInitialStock(new BigDecimal(0));
                mtSH.setCreator(user.getUserID());
                mtSH.setCreateName(user.getUserName());
                mtSH.setCreateDate(new Date());
                stockInfoHistoryService.saveMtStockInfoHistory(mtSH);

                pdMerchandise1.setMinimumStock(BigDecimal.valueOf(jsonCustomerProduct.optDouble("minimumStock")));

            }

            //1.48新字段

            pdMerchandise1.setProcessDept(jsonCustomerProduct.optInt("processDept"));
            pdMerchandise1.setProcessDeptName(jsonCustomerProduct.optString("processDeptName"));
            pdMerchandise1.setTechnicalPrincipal(jsonCustomerProduct.optString("technicalPrincipal"));
            pdMerchandise1.setTechnicalPrincipalName(jsonCustomerProduct.optString("technicalPrincipalName"));
            pdMerchandise1.setPhrase(jsonCustomerProduct.optString("phrase"));
            pdMerchandise1.setIsContract(jsonCustomerProduct.optString("isContract"));
            pdMerchandise1.setIsContain(jsonCustomerProduct.optString("isContain"));

            if(StringUtils.isNotBlank(jsonCustomerProduct.optString("expirationDate")))
                pdMerchandise1.setExpirationDate(sd.parse(jsonCustomerProduct.optString("expirationDate")));
            if(StringUtils.isNotBlank(jsonCustomerProduct.optString("signatureDate")))
                pdMerchandise1.setSignatureDate(sd.parse(jsonCustomerProduct.optString("signatureDate")));
            pdMerchandise1.setHasInvoice(jsonCustomerProduct.optString("hasInvoice"));
            pdMerchandise1.setInvoiceCategory(jsonCustomerProduct.optString("invoiceCategory"));
            pdMerchandise1.setIsFreight(jsonCustomerProduct.optString("isFreight"));
            pdMerchandise1.setMinimumStock(BigDecimal.valueOf(jsonCustomerProduct.optDouble("minimumStock")));


            pdMerchandise1.setOuterSn(jsonCustomerProduct.optString("outerSn"));
            pdMerchandise1.setInnerSn(jsonBase.optString("innerSn"));
            pdMerchandise1.setOuterName(jsonCustomerProduct.optString("outerName"));


            if(jsonCustomerProduct.optInt("customerId")==0) {//customerId=0 未选择下拉框 否则已选择
                pdMerchandise1.setCustomerName(jsonCustomerProduct.optString("customerName"));
                SlCustomer slCustomer =new SlCustomer();
                slCustomer.setOid(oid);
                slCustomer.setCategory("2");//2为新用户
                slCustomer.setCreateDate(new Date());
                slCustomer.setPeroid(Integer.valueOf(DateUtil.getDy(slCustomer.getCreateDate())));
                slCustomer.setCreateName(user.getUserName());
                slCustomer.setCreator(user.getUserID());
                slCustomer.setName(jsonCustomerProduct.optString("customerName"));
                slCustomer.setType("1");
                pdCustomerService.addPdCustomer(slCustomer);
                pdMerchandise1.setCustomer(slCustomer);

            }else{
                SlCustomer slCustomer =pdCustomerService.getPdCustomerByIdAndOid(oid,jsonCustomerProduct.optInt("customerId"));
                pdMerchandise1.setCustomerName(slCustomer.getName());
                pdMerchandise1.setCustomer(slCustomer);
            }
            pdMerchandise1.setOuterPackingMode(jsonCustomerProduct.optString("outerPackingMode"));
            pdMerchandise1.setOuterPackingMaterial(jsonCustomerProduct.optString("outerPackingMaterial"));
            pdMerchandise1.setOuterPackingAmount(jsonCustomerProduct.optLong("outerPackingAmount"));

            if(jsonCustomerProduct.optDouble("unitPrice",*********)!=*********)
            pdMerchandise1.setUnitPrice(new BigDecimal(jsonCustomerProduct.optDouble("unitPrice")));
            if(jsonCustomerProduct.optDouble("taxRate",*********)!=*********)
            pdMerchandise1.setTaxRate(new BigDecimal(jsonCustomerProduct.optDouble("taxRate")));
            if(jsonCustomerProduct.optDouble("unitPriceNotax",*********)!=*********)
            pdMerchandise1.setUnitPriceNotax(new BigDecimal(jsonCustomerProduct.optDouble("unitPriceNotax")));

            pdMerchandise1.setOuterName(jsonCustomerProduct.optString("outerName"));
            pdMerchandise1.setMiniPackingMode(jsonCustomerProduct.optString("miniPackingMode"));
            pdMerchandise1.setMiniPackingMaterial(jsonCustomerProduct.optString("miniPackingMaterial"));
            pdMerchandise1.setMiniPackingAmount(jsonCustomerProduct.optLong("miniPackingAmount"));
            pdMerchandise1.setMemo(jsonCustomerProduct.optString("memo"));
            pdMerchandise1.setUpdateName(user.getUserName());
            pdMerchandise1.setUpdator(user.getUserID());
            Timestamp date = new Timestamp(System.currentTimeMillis());
            pdMerchandise1.setUpdateDate(date);
            productService.updatePdMerchandise(pdMerchandise1);
            //map.put("pdMerchandise", pdMerchandise1);
            map.put("status", 1);
        } else {
            map.put("status", 0);
        }
        ObjectToJson.objectToJson1(map, new String[]{}, response);
    }

    //查询工序信息列表
    /*
    **
    *<AUTHOR>
    *@Date 2016/10/12 16:31
    */
    @ResponseBody
    @RequestMapping("/getProcess.do")
    public void getProcess(String innerSn, HttpServletResponse response, Integer currPage, Integer pageSize, User user) throws IOException {

        Integer oid = user.getOid();
        Map<String, Object> map = new HashMap<String, Object>();
        List<PdProcess> processList = pdProcessService.findListPdProcessAndBaseThreeProperty(innerSn, oid);
        for (PdProcess pdProcess : processList) {

            Integer productID = pdProcess.getProduct_();
            if (productID != null) {
                PdBase pdBase = pdProcessService.getById(productID);
                pdProcess.setProductName(pdBase.getName());
                pdProcess.setNetWeight(pdBase.getNetWeight());
                pdProcess.setProComposition(pdBase.getComposition());

            }


        }


        if (currPage == null || "".equals(currPage) || currPage <= 0) {
            currPage = 1;//默认当前页
        }
        int totalPage = 1;//默认总页数

        if (pageSize == null || "".equals(pageSize) || pageSize <= 0) {
           /* pageSize=30;*/
            map.put("pageNumber", currPage);//当前页数
            map.put("totalPage", totalPage);//总页数
            map.put("Fylist", processList);//分页列表
            ObjectToJson.objectToJson1(map, new String[]{"product", "sn", "pdComposition", "packagingInstructor", "processInstructor", "craftInstructor", "creator"
                    , "createName", "createDate", "updator", "updateName", "updateDate", "TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct", "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct"}, response);

        }


        int maxResult = currPage * pageSize;//显示最大结果
        int minResult = (currPage - 1) * pageSize;//初始化结果
        int total = processList.size();
            /*totalPage = (total + pageSize - 1) / pageSize;//计算总页数*/

            /*
            * 计算总页数
            * */
        double total1 = total;
        double pageSize1 = pageSize;
        double num = total1 / pageSize1;
        double totalPage1 = Math.ceil(num);
        totalPage = (int) totalPage1;

        if (total > 0) {
            List list = new ArrayList();
            for (int i = minResult; i < maxResult; i++) {
                if (i < total) {
                    list.add(processList.get(i));
                }
            }
            map.put("totalPage", totalPage);//总页数
            map.put("pageNumber", currPage);//当前页数
            map.put("Fylist", list);//分页列表
            ObjectToJson.objectToJson1(map, new String[]{"product", "sn", "pdComposition", "packagingInstructor", "processInstructor", "craftInstructor", "creator"
                    , "createName", "createDate", "updator", "updateName", "updateDate", "TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct", "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct"}, response);
        } else {
            totalPage = 1;

            map.put("pageNumber", currPage);//当前页数
            map.put("totalPage", totalPage);//总页数
            map.put("Fylist", processList);//分页列表
            ObjectToJson.objectToJson1(map, new String[]{"product", "sn", "pdComposition", "packagingInstructor", "processInstructor", "craftInstructor", "creator"
                    , "createName", "createDate", "updator", "updateName", "updateDate", "TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct", "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct"}, response);

        }


    }

    /*
    **
    *<AUTHOR>
    *@Date 2016/10/13 10:11
    * 查询详细工序信息
    */
    @ResponseBody
    @RequestMapping("/getPartProcess.do")
    public void getPartProcess(HttpServletResponse response, User user, Integer id, Integer productID) throws IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        PdBase pdBases = pdProcessService.getById(productID);
        Integer oid = user.getOid();
        List list = pdProcessService.findPdProcessAndBaseDetail(id, oid);
        map.put("pdBases", pdBases);
        map.put("pdProcessDetail", list);
        ObjectToJson.objectToJson1(map, new String[]{"product", "sn", "pdComposition", "creator"
                , "createName", "createDate", "updator", "updateName", "updateDate", "TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct", "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct","TPdCompositionViaParent","product"}, response);
    }

//
//    /*
//    **
//    *<AUTHOR>
//    *@Date 2016/10/13 10:15
//    * 更新工序信息
//    */
//    @ResponseBody
//    @RequestMapping("/updatePartProcess.do")
//    public void updatePartProcess(HttpServletResponse response, PdProcess pdProcess,User user,Integer id) throws IOException {
//        Map<String,Object> map = new HashMap<String, Object>();
//    /*    Integer oid=user.getOid();*/
//        if((id!=null&&!"".equals(id))&&pdProcess!=null) {
//            PdProcess pdProcess1 = pdProcessService.getPdProcessById(id);
//            
//            pdProcess1.setUpdateDate(Timestamp.valueOf(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())));
//            pdProcess1.setUpdateName(user.getUserName());
//            pdProcess1.setUpdator(user.getUserID());
////            pdProcess1.setInnerSn(pdProcess.getInnerSn());//内部图号
//            pdProcess1.setName(pdProcess.getName());//工序名称
//            pdProcess1.setCavityPerModel(pdProcess.getCavityPerModel());
//            pdProcess1.setUnitConsumption(pdProcess.getUnitConsumption());
//            pdProcess1.setMateriaUtilization(pdProcess.getMateriaUtilization());
//            pdProcess1.setLossQuato(pdProcess.getLossQuato());
//            pdProcess1.setRejectionRateQuato(pdProcess.getRejectionRateQuato());
//            pdProcess1.setCraftInstructor(pdProcess.getCraftInstructor());
//            pdProcess1.setProcessInstructor(pdProcess.getProcessInstructor());
//            pdProcess1.setPackagingInstructor(pdProcess.getPackagingInstructor());
//            pdProcess1.setMemo(pdProcess.getMemo());
//            pdProcessService.updatePdProcess(pdProcess1);
//
//            map.put("status",1);
//            map.put("Process",pdProcess1);
//        }else{
//            map.put("status",0);
//
//        }
//
//        ObjectToJson.objectToJson1(map,new String[]{"product","pdComposition"},response);
//
//    }

    /**
     * <AUTHOR>
     * @Date 2016/10/13 14:19
     * 获取商品构成主页数据
     */
    @ResponseBody
    @RequestMapping("/getPdComposition.do")
    public void getPdComposition(User user, String innerSn, Integer pageNumber, Integer quantum, HttpServletResponse response) throws IOException {
        Integer oid = user.getOid();
        List<PdBase> pdBases = pdCompositionService.getPdBase(oid, innerSn);

        List<PdBase> list = new ArrayList<PdBase>();
        for (PdBase p : pdBases) {
            for (MtBase m : p.getTMtBaseViaProduct()) {
                MtCategory mtCategory = materielService.getMtCategoryById(m.getCategory().getFirstGradeId());
                if ("商品".equals(m.getCategory().getName())) {
                    list.add(p);
                } else if ("商品".equals(mtCategory.getName())) {
                    list.add(p);
                }
            }
        }

        List<PdBase> pdBaseList = new ArrayList<>();

        int max = pageNumber * quantum;
        int min = pageNumber * quantum - quantum;
        int total = list.size();
        for (int i = min; i < max; i++) {
            if (i < total)
                pdBaseList.add(list.get(i));
        }

        int totalPage = (total + quantum - 1) / quantum;//计算总页数
        Map<String, Object> map = new HashMap<>();
        map.put("pdBases", pdBaseList);
        map.put("pageNumber", pageNumber);
        map.put("quantum", quantum);
        map.put("totalPage", totalPage);
        ObjectToJson.objectToJson1(map, new String[]{"TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct", "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct", "TPdCompositionViaParent"}, response);
    }


//    /**
//     * <AUTHOR>
//     * @Date 2016/10/14 15:58
//     * 单个商品构成信息获取
//     */
//    @ResponseBody
//    @RequestMapping("/getCompositionByPdId.do")
//    public void getCompositionByPdId(Integer id, HttpServletResponse response) throws IOException {
//        Map<String, Object> map = new HashMap<>();
//        PdBase pdBase = productService.getById(id);
//        map.put("pdBase", pdBase);
//        List<PdComposition> compositionList = new ArrayList<PdComposition>();
//        List<PdComposition> con=pdCompositionService.getPdCompositionLists(pdBase.getId());
//        for (PdComposition c : con) {
//            if (c.getMaterial() != null) {
//                if (c.getMaterial().getCategory() != null) {
//                    c.setCategoryName(c.getMaterial().getCategory().getParent().getName());
//                } else {
//                    c.setCategoryName("");
//                }
//            } else {
//                if (c.getProduct() != null) {
//                    if ("4".equals(pdBase.getType())) {
//                        c.setCategoryName("外购成品");
//                    }
//                    if ("5".equals(pdBase.getType())) {
//                        c.setCategoryName("包装材料");
//                    }
//                }
//            }
//            compositionList.add(c);
//        }
//        map.put("compositionList", compositionList);
//
//        ObjectToJson.objectToJson1(map, new String[]{"TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct", "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct", "parent", "TPdCompositionPdCompositionViaTPId", "TPdProcessPdCompositionViaId", "TPdCompositionViaParent", "material"}, response);
//    }

    /**
     * <AUTHOR>
     * @Date 2016/10/17 15:10
     * 删除商品的够成成分
     */
    @ResponseBody
    @RequestMapping("/deleteCompositionById.do")
    public void deleteCompositionById(Integer id, HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<>();
        if (id != null) {
            PdComposition pdComposition = pdCompositionService.getPdCompositionById(id);
            pdCompositionService.deletePdComposition(pdComposition);
            map.put("status", 1);

        } else {
            map.put("status", 0);
        }
        ObjectToJson.objectToJson1(map, new String[]{}, response);
    }

//    /**
//     * <AUTHOR>
//     * @Date 2016/10/17 15:28
//     * 编辑构成信息的保存接口
//     */
//    @ResponseBody
//    @RequestMapping("/updatePdBaseById.do")
//    public void updatePdBaseById(Integer id,String source,String composition,String stuff,String memo,String type,String innerSn, Integer compositionId, HttpServletResponse response, User user) throws IOException {
//        Integer oid = user.getOid();
//        Map<String, Object> map = new HashMap<>();
//        if (id!= null) {
//            PdBase base = productService.getById(id);
//            base.setSource(source);//来源
//            base.setComposition(composition);//构成
//            base.setStuff(stuff);//材料
//            base.setMemo(memo);//备注
//            if ("2".equals(composition) || "3".equals(composition)) {
//                base.setType("3");//分类  3-半成品
//            } else {
//                base.setType(type);//分类   4-外购成品 /5-包装材料
//            }
//            productService.updatePdBase(base);
//
//            //source=1 认为是外购的
//            if (source.equals("1")) {
//                MtCategory category = new MtCategory();
//                //如果此商品在物料中对应数据为空执行新增
//                if (base.getTMtBaseViaProduct().size() <= 0) {
//                    MtBase mBase = new MtBase();
//                    if ("4".equals(type)) {
//                        category = materielService.getMtCategoryByOidCName(oid, "外购成品");
//                        for (MtCategory mc : category.getMtCategoryHashSet()) {
//                            if ("待分类".equals(mc.getName())) {
//                                category = mc;
//                            }
//                        }
//                    } else {
//                        //否则为5包装材料
//                        category = materielService.getMtCategoryByOidCName(oid, "包装材料");
//                        for (MtCategory mc : category.getMtCategoryHashSet()) {
//                            if ("待分类".equals(mc.getName())) {
//                                category = mc;
//                            }
//                        }
//                        mBase.setCode(innerSn);
//                    }
//                    mBase.setCategory(category);
//                    mBase.setProduct(base);
//                    materielService.saveMtBase(mBase);//新增
//                } else {
//                    if ("4".equals(type)) {
//                        category = materielService.getMtCategoryByOidCName(oid, "外购成品");
//                        for (MtCategory mc : category.getMtCategoryHashSet()) {
//                            if ("待分类".equals(mc.getName())) {
//                                category = mc;
//                            }
//                        }
//                    } else {
//                        //否则为5
//                        category = materielService.getMtCategoryByOidCName(oid, "包装材料");
//                        for (MtCategory mc : category.getMtCategoryHashSet()) {
//                            if ("待分类".equals(mc.getName())) {
//                                category = mc;
//                            }
//                        }
//                    }
//                    for (MtBase m : base.getTMtBaseViaProduct()) {
//                        MtBase mBase = materielService.getMtBaseById(m.getId());
//                        mBase.setCategory(category);
//                        materielService.updateMtBase(mBase);//编辑
//                    }
//                }
//
//            } else {
//                //否则认为是2,自制的
//                MtCategory category = new MtCategory();
//                //如果此商品在物料中对应数据为空执行新增
//                if (base.getTMtBaseViaProduct().size() <= 0) {
//                    MtBase mBase = new MtBase();
//                    //否则为3半成品
//                    category = materielService.getMtCategoryByOidCName(oid, "半成品");
//                    for (MtCategory mc : category.getMtCategoryHashSet()) {
//                        if ("待分类".equals(mc.getName())) {
//                            category = mc;
//                        }
//                    }
//                    mBase.setCategory(category);
//                    mBase.setProduct(base);
//                    materielService.saveMtBase(mBase);//新增
//                } else {
//                    //否则为3半成品
//
//                    category = materielService.getMtCategoryByOidCName(oid, "半成品");
//                    for (MtCategory mc : category.getMtCategoryHashSet()) {
//                        if ("待分类".equals(mc.getName())) {
//                            category = mc;
//                        }
//                    }
//                    for (MtBase m : base.getTMtBaseViaProduct()) {
//                        MtBase mBase = materielService.getMtBaseById(m.getId());
//
//                        mBase.setCategory(category);
//                        materielService.updateMtBase(mBase);//编辑
//                    }
//                }
//            }
//
//            map.put("status", 1);
//            map.put("pdBase", base);
//        } else if (compositionId != null) {
//            PdComposition pdComposition = pdCompositionService.getPdCompositionById(compositionId);
//            if (pdComposition.getProduct() == null) {
//                MtBase mbase = materielService.getMtBaseById(pdComposition.getMaterial().getId());
//
//                //否则为未分类的才进行分类，新增
//                PdBase base = new PdBase();
//                base.setSpecifications(mbase.getSpecifications());
//                base.setUnit(mbase.getUnit());
//                base.setInnerSn(mbase.getCode());
//                base.setName(mbase.getName());
//                base.setModel(mbase.getModel());
//                base.setSource(source);//来源
//                base.setComposition(composition);//构成
//                base.setStuff(stuff);//材料
//                base.setMemo(memo);//备注
//                base.setType(type);//分类
//                productService.addPdBase(base);
//
//                mbase.setProduct(base);
//                MtCategory category = new MtCategory();
//                if ("3".equals(type)) {
//                    category = materielService.getMtCategoryByOidCName(oid, "半成品");
//                    for (MtCategory mc : category.getMtCategoryHashSet()) {
//                        if ("待分类".equals(mc.getName())) {
//                            category = mc;
//                            pdComposition.setCategoryName("半成品");
//                        }
//                    }
//                    pdComposition.setSource("2");
//                    pdComposition.setComposition("3");
//                }
//                if ("4".equals(type)) {
//                    category = materielService.getMtCategoryByOidCName(oid, "外购成品");
//                    for (MtCategory mc : category.getMtCategoryHashSet()) {
//                        if ("待分类".equals(mc.getName())) {
//                            category = mc;
//                            pdComposition.setCategoryName("外购成品");
//
//                        }
//                    }
//                    pdComposition.setSource("1");
//                    pdComposition.setComposition("1");
//                }
//                if ("5".equals(type)) {
//                    category = materielService.getMtCategoryByOidCName(oid, "包装材料");
//                    for (MtCategory mc : category.getMtCategoryHashSet()) {
//                        if ("待分类".equals(mc.getName())) {
//                            category = mc;
//                            pdComposition.setCategoryName("包装材料");
//
//                        }
//                    }
//                    pdComposition.setSource("1");
//                    pdComposition.setComposition("1");
//                }
//                mbase.setCategory(category);
//                materielService.updateMtBase(mbase);
//
//                pdComposition.setMaterial(null);
//                pdComposition.setProduct(base);
//                map.put("pdBase", base);
//            } else {
//                PdBase base = productService.getById(pdComposition.getProduct_());
//                base.setSource(source);//来源
//                base.setComposition(composition);//构成
//                base.setStuff(stuff);//材料
//                base.setMemo(memo);//备注
//                base.setType(type);//分类
//                productService.updatePdBase(base);
//
//                for (MtBase m : base.getTMtBaseViaProduct()) {
//                    MtCategory category = new MtCategory();
//                    if ("3".equals(type)) {
//                        category = materielService.getMtCategoryByOidCName(oid, "半成品");
//                        for (MtCategory mc : category.getMtCategoryHashSet()) {
//                            if ("待分类".equals(mc.getName())) {
//                                category = mc;
//                                pdComposition.setCategoryName("半成品");
//                            }
//                        }
//                        pdComposition.setSource("2");
//                        pdComposition.setComposition("3");
//                    }
//                    if ("4".equals(type)) {
//                        category = materielService.getMtCategoryByOidCName(oid, "外购成品");
//                        for (MtCategory mc : category.getMtCategoryHashSet()) {
//                            if ("待分类".equals(mc.getName())) {
//                                category = mc;
//                                pdComposition.setCategoryName("外购成品");
//
//                            }
//                        }
//                        pdComposition.setSource("1");
//                        pdComposition.setComposition("1");
//                    }
//                    if ("5".equals(type)) {
//                        category = materielService.getMtCategoryByOidCName(oid, "包装材料");
//                        for (MtCategory mc : category.getMtCategoryHashSet()) {
//                            if ("待分类".equals(mc.getName())) {
//                                category = mc;
//                                pdComposition.setCategoryName("包装材料");
//
//                            }
//                        }
//                        pdComposition.setSource("1");
//                        pdComposition.setComposition("1");
//                    }
//                    m.setCategory(category);
//                    materielService.updateMtBase(m);
//                }
//                map.put("pdBase", base);
//            }
//            pdCompositionService.updatePdComposition(pdComposition);
//
//            map.put("status", 1);
////            map.put("composition",composition);
//        } else {
//            map.put("status", 0);
//        }
//        ObjectToJson.objectToJson1(map, new String[]{"TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct", "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct", "product", "material", "parent", "TPdCompositionPdCompositionViaTPId", "TPdProcessPdCompositionViaId", "TPdCompositionViaParent"}, response);
//    }

//    /**
//     * <AUTHOR>
//     * @Date 2016/10/18 9:09
//     * 商品录入原辅材料接口
//     */
//    @ResponseBody
//    @RequestMapping("/addRawMaterial.do")
//    public void addRawMaterial(String code, Integer id, String name, String specifications, String model, String unit, String matching, Integer ptBaseId, User user, String categoryName, HttpServletResponse response) throws IOException {
//        Map<String, Object> map = new HashMap<>();
//        if (!"".equals(name) && name != null && categoryName != null && !"".equals(categoryName) && !"".equals(code) && code != null) {
//            Integer oid = user.getOid();
//            PdBase pBase = productService.getById(ptBaseId);//商品
//
//            if (id != null) {
//                MtBase mBase = materielService.getMtBaseById(id);
//
//                PdComposition pdComposition = new PdComposition();
//                pdComposition.setMaterial(mBase);//物料外键
//                pdComposition.setParent(pBase);//被构成商品外键
//                pdComposition.setName(mBase.getName());
//                pdComposition.setInnerSn(mBase.getCode());
//                pdComposition.setModel(mBase.getModel());
//                pdComposition.setSpecifications(mBase.getSpecifications());
//                pdComposition.setSource("2");
//                pdComposition.setComposition("2");
//                pdComposition.setMatching(matching);
//                pdCompositionService.savePdComposition(pdComposition);
//
//                mBase.setCategoryName(mBase.getCategory().getParent().getName());
//                map.put("status", 1);
//                map.put("mtBase", mBase);
//                map.put("composition", pdComposition);
//
//            } else {
//                List<MtBase> mbs = materielService.getMtBaseListByCodeAndName(oid, code,name);
//                if (mbs.size() <= 0) {
//
//                    MtBase mtBase = new MtBase();
//                    mtBase.setCode(code);
//                    mtBase.setSpecifications(specifications);
//                    mtBase.setModel(model);
//                    mtBase.setUnit(unit);
//                    mtBase.setMatching(matching);
//                    mtBase.setName(name);
//
//                    MtCategory category = materielService.getMtCategoryByOidCName(oid, categoryName);
//                    for (MtCategory mc : category.getMtCategoryHashSet()) {
//                        if ("待分类".equals(mc.getName())) {
//                            mtBase.setCategory(mc);//分类外键
//                        }
//                    }
//                    mtBase.setCategoryName(mtBase.getCategory().getParent().getName());
//                    materielService.saveMtBase(mtBase);
//
//                    PdComposition pdComposition = new PdComposition();
//                    pdComposition.setMaterial(mtBase);//物料外键
//                    pdComposition.setParent(pBase);//被构成商品外键
//                    pdComposition.setName(mtBase.getName());
//                    pdComposition.setInnerSn(mtBase.getCode());
//                    pdComposition.setSpecifications(mtBase.getSpecifications());
//                    pdComposition.setModel(mtBase.getModel());
//                    pdComposition.setSource("2");
//                    pdComposition.setComposition("2");
//                    pdComposition.setMatching(mtBase.getMatching());
//                    pdCompositionService.savePdComposition(pdComposition);
//
//                    map.put("status", 1);
//                    map.put("mtBase", mtBase);
//                    map.put("composition", pdComposition);
//
//                } else {
//                    map.put("status", 2);//代号已经存在，不可重复录入
//                }
//            }
//
//        } else {
//            map.put("status", 0);
//        }
//        ObjectToJson.objectToJson1(map, new String[]{"parent", "org", "mtBaseHashSet", "mtCategoryHashSet", "category", "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet", "TPdCompositionViaParent", "TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct", "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct", "product", "material", "parent", "TPdCompositionPdCompositionViaTPId", "TPdProcessPdCompositionViaId", "TPdCompositionViaParent"}, response);
//
//    }

//    /**
//     * <AUTHOR>
//     * @Date 2016/10/18 15:53
//     * 商品构成录入半成品接口
//     */
//    @ResponseBody
//    @RequestMapping("/addBanChengPin.do")
//    public void addBanChengPin(PdBase pdBase, Integer ptBaseId, User user, HttpServletResponse response) throws IOException {
//        Map<String, Object> map = new HashMap<>();
//        if (!"".equals(pdBase.getName()) && pdBase.getName() != null && !"".equals(pdBase.getInnerSn()) && pdBase.getInnerSn() != null) {
//            Integer oid = user.getOid();
//            PdBase pBase = productService.getById(ptBaseId);//商品
//
//
//            if (pdBase.getId() != null) {
//                PdBase base = productService.getById(pdBase.getId());//成分商品或者半成品
//
//                PdComposition pdComposition = new PdComposition();
//                pdComposition.setProduct(base);//成分商品外键
//                pdComposition.setParent(pBase);//被构成商品外键
//                pdCompositionService.savePdComposition(pdComposition);
//
//
//                MtBase mtBase = new MtBase();
//                mtBase.setProduct(base);
//                MtCategory category = materielService.getMtCategoryByOidCName(oid, "半成品");
//                for (MtCategory mc : category.getMtCategoryHashSet()) {
//                    if ("待分类".equals(mc.getName())) {
//                        mtBase.setCategory(mc);//分类外键
//                    }
//                }
//                if ("1".equals(base.getType())) {
//                    if (base.getTMtBaseViaProduct().size() <= 1) {
//                        //如果此商品在物料表里外键小于等于1，说明之前只是商品，在物料表里新增此半成品
//                        materielService.saveMtBase(mtBase);
//
//                        base.setType("2");//即是商品又是半成品-2
//                        productService.updatePdBase(base);//更新成分商品
//                    }
//                }
//                if ("3".equals(base.getType())) {
//                    if (base.getTMtBaseViaProduct().size() <= 0) {
//                        //如果此商品在物料表里外键小于等于0，说明只是半成品,在物料表里新增次半成品
//                        materielService.saveMtBase(mtBase);
//                    }
//                }
//
//                map.put("status", 1);
//                map.put("pdBase", base);
//                map.put("composition", pdComposition);
//
//            } else {
//                //否则只是半成品
//                List<PdBase> pbs = pdCompositionService.getPdBase(oid, pdBase.getInnerSn());
//                if (pbs.size() <= 0) {
//                    //内部图号可用，新增半成品
//                    pdBase.setType("3");//半成品-3
//                    productService.addPdBase(pdBase);
//
//                    PdComposition pdComposition = new PdComposition();
//                    pdComposition.setProduct(pdBase);//成分商品外键
//                    pdComposition.setParent(pBase);//被构成商品外键
//                    pdCompositionService.savePdComposition(pdComposition);
//
//                    MtBase mtBase = new MtBase();//如果此商品在物料表里外键小于等于0，说明只是半成品
//                    mtBase.setProduct(pdBase);
//                    MtCategory category = materielService.getMtCategoryByOidCName(oid, "半成品");
//                    for (MtCategory mc : category.getMtCategoryHashSet()) {
//                        if ("待分类".equals(mc.getName())) {
//                            mtBase.setCategory(mc);//分类外键
//                        }
//                    }
//                    materielService.saveMtBase(mtBase);
//
//                    map.put("status", 1);
//                    map.put("pdBase", pdBase);
//                    map.put("composition", pdComposition);
//
//                } else {
//                    map.put("status", 2);//内部图号已经存在，不可重复录入
//                }
//            }
//        } else {
//            map.put("status", 0);
//        }
//        ObjectToJson.objectToJson1(map, new String[]{"parent", "org", "mtBaseHashSet", "mtCategoryHashSet", "category", "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet", "TPdCompositionViaParent", "TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct", "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct", "product", "material", "parent", "TPdCompositionPdCompositionViaTPId", "TPdProcessPdCompositionViaId", "TPdCompositionViaParent"}, response);
//
//    }

    /**
     * <AUTHOR>
     * @Date 2016/10/18 16:41
     * 根据物料代号检索物料信息的接口
     */
    @ResponseBody
    @RequestMapping("/getMtBaseByCode.do")
    public void getMtBaseByCode(String code, User user, HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        Integer oid = user.getOid();
        List<MtBase> mtBases = materielService.getMtBaseByOidAndCode(oid, code);
        List<MtBase> mtBaseList=new ArrayList<MtBase>();
        for (MtBase m:mtBases){
            MtCategory mtCategory=materielService.getMtCategoryById(m.getCategory().getFirstGradeId());
            if ("构成商品的原辅材料".equals(mtCategory.getName())){
                mtBaseList.add(m);
            }
        }
        map.put("mtBases", mtBaseList);
        ObjectToJson.objectToJson1(map, new String[]{"parent", "org", "mtBaseHashSet", "mtCategoryHashSet", "category", "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet", "TPdCompositionViaParent", "TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct", "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct", "product", "material", "parent", "TPdCompositionPdCompositionViaTPId", "TPdProcessPdCompositionViaId", "TPdCompositionViaParent"
                , "TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct"
                , "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct", "TPdCompositionViaParent", "product", "category", "mtQualityMtBaseViaId"
                , "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet", "PdCompositionMaterialHashSet", "pdCompositionMaterialHashSet"}, response);
    }


//    /**
//     * <AUTHOR>
//     * @Date 2016/10/21 15:59
//     * 查询商品构成中单个原辅材料信息
//     */
//    @ResponseBody
//    @RequestMapping("/getOneRawMaterialById.do")
//    public void getOneRawMaterialById(Integer compositionId, HttpServletResponse response) throws IOException {
//        PdComposition composition = pdCompositionService.getPdCompositionById(compositionId);
//        MtBase mtBase = materielService.getMtBaseById(composition.getMaterial().getId());
//        mtBase.setCategoryName(mtBase.getCategory().getName());
//        Map<String, Object> map = new HashMap<String, Object>();
//        map.put("composition", composition);//构成关系
//        map.put("maBase", mtBase);//原辅材料信息
//        ObjectToJson.objectToJson1(map, new String[]{"parent", "org", "mtBaseHashSet", "mtCategoryHashSet", "category", "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet", "TPdCompositionViaParent", "TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct", "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct", "product", "material", "parent", "TPdCompositionPdCompositionViaTPId", "TPdProcessPdCompositionViaId", "TPdCompositionViaParent"}, response);
//
//    }


//    /**
//     * <AUTHOR>
//     * @Date 2016/10/21 15:46
//     * 修改商品录入的原辅材料接口
//     */
//    @ResponseBody
//    @RequestMapping("updateRawMaterial.do")
//    public void updateRawMaterial(String name,String specifications,String model,String matching, Integer compositionId, HttpServletResponse response) throws IOException {
//        Map<String, Object> map = new HashMap<String, Object>();
//        if (compositionId != null) {
//            PdComposition composition = pdCompositionService.getPdCompositionById(compositionId);
//            MtBase base = materielService.getMtBaseById(composition.getMaterial_());
//            base.setName(name);
//            base.setSpecifications(specifications);
//            base.setModel(model);
//            base.setMatching(matching);
//            materielService.updateMtBase(base);
//
//            composition.setName(name);
//            composition.setSource("2");
//            composition.setComposition("2");
//            composition.setMatching(matching);
//            composition.setModel(model);
//            composition.setSpecifications(specifications);
//            composition.setCategoryName("原辅材料");
//            pdCompositionService.updatePdComposition(composition);
//            map.put("status", 1);
//            map.put("mtBase", base);
//            map.put("composition", composition);
//        } else {
//            map.put("status", 0);
//        }
////        ObjectToJson.objectToJson1(map,new String[]{"parent","org","mtBaseHashSet","mtCategoryHashSet","category","mtQualityMtBaseViaId","mtStockInfoHashSet","mtSupplierHistoryHashSet","mtSupplierMaterialHashSet","TPdCompositionViaParent","TPdBaseHistoryPdBaseViaProduct","TPdCompositionPdBaseViaProduct","TPdMerchandisePdBaseViaProduct","TPdProcessPdBaseViaProduct","TMtBaseViaProduct","product","material","parent","TPdCompositionPdCompositionViaTPId","TPdProcessPdCompositionViaId","TPdCompositionViaParent"},response);
//        ObjectToJson.objectToJson1(map, new String[]{"product", "category", "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet", "PdCompositionMaterialHashSet", "mtSupplierHistoryHashSet", "product", "material", "parent", "TPdProcessPdCompositionViaId", "TMtStockTMtBaseViaMaterial"}, response);
//
//    }

//    /**
//     * <AUTHOR>
//     * @Date 2016/10/21 16:07
//     * 删除商品构成中原辅材料接口
//     */
//    @ResponseBody
//    @RequestMapping("/deleteRawMaterialById.do")
//    public void deleteRawMaterialById(Integer compositionId, HttpServletResponse response) throws IOException {
//        Map<String, Object> map = new HashMap<String, Object>();
//        if (compositionId != null) {
//            PdComposition composition = pdCompositionService.getPdCompositionById(compositionId);
//
//            MtBase mtBase = materielService.getMtBaseById(composition.getMaterial().getId());//查出此构成关系中的原辅材料
//
//            pdCompositionService.deletePdComposition(composition);//删除构成关系
//
//            if (mtBase.getMtStockInfoHashSet().size() <= 0 && mtBase.getMtSupplierMaterialHashSet().size() <= 0 && mtBase.getMtQualityMtBaseViaId().size() <= 0 && mtBase.getPdCompositionMaterialHashSet().size() <= 0 && mtBase.getMtSupplierHistoryHashSet().size() <= 0) {
//                materielService.deleteMtBase(mtBase);//如果此原辅材料没有其他用处，则删除此原辅材料
//            }
//            map.put("status", 1);
//        } else {
//            map.put("status", 0);
//        }
//        ObjectToJson.objectToJson1(map, new String[]{"parent", "org", "mtBaseHashSet", "mtCategoryHashSet", "category", "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet", "TPdCompositionViaParent"}, response);
//
//    }


//    /**
//    * <AUTHOR>
//    * @Date 2016/10/24 15:19
//    * 拆分商品或半成品接口
//    */
//    @ResponseBody
//    @RequestMapping("/splitCommodity.do")
//    public void splitCommodity(Integer pdBaseId,MtBase mtBase,Integer type,Integer amount,User user,HttpServletResponse response) throws IOException {
//        Map<String, Object> map = new HashMap<String, Object>();
//
//        if (pdBaseId!=null) {
//            PdBase pBase = productService.getById(pdBaseId);
//
//            if (mtBase.getId() != null) {
//                //type  1-mtBase， 2-pdBase
//                if (type == 1) {
//                    MtBase mBase = materielService.getMtBaseById(mtBase.getId());//将检索出的物料赋给mBase
//                    PdComposition pdComposition = new PdComposition();
//                    pdComposition.setParent(pBase);//被构成商品的外键
//                    pdComposition.setAmount(amount);//装配数量
//                    pdComposition.setMaterial(mBase);//物料外键
//                    pdComposition.setName(mBase.getName());
//                    pdComposition.setInnerSn(mBase.getCode());
//                    pdComposition.setModel(mBase.getModel());
//                    pdComposition.setSpecifications(mBase.getSpecifications());
//                    pdComposition.setUnit(mBase.getUnit());
//                    if ("包装材料".equals(mBase.getCategory().getParent().getName())){
//                        pdComposition.setSource("1");
//                        pdComposition.setComposition("1");
//                    }else {
//                        pdComposition.setSource("2");
//                        pdComposition.setComposition("3");
//                    }
//
//                    pdCompositionService.savePdComposition(pdComposition);
//                    map.put("composition", pdComposition);//构成关系放入map
//                    map.put("mtBase", mBase);//物料信息放入map
//                    map.put("mtCategory",mBase.getCategory().getParent().getName());
//                }
//                if (type == 2) {
//                    PdBase ppBase = productService.getById(mtBase.getId());//将检索出的商品赋给ppBase
//                    PdComposition pdComposition = new PdComposition();
//                    pdComposition.setParent(pBase);//被构成商品的外键
//                    pdComposition.setAmount(amount);//装配数量
//                    pdComposition.setProduct(ppBase);//商品外键
//                    pdComposition.setName(ppBase.getName());
//                    pdComposition.setInnerSn(ppBase.getInnerSn());
//                    pdComposition.setModel(ppBase.getModel());
//                    pdComposition.setSpecifications(ppBase.getSpecifications());
////                    pdComposition.setSource("2");
////                    pdComposition.setComposition("3");
//                    pdComposition.setSource(ppBase.getSource());
//                    pdComposition.setComposition(ppBase.getComposition());
//                    pdComposition.setUnit(ppBase.getUnit());
//                    String category="";
//
//                    if ("2".equals(ppBase.getType())||"3".equals(ppBase.getType())){
//                        category="半成品";
//                    }
//                    if ("4".equals(ppBase.getType())){
//                        category="外购成品";
//                    }
//                    if ("5".equals(ppBase.getType())){
//                        category="包装材料";
//                    }
//                    pdComposition.setCategoryName(category);
//                    pdCompositionService.savePdComposition(pdComposition);
//                    map.put("composition", pdComposition);//构成关系放入map
//                    map.put("ppBase", ppBase);//物料信息放入map
//
//                    map.put("mtCategory",category);
//                }
//            } else {
//                //否则新增此物料
//                MtBase mBase = new MtBase();
//                materielService.saveMtBase(mtBase);
//                mBase = mtBase;//把新增的物料赋给mBase
//                PdComposition pdComposition = new PdComposition();
//                pdComposition.setParent(pBase);//被构成商品的外键
//                pdComposition.setAmount(amount);//装配数量
//                pdComposition.setMaterial(mBase);//物料外键
//                pdComposition.setName(mBase.getName());
//                pdComposition.setInnerSn(mBase.getCode());
//                pdComposition.setModel(mBase.getModel());
//                pdComposition.setSpecifications(mBase.getSpecifications());
//                pdComposition.setUnit(mBase.getUnit());
//                pdCompositionService.savePdComposition(pdComposition);
//                map.put("composition", pdComposition);//构成关系放入map
//                map.put("mtBase", mBase);//物料信息放入map
////                map.put("mtCategory",mBase.getCategory().getName());
//            }
//            map.put("status",1);
//        }else {
//            map.put("status",0);
//        }
//
//        ObjectToJson.objectToJson1(map,new String[]{"parent","org","mtBaseHashSet","mtCategoryHashSet","category","mtQualityMtBaseViaId","mtStockInfoHashSet","mtSupplierHistoryHashSet","mtSupplierMaterialHashSet","TPdCompositionViaParent","TPdBaseHistoryPdBaseViaProduct","TPdCompositionPdBaseViaProduct","TPdMerchandisePdBaseViaProduct","TPdProcessPdBaseViaProduct","TMtBaseViaProduct","product","material","parent","TPdCompositionPdCompositionViaTPId","TPdProcessPdCompositionViaId","TPdCompositionViaParent"},response);
//
//    }


//    /**
//     * <AUTHOR>
//     * @Date 2016/10/24 15:19
//     * 拆分商品或半成品接口
//     */
//    @ResponseBody
//    @RequestMapping("/splitCommodity.do")
//    public void splitCommodity(Integer pdBaseId, String code, Integer id, String name, String specifications, String model, String unit, Integer type, Integer amount, HttpServletResponse response,HttpSession s
//    ) throws IOException {
//        Map<String, Object> map = new HashMap<String, Object>();
//        Integer oid = (Integer) s.getAttribute("oid");
//        if (pdBaseId != null) {
//            PdBase pBase = productService.getById(pdBaseId);
//
//            if (id != null) {
//                //type  1-mtBase， 2-pdBase
//                if (type == 1) {
//                    MtBase mBase = materielService.getMtBaseById(id);//将检索出的物料赋给mBase
//                    PdComposition pdComposition = new PdComposition();
//                    pdComposition.setParent(pBase);//被构成商品的外键
//                    pdComposition.setAmount(amount);//装配数量
//                    pdComposition.setMaterial(mBase);//物料外键
//                    pdComposition.setName(mBase.getName());
//                    pdComposition.setInnerSn(mBase.getCode());
//                    pdComposition.setModel(mBase.getModel());
//                    pdComposition.setSpecifications(mBase.getSpecifications());
//                    pdComposition.setUnit(mBase.getUnit());
//                    if ("包装材料".equals(mBase.getCategory().getParent().getName())) {
//                        pdComposition.setSource("1");
//                        pdComposition.setComposition("1");
//                    } else {
//                        pdComposition.setSource("2");
//                        pdComposition.setComposition("3");
//                    }
//
//                    pdCompositionService.savePdComposition(pdComposition);
//                    map.put("composition", pdComposition);//构成关系放入map
//                    map.put("mtBase", mBase);//物料信息放入map
//                    map.put("mtCategory", mBase.getCategory().getParent().getName());
//                }
//                if (type == 2) {
//                    PdBase ppBase = productService.getById(id);//将检索出的商品赋给ppBase
//                    PdComposition pdComposition = new PdComposition();
//                    pdComposition.setParent(pBase);//被构成商品的外键
//                    pdComposition.setAmount(amount);//装配数量
//                    pdComposition.setProduct(ppBase);//商品外键
//                    pdComposition.setName(ppBase.getName());
//                    pdComposition.setInnerSn(ppBase.getInnerSn());
//                    pdComposition.setModel(ppBase.getModel());
//                    pdComposition.setSpecifications(ppBase.getSpecifications());
////                    pdComposition.setSource("2");
////                    pdComposition.setComposition("3");
//                    pdComposition.setSource(ppBase.getSource());
//                    pdComposition.setComposition(ppBase.getComposition());
//                    pdComposition.setUnit(ppBase.getUnit());
//                    String category = "";
//
//                    if ("2".equals(ppBase.getType()) || "3".equals(ppBase.getType())) {
//                        category = "半成品";
//                    }
//                    if ("4".equals(ppBase.getType())) {
//                        category = "外购成品";
//                    }
//                    if ("5".equals(ppBase.getType())) {
//                        category = "包装材料";
//                    }
//                    pdComposition.setCategoryName(category);
//                    pdCompositionService.savePdComposition(pdComposition);
//                    map.put("composition", pdComposition);//构成关系放入map
//                    map.put("ppBase", ppBase);//物料信息放入map
//
//                    map.put("mtCategory", category);
//                }
//            } else {
//
//                List<MtBase> mbs = materielService.getMtBaseListByCodeAndName(oid, code,name);
//                if (mbs.size() <= 0) {
//                    MtCategory category = materielService.getMtCategoryByOidCName(oid,"半成品");
//                    List<MtCategory> mtCategories = materielService.getMtCategoryByOidAndPid(oid,category.getId());
//
//                    for(MtCategory c : mtCategories){
//                        if("待分类".equals(c.getName()))
//                            category = c;
//                    }
//                    //否则新增此物料
//                    MtBase mBase = new MtBase();
//                    mBase.setCode(code);
//                    mBase.setName(name);
//                    mBase.setSpecifications(specifications);
//                    mBase.setModel(model);
//                    mBase.setUnit(unit);
//                    mBase.setCategory(category);
//                    materielService.saveMtBase(mBase);
//
//                    PdComposition pdComposition = new PdComposition();
//                    pdComposition.setParent(pBase);//被构成商品的外键
//                    pdComposition.setAmount(amount);//装配数量
//                    pdComposition.setMaterial(mBase);//物料外键
//                    pdComposition.setName(mBase.getName());
//                    pdComposition.setInnerSn(mBase.getCode());
//                    pdComposition.setModel(mBase.getModel());
//                    pdComposition.setSpecifications(mBase.getSpecifications());
//                    pdComposition.setUnit(mBase.getUnit());
//                    pdCompositionService.savePdComposition(pdComposition);
//                    map.put("composition", pdComposition);//构成关系放入map
//                    map.put("mtBase", mBase);//物料信息放入map
//                }else {
//                    map.put("status", 2);
//                    ObjectToJson.objectToJson1(map, new String[]{"parent", "org", "mtBaseHashSet", "mtCategoryHashSet", "category", "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet", "TPdCompositionViaParent", "TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct", "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct", "product", "material", "parent", "TPdCompositionPdCompositionViaTPId", "TPdProcessPdCompositionViaId", "TPdCompositionViaParent"}, response);
//                    return;
//                }
//
////                map.put("mtCategory",mBase.getCategory().getName());
//            }
//            map.put("status", 1);
//        } else {
//            map.put("status", 0);
//        }
//
//        ObjectToJson.objectToJson1(map, new String[]{"parent", "org", "mtBaseHashSet", "mtCategoryHashSet", "category", "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet", "TPdCompositionViaParent", "TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct", "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct", "product", "material", "parent", "TPdCompositionPdCompositionViaTPId", "TPdProcessPdCompositionViaId", "TPdCompositionViaParent"}, response);
//
//    }

    /**
     * <AUTHOR>
     * @Date 2016/10/26 9:50
     * 检索内部图号和材料代号
     */
    @ResponseBody
    @RequestMapping("/retrievalByInnerSnAndCode.do")
    public void retrievalByInnerSnAndCode(String code, User user, HttpServletResponse response) throws IOException {
        Integer oid = user.getOid();
        List<MtBase> mtBase = materielService.getMtBaseByOidAndCode(oid, code);
        List<MtBase> mtBaseList = new ArrayList<MtBase>();
        for (MtBase m : mtBase) {
            if ("包装材料".equals(m.getCategory().getParent().getName()) || "半成品".equals(m.getCategory().getParent().getName()) || "外购成品".equals(m.getCategory().getParent().getName())) {
                mtBaseList.add(m);
            }
        }

        List<PdBase> pdBase = pdCompositionService.getPdBase(oid, code);

        Map<String, Object> map = new HashMap<String, Object>();
        map.put("mtBase", mtBaseList);
        map.put("pdBase", pdBase);
        ObjectToJson.objectToJson1(map, new String[]{"parent", "org", "mtBaseHashSet", "mtCategoryHashSet", "category", "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet", "TPdCompositionViaParent", "TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct", "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct", "product", "material", "parent", "TPdCompositionPdCompositionViaTPId", "TPdProcessPdCompositionViaId", "TPdCompositionViaParent"
                , "TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct"
                , "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct", "TPdCompositionViaParent", "product", "category", "mtQualityMtBaseViaId"
                , "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet", "PdCompositionMaterialHashSet", "pdCompositionMaterialHashSet","slOrdersItemHashSet","pdPackHashSet"}, response);
    }
//
//
//    /**
//     * <AUTHOR>
//     * @Date 2016/11/1 13:38
//     * 商品构成装配时编辑页面保存的接口
//     */
//    @ResponseBody
//    @RequestMapping("/updateSplitCommodity.do")
//    public void updateSplitCommodity(Integer compositionId, Integer id, String name, String specifications, String unit, String model, Integer type, Integer amount, HttpServletResponse response) throws IOException {
//        Map<String, Object> map = new HashMap<String, Object>();
//
//        if (compositionId != null) {
//            PdComposition pdComposition = pdCompositionService.getPdCompositionById(compositionId);
//
//            if (id != null) {
//                //type  1-mtBase， 2-pdBase
//                if (type == 1) {
//                    MtBase mBase = materielService.getMtBaseById(pdComposition.getMaterial_());//将物料赋给mBase
//                    mBase.setName(name);
//                    mBase.setSpecifications(specifications);
//                    mBase.setUnit(unit);
//                    mBase.setModel(model);
//                    materielService.updateMtBase(mBase);
//
//                    map.put("mtBase", mBase);//物料信息放入map
//                    pdComposition.setName(mBase.getName());
//                    pdComposition.setInnerSn(mBase.getCode());
//                    pdComposition.setModel(mBase.getModel());
//                    pdComposition.setSpecifications(mBase.getSpecifications());
//                }
//                if (type == 2) {
//                    PdBase ppBase = productService.getById(id);//将商品赋给ppBase
//                    ppBase.setName(name);
//                    ppBase.setSpecifications(specifications);
//                    ppBase.setUnit(unit);
//                    ppBase.setModel(model);
//                    productService.updatePdBase(ppBase);
//
//                    map.put("pdBase", ppBase);//物料信息放入map
//                    pdComposition.setName(ppBase.getName());
//                    pdComposition.setModel(ppBase.getModel());
//                    pdComposition.setSpecifications(ppBase.getSpecifications());
//                    pdComposition.setInnerSn(ppBase.getInnerSn());
//                }
//            }
//            pdComposition.setSource("2");
//            pdComposition.setComposition("3");
//            pdComposition.setAmount(amount);//装配数量
//            pdCompositionService.updatePdComposition(pdComposition);
//            map.put("composition", pdComposition);//构成关系放入map
//            map.put("status", 1);
//
//        } else {
//            map.put("status", 0);
//        }
//
//        ObjectToJson.objectToJson1(map, new String[]{"parent", "org", "mtBaseHashSet", "mtCategoryHashSet", "category", "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet", "TPdCompositionViaParent", "TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct", "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct", "product", "material", "parent", "TPdCompositionPdCompositionViaTPId", "TPdProcessPdCompositionViaId", "TPdCompositionViaParent"}, response);
//
//    }

//
//    /**
//     * <AUTHOR>
//     * @Date 2016/11/2 13:33
//     * 根据被构成商品的id删除商品构成关系
//     */
//    @ResponseBody
//    @RequestMapping("/deleteCompositionByPdId.do")
//    public void deleteCompositionByPdId(User user, Integer goodsType, Integer type, String stuff, String memo, Integer compositionId, Integer pdBaseId, String source, String composition, HttpServletResponse response) throws IOException {
//        Integer oid = user.getOid();
//        Map<String, Object> map = new HashMap<String, Object>();
//        if (goodsType == 2) {
//            if (pdBaseId != null) {
//                PdBase pdBase = productService.getById(pdBaseId);
//                if (pdBase != null) {
//                    for (PdComposition com : pdBase.getTPdCompositionViaParent()) {
//                        PdComposition pdComposition = pdCompositionService.getPdCompositionById(com.getId());
//                        pdCompositionService.deletePdComposition(pdComposition);
//                    }
//                    pdBase.setSource(source);
//                    pdBase.setComposition(composition);
//                    pdBase.setStuff(stuff);//材料
//                    pdBase.setMemo(memo);//备注
//                    if ("2".equals(pdBase.getComposition()) || "3".equals(pdBase.getComposition()) || "".equals(pdBase.getComposition()) || pdBase.getComposition() == null) {
//                        pdBase.setType("3");//分类  3-半成品
//                    } else {
//                        pdBase.setType("4");//分类   4-外购成品 /5-包装材料
//                    }
//                    productService.updatePdBase(pdBase);
//                }
//                map.put("pdBaseId", pdBaseId);//把传入的id返回
//                map.put("status", 1);
//
//            } else {
//                map.put("status", 0);
//            }
//        } else {
//            if (compositionId != null) {
//                PdComposition pdComposition = pdCompositionService.getPdCompositionById(compositionId);
//                if (pdComposition.getMaterial() != null) {
//                    pdComposition.setName(pdComposition.getMaterial().getName());
//                    pdComposition.setInnerSn(pdComposition.getMaterial().getCode());
//                    pdComposition.setModel(pdComposition.getMaterial().getModel());
//                    pdComposition.setSpecifications(pdComposition.getMaterial().getSpecifications());
//                    pdComposition.setSource(source);
//                    pdComposition.setComposition(composition);
//
//                    PdBase pdBase = new PdBase();
//                    pdBase.setName(pdComposition.getMaterial().getName());
//                    pdBase.setInnerSn(pdComposition.getMaterial().getCode());
//                    pdBase.setModel(pdComposition.getMaterial().getModel());
//                    pdBase.setSpecifications(pdComposition.getMaterial().getSpecifications());
//                    pdBase.setSource(source);
//                    pdBase.setComposition(composition);
//                    pdBase.setStuff(stuff);//材料
//                    pdBase.setMemo(memo);//备注
//
//                    MtBase mtBase = materielService.getMtBaseById(pdComposition.getMaterial_());
//
//                    if ("2".equals(pdBase.getComposition()) || "3".equals(pdBase.getComposition()) || "".equals(pdBase.getComposition()) || pdBase.getComposition() == null) {
//                        pdBase.setType("3");//分类  3-半成品
//                        pdComposition.setCategoryName("半成品");
//
//                        MtCategory category = materielService.getMtCategoryByOidCName(oid, "半成品");
//                        for (MtCategory mc : category.getMtCategoryHashSet()) {
//                            if ("待分类".equals(mc.getName())) {
//                                mtBase.setCategory(mc);//分类外键
//                            }
//                        }
//
//                    } else {
//                        pdBase.setType(String.valueOf(type));//分类   4-外购成品 /5-包装材料
//                        if (type == 4) {
//                            MtCategory category = materielService.getMtCategoryByOidCName(oid, "外购成品");
//                            pdComposition.setCategoryName("外购成品");
//                            for (MtCategory mc : category.getMtCategoryHashSet()) {
//                                if ("待分类".equals(mc.getName())) {
//                                    mtBase.setCategory(mc);//分类外键
//                                }
//                            }
//                        } else {
//                            MtCategory category = materielService.getMtCategoryByOidCName(oid, "包装材料");
//                            pdComposition.setCategoryName("包装材料");
//                            for (MtCategory mc : category.getMtCategoryHashSet()) {
//                                if ("待分类".equals(mc.getName())) {
//                                    mtBase.setCategory(mc);//分类外键
//                                }
//                            }
//                        }
//                    }
//                    pdBase.setOid(mtBase.getCategory().getOrg_());
//                    productService.addPdBase(pdBase);
//
//                    mtBase.setProduct(pdBase);
//                    materielService.updateMtBase(mtBase);
//
//                    pdComposition.setMaterial(null);
//                    pdComposition.setProduct(pdBase);
//                    pdCompositionService.updatePdComposition(pdComposition);
//                    map.put("pdBaseId", pdBase.getId());//把新增的id返回
//                } else {
//                    PdBase pdBase = productService.getById(pdComposition.getProduct_());
//                    pdBase.setSource(source);
//                    pdBase.setComposition(composition);
//                    pdBase.setStuff(stuff);//材料
//                    pdBase.setMemo(memo);//备注
//                    productService.updatePdBase(pdBase);
//
//                    pdComposition.setName(pdBase.getName());
//                    pdComposition.setInnerSn(pdBase.getInnerSn());
//                    pdComposition.setModel(pdBase.getModel());
//                    pdComposition.setSpecifications(pdBase.getSpecifications());
//                    pdComposition.setSource(source);
//                    pdComposition.setComposition(composition);
//
//                    for (MtBase m : pdBase.getTMtBaseViaProduct()) {
//                        if ("2".equals(pdBase.getComposition()) || "3".equals(pdBase.getComposition()) || "".equals(pdBase.getComposition()) || pdBase.getComposition() == null) {
//                            pdBase.setType("3");//分类  3-半成品
//                            pdComposition.setCategoryName("半成品");
//
//                            MtCategory category = materielService.getMtCategoryByOidCName(oid, "半成品");
//                            for (MtCategory mc : category.getMtCategoryHashSet()) {
//                                if ("待分类".equals(mc.getName())) {
//                                    m.setCategory(mc);//分类外键
//                                }
//                            }
//                        } else {
//                            pdBase.setType(String.valueOf(type));//分类   4-外购成品 /5-包装材料
//                            if (type == 4) {
//                                MtCategory category = materielService.getMtCategoryByOidCName(oid, "外购成品");
//                                pdComposition.setCategoryName("外购成品");
//                                for (MtCategory mc : category.getMtCategoryHashSet()) {
//                                    if ("待分类".equals(mc.getName())) {
//                                        m.setCategory(mc);//分类外键
//                                    }
//                                }
//                            } else {
//                                MtCategory category = materielService.getMtCategoryByOidCName(oid, "包装材料");
//                                pdComposition.setCategoryName("包装材料");
//                                for (MtCategory mc : category.getMtCategoryHashSet()) {
//                                    if ("待分类".equals(mc.getName())) {
//                                        m.setCategory(mc);//分类外键
//                                    }
//                                }
//                            }
//                        }
//                        materielService.updateMtBase(m);
//
//                    }
//                    pdCompositionService.updatePdComposition(pdComposition);
//
//                    map.put("pdBaseId", pdBase.getId());//把商品表的id返回
//                }
//
//                map.put("status", 1);
//
//            } else {
//                map.put("status", 0);
//            }
//        }
//        ObjectToJson.objectToJson1(map, new String[]{}, response);
//    }
//
//
//    /**
//     * <AUTHOR>
//     * @Date 2016/11/4 14:40
//     * 打开第二层及多层构成编辑接口
//     */
//    @ResponseBody
//    @RequestMapping("/getSecondBaseByCompositionId.do")
//    public void getSecondBaseByCompositionId(Integer compositionId, HttpServletResponse response) throws IOException {
//        Map<String, Object> map = new HashMap<String, Object>();
//        PdComposition composition = pdCompositionService.getPdCompositionById(compositionId);
//        if (compositionId != null) {
//            if (composition.getMaterial() != null) {
//                map.put("mtBase", composition.getMaterial());
//            }
//            List<PdComposition> compositionList = new ArrayList<PdComposition>();
//            if (composition.getProduct() != null) {
//                PdBase pdBase = productService.getById(composition.getProduct().getId());
//                for (PdComposition c : pdBase.getTPdCompositionViaParent()) {
//                    if (c.getMaterial() != null) {
//                        if (c.getMaterial().getCategory() != null) {
//                            c.setCategoryName(c.getMaterial().getCategory().getName());
//                        } else {
//                            c.setCategoryName("");
//                        }
//                    } else {
//                        if (c.getProduct() != null) {
//                            if ("4".equals(pdBase.getType())) {
//                                c.setCategoryName("外购成品");
//                            }
//                            if ("5".equals(pdBase.getType())) {
//                                c.setCategoryName("包装材料");
//                            }
//                        }
//                    }
//                    compositionList.add(c);
//                }
//                map.put("pdBase", pdBase);
//            }
//            map.put("compositionList", compositionList);
//            map.put("status", 1);
//
//        } else {
//            map.put("status", 0);
//        }
//        ObjectToJson.objectToJson1(map, new String[]{"parent", "org", "mtBaseHashSet", "mtCategoryHashSet", "category", "mtQualityMtBaseViaId", "mtStockInfoHashSet", "mtSupplierHistoryHashSet", "mtSupplierMaterialHashSet", "TPdCompositionViaParent", "TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct", "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct", "product", "material", "parent", "TPdCompositionPdCompositionViaTPId", "TPdProcessPdCompositionViaId", "TPdCompositionViaParent","pdPackHashSet","slOrdersItemHashSet"}, response);
//
//    }


    /**
     * <AUTHOR>
     * @Date 2016/12/28 15:15
     * 工序信息
     */
    @ResponseBody
    @RequestMapping("/getProcessList.do")
    public void getProcessList(User user, String innerSn, Integer pageNumber, Integer quantum, HttpServletResponse response) throws IOException {
        Integer oid = user.getOid();
        Map<String, Object> map = new HashMap<String, Object>();
        List<PdBase> pdBaseList = new ArrayList<PdBase>();
        List<PdBase> pdBases = productService.getByInnerSnVague(oid, innerSn);
        for (PdBase p : pdBases) {
            if ("2".equals(p.getSource())) {
                pdBaseList.add(p);
            }
        }

        int total = 0;
        if (pdBaseList.size() > 0) {
            total = pdBaseList.size();
        }
        int totalPage;
        if (total != 0) {
            if (total % quantum == 0) {
                totalPage = total / quantum;
            } else {
                totalPage = total / quantum + 1;
            }
        } else {
            totalPage = 1;
        }
        List<PdBase> list = new ArrayList<PdBase>();

        if (pageNumber != 0 && quantum != 0 && total != 0) {
            int max = pageNumber * quantum;
            int min = pageNumber * quantum - quantum;
            for (int i = min; i < max; i++) {
                if (i < total) {
                    list.add(pdBaseList.get(i));
                }
            }
        }
        map.put("pageNumber", pageNumber);
        map.put("totalPage", totalPage);
        map.put("pdProcessListList", list);
        ObjectToJson.objectToJson1(map, new String[]{"TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct",
                "TMtBaseViaProduct", "TPdCompositionViaParent", "TPdMerchandisePdBaseViaProduct", "TMtBaseViaProduct"}, response);
    }


    /**
     * <AUTHOR>
     * @Date 2016/12/30 9:41
     * 编辑保存工序信息
     */
    @ResponseBody
    @RequestMapping("/updatePartProcess.do")
    public void updatePartProcess(User user, PdProcess pdProcess, Integer pdBaseId, HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<String, Object>();
        

        if (pdProcess.getId() != null && pdProcess.getId() != 0) {
            PdProcess pdProcess1 = pdProcessService.getPdProcessById(pdProcess.getId());
            pdProcess1.setUpdateDate(Timestamp.valueOf(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())));
            pdProcess1.setUpdateName(user.getUserName());
            pdProcess1.setUpdator(user.getUserID());
//            pdProcess1.setInnerSn(pdProcess.getInnerSn());//内部图号
            pdProcess1.setName(pdProcess.getName());//工序名称
            pdProcess1.setCavityPerModel(pdProcess.getCavityPerModel());
            pdProcess1.setUnitConsumption(pdProcess.getUnitConsumption());
            pdProcess1.setMateriaUtilization(pdProcess.getMateriaUtilization());
            pdProcess1.setLossQuato(pdProcess.getLossQuato());
            pdProcess1.setRejectionRateQuato(pdProcess.getRejectionRateQuato());
            pdProcess1.setCraftInstructor(pdProcess.getCraftInstructor());
            pdProcess1.setProcessInstructor(pdProcess.getProcessInstructor());
            pdProcess1.setPackagingInstructor(pdProcess.getPackagingInstructor());
            pdProcess1.setMemo(pdProcess.getMemo());
            pdProcessService.updatePdProcess(pdProcess1);

            map.put("status", 1);
            map.put("Process", pdProcess1);
        } else {
            PdProcess pdProcess1 = new PdProcess();
            PdBase base = productService.getById(pdBaseId);
            pdProcess1.setProduct(base);
            pdProcess1.setUpdateDate(Timestamp.valueOf(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())));
            pdProcess1.setUpdateName(user.getUserName());
            pdProcess1.setUpdator(user.getUserID());
            pdProcess1.setInnerSn(pdProcess.getInnerSn());//内部图号
            pdProcess1.setName(pdProcess.getName());//工序名称
            pdProcess1.setCavityPerModel(pdProcess.getCavityPerModel());
            pdProcess1.setUnitConsumption(pdProcess.getUnitConsumption());
            pdProcess1.setMateriaUtilization(pdProcess.getMateriaUtilization());
            pdProcess1.setLossQuato(pdProcess.getLossQuato());
            pdProcess1.setRejectionRateQuato(pdProcess.getRejectionRateQuato());
            pdProcess1.setCraftInstructor(pdProcess.getCraftInstructor());
            pdProcess1.setProcessInstructor(pdProcess.getProcessInstructor());
            pdProcess1.setPackagingInstructor(pdProcess.getPackagingInstructor());
            pdProcess1.setMemo(pdProcess.getMemo());
            pdProcessService.savePdProcess(pdProcess1);
            map.put("status", 1);
            map.put("Process", pdProcess1);

        }
        ObjectToJson.objectToJson1(map, new String[]{"product", "pdComposition"}, response);

    }

    /**
     * <AUTHOR>
     * @Date 2017/1/15 16:42
     * 第一级商品编辑构成保存接口
     */
    @ResponseBody
    @RequestMapping("/updateOnePdBaseById.do")
    public void updateOnePdBaseById(PdBase pdBase, Integer compositionId, HttpServletResponse response, User user) throws IOException {
        Integer oid = user.getOid();
        Map<String, Object> map = new HashMap<>();
        if (pdBase.getId() != null) {
            PdBase base = productService.getById(pdBase.getId());
            base.setSource(pdBase.getSource());//来源
            base.setComposition(pdBase.getComposition());//构成
            base.setStuff(pdBase.getStuff());//材料
            base.setMemo(pdBase.getMemo());//备注
            if ("2".equals(pdBase.getComposition()) || "3".equals(pdBase.getComposition())) {
                List<MtBase> mtBases = materielService.getMtBaseByProductId(base.getId());
                MtBase mtBase;
                if(mtBases!=null&&mtBases.size()>=2){

                    mtBase = mtBases.get(mtBases.size()-1);

                    MtCategory category = materielService.getMtCategoryByOidCName(oid, "半成品");

                    for (MtCategory mc : category.getMtCategoryHashSet()) {
                        if ("待分类".equals(mc.getName())) {
                            mtBase.setCategory(mc);//分类外键
                        }
                    }
                    materielService.updateMtBase(mtBase);
                }
                base.setType("3");//分类  3-半成品

            } else {
                if(pdBase.getSource().equals("1")){
                    base.setType("4");//分类   4-外购成品 /5-包装材料
                    base.setComposition("4");

                    List<MtBase> mtBases = materielService.getMtBaseByProductId(base.getId());

                    MtBase mtBase;
                    if(mtBases!=null&&mtBases.size()>=2){

                        mtBase = mtBases.get(mtBases.size()-1);

                        MtCategory category = materielService.getMtCategoryByOidCName(oid, "外购成品");

                        for (MtCategory mc : category.getMtCategoryHashSet()) {
                            if ("待分类".equals(mc.getName())) {
                                mtBase.setCategory(mc);//分类外键
                            }
                        }
                        materielService.updateMtBase(mtBase);
                    }
                }
            }
            productService.updatePdBase(base);
            map.put("status", 1);
            map.put("pdBase", base);
        } else {
            map.put("status", 0);
        }
        ObjectToJson.objectToJson1(map, new String[]{"TPdBaseHistoryPdBaseViaProduct", "TPdCompositionPdBaseViaProduct", "TPdMerchandisePdBaseViaProduct", "TPdProcessPdBaseViaProduct", "TMtBaseViaProduct", "product", "material", "parent", "TPdCompositionPdCompositionViaTPId", "TPdProcessPdCompositionViaId", "TPdCompositionViaParent","TMtBaseViaProduct","tMtBaseViaProduct","TPdCompositionViaParent","tPdCompositionViaParent","TPdBaseHistoryPdBaseViaProduct","TPdCompositionPdBaseViaProduct","TPdMerchandisePdBaseViaProduct","TPdProcessPdBaseViaProduct","TMtBaseViaProduct","TPdCompositionViaParent"}, response);
    }
}