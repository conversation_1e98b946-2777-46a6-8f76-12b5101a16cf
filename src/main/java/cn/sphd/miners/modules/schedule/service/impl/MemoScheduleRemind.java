package cn.sphd.miners.modules.schedule.service.impl;

import cn.sphd.miners.modules.schedule.service.MemoScheduleService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.socket.service.DelayCallback;
import org.springframework.context.ApplicationContext;

import java.util.Date;

/**
 * Created by Administrator on 2019/1/24.
 * 备忘与日程 延时调用
 */
public class MemoScheduleRemind implements DelayCallback {
    private Integer id;

    public Integer getId() {
        return id;
    }

    public MemoScheduleRemind(Integer id) {
        this.id = id;
    }

    @Override
    public void delayCall(ClusterMessageSendingOperations clusterMessageSendingOperations, ApplicationContext ac) {
        MemoScheduleService memoScheduleService = ac.getBean(MemoScheduleService.class);
        memoScheduleService.addMemoScheduleRemind(id);
    }
}
