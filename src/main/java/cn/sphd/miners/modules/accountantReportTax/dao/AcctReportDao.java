package cn.sphd.miners.modules.accountantReportTax.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.accountantReportTax.dto.AcctReportDetailDto;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctReportEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctReportHistoryEntity;
import cn.sphd.miners.modules.system.entity.User;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface AcctReportDao extends IBaseDao<AcctReportEntity,Serializable> {

    List<AcctReportHistoryEntity> getDisenabledReportByDate(User user, int year, int month);//不再申报的报表集合

    List<AcctReportDetailDto> getListFromDetail(User user, int year, int month);

    Map<String,Object> getByPeriod(User user, int year, int month);

    boolean checkReportName(String name, Integer org);

    AcctReportEntity getReportDetial(User user, int reportId);
}
