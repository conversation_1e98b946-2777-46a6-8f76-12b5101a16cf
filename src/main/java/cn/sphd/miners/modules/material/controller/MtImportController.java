package cn.sphd.miners.modules.material.controller;

import cn.sphd.miners.modules.material.entity.*;
import cn.sphd.miners.modules.material.service.*;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSONArray;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.List;

/**
 * @ClassName MtImportController
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/9 9:37
 * @Version 1.0
 */

@Controller
@RequestMapping("/mtImport")
public class MtImportController {


    @Autowired
    MtImportService mtImportService;
    @Autowired
    MaterielService materielService;

    //判断是否存在未完成批量导入
    // isPurchased;//1是采购过 0是未采购过（string）
    //返回值1，为没有未完成批量导入，可直接导入
    //      0，为有未完成批量导入
    @ResponseBody
    @RequestMapping("/whetherUnfinishedImport.do")
    public RespStatus whetherUnfinishedImport(User user, String isPurchased) throws IOException {
        RespStatus respStatus=new RespStatus();
        Integer org = user.getOid();
        int status=mtImportService.whetherUnfinishedImport(org,isPurchased);
        respStatus.setStatus(status);
        return respStatus;
    }


    //确认单条未导入错误信息是否修改为合法信息
    //传值 code 代号
    //返回值 status 1为可修改，2为和已有的材料相同 3为计量单位已被禁用
    @ResponseBody
    @RequestMapping("/updateFalseMtEnter.do")
    public RespStatus updateFalseMtEnter(User user, MtBase mtBase) throws IOException {
        Integer org = user.getOid();
        mtBase.setOrg(org);
        RespStatus respStatus=mtImportService.updateFalseUserEnter(mtBase);
        return respStatus;
    }

    //确认全部导入数据有多少不合法
    //传值 List<MtBase> mtBaseList  物料列表
    //      importSum;//导入总数
    //返回值 falseImportSum 无法保存的数量
    //      tureImportSum 可以保存的数量
    //      importSum;//导入总数
    @ResponseBody
    @RequestMapping("/allImportMtEnter.do")
    public ReqMtObject allImportMtEnter(User user, ReqMtObject reqMtObject) throws IOException {
        List<MtBase> mtBases = JSONArray.parseArray(reqMtObject.getMtBasesList(),MtBase.class);
        reqMtObject.setMtBaseList(mtBases);
        reqMtObject.setOrg(user.getOid());
        ReqMtObject reqMt=mtImportService.allImportMtEnter(reqMtObject);
        return reqMt;
    }

    //点击确定，提交所有导入材料，不可导入数据删除，可导入数据入数据库
    //传值 List<MtBase> mtBaseList  物料列表
    //     importSum;//导入总数
    //     isPurchased;//1是采购过 0是未采购过（string）
    //返回值
    //      tureImportSum 可以保存的数量
    //      importSum;//导入总数
   //       List<MtBase> mtBaseList  物料列表
    @ResponseBody
    @RequestMapping("/saveImportMt.do")
    public ReqMtObject saveImportMt(User user, ReqMtObject reqMtObject,Integer categoryId) throws IOException {
        MtCategory mtCategory=null;
        List<MtBase> mtBases = JSONArray.parseArray(reqMtObject.getMtBasesList(),MtBase.class);
        reqMtObject.setMtBaseList(mtBases);
        reqMtObject.setOrg(user.getOid());
        List<MtCategory> mtCategories = materielService.getMtCategoryByOidAndPid(reqMtObject.getOrg(), null);
        if(categoryId!=null){
            mtCategory = materielService.getMtCategoryById(categoryId);
        }else {
            for (MtCategory m:mtCategories)
            {
                if("其他原辅材料".equals(m.getName()))
                {
                    List<MtCategory> l=materielService.getMtCategoriesById(m.getId());
                    for (MtCategory m2:l ){
                        if("待分类".equals(m2.getName())){
                            mtCategory=m2;
                        }
                    }
                }
            }
        }

        ReqMtObject reqMt=mtImportService.saveImportMt(reqMtObject,user,mtCategory);
        Integer noUnitNumber=mtImportService.getNoUnitNumberCountByOrg(user.getOid(),reqMtObject.getIsPurchased());
        if (noUnitNumber==0){
            reqMt.setButtonState(1);//  确定按钮状态1- 变亮 0- 置灰
        }else {
            reqMt.setButtonState(0);//  确定按钮状态1- 变亮 0- 置灰
        }
        return reqMt;
    }

    //未完成批量导入列表接口
    //传值 isPurchased;//1是采购过 0是未采购过（string）
    //返回值 falseImportSum 无法保存的数量
    //       tureImportSum 可以保存的数量
    //       importSum;//导入总数
    //       List<MtBase> mtBaseList  物料列表

    @ResponseBody
    @RequestMapping("/unfinishedImportMt.do")
    public ReqMtObject unfinishedImportMt(User user, String isPurchased) throws IOException {
        ReqMtObject reqMt=mtImportService.unfinishedImportMt(isPurchased,user);
        Integer noUnitNumber=mtImportService.getNoUnitNumberCountByOrg(user.getOid(),isPurchased);
        if (noUnitNumber==0){
            reqMt.setButtonState(1);//  确定按钮状态1- 变亮 0- 置灰
        }else {
            reqMt.setButtonState(0);//  确定按钮状态1- 变亮 0- 置灰
        }
        return reqMt;
    }

    //确定或放弃导入
    //传值 type 1保存，0放弃 isPurchased;//1是采购过 0是未采购过（string）
    //返回值 status 1成功，0失败
    @ResponseBody
    @RequestMapping("/finishImportMt.do")
    public RespStatus finishImportMt(User user, String isPurchased,Integer type,Integer category) {
        RespStatus respStatus=new RespStatus();
        int status=mtImportService.finishImportMt(isPurchased,user,type,category);
        respStatus.setStatus(status);
        return respStatus;
    }
    //修改物料
    //传值 材料名称","name"
    //        "材料代号","code"
    //        "规格","specifications
    //        "型号","model"
    //      "备注","memo"
    //    isPurchased;//1是采购过 0是未采购过（string）
    //   id
    // unitId 计量单位id
    //unit 计量单位
    //返回值 status 1成功，0失败,-1代号重复
    @ResponseBody
    @RequestMapping("/updateImportMt.do")
    public RespStatus updateImportMt(MtBase mtBase,User user) throws IOException {
        mtBase.setOrg(user.getOid());
        RespStatus respStatus=mtImportService.updateImportMt(mtBase);
        Integer noUnitNumber=mtImportService.getNoUnitNumberCountByOrg(user.getOid(),mtBase.getIsPurchased());
        if (noUnitNumber==0){
            respStatus.setButtonState(1);//  确定按钮状态1- 变亮 0- 置灰
        }else {
            respStatus.setButtonState(0);//  确定按钮状态1- 变亮 0- 置灰
        }
        return respStatus;
    }
    //删除物料
    //传值 id isPurchased;//1是采购过 0是未采购过（string）
    //返回值 status 1成功，0失败
    @ResponseBody
    @RequestMapping("/deleteImportMt.do")
    public RespStatus deleteImportMt(int id,User user,String isPurchased) throws IOException {
        RespStatus respStatus=new RespStatus();
        int status=mtImportService.deleteImportMt(id, user,isPurchased);
        respStatus.setStatus(status);
        Integer noUnitNumber=mtImportService.getNoUnitNumberCountByOrg(user.getOid(),isPurchased);
        if (noUnitNumber==0){
            respStatus.setButtonState(1);//  确定按钮状态1- 变亮 0- 置灰
        }else {
            respStatus.setButtonState(0);//  确定按钮状态1- 变亮 0- 置灰
        }
        return respStatus;
    }
    //保存时确定有多少数据不合法
    //传值 isPurchased;//1是采购过 0是未采购过（string）
    //返回值 count数量
    @ResponseBody
    @RequestMapping("/finishImportMtEnter.do")
    public RespStatus finishImportMtEnter(User user , String isPurchased) throws IOException {
        RespStatus respStatus=new RespStatus();
        int count=mtImportService.finishImportMtEnter(isPurchased,user);
        respStatus.setCount(count);
        return respStatus;
    }

}
