<template>
  <div id="orderQualityList" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="不合格品处理" :isBackHome=false></TY_NavTop>
    <div class="container">
      <div class="tip" v-show="state === ''">本次共报验如下{{applyHandle.length}}种物料：</div>
      <div class="tip" v-show="state === '3'">本次报验的物料中，如下{{applyHandle.length}}种检验已通过：</div>
      <div class="tip" v-show="state === '4'">本次报验的物料中，如下{{applyHandle.length}}种检验不合格：</div>
      <div class="ui-cells">
        <a class="ui-cell" v-on:click="jump(item.id, 1)" v-for="(item, index) in applyHandle" v-bind:key="index">
          <div class="ui-cell__bd">
            <div class="ui-cell_con">
              <div>{{item.name}}</div>
              <div>{{item.model}}</div>
              <div>申请入库的数量  {{item.quantity_plan}}{{item.unit}}  到货日期 {{item.arrive_date | formatDay('YYYY-MM-DD')}}</div>
            </div>
          </div>
          <div class="ui-cell__ft">查看</div>
        </a>
      </div>
    </div>
  </div>
</template>

<style>
  #orderQualityList .ui-cell{
    font-size: 12px;
  }
</style>

<script>
export default {
  name: 'orderQualityList',
  data () {
    return {
      isBackHome: false,
      listenersUid: [],
      applyHandle: [],
      state: '',
      loading: true
    }
  },
  created: function () {
    let id = this.$route.params.id
    let state = window.localStorage.getItem('orderQualityState')
    let that = this
    this.state = state
    this.axios.post('../../../inStock/countList', {
      applicationId: id,
      state: state
    }).then(function (response) {
      that.loading = false
      let list = response.data.data
      if (list) {
        that.applyHandle = list
      } else {
        that.applyHandle = []
      }
      console.log('orderQualityList', list)
    })
  },
  methods: {
    jump: function (id, tab) {
      this.$store.dispatch('setMaterialParam', {
        user: 'zbuOQ',
        tab: tab
      })
      this.$router.push({
        path: `/materialDetail/${id}`
      })
    }
  }
}
</script>
