package cn.sphd.miners.modules.commodity.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.commodity.entity.PdMerchandise;
import cn.sphd.miners.modules.commodity.entity.PdMerchandiseHistory;

import java.io.Serializable;

public interface PdMerchandiseHistoryDao extends IBaseDao<PdMerchandiseHistory,Serializable> {

    int insert(PdMerchandise product);
    //phid pdbaseId
    int insert(PdMerchandise product,Integer phid);

    int insert(PdMerchandise product,String o);
}
