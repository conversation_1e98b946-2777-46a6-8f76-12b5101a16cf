package cn.sphd.miners.modules.accountant.entity;

import org.apache.ibatis.type.Alias;

import java.io.Serializable;
import java.util.Date;

@Alias("TaccountantSubjectJournal")
public class TaccountantSubjectJournal implements Serializable {

    private static final long serialVersionUID =  7675884414599401232L;

    private Integer id;

    private Integer org;
    private Integer establish;//建账ID

    private Date changeDate;//变动日期

    private Integer subject1;//一级科目变动数

    private Integer subject2;//二级科目变动数

    private Integer subject3;//三级科目变动数

    private Integer subject4;//四级科目变动数

    private String memo;

    private Integer creator;

    private String createName;

    private Date createDate;

    private Integer updator;

    private String updateName;

    private Date updateDate;

    public Integer getEstablish() {
        return establish;
    }

    public void setEstablish(Integer establish) {
        this.establish = establish;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Date getChangeDate() {
        return changeDate;
    }

    public void setChangeDate(Date changeDate) {
        this.changeDate = changeDate;
    }

    public Integer getSubject1() {
        return subject1;
    }

    public void setSubject1(Integer subject1) {
        this.subject1 = subject1;
    }

    public Integer getSubject2() {
        return subject2;
    }

    public void setSubject2(Integer subject2) {
        this.subject2 = subject2;
    }

    public Integer getSubject3() {
        return subject3;
    }

    public void setSubject3(Integer subject3) {
        this.subject3 = subject3;
    }

    public Integer getSubject4() {
        return subject4;
    }

    public void setSubject4(Integer subject4) {
        this.subject4 = subject4;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo == null ? null : memo.trim();
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName == null ? null : createName.trim();
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName == null ? null : updateName.trim();
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }
}