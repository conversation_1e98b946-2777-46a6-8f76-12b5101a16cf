import packageJson from '@/../package.json'
import { Base64 } from 'js-base64'
import router from '@/router'
class Auth {
  getToken () {
    let token = localStorage.getItem('token')
    if (typeof token === 'string' && token.length > 0) {
      return token
    } else if ((token = this.getCookie('token')) && typeof token === 'string' && token.length > 0) {
      return token
    } else {
      return null
    }
  }
  saveToken (token) {
    if (typeof token === 'string' && token.length > 0) {
      localStorage.setItem('token', token)
      console.log('token saved')
    }
  }
  getGuestToken () {
    let webRoot
    if (location.href.indexOf('/vue/') > 0) {
      webRoot = location.href.substring(0, location.href.indexOf('/vue/'))
    } else {
      webRoot = ''
    }
    let result = this.getUrl(webRoot + '/auth/getGuestToken.do')
    if (typeof result === 'string' && result.length > 0) {
      localStorage.setItem('token', result)
      return result
    } else {
      return null
    }
  }
  refreshCurrentUserOrg () {
    let webRoot
    if (location.href.indexOf('/vue/') > 0) {
      webRoot = location.href.substring(0, location.href.indexOf('/vue/'))
    } else {
      webRoot = ''
    }
    let result = this.getUrl(webRoot + '/sys/refreshCurrentUserOrg.do')
    if (typeof result === 'string' && result.length > 0) {
      let data = result.data
      let user = data.user
      if (user != null) {
        localStorage.setItem('user', user)
      }
      let org = data.org
      if (org != null) {
        localStorage.setItem('org', org)
      }
    }
  }
  getTokenPayload () {
    let token = this.getToken()
    if (token != null) {
      let tokenArr = token.split('.')
      if (typeof tokenArr === 'object' && tokenArr instanceof Array && tokenArr.length === 3) {
        return Base64.decode(tokenArr[1])
        // return atob(tokenArr[1].replace(/-/g,'+').replace(/_/g,'/'))
      } else {
        return null
      }
    }
  }
  getByName (name) {
    let tokenStr = this.getTokenPayload()
    if (typeof tokenStr === 'string') {
      let tokenObj = JSON.parse(tokenStr)
      if (typeof tokenObj === 'object') {
        return tokenObj[name]
      }
    }
    return null
  }
  getAcc () {
    return this.getByName('acc')
  }
  getSessionid () {
    return this.getByName('sessionid')
  }
  getUser () {
    if (localStorage.getItem('user') != null) {
      return JSON.parse(localStorage.getItem('user'))
    } else if (this.getByName('userID') != null) {
      this.refreshCurrentUserOrg()
      if (localStorage.getItem('user') != null) {
        return JSON.parse(localStorage.getItem('user'))
      }
    }
    return null
  }
  getOrg () {
    if (localStorage.getItem('org') != null) {
      return JSON.parse(localStorage.getItem('user'))
    } else if (this.getByName('userID') != null) {
      this.refreshCurrentUserOrg()
      if (localStorage.getItem('org') != null) {
        return JSON.parse(localStorage.getItem('org'))
      }
    }
    return null
  }
  getPackageName() {
    return packageJson.name ? packageJson.name : ''
  }
  getCookie (name) { // 获取cookie，仅在同域名iframe中可用
    var arr = document.cookie.match(new RegExp('(^| )' + name + '=([^;]*)(;|$)'))
    if (arr != null) {
      return decodeURIComponent(arr[2])
    } else {
      return null
    }
  }
  getUrl (url) {
    let xmlhttp = new XMLHttpRequest()
    xmlhttp.open('GET', url, false)
    xmlhttp.send(null)
    return xmlhttp.responseText
  }
  constructor (Vue) {
    let token = this.getToken()
    if (typeof token !== 'string' || token.length === 0) {
      token = this.getGuestToken()
    }
    if (typeof token !== 'string' || token.length === 0) {
      router.replace({name: 'error', params: {message: '服务器无法访问'}})
    } else {
      Vue.http.headers.common['token'] = token
    }
    Vue.http.interceptors.push((request, next) => {
      // request.headers.set('token', this.getToken())
      request.headers.set('Wndrss-Prj', this.getPackageName())
      next((response) => {
        if (typeof response === 'object' && typeof response.headers === 'object' && typeof response.headers.get('token') === 'string' && response.headers.get('token').length > 0) {
          this.saveToken(response.headers.get('token'))
        }
        return response
      })
    })
  }
}
export default Auth
