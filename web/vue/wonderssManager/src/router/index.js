import auth from '@/sys/auth'
import {createRouter, createWebHashHistory} from 'vue-router'

const routes = [
    {
        path: "/",
        //   name: "blankPage",
        //   component: () => import("../views/blankPage.vue"),
        // },
        // {
        //   path: "/Start",
        name: "Start",
        component: () => import("../views/Start.vue"),
    },
    {
        path: "/orgList",
        name: "orgList",
        component: () => import("../views/orgList.vue"),
    },
    {
        path: "/switchOrg",
        name: "switchOrg",
        component: () => import("../views/mine/switchOrg.vue"),
    },
    {
        path: '/home',
        name: 'home',
        component: () => import('../views/home.vue'),
        children: [
            {
                path: '/homeMy',
                name: 'homeMy',
                component: () => import('../views/home/<USER>'),
            },
            {
                path: '/chuli',
                name: 'chuli',
                component: () => import('../views/home/<USER>'),
            },
            {
                path: '/gongzuo',
                name: 'gongzuo',
                component: () => import('../views/home/<USER>'),
            },
            {
                path: '/shouye',
                name: 'shouye',
                component: () => import('../views/home/<USER>'),
            },
            {
                path: '/tongxunlu',
                name: 'tongxunlu',
                component: () => import('../views/home/<USER>'),
            },

        ]
    },
    { // 私人领地
        path: '/privateSpace',
        name: 'privateSpace',
        component: () => import('../views/homeMy/privateSpace.vue'),
        children: [
            {
                path: '/life',
                name: 'life',
                component: () => import('../views/homeMy/privateSpace/life.vue'),
            },
            {
                path: '/mailList',
                name: 'mailList',
                component: () => import('../views/homeMy/privateSpace/mailList.vue'),
            },
            {
                path: '/manage',
                name: 'manage',
                component: () => import('../views/homeMy/privateSpace/manage.vue'),
            },
            {
                path: '/remind',
                name: 'remind',
                component: () => import('../views/homeMy/privateSpace/remind.vue'),
            },
            {
                path: '/mine',
                name: 'mine',
                component: () => import('../views/homeMy/privateSpace/mine.vue'),
            },


        ]
    },
    {
        path: "/centralControl",
        name: "centralControl",
        component: () => import("../views/centralControl/centralControl.vue"),
        meta: {
            title: '中枢管控'
        }
    },
    {
        path: "/managerManage",
        name: "managerManage",
        component: () => import("../views/centralControl/managerManage.vue"),
        meta: {
            title: '高管管理'
        }
    },
    {
        path: "/operationGuide",
        name: "operationGuide",
        component: () => import("../views/centralControl/operationGuide.vue"),
        meta: {
            title: '中枢管控-更多操作指南'
        }
    },
    {
        path: "/coreRole/:type",
        name: "coreRole",
        component: () => import("../views/centralControl/coreRole.vue"),
        meta: {
            title: '中枢管控-核心人物管控-临时管理员中间态（set/reset)'
        }
    },
    {
        path: "/coreInput/:type",
        name: "coreInput",
        component: () => import("../views/centralControl/coreInput.vue"),
        meta: {
            title: '中枢管控-核心人物管控-临时管理员录入（set/change/reset）'
        }
    },
    {
        path: "/coreManage",
        name: "coreManage",
        component: () => import("../views/centralControl/coreManage.vue"),
        meta: {
            title: '中枢管控-核心人物管控-核心高管列表'
        }
    },
    {
        path: "/managerInput/:type",
        name: "managerInput",
        component: () => import("../views/centralControl/managerInput.vue"),
        meta: {
            title: '中枢管控-核心人物管控-高管人员选择（add/change）'
        }
    },
    {
        path: "/managerDone",
        name: "managerDone",
        component: () => import("../views/centralControl/managerDone.vue"),
        meta: {
            title: '中枢管控-核心人物管控-高管人员查看'
        }
    },
    {
        path: "/chargeHistory",
        name: "chargeHistory",
        component: () => import("../views/centralControl/chargeHistory.vue"),
        meta: {
            title: '中枢管控-核心人物管控-全权负责人'
        }
    },

// ===========首页财会 开始


    {
        path: '/caikuai',
        name: 'caikuai',
        component: () => import('../views/caikuai/caikuai.vue'),
        children: [
            {
                path: '',
                name: 'caikuai-default',
                component: () => import('../views/caikuai/caikuai-default.vue'),
            },
            {
                path: '/today',
                name: 'today',
                component: () => import('../views/caikuai/today.vue'),
                meta: {
                    title: '现金/备用金与银行账户 - 本日'
                },
                children: [
                    {
                        path: '/ViewByTime',
                        name: 'ViewByTime',
                        component: () => import('../views/caikuai/cai-views/ViewByTime.vue'),
                    },
                    {
                        path: '/ViewByAccount',
                        name: 'ViewByAccount',
                        component: () => import('../views/caikuai/cai-views/ViewByAccount.vue'),
                    }
                ]
            },


            {
                path: '/currentAccountDate/:id',
                name: 'currentAccountDate',
                component: () => import('../views/caikuai/cai-views/currentAccountDate.vue'),
                meta: {
                    title: '现金/备用金与银行账户 - 今日 日流水'
                },
            },
            {
                path: '/currentAccountMonth/:id',
                name: 'currentAccountMonth',
                component: () => import('../views/caikuai/cai-views/currentAccountMonth.vue'),
                meta: {
                    title: '现金/备用金与银行账户 - 本年 日流水'
                },
            },
            {
                path: '/currentAccountDateDur/:id',
                name: 'currentAccountDateDur',
                component: () => import('../views/caikuai/cai-views/currentAccountDateDur.vue'),
                meta: {
                    title: '现金/备用金与银行账户 - 日流水'
                },
            },
            {
                path: '/searchMonthDur',
                name: 'searchMonthDur',
                component: () => import('../views/caikuai/cai-views/searchMonthDur.vue'),
                children: [
                    {
                        path: '/viewByTimeDayList',
                        name: 'viewByTimeDayList',
                        component: () => import('../views/caikuai/cai-views/viewByTimeDayList.vue'),
                    },
                    {
                        path: '/ViewByAccountDayList',
                        name: 'ViewByAccountDayList',
                        component: () => import('../views/caikuai/cai-views/ViewByAccount.vue'),
                    }
                ]
            },
            {
                path: '/searchMonthDur2',
                name: 'searchMonthDur2',
                component: () => import('../views/caikuai/cai-views/searchMonthDur2.vue'),
            },
            {
                path: '/searchYearDur',
                name: 'searchYearDur',
                component: () => import('../views/caikuai/cai-views/searchYearDur.vue'),
                children: [
                    {
                        path: '/viewByTimemonthList',
                        name: 'viewByTimemonthList',
                        component: () => import('../views/caikuai/cai-views/viewByTimemonthList.vue'),
                    },
                    {
                        path: '/ViewByAccountMonthList',
                        name: 'ViewByAccountMonthList',
                        component: () => import('../views/caikuai/cai-views/ViewByAccount.vue'),
                    }
                ]
            },
            {
                path: '/searchYearDur2',
                name: 'searchYearDur2',
                component: () => import('../views/caikuai/cai-views/searchYearDur2.vue'),
                meta: {
                    title: '现金/备用金与银行账户 - 年流水'
                },
            },
            {
                path: '/searchByAccountYearList',
                name: 'searchByAccountYearList',
                component: () => import('../views/caikuai/cai-views/searchByAccountYearList.vue'),
                meta: {
                    title: '按账户查看 - 年列表'
                },
            },
            {
                path: '/monthListAll',
                name: 'monthListAll',
                component: () => import('../views/caikuai/cai-views/monthListAll.vue'),
                meta: {
                    title: '现金/备用金与银行账户 - 年进月 列表'
                },
            },
            {
                path: '/searchYearList',
                name: 'searchYearList',
                component: () => import('../views/caikuai/cai-views/searchYearList.vue'),
                children: [
                    {
                        path: '/viewByTimeYearList',
                        name: 'viewByTimeYearList',
                        component: () => import('../views/caikuai/cai-views/viewByTimeYearList.vue'),
                    },
                    {
                        path: '/ViewByAccountYearList',
                        name: 'ViewByAccountYearList',
                        component: () => import('../views/caikuai/cai-views/ViewByAccount.vue'),
                    }
                ]
            },
            {
                path: '/picList',
                name: 'picList',
                component: () => import('../views/caikuai/cai-views/picList.vue'),
                meta: {
                    title: '现金/备用金与银行账户 - 年流水'
                },
            },
            {
                path: '/currentAccountMonthDur/:id',
                name: 'currentAccountMonthDur',
                component: () => import('../views/caikuai/cai-views/currentAccountMonthDur.vue'),
                meta: {
                    title: '现金/备用金与银行账户 - 月流水'
                },
            },
            {
                path: '/currentMonth',
                name: 'currentMonth',
                component: () => import('../views/caikuai/currentMonth.vue'),
                meta: {
                    title: '现金/备用金与银行账户 - 本月'
                },
                children: [
                    {
                        path: '/ViewByTimeB',
                        name: 'ViewByTimeB',
                        component: () => import('../views/caikuai/cai-views/ViewByTime.vue'),
                    },
                    {
                        path: '/ViewBA2',
                        name: 'ViewBA2',
                        component: () => import('../views/caikuai/cai-views/ViewBA2.vue'),
                    }, {
                        path: '/timeBudget',
                        name: 'timeBudget',
                        component: () => import('../views/caikuai/cai-views/timeBudget.vue')
                    }
                ]
            },
            {
                path: '/currentAD2/:id',
                name: 'currentAD2',
                component: () => import('../views/caikuai/cai-views/currentAD2.vue'),
                meta: {
                    title: '现金/备用金与银行账户 - 月流水（日列表）'
                },
            },
            {
                path: '/searchDayList/:id',
                name: 'searchDayList',
                component: () => import('../views/caikuai/cai-views/searchDayList.vue'),
                meta: {
                    title: '现金/备用金与银行账户 - 月流水（日列表）'
                },
            },
            {
                path: '/timePicker',
                name: 'timePicker',
                component: () => import('../views/caikuai/timePicker.vue'),
                meta: {
                    title: '现金/备用金与银行账户 - 自定义查询'
                },
            },


            {
                path: '/currentYear',
                name: 'currentYear',
                component: () => import('../views/caikuai/currentYear.vue'),
                children: [
                    {
                        path: '/ViewByTimeYear',
                        name: 'ViewByTimeYear',
                        component: () => import('../views/caikuai/cai-views/ViewByTimeYear.vue'),
                    }, {
                        path: '/ViewByAccountYear',
                        name: 'ViewByAccountYear',
                        props: true,
                        component: () => import('../views/caikuai/cai-views/ViewByAccount.vue'),
                    },

                ]
            },


            // {
            //   path: '/custom',
            //   name: 'custom',
            //   component: () => import('../views/caikuai/custom.vue'),
            //   children: [
            //     {
            //       path: '',
            //       name: 'custom-view1',
            //       component: () => import('../views/caikuai/cai-views/custom-view.vue'),
            //       children: [
            //         {
            //           path: '',
            //           component: () => import('../views/caikuai/cai-views/ViewByTime.vue'),
            //         },
            //       ]
            //     },
            //     {
            //       path: '/custom-view',
            //       name: 'custom-view',
            //       component: () => import('../views/caikuai/cai-views/custom-view.vue'),
            //       children: [
            //         {
            //           path: '',
            //           component: () => import('../views/caikuai/cai-views/ViewByTime.vue'),
            //         },
            //       ]
            //     },
            //     {
            //       path: '/custom-viewAccount',
            //       name: 'custom-viewAccount',
            //       component: () => import('../views/caikuai/cai-views/custom-viewAccount.vue'),
            //       children: [
            //         {
            //           path: '',
            //           component: () => import('../views/caikuai/cai-views/ViewByTime.vue'),
            //         },
            //       ]
            //     },
            //   ]
            // },
            //


            {
                path: '/employeeSalary',
                name: 'employeeSalary',
                component: () => import('../views/caikuai/cai-views/employeeSalary.vue'),
            },

            {
                path: '/xiangQing',
                name: 'xiangQing',
                component: () => import('../views/caikuai/cai-views/xiangQing.vue'),
            },
            {
                path: '/ticketInfo/:id',
                name: 'ticketInfo',
                component: () => import('../views/caikuai/cai-views/ticketInfo.vue'),
            },
            {
                path: '/loanDetail',
                name: 'loanDetail',
                component: () => import('../views/caikuai/cai-views/loanDetail.vue'),
            },
            {
                path: '/loanPayDetail/:id',
                name: 'loanPayDetail',
                component: () => import('../views/caikuai/cai-views/loanPayDetail.vue'),
            },
            {
                path: '/loanEditList',
                name: 'loanEditList',
                component: () => import('../views/caikuai/cai-views/loanEditList.vue'),
            },
            {
                path: '/loanEditDetail',
                name: 'loanEditDetail',
                component: () => import('../views/caikuai/cai-views/loanEditDetail.vue'),
            },
            {
                path: '/loanRepayList',
                name: 'loanRepayList',
                component: () => import('../views/caikuai/cai-views/loanRepayList.vue'),
            },
            {
                path: '/loanRepayDetail',
                name: 'loanRepayDetail',
                component: () => import('../views/caikuai/cai-views/loanRepayDetail.vue'),
            },
            {
                path: '/loanRepayEditList',
                name: 'loanRepayEditList',
                component: () => import('../views/caikuai/cai-views/loanRepayDetailEditList.vue'),
            },
            { // 常规借款-付款(还款)/收款(收回借款) - 修改记录 - 详情
                path: '/loanRepayEditDetail',
                name: 'loanRepayEditDetail',
                component: () => import('../views/caikuai/cai-views/loanRepayEditDetail.vue'),
            },
            {
                path: '/changeLog',
                name: 'changeLog',
                component: () => import('../views/caikuai/cai-views/changeLog.vue'),
            },
            {
                path: '/changeLogRaw',
                name: 'changeLogRaw',
                component: () => import('../views/caikuai/cai-views/changeLogRaw.vue'),
            },
        ],
    },
    {
        path: '/cmoHome',
        name: 'cmoHome',
        component: () => import('../views/caikuai/chequesMoneyOrders/cmoHome.vue'),
        meta: {
            title: '主页财会--手中的转账支票及承兑汇票--主页'
        },
    },
    {
        path: '/moneyOrderList',
        name: 'moneyOrderList',
        component: () => import('../views/caikuai/chequesMoneyOrders/moneyOrder/moneyOrderList.vue'),
        meta: {
            title: '主页财会--手中的转账支票及承兑汇票--主页--承兑汇票列表'
        },
    },
    {
        path: '/statisticsHome',
        name: 'statisticsHome',
        component: () => import('../views/caikuai/chequesMoneyOrders/statistics/statisticsHome.vue'),
        meta: {
            title: '主页财会--手中的转账支票及承兑汇票--统计'
        },
    },
    {
        path: '/statisticsDetails',
        name: 'statisticsDetails',
        component: () => import('../views/caikuai/chequesMoneyOrders/statistics/statisticsDetails.vue'),
        meta: {
            title: '主页财会--手中的转账支票及承兑汇票--主页--统计列表--详情页'
        },
    },
    {
        path: '/payStatisticsList',
        name: 'payStatisticsList',
        component: () => import('../views/caikuai/chequesMoneyOrders/statistics/payStatisticsList.vue'),
        meta: {
            title: '主页财会--手中的转账支票及承兑汇票--统计--承兑汇票按收款方列表'
        },
    },
    {
        path: '/payStatisticsDetail',
        name: 'payStatisticsDetail',
        component: () => import('../views/caikuai/chequesMoneyOrders/statistics/payStatisticsDetail.vue'),
        meta: {
            title: '主页财会--手中的转账支票及承兑汇票--承兑汇票按收款方列表--付给XX列表'
        },
    },
    {
        path: '/moneyOrderDetail',
        name: 'moneyOrderDetail',
        component: () => import('../views/caikuai/chequesMoneyOrders/moneyOrder/moneyOrderDetail.vue'),
        meta: {
            title: '主页财会--手中的转账支票及承兑汇票--承兑汇票/转账支票的详情'
        },
    },
    {
        path: '/cftList',
        name: 'cftList',
        component: () => import('../views/caikuai/chequesMoneyOrders/chequeForTransfer/cftList.vue'),
        meta: {
            title: '主页财会--手中的转账支票及承兑汇票--转账支票列表'
        },
    },
    {
        //主页财会--手中的转账支票及承兑汇票>主页--更多数据的下一级页面--点击统计按钮后出现列表(转账支票)，三个类型的选择--选择后详情页
        path: '/cmoStatisticsDetails',
        name: 'cmoStatisticsDetails',
        component: () => import('../views/caikuai/chequesMoneyOrders/statistics/cmoStatisticsDetails.vue'),
        meta: {
            title: '主页财会--手中的转账支票及承兑汇票--统计列表--转账支票详情页'
        },
    },
    {
        path: '/cmoPayStatisticsDetail',
        name: 'cmoPayStatisticsDetail',
        component: () => import('../views/caikuai/chequesMoneyOrders/statistics/cmoPayStatisticsDetail.vue'),
        meta: {
            title: '主页财会--手中的转账支票及承兑汇票--转账支票按收款方列表--付给XX列表'
        },
    },
    {
        //主页财会--手中的转账支票及承兑汇票>主页--更多数据的下一级页面--点击统计按钮后出现列表(转账支票)，三个类型的选择--选择后详情页--点击又付出去的下一步按钮后出现统计-统计列表
        path: '/cmoPayStatisticsList',
        name: 'cmoPayStatisticsList',
        component: () => import('../views/caikuai/chequesMoneyOrders/statistics/cmoPayStatisticsList.vue'),
        meta: {
            title: '主页财会--手中的转账支票及承兑汇票--统计--转账支票--按收款方列表'
        },
    },
    // {
    //     //主页财会--银行的账户信息
    //     path: '/accountList',
    //     name: 'accountList',
    //     component: () => import('../views/caikuai/bankAccount/accountList.vue'),
    // },
    // {
    //     //主页财会--银行的账户信息--某账户的详细信息
    //     path: '/accountDetails',
    //     name: 'accountDetails',
    //     component: () => import('../views/caikuai/bankAccount/accountDetails.vue'),
    // },
    // {
    //     //主页财会--银行的账户信息--某账户的详细信息--修改及录 当前资料未经过修改
    //     path: '/notModified',
    //     name: 'notModified',
    //     component: () => import('../views/caikuai/bankAccount/notModified.vue'),
    // },
    // {
    //     //主页财会--银行的账户信息--某账户的详细信息--修改及录--修改记录列表
    //     path: '/changeRecordListB',
    //     name: 'changeRecordListB',
    //     component: () => import('../views/caikuai/bankAccount/changeRecordList.vue'),
    // },
    // {
    //     //主页财会--银行的账户信息--某账户的详细信息--修改及录--修改记录列表--某次修改详情
    //     path: '/modifyDefails',
    //     name: 'modifyDefails',
    //     component: () => import('../views/caikuai/bankAccount/modifyDefails.vue'),
    // },
    {
        path: '/loansHome',
        name: 'loansHome',
        component: () => import('../views/caikuai/loans/loansHome.vue'),
        meta: {
            title: '贷款-贷款主页'
        },
    },
    {
        path: '/loansDetail',
        name: 'loansDetail',
        component: () => import('../views/caikuai/loans/loansDetails/loansDetail.vue'),
        meta: {
            title: '贷款-贷款主页--贷款详情页'
        },
    },
    {
        path: '/loanRecordList',
        name: 'loanRecordList',
        component: () => import('../views/caikuai/loans/loanRecord/loanRecordList.vue'),
        meta: {
            title: '贷款-贷款主页--贷款详情页--信息修改记录'
        },
    },
    {
        path: '/loanRecordDetails',
        name: 'loanRecordDetails',
        component: () => import('../views/caikuai/loans/loanRecord/loanRecordDetails.vue'),
        meta: {
            title: '贷款-贷款列表--贷款详情页--信息修改记录--修改详情'
        },
    },
    {
        path: '/endDataList',
        name: 'endDataList',
        component: () => import('../views/caikuai/loans/loansEndData/endDataList.vue'),
        meta: {
            title: '贷款-贷款列表--已完结贷款列表'
        },
    },
    {
        path: '/payRecordList',
        name: 'payRecordList',
        component: () => import('../views/caikuai/loans/payRecord/payRecordList.vue'),
        meta: {
            title: '贷款-贷款列表--贷款详情页--付款记录列表'
        },

    },
    {
        path: '/payRecordDetails',
        name: 'payRecordDetails',
        component: () => import('../views/caikuai/loans/payRecord/payRecordDetails.vue'),
        meta: {
            title: '贷款-贷款列表--贷款详情页--付款记录列表--付款记录详情'
        },
    },
    {
        path: '/payChangesDetails',
        name: 'payChangesDetails',
        component: () => import('../views/caikuai/loans/payRecord/payChangesDetails.vue'),
        meta: {
            title: '贷款-贷款列表--贷款详情页--付款记录列表--付款记录详情--修改记录中原始信息/第X次修改后的详情'
        },
    },
    {
        path: '/payChangeRecordList',
        name: 'payChangeRecordList',
        component: () => import('../views/caikuai/loans/payRecord/payChangeRecordList.vue'),
        meta: {
            title: '贷款-贷款列表--贷款详情页--付款记录列表--付款记录详情--修改记录列表--修改记录中原始信息/第X次修改后的详情'
        },
    },

    {
        path: '/borrowMoneyHome',
        name: 'borrowMoneyHome',
        component: () => import('../views/caikuai/borrowMoney/borrowMoneyHome.vue'),
        meta: {
            title: '借款-借款列表'
        },
    },
    {
        path: '/borrowMoneyDetails',
        name: 'borrowMoneyDetails',
        component: () => import('../views/caikuai/borrowMoney/borrowMoneyDetails.vue'),
        meta: {
            title: '借款-借款列表-借款详情页'
        },
    },
    {
        path: '/bmRecordList',
        name: 'bmRecordList',
        component: () => import('../views/caikuai/borrowMoney/bmRecord/bmRecordList.vue'),
        meta: {
            title: '借款-借款列表-借款详情页--借款详情修改记录列表'
        },
    },
    {
        path: '/bmRecordDetail',
        name: 'bmRecordDetail',
        component: () => import('../views/caikuai/borrowMoney/bmRecord/bmRecordDetail.vue'),
        meta: {
            title: '借款-借款列表-借款详情页--借款详情修改记录列表--修改后详情'
        },
    },
    {
        path: '/bmPayRecordList',
        name: 'bmPayRecordList',
        component: () => import('../views/caikuai/borrowMoney/bmPayRecord/bmPayRecordList.vue'),
        meta: {
            title: '借款-借款列表-借款详情页--收款记录'
        },
    },
    {
        path: '/bmPayRecordDetail',
        name: 'bmPayRecordDetail',
        component: () => import('../views/caikuai/borrowMoney/bmPayRecord/bmPayRecordDetail.vue'),
        meta: {
            title: '借款-借款列表-借款详情页--收款记录--收款记录详情'
        },
    },
    {
        path: '/bmPayChangeRecordList',
        name: 'bmPayChangeRecordList',
        component: () => import('../views/caikuai/borrowMoney/bmPayRecord/bmPayChangeRecordList.vue'),
        meta: {
            title: '借款-借款列表-借款详情页--收款记录列表--收款记录详情--收款详情修改记录列表'
        },
    }, {
        path: '/bmPayChangesDetail',
        name: 'bmPayChangesDetail',
        component: () => import('../views/caikuai/borrowMoney/bmPayRecord/bmPayChangesDetail.vue'),
        meta: {
            title: '借款-借款列表-借款详情页--收款记录列表--收款记录详情--收款详情修改记录详情'
        },
    },
    {
        path: '/bmEndDataList',
        name: 'bmEndDataList',
        component: () => import('../views/caikuai/borrowMoney/borrowMoneyEndData/bmEndDataList.vue'),
        meta: {
            title: '借款-借款列表-已完结借款列表'
        },
    },
    {
        //主页财会--未完结的款项
        path: '/uncompletedFundHome',
        name: 'uncompletedFundHome',
        component: () => import('../views/caikuai/uncompletedFund/uncompletedFundHome.vue'),
        meta: {
            title: '财会--未完结的款项'
        },
    },
    {
        path: '/invoiceHome',
        name: 'invoiceHome',
        component: () => import('../views/caikuai/invoice/invoiceHome.vue'),
        meta: {
            title: '财会--申领来的发票'
        },
    },
    {
        //发票的使用记录--按年的列表
        path: '/annualList',
        name: 'annualList',
        component: () => import('../views/caikuai/invoice/invoiceRecord/annualList.vue'),
        meta: {
            title: '发票的使用记录--月列表'
        },
        children: [
            {
                //默认路由 显示所点击年的月列表
                path: '',
                name: 'monthlyList.vue',
                component: () => import('../views/caikuai/invoice/invoiceRecord/monthlyList.vue')
            },
            {
                //所点击年的按发票类型展示
                path: '/category',
                name: 'category',
                component: () => import('../views/caikuai/invoice/invoiceRecord/category.vue'),
            },
        ]
    },
    {
        //所点击年的按发票类型展示-->下一级 某发票的月列表
        path: '/invoiceMonthly',
        name: 'invoiceMonthly',
        component: () => import('../views/caikuai/invoice/invoiceRecord/invoiceMonthly.vue'),
    },
    {
        //某月开具某发票--所开发票的具体列表
        path: '/invoiceDetailsList',
        name: 'invoiceDetailsList',
        component: () => import('../views/caikuai/invoice/invoiceRecord/invoiceDetailsList.vue'),
    },
    {
        //单发票的详情页
        path: '/invoiceDetails',
        name: 'invoiceDetails',
        component: () => import('../views/caikuai/invoice/invoiceRecord/invoiceDetails.vue'),
    },
    {
        //当月 所有发票具体列表
        path: '/monthlyInvoice',
        name: 'monthlyInvoice',
        component: () => import('../views/caikuai/invoice/invoiceRecord/monthlyInvoice.vue'),
        children: [
            {
                //默认路由
                path: '/monthlyInvoiceList',
                name: 'monthlyInvoiceList',
                component: () => import('../views/caikuai/invoice/invoiceRecord/monthlyInvoiceList.vue')
            },
            {
                //所点击年的按发票类型展示
                path: '/categoryB',
                name: 'categoryB',
                component: () => import('../views/caikuai/invoice/invoiceRecord/category.vue'),
            },

        ]
    },

    {
        //发票的申领记录--该时间段的申领列表
        path: '/invoiceClaimList',
        name: 'invoiceClaimList.vue',
        component: () => import('../views/caikuai/invoice/claimRecord/invoiceClaimList.vue'),
    },
    {
        //发票的申领记录--该时间段的申领列表--单个发票的申领详情信息
        path: '/invoiceClaimDetails',
        name: 'invoiceClaimDetails.vue',
        component: () => import('../views/caikuai/invoice/claimRecord/invoiceClaimDetails.vue'),

    },
    {
        //空白发票--增值专用
        path: '/zengZhiZhuanYong',
        name: 'zengZhiZhuanYong.vue',
        component: () => import('../views/caikuai/invoice/blankInvoice/zengZhiZhuanYong.vue'),
    },
    {
        //空白发票--增值普通
        path: '/zengZhiPuTong',
        name: 'zengZhiPuTong.vue',
        component: () => import('../views/caikuai/invoice/blankInvoice/zengZhiPuTong.vue'),
    },
    {
        //空白发票--其他发票
        path: '/otherInvoices',
        name: 'otherInvoices',
        component: () => import('../views/caikuai/invoice/blankInvoice/otherInvoices.vue'),
    },

    // ==============   首页财会结束


    {
        path: "/errPage",
        name: "errPage",
        component: () => import("../views/errPage.vue"),
    },

]

const router = createRouter({
    history: createWebHashHistory(),
    routes
})
//无需登录可前往的页面
const guestPaths = [
    '/',
    '/Start',
    '/errPage'
]
////领地或选机构页面
const manorPaths = [
    '/orgList',
    '/privateSpace',
    '/life',
    '/mailList',
    '/manage',
    '/remind',
    '/mine',
]
router.beforeEach((to, from, next) => {
    if (auth.isLogged() && !auth.isLogInOrg() && manorPaths.length > 0 && !manorPaths.includes(to.path) && !guestPaths.includes(to.path)) {
        next('/orgList')
    } else if (!auth.isLogged() && guestPaths.length > 0 && !guestPaths.includes(to.path)) {
        next('/')
    } else {
        next()
    }
})
export default router
