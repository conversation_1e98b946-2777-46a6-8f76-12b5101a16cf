<template>
  <div id="overReceivedMoney">
    <TY_NavTop title="多收来的款" isSearch="true"></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="可付款" name="1" closable='false' :msgCount="onlineAuditAble.length">
        <div class="tip">
          <p>收到承兑汇票或转账支票时，有时收到金额会超过应收金额。此种情况下，多收来的款需退回。以下为此类款项的清单，处理后请及时在此操作！</p>
        </div>
        <div>
          <a class="ui-cell" v-on:click="jump( item.loanBiz.id, 1, item )" v-for="(item, index) in onlineAuditAble" v-bind:key="index">
            <div class="item_fn">
              <div class="supName">原始的付款方： <span>{{ item.loanBiz.supplierName }}</span></div>
              <div>
                <p> 多收来的金额：{{ (item.loanBiz.amount || 0).toFixed(2) }}元</p>
              </div>
            </div>
            <div class="txt_right">数据创建：{{ item.loanBiz.createName  }}
              {{item.loanBiz.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="待复核" name="2" closable='false'>
        <div class="tip">
          <p>收到承兑汇票或转账支票时，有时收到金额会超过应收金额。此种情况下，多收来的款需退回。以下为此类款项的清单，处理后请及时在此操作！</p>
        </div>
        <div>
          <a class="ui-cell" v-on:click="jump(item.loanBiz.id, 2, item )" v-for="(item, index) in onlineAuditReview" v-bind:key="index">
            <div class="item_fn">
              <div class="supName">原始的付款方： <span>{{ item.loanBiz.supplierName }}</span></div>
              <div>
                <p> 多收来的金额：{{ (item.loanBiz.amount || 0).toFixed(2) }}元</p>
              </div>
            </div>
            <div class="txt_right">数据创建：{{ item.loanBiz.createName  }}
              {{item.loanBiz.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="待付款" name="3" closable='false' :msgCount="onlineAuditHandle.length">
        <div class="tip">
          <p>收到承兑汇票或转账支票时，有时收到金额会超过应收金额。此种情况下，多收来的款需退回。以下为此类款项的清单，处理后请及时在此操作！</p>
        </div>
        <div>
          <a class="ui-cell" v-on:click="jump( item.loanBiz.id, 3, item )" v-for="(item, index) in onlineAuditHandle" v-bind:key="index">
            <div class="item_fn">
              <div class="supName">原始的付款方： <span class="right">{{ item.codeName }}</span></div>
              <div>
                <p> 多收来的金额：{{ (item.loanBiz.amount || 0).toFixed(2) }}元</p>
              </div>
            </div>
            <div class="txt_right">数据创建：{{ item.loanBiz.createName  }}
              {{item.loanBiz.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>
<style lang="less">
#overReceivedMoney{
  overflow: auto;
  .ui-cell{ display: block;  }
  .txt_right{ text-align: right;  }
  .tabCount{
    top:0;
    right:0 ;
    left:auto;
  }
  .tabs-tab{
    width:33%;
  }
  .tip{
    padding:5px 12px;
    background-color: #E2EFDA;
    display: block;
  }
}
</style>
<script>
export default {
  name: 'overreceivedMoney',
  data () {
    return {
      onlineAuditAble: [],
      onlineAuditReview: [],
      onlineAuditHandle: [],
      listenersUid: [],
      tabValue: '1'
    }
  },
  created () {
    this.tabValue = localStorage.navNum || '1'
    this.getList()
    let that = this
    this.listenersUid = [
      // 可付款
      this.sphdSocket.subscribe('overpaymentPayable', function (data) {
        console.log('overpaymentPayable OK:')
        let getData = JSON.parse(data)
        console.log(getData)
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.onlineAuditAble.unshift(getData)
        } else if (operate < 0) {
          let id = getData.loanBiz.id
          let _index = -1
          that.onlineAuditAble.forEach(function (item, index) {
            if (item.loanBiz.id === id) {
              _index = index
            }
          })
          if (_index > -1) {
            that.onlineAuditAble.splice(_index, 1)
          }
        }
      }, null, 'user'),
      // 待复核
      this.sphdSocket.subscribe('overpaymentReviewed', function (data) {
        console.log('overpaymentReviewed OK:')
        let getData = JSON.parse(data)
        console.log(getData)
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.onlineAuditReview.unshift(getData)
        } else if (operate < 0) {
          let id = getData.loanBiz.id
          let _index = -1
          that.onlineAuditReview.forEach(function (item, index) {
            if (item.loanBiz.id === id) {
              _index = index
            }
          })
          if (_index > -1) {
            that.onlineAuditReview.splice(_index, 1)
          }
        }
      }, null, 'user'),
      // 待付款
      this.sphdSocket.subscribe('overpaymentWaiting', function (data) {
        console.log('overpaymentWaiting  ')
        let getData = JSON.parse(data)
        console.log(getData)
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.onlineAuditHandle.unshift(getData)
        } else if (operate < 0) {
          let id = getData.loanBiz.id
          let _index = -1
          that.onlineAuditHandle.forEach(function (item, index) {
            if (item.loanBiz.id === id) {
              _index = index
            }
          })
          if (_index > -1) {
            that.onlineAuditHandle.splice(_index, 1)
          }
        }
      }, null, 'user')
    ]
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    getList: function () {
      let that = this
      this.axios.get('../../../overpayment/getOverpaymentList.do')
        .then(function (response) {
          let res = response.data.data
          console.log('列表：', res)
          that.onlineAuditAble = res.overpaymentPayable
          that.onlineAuditReview = res.overpaymentReviewed
          that.onlineAuditHandle = res.overpaymentWaiting
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    jump: function (id, num, item) {
      console.log('item:', item)
      let businessGroup = Number(item && item.businessGroup || 0)
      if (num === 1) { // 可付款
        this.$router.push({
          path: `/overreceivedMoneyApprove1/${id}/0/0`
        })
      } else {//待复核/待付款
        this.$router.push({
          path: `/overreceivedMoneyApprove2/${id}/${num}/${businessGroup}`
        })
      }
    },
  }
}
</script>
