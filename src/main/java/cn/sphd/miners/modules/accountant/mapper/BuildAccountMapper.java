package cn.sphd.miners.modules.accountant.mapper;

import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.accountant.entity.TAccountantCash;
import cn.sphd.miners.modules.accountant.entity.TAccountantSetting;
import cn.sphd.miners.modules.accountant.entity.TAccountantSubjectEntity;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2018-06-14 19:39
 * @description
 **/
@Component
public interface BuildAccountMapper {
    List<TAccountantSubjectEntity> getBaseSubjectsByOrg(QueryData org);

    TAccountantSetting getBuildAccountState(TAccountantSetting link);

    int setBuildAccountState(Map qd);

    int saveBuildAccount(TAccountantSubjectEntity subjectEntity);

    void clearParentSubject(int org);

    TAccountantSubjectEntity getParentSubject(TAccountantSubjectEntity subjectEntity);

    void deleteOldData(QueryData qd);

    void deleteOldCashFlow(int org);

    void buildFinishCashFlow(int org);

    void buildFinishBalanceSheet(int org);

    void buildFinishProfitSheet(int org);

    void updateSettingByOrg(Map qd);

    void setInitialBuildMonth(Map initMonth);

    void deleteBalanceSheet(QueryData qd);

    void deleteProfitSheet(QueryData qd);

    void deletePeriodData(int org);

//    void deleteReport(int org);

    TAccountantSetting checkRebuildFlag(TAccountantSetting link);

    List<TAccountantCash> getTempCashFlowByOrg(int org);

    void deleteCashReport(int org);

    void deleteCashFlowReport(int org);

    void deleteCashHistoryReport(int org);

    void deleteBalanceSheetReport(int org);

    void deleteProfitReport(int org);

    void deleteSettle(int org);

    void deleteSetting(int org);

    void deleteSubject(int org);

    void deleteSubjectHistory(int org);

    void clearAllParentSubject(int org);

    void buildFinishPeriod(Map qd);

    void setOrgBuildAccountState(Map qd);

    void setOrgRebuildState(Map qd);

    TAccountantSetting getSettingByKey(TAccountantSetting link);

    void initOrgBuildState(int org);

    String enableAccountantModule(QueryData qd);

    void insertSettingKey(TAccountantSetting link);

    TAccountantSetting checkSettingKey(TAccountantSetting link);

}
