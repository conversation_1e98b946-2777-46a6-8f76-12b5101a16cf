<template>
  <div id="testDetails" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="试卷"></TY_NavTop>
    <div class="info">
      <div><span class="ttl">考核目的<span v-if="info.isAgin === '2'">(补考)</span></span><span class="con">{{ info.goal }}</span></div>
      <div><span class="ttl">参考人数</span><span class="con">{{ info.isAgin === '2' ? '---': info.userNum }}人</span></div>
      <div><span class="ttl">试题数量</span><span class="con">共{{ info.choiceNum + info.tfNum  }}道，其中单选题{{ info.choiceNum }}道，判断题{{ info.tfNum }}道</span></div>
      <div><span class="ttl">交卷截至时间</span><span class="con">{{ info.endTime | formatDay('M月DD日 HH:mm') }}</span></div>
      <div><span class="ttl">答卷时间</span><span class="con">{{ info.answerDuration }}分钟</span></div>
      <div><span class="ttl">及格分数</span><span class="con">{{ info.passingScore }}分</span></div>
    </div>
    <div class="tip">
      <p>重要提示！！</p>
      <p>1、交卷截止时间前未能答题者，以零分计</p>
      <p>2、开始答题后，答卷时间内未答的题以零分计</p>
      <p>3、达不到及格分数者，需补考</p>
    </div>
    <div class="subBtn" @click="jump">开始答题</div>
  </div>
</template>
<style lang="less">
  #testDetails{
    background:#fff;
    overflow: auto;
    .info{   }
    .con{ float: right   }
    .info>div { padding:10px 20px; background: #fff; }
    .info>div:nth-child(even){ background:#eee;  }
    .tip{ color:red; padding:20px; display: block;  }
    .subBtn{ width:50%; margin:30px auto 0;line-height:30px; text-align:center; color:#fff;
      background: #4797f1; border-radius: 4px; }

  }
</style>
<script>
export default {
  name: 'testDetails',
  data () {
    return {
      info: {},
      loading: false
    }
  },
  created () {
    let res = JSON.parse(localStorage.getItem('info'))
    this.info = res.trainingExam
  },
  methods: {
    jump: function () {
      let id = this.info.id
      this.$router.push({
        path: `/questionList/${id}`
      })
    }
  }
}
</script>
