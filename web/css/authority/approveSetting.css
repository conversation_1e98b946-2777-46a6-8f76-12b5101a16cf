.area-con>div{ padding: 10px 0;}
.narrowBody{margin: 0 auto;width: 80%;}
.changeDot{padding: 6px 10px;}
.changeDot span{margin-right: 12px;}
.select-active{background: #dbe2ea;  }
.tick1,.tick2{margin-bottom: 20px;}
.tiph{ position: relative; margin-top:6px;  }
.tiph>span{ position: absolute; }
.tiph>div{ margin-left: 40px; }
#paymentSetUpdate select{ width: 150px; text-align: center; }
#paymentSetUpdate p.selectP{background: #7bb3fa;color: #fff;}
.sp>p{  cursor: default; padding: 10px 15px;  }
.yard_item{padding: 30px 0;}
.ptMode{margin-bottom:40px;}
.ptMode hr{border-color: #dcdfe6;}
.ptMode label{margin-right: 12px;}
.ptMode .ty-radio label + span{left: 2px;}
.ptMode .ty-radio{padding: 6px 0;}
.authorAble .ty-radio{width: 260px;}
.modeOption .ty-radio{width: 150px;margin-right: 122px;}
.grayState {background: #bebebe}
#pt_startDate input{margin-left:66px;}
#ptModeSettingsSee hr{border-color: #bebebe;}
.authorCare{display: inline-block;}
.txt{display: inline;font-size: 85%;vertical-align: baseline;}





