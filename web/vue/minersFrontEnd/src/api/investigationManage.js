import {request, noloadingHttp} from "@/utils/request";




/*
 * creator: 2024-09-06  hxz
 *  获取调查列表
 * params:  
*/
export function selectPublishList(data) {
    return request({
        url: '/investigationPublish/selectPublishList.do',
        data: data
    })
}

/*
 * creator: 2024-09-06  hxz
 *  获取 已终止 调查列表
 * params:
*/
export function selectPublishStopByMonthList(data) {
    return request({
        url: '/investigationPublish/selectPublishStopByMonthList.do',
        data: data
    })
}


/*
 * creator: 2024-09-06  hxz
 *  按照 关键字或素材代号 搜索 调查列表
 * params:
*/
export function selectPublishByKeywordList(data) {
    return request({
        url: '/investigationPublish/selectPublishByKeywordList.do',
        data: data
    })
}


/*
 * creator: 2024-09-06  hxz
 *  查看全部
 * params:
*/
export function completeSubjectList(data) {
    return request({
        url: '/investigationPublish/completeSubjectList.do',
        data: data
    })
}

/*
 * creator: 2024-09-09  hxz
 *  调查分析
 * params:
*/
export function investigateAnswerStatistics(data) {
    return request({
        url: '/investigationPublish/investigateAnswerStatistics.do',
        data: data
    })
}

/*
 * creator: 2024-09-09  hxz
 *   调查分析 -  查看题型 列表
 * params:
*/
export function investigateQuestionStatistics(data) {
    return request({
        url: '/investigationPublish/investigateQuestionStatistics.do',
        data: data
    })
}

/*
 * creator: 2024-09-09  hxz
 * 切换至答卷者选择结果的统计报表
 * params:
*/
export function answerListByMultipleChoiceQuestion(data) {
    return request({
        url: '/investigationPublish/answerListByMultipleChoiceQuestion.do',
        data: data
    })
}

/*
 * creator: 2024-09-09  hxz
 *  调查分析 -  查看题型 问答题
 * params:
*/
export function investigateAnswerListByQuestion(data) {
    return request({
        url: '/investigationPublish/investigateAnswerListByQuestion.do',
        data: data
    })
}

/*
 * creator: 2024-09-09  hxz
 *  调查分析 -  全部数据；日期地址的详情
 * params:
*/
export function areaListByQuestion(data) {
    return request({
        url: '/investigationPublish/areaListByQuestion.do',
        data
    })
}


/*
 * creator: 2024-09-09  hxz
 *  查看全部 - 答题详情
 * params:
*/
export function selectObjectById(data) {
    return request({
        url: '/investigationPublish/selectObjectById.do',
        data
    })
}

/*
 * creator: 2024-09-11  hxz
 *   查看全部 - 数据展示设置\导出设置
 * params:
*/
export function selectQuestionOptionsList(data) {
    return request({
        url: '/investigationPublish/selectQuestionOptionsList.do',
        data
    })
}


/*
 * creator: 2024-09-11  hxz
 *   查看全部 - 数据展示设置保存
 * params:
*/
export function updateShowQuestion(data) {
    return request({
        url: '/investigationPublish/updateShowQuestion.do',
        data
    })
}


/*
 * creator: 2024-09-11  hxz
 *   查看全部 - 数据展示设置保存
 * params:
*/
export function getQRLink(data) {
    return request({
        url: '/investigationPublish/getQRLink.do',
        data
    })
}


/*
 * creator: 2024-09-12 hxz
 *   获取问卷
 * params:
*/
export function selectSubjectOptionsList() {
    return request({
        url: '/investigationPublish/selectSubjectOptionsList.do',
    })
}

/*
 * creator: 2024-09-13 hxz
 * 发起新的调查 确定
 * params:
*/
export function addPublish(data) {
    return request({
        url: '/investigationPublish/addPublish.do',
        data
    })
}

/*
 * creator: 2024-09-13 hxz
 * 管理
 * params:
*/
export function managePublish(data) {
    return request({
        url: '/investigationPublish/managePublish.do',
        data
    })
}
/*
 * creator: 2024-09-13 hxz
 * 截止日期履历
 * params:
*/
export function selectInvestigateQr(data) {
    return request({
        url: '/investigationPublish/selectInvestigateQr.do',
        data
    })
}


/*
 * creator: 2024-09-13 hxz
 * 是否 纳入统计
 * params:
 *    id 应答对象id（object的id）
      type 1 将状态修改为纳入统计，0 将状态修改为未纳入统计
*/
export function updateObjectSubsumable(data) {
    return request({
        url: '/investigationPublish/updateObjectSubsumable.do',
        data
    })
}




