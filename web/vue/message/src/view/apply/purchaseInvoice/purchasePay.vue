<template>
  <div id="purchasePay">
    <TY_NavTop title="采购部门的付款" isSearch="true" @toggleList="search"></TY_NavTop>
    <div class="container">
      <div class="nowTime">下列付款申请系采购部门提交，有待审批！</div>
      <div>
        <a class="ui-cell" v-on:click="jump('12' + item.poPaymentApplication.id)" v-for="(item, index) in toHandle" v-bind:key="index">
          <div class="item_fn">
            <div class="supName">{{item.fullName}} <span class="right">{{ item.codeName }}</span></div>
            <div class="oInfo">
              <p> {{ item.poPaymentApplication.type | formatType(item.poPaymentApplication.amount) }}</p>
              <p v-if="item.poPaymentApplication.type !== '3'">票据 {{item.poPaymentApplication.billCount }} 张，共 {{item.poPaymentApplication.billAmount.toFixed(2) }} 元</p>
              <p v-else>本次付款申请没有附带的票据</p>
            </div>
          </div>
          <div class="txt_right">申请人：{{ item.poPaymentApplication.createName  }}
            {{item.poPaymentApplication.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
          <div class="ui-cell__ft"></div>
        </a>
      </div>
    </div>
  </div>
</template>
<style lang="less">
  #purchasePay{
    .ui-cell{ display: block;  }
    .oInfo{ padding:5px 20px; }
    .txt_right{ text-align: right;  }
  }
</style>
<script>
export default {
  name: 'purchasePay',
  data () {
    return {
      toHandle: [],
      listenersUid: [],
      nowTime: 0
    }
  },
  created () {
    let that = this
    this.getList();
    this.listenersUid = [
      this.sphdSocket.subscribe('paymentApplyHandle', function (data) {
        console.log('paymentApplyHandle')
        let getData = JSON.parse(data)
        console.log(getData)
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.toHandle.unshift(getData)
        } else if (operate < 0) {
          let id = getData.poPaymentApplication.id
          let _index = -1
          that.toHandle.forEach(function (item, index) {
            if (item.poPaymentApplication.id === id) {
              _index = index
            }
          })
          if (_index > -1) {
            that.toHandle.splice(_index, 1)
          }
        }
      }, null, 'user')
    ]
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  filters: {
    formatType: function (num, amount) {
      switch (Number(num)){
        case 1:
          return "本次仅提交票据，不付款";
          break;
        case 2:
          return `申请付款${amount.toFixed(2)}元`;
          break;
        case 3:
          return `申请付款${amount.toFixed(2)}元`;
          break;
      }
    },
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  methods: {
    getList: function () {
      let that = this
      this.axios.get('../../../purchaseInvoice/getPaymentApply.do')
        .then(function (response) {
          console.log('列表：', response)
          let res = response.data.data
          let list = res.paymentApplyHandle // 待处理
          that.toHandle = list
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    jump: function (id) {
      this.$router.push({
        path: `/purchaseInvoiceDetails/${id}`
      })
    },
    search: function () {
      this.$router.push({
        path: `/applyReimburseQuery`
      })
    }
  }
}
</script>
