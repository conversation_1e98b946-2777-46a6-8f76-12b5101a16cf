export default [
  // ----------日常事务----------
  {
    path: '/applyAffair/:pid',
    name: 'applyAffair',
    component: () => import('@/view/apply/dailyAffair/apply'),
    meta: {
      title: '我的申请-日常事务'
    }
  },
  {
    path: '/approvalAffair/:pid',
    name: 'approvalAffair',
    component: () => import('@/view/approve/dailyAffair/approval'),
    meta: {
      title: '处理/审批-日常事务'
    }
  },
  {
    path: '/applyOutTime',
    name: 'applyOutTime',
    component: () => import('@/view/apply/dailyAffair/overTime'),
    meta: {
      title: '我的申请-日常事务-加班申请'
    }
  },
  {
    path: '/approvalOutTime',
    name: 'approvalOutTime',
    component: () => import('@/view/approve/dailyAffair/overTime'),
    meta: {
      title: '处理/审批-日常事务-加班审批'
    }
  },
  {
    path: '/applyOutTimeDetail/:outTimeId',
    name: 'applyOutTimeDetail',
    component: () => import('@/view/apply/dailyAffair/overTime_detail'),
    meta: {
      title: '我的申请-日常事务-加班申请--加班详情'
    }
  },
  {
    path: '/approvalOutTimeDetail/:outTimeId',
    name: 'approvalOutTimeDetail',
    component: () => import('@/view/approve/dailyAffair/overTime_detail'),
    meta: {
      title: '处理/审批-日常事务-加班审批--加班详情'
    }
  },
  {
    path: '/applyOverTimeQuery',
    name: 'applyOverTimeQuery',
    component: () => import('@/view/apply/dailyAffair/overTime_query'),
    meta: {
      title: '我的申请-日常事务-加班申请--查询'
    }
  },
  {
    path: '/approvalOverTimeQuery',
    name: 'approvalOverTimeQuery',
    component: () => import('@/view/approve/dailyAffair/overTime_query'),
    meta: {
      title: '处理/审批-日常事务-加班审批--查询'
    }
  },
  {
    path: '/applyOverTimeQueryPer',
    name: 'applyOverTimeQueryPer',
    component: () => import('@/view/apply/dailyAffair/overTime_query_per'),
    meta: {
      title: '我的申请-日常事务-加班申请--查询结果'
    }
  },
  {
    path: '/approvalOverTimeQueryPer',
    name: 'approvalOverTimeQueryPer',
    component: () => import('@/view/approve/dailyAffair/overTime_query_per'),
    meta: {
      title: '处理/审批-日常事务-加班审批--查询结果'
    }
  },
  {
    path: '/assignOutTime/:pid',
    name: 'overtimeAssign',
    component: () => import('@/view/approve/dailyAffair/overtimeAssign'),
    meta: {
      title: '处理/审批-被指派的加班'
    }
  },
  {
    path: '/overtimeAssignDetail/:id/:mark',
    name: 'overtimeAssignDetail',
    component: () => import('@/view/approve/dailyAffair/overtimeAssign_detail'),
    meta: {
      title: '处理/审批-被指派的加班--详情'
    }
  },
  {
    path: '/overtimeAssignQuery',
    name: 'overtimeAssignQuery',
    component: () => import('@/view/query/overtimeAssign_query'),
    meta: {
      title: '处理/审批-被指派的加班--查询'
    }
  },
  {
    path: '/overtimeAssignQueryPer',
    name: 'overtimeAssignQueryPer',
    component: () => import('@/view/query/overtimeAssign_query_per'),
    meta: {
      title: '处理/审批-被指派的加班--查询结果'
    }
  },
  {
    path: '/applyLeave',
    name: 'applyLeave',
    component: () => import('@/view/apply/dailyAffair/leave'),
    meta: {
      title: '我的申请-日常事务-请假申请'
    }
  },
  {
    path: '/approvalLeave',
    name: 'approvalLeave',
    component: () => import('@/view/approve/dailyAffair/leave'),
    meta: {
      title: '处理/审批-日常事务-请假审批'
    }
  },
  {
    path: '/applyLeaveDetail/:leaveId',
    name: 'applyLeaveDetail',
    component: () => import('@/view/apply/dailyAffair/leave_detail'),
    meta: {
      title: '我的申请-日常事务-请假申请--详情'
    }
  },
  {
    path: '/approvalLeaveDetail/:leaveId',
    name: 'approvalLeaveDetail',
    component: () => import('@/view/approve/dailyAffair/leave_detail'),
    meta: {
      title: '处理/审批-日常事务-请假审批--详情'
    }
  },
  {
    path: '/applyLeaveQuery',
    name: 'applyLeaveQuery',
    component: () => import('@/view/apply/dailyAffair/leave_query'),
    meta: {
      title: '我的申请-日常事务-请假申请--查询'
    }
  },
  {
    path: '/approvalLeaveQuery',
    name: 'approvalLeaveQuery',
    component: () => import('@/view/approve/dailyAffair/leave_query'),
    meta: {
      title: '处理/审批-日常事务-请假审批--查询'
    }
  },
  {
    path: '/applyLeaveQueryPer',
    name: 'applyLeaveQueryPer',
    component: () => import('@/view/apply/dailyAffair/leave_query_per'),
    meta: {
      title: '我的申请-日常事务-请假申请--查询结果'
    }
  },
  {
    path: '/approvalLeaveQueryPer',
    name: 'approvalLeaveQueryPer',
    component: () => import('@/view/approve/dailyAffair/leave_query_per'),
    meta: {
      title: '处理/审批-日常事务-请假审批--查询结果'
    }
  },
  {
    path: '/myMessage',
    name: 'myMessage',
    component: () => import('@/view/message/message'),
    meta: {
      title: '我的消息'
    }
  },
  // ----------考勤----------
  {
    path: '/attendanceEditApply/:pid',
    name: 'attendanceEditApply',
    component: () => import('@/view/apply/attendance/attendanceChangeApply'),
    meta: {
      title: '职工考勤修改'
    }
  },
  {
    path: '/myAttendanceEditApply',
    name: 'myAttendanceEditApply',
    component: () => import('@/view/apply/attendance/myAttendanceEditApply'),
    meta: {
      title: '我的考勤修改'
    }
  },
  {
    path: '/attendanceEditApproval/:pid',
    name: 'attendanceEditApproval',
    component: () => import('@/view/approve/attendance/attendanceChangeApproval'),
    meta: {
      title: '职工考勤修改'
    }
  },
  {
    path: '/attendanceOfficerSubmitEdit',
    name: 'attendanceClerkChange',
    component: () => import('@/view/approve/attendance/attendanceChange/attendanceClerk'),
    meta: {
      title: '职工考勤修改'
    }
  },
  {
    path: '/personalSubmitAttendanceApproval',
    name: 'personalSubmitAttendanceApproval',
    component: () => import('@/view/approve/attendance/attendanceChange/attendanceClerkPerson'),
    meta: {
      title: '个人提交的考勤修改'
    }
  },
  {
    path: '/attendanceChangeDetail/:id/:mark/:type',
    name: 'attendanceChangeDetail',
    component: () => import('@/view/common/attendance/attendanceChangeDetail'),
    meta: {
      title: '职工考勤修改-详情'
    }
  },
  {
    path: '/myAttendanceChangeDetail/:id/:mark/:type',
    name: 'myAttendanceChangeDetail',
    component: () => import('@/view/common/attendance/myAttendanceChangeDetail'),
    meta: {
      title: '考勤修改-详情（包括我的考勤修改、考勤员提交的考勤修改、个人提交的考勤修改）'
    }
  },
  {
    path: '/attendanceChangeAffairsDetail',
    name: 'attendanceChangeAffairsDetail',
    component: () => import('@/view/common/attendance/attendanceChangeAffairsDetail'),
    meta: {
      title: '职工考勤修改-修改前后加班请假等详情'
    }
  },
  {
    path: '/myAttendanceChangeAffairsDetail',
    name: 'myAttendanceChangeAffairsDetail',
    component: () => import('@/view/common/attendance/myAttendanceChangeAffairsDetail'),
    meta: {
      title: '考勤修改-详情（包括我的考勤修改、考勤员提交的考勤修改、个人提交的考勤修改）-修改前后加班请假等详情'
    }
  },
  {
    path: '/attendanceChangeQuery/:name',
    name: 'attendanceChangeQuery',
    component: () => import('@/view/query/attendanceChange_query'),
    meta: {
      title: '职工考勤修改、我的考勤修改、考勤员提交的考勤修改、个人提交的考勤修改-查询'
    }
  },
  {
    path: '/attendanceChangeQueryPer',
    name: 'attendanceChangeQueryPer',
    component: () => import('@/view/query/attendanceChange_query_per'),
    meta: {
      title: '考勤修改-查询结果'
    }
  },
  {
    path: '/attendanceChangePersonQueryPer',
    name: 'attendanceChangePersonQueryPer',
    component: () => import('@/view/query/attendanceChangePerson_query_per'),
    meta: {
      title: '考勤修改-查询结果'
    }
  },
  // ----------讨论区----------
  {
    path: '/forumApply/:pid',
    name: 'forumApply',
    component: () => import('@/view/apply/discussion/discuss'),
    meta: {
      title: '讨论发起申请'
    }
  },
  {
    path: '/forumLaunchApproval/:pid',
    name: 'forumLaunchApproval',
    component: () => import('@/view/approve/discussion/discussMiddle'),
    meta: {
      title: '讨论发起申请（中间人）'
    }
  },
  {
    path: '/forumApproval/:pid',
    name: 'forumApproval',
    component: () => import('@/view/approve/discussion/discuss'),
    meta: {
      title: '讨论发起审批'
    }
  },
  {
    path: '/discussDetail/:id/:mark',
    name: 'discussDetail',
    component: () => import('@/view/common/discussion/discussDetail'),
    meta: {
      title: '讨论详情（讨论发起申请、讨论发起审批、管理的讨论组）'
    }
  },
  {
    path: '/willCleanForum/:pid',
    name: 'willCleanForum',
    component: () => import('@/view/approve/discussion/willCleanForum'),
    meta: {
      title: '将被清除的讨论组'
    }
  },
  {
    path: '/manageTheForum/',
    name: 'manageTheForum',
    component: () => import('@/view/approve/discussion/manageTheForum'),
    meta: {
      title: '管理的讨论组'
    }
  },
  {
    path: '/discussQuery/:name',
    name: 'discussQuery',
    component: () => import('@/view/query/discuss_query'),
    meta: {
      title: '讨论-查询（讨论发起申请、讨论发起审批）'
    }
  },
  {
    path: '/discussQueryPer',
    name: 'discussQueryPer',
    component: () => import('@/view/query/discuss_query_per'),
    meta: {
      title: '讨论-查询结果（讨论发起申请、讨论发起审批）'
    }
  }
]
