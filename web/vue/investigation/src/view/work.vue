<template id="work">
  <div class="work">
    <el-row>
      <div class="panel"><span class="panelTitle">工作经历</span></div>
    </el-row>
    <el-form :model="workForm" ref="workForm" label-width="130px" class="workForm">
      <div v-for="(occupation, index) in workForm.occupations" v-bind:key="occupation.key">
        <el-form-item :prop="'occupations.' + index + '.corpName'" label="公司名称">
          <el-input v-model="occupation.corpName"></el-input>
        </el-form-item>
        <el-form-item :prop="'occupations.' + index + '.beginTime'" label="工作时间">
          <el-date-picker type="date" placeholder="选择起时间" v-model="occupation.beginTime"></el-date-picker>
        </el-form-item>
        <el-form-item :prop="'occupations.' + index + '.endTime'" label="">
          <el-date-picker type="date" placeholder="选择止时间" v-model="occupation.endTime"></el-date-picker>
        </el-form-item>
        <el-form-item :prop="'occupations.' + index + '.salary'" label="薪资水平" :rules="{
            required: true, message: '请输入薪资水平', trigger: 'blur', validator: validateNum
          }">
          <el-input v-model="occupation.salary"></el-input>
        </el-form-item>
        <el-form-item :prop="'occupations.' + index + '.post'" label="在职职位">
          <el-input v-model="occupation.post"></el-input>
        </el-form-item>
        <el-form-item :prop="'occupations.' + index + '.memo'" label="未继续工作的原因">
          <el-input v-model="occupation.memo"></el-input>
        </el-form-item>
        <el-form-item :prop="'occupations.' + index + '.operatingDuty'" label="工作职责">
          <el-input v-model="occupation.operatingDuty"></el-input>
        </el-form-item>
      </div>
      <el-form-item>
        <el-button @click="addWork">+</el-button>
      </el-form-item>
      <el-form-item size="large">
        <el-button type="primary" @click="step3('workForm')">下一步<i class="el-icon-arrow-right el-icon--right"></i>
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
export default {
  data () {
    let validateNum = (rule, value, callback) => {
      let reg = /^[0-9]*[1-9][0-9]*$/
      if (!reg.test(value)) {
        callback(new Error('请输入数字'))
      } else {
        callback()
      }
    }
    return {
      workForm: {
        occupations: [{
          corpName: '',
          beginTime: '',
          endTime: '',
          salary: '',
          post: '',
          memo: '',
          operatingDuty: ''
        }]
      },
      rules: {
        occupations: [{
          salary: [
            { type: 'number', validator: validateNum }
          ]
        }]
      }
    }
  },
  methods: {
    step3 (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          window.localStorage.setItem('occupations', JSON.stringify(this.workForm.occupations))
          this.$router.push({
            path: '/education',
            name: 'education'
          })
        } else {
          this.$message('请填必填项！')
          return false
        }
      })
    },
    addWork () {
      this.workForm.occupations.push({
        corpName: '',
        beginTime: '',
        endTime: '',
        salary: '',
        post: '',
        memo: '',
        operatingDuty: ''
      })
    },
    jumpError (message) {
      this.$router.replace({ name: 'error', params: { message: message } })
    }
  }
}
</script>
