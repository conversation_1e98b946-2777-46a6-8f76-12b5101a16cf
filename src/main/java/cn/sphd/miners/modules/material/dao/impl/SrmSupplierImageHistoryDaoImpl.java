package cn.sphd.miners.modules.material.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.modules.material.dao.SrmSupplierImageHistoryDao;
import cn.sphd.miners.modules.material.entity.SrmSupplierImage;
import cn.sphd.miners.modules.material.entity.SrmSupplierImageHistory;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
@Repository
public class SrmSupplierImageHistoryDaoImpl extends BaseDao<SrmSupplierImageHistory, Serializable> implements SrmSupplierImageHistoryDao {
    @Override
    public SrmSupplierImageHistory insert(SrmSupplierImage srmSupplierImage,Integer chId) {
        SrmSupplierImageHistory srmSupplierImageHistory=new SrmSupplierImageHistory();
        BeanUtils.copyProperties(srmSupplierImage,srmSupplierImageHistory);
        srmSupplierImageHistory.setId(null);
        srmSupplierImageHistory.setSupplier(chId);
        srmSupplierImageHistory.setSupplierImage(srmSupplierImage.getId());
        save(srmSupplierImageHistory);
        return srmSupplierImageHistory;
    }
}
