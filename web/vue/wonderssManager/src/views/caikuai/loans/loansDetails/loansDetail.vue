<template>
  <!-- 贷款列表--贷款详情-->
  <div class="box">
    <div class="head">
      <PageHeader :title="tit" :showClose=true @mycloseFun="handleCustomClose" :showBack=true>
      </PageHeader>
    </div>
    <div class="sticky ty-fff h35f14 padding-0-15 spaceBetween">
      <p>
        <span>{{ loanDetail.createName }}</span>
        <span class="padding-0-10">{{ new
        Date(loanDetail.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>创建
      </p>
      <span style="color:#337ab7;" @click="rou()">修改记录</span>
    </div>
    <div class="re margin_top_16">
      <div class="padding-0-15 ty-fff">
        <p>
          <span>本金金额</span>
          <span class="hH">{{ this.formatCurrency(loanDetail.principalAmount) }}元</span>
        </p>
        <p>
          <span>出资方</span>
          <span style="text-align: right" class="hH">{{ loanDetail.loaner }}</span>
        </p>
        <p>
          <span>本金型式</span>
          <span v-if="loanDetail.incomeMethod==='1'">现金</span>
          <span v-else-if="loanDetail.incomeMethod==='3'">转账支票</span>
          <span v-else-if="loanDetail.incomeMethod==='4'">承兑汇票</span>
          <span v-else-if="loanDetail.incomeMethod==='5'">银行转账</span>
        </p>
        <div v-if="loanDetail.incomeMethod==='1'">
          <p>
            <span>收款日期</span>
            <span class="hH">{{ new
            Date(loanDetail.paymentDate).format('yyyy-MM-dd') }}</span>
          </p>
          <p>
            <span>收款经手人</span>
            <span class="hH">{{ loanDetail.partnerName }}</span>
          </p>
        </div>
        <div v-else-if="loanDetail.incomeMethod==='5'">
          <p>
            <span>到账日期</span>
            <span class="hH">{{ new
            Date(loanDetail.arriveDate).format('yyyy-MM-dd') }}</span>
          </p>
          <p>
            <span>收款银行</span>
            <span class="hH">{{ loanDetail.receiveBank }}</span>
          </p>
        </div>
        <div v-else-if="loanDetail.incomeMethod==='3'||loanDetail.incomeMethod==='4'">
          <p>
            <span>收到{{loanDetail.incomeMethod === '3' ? '支' : loanDetail.incomeMethod === '4' ? '汇' : 'err'}}票日期</span>
            <span>{{ new
            Date(loanDetail.billReceiveDate).format('yyyy-MM-dd') }}</span>
          </p>
          <p>
            <span>{{loanDetail.incomeMethod === '3' ? '支' : loanDetail.incomeMethod === '4' ? '汇' : 'err'}}票号</span>
            <span class="hH">{{ loanDetail.billNo }}</span>
          </p>
          <p>
            <span>票据金额</span>
            <span class="hH">{{ this.formatCurrency(loanDetail.principalAmount) }}元</span>
          </p>
          <p>
            <span>{{loanDetail.incomeMethod === '3' ? '支' : loanDetail.incomeMethod === '4' ? '汇' : 'err'}}票到期日</span>
            <span>{{ loanDetail.billEndDate }}</span>
          </p>
          <p>
            <span>{{loanDetail.incomeMethod === '3' ? '出具支' : loanDetail.incomeMethod === '4' ? '原始出具汇' : 'err'}}票单位</span>
            <span style="text-align: end" class="hH">{{ loanDetail.billSource }}</span>
          </p>
          <p>
            <span>出具{{loanDetail.incomeMethod === '3' ? '支' : loanDetail.incomeMethod === '4' ? '汇' : 'err'}}票银行</span>
            <span style="text-align: end" class="hH">{{ loanDetail.billBank }}</span>
          </p>
        </div>
        <p>
          <span>应归还本金的日期</span>
          <span v-if="loanDetail.repaymentMethod==='1'">未约定具体日期</span>
          <span v-if="loanDetail.repaymentMethod==='2'">{{ new
          Date(loanDetail.repaymentDate).format('yyyy-MM-dd') }}</span>
        </p>
        <p>
          <span>名义利率</span>
          <span>{{ ((loanDetail.nominalRate) * 100).toFixed(2) }}%</span>
        </p>
        <p>
          <span>利息的支付方式</span>
          <span v-if="loanDetail.interestMethod==='0'">无利息</span>
          <span
              v-if="loanDetail.interestMethod!=='0'">按{{ loanDetail.interestMethod === '1' ? '日' : loanDetail.interestMethod === '2' ? '月' : loanDetail.interestMethod === '3' ? '年' : '错误'
            }}付</span>
        </p>
        <div v-if="loanDetail.interestMethod!=='0'">
          <p>
            <span>开始计息日期</span>
            <span>{{ new
            Date(loanDetail.interestAccrualDate).format('yyyy-MM-dd') }}</span>
          </p>
          <p>
            <span>每{{ loanDetail.interestMethod === '1' ? '日' : loanDetail.interestMethod === '2' ? '月' : loanDetail.interestMethod === '3' ? '年' : '错误'
              }}还款日</span>
            <span>{{ loanDetail.periodRepaymentDate }}</span>
          </p>
          <p>
            <span>每次应付款金额</span>
            <span>{{ this.formatCurrency(loanDetail.periodRepaymentAmount) }}元</span>
          </p>
        </div>
        <p>
          <span>备注</span>
          <span class="hH" style="width: 70%">{{ loanDetail.memo }}</span>
        </p>
      </div>
      <p class="ty-fff spaceBetween padding-0-15 h35f14 margin_top_16">
        <span>已付款金额</span>
        <span>{{ sumRepaymentAmount }}元</span>
        <span style="color:#337ab7;" @click="pay()">付款记录</span>
      </p>
      <div class="loansBottom">
        <div v-for="(item,index) in recordList" :key="index">
          <div class="hr_gray "></div>
          <p class="ty-fff recordList">{{ item.createName }}于
            {{ new
            Date(item.createDate).format('yyyy-MM-dd hh:mm:ss')}}
            将本条数据{{ item.state === true ? "由“已完结”恢复至“尚需继续付款”。" : item.state === false ? "操作为“已完结”。" : 'err'}}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import finance from '@/mixins/finance.js'


export default {
  name: "loansDetail",
  mixins: [finance],
  data() {
    return {
      tit: "财会",
      loanDetail: [],
      recordList: [],
    }

  },
  computed: {
    id() {
      return this.$route.query.id
    },
  },
  created() {
  },
  mounted() {
    this.getLoansDetails()
  },
  methods: {
    rou() {
      this.$router.push({
        path: '/loanRecordList',
        query: {id: this.id, type: "loans"}
      })
      console.log(this.id, 'id')
    },
    getLoansDetails() {
      let params = {id: this.id, sourceType: 2}
      this.financeApi.ordLoanDetail(params).then(res1 => {
        this.loanDetail = res1.data.data.loanDetail
        this.recordList = res1.data.data.recordList
        this.sumRepaymentAmount = this.formatCurrency(res1.data.data.sumRepaymentAmount)
      }).catch(err => {
        console.log('err=', err)
        this.$message.error('链接失败，请重试！')
      })
    },
    pay() {
      if (this.sumRepaymentAmount === 0) {
        this.$message({
          message: '暂无付款记录！',
          offset: 300 // 设置偏移量为300px
        });
      } else {
        this.$router.push({path: '/payRecordList', query: {id: this.id}})

      }
    }
    ,
    handleCustomClose() {
      this.$router.push("/caikuai")
    }
  }
}
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";

.head {
  position: fixed;
  width: 100%;
  z-index: 999;
}

.hH {
  width: 55%;
  text-align: end;
  line-height: 22px;
}

.box {
  background-color: $tyColorBg;
}

.re {
  position: relative;
  top: 40px;

  > div:nth-child(1) {
    line-height: 30px;

    > p {
      display: flex;
      justify-content: space-between;
      padding: 2px 0;
      align-items: center;
    }

    > div {
      > p {
        display: flex;
        justify-content: space-between;
        padding: 2px 0;
        align-items: center;
      }
    }
  }
}

.recordList {
  padding: 6px 15px;
  word-break: break-all;
  white-space: normal;
  line-height: 25px;
}

.loansBottom {
  overflow: hidden;
}

.hr_gray {
  width: 100%;
  height: 15px;
  background-color: #eee;
}
</style>