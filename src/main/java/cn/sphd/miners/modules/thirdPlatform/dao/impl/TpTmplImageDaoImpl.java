package cn.sphd.miners.modules.thirdPlatform.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.thirdPlatform.dao.TpTmplImageDao;
import cn.sphd.miners.modules.thirdPlatform.entity.TpTmplImage;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

@Repository
public class TpTmplImageDaoImpl extends BaseDao<TpTmplImage, Serializable> implements TpTmplImageDao {
}
