package cn.sphd.miners.modules.material.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.material.entity.SrmSupplierContact;

import java.io.Serializable;

/**
 * Created by Administrator on 2016/9/19.
 */
public interface SrmSupplierContactDao extends IBaseDao<SrmSupplierContact,Serializable>{
    void deleteSrmSupplierContact(SrmSupplierContact mtSupplierContact);
    SrmSupplierContact getSrmSupplierContactById(Integer id);
   ;



}
