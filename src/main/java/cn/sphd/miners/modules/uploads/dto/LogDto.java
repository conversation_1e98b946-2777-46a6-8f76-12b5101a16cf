package cn.sphd.miners.modules.uploads.dto;

import cn.sphd.miners.common.utils.CallStackUtils;
import cn.sphd.miners.common.utils.GetLocalIPUtils;
import eu.bitwalker.useragentutils.UserAgent;

import javax.servlet.http.HttpServletRequest;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021年02月08日 16:00
 **/
public class LogDto implements Serializable {
    private static final long serialVersionUID = 1L;
    private String source;
    private String callingFunction;
    private String calledFunction;
    private String info;
    private String originalFilename;//只在复制的时候使用，记录旧文件filename

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getCallingFunction() {
        return callingFunction;
    }

    public void setCallingFunction(String callingFunction) {
        this.callingFunction = callingFunction;
    }

    public String getCalledFunction() {
        return calledFunction;
    }

    public void setCalledFunction(String calledFunction) {
        this.calledFunction = calledFunction;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public String getOriginalFilename() {
        return originalFilename;
    }

    public void setOriginalFilename(String originalFilename) {
        this.originalFilename = originalFilename;
    }

    public LogDto(HttpServletRequest request, String info) {
        StackTraceElement stack = new CallStackUtils().getCalledStack("controller");
        if(stack!=null) {
            setCalledFunction(stack.getMethodName());
        }
        if(request!=null) {
            String ua = request.getHeader("User-Agent");
            UserAgent userAgent = UserAgent.parseUserAgentString(ua);
            setCallingFunction(userAgent.getOperatingSystem().toString()+" "+userAgent.getBrowser().toString());
            setSource(GetLocalIPUtils.getLocalIp(request));
        }
        this.info = info;
    }
    public LogDto(String info, String className) {
        CallStackUtils utils = new CallStackUtils();
        StackTraceElement stackCalled;
        if((stackCalled=utils.getCalledStack(className))!=null) {
            setCalledFunction(stackCalled.getMethodName());
        }
        StackTraceElement stackCalling;
        if((stackCalling=utils.getCallingStack(className))!=null) {
            setSource(stackCalling.getClassName());
            setCallingFunction(stackCalling.getMethodName());
        }
        this.info = info;
    }
}