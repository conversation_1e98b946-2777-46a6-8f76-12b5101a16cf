package cn.sphd.miners.modules.personal.service.impl;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.service.BaseServiceImpl;
import cn.sphd.miners.common.utils.DateUtils;
import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.dailyAffairs.service.impl.OverturnOverTime;
import cn.sphd.miners.modules.generalAffairs.dto.WorkOffBlockDto;
import cn.sphd.miners.modules.generalAffairs.entity.PersonnelAttendanceConfig;
import cn.sphd.miners.modules.generalAffairs.entity.PersonnelAttendanceUserDetail;
import cn.sphd.miners.modules.generalAffairs.service.AttendanceEventSerivce;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceOldService;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceService;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceService.WorkdayType;
import cn.sphd.miners.modules.personal.dao.PersonnelOvertimeDao;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.entity.PersonnelLeave;
import cn.sphd.miners.modules.personal.entity.PersonnelOvertime;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.personal.service.OvertimeService;
import cn.sphd.miners.modules.personal.service.UserMessageService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.entity.ApprovalFlow;
import cn.sphd.miners.modules.system.entity.ApprovalItem;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.ApprovalService;
import cn.sphd.miners.modules.system.service.PopedomTaskService;
import cn.sphd.miners.modules.system.service.RoleService;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSON;
import io.netty.util.internal.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by Administrator on 2017/5/8.
 */
@Service("overtimeService")
public class OvertimeServiceImpl extends BaseServiceImpl implements OvertimeService, AttendanceEventSerivce {
    @Autowired
    PersonnelOvertimeDao personnelOvertimeDao;

    @Autowired
    UserMessageService userMessageService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    WorkAttendanceOldService workAttendanceOldService;
    @Autowired
    WorkAttendanceService workAttendanceService;
    @Autowired
    ApprovalService approvalService;
    @Autowired
    UserService userService;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    SWMessageService swMessageService;
    @Autowired
    RoleService roleService;
    @Autowired
    PopedomTaskService popedomTaskService;

    String sdf = "yyyy-MM-dd HH:mm:ss";

    @Override
    public List<PersonnelOvertime> getPersonnelOvertimeListByUser(Integer oid,Integer userId,Integer loginUserId, Integer approvalStatus,String actualType,Integer type) {
        Map<String,String> orderBy = new HashMap<String, String>();
        String hql = "o.org="+oid;
        if (userId!=null) {
            hql+=" and o.user=" + userId;
        }else if (loginUserId!=null){
            if(type!=null&&type==2) {
                Map<String, Object> map1 = userService.getUserIds(loginUserId, 1);
                String userIds = (String) map1.get("userIdsString");
                hql += " and o.user_ in (" + userIds + ")";
            }
        }
        if (approvalStatus!=null&&approvalStatus!=0){
            hql+=" and o.approveStatus="+approvalStatus;
        }else {
            hql+=" and o.approveStatus=1";
        }

        if (!"".equals(actualType) && actualType!=null){
            hql+=" and o.actualType="+actualType;
        }else {
            hql+=" and o.actualType!='2'";
        }

        if (approvalStatus!=null) {
            if (approvalStatus == 1 || approvalStatus == 2) {
                orderBy.put("o.createDate", "asc");
            } else if (approvalStatus == 3 || approvalStatus == 4) {
                orderBy.put("o.createDate", "desc");
            }
        }
        List<PersonnelOvertime> personnelOvertimeList=personnelOvertimeDao.findCollectionByConditionNoPage(hql,null,orderBy);
        return personnelOvertimeList;
    }

    @Override
    public Double getDuration(String begin, String end) {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        long diff = 0;//相差毫秒数
        diff = NewDateUtils.dateFromString(end,sdf).getTime() - NewDateUtils.dateFromString(begin,sdf).getTime();

        long day = diff / nd;//天
        long hour=diff/nh;
        long min = diff % nd % nh / nm;//分钟
        String duration=String.valueOf(hour);
        if (min>0){
            duration+=".5";
        }
        return Double.valueOf(duration);
    }

    @Override
    public Double getOverTimeHours(Integer userId, String approvalStatus, Date beginTime, Date endTime, String actualState) {
        HashMap<String, Object> params = new HashMap<>();
        String hql=" select sum(approveDuration) from PersonnelOvertime o where 1=1";
        if (userId!=null){
            hql+=" and o.user_=:userId";
            params.put("userId",userId);
        }
        if (approvalStatus!=null){
            hql+=" and o.approveStatus=:approvalStatus";
            params.put("approvalStatus",approvalStatus);
        }else {
            hql+=" and o.approveStatus=:approvalStatus";
            params.put("approvalStatus","1");
        }
        if (beginTime!=null){
            hql+=" and o.createDate>=:beginTime";
            params.put("beginTime",beginTime);
        }
        if (endTime!=null){
            hql+=" and o.createDate<=:endTime";
            params.put("endTime",endTime);
        }
        if (!"".equals(actualState) && actualState!=null){
            if ("4".equals(actualState)){
                hql+=" and o.type in ('1','2')";
            }else {
                hql+=" and o.type=:actualState";
                params.put("actualState",actualState);
            }
        }
        Double hours = (Double) personnelOvertimeDao.getByHQLWithNamedParams(hql,params);
        if (hours!=null) {
            return hours;
        }else {
            return 0.0;
        }
    }

    @Override
    public List<PersonnelOvertime> getPersonnelOvertimeListByTime(Integer userId, Integer approvalStatus, Date beginTime, Date endTime) {
        StringBuilder hql = new StringBuilder("from PersonnelOvertime where user_=:user");
        Map<String,Object> params = new HashMap<>() {{
            put("user", userId);
        }};
        hql.append(" and approveStatus=:approvalStatus");
        if (approvalStatus!=null&&approvalStatus!=0){
            params.put("approvalStatus",String.valueOf(approvalStatus));
        }else {
            params.put("approvalStatus","1");
        }

        if (beginTime!=null){
            hql.append(" and actualEndTime>:beginTime");
            params.put("beginTime",beginTime);
        }
        if (endTime!=null){
            hql.append(" and actualBeginTime<:endTime");
            params.put("endTime",endTime);
        }
        return personnelOvertimeDao.getListByHQLWithNamedParams(hql.append(" and actualBeginTime<actualEndTime  order by actualBeginTime,id").toString(), params);
    }

    @Override
    public List<WorkOffBlockDto> getBlocks(User user, Date begin, Date end) {
        List<PersonnelOvertime> list = getPersonnelOvertimeListByTime(user.getUserID(), 4, begin, end);
        String beanName = getBeanName();
        return list.stream().map(source->new WorkOffBlockDto(WorkdayType.Workday, beanName, source.getId(), source.getActualBeginTime(), source.getActualEndTime(), source.getUpdateDate())).collect(Collectors.toList());
    }
    @Override
    public Pair<Integer, WorkOffBlockDto> getToUpdateEvent(Integer business) {
        PersonnelOvertime overtime = personnelOvertimeDao.get(business);
        return Pair.of(overtime.getUser_(), new WorkOffBlockDto(WorkdayType.DayOff, getBeanName(), overtime.getId(), overtime.getActualBeginTime(), overtime.getActualEndTime(), overtime.getUpdateDate()));
    }
    @Override
    public void fillNewDetail(PersonnelAttendanceUserDetail detail) {
        PersonnelOvertime item = personnelOvertimeDao.get(detail.getBusiness());
        detail.setReason(StringUtils.isNotBlank(item.getActualReason())?item.getActualReason():item.getReason());
        detail.setBusinessType(item.getType());
        Integer userId = item.getCreator()!=null?item.getCreator():item.getUpdator();
        String userName = StringUtils.isNotBlank(item.getCreateName())?item.getCreateName():item.getUpdateName();
        if(userId!=null){
            detail.setCreator(userId);
        }
        if(StringUtils.isNotBlank(userName)){
            detail.setCreateName(userName);
        }
        userId = item.getUpdator()!=null?item.getUpdator():item.getCreator();
        userName = StringUtils.isNotBlank(item.getUpdateName())?item.getUpdateName():item.getCreateName();
        if(userId!=null){
            detail.setUpdator(userId);
        }
        if(StringUtils.isNotBlank(userName)){
            detail.setUpdateName(userName);
        }
    }

    @Override
    public List<PersonnelOvertime> getPersonnelOvertimeListByBeginTime(Integer userId, Integer approvalStatus, Date beginTime, Date endTime, String actualType,Integer sortType,Integer approverId) {
        Map<String,Object> params = new HashMap<String, Object>();
        String hql=" from PersonnelOvertime o where o.user_=:userId";
        params.put("userId",userId);
        if (approvalStatus!=null&&approvalStatus!=0){
            hql+=" and o.approveStatus=:approvalStatus";
            params.put("approvalStatus",approvalStatus.toString());
        }else {
            hql+=" and o.approveStatus=1";
        }

        if (beginTime!=null){
            hql+=" and o.beginTime>=:beginTime";
            params.put("beginTime",beginTime);
        }
        if (endTime!=null){
            hql+=" and o.beginTime<=:endTime";
            params.put("endTime",endTime);
        }
        if (StringUtils.isNotEmpty(actualType)){
            hql+=" and o.actualType=:actualType";
            params.put("actualType",actualType);
        }
        if (approverId!=null){
            hql+=" and o.id in (select business from ApprovalProcess where toUser=:approverId)";
            params.put("approverId",approverId);
        }
        if (sortType!=null&&sortType==1){
            hql+=" order by o.beginTime desc";  //以加班开始时间倒序排列
        }else {
            hql+=" order by o.beginTime asc";  //以加班开始时间顺序排列
        }

        List<PersonnelOvertime> personnelOvertimeList= personnelOvertimeDao.getListByHQLWithNamedParams(hql,params);
        return personnelOvertimeList;
    }

    @Override        //toUserId (审批人的加班)   approvalStatus(审批流程中的审批状态)   businessType(审批流程中的审批类型)
    public List<PersonnelOvertime> getPersonnelOvertimeByApproverId(Integer toUserId, String approvalStatus, Integer businessType, String beginTime, String endTime, PageInfo page) {
        String hql = " o.approveStatus=4";
        if (!"".equals(beginTime) && beginTime!=null){
            hql+=" and o.beginTime>='"+beginTime+"'";
        }
        if (!"".equals(endTime) && endTime!=null){
            hql+=" and o.beginTime<='"+endTime+"'";
        }
        if (toUserId!=null){
            hql+=" and o.id in (select business from ApprovalProcess p where p.toUser="+toUserId+" and p.approveStatus="+approvalStatus+" and p.businessType="+businessType+")";
        }
        Map<String,String> orderBy = new HashMap<String, String>();
        orderBy.put("o.beginTime","desc");  //以加班开始时间倒序排列
        List<PersonnelOvertime> personnelOvertimes = personnelOvertimeDao.findCollectionByConditionNoPage(hql,null,orderBy);
        return personnelOvertimes;
    }

    @Override    //待处理
    public List<PersonnelOvertime> getPersonnelOvertimeByHandle(Integer toUserId,Integer oid,String approvalStatus, Integer businessType,Integer type) {
        Map<String,Object> map = new HashMap<>();
        StringBuffer hql = new StringBuffer(" from PersonnelOvertime o where o.actualType!='2'");
        if (businessType!=null) {
            hql.append(" and o.approveStatus=:approval");
            if (6 == businessType && "2".equals(approvalStatus)) {
                map.put("approval", "1");  //申报加班待处理
            } else {
                map.put("approval", approvalStatus);   //计划加班待处理
            }
        }
        if (toUserId!=null||oid!=null){
            hql.append(" and o.id in (select business from ApprovalProcess p where");
            if (type!=null&&type==2){  //判断要有，两者有其一
                if (toUserId!=null){
                    Map<String, Object> map1 = userService.getUserIds(toUserId, 1);
                    String userIds = (String) map1.get("userIdsString");
                    hql.append(" p.toUser in ("+userIds+")");
                }
            }else if (type==null||type!=1){  //判断要有，两者有其一
                if (toUserId!=null){
                    hql.append(" p.toUser=:toUserId");
                    map.put("toUserId", toUserId);
                }
            }
            if (oid!=null){
                if (type!=null&&type!=1){
                    hql.append(" and");
                }
                hql.append(" p.org=:org");
                map.put("org", oid);
            }

            if (!MyStrings.nulltoempty(approvalStatus).isEmpty()){
                hql.append(" and p.approveStatus=:approvalStatus");
                map.put("approvalStatus",approvalStatus);
            }
            if (businessType!=null){
                hql.append(" and p.businessType=:businessType");
                map.put("businessType",businessType);
            }
            hql.append(")");
        }
        hql.append(" order by o.beginTime asc");  //以加班开始时间顺序排列

        List<PersonnelOvertime> personnelOvertimes = personnelOvertimeDao.getListByHQLWithNamedParams(hql.toString(),map);
        return personnelOvertimes;
    }

    @Override     //已批准
    public List<PersonnelOvertime> getPersonnelOvertimeByApproval(Integer toUserId,Integer oid,Integer type) {
        //需返回的数据：1.当前审批人审批通过的计划加班（多级加班可能未审批完）   2.当前审批人审批通过的申报加班（申报加班定为审批未完成的）
        //   3.多级审批时，申请人申报的申报加班审批流程，未到此审批人时的，此审批人为计划加班
        Map<String,Object> map = new HashMap<>();
        String hql = "select max(ap.id) from PersonnelOvertime po,ApprovalProcess ap where po.id=ap.business and ap.businessType in(2,6) and po.approveStatus in('1','2')";
        if (type!=null&&type==2){
            if (toUserId!=null){
                Map<String, Object> map1 = userService.getUserIds(toUserId, 1);
                String userIds = (String) map1.get("userIdsString");
                hql+=" and ap.toUser in ("+userIds+")";
            }
        }else if (type==null||type!=1){
            if (toUserId!=null){
                hql+=" and ap.toUser=:toUserId";
                map.put("toUserId",toUserId);
            }
        }
        if (oid!=null){
            hql+=" and ap.org=:org";
            map.put("org",oid);
        }
        hql+=" group by po.id";
        List<Integer> ids = (List<Integer>) personnelOvertimeDao.getListByHQLWithNamedParams(hql,map);

        map.clear();

        List<PersonnelOvertime> personnelOvertimes = new ArrayList<>();
        if (ids.size()>0){
            String hql1 = "select po from PersonnelOvertime po,ApprovalProcess ap where ap.id in(:ids) and po.actualType!='2' and po.id=ap.business and ap.approveStatus='2' group by po.id";
            map.put("ids",ids);
            personnelOvertimes = personnelOvertimeDao.getListByHQLWithNamedParams(hql1.toString(),map);
        }
        return personnelOvertimes;
    }


    @Override     // type 1-计划加班 2-补报加班 3-加班指派 4-到时未提交实际加班的，系统默认实际未加班  5-判断是否需要默认添加打卡
    public void overturn(Integer id, Integer type) {
        PersonnelOvertime personnelOvertime=personnelOvertimeDao.get(id);
        if (type!=3&&type!=4&&"1".equals(personnelOvertime.getApproveStatus())&&"0".equals(personnelOvertime.getActualType())) { //待处理，且是计划申请阶段
            List<ApprovalProcess> approvalProcessList = new ArrayList<>();
            if (1==type) {
                approvalProcessList = approvalProcessService.getApprovalProcessByBusiness(id, 2, null);
            }else if (2==type){   //补报加班中查询的是申报的
                approvalProcessList = approvalProcessService.getApprovalProcessByBusiness(id, 6, null);
            }
            String reason="该申请未被及时审批";
            personnelOvertime.setApproveStatus("3");
            personnelOvertime.setApplyMemo(reason);//驳回理由
            personnelOvertime.setAuditDate(personnelOvertime.getBeginTime());
            personnelOvertime.setFinalResult("5");  //事件结果:0-其它,1-已完结的加班,2-未被认可的加班,3-提交了加班申请,但实际未加班,4-无需再填报时长的加班,5-加班申请被驳回'
            userMessageService.updatePersonnelOvertime(personnelOvertime);

            //给申请人 待处理推
            this.outTimeRejectSend(0,-1,personnelOvertime,personnelOvertime.getUser_(),"/applyOutTimeHandle",null,null,"applyOutTime");

           userSuspendMsgService.saveUserSuspendMsg(1, "您的加班申请被驳回了！", "驳回时间  " + NewDateUtils.dateToString(personnelOvertime.getBeginTime(),sdf), personnelOvertime.getUser_(),"applyOutTimeDetail",id);

            for (ApprovalProcess approvalProcess : approvalProcessList) {
                if ("1".equals(approvalProcess.getApproveStatus())) {
                    //待处理的变为 驳回
                    approvalProcess.setApproveStatus("3");
                    approvalProcess.setHandleTime(personnelOvertime.getBeginTime());
                    approvalProcess.setReason(reason);
                    approvalProcess.setUserName("系统");
                    approvalProcessService.updateApprovalProcess(approvalProcess);

                    //给审批者 待处理推
                    this.outTimeRejectSend(-1,-1,personnelOvertime,approvalProcess.getToUser(),"/approvalOutTimeHandle",null,null,"approvalOutTime");
                } else if ("2".equals(approvalProcess.getApproveStatus())) {

                    //给审批者 已批准推
                    this.outTimeRejectSend(0,-1,personnelOvertime,approvalProcess.getToUser(),"/approvalOutTimeApproval",null,null,"approvalOutTime");
                }
            }

        }else if (3==type){  //指派加班的
            List<ApprovalProcess> approvalProcessList = approvalProcessService.getApprovalProcessByBusiness(id, 60, null);

            String reason="该申请未被及时审批";
            personnelOvertime.setApproveStatus("3");
            personnelOvertime.setApplyMemo(reason);//驳回理由
            personnelOvertime.setAuditDate(personnelOvertime.getBeginTime());
            userMessageService.updatePersonnelOvertime(personnelOvertime);

            userSuspendMsgService.saveUserSuspendMsg(1, personnelOvertime.getUserName()+"未同意"+NewDateUtils.dateToString(personnelOvertime.getBeginTime(),"yyyy-MM-dd")+"来加班！", "操作时间  " + NewDateUtils.dateToString(personnelOvertime.getBeginTime(),sdf), personnelOvertime.getUser_(),"assignOutTimeDetail",id);

            for (ApprovalProcess approvalProcess : approvalProcessList) {
                if ("1".equals(approvalProcess.getApproveStatus())) {
                    //待处理的变为 驳回
                    approvalProcess.setApproveStatus("3");
                    approvalProcess.setHandleTime(personnelOvertime.getBeginTime());
                    approvalProcess.setReason(reason);
                    approvalProcess.setUserName("系统");
                    approvalProcessService.updateApprovalProcess(approvalProcess);

                    //给审批者 待处理推
                    this.outTimeRejectSend(-1,-1,personnelOvertime,approvalProcess.getToUser(),"/assignOutTimeHandle",null,null,"");
                }
            }
        }else if (4==type&&"2".equals(personnelOvertime.getApproveStatus())&&"0".equals(personnelOvertime.getActualType())){ //4-到时未提交，系统默认的实际未加班
            this.deletePersonnelOvertime(personnelOvertime.getId(),4);
        }
    }



    //加班 驳回 长连接推送   pass 通道  superscript 角标
    @Override
    public void outTimeRejectSend(int loginNum,int operate,PersonnelOvertime personnelOvertime,Integer toUserId,String pass,String title, String content,String code){
        System.out.println("加班推送开始:"+new Date());
        System.out.println("加班id："+personnelOvertime.getId()+" userId: "+toUserId);
        HashMap<String,Object> hashMap=new HashMap<>();
        hashMap.put("personnelOvertime", personnelOvertime);
        User approvalUser= userService.getUserByID(toUserId);
        swMessageService.rejectSend(loginNum,operate,hashMap,toUserId.toString(),pass,title,content,approvalUser,code);
        System.out.println("加班推送结束:"+new Date());

    }

    @Override
    public Integer getOvertimeCounts(Integer userId, String approveStatus,String actualType) {
        String hql="select count(id) from PersonnelOvertime where user_=:userId and approveStatus=:approveStatus and actualType=:actualType";
        Map<String,Object> map = new HashMap<>();
        map.put("userId",userId);
        map.put("approveStatus",approveStatus);
        map.put("actualType",actualType);
        Long count= (Long) personnelOvertimeDao.getByHQLWithNamedParams(hql,map);
        return count.intValue();
    }

    @Override
    public HashMap<String, Object> myOutTimeInfoById(Integer userId, Integer id,HashMap<String,Object> map) {
        map.put("status", 1);   //获取成功
        PersonnelOvertime outTime = personnelOvertimeDao.get(id);
        User overUser = userService.getUserByID(outTime.getUser_());  //加班的人员
        Integer dept = 0;  //部门其他的
        if (StringUtils.isNotEmpty(overUser.getDepartment())) {
            dept = Integer.parseInt(overUser.getDepartment());
        }
        PersonnelAttendanceConfig personnelAttendanceConfig = workAttendanceOldService.getPersonnelAttendanceConfigByDept(overUser.getOid(),dept,outTime.getBeginTime());  //获取加班人员那天的考勤规则
        if (personnelAttendanceConfig!=null&&personnelAttendanceConfig.getAttendancePattern().equals(WorkAttendanceOldService.ConfigAttendancePattern.attendanceTreasure.getIndex())){  //考勤宝的
            PersonnelAttendanceUserDetail personnelAttendanceUserDetail = workAttendanceService.getPersonnelAttendanceUserDetail(overUser.getUserID(), NewDateUtils.today(outTime.getBeginTime()), "8", id, null);
//            List<Map<String, Object>> punchList = new ArrayList<>();
            if (personnelAttendanceUserDetail!=null) {
                Map<String,Object> map1 = workAttendanceOldService.getClockRecord(overUser.getUserID(),(byte)7,personnelAttendanceUserDetail.getId(),null); //加班开始打卡时间，打卡得最早时间
                Map<String,Object> map2 = workAttendanceOldService.getClockRecord(overUser.getUserID(),(byte)8,personnelAttendanceUserDetail.getId(),1);  //加班结束打卡时间，打卡得最晚时间
                if (map1!=null&&map1.size()>0){
                    map.put("beginPunchTime",map1.get("punchTime"));  //最早打卡时间
                }else {
                    map.put("beginPunchTime",null);  //最早打卡时间
                    map.put("status", 2);   //不可以申报加班
                }
                if (map2!=null&&map2.size()>0){
                    map.put("endPunchTime",map2.get("punchTime"));  //最早打卡时间
                }else {
                    map.put("endPunchTime",null);  //最早打卡时间
                    map.put("status", 2);   //不可以申报加班
                }
            }else {
                map.put("status", 2);   //不可以申报加班
            }
            map.put("attendancePattern",personnelAttendanceConfig.getAttendancePattern());   //考勤模式:1-考勤宝,2-手工录入(默认)
        }else {
            map.put("attendancePattern",2);//考勤模式:1-考勤宝,2-手工录入(默认)
        }

        outTime.setUserName(outTime.getUser().getUserName());
        map.put("personnelOvertime", outTime);
        List<ApprovalProcess> processList = approvalProcessService.getApprovalProcessByBusiness(outTime.getId(),2,null);
        List<ApprovalProcess> processList1 = approvalProcessService.getApprovalProcessByBusiness(outTime.getId(),6,null);
        map.put("processList", processList);//计划加班审批流程列表
        map.put("processList1", processList1);//申报加班审批流程列表
        ApprovalItem approvalItem = roleService.getCurrentItem(outTime.getOrg(),"submitOvertimeRules");
        if (approvalItem!=null){
            map.put("upperLimit",approvalItem.getUpperLimit());
        }else {
            map.put("upperLimit","");
        }
        return map;
    }

    @Override    //(tjtype 1-计划加班申请 2-申报加班申请 3-补报加班 4-指派加班)
    public PersonnelOvertime addPersonnelOvertime(User user,PersonnelOvertime personnelOvertime,Double actualDuration,String actualReason,String beginTime1, String endTime1,String tjtype) {

        Date begin=StringUtils.isNotEmpty(beginTime1)?NewDateUtils.dateFromString(beginTime1,sdf):null;
        Date end=StringUtils.isNotEmpty(endTime1)?NewDateUtils.dateFromString(endTime1,sdf):null;

        if ("1".equals(tjtype)||"4".equals(tjtype)){   //计划加班
            personnelOvertime.setKind(Kind.ordinary.getIndex()); //类型:1-普通(默认),2-补报
            if ("1".equals(tjtype)){
                personnelOvertime.setBeginTime(begin);
                personnelOvertime.setEndTime(end);
                personnelOvertime.setDuration(this.getDuration(beginTime1,endTime1));//时长
                personnelOvertime.setOrg(user.getOid());
                personnelOvertime.setUser(user);
                personnelOvertime.setUser_(user.getUserID());
            }
            personnelOvertime.setCreateName(user.getUserName());
            personnelOvertime.setCreator(user.getUserID());
            personnelOvertime.setApproveStatus("1");
            personnelOvertime.setActualType("0");//实际加班状态:0-未加班,1-已加班，2-实际未加班
            personnelOvertime.setAuditDate(new Date());
            Integer dept = 0;  //无部门的人员，在考勤中部门id为0
            if (StringUtils.isNotEmpty(user.getDepartment())){
                dept=Integer.parseInt(user.getDepartment());
            }
            String type;
            if((type = workAttendanceOldService.overTimeType(null,user,personnelOvertime.getBeginTime(),dept))==null
                    && (MyStrings.nulltoempty(type = personnelOvertime.getType()).isEmpty())) {
                type = NewDateUtils.isWeekend(personnelOvertime.getBeginTime()) ? "2" : "1";
            }
            personnelOvertime.setType(type);  //有考勤的按照考勤设置返回加班类型；没有考勤的按照传值返回加班类型
            personnelOvertime.setActualState(personnelOvertime.getType());  //加班时段类型:1-工作日加班2-周末假日加班3-法定节假日加班
            personnelOvertime.setFinalResult("0");
            personnelOvertimeDao.save(personnelOvertime);
        }else if ("2".equals(tjtype)){  //申报加班
            personnelOvertime.setActualBeginTime(begin);
            personnelOvertime.setActualEndTime(end);
            personnelOvertime.setActualDuration(actualDuration);  //时长
            personnelOvertime.setActualReason(actualReason); //原因
            personnelOvertime.setActualApplyTime(new Date());  //申报申请时间
            personnelOvertime.setActualType("1");  //实际加班状态:0-未加班,1-已加班
            personnelOvertime.setApproveStatus("1");
            personnelOvertime.setAuditDate(new Date());
            userMessageService.updatePersonnelOvertime(personnelOvertime);
        }else if ("3".equals(tjtype)){   //补报加班的
            //计划加班和申报加班一起的
            personnelOvertime.setKind(Kind.supplementary.getIndex()); //类型:1-普通(默认),2-补报
            personnelOvertime.setBeginTime(begin);
            personnelOvertime.setEndTime(end);
            personnelOvertime.setDuration(actualDuration);//时长
            personnelOvertime.setCreateName(user.getUserName());
            personnelOvertime.setCreator(user.getUserID());
            personnelOvertime.setOrg(user.getOid());
            personnelOvertime.setUser(user);
            personnelOvertime.setUser_(user.getUserID());
//            personnelOvertime.setApproveStatus("1");
//            personnelOvertime.setActualType("0");//实际加班状态:0-未加班,1-已加班，2-实际未加班
            personnelOvertime.setAuditDate(new Date());
            Integer dept = 0;  //无部门的人员，在考勤中部门id为0
            if (!"".equals(user.getDepartment()) && user.getDepartment()!=null){
                dept=Integer.parseInt(user.getDepartment());
            }
            String type;
            if((type = workAttendanceOldService.overTimeType(null,user,begin,dept))==null
                    && (MyStrings.nulltoempty(type = personnelOvertime.getType()).isEmpty())) {
                type = NewDateUtils.isWeekend(begin) ? "2" : "1";
            }
            personnelOvertime.setType(type);  //有考勤的按照考勤设置返回加班类型；没有考勤的按照传值返回加班类型
            personnelOvertime.setActualState(personnelOvertime.getType());  //加班时段类型:1-工作日加班2-周末假日加班3-法定节假日加班

            personnelOvertime.setActualBeginTime(begin);
            personnelOvertime.setActualEndTime(end);
            personnelOvertime.setActualDuration(actualDuration);  //时长
            personnelOvertime.setActualReason(actualReason); //原因
            personnelOvertime.setActualApplyTime(new Date());  //申报申请时间
            personnelOvertime.setActualType("1");  //实际加班状态:0-未加班,1-已加班
            personnelOvertime.setApproveStatus("1");
            personnelOvertime.setAuditDate(new Date());
            personnelOvertimeDao.save(personnelOvertime);
        }
        return personnelOvertime;
    }

    @Override
    public PersonnelOvertime getPersonnelOvertimeById(Integer id) {
        return personnelOvertimeDao.get(id);
    }

    @Override   //type (1-计划加班 2-申报加班 3-批准加班)
    public void approvalOutTimeApproval(ApprovalProcess approvalProcess,User user,PersonnelOvertime outTime,Integer type,Integer id,Double approveDuration,String approveExplain) {
        approvalProcess.setApproveStatus("2");
        approvalProcess.setHandleTime(new Date());
        approvalProcess.setUserName(user.getUserName());//审批人名字
        approvalProcessService.updateApprovalProcess(approvalProcess);

        //找到加班审批项
        ApprovalItem approvalItem  = approvalService.getById(outTime.getApproveItem());
        ApprovalFlow thisFlow=approvalService.getApprovalFlowByItemIdAndLevel(approvalItem.getId(),approvalProcess.getLevel());

        if (type!=1) {
            if (approveDuration != null) {
                outTime.setApproveDuration(approveDuration);  //批准时长
                outTime.setApproveExplain(approveExplain);   //批准说明
            } else {
                outTime.setApproveDuration(outTime.getActualDuration());  //批准时长
            }
        }
        //总级别<=当前审批级别 或者 加班时长<=当前审批人审批时限  直接出结果，不再往下提交
        String content=null;
        String time=null;
        if (approvalItem.getLevel()<=approvalProcess.getLevel()||outTime.getDuration()<=thisFlow.getAmountCeiling()){
            if (type!=1){
                outTime.setFinalResult("1");   //0-其它,1-已完结的加班,2-未被认可的加班,3-提交了加班申请,但实际未加班,4-无需再填报时长的加班,5-加班申请被驳回'
                outTime.setApproveStatus("4");
                if (approveDuration!=null&&approveDuration==0){
                    outTime.setFinalResult("2");   //0-其它,1-已完结的加班,2-未被认可的加班,3-提交了加班申请,但实际未加班,4-无需再填报时长的加班,5-加班申请被驳回'
                }
                //将当天加班添加到考勤中
//                workAttendanceOldService.leaveOvertimeToAttendance(1,outTime.getUser_(),user.getUserID(),outTime.getId(),outTime.getType(),null,outTime.getApproveDuration(),outTime.getActualBeginTime(),outTime.getActualEndTime(),outTime.getReason());
                workAttendanceService.setAttendanceUserEvent(getToUpdateEvent(outTime.getId()));
            }else {
                outTime.setApproveStatus("2");
            }
            outTime.setAuditorName(user.getUserName());
            outTime.setAuditDate(new Date());

            if (type==1) {
                //给申请人 已批准推
                this.outTimeRejectSend(1,1,outTime, outTime.getUser_(), "/applyOutTimeApproval","您的加班申请被批准了","您的加班申请被批准了","applyOutTime");
                //到提交实际加班时间后仍未提交，则自动为实际未加班
                OverturnOverTime overturnOverTime = new OverturnOverTime(outTime.getId(), 4);
                ApprovalItem approvalItem1 = roleService.getCurrentItem(user.getOid(),"submitOvertimeRules"); //获取提交实际加班时限规则
                if (approvalItem1!=null){
                    if (approvalItem1.getUpperLimit().compareTo(new BigDecimal(-1))!=0){  //有具体的提前提交实际加班天数
                        Date submitTime = NewDateUtils.changeDay(outTime.getBeginTime(),approvalItem1.getUpperLimit().intValue()+1); //某一天的24：00前，就是后一天的0点0分0秒
                        clusterMessageSendingOperations.delayCall(submitTime, overturnOverTime);
                    }
                }

                //如果是加班开始时间与下班时间重合，那么加班开始打卡默认打上。(或者是加班结束时间与上班时间重合)
                workAttendanceService.addDefaultClockRecord(outTime,2,null,null);

            }else {
                List<ApprovalProcess> processList = approvalProcessService.getApprovalProcessByBusiness(id, 2, null);  //计划加班
                for (ApprovalProcess ap : processList) {
                    //给审批人的已批准发送
                    this.outTimeRejectSend(0, -1, outTime, ap.getToUser(), "/approvalOutTimeApproval", null, null, "approvalOutTime");
                }
            }
            //给申请人 待处理推
            this.outTimeRejectSend(0,-1,outTime,outTime.getUser_(),"/applyOutTimeHandle",null,null,"applyOutTime");
            switch (type) {
                case 1:
                    content = "您的加班申请被批准了！加班后，请及时申报实际加班的情况！";
                    time = "审批人："+" "+user.getUserName()+" "+NewDateUtils.dateToString(new Date(),sdf);
                    break;
                case 2:
                    content = "您申报的实际加班时长被批准通过了！";
                    time = "批准时间  " + NewDateUtils.dateToString(new Date(),sdf);
                    break;
                case 3:
                    if (approveDuration!=0) {
                        if (approveDuration.equals((double)approveDuration.intValue())) {
                            content = "您申报的实际加班时长被批准为" + approveDuration.intValue() + "小时！";
                        } else {
                            content = "您申报的实际加班时长被批准为" + approveDuration + "小时！";
                        }
                    }else {
                        content = "您申报的实际加班时长未被审批通过！";
                    }
                    time = "批准时间  " + NewDateUtils.dateToString(new Date(),sdf);
                    break;
            }

        }else{
            //否则向下一级审批者提交此申请
            if (type==3 &&approveDuration!=null&&approveDuration==0){   //当批准时长修改为0后，只需要一级审批者审批，审批完成后就直接结束
                outTime.setApproveDuration(approveDuration);  //批准时长
                outTime.setApproveExplain(approveExplain);   //批准说明approveExplain
                outTime.setApproveStatus("4");
                outTime.setAuditorName(user.getUserName());
                outTime.setAuditDate(new Date());
                outTime.setFinalResult("2");//0-其它,1-已完结的加班,2-未被认可的加班,3-提交了加班申请,但实际未加班,4-无需再填报时长的加班,5-加班申请被驳回'
                userMessageService.updatePersonnelOvertime(outTime);//结果

                content = "您申报的实际加班时长未被审批通过！";
                time = "批准时间  " + NewDateUtils.dateToString(new Date(),sdf);

                //将当天加班添加到考勤中
//                workAttendanceOldService.leaveOvertimeToAttendance(1,outTime.getUser_(),user.getUserID(),outTime.getId(),outTime.getType(),null,outTime.getApproveDuration(),outTime.getActualBeginTime(),outTime.getActualEndTime(),outTime.getReason());  //将当天加班添加到考勤中
                workAttendanceService.setAttendanceUserEvent(getToUpdateEvent(outTime.getId()));
                //给申请人 待处理推
                this.outTimeRejectSend(0,-1,outTime,outTime.getUser_(),"/applyOutTimeHandle",null,null,"applyOutTime");
            }else {
                if (type==3){
                    outTime.setApproveDuration(approveDuration);  //批准时长
                    outTime.setApproveExplain(approveExplain);   //批准说明
                    outTime.setAuditDate(new Date());
                    userMessageService.updatePersonnelOvertime(outTime);//结果
                }

                Integer handleId=null;
                String handleName="";//审批人总称
                String userName="";//审批人姓名
                Integer uid=null;
                Integer level=approvalProcess.getLevel()+1;
                for (ApprovalFlow f:approvalItem.getApprovalFlowHashSet()){
                    if (f.getLevel()==level){
                        uid=f.getToUserId();
                        handleName=f.getToUser();//审批人总称
                        userName=f.getUserName();
                    }
                }
                if (0==uid){
                    handleId=Integer.decode(user.getLeader());
//                        handleName="直接上级";
                    userName=userService.getUserByID(Integer.decode(user.getLeader())).getUserName();
                }else {
                    handleId=uid;
//                        handleName="指定审批人";
                }
                ApprovalProcess app=new ApprovalProcess();

                app.setApproveStatus("1");
                app.setBusiness(id);
                if (type==1){
                    app.setBusinessType(2);//业务类型  1-财务修改 2-计划加班 3-请假 4-入库 5-投诉 6-申报加班
                }else {
                    app.setBusinessType(6);//业务类型  1-财务修改 2-计划加班 3-请假 4-入库 5-投诉 6-申报加班
                }

                app.setToUser(handleId);
                app.setLevel(level);
                app.setToUserName(handleName);//审批人总称
                app.setUserName(userName);//审批人名称
                app.setCreateDate(new Date());
                app.setDescription(outTime.getUser().getUserName()+"的加班申请");
                app.setHandleTime(new Date());
                app.setFromUser(outTime.getUser().getUserID());//申请人 id
                app.setAskName(outTime.getUser().getUserName());//申请人名
                app.setOrg(user.getOid());
                approvalProcessService.saveApprovalProcess(app);

                //给下一级审批人 待处理推
                this.outTimeRejectSend(1,1,outTime,handleId,"/approvalOutTimeHandle",outTime.getUser().getUserName()+"的加班申请",outTime.getUser().getUserName()+"的加班申请","approvalOutTime");

                if (type!=1){
                    this.outTimeRejectSend(0,-1,outTime,handleId,"/approvalOutTimeApproval",null,null,"approvalOutTime");
                }
            }
        }
        if(content!=null) {
            if (type==1){  //不需跳轉
                userSuspendMsgService.saveUserSuspendMsg(1, content, time, outTime.getUser_(), null, null);
            }else {
                userSuspendMsgService.saveUserSuspendMsg(1, content, time, outTime.getUser_(), "applyOutTimeDetail", id);
            }
        }
    }

    @Override
    public void approvalOutTimeDisapproval(ApprovalProcess approvalProcess,User user,PersonnelOvertime outTime,String reason) {
        approvalProcess.setApproveStatus("3");
        approvalProcess.setHandleTime(new Date());
        approvalProcess.setUserName(user.getUserName());
        approvalProcess.setReason(reason);//驳回理由
        approvalProcessService.updateApprovalProcess(approvalProcess);

        outTime.setFinalResult("5"); //0-其它,1-已完结的加班,2-未被认可的加班,3-提交了加班申请,但实际未加班,4-无需再填报时长的加班,5-加班申请被驳回'
        outTime.setApproveStatus("3");
        outTime.setAuditorName(user.getUserName());
        outTime.setAuditDate(new Date());
        outTime.setApplyMemo(reason); //驳回理由
        userMessageService.updatePersonnelOvertime(outTime);//结果

    }

    @Override   //userId(审批人id)  approvalStatus（1-批准 2-驳回）  type (1-计划加班 2-申报加班 3-批准加班) approveDuration(批准时长) approveExplain（批准说明） reason（驳回原因） id（加班详情id）
    public void approvalOutTime(User user,Integer id,Integer type,Double approveDuration,String approveExplain,String reason, Integer approvalProcessId,String approvalStatus) {
        ApprovalProcess approvalProcess=approvalProcessService.getApprovalProcessById(approvalProcessId);
        Integer userId = user.getUserID();
        if(approvalProcess.getApproveStatus().equals("1")&&approvalProcess.getToUser().equals(userId)) {
            PersonnelOvertime outTime = personnelOvertimeDao.get(id);
            outTime.setApproveLevel(approvalProcess.getLevel());//最新的审批级次

            String content = null;
            String time = null;
            //1为批准
            if ("1".equals(approvalStatus)) {

                System.out.println("批准加班开始：" + new Date());
                this.approvalOutTimeApproval(approvalProcess, user, outTime, type, outTime.getId(), approveDuration, approveExplain);   //审批加班批准

                //给当前审批人 已批准推
                this.outTimeRejectSend(0, 1, outTime, userId, "/approvalOutTimeApproval", null, null, "approvalOutTime");

                System.out.println("批准加班结束：" + new Date());

            } else {   //否则皆为驳回
                System.out.println("驳回加班开始：" + new Date());

                this.approvalOutTimeDisapproval(approvalProcess, user, outTime, reason);   //审批加班驳回

                //给申请人 待处理推
                this.outTimeRejectSend(0, -1, outTime, outTime.getUser_(), "/applyOutTimeHandle", null, null, "applyOutTime");

                List<ApprovalProcess> processList = approvalProcessService.getApprovalProcessByBusiness(id, 2, null);  //计划加班
                for (ApprovalProcess ap : processList) {
                    //给审批人的已批准发送
                    this.outTimeRejectSend(0, -1, outTime, ap.getToUser(), "/approvalOutTimeApproval", null, null, "approvalOutTime");
                }

                // 新增 提示消息，并给申请人一个推送
                switch (type) {
                    case 1:
                        content = "您的加班申请被驳回了！";
                        break;
                    case 2:
                        content = "您申报的实际加班时长未被审批通过";
                        break;
                }
                time = "驳回时间  " + NewDateUtils.dateToString(new Date(), sdf);
                System.out.println("驳回加班结束：" + new Date());
            }
            if (content != null) {
                userSuspendMsgService.saveUserSuspendMsg(1, content, time, outTime.getUser_(), "applyOutTimeDetail", id);
            }
            //给当前审批人 待处理推
            this.outTimeRejectSend(-1, -1, outTime, userId, "/approvalOutTimeHandle", null, null, "approvalOutTime");
        }
    }

    @Override    //type 1-自己操作的实际未加班  4-到时未提交，系统默认的实际未加班
    public Integer deletePersonnelOvertime(Integer outTimeId,Integer type) {
        PersonnelOvertime p = personnelOvertimeDao.get(outTimeId);
        Integer s=0;
        if (p!=null){
            if (p.getActualType().equals("0")) {
                User user = userService.getUserByID(p.getUser_());
                p.setActualType("2");
                p.setUpdateName(user.getUserName());
                p.setUpdator(user.getUserID());
                p .setFinalResult("3"); //0-其它,1-已完结的加班,2-未被认可的加班,3-提交了加班申请,但实际未加班,4-无需再填报时长的加班,5-加班申请被驳回'
                if (type!=null&&4==type){  //4-到时未提交，系统默认的实际未加班
                    p .setFinalResult("6"); //0-其它,1-已完结的加班,2-未被认可的加班,3-提交了加班申请,但实际未加班,4-无需再填报时长的加班,5-加班申请被驳回,6-规定时间内未提交实际加班的数据(系统到时默认实际未加班)'
                }
                personnelOvertimeDao.update(p);

                List<ApprovalProcess> processList = approvalProcessService.getApprovalProcessByBusiness(outTimeId, 2, null);  //计划加班
                for (ApprovalProcess approvalProcess : processList) {
                    //给审批人的已批准发送
                    this.outTimeRejectSend(0, -1, p, approvalProcess.getToUser(), "/approvalOutTimeApproval", null, null, "approvalOutTime");
                    // 新增 我的消息提醒，并给审批人推送
                    userSuspendMsgService.saveUserSuspendMsg(1, user.getUserName() + "申请的" + new SimpleDateFormat("MM月dd日").format(p.getBeginTime()) + "要加班" + p.getDuration() + "小时，实际未加班。", "提交时间 " + NewDateUtils.dateToString(new Date(),sdf), approvalProcess.getToUser(), "approvalOutTimeDetail", outTimeId);
                }
                //给申请人的已批准发送
                this.outTimeRejectSend(-1, -1, p, p.getUser_(), "/applyOutTimeApproval", null, null, "applyOutTime");
                if (type!=null&&4==type){  //4-到时未提交，系统默认的实际未加班
                    // 新增 我的消息提醒，并给申请人推送
                    String content = "您有一条加班被系统记作了实际未加班，原因是规定时间内未提交实际加班的数据。";
                    String memo = "审批人："+" "+"系统"+" "+ NewDateUtils.dateToString(new Date(),sdf);
                    userSuspendMsgService.saveUserSuspendMsg(1, content, memo, user.getUserID(), null, null);
//                    userSuspendMsgService.saveUserSuspendMsg(1, content, memo, user.getUserID(), "approvalOutTimeDetail", outTimeId);
                }
                s=1;  //成功
            }else {
                s=2;  //已进行操作
            }
        }
        return s;
    }

    @Override
    public Map<String, Object> getApplicantOutTimes(Integer userId,Integer approverId,Integer state,String beginDate,String endDate,Map<String, Object> map) throws ParseException {
        String beginTime = null;
        String endTime = null;
        Date begin = null;
        Date end = null;
        List<PersonnelOvertime> outTimeList = new ArrayList<>();//申请人和审批人使用
        Double overtimeHours = 0.0;  //加班总时长
        Double generalHours = 0.0;  //平时加班时长
        Double weekendHours = 0.0;  //周六日加班总时长
        List<String> dateList = new ArrayList<>();  //存日期
        if (state!=null){
            if (state!=3){
                end = new Date();
                endTime = NewDateUtils.dateToString(end,"yyyy-MM-dd");
                if (state==1){   //本月
                    begin = NewDateUtils.changeMonth(new Date(),0);  //当月第一天
                    beginTime = NewDateUtils.dateToString(begin,"yyyy-MM-dd");
                }else if (state==2){  //本年
                    begin = NewDateUtils.getNewYearsDay();  //本年第一天
                    beginTime = NewDateUtils.dateToString(begin,"yyyy-MM-dd");
                    dateList = DateUtils.dateList(beginTime,endTime);
                }
            } else {  //自定义
                beginTime = beginDate+" 00:00:00";
                begin = NewDateUtils.dateFromString(beginTime,"yyyy-MM-dd HH:mm:ss");
                endTime = endDate+" 23:59:59";
                end = NewDateUtils.dateFromString(endTime,"yyyy-MM-dd HH:mm:ss");
                dateList = DateUtils.dateList(beginTime,endTime);
            }
            outTimeList=this.getPersonnelOvertimeListByBeginTime(userId,4,begin,end,null,1,approverId);  //已完成的加班（按加班开始时间倒序排列）【已加入审批人id，可以按照审批人id查询数据】
            for (PersonnelOvertime p:outTimeList) {
                if (!"".equals(p.getType()) && p.getType()!=null){  //1-工作日加班2-周末假日加班3-法定节假日加班'
                    if (!"3".equals(p.getType())){
                        if (p.getApproveDuration()!=null){
                            overtimeHours=overtimeHours+p.getApproveDuration();
                            if ("1".equals(p.getType())){
                                generalHours=generalHours+p.getApproveDuration();
                            }else {
                                weekendHours=weekendHours+p.getApproveDuration();
                            }
                        }
                    }
                }
            }
        }
        map.put("outTimeList",outTimeList); //审批人查看申请人数据列表
        map.put("beginTime",beginTime);//开始时间
        map.put("endTime",endTime);  //结束时间
        map.put("overtimeHours",overtimeHours);//加班总时长
        map.put("generalHours",generalHours);//平时加班时长
        map.put("weekendHours",weekendHours);//周六日加班总时长
        map.put("dateList",dateList);   //跨年或者跨月时的日期
        return map;
    }

    @Override
    public Map<String, Object> getApproverOutTimes(Integer userId,Integer state,String beginDate,String endDate,Map<String,Object> map) throws ParseException {
        String beginTime = null;
        String endTime = null;
        Date begin = null;
        Date end = null;
        List<PersonnelOvertime> outTimeList = new ArrayList<>();
        List<PersonnelOvertime> outTimeList1 = new ArrayList<>();
        Double overtimeHours = 0.0;  //加班总时长
        Double generalHours = 0.0;  //平时加班时长
        Double weekendHours = 0.0;  //周六日加班总时长
        Integer userNum = 0;

        List<String> dateList = new ArrayList<>();  //存日期
        if (userId!=null && state!=null){
            if (state!=3){
                end = NewDateUtils.getLastTimeOfDay(new Date());
                endTime = NewDateUtils.dateToString(end,"yyyy-MM-dd");
                if (state==1){   //本月
                    begin = NewDateUtils.changeMonth(new Date(),0);  //当月第一天
                    beginTime = NewDateUtils.dateToString(begin,"yyyy-MM-dd");
                }else if (state==2){  //本年
                    begin = NewDateUtils.getNewYearsDay();  //本年第一天
                    beginTime = NewDateUtils.dateToString(begin,"yyyy-MM-dd");
                    dateList = DateUtils.dateList(beginTime,endTime);
                }

                if (state==4){  //昨日
                    begin = NewDateUtils.yesterday();
                    end = begin;
                }

            } else {  //自定义
                beginTime = beginDate;
                endTime = endDate;
                dateList = DateUtils.dateList(beginTime,endTime);
            }

            outTimeList = this.getPersonnelOvertimeByApproverId(userId,"2",6,beginTime,endTime,null);  //根据加班开始时间和审批人id查询加班
            if (outTimeList.size()>0){
                outTimeList1 = removeDuplicateUser(outTimeList);  //根据用户id去重后剩余的所有用户(每个用户一条)
                userNum = outTimeList1.size();
                Integer num = 0;
                for (PersonnelOvertime personnelOverTime1:outTimeList1) {  //所有用户的列表
                    Double userTotalHours = 0.0; //每个人的加班总和
                    for (PersonnelOvertime personnelOvertime2:outTimeList) {  //所有的加班列表
                        if (personnelOvertime2.getApproveDuration()!=null){
                            //某个人的总加班时长
                            if (personnelOverTime1.getUser().getUserID().equals(personnelOvertime2.getUser().getUserID())){
                                userTotalHours = userTotalHours+personnelOvertime2.getApproveDuration();
                            }

                            if (num==0){
                                // 所有人的加班总时长、平时加班总时长、周六日加班总时长(只需遍历一遍)
                                overtimeHours = overtimeHours+personnelOvertime2.getApproveDuration();
                                if (!"".equals(personnelOvertime2.getType()) && personnelOvertime2.getType()!=null && "1".equals(personnelOvertime2.getType()) ){
                                    generalHours+=personnelOvertime2.getApproveDuration();
                                }else {
                                    weekendHours+=personnelOvertime2.getApproveDuration();
                                }
        }
    }
                    }
                    personnelOverTime1.setUserTotalHours(userTotalHours);  //某个人的加班总时长
                    num=1;
                }
            }
        }
        map.put("beginTime",beginTime);//开始时间
        map.put("endTime",endTime);  //结束时间
        map.put("outTimeList",outTimeList); //所有数据列表
        map.put("outTimeList1",outTimeList1); //根据用户id去重后剩余的所有用户(每个用户一条)
        map.put("userNum",userNum);//加班总人数
        map.put("overtimeHours",overtimeHours);//加班总时长
        map.put("generalHours",generalHours);//平时加班总时长
        map.put("weekendHours",weekendHours);//周六日加班总时长
        map.put("dateList",dateList);   //跨年或者跨月时的日期
        return null;
    }

    /**
     *<AUTHOR>
     *@date 2017/9/28 17:14
     *list中去除重复的id(在加班自定义查询的接口中使用)
     */
    private static ArrayList<PersonnelOvertime> removeDuplicateUser(List<PersonnelOvertime> users) {
        Set<PersonnelOvertime> set = new TreeSet<PersonnelOvertime>(new Comparator<PersonnelOvertime>()
        {
            @Override
            public int compare(PersonnelOvertime o1, PersonnelOvertime o2) {
                //字符串,则按照asicc码升序排列
                return o1.getUser().getUserID().compareTo(o2.getUser().getUserID());
            }
        });
        set.addAll(users);
        return new ArrayList<PersonnelOvertime>(set);
    }

    @Override
    public Map<String, Object> getOverTimeDetails(Integer outTimeId,Map<String,Object> map) {
        map.put("status",1);  //获取成功
        PersonnelOvertime personnelOvertime=personnelOvertimeDao.get(outTimeId);
        User overUser = userService.getUserByID(personnelOvertime.getUser_());  //加班的人员
        Integer dept = 0;  //部门其他的
        if (StringUtils.isNotEmpty(overUser.getDepartment())) {
            dept = Integer.parseInt(overUser.getDepartment());
        }
        PersonnelAttendanceConfig personnelAttendanceConfig = workAttendanceOldService.getPersonnelAttendanceConfigByDept(overUser.getOid(),dept,personnelOvertime.getBeginTime());  //获取加班人员那天的考勤规则
        if (personnelAttendanceConfig!=null&&personnelAttendanceConfig.getAttendancePattern().equals(WorkAttendanceOldService.ConfigAttendancePattern.attendanceTreasure.getIndex())){  //考勤宝的
            PersonnelAttendanceUserDetail personnelAttendanceUserDetail = workAttendanceService.getPersonnelAttendanceUserDetail(overUser.getUserID(), NewDateUtils.today(personnelOvertime.getBeginTime()), "8", outTimeId, null);
//            List<Map<String, Object>> punchList = new ArrayList<>();
            if (personnelAttendanceUserDetail!=null) {
                Map<String,Object> map1 = workAttendanceOldService.getClockRecord(overUser.getUserID(),(byte)7,personnelAttendanceUserDetail.getId(),null); //加班开始打卡时间，打卡得最早时间
                Map<String,Object> map2 = workAttendanceOldService.getClockRecord(overUser.getUserID(),(byte)8,personnelAttendanceUserDetail.getId(),1);  //加班结束打卡时间，打卡得最晚时间
                if (map1!=null&&map1.size()>0){
                    map.put("beginPunchTime",map1.get("punchTime"));  //最早打卡时间
                }else {
                    map.put("beginPunchTime",null);  //最早打卡时间
                    map.put("status", 2);   //不可以申报加班
                }
                if (map2!=null&&map2.size()>0){
                    map.put("endPunchTime",map2.get("punchTime"));  //最早打卡时间
                }else {
                    map.put("endPunchTime",null);  //最早打卡时间
                    map.put("status", 2);   //不可以申报加班
                }
            }else {
                map.put("status", 2);   //不可以申报加班
            }
            map.put("attendancePattern",personnelAttendanceConfig.getAttendancePattern());   //考勤模式:1-考勤宝,2-手工录入(默认)
        }else {
            map.put("attendancePattern",2);//考勤模式:1-考勤宝,2-手工录入(默认)
        }
        personnelOvertime.setUserName(personnelOvertime.getUser().getUserName());
        List<ApprovalProcess> processList=approvalProcessService.getApprovalProcessByBusiness(personnelOvertime.getId(),2,null);  //计划加班审批流程
        List<ApprovalProcess> processList1= approvalProcessService.getApprovalProcessByBusiness(personnelOvertime.getId(),6,null);  //申报加班审批流程
        Integer approvalProcessId = null;
        if (processList1.size()>0){
//            ApprovalProcess approvalProcess = processList1.get(processList1.size()-1);
            ApprovalProcess approvalProcess = processList1.stream().filter(process -> !Arrays.asList(ApprovalStatus.rejected.getName(), ApprovalStatus.withdraw.getName()).contains(process.getApproveStatus())).reduce((first, second) -> second).orElse(new ApprovalProcess(approvalProcessId));
            approvalProcessId = approvalProcess.getId();
            if(!MyStrings.nulltoempty(personnelOvertime.getApproveStatus()).isEmpty() && "1".equals(personnelOvertime.getApproveStatus()) && 1==approvalProcess.getLevel()){
                map.put("button",1);   //有“修改加班时长”的
            }else {
                map.put("button",0);   //无“修改加班时长”的按钮
            }
        }else if (processList.size()>0){
//            approvalProcessId = processList.get(processList.size()-1).getId();
            approvalProcessId = processList.stream().filter(process -> !ApprovalStatus.withdraw.getName().equals(process.getApproveStatus())).reduce((first, second) -> second).orElse(new ApprovalProcess(approvalProcessId)).getId();
            map.put("button",0);   //无“修改加班时长”的按钮
        }
        ApprovalItem approvalItem = roleService.getCurrentItem(personnelOvertime.getOrg(),"submitOvertimeRules");
        if (approvalItem!=null){
            map.put("upperLimit",approvalItem.getUpperLimit());
        }else {
            map.put("upperLimit","");
        }
        map.put("processList",processList);
        map.put("processList1",processList1);
        map.put("personnelOvertime",personnelOvertime);
        map.put("approvalProcessId",approvalProcessId);
        return map;
    }

    public boolean checkOverTimeOcupiedByUserTime(Date beginTime, Date endTime, User user,List<String> approveStatuses, Integer excludeId) {
        StringBuffer hql = new StringBuffer("select 1 from PersonnelOvertime where user_ = :userId and finalResult not in (:actualNotOverTime) and approveStatus in (:approveStatuses) and coalesce(actualBeginTime, beginTime) < :endTime and coalesce(actualEndTime, endTime) > :beginTime");
//        StringBuffer hql = new StringBuffer("select 1 from PersonnelOvertime where user_ = :userId and approveStatus in (:approveStatuses) and coalesce(actualBeginTime, beginTime) < :endTime and coalesce(actualEndTime, endTime) > :beginTime");
//        Map<String, Object> params = new HashMap<String, Object>(5){{
        Map<String, Object> params = new HashMap<String, Object>(4){{
            put("userId", user.getUserID());
            //施总说实际未加班不许提交补报加班，20250213改为可以补报加班
            put("actualNotOverTime", new ArrayList<String>(2){{
                add("3");//自己申请并审批通过的实际未加班
                add("6");//系统超时设置的实际未加班
            }});
            put("approveStatuses", approveStatuses);
            put("endTime", endTime);
            put("beginTime", beginTime);
        }};
        if (excludeId!=null) {
            hql.append(" and id != :excludeId");
            params.put("excludeId", excludeId);
        }
//        System.out.println(personnelOvertimeDao.getByHQLWithNamedParams(hql.toString(), params));
        return personnelOvertimeDao.getByHQLWithNamedParams(hql.toString(), params) != null;
    }

    @Override
    public Map<String, Object> addPlanOverTime(User user,PersonnelOvertime personnelOvertime, String beginTime1, String endTime1,Double ruleTime,Map<String,Object> map) {
        Pair<Date, Date> beginEnd = sortDatePair(Pair.of(NewDateUtils.dateFromString(beginTime1, sdf), NewDateUtils.dateFromString(endTime1, sdf)));
        beginTime1 = NewDateUtils.dateToString(beginEnd.getLeft(), sdf);
        endTime1 = NewDateUtils.dateToString(beginEnd.getRight(), sdf);
        ApprovalItem approvalItemRule = roleService.getCurrentItem(user.getOid(), "overtimeRule");  //获取加班的时间规则(提前量)
        if (approvalItemRule!=null&&ruleTime!=null&&approvalItemRule.getUpperLimit().compareTo(new BigDecimal(ruleTime))!=0) {
            map.put("status", 5);  //加班提前量进行了修改【操作失败，因为总务人员已修改了系统设置！】
        } else if (checkOverTimeOcupiedByUserTime(beginEnd.getLeft(), beginEnd.getRight(), user, Arrays.asList(ApprovalStatus.pending.getName(), ApprovalStatus.approved.getName()), null)) {
            map.put("status",7);
            map.put("content", "操作失败，因为您选择的时间已有加班！");
        } else {
            Integer dept = null;
            if (!MyStrings.nulltoempty(user.getDepartment()).isEmpty()) {
                dept = Integer.getInteger(user.getDepartment());
            }
            //获取考勤的开始时间、结束时间、午休开始和结束时间与加班时间对比
            Integer stateTime = workAttendanceOldService.getStateTime(user.getOid(), dept, NewDateUtils.today(NewDateUtils.dateFromString(beginTime1, "yyyy-MM-dd HH:mm:ss")), 1, beginTime1, endTime1);

            if (0 == stateTime) {
                map.put("status", 4);  //您申请中的时间有些问题，请重新申请！
            } else if (1 == stateTime) {   //加班申请的时间在正常的范围之内
                ApprovalItem approvalItem = roleService.getCurrentItem(user.getOid(), "overTimeApply");
                Double durationSub = this.getDuration(beginTime1, endTime1);  //提交时的加班时长
                Double duration = 0.0;
                for (ApprovalFlow f : approvalItem.getApprovalFlowHashSet()) {
                    if (f.getLevel() == approvalItem.getApprovalFlowHashSet().size()) {
                        duration = f.getAmountCeiling();
                        break;
                    }
                }

                //审批状态为1则需要审批，去找审批流程
                if (approvalItem.getStatus() == 1) {
                    if (durationSub <= duration) {
                        PersonnelOvertime personnelOvertime1 = this.addPersonnelOvertime(user, personnelOvertime, null, null, beginTime1, endTime1, "1");
                        System.out.println("计划加班申请开始：overtimeId:" + personnelOvertime1.getId());
                        personnelOvertime1.setApproveItem(approvalItem.getId());

                        Integer handleId = approvalProcessService.addApplyApprovalProcess(personnelOvertime1.getId(), 2, user, "加班申请", approvalItem);  //计划加班审批流程
                        if (NewDateUtils.dateFromString(beginTime1, sdf).getTime() <= new Date().getTime()) {
                            //给审批人的待处理发送
                            this.outTimeRejectSend(1, 1, personnelOvertime1, handleId, "/approvalOutTimeHandle", null, null, "approvalOutTime");
                            this.overturn(personnelOvertime1.getId(), 1);
                        } else {
                            //给审批人的待处理发送
                            this.outTimeRejectSend(1, 1, personnelOvertime1, handleId, "/approvalOutTimeHandle", user.getUserName() + "提交了加班申请", user.getUserName() + "提交了加班申请", "approvalOutTime");

                            //给申请人的待处理发送
                            this.outTimeRejectSend(0, 1, personnelOvertime1, user.getUserID(), "/applyOutTimeHandle", null, null, "applyOutTime");

                            //申请 到时间自动驳回
                            OverturnOverTime overturnOverTime = new OverturnOverTime(personnelOvertime1.getId(), 1);
                            clusterMessageSendingOperations.delayCall(personnelOvertime1.getBeginTime(), overturnOverTime);
                            System.out.println("计划加班申请 beginTime: " + personnelOvertime1.getBeginTime());
                        }
                        map.put("status", 1);//成功
                        System.out.println("计划加班申请结束：overtimeId:" + personnelOvertime1.getId());
                    } else {
                        map.put("status", 2);//高过审批人的审批时长
                        map.put("duration", duration);  //系统加班审批时长上限
                    }

                } else {    // 不需要审批，直接通过
                    PersonnelOvertime personnelOvertime1 = this.addPersonnelOvertime(user, personnelOvertime, null, null, beginTime1, endTime1, "1");
                    System.out.println("计划加班申请开始：overtimeId:" + personnelOvertime1.getId());

                    personnelOvertime1.setApproveItem(approvalItem.getId());
                    personnelOvertime1.setApproveStatus("2");
                    personnelOvertime1.setAuditDate(new Date());
                    userMessageService.updatePersonnelOvertime(personnelOvertime1);
                    map.put("status", 1);//成功
                    System.out.println("计划加班申请结束：overtimeId:" + personnelOvertime1.getId());
                }
            }
        }
        return map;
    }

    @Override
    public Map<String, Object> addDeclareOverTime(String sessionid,User user,Integer overtimeId,String beginTime1, String endTime1,Double actualDuration,String actualReason,Map<String, Object> map) {
        Integer tt = 0;//是否已经进行了申报加班 0-没有 1-已进行
        PersonnelOvertime personnelOvertime1 = new PersonnelOvertime();
        System.out.println("申报加班申请开始：overtimeId:"+overtimeId);
        if (overtimeId!=0){
            personnelOvertime1 = personnelOvertimeDao.get(overtimeId);
            if (!StringUtil.isNullOrEmpty(personnelOvertime1.getActualType()) && !"0".equals(personnelOvertime1.getActualType())){
                tt = 1;  //是否已经进行了申报加班 0-没有 1-已进行
            }
        }

        if (tt==0){
            Pair<Date, Date> beginEnd = sortDatePair(Pair.of(NewDateUtils.dateFromString(beginTime1, sdf), NewDateUtils.dateFromString(endTime1, sdf)));
            beginTime1 = NewDateUtils.dateToString(beginEnd.getLeft(), sdf);
            endTime1 = NewDateUtils.dateToString(beginEnd.getRight(), sdf);
            if (checkOverTimeOcupiedByUserTime(beginEnd.getLeft(), beginEnd.getRight(), user, Arrays.asList(ApprovalStatus.pending.getName(), ApprovalStatus.approved.getName()), personnelOvertime1.getId())) {
                map.put("status",7);
                map.put("content", "操作失败，因为您选择的时间已有加班！");
            } else {
                Integer dept = null;
                if (!MyStrings.nulltoempty(user.getDepartment()).isEmpty()) {
                    dept = Integer.parseInt(user.getDepartment());
                }
                //获取考勤的开始时间、结束时间、午休开始和结束时间与加班时间对比
                Integer stateTime = workAttendanceOldService.getStateTime(user.getOid(), dept, NewDateUtils.today(personnelOvertime1.getBeginTime()), 3, beginTime1, endTime1);
                if (0 == stateTime) {
                    map.put("status", 4);  //您申请中的时间有些问题，请重新申请！
                } else if (1 == stateTime) {   //加班申请的时间在正常的范围之内
                    ApprovalItem approvalItem = roleService.getApprovalItemById(personnelOvertime1.getApproveItem());

                    //判断申报时间与打卡时间，是否可以进行申报
                    PersonnelAttendanceConfig personnelAttendanceConfig = workAttendanceOldService.getPersonnelAttendanceConfigByDept(user.getOid(), dept, personnelOvertime1.getBeginTime());  //获取加班人员那天的考勤规则
                    if (personnelAttendanceConfig != null && personnelAttendanceConfig.getAttendancePattern().equals(WorkAttendanceOldService.ConfigAttendancePattern.attendanceTreasure.getIndex())) {  //考勤宝的
                        PersonnelAttendanceUserDetail personnelAttendanceUserDetail = workAttendanceService.getPersonnelAttendanceUserDetail(user.getUserID(), NewDateUtils.today(personnelOvertime1.getBeginTime()), "8", personnelOvertime1.getId(), null);
                        Date beginPunchTime = null;
                        Date endPunchTime = null;
                        if (personnelAttendanceUserDetail != null) {
                            Map<String, Object> map1 = workAttendanceOldService.getClockRecord(user.getUserID(), (byte) 7, personnelAttendanceUserDetail.getId(), null); //加班开始打卡时间，打卡得最早时间
                            Map<String, Object> map2 = workAttendanceOldService.getClockRecord(user.getUserID(), (byte) 8, personnelAttendanceUserDetail.getId(), 1);  //加班结束打卡时间，打卡得最晚时间
                            if (map1 != null) {
                                beginPunchTime = (Date) map1.get("punchTime");  //最早打卡时间
                            }
                            if (map2 != null) {
                                endPunchTime = (Date) map2.get("punchTime");  //最早打卡时间
                            }
                            if (beginPunchTime != null && endPunchTime != null && StringUtils.isNotEmpty(beginTime1) && StringUtils.isNotEmpty(endTime1)) {
                                Date begin = NewDateUtils.dateFromString(beginTime1, sdf);
                                Date end = NewDateUtils.dateFromString(endTime1, sdf);
                                //申报开始时间要大于开始打卡时间，申报结束时间要小于结束打卡时间(“实际开始时间”不得早于“加班开始打卡”时间，“实际结束时间”不得晚于“加班结束打卡”时间)
                                if (beginPunchTime.getTime() <= begin.getTime() && endPunchTime.getTime() >= end.getTime()) {
                                    map.put("status", 1);  //成功
                                } else {
                                    map.put("status", 0);  //失败
                                    clusterMessageSendingOperations.convertAndSendToUser(sessionid, "/notice", null, null, null, null, map);   //返回 处理结果的订阅
                                    return map;
                                }
                            } else {
                                map.put("status", 0); //失败
                                clusterMessageSendingOperations.convertAndSendToUser(sessionid, "/notice", null, null, null, null, map);   //返回 处理结果的订阅
                                return map;
                            }
                        }
                    }

                    //审批状态为1则需要审批，去找审批流程
                    if (approvalItem.getStatus() == 1) {
                        Double duration = 0.0;
                        for (ApprovalFlow approvalFlow : approvalItem.getApprovalFlowHashSet()) {
                            if (approvalFlow.getLevel() == approvalItem.getApprovalFlowHashSet().size()) {
                                duration = approvalFlow.getAmountCeiling();
                                break;
                            }
                        }
                        if (actualDuration <= duration) {
                            personnelOvertime1 = this.addPersonnelOvertime(user, personnelOvertime1, actualDuration, actualReason, beginTime1, endTime1, "2");

                            Integer handleId = approvalProcessService.addApplyApprovalProcess(personnelOvertime1.getId(), 6, user, "加班申请", approvalItem);  //申报加班审批流程

                            Integer num = 1;
                            if (num == 1) {
                                num = 2;
                                //给申请人的已批准发送
                                this.outTimeRejectSend(-1, -1, personnelOvertime1, user.getUserID(), "/applyOutTimeApproval", null, null, "applyOutTime");

                                //给审批人的已批准发送
                                this.outTimeRejectSend(0, -1, personnelOvertime1, handleId, "/approvalOutTimeApproval", null, null, "approvalOutTime");

                                //给审批人的待处理发送
                                this.outTimeRejectSend(1, 1, personnelOvertime1, handleId, "/approvalOutTimeHandle", user.getUserName() + "提交了实际加班申请", user.getUserName() + "提交了实际加班申请", "approvalOutTime");

                                //给申请人的待处理发送
                                this.outTimeRejectSend(0, 1, personnelOvertime1, user.getUserID(), "/applyOutTimeHandle", null, null, "applyOutTime");
                            }
                            map.put("status", 1);//成功
                        } else {
                            map.put("status", 3);//高于加班申请时长上限
                            map.put("duration", duration);  //系统加班审批时长上限
                        }

                    } else {    // 不需要审批，直接通过
                        personnelOvertime1 = this.addPersonnelOvertime(user, personnelOvertime1, actualDuration, actualReason, beginTime1, endTime1, "2");

                        personnelOvertime1.setApproveStatus("4");
                        personnelOvertime1.setAuditDate(new Date());
                        userMessageService.updatePersonnelOvertime(personnelOvertime1);
                        map.put("status", 1);//成功
                    }
                    System.out.println("申报加班申请结束：overtimeId:" + overtimeId);
                }
            }
        }else {
            map.put("status",2);//已进行申报加班，不能进行第二次申报申请
        }
        clusterMessageSendingOperations.convertAndSendToUser(sessionid,"/notice",null,null,null,null,map);   //返回 处理结果的订阅

        return map;
    }

    @Override
    public List<PersonnelOvertime> getPersonnelOvertimeListQuery(Integer userId,Integer type, Date beginDate, Date endDate) {
        Map<String,Object> map = new HashMap<>();
        String hql=" from PersonnelOvertime where user_=:userId";
//        switch (type){//type 1-已完成的加班 2-未被认可的加班 3-实际未加班 4-加班申请被驳回
//            case 1: hql+=" and actualType='1' and approveStatus='4' and approveDuration>0";
//                break;
//            case 2: hql+=" and actualType='1' and ((approveStatus='4' and approveDuration=0) or approveStatus='3')";
//                break;
//            case 3: hql+=" and actualType='2'";
//                break;
//            case 4: hql+=" and actualType='0' and approveStatus='3'";
//                break;
//        }
        if (type!=null){  //type 1-已完成的加班 2-未被认可的加班 3-实际未加班 4-无需再填报时长的加班,5-加班申请被驳回'
            if (3==type){
                hql += " and finalResult in (3,6)";  //3-实际未加班(自己操作的) 6-实际未加班(系统操作的)
            }else {
                hql += " and finalResult=:finalResult";
                map.put("finalResult", type.toString());
            }
        }
        if(type==1){
            hql+=" and beginTime>=:beginDate and endTime<=:endDate";
        }else {
            hql+=" and createDate>=:beginDate and createDate<=:endDate";
        }

        hql+=" order by beginTime desc";
        map.put("beginDate",beginDate);
        map.put("endDate",endDate);
        map.put("userId",userId);
        List<PersonnelOvertime> personnelOvertimeList=personnelOvertimeDao.getListByHQLWithNamedParams(hql,map);
        return personnelOvertimeList;
    }

    @Override
    public HashMap<String,Object> getOvertimeStatistics(List<PersonnelOvertime> personnelOvertimeList) {
        HashMap<String,Object> hashMap=new HashMap<>();
        List<PersonnelOvertime> pingShi=new ArrayList<>();
        List<PersonnelOvertime> jiaQi=new ArrayList<>();
        Double pingSum=0.0;
        Double jiaSum=0.0;
        for (PersonnelOvertime personnelOvertime:personnelOvertimeList){
            if ("1".equals(personnelOvertime.getType())){
                Double du=personnelOvertime.getApproveDuration();
                pingShi.add(personnelOvertime);
                if (du==null){
                    du=0.0;
                }
                pingSum+=du;
            }else {
                jiaQi.add(personnelOvertime);
                if(personnelOvertime.getApproveDuration()!=null){
                    jiaSum+=personnelOvertime.getApproveDuration();
                }
            }
        }
        Map<String,Object> jiaQiMap=new HashMap<>();
        jiaQiMap.put("count",jiaQi.size());
        jiaQiMap.put("sum",jiaSum);
        jiaQiMap.put("overtimeList",jiaQi);

        Map<String,Object> pingShiMap=new HashMap<>();
        pingShiMap.put("count",pingShi.size());
        pingShiMap.put("sum",pingSum);
        pingShiMap.put("overtimeList",pingShi);

        Map<String,Object> allMap=new HashMap<>();
        allMap.put("count",personnelOvertimeList.size());
        allMap.put("sum",pingSum+jiaSum);
        allMap.put("overtimeList",personnelOvertimeList);

        hashMap.put("allMap",allMap);
        hashMap.put("holiday",jiaQiMap);
        hashMap.put("weekday",pingShiMap);
        return hashMap;
    }

    @Override
    public HashMap<String,Object> getOverTimeSum(Integer userId, Integer type, Date beginDate, Date endDate,Integer applyId,boolean isApproval) {
        HashMap<String,Object> map=new HashMap<>();
        Date over=NewDateUtils.changeMonth(endDate, 0);
        over = over.getTime()>beginDate.getTime() ? over : beginDate;
        Double sumPingDurations=0.0;
        Double sumJiaDurations=0.0;
        Integer sumCounts=0;
        List<HashMap> hashMapList=new ArrayList<>();

        Long terminate=beginDate.getTime();
        for (;endDate.getTime()>=terminate;over=NewDateUtils.changeMonth(over, -1),endDate=NewDateUtils.getLastTimeOfMonth(over)) {
            HashMap<String, Object> hashMap = new HashMap<>();
            if(over.getTime()<terminate){
                over=beginDate;
            }
            if (type==1) {  //type 1-已完成的加班 2-未被认可的加班 3-实际未加班 4-加班申请被驳回
                Double pingDurations =0.0;
                Double jiaDurations = 0.0;
                if (isApproval){//审批人查询
                    pingDurations=this.overTimeApprovalDuration(userId, over, endDate, 1,applyId,type);
                    jiaDurations=this.overTimeApprovalDuration(userId, over, endDate, 2,applyId,type);
                }else {// 申请人查询
                    pingDurations=this.overTimeDuration(userId, over, endDate, 1,type);
                    jiaDurations=this.overTimeDuration(userId, over, endDate, 2,type);
                }
                sumPingDurations+=pingDurations;
                sumJiaDurations+=jiaDurations;
                hashMap.put("beginDate", over);
                hashMap.put("endDate", endDate);
                hashMap.put("pingDurations", pingDurations);//平时时长
                hashMap.put("jiaDurations", jiaDurations);//假期时长
                hashMap.put("count", 0);//这种情况不展示次数，加了只是为了统一key
            }else {//否则都查次数
                Integer count=0;
                if (isApproval){//审批人查询
                    count = this.overTimeApprovalCounts(userId, over, endDate, type,applyId);
                }else {// 申请人查询
                    count = this.overTimeCounts(userId, over, endDate, type);
                }
                sumCounts+=count;

                hashMap.put("beginDate", over);
                hashMap.put("endDate", endDate);
                hashMap.put("pingDurations", 0);//平时时长
                hashMap.put("jiaDurations", 0);//假期时长
                hashMap.put("count", count);//次数
            }
            hashMapList.add(hashMap);
        }
        map.put("sumPingDurations",sumPingDurations);
        map.put("sumJiaDurations",sumJiaDurations);
        map.put("sumCounts",sumCounts);
        map.put("hashMapList",hashMapList);
        return map;
    }

    private Double overTimeDuration(Integer userId,Date begin,Date end,Integer state,Integer type) {//state 1- 平时，2-假期
        Map<String,Object> map = new HashMap<>();
        String hql = "select sum(approveDuration) from PersonnelOvertime where user_=:userId";
        if (type!=null&&type==1){
            hql+=" and beginTime>=:beginDate and endTime<=:endDate ";
        }else {
            hql+=" and createDate>=:beginDate and createDate<=:endDate ";
        }

        switch (state) {//state 1- 平时，2-假期
            case 1:
                hql += " and actualState='1'";
                break;
            case 2:
                hql += " and actualState!='1'";
                break;
        }

//        switch (type){//type 1-已完成的加班 2-未被认可的加班 3-实际未加班 4-加班申请被驳回
//            case 1: hql+=" and actualType='1' and approveStatus='4'";
//                break;
//            case 2: hql+=" and actualType='1' and ((approveStatus='4' and approveDuration=0) or approveStatus='3')";
//                break;
//            case 3: hql+=" and actualType='2'";
//                break;
//            case 4: hql+=" and actualType='0' and approveStatus='3'";
//                break;
//        }
        if(type!=null){  //type 1-已完成的加班 2-未被认可的加班 3-实际未加班 4-无需再填报时长的加班,5-加班申请被驳回'
            if (3==type){
                hql += " and finalResult in (3,6)";  //3-实际未加班(自己操作的) 6-实际未加班(系统操作的)
            }else {
                hql += " and finalResult=:finalResult";
                map.put("finalResult", type.toString());
            }
        }
        map.put("userId",userId);
        map.put("beginDate",begin);
        map.put("endDate",end);
        Double durations= (Double) personnelOvertimeDao.getByHQLWithNamedParams(hql,map);
        if (durations==null){
            durations=0.0;
        }

        return durations;
    }

    private Integer overTimeCounts(Integer userId,Date begin,Date end,Integer type){
        Map<String,Object> map = new HashMap<>();
        String hql="select count(id) from PersonnelOvertime where user_=:userId  and createDate>=:beginDate and createDate<=:endDate";
//        switch (type){//type 1-已完成的加班 2-未被认可的加班 3-实际未加班 4-加班申请被驳回
//            case 2: hql+=" and actualType='1' and ((approveStatus='4' and approveDuration=0) or approveStatus='3')";
//                break;
//            case 3: hql+=" and actualType='2'";
//                break;
//            case 4: hql+=" and actualType='0' and approveStatus='3'";
//                break;
//        }
        if(type!=null){  //type 1-已完成的加班 2-未被认可的加班 3-实际未加班 4-无需再填报时长的加班,5-加班申请被驳回'
            if (3==type){
                hql += " and finalResult in (3,6)";  //3-实际未加班(自己操作的) 6-实际未加班(系统操作的)
            }else {
                hql += " and finalResult=:finalResult";
                map.put("finalResult", type.toString());
            }
        }
        map.put("userId",userId);
        map.put("beginDate",begin);
        map.put("endDate",end);
        Long durations= (Long) personnelOvertimeDao.getByHQLWithNamedParams(hql,map);
        return durations.intValue();
    }

    @Override
    public List<PersonnelOvertime> getApprovalOverTimeListQuery(Integer userId, Integer type, Date beginDate, Date endDate,Integer applyUserId) {
        HashMap<String, Object> map = new HashMap<>();
        String hql=" from PersonnelOvertime where";
        if (type!=null&&type==1){
            hql+=" beginTime>=:beginDate and endTime<=:endDate";
        }else {
            hql+=" createDate>=:beginDate and createDate<=:endDate";
        }
//        switch (type){//type 1-已完成的加班 2-未被认可的加班 3-实际未加班 4-加班申请被驳回
//            case 1: hql+=" and actualType='1' and approveStatus='4' and approveDuration>0";
//                break;
//            case 2: hql+=" and actualType='1' and ((approveStatus='4' and approveDuration=0) or approveStatus='3')";
//                break;
//            case 3: hql+=" and actualType='2'";
//                break;
//            case 4: hql+=" and actualType='0' and approveStatus='3'";
//                break;
//        }
        if (type!=null){  //type 1-已完成的加班 2-未被认可的加班 3-实际未加班 4-无需再填报时长的加班,5-加班申请被驳回'
            if (3==type){
                hql+=" and finalResult in (3,6)";
            }else {
                hql += " and finalResult=:finalResult";
                map.put("finalResult", type.toString());
            }
        }
        hql+=" and id in(select business from ApprovalProcess where toUser=:userId";
        if (1==type){
            hql+=" and businessType=6)";
        }else {
            hql+=" and businessType=2)";
        }

        map.put("userId",userId);
        map.put("beginDate",beginDate);
        map.put("endDate",endDate);

        if (applyUserId!=null){
            hql+= " and user_=:applyId";
            map.put("applyId",applyUserId);
        }
        hql+=" order by beginTime desc";
        List<PersonnelOvertime> personnelOvertimeList=personnelOvertimeDao.getListByHQLWithNamedParams(hql,map);
        return personnelOvertimeList;
    }

    private Double overTimeApprovalDuration(Integer userId,Date begin,Date end,Integer state,Integer applyId,Integer type) {//state 1- 平时，2-假期
        Map<String,Object> map = new HashMap<>();
        String hql = "select sum(approveDuration) from PersonnelOvertime where id in (select business from ApprovalProcess where toUser=:userId";
        if (type!=null&&1==type){
            hql+=" and businessType=6)";
        }else {
            hql+=" and businessType=2)";
        }
        if(type!=null){  //type 1-已完成的加班 2-未被认可的加班 3-实际未加班 4-无需再填报时长的加班,5-加班申请被驳回'
            if (3==type){
                hql += " and finalResult in (3,6)";  //3-实际未加班(自己操作的) 6-实际未加班(系统操作的)
            }else {
                hql += " and finalResult=:finalResult";
                map.put("finalResult", type.toString());
            }
        }
        switch (state) {//state 1- 平时，2-假期
            case 1:
                hql += " and actualState='1'";
                break;
            case 2:
                hql += " and actualState!='1'";
                break;
        }

//        switch (type){//type 1-已完成的加班 2-未被认可的加班 3-实际未加班 4-加班申请被驳回
//            case 1: hql+=" and actualType='1' and approveStatus='4'";
//                break;
//            case 2: hql+=" and actualType='1' and ((approveStatus='4' and approveDuration=0) or approveStatus='3')";
//                break;
//            case 3: hql+=" and actualType='2'";
//                break;
//            case 4: hql+=" and actualType='0' and approveStatus='3'";
//                break;
//        }

        if (applyId!=null){
            hql+= " and user_=:applyId";
            map.put("applyId",applyId);
        }
        if (type==1){
            hql+=" and beginTime>=:beginDate and endTime<=:endDate";
        }else {
            hql+=" and createDate>=:beginDate and createDate<=:endDate";
        }
        map.put("userId",userId);
        map.put("beginDate",begin);
        map.put("endDate",end);
        Double durations= (Double) personnelOvertimeDao.getByHQLWithNamedParams(hql,map);
        if (durations==null){
            durations=0.0;
        }
        return durations;
    }

    private Integer overTimeApprovalCounts(Integer userId,Date begin,Date end,Integer type,Integer applyId){
        Map<String,Object> map = new HashMap<>();
        String hql="select count(id) from PersonnelOvertime where id in(select business from ApprovalProcess where toUser=:userId";
//        switch (type){//type 1-已完成的加班 2-未被认可的加班 3-实际未加班 4-加班申请被驳回
//            case 2: hql+=" and actualType='1' and ((approveStatus='4' and approveDuration=0) or approveStatus='3')";
//                break;
//            case 3: hql+=" and actualType='2'";
//                break;
//            case 4: hql+=" and actualType='0' and approveStatus='3'";
//                break;
//        }
        if (type!=null&&1==type){
            hql+=" and businessType=6)";
        }else {
            hql+=" and businessType=2)";
        }
        map.put("userId",userId);
        if(type!=null){  //type 1-已完成的加班 2-未被认可的加班 3-实际未加班 4-无需再填报时长的加班,5-加班申请被驳回'
            if (3==type){
                hql += " and finalResult in (3,6)";  //3-实际未加班(自己操作的) 6-实际未加班(系统操作的)
            }else {
                hql += " and finalResult=:finalResult";
                map.put("finalResult", type.toString());
            }
        }

        if (applyId!=null){
            hql+= " and user_=:applyId";
            map.put("applyId",applyId);
        }
        if (begin!=null&&end!=null){
            hql+= " and createDate>=:beginDate and createDate<=:endDate";
            map.put("beginDate",begin);
            map.put("endDate",end);
        }
        Long durations= (Long) personnelOvertimeDao.getByHQLWithNamedParams(hql,map);
        return durations.intValue();
    }

    @Override
    public Integer getBadgeNumber(User user, String code) {
        Integer result = null;
        switch (code) {
            case "applyOutTime": //加班申请
                result = this.getOvertimeCounts(user.getUserID(),ApprovalStatus.approved.getName(),"0");//加班申请
                break;
        }
        return result;
    }

    @Override
    public Integer getPersonnelOvertimeCountsByOid(Integer oid) {
        String hql="select count(id) from PersonnelOvertime where user.oid=:oid and approveStatus in('1','2') and actualType!='2'";
        Map<String,Object> map = new HashMap<>();
        map.put("oid",oid);
        Long counts=(long)personnelOvertimeDao.getByHQLWithNamedParams(hql,map);
        return counts.intValue();
    }

    @Override
    public List<PersonnelOvertime> getPersonnelOvertimeHandleByTime(Integer userId, Date beginDate, Date endDate) {
        String hql="from PersonnelOvertime where beginTime>=:beginDate and beginTime<=:endDate and (approveStatus='1' or (approveStatus='2' and actualType='0'))";
        Map<String,Object> map = new HashMap<>();
        if (userId!=null){
            hql+= " and user_=:userId";
            map.put("userId",userId);
        }
        map.put("beginDate",beginDate);
        map.put("endDate",endDate);
        return personnelOvertimeDao.getListByHQLWithNamedParams(hql,map);
    }

    @Override
    public Map<String, Object> supplementaryOvertime(User user, String beginTime, String endTime, Double durationSub, String reason) {
        Map<String, Object> map = new HashMap<>(2);
        Pair<Date, Date> beginEnd = sortDatePair(Pair.of(NewDateUtils.dateFromString(beginTime, sdf), NewDateUtils.dateFromString(endTime, sdf)));
        beginTime = NewDateUtils.dateToString(beginEnd.getLeft(), sdf);
        endTime = NewDateUtils.dateToString(beginEnd.getRight(), sdf);
        ApprovalItem approvalItemSupplementary = roleService.getCurrentItem(user.getOid(), "supplementaryOvertime");  //补报加班的是否已关闭
        if (approvalItemSupplementary!=null&&!approvalItemSupplementary.isEnabled()){  //补报加班的,提交的时候按钮已关闭
            map.put("status",5);  //补报功能关闭的
            map.put("content", "操作失败，因为总务人员已修改了系统设置！");
        } else if (checkOverTimeOcupiedByUserTime(beginEnd.getLeft(), beginEnd.getRight(), user, Arrays.asList(ApprovalStatus.pending.getName(), ApprovalStatus.approved.getName()), null)) {
            map.put("status",7);
            map.put("content", "操作失败，因为您选择的时间已有加班！");
        } else {
            Integer dept = null;
            if (!MyStrings.nulltoempty(user.getDepartment()).isEmpty()) {
                dept = Integer.getInteger(user.getDepartment());
            }
            //获取考勤的开始时间、结束时间、午休开始和结束时间与加班时间对比
            Integer stateTime = 0;
            if (NewDateUtils.dateFromString(endTime, sdf).getTime() <= new Date().getTime()) {    //补报加班都是过去的时间
                stateTime = workAttendanceOldService.getStateTime(user.getOid(), dept, NewDateUtils.today(NewDateUtils.dateFromString(beginTime, "yyyy-MM-dd HH:mm:ss")), 3, beginTime, endTime);
            }
            if (0 == stateTime) {
                map.put("status", 4);  //您申请中的时间有些问题，请重新申请！
            } else if (1 == stateTime) {   //加班申请的时间在正常的范围之内
                ApprovalItem approvalItem = roleService.getCurrentItem(user.getOid(), "overTimeApply");
                Double duration = 0.0;
                for (ApprovalFlow f : approvalItem.getApprovalFlowHashSet()) {
                    if (f.getLevel() == approvalItem.getApprovalFlowHashSet().size()) {
                        duration = f.getAmountCeiling();
                        break;
                    }
                }

                //审批状态为1则需要审批，去找审批流程
                if (durationSub <= duration) {
                    PersonnelOvertime personnelOvertime1 = this.addPersonnelOvertime(user, new PersonnelOvertime(), durationSub, reason, beginTime, endTime, "3");
                    System.out.println("补报加班申请开始：overtimeId:" + personnelOvertime1.getId());
                    personnelOvertime1.setApproveItem(approvalItem.getId());

                    Integer handleId = approvalProcessService.addApplyApprovalProcess(personnelOvertime1.getId(), 6, user, "加班申请", approvalItem);  //申报加班审批流程

                    //给审批人的待处理发送
                    this.outTimeRejectSend(1, 1, personnelOvertime1, handleId, "/approvalOutTimeHandle", user.getUserName() + "提交了加班申请", user.getUserName() + "提交了加班申请", "approvalOutTime");

                    //给申请人的待处理发送
                    this.outTimeRejectSend(0, 1, personnelOvertime1, user.getUserID(), "/applyOutTimeHandle", null, null, "applyOutTime");

                    //申请 到时间自动驳回
                    OverturnOverTime overturnOverTime = new OverturnOverTime(personnelOvertime1.getId(), 2);
                    clusterMessageSendingOperations.delayCall(personnelOvertime1.getBeginTime(), overturnOverTime);
                    System.out.println("补报加班申请 beginTime: " + personnelOvertime1.getBeginTime());

                    map.put("status", 1);//成功
                    System.out.println("补报加班申请结束：overtimeId:" + personnelOvertime1.getId());
                } else {
                    map.put("status", 2);//高过审批人的审批时长
                    map.put("duration", duration);  //系统加班审批时长上限
                }
            }
        }
        return map;
    }

    /**
     * 指派加班
     * @param user 登录人
     * @param beginTime 开始时间
     * @param endTime  结束时间
     * @param durationSub 时长
     * @param reason 事由
     * @param overtimeUserIds  需加班者id，用逗号隔开
     * @return
     */
    @Override
    public Map<String, Object> assignOvertime(User user, String beginTime, String endTime, Double durationSub, String reason, String overtimeUserIds) {
        Map<String,Object> map = new HashMap<>();
        if (StringUtils.isNotEmpty(beginTime)&&StringUtils.isNotEmpty(endTime)&&durationSub!=null&&StringUtils.isNotEmpty(reason)&&StringUtils.isNotEmpty(overtimeUserIds)){
            String[] overtimeUsers = overtimeUserIds.split(",");
            String uuid = UUID.randomUUID().toString();
            for (String overtimeUserId:overtimeUsers) {
                User user1 = userService.getUserByID(Integer.parseInt(overtimeUserId));  //需加班者
                Integer dept = null;
                if (!MyStrings.nulltoempty(user.getDepartment()).isEmpty()){
                    dept = Integer.getInteger(user.getDepartment());
                }
                //获取考勤的开始时间、结束时间、午休开始和结束时间与加班时间对比
                Integer stateTime = workAttendanceOldService.getStateTime(user.getOid(), dept, NewDateUtils.today(NewDateUtils.dateFromString(beginTime, "yyyy-MM-dd HH:mm:ss")), 3, beginTime, endTime);
                if (0==stateTime){
                    map.put("status",3);  //您申请中的时间有些问题，请重新申请！
                    map.put("content","您申请中的时间有些问题，请重新申请！");  //您申请中的时间有些问题，请重新申请！
                    break;
                }else if (1==stateTime) {   //加班申请的时间在正常的范围之内
                    ApprovalItem approvalItem = roleService.getCurrentItem(user.getOid(), "overTimeApply");
                    Double duration = 0.0;
                    for (ApprovalFlow f : approvalItem.getApprovalFlowHashSet()) {
                        if (f.getLevel() == approvalItem.getApprovalFlowHashSet().size()) {
                            duration = f.getAmountCeiling();
                            break;
                        }
                    }
                    if (durationSub<=duration) {
                        PersonnelOvertime personnelOvertime = new PersonnelOvertime();
                        personnelOvertime.setBeginTime(NewDateUtils.dateFromString(beginTime, "yyyy-MM-dd HH:mm:ss"));
                        personnelOvertime.setEndTime(NewDateUtils.dateFromString(endTime, "yyyy-MM-dd HH:mm:ss"));
                        personnelOvertime.setDuration(durationSub);
                        personnelOvertime.setReason(reason);
                        personnelOvertime.setUser(user1);
                        personnelOvertime.setUser_(user1.getUserID());
                        personnelOvertime.setAssigner(user.getUserID());
                        personnelOvertime.setAssignerName(user.getUserName());
                        personnelOvertime.setAssigneTime(new Date());
                        personnelOvertime.setAssignUuid(uuid);
                        personnelOvertime.setFinalResult("0");
                        personnelOvertime.setActualType("0");
                        personnelOvertime.setOrg(user1.getOid());
                        personnelOvertimeDao.save(personnelOvertime);

                        ApprovalProcess app = new ApprovalProcess();
                        app.setLevel(1);
                        app.setApproveStatus("1");
                        app.setToUser(user1.getUserID());
                        app.setBusiness(personnelOvertime.getId());
                        app.setBusinessType(60);//业务类型  1-财务修改，2- 加班，3-请假
                        app.setToUserName("指定审批人");//审批人总称
                        app.setUserName(user1.getUserName());//审批人名称
                        app.setCreateDate(new Date());
//                        app.setDescription(user.getUserName() +"的"+description);
                        app.setHandleTime(new Date());
                        app.setFromUser(user.getUserID());//申请人 id
                        app.setAskName(user.getUserName());//申请人名
                        app.setOrg(user.getOid());
                        approvalProcessService.saveApprovalProcess(app);

                        //给审批人的待处理发送
                        this.outTimeRejectSend(1, 1, personnelOvertime, user1.getUserID(), "/assignOutTimeHandle", null, null, "assignOutTime");

                        //申请 到时间自动驳回
                        OverturnOverTime overturnOverTime = new OverturnOverTime(personnelOvertime.getId(),3);  //加班指派
                        clusterMessageSendingOperations.delayCall(personnelOvertime.getBeginTime(), overturnOverTime);
                    }else {
                        map.put("status", 2);//高过审批人的审批时长
                        map.put("duration", duration);  //系统加班审批时长上限
                        map.put("content", "高过审批人的审批时长");  //系统加班审批时长上限
                        break;
                    }
                }
            }
            map.put("status", 1);
            map.put("content", "操作成功");

        }else {
            map.put("status", 0);
            map.put("content", "缺少传值");
        }
        return map;
    }

    @Override
    public List<PersonnelOvertime> assignOvertimeList(Integer userId, String approveStatus,Date beginDate,Date endDate) {
        Map<String,Object> map = new HashMap<>();
        String hql = "from PersonnelOvertime where id in (select business from ApprovalProcess where businessType=60";
        if (beginDate!=null){
            hql+=" and createDate>=:beginDate";
            map.put("beginDate",beginDate);
        }

        if (endDate!=null){
            hql+=" and createDate<=:endDate";
            map.put("endDate",endDate);
        }

        if (StringUtils.isNotEmpty(approveStatus)){
            hql+=" and approveStatus=:approveStatus)";
            map.put("approveStatus",approveStatus);
        }else {
            hql+=")";
        }

        if (userId!=null){
            hql+=" and user_=:userId";
            map.put("userId",userId);
        }
        List<PersonnelOvertime> personnelOvertimes = personnelOvertimeDao.getListByHQLWithNamedParams(hql,map);
        return personnelOvertimes;
    }

    @Override
    public Map<String, Object> approvalAssignOutTime(User user,Integer assignOvertimeId, String approveStatus, String reason) {
        Map<String,Object> map = new HashMap<>();
        if (assignOvertimeId!=0){
            PersonnelOvertime outTime = personnelOvertimeDao.get(assignOvertimeId);  //approvalProcessId
            ApprovalProcess approvalProcess=approvalProcessService.getApprovalProcessByBusToUser(assignOvertimeId,60,user.getUserID());
            if(approvalProcess.getApproveStatus().equals("1")&&approvalProcess.getToUser().equals(user.getUserID())) {
                String content;  //消息的描述
                String time = "操作时间 " + user.getUserName() + " " + NewDateUtils.dateToString(new Date(), sdf);  //消息的时间格式
                //1为批准
                if ("1".equals(approveStatus)) {
                    if (checkOverTimeOcupiedByUserTime(outTime.getActualBeginTime() != null ? outTime.getActualBeginTime() : outTime.getBeginTime(), outTime.getActualEndTime() !=null ? outTime.getActualEndTime() : outTime.getEndTime(),
                            user, Arrays.asList(ApprovalStatus.pending.getName(), ApprovalStatus.approved.getName()), outTime.getId())) {
                        map.put("status", 7);
                        map.put("content", "操作失败，因为您选择的时间已有加班！");
                        System.out.println("approvalAssignOutTime : " + JSON.toJSONString(map));
                        return map;
                    } else {
                        outTime.setApproveLevel(approvalProcess.getLevel());//最新的审批级次
                        approvalProcess.setApproveStatus("2");
                        approvalProcess.setHandleTime(new Date());
//                        approvalProcess.setUserName(user.getUserName());//审批人名字
                        approvalProcessService.updateApprovalProcess(approvalProcess);

                        ApprovalItem approvalItem = roleService.getCurrentItem(user.getOid(), "overTimeApply");

                        //审批状态为1则需要审批，去找审批流程
                        if (approvalItem.getStatus() == 1) {
                            outTime = this.addPersonnelOvertime(user, outTime, null, null, null, null, "4");
                            outTime.setApproveItem(approvalItem.getId());

                            //指派加班审批后，直接为计划加班的申请流程
                            Integer handleId = approvalProcessService.addApplyApprovalProcess(outTime.getId(), 2, user, "加班申请", approvalItem);  //计划加班审批流程
                            if (outTime.getBeginTime().getTime() <= new Date().getTime()) {
                                //给加班审批人的待处理发送
                                this.outTimeRejectSend(1, 1, outTime, handleId, "/approvalOutTimeHandle", null, null, "approvalOutTime");
                                this.overturn(outTime.getId(), 1);
                            } else {
                                //给加班审批人的待处理发送
                                this.outTimeRejectSend(1, 1, outTime, handleId, "/approvalOutTimeHandle", user.getUserName() + "提交了加班申请", user.getUserName() + "提交了加班申请", "approvalOutTime");

                                //给加班申请人的待处理发送
                                this.outTimeRejectSend(0, 1, outTime, user.getUserID(), "/applyOutTimeHandle", null, null, "applyOutTime");

                                //申请 到时间自动驳回
                                OverturnOverTime overturnOverTime = new OverturnOverTime(outTime.getId(), 1);
                                clusterMessageSendingOperations.delayCall(outTime.getBeginTime(), overturnOverTime);
                            }

                        } else {    // 不需要审批，直接通过
                            outTime = this.addPersonnelOvertime(user, outTime, null, null, null, null, "4");
//                        System.out.println("计划加班申请开始：overtimeId:" + personnelOvertime1.getId());

                            outTime.setApproveItem(approvalItem.getId());
                            outTime.setApproveStatus("2");
                            outTime.setAuditDate(new Date());
                            userMessageService.updatePersonnelOvertime(outTime);
//                        System.out.println("计划加班申请结束：overtimeId:" + outTime.getId());
                        }
                        //给当前审批人 待处理推
//                    this.outTimeRejectSend(-1, -1, outTime, user.getUserID(), "/assignOutTimeHandle", null, null, "assignOutTime");
                        content = user.getUserName() + "已同意" + NewDateUtils.dateToString(outTime.getBeginTime(), "yyyy-MM-dd") + "来加班！";
                    }
                } else {   //否则皆为驳回
                    approvalProcess.setApproveStatus("3");
                    approvalProcess.setHandleTime(new Date());
                    approvalProcess.setUserName(user.getUserName());
                    approvalProcess.setReason(reason);//驳回理由
                    approvalProcessService.updateApprovalProcess(approvalProcess);

                    outTime.setApproveStatus("3");
                    outTime.setAuditorName(user.getUserName());
                    outTime.setAuditDate(new Date());
                    outTime.setApplyMemo(reason); //驳回理由
                    outTime.setReplyReason(reason);
                    personnelOvertimeDao.update(outTime);//结果

                    content = user.getUserName() + "未同意" + NewDateUtils.dateToString(outTime.getBeginTime(), "yyyy-MM-dd") + "来加班！";
                }
                userSuspendMsgService.saveUserSuspendMsg(1, content, time, outTime.getAssigner(), "overtimeAssignDetail", outTime.getId());
                //给当前审批人 待处理推
                this.outTimeRejectSend(-1, -1, outTime, user.getUserID(), "/assignOutTimeHandle", null, null, "assignOutTime");

                map.put("status", 1);
                map.put("content", "操作成功");
            } else {
                map.put("status", 2);
                map.put("content", "已处理");
            }
        }else {
            map.put("status", 0);
            map.put("content", "缺少传值");
        }
        return map;
    }

    @Override
    public JsonResult progressiveApprovalOutTime(Integer approvalProcessId, Integer id, Integer type, Double approveDuration, String approveExplain, String reason, String approvalStatus, String sessionid, User user) {
        ApprovalProcess approvalProcess;
        if (id == null && !ApprovalStatus.withdraw.getName().equals(approvalStatus)
                || approvalProcessId == null
                || (approvalProcess=approvalProcessService.getApprovalProcessById(approvalProcessId))==null) {
            return new JsonResult(1, 0);//参数错误，操作失败
        }
        Integer status=1;  //操作成功
        if (ApprovalStatus.withdraw.getName().equals(approvalStatus)) {
            if(ApprovalStatus.pending.getName().equals(approvalProcess.getApproveStatus())) {
                approvalProcess.setApproveStatus(ApprovalStatus.withdraw.getName());
                approvalProcessService.updateApprovalProcess(approvalProcess);
                PersonnelOvertime outTime = personnelOvertimeDao.get(approvalProcess.getBusiness());
                if (outTime != null) {
                    outTime.setApproveStatus(ApprovalStatus.withdraw.getName());
                    personnelOvertimeDao.update(outTime);
                }
                updateApprovalProcessUsersBadgeNumber(popedomTaskService, userService, approvalProcess);
            } else {
                status = 4; //已批准
            }
        } else {
            if(ApprovalStatus.withdraw.getName().equals(approvalProcess.getApproveStatus())) {
                status = 9; //已取消
            } else if (ApprovalStatus.pending.getName().equals(approvalProcess.getApproveStatus()) && approvalProcess.getToUser().equals(user.getUserID())) {
                approvalOutTime(user, id, type, approveDuration, approveExplain, reason, approvalProcessId, approvalStatus);   //审批加班
            } else {
                status = 4; //已处理过
            }
        }
        return new JsonResult(1,status);
    }
}