package cn.sphd.miners.modules.resourceAuthority.mapper;

import cn.sphd.miners.modules.accountant.mapper.BaseMapper;
import cn.sphd.miners.modules.resourceAuthority.entity.ResAcl;
import cn.sphd.miners.modules.resourceAuthority.entity.ResEntity;
import cn.sphd.miners.modules.system.dto.UserDto;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

/**
 * Created by 朱思旭 on 2017/10/31.
 */
@Component
public interface ResAclMapper extends BaseMapper<ResAcl> {

    int delAuthByCategoryAndUser(HashMap map);

    int insertAuthByCategoryAuth(ResEntity r);

    //获取某文件的全部权限
    List<ResAcl> getFileAuth(Integer FileId);
    //得到某文件的全部UserId
    List<Integer> getAllUserIdsByFileId(Integer fileId);

    //删除某文件的全部权限
    int delAllAuthByFile(Integer fileId);

    //根据文件ID和user删除权限
    int delAuthByFileAndUser(ResAcl r);

    //利用categoryId和userId查询权限是否存在。
    Integer count(ResAcl r);

    //得到某部门没有被选择过的全部人员
    List<UserDto> listUserByResource(HashMap map);

    //得到某部门的全部人员
    List<UserDto> listAllUserByResource(HashMap map);

}
