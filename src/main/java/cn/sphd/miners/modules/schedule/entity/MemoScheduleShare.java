package cn.sphd.miners.modules.schedule.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by Administrator on 2020/7/28.
 * 日程分享表
 */
@Entity
@Table(name = "t_memo_schedule_share")
public class MemoScheduleShare {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="memo_schedule"  /*, nullable=true , unique=false, insertable=false, updatable=false*/)
    private Integer scheduleId;//日程ID

    @Column(name="schedule_uid"  , length=36 , nullable=true , unique=false)
    private String scheduleUid;   //日程的唯一标识符。 此值用于标识分布式提醒

    @Column(name="title"  , length=100 , nullable=true , unique=false)
    private String title;   //标题

    @Column(name="memo"  , length=100 , nullable=true , unique=false)
    private String memo;   //备注

    @Column(name="recipients"   , nullable=true , unique=false)
    private Integer recipients; //分享人数

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_time"   , nullable=true , unique=false)
    private Date createTime;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_time"   , nullable=true , unique=false)
    private Date updateTime;

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;//操作:1-增,2-删,3-改

    @Column(name="previous_id"   , nullable=true , unique=false)
    private Integer previousId;

    @Column(name="version_no"   , nullable=true , unique=false)
    private Integer versionNo;


    @OrderBy("id")
    @OneToMany(targetEntity=MemoScheduleShareRecipients.class, fetch= FetchType.LAZY, mappedBy="scheduleShare", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<MemoScheduleShareRecipients> memoScheduleShareRecipientsHashSet = new HashSet<MemoScheduleShareRecipients>();


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getScheduleId() {
        return scheduleId;
    }

    public void setScheduleId(Integer scheduleId) {
        this.scheduleId = scheduleId;
    }

    public String getScheduleUid() {
        return scheduleUid;
    }

    public void setScheduleUid(String scheduleUid) {
        this.scheduleUid = scheduleUid;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getRecipients() {
        return recipients;
    }

    public void setRecipients(Integer recipients) {
        this.recipients = recipients;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public Set<MemoScheduleShareRecipients> getMemoScheduleShareRecipientsHashSet() {
        return memoScheduleShareRecipientsHashSet;
    }

    public void setMemoScheduleShareRecipientsHashSet(Set<MemoScheduleShareRecipients> memoScheduleShareRecipientsHashSet) {
        this.memoScheduleShareRecipientsHashSet = memoScheduleShareRecipientsHashSet;
    }
}
