package cn.sphd.miners.modules.forumArea.entity;


import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2020-05-06
 * @description
 **/
@Entity(name="ForumPostHistory")
@Table(name = "t_forum_post_history")
public class ForumPostHistory implements Serializable {
  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;
  @Column(name = "org")
  private Integer org;
  @Column(name = "post")
  private Integer post;
  @Column(name = "category")
  private Integer category;
  @Column(name = "name")
  private String name;
  @Column(name = "title")
  private String title;
  @Column(name = "file_sn")
  private String fileSn;
  @Column(name = "content")
  private String content;
  @Column(name = "size")
  private Integer size;
  @Column(name = "keyword")
  private String keyword;
  @Column(name = "valid")
  private Integer valid;
  @Column(name = "valid_time")
  private Date validTime;
  @Column(name = "is_archived")
  private Integer isArchived;
  @Column(name = "is_cleaned")
  private Integer isCleaned;
  @Column(name = "clean_time")
  private Date cleanTime;
  @Column(name = "enabled")
  private Integer enabled;
  @Column(name = "enabled_time")
  private Date enabledTime;
  @Column(name = "is_stick")
  private Integer isStick;
  @Column(name = "participants_num")
  private Integer participantsNum;
  @Column(name = "read_num")
  private Integer readNum;
  @Column(name = "reply_num")
  private Integer replyNum;
  @Column(name = "lastest_reply_time")
  private Date lastestReplyTime;
  @Column(name = "download_num")
  private Integer downloadNum;
  @Column(name = "memo")
  private String memo;
  @Column(name = "compere")
  private Integer compere;
  @Column(name = "compere_name")
  private String compereName;
  @Column(name = "creator")
  private Integer creator;
  @Column(name = "create_name")
  private String createName;
  @Column(name = "create_date")
  private Date createDate;
  @Column(name = "updator")
  private Integer updator;
  @Column(name = "update_name")
  private String updateName;
  @Column(name = "update_date")
  private Date updateDate;
  @Column(name = "approve_item")
  private Integer approveItem;
  @Column(name = "approve_status")
  private String approveStatus;
  @Column(name = "approve_level")
  private Integer approveLevel;
  @Column(name = "auditor")
  private Integer auditor;
  @Column(name = "auditor_name")
  private String auditorName;
  @Column(name = "audit_date")
  private Date auditDate;
  @Column(name = "apply_memo")
  private String applyMemo;
  @Column(name = "approve_memo")
  private String approveMemo;
  @Column(name = "message_id")
  private Integer messageId;
  @Column(name = "operation")
  private String operation;
  @Column(name = "previous_id")
  private Integer previousId;
  @Column(name = "version_no")
  private Integer versionNo;

  @Transient
  private List<ForumPostUserHistory> listForumPostUserHistory;

  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Integer getOrg() {
    return org;
  }

  public void setOrg(Integer org) {
    this.org = org;
  }

  public Integer getPost() {
    return post;
  }

  public void setPost(Integer post) {
    this.post = post;
  }

  public Integer getCategory() {
    return category;
  }

  public void setCategory(Integer category) {
    this.category = category;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public String getTitle() {
    return title;
  }

  public void setTitle(String title) {
    this.title = title;
  }

  public String getFileSn() {
    return fileSn;
  }

  public void setFileSn(String fileSn) {
    this.fileSn = fileSn;
  }

  public String getContent() {
    return content;
  }

  public void setContent(String content) {
    this.content = content;
  }

  public Integer getSize() {
    return size;
  }

  public void setSize(Integer size) {
    this.size = size;
  }

  public String getKeyword() {
    return keyword;
  }

  public void setKeyword(String keyword) {
    this.keyword = keyword;
  }

  public Integer getValid() {
    return valid;
  }

  public void setValid(Integer valid) {
    this.valid = valid;
  }

  public Integer getEnabled() {
    return enabled;
  }

  public void setEnabled(Integer enabled) {
    this.enabled = enabled;
  }

  public Integer getIsStick() {
    return isStick;
  }

  public void setIsStick(Integer isStick) {
    this.isStick = isStick;
  }

  public Integer getParticipantsNum() {
    return participantsNum;
  }

  public void setParticipantsNum(Integer participantsNum) {
    this.participantsNum = participantsNum;
  }

  public Integer getReadNum() {
    return readNum;
  }

  public void setReadNum(Integer readNum) {
    this.readNum = readNum;
  }

  public Integer getReplyNum() {
    return replyNum;
  }

  public void setReplyNum(Integer replyNum) {
    this.replyNum = replyNum;
  }

  public Date getLastestReplyTime() {
    return lastestReplyTime;
  }

  public void setLastestReplyTime(Date lastestReplyTime) {
    this.lastestReplyTime = lastestReplyTime;
  }

  public Integer getDownloadNum() {
    return downloadNum;
  }

  public void setDownloadNum(Integer downloadNum) {
    this.downloadNum = downloadNum;
  }

  public String getMemo() {
    return memo;
  }

  public void setMemo(String memo) {
    this.memo = memo;
  }

  public Integer getCompere() {
    return compere;
  }

  public void setCompere(Integer compere) {
    this.compere = compere;
  }

  public String getCompereName() {
    return compereName;
  }

  public void setCompereName(String compereName) {
    this.compereName = compereName;
  }

  public Integer getCreator() {
    return creator;
  }

  public void setCreator(Integer creator) {
    this.creator = creator;
  }

  public String getCreateName() {
    return createName;
  }

  public void setCreateName(String createName) {
    this.createName = createName;
  }

  public Date getCreateDate() {
    return createDate;
  }

  public void setCreateDate(Date createDate) {
    this.createDate = createDate;
  }

  public Integer getUpdator() {
    return updator;
  }

  public void setUpdator(Integer updator) {
    this.updator = updator;
  }

  public String getUpdateName() {
    return updateName;
  }

  public void setUpdateName(String updateName) {
    this.updateName = updateName;
  }

  public Date getUpdateDate() {
    return updateDate;
  }

  public void setUpdateDate(Date updateDate) {
    this.updateDate = updateDate;
  }

  public Integer getApproveItem() {
    return approveItem;
  }

  public void setApproveItem(Integer approveItem) {
    this.approveItem = approveItem;
  }

  public String getApproveStatus() {
    return approveStatus;
  }

  public void setApproveStatus(String approveStatus) {
    this.approveStatus = approveStatus;
  }

  public Integer getApproveLevel() {
    return approveLevel;
  }

  public void setApproveLevel(Integer approveLevel) {
    this.approveLevel = approveLevel;
  }

  public Integer getAuditor() {
    return auditor;
  }

  public void setAuditor(Integer auditor) {
    this.auditor = auditor;
  }

  public String getAuditorName() {
    return auditorName;
  }

  public void setAuditorName(String auditorName) {
    this.auditorName = auditorName;
  }

  public Date getAuditDate() {
    return auditDate;
  }

  public void setAuditDate(Date auditDate) {
    this.auditDate = auditDate;
  }

  public String getApplyMemo() {
    return applyMemo;
  }

  public void setApplyMemo(String applyMemo) {
    this.applyMemo = applyMemo;
  }

  public String getApproveMemo() {
    return approveMemo;
  }

  public void setApproveMemo(String approveMemo) {
    this.approveMemo = approveMemo;
  }

  public Integer getMessageId() {
    return messageId;
  }

  public void setMessageId(Integer messageId) {
    this.messageId = messageId;
  }

  public String getOperation() {
    return operation;
  }

  public void setOperation(String operation) {
    this.operation = operation;
  }

  public Integer getPreviousId() {
    return previousId;
  }

  public void setPreviousId(Integer previousId) {
    this.previousId = previousId;
  }

  public Integer getVersionNo() {
    return versionNo;
  }

  public void setVersionNo(Integer versionNo) {
    this.versionNo = versionNo;
  }

  public List<ForumPostUserHistory> getListForumPostUserHistory() {
    return listForumPostUserHistory;
  }

  public void setListForumPostUserHistory(List<ForumPostUserHistory> listForumPostUserHistory) {
    this.listForumPostUserHistory = listForumPostUserHistory;
  }

  public Date getValidTime() {
    return validTime;
  }

  public void setValidTime(Date validTime) {
    this.validTime = validTime;
  }

  public Integer getIsArchived() {
    return isArchived;
  }

  public void setIsArchived(Integer isArchived) {
    this.isArchived = isArchived;
  }

  public Integer getIsCleaned() {
    return isCleaned;
  }

  public void setIsCleaned(Integer isCleaned) {
    this.isCleaned = isCleaned;
  }

  public Date getCleanTime() {
    return cleanTime;
  }

  public void setCleanTime(Date cleanTime) {
    this.cleanTime = cleanTime;
  }

  public Date getEnabledTime() {
    return enabledTime;
  }

  public void setEnabledTime(Date enabledTime) {
    this.enabledTime = enabledTime;
  }
}
