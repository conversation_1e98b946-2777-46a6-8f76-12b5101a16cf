<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sphd.miners.modules.equipment.mapper.TEquEquipmentMapper">
    
    <resultMap type="TEquEquipment" id="TEquEquipmentResult">
        <result property="id"    column="id"    />
        <result property="org"    column="org"    />
        <result property="name"    column="name"    />
        <result property="fullName"    column="full_name"    />
        <result property="units"    column="units"    />
        <result property="modelCount"    column="model_count"    />
        <result property="supplierCount"    column="supplier_count"    />
        <result property="quantity"    column="quantity"    />
        <result property="memo"    column="memo"    />
        <result property="enabled"    column="enabled"    />
        <result property="enabledTime"    column="enabled_time"    />
        <result property="creator"    column="creator"    />
        <result property="createName"    column="create_name"    />
        <result property="createDate"    column="create_date"    />
        <result property="updator"    column="updator"    />
        <result property="updateName"    column="update_name"    />
        <result property="updateDate"    column="update_date"    />
        <result property="operation"    column="operation"    />
        <result property="previousId"    column="previous_id"    />
        <result property="versionNo"    column="version_no"    />
    </resultMap>

    <sql id="selectTEquEquipmentVo">
        select id, org, name, full_name, units, model_count, supplier_count,quantity, memo, enabled, enabled_time, creator, create_name, create_date, updator, update_name, update_date, operation, previous_id, version_no from t_equ_equipment
    </sql>

    <select id="selectTEquEquipmentList" parameterType="TEquEquipment" resultMap="TEquEquipmentResult">
        <include refid="selectTEquEquipmentVo"/>
        <where>  
            <if test="org != null "> and org = #{org}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="fullName != null  and fullName != ''"> and full_name like concat('%', #{fullName}, '%')</if>
            <if test="units != null  and units != ''"> and units = #{units}</if>
            <if test="modelCount != null "> and model_count = #{modelCount}</if>
            <if test="supplierCount != null "> and supplier_count = #{supplierCount}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
            <if test="enabled != null "> and enabled = #{enabled}</if>
            <if test="enabledTime != null "> and enabled_time = #{enabledTime}</if>
            <if test="creator != null "> and creator = #{creator}</if>
            <if test="createName != null  and createName != ''"> and create_name like concat('%', #{createName}, '%')</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updator != null "> and updator = #{updator}</if>
            <if test="updateName != null  and updateName != ''"> and update_name like concat('%', #{updateName}, '%')</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="operation != null "> and operation = #{operation}</if>
            <if test="previousId != null "> and previous_id = #{previousId}</if>
            <if test="versionNo != null "> and version_no = #{versionNo}</if>
        </where>
        order by CONVERT( full_name USING gbk ) COLLATE gbk_chinese_ci ASC
    </select>
    
    <select id="selectTEquEquipmentById" parameterType="Long" resultMap="TEquEquipmentResult">
        <include refid="selectTEquEquipmentVo"/>
        where id = #{id}
    </select>
    <select id="selectTEquEquipmentByName" resultType="cn.sphd.miners.modules.equipment.entity.TEquEquipment" parameterType="TEquEquipment">
        <include refid="selectTEquEquipmentVo"/>
        where name = #{name} and org=#{org}
    </select>

    <insert id="insertTEquEquipment" parameterType="TEquEquipment" useGeneratedKeys="true" keyProperty="id">
        insert into t_equ_equipment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="org != null">org,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="fullName != null">full_name,</if>
            <if test="units != null">units,</if>
            <if test="modelCount != null">model_count,</if>
            <if test="supplierCount != null">supplier_count,</if>
            <if test="quantity != null">quantity,</if>
            <if test="memo != null">memo,</if>
            <if test="enabled != null">enabled,</if>
            <if test="enabledTime != null">enabled_time,</if>
            <if test="creator != null">creator,</if>
            <if test="createName != null">create_name,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updator != null">updator,</if>
            <if test="updateName != null">update_name,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="operation != null">operation,</if>
            <if test="previousId != null">previous_id,</if>
            <if test="versionNo != null">version_no,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="org != null">#{org},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="fullName != null">#{fullName},</if>
            <if test="units != null">#{units},</if>
            <if test="modelCount != null">#{modelCount},</if>
            <if test="supplierCount != null">#{supplierCount},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="memo != null">#{memo},</if>
            <if test="enabled != null">#{enabled},</if>
            <if test="enabledTime != null">#{enabledTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createName != null">#{createName},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updator != null">#{updator},</if>
            <if test="updateName != null">#{updateName},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="operation != null">#{operation},</if>
            <if test="previousId != null">#{previousId},</if>
            <if test="versionNo != null">#{versionNo},</if>
         </trim>
    </insert>

    <update id="updateTEquEquipment" parameterType="TEquEquipment" useGeneratedKeys="true">
        update t_equ_equipment
        <trim prefix="SET" suffixOverrides=",">
            <if test="org != null">org = #{org},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="fullName != null">full_name = #{fullName},</if>
            <if test="units != null">units = #{units},</if>
            <if test="modelCount != null">model_count = #{modelCount},</if>
            <if test="supplierCount != null">supplier_count = #{supplierCount},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="enabled != null">enabled = #{enabled},</if>
            <if test="enabledTime != null">enabled_time = #{enabledTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="createName != null">create_name = #{createName},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updator != null">updator = #{updator},</if>
            <if test="updateName != null">update_name = #{updateName},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="operation != null">operation = #{operation},</if>
            <if test="previousId != null">previous_id = #{previousId},</if>
            <if test="versionNo != null">version_no = #{versionNo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTEquEquipmentById" parameterType="Long">
        delete from t_equ_equipment where id = #{id}
    </delete>

    <delete id="deleteTEquEquipmentByIds" parameterType="String">
        delete from t_equ_equipment where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>