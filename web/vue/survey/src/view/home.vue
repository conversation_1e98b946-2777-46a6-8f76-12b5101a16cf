<template>
  <div class="home" id="home">
    <div class="nav-bar">
      <el-button style="position: absolute;left: 8px;top: 10px;" type="" circle v-if="goback" icon="el-icon-arrow-left" @click="backAnswer"></el-button>
      <div class="nav-bar__title" style="white-space: pre-wrap; line-height: 25px;" v-html="title">{{ title }}</div>
    </div>
    <div class="tip">
      <p style="white-space: pre-wrap" >天津市高校校友会科技经济融合研究会是由76所高校的天津校友会和校友工作者参加，市科协为主管部门，市民政局登记注册的全国唯一以校友会为主体从事高校科技成果转化与企业技术服务的科技服务社团组织。</p>
      <p>为了更好的服务母校、服务校友、服务企业、服务社会，现征集企业信息及需求相关信息，以便为大家提供更精准的智能匹配服务。</p>
    </div>
    <div v-if="!answerInfo">
      <div class="itemCon" v-for="(item, index) in questionList" v-bind:key="index">
        <h4>{{item.name}}</h4>
        <div style="margin-top:15px; " v-for="(question, qindex) in item.respSurveyQuestionList" v-bind:key="qindex">
          <div v-if="question.type === '1'  "> <!--单选-->
            <div v-if="question.category == 1 || (question.category == 2 && ansList.indexOf(question.parentKey) > -1) ">
              <div class="quest van-cell__title van-field__label">
                <span class="opZ"><i class="red" v-if="question.isRequired === 1">*</i></span>
                {{question.no }} {{question.content}}
              </div>
              <div v-for="(option, opindex) in question.surveyQuestionKeyList" v-bind:key="opindex">
                <div v-if="option.category == 1 || (option.category == 2 && ansList.indexOf(option.parentKey) > -1)">
                   <span class="ty-radio" @click="clickitem(option.id, question, $event)">
                    <span :class="['radio-icon', question.ans == option.id ? 'ra-check': '']"></span>
                    <span :class="[ question.ans == option.id ? 'txt-check': '']">{{option.content}}</span>
                  </span>
                  <div v-if="option.isOher">
                    <el-input type="text" v-show="question.otherTextShow" v-model="question.otherText" :placeholder="option.memo"></el-input>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else-if="question.type === '3'" > <!--多选-->
            <div v-if="question.category == 1 || (question.category == 2 && ansList.indexOf(question.parentKey) > -1) ">
              <div class="quest van-cell__title van-field__label">
                <span class="opZ"><i class="red" v-if="question.isRequired === 1">*</i></span>
                {{question.no}}{{question.content}}
              </div>
              <div v-for="(option, opindex) in question.surveyQuestionKeyList" v-bind:key="opindex">
                <div v-if="option.category == 1 || (option.category == 2 && ansList.indexOf(option.parentKey) > -1)">
                  <el-checkbox v-model="question.ans" :label="option.id">{{ option.content }}</el-checkbox>
                  <div v-if="option.isOher">
                    <el-input type="text" v-show="question.otherTextShow" v-model="question.otherText" :placeholder="option.memo"></el-input>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else-if="question.type === '4'"> <!--填空-->
            <div class="quest van-cell__title van-field__label">
              <span class="opZ"><i class="red" v-if="question.isRequired === 1">*</i></span>
              {{question.no}}{{question.content}}
            </div>
            <van-field  border v-model="question.ans" :placeholder="question.memo" :required = "question.isRequired === '1'"/>
          </div>
          <div v-else-if="question.type === '5'"> <!-- 联动 -->
            <div class="quest van-cell__title van-field__label">
              <span class="opZ"><i class="red" v-if="question.isRequired === 1">*</i></span>
              {{question.no}}{{question.content}}
            </div>
            <van-field readonly clickable :value="question.ans0" placeholder="请选择地区 省/市/区" @click="openPicker(question)"/>
            <van-field  border v-model="question.ans" :placeholder="question.memo" :required = "question.isRequired === '1'"/>
          </div>
          <div v-else>
            <h5>无法识别的题型</h5>
          </div>
        </div>
      </div>
      <van-button v-show="showBtn" class="submit" type="primary" round block color="linear-gradient(to right, #83b7f1, #4084e1)" @click="submit">提交</van-button>
    </div>
    <div v-if="answerInfo" style="background: #fff; padding: 15px;">
      <div class="itemCon" v-for="(item, index) in questionList" v-bind:key="index">
        <div style="margin-top:15px; " v-for="(question, qindex) in item.respSurveyQuestionList" v-bind:key="qindex">
          <div class="questinfottl">
            <span class="opZ"><i class="red" v-if="question.isRequired === 1">*</i></span>
            {{question.no}}{{question.content}}
          </div>
          <div v-for="(as, aIndex) in question.surveyAnswerList" v-bind="aIndex">
            <div class="textCon">{{ as.content || "" }}</div>
          </div>
        </div>
      </div>
    </div>
    <van-popup v-model="showPicker" position="bottom">
      <van-picker show-toolbar :columns="columns" @cancel="showPicker = false" @confirm="onConfirm" @change="onChange"/>
    </van-popup>
  </div>
</template>

<style>
  #home{ background:#fff;  }
  .ty-radio{ display: block; line-height:30px; color: #606266; font-size:14px;   }
  .radio-icon{ display: inline-block; border:1px solid #dcdfe6; width:14px; height:14px; margin-right:5px; border-radius:7px; position: relative; top: 3px; }
  .ra-check{  border:6px solid #409eff!important; width: 4px; height: 4px; }
  .txt-check{ color:#409eff ;  }
  body{ padding:0; margin: 0; }
  .el-checkbox__label{ line-height: 30px; }
  .opZ{ display:inline-block; width:8px; text-align: center; margin-left:-25px;   }
  .red{ color:red; }
  .el-input{ min-width: 300px!important; }
  .el-radio__input:visited, .el-radio__input:active,el-radio__inner:active{ box-shadow:none!important; border-color:#ddd;   }
  input:focus,el-radio__inner:after { border:none!important; box-shadow:none!important;   }
  .el-radio__inner:hover{ border-color: #dcdfe6!important; }
  .van-field__control{ border: 0; border-bottom: 1px solid #ddd; }
  .van-field__control{ border-bottom:1px solid #ddd;  }
  #home .submit{ margin:40px auto!important;  width: 80%;  }
  #home .el-radio-group{ display:block;   }
  #home .el-radio-group{ width:70px;   }
  #home .el-button.is-circle{ padding:8px;  }
  #home .questinfottl{ color:#333; margin-top:10px; font-size:15px;   }
  #home .textCon{ color:#df7234; font-size:15px; }
  .van-cell{ display: block!important; }
  .van-radio-group{ margin-left:30px;  }
  .itemCon{ padding: 0 15px; }
  .quest{ font-size: 16px; color: #000; margin-left: 20px; }
  .van-radio,.van-checkbox { font-size: 14px; margin: 10px; }
  .van-field__label { width:90%!important;  }
  .box{ padding:8px 0; border-bottom: 8px solid #eee; }
  .right{  text-align: right;  }
  #app .van-button{  margin: 4px 0;  }
  .nav-bar{ position: relative; height: 46px; line-height: 46px; text-align: center; background-color: #fff; -webkit-user-select: none; user-select: none; }
  .nav-bar__left{ position: absolute; left: 16px; bottom: 0 }
  .nav-bar__title{ margin: 0 auto; color: #323233; font-weight: 500; font-size: 16px; }
  .nav-bar__right{ position: absolute; bottom: 0; right: 16px; }
  .el-radio, .el-checkbox{ line-height: 30px!important; }
  .tip{ color: #df7234; font-size: 14px; padding:15px; text-indent:2em; background: #fff; }
</style>

<script>
import area from '../assets/area.json'
  export default {
  name: 'home',
  data () {
    return {
      title: '企业需求调查表',
      showPicker: false,
      editObj: null,
      description: '',
      area: area,
      status: '',
      goback: false,
      showBtn: false,
      answerInfo: false,
      questionList: [], // 问题列表
      ansList: [], // 选项集合
      columns: []
    }
  },
  created () {
    this.$toast.loading({
      message: '正在加载...',
      forbidClick: true,
      loadingType: 'spinner'
    })
    // wyu：打印base传过来的参数
    console.log(this.$route.params)
    this.goback = this.$route.params.goback || false
    this.getQuestion()
  },
  mounted(){
    this.initAreaData()
  },
  watch:{
    questionList:{
      handler(newList, oldVal) {
        console.log('newVal', newList)
        console.log('oldVal', oldVal)
        let newAnsList = []
        let list = JSON.parse(JSON.stringify(newList))
        list.forEach(function (item, index) {
          let questions = item.respSurveyQuestionList
          questions.forEach(function (ques, qindex) {
            if (ques.type === '4') { // 填空
            } else if (ques.type === '5') { // 城市多选
            } else {
              if(ques.ans.length > 0){
                if(ques.type === '1'){ // 单选
                  newAnsList.push(ques.ans)
                }else{
                  newAnsList.push(...ques.ans)
                }
              }
            }
          })
        })
        this.ansList = newAnsList
        console.log(newAnsList)
      },
      deep: true,
      immediate: false
    }
  },
  methods: {
    clickitem:function (op, question, event) {
      op === question.ans ? question.ans = '' : question.ans = op
    },
    submit () {
      let _this = this
      console.log(this.questionList)
      let data = { 'survey': this.$route.params.id , 'hashKey': this.$route.params.hashKey , }
      let surveyAnswers = []
      let count = 0
      let list = JSON.parse(JSON.stringify(this.questionList))
      list.forEach(function (item, index) {
        let questions = item.respSurveyQuestionList
        questions.forEach(function (ques, qindex) {
          if( ques.category == 2 && _this.ansList.indexOf(ques.parentKey) === -1 ){ // 把 没有显示的题 答案干掉
            if(ques.type === '1'){ // 单选
              ques['ans'] = ''
            }else if(ques.type === '3'){ // 多选
              ques['ans'] = []
            }
          }
          if (String(ques.isRequired) === '1') {
            if (ques.type === '4' || ques.type === '1') {
              ques.ans = String(ques.ans)
            }
            if (ques.ans.length === 0 ) { // 彻底没填
              if( ques.category === '2' && _this.ansList.indexOf(ques.parentKey) === -1){
              }else{
                count++
                console.log(ques)
              }
            }else{ // 选择了答案， 答案 是没显示的选项
              if(ques.type === '3'&& _this.ansList.indexOf(ques.parentKey) > -1 ){ // 多选
                let iscount = false  // 默认不算空
                ques.surveyQuestionKeyList.forEach(function (op) {
                  if( op.category === '2' && _this.ansList.indexOf(op.parentKey) > -1 ){ // 显示的选项
                    let i = ques.ans.indexOf(op.id)
                    if (i > -1) {
                      iscount = true
                    }
                  }
                })
                if(!iscount){
                  count++
                  console.log(ques)
                }
              }
            }
          }
          if (ques.type === '4') { // 填空
            surveyAnswers.push({ 'question': ques.id, 'content': ques.ans })
          } else if (ques.type === '5') { // 城市多选
            surveyAnswers.push({ 'question': ques.id, 'content': ques.ans0 + '--' + ques.ans })
          } else {
            let con = ''
            ques.surveyQuestionKeyList.forEach(function (op) {
              if (ques.type === '1') { // 单选
                if (ques.ans === String(op.id)) {
                  if (op.content === '其他' || op.content === '其它') {
                    op.content += '：' + ques.otherText
                  }
                  con += op.content + ','
                }
              } else if (ques.type === '3'){ // 多选
                if(op.category === '1' || (op.category === '2' && _this.ansList.indexOf(op.parentKey) > -1 ) ){
                  let i = ques.ans.indexOf(op.id)
                  if (i > -1) {
                    if (op.content === '其他' || op.content === '其它') {
                      op.content += '：' + ques.otherText
                    }
                    con += op.content + ','
                  }
                }
              }
            })
            surveyAnswers.push({ 'question': ques.id, 'content': con, 'answer': ques.ans })
          }
        })
      })
      if (count > 0) {
        _this.$toast.fail('请将必填项补充完整！')
      } else {
        _this.showBtn = false
        this.$toast.loading({
          message: '提交中...',
          forbidClick: true,
          loadingType: 'spinner'
        })
        data['surveyAnswers'] = JSON.stringify(surveyAnswers)
        let url = window.parent.$.webRoot + '/survey/addSurveyObject.do'
        if(_this.status === 1 ){
          url = window.parent.$.webRoot + '/survey/updateSurveyObject.do'
          data['objective'] = _this.$route.params.objective
        }
        this.$http.post(url, data, {
          emulateJSON: true
        }).then((response) => {
          _this.$toast.clear()
          console.log(response.body)
          let res = response.body
          let success = res.success
          console.log(success)
          if (Number(success) === 1) {
            window.localStorage.setItem('wonderssSurveyObject', res.data)
            console.log("save wonderssSurveyObject",window.localStorage.getItem('wonderssSurveyObject'))
            console.log(window.localStorage.getItem('wonderssSurveyObject').length)
            this.$toast.clear()
            this.$router.replace({name: 'success'})
          } else {
            this.jumpError('没有提交成功哦，<br>请重新扫码提交！')
          }
        }).catch(function (err) {
          console.log(err)
          this.jumpError('提交失败，请重新扫码提交！')
          _this.loading = false
          _this.$toast.fail('提交失败，请重新扫码提交！')
        })
      }
    },
    jumpError (message) {
      this.$toast.clear()
      this.$router.replace({name: 'error', params: {message: message}})
    },
    backAnswer () {
      let params = this.$route.params
      this.$router.replace({ name: 'answer',  params: this.$route.params})
    },
    getQuestion () {
      let _this = this
      let id = this.$route.params.id
      let isHistory = this.$route.params.isHistory
      let hisID = this.$route.params.hisID
      let hashKey = this.$route.params.hashKey
      console.log('查询id:' + id)
      let url = '../../../survey/surveySubjectDetails.do'
      let data = { 'id': id, 'hashKey':hashKey }
      if(isHistory){
        url = '../../../survey/surveyAnswerHistoryDetails.do'
        data = { 'id': hisID }
        /*
      应答历史详情
      /survey/surveyAnswerHistoryDetails.do
      传值id 历史应答id
      返回值（同任务1查看）
id(调查id)
      name(调查表名)
      endTime(截至日期)
      memo(调查表前后，所加内容)
      respSurveySubjectTagList标签list{id 标签,name(基本信息等分类，一共4种)，
	respSurveyQuestionList问题list{id 问题id,content问题内容,type类型，1单选，3多选，4填空，code（题号）
					新增返回值：category 为1为普通题，不需要处理，为2为特殊题，需要满足选择parentKey的选项后，才可显示，没选择，则没有此题
          parentKey 为QuestionKey的id，选择了这个id，此题显示
          surveyQuestionKeyList 选项list，填空题没有选项{
            id 选项id ,content 选项内容
            新增返回值：category 为1为普通选项，不需要处理，为2为特殊选项，需要满足选择parentKey的选项后，才可显示，没选择，则没有此选项
            parentKey 为QuestionKey的id，选择了这个id，此选项显示
          }
          surveyAnswerList 答题list，{content 应答内容  answer已经选择的id  格式为答题时前端上传格式我读数据库的时候看好像是数组}
        }
      }
    }*/
      }
      this.$http.post(url, data, {
        emulateJSON: true
      }).then((response) => {
        _this.$toast.clear()
        console.log(response.body)
        let res = response.body
        _this.title = res.name
        _this.description = res.memo
        _this.status = res.status
        // _this.status = 2
        let List = res.respSurveySubjectTagList
        List.forEach(function (item) {
          let quesArr = item.respSurveyQuestionList
          quesArr.forEach(function (ques) {
            ques['no'] = ques.code
            ques.parentKey = ques.parentKey ? String( ques.parentKey ) : ''
            let ansList = ques.surveyAnswerList && ques.surveyAnswerList[0]
            if (ques.type === '3' || ques.type === '1') { // 单选，多选
              let opList = ques.surveyQuestionKeyList
              opList.forEach(function (op) {
                op.id = String(op.id)
                op.parentKey = op.parentKey ? String( op.parentKey ) : ''
                if (op.content === '其他' || op.content === '其它') {
                  ques.otherText = ''
                  ques.otherTextShow = true
                  op.isOher = true
                } else {
                  op.isOher = false
                }
              })
              if (ques.type === '3') { // 多选
                ques['ans'] = []
                if(_this.status === 1 && ansList){ // 答过题且可修改
                  ques['ans'] = JSON.parse(ansList['answer'])
                  let i = ansList.content.indexOf("其它：")
                  if(i === -1){
                    i = ansList.content.indexOf("其他：")
                  }
                  if(i > -1){
                    let otherStr = ansList.content.substr(i+3)
                    otherStr = otherStr.substr(0, otherStr.length-1)
                    ques.otherText = otherStr
                  }
                }
              } else { // 单选
                ques['ans'] = ''
                if(_this.status === 1 && ansList){ // 答过题且可修改
                  ques['ans'] = ansList['answer']
                }
              }
            } else { // 填空，联动
              ques['ans'] = ''
              ques['ans0'] = ''
              if(_this.status === 1 && ansList){ // 答过题且可修改
                if (ques.type === '4') { // 填空
                  ques['ans'] = ansList['content']
                }else if(ques.type === '5'){
                  let ansStr = ansList['content'];
                  ques['ans'] = ansStr.split("--")[1]
                  ques['ans0'] = ansStr.split("--")[0]
                }
              }
            }
          })
        })

        if(_this.status === 0){ // 未曾答过题
          _this.showBtn = true

        }else if(_this.status === 1){ // 答过题且可修改
          _this.showBtn = true

        }else if(_this.status === 2){ // 答过题且不可修改
          _this.showBtn = false
          _this.answerInfo = true
        }
        if (_this.$route.params.isHistory){
          _this.answerInfo = true
        }

        _this.questionList = List
        console.log('初始化之后的list')
        console.log(_this.questionList)
      }).catch(function (err) {
        console.log('跳转错误')
        console.log(err)
        _this.loading = false
        _this.$toast.fail('系统错误，请重试！')
      })
    },
    initAreaData () {
      let column1 = [{'code': 0, 'text':'请选择'}]
      let that = this
      for (let key in that.area) {
        column1.push({
          'code': key,
          'text':that.area[key]['name'],
          'kids': that.area[key]['child']
        })
      }
      console.log('第一级')
      console.log(column1)
      that.columns = [
        {
          values: column1,
          defaultIndex: 0,
          className: '省'
        },
        {
          values: [],
          defaultIndex: 0,
          className: '市'
        },
        {
          values: [],
          defaultIndex: 0,
          className: '区'
        }
      ]
    },
    openPicker (question) {
      this.showPicker = true
      this.editObj = question
    },
    onChange(picker, value, index) {
      if (index == 0) { // 改变第一列的值
        let i = value[0]['code'];
        let data = value[0]['kids']
        let list = [{'code': 0, 'text':'请选择'}]
        if (data) {
          for (let key in data) {
            list.push({
              'code': key,
              'text':data[key]['name'],
              'kids': data[key]['child']
            })
          }
        }
        if (list.length === 1) {
          list = []
        }
        picker.setColumnValues(1, list)
        picker.setColumnValues(2, [])
        value[1]= {}
        value[2]= {}
        console.log(list)
      } else if (index == 1) {
        let data2 = value[1]['kids']
        let list2 = [{'code': 0, 'text':'请选择'}]
        if (data2) {
          for (let key2 in data2) {
            list2.push({
              'code': key2,
              'text':data2[key2]
            })
          }
        }
        if (list2.length === 1) {
          list2 = []
        }
        picker.setColumnValues(2, list2)
        console.log(list2)
        value[2]= {}
      }
    },
    onConfirm (value, index) {
      console.log(this.columns)
      if (index.indexOf(0) > -1) {
        let giveTip = false
        let noIndex = 0
        this.columns.forEach(function (col, i) {
          if (col.values.length > 0 && !(value[i])) {
            giveTip = true
            if(noIndex === 0){
              noIndex = i
            }
          }
        })
        if(giveTip) {
          this.$toast.fail('请选择！')
        }else {
          let content = value[0]['text']
          for(let j = 0 ; j < 3; j++){
            if(j < noIndex && j !== 0){
              content = ' / ' + value[j]['text']
            }
          }
          this.editObj.ans0 = content
          this.showPicker = false
        }
      } else {
        let content = value[0]['text'] + ' / ' + value[1]['text'] + ' / ' + value[2]['text']
        this.editObj.ans0 = content
        this.showPicker = false
      }
    }
  }
}
</script>
