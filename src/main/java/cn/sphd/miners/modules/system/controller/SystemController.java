package cn.sphd.miners.modules.system.controller;

import cn.sphd.miners.common.controller.BaseController;
import cn.sphd.miners.common.initializer.AuthPassport;
import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.*;
import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.auth.service.AuthService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.finance.service.AccountService;
import cn.sphd.miners.modules.generalAffairs.service.PersonalService;
import cn.sphd.miners.modules.material.service.MaterielService;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.personal.service.PersonnelReimburseService;
import cn.sphd.miners.modules.personal.service.UserMessageService;
import cn.sphd.miners.modules.resourceAuthority.service.ResCategoryService;
import cn.sphd.miners.modules.sms.service.SendMessageService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.socket.service.MqMobileDeviceService;
import cn.sphd.miners.modules.system.dto.UserDto;
import cn.sphd.miners.modules.system.dto.UserLoginDto;
import cn.sphd.miners.modules.system.entity.*;
import cn.sphd.miners.modules.system.service.*;
import cn.sphd.miners.modules.system.service.impl.LockScreenCheck;
import cn.sphd.miners.modules.system.service.impl.LockScreenCheckLogout;
import cn.sphd.miners.modules.uploads.utils.UploadRootPath;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;

/**
 * Created by 赵应 on 2016-06-20.
 */
@Controller
@RequestMapping("/sys")
public class SystemController extends BaseController {
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    RedisTemplate redisTemplate;
    @Autowired
    UserService userService;
    @Autowired
    UserLogService userLogService;
    @Autowired
    RoleService roleService;
    @Autowired
    PopedomService popedomService;
    @Autowired
    OrgService orgService;
    @Autowired
    AccountService accountService;
    @Autowired
    ApprovalService approvalService;
    @Autowired
    MaterielService materielService;
    @Autowired
    CodeService codeService;
    @Autowired
    OffDutyService offDutyService;
    @Autowired
    UserMessageService userMessageService;
    @Autowired
    PersonnelReimburseService personnelReimburseService;
    @Autowired
    RoleTmplService roleTmplService;
    @Autowired
    UserPopedomService userPopedomService;
    @Autowired
    PersonalService personalService;
    @Autowired
    ResCategoryService resCategoryService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    OrgPopedomService orgPopedomService;
    @Autowired
    SendMessageService sendMessageService;
    @Autowired
    PopedomTaskService popedomTaskService;
    @Autowired
    UserHistoryService userHistoryService;
    @Autowired
    RolePrincipalHistoryService rolePrincipalHistoryService;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    MqMobileDeviceService mqMobileDeviceService;
    @Autowired
    AuthService authService;

    public SystemController() {
        //关闭父类参数自动转换，不防范XSS攻击,不关闭的话，会进行html和Date数据转义，详细参考BaseController
        setInitBinderConfig(InitBinderConfig.DisableXSSDefence, InitBinderConfig.DisableDateChange);
    }

    @AuthPassport(validate = false)
    @RequestMapping("/index.do")
    public String index(HttpServletRequest request, AuthAcc acc, User user) {
        if(/*GetLocalIPUtils.getDomain(request).equals("dvm04.btransmission.com") && */new File(request.getServletContext().getRealPath("/vue/minersFrontEnd/dist/index.html")).exists()) {
            return "redirect:/vue/minersFrontEnd/dist/index.html";//wyu：跳转到vue大窗
        }
        if (user != null) {
            System.out.println("SystemControl.index.do userID："+user.getUserID());
            return "/system/main";//wyu：登录成功进入主页面
//                return "redirect:/sys/main.do";//wyu：登录成功进入主页面
        } else if(acc!=null) {
            System.out.println("SystemControl.index.do accId："+acc.getId());
            return "redirect:/sys/goBack.do";//wyu：验证密码成功，但是没有选择机构，进入选择机构页面
        } else {
            return "/system/login";//wyu：用户未登录或者登录过期
        }
    }

    /**
     * <AUTHOR>
     * @description 获取当前用户和机构
     * @date Create at 2021/9/27 13:30
     * @method refreshCurrentUserOrg
     * @param response
     * @return: java.lang.String:
     **/
    @AuthPassport(validate = false)
    @ResponseBody
    @RequestMapping(value = "/refreshCurrentUserOrg.do", produces = "application/json;charset=utf-8")
    public JsonResult refreshCurrentUserOrg(User user) {
        Map<String, String> result = new HashMap<>();
        if(user!=null) {
            result.put("user", JSON.toJSONString(user));
            Organization organization = user.getOrganization();
            if(organization!=null) {
                result.put("org", JSON.toJSONString(organization));
            }
        }
        return new JsonResult(1, result);
    }

//    @RequestMapping("main.do")
//    public String main(Model model, HttpSession session) {
//        User u = (User) session.getAttribute("user");
//        Integer uid = u.getUserID();
//        List<Popedom> menu = getMenu(u);
//        List<PopedomTask> messageMenu = popedomTaskService.getCornerMarkerProcessing(uid);
//        HashMap<String, Object> userBadgeNumbers = popedomTaskService.getUserBadgeNumbers(uid);
//        model.addAttribute("mainMenu",JSON.toJSONString(menu));
//        model.addAttribute("messageMenu",JSON.toJSONString(messageMenu));
//        model.addAttribute("userBadgeNumbers",JSON.toJSONString(userBadgeNumbers));
//        return "/system/main";
//    }

    @ResponseBody
    @RequestMapping("getAllMenu.do")
    public JsonResult getAllMenu(User user) {
        HashMap<String, Object> result = new HashMap<>();
        Integer uid = user.getUserID();
        List<Popedom> menu = getMenu(user);
        result.put("mainMenu",JSON.toJSONString(menu));
        List<PopedomTask> messageMenu = popedomTaskService.getCornerMarkerProcessing(uid);
        result.put("messageMenu",JSON.toJSONString(messageMenu));
        HashMap<String, Object> userBadgeNumbers = popedomTaskService.getUserBadgeNumbers(uid);
        result.put("userBadgeNumbers",JSON.toJSONString(userBadgeNumbers));
        Integer masterUserId = user.getMasterUserID()!=null?user.getMasterUserID():user.getUserID();
        List<User> userList= userService.getUsersByMasterUserID(masterUserId);// 包含员工本身 的身份列表
        result.put("rolePrincipals",userList); //身份列表
        return new JsonResult(1, result);
    }

    @ResponseBody
    @RequestMapping("/getUserBadgeNumbers.do")
    public JsonResult getUserBadgeNumbers(User user) {
        HashMap<String, Object> userBadgeNumbers = popedomTaskService.getUserBadgeNumbers(user.getUserID());
        return new JsonResult(1, JSON.toJSONString(userBadgeNumbers));
    }

    @AuthPassport(validate = false)
    @RequestMapping("/login.do")
    public ModelAndView login(HttpServletRequest request, HttpServletResponse response, String logonName, String logonPwd, Model model){
//        if(user==null||user.getLogonName()==null||user.getLogonPwd()==null){
        if (MyStrings.nulltoempty(logonName).isEmpty() || MyStrings.nulltoempty(logonPwd).isEmpty()) {
            model.addAttribute("error", "帐号密码不能为空！");
//            return "/system/login";
            return toJsonOrView("/system/login", model, request);

        }
        AuthAcc acc = authService.setTokenByMobilePassword(response, logonName, logonPwd, Boolean.FALSE);
        if (acc==null) {
            model.addAttribute("error", "您输入的账号或密码有误!");
//            return "/system/login";
            return toJsonOrView("/system/login", model, request);

        } else if(StringUtils.isEmpty(response.getHeader("token"))) {
            if(AuthService.State.locked.equals(acc.GetState())) {
                model.addAttribute("error", "账号被锁定!");
            } else {
                model.addAttribute("error", "需要短信验证!");
            }
            model.addAttribute("logonName",logonName);// 超79天，页面需给另一种展示，列出的登陆账号  1.159私人领地奠基 lixu 2021/6/16
//            return "/system/login";
            return toJsonOrView("/system/login", model, request);

        }
        //保留查询用户机构列表的原因是需要判断是否设置默认机构或者只有一个机构时按需求要求直接进入机构。
        PageInfo pageInfo = new PageInfo(0, 2);
//        List<UserLoginDto> list = userService.getUserList(logonName, logonPwd, true, pageInfo);
        List<UserLoginDto> list = userService.getUserList(acc.getId(), pageInfo);
        if (!list.isEmpty()) {
            UserLoginDto userLoginDto = list.get(0);
            //wyu：重设cookies("mobiles")
            if (userLoginDto != null) {
                setCookieMobiles(userLoginDto.getMobile(), request, response);
            }
//            CookieUtils.setCookie(response, "lockScreen", "", 0);//清除修改密码锁屏的标志
//            session.setMaxInactiveInterval((int)TimeUnit.MINUTES.toSeconds(30));//wyu：session过期时间30分钟。
//            session.setAttribute("user", SerializationUtils.clone(userService.getUserByID(list.get(0).getUserID())));
            if (list.size() == 1 || userLoginDto.isDefault()) {//登陆成功
                model.addAttribute("success", "登录成功，进入默认机构!");
                return sureLogin(userLoginDto, response.getHeader("token"), acc, null, model, request, response);  //当有默认机构时，直接跳转到默认的机构中
            } else {//多个机构显示机构列表
//                return "/system/regist";
                model.addAttribute("success", "登录成功，请选择机构!");
                return toJsonOrView("/system/regist", model, request);

            }
        } else {//无机构无法登录，后续改为无机构进入领地
            model.addAttribute("manor","很抱歉，目前您的数据不能通过电脑端访问，推荐您使用手机端！");
//            return "/system/login";
            return toJsonOrView("/system/login", model, request);

        }
    }

    // 2020/9/17 1.125多重身份 lixu 改 加了一个 参数loginUserId(多重身份切换 进入机构时 传此id，直接进入机构)
    @AuthPassport(manor = true)
    @RequestMapping("/sureLogin.do")
    public ModelAndView sureLogin(UserLoginDto user, String token, AuthAcc acc, Integer loginUserId, Model model, HttpServletRequest request, HttpServletResponse response){
        User loginUser;
        loginUser = userService.getUserByLogon(loginUserId==null ? user.getUserID() : loginUserId, acc.getMobile(),user.getOid());

        if (acc==null
                ||loginUser==null
                || !acc.getMobile().equals(loginUser.getMobile())) {
//                ||!(userService.getUserIdsByMobile(oldUser.getMobile()).contains(loginUser.getUserID()))){
//            return "redirect:/sys/logout.do";
            model.addAttribute("error", "账号或用户有问题！");
            return toJsonOrView("/sys/logout.do", model, request);
        }

        Organization organization = loginUser.getOrganization();
        Pair<Integer, User> check = checkLogin(loginUser, organization, model, request);
        if (check.getLeft() != 0) {
            return toJsonOrView("/system/login", model, request);
        } else if(check.getRight() != null) {
            loginUser = check.getRight();
        }

        Map<String, Object> authMap = authService.updateTokenByUser(response, token, loginUser);
        String sessionid = (String) authMap.get("sessionid");
        loginUser = (User) authMap.get("user");
//        CookieUtils.setCookie(response, "user", JSON.toJSONString(loginUser));
        try {
            response.setHeader("user", URLEncoder.encode(JSON.toJSONString(loginUser), StandardCharsets.UTF_8.name()));
        } catch (UnsupportedEncodingException e) {
            logger.warn("response.setHeader org error", e);
        }
        if (organization != null) {
//            CookieUtils.setCookie(response, "org", JSON.toJSONString(organization));
            try {
                response.setHeader("org", URLEncoder.encode(JSON.toJSONString(organization), StandardCharsets.UTF_8.name()));
            } catch (UnsupportedEncodingException e) {
                logger.warn("response.setHeader org error", e);
            }
            if("NFS".equalsIgnoreCase(organization.getUploadStorageType())) {
                UploadRootPath path = UploadRootPath.getInstance();
                String ow365url = path.getOw365Path(request, organization);
                request.setAttribute("ow365url", ow365url);//ow365预览前缀
                String uploadUrl = path.getUploadPath(request, organization);
                request.setAttribute("uploadUrl", uploadUrl);//同域下载前缀
                String fileUrl = path.getFilePath(request, organization);
                request.setAttribute("fileUrl", fileUrl);//文件服务器下载前缀
            }
        }

        //wyu：十分钟无操作统计。
        Long operatortime = System.currentTimeMillis();
        redisTemplate.opsForValue().set("miners:operatortime:"+sessionid,operatortime,35, TimeUnit.MINUTES);//设置锁屏缓存35分钟失效
        redisTemplate.opsForValue().set("miners:logout:"+sessionid,operatortime,35, TimeUnit.MINUTES);//wyu：设置注销缓存35分钟过期

        this.loginLog(loginUser,request,sessionid);
        return toJsonOrView("/system/main", model, request);
    }

    private Pair<Integer, User> checkLogin(User user, Organization organization, Model model, HttpServletRequest request) {

        JsonResult jsonResult=userService.checkLogin(user,organization,request);
        if (jsonResult.getSuccess()==1){
            return Pair.of(0, (User) jsonResult.getData());
        }else {
            model.addAttribute("error",jsonResult.getErrorMsg());
            return Pair.of(Integer.valueOf(jsonResult.getErrorCode()), null);
        }

        //1.118暂停服务 lixu
//        if ("1".equals(user.getOrganization().getState())){
//            model.addAttribute("error", "贵公司已被暂停服务，请告知公司管理人员！");
//            return Pair.of(1, null);
//        }
//
//        //1.139中枢管控2  lixu 2020/12/17
//        if("0".equals(organization.getInitState())&&"staff".equals(user.getRoleCode())){ //未正式 启用前， 以全权负责人身份 直接进入机构
//            user=userService.getManageUserByMasterUserID(user.getUserID(),"general");
//            if (user==null){
//                model.addAttribute("error", "当前账户没有分配权限");
//                return Pair.of(2, null);
//            } else {
//                return Pair.of(0, user);
//            }
//        }
//        /////wyu:使用当前登录名查询当前登录名所具有的权限，将权限组织成字符串的形式（格式：aa,ab,ac,ad,ae）
//        String popedom=userService.getPopedomStringByUser(user);
//        if(popedom.isEmpty()) {
//            model.addAttribute("error", "当前账户没有分配权限");
//            return Pair.of(3, null);
//        }
//        if("liveHelper".equalsIgnoreCase(organization.getCode())
//            && !GetLocalIPUtils.isCmwPrj(request)) {
//            model.addAttribute("error", "请从字幕精灵小程序或者字幕精灵PC端登录");
//            return Pair.of(4, null);
//        }
//        return Pair.of(0, null);
    }

    private void loginLog(User loginUser, HttpServletRequest request, String sessionid){
        userLogService.addWebUserLog("登录系统", loginUser, sessionid, request);
    }

    @RequestMapping("/toAdminIndex.do")
    public String toAdminIndex(){
        return "/finance/indexForAdmin";
    }

    /**
     * 退出登录的接口
     * <AUTHOR>
     * @since 2021/4/23 15:32
     * @param request
     * @param response
     * @param sessionid
     * @return: java.lang.String
     */
    @AuthPassport(validate = false)
    @RequestMapping("/logout.do")
    public ModelAndView logout(HttpServletRequest request, HttpServletResponse response, String sessionid, Model model){
        //设置登录时长。
        Integer userLogId;
        if((userLogId = (Integer) redisTemplate.opsForValue().get("miners:userLogId:" + sessionid))!=null) {
            userLogService.setDuration(userLogId);
            redisTemplate.delete("miners:userLogId:" + sessionid);
        }
        //清空缓存
        redisTemplate.delete("miners:operatortime:" + sessionid);
        authService.cleanToken(request,response);
        CookieUtils.clearAll(request, response);
        //wyu：清除剩余的登录信息
        if(request.isRequestedSessionIdValid()) {
            request.getSession().invalidate();
        }
//        return "/system/login";
        if(new File(request.getServletContext().getRealPath("/vue/minersFrontEnd/dist/index.html")).exists()//有新大窗
            && GetLocalIPUtils.isAjaxOrApp(request)==0) {//页面提交或跳转
            return new ModelAndView("redirect:/vue/minersFrontEnd/dist/index.html#/?logout=1");//跳转到新大窗登录并注销
        } else {
            return toJsonOrView("/system/login", model, request);
        }
    }

    /**
     * 切换机构的接口 1.159PIA重构
     * <AUTHOR>
     * @since 2021/4/22 15:30
     */
    @AuthPassport(manor = true)
    @RequestMapping("/goBack.do")
    public String goBack() {
        return "/system/regist";
    }

    @AuthPassport(manor = true)
    @RequestMapping("/codeLogin.do")
    public ModelAndView codeLogin(AuthAcc acc, String token, HttpServletRequest request, HttpServletResponse response, Model model) {
        PageInfo pageInfo = new PageInfo(0, 2);
        List<UserLoginDto> list = userService.getUserList(acc.getId(), pageInfo);
        UserLoginDto userLoginDto = list.get(0);
        //wyu：重设cookies("mobiles")
        if (userLoginDto != null) {
            setCookieMobiles(userLoginDto.getMobile(), request, response);
        }
        if (list.size() == 1 || userLoginDto.isDefault()) {//登陆成功
            return sureLogin(userLoginDto, token, acc, null, model, request, response);  //当有默认机构时，直接跳转到默认的机构中
        } else {
//            return "/system/regist";
            return toJsonOrView("/system/regist", model, request);
        }
    }

    private void setCookieMobiles(String mobile, HttpServletRequest request, HttpServletResponse response) {
        if (StringUtils.isNotEmpty(mobile)) {
            String mobile_str = CookieUtils.getCookie(request, "mobiles");
            List<String> mobiles = new ArrayList<>();
            if (StringUtils.isNotEmpty(mobile_str)) {
                mobiles = JSON.parseArray(mobile_str, String.class);
                int index = mobiles.lastIndexOf(mobile);
                if (index >= 0) {
                    mobiles.remove(index);
                }
            }
            mobiles.add(0, mobile);
            CookieUtils.setCookie(response, "mobiles", JSON.toJSONString(mobiles), Integer.MAX_VALUE);
        }
    }

//    /**
//     * 切换机构的接口
//     * @param session
//     * @return
//     */
//    @RequestMapping("/goBack.do")
//    public String goBack(HttpSession session, Model model, HttpServletResponse response){
//        User user = (User)session.getAttribute("user");
//        if(user!=null) {
//            PageInfo pageInfo = new PageInfo(1, 11);//  登录只展示9条
//            List<UserLoginDto> list = userService.getUserList(user.getLogonName(), user.getLogonPwd(), false, pageInfo);
//            clearPartLogin(response, session);
//            if (list!=null&&list.size()>0) {
//                UserLoginDto uld = list.get(0);
//                loginPart(uld, model, list);
//                return "/system/regist";
//            }
//        }
//        return "/system/login";//wyu：用户未登录或者登录过期
//    }

//    /**
//     * 获取用户的所有机构信息
//     * @param user
//     * @param model
//     * @param list
//     */
//    @Deprecated //未使用
//    public void loginPart(UserLoginDto user, Model model, List<UserLoginDto> list){
//        List<Logon> listLogon=new ArrayList<>(list.size());
//        int displaySwitch = 0;
//        Pattern p = Pattern.compile("\\d*");
//        StringBuffer oidName = new StringBuffer();
//        List<UserLoginDto> userLoginDtoList=userService.getUserMsgCountSum(user.getMobile());
//        for (UserLoginDto u:list) {
//            if (!("".equals(u.getLeader()) || u.getLeader() == null)) {
//                oidName.setLength(0);
//                oidName.append(u.getOidName());
//                Matcher m = p.matcher(oidName);
//                if (m.matches()) {
//                    oidName.append("(").append(u.getOid()).append(")");
//                }
//                if (displaySwitch !=1 && userService.isSuper(u)) {
//                    displaySwitch = 1;
//                }
//                Logon logon=new Logon();
//                logon.setUserID(u.getUserID());
//                logon.setLogonName(user.getMobile());
////                logon.setLogonPwd(u.getLogonPwd());
//                logon.setOid(u.getOid());
//                logon.setOidName(oidName.toString());
//                Integer msgCountSum=0;
//                for (UserLoginDto userLoginDto:userLoginDtoList){   //2020/10/19 多重身份 代码调优 ，查一次数据库循环赋值  lixu
//                    if (logon.getOid().equals(userLoginDto.getOid())){
//                        msgCountSum=userLoginDto.getMsgCount();
//                    }
//                }
//                logon.setMsgCount(msgCountSum);
////                Integer msgCountSum=userService.getUserMsgCountSum(u.getOid(),user.getMobile()); // 2020/9/17 1.125多重身份，获取所有身份角标和
////                logon.setMsgCount((msgCountSum==null?0:msgCountSum)+(u.getForumCount()==null?0:u.getForumCount()));
//                listLogon.add(logon);
//            }
//        }
//
//        UserLog userLog=userLogService.getLastLog(user.getMobile());
//        String lastTime="";
//        String lastIp="";
//        if(userLog!=null) {
//            lastTime = NewDateUtils.dateToString(userLog.getOperatetime(), "yyyy-MM-dd HH:mm:ss");
//            lastIp = userLog.getIp();
//        }
//
//        model.addAttribute("listLogon", listLogon);
//        model.addAttribute("lastTime", lastTime);
//        model.addAttribute("lastIp",lastIp);
//        model.addAttribute("displaySwitch", displaySwitch);
//    }

    @RequestMapping("/showMenu.do")
    @ResponseBody
    @Deprecated//未使用
    public void showMenu(User user, HttpServletResponse response) throws IOException {
        //获取当前登录人操作的权限
        String popedom=userService.getPopedomStringByUser(user);
        HashMap<String, String> search = popedomService.getAllPopedomSearch(1);
        List<Popedom> list = popedomService.findFullPopedomListByPopedom(popedom, search,user.getOid(),1);
        ObjectToJson.objectToJson(list,new String[]{"orgPopedomHashSet"},response);
    }
    public List<Popedom> getMenu(User u) {
        String popedom=userService.getPopedomStringByUser(u);
        HashMap<String, String> search = popedomService.getAllPopedomSearch(1);
        return popedomService.findFullPopedomListByPopedom(popedom, search, u.getOid(),1);
    }

    //跳转到高管管理界面
    @RequestMapping("/toManagesList.do")
    public String toManagesList(){
//        Organization organization= (Organization) session.getAttribute("organization");
//        if (organization.getInitState().equals("0")) {//没正式启用
//            return "/system/managesBeforeImport";
//        }else {
            return "/system/manages";
//        }
    }

    //新增高管
    @ResponseBody
    @RequestMapping("/addManage.do")
    public void addFinance(User user,String roleCode,Integer sonOid,Integer passiveUserId,AuthInfoDto authInfo,HttpServletRequest request,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<>();
        if (passiveUserId!=null&&roleCode!=null&&!roleCode.isEmpty()) {
            Integer oid=user.getOid();
            if (sonOid!=null){ // 1.214多地点 lixu加
                oid=orgService.getOidByOrgSonOrg(oid,sonOid);
            }
            Organization o = orgService.getByOid(oid,true,false);
            User manage=userService.getUserByRoleCode(o.getId(),roleCode);//考虑到并发，添加高管前先查一下 是否已有该高管。
            if (manage==null) {
                User passiveUser = userService.getUserByID(passiveUserId);//被选为高管的人

                User u = userService.addManage(user, o, passiveUser.getMobile(), roleCode, passiveUser.getUserName(),request,authInfo);

                u.setMasterUserID(passiveUserId);//存一下主用户id
                u.setAccId(passiveUser.getAccId());// 身份数据也存 账号id
                userService.updateUser(u);
                rolePrincipalHistoryService.saveRolePrincipalHistoryBy(u, passiveUser, roleCode);//保存高管身份变更历史

                String messageCont = "您已在系统中被指定为" + u.getRoleName() + "，请及时处理与之有关的各项工作！";
                userSuspendMsgService.saveUserSuspendMsg(1, messageCont, messageCont, "操作时间  " + user.getUserName() + " " + NewDateUtils.dateToString(u.getCreateTime(), "yyyy-MM-dd HH:mm:ss"), passiveUserId, "", u.getUserID());//推送我的消息

                List<User> userList = userService.getUsersByMasterUserID(passiveUserId);// 包含员工本身 的身份列表
                clusterMessageSendingOperations.convertAndSendToUser(passiveUserId.toString(), "/changeRolePrincipals", null, null, u.getOid(), u.getOrganization().getName(), JSON.toJSONString(userList));
                map.put("status", 1);//成功
            }else {
                map.put("status", 2);//已有该高管，不能再次添加，请刷新后操作。
            }

        }else {
            map.put("status", 0);//成功

        }
        ObjectToJson.objectToJson1(map,new String[]{},response);

//        return "redirect:/sys/toManagesList.do";
    }

    //1.125 参数由 userID 改为 masterUserID    9/22日 需求变更  此 接口作废  9/28日  需求再次变更，此接口作为 代理会计编辑 换人用
    @ResponseBody
    @RequestMapping("/editManage.do")
    public JsonResult editManage(User user,Integer manageId,String phone,String userName) throws IOException {
        Integer state=0;// 失败
        if (phone!=null&&userName!=null&&!phone.isEmpty()&&!userName.isEmpty()) {
            User manageUser = userService.getUserByID(manageId);
//                userHistoryService.addUserHistoryByUser(user);//保存历史记录
            Date oldUserTime;
            List<RolePrincipalHistory> rolePrincipalHistories = rolePrincipalHistoryService.getRolePrincipalHistoryByManageId(manageId);
            if (rolePrincipalHistories.size() > 0) {
                oldUserTime = rolePrincipalHistories.get(0).getCreateDate();//上任 高管的开始时间
            } else {//没有历史 记录 说明是历史数据 人员，需要补上初始记录
                rolePrincipalHistoryService.saveRolePrincipalHistoryBy(manageUser, manageUser, manageUser.getRoleCode());//保存高管身份变更历史
                oldUserTime = manageUser.getCreateTime();
            }
            User passiveUser=new User();// 代理会计换人 没有实际 的被选员工，所以new一个 代替
            passiveUser.setUserName(userName);
            passiveUser.setMobile(phone);

            rolePrincipalHistoryService.saveRolePrincipalHistory(manageUser, passiveUser, user, oldUserTime);//保存 换人历史记录

            manageUser.setUserName(userName);
//            manageUser.setLogonPwd(userService.getLogonPwdByPhone(phone));
            AuthAcc authAcc= authService.newEnabledAccInOrg(phone,userName, manageUser.getOrganization());// 生成登陆账号
            manageUser.setAccId(authAcc.getId());
            manageUser.setMobile(phone);
//            manageUser.setLogonName(phone);
            manageUser.setHandleTime(new Date());
            manageUser.setUpdateDate(new Date());
            manageUser.setUpdateName(user.getUserName());//操作人
            manageUser.setUpdator(user.getUserID());//操作人id
            manageUser.setVersionNo(manageUser.getVersionNo() == null ? 1 : manageUser.getVersionNo() + 1);
            userService.updateUser(manageUser);
//                    userHistoryService.addUserHistoryByUser(u);//保存历史记录
            state=1;//成功
        }
        return new JsonResult(1,state);
    }

    @RequestMapping("/onOrOffManage.do")
    public String onOrOffManage(Integer userID,String isDuty){
        User u = userService.getUserByID(userID);
        u.setIsDuty(isDuty);
        userService.updateUser(u);
        return "redirect:/sys/toManagesList.do";
    }

    //更改审批状态
    @RequestMapping("/changeApprove.do")
    public String changeApprove(ApprovalItem approvalItem, Integer postID, Integer userID, User user){
        Integer flag = null;
        Organization organization = user.getOrganization();
        ApprovalItem ap = roleService.getApprovalItemById(approvalItem.getId());
        if(approvalItem.getApproveUser()!=null)
            flag = Integer.valueOf(approvalItem.getApproveUser());

        ApprovalFlow approvalFlow = new ApprovalFlow();
        List<ApprovalFlow> approvalFlows = ap.getApprovalFlowHashSet();

        if(approvalFlows!=null&&approvalFlows.size()>0){//说明已有记录，则在原有记录上进行更新
            for(ApprovalFlow al : approvalFlows){
                approvalFlow=al;
            }
        }

        if(approvalItem.getStatus()==0){
            approvalFlow.setToOrg(null);
            approvalFlow.setToPost(null);
            approvalFlow.setToRole(null);
            approvalFlow.setToUser(null);
            approvalFlow.setType(0);
        }
        else if(flag!=null){
            if(flag==1){
                approvalFlow.setDescription("默认为上级审批");
                approvalFlow.setFromOrg(organization.getId());
                approvalFlow.setFromRole(Integer.valueOf(user.getPostID()));
                approvalFlow.setType(2);
                approvalFlows.add(approvalFlow);
            }else if(flag==2){
                approvalFlow.setDescription("超管审批");
                approvalFlow.setFromOrg(organization.getId());
                approvalFlow.setFromRole(Integer.valueOf(user.getPostID()));
                approvalFlow.setToRole(Integer.valueOf(user.getPostID()));
                approvalFlow.setType(3);
                approvalFlows.add(approvalFlow);
            }else if(flag==3){
                approvalFlow.setDescription("指定审批人审批");
                approvalFlow.setFromOrg(organization.getId());
                approvalFlow.setFromRole(Integer.valueOf(user.getPostID()));
                User user1 = userService.getUserByID(userID);
                approvalFlow.setToUser(String.valueOf(userID)+"_"+(user1.getUserName()==null?"未填写姓名":user1.getUserName()));
                approvalFlow.setType(1);
                approvalFlows.add(approvalFlow);
            }
        }
        for(ApprovalFlow a:approvalFlows){
            a.setItem(approvalItem);
        }
        ap.setApprovalFlowHashSet(approvalFlows);
        ap.setStatus(approvalItem.getStatus());
        roleService.updateApprovalItem(ap);
        return "redirect:/sys/toApproveIndex.do";
    }

    /**
     * <AUTHOR>
     * @Date 2017/10/27
     *  申请开通一个新机构
     */
    @ResponseBody
    @RequestMapping("/applyCustomer.do")
    public JsonResult applyCustomer(String logonName) throws Exception {
        String url=System.getProperty("miners.glptApiRoot")+"/message/addCustomerMessage.do";
        Map<String,String> map=new HashMap<String,String>();
        map.put("mobile", logonName);
//        JsonResult jsonResult = MinersHttpClientUtils.postJson(url,map);
        HttpClientUtils client = new HttpClientUtils(url);
        JsonResult jsonResult = client.jsonResponseToT(client.doPost(null, url, null,null, map),JsonResult.class);
        return jsonResult;
    }

    /**
     * <AUTHOR>
     * @Date 2017/7/4 14:53
     * 高管办理离职
     *
     */
    @RequestMapping("/outManage.do")
    public void outManage(User user,Integer id,String phone,String userName,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();
        Organization o = user.getOrganization();
        if (id!=null&&phone.length()==11) {
            User userByLogonName = userService.getUserByOidAndPhone(o.getId(),phone);
            if(userByLogonName!=null){
                map.put("status",2);//手机号已存在
            }else {
                User manageUser=userService.outManage(o,id,phone,userName);
                map.put("status", 1);//成功
                map.put("user",manageUser);
            }
        }else {
            map.put("status", 0);//参数不对
        }
        ObjectToJson.objectToJson1(map,new String[]{"parent","userMessages","personnelOccupations","personalEducations",
                "personnelSalaryLogUser","personalRewardPunishments","personalAssessments","personnelLeaveUser","personnelOvertimeUser",
                "userLogs","personnelReimburseHashSet","roles","organization"},response);
    }


    /**
    * <AUTHOR>
    * @Date 2018/1/25 17:45
    * 新增会计、代理会计接口
     *
     * 2020/9/11 1.125多重身份 李旭
    */
    @ResponseBody
    @RequestMapping("/addAccounting.do")
    public void addAccounting(String phone,User user,String agentType,String userName,Integer passiveUserId,AuthInfoDto authInfo,HttpServletRequest request,HttpServletResponse response) throws IOException {
        Integer oid= user.getOid();
        List<User> users=userService.getUserListByOidPhone(oid,phone);
        Map<String,Object> map=new HashMap<>();
        if (!"".equals(phone)&&!"".equals(agentType)&&!"".equals(userName)) {
            if (users.size() > 1) {
//            model.addAttribute("error","已存在的手机号！请更换.");
                map.put("status", 2);//
            } else {
                Organization o = orgService.getByOid(oid,true,false);
                User u;
                if(passiveUserId!=null) { // 被选择人id 不为null 说明 选择了 机构员工
                    User passiveUser= userService.getUserByID(passiveUserId);//被选为高管的人
                    phone=passiveUser.getMobile();
                    userName=passiveUser.getUserName();
                    u=userService.addAccountingUser(user,o,phone,agentType,userName,request,authInfo);
                    u.setMasterUserID(passiveUserId);//存一下主用户id
                    userService.updateUser(u);

                    String messageCont="您已在系统中被指定为"+u.getRoleName()+"，请及时处理与之有关的各项工作！";
                    userSuspendMsgService.saveUserSuspendMsg(1, messageCont, messageCont,"操作时间  "+user.getUserName()+" " + NewDateUtils.dateToString(u.getCreateTime(), "yyyy-MM-dd HH:mm:ss"), passiveUserId, "", u.getUserID());//推送我的消息

                    List<User> userList= userService.getUsersByMasterUserID(passiveUserId);// 包含员工本身 的身份列表
                    clusterMessageSendingOperations.convertAndSendToUser(passiveUserId.toString(),"/changeRolePrincipals",null,null,passiveUser.getOid(),passiveUser.getOrganization().getName(), JSON.toJSONString(userList));

                }else {
                    u=userService.addAccountingUser(user,o,phone,agentType,userName,request,authInfo);
                }
                rolePrincipalHistoryService.saveRolePrincipalHistoryBy(u, u,user.getRoleCode());//保存高管身份变更历史

                map.put("status", 1);//成功
                map.put("user",u);
            }
        }else {
            map.put("status", 0);//参数不对
        }
        ObjectToJson.objectToJson1(map,new String[]{"personnelFolksHashSet","personnelInterviewHashSet","userPopedomHashSet","personnelOvertimeUser","personnelLeaveUser","parent","roles","educationClasses","teacherRecommend","organization","educations","leaves"
                ,"userMessages","personnelOccupationUser","personalEducationUser","personnelSalaryLogUser","personalRewardPunishmentUser","personalAssessmentUser"
                ,"userFeedbackUser","userLogs","opMemberAssignerUser","opMemberTracerUser","opMemberSubmitterUser","opTraceTracerUser","opTraceTracerDetailUser","inputStream","volumeM"
                ,"volumeY","transferTime","page","offDutyDate","onDutyDate","volume","submit","submitM","submitY","password"
                ,"money","moneyY","moneym","lv","lvM","lvY","logonState","new","newDiary","contentApprovalHashSet","contentHashSet","personnelReimburseHashSet","personnelOccupations","personalEducations","personnelSalaryLogUser"
                ,"personalRewardPunishments","personalAssessments","personnelLeaveUser","personnelOvertimeUser","userLogs","personnelReimburseHashSet","default","collegeName1","ebeginTime1","eendTime1","major1","degree1","ememo1","collegeName2"
                ,"ebeginTime2","eendTime2","major2","degree2","ememo2","collegeName3","ebeginTime3","eendTime3","major3","degree3","ememo3","occurDate1","ocontent1","omemo1","ooperator1","ooperatorName1","occurDate2","ocontent2","omemo2","ooperator2","ooperatorName2","occurDate3"
                ,"ocontent3","omemo3","ooperator3","ooperatorName3","corpName1","beginTime1","endTime1","post1","csalary1","dmemo1","operatingDuty1","corpName2","beginTime2","endTime2","post2","csalary2","dmemo2","operatingDuty2"
                ,"corpName3","beginTime3","endTime3","post3","csalary3","dmemo3","operatingDuty3","assessDate1","assessUser1","type1","content1","amemo1","assessUserName1","assessDate2","assessUser2","type2","content2","amemo2","assessUserName2"
                ,"assessDate3","assessUser3","type3","content3","amemo3","assessUserName3","salary1","adjustDate1","admustResaon1","operateTime1","operator1","operatorName1","smemo1","salary2","adjustDate2","admustResaon2","operateTime2","operator2"
                ,"operatorName2","smemo2","salary3","adjustDate3","admustResaon3","operateTime3","operator3","operatorName3","smemo3"
        },response);
    }
    
    
    /**
    * <AUTHOR>
    * @Date 2018/1/26 11:38
    * 新增(小会计)、代理小会计
    */
    @ResponseBody
    @RequestMapping("/addSmallAccounting.do")
    public void addSmallAccounting(User user,String phone,String userName,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<>();

        if (!"".equals(phone)&&!"".equals(userName)) {
            List<User> users=userService.getUserListByOidPhone(user.getOid(),phone);
            if (users.size() > 0) {
                map.put("status", 2);//手机号存在
            }else {
                if ("accounting".equals(user.getRoleCode()) || "agentAccounting".equals(user.getRoleCode())) {
                    Organization o = orgService.getByOid(user.getOid(),true,false);
                    User u = new User();
//                    u.setLogonName(phone);
                    u.setMobile(phone);
//                    u.setLogonPwd(userService.getLogonPwdByPhone(phone));//获取该有的登录密码
                    u.setOrganization(o);
                    u.setOid(o.getId());
                    u.setCreateTime(new Date());
                    u.setLeader(String.valueOf(user.getUserID()));
                    u.setLeaderName(user.getUserName());
                    u.setLogonState(0L);
                    u.setOid(o.getId());
                    u.setLoginStatus(0);
                    u.setUserName(userName);
                    u.setHandleTime(new Date());

                    u.setOrdinaryEmployees(1);//非普通员工
                    u.setAgentType(user.getAgentType());//会计代理状态 1-代理，0-不代理
//                    u.setUserType(5);
                    if ("1".equals(u.getAgentType())) {
                        u.setIsDuty("3");//可登录的代理人员
                        u.setManagerCode("agentAccounting");//高管code
                        u.setRoleCode("agentSmallAccounting");//自己权限code
//                        AuthAcc authAcc= authService.newEnabledAcc(u.getMobile(),u.getUserName());// 生成登陆账号
//                        u.setAccId(authAcc.getId());
                        userService.addUser(u, o);
                        userPopedomService.saveUserPopedomByCode("agentSmallAccounting",u);//给代理会计赋权限
                        u.setSuperId(o.getSuperId());
                        userService.updateUser(u);
                    } else {
                        u.setSuperId(o.getSuperId());
                        u.setIsDuty("1");//正式职工
                        u.setManagerCode("accounting");//高管code
                        u.setRoleCode("smallAccounting");//自己权限code
//                        AuthAcc authAcc= authService.newEnabledAcc(u.getMobile(),u.getUserName());// 生成登陆账号
//                        u.setAccId(authAcc.getId());
                        userService.addUser(u, o);
                        userPopedomService.saveUserPopedomByCode("smallAccounting", u);//给会计赋权限
                    }

                    map.put("status", 1);//成功
                    map.put("user", u);
                } else {
                    map.put("status", 3);//失败，操作者不是会计
                }
            }
        }else {
            map.put("status", 0);//参数不对
        }

        ObjectToJson.objectToJson1(map,new String[]{"personnelFolksHashSet","personnelInterviewHashSet","userPopedomHashSet","personnelOvertimeUser","personnelLeaveUser","parent","roles","educationClasses","teacherRecommend","organization","educations","leaves"
                ,"userMessages","personnelOccupationUser","personalEducationUser","personnelSalaryLogUser","personalRewardPunishmentUser","personalAssessmentUser"
                ,"userFeedbackUser","userLogs","opMemberAssignerUser","opMemberTracerUser","opMemberSubmitterUser","opTraceTracerUser","opTraceTracerDetailUser","inputStream","volumeM"
                ,"volumeY","transferTime","page","offDutyDate","onDutyDate","volume","submit","submitM","submitY","password"
                ,"money","moneyY","moneym","lv","lvM","lvY","logonState","new","newDiary","contentApprovalHashSet","contentHashSet","personnelReimburseHashSet","personnelOccupations","personalEducations","personnelSalaryLogUser"
                ,"personalRewardPunishments","personalAssessments","personnelLeaveUser","personnelOvertimeUser","userLogs","personnelReimburseHashSet","default","collegeName1","ebeginTime1","eendTime1","major1","degree1","ememo1","collegeName2"
                ,"ebeginTime2","eendTime2","major2","degree2","ememo2","collegeName3","ebeginTime3","eendTime3","major3","degree3","ememo3","occurDate1","ocontent1","omemo1","ooperator1","ooperatorName1","occurDate2","ocontent2","omemo2","ooperator2","ooperatorName2","occurDate3"
                ,"ocontent3","omemo3","ooperator3","ooperatorName3","corpName1","beginTime1","endTime1","post1","csalary1","dmemo1","operatingDuty1","corpName2","beginTime2","endTime2","post2","csalary2","dmemo2","operatingDuty2"
                ,"corpName3","beginTime3","endTime3","post3","csalary3","dmemo3","operatingDuty3","assessDate1","assessUser1","type1","content1","amemo1","assessUserName1","assessDate2","assessUser2","type2","content2","amemo2","assessUserName2"
                ,"assessDate3","assessUser3","type3","content3","amemo3","assessUserName3","salary1","adjustDate1","admustResaon1","operateTime1","operator1","operatorName1","smemo1","salary2","adjustDate2","admustResaon2","operateTime2","operator2"
                ,"operatorName2","smemo2","salary3","adjustDate3","admustResaon3","operateTime3","operator3","operatorName3","smemo3"
        },response);
    }

    /**
    * <AUTHOR>
    * @Date 2018/1/26 18:49
    * 修改大会计代理状态
    */
    @ResponseBody
    @RequestMapping("/editAccountingAgentType.do")
    public void editAccountingAgentType(User user,String agentType,Integer userId,String phone,String userName,Integer passiveUserId,HttpServletResponse response) throws IOException {
        User manageUser=userService.getUserByID(userId);
//        User superUser= (User) session.getAttribute("user");
//        List<User> users=userService.getUserListByOidPhone(user.getOid(),phone);
        Map<String,Object> map=new HashMap<>();

        if (!"".equals(phone)&&!"".equals(agentType)&&!"".equals(userName)) {
//            if (users.size() > 1) {
//                map.put("status", 2);//已存在的手机号！请更换.
//            } else {
//            manageUser.setLogonPwd(userService.getLogonPwdByPhone(phone));//获取该有的登录密码
//                user.setLogonName(phone);
            AuthAcc authAcc= authService.newEnabledAccInOrg(phone,userName, manageUser.getOrganization());// 生成登陆账号
            manageUser.setAccId(authAcc.getId());
            manageUser.setMobile(phone);
            manageUser.setUserName(userName);
            manageUser.setUpdateDate(new Date());
            manageUser.setUpdateName(user.getUserName());
                Integer masterUserId=passiveUserId;
                if (manageUser.getAgentType().equals(agentType)) {
                    userService.updateUser(manageUser);
                } else {
                    Organization o = orgService.getByOid(manageUser.getOid(),true,false);
                    //添加大会计或代理大会计3
                    manageUser.setAgentType(agentType);//会计代理状态 1-代理，0-不代理

                    Date oldUserTime; // 前者上任时间
                    User passiveUser=new User();//被选中为新会计的人，非代理情况下 是人， 代理情况下 附上值传到下一层。

                    List<RolePrincipalHistory> rolePrincipalHistories = rolePrincipalHistoryService.getRolePrincipalHistoryByManageId(userId);
                    if (rolePrincipalHistories.size() > 0) {
                        oldUserTime = rolePrincipalHistories.get(0).getCreateDate();//上任 高管的开始时间
                    } else {//没有历史 记录 说明是历史数据 人员，需要补上初始记录
                        rolePrincipalHistoryService.saveRolePrincipalHistoryBy(manageUser, manageUser, manageUser.getRoleCode());//保存高管身份变更历史
                        oldUserTime = manageUser.getCreateTime();
                    }

//                    if (userHistoryService.getEditAccountingAgentTypeCounts(user.getOid())==0){
//                        userHistoryService.addManageUserHistoryByUser(user);//改之前的会计历史
//                    }

                    userPopedomService.deleteUserPopedomAll(new ArrayList<UserPopedom>(manageUser.getUserPopedomHashSet()));// 删除之前全部权限

                    if ("1".equals(agentType)) {//非代理改成代理
//                        userPopedomService.deleteUserPopedomByRoleCode(userId, user.getRoleCode());//删除会计权限
                        List<User> users = userService.getUserListByCodeOid(manageUser.getOid(), manageUser.getManagerCode(), "1");//小会计
                        for (User u : users) {
                            if (!manageUser.getManagerCode().equals(manageUser.getRoleCode())) {
                                userPopedomService.deleteUserPopedomByRoleCode(u.getUserID(), manageUser.getRoleCode());//删除小会计权限
                                u.setRoleCode("staff");
                                u.setManagerCode("");
                                userService.updateUser(u);//把小会计变成普通员工
                            }
                        }
                        masterUserId=manageUser.getMasterUserID();//之前的员工
                        manageUser.setRoleCode("agentAccounting");
                        manageUser.setManagerCode("agentAccounting");
                        manageUser.setRoleName("会计事务的负责人(代理)");
                        manageUser.setIsDuty("3");//可登录的代理人员
                        manageUser.setSuperId(null);
                        manageUser.setMasterUserID(null);// 把坑制空

                        // 代理会计换人 没有实际 的被选员工，所以new一个 代替
                        passiveUser.setUserName(userName);
                        passiveUser.setMobile(phone);

                    } else {//代理改成非代理
                        List<User> users = userService.getUserListByCodeOid(manageUser.getOid(), manageUser.getManagerCode(), "3");//代理小会计
                        for (User u : users) {
                            if (!u.getManagerCode().equals(u.getRoleCode())) {
//                                userService.deleteUser(u);//删除代理小会计
                                u.setIsDuty("2");
                                userService.updateUser(u);// 代理小会计的删除 改成 离职
                            }
                        }
                        manageUser.setIsDuty("1");//正式会计高管
                        manageUser.setManagerCode("accounting");//高管code
                        manageUser.setRoleCode("accounting");//自己权限code
                        manageUser.setRankUrl("/"+user.getUserID());
                        manageUser.setRoleName("会计事务的负责人");
                        manageUser.setSuperId(o.getSuperId());

                        if(passiveUserId!=null) { // 被选择人id 不为null 说明 选择了 机构员工
                            manageUser.setMasterUserID(passiveUserId);//存一下主用户id
                            masterUserId=passiveUserId;
                            passiveUser = userService.getUserByID(passiveUserId);//被选为高管的人


                            String messageCont="您已在系统中被指定为"+manageUser.getRoleName()+"，请及时处理与之有关的各项工作！";
                            userSuspendMsgService.saveUserSuspendMsg(1, messageCont, messageCont,"操作时间  "+user.getUserName()+" " + NewDateUtils.dateToString(manageUser.getCreateTime(), "yyyy-MM-dd HH:mm:ss"), passiveUserId, "", manageUser.getUserID());//推送我的消息

                        }

                    }
                    userService.updateUser(manageUser);

                    if (o.getInitState().equals("1")) { //机构已正式启用
                        User finance=userService.getUserByRoleCode(o.getId(),"finance");  // 财务高管   1.307初始化 会计部分   会计初始化完成 标志为 财务初始化完成，  完成前只有 初始化-会计模块
                        UserPopedom userPopedom=userPopedomService.getUserPopedomByUserIdMid(finance.getUserID(),"vh");
                        if (userPopedom==null) {  //财务 初始化完成
                            userPopedomService.saveUserPopedomByCode(manageUser.getRoleCode(), manageUser);//给会计赋 对应code权限
                        }else {   //财务初始化 未完成  只给 初始化-会计菜单
                            userPopedomService.saveUserPopedomByCode("initialBeforeAccounting", manageUser);//给会计赋 对应code权限
                        }
                    }
//                    userPopedomService.saveUserPopedomByCode(manageUser.getRoleCode(), manageUser);//给会计赋 对应code权限

                    rolePrincipalHistoryService.saveRolePrincipalHistory(manageUser, passiveUser, user, oldUserTime);//保存 换人历史记录

                    List<User> users= userService.getUsersByMasterUserID(masterUserId);// 包含员工本身 的身份列表
                    clusterMessageSendingOperations.convertAndSendToUser(masterUserId.toString(),"/changeRolePrincipals",null,null,user.getOid(),user.getOrganization().getName(), JSON.toJSONString(users));

//                    userHistoryService.addManageUserHistoryByUser(user);//改之后的会计历史

                    map.put("user",manageUser);

                }
                map.put("status", 1);//成功

//            }
        }else {
            map.put("status", 0);//参数不对
        }
        ObjectToJson.objectToJson1(map,new String[]{"personnelFolksHashSet","personnelInterviewHashSet","userPopedomHashSet","personnelOvertimeUser","personnelLeaveUser","parent","roles","educationClasses","teacherRecommend","organization","educations","leaves"
                ,"userMessages","personnelOccupationUser","personalEducationUser","personnelSalaryLogUser","personalRewardPunishmentUser","personalAssessmentUser"
                ,"userFeedbackUser","userLogs","opMemberAssignerUser","opMemberTracerUser","opMemberSubmitterUser","opTraceTracerUser","opTraceTracerDetailUser","inputStream","volumeM"
                ,"volumeY","transferTime","page","offDutyDate","onDutyDate","volume","submit","submitM","submitY","password"
                ,"money","moneyY","moneym","lv","lvM","lvY","logonState","new","newDiary","contentApprovalHashSet","contentHashSet","personnelReimburseHashSet","personnelOccupations","personalEducations","personnelSalaryLogUser"
                ,"personalRewardPunishments","personalAssessments","personnelLeaveUser","personnelOvertimeUser","userLogs","personnelReimburseHashSet","default","collegeName1","ebeginTime1","eendTime1","major1","degree1","ememo1","collegeName2"
                ,"ebeginTime2","eendTime2","major2","degree2","ememo2","collegeName3","ebeginTime3","eendTime3","major3","degree3","ememo3","occurDate1","ocontent1","omemo1","ooperator1","ooperatorName1","occurDate2","ocontent2","omemo2","ooperator2","ooperatorName2","occurDate3"
                ,"ocontent3","omemo3","ooperator3","ooperatorName3","corpName1","beginTime1","endTime1","post1","csalary1","dmemo1","operatingDuty1","corpName2","beginTime2","endTime2","post2","csalary2","dmemo2","operatingDuty2"
                ,"corpName3","beginTime3","endTime3","post3","csalary3","dmemo3","operatingDuty3","assessDate1","assessUser1","type1","content1","amemo1","assessUserName1","assessDate2","assessUser2","type2","content2","amemo2","assessUserName2"
                ,"assessDate3","assessUser3","type3","content3","amemo3","assessUserName3","salary1","adjustDate1","admustResaon1","operateTime1","operator1","operatorName1","smemo1","salary2","adjustDate2","admustResaon2","operateTime2","operator2"
                ,"operatorName2","smemo2","salary3","adjustDate3","admustResaon3","operateTime3","operator3","operatorName3","smemo3"
        },response);
    }

    /**
    * <AUTHOR>
    * @Date 2018/1/27 9:29
    * 小会计人员列表
    */
    @ResponseBody
    @RequestMapping("/smallAccountingList.do")
    public void smallAccountingList(User user,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<>();
        List<User> users = userService.getUserListByCodeOid(user.getOid(), user.getManagerCode(), user.getIsDuty());//小会计
        if (users.isEmpty()){
            map.put("status", 0);//没有
        }else {
            map.put("status", 1);//有
            map.put("users",users);
        }
        ObjectToJson.objectToJson1(map,new String[]{"personnelFolksHashSet","personnelInterviewHashSet","userPopedomHashSet","personnelOvertimeUser","personnelLeaveUser","parent","roles","educationClasses","teacherRecommend","organization","educations","leaves"
                ,"userMessages","personnelOccupationUser","personalEducationUser","personnelSalaryLogUser","personalRewardPunishmentUser","personalAssessmentUser"
                ,"userFeedbackUser","userLogs","opMemberAssignerUser","opMemberTracerUser","opMemberSubmitterUser","opTraceTracerUser","opTraceTracerDetailUser","inputStream","volumeM"
                ,"volumeY","transferTime","page","offDutyDate","onDutyDate","volume","submit","submitM","submitY","password"
                ,"money","moneyY","moneym","lv","lvM","lvY","logonState","new","newDiary","contentApprovalHashSet","contentHashSet","personnelReimburseHashSet","personnelOccupations","personalEducations","personnelSalaryLogUser"
                ,"personalRewardPunishments","personalAssessments","personnelLeaveUser","personnelOvertimeUser","userLogs","personnelReimburseHashSet","default","collegeName1","ebeginTime1","eendTime1","major1","degree1","ememo1","collegeName2"
                ,"ebeginTime2","eendTime2","major2","degree2","ememo2","collegeName3","ebeginTime3","eendTime3","major3","degree3","ememo3","occurDate1","ocontent1","omemo1","ooperator1","ooperatorName1","occurDate2","ocontent2","omemo2","ooperator2","ooperatorName2","occurDate3"
                ,"ocontent3","omemo3","ooperator3","ooperatorName3","corpName1","beginTime1","endTime1","post1","csalary1","dmemo1","operatingDuty1","corpName2","beginTime2","endTime2","post2","csalary2","dmemo2","operatingDuty2"
                ,"corpName3","beginTime3","endTime3","post3","csalary3","dmemo3","operatingDuty3","assessDate1","assessUser1","type1","content1","amemo1","assessUserName1","assessDate2","assessUser2","type2","content2","amemo2","assessUserName2"
                ,"assessDate3","assessUser3","type3","content3","amemo3","assessUserName3","salary1","adjustDate1","admustResaon1","operateTime1","operator1","operatorName1","smemo1","salary2","adjustDate2","admustResaon2","operateTime2","operator2"
                ,"operatorName2","smemo2","salary3","adjustDate3","admustResaon3","operateTime3","operator3","operatorName3","smemo3"
        },response);


    }

    /**
    * <AUTHOR>
    * @Date 2018/2/3 13:54
    * 获取小会计当前权限
    */
    @ResponseBody
    @RequestMapping("/getAccountingPopedomList.do")
    public void getAccountingPopedomList(Integer manageId,Integer userId,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();
        if (userId!=null&&manageId!=null){
            User manage=userService.getUserByID(manageId);
            User user=userService.getUserByID(userId);
            map.put("status", 1);//成功
            map.put("popedomList", user.getUserPopedomHashSet());

        }else {
            map.put("status", 0);//失败
        }
        ObjectToJson.objectToJson1(map,new String[]{"user"},response);


    }




    /**
    * <AUTHOR>
    * @Date 2018/2/2 17:15
    * 保存会计分配的权限
    */
    @ResponseBody
    @RequestMapping("/saveAccountingPopedomList.do")
    public void saveAccountingPopedomList(User user,Integer userId,HttpServletResponse response,String ... mid) throws IOException {
        Map<String,Object> map=new HashMap<String,Object>();

        if (user!=null&&userId!=null&&mid!=null){
            userPopedomService.deleteUserPopedomByRoleCode(userId,"accounting");

            for (String m:mid) {
                Popedom popedom = popedomService.getPopedomByMid(m);
                if (popedom.getUrl() != null && !"".equals(popedom.getUrl())) {
                    UserPopedom userPopedom = new UserPopedom();
                    userPopedom.setMid(m);
                    userPopedom.setOid(user.getOid());
                    userPopedom.setUserId(userId);
                    userPopedomService.saveUserPopedom(userPopedom);
                }
            }
            User selectUser=userService.getUserByID(userId);
            userService.getPopedomStringByUser(selectUser,true);//变更权限时用。
            popedomTaskService.updateUserBadgeNumber(selectUser);//重新计算角标
            map.put("status", 1);//成功
            map.put("popedomList",selectUser.getUserPopedomHashSet());
        }else {
            map.put("status", 0);//失败
        }
        ObjectToJson.objectToJson1(map,new String[]{"user"},response);

    }

    /**
     * <AUTHOR>
     * @Date 20180409
     * 锁屏核对密码
     */
    @RequestMapping("/checkPassword.do")
    @ResponseBody
    public Boolean checkPassword(String sessionid, AuthAcc acc, String password, HttpServletResponse response){
        boolean result = false;
        if( StringUtils.isNotEmpty(password)) {
            result = acc.checkPassword(password);
            if(result){
//                CookieUtils.setCookie(response,"lockScreen","", 0);//清除修改密码锁屏的标志
                setNewDelayLock(sessionid,System.currentTimeMillis());
            }
        }
        return result;
    }

    /**
     * <AUTHOR>
     * @description 超时锁屏，接收send并刷新操作时间，同时支持长连接和http提交
     * @date Create at 2018/08/03, adapted at 2020/4/4 00:24
     * @method refreshOperatortime
     * @param data
     * @return java.lang.String 固定返回"1"，避免ajax.error
    **/
    @ResponseBody
    @RequestMapping("/refreshOperatortime.do")
    @MessageMapping("/refreshOperatortime")
    public String refreshOperatortime(String sessionid,String data, Long operatortime, Boolean operationed) {
        System.out.println("refreshOperatortime sessionid : "+ sessionid + ", operatortime :" + operatortime + ", operatored :" + operationed);
        if(StringUtils.isNotBlank(data)) {
            JSONObject jsonObject = JSON.parseObject(data.replaceAll("&quot;", "'"));
            operatortime = jsonObject.getLong("operatortime");
            operationed = jsonObject.getBoolean("operationed");
        }
        if(operationed) {
            Long now = System.currentTimeMillis();
            operatortime=Math.abs(now-operatortime)<TimeUnit.MINUTES.toMillis(1)?operatortime:now;//wyu：如果传上来的操作时间误差超过1分钟，使用当前服务器时间
            redisTemplate.opsForValue().set("miners:operatortime:"+sessionid,operatortime,35, TimeUnit.MINUTES);//wyu：延长35分钟过期
            redisTemplate.opsForValue().set("miners:logout:"+sessionid,operatortime,35, TimeUnit.MINUTES);//wyu：延长35分钟过期
//            System.out.println("operatortime:"+new SimpleDateFormat("yyyy-MM-dd HH:mm:ss:SSS").format(operatortime));
        }
        Integer userLogId;
        if((userLogId = (Integer) redisTemplate.opsForValue().get("miners:userLogId:" + sessionid))!=null) {
            userLogService.setDuration(userLogId);
            redisTemplate.expire("miners:userLogId:" + sessionid, 35, TimeUnit.MINUTES);//wyu：延长35分钟过期。
        }
        return "1";
    }
    /**
     * <AUTHOR>
     * @Date 20180731
     * 超时锁屏，接收send不刷新操作时间
     */
    @MessageMapping("/lockScreenCheck")
    public void lockScreenCheck(String sessionid,JSONObject json) {
        Long operatortime=json.getLong("operatortime");
        Long now = System.currentTimeMillis();
        operatortime=Math.abs(now-operatortime)<TimeUnit.MINUTES.toMillis(1)?operatortime:now;//wyu：如果传上来的操作时间误差超过1分钟，使用当前服务器时间
        setNewDelayLock(sessionid,operatortime);
        Integer userLogId;
        if((userLogId = (Integer) redisTemplate.opsForValue().get("miners:userLogId:" + sessionid))!=null) {
            userLogService.setDuration(userLogId);
            redisTemplate.expire("miners:userLogId:" + sessionid, 35, TimeUnit.MINUTES);//wyu：延长35分钟过期。
        }
    }
    /**
     * <AUTHOR>
     * @Date 20180731
     * 重设锁屏延时器，20200305修改设置两个key，大文件上传key延长logout时间。
     */
    private void setNewDelayLock(String sessionid, Long operatortime){
        final long lockMillis = TimeUnit.MINUTES.toMillis(10);//wyu：10分钟的毫秒数
        final long delayMillis = TimeUnit.MINUTES.toMillis(1);//wyu：超时lock消息发两次
        final long logoutkMillis = TimeUnit.MINUTES.toMillis(30);//wyu：30分钟的毫秒数
        if(!MyStrings.nulltoempty(sessionid).isEmpty()&&operatortime!=null) {
            String keyLock = "miners:operatortime:" + sessionid;
            redisTemplate.opsForValue().set(keyLock, operatortime, 35, TimeUnit.MINUTES);//设置缓存35分钟失效
            LockScreenCheck lockScreenCheck = new LockScreenCheck(keyLock,sessionid,lockMillis,delayMillis);
            String keyLogout = "miners:logout:" + sessionid;
            redisTemplate.opsForValue().set(keyLogout, operatortime, 35, TimeUnit.MINUTES);//设置缓存35分钟失效
            clusterMessageSendingOperations.delayCall(lockMillis, lockScreenCheck, false);
            LockScreenCheckLogout lockScreenCheckLogout = new LockScreenCheckLogout(keyLogout,sessionid,logoutkMillis, delayMillis);
            clusterMessageSendingOperations.delayCall(logoutkMillis, lockScreenCheckLogout, false);
        }
    }

    /**
     * 延长注销时间半小时，仅延时自动注销时间和登录时长(避免session超时，不能使用长连接请求)。
     * <AUTHOR>
     * @since 2021/4/23 15:25
     * @param sessionid
     * @return: java.lang.String
     * @return java.lang.String 固定返回"1",避免ajax.error
    **/
    @ResponseBody
    @RequestMapping("/refreshLogoutTime.do")
    public void refreshLogoutTime(String sessionid) {
        //每次延长35分钟
        redisTemplate.opsForValue().set("miners:logout:" + sessionid, System.currentTimeMillis(), 35, TimeUnit.MINUTES);
        //延长登录时间
        Integer userLogId;
        if((userLogId = (Integer) redisTemplate.opsForValue().get("miners:userLogId:" + sessionid))!=null) {
            userLogService.setDuration(userLogId);
            redisTemplate.expire("miners:userLogId:" + sessionid, 35, TimeUnit.MINUTES);//wyu：延长35分钟过期。
        }
//        return NewDateUtils.dateToString(session.getLastAccessedTime(),"yyyy-MM-dd HH:mm:ss.SSS");
    }

    /**
     * <AUTHOR>
     * @Date 2018/4/17 11:13    2024/1/15调整 lixu
     * 给所有机构历史在职人员附上 等级路径，直系上级串
     */
    @RequestMapping("/rankUrl.do")
    public void rankUrl(HttpServletRequest request, HttpServletResponse response) throws Exception {
        //将实体对象转换为JSON Object转换
        response.setCharacterEncoding("UTF-8");
        response.setContentType("text/plain; charset=utf-8");
        response.setHeader("Cache-Control", "no-store");
        response.setHeader("Pragma", "no-cache");
        response.setDateHeader("expires", -1);
        PrintWriter writer = response.getWriter();
        if(!"i4h1Krq3A90ZL4yWG1iVznE0x1E9IKF".equals(request.getParameter("value"))){
            writer.write("参数不对！");
            return;
        }
        userService.updateUsersRankUrl(writer);
//        userService.updateSuperRankUrl("/"); //先把董事长的 路径改成/   再更新所有人员路径
//        List<User> userList=userService.getAllUser();
//        for (User u:userList){
//            if (StringUtils.isNotEmpty(u.getLeader())&&!"0".equals(u.getLeader())){
//                String url="";
//                url=this.getUserLeader(u,url);
//                u.setRankUrl(url);//级别串
//                writer.println("正在检查,人员："+u.getMobile()+"机构："+u.getOrganization().getName());
//                response.flushBuffer();
//                System.out.println("正在检查,人员："+u.getMobile()+" 机构："+u.getOrganization().getName());
//            }
//        }
        System.out.println("恭喜恭喜，各机构在职人员级别串插入完成！");
        writer.write("恭喜恭喜，各机构在职人员级别串插入完成！");
        writer.close();
    }

    private String getUserLeader(User user,String url) throws Exception {
        User leader=userService.getUserByID(Integer.valueOf(user.getLeader()));
        if (leader!=null) {
            if ((url+"/").indexOf("/"+leader.getUserID()+"/")>=0){
                throw new Exception(leader.getUserID()+"+++"+leader.getMobile()+"+++"+url+"+++"+user.getOrganization().getName()+"+++"+user.getOid()+"+++"+user.getUserID()+"+++"+user.getMobile());
            }
            url = "/" + leader.getUserID() + url;
            if (!"0".equals(leader.getLeader()) && leader.getLeader() != null&&!"".equals(leader.getLeader())) {
                url = this.getUserLeader(leader, url);
            }
        }
        return url;
    }

    /**
     * <AUTHOR>
     * @Date 2022/10/11
     * 1.230中枢管控 获取可选为总经理的人员列表
     */
    @ResponseBody
    @RequestMapping("/getSelectStaffs.do")
    public JsonResult getSelectStaffs(User user,Integer sonOid) throws Exception {
        Integer oid = user.getOid();
        if (sonOid != null) { //
            oid = orgService.getOidByOrgSonOrg(oid, sonOid);
        }
        List<UserDto> userList = userService.getStaffAndLocking(oid,null);
        return new JsonResult(1, userList);
    }


    /**
    * <AUTHOR>
    * 新增机构小超管（总经理） 1.230中枢管控
     * 2022/10/13
    */
    @ResponseBody
    @RequestMapping("/addOrgSmallSuper.do")
    public JsonResult addOrgSmallSuper(User user,Integer passiveUserId,Integer sonOid,HttpServletRequest request,AuthInfoDto authInfo) throws IOException {
        Map<String,Object> map=new HashMap<>();

        if (passiveUserId!=null) {
            Integer oid= user.getOid();
            if (sonOid!=null){ // 1.214多地点 lixu加
                oid=orgService.getOidByOrgSonOrg(oid,sonOid);
            }
            userService.addSmallSuper(user,oid,passiveUserId,map,request,authInfo);
        }else {
            map.put("status", 0);
        }
        return new JsonResult(1,map);

    }

    /**
     * <AUTHOR>
     * @Date 2022/10/20 9:43
     * 更换小超管（总经理） 1.230中枢管控
     */
    @ResponseBody
    @RequestMapping("/updateOrgSmallSuper.do")
    public JsonResult updateOrgSmallSuper(User user,Integer oldUserId,Integer newUserId,Integer sonOid,HttpServletRequest request,AuthInfoDto authInfo) throws IOException {
        Map<String,Object> map=new HashMap<>();
        if (oldUserId!=null&&newUserId!=null){
            if (user.getRoleCode().equals("super")){
                Integer oid= user.getOid();
                if (sonOid!=null){ // 1.214多地点 lixu加
                    oid=orgService.getOidByOrgSonOrg(oid,sonOid);
                }
                User passiveUser=userService.updateOrgSmallSuper(user,oid,oldUserId,newUserId,request,authInfo);
                map.put("status", 1);//成功
                map.put("smallSuper", passiveUser);//小超管信息
            } else {
                map.put("status", 2);//你不是超管
            }
        }else {
            map.put("status", 0);
        }
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Date 2022/10/21 8:00
     * 暂不设置总经理(小超管) 1.230中枢管控
     */
    @ResponseBody
    @RequestMapping("/noSmallSuper.do")
    public JsonResult noSmallSuper(User user,Integer oldUserId,Integer sonOid) throws IOException {
        Map<String, Object> map = new HashMap<>();
        if (oldUserId != null && user.getRoleCode().equals("super")) {
            Integer oid= user.getOid();
            if (sonOid!=null){ // 1.214多地点 lixu加
                oid=orgService.getOidByOrgSonOrg(oid,sonOid);
            }
            userService.noSmallSuper(user,oldUserId,oid);
            map.put("status", 1);//成功
        } else {
            map.put("status", 0);
        }
        return new JsonResult(1, map);
    }


        /**
        * <AUTHOR>
        * @Date 2018/7/18 10:03
        * 动态 高管管理
        */
    @ResponseBody
    @RequestMapping("/dynamicManageList.do")
    public void dynamicManageList(User user,Integer sonOid,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<>();
        List<Map<String,Object>> list=new ArrayList<>();
        Integer oid = user.getOid();
        if (sonOid!=null){ // 1.214多地点 lixu加
            oid=orgService.getOidByOrgSonOrg(oid,sonOid);
        }
        Organization organization=orgService.getByOid(oid,true,false);
        String superButton = "0";// 设置小超管按钮 0-没有按钮，1有按钮
        String manageButton = "0";// 操作高管 按钮 0-没有按钮，1有按钮
        if (user.getRoleCode().equals("smallSuper")) {
            manageButton = "1";//登录人是总经理  可以操作 高管
            superButton = "0";// 有懂事长，总经理不能操作 总经理
        } else if (user.getRoleCode().equals("super")) {
            User smallSuper = userService.getUserByRoleCode(oid, "smallSuper");//总经理
            if (smallSuper!=null&&smallSuper.getUpdateDate()==null){
                smallSuper.setUpdateDate(smallSuper.getCreateTime());
            }
            Map<String,Object> listMap=new HashMap<>();
            listMap.put("name","全权负责人");
            listMap.put("code","smallSuper");
            listMap.put("user",smallSuper);
            list.add(listMap);

            superButton = "1";// 总经理 你可以操作
            if (smallSuper!=null){
                manageButton = "0";// 你是懂事长，说明有总经理，高管得由总经理去操作
            }else {
                manageButton = "1";// 没有有总经理，可以操作
            }
        }
        List<OrgPopedom> orgPopedomList=orgPopedomService.getOrgPopedomListByOid(oid);
        for (OrgPopedom orgPopedom:orgPopedomList){
            Map<String, Object> listMap = new HashMap<>();
            switch (orgPopedom.getMid()) {
                case "kb":
//                if ("kb".equals(orgPopedom.getMid())) {
                    User general = userService.getUserByRoleCode(oid, "general");//总务
                    if (general!=null&&general.getUpdateDate()==null){
                        general.setUpdateDate(general.getCreateTime());
                    }
                    listMap.put("name", "总务权限的分配者");
                    listMap.put("code", "general");
                    listMap.put("user", general);
                    list.add(listMap);
                    break;
//                }
                case "ld":
                    User finance = userService.getUserByRoleCode(oid, "finance");//财务
                    if (finance!=null&&finance.getUpdateDate()==null){
                        finance.setUpdateDate(finance.getCreateTime());
                    }
                    listMap.put("name","财务权限的分配者");
                    listMap.put("code","finance");
                    listMap.put("user",finance);
                    list.add(listMap);
                    break;
                case "qb":
                    User sale = userService.getUserByRoleCode(oid, "sale");//销售
                    if (sale!=null&&sale.getUpdateDate()==null){
                        sale.setUpdateDate(sale.getCreateTime());
                    }
                    listMap.put("name","销售权限的分配者");
                    listMap.put("code","sale");
                    listMap.put("user",sale);
                    list.add(listMap);
                    break;
                case "sb":
                    User accounting = userService.getUserByRoleCode(oid, "agentAccounting");//代理会计
                    if (accounting == null) {
                        accounting = userService.getUserByRoleCode(oid, "accounting");//会计
                    }
                    if (accounting!=null&&accounting.getUpdateDate()==null){
                        accounting.setUpdateDate(accounting.getCreateTime());
                    }
                    listMap.put("name","会计事务的负责人");
                    listMap.put("code","accounting");
                    listMap.put("user",accounting);
                    list.add(listMap);
                    break;
            }
        }
//        if (orgPopedomService.getOrgPopedomByMid(oid,"kb")){
//            User general = userService.getUserByRoleCode(oid, "general");//总务
//            Map<String,Object> listMap=new HashMap<>();
//            listMap.put("name","总务权限的分配者");
//            listMap.put("code","general");
//            listMap.put("user",general);
//            list.add(listMap);
//        }
//        if (orgPopedomService.getOrgPopedomByMid(oid,"ld")){
//            User finance = userService.getUserByRoleCode(oid, "finance");//财务
//            Map<String,Object> listMap=new HashMap<>();
//            listMap.put("name","财务权限的分配者");
//            listMap.put("code","finance");
//            listMap.put("user",finance);
//            list.add(listMap);
//        }
//        if (orgPopedomService.getOrgPopedomByMid(oid,"qb")){
//            User sale = userService.getUserByRoleCode(oid, "sale");//销售
//            Map<String,Object> listMap=new HashMap<>();
//            listMap.put("name","销售权限的分配者");
//            listMap.put("code","sale");
//            listMap.put("user",sale);
//            list.add(listMap);
//        }
//        if (orgPopedomService.getOrgPopedomByMid(oid,"sb")){
//            User accounting = userService.getUserByRoleCode(oid, "agentAccounting");//代理会计
//            if (accounting == null) {
//                accounting = userService.getUserByRoleCode(oid, "accounting");//会计
//            }
//            Map<String,Object> listMap=new HashMap<>();
//            listMap.put("name","会计事务的负责人");
//            listMap.put("code","accounting");
//            listMap.put("user",accounting);
//            list.add(listMap);
//        }
        map.put("manageList",list);
        map.put("superButton", superButton);
        map.put("manageButton", manageButton);
        map.put("initState",organization.getInitState());
        ObjectToJson.objectToJson1(map,new String[]{"personnelFolksHashSet","personnelInterviewHashSet","userPopedomHashSet","personnelOvertimeUser","personnelLeaveUser","parent","roles","educationClasses","teacherRecommend","organization","educations","leaves"
                ,"userMessages","personnelOccupationUser","personalEducationUser","personnelSalaryLogUser","personalRewardPunishmentUser","personalAssessmentUser"
                ,"userFeedbackUser","userLogs","opMemberAssignerUser","opMemberTracerUser","opMemberSubmitterUser","opTraceTracerUser","opTraceTracerDetailUser","inputStream","volumeM"
                ,"volumeY","transferTime","page","offDutyDate","onDutyDate","volume","submit","submitM","submitY","password"
                ,"money","moneyY","moneym","lv","lvM","lvY","logonState","new","newDiary","contentApprovalHashSet","contentHashSet","personnelReimburseHashSet","personnelOccupations","personalEducations","personnelSalaryLogUser"
                ,"personalRewardPunishments","personalAssessments","personnelLeaveUser","personnelOvertimeUser","userLogs","personnelReimburseHashSet","default","collegeName1","ebeginTime1","eendTime1","major1","degree1","ememo1","collegeName2"
                ,"ebeginTime2","eendTime2","major2","degree2","ememo2","collegeName3","ebeginTime3","eendTime3","major3","degree3","ememo3","occurDate1","ocontent1","omemo1","ooperator1","ooperatorName1","occurDate2","ocontent2","omemo2","ooperator2","ooperatorName2","occurDate3"
                ,"ocontent3","omemo3","ooperator3","ooperatorName3","corpName1","beginTime1","endTime1","post1","csalary1","dmemo1","operatingDuty1","corpName2","beginTime2","endTime2","post2","csalary2","dmemo2","operatingDuty2"
                ,"corpName3","beginTime3","endTime3","post3","csalary3","dmemo3","operatingDuty3","assessDate1","assessUser1","type1","content1","amemo1","assessUserName1","assessDate2","assessUser2","type2","content2","amemo2","assessUserName2"
                ,"assessDate3","assessUser3","type3","content3","amemo3","assessUserName3","salary1","adjustDate1","admustResaon1","operateTime1","operator1","operatorName1","smemo1","salary2","adjustDate2","admustResaon2","operateTime2","operator2"
                ,"operatorName2","smemo2","salary3","adjustDate3","admustResaon3","operateTime3","operator3","operatorName3","smemo3"
        },response);
    }


    /**
    * <AUTHOR>
    * @Date 2018/9/14 9:31
    * 机构列表更多分页接口
    */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/getOrgListPageInfo.do")
    public JsonResult getOrgListPageInfo(AuthAcc acc, String orgCode, PageInfo pageInfo){
//        System.out.println(JSON.toJSONString(request.getAttribute("acc")));
//        System.out.println(JSON.toJSONString(acc));
//        String token = request.getHeader("token");
//        System.out.println(token);
//        token = authService.flushToken(token);
//        System.out.println(token);
//        Map<String, Object> map = authService.verifyToken(token);
//        System.out.println(JSON.toJSONString(map));
//        User user = (User) session.getAttribute("user");
        List<String> orgCodes = Organization.getAllSearchCode();
        if(StringUtils.isNotEmpty(orgCode)) {
            orgCodes = new ArrayList<String>(1) {{
                add(orgCode);
            }};
        }
        List<UserLoginDto> userLoginDtoList = userService.getUserList(acc.getId(), User.personCodes(), User.getIsDutyListWebCanLogin(), orgCodes, pageInfo);
        List<UserLoginDto> list=userService.getUserMsgCountSum(acc.getId()); //按机构分组查询 手机号多重身份角标和
        for (UserLoginDto userLoginDto:userLoginDtoList){
            for (UserLoginDto u:list){
                if (userLoginDto.getOid().equals(u.getOid())){
                    userLoginDto.setMsgCount(u.getMsgCount());
                }
            }
        }
        return new JsonResult(1,userLoginDtoList,pageInfo);
    }

    @ResponseBody
    @RequestMapping("/listRoleCodes.do")
    public JsonResult listRoleCodes(User user) {
        final Map<String, String> map = new HashMap<String,String>(){{
            put("super", "董事长");
            put("general", "总务权限的分配者");
            put("finance", "财务权限的分配者");
            put("sale", "销售权限的分配者");
            put("agentAccounting", "会计事务的负责人(代理)");
            put("accounting", "会计事务的负责人");
            put("smallSuper", "全权负责人");
        }};
        Map<String, Object> result = new HashMap<>();
        Integer oid = user.getOid();
        for (Entry<String, String> entry : map.entrySet()) {
            User manageUser=userService.getUserByRoleCode(oid,entry.getKey());
            if(manageUser==null) {
                result.put(entry.getValue(), null);
            } else {
                result.put(entry.getValue(), manageUser.getMobile()+":"+manageUser.getRoleCode());
            }
        }
        return new JsonResult(1, result);
    }

    /**
     * <AUTHOR>
     * @Date 2020/5/12 18:49
     * 1.96 版 高管管理的修改记录
     *passiveUserId 被查看人的id
     */
    @ResponseBody
    @RequestMapping("/getHighManageUpRecord.do")
    public JsonResult getHighManageUpRecord(Integer passiveUserId,PageInfo pageInfo){
        User user=userService.getUserByID(passiveUserId);
        List<UserHistory> userHistoryList= userHistoryService.getUserBasicInfoHistoryListByUserId(passiveUserId,null,pageInfo);
        List<Map<String,Object>> mapList=new ArrayList<>();
        int number=0;
        if (pageInfo.getCurrentPageNo()>1){
            number=(pageInfo.getCurrentPageNo()-1)*pageInfo.getPageSize();
        }

        String dataState="";
        String updateName="";
        Date updateTime=new Date();
        for (UserHistory userHistory:userHistoryList){
            Map<String,Object> map=new HashMap<>();

            if (pageInfo.getCurrentPageNo()==1&&number==0){
                dataState="原始信息";
                updateName=userHistory.getCreateName();
                updateTime=userHistory.getCreateTime();

            }else {
                dataState="第"+number+"次修改后";
                updateName=userHistory.getUpdateName();
                updateTime=userHistory.getUpdateDate();
            }
            map.put("dataState",dataState);//资料状态
            map.put("id",userHistory.getId());
            map.put("userName",userHistory.getUserName());//高管姓名
            map.put("mobile",userHistory.getMobile());//高管手机号
            map.put("updateName",updateName);//操作人名
            map.put("updateTime",updateTime);//修改时间
            mapList.add(map);
            number+=1;
        }

        Map<String,Object> map=new HashMap<>();
//        map.put("number",number==0?0:number-1);//最新的修改次数
        map.put("number",pageInfo.getTotalResult());//最新的修改次数
        map.put("updateName",updateName);//人
        map.put("updateTime",updateTime);//修改时间
        map.put("userHistoryList",mapList);
        map.put("pageInfo",pageInfo);
        return new JsonResult(1,map);

    }


    /**
     * <AUTHOR>
     * @Date 2020/5/12 18:49
     * 1.96 版 会计代理状态的修改记录
     *passiveUserId 被查看人的id
     */
    @ResponseBody
    @RequestMapping("/getEditAccountingAgentTypeRecord.do")
    public JsonResult getEditAccountingAgentTypeRecord(Integer passiveUserId,PageInfo pageInfo){
        User user=userService.getUserByID(passiveUserId);
        List<UserHistory> userHistoryList= userHistoryService.getEditAccountingAgentTypeHistories(user.getOid(),pageInfo);
        List<Map<String,Object>> mapList=new ArrayList<>();
        int number=0;
        if (pageInfo.getCurrentPageNo()>1){
            number=(pageInfo.getCurrentPageNo()-1)*pageInfo.getPageSize();
        }

        String dataState="";
        String updateName="";
        Date updateTime=new Date();
        for (UserHistory userHistory:userHistoryList){
            Map<String,Object> map=new HashMap<>();

            if (pageInfo.getCurrentPageNo()==1&&number==0){
                dataState="原始信息";
                updateName=userHistory.getCreateName();
                updateTime=userHistory.getCreateTime();

            }else {
                dataState="第"+number+"次修改后";
                updateName=userHistory.getUpdateName();
                updateTime=userHistory.getUpdateDate();
            }
            map.put("dataState",dataState);//资料状态
            map.put("id",userHistory.getId());
            map.put("agentType",userHistory.getAgentType());//会计代理类型，1-代理，0-非代理
            map.put("userName",userHistory.getUserName());//高管姓名
            map.put("mobile",userHistory.getMobile());//高管手机号
            map.put("updateName",updateName);//操作人名
            map.put("updateTime",updateTime);//修改时间
            mapList.add(map);
            number+=1;
        }

        Map<String,Object> map=new HashMap<>();
        map.put("number",number==0?0:number-1);//最新的修改次数
//        map.put("number",number);//最新的修改次数
        map.put("updateName",updateName);//人
        map.put("updateTime",updateTime);//修改时间
        map.put("userHistoryList",mapList);
        map.put("pageInfo",pageInfo);
        return new JsonResult(1,map);
    }

    /**
     * <AUTHOR>
     * @Date 2020/9/12 18:49
     * 1.125多重身份 获取可换为高管的人员列表
     * roleCode
     */
    @ResponseBody
    @RequestMapping("/getSelectStaffUsers.do")
    public JsonResult getSelectStaffUsers(User user,Integer sonOid,String roleCode) throws Exception {
//        if (userId==null&&user==null){
//            throw new Exception("参数userId不能为空！");
//        }
//        userId=userId==null?user.getUserID():userId;

//        User loginUser = userService.getUserByID(userId);
        Integer oid=user.getOid();
        if (sonOid!=null){ // 1.214多地点 lixu加
            oid=orgService.getOidByOrgSonOrg(oid,sonOid);
        }
        User manageUser= userService.getUserByRoleCode(oid,roleCode);

        List<User> userList=userService.getUserListByOrgId(oid," and roleCode in('staff','super','smallSuper') and isDuty in('1','9')");
        if (manageUser!=null){
            if ("smallSuper".equals(manageUser.getRoleCode())) {
                User remUser = userService.getUserByID(manageUser.getUserID());// 已是高管的员工
                userList.remove(remUser);
            }else {
                User remUser = userService.getUserByID(manageUser.getMasterUserID());// 已是高管的员工
                //把目前已是 高管的人员 再列表中排除
                userList.remove(remUser);
            }
        }
        return new JsonResult(1,userList);
    }

    /**
     * <AUTHOR>
     * @Date 2020/9/12 18:49
     * 1.125多重身份 总务、财务、销售 换为其他人
     *passiveUserId 被换的人的id
     */
    @ResponseBody
    @RequestMapping("/replaceManages.do")
    public JsonResult replaceManages(User user, Integer manageId, Integer passiveUserId, HttpServletRequest request, AuthInfoDto authInfo) throws Exception {
//        if (userId==null&&loginUser==null){
//            throw new Exception("参数userId不能为空！");
//        }
//        userId=userId==null?loginUser.getUserID():userId;

        Integer state=0;//失败
        if (user!=null&manageId!=null&passiveUserId!=null) {
            rolePrincipalHistoryService.replaceManages(user.getUserID(),Arrays.asList(new AbstractMap.SimpleEntry<>(manageId,passiveUserId)),request,authInfo);
            state=1;//成功
        }
        return new JsonResult(1,state);

    }

    /**
     * <AUTHOR>
     * @Date 2020/9/15 16:00
     * 1.125多重身份 查看某种历任高管列表
     *
     */
    @ResponseBody
    @RequestMapping("/getRolePrincipalHistories.do")
    public JsonResult getRolePrincipalHistories(Integer manageId){
        User manageUser=userService.getUserByID(manageId);
        List<RolePrincipalHistory> rolePrincipalHistories=new ArrayList<>();
        if (manageUser.getRoleCode().equals("smallSuper")){
            rolePrincipalHistories=rolePrincipalHistoryService.getRolePrincipalHistoriesByOidRoleCode(manageUser.getOid(),"smallSuper");
        }else {
            rolePrincipalHistories=rolePrincipalHistoryService.getRolePrincipalHistoryByManageId(manageId);
        }
        Map<String,Object> map=new HashMap<>();
        map.put("manageInfo",manageUser);
        map.put("orgName",manageUser.getOrganization().getName());
        map.put("orgAddress",manageUser.getOrganization().getAddress());
        map.put("rolePrincipalHistories",rolePrincipalHistories);
        return new JsonResult(1,map);
    }


    /**
     * <AUTHOR>
     * @Date 2020/9/17 14:24
     * 1.125多重身份 拥有多重身份的人员登录获取身份 及身份角标 列表
     *
     */
//    @ResponseBody
//    @RequestMapping("/getRolePrincipals.do")
//    public JsonResult getRolePrincipals(HttpSession session,Integer userId) throws Exception {
//        System.out.println("getRolePrincipals.do sessionid: "+ session.getId());
//        User user= (User) session.getAttribute("user");
//        if (userId==null&&user==null){
//            throw new Exception("参数userId不能为空！");
//        }
//        userId=userId==null?user.getUserID():userId;
//        System.out.println(userId);
//        user=userService.getUserByID(userId);
//
//        Integer masterUserId=userId;//暂且认为 当前登录的是 员工
//        if (user.getMasterUserID()!=null){
//            //当前是身份 ，把正确员工id赋值。
//            masterUserId=user.getMasterUserID();
//        }
//        List<User> userList= userService.getUsersByMasterUserID(masterUserId);// 包含员工本身 的身份列表
//        System.out.println("getRolePrincipals list");
//        for(User u: userList) {
//            System.out.println(u.getUserID() + " : " + u.getUserName());
//        }
//        userList.remove(user);//排除当前 登录的 账号，剩余账号展示在可切换身份中。
//        return  new JsonResult(1,userList);
//    }



    /**
     * <AUTHOR>
     * @Date 2020/10/28 15:52
     * 1.128 冻结账号  换代办人的 选择人员列表接口
     *
     */
    @ResponseBody
    @RequestMapping("/getOnJobUsers.do")
    public JsonResult getOnJobUsers(User user){
//        User user= (User) session.getAttribute("user");
//        if (userId==null&&user==null){
//            throw new Exception("参数userId不能为空！");
//        }
//        userId=userId==null?user.getUserID():userId;

//        User loginUser = userService.getUserByID(userId);
        // 要查出来 B代A 展示为灰色 不可被选取。
        List<User> userList=userService.getUserListByOrgId(user.getOid()," and roleCode in('staff','super','agent') and isDuty in('1','9')");
        return new JsonResult(1,userList);
    }

    /**
     * <AUTHOR>
     * @Date 2025/4/7
     * 根据与吴老师沟通
     * 给小程序提供 的 主页 和工作 权限接口
     */
    @ResponseBody
    @RequestMapping("/getAppHomePageAndWorks.do")
    public JsonResult getAppHomePageAndWorks(User user){
        Map<String,Object> map=userService.getAppHomePageAndWorks(user);
        return new JsonResult(1,map);

    }

    @AuthPassport(guest = true)
    @ResponseBody
    @RequestMapping("/getServerTail.do")
    public JsonResult getServerTail(){
        String ip= GetLocalIPUtils.getServerIp();
        int index=ip.lastIndexOf(".");
        if(index>=0) {
            return new JsonResult(1,ip.substring(index+1));
        } else {
            return new JsonResult(new MyException("500"));
        }
    }
}