<!--在本日流水页面 点击按账户查看， 展示账户列表-->
<template>
  <ul class="val_list">
    <li v-for="item in accountList" :key="item.id" class="ta-list">
      <div @click="rou(item.id)">
        <span>{{ item.name }}</span>
        <span>当前余额</span>
        <span>{{ item.moeny }}</span>
        <span>
        <i class="fa fa-angle-right"></i>
      </span>
      </div>

    </li>
  </ul>
</template>

<script>

import finance from '@/mixins/finance.js'

export default {
  mixins: [finance],
  data() {
    return {
      name:'' ,
      accountList: [
        {id: 0, name: "现金/备用金"},
        {id: 1, name: "银行账户汇总"},
      ]
    }
  },

  mounted() {
    let name = this.$route.name
    console.log('path ===== ', name)
    this.name = name
    let org = this.auth.getOrg()
    let params = { oid:org.id }
    if(name === 'ViewByAccountYear'){
      params.state = 4
      this.getAccountData(params)
    }else if(name === 'ViewByAccount'){
      params.state = 1
      this.getAccountData(params)
    }else if(name === 'ViewByAccountYearList'){
      let beginDate = localStorage.getItem('searchYearList_Begin')
      let endDate = localStorage.getItem('searchYearList_End')
      params.state = 5
      params.beginDate = beginDate
      params.endDate = endDate
      this.getAccountData(params)
    }else if(name === 'ViewByAccountMonthList'){
      let beginDate = localStorage.getItem('searchYearDur_monthBegin')
      let endDate = localStorage.getItem('searchYearDur_monthEnd')
      params.state = 5
      params.beginDate = beginDate
      params.endDate = endDate
      this.getAccountData(params)
    }else if(name === 'ViewByAccountDayList'){
      let beginDate = localStorage.getItem('searchMonthDur3_beginDate')
      let endDate = localStorage.getItem('searchMonthDur3_endDate')

      params.state = 5
      params.beginDate = beginDate
      params.endDate = endDate
      this.getAccountData(params)
    }

  },
  methods: {
    getAccountData(params){
      console.log('传参=', params)
      this.financeApi.getDataByAccountApp(params).then(res1=>{
        let res = res1.data.data
        console.log('getDataByAccountApp res=', res)
        let accountPeriod1 = res.accountPeriod1
        let accountPeriod2 = res.accountPeriod2
        let accountPeriod3 = res.accountPeriod3 || []
        this.accountList = [
          {id: accountPeriod1.accountId, name: accountPeriod1.bankName, moeny: this.formatCurrency(accountPeriod1.balance) },
          {id: accountPeriod2.accountId, name: accountPeriod2.bankName, moeny: this.formatCurrency(accountPeriod2.balance) },
        ]
        accountPeriod3.forEach(bankItem=>{
          this.accountList.push({
            id: bankItem.accountId, name: bankItem.bankName, moeny: this.formatCurrency(bankItem.balance)
          })
        })

      }).catch(err=>{
        console.log('err=', err)
        this.$message.error('链接失败，请重试！')
      })

    },
    rou(id) {
      let path = ``, name = this.name
      if(name === 'ViewByAccountYear'){
        localStorage.setItem('currentAccountDate_beginDate', new Date().format('yyyy-MM-dd') )
        path = `/currentAccountMonth/${id}`
      }else if(name === 'ViewByAccount'){
        localStorage.setItem('currentAccountDate_beginDate', new Date().format('yyyy-MM-dd') )
        path = `/currentAccountDate/${id}`
      }else if(name === 'ViewByAccountYearList'){
        let item = {
          beginDate:  localStorage.getItem('searchYearList_Begin') ,
          endDate:  localStorage.getItem('searchYearList_End') ,
          fid : id
        }
        localStorage.setItem('searchByAccountYearList', JSON.stringify(item))
        path = `/searchByAccountYearList`
      }else if(name === 'ViewByAccountMonthList'){
        let item = {
          beginDate:  localStorage.getItem('searchYearDur_monthBegin') ,
          endDate:  localStorage.getItem('searchYearDur_monthEnd') ,
          fid : id
        }
        localStorage.setItem('searchYearDur2', JSON.stringify(item))
        path = `/searchYearDur2`
      }else if(name === 'ViewByAccountDayList'){
        localStorage.setItem('searchMonthDur_beginDate', localStorage.getItem('searchMonthDur3_beginDate'))
        localStorage.setItem('searchMonthDur_endDate', localStorage.getItem('searchMonthDur3_endDate'))
        localStorage.setItem('searchMonthDur_fid', id)
        path = `/searchMonthDur2`
      }

      this.$router.push({ path:path })
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../../style/account.scss";
@import "../../../style/common.scss";

.ta-list {
  span:nth-child(3), span:nth-child(2) {
    position: fixed;
    margin-bottom: 0px;
  }

  span:nth-child(3) {
    right: 35px;
  }

  span:nth-child(2) {
    right: 130px;
  }

  img {
    margin-top: 2px;
  }
}
.ta-list:nth-child(n+3){
  span:nth-child(1) {
    margin-left: 16px;
  }
}
</style>