package cn.sphd.miners.modules.commodity.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name="t_pd_category")
public class PdCategory implements Serializable {


    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "org", nullable = true, unique = false)
    private Integer org;//机构id

    @Column(name = "name", length = 100, nullable = true, unique = false)
    private String name;  //名称

    @Column(name = "code", length = 20, nullable = true, unique = false)
    private String code;   //代码

    @Column(name="parent"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer parentId;//父结点ID

    @Column(name = "level", nullable = true, unique = false)
    private Integer level=1;//级次（此表加级次是为了满足需求只到八级，为程序实现方便）

    @Column(name = "orders", nullable = true, unique = false)
    private Integer orders;//'同级排序'

    @Column(name = "children_count", nullable = true, unique = false)
    private Integer childrenCount=0;//''儿子数量(子分类数量)'

    @Column(name = "leaf_count", nullable = true, unique = false)
    private Integer leafCount=0;//'叶子数量'

    @Column(name = "mdse_count", nullable = true, unique = false)
    private Long mdseCount=0L;//'包含商品数量'

    @Column(name = "path", length = 255, nullable = true, unique = false)
    private String path;   //路径，记录各父节点id以 / 分割

    @Column(name = "first_grade_id", nullable = true, unique = false)
    private Integer firstGradeId;//'根节点id'

    @Column(name = "is_locked", nullable = true, unique = false)
    private Boolean locked=true;//'是否锁定，true锁定。用于标识是否可修改、删除'

    @Column(name = "keywords", length = 100, nullable = true, unique = false)
    private String keywords;   //关键字

    @Column(name = "memo", length = 255, nullable = true, unique = false)
    private String memo;  //备注

    @Column(name = "creator", nullable = true, unique = false)
    private Integer creator;  //创建人id

    @Column(name = "create_name", length = 100, nullable = true, unique = false)
    private String createName;   //创建人

    @Column(name = "create_time", nullable = true, unique = false)
    private Date createTime;   //创建时间

    @Column(name = "updator", nullable = true, unique = false)
    private Integer updator;

    @Column(name = "update_name", length = 100, nullable = true, unique = false)
    private String updateName;

    @Column(name = "update_time", nullable = true, unique = false)
    private Date updateTime;

    @Column(name = "operation", length = 1, nullable = true, unique = false)
    private String operation="1";  // 操作:1-增,2-删,3-修改

    @Column(name = "previous_id", nullable = true, unique = false)
    private Integer previousId;   //'修改前记录ID'

    @Column(name = "version_no", nullable = true, unique = false)
    private Integer versionNo;//版本号

    @ManyToOne(fetch= FetchType.EAGER )
    @JsonIgnore
    @JSONField(serialize = false)
    @JoinColumn(name="parent", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private PdCategory pdCategory;


    @JsonIgnore
    @JSONField(serialize = false)
    @OneToMany(targetEntity = PdCategory.class, fetch = FetchType.EAGER, mappedBy = "pdCategory", cascade = CascadeType.REMOVE)
    private Set<PdCategory> pdCategoryHashSet = new HashSet<PdCategory>();


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public Integer getChildrenCount() {
        return childrenCount;
    }

    public void setChildrenCount(Integer childrenCount) {
        this.childrenCount = childrenCount;
    }

    public Integer getLeafCount() {
        return leafCount;
    }

    public void setLeafCount(Integer leafCount) {
        this.leafCount = leafCount;
    }

    public Long getMdseCount() {
        return mdseCount;
    }

    public void setMdseCount(Long mdseCount) {
        this.mdseCount = mdseCount;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Integer getFirstGradeId() {
        return firstGradeId;
    }

    public void setFirstGradeId(Integer firstGradeId) {
        this.firstGradeId = firstGradeId;
    }

    public Boolean getLocked() {
        return locked;
    }

    public void setLocked(Boolean locked) {
        this.locked = locked;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }

    public PdCategory getPdCategory() {
        return pdCategory;
    }

    public void setPdCategory(PdCategory pdCategory) {
        this.pdCategory = pdCategory;
    }

    public Set<PdCategory> getPdCategoryHashSet() {
        return pdCategoryHashSet;
    }

    public void setPdCategoryHashSet(Set<PdCategory> pdCategoryHashSet) {
        this.pdCategoryHashSet = pdCategoryHashSet;
    }
}
