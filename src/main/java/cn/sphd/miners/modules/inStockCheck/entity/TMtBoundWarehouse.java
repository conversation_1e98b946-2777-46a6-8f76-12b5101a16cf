package cn.sphd.miners.modules.inStockCheck.entity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 材料_出入库智能库位对照表20241018 1.316入库 新增
 */
public class TMtBoundWarehouse {
    /**
    * ID
    */
    private Long id;

    /**
    * 库位ID
    */
    private Long location;

    /**
    * 供货关系ID,为空时,不区分供应商
    */
    private Integer supplierMaterial;

    /**
    * 数量
    */
    private BigDecimal amount;

    /**
    * 锁定数量
    */
    private BigDecimal lockedAmount;

    /**
    * 包装个数,0-无
    */
    private Integer packagingCount;

    /**
    * 数据单位
    */
    private String unit;

    /**
    * 备注
    */
    private String memo;

    /**
    * 创建人id
    */
    private Integer creator;

    /**
    * 创建人
    */
    private String createName;

    /**
    * 创建时间
    */
    private Date createDate;

    /**
    * 修改人id
    */
    private Integer updator;

    /**
    * 修改人
    */
    private String updateName;

    /**
    * 修改时间
    */
    private Date updateDate;

    /**
    * 操作:1-增,2-删,3-改,4-启停用
    */
    private String operation;

    /**
    * 修改前记录ID
    */
    private Integer previousId;

    /**
    * 版本号,每次修改+1
    */
    private Integer versionNo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getLocation() {
        return location;
    }

    public void setLocation(Long location) {
        this.location = location;
    }

    public Integer getSupplierMaterial() {
        return supplierMaterial;
    }

    public void setSupplierMaterial(Integer supplierMaterial) {
        this.supplierMaterial = supplierMaterial;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getLockedAmount() {
        return lockedAmount;
    }

    public void setLockedAmount(BigDecimal lockedAmount) {
        this.lockedAmount = lockedAmount;
    }

    public Integer getPackagingCount() {
        return packagingCount;
    }

    public void setPackagingCount(Integer packagingCount) {
        this.packagingCount = packagingCount;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
}