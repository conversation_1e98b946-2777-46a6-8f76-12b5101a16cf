<template>
  <div id="start" class="start">
    <div :class="regType === 1 ? 'main_avatar_min' : 'main_avatar'">
      <div class="logo_avatar">
        <div class="logo"><span class="logo_w">W</span>onderss</div>
        <div class="logo_welcome">欢迎使用通用框架管理系统</div>
      </div>
      <div class="login_login_avatar">
        <div class="login_avatar">
          <div v-if="mainCVisble === 1">
            <span class="rLink ccBtn" v-if="zhuShow">
              <span class="ty-linkBtn" @click="regAccBtn">注册账号</span>
              <a class="ty-linkBtn" target="_blank" href="https://www.btransmission.com">关于我们</a>
            </span>
            <div class="item" v-if="nav === 1">
              <div v-if="loginErr">
                <div class="conTtl">账号密码登录</div>
                <el-form class="form passwordForm" label-position="left" label-width="44px" :model="formLabelAlign">
                  <el-form-item label="账号" size="large" class="el-form-item_border_bottom">
                    <el-input v-model="formLabelAlign.name" placeholder="如无法确定，请咨询公司管理人员" :clearable="true"></el-input>
                  </el-form-item>
                  <el-form-item label="密码" size="large" class="el-form-item_border_bottom">
                    <el-input type="password" v-model="formLabelAlign.pass" placeholder="请输入密码" :clearable="true"></el-input>
                  </el-form-item>
                  <div v-if="passWdloginClickNum > 9" >
                    <el-input style="width:185px; display: inline-block;" clear v-model="formLabelAlign.codeVerify" placeholder="请输入右图中的字符" size="large"></el-input>
                    <div class="verificationCode">
                      <div id="checkCode"></div>
                      <span>看不清？<a class="ty-color-blue" @click="changePicVerification">换一个</a></span>
                    </div>
                  </div>
                  <div class="subC el-form-item">
                    <div class="login_btn" @click="loginBtn">登录</div>
                  </div>
                </el-form>
              </div>
              <div v-if="!loginErr">
                <div class="line20">
                  您已有{{ loginErrDay }} 天以上没有验证账号了！<br>
                  为了您数据的安全，本次登录需要您进行如下操作
                </div>
                <el-form class="form mobileForm" label-position="left" label-width="60px" :model="formLabelAlign">
                  <el-form-item label="账号" size="large" class="el-form-item_border_bottom">
                    <el-input v-model="formLabelAlign.name" placeholder="请输入手机号码"></el-input>
                  </el-form-item>
                  <el-form-item label="验证码" size="large" class="el-form-item_border_bottom">
                    <el-input v-model="formLabelAlign.code" placeholder="请输入验证码" class="code">
                      <template #append>
                        <span :class="['codeBtn', {'disabled': sendCodeDisabled}]" type="primary" @click="sendCode">{{sendCodeName}}</span>
                      </template>
                    </el-input>

                  </el-form-item>
                  <div class="subC el-form-item">
                    <div class="login_btn" @click="codeLoginBtn">登录</div>
                  </div>
                </el-form>

              </div>
            </div>
            <div class="item" v-if="nav === 2">
              <div class="conTtl">手机短信登录</div>
              <el-form class="form mobileForm" label-position="left" label-width="60px" :model="formLabelAlign">
                <el-form-item label="账号" size="large" class="el-form-item_border_bottom">
                  <el-input v-model="formLabelAlign.name" placeholder="请输入手机号码"></el-input>
                </el-form-item>
                <el-form-item label="验证码" size="large" class="el-form-item_border_bottom">
                  <el-input v-model="formLabelAlign.code" placeholder="请输入验证码" class="code">
                    <template #append>
                      <span :class="['codeBtn', {'disabled': sendCodeDisabled}]" type="primary" @click="sendCode">{{sendCodeName}}</span>
                    </template>
                  </el-input>

                </el-form-item>
                <div class="subC el-form-item">
                  <div class="login_btn" @click="codeLoginBtn(1)">登录</div>
                </div>
              </el-form>
            </div>
            <div class="item" v-if="nav === 3">
              <div v-if="loginErr3 === 1">
                <div class="conTtl">微信登录</div>
                <div class="form">
                  <wxlogin
                      style="height: 416px;width: 100%"
                      :appid="app.appId"
                      scope="snsapi_login"
                      theme='black'
                      :redirect_uri="redirectUriStr"
                      :href="href"
                      :state="state"
                      rel="external nofollow"
                  ></wxlogin>
                </div>
              </div>
              <div v-if="loginErr3 === 2">
                <p><span class="ty-color-red" style="font-weight: bold">!!提示</span></p><br>
                <div class="line20">
                  您已有{{ loginErr3Day }}天以上没有验证账号了，此次登录需要验证账号！<br><br>
                  要用哪个手机号作为您的账号？<br>
                  <span class="ty-color-blue">注：能获得短信验证码的手机号才能作为账号！</span>
                  <br><br>
                  <div>
                    <el-radio v-model="loginErr3Radio" label="1">继续使用{{ formLabelAlign.name }}作为账号</el-radio>
                    <br>
                    <el-radio v-model="loginErr3Radio" label="2">采用其他手机号作为账号</el-radio>
                    <br><br>
                  </div>
                  <div class="subC el-form-item">
                    <div class="login_btn" @click="loginErr3LoginBtn">下一步</div>
                  </div>
                </div>
              </div>
              <div v-if="loginErr3 === 3">
                <p><span class="tipstr">!!提示</span></p>
                <div>
                  <div>
                    <el-form class="form mobileForm" label-position="left" label-width="60px" :model="formLabelAlign">
                      <el-form-item label="账号" size="large" class="el-form-item_border_bottom">
                        <el-input v-model="formLabelAlign.name" placeholder="请输入中国大陆地区的手机号"></el-input>
                      </el-form-item>
                      <el-form-item label="验证码" size="large" class="el-form-item_border_bottom">
                        <el-input v-model="formLabelAlign.code" placeholder="请输入验证码" class="code">
                          <template #append>
                            <span :class="['codeBtn', {'disabled': sendCodeDisabled}]" type="primary" @click="sendCode2">{{sendCodeName}}</span>
                          </template>
                        </el-input>

                      </el-form-item>
                      <div class="subC el-form-item">
                        <div class="login_btn" @click="codeLoginBtn2">下一步</div>
                      </div>
                    </el-form>
                  </div>
                </div>
              </div>
              <div v-if="loginErr3 === 4">
                <p><span class="tipstr">!!提示</span></p>
                <div>
                  如需设置密码，请在下方设置！ <br>
                  <span class="ty-color-blue">注：能获得短信验证码的手机号才能作为账号！</span>
                  <div>
                    <el-form class="form formTr" label-position="left" label-width="80px" :model="codeNextFrm">
                      <el-form-item label="密码" class="el-form-item_border_bottom">
                        <el-input type="password" v-model="codeNextFrm.pass" placeholder="请输入密码" :clearable="true" autocomplete="new-password" show-password></el-input>
                      </el-form-item>
                      <el-form-item label="确认密码" class="el-form-item_border_bottom">
                        <el-input type="password" v-model="codeNextFrm.pass2" placeholder="请重新输入密码" :clearable="true" autocomplete="new-password" show-password></el-input>
                      </el-form-item>
                      <div style="color: #5d9cec; font-size: 14px; ">
                        <small>注：密码需为8-16位，必须包括数字和英文字母，英文字母分大小写</small>
                      </div>
                      <div class="subC el-form-item" style="margin-top: 16px">
                        <div class="login_btn" @click="codeNextBtn0">取  消</div>
                        <div class="login_btn" @click="codeNextBtn1">暂不设置，直接登录</div>
                        <div class="login_btn" @click="codeNextBtn">设置完毕，登录系统</div>
                      </div>
                    </el-form>
                  </div>
                  <div class="subC el-form-item">
                    <div class="login_btn" @click="loginErr3LoginBtn">下一步</div>
                  </div>
                </div>
              </div>
              <div v-if="loginErr3 === 5">
                <p><span class="ty-color-red" style="font-weight: bold">!!提示</span></p><br>
                <div class="line20">
                  您当前有两个领地，请选择您想要的领地！<br>
                  <br><br>
                  <div>
                    <el-radio v-model="loginErr3Ling" label="1">使用被冻结的原领地</el-radio>
                    <br>
                    <el-radio v-model="loginErr3Ling" label="2">使用新领地</el-radio>
                    <br><br>
                  </div>
                  <div class="subC el-form-item">
                    <div class="login_btn" @click="loginErr3LingBtn">下一步</div>
                  </div>
                </div>
              </div>

            </div>
            <div class="createShortCut" v-if="showShortcut">
              建议在桌面创建快捷方式！
              <el-tooltip effect="dark" placement="bottom">
                <i class="fa fa-question-circle"></i>
                <template #content>
                  <p class="shortcutTip" style="width: 450px">
                    {{shortcutTips}}
                  </p>
                </template>
              </el-tooltip>
              <a v-if="!isWindowsFirefox" class="ty-linkBtn ty-tight" @click="download">创建</a>
              <a v-if="isWindowsFirefox" class="ty-linkBtn ty-tight" @click="downloadBat">创建</a>
            </div>
            <div class="logMethod">
              <div class="logMethod_title">
                <div class="line"></div>
                <span class="ttlTxt">登陆方式</span>
                <div class="line"></div>
              </div>
              <div class="icon_row">
                <div :class="['imIm', {'active': nav===3}]" @click="changeNav(3)">
                  <span class="fa fa-weixin"></span><br>
                  微信
                </div>
                <div :class="['imIm', {'active': nav===2}]" @click="changeNav(2)">
                  <span class="fa fa-envelope"></span><br>
                  手机短信
                </div>
                <div :class="['imIm', {'active': nav===1}]" @click="changeNav(1)">
                  <span class="fa fa-user-circle-o"></span><br>
                  账号密码
                </div>
              </div>
            </div>
          </div>
          <div class="regArea" v-if="mainCVisble === 2" >
            <div class="small_nav">
              <div class="left_logo"><span class="fa fa-angle-left regBack" @click="regBackBtn"></span></div>
              <div class="title" v-if="regType !== 1">注册Wonderss账号</div>
              &nbsp;
              <span v-if="regType === 1" :class="regTypeBtnOk ? 'ty-btn-darkGreen' : 'ty-btn-gray'" class="ty-btn ty-btn-big darkGreen ty-circle-3 ty-right" @click="regTypeBtnOkFun">提交</span>
            </div>
            <div v-if="regType !== 1">
              <div style="padding:50px 0 50px 50px; ">
                <span class="pointer" @click="toggleRegVal(1)"><i class="fa" :class="regVal===1? 'fa-dot-circle-o':'fa-circle-o'"></i> 为公司注册，全公司的同事都将使用</span>
                <br>
                <span class="pointer" @click="toggleRegVal(2)"><i class="fa" :class="regVal===2? 'fa-dot-circle-o':'fa-circle-o'"></i> 为自己注册，仅个人使用</span>
              </div>
              <div class="el-form-item">
                <div class="login_btn" @click="regTypeOk">下一步</div>
              </div>
            </div>
            <div v-if="regType === 1" style="position: relative">
              <table class="frmTab">
                <tbody>
                <tr><td colspan="4">以下各项涉及到将来系统的状态，请按实际填写或选择！</td></tr>
                <tr><td colspan="4">1. 机构的基本信息</td></tr>
                <tr>
                  <td class="padL40">机构名称<i class="ty-color-red">*</i></td>
                  <!--<td colspan="3">经营地址<i class="ty-color-red">*</i></td>-->
                  <td class="padL40">联系人姓名<i class="ty-color-red">*</i></td>
                  <td class="padL40" colspan="3">联系电话<i class="ty-color-red">*</i>（请填写能拨通的手机号）
                    <span v-if="!regMobileCheck" class="ty-color-red" style="font-size: 14px;">请输入正确的手机号！</span>
                  </td>
                </tr>
                <tr>
                  <td class="padL40 padR20"><el-input v-model="regType1Data.name" placeholder="请录入" @keyup="setLenVal('name', 30)"></el-input></td>
                  <!--<td colspan="3"><el-input v-model="regType1Data.address" placeholder="请录入" ></el-input></td>-->
                  <td class="padL40 padR20"><el-input v-model="regType1Data.contactName" placeholder="请录入" @keyup="setLenVal('contactName', 15)"></el-input></td>
                  <td class="padL40" colspan="5" style="padding-right: 23px;"><el-input v-model="regType1Data.contactTel" @change="checkRegTel" placeholder="请录入" style="width: 351px;"></el-input></td>
                </tr>
                <tr>
                  <td class="padL40">经营地址<i class="ty-color-red">*</i></td>
                  <!--<td class="padL40">联系人姓名<i class="ty-color-red">*</i></td>
                  <td colspan="3">联系电话<i class="ty-color-red">*</i>（请填写能拨通的手机号）
                    <span v-if="!regMobileCheck" class="ty-color-red" style="font-size: 14px;">请输入正确的手机号！</span>
                  </td>-->
                </tr>
                <tr>
                  <td class="padL40" colspan="5" style="padding-right: 23px;">
                    <el-input v-model="regType1Data.address" placeholder="请录入"></el-input>
                  </td>
                  <!--<td class="padL40 padR20"><el-input v-model="regType1Data.contactName" placeholder="请录入" @keyup="setLenVal('contactName', 15)"></el-input></td>
                  <td colspan="3"><el-input v-model="regType1Data.contactTel" @change="checkRegTel" placeholder="请录入"></el-input></td>-->
                </tr>
                <tr>
                  <td class="padL40">注册地址</td>
                </tr>
                <tr>
                  <td class="padL40" colspan="5" style="padding-right: 23px;">
                    <el-input v-model="regType1Data.registaddress" placeholder="请录入"></el-input>
                  </td>
                </tr>
                <tr><td colspan="4">2. 总务、会计、销售与财务等四项功能是系统层面的基本功能，其中总务为自动开通，其他功能是否开通则需选择。请选择！<i class="ty-color-red">*</i></td></tr>
                <tr>
                  <td class="padL40"><span class="pointer" :class="regType1Data.opType.indexOf(1) >-1 ? 'txtGreen':''"  @click="toggleCheck(1)"><i class="fa" :class="regType1Data.opType.indexOf(1) >-1 ? 'fa-check-square' : 'fa-square-o' "></i> 无需开通其他功能</span></td>
                  <td><span class="pointer" :class="regType1Data.opType.indexOf(2) >-1 ? 'txtGreen':''" @click="toggleCheck(2)"><i class="fa" :class="regType1Data.opType.indexOf(2) >-1 ? 'fa-check-square' : 'fa-square-o' "></i> 销售功能</span></td>
                  <td><span class="pointer" :class="regType1Data.opType.indexOf(3) >-1 ? 'txtGreen':''"  @click="toggleCheck(3)"><i class="fa" :class="regType1Data.opType.indexOf(3) >-1 ? 'fa-check-square' : 'fa-square-o' "></i> 会计功能</span></td>
                  <td><span class="pointer" :class="regType1Data.opType.indexOf(4) >-1 ? 'txtGreen':''"  @click="toggleCheck(4)"><i class="fa" :class="regType1Data.opType.indexOf(4) >-1 ? 'fa-check-square' : 'fa-square-o' "></i> 财务功能</span></td>
                </tr>
                <tr>
                  <td colspan="4">3. 机构向其客户销售什么？<i class="ty-color-red">*</i></td>
                </tr>
                <tr>
                  <td class="padL40"><span class="pointer" :class="regType1Data.gsType === 1 ? 'txtGreen':''" @click="toggleRadio(1, 'gsType')"><i class="fa" :class="regType1Data.gsType === 1? 'fa-dot-circle-o' : 'fa-circle-o'"></i> 只提供服务，不销售商品</span></td>
                  <td><span class="pointer" :class="regType1Data.gsType === 2 ? 'txtGreen':''" @click="toggleRadio(2, 'gsType')"><i class="fa" :class="regType1Data.gsType === 2? 'fa-dot-circle-o' : 'fa-circle-o'"></i> 销售商品，服务不另行收费</span></td>
                  <td colspan="2"><span class="pointer" :class="regType1Data.gsType === 3 ? 'txtGreen':''" @click="toggleRadio(3, 'gsType')"><i class="fa" :class="regType1Data.gsType === 3? 'fa-dot-circle-o' : 'fa-circle-o'"></i> 除销售商品外，还经营另行收费的服务项目</span></td>
                </tr>
                <tr>
                  <td colspan="4">4. 机构商品的情况<i class="ty-color-red">*</i></td>
                </tr>
                <tr>
                  <td class="padL40"><span class="pointer" :class="regType1Data.gsStatus === 1 ? 'txtGreen':''" @click="toggleRadio(1, 'gsStatus')"><i class="fa" :class="regType1Data.gsStatus === 1? 'fa-dot-circle-o' : 'fa-circle-o'"></i> 有自己生产的产品</span></td>
                  <td><span class="pointer" :class="regType1Data.gsStatus === 2 ? 'txtGreen':''" @click="toggleRadio(2, 'gsStatus')"><i class="fa" :class="regType1Data.gsStatus === 2? 'fa-dot-circle-o' : 'fa-circle-o'"></i> 均系采购来且均不换包装</span></td>
                  <td colspan="2"><span class="pointer" :class="regType1Data.gsStatus === 3 ? 'txtGreen':''" @click="toggleRadio(3, 'gsStatus')"><i class="fa" :class="regType1Data.gsStatus === 3? 'fa-dot-circle-o' : 'fa-circle-o'"></i> 均系采购来，但有的会换包装</span></td>
                </tr>
                <tr>
                  <td colspan="4">5. Wonderss的仓库有智能模式与非智能模式。贵机构的仓库将采用哪种模式？<i class="ty-color-red">*</i></td>
                </tr>
                <tr>
                  <td class="padL40"><span class="pointer" :class="regType1Data.ckType === 1 ? 'txtGreen':''"  @click="toggleRadio(1,'ckType')"><i class="fa" :class="regType1Data.ckType === 1? 'fa-dot-circle-o' : 'fa-circle-o'"></i> 智能仓库模式</span></td>
                  <td><span class="pointer" :class="regType1Data.ckType === 2 ? 'txtGreen':''" @click="toggleRadio(2,'ckType')"><i class="fa" :class="regType1Data.ckType === 2? 'fa-dot-circle-o' : 'fa-circle-o'"></i> 非智能仓库模式</span></td>
                  <td colspan="2"><span class="pointer" :class="regType1Data.ckType === 3 ? 'txtGreen':''" @click="toggleRadio(3,'ckType')"><i class="fa" :class="regType1Data.ckType === 3? 'fa-dot-circle-o' : 'fa-circle-o'"></i> 本机构不使用仓库功能</span></td>
                </tr><tr><td colspan="4">&nbsp;</td></tr>
                </tbody>
              </table>
              <div class="mask1" v-if="(regType1Data.opType.indexOf(2) === -1)"></div>
              <div class="mask2" v-if="!(regType1Data.gsType === 2 || regType1Data.gsType === 3)"></div>
            </div>
          </div>
          <div class="codeNext" v-if="mainCVisble === 3"  style=" font-size: 16px  ">
            <div style="color: #ed5565; font-size: 14px; line-height:30px;">
              <p v-if="codeNextErr == 1">两次输入的密码不一致，请重新设置！</p>
              <p v-if="codeNextErr == 2">您设置的密码过于简单，请重新设置！</p>
            </div>
            <div style="margin-bottom: 40px; font-size: 14px; line-height:22px;  ">
              <p class="tipstr">提示！！</p>
              <div class="login_btn tipbtn" @click="goLogin">暂不设置，直接登录</div>
              <div style="clear:both;margin-bottom:20px; "></div>
              <p style="color: #5d9cec;">如需设置/重新设置密码，请在下方设置！
              </p>
            </div>

            <div>
              <el-form class="form formTr" label-position="left" label-width="80px" :model="codeNextFrm">
                <el-form-item label="密码" class="el-form-item_border_bottom">
                  <el-input type="password" v-model="codeNextFrm.pass" placeholder="请输入密码" :clearable="true" autocomplete="new-password" show-password></el-input>
                </el-form-item>
                <el-form-item label="确认密码" class="el-form-item_border_bottom">
                  <el-input type="password" v-model="codeNextFrm.pass2" placeholder="请重新输入密码" :clearable="true" autocomplete="new-password" show-password></el-input>
                </el-form-item>
                <div style="color: #5d9cec; font-size: 14px; ">
                  <small>注：密码需为8-16位，必须包括数字和英文字母，英文字母分大小写</small>
                </div>

                <div class="subC el-form-item" style="margin-top: 16px">
                  <div class="login_btn" @click="codeNextBtn">设置完毕，登录系统</div>
                </div>
              </el-form>
            </div>
          </div>
        </div>
      </div>
    </div>

    <TyDialog class="custormerLeading" v-if="regType1Data.tipVisible" width="550" dialogTitle="提示" color="blue" :dialogHide="hideTipFun">
      <template #dialogBody>
        <div style="text-align: center; font-size:15px; padding: 20px 0; line-height:30px  ">
          提交成功！<br>
          客户经理将于{{ regType1Data.tipTime }}前与您联系，请耐心等待！
        </div>
      </template>
    </TyDialog>
  </div>
  <div id='serverTail' class='hidden' :data='serverTail'></div>
</template>
<style lang="scss">
#start {
  background-color: #1c2232 ;
  background-image: url('../assets/img/bg.png') ;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
  overflow: hidden;
  height: 100%;
  .regArea{
    line-height: 40px; position: relative;
    .pointer{ cursor: pointer; }
    .fa{ color:$ty-color-green;  }
  }
  .main_avatar_min{
    width: 1310px;
    margin:140px auto;
    display: flex;
    .padL40{ padding-left: 20px }
    .padR20{ padding-right: 20px }
    .txtGreen{ color: $ty-color-green; }
    .logo_avatar{
      padding-top: 48px;
      width: 340px;
      flex: none;
      .logo{
        color: #fff;
        font-size: 55px;
        line-height:55px;
        margin: 0;
        padding: 0;
        .logo_w{
          color: $ty-color-green;
        }
      }
      .logo_welcome{
        color: #9aa4a3;
        font-size: 26px;
        line-height: 26px;
      }
    }
    .login_login_avatar{
      flex: auto;
      .login_avatar{
        width: 880px;
        margin: 0 auto;
        background-color: #fbfafa;
        border-radius: 2px;
        padding: 20px 40px;
        box-shadow: 0 0 6px #242626;
        border: 2px solid #fff;
        .rLink{
          float: right;
        }
        .form{
          margin-top: 36px;
          .el-form-item_border_bottom{
            border-bottom: 1px solid #eee;
          }
          .el-input,.el-input__wrapper{
            border: none!important;
            box-shadow:none;
            background: inherit;
          }
          .el-input-group__append{
            border: none;
            box-shadow: none;
            background: inherit;
            border-radius: 5px;
            padding: 0;
            .codeBtn {
              padding: 0 14px;
              background-color: $ty-color-green;
              color: #fff;
              display: inline-block;
              line-height: 36px;
              text-align: center;
              border-radius: 3px;
              height: 36px;
              cursor: pointer;
              font-size: 13px;
              border: none;
              &.disabled{
                background-color: $ty-color-gray;
                color: #666;
                cursor: default;
              }
            }
          }

        }
        .createShortCut{
          font-size: 14px;
          color: #666;
        }
        .logMethod{
          margin-top: 36px;
          .logMethod_title{
            display: flex;
            align-items: center;
            .ttlTxt{
              color: #666;
              margin: 0 16px;
            }
            .line{
              height: 1px;
              background-color: #eee;
              flex: auto;
            }
          }
          .icon_row{
            margin-top: 16px;
            display: flex;
            justify-content: space-between;
            .imIm{
              width: 70px;
              text-align: center;
              font-size: 13px;
              line-height: 2;
              .fa{
                font-size: 22px;
                color: #ccc;
              }
              &.active .fa-weixin{
                color: #04c15f;
              }
              &.active .fa-envelope{
                color: $ty-color-orange
              }
              &.active .fa-user-circle-o{
                color: $tyBounce-color-blue
              }
            }
          }
        }

        .small_nav{
          display: flex;
          height: 35px;
          align-items: center;
          .left_logo{
            width: 20px;
            position: absolute;
            .regBack{
              font-size: 28px;
              color: #888;
            }
          }
          .title{
            flex: auto;
            text-align: center;
          }
        }
      }
    }
    .login_btn{
      width: 100%;
      background: $ty-color-green;
      border-radius: 3px;
      text-align: center;
      padding: 12px 0;
      color: #fff;
      &:hover{
        background: $ty-color-green;
      }
    }
    .frmTab{
      font-size: 15px; line-height:30px ;
    }
    .darkGreen{
      position: absolute;
      right: 20px;
    }
    .mask1, .mask2{ position: absolute;  width:100%; background-color: rgba(100,100,100, 0.2); left: -26px; }
    .mask1{ height: 66px; top: 328px;  }
    .mask2{ height: 64px;  top: 398px; }
  }
  .main_avatar{
    width: 960px;
    margin: 150px auto;
    display: flex;

    .logo_avatar{
      padding-top: 48px;
      width: 450px;
      flex: none;
      .logo{
        color: #fff;
        font-size: 90px;
        line-height: 90px;
        margin: 0;
        padding: 0;
        .logo_w{
          color: $ty-color-green;
        }
      }
      .logo_welcome{
        color: #9aa4a3;
        font-size: 36px;
        line-height: 36px;
      }
    }
    .login_login_avatar{
      flex: auto;
      .login_avatar{
        width: 380px;
        margin: 0 auto;
        background-color: #fbfafa;
        border-radius: 2px;
        padding: 20px 40px;
        box-shadow: 0 0 6px #242626;
        border: 2px solid #fff;
        .rLink{
          float: right;
        }
        .form{
          margin-top: 36px;
          .el-form-item_border_bottom{
            border-bottom: 1px solid #eee;
          }
          .el-input,.el-input__wrapper{
            border: none!important;
            box-shadow:none;
            background: inherit;
          }
          .el-input-group__append{
            border: none;
            box-shadow: none;
            background: inherit;
            border-radius: 5px;
            padding: 0;
            .codeBtn {
              padding: 0 14px;
              background-color: $ty-color-green;
              color: #fff;
              display: inline-block;
              line-height: 36px;
              text-align: center;
              border-radius: 3px;
              height: 36px;
              cursor: pointer;
              font-size: 13px;
              border: none;
              &.disabled{
                background-color: $ty-color-gray;
                color: #666;
                cursor: default;
              }
            }
          }

        }
        .createShortCut{
          font-size: 14px;
          color: #666;
        }
        .logMethod{
          margin-top: 36px;
          .logMethod_title{
            display: flex;
            align-items: center;
            .ttlTxt{
              color: #666;
              margin: 0 16px;
            }
            .line{
              height: 1px;
              background-color: #eee;
              flex: auto;
            }
          }
          .icon_row{
            margin-top: 16px;
            display: flex;
            justify-content: space-between;
            .imIm{
              width: 70px;
              text-align: center;
              font-size: 13px;
              line-height: 2;
              .fa{
                font-size: 22px;
                color: #ccc;
              }
              &.active .fa-weixin{
                color: #04c15f;
              }
              &.active .fa-envelope{
                color: $ty-color-orange
              }
              &.active .fa-user-circle-o{
                color: $tyBounce-color-blue
              }
            }
          }
        }

        .small_nav{
          display: flex;
          height: 35px;
          align-items: center;
          .left_logo{
            width: 20px;
            position: absolute;
            .regBack{
              font-size: 28px;
              color: #888;
            }
          }
          .title{
            flex: auto;
            text-align: center;
          }
        }
      }
    }
    .login_btn{
      width: 100%;
      background: $ty-color-green;
      border-radius: 3px;
      text-align: center;
      padding: 12px 0;
      color: #fff;
      &:hover{
        background: $ty-color-green;
      }
    }
  }
  .tipstr{ color: #ed5565; font-size:18px; position: relative; top: 14px;  }
  .tipbtn{ float: right; width: 160px!important; line-height:10px; position: relative; top: -15px;  }

  .line20{ line-height: 25px; font-size: 15px; color: #ed5565;     }
}
@media screen and (min-width: 1921px) {
  #start{
    display: flex;
    align-items: center;
  }
}
</style>
<script lang="js">
import { inject } from 'vue';
import { saveAs } from 'file-saver'
import { login, sendMobileCode, verificationCode, codeLogin, mpCodeLogin,
  submit_changePassword_start, otherMobileVerificationCode, organizationList,
  unlockGetNewAccId, selectNewAcc,selectOldAcc,unlockAcc, checkOtherMobileVerificationCode,
  logout, otherChangePassword, registeredOrgApply, getServerTail } from "@/api/api";
import {clearStorage} from "@/utils/clearStorage";
import wxlogin from 'vue-next-wxlogin'
import auth from '@/sys/auth'
import '@/utils/GVerify.js'
import { ElMessage } from 'element-plus'
import qs from 'qs';
import { isFirefox, isWindows, windowsNtVersion } from '@/utils/browerUtils'
import { testMobile } from '@/utils/formTest'
import moment from 'moment'
import sphdSocket from '@/sys/sphd'

var verifyCode
export default {
  name: "Start",
  data() {
    return {
      passportHost: '',
      app: {
        tgCode: '',
        appId: '',
      },
      redirectUriStr:'',
      state:'',
      href:'',
      showShortcut: false,
      isWindowsFirefox: false,
      shortcutTips: '',
      passWdloginClickNum:0,
      GVerifyTimer:-1,
      wxIframeSrc : '',
      qrCodeVisble : false,
      regVal : '',
      mainCVisble : 1,
      codeNextErr : 0,
      hrefStr : '',
      rootPath : '',
      heightClent: 0,
      nav: 1,
      zhuShow: true,
      loginErr: true,
      loginErrDay: 75,
      loginErr3: 2,
      loginErr3Day: 0,
      loginErr3Radio: '',
      loginErr3Ling: '',
      labelPosition: "right",
      codeNextFData:{},
      codeNextFrm:{
        pass:'',
        pass2:'',
      },
      formLabelAlign: {
        name: "",
        pass: "",
        // name: "18622650001",
        // pass: "650001",
        code: "",
        codeVerify: "",
        jiAcc: "",
        jiPas: "",
      },
      sendCodeName: '获取验证码',
      sendCodeDisabled: false,
      regType : 0 , // 注册Wonderss账号 的类型
      regType1Data:{
        name:'',
        address:'',
        registaddress: '',
        contactName:'',
        contactTel:'',
        opType:[],
        gsType:'',
        gsStatus:'',
        ckType:'',
        tipVisible: false,
        tipTime:'',
      },
      regMobileCheck:false,
      serverTail: ''
    };
  },
  components: {
    wxlogin
  },
  mounted() {
    const globalEnum = inject("globalConstEnum")
    this.passportHost = globalEnum.getDescByKey('Passport', 'host')
    this.app = globalEnum.getDescByKey('WonderssTpApps', 'WxPcLogin')
    let urlParams = location.search.lastIndexOf('?') >= 0 ? location.search : location.hash
    let params = qs.parse(urlParams.substring(urlParams.lastIndexOf('?')), { ignoreQueryPrefix: true })
    console.log('qs', params, params.code, params.state, params.state == auth.getToken())
    //显示创建快捷方式
    if(this.showShortcut = params.from!='shortcut') {
      this.showShortcutTip()
    }
    if(typeof params.logout === 'undefined' && auth.isLogged()){
      this.$router.push('desktop')
    } else if(typeof params.code !== 'undefined' && typeof params.state !== 'undefined' && !auth.isLogged() && params.state == auth.getToken()) {
      this.mpCodeLoginFun(params)
    } else {
      clearStorage()
      auth.getGuestToken()
      setTimeout(()=>{
        getServerTail().then(res=>this.serverTail=res.data.data)
      },1)
    }
    let height = Math.max(document.documentElement.clientHeight, document.body.clientHeight);
    // console.log(height);
    this.heightClent = height;
  },
  watch: {
    passWdloginClickNum(newVal, oldVal) {
      console.log(newVal, oldVal)
      let that = this
      if(newVal>9){
        this.GVerifyTimer = setInterval(function () {
          that.setGVerify();
        }, 200)
      }
    },
  },
  computed:{
    regTypeBtnOk(){
      let status = false
      if(this.regType1Data.name && this.regType1Data.address && this.regType1Data.contactName && this.regType1Data.contactTel ){ // 第一题
        if(this.regType1Data.ckType > 0){ // 第五题
          if(this.regType1Data.opType.length > 0){ // 第二题
            if(this.regType1Data.opType.indexOf(2) > -1){ // 有销售
              if(this.regType1Data.gsType > 0){ // 第三题
                if(this.regType1Data.gsType === 1){
                  status = true
                }else{
                  if(this.regType1Data.gsStatus){ // 第四题
                    status = true
                  }
                }
              }
            }
            else{ // 没有销售
              status = true
            }
          }
        }
      }
      return status
    },
  },
  methods: {
    setLenVal(key,len){
      if(this.regType1Data[key].length > len){
        this.regType1Data[key] = this.regType1Data[key].substr(0,len)
      }

    },
    hideTipFun(){
      this.regType1Data.tipVisible = false
    },
    checkRegTel(){
      if(this.regType1Data.contactTel){
        this.regMobileCheck = testMobile(this.regType1Data.contactTel)
      }else {
        this.regMobileCheck = true
      }
    },
    regTypeBtnOkFun(){
      debugger;
      if(this.regTypeBtnOk){
        let params = {
          fullName: this.regType1Data.name,
          address: this.regType1Data.address,
          contactName: this.regType1Data.contactName,
          contactPhone: this.regType1Data.contactTel,
        };
        if(this.regType1Data.registaddress){//如果注册地址有值就传值
          params.registeredAddress = this.regType1Data.registaddress;
        }
        let list = []
        this.regType1Data.opType.forEach(potI=>{
          switch (potI) {
            case 1 : list.push('无需开通其他功能'); break;
            case 2 : list.push('销售功能'); break;
            case 3 : list.push('会计功能'); break;
            case 4 : list.push('财务功能'); break;
            default:
              console.log('opType=', this.regType1Data.opType)
          }
        })
        params.functionOpt = JSON.stringify(list)
        switch (this.regType1Data.gsType ) {
          case 1 : params.saleOpt = '只提供服务，不销售商品' ; break;
          case 2 : params.saleOpt = '销售商品，服务不另行收费' ; break;
          case 3 : params.saleOpt = '除销售商品外，还经营另行收费的服务项目' ; break;
          default:
            console.log('saleOpt=', this.regType1Data.saleOpt)
        }
        switch (this.regType1Data.gsStatus ) {
          case 1 : params.commodityOpt = '有自己生产的产品' ; break;
          case 2 : params.commodityOpt = '均系采购来且均不换包装' ; break;
          case 3 : params.commodityOpt = '均系采购来，但有的会换包装' ; break;
          default:
            console.log('commodityOpt=', this.regType1Data.commodityOpt)
        }
        switch (this.regType1Data.ckType ) {
          case 1 : params.warehouseOpt = '智能仓库模式' ; break;
          case 2 : params.warehouseOpt = '非智能仓库模式' ; break;
          case 3 : params.warehouseOpt = '本机构不使用仓库功能' ; break;
          default:
            console.log('warehouseOpt=', this.regType1Data.warehouseOpt)
        }
        console.log('params=', params)

        registeredOrgApply(params).then(res1=>{
          let res = res1.data
          let time = res.data
          if(res.success === 1){
            this.regType1Data.tipVisible = true
            this.regType1Data.tipTime = new Date(time).format('yyyy年MM月dd日')
            let that = this
            setTimeout(function () {
              that.regType1Data.tipVisible = false
            }, 5000)
            this.regBackBtn()
          }else {
            this.$message.error(res.errorMsg)
          }

        }).catch(err=>{
          console.log('err=', err)
        })

      }
    },
    toggleCheck(num){
      let index = this.regType1Data.opType.indexOf(num)
      if(index > -1){
        this.regType1Data.opType.splice(index,1)
        if(this.regType1Data.opType.indexOf(2) === -1){
          this.regType1Data.gsType = 0
          this.regType1Data.gsStatus = 0
        }
      }else{
        if(num === 1){
          this.regType1Data.opType = [1]
          this.regType1Data.gsType = ''
          this.regType1Data.gsStatus = ''
        }else{
          let index1 = this.regType1Data.opType.indexOf(1)
          if(index1 > -1){
            this.regType1Data.opType.splice(index1, 1)
          }
          this.regType1Data.opType.push(num)
        }
      }

    },
    toggleRadio(num, type){
      if(this.regType1Data[type] === num){
        this.regType1Data[type] = ''
      }else{
        this.regType1Data[type] = num
      }
      if(type === 'gsType' && num === 1){
        this.regType1Data.gsStatus = 0
      }
    },
    regTypeOk(){
      this.regType = this.regVal
      this.regMobileCheck = true
      this.regType1Data={
        name:'',
        address:'',
        contactName:'',
        contactTel:'',
        opType:[],
        gsType:'',
        gsStatus:'',
        ckType:'',
      }
    },
    toggleRegVal(val){
      if(this.regVal === val){
        this.regVal = ''
      }else{
        this.regVal = val
      }
    },
    codeNextBtn(){
      if (this.codeNextFrm.pass !== this.codeNextFrm.pass2) {
        this.codeNextErr = 1
      } else {
        if (this.isPassword(this.codeNextFrm.pass)) {
          this.codeNextErr = 0
          let data = {
            newPassword: this.codeNextFrm.pass2,
          }
          if(this.codeNextFrm.type === 1){ // 正常账号密码登录的 情况 修改密码
            submit_changePassword_start(data).then(res => {
              console.log('res=', res)
              let success = res.data.success
              if (success === 1) {
                ElMessage.success('密码修改成功！')
                this.codeLoginFun()
              } else {
                ElMessage.error("设置密码失败,请重试！")
              }
            }).catch(err => {
              debugger
              ElMessage.error("操作失败！")
              alert('错误啦')
            })
          }else{ // 激活之后， 新手机号设置密码
            otherChangePassword(data).then(res => {
              console.log('res=', res)
              let success = res.data.success
              if (success === 1) {
                ElMessage.success('密码修改成功！')
                this.codeLoginFun()
              } else {
                ElMessage.error("设置密码失败,请重试！")
              }
            }).catch(err => {
              debugger
              ElMessage.error("操作失败！")
              alert('错误啦')
            })
          }

        }
        else{
          this.codeNextErr = 2
        }
      }
    },
    isPassword(password) {
      return /^(?=.*[a-zA-Z])(?=.*\d)[^]{8,16}$/.test(password);
    },
    showShortcutTip() {
      this.isWindowsFirefox = isFirefox() && isWindows()
      if(this.isWindowsFirefox) {
        // this.shortcutTips = "点击“创建”后，将文件保存或复制到桌面，删掉火狐浏览器自动添加的文件后缀“.donwload”，下次双击Wonderss即可进入通用框架！\n" +
        //     "避免后缀问题，建议点击“创建批处理”，将文件保存或复制到桌面，下次双击Wonderss也可进入通用框架！"
        this.shortcutTips = '点击“创建”后，将文件保存或复制到桌面，下次双击Wonderss即可进入通用框架！'
        if (windowsNtVersion() >= 10) {
          this.shortcutTips += "\n(右键选择文件菜单 属性->常规->解除锁定 可去掉文件执行安全提示)"
        }
      } else if (isFirefox()) {
        this.shortcutTips = '点击“创建”后，将文件保存或复制到桌面，删掉火狐浏览器自动添加的文件后缀“.donwload”，下次双击Wonderss即可进入通用框架！'
      } else {
        this.shortcutTips = '点击“创建”后，将文件保存或复制到桌面，下次双击Wonderss即可进入通用框架！'
      }
    },
    getWebRootShort() {
      return auth.getWebRootShort() ?? auth.webRoot + '/vue/minersFrontEnd/dist/index.html#'
    },
    download() {
      let url = this.getWebRootShort() + '/?from=shortcut'
      saveAs(new Blob(["[InternetShortcut]\n" +
          "URL=" + url +
          "\nIDList=\n" +
          "HotKey=0\n" +
          "IconFile=" + auth.webRoot + "/vue/minersFrontEnd/dist/favicon.ico\n" +
          "IconIndex=0"], {type: "text/x-uri;charset=utf-8"}),
          "Wonderss.url");
      // }
    },
    downloadBat() {
      let url = this.getWebRootShort() + '/?from=shortcut'
      saveAs(new Blob(["@echo off\n" +
      "set source_icon=\"" + auth.webRoot + "/vue/minersFrontEnd/dist/favicon.ico\"\n" +
      "set file_path=\"%ProgramFiles%\\Mozilla Firefox\\firefox.exe\"\n" +
      "if not exist %file_path% (\n" +
      "set file_path=\"%ProgramFiles(x86)%\\Mozilla Firefox\\firefox.exe\"\n" +
      ")\n"+
      "if not exist %file_path% (\n" +
      "for /f delims^=^\"^ tokens^=2 %%a in ('reg query \"HKLM\\Software\\Clients\\StartMenuInternet\" /f *firefox.exe /d /s') do (\n" +
      "  set file_path=\"%%~a\"\n" +
      ")\n"+
      ")\n" +
      "%file_path% -new-tab " + url], {type: "text/plain;charset=utf-8"}), "Wonderss.bat")
    },
    setGVerify(){
      var checkCodeObj = document.getElementById('checkCode');
      if(checkCodeObj){
        verifyCode = new GVerify({
          id: 'checkCode',
          width: '90',
          height: '30'
        });
        clearInterval(this.GVerifyTimer )
      }
    },
    mpCodeLoginFun (params) {
      let data = {
        appId: this.app.appId,
        tgCode: this.app.tgCode,
        code: params.code
      }
      mpCodeLogin(data).then(res => {
        this.$route.query = {};
        let data = res.data
        console.log(data)
        if(data.success > 0) {
          console.log('登录成功！')
          this.$router.push('orgList')
          // let url = auth.webRoot + '/vue/minersFrontEnd/dist/index.html#/orgList'
          // location.href = url
          console.log('微信登录跳转2', moment(this.startTime).format(), moment().format(),  moment(this.startTime).fromNow())
        }
        else {
          this.nav = 3
          let error = data.error
          console.log('登录失败！error msg', error)
          // console.log('startPage check token', auth.getByName('tpMemberId', auth.getToken()), auth.getUserID())
          switch (error.code) {
            case '2': //超过75天，需要短信验证激活
              this.loginErr3 = 2
              this.zhuShow = true
              this.loginErr3Day = 75
              this.formLabelAlign.name = data.data.mobile
              break
            case '6': //超过89天，需要短信验证激活
              this.loginErr3 = 2
              this.zhuShow = true
              this.loginErr3Day = 89
              let mobile = data.data.mobile
              this.formLabelAlign.name = mobile
              break
            case '3': //需要注册，微信号未与通用框架账号绑定
              // console.log('微信号未与通用框架账号绑定')
              this.nav = 1
              this.$message.error('操作失败，该微信未绑定账号！')
              this.reFrash()
              break
            case '4': //可能是授权或者code问题
              this.nav = 1
              this.$message.error('授权问题，建议更换其他登录方式！')
              this.reFrash()
              break
            case '5': //服务器问题，比如wonderss服务器访问微信/字节服务器异常
              console.log('服务器问题，比如wonderss服务器访问微信服务器异常，建议更换其他登录方式')
              this.nav = 1
              this.$message.error('服务器问题，比如wonderss服务器访问微信服务器异常，建议更换其他登录方式')
              this.reFrash()
              break
          }
        }
      }).catch(err => {
        console.log('err=', err)
      })

    },
    reFrash(){
      setTimeout(function(){
        let url = location.href
        let u = url.split('?')[0]
        location.href = u
      },3000)
    },
    redirectPage(){
      console.log('redirectUriStr=', this.redirectUriStr)
      // location.href = this.redirectUriStr
    },
    regBackBtn(){
      this.mainCVisble = 1
      this.regVal = ''
      this.regType = 0
    },
    regAccBtn(){
      this.mainCVisble = 2
    },
    changeNav(navIndex){
      this.nav = navIndex
      if(navIndex === 1){
        this.zhuShow = true
        this.loginErr = true
      }
      else if(navIndex === 2){

      }
      else if(navIndex === 3){
        localStorage.setItem('wxtel', this.formLabelAlign.name)
        this.loginErr3 = 1
        // this.loginErr3 = 2
        this.loginErr3Radio = 0
        if (location.href.indexOf('/vue/') > 0) {
          this.redirectUriStr = auth.webRoot.substring( 0, auth.webRoot.indexOf('://') + 3) + this.passportHost + '/' + (auth.getWebRootShort() ?? auth.getDomain(location.href) + '/vue/minersFrontEnd/dist/index.html#' ) + '/'
        } else {
          this.redirectUriStr = location.protocol + '//' + this.passportHost + '/' + auth.getDomain(location.href) + '/'

        }
        this.state = auth.getToken()
        console.log('redirectUriStr=', this.redirectUriStr)
      }
    },
    loginBtn: function () {
      if(this.passWdloginClickNum > 9){
        let res = verifyCode.validate(this.codeVerify??'');
        if(!res){
          this.$message.error('字符输入不正确，请重新输入！')
        }else{
          this.loginFun()
        }
      }else {
        this.loginFun()
      }
    },
    loginErr3LoginBtn(){
      if(this.loginErr3Radio == 1){
        //原来手机号
      }
      else if(this.loginErr3Radio == 2){
        this.formLabelAlign.name = ''
      }else{
        this.$message.error('请先选择')
        return false
      }
      this.formLabelAlign.code = ''
      this.loginErr3 = 3
    },
    sendCode2(){
      if(this.loginErr3Radio == 1){
        //原来手机号
        this.sendCode()
      }
      else if(this.loginErr3Radio == 2){
        // 采用其他手机号作为账号
        let data = { phone: this.formLabelAlign.name }
        let sendCodeDisabled = this.sendCodeDisabled
        if(data.phone.length === 11 && !sendCodeDisabled){
          console.log('phone type=', typeof data.phone)
          otherMobileVerificationCode(data).then(res => {
            let success = res.data.success
            let errorCode = res.data.errorCode
            if (success === 1) {
              ElMessage.success('验证码已发送！')
              this.countdown(60)
            } else {
              if (errorCode === '5') {
                ElMessage.error('请勿重复获取验证码！')
              } else if (errorCode === '2' || errorCode === '3') {
                ElMessage.error('请输入正确的手机号！')
              }
            }
          }).catch(err => {
            console.log('sendMobileCode err=', err)
            debugger
            ElMessage.error('操作失败！')
            alert('错误啦')
          })
        }else{
          ElMessage.error('手机号码不正确！')
        }
      }
    },
    codeLoginBtn2(){
      if(this.loginErr3Radio == 1){
        //原来手机号
        this.codeLoginBtn()
      }
      else if(this.loginErr3Radio == 2){
        // 采用其他手机号作为账号
        let data = {
          phone: this.formLabelAlign.name,
          code: this.formLabelAlign.code,
        }
        if(data.phone.length === 11 && data.code.length === 4  ){
          checkOtherMobileVerificationCode(data).then(res => {
            console.log('checkOtherMobileVerificationCode res =', res)
            let success = res.data.success
            if (success === 1) {
              ElMessage.success('验证通过！')
              this.codeNextF()

            } else {
              let errorCode = res.data.errorCode
              let errorMsg = res.data.errorMsg
              ElMessage.error(errorMsg)
            }
          }).catch(err => {
            console.log('checkOtherMobileVerificationCode err =', err)
            ElMessage.error('您输入的验证码有误！')
          })
        }else{
          ElMessage.error('请录入正确的数据')
        }

      }

    },
    loginFun(){
      let logonName = this.formLabelAlign.name
      let logonPwd = this.formLabelAlign.pass
      login(logonName, logonPwd).then(res => {
        let loginData = res.data
        if (loginData.error != undefined) {
          if(loginData.error === "需要短信验证!"){
            this.loginErr = false
            this.loginErrDay = 75
            this.zhuShow = true

          }else if(loginData.error === "账号被锁定!"){
            this.loginErr = false
            this.loginErrDay = 89
            this.zhuShow = true
          }else{
            this.passWdloginClickNum++;
            this.$alert(loginData.error, '提示', { confirmButtonText: '确定' })
          }
        }
        else if (auth.isLogInOrg()) {//进入默认或者唯一机构
          this.$router.push('desktop')
        } else if (auth.isLogged()){
          this.$router.push('orgList')
        } else {
          this.$alert('登录失败，请联系管理员！', '提示', { confirmButtonText: '确定' })
        }
      }).catch(err => {
        console.log('err=', err)
        this.$alert('登录失败，请重试！', '提示', { confirmButtonText: '确定' })
      })
    },
    sendCode: function () {
      console.log('sendCode 开始')
      let data = {
        phone: this.formLabelAlign.name
      }
      let sendCodeDisabled = this.sendCodeDisabled
      if(data.phone.length === 11 && !sendCodeDisabled){
        console.log('phone type=', typeof data.phone)
        sendMobileCode(data).then(res => {
          console.log('sendMobileCode res=',res)
          let success = res.data.success
          let errorCode = res.data.errorCode
          if (success === 1) {
            ElMessage.success('验证码已发送！')
            this.countdown(60)
          } else {
            if (errorCode === '5') {
              ElMessage.error('请勿重复获取验证码！')
            } else if (errorCode === '2' || errorCode === '3') {
              ElMessage.error('请输入正确的手机号！')
            }
          }
        }).catch(err => {
          console.log('sendMobileCode err=', err)
          debugger
          ElMessage.error('操作失败！')
          alert('错误啦')
        })
      }else{
        ElMessage.error('手机号码不正确！')
      }
    },
    countdown (seconds) {
      let that = this
      if (seconds > 1){
        seconds--;
        this.sendCodeName = seconds + '秒后可重新获取'
        this.sendCodeDisabled = true
        // 定时1秒调用一次
        setTimeout(function(){
          that.countdown(seconds)
        },1000)
      } else {
        this.sendCodeName = '获取验证码'
        this.sendCodeDisabled = false
      }
    },
    codeLoginBtn:function (num) {
      let data = {
        mobile: this.formLabelAlign.name,
        code: this.formLabelAlign.code,
      }
      if(data.mobile.length === 11 && data.code.length === 4  ){
        verificationCode(data).then(res => {
          console.log('verificationCodeStart res =', res)
          let data = res.data.data
          if (data) {
            if(data == "等待激活！"){
              // 两种情况， 1.微信登录的需要选择 领地;2.短信登录的直接跳 StartActive 安全验证
              if(this.nav === 3){ // wx
                unlockGetNewAccId().then(res1=>{
                  let res = res1.data
                  let success = res.success
                  if(success === 1){
                    this.loginErr3 = 5
                  }else{
                    unlockAcc().then(res1=>{
                      let res = res1.data
                      let success = res.success
                      if(success === 1){
                        this.$message.success('认证通过,账号已激活！')
                        this.$router.push('orgList')
                      }else{
                        this.$message.error('认证通过,账号激活失败！')
                      }
                    }).catch(err=>{
                      console.log('激活失败 err=', err)
                    })
                  }

                }).catch(err=>{
                  console.log('err=', err)
                })
              }else{
                localStorage.setItem('activePhone', this.formLabelAlign.name)
                this.$router.push('StartActive')
              }

            }
            else {
              if(num === 1){ // 正常 手机号验证码登录， 需要进 修改密码页
                this.codeNextF(1)
              }else{ // 激活的
                // 先判断一下有没有机构， 只有领地没有机构：需要返回登录页，给 手机端登录提示； 有机构的 进行登录流程
                organizationList({ currentPageNo: 1, pageSize: 10 }).then(res1=>{
                  let res = res1.data
                  let orgList = res.data || []
                  if(orgList.length === 0){ // 只有领地，PC目前登录不了
                    let str = '很抱歉，目前您的数据不能通过电脑端访问，推荐您使用手机端！'
                    this.$alert(str, '提示', {
                      confirmButtonText: '确定',
                      callback: (action) => {
                        clearStorage()
                        //获取游客token
                        auth.getGuestToken()
                        sphdSocket.reflushAuth()
                        this.nav = 1
                        this.formLabelAlign.name = ''
                        this.formLabelAlign.pass = ''
                        this.formLabelAlign.code = ''

                      },
                    })
                  }else{ // 有机构，可以走登录
                    this.$router.push('orgList')
                  }

                }).catch(err=>{
                  console.log('err=', err)
                })
              }


            }
          } else {
            ElMessage.error('您输入的验证码有误！')
          }
        }).catch(err => {
          console.log('codeLogin err =', err)
          ElMessage.error('您输入的验证码有误！')
        })
      }else{
        ElMessage.error('请录入正确的数据')
      }
    },
    loginErr3LingBtn(){
      if(this.loginErr3Ling == 1){ // 使用被冻结的原领地
        selectOldAcc().then(res1=>{
          let res = res1.data
          let success = res.success
          console.log('selectOldAcc=', res)
          if(success === 1){
            this.$message.success('操作成功！')
            this.logoutFun()
          }else{
            this.$message.success('操作失败，请重试！')
          }
        }).catch(err=>{
          console.log('err=', err)
        })
      }
      else if(this.loginErr3Ling == 2){ // 使用新领地
        selectNewAcc().then(res1=>{
          let res = res1.data
          let success = res.success
          if(success === 1){
            this.$message.success('操作成功！')
            this.logoutFun()
          }else{
            this.$message.success('操作失败，请重试！')
          }

        }).catch(err=>{
          console.log('err=', err)
        })
      }
      else{
        this.$message.error('请先选择！')
      }
    },
    logoutFun (){
      logout().then(res => {
        console.log('logOut res=', res)
        let status = res.status
        if(status === 200){
          sphdSocket.reflushAuth()
          this.$router.replace('Start')
          location.reload(true)
        }else{
          this.$message.error('退出失败！')
        }
      }).catch(err => {
        console.log('logOut err=', err)
        this.$message.error('退出失败！')
      })
    },
    goLogin:function(){
      this.codeLoginFun()

    },
    codeLoginFun:function () {
      codeLogin().then(res => {
        console.log('codeLoginFun res=', res)
        let logged = false
        let user = res.data.user
        let error = res.data.error
        if(error != undefined){
          this.$alert(error, '提示', {
            confirmButtonText: '确定',
          })
        }else {
          this.codeNextFData = user
          this.codeNextFunOk()
        }

        // //没有找到，跳转领地页？
        // if(!logged) {
        //   this.$router.push('errPage')//没有找到，跳转领地页还是报错提示？
        // }

      }).catch(err => {
        console.log('codeLoginFun err=', err)
        this.$alert(err, '提示', {
          confirmButtonText: '确定',
        })
      })

    },
    codeNextF(num){
      this.mainCVisble = 3
      this.codeNextFrm.pass = ''
      this.codeNextFrm.pass2 = ''
      this.codeNextFrm.type = num
    },
    codeNextFunOk(){
      if(this.codeNextFData){ // 只有一个机构，进入
        this.$router.push('mainPage')
      }
      else {
        this.$router.push('orgList')
      }
    },
  },
}
</script>






