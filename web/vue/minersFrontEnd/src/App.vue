<template>
  <div id="nav">
    <router-view/>
  </div>

</template>

<style lang="scss">
[class^="el-icon-fa"], [class*=" el-icon-fa"] {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome!important;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
@import 'font-awesome/css/font-awesome.css';
$fa-css-prefix: el-icon-fa;


#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  height: 100%;
}
#nav{
  height: 100%;
}
*{
  padding: 0; margin:0;
  font-family: "Helvetica Neue",Helvetica,Arial,"Microsoft Yahei","Hiragino Sans GB","Heiti SC","WenQuanYi Micro Hei",sans-serif;
}
</style>
<script setup>
import auth from '@/sys/auth'
import initStorage from '@/sys/initStorage'
import sphdSocket from '@/sys/sphd'
auth.init({isGuest: true})
initStorage({})
sphdSocket.start()
</script>