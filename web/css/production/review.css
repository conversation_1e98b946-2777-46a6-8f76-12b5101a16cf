/*弹窗上侧每个键值对样式*/
.orderItem{
    width:240px;
    font-size: 14px;
    float: left;
    margin: 0 5px;
}
.orderItem>.orderItemTitle{
    color: #999;
    margin: 8px 0;
    text-align: left;
}

.hd {
    display: none;
}
.orderItem>.orderItemCon{
    width: 240px;
    height: 32px;
    background-color: #fff;
    border-radius: 5px;
    line-height: 31px;
    text-align: center;
    color: #333;
}

/*下方表格样式*/
.approveList{
    padding: 10px 5px;
    /*min-height: 300px;*/
}
.approveHistoryList{
    padding: 10px 5px;
    min-height: 300px;
}

#reviewTip{ color:#ed5565; text-align:right; font-size:13px;    }

tr.throw td{ text-decoration:line-through ; color:#bbb;   }


.orderItemTiny i{ color:#48cfad ; }
#roleCon_update{ width:455px;   }
.orderItemTiny{
    cursor:pointer;
    display:inline-block;
    width: 100px;
    height: 30px;
    line-height: 30px;
    border-radius: 2px;
    margin-left: 5px;
    margin-top:8px;
    text-align: left;
}
.orderItemTiny i{
    font-size: 14px;
    margin-right:5px;
    width: 14px;
    height: 14px;
}
.ty-dropdownCon>div:nth-child(2){ padding:0 0 5px 15px ; border-bottom:1px solid #eaeaea;    }
.ty-dropdownCon>div:nth-child(3){ padding: 0 0 15px 15px  ;  }
.ty-dropdownCon>div:nth-child(4){ text-align:right; padding: 0 15px 15px;  }
.tipitemCon{ padding: 10px; display:inline-block;  }
#roleCon , #roleCon_update{ width:455px;   }
#supplier{  position: relative;  }
.searchArea{  overflow: hidden;  }
.down-arrow{  float: left; margin-top: 8px;  color: #fff; width: 100px;height:36px;text-align:center;line-height: 36px;  font-size: 18px;background: #0070c0; }
.matItem{margin: 20px 0 10px 0;}
.chooseInSto{margin-left: 40px;}
.bg-yellow tr:nth-child(even) {  background: #ddebf7;  }
.bg-yellow thead td {  border-color: #fff0;  background-color: #fff2cc;  }
#materielWaitingIn{margin-top: 20px;}
#chooseInMateriel .bonceCon{padding-left: 40px;}
.lt_ttl{margin-left: 6px;display: inline-block;width: 80px;}
.rt_ttl{margin-left: 6px;display: inline-block;width: 100px;text-align: center;}
.specialPlace{ overflow: hidden;}
.specialPlace li{ float: left;margin: 6px 0; width: 50%;}
.bonceCon .specialPlace li input[readonly],.bonceCon .specialPlace li select[readonly],
.bonceCon .specialPlace li input[disabled],.bonceCon .specialPlace li select[disabled]
{ background-color: #e7e7e7;}
.selecGS {
    display: none;
    position: absolute;
    background: #fff;
    width: 182px;
    max-height: 200px;
    overflow-y: auto;
    border-radius: 4px;
    border: 1px solid #ccc;
    border-top: none;
    right:0;
}
.selecGS div { border-bottom:1px solid #eee; line-height: 30px; padding: 0 10px;  }
.selecGS div:hover, .selecGS div.active { background:#eee; cursor: default;    }

.guide_avatar{  width: 400px;  margin:250px auto;  position: relative;  }
.searchInput{  float: left;  width: 300px;  height: 36px;  border: 1px solid #c8c7c7;  margin-top: 8px;  background-color: #fff;  padding: 0 10px;  font-size: 12px;
    min-width: 182px;  color: #666;  }
.input_choose_list{  position: absolute;  width: 300px;  top: 46px;  left: 0;  border: 1px solid #dcdfe6;  display: flex;  flex-direction: column;
    background: #fff;  display: none;  max-height: 200px;  overflow: auto;  box-shadow: 0 1px 2px #a3acaa;  z-index: 50;  }
.input_choose_item{  padding: 6px 10px;  cursor: pointer;  line-height: 24px;  }
.input_choose_item:not(.disabled):hover{  background-color: #e9ecee;  }
.ttl-bold{font-weight: 500;}
.conWrap {padding: 0 30px;}
.sect{ margin-top: 10px;}
.line2{ padding: 10px 0; border-bottom: 1px solid #cccccc;}
.indent {margin-left: 20px;}
.indent .fa{ margin-right: 20px;}
.line3{ background: #fce4d6; padding: 10px;  }
.line3 p{ margin:0;  }
.line4{padding: 0 10px;}
.btnDo{font-weight: bold; cursor:default; }
.linkBtn:hover{ text-decoration:underline; mix-blend-mode:darken;  }
.supplerInfo > div{ margin-right: 40px; display: inline-block;}
.ttl{margin-right: 20px;}
.select1{display: none;}

#arriveDate{padding: 5px;color: #444;}
#inStorageApplyTip .fa, #memoSelect .fa{ color: #036bc0; }
#matDistribution input { display: inline-block; width: 100px; }
#matDistribution input[disabled]{ background:#ddd; }
.mainCon,.reviewList1,.unableArrive{display: none;}
.gapFar{margin-left: 50px;}
.mainCon input{width: 100%;height: 30px;line-height: 30px;border: 1px solid #d7d7d7;}
.mainCon input[disabled]{background: #d8d8d8; cursor: no-drop;}
.gapBt,.headGroup {margin-bottom: 30px;}
.itemCn{width:260px;font-size: 14px;float: left;margin-bottom: 10px;margin-left: 50px;}
.itemCn>.orderItemTitle{margin: 8px 0;text-align: left;}
.itemCn-l {width:570px;}
.linkBtn {color: #5a94ff;display: inline-block;padding: 0 10px;cursor: default;}
.itemCon{padding-left: 4px;min-height: 30px;line-height: 30px;background: #e1dfdf;}
.redWarning{color: #ed3501}
.ty-table tbody td.red{color: #ed3501;}
.goReviewList0 tbody tr td:nth-child(1),.goReviewList0 tbody tr td:nth-child(2),.goReviewList0 tbody tr td:nth-child(6),
.goReviewList1 tbody tr td:nth-child(2),.goReviewList1 tbody tr td:nth-child(3),.goReviewList1 tbody tr td:nth-child(7) {text-align: left;}
.goReviewList0 ,.goReviewList1{table-layout: fixed;}
.goReviewList0 tbody tr td ,.goReviewList1 tbody tr td {overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}
.goReviewList1 tbody tr.grayBg td{background: #e6e6e6;}
.redWarning .shipped{float: right;color: #34495e;}
.modHistoryMsg{margin-left: 50px;color:  #ed3501;}

.el-row{
    display: flex;
    margin-left: -10px;
    margin-right: -10px;
    margin-bottom: 8px;
}
.el-12{
    width: 50%;
    padding-left: 10px;
    padding-right: 10px;
}
.el-24{
    width: 100%;
    padding-left: 10px;
    padding-right: 10px;
}
.el-6{
    width: 25%;
    padding-left: 10px;
    padding-right: 10px;
}
#chooseMateriel_baoZhiQi .form-item{
    display: flex;
}
#chooseMateriel_baoZhiQi .form-item.label-top{
    display: block;
}
#chooseMateriel_baoZhiQi .form-item.label-top .form-item-label{
    width: 100%;
}
#chooseMateriel_baoZhiQi .form-item-content{
    position: relative;
}
#chooseMateriel_baoZhiQi .form-item-content .ty-inputText{
    width: 100%;
}
.search_choose_list{
    display: none;
    background: #fefefe;
    position: absolute;
    top: 32px;
    width: 100%;
    left: 1px;
    z-index: 20;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #dcdfe6;
}
.search_choose_list_avatar{
    width: 100%;
    display: flex;
    flex-direction: column;
}
.search_item{
    padding: 8px 16px;
    color: #46494d;
}
.search_item:hover{
    background: #f2f2f2;
}
.bounceCon input:read-only{
    background: #eee;
}
.delBtn{
    position: absolute;
    right: -36px;
    line-height: 30px;
    top: 2px;
}
.hidePart{
    display: none;
}


#chooseMateriel_baoZhiQi .bonceCon input:disabled{
    background: #eee;
}
#chooseMateriel_baoZhiQi .bonceCon input:read-only{
    background: #eee;
}

.groupItem .unit{
    position: absolute;
    right: 16px;
    line-height: 30px;
}





