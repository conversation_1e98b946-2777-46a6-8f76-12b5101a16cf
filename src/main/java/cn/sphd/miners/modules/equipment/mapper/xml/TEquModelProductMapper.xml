<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.sphd.miners.modules.equipment.mapper.TEquModelProductMapper">
    
    <resultMap type="TEquModelProduct" id="TEquModelProductResult">
        <result property="id"    column="id"    />
        <result property="org"    column="org"    />
        <result property="model"    column="model"    />
        <result property="product"    column="product"    />
        <result property="enabled"    column="enabled"    />
        <result property="enabledTime"    column="enabled_time"    />
        <result property="memo"    column="memo"    />
        <result property="creator"    column="creator"    />
        <result property="createName"    column="create_name"    />
        <result property="createDate"    column="create_date"    />
        <result property="updator"    column="updator"    />
        <result property="updateName"    column="update_name"    />
        <result property="updateDate"    column="update_date"    />
        <result property="operation"    column="operation"    />
        <result property="previousId"    column="previous_id"    />
        <result property="versionNo"    column="version_no"    />
    </resultMap>

    <sql id="selectTEquModelProductVo">
        select id, org, model, product, enabled, enabled_time, memo, creator, create_name, create_date, updator, update_name, update_date, operation, previous_id, version_no from t_equ_model_product
    </sql>

    <select id="selectTEquModelProductList" parameterType="TEquModelProduct" resultMap="TEquModelProductResult">
        <include refid="selectTEquModelProductVo"/>
        <where>  
            <if test="org != null "> and org = #{org}</if>
            <if test="model != null "> and model = #{model}</if>
            <if test="product != null "> and product = #{product}</if>
            <if test="enabled != null "> and enabled = #{enabled}</if>
            <if test="enabledTime != null "> and enabled_time = #{enabledTime}</if>
            <if test="memo != null  and memo != ''"> and memo = #{memo}</if>
            <if test="creator != null "> and creator = #{creator}</if>
            <if test="createName != null  and createName != ''"> and create_name like concat('%', #{createName}, '%')</if>
            <if test="createDate != null "> and create_date = #{createDate}</if>
            <if test="updator != null "> and updator = #{updator}</if>
            <if test="updateName != null  and updateName != ''"> and update_name like concat('%', #{updateName}, '%')</if>
            <if test="updateDate != null "> and update_date = #{updateDate}</if>
            <if test="operation != null "> and operation = #{operation}</if>
            <if test="previousId != null "> and previous_id = #{previousId}</if>
            <if test="versionNo != null "> and version_no = #{versionNo}</if>
        </where>
    </select>
    
    <select id="selectTEquModelProductById" parameterType="Long" resultMap="TEquModelProductResult">
        <include refid="selectTEquModelProductVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTEquModelProduct" parameterType="TEquModelProduct" useGeneratedKeys="true" keyProperty="id">
        insert into t_equ_model_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="org != null">org,</if>
            <if test="model != null">model,</if>
            <if test="product != null">product,</if>
            <if test="enabled != null">enabled,</if>
            <if test="enabledTime != null">enabled_time,</if>
            <if test="memo != null">memo,</if>
            <if test="creator != null">creator,</if>
            <if test="createName != null">create_name,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updator != null">updator,</if>
            <if test="updateName != null">update_name,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="operation != null">operation,</if>
            <if test="previousId != null">previous_id,</if>
            <if test="versionNo != null">version_no,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="org != null">#{org},</if>
            <if test="model != null">#{model},</if>
            <if test="product != null">#{product},</if>
            <if test="enabled != null">#{enabled},</if>
            <if test="enabledTime != null">#{enabledTime},</if>
            <if test="memo != null">#{memo},</if>
            <if test="creator != null">#{creator},</if>
            <if test="createName != null">#{createName},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updator != null">#{updator},</if>
            <if test="updateName != null">#{updateName},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="operation != null">#{operation},</if>
            <if test="previousId != null">#{previousId},</if>
            <if test="versionNo != null">#{versionNo},</if>
         </trim>
    </insert>

    <update id="updateTEquModelProduct" parameterType="TEquModelProduct">
        update t_equ_model_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="org != null">org = #{org},</if>
            <if test="model != null">model = #{model},</if>
            <if test="product != null">product = #{product},</if>
            <if test="enabled != null">enabled = #{enabled},</if>
            <if test="enabledTime != null">enabled_time = #{enabledTime},</if>
            <if test="memo != null">memo = #{memo},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="createName != null">create_name = #{createName},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updator != null">updator = #{updator},</if>
            <if test="updateName != null">update_name = #{updateName},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="operation != null">operation = #{operation},</if>
            <if test="previousId != null">previous_id = #{previousId},</if>
            <if test="versionNo != null">version_no = #{versionNo},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTEquModelProductById" parameterType="Long">
        delete from t_equ_model_product where id = #{id}
    </delete>

    <delete id="deleteTEquModelProductByIds" parameterType="String">
        delete from t_equ_model_product where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>