package cn.sphd.miners.modules.resourceAuthority.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.resourceAuthority.entity.*;
import cn.sphd.miners.modules.resourceAuthority.service.ResCategoryService;
import cn.sphd.miners.modules.resourceAuthority.service.ResNoticeService;
import cn.sphd.miners.modules.resourceAuthority.service.ResService;
import cn.sphd.miners.modules.system.dto.UserHonePageDto;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import java.util.*;

/**
 * Created by 朱思旭 on 2017/11/6.
 */
@Controller
@RequestMapping("/res")
public class DataManagerController {

    @Autowired
    ResCategoryService resCategoryService;
    @Autowired
    ResService resService;
    @Autowired
    UserService userService;
    @Autowired
    RedisTemplate redisTemplate;
    @Autowired
    ResNoticeService resNoticeService;

    /*资源中心和资料管理公共调用接口*/

    //修改科目名称
    @ResponseBody
    @RequestMapping("/UpdateFolderName.do")
    public JsonResult UpdateFolderName(User user, ResCategory resCategory) {
        QueryData query = resCategoryService.UpdateCategoryName(user, resCategory);
        Integer upState = (Integer) query.get("upState");
        Integer state = (Integer) query.get("state");
        if (state == 0) {
            return new JsonResult("文件名称重复");
        } else {
            if (upState > 0) {
                ResCategory category = (ResCategory) query.get("category");
                return new JsonResult(1, category);
            } else {
                return new JsonResult("修改失败");
            }
        }
    }

    //获取文件夹下文件
    @ResponseBody
    @RequestMapping("/getFile.do")
    public JsonResult getFile(User user, Integer categoryId, PageInfo pageInfo, String type, String teminateState) {
        QueryData query = resService.getFile(user, categoryId, pageInfo, type, teminateState);
        return new JsonResult(1, query);
    }

    //点击查看换版记录按钮
    @ResponseBody
    @RequestMapping("/getUpdateFile.do")
    public JsonResult getUpdateFile(User user, PageInfo pageInfo, Integer fileId) {
        QueryData query = resService.getUpFile(user, pageInfo, fileId);
        return new JsonResult(1, query);
    }


    /*资料管理调用接口*/

    //删除文件夹（资料管理）
    @ResponseBody
    @RequestMapping("/delFolder.do")
    public JsonResult delFolder(User user, Integer categoryId) {
        String state = resCategoryService.deleteCategory(categoryId);
        return new JsonResult(1, state);
    }

    //新增文件夹（资料管理）
    @ResponseBody
    @RequestMapping("/AddSameFolderByData.do")
    public JsonResult AddSameFolderByData(User user, Integer parent, String categoryName) {
        QueryData query = resCategoryService.addSameCategoryByData(user, parent, categoryName, "2");
        Integer state = (Integer) query.get("state");
        if (state == 0) {
            return new JsonResult("文件夹名称重复");
        } else {
            ResCategory newCategory = (ResCategory) query.get("newCategory");
            return new JsonResult(1, newCategory);
        }
    }

    //用于判断点击删除和新增子级文件夹时本级文件夹下是否存在文件(资料管理)
    @ResponseBody
    @RequestMapping("/clickButtenJudgementFile.do")
    public JsonResult clickButtenJudgementFile(Integer categoryId) {
        List<ResEntity> list = resService.judgementFileByCategoryId(categoryId);
        if (list.isEmpty()) {
            return new JsonResult(1, "可以新增或删除文件");
        } else {
            return new JsonResult(0, "该文件夹下存在文件，不可以删除或新增文件");
        }
    }

    //资料管理确定上传文件(资料管理)
    @ResponseBody
    @RequestMapping("/resManageAffridFile.do")
    public JsonResult resManageAffridFile(User user, ResEntity resEntity, String module) {
        ResEntity resource = resService.insertResManageFile(user, resEntity, module);
        return new JsonResult(1, resource);
    }

    /*//资料管理删除文件(资料管理)
    @ResponseBody
    @RequestMapping("/delFile.do")
    public JsonResult delFile(HttpSession session, Integer fileId) {
        String loadpath = session.getServletContext().getRealPath("/") + "upload";
        Integer status = resService.deleteFile(fileId, loadpath);
        return new JsonResult(1, "删除成功");
    }*/

    //点击文件基本信息按钮获取文件的信息(资料管理)
    @ResponseBody
    @RequestMapping("/getFileMessageByReference.do")
    public JsonResult getFileMessageByReference(User user, Integer id) {
        QueryData query = resService.getFileMesByReference(id, user);
        return new JsonResult(1, query);
    }

    //点击换版记录中文件的基本信息按钮获取文件的信息(资料管理)
    @ResponseBody
    @RequestMapping("/getUpFileMesByReference.do")
    public JsonResult getUpFileMesByReference(Integer id) {
        QueryData query = resService.getHisFileMesByReference(id);
        return new JsonResult(1, query);
    }

    //移动文件(资料管理)
    @ResponseBody
    @RequestMapping("/changeFilePlace.do")
    public JsonResult changeFilePlace(Integer fileId, Integer categoryId) {
        Integer state = resService.removeStatus(fileId, categoryId);
        if (state == 0) {
            return new JsonResult("移动失败");
        } else {
            return new JsonResult(1, "移动成功");
        }
    }


    /*资源中心调用*/

    //(获取权限锁)点击移动或修改权限时同时连点的问题的接口
    @ResponseBody
    @RequestMapping("/getResLockUuid.do")
    public JsonResult getResLockUuid(Integer categoryId, Integer fileId) {
        String uuid = null;
        if (categoryId != null) {
            ResCategory cat = resCategoryService.getSingle(categoryId);
            uuid = cat.getLockUuid();
        }
        if (fileId != null) {
            ResEntity res = resService.getSingle(fileId);
            uuid = res.getLockUuid();
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("resLockUuid", uuid);
        return new JsonResult(1, map);
        /*String uu = UUID.randomUUID().toString();
        String uuid = uu.replaceAll("-", "");
        System.out.println(uuid);*/
    }

    //用于判断点击删除和新增子级文件夹时本级文件夹下是否存在文件(资源中心)
    @ResponseBody
    @RequestMapping("/clickButtenJudgementFileByCentre.do")
    public JsonResult clickButtenJudgementFileByCentre(User user, Integer categoryId) {
        QueryData query = resService.judgementFileByCategoryIdCentre(user, categoryId);
        return new JsonResult(1, query);
    }

    //移动文件夹之前用于判断是否可以移动的接口
    @ResponseBody
    @RequestMapping("/clickButtenJudgementMove.do")
    public JsonResult clickButtenJudgementFolder(User user, Integer oldCategoryId, Integer fileId, Integer newCategoryId, String type) {
        HashMap<String, Object> map = resCategoryService.judgementMove(user, oldCategoryId, fileId, newCategoryId, type);
        return new JsonResult(1, map);
    }

    //删除文件夹
    @ResponseBody
    @RequestMapping("/delCentreFolder.do")
    public JsonResult delCentreFolder(User user,Integer categoryId) {
        QueryData query = resService.judgementFileByCategoryIdCentre(user, categoryId);
        String state = (String) query.get("state");
        if (!("2".equals(state))) {
            resCategoryService.deleteCategory(categoryId);
            return new JsonResult(1, state);
        }else {
            return new JsonResult(0, state);
        }
    }

    //超管总务小总务获取文件夹下文件
    @ResponseBody
    @RequestMapping("/getFileByManager.do")
    public JsonResult getFileByManager(User user, Integer categoryId, PageInfo pageInfo, String type, String teminateState) {
        QueryData query = resService.getFileByManager(user, categoryId, pageInfo, type, teminateState);
        return new JsonResult(1, query);
    }

    //获取有效或历史文件大小
    @ResponseBody
    @RequestMapping("/effectiveFileSize.do")
    public JsonResult effectiveFileSize(User user, String categoryPath, String type) {
        String size = resService.getFileSize(user,categoryPath,type);
        HashMap<String, Object> map = new HashMap<>();
        map.put("size", size);
        return new JsonResult(1, map);
    }


    //点击文件基本信息按钮获取文件的信息(资源中心)
    @ResponseBody
    @RequestMapping("/getFileMessage.do")
    public JsonResult getFileMessage(User user, Integer id) {
        QueryData query = resService.getFileMes(id, user);
        return new JsonResult(1, query);
    }

    //点击换版记录中文件的基本信息按钮获取文件的信息(资源中心)
    @ResponseBody
    @RequestMapping("/getUpFileMes.do")
    public JsonResult getUpFileMes(User user, Integer id) {
        QueryData query = resService.getHisFileMes(id, user);
        return new JsonResult(1, query);
    }

    //查看某移动的文件下借阅的人的详细记录
    @ResponseBody
    @RequestMapping("/getReadroomUserRecord.do")
    public JsonResult getReadroomUserRecord(User user, Integer fileId, Integer fileHisId) {
        HashMap<String, Object> map = resService.getReadroomUserRecordByFileid(user, fileId, fileHisId);
        return new JsonResult(1, map);
    }

    //移动文件(资源中心)
    @ResponseBody
    @RequestMapping("/changeFilePlaceByResourCentre.do")
    public JsonResult changeFilePlaceByResourCentre(User user, Integer fileId, Integer categoryId, String resLockUuid, String userID) {
        ResEntity res = resService.getSingle(fileId);
        Integer state = 1;
        if (res.getLockUuid() != null) {
            if (!(resLockUuid.equals(res.getLockUuid()))) {
                state = 2;
            } else {
                resService.updateResUuid(fileId);
            }
        }else {
            resService.updateResUuid(fileId);
        }
        if (state == 1) {
            Integer newResHisId = resService.getNewFileHisId(fileId);
            String path = resService.removeStatusByCentre(fileId, categoryId, user, res, newResHisId,"1");
            List<Integer> listUserID = JSON.parseArray(userID, Integer.class);
            resService.changeAuthByMoveFile("1",fileId, newResHisId, categoryId, user, res, listUserID, path, "5");
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        return new JsonResult(1, map);
    }

    //移动文件夹（资源中心）
    @ResponseBody
    @RequestMapping("/changeCategoryPlace.do")
    public JsonResult changeCategoryPlace(User user, Integer oldCategoryId, Integer newCategoryId, String resLockUuid, String userID) {
        Integer state = resCategoryService.changeCategory(user, oldCategoryId, newCategoryId, resLockUuid, userID);
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        return new JsonResult(1, map);
    }

    //垃圾文件还原（资源中心）
    @ResponseBody
    @RequestMapping("/trashFileRestore.do")
    public JsonResult trashFileRestore(User user, Integer fileId) {
        ResEntity res = resService.getSingle(fileId);
        Integer state = resService.restoreFile(user, fileId);
        if (state.equals(1)) {
            ResEntity resNew = resService.getSingle(fileId);
            ResCategory cat = resCategoryService.getSingle(resNew.getCategory());
            String allCategoryName = resService.allParentCategoryName(cat.getParent(), cat.getName(), null, "1");
            allCategoryName = "暂时不用的文件转移到" + allCategoryName;
            Integer newResHisId = resService.getNewFileHisId(fileId);
            resService.changeAuthByMoveFile("2", fileId, newResHisId, resNew.getCategory(), user, res,null, allCategoryName,"5");
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        return new JsonResult(1, map);
    }

    //删除垃圾文件（资源中心）
    @ResponseBody
    @RequestMapping("/delTrashFile.do")
    public JsonResult delTrashFile(User user, Integer fileId) {
        Integer state = resService.updateFileValid(user, fileId);
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        return new JsonResult(1, map);
    }

    //获取即将删除的文件（资源中心）
    @ResponseBody
    @RequestMapping("/getDelTrashFile.do")
    public JsonResult getDelTrashFile(User user) {
        List<ResEntity> list = resService.getAboutToDisapperFile(user);
        HashMap<String, Object> map = new HashMap<>();
        map.put("list", list);
        return new JsonResult(1, map);
    }

    //点击查看即将删除文件详情
    @ResponseBody
    @RequestMapping("/getDelTrashFileMessage.do")
    public JsonResult getDelTrashFileMessage(Integer fileId) {
        HashMap<String, Object> map = resService.getAboutToDisapperFileMessage(fileId);
        return new JsonResult(1, map);
    }

    //还原即将删除的文件到回收站
    @ResponseBody
    @RequestMapping("/delFileRestore.do")
    public JsonResult delFileRestore(User user, Integer fileId) {
        Integer state = resService.restoreDelFile(user, fileId);
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        return new JsonResult(1, map);
    }

    //禁用文件时判断是否可以禁用和历史文件是否被借阅
    @ResponseBody
    @RequestMapping("/clickButtenDisable.do")
    public JsonResult clickButtenDisable(User user, Integer fileHisId) {
        ResHistory rh = new ResHistory();
        rh.setId(fileHisId);
        ResHistory resHistory = resService.gethisSingle(rh);
        Integer state = 1;
        if (!("1".equals(resHistory.getTeminateState()))) {
            state = resService.handleOrGetBorrowedFileHistory(fileHisId,"1",user);
        }else {
            state = 2;
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        return new JsonResult(1, state);
    }

    //禁用某个版本的历史文件
    @ResponseBody
    @RequestMapping("/disableFileHis.do")
    public JsonResult disableFileHis(User user, Integer fileHisId) {
        Integer state = resService.disablefileHistoryVersion(user, fileHisId);
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        return new JsonResult(1, map);
    }

    //修改文件名字(资源中心)
    @ResponseBody
    @RequestMapping("/updateFileName.do")
    public JsonResult updateFileName(User user, String name, Integer id){
        Integer state = resService.updateFileName(name, user, id);
        if (state == 0) {
            return new JsonResult("修改失败");
        } else {
            return new JsonResult(1,"修改成功");
        }
    }

    //记录浏览和移动记录(资源中心)
    @ResponseBody
    @RequestMapping("/viewAndDownloadRecord.do")
    public JsonResult viewAndDownloadRecord(User user, Integer id, String type){
        Integer state = resService.recordViewAndDownload(user, id,type);
        if (state == 0) {
            return new JsonResult("失败");
        } else {
            return new JsonResult(1,"成功");
        }
    }

    //修改文件编号(资源中心)
    @ResponseBody
    @RequestMapping("/updateFileSn.do")
    public JsonResult updateFileSn(User user, String fileSn, Integer id){
        Integer state = resService.updateFileSn(user, id, fileSn);
        if (state == 0) {
            return new JsonResult("文件编号重复");
        } else if(state == 1) {
            return new JsonResult(1,"成功");
        }else {
            return new JsonResult("失败");
        }
    }

    //查看移动，修改名称和修改编号的记录(资源中心)
    @ResponseBody
    @RequestMapping("/getRecordByUpAndMove.do")
    private JsonResult getRecordByUpAndMove(String operation, Integer fileId){
        List<FiveRecordEntity> listRecard = resService.getRecordByOperation(operation, fileId);
        return new JsonResult(1,listRecard);
    }

    //查看浏览和下载记录(资源中心)
    @ResponseBody
    @RequestMapping("/getRecordByShow.do")
    private JsonResult getRecordByShow(String type, Integer id){
        List<ResVisit> listRecard = resService.getRecordByType(type, id);
        return new JsonResult(1,listRecard);
    }

    //根据名字和编号搜索文件(普通检索)
    @ResponseBody
    @RequestMapping("/generalFindFile.do")
    public JsonResult generalFindFile(User user, ResEntity resEntity, PageInfo pageInfo, String type){
        QueryData queryData = resService.generalSearchFile(user, pageInfo, resEntity, type);
        List<ResEntity> list = (List<ResEntity>) queryData.get("listFile");
        List<String> listCategory = this.listCategory(list);
        queryData.put("listCategory", listCategory);
        return new JsonResult(1, queryData);
    }

    //根据名字，编号，类型，上传人等搜索文件（高级检索）
    @ResponseBody
    @RequestMapping("/advancedFindFile.do")
    public JsonResult advancedFindFile(User user, ResEntity resEntity, PageInfo pageInfo){
        QueryData queryData = resService.advancedSearchFile(user, pageInfo, resEntity);
        List<ResEntity> list = (List<ResEntity>) queryData.get("listFile");
        List<String> listCategory = this.listCategory(list);
        queryData.put("listCategory", listCategory);
        return new JsonResult(1, queryData);
    }

    //根据名字模糊搜索文件夹
    @ResponseBody
    @RequestMapping("/findFolder.do")
    public JsonResult findFolder(User user, String name, PageInfo pageInfo){
        QueryData queryData = resService.searchFolder(user, pageInfo, name);
        return new JsonResult(1, queryData);
    }

    //获取修改文件夹名字的历史记录
    @ResponseBody
    @RequestMapping("/getCategoryHistory.do")
    public JsonResult getCategoryHistory(Integer categoryId){
        List<ResCategoryHistory> list = resCategoryService.listCategoryHistory(categoryId);
        HashMap<String, Object> map = new HashMap<>();
        map.put("catHistory", list);
        return new JsonResult(1,map);
    }

    //获取使用权限操作记录
    @ResponseBody
    @RequestMapping("/getResAclLog.do")
    public JsonResult getResAclLog(User user, String year, String month, String name, String type, PageInfo pageInfo){
        Integer oid = user.getOid();
        List<ResAclLog> list = resCategoryService.listResAclLog(oid,year,month,name,type,pageInfo);
        HashMap<String, Object> map = new HashMap<>();
        map.put("listAclLog", list);
        map.put("pageInfo", pageInfo);
        return new JsonResult(1,map);
    }

    //获取使用权限操作记录的详细信息
    @ResponseBody
    @RequestMapping("/resAclLogMes.do")
    public JsonResult resAclLogMes(Integer aclId, String type){
        QueryData queryData = resCategoryService.resAclLogMessage(aclId, type);
        return new JsonResult(1,queryData);
    }

    //根据type获取需要签收的信息
    @ResponseBody
    @RequestMapping("/listResNoticeByType.do")
    public JsonResult listResNoticeByType(User user, String type){
        List<ResNotice> list = resNoticeService.listResNoticeByType(user, type);
        HashMap<String, Object> map = new HashMap<>();
        map.put("listResNotice", list);
        return new JsonResult(1,map);
    }

    //获取某批次上传的具体文件
    @ResponseBody
    @RequestMapping("/listBatchRes.do")
    public JsonResult listBacthRes(User user, String batchUuid){
        List<ResHistory> list = resService.listHisFileByBatchUuid(batchUuid);
        HashMap<String, Object> map = new HashMap<>();
        map.put("listHis", list);
        return new JsonResult(1,map);
    }

    //签收文件通知
    @ResponseBody
    @RequestMapping("/signedResource.do")
    public JsonResult signedResource(User user, String type, Integer id){
        Integer state = resNoticeService.signedResourceByType(user,id,type);
        HashMap<String, Object> map = new HashMap<>();
        map.put("state", state);
        return new JsonResult(1,map);
    }

    //获取即将消失的文件通知
    @ResponseBody
    @RequestMapping("/getDisappearResNotice.do")
    public JsonResult getDisappearResNotice(User user, String type){
        List<ResNotice> list = resNoticeService.listDisappearResNoticeByType(user,type);
        HashMap<String, Object> map = new HashMap<>();
        map.put("listResNotice", list);
        return new JsonResult(1,map);
    }

    //获取某个文件版本的具体签收记录
    @ResponseBody
    @RequestMapping("/getResHisSignedRecord.do")
    public JsonResult getResHisSignedRecord(Integer resHisId,String type){
        ResHistory rh = new ResHistory();
        rh.setId(resHisId);
        ResHistory resHistory = resService.gethisSingle(rh);
        List<ResNotice> list = null;
        if (resHistory.getBatchUuid() != null) {
            list = resNoticeService.getListNotice(null, null, resHistory.getBatchUuid(), "1", null);
        }else {
            list = resNoticeService.getListNotice(null, resHisId, null, type, null);
        }
        if (list != null) {
            for (ResNotice rn : list) {
                UserHonePageDto userHonePageDto = userService.getUserHonePageDtoByUserId(rn.getUser());
                rn.setUserName(userHonePageDto.getUserName());
                rn.setMobile(userHonePageDto.getMobile());
            }
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("listResNotice", list);
        return new JsonResult(1,map);
    }

    //获取文件禁用和启用的记录
    @ResponseBody
    @RequestMapping("/getResTemianteRecord.do")
    public JsonResult getResTemianteRecord(Integer fileId){
        List<ResHistory> list = resService.listHisFileByTemiante(fileId);
        HashMap<String, Object> map = new HashMap<>();
        map.put("listResHistory", list);
        return new JsonResult(1,map);
    }

    //去重操作去掉重复的文件夹
    private List<String> listCategory(List<ResEntity> listFile){
        Set<String> set = new HashSet<>();
        List<String> list = new ArrayList<>();
        for(ResEntity r : listFile){
            if(set.add(r.getCategory().toString())){
                list.add(r.getCategory().toString());
            }
        }
        return list;
    }


}
