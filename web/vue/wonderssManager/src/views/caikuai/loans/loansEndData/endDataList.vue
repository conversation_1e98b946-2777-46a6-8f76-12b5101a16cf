<template>
  <!-- 已完结的数据-->
  <div class="box">
    <div class="head">
      <PageHeader :title="tit" :showClose=true @mycloseFun="handleCustomClose" :showBack=true>
      </PageHeader>
    </div>
    <div class="sticky ty-fff">
      <p class="h35f14  padding-0-15 spaceBetween">
        <span>以下为已完结的贷款</span>
      </p>
    </div>
    <div class="re margin_top_16">
      <el-table
          :data="list"
          style="width: 100%"
          stripe
          header-row-class-name="el_title"
          :default-sort="{prop: 'date', order: 'descending'}"
          :header-cell-style="{background: '#ffffff',color:'#2c3e50'}"
          @cell-click="rou"
      >
        <!--表头背景颜色 header-cell-style-->
        <el-table-column prop="loaner" label="出资方" header-align="center" align="center" min-width="130"
                         label-class-name="h35f16"
                         show-overflow-tooltip>
        </el-table-column>

        <el-table-column prop="principalAmount" label="本金" align="center" header-align="center" min-width="100"
                         show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="amountRepayed" label="已付款总额" align="center" header-align="center" min-width="100"
                         show-overflow-tooltip>
        </el-table-column>
        <el-table-column min-width="40" align="center">
          <el-icon>
            <arrow-right/>
          </el-icon>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import finance from '@/mixins/finance.js'

export default {
  name: "endDataList",
  mixins: [finance],
  data() {
    return {
      tit: "财会",
      list: [],
      loanDetail: {}
    }
  },
  mounted() {
    this.getLoans()
  },
  methods: {
    getLoans() {
      const params = {state: false, loanType: 1}
      this.financeApi.getLoansList(params).then(res1 => {
        console.log('getLoansList res1=', res1)
        //格式化时间戳
        this.list = res1.data.data.list
        this.list.forEach(item => {
          item.arriveDate = new Date(item.arriveDate).format('yyyy-MM-dd');
          item.principalAmount = this.formatCurrency(item.principalAmount)
          if (item.amountRepayed == null) {
            item.amountRepayed = ''
          } else {
            item.amountRepayed = this.formatCurrency(item.amountRepayed)
          }
        });
      }).catch(err => {
        console.log('err=', err)
        this.$message.error('链接失败，请重试！')
      })

    },
    rou(row, column, cell, event) {
      this.$router.push({
        path: '/loansDetail', query: {id: row.id}
      })
    },
    handleCustomClose() {
      this.$router.push("/caikuai")
    }
  }
}
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";

.head {
  position: fixed;
  width: 100%;
  z-index: 999;
}

::v-deep .el-table__row td, .el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf {
  border-bottom: none;
}

::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background: #ffffff;
}

//非斑马纹颜色
::v-deep .el-table tr {
  background: #e8e8e8;
}

.re {
  position: relative;
  top: 40px;
}

</style>