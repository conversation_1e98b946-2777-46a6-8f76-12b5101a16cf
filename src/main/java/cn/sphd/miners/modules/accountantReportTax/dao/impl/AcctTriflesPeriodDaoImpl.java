package cn.sphd.miners.modules.accountantReportTax.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTriflesDao;
import cn.sphd.miners.modules.accountantReportTax.dao.AcctTriflesPeriodDao;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTriflesEntity;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTriflesPeriodEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

@Repository
public class AcctTriflesPeriodDaoImpl extends BaseDao<AcctTriflesPeriodEntity, Serializable> implements AcctTriflesPeriodDao {

    @Autowired
    AcctTriflesPeriodDao triflesPeriodDao;

    @Override
    public List<AcctTriflesPeriodEntity> getTriflesPeriodList(Integer triflesId){
        String periodHql = "from AcctTriflesPeriodEntity where trifles=:trifles";
        HashMap<String, Object> periodParam = new HashMap<>();
        periodParam.put("trifles", triflesId);
        List<AcctTriflesPeriodEntity> periodList = triflesPeriodDao.getListByHQLWithNamedParams(periodHql,periodParam);
        return periodList;
    }
}
