package cn.sphd.miners.modules.steamer.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.TimeUtils;
import cn.sphd.miners.modules.lyric.dao.CmwPlaylistDao;
import cn.sphd.miners.modules.lyric.dao.CmwPlaylistSongsDao;
import cn.sphd.miners.modules.lyric.entity.CmwPlaylist;
import cn.sphd.miners.modules.lyric.entity.CmwPlaylistSongs;
import cn.sphd.miners.modules.lyric.entity.CmwRhythm;
import cn.sphd.miners.modules.lyric.entity.CmwSongs;
import cn.sphd.miners.modules.lyric.service.LyricService;
import cn.sphd.miners.modules.steamer.dto.PlaylistSongsDto;
import cn.sphd.miners.modules.steamer.service.SteamerService;
import cn.sphd.miners.modules.system.entity.User;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service("SteamerService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED)
public class SteamerServiceImpl implements SteamerService {

    @Autowired
    CmwPlaylistDao playlistDao;
    @Autowired
    CmwPlaylistSongsDao playlistSongsDao;
    @Autowired
    LyricService lyricService;

    @Override
    public List<CmwSongs> getPlayListSongs(User user, Long id) {
        List<Long> playListIds = null;
        if (id != null) {
            String hql = "select songs from CmwPlaylistSongs where playlist = :playlist";
            Map<String, Object> param = new HashMap<String, Object>(1){{
                put("playlist", id);
            }};
            playListIds = playlistSongsDao.getListByHQLWithNamedParams(hql,param);
        }
        List<CmwSongs> listAllSongs = lyricService.listAllSongsByPlayList(user, playListIds);
        return listAllSongs;
    }

    @Override
    public Map<String, Object> getAllPlaylist(User user, String time) {
        String hql = " from CmwPlaylist where creator = :creator";
        Map<String, Object> param = new HashMap<String, Object>(){{
            put("creator", user.getUserID());
        }};
        List<CmwPlaylist> playlist = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        if (StringUtils.isBlank(time)) {
            CmwPlaylist allPlaylist = new CmwPlaylist();
            allPlaylist.setName("全部");
            PageInfo pageInfo = new PageInfo();
            pageInfo.setPageSize(20);
            List<PlaylistSongsDto> listSongs =  this.getPlaylistByAll(user, null, pageInfo);
            allPlaylist.setSongNum(pageInfo.getTotalResult());
            playlist.add(allPlaylist);
            map.put("listSongs", listSongs);
            map.put("pageInfo", pageInfo);
        } else {
            Date createTime = NewDateUtils.dateFromString(time, "yyyy-MM-dd HH:mm:ss");
            hql = hql + " and createTime < :createTime";
            param.put("createTime", createTime);
        }
        hql = hql + " order by createTime desc";
        List<CmwPlaylist> list = playlistDao.getListByHQLWithNamedParams(hql,param);
        playlist.addAll(list);
        map.put("playlist", playlist);
        return map;
    }

    @Override
    public Map<String, Object> getPlaylistMes(User user, Long id) {
        Map<String, Object> map = new HashMap<>();
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(20);
        CmwPlaylist playlist = new CmwPlaylist();
        if (id == null) {
            playlist.setName("全部");
            List<PlaylistSongsDto> listSongs =  this.getPlaylistByAll(user, null, pageInfo);
            playlist.setSongNum(pageInfo.getTotalResult());
            map.put("listSongs", listSongs);
        } else {
            playlist = this.getPlayListSingle(id);
            List<PlaylistSongsDto> list = this.moreSongs(user, id, null, pageInfo);
            map.put("listSongs", list);
        }
        map.put("playlist", playlist);
        map.put("pageInfo", pageInfo);
        return map;
    }

    @Override
    public List<PlaylistSongsDto> moreSongs(User user, Long id, String name, PageInfo pageInfo) {
        List<PlaylistSongsDto> listNowPlaylist = null;
        if (id == null) {
            listNowPlaylist =  this.getPlaylistByAll(user, name, pageInfo);
        }else {
            String hql = "select new cn.sphd.miners.modules.steamer.dto.PlaylistSongsDto(p.id, p.playlist, p.songs, s.name, s.captionRequired, s.originalPath, s.trackDuration, s.bgmPath, s.bgmDuration, s.songDuration) from CmwPlaylistSongs p left join CmwSongs s on p.songs = s.id where p.playlist = :playlist and p.creator = :creator";
            Map<String, Object> param = new HashMap<String, Object>(){{
                put("playlist", id);
                put("creator", user.getUserID());
            }};
            if (StringUtils.isNotBlank(name)) {
                hql = hql + " and s.name like :name";
                param.put("name", "%"+name+"%");
            }
            hql = hql + " order by s.name asc";
            listNowPlaylist= playlistSongsDao.getListByHQLWithNamedParams(hql,param,pageInfo);
            if (!listNowPlaylist.isEmpty()) {
                for (PlaylistSongsDto s : listNowPlaylist) {
                    if (s.getCaptionRequired()) {
                        CmwRhythm cmwRhythm = lyricService.cmwRhythm(s.getSongs(),null, true);
                        s.setRhythm(cmwRhythm.getRhythm());
                    }
                }
            }
        }
        return listNowPlaylist;
    }

    @Override
    public Integer addPlayList(User user, String liveDate, String songIds) {
        String name = liveDate.substring(0,10);
        Date liveTiem = NewDateUtils.dateFromString(liveDate, "yyyy-MM-dd HH:mm:ss");
        CmwPlaylist playlist = new CmwPlaylist();
        playlist.setName(name);
        playlist.setLiveTime(liveTiem);
        playlist.setCreator(user.getUserID());
        playlist.setCreateName(user.getUserName());
        playlist.setCreateTime(new Date());
        playlistDao.save(playlist);
        if (StringUtils.isNotBlank(songIds)) {
            String[] ids = songIds.split(",");
            List<String> listIds = new ArrayList<>(ids.length);
            Collections.addAll(listIds,ids);
            playlist.setSongNum(listIds.size());
            for (String s : listIds) {
                CmwPlaylistSongs playlistSongs = new CmwPlaylistSongs();
                playlistSongs.setSongs(Long.valueOf(s));
                playlistSongs.setPlaylist(playlist.getId());
                playlistSongs.setCreator(user.getUserID());
                playlistSongs.setCreateName(user.getUserName());
                playlistSongs.setCreateTime(playlist.getCreateTime());
                playlistSongsDao.save(playlistSongs);
            }
        }
        Integer state = 1;
        return state;
    }

    @Override
    public Integer upPlayList(User user, Long id, String songIds, String liveDate) {
        CmwPlaylist playlist = this.getPlayListSingle(id);
        if (StringUtils.isNotBlank(liveDate)) {
            String name = liveDate.substring(0,10);
            Date liveTiem = NewDateUtils.dateFromString(liveDate, "yyyy-MM-dd HH:mm:ss");
            playlist.setName(name);
            playlist.setLiveTime(liveTiem);
        }
        playlist.setUpdateTime(new Date());
        Integer state = 1;
        if (StringUtils.isNotBlank(songIds)) {
            String[] ids = songIds.split(",");
            List<String> addSongs = new ArrayList<>(ids.length);
            Collections.addAll(addSongs,ids);
            playlist.setSongNum(addSongs.size());     //新增下歌曲的数量
            String hql = " from CmwPlaylistSongs where playlist = :playlist";
            Map<String, Object> param = new HashMap<String, Object>(1){{
                put("playlist", id);
            }};
            List<CmwPlaylistSongs> listNowPlaylist= playlistSongsDao.getListByHQLWithNamedParams(hql,param);
            List<String> oldSongs = new ArrayList<>();
            List<String> delSongs = new ArrayList<>();
            for (CmwPlaylistSongs playlistSongs : listNowPlaylist) {
                String songid = playlistSongs.getSongs().toString();
                if (addSongs.contains(songid)) {
                    oldSongs.add(songid);
                } else {
                    //删掉
                    playlistSongsDao.delete(playlistSongs);
                }
            }
            addSongs.removeAll(oldSongs);
            //新增剩下的
            if (!addSongs.isEmpty()) {
                for (String s : addSongs) {
                    CmwPlaylistSongs playlistSongs = new CmwPlaylistSongs();
                    playlistSongs.setSongs(Long.valueOf(s));
                    playlistSongs.setPlaylist(playlist.getId());
                    playlistSongs.setCreator(user.getUserID());
                    playlistSongs.setCreateName(user.getUserName());
                    playlistSongs.setCreateTime(playlist.getCreateTime());
                    playlistSongsDao.save(playlistSongs);
                }
            }
        }else {
            state = 0;
        }
        return state;
    }

    @Override
    public Integer delPlayList(Long id) {
        String hql = "delete from CmwPlaylistSongs where playlist = :playlist";
        Map<String, Object> param = new HashMap<String, Object>(1){{
            put("playlist", id);
        }};
        Integer state = playlistSongsDao.queryHQLWithNamedParams(hql, param);
        CmwPlaylist playlist = this.getPlayListSingle(id);
        playlistDao.delete(playlist);
        return 1;
    }

    @Override
    public Integer removePlayListSong(Long id, Integer playListNum) {
        CmwPlaylistSongs playlistSongs = this.getplayListSongsSingle(id);
        CmwPlaylist cmwPlaylist = this.getPlayListSingle(playlistSongs.getPlaylist());
        Integer state = 1;
        if (playListNum.equals(cmwPlaylist.getSongNum())) {
            cmwPlaylist.setSongNum(cmwPlaylist.getSongNum()-1);
            playlistSongsDao.delete(playlistSongs);
        } else {
            state = 0;   //歌曲数目不一致，出现问题请联系管理员
        }
        return state;
    }

    private CmwPlaylist getPlayListSingle(Long id){
        CmwPlaylist playlist = playlistDao.get(id);
        return playlist;
    }

    private CmwPlaylistSongs getplayListSongsSingle(Long id){
        CmwPlaylistSongs playlistSongs = playlistSongsDao.get(id);
        return playlistSongs;
    }

    //获取“全部”歌单时要对歌曲进行重新包装处理
    private List<PlaylistSongsDto> getPlaylistByAll(User user, String name, PageInfo pageInfo){
        List<CmwSongs> listSongs = lyricService.listSongsPage(user, name, pageInfo);
        List<PlaylistSongsDto> list = new ArrayList<>();
        if (!listSongs.isEmpty()) {
            for (CmwSongs s : listSongs) {
                PlaylistSongsDto song = new PlaylistSongsDto();
                song.setSongs(s.getId());
                song.setName(s.getName());
                song.setCaptionRequired(s.getCaptionRequired());
                song.setOriginalPath(s.getOriginalPath());
                song.setTrackDuration(s.getTrackDuration());
                song.setBgmPath(s.getBgmPath());
                song.setBgmDuration(s.getBgmDuration());
                song.setSongDuration(s.getSongDuration());
                if (song.getCaptionRequired()) {
                    CmwRhythm cmwRhythm = lyricService.cmwRhythm(s.getId(),null,true);
                    song.setRhythm(cmwRhythm.getRhythm());
                }
                list.add(song);
            }
        }
        return list;
    }
}
