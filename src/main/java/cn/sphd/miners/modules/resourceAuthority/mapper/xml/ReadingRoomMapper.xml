<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.resourceAuthority.mapper.ReadingRoomMapper">

    <select id="getSingle" parameterType="readRoom" resultType="readRoom">
        select
        id, org, instance, resource, resource_history AS resourceHistory, resource_forum AS resourceForum, user, view,
        is_viewed AS isViewed, view_time AS viewTime, operate, category, original_category AS originalCategory,
        original_path AS originalPath, creator, create_name AS createName, create_date AS createDate,
        updator, update_name AS updateName, update_date AS updateDate, approve_item AS approveItem,
        approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
        audit_date AS auditDate, operation, apply_memo AS applyMemo, apply_memo AS applyMemo,
        message_id AS messageId
        from t_resource_readingroom
        where id = #{id}
    </select>

    <select id="listPage" parameterType="hashmap" resultType="readRoom">
        select
        id, org, instance, resource, resource_history AS resourceHistory, user, view,
        is_viewed AS isViewed, view_time AS viewTime，operate, category, original_category AS originalCategory,
        original_path AS originalPath, creator, create_name AS createName, create_date AS createDate,
        updator, update_name AS updateName, update_date AS updateDate, approve_item AS approveItem,
        approve_status AS approveStatus, approve_level AS approveLevel, auditor, auditor_name AS auditorName,
        audit_date AS auditDate, operation, apply_memo AS applyMemo, apply_memo AS applyMemo,
        message_id AS messageId
        from t_resource_readingroom
    </select>

    <select id="getApplyReadFile" resultType="readRoom">
        select
        rd.id, rd.org, rd.resource_history AS resourceHistory, rd.user, rd.is_viewed AS isViewed,
        rh.name, rh.file_sn AS fileSn, rh.version, rd.create_name AS createName,
        DATE_FORMAT(rd.create_date,'%Y-%m-%d %H:%i:%s') AS createDate,rh.change_num AS changeNum
        from t_resource_readingroom rd, t_resource_history rh
        where rd.resource_history = rh.id AND rd.creator = #{creator} AND rd.approve_status = #{approveStatus}
        <if test="timeBegin != null" >
            AND DATE(rd.audit_date) BETWEEN #{timeBegin} AND #{timeEnd}
        </if>
        order by
        rd.create_date desc
    </select>

    <select id="getReadFileByApproveStatuslistPage" resultType="readRoom">
        select
        rd.id, rd.org, rd.resource_history AS resourceHistory, rd.user, rd.is_viewed AS isViewed,
        rh.name, rh.file_sn AS fileSn, rh.version, rd.create_name AS createName,
        DATE_FORMAT(rd.create_date,'%Y-%m-%d %H:%i:%s') AS createDate,rh.change_num AS changeNum
        from t_resource_readingroom rd, t_resource_history rh
        where rd.resource_history = rh.id AND rd.creator = #{toUser} AND rd.approve_status = #{approveStatus}
        AND DATE(rd.create_date) BETWEEN #{timeBegin} AND #{timeEnd}
        order by
        rd.create_date desc
    </select>

    <select id="countUnreadFile" resultType="java.lang.Integer">
        select
        COUNT(id) num
        from t_resource_readingroom
        where creator = #{creator} AND approve_status = #{approveStatus} AND is_viewed = 0
        AND resource_history is not null
    </select>

    <select id="getApproveReadFile" resultType="readRoom">
        select
        rd.id, rd.org, rd.resource_history AS resourceHistory, rd.user, rd.is_viewed AS isViewed,
        rh.name, rh.file_sn AS fileSn, rh.version, rd.create_name AS createName,
        DATE_FORMAT(rd.create_date,'%Y-%m-%d %H:%i:%s') AS createDate,rh.change_num AS changeNum
        from t_resource_readingroom rd, t_resource_history rh, t_sys_approval_process ap
        where rd.id = ap.business and rd.resource_history = rh.id AND ap.business_type = 29
        AND ap.to_user = #{toUser} AND ap.approve_status = #{approveStatus} AND ap.to_mid is null
        <if test= 'approveStatus == "2"' >
            AND rd.approve_status = 1
        </if>
        order by
        rd.create_date desc
    </select>

    <select id="countApproveReadFile" resultType="java.lang.Integer">
        select count(rd.id)
        from t_resource_readingroom rd, t_resource_history rh, t_sys_approval_process ap
        where rd.id = ap.business and rd.resource_history = rh.id AND ap.business_type = 29
        AND ap.to_user = #{userId} AND ap.approve_status = 1 AND ap.to_mid is null
    </select>

    <select id="getApproveReadFilelistPage" resultType="readRoom">
        select
        rd.id, rd.org, rd.resource_history AS resourceHistory, rd.user, rd.is_viewed AS isViewed,
        rh.name, rh.file_sn AS fileSn, rh.version, rd.create_name AS createName,
        DATE_FORMAT(rd.create_date,'%Y-%m-%d %H:%i:%s') AS createDate,rh.change_num AS changeNum
        from t_resource_readingroom rd, t_resource_history rh, t_sys_approval_process ap
        where rd.id = ap.business and rd.resource_history = rh.id AND ap.business_type = 29
        AND ap.to_user = #{toUser} AND ap.to_mid is null AND rd.approve_status = #{approveStatus}
        AND DATE(rd.create_date) between #{timeBegin} and #{timeEnd}
        <if test= 'creator != null' >
            AND rd.creator = #{creator}
        </if>
        order by
        rd.create_date desc
    </select>

    <select id="getLastApproveReadFile" resultType="readRoom">
        select
        rd.id, rd.org, rd.resource_history AS resourceHistory, rd.user, rd.is_viewed AS isViewed,
        rh.name, rh.file_sn AS fileSn, rh.version, rd.create_name AS createName,
        DATE_FORMAT(rd.create_date,'%Y-%m-%d %H:%i:%s') AS createDate,rh.change_num AS changeNum
        from t_resource_readingroom rd, t_resource_history rh, t_sys_approval_process ap
        where rd.id = ap.business and rd.resource_history = rh.id AND ap.business_type = 29
        AND ap.to_mid = #{toMid} AND ap.org = #{fromOrg} AND rd.approve_status = 1
        order by
        rd.create_date desc
    </select>

    <select id="countLastApproveReadFile" resultType="java.lang.Integer">
        select count(rd.id)
        from t_resource_readingroom rd, t_resource_history rh, t_sys_approval_process ap
        where rd.id = ap.business and rd.resource_history = rh.id AND ap.business_type = 29
        AND ap.to_mid = #{toMid} AND ap.org = #{fromOrg} AND rd.approve_status = 1
    </select>

    <select id="getLastApproveReadFilelistPage" resultType="readRoom">
        select
        rd.id, rd.org, rd.resource_history AS resourceHistory, rd.user, rd.is_viewed AS isViewed,
        rh.name, rh.file_sn AS fileSn, rh.version, rd.create_name AS createName,
        DATE_FORMAT(rd.create_date,'%Y-%m-%d %H:%i:%s') AS createDate,rh.change_num AS changeNum
        from t_resource_readingroom rd, t_resource_history rh, t_sys_approval_process ap
        where rd.id = ap.business and rd.resource_history = rh.id AND ap.business_type = 29
        AND ap.to_mid = #{toMid} AND ap.org = #{fromOrg} AND rd.approve_status = #{approveStatus}
        AND DATE(rd.create_date) between #{timeBegin} and #{timeEnd}
        <if test= 'creator != null' >
            AND rd.creator = #{creator}
        </if>
        order by
        rd.create_date desc
    </select>

    <select id="readingRoomSingle" resultType="readRoom">
        select
        rd.id, rd.org, rd.resource_history AS resourceHistory, rd.user, rd.is_viewed AS isViewed,
        rh.name, rh.file_sn AS fileSn, rh.version, rd.create_name AS createName,
        DATE_FORMAT(rd.create_date,'%Y-%m-%d %H:%i:%s') AS createDate,rh.change_num AS changeNum
        from t_resource_readingroom rd, t_resource_history rh
        where rd.resource_history = rh.id AND rd.id = #{id}
    </select>

    <select id="getDaysAgoReadFile" resultType="readRoom">
        select
        rd.id, rd.org, rd.resource_history AS resourceHistory, rd.user, rd.is_viewed AS isViewed,
        rh.name, rh.file_sn AS fileSn, rh.version, rd.creator, rd.create_name AS createName,
        DATE_FORMAT(rd.create_date,'%Y-%m-%d %H:%i:%s') AS createDate,rh.change_num AS changeNum
        from t_resource_readingroom rd, t_resource_history rh
        where rd.resource_history = rh.id AND rd.approve_status = 2 and rd.is_viewed = 0
        and DATE(rd.audit_date) &lt; #{timeBegin}
        order by
        rd.create_date desc
    </select>

    <select id="getReadRoomByCategoryPath" resultType="readRoom">
        select
        rd.id, rd.org, rd.resource_history AS resourceHistory, rd.user, rd.is_viewed AS isViewed,
        rh.name, rh.file_sn AS fileSn, rh.version, rd.creator, rd.create_name AS createName,
        DATE_FORMAT(rd.create_date,'%Y-%m-%d %H:%i:%s') AS createDate,rh.change_num AS changeNum,
        rd.approve_status AS approveStatus
        from t_resource_readingroom rd, t_resource_history rh
        where rd.resource_history = rh.id AND rh.category_path like CONCAT(#{categoryPath},'%') and rh.is_trash = #{isTrash}
        AND ((rd.approve_status = 2 AND DATE(rd.audit_date) &gt; #{timeBegin}) OR  rd.approve_status = 1)
        order by
        rd.create_date desc
    </select>

    <select id="getReadRoomByFileId" resultType="readRoom">
        select
        rd.id, rd.org, rd.resource_history AS resourceHistory, rd.user, rd.is_viewed AS isViewed,
        rh.name, rh.file_sn AS fileSn, rh.version, rd.creator, rd.create_name AS createName,
        DATE_FORMAT(rd.create_date,'%Y-%m-%d %H:%i:%s') AS createDate,rh.change_num AS changeNum,
        rd.approve_status AS approveStatus
        from t_resource_readingroom rd, t_resource_history rh
        where rd.resource_history = rh.id AND rh.file = #{file} and rh.is_trash = #{isTrash}
        AND ((rd.approve_status = 2 AND DATE(rd.audit_date) &gt; #{timeBegin}) OR rd.approve_status = 1)
        order by
        rd.create_date desc
    </select>

    <select id="getReadRoomByHisFileId" resultType="readRoom">
        select
        rd.id, rd.org, rd.resource_history AS resourceHistory, rd.user, rd.is_viewed AS isViewed,
        rh.name, rh.file_sn AS fileSn, rh.version, rd.creator, rd.create_name AS createName,
        DATE_FORMAT(rd.create_date,'%Y-%m-%d %H:%i:%s') AS createDate,rh.change_num AS changeNum,
        rd.approve_status AS approveStatus
        from t_resource_readingroom rd, t_resource_history rh
        where rd.resource_history = rh.id AND rd.resource_history = #{hisFileId} AND rd.approve_status != 3
        order by
        rd.create_date desc
    </select>

    <select id="getDaysAgoReadForum" resultType="readRoom">
        select
        rd.id, rd.org, rd.resource_forum AS resourceForum, rd.user, rd.is_viewed AS isViewed,
        f.title, f.participants_num AS participantsNum, rd.create_name AS createName, rd.creator,
        DATE_FORMAT(rd.create_date,'%Y-%m-%d %H:%i:%s') AS createDate,f.is_open AS isOpen,
        f.is_cleaned AS isCleaned, f.clean_time AS cleanTime, f.compere_name AS compereName
        from t_resource_readingroom rd, t_forum_post f
        where rd.resource_forum = f.id AND rd.approve_status = 2 and rd.is_viewed = 0
        and DATE(rd.audit_date) &lt; #{timeBegin}
        order by
        rd.create_date desc
    </select>


    <select id="readingRoomSingleByForum" resultType="readRoom">
        select
        rd.id, rd.org, rd.resource_forum AS resourceForum, rd.user, rd.is_viewed AS isViewed,
        f.title, f.participants_num AS participantsNum, rd.create_name AS createName,
        DATE_FORMAT(rd.create_date,'%Y-%m-%d %H:%i:%s') AS createDate
        from t_resource_readingroom rd, t_forum_post f
        where rd.resource_forum = f.id AND rd.id = #{id}
    </select>

    <select id="getApplyReadForum" resultType="readRoom">
        select
        rd.id, rd.org, rd.resource_forum AS resourceForum, rd.user, rd.is_viewed AS isViewed,
        f.title, f.participants_num AS participantsNum, rd.create_name AS createName,
        DATE_FORMAT(rd.create_date,'%Y-%m-%d %H:%i:%s') AS createDate,f.is_open AS isOpen,
        f.is_cleaned AS isCleaned, f.clean_time AS cleanTime, f.compere_name AS compereName
        from t_resource_readingroom rd, t_forum_post f
        where rd.resource_forum = f.id AND rd.creator = #{creator} AND rd.approve_status = #{approveStatus}
        <if test="timeBegin != null" >
            AND DATE(rd.audit_date) BETWEEN #{timeBegin} AND #{timeEnd}
        </if>
        order by
        rd.create_date desc
    </select>

    <select id="getReadForumByApproveStatuslistPage" resultType="readRoom">
        select
        rd.id, rd.org, rd.resource_forum AS resourceForum, rd.user, rd.is_viewed AS isViewed,
        f.title, f.participants_num AS participantsNum, rd.create_name AS createName,
        DATE_FORMAT(rd.create_date,'%Y-%m-%d %H:%i:%s') AS createDate
        from t_resource_readingroom rd, t_forum_post f
        where rd.resource_forum = f.id AND rd.creator = #{toUser} AND rd.approve_status = #{approveStatus}
        AND DATE(rd.create_date) BETWEEN #{timeBegin} AND #{timeEnd}
        order by
        rd.create_date desc
    </select>

    <select id="countUnreadForum" resultType="java.lang.Integer">
        select
        COUNT(id) num
        from t_resource_readingroom
        where creator = #{creator} AND approve_status = #{approveStatus} AND is_viewed = 0
        AND resource_forum is not null
    </select>

    <select id="getApproveReadForum" resultType="readRoom">
        select
        rd.id, rd.org, rd.resource_forum AS resourceForum, rd.user, rd.is_viewed AS isViewed,
        f.title, f.participants_num AS participantsNum, rd.create_name AS createName,
        DATE_FORMAT(rd.create_date,'%Y-%m-%d %H:%i:%s') AS createDate
        from t_resource_readingroom rd, t_forum_post f, t_sys_approval_process ap
        where rd.id = ap.business and rd.resource_forum = f.id AND ap.business_type = 29
        AND ap.to_user = #{toUser} AND ap.approve_status = #{approveStatus} AND ap.to_mid is null
        <if test= 'approveStatus == "2"' >
            AND rd.approve_status = 1
        </if>
        order by
        rd.create_date desc
    </select>

    <select id="countApproveReadForum" resultType="java.lang.Integer">
        select count(rd.id)
        from t_resource_readingroom rd, t_forum_post f, t_sys_approval_process ap
        where rd.id = ap.business and rd.resource_forum = f.id AND ap.business_type = 29
        AND ap.to_user = #{toUser} AND ap.approve_status = 1 AND ap.to_mid is null
    </select>

    <select id="getApproveReadForumlistPage" resultType="readRoom">
        select
        rd.id, rd.org, rd.resource_forum AS resourceForum, rd.user, rd.is_viewed AS isViewed,
        f.title, f.participants_num AS participantsNum, rd.create_name AS createName,
        DATE_FORMAT(rd.create_date,'%Y-%m-%d %H:%i:%s') AS createDate
        from t_resource_readingroom rd, t_forum_post f, t_sys_approval_process ap
        where rd.id = ap.business and rd.resource_forum = f.id AND ap.business_type = 29
        AND ap.to_user = #{toUser} AND ap.to_mid is null AND rd.approve_status = #{approveStatus}
        AND DATE(rd.create_date) between #{timeBegin} and #{timeEnd}
        <if test= 'creator != null' >
            AND rd.creator = #{creator}
        </if>
        order by
        rd.create_date desc
    </select>

    <select id="getLastApproveReadForum" resultType="readRoom">
        select
        rd.id, rd.org, rd.resource_forum AS resourceForum, rd.user, rd.is_viewed AS isViewed,
        f.title, f.participants_num AS participantsNum, rd.create_name AS createName,
        DATE_FORMAT(rd.create_date,'%Y-%m-%d %H:%i:%s') AS createDate
        from t_resource_readingroom rd, t_forum_post f, t_sys_approval_process ap
        where rd.id = ap.business and rd.resource_forum = f.id AND ap.business_type = 29
        AND ap.to_mid = #{toMid} AND ap.org = #{fromOrg} AND rd.approve_status = 1
        order by
        rd.create_date desc
    </select>

    <select id="countLastApproveReadForum" resultType="java.lang.Integer">
        select count(rd.id)
        from t_resource_readingroom rd, t_forum_post f, t_sys_approval_process ap
        where rd.id = ap.business and rd.resource_forum = f.id AND ap.business_type = 29
        AND ap.to_mid = #{toMid} AND ap.org = #{fromOrg} AND rd.approve_status = 1
    </select>

    <select id="getLastApproveReadForumlistPage" resultType="readRoom">
        select
        rd.id, rd.org, rd.resource_forum AS resourceForum, rd.user, rd.is_viewed AS isViewed,
        f.title, f.participants_num AS participantsNum, rd.create_name AS createName,
        DATE_FORMAT(rd.create_date,'%Y-%m-%d %H:%i:%s') AS createDate
        from t_resource_readingroom rd, t_forum_post f, t_sys_approval_process ap
        where rd.id = ap.business and rd.resource_forum = f.id AND ap.business_type = 29
        AND ap.to_mid = #{toMid} AND ap.org = #{fromOrg} AND rd.approve_status = #{approveStatus}
        AND DATE(rd.create_date) between #{timeBegin} and #{timeEnd}
        <if test= 'creator != null' >
            AND rd.creator = #{creator}
        </if>
        order by
        rd.create_date desc
    </select>

    <delete id="delete" parameterType="readRoom" >
        delete from t_resource_readingroom
        where id = #{id}
    </delete>

    <insert id="insert" useGeneratedKeys="true" keyProperty="id" parameterType="readRoom">
        insert into t_resource_readingroom (org, instance,
        resource, resource_history, resource_forum, user,
        view, is_viewed, operate,
        category, original_category, original_path,
        creator, create_name, create_date,
        updator, update_name, update_date,
        approve_item, approve_status, approve_level,
        auditor, auditor_name, audit_date,
        operation, apply_memo, approve_memo,
        message_id)
        values (#{org}, #{instance},
        #{resource}, #{resourceHistory}, #{resourceForum}, #{user},
        #{view}, #{isViewed}, #{operate},
        #{category}, #{originalCategory}, #{originalPath},
        #{creator}, #{createName}, #{createDate},
        #{updator}, #{updateName}, #{updateDate},
        #{approveItem}, #{approveStatus}, #{approveLevel},
        #{auditor}, #{auditorName}, #{auditDate},
        #{operation}, #{applyMemo}, #{approveMemo},
        #{messageId})
    </insert>

    <update id="update" parameterType="readRoom" >
        update t_resource_readingroom
        <set >
            <if test="org != null" >
                org = #{org},
            </if>
            <if test="instance != null" >
                instance = #{instance},
            </if>
            <if test="resource != null" >
                resource = #{resource},
            </if>
            <if test="resourceHistory != null" >
                resource_history = #{resourceHistory},
            </if>
            <if test="resourceForum != null" >
                resource_forum = #{resourceForum},
            </if>
            <if test="user != null" >
                user = #{user},
            </if>
            <if test="view != null" >
                view = #{view},
            </if>
            <if test="isViewed != null" >
                is_viewed = #{isViewed},
            </if>
            <if test="viewTime != null" >
                view_time = #{viewTime},
            </if>
            <if test="operate != null" >
                operate = #{operate},
            </if>
            <if test="category != null" >
                category = #{category},
            </if>
            <if test="originalCategory != null" >
                original_category = #{originalCategory},
            </if>
            <if test="originalPath != null" >
                original_path = #{originalPath},
            </if>
            <if test="creator != null" >
                creator = #{creator},
            </if>
            <if test="createName != null" >
                create_name = #{createName},
            </if>
            <if test="createDate != null" >
                create_date = #{createDate},
            </if>
            <if test="updator != null" >
                updator = #{updator},
            </if>
            <if test="updateName != null" >
                update_name = #{updateName},
            </if>
            <if test="updateDate != null" >
                update_date = #{updateDate},
            </if>
            <if test="approveItem != null" >
                approve_item = #{approveItem},
            </if>
            <if test="approveStatus != null" >
                approve_status = #{approveStatus},
            </if>
            <if test="approveLevel != null" >
                approve_level = #{approveLevel},
            </if>
            <if test="auditor != null" >
                auditor = #{auditor},
            </if>
            <if test="auditorName != null" >
                auditor_name = #{auditorName},
            </if>
            <if test="auditDate != null" >
                audit_date = #{auditDate},
            </if>
            <if test="operation != null" >
                operation = #{operation},
            </if>
            <if test="applyMemo != null" >
                apply_memo = #{applyMemo},
            </if>
            <if test="approveMemo != null" >
                approve_memo = #{approveMemo},
            </if>
            <if test="messageId != null" >
                message_id = #{messageId},
            </if>
        </set>
        where id = #{id}
    </update>


</mapper>