package cn.sphd.miners.modules.improvement.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by 朱思旭 on 2018/5/21.
 */
@Entity(name="CiBase")
@Table(name = "t_ci_base")
public class TCiBase implements Serializable {

    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    @Column(name = "org")
    private Integer org;
    @Column(name = "project_name")
    private String projectName;
    @Column(name = "category")
    private String category;
    @Column(name = "tag")
    private String tag;
    @Column(name = "code")
    private String code;
    @Column(name = "foundation")
    private String foundation;
    @Column(name = "principal")
    private Integer principal;
    @Column(name = "principal_name")
    private String principalName;
    @Column(name = "member")
    private String member;
    @Column(name = "current_situation")
    private String currentSituation;
    @Column(name = "proposal")
    private String proposal;
    @Column(name = "description")
    private String description;
    @Column(name = "begin_time")
    private Date beginTime;
    @Column(name = "end_time")
    private Date endTime;
    @Column(name = "profit_estimate")
    private String profitEstimate;
    @Column(name = "memo")
    private String memo;
    @Column(name = "state")
    private String state;
    @Column(name = "register_approve_time")
    private Date registerApproveTime;
    @Column(name = "register_approver")
    private Integer registerApprover;
    @Column(name = "register_reject_reason")
    private String registerRejectReason;
    @Column(name = "processor")
    private Integer processor;
    @Column(name = "settle_type")
    private String settleType;
    @Column(name = "settle_opinion")
    private String settleOpinion;
    @Column(name = "settle_time_fact")
    private Date settleTimeFact;
    @Column(name = "settler")
    private Integer settler;
    @Column(name = "settle_time")
    private Date settleTime;
    @Column(name = "settle_reject_reason")
    private String settleRejectReason;
    @Column(name = "settle_approver")
    private Integer settleApprover;
    @Column(name = "settle_approve_time")
    private Date settleApproveTime;
    @Column(name = "initial_receipter")
    private Integer initialReceipter;
    @Column(name = "initial_handler")
    private Integer initialHandler;
    @Column(name = "creator")
    private Integer creator;
    @Column(name = "create_name")
    private String createName;
    @Column(name = "create_time")
    private Date createTime;
    @Column(name = "updator")
    private Integer updator;
    @Column(name = "update_name")
    private String updateName;
    @Column(name = "update_time")
    private Date updateTime;

    @Transient
    private String startTimeFind;//查询开始时间
    @Transient
    private String endTimeFind;//查询结束时间

    @Transient
    private Date settleApproveBegin;
    @Transient
    private Date settleApproveEnd;
    @Transient
    private String  settleName; //结案人名字
    @Transient
    private String factTime;//查询开始时间

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getFoundation() {
        return foundation;
    }

    public void setFoundation(String foundation) {
        this.foundation = foundation;
    }

    public Integer getPrincipal() {
        return principal;
    }

    public void setPrincipal(Integer principal) {
        this.principal = principal;
    }

    public String getPrincipalName() {
        return principalName;
    }

    public void setPrincipalName(String principalName) {
        this.principalName = principalName;
    }

    public String getMember() {
        return member;
    }

    public void setMember(String member) {
        this.member = member;
    }

    public String getCurrentSituation() {
        return currentSituation;
    }

    public void setCurrentSituation(String currentSituation) {
        this.currentSituation = currentSituation;
    }

    public String getProposal() {
        return proposal;
    }

    public void setProposal(String proposal) {
        this.proposal = proposal;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getProfitEstimate() {
        return profitEstimate;
    }

    public void setProfitEstimate(String profitEstimate) {
        this.profitEstimate = profitEstimate;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public Date getRegisterApproveTime() {
        return registerApproveTime;
    }

    public void setRegisterApproveTime(Date registerApproveTime) {
        this.registerApproveTime = registerApproveTime;
    }

    public Integer getRegisterApprover() {
        return registerApprover;
    }

    public void setRegisterApprover(Integer registerApprover) {
        this.registerApprover = registerApprover;
    }

    public String getRegisterRejectReason() {
        return registerRejectReason;
    }

    public void setRegisterRejectReason(String registerRejectReason) {
        this.registerRejectReason = registerRejectReason;
    }

    public Integer getProcessor() {
        return processor;
    }

    public void setProcessor(Integer processor) {
        this.processor = processor;
    }

    public String getSettleType() {
        return settleType;
    }

    public void setSettleType(String settleType) {
        this.settleType = settleType;
    }

    public String getSettleOpinion() {
        return settleOpinion;
    }

    public void setSettleOpinion(String settleOpinion) {
        this.settleOpinion = settleOpinion;
    }

    public Date getSettleTimeFact() {
        return settleTimeFact;
    }

    public void setSettleTimeFact(Date settleTimeFact) {
        this.settleTimeFact = settleTimeFact;
    }

    public Integer getSettler() {
        return settler;
    }

    public void setSettler(Integer settler) {
        this.settler = settler;
    }

    public Date getSettleTime() {
        return settleTime;
    }

    public void setSettleTime(Date settleTime) {
        this.settleTime = settleTime;
    }

    public String getSettleRejectReason() {
        return settleRejectReason;
    }

    public void setSettleRejectReason(String settleRejectReason) {
        this.settleRejectReason = settleRejectReason;
    }

    public Integer getSettleApprover() {
        return settleApprover;
    }

    public void setSettleApprover(Integer settleApprover) {
        this.settleApprover = settleApprover;
    }

    public Date getSettleApproveTime() {
        return settleApproveTime;
    }

    public void setSettleApproveTime(Date settleApproveTime) {
        this.settleApproveTime = settleApproveTime;
    }

    public Integer getInitialReceipter() {
        return initialReceipter;
    }

    public void setInitialReceipter(Integer initialReceipter) {
        this.initialReceipter = initialReceipter;
    }

    public Integer getInitialHandler() {
        return initialHandler;
    }

    public void setInitialHandler(Integer initialHandler) {
        this.initialHandler = initialHandler;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getStartTimeFind() {
        return startTimeFind;
    }

    public void setStartTimeFind(String startTimeFind) {
        this.startTimeFind = startTimeFind;
    }

    public String getEndTimeFind() {
        return endTimeFind;
    }

    public void setEndTimeFind(String endTimeFind) {
        this.endTimeFind = endTimeFind;
    }

    public Date getSettleApproveBegin() {
        return settleApproveBegin;
    }

    public void setSettleApproveBegin(Date settleApproveBegin) {
        this.settleApproveBegin = settleApproveBegin;
    }

    public Date getSettleApproveEnd() {
        return settleApproveEnd;
    }

    public void setSettleApproveEnd(Date settleApproveEnd) {
        this.settleApproveEnd = settleApproveEnd;
    }

    public String getSettleName() {
        return settleName;
    }

    public void setSettleName(String settleName) {
        this.settleName = settleName;
    }

    public String getFactTime() {
        return factTime;
    }

    public void setFactTime(String factTime) {
        this.factTime = factTime;
    }
}
