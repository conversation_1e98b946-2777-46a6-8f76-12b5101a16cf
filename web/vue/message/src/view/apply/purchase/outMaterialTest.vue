<template>
  <div id="outMaterialTest">
    <div>
      <div>以下待检验的物料购于西城区第一五交化商店</div>
      <div>采  购：XXX  XXXX/XX/XX XX:XX:XX</div>
    </div>
    <TY_NavTop title="外购物料入库申请" :isBackHome=false></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待检验" name="1" closable='false'>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in applyHandleCheck" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.title}}</div>
                <div style="margin-left: 20px">采购 {{item.createName}} {{item.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="检验通过" name="2" closable='false'>
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in applyHandleStorage" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.outSn}}</div>
                <div>{{item.outName}}</div>
                <div>申请入库的数量  {{item.num}}只  到货日期 {{item.beginTime | formatDay('YYYY年MM月DD日 dddd')}}</div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="检验不合格" name="3" closable='false' :msgCount="applyHandleNumCount">
        <div class="ui-cells">
          <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in applyHandleNum" v-bind:key="index">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div>{{item.outSn}}</div>
                <div>{{item.outName}}</div>
                <div>申请入库的数量  {{item.num}}只  到货日期 {{item.beginTime | formatDay('YYYY年MM月DD日 dddd')}}</div>
              </div>
            </div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>

<script>
export default {
  name: 'outMaterialTest',
  data () {
    return {
      pid: 0,
      isBackHome: false,
      listenersUid: []
    }
  },
  created: function () {
    // 从路由获取父级菜单id
    this.pid = this.$route.params.pid
  },
  methods: {
    approveTo: function (item) {
      this.$router.push({
        path: '/' + item.code,
        name: item.code,
        params: {
          id: item.id
        }
      })
    }
  }
}
</script>
