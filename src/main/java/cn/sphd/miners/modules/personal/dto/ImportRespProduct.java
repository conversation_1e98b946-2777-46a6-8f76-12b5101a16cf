package cn.sphd.miners.modules.personal.dto;


import cn.sphd.miners.modules.commodity.entity.PdCommodityImport;

import java.math.BigDecimal;
import java.util.List;

public class ImportRespProduct {
    private Integer status;//0失败，1成功
    private Integer type;//=1为通用商品 =2为专属商品
    private Integer customer;//客户id
    private Integer importOption;//导入选项:1-全部,2-开增值税专用发票的,3-开普通发票的,4-不开发票的,5-通用商品,不开增值税专用发票的
    private BigDecimal taxRate;//税率
    private Integer taxInclusive;//是否含税价:1-含税,0不含税
    private Integer isSaled;//是否销售过,1销售过,0未销售过
    private Integer importSum;//导入总数
    private Integer falseImportSum;//无法导入总数
    private List<PdCommodityImport> truePdProductList;//成功导入商品列表
    private List<PdCommodityImport> falsePdProducList;//无法导入商品列表

    public Integer getCustomer() {
        return customer;
    }

    public void setCustomer(Integer customer) {
        this.customer = customer;
    }

    public Integer getImportOption() {
        return importOption;
    }

    public void setImportOption(Integer importOption) {
        this.importOption = importOption;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public Integer getTaxInclusive() {
        return taxInclusive;
    }

    public void setTaxInclusive(Integer taxInclusive) {
        this.taxInclusive = taxInclusive;
    }

    public Integer getIsSaled() {
        return isSaled;
    }

    public void setIsSaled(Integer isSaled) {
        this.isSaled = isSaled;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getImportSum() {
        return importSum;
    }

    public void setImportSum(Integer importSum) {
        this.importSum = importSum;
    }

    public Integer getFalseImportSum() {
        return falseImportSum;
    }

    public void setFalseImportSum(Integer falseImportSum) {
        this.falseImportSum = falseImportSum;
    }

    public List<PdCommodityImport> getTruePdProductList() {
        return truePdProductList;
    }

    public void setTruePdProductList(List<PdCommodityImport> truePdProductList) {
        this.truePdProductList = truePdProductList;
    }

    public List<PdCommodityImport> getFalsePdProducList() {
        return falsePdProducList;
    }

    public void setFalsePdProducList(List<PdCommodityImport> falsePdProducList) {
        this.falsePdProducList = falsePdProducList;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
