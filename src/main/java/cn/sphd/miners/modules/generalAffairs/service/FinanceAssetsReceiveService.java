package cn.sphd.miners.modules.generalAffairs.service;

import cn.sphd.miners.modules.generalAffairs.entity.FinanceAssetsReceive;

import java.util.List;

/**
 * Created by Administrator on 2017/7/11.
 */
public interface FinanceAssetsReceiveService {

    void addFinanceAssetsReceive(FinanceAssetsReceive financeAssetsReceive);

    void updateFinanceAssetsReceive(FinanceAssetsReceive financeAssetsReceive);

    List<FinanceAssetsReceive> getFinanceAssetsReceiveList(Integer aid);

    FinanceAssetsReceive getFinanceAssetsReceive(Integer farId);

}
