package cn.sphd.miners.modules.equipment.controller;

import java.util.List;

import cn.sphd.miners.modules.equipment.entity.TEquModelProduct;
import cn.sphd.miners.modules.equipment.service.ITEquModelProductService;
import cn.sphd.miners.modules.mci.utils.AjaxResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import static cn.sphd.miners.modules.mci.utils.AjaxResult.toAjax;

/**
 * 装备管理_型号产品参与Controller
 * 
 * <AUTHOR>
 * @date 2023-02-10
 */
@Controller
@RequestMapping("/equipment/model/product")
public class EquModelProductController
{
    private String prefix = "system/product";

    @Autowired
    private ITEquModelProductService tEquModelProductService;

    @GetMapping()
    public String product()
    {
        return prefix + "/product";
    }

    /**
     * 查询装备管理_型号产品参与列表
     */
    @PostMapping("/list")
    @ResponseBody
    public List<TEquModelProduct> list(TEquModelProduct tEquModelProduct)
    {
        List<TEquModelProduct> list = tEquModelProductService.selectTEquModelProductList(tEquModelProduct);
        return list;
    }



    /**
     * 新增装备管理_型号产品参与
     */
    @GetMapping("/add")
    public String add()
    {
        return prefix + "/add";
    }

    /**
     * 新增保存装备管理_型号产品参与
     */
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(TEquModelProduct tEquModelProduct)
    {
        return toAjax(tEquModelProductService.insertTEquModelProduct(tEquModelProduct));
    }

    /**
     * 修改装备管理_型号产品参与
     */
    @GetMapping("/edit/{id}")
    public String edit(@PathVariable("id") Long id, ModelMap mmap)
    {
        TEquModelProduct tEquModelProduct = tEquModelProductService.selectTEquModelProductById(id);
        mmap.put("tEquModelProduct", tEquModelProduct);
        return prefix + "/edit";
    }

    /**
     * 修改保存装备管理_型号产品参与
     */
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(TEquModelProduct tEquModelProduct)
    {
        return toAjax(tEquModelProductService.updateTEquModelProduct(tEquModelProduct));
    }

    /**
     * 删除装备管理_型号产品参与
     */
    @PostMapping( "/remove")
    @ResponseBody
    public AjaxResult remove(String ids)
    {
        return toAjax(tEquModelProductService.deleteTEquModelProductByIds(ids));
    }
}
