<template>
  <div id="approveFile" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="文件审批" :isBackHome="isBackHome" isSearch="true" @toggleList="screen"></TY_NavTop>
    <div class="tabs">
      <div class="tabs-bar">
        <router-link :to="{name: 'approveFileIssue'}" tag="div">
          <TY_TabBar label="待处理" :msgCount="$store.state.fileList.fileApprove.length" :bool="$store.getters.getFileNav === 1">
          </TY_TabBar>
        </router-link>
        <router-link :to="{name: 'approvedFileIssue'}" tag="div">
          <TY_TabBar label="已批准" :bool="$store.getters.getFileNav === 2">
          </TY_TabBar>
        </router-link>
      </div>
      <div class="tabs-content">
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'fileApproval',
  data () {
    return {
      loading: true,
      list: this.$store.getters.getFileList,
      isBackHome: true,
      listenersUid: [],
      fileHandle: [],
      fileApprove: []
    }
  },
  created: function () {
    let that = this
    let parames = {
      'userID': that.sphdSocket.user.userID
    }
    // this.$http.post('../../../res/approveFile.do', parames, {
    console.log(window.parent.$.webRoot + '/res/approveFile.do')
    this.$http.post(window.parent.$.webRoot + '/res/approveFile.do', parames, {
      emulateJSON: true
    }).then((response) => {
      let list = response.body.data
      let listArr = {
        'fileHandle': list.resHitoryListApprove,
        'fileApprove': list.resHitoryList // 待处理
      }
      that.fileHandle = listArr['fileHandle']
      that.fileApprove = listArr['fileApprove']
      that.$store.dispatch('setNewFileList', listArr)
      that.loading = false
    }).catch((err) => {
      console.log('获取数据错误了！', err)
    })
    that.setSubscribe()
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    setSubscribe: function () {
      let _this = this
      _this.loading = false
      this.listenersUid = [
        this.sphdSocket.subscribe('approveFile', function (data) { // 申请发布
          let reqData = JSON.parse(data)
          let update = reqData.operate
          let newDate = {}
          if (update > 0) { // 增加一条
            newDate = reqData.resHis
            _this.fileApprove.push(newDate)
          } else if (update < 0) { // 减少一条
            let deletId = reqData.resHis.id
            _this.fileApprove.forEach(function (item, index) {
              if (deletId === item.id) {
                _this.fileApprove.splice(index, 1)
              }
            })
          }
        }, null, 'user'),
        this.sphdSocket.subscribe('approvalsFile', function (data) { // 文件审批进入已批准中
          let reqData = JSON.parse(data)
          let update = reqData.operate
          let newDate = {}
          if (update > 0) { // 增加一条
            newDate = reqData.resHis
            _this.fileHandle.push(newDate)
          } else if (update < 0) { // 减少一条,文件被驳回时用
            let deletId = reqData.resHis.id
            _this.fileHandle.forEach(function (item, index) {
              if (deletId === item.id) {
                _this.fileHandle.splice(index, 1)
              }
            })
          }
        }, null, 'user')
      ]
    },
    screen: function () {
      this.$router.push({
        path: '/approveFileQuery',
        name: 'approveFileQuery',
        params: {
          event: 3
        }
      })
    }
  }
}
</script>
