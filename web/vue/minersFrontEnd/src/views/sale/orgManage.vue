<template>
  <div class="orgManage">
    <div v-show="curPage === 'orgList'" class="main" @click="slDropdownHide">
      <div class="titleMemo">
        <div v-if="orgManageMainData.type === 'searchByPhone'">
          <p><span class="ty-btn ty-btn-big ty-circle-5 ty-btn-blue" @click="backOrgList"><i class="fa fa-chevron-left"></i> 返回</span></p>
          拥有最高权限的手机号码为"{{ orgManageMainData.searchPhone }}"的机构有如下{{ this.orgListData.pageInfo.totalResult }}个，具体如下
        </div>
        <div v-else-if="orgManageMainData.type === 'searchByName'">
          <p><span class="ty-btn ty-btn-big ty-circle-5 ty-btn-blue" @click="backOrgList"><i class="fa fa-chevron-left"></i> 返回</span></p>
          机构名称/机构简称为"{{ orgManageMainData.searchName }}"的机构有如下{{ this.orgListData.pageInfo.totalResult }}个，具体如下
        </div>
        <table style="width: 100%; margin-bottom: 10px;" v-else>
          <tr>
            <td width="300px">
              <el-input placeholder="请录入机构名称或简称" v-model="orgManageMainData.searchName">
                <template #append>
                  <span @click="searchOrgByName" style="cursor: pointer;"><i class="fa fa-search"></i> 搜索</span>
                </template>
              </el-input>
            </td>
            <td width="330px" style="padding-left: 20px">
              <el-input placeholder="请录入拥有最高权限的手机号码" v-model="orgManageMainData.searchPhone">
                <template #append>
                  <span @click="searchOrgByPhone" style="cursor: pointer;"><i class="fa fa-search"></i> 搜索</span>
                </template>
              </el-input>
            </td>
            <td class="btnccc" style="text-align: right">
              <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 " @click="outOfOrgList"><i class="fa fa-unlink"></i> 已暂停服务的机构</span>
            </td>
          </tr>
        </table>
      </div>
      <table class="ty-table ty-table-control">
        <tbody>
        <tr>
          <td>机构名称</td>
          <td>拥有最高权限的手机号码</td>
          <td>超管姓名</td>
          <td>创建人</td>
          <td>操作</td>
        </tr>
        <tr v-for="(item, index) in orgListData.list" :key="index">
          <td>{{ item.name }}</td>
          <td>{{ item.supervisorMobile }}</td>
          <td>{{ item.supervisorName }}</td>
          <td>{{ item.create }}</td>
          <td>
            <span class="ty-color-blue" @click="orgInfo(item)">机构信息</span>
            <span class="ty-color-blue" @click="proInfo(item)">产品信息</span>
            <span class="ty-color-red" @click="stopService(item)">暂停服务</span>
            <div class="sl-dropdown" @mouseout="slDropdownHide" >
              <div :class="dropdownData.showType === 'normal'? 'dropdownCon' : 'dropdownCon2'" v-if="dropdownData.visibleIndex === index"  @mouseover="slDropdownClick(index)">
                <div v-if="item.mpPackageId" class="dropdownItem" ><span @click="addService(item)">增值服务</span></div>
                <div v-else class="disableItem" ><span>增值服务</span></div>

                <div class="dropdownItem"><span @click="loginRecord(item, 'orgList')">登录记录</span></div>
                <div class="dropdownItem"><span @click="resouceManage(item)">空间与流量</span></div>
                <div class="dropdownItem"><span @click="menuReName(item)">菜单重命名</span></div>
                <div class="dropdownItem"><span @click="menuReNameLog(item)">菜单重命名记录</span></div>
              </div>
              <span class="ty-color-blue" @click.stop=" slDropdownClick(index, $event); ">更多</span>
            </div>
          </td>
        </tr>
        </tbody>
      </table>

      <TyPage v-if="orgListData.pageShow"
              :curPage="orgListData.pageInfo.currentPageNo" :pageSize="orgListData.pageInfo.pageSize"
              :allPage="orgListData.pageInfo.totalPage" :pageClickFun="mainPageClickOrg"></TyPage>

    </div>

    <div v-show="curPage === 'outOfOrgList'" class="main dfdfd" @click="slDropdownHide">
      <div class="titleMemo">
        <div v-if="outOfOrgListData.type === 'outSearchByPhone'">
          <span class="ty-btn ty-btn-big ty-circle-5 ty-btn-blue" @click="backOutOrgList"><i class="fa fa-chevron-left"></i> 返回</span><br/>
          拥有最高权限的手机号码为"{{ outOfOrgListData.searchPhone }}"的已暂停服务的机构有如下{{ outOfOrgListData.pageInfo.totalResult }}个，具体如下
        </div>
        <div v-else>
          <table style="width: 100%; margin-bottom: 10px;"  >
            <tr>
              <td width="300px">
                <span class="ty-btn ty-btn-big ty-circle-5 ty-btn-blue" @click="backOrgList"><i class="fa fa-chevron-left"></i> 返回</span>
              </td>
              <td></td>
              <td class="btnccc" style="text-align: right" width="300px">
                <el-input placeholder="请录入拥有最高权限的手机号码" v-model="outOfOrgListData.searchPhone">
                  <template #append>
                    <span @click="searchOutOrgByPhone" style="cursor: pointer;"><i class="fa fa-search"></i> 搜索</span>
                  </template>
                </el-input>
              </td>
            </tr>
          </table>
          已暂停服务的机构有如下{{ outOfOrgListData.pageInfo.totalResult }}个，具体如下
        </div>
      </div>

      <table class="ty-table ty-table-control">
        <tbody>
        <tr>
          <td>机构名称</td>
          <td>拥有最高权限的手机号码</td>
          <td>超管姓名</td>
          <td>创建人</td>
          <td>操作</td>
        </tr>
        <tr v-for="(item, index) in outOfOrgListData.list" :key="index">
          <td>{{ item.name }}</td>
          <td>{{ item.supervisorMobile }}</td>
          <td>{{ item.supervisorName }}</td>
          <td>{{ item.create }}</td>
          <td>
            <span class="ty-color-blue" @click="orgInfo(item)">机构信息</span>
            <span class="ty-color-blue" @click="proInfo(item)">产品信息</span>
            <span class="ty-color-blue" @click="startService(item)">恢复服务</span>
            <div class="sl-dropdown" @mouseout="slDropdownHide" >
              <div :class="dropdownData.showType === 'normal'? 'dropdownCon' : 'dropdownCon2'"  v-if="dropdownData.visibleIndex === index"  @mouseover="slDropdownClick(index)">
                <div class="disableItem"><span>增值服务</span></div>
                <div class="dropdownItem"><span @click="loginRecord(item, 'outOfOrgList')">登录记录</span></div>
                <div class="dropdownItem"><span @click="resouceManage(item)">空间与流量</span></div>
              </div>
              <span class="ty-color-blue" @click.stop=" slDropdownClick(index, $event); ">更多</span>
            </div>
          </td>
        </tr>
        </tbody>
      </table>


      <TyPage v-if="outOfOrgListData.pageShow"
              :curPage="outOfOrgListData.pageInfo.currentPageNo" :pageSize="outOfOrgListData.pageInfo.pageSize"
              :allPage="outOfOrgListData.pageInfo.totalPage" :pageClickFun="mainPageClickOrg2"></TyPage>

    </div>

    <div v-show="curPage === 'loginRecord'">
      <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 " @click="backOrgList2"><i class="fa fa-chevron-left"></i> 返回</span>
      <div style="margin-top:20px; ">
        <div>
          登录记录
          <div class="btnss">
            <el-popover placement="bottom" :width="300" trigger="manual" v-model:visible="orgLoginData.visible">
              <template #reference>
                <span class="ty-btn-group" style="margin:0 10px ">
                  <span class="ty-btn ty-btn-big" :class="orgLoginData.searchType === 3 ? 'ty-btn-blue' : 'ty-btn-gray' "  @click="defaultSearchBtn">自定义查询</span>
                </span>
              </template>
              <div>
                <el-form-item label="起时间：">
                  <el-date-picker v-model="orgLoginData.beginDate" :teleported="false" type="date" placeholder="选择日期">
                  </el-date-picker>
                </el-form-item>
                <el-form-item label="止时间：">
                  <el-date-picker v-model="orgLoginData.endDate" :teleported="false" type="date" placeholder="选择日期">
                  </el-date-picker>
                </el-form-item>
                <div style="text-align: right;">
                  <span class="ty-btn ty-btn-gray ty-btn-big ty-circle-3" :class="orgLoginData.SearchType === 'search' ? 'ty-btn-blue' : 'ty-btn-gray' "  @click="orgLoginData.visible = false">取消</span>
                  <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3" @click="searchType(3)">确定</span>
                </div>
              </div>
            </el-popover>
            <span class="ty-btn-group" style="margin:0 10px ">
              <span class="ty-btn ty-btn-big" :class="orgLoginData.searchType === 1 ? 'ty-btn-blue' : 'ty-btn-gray' "  @click="searchType(1)">本月</span>
              <span class="ty-btn ty-btn-big" :class="orgLoginData.searchType === 2 ? 'ty-btn-blue' : 'ty-btn-gray' "  style="border-left:1px solid #eee; " @click="searchType(2)">本年</span>
            </span>
          </div>

        </div>
        <div style="margin-top: 20px;">
          机构名称：{{ orgLoginData.logData.name   }}
          <span style="margin-left:100px; ">创建人：{{ orgLoginData.creatInfo }}  </span>
        </div>
      </div>
      <div class="orgList">
        <el-table stripe class="ty-table-control" :data="orgLoginData.logData.statisticsList" height="700" border >
          <el-table-column prop="dateFormat" :label="orgLoginData.td1Txt" ></el-table-column>
          <el-table-column prop="sumUser" label="登录人数" > </el-table-column>
          <el-table-column prop="sumDuration" label="登录总时长"> </el-table-column>
          <el-table-column prop="sumLogin" label="登录总次数" ></el-table-column>
          <el-table-column prop="sumPc" label="电脑端登录总次数" > </el-table-column>
          <el-table-column prop="sumApp" label="手机端登录总次数" > </el-table-column>
        </el-table>
      </div>
    </div>

    <!--  弹窗的高低顺序 是按照代码的前后顺序   -->
    <div>

      <!-- 菜单重命名 -->
      <TyDialog v-show="menuReNameData.visible" dialogTitle="菜单重命名" color="blue" :dialogHide="hideMR">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideMR">关闭</span>
          <span class="ty-btn  ty-btn-big ty-circle-5" :class="mrOKBtnOrNot ? 'bounce-ok' : 'bounce-cancel'" @click="mrOKBtn">确定</span>
        </template>
        <template #dialogBody>
          <div>
            是否有需要重命名的菜单？
            <el-radio v-model="menuReNameData.needReName" label="1">有</el-radio>
            <el-radio v-model="menuReNameData.needReName" label="2">没有</el-radio>
          </div>
        </template>
      </TyDialog>

      <!-- 重命名管理 -->
      <TyDialog v-if="menuReNameData.visible2" dialogTitle="重命名管理" color="blue" :dialogHide="hideMR2" width="600">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideMR2">关闭</span>
          <span class="ty-btn  ty-btn-big ty-circle-5 bounce-ok" @click="mrOKBtn2">确定</span>
        </template>
        <template #dialogBody>
          <div>
            <p style="line-height: 40px">已重命名模块的数量 {{ menuReNameData.reNameList.length || 0 }} 个</p>
            <table class="ty-table ty-table-control">
              <tbody>
              <tr>
                <td>原名称</td>
                <td>新名称</td>
                <td>操作</td>
              </tr>
              <tr v-for="(menuItem , index) in menuReNameData.reNameList" :key="index">
                <td v-html="menuItem.name"></td>
                <td v-html="menuItem.rename"></td>
                <td><span class="ty-color-blue" @click="reReNameBtn(menuItem, index)">再次重命名</span></td>
              </tr>
              </tbody>

            </table>
            <div style="height: 40px"></div>
            <TyMenuTree :list="menuReNameData.list" :tdClickFun="reNameClick"></TyMenuTree>
          </div>
        </template>
      </TyDialog>

      <!-- 模块重命名 -->
      <TyDialog v-show="menuReNameData.visible3" dialogTitle="模块重命名" color="blue" :dialogHide="hideMR3">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideMR3">关闭</span>
          <span class="ty-btn  ty-btn-big ty-circle-5 bounce-ok" @click="reNameOK">确定</span>
        </template>
        <template #dialogBody>
          <div>
            <el-form-item label="原名称">
              <el-input v-model="menuReNameData.oldMDName" :disabled="true"></el-input>
            </el-form-item>
            <el-form-item label="新名称">
              <el-input v-model="menuReNameData.newMDName"></el-input>
            </el-form-item>

          </div>
        </template>
      </TyDialog>

      <!-- 菜单重命名记录 -->
      <TyDialog v-if="menuReNameLogData.visible" dialogTitle="菜单重命名记录" color="blue" :dialogHide="hideMRLog" width="600">
        <template #dialogFooter>
          <span class="ty-btn  ty-btn-big ty-circle-5 bounce-ok" @click="hideMRLog">关闭</span>
        </template>
        <template #dialogBody>
          <div>
            <p style="line-height: 40px">重命名过的菜单如下：</p>
            <table class="ty-table ty-table-control">
              <tbody>
              <tr>
                <td>菜单初始名称</td>
                <td>菜单当前名称</td>
                <td>最后操作人</td>
                <td>操作记录</td>
              </tr>
              <tr v-for="(item , index) in menuReNameLogData.list" :key="index">
                <td v-html="item.initialName"></td>
                <td v-html="item.newName"></td>
                <td v-html="item.create"></td>
                <td><span class="ty-color-blue" @click="rnDetails(item, index)">查看</span></td>
              </tr>
              </tbody>

            </table>

          </div>
        </template>
      </TyDialog>


      <!-- 菜单重命名记录 -->
      <TyDialog v-if="menuReNameLogData.visible2" dialogTitle="菜单重命名记录" color="blue" :dialogHide="hideMRLog2" width="600">
        <template #dialogFooter>
          <span class="ty-btn  ty-btn-big ty-circle-5 bounce-ok" @click="hideMRLog2">关闭</span>
        </template>
        <template #dialogBody>
          <div>

            <p style="line-height: 40px">初始名称为 <span class="ty-color-blue">{{ menuReNameLogData.historyInfo.initialName }}</span>，当前名称为 <span class="ty-color-blue">{{ menuReNameLogData.historyInfo.currentName }}</span> 菜单的重命名记录</p>
            <table class="ty-table ty-table-control">
              <tbody>
              <tr>
                <td>重命名的操作</td>
                <td>重命名后菜单的名称</td>
                <td>重命名的类别</td>
              </tr>
              <tr v-for="(item , index) in menuReNameLogData.historyInfo.historyList" :key="index">
                <td v-html="item.create"></td>
                <td v-html="item.newName"></td>
                <td v-html="item.memo"></td>
              </tr>
              </tbody>

            </table>

          </div>
        </template>
      </TyDialog>




      <!-- 恢复服务 -->
      <TyDialog v-if="stopServiceData.visible2" dialogTitle="！！提示" color="blue" :dialogHide="hideSSD2">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideSSD2">关闭</span>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="SSDok2">确定</span>
        </template>
        <template #dialogBody>
          <div style="line-height:40px; text-align: center;  ">
            确定恢复向该客户提供服务吗？
          </div>
        </template>
      </TyDialog>


      <!-- 暂停服务 -->
      <TyDialog v-if="stopServiceData.visible" dialogTitle="！！提示" color="red" :dialogHide="hideSSD">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideSSD">关闭</span>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="SSDok">确定</span>
        </template>
        <template #dialogBody>
          <div style="line-height:40px; text-align: center;  ">
            机构如被暂停服务，其成员将无法登录。 <br>
            确定继续操作吗?
          </div>
        </template>
      </TyDialog>


      <!-- 空间与流量 -->
      <TyDialog v-if="resourceManageData.visible" width="600" dialogTitle="空间与流量" color="blue" :dialogHide="hideRM">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideRM">关闭</span>
        </template>
        <template #dialogBody>
          <div class="rMMsg">
            <div><span class="ttl">机构名称</span><span class="con">{{ resourceManageData.info.fullName || ' --' }}</span></div>
            <div><span class="ttl">机构简称</span><span class="con">{{ resourceManageData.info.name || ' --' }}</span></div>
            <div class="hr"></div>
            <div><span class="ttl">空间上限</span><span class="con">{{  resourceManageData.info.ratedSpace || ' --' }}</span></div>
            <div><span class="ttl">已用空间</span><span class="con">{{  resourceManageData.info.usedSpace || ' --' }}</span></div>
            <div class="hr"></div>
            <div>{{ resourceManageData.info.beginDate ? new Date(resourceManageData.info.beginDate).format("yyyy年MM月dd日") : ' -- ' }} -
              {{ resourceManageData.info.endDate ? new Date(resourceManageData.info.endDate).format("yyyy年MM月dd日") : ' --' }}</div>
            <div><span class="ttl">期间的流量上限</span><span class="con">{{ resourceManageData.info.ratedTraffic || ' --' }}</span></div>
            <div><span class="ttl">此期间已用流量</span><span class="con">{{ resourceManageData.info.usedTraffic ||  ' --' }}</span></div>
            <div class="hr"></div>
          </div>
        </template>
      </TyDialog>


      <!-- 机构信息 -->
      <TyDialog v-if="orgInfoData.visible" width="600" dialogTitle="机构信息" color="blue" :dialogHide="hideOrgInfo">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideOrgInfo">关闭</span>
        </template>
        <template #dialogBody>
          <div class="orgEdit">
            <div style="text-align: right">
              <span class="ty-linkBtn" @click="updateOrg">修改机构信息</span>
              <span class="ty-linkBtn" @click="getOrgHisList">机构信息变动记录</span>
            </div>
            <div style="text-align: right">
              <span class="ty-linkBtn" @click="getOrgCtrlList">暂停服务/恢复服务的操作记录</span>
            </div>
            <table>
              <tbody>
              <tr> <td>机构名称</td><td style="width: 270px;" v-html="orgInfoData.info.fullName"></td></tr>
              <tr> <td>机构简称</td> <td v-html="orgInfoData.info.name"></td></tr>
              <tr> <td>经营地址</td> <td v-html="orgInfoData.info.address"></td></tr>
              <tr> <td>超管姓名</td> <td v-html="orgInfoData.info.supervisorName"></td></tr>
              <tr> <td>超管手机</td> <td v-html="orgInfoData.info.supervisorMobile"></td></tr>
              <tr> <td>数据存储地点</td><td v-html="orgInfoData.info.uploadStorageType"></td></tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>


      <!-- 暂停服务/恢复服务的操作记录 -->
      <TyDialog v-if="orgEditLogData.visible" width="600" dialogTitle="暂停服务/恢复服务的操作记录" color="blue" :dialogHide="hideOrgEditLog">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideOrgEditLog">关闭</span>
        </template>
        <template #dialogBody>
          <div>
            <table class="ty-table">
              <tbody>
              <tr>
                <td>操作性质</td>
                <td>操作者</td>
              </tr>
              <tr v-if="orgEditLogData.list.length > 0" v-for="(item,index) in orgEditLogData.list">
                <td>{{ item.operationNature }}</td>
                <td>{{ item.timeStr }}</td>
              </tr>
              <tr v-else>
                <td colspan="2">暂无数据</td>
              </tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>


      <!-- 机构信息变动记录 -->
      <TyDialog v-if="orgInfoHisData.visible" width="600" dialogTitle="机构信息变动记录" color="blue" :dialogHide="hideOrgInfoHis">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideOrgInfoHis">关闭</span>
        </template>
        <template #dialogBody>
          <div>
            <table class="ty-table">
              <tbody>
              <tr>
                <td>资料状态</td>
                <td>操作</td>
                <td>创建人/修改人</td>
              </tr>
              <tr v-if="orgInfoHisData.list.length >0" v-for="(item , indexI) in orgInfoHisData.list" :key="indexI">
                <td>{{ item.dataState }}</td>
                <td class="ty-td-control"><span class="ty-color-blue" @click="orgHisDetails(item, indexI)">查看</span></td>
                <td></td>
              </tr>
              <tr v-else><td colspan="3"> 暂无数据 </td></tr>
              </tbody>
            </table>

            <TyPage v-if="orgInfoHisData.pageShow"
                    :curPage="orgInfoHisData.pageInfo.currentPageNo" :pageSize="orgInfoHisData.pageInfo.pageSize"
                    :allPage="orgInfoHisData.pageInfo.totalPage" :pageClickFun="orgHisClick"></TyPage>



          </div>
        </template>
      </TyDialog>


      <!-- 机构信息变动记录 详情 -->
      <TyDialog v-if="orgHisInfoData.visible" width="600" dialogTitle="机构信息查看" color="blue" :dialogHide="hideOrgHisInfo">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideOrgHisInfo">关闭</span>
        </template>
        <template #dialogBody>
          <div class="orgEdit">
            <table>
              <tbody>
              <tr> <td>机构名称</td><td style="width: 270px;" v-html="orgHisInfoData.info.fullName"></td></tr>
              <tr> <td>机构简称</td> <td v-html="orgHisInfoData.info.name"></td></tr>
              <tr> <td>经营地址</td> <td v-html="orgHisInfoData.info.address"></td></tr>
              <tr> <td>超管姓名</td> <td v-html="orgHisInfoData.info.supervisorName"></td></tr>
              <tr> <td>超管手机</td> <td v-html="orgHisInfoData.info.supervisorMobile"></td></tr>
              <tr> <td>数据存储地点</td><td v-html="orgHisInfoData.info.uploadStorageType"></td></tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>


      <!-- 产品信息 -->
      <TyDialog v-if="proInfoData.visible" width="600" dialogTitle="产品信息" color="blue" :dialogHide="hideProInfo">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideProInfo">关闭</span>
        </template>
        <template #dialogBody>
          <div class="orgEdit">
            <table>
              <tbody>
              <tr> <td>机构名称</td><td style="width: 270px;" v-html="proInfoData.info.fullName"></td></tr>
              <tr> <td>机构简称</td> <td v-html="proInfoData.info.name"></td></tr>
              <tr> <td>经营地址</td> <td v-html="proInfoData.info.address"></td></tr>
              <tr> <td>超管姓名</td> <td v-html="proInfoData.info.supervisorName"></td></tr>
              <tr> <td>超管手机</td> <td v-html="proInfoData.info.supervisorMobile"></td></tr>
              <tr> <td>数据存储地点</td><td v-html="proInfoData.info.uploadStorageType"></td></tr>
              <tr> <td>所选产品</td><td v-html="proInfoData.info.mpPackageName"></td></tr>
              <tr> <td>所选产品修改记录</td><td>
                <span class="ty-linkBtn" @click="updatePro">修改所选产品</span> &nbsp;&nbsp;&nbsp;
                <span class="ty-linkBtn" @click="scanSelectPackage(proInfoData.info.mpPackageId)">查看所选产品</span>
              </td></tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>



      <!-- 新增机构 / 修改机构 / 修改产品信息 -->
      <TyDialog v-show="orgEditData.visible" width="600" :dialogTitle="orgEditData.title" color="blue" :dialogHide="hideOrgEdit">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideOrgEdit">取消</span>
          <span class="ty-btn  ty-btn-big ty-circle-5" :class="orgEditOKBtn ? 'bounce-ok' : 'bounce-cancel'" @click="orgEditOK">确定</span>
        </template>
        <template #dialogBody>
          <div class="orgEdit">
            <table>
              <tbody>
              <tr> <td>机构名称<i class="ty-color-red">*</i></td> <td style="width: 270px;"><el-input v-model="orgEditData.fullName" placeholder="请输入内容" :disabled="orgEditData.type === 'update' || orgEditData.type === 'updatepro' "></el-input></td></tr>
              <tr> <td>机构简称<i class="ty-color-red">*</i></td> <td><el-input v-model="orgEditData.name" placeholder="请输入内容" :disabled="orgEditData.type === 'update'|| orgEditData.type === 'updatepro' "></el-input></td></tr>
              <tr> <td>经营地址</td> <td><el-input v-model="orgEditData.address" placeholder="请输入内容" :disabled="orgEditData.type === 'update'|| orgEditData.type === 'updatepro' "></el-input></td></tr>
              <tr> <td>超管姓名</td> <td><el-input v-model="orgEditData.supervisorName" placeholder="请输入内容" :disabled="orgEditData.type === 'update'|| orgEditData.type === 'updatepro' "></el-input></td></tr>
              <tr> <td>超管手机<i class="ty-color-red">*</i></td> <td><el-input v-model="orgEditData.supervisorMobile" placeholder="请输入内容" :disabled="orgEditData.type === 'update'|| orgEditData.type === 'updatepro' "></el-input></td></tr>
              <tr> <td>数据存储地点<i class="ty-color-red">*</i></td> <td>
                <el-select v-model="orgEditData.uploadStorageType" placeholder="请选择" :disabled="orgEditData.type === 'updatepro' ">
                  <el-option label="请选择" value=""></el-option>
                  <el-option label="NFS" value="NFS"></el-option>
                  <el-option label="Seafile" value="Seafile" disabled></el-option>
                </el-select>
              </td></tr>
              <tr v-show="orgEditData.type === 'add' || orgEditData.type === 'updatepro'"> <td>所选产品<i class="ty-color-red">*</i></td> <td>
                <el-select v-model="orgEditData.packageId" placeholder="请选择">
                  <el-option
                      v-for="item in orgEditData.packageList"
                      :key="item.id" :label="item.name" :value="item.id" >
                  </el-option>
                </el-select>
              </td></tr>
              <tr v-if="orgEditData.type != 'update'"><td></td><td><span class="ty-linkBtn" @click="scanSelectPackage(orgEditData.packageId)">查看所选产品</span></td></tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>


      <!-- 增值服务 -->
      <TyDialog v-if="addServiceData.visible" width="600" dialogTitle="增值服务" color="blue" :dialogHide="hideService">
        <template #dialogFooter>
          <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideService">取消</span>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="packageSubmit">提交</span>
        </template>
        <template #dialogBody>
          <div class="addService">
            <el-form-item label="机构名称">
              <el-input v-model="addServiceData.info.orgInfo.fullName" disabled></el-input>
            </el-form-item>
            <el-form-item label="机构简称">
              <el-input v-model="addServiceData.info.orgInfo.name" disabled></el-input>
            </el-form-item>
            <el-form-item label="所用产品">
              <el-input v-model="addServiceData.info.mpPackageInfo.name" disabled></el-input>
            </el-form-item>
            <el-form-item label="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;">
              <span class="ty-linkBtn" @click="seeModule(addServiceData.info.mpPackageInfo)">查看所选产品</span>
            </el-form-item>

            <div class="radioB">
              <span @click="toggleType(1)"><i class="fa" :class="addServiceData.type === 1 ? 'fa-dot-circle-o':'fa-circle-o'"></i>作为该产品的增值服务，仅从模块的角度勾选</span>
            </div>
            <el-table :data="addServiceData.info.increaseModuleList" border style="width: 100%">
              <el-table-column label="" width="50">
                <template #default="scope1">
                  <el-checkbox v-model="addServiceData.tab1" :label="scope1.row.id">&nbsp;</el-checkbox>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="模块名称" width="180">
              </el-table-column>
              <el-table-column prop="name" label="下辖的一级菜单数量" width="180">
                <template #default="scope2">
                  {{ scope2.row.topMenu || '--' }} 个
                </template>
              </el-table-column>
              <el-table-column prop="address" label="操作">
                <template #default="scope">
                  <div>
                    <span class="ty-color-blue" @click="seeModule(scope.row)">查看</span>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <div class="radioB">
              <span @click="toggleType(2)"><i class="fa" :class="addServiceData.type === 2 ? 'fa-dot-circle-o':'fa-circle-o'"></i>作为该产品的增值服务，仅从模块的角度勾选</span>
            </div>
            <el-table :data="addServiceData.info.mpSetList" border style="width: 100%">
              <el-table-column label="" width="50">
                <template #default="scope1">
                  <el-radio v-model="addServiceData.tab2" :label="scope1.row.id">&nbsp;</el-radio>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="模块名称" width="180">
              </el-table-column>
              <el-table-column prop="name" label="下辖的一级菜单数量" width="180">
                <template #default="scope2">
                  {{ scope2.row.topMenu || '--' }} 个
                </template>
              </el-table-column>
              <el-table-column prop="address" label="操作">
                <template #default="scope">
                  <div>
                    <span class="ty-color-blue" @click="seeMenu(scope.row)">查看</span>
                  </div>
                </template>
              </el-table-column>
            </el-table>

          </div>
        </template>
      </TyDialog>

      <!-- seeSetMenu -->
      <TyDialog v-if="seeSetMenuData.visible" width="600" dialogTitle="套餐查看" color="blue" :dialogHide="hideSeeSetMenu">
        <template #dialogFooter>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="hideSeeSetMenu">关闭</span>
        </template>
        <template #dialogBody>
          <div class="seeSetMenu">
            <h4><span class="setMenuName">{{ seeSetMenuData.setMenuName }}</span></h4>
            <div class="ty-alert">
              组成本套餐的模块
              <div class="ty-right">
                创建 <span class="create">{{ seeSetMenuData.create }}</span>
              </div>
            </div>
            <table class="ty-table ty-table-control">
              <tbody>
              <tr><td>模块名称</td>
                <td>下辖一级菜单数量</td>
                <td>操作</td></tr>
              <tr v-for="(iitem , iiIndex) in seeSetMenuData.list" :key="iiIndex">
                <td>{{ iitem.name }}</td>
                <td>{{ iitem.topMenu || '' }}</td>
                <td>
                  <span class="ty-color-blue" @click="seeModule(iitem)">查看</span>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </template>
      </TyDialog>

      <!-- 产品查看 -->
      <TyDialog v-if="packageData.visible" width="600" dialogTitle="产品查看" color="blue" :dialogHide="hidePackage">
        <template #dialogFooter>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="hidePackage">关闭</span>
        </template>
        <template #dialogBody>
          <div class="packageInfo">
            <div class="packageName"> {{ packageData.info.mpPackages && packageData.info.mpPackages.name }} </div>
            <div>
              <div class="smallT">
                所选模板： <span class="pName">{{ packageData.info.mpTmpl && packageData.info.mpTmpl.name }}</span>
                <span class="ty-right">操作人：{{ packageData.info.mpPackages.createName }} {{ new Date(packageData.info.mpPackages.createDate).format('yyyy-MM-dd hh:mm:ss') }}</span>
              </div>

              <div class="marT39">已重命名模块的数量 {{ packageData.info.reNameList.length }} 个</div>
              <el-table :data="packageData.info.reNameList || []" border style="width:360px">
                <el-table-column prop="name" label="原名称" width="180"> </el-table-column>
                <el-table-column prop="newName" label="新名称" width="180"> </el-table-column>
              </el-table>
              <div class="marT39">主套餐内的模块</div>
              <el-table :data="packageData.info.mainModuleList || []" border>
                <el-table-column prop="name" label="模块名称" width="180"> </el-table-column>
                <el-table-column label="下辖的一级菜单数量" width="180">
                  <template #default="scope1" class="ty-td-control"> {{ scope1.row.topMenu || '' }}个 </template>
                </el-table-column>
                <el-table-column label="操作">
                  <template #default="scope" >
                    <div class="ty-td-control"><span class="ty-color-blue" @click="seeModule(scope.row)">查看</span></div>
                  </template>
                </el-table-column>
              </el-table>
              <div class="marT39">使用本模板的用户增值功能的模块</div>
              <el-table :data="packageData.info.increaseModuleList || []" border>
                <el-table-column prop="name" label="模块名称" width="180"> </el-table-column>
                <el-table-column label="下辖的一级菜单数量" width="180">
                  <template #default="scope1" class="ty-td-control"> {{ scope1.row.topMenu || '' }}个 </template>
                </el-table-column>
                <el-table-column label="操作">
                  <template #default="scope" >
                    <div class="ty-td-control"><span class="ty-color-blue" @click="seeModule(scope.row)">查看</span></div>
                  </template>
                </el-table-column>
              </el-table>
              <div class="marT39">
                与本模板增值功能模块对应的已有套餐
                <span class="ty-right ty-linkBtn ccc" @click="packageList"> 查 看 </span>
              </div>
            </div>
          </div>
        </template>
      </TyDialog>


      <!-- 模块查看  -->
      <TyDialog v-if="proScanData.visible" width="800" dialogTitle="模块查看" color="blue" :dialogHide="hideProScan">
        <template #dialogFooter>
          <span class="ty-btn ty-btn-big ty-circle-5 bounce-ok" @click="hideProScan">关闭</span>
        </template>
        <template #dialogBody>
          <div>
            <div class="bdTtl">{{ proScanData.name }}</div>
            <TyMenuTree :list="proScanData.list"></TyMenuTree>
          </div>
        </template>
      </TyDialog>

      <!-- 套餐清单查看 -->
      <TyDialog v-if="packageListData.visible" width="600" dialogTitle="套餐清单查看" color="blue" :dialogHide="hidePackageList">
        <template #dialogFooter>
          <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="hidePackageList">关闭</span>
        </template>
        <template #dialogBody>
          <div class="packageList">
            <div class="tip">本模板的用户不可选择的模块数为A，设系统内已有套餐数为B 需根据A、B，算出系统内不包含A的已有套餐数C，并需列出C的清单 机构可选择的套餐，即为上述C的清单，具体如下：</div>
          </div>
        </template>
      </TyDialog>




    </div>

  </div>
</template>

<script>
import customerManageJs from '@/mixins/customerManage.js'
import { beforeRouteLeave, initNav } from "@/utils/routeChange"
import * as customerApi from "@/api/customer.js"

export default {
  mixins: [customerManageJs],
  data() {
    return {
      pageName:'orgManage',
      orgManageMainData:{
        searchName:'',
        searchPhone:'',
        type:'', // 默认空字符串， searchByPhone - 按照手机号搜； searchByName-按照机构名搜
      },
      outOfOrgListData:{
        list:[] ,
        pageShow:true,
        pageInfo:{},
        type:'',
        searchPhone:''
      },
      menuReNameData: {
        visible:false,
        visible2:false,
        visible3:false,
        needReName:'',
        editOrg: null,
        list:[],
        editReNameObj:null,
        oldMDName:'',
        newMDName:'',
        reType:'',
        reNameList:[],
      },
      menuReNameLogData:{
        visible: false ,
        visible2: false ,
        editOrg: null ,
        historyInfo: {} ,
        list:[],
      },

    }
  },
  created(){
    initNav({mid: 'qz', name: '机构管理', pageName: this.pageName}, this.getListOrg, this)
  },
  computed: {
    mrOKBtnOrNot() {
      let status = false
      if(this.menuReNameData.needReName){
        status = true
      }
      return status
    },
  },
  methods:{
    hideMR(){
      this.menuReNameData.visible = false
    },
    menuReName(item){
      // 菜单重命名
      this.menuReNameData.visible = true
      this.menuReNameData.needReName = ''
      this.menuReNameData.editOrg = item

    },
    hideMR2(){
      this.menuReNameData.visible2 = false
    },
    hideMR3(){
      this.menuReNameData.visible3 = false
    },
    mrOKBtn(){
      if(this.mrOKBtnOrNot){
        this.menuReNameData.visible = false
        if(this.menuReNameData.needReName == 1){ // 有重命名
          let id = this.menuReNameData.editOrg.mOrgId
          customerApi.getSelectPopedomsByOid({ oid: id }).then(res1=>{
            let res = res1.data.data
            this.menuReNameData.list = res.popedomList
            let list = []
            res.reNameList.forEach(item=>{
              item.rename = item.newName
            })
            this.menuReNameData.reNameList = res.reNameList
            console.log('list=', res)
            this.menuReNameData.visible2 = true


          }).catch(err=>{
            console.log('err=', err)
            this.$message.error('链接错误，请重试')
          })
        }else if(this.menuReNameData.needReName == 2){ // 没有重命名

        }

      }
    },
    mrOKBtn2(){
      // 确定重命名
      let midRenames = []
      let oid = this.menuReNameData.editOrg.mOrgId

      this.menuReNameData.reNameList.forEach(menuI=>{
        let item = {mid:menuI.mid  , newName: menuI.rename }
        midRenames.push(item)
      })
      customerApi.orgMidRenames({ oid: oid, midRenames:JSON.stringify(midRenames) }).then(res1=>{
        let res = res1.data
        let data = res.data
        let success = res.success
        if(success === 1){
          this.$message.success(data)
        }else{
          this.$message.error(data)
        }
        this.menuReNameData.visible2 = false



      }).catch(err=>{
        console.log('err=', err)
        this.$message.error('链接错误')
      })

    },
    reNameClick(menuItem, level){
      // menuItem 菜单详情， level：等级
      menuItem.level = level
      this.menuReNameData.visible3 = true
      this.menuReNameData.oldMDName = menuItem.name
      this.menuReNameData.newMDName = ''
      this.menuReNameData.reType = 'add'
      this.menuReNameData.editReNameObj = JSON.parse(JSON.stringify(menuItem))
    },
    reReNameBtn(menuItem,index){
      menuItem.index = index
      this.menuReNameData.visible3 = true
      this.menuReNameData.reType = 'update'
      this.menuReNameData.oldMDName = menuItem.name
      this.menuReNameData.newMDName = menuItem.rename
      this.menuReNameData.editReNameObj = JSON.parse(JSON.stringify(menuItem))
    },
    reNameOK(){
      // 重命名确定
      this.menuReNameData.editReNameObj.rename = this.menuReNameData.newMDName
      if(this.menuReNameData.reType === 'update'){
        this.menuReNameData.reNameList[this.menuReNameData.editReNameObj.index] = JSON.parse(JSON.stringify(this.menuReNameData.editReNameObj))
      }else{
        this.menuReNameData.reNameList.push(JSON.parse(JSON.stringify(this.menuReNameData.editReNameObj)) )
      }
      this.menuReNameData.visible3 = false

    },
    hideMRLog(){
      this.menuReNameLogData.visible = false
    },
    menuReNameLog(item){
      // 菜单重命名记录
      this.menuReNameLogData.visible = true
      this.menuReNameLogData.editOrg = item
      let oid = item.mOrgId
      customerApi.getOrgRenameHistories({ oid : oid }).then(res1=>{
        let res = res1.data.data
        let list = []
        res.forEach(item=>{
          item.create = ( item.createName||'--' ) + ' ' + (new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss'))
        })
        this.menuReNameLogData.list = res


      }).catch(err=>{
        console.log('err=', err)
        this.$message.error('链接失败！')
      })
    },
    rnDetails(item, index){
      let oid = this.menuReNameLogData.editOrg.mOrgId
      let mid = item.mid
      customerApi.getOrgRenameHistoryInfo({ oid:oid , mid:mid  }).then(res1=>{
        let res = res1.data.data
        res.historyList.forEach(item=>{
          item.create = (item.createName || '') + (new Date(item.createDate).format('yyyy-MM-dd hh:mm:ss'))
        })
        this.menuReNameLogData.historyInfo = res
        this.menuReNameLogData.visible2 = true


      }).catch(err=>{
        console.log('err=',err)
        this.$message.error('链接失败')
      })
    },
    hideMRLog2(){
      this.menuReNameLogData.visible2 = false
    },
    backOrgList(){
      this.orgManageMainData.searchName = ''
      this.orgManageMainData.searchPhone = ''
      this.getOrgManageListFun({ pageSize: this.pageSize , currentPageNo:1 })
    },
    searchOrgByPhone(){
      let params = { 'mobile': this.orgManageMainData.searchPhone , 'currentPageNo': 1 , 'pageSize': this.pageSize  }
      this.getOrgManageListFun(params,'searchByPhone')
    },
    searchOrgByName(){
      let params = { 'name': this.orgManageMainData.searchName , 'currentPageNo': 1 , 'pageSize': this.pageSize  }
      this.getOrgManageListFun(params,'searchByName')
    },
    outOfOrgList(){
      this.outOfOrgListData.type = ''
      this.outOfOrgListData.searchPhone = ''
      this.getLockedOrgManageListFun({ pageSize: this.pageSize , currentPageNo:1 })
    },
    backOutOrgList(){
      this.outOfOrgListData.type = ''
      this.outOfOrgListData.searchPhone = ''
      this.getLockedOrgManageListFun({ pageSize: this.pageSize , currentPageNo:1 })
    },
    searchOutOrgByPhone(){
      this.outOfOrgListData.type = 'outSearchByPhone'
      let params = { 'mobile':this.outOfOrgListData.searchPhone, 'currentPageNo': 1 , 'pageSize': this.pageSize  }
      this.getLockedOrgManageListFun(params)
    },
    getLockedOrgManageListFun(params){
      let paramData = {
        pageSize: params.pageSize,
        currentPageNo: params.currentPageNo,
      }
      params.mobile ? paramData.mobile = params.mobile : ''
      customerApi.getLockedOrgManageList(paramData).then(res1=>{
        let res = res1.data.data
        res.orgList.forEach(orgItem=>{
          orgItem.create = orgItem.createName +'  ' + ( new Date(orgItem.createDate).format('yyyy-MM-dd hh:mm:ss') )
        })
        this.curPage = 'outOfOrgList'
        this.outOfOrgListData.list = res.orgList
        this.outOfOrgListData.pageShow = true
        this.outOfOrgListData.pageInfo = res.pageInfo


      }).catch(err=>{
        console.log('err=',err)
        this.$message.error('链接失败，请重试！')
      })
    },
    getListOrg(){
      this.curPage= 'orgList'
      this.getOrgManageListFun({ pageSize: this.pageSize , currentPageNo:1 })
    },
    mainPageClickOrg2(pageItem){
      let params = { 'name': this.orgManageMainData.searchName , 'mobile':this.orgManageMainData.searchPhone, 'currentPageNo':pageItem.page, 'pageSize': this.pageSize  }
      this.getLockedOrgManageListFun(params)
    },
    mainPageClickOrg(pageItem){
      let params = { 'name': this.orgManageMainData.searchName , 'mobile':this.orgManageMainData.searchPhone, 'currentPageNo':pageItem.page, 'pageSize': this.pageSize  }
      this.getOrgManageListFun(params)
    },
    getOrgManageListFun(params, type){
      this.orgListData.pageShow = false
      customerApi.getOrgManageList(params).then(res1=>{
        let res = res1.data.data
        res.orgList.forEach(orgItem=>{
          orgItem.create = orgItem.createName +'  ' + ( new Date(orgItem.createDate).format('yyyy-MM-dd hh:mm:ss') )
        })
        this.orgManageMainData.type = type

        this.orgListData.list = res.orgList
        this.curPage = 'orgList'
        this.orgListData.pageShow = true
        this.orgListData.pageInfo = res.pageInfo

      }).catch(err=>{
        console.log('err=', err)
        this.$message.error('获取数据失败！')
      })
    }
  },


}

</script>


<style lang="scss" scoped>
.orgManage{
  padding: 40px;

  .bdTtl{ line-height: 40px;  }
  .sl-dropdown{
    display: inline-block; position: relative;
    .dropdownCon{
      position: absolute;
      background: #fff;
      border: 1px solid #ccc;
      border-bottom: none;
      width: 120px;
      height: 160px;
      top: 23px;
      opacity: 1;
      left: -33px;
      border-radius: 4px;
      box-shadow: 0 0 5px #ccc;
      z-index: 1;
      padding-top: 5px;
      &:before{
        content: "";
        display: inline-block;
        width: 8px;
        height: 8px;
        border: 1px solid #ccc;
        transform: rotate(45deg);
        position: absolute;
        top: -6px;
        left: 55px;
        background: #fff;
        border-bottom: none;
        border-right: none;
      }
      .disableItem{
        border-bottom: 1px solid #ccc; color: #666;
        line-height: 30px;
        background-color: #eee;
      }
      .dropdownItem:hover{
        background-color: $tyBounce-color-blue-hover;
        color: #fff;
      }
      .dropdownItem{
        border-bottom: 1px solid #ccc;
        color: $tyBounce-color-blue-hover;
        line-height: 30px;
      }
    }
    .dropdownCon2{
      position: absolute;
      background: #fff;
      border: 1px solid #ccc;
      border-bottom: none;
      width: 120px;
      height: 180px;
      top: -193px;
      opacity: 1;
      left: -33px;
      border-radius: 4px;
      box-shadow: 0 0 5px #ccc;
      z-index: 1;
      padding-top: 5px;
      &:before{
        content: "";
        display: inline-block;
        width: 8px;
        height: 8px;
        border: 1px solid #ccc;
        transform: rotate(45deg);
        position: absolute;
        top: 181px;
        left: 55px;
        background: #fff;
        border-top: none;
        border-left: none;
      }
      .disableItem{
        border-bottom: 1px solid #ccc; color: #666;
        line-height: 30px;
        background-color: #eee;
      }
      .dropdownItem:hover{
        background-color: $tyBounce-color-blue-hover;
        color: #fff;
      }
      .dropdownItem{
        border-bottom: 1px solid #ccc;
        color: $tyBounce-color-blue-hover;
        line-height: 30px;
      }
    }
  }
  .dfdfd{
    .sl-dropdown{
      display: inline-block; position: relative;
      .dropdownCon{
        position: absolute;
        background: #fff;
        border: 1px solid #ccc;
        border-bottom: none;
        width: 120px;
        height: 110px;
        top: 23px;
        opacity: 1;
        left: -33px;
        border-radius: 4px;
        box-shadow: 0 0 5px #ccc;
        z-index: 1;
        padding-top: 5px;
        &:before{
          content: "";
          display: inline-block;
          width: 8px;
          height: 8px;
          border: 1px solid #ccc;
          transform: rotate(45deg);
          position: absolute;
          top: -6px;
          left: 55px;
          background: #fff;
          border-bottom: none;
          border-right: none;
        }
        .disableItem{
          border-bottom: 1px solid #ccc; color: #666;
          line-height: 30px;
          background-color: #eee;
        }
        .dropdownItem:hover{
          background-color: $tyBounce-color-blue-hover;
          color: #fff;
        }
        .dropdownItem{
          border-bottom: 1px solid #ccc;
          color: $tyBounce-color-blue-hover;
          line-height: 30px;
        }
      }
      .dropdownCon2{
        position: absolute;
        background: #fff;
        border: 1px solid #ccc;
        //border-bottom: none;
        width: 120px;
        height: 110px;
        top: -133px;
        opacity: 1;
        left: -33px;
        border-radius: 4px;
        box-shadow: 0 0 5px #ccc;
        z-index: 1;
        padding-top: 5px;
        &:before{
          content: "";
          display: inline-block;
          width: 8px;
          height: 8px;
          border: 1px solid #ccc;
          transform: rotate(45deg);
          position: absolute;
          top: 112px;
          left: 55px;
          background: #fff;
          border-top: none;
          border-left: none;
        }
        .disableItem{
          border-bottom: 1px solid #ccc; color: #666;
          line-height: 30px;
          background-color: #eee;
        }
        .dropdownItem:hover{
          background-color: $tyBounce-color-blue-hover;
          color: #fff;
        }
        .dropdownItem{
          border-bottom: 1px solid #ccc;
          color: $tyBounce-color-blue-hover;
          line-height: 30px;
        }
      }
    }
  }
  .rMMsg{
    padding:20px 50px; line-height: 30px;
    .ttl{ width: 150px; display: inline-block;  }
    .hr{ border-bottom: 1px solid #ccc; margin: 10px 0;  }
  }
  .addService{
    .radioB{
      line-height:40px; cursor: pointer;
      .fa{ margin-right:10px; margin-left:10px; color:$tyBounce-color-green; font-size: 18px;  }
    }
  }
  .packageList{
    .tip{ font-size: 14px; line-height:20px; color: #666; padding: 10px 20px;    }
  }
  .packageInfo{
    line-height:30px ;
    .packageName{ font-weight: bold; font-size:24px;  }
    .marT39{ margin-top: 12px; }
    .smallT{ font-size:14px; color: #666;  }
    .ccc{ font-size: 14px; font-weight: bold; margin-right:90px;   }
  }
  .orgEdit{
    line-height: 40px;
    table{ margin-left: 50px; }
    .el-select{ width: 100%; }
    td:nth-child(2){ padding-left: 20px; }
    td:nth-child(1){
      text-align: right; padding-left: 16px;
    }
  }
  .btnss{ width:350px; float: right;   }
  .orgList{
    font-size:14px ; width: 100%; margin-top: 20px;
  }
  .titleMemo{ line-height: 40px; }
}
</style>
<style lang="scss">
.el-table .cell{ text-align: center;  }
</style>

