package cn.sphd.miners.modules.material.entity;

import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by Administrator on 2016/9/19.
 */

public class MtSupplierMaterialDto implements Serializable {



    //@GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    private Date beginDate;
    /**
     * 最低采购量
     */
    private Integer minimumPurchase;

    private Integer perchaseCycle;

    private BigDecimal unitPrice;

    private BigDecimal taxRate;

    private BigDecimal unitPriceNotax;

    private String contractSn;

    private String memo;

    private Integer creator;

    private String createName;

    private Date createDate;

    private Integer updator;

    private String updateName;

    private Date updateDate;

    private Integer deleteType;//删除状态，1-可以删，0-不可删

    private BigDecimal minimumStock;    //'最低库存',
    private BigDecimal initialStock;    //'初始库存',


    private String priceStable;//'价格是否稳定:1-相对稳定,2-变动频繁',

    private Integer draftAcceptable;//'可否接受汇票:true-接受,false-不接受',

    private String codeName;

    private String chargeAcceptable;    //boolean comment '是否接受挂帐',

    private String chargeBegin;         //char(1) comment '挂帐开始类型:1-自入库之日起,2-自发票提交之日起 ',

    private Integer chargePeriod;        //int comment '帐期(天)',

    private String isInclude;           //boolean comment '合同是否包含本物料:true-包含,false—不包含',

    private int materialInvoicable;  //boolean comment '本物料可否开具发票::true-可开具,false-不能开',

    private String materialInvoiceCategory; //char(1) comment '本物料发票类型:1-增值税专票,2-普通发票',

    private BigDecimal materialTaxRate;    //decimal(10,5) comment '本物料税率',

    private String isImprest;           //b是否需要预付款:0-不确定,1-需要,2-不需要',

    private int inclusiveFreight;
    //包装方式 包装方式:1-基本固定,2-型式不定
    private String packageMethod;

    private String hasContact;  //'是否有合同;true-有,false-无',

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date validDate;//'有效期至',

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date signDate;//'签署日期',

    private String invoicable;  //'可否开具发票::true-可开具,false-不能开',

    private String invoiceCategory;//'发票类型:1-增值税专票,2-普通发票',

    private Integer isTax;

    private String atPar;

    private String payMethod;

    private String isParValue;//是否为开票价格

    private Integer material;

    private Integer supplier;

    //1.95

    private String enabled;

    private Date enabledTime;

    private String isAppointed;

    private Date appointedTime;

    private String appointedUser;

    private Integer instance;

    private String instanceChain;

    private String approveStatus;

    private String rejectReasionDesc;

    private String operation;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    public Integer getMinimumPurchase() {
        return minimumPurchase;
    }

    public void setMinimumPurchase(Integer minimumPurchase) {
        this.minimumPurchase = minimumPurchase;
    }

    public Integer getPerchaseCycle() {
        return perchaseCycle;
    }

    public void setPerchaseCycle(Integer perchaseCycle) {
        this.perchaseCycle = perchaseCycle;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getTaxRate() {
        return taxRate;
    }

    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    public BigDecimal getUnitPriceNotax() {
        return unitPriceNotax;
    }

    public void setUnitPriceNotax(BigDecimal unitPriceNotax) {
        this.unitPriceNotax = unitPriceNotax;
    }

    public String getContractSn() {
        return contractSn;
    }

    public void setContractSn(String contractSn) {
        this.contractSn = contractSn;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getDeleteType() {
        return deleteType;
    }

    public void setDeleteType(Integer deleteType) {
        this.deleteType = deleteType;
    }

    public BigDecimal getMinimumStock() {
        return minimumStock;
    }

    public void setMinimumStock(BigDecimal minimumStock) {
        this.minimumStock = minimumStock;
    }

    public BigDecimal getInitialStock() {
        return initialStock;
    }

    public void setInitialStock(BigDecimal initialStock) {
        this.initialStock = initialStock;
    }

    public String getPriceStable() {
        return priceStable;
    }

    public void setPriceStable(String priceStable) {
        this.priceStable = priceStable;
    }

    public Integer getDraftAcceptable() {
        return draftAcceptable;
    }

    public void setDraftAcceptable(Integer draftAcceptable) {
        this.draftAcceptable = draftAcceptable;
    }

    public String getCodeName() {
        return codeName;
    }

    public void setCodeName(String codeName) {
        this.codeName = codeName;
    }

    public String getChargeAcceptable() {
        return chargeAcceptable;
    }

    public void setChargeAcceptable(String chargeAcceptable) {
        this.chargeAcceptable = chargeAcceptable;
    }

    public String getChargeBegin() {
        return chargeBegin;
    }

    public void setChargeBegin(String chargeBegin) {
        this.chargeBegin = chargeBegin;
    }

    public Integer getChargePeriod() {
        return chargePeriod;
    }

    public void setChargePeriod(Integer chargePeriod) {
        this.chargePeriod = chargePeriod;
    }

    public String getIsInclude() {
        return isInclude;
    }

    public void setIsInclude(String isInclude) {
        this.isInclude = isInclude;
    }

    public int getMaterialInvoicable() {
        return materialInvoicable;
    }

    public void setMaterialInvoicable(int materialInvoicable) {
        this.materialInvoicable = materialInvoicable;
    }

    public String getMaterialInvoiceCategory() {
        return materialInvoiceCategory;
    }

    public void setMaterialInvoiceCategory(String materialInvoiceCategory) {
        this.materialInvoiceCategory = materialInvoiceCategory;
    }

    public BigDecimal getMaterialTaxRate() {
        return materialTaxRate;
    }

    public void setMaterialTaxRate(BigDecimal materialTaxRate) {
        this.materialTaxRate = materialTaxRate;
    }

    public String getIsImprest() {
        return isImprest;
    }

    public void setIsImprest(String isImprest) {
        this.isImprest = isImprest;
    }

    public int getInclusiveFreight() {
        return inclusiveFreight;
    }

    public void setInclusiveFreight(int inclusiveFreight) {
        this.inclusiveFreight = inclusiveFreight;
    }

    public String getPackageMethod() {
        return packageMethod;
    }

    public void setPackageMethod(String packageMethod) {
        this.packageMethod = packageMethod;
    }

    public String getHasContact() {
        return hasContact;
    }

    public void setHasContact(String hasContact) {
        this.hasContact = hasContact;
    }

    public Date getValidDate() {
        return validDate;
    }

    public void setValidDate(Date validDate) {
        this.validDate = validDate;
    }

    public Date getSignDate() {
        return signDate;
    }

    public void setSignDate(Date signDate) {
        this.signDate = signDate;
    }

    public String getInvoicable() {
        return invoicable;
    }

    public void setInvoicable(String invoicable) {
        this.invoicable = invoicable;
    }

    public String getInvoiceCategory() {
        return invoiceCategory;
    }

    public void setInvoiceCategory(String invoiceCategory) {
        this.invoiceCategory = invoiceCategory;
    }

    public Integer getIsTax() {
        return isTax;
    }

    public void setIsTax(Integer isTax) {
        this.isTax = isTax;
    }

    public String getAtPar() {
        return atPar;
    }

    public void setAtPar(String atPar) {
        this.atPar = atPar;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public String getIsParValue() {
        return isParValue;
    }

    public void setIsParValue(String isParValue) {
        this.isParValue = isParValue;
    }

    public Integer getMaterial() {
        return material;
    }

    public void setMaterial(Integer material) {
        this.material = material;
    }

    public Integer getSupplier() {
        return supplier;
    }

    public void setSupplier(Integer supplier) {
        this.supplier = supplier;
    }

    public String getEnabled() {
        return enabled;
    }

    public void setEnabled(String enabled) {
        this.enabled = enabled;
    }

    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    public String getIsAppointed() {
        return isAppointed;
    }

    public void setIsAppointed(String isAppointed) {
        this.isAppointed = isAppointed;
    }

    public Date getAppointedTime() {
        return appointedTime;
    }

    public void setAppointedTime(Date appointedTime) {
        this.appointedTime = appointedTime;
    }

    public String getAppointedUser() {
        return appointedUser;
    }

    public void setAppointedUser(String appointedUser) {
        this.appointedUser = appointedUser;
    }

    public Integer getInstance() {
        return instance;
    }

    public void setInstance(Integer instance) {
        this.instance = instance;
    }

    public String getInstanceChain() {
        return instanceChain;
    }

    public void setInstanceChain(String instanceChain) {
        this.instanceChain = instanceChain;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public String getRejectReasionDesc() {
        return rejectReasionDesc;
    }

    public void setRejectReasionDesc(String rejectReasionDesc) {
        this.rejectReasionDesc = rejectReasionDesc;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }
}
