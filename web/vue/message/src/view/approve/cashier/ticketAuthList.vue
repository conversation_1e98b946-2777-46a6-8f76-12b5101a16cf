<template><!-- 增票认证-->
  <div id="ticketAuthList"  v-loading.fullscreen.lock="loading">
    <div v-show="!showSure3">
      <TY_NavTop title="增票认证" isButton="true" @toggleSubmit="submitBtn"></TY_NavTop>
      <div class="container">
        <div class="p1">
          <p v-if="approvalStatus != 3">请选择<span class="red">{{approvalStatusShow}}</span>的增值税专用发票。</p>
          <p v-if="approvalStatus === 3">请选择<span class="red">认证未通过、换了新发票</span>的发票，并录入所换回新发票的发票号码。</p>
        </div>
        <div class="p1">
          <p>共{{list.length}}张，已选{{multipleSelection.length}}张。<!--<span class="ri" @click="allSelect()">全选</span>--></p>
        </div>
        <div>
          <el-table ref="multipleTable" :data="list" tooltip-effect="dark" style="width: 100%" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" v-model="multipleSelection"></el-table-column>
            <el-table-column prop="billNo" label="发票号码" width="120"></el-table-column>
            <el-table-column prop="itemAmount" label="价税合计" width="120"></el-table-column>
            <el-table-column label="查看">
              <template slot-scope="scope"><span class="linkBtn" @click="getAuthBillItemDetail(scope.row)">票据信息</span></template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <!-- 换了新发票3确定后的页面 -->
    <div v-show="showSure3">
      <TY_NavTop title="出纳" isButton="true" @toggleSubmit="submitOk"></TY_NavTop>
      <div class="container">
        <div class="p1">
          <p><span class="red">{{approvalStatusShow}}</span>的增值税专用发票，您共选了{{multipleSelection.length}}张。</p>
          <p>请录入所换回新增值税专用发票的发票日期与号码。</p>
        </div>
        <div class="">
          <div>
            <el-table ref="multipleTable" :default-expand-all="true" :data="multipleSelection" style="width:100%" @selection-change="handleSelectionChange">
              <el-table-column type="expand">
                <template slot-scope="props">
                  <el-form label-position="left" inline class="demo-table-expand">
                    <el-form-item label="所换回新增值税专用发票的发票日期">
                      <el-date-picker type="date" placeholder="请选择" v-model="props.row.issueDate" value-format="yyyy-MM-dd" size="small"></el-date-picker>
                    </el-form-item>
                    <el-form-item label="所换回新增值税专用发票的发票号码">
                      <el-input size="small" v-model="props.row.no" @blur="chargeLength(8,props.row)"></el-input>
                    </el-form-item>
                  </el-form>
                </template>
              </el-table-column>
              <el-table-column prop="billNo" label="发票号码" width="120"></el-table-column>
              <el-table-column prop="itemAmount" label="价税合计" width="120" :formatter="toFix2"></el-table-column>
              <el-table-column label="查看">
                <template slot-scope="scope"><span class="linkBtn" @click="getAuthBillItemDetail(scope.row)">票据信息</span></template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
    <!-- 提示 -->
    <el-dialog title=" ! 提示" :visible.sync="submitTip" width="80%" >
      <p class="handle-center">确定选中以上增值税专用发票吗？？</p>
      <span slot="footer" class="dialog-footer">
        <div class="handle_button handle-center">
          <input type="submit" class="fd-btn fc-btn-yellow" value="取消" @click="submitTip = false">
          <input type="submit" class="fd-btn fc-btn-green" value="确定" @click="submitOk()">
        </div>
      </span>
    </el-dialog>
  </div>
</template>
<style lang="less">
#ticketAuthList{
  overflow-y: auto!important;
  .p1{ background:#fff;margin-bottom:10px; padding:15px;  }
  .linkBtn{ color: #0b9df9; cursor:default;  }
  .linkBtn:hover{ text-decoration:underline;   }
  .red{ color: #ed5565; }
  .ri{ float: right; color:#fff; padding: 2px 10px; background:#0b9df9; cursor: default;    }
  .ri:hover{ background: #30a8f9;   }
  .buttonGroupRight .nav_btn:last-child { padding:0 30px; }
  .el-form-item__label{line-height:20px; }
  .el-input--small .el-input__inner{ width:225px;  }

}
</style>
<script>
var that
export default {
  name: 'ticketAuthList',
  data () {
    return {
      loading: false,
      showSure3: false,
      submitTip: false,
      approvalStatus: '',
      approvalStatusShow: '',
      list: [],
      multipleSelection: [],
      listenersUid: []
    }
  },
  created () {
    that = this
    this.approvalStatus = Number(this.$route.params.sta)
    this.approvalStatusShow = this.setStaName(this.approvalStatus)
    this.listenersUid = [
      this.sphdSocket.subscribe('cashierAuthenticationHandle', function (data) {
        that.loading = false
        let res = JSON.parse(data)
        console.log('cashierAuthentication receive OK : ' + res)
        console.log(res)
        that.list = res['cashierAuthentication'] || []
      }),
      // 认证操作人
      this.sphdSocket.subscribe('reimburseNotice', function (data) {
        that.loading = false
        let res = JSON.parse(data)
        console.log('reimburseNotice认证操作人 receive OK : ' + res)
        console.log(res)
        let status = res.status
        let content = res.content
        console.log(status)
        that.$kiko_message(content)
        that.$router.push({
          path: `/ticketAuthentication`
        })
      }),
      // 财务增票认证审批人
      this.sphdSocket.subscribe('cashierAuthentication', function (data) {
        that.loading = false
        let res = JSON.parse(data)
        console.log('cashierAuthentication 财务增票认证审批人 receive OK : ' + res)
        console.log(res)
        let status = res.status
        if (status === 1) {
          this.$kiko_message('已有票据完成认证请返回重新选择')
          that.$router.push({
            path: `/ticketAuthentication`
          })
        }
      }, 'user')
    ]
    let sessionid = that.sphdSocket.sessionid
    let oid = that.sphdSocket.org.id
    this.sphdSocket.send('cashierAuthentication', { 'session': sessionid, 'oid': oid })
  },
  filter: {
    toFix2: function (val) {
      return Number(val).toFixed(2)
    }
  },
  destroyed: function () {
    let that = this
    this.listenersUid.forEach(function (item) {
      that.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    chargeLength: function (len) {
      let tip = false
      this.multipleSelection.forEach(function (rowItem) {
        let str = rowItem.no
        if (str.length > len) {
          tip = true
        }
      })
      if (tip) {
        this.$kiko_message('发票字数不能超过8个！')
      }
    },
    setStaName: function (sta) {
      let str = ''
      switch (sta) {
        case 1:
          str = '认证已通过'
          break
        case 2:
          str = '认证未通过、不用来抵扣，只用作报销'
          break
        case 3:
          str = '认证未通过、换了新发票'
          break
        default:
          str = ''
      }
      return str
    },
    handleSelectionChange (val) {
      this.multipleSelection = val
      console.log('操作multipleSelection')
      console.log(this.multipleSelection)
    },
    submitBtn: function () {
      console.log(this.multipleSelection)
      if (this.multipleSelection.length === 0) {
        this.$kiko_message('请先选择发票！')
        return false
      }
      console.log(this.approvalStatus)
      if (this.approvalStatus === 3) {
        this.showSure3 = true
      } else {
        this.submitTip = true
      }
    },
    submitOk: function () {
      this.submitTip = false
      let bills = []
      let isok = true
      let isok2 = true
      this.multipleSelection.forEach(function (item) {
        if (that.approvalStatus === 3) {
          if (!item.no || !item.issueDate) {
            isok = false
          }
          if (item.no.length > 8) {
            isok2 = false
          }
          bills.push({
            'billId': item.id,
            'billNo': item.no,
            'issueDate': item.issueDate
          })
        } else {
          bills.push({
            'billId': item.id,
            'billNo': '',
            'issueDate': ''
          })
        }
      })
      if (!isok) {
        this.$kiko_message('请将所换回新增值税专用发票的发票日期和号码补充完整！')
        return false
      }
      if (!isok2) {
        return false
      }
      console.log(bills)
      this.loading = true
      let json = {
        'session': that.sphdSocket.sessionid,
        'approvalStatus': that.approvalStatus,
        'bills': JSON.stringify(bills)
      }
      console.log(json)
      this.sphdSocket.send('authApproval', json)
    },
    getAuthBillItemDetail: function (item) {
      console.log('打印一下值')
      console.log(item)
      let params = {
        'name': 'auth',
        'id': item.id,
        'createName': '',
        'reimburseId': '',
        'billCat': item.billCat,
        'feeCat': '',
        'secondFeeCat': '',
        'billCatName': '增值税专用发票',
        'feeCatName': '',
        'secondFeeCatName': '',
        'totalAmount': item.itemAmount,
        'num': '',
        'session': this.sphdSocket.sessionid
      }
      this.$store.dispatch('setBillParam', params)
      this.$router.push({
        path: 'bill',
        name: 'bill'
      })
    }
  }
}
</script>
