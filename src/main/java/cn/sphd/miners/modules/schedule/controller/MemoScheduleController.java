package cn.sphd.miners.modules.schedule.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.schedule.entity.*;
import cn.sphd.miners.modules.schedule.service.*;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import cn.sphd.miners.modules.uploads.service.UploadService;
import io.netty.util.internal.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by Administrator on 2019/1/24.
 */
@Controller
@RequestMapping("/schedule")
public class MemoScheduleController {
    @Autowired
    MemoScheduleService memoScheduleService;
    @Autowired
    MemoJobService memoJobService;
    @Autowired
    UserService userService;
    @Autowired
    MemoScheduleHistoryService memoScheduleHistoryService;
    @Autowired
    MemoScheduleImageService memoScheduleImageService;
    @Autowired
    MemoScheduleSupplementService memoScheduleSupplementService;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    MemoJobMsgService memoJobMsgService;
    @Autowired
    MemoScheduleShareService memoScheduleShareService;
    @Autowired
    UploadService uploadService;



    /**
    * <AUTHOR>
    * @Date 2019/1/24 15:07
    * 跳转事件管理页面
    */
    @RequestMapping("/toScheduleManage.do")
    public String toScheduleManage(){
        return "/memo/event";
    }

    /**
    * <AUTHOR>
    * @Date 2019/1/24 15:06
    * 事件管理主页数据
    */
    @ResponseBody
    @RequestMapping("/scheduleManageList.do")
    public void scheduleManageList(User user, PageInfo pageInfo,String keyword,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<>();
//        memoScheduleService.scheduleSettleDay(user);
        List<MemoSchedule> memoScheduleList=memoScheduleService.getMemoScheduleListByUserId(user.getUserID(),pageInfo,keyword);
        Integer number=memoScheduleService.getMemoScheduleCounts(user.getUserID());
        map.put("memoScheduleList",memoScheduleList);
        map.put("pageInfo",pageInfo);
        map.put("number",number);
        ObjectToJson.objectToJson1(map,new String[]{"memoJobHashSet","memoScheduleHistoryHashSet","memoScheduleImageHashSet","memoScheduleSupplementHashSet"},response);
    }

    /**
    * <AUTHOR>
    * @Date 2019/8/13 11:41
    * 新增日程
    */
    @ResponseBody
    @RequestMapping("/addSchedule.do")
    public void addSchedule(User user, MemoSchedule memoSchedule, String[] imagePaths, String startDate, HttpServletResponse response, Integer[] shareableUserIds) throws Exception {
        Map<String,Object> map=new HashMap<>();
//        if (user==null){
//            throw new Exception("参数userId不能为空！");
//        }
        Integer status=0;//失败

        if (!StringUtil.isNullOrEmpty(startDate)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
            memoSchedule.setActiveStartDate(sdf.parse(startDate));//首次 执行时间
            if (memoSchedule.getActiveStartDate().getTime() <= new Date().getTime()) {
                status = 2;//时间已过时，日程不能新增
            } else {
                memoScheduleService.saveMemoSchedule(memoSchedule, user, "1", imagePaths);
                status = 1;//成功
            }
        } else {
            memoScheduleService.saveMemoSchedule(memoSchedule, user, "1", imagePaths);
            status = 1;//成功
        }
        map.put("status", status);
        if (status==1) {
            map.put("memoSchedule", memoSchedule); //新增成功返回详情
            if (shareableUserIds!=null&&shareableUserIds.length > 0){
                memoScheduleShareService.addMemoScheduleShareByMemoSchedule(memoSchedule, user, shareableUserIds); //新建日常直接分享   2021/1/20 1.150备忘收藏版更改
            }
        }
        ObjectToJson.objectToJson1(map,new String[]{"memoJobHashSet","memoScheduleHistoryHashSet","memoScheduleImageHashSet","memoScheduleSupplementHashSet"},response);

    }


    /**
    * <AUTHOR>
    * @Date 2019/1/24 17:05
    * 日程不再提醒
    */
    @ResponseBody
    @RequestMapping("/scheduleNoRemind.do")
    public void scheduleNoRemind(Integer id,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<>();
        if (id!=null) {
            memoScheduleService.scheduleNoRemind(id);
            map.put("status", 1);//成功
        }else {
            map.put("status", 0);//失败
        }
        ObjectToJson.objectToJson1(map,new String[]{""},response);

    }

//    /**
//    * <AUTHOR>
//    * @Date 2019/1/24 17:20
//    *  补充记录
//    */
//    @ResponseBody
//    @RequestMapping("/updateSupplement.do")
//    public void updateSupplement(Integer id,String supplement,HttpServletResponse response) throws IOException {
//        Map<String,Object> map=new HashMap<>();
//        if (id!=null&&!StringUtil.isNullOrEmpty(supplement)) {
//            MemoSchedule m = memoScheduleService.getMemoScheduleById(id);
////            m.setSupplement(m.getSupplement()==null?supplement:m.getSupplement()+supplement);
//            m.setSupplement(supplement);
//            memoScheduleService.updateMemoSchedule(m);
//            map.put("status", 1);//成功
//        }else {
//            map.put("status", 0);//失败
//        }
//        ObjectToJson.objectToJson1(map,new String[]{""},response);
//
//    }

    /**
    * <AUTHOR>
    * @Date 2019/1/25 9:13
    * 删除单个日程
    */
    @ResponseBody
    @RequestMapping("/deleteSchedule.do")
    public void deleteSchedule(User user,Integer id,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<>();
        if (id!=null) {

            user=userService.getUserByID(user.getUserID());
//            List<String> memoJobList=new ArrayList<>();
//            System.out.println("deleteSchedule.do接口 sessionId："+session.getId()+" userId:"+user.getUserID());
            memoScheduleService.deleteMemoSchedule(id,user);


            map.put("status", 1);//成功
        }else {
            map.put("status", 0);//失败
        }
        ObjectToJson.objectToJson1(map,new String[]{""},response);
    }

    /**
    * <AUTHOR>
    * @Date 2019/1/25 9:53
    * 删除日程全部提醒历史记录
    */
    @ResponseBody
    @RequestMapping("/deleteMemoJob.do")
    public void deleteMemoJob(User user,Integer id,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<>();
        if (id!=null) {
//            if (userId==null){
//                userId=user.getUserID();
//            }
            user=userService.getUserByID(user.getUserID());
//            List<String> memoJobList=new ArrayList<>();
//            System.out.println("deleteMemoJob.do接口 sessionId："+session.getId()+" userId:"+user.getUserID());


            MemoSchedule m = memoScheduleService.getMemoScheduleById(id);

            List<MemoJobMsg> memoJobMsgList=memoJobMsgService.getMemoJobMsgByScheduleId(id,null);
            for (MemoJobMsg memoJobMsg:memoJobMsgList){
                memoJobMsgService.updateMemoJobMsg(memoJobMsg,"4",user.getUserID());//目前未处理的 变成删除 并推送减去角标和数据
            }
            memoJobMsgService.deleteAllMemoJobMsg(memoJobMsgList);//删除所有该日程的具体提醒记录

            for (MemoJob mj : m.getMemoJobHashSet()) {
                mj.setEnabled(0);
                mj.setValid(false);
//                memoJobList.add(mj.getId().toString());
//                memoJobService.updateMemoJob(mj);
                memoJobService.deleteMemoJob(mj);
            }
//            if (memoJobList.size()>0) {
//                userSuspendMsgService.deleteUserSuspendMsg(user, "memoScheduleService", memoJobList);
//            }

            List<MemoScheduleSupplement> memoScheduleSupplementList=memoScheduleSupplementService.getMemoScheduleSupplementListBySid(id);
            for (MemoScheduleSupplement scheduleSupplement:memoScheduleSupplementList){
                memoScheduleService.delMemoScheduleSupplementImagFileUsing(scheduleSupplement.getMemoScheduleSupplementImageHashSet(),user);  //删除日程 补充记录 的 文件引用  2021/3/20 lixu 1.52空间与流量
            }

            map.put("status", 1);//成功
        }else {
            map.put("status", 0);//失败
        }
        ObjectToJson.objectToJson1(map,new String[]{""},response);
    }

//    /**
//    * <AUTHOR>
//    * @Date 2019/1/25 10:35
//    * 查询“未来日程”和“历史日程”和接口
//    */
//    @ResponseBody
//    @RequestMapping("/queryScheduleByDate.do")
//    public void queryScheduleByDate(User user,String beginDate,String endDate,HttpServletResponse response) throws ParseException, IOException {
//        SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
//        Map<String,Object> map=new HashMap<>();
//        User user= (User) session.getAttribute("user");
//        if (!StringUtil.isNullOrEmpty(beginDate)){
//            Date begin=NewDateUtils.today(sdf.parse(beginDate));
//            Date end;
//            if (StringUtil.isNullOrEmpty(endDate)){// 结束时间没值，按某天查询
//                end=NewDateUtils.getLastTimeOfDay(begin);
//            }else {
//                end=NewDateUtils.getLastTimeOfDay(sdf.parse(endDate));
//            }
//            List<MemoSchedule> mList=memoScheduleService.getMemoScheduleListByDate(user.getUserID(),begin,end);
//            map.put("memoScheduleList",mList);
//            map.put("status", 1);//成功
//        }else {
//            map.put("status", 0);//失败
//        }
//        ObjectToJson.objectToJson1(map,new String[]{"memoJobHashSet"},response);
//    }

//    /**
//    * <AUTHOR>
//    * @Date 2019/1/25 15:18
//    * 关键字搜索
//    */
//    @ResponseBody
//    @RequestMapping("/keywordQuerySchedule.do")
//    public void keywordQuerySchedule(User user,PageInfo pageInfo,String keyword,String description,String supplement,String place,HttpServletResponse response) throws IOException {
//        User user= (User) session.getAttribute("user");
//        Map<String,Object> map=new HashMap<>();
//
//        List<MemoSchedule> mList=memoScheduleService.keywordQuerySchedule(user.getUserID(),keyword,description,supplement,place,pageInfo);
//        map.put("pageInfo",pageInfo);
//        map.put("memoScheduleList",mList);
//        map.put("keyword",keyword);//关键字
//        map.put("description",description);
//        map.put("supplement",supplement);
//        map.put("place",place);
//        ObjectToJson.objectToJson(map,new String[]{"memoJobHashSet"},response);
//
//    }

    /**
    * <AUTHOR>
    * @Date 2019/1/25 17:10
    * 日程结案确定接口
     * type 1-本次活动已完成 ，2-不再提醒 ，3-再次提醒
     * remindDate 提醒时间
    */
    @ResponseBody
    @RequestMapping("/handleSchedule.do")
    public void handleSchedule(Integer id,Integer msgId,String type,String remindDate,HttpServletResponse response) throws ParseException, IOException {
        Integer status=0;
        Map<String,Object> map=new HashMap<>();
        if (id!=null&&!StringUtil.isNullOrEmpty(type)) {
            MemoSchedule memoSchedule=memoScheduleService.getMemoScheduleById(id);
            if (memoSchedule.getState()==2) {
                if (!type.equals("3")) {
                    memoScheduleService.handleSchedule(memoSchedule, type, null);
                } else {
                    Date date = new Date();
                    long ss = 0L;
                    switch (remindDate) {
                        case "fiveMinutes":
                            ss = 300000;
                            break;
                        case "tenMinutes":
                            ss = 600000;
                            break;
                        case "fifteenMinutes":
                            ss = 900000;
                            break;
                        case "halfHour":
                            ss = 30 * 60 * 1000;
                            break;
                        case "oneHour":
                            ss = 60 * 60 * 1000;
                            break;
                        case "twoHours":
                            ss = 2 * 60 * 60 * 1000;
                            break;
                        case "fourHours":
                            ss = 4 * 60 * 60 * 1000;
                            break;
                        case "eightHours":
                            ss = 8 * 60 * 60 * 1000;
                            break;
                    }
                    date = new Date(date.getTime() + ss);// 延时后的时间

                    memoScheduleService.handleSchedule(memoSchedule, type, date);
                }

                MemoJobMsg memoJobMsg=memoJobMsgService.getMemoJobMsgById(msgId);
                memoJobMsgService.updateMemoJobMsg(memoJobMsg,"2",memoSchedule.getUser());
                status=1;//成功
            }else {
                status=2;// 状态已变更
            }
        }
        map.put("status", status);
        ObjectToJson.objectToJson1(map,new String[]{""},response);

    }

    /**
    * <AUTHOR>
    * @Date 2019/1/26 11:20
    * 自定义提醒时间接口
    */
    @ResponseBody
    @RequestMapping("/copySchedule.do")
    public void copySchedule(Integer msgId,MemoSchedule memoSchedule,User user ,String startDate,String[] imagePaths,HttpServletResponse response) throws IOException, ParseException {
        Map<String,Object> map=new HashMap<>();
        if (msgId!=null) {
//            if (userId==null){
//                userId=user.getUserID();
//            }
            user=userService.getUserByID(user.getUserID());
            if (!StringUtil.isNullOrEmpty(startDate)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
                memoSchedule.setActiveStartDate(sdf.parse(startDate));//首次 执行时间
            }
            memoSchedule.setFreqType(1);
            if (memoSchedule.getActiveStartDate().getTime() <= new Date().getTime()){
                map.put("status", 2);//时间已过时，日程不能新增
            }else {
//                memoScheduleService.scheduleNoRemind(id);//日程不再提醒
                memoScheduleService.saveMemoSchedule(memoSchedule, user, "1", imagePaths);

                MemoJobMsg memoJobMsg=memoJobMsgService.getMemoJobMsgById(msgId);//具体的提醒
                MemoJob memoJob=memoJobService.getMemoJobById(memoJobMsg.getJob());
                memoJob.setState(3); //状态:1-已完成、2-提醒中、3-不再提醒、4-提醒已过期、5-尚未开始提醒
                memoJobService.updateMemoJob(memoJob);//历史日程 变为不再提醒。

                if (memoJob.getSchedule().getFreqType()>1) {
                    memoJob.getSchedule().setState(1);//原日程 是重复性日程  暂时从 提醒中 变为 "已完成"状态 主页详情展示为 下次提醒时间
                }else {
                    memoJob.getSchedule().setState(3);// 原日程是一次性日程， 变为 不再提醒。
                }
                memoJobMsgService.updateMemoJobMsg(memoJobMsg,"2",user.getUserID());//具体提醒变为已读

                map.put("status", 1);//成功
            }
        }else {
            map.put("status", 0);//失败
    }
        ObjectToJson.objectToJson1(map,new String[]{""},response);
    }

    /**
    * <AUTHOR>
    * @Date 2019/3/7 15:31
    * 根据日程id，获取提醒历史记录
    */
    @ResponseBody
    @RequestMapping("/getMemoJobList.do")
    public void getMemoJobList(Integer sid,HttpServletResponse response) throws IOException {
        MemoSchedule ms=memoScheduleService.getMemoScheduleById(sid);
        List<MemoJob> memoJobList=memoJobService.getMemoJobListBySid(sid);

        Map<String,Object> map=new HashMap<>();
        map.put("memoSchedule",ms);
        map.put("memoJobList",memoJobList);

        ObjectToJson.objectToJson1(map,new String[]{"schedule","memoJobHashSet"},response);
    }

    /**
    * <AUTHOR>
    * @Date 2019/3/7 18:53
    * 删除单条提醒记录
    */
    @ResponseBody
    @RequestMapping("/deleteOneMemoJob.do")
    public void deleteOneMemoJob(User user,Integer id,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<>();
        if (id!=null) {

            user=userService.getUserByID(user.getUserID());
//            System.out.println("deleteOneMemoJob.do接口 sessionId："+session.getId()+" userId:"+user.getUserID());
            MemoJob mj = memoJobService.getMemoJobById(id);
            mj.setValid(false);//改无效

            List<MemoJobMsg> memoJobMsgList=memoJobMsgService.getMemoJobMsgByJobId(id,null);
            for (MemoJobMsg memoJobMsg:memoJobMsgList){
                memoJobMsgService.updateMemoJobMsg(memoJobMsg,"4",user.getUserID());
            }
            memoJobMsgService.deleteAllMemoJobMsg(memoJobMsgList);//删除 提醒记录 的 每条具体记录

//            memoJobService.updateMemoJob(mj);
            if(1==mj.getSchedule().getFreqType()){
                mj.getSchedule().setValid(false);//改无效
//                memoScheduleService.updateMemoSchedule(mj.getSchedule());
                memoScheduleService.deleteMemoSchedule(mj.getSchedule());
            }else {
                memoJobService.deleteMemoJob(mj);
            }

            List<MemoScheduleSupplement> memoScheduleSupplementList=memoScheduleSupplementService.getMemoScheduleSupplementListByJobId(id);
            for (MemoScheduleSupplement scheduleSupplement:memoScheduleSupplementList){
                memoScheduleService.delMemoScheduleSupplementImagFileUsing(scheduleSupplement.getMemoScheduleSupplementImageHashSet(),user);  //删除提醒 补充记录 的 文件引用  2021/3/20 lixu 1.52空间与流量
            }



            //            List<String> memoJobList=new ArrayList<>()
//            memoJobList.add(mj.getId().toString());
//            userSuspendMsgService.deleteUserSuspendMsg(user,"memoScheduleService",memoJobList);

            map.put("status", 1);//成功
        }else {
            map.put("status", 0);//失败
        }
        ObjectToJson.objectToJson1(map,new String[]{""},response);
    }

//    /**
//     * <AUTHOR>
//     * @Date 2019/1/24 17:20
//     *  对提醒补充记录
//     */
//    @ResponseBody
//    @RequestMapping("/updateMemoJobSupplement.do")
//    public void updateMemoJobSupplement(Integer id,String supplement,HttpServletResponse response) throws IOException {
//        Map<String,Object> map=new HashMap<>();
//        if (id!=null&&!StringUtil.isNullOrEmpty(supplement)) {
//            MemoJob mj = memoJobService.getMemoJobById(id);
////            mj.setSupplement(mj.getSupplement()==null?supplement:mj.getSupplement()+supplement);
////            mj.getSchedule().setSupplement(mj.getSchedule().getSupplement()==null?supplement:mj.getSchedule().getSupplement()+supplement);
//            mj.setSupplement(supplement);
//            mj.getSchedule().setSupplement(supplement);
//            memoJobService.updateMemoJob(mj);
//            map.put("status", 1);//成功
//        }else {
//            map.put("status", 0);//失败
//        }
//        ObjectToJson.objectToJson1(map,new String[]{""},response);
//
//    }

    /**
    * <AUTHOR>
    * @Date 2019/3/8 9:24
    * 日程历史记录 中提醒改为不再提醒
    */
    @ResponseBody
    @RequestMapping("/memoJobNoRemind.do")
    public void memoJobNoRemind(Integer id,HttpServletResponse response) throws IOException {
        Map<String,Object> map=new HashMap<>();
        if (id!=null) {
            MemoJob memoJob=memoJobService.getMemoJobById(id);
            memoJob.setEnabled(0);
            memoJob.setState(3);
            memoJob.setUpdateDate(new Date());
            memoJob.getSchedule().setModifiable(false);//设置成不可修改

            memoJobService.updateMemoJob(memoJob);
            if(memoJob.getSchedule().getFreqType()==1) {
                memoJob.getSchedule().setState(3);
            }
//            删除消息记录
//            List<String> memoJobList=new ArrayList<>();
//            memoJobList.add(memoJob.getId().toString());
//            userSuspendMsgService.deleteUserSuspendMsg(userService.getUserByID(memoJob.getUser()),"memoScheduleService",memoJobList);
            List<MemoJobMsg> memoJobMsgList=memoJobMsgService.getMemoJobMsgByJobId(memoJob.getId(),"1");
            for (MemoJobMsg memoJobMsg:memoJobMsgList){
                memoJobMsgService.updateMemoJobMsg(memoJobMsg,"2",memoJob.getUser());
            }
            map.put("status", 1);//成功
        }else {
            map.put("status", 0);//失败
        }
        ObjectToJson.objectToJson1(map,new String[]{""},response);

    }
    
//    /**
//    * <AUTHOR>
//    * @Date 2019/3/14 10:31
//    *  按时间查询日程历史提醒记录
//    */
//    @ResponseBody
//    @RequestMapping("/queryMemoJobByDate.do")
//    public void  queryMemoJobByDate(User user,String beginDate,String endDate,PageInfo pageInfo,HttpServletResponse response) throws ParseException, IOException {
//            SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
//            Map<String,Object> map=new HashMap<>();
//            User user= (User) session.getAttribute("user");
//            if (!StringUtil.isNullOrEmpty(beginDate)){
//                Date begin=NewDateUtils.today(sdf.parse(beginDate));
//                Date end;
//
//                if (StringUtil.isNullOrEmpty(endDate)){// 结束时间没值，按某天查询
//                    end=NewDateUtils.getLastTimeOfDay(begin);
//                }else {
//                    end=NewDateUtils.getLastTimeOfDay(sdf.parse(endDate));
//                }
//                List<MemoJob> mList=memoJobService.getMemoJobListByDate(user.getUserID(),begin,end,pageInfo);
//                for (MemoJob j:mList){
//                    j.setFreqType(j.getSchedule().getFreqType());
//                }
//                map.put("memoJobList",mList);
//                map.put("pageInfo",pageInfo);
//
//                map.put("status", 1);//成功
//            }else {
//                map.put("status", 0);//失败
//            }
//            ObjectToJson.objectToJson1(map,new String[]{"schedule"},response);
//    }

    /**
    * <AUTHOR>
    * @Date 2019/8/13 11:55
    * 修改日程接口
    */
    @ResponseBody
    @RequestMapping("/updateMemoSchedule.do")
    public JsonResult updateMemoSchedule(User user,MemoSchedule memoSchedule,String startDate,String[] imagePaths) throws Exception {
        Map<String,Object> map=new HashMap<>();
        if(memoSchedule.getId()!=null){
//            User user= (User) session.getAttribute("user");
//            if (userId==null&&user==null){
//                throw new Exception("参数userId不能为空！");
//            }
//            userId=userId==null?user.getUserID():userId;
//            user=userService.getUserByID(user.getUserID());
            MemoSchedule oldMemoSchedule=memoScheduleService.getMemoScheduleById(memoSchedule.getId());
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");

            if (!StringUtil.isNullOrEmpty(startDate)&&sdf.parse(startDate).getTime()<= new Date().getTime()) {
                map.put("status", 2);//时间已过时，日程不能修改至此时间
            }else {
                memoSchedule.setActiveStartDate(StringUtil.isNullOrEmpty(startDate)?null:sdf.parse(startDate));//首次 执行时间
                if (oldMemoSchedule.getMemoScheduleHistoryHashSet().size()==0) {
                    memoScheduleHistoryService.saveMemoScheduleHistory(oldMemoSchedule,user,true,null);//修改前的内容 生成历史记录
                }
                    memoScheduleImageService.deleteAllMemoScheduleImage(oldMemoSchedule.getId());//删除原附件
                oldMemoSchedule.getMemoScheduleImageHashSet().clear();
                oldMemoSchedule.setTitle(memoSchedule.getTitle());
                oldMemoSchedule.setDescription(memoSchedule.getDescription());
                oldMemoSchedule.setFreqType(memoSchedule.getFreqType());
                oldMemoSchedule.setActiveStartDate(memoSchedule.getActiveStartDate());
                oldMemoSchedule.setSpecialInterval(memoSchedule.getSpecialInterval());
                memoScheduleService.updateMemoSchedule(oldMemoSchedule, user, imagePaths);//对日程进行修改
                memoScheduleHistoryService.saveMemoScheduleHistory(oldMemoSchedule,user,false,null);//把修改后的 内容生成历史记录

                map.put("status", 1);//成功
                map.put("memoSchedule", memoSchedule);
            }
        }else {
            map.put("status", 0);//失败
        }
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Date 2019/8/13 13:43
    * 获取日程修改记录接口
    */
    @ResponseBody
    @RequestMapping("/getMemoScheduleHistories.do")
    public JsonResult getMemoScheduleHistories(Integer id,PageInfo pageInfo){
        Map<String,Object> map=memoScheduleHistoryService.getMemoScheduleHistories(id,pageInfo);
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Date 2019/8/13 14:11
    * 获取不需要提醒的日程列表
    */
    @ResponseBody
    @RequestMapping("/getMemoList.do")
    public JsonResult getMemoList(User user,PageInfo pageInfo){
        List<MemoSchedule> memoScheduleList=memoScheduleService.getMemoListByUserId(user.getUserID(),pageInfo);
        Map<String,Object> map=new  HashMap<>();
        map.put("pageInfo",pageInfo);
        map.put("memoScheduleList",memoScheduleList);
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Date 2019/8/13 16:35
    *  查看日程单一修改记录详情
    */
    @ResponseBody
    @RequestMapping("/getMemoScheduleHistoryById.do")
    public JsonResult getMemoScheduleHistoryById(Integer id){
        MemoScheduleHistory m= memoScheduleHistoryService.getMemoScheduleHistoryById(id);
        return new JsonResult(1,m);
    }

    /**
    * <AUTHOR>
    * @Date 2019/8/14 9:28
    * 根据id，查看日程详情
    */
    @ResponseBody
    @RequestMapping("/scheduleInfo.do")
    public JsonResult scheduleInfo(Integer id,Integer jobId,Integer msgId) throws IOException {
        MemoSchedule m=memoScheduleService.getMemoScheduleById(id);
        List<MemoScheduleImage>  scheduleImageList=memoScheduleImageService.getMemoScheduleImageListBySid(id);
//        List<MemoScheduleSupplement> scheduleSupplementList=memoScheduleSupplementService.getMemoScheduleSupplementListBySid(id);
        if (msgId!=null&&jobId!=null){
            MemoJob memoJob=memoJobService.getMemoJobById(jobId);
            MemoJobMsg memoJobMsg=memoJobMsgService.getMemoJobMsgById(msgId);
            if (memoJob.getState()==4)
                memoJobMsgService.updateMemoJobMsg(memoJobMsg,"2",memoJobMsg.getUserId());
        }
        Map<String,Object> map=new HashMap<>();
        map.put("memoSchedule",m);//日程详情
        map.put("scheduleImageList",scheduleImageList);// 附件
//        map.put("scheduleSupplementList",scheduleSupplementList);// 补充记录  及 补充记录附件
        return new JsonResult(1,map);
    }
    
    /**
    * <AUTHOR>
    * @Date 2019/8/14 9:56
    * 获取本人 所有提醒记录  历史日程
    */
    @ResponseBody
    @RequestMapping("/getMemoJobListByUserId.do")
    public JsonResult getMemoJobListByUserId(User user,PageInfo pageInfo){

        List<MemoJob> memoJobList=memoJobService.getMemoJobListByUserId(user.getUserID(),null,pageInfo);
        for (MemoJob memoJob:memoJobList){
            memoJob.setFreqType(memoJob.getSchedule().getFreqType());
        }
        Map<String,Object> map=new  HashMap<>();
        map.put("pageInfo",pageInfo);
        map.put("memoJobList",memoJobList);
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Date 2019/8/14 10:12
    * 无需提醒的日程关键字检索功能
    */
    @ResponseBody
    @RequestMapping("/searchMemoSchedule.do")
    public JsonResult searchMemoSchedule(User user,String keyword,PageInfo pageInfo){
//        User user= (User) session.getAttribute("user");
//        if (userId==null){
//            userId=user.getUserID();
//        }
        user=userService.getUserByID(user.getUserID());
        List<MemoSchedule> mList=memoScheduleService.getMemoScheduleListByKeyword(user.getUserID(),keyword,pageInfo);
        Map<String,Object> map=new  HashMap<>();
        map.put("pageInfo",pageInfo);
        map.put("memoScheduleList",mList);
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Date 2019/8/14 11:34
    * 对提醒新增补充记录
    */
    @ResponseBody
    @RequestMapping("/addMemoJobSupplement.do")
    public JsonResult addMemoJobSupplement(User user,Integer memoJobId,String description,String[] imagePaths){
//        User user= (User) session.getAttribute("user");
//        if (userId==null){
//            userId=user.getUserID();
//        }
        user=userService.getUserByID(user.getUserID());
        if (memoScheduleSupplementService.addMemoScheduleSupplement(user,memoJobId,description,imagePaths)){
            return new JsonResult(1,1);//成功
        }else {
            return new JsonResult(1,0);//失败
        }
    }

    /**
    * <AUTHOR>
    * @Date 2019/8/14 13:35
    * 删除补充记录
    */
    @ResponseBody
    @RequestMapping("/deleteMemoJobSupplement.do")
    public JsonResult deleteMemoJobSupplement(Integer supplementId){
        if (supplementId!=null){
            MemoScheduleSupplement memoScheduleSupplement=memoScheduleSupplementService.getMemoScheduleSupplementById(supplementId);
            User user=userService.getUserByID(memoScheduleSupplement.getSchedule().getUser());

            memoScheduleService.delMemoScheduleSupplementImagFileUsing(memoScheduleSupplement.getMemoScheduleSupplementImageHashSet(),user); // 删除补充记录的附件 引用
            memoScheduleSupplementService.deleteMemoScheduleSupplement(supplementId);// 删除补充记录（对附件有集连删除）
            return new JsonResult(1,1);//成功

        }else {
           return new JsonResult(1,0);//失败
        }
    }



    /**
    * <AUTHOR>
    * @Date 2019/8/14 13:47
    * 提醒记录（历史记录）筛选 检索
    */
    @ResponseBody
    @RequestMapping("/searchMemoJob.do")
    public  JsonResult searchMemoJob(User user,String keyword,PageInfo pageInfo){

        List<MemoJob> memoJobList=memoJobService.getMemoJobListByUserId(user.getUserID(),keyword,pageInfo);
        Map<String,Object> map=new  HashMap<>();
        map.put("pageInfo",pageInfo);
        map.put("memoJobList",memoJobList);
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Date 2019/8/14 13:56
    * 提醒记录详情
    */
    @ResponseBody
    @RequestMapping("/getMemoJobById.do")
    public JsonResult getMemoJobById(Integer memoJobId){
        MemoJob memoJob=memoJobService.getMemoJobById(memoJobId);
        MemoSchedule m=memoJob.getSchedule();
        memoJob.setMemoScheduleImageHashSet(m.getMemoScheduleImageHashSet());
        memoJob.setFreqType(m.getFreqType());
        memoJob.setActiveStartDate(m.getActiveStartDate());
//        List<MemoScheduleImage>  scheduleImageList=memoScheduleImageService.getMemoScheduleImageListBySid(m.getId());//日程附件
        List<MemoScheduleSupplement> scheduleSupplementList=memoScheduleSupplementService.getMemoScheduleSupplementListByJobId(memoJobId);
        Map<String,Object> map=new HashMap<>();
        map.put("memoSchedule",memoJob);//日程详情
//        map.put("scheduleImageList",scheduleImageList);// 附件
        map.put("scheduleSupplementList",scheduleSupplementList);// 补充记录  及 补充记录附件
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Date 2020/1/4 10:32
    * 悬浮窗待处理的提醒，初始数据列表
    */
    @ResponseBody
    @RequestMapping("/getMemoJobMsgList.do")
    public JsonResult  getMemoJobMsgList(User user){
        List<MemoJobMsg> memoJobMsgList=memoJobMsgService.getMemoJobMsgBuUserId(user,"1");
        return new JsonResult(1,memoJobMsgList);
    }



}
