import invoiceApply from '@/view/apply/invoice/invoice'
import invoiceApply1 from '@/view/apply/invoice/invoice_1'
import invoiceApply2 from '@/view/apply/invoice/invoice_2'
import invoiceApply3 from '@/view/apply/invoice/invoice_3'
import invoiceApply4 from '@/view/apply/invoice/invoice_4'
import applyInvoiceDetail from '@/view/apply/invoice/invoiceDetail'
import applyInvoiceDetailInfo from '@/view/apply/invoice/invoiceDetailInfo'
import invoiceEndInfo from '@/view/apply/invoice/invoiceEndInfo'
import invoiceEndInfoForMsg from '@/view/apply/invoice/invoiceEndInfoForMsg'
import invoiceEnd from '@/view/apply/invoice/invoiceEnd'
import invoiceApproval from '@/view/approve/invoice/invoice'
import invoiceApproval1 from '@/view/approve/invoice/invoice_1'
import invoiceApproval2 from '@/view/approve/invoice/invoice_2'
import invoiceApproval3 from '@/view/approve/invoice/invoice_3'
import invoiceApproval4 from '@/view/approve/invoice/invoice_4'
import approvalInvoiceDetail from '@/view/approve/invoice/invoiceDetail'
import invoiceRegister from '@/view/approve/invoice/invoiceRegister'
import invoiceRegister1 from '@/view/approve/invoice/invoiceRegister1'
import invoiceRegister1Tip from '@/view/approve/invoice/invoiceRegister1Tip'
import invoiceRegister2 from '@/view/approve/invoice/invoiceRegister2'
import invoiceRegisterDetail from '@/view/approve/invoice/invoiceRegisterDetail'
import invoiceRegisterDetailInfo from '@/view/approve/invoice/invoiceRegisterDetailInfo'
import invoiceRegisterDetailInfoInvoice from '@/view/approve/invoice/invoiceRegisterDetailInfoInvoice'

import applyReimburse from '@/view/apply/cashier/reimburse'
import applyReimburseDetail from '@/view/apply/cashier/reimburseDetail'
import approvalReimburse from '@/view/approve/cashier/reimburse'
import reimburseBillVerification from '@/view/approve/cashier/reimburse_financeBillVerify'
import approveReimburseFinance from '@/view/approve/cashier/reimburse_finance'
import approveReimburseDetail from '@/view/approve/cashier/reimburseDetail'
import approveReimburseDetailExplain from '@/view/approve/cashier/reimburseDetailExplain'
import approveReimburseDetail2 from '@/view/approve/cashier/reimburseDetailapprove'
import approveReimburseDetailFu from '@/view/approve/cashier/reimburseDetailFu'
import approveReimburseDetailFu2 from '@/view/approve/cashier/reimburseDetailFu2'
import approveReimburseDetailFu3 from '@/view/approve/cashier/reimburseDetailFu3'
import reviewFinanceReimburse from '@/view/approve/cashier/reviewApproveFinance'
import reviewApproveFinanceSearch from '@/view/approve/cashier/reviewApproveFinanceSearch'
import reviewApproveFinanceSearchList from '@/view/approve/cashier/reviewApproveFinanceSearchList'
import reimburseVerificationBillsQuery from '@/view/approve/cashier/reimburse_verificationBillsQuery'
import reimburseVerificationBillsQueryPer from '@/view/approve/cashier/reimburse_verificationBillsQueryPer'
import applyReimburseQuery from '@/view/apply/cashier/reimburse_query'
import applyReimburseQueryPer from '@/view/apply/cashier/reimburse_query_per'
import approveReimburseQuery from '@/view/approve/cashier/reimburse_query'
import approveReimburseQueryPer from '@/view/approve/cashier/reimburse_query_per'
import cashier from '@/view/approve/cashier/cashier'
import ticketAuthentication from '@/view/approve/cashier/ticketAuthentication'
import ticketAuthList from '@/view/approve/cashier/ticketAuthList'
import ticketAuthSearchList from '@/view/approve/cashier/ticketAuthSearchList'
import ticketAuthSearch from '@/view/approve/cashier/ticketAuthSearch'

import memoJobMsg from '@/view/schedule/schedule_list'
import scheduleDetail from '@/view/schedule/schedule_detail'
import bill from '@/view/common/bill'
import subjectMessage from '@/view/message/subjectMessage'

import formulaEditApply from '@/view/apply/formulaEdit/formulaEditApply'
import formulaEditApplyDetail from '@/view/apply/formulaEdit/formulaEditApplyDetail'
import formulaInfo from '@/view/apply/formulaEdit/formulaInfo'
import formulaEditApproval from '@/view/approve/formulaEdit/formulaEditApproval'
import formulaEditSearch from '@/view/approve/formulaEdit/formulaEditSearch'
import formulaEditSearchList from '@/view/approve/formulaEdit/formulaEditSearchList'
import formulaEditApprovalDetail from '@/view/approve/formulaEdit/formulaEditApprovalDetail'

import returnInput from '@/view/apply/payback/payBack'
import payApplyDetail from '@/view/apply/payback/payBackDetail'
import returnInAccount from '@/view/approve/payback/payBack'
import payApproveDetail from '@/view/approve/payback/payBackDetail'
import payManage from '@/view/approve/payback/payBackManage'
import payApplyScreen from '@/view/apply/payback/payBackSearch'
import paySearchList from '@/view/apply/payback/payBackSearchList'
import paySearchDetail from '@/view/apply/payback/payBackSearchInfo'
import payApproveScreen from '@/view/approve/payback/payBackScreen'
import collectShList from '@/view/approve/payback/payBackSnList'
import collectShDetail from '@/view/approve/payback/payBackSnDetail'
import payBackMsg from '@/view/message/payBackMes'
import collectDetail from '@/view/common/payBack/payDetails'

import complaintInput from '@/view/apply/complaint/complaint'
import applyComplaintSearch from '@/view/apply/complaint/complaintSearch'
import approveComplaintSearch from '@/view/approve/complaint/complaintSearch'
import complaintHandle from '@/view/approve/complaint/complaint'
import complaintApproval1 from '@/view/approve/complaint/complaint_1'
import complaintApproval2 from '@/view/approve/complaint/complaint_2'
import complaintApproval3 from '@/view/approve/complaint/complaint_3'

import financeApprove from '@/view/approve/finance/financeApprove'
import financeModify from '@/view/apply/finance/financeChange'
import financeModifyApproval from '@/view/approve/finance/financeChange'
import financeChangeQuery from '@/view/query/financeChange_query'
import financeChangeQueryPer from '@/view/query/financeChange_query_per'

// 采购 票款处理
import purchaseBillHandleApply from '@/view/apply/purchaseInvoice/purchaseInvoiceApply'
import purchaseBillApply from '@/view/apply/purchaseInvoice/purchaseInvoiceCharge'
import purchasePaymentApply from '@/view/apply/purchaseInvoice/purchasePay'
import purchaseBillApproval from '@/view/approve/purchaseInvoice/purchaseBillApproval'
import purchaseInvoiceMoreadd from '@/view/approve/purchaseInvoice/purchaseInvoiceMoreadd'

export default [
  {
    path: '/invoiceApply',
    name: 'invoiceApply',
    component: invoiceApply,
    children: [
      {
        path: '',
        name: 'invoiceApply1',
        component: invoiceApply1
      },
      {
        path: 'invoiceApply2',
        name: 'invoiceApply2',
        component: invoiceApply2
      },
      {
        path: 'invoiceApply3',
        name: 'invoiceApply3',
        component: invoiceApply3
      },
      {
        path: 'invoiceApply4',
        name: 'invoiceApply4',
        component: invoiceApply4
      }
    ]
  }, {
    path: '/invoiceApproval',
    name: 'invoiceApproval',
    component: invoiceApproval,
    children: [
      {
        path: '',
        name: 'invoiceApproval1',
        component: invoiceApproval1
      },
      {
        path: 'invoiceApproval2',
        name: 'invoiceApproval2',
        component: invoiceApproval2
      },
      {
        path: 'invoiceApproval3',
        name: 'invoiceApproval3',
        component: invoiceApproval3
      },
      {
        path: 'invoiceApproval4',
        name: 'invoiceApproval4',
        component: invoiceApproval4
      }
    ]
  }, {
    path: '/applyInvoiceDetail/:id',
    name: 'applyInvoiceDetail',
    component: applyInvoiceDetail
  }, {
    path: '/approvalInvoiceDetail/:id',
    name: 'approvalInvoiceDetail',
    component: approvalInvoiceDetail
  }, {
    path: '/applyInvoiceDetailInfo/:id/:invoiceId/:way/:fid',
    name: 'applyInvoiceDetailInfo',
    component: applyInvoiceDetailInfo
  }, {
    path: '/invoiceRegister',
    name: 'invoiceRegister',
    component: invoiceRegister,
    children: [
      {
        path: '',
        name: 'invoiceRegister1',
        component: invoiceRegister1
      },
      {
        path: 'invoiceRegister2',
        name: 'invoiceRegister2',
        component: invoiceRegister2
      }
    ]
  }, {
    path: '/invoiceRegister1Tip/:id',
    name: 'invoiceRegister1Tip',
    component: invoiceRegister1Tip
  },  {
    path: '/invoiceRegisterDetail/:id',
    name: 'invoiceRegisterDetail',
    component: invoiceRegisterDetail
  }, {
    path: '/invoiceRegisterDetailInfo/:id/:invoiceId/:invoiceCategory',
    name: 'invoiceRegisterDetailInfo',
    component: invoiceRegisterDetailInfo
  }, {
    path: '/invoiceRegisterDetailInfoInvoice/:id',
    name: 'invoiceRegisterDetailInfoInvoice',
    component: invoiceRegisterDetailInfoInvoice
  }, {
    path: '/invoiceEnd',
    name: 'invoiceEnd',
    component: invoiceEnd
  }, {
    path: '/invoiceEndInfo/:id',
    name: 'invoiceEndInfo',
    component: invoiceEndInfo
  }, {
    path: '/invoiceEndInfoForMsg',
    name: 'invoiceEndInfoForMsg',
    component: invoiceEndInfoForMsg
  }, {
    path: '/memoJobMsg',
    name: 'memoJobMsg',
    component: memoJobMsg
  }, {
    path: '/scheduleDetail/:schedule/:id/:jobId',
    name: 'scheduleDetail',
    component: scheduleDetail
  }, {
    path: '/bill',
    name: 'bill',
    component: bill
  }, {
    path: '/applyReimburse',
    name: 'applyReimburse',
    component: applyReimburse
  }, {
    path: '/applyReimburseDetail/:id',
    name: 'applyReimburseDetail',
    component: applyReimburseDetail
  }, {
    path: '/approvalReimburse',
    name: 'approvalReimburse',
    component: approvalReimburse
  }, {
    path: '/approvalCashier/:pid',
    name: 'approvalCashier',
    component: cashier
  }, {
    path: '/ticketAuthSearch',
    name: 'ticketAuthSearch',
    component: ticketAuthSearch
  }, {
    path: '/reimburseVerificationBillsQuery',
    name: 'reimburseVerificationBillsQuery',
    component: reimburseVerificationBillsQuery
  }, {
    path: '/reimburseVerificationBillsQueryPer',
    name: 'reimburseVerificationBillsQueryPer',
    component: reimburseVerificationBillsQueryPer
  }, {
    path: '/ticketAuthSearchList',
    name: 'ticketAuthSearchList',
    component: ticketAuthSearchList
  }, {
    path: '/ticketAuthentication',
    name: 'ticketAuthentication',
    component: ticketAuthentication
  }, {
    path: '/ticketAuthList/:sta',
    name: 'ticketAuthList',
    component: ticketAuthList
  }, {
    path: '/financeReimburse',
    name: 'financeReimburse',
    component: approveReimburseFinance
  }, {
    path: '/reimburseBillVerification',
    name: 'reimburseBillVerification',
    component: reimburseBillVerification
  }, {
    path: '/purchaseBillFinanceApproval',
    name: 'purchaseBillFinanceApproval',
    component: () => import('@/view/approve/cashier/purchaseBillFinanceApproval'),
    meta: {
      title: '采购部门的票据审核'
    }
  }, {
    path: '/purchasePaymentFinanceApproval',
    name: 'purchasePaymentFinanceApproval',
    component: () => import('@/view/approve/cashier/purchasePaymentFinanceApproval'),
    meta: {
      title: '采购部门的付款'
    }
  }, {
    path: '/approveReimburseDetail/:id/:billCat',
    name: 'approveReimburseDetail',
    component: approveReimburseDetail
  }, {
    path: '/approveReimburseDetailExplain',
    name: 'approveReimburseDetailExplain',
    component: approveReimburseDetailExplain
  }, {
    path: '/approveReimburseDetail2/:id/:billCat',
    name: 'approveReimburseDetail2',
    component: approveReimburseDetail2
  }, {
    path: '/approveReimburseDetailFu/:id/:billCat/:updatePay',
    name: 'approveReimburseDetailFu',
    component: approveReimburseDetailFu
  }, {
    path: '/approveReimburseDetailFu2/:id/:billCat',
    name: 'approveReimburseDetailFu2',
    component: approveReimburseDetailFu2
  }, {
    path: '/approveReimburseDetailFu3/:id/:billCat',
    name: 'approveReimburseDetailFu3',
    component: approveReimburseDetailFu3
  }, {
    path: '/paymentReview/:pid',
    name: 'paymentReview',
    component: () => import('@/view/approve/cashier/paymentReview'),
    meta: {
      title: '付款复核'
    }
  }, {
    path: '/reviewFinanceReimburse',
    name: 'reviewFinanceReimburse',
    component: reviewFinanceReimburse
  }, {
    path: '/reviewApproveFinanceDetail/:id/:billCat',
    name: 'reviewApproveFinanceDetail',
    component: () => import('@/view/approve/cashier/reviewApproveFinanceDetail'),
    meta: {
      title: '付款复核（待付款详情页）'
    }
  }, {
    path: '/reviewApproveFinanceSearch',
    name: 'reviewApproveFinanceSearch',
    component: reviewApproveFinanceSearch
  }, {
    path: '/reviewApproveFinanceSearchList',
    name: 'reviewApproveFinanceSearchList',
    component: reviewApproveFinanceSearchList
  }, {
    path: '/applyReimburseQuery',
    name: 'applyReimburseQuery',
    component: applyReimburseQuery
  }, {
    path: '/applyReimburseQueryPer',
    name: 'applyReimburseQueryPer',
    component: applyReimburseQueryPer
  }, {
    path: '/approveReimburseQuery/:type',
    name: 'approveReimburseQuery',
    component: approveReimburseQuery
  }, {
    path: '/approveReimburseQueryPer',
    name: 'approveReimburseQueryPer',
    component: approveReimburseQueryPer
  }, {
    path: '/subjectMessage',
    name: 'subjectMessage',
    component: subjectMessage
  }, {
    path: '/formulaEditApply',
    name: 'formulaEditApply',
    component: formulaEditApply
  }, {
    path: '/formulaEditApplyDetail/:id',
    name: 'formulaEditApplyDetail',
    component: formulaEditApplyDetail
  }, {
    path: '/formulaInfo',
    name: 'formulaInfo',
    component: formulaInfo
  }, {
    path: '/formulaEditApproval',
    name: 'formulaEditApproval',
    component: formulaEditApproval
  }, {
    path: '/formulaEditSearch/:type',
    name: 'formulaEditSearch',
    component: formulaEditSearch
  }, {
    path: '/formulaEditSearchList',
    name: 'formulaEditSearchList',
    component: formulaEditSearchList
  }, {
    path: '/formulaEditApprovalDetail/:id',
    name: 'formulaEditApprovalDetail',
    component: formulaEditApprovalDetail
  }, {
    path: '/returnInput',
    name: 'returnInput',
    component: returnInput
  },
  {
    path: '/payApplyDetail/:sId',
    name: 'payApplyDetail',
    component: payApplyDetail
  },
  {
    path: '/returnInAccount',
    name: 'returnInAccount',
    component: returnInAccount
  },
  {
    path: '/payApproveDetail/:sId',
    name: 'payApproveDetail',
    component: payApproveDetail
  },
  {
    path: '/returnHandle',
    name: 'payManage',
    component: payManage
  },
  {
    path: '/payApplyScreen/:type',
    name: 'payApplyScreen',
    component: payApplyScreen
  },
  {
    path: '/paySearchList',
    name: 'paySearchList',
    component: paySearchList
  },
  {
    path: '/paySearchDetail/:id',
    name: 'paySearchDetail',
    component: paySearchDetail
  },
  {
    path: '/payApproveScreen',
    name: 'payApproveScreen',
    component: payApproveScreen
  },
  {
    path: '/collectShList',
    name: 'collectShList',
    component: collectShList
  },
  {
    path: '/collectShDetail/:id',
    name: 'collectShDetail',
    component: collectShDetail
  },
  {
    path: '/payBackMsg/:id',
    name: 'payBackMsg',
    component: payBackMsg
  },
  {
    path: '/collectDetail/:id',
    name: 'collectDetail',
    component: collectDetail
  },
  {
    path: '/complaintInput',
    name: 'complaintInput',
    component: complaintInput
  },
  {
    path: '/applyComplaintSearch',
    name: 'applyComplaintSearch',
    component: applyComplaintSearch
  },
  {
    path: '/approveComplaintSearch',
    name: 'approveComplaintSearch',
    component: approveComplaintSearch
  },
  {
    path: '/complaintHandle',
    name: 'complaintHandle',
    component: complaintHandle,
    children: [
      {
        path: '',
        name: 'complaintApproval1',
        component: complaintApproval1
      },
      {
        path: 'complaintApproval2',
        name: 'complaintApproval2',
        component: complaintApproval2
      },
      {
        path: 'complaintApproval3',
        name: 'complaintApproval3',
        component: complaintApproval3
      }
    ]
  },
  {
    path: '/financeApproval/:pid',
    name: 'financeApprove',
    component: financeApprove
  },
  {
    path: '/financeModify/:pid',
    name: 'financeModify',
    component: financeModify
  },
  {
    path: '/financeModifyApproval/:pid',
    name: 'financeModifyApproval',
    component: financeModifyApproval
  },
  {
    path: '/financeChangeQuery/:name',
    name: 'financeChangeQuery',
    component: financeChangeQuery
  },
  {
    path: '/financeChangeQueryPer',
    name: 'financeChangeQueryPer',
    component: financeChangeQueryPer
  },

  {
    path: '/purchaseBillHandleApply/:pid',
    name: 'purchaseBillHandleApply',
    component: purchaseBillHandleApply
  },
  {
    path: '/purchaseBillApply',
    name: 'purchaseBillApply',
    component: purchaseBillApply
  },
  {
    path: '/purchaseAdvancePayApply',
    name: 'purchaseAdvancePayApply',
    component: () => import('@/view/apply/purchaseInvoice/purchaseAdvancecation'),
    meta: {
      title: '申请人-采购预付款的申请列表'
    }
  },
  {
    path: '/purchaseAdvancePayApproval',
    name: 'purchaseAdvancePayApproval',
    component: () => import('@/view/approve/payment/paymentAdvanceApproval'),
    meta: {
      title: '采购的预付款(待付款审批，已批准)'
    }
  },
  {
    path: '/purchasePaymentApply',
    name: 'purchasePaymentApply',
    component: purchasePaymentApply
  },
  {
    path: '/purchaseInvoiceDetails/:id',
    name: 'purchaseInvoiceDetails',
    component: () => import('@/view/apply/purchaseInvoice/purchaseInvoiceDetails'),
    meta: {
      title: '我的信息详情查看(票据)'
    }
  },
  {
    path: '/purchaseInvoiceDetails3/:id/:pun/:busyid',
    name: 'purchaseInvoiceDetails3',
    component: () => import('@/view/approve/cashier/purchaseInvoiceDetails3'),
    meta: {
      title: '采购部门的票据审核(待付款审批)'
    }
  },
  {
    path: '/purchaseInvoiceDetails4/:id/:sq',
    name: 'purchaseInvoiceDetails4',
    component: () => import('@/view/apply/purchaseInvoice/purchaseInvoiceDetails4'),
    meta: {
      title: '采购预付款的申请'
    }
  },
  {
    path: '/purchasePaymentFinanceFu/:id/:updatePay/:selectOk/:pun/:choose',
    name: 'purchasePaymentFinanceFu',
    component: () => import('@/view/approve/cashier/purchasePaymentFinanceFu'),
    meta: {
      title: '采购部门的票据审核(可付款)'
    }
  },
  {
    path: '/purchasePaymentFinanceAlready/:invoiceID',
    name: 'purchasePaymentFinanceAlready',
    component: () => import('@/view/approve/cashier/purchasePaymentFinanceAlready'),
    meta: {
      title: '已付款'
    }
  },

  {
    path: '/purchasePaymentFinanceCheck/:invoiceID',
    name: 'purchasePaymentFinanceCheck',
    component: () => import('@/view/approve/cashier/purchasePaymentFinanceCheck'),
    meta: {
      title: '待复核的付款'
    }
  },

  {
    path: '/purchasePaymentFinanceMethodInfo/:invoiceID/:type/:id/:pund',
    name: 'purchasePaymentFinanceMethodInfo',
    component: () => import('@/view/approve/cashier/purchasePaymentFinanceMethodInfo'),
    meta: {
      title: '待复核的付款'
    }
  },

  {
    path: '/purchasePaymentMethod/:type/:pund/:mount',
    name: 'purchasePaymentMethod',
    component: () => import('@/view/approve/cashier/purchasePaymentMethod'),
    meta: {
      title: '选择外部承兑汇票'
    }
  },
  {
    path: '/purchasePaymentMethod2/:pund',
    name: 'purchasePaymentMethod2',
    component: () => import('@/view/approve/cashier/purchasePaymentMethod2'),
    meta: {
      title: '录入数据'
    }
  },
  {
    path: '/purchasePaymentInvoiceInfo',
    name: 'purchasePaymentInvoiceInfo',
    component: () => import('@/view/approve/cashier/purchasePaymentInvoiceInfo'),
    meta: {
      title: '查看票据'
    }
  },
  {
    path: '/purchasePaymentFinanceFu2/:id/:pun/:choose/:busyid',
    name: 'purchasePaymentFinanceFu2',
    component: () => import('@/view/approve/cashier/purchasePaymentFinanceFu2'),
    meta: {
      title: '采购部门的票据审核(待复核，待付款)'
    }
  },
  {
    path: '/purchasePaymentApplyApproval',
    name: 'purchasePaymentApplyApproval',
    component: () => import('@/view/approve/payment/purchasePaymentApplyApproval'),
    meta: {
      title: '采购部门的付款'
    }
  },
  {
    path: '/purchasePaymentFinanceFu3/:id/:pun/:yid',
    name: 'purchasePaymentFinanceFu3',
    component: () => import('@/view/approve/cashier/purchasePaymentFinanceFu3'),
    meta: {
      title: '原定的付款方式'
    }
  },
  {
    path: '/purchaseBillHandleApproval/:pid',
    name: 'purchaseBillHandleApproval',
    component: () => import('@/view/approve/purchaseInvoice/purchaseBillHandleApproval'),
    meta: {
      title: '采购的票款处理--采购的常规付款（点击前页面）'
    }
  },
  {
    path: '/purchaseBillApproval',
    name: 'purchaseBillApproval',
    component: purchaseBillApproval
  },
  {
    path: '/purchasePaymentApproval',
    name: 'purchasePaymentApproval',
    component: () => import('@/view/approve/purchaseInvoice/purchasePaymentApproval'),
    meta: {
      title: '采购的票款处理--采购的常规付款（点击后页面）'
    }
  },
  {
    path: '/purchaseFinanceReimburse',
    name: 'purchaseFinanceReimburse',
    component: () => import('@/view/approve/cashier/purchaseFinanceReimburse'),
    meta: {
      title: '付款复核'
    }
  },
  {
    path: '/purchasePaymentFinanceApprovalDetail/:id/:pun/:busyid',
    name: 'purchasePaymentFinanceApprovalDetail',
    component: () => import('@/view/approve/cashier/purchasePaymentFinanceApprovalDetail'),
    meta: {
      title: '付款复核(票据)'
    }
  },
  {
    path: '/purchaseInvoiceDetails2/:id',
    name: 'purchaseInvoiceDetails2',
    component: () => import('@/view/approve/purchaseInvoice/purchaseInvoiceDetails2'),
    meta: {
      title: '采购的部门付款中点击单条数据后的详情页'
    }
  },
  {
    path: '/purchaseInvoiceMore/:id',
    name: 'purchaseInvoiceMore',
    component: () => import('@/view/apply/purchaseInvoice/purchaseInvoiceMore'),
    meta: {
      title: '订单查看'
    }
  },
  {
    path: '/purchaseInvoiceYfk/:id',
    name: 'purchaseInvoiceYfk',
    component: () => import('@/view/apply/purchaseInvoice/purchaseInvoiceYfk'),
    meta: {
      title: '我的信息详情查看(预付款)'
    }
  },
  {
    path: '/purchasePaymentFinanceApprovalDetail2/:id/:pun/:busyid',
    name: 'purchasePaymentFinanceApprovalDetail2',
    component: () => import('@/view/approve/cashier/purchasePaymentFinanceApprovalDetail2'),
    meta: {
      title: '付款复核(预付款)'
    }
  },
  {
    path: '/purchaseInvoiceMoreadd',
    name: 'purchaseInvoiceMoreadd',
    component: purchaseInvoiceMoreadd
  },
  {
    path: '/purchaseInvoiceDetails6/:id',
    name: 'purchaseInvoiceDetails6',
    component: () => import('@/view/approve/purchaseInvoice/purchaseInvoiceDetails6'),
    meta: {
      title: '采购的预付款'
    }
  },
  {
    path: '/needRecoveredAmount',
    name: 'needRecoveredAmount',
    component: () => import('@/view/approve/amountRecovered/needRecoveredAmountList'),
    meta: {
      title: '采购的预付款'
    }
  },

  {
    path: '/amountRecoveredDetails/:id',
    name: 'amountRecoveredDetails',
    component: () => import('@/view/approve/amountRecovered/amountRecoveredDetails'),
    meta: {
      title: '需收回的款'
    }
  },
  {
    path: '/overreceivedFundsApproval',
    name: 'overreceivedFundsApproval',
    component: () => import('@/view/approve/overreceivedMoney/overreceivedMoney'),
    meta: {
      title: '多收来的款'
    }
  },
  {
    path: '/overreceivedMoneyApprove1/:id/:updatePay/:selectOk',
    name: 'overreceivedMoneyApprove1',
    component: () => import('@/view/approve/overreceivedMoney/overreceivedMoneyApprove1'),
    meta: {
      title: '多收来的款-可付款'
    }
  },
  {
    path: '/overreceivedMoneyApprove2/:id/:code/:busyid',
    name: 'overreceivedMoneyApprove2',
    component: () => import('@/view/approve/overreceivedMoney/overreceivedMoneyApprove2'),
    meta: {
      title: '多收来的款-待复核'
    }
  },
  {
    path: '/overreceivedFundsFinance',
    name: 'overreceivedFundsFinance',
    component: () => import('@/view/approve/overreceivedMoney/overreceivedFunds'),
    meta: {
      title: '付款复核-多收来的款'
    }
  },
  {
    path: '/needRecoveredAmountOk/:poLoanId',
    name: 'needRecoveredAmountOk',
    component: () => import('@/view/approve/amountRecovered/needRecoveredAmountOk'),
    meta: {
      title: '需收回的款-已收回'
    }
  },
  {
    path: '/needRecoveredAmountOkInfo/:id',
    name: 'needRecoveredAmountOkInfo',
    component: () => import('@/view/approve/amountRecovered/needRecoveredAmountOkInfo'),
    meta: {
      title: '需收回的款-已收回'
    }
  }
]
