package cn.sphd.miners.modules.accountant.entity;

import org.apache.ibatis.type.Alias;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Alias("TaccountantSubjectChange")
public class TaccountantSubjectChange implements Serializable {

    private static final long serialVersionUID =  7675884414599401232L;

    private Integer id;

    private Integer org;

    @DateTimeFormat(pattern="yyyy-MM-dd")
    private Date changeDate;

    private Boolean isChanged;//0-建账后，1-变动后。建账后所有科目要保存一遍，如果某天对科目有操作，还要为这天整体保存一遍所有科目

    private String subject;

    private String parent;

    private String name;

    private Integer category;

    private String categoryname;

    private Integer level;

    private Integer orders;

    private Byte state;

    private Integer maxChildSubjects;

    private String accountMonth;

    private BigDecimal initialAmount;

    private Double initialQuantity;

    private BigDecimal beginningBalance;

    private BigDecimal previousBalance;

    private Double beginningQuantity;

    private Double previousQuantity;

    private Date carriedForwardDate;

    private BigDecimal balance;

    private Double quantity;

    private String summary;

    private BigDecimal credit;

    private BigDecimal debit;

    private BigDecimal creditAccumulative;

    private Double creditAccumulativeQuantity;

    private BigDecimal debitAccumulative;

    private Double debitAccumulativeQuantity;

    private String beginningDirection;

    private String previousDirection;

    private String endDirection;

    private String balanceDirection;

    private String measureUnit;

    private String quantityAssistingAccounting;

    private String label;

    private String memo;

    private String relevanceType;

    private Integer relevanceItem;

    private Integer establish;

    private Integer creator;

    private String createName;

    private Date createDate;

    private Integer updator;

    private String updateName;

    private Date updateDate;

    private Integer approveItem;

    private String approveStatus;

    private Integer approveLevel;

    private Integer auditor;

    private String auditorName;

    private Date auditDate;

    private String operation;

    private String applyMemo;

    private String approveMemo;

    private Integer messageId;

    private Integer previousId;

    private Integer versionNo;

    private List<Integer> categoryList;//t_accountant_subject_category表的ID列表.科目的category和这个表的ID关联得到类别名称

    public Boolean getChanged() {
        return isChanged;
    }

    public void setChanged(Boolean changed) {
        isChanged = changed;
    }

    public String getCategoryname() {
        return categoryname;
    }

    public void setCategoryname(String categoryname) {
        this.categoryname = categoryname;
    }

    public List<Integer> getCategoryList() {
        return categoryList;
    }

    public void setCategoryList(List<Integer> categoryList) {
        this.categoryList = categoryList;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Date getChangeDate() {
        return changeDate;
    }

    public void setChangeDate(Date changeDate) {
        this.changeDate = changeDate;
    }

    public Boolean getIsChanged() {
        return isChanged;
    }

    public void setIsChanged(Boolean isChanged) {
        this.isChanged = isChanged;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject == null ? null : subject.trim();
    }

    public String getParent() {
        return parent;
    }

    public void setParent(String parent) {
        this.parent = parent == null ? null : parent.trim();
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public Byte getState() {
        return state;
    }

    public void setState(Byte state) {
        this.state = state;
    }

    public Integer getMaxChildSubjects() {
        return maxChildSubjects;
    }

    public void setMaxChildSubjects(Integer maxChildSubjects) {
        this.maxChildSubjects = maxChildSubjects;
    }

    public String getAccountMonth() {
        return accountMonth;
    }

    public void setAccountMonth(String accountMonth) {
        this.accountMonth = accountMonth == null ? null : accountMonth.trim();
    }

    public BigDecimal getInitialAmount() {
        return initialAmount;
    }

    public void setInitialAmount(BigDecimal initialAmount) {
        this.initialAmount = initialAmount;
    }

    public Double getInitialQuantity() {
        return initialQuantity;
    }

    public void setInitialQuantity(Double initialQuantity) {
        this.initialQuantity = initialQuantity;
    }

    public BigDecimal getBeginningBalance() {
        return beginningBalance;
    }

    public void setBeginningBalance(BigDecimal beginningBalance) {
        this.beginningBalance = beginningBalance;
    }

    public BigDecimal getPreviousBalance() {
        return previousBalance;
    }

    public void setPreviousBalance(BigDecimal previousBalance) {
        this.previousBalance = previousBalance;
    }

    public Double getBeginningQuantity() {
        return beginningQuantity;
    }

    public void setBeginningQuantity(Double beginningQuantity) {
        this.beginningQuantity = beginningQuantity;
    }

    public Double getPreviousQuantity() {
        return previousQuantity;
    }

    public void setPreviousQuantity(Double previousQuantity) {
        this.previousQuantity = previousQuantity;
    }

    public Date getCarriedForwardDate() {
        return carriedForwardDate;
    }

    public void setCarriedForwardDate(Date carriedForwardDate) {
        this.carriedForwardDate = carriedForwardDate;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary == null ? null : summary.trim();
    }

    public BigDecimal getCredit() {
        return credit;
    }

    public void setCredit(BigDecimal credit) {
        this.credit = credit;
    }

    public BigDecimal getDebit() {
        return debit;
    }

    public void setDebit(BigDecimal debit) {
        this.debit = debit;
    }

    public BigDecimal getCreditAccumulative() {
        return creditAccumulative;
    }

    public void setCreditAccumulative(BigDecimal creditAccumulative) {
        this.creditAccumulative = creditAccumulative;
    }

    public Double getCreditAccumulativeQuantity() {
        return creditAccumulativeQuantity;
    }

    public void setCreditAccumulativeQuantity(Double creditAccumulativeQuantity) {
        this.creditAccumulativeQuantity = creditAccumulativeQuantity;
    }

    public BigDecimal getDebitAccumulative() {
        return debitAccumulative;
    }

    public void setDebitAccumulative(BigDecimal debitAccumulative) {
        this.debitAccumulative = debitAccumulative;
    }

    public Double getDebitAccumulativeQuantity() {
        return debitAccumulativeQuantity;
    }

    public void setDebitAccumulativeQuantity(Double debitAccumulativeQuantity) {
        this.debitAccumulativeQuantity = debitAccumulativeQuantity;
    }

    public String getBeginningDirection() {
        return beginningDirection;
    }

    public void setBeginningDirection(String beginningDirection) {
        this.beginningDirection = beginningDirection == null ? null : beginningDirection.trim();
    }

    public String getPreviousDirection() {
        return previousDirection;
    }

    public void setPreviousDirection(String previousDirection) {
        this.previousDirection = previousDirection == null ? null : previousDirection.trim();
    }

    public String getEndDirection() {
        return endDirection;
    }

    public void setEndDirection(String endDirection) {
        this.endDirection = endDirection == null ? null : endDirection.trim();
    }

    public String getBalanceDirection() {
        return balanceDirection;
    }

    public void setBalanceDirection(String balanceDirection) {
        this.balanceDirection = balanceDirection == null ? null : balanceDirection.trim();
    }

    public String getMeasureUnit() {
        return measureUnit;
    }

    public void setMeasureUnit(String measureUnit) {
        this.measureUnit = measureUnit == null ? null : measureUnit.trim();
    }

    public String getQuantityAssistingAccounting() {
        return quantityAssistingAccounting;
    }

    public void setQuantityAssistingAccounting(String quantityAssistingAccounting) {
        this.quantityAssistingAccounting = quantityAssistingAccounting == null ? null : quantityAssistingAccounting.trim();
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label == null ? null : label.trim();
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo == null ? null : memo.trim();
    }

    public String getRelevanceType() {
        return relevanceType;
    }

    public void setRelevanceType(String relevanceType) {
        this.relevanceType = relevanceType == null ? null : relevanceType.trim();
    }

    public Integer getRelevanceItem() {
        return relevanceItem;
    }

    public void setRelevanceItem(Integer relevanceItem) {
        this.relevanceItem = relevanceItem;
    }

    public Integer getEstablish() {
        return establish;
    }

    public void setEstablish(Integer establish) {
        this.establish = establish;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName == null ? null : createName.trim();
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName == null ? null : updateName.trim();
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus == null ? null : approveStatus.trim();
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName == null ? null : auditorName.trim();
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation == null ? null : operation.trim();
    }

    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo == null ? null : applyMemo.trim();
    }

    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo == null ? null : approveMemo.trim();
    }

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
}