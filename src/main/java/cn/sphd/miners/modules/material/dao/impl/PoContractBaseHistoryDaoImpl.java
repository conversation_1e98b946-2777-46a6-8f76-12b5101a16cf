package cn.sphd.miners.modules.material.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.material.dao.PoContractBaseHistoryDao;
import cn.sphd.miners.modules.material.entity.PoContractBase;
import cn.sphd.miners.modules.material.entity.PoContractBaseHistory;
import cn.sphd.miners.modules.sales.entity.SlContractBaseHistory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
@Repository
public class PoContractBaseHistoryDaoImpl extends BaseDao<PoContractBaseHistory, Serializable> implements PoContractBaseHistoryDao {
    @Override
    public PoContractBaseHistory insert(PoContractBase poContractBase) {
        PoContractBaseHistory history=new PoContractBaseHistory();

        BeanUtils.copyProperties(poContractBase,history);
        history.setContract(poContractBase.getId());
        history.setId(null);
        save(history);
        getSession().flush();
        return history;
    }
}
