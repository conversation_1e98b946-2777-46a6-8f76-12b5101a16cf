package fr.noop.subtitle.lrc;

import fr.noop.subtitle.model.SubtitleParser;
import fr.noop.subtitle.model.SubtitleParsingException;
import fr.noop.subtitle.util.SubtitlePlainText;
import fr.noop.subtitle.util.SubtitleTextLine;
import fr.noop.subtitle.util.SubtitleTimeCode;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2022年12月07日 09:51
 **/
public class LrcParser implements SubtitleParser {
    private String charset;

    public LrcParser(String charset) {
        this.charset = charset;
    }

    public LrcObject parse(InputStream is) throws IOException, SubtitleParsingException {
        LrcObject lrcObject = new LrcObject();
        BufferedReader br = new BufferedReader(new InputStreamReader(is, this.charset));
        String textLine, lyricLine;
        LrcCue cue = new LrcCue();
        cue.setStartTime(new SubtitleTimeCode(0));
        String regex = "\\[(\\d+):(\\d{1,2}).(\\d+)\\]";
        Pattern pattern = Pattern.compile(regex);

        while ((textLine = br.readLine()) != null) {
            textLine = textLine.trim();
            Matcher matcher = pattern.matcher(textLine);
            if (matcher.find()) {
                // 用于存储当前时间和文字信息的容器
                // System.out.println(m.group(0)); // 例：[02:34.94]
                // [02:34.94] ----对应---> [分钟:秒.毫秒]
                String min = matcher.group(1); // 分钟
                String sec = matcher.group(2); // 秒
                String mill = matcher.group(3); // 毫秒，注意这里其实还要乘以10
                SubtitleTimeCode timeCode = this.parseTimeCode(min, sec, mill);
                if (timeCode.compareTo(cue.getStartTime()) > 0) {
                    if (cue.getLines().size() > 0) {
                        cue.setEndTime(timeCode);
                        lrcObject.addCue(cue);
                    }
                    cue = new LrcCue();
                    cue.setStartTime(timeCode);
                }
                if (StringUtils.isNotEmpty(lyricLine = textLine.substring(matcher.end()).trim())) {
                    SubtitleTextLine line = new SubtitleTextLine();
                    line.addText(new SubtitlePlainText(lyricLine));
                    cue.addLine(line);
                }
            }
        }
        if (cue != null && cue.getLines().size() > 0) {
            cue.setEndTime(new SubtitleTimeCode(cue.getStartTime().getTime() + TimeUnit.SECONDS.toMillis(5)));
            lrcObject.addCue(cue);
        }
        return lrcObject;
    }

    private SubtitleTimeCode parseTimeCode(String hmin, String sec, String mill) throws SubtitleParsingException {
        try {
            int hourminute = Integer.parseInt(hmin);
            int hour = (int) (hourminute / TimeUnit.HOURS.toMinutes(1));
            int minute = (int) (hourminute % TimeUnit.HOURS.toMinutes(1));
            int second = Integer.parseInt(sec);
            int millisecond = Integer.parseInt(mill);
            if(mill.length()<3) {
                millisecond *= (3-mill.length())*10;
            }
            return new SubtitleTimeCode(hour, minute, second, millisecond);
        } catch (NumberFormatException var6) {
            throw new SubtitleParsingException(String.format("Unable to parse time code: %s:%s.%s", hmin, sec, mill));
        }
    }
}