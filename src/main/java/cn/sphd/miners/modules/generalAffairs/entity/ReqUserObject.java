package cn.sphd.miners.modules.generalAffairs.entity;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.system.entity.User;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName ReqUserObject
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/12 10:16
 * @Version 1.0
 */
public class ReqUserObject implements Serializable {
    private String usersList;
    private User user;
    private List<User> userList;
    private List<UserImport> UserImportList;
    private List<User> manageList;//高管列表
    private Integer org;
    private Integer tureImportSum;//可导入总数
    private Integer importSum;//导入总数
    private Integer falseImportSum;//无法导入总数
    private Integer buttonState;//按钮是否可点击 确定按钮状态1- 变亮 0- 置灰
    private PageInfo pageInfo;
    private Integer status;
    private List<UserLock> userLockList;
    private String userlockCreator;//操作人
    private Integer userId;

    public List<UserImport> getUserImportList() {
        return UserImportList;
    }

    public void setUserImportList(List<UserImport> userImportList) {
        UserImportList = userImportList;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getUserlockCreator() {
        return userlockCreator;
    }

    public void setUserlockCreator(String userlockCreator) {
        this.userlockCreator = userlockCreator;
    }

    public List<UserLock> getUserLockList() {
        return userLockList;
    }

    public void setUserLockList(List<UserLock> userLockList) {
        this.userLockList = userLockList;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<User> getManageList() {
        return manageList;
    }

    public void setManageList(List<User> manageList) {
        this.manageList = manageList;
    }

    public String getUsersList() {
        return usersList;
    }

    public void setUsersList(String usersList) {
        this.usersList = usersList;
    }

    public Integer getButtonState() {
        return buttonState;
    }

    public void setButtonState(Integer buttonState) {
        this.buttonState = buttonState;
    }

    public List<User> getUserList() {
        return userList;
    }

    public void setUserList(List<User> userList) {
        this.userList = userList;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getImportSum() {
        return importSum;
    }

    public void setImportSum(Integer importSum) {
        this.importSum = importSum;
    }

    public Integer getFalseImportSum() {
        return falseImportSum;
    }

    public void setFalseImportSum(Integer falseImportSum) {
        this.falseImportSum = falseImportSum;
    }

    public Integer getTureImportSum() {
        return tureImportSum;
    }

    public void setTureImportSum(Integer tureImportSum) {
        this.tureImportSum = tureImportSum;
    }



}
