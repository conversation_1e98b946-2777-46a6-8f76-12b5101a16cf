<template>
  <div id="responsePlaneDetails">
    <TY_NavTop title="应对方案" ></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading" v-if="details.outer_sn">
      <p class="tabtip">
        下面商品的基本信息近期被修改了，并且该要货计划所属的订单还没完结。
      </p>
      <p class="grayBg">
        商品代号/名称/规格/型号
      </p>
      <div>
        <span class="tttl">原数据</span><span>
          {{ details.outer_sn }} / {{ details.outer_name }} / {{ details.specifications }} / {{ details.model }}
        </span>
        <br>
        <span class="tttl">新数据</span>
        <span>
          {{ details.new_outer_sn }} / {{ details.new_outer_name }} / {{ details.new_specifications }} / {{ details.new_model }}
        </span>
      </div>
      <p class="grayBg">
        要货计划
      </p>
      <div>
        <span class="ttl2">订购数量/已发货数量</span><span>{{ details.amount || 0 }} / {{ details.send_amount || 0 }}</span>
      </div>
      <div>
        <span class="ttl2">所属订单的订单号</span><span>{{ details.sn }}</span>
      </div>
      <div>
        <span class="ttl2">客户/订单收到日期</span><span>{{ details.customer_name }}/ {{ details.sign_date |formatDay('YYYY-MM-DD') }}</span>
      </div>

      <div class="marTop" v-if="type==='1'">
        <el-form ref="square" style="margin-top:10px;">
          <p>针对该要货计划，以下事项需您确认：</p>
          <el-form-item prop="method">
            <p>
              <i class="red">*</i>1、对已发货的{{ details.outer_name }}需采取什么措施？
              <span class="ty-right">{{ txt.length }} / 100</span>
            </p>
            <el-input type="textarea" :rows="2" placeholder="请录入类似“需紧急更换标签”、“无需采取措施”等内容" v-model="txt"></el-input>
          </el-form-item>
          <el-form-item prop="newOr">
            <p><i class="red">*</i>2、未发货的{{ details.outer_name }}采用原数据还是新数据？</p>
            <el-radio-group v-model="newOr">
              <el-radio :label="0">原数据</el-radio>
              <el-radio :label="1">新数据</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <div class="handle_button" style="text-align: center; padding-bottom: 40px">
          <span style="width:80%; margin-top:20px;" class="ui-btn ui-btn_info" @click="approve()">确 定</span>
        </div>
      </div>

      <div v-if="type==='2'">
        <p>
          这条要货计划的处置方案如下：
        </p>
        <div>
          对已发货的{{ details.outer_name }}需采取的措施：{{ details.delivered_measure }} <br>
          未发货的{{ details.outer_name }}采用{{ details.undelivered_measure == 1 ? '新' : '原' }}数据。
        </div>
        <p>&nbsp; <span class="ty-right">{{ details.update_name }} {{ details.update_date |formatDay('YYYY-MM-DD HH:mm:ss') }}</span></p>

        <div v-if="isSend === 1">
          <p class="blueTip">注：物流人员签收之前，上述方案还可修改！</p>
          <div class="handle_button" style="text-align: center;">
            <span style="width:80%; margin-top:20px;" class="ui-btn ui-btn_info" @click="edit()">修  改</span>
          </div>
        </div>
        <div v-if="isSend === 2">
          <p class="blueTip">说明1 您如对上述处置方案有疑问，暂时就不要操作，请及时向信息发出者反馈！</p>
          <p class="blueTip">说明2 上述处置方案仅此处可见，订单里没有展示！</p>
          <p>
            <el-checkbox v-model="zhiOr" label=""></el-checkbox> 已知晓上述事项，对处置方案无异议，现予签收，并将按照执行！
          </p>

          <div class="handle_button">
            <span style="width:80%; margin-top:20px;" class="ui-btn ui-btn_info" @click="signSure">确  定</span>
          </div>
        </div>
      </div>
      <div v-if="type==='3'">
        <p>
          这条要货计划的处置方案如下：
        </p>
        <div>
          对已发货的{{ details.outer_name }}需采取的措施：{{ details.delivered_measure }} <br>
          未发货的{{ details.outer_name }}采用{{ details.undelivered_measure == 1 ? '新' : '原' }}数据。
        </div>
        <p>&nbsp; <span class="ty-right">{{ details.update_name }} {{ details.update_date |formatDay('YYYY-MM-DD HH:mm:ss') }}</span></p>
        <div>
          物流负责人员已知晓上述事项，对处置方案无异议，已予签收，并将按照执行！
        </div>
        <p>&nbsp; <span class="ty-right">{{ details.signer }} {{ details.s_date |formatDay('YYYY-MM-DD HH:mm:ss') }}</span></p>
      </div>

    </div>
  </div>
</template>
<style>
#responsePlaneDetails .el-radio{
  display: inline-block;
}
</style>
<style lang="less" scoped>
#responsePlaneDetails{
  line-height: 30px;
  background: #fff;
  .red{ color: red;  }
  .linkCon .el-link{ font-size: 12px; }
  .linkBtn{ color:#5d9cec; cursor:default; margin-right:20px; }
  .linkBtn:hover{ text-decoration:underline;  }
  .marTop{ margin-top: 40px; }
  .ty-right{ float: right;  }
  .blueTip{
    color:#5d9cec;
    font-size: 0.8em;
  }
  .grayBg{
    background: #eee;
    padding:0 10px;
    line-height: 40px;
  }
  .tabtip{
    padding:10px;
    background: #fff;
  }
  .container>div{
    padding: 0 16px;
  }
  .tttl{ display: inline-block; width: 46px; }
  .ttl2{ display: inline-block; width: 140px; }
  .handle_button{
    text-align: center;
    padding-bottom: 40px;
  }

}
</style>
<script>
import auth from '@/sys/auth'

export default {
  name: 'responsePlaneDetails',
  data () {
    return {
      goodsID: '',
      type: '',
      isSend: '',
      isEdit:false,
      loading: false,
      details:{},
      txt:'',
      zhiOr:'',
      newOr:''
    }
  },
  created () {
    let that = this
    that.goodsID = this.$route.params.id
    let newType = this.$route.params.type
    let tArr = newType.split(',')
    that.type = tArr[0]
    that.isSend = Number(tArr[1])
    that.getDetail()
  },
  methods: {
    edit:function () {
      this.isEdit = true
      this.type = '1'

    },
    signSure:function () {
      let that = this
      if(!this.zhiOr){
        that.$kiko_message('请先勾选"已知晓上述事。。')
        return false
      }
      let url = '../../../sales/pd/sign'
      let data = {
        itemId:this.goodsID,
        sign:this.zhiOr ? 1 : 0
      }
      that.loading = true
      this.axios.post(url, data)
        .then(function (response) {
          let res = response.data
          console.log('response.data.data=', response)
          if(res == 1){
            that.$kiko_message('操作成功')
            that.loading = false
            that.$router.push({
              path: `/productChangePlanApproval`
            })
          }

        })
        .catch(function (error) {
          console.log(error)
          that.loading = false
          that.$kiko_message('操作失败')

        })


    },
    approve:function () {
      let that = this
      let url = '../../../sales/pd/suHandle.do'
      let data = {
        itemId:this.goodsID,
        deliveredMeasure: this.txt,
        undeliveredMeasure: this.newOr,// 未发货处理方案:0-原数据,1-新数据
      }
      if(this.txt.length === 0 ){
        that.$kiko_message('请录入需采取什么措施！')
        return false
      }else if(this.newOr === '' ){
        that.$kiko_message('请录入未发货处理方案！')
        return false
      }
      that.loading = true
      this.axios.post(url, data)
        .then(function (response) {
          let res = response.data
          console.log('response.data.data=', response)
          if(res.id){
            that.$kiko_message('操作成功')
            that.loading = false
            that.$router.push({
              path: `/productChangePlanApproval`
            })
          }else{
            that.$kiko_message('链接错误')
          }

        })
        .catch(function (error) {
          console.log(error)
          that.loading = false
          that.$kiko_message('操作失败')

        })

    },
    jumpExplain: function () {
      this.$router.push({
        path: `/approveReimburseDetailExplain`
      })
    },
    getDetail: function () {
      let that = this
      that.loading = true
      this.axios.post('../../../sales/pd/suDetail', {
        'itemId': that.goodsID,
      })
      .then(function (response) {
        console.log('suDetail =', response)
        let res = response.data.data
        that.details = res[0]
        console.log('that.details = ', that.details )
        that.loading = false

      })
      .catch(function (error) {
        console.log(error)
      })
    },


  }
}
</script>
