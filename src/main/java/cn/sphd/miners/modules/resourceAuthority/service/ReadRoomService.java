package cn.sphd.miners.modules.resourceAuthority.service;

import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.resourceAuthority.entity.ReadingRoomEntity;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.BadgeNumberCallback;

import java.util.List;

/**
 * Created by 朱思旭 on 2020/10/23.
 */
public interface ReadRoomService extends BadgeNumberCallback {
    //新增一个借阅文件
    Integer applyBorrowingFile(User user, Integer fileId, String type, Integer auditor, String auditorName, String idType);

    //中间审批和终极审批借阅文件
    String handleBorrowFile(User user, Integer borrowId, Integer processId, String approveStatus, String approveMemo,
                            Integer toUser, String toUserName, String type);

    //获取一个借阅文件实体
    ReadingRoomEntity getReadingFile(Integer id);

    //申请人获取借阅文件
    QueryData getApplyReadFile(Integer userID);

    //中间审批人获取借阅文件
    QueryData getApproveReadFile(Integer userID);

    //终极审批人获取借阅文件
    List<ReadingRoomEntity> getLastReadFile(Integer oid);

    //查询借阅文件
    List<ReadingRoomEntity> findReadFileByPage(QueryData query, String type);

    //获取某个借阅文件的详情
    QueryData readFileMes(Integer borrowId, User user);

    //获取某个文件最新的保存位置
    String getNewFilePlace(Integer fileId);

    //定时任务方法清除掉30前借阅的文件
    void cleanedReadFileByDaysAgo();

    //新增一个借阅讨论
    void applyBorrowingForum(User user, Integer postId, String type, Integer auditor, String auditorName);

    //中间审批和终极审批借阅讨论
    String handleBorrowForum(User user, Integer borrowId, Integer processId, String approveStatus, String approveMemo,
                             Integer toUser, String toUserName, String type);

    //申请人获取借阅讨论
    QueryData getApplyReadForum(Integer userID);

    //中间审批人获取借阅讨论 待审批 已批准
    QueryData getApproveReadForum(Integer userID);

    //终极审批人获取借阅讨论
    List<ReadingRoomEntity> getLastReadForum(Integer oid);

    //查询借阅讨论
    List<ReadingRoomEntity> findReadForumByPage(QueryData query, String eventType);

    //获取借阅讨论详情
    QueryData readForumPostMes(Integer borrowId, User user);
}
