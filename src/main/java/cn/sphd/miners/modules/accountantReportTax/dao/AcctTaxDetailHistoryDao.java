package cn.sphd.miners.modules.accountantReportTax.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.accountantReportTax.dto.PayTaxDto;
import cn.sphd.miners.modules.accountantReportTax.entity.AcctTaxDetailHistoryEntity;
import cn.sphd.miners.modules.system.entity.User;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public interface AcctTaxDetailHistoryDao extends IBaseDao<AcctTaxDetailHistoryEntity,Serializable> {
    Map<String,Object> getEditRecord(AcctTaxDetailHistoryEntity param);

    List<PayTaxDto> getPayTaxList(User user, Integer detailId);
}
