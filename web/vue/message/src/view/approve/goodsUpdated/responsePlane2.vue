<template>
  <div id="responsePlane" :class="orderManage ? 'klass3':'klass2'">
    <TY_NavTop title="应对方案"></TY_NavTop>
    <TY_Tabs :value="tabValue">
       <TY_TabPane label="待签收" name="2" closable='false' :msgCount="list2.length">
        <div class="tabtip" >
          以下各条数据商品的基本信息已被修改，而所属订单尚未完成。针对销售确定的方案，请及时确认并签收！
        </div>

        <div>
          <div class="liItem" v-for="(item, index) in list2" v-bind:key="index">
            <a class="ui-cell" v-on:click="jump(item.item_id,2)" >
              <div class="ui-cell__bd">
                <div class="ui-cell_con">
                  {{ item.outer_sn }} / {{ item.outer_name }} / {{ item.specifications }} / {{ item.model }} / {{ item.amount }}
                </div>
              </div>
              <div class="ui-cell__ft"></div>
            </a>
          </div>
        </div>
      </TY_TabPane>
      <TY_TabPane label="已签收" name="3" closable='false'>
        <div class="tabtip">
          以下各条要货计划的应对方案物流人员已签收！
        </div>
        <div>
          <div  class="liItem" v-for="(item, index) in list3" v-bind:key="index">
            <a class="ui-cell" v-on:click="jump(item.item_id,3)" >
              <div class="ui-cell__bd">
                <div class="ui-cell_con">
                  {{ item.outer_sn }} / {{ item.outer_name }} / {{ item.specifications }} / {{ item.model }} / {{ item.amount }}
                </div>
              </div>
              <div class="ui-cell__ft"></div>
            </a>
          </div>

        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>
<style lang="less">
#responsePlane{
  &.klass2{
    .tabs-tab{
      width: 50%;
    }
  }
  &.klass3{
    .tabs-tab{
      width: 33%;
    }
  }
  .tabtip{
    padding:10px;
    background: #fff;
    margin-bottom: 16px;
  }
  .liItem{
    border-bottom: 1px solid #ddd;
  }
  div.liItem:nth-child(odd){
    background: #fff;
  }


}
</style>
<script>
import auth from '@/sys/auth'

export default {
  name: 'productChangePlanApproval',
  data () {
    return {
      list1: [],
      list2: [],
      list3: [],
      listenersUid: [],
      nowTime: 0,
      orderManage: '',
      tabValue: '1'
    }
  },
  computed: {
    toHandleCount: function () {
      return this.toHandle.length
    }
  },
  created () {
    let that = this
    // let mainMenu = localStorage.getItem('mainMenu')
    // mainMenu = JSON.parse(mainMenu)
    let orderManage = false
    // mainMenu.forEach( menuItem => {
    //   if(menuItem.mid === 'qc'){
    //     console.log('menuItem=', menuItem)
    //     orderManage = menuItem
    //   }
    // })
    this.orderManage = orderManage


    if(orderManage){ // 有订单管理
      this.tabValue = '1'
      that.getList(1)
      that.getList(2)
      that.getList(3)
    }else {
      this.tabValue = '2'
      that.getList(2)
      that.getList(3)
    }

    this.listenersUid = [
      // 修改商品信息
      this.sphdSocket.subscribe('productChange', function (data) {
        let getData = JSON.parse(data)
        console.log('修改商品信息=', getData)
        if(this.orderManage){
          this.tabValue = '1'
          that.getList(1)
          that.getList(2)
          that.getList(3)
        }else{
          this.tabValue = '2'
          that.getList(2)
          that.getList(3)
        }
      }, null, 'user'),
      // 待处理通道名：
      this.sphdSocket.subscribe('productChangePlan', function (data) {
        let getData = JSON.parse(data)
        console.log('待处理通道名=', getData)
        if(this.orderManage){
          this.tabValue = '1'
          that.getList(1)
          that.getList(2)
          that.getList(3)
        }else{
          this.tabValue = '2'
          that.getList(2)
          that.getList(3)
        }
      }, null, 'user'),
      // 待签收通道名：
      this.sphdSocket.subscribe('productChangeSign', function (data) {
        let getData = JSON.parse(data)
        console.log('待签收通道名=', getData)
        if(this.orderManage){
          this.tabValue = '1'
          that.getList(1)
          that.getList(2)
          that.getList(3)
        }else{
          this.tabValue = '2'
          that.getList(2)
          that.getList(3)
        }
      }, null, 'user'),

    ]


  },
  methods: {
    jump: function (id, type) {
      // type : 1 - 方案待提出 ; 2 - 待签收 ; 3 - 已签收
      let newType = type + ',' + 2 //  物流的
      this.$router.push({
        path: `/responsePlaneDetails/${id}/${newType}`
      })
    },
    getList: function (type) {
      let that = this
      this.axios.post('../../../sales/pd/suList', { 'type': type })
        .then(function (response) {
          console.log('suList response', response)
          let res = response.data.data
          that[`list${ type }`] = res || []
          console.log(`that['list${type}'] = `, that[`list${ type }`])

        })
        .catch(function (error) {
          console.log(error)
        })
    }

  }
}
</script>
