package cn.sphd.miners.modules.dailyAffairs.service.impl;

import cn.sphd.miners.modules.personal.service.OvertimeService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.socket.service.DelayCallback;
import org.springframework.context.ApplicationContext;

/**
 * Created by Administrator on 2018/10/23.
 */
public class OverturnOverTime implements DelayCallback{
    private Integer id;
    private Integer type;

    public Integer getId() {
        return id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

//    public OverturnOverTime(Integer id) {
//        this.id = id;
//    }

    public OverturnOverTime(Integer id,Integer type) {
        this.id = id;
        this.type = type;
    }

    @Override
    public void delayCall(ClusterMessageSendingOperations clusterMessageSendingOperations, ApplicationContext ac) {
        OvertimeService overtimeService = ac.getBean(OvertimeService.class);
        overtimeService.overturn(id,type);
    }
}
