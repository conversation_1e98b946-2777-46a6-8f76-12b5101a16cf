<template>
  <div id="purchasePaymentFinanceAlready" style="overflow-y: auto;" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="已付款"></TY_NavTop>
    <div class="detailInfo">
      <!--票据-->
      <div v-if="pund === 0">
        <div>
          <span><span class="ttl1">申请付款金额 </span><span> {{detailsInfo.amount && detailsInfo.amount.toFixed(2)}} 元 </span></span>
        </div>
        <div>
          <span class="ttl1">收款单位</span><span>  {{details.supplierName }}</span>
        </div>
        <div>
          <span class="ttl1">已付款</span><span>{{detailsInfo.payNum || 0 }}笔，共{{(detailsInfo.paidAmount && detailsInfo.paidAmount.toFixed(2)) || 0 }} 元 </span>
        </div>
      </div>
      <!--多收来的款-->
      <div v-else-if="pund === 3">
        <div>
          <span class="ttl1">原始的付款方</span><span> {{detailsInfo.supplierName }} </span>
        </div>
        <div>
          <span><span class="ttl1">多收来的金额 </span><span>  {{detailsInfo.amount && detailsInfo.amount.toFixed(2)}} 元 </span></span>
        </div>
        <div>
          <span class="ttl1">已付款</span><span> {{detailsInfo.paidTimes || 0 }}笔，共{{(detailsInfo.paidAmount && detailsInfo.paidAmount.toFixed(2)) || 0 }}元 </span>
        </div>
      </div>
      <div v-else>
        <div>
          <span><span class="ttl1">申请付款金额 </span><span>  {{detailsInfo.planAmount && detailsInfo.planAmount.toFixed(2)}} 元 </span></span>
        </div>
        <div>
          <span class="ttl1">收款单位</span><span> {{details.srmSupplier.fullName }} </span>
        </div>
        <div>
          <span class="ttl1">已付款</span><span> {{detailsInfo.payNum || 0 }}笔，共{{(detailsInfo.paidAmount && detailsInfo.paidAmount.toFixed(2)) || 0 }} </span>
        </div>
      </div>
    </div>
    <div class="marT20">
      <el-table :data="list" class="tbddd"  stripe  style="margin:0 auto;">
      <el-table-column
        prop="date"
        label="付款完成时间">
      </el-table-column>
        <el-table-column
          label="付款方式及金额">
          <template slot-scope="scope">
            <span class="tdCtrl" @click="getPayDetails(scope.row)">
              {{ scope.row.amount }}
               <i class="el-icon-arrow-right marL"></i>
            </span>
          </template>
        </el-table-column>
    </el-table>
    </div>
  </div>
</template>
<style lang="less" scoped>
.tdCtrl{
  display: block;
  cursor: pointer;
  position: relative;
}
.tbddd .el-table__header-wrapper{
  position: relative;
    left: 18px;
}
  .marT20{
    margin-top: 20px;
  }
  .detailInfo{
    padding:10px 20px 10px 20px;
    background:#fff; font-size:14px;
    border-bottom:1px solid #ddd ;
    line-height: 26px;
    .ttl1{
      display: inline-block;
      width: 86px;
      text-align: right;
      margin-right: 10px;
    }
  }
</style>
<script>
import * as moment from 'moment'
export default {
  name: 'purchasePaymentFinanceAlready',
  data () {
    return {
      invoiceID: '',
      pund: '',
      loading: true,
      toggle: -1,
      detailsInfo: {},
      details: {
      },
      list: []
    }
  },
  created () {
    this.invoiceID = this.$route.params.invoiceID
    this.details = this.$store.getters.getPurchaseFinanceInfo
    console.log('getPurchaseFinanceInfo', this.details)
    let type = Number(this.details.pund)
    if(isNaN(type)){
      this.$router.back(-1)
      return false
    }
    this.pund = type
    if (type === 1) {
      this.detailsInfo = this.details.poOrdersPrepayment
    } else  if (type === 3) {
      this.detailsInfo = this.details.loanBiz || this.details.overLoanBiz
    } else {
      this.detailsInfo = this.details.poPaymentApplication
    }
    this.getList(type)
  },
  destroyed: function () {
  },
  methods: {
    getPayDetails:function (itemTr) {
      console.log('点击tr:', itemTr)
      this.$router.push({
        path: `/purchasePaymentFinanceMethodInfo/${this.invoiceID}/2/${itemTr.id}/${this.details.pund}`
      })
    },
    formatMethod (item) {
      let str = ''
      switch (Number(item.method)) {
        case 1:
          str = '现金'; break
        case 5:
          str = '银行转账'; break
        case 4:
          str = '承兑汇票（外）'; break
        case 3:
          if (Number(item.cheque) > 0) { // cheque & returnBill 为 支票ID
            str = '转账支票（内）'; break
          } else if (Number(item.returnBill) > 0) {
            str = '转账支票（外）'; break
          } else {
            str = `未识别支付方式`
          }
          break
        default:
          str = `未识别支付方式:${item.method}`
      }
      return str
    },
    getList: function (type) {
      let that = this
      that.loading = false
      let params = { status: 9 }
      let url = `../../../purchaseInvoice/payDetail.do`
      if (Number(type) === 1) { // 预付款
        params.orderPrepaymentId = this.detailsInfo.id
      } else if (Number(type) === 3) { // 多收来的款
        params.applicationId = this.detailsInfo.id
        url = `../../../overpayment/payOverpaymentList.do`
      } else {
        params.applicationId = this.detailsInfo.id
      }
      this.axios.post(url, params)
        .then(({ data }) => {
          console.log('payDetail', data)
          let arr = data.data.invoiceDetails || []
          arr.forEach(item => {
            let methodStr = this.formatMethod(item)
            that.list.push({
              id: item.id ,
              date: moment(item.updateDate).format('YYYY-MM-DD HH:mm:ss'),
              amount: `${methodStr} - ${item.amount && item.amount.toFixed(2)}元`
            })
          })
        })
        .catch(function (error) {
          console.log(error)
        })
    }
  },
  computed: {
  }
}
</script>
