{"name": "file", "version": "0.0.0", "scripts": {"serve": "vite --mode development --host 0.0.0.0 --port 443", "dev": "vite --mode development", "build": "vite build --mode production", "preview": "vite preview --port 5050", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"vant": "^3.6.4", "vue": "^3.2.33", "vue-router": "^4.0.14"}, "devDependencies": {"@vitejs/plugin-vue": "^2.3.1", "axios": "^0.27.2", "dayjs": "^1.11.7", "element-plus": "^2.2.2", "eslint": "^8.5.0", "eslint-plugin-vue": "^8.2.0", "hls.js": "^1.2.9", "js-base64": "^3.7.2", "js-cookie": "^3.0.1", "json-bigint": "^1.0.0", "qs": "^6.10.3", "sass": "^1.56.0", "sass-loader": "^13.1.0", "vconsole": "^3.15.0", "vite": "^2.9.5", "weixin-js-sdk": "^1.6.0"}}