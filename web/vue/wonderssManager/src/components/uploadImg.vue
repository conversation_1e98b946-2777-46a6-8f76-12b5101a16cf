<!--
  上传图片子组件
----desc：该子组件不是全局公用组件
----pages（用到的页面）：['views/mainPage.vue', ]
-->
<template>
 <div class="uploadImg">
   <el-upload ref="upload"
      :headers="uploadHeaders"
      :limit="1"
      :show-file-list="false"
      :multiple="false"
      :action="uploadAction"
      :accept="uploadLyc"
      :data="postData"
      :auto-upload="true"
      :before-remove="beforeRemove"
      :before-upload="beforeUpload"
      :on-change="handleChange"
      :on-preview="handlePreview"
      :on-remove="uploadRemove"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-progress="handleProgress"
      :on-exceed="handleExceed"
   >
     <template #trigger>
       <slot name="btnArea"></slot>
<!--       <el-button size="small" type="primary">选取文件</el-button>-->
     </template>

<!--

     <el-button
         style="margin-left: 10px;"
         size="small"
         type="success"
         @click="submitUpload"
     >上传到服务器</el-button>
-->


   </el-upload>


   <div


   >

   </div>

 </div>
</template>
<script>
import auth from '@/sys/auth'

export default {
  name: "uploadImg",
  data() {
    return {
      uploadHeaders:{ 'Token': auth.getToken() },
      uploadAction: auth.webRoot + '/uploads/uploadfyByFile.do',
      uploadLyc: '.png,.jpeg,.jpg,.gif',
      postData: {
        module: '未知', userId: auth.getUserID()
      },
    };
  },
  props:{
    module: { 'type':String, 'default':'' },
    removeFun: { 'type':Function, 'default': null },
    successFun: { 'type':Function, 'default': null },
    beforeUploadFun: { 'type':Function, 'default': null },
  },
  mounted() {
    this.postData.module = this.module

  },
  methods: {
    beforeRemove(file, fileList) {
      console.log('beforeRemove 0000')
      console.log('this.postData=', this.postData)
      console.log(' uploadAction ', this.uploadAction)
      console.log(' file ', file)
      console.log(' fileList ', fileList)

    },
    submitUpload() {
      console.log('submitUpload 出发了')
      console.log('this.$refs=', this.$refs)
      console.log('this.$refs.upload=', this.$refs.upload)
      this.$refs.upload.submit()
    },
    handleExceed(files, fileList){
      console.log('handleExceed  ' )
    },
    handleProgress(event, file, fileList){
      console.log('handleProgress file=', file )
    },
    handleError(err, file, fileList){
      console.log('handleError file=', file )
    },
    handlePreview(file){
      console.log('handlePreview file=', file )
    },
    handleChange(file){
      console.log('handleChange file=', file )
    },
    beforeUpload(file){
      console.log('子组件的beforeUpload file=', file )
      console.log('uploadHeaders=', this.uploadHeaders)
      // this.beforeUploadFun( file)
    },
    handleSuccess(res,file,fileList){
      console.log('handleSuccess 调用了')
      this.successFun(res,file)
    },
    uploadRemove(file, fileList){
      console.log('uploadRemove 调用了 文件列表移除文件时的钩子')
      console.log('file =', file)
      // this.removeFun(res, file)
    },

  },
}
</script>

<style lang="scss" scoped>
.uploadImg{

}
</style>