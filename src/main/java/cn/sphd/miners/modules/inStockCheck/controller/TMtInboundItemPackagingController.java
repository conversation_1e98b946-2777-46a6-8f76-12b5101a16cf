package cn.sphd.miners.modules.inStockCheck.controller;
import cn.sphd.miners.modules.inStockCheck.entity.TMtInboundItemPackaging;
import cn.sphd.miners.modules.inStockCheck.service.impl.TMtInboundItemPackagingServiceImpl;
import org.springframework.web.bind.annotation.*;

import org.springframework.beans.factory.annotation.Autowired;

/**
* 材料_入库申请单明细包装表20241015 1.316入库 新增(t_mt_inbound_item_packaging)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/t_mt_inbound_item_packaging")
public class TMtInboundItemPackagingController {
/**
* 服务对象
*/
    @Autowired
    private TMtInboundItemPackagingServiceImpl tMtInboundItemPackagingServiceImpl;

    /**
    * 通过主键查询单条数据
    *
    * @param id 主键
    * @return 单条数据
    */
    @GetMapping("selectOne")
    public TMtInboundItemPackaging selectOne(Integer id) {
    return tMtInboundItemPackagingServiceImpl.selectByPrimaryKey(id);
    }

}
