package cn.sphd.miners.modules.resourceAuthority.mapper;

import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.accountant.mapper.BaseMapper;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.resourceAuthority.dto.ResNumDto;
import cn.sphd.miners.modules.resourceAuthority.entity.ResCategory;
import cn.sphd.miners.modules.resourceAuthority.entity.ResEntity;
import org.springframework.stereotype.Component;

import javax.xml.namespace.QName;
import java.util.HashMap;
import java.util.List;

/**
 * Created by 朱思旭 on 2017/10/30.
 */
@Component
public interface ResMapper extends BaseMapper<ResEntity> {

    //根据user和category来查看文件
    List<ResEntity> getFileByCategoryAndUserId(ResEntity re);

    //根据user和categoryhe和approveStatus来查看文件
    List<ResEntity> getFileByCategoryAndUserIdAndapproveStatus(ResEntity re);

    //根据category来查看文件
    List<ResEntity> getFileBycategory(Integer category);

    //获取某category下的一条文件(现在这里都是没有加权限的，要是测出问题在改改)
    ResEntity getOneFileByCategorylistPage(HashMap<String, Object> query);

    //根据category获取文件ids
    List<Integer> getFileIdsByCategory(Integer category);

    //根据user和category来分页获取文件(带权限)
    List<ResEntity> filelistPage(QueryData query);

    //根据category来分页获取文件
    List<ResEntity> fileByManagerlistPage(QueryData query);

    //根据某机构的所有文件夹获取所有的文件
    List<ResEntity> getAllFileByListCategory(HashMap map);

    //普通模糊搜索文件(有权限限制)
    List<ResEntity> searchfilelistPage(QueryData query);

    //高级模糊搜索文件(有权限限制)
    List<ResEntity> searchFilelistPage(QueryData query);

    //普通模糊搜索文件(无权限限制)
    List<ResEntity> searchfileByAuthlistPage(QueryData query);

    //高级模糊搜索文件(无权限限制)
    List<ResEntity> searchFileByAuthlistPage(QueryData query);

    //模糊匹配编号前几位一样的文件
    List<ResEntity> searchFileByHeadNum(QueryData query);

    //普通模糊搜索全部文件（无权限限制）
    List<ResEntity> searchfile(QueryData query);

    //普通模糊搜索全部文件(有权限限制)
    List<ResEntity> searchfileByAuth(QueryData query);

    //核对现有文件编号是否重复
    Integer checckFileSnByFile(HashMap map);

    //统计某机构文件个数或某文件夹下的个数
    ResCategory getFileNum(HashMap map);

    //统计某机构有效文件大小或某文件夹下有效文件大小
    Long getFileSize(HashMap map);

    //修改某文件夹下文件的categoryPath
    Integer updateResourceCategoryPath(ResEntity re);

    // 获取某机构删除的文件
    List<ResEntity> getFileByValid(HashMap<String,Object> map);

    //统计某机构要删除的文件的个数
    Integer getFileByValidNum(Integer oid);

    //模糊查询获取三种属性的次数
    ResNumDto getResNumDtoByCategoryPath(String categoryPath);

    //回复废止文件为正常的
    Integer reuserFile(ResEntity r);
}
