<template>
  <div class="mainPage">
    <el-container @click="hideFun" v-if="user">
      <el-aside width="185px" >
        <el-container>
          <el-header class="header">
            <div class="header_left">
               <div class="logo_avatar">
                 <div class="logo">
                   <span>W</span>onderss
                 </div>
               </div>
              <!--            <span class="menuBtn fa fa-navicon"></span>-->
            </div>
          </el-header>
          <el-main class="menuBg" >
            <div class="menuBgCon" :style="{ height:menuHeight + 'px', }">
              <div class="navList">
                <div class="menu1" v-for="(menu1, index1) in treeMenu" :key="index1">
                  <div @click="menuClick(menu1,1)" :class="{menuItem: true, active: menu1.active, collapse: menu1.collapse}">
                    <div class="menuIcon" :class="menu1.icon"></div>
                    <div class="menuTitle" v-html="menu1.name"></div>
                    <div class="menuAngle" v-if="menu1.children.length > 0">
                      <i class="fa fa-angle-right"></i>
                    </div>
                  </div>
                  <div class="menu1Son" v-if="menu1.children.length > 0" v-show="menu1.collapse" >
                    <div class="menu2" v-for="(menu2, index2) in menu1.children" :key="index2">
                      <div @click="menuClick(menu2,2)" :class="{menuItem: true, active: menu2.active, collapse: menu2.collapse}">
                        <div class="menuIcon" :class="menu2.icon"></div>
                        <div class="menuTitle" v-html="menu2.name"></div>
                        <div class="menuAngle" v-if="menu2.children.length > 0">
                          <i class="fa fa-angle-right"></i>
                        </div>
                      </div>
                      <div class="menu2Son" v-if="menu2.children.length > 0" v-show="menu2.collapse" >
                        <div class="menu3" v-for="(menu3, index3) in menu2.children" :key="index3">
                          <div @click="menuClick(menu3,3)" :class="{menuItem: true, active: menu3.active}">
                            <span class="menuIcon" :class="menu3.icon"></span>
                            <span v-html="menu3.name"></span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-main>
        </el-container>
      </el-aside>
      <el-container style="background-color: #fff;">
        <el-header class="header" style="padding-left: 0">
          <div class="header_main">
            <div class="icon icon_home" @click="openHome" :class="{active: activeMenuMid === 'wodezhuomian'}">
              <SvgIcon name="home"/>
            </div>
            <div class="currentRole"><span class="header_des">您的当前身份为</span><span class="currentRole_txt">{{ curRole }}</span></div>
            <div class="otherRole">
              <span class="header_des">其他身份</span>
              <div class="role_avatar">
                <div class="role_item" v-for="(role , rIndex) in roleList" :key="rIndex">
                  <span class="role_name" :title="role.desc" @click="roleClick(role)">{{role.name}} ({{role.msgCount}})</span>
                </div>
              </div>
            </div>
          </div>
          <div class="header_right">
            <div :class="['icon', 'icon_commenting', {dot: discussionTwink}]" @click="jumpPage('../forum/discussionIndex.do')" title="讨论区" v-if="isShowDiscuss">
              <SvgIcon name="discuss"/>
            </div>
            <div class="icon icon_envelope" @click="jumpPage('../forum/messagePage.do')">
              <SvgIcon name="message"/>
              <div class="badge" v-show="messageNumber">{{messageNumber}}</div>
            </div>
            <div class="icon icon_pencil" @click.stop="toggleIframe" title="消息">
              <SvgIcon name="pen"/>
              <div class="badge" v-show="msgNumControl">{{msgNumControl}}</div>
              <transition name="fade">
                <div class="bounceFloating" v-show="iframeVisible">
                  <div class="floatingContainer">
                    <iframe :src="iframeSrc" ref="floatingContainer"></iframe>
                  </div>
                </div>
              </transition>
            </div>
            <div class="header_user" @click.stop="toggleUserArea">
              <div class="avatar">
                <img :src="user.imgPath??defaultUser" class="userimg" @error="onImageError">
              </div>

              <div class="txt_user">
                <span class="username">{{ user.userName }}</span>
                <span class="orgFullname">({{ org.name }})</span>
                <span class="fa fa-angle-down"></span>
              </div>
              <div class="userControl" v-show="userArea.areaShow">
                <div v-if="userArea.controlNum === 1" class="control1">
                  <div class="user_info">
                    <div class="logo-avatar">
                      <img class="img-circle" :src="user.imgPath??defaultUser" @mouseover.stop="changeImgBtn(1)" @error="onImageError">
                      <div v-show="upimgShow" class="changeImgCon" @mouseout.stop="changeImgBtn">
                        <uploadImg
                            module="职工头像"
                            :removeFun="uploadRemove"
                            :successFun="handleSuccess"
                            :beforeUploadFun="beforeUpload"
                        >
                          <template #btnArea>
                            <span class="txtLink">{{ uploadBtn1_txt }}</span>
                          </template>
                        </uploadImg>

                      </div>
                    </div>
                    <div class="u1">
                      <div class="name">{{ user.userName }}</div>
                      <div class="mobile_avatar" @click.stop="editAccountBtn">
                        <span>{{ user.mobile }}</span>
                        <el-icon><EditPen /></el-icon>
                      </div>

                    </div>
                  </div>
                  <ul class="menuList_avatar">
                    <li class="litLi" @click.stop="passwordEditBtn">
                      <i class="fa fa-edit"></i>
                      <span class="label">修改密码</span>
                      <el-icon><ArrowRight /></el-icon>
                    </li>
                    <li class="litLi" @click.stop="g2(2)">
                      <i class="fa fa-exclamation-circle"></i>
                      <span class="label">关于</span>
                      <el-icon><ArrowRight /></el-icon>
                    </li>
                    <li class="litLi" @click.stop="goWeixinBind">
                      <i class="fa fa-weixin"></i>
                      <span class="label">微信绑定</span>
                    </li>
                    <li class="litLi" @click.stop="changeOrg">
                      <i class="fa fa-flag"></i>
                      <span class="label">切换机构</span>
                    </li>
                    <li class="litLi" @click.stop="goOutBtn">
                      <i class="fa fa-mail-reply"></i>
                      <span class="label">退出</span>
                    </li>
                  </ul>
                </div>
                <div v-if="userArea.controlNum === 2" class="control2">
                  <div class="header_nav">
                      <el-icon @click.stop="g2(1)"><ArrowLeft /></el-icon> <div class="nav_title">关于</div>
                  </div>
                  <li class="litLi" v-for="(about, aboutIndex) in userArea.aboutMenu" :key="aboutIndex" >
                    <a @click.stop="jumpPage(about.url)"><i :class="about.icon"></i>{{ about.name }}</a>
                  </li>
                </div>
              </div>
            <div/>
          </div>
          </div>
        </el-header>
        <el-main class="mainall" :style="{ height:homePageHeight + 'px', }" id="bigcon">
          <router-view ref="homePage"></router-view>
        </el-main>
      </el-container>
    </el-container>

<!--    修改密码弹窗-->
    <TyDialog v-if="userArea.passwordEditDialog.visible"
               :dialogTitle="userArea.passwordEditDialog.ttl"
               :dialogHide="passwordEditDialogHide" >
       <template #dialogBody>
         <div class="passwordEditCon" v-show="userArea.passwordEditDialog.showNum === 1">
           <el-form class="form mobileForm" label-position="left" label-width="60px" :model="passwordEditForm">
             <el-form-item label="账号">
               <el-input v-model="passwordEditForm.user" placeholder="请输入字幕精灵的手机号" disabled></el-input>
             </el-form-item>
             <el-form-item label="验证码">
               <el-input v-model="passwordEditForm.code" placeholder="请输入验证码" class="code">
                 <template #append>
                   <el-button type="primary" @click="sendCode_changePassword">{{VCode_changePassword.sendCodeName}}</el-button>
                 </template>
               </el-input>
             </el-form-item>
           </el-form>
         </div>
         <div class="passwordEditCon" v-show="userArea.passwordEditDialog.showNum === 2">
           <el-form  label-position="left" label-width="80px" :model="passwordEditForm2">
             <el-form-item label="密码">
               <el-input v-model="passwordEditForm2.passW" placeholder="8-16位字符" clearable show-password autocomplete="new-password"></el-input>
             </el-form-item>
             <el-form-item label="确认密码">
               <el-input v-model="passwordEditForm2.passW2" placeholder="8-16位字符" clearable show-password autocomplete="new-password"></el-input>
             </el-form-item>
             <small class="ty-color-blue">注：密码需为8-16位，必须包括数宇和英文宇母，英文字母分大小写。</small>
           </el-form>
         </div>
       </template>
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="passwordEditDialogHide">取消</el-button>
        <el-button v-show="userArea.passwordEditDialog.showNum === 1" class="bounce-ok" type="primary" @click="checkCode_changePassword">确定</el-button>
        <el-button v-show="userArea.passwordEditDialog.showNum === 2" class="bounce-ok" type="primary" @click="submit_changePassword">设置完毕，重新登陆</el-button>
      </template>
    </TyDialog>
    <TyDialog
        v-if="userArea.passwordEditDialog.resultVisible"
        :dialogTitle="userArea.passwordEditDialog.resultTtl" :color="'red'"
        :dialogHide="passwordEditResultOk"
    >
      <div class="ty-center">
        {{ userArea.passwordEditDialog.result ? '您wonderss系统的密码已修改，请重新登录。': '设置失败，请重新设置！'}}
      </div>
      <template #dialogFooter>
        <el-button v-show="userArea.passwordEditDialog.showNum === 1" class="bounce-ok" @click="passwordEditResultOk('passEdit')">确定</el-button>
      </template>
    </TyDialog>
<!--    修改登录账号-->
    <TyDialog
      v-if="userArea.editAccountDialog.visible"
      :dialogTitle="userArea.editAccountDialog.ttl"
      :dialogHide="editAccountDislogHide"
    >
      <div class="passwordEditCon ty-center" v-show="userArea.editAccountDialog.showNum === 3">
        <p style="font-size: 14px">您能否收到手机号{{ accEditForm.userOld }}的短信验证码？</p>
        <el-radio-group v-model="accEditForm.receiveCode">
          <el-radio :value="1" size="large">能</el-radio>
          <el-radio :value="2" size="large">不能</el-radio>
        </el-radio-group>
        <div>
        </div>
      </div>
      <div class="passwordEditCon ty-center" v-show="userArea.editAccountDialog.showNum === 4">
        如想修改登录账号又无法获取相应手机号的短信验证码，可请 {{ accEditForm.leaderStr }} 为您修改。
      </div>
      <template #dialogFooter>
        <el-button v-show="userArea.editAccountDialog.showNum === 4" class="bounce-ok" type="primary" @click="editAccountDislogHide">我知道了</el-button>
        <el-button v-show="(userArea.editAccountDialog.showNum ) === 3" class="bounce-cancel" @click="editAccountDislogHide">取 消</el-button>
        <el-button v-show="userArea.editAccountDialog.showNum === 3" class="bounce-ok" type="primary" @click="editAccountNext">下一步</el-button>
      </template>
    </TyDialog>
    <TyDialog
        v-show="userArea.editAccountDialog.visible2"
        :dialogTitle="userArea.editAccountDialog.ttl"
        :dialogHide="editAccountDislogHide2"
    >
      <div class="passwordEditCon" v-show="userArea.editAccountDialog.showNum === 1">
        <el-form class="form mobileForm" label-position="left" label-width="60px" :model="accEditForm">
          <el-form-item label="账号">
            <el-input v-model="accEditForm.userOld" placeholder="请输入字幕精灵的手机号" disabled></el-input>
          </el-form-item>
          <el-form-item label="验证码">
            <el-input v-model="accEditForm.code" placeholder="请输入验证码" class="code">
              <template #append>
                <span type="primary" @click="sendCode_oldPhone">{{VCode_verifyCurrentPhone.sendCodeName}}</span>
              </template>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="passwordEditCon" v-show="userArea.editAccountDialog.showNum === 2">
        <el-form class="form mobileForm" label-position="left" label-width="60px" :model="accEditForm">
          <el-form-item label="新账号">
            <el-input v-model="accEditForm.user" placeholder="请输入新账号" @change="newMobileCheck"></el-input>
          </el-form-item>
          <el-form-item label="验证码">
            <el-input v-model="accEditForm.code2" placeholder="请输入验证码" class="code">
              <template #append>
                <span type="primary" @click="sendCode_newPhone">{{VCode_newPhone.sendCodeName}}</span>
              </template>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="passwordEditCon" v-show="userArea.editAccountDialog.showNum === 5">
        <div class="accEditTip">
          <p class="orangeTip">请您设置密码，以便登录更快捷。</p>
          <p>忘记密码时，您可使用验证码登录。</p>
        </div>
        <el-form class="form mobileForm" :model="accEditForm" label-position="left" label-width="70px" style="margin-top: 8px">
          <el-form-item label="密码">
            <el-input v-model="accEditForm.passW" placeholder="8-16位字符" clearable show-password autocomplete="new-password"></el-input>
          </el-form-item>
          <el-form-item label="确认密码">
            <el-input v-model="accEditForm.passW2" placeholder="8-16位字符" clearable show-password autocomplete="new-password"></el-input>
          </el-form-item>
          <small class="ty-color-blue">注：密码需为8-16位数字与字符的组合，不能含空格。</small>
        </el-form>
      </div>
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="userArea.editAccountDialog.visible2 = false">取 消</el-button>
        <el-button v-show="userArea.editAccountDialog.showNum === 2" class="bounce-ok" type="primary" @click="checkCode_newPhone">确定</el-button>
        <el-button v-show="userArea.editAccountDialog.showNum === 5" class="bounce-ok" type="primary" @click="submit_changePassword_changePhone">设置完毕，重新登陆</el-button>
        <el-button v-show="userArea.editAccountDialog.showNum === 1" class="bounce-ok" type="primary" @click="checkCode_oldPhone">确 定</el-button>
      </template>
    </TyDialog>

    <TyDialog v-if="userArea.editAccountDialog.resultVisible"
              :dialogTitle="userArea.editAccountDialog.resultTtl"
              :dialogHide="passwordEditResultOk" >
      <template #dialogFooter>
        <el-button class="bounce-ok" type="primary" @click="passwordEditResultOk('accEdit')">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center">
          <div v-if="userArea.editAccountDialog.result">
            <p>修改成功</p>
            <p>再登录系统，请使用{{ accEditForm.user }}账号！</p>
            <p>退出中……</p>
          </div>
          <div v-else>
            设置失败，请重新设置！
          </div>
        </div>

      </template>
    </TyDialog>

    <!--  复杂的详情页 内嵌iframe  -->
    <div v-if="someDetailsPageVisible" ref="somethingItemDetailsPage" class="somethingItemDetailsPage" @click="someDetailsPageVisible = false">
      <iframe :src="someIframeSrc" :style="someIframeStyle"></iframe>
    </div>

    <!--  这个弹窗是最低的， 不要在下面再写弹窗了！！！  -->
    <TyDialog v-if="operaPropShow" :dialogTitle="operaTip"
              :dialogHide="operaPropCancel" >
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="operaPropCancel">取消</el-button>
        <el-button class="bounce-ok" type="primary" @click="operaPropOk">确定</el-button>
      </template>
      <template #dialogBody>
        <div class="ty-center" style="width: 350px; margin: 16px auto 0">
          <el-form class="demo-form-inline" label-position="left" label-width="60px" >
            <el-form-item label="账号">
              <el-input v-model="opera.user" placeholder="请输入内容" :disabled="true"></el-input>
            </el-form-item>
            <el-form-item label="密码">
              <el-input show-password autocomplete='new-password' v-model="opera.passwd" placeholder="请输入"></el-input>
            </el-form-item>
          </el-form>
        </div>

      </template>
    </TyDialog>

  </div>
</template>
<script>
import * as api from "@/api/api";
import defaultUser from "@/assets/commonImg/avatar_default_small1.png"
import auth from '@/sys/auth'
import JSONBig from 'json-bigint'
import sphdSocket from '@/sys/sphd'
import { testMobile } from '@/utils/formTest'
import { ElMessage } from 'element-plus'
import { initNav,findActivePage, findActivePageByMid, showActivePage, getShowthisPage, removeAllActivePage, jumpToOldPage } from "@/utils/routeChange"
import { previewOrDownloadFun } from "@/utils/downloadFile"
import operaMixin from '@/mixins/mainOperating.js'
import uploadImg from "@/components/uploadImg.vue"

var scopeThis = null
export default {
  mixins: [operaMixin],
  data() {
    return {
      VCode_changePassword: {
        timer: null,
        sendCodeName: '获取验证码',
        sendCodeDisabled: false
      },
      VCode_verifyCurrentPhone: {
        timer: null,
        sendCodeName: '获取验证码',
        sendCodeDisabled: false
      },
      VCode_newPhone: {
        timer: null,
        sendCodeName: '获取验证码',
        sendCodeDisabled: false
      },
      visible:false,
      iframeVisible:false,
      someDetailsPageVisible:false,
      someIframeSrc:'',
      someIframeStyle:'',
      iframeSrc:'',
      resizeTimer:'',
      loadUserTimer:'',
      user:{},
      org:{},
      menuHeight:window.innerHeight-60,
      homePageHeight:window.innerHeight-100,
      messageNumber:0,
      msgNumDiscuss:0,
      msgNumControl:0,
      msgNumTipTimer:'',
      discussionTwink: false ,
      menu:[],
      mainMenu:[],
      curRole:'',
      roleList:[],
      activeMenuMid: 'wodezhuomian',
      accEditForm:{
        userOld: '',
        user: '',
        code: '',
        code2: '',
        passW: '',
        passW2: '',
      },
      passwordEditForm:{
        user: '',
        code: '',
      },
      passwordEditForm2:{
        passW: '',
        passW2: '',
      },
      userArea: {
        areaShow:false,
        controlNum:1,
        aboutMenu:null,
        passwordEditDialog:{
          visible:false,
          resultVisible:false,
          resultTtl:'',
          result:'',
          receiveCode:0,
          ttl:'',
          showNum:1
        },
        editAccountDialog:{
          visible:false,
          visible2:false,
          resultVisible:false,
          resultTtl:'',
          result:'',
          receiveCode:0,
          ttl:'',
          color:'red',
          showNum:1
        },

      },
      upimgShow:false,
      uploadBtn1_txt:'更换头像',
    }
  },
  components: {
    uploadImg,
  },
  computed: {
    treeMenu: function () {
      return this.changeDataToTree(this.mainMenu)
    },
    isShowDiscuss: function () {
      let canShow = this.user.roleCode === 'super' || this.user.roleCode === 'staff' || this.user.roleCode === 'see' || this.user.roleCode === 'smallSuper'
      let cantShow = this.chargeRole("超级浏览者") || this.chargeRole("代理会计") || this.chargeRole("代理小会计")
      return canShow && !cantShow
    }
  },
  watch: {
    '$store.state': {
      deep: true,
      immediate: true,
      handler(newVal,oldVal) {
        let activePage = newVal?.activePage
        this.updateMenuByPage(activePage)
      }
    },
    'activeMenuMid': {
      deep: true,
      immediate: true,
      handler(newVal, oldVal) {
        this.setActiveMenu(newVal)
      }
    }
  },
  created() {
    scopeThis = this
    this.mainMountedFun()

  },
  methods: {
    handleError(error){
      console.log('父组件的', error)
    },
    beforeUpload (file) {
      console.log('父组件的beforeUpload file==', file)
      this.uploadBtn1_txt = '正在上传...'
    },
    uploadRemove(file){
      console.log('uploadRemove = ', file)
    },
    handleSuccess(json,file){
      console.log('成功后的返回值', json)
      var data = json
      // var data = JSON.parse(json)
      if( typeof data.filename === 'string' && data.filename.length > 0 ) {
        let path = this.rootPath.fileUrl + data.filename
        var fileUid = 'upload/' + data.fileUid;
        var pathOral = data.filename;
        this.user.imgPath = path

        const iframe = this.$refs.floatingContainer; // 获取 iframe 元素
        const iframeContent = iframe.contentWindow || iframe.contentDocument; // 获取 iframe 的内容窗口或文档对象
        if (iframeContent) {
          const targetElement = iframeContent.document.getElementsByClassName('user_logo'); // 使用标准的 DOM 操作方法获取目标元素
          console.log(targetElement); // 打印目标元素到控制台
          targetElement.src = path
        }
        this.updateUserImg(pathOral, fileUid);
      }
    },
    updateUserImg(path , fileUid) {
      let userID = this.user.userID
      let params = {"path":path , "userId":userID}
      api.updateUserimgByUserID(params).then(res => {
        let data = res.data
        auth.getUser(true)
        sphdSocket.reflushAuth();
        console.log('data=', data)

      }).catch(err => {
        console.log('err=', err)
        this.$message.error('上传失败！')
      })

    },
    updateMenuByPage(activePage) {
      if (activePage.length > 0) { // 空白的话等待initDesktop后再执行
        let activeMid = getShowthisPage()?.menuInfo?.mid
        if (activeMid == null || activeMid == 'wodezhuomian' && activePage.length==1) {
          //clear all
          this.activeMenuMid = 'wodezhuomian'
        } else {
          this.activeMenuMid = activeMid
        }
      }
    },
    onImageError(event){
      event.target.src = defaultUser ;
    },
    mainMountedFun(){
      this.loadUser()
      if(!auth.getUser()){
        window.location.reload(true);
      }
      window.addEventListener("resize", this.getMenuYwidth);
      let that = this
      setTimeout(function () {
        that.setIframeWindowParentData()
      }, 500)
    },

    loadUser() {
      this.user = auth.getUser()
      this.org = auth.getOrg()
      console.log('org 我看看', this.org);
      if(!this.user){
        this.$router.push('orgList')
      }
      let thatss = this
      // TODO 长链接订阅 处理角标 变化
      sphdSocket.subscribeV2(sphdSocket.level.user, 'updateUserBadgeNumbers',
          function (data) {
            let getData = JSONBig.parse(data) // { 'list':{} ,'userId':6464 ,'ver':108812  }
            console.log('处理角标 订阅返回值 ok=', getData)
            let info = getData.list
            let message = info?.message ?? 0
            thatss.msgNumControl = message

          },
          function (err) {
            console.log('处理角标 订阅返回值err =', err)
          }, 'custom', thatss.user.userID)

      this.loadUserTimer = setInterval(function () {
        if(thatss.rootPath && thatss.rootPath.fileUrl){
          console.log('thatss.user', thatss.user)
          clearInterval(thatss.loadUserTimer)
          if(thatss.user && thatss.user.imgPath){
            thatss.user.imgPath = thatss.rootPath.fileUrl +  thatss.user.imgPath
          }

        }
      }, 200)

      this.getNavList()
      this.getMenuYwidth()
    },
    ssetPagr(){
      let setIframeStyle = {
        'height': '400px',
        'margin-top': '200px',
        'border': 'none'
      }
      console.log( '设置一下"floatToPage"= ' )
      window.floatToPage( auth.webRoot + '/complaint/complaintInfo.do', { 'id': 23, 't': 1 }, setIframeStyle )

      //showMenuWxMsg=true 显示右上方菜单微信绑定状态；showMenuWxMsg=false 不显示显示右上方菜单微信绑定状态；
      window.showMenuWxMsg = false


    },
    setIframeWindowParentData(){
      var thatss = this
      window.initBounceFloating = function () {
        console.log('initBounceFloating')
      }
      // 从浮窗列表 跳转到 详情页（主页面的）
      window.floatToPage = function (action, params, setIframeStyle) {
        scopeThis.someDetailsPageVisible = true
        var urlStr = "" + action ;
        if(params){
          urlStr += "?" ;
          for(var key in params) {
            urlStr += key + "=" + params[key] + "&" ;
          }
          urlStr = urlStr.substr(0, urlStr.length-1) ;
        }
        urlStr = urlStr.split('../../..')[1]
        let allUrlStr = auth.webRoot + urlStr
        scopeThis.someIframeSrc = allUrlStr
        if(setIframeStyle){
          let styleStr = ``
          for(var key in setIframeStyle) {
            styleStr += `${ key } : ${ setIframeStyle[key] };`
          }
          scopeThis.someIframeStyle = styleStr
        }else{
          scopeThis.someIframeStyle = ''
        }

      }
      window.previewOrDownload = function (obj, type) {
        previewOrDownloadFun(obj, type, thatss)
        
      }
      window.seePicture = function (path) {
        if (path) {
          path = path.replace("^/upload/","/").replace("^upload/","");
          let rootp= localStorage.getItem('rootPath')
          rootp = JSON.parse(rootp)
          let fileU = this.rootPath ? this.rootPath.fileUrl : rootp.fileUrl
          var url = fileU + path
          window.open(url)
        }
      }
      window.hideDetailsPage = function () {
        scopeThis.someDetailsPageVisible = false
        scopeThis.someIframeSrc = ''
        scopeThis.someIframeStyle = ''
      }

      //wyu：为小窗/vue/message的window.parent.sphdSocket赋值
      window.sphdSocket = sphdSocket
      this.iframeSrc = auth.webRoot + '/vue/message/dist/index.html'

      let bnsShow = localStorage.getItem('bnsShow')
      console.log('获取 小窗的 显示隐藏状态', bnsShow)
      if(bnsShow){
        bnsShow == 1 ? this.iframeVisible = true : this.iframeVisible = false ;
      }else {
        this.iframeVisible = true
      }

    },
    toggleIframe(){
      this.iframeVisible = !(this.iframeVisible)
    },
    hideIframe(){
      this.iframeVisible = false
    },
    setTotalCorner(){
      var badgeNumbers =  JSONBig.parse(localStorage.getItem('userBadgeNumbers'));
      var countAll =  badgeNumbers && badgeNumbers.list.handle;
      this.setMsgNum(false, countAll)
    },
    setMsgNum(bool, num) {
      //  给信封处的消息赋值
      if (Number(num) && Number(num) > 0) {
        this.msgNum = num
        if (bool) {
          console.log(bool)
          this.msgNumTipTimer = setInterval(function () {
            window.document.title = "您有未读的新消息";
            setTimeout(function () {
              window.document.title = "通用框架";
            }, 1000)
          }, 1500);
          window.document.title = "您有未读的新消息";
        }
      } else {
        this.msgNum = 0
      }
    },
    getMenuYwidth(){
      let height = window.innerHeight
      let litHeight = height - 66
      this.menuHeight = litHeight
      this.homePageHeight = litHeight - 30
    },
    newMobileCheck(){
      let p = this.accEditForm.user
      let op = this.accEditForm.userOld
      if (testMobile(p) && p != op) {
        let dataSend = { "mobile": p }
        api.mobileCheckRepeat(dataSend).then(res => {
          let data = res.data.data
          let status = data.state
          // state 1-查重通过  0-与在职人员相同  2- 与离职人员相同 3- 与历史记录相同
          if (status === 1) {
            var str =
                '<p>您此次的修改将导致原手机号无法登录系统。</p>' +
                '<p>确定修改吗？</p>' ;
            this.$confirm(str, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
              dangerouslyUseHTMLString: true,
            }).then(() => {
                  // this.$message({
                  //   type: 'success',
                  //   message: '删除成功!',
                  // })
                })
                .catch(() => {
                  this.accEditForm.user = ''
                })
          }
          else if (status === 2 || status === 3) {
            this.$confirm(data.cont , '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
              dangerouslyUseHTMLString: true,
            }).then(() => {

            }).catch(() => {
              this.accEditForm.user = ''
            })
          }
          else if (status === 0) {
            this.$alert(data.cont , '提示', {
              confirmButtonText: '我知道了',
              callback: (action) => {
                this.accEditForm.user = ''
              },
            })
          }


        }).catch(err => {
          console.log('err=', err)
        })
      }
    },
    sendCode_oldPhone(){
      let sendCodeDisabled = this.sendCodeDisabled
      let data = { phone: this.accEditForm.userOld  }
      if (sendCodeDisabled) return false
      if(data.phone.length === 11) {
        api.sendCode_oldPhone(data).then(res => {
          let success = res.data.success
          let errorMsg = res.data.errorMsg
          if (success === 1) {
            ElMessage.success('发送成功！')
            this.countdown(this.VCode_verifyCurrentPhone, 60)
          } else {
            ElMessage.error(errorMsg)
          }
        }).catch(err => {
          console.log('err=', err)
        })
      } else {
        ElMessage.error('手机号码不正确！')
      }
    },
    checkCode_oldPhone(){
      let data = {
        phone: this.accEditForm.userOld,
        code: this.accEditForm.code
      }
      if(data.code.length === 0){
        ElMessage.error('请先输入验证码!')
        return false
      }
      api.checkCode_oldPhone(data).then(res => {
        let success = res.data.success
        // 返回值： true- 验证成功 false-验证失败
        if(success === 1){
          this.userArea.editAccountDialog.showNum = 2
          this.accEditForm.user = ''
          this.accEditForm.code2 = ''
        }else{
          ElMessage.error('您输入的验证码有误!')
        }
      }).catch(err => {
        ElMessage.error('系统错误!')
        console.log('System Error：' + err)
      })
    },
    sendCode_newPhone(){
      let sendCodeDisabled = this.sendCodeDisabled
      let data = { phone: this.accEditForm.user  }
      if (sendCodeDisabled) return false
      if(data.phone.length === 11) {
        api.sendCode_newPhone(data).then(res => {
          let success = res.data.success
          let errorMsg = res.data.errorMsg
          if (success === 1) {
            ElMessage.success('发送成功！')
            this.countdown(this.VCode_verifyCurrentPhone, 60)
          } else {
            ElMessage.error(errorMsg)
          }
        }).catch(err => {
          console.log('err=', err)
        })
      } else {
        ElMessage.error('手机号码不正确！')
      }
    },
    checkCode_newPhone(){
      let data = {
        phone: this.accEditForm.user,
        code: this.accEditForm.code2,
      }
      if(data.code.length === 0){
        ElMessage.error('请先输入验证码!')
        return false
      }
      api.checkCode_newPhone(data).then(res => {
        let success = res.data.success
        // 返回值： true- 验证成功 false-验证失败
        if(success === 1){
          this.userArea.editAccountDialog.showNum = 5
          this.accEditForm.passW = ''
          this.accEditForm.passW2 = ''
        }else{
          ElMessage.error('您输入的验证码有误!')
        }
      }).catch(err => {
        ElMessage.error('系统错误!')
        console.log('System Error：' + err)
      })
    },
    submit_changePassword_changePhone(){
      if(this.accEditForm.passW !== this.accEditForm.passW2){
        this.$message.error("两次输入的密码不一致，请重新设置！")
        return false
      } else if(!this.isPassword(this.accEditForm.passW)){
        this.$message.error("您设置的密码过于简单，请重新设置！")
        return false
      }
      let json = { "userId": this.user.userID,
        "phone": this.accEditForm.user ,
        "password": this.accEditForm.passW,
        "code": this.accEditForm.code2
      };

      api.changePassword_changePhone(json).then(res => {
        let data = res.data
        this.userArea.editAccountDialog.resultVisible = true;
        this.userArea.editAccountDialog.visible = false;
        this.userArea.editAccountDialog.result = data;
        this.userArea.editAccountDialog.resultTtl = '提示'

      }).catch(err => {

      })
    },
    sendCode_changePassword(){
      // 直接修改密码 - 发送验证码
      let sendCodeDisabled = this.VCode_changePassword.sendCodeDisabled
      let data = { phone: this.passwordEditForm.user  }
      if (sendCodeDisabled) return false
      if(data.phone.length === 11) {
        api.sendCode_changePassword(data).then(res => {
          let success = res.data.success
          let errorMsg = res.data.errorMsg
          if (success === 1) {
            ElMessage.success('发送成功！')
            this.countdown(this.VCode_changePassword, 60)
          } else {
            ElMessage.error(errorMsg)
          }
        }).catch(err => {
          console.log('err=', err)

        })
      } else {
        ElMessage.error('手机号码不正确！')
      }
    },
    checkCode_changePassword(){
      let data = {
        phone: this.passwordEditForm.user,
        code: this.passwordEditForm.code,
      }
      if(data.code.length === 0){
        ElMessage.error('请先输入验证码!')
        return false
      }
      api.checkCode_changePassword(data).then(res => {
        let success = res.data.success
        // 返回值： true- 验证成功 false-验证失败
        if(success === 1){
          this.userArea.passwordEditDialog.showNum = 2
          this.passwordEditForm2.passW = ''
          this.passwordEditForm2.passW2 = ''
        }else{
          ElMessage.error('您输入的验证码有误!')
        }
      }).catch(err => {
        ElMessage.error('系统错误!')
        console.log('System Error：' + err)
      })
    },
    submit_changePassword(){
      // 直接修改密码，最后提交修改的密码
      if(this.passwordEditForm2.passW !== this.passwordEditForm2.passW2){
        this.$message.error("两次输入的密码不一致，请重新设置！")
        return false
      } else if(!this.isPassword(this.passwordEditForm2.passW)){
        this.$message.error("您设置的密码过于简单，请重新设置！")
        return false
      }
      let data = {
        newPassword:this.passwordEditForm2.passW
      }
      api.resetLoginPassWord(data).then(res => {
        let resData = res.data
        if(resData) {
          this.userArea.passwordEditDialog.resultVisible = true
          this.userArea.passwordEditDialog.visible = false
          this.userArea.passwordEditDialog.resultTtl = '提示'
          this.userArea.passwordEditDialog.result = resData
          setTimeout(this.logout(), 3000)
        } else  {
          console.log('密码修改失败，请重新设置！', resData)
          this.$message.error('密码修改失败，请重新设置！')
        }
      }).catch(err=>{
        console.log('密码修改失败，请重新设置！', err)
        this.$message.error('密码修改失败，请重新设置！')
      })

    },
    editAccountNext(){
      let receiveCode = this.accEditForm.receiveCode
      if(receiveCode == 1){
        this.userArea.editAccountDialog.visible = false
        this.userArea.editAccountDialog.showNum = 1
        this.accEditForm.userOld = this.user.mobile
        this.accEditForm.code = ''
        this.userArea.editAccountDialog.ttl = '修改登录账号'
        this.userArea.editAccountDialog.visible2 = true

      }
      else if(receiveCode == 2){
        let data = {
          'userId': this.user.userID
        }
        api.getLeaderUserName(data).then(res => {
          let resName = res.data.data;
          let str = '';
          if (this.chargeRole('超管')){
            str = resName;
          } else if (this.chargeRole('普通员工')){
            str = '管理人员' + resName;
          } else {
            str = '领导' + resName;
          }
          this.accEditForm.leaderStr = str
          this.userArea.editAccountDialog.showNum = 4

        }).catch(err => {
          console.log('err=', err)
        })
      }
      else {
        ElMessage.error('请先选择能否收到短信验证码！')
      }
    },
    chargeRole(roleStr) { // roleStr 值：“超管”, “总务”, “财务”, “销售”, “超级浏览者”, “普通员工”,  “会计”,  “代理会计”,  “代理小会计”,  “小超管”,
      var roleCodeList = [
        {"name": "超管", "code": "super"},
        {"name": "总经理", "code": "smallSuper"},
        {"name": "总务", "code": "general"},
        {"name": "财务", "code": "finance"},
        {"name": "销售", "code": "sale"},
        {"name": "超级浏览者", "code": "browse"},
        {"name": "浏览者", "code": "browse"},
        {"name": "普通员工", "code": "staff"},
        {"name": "会计", "code": "accounting"},
        {"name": "代理会计", "code": "agentAccounting"},
        {"name": "代理小会计", "code": "agentSmallAccounting"},
        {"name": "小超管", "code": "smallSuper"}
      ];
      var roleCode = this.user.roleCode;
      for (var i = 0; i < roleCodeList.length; i++) {
        if (roleCodeList[i]["name"] == roleStr) {
          if (roleCode == roleCodeList[i]["code"]) {
            return true;
          } else {
            return false;
          }
        }
      }
    },
    editAccountDislogHide(){
      this.userArea.editAccountDialog.visible = false
    },
    editAccountDislogHide2() {
      this.userArea.editAccountDialog.visible2 = false
    },
    editAccountBtn(){
      this.userArea.editAccountDialog.color ='red'
      this.userArea.editAccountDialog.showNum = 3
      this.userArea.editAccountDialog.visible = true
      this.userArea.editAccountDialog.ttl = '！！提示'
      this.accEditForm.userOld = this.user.mobile
      this.accEditForm.receiveCode =''

    },
    passwordEditResultOk(type){
      let result = ''
      if(type === 'passEdit'){
        result = this.userArea.passwordEditDialog.result
      }else if(type === 'accEdit'){
        result = this.userArea.editAccountDialog.result
      }
      if(result){
        this.logout()
      }else{
        this.userArea.passwordEditDialog.resultVisible = false
      }
    },

    isPassword(password) {
      return /^(?=.*[a-zA-Z])(?=.*\d)[^]{8,16}$/.test(password);
    },
    countdown (obj, seconds) {
      let that = this
      if (seconds > 1){
        seconds--;
        obj.sendCodeName = seconds + '秒后可重新获取'
        obj.sendCodeDisabled = true
        // 定时1秒调用一次
        clearTimeout(obj.timer)
        obj.timer = null
        obj.timer = setTimeout(function(){
          that.countdown(obj, seconds)
        },1000)
      } else {
        clearTimeout(obj.timer)
        obj.sendCodeName = '获取验证码'
        obj.sendCodeDisabled = false
      }
    },
    passwordEditDialogHide(){
      this.userArea.passwordEditDialog.visible = false
    },
    toggleUserArea(){
      this.iframeVisible = false
      this.userArea.areaShow = !this.userArea.areaShow
    },
    hideFun(){
      this.hideIframe()
      this.hideUserArea()
    },
    hideUserArea(){
      this.userArea.areaShow = false
    },
    changeImgBtn(num){
      if(num === 1){
        this.upimgShow = true
      }else {
        this.upimgShow = false
      }
    },
    upPeImg(){},
    passwordEditBtn(){
      this.userArea.passwordEditDialog = {
        visible:true,
        receiveCode:0,
        showNum:1,
        ttl:'修改密码',
      }
      this.passwordEditForm = {
        user: this.user.mobile,
        code: '',
      }
    },
    g2(num){
      this.userArea.controlNum = num
      if(num === 2){
        if(this.userArea.aboutMenu){

        }else{
          api.getAboutInitialDirectory().then(res => {
            let data = res.data.data
            let listFirstFolder = data.listFirstFolder
            let iconArr = ['bars', 'bell-o', 'info', 'book']

            this.userArea.aboutMenu = [
              {
                'icon':'fa fa-download',
                'url': '/about/scanDownload.do',
                'name':'扫描下载'
              }
            ]
            listFirstFolder.forEach((item, index) => {
              this.userArea.aboutMenu.push(
                  {
                    'icon': 'fa fa-' + iconArr[index],
                    'url': '/about/aboutCommon.do?id=' + item.id,
                    'name': item.name
                  }
              )
            })


          }).catch(err => {

          })
        }
      }
    },
    jumpPage(urlStr){
      jumpToOldPage(urlStr)
    },
    changeOrg(){
      this.$router.push("orgListChange")
    },
    goWeixinBind(){
      console.log('goWeixinBind 方法调用')
      this.$router.push("weixinBind")
    },
    goOutBtn(){
      this.logout()
    },
    menuClick(menuI, level) {

      if(level){
        // 此段代码为大窗使用，后续全部改为vue时可以删掉， level字段也可以删掉
        localStorage.setItem('level' + level, menuI.mid)
        localStorage.setItem('pidLevel' + level, menuI.pid)
      }

      let thisCollapse = menuI.collapse // 当前模块是否展开
      let mainMenu = this.mainMenu

      if (thisCollapse) {
        menuI.collapse = false
      } else {
        menuI.collapse = true
        mainMenu.map(item => {
          if (item.pid === menuI.pid && item.mid !== menuI.mid) {
            item.collapse = false
          }
          if (item.pid === menuI.mid) {
            item.collapse = false
          }
        })
      }
      //  处理 高亮菜单展示
      let url = menuI?.url
      let pageName
      if(url?.length>4) {
        if (!url.startsWith('../')
            && url.indexOf(auth.getPackageName()) >= 0
            && (
                (pageName = menuI.pageName) != null
                || (pageName = url.substring(url.lastIndexOf('/') + 1))
                && this.$router.hasRoute(pageName)
            )
        ) {
          let index
          if ((index = findActivePageByMid(menuI.mid)) >= 0) {
            showActivePage(index)
          } else {
            this.$router.push(pageName)
          }
        } else {
          this.jumpPage(menuI.url)
        }
      }
    },
    getNavList(){
      // 获取大菜单
      api.getAllMenu().then(res => {
        let data = res.data.data
        let mainMenu = data.mainMenu
        let messageMenu = data.messageMenu
        let userBadgeNumbers = data.userBadgeNumbers
        let rolePrincipals = data.rolePrincipals

        localStorage.setItem('mainMenu', mainMenu);
        localStorage.setItem('messageMenu', messageMenu);
        localStorage.setItem('userBadgeNumbers', userBadgeNumbers);
        localStorage.setItem('rolePrincipals', JSONBig.stringify(rolePrincipals));

        this.mainMenu = JSONBig.parse(mainMenu)
        messageMenu = JSONBig.parse(messageMenu)
        userBadgeNumbers = JSONBig.parse(userBadgeNumbers)
        var countAll =  userBadgeNumbers && userBadgeNumbers.list.handle;
        this.msgNumControl = countAll

        this.roleList = []

        rolePrincipals.forEach(role => {
          if(this.user.userID == role.userID){
            this.curRole = this.roleStr(role.roleCode, role.roleName, "all")
          }else{
            let roItem = {
              'id': role.userID,
              'name':this.roleStr(role.roleCode, role.roleName),
              'dec':"您在系统中有其他身份，请点击进入，以便及时处理与您有关的事务。",
              'msgCount':role.msgCount
            }
            this.roleList.push(roItem)
            let that = this
            sphdSocket.subscribeV2(sphdSocket.level.user, 'updateUserBadgeNumbers',
                function (data) {
                  var getData = JSONBig.parse(data)
                  let dID = getData.userId
                  console.log('其他角色 订阅返回值 ok=', getData)
                  that.roleList.forEach( roI => {
                    if(dID === roI.id){
                      console.log('找到了该角色')
                      roI.msgCount = getData.list.handle
                    }

                  })

                },
                function (err) {
                  console.log('其他角色 订阅返回值err =', err)
                }, 'custom', role.userID)
          }
        })

        // 设置左侧菜单 (这个方法要执行一次，因为 watch的响应太早，menu数据还未加载）
        let mid = this.activeMenuMid
        this.setActiveMenu(mid)


      }).catch(err=>{
        console.log('err=', err)
      })
      // 获取讨论区角标
      api.getMessageNum().then(res => {
        let data = res.data
        var num = data['userMessageNumber'];
        this.messageNumber = num
        console.log('获取讨论区角标 this.messageNumber=', res)
        let that = this
        // TODO 长链接订阅 旧消息 变化
        sphdSocket.subscribeV2(sphdSocket.level.user, 'userMessageNumber',
          function(data){ // success
            console.log('订阅 旧消息 返回值：', data)
            that.messageNumber = data
          },
          function (err) {  // error
            console.log('订阅 旧消息 返回值 错误：', err)
          },
          'user'
        ),
        //TODO wyu：长连接获取 讨论区 角标数量
        sphdSocket.subscribeV2(sphdSocket.level.user, 'formPostNumforSuperscript',
            function(data){
              if (data) {
                // {"twinkleTag":"1"}
                // todo 判断当前页面是不是讨论区，不是讨论区 就闪烁，
                //  目前没有重构讨论区页面 就都设置成闪烁
                that.discussionTwink = true

              }
              console.log('讨论区 角标数量 OK:'+data);
            },
            function(err){
              console.log('讨论区 角标数量 Error:', err)
            },
            'user'
        )
      }).catch(err => {
        console.log('err=', err)
      })
    },
    setActiveMenu(mid) {
      let mainMenu = this.mainMenu
      mainMenu.map(item => item.collapse = false)
      mainMenu.map(item => item.active = false)
      if (mid && mainMenu.length > 0) {
        let openMid = this.findAllParents(mainMenu, mid) // 展开的模块

        // 设置模块展开
        mainMenu.map(item => {
          openMid.forEach(it => {
            if (it === item.mid) {
              item.collapse = true
            }
          })
        })
        // 设置模块选中
        mainMenu.map(item => {
          if (item.mid === mid) {
            item.active = true
          }
        })
      }
    },
    roleClick(role){
      let loginUserId = role.id
      let data = { loginUserId: loginUserId  }
      api.sureLogin(data).then(res => {
        sphdSocket.reflushAuth()
        let slData = res.data
        // console.log('sureLogin', slData)
        if (slData.error != undefined) {
          console.log('切换身份失败！！', slData.error)
          // this.$message.error(slData.error)
          this.$alert(slData.error, '提示', {
            confirmButtonText: '确定',
          })
        } else {//切换身份成功
          console.log('切换身份成功！！', auth.getUserID())
          sphdSocket.unsubscribeAll(sphdSocket.level.user)
          localStorage.removeItem("floating") // 清楚悬浮窗设置的导航（跟返回有关的）
          localStorage.removeItem('floatMenu') // 清除悬浮窗的url
          localStorage.removeItem("mainMenu");
          localStorage.removeItem("messageMenu");
          localStorage.removeItem("userBadgeNumbers");
          localStorage.removeItem("rolePrincipals");
          this.loadUser()
          console.log('先获取当前高亮页面名称，再清除ActivePage')
          let currentPageName = getShowthisPage(true)?.menuInfo?.pageName
          removeAllActivePage()
          if(currentPageName==='desktop') {
            location.reload()
          } else {
            this.$router.push('desktop')
          }
        }
      }).catch(err => {
        // console.log('err=', err)
        console.log('切换身份失败！', err)
        // this.$message.error(slData.error)
        this.$alert('切换身份失败！', '提示', {
          confirmButtonText: '确定',
        })
      })
    },
    openHome(){
      let pageName = 'desktop'
      initNav({mid:'wodezhuomian', name:'我的桌面', pageName:pageName})
      let index = findActivePage(pageName)
      if(index >= 0) {
        showActivePage(index)
      }
      // let home = {
      //   code : "" ,
      //   pageName : "desktop" ,
      //   label: "我的桌面",
      //   mid: "wodezhuomian",
      //   name: "我的桌面",
      //   url:auth.getPackageName()
      // }
      // this.menuClick(home)
    },
    roleStr(code, name, state) {
      if (state == "all") {
        switch (code) {
          case 'super':
            return '董事长';
            break
          case 'smallSuper':
            return '总经理';
            break
          case 'general':
            return '总务';
            break
          case 'finance':
            return '财务';
            break
          case 'sale':
            return '销售';
            break
          case 'accounting':
            return '会计';
            break
          case 'agent':
            return name;
            break
          default:
            return '普通员工'
        }
      } else {
        switch (code) {
          case 'super':
            return '董';
            break
          case 'smallSuper':
            return '全';
            break
          case 'general':
            return '总';
            break
          case 'finance':
            return '财';
            break
          case 'sale':
            return '销';
            break
          case 'accounting':
            return '会';
            break
          case 'agent':
            return name;
            break
          default:
            return '普通员工'
        }
      }
    },
    findAllParents(tree, mid) {
      const parents = []
      if (mid) {
        function findParent(tree, id) {
          let item = tree.find(item => item.mid === id)
          if (item?.pid) {
            if (item.pid !== '0') {
              parents.push(item.pid)
              findParent(tree, item.pid)
            }
          }
        }
        findParent(tree, mid)
      }
      return parents
    },
    changeDataToTree(data) {
      data.forEach(item => item.children = [])
      const originData = data
      let newData = []
      for (let item of originData) {
        for (let it of originData) {
          if (it.pid === item.mid) {
            item.children.push(it)
          }
        }
        if (item.pid === '0') {
          newData.push(item)
        }
      }
      return newData
    },
  }
}
</script>

<style lang="scss" scoped>
@import "@/style/tyIcon/icon_common.css";
body{
  font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
  position: relative;
}
/* 浮窗复杂的详情页 */
.somethingItemDetailsPage{
  width: 100%; position: fixed; top: 0; z-index: 9;
  background: rgba(100,100,100,0.5); height: 100%;
  &>iframe{
    width: 1200px; border: 1px solid #ccc; margin: 200px auto 0;
    height: inherit; justify-content: center; overflow-x: hidden;
    display: block;
  }
}
.badge{
  position: absolute;
  border-radius: 8px;
  background: $ty-color-red;
  color: #fff;
  font-size: 12px;
  right: -2px;
  top: -2px;
  font-family: 'Helvetica';
  padding: 2px 6px;
  line-height: 1;
}
.header{
  display: flex;
  align-items: center;
  background: #2b3643;
  color:#eaeaea ;
  height:46px;
  padding: 0 16px;
  .icon{
    padding: 8px;
    border-radius: 3px;
    color: #cdd6e1;
    font-size: 20px;
    display: flex;
    align-items: center;
    cursor: pointer;
    &:hover{
      background: #404e5e;
      svg {
        opacity: 1;
      }
    }
    svg{
      opacity: 0.8;
    }
  }
  .header_left{
    .logo_avatar{
      .logo{
        font-size: 24px;
        position: relative;
        cursor: default;
        font-family: 'Helvetica';
        color: #fff;
        span{
          color: $ty-color-green;
        }
      }
    }
  }
  .header_main{
    display: flex;
    align-items: center;
    font-size: 14px;
    flex: auto;
    color: #c1cbd7;
    .header_des{
      //color: #8794a4;
    }
    .icon_home{
      margin-right: 16px;
      &.active{
        background: $ty-color-green;
      }
    }
    .currentRole{
      .currentRole_txt{
        margin-left: 8px;
      }
    }
    .otherRole{
      margin-left: 32px;
      display: flex;
      align-items: center;
      .role_avatar{
        display: flex;
        margin-left: 8px;
        .role_item{
          padding: 10px 12px;
          border-radius: 3px;
          cursor: pointer;
          &:hover{
            background: #404e5e;
            color: #fff;
          }
        }
      }
    }
  }
  .header_right{
    display: flex;
    align-items: center;
    color: #c1cbd7;
    font-size: 14px;
    .icon{
      margin: 0 8px;
      position: relative;
    }
    .icon_commenting{
      &.dot::after{
        content: '';
        background: $ty-color-red;
        width: 6px;
        height: 6px;
        border-radius: 3px;
        position: absolute;
        top: 4px;
        right: 4px;
        color: #fff;
        //-webkit-animation: twink 0.8s linear 1s 5 alternate;
        //animation: twink 0.8s linear infinite;
      }

    }
    .icon_pencil{
      .iframeCon{
        position: relative;
      }
      /* 浮窗 */
      .bounceFloating {
        //display: none;
        position: absolute; top: 48px; z-index: 99; box-shadow: 0 1px 4px #bdbdbd;
        border: 1px solid #c8c8c8; background-color: #36C6D3; border-top: none; width: 395px; height: 550px; right: 0;

        iframe { width: 100%; height: 100%; border: none; }

      }
      .bounceFloating:after{
        content: ''; position: absolute; display: block; border: 8px solid transparent; border-bottom-color: transparent; border-bottom-color: #36c6d3; top: -16px; right: 8px;
      }

      div.floatingContainer {
        position: relative; width: 100%; height: 100%; overflow: hidden;
      }

      div.floatingContainer > div.bonceHead {
        border-bottom: none; background: none; text-align: right; cursor: all-scroll; height: 8px;
        position: absolute; top: 0; width: 100%; right: 0; padding-right: 32px;

      }
    }
    .header_user{
      margin-left: 28px;
      display: flex;
      align-items: center;
      font-size: 14px;
      position: relative;
      cursor: pointer;
      .avatar{
        width: 28px;
        height: 28px;
        margin-right: 8px;
        img{
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }
      .txt_user{
        padding: 10px 12px;
        border-radius: 3px;
        color: #cdd6e1;
        display: flex;
        align-items: center;
        cursor: pointer;
        &:hover{
          background: #404e5e;
        }
      }
      .fa-angle-down {
        margin-left: 8px;
      }
      .userControl {
        position: absolute; top:40px; right:0; width: 200px; background: #fff; color: #333;
        box-shadow: 0 6px 12px #ddd;
        border-radius: 5px;
        line-height: 1.2;
        padding: 16px;
        cursor: default;
        z-index: 99;
        .fa { margin: 0; margin-right: 10px; }
        .control1{
          .changeImgBtn { width: 64px; height: 64px;
            position: absolute; left: 0; top: 0; text-align: center; z-index: 1; background: rgba(0,0,0,0.3); border-radius: 50%;
          }
          .litLi{
            display: flex;
            padding: 10px 4px;
            border-bottom: 1px solid #f6f6f6;
            color: #333;
            cursor: pointer;
            border-radius: 4px;
            .label{
              flex: auto;
            }
            i{
              font-size: 16px;
              color: #333;
              &.fa{
                width: 16px;
                text-align: center;
                color: #333;
              }
            }
            &:last-child{
              border-bottom: none;
            }
            &:hover{
              color:#0b94ea;
              i{
                color:#0b94ea;
              }
            }
          }
          .user_info {
            display: flex; flex-direction: row;align-items: center; margin: 8px 0 16px 0;
            .logo-avatar {
              position: relative;
              img.img-circle, .changeImgCon { width: 56px; height: 56px; border-radius: 50%; border: 3px solid #eee}
              .changeImgCon{
                position: absolute;
                background-color: rgba(100,100,100, 0.3);
                z-index: 1; text-align: center; top:0;
                .txtLink{
                  line-height: 60px;
                  color: #fff;
                  &:hover{
                    font-weight: bold;
                  }
                }
              }
            }
            .u1 {
              margin-left: 8px;
              line-height: 1.5;
              color: #333;
              .name{font-weight: bold; font-size: 16px;}
              .mobile_avatar{
                display: flex; align-items: center;
                .el-icon{
                  font-size: 16px;
                  margin-left: 8px;
                  color: #0b94ea;
                  cursor: pointer;
                }
              }
            }

          }
        }
        .bigLi>div {
          padding: 0 14px;
          background: #fff;
          a{
            display: block; height: 36px; color: #333; line-height: 36px; border-bottom: 1px solid #eaeaea;
            &:hover{ color:$ty-color-green; }
          }
          #bdWx { font-size: 0.8em; margin-right: 5px; }
        }

        .control2{
          .header_nav{
            padding: 4px 0;
            margin-bottom: 8px;
            font-size: 16px;
            position: relative;
            .nav_title{
              flex: auto;
              text-align: center;
            }
            .el-icon{
              cursor: pointer;
              font-size: 18px;
              position: absolute;
              top: 4px;
              left: 0;
            }
          }
          .litLi{
            display: flex;
            padding: 10px 4px;
            border-bottom: 1px solid #f6f6f6;
            color: #333;
            cursor: pointer;
            border-radius: 4px;
            .label{
              flex: auto;
            }
            i{
              font-size: 16px;
              color: #333;
              &.fa{
                width: 16px;
                text-align: center;
                color: #333;
              }
            }
            &:last-child{
              border-bottom: none;
            }
            &:hover{
              color:#0b94ea;
              i{
                color:#0b94ea;
              }
            }
          }
        }
      }
    }
  }
}
.tykj{
  top: 0;
}

.mainall{ padding: 0!important; }
.menuBg{
  background: #364150;
  padding: 0;
  cursor: default;
  .menuBgCon{
    padding-bottom: 20px;
    overflow-y: auto;
    overflow-x: hidden;
    //border: 2px solid red;
  }
}
.navList{
  .menuItem{
    padding-left:16px;
    display: flex;
    align-items: center;
    .menuIcon {
      flex: none;
    }
    .menuTitle{
      flex: auto;
    }
    .menuAngle{
      width: 32px;
      flex: none;
      font-size: 16px;
      text-align: center;
    }
    &:hover:not(.active){
      background: #2C3542!important;
    }
    i.fa-angle-right{
      transition: transform 0.1s ease;
    }
    &.collapse{
      i.fa-angle-right{
        transform: rotate(90deg);
      }
    }

  }
  .menu1{
    line-height:42px;
    font-size:14px;
    position: relative;
    font-weight: 500;
    .menuItem{
      background: #364150;
      color: #ced5dd;
      &.active{
        background: #36c6d3; color: #fff;
      }
      &.collapse{
        color: #fff;
      }
    }
  }
  .menu2{
    line-height:36px;
    font-size:13px;
    position: relative;
    font-weight: 300;
    .menuItem{
      background: #313b4a;
      color: #c3cad1;
      &.active{
        background: #36c6d3; color: #fff;
      }
      &.collapse{
        color: #fff;
      }
    }
  }
  .menu3{
    line-height:36px;
    font-size:13px;
    position: relative;
    font-weight: 300;
    .menuItem{
      background: #313b4a;
      color: #b4bac1;
      &.active{
        background: #36c6d3; color: #fff;
      }
    }
  }
  .menuIcon{
    display: inline-block;
    width: 16px; height: 16px;
    margin-right: 8px;
  }
  .menu3 .menuItem{ padding-left:40px; }
  .menu2, .menu3{  line-height:36px;}

}
.passwordEditCon{
  margin: 20px;
  .tip{ font-size: 0.8em; color: $ty-color-blue; }
}

.accEditTip{
  font-size: 0.8em;
  .orangeTip{ color:$ty-color-orange; }
}


@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(180deg);
  }
}
@-webkit-keyframes twink {
  from {opacity: 1.0;}
  50% {opacity: 0.4;}
  to {opacity: 1.0;}
}
@keyframes twink {
  from {opacity: 1.0;}
  50% {opacity: 0.2;}
  to {opacity: 1.0;}
}
</style>

