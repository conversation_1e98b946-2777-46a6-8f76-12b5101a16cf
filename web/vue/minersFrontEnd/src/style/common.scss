@charset "utf-8";

@use "variable";
@use "normalize.css";


html,body{
  height: 100%;
}

li{
  list-style: none;
}

.ty-left{
  float: left;
}
.ty-right{
  float: right;
}
.ty-center{
  text-align: center;
}
.ty-clear{
  clear: both;
}
.ty-linkBtn-red{
  color: $tyBounce-color-red; font-size:15px; display: inline-block; padding:0 10px; cursor: pointer; text-decoration: none;
  &:hover{
    text-decoration: underline;
  }
}
.ty-linkBtn, .ty-linkBtn-blue{
  color: $tyBounce-color-blue; font-size:15px; display: inline-block; padding:0 10px; cursor: pointer; text-decoration: none;
  &:hover{
    text-decoration: underline;
  }
  &:last-child{
    padding-right: 0;
  }
}

.ty-btn{ text-decoration: none;  padding:0 10px;cursor: pointer; background-color:#fff;color:#101010; height:24px; line-height:24px; display:inline-block; border:none;       }
.ty-btn-big{ padding:0 24px;height:32px; line-height:32px;        }
.ty-btn-gray,
.ty-btn-group > .ty-btn.ty-btn-gray
{ background-color: $tyBounce-color-gray; color:#666;   }
.ty-btn-darkGreen{ background-color:$ty-color-green; color:#fff;      }
.ty-btn-green{ background-color:$tyBounce-color-green; color:#fff;      }
.ty-btn-cyan{ background-color:$ty-color-cyan; color:#fff;      }
.ty-btn-red{ background-color:$ty-color-red; color:#fff;      }
.ty-btn-blue,
.ty-btn-group > .ty-btn.ty-btn-blue
{ background-color:$ty-color-blue-btn; color:#fff;      }
.ty-btn-blue.active{ background-color: $ty-color-blue-btn-active; color:#fff;      }
.ty-btn-orange{ background-color:$ty-color-orange; color:#fff;      }
.ty-btn-yellow{ background-color: $ty-color-yellow; color:#000;      }
.ty-btn-green:hover{ color:#fff;      }
.ty-btn-red:hover{ color:#fff;      }
.ty-btn-blue:hover{ color:#fff; background-color: $ty-color-blue-btn-hover;     }
.ty-btn-orange:hover{ color:#fff;      }
.ty-btn-disabled{  cursor: default;  background-color: #ccc;  color:#666;  }
.ty-btn-disabled:hover{  cursor: default;  background-color: #ccc;  color:#666;  }
.ty-btn:disabled {cursor: default;  background-color: #ccc;  color:#666;}


.bonceFoot .bounce-ok { margin:0 10px;  }
.bonceFoot .bounce-cancel {  background:$tyBounce-color-gray; margin:0 10px;   }

.bounce-blue .bonceFoot .bounce-ok {  background: $tyBounce-color-blue;  color: #fff;  }
.bounce-red .bonceFoot .bounce-ok {  background: $tyBounce-color-red;  color: #fff;  }
.bounce-green .bonceFoot .bounce-ok {  background: $tyBounce-color-green!important;  color: #fff;  }
.bounce-orange .bonceFoot .bounce-ok {  background: $tyBounce-color-orange;  color: #fff;  }
.bounce-cyan .bonceFoot .bounce-ok {  background: $tyBounce-color-cyan;  color: #fff;  }

/*  circle   */
.ty-circle-5{ border-radius:5px;   }
.ty-circle-3{ border-radius:3px;   }
.ty-circle-10{ border-radius:10px;   }


/*  table  */
.ty-table{ width:100%; border-collapse: collapse;  }
.ty-table td{ border:1px solid #d7d7d7; font-size:14px; text-align:center; padding:0 15px; height:40px;  word-wrap: break-word;  word-spacing:normal;word-break: break-all ;color:#101010     }
.ty-table thead td{ border-color:#fff #fff #d7d7d7 #fff ;   }
.ty-table-headerLine thead td{ border-color: #d7d7d7 ;                   }
.ty-table tbody td{ background-color:#fff;                 }
.ty-table tbody tr:hover td{ background-color:#f0f8ff;              }
.ty-table-control tbody tr td span{ cursor:pointer; white-space:nowrap  }
.ty-table-control tbody tr td span{ cursor:pointer; white-space:nowrap;  }

.ty-head-yellow td{  border-color: #fff2cc ;  background-color: #fff2cc; }

.ty-table-txtLeft td{  text-align: left;  }
.ty-table-txtRight td{  text-align: right;  }
td.ty-td-txtCenter {  text-align: center;  }

.ty-table-control td:last-child span ,
.ty-table-control td:last-child a ,
.ty-td-control span ,
.ty-td-control a
{ font-size:12px; font-family: 宋体;  padding:5px 15px 5px; border-radius:3px; font-weight:bold; cursor:pointer;   }

.ty-table-control .ty-color-blue, .ty-td-control .ty-color-blue{
  color: $tyBounce-color-blue;
}

.ty-color-blue{  color: $tyBounce-color-blue; }
.ty-color-green{  color: $tyBounce-color-green; }
.ty-color-red{  color: $ty-color-red; }
.ty-color-orange{  color: $ty-color-orange; }
.ty-color-gray{ color: $ty-color-gray; }

.ty-table-control .ty-color-green, .ty-td-control .ty-color-green{
  color: $tyBounce-color-green;
}

.ty-table-control .ty-color-blue:hover,
.ty-td-control .ty-color-blue:hover
{ color:#fff; background-color:$tyBounce-color-blue; transition: all .2s    }

.ty-table-control .ty-color-green:hover,
.ty-td-control .ty-color-green:hover
{ color:#fff; background-color:$tyBounce-color-green;     }

.ty-table-control .ty-color-red:hover,
.ty-td-control .ty-color-red:hover
{ color:#fff; background-color:$ty-color-red;     }

.ty-table-control .ty-color-cyan:hover,
.ty-td-control .ty-color-cyan:hover
{ color:#fff; background-color:$ty-color-cyan;     }
.ty-table-control .ty-color-orange:hover,
.ty-td-control .ty-color-orange:hover{ color:#fff; background-color:$ty-color-orange;     }
.ty-table-control .ty-color-darkBlue:hover,
.ty-td-control .ty-color-darkBlue:hover{ color:#fff; background-color:$ty-color-darkBlue;     }

.ty-table-none tbody tr td{ border:none; background-color: transparent; }
.ty-table-control .ty-color-gray:hover, .ty-td-control .ty-color-gray:hover{ color:#fff; background-color:#aaa;     }
.ty-table-left td {  text-align: left;  }
.ty-table-header, .ty-bold
{ font-size:12px; font-family: 宋体;  padding:5px 15px 5px; border-radius:3px; font-weight:bold;    }
.ty-txt-left{ text-align: left!important;  }
.ty-txt-right{ text-align: right!important;  }
.ty-table td.ty-td-gray, .ty-table tbody tr:hover td.ty-td-gray {
  background: #eaeaea;
}


.ty-secondTab { border-bottom: 1px solid #eaeaea; font-size: 14px; line-height: 50px; height: 50px; }
.ty-secondTab li.ty-active { border-bottom: 2px solid $tyBounce-color-green; color: $tyBounce-color-green; }
.ty-secondTab li { list-style: none; display: inline-block; height: 50px; padding: 0 20px; cursor: pointer; }
/*按钮组*/
.ty-btn-group{  position: relative;  display: inline-block;  vertical-align: middle; white-space: nowrap ;font-size: 0;  -webkit-text-size-adjust:none;}
.ty-btn-group > .ty-btn {  position: relative;   background-color: #f2f2f2;  color: #aaa; font-size: 14px }
.ty-btn-group > .ty-btn:last-child:not(:first-child) {  border-top-left-radius: 0;  border-bottom-left-radius: 0;  }
.ty-btn-group > .ty-btn:first-child:not(:last-child):not(.dropdown-toggle) {  border-top-right-radius: 0;  border-bottom-right-radius: 0;  }
.ty-btn-group > .ty-btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {  border-radius: 0;  }
.ty-btn-group > .ty-btn-active-green{  background-color:#48cfad; color:#fff;  }
.ty-btn-group > .ty-btn-active-blue{  background-color:#5d9cec; color:#fff;  }
.ty-btn-group > .ty-btn-active-red{  background-color:#ed5565; color:#fff;  }






