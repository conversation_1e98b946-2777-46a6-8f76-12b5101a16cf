package cn.sphd.miners.modules.material.service.impl;

import cn.sphd.miners.modules.material.dao.MtMaterialApplyDao;
import cn.sphd.miners.modules.material.entity.MtMaterialApply;
import cn.sphd.miners.modules.material.service.MtMaterialApplyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by Administrator on 2017/8/2.
 */
@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class MtMaterialApplyServiceImpl implements MtMaterialApplyService {
    @Autowired
    MtMaterialApplyDao mtMaterialApplyDao;
    @Override
    public void addMtMaterialApply(MtMaterialApply mtMaterialApply) {
        mtMaterialApplyDao.save(mtMaterialApply);
    }

    @Override
    public MtMaterialApply getById(Integer ID, Integer oid) {
        String hql=" from MtMaterialApply o where o.id="+ID+" and o.oid="+oid;
        return mtMaterialApplyDao.getByHQL(hql);
    }

    @Override
    public List<MtMaterialApply> getMtMaterialApplyLists(Integer oid, Integer state) {
        String hql=" from MtMaterialApply o where o.oid="+oid+" and o.state="+state+" and o.type=1"+" order by o.createDate asc";
        return mtMaterialApplyDao.getListByHQL(hql);
    }

    @Override
    public List<MtMaterialApply> getMtMaterialApplyLists1(Integer oid) {
        String hql=" from MtMaterialApply o where o.oid="+oid+" and o.type=1 order by o.createDate asc";
        return mtMaterialApplyDao.getListByHQL(hql);
    }

    @Override
    public void updateMtMaterialApply(MtMaterialApply mtMaterialApply) {
        mtMaterialApplyDao.update(mtMaterialApply);
    }
}
