package cn.sphd.miners.modules.equipment.service.impl;

import java.util.List;
import java.util.Map;

import cn.sphd.miners.modules.equipment.entity.TEquEquipmentHistory;
import cn.sphd.miners.modules.equipment.mapper.TEquEquipmentHistoryMapper;
import cn.sphd.miners.modules.equipment.service.ITEquEquipmentHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 装备管理_装备器具历史
 * 20230209 1.247装备器具新增
 * Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Service
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class TEquEquipmentHistoryServiceImpl implements ITEquEquipmentHistoryService {
    @Autowired
    private TEquEquipmentHistoryMapper tEquEquipmentHistoryMapper;

    /**
     * 查询装备管理_装备器具历史
     * 20230209 1.247装备器具新增
     *
     * @param id 装备管理_装备器具历史
     *           20230209 1.247装备器具新增
     *           主键
     * @return 装备管理_装备器具历史
     * 20230209 1.247装备器具新增
     */
    @Override
    public TEquEquipmentHistory selectTEquEquipmentHistoryById(Long id) {
        return tEquEquipmentHistoryMapper.selectTEquEquipmentHistoryById(id);
    }

    /**
     * 查询装备管理_装备器具历史
     * 20230209 1.247装备器具新增
     * 列表
     *
     * @param tEquEquipmentHistory 装备管理_装备器具历史
     *                             20230209 1.247装备器具新增
     * @return 装备管理_装备器具历史
     * 20230209 1.247装备器具新增
     */
    @Override
    public List<TEquEquipmentHistory> selectTEquEquipmentHistoryList(TEquEquipmentHistory tEquEquipmentHistory) {
        return tEquEquipmentHistoryMapper.selectTEquEquipmentHistoryList(tEquEquipmentHistory);
    }

    /**
     * 新增装备管理_装备器具历史
     * 20230209 1.247装备器具新增
     *
     * @param tEquEquipmentHistory 装备管理_装备器具历史
     *                             20230209 1.247装备器具新增
     * @return 结果
     */
    @Override
    public int insertTEquEquipmentHistory(TEquEquipmentHistory tEquEquipmentHistory) {
        return tEquEquipmentHistoryMapper.insertTEquEquipmentHistory(tEquEquipmentHistory);
    }

    /**
     * 修改装备管理_装备器具历史
     * 20230209 1.247装备器具新增
     *
     * @param tEquEquipmentHistory 装备管理_装备器具历史
     *                             20230209 1.247装备器具新增
     * @return 结果
     */
    @Override
    public int updateTEquEquipmentHistory(TEquEquipmentHistory tEquEquipmentHistory) {
        return tEquEquipmentHistoryMapper.updateTEquEquipmentHistory(tEquEquipmentHistory);
    }

    /**
     * 批量删除装备管理_装备器具历史
     * 20230209 1.247装备器具新增
     *
     * @param ids 需要删除的装备管理_装备器具历史
     *            20230209 1.247装备器具新增
     *            主键
     * @return 结果
     */
    @Override
    public int deleteTEquEquipmentHistoryByIds(String... ids) {
        return tEquEquipmentHistoryMapper.deleteTEquEquipmentHistoryByIds(ids);
    }

    /**
     * 删除装备管理_装备器具历史
     * 20230209 1.247装备器具新增
     * 信息
     *
     * @param id 装备管理_装备器具历史
     *           20230209 1.247装备器具新增
     *           主键
     * @return 结果
     */
    @Override
    public int deleteTEquEquipmentHistoryById(Long id) {
        return tEquEquipmentHistoryMapper.deleteTEquEquipmentHistoryById(id);
    }

    @Override
    public List<Map<String, Object>> history(Long id) {
        return tEquEquipmentHistoryMapper.history(id);
    }
}
