package cn.sphd.miners.modules.schedule.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.schedule.entity.MemoSchedule;
import cn.sphd.miners.modules.schedule.entity.MemoScheduleHistory;
import cn.sphd.miners.modules.schedule.entity.MemoScheduleImageHistory;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2019/8/13.
 */
public interface MemoScheduleHistoryService {

    Map<String,Object> getMemoScheduleHistories(Integer id, PageInfo pageInfo);

    MemoScheduleHistory getNewMemoScheduleHistory(Integer scheduleId);// 获取最新的历史记录

    MemoScheduleHistory  saveMemoScheduleHistory(MemoSchedule memoSchedule, User user, Boolean delFileUsing,String source);//产生日程历史记录

    MemoScheduleHistory getMemoScheduleHistoryById(Integer id);

    MemoScheduleImageHistory getMemoScheduleImageHistoryById(Integer id);

    List<MemoScheduleImageHistory> getMemoScheduleImageHistories(Integer memoScheduleHistoryId);
}
