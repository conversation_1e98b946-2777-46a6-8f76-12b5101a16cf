package cn.sphd.miners.modules.investigate.dto;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.investigate.entity.InvestigateAnswer;
import cn.sphd.miners.modules.investigate.entity.InvestigateQuestion;
import cn.sphd.miners.modules.investigate.entity.InvestigateQuestionKey;

import java.util.List;

public class  RespInvestigateQuestion extends InvestigateQuestion {
    private Integer answerSum;
    private List<InvestigateQuestionKey> investigateQuestionKeyList;
    private String investigateQuestionKeyJson;
    private List<InvestigateAnswer> investigateAnswerList;
    private List<RespInvestigateQuestionKey> respInvestigateQuestionKeyList;
    private PageInfo pageInfo;

    public PageInfo getPageInfo() {
        return pageInfo;
    }

    public void setPageInfo(PageInfo pageInfo) {
        this.pageInfo = pageInfo;
    }

    public List<RespInvestigateQuestionKey> getRespInvestigateQuestionKeyList() {
        return respInvestigateQuestionKeyList;
    }

    public void setRespInvestigateQuestionKeyList(List<RespInvestigateQuestionKey> respInvestigateQuestionKeyList) {
        this.respInvestigateQuestionKeyList = respInvestigateQuestionKeyList;
    }

    public Integer getAnswerSum() {
        return answerSum;
    }

    public void setAnswerSum(Integer answerSum) {
        this.answerSum = answerSum;
    }

    public List<InvestigateAnswer> getInvestigateAnswerList() {
        return investigateAnswerList;
    }

    public void setInvestigateAnswerList(List<InvestigateAnswer> investigateAnswerList) {
        this.investigateAnswerList = investigateAnswerList;
    }

    public String getInvestigateQuestionKeyJson() {
        return investigateQuestionKeyJson;
    }

    public void setInvestigateQuestionKeyJson(String investigateQuestionKeyJson) {
        this.investigateQuestionKeyJson = investigateQuestionKeyJson;
    }

    public List<InvestigateQuestionKey> getInvestigateQuestionKeyList() {
        return investigateQuestionKeyList;
    }

    public void setInvestigateQuestionKeyList(List<InvestigateQuestionKey> investigateQuestionKeyList) {
        this.investigateQuestionKeyList = investigateQuestionKeyList;
    }
}
