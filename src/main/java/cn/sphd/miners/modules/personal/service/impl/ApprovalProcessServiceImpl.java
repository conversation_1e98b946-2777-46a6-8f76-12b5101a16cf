package cn.sphd.miners.modules.personal.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.generalAffairs.service.PersonalService;
import cn.sphd.miners.modules.personal.dao.ApprovalProcessDao;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.personal.service.UserMessageService;
import cn.sphd.miners.modules.sales.service.PoService;
import cn.sphd.miners.modules.system.dao.ApprovalItemDao;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.dto.UserHonePageDto;
import cn.sphd.miners.modules.system.entity.ApprovalFlow;
import cn.sphd.miners.modules.system.entity.ApprovalItem;
import cn.sphd.miners.modules.system.entity.Offer;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import io.netty.util.internal.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by Administrator on 2016/12/22.
 */
@Service("approvalProcessService")
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class ApprovalProcessServiceImpl implements ApprovalProcessService {
    @Autowired
    ApprovalProcessDao approvalProcessDao;
    @Autowired
    UserDao userDao;
    @Autowired
    ApprovalItemDao approvalItemDao;

    @Autowired
    UserMessageService userMessageService;
    @Autowired
    UserService userService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    SWMessageService swMessageService;
    @Autowired
    PersonalService personalService;
    @Autowired
    PoService poService;

    @Override
    public void saveApprovalProcess(ApprovalProcess approvalProcess) {
        if (approvalProcess.getHandleTime()==null) {
            approvalProcess.setHandleTime(new Date());
        }
        approvalProcessDao.save(approvalProcess);
    }

//    @Override
//    public List<ApprovalProcess> getApprovalProcessByApprovalStatus(Integer toUserId, String approvalStatus) {
//        String hql=" and o.reimburse!=null and o.toUser="+toUserId;
//        if (approvalStatus!=null){
//            hql+=" and o.approveStatus="+approvalStatus;
//        }else {
//            hql+=" and o.approveStatus=1";
//        }
//        Map<String,String> orderBy = new HashMap<String, String>();
//        orderBy.put("id","desc");
//        return approvalProcessDao.findCollectionByConditionNoPage(hql,null,orderBy);
//    }

    @Override
    public ApprovalProcess getApprovalProcessById(Integer id) {
        return approvalProcessDao.get(id);
    }

    @Override
    public void updateApprovalProcess(ApprovalProcess approvalProcess) {
        approvalProcessDao.update(approvalProcess);
    }

    @Override
    public List<ApprovalProcess> getApprovalProcessByPersonnelReimburseId(Integer id) {
        Map<String, Object> params = new HashMap<>();
        String hql = "from ApprovalProcess where reimburse_=:reimburse order by createDate";
        params.put("reimburse", id);
        return approvalProcessDao.getListByHQLWithNamedParams(hql, params);
    }

    @Override
    public List<ApprovalProcess> getApprovalProcessByBusiness(Integer business, Integer businessType, String approvalStatus) {
        String hql = " from ApprovalProcess where business=:business and businessType=:businessType";
        Map<String, Object> map = new HashMap<>();
        map.put("business", business);
        map.put("businessType", businessType);
        if (!"".equals(approvalStatus) && approvalStatus != null) {
            hql += " and approveStatus=:approvalStatus";
            map.put("approvalStatus", approvalStatus);
        }
        List<ApprovalProcess> approvalProcesses = approvalProcessDao.getListByHQLWithNamedParams(hql, map);
        return approvalProcesses;
    }

    @Override
    public List<ApprovalProcess> getApprovalProcessByDescription(String description, Integer businessType, String approvalStatus) {
        String hql = " from ApprovalProcess where approveMemo=:description and businessType=:businessType";
        Map<String, Object> map = new HashMap<>();
        map.put("description", description);
        map.put("businessType", businessType);
        if (!"".equals(approvalStatus) && approvalStatus != null) {
            hql += " and approveStatus=:approvalStatus";
            map.put("approvalStatus", approvalStatus);
        }
        List<ApprovalProcess> approvalProcesses = approvalProcessDao.getListByHQLWithNamedParams(hql, map);
        return approvalProcesses;
    }

    @Override
    public List<ApprovalProcess> getApprovalProcessByBusinessType(Integer toUserId, String approvalStatus, Integer businessType, Date beginTime, Date endTime, PageInfo page, String sort) {
        Map<String, Object> map = new HashMap<>();
        String hql = "from ApprovalProcess where toUser=:toUserId";
        map.put("toUserId", toUserId);
        if (!MyStrings.nulltoempty(approvalStatus).isEmpty()) {
            hql += " and approveStatus=:approvalStatus";
            map.put("approvalStatus", approvalStatus);
        } else {
            hql += " and approveStatus=1";
        }
        if (businessType != null) {
            hql += " and businessType=:businessType";
            map.put("businessType", businessType);
        }
        if (beginTime != null && endTime != null) {
            hql += " and createDate>=:beginTime and createDate<=:endTime";
            map.put("beginTime", beginTime);
            map.put("endTime", endTime);
        }
        if (!MyStrings.nulltoempty(sort).isEmpty()) {
            hql += " order by handleTime";
            if ("desc".equals(sort)) {
                hql += " desc";
            }
        }
        return approvalProcessDao.getListByHQLWithNamedParams(hql, map);
    }

    @Override
    public List<ApprovalProcess> getApprovalProcessByFromUser(Integer userId, String approvalStatus, Integer businessType, Date beginTime, Date endTime, String sort) {
        approvalProcessDao.getSession().flush();
        Map<String, Object> map = new HashMap<>();
        String hql = "from ApprovalProcess where fromUser=:userId";
        map.put("userId", userId);
        if (!MyStrings.nulltoempty(approvalStatus).isEmpty()) {
            hql += " and approveStatus=:approvalStatus";
            map.put("approvalStatus", approvalStatus);
        } else {
            hql += " and approveStatus=1";
        }
        if (businessType != null) {
            hql += " and businessType=:businessType";
            map.put("businessType", businessType);
        }
        if (beginTime != null && endTime != null) {
            hql += " and createDate >= :beginTime and createDate <= :endTime";
            map.put("beginTime", beginTime);
            map.put("endTime", endTime);
        }
        if (!MyStrings.nulltoempty(sort).isEmpty()) {
            hql += " order by handleTime";
            if ("desc".equals(sort)) {
                hql += " desc";
            }
        }
        List<ApprovalProcess> approvalProcess = approvalProcessDao.getListByHQLWithNamedParams(hql, map);
        if (businessType != null && 10 == businessType) {
            for (ApprovalProcess approvalProcess1 : approvalProcess) {
                if (approvalProcess1.getApproveMemo() == null) {
                    approvalProcess1.setCreateDateItem(getCreateDateItem(approvalProcess1.getBusiness()));
                } else {
                    long d = Long.parseLong(approvalProcess1.getApproveMemo());
                    approvalProcess1.setCreateDateItem(new Date(d));
                }
            }
        }
        return approvalProcess;
    }


    @Override
    public List<ApprovalProcess> getApprovalProcessByFromUser(Integer oid, Integer userId, String approvalStatus, Integer businessType, Date beginTime, Date endTime, String sort) {
        approvalProcessDao.getSession().flush();
        Map<String, Object> map = new HashMap<>();
        String hql = "from ApprovalProcess where fromUser=:userId";
        map.put("userId", userId);
        if (oid != null) {
            hql += " and org=:org";
            map.put("org", oid);
        }
        if (!MyStrings.nulltoempty(approvalStatus).isEmpty()) {
            hql += " and approveStatus=:approvalStatus";
            map.put("approvalStatus", approvalStatus);
        } else {
            hql += " and approveStatus=1";
        }
        if (businessType != null) {
            hql += " and businessType=:businessType";
            map.put("businessType", businessType);
        }
        if (beginTime != null && endTime != null) {
            hql += " and createDate >= :beginTime and createDate <= :endTime";
            map.put("beginTime", beginTime);
            map.put("endTime", endTime);
        }
        if (!MyStrings.nulltoempty(sort).isEmpty()) {
            hql += " order by handleTime";
            if ("desc".equals(sort)) {
                hql += " desc";
            }
        }
        List<ApprovalProcess> approvalProcess = approvalProcessDao.getListByHQLWithNamedParams(hql, map);
        if (businessType != null && 10 == businessType) {
            for (ApprovalProcess approvalProcess1 : approvalProcess) {
                if (approvalProcess1.getApproveMemo() == null) {
                    approvalProcess1.setCreateDateItem(getCreateDateItem(approvalProcess1.getBusiness()));
                } else {
                    long d = Long.parseLong(approvalProcess1.getApproveMemo());
                    approvalProcess1.setCreateDateItem(new Date(d));
                }
            }
        }
        return approvalProcess;
    }

    private Date getCreateDateItem(Integer business) {
        Map<String, Object> map = new HashMap<>();
        String hql = "select createDate from ApprovalItem where id=:business";
        map.put("business", business);
        return (Date) approvalItemDao.getByHQLWithNamedParams(hql, map);
    }

    @Override
    public List<ApprovalProcess> getApprovalProcessByMid(String mid, String approvalStatus, Integer oid) {
        String hql = " and o.reimburse!=null and o.reimburse.user.oid=" + oid + " and o.toMid='" + mid + "'";
        if (approvalStatus != null) {
            hql += " and o.approveStatus=" + approvalStatus;
        } else {
            hql += " and o.approveStatus=6";
        }
        Map<String, String> orderBy = new HashMap<String, String>();
        orderBy.put("id", "desc");
        return approvalProcessDao.findCollectionByConditionNoPage(hql, null, orderBy);
    }

    @Override
    public List<ApprovalProcess> getApprovalProcessListByToUserId(Integer toUserId, String approvalStatus, PageInfo pageInfo) {
        String hql = " and o.reimburse!=null and o.toUser=" + toUserId;
        if (approvalStatus != null) {
            hql += " and o.approveStatus=" + approvalStatus;
        } else {
            hql += " and o.approveStatus=1";
        }
        Map<String, String> orderBy = new HashMap<String, String>();
        orderBy.put("id", "desc");
        return approvalProcessDao.findCollectionByConditionWithPage(hql, null, orderBy, pageInfo);//.findCollectionByConditionNoPage(hql,null,orderBy);

    }

    @Override
    public List<ApprovalProcess> getApprovalProcessListByToUserIdCreateDate(Integer toUserId, String approvalStatus, String beginDate, String endDate, Integer feeCat, Integer billCat, String userName) {
        String hql = " and o.reimburse!=null and o.toUser=" + toUserId + " and o.approveStatus='" + approvalStatus + "'";
        if (beginDate != null && !beginDate.equals("")) {
            hql += " and o.handleTime>='" + beginDate + "'";
        }
        if (endDate != null && !endDate.equals("")) {
            hql += " and o.handleTime<='" + endDate + "'";
        }
        if (feeCat != null) {
            hql += " and o.reimburse.feeCat=" + feeCat;
        }
        if (billCat != null) {
            hql += " and o.reimburse.billCat=" + billCat;
        }
        if (userName != null && !userName.equals("")) {
            hql += " and o.reimburse.user.userName like '%" + userName + "%'";
        }
        Map<String, String> orderBy = new HashMap<String, String>();
        orderBy.put("id", "desc");
        return approvalProcessDao.findCollectionByConditionNoPage(hql, null, orderBy);
    }

    @Override
    public List<ApprovalProcess> getApprovalProcessOutTime(Integer toUserId, String approvalStatus, Integer businessType, String beginTime, String endTime, PageInfo page) {
        String hql = " and o.toUser=" + toUserId;
        Map<String, String> orderBy = new HashMap<String, String>();
        if (!"3".equals(approvalStatus)) {
            hql += " and o.approveStatus=" + approvalStatus;
            orderBy.put("id", "asc");
            if (businessType != null) {
                hql += " and o.businessType=" + businessType;
            }
        } else {
            hql += " and (o.businessType=2 or o.businessType=6)";
            orderBy.put("id", "desc");
        }
        if (beginTime != null && endTime != null) {
            hql += " and o.createDate >= '" + beginTime + " 00:00:00' and o.createDate <= '" + endTime + " 23:59:59'";
        }
        return approvalProcessDao.findCollectionByConditionWithPage(hql, null, orderBy, page);//.findCollectionByConditionNoPage(hql,null,orderBy);
    }

    @Override
    public List<ApprovalProcess> getApprovalProcessByBusinessType(Integer oid, String approvalStatus, Integer businessType) {
        Map<String,Object> map=new HashMap<>();

        String hql=" from ApprovalProcess where org=:oid ";
        map.put("oid",oid);
        if (approvalStatus != null) {
            hql += " and approveStatus=:approvalStatus";
            map.put("approvalStatus",approvalStatus);
        } else {
            hql += " and approveStatus=1";
        }

        if (businessType != null) {
            hql += " and businessType=:businessType";
            map.put("businessType",businessType);
        }

        return approvalProcessDao.getListByHQLWithNamedParams(hql+=" order by id desc", map);
    }


    @Override
    public List<ApprovalProcess> getApprovalProcessByBusinessDesc(Integer business, Integer businessType, String approvalStatus) {
        String hql = "";
        if (business != null) {
            hql += "and o.business=" + business;
        }
        if (businessType != null) {
            hql += " and o.businessType=" + businessType;
        }
        if (!"".equals(approvalStatus) && approvalStatus != null) {
            hql += " and o.approveStatus = " + approvalStatus;
        }
        Map<String, String> orderBy = new HashMap<String, String>();
        orderBy.put("o.handleTime", "desc");
        return approvalProcessDao.findCollectionByConditionNoPage(hql, null, orderBy);
    }

    @Override
    public void updateApprovalProcessListByToUser(Integer oldUserId, Integer newUserId) {
        String hql = "update ApprovalProcess set toUser=:newUserId where toUser=:oldUserId";
        Map<String, Object> map = new HashMap<>();
        map.put("newUserId", newUserId);
        map.put("oldUserId", oldUserId);
        approvalProcessDao.queryHQLWithNamedParams(hql, map);
    }

    @Override
    public Integer addApplyApprovalProcess(Integer business, Integer businessType, User user, String description, ApprovalItem approvalItem) {
        ApprovalProcess app = new ApprovalProcess();
        Integer handleId = null;
        String handleName = "";//审批人总称
        String userName = "";//审批人名字
        Integer uid = 0;
        for (ApprovalFlow f : approvalItem.getApprovalFlowHashSet()) {
            if (f.getLevel() == 1) {
                uid = f.getToUserId();
                handleName = f.getToUser();
                userName = f.getUserName();
            }
        }
        if (0 == uid) {
            handleId = Integer.decode(user.getLeader());//直属上级
            userName = userService.getUserByID(Integer.decode(user.getLeader())).getUserName();
        } else {
            handleId = uid;//指定的人
        }
        app.setLevel(1);
        app.setApproveStatus("1");
        app.setToUser(handleId);
        app.setBusiness(business);
        app.setBusinessType(businessType);//业务类型  1-财务修改，2- 加班，3-请假
        app.setToUserName(handleName);//审批人总称
        app.setUserName(userName);//审批人名称
        app.setCreateDate(new Date());
        app.setDescription(user.getUserName() + "的" + description);
        app.setHandleTime(new Date());
        app.setFromUser(user.getUserID());//申请人 id
        app.setAskName(user.getUserName());//申请人名
        app.setOrg(user.getOid());
        approvalProcessDao.save(app);
        return handleId;
    }

    @Override
    public ApprovalProcess addApplyApprovalProcess(Integer business, Integer businessType, String description, Integer level, Integer fromUser, String askName, User user, ApprovalItem approvalItem) {
        ApprovalProcess app = new ApprovalProcess();
        Integer handleId = null;
        String handleName = "";//审批人总称
        String userName = "";//审批人名字
        Integer uid = 0;
        for (ApprovalFlow f : approvalItem.getApprovalFlowHashSet()) {
            if (f.getLevel() == level) {
                uid = f.getToUserId();
                handleName = f.getToUser();
                userName = f.getUserName();
            }
        }
        if (0 == uid) {
            handleId = Integer.decode(user.getLeader());//直属上级
            userName = userService.getUserByID(Integer.decode(user.getLeader())).getUserName();
        } else {
            handleId = uid;//指定的人
        }
        app.setLevel(level);
        app.setApproveStatus("1");
        app.setToUser(handleId);
        app.setBusiness(business);
        app.setBusinessType(businessType);//业务类型  1-财务修改，2- 加班，3-请假
        app.setToUserName(handleName);//审批人总称
        app.setUserName(userName);//审批人名称
        app.setCreateDate(new Date());
        app.setDescription(description);
        app.setHandleTime(new Date());
        app.setFromUser(fromUser);//申请人 id
        app.setAskName(askName);//申请人名
        app.setOrg(user.getOid());
        approvalProcessDao.save(app);
        return app;
    }

    @Override
    public ApprovalProcess addApprovalProcess(Integer business, Integer businessType, String description, Integer level, User applyUser, User approvalUser,String handleName) {
        ApprovalProcess app = new ApprovalProcess();
        //handleName 审批人总称    比如 叫‘文管’、“总务、财务”之类的
        // userName 审批人实际姓名

        app.setLevel(level);
        app.setApproveStatus("1");
        app.setBusiness(business);
        app.setBusinessType(businessType);//业务类型  1-财务修改，2- 加班，3-请假
        app.setToUser(approvalUser.getUserID());
        app.setToUserName(handleName);//审批人总称
        app.setUserName(approvalUser.getUserName());//审批人名称
        app.setCreateDate(new Date());
        app.setDescription(description);
        app.setHandleTime(new Date());
        app.setFromUser(applyUser.getUserID());//申请人 id
        app.setAskName(applyUser.getUserName());//申请人名
        app.setOrg(applyUser.getOid());
        approvalProcessDao.save(app);
        return app;
    }

    @Override
    public Integer getApprovalProcessCounts(Integer toUserId, Integer... businessType) {
        String hql = "select count(*) from ApprovalProcess where toUser=:toUserId and approveStatus='1'";
        Map<String, Object> map = new HashMap<>();
        map.put("toUserId", toUserId);
        if (businessType != null) {
            hql += " and businessType in(:businessType)";
            map.put("businessType", businessType);
        }
        Long result = (Long) approvalProcessDao.getByHQLWithNamedParams(hql, map);
        return result.intValue();
    }

    @Override
    public Integer getApprovalProcessCountsMid(Integer oid, String mid, Integer... businessType) {
        String hql = "select count(*) from ApprovalProcess where org=:org and approveStatus='1'";
        Map<String, Object> map = new HashMap<>();
        map.put("org", oid);
        if (!MyStrings.nulltoempty(mid).isEmpty()) {
            hql += " and toMid=:mid";
            map.put("mid", mid);
        }
        if (businessType != null) {
            hql += " and businessType in(:businessType)";
            map.put("businessType", businessType);
        }
        Long result = (Long) approvalProcessDao.getByHQLWithNamedParams(hql, map);
        return result.intValue();
    }

    @Override
    public List<UserHonePageDto> getUserHonePageDtoListByApprovalId(Integer userId, Integer businessType) {
        String hql = "select new cn.sphd.miners.modules.system.dto.UserHonePageDto(userID,userName,departName,mobile,isDuty) from User where userID in(select fromUser from ApprovalProcess where businessType=:businessType and toUser=:userId)";
        Map<String, Object> map = new HashMap<>();
        map.put("userId", userId);
        map.put("businessType", businessType);
        List<UserHonePageDto> userHonePageDtoList = approvalProcessDao.getListByHQLWithNamedParams(hql, map);
        return userHonePageDtoList;
    }

    @Override
    public Integer approvalCompleted(Integer oid, Integer financeAccountId) {
        Map<String, Object> map = new HashMap<>();
        StringBuffer hql = new StringBuffer("select userID from User where oid=:oid and roleCode=:roleCode and isDuty='1'"); //查找机构下的超管或总经理
        map.put("oid", oid);
        map.put("roleCode", "smallSuper");
        Integer superId = (Integer) userDao.getByHQLWithNamedParams(hql.toString(), map);
        if (superId == null) {
            map.put("roleCode", "super");
            superId = (Integer) userDao.getByHQLWithNamedParams(hql.toString(), map);
        }
        hql.setLength(0);
        map.clear();

        hql.append("select oldId from ApprovalProcess where toUser=:toUserId and approveStatus='1' and businessType =:businessType");
        map.put("toUserId", superId);
        map.put("businessType", 1);
        List<Integer> approvalProcesses = approvalProcessDao.getListByHQLWithNamedParams(hql.toString(), map);
        hql.setLength(0);
        hql.append("select newId from ApprovalProcess where toUser=:toUserId and approveStatus='1' and businessType =:businessType");
        List<Integer> approvalProcesses1 = approvalProcessDao.getListByHQLWithNamedParams(hql.toString(), map);

        if (approvalProcesses.size() > 0) {
            Map<String, Object> map1 = new HashMap<>();
            String hql1 = "select count(id) from AccountDetail where (oppositeId = :financeAccountId or accountId = :financeAccountId) and id in (:approvalProcesses)";
            map1.put("financeAccountId", financeAccountId);
            map1.put("approvalProcesses", approvalProcesses);
            long num = (long) approvalProcessDao.getByHQLWithNamedParams(hql1, map1);

            String hql2 = "select count(id) from AccountDetailHistory where (oppositeId = :financeAccountId or accountId = :financeAccountId) and id in (:approvalProcesses)";
            map1.put("approvalProcesses", approvalProcesses1);  //将上一个的值覆盖
            long num1 = (long) approvalProcessDao.getByHQLWithNamedParams(hql2, map1);
            if (num > 0 || num1 > 0) {
                return 1;
            } else {
                return 0;
            }
        } else {
            return 0;
        }
    }

    @Override
    public List<UserHonePageDto> getUserHonePageDtoListByToUserId(Integer toUserId, String userName, Integer businessType, Integer oid) {
        Map<String, Object> map = new HashMap<>();
        String hql = "select new cn.sphd.miners.modules.system.dto.UserHonePageDto(userID,userName,departName,mobile,isDuty) from User where userID in(select fromUser from ApprovalProcess where reimburse_ is not null";
        if (toUserId != null) {
            hql += " and toUser=:userId";
            map.put("userId", toUserId);
        }
        if (oid != null) {
            hql += " and org=:org";
            map.put("org", oid);
        }
        if (businessType != null) {
            hql += " and businessType=:businessType )";
            map.put("businessType", businessType);
        } else {
            hql += " and businessType is null )";
        }
        if (!StringUtil.isNullOrEmpty(userName)) {
            hql += " and userName like:userName";
            map.put("userName", "%" + userName + "%");
        }
        hql += " order by convert(userName, 'gbk') asc";
        List<UserHonePageDto> userHonePageDtoList = userDao.getListByHQLWithNamedParams(hql, map);
        return userHonePageDtoList;
    }

    @Override
    public Integer getBadgeNumber(User user, String code) {
        Integer result = null;
        switch (code) {
            case "approvalOutTime": //加班审批
                result = this.getApprovalProcessCounts(user.getUserID(), 2, 6);
                break;
            case "approvalLeave": //请假审批
                result = this.getApprovalProcessCounts(user.getUserID(), 3, 8);
                break;
            case "financeModifyApproval":  //财务审批--财务修改
                result = this.getApprovalProcessCounts(user.getUserID(), 1);
                break;
            case "approvalSettingsApproval":   //加班/付款审批设置修改申请的审批
                result = this.getApprovalProcessCounts(user.getUserID(), 10);
                break;
            case "offerApproval": //招聘面试官待处理
                result = this.getApprovalProcessCounts(user.getUserID(), 16);
                break;
            case "personalReimburseApproval":   //付款审批-个人报销付款
                result = this.getApprovalProcessCounts(user.getUserID(), 17);
                break;
            case "reviewFinanceReimburse":   //付款复核-个人报销
                result = this.getApprovalProcessCounts(user.getUserID(), 20);
                break;
            case "tryApproval":   //特殊机构新增机构审批
                result = this.getApprovalProcessCounts(user.getUserID(), 18);
                break;
            case "editOrgPopedomApproval":   //特殊机构所选产品修改审批
                result = this.getApprovalProcessCounts(user.getUserID(), 19);
                break;
            case "incrementApproval":   //特殊机构增值服务审批
                result = this.getApprovalProcessCounts(user.getUserID(), 55);
                break;
            case "incrementEditApproval":   //特殊机构增值服务修改的审批
                result = this.getApprovalProcessCounts(user.getUserID(), 56);
                break;
            case "purchaseOrderApproval":   //采购审批--采购订单审批
                result = this.getApprovalProcessCounts(user.getUserID(), 23);
                break;
            case "salaryOutApproval":   //薪资宝转出--付款审批审批人
                result = this.getApprovalProcessCounts(user.getUserID(), 24);
                break;
            case "cashierSalaryOutApproval":   //薪资宝转出--出纳可付款/待付款
                if ("finance".equals(user.getManagerCode())) {   //判断登陆的人是否是财务
                    result = this.getApprovalProcessCountsMid(user.getOid(), "at", 25, 27);
                }
                break;
            case "reviewSalaryOutApproval":   //薪资宝转出--付款复核审批人
                result = this.getApprovalProcessCounts(user.getUserID(), 26);
                break;
            case "personalSubmitAttendanceApproval":   //审批人-个人提交的考勤修改
                result = this.getApprovalProcessCounts(user.getUserID(), 36);
                break;
            case "purchaseBillApproval":   //采购审批人-采购部门的票据审核【1.169采购的票款处理】
                result = this.getApprovalProcessCounts(user.getUserID(), 46);
                break;
            case "purchasePaymentApproval":   //采购审批人-采购部门的付款【1.169采购的票款处理】
                result = this.getApprovalProcessCounts(user.getUserID(), 48);
                break;
            case "purchasePaymentApplyApproval":   //付款审批人-采购部门的常规付款【1.169采购的票款处理】
                result = this.getApprovalProcessCounts(user.getUserID(), 49);
                break;
            case "purchaseFinanceReimburse":   //付款复核者-采购部门报销【1.169采购的票款处理,65-1.229采购之预付款】
                result = this.getApprovalProcessCounts(user.getUserID(), 51, 65);
                break;
            case "purchaseBillFinanceApproval":   //出纳（财务审批人）-采购部门的票据审核【待在线审核和待线下审核之和】【1.169采购的票款处理】
                if ("finance".equals(user.getManagerCode())) {   //判断登陆的人是否是财务
                    result = this.getApprovalProcessCountsMid(user.getOid(), "lp", 45, 47);
                }
                break;
            case "purchasePaymentFinanceApproval":   //出纳（财务审批人）-采购部门的付款【可付款和待付款之和】【1.169采购的票款处理,64和66是1.229采购之预付款对应的】
                if ("finance".equals(user.getManagerCode())) {   //判断登陆的人是否是财务
                    result = this.getApprovalProcessCountsMid(user.getOid(), "lp", 50, 52, 64, 66);
                }
                break;
            case "outOfServiceApproval":   //特殊机构 暂停服务
                result = getApprovalProcessCounts(user.getUserID(), 58);
                break;
            case "restoreServiceApproval":   //特殊机构 恢复服务
                result = getApprovalProcessCounts(user.getUserID(), 59);
                break;
            case "assignOutTime":   //指派加班
                result = getApprovalProcessCounts(user.getUserID(), 60);
                break;
            case "purchaseAdvancePayApply":   //申请人-采购预付款的申请【1.229采购之预付款】
                result = poService.getPrepaymentStatus(user, "1", user.getUserID());
                break;
            case "purchaseAdvancePayApproval":   //付款审批人-采购的预付款【1.229采购之预付款】
                result = this.getApprovalProcessCounts(user.getUserID(), 63);
                break;
            case "needRecoveredAmount":   //审批人-需收回的款【1.231差额处理,1.233差额处理2】
                if ("finance".equals(user.getManagerCode())) {   //判断登陆的人是否是财务
                    result = this.getApprovalProcessCountsMid(user.getOid(), "lp", 69);
                }
                break;
            case "overreceivedFundsApproval":   //审批人-多收来的款-可付款和待付款之和【1.233差额处理2】
                if ("finance".equals(user.getManagerCode())) {   //判断登陆的人是否是财务
                    result = this.getApprovalProcessCountsMid(user.getOid(), "lp", 70,72);
                }
                break;
            case "overreceivedFundsFinance":   //复核审批人(付款复核)-多收来的款【1.233差额处理2】
                result = this.getApprovalProcessCounts(user.getUserID(),  71);
                break;
            case "meetingMiddleApproval":   // 会议中间审批
                result = this.getApprovalProcessCounts(user.getUserID(),  74);
                break;
            case "meetingSignApproval":   //会议通知签收
                result = this.getApprovalProcessCounts(user.getUserID(),  76);
                break;
            case "meetingFinalApproval":   //会议最终审批
                result = this.getApprovalProcessCounts(user.getUserID(),  75);
                break;
        }
        return result;
    }

    @Override
    public List<ApprovalProcess> getApprovalProcessByLeaveItem(Integer business, Integer businessType) {
        Map<String, Object> map = new HashMap<>();
        String hql = "from ApprovalProcess where business=:business and businessType=:businessType and approveStatus!='1' order by id";
        map.put("business", business);
        map.put("businessType", businessType);
        return approvalProcessDao.getListByHQLWithNamedParams(hql, map);
    }

    @Override
    public void choiceInterviewers(Offer offer, Integer handleId,Date handleDate,User loginUser) {

        User handleUser = userDao.get(handleId);
        ApprovalProcess app = new ApprovalProcess();
        app.setApproveStatus("1");
        app.setBusiness(offer.getUserID());
        app.setBusinessType(16);//16招聘管理
        app.setToUser(handleId);
        app.setLevel(1);
        app.setToUserName("面试官");//审批人总称
        app.setUserName(handleUser.getUserName());//审批人名称
        app.setCreateDate(new Date());
        app.setCreator(loginUser.getUserID());
        app.setCreateName(loginUser.getUserName());
        String cont = "应聘者" + offer.getUserName() + "在" + new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(offer.getCreateTime()) + "提交了申请";
        app.setDescription(cont);
        app.setHandleTime(handleDate); //面试时间
//            app.setFromUser(outTime.getUser().getUserID());//申请人 id
        app.setAskName(offer.getUserName());//申请人名
        app.setOrg(loginUser.getOid());
        approvalProcessService.saveApprovalProcess(app);

        personalService.offerRejectSend(1, 1, offer, handleId, "/offerApproval", "有一条申请待审批", cont, "offerApproval");//给面试官推送 任务栏
    }

    @Override
    public List<Integer> getUserIdsByBusiness(Integer business, Integer businessType) {
        Map<String, Object> map = new HashMap<>();
        String hql = "select toUser from ApprovalProcess where business=:business and businessType=:businessType";
        map.put("business", business);
        map.put("businessType", businessType);
        List<Integer> userIdList = approvalProcessDao.getListByHQLWithNamedParams(hql, map);
        return userIdList;
    }

    @Override
    public void deleteApprovalProcess(ApprovalProcess approvalProcess) {
        approvalProcessDao.delete(approvalProcess);
    }

    @Override
    public ApprovalProcess getApprovalProcessByBusToUser(Integer business, Integer businessType, Integer toUser) {
        Map<String, Object> map = new HashMap<>();
        String hql = " from ApprovalProcess where business=:business and businessType=:businessType";
        map.put("business", business);
        map.put("businessType", businessType);
        if (toUser != null) {
            hql += " and toUser=:toUser";
            map.put("toUser", toUser);
        }
        ApprovalProcess approvalProcess = (ApprovalProcess) approvalProcessDao.getByHQLWithNamedParams(hql, map);
        return approvalProcess;
    }
    @Override
    public ApprovalProcess getApprovalProcessByBusToUser(Integer business, Integer businessType, Integer toUser,String approveStatus) {
        Map<String, Object> map = new HashMap<>();
        String hql = " from ApprovalProcess where business=:business and businessType=:businessType";
        map.put("business", business);
        map.put("businessType", businessType);
        if (toUser != null) {
            hql += " and toUser=:toUser";
            map.put("toUser", toUser);
        }
        if (StringUtils.isNotEmpty(approveStatus)){
            hql += " and approveStatus=:approveStatus";
            map.put("approveStatus", approveStatus);
        }
        ApprovalProcess approvalProcess = (ApprovalProcess) approvalProcessDao.getByHQLWithNamedParams(hql, map);
        return approvalProcess;
    }

    @Override
    public List<ApprovalProcess> getApprovalProcessByUserId(Integer oid, Integer toUserId, String approvalStatus, String approvalStatusItem, Integer businessType, Date beginTime, Date endTime, String sort) {
        Map<String, Object> map = new HashMap<>();
        String hql = "from ApprovalProcess a where a.toUser=:toUserId";
        map.put("toUserId", toUserId);
        if ("3".equals(approvalStatusItem)) {
            hql += " and a.approveStatus in (2,3)";
        } else {
            if (!MyStrings.nulltoempty(approvalStatus).isEmpty()) {
                hql += " and a.approveStatus=:approvalStatus";
                map.put("approvalStatus", approvalStatus);
            } else {
                hql += " and a.approveStatus=1";
            }
        }
        if (businessType != null) {
            hql += " and a.businessType=:businessType";
            map.put("businessType", businessType);
        }
        if (beginTime != null && endTime != null) {
            hql += " and a.createDate between :beginTime and :endTime ";
            map.put("beginTime", beginTime);
            map.put("endTime", endTime);
        }
        hql += " and (newId in (select i.id from ApprovalItem i where i.belongTo=:oid and i.approveStatus=:approvalStatusItem  or (i.code='purchaseApprovalSettings' and  a.approveStatus=:approvalStatus)))";
        map.put("oid", oid);
        map.put("approvalStatusItem", approvalStatusItem);
        map.put("approvalStatus", approvalStatus);
        if (!MyStrings.nulltoempty(sort).isEmpty()) {
            hql += " order by a.handleTime";
            if ("desc".equals(sort)) {
                hql += " desc";
            }
        }
        List<ApprovalProcess> approvalProcesses = approvalProcessDao.getListByHQLWithNamedParams(hql, map);
        for (ApprovalProcess approvalProcess1 : approvalProcesses) {
            if (approvalProcess1.getApproveMemo() == null) {
                approvalProcess1.setCreateDateItem(getCreateDateItem(approvalProcess1.getBusiness()));
            } else {
                long d = Long.parseLong(approvalProcess1.getApproveMemo());
                approvalProcess1.setCreateDateItem(new Date(d));
            }
        }
        return approvalProcesses;
    }

    @Override
    public List<ApprovalProcess> getApprovalProcessByFromUserId(Integer oid, Integer userId, String approvalStatus, String approvalStatusItem, Integer businessType, Date beginTime, Date endTime, String sort) {
        Map<String, Object> map = new HashMap<>();
        String hql = "from ApprovalProcess where fromUser=:userId";
        map.put("userId", userId);
        if (!"3".equals(approvalStatusItem)) {  //如果查询驳回的数据，则只根据主表（ApprovalItem）的状态即可
            if (!MyStrings.nulltoempty(approvalStatus).isEmpty()) {
                hql += " and approveStatus=:approvalStatus";
                map.put("approvalStatus", approvalStatus);
            } else {
                hql += " and approveStatus=1";
            }
        }
        if (businessType != null) {
            hql += " and businessType=:businessType";
            map.put("businessType", businessType);
        }
        if (beginTime != null && endTime != null) {
            hql += " and createDate between :beginTime and :endTime";
            map.put("beginTime", beginTime);
            map.put("endTime", endTime);
        }
//        hql+=" and newId in (select id from ApprovalItem where belongTo=:oid and approveStatus=:approvalStatusItem and code in ('overTimeApply','paymentApproval','leaveApply','reimburseApply'))";
        hql += " and (newId in (select id from ApprovalItem where belongTo=:oid and approveStatus=:approvalStatusItem) ) and approveData is  null ";
        map.put("oid", oid);
        map.put("approvalStatusItem", approvalStatusItem);

        hql += " group by business";
        if (!MyStrings.nulltoempty(sort).isEmpty()) {
            hql += " order by handleTime";
            if ("desc".equals(sort)) {
                hql += " desc";
            }
        }
        List<ApprovalProcess> approvalProcesses = approvalProcessDao.getListByHQLWithNamedParams(hql, map);
        List<ApprovalProcess> approvalProcesses2 = getApprovalProcessByFromUserId(userId, approvalStatus, businessType, beginTime, endTime, sort);
        approvalProcesses.addAll(approvalProcesses2);

        Collections.sort(approvalProcesses, new Comparator<ApprovalProcess>() {

            @Override
            public int compare(ApprovalProcess o1, ApprovalProcess o2) {
                int flag = o1.getHandleTime().compareTo(o2.getHandleTime());
                return flag;
            }
        });


        for (ApprovalProcess approvalProcess1 : approvalProcesses) {
            if (approvalProcess1.getApproveMemo() == null) {
                approvalProcess1.setCreateDateItem(getCreateDateItem(approvalProcess1.getBusiness()));
            } else {
                long d = Long.parseLong(approvalProcess1.getApproveMemo());
                approvalProcess1.setCreateDateItem(new Date(d));
            }
        }
        return approvalProcesses;
    }

    // 采购审批专用
    private List<ApprovalProcess> getApprovalProcessByFromUserId(Integer fromUserId, String approvalStatus, Integer businessType, Date beginTime, Date endTime, String sort) {
        Map<String, Object> map = new HashMap<>();
        String hql = "from ApprovalProcess where fromUser=:userId  and approveStatus=:approvalStatus and level=1 and approveData is not null";
        map.put("userId", fromUserId);
        map.put("approvalStatus", approvalStatus);

        if (businessType != null) {
            hql += " and businessType=:businessType";
            map.put("businessType", businessType);
        }
        if (beginTime != null && endTime != null) {
            hql += " and createDate between :beginTime and :endTime";
            map.put("beginTime", beginTime);
            map.put("endTime", endTime);
        }

        if (!MyStrings.nulltoempty(sort).isEmpty()) {
            hql += " order by handleTime";
            if ("desc".equals(sort)) {
                hql += " desc";
            }
        }
        List<ApprovalProcess> approvalProcesses = approvalProcessDao.getListByHQLWithNamedParams(hql, map);

        return approvalProcesses;
    }

    @Override
    public List<Integer> getBusinessByToUserBusinessType(Integer toUser, Integer businessType) {
        Map<String, Object> map = new HashMap<>();
        String hql = "select business from ApprovalProcess where approveStatus='1' and toUser=:toUser and businessType=:businessType";
        map.put("toUser", toUser);
        map.put("businessType", businessType);
        List<Integer> businessList = approvalProcessDao.getListByHQLWithNamedParams(hql, map);
        return businessList;
    }

    @Override
    public List<ApprovalProcess> getApprovalProcessAll(Integer toUserId, Integer business, Integer orderby, Integer... businessType) {
        Map<String, Object> map = new HashMap<>();
        String hql = "from ApprovalProcess where business=:business";
        map.put("business", business);
        if (toUserId != null) {
            hql += " and toUser =:toUser";
            map.put("toUser", toUserId);
        }
        if (businessType != null) {
            hql += " and businessType in (:businessType)";
            map.put("businessType", businessType);
        }
        if (orderby != null) {
            if (1 == orderby) {
                hql += " order by createDate asc";
            }
        }
        List<ApprovalProcess> approvalProcesses = approvalProcessDao.getListByHQLWithNamedParams(hql, map);
        return approvalProcesses;
    }

    @Override
    public List<ApprovalProcess> getApprovalProcesses(Integer toUserId, Integer business, Integer businessGroup, Integer orderby, Integer... businessType) {
        Map<String, Object> map = new HashMap<>();
        Integer businessType1 = 50;
        String hql = "from ApprovalProcess where business=:business";
        map.put("business", business);
        if (toUserId != null) {
            hql += " and toUser =:toUser";
            map.put("toUser", toUserId);
        }
        if (businessType != null) {
            hql += " and businessType in (:businessType)";
            map.put("businessType", businessType);
            businessType1 = businessType[0];

        }
        if (businessGroup != null) {
            hql += " and (businessGroup is null or businessGroup = :businessGroup)";
            map.put("businessGroup", businessGroup.toString());

            if (businessType1<60) {  //票据的，有差额的情况：刨除可付款新增的那条待处理的审批流程，businessGroup有值那就一定过去可付款了。
                hql += " and id not in (select id from ApprovalProcess where businessType=50 and approveStatus='1')";
            }else if (businessType1>60&&businessType1<70){   //预付款的，有差额的情况：刨除可付款新增的那条待处理的审批流程，businessGroup有值那就一定过去可付款了。
                hql += " and id not in (select id from ApprovalProcess where businessType=64 and approveStatus='1')";
            }else if (businessType1>=70){   //多收来的款的，有差额的情况：刨除可付款新增的那条待处理的审批流程，businessGroup有值那就一定过去可付款了。
                hql += " and id not in (select id from ApprovalProcess where businessType=70 and approveStatus='1')";
            }
        } else {
            hql += " and businessGroup is null";
        }
        if (orderby != null) {
            if (1 == orderby) {
                hql += " order by createDate asc";
            }
        }
        List<ApprovalProcess> approvalProcesses = approvalProcessDao.getListByHQLWithNamedParams(hql, map);
        return approvalProcesses;
    }

    @Override
    public List<ApprovalProcess> getApprovalProcessCounts1(Integer org,Integer toUserId,  String mid, Integer... businessType) {
        String hql = "from ApprovalProcess where org=:org and approveStatus='1'";
        Map<String, Object> map = new HashMap<>();
        map.put("org", org);
        if (toUserId != null) {
            hql += " and toUser=:toUserId";
            map.put("toUserId", toUserId);
        }
        if (businessType != null) {
            hql += " and businessType in(:businessType)";
            map.put("businessType", businessType);
        }
        if (!MyStrings.nulltoempty(mid).isEmpty()) {
            hql += " and toMid=:mid";
            map.put("mid", mid);
        }
//        Long result = (Long) approvalProcessDao.getByHQLWithNamedParams(hql,map);
        return approvalProcessDao.getListByHQLWithNamedParams(hql, map);
    }

    @Override
    public List<ApprovalProcess> getApprovalProcessByGroup(Integer toUser,Integer business, String businessGroup, String approveStatus, Integer... businessType) {
        Map<String, Object> map = new HashMap<>();
        StringBuffer where = new StringBuffer();
        StringBuffer hql = new StringBuffer("from ApprovalProcess");
        if (business != null && StringUtils.isEmpty(businessGroup)) {
            where.append(" and business=:business and businessGroup is null");
            map.put("business", business);
        }
        if (StringUtils.isNotEmpty(businessGroup)) {
            where.append(" and businessGroup=:businessGroup");
            map.put("businessGroup", businessGroup);
        }
        if (businessType != null) {
            where.append(" and businessType in (:businessType)");
            map.put("businessType", businessType);
        }
        if (toUser != null) {
            where.append(" and toUser=:toUser");
            map.put("toUser", toUser);
        }
        if (StringUtils.isNotEmpty(approveStatus)) {
            where.append(" and approveStatus=:approveStatus");
            map.put("approveStatus", approveStatus);
        }
        if (where.length() > 0) {
            hql.append(" where ").append(where.substring(4)).append(" order by createDate desc");
        }
        List<ApprovalProcess> approvalProcesses = approvalProcessDao.getListByHQLWithNamedParams(hql.toString(), map);
        return approvalProcesses;
    }

    @Override
    public List<ApprovalProcess> getApprovalProcessByOidBusinessType(Integer oid, Integer businessType,List<Integer> business) {
        Map<String, Object> map = new HashMap<>();
        String hql = "from ApprovalProcess where org=:oid and businessType=:businessType and approveStatus='1'";
        map.put("oid", oid);
        map.put("businessType", businessType);
        if (business.size()>0){
            hql+=" and business in(:business)";
            map.put("business", business);
        }
        return approvalProcessDao.getListByHQLWithNamedParams(hql, map);
    }
}
