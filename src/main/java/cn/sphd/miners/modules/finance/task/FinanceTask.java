package cn.sphd.miners.modules.finance.task;

import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.modules.finance.entity.FinanceAccount;
import cn.sphd.miners.modules.finance.service.AccountService;
import cn.sphd.miners.modules.system.service.DlmService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by Administrator on 2016-04-01.
 */
@Controller
public class FinanceTask {
    @Autowired
    AccountService accountService;
//    @Autowired
//    AccountantSettleService settleService;
    @Autowired
    DlmService dlmService;
    
    public void settleDay(){//日结转
        //wyu:获取分布式锁
        String lockKey, methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
        if ((lockKey=dlmService.getLock(methodName)) != null) {
            System.out.println("财务开始：" + new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
            Date today = NewDateUtils.today();
//			Date yestoday= NewDateUtils.yesterday();
            //先对往日结转进行整理
            List<FinanceAccount> financeAccounts = accountService.getAllFinanceAccounts();
            for (FinanceAccount f : financeAccounts) {
                //如果当前时间是月初
//                if (today.getTime() == NewDateUtils.changeMonth(new Date(), 0).getTime()) {

                accountService.getAccountPeriodByMonth(f.getId(), today);//查询月结，若没有则补齐

//                }
                accountService.getAccountPeriodByDay(f.getId(), today);//查询最新日结，若没有则补齐
            }
            System.out.println("ThreadID=" + Thread.currentThread().getId());
            System.out.println("财务日结结束：" + new SimpleDateFormat("yyyy/MM/dd-HH:mm:ss:SSS").format(System.currentTimeMillis()));
            //wyu:释放分布式锁
            dlmService.releaseLock(methodName, lockKey);
        }
    }
}
