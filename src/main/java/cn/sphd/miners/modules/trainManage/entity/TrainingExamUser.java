package cn.sphd.miners.modules.trainManage.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2021-02-03 
 */

@Entity ( name ="TrainingExamUser" )
@Table ( name ="t_training_exam_user" )
public class TrainingExamUser  implements Serializable {

	private static final long serialVersionUID =  61114304426802250L;

	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * 机构ID
	 */
   	@Column(name = "org" )
	private Integer org;
	/**
	 * 期数:存年月YYYYMM,增加索引,提高查询统计效率
	 */
	@Column(name = "peroid" )
	private Integer peroid;

	/**
	 * 考核ID
	 */
   	@Column(name = "exam" )
	private Integer exam;

	/**
	 * 用户ID
	 */
   	@Column(name = "user" )
	private Integer user;

	/**
	 * 开始时间
	 */
   	@Column(name = "start_time" )
	private Date startTime;

	/**
	 * 结束时间
	 */
   	@Column(name = "stop_time" )
	private Date stopTime;

	/**
	 * 停止状态:1-正常提交,2-超时终止,3-逾期停止
	 */
   	@Column(name = "stop_state" )
	private Integer stopState;

	/**
	 * 次数:>1为补考
	 */
   	@Column(name = "answer_times" )
	private Integer answerTimes;

	/**
	 * 得分
	 */
   	@Column(name = "score" )
	private Integer score;

	/**
	 * 通过状态:0-未通过,1-1次通过,2-补考通过
	 */
   	@Column(name = "passing_state" )
	private Integer passingState;

	/**
	 * 名次
	 */
   	@Column(name = "ranking" )
	private Integer ranking;

	/**
	 * 备注
	 */
   	@Column(name = "memo" )
	private String memo;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_date" )
	private Date createDate;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_date" )
	private Date updateDate;

	/**
	 * 操作:1-增,2-删,3-改
	 */
   	@Column(name = "operation" )
	private String operation;

	/**
	 * 修改前记录ID
	 */
   	@Column(name = "previous_id" )
	private Integer previousId;

	/**
	 * 版本号,每次修改+1
	 */
   	@Column(name = "version_no" )
	private Integer versionNo;

   	@Transient
	private Date examStopTime;

	public Date getExamStopTime() {
		return examStopTime;
	}

	public void setExamStopTime(Date examStopTime) {
		this.examStopTime = examStopTime;
	}

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOrg() {
		return this.org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getPeroid() {
		return peroid;
	}

	public void setPeroid(Integer peroid) {
		this.peroid = peroid;
	}

	public Integer getExam() {
		return this.exam;
	}

	public void setExam(Integer exam) {
		this.exam = exam;
	}

	public Integer getUser() {
		return this.user;
	}

	public void setUser(Integer user) {
		this.user = user;
	}

	public Date getStartTime() {
		return this.startTime;
	}

	public void setStartTime(Date startTime) {
		this.startTime = startTime;
	}

	public Date getStopTime() {
		return this.stopTime;
	}

	public void setStopTime(Date stopTime) {
		this.stopTime = stopTime;
	}

	public Integer getStopState() {
		return this.stopState;
	}

	public void setStopState(Integer stopState) {
		this.stopState = stopState;
	}

	public Integer getAnswerTimes() {
		return this.answerTimes;
	}

	public void setAnswerTimes(Integer answerTimes) {
		this.answerTimes = answerTimes;
	}

	public Integer getScore() {
		return this.score;
	}

	public void setScore(Integer score) {
		this.score = score;
	}

	public Integer getPassingState() {
		return this.passingState;
	}

	public void setPassingState(Integer passingState) {
		this.passingState = passingState;
	}

	public Integer getRanking() {
		return this.ranking;
	}

	public void setRanking(Integer ranking) {
		this.ranking = ranking;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getCreator() {
		return this.creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return this.createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getUpdator() {
		return this.updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return this.updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return this.updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public String getOperation() {
		return this.operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public Integer getPreviousId() {
		return this.previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return this.versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}

}
