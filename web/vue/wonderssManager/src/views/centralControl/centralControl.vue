<template>
  <Pageheader title="中枢管控" :showBack="true"></Pageheader>
  <div class="container_main">
    <el-row>
      <el-col :span="24">
        尊敬的{{sysInfo.orgName}}公司：
        <div class="ty-right">
          <el-button type="primary" link @click="jump('operationGuide')">更多操作指南</el-button>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24" class="article">欢迎使用wonderss管理系统！</el-col>
    </el-row>
    <el-row>
      <el-col :span="24" class="article">贵公司已于{{sysInfo.createDate?$filter.format(sysInfo.createDate, 'YYYY年M月DD日'):'2017年01月01日'}}获准使用wonderss产品，拥有最高权限的手机号为{{sysInfo.superMobile}}。</el-col>
    </el-row>
    <el-row>
      <el-col :span="24" class="article">使用该号码登录系统后，通过在本页面的操作，可掌控系统的最高权限。</el-col>
    </el-row>
    <el-divider />
    <el-row>
      <el-col :span="24">
        <h2 class="h2_red">总机构</h2>
        <div class="ty-right">
          <el-button type="primary" link @click="coreControl" size="large">核心人物管控</el-button>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">全权负责人  {{sysInfo.smallUserName || '--'}}</el-col>
    </el-row>
    <el-row>
      <el-col :span="24">职工总人数  {{sysInfo.userNumber || 0}}</el-col>
    </el-row>
    <el-row>
      <el-col :span="24">地址  {{sysInfo.address || '--'}}</el-col>
    </el-row>
    <el-divider />
    <el-row>
      <el-col :span="24">
        如在其他地址有分支机构，可点击“增加分支机构”。
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <div class="ty-right">
          <el-button type="primary" link>更多操作指南</el-button>
          <el-button type="primary" link>增加分支机构</el-button>
        </div>
      </el-col>
    </el-row>
  </div>

</template>
<script>
import * as api from "@/api/centralControl";
export default {
  data() {
    return {
      sysInfo: {
        orgName: '',
        today: '',
        week: '',
        superMobile: '',
        createDate: '',
        address: '--',
        userNumber: 0,
        smallUserName: '',
      }
    }
  },
  created() {
    api.getControlInfo()
    .then(res => {
      let data = res.data.data
      let sons = data.sonOrgList, html = ``;
      console.log(data)
      this.sysInfo = data
    })
  },
  methods: {
    jump: function (path, query) {
      this.$router.push({
        path: path
      })
    },
    coreControl: function () {
      let manageState = this.sysInfo.manageState
      switch (manageState) {
        case 0:
          // 0 正式启用前，且没有设置临时管理员
          this.jump('/coreInput/set')
          break;
        case 1:
          // 1- 正式启用前， 设置了临时管理员，但未导入完成 跳转至查看临时管理员页面
          this.jump('/coreRole/set')
          break;
        case 2:
          // 2- 正式启用前， 导入完成 进入高管管理页面 或者 启用前
          this.jump('/coreManage')
          break;
        case 3:
          // 2- 正式启用后
          this.jump('/coreManage')
          break;
        case 4:
          // 4- 批量导入过程中
          this.jump('/coreRole/reset')
          break
      }
    }
  }
}

</script>

<style lang="scss" scoped>
.article{
  text-indent: 26px;
}
.h2_red{
  color: red;
  display: inline;
  font-size: 18px;
  font-weight: normal;
}
.el-row {
  margin-bottom: 16px;
}
.el-row:last-child {
  margin-bottom: 0;
}

</style>
