package cn.sphd.miners.modules.uploads.dto;

import cn.sphd.miners.common.utils.DigestUtils;
import com.alibaba.fastjson.JSON;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.lang.Nullable;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 块上传保存于合并文件类，目前仅kendo chunks 前端测试通过。
 * @date Create at 2020/3/4 09:39
**/
public class ChunkUploadDto {
    final private RedisTemplate redisTemplate;
    final private MultipartFile file;
    final private ResPathDto pathDto;
    final private MetaDataDto metaDto;
    final private String sessionid;
    final private String missCache;
    final private String tempPath;
    public ChunkUploadDto(ResPathDto pathDto, MetaDataDto metaDto, MultipartFile file, String sessionid, RedisTemplate redisTemplate) {
        this.pathDto = pathDto;
        this.metaDto = metaDto;
//        redisTemplate.opsForValue().setIfAbsent("miners:chunkUpload:time:"+metaDto.getUploadUid(), System.currentTimeMillis());
//        if(redisTemplate.opsForValue().get("miners:chunkUpload:time:"+metaDto.getUploadUid())==null) {
//            redisTemplate.opsForValue().set("miners:chunkUpload:time:"+metaDto.getUploadUid(),System.currentTimeMillis(),1,TimeUnit.DAYS);
//        }
        this.file = file;
        this.sessionid = sessionid;
        this.redisTemplate = redisTemplate;
        this.missCache = "miners:chunkUpload:Missing:"+metaDto.getUploadUid();
        //wyu：临时目录不含年月。
        this.tempPath = pathDto.getLoadpath()+pathDto.separator+"caches"+pathDto.separator+metaDto.getUploadUid()+pathDto.separator;
//        //保存临时路径。————wyu：由于kendo上传过程终止不调用remove，此时无法清理临时路径。
//        redisTemplate.opsForValue().set("miners:chunkUpload:tempPath:"+sessionid+":"+metaDto.getFileName(), this.tempPath, 1, TimeUnit.DAYS);
    }

    public ResPathDto getPathDto() {
        return pathDto;
    }

    public MetaDataDto getMetaDto() {
        return metaDto;
    }

    /**
     * <AUTHOR>
     * @description 块保持方法，如果全部完成，合并分块。
     * @date Create at 2020/3/3 13:02
     * @method saveChunk
     * @param
     * @return ResFileDto 未上传完成返回null，上传完成返回ResFileDto。
    **/
    public ResFileDto saveChunk() {
        //错误的chunkIndex值，检查是否已经上传完成了。
        if (metaDto.getChunkIndex() >= metaDto.getTotalChunks()) {
            String result = (String) redisTemplate.opsForValue().get("miners:chunkUpload:uploadFileDto:" + sessionid + ":" + metaDto.getUploadUid());
            if (result != null) {
//                System.out.println("saveChunk redisTemplate:" + result);
//                System.out.println("saveChunk redisTemplate obj:" + JSON.toJSONString(JSON.parseObject(result, ResFileDto.class)));
                return JSON.parseObject(result, ResFileDto.class);
            }
        }
        Long minMissIndex = -1L;
        if (!saveMeta()) {//文件保存或者校验失败
            minMissIndex = (Long) redisTemplate.execute(new SessionCallback<Long>() {
                String key;
                Long minMissIndex;

                @Nullable
                @Override
                public Long execute(RedisOperations operations) throws DataAccessException {
                    Boolean result = operations.opsForValue().setIfAbsent(key, minMissIndex);
                    if (result) {
                        operations.expire(key, 2, TimeUnit.DAYS);
                        return minMissIndex;
                    } else {
                        Long value = (Long) operations.opsForValue().get(key);
                        if (value == null || value.longValue() < 0 || value.longValue() > minMissIndex.longValue()) {
                            //wyu:取值失败或者比当前值大，强行覆盖
                            operations.opsForValue().set(key, minMissIndex, 2, TimeUnit.DAYS);
                        } else {
                            minMissIndex = value;
                        }
                        return minMissIndex;
                    }
                }

                public SessionCallback setParams(String key, Long minMissIndex) {
                    this.key = key;
                    this.minMissIndex = minMissIndex;
                    return this;
                }
            }.setParams(missCache, metaDto.getChunkIndex()));
//            if (minMissIndex == null) {
//                System.out.println("saveChunk checkpoint1 :" + minMissIndex);
//            }
            return new ResFileDto(minMissIndex);
        }

//        System.out.println("saveChunk0, metaDto :" + JSON.toJSONString(metaDto));
//        System.out.println("saveChunk1, (metaDto.getChunkIndex() + 1L >= metaDto.getTotalChunks() :" + (metaDto.getChunkIndex() + 1L >= metaDto.getTotalChunks()));
//        System.out.println("saveChunk2, (minMissIndex = (Long) redisTemplate.opsForValue().get(missCache))) :" + redisTemplate.opsForValue().get(missCache));
//        System.out.println("saveChunk3, (minMissIndex = (Long) redisTemplate.opsForValue().get(missCache)) == null) : " + (redisTemplate.opsForValue().get(missCache) == null));
//        System.out.println("saveChunk3.1, (minMissIndex = (Long) redisTemplate.opsForValue().get(missCache)) : " + redisTemplate.opsForValue().get(missCache));
//        System.out.println("saveChunk3.2 : " + (metaDto.getChunkIndex() + 1L < metaDto.getTotalChunks()//wyu：只有一块或者已经到最后一块，需要执行checkMissing。
//                && redisTemplate.opsForValue().get(missCache) != null));
        if (metaDto.getChunkIndex() + 1L >= metaDto.getTotalChunks()//wyu：只有一块或者已经到最后一块，需要执行checkMissing。
                || redisTemplate.opsForValue().get(missCache) != null) { //wyu：不是最后一块，但已被设置Missing标志（kindochunk不会出现），也需要执行checkMissing。
            if ((minMissIndex = checkMissing()).longValue() == -2L) {//wyu：检查是否遗漏
//                System.out.println("saveChunk checkpoint2 :" + minMissIndex);
                return mergeUpload(minMissIndex);
            }
        }
//        if (minMissIndex == null) {
//            System.out.println("saveChunk4, (metaDto.getChunkIndex() + 1L >= metaDto.getTotalChunks() :" + (metaDto.getChunkIndex() + 1L >= metaDto.getTotalChunks()));
//            System.out.println("saveChunk5, (minMissIndex = (Long) redisTemplate.opsForValue().get(missCache))) :" + redisTemplate.opsForValue().get(missCache));
//            System.out.println("saveChunk6, (minMissIndex = (Long) redisTemplate.opsForValue().get(missCache)) != null) :" + (redisTemplate.opsForValue().get(missCache) != null));
//            System.out.println("saveChunk checkpoint3 :" + minMissIndex);
//        }
        return new ResFileDto(minMissIndex);
    }

    /**
     * <AUTHOR>
     * @description 保存单块文件
     * @date Create at 2020/3/3 18:44
     * @method saveMeta
     * @param
     * @return void
    **/
    private boolean saveMeta() {
        boolean result = false;
        File dir = new File(tempPath);
        if(!dir.exists()) {
            dir.mkdirs();
        }
        File destination = new File(tempPath+metaDto.getChunkIndex());
        try {
            FileUtils.copyInputStreamToFile(file.getInputStream(), destination);//如果扩展名属于允许上传的类型，则创建文件
        } catch (IOException e) {
            System.out.println("断点续传上传文件报错："+ tempPath+metaDto.getChunkIndex());
            Logger.getLogger(getClass()).error("文件断点上传片段保存错误", e);
            return result;
        }
        if(destination.exists()) {
            if(StringUtils.isNotBlank(metaDto.getHash())) {
                result = metaDto.getHash().equalsIgnoreCase(DigestUtils.digest(destination, DigestUtils.MD5));//返回MD5校验结果
                if(!result) {//校验异常，删除已上传文件
                    destination.delete();
                    System.out.println("断点续传上传文件校验错误 metaDto.getHash() : " + metaDto.getHash() + " DigestUtils.digest : " + DigestUtils.digest(destination, DigestUtils.MD5));
                }
            } else {
                result = true;//metaDto.getHash()为空，无需校验
            }
        }
        return result;
    }

    /**
     * <AUTHOR>
     * @description 检查临时文件夹是否存在遗留文件
     * @date Create at 2020/3/3 13:00
     * @method checkMissing
     * @param  
     * @return boolean true：完整；false：不完整。
    **/
    private long checkMissing() {
        System.out.println("checkMissing in index : " + metaDto.getChunkIndex());
        long i, count = metaDto.getTotalChunks();
        for(i = 0; i < count; i++) {
            if(!new File(tempPath+i).exists()) {
                break;
            }
        }
        if(i>=count) {
            redisTemplate.delete(missCache); //wyu：清除Missing标志。
            System.out.println("checkMissing -2L "+i + " : "+ count);
            return -2L;
        } else {
            redisTemplate.opsForValue().set(missCache, i,2, TimeUnit.DAYS); //wyu：设置Missing标志。
            System.out.println("checkMissing i : "+i + " : "+ count);
            return i;
        }
    }
    /**
     * <AUTHOR>
     * @description 合并文件
     * @date Create at 2020/3/3 17:22
     * @method mergeUpload
     * @param
     * @return ResFileDto 上传的新文件名
    **/
    private ResFileDto mergeUpload(Long minMissIndex) {
//        Long start = (Long) redisTemplate.opsForValue().get("miners:chunkUpload:time:"+metaDto.getUploadUid());
//        Long now=System.currentTimeMillis();
//        if(start!=null) {
//            System.out.println("上传时间："+ TimeUtils.toTimeString(now-start)+" 文件大小："+metaDto.getTotalFileSize()+" 文件名称："+metaDto.getFileName());
//        }
        System.out.println("mergeUpload metaDto: "+JSON.toJSONString(metaDto));
        ResFileDto fileDto = new ResFileDto(metaDto.getFileName(),metaDto.getRelativePath(), minMissIndex);
        System.out.println("mergeUpload fileDto: "+JSON.toJSONString(fileDto));
        pathDto.mkdir();
        String filePath = pathDto.getFullPath()+fileDto.getFileName();
        try {
            FileOutputStream fos = new FileOutputStream(filePath);
            long i, count = metaDto.getTotalChunks();
            for(i = 0; i < count; i++) {
                FileUtils.copyFile(new File(tempPath + i), fos);
            }
            fos.close();
            FileUtils.deleteDirectory(new File(tempPath));
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        redisTemplate.opsForValue().set("miners:chunkUpload:uploadPathByUploadUid:"+sessionid+":"+metaDto.getUploadUid(), filePath, 3, TimeUnit.DAYS);
        redisTemplate.opsForValue().set("miners:chunkUpload:uploadPathByRelativePath:"+sessionid+":"+metaDto.getRelativePath(), filePath, 3, TimeUnit.DAYS);
        redisTemplate.opsForValue().set("miners:chunkUpload:uploadFileDto:"+sessionid+":"+metaDto.getUploadUid(), JSON.toJSONString(fileDto), 3, TimeUnit.DAYS);
//        //wyu：延长无操作注销用户时间
//        redisTemplate.opsForValue().set("miners:logout:" + sessionid, System.currentTimeMillis(), 35, TimeUnit.MINUTES);
//        Long start = (Long) redisTemplate.opsForValue().get("miners:chunkUpload:time:"+metaDto.getUploadUid());
//        if(now!=null) {
//            System.out.println("合并时间："+ TimeUtils.toTimeString(System.currentTimeMillis()-now)+" 文件大小："+metaDto.getTotalFileSize()+" 文件名称："+metaDto.getFileName()+" 文件路径："+filePath);
//        }
        return fileDto;
    }

    public static String removeUpload(String sessionid,String fileNames,RedisTemplate redisTemplate) {
//        //删除临时路径。————wyu：由于kendo上传过程终止不调用remove，此时无法清理临时路径。
//        String tempPath= (String) redisTemplate.opsForValue().get("miners:chunkUpload:tempPath:"+sessionid+":"+fileNames);
//        File file;
//        if(StringUtils.isNotEmpty(tempPath)
//                && (file=new File(tempPath)).isDirectory()) {
//            try {
//                FileUtils.deleteDirectory(file);
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
        //删除已上传文件。
        String uploadPath;
        int index;
        if((index = fileNames.indexOf('\0'))>0) {//wyu：当包含'\0'时，取后面的文件UploadUid，避免重名文件影响。
            fileNames = fileNames.substring(index+1);
            uploadPath = (String) redisTemplate.opsForValue().get("miners:chunkUpload:uploadPathByUploadUid:" + sessionid + ":" + fileNames);
        } else {
            uploadPath = (String) redisTemplate.opsForValue().get("miners:chunkUpload:uploadPathByRelativePath:" + sessionid + ":" + fileNames);
        }
        System.out.println("uploadPath: "+uploadPath);
        if (StringUtils.isNotEmpty(uploadPath)) {
            new File(uploadPath).delete();
        }
        return uploadPath;
    }
}
