package cn.sphd.miners.modules.finance.controller;

import cn.sphd.miners.common.persistence.AnnotationLimit;
import cn.sphd.miners.common.utils.DateUtils;
import cn.sphd.miners.modules.finance.entity.AccountPeriod;
import cn.sphd.miners.modules.finance.entity.FinanceAccount;
import cn.sphd.miners.modules.finance.service.AccountService;
import cn.sphd.miners.modules.finance.service.FinanceAccountService;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import java.math.BigDecimal;
import java.util.*;

/**
 * Created by 赵应 on 2015-12-01.
 */
@Controller
@RequestMapping("/finance")
public class FinanceController {
    @Autowired
    OrgService orgService;
    @Autowired
    AccountService accountService;
    @Autowired
    FinanceAccountService financeAccountService;


    @AnnotationLimit(mid = "ld",pid = "la")
    @RequestMapping("/financeManage.do")
    public String financeManage(){
        return "/finance/financeManage";
    }

    @AnnotationLimit(mid = "lh",pid = "la")
    @RequestMapping("/initCashFinance.do")
    public String initCashFinance(User user){
        Organization o = user.getOrganization();
        FinanceAccount financeAccount =accountService.getFinanceAccountByOidAndType(o.getId(),1);
        if(financeAccount==null){//无现金账户初始化
            FinanceAccount f = new FinanceAccount();
            f.setOrg(o);
            f.setPreviousBalance(BigDecimal.valueOf(0));
            f.setBalance(BigDecimal.valueOf(0));
            f.setAccountType(1);
            f.setApproveStatus("1");
            f.setCreateDate(new Date());
            f.setCreateName("系统初始化");
            f.setDebit(BigDecimal.valueOf(0));
            f.setCredit(BigDecimal.valueOf(0));
            f.setAccountStatus(1);
            f.setName("现金/备用金");
            f.setBankName("现金/备用金");
            f.setCashable(1);//可取现
            accountService.addAccount(f);

            AccountPeriod ap = new AccountPeriod();
            ap.setFid(f.getId());
            ap.setAccount(String.valueOf(f.getId()));
            ap.setPreviousBalance(BigDecimal.valueOf(0));
            ap.setCredit(BigDecimal.valueOf(0));
            ap.setDebit(BigDecimal.valueOf(0));
            ap.setOrg(o);
            ap.setBalance(BigDecimal.valueOf(0));
            ap.setBeginDate(new Date());
            ap.setAccountType(1);
            ap.setBuildState("未生成");
            ap.setPeriodType(2);
//            ap.setAccountId(f);
            ap.setAccountId(f.getId());

            accountService.addPeroid(ap);


            AccountPeriod apyue = new AccountPeriod();
            apyue.setFid(f.getId());
            apyue.setAccount(String.valueOf(f.getId()));
            apyue.setPreviousBalance(f.getPreviousBalance());
            apyue.setCredit(f.getBalance()==null? new BigDecimal(0):f.getBalance());
            apyue.setDebit(BigDecimal.valueOf(0));
            apyue.setOrg(o);
            apyue.setBalance(f.getBalance());
            apyue.setBeginDate(DateUtils.thisMonthFirstDate());
            apyue.setEndDate(DateUtils.monthEnd(DateUtils.thisMonthFirstDate()));//下月的第一天0点作为结束时间

            apyue.setAccountType(1);
            apyue.setBuildState("未生成");
            apyue.setPeriodType(1);
            apyue.setCreateDate(new Date());
//            apyue.setAccountId(f);
            apyue.setAccountId(f.getId());


            accountService.addPeroid(apyue);
        }else {
            return "redirect:/finance/financeManage.do";
        }
        return "redirect:/finance/financeManage.do";
    }
}
