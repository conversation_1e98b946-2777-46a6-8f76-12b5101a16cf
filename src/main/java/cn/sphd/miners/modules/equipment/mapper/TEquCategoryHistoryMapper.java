package cn.sphd.miners.modules.equipment.mapper;

import cn.sphd.miners.modules.equipment.entity.TEquCategoryHistory;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 装备管理_分类历史
 * 20230209 1.247装备器具新增Mapper接口
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Repository
public interface TEquCategoryHistoryMapper {
    /**
     * 查询装备管理_分类历史
     * 20230209 1.247装备器具新增
     *
     * @param id 装备管理_分类历史
     *           20230209 1.247装备器具新增主键
     * @return 装备管理_分类历史
     * 20230209 1.247装备器具新增
     */
    public TEquCategoryHistory selectTEquCategoryHistoryById(Long id);

    /**
     * 查询装备管理_分类历史
     * 20230209 1.247装备器具新增列表
     *
     * @param tEquCategoryHistory 装备管理_分类历史
     *                            20230209 1.247装备器具新增
     * @return 装备管理_分类历史
     * 20230209 1.247装备器具新增集合
     */
    public List<TEquCategoryHistory> selectTEquCategoryHistoryList(TEquCategoryHistory tEquCategoryHistory);

    /**
     * 新增装备管理_分类历史
     * 20230209 1.247装备器具新增
     *
     * @param tEquCategoryHistory 装备管理_分类历史
     *                            20230209 1.247装备器具新增
     * @return 结果
     */
    public int insertTEquCategoryHistory(TEquCategoryHistory tEquCategoryHistory);

    /**
     * 修改装备管理_分类历史
     * 20230209 1.247装备器具新增
     *
     * @param tEquCategoryHistory 装备管理_分类历史
     *                            20230209 1.247装备器具新增
     * @return 结果
     */
    public int updateTEquCategoryHistory(TEquCategoryHistory tEquCategoryHistory);

    /**
     * 删除装备管理_分类历史
     * 20230209 1.247装备器具新增
     *
     * @param id 装备管理_分类历史
     *           20230209 1.247装备器具新增主键
     * @return 结果
     */
    public int deleteTEquCategoryHistoryById(Long id);

    /**
     * 批量删除装备管理_分类历史
     * 20230209 1.247装备器具新增
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTEquCategoryHistoryByIds(String[] ids);
}
