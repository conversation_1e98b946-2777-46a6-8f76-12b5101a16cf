<template>
  <div id="saleInit">
    <div v-if="saleNum === 1">
      <div>
        <div>
          <el-row class="line">
            <el-col :span="15">欢迎对“销售1”模块进行初始化！</el-col>
            <el-col :span="9">
              <div class="txtRight">
                <span class='fa fa-blue gapRt' :class="{'fa-dot-circle-o': initFinishState, ' fa-circle-o': !initFinishState}" @click="checkNextStep"></span>
                <span class="gapRL">“必须编辑”的项均已编辑完，“销售1”模块初始化完成！</span>
                <el-button class="ty-right" type="primary" :disabled="nextPattern" @click="nextStep(1)">确 定</el-button>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="ty-hr"></div>
      </div>
      <div class="btCon">
        <el-row class="rowGap">
          <el-col :span="24">请完成以下各项中“必须编辑”的项，之后勾选右上方的选项并点击“确定”！</el-col>
        </el-row>
        <table class="ty-table ty-table-control">
          <tbody>
          <tr>
            <td>需编辑的项</td>
            <td>是否必须编辑</td>
            <td>状态</td>
            <td>操作</td>
          </tr>
          <tr>
            <td>客户清单</td>
            <td></td>
            <td></td>
            <td>
              <span class='ty-color-blue' @click="editCustomersList()">编辑</span>
            </td>
          </tr>
          <tr>
            <td>通用型商品清单</td>
            <td></td>
            <td>通用型商品清单</td>
            <td>
              <span class='ty-color-blue' @click="editUniversalPt()">编辑</span>
            </td>
          </tr>
          <tr v-for="(item, index) in indexInitMenu" v-bind:key="index">
            <td>{{ categoryCharge(item["category"]) }}</td>
            <td>{{ (item["hasAttachment"] ? "有" : "无") }}</td>
            <td>{{ (item["linesLimited"]||"--") }}</td>
            <td class="ty-td-control">
              <span class='ty-color-blue' @click="editUniversalPt(item)">编辑</span>
            </td>
          </tr>
          </tbody>
        </table>
      </div>

    </div>
    <div v-if="saleNum === 2">
      <div class="rowGap">
        <el-row class="line">
          <el-col :span="16">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="saleNum = 1">返 回</span>
          </el-col>
          <el-col :span="8">
            <div class="txtRight">
              <span class='fa gapRt fa-blue' :class="{'fa-circle': cusFinishState, ' fa-circle-o': !cusFinishState}" @click="cusFinish"></span>
              <span class="gapRtL">客户已全部录完！</span>
              <el-button class="ty-right" type="primary" @click="nextStep(2)">确 定</el-button>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="ty-alert">
        <el-input placeholder="客户代号/客户名称" v-model="cusMainData.searchVal" class="searchTxt">
          <template #append>
            <span @click="searchCustomer()"> 搜 索</span>
          </template>
        </el-input>
        <div class="btn-group">
          <span class="panel_4 ty-btn ty-btn-big ty-btn-blue ty-circle-3 gapRt" id="customerImport" @click="uploadSalerBtn()">批量导入</span>
          <span class="panel_4 ty-btn ty-btn-big ty-btn-blue ty-circle-3" id="addCus" @click="addSalerBtn()">新增客户</span>
        </div>
      </div>
      <el-table
          :data="tableData"
          style="width: 100%">
        <el-table-column
            prop="date"
            label="序号"
            width="180">
        </el-table-column>
        <el-table-column
            prop="name"
            label="客户代号"
            width="180">
        </el-table-column>
        <el-table-column
            prop="name"
            label="客户名称"
            width="180">
        </el-table-column>
        <el-table-column
            prop="name"
            label="创建者"
            width="180">
        </el-table-column>
        <el-table-column
            prop="address"
            label="操作">
        </el-table-column>
      </el-table>
    </div>
    <div v-if="saleNum === 3" class="narrPage">
      <span class="backGap ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="saleNum = 1">返 回</span>
      <div class="rowGap">
        <el-row class="line">
          <el-col :span="8">请录入通用型商品！</el-col>
          <el-col :span="16" class="txtRight">
            <el-button type="primary" @click="newSalesCommodity()">新  增</el-button>
            <el-button type="primary" @click="goodsAddNoticeBtn()">操作说明</el-button>
          </el-col>
        </el-row>
        <div class="ty-hr"></div>
      </div>
      <div class="gapFar">
        <span class='fa fa-blue gapRt' :class="{'fa-dot-circle-o': nextPattern, ' fa-circle-o': !nextPattern}" @click="checkNextStep"></span>
        <span class="gapRL">本公司没有需录入到系统的通用型商品！</span>
        <el-button class="ty-right" type="primary" :disabled="nextPattern" @click="nextStep(1)">确 定</el-button>
      </div>
    </div>
    <div v-show="saleNum === 4">
      <div class="rowGap">
        <el-row class="line">
          <el-col :span="14">
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="saleNum = 1">返 回</span>
          </el-col>
          <el-col :span="10">
            <div class="txtRight">
              <span class='fa gapRt fa-blue' :class="{'fa-circle': finishState, ' fa-circle-o': !finishState}" @click="accountsFinish"></span>
              <span class="gapRL">“通用型商品”已全部录入！</span>
              <el-button class="ty-right" type="primary" @click="nextStep(2)">确 定</el-button>
            </div>
          </el-col>
        </el-row>
      </div>
      <div id="mtInfo">
        <div class="bigContainer" ref="bigContainerRef">
          <div class="left_container">
            <div class="indexInput">
              <div class="Btop"><span>录入商品</span></div>
              <ul class="faceul Left-label">
                <li class="faceul1" :class="{'disabled': indexInputState}">
                  <a><button @click="newSalesCommodity(1,2)" :disabled="indexInputState">录入已销售过的商品</button></a>
                </li>
                <li class="faceul1">
                  <a><button @click="newSalesCommodity(0,2)" :disabled="indexInputState">录入未销售过的商品</button></a>
                </li>
              </ul>
            </div>
            <div>
              <div class="Btop" id="firstLevel"><span id="firstLevelName">全部</span>(<span id="firstLevelAmount">{{firstLevelAmount}}</span>种)</div>
              <form>
                <ul class="faceul bottomTree" id="kindsTree">
                  <li class="faceul1" v-for="(item, index) in kindsTree" v-bind:key="index">
                    <a>
                      <span @click="kindBtn(item)" data-source="1">{{ item.name +'（'+ item.num +'种）'}}</span>
                    </a>
                    </li>
                </ul>
              </form>
            </div>
          </div>
          <div class="between" :style="{'height': 100}"><div class="between2"></div></div>
          <div class="right_container">
            <div class="Right-label" id="right_container">
              <div id="inSales">
                <div class="container_nav">
                  <div class="conon">
                    <div class="dq">
                      <span>当前分类</span> <span>：</span>
                      <span id="curID">
                        <span @click="showkindNav()">全部</span>
                        <span @click="showkindNav(item)" v-for="(item, index) in currNavList" v-bind:key="index">
                          {{item.fullName}}
                        </span>
                      </span>
                    </div>
                  </div>
                  <div class="ty-right searchSect">
                    <div class="ty-left keywordSearch">
                      查找商品
                      <input placeholder="请输入商品代号或名称" v-model="searchKeyBase" />
                    </div>
                    <span class="ty-left ty-btn ty-btn-blue" @click="getUniversalPtList()">确 定</span>
                  </div>
                </div>
                <div class="">
                  <div class="inSales inSalesList">
                    <table class="ty-table ty-table-none bg-yellow" id="classifiedGoods">
                      <thead>
                      <tr>
                      <td>商品代号</td>
                      <td>商品名称</td>
                      <td>型号</td>
                      <td>规格</td>
                      <td>计量单位</td>
                      <td>创建</td>
                      <td>操作</td>
                      </tr>
                      </thead>
                      <tbody>
                      <tr v-for="(item, index) in inSalesPtList" v-bind:key="index">
                        <td>{{handleNull(list[t].outerSn)}}</td>
                        <td>{{handleNull(list[t].outerName)}}</td>
                        <td>{{handleNull(list[t].model)}}</td>
                        <td>{{ handleNull(list[t].specifications)}}</td>
                        <td>{{ handleNull(list[t].unit)}}</td>
                        <td>{{ parseFloat(Number(list[t].minimumStock || "0").toFixed(4))}}</td>
                        <td>{{ parseFloat(Number(list[t].currentStock || "0").toFixed(4))}}</td>
                        <td class="createInfo">{{ list[t].createName}} {{ new Date(list[t].createDate).format('yyyy/MM/dd hh:mm:ss') }}</td>
                        <td>
                          <span class="ty-color-blue tb-btn-sm" @click="seeCommodityDetails($(this),1, false, true)">查看</span>
                          <span class="ty-color-red tb-btn-sm" @click="deleteCommodity($(this),0)">删除</span>
                        </td>
                      </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
<!--                <TyPage v-if="inSales.pageShow"
                        :curPage="inSales.pageInfo.currentPageNo" :pageSize="inSales.pageInfo.pageSize"
                        :allPage="inSales.pageInfo.totalPage" :pageClickFun="inSales"></TyPage>-->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="saleNum === 41">
      <div class="narrowLamp bg-yellow" id="importEnteryType">
        <div class="importCon1">
          <div class="importNoSave stepItem">
            <span class="ty-btn ty-btn-yellow ty-btn-big btn" data-name="clearNoSave" style="margin-right: 250px;">放 弃</span>
            <span class="ty-btn ty-btn-blue ty-btn-big btn" data-name="stepNext">下一步</span>
          </div>
          <p>您共导入专属商品<span class="initAll"></span>条，其中以下<span class="initWrong"></span>条存在问题，<span class="ty-color-red">无法保存至系统</span>。</p>
          <p>名称或代号未录入，本次导入商品的代号互相重复或与系统中已有代号相同等情况，计量单位未填写或计量单位被停用。单价未填写均算作问题。</p>
          <div class="gap-Tp">
            <div id="tureMtList" style="display: none;"></div>
            <table class="ty-table ty-table-control">
              <thead>
              <td>商品名称</td>
              <td>商品代号</td>
              <td>型号</td>
              <td>规格</td>
              <td>计量单位</td>
              <td>最低库存</td>
              <td class="hasRatePrice">含税单价</td>
              <td>商品说明</td>
              <td>操作</td>
              </thead>
              <tbody>
              </tbody>
            </table>
          </div>
        </div>
        <div class="importCon2">
          <div class="importing stepItem">
            <span class="ty-btn ty-btn-yellow ty-btn-big ty-circle-3 btn" data-name="cancelSave" style="margin-right: 250px;">放 弃</span>
            <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-3 btn" id="save">保 存</span>
          </div>
          <p>您共导入商品<span class="initAll"></span>条，可保存至系统的共<span class="inabledSum"></span>条。</p>
          <table class="ty-table ty-table-control normal1">
            <thead>
            <tr>
              <td width="30%">商品</td>
              <td>最低库存</td>
              <td class="hasRatePrice">含税单价</td>
              <td>商品说明</td>
              <td width="10%" rowspan="2">操作</td>
            </tr>
            </thead>
            <tbody>
            </tbody>
          </table>
          <table class="ty-table ty-table-control normal2">
            <thead>
            <tr>
              <td width="30%" rowspan="2">商品</td>
              <td width="10%" rowspan="2" class="po-category">该商品需开何种发票</td>
              <td width="30%" colspan="3" class="tdSpecial po-invoice1">增值税专用发票</td>
              <td width="10%" rowspan="2" class="po-invoice2">开普通发票时的开票单价</td>
              <td width="10%" rowspan="2" class="po-invoice4">不开发票时的单价</td>
              <td width="10%" rowspan="2">操作</td>
            </tr>
            <tr class="po-invoice1">
              <td>税率</td>
              <td>含税价</td>
              <td>不含税价</td>
            </tr>
            </thead>
            <tbody>
            </tbody>
          </table>
        </div>
      </div>
      <table class="ty-table ty-table-control">
        <tbody>
        <tr>
          <td> 账户名称</td>
          <td> 开户行 </td>
          <td> 账号 </td>
          <td> 账户类型 </td>
          <td> 当前余额 </td>
          <td> 当前状态 </td>
          <td width="220px"> 操作 </td>
        </tr>
        <tr v-for="(item, index) in accountsList" v-bind:key="index">
          <td>{{ item.name}}</td>
          <td>{{ item.accountType === 1?'--': item.bankName }}</td>
          <td>{{ item.accountType === 1?'--': item.account }}</td>
          <td>{{ item.accountType === 1?'--': item.operation}}</td>
          <td>{{ (item.balance || 0).toFixed(2)}}</td>
          <td>{{ item.accountType === 1?'--': scanState(item.accountStatus)}}</td>
          <td class="ty-td-control txt-left">
            <el-button type="primary" :disabled="item.accountType === 1 && buildState == 4" @click="editAccount(item)" plain>修改</el-button>
            <el-button type="danger" v-if="item.accountType !== 1" @click="delAccount(item)" plain>删除</el-button>
            <!--            <span class='ty-color-blue' @click="editAccount(item)">修改</span>-->
            <!--            <span v-if="item.accountType !== 1" class='ty-color-blue gapLt' @click="delAccount(item)">删除</span>-->
          </td>
        </tr>
        </tbody>
      </table>
    </div>
    <div v-if="saleNum === 5">
      <el-row>
        <el-col :span="14">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainNum = 2">返 回</span>
        </el-col>
        <el-col :span="10">
          <div class="txtRight">
            <span class="gapRL">已处理XX个客户</span>
            <el-link type="primary">查看</el-link>
          </div>
        </el-col>
      </el-row>
      <div class="rowGap">XXXX-XX-XX XX:XX:XX之后，“客户清单”中新增了XXX个客户。本公司是否向这些客户供应专属商品？请处理！</div>
      <el-table
          :data="tableData"
          style="width: 100%">
        <el-table-column
            prop="date"
            label="客户名称"
            width="180">
        </el-table-column>
        <el-table-column
            prop="name"
            label="客户简称"
            width="180">
        </el-table-column>
        <el-table-column
            prop="address"
            label="地址">
        </el-table-column>
        <el-table-column
            prop="address"
            label="地址">
        </el-table-column>
        <el-table-column
            prop="address"
            label="是否供应专属商品">
        </el-table-column>
      </el-table>
    </div>
    <div v-if="saleNum === 6">
      <el-row>
        <el-col :span="14">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainNum = 2">返 回</span>
        </el-col>
        <el-col :span="10">
          <div class="txtRight">
            <span class="gapRL">已处理XX个客户</span>
            <el-link type="primary">查看</el-link>
          </div>
        </el-col>
      </el-row>
      <div class="rowGap">XXXX-XX-XX XX:XX:XX之后，“客户清单”中有XX个客户的基本信息有变动。本公司向这些客户供应的专属商品情况有无变化？请处理！</div>
      <el-table
          :data="tableData"
          style="width: 100%">
        <el-table-column
            prop="date"
            label="客户名称"
            width="180">
        </el-table-column>
        <el-table-column
            prop="name"
            label="修改状态"
            width="180">
        </el-table-column>
        <el-table-column
            prop="address"
            label="客户名称/简称/代号">
        </el-table-column>
        <el-table-column
            prop="address"
            label="更多信息">
        </el-table-column>
        <el-table-column
            prop="address"
            label="创建/修改">
        </el-table-column>
        <el-table-column
            prop="address"
            label="所供应的专属商品">
        </el-table-column>
        <el-table-column
            prop="address"
            label="处理状态">
        </el-table-column>
        <el-table-column
            prop="address"
            label="所供应的专属商品是否有变化">
        </el-table-column>
      </el-table>
    </div>
    <div v-if="saleNum === 7">
      <el-row>
        <el-col :span="24">
          <span class="ty-btn ty-btn-blue ty-btn-big ty-circle-5" @click="mainNum = 2">返 回</span>
        </el-col>
      </el-row>
      <div class="rowGap">XXXX-XX-XX XX:XX:XX之后，“客户清单”中新增的XXX个客户中，已处理完毕的共如下XX个：</div>
      <el-table
          :data="tableData"
          style="width: 100%">
        <el-table-column
            prop="date"
            label="客户名称"
            width="180">
        </el-table-column>
        <el-table-column
            prop="name"
            label="修改状态"
            width="180">
        </el-table-column>
        <el-table-column
            prop="address"
            label="客户名称/简称/代号">
        </el-table-column>
        <el-table-column
            prop="address"
            label="更多信息">
        </el-table-column>
        <el-table-column
            prop="address"
            label="创建/修改">
        </el-table-column>
        <el-table-column
            prop="address"
            label="所供应的专属商品">
        </el-table-column>
        <el-table-column
            prop="address"
            label="处理状态">
        </el-table-column>
        <el-table-column
            prop="address"
            label="所供应的专属商品是否有变化">
        </el-table-column>
      </el-table>
    </div>

    <!-- 新增客户-->
    <TyDialog class="custormerAdd" v-show="addData.visible" width="1060" dialogTitle="新增客户" color="blue" :dialogHide="hideAddFun">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideAddFun">取消</span>
        <span v-if="!specialOrgPart" class="ty-btn ty-btn-big ty-circle-5" :class="addDataOkBtn ? 'bounce-ok' : 'bounce-cancel'" @click="addDataOk">提交</span>
        <span v-if="specialOrgPart" class="ty-btn ty-btn-big ty-circle-5" :class="addDataOkBtn ? 'bounce-ok' : 'bounce-cancel'" @click="addDataOkSpecial(0)">录入完毕，暂不选择产品</span>
        <span v-if="specialOrgPart" class="ty-btn ty-btn-big ty-circle-5" :class="addDataOkBtn ? 'bounce-ok' : 'bounce-cancel'" @click="addDataOkSpecial(1)">录入完毕，选择产品</span>
      </template>
      <template #dialogBody>
        <div class="custormerAddCC" style="max-height: 200px;">
          <table>
            <tbody>
            <tr>
              <td colspan="6" class="ttl">基本信息</td>
            </tr>
            <tr>
              <td class="txtRight" width="120px;"><i class="ty-color-red">*</i>客户名称：</td>
              <td colspan="3"><el-input v-model="addData.base.fullName" placeholder="请输入" @change="setName2"></el-input></td>
              <td class="txtRight">客户代号：</td>
              <td><el-input v-model="addData.base.cuscoding" placeholder="请输入"></el-input></td>
            </tr>
            <tr>
              <td class="txtRight">地址：</td>
              <td colspan="3"><el-input v-model="addData.base.address1" placeholder="请输入"></el-input></td>
              <td class="txtRight">客户简称：</td>
              <td><el-input v-model="addData.base.name" placeholder="请输入"></el-input></td>
            </tr>
            <tr v-if="specialOrgPart">
              <td class="txtRight" width="120px;"><i class="ty-color-red">*</i>最高负责人：</td>
              <td colspan="2"><el-input v-model="addData.base.supervisorName" placeholder="请输入"></el-input></td>
              <td colspan="3" style="padding-left: 80px; position: relative; ">
                <span style="position: absolute; left:30px;"><i class="ty-color-red">*</i>手机：</span>
                <el-input v-model="addData.base.supervisorMobile" placeholder="请输入"></el-input>
              </td>
            </tr>
            <tr>
              <td colspan="6">
                <span class="marL30"><i class="ty-color-red">*</i>成为公司客户的时间？</span>
                <span class="marL30">
                      <span class="radiop" @click="toggefirstTime(1)"><i class="fa" :class="this.addData.base.firstTime === 1? 'fa-dot-circle-o':'fa-circle-o'"></i>今年</span>
                      <span v-show="addData.base.firstTime === 1">
                         <el-date-picker v-model="addData.base.initialPeriod" type="month" placeholder="选择月" @change="setFirstTimeN"
                                         style="width: 100px" format="M月" value-format="YYYYMM" :disabled-date="disabledDate"></el-date-picker>
                          <span class="radiop" @click="toggefirstTimeN"><i class="fa" :class="this.addData.base.firstTimeN? 'fa-dot-circle-o':'fa-circle-o'"></i>不确定月份</span>
                      </span>
                      <span class="radiop" @click="toggefirstTime(2)"><i class="fa" :class="this.addData.base.firstTime === 2? 'fa-dot-circle-o':'fa-circle-o'"></i>去年</span>
                      <span class="radiop" @click="toggefirstTime(3)"><i class="fa" :class="this.addData.base.firstTime === 3? 'fa-dot-circle-o':'fa-circle-o'"></i>更久之前</span>
                    </span>
              </td>
            </tr>
            <tr>
              <td class="txtRight">全景照片：</td>
              <td colspan="5">
                <span></span>
                <div class="pa20" >
                  <uploadFile ref="uploadFile2" class="ty-right"
                              module="客户管理"
                              committed.async="false"
                              :showSelect="false"
                              :showFileList="false"
                              :autoUpload="true"
                              ext='.png,.jpg,.jpeg,'
                              extMsg='请上传照片！'
                              :multiple="true"
                              :beforeUploadFun="beforeUpload1"
                              :successFun="fileSuccess1">
                    <template #btnArea>
                      <span class="ty-btn ty-btn-blue " @click="uploadFile1">上传</span>
                    </template>
                  </uploadFile>
                </div>
                <span>(共可上传9张)</span>
              </td>
            </tr>
            <tr v-if="addData.base.quanImg.length > 0">
              <td></td>
              <td colspan="5">
                <div class="imgList">
                      <span class="imgI" v-for="(img, indexC2) in addData.base.quanImg" :key="indexC2">
                        <el-image class="img" :src="img.src" fit="cover" :preview-src-list="[img.src]"></el-image>
                        <span class="delBtn ty-color-red" @click="delImg(indexC2, 'quanImg')"><i class="fa fa-close"></i></span>
                      </span>
                </div>
              </td>
            </tr>
            <tr>
              <td class="txtRight">产品图片：</td>
              <td colspan="5">
                <div class="pa20" >
                  <uploadFile ref="uploadFile3" class="ty-right"
                              module="客户管理"
                              committed.async="false"
                              :showSelect="false"
                              :showFileList="false"
                              :autoUpload="true"
                              ext='.png,.jpg'
                              extMsg='请上传照片！'
                              :multiple="true"
                              :beforeUploadFun="beforeUpload2"
                              :successFun="fileSuccess2">
                    <template #btnArea>
                      <span class="ty-btn ty-btn-blue " @click="uploadFile2">上传</span>
                    </template>
                  </uploadFile>
                </div>
                <span>(共可上传9张)</span>
              </td>
            </tr>
            <tr v-if="addData.base.chanImg.length > 0">
              <td></td>
              <td colspan="5">
                <div class="imgList">
                      <span class="imgI" v-for="(img, indexC2) in addData.base.chanImg" :key="indexC2">
                        <el-image class="img" :src="img.src" fit="cover" :preview-src-list="[img.src]"></el-image>
                        <span class="delBtn ty-color-red" @click="delImg(indexC2, 'chanImg')"><i class="fa fa-close"></i></span>
                      </span>
                </div>
              </td>
            </tr>
            <tr>
              <td class="txtRight">首次接触时间：</td>
              <td>

                <el-date-picker v-model="addData.base.firstContactTime" type="date" placeholder="选择日期">
                </el-date-picker>
              </td>
              <td class="txtRight" width="120px">首次接触地点：</td>
              <td><el-input v-model="addData.base.firstContactAddress" format="YYYY/MM/DD" value-format="YYYY/MM/DD" placeholder="请输入"></el-input></td>
              <td class="txtRight" width="120px">信息获取渠道：</td>
              <td><el-input v-model="addData.base.infoSource" placeholder="请输入"></el-input></td>
            </tr>
            <tr>
              <td class="txtRight">备注：</td>
              <td colspan="5"><el-input v-model="addData.base.memo" placeholder=""></el-input></td>
            </tr>
            <tr>
              <td class="ttl">开票信息</td>
              <td colspan="5" class="txtRight">
                <span>客户对发票方面的要求</span>
                <span class="marL30">{{ (addData.invoice.invoiceType && addData.invoice.invoiceType.txt) || '尚未设置'}}</span>
                <span class="ty-linkBtn marL30" @click="goSet">去设置</span>
              </td>
            </tr>
            <tr>
              <td class="txtRight">公司名称：</td>
              <td>
                <el-input v-model="addData.invoice.invoiceName" placeholder="请输入"></el-input>
              </td>
              <td class="txtRight">地址：</td>
              <td><el-input v-model="addData.invoice.invoiceAddress" placeholder="请输入"></el-input></td>
              <td class="txtRight">电话：</td>
              <td><el-input v-model="addData.invoice.telephone" placeholder="请输入"></el-input></td>
            </tr>
            <tr>
              <td class="txtRight">开户行：</td>
              <td>
                <el-input v-model="addData.invoice.bankName" placeholder="请输入"></el-input>
              </td>
              <td class="txtRight">账号：</td>
              <td><el-input v-model="addData.invoice.bankNo" placeholder="请输入"></el-input></td>
              <td class="txtRight">税号：</td>
              <td><el-input v-model="addData.invoice.taxpayerID" placeholder="请输入"></el-input></td>
            </tr>
            <tr>
              <td colspan="6">
                <span class="ttl"><i class="ty-color-red">*</i>货物的交付地点与方式</span>
                <span class="ty-btn ty-btn-blue marL30" @click="huoEdit">编辑</span>
              </td>
            </tr>
            <tr>
              <td colspan="6" style="padding-left: 40px;">
                {{ addData.huo.txt }}
              </td>
            </tr>
            <tr>
              <td class=" ttl">合同信息</td>
              <td colspan="5">
                <span class="ty-btn ty-btn-blue marL50" @click="addContract"> 新增</span>
                <span class="ty-color-blue marL30">注：点击“新增”，可录入该客户的合同信息。如与该客户有多个合同，需多次“新增”。</span>
              </td>
            </tr>
            <tr v-if="addData.contract.tableData.length >0">
              <td colspan="6">
                <el-table :data="addData.contract.tableData" stripe border>
                  <el-table-column prop="no" label="合同编号" width="200"> </el-table-column>
                  <el-table-column prop="date" label="签署日期" width="160"> </el-table-column>
                  <el-table-column prop="expDur" label="合同的有效期" width="240"> </el-table-column>
                  <el-table-column label="操作">
                    <template #default="scope">
                      <el-button  @click="updateContract(scope.row,scope.$index)" type="text" size="small">修改</el-button>
                      <el-button @click="delContract(scope.row,scope.$index)" type="text" size="small">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </td>
            </tr>
            <tr>
              <td class=" ttl">发票邮寄信息</td>
              <td colspan="5">
                <span class="ty-btn ty-btn-blue marL50" @click="addMail">新增</span>
              </td>
            </tr>
            <tr v-if="addData.mail.tableData.length > 0">
              <td colspan="6">
                <el-table :data="addData.mail.tableData" stripe border style="width: 100%">
                  <el-table-column type="index" label="序号" width="80"> </el-table-column>
                  <el-table-column prop="address" label="邮寄地址" width="180"> </el-table-column>
                  <el-table-column prop="recivePerson" label="发票接收人" width="180"> </el-table-column>
                  <el-table-column prop="mailNo" label="邮政编码" width="180"> </el-table-column>
                  <el-table-column prop="tel" label="联系电话" width="180"> </el-table-column>
                  <el-table-column label="操作" width="">
                    <template #default="scope">
                      <el-button @click="updateMail(scope.row, scope.$index)" type="text" size="small">修改</el-button>
                      <el-button @click="delMail(scope.row, scope.$index)" type="text" size="small">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </td>
            </tr>
            <tr>
              <td class=" ttl">联系人</td>
              <td colspan="5">
                <span class="ty-btn ty-btn-blue marL50" @click="addContact('contact')">新增</span>
              </td>
            </tr>
            <tr v-if="addData.contactLianxi.tableData.length > 0">
              <td colspan="6">
                <el-table v-if="addData.contactLianxi.tableData.length === 1" :data="contactLianxiTwo" stripe border style="width: 50%">
                  <el-table-column prop="name" label="姓名" width="80"> </el-table-column>
                  <el-table-column prop="post" label="职位" width="100"> </el-table-column>
                  <el-table-column label="操作">
                    <template #default="scope">
                      <el-button @click="updateContact(scope.row, 1)" type="text" size="small">修改</el-button>
                      <el-button @click="delContact(scope.row, 1)" type="text" size="small">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>

                <el-table v-else :data="contactLianxiTwo" stripe border style="width: 100%">
                  <el-table-column prop="name" label="姓名" width="80"> </el-table-column>
                  <el-table-column prop="post" label="职位" width="100"> </el-table-column>
                  <el-table-column label="操作">
                    <template #default="scope">
                      <el-button @click="updateContact(scope.row, 1)" type="text" size="small">修改</el-button>
                      <el-button @click="delContact(scope.row, 1)" type="text" size="small">删除</el-button>
                    </template>
                  </el-table-column>
                  <el-table-column prop="name2" label="姓名" width="80"> </el-table-column>
                  <el-table-column prop="post2" label="职位" width="100"> </el-table-column>
                  <el-table-column label="操作" >
                    <template #default="scope">
                          <span v-if="scope.row.name2">
                            <el-button @click="updateContact(scope.row, 2)" type="text" size="small">修改</el-button>
                            <el-button @click="delContact(scope.row, 2)" type="text" size="small">删除</el-button>
                          </span>
                    </template>
                  </el-table-column>
                </el-table>

              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>

    <!-- 新增发票邮寄地址 -->
    <TyDialog v-if="mailInfoData.visible" width="460" :dialogTitle="mailInfoData.title" color="green" :dialogHide="hideMail">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideMail">取消</span>
        <span class="ty-btn ty-btn-big ty-circle-5" :class="mailInfoOkBtn ? 'bounce-ok' : 'bounce-cancel'" @click="mailInfoOk">提交</span>
      </template>
      <template #dialogBody>
        <div class="newMailInfo">
          <table>
            <tbody>
            <tr>
              <td class="txtRight">邮寄地址：</td>
              <td><el-input v-model="mailInfoData.address" placeholder="请输入内容" clearable></el-input></td>
              <td></td>
            </tr>
            <tr>
              <td class="txtRight">邮寄编码：</td>
              <td><el-input v-model="mailInfoData.code" @change="chargeCode" placeholder="请输入内容" clearable></el-input>
              </td>
              <td></td>
            </tr>
            <tr v-if="!mailInfoData.mailValidSta" >
              <td></td>
              <td class="ty-color-red" style="font-size: 14px; line-height: 20px;">请输入正确的邮寄编码！</td>
              <td></td>
            </tr>
            <tr>
              <td class="txtRight">发票接收人：</td>
              <td>
                <div class="mask" @click="showContact('mailInfo')"></div>
                <el-input v-model="mailInfoData.contact.name" placeholder="请选择">
                  <template #append>
                    <i class="fa fa-angle-down"></i>
                  </template>
                </el-input></td>
              <td>
                <span class="ty-linkBtn" @click="addContact('mailInfo')">新增</span>
              </td>
            </tr>
            </tbody>
          </table>

        </div>
      </template>
    </TyDialog>

    <!-- 编辑收货信息-->
    <TyDialog class="receiveInfo" v-show="receiveData.visible" width="800" dialogTitle="编辑收货信息" color="blue" :dialogHide="hideReceiveInfoFun">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideReceiveInfoFun">取消</span>
        <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="receiveInfoOk">确定</span>
      </template>
      <template #dialogBody>
        <div class="bcc">
          <span @click="toggleReceiveDataType(1)"> <i class="fa" :class="receiveData.type === 1? 'fa-dot-circle-o':'fa-circle-o'"></i> 向该客户只提供服务，不提供实体货物，不需编辑收货地址 </span>
          <br>
          <span @click="toggleReceiveDataType(2)"> <i class="fa" :class="receiveData.type === 2? 'fa-dot-circle-o':'fa-circle-o'"></i> 向该客户提供实体货物 </span>
          <br>
        </div>
        <div v-if="receiveData.type == 2" class="nextcc">
          <div>
            <span>如向该客户提供送货上门服务，请点击“新增收货地址”，以录入可能的收货地址</span>
            <span class="ty-linkBtn ty-right" @click="addReceive">新增收货地址</span>
            <br>
            <el-table v-if="receiveData.addressList.length >0" :data="receiveData.addressList" border stripe style="width: 100%">
              <el-table-column prop="addr" label="收货地址" width="180"> </el-table-column>
              <el-table-column prop="name" label="收货人" width="180"> </el-table-column>
              <el-table-column prop="tel" label="收货电话"> </el-table-column>
              <el-table-column label="操作" width="200">
                <template #default="scope">
                    <span class="ty-table-control">
                      <span class="ty-color-blue" @click="addressItemEdit(scope.row,scope.$index)">修改</span>
                      <span class="ty-color-red" @click="addressItemDel(scope.row,scope.$index)">删除</span>
                    </span>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div>
            <span>如向该客户提供配送至某城市的服务，请点击“新增到货区域”，以录入可能的到货区域</span>
            <span class="ty-linkBtn ty-right" @click="addArea('add')">新增到货区域</span>
            <br>
            <el-table v-if="receiveData.areaList.length > 0" :data="receiveData.areaList" border stripe style="width: 100%">
              <el-table-column prop="addr" label="到货区域" width="280"> </el-table-column>
              <el-table-column prop="name" label="收货人" width="100"> </el-table-column>
              <el-table-column prop="tel" label="收货电话"> </el-table-column>
              <el-table-column label="操作" width="160">
                <template #default="scope">
                  <div class="ty-table-control">
                    <span class="ty-btn ty-btn-blue ty-circle-5" @click="areaItemEdit(scope.row, scope.$index)">修改</span>
                    <span class="ty-btn ty-btn-red ty-circle-5" @click="areaItemDel(scope.row,scope.$index)">删除</span>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div>
            <span>本选项为“上门自提”选项。当前状态为“{{ receiveData.status === 0 ? '未开启' : '开启'}}”。如需要可“改变状态”。</span>
            <span class="ty-linkBtn ty-right" @click="toggleSta">改变状态</span>
            <br>
            <span class="ty-color-blue">
                注：状态为“未开启”的，录入该客户商品或订单的到货地点时，“上门自提”选项不展示，“开启”则反之
              </span>
          </div>
        </div>
      </template>
    </TyDialog>

    <!-- 新增/修改 收货地址-->
    <TyDialog class="addressEdit" v-show="addressEditData.visible" width="460" :dialogTitle="addressEditData.title" color="blue" :dialogHide="hideaddressEditFun">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideaddressEditFun">取消</span>
        <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="addressEditOk">确定</span>
      </template>
      <template #dialogBody>
        <div class="addressEditcc">
          <table>
            <tr>
              <td class="txtRight"><i class="ty-color-red">*</i>收货地址</td>
              <td><el-input v-model="addressEditData.address" placeholder="请输入内容"></el-input></td>
              <td></td>
            </tr>
            <tr>
              <td class="txtRight"><i class="ty-color-red">*</i>联系人</td>
              <td>
                <div class="mask" @click="showContact('addressEdit')"></div>
                <el-input v-model="addressEditData.contact.name" placeholder="">
                  <template #append>
                    <i class="fa fa-angle-down"></i>
                  </template>
                </el-input></td>
              <td>
                <span class="ty-linkBtn" @click="addContact('addressEdit')">新增</span>
              </td>
            </tr>
          </table>
        </div>
      </template>
    </TyDialog>

    <!-- 新增到货区域-->
    <TyDialog class="receiveArea" v-show="receiveAreaInfoData.visible" width="560" :dialogTitle="receiveAreaInfoData.title" color="blue" :dialogHide="hideReceiveAreaInfo">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideReceiveAreaInfo">取消</span>
        <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="receiveAreaOK">确定</span>
      </template>
      <template #dialogBody>
        <div class="addressEditcc">
          <table>
            <tbody>
            <tr>
              <td class="txtRight"><i class="ty-color-red">*</i>货物需到达的城市或地区</td>
              <td>
                <el-cascader style="width:230px "
                             v-model="receiveAreaInfoData.areaArr"
                             :options="receiveAreaInfoData.options"
                             separator="" @change="handleChange"
                ></el-cascader>

              </td>
              <td></td>
            </tr>
            <tr>
              <td class="txtRight">对需到达地点的特殊要求</td>
              <td><el-input v-model="receiveAreaInfoData.requirements" placeholder="请输入内容" clearable></el-input></td>
              <td></td>
            </tr>
            <tr>
              <td class="txtRight"><i class="ty-color-red">*</i>联系人</td>
              <td>
                <div class="mask" @click="showContact('receiveArea')"></div>
                <el-input v-model="receiveAreaInfoData.contact.name" placeholder="">
                  <template #append>
                    <i class="fa fa-angle-down"></i>
                  </template>
                </el-input></td>
              <td>
                <span class="ty-linkBtn" @click="addContact('receiveArea')">新增</span>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>

    <!-- 客户对发票方面要求的设置-->
    <TyDialog class="invoiceSet" v-if="invoiceSetData.visible" width="400" dialogTitle="客户对发票方面要求的设置" color="blue" :dialogHide="hideinvoiceSet">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideinvoiceSet">取消</span>
        <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="invoiceSetOk">确定</span>
      </template>
      <template #dialogBody>
        <div class="chooseCusContactcc">
          <div>请选择此客户对发票方面的要求</div>
          <div class="line30">
            <table>
              <tr v-for="(invoiceItem, indexIv) in invoiceSetData.options" :key="indexIv">
                <td @click="clickInvoice(invoiceItem)" >
                    <span class="clickArea">
                      <i class="fa" :class="invoiceSetData.selectType && invoiceSetData.selectType.type === invoiceItem.type ? 'fa-dot-circle-o' : 'fa-circle-o' "></i> {{ invoiceItem.txt }}
                    </span>
                </td>
              </tr>
            </table>
          </div>
        </div>
      </template>
    </TyDialog>

    <!-- 选择客户联系人-->
    <TyDialog class="chooseCusContact" v-if="chooseCusContactData.visible" width="460" dialogTitle="选择客户联系人" color="blue" :dialogHide="hideChooseCusContactFun">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideChooseCusContactFun">取消</span>
        <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="chooseCusContactOk">确定</span>
      </template>
      <template #dialogBody>
        <div class="chooseCusContactcc">
          <div>以下为可供选择的客户联系人</div>
          <div class="table">
            <table>
              <tr v-for="(Conac, indexConac) in chooseCusContactData.contactList" :key="indexConac">
                <td width="80%">
                    <span @click="clickItem(Conac)" class="clickArea">
                      <i class="fa" :class="chooseCusContactData.selectConact && chooseCusContactData.selectConact.id == Conac.id ? 'fa-dot-circle-o' : 'fa-circle-o' "></i>
                      {{ Conac.name }}
                      <span style="margin-left: 16px;">{{ Conac.post }}</span>
                      <span style="margin-left: 16px;">{{ Conac.mobile }}</span>
                    </span>
                </td>
                <td class="txtRight"><span class="ty-linkBtn" @click="detailsConac(Conac)">查看</span></td>
              </tr>
            </table>
          </div>
        </div>
      </template>
    </TyDialog>

    <!-- 新增联系人-->
    <TyDialog class="editContactLianXi" v-if="editContactLianXiData.visible" width="750" :dialogTitle="editContactLianXiData.title" color="green" :dialogHide="hideEditContactLianXiFun">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideEditContactLianXiFun">取消</span>
        <span class="ty-btn ty-btn-big ty-circle-5" :class="editContactLianXiOkStatus ? 'bounce-ok':'bounce-cancel'" @click="editContactLianXiOk">确定</span>
      </template>
      <template #dialogBody>
        <div class="editContactLianXicc">
          <table>
            <tr>
              <td class="txtRight">联系人标签：</td>
              <td colspan="3">{{ editContactLianXiData.tag }}</td>
            </tr>
            <tr>
              <td class="txtRight">姓名：</td>
              <td><el-input v-model="editContactLianXiData.name" placeholder="请录入"></el-input></td>
              <td class="txtRight">职位：</td>
              <td><el-input v-model="editContactLianXiData.postName" placeholder="请录入"></el-input></td>
            </tr>
            <tr>
              <td class="txtRight">手机：</td>
              <td><el-input v-model="editContactLianXiData.tel" placeholder="请录入"></el-input></td>
              <td><span class="ty-linkBtn" @click="showOptionsFun">添加更多联系方式</span></td>
              <td>
                <el-select v-if="editContactLianXiData.showOptions" v-model="editContactLianXiData.defineType" placeholder="请选择" @change="setLink">
                  <el-option
                      v-for="(item, indexI) in editContactLianXiData.typeList"
                      :key="item.value"
                      :label="item.label"
                      :value="indexI"
                      :disabled="item.disabled"
                  >
                  </el-option>
                </el-select>
              </td>
            </tr>
            <tr v-for="(contact, index) in editContactLianXiData.contactList" :key="index">
              <td class="txtRight">{{ contact.name }}：</td>
              <td><el-input v-model="contact.val" @change="setArrVal(contact,1)"></el-input><span class="ty-linkBtn-red" @click="delLink(contact, 1)">删除</span></td>
              <td class="txtRight"><span v-if="contact.name2">{{ contact.name2 }}：</span></td>
              <td >
                <div v-if="contact.name2" >
                  <el-input v-model="contact.val2" @change="setArrVal(contact,2)"></el-input><span class="ty-linkBtn-red" @click="delLink(contact, 2)">删除</span>
                </div>
              </td>
            </tr>
            <tr>
              <td class="txtRight">名片：</td>
              <td colspan="3"></td>
            </tr>
            <tr >
              <td class="txtRight"></td>
              <td colspan="3">
                <div v-if="editContactLianXiData.picInfo.src" class="imgcc">
                  <img :src="editContactLianXiData.picInfo.src" alt="名片">
                  <span class="ty-linkBtn-red" @click="delPic">删除</span>
                </div>
                <div v-else
                     v-loading="editContactLianXiData.loading"
                     element-loading-background="rgba(100, 100, 100, 0.1)"
                     element-loading-text="正在上传...."
                     style="width: 200px; min-height: 40px; max-height: 150px;">
                  <uploadFile ref="uploadFile"
                              module="客户管理"
                              committed.async="false"
                              :showSelect="false"
                              :showFileList="false"
                              :autoUpload="true"
                              ext='.png,.jpg,.jpeg'
                              extMsg='请上传名片！'
                              :multiple="false"
                              :beforeUploadFun="beforeUploadLianXi"
                              :successFun="fileSuccessLianXi">
                    <template #btnArea>
                      <span class="ty-linkBtn lix" @click="uploadFileLianXi">{{ editContactLianXiData.loadingBtn }}</span>
                    </template>
                  </uploadFile>
                </div>

              </td>

            </tr>
          </table>

        </div>
      </template>
    </TyDialog>

    <!-- 自定义标签-->
    <TyDialog class="useDefinedLabel" v-if="useDefinedLabelData.visible" width="450" dialogTitle="自定义标签" color="blue" :dialogHide="hideUseDefinedLabel">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideUseDefinedLabel">取消</span>
        <span class="ty-btn ty-btn-big ty-circle-5" :class="useDefinedLabelData.val?'bounce-ok':'ty-btn-gray'" @click="useDefinedLabelOk">使用</span>
      </template>
      <template #dialogBody>
        <div class="useDefinedLabelcc">
          <span>自定义标签</span><br>
          <el-input v-model="useDefinedLabelData.val"></el-input>
        </div>
      </template>
    </TyDialog>

    <!-- 新增、修改、续约 合同-->
    <TyDialog class="contractInfo" v-show="contractInfoData.visible" width="500" :dialogTitle="contractInfoData.title" color="blue" :dialogHide="hideContractInfo">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideContractInfo">取消</span>
        <span class="ty-btn ty-btn-big ty-circle-5 bounce-ok" @click="contractInfoOk">提交</span>
      </template>
      <template #dialogBody>
        <div class="contractInfo" v-if="contractInfoData">
          <table>
            <tbody>
            <tr v-if="contractInfoData.type === 'manageAdd' || contractInfoData.type === 'manageUpdate'|| contractInfoData.type === 'manageXu'">
              <td>客户</td>
            </tr>
            <tr v-if="contractInfoData.type === 'manageAdd' || contractInfoData.type === 'manageUpdate'|| contractInfoData.type === 'manageXu'">
              <td colspan="3">
                <el-input v-model="contractInfoData.cusName" disabled="true"></el-input>
              </td>
            </tr>
            <tr>
              <td><i class="ty-color-red">* </i>合同编号</td>
              <td width="50px;"></td>
              <td>签署日期</td>
            </tr>
            <tr>
              <td><el-input v-model="contractInfoData.cNo" placeholder="请录入"></el-input></td>
              <td></td>
              <td><el-date-picker v-model="contractInfoData.cSignDate" type="date" placeholder="选择日期" value-format="YYYY-MM-DD"></el-date-picker></td>
            </tr>
            <tr class="pt">
              <td colspan="3"><i class="ty-color-red">* </i>合同的有效期</td>
            </tr>
            <tr>
              <td><el-date-picker v-model="contractInfoData.cStartDate" type="date" placeholder="选择日期" value-format="YYYY-MM-DD"></el-date-picker></td>
              <td></td>
              <td><el-date-picker v-model="contractInfoData.cEndDate" type="date" placeholder="选择日期" value-format="YYYY-MM-DD"></el-date-picker></td>
            </tr>
            <tr class="pt">
              <td colspan="2">合同的扫描件或照片(共可上传9张)</td>
              <td class="txtRight">
                <uploadFile ref="uploadFile"
                            module="客户管理"
                            committed.async="false"
                            :showSelect="false"
                            :showFileList="false"
                            :autoUpload="true"
                            ext='.png,.jpg,.jpeg,.pdf'
                            extMsg='请上传扫描件或照片！'
                            :multiple="true"
                            :beforeUploadFun="beforeUploadContract"
                            :successFun="fileSuccessContract">
                  <template #btnArea>
                    <span class="ty-linkBtn">上传</span>
                  </template>
                </uploadFile>
              </td>
            </tr>
            <tr>
              <td colspan="3" class="box">
                  <span v-for="(pic, index) in contractInfoData.scanPics" :key="index" class="scanPicItem">
                    {{ index+1 }} <i class="fa fa-close" @click="delSP(index)"></i>
                  </span>
              </td>
            </tr>
            <tr class="pt">
              <td colspan="2">合同的可编辑版</td>
              <td class="txtRight">
                <uploadFile ref="uploadFile"
                            module="客户管理"
                            committed.async="false"
                            :showSelect="false"
                            :showFileList="false"
                            :autoUpload="true"
                            ext='.doc,.docx,.xls,.xlsx,.ppt,.txt'
                            extMsg='请上传合同的可编辑版！'
                            :multiple="true"
                            :successFun="fileSuccessContract2">
                  <template #btnArea>
                    <span class="ty-linkBtn">上传</span>
                  </template>
                </uploadFile>
              </td>
            </tr>
            <tr>
              <td colspan="3" class="box">
                   <span class="scanPicItem" v-if="contractInfoData.scanFile && contractInfoData.scanFile.name">
                    <i class="fa fa-file-word-o"></i> {{ contractInfoData.scanFile.name }} <i class="fa fa-close" @click="delSF"></i>
                  </span>
              </td>
            </tr>
            <tr class="pt" v-if="contractInfoData.type === 'manageAdd' || contractInfoData.type === 'manageUpdate'|| contractInfoData.type === 'manageXu'">
              <td colspan="2">本合同下的专属商品 <span class="ty-color-blue">{{ (contractInfoData.selectZSGs && contractInfoData.selectZSGs.length) || 0 }}</span> 种</td>
              <td class="txtRight">
                <span class="ty-linkBtn" @click="removeTyGs('zs')">移出商品</span>
                <span class="ty-linkBtn" @click="addTyGs('zs')">添加商品</span>
              </td>
            </tr>
            <tr v-if="contractInfoData.type === 'manageAdd' || contractInfoData.type === 'manageUpdate'|| contractInfoData.type === 'manageXu'">
              <td colspan="3" class="box">
                <span class="scanPicItem" v-for="(gs,index) in contractInfoData.selectZSGs" :key="index">{{ gs.outerName }}</span>
              </td>
            </tr>
            <tr class="pt">
              <td colspan="2">本合同下的通用型商品 <span class="ty-color-blue">{{ (contractInfoData.selectGs && contractInfoData.selectGs.length ) || 0}}</span> 种</td>
              <td class="txtRight">
                <span class="ty-linkBtn" @click="removeTyGs">移出商品</span>
                <span class="ty-linkBtn" @click="addTyGs">添加商品</span>
              </td>
            </tr>
            <tr>
              <td colspan="3" class="box">
                <span class="scanPicItem" v-for="(gs,index) in contractInfoData.selectGs" :key="index">{{ gs.outerName }}</span>
              </td>
            </tr>
            <tr class="pt">
              <td colspan="3">备注</td>
            </tr>
            <tr>
              <td colspan="3"><el-input v-model="contractInfoData.cMemo" placeholder="请录入"></el-input></td>
            </tr>
            <tr>

            </tr>
            </tbody>
          </table>
        </div>
      </template>
    </TyDialog>

    <!-- 向本合同添加商品 从本合同移出商品 本合同下的商品-->
    <TyDialog v-if="contractGoodsData.visible" width="800" :dialogTitle="contractGoodsData.title" color="blue" :dialogHide="hideCG">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="hideCG">取消</span>
        <span class="ty-btn ty-btn-big ty-circle-5 bounce-ok" @click="contractGoodsOk">确定</span>
      </template>
      <template #dialogBody>
        <div class="tipcontractGoods">
          <p class="mar" >系统内共有以下{{ contractGoodsData.gslist && contractGoodsData.gslist.length }}种通用型商品，均可选择
            <span class="ty-right">已选 {{ contractGoodsData.num }} 种</span>
          </p>
          <table class="ty-table ty-table-control">
            <tbody>
            <tr>
              <td></td>
              <td>商品代号</td>
              <td>商品名称</td>
              <td>型号</td>
              <td>规格</td>
              <td>计量单位</td>
              <td>已包含的合同</td>
            </tr>
            <tr v-for="(gs, index) in contractGoodsData.gslist" :key="index">
              <td width="40px" @click="toggleSelect(gs)"><i class="fa" :class="gs.selected ? 'fa-check-square-o':'fa-square-o'"></i></td>
              <td>{{gs.outerSn }}</td>
              <td>{{gs.outerName }}</td>
              <td>{{gs.model }}</td>
              <td>{{gs.specifications }}</td>
              <td>{{gs.unit }}</td>
              <td>
                <span class="ty-color-blue" @click="gsContracts(gs)">{{ gs.contractNum  }} 个</span>
              </td>
            </tr>
            </tbody>
          </table>

        </div>
      </template>
    </TyDialog>

    <!-- 批量导入-->
    <TyDialog class="custormerLeading" v-if="custormerLeadingData.visible" width="550" dialogTitle="批量导入" color="blue" :dialogHide="hideloadingFun">
      <template #dialogBody>
        <div>
          <div class="exportStep">
            <div class="stepItem">
              <p>第一步：下载空白的“客户清单”。</p>
              <div class="flexRow">
                <span>客户清单</span>
                <a :href="customer_blank"
                   id="mould1" download="客户清单.xls" class="ty-btn ty-btn-blue ty-btn-middle">下 载</a>
              </div>
            </div>
            <div class="stepItem">
              第二步：在空白的“客户清单”中填写内容，并存至电脑。
            </div>
            <div class="stepItem">
              <p>第三步：点击“浏览”后，选择所保存的文件并上传。</p>
              <div class="flexRow">
                <div class="upload_sect viewBtn">
                  <div>
                    <el-input readonly id="uploadFile" placeholder="尚未选择文件" v-model="custormerLeadingData.fileName">
                      <template #prepend>
                        <uploadFile ref="uploadFile" class="ty-right"
                                    module="销售管理"
                                    committed.async="false"
                                    :showSelect="false"
                                    :showFileList="false"
                                    :autoUpload="true"
                                    ext='.xls,.xlsx'
                                    extMsg='请按照客户清单模版上传！'
                                    :successFun="fileSuccess"
                        >
                          <template #btnArea>
                            <div>
                              <span @click="uploadFile">浏 览</span>
                            </div>
                          </template>
                        </uploadFile>
                      </template>
                    </el-input>
                  </div>
                </div>
              </div>
            </div>
            <div class="stepItem">
              <p>第四步：点击“导入”。</p>
              <div class="flexRow">
                <span class="ty-btn ty-btn-yellow ty-btn-middle" @click="hideloadingFun">取 消</span>
                <span class="ty-btn ty-btn-blue ty-btn-middle" @click="matImportOk">导 入</span>
              </div>
            </div>
            <div class="importIntro stepItem">
              <div style="text-align:left;color:red;"><span>导入说明：</span></div>
              <div style="text-align:left;">
                <span>1、请勿增加、删除或修改所下载“客户清单”空白表的“列”，否则上传会失败。</span><br>
                <span>2、在电脑上保存“客户清单”时，可为该文件重新命名，但“浏览”时如选错文件则上传会失败。</span>
              </div>
            </div>
          </div>
        </div>
      </template>
    </TyDialog>
    <!--录入已销售过的商品-->
    <TyDialog v-show="addUniversalPt.visible" dialogTitle="录入已销售过的商品" color="blue" width="980" :dialogHide="chargeXhr">
      <template #dialogFooter>
        <span class="ty-btn bounce-cancel ty-btn-big ty-circle-5" @click="chargeXhr">关闭</span>
        <span class="ty-btn bounce-ok ty-btn-big ty-circle-5" @click="addCommoditySure">确定</span>
      </template>
      <template #dialogBody>
        <el-form ref="addUniversalPtRef" :rules="addPtrules" :model="addUniversalPtForm" label-position="top" label-width="80px">
          <div class="modInput">
            <div class="clearBoth">
              <div class="modTip ty-left">
                <p>您正在向“<span class="ty-color-red">待分类</span>”下录入商品！</p>
                <p class="ty-color-blue sold" style="font-size: 12px;">注：已销售过的商品录入后，系统将提示成品库的库管员<span class="ty-color-red">填写初始库存。</span></p>
                <p class="ty-color-blue noSold" style="font-size: 12px;">注：未销售过的商品录入后，其<span class="ty-color-red">初始库存默认为零！</span></p>
              </div>
<!--              <div class="ty-right importSect">
                <span class="nodeBtn" id="importBtn" data-fun="importOpen">批量导入</span>
              </div>-->
            </div>
            <div class="ty-hr"></div>
            <table class="pannelTab">
              <tr>
                <td>
                  <el-form-item label="商品代号" prop="outerSn">
                    <el-input v-model="addUniversalPtForm.outerSn" placeholder="请录入" required autocomplete="off" clearable></el-input>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="商品名称" prop="outerName">
                    <el-input v-model="addUniversalPtForm.outerName" placeholder="请录入" required autocomplete="off" clearable></el-input>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="规格" prop="specifications">
                    <el-input v-model="addUniversalPtForm.specifications" placeholder="请录入"  autocomplete="off" clearable></el-input>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="型号" prop="model">
                    <el-input v-model="addUniversalPtForm.model" placeholder="请录入"  autocomplete="off" clearable></el-input>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td colspan="2" class="firstTimeCCon">
                  <span>成为公司通用型商品的时间</span>
                  <span class="marL30 " id="firstTimeCC" data-type="0">
                              <span class="radiop cc1" @click="toggefirstTimeGoods(1)"><i class="fa" :class="addUniversalPtForm.firstTime === 1? 'fa-dot-circle-o':'fa-circle-o'"></i>今年</span>
                             <span v-show="addUniversalPtForm.firstTime === 1">
                         <el-date-picker v-model="addUniversalPtForm.initialPeriod" type="month" placeholder="选择月" @change="setFirstTimeNGoods"
                                         style="width: 100px" format="M月" value-format="YYYYMM" :disabled-date="disabledDate"></el-date-picker>
                          <span class="radiop" @click="toggefirstTimeNGoods"><i class="fa" :class="addUniversalPtForm.firstTimeN? 'fa-dot-circle-o':'fa-circle-o'"></i>不确定月份</span>
                      </span>
                              <span class="radiop cc2" @click="toggefirstTimeGoods(2)"><i class="fa" :class="this.addUniversalPtForm.firstTime === 2? 'fa-dot-circle-o':'fa-circle-o'"></i>去年</span>
                              <span class="radiop cc3" @click="toggefirstTimeGoods(3)"><i class="fa" :class="this.addUniversalPtForm.firstTime === 3? 'fa-dot-circle-o':'fa-circle-o'"></i>更久之前</span>
                            </span>
                </td>
              </tr>
            </table>
            <div class="ty-hr"></div>
            <div class="ty-color-blue" style="margin:20px 0 0 32px;font-size: 12px;">注：录入订单时，需确认开票情况，并将引用此处单价。在此也可只录入参考单价。</div>
            <table class="pannelTab">
              <tr>
                <td class="invoice1">
                  <div>开增值税专用发票时的单价</div>
                  <div class="modItem-ss ty-left">
                    <el-form-item label="税率" prop="taxRate">
                      <el-select v-model="addUniversalPtForm.taxRate" placeholder="请录入" @change="reSetPrice()">
                        <el-option value="">请选择</el-option>
                        <el-option
                            v-for="(item, index) in invoiceTaxRateList"
                            :key="item"
                            :label="item+'%'"
                            :value="item"
                        >
                          </el-option>
                      </el-select>
                    </el-form-item>
                  </div>
                  <div class="modItem-s ty-left">
                    <el-form-item label="不含税单价" prop="unitPriceNotax">
                      <el-input v-model="addUniversalPtForm.unitPriceNotax" placeholder="请录入"  autocomplete="off" @keyup="clearNoNumN(this,10);autoPrice(1);" clearable></el-input>
                    </el-form-item>
                  </div>
                  <div class="modItem-s ty-right">
                    <el-form-item label="含税单价" prop="unitPrice">
                      <el-input v-model="addUniversalPtForm.unitPrice" placeholder="请录入"  autocomplete="off" @keyup="clearNoNumN(this,10);autoPrice(2);" clearable></el-input>
                    </el-form-item>
                  </div>
                </td>
                <td>
                  <el-form-item class="moveBt" label="开普通发票时的开票单价" prop="unitPriceInvoice">
                    <el-input v-model="addUniversalPtForm.unitPriceInvoice" placeholder="请录入"  autocomplete="off" @keyup="clearNoNumN(this,10)" clearable></el-input>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="不开发票时的单价" prop="unitPriceNoinvoice">
                    <el-input v-model="addUniversalPtForm.unitPriceNoinvoice" placeholder="请录入"  autocomplete="off" @keyup="clearNoNumN(this,10)" clearable></el-input>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="参考单价" prop="unitPriceReference">
                    <el-input v-model="addUniversalPtForm.unitPriceReference" placeholder="请录入"  autocomplete="off" @keyup="clearNoNumN(this,10)" clearable></el-input>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td>
<!--                  <input type="hidden" name="unit" id="add_unitName">-->
                  <span class="nodeBtn ty-right" @click="addUnit">新增</span>
                  <el-form-item label="计量单位" prop="unitId">
                    <el-select v-model="addUniversalPtForm.unitId">
                      <el-option value="">请选择</el-option>
                      <el-option
                          v-for="item in unitList"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="价格说明" prop="priceDesc">
                    <el-input v-model="addUniversalPtForm.priceDesc"
                              placeholder="请录入"  autocomplete="off"
                              clearable
                              maxlength="100"
                              show-word-limit
                    ></el-input>
                  </el-form-item>
                </td>
              </tr>
            </table>
            <div class="ty-hr"></div>
            <table class="pannelTab">
              <tr>
                <td>
                  <p class="modItemTtl">
                    商品照片(最多可上传9张)
                    <uploadFile ref="uploadFile3" class="ty-right"
                                module="初始化"
                                committed.async="false"
                                :showSelect="false"
                                :showFileList="false"
                                :autoUpload="true"
                                ext='.png,.jpg,.gif,.jpeg'
                                extMsg='上传！'
                                :multiple="true"
                                :beforeUploadFun="gs_beforeUpload2"
                                :successFun="gs_fileSuccess2">
                      <template #btnArea>
                        <span class="ty-btn ty-btn-blue " @click="gs_uploadFile2">上传</span>
                      </template>
                    </uploadFile>
                  </p>
                  <div class="modPics">
                     <span class="imgI" v-for="(img, indexC2) in addUniversalPtForm.chanImg" :key="indexC2">
                        <el-image class="img" :src="img.uplaodPath" fit="cover" :preview-src-list="[img.uplaodPath]"></el-image>
                        <span class="delBtn ty-color-red" @click="gs_delImg(indexC2, 'chanImg')"><i class="fa fa-close"></i></span>
                      </span>
                  </div>
                </td>
                <td>
                  <p class="modItemTtl">
                    商品视频(可上传一个，且不可超过15秒)
                    <uploadFile ref="uploadFile3" class="ty-right"
                                module="初始化"
                                committed.async="false"
                                :showSelect="false"
                                :showFileList="false"
                                :autoUpload="true"
                                ext='.mp4'
                                extMsg='上传！'
                                :multiple="false"
                                :beforeUploadFun="gs_beforeUpload"
                                :successFun="gs_fileSuccess">
                      <template #btnArea>
                        <span class="ty-btn ty-btn-blue " @click="gs_uploadFile">上传</span>
                      </template>
                    </uploadFile>
                  </p>
                  <div class="file-box fileVedioBox clear">
                    <span class="inTip" v-if="addUniversalPtForm.vedioNum === 0">请上传</span>
                    <el-link type="primary" @click="vedioPlayBtn()">1</el-link>
                  </div>

                </td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="最低库存" prop="minimumStock">
                    <el-input v-model="addUniversalPtForm.minimumStock" placeholder="请录入"  autocomplete="off" @keyup="testNumSize3(this)" required clearable></el-input>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="商品说明" prop="memo">
                    <el-input v-model="addUniversalPtForm.memo" placeholder="请录入"  autocomplete="off" clearable
                              maxlength="100"
                              show-word-limit
                    ></el-input>
                  </el-form-item>
                </td>
              </tr>
            </table>
          </div>
        </el-form>

      </template>
    </TyDialog>
    <div id="video-box" v-show="vedioPlay">
      <video id="Video1" controls height="320" width="240" title="video element">
        <source :src="addUniversalPtForm.vedio" type="video/mp4">
      </video>
    </div>
    <!-- 操作说明-->
    <TyDialog v-if="goodsAddNotice" width="650" dialogTitle="操作说明" color="blue" :dialogHide="goodsAddNoticeHide">
      <template #dialogFooter>
        <span class="ty-btn ty-btn-big ty-circle-5 bounce-ok" @click="goodsAddNoticeHide">知道了</span>
      </template>
      <template #dialogBody>
        <div class="rowGap">
          1 此处只可录入“通用型商品”，请根据实际情况录入！
        </div>
        <div class="rowGap">
          2 系统要求通用型商品或专属商品至少需录入一种，否则初始化工作无法完成。所以，没有需录入到系统的”通用型商品“时，可在页面进行相应操作，但专属商品处将有限制！
        </div>
      </template>
    </TyDialog>
    <!--  新增计量单位   -->
    <TyDialog v-if="addUnitLog" width="500" dialogTitle="新增计量单位" color="blue" :dialogHide="addUnitLogHide">
      <template #dialogFooter>
        <el-button class="bounce-cancel" @click="addUnitLogHide">取消</el-button>
        <el-button class="bounce-ok" @click="addUnitOkBtn(1)">确定</el-button>
      </template>
      <template #dialogBody>
        <el-form ref="unitForm" :model="unitForm" label-width="80px" label-position="top">
          <el-form-item label="计量单位">
            <el-input type="text" v-model="unitForm.name"  placeholder="请录入计量单位的名称"></el-input>
          </el-form-item>
        </el-form>
      </template>
    </TyDialog>
  </div>
</template>

<script>
import { initNav } from "@/utils/routeChange"
import customerManageJs from "@/mixins/customerManage";
import saleGoodsJs from "@/mixins/saleGoods";

export default {
  mixins: [customerManageJs, saleGoodsJs],
  data() {
    return {
      pageName:'saleInit',
      saleNum: 2,
      initFinishState: false,
      cusFinishState: false,
      indexInitMenu: [],
      cusMainData: []
    }
  },
  watch:{
  },
  mounted() {
    initNav({mid: 'vh', name: '销售1', pageName: this.pageName}, this.getInitState, this)
  },
  methods: {
    getInitState(){
      this.saleNum = 1
    },
    checkNextStep(){
      this.initFinishState = !this.initFinishState
    },
    editCustomersList(){
      this.saleNum = 2
    },
    editUniversalPt(){
      this.getUniversalPtList()
    },
    kindBtn(json){
      this.currNavList.push(json)
      this.getUniversalPtList()
    }
  }
}
</script>
<style scoped lang="scss">
@use "@/style/commodityCommon.scss";
$primary-blue: #5d9cec;
#saleInit{
  font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;
  font-size: 14px;
  font-weight: normal;
  line-height: 1.42857;
  color: #34495e;
  padding: 20px 100px;
  margin-top: 10px;
  .line{
    line-height: 30px;
  }
  .btCon{
    margin-top: 22px;
  }
  .rowGap{
    margin-bottom: 10px;
  }
  .gapRt{
    margin-right: 10px;
  }
  .gapRtL{
    margin-right: 50px;
  }
  .ty-hr{
    margin: 16px 0;
    width: 100%;
    height: 1px;
    background: #eee;
  }
  .ty-alert{
    clear: both;
    color: #101010;justify-content: space-between;
    padding: 8px 0;
    box-sizing: border-box;
    border-radius: 2px;
    position: relative;
    font-size: 13px;
    overflow: hidden;
    opacity: 1;
    display: flex;
    align-items: center;
    transition: opacity .2s;
    margin-bottom: 4px;
  }
  .backGap{
    margin-bottom: 40px;
  }
  .searchTxt{
    width: 260px;
  }
  .searchTxt :deep(.el-input-group__append){
    background-color: $primary-blue;
    color: #fff;
  }

  /*客户管理*/
  .sl-dropdown{
    display: inline-block; position: relative;
    .dropdownCon{
      position: absolute;
      background: #fff;
      border: 1px solid #ccc;
      border-bottom: none;
      width: 120px;
      top: 23px;
      opacity: 1;
      left: -33px;
      border-radius: 4px;
      box-shadow: 0 0 5px #ccc;
      z-index: 1;
      padding-top: 5px;
      &:before{
        content: "";
        display: inline-block;
        width: 8px;
        height: 8px;
        border: 1px solid #ccc;
        transform: rotate(45deg);
        position: absolute;
        top: -6px;
        left: 55px;
        background: #fff;
        border-bottom: none;
        border-right: none;
      }
      .disableItem{
        border-bottom: 1px solid #ccc; color: #666;
        line-height: 30px;
        background-color: #eee;
      }
      .dropdownItem:hover{
        background-color: $tyBounce-color-blue-hover;
        color: #fff;
      }
      .dropdownItem{
        border-bottom: 1px solid #ccc;
        color: $tyBounce-color-blue-hover;
        line-height: 30px;
      }
    }
    .dropdownCon2{
      position: absolute;
      background: #fff;
      border: 1px solid #ccc;
      //border-bottom: none;
      width: 120px;
      height: 120px;
      top: -93px;
      opacity: 1;
      left: -33px;
      border-radius: 4px;
      box-shadow: 0 0 5px #ccc;
      z-index: 1;
      padding-top: 5px;
      &:before{
        content: "";
        display: inline-block;
        width: 8px;
        height: 8px;
        border: 1px solid #ccc;
        transform: rotate(45deg);
        position: absolute;
        top: 121px;
        left: 54px;
        background: #fff;
        border-top: none;
        border-left: none;
      }
      .disableItem{
        border-bottom: 1px solid #ccc; color: #666;
        line-height: 30px;
        background-color: #eee;
      }
      .dropdownItem:hover{
        background-color: $tyBounce-color-blue-hover;
        color: #fff;
      }
      .dropdownItem{
        border-bottom: 1px solid #ccc;
        color: $tyBounce-color-blue-hover;
        line-height: 30px;
      }
    }
  }

  .rotating {
    animation: rotate-animation 1.5s linear infinite;
  }

  @keyframes rotate-animation {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  .txtRight{
    text-align: right;
  }
  .txtLeft{
    text-align: left;
  }
  .btnss{ width:350px; float: right;   }
  .rMMsg{
    padding:20px 50px; line-height: 30px;
    .ttl{ width: 150px; display: inline-block;  }
    .hr{ border-bottom: 1px solid #ccc; margin: 10px 0;  }
  }
  .addService{
    .radioB{
      line-height:40px; cursor: pointer;
      .fa{ margin-right:10px; margin-left:10px; color:$tyBounce-color-green; font-size: 18px;  }
    }
  }
  .packageList{
    .tip{ font-size: 14px; line-height:20px; color: #666; padding: 10px 20px;    }
  }
  .bdTtl{ font-weight:bold; font-size: 14px; color:#666; line-height: 40px;   }
  .packageInfo{
    line-height:30px ;
    .packageName{ font-weight: bold; font-size:24px;  }
    .marT39{ margin-top: 12px; }
    .smallT{ font-size:14px; color: #666;  }
    .ccc{ font-size: 14px; font-weight: bold; margin-right:90px;   }
  }
  .orgEdit{
    line-height: 40px;
    table{ margin-left: 50px; }
    .el-select{ width: 100%; }
    td:nth-child(2){ padding-left: 20px; }
    td:nth-child(1){
      text-align: right; padding-left: 16px;
    }

  }
  .orgList{
    font-size:14px ; width: 100%; margin-top: 20px;
  }
  .contactUpdateLogDetails{
    td{ padding-left: 20px!important; }
  }
  .adressLogDetails{
    padding-left: 50px;
    td{ line-height: 40px; }
    td:nth-child(1){ text-align: right; }
  }

  .contractStop{ line-height: 40px; text-align: center; margin:20px 0;  }
  .contractScan{
    .imgBtn{ overflow: hidden; position: relative; width: 20px; height: 30px; }
    .img{ width:100%; height:100%; left:0; top:0;  opacity:0.5; z-index: 1; position: absolute;  }
    .opacity1{ opacity:1;  }
    .opacity0{ opacity:0; }
    .hisList{ padding: 6px 10px; line-height: 22px; font-size: 14px;  color: #666; }
  }
  .updateCustomerPanel{
    .marLl20{ margin-left:-15px;  }
    .pdL40{ padding-left:40px;  }
    .cusName{ color: $tyBounce-color-green; font-size:16px; padding: 10px 0; font-weight: bold;    }
    .manageTtl td{ background-color:#eee; font-weight: bold; color:#333;    }
  }
  .updateRecords{
    font-size:16px ;
    .recInfo{ line-height: 60px }
  }
  .newMailInfo{
    line-height: 40px;
    td{ padding-left:10px; position: relative; }
    .fa-angle-down{ font-size: 18px; }
    .mask{ position: absolute; width: 100%; height: 100%; z-index: 1 }

  }
  .tipcontractGoods{
    .mar{ margin:10px 0;  }
    .fa{ color:$tyBounce-color-blue; font-weight: bold;   }
  }
  .contractInfo{
    font-size: 15px; line-height: 24px;
    tr.pt td{ padding-top: 10px }
    .box{ border: 1px solid #ccc; border-radius: 2px; height: 30px; background: #fff;   }
    .fa{ color:$tyBounce-color-blue;   }
    .fa-close:hover{ color:$tyBounce-color-red;   }
    .scanPicItem{ padding:0 10px; }

  }
  .cusScan{
    .ttl{
      position: relative; padding-left: 16px;
      &::before{
        border-left:4px solid #bbb; display: inline-block; height: 18px;
        position: absolute;left: 6px;top: 11px; content: "";
      }
    }
  }
  .custormerAddCC{
    line-height:40px; font-size: 14px; padding-left:40px;
    .ttl{
      font-weight: bold; font-size: 13px;
    }
    .marL30{ margin-left:30px;  }
    .marL50{ margin-left:42px;  }
    .radiop{ padding: 0 10px;
      .fa{ color:$tyBounce-color-blue; margin-right: 6px; font-size: 18px; }
    }
    .pa20{ padding:0 20px; display: inline-block; position: relative; top:8px;  }
    .imgList{
      .imgI{
        position: relative; margin-right: 10px;
        .delBtn{ cursor: pointer;
          .fa{ font-size: 20px; opacity:0.2; margin:0 10px; }
          .fa:hover{ opacity:1;  }
        }
        .img, img{width: 90px; height:60px;display: inline-block; background-size: 100%;
          background-position: center center; box-shadow:0 0 3px #ccc ; background-color: #fff;}
      }
    }
  }
  .btnccc{
    text-align: right;
    span{ margin-right: 10px; }
  }
  .line30{
    line-height: 20px; margin-top: 10px; margin-left: 20px; font-size:15px ;
  }
  .custormerLeading{
    font-size: 14px; line-height: 30px;
    .exportStep{ padding-left: 40px; }
    .stepItem { margin-bottom: 20px;  }
    .upload_sect{ width: 352px; }
    #uploadFile{ cursor: default;  }
    .flexRow { margin-left: 50px; display: flex; justify-content: space-between; width: 356px; }
  }
  .receiveInfo{
    .bcc{
      padding-left: 50px; line-height: 40px; cursor: pointer;
      .fa{ color: $tyBounce-color-blue;  }
    }
    .nextcc{
      padding-top: 30px; line-height:26px;
      &>div{ border-top: 1px solid #cdcdcd; padding: 30px 0; font-size: 14px;  }
      .ty-table-control{
        span{ cursor: pointer; padding:3px 6px; margin:2px 5px; border-radius: 3px }
      }
    }
  }
  .addressEditcc{
    line-height: 40px;
    td{ padding-left:10px; position: relative; }
    .fa-angle-down{ font-size: 18px; }
    .mask{ position: absolute; width: 100%; height: 100%; z-index: 1 }
  }
  .chooseCusContactcc{
    line-height:20px;
    .fa{ color: $tyBounce-color-blue; margin-right: 5px;   }
    .table{ background: #fff; padding: 10px; margin:10px;   }
    .clickArea{ cursor: pointer;
      -webkit-user-select: none; /* Safari */
      -moz-user-select: none; /* Firefox */
      -ms-user-select: none; /* IE10+/Edge */
      user-select: none; /* Standard syntax */
    }
    table{ width: 100%; }

  }
  .editContactLianXicc{
    line-height: 30px; font-size: 15px;
    table{ width: 100%;  }
    .imgcc{
      img{ max-width:100px; max-height: 80px; }
    }
    .lix{
      position: relative;
    }
  }
  .useDefinedLabelcc{
    text-align: center; padding: 20px 100px; line-height: 40px;
  }
  .narrPage {
    width: 50%;
  }
  .gapFar{
    margin-top: 70px;
  }
  /*商品录入*/
  .moveBt{
    margin-top: 15px;
  }
}
</style>