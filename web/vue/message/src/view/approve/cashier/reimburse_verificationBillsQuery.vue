<template>
  <div id="reimburseVerificationBillsQuery" class="reimburseQuery" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="个人报销的票据审核" isButton="true" @toggleSubmit="submit"></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div style="padding: 0 4px;">
        <div class="tip tip-success">您可更换查询条件，以得到其他查询结果。</div>
      </div>
      <div class="ui-message">
        <div class="sendContainer">
          <el-form :model="queryForm" ref="queryForm" label-width="70px">
            <div>
              <h4 class="item-header">提交时间</h4>
              <div class="el-form-item">
                <el-radio-group v-model="queryForm.type" size="small">
                  <el-radio label="1" border>近七日</el-radio>
                  <el-radio label="2" border>本月</el-radio>
                  <el-radio label="3" border>自定义</el-radio>
                </el-radio-group>
              </div>
              <div class="el-form-item" v-show="queryForm.type === '3'">
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.beginDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker> -
                <el-date-picker type="date" placeholder="请选择" v-model="queryForm.endDate" style="width: 130px;" size="small" value-format="yyyy-MM-dd"></el-date-picker>
              </div>
            </div>
            <div>
              <h4 class="item-header">审批结果</h4>
              <div class="el-form-item" id="el3">
                <el-radio-group v-model="queryForm.approveStatus" size="small">
                  <br>
                  <el-radio width="150px;" label="1" border>票据在线审批未通过</el-radio>
                  <br>
                  <el-radio width="150px;" label="2" border>审批流程被驳回</el-radio>
                  <br>
                  <el-radio width="150px;" label="3" border>线下票据不一致</el-radio>
                </el-radio-group>
              </div>
            </div>
            <div>
              <h4 class="item-header">申请人</h4>
              <div class="el-form-item">
                <span :class="queryForm.applier === 0?'btn_all btn_active':'btn_all'" v-on:click="chooseApplier(0)">全部</span>
                <span class="chooseApplier" v-on:click="chooseApplier(1)">{{queryForm.applyName}} <i class="el-icon-arrow-right"></i></span>
              </div>
            </div>

          </el-form>
        </div>
      </div>
    </div>
    <TY_AddressBook :rolelist="roleList" :title="选择申请人" v-bind:show.sync="bookVisible" @select_role="setRole"></TY_AddressBook>
  </div>
</template>
<style>
  #el3 .el-radio--small.is-bordered{ width:160px;  }
  .btn_all{
    display: inline-block;
    padding: 5px 20px;
    border-radius: 3px;
    font-size: 12px;
    border: 1px solid #dcdfe6;
    margin-left: 10px;
  }
  .btn_active{
    border: 1px solid #409eff;
    color: #409eff;
  }
  #reimburseApproveQuery .el-radio__input, #reimburseApplyQuery .el-radio__input {
    display: none;
  }
  .chooseApplier{
    margin-left: 110px;
    cursor: pointer;
  }
</style>

<script>
// import { formatDate, filterLeaveType } from '../../js/common'
export default {
  name: 'reimburseVerificationBillsQuery',
  data () {
    return {
      loading: true,
      queryForm: {
        type: '1',
        beginDate: '',
        endDate: '',
        approveStatus: '1',
        feeCat: '',
        secondFeeCat: '',
        billCat: '',
        billCatAll: '0',
        feeCatAll: '0',
        secondFeeCatAll: '0',
        applier: 0,
        applyName: '选择申请人'
      },
      billCat: [],
      feeCat: [],
      secondFeeCatList: [],
      roleList: [],
      bookVisible: false,
      type: 0
    }
  },
  created: function () {
    let that = this
    let type = this.$route.params.type
    this.type = type

    this.axios.get('../../../reimburse/toCode.do')
      .then(function (response) {
        let feeCats = response.data.codeList
        console.log(response.data)
        that.feeCat = feeCats
      })
      .catch(function (error) {
        console.log(error)
      })
    this.axios.get('../../../expense/queryCodeCategory.do')
      .then(function (response) {
        let billCats = response.data.billCats
        console.log(response.data)
        that.billCat = billCats
      })
      .catch(function (error) {
        console.log(error)
      })
    this.$http.post('../../../reimburseWindow/getReimburseApplyUserList.do', { userId: this.sphdSocket.user.userID, userName: '', 'businessType': '32' }, {
      emulateJSON: true
    }).then((response) => {
      that.loading = false
      let data = response.body
      if (data) {
        let data = response.data
        let list = data.data
        that.roleList = list
      } else {
        console.log('加载失败！')
      }
    })
  },
  methods: {
    submit: function () {
      // type 1-近七日，2-本月，3-自定义
      // 自定义时传 beginDate 开始时间，endDate 结束时间
      // approveStatus 2- 批准 3- 驳回
      console.log(JSON.stringify(this.queryForm))
      let approveStatus = Number(this.queryForm.approveStatus)
      let queryParam = {
        approveStatus: approveStatus,
        beginDate: this.queryForm.beginDate,
        endDate: this.queryForm.endDate,
        type: this.queryForm.type,
        applyId: this.queryForm.applier,
        applyUserName: this.queryForm.applyName,
        userId: this.sphdSocket.user.userID
      }
      console.log('搜索值')
      console.log(queryParam)
      localStorage.setItem('query', JSON.stringify(queryParam))
      this.$router.push({
        path: '/reimburseVerificationBillsQueryPer',
        name: 'reimburseVerificationBillsQueryPer',
        params: {
          data: queryParam
        }
      })
    },
    chooseApplier: function (type) {
      if (type === 0) {
        this.queryForm.applier = 0
        this.queryForm.applyName = '请选择申请人'
      } else {
        this.bookVisible = true
      }
    },
    setRole: function (data) {
      this.queryForm.applier = data.userID
      this.queryForm.applyName = data.userName
    }
  }
}
</script>
