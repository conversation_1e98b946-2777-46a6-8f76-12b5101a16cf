package cn.sphd.miners.modules.commodity.service.impl;

import cn.sphd.miners.modules.accountant.util.DateUtil;
import cn.sphd.miners.modules.commodity.dao.*;
import cn.sphd.miners.modules.commodity.entity.PdAssemble;
import cn.sphd.miners.modules.commodity.entity.PdBase;
import cn.sphd.miners.modules.commodity.service.ConstituteService;
import cn.sphd.miners.modules.material.dao.MtBaseDao;
import cn.sphd.miners.modules.material.dao.MtCategoryDao;
import cn.sphd.miners.modules.material.entity.MtBase;
import cn.sphd.miners.modules.material.entity.MtCategory;
import cn.sphd.miners.modules.material.service.UnitService;
import cn.sphd.miners.modules.processManagement.dao.PpmProcessStatDao;
import cn.sphd.miners.modules.processManagement.entity.PpmProcessStat;
import cn.sphd.miners.modules.processManagement.service.ProcessSettingsService;
import cn.sphd.miners.modules.system.entity.User;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.hibernate.Criteria;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

@Service
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class ConstituteServiceImpl implements ConstituteService {
    @Autowired
    PdProductStatDao pdProductStatDao;

    @Autowired
    PdBaseDao pdBaseDao;
    @Autowired
    PdBaseHistoryDao pdBaseHistoryDao;
    @Autowired
    PdCompositionDao pdCompositionDao;
    @Autowired
    MtBaseDao mtBaseDao;
    @Autowired
    MtCategoryDao mtCategoryDao;
    @Autowired
    UnitService unitService;

    @Autowired
    PdAssembleDao pdAssembleDao;
    @Autowired
    ProcessSettingsService processSettingsService;
    @Autowired
    PpmProcessStatDao ppmProcessStatDao;


    @Override
    public Map<String, Object> getConfirmedList(Integer oid,Integer currPage, Integer pageSize) {


        String sql=getListSql();

        sql  =sql+" where oid="+oid+" and enabled=1 and (state=1 or state is null ) and type=1";
        List<Map<String,Object>>list=pdBaseDao.getSession().createSQLQuery(sql).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list();

        return returnAllListMap(list,oid);
    }

    @Override
    public Map<String, Object> getDataNumber(Integer oid) {
        Map<String,Object> r=new HashMap<>();
        r.put("jiagong",pdBaseDao.getListByHQL("from PdBase where oid="+oid+" and enabled=1 and (state=1 or state is null ) and type=1").size());
        r.put("goucheng",pdBaseDao.getListByHQL("from PdBase where oid ="+oid+" and enabled=1 and state='2' and stage ='2' and type=1").size());
        r.put("chaifen",pdBaseDao.getListByHQL("from PdBase where  oid ="+oid+" and enabled='1' and state='2' and stage ='3'").size());


        return r;
    }

    @Override
    public String processingConfirmation(String baseList, String process,User user) {
        JSONArray jsonArray = JSONArray.fromObject(baseList);

        //将新录入的产品添加到相关的统计中，1.251工序1-lyx
        PpmProcessStat ppmProcessStat = processSettingsService.getPpmProcessStat(user.getOid(),ProcessSettingsService.ProcessStatType.product.getIndex(),ProcessSettingsService.ProcessStatAssembly.manufacturing.getIndex());

        for (int i=0;i<jsonArray.size();i++){
            JSONObject obj=jsonArray.getJSONObject(i);
            if("2".equals(process)||"3".equals(process)){
                BigDecimal netWeight=null;
                if(obj.get("netWeight")!=null&&!"null".equals(obj.get("netWeight").toString())&&!"".equals(obj.get("netWeight").toString())){
                    netWeight=new BigDecimal(obj.get("netWeight").toString());
                }

                String weightUnit=null;
                if(obj.get("weightUnit")!=null&&!"null".equals(obj.get("weightUnit").toString())&&!"".equals(obj.get("weightUnit").toString())){
                    weightUnit=obj.get("netWeight").toString();
                }
                pdBaseDao.updateUnitAndProcess(obj.getInt("id"),netWeight,weightUnit,process);

                PdBase p=pdBaseDao.get(obj.getInt("id"));
                if("3".equals(process)){
                    //添加进入材料模块
                    addToMtBase(p,user,"外购成品");
                }

                //将新录入的产品添加到相关的统计中，1.251工序1-lyx
                if (ppmProcessStat!=null){  //生产工序统计有了，那么生产生产统计就有；“委托为外部加工”的产品不进入工序设置中。
                    //判断是否有工序(生产)的统计,没有就添加
                    Integer state = processSettingsService.havingPpmProductionStatList(user,ProcessSettingsService.ProcessStatType.product.getIndex(),p.getId());
                    if (state!=null&&state==1){
                        ppmProcessStat.setTbcAmount(ppmProcessStat.getTbcAmount()+1); //应设置数量
                    }
                }

            }else{
                PdBase p=pdBaseDao.get(obj.getInt("id"));

                p.setProcess(process);
                p.setState("0");
                //p.setComposition("1");
                p.setSplit("0");
                p.setStage("0");
                BigDecimal netWeight=obj.get("netWeight")==null?null:new BigDecimal(obj.get("netWeight").toString());
                String weightUnit=obj.optString("weightUnit");
                if(netWeight!=null&&weightUnit!=null&&!"null".equals(weightUnit)){
                    BigDecimal a=UnitUnit.toMilligram(netWeight,weightUnit);
                    p.setNetWeight(UnitUnit.milligramToBest(a));
                    p.setWeightUnit(UnitUnit.milligramToUnit(a));
                }
                pdBaseDao.update(p);
                addToMtBase(p,user,"外购成品");
            }
        }
        if (ppmProcessStat!=null) {
            ppmProcessStatDao.update(ppmProcessStat);
        }
        return "success";
    }

    @Override
    public Map<String, Object> getCompositionToBeConfirmedList(Integer oid, Integer currPage, Integer pageSize) {

        Object[] p = new Integer[]{};
        String sql=getListSql();

        sql  =sql+" where oid ="+oid+" and enabled=1 and state='2' and stage ='2' and type=1";

        List<Map<String,Object>>list=pdBaseDao.getSession().createSQLQuery(sql).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list();


        return returnAllListMap(list,oid);
    }

    @Override
    public PdBase getPdBase(Integer id) {
        return pdBaseDao.get(id);
    }

    @Override
    public String compositionToBeConfirmed(String baseList, String composition) {
        JSONArray jsonArray = JSONArray.fromObject(baseList);
     ;
        for (int i=0;i<jsonArray.size();i++){
            JSONObject obj=jsonArray.getJSONObject(i);

            BigDecimal netWeight=null;
            if(obj.get("netWeight")!=null&&!"null".equals(obj.get("netWeight").toString())&&!"".equals(obj.get("netWeight").toString())){
                netWeight=new BigDecimal(obj.get("netWeight").toString());
            }

            if("2".equals(composition)){
                pdBaseDao.updateUnitAndComposition(obj.getInt("id"),netWeight,obj.optString("weightUnit"),composition,"3");
            }else if("4".equals(composition)){
                pdBaseDao.updateUnitAndComposition(obj.getInt("id"),netWeight,obj.optString("weightUnit"),composition,"4");
            }else {
                pdBaseDao.updateUnitAndComposition(obj.getInt("id"),netWeight,obj.optString("weightUnit"),composition,"5");
            }

        }

        return "success";
    }

    @Override
    public   Map<String,Object> getSplitList(Integer oid,Integer currPage, Integer pageSize) {
        String sql=getListSql();
        sql  =sql+" where  oid ="+oid+" and enabled='1' and state='2' and stage ='3'";
        List<Map<String,Object>>list=pdBaseDao.getSession().createSQLQuery(sql).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list();
        return returnAllListMap(list,oid);
    }

    @Override
    public String addPdBaseLingJian(PdBase pdBase, Integer pdBaseId,Integer oldId, User user,Integer amount) {
        if(pdBase.getId()==null||"0".equals(pdBase.getId())){
            PdBase p=pdBaseDao.getByHQL("from PdBase where innerSn='"+pdBase.getInnerSn()+"' and oid="+user.getOid());
            if(p!=null){
                return "已有产品或零组件使用该图号";
            }
            if("2".equals(pdBase.getComposition())){
                pdBase.setSplit("1");
            }else{
                pdBase.setSplit("0");
            }
            pdBase.setOid(user.getOid());
            pdBase.setType("2");
            pdBase.setState("0");
            pdBase.setStage("0");
            pdBase.setEnabled("1");
            pdBase.setCreator(user.getUserID());
            pdBase.setCreateName(user.getUserName());
            pdBase.setCreateDate(new Date());
            pdBase.setOperation("1");
            pdBase.setVersionNo(1);
            if("1".equals(pdBase.getProcess())){
                pdBase.setComposition("1");
                pdBase.setSource("1");
            }else{
                pdBase.setSource("2");
            }
            if("".equals(pdBase.getWeightUnit())){
                pdBase.setWeightUnit(null);
            }

            if("".equals(pdBase.getComposition())){
                pdBase.setComposition(null);
            }
            pdBaseDao.save(pdBase);
            pdBaseDao.getSession().flush();
            pdBaseHistoryDao.setHistory(pdBase);

            //给所有上级添加后缀
            if("3".equals(pdBase.getProcess())&&"4".equals(pdBase.getComposition())){
                addToMtBase(pdBase,user,"外购成品");
            }
            if("3".equals(pdBase.getProcess())&&"2".equals(pdBase.getComposition())){
                addToMtBase(pdBase,user,"外购成品");
            }

            if("1".equals(pdBase.getProcess())){
                addToMtBase(pdBase,user,"外购成品");
            }

            if(pdBase.getNetWeight()!=null&&pdBase.getWeightUnit()!=null){
                BigDecimal a=UnitUnit.toMilligram(pdBase.getNetWeight(),pdBase.getWeightUnit());
                pdBase.setNetWeight(UnitUnit.milligramToBest(a));
                pdBase.setWeightUnit(UnitUnit.milligramToUnit(a));
            }

        }
        //先查直接上级
        PdBase parent=pdBaseDao.get(pdBaseId);
        com.alibaba.fastjson.JSONArray jsonArray= com.alibaba.fastjson.JSONArray.parseArray(parent.getJson());
        if(jsonArray==null){
            jsonArray=new com.alibaba.fastjson.JSONArray();
        }

        if(oldId!=null&&!"0".equals(oldId)){
            //先删除老的
            ConstituteUtil.delNode(jsonArray,"id",oldId);
        }
        //添加新的
        com.alibaba.fastjson.JSONObject obj=new com.alibaba.fastjson.JSONObject();
        obj.put("amount",new BigDecimal(amount));
        obj.put("innerSn",pdBase.getInnerSn());
        obj.put("id",pdBase.getId());
        obj.put("date", DateUtil.datefomat(new Date(),1));
        obj.put("gcName",user.getUserName());
        obj.put("name",pdBase.getName());
        obj.put("specifications",pdBase.getSpecifications());
        obj.put("model",pdBase.getModel());
        obj.put("unit",pdBase.getUnit());
        obj.put("process",pdBase.getProcess());
        obj.put("composition",pdBase.getComposition());
        obj.put("netWeight",pdBase.getNetWeight());
        obj.put("weightUnit",pdBase.getWeightUnit());

        ConstituteUtil.addNode(jsonArray,"id",0,0,obj,1);
        parent.setJson(jsonArray.toJSONString());
        parent.setPath(ConstituteUtil.createPath(jsonArray));
        parent.setOperation("8");
        parent.setUpdateDate(new Date());
        parent.setUpdateName(user.getUserName());
        parent.setUpdator(user.getUserID());
        //添加d级历史
        pdBaseHistoryDao.setHistory(parent);

        //查出parent的所有上级
        List<PdBase> list=pdBaseDao.getListByHQL("from PdBase where path like '%:"+parent.getId()+"%'");
        for (PdBase pd:list) {
            com.alibaba.fastjson.JSONArray array= com.alibaba.fastjson.JSONArray.parseArray(pd.getJson());
            if(array==null){
                array=new com.alibaba.fastjson.JSONArray();
            }
            com.alibaba.fastjson.JSONObject parentObj=ConstituteUtil.getNode(array,"id",parent.getId());

            if(parentObj.getJSONArray("children")!=null&&parentObj.getJSONArray("children").size()!=0){
                int level=parentObj.getInteger("level")+1;
                ConstituteUtil.addNode(array,"id",parent.getId(),0,obj,level);
            }else{
                int level=parentObj.getInteger("level")+1;
                ConstituteUtil.addNode(array,"id",parent.getId(),0,obj,level);
            }

            pd.setJson(array.toJSONString());
            pd.setPath(ConstituteUtil.createPath(array));
            pdBaseDao.update(pd);
            //添加所有上级历史
            pdBaseHistoryDao.setHistory(pd);
        }


//
//        PdComposition pc=new PdComposition();
//        pc.setAmount(new BigDecimal(amount));
//        pc.setOrg(user.getOid());
//        pc.setInnerSn(pdBase.getInnerSn());
//        pc.setProduct(pdBase.getId());
//        pc.setParent(pdBaseId);
//        pc.setCreator(user.getUserID());
//        pc.setCreateName(user.getUserName());
//        pc.setCreateDate(new Date());
//        pc.setOperation("1");
//        pc.setVersionNo(1);
//
//        pdCompositionDao.save(pc);

        //重新计算，集合
        jsProAndParts(pdBase.getId(),user);
        if(pdBase.getUnitId()!=null){
            //更新计量单位
            unitService.selectUnit(pdBase.getUnitId(),4);
        }

        //将新录入的零组件添加到相关的统计中，1.251工序1-lyx
        PpmProcessStat ppmProcessStat = processSettingsService.getPpmProcessStat(user.getOid(), ProcessSettingsService.ProcessStatType.spareParts.getIndex(),ProcessSettingsService.ProcessStatAssembly.manufacturing.getIndex());
        if (ppmProcessStat!=null&&!"1".equals(pdBase.getProcess())){  //生产工序统计有了，那么生产生产统计就有。“委托外部加工”的不需要在工序设置中加1.

            //判断是否有工序(生产)的统计,没有就添加
            Integer state = processSettingsService.havingPpmProductionStatList(user,ProcessSettingsService.ProcessStatType.spareParts.getIndex(),pdBase.getId());
            if (state!=null&&state==1){
                ppmProcessStat.setTbcAmount(ppmProcessStat.getTbcAmount()+1); //应设置数量
                ppmProcessStatDao.update(ppmProcessStat);
            }
        }
        return "success";
    }

    @Override
    public Map<String, Object> getPdOrLingJianList(Integer id) {
        String sql="SELECT\n" +
                "p.id AS id,\n" +
                "p.inner_sn as innerSn,\n" +
                "p.name AS name,\n" +
                "IF(p.model='',null,p.model) AS model,\n" +
                "IF(p.specifications='',null,p.specifications) AS specifications,\n" +
                "IF(p.process='',null,p.process) as process,\n" +
                "p.unit AS unit,\n" +
                "p.unit_id AS unitId,\n" +
                "p.create_name AS createName,\n" +
                "p.create_date AS createDate,\n" +
                "p.memo AS memo,\n" +
                "IF(p.weight_unit='',null,p.weight_unit) as weightUnit,\n" +
                "p.net_weight as netWeight,\n" +
                "p.composition as composition,\n" +
                "(select count(0) from t_pd_base pb where pb.path like CONCAT('%1:',p.id,'%') ) num, "+
                "p.split as split\n" +
                "from t_pd_base p\n";


        PdBase pdBase=pdBaseDao.get(id);
        com.alibaba.fastjson.JSONArray array= com.alibaba.fastjson.JSONArray.parseArray(pdBase.getJson());
        if(array==null||array.size()==0){
            return returnAllListMap(new ArrayList(),null);
        }
        String ids=ConstituteUtil.getIds(array,1);
        if(ids==null||ids.length()==0){
            return returnAllListMap(new ArrayList(),null);
        }


        sql+=" where p.id in ("+ids+")";
        List<Map<String,Object>>list=pdBaseDao.getSession().createSQLQuery(sql).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list();


        for (Map<String,Object> p:list) {
            for (int i = 0; i <array.size() ; i++) {
                Integer pid= (Integer) p.get("id");
                if(pid.intValue()==array.getJSONObject(i).getInteger("id").intValue()){
                    p.put("amount",array.getJSONObject(i).get("amount"));
                }
            }
        }

        return returnAllListMap(list,null);
    }

    @Override
    public String splitComplete(Integer id, User user) {
        PdBase pdBase=pdBaseDao.get(id);

//        if(!"1".equals(pdBase.getSplit())){
//            return "无需拆分，或已拆分完毕";
//        }
        String sql="SELECT\n" +
                "p.id AS id,\n" +
                "p.inner_sn as innerSn,\n" +
                "p.name AS name,\n" +
                "IF(p.model='',null,p.model) AS model,\n" +
                "IF(p.specifications='',null,p.specifications) AS specifications,\n" +
                "IF(p.process='',null,p.process) as process,\n" +
                "p.unit AS unit,\n" +
                "p.unit_id AS unitId,\n" +
                "p.create_name AS createName,\n" +
                "p.create_date AS createDate,\n" +
                "p.memo AS memo,\n" +
                "IF(p.weight_unit='',null,p.weight_unit) as weightUnit,\n" +
                "p.net_weight as netWeight,\n" +
                "p.composition as composition,\n" +
                "(select count(0) from t_pd_base pb where pb.path like CONCAT('%1:',p.id,'%') ) num, "+
                "p.split as split\n" +
                "from t_pd_base p\n";



        com.alibaba.fastjson.JSONArray array= com.alibaba.fastjson.JSONArray.parseArray(pdBase.getJson());

        //总重量
        BigDecimal sum=new BigDecimal(0);
        if(array!=null&&array.size()!=0){

            String ids=ConstituteUtil.getIds(array,1);
            if(ids!=null&&ids.length()!=0){
                sql+=" where p.id in ("+ids+")";
                List<Map<String,Object>>list=pdBaseDao.getSession().createSQLQuery(sql).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list();

                for (Map<String,Object> m:list) {
                    if(m.get("split")!=null){
                        String split=m.get("split").toString();
                        if("1".equals(split)){
                            return "success";
                        }
                    }
                }

                //计算单重
                for (Map<String,Object> p:list) {
                    for (int i = 0; i <array.size() ; i++) {
                        Integer pid= (Integer) p.get("id");

                        //获取毫克
                        if(p.get("netWeight")!=null&& p.get("weightUnit")!=null){
                            //毫克
                            BigDecimal amount=UnitUnit.toMilligram(new BigDecimal(p.get("netWeight").toString()),p.get("weightUnit").toString());
                            sum=sum.add(amount.multiply(array.getJSONObject(i).getBigDecimal("amount")));
                        }
                    }
                }
            }
        }




        pdBase.setStage("9");
        pdBase.setState("3");
        pdBase.setSplit("2");
        pdBase.setNetWeight(UnitUnit.milligramToBest(sum));
        pdBase.setWeightUnit(UnitUnit.milligramToUnit(sum));
        pdBase.setVersionNo(pdBase.getVersionNo()+1);
        pdBase.setUpdator(user.getUserID());
        pdBase.setUpdateName(user.getUserName());
        pdBase.setUpdateDate(new Date());
        pdBase.setOperation("8");
        pdBaseDao.update(pdBase);

        pdBaseHistoryDao.setHistory(pdBase);
        return "success";
    }

    @Override
    public String lingJianDelete(Integer id, Integer parentId,User user) {

        //先查直接上级
        PdBase parent=pdBaseDao.get(parentId);
        com.alibaba.fastjson.JSONArray jsonArray= com.alibaba.fastjson.JSONArray.parseArray(parent.getJson());
        if(jsonArray==null){
            jsonArray=new com.alibaba.fastjson.JSONArray();
        }

        //先删除
        ConstituteUtil.delNode(jsonArray,"id",id);


        parent.setJson(jsonArray.toJSONString());
        parent.setPath(ConstituteUtil.createPath(jsonArray));
        parent.setOperation("8");
        parent.setUpdateDate(new Date());
        parent.setUpdateName(user.getUserName());
        parent.setUpdator(user.getUserID());
        //添加上级历史
        pdBaseHistoryDao.setHistory(parent);
        //查出parent的所有上级
        List<PdBase> list=pdBaseDao.getListByHQL("from PdBase where path like '%:"+parent.getId()+",%'");
        for (PdBase pd:list) {
            com.alibaba.fastjson.JSONArray array= com.alibaba.fastjson.JSONArray.parseArray(pd.getJson());
            if(array==null){
                array=new com.alibaba.fastjson.JSONArray();
            }
            ConstituteUtil.delNode(array,"id",id);
            pd.setJson(array.toJSONString());
            pd.setPath(ConstituteUtil.createPath(array));
            pdBaseDao.update(pd);
            //添加所有上级历史
            pdBaseHistoryDao.setHistory(pd);
        }

        //重新计算，集合
        jsProAndParts(id,user);
        return "success";
    }

    @Override
    public List<Map<String, Object>> selectLingJianListByParent(Integer oid,Integer id) {


        String sql="SELECT\n" +
                "p.id AS id,\n" +
                "p.inner_sn as innerSn,\n" +
                "p.name AS name,\n" +
                "IF(p.model='',null,p.model) AS model,\n" +
                "IF(p.specifications='',null,p.specifications) AS specifications,\n" +
                "IF(p.process='',null,p.process) as process,\n" +
                "p.unit AS unit,\n" +
                "p.unit_id AS unitId,\n" +
                "p.memo AS memo,\n" +
                "IF(p.weight_unit='',null,p.weight_unit) as weightUnit,\n" +
                "p.net_weight as netWeight,\n" +
                "p.composition as composition,\n" +
                "p.split as split\n" +
                "from t_pd_base p\n" +
                "where  p.oid="+oid+"  and p.enabled='1' and p.type='2' ";
        PdBase pdBase=pdBaseDao.get(id);
        com.alibaba.fastjson.JSONArray array= com.alibaba.fastjson.JSONArray.parseArray(pdBase.getJson());
        if(array!=null && array.size()!=0){
            String ids =ConstituteUtil.getIds(array);
            if(ids.length()!=0){
                sql+=" and p.id not in("+ids+")";
            }
        }

        return pdBaseDao.getSession().createSQLQuery(sql).setResultTransformer(Criteria.ALIAS_TO_ENTITY_MAP).list();
    }

    private static String getListSql(){

        String sql="SELECT\n" +
                "id AS id,\n" +
                "inner_sn as innerSn,\n" +
                "name AS name,\n" +
                "model AS model,\n" +
                "specifications AS specifications,\n" +
                "unit AS unit,\n" +
                "create_name AS createName,\n" +
                "create_date AS createDate,\n" +
                "update_name AS updateName,\n" +
                "update_date AS updateDate,\n" +
                "IF(weight_unit='',null,weight_unit) as weightUnit,\n" +
                "net_weight as netWeight,\n" +
                "state AS state,\n" +
                "process_dept_name AS processDeptName,\n" +
                "memo as memo\n"+
                "FROM\n" +
                "t_pd_base\n" ;
        return sql;
    }


    private Map<String,Object> returnAllListMap(List list,Integer oid){
        Map<String,Object> map=new HashMap<>();
        map.put("data",list);
        map.put("totalRows",list.size());
        map.put("code",200);



        if(oid!=null){
            Map<String,Object> r=new HashMap<>();
            r.put("jiagong",pdBaseDao.getListByHQL("from PdBase where oid="+oid+" and enabled=1 and (state=1 or state is null ) and type=1").size());
            r.put("goucheng",pdBaseDao.getListByHQL("from PdBase where oid ="+oid+" and enabled=1 and state='2' and stage ='2' and type=1").size());
            r.put("chaifen",pdBaseDao.getListByHQL("from PdBase where  oid ="+oid+" and enabled='1' and state='2' and stage ='3'").size());

            map.put("dataNum",r);
        }

        return map;
    }


    private void addToMtBase(PdBase pdBase,User user,String categoryName){
        MtBase mtBase =new MtBase();

        mtBase.setSource("1");
        mtBase.setVersionNo(1);
        mtBase.setName(pdBase.getName());
        mtBase.setCode(pdBase.getInnerSn());
        mtBase.setUnit(pdBase.getUnit());
        mtBase.setUnitId(pdBase.getUnitId());
        mtBase.setOrg(pdBase.getOid());
        mtBase.setModel(pdBase.getModel());
        mtBase.setSpecifications(pdBase.getSpecifications());
        mtBase.setProduct(pdBase);
        mtBase.setCreator(user.getUserID());
        mtBase.setCreateName(user.getUserName());
        mtBase.setCreateDate(new Date());
        mtBase.setEnabled("1");
        mtBase.setEnabledTime(new Date());
        mtBase.setOrigin("3");
        MtCategory category=null;

        List<MtCategory> mtCategories =getMtCategoryByOidAndPid(user.getOid(), null);

        for (MtCategory m:mtCategories) {
            if(categoryName.equals(m.getName())){

                List<MtCategory> l=getMtCategoriesById(m.getId());
                for (MtCategory m2:l ) {
                    if("待分类".equals(m2.getName())){
                        category=m2;
                    }
                }

            }
        }
        mtBase.setCategory(category);
        mtBaseDao.save(mtBase);

    }

    public List<MtCategory> getMtCategoryByOidAndPid(Integer oid, Integer pid) {
        String hql = " and o.org=" + oid;
        if (pid == null) {
            hql += " and o.level=1";
        } else {
            hql += " and o.parent=" + pid;
        }
        return mtCategoryDao.findCollectionByConditionNoPage(hql, null, null);
    }

    public List<MtCategory> getMtCategoriesById(Integer id) {
        String hql = " and o.parent=" + id;
        List<MtCategory> mtCategories = mtCategoryDao.findCollectionByConditionNoPage(hql, null, null);//得到当前类别
        List<MtCategory> ms = new ArrayList<MtCategory>();//创建结果集
        List<MtCategory> mtCategoryList = this.getMtCategories(mtCategories, ms);
        mtCategories.addAll(mtCategoryList);
        return mtCategories;
    }

    private List<MtCategory> getMtCategories(List<MtCategory> mtCategories, List<MtCategory> ms) {
        List<MtCategory> mtCategoryList = new ArrayList<MtCategory>();
        for (MtCategory m : mtCategories) {
            String hql = " and o.parent=" + m.getId();
            List<MtCategory> mts = new ArrayList<MtCategory>();
            mts = mtCategoryDao.findCollectionByConditionNoPage(hql, null, null);//得到当前类别
            if (mts.size() > 0) {
                mtCategoryList.addAll(mts);
            }
        }
        ms.addAll(mtCategoryList);
        if (mtCategoryList.size() > 0) {//若得到的子级数量大于0，继续往下遍历,否则结束
            getMtCategories(mtCategoryList, ms);
        }
        return ms;
    }


    /**
     * 计算 含有本件的产品组建
     */

    private void jsProAndParts(Integer pdBaseId,User user){

        PdAssemble pdAssemble=pdAssembleDao.getByPdBaseId(pdBaseId);
        if(pdAssemble==null){
            pdAssemble=new PdAssemble();
            pdAssemble.setCreateDate(new Date());
            pdAssemble.setCreateName(user.getUserName());
            pdAssemble.setCreator(user.getUserID());
            pdAssemble.setOrg(user.getOid());
            pdAssemble.setOperation("1");
            pdAssemble.setProduct(pdBaseId);
        }else{
            pdAssemble.setOperation("3");
        }
        pdAssemble.setUpdateDate(new Date());
        pdAssemble.setUpdator(user.getUserID());
        pdAssemble.setUpdateName(user.getUpdateName());
        //直接参与组装的产品的种数
        pdAssemble.setDirectPartProduct(pdBaseDao.directComposition(user.getOid(),pdBaseId,"1"));
        //直接参与组装的组件的种数
        pdAssemble.setDirectPartComposition(pdBaseDao.directComposition(user.getOid(),pdBaseId,"2"));
        //间接参与组装的产品的种数
        pdAssemble.setIndirectPartProduct(pdBaseDao.indirectPartProduct(user.getOid(),pdBaseId));

        //曾经直接参与组装的产品的种数
        pdAssemble.setFormerDirectPartProduct(pdBaseDao.formerdirectPartComposition(user.getOid(),pdBaseId,"1"));
        //曾经直接参与组装的组件的种数
        pdAssemble.setFormerdirectPartComposition(pdBaseDao.formerdirectPartComposition(user.getOid(),pdBaseId,"2"));
        //曾经间接参与组装的产品的种数
        pdAssemble.setFormerIndirectPartProduct(pdBaseDao.formerIndirectPartProduct(user.getOid(),pdBaseId));

        pdAssembleDao.saveOrUpdate(pdAssemble);

    }

}
