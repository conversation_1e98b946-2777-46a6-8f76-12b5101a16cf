package cn.sphd.miners.modules.commodity.service.impl;

import cn.sphd.miners.modules.commodity.dao.PdBaseDao;
import cn.sphd.miners.modules.commodity.dao.PdProcessDao;
import cn.sphd.miners.modules.commodity.entity.PdBase;
import cn.sphd.miners.modules.commodity.entity.PdProcess;
import cn.sphd.miners.modules.commodity.service.PdProcessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Created by Administrator on 2016/10/8.
 */
@Service
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
class PdProcessServiceImpl implements PdProcessService {
    @Autowired
    PdProcessDao pdProcessDao;
    @Autowired
    PdBaseDao pdBaseDao;

    @Override
    public List<PdProcess>  findListPdProcessAndBaseThreeProperty(String innerSn,Integer oid) {
/*

        String sql = "SELECT b.`name` as bname,b.net_weight,b.composition,p.inner_sn,p.`name` as pname,p.cavity_per_model,p.unit_consumption,p.materia_utilization,p.loss_quato,p.rejection_rate_quato,p.memo FROM t_pd_base b INNER JOIN t_pd_process p ON b.id=p.product where b.type<=3 and b.oid="+oid;
*/

        String condition =" and o.product.type<=3 and o.product.oid="+oid;
        if (innerSn!=null&&!"".equals(innerSn) ) {
          /*  sql+=" and p.inner_sn like '%"+innerSn+"%'";*/
            condition += " and o.innerSn like '%" + innerSn + "%'";
        }

           List pdProcessDaoListByHQL =pdProcessDao.findCollectionByConditionNoPage(condition,null,null);

            return pdProcessDaoListByHQL;


    }

    @Override
    public List<PdProcess>  findPdProcessAndBaseDetail(Integer id,Integer oid) {

/*
        String sql="SELECT b.`name` as bname,b.net_weight,p.comp_desc,p.inner_sn,p.`name` as pname,p.cavity_per_model,p.unit_consumption,p.materia_utilization,p.loss_quato,p.rejection_rate_quato,p.craft_instructor,p.process_instructor,p.packaging_instructor FROM t_pd_base b INNER JOIN t_pd_process p ON b.id=p.product where b.type<=3 and b.oid="+oid;
*/

        String condition =" and o.product.type<=3 and o.product.oid="+oid;

        if(id!=null && !"".equals(id)){
            condition+=" and o.id="+id;

        }


        List pdProcess=pdProcessDao.findCollectionByConditionNoPage(condition,null,null);

        return pdProcess;
    }

    @Override
    public void updatePdProcess(PdProcess pdProcess) {
        pdProcessDao.update(pdProcess);

    }

    @Override
    public PdProcess getPdProcessById(Integer id) {
        return pdProcessDao.get(id);
    }

    @Override
    public PdBase getById(Integer id) {
        PdBase pdBase = pdBaseDao.get(id);
        return pdBase;
    }

    @Override
    public List<PdProcess> getProcessByOidInnerSn(Integer oid, String innerSn) {
        String condition = " and o.product.oid = "+oid;
        if (!"".equals(innerSn) && innerSn!=null){
            condition+=" and o.innerSn like '%" +innerSn + "%'";
        }
        List<PdProcess> pdProcessList = pdProcessDao.findCollectionByConditionNoPage(condition,null,null);
        return pdProcessList;
    }

    @Override
    public void savePdProcess(PdProcess pdProcess) {
        pdProcessDao.save(pdProcess);
    }
}
