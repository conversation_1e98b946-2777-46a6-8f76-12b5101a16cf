package cn.sphd.miners.modules.material.controller;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.material.entity.*;
import cn.sphd.miners.modules.material.service.*;
import cn.sphd.miners.modules.system.entity.User;
import io.netty.util.internal.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.*;

/**
 * @ClassName UnitController
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/8/23 17:38
 * @Version 1.0
 */
@Controller
@RequestMapping("/unit")
public class UnitController {
    @Autowired
    UnitService unitService;
    //跳转到计量单位首页
    @RequestMapping("/unitManage.do")
    public String unitIndex() {
        return "/unit/unitManage";
    }

    //计量单位列表接口
    //传值 status 为1，是正在启用的计量单位，为0，是已经停用的列表
    //pageSize 每页条数 currentPageNo 当前页数

    //返回值  pageSize 每页条数 currentPageNo 当前页数  sumPage 总页数 totalResult 总条数
    @ResponseBody
    @RequestMapping("/getUnitList.do")
    public RespListPage getUnitList(User user, Integer status,PageInfo pageInfo) throws IOException {
        RespListPage respListPage =new RespListPage();
        Integer org = user.getOid();
        List<MtUnit> list=unitService.selectUnitList(org,status,pageInfo);
        respListPage.setSumHistory(unitService.selectUnitNumber(org));
        respListPage.setList(list);
        if(pageInfo.getTotalPage()==0)
            pageInfo.setTotalPage(1);
        respListPage.setPageInfo(pageInfo);
        return respListPage;
        }
    //新增计量单位接口
    //name 计量单位名称
    // module 模块代码:0计量单位
                     //1销售管理—通用型商品
                    //2销售管理—专属商品
                    //3技术管理—产品档案
                    //4技术管理—构成管理
                    //5配方管理-配方
                    //6配方管理-材料
                    //7采购-材料录入
                    //8技术管理-材料
                    //12-机器初始设置-控制点单位
                    //返回值  status 1成功，0已经存在，2已被停用
    @ResponseBody
    @RequestMapping("/addUnit.do")
    public RespStatus addUnit(User user,MtUnit mtUnit,Integer module) throws IOException {
        Integer org = user.getOid();
        mtUnit.setOrg(org);
        mtUnit.setCreator(user.getUserID());
        mtUnit.setCreateDate(new Date());
        mtUnit.setCreateName(user.getUserName());
        mtUnit.setOperation("1");
        mtUnit.setVersionNo(1);
        mtUnit.setEnalbed(true);
        RespStatus respStatus=unitService.addUnit(mtUnit,module);
        return respStatus;
    }

    //停、启用计量单位接口
    //传值 type 为1启用，为0停用,id
    //返回值  启用的话status 1成功
    //        停用时 1时成功，0是正在使用，无法停用
    @ResponseBody
    @RequestMapping("/updateUnit.do")
    public RespStatus updateUnit( User user,Integer type,Integer id) throws IOException {
        RespStatus respStatus = new RespStatus();
        Integer org = user.getOid();
        MtUnit mtUnit=unitService.selectMtUnit(id);
        mtUnit.setId(id);
        mtUnit.setOrg(org);
        mtUnit.setUpdator(user.getUserID());
        mtUnit.setUpdateDate(new Date());
        mtUnit.setUpdateName(user.getUserName());
        if(type==1)
        {
            mtUnit.setEnalbed(true);
            mtUnit.setOperation("3");
        }
        else if (type==0)
        {
            mtUnit.setEnalbed(false);
            mtUnit.setOperation("2");
        }
        int status=unitService.updateUnit(mtUnit);
        respStatus.setStatus(status);
        return respStatus;
    }
    //判断是否可以停用接口
    //id
    //返回值 1时可以被停用，0是正在使用，无法停用
    @ResponseBody
    @RequestMapping("/stopUnitEnter.do")
    public RespStatus stopUnitEnter(Integer id) throws IOException {
        RespStatus respStatus = new RespStatus();
        int status=unitService.stopUnitEnter(id);
        respStatus.setStatus(status);
        return respStatus;
    }


    /**
     *
     *  根据模板获取计量单位
     * @param
     * @return
     * @throws IOException
     */
    @ResponseBody
    @RequestMapping("/selectUnitListForModule.do")
    public RespListPage selectUnitListForModule(User user, Integer module) {
        RespListPage respListPage =new RespListPage();
//        Map<String,Object> map=new HashMap<>();
        Integer org = user.getOid();
//        map.put("list",unitService.selectUnitListForModule(org,module));
//        map.put("code",200);
        respListPage.setList(unitService.selectUnitListForModule(org,module));
        return respListPage;
    }
//    @ResponseBody
//    @RequestMapping("/testUnit.do")
//    public void testUnit( HttpSession session) throws IOException {
//        Integer unitId=2;
//        Integer module=1;
//        Integer org=2621;
//        unitService.selectUnit(unitId,module);
//        //org  机构id，module模块代码
//        List<MtUnit> mtUnits= unitService.selectUnitListForModule(org,module);
//        int x=0;
//    }
}
