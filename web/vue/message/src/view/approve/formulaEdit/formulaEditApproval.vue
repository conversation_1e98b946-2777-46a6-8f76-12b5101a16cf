<template>
  <div id="formulaEditApproval">
    <TY_NavTop title="配方修改" isSearch="true" @toggleList="search"></TY_NavTop>
    <div class="container">
      <div class="blue">下列申请有待审批</div>
      <div>
        <a class="ui-cell" v-on:click="jump(item.id)" v-for="(item, index) in list" v-bind:key="index">
          <div class="Item">
            <div>{{item.description}}</div>
            <div>{{item.userName}} {{item.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
          </div>
          <div class="ui-cell__ft"></div>
        </a>
      </div>
    </div>
  </div>
</template>
<style lang="less">
  #formulaEditApproval{
    .blue{ background:#e0eefb; padding:5px 15px; }
    .container>div>a{
      background: #fff;
      &:nth-child(odd){ background:#f3f3f3;  }
    }
    .Item{
      width:100%;
      div:nth-child(2){ text-align:right;  }
    }
  }
</style>
<script>
var that
export default {
  name: 'formulaEditApproval',
  data () {
    return {
      list: [],
      loading: true,
      listenersUid: []
    }
  },
  created () {
    that = this
    this.listenersUid = [
      this.sphdSocket.subscribe('formulaEditApprovalList', function (data) {
        let listData = JSON.parse(data)
        that.list = listData.list
        console.log('formulaEditApprovalList:')
        console.log(listData)
      }),
      this.sphdSocket.subscribe('formulaEditApprovalList', function (data) {
        console.log('formulaEditApprovalList 返回值user:')
        let res = JSON.parse(data)
        console.log(res)
        let formula = res.ap
        let operate = Number(res.operate)
        if (operate > 0) {
          that.list.unshift(formula)
        } else if (operate < 0) {
          let fid = formula.id
          let _index = -1
          that.list.forEach(function (item, index) {
            if (Number(item.id) === Number(fid)) {
              _index = index
            }
          })
          if (_index > -1) {
            that.list.splice(_index, 1)
          }
        }
      }, null, 'user')
    ]
    let userId = this.sphdSocket.user.userID
    let session = this.sphdSocket.sessionid
    console.log(userId)
    console.log(session)
    let json = { userId: userId, session: session }
    console.log(JSON.stringify(json))
    this.sphdSocket.send('formulaEditApprovalList', json)
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    jump: function (id) {
      this.$router.push({
        path: '/formulaEditApprovalDetail/' + id
      })
    },
    search: function () {
      this.$router.push({
        path: `/formulaEditSearch/2`
      })
    }
  }
}
</script>
