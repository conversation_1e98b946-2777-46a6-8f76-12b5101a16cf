package cn.sphd.miners.modules.finance.entity;

import cn.sphd.miners.modules.system.entity.Organization;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * Created by Administrator on 2015-12-01.
 */
@Entity
@Table(name="t_finance_account")
public class FinanceAccount implements Serializable,Comparable<Object>{
    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="account_type"   , nullable=true , unique=false)
    private Integer accountType;// 1-现金，2-银行,3-其他

    @Column(name="account"  , length=100 , nullable=true , unique=false)
    private String account;  //帐户

    @Column(name="sub_acount"  , length=100 , nullable=true , unique=false)
    private String subAcount;

    @Column(name="previous_balance"   , nullable=true , unique=false)
    private BigDecimal previousBalance;   //上期余额

    @Column(name="balance"   , nullable=true , unique=false)
    private BigDecimal balance;  //本期余额

    @Column(name="summary"  , length=255 , nullable=true , unique=false)
    private String summary;   //摘要

    @Column(name="credit"   , nullable=true , unique=false)
    private BigDecimal credit;  //本期借、收入

    @Column(name="debit"   , nullable=true , unique=false)
    private BigDecimal debit;   //本期贷、支出

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;   //说明

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;   //修改时间

    @Column(name="approve_Item"   , nullable=true , unique=false)
    private Integer approveItem;   //申批项目

    @Column(name="approve_status"  , length=1 , nullable=true , unique=false)
    private String approveStatus;   //审批状态：0-未申请 ，1-申请提交，2-通过审核，3-否决审核

    @Column(name="approve_level"   , nullable=true , unique=false)
    private Integer approveLevel;  //审批次级

    @Column(name="auditor"   , nullable=true , unique=false)
    private Integer auditor;   //审批者ID

    @Column(name="auditor_name"  , length=100 , nullable=true , unique=false)
    private String auditorName;   //审批者

    @Column(name="audit_date"   , nullable=true , unique=false)
    private Date auditDate;   //审批日期

    @Column(name="operation"  , length=100 , nullable=true , unique=false)
    private String operation;   //(是否基本户  基本户 非基本户 非对公户 此内容已换为isBasic)

    @Column(name="apply_memo"  , length=255 , nullable=true , unique=false)
    private String applyMemo;   //申请备注

    @Column(name="approve_memo"  , length=255 , nullable=true , unique=false)
    private String approveMemo;   //审批备注

    @Column(name="message_id"   , nullable=true , unique=false)
    private Integer messageId;  //消息ID

    @Column
    private Integer accountStatus;//账户状态 0:关闭 1：正常

    @Column(name = "illustrate", length=255 , nullable=true , unique=false)
    private String illustrate;//操作说明

    @Column(name = "delete_state", nullable=true , unique=false)
    private Integer deleteState;//删除状态  1-删除（不显示）

    @Column(name = "handle_reply" , length=255 , nullable=true , unique=false)
    private String handleReply;//处理回复

    @Column(name = "initial_amount",nullable=true , unique=false)
    private BigDecimal initialAmount;//初始金额

    @Column(name="is_public"  , length=10 , nullable=true , unique=false)
    private String isPublic;   //是否为对公帐号:1-是 0-否

    private String bankName;//开户行(银行名称)

    private String name;//账户名称

    private Date endDate;//截止时间 (暂时存账户开启或关闭的最后操作时间)

    @Column(name = "cashable",length=1, nullable=true , unique=false)
    private Integer cashable; //是否可取现  1：可取现  2：不可取现

    @Column(name = "update_content" , length=255 , nullable=true , unique=false)
    private String updateContent; //修改说明

    @Column(name = "subject", length = 16,nullable=true , unique=false)
    private String subject; //对应会计科目

    @Column(name="carried_forward_date"   , nullable=true , unique=false)
    private Date carriedForwardDate;//最近结转日期

    @Column(name = "is_basic",nullable=true , unique=false)
    private  Integer isBasic;//是否是 基本户 true-是，false-否

    @JsonIgnore
    @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="org", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private Organization org;

    @Column(name="org"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer org_;

    @JsonIgnore
    @JSONField(serialize = false)
    @OneToMany(targetEntity=FinanceAccountHistory.class, fetch= FetchType.LAZY, mappedBy="accountId", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<FinanceAccountHistory> financeAccountHistories = new HashSet<FinanceAccountHistory>();

//    @JsonIgnore
//    @JSONField(serialize = false)
//    @OneToMany(targetEntity=AccountPeriod.class, fetch= FetchType.LAZY, mappedBy="accountId", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
//    private Set<AccountPeriod> accountPeriodHashSet = new HashSet<AccountPeriod>();

    @JsonIgnore
    @JSONField(serialize = false)
    @OneToMany(targetEntity=AccountDetail.class, fetch= FetchType.LAZY, mappedBy="accountId", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<AccountDetail> accountDetailHashSet = new HashSet<AccountDetail>();

    //与支票明细表
    @JsonIgnore
    @JSONField(serialize = false)
    @OneToMany(targetEntity=FinanceChequeDetail.class, fetch= FetchType.LAZY, mappedBy="accountId", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<FinanceChequeDetail> financeChequeDetailHashSet = new HashSet<FinanceChequeDetail>();

    //与账户启用记录表
    @JsonIgnore
    @JSONField(serialize = false)
    @OneToMany(targetEntity=FinanceAccountRecord.class, fetch= FetchType.LAZY, mappedBy="financeAccount", cascade= CascadeType.REMOVE)//, cascade=CascadeType.ALL)
    private Set<FinanceAccountRecord> financeAccountRecordHashSet = new HashSet<FinanceAccountRecord>();

    @Transient
    private String orgName;  //机构名称

    public String getUpdateContent() {
        return updateContent;
    }

    public void setUpdateContent(String updateContent) {
        this.updateContent = updateContent;
    }

    public Integer getCashable() {
        return cashable;
    }

    public void setCashable(Integer cashable) {
        this.cashable = cashable;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getAccountType() {
        return accountType;
    }

    public void setAccountType(Integer accountType) {
        this.accountType = accountType;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getSubAcount() {
        return subAcount;
    }

    public void setSubAcount(String subAcount) {
        this.subAcount = subAcount;
    }

    public BigDecimal getPreviousBalance() {
        return previousBalance;
    }

    public void setPreviousBalance(BigDecimal previousBalance) {
        this.previousBalance = previousBalance;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public BigDecimal getCredit() {
        return credit;
    }

    public void setCredit(BigDecimal credit) {
        this.credit = credit;
    }

    public BigDecimal getDebit() {
        return debit;
    }

    public void setDebit(BigDecimal debit) {
        this.debit = debit;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Integer getApproveItem() {
        return approveItem;
    }

    public void setApproveItem(Integer approveItem) {
        this.approveItem = approveItem;
    }

    public String getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(String approveStatus) {
        this.approveStatus = approveStatus;
    }

    public Integer getApproveLevel() {
        return approveLevel;
    }

    public void setApproveLevel(Integer approveLevel) {
        this.approveLevel = approveLevel;
    }

    public Integer getAuditor() {
        return auditor;
    }

    public void setAuditor(Integer auditor) {
        this.auditor = auditor;
    }

    public String getAuditorName() {
        return auditorName;
    }

    public void setAuditorName(String auditorName) {
        this.auditorName = auditorName;
    }

    public Date getAuditDate() {
        return auditDate;
    }

    public void setAuditDate(Date auditDate) {
        this.auditDate = auditDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getApplyMemo() {
        return applyMemo;
    }

    public void setApplyMemo(String applyMemo) {
        this.applyMemo = applyMemo;
    }

    public String getApproveMemo() {
        return approveMemo;
    }

    public void setApproveMemo(String approveMemo) {
        this.approveMemo = approveMemo;
    }

    public Integer getMessageId() {
        return messageId;
    }

    public void setMessageId(Integer messageId) {
        this.messageId = messageId;
    }

    public Organization getOrg() {
        return org;
    }

    public void setOrg(Organization org) {
        this.org = org;
    }

    public Integer getOrg_() {
        return org_;
    }

    public void setOrg_(Integer org_) {
        this.org_ = org_;
    }

    public Integer getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(Integer accountStatus) {
        this.accountStatus = accountStatus;
    }

    public Set<FinanceAccountHistory> getFinanceAccountHistories() {
        return financeAccountHistories;
    }

    public void setFinanceAccountHistories(Set<FinanceAccountHistory> financeAccountHistories) {
        this.financeAccountHistories = financeAccountHistories;
    }

    @Override
    public int compareTo(Object o) {
        FinanceAccount st=(FinanceAccount) o;
        //根据id排序
        Integer stu0Id=id;
        Integer stu1Id=st.id;
        return stu0Id>stu1Id?1:(stu0Id==stu1Id?0:-1);
    }

    public String getBankName() {
        return bankName;
    }

    public String getName() {
        return name;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getIllustrate() {
        return illustrate;
    }

    public void setIllustrate(String illustrate) {
        this.illustrate = illustrate;
    }

    public Integer getDeleteState() {
        return deleteState;
    }

    public void setDeleteState(Integer deleteState) {
        this.deleteState = deleteState;
    }

    public String getHandleReply() {
        return handleReply;
    }

    public void setHandleReply(String handleReply) {
        this.handleReply = handleReply;
    }

    public BigDecimal getInitialAmount() {
        return initialAmount;
    }

    public void setInitialAmount(BigDecimal initialAmount) {
        this.initialAmount = initialAmount;
    }

//    public Set<AccountPeriod> getAccountPeriodHashSet() {
//        return accountPeriodHashSet;
//    }
//
//    public void setAccountPeriodHashSet(Set<AccountPeriod> accountPeriodHashSet) {
//        this.accountPeriodHashSet = accountPeriodHashSet;
//    }

    public Set<AccountDetail> getAccountDetailHashSet() {
        return accountDetailHashSet;
    }

    public void setAccountDetailHashSet(Set<AccountDetail> accountDetailHashSet) {
        this.accountDetailHashSet = accountDetailHashSet;
    }

    public Set<FinanceChequeDetail> getFinanceChequeDetailHashSet() {
        return financeChequeDetailHashSet;
    }

    public void setFinanceChequeDetailHashSet(Set<FinanceChequeDetail> financeChequeDetailHashSet) {
        this.financeChequeDetailHashSet = financeChequeDetailHashSet;
    }

    public String getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(String isPublic) {
        this.isPublic = isPublic;
    }

    public Set<FinanceAccountRecord> getFinanceAccountRecordHashSet() {
        return financeAccountRecordHashSet;
    }

    public void setFinanceAccountRecordHashSet(Set<FinanceAccountRecord> financeAccountRecordHashSet) {
        this.financeAccountRecordHashSet = financeAccountRecordHashSet;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public Date getCarriedForwardDate() {
        return carriedForwardDate;
    }

    public void setCarriedForwardDate(Date carriedForwardDate) {
        this.carriedForwardDate = carriedForwardDate;
    }

    public Integer getIsBasic() {
        return isBasic;
    }

    public void setIsBasic(Integer isBasic) {
        this.isBasic = isBasic;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }
}
