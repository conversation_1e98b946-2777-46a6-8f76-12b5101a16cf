package cn.sphd.miners.modules.commodity.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.commodity.dao.PdFormulaDao;
import cn.sphd.miners.modules.commodity.entity.PdFormula;
import org.springframework.stereotype.Repository;


import java.io.Serializable;
@Repository
public class PdFormulaDaoImpl  extends BaseDao<PdFormula, Serializable> implements PdFormulaDao {


}
