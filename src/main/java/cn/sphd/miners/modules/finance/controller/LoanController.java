package cn.sphd.miners.modules.finance.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.utils.NewDateUtils;
import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.finance.entity.FinanceReturnHistory;
import cn.sphd.miners.modules.finance.service.*;
import cn.sphd.miners.modules.system.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by lyx on 2018/11/30.
 * 1.55常规借款
 */
@Controller
@RequestMapping("/loan")
public class LoanController {
    @Autowired
    LoanService loanService;
    @Autowired
    DataService dataService;
    @Autowired
    FinanceChequeService financeChequeService;
    @Autowired
    FinanceReturnService financeReturnService;

    /**
     *<AUTHOR>
     *@date 2018/11/27 13:45
     *调转常规借款页面
    */
    @RequestMapping("/toLoanPage.do")
    public String toInvoiceSettingPage(){
        return "/ConventionalBorrowing/ConventionalBorrowing";
    }

    /**
     *<AUTHOR>
     *@date 2018/12/20 10:23
     *借款收入(财务的新增借款收入  借款中只用service方法)
    */
    @ResponseBody
    @RequestMapping("/addLoanCredit.do")
    public void addLoanCredit(Integer userId,Integer business,String method, Double money, Date receiveAccountDate, Integer accountId, String returnNo, Date expireDate, String originalCorp, String bankName, String memo, User user){
//        loanService.addLoanCredit(userId,business,method,money,receiveAccountDate,accountId,returnNo,expireDate,originalCorp,bankName,memo,session);
    }

    /**
     *<AUTHOR>
     *@date 2019/12/24 10:31
     *修改借款收入信息(借款中只用service方法)
    */
    @ResponseBody
    @RequestMapping("/updateLoanCredit.do")
    public void updateLoanCredit(Integer userId,Integer business,Integer businessHistory,Integer previousId,String method, Double money, Date receiveAccountDate, Integer accountId, String returnNo, Date expireDate, String originalCorp, String bankName, String memo,User user){
        loanService.updateLoanCredit(userId,business,businessHistory,previousId,method,money,receiveAccountDate,accountId,returnNo,expireDate,originalCorp,bankName,memo,null,null,new Date(),1,null,null);
    }

    /**
     *<AUTHOR>
     *@date 2018/12/27 9:44
     *借款的付款录入(借款中只用service方法)
    */
    @ResponseBody
    @RequestMapping("/loanPayEntry.do")
    public void loanPayEntry(Integer userId,Integer business,String method, Double money,String withinOrAbroad, Integer checkId,Integer accountId,Date receiveDate, Date expireDate,String operator, String receiver,Date paymentDate){
        loanService.loanPayEntry(userId,business,method,money,withinOrAbroad,checkId,accountId,receiveDate,expireDate,operator,receiver,paymentDate,null,null);
    }

    /**
     *<AUTHOR>
     *@date 2018/12/27 9:44
     *修改借款的付款录入(借款中只用service方法)
     */
    @ResponseBody
    @RequestMapping("/updateLoanPayEntry.do")
    public void updateLoanPayEntry(Integer userId,Integer payId,Integer payHistoryId,Integer previousId,String methodOld,String method, Double money,String withinOrAbroad, Integer checkId,Integer accountId,Date receiveDate, Date expireDate,String operator, String receiver,Date paymentDate){
        loanService.updateLoanPayEntry(userId,payId,payHistoryId,previousId,methodOld,method,money,withinOrAbroad,checkId,accountId,receiveDate,expireDate,operator,receiver,paymentDate,null,null);
    }
    
    /**
     *<AUTHOR>
     *@date 2018/12/28 15:24
     *查询某一条借款收入/支出的详情(借款中只用service方法)
    */
    @ResponseBody
    @RequestMapping("/getLoanCreditDetail.do")
    public void getLoanCreditDetail(Integer business,String method,Integer type){
        loanService.getLoanCreditDetail(business,method,type);
    }

    /**
     *<AUTHOR>
     *@date 2019/1/4 10:48
     *查看某一条借款还款的修改记录详情（财务借款收入/支出的修改详情）
     * businessType 0101-常规借款借入,0102-常规借款还款,0103-常规借款借出，0104-常规借款收款
    */
    @ResponseBody
    @RequestMapping("/getLoanPayUpdateDetail.do")
    public void getLoanPayUpdateDetail(Integer businessHistory,String method,String businessType,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        map = loanService.getLoanPayUpdateDetail(businessHistory,method,businessType,map);
        ObjectToJson.objectToJson1(map,new String[]{"billDetail","chequeReg","accountId","financeAccountBillHashSet","org","detailId"},response);
    }

    /**
     *<AUTHOR>
     *@date 2019/1/9 11:12
     * 在支出中根据支出方式不同，显示的内容不同
     * 返回各支出方式的支票、汇票、银行
     * type 1-转账支票的内部支票 2-转账支票的外部支票 3-承兑汇票 4-银行转账
     */
    @ResponseBody
    @RequestMapping("/chooseChequeOrBank.do")
    public void chooseChequeOrBank(User user,Integer type, HttpServletResponse response) throws IOException {
        Map<String, Object> map = new HashMap<>();
        if (user!=null && type!=null) {
            financeReturnService.chooseChequeOrBank(user.getOid(),type, map);
        }
        ObjectToJson.objectToJson1(map, new String[]{"org", "financeAccountHistories", "accountPeriodHashSet", "accountDetailHashSet", "financeChequeDetailHashSet","financeAccountRecordHashSet"}, response);
    }


    /**
     *测试
     */
    @ResponseBody
    @RequestMapping("/test.do")
    public JsonResult test(User user) {
        FinanceReturnHistory financeReturnHistory = loanService.getReturnHistoryById(472);
        return new JsonResult(1,financeReturnHistory);
    }
}
