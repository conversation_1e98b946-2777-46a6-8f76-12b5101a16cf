package cn.sphd.miners.modules.orderReview.controller;

import java.util.List;

import cn.sphd.miners.modules.mci.utils.AjaxResult;
import cn.sphd.miners.modules.orderReview.entity.TSlOrdersItemReviewHistory;
import cn.sphd.miners.modules.orderReview.service.ITSlOrdersItemReviewHistoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static cn.sphd.miners.modules.mci.utils.AjaxResult.success;
import static cn.sphd.miners.modules.mci.utils.AjaxResult.toAjax;

/**
 * Controller
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
@Controller
@RequestMapping("/orders/item/history")
public class SlOrdersItemReviewHistoryController
{
    @Autowired
    private ITSlOrdersItemReviewHistoryService tSlOrdersItemReviewHistoryService;

    /**
     * 查询列表
     */
    @GetMapping("/list")
    public List list(TSlOrdersItemReviewHistory tSlOrdersItemReviewHistory)
    {
        List<TSlOrdersItemReviewHistory> list = tSlOrdersItemReviewHistoryService.selectTSlOrdersItemReviewHistoryList(tSlOrdersItemReviewHistory);
        return list;
    }


    /**
     * 获取详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(tSlOrdersItemReviewHistoryService.selectTSlOrdersItemReviewHistoryById(id));
    }

    /**
     * 新增
     */
    @PostMapping
    public AjaxResult add(@RequestBody TSlOrdersItemReviewHistory tSlOrdersItemReviewHistory)
    {
        return toAjax(tSlOrdersItemReviewHistoryService.insertTSlOrdersItemReviewHistory(tSlOrdersItemReviewHistory));
    }

    /**
     * 修改
     */
    @PutMapping
    public AjaxResult edit(@RequestBody TSlOrdersItemReviewHistory tSlOrdersItemReviewHistory)
    {
        return toAjax(tSlOrdersItemReviewHistoryService.updateTSlOrdersItemReviewHistory(tSlOrdersItemReviewHistory));
    }

    /**
     * 删除
     */
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(tSlOrdersItemReviewHistoryService.deleteTSlOrdersItemReviewHistoryByIds(ids));
    }
}
