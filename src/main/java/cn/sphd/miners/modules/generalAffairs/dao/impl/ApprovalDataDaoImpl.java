package cn.sphd.miners.modules.generalAffairs.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.generalAffairs.dao.ApprovalDataDao;
import cn.sphd.miners.modules.generalAffairs.dao.FinanceAssetsReceiveDao;
import cn.sphd.miners.modules.generalAffairs.entity.ApprovalData;
import cn.sphd.miners.modules.generalAffairs.entity.FinanceAssetsReceive;
import org.springframework.stereotype.Repository;

import java.io.Serializable;

/**
 * @ClassName ApprovalDataDaoImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/1/9 10:11
 * @Version 1.0
 */
@Repository
public class ApprovalDataDaoImpl extends BaseDao<ApprovalData,Serializable> implements ApprovalDataDao {
}
