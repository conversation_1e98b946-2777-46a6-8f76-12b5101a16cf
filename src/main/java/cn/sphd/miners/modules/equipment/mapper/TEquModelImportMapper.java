package cn.sphd.miners.modules.equipment.mapper;

import java.util.List;

import cn.sphd.miners.modules.equipment.entity.TEquModelImport;
import org.springframework.stereotype.Component;

/**
 * Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-03-30
 */
@Component
public interface TEquModelImportMapper 
{
    /**
     * 查询
     * 
     * @param id 主键
     * @return 
     */
    public TEquModelImport selectTEquModelImportById(Long id);

    /**
     * 查询列表
     * 
     * @param tEquModelImport 
     * @return 集合
     */
    public List<TEquModelImport> selectTEquModelImportList(TEquModelImport tEquModelImport);

    /**
     * 新增
     * 
     * @param tEquModelImport 
     * @return 结果
     */
    public int insertTEquModelImport(TEquModelImport tEquModelImport);

    /**
     * 修改
     * 
     * @param tEquModelImport 
     * @return 结果
     */
    public int updateTEquModelImport(TEquModelImport tEquModelImport);

    /**
     * 删除
     * 
     * @param id 主键
     * @return 结果
     */
    public int deleteTEquModelImportById(Long id);

    /**
     * 批量删除
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTEquModelImportByIds(String[] ids);
}
