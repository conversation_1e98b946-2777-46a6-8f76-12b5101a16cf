package cn.sphd.miners.modules.commodity.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.commodity.entity.PdPackagingStructure;
import cn.sphd.miners.modules.commodity.entity.PdPackagingStructureHistory;

import java.io.Serializable;

public interface PdPackagingStructureHistoryDao extends IBaseDao<PdPackagingStructureHistory, Serializable> {

    //添加历史记录
    PdPackagingStructureHistory insert(PdPackagingStructure pdPackagingStructure,Integer infoHistoryId);
}
