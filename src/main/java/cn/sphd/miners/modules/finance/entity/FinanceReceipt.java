package cn.sphd.miners.modules.finance.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created by lyx on 2022/11/18
 * 财务_收款表  1.231差额处理
 */
@Entity
@Table(name="t_finance_receipt")
public class FinanceReceipt implements Serializable{

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="org"   , nullable=true , unique=false)
    private Integer org;   //机构ID

    @Column(name="business_type"  , length=20 , nullable=true , unique=false)
    private String businessType;  //业务类型:sale-销售,po_loan-差额借款,payment-支付

    @Column(name="business"   , nullable=true , unique=false)
    private Integer business;   //业务ID

    @Column(name="purpose"  , length=255 , nullable=true , unique=false)
    private String purpose;   //理由

    @Column(name="amount"  , nullable=true , unique=false)
    private BigDecimal amount;   //金额

    @Column(name = "method",length = 1,nullable = true,unique = false)
    private String method;//支付方式:1-现金,2-现金支票,3-转帐支票(本公司),4-承兑汇票,5-银行转帐与帐务表保持一致

    @Column(name="fact_date"  , nullable=true , unique=false)
    private Date factDate;   //实际时间

    @Column(name="summary"  , length=255 , nullable=true , unique=false)
    private String summary;   //摘要

    @Column(name="account_id"   , nullable=true , unique=false)
    private Integer accountId;   //帐户ID

    @Column(name="account_detail"   , nullable=true , unique=false)
    private Integer accountDetail;   //帐户明细ID

    @Column(name="account_bill"   , nullable=true , unique=false)
    private Integer accountBill;   //帐户票据ID

    @Column(name="account_bill_history"   , nullable=true , unique=false)
    private Integer accountBillHistory;   //帐户票据历史ID,多个以逗号分隔

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;   //备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="operation"  , length=1 , nullable=true , unique=false)
    private String operation;  //操作:1-增,2-删,3-改

    @Column(name="previous_id"  , nullable=true , unique=false)
    private Integer previousId;  //修改前记录ID

    @Column(name="version_no"  , nullable=true , unique=false)
    private Integer versionNo;  //版本号,每次修改+1

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public Integer getBusiness() {
        return business;
    }

    public void setBusiness(Integer business) {
        this.business = business;
    }

    public String getPurpose() {
        return purpose;
    }

    public void setPurpose(String purpose) {
        this.purpose = purpose;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public Date getFactDate() {
        return factDate;
    }

    public void setFactDate(Date factDate) {
        this.factDate = factDate;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public Integer getAccountDetail() {
        return accountDetail;
    }

    public void setAccountDetail(Integer accountDetail) {
        this.accountDetail = accountDetail;
    }

    public Integer getAccountBill() {
        return accountBill;
    }

    public void setAccountBill(Integer accountBill) {
        this.accountBill = accountBill;
    }

    public Integer getAccountBillHistory() {
        return accountBillHistory;
    }

    public void setAccountBillHistory(Integer accountBillHistory) {
        this.accountBillHistory = accountBillHistory;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public Integer getPreviousId() {
        return previousId;
    }

    public void setPreviousId(Integer previousId) {
        this.previousId = previousId;
    }

    public Integer getVersionNo() {
        return versionNo;
    }

    public void setVersionNo(Integer versionNo) {
        this.versionNo = versionNo;
    }
}
