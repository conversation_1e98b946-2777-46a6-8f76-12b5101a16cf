<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.sphd.miners.modules.accountant.mapper.ReimburseRelevanceHistoryMapper" >

  <sql id="Base_Column_List" >
    id, subject_relevance AS subjectRelevance, org, module, subject_id AS subjectId, subject, fee_cat AS feeCat, sub_fee_cat AS subFeeCat, code,
    sub_code AS subCode, name, enabled, memo, creator, create_name AS createName, create_date AS createDate, updator, update_name AS updateName,
    update_date AS updateDate, operation, previous_id AS previousId, version_no AS versionNo
  </sql>

  <select id="getSingle" parameterType="TAccountantSubjectRelevanceHistory" >
    select 
    <include refid="Base_Column_List" />
    from t_accountant_subject_relevance_history
    where id = #{id,jdbcType=INTEGER}
  </select>

  <select id="getCategoryRelevanceHistory"
          resultType="TAccountantSubjectRelevanceHistory">

    select
    <include refid="Base_Column_List" />
    from t_accountant_subject_relevance_history
    where org = #{org} and code = #{code} and fee_cat = #{feeCat}

  </select>

  <delete id="delete" parameterType="TAccountantSubjectRelevanceHistory" >
    delete from t_accountant_subject_relevance_history
    where id = #{id,jdbcType=INTEGER}
  </delete>

    <delete id="deleteByOrg">
      delete from t_accountant_subject_relevance_history
      where org = #{org}
    </delete>


    <insert id="insert" parameterType="TAccountantSubjectRelevanceHistory" useGeneratedKeys="true" keyProperty="id">
    insert into t_accountant_subject_relevance_history
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="subjectRelevance != null" >
        subject_relevance,
      </if>
      <if test="org != null" >
        org,
      </if>
      <if test="module != null" >
        module,
      </if>
      <if test="subjectId != null" >
        subject_id,
      </if>
      <if test="subject != null" >
        subject,
      </if>
      <if test="feeCat != null" >
        fee_cat,
      </if>
      <if test="subFeeCat != null" >
        sub_fee_cat,
      </if>
      <if test="code != null" >
        code,
      </if>
      <if test="subCode != null" >
        sub_code,
      </if>
      <if test="name != null" >
        name,
      </if>
      <if test="enabled != null" >
        enabled,
      </if>
      <if test="memo != null" >
        memo,
      </if>
      <if test="creator != null" >
        creator,
      </if>
      <if test="createName != null" >
        create_name,
      </if>
      <if test="createDate != null" >
        create_date,
      </if>
      <if test="updator != null" >
        updator,
      </if>
      <if test="updateName != null" >
        update_name,
      </if>
      <if test="updateDate != null" >
        update_date,
      </if>
      <if test="operation != null" >
        operation,
      </if>
      <if test="previousId != null" >
        previous_id,
      </if>
      <if test="versionNo != null" >
        version_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=INTEGER},
      </if>
      <if test="subjectRelevance != null" >
        #{subjectRelevance,jdbcType=INTEGER},
      </if>
      <if test="org != null" >
        #{org,jdbcType=INTEGER},
      </if>
      <if test="module != null" >
        #{module,jdbcType=VARCHAR},
      </if>
      <if test="subjectId != null" >
        #{subjectId,jdbcType=INTEGER},
      </if>
      <if test="subject != null" >
        #{subject,jdbcType=VARCHAR},
      </if>
      <if test="feeCat != null" >
        #{feeCat,jdbcType=INTEGER},
      </if>
      <if test="subFeeCat != null" >
        #{subFeeCat,jdbcType=INTEGER},
      </if>
      <if test="code != null" >
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="subCode != null" >
        #{subCode,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null" >
        #{enabled,jdbcType=BIT},
      </if>
      <if test="memo != null" >
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="createName != null" >
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null" >
        #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateName != null" >
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null" >
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="operation != null" >
        #{operation,jdbcType=CHAR},
      </if>
      <if test="previousId != null" >
        #{previousId,jdbcType=INTEGER},
      </if>
      <if test="versionNo != null" >
        #{versionNo,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="TAccountantSubjectRelevanceHistory" >
    update t_accountant_subject_relevance_history
    <set >
      <if test="subjectRelevance != null" >
        subject_relevance = #{subjectRelevance,jdbcType=INTEGER},
      </if>
      <if test="org != null" >
        org = #{org,jdbcType=INTEGER},
      </if>
      <if test="module != null" >
        module = #{module,jdbcType=VARCHAR},
      </if>
      <if test="subjectId != null" >
        subject_id = #{subjectId,jdbcType=INTEGER},
      </if>
      <if test="subject != null" >
        subject = #{subject,jdbcType=VARCHAR},
      </if>
      <if test="feeCat != null" >
        fee_cat = #{feeCat,jdbcType=INTEGER},
      </if>
      <if test="subFeeCat != null" >
        sub_fee_cat = #{subFeeCat,jdbcType=INTEGER},
      </if>
      <if test="code != null" >
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="subCode != null" >
        sub_code = #{subCode,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="enabled != null" >
        enabled = #{enabled,jdbcType=BIT},
      </if>
      <if test="memo != null" >
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        creator = #{creator,jdbcType=INTEGER},
      </if>
      <if test="createName != null" >
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updator != null" >
        updator = #{updator,jdbcType=INTEGER},
      </if>
      <if test="updateName != null" >
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateDate != null" >
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="operation != null" >
        operation = #{operation,jdbcType=CHAR},
      </if>
      <if test="previousId != null" >
        previous_id = #{previousId,jdbcType=INTEGER},
      </if>
      <if test="versionNo != null" >
        version_no = #{versionNo,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

</mapper>