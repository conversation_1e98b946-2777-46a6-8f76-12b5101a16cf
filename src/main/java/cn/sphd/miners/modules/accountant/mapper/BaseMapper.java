package cn.sphd.miners.modules.accountant.mapper;

import cn.sphd.miners.common.utils.QueryData;
import java.util.List;
import java.util.Map;

/**
 * Created by 刘洪涛 on 17-3-20.
 */
public interface BaseMapper<T> {

    public List<T> listPage(QueryData queryData);

    public T getSingle(T t);

    public Integer insert(T t);

    public int update(T t);

    public int delete(T t);

    public List<Map<String,Object>> getLoanCredit (Map<String,Object> map);

}
