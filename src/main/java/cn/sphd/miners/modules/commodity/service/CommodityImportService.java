package cn.sphd.miners.modules.commodity.service;


import cn.sphd.miners.modules.commodity.dto.ReqPdObject;
import cn.sphd.miners.modules.commodity.dto.RespCommodityImport;
import cn.sphd.miners.modules.commodity.entity.PdCommodityImportAttach;
import cn.sphd.miners.modules.commodity.entity.PdMerchandise;
import cn.sphd.miners.modules.commodity.entity.PdCommodityImport;
import cn.sphd.miners.modules.material.entity.RespStatus;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;

/**
 * @ClassName ProductImportService
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/08/26 11:19
 * @Version 1.0
 */
public interface CommodityImportService {
    List<PdMerchandise> getPdByOid(int org);
    List<PdCommodityImport> getImportPdByOid(int org);
    Integer whetherUnfinishedImport(int org, Integer isPurchased, String produdctCatetory);
    RespStatus updateFalsePdEnter(PdCommodityImport pdCommodityImport);
    ReqPdObject allImportPdEnter(ReqPdObject reqPdObject);
    ReqPdObject saveImportPd(ReqPdObject reqPdObject, User user);
    Integer getNoUnitNumberCountByOrg(Integer org, String isPurchased,Integer type);
    ReqPdObject unfinishedImportPd(User user, String isPurchased,Integer type);
    Integer finishImportPd(User user, String isPurchased,Integer type,Integer produdctCatetory);
    RespStatus updateImportPd(RespCommodityImport pdCommodityImport, User user);
    Integer deleteImportPd(Integer id,User user);
    Integer finishImportPdEnter(User user, String isPurchased,Integer type);

    PdCommodityImportAttach getPdCommodityImportAttach(int id);
    PdCommodityImport getPdCommodityImport(int id);

    int handleImportData(User user);
}
