package cn.sphd.miners.modules.inStockCheck.mapper;

import java.util.List;

import cn.sphd.miners.modules.inStockCheck.entity.TMtBoundWarehouse;
import org.apache.ibatis.annotations.Param;

public interface TMtBoundWarehouseMapper {
    int deleteByPrimaryKey(Long id);

    int deleteByPrimaryKeyIn(List<Long> list);

    int insert(TMtBoundWarehouse record);

    int insertOrUpdate(TMtBoundWarehouse record);

    int insertOrUpdateSelective(TMtBoundWarehouse record);

    int insertSelective(TMtBoundWarehouse record);

    TMtBoundWarehouse selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TMtBoundWarehouse record);

    int updateByPrimaryKey(TMtBoundWarehouse record);

    int updateBatch(@Param("list") List<TMtBoundWarehouse> list);

    int updateBatchUseMultiQuery(@Param("list") List<TMtBoundWarehouse> list);

    int updateBatchSelective(@Param("list") List<TMtBoundWarehouse> list);

    int batchInsert(@Param("list") List<TMtBoundWarehouse> list);

    int batchInsertSelectiveUseDefaultForNull(@Param("list") List<TMtBoundWarehouse> list);
}