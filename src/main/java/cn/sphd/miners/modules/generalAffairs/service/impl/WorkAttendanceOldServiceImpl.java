package cn.sphd.miners.modules.generalAffairs.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.common.service.BaseServiceImpl;
import cn.sphd.miners.common.utils.*;
import cn.sphd.miners.common.utils.NewDateUtils.Period;
import cn.sphd.miners.modules.accountant.entity.TAccountantSettle;
import cn.sphd.miners.modules.accountant.service.SubjectSelectService;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.generalAffairs.dao.*;
import cn.sphd.miners.modules.generalAffairs.dto.*;
import cn.sphd.miners.modules.generalAffairs.entity.*;
import cn.sphd.miners.modules.generalAffairs.service.PersonnelAttendanceMonthlyService;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceOldService;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceService;
import cn.sphd.miners.modules.iot.dao.IotTerminalDao;
import cn.sphd.miners.modules.iot.entity.IotTerminal;
import cn.sphd.miners.modules.iot.service.IotService;
import cn.sphd.miners.modules.myWork.service.MyWorkService;
import cn.sphd.miners.modules.personal.dao.ApprovalProcessDao;
import cn.sphd.miners.modules.personal.dao.PersonnelLeaveDao;
import cn.sphd.miners.modules.personal.dao.PersonnelOvertimeDao;
import cn.sphd.miners.modules.personal.entity.ApprovalProcess;
import cn.sphd.miners.modules.personal.entity.PersonnelLeave;
import cn.sphd.miners.modules.personal.entity.PersonnelLeaveItem;
import cn.sphd.miners.modules.personal.entity.PersonnelOvertime;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.personal.service.LeaveService;
import cn.sphd.miners.modules.personal.service.OvertimeService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.dao.ApprovalFlowDao;
import cn.sphd.miners.modules.system.dao.ApprovalItemDao;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.dto.UserDto;
import cn.sphd.miners.modules.system.entity.ApprovalFlow;
import cn.sphd.miners.modules.system.entity.ApprovalItem;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgService;
import cn.sphd.miners.modules.system.service.UserService;
import com.alibaba.fastjson.JSON;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by Administrator on 2018/4/11.
 */
@Service("workAttendanceOldService")
public class WorkAttendanceOldServiceImpl extends BaseServiceImpl implements WorkAttendanceOldService {
    protected final String sdf = "yyyy-MM-dd HH:mm:ss";
    @Autowired
    WorkAttendanceService workAttendanceService;
    @Autowired
    RedisTemplate redisTemplate;
    @Autowired
    WorkAttendanceOldService workAttendanceOldService;
    @Autowired
    SWMessageService swMessageService;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    PersonnelAttendanceConfigDao personnelAttendanceConfigDao;
    @Autowired
    PersonnelAttendanceDepartmentConfigDao personnelAttendanceDepartmentConfigDao;
    @Autowired
    PersonnelAttendanceExceptionDao personnelAttendanceExceptionDao;
    @Autowired
    PersonnelAttendanceUserDao personnelAttendanceUserDao;
    @Autowired
    ApprovalItemDao approvalItemDao;
    @Autowired
    ApprovalProcessDao approvalProcessDao;
    @Autowired
    ApprovalInstanceDao approvalInstanceDao;
    @Autowired
    ApprovalDataDao approvalDataDao;
    @Autowired
    ApprovalFlowDao approvalFlowDao;
    @Autowired
    PersonnelAttendanceUserDetailDao personnelAttendanceUserDetailDao;
    @Autowired
    PersonnelAttendanceUserHistoryDao personnelAttendanceUserHistoryDao;
    @Autowired
    PersonnelAttendanceUserDetailHistoryDao personnelAttendanceUserDetailHistoryDao;
    @Autowired
    PersonnelAttendanceConfigHistoryDao personnelAttendanceConfigHistoryDao;
    @Autowired
    PersonnelAttendanceDepartmentConfigHistoryDao personnelAttendanceDepartmentConfigHistoryDao;
    @Autowired
    PersonnelAttendanceRecordDao personnelAttendanceRecordDao;
    @Autowired
    PersonnelAttendanceMonthlyDao personnelAttendanceMonthlyDao;
    @Autowired
    PersonnelAttendanceOrgMonitorDao personnelAttendanceOrgMonitorDao;
    @Autowired
    UserDepartmentHistoryDao userDepartmentHistoryDao;
    @Autowired
    PersonnelLeaveDao personnelLeaveDao;
    @Autowired
    PersonnelOvertimeDao personnelOvertimeDao;
    @Autowired
    PersonnelAttendanceUserDetailPunchDao personnelAttendanceUserDetailPunchDao;
    @Autowired
    PersonnelAttendancePunchDao personnelAttendancePunchDao;
    @Autowired
    IotTerminalDao iotTerminalDao;
    @Autowired
    UserDao userDao;
    @Autowired
    PersonnelAttendanceTerminalDao personnelAttendanceTerminalDao;

    @Autowired
    LeaveService leaveService;
    @Autowired
    OvertimeService overtimeService;
    @Autowired
    PersonnelAttendanceMonthlyService personnelAttendanceMonthlyService;
    @Autowired
    UserService userService;
    @Autowired
    OrgService orgService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    MyWorkService myWorkService;
    @Autowired
    IotService iotService;
    @Autowired
    SubjectSelectService subjectSelectService;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;

//    @Override
//    public void attendanceTimeSetting(String effectDate,Integer attendancePattern,Short lateLimit,Short earlyLimit,Boolean leaveWork,String personnelAttendanceConfigs,User user) {
//        JSONArray jsonArray = JSONArray.fromObject(personnelAttendanceConfigs);
//        List personnelAttendanceConfigList = JSONArray.toList(jsonArray);
//        for (int i=0;i<personnelAttendanceConfigList.size(); i++){
//            JSONObject jo = JSONObject.fromObject(personnelAttendanceConfigList.get(i));
//            String inputTimeString = (String) jo.get("inputTime");
//            Date inputTime = null; //每日考勤录入时间
//            if (StringUtils.isNotEmpty(inputTimeString)){
//                inputTime = NewDateUtils.dateFromString(jo.getString("inputTime"), "HH:mm");
//            }
//            Date beginTime = NewDateUtils.dateFromString(jo.getString("beginTime"), "HH:mm"); //上班时间
//            Date endTime = NewDateUtils.dateFromString(jo.getString("endTime"), "HH:mm"); //下班时间
//            Date openDate = NewDateUtils.dateFromString(effectDate,"yyyy-MM-dd");
//            String type = jo.getString("type"); //类型:1-正常班,2-倒班
//            Boolean isBreak = jo.getBoolean("isBreak");//是否中间休息(是否中午考勤),true-是
//            Date breakBegin = NewDateUtils.dateFromString(jo.getString("breakBegin"), "HH:mm");//午休开始时间
//            Date breakEnd = NewDateUtils.dateFromString(jo.getString("breakEnd"), "HH:mm");//午休结束时间
//            String departmentIds = jo.getString("departmentIds"); //部门id，以”，“分割
//            Byte attendancePatternByte = ConfigAttendancePattern.manualEntry.getIndex();
//            if (attendancePattern!=null && attendancePattern==1){
//                attendancePatternByte = ConfigAttendancePattern.attendanceTreasure.getIndex();
//            }
//            PersonnelAttendanceConfig personnelAttendanceConfig = workAttendanceOldService.addPersonnelAttendanceConfig(user,inputTime,beginTime,endTime,openDate,type,isBreak,breakBegin,breakEnd,WorkAttendanceOldService.AttendanceConfigOperation.create.getIndex(),
//                    attendancePatternByte,lateLimit,earlyLimit,leaveWork,null,departmentIds);
//            System.out.println("/attendanceTimeSetting.do getPersonnelAttendanceDepartmentConfigHashSet size="+personnelAttendanceConfig.getPersonnelAttendanceDepartmentConfigHashSet().size());
//        }
//    }

//    @Override
//    @CacheEvict(value = "getPersonnelAttendanceConfigsByExactOpenDate")
//    public PersonnelAttendanceConfig addPersonnelAttendanceConfig(User user,Date inputTime,Date beginTime,Date endTime,Date openDate,String type,Boolean isBreak,
//        Date breakBegin,Date breakEnd,Byte operation,Byte attendancePattern,Short lateLimit,Short earlyLimit,Boolean leaveWork,Integer recordId,String departmentIds) {
////        personnelAttendanceConfigDao.save(personnelAttendanceConfig);
//        PersonnelAttendanceConfig personnelAttendanceConfig = new PersonnelAttendanceConfig();
//        personnelAttendanceConfig.setCreator(user.getUserID());
//        personnelAttendanceConfig.setCreateDate(new Date());
//        personnelAttendanceConfig.setCreateName(user.getUserName());
//        personnelAttendanceConfig.setUpdator(user.getUserID());
//        personnelAttendanceConfig.setUpdateDate(new Date());
//        personnelAttendanceConfig.setUpdateName(user.getUserName());
//        personnelAttendanceConfig.setOrg(user.getOid());
//        personnelAttendanceConfig.setEnabled(true);//是否有效,true-有效,false-无效
//
//        personnelAttendanceConfig.setInputTime(inputTime);//每日考勤录入时间(如未按时录入,则为旷工)
//        personnelAttendanceConfig.setBeginTime(beginTime); //上班时间
//        personnelAttendanceConfig.setEndTime(endTime);  //下班时间
//        personnelAttendanceConfig.setOpenDate(openDate); //规则启用日期
//        personnelAttendanceConfig.setType(type);//类型:1-正常班,2-倒班
//        personnelAttendanceConfig.setMiddleBreak(isBreak);//是否中间休息(是否中午考勤),true-是
//        personnelAttendanceConfig.setBreakBegin(breakBegin);//午休开始时间
//        personnelAttendanceConfig.setBreakEnd(breakEnd);//午休结束时间
//        //修改或新增的
//        personnelAttendanceConfig.setOperation(operation);//操作：0-正常,1-增,2-删,3-改,4-修改录入日期,5-修改迟到早退和请假期设置
//        if (attendancePattern!=null&&1==attendancePattern){
//            personnelAttendanceConfig.setAttendancePattern(WorkAttendanceOldService.ConfigAttendancePattern.attendanceTreasure.getIndex()); //考勤模式:1-考勤宝,2-手工录入(默认)
//            personnelAttendanceConfig.setLateLimit(lateLimit);//迟到时限(分钟)
//            personnelAttendanceConfig.setEarlyLimit(earlyLimit); //早退时限(分钟)
//            personnelAttendanceConfig.setLeaveWork(leaveWork); //请假到岗是否使用考勤宝:0-不使用(默认,false),1-使用(true)
//        }else {
//            personnelAttendanceConfig.setAttendancePattern(WorkAttendanceOldService.ConfigAttendancePattern.manualEntry.getIndex()); //2-手工录入(默认)
//        }
//        personnelAttendanceConfig.setRecord(recordId);
//        personnelAttendanceConfigDao.save(personnelAttendanceConfig);
//
//        if (StringUtils.isNotEmpty(departmentIds)) {
//            String[] deptIds = departmentIds.split(",");
//            System.out.println("/addPersonnelAttendanceConfig deptIds size="+deptIds.length);
//            for (int j = 0; j < deptIds.length; j++) {
//                if (StringUtils.isNotEmpty(deptIds[j])){
//                    workAttendanceOldService.addPersonnelAttendanceDepartmentConfig(deptIds[j],user,personnelAttendanceConfig,null,1);
//                    PersonnelAttendanceDepartmentConfig personnelAttendanceDepartmentConfig = new PersonnelAttendanceDepartmentConfig();
//                    personnelAttendanceDepartmentConfig.setCreator(user.getUserID());
//                    personnelAttendanceDepartmentConfig.setCreateDate(new Date());
//                    personnelAttendanceDepartmentConfig.setCreateName(user.getUserName());
//                    personnelAttendanceDepartmentConfig.setDept(Integer.parseInt(deptIds[j]));
//                    personnelAttendanceDepartmentConfig.setRule(personnelAttendanceConfig);
//                    personnelAttendanceDepartmentConfig.setOrg(user.getOid());
//                    personnelAttendanceDepartmentConfigDao.save(personnelAttendanceDepartmentConfig);
//                    System.out.println("/addPersonnelAttendanceConfig add deptId:"+deptIds[j]);
//                }
//            }
//        }
//        return personnelAttendanceConfig;
//    }

    @Override
    public void addPersonnelAttendanceException(Integer oid, Date nextBeginTime, Date nextEndTime) {
        if (!workAttendanceService.hasPersonnelAttendanceExceptionByMonth(oid, nextBeginTime, nextEndTime)) {
            Long end = nextEndTime.getTime();
            for (; nextBeginTime.getTime() < end; nextBeginTime = NewDateUtils.changeDay(nextBeginTime, 1)) {
                addPersonnelAttendanceException(oid, nextBeginTime, null, null);
            }
        }
    }

    @Override
    public void addPersonnelAttendanceException(Integer oid, Date exceptionDate, String type, User user) {
        PersonnelAttendanceException personnelAttendanceException = new PersonnelAttendanceException();
        personnelAttendanceException.setOrg(oid);
        personnelAttendanceException.setCreateDate(new Date());
        if (user != null) {
            personnelAttendanceException.setCreator(user.getUserID());
            personnelAttendanceException.setCreateName(user.getUserName());
        } else {
//            personnelAttendanceException.setCreator(0);
            personnelAttendanceException.setCreateName("系统");
        }
        if (MyStrings.nulltoempty(type).isEmpty()) {   //type为空，或者为null：1-假，2-班
            type = NewDateUtils.isWeekend(exceptionDate) ? "1" : "2";
        }
        personnelAttendanceException.setType("1".equals(type)?WorkAttendanceService.WorkdayType.DayOff:WorkAttendanceService.WorkdayType.Workday); //1-假 2-班
        personnelAttendanceException.setExceptionDate(exceptionDate); //考勤日期
        personnelAttendanceExceptionDao.save(personnelAttendanceException);
    }

//    @Override
//    public List<PersonnelAttendanceConfig> getPersonnelAttendanceConfigList(Integer oid, Integer type, Date openDate) {
//        Map<String, String> orderBy = new HashMap<>();
//        String hql = " and o.enabled = 1";  //有效的规则
//        if (oid != null) {
//            hql += " and o.org = " + oid;
//        }
//        if (type != null) {
//            hql += " and o.type = " + type.toString();
//        }
//        if (openDate != null) {
//            hql += " and o.openDate ='" + openDate + "'";
//        }
//        orderBy.put("id", "desc");
//        return personnelAttendanceConfigDao.findCollectionByConditionNoPage(hql, null, orderBy);
//    }

//    @Override
//    public PersonnelAttendanceUserDetail addPersonnelAttendanceUserDetail(Date beginDate, Date endDate, String type, String source, String businessType,Integer leaveType, Integer business, String reason, PersonnelAttendanceUser p, User user, String overDuration, String overMemo, String state, Date createDate) {
//        PersonnelAttendanceUserDetail personnelAttendanceUserDetail = new PersonnelAttendanceUserDetail();
//        personnelAttendanceUserDetail.setType(type);//类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班
//        if (beginDate != null) {
//            personnelAttendanceUserDetail.setBeginTime(beginDate);
//        }
//        if (endDate != null) {
//            personnelAttendanceUserDetail.setEndTime(endDate);
//        }
//        if (!MyStrings.nulltoempty(overDuration).isEmpty()) {
//            personnelAttendanceUserDetail.setDuration(Double.valueOf(overDuration));
//        } else if (beginDate != null && endDate != null) {
//            personnelAttendanceUserDetail.setDuration((double) TimeUnit.MILLISECONDS.toHours(endDate.getTime() - beginDate.getTime()));
//        }
//        personnelAttendanceUserDetail.setMemo(overMemo);
//        personnelAttendanceUserDetail.setAttendanceId(p.getId());
////        personnelAttendanceUserDetail.setPersonnelAttendanceUser(p);//考勤
//        personnelAttendanceUserDetail.setSource(source);//来源:1-审批,2-录入
//        if (!MyStrings.nulltoempty(businessType).isEmpty()) {
//            personnelAttendanceUserDetail.setBusinessType(businessType);
//        }
//        personnelAttendanceUserDetail.setLeaveType(leaveType);
//        personnelAttendanceUserDetail.setBusiness(business);
//        personnelAttendanceUserDetail.setReason(reason);
//        personnelAttendanceUserDetail.setUser(p.getUser());
//        personnelAttendanceUserDetail.setState(state);
//        personnelAttendanceUserDetail.setAttendanceDate(p.getAttendanceDate());//考勤日期
//        if (user != null) {
//            personnelAttendanceUserDetail.setCreator(user.getUserID());
//            personnelAttendanceUserDetail.setCreateName(user.getUserName());
//        } else {
////            personnelAttendanceUserDetail.setCreator(0);
//            personnelAttendanceUserDetail.setCreateName("系统");
//        }
//        personnelAttendanceUserDetail.setOrg(p.getOrg());
//        personnelAttendanceUserDetailDao.save(personnelAttendanceUserDetail);
//        return personnelAttendanceUserDetail;
//    }

    @Override
    public PersonnelAttendanceUserDetail addAttendanceUserDetail(Date beginDate, Date endDate, String type, String source, String businessType, Integer leaveType, Integer business, String reason, PersonnelAttendanceUser p, User user, String overDuration, String overMemo, String state, Date createDate) {
        PersonnelAttendanceUserDetail personnelAttendanceUserDetail = new PersonnelAttendanceUserDetail();
        personnelAttendanceUserDetail.setType(type);//类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班
        if (beginDate != null) {
            personnelAttendanceUserDetail.setBeginTime(beginDate);
        }
        if (endDate != null) {
            personnelAttendanceUserDetail.setEndTime(endDate);
        }
        if (!MyStrings.nulltoempty(overDuration).isEmpty()) {
            personnelAttendanceUserDetail.setDuration(Double.valueOf(overDuration));
        } else if (beginDate != null && endDate != null) {
            personnelAttendanceUserDetail.setDuration((double) TimeUnit.MILLISECONDS.toHours(endDate.getTime() - beginDate.getTime()));
        }
        personnelAttendanceUserDetail.setMemo(overMemo);
        personnelAttendanceUserDetail.setAttendanceId(p.getId());
//        personnelAttendanceUserDetail.setPersonnelAttendanceUser(p);//考勤
        personnelAttendanceUserDetail.setSource(source);//来源:1-审批,2-录入
        personnelAttendanceUserDetail.setBusinessType(businessType);
        personnelAttendanceUserDetail.setLeaveType(leaveType);
//        if ("5".equals(type)) {
//            personnelAttendanceUserDetail.setLeaveTypeName(leaveService.getLeaveTypeName(leaveType, businessType));
//        }
        personnelAttendanceUserDetail.setBusiness(business);
        personnelAttendanceUserDetail.setReason(reason);
        personnelAttendanceUserDetail.setUser(p.getUser());
        personnelAttendanceUserDetail.setState(state);
        personnelAttendanceUserDetail.setAttendanceDate(p.getAttendanceDate());//考勤日期
        if (user != null) {
            personnelAttendanceUserDetail.setCreator(user.getUserID());
            personnelAttendanceUserDetail.setCreateName(user.getUserName());
        } else {
//            personnelAttendanceUserDetail.setCreator(0);
            personnelAttendanceUserDetail.setCreateName("系统");
        }
        personnelAttendanceUserDetail.setOrg(p.getOrg());
        return personnelAttendanceUserDetail;
    }

//    @Override
//    public PersonnelAttendanceUser getPersonnelAttendanceUserByUserIdAndId(Integer id, Integer userId, Date time, String endState) {
//        StringBuffer hql = new StringBuffer("from PersonnelAttendanceUser");
//        HashMap<String, Object> params = new HashMap<>();
//        StringBuffer where = new StringBuffer();
//        if (id != null) {
//            where.append(" and id = :id");
//            params.put("id", id);
//        }
//        if (userId != null) {
//            where.append(" and user = :user");
//            params.put("user", userId);
//        }
//        if (!MyStrings.nulltoempty(endState).isEmpty()) {    //上班状态:0-未考勤,1-已考勤，2-请假
//            where.append(" and endState = :endState");
//            params.put("endState", endState);
//        }
//        if (time != null) {
//            where.append(" and attendanceDate = :attendanceDate");
//            params.put("attendanceDate", NewDateUtils.today(time));
//        }
//        if (where.length() > 0) {
//            hql.append(" where ").append(where.substring(4));
//        }
//        return (PersonnelAttendanceUser) personnelAttendanceUserDao.getByHQLWithNamedParams(hql.toString(), params);
//    }

    @Override
    public PersonnelAttendanceException getPersonnelAttendanceExceptionByOid(Integer oid, Date exceptionDate, String type) {
        StringBuffer hql = new StringBuffer("from PersonnelAttendanceException where org = :oid");
        HashMap<String, Object> params = new HashMap<>();
        params.put("oid", oid);
        if (exceptionDate != null) {
            hql.append(" and exceptionDate = :exceptionDate");
            params.put("exceptionDate", exceptionDate);
        }
        if (!MyStrings.nulltoempty(type).isEmpty()) {
            hql.append(" and type = :type");
            params.put("type", type);
        }
        PersonnelAttendanceException personnelAttendanceException = (PersonnelAttendanceException) personnelAttendanceExceptionDao.getByHQLWithNamedParams(hql.toString(), params);
        return personnelAttendanceException;
    }

    @Override
    public List<PersonnelAttendanceUserDetail> getPersonnelAttendanceUserDetailByUserId(Integer userId, Integer attendanceId, Date time, String type, String duration) {
        StringBuffer hql = new StringBuffer(" from PersonnelAttendanceUserDetail ");
        HashMap<String, Object> params = new HashMap<>();
        List<String> where = new ArrayList<>();
        if (userId != null) {
            where.add("user = :user");
            params.put("user", userId);
        }
        if (attendanceId != null) {
            where.add("personnelAttendanceUser.id = :attendanceId");
            params.put("attendanceId", attendanceId);
        }
        if (!"".equals(type) && type != null) {
            where.add("type = :type");
            params.put("type", type);
        }
        if (time != null) {
            where.add("attendanceDate between :beginTime and :endTime");
            params.put("beginTime", NewDateUtils.today(time));
            params.put("endTime", NewDateUtils.getLastTimeOfDay(time));
        }
        if (!"".equals(duration) && duration != null) {   //加班时用，如果时长为0，则不计入
            where.add("duration!=0.0");
        }
        if (!where.isEmpty()) {
            hql.append(" where ").append(org.apache.commons.lang3.StringUtils.join(where, " and "));
        }
        return personnelAttendanceUserDetailDao.getListByHQLWithNamedParams(hql.toString(), params);
    }

//    @Override
//    public List<PersonnelAttendanceUserDetail> getPersonnelAttendanceUserDetailByBusiness(Integer attendanceId, String type, String source, Integer business, String state) {
////        String hql = " from PersonnelAttendanceUserDetail where 1=1 ";
////        if (attendanceId != null) {
////            hql += " and attendanceId = " + attendanceId;
////        }
////        if (!MyStrings.nulltoempty(type).isEmpty()) {
////            if ("10".equals(type)){  //除了正常和加班的，加班的另外算
////                hql += " and type not in (1,8)";
////            }else {
////                hql += " and type = " + type;
////            }
////        }
////        if (!MyStrings.nulltoempty(source).isEmpty()) {
////            if ("4".equals(source)) {   //系统默认旷工的排除
////                hql += " and source != 3 ";
////            } else {
////                hql += " and source = " + source;
////            }
////        }
////        if (business != null) {
////            hql += " and business = " + business;
////        }
////        if (!"".equals(state) && state != null) {
////            hql += " and state = " + state;
////        }
////        return personnelAttendanceUserDetailDao.getListByHQL(hql);
//        StringBuffer hql = new StringBuffer("from PersonnelAttendanceUserDetail");
//        List<String> where = new ArrayList<>();
//        Map<String, Object> params = new HashMap<>();
//        if (attendanceId != null) {
//            where.add("attendanceId=:attendanceId");
//            params.put("attendanceId", attendanceId);
//        }
//        if (!StringUtils.isBlank(type)) {
//            if ("10".equals(type)){  //除了正常和加班的，加班的另外算
//                where.add("type not in (:type)");
//                params.put("type", Arrays.asList("1","8"));
//            }else {
//                where.add("type=:type");
//                params.put("type", type);
//            }
//        }
//        if (!StringUtils.isBlank(source)) {
//            if ("4".equals(source)) {   //系统默认旷工的排除
//                where.add("source!=:source");
//                params.put("source", "3");
//            } else {
//                where.add("source=:source");
//                params.put("source", source);
//            }
//        }
//        if (business != null) {
//            where.add("business=:business");
//            params.put("business", business);
//        }
//        if (!StringUtils.isBlank(state)) {
//            where.add("state!=:state");
//            params.put("state", state);
//        }
//        if(!where.isEmpty()) {
//            hql.append(" where ").append(StringUtils.join(where, " and "));
//        } else {
//            logger.warn("getPersonnelAttendanceUserDetailByBusiness params is empty, 查询参数为空！");
//        }
//        return personnelAttendanceUserDetailDao.getListByHQLWithNamedParams(hql.toString(), params);
//    }

//    @Override
//    public List<PersonnelAttendanceUserDetail> getPersonnelAttendanceUserDetailByMonth(Integer userId, Integer attendanceId, Date begin, Date end, String type) {
//        Map<String, Object> params = new HashMap<>();
//        String hql = "from PersonnelAttendanceUserDetail o where o.user =:userId";
//        params.put("userId", userId);  //userId不为空
//        if (attendanceId != null) {
//            hql += " and o.personnelAttendanceUser.id =:attendanceId";
//            params.put("attendanceId", attendanceId);
//        }
//        if (begin != null) {
//            hql += " and o.attendanceDate >=:beginDate";
//            params.put("beginDate", begin);
//        }
//        if (end != null) {
//            hql += " and o.attendanceDate <=:endDate";
//            params.put("endDate", end);
//        }
//        if (!MyStrings.nulltoempty(type).isEmpty()) {
//            hql += " and o.type =:type";
//            params.put("type", type);
//        }
//        hql+=" order by o.attendanceDate";
//        List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetails = (List<PersonnelAttendanceUserDetail>) personnelAttendanceUserDetailDao.getListByHQLWithNamedParams(hql, params);
//        return personnelAttendanceUserDetails;
//    }

//    @Override
//    public List<PersonnelAttendanceException> getPersonnelAttendanceExceptionByMonth(Integer oid, String beginTime, String endTime) {
//        Map<String, String> orderBy = new HashMap<>();
//        String sql = " and o.org = " + oid;
//        if (!"".equals(beginTime) && beginTime != null) {
//            sql += " and o.exceptionDate>='" + beginTime + " 00:00:00'";
//        }
//        if (!"".equals(endTime) && endTime != null) {
//            sql += " and o.exceptionDate<='" + endTime + " 23:59:59'";
//        }
//        orderBy.put("id", "asc");
//        return personnelAttendanceExceptionDao.findCollectionByConditionNoPage(sql, null, orderBy);
//    }

    @Override
    public List<PersonnelAttendanceException> getPersonnelAttendanceExceptionByMonth(Integer oid, Date beginTime, Date endTime) {
        Map<String, Object> params = new HashMap<>();
        StringBuffer hql = new StringBuffer("from PersonnelAttendanceException where org = :org");
        params.put("org", oid);
        if (beginTime != null && endTime != null) {
            hql.append(" and exceptionDate between :beginTime and :endTime");
            params.put("beginTime", beginTime);
            params.put("endTime", endTime);
        }
        hql.append(" order by exceptionDate desc, id desc");
        return personnelAttendanceExceptionDao.getListByHQLWithNamedParams(hql.toString(), params);
    }

//    @Override
//    public boolean hasPersonnelAttendanceExceptionByMonth(Integer oid, Date beginTime, Date endTime) {
//        Map<String, Object> params = new HashMap<>();
//        StringBuffer hql = new StringBuffer("select id from PersonnelAttendanceException where org = :org");
//        params.put("org", oid);
//        if (beginTime != null && endTime != null) {
//            hql.append(" and exceptionDate between :beginTime and :endTime");
//            params.put("beginTime", beginTime);
//            params.put("endTime", endTime);
//        }
//        return personnelAttendanceExceptionDao.getByHQLWithNamedParams(hql.toString(), params) != null;
//    }

    @Override
    public List<PersonnelAttendanceUserDetailHistory> getPersonnelAttendanceUserDetailHistoryByPHId(Integer phId, String state, String type) {
        Map<String, Object> params = new HashMap<>();
        String hql = " from PersonnelAttendanceUserDetailHistory where source!=3";
        if (phId != null) {
            hql += " and attendance=:attendance";
            params.put("attendance", phId);
        }
        if (!"".equals(state) && state != null) {
            hql += " and state=:state";
            params.put("state", state);
        }
        if (!"".equals(type) && type != null) {
            hql += " and type=:type";
            params.put("type", type);
        }
        List<PersonnelAttendanceUserDetailHistory> personnelAttendanceUserDetailHistories = personnelAttendanceUserDetailHistoryDao.getListByHQLWithNamedParams(hql,params);
        return personnelAttendanceUserDetailHistories;
    }

    @Override
    public List<PersonnelAttendanceUserDetail> getPersonnelAttendanceUserDetailById(Integer pId) {
        Map<String, Object> map = new HashMap<>();
        String hql = "from PersonnelAttendanceUserDetail where attendanceId=:attendanceId";
        map.put("attendanceId", pId);
        List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetails = personnelAttendanceUserDetailDao.getListByHQLWithNamedParams(hql, map);
        return personnelAttendanceUserDetails;
    }

    @Override
    public PersonnelAttendanceConfig getPersonnelAttendanceConfigById(Integer attendanceId) {
        return personnelAttendanceConfigDao.get(attendanceId);
    }

    @Override
    public void deletePersonnelAttendanceConfig(PersonnelAttendanceConfig personnelAttendanceConfig) {
        personnelAttendanceConfigDao.delete(personnelAttendanceConfig);
    }

//    @Override
//    public PersonnelAttendanceException getPersonnelAttendanceExceptionByExceptionDate(Integer oid, Date exceptionDate) {
//        Map<String, Object> params = new HashMap<>();
//        String hql = " from PersonnelAttendanceException where org=:oid and exceptionDate=:exceptionDate";
//        params.put("oid", oid);
//        params.put("exceptionDate", exceptionDate);
//        return (PersonnelAttendanceException) personnelAttendanceExceptionDao.getByHQLWithNamedParams(hql, params);
//    }

//    @Override
//    public void deletePersonnelAttendanceException(PersonnelAttendanceException personnelAttendanceException) {
//        personnelAttendanceExceptionDao.delete(personnelAttendanceException);
//    }

    private PersonnelAttendanceConfig getOnePersonnelAttendanceConfigByOpenDate(Integer oid, Integer type, Date openDate) {
        Map<String, Object> params = new HashMap<>();
        StringBuffer hql = new StringBuffer("from PersonnelAttendanceConfig o where o.enabled = 1");
        if (oid != null) {
            hql.append(" and o.org=:oid");
            params.put("oid", oid);
        }
        if (type != null) {
            hql.append(" and o.type=:type");
            params.put("type", type.toString());
        }
        if (openDate != null) {
            hql.append(" and o.openDate<=:openDate");
            params.put("openDate", openDate);
        }
        hql.append(" order by openDate desc");
        return (PersonnelAttendanceConfig) personnelAttendanceConfigDao.getByHQLWithNamedParams(hql.toString(), params);
    }

    private List<PersonnelAttendanceConfig> getPersonnelAttendanceConfigsByOpenDate(Integer oid, Integer type, Date openDate) {
        Map<String, Object> params = new HashMap<>();
        StringBuffer hql = new StringBuffer("from PersonnelAttendanceConfig o where o.enabled = 1");
        if (oid != null) {
            hql.append(" and o.org=:oid");
            params.put("oid", oid);
        }
        if (type != null) {
            hql.append(" and o.type=:type");
            params.put("type", type.toString());
        }
        if (openDate != null) {
            hql.append(" and o.openDate<=:openDate");
            params.put("openDate", openDate);
        }
        Date maxdate = (Date) personnelAttendanceConfigDao.getByHQLWithNamedParams("select max(openDate) " + hql.toString(), params);
        if (maxdate == null) {
            return new ArrayList<>();
        } else {
            if (openDate != null) {
                hql.delete(hql.indexOf(" and o.openDate<=:openDate"), hql.length());
            }
            hql.append(" and o.openDate=:openDate");
            params.put("openDate", maxdate);
            return (List<PersonnelAttendanceConfig>) personnelAttendanceConfigDao.getListByHQLWithNamedParams(hql.toString(), params);
        }
    }

    @Override
    public List<PersonnelAttendanceConfig> getPersonnelAttendanceConfigByOpenDate(Integer oid, Integer type, Date openDate) {
        Map<String, Object> params = new HashMap<>();
        String hql = "from PersonnelAttendanceConfig o where o.enabled = 1";
        if (oid != null) {
            hql += " and o.org=:oid ";
            params.put("oid", oid);
        }
        if (type != null) {
            hql += " and o.type=:type ";
            params.put("type", type.toString());
        }
        if (openDate != null) {
            hql += " and o.openDate<=:openDate ";
            params.put("openDate", openDate);
        }
        hql += " order by id desc";
        return (List<PersonnelAttendanceConfig>) personnelAttendanceConfigDao.getListByHQLWithNamedParams(hql, params);
    }

    @Override
    @CacheEvict(value = "getPersonnelAttendanceConfigsOpenDates")    //1-只修改personnelAttendanceConfig 2-只添加历史 3-修改和添加历史
    public PersonnelAttendanceConfig updatePersonnelAttendanceConfig(PersonnelAttendanceConfig personnelAttendanceConfig) {
        personnelAttendanceConfigDao.update(personnelAttendanceConfig);
        return personnelAttendanceConfig;
    }

    @Override
    public List<PersonnelAttendanceConfigHistory> getPersonnelAttendanceConfigHistoryByRecordId(Integer attendanceRecordId, Integer oid, Integer ruleId, String operation) {
        String hql = " from PersonnelAttendanceConfigHistory where 1=1";
        if (attendanceRecordId != null) {
            hql += " and record = " + attendanceRecordId;
        }
        if (oid != null) {
            hql += " and org = " + oid;
        }
        if (ruleId != null) {
            hql += " and ruleId = " + ruleId;
        }
        if (!"".equals(operation) && operation != null) {
            hql += " and operation = " + operation;
        }
        return personnelAttendanceConfigHistoryDao.getListByHQL(hql);
    }

    @Override
    public PersonnelAttendanceUserDetail getPersonnelAttendanceUserDetailByDeatilId(Integer atterndanceDetailId) {
        return personnelAttendanceUserDetailDao.get(atterndanceDetailId);
    }

//    @Override
//    public Date getStartUsingSystemTime(Integer oid) {
//        Date startUsingSystemTime = null;
//        String hql = "from PersonnelAttendanceConfig where org=:oid and enabled=1 order by openDate";
//        HashMap<String, Object> params = new HashMap<>();
//        params.put("oid", oid);
//        PersonnelAttendanceConfig personnelAttendanceConfig = (PersonnelAttendanceConfig) personnelAttendanceConfigDao.getByHQLWithNamedParams(hql, params);
//        if (personnelAttendanceConfig != null) {
//            startUsingSystemTime = personnelAttendanceConfig.getOpenDate();
//        }
//        return startUsingSystemTime;
//    }

//    @Override
//    public boolean isOpenConfig(Integer oid, Date openDate) {
//        if (openDate == null) {
//            openDate = NewDateUtils.today();
//        }
//        String hql = "select id from PersonnelAttendanceConfig where org =:oid and openDate=:openDate and enabled = 1";
//        HashMap<String, Object> params = new HashMap<>();
//        params.put("oid", oid);
//        params.put("openDate", openDate);
//        Integer result = (Integer) personnelAttendanceConfigDao.getByHQLWithNamedParams(hql, params);
//        return result != null;
//    }

    @Override    //attendanceUserId 考勤人员的职工id
    public List<PersonnelAttendanceUserHistory> getPersonnelAttendanceUserHistoryList(Integer oid,Integer attendanceUserId,Integer attendanceId,String approveStatus,Date attendanceDate,Integer creator,Integer businessType) {
        Map<String, Object> params = new HashMap<>();
        StringBuffer hql = new StringBuffer(" from PersonnelAttendanceUserHistory");
        StringBuffer where = new StringBuffer();
        if (attendanceId!=null) {
            where.append(" and attendanceId=:attendanceId");
            params.put("attendanceId", attendanceId);
        }
        where.append(" and id in ( select business from ApprovalProcess where org =:org");
        params.put("org", oid);   //不能为空了
        if (businessType!=null){
            where.append(" and businessType=:businessType");
            params.put("businessType", businessType);
        }
        if (!MyStrings.nulltoempty(approveStatus).isEmpty()){
            where.append(" and approveStatus=:approveStatus1");
            params.put("approveStatus1", approveStatus);
        }
        where.append(")");

        if (attendanceUserId!=null){
            where.append(" and user=:user");
            params.put("user", attendanceUserId);
        }
        if (!MyStrings.nulltoempty(approveStatus).isEmpty()){
            where.append(" and approveStatus=:approveStatus");
            params.put("approveStatus", approveStatus);
        }
        if (attendanceDate!=null){
            where.append(" and attendanceDate=:attendanceDate");
            params.put("attendanceDate", attendanceDate);
        }
        if (creator!=null){
            where.append(" and creator=:creator");
            params.put("creator", creator);
        }
//        if (oid!=null){
//            where.append(" and user in ( select userID from User where oid =:oid)");
//            params.put("oid", oid);
//        }

        if (where.length() > 0) {
            hql.append(" where ").append(where.substring(4));
        }
        return personnelAttendanceUserHistoryDao.getListByHQLWithNamedParams(hql.toString(),params);
    }

    @Override
    public PersonnelAttendanceUserHistory getPersonnelAttendanceUserHistoryById(Integer attendanceUserHistoryId) {
        return personnelAttendanceUserHistoryDao.get(attendanceUserHistoryId);
    }

    @Override
    public PersonnelAttendanceUserDetailHistory addPersonnelAttendanceUserDetailHistory(Date beginDate, Date endDate, String type, String source, String businessType, String reason, PersonnelAttendanceUserHistory ph, User user, String overDuration, String overMemo, String state){
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        PersonnelAttendanceUserDetailHistory personnelAttendanceUserDetailHistory = new PersonnelAttendanceUserDetailHistory();
        personnelAttendanceUserDetailHistory.setType(type);//类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班
        if (!"".equals(type) && type != null && "8".equals(type)) {
            personnelAttendanceUserDetailHistory.setDuration(Double.valueOf(overDuration));
            personnelAttendanceUserDetailHistory.setMemo(overMemo);
        } else {
            if (beginDate != null) {
                personnelAttendanceUserDetailHistory.setBeginTime(beginDate);
            }
            if (endDate != null) {
                personnelAttendanceUserDetailHistory.setEndTime(endDate);
            }
            if (beginDate != null && endDate != null) {
                personnelAttendanceUserDetailHistory.setDuration(leaveService.getDuration(beginDate, endDate));
            }
        }
        personnelAttendanceUserDetailHistory.setSource(source);//来源:1-审批,2-录入
        if (!MyStrings.nulltoempty(businessType).isEmpty()){
            Integer leaveType = Integer.parseInt(businessType);
            if (leaveType<0) {
                leaveType = -leaveType;
                personnelAttendanceUserDetailHistory.setBusinessType(leaveType.toString());
            }else {
                personnelAttendanceUserDetailHistory.setLeaveType(leaveType);
            }
        }
        personnelAttendanceUserDetailHistory.setReason(reason);
        personnelAttendanceUserDetailHistory.setUser(ph.getUser());
        personnelAttendanceUserDetailHistory.setAttendanceDate(ph.getAttendanceDate());//考勤日期
        personnelAttendanceUserDetailHistory.setState(state);
        personnelAttendanceUserDetailHistory.setCreateDate(new Date());
        personnelAttendanceUserDetailHistory.setCreator(user.getUserID());
        personnelAttendanceUserDetailHistory.setCreateName(user.getUserName());
        personnelAttendanceUserDetailHistory.setAttendance(ph.getId());
        if (ph.getOrg() != null) {
            personnelAttendanceUserDetailHistory.setOrg(ph.getOrg());
        }
        personnelAttendanceUserDetailHistoryDao.save(personnelAttendanceUserDetailHistory);
        return personnelAttendanceUserDetailHistory;
    }

    @Override
    public PersonnelAttendanceRecord addPersonnelAttendanceRecord(User user, String effectDate, String operation,Boolean patternType) throws ParseException {
//        SimpleDateFormat sdff = new SimpleDateFormat("yyyy-MM-dd");
        PersonnelAttendanceRecord personnelAttendanceRecord = new PersonnelAttendanceRecord();
        personnelAttendanceRecord.setUpdator(user.getUserID());
        personnelAttendanceRecord.setUpdateName(user.getUserName());
        personnelAttendanceRecord.setUpdateDate(new Date());
        personnelAttendanceRecord.setCreator(user.getUserID());
        personnelAttendanceRecord.setCreateName(user.getUserName());
        personnelAttendanceRecord.setCreateDate(new Date());
        personnelAttendanceRecord.setOrg(user.getOid());
//        personnelAttendanceRecord.setEffectDate(sdff.parse(effectDate));  //规则生效时间
        personnelAttendanceRecord.setEffectDate(NewDateUtils.dateFromString(effectDate, "yyyy-MM-dd"));  //规则生效时间
        personnelAttendanceRecord.setOperation(operation);  //操作：0-正常,1-增,2-删,3-改,4-未到生效日期的考勤
        personnelAttendanceRecord.setPatternType(patternType);  //是否更换模式 true-是 false-否
        personnelAttendanceRecordDao.save(personnelAttendanceRecord);
        return personnelAttendanceRecord;
    }

//    @Override
//    public List<PersonnelAttendanceRecord> getPersonnelAttendanceRecordByOid(Integer oid, String operation, Date effectDate, PageInfo page) {
//        Map<String, Object> map = new HashMap<>();
//        String hql = "from PersonnelAttendanceRecord where org=:org";
//        map.put("org",oid);
//        if (!"".equals(operation) && operation != null) {
//            hql+= " and operation=:operation";
//            map.put("operation",operation);
//        }
//        if (!"".equals(effectDate) && effectDate != null) {
//            hql += " and effectDate<=:effectDate";
//            map.put("effectDate",effectDate);  //NewDateUtils.dateFromString(effectDate,"yyyy-MM-dd HH:mm:ss")
//        }
//        List<PersonnelAttendanceRecord> personnelAttendanceRecords = personnelAttendanceRecordDao.getListByHQLWithNamedParams(hql,map,page);
//        return personnelAttendanceRecords;
//    }

    @Override
    public PersonnelAttendanceRecord getPersonnelAttendanceRecordById(Integer attendanceRecordId) {
        return personnelAttendanceRecordDao.get(attendanceRecordId);
    }

    @Override
    public void updatePersonnelAttendanceRecord(PersonnelAttendanceRecord personnelAttendanceRecord) {
        personnelAttendanceRecordDao.update(personnelAttendanceRecord);
    }

//    @Override
//    public List<PersonnelAttendanceConfig> getPersonnelAttendanceConfigByRecordIdAndOperation(Integer recordId, String operation) {
//        String hql = " from PersonnelAttendanceConfig where id in (select ruleId from PersonnelAttendanceConfigHistory where record = " + recordId + " and operation = " + operation + ")";
//        return personnelAttendanceConfigDao.getListByHQL(hql);
//    }

    @Override
    public void updatePersonnelAttendanceConfigByRecordId(Integer recordId) {
        Map<String, Object> map = new HashMap<>();
        String hql = " update PersonnelAttendanceConfig set enabled = false where record =:recordId and enabled is not false";
        map.put("recordId", recordId);
        personnelAttendanceConfigDao.queryHQLWithNamedParams(hql, map);
    }

    @Override
    public void updatePersonnelAttendanceConfigHistoryByRecordId(Integer recordId) {
        Map<String, Object> map = new HashMap<>();
        String hql = " update PersonnelAttendanceConfigHistory set enabled = false where record =:recordId and enabled is not false";
        map.put("recordId", recordId);
        personnelAttendanceConfigDao.queryHQLWithNamedParams(hql, map);
    }

    @Override
    public List<PersonnelAttendanceUser> getPersonnelAttendanceUser(Integer id,List<Integer> oids, List<Integer> uids, Date beginDate, Date endDate, List<String> types) {
        List<String> where = new ArrayList<>();
        Map<String, Object> params = new HashMap<>();
        if(id!=null) {
            where.add("id=:id");
            params.put("id",id);
        }
        if(ObjectUtils.isNotEmpty(oids)) {
            if(oids.size()==1) {
                where.add("org=:oid");
                params.put("oid",oids.get(0));
            } else {
                where.add("org in :oids");
                params.put("oids",oids);
            }
        }
        if(ObjectUtils.isNotEmpty(uids)) {
            if(uids.size()==1) {
                where.add("user=:uid");
                params.put("uid",uids.get(0));
            } else {
                where.add("user in :uids");
                params.put("uids",uids);
            }
        }
        if(beginDate!=null && endDate!=null) {
            where.add("attendanceDate in :beginDate and :endDate");
            params.put("beginDate",beginDate);
            params.put("endDate",endDate);
        } else if(beginDate!=null){
            where.add("attendanceDate >= :beginDate");
            params.put("endDate",endDate);
        } else if(endDate!=null){
            where.add("attendanceDate <= :endDate");
            params.put("endDate",endDate);
        }
        if(ObjectUtils.isNotEmpty(types)) {
            if(types.size()==1) {
                where.add("type=:type");
                params.put("type",types.get(0));
            } else {
                where.add("type in :types");
                params.put("types",types);
            }
        }
        StringBuffer hql = new StringBuffer("from PersonnelAttendanceUser");
        if(!where.isEmpty()) {
            hql.append(" where ").append(StringUtils.join(where, " and "));
        }
        hql.append(" order by org,user,attendanceDate");
        return personnelAttendanceUserDao.getListByHQLWithNamedParams(hql.toString(), params, null);
    }

//    @Override
//    public List<PersonnelAttendanceUserDetail> getPersonnelAttendanceUserDetailByType(Integer attendanceId, Date begin, String source) {
//
//        String hql = " and (o.type=5 or o.type=7 or o.type=8 or o.type=9)";   //类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班，9-其他
//        if (attendanceId != null) {
//            hql += " and o.attendanceId = " + attendanceId;
//        }
//        if (begin != null) {
//            hql += " and TO_DAYS(o.attendanceDate) = TO_DAYS('" + new SimpleDateFormat("yyyy-MM-dd").format(begin) + "')";
//        }
//        if (!"".equals(source) && source != null) {
//            if ("4".equals(source)) {
//                hql += " and o.source!=3";   //不取系统默认的旷工，且没有进行下班录入的
//            } else {
//                hql += " and o.source=" + source;
//            }
//        }
//        return personnelAttendanceUserDetailDao.findCollectionByConditionNoPage(hql, null, null);
//    }

//    @Override
//    public PersonnelAttendanceConfig getPersonnelAttendanceConfig(Integer dept, Integer oid, Date openDate) {
//        HashMap<String, Object> params = new HashMap<>();
//        String hql = " from PersonnelAttendanceConfig where openDate=:openDate and id in (select rule from PersonnelAttendanceDepartmentConfig where org=:oid and dept=:dept)";
//        params.put("openDate", openDate);
//        params.put("oid", oid);
//        params.put("dept", dept);
//        PersonnelAttendanceConfig personnelAttendanceConfig = (PersonnelAttendanceConfig) personnelAttendanceConfigDao.getByHQLWithNamedParams(hql.toString(), params);
//        return personnelAttendanceConfig;
//    }

    @Override
    public PersonnelAttendanceUser getOnePersonnelAttendanceUserByOid(Integer oid, Integer dept, Date openDate, String beginState, Integer userId) {
        HashMap<String, Object> params = new HashMap<>();
        StringBuffer hql = new StringBuffer("select p.id from PersonnelAttendanceUser p,User u where p.user=u.userID");
        if (openDate != null) {
            hql.append(" and p.attendanceDate=:attendanceDate");
            params.put("attendanceDate", openDate);
        }
        if (!MyStrings.nulltoempty(beginState).isEmpty()) {
            hql.append(" and p.beginState=:beginState");
            params.put("beginState", beginState);
        }
        if (oid != null) {
            hql.append(" and u.oid=:oid");
            params.put("oid", oid);
        }
        if (userId != null) {
            hql.append(" and u.userID=:userId");
            params.put("userId", userId);
        }
        Integer id = (Integer) personnelAttendanceUserDao.getByHQLWithNamedParams(hql.append(" order by p.attendanceDate desc").toString(), params);
        if (id != null) {
            return personnelAttendanceUserDao.get(id);
        } else {
            return null;
        }
    }

    @Override   //sorcType 排序规则(1-名字首字母排序  2-id倒序)
    public List<PersonnelAttendanceUser> getPersonnelAttendanceUserByOid(Integer oid, Integer dept, Integer userId, Date openDate, String beginState, String userName, Integer sorcType) {
        HashMap<String, Object> params = new HashMap<>();
        StringBuffer hql = new StringBuffer("select new cn.sphd.miners.modules.generalAffairs.entity.PersonnelAttendanceUser(p.id,p.user,p.userName,p.org,p.dept,p.departName,p.attendanceDate,p.type,p.beginTime," +
                "p.endTime,p.breakBegin,p.breakEnd,p.beginState,p.endState,p.breakBeginState,p.breakEndState,p.amAttendance,p.pmAttendance,p.creator,p.createName,p.createDate,p.updator," +
                "p.updateName,p.updateDate,p.source,p.noNeed,p.finalModified,p.scanStartTime,p.scanEndTime,p.scanTime,p.lastScanPunchId,p.p1Before,p.p1After,p.p2Before,p.p2After,p.omitBefore," +
                "p.omitAfter,p.lateLimit,p.earlyLimit,u.imgPath) from PersonnelAttendanceUser p,User u where p.user=u.userID");
//        HashMap<String, Object> params = new HashMap<>(9);
//        StringBuffer hql = new StringBuffer("select new cn.sphd.miners.modules.generalAffairs.entity.PersonnelAttendanceUser(p.id,p.user,p.org,p.dept,p.attendanceDate,p.type,p.beginTime," +
//                "p.endTime,p.breakBegin,p.breakEnd,p.beginState,p.endState,p.breakBeginState,p.breakEndState,p.amAttendance,p.pmAttendance,p.creator,p.createName,p.createDate,p.updator," +
//                "p.updateName,p.updateDate,p.source,p.noNeed,p.finalModified,p.scanStartTime,p.scanEndTime,p.scanTime,p.p1Before,p.p1After,p.p2Before,p.p2After,p.lateLimit,p.earlyLimit," +
//                "u.departName,u.userName,u.imgPath) from PersonnelAttendanceUser p,User u where p.user=u.userID and u.roleCode=:roleCode and u.isDuty in :isDuty");
//        params.put("roleCode", "staff");
//        params.put("isDuty", Arrays.asList("1","9"));
        if (openDate != null) {
            hql.append(" and p.attendanceDate=:attendanceDate");
            params.put("attendanceDate", openDate);
        }
        if (!MyStrings.nulltoempty(beginState).isEmpty()) {
            hql.append(" and p.beginState=:beginState");
            params.put("beginState", beginState);
        }
        if (dept != null) {
            hql.append(" and p.dept=:dept");
            params.put("dept", dept);
        }
        if (oid != null || Integer.valueOf(0).equals(dept)) {
            hql.append(" and u.oid=:oid");
            params.put("oid", oid);
        }
        if (!MyStrings.nulltoempty(userName).isEmpty()) {
            hql.append(" and u.userName like :userName");
            params.put("userName", "%" + userName + "%");
        }
        if (userId != null) {  //取此人员的下属以及间接下属
            hql.append(" and (u.userID=:userId or u.rankUrl like :userID)");
            params.put("userId", userId); //取职工自己
            params.put("userID", "%/" + userId + "/%");  //取本职工的直接以及间接下级
        }
        if (sorcType == 1) {
            hql.append(" order by convert(u.userName, 'gbk') asc");
        } else if (sorcType == 2) {
            hql.append(" order by id desc");
        }
        List<PersonnelAttendanceUser> personnelAttendanceUsers = (List<PersonnelAttendanceUser>) personnelAttendanceUserDao.getListByHQLWithNamedParams(hql.toString(), params);
        return personnelAttendanceUsers;
    }

    @Override
    public List<PersonnelAttendanceConfig> getPersonnelAttendanceConfigByDeptId(Integer oid, Integer deptId, Date openDate) {
        HashMap<String, Object> params = new HashMap<>();
        String hql = " from PersonnelAttendanceConfig where enabled=1";
        if (openDate != null) {
            hql += " and openDate<=:openDate";
            params.put("openDate", openDate);
        }
        if (oid != null) {
            hql += " and org=:oid";
            params.put("oid", oid);
        }
        if (deptId != null) {
            hql += " and id in (select rule from PersonnelAttendanceDepartmentConfig where dept=:dept)";
            params.put("dept", deptId);
        }
        hql += " order by id desc";
        List<PersonnelAttendanceConfig> personnelAttendanceConfigs = (List<PersonnelAttendanceConfig>) personnelAttendanceConfigDao.getListByHQLWithNamedParams(hql.toString(), params);
        return personnelAttendanceConfigs;
    }

    @Override
    public AttendanceNumDto attendanceNumDay(Integer userId, Date time) {
        List<String> types = Arrays.asList("2", "3", "4", "5", "6", "7", "8");
        HashMap<String, Object> params = new HashMap<>();
        StringBuffer hql = new StringBuffer("select new cn.sphd.miners.modules.generalAffairs.dto.AttendanceNumDto(");
        for (String type : types) {
            hql.append("sum(case when (type='").append(type).append("')  then 1 else 0 end),");
        }
        hql.deleteCharAt(hql.length() - 1);
        hql.append(") from PersonnelAttendanceUserDetail where user=:userId");
        params.put("userId", userId);
        if (time != null) {
            hql.append(" and attendanceDate=:timeDay");
            params.put("timeDay", time);

        }
        AttendanceNumDto attendanceNum = (AttendanceNumDto) personnelAttendanceUserDao.getByHQLWithNamedParams(hql.toString(), params);
        return attendanceNum;
    }

//    @Override    //不取系统默认的旷工，且没有进行下班录入的
//    public List<PersonnelAttendanceUserDetail> getPersonnelAttendanceUserDetailBykg(Integer attendanceId, Integer userId, Date begin, String source) {
//        String hql = " and o.type=7";   //类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班，9-其他
//        if (begin != null) {
//            hql += " and TO_DAYS(o.attendanceDate) = TO_DAYS('" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(begin) + "')";
//        }
//        if (!"".equals(source) && source != null) {
//            if ("4".equals(source)) {
//                hql += " and o.source!=3 ";
//            } else {
//                hql += " and o.source=" + source;
//            }
//
//        }
//        if (userId != null) {
//            hql += " and o.user=" + userId;
//        }
//        if (attendanceId != null) {
//            hql += " and o.personnelAttendanceUser in (select id from PersonnelAttendanceUser where id = " + attendanceId + ") ";
//        }
//        return personnelAttendanceUserDetailDao.findCollectionByConditionNoPage(hql, null, null);
//    }

    @Override
    public Long countPersonnelAttendanceUserDetailBykg(Integer attendanceId, Integer userId, Date begin, String source) {
        StringBuffer hql = new StringBuffer("select count(*) from PersonnelAttendanceUserDetail where type=7");   //类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班，9-其他
        HashMap<String, Object> params = new HashMap<>();
        if (begin != null) {
            hql.append(" and attendanceDate = :attendanceDate");
            params.put("attendanceDate", begin);
        }
        if (!MyStrings.nulltoempty(source).isEmpty()) {
            if ("4".equals(source)) {
                hql.append(" and source<3 ");
            } else {
                hql.append(" and source=:source");
                params.put("source", source);
            }
        }
        if (userId != null) {
            hql.append(" and user=:user");
            params.put("user", userId);
        }
        if (attendanceId != null) {
            hql.append(" and personnelAttendanceUser in (select id from PersonnelAttendanceUser where id =:attendanceId)");
            params.put("attendanceId", attendanceId);
        }
        return (Long) personnelAttendanceUserDetailDao.getByHQLWithNamedParams(hql.toString(), params);
    }

    @Override    //将personnelAttendanceConfig存到历史表PersonnelAttendanceConfigHistory中    operation(0-正常,1-增,2-删,3-改)
    @CacheEvict(value = "getPersonnelAttendanceConfigsByExactOpenDate")
    public PersonnelAttendanceConfigHistory pcToPH(PersonnelAttendanceConfig personnelAttendanceConfig, PersonnelAttendanceConfig oldPersonnelAttendanceConfig, User user, PersonnelAttendanceRecord personnelAttendanceRecord, Byte operation) {
        //将要删除的考勤设置保存到历史表中
        PersonnelAttendanceConfigHistory personnelAttendanceConfigHistory = new PersonnelAttendanceConfigHistory();
        BeanUtils.copyPropertiesIgnoreNull(oldPersonnelAttendanceConfig, personnelAttendanceConfigHistory);
        personnelAttendanceConfigHistory.setUpdateDate(new Date());
        personnelAttendanceConfigHistory.setUpdator(user.getUserID());
        personnelAttendanceConfigHistory.setUpdateName(user.getUserName());
        personnelAttendanceConfigHistory.setRecord(personnelAttendanceRecord.getId());  //修改记录id
        personnelAttendanceConfigHistory.setOperation(operation);//操作：0-正常,1-增,2-删,3-改
        personnelAttendanceConfigHistory.setRule(personnelAttendanceConfig);
        personnelAttendanceConfigHistory.setPreviousRule(oldPersonnelAttendanceConfig.getId()); //老考勤的id【修改前的考勤id】
        personnelAttendanceConfigHistoryDao.save(personnelAttendanceConfigHistory);

        return personnelAttendanceConfigHistory;
    }

    @Override
    public Date getMaxdateByOpenDate(Integer oid, Date openDate) {
        Map<String, Object> params = new HashMap<>();
        StringBuffer hql = new StringBuffer("select max(openDate) from PersonnelAttendanceConfig o where o.enabled = 1");
        if (oid != null) {
            hql.append(" and o.org=:oid");
            params.put("oid", oid);
        }
        if (openDate != null) {
            hql.append(" and o.openDate<=:openDate");
            params.put("openDate", openDate);
        }
        return (Date) personnelAttendanceConfigDao.getByHQLWithNamedParams(hql.toString(), params);
    }

    @Override    //根据部门查找规则
    public PersonnelAttendanceConfig getPersonnelAttendanceConfigByDept(Integer oid, Integer deptId, Date openDate) {
        Date maxDate = getMaxdateByOpenDate(oid, openDate);
        Map<String, Object> params = new HashMap<>();

        if (maxDate != null) {
            StringBuffer hql = new StringBuffer(" from PersonnelAttendanceConfig c where c.enabled=1 ");

            if (openDate != null) {
                hql.append(" and c.openDate=:openDate");
                params.put("openDate", maxDate);
            }
            if (deptId != null) {
                hql.insert(hql.indexOf("where"), " inner join fetch c.personnelAttendanceDepartmentConfigHashSet d ");
                hql.append(" and d.dept=:deptId");
                params.put("deptId", deptId);
            }
            PersonnelAttendanceConfig personnelAttendanceConfig = (PersonnelAttendanceConfig) personnelAttendanceConfigDao.getByHQLWithNamedParams(hql.toString(), params);
            return personnelAttendanceConfig;
        } else {
            return null;
        }
    }

//    @Override
//    public Date getInputTime(Integer oid, Date openDate) {
//        Date result = null, inputTime;
//        PersonnelAttendanceConfig personnelAttendanceConfig = getOnePersonnelAttendanceConfigByOpenDate(oid, 1, openDate);
//        if (personnelAttendanceConfig != null) {
//            if (personnelAttendanceConfig.getAttendancePattern().equals(ConfigAttendancePattern.manualEntry.getIndex())) {  //手动录入
//                if (personnelAttendanceConfig.getOldCreateTime() != null && personnelAttendanceConfig.getEffectDate().getTime() > new Date().getTime()) {
//                    inputTime = personnelAttendanceConfig.getOldCreateTime();
//                } else {
//                    inputTime = personnelAttendanceConfig.getInputTime();
//                }
//                if (inputTime != null) {
//                    Calendar calendar = Calendar.getInstance();
//                    calendar.setTime(NewDateUtils.today(openDate));
//                    calendar.set(Calendar.HOUR, Integer.valueOf(new SimpleDateFormat("HH").format(inputTime)));
//                    calendar.set(Calendar.MINUTE, Integer.valueOf(new SimpleDateFormat("mm").format(inputTime)));
//                    result = calendar.getTime();
//                }
//            }else if (personnelAttendanceConfig.getAttendancePattern().equals(ConfigAttendancePattern.attendanceTreasure.getIndex())){  //考勤宝
//                Date beginTime = NewDateUtils.joinDateTimeString(openDate, NewDateUtils.dateToString(personnelAttendanceConfig.getBeginTime(), "HH:mm:ss")); //上班时间，拼接上年月日
//                Short lateLimit = personnelAttendanceConfig.getLateLimit();  //迟到时限(分钟)
//                long lateLimitMills = lateLimit * 60 * 1000; //将分钟转换成毫秒
//                long timeInterval = beginTime.getTime() + lateLimitMills;  //开始时间+迟到时限
//                result = NewDateUtils.dateFromString(NewDateUtils.dateToString(timeInterval,"yyyy-MM-dd HH:mm:ss"),"yyyy-MM-dd HH:mm:ss");
//            }
//        }
//        return result;
//    }

//    @Override
//    public Date getBeginTime(Integer oid, Integer deptId, Date openDate) {
//        PersonnelAttendanceConfig config = this.getPersonnelAttendanceConfigByDept(oid, deptId, openDate);
//        if (config != null) {
//            return config.getBeginTime();
//        } else {
//            return null;
//        }
//    }

    public Map<String, Object> returnTime(Integer oid, Integer deptId, Date openDate, Map<String, Object> map) {
        Map<String, Object> result = map;
        PersonnelAttendanceConfig personnelAttendanceConfigs = getPersonnelAttendanceConfigByDept(oid, deptId, openDate);
        if (personnelAttendanceConfigs != null) {
            WorkAttendanceService.ConfigAttendancePattern attendancePattern = WorkAttendanceService.ConfigAttendancePattern.getByIndex(personnelAttendanceConfigs.getAttendancePattern()); //考勤模式:1-考勤宝,2-手工录入(默认)//考勤人员的来源:1-手工录入(默认),2-打卡
            Date date1 = personnelAttendanceConfigs.getBeginTime();   //上班时间
            Date date2 = personnelAttendanceConfigs.getEndTime();   //下班时间
            Date date3 = personnelAttendanceConfigs.getBreakBegin();   //午休开始时间(上午下班时间)
            Date date4 = personnelAttendanceConfigs.getBreakEnd();   //午休结束时间(下午上班时间)
            result.put("beginTime", NewDateUtils.dateToString(date1, "HH:mm"));  //上班时间
            result.put("endTime", NewDateUtils.dateToString(date2, "HH:mm")); //下班时间
            result.put("breakBegin", NewDateUtils.dateToString(date3, "HH:mm"));  //午休开始时间(上午下班时间)
            result.put("breakEnd", NewDateUtils.dateToString(date4, "HH:mm")); //午休结束时间(下午上班时间)
            result.put("attendancePattern",attendancePattern.getIndex()); //考勤模式:1-考勤宝,2-手工录入
            result.put("source",attendancePattern.getSource()); //来源:1-手工录入(默认),2-打卡
        }
        return result;
//        PersonnelAttendanceConfig personnelAttendanceConfigs = this.getPersonnelAttendanceConfigByDept(oid, deptId, openDate);
//        if (personnelAttendanceConfigs != null) {
//            Integer source = 1; //考勤模式:1-考勤宝,2-手工录入(默认)
//            Integer sourceUser = 2; //考勤人员的来源:1-手工录入(默认),2-打卡
//            if (ConfigAttendancePattern.manualEntry.getIndex().compareTo(personnelAttendanceConfigs.getAttendancePattern())==0){
//                source = 2;
//                sourceUser = 1;
//            }
//            Date date1 = personnelAttendanceConfigs.getBeginTime();   //上班时间
//            Date date2 = personnelAttendanceConfigs.getEndTime();   //下班时间
//            Date date3 = personnelAttendanceConfigs.getBreakBegin();   //午休开始时间(上午下班时间)
//            Date date4 = personnelAttendanceConfigs.getBreakEnd();   //午休结束时间(下午上班时间)
//            map.put("beginTime", NewDateUtils.dateToString(date1, "HH:mm"));  //上班时间
//            map.put("endTime", NewDateUtils.dateToString(date2, "HH:mm")); //下班时间
//            map.put("breakBegin", NewDateUtils.dateToString(date3, "HH:mm"));  //午休开始时间(上午下班时间)
//            map.put("breakEnd", NewDateUtils.dateToString(date4, "HH:mm")); //午休结束时间(下午上班时间)
//            map.put("source",source); //考勤模式:1-考勤宝,2-手工录入
//            map.put("sourceUser",source); //来源:1-手工录入(默认),2-打卡
//        } else {
//            map.put("beginTime", null);  //上班时间
//            map.put("endTime", null); //下班时间
//            map.put("breakBegin", null);  //午休开始时间(上午下班时间)
//            map.put("breakEnd", null); //午休结束时间(下午上班时间)
//            map.put("source",2);//考勤模式:1-考勤宝,2-手工录入(默认)
//            map.put("sourceUser",1); //来源:1-手工录入(默认),2-打卡
//        }
//        return map;
    }

    @Override  //type 1-每日考勤录入时间 2-上班时间 3-下班时间
    public Map<String, Object> returnTime(Integer oid, Integer deptId, Date openDate, Map<String, Object> map, Integer type) {
        PersonnelAttendanceConfig personnelAttendanceConfigs = this.getPersonnelAttendanceConfigByDept(oid, deptId, openDate);
        if (personnelAttendanceConfigs != null) {
            Integer source = 1; //考勤模式:1-考勤宝,2-手工录入
            if (ConfigAttendancePattern.manualEntry.getIndex().compareTo(personnelAttendanceConfigs.getAttendancePattern())==0){
                source = 2;
            }
            Date date3 = null;  //考勤录入时间
            if (personnelAttendanceConfigs.getId() != null) {
                if (personnelAttendanceConfigs.getOldCreateTime() != null && personnelAttendanceConfigs.getEffectDate().getTime() > new Date().getTime()) {
                    date3 = personnelAttendanceConfigs.getOldCreateTime();
                } else {
                    date3 = personnelAttendanceConfigs.getInputTime();
                }
            }
            Date date1 = personnelAttendanceConfigs.getBeginTime();   //上班时间
            Date date2 = personnelAttendanceConfigs.getEndTime();   //下班时间
            if (type != null) {
                if (type == 1) {
                    if (date3!=null) {
                        map.put("inputTime", NewDateUtils.dateToString(date3, "HH:mm")); //考勤录入时间
                    }else {
                        map.put("inputTime", null); //考勤录入时间
                    }
                } else if (type == 2) {
                    map.put("beginTime", NewDateUtils.dateToString(date1, "HH:mm"));  //上班时间
                } else if (type == 3) {
                    map.put("endTime", NewDateUtils.dateToString(date2, "HH:mm")); //下班时间
                }else if (type == 4){
                    Date date5 = personnelAttendanceConfigs.getBreakBegin();   //上班时间
                    Date date6 = personnelAttendanceConfigs.getBreakEnd();   //下班时间
                    map.put("beginTime", NewDateUtils.dateToString(date1, "HH:mm"));  //上班时间
                    map.put("endTime", NewDateUtils.dateToString(date2, "HH:mm")); //下班时间
                    map.put("breakBegin", NewDateUtils.dateToString(date5, "HH:mm"));  //午休上班时间
                    map.put("breakEnd", NewDateUtils.dateToString(date6, "HH:mm")); //午休下班时间
                }
            } else {
                if (date3!=null) {
                    map.put("inputTime", NewDateUtils.dateToString(date3, "HH:mm")); //考勤录入时间
                }else {
                    map.put("inputTime", null); //考勤录入时间
                }
                map.put("beginTime", NewDateUtils.dateToString(date1, "HH:mm"));  //上班时间
                map.put("endTime", NewDateUtils.dateToString(date2, "HH:mm")); //下班时间
            }
            map.put("leaveWork",personnelAttendanceConfigs.getLeaveWork());//请假到岗是否使用考勤宝:0-不使用(默认,false),1-使用(true)
            map.put("middleBreak",personnelAttendanceConfigs.getMiddleBreak()); //是否中间休息(是否中午考勤),true-是,false-否
            map.put("workOrNo", 1);  //有设置考勤的
            map.put("source",source); //考勤模式:1-考勤宝,2-手工录入
        } else {    // 系统开始时间那一天的考勤规则不包含的部门和人员 (后来重新设置了考勤规则，进行了考勤的)
            if (type != null) {
                if (type == 1) {
                    map.put("inputTime", null); //考勤录入时间
                } else if (type == 2) {
                    map.put("beginTime", null);  //上班时间
                } else if (type == 3) {
                    map.put("endTime", null); //下班时间
                }else if (type == 4){
                    map.put("beginTime", null);  //上班时间
                    map.put("endTime", null); //下班时间
                }
            } else {
                map.put("inputTime", null); //考勤录入时间
                map.put("beginTime", null);  //上班时间
                map.put("endTime", null); //下班时间
            }
            map.put("leaveWork",null);
            map.put("middleBreak",null);
            map.put("workOrNo", 0);  //没有设置考勤的
            map.put("source",2); //考勤模式:1-考勤宝,2-手工录入(默认)
        }
        return map;
    }

    @Override  //根据考勤作息时间的设置，来判断此加班是什么类型的加班（即加班的type为什么值）
    public String overTimeType(String type, User user, Date begin, Integer dept) {
        String type1 = type;
        Date startUsingSystemTime = workAttendanceService.getStartUsingSystemTime(user.getOid());  //获取系统时间
        if (startUsingSystemTime != null && startUsingSystemTime.getTime() <= begin.getTime()) {     //是否已使用了考勤系统

            PersonnelAttendanceConfig personnelAttendanceConfigs = this.getPersonnelAttendanceConfigByDept(user.getOid(), dept, begin);
            if (personnelAttendanceConfigs != null) {    //说明此员工参与考勤
                PersonnelAttendanceException personnelAttendanceException = this.getPersonnelAttendanceExceptionByOid(user.getOid(), NewDateUtils.today(begin), null);
                if (personnelAttendanceException != null) {
                    if ("1".equals(personnelAttendanceException.getType())) {    //1-上班改休息(假)，2-休息改上班(班)
                        type1 = "2";    //1-工作日加班 2-周末假日加班 3-法定节假日加班'
                    } else {
                        type1 = "1";    //1-工作日加班 2-周末假日加班 3-法定节假日加班'
                    }
                }
            }
        }
        return type1;
    }

//    @Override   //oneDay(1-以前的日期，需要默认旷工的)
//    public void updateYesterdayAttendance(PersonnelAttendanceUser py, User user, Date yesterday, Date inputTime, Date start1, String newDate, Integer oneDay) throws ParseException {
//        User user1 = userService.getUserByID(py.getUser());
//        Date amAttendance = null;
//        Date amAttendance1 = null;
//        if ("0".equals(py.getBeginState())) {   //昨天上班考勤未录入
//            py.setBeginState("7");
//            py.setType("7");
//            if (inputTime!=null) {
//                amAttendance = DateUtils.getTime(yesterday, inputTime);
//                amAttendance1 = DateUtils.getTime(start1, inputTime);
//            }
//            py.setAmAttendance(amAttendance);  // 上班考勤录入时间
//
//            List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetails = this.getPersonnelAttendanceUserDetailBykg(null, py.getUser(), py.getAttendanceDate(), "3");  //取系统默认的旷工
//            if (personnelAttendanceUserDetails.size() <= 0) {
//                this.addPersonnelAttendanceUserDetail(null, null, "7", "3", null,null, null, "系统默认旷工", py, user, null, null, null, amAttendance);
//                Byte absenteeismeNum = personnelAttendanceMonthlyService.GetAbsenteeisme(user1, yesterday);  //旷工次数
//                personnelAttendanceMonthlyService.SetAbsenteeisme(user1, yesterday, (byte) (absenteeismeNum + 1));
//            }
//
//        }
//
//        py.setUpdator(user.getUserID());
//        py.setUpdateDate(new Date());
//        py.setUpdateName(user.getUserName());
////        py.setUpdateDate(DateUtils.getTime(start1, inputTime));
//        py.setUpdateDate(amAttendance1);
//        if (oneDay != null && oneDay == 1) {   //今天之前的日期
//            if ("0".equals(py.getType()) || "1".equals(py.getType()) || "7".equals(py.getType())) {
//                py.setType("7");  //1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
//            } else {
//                py.setType("9");  //1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
//            }
//            py.setEndState("7");  //下班状态:0-未考勤,1-已考勤,2-请假，7-旷工(系统默认的旷工)
////            py.setPmAttendance(DateUtils.getTime(start1, inputTime));
//            py.setPmAttendance(amAttendance1);
//
//            List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetails = this.getPersonnelAttendanceUserDetailBykg(null, py.getUser(), py.getAttendanceDate(), "3");  //取系统默认的旷工
//            if (personnelAttendanceUserDetails.size() <= 0) {
////                this.addPersonnelAttendanceUserDetail(null, null, "7", "3", null,null, null, "系统默认旷工", py, user, null, null, null, DateUtils.getTime(start1, inputTime));
//                this.addPersonnelAttendanceUserDetail(null, null, "7", "3", null,null, null, "系统默认旷工", py, user, null, null, null, amAttendance1);
//
//                Byte absenteeismeNum = personnelAttendanceMonthlyService.GetAbsenteeisme(user1, yesterday);  //旷工次数
//                personnelAttendanceMonthlyService.SetAbsenteeisme(user1, yesterday, (byte) (absenteeismeNum + 1));
//            }
//        }
//        else if (inputTime != null && NewDateUtils.dateFromString(newDate, "HH:mm").after(inputTime)) {   //当前时间在考勤录入时间之后，考勤录入为旷工
//            if ("0".equals(py.getType()) || "1".equals(py.getType()) || "7".equals(py.getType())) {
//                py.setType("7");  //1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
//            } else {
//                py.setType("9");  //1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
//            }
//            py.setEndState("7");  //下班状态:0-未考勤,1-已考勤,2-请假，7-旷工(系统默认的旷工)
////            py.setPmAttendance(DateUtils.getTime(start1, inputTime));
//            py.setPmAttendance(amAttendance1);
//
//            List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetails = this.getPersonnelAttendanceUserDetailBykg(null, py.getUser(), py.getAttendanceDate(), "3");  //取系统默认的旷工
//            if (personnelAttendanceUserDetails.size() <= 0) {
////                this.addPersonnelAttendanceUserDetail(null, null, "7", "3", null,null, null, "系统默认旷工", py, user, null, null, null, DateUtils.getTime(start1, inputTime));
//                this.addPersonnelAttendanceUserDetail(null, null, "7", "3", null,null, null, "系统默认旷工", py, user, null, null, null,amAttendance1);
//                Byte absenteeismeNum = personnelAttendanceMonthlyService.GetAbsenteeisme(user1, yesterday);  //旷工次数
//                personnelAttendanceMonthlyService.SetAbsenteeisme(user1, yesterday, (byte) (absenteeismeNum + 1));
//            }
//        }
//        personnelAttendanceUserDao.update(py);
//    }

    @Override   //user(考勤录入操作人)   userId(被考勤职工id)
    public PersonnelAttendanceUser addPersonnelAttendanceUser1(Integer oid, Integer deptId, User user, String beginState, String endState, String type, Integer userId, Date openDate, Date workBegin, Date workEnd,Date breakBegin, Date breakEnd,Integer source) {
        PersonnelAttendanceUser personnelAttendanceUser = new PersonnelAttendanceUser();
        if (user != null) {
            personnelAttendanceUser.setCreator(user.getUserID());
            personnelAttendanceUser.setCreateName(user.getUserName());
            personnelAttendanceUser.setUpdator(user.getUserID());
            personnelAttendanceUser.setUpdateName(user.getUserName());
        } else {
            personnelAttendanceUser.setCreateName("系统");
            personnelAttendanceUser.setUpdateName("系统");
        }
        personnelAttendanceUser.setCreateDate(new Date());
        personnelAttendanceUser.setUser(userId);
        personnelAttendanceUser.setAttendanceDate(openDate);  //考勤日期
        personnelAttendanceUser.setOrg(oid);
        personnelAttendanceUser.setBeginTime(workBegin);  //上班时间
        personnelAttendanceUser.setEndTime(workEnd);   //下班时间
        personnelAttendanceUser.setBreakBegin(breakBegin);  //午休开始时间(上午下班时间)
        personnelAttendanceUser.setBreakEnd(breakEnd);   //午休结束时间(下午上班时间)
        personnelAttendanceUser.setEndState(endState);
        personnelAttendanceUser.setBeginState(beginState);  //上班状态:0-未考勤,1-已考勤，2-请假, 7-旷工(系统默认的旷工)
        personnelAttendanceUser.setType(Byte.valueOf(type));  //0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
        personnelAttendanceUser.setDept(deptId);
        personnelAttendanceUser.setSource(source);  //来源:1-手工录入(默认),2-打卡
        personnelAttendanceUserDao.save(personnelAttendanceUser);
        return personnelAttendanceUser;
    }

    @Override     //将原来的考勤部门添加到考勤部门历史表中
    public void pdcToPdch(User user, PersonnelAttendanceConfig personnelAttendanceConfig, PersonnelAttendanceConfigHistory personnelAttendanceConfigHistory) {
        for (PersonnelAttendanceDepartmentConfig departmentConfig : personnelAttendanceConfig.getPersonnelAttendanceDepartmentConfigHashSet()) {
            PersonnelAttendanceDepartmentConfigHistory personnelAttendanceDepartmentConfigHistory = new PersonnelAttendanceDepartmentConfigHistory();
            BeanUtils.copyPropertiesIgnoreNull(departmentConfig, personnelAttendanceDepartmentConfigHistory);  //相同的部分已复制
            personnelAttendanceDepartmentConfigHistory.setRule(personnelAttendanceConfigHistory);  //考勤历史表的id
            personnelAttendanceDepartmentConfigHistory.setUpdateDate(new Date());
            personnelAttendanceDepartmentConfigHistory.setUpdator(user.getUserID());
            personnelAttendanceDepartmentConfigHistory.setUpdateName(user.getUserName());
            personnelAttendanceDepartmentConfigHistoryDao.save(personnelAttendanceDepartmentConfigHistory);
        }
    }

    @Override     //新添加考勤设置部门历史
    public void addPersonnelAttendanceDepartmentConfigHistory(User user, PersonnelAttendanceConfigHistory personnelAttendanceConfigHistory, PersonnelAttendanceRecord record, Integer deptId) {
        PersonnelAttendanceDepartmentConfigHistory personnelAttendanceDepartmentConfigHistory = new PersonnelAttendanceDepartmentConfigHistory();
        personnelAttendanceDepartmentConfigHistory.setCreator(user.getUserID());
        personnelAttendanceDepartmentConfigHistory.setCreateDate(new Date());
        personnelAttendanceDepartmentConfigHistory.setCreateName(user.getUserName());
        personnelAttendanceDepartmentConfigHistory.setDept(deptId);
        personnelAttendanceDepartmentConfigHistory.setRule(personnelAttendanceConfigHistory);
        personnelAttendanceDepartmentConfigHistory.setOrg(user.getOid());
        personnelAttendanceDepartmentConfigHistory.setRecord(record.getId());
        personnelAttendanceDepartmentConfigHistoryDao.save(personnelAttendanceDepartmentConfigHistory);
    }

    @Override
    public PersonnelAttendanceConfig addPersonnelAttendanceConfigByDetail(User user, PersonnelAttendanceRecord record, PersonnelAttendanceConfig personnelAttendanceConfig, JSONObject jo, String effectDate,
             Integer attendancePattern,Short lateLimit,Short earlyLimit,Boolean leaveWork) {
//        if (StringUtils.isNotEmpty((String)jo.get("inputTime"))) {
//            personnelAttendanceConfig.setInputTime(DateUtils.hourM(jo.getString("inputTime")));//每日考勤录入时间(如未按时录入,则为旷工)
//        }
//        personnelAttendanceConfig.setBeginTime(DateUtils.hourM(jo.getString("beginTime"))); //上班时间
//        personnelAttendanceConfig.setEndTime(DateUtils.hourM(jo.getString("endTime")));  //下班时间
        Object inputTimeOb = jo.get("inputTime");
        if (inputTimeOb!=null) {
            String inputTimeString = (String) inputTimeOb;
            personnelAttendanceConfig.setInputTime(NewDateUtils.dateFromString(inputTimeString, "HH:mm"));//每日考勤录入时间(如未按时录入,则为旷工)
        }
        String beginTimeString = jo.getString("beginTime");
        personnelAttendanceConfig.setBeginTime(NewDateUtils.dateFromString(beginTimeString, "HH:mm")); //上班时间
        String endTimeString = jo.getString("endTime");
        personnelAttendanceConfig.setEndTime(NewDateUtils.dateFromString(endTimeString, "HH:mm"));  //下班时间
        if (!"".equals(effectDate)) {
            personnelAttendanceConfig.setOpenDate(NewDateUtils.dateFromString(effectDate, "yyyy-MM-dd")); //规则启用日期
        }
        personnelAttendanceConfig.setType(jo.getString("type"));//类型:1-正常班,2-倒班
        personnelAttendanceConfig.setMiddleBreak(jo.getBoolean("isBreak"));//是否中间休息(是否中午考勤),true-是
//        personnelAttendanceConfig.setBreakBegin(DateUtils.hourM(jo.getString("breakBegin")));//午休开始时间
//        personnelAttendanceConfig.setBreakEnd(DateUtils.hourM(jo.getString("breakEnd")));//午休结束时间
        String breakBeginString = jo.getString("breakBegin");
        personnelAttendanceConfig.setBreakBegin(NewDateUtils.dateFromString(breakBeginString, "HH:mm"));//午休开始时间
        String breakEndString = jo.getString("breakEnd");
        personnelAttendanceConfig.setBreakEnd(NewDateUtils.dateFromString(breakEndString, "HH:mm"));//午休结束时间
        personnelAttendanceConfig.setRecord(record.getId());
        personnelAttendanceConfig.setOrg(user.getOid());
        personnelAttendanceConfig.setEnabled(true);//是否有效,true-有效,false-无效
        personnelAttendanceConfig.setCreator(user.getUserID());
        personnelAttendanceConfig.setCreateDate(new Date());
        personnelAttendanceConfig.setCreateName(user.getUserName());
//        personnelAttendanceConfig.setOperation("1"); //操作：0-正常,1-增,2-删,3-改
        personnelAttendanceConfig.setOperation(WorkAttendanceOldService.AttendanceConfigOperation.create.getIndex());//操作：0-正常,1-增,2-删,3-改,4-修改录入日期,5-修改迟到早退和请假期设置
        if (attendancePattern!=null&&1==attendancePattern){
            personnelAttendanceConfig.setAttendancePattern(WorkAttendanceOldService.ConfigAttendancePattern.attendanceTreasure.getIndex()); //考勤模式:1-考勤宝,2-手工录入(默认)
            personnelAttendanceConfig.setLateLimit(lateLimit);//迟到时限(分钟)
            personnelAttendanceConfig.setEarlyLimit(earlyLimit); //早退时限(分钟)
            personnelAttendanceConfig.setLeaveWork(leaveWork); //请假到岗是否使用考勤宝:0-不使用(默认,false),1-使用(true)
        }else {
            personnelAttendanceConfig.setAttendancePattern(WorkAttendanceOldService.ConfigAttendancePattern.manualEntry.getIndex()); //2-手工录入(默认)
        }
        personnelAttendanceConfigDao.save(personnelAttendanceConfig);  //新增

        return personnelAttendanceConfig;
    }

//    //系统默认今天和昨天旷工的接口  user(操作录入考勤的人员)
//    public void defaultAbsenteeism(Date today, String inputTime, PersonnelAttendanceUser personnelAttendanceUser) throws ParseException {
//        SimpleDateFormat sdf1 = new SimpleDateFormat("HH:mm");
//        User attendanceUser = userService.getUserByID(personnelAttendanceUser.getUser());
//        personnelAttendanceMonthlyService.AddUpdateUserInfo(attendanceUser, today);
//
//        if ("0".equals(personnelAttendanceUser.getBeginState())) {
//            personnelAttendanceUser.setBeginState("7");  //上班状态:0-未考勤,1-已考勤,2-请假
//            personnelAttendanceUser.setType("7");  //1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 0-其他
//            personnelAttendanceUser.setAmAttendance(NewDateUtils.joinDateTimeString(today, inputTime));
//            personnelAttendanceUser.setUpdateDate(NewDateUtils.joinDateTimeString(today, inputTime));
//            this.addPersonnelAttendanceUserDetail(null, null, "7", "3", null,null, null, "系统默认旷工", personnelAttendanceUser, null, null, null, null, DateUtils.getTime(today, sdf1.parse(inputTime)));
//            Byte absenteeismeNum = personnelAttendanceMonthlyService.GetAbsenteeisme(attendanceUser, today);  //旷工次数
//            personnelAttendanceMonthlyService.SetAbsenteeisme(attendanceUser, today, (byte) (absenteeismeNum + 1));
//        }
//
//        Date yesterday = NewDateUtils.yesterday(today);
//        yesterday = this.getDateByWork(yesterday, personnelAttendanceUser.getOrg());
//        PersonnelAttendanceUser py = this.getPersonnelAttendanceUserByUserIdAndId(null, attendanceUser.getUserID(), yesterday, "0");//昨天的考勤,下班未录入的
//        if (py != null) {
//            if ("0".equals(py.getBeginState())) {
//                py.setBeginState("7");  //上班状态:0-未考勤,1-已考勤，2-请假  7-旷工(系统默认的旷工)
//                py.setType("7");  //1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
//                py.setAmAttendance(NewDateUtils.joinDateTimeString(yesterday, inputTime));
//            }
////                        py.setUpdator();
//            py.setUpdateDate(new Date());
//            py.setUpdator(0);
//            py.setUpdateName("系统");
//            py.setEndTime(personnelAttendanceUser.getEndTime());
//            if (!"1".equals(py.getType()) && !"7".equals(py.getType())) {
//                py.setType("9");  //1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
//            } else {
//                py.setType("7");  //1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
//            }
//            py.setEndState("7");//下班状态:0-未考勤,1-已考勤，2-请假, 7-旷工(系统默认的旷工)
//            py.setPmAttendance(NewDateUtils.joinDateTimeString(today, inputTime));
//            py.setUpdateDate(NewDateUtils.joinDateTimeString(today, inputTime));
//            personnelAttendanceUserDao.update(py);
//            List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetails = this.getPersonnelAttendanceUserDetailByBusiness(py.getId(), "7", "3", null, null);
//            if (personnelAttendanceUserDetails.size() <= 0) {
//                this.addPersonnelAttendanceUserDetail(null, null, "7", "3", null,null, null, "系统默认旷工", py, null, null, null, null, DateUtils.getTime(today, sdf1.parse(inputTime)));
//                Byte absenteeismeNum = personnelAttendanceMonthlyService.GetAbsenteeisme(attendanceUser, yesterday);  //旷工次数
//                personnelAttendanceMonthlyService.SetAbsenteeisme(attendanceUser, yesterday, (byte) (absenteeismeNum + 1));
//            }
//        }
//    }

    //返回系统开始使用时间
    @Override
    @Deprecated //多余，直接调用getStartUsingSystemTime就行
    public String returnStartUsingSystemTime(Integer oid) {
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date systemTime = workAttendanceService.getStartUsingSystemTime(oid);  //获取系统时间
        String startUsingSystemTime = null;
        if (systemTime != null) {
//            startUsingSystemTime = sdf.format(systemTime);  //系统开始使用时间
            startUsingSystemTime = NewDateUtils.dateToString(systemTime, "yyyy-MM-dd");  //系统开始使用时间
        }
        return startUsingSystemTime;
    }

//    /**
//     * <AUTHOR>
//     * @date 2018/4/20 17:03
//     * 取距离今天/昨天最近的日期，此日期必须是“班” 1-假，2-班
//     */
//    public Date getDateByWork(Date currendTime, Integer oid) {
//        return workAttendanceService.getPreWorkDate(currendTime, oid);
////        PersonnelAttendanceException personnelAttendanceExceptionYesterday = this.getPersonnelAttendanceExceptionByOid(oid, currendTime, null);
////        if (personnelAttendanceExceptionYesterday != null) {
////            while (!"".equals(personnelAttendanceExceptionYesterday.getType()) && personnelAttendanceExceptionYesterday.getType() != null && "1".equals(personnelAttendanceExceptionYesterday.getType())) {
////                currendTime = NewDateUtils.yesterday(currendTime);
////                personnelAttendanceExceptionYesterday = this.getPersonnelAttendanceExceptionByOid(oid, currendTime, null);
////                if (personnelAttendanceExceptionYesterday == null) {
////                    currendTime = null;
////                    break;
////                }
////            }
////        } else {
////            currendTime = null;
////        }
////        return currendTime;
//    }

    /**
     * 取下一个工作日日期
     *
     * @param currendDate 当前日期
     * @param oid         机构id
     * @return
     * <AUTHOR>
     * @since 2019/7/19 10:42
     */
    @Override
    public Date getNextDateByWork(Date currendDate, Integer oid) {
        Date tomorrow = NewDateUtils.tomorrow(currendDate);
        String hql = "from PersonnelAttendanceException where org = :oid and type = 2 and exceptionDate >= :tomorrow order by exceptionDate,id";//type 1-假 2-班
        HashMap<String, Object> params = new HashMap<>();
        params.put("oid", oid);
        params.put("tomorrow", tomorrow);
        PersonnelAttendanceException exception = (PersonnelAttendanceException) personnelAttendanceExceptionDao.getByHQLWithNamedParams(hql, params);
        if (exception != null) {
            return exception.getExceptionDate();
        } else {
            return NewDateUtils.changeYear(new Date(System.currentTimeMillis()), 1);
        }
    }

    //系统里默认将添加添加到考勤中   type 1-无需考勤 2-正常考勤   attendanceUserId(要考勤的人员)  workBegin(上班开始时间)  workEnd(上班结束时间) deptId(要考勤人员部门id)
    //  generalUser(操作考勤的人员，此默认总务)
    public void defaultOverTime(PersonnelAttendanceUser personnelAttendanceUser, User attendanceUser, Integer deptId, Date start1, Integer type, Date workBegin, Date workEnd,Date breakBegin, Date breakEnd,Integer source) {
        List<PersonnelOvertime> personnelOvertimes = overtimeService.getPersonnelOvertimeListByTime(attendanceUser.getUserID(), 4, NewDateUtils.today(start1), NewDateUtils.getLastTimeOfDay(start1));
        if (personnelOvertimes.size() > 0) {
            if (type == 1) {
                personnelAttendanceUser = workAttendanceService.getPersonnelAttendanceUserByUserIdAndId(null, attendanceUser.getUserID(), start1, null);
                if (personnelAttendanceUser == null) {
                    personnelAttendanceUser = this.addPersonnelAttendanceUser1(attendanceUser.getOid(), deptId, null, "8", "8", "8", attendanceUser.getUserID(), start1, workBegin, workEnd,breakBegin,breakEnd,source);
                }
            }
            Double overDuration = 0.0;
            Integer workOverNum = 0;  //此次计入考勤加班的次数

            for (PersonnelOvertime p : personnelOvertimes) {
                List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetails = workAttendanceService.getPersonnelAttendanceUserDetailByBusiness(null, "8", null, p.getId(), null);
                if (personnelAttendanceUserDetails.size() <= 0) {
                    workAttendanceService.addPersonnelAttendanceUserDetail(p.getActualBeginTime(), p.getActualEndTime(), "8", "1", p.getType(),null, p.getId(), p.getActualReason(), personnelAttendanceUser, null, String.valueOf(p.getApproveDuration()), null, null, new Date());
//                    this.addDefaultClockRecord(p,1,personnelAttendanceUser,personnelAttendanceUserDetail.getId());
                }else {
                    PersonnelAttendanceUserDetail personnelAttendanceUserDetail = personnelAttendanceUserDetails.get(0);
                    personnelAttendanceUserDetail.setBeginTime(p.getActualBeginTime());
                    personnelAttendanceUserDetail.setEndTime(p.getActualEndTime());
                    personnelAttendanceUserDetail.setDuration(p.getApproveDuration());
                    personnelAttendanceUserDetailDao.update(personnelAttendanceUserDetail);  //可能是打卡的已经生产了详情，这次将实际的加班时间和时长更新下。
                }
                overDuration = overDuration + p.getApproveDuration();
                workOverNum += 1;
            }
            Pair<Byte, Float> overResult = personnelAttendanceMonthlyService.GetOvertime(attendanceUser, start1);
            personnelAttendanceMonthlyService.SetOvertime(attendanceUser, start1, (byte) (overResult.getLeft() + workOverNum), Float.valueOf(overResult.getRight() + overDuration.floatValue()));
        }

        //计划加班申报的，添加默认打卡
        if (2==type){  //无需考勤的没有默认打卡，只有正常考勤有
            List<PersonnelOvertime> personnelOvertimes1 = overtimeService.getPersonnelOvertimeListByBeginTime(attendanceUser.getUserID(), 2, NewDateUtils.today(personnelAttendanceUser.getAttendanceDate()), NewDateUtils.getLastTimeOfDay(personnelAttendanceUser.getAttendanceDate()), "0", null, null);
            for (PersonnelOvertime p1:personnelOvertimes1) {
                workAttendanceService.addDefaultClockRecord(p1,1,personnelAttendanceUser,null);
            }

        }
    }

    /**
     * 获取需要考勤的部门
     *
     * @param currentTime
     * @param oid
     * @return 没有设部门人员的返回"0"，如果当前没有部门需考勤，返回空List
     */
    @Override
    public List<String> getDeptIds(Date currentTime, Integer oid) {
        String cacheName = "miners:Attendance:getDeptIds:" + oid + ":" + currentTime.getTime();
        Set<String> deptIds = (Set<String>) redisTemplate.opsForValue().get(cacheName);
        if (deptIds == null || deptIds.isEmpty()) {
            deptIds = new HashSet<>();
            List<PersonnelAttendanceConfig> personnelAttendanceConfigList = this.getPersonnelAttendanceConfigsByOpenDate(oid, 1, currentTime);
            if (personnelAttendanceConfigList.size() > 0) {
                String hql = "select dept from PersonnelAttendanceDepartmentConfig where ruleId=:ruleId";
                HashMap<String, Object> params = new HashMap<>();
                for (PersonnelAttendanceConfig personnelAttendanceConfig : personnelAttendanceConfigList) {
//                    System.out.println("getDeptIds:");
//                    System.out.println(personnelAttendanceConfig.getId());
                    params.put("ruleId", personnelAttendanceConfig.getId());
                    List<Integer> configDeptIds = personnelAttendanceDepartmentConfigDao.getListByHQLWithNamedParams(hql, params);
                    for (Integer detptId : configDeptIds) {
//                        System.out.println("getDeptIds deptId=:"+detptId);
                        deptIds.add(detptId.toString());
                    }
//                    System.out.println("getPersonnelAttendanceDepartmentConfigHashSet size="+ configDeptIds.size());
                }
            }
            if (!deptIds.isEmpty()) {
                redisTemplate.opsForValue().set(cacheName, deptIds, 10, TimeUnit.DAYS);
            }
//            System.out.println( "from SQL " + cacheName + "| time" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(currentTime) + " get size=" + deptIds.size());
//        } else {
//            System.out.println( cacheName + "| time" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(currentTime) + " get size=" + deptIds.size());
        }
        return new ArrayList<>(deptIds);
    }

    public List<UserDepartmentHistory> getDailyUserMonitor(Date currentTime, Integer oid) {
        String hql = "from UserDepartmentHistory where org=:org and effdt=:effdt and isUsed=:isUsed";
        HashMap<String, Object> params = new HashMap<>();
        params.put("org", oid);
        params.put("effdt", NewDateUtils.today(currentTime));
        params.put("isUsed", false);
        return userDepartmentHistoryDao.getListByHQLWithNamedParams(hql, params);
    }

    public List<UserDepartmentHistory> getChangeUserMonitor(Date currentTime, Integer oid) {
        String cacheName = "miners:Attendance:changeUserMonitor:" + oid;
        Set<String> cacheStrings = (Set<String>) redisTemplate.opsForZSet().rangeByScore(cacheName, currentTime.getTime(), currentTime.getTime());
        List<UserDepartmentHistory> result;
        if (cacheStrings.isEmpty()) {
            String hql = "from UserDepartmentHistory where org=:org and effdt between :firstDate and :lastDate order by effdt";
            HashMap<String, Object> params = new HashMap<>();
            params.put("org", oid);
            params.put("firstDate", NewDateUtils.tomorrow(currentTime));
            params.put("lastDate", NewDateUtils.tomorrow());
            List<UserDepartmentHistory> list = userDepartmentHistoryDao.getListByHQLWithNamedParams(hql, params);
            HashMap<Integer, UserDepartmentHistory> map = new HashMap<>();
            UserDepartmentHistory tmp;
            for (UserDepartmentHistory rec : list) {
                userDepartmentHistoryDao.getSession().evict(rec);
                tmp = map.get(rec.getUser());
                if (tmp != null) {
                    tmp.setNewDeptId(rec.getNewDeptId());
                } else {
                    map.put(rec.getUser(), rec);
                }
            }
            result = new ArrayList<>(new HashSet<>(map.values()));
            redisTemplate.opsForZSet().add(cacheName, JSON.toJSONString(result), currentTime.getTime());
        } else {
            result = JSON.parseArray(cacheStrings.iterator().next(), UserDepartmentHistory.class);
//            System.out.println( cacheName + "| time" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(currentTime) + " get size=" + result.size());
        }
        return result;
    }

    public final void fillUserDtoAndAddList(UserDepartmentHistory item, String userName, List<UserDto> addList) {
        UserDto dto = userService.getUserDtoByUserId(item.getUser());
        if (StringUtils.isBlank(userName) || dto.getUserName().contains(userName)) {//模糊查询，不对忽略
            dto.setDepartment(item.getOldDeptId());
            if (item.getOldDeptId().equalsIgnoreCase("0")) {
                dto.setDepartName("");//其他
            } else {
                dto.setDepartName(orgService.getOrgByOid(Integer.valueOf(item.getOldDeptId()), OrgService.OrgType.department).getName());
            }
            addList.add(dto);
        }
    }

    public List<Integer> getUserChange(Date currentTime, Integer oid, List<String> deptIds, String userName, List<UserDto> addList) {
        List<Integer> result = new ArrayList<>();
        if (!deptIds.isEmpty()) {//只在考勤部门中处理
            List<UserDepartmentHistory> list = getChangeUserMonitor(currentTime, oid);
            if (!list.isEmpty()) {
                for (UserDepartmentHistory item : list) {
                    if (item.getOldDeptId() == null //当时还未入职的人员需要删除
                            || !deptIds.contains(item.getOldDeptId()) && item.getNewDeptId() != null && deptIds.contains(item.getNewDeptId())) {//当时在部门组外现在转入部门组的人需要删除
                        result.add(item.getUser());
                    }
                    if (addList != null//数组不为空
                            && (item.getNewDeptId() == null  //当时在职现在已离职的人员需要加入
                            || !deptIds.contains(item.getNewDeptId()))) {//这些人当时在部门组/部门中，需要加回来
                        fillUserDtoAndAddList(item, userName, addList);
                    }
                }
            }
        }
        return result;
    }

    public List<Integer> getNoUserChange(Date currentTime, Integer oid, List<String> deptIds, String deptId, String userName, List<UserDto> addList) {
        List<UserDepartmentHistory> list = getChangeUserMonitor(currentTime, oid);
        List<Integer> result = new ArrayList<>();
        if (!list.isEmpty()) {
            for (UserDepartmentHistory item : list) {
                if ((item.getOldDeptId() == null //当时还未入职的人员
                        || deptIds.contains(item.getOldDeptId())) //当时在需要考勤的部门
                        && item.getNewDeptId() != null && !deptIds.contains(item.getNewDeptId())) {//现在不在需要考勤的部门且未离职的人需要删除
                    result.add(item.getUser());
                }
                if (addList != null//数组不为空
                        && item.getOldDeptId() != null
                        && (deptId == null && !deptIds.contains(item.getOldDeptId()) || item.getOldDeptId().equals(deptId))
                        && (item.getNewDeptId() == null || deptIds.contains(item.getNewDeptId()))) {//旧部门无需考勤，现离职或在需要考勤的部门+当时不需要考勤，补充所有离职人员
                    fillUserDtoAndAddList(item, userName, addList);
                }
            }
        }
        return result;
    }

    public List<UserDto> getUser(Date currentTime, Integer oid, Integer userId, String deptId, String userName) {
        List<String> deptIds;
        deptIds = getDeptIds(currentTime, oid);
        if(!StringUtils.isBlank(deptId)) {
            if(deptIds.contains(deptId)) {//如果deptId是需要考勤的部门
                deptIds = new ArrayList<>(1) {{add(deptId);}};
            } else {//如果deptId部门无需考勤
                return Collections.emptyList();
            }
        }
        if (userId == null && userName == null) {
            return getUser(currentTime, oid, deptIds);
        } else {
            List<UserDto> addList = new ArrayList<>();
            List<Integer> removeUids = getUserChange(currentTime, oid, deptIds, userName, addList);
            List<UserDto> result = userService.getUserDtoByOidAndDeptIdsLocking(oid, userId, deptIds, userName, removeUids);
            if (!addList.isEmpty()) {
                result.addAll(addList);
            }
            return result;
        }
    }

    public List<UserDto> getNoUser(Date currentTime, Integer oid, Integer userId, String deptId, String userName) {
        List<UserDto> result = new ArrayList<>();
        List<String> deptIds = getDeptIds(currentTime, oid);
        if(!StringUtils.isBlank(deptId)) {
            if(deptIds.contains(deptId)) {//如果deptId是需要考勤的部门
                return Collections.emptyList();
            } else {//如果deptId部门无需考勤
                deptIds = new ArrayList<>(1) {{add(deptId);}};
            }
        }
        ArrayList<UserDto> addList = new ArrayList<>();
        List<Integer> removeUids;
        removeUids = getNoUserChange(currentTime, oid, deptIds, deptId, userName, addList);
        result = userService.getUserDtoByOidAndNoDeptIdsLocking(oid, userId, deptIds, userName, removeUids);
        if (!addList.isEmpty()) {
            result.addAll(addList);
        }
        return result;
    }

    /**
     * getUser 机构部门指定时间需要考勤的人员
     * <AUTHOR>
     * @since 4.0
     * @Param currentTime:
     * @Param oid:
     * @Param deptIds:
     * @return List<UserDto>
     * @date 2025-04-23 15:50:40
     **/
    public List<UserDto> getUser(Date currentTime, Integer oid, List<String> deptIds) {
        List<UserDto> result;
        if (deptIds == null || deptIds.isEmpty()) {
            result = Collections.emptyList();
        } else {
            String deptIdsString = StringUtils.join(deptIds, ",");
            String cacheName = "miners:Attendance:getUser:" + oid + ":" + currentTime.getTime() + ":" + deptIdsString;
            String userDtosString = (String) redisTemplate.opsForValue().get(cacheName);
            if (userDtosString == null || userDtosString.isEmpty()) {
                List<UserDto> addList = new ArrayList<>();
                List<Integer> removeUids = getUserChange(currentTime, oid, deptIds, null, addList);
                result = userService.getUserDtoByOidAndDeptIdsLocking(oid, null, deptIds, null, removeUids);
                if (!addList.isEmpty()) {
                    result.addAll(addList);
                }
                if (!result.isEmpty()) {
                    redisTemplate.opsForValue().set(cacheName, JSON.toJSONString(result), 10, TimeUnit.DAYS);
                }
            } else {
                result = JSON.parseArray(userDtosString, UserDto.class);
            }
        }
        return result;
    }
//    /**
//     * <AUTHOR>
//     * @date 2018/4/18 14:44
//     * 参与考勤人员或者不参与考勤人员
//     * type 1-参与考勤人员 2-不参与考勤人员
//     * oid 机构id ，deptId 部门id
//     */
//    public List<UserDto> getUser(Integer type, Date currentTime, Integer oid, Integer userId, Integer did, String userName) {
//        String deptId = null;
//        if (did != null) {
//            deptId = String.valueOf(did);
//        }
//        if (type == 1) {
//            return getUser(currentTime, oid, userId, deptId, userName);
//        } else {//if(type==2)
//            return getNoUser(currentTime, oid, userId, deptId, userName);
//        }
//
//    }

    //根据请假修改考勤上下班的状态
    public void beginAndEndStatus(PersonnelAttendanceUser personnelAttendanceUser, List<PersonnelLeave> personnelLeaves, Date start, String workBegin, String workEnd) {
        Integer aa = 0;
        Integer bb = 0;

        Long beginDate = NewDateUtils.joinDateTimeString(start, workBegin).getTime();
        Long endDate = NewDateUtils.joinDateTimeString(start, workEnd).getTime();
        for (PersonnelLeave personnelLeave : personnelLeaves) {
            Date leaveBegin = personnelLeave.getActualBeginTime();   //实际请假开始时间
            Date leaveEnd = personnelLeave.getActualEndTime();  //实际请假结束时间
            if (aa != 1) {
                if (leaveBegin.getTime() <= beginDate && leaveEnd.getTime() > beginDate) {   //请假开始时间包括实际上班时间，则上班状态为已请假
                    personnelAttendanceUser.setBeginState("5");  //上班状态:0-未考勤,1-已考勤，2-请假
                    personnelAttendanceUser.setAmAttendance(new Date());
                    personnelAttendanceUser.setUpdateDate(new Date());
                    aa = 1;  // 上班状态为已请假
                }
            }
            if (bb != 1) {
                if (leaveBegin.getTime() < endDate && leaveEnd.getTime() >= endDate) {  //实际请假结束时间包括实际下班时间，则下班状态为已请假
                    personnelAttendanceUser.setEndState("5");  //上班状态:0-未考勤,1-已考勤，2-请假
                    personnelAttendanceUser.setPmAttendance(new Date());
                    personnelAttendanceUser.setUpdateDate(new Date());
                    bb = 1;  //下班状态为已请假
                }
            }

            workAttendanceService.addPersonnelAttendanceUserDetail(leaveBegin, leaveEnd, "5", "1", personnelLeave.getType(),personnelLeave.getLeaveType(), personnelLeave.getId(), personnelLeave.getActualReason(), personnelAttendanceUser, null, null, null, null, new Date());
        }
    }

    @Override    //考勤录入的处理接口
    public void getAttendanceRecord(PersonnelAttendanceUser personnelAttendanceUser, String date, Integer dtype, User user, String kuangGong) {
        String sdf1 = "yyyy-MM-dd";
        PersonnelAttendanceUser py = workAttendanceService.getPersonnelAttendanceUserByUserIdAndId(personnelAttendanceUser.getId(), null, null, null);

        User attendanceUser = userService.getUserByID(py.getUser());   //要考勤的职工
        if (dtype == 1) {  //1-今天上班考勤
            py.setCreateName(user.getUserName());
            py.setCreator(user.getUserID());
            py.setBeginState(personnelAttendanceUser.getBeginState());  //上班状态:0-未考勤,1-已考勤
            if ("0".equals(py.getType()) || "1".equals(py.getType()) || "8".equals(py.getType())) {
                py.setType(Byte.valueOf(personnelAttendanceUser.getBeginState()));
            } else if (!"1".equals(personnelAttendanceUser.getType())) {
                py.setType((byte)9);
            }
            py.setUpdator(user.getUserID());
            py.setUpdateName(user.getUserName());
            py.setUpdateDate(new Date());
            py.setAmAttendance(new Date());//上午考勤时间
            personnelAttendanceUserDao.update(py);

            if ("2".equals(personnelAttendanceUser.getBeginState())) {
                workAttendanceService.addPersonnelAttendanceUserDetail(null, null, "2", "2", null,null, null, null, py, user, null, null, "1", new Date()); //上班 2-迟到
                personnelAttendanceMonthlyService.SetLate(attendanceUser, NewDateUtils.dateFromString(date, sdf1), true);
            }
        } else {     //2-昨天下班考勤2
            py.setCreateName(user.getUserName());
            py.setCreator(user.getUserID());
            py.setUpdator(user.getUserID());
            py.setUpdateDate(new Date());
            py.setUpdateName(user.getUserName());
            py.setEndState(personnelAttendanceUser.getEndState());  //下班状态:0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班,9-其它
            py.setPmAttendance(new Date());//下午考勤时间
            if ("1".equals(py.getType()) || "0".equals(py.getType()) || "8".equals(py.getType())) {
                py.setType(Byte.valueOf(personnelAttendanceUser.getEndState()));
            } else if (!"1".equals(personnelAttendanceUser.getEndState())) {
                py.setType((byte)9);//类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班,9-其它
            }
            personnelAttendanceUserDao.update(py);

            if ("3".equals(personnelAttendanceUser.getEndState())) {
                workAttendanceService.addPersonnelAttendanceUserDetail(null, null, "3", "2", null,null, null, null, py, user, null, null, "2", new Date()); //下班 3-早退
                personnelAttendanceMonthlyService.SetLeaveEarly(attendanceUser, NewDateUtils.dateFromString(date, sdf), true);
            }
        }

        //旷工信息  attendanceDetailId为修改的旷工id【下班考勤时有可能会修改旷工信息】
        JSONArray time = JSONArray.fromObject(kuangGong);
        Integer aa = 0;
        if (!"".equals(kuangGong) && kuangGong != null) {
            for (int i = 0; i < time.size(); i++) {
                JSONObject jo = JSONObject.fromObject(time.get(i));
                if (!MyStrings.nulltoempty(jo.getString("attendanceDetailId")).isEmpty()) {
                    PersonnelAttendanceUserDetail personnelAttendanceUserDetail = this.getPersonnelAttendanceUserDetailByDeatilId(Integer.parseInt(jo.getString("attendanceDetailId")));
                    if (personnelAttendanceUserDetail != null) {
                        if (!"".equals(jo.getString("begin"))) {
                            personnelAttendanceUserDetail.setBeginTime(NewDateUtils.dateFromString(jo.getString("begin"), sdf));  //上班时间(开始时间)
                        }
                        if (!"".equals(jo.getString("end"))) {
                            personnelAttendanceUserDetail.setEndTime(NewDateUtils.dateFromString(jo.getString("end"), sdf));  //下班时间(结束时间)
                        }
                        if(personnelAttendanceUserDetail.getEndTime()!=null && personnelAttendanceUserDetail.getBeginTime()!=null) {
                            personnelAttendanceUserDetail.setDuration(leaveService.getDuration(personnelAttendanceUserDetail.getEndTime(), personnelAttendanceUserDetail.getBeginTime()));
                        }
                        personnelAttendanceUserDetailDao.update(personnelAttendanceUserDetail);
                    }
                } else {
                    if (!"".equals(jo.getString("begin")) || !"".equals(jo.getString("end"))) {
                        if (aa != 1) {
                            aa = 1;
                        }

                        Date beginDate;
                        try {
                            beginDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(jo.getString("begin"));
                        } catch (ParseException e) {
                            beginDate = null;
                        }
                        Date endDate;
                        try {
                            endDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(jo.getString("end"));
                        } catch (ParseException e) {
                            endDate = null;
                        }

                        //新添加旷工
                        workAttendanceService.addPersonnelAttendanceUserDetail(beginDate, endDate, "7", "2", null, null,null, null, py, user, null, null, null, new Date());
//                        Byte absenteeismeNum = personnelAttendanceMonthlyService.GetAbsenteeisme(attendanceUser,sdf1.parse(date));  //旷工次数
                        personnelAttendanceMonthlyService.SetAbsenteeisme(attendanceUser, NewDateUtils.dateFromString(date, sdf1), (byte) 1);  //添加一次旷工
                    }
                }
            }
        }
        if (dtype == 1 && aa == 1) {  //上班状态
            if ("2".equals(personnelAttendanceUser.getBeginState())) {  //上班迟到并且有旷工
                py.setType((byte)9);
            } else if ("1".equals(personnelAttendanceUser.getBeginState())) {   //上班有旷工
                py.setType((byte)7);
            }
            py.setUpdateDate(new Date());
            personnelAttendanceUserDao.update(py);
        } else if (dtype == 2 && aa == 1) {   //下班时状态
            if ("1".equals(py.getType()) || "8".equals(py.getType()) || "7".equals(py.getType())) {
                py.setType((byte)7);
            } else {
                py.setType((byte)9);
            }
            py.setUpdateDate(new Date());
            personnelAttendanceUserDao.update(py);
        }
    }

    //Final functions of setDefaultWorkAttendance
    private final PersonnelAttendanceUser getOrAddNewPersonnelAttendanceUser(Integer oid, Integer deptId, Integer uid, Date day, String workBegin, String workEnd,String breakBegin, String breakEnd,Integer source) {
        User user = userService.getUserByID(uid);   //要默认添加考勤的人员
        PersonnelAttendanceUser personnelAttendanceUser = this.getOnePersonnelAttendanceUserByOid(oid, null, day, null, uid);
        if (personnelAttendanceUser == null) {
            //新添加此时的考勤，然后返回 joinDateTimeString
            personnelAttendanceUser = this.addPersonnelAttendanceUser1(oid, deptId, null, "0", "0", "0", uid, day, NewDateUtils.joinDateTimeString(day,workBegin), NewDateUtils.joinDateTimeString(day,workEnd),NewDateUtils.joinDateTimeString(day,breakBegin),NewDateUtils.joinDateTimeString(day,breakEnd),source);
            personnelAttendanceMonthlyService.AddUpdateUserInfo(user, day);
            personnelAttendanceMonthlyService.SetWorkingHours(user, day, personnelAttendanceUser.GetWorkHours().floatValue());
            List<PersonnelLeave> personnelLeaves = leaveService.getPersonnelLeaveListByTime(uid, NewDateUtils.today(day), NewDateUtils.getLastTimeOfDay(day));
            if (!personnelLeaves.isEmpty()) {
//                Byte liveNum = personnelAttendanceMonthlyService.GetLive(user,day);
                //wyu：大于0的duration求和
                Double duration = getDurationFromLeaves(personnelAttendanceUser, personnelLeaves);
                personnelAttendanceMonthlyService.SetLeave(user, day, (byte) personnelLeaves.size(), duration.floatValue());
                personnelAttendanceUser.setType((byte)5);  //1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
                //根据请假修改考勤上下班的状态
                beginAndEndStatus(personnelAttendanceUser, personnelLeaves, day, workBegin, workEnd);
            }
            //考勤日系统里的审批完成的加班
            this.defaultOverTime(personnelAttendanceUser, user, deptId, day, 2, NewDateUtils.joinDateTimeString(day,workBegin), NewDateUtils.joinDateTimeString(day,workEnd),NewDateUtils.joinDateTimeString(day,breakBegin),NewDateUtils.joinDateTimeString(day,breakEnd),source);
        }else {
            if (personnelAttendanceUser.getBreakBegin()==null&&StringUtils.isNotEmpty(breakBegin)){
                personnelAttendanceUser.setBreakBegin(NewDateUtils.joinDateTimeString(day,breakBegin));
            }
            if (personnelAttendanceUser.getBreakEnd()==null&&StringUtils.isNotEmpty(breakEnd)){
                personnelAttendanceUser.setBreakEnd(NewDateUtils.joinDateTimeString(day,breakEnd));
            }
        }
        return personnelAttendanceUser;
    }

    private final void scanWeekend(List<UserDto> userDtoList, Integer oid, Date start, List<Integer> uids) {
        String workBegin, workEnd,breakBegin,breakEnd;
        for (UserDto userDto : userDtoList) {
//            System.out.println("scanWeekend userDtoList oid:"+oid+" count:"+userDtoList.size());
            if (uids != null) {
                uids.add(userDto.getUserID());
            }
            User user = userService.getUserByID(userDto.getUserID());   //要默认添加考勤的人员
            Integer deptId;
            try {
                deptId = Integer.valueOf(userDto.getDepartment());
            } catch (NumberFormatException e) {
                deptId = Integer.valueOf(0);
            }
            HashMap<String, Object> map = new HashMap<>();
            personnelAttendanceMonthlyService.SetNoNeed(user, start, true);
            //无需考勤日系统里的审批完成的加班
            this.returnTime(oid, deptId, start, map);  //获取此时此部门所在的考勤规则的三种时间
            workBegin = (String) map.get("beginTime");   //开始上班时间  时分
            workEnd = (String) map.get("endTime");  //下班时间   时分
            breakBegin = (String) map.get("breakBegin");   //午休开始时间(上午下班时间)
            breakEnd = (String) map.get("breakEnd");  //午休结束时间(下午上班时间)
            Integer source = (Integer) map.get("source"); //来源:1-手工录入,2-打卡
            Date workBegin1 = NewDateUtils.joinDateTimeString(NewDateUtils.today(), workBegin);
            Date workEnd1 = NewDateUtils.joinDateTimeString(NewDateUtils.today(), workEnd);
            Date breakBegin1 = NewDateUtils.joinDateTimeString(NewDateUtils.today(), breakBegin);
            Date breakEnd1 = NewDateUtils.joinDateTimeString(NewDateUtils.today(), breakEnd);
            this.defaultOverTime(null, user, deptId, start, 1, workBegin1, workEnd1,breakBegin1,breakEnd1,source);
        }
    }

    //1-系统默认考勤中用
    private final boolean clockiAbsent(PersonnelAttendanceUser personnelAttendanceUser, Date inputDate, Date day, Boolean isNewAbsent) {
        User user = userService.getUserByID(personnelAttendanceUser.getUser());
        //上班考勤未录入
        personnelAttendanceUser.setBeginState("7");
        personnelAttendanceUser.setAmAttendance(inputDate);
        personnelAttendanceUser.setUpdateDate(inputDate);
        if ("0".equals(personnelAttendanceUser.getType()) || "1".equals(personnelAttendanceUser.getType()) || "7".equals(personnelAttendanceUser.getType()) || "8".equals(personnelAttendanceUser.getType())) {
            personnelAttendanceUser.setType((byte)7);
        } else {
            personnelAttendanceUser.setType((byte)9);
        }

        if (isNewAbsent) {
            workAttendanceService.addPersonnelAttendanceUserDetail(null, null, "7", "3", null,null, null, "系统默认旷工", personnelAttendanceUser, null, null, null, null, inputDate);
            Byte absenteeismeNum = personnelAttendanceMonthlyService.GetAbsenteeisme(user, day);  //旷工次数
            personnelAttendanceMonthlyService.SetAbsenteeisme(user, day, (byte) (absenteeismeNum + 1));
        }
        return false;
    }

    //1-系统默认考勤中用
    private final boolean clockoutAbsent(PersonnelAttendanceUser personnelAttendanceUser, Date inputDate, Date day, Boolean isNewAbsent) {
        User user = userService.getUserByID(personnelAttendanceUser.getUser());
        //下班考勤未录入
        personnelAttendanceUser.setEndState("7");
        personnelAttendanceUser.setPmAttendance(inputDate);
        personnelAttendanceUser.setUpdateDate(inputDate);
        if ("0".equals(personnelAttendanceUser.getType()) || "1".equals(personnelAttendanceUser.getType()) || "7".equals(personnelAttendanceUser.getType()) || "8".equals(personnelAttendanceUser.getType())) {
            personnelAttendanceUser.setType((byte)7);
        } else {
            personnelAttendanceUser.setType((byte)9);
        }

        if (isNewAbsent) {
            workAttendanceService.addPersonnelAttendanceUserDetail(null, null, "7", "3", null,null, null, "系统默认旷工", personnelAttendanceUser, null, null, null, null, inputDate);
            Byte absenteeismeNum = personnelAttendanceMonthlyService.GetAbsenteeisme(user, day);  //旷工次数
            personnelAttendanceMonthlyService.SetAbsenteeisme(user, day, (byte) (absenteeismeNum + 1));
        }
        return false;
    }
    //End of final functions of setDefaultWorkAttendance

    /**
     * 批量设置默认考勤。除了正常默认考勤外，如果涉及考勤设置变更
     * 本方法1、把考勤开始前当月月初到开始前一天设为无需考勤；2、把不在考勤人员，但当月其他日期有考勤月记录的人员自动设置无需考勤。
     * 考勤开始后人员从无需考勤设为需要考勤，需要另外调用月方法设置从月初到设置当天设为无需考勤。
     *
     * @param oid
     * @return true 机构已启用考勤； false 机构未启用考勤
     */
    @Override
    public void setDefaultWorkAttendance(Integer oid) {
        if (oid < 0) {//禁用旧扫描
            Date start, startWorkday, inputDate;
            final long end = NewDateUtils.today().getTime();   //今天
            Map<String, Object> map = new HashedMap();
            String workBegin, workEnd, breakBegin, breakEnd;
            boolean hasInput, isNewAbsent;
            PersonnelAttendanceUser personnelAttendanceUser;
            PersonnelAttendanceOrgMonitor attendanceOrgMonitor = getOrgMonitor(oid);
            ArrayList<Integer> uids = new ArrayList<>();
            if (attendanceOrgMonitor.getStartDate() == null) { //跑过一次第一个考勤工作日考勤录入时间之后，就不再执行（第一个工作日的上午需要特殊处理）。
                Date systemTime = workAttendanceService.getStartUsingSystemTime(oid);
                if (systemTime == null) {
                    return;//系统还没有设置考勤
                }
                systemTime = NewDateUtils.today(systemTime);
                if (NewDateUtils.today().getTime() < systemTime.getTime()) {
                    return;//系统还没有开始启用考勤
                } else if (systemTime.getTime() <= NewDateUtils.today().getTime()) {  //已启用，但是还没有考勤数据
                    addAttendanceException(oid, systemTime);//判断并添加作息时间
                    //获取第一个工作日
                    startWorkday = getNextDateByWork(NewDateUtils.yesterday(systemTime), oid);
                    inputDate = workAttendanceService.getInputTime(oid, NewDateUtils.today(startWorkday));
                    if (System.currentTimeMillis() < inputDate.getTime()) { //手动录入的，还没到第一一个工作日的考勤录入时间，无需处理上班考勤
                        if ((start = attendanceOrgMonitor.getScanDate()) != null) {//跑过假attendanceOrgMonitor.getScanDate()不为空
                            if (attendanceOrgMonitor.getScanTimes().equals((byte) 2)) {//上次是假
                                start = NewDateUtils.tomorrow(start);//从第二天开始跑
                                attendanceOrgMonitor.setScanTimes((byte) 0);
                            }// else 上次是第一个工作日，但是没过考勤录入时间，无需再跑。
                        } else {
                            start = systemTime;//没跑过假
                        }
                        hasInput = false;
                    } else {
                        start = startWorkday;
                        hasInput = true;
                    }
                    //考勤第一天，非每月1日添加无需考勤
                    if (attendanceOrgMonitor.getScanTimes().equals((byte) 0) || hasInput) {
                        //设置考勤开始前无需考勤
                        if (attendanceOrgMonitor.getScanTimes().equals((byte) 0) && NewDateUtils.changeMonth(systemTime, 0).getTime() != systemTime.getTime()) {
                            List<UserDto> userDtoList = getUser(systemTime, oid, getDeptIds(systemTime, oid));  //考勤第一天需考勤的人员
                            for (UserDto userDto : userDtoList) {
                                User user = userService.getUserByID(userDto.getUserID());   //要默认添加考勤的人员
                                personnelAttendanceMonthlyService.SetNewUserNoNeed(user, NewDateUtils.yesterday(systemTime), null);//设置无需考勤
                            }
                        }
                        for (; start.getTime() <= end && start.getTime() <= startWorkday.getTime(); start = NewDateUtils.changeDay(start, 1)) {
                            setPersonelAttendanceNoNeed(oid, start);
                            uids.clear();
                            List<UserDto> userDtoList = this.getUser(start, oid, getDeptIds(start, oid));  //需考勤的人员
                            if (start.getTime() == startWorkday.getTime()) {//工作日
                                if (hasInput || attendanceOrgMonitor.getScanDate() != null || attendanceOrgMonitor.getScanTimes().equals((byte) 0)) {//第一个工作日考勤录入时间后第一次跑，或者当天第一次跑(分跑过假或者第一天就是工作日两种情况)
                                    for (UserDto userDto : userDtoList) {
                                        uids.add(userDto.getUserID());
                                        Integer deptId = NumberUtils.toInt(userDto.getDepartment(), 0);
                                        map = this.returnTime(oid, deptId, startWorkday, map);  //获取此时此部门所在的考勤规则的三种时间
                                        workBegin = (String) map.get("beginTime");   //开始上班时间  时分
                                        workEnd = (String) map.get("endTime");  //下班时间   时分
                                        breakBegin = (String) map.get("breakBegin");
                                        breakEnd = (String) map.get("breakEnd");
                                        Integer source = (Integer) map.get("source"); //来源:1-手工录入(默认),2-打卡
                                        personnelAttendanceUser = getOrAddNewPersonnelAttendanceUser(oid, deptId, userDto.getUserID(), startWorkday, workBegin, workEnd, breakBegin, breakEnd, source);
                                        if (hasInput && "0".equals(personnelAttendanceUser.getBeginState())) {
                                            isNewAbsent = this.countPersonnelAttendanceUserDetailBykg(null, personnelAttendanceUser.getUser(), personnelAttendanceUser.getAttendanceDate(), "3").equals(0L);  //取系统默认的旷工
                                            clockiAbsent(personnelAttendanceUser, inputDate, startWorkday, isNewAbsent);
                                        }
                                    }
                                    personnelAttendanceMonthlyService.SetRemoveUser(oid, uids, start);
                                    if (hasInput) {
                                        attendanceOrgMonitor.setScanDate(startWorkday);
                                        attendanceOrgMonitor.setStartDate(startWorkday);
                                        attendanceOrgMonitor.setScanTimes((byte) 2);
                                    } else {
                                        attendanceOrgMonitor.setScanTimes((byte) 1);
                                        return;//没到第一天的考勤录入时间，不需要再执行。
                                    }
                                }
                            } else { //考勤开始第一天是假
                                scanWeekend(userDtoList, oid, start, uids);
                                personnelAttendanceMonthlyService.SetRemoveUser(oid, uids, start);
                                attendanceOrgMonitor.setScanDate(start);
                                attendanceOrgMonitor.setScanTimes((byte) 2);
                            }
                        }
                    }
                }
                if (attendanceOrgMonitor.getScanDate() == null || attendanceOrgMonitor.getStartDate() == null) {//考勤PersonnelAttendanceOrgMonitor未跑出完整数据，下面不再跑。
                    return;
                }
            }
            addAttendanceException(oid, attendanceOrgMonitor.getStartDate());//判断并添加作息时间
            if (attendanceOrgMonitor.getExceptionDate().getTime() < end) {
                if (attendanceOrgMonitor.getExceptionDate().getTime() > NewDateUtils.getMinDate().getTime()) {
                    start = NewDateUtils.today(attendanceOrgMonitor.getExceptionDate());
                } else {
                    start = attendanceOrgMonitor.getScanDate();
                }
                Date nextBeginTime = NewDateUtils.changeMonth(start, 1);  //获取下个月第一天
                Date nextEndTime = NewDateUtils.getLastTimeOfMonth(nextBeginTime);//获取下个月最后一天
                addPersonnelAttendanceException(oid, nextBeginTime, nextEndTime);
                attendanceOrgMonitor.setExceptionDate(nextEndTime);
            }
            //start 扫描时间， startWorkday 当前扫描的工作日， nextWorkday 下一个工作日
            startWorkday = attendanceOrgMonitor.getStartDate();
            Date nextWorkday = getNextDateByWork(startWorkday, oid);
            inputDate = workAttendanceService.getInputTime(oid, NewDateUtils.today(startWorkday));
            Date nextInputDate = workAttendanceService.getInputTime(oid, NewDateUtils.today(nextWorkday));
            if (System.currentTimeMillis() < nextInputDate.getTime()) { //还没到下一个工作日的考勤录入时间，无需处理上个工作日的下班考勤
                start = NewDateUtils.tomorrow(attendanceOrgMonitor.getScanDate());
                hasInput = false;
            } else {
                start = startWorkday;
                hasInput = true;
            }
            Date scanDate = attendanceOrgMonitor.getScanDate();
            for (; start.getTime() < end; start = NewDateUtils.changeDay(start, 1)) {   //遍历两个时间之间的时间
                setPersonelAttendanceNoNeed(oid, start);
                uids.clear();
                List<UserDto> userDtoList = this.getUser(start, oid, getDeptIds(start, oid));  //某天需考勤的人员
                if (start.getTime() == nextWorkday.getTime()) {//更新今明两个工作日
                    startWorkday = nextWorkday;
                    nextWorkday = getNextDateByWork(startWorkday, oid);
                    inputDate = nextInputDate;
                    nextInputDate = workAttendanceService.getInputTime(oid, NewDateUtils.today(nextWorkday));
                    if (System.currentTimeMillis() < nextInputDate.getTime()) { //还没到下一个工作日的考勤录入时间，无需处理上个工作日的下班考勤
                        hasInput = false;
                    } else {
                        hasInput = true;
                    }
                }

                if (start.getTime() == startWorkday.getTime()) {    //如果今天是班，则需考勤，进行考勤录入
                    for (UserDto userDto : userDtoList) {
                        uids.add(userDto.getUserID());
                        Integer deptId = NumberUtils.toInt(userDto.getDepartment(), 0);
                        map = this.returnTime(oid, deptId, start, map);  //获取此时此部门所在的考勤规则的三种时间
                        workBegin = (String) map.get("beginTime");   //开始上班时间  时分
                        workEnd = (String) map.get("endTime");  //下班时间   时分
                        breakBegin = (String) map.get("breakBegin");   //午休开始时间(上午下班时间)
                        breakEnd = (String) map.get("breakEnd");  //午休结束时间(下午上班时间)
                        Integer source = (Integer) map.get("source"); //来源:1-手工录入(默认),2-打卡
                        personnelAttendanceUser = getOrAddNewPersonnelAttendanceUser(oid, deptId, userDto.getUserID(), startWorkday, workBegin, workEnd, breakBegin, breakEnd, source);

                        isNewAbsent = this.countPersonnelAttendanceUserDetailBykg(null, personnelAttendanceUser.getUser(), personnelAttendanceUser.getAttendanceDate(), "3").equals(0L);  //取系统默认的旷工
                        if ("0".equals(personnelAttendanceUser.getBeginState())) {
                            isNewAbsent = clockiAbsent(personnelAttendanceUser, inputDate, startWorkday, isNewAbsent);
                        }

                        if (hasInput && "0".equals(personnelAttendanceUser.getEndState())) {
                            clockoutAbsent(personnelAttendanceUser, nextInputDate, startWorkday, isNewAbsent);
                        }
                    }
                    personnelAttendanceMonthlyService.SetRemoveUser(oid, uids, start);
                    attendanceOrgMonitor.setStartDate(startWorkday);
                } else if (start.getTime() > scanDate.getTime()) {    //如果今天是假，则查看有无加班,和前一天的考勤是否已录入，已经扫描过的可以跳过
                    scanWeekend(userDtoList, oid, start, uids);
                    personnelAttendanceMonthlyService.SetRemoveUser(oid, uids, start);
                }
            }
            if (start.getTime() == end && (attendanceOrgMonitor.getScanDate().getTime() < end //今天第一次跑
                    || attendanceOrgMonitor.getScanTimes() < 2 && inputDate.getTime() < System.currentTimeMillis())) {  //上次跑在考勤录入时间前，现在已经过了考勤录入时间
                setPersonelAttendanceNoNeed(oid, start);
                List<UserDto> userDtoList = this.getUser(start, oid, getDeptIds(start, oid));  //今天需考勤的人员
                uids.clear();
                if (start.getTime() == nextWorkday.getTime()) {//更新今明两个工作日，考勤第一天有用
                    startWorkday = nextWorkday;
                    inputDate = nextInputDate;
                    if (System.currentTimeMillis() < inputDate.getTime()) { //还没到下一个工作日的考勤录入时间，无需处理上个工作日的下班考勤
                        hasInput = false;
                    } else {
                        hasInput = true;
                    }
                }

                if (start.getTime() == startWorkday.getTime()) {//今天是工作日
                    for (UserDto userDto : userDtoList) {
                        Integer deptId = NumberUtils.toInt(userDto.getDepartment(), 0);
                        map = this.returnTime(oid, deptId, start, map);  //获取此时此部门所在的考勤规则的三种时间
                        workBegin = (String) map.get("beginTime");   //开始上班时间  时分
                        workEnd = (String) map.get("endTime");  //下班时间   时分
                        breakBegin = (String) map.get("breakBegin");   //午休开始时间(上午下班时间)
                        breakEnd = (String) map.get("breakEnd");  //午休结束时间(下午上班时间)
                        Integer source = (Integer) map.get("source"); //来源:1-手工录入(默认),2-打卡

                        uids.add(userDto.getUserID());
                        personnelAttendanceUser = getOrAddNewPersonnelAttendanceUser(oid, deptId, userDto.getUserID(), startWorkday, workBegin, workEnd, breakBegin, breakEnd, source);
                        if (hasInput && "0".equals(personnelAttendanceUser.getBeginState())) {
                            isNewAbsent = this.countPersonnelAttendanceUserDetailBykg(null, personnelAttendanceUser.getUser(), personnelAttendanceUser.getAttendanceDate(), "3").equals(0L);
                            clockiAbsent(personnelAttendanceUser, inputDate, startWorkday, isNewAbsent);
                        }
                    }
                    personnelAttendanceMonthlyService.SetRemoveUser(oid, uids, start);
                    if (hasInput) {
                        attendanceOrgMonitor.setScanTimes((byte) 2);//当前时间<考勤录入时间?1:2
                        attendanceOrgMonitor.setStartDate(startWorkday);
                    } else {
                        attendanceOrgMonitor.setScanTimes((byte) 1);//当前时间<考勤录入时间?1:2
                    }
                } else { //今天是假
                    scanWeekend(userDtoList, oid, start, uids);
                    personnelAttendanceMonthlyService.SetRemoveUser(oid, uids, start);
                    attendanceOrgMonitor.setScanTimes((byte) 2);
                }
                attendanceOrgMonitor.setScanDate(start);
            }
        }
    }

    //是否需要添加作息时间
    private void addAttendanceException(Integer oid, Date start) {
        Date now = new Date(System.currentTimeMillis());
        Map<String, Object> map = new HashMap<>();
        String hql = "select max(exceptionDate) from PersonnelAttendanceException where org=:oid";
        map.put("oid", oid);
        Date prvDate = (Date) personnelAttendanceExceptionDao.getByHQLWithNamedParams(hql, map);
        Date day;
        if (prvDate == null) {
            day = NewDateUtils.today(start);
        } else {
            day = NewDateUtils.tomorrow(prvDate);//最新考勤作息时间的0时0分0秒000
        }
        Date lastDay = NewDateUtils.today(NewDateUtils.changeMonth(now, 1));   //下个月的1日0时0分0秒000
        while (day.getTime() < lastDay.getTime()) {
            addPersonnelAttendanceException(oid, day, null, null);
            day = NewDateUtils.tomorrow(day);
        }
    }

    @Override
    public Map<String, Object> noNeedAttendance(User user, JSONObject attendanceJson, Map<String, Object> map, HttpServletRequest request) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String attendanceDate = attendanceJson.getString("attendanceDate");  //考勤日期
        Integer attendanceUserId = attendanceJson.getInt("attendanceUserId");//考勤人员
        String upIsNormal = attendanceJson.getString("upIsNormal");// 上班考勤 1-正常，0-迟到
        String isAbsenteeism = attendanceJson.getString("isAbsenteeism"); //1-旷工， 0-不旷工
        String isLeave = attendanceJson.getString("isLeave");// 1- 请假 0-没请假
        String downIsNormal = attendanceJson.getString("downIsNormal");//下班考勤 1-正常，0-早退
        String isOverTime = attendanceJson.getString("isOverTime");// 1-加班 0-没加班
        String updateDesc = attendanceJson.getString("updateDesc");// 修改理由

        PersonnelAttendanceUser pu = new PersonnelAttendanceUser();//无需考勤中的修改前和修改后的内容有一样
        PersonnelAttendanceUserHistory ph = new PersonnelAttendanceUserHistory();//存储改前考勤信息
        ph.setAttendanceDate(sdf.parse(attendanceDate));
        pu.setAttendanceDate(sdf.parse(attendanceDate));

        User attendanceUser = userService.getUserByID(attendanceUserId);  //无需考勤进行修改的职工
        personnelAttendanceMonthlyService.ClearDay(attendanceUser, sdf.parse(attendanceDate));  //清除某人某天的考勤月表
        if (upIsNormal.equals("1") && downIsNormal.equals("1")) {
            ph.setType("10");//类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班，9-其他 10-由无需考勤改到考勤【若是10，则不展示改前信息，找本表查具体的类型】
            pu.setType((byte)1);  //类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他

            if (isAbsenteeism.equals("1") && isLeave.equals("1")) {
                pu.setType((byte)9);
            } else {
                if (isAbsenteeism.equals("1")) {
                    pu.setType((byte)7);
                }
                if (isLeave.equals("1")) {
                    if (!"1".equals(pu.getType())) {
                        pu.setType((byte)9);
                    } else {
                        pu.setType((byte)5);
                    }
                }
            }
        } else {
            String type = this.addTotalType( upIsNormal, downIsNormal, isAbsenteeism, isLeave, isOverTime);  //判断考勤人员表的总类型
            pu.setType(Byte.valueOf(type));
        }
        pu.setCreator(user.getUserID());
        pu.setCreateName(user.getUserName());
        pu.setCreateDate(new Date());
        pu.setUser(attendanceUserId);  //考勤人员id
        if (!MyStrings.nulltoempty(upIsNormal).isEmpty()) {
            if ("0".equals(upIsNormal)) {
                pu.setBeginState("2"); //上班状态:0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
            } else {
                pu.setBeginState("1"); //上班状态:0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
            }
        }
        if (!MyStrings.nulltoempty(downIsNormal).isEmpty()) {
            if ("0".equals(downIsNormal)) {
                pu.setEndState("3"); //上班状态:0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
            } else {
                pu.setEndState("1"); //上班状态:0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
            }
        }
        pu.setAmAttendance(new Date());
        pu.setPmAttendance(new Date());
        pu.setOrg(user.getOid());

        int dept = 0;
        try {
            dept = Integer.parseInt(attendanceUser.getDepartment());
        } catch (NumberFormatException e) {//==null ==""
            //Do Nothing
        }
        pu.setDept(dept);
        pu.setUpdator(user.getUserID());
        pu.setUpdateName(user.getUserName());
        pu.setUpdateDate(new Date());
        personnelAttendanceUserDao.save(pu);

        ph.setUpdateDesc(updateDesc);
        ph.setPersonnelAttendanceUser(pu);
        BeanUtils.copyPropertiesIgnoreNull(pu, ph);
        ph.setType("10");
        personnelAttendanceUserHistoryDao.save(ph);

        if (isAbsenteeism.equals("1")) {//旷工
            this.updateAttendanceAddOther(3, 1, user, attendanceUser, sdf.parse(attendanceDate), pu, ph, request);
        }

        if (isLeave.equals("1")) {  //有请假
            this.updateAttendanceAddOther(2, 1, user, attendanceUser, sdf.parse(attendanceDate), pu, ph, request);
        }

        if (isOverTime.equals("1")) {   //有加班
            this.updateAttendanceAddOther(1, 1, user, attendanceUser, sdf.parse(attendanceDate), pu, ph, request);
        }

        if (!"".equals(upIsNormal) && "0".equals(upIsNormal)) {  // 上班考勤 1-正常，0-迟到
            this.addPersonnelAttendanceUserDetailHistory(null, null, "2", "2", null, null, ph, user, null, null, "1"); //2-迟到
            workAttendanceService.addPersonnelAttendanceUserDetail(null, null, "2", "2", null,null, null, null, pu, user, null, null, "1", new Date());
            personnelAttendanceMonthlyService.SetLate(attendanceUser, sdf.parse(attendanceDate), true);
        }
        if (!"".equals(downIsNormal) && "0".equals(downIsNormal)) {   //下班考勤 1-正常，0-早退
            this.addPersonnelAttendanceUserDetailHistory(null, null, "3", "2", null, null, ph, user, null, null, "2");  //3-早退
            workAttendanceService.addPersonnelAttendanceUserDetail(null, null, "3", "2", null,null, null, null, pu, user, null, null, "2", new Date());
            personnelAttendanceMonthlyService.SetLeaveEarly(attendanceUser, sdf.parse(attendanceDate), true);
        }

        map.put("oldPersonnelAttendanceUser", pu);//改后
        map.put("oldPersonnelAttendanceUserDetail", workAttendanceService.getPersonnelAttendanceUserDetailByBusiness(pu.getId(), null, null, null, null));//改后详情
        map.put("userName", attendanceUser.getUserName());
        map.put("attendanceDate", attendanceDate);//考勤日期
        map.put("updateDesc", updateDesc);//修改理由
        map.put("status", 1);  //修改成功
        return map;
    }

    /**
     * <AUTHOR>
     * @date 2018/8/10 17:51
     * 考勤人员表中添加总类型
     */
    public String addTotalType(String upIsNormal, String downIsNormal, String isAbsenteeism, String isLeave, String isOverTime) {
        String type = "1";
        if (upIsNormal.equals("0")) {
            type = "2";//迟到
        }
        if (downIsNormal.equals("0")) {
            if (!"".equals(type) && !"1".equals(type)) {
                type = "9";//其他
            } else {
                type = "3";//早退
            }
        }
        if (isAbsenteeism.equals("1")) {
            if (!"".equals(type) && !"1".equals(type)) {
                type = "9";//其他
            } else {
                type = "7";// 旷工
            }
        }
        if (isLeave.equals("1")) {
            if (!"".equals(type) && !"1".equals(type)) {
                type = "9";//其他
            } else {
                type = "5";//请假
            }
        }
        return type;
    }


    //修改考勤或者修改无需考勤时，添加加班、请假、旷工的方法   type(1-加班 2-请假 3-旷工 )   status(1-无需考勤)
    private void updateAttendanceAddOther(Integer type, Integer status, User user, User attendanceUser, Date attendanceDate, PersonnelAttendanceUser pu, PersonnelAttendanceUserHistory ph, HttpServletRequest request){
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (type != null) {
            if (type == 1) {   //有加班
                String over = request.getParameter("overList");//加班列表
                JSONArray overArray = JSONArray.fromObject(over);
                List overList = JSONArray.toList(overArray);
                if (overList != null && overList.size() > 0) {
                    Double overDurationTotal = 0.0D;
                    for (int i = 0; i < overList.size(); i++) {
                        JSONObject jo = JSONObject.fromObject(overList.get(i));
                        String overDuration = jo.getString("overDuration");//时长
                        String overReason = jo.getString("overReason");//加班事由
                        String overMemo = jo.getString("overMemo");//备注
                        Double duration;
                        if((duration=Double.valueOf(overDuration))!=null) {
                            overDurationTotal += duration;
                        }

                        if (status != null && status == 1) {
                            this.addPersonnelAttendanceUserDetailHistory(null, null, "8", "2", null, overReason, ph, user, overDuration, overMemo, null);
                        }
                        workAttendanceService.addPersonnelAttendanceUserDetail(null, null, "8", "2", null,null, null, overReason, pu, user, overDuration, overMemo, null, new Date());
                    }
                    personnelAttendanceMonthlyService.SetOvertime(attendanceUser, attendanceDate, (byte) overList.size(), overDurationTotal.floatValue());
                }
            } else if (type == 2) {   //请假
                String leave = request.getParameter("leaveList");//请假列表
                JSONArray jsonArray = JSONArray.fromObject(leave);
                List leaveList = JSONArray.toList(jsonArray);
                if (leaveList != null && leaveList.size() > 0) {
                    Long duration = 0L;
                    for (int i = 0; i < leaveList.size(); i++) {
                        JSONObject jo = JSONObject.fromObject(leaveList.get(i));
                        String leaveBeginDate = jo.getString("leaveBeginDate"); //开始时间
                        String leaveEndDate = jo.getString("leaveEndDate");//结束时间
                        String leaveType = jo.getString("leaveType");//请假类型 1-事假,2-病假,3-年假,4-调休,5-婚假,6-产假,7-陪产假,8-路途假,9-其他'
                        String leaveReason = jo.getString("leaveReason");//请假原因
                        Date beginDate = NewDateUtils.dateFromString(leaveBeginDate, sdf);
                        Date endDate = NewDateUtils.dateFromString(leaveEndDate, sdf);

                        if (status != null && status == 1) {   //无需考勤
                            this.addPersonnelAttendanceUserDetailHistory(beginDate, endDate, "5", "2", leaveType, leaveReason, ph, user, null, null, null);
                        }
                        Integer leaveType1 = Integer.parseInt(leaveType);
                        if (leaveType1>0) {
                            workAttendanceService.addPersonnelAttendanceUserDetail(beginDate, endDate, "5", "2", null,leaveType1, null, leaveReason, pu, user, null, null, null, new Date());
                        }else {
                            leaveType1 = -leaveType1;
                            workAttendanceService.addPersonnelAttendanceUserDetail(beginDate, endDate, "5", "2", leaveType1.toString(),null, null, leaveReason, pu, user, null, null, null, new Date());
                        }
                        if (endDate.getTime() > beginDate.getTime()) {
                            duration += endDate.getTime() - beginDate.getTime();
                        }
                    }
                    personnelAttendanceMonthlyService.SetLeave(attendanceUser, attendanceDate, (byte) leaveList.size(), Double.valueOf(Double.valueOf(duration)/TimeUnit.HOURS.toMillis(1)).floatValue());
                }
            } else if (type == 3) {   //旷工
                String absenteeism = request.getParameter("absenteeism");
                JSONArray jsonArray = JSONArray.fromObject(absenteeism);
                List absenteeismList = JSONArray.toList(jsonArray);
                if (absenteeismList != null && absenteeismList.size() > 0) {
                    for (int i = 0; i < absenteeismList.size(); i++) {
                        JSONObject absenteeismJson = JSONObject.fromObject(absenteeismList.get(i));
                        String aBeginDate = absenteeismJson.getString("aBeginDate");//开始时间
                        String aEndDate = absenteeismJson.getString("aEndDate");//结束时间
                        Date beginDate = NewDateUtils.dateFromString(aBeginDate, sdf);
                        Date endDate = NewDateUtils.dateFromString(aEndDate, sdf);

                        if (status != null && status == 1) {   //无需考勤
                            this.addPersonnelAttendanceUserDetailHistory(beginDate, endDate, "7", "2", null, null, ph, user, null, null, null);
                        }
                        workAttendanceService.addPersonnelAttendanceUserDetail(beginDate, endDate, "7", "2", null,null, null, null, pu, user, null, null, null, new Date());
                    }
                    personnelAttendanceMonthlyService.SetAbsenteeisme(attendanceUser, attendanceDate, (byte) absenteeismList.size());
                }
            }
        }
    }

    /**
     * 总务的考勤修改申请接口
     * @param user
     * @param noNeedType 1-无需考勤   0-有考勤状态
     * @param absenteeismAll 矿工信息
     * @param leaveListAll 请假信息
     * @param overListAll 加班信息
     * @param attendanceId  考勤id
     * @param attendanceDate 考勤日期
     * @param attendanceUserId 考勤人员
     * @param upIsNormal 上班考勤 1-正常，0-迟到
     * @param isAbsenteeism 1-旷工， 0-不旷工
     * @param isLeave 1- 请假 0-没请假
     * @param downIsNormal  下班考勤 1-正常，0-早退
     * @param isOverTime 1-加班 0-没加班
     * @param updateDesc 修改理由
     * @return
     */
    @Override
    public Map<String, Object> attendanceUserHistory(User user,Integer noNeedType,String absenteeismAll,String leaveListAll,String overListAll,Integer attendanceId,String attendanceDate,Integer attendanceUserId,String upIsNormal,String isAbsenteeism,String isLeave,String downIsNormal,String isOverTime,String updateDesc){
        Map<String, Object> map = new HashMap<>();
        if (attendanceId!=null&&StringUtils.isNotEmpty(attendanceDate)&&attendanceUserId!=null&&StringUtils.isNotEmpty(upIsNormal)&&StringUtils.isNotEmpty(downIsNormal)) {
            Date now = new Date(System.currentTimeMillis());

            User attendanceUser = userService.getUserByID(attendanceUserId);  //考勤的人员
            List<PersonnelAttendanceUserHistory> phPrevious = new ArrayList<>();
            if (0==noNeedType || (1==noNeedType&&attendanceId!=0)) {
                phPrevious = this.getPersonnelAttendanceUserHistoryList(user.getOid(),attendanceUserId, attendanceId, "1",null,null,54);//上一次修改未审批完的
            }else if (1==noNeedType){
                phPrevious = this.getPersonnelAttendanceUserHistoryList(user.getOid(), attendanceUserId,null, "1",NewDateUtils.today(NewDateUtils.dateFromString(attendanceDate,"yyyy-MM-dd")),user.getUserID(),54);//上一次修改未审批完的
            }

            if (phPrevious.size() > 0) {
                map.put("status", 2);
                map.put("content", "本条数据的修改还未审批");
            } else {
                Integer previousId = 0;  //上一次修改的id
                Integer versionNo = 1;
                PersonnelAttendanceUser oldAttendanceUser = new PersonnelAttendanceUser();
                if (attendanceId != 0) {
                    oldAttendanceUser = personnelAttendanceUserDao.get(attendanceId);//旧的考勤
                }
                List<PersonnelAttendanceUserHistory> phPrevious1 = this.getPersonnelAttendanceUserHistoryList(null, attendanceUserId,attendanceId, "2",NewDateUtils.today(NewDateUtils.dateFromString(attendanceDate,"yyyy-MM-dd")),user.getCreator(),null);//是否有上一次修改的记录（如果没有就把现在的考勤user添加上）
                if (phPrevious1.size() == 0 && oldAttendanceUser.getId() != null) {
                    //将PersonnelAttendanceUser中的原值添加到PersonnelAttendanceUser历史表（PersonnelAttendanceUserHistory）中
                    PersonnelAttendanceUserHistory ph1 = new PersonnelAttendanceUserHistory();//存储改前考勤信息
                    BeanUtils.copyPropertiesIgnoreNull(oldAttendanceUser, ph1);   //将p与ph中一样的值从p复制到ph中，不一样的值，另外set
                    ph1.setId(null);
                    ph1.setPersonnelAttendanceUser(oldAttendanceUser);
                    ph1.setCreateDate(now);
                    ph1.setApproveStatus("2");
                    ph1.setPreviousId(0);
                    ph1.setVersionNo(0); //初始的
                    ph1.setAttendanceId(attendanceId);
                    personnelAttendanceUserHistoryDao.save(ph1);

                    List<PersonnelAttendanceUserDetail> attendanceUserDetails = this.getPersonnelAttendanceUserDetailById(oldAttendanceUser.getId());
                    for (PersonnelAttendanceUserDetail pd : attendanceUserDetails) {
                        PersonnelAttendanceUserDetailHistory pdh = new PersonnelAttendanceUserDetailHistory();
                        BeanUtils.copyPropertiesIgnoreNull(pd, pdh);  //将pd与pdh中一样的值从p复制到ph中，不一样的值，另外set
                        pdh.setId(null);
                        pdh.setAttendance(ph1.getId());
                        personnelAttendanceUserDetailHistoryDao.save(pdh);
                    }
                    phPrevious1.add(ph1);
                }
                if (phPrevious1.size() > 0) {
                    previousId = phPrevious1.get(phPrevious1.size() - 1).getId();
                    versionNo = phPrevious1.get(phPrevious1.size() - 1).getVersionNo() + 1;
                }

                //将修改的值添加到历史数据中
                PersonnelAttendanceUserHistory ph = new PersonnelAttendanceUserHistory();//存储新的考勤信息

                if (oldAttendanceUser.getId()!=null){   //如果是无需考勤第一次修改，那么此值是空的
                    ph.setAttendanceId(attendanceId);
                    ph.setPersonnelAttendanceUser(oldAttendanceUser);
                }
                ph.setAttendanceDate(NewDateUtils.dateFromString(attendanceDate, "yyyy-MM-dd"));
                if (noNeedType == 1) {   //无需考勤的
                    ph.setType("10");//类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班，9-其他 10-由无需考勤改到考勤【若是10，则不展示改前信息，找本表查具体的类型】
                } else if (upIsNormal.equals("1") && downIsNormal.equals("1")) {
                    ph.setType("1");  //类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
                    if (isAbsenteeism.equals("1") && isLeave.equals("1")) {
                        ph.setType("9");
                    } else {
                        if (isAbsenteeism.equals("1")) {
                            ph.setType("7");
                        }
                        if (isLeave.equals("1")) {
                            if (!"1".equals(ph.getType())) {
                                ph.setType("9");
                            } else {
                                ph.setType("5");
                            }
                        }
                    }
                } else {
                    String type = this.addTotalType(upIsNormal, downIsNormal, isAbsenteeism, isLeave, isOverTime);  //判断考勤人员表的总类型
                    ph.setType(type);
                }
                ph.setCreator(user.getUserID());
                ph.setCreateName(user.getUserName());
                ph.setCreateDate(new Date());
                ph.setUser(attendanceUserId);  //考勤人员id
                if (!MyStrings.nulltoempty(upIsNormal).isEmpty()) {
                    if ("0".equals(upIsNormal)) {
                        ph.setBeginState("2"); //上班状态:0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
                    } else {
                        ph.setBeginState("1"); //上班状态:0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
                    }
                }
                if (!MyStrings.nulltoempty(downIsNormal).isEmpty()) {
                    if ("0".equals(downIsNormal)) {
                        ph.setEndState("3"); //上班状态:0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
                    } else {
                        ph.setEndState("1"); //上班状态:0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
                    }
                }
                ph.setAmAttendance(new Date());
                ph.setPmAttendance(new Date());
                ph.setOrg(user.getOid());

                int dept = 0;
                try {
                    dept = Integer.parseInt(user.getDepartment());
                } catch (NumberFormatException e) {//==null ==""
                    //Do Nothing
                }
                ph.setDept(dept);
                ph.setUpdateDesc(updateDesc);
                ph.setUpdator(user.getUserID());
                ph.setUpdateName(user.getUserName());
                ph.setUpdateDate(new Date());
                ph.setApproveStatus("1");
                ph.setPreviousId(previousId);
                ph.setVersionNo(versionNo);
                personnelAttendanceUserHistoryDao.save(ph);

                if (isAbsenteeism.equals("1")) {  //旷工
                    JSONArray jsonArray = JSONArray.fromObject(absenteeismAll);
                    List absenteeismList = JSONArray.toList(jsonArray);
                    if (absenteeismList != null && absenteeismList.size() > 0) {
                        for (int i = 0; i < absenteeismList.size(); i++) {
                            JSONObject absenteeismJson = JSONObject.fromObject(absenteeismList.get(i));
                            String aBeginDate = absenteeismJson.getString("aBeginDate");//开始时间
                            String aEndDate = absenteeismJson.getString("aEndDate");//结束时间
                            Date beginDate = NewDateUtils.dateFromString(aBeginDate, sdf);
                            Date endDate = NewDateUtils.dateFromString(aEndDate, sdf);
                            this.addPersonnelAttendanceUserDetailHistory(beginDate, endDate, "7", "2", null, null, ph, user, null, null, null);
                        }
                    }
                }
                if (isLeave.equals("1")) {  //有请假
                    JSONArray jsonArray = JSONArray.fromObject(leaveListAll);
                    List leaveList = JSONArray.toList(jsonArray);
                    if (leaveList != null && leaveList.size() > 0) {
                        for (int i = 0; i < leaveList.size(); i++) {
                            JSONObject jo = JSONObject.fromObject(leaveList.get(i));
                            String leaveBeginDate = jo.getString("leaveBeginDate"); //开始时间
                            String leaveEndDate = jo.getString("leaveEndDate");//结束时间
                            String leaveType = jo.getString("leaveType");//请假类型 1-事假,2-病假,3-年假,4-调休,5-婚假,6-产假,7-陪产假,8-路途假,9-其他'
                            String leaveReason = jo.getString("leaveReason");//请假原因
                            Date beginDate = NewDateUtils.dateFromString(leaveBeginDate, sdf);
                            Date endDate = NewDateUtils.dateFromString(leaveEndDate, sdf);
                            this.addPersonnelAttendanceUserDetailHistory(beginDate, endDate, "5", "2", leaveType, leaveReason, ph, user, null, null, null);
                        }
                    }
                }
                if (isOverTime.equals("1")) {   //有加班
                    JSONArray overArray = JSONArray.fromObject(overListAll);
                    List overList = JSONArray.toList(overArray);
                    if (overList != null && overList.size() > 0) {
                        for (int i = 0; i < overList.size(); i++) {
                            JSONObject jo = JSONObject.fromObject(overList.get(i));
                            String overDuration = jo.getString("overDuration");//时长
                            String overReason = jo.getString("overReason");//加班事由
                            String overMemo = jo.getString("overMemo");//备注
                            this.addPersonnelAttendanceUserDetailHistory(null, null, "8", "2", null, overReason, ph, user, overDuration, overMemo, null);
                        }
                    }
                }

                if (!"".equals(upIsNormal) && "0".equals(upIsNormal)) {  // 上班考勤 1-正常，0-迟到
                    this.addPersonnelAttendanceUserDetailHistory(null, null, "2", "2", null, null, ph, user, null, null, "1"); //2-迟到
                }
                if (!"".equals(downIsNormal) && "0".equals(downIsNormal)) {   //下班考勤 1-正常，0-早退
                    this.addPersonnelAttendanceUserDetailHistory(null, null, "3", "2", null, null, ph, user, null, null, "2");  //3-早退
                }

                User superUser = userService.getUserByRoleCode(user.getOid(), "super");  //默认审批人为超管
                ApprovalProcess approvalProcess = new ApprovalProcess();
                approvalProcess.setOrg(user.getOid());
                approvalProcess.setFromUser(user.getUserID());
                approvalProcess.setBusinessType(54);
                approvalProcess.setBusiness(ph.getId());
                approvalProcess.setApproveStatus("1");
                approvalProcess.setDescription(attendanceUser.getUserName() + attendanceDate + "考勤的修改");
                approvalProcess.setLevel(1);
                approvalProcess.setToUser(superUser.getUserID());
                approvalProcess.setToUserName("指定审批人");  //审批人总称
                approvalProcess.setUserName(superUser.getUserName());
                approvalProcess.setAskName(user.getUserName());
                approvalProcess.setCreateDate(now);
                approvalProcessDao.save(approvalProcess);

                //角标推送
                //推给审批人(超管)，审批角标加1，列表加1
                this.attendanceRejectSend(1, 1, approvalProcess, superUser.getUserID(), "/attendanceApprovalInstance", approvalProcess.getDescription(), approvalProcess.getDescription(), "attendanceOfficerSubmitEdit");
                //给申请人(总务)的待处理发送  申请角标不变，列表加1
                this.attendanceRejectSend(0, 1, approvalProcess, user.getUserID(), "/attendanceApplyInstance", approvalProcess.getDescription(), approvalProcess.getDescription(), "attendanceEditApply");
                map.put("status", 1);
                map.put("content", "操作成功");
            }

        }else {
            map.put("status", 0);
            map.put("content", "操作失败");
        }
        return map;
    }

//    @Override
//    public int applyUpdateAttendanceUser(User user, JSONObject attendanceJson, HttpServletRequest request) throws ParseException {
//        String attendance = attendanceJson.getString("attendanceId");//考勤id
//        String attendanceDate = attendanceJson.getString("attendanceDate");  //考勤日期
//        Integer attendanceUserId = attendanceJson.getInt("attendanceUserId");//考勤人员
//        String upIsNormal = attendanceJson.getString("upIsNormal");// 上班考勤 1-正常，0-迟到
//        String isAbsenteeism = attendanceJson.getString("isAbsenteeism"); //1-旷工， 0-不旷工
//        String isLeave = attendanceJson.getString("isLeave");// 1- 请假 0-没请假
//        String downIsNormal = attendanceJson.getString("downIsNormal");//下班考勤 1-正常，0-早退
//        String isOverTime = attendanceJson.getString("isOverTime");// 1-加班 0-没加班
//        String updateDesc = attendanceJson.getString("updateDesc");// 修改理由
//        Date now = new Date(System.currentTimeMillis());
//        if (attendance != null && !"".equals(attendance)) {
//            int attendanceId = Integer.parseInt(attendance);
//            PersonnelAttendanceUser oldAttendanceUser = personnelAttendanceUserDao.get(attendanceId);//旧的考勤
//            User attendanceUser = userService.getUserByID(oldAttendanceUser.getUser());   //要修改考勤的人员
//            String hql = "from ApprovalItem where belongTo=" + user.getOid() + " and code='workAttendanceApply'";
//            List<ApprovalItem> approvalItemList = approvalItemDao.getListByHQL(hql);
//            if (approvalItemList.size() > 0) {
//                ApprovalItem approvalItem = approvalItemList.get(0);
//                if (approvalItem.getStatus() == 1) {
//                    String hqlFlow = "from ApprovalFlow where item = " + approvalItem.getId();
//                    List<ApprovalFlow> approvalFlowList = approvalFlowDao.getListByHQL(hqlFlow);
//                    if (approvalFlowList.size() > 0) {
//                        for (ApprovalFlow approvalFlow : approvalFlowList) {
//                            if (approvalFlow.getLevel() == 1) {
//                                String dateString = NewDateUtils.dateToString(oldAttendanceUser.getAttendanceDate(), "yyyy-MM-dd");
//                                ApprovalInstance approvalInstance = new ApprovalInstance();//新建审批实体类
//                                approvalInstance.setOrg(user.getOid());
//                                approvalInstance.setBusiness(oldAttendanceUser.getId());
//                                approvalInstance.setTitle(attendanceUser.getUserName() + dateString + "考勤的修改");
//                                approvalInstance.setDescription("考勤修改");
//                                approvalInstance.setItem(approvalItem.getId());
//                                approvalInstance.setStatus("1");
//                                approvalInstance.setLevel(1);
//                                approvalInstance.setAuditor(approvalFlow.getToUserId());
//                                approvalInstance.setAuditorName(approvalFlow.getToUser());
//                                approvalInstance.setOperation("2");
//                                approvalInstance.setApplyMemo(updateDesc);
//                                approvalInstance.setCreateDate(now);
//                                approvalInstance.setCreator(user.getUserID());
//                                approvalInstance.setCreateName(user.getUserName());
//                                approvalInstanceDao.save(approvalInstance);//新增审批实例
//
//                                ApprovalData approvalData = new ApprovalData();
//                                approvalData.setOrg(user.getOid());
//                                approvalData.setInstance(approvalInstance.getId());
//                                approvalData.setTitle(approvalInstance.getTitle());
//                                approvalData.setItem(approvalInstance.getItem());
//                                approvalData.setTableCode("AttendanceUser");
//                                approvalData.setRowId(oldAttendanceUser.getId());
//                                approvalData.setDataType("6");
//                                approvalData.setApproveStatus("1");
//                                approvalData.setApprovalMemo(updateDesc);
//                                approvalData.setCreateTime(now);
//                                approvalData.setCreator(user.getUserID());
//                                approvalData.setCreateName(user.getUserName());
//                                approvalData.setType("2");
//
//                                AttendanceUserDto oldAttendanceUserDto = new AttendanceUserDto();
//                                BeanUtils.copyPropertiesIgnoreNull(oldAttendanceUser, oldAttendanceUserDto);
//                                String oldDetailHql = " from PersonnelAttendanceUserDetail where attendanceId = " + attendanceId + " and source!='3'";
//                                List<PersonnelAttendanceUserDetail> oldAttendanceUserDetails = personnelAttendanceUserDetailDao.getListByHQL(oldDetailHql);
//                                oldAttendanceUserDto.setAttendanceUserDetailList(oldAttendanceUserDetails);
//                                JsonConfig jsonConfig = new JsonConfig();
//                                jsonConfig.setExcludes(new String[]{"personnelAttendanceUser", "personnelAttendanceUserDetailHashSet", "personnelAttendanceUserDetailHistoryHashSet", "personnelAttendanceUserHistoryHashSet"});
//                                jsonConfig.registerJsonValueProcessor(java.util.Date.class, new JsonDateValueProcessor());// 注入处理Date类
//                                JSONArray arrayOld = JSONArray.fromObject(oldAttendanceUserDto, jsonConfig);
//                                approvalData.setOldValue(String.valueOf(arrayOld.toString()));
//
//                                //新考勤生成
//                                AttendanceUserDto newAttendanceUserDto = new AttendanceUserDto();
//                                BeanUtils.copyPropertiesIgnoreNull(oldAttendanceUserDto, newAttendanceUserDto);
//
//                                List<PersonnelAttendanceUserDetail> newAttendanceUserDetails = new ArrayList<>();
//                                if (isAbsenteeism.equals("1")) {  //旷工
//                                    String absenteeism = request.getParameter("absenteeism");
//                                    JSONArray jsonArray = JSONArray.fromObject(absenteeism);
//                                    List absenteeismList = JSONArray.toList(jsonArray);
//                                    if (absenteeismList != null && absenteeismList.size() > 0) {
//                                        for (int i = 0; i < absenteeismList.size(); i++) {
//                                            JSONObject absenteeismJson = JSONObject.fromObject(absenteeismList.get(i));
//                                            String aBeginDate = absenteeismJson.getString("aBeginDate");//开始时间
//                                            String aEndDate = absenteeismJson.getString("aEndDate");//结束时间
////                                                    PersonnelAttendanceUserDetail attendanceUserDetail = this.addAttendanceUserDetail(sdf.parse(aBeginDate), sdf.parse(aEndDate), "7", "2", null, null, null, oldAttendanceUser, user, null, null, null, new Date());
//                                            PersonnelAttendanceUserDetail attendanceUserDetail = this.addAttendanceUserDetail(NewDateUtils.dateFromString(aBeginDate, sdf), NewDateUtils.dateFromString(aEndDate, sdf), "7", "2", null,null, null, null, oldAttendanceUser, user, null, null, null, new Date());
//                                            newAttendanceUserDetails.add(attendanceUserDetail);
//                                        }
//                                    }
//                                }
//                                if (isLeave.equals("1")) {  //有请假
//                                    String leave = request.getParameter("leaveList");//请假列表
//                                    JSONArray jsonArray = JSONArray.fromObject(leave);
//                                    List leaveList = JSONArray.toList(jsonArray);
//                                    if (leaveList != null && leaveList.size() > 0) {
//                                        for (int i = 0; i < leaveList.size(); i++) {
//                                            JSONObject jo = JSONObject.fromObject(leaveList.get(i));
//                                            String leaveBeginDate = jo.getString("leaveBeginDate"); //开始时间
//                                            String leaveEndDate = jo.getString("leaveEndDate");//结束时间
//                                            String leaveType = jo.getString("leaveType");//请假类型 1-事假,2-病假,3-年假,4-调休,5-婚假,6-产假,7-陪产假,8-路途假,9-其他'
//                                            String leaveReason = jo.getString("leaveReason");//请假原因
//                                            PersonnelAttendanceUserDetail attendanceUserDetail = new PersonnelAttendanceUserDetail();
//                                            Integer leaveType1 = Integer.parseInt(leaveType);
//                                            if (leaveType1>0) {
//                                                attendanceUserDetail = this.addAttendanceUserDetail(NewDateUtils.dateFromString(leaveBeginDate, sdf), NewDateUtils.dateFromString(leaveEndDate, sdf), "5", "2",null, leaveType1, null, leaveReason, oldAttendanceUser, user, null, null, null, new Date());
//                                            }else {
//                                                leaveType1 = -leaveType1;
//                                                attendanceUserDetail = this.addAttendanceUserDetail(NewDateUtils.dateFromString(leaveBeginDate, sdf), NewDateUtils.dateFromString(leaveEndDate, sdf), "5", "2",null, leaveType1, null, leaveReason, oldAttendanceUser, user, null, null, null, new Date());
//                                            }
//                                            newAttendanceUserDetails.add(attendanceUserDetail);
//                                        }
//                                    }
//                                }
//                                if (isOverTime.equals("1")) {   //有加班
//                                    String over = request.getParameter("overList");//加班列表
//                                    JSONArray overArray = JSONArray.fromObject(over);
//                                    List overList = JSONArray.toList(overArray);
//                                    if (overList != null && overList.size() > 0) {
//                                        Double overDurationTotal = null;
//                                        for (int i = 0; i < overList.size(); i++) {
//                                            JSONObject jo = JSONObject.fromObject(overList.get(i));
//                                            String overDuration = jo.getString("overDuration");//时长
//                                            String overReason = jo.getString("overReason");//加班事由
//                                            String overMemo = jo.getString("overMemo");//备注
//                                            if (overDurationTotal != null) {
//                                                overDurationTotal = overDurationTotal + Double.valueOf(overDuration);
//                                            } else {
//                                                overDurationTotal = Double.valueOf(overDuration);
//                                            }
//                                            PersonnelAttendanceUserDetail attendanceUserDetail = this.addAttendanceUserDetail(null, null, "8", "2", null,null, null, overReason, oldAttendanceUser, user, overDuration, overMemo, null, new Date());
//                                            newAttendanceUserDetails.add(attendanceUserDetail);
//                                        }
//                                    }
//                                }
//                                if (!MyStrings.nulltoempty(upIsNormal).isEmpty() && "0".equals(upIsNormal)) {  // 上班考勤 1-正常，0-迟到
//                                    newAttendanceUserDto.setBeginState("2");
//                                    PersonnelAttendanceUserDetail attendanceUserDetail = this.addAttendanceUserDetail(null, null, "2", "2", null,null, null, null, newAttendanceUserDto, user, null, null, "1", new Date()); //上班 2-迟到
//                                    newAttendanceUserDetails.add(attendanceUserDetail);
//                                } else {
//                                    newAttendanceUserDto.setBeginState(upIsNormal);
//                                }
//                                if (!MyStrings.nulltoempty(downIsNormal).isEmpty() && "0".equals(downIsNormal)) {   //下班考勤 1-正常，0-早退
//                                    newAttendanceUserDto.setEndState("3");
//                                    PersonnelAttendanceUserDetail attendanceUserDetail = this.addAttendanceUserDetail(null, null, "3", "2", null,null, null, null, newAttendanceUserDto, user, null, null, "2", new Date());  //下班 3-早退
//                                    newAttendanceUserDetails.add(attendanceUserDetail);
//                                } else {
//                                    newAttendanceUserDto.setEndState(downIsNormal);
//                                }
//                                newAttendanceUserDto.setType("");//清空之前 当天考勤类型
//                                if (upIsNormal.equals("1") && downIsNormal.equals("1")) {
//                                    newAttendanceUserDto.setType("1");//类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
//                                    if (isAbsenteeism.equals("1") && isLeave.equals("1")) {
//                                        newAttendanceUserDto.setType("9");
//                                    } else {
//                                        if (isAbsenteeism.equals("1")) {
//                                            newAttendanceUserDto.setType("7");
//                                        }
//                                        if (isLeave.equals("1")) {
//                                            if (!"1".equals(newAttendanceUserDto.getType()) && !"8".equals(newAttendanceUserDto.getType())) {
//                                                newAttendanceUserDto.setType("9");
//                                            } else {
//                                                newAttendanceUserDto.setType("5");
//                                            }
//                                        }
//                                    }
//                                } else {
//                                    String type = this.addTotalType(upIsNormal, downIsNormal, isAbsenteeism, isLeave, isOverTime);  //判断考勤人员表的总类型
//                                    newAttendanceUserDto.setType(type);
//                                }
//                                newAttendanceUserDto.setCreateName(user.getUserName());
//                                newAttendanceUserDto.setCreator(user.getUserID());
//                                newAttendanceUserDto.setUpdator(user.getUserID());
//                                newAttendanceUserDto.setUpdateDate(now);
//                                newAttendanceUserDto.setUpdateName(user.getUserName());
//                                newAttendanceUserDto.setAmAttendance(now);
//                                newAttendanceUserDto.setPmAttendance(now);
//
//                                newAttendanceUserDto.setAttendanceUserDetailList(newAttendanceUserDetails);
//                                JSONArray arrayNew = JSONArray.fromObject(newAttendanceUserDto, jsonConfig);
//                                approvalData.setNewValue(arrayNew.toString());
//                                approvalDataDao.save(approvalData);
//
//                                ApprovalProcess approvalProcess = new ApprovalProcess();
//                                approvalProcess.setInstance(approvalInstance.getId());
//                                approvalProcess.setOrg(user.getOid());
//                                approvalProcess.setFromUser(user.getUserID());
//                                approvalProcess.setUserName(user.getUserName());
//                                approvalProcess.setBusinessType(31);
//                                approvalProcess.setBusiness(oldAttendanceUser.getId());
//                                approvalProcess.setApproveStatus("1");
//                                approvalProcess.setDescription(approvalInstance.getTitle());
//                                approvalProcess.setLevel(1);
//                                approvalProcess.setToUser(approvalFlow.getToUserId());
//                                approvalProcess.setToUserName(approvalFlow.getToUser());
//                                approvalProcess.setAskName(user.getUserName());
//                                approvalProcess.setCreateDate(now);
//                                approvalProcessDao.save(approvalProcess);
//                                User approvalUser = userService.getUserByID(approvalFlow.getToUserId());
//                                HashMap<String, Object> map = new HashMap<>();
//                                map.put("approvalInstance", approvalInstance);
//                                //角标推送
//                                //推给超管，审批角标加1，列表加1
//                                swMessageService.rejectSend(1, 1, map, approvalFlow.getToUserId().toString(), "/attendanceApprovalInstance", approvalInstance.getTitle(), approvalInstance.getTitle(), approvalUser, "attendanceOfficerSubmitEdit");
//                                //推给总务，申请角标不变，列表加1
//                                swMessageService.rejectSend(0, 1, map, user.getUserID().toString(), "/attendanceApplyInstance", approvalInstance.getTitle(), approvalInstance.getTitle(), user, "attendanceEditApply");
//
//                            }
//                        }
//                    }
//                }
//            }
//        } else {
//            User attendanceUser = userService.getUserByID(attendanceUserId);   //要修改考勤的人员
//            String hql = "from ApprovalItem where belongTo=" + user.getOid() + " and code='workAttendanceApply'";
//            List<ApprovalItem> approvalItemList = approvalItemDao.getListByHQL(hql);
//            if (approvalItemList.size() > 0) {
//                ApprovalItem approvalItem = approvalItemList.get(0);
//                if (approvalItem.getStatus() == 1) {
//                    String hqlFlow = "from ApprovalFlow where item = " + approvalItem.getId();
//                    List<ApprovalFlow> approvalFlowList = approvalFlowDao.getListByHQL(hqlFlow);
//                    if (approvalFlowList.size() > 0) {
//                        for (ApprovalFlow approvalFlow : approvalFlowList) {
//                            if (approvalFlow.getLevel() == 1) {
//                                String dateString = attendanceDate;
//                                ApprovalInstance approvalInstance = new ApprovalInstance();//新建审批实体类
//                                approvalInstance.setOrg(user.getOid());
////                                approvalInstance.setBusiness(attendanceUserId);
//                                approvalInstance.setTitle(attendanceUser.getUserName() + dateString + "考勤的修改");
//                                approvalInstance.setDescription("考勤修改");
//                                approvalInstance.setItem(approvalItem.getId());
//                                approvalInstance.setStatus("1");
//                                approvalInstance.setLevel(1);
//                                approvalInstance.setAuditor(approvalFlow.getToUserId());
//                                approvalInstance.setAuditorName(approvalFlow.getToUser());
//                                approvalInstance.setOperation("1");
//                                approvalInstance.setApplyMemo(updateDesc);
//                                approvalInstance.setCreateDate(now);
//                                approvalInstance.setCreator(user.getUserID());
//                                approvalInstance.setCreateName(user.getUserName());
//                                approvalInstanceDao.save(approvalInstance);//新增审批实例
//
//                                ApprovalData approvalData = new ApprovalData();
//                                approvalData.setOrg(user.getOid());
//                                approvalData.setInstance(approvalInstance.getId());
//                                approvalData.setTitle(approvalInstance.getTitle());
//                                approvalData.setItem(approvalInstance.getItem());
//                                approvalData.setTableCode("user");
//                                approvalData.setRowId(attendanceUserId);
//                                approvalData.setDataType("6");
//                                approvalData.setApproveStatus("1");
//                                approvalData.setApprovalMemo(updateDesc);
//                                approvalData.setCreateTime(now);
//                                approvalData.setCreator(user.getUserID());
//                                approvalData.setCreateName(user.getUserName());
//                                approvalData.setType("1");
//                                approvalData.setOldValue(attendanceDate);
//
//                                AttendanceUserDto newAttendanceUserDto = new AttendanceUserDto();
//                                newAttendanceUserDto.setUser(attendanceUserId);
//                                newAttendanceUserDto.setOrg(user.getOid());
//                                String formatter = "yyyy-MM-dd";
//                                newAttendanceUserDto.setAttendanceDate(NewDateUtils.dateFromString(attendanceDate, formatter));
//                                if (upIsNormal.equals("1") && downIsNormal.equals("1")) {
//                                    newAttendanceUserDto.setType("1");  //类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
//
//                                    if (isAbsenteeism.equals("1") && isLeave.equals("1")) {
//                                        newAttendanceUserDto.setType("9");
//                                    } else {
//                                        if (isAbsenteeism.equals("1")) {
//                                            newAttendanceUserDto.setType("7");
//                                        }
//                                        if (isLeave.equals("1")) {
//                                            if (!"1".equals(newAttendanceUserDto.getType())) {
//                                                newAttendanceUserDto.setType("9");
//                                            } else {
//                                                newAttendanceUserDto.setType("5");
//                                            }
//                                        }
//                                    }
//                                } else {
//                                    String type = this.addTotalType(upIsNormal, downIsNormal, isAbsenteeism, isLeave, isOverTime);  //判断考勤人员表的总类型
//                                    newAttendanceUserDto.setType(type);
//                                }
//                                newAttendanceUserDto.setCreateName(user.getUserName());
//                                newAttendanceUserDto.setCreator(user.getUserID());
//                                newAttendanceUserDto.setCreateDate(now);
//                                newAttendanceUserDto.setUpdator(user.getUserID());
//                                newAttendanceUserDto.setUpdateDate(now);
//                                newAttendanceUserDto.setUpdateName(user.getUserName());
//                                newAttendanceUserDto.setAmAttendance(now);
//                                newAttendanceUserDto.setPmAttendance(now);
//                                newAttendanceUserDto.setAttendanceDate(NewDateUtils.dateFromString(attendanceDate, "yyyy-MM-dd"));
//                                int dept = 0;
//                                try {
//                                    dept = Integer.parseInt(attendanceUser.getDepartment());
//                                } catch (NumberFormatException e) {//==null ==""
//                                    //Do Nothing
//                                }
//                                newAttendanceUserDto.setDept(dept);
//
//                                List<PersonnelAttendanceUserDetail> newAttendanceUserDetails = new ArrayList<>();
//                                if (isAbsenteeism.equals("1")) {  //旷工
//                                    String absenteeism = request.getParameter("absenteeism");
//                                    JSONArray jsonArray = JSONArray.fromObject(absenteeism);
//                                    List absenteeismList = JSONArray.toList(jsonArray);
//                                    if (absenteeismList != null && absenteeismList.size() > 0) {
//                                        for (int i = 0; i < absenteeismList.size(); i++) {
//                                            JSONObject absenteeismJson = JSONObject.fromObject(absenteeismList.get(i));
//                                            String aBeginDate = absenteeismJson.getString("aBeginDate");//开始时间
//                                            String aEndDate = absenteeismJson.getString("aEndDate");//结束时间
////                                            PersonnelAttendanceUserDetail attendanceUserDetail = this.addAttendanceUserDetail(sdf.parse(aBeginDate), sdf.parse(aEndDate), "7", "2", null, null, null, newAttendanceUserDto, user, null, null, null, new Date());
//                                            PersonnelAttendanceUserDetail attendanceUserDetail = this.addAttendanceUserDetail(NewDateUtils.dateFromString(aBeginDate, sdf), NewDateUtils.dateFromString(aEndDate, sdf), "7", "2", null,null, null, null, newAttendanceUserDto, user, null, null, null, new Date());
//                                            newAttendanceUserDetails.add(attendanceUserDetail);
//                                        }
//                                    }
//                                }
//                                if (isLeave.equals("1")) {  //有请假
//                                    String leave = request.getParameter("leaveList");//请假列表
//                                    JSONArray jsonArray = JSONArray.fromObject(leave);
//                                    List leaveList = JSONArray.toList(jsonArray);
//                                    if (leaveList != null && leaveList.size() > 0) {
//                                        for (int i = 0; i < leaveList.size(); i++) {
//                                            JSONObject jo = JSONObject.fromObject(leaveList.get(i));
//                                            String leaveBeginDate = jo.getString("leaveBeginDate"); //开始时间
//                                            String leaveEndDate = jo.getString("leaveEndDate");//结束时间
//                                            String leaveType = jo.getString("leaveType");//请假类型 1-事假,2-病假,3-年假,4-调休,5-婚假,6-产假,7-陪产假,8-路途假,9-其他'
//                                            String leaveReason = jo.getString("leaveReason");//请假原因
////                                            PersonnelAttendanceUserDetail attendanceUserDetail = this.addAttendanceUserDetail(sdf.parse(leaveBeginDate), sdf.parse(leaveEndDate), "5", "2", leaveType, null, leaveReason, newAttendanceUserDto, user, null, null, null, new Date());
//                                            PersonnelAttendanceUserDetail attendanceUserDetail = new PersonnelAttendanceUserDetail();
//                                            Integer leaveType1 = Integer.parseInt(leaveType);
//                                            if (leaveType1>0) {
//                                                attendanceUserDetail = this.addAttendanceUserDetail(NewDateUtils.dateFromString(leaveBeginDate, sdf), NewDateUtils.dateFromString(leaveEndDate, sdf), "5", "2",null, leaveType1, null, leaveReason, newAttendanceUserDto, user, null, null, null, new Date());
//                                            }else {
//                                                leaveType1 = -leaveType1;
//                                                attendanceUserDetail = this.addAttendanceUserDetail(NewDateUtils.dateFromString(leaveBeginDate, sdf), NewDateUtils.dateFromString(leaveEndDate, sdf), "5", "2",null, leaveType1, null, leaveReason, newAttendanceUserDto, user, null, null, null, new Date());
//                                            }
//                                            newAttendanceUserDetails.add(attendanceUserDetail);
//                                        }
//                                    }
//                                }
//                                if (isOverTime.equals("1")) {   //有加班
//                                    String over = request.getParameter("overList");//加班列表
//                                    JSONArray overArray = JSONArray.fromObject(over);
//                                    List overList = JSONArray.toList(overArray);
//                                    if (overList != null && overList.size() > 0) {
//                                        Double overDurationTotal = null;
//                                        for (int i = 0; i < overList.size(); i++) {
//                                            JSONObject jo = JSONObject.fromObject(overList.get(i));
//                                            String overDuration = jo.getString("overDuration");//时长
//                                            String overReason = jo.getString("overReason");//加班事由
//                                            String overMemo = jo.getString("overMemo");//备注
//                                            if (overDurationTotal != null) {
//                                                overDurationTotal = overDurationTotal + Double.valueOf(overDuration);
//                                            } else {
//                                                overDurationTotal = Double.valueOf(overDuration);
//                                            }
//                                            PersonnelAttendanceUserDetail attendanceUserDetail = this.addAttendanceUserDetail(null, null, "8", "2", null,null, null, overReason, newAttendanceUserDto, user, overDuration, overMemo, null, new Date());
//                                            newAttendanceUserDetails.add(attendanceUserDetail);
//                                        }
//                                    }
//                                }
//                                if (!MyStrings.nulltoempty(upIsNormal).isEmpty() && "0".equals(upIsNormal)) {  // 上班考勤 1-正常，0-迟到
//                                    newAttendanceUserDto.setBeginState("2");
//                                    PersonnelAttendanceUserDetail attendanceUserDetail = this.addAttendanceUserDetail(null, null, "2", "2", null,null, null, null, newAttendanceUserDto, user, null, null, "1", new Date()); //上班 2-迟到
//                                    newAttendanceUserDetails.add(attendanceUserDetail);
//                                } else {
//                                    newAttendanceUserDto.setBeginState(upIsNormal);
//                                }
//                                if (!MyStrings.nulltoempty(downIsNormal).isEmpty() && "0".equals(downIsNormal)) {   //下班考勤 1-正常，0-早退
//                                    newAttendanceUserDto.setEndState("3");
//                                    PersonnelAttendanceUserDetail attendanceUserDetail = this.addAttendanceUserDetail(null, null, "3", "2", null,null, null, null, newAttendanceUserDto, user, null, null, "2", new Date());  //下班 3-早退
//                                    newAttendanceUserDetails.add(attendanceUserDetail);
//                                } else {
//                                    newAttendanceUserDto.setEndState(downIsNormal);
//                                }
//                                newAttendanceUserDto.setAttendanceUserDetailList(newAttendanceUserDetails);
//                                JsonConfig jsonConfig = new JsonConfig();
//                                jsonConfig.setExcludes(new String[]{"personnelAttendanceUser", "personnelAttendanceUserDetailHashSet", "personnelAttendanceUserDetailHistoryHashSet", "personnelAttendanceUserHistoryHashSet"});
//                                jsonConfig.registerJsonValueProcessor(java.util.Date.class, new JsonDateValueProcessor());// 注入处理Date类
//                                JSONArray arrayNew = JSONArray.fromObject(newAttendanceUserDto, jsonConfig);
//                                approvalData.setNewValue(arrayNew.toString());
//                                approvalDataDao.save(approvalData);
//
//                                ApprovalProcess approvalProcess = new ApprovalProcess();
//                                approvalProcess.setInstance(approvalInstance.getId());
//                                approvalProcess.setOrg(user.getOid());
//                                approvalProcess.setFromUser(user.getUserID());
//                                approvalProcess.setUserName(user.getUserName());
//                                approvalProcess.setBusinessType(31);
//
//                                approvalProcess.setApproveStatus("1");
//                                approvalProcess.setDescription(approvalInstance.getTitle());
//                                approvalProcess.setLevel(1);
//                                approvalProcess.setToUser(approvalFlow.getToUserId());
//                                approvalProcess.setToUserName(approvalFlow.getToUser());
//                                approvalProcess.setAskName(user.getUserName());
//                                approvalProcess.setCreateDate(now);
//                                approvalProcessDao.save(approvalProcess);
//                                User approvalUser = userService.getUserByID(approvalFlow.getToUserId());
//                                HashMap<String, Object> map = new HashMap<>();
//                                map.put("approvalInstance", approvalInstance);
//                                //角标推送
//                                //推给超管，审批角标加1，列表加1
//                                swMessageService.rejectSend(1, 1, map, approvalFlow.getToUserId().toString(), "/attendanceApprovalInstance", approvalInstance.getTitle(), approvalInstance.getTitle(), approvalUser, "attendanceOfficerSubmitEdit");
//                                //推给总务，申请角标不变，列表加1
//                                swMessageService.rejectSend(0, 1, map, user.getUserID().toString(), "/attendanceApplyInstance", approvalInstance.getTitle(), approvalInstance.getTitle(), user, "attendanceEditApply");
//                            }
//                        }
//                    }
//                }
//            }
//        }
//        return 1;
//    }

//    @Override
//    public List<ApprovalInstance> selectApprovalInstanceListByUserId(int userId, String description) {
//        String hql = " from ApprovalInstance where creator = " + userId + " and status='1' and description='" + description + "'";
//        List<ApprovalInstance> approvalInstanceList = approvalInstanceDao.getListByHQL(hql);
//        return approvalInstanceList;
//    }

//    @Override
//    public List<ApprovalInstance> selectApproveApprovalInstanceListByUserId(int userId, String description) {
//        String hql = " from ApprovalInstance where auditor = " + userId + " and status='1' and description='" + description + "'";
//        List<ApprovalInstance> approvalInstanceList = approvalInstanceDao.getListByHQL(hql);
//        return approvalInstanceList;
//    }

//    @Override
//    public int handleApprovalInstance(User user, Integer id, String approveMemo, int type) {
//        int status = 1;
//        Date now = new Date(System.currentTimeMillis());
//        ApprovalInstance approvalInstance = approvalInstanceDao.get(id);
//        if (type == 0) {  //驳回
//            ApprovalProcess approvalProcess = new ApprovalProcess();
//            approvalProcess.setInstance(approvalInstance.getId());
//            approvalProcess.setOrg(user.getOid());
//            approvalProcess.setFromUser(user.getUserID());
//            approvalProcess.setUserName(user.getUserName());
//            approvalProcess.setBusinessType(31);
//            approvalProcess.setBusiness(approvalInstance.getBusiness());
//            approvalProcess.setApproveStatus("3");
//            approvalProcess.setDescription(approvalInstance.getTitle());
//            approvalProcess.setLevel(1);
//            approvalProcess.setToUser(approvalInstance.getAuditor());
//            approvalProcess.setToUserName(approvalInstance.getAuditorName());
//            approvalProcess.setAskName(user.getUserName());
//            approvalProcess.setCreateDate(now);
//            approvalProcess.setReason(approveMemo);
//            approvalProcess.setHandleTime(now);
//            approvalProcessDao.save(approvalProcess);
//
//            approvalInstance.setStatus("3");
//            approvalInstance.setUpdateDate(now);
//            approvalInstance.setUpdator(user.getUserID());
//            approvalInstance.setUpdateName(user.getUserName());
//            approvalInstance.setApproveMemo(approveMemo);
//            approvalInstanceDao.update(approvalInstance);
//
//            String hqlData = "from ApprovalData where instance=" + id;
//            List<ApprovalData> approvalDataList = approvalDataDao.getListByHQL(hqlData);
//            for (ApprovalData a : approvalDataList) {
//                a.setApproveStatus("3");
//                a.setApproveMemo(approveMemo);
//                a.setUpdateTime(now);
//                a.setUpdator(user.getUserID());
//                a.setUpdateName(user.getUserName());
//                approvalDataDao.update(a);
//            }
//            HashMap<String, Object> map = new HashMap<>();
//            map.put("approvalInstance", approvalInstance);
//            //否决的推送
//            swMessageService.rejectSend(-1, -1, map, user.getUserID().toString(), "/attendanceApprovalInstance", approvalInstance.getTitle(), approvalInstance.getTitle(), user, "attendanceOfficerSubmitEdit");
//            //消息推送，给总务
//            String messageCont = approvalInstance.getTitle() + "申请被驳回了！";
//            String memo = "操作时间  " + user.getUserName() + " " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
//            userSuspendMsgService.saveUserSuspendMsg(1, messageCont, messageCont, memo
//                    , approvalInstance.getCreator(), "attendanceChangeDetail", approvalInstance.getId());//推送我的消息
//            User generalUser = userService.getUserByID(approvalInstance.getCreator());
//            //推给总务，申请角标不变，列表加1
//            swMessageService.rejectSend(0, -1, map, approvalInstance.getCreator().toString(), "/attendanceApplyInstance", approvalInstance.getTitle(), approvalInstance.getTitle(), generalUser, "attendanceEditApply");
//
//        } else {
//            ApprovalProcess approvalProcess = new ApprovalProcess();
//            approvalProcess.setInstance(approvalInstance.getId());
//            approvalProcess.setOrg(user.getOid());
//            approvalProcess.setFromUser(user.getUserID());
//            approvalProcess.setUserName(user.getUserName());
//            approvalProcess.setBusinessType(31);
//            approvalProcess.setBusiness(approvalInstance.getBusiness());
//            approvalProcess.setApproveStatus("2");
//            approvalProcess.setDescription(approvalInstance.getTitle());
//            approvalProcess.setLevel(1);
//            approvalProcess.setToUser(approvalInstance.getAuditor());
//            approvalProcess.setToUserName(approvalInstance.getAuditorName());
//            approvalProcess.setAskName(user.getUserName());
//            approvalProcess.setCreateDate(now);
//            approvalProcess.setHandleTime(now);
//            approvalProcessDao.save(approvalProcess);
//            approvalInstance.setStatus("2");
//            approvalInstance.setUpdateDate(now);
//            approvalInstance.setUpdator(user.getUserID());
//            approvalInstance.setUpdateName(user.getUserName());
//            approvalInstanceDao.update(approvalInstance);
//            String hqlData = "from ApprovalData where instance=" + id;
//            List<ApprovalData> approvalDataList = approvalDataDao.getListByHQL(hqlData);
//            for (ApprovalData a : approvalDataList) {
//                a.setApproveStatus("2");
//                a.setUpdateTime(now);
//                a.setUpdator(user.getUserID());
//                a.setUpdateName(user.getUserName());
//                approvalDataDao.update(a);
//            }
//            AttendanceUserDto newAttendanceUser = JSON.parseArray(approvalDataList.get(0).getNewValue(), AttendanceUserDto.class).get(0);
//            User attendanceUser = userService.getUserByID(newAttendanceUser.getUser());   //要修改考勤的人员
//            //此处为无需考勤，修改为需要考勤的数据变化
//            if ("1".equals(approvalInstance.getOperation())) {
//                //开始处理新数据
//                List<PersonnelAttendanceUserDetail> newAttendanceUserDetails = newAttendanceUser.getAttendanceUserDetailList();
//                Double overDurationTotal = 0.0D;//总时长
//                int overSize = 0;
//                int leaveSize = 0;
//                Double leaveDuration = 0.0D;
//                int absenteeismSize = 0;
//                PersonnelAttendanceUser personnelAttendanceUser = new PersonnelAttendanceUser();
//                BeanUtils.copyPropertiesIgnoreNull(newAttendanceUser, personnelAttendanceUser);
//                personnelAttendanceUserDao.save(personnelAttendanceUser);
//                personnelAttendanceMonthlyService.ClearDay(attendanceUser, newAttendanceUser.getAttendanceDate());  //清除某人某天的考勤月表
//                for (PersonnelAttendanceUserDetail pd : newAttendanceUserDetails) {
//                    pd.setId(null);
//                    pd.setAttendanceId(personnelAttendanceUser.getId());
//                    pd.setPersonnelAttendanceUser(personnelAttendanceUser);
//                    personnelAttendanceUserDetailDao.save(pd);
//                    if ("8".equals(pd.getType())) {//加班
//                        overDurationTotal = overDurationTotal + Double.valueOf(pd.getDuration());
//                        overSize++;
//                    } else if ("5".equals(pd.getType())) {
//                        leaveSize++;
//                        leaveDuration+=pd.getDuration();
//                    } else if ("7".equals(pd.getType())) {
//                        absenteeismSize++;
//                    }
//                }
//                //加班
////                    Byte overNum = (Byte) overResult.get("overtime"); //加班次数
////                    BigDecimal overDuration = (BigDecimal) overResult.get("overtimeDuration");  //加班时长
//                if (overSize > 0)
//                    personnelAttendanceMonthlyService.SetOvertime(attendanceUser, newAttendanceUser.getAttendanceDate(), (byte) overSize, overDurationTotal.floatValue());
//                //请假
//                if (leaveSize > 0)
//                    personnelAttendanceMonthlyService.SetLeave(attendanceUser, newAttendanceUser.getAttendanceDate(), (byte) leaveSize, leaveDuration.floatValue());
//                //矿工
//                if (absenteeismSize > 0)
//                    personnelAttendanceMonthlyService.SetAbsenteeisme(attendanceUser, newAttendanceUser.getAttendanceDate(), (byte) absenteeismSize);
//                if ("2".equals(personnelAttendanceUser.getBeginState())) {  // 上班考勤 1-正常，0-迟到
//                    personnelAttendanceMonthlyService.SetLate(attendanceUser, personnelAttendanceUser.getAttendanceDate(), true);
//                }
//                if ("3".equals(personnelAttendanceUser.getEndState())) {   //下班考勤 1-正常，0-早退
//                    personnelAttendanceMonthlyService.SetLeaveEarly(attendanceUser, personnelAttendanceUser.getAttendanceDate(), true);
//                }
//            } else {
//                //数据变化
//                AttendanceUserDto oldAttendanceUser = JSON.parseArray(approvalDataList.get(0).getOldValue(), AttendanceUserDto.class).get(0);
//                //先处理旧数据
//                personnelAttendanceMonthlyService.ClearDay(attendanceUser, oldAttendanceUser.getAttendanceDate());//清除某人某天的考勤月表
//
//                PersonnelAttendanceUserHistory oldAttendanceUserHistory = new PersonnelAttendanceUserHistory();//存储改前考勤信息
//
//                BeanUtils.copyPropertiesIgnoreNull(oldAttendanceUser, oldAttendanceUserHistory);//将p与ph中一样的值从p复制到ph中，不一样的值，另外set
//                oldAttendanceUserHistory.setPersonnelAttendanceUser(oldAttendanceUser);
//                oldAttendanceUserHistory.setUpdateDesc(approvalInstance.getApplyMemo());
//                oldAttendanceUserHistory.setCreateDate(now);
//                personnelAttendanceUserHistoryDao.save(oldAttendanceUserHistory);
//                //把系统默认矿工的加上
//                List<PersonnelAttendanceUserDetail> attendanceUserDetailsList = this.getPersonnelAttendanceUserDetailById(oldAttendanceUserHistory.getPersonnelAttendanceUser().getId());
//                for (PersonnelAttendanceUserDetail pd : attendanceUserDetailsList) {
//                    PersonnelAttendanceUserDetailHistory ph = new PersonnelAttendanceUserDetailHistory();
//                    BeanUtils.copyPropertiesIgnoreNull(pd, ph);  //将pd与pdh中一样的值从p复制到ph中，不一样的值，另外set
//                    ph.setAttendance(oldAttendanceUserHistory.getId());
//                    personnelAttendanceUserDetailHistoryDao.save(ph);
//                    personnelAttendanceUserDetailDao.delete(pd);  //清除考勤明细
//                }
//                //开始处理新数据
//                List<PersonnelAttendanceUserDetail> newAttendanceUserDetails = newAttendanceUser.getAttendanceUserDetailList();
//                Double overDurationTotal = 0.0D;//总时长
//                Double leaveDurationTotal = 0.0D;//总时长
//                int overSize = 0;
//                int leaveSize = 0;
//                int absenteeismSize = 0;
//                PersonnelAttendanceUser personnelAttendanceUser = personnelAttendanceUserDao.get(newAttendanceUser.getId());
//                personnelAttendanceUser.setUpdateDate(now);
//                personnelAttendanceUser.setPmAttendance(newAttendanceUser.getPmAttendance());
//                personnelAttendanceUser.setAmAttendance(newAttendanceUser.getAmAttendance());
//                personnelAttendanceUser.setEndState(newAttendanceUser.getEndState());
//                personnelAttendanceUser.setBeginState(newAttendanceUser.getBeginState());
//                personnelAttendanceUser.setType(newAttendanceUser.getType());
//                personnelAttendanceUser.setUpdateName(newAttendanceUser.getUserName());
//                personnelAttendanceUser.setUpdator(newAttendanceUser.getUpdator());
//                personnelAttendanceUserDao.update(personnelAttendanceUser);
//
//                for (PersonnelAttendanceUserDetail pd : newAttendanceUserDetails) {
//                    pd.setId(null);
//                    pd.setAttendanceId(personnelAttendanceUser.getId());
//                    pd.setPersonnelAttendanceUser(personnelAttendanceUser);
//                    personnelAttendanceUserDetailDao.save(pd);
//                    if ("8".equals(pd.getType())) {//加班
//                        overDurationTotal += overDurationTotal;
//                        overSize++;
//                    } else if ("5".equals(pd.getType())) {
//                        leaveSize++;
//                        leaveDurationTotal += pd.getDuration();
//                    } else if ("7".equals(pd.getType())) {
//                        absenteeismSize++;
//                    }
//                }
//                //加班
////                    Byte overNum = (Byte) overResult.get("overtime"); //加班次数
////                    BigDecimal overDuration = (BigDecimal) overResult.get("overtimeDuration");  //加班时长
//                if (overSize > 0)
//                    personnelAttendanceMonthlyService.SetOvertime(attendanceUser, newAttendanceUser.getAttendanceDate(), (byte) overSize, overDurationTotal.floatValue());
//                //请假
//                if (leaveSize > 0)
//                    personnelAttendanceMonthlyService.SetLeave(attendanceUser, newAttendanceUser.getAttendanceDate(), (byte) leaveSize, leaveDurationTotal.floatValue());
//                //矿工
//                if (absenteeismSize > 0)
//                    personnelAttendanceMonthlyService.SetAbsenteeisme(attendanceUser, newAttendanceUser.getAttendanceDate(), (byte) absenteeismSize);
//                if ("2".equals(personnelAttendanceUser.getBeginState())) {  // 上班考勤 1-正常，0-迟到
//                    personnelAttendanceMonthlyService.SetLate(attendanceUser, personnelAttendanceUser.getAttendanceDate(), true);
//                }
//                if ("3".equals(personnelAttendanceUser.getEndState())) {   //下班考勤 1-正常，0-早退
//                    personnelAttendanceMonthlyService.SetLeaveEarly(attendanceUser, personnelAttendanceUser.getAttendanceDate(), true);
//                }
//            }
//
//            User messageUser = userService.getUserByID(approvalInstance.getCreator());
//            this.commonOverTime(messageUser, newAttendanceUser.getAttendanceDate(),attendanceUser.getUserID()); //将机构中有正在处理的计划和申报加班进行最终处理
//
//            HashMap<String, Object> map = new HashMap<>();
//            map.put("approvalInstance", approvalInstance);
//            //同意的推送
//            swMessageService.rejectSend(-1, -1, map, user.getUserID().toString(), "/attendanceApprovalInstance", approvalInstance.getTitle(), approvalInstance.getTitle(), user, "attendanceOfficerSubmitEdit");
//            //消息推送，给总务
//            String messageCont = approvalInstance.getTitle() + "申请被批准了！";
//            String memo = "操作时间  " + user.getUserName() + " " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
//            userSuspendMsgService.saveUserSuspendMsg(1, messageCont, messageCont, memo
//                    , approvalInstance.getCreator(), "attendanceChangeDetail", approvalInstance.getId());//推送我的消息
//            User generalUser = userService.getUserByID(approvalInstance.getCreator());
//            //推给总务，申请角标不变，列表加1
//            swMessageService.rejectSend(0, -1, map, approvalInstance.getCreator().toString(), "/attendanceApplyInstance", approvalInstance.getTitle(), approvalInstance.getTitle(), generalUser, "attendanceEditApply");
//
//
//            //消息推送，给被修改得员工
//            String messageCont1 = "您" + new SimpleDateFormat("yyyy-MM-dd").format(newAttendanceUser.getAttendanceDate()) + " 的考勤修改了，具体可到“我的考勤”中查看";
//            userSuspendMsgService.saveUserSuspendMsg(1, messageCont1, messageCont1, memo
//                    , attendanceUser.getUserID(), "attendanceChangeDetail", approvalInstance.getId());//推送我的消息
//        }
//        return status;
//    }

    @Override
    public int judgeUpdateAttendanceUser(User user, Integer id, Integer attendanceUserId, String attendanceDate) {
        int status = 1;
        if (id != null) {
            String hql = " from ApprovalInstance where business=" + id + " and status='1' and description='考勤修改'";
            List<ApprovalInstance> approvalInstanceList = approvalInstanceDao.getListByHQL(hql);
            if (approvalInstanceList.size() > 0)
                status = 0;
        } else {
            String hql = " from ApprovalData where rowId=" + attendanceUserId + " and approveStatus='1' and oldValue='" + attendanceDate + "'";
            List<ApprovalData> approvalDataList = approvalDataDao.getListByHQL(hql);
            if (approvalDataList.size() > 0)
                status = 0;
        }
        return status;
    }

    //考勤修改-我的申请
    //传值 userId
    //source来源 ，1申请人，2审批人
    //type 1通过，0驳回   status 1，七日，2本月，3自定义
    //startTime endTime
    @Override
    public ApprovalDto approvalInstanceByCriterionList(SelectCriterionDto selectCriterionDto) {
        ApprovalDto approvalDto = new ApprovalDto();
        String s = "yyyy-MM-dd HH:mm:ss";
        String hql = " from ApprovalInstance where 1=1";
        if (selectCriterionDto.getType() == 1) {
            hql = hql + " and status='2' ";
        } else if (selectCriterionDto.getType() == 0) {
            hql = hql + " and status='3' ";
        }
        if (selectCriterionDto.getSource() == 1)  //申请人
        {
            hql = hql + " and creator = " + selectCriterionDto.getUserId() + " and description='个人提交的考勤修改'";
        } else if (selectCriterionDto.getSource() == 2)   //审批人
        {
            hql = hql + " and auditor =" + selectCriterionDto.getUserId() + " and description='考勤修改'";
        }
        if (selectCriterionDto.getStatus() == 1) {
            Date dateEnd = NewDateUtils.getLastTimeOfDay(NewDateUtils.today());
            Date dateStart = NewDateUtils.changeDay(dateEnd, -6);
            selectCriterionDto.setEndTime1(dateEnd);
            selectCriterionDto.setStartTime1(dateStart);
        } else if (selectCriterionDto.getStatus() == 2) {
            Date dateEnd = NewDateUtils.getLastTimeOfMonth(NewDateUtils.today());
            Date dateStart = NewDateUtils.changeMonth(NewDateUtils.today(), 0);
            selectCriterionDto.setEndTime1(dateEnd);
            selectCriterionDto.setStartTime1(dateStart);
        } else {
            selectCriterionDto.setEndTime1(NewDateUtils.dateFromString(selectCriterionDto.getEndTime() + " 23:59:59", s));
            selectCriterionDto.setStartTime1(NewDateUtils.dateFromString(selectCriterionDto.getStartTime() + " 00:00:00", s));
        }
        String startTime = NewDateUtils.dateToString(selectCriterionDto.getStartTime1(), s);
        String EndTime = NewDateUtils.dateToString(selectCriterionDto.getEndTime1(), s);
        hql = hql + " and createDate between '" + startTime + "' and '" + EndTime + "'";
        List<ApprovalInstance> approvalInstanceList = approvalInstanceDao.getListByHQL(hql);
        approvalDto.setApprovalInstanceList(approvalInstanceList);
        approvalDto.setCount(approvalInstanceList.size());
        approvalDto.setStartTime(selectCriterionDto.getStartTime1());
        approvalDto.setEndTime(selectCriterionDto.getEndTime1());
        return approvalDto;
    }

//    @Override
//    public ApprovalInstanceDto applyApprovalInstanceDetail(int instance) {
//        ApprovalInstanceDto approvalInstanceDto = new ApprovalInstanceDto();
//        String hqlProcess = "from ApprovalProcess where instance=" + instance;
//        List<ApprovalProcess> approvalProcessList = approvalProcessDao.getListByHQL(hqlProcess);
//        approvalInstanceDto.setApprovalProcessList(approvalProcessList);
//        String hqlData = "from ApprovalData where instance=" + instance;
//        List<ApprovalData> approvalDataList = approvalDataDao.getListByHQL(hqlData);
//        approvalInstanceDto.setApprovalDataList(approvalDataList);
//        return approvalInstanceDto;
//    }

//    @Override
//    public void initializeWorkAttendanceApply(int org) {
//        User superUser = userService.getUserByRoleCode(org, "super");//董事长
//        String hql = "from ApprovalItem where belongTo=" + org + " and code='workAttendanceApply'";
//        List<ApprovalItem> approvalItemList = approvalItemDao.getListByHQL(hql);
//        ApprovalItem approvalItem = approvalItemList.get(0);
//        String hqlFlow = "from ApprovalFlow where item = " + approvalItem.getId();
//        List<ApprovalFlow> approvalFlowList = approvalFlowDao.getListByHQL(hqlFlow);
//        if (approvalItem.getStatus() == 0 && approvalFlowList.isEmpty()) {
//            approvalItem.setStatus(1);
//            approvalItem.setLevel(1);
//            approvalItem.setCreateDate(new Date());
//            approvalItem.setOpenDate(NewDateUtils.today(new Date()));
//            approvalItem.setApproveStatus("2");
//            approvalItem.setAuditDate(new Date());
//            approvalItem.setUpperLimit(new BigDecimal(-1));
//            approvalItemDao.update(approvalItem);
//
//            ApprovalFlow kaoQinFlow = new ApprovalFlow();
//            kaoQinFlow.setToUser("董事长");
//            kaoQinFlow.setType(1);
//            kaoQinFlow.setLevel(1);
//            kaoQinFlow.setItem(approvalItem);
//            kaoQinFlow.setLimitAmount(new BigDecimal(-1));
//            kaoQinFlow.setAmountCeiling((double) -1);
//            kaoQinFlow.setToUserId(superUser.getUserID());
//            kaoQinFlow.setUserName(superUser.getUserName());
//            approvalFlowDao.save(kaoQinFlow);
//        }
//    }

    /**
     * <AUTHOR>
     * @date 2018/7/31 16:26
     * 将当天加班/请假添加到考勤里
     * type 1-加班 2-请假 3-提前结束请假 4-补报请假
     * leaveOverDate (请假/加班日期)、userId (加班/请假人员id)、approvelUserId（审批人id）
     * userDetailType(此暂时未用到)
     */
    @Override
    public void leaveOvertimeToAttendance(Integer type, Integer userId, Integer approvelUserId, Integer business, String businessType,Integer leaveType, Double duration, Date beginDate, Date endDate, String reason) {
        Date today = NewDateUtils.today();
        User user = userService.getUserByID(userId);

        Integer deptId = 0;
        if (!MyStrings.nulltoempty(user.getDepartment()).isEmpty()) {
            deptId = Integer.parseInt(user.getDepartment());
        }
        if (Integer.valueOf(1).equals(type) || Integer.valueOf(4).equals(type)) { //加班时，时间可以为已过去的某天 【4-补报请假的，时间是过去的某天】
            today = NewDateUtils.today(beginDate);
        }

        if (Integer.valueOf(4).equals(type)){  //补报请假的
            while (today.getTime()<=NewDateUtils.today(endDate).getTime()){
                //查找当前使用的考勤设置
                PersonnelAttendanceConfig personnelAttendanceConfig = this.getPersonnelAttendanceConfigByDept(user.getOid(), deptId, today);  //获取此时、此部门所在的考勤规则
                if (personnelAttendanceConfig != null) {  //此职工需要考勤的，则进入以下的方法
                    Date attendanceBeginTime = NewDateUtils.joinDateTimeString(today, NewDateUtils.dateToString(personnelAttendanceConfig.getBeginTime(), "HH:mm"));  //考勤上班时间
                    Date attendanceEndTime = NewDateUtils.joinDateTimeString(today, NewDateUtils.dateToString(personnelAttendanceConfig.getEndTime(), "HH:mm"));   //考勤结束时间
                    //考勤设置结束

                    PersonnelAttendanceException personnelAttendanceException = this.getPersonnelAttendanceExceptionByOid(user.getOid(), today, null);  //查看某天是否需要考勤
                    if (personnelAttendanceException != null && "2".equals(personnelAttendanceException.getType())) {    //2-班
                        PersonnelAttendanceUser personnelAttendanceUser = workAttendanceService.getPersonnelAttendanceUserByUserIdAndId(null, userId, today, null); //查询某人某天的考勤记录
                        if (personnelAttendanceUser != null) {   //补报请假的直接进入考勤
//                            if (today.getTime() == NewDateUtils.today(beginDate).getTime()) {   //如果是当天的请假，则直接进入考勤系统
                                if (endDate.compareTo(attendanceBeginTime) > 0 && today.compareTo(attendanceEndTime) < 0) {
                                    if (today.compareTo(attendanceBeginTime) <= 0) {  //请假开始时间包括考勤上班时间
                                        personnelAttendanceUser.setBeginState("5");  //上班考勤状态 5-已请假
                                        personnelAttendanceUser.setAmAttendance(attendanceBeginTime);
                                    }
                                    if (endDate.compareTo(attendanceEndTime) >= 0) {   //请假结束时间包括考勤下班时间
                                        personnelAttendanceUser.setEndState("5");  //下班考勤状态 5-已请假
                                        personnelAttendanceUser.setPmAttendance(attendanceEndTime);
                                    }

                                    //上班或下班有请假后，才改变考勤的状态
                                    if ("0".equals(personnelAttendanceUser.getType()) || "1".equals(personnelAttendanceUser.getType()) || "5".equals(personnelAttendanceUser.getType()) || "8".equals(personnelAttendanceUser.getType())) {
                                        personnelAttendanceUser.setType((byte)5);   //总类型：0-未考勤 1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
                                    } else {
                                        personnelAttendanceUser.setType((byte)9);   //总类型：0-未考勤 1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
                                    }
                                    if(duration==null) {
                                        duration = 0.0D;
                                    }
                                    workAttendanceService.addPersonnelAttendanceUserDetail(beginDate, endDate, "5", "1", businessType, leaveType, business, reason, personnelAttendanceUser, null, String.valueOf(duration), null, null, new Date());
                                    //如果确认已经保存过工作时间，不需要重设
                                    Float workHours = personnelAttendanceMonthlyService.GetWorkingHours(user, today);
                                    if(Float.valueOf(0.0f).equals(workHours)) {
                                        personnelAttendanceMonthlyService.SetWorkingHours(user, today, personnelAttendanceUser.GetWorkHours().floatValue());
                                    }
                                    Pair<Byte, Float>  leave = personnelAttendanceMonthlyService.GetLeave(user, today);
                                    personnelAttendanceMonthlyService.SetLeave(user, today, (byte)(leave.getLeft()+1), Double.valueOf(leave.getRight()+duration).floatValue());
                                }
//                            }
                        }
                    }
                }
                today = NewDateUtils.changeDay(today,1);  //加一天
            }

        }else {
            //查找当前使用的考勤设置
            PersonnelAttendanceConfig personnelAttendanceConfig = this.getPersonnelAttendanceConfigByDept(user.getOid(), deptId, today);  //获取此时、此部门所在的考勤规则
            Integer source = 1; //考勤模式:1-考勤宝,2-手工录入
            if (personnelAttendanceConfig != null) {  //此职工需要考勤的，则进入以下的方法
                if (ConfigAttendancePattern.manualEntry.getIndex().equals(personnelAttendanceConfig.getAttendancePattern())){
//                if (ConfigAttendancePattern.manualEntry.getIndex().compareTo(personnelAttendanceConfig.getAttendancePattern())==0){
                    source = 2;
                }
                Date attendanceBeginTime = NewDateUtils.joinDateTimeString(today, NewDateUtils.dateToString(personnelAttendanceConfig.getBeginTime(), "HH:mm"));  //考勤上班时间
                Date attendanceEndTime = NewDateUtils.joinDateTimeString(today, NewDateUtils.dateToString(personnelAttendanceConfig.getEndTime(), "HH:mm"));   //考勤结束时间
                //考勤设置结束

                PersonnelAttendanceException personnelAttendanceException = this.getPersonnelAttendanceExceptionByOid(user.getOid(), today, null);  //查看某天是否需要考勤
                if (personnelAttendanceException != null && "2".equals(personnelAttendanceException.getType())) {    //2-班
                    PersonnelAttendanceUser personnelAttendanceUser = workAttendanceService.getPersonnelAttendanceUserByUserIdAndId(null, userId, today, null); //查询某人某天的考勤记录
                    if (personnelAttendanceUser != null && type != null) {
                        if (type == 2) {  //2-请假
                            if (today.getTime() == NewDateUtils.today(beginDate).getTime()) {   //如果是当天的请假，则直接进入考勤系统
                                if (endDate.compareTo(attendanceBeginTime) > 0 && beginDate.compareTo(attendanceEndTime) < 0) {
                                    if (beginDate.compareTo(attendanceBeginTime) <= 0) {  //请假开始时间包括考勤上班时间
                                        personnelAttendanceUser.setBeginState("5");  //上班考勤状态 5-已请假
                                        personnelAttendanceUser.setAmAttendance(attendanceBeginTime);
                                    }
                                    if (endDate.compareTo(attendanceEndTime) >= 0) {   //请假结束时间包括考勤下班时间
                                        personnelAttendanceUser.setEndState("5");  //下班考勤状态 5-已请假
                                        personnelAttendanceUser.setPmAttendance(attendanceEndTime);
                                    }

                                    //上班或下班有请假后，才改变考勤的状态
                                    if ("0".equals(personnelAttendanceUser.getType()) || "1".equals(personnelAttendanceUser.getType()) || "5".equals(personnelAttendanceUser.getType()) || "8".equals(personnelAttendanceUser.getType())) {
                                        personnelAttendanceUser.setType((byte)5);   //总类型：0-未考勤 1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
                                    } else {
                                        personnelAttendanceUser.setType((byte)9);   //总类型：0-未考勤 1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
                                    }
                                    if(duration==null) {
                                        duration = 0.0D;
                                    }
                                    workAttendanceService.addPersonnelAttendanceUserDetail(beginDate, endDate, "5", "1", businessType, leaveType, business, reason, personnelAttendanceUser, null, String.valueOf(duration), null, null, new Date());
                                    //如果确认已经保存过工作时间，不需要重设
                                    Float workHours = personnelAttendanceMonthlyService.GetWorkingHours(user, today);
                                    if(Float.valueOf(0.0f).equals(workHours)) {
                                        personnelAttendanceMonthlyService.SetWorkingHours(user, today, personnelAttendanceUser.GetWorkHours().floatValue());
                                    }
                                    Pair<Byte, Float>  leave = personnelAttendanceMonthlyService.GetLeave(user, today);
                                    personnelAttendanceMonthlyService.SetLeave(user, today, (byte)(leave.getLeft()+1), Double.valueOf(leave.getRight()+duration).floatValue());
                                }
                            }
                        } else if (type == 3) {   //提前结束请假
                            List<PersonnelAttendanceUserDetail> p = workAttendanceService.getPersonnelAttendanceUserDetailByBusiness(null, "5", null, business, null); //查出来仅有一条数据
                            if (p.size() > 0) {
                                for (PersonnelAttendanceUserDetail pp : p) {
                                    pp.setEndTime(endDate);  //下班时间(结束时间)
                                    pp.setDuration(duration);
                                    personnelAttendanceUserDetailDao.update(pp);
                                }
                            }

                            if (today.getTime() == NewDateUtils.today(endDate).getTime()) {   //当天的提前结束请假需要修改状态
                                personnelAttendanceUser = workAttendanceService.getPersonnelAttendanceUserByUserIdAndId(null, userId, endDate, null);
                                if (endDate.compareTo(attendanceBeginTime) <= 0) {  //计划上班时间在今天上班时间之前
                                    personnelAttendanceUser.setBeginState("0");  //上班状态 5-已请假
                                }
                                if (endDate.compareTo(attendanceEndTime) < 0) {   //计划上班时间在今天下班时间之前
                                    personnelAttendanceUser.setEndState("0");  //下班考勤状态 5-已请假
                                }
                            }
                        } else if (type == 1) {    //type 1-加班   加班可以不是当天的，有可能是已过日期的加班
                            PersonnelAttendanceUserDetail personnelAttendanceUserDetail = workAttendanceService.getPersonnelAttendanceUserDetail(user.getUserID(), personnelAttendanceUser.getAttendanceDate(),"8", business, null); //查出来仅有一条数据
                            if (personnelAttendanceUserDetail==null) {
                                if(duration==null) {
                                    duration = 0.0D;
                                }
                                workAttendanceService.addPersonnelAttendanceUserDetail(beginDate, endDate, "8", "1", businessType, null, business, reason, personnelAttendanceUser, null, String.valueOf(duration), null, null, new Date());
                                Pair<Byte, Float> overResult = personnelAttendanceMonthlyService.GetOvertime(user, beginDate);
                                personnelAttendanceMonthlyService.SetOvertime(user, beginDate, (byte) (overResult.getLeft() + 1), duration.floatValue()+overResult.getRight());
                            }else {
                                personnelAttendanceUserDetail.setBeginTime(beginDate);
                                personnelAttendanceUserDetail.setEndTime(endDate);
                                personnelAttendanceUserDetail.setDuration(duration);
                                personnelAttendanceUserDetailDao.update(personnelAttendanceUserDetail);  //可能是打卡的已经生产了详情，这次将实际的加班时间和时长更新下。

                                Pair<Byte, Float> overResult = personnelAttendanceMonthlyService.GetOvertime(user, beginDate);
//                                Byte overNum = (Byte) overResult.get("ouvertime"); //加班次数
//                                BigDecimal overDuration = (BigDecimal) overResult.get("ouvertimeDuration");  //加班时长
                                personnelAttendanceMonthlyService.SetOvertime(user, beginDate, (byte) (overResult.getLeft() + 1), duration.floatValue()+overResult.getRight());
                            }
                        }
                    }
                } else {    // 1-假
                    if (Integer.valueOf(1).equals(type)) {
                        this.defaultOverTime(null, user, deptId, NewDateUtils.today(beginDate), 1, personnelAttendanceConfig.getBeginTime(), personnelAttendanceConfig.getEndTime(),personnelAttendanceConfig.getBreakBegin(),personnelAttendanceConfig.getBreakEnd(),source);
                    }
                }
            }
        }
    }

    @Override
    public Map<String, Object> getAllTypeAttendance(Integer oid, Integer deptId, Integer userId, Date currentTime, Map<String, Object> map) {
        Integer userTotalNum = 0;  //应出勤人数
        List<PersonnelAttendanceUser> userList = new ArrayList<>();  //参与考勤人员（应出勤人）
        Integer notInAttendance = 0;  //不参与考勤人数
        List<UserDto> notInAttendanceUser = new ArrayList<>();//不参与考勤人员
        Integer realNum = 0;//实际出勤人数
        List<PersonnelAttendanceUser> realUser = new ArrayList<>();//实际出勤人
        Integer lateNum = 0;  //迟到人数
        List<PersonnelAttendanceUser> lateUser = new ArrayList<>();//迟到人员
        Integer leaveEarlyNum = 0;  //早退人数
        List<PersonnelAttendanceUser> leaveEarlyUser = new ArrayList<>();//早退人员
        Integer outsideNum = 0;  //外出人数
        List<PersonnelAttendanceUser> outsideUser = new ArrayList<>();//外出人员
        Integer leaveNum = 0;  //请假人数
        List<PersonnelAttendanceUser> leaveUser = new ArrayList<>();//请假人员
        Integer travelNum = 0;  //出差人数
        List<PersonnelAttendanceUser> travelUser = new ArrayList<>();//出差人员
        Integer absenteeismNum = 0;  //旷工人数
        List<PersonnelAttendanceUser> absenteeismUser = new ArrayList<>();//旷工人员
        Integer overTimeNum = 0;  //加班人数
        List<PersonnelAttendanceUser> overTimeUser = new ArrayList<>();//加班人员
        userList = getPersonnelAttendanceUserByOid(oid, deptId, userId, currentTime, null, null, 1); //参与考勤人员（应出勤人）【需要改成getUser方法】
        notInAttendanceUser = getNoUser(currentTime, oid, userId, deptId!=null?deptId.toString():null, null);  //不参与考勤人员
//        notInAttendanceUser = this.getUser(2, currentTime, oid, userId, deptId, null);  //不参与考勤人员
        notInAttendance = notInAttendanceUser.size();  //不考勤人数

        PersonnelAttendanceException personnelAttendanceException = this.getPersonnelAttendanceExceptionByOid(oid, currentTime, null);
        String workOrRest = null;  //当天是上班还是修假  1-假 2-班
        if (personnelAttendanceException != null && !"".equals(personnelAttendanceException.getType()) && personnelAttendanceException.getType() != null) {
            workOrRest = personnelAttendanceException.getType();  //当天是上班还是休息  1-假 2-班
            if ("2".equals(personnelAttendanceException.getType())) {
                userTotalNum = userList.size();  //应出勤人数

                for (PersonnelAttendanceUser pu : userList) {
                    if (!"0".equals(pu.getBeginState())) {    //只有上班考勤录入后，才能算出勤
                        User user = userService.getUserByID(pu.getUser());
                        pu.setUserName(user.getUserName());
                        if (!MyStrings.nulltoempty(user.getImgPath()).isEmpty()) {
                            pu.setImgPath(user.getImgPath());  //职工图片路径
                        }
                        if (pu.getDept() != null && pu.getDept() != 0) {
                            Organization organization = orgService.getOrgByOid(pu.getDept(), OrgService.OrgType.department);
                            pu.setDeptName(organization.getName());
                        }

                        realNum = realNum + 1;  //实际出勤人数
                        realUser.add(pu);   //实际出勤人员

                        //获取详情中的数据
                        List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetailList = workAttendanceService.getPersonnelAttendanceUserDetailByBusiness(pu.getId(), "10", null, null, null);
                        if (personnelAttendanceUserDetailList.size()>0){
                            pu.setPersonnelAttendanceUserDetailList(personnelAttendanceUserDetailList);
                        }

                        if ("2".equals(pu.getType())) {   //1-正常,2-迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
                            lateNum = lateNum + 1;
                            lateUser.add(pu);
                        } else if ("3".equals(pu.getType())) {
                            leaveEarlyNum += 1;
                            leaveEarlyUser.add(pu);
                        } else if ("4".equals(pu.getType())) {
                            outsideNum += 1;
                            outsideUser.add(pu);
                        } else if ("5".equals(pu.getType())) {   //1-正常,2-迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
                            leaveNum += 1;
//                            List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetailList = this.getPersonnelAttendanceUserDetailByBusiness(pu.getId(), "5", null, null, null);
                            for (PersonnelAttendanceUserDetail pd:personnelAttendanceUserDetailList) {
                                if ("5".equals(pd.getType())) {
                                    pd.setLeaveTypeName(leaveService.getLeaveTypeName(pd.getLeaveType(), pd.getBusinessType()));
                                }
                            }
//                            pu.setPersonnelAttendanceUserDetailList(personnelAttendanceUserDetailList);
                            leaveUser.add(pu);

                            Integer subtractRealNumAbsenteeism = this.subtractRealNumLeaveOrAbsenteeism(pu);
                            if (subtractRealNumAbsenteeism != null && subtractRealNumAbsenteeism != 0 && realNum > 0) {
                                realNum = realNum - 1;   //从实际出勤人数中减去
                                realUser.remove(pu);
                            }
                        } else if ("6".equals(pu.getType())) {
                            travelNum += 1;
                            travelUser.add(pu);
                        } else if ("7".equals(pu.getType())) {     //某日仅录入了上班考勤时，如某员工被记为旷工，且开始时间与应上班时间相同的，该职工仅计入旷工人数，而不计入当日“实际出勤人数”；
                            // 开始时间晚于应上班时间的，则旷工人数与实际出勤人数都计入；
                            Integer subtractRealNumAbsenteeism = this.subtractRealNumLeaveOrAbsenteeism(pu);
                            if (subtractRealNumAbsenteeism != null && subtractRealNumAbsenteeism != 0 && realNum > 0) {
                                realNum = realNum - 1;   //从实际出勤人数中减去
                                realUser.remove(pu);
                            }
                            absenteeismUser.add(pu);
                            absenteeismNum += 1;
                        } else if ("9".equals(pu.getType())) {    //此中的各个状态是分开的，可能重复计算人数(查询的为考勤详情表)；以上的都是单个状态的
                            Integer subtractRealNum = 0; //是否已减去了实际出勤人数 0-没有

                            //1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他【某ri的】
                            AttendanceNumDto result = this.attendanceNumDay(pu.getUser(), NewDateUtils.today(pu.getAttendanceDate()));
                            Integer lateNumOne = result.getLateNum();  //2--迟到
                            if (lateNumOne != null && lateNumOne > 0) {
                                lateNum = lateNum + 1;
                                lateUser.add(pu);
                            }
                            Integer leaveEarlyNumOne = result.getLeaveEarlyNum();  //3-早退
                            if (leaveEarlyNumOne != null && leaveEarlyNumOne > 0) {
                                leaveEarlyNum += 1;
                                leaveEarlyUser.add(pu);
                            }
                            Integer outsideNumOne = result.getOutsideNum();  //4-外出
                            if (outsideNumOne != null && outsideNumOne > 0) {
                                outsideNum += 1;
                                outsideUser.add(pu);
                            }

                            Integer travelNumOne = result.getTravelNum();  //6-出差
                            if (travelNumOne != null && travelNumOne > 0) {
                                travelNum += 1;
                                travelUser.add(pu);
                            }
                            Integer leaveNumOne = result.getLeaveNum();  //5-请假
                            if (leaveNumOne != null && leaveNumOne > 0) {
                                leaveNum += 1;
//                                List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetailList = this.getPersonnelAttendanceUserDetailByBusiness(pu.getId(), "5", null, null, null);
                                for (PersonnelAttendanceUserDetail pd:personnelAttendanceUserDetailList) {
                                    if ("5".equals(pd.getType())) {
                                        pd.setLeaveTypeName(leaveService.getLeaveTypeName(pd.getLeaveType(), pd.getBusinessType()));
                                    }
                                }
//                                if (pu.getPersonnelAttendanceUserDetailList() != null && pu.getPersonnelAttendanceUserDetailList().size() > 0) {
//                                    personnelAttendanceUserDetailList.addAll(pu.getPersonnelAttendanceUserDetailList());
//                                    pu.setPersonnelAttendanceUserDetailList(personnelAttendanceUserDetailList);
//                                } else {
//                                    pu.setPersonnelAttendanceUserDetailList(personnelAttendanceUserDetailList);
//                                }
                                leaveUser.add(pu);
                                Integer subtractRealNumAbsenteeism = this.subtractRealNumLeaveOrAbsenteeism(pu);
                                if (subtractRealNumAbsenteeism != null && subtractRealNumAbsenteeism != 0 && realNum > 0) {
                                    realNum = realNum - 1;   //从实际出勤人数中减去
                                    realUser.remove(pu);
                                    subtractRealNum = 1;   //这里减去实际出勤人数，则下面的旷工则不用减
                                }
                            }

                            Integer absenteeismNumOne = result.getAbsenteeismNum();  //7-旷工
                            if (absenteeismNumOne != null && absenteeismNumOne > 0) {
                                absenteeismUser.add(pu);
                                absenteeismNum += 1;
                                if (subtractRealNum == 0) {   //上面的请假没有减去实际出勤人数，所以这里判断是否需要减去
                                    Integer subtractRealNumAbsenteeism = this.subtractRealNumLeaveOrAbsenteeism(pu);
                                    if (subtractRealNumAbsenteeism != null && subtractRealNumAbsenteeism != 0 && realNum > 0) {
                                        realNum = realNum - 1;   //从实际出勤人数中减去
                                        realUser.remove(pu);
                                    }
                                }
                            }
                        }

                        //加班不算总考勤状态，所以单独计加班
                        List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetailList1 = this.getPersonnelAttendanceUserDetailByUserId(pu.getUser(), null, currentTime, "8", "1");
                        if (personnelAttendanceUserDetailList1.size() > 0) {    //被批准的某日加班申请人数的和即为某日的加班人数，但如该加班者中有实际未加班或批准时长为零者，则自数据产生之时，该日的加班人数相应减少；
                            if (pu.getPersonnelAttendanceUserDetailList() != null && pu.getPersonnelAttendanceUserDetailList() != null) {   //是否上面的请假已存入了
                                List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetailList2 = new ArrayList<>();
                                for (PersonnelAttendanceUserDetail pd:personnelAttendanceUserDetailList1) {
                                    if (pd.getBusiness()!=null){
                                        PersonnelOvertime personnelOvertime = personnelOvertimeDao.get(pd.getBusiness());
                                        if (personnelOvertime!=null&&"4".equals(personnelOvertime.getApproveStatus())){ //申报加班的才显示
                                            personnelAttendanceUserDetailList2.add(pd);
                                        }
                                    }else {
                                        personnelAttendanceUserDetailList2.add(pd);  //修改的加班
                                    }
                                }
                                if (personnelAttendanceUserDetailList2.size()>0){
                                    personnelAttendanceUserDetailList2.addAll(pu.getPersonnelAttendanceUserDetailList());
                                    pu.setPersonnelAttendanceUserDetailList(personnelAttendanceUserDetailList2);
                                    overTimeNum += 1;
                                    overTimeUser.add(pu);
                                }
                            } else {
                                List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetailList2 = new ArrayList<>();
                                for (PersonnelAttendanceUserDetail pd:personnelAttendanceUserDetailList1) {
                                    if (pd.getBusiness()!=null){
                                        PersonnelOvertime personnelOvertime = personnelOvertimeDao.get(pd.getBusiness());
                                        if (personnelOvertime!=null&&"4".equals(personnelOvertime.getApproveStatus())){ //申报加班的才显示
                                            personnelAttendanceUserDetailList2.add(pd);
                                        }
                                    }else {
                                        personnelAttendanceUserDetailList2.add(pd);  //修改的加班
                                    }
                                }
                                if (personnelAttendanceUserDetailList2.size()>0){
                                    pu.setPersonnelAttendanceUserDetailList(personnelAttendanceUserDetailList2);
                                    overTimeNum += 1;
                                    overTimeUser.add(pu);
                                }
                            }

                        }
                    } else {
                        User user = userService.getUserByID(pu.getUser());
                        pu.setUserName(user.getUserName());
                        if (!MyStrings.nulltoempty(user.getImgPath()).isEmpty()) {
                            pu.setImgPath(user.getImgPath());  //职工图片路径
                        }
                        if (pu.getDept() != null && pu.getDept() != 0) {
                            Organization organization = orgService.getOrgByOid(pu.getDept(), OrgService.OrgType.department);
                            pu.setDeptName(organization.getName());
                        }

                        //加班
                        List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetailList = this.getPersonnelAttendanceUserDetailByUserId(pu.getUser(), null, currentTime, "8", "1");  //已把时长为0的加班除外
                        if (personnelAttendanceUserDetailList.size() > 0) {    //被批准的某日加班申请人数的和即为某日的加班人数，但如该加班者中有实际未加班或批准时长为零者，则自数据产生之时，该日的加班人数相应减少；

                            List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetailList2 = new ArrayList<>();
                            for (PersonnelAttendanceUserDetail pd:personnelAttendanceUserDetailList) {
                                if (pd.getBusiness()!=null){
                                    PersonnelOvertime personnelOvertime = personnelOvertimeDao.get(pd.getBusiness());
                                    if (personnelOvertime!=null&&"4".equals(personnelOvertime.getApproveStatus())){ //申报加班的才显示
                                        personnelAttendanceUserDetailList2.add(pd);
                                    }
                                }else {
                                    personnelAttendanceUserDetailList2.add(pd);  //修改的加班
                                }
                            }
                            if (personnelAttendanceUserDetailList2.size()>0){
                                pu.setPersonnelAttendanceUserDetailList(personnelAttendanceUserDetailList2);
                                overTimeNum += 1;
                                overTimeUser.add(pu);
                            }

//                            pu.setPersonnelAttendanceUserDetailList(personnelAttendanceUserDetailList);
//                            overTimeNum += 1;
//                            overTimeUser.add(pu);
                        }

                        //请假
                        List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetailList1 = workAttendanceService.getPersonnelAttendanceUserDetailByBusiness(pu.getId(), "5", null, null, null);
                        if (personnelAttendanceUserDetailList1.size() > 0) {
                            if (pu.getPersonnelAttendanceUserDetailList() != null && pu.getPersonnelAttendanceUserDetailList().size() > 0) {
                                personnelAttendanceUserDetailList1.addAll(pu.getPersonnelAttendanceUserDetailList());
                                pu.setPersonnelAttendanceUserDetailList(personnelAttendanceUserDetailList1);
                            } else {
                                pu.setPersonnelAttendanceUserDetailList(personnelAttendanceUserDetailList1);
                            }
                            for (PersonnelAttendanceUserDetail pd:pu.getPersonnelAttendanceUserDetailList()) {
                                if ("5".equals(pd.getType())) {
                                    pd.setLeaveTypeName(leaveService.getLeaveTypeName(pd.getLeaveType(), pd.getBusinessType()));
                                }
                            }
                            leaveNum += 1;
                            leaveUser.add(pu);
                        }
                    }
                }
            } else {   //当天为无需考勤的
                for (PersonnelAttendanceUser pu : userList) {
                    User user = userService.getUserByID(pu.getUser());
                    pu.setUserName(user.getUserName());
                    if (pu.getDept() != null && pu.getDept() != 0) {
                        Organization organization = orgService.getOrgByOid(pu.getDept(), OrgService.OrgType.department);
                        pu.setDeptName(organization.getName());
                    }
                    List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetailList = this.getPersonnelAttendanceUserDetailByUserId(pu.getUser(), null, currentTime, "8", "1");  //已把时长为0的加班除外
                    if (personnelAttendanceUserDetailList.size() > 0) {    //被批准的某日加班申请人数的和即为某日的加班人数，但如该加班者中有实际未加班或批准时长为零者，则自数据产生之时，该日的加班人数相应减少；
//                        pu.setPersonnelAttendanceUserDetailList(personnelAttendanceUserDetailList);
//                        overTimeNum += 1;
//                        overTimeUser.add(pu);
                        List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetailList2 = new ArrayList<>();
                        for (PersonnelAttendanceUserDetail pd:personnelAttendanceUserDetailList) {
                            if (pd.getBusiness()!=null){
                                PersonnelOvertime personnelOvertime = personnelOvertimeDao.get(pd.getBusiness());
                                if (personnelOvertime!=null&&"4".equals(personnelOvertime.getApproveStatus())){ //申报加班的才显示
                                    personnelAttendanceUserDetailList2.add(pd);
                                }
                            }else {
                                personnelAttendanceUserDetailList2.add(pd);  //修改的加班
                            }
                        }
                        if (personnelAttendanceUserDetailList2.size()>0){
                            pu.setPersonnelAttendanceUserDetailList(personnelAttendanceUserDetailList2);
                            overTimeNum += 1;
                            overTimeUser.add(pu);
                        }
                    }
                }
                userTotalNum = 0;  //应出勤人数
                userList.clear();  //参与考勤人员（应出勤人）
                notInAttendance = 0;  //不参与考勤人数
                notInAttendanceUser.clear();//不参与考勤人员
                realNum = 0;//实际出勤人数
                realUser.clear();//实际出勤人
            }
        }
        map.put("workOrRest", workOrRest); //当天是上班还是休息  1-假 2-班
        map.put("userTotalNum", userTotalNum);//应出勤人数
        map.put("userList", userList); //应出勤人员
        map.put("notInAttendance", notInAttendance);//不参与考勤人数
        map.put("notInAttendanceUser", notInAttendanceUser);//不参与考勤人员
        map.put("realNum", realNum);//实际出勤人数
        map.put("realUser", realUser);//实际出勤人
        map.put("lateNum", lateNum);//迟到人数
        map.put("lateUser", lateUser);//迟到人员
        map.put("leaveEarlyNum", leaveEarlyNum);//早退人数
        map.put("leaveEarlyUser", leaveEarlyUser);//早退人员
        map.put("outsideNum", outsideNum);//外出人数
        map.put("outsideUser", outsideUser);//外出人员
        map.put("leaveNum", leaveNum);//请假人数
        map.put("leaveUser", leaveUser);//请假人员
        map.put("travelNum", travelNum);//出差人数
        map.put("travelUser", travelUser);//出差人员
        map.put("absenteeismNum", absenteeismNum);//旷工人数
        map.put("absenteeismUser", absenteeismUser);//旷工人员
        map.put("overTimeNum", overTimeNum);//加班人数
        map.put("overTimeUser", overTimeUser);//加班人员
        return map;
    }
    private PersonnelAttendanceUser addDetailToUser(PersonnelAttendanceUser pu) {
        List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetailList = workAttendanceService.getPersonnelAttendanceUserDetailByBusiness(pu.getId(), "3", null, null, null);
        if (personnelAttendanceUserDetailList.size() > 0) {
            if (pu.getPersonnelAttendanceUserDetailList() != null && pu.getPersonnelAttendanceUserDetailList().size() > 0) {
                personnelAttendanceUserDetailList.addAll(pu.getPersonnelAttendanceUserDetailList());
                pu.setPersonnelAttendanceUserDetailList(personnelAttendanceUserDetailList);
            } else {
                pu.setPersonnelAttendanceUserDetailList(personnelAttendanceUserDetailList);
            }
        }
        return pu;
    }

    //根据请假/旷工判断，是否需要从实际出勤人数中减去
    private Integer subtractRealNumLeaveOrAbsenteeism(PersonnelAttendanceUser pu) {
        Integer subtractRealNumAbsenteeism = 0;
        if ("5".equals(pu.getBeginState()) && "0".equals(pu.getEndState())) {  //仅上班考勤录入请假
            subtractRealNumAbsenteeism = 1;
        } else if ("5".equals(pu.getBeginState()) && "5".equals(pu.getEndState())) {   //下班考勤也录入了（上下班都是请假）
            subtractRealNumAbsenteeism = 1;
        } else if ("5".equals(pu.getBeginState()) && "7".equals(pu.getEndState())) {   //下班考勤也录入了（上班请假，下班旷工）
            subtractRealNumAbsenteeism = 1;
        } else if ("7".equals(pu.getBeginState()) && "5".equals(pu.getEndState())) {   //下班考勤也录入了（下班请假，上班旷工）
            subtractRealNumAbsenteeism = 1;
        }
        if ("7".equals(pu.getBeginState()) && "0".equals(pu.getEndState())) {    //仅录入了上班考勤旷工
            subtractRealNumAbsenteeism = 1;  //从实际出勤人数中减去
        } else if ("7".equals(pu.getBeginState()) && "7".equals(pu.getEndState())) {  //录入了上下班考勤时（均是旷工）
            subtractRealNumAbsenteeism = 1;  //从实际出勤人数中减去
        }
        //需求旷工的也要具体的详情(类似请假的) 考勤打卡-20240905/lyx
//        List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetailList = this.getPersonnelAttendanceUserDetailByUserId(pu.getUser(), pu.getId(), null, "7", null);
//        if (personnelAttendanceUserDetailList.size()>0) {
//            if (pu.getPersonnelAttendanceUserDetailList() != null && pu.getPersonnelAttendanceUserDetailList().size() > 0) {
//                personnelAttendanceUserDetailList.addAll(pu.getPersonnelAttendanceUserDetailList());
//                pu.setPersonnelAttendanceUserDetailList(personnelAttendanceUserDetailList);
//            } else {
//                pu.setPersonnelAttendanceUserDetailList(personnelAttendanceUserDetailList);
//            }
//        }
//        List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetailList = this.getPersonnelAttendanceUserDetailByUserId(pu.getUser(), pu.getId(), null, "7", null);
//        if (personnelAttendanceUserDetailList.size() == 1) {   //只有一条旷工信息时，才显示具体的开始时间和结束时间
//            pu.setBeginTimeType(personnelAttendanceUserDetailList.get(0).getBeginTime());
//            pu.setEndTimeType(personnelAttendanceUserDetailList.get(0).getEndTime());
//        }
        return subtractRealNumAbsenteeism;
    }

    @Override
    public PersonnelAttendanceOrgMonitor getOrgMonitor(Integer oid) {
        String hql = "from PersonnelAttendanceOrgMonitor where org=:org";
        HashMap<String, Object> params = new HashMap<>(1);
        params.put("org", oid);
        PersonnelAttendanceOrgMonitor result = (PersonnelAttendanceOrgMonitor) personnelAttendanceOrgMonitorDao.getByHQLWithNamedParams(hql, params);
        if (result == null) {
            result = new PersonnelAttendanceOrgMonitor(oid, null, (byte) 0, null, NewDateUtils.getMinDate());
            personnelAttendanceOrgMonitorDao.save(result);
        }
        return result;
    }

    /**
     * 用户更换部门时调用，更新用户的考勤
     *
     * @param user      ChangeDeptReasons.Enroll为save后的对象，其他为更改前的user。
     * @param newDeptId 如果 resion==ChangeDeptReasons.Enroll or ChangeDeptReasons.ExchangDept 时，输入新部门id，ChangeDeptReasons.Resign，输入null。
     * @param reason    列子 workAttendanceService.ChangeDeptReasons.ExchangDept;
     */
    @Override
    public void changeUserDepartment(User user, String newDeptId, ChangeDeptReasons reason) {
        changeUserDepartment(user, newDeptId, reason, NewDateUtils.tomorrow());
    }

    /**
     * 用户更换部门时调用，更新用户的考勤
     *
     * @param user          ChangeDeptReasons.Enroll为save后的对象，其他为更改前的user。
     * @param newDeptId     如果 resion==ChangeDeptReasons.Enroll or ChangeDeptReasons.ExchangDept 时，输入新部门id，ChangeDeptReasons.Resign，输入null。
     * @param reason        列子 workAttendanceService.ChangeDeptReasons.ExchangDept;
     * @param effectiveDate 生效日期
     * <AUTHOR>
     * @since 2019/1/23 14:21
     */
    public void changeUserDepartment(User user, String newDeptId, ChangeDeptReasons reason, Date effectiveDate) {
        if (reason == null) {
            reason = ChangeDeptReasons.ExchangDept;
        }
        Integer oid = user.getOid();
        if (ChangeDeptReasons.ExchangDept == reason) {//只有变更部门需要先处理考勤
            setDefaultWorkAttendance(oid);//处理已有考勤，并生成PersonnelAttendanceOrgMonitor
        }
        PersonnelAttendanceOrgMonitor attendanceOrgMonitor = getOrgMonitor(oid);
        Date today = NewDateUtils.yesterday(effectiveDate);//设置日
        Date tomorrow = NewDateUtils.today(effectiveDate);//生效日
        if (attendanceOrgMonitor.getScanDate() != null//执行过第一天或者还没跑过setDefaultWorkAttendance扫描
                || workAttendanceService.getStartUsingSystemTime(oid) != null && NewDateUtils.today(workAttendanceService.getStartUsingSystemTime(oid)).getTime() <= today.getTime()) {//还没跑过setDefaultWorkAttendance但已经开始考勤
            String oldDeptId;
            if (ChangeDeptReasons.Enroll == reason) {
                oldDeptId = null;
            } else {
                try {
                    oldDeptId = Integer.valueOf(user.getDepartment()).toString();
                } catch (NumberFormatException e) {
                    oldDeptId = "0";
                }
            }
            if (ChangeDeptReasons.Resign == reason) {
                newDeptId = null;
            } else {
                try {
                    Integer.valueOf(newDeptId);
                } catch (NumberFormatException e) {
                    newDeptId = "0";
                }
            }
            //查看今天有没有改过。
            String hql = "from UserDepartmentHistory where user=:user and effdt=:effdt";
            HashMap<String, Object> params = new HashMap<>();
            params.put("user", user.getUserID());
            params.put("effdt", tomorrow);
            UserDepartmentHistory duplicate = (UserDepartmentHistory) userDepartmentHistoryDao.getByHQLWithNamedParams(hql, params);
            if (duplicate != null) {
                oldDeptId = duplicate.getOldDeptId();//oldDeptId改为今天第一次修改的原值。
                if (duplicate.getNewDeptId() == null) {//离职
                    return;//不再修改
                }
            }
            if (duplicate == null) {
                duplicate = new UserDepartmentHistory(oid, user.getUserID(), tomorrow, oldDeptId, newDeptId, reason.toString());
            } else {
                duplicate.setNewDeptId(newDeptId);
                duplicate.setReason(reason.toString());
            }
            userDepartmentHistoryDao.save(duplicate);
            String cacheName = "miners:Attendance:changeUserMonitor:" + oid;
            redisTemplate.opsForZSet().removeRangeByScore(cacheName, Double.MIN_VALUE, Double.MAX_VALUE);
        }
    }

//    @Override
//    public List<PersonnelAttendanceUserDto> getAttendanceList(Integer oid, Date today, Date yesterday, PageInfo pageInfo) {
//        HashMap<String, Object> params = new HashMap<>();
//        StringBuffer hql = new StringBuffer("select distinct new cn.sphd.miners.modules.generalAffairs.dto.PersonnelAttendanceUserDto( u.userID,u.userName,u.department,u.departName) from PersonnelAttendanceUser p, User u where p.user=u.userID and u.roleCode!='super'");
//        if (oid != null && !oid.equals(0)) {
//            hql.append(" and u.oid=:oid");
//            params.put("oid", oid);
//        }
//        if (today != null && yesterday != null) {
//            hql.append(" and (p.attendanceDate=:today or p.attendanceDate=:yesterday)");
//            params.put("today", today);
//            params.put("yesterday", yesterday);
//        }
//        hql.append(" order by convert(u.userName, 'gbk')");
//        List<PersonnelAttendanceUserDto> result = personnelAttendanceUserDao.getListByHQLWithNamedParams(hql.toString(), params, pageInfo);
//        if (result != null && result.size() > 0) {
//            List<Integer> uids = new ArrayList<>();
//            for (PersonnelAttendanceUserDto u : result) {
//                uids.add(u.getUserID());
//            }
//            hql.setLength(0);
//            params.clear();
//            hql.append("from PersonnelAttendanceUser where user in (:uids) and attendanceDate=:day");
//            params.put("uids", uids);
//            params.put("day", today);
//            List<PersonnelAttendanceUser> todays = personnelAttendanceUserDao.getListByHQLWithNamedParams(hql.toString(), params);
//            HashMap<Integer, PersonnelAttendanceUser> todaymap = new HashMap(todays.size());
//            for (PersonnelAttendanceUser u : todays) {
//                todaymap.put(u.getUser(), u);
//            }
//            params.put("day", yesterday);
//            List<PersonnelAttendanceUser> yesterdays = personnelAttendanceUserDao.getListByHQLWithNamedParams(hql.toString(), params);
//            HashMap<Integer, PersonnelAttendanceUser> yesterdaymap = new HashMap(todays.size());
//            for (PersonnelAttendanceUser u : yesterdays) {
//                yesterdaymap.put(u.getUser(), u);
//            }
//            for (PersonnelAttendanceUserDto rec : result) {
//                Date beginTime = getBeginTime(oid, Integer.valueOf(rec.getDeptId()), today);
//                PersonnelAttendanceUser u = todaymap.get(rec.getUserID());
//                if (u != null) {
//                    rec.setIdToday(u.getId());
//                    rec.setBeginStateToday(u.getBeginState());
//                    rec.setAmAttendanceToday(u.getAmAttendance());
//                }
//                u = yesterdaymap.get(rec.getUserID());
//                if (u != null) {
//                    rec.setIdYesterday(u.getId());
//                    rec.setBeginStateYesterday(u.getBeginState());
//                    rec.setEndStateYesterday(u.getEndState());
//                    rec.setAmAttendanceYesterday(u.getAmAttendance());
//                    rec.setPmAttendanceYesterday(u.getPmAttendance());
//                }
//                if (beginTime != null) {
//                    rec.setWorkBeginTime(NewDateUtils.dateToString(beginTime, "HH:mm"));
//                } else {
//                    rec.setWorkBeginTime("00:00");
//                }
//            }
//        }
//        return result;
//    }

    /**
     * 添加新增考勤人员，当月变更前为无需考勤
     *
     * @param oid
     * @param day
     */
    @Override
    public void setPersonelAttendanceNoNeed(Integer oid, Date day) {
        if (NewDateUtils.changeMonth(day, 0).getTime() == day.getTime()) {//每月1日无需修改
            return;
        }
        List<String> todayDetpList, yesterdayDetpList;
        PersonnelAttendanceConfig personnelAttendanceConfig = getOnePersonnelAttendanceConfigByOpenDate(oid, 1, day);
        if (personnelAttendanceConfig.getOpenDate().getTime() == day.getTime()) {//考勤生效日，同时处理部门包括个人
            todayDetpList = getDeptIds(day, oid);
            if (todayDetpList.isEmpty()) {//今天没有设置考勤
                return;
            }
            yesterdayDetpList = getDeptIds(NewDateUtils.yesterday(day), oid);
            Set<Integer> uids = new HashSet<>();
            List<UserDto> todayUserDtos = getUser(day, oid, todayDetpList);
            for (UserDto userDto : todayUserDtos) {
                uids.add(userDto.getUserID());
            }
            List<UserDto> yesterdayUserDtos = getUser(day, oid, yesterdayDetpList);
            for (UserDto userDto : yesterdayUserDtos) {
                uids.remove(userDto.getUserID());
            }
            for (Integer uid : uids) {
                personnelAttendanceMonthlyService.SetNewUserNoNeed(userService.getUserByID(uid), NewDateUtils.yesterday(day), null);
            }
//            //已经处理过了，个人部分直接设置isUsed即可。
//            List<UserDepartmentHistory> dailyUserMonitors = getDailyUserMonitor(day, oid);
//            for (UserDepartmentHistory userMonitor : dailyUserMonitors) {
//                userMonitor.setUsed(true);
//            }
        } else { //处理个人变更部门
            List<UserDepartmentHistory> dailyUserMonitors = getDailyUserMonitor(day, oid);
            if (!dailyUserMonitors.isEmpty()) {
                todayDetpList = getDeptIds(day, oid);
                if (todayDetpList.isEmpty()) {//今天没有设置考勤
                    return;
                }
                yesterdayDetpList = getDeptIds(NewDateUtils.yesterday(day), oid);
                for (UserDepartmentHistory userMonitor : dailyUserMonitors) {
                    User user;
                    if (!yesterdayDetpList.contains(userMonitor.getOldDeptId()) && todayDetpList.contains(userMonitor.getNewDeptId())
                            && (user=userService.getUserByID(userMonitor.getUser()))!=null) { //从无需考勤到需要考勤
                        personnelAttendanceMonthlyService.SetNewUserNoNeed(user, NewDateUtils.yesterday(day), null);
                    }
                    userMonitor.setUsed(true);
                }
            }
        }
    }

    @Override
    public void updateInputTime(Integer attendanceId, String inputTime, User user) {
        PersonnelAttendanceConfig personnelAttendanceConfig = getPersonnelAttendanceConfigById(attendanceId);
        if (personnelAttendanceConfig != null) {
//            Date effectDate = NewDateUtils.tomorrow();  //修改后次日生效时间
//            Date inputTimeDate = NewDateUtils.dateFromString(inputTime, "HH:mm");
            personnelAttendanceConfig.setEffectDate(NewDateUtils.tomorrow());//修改后次日生效时间
            personnelAttendanceConfig.setOldCreateTime(personnelAttendanceConfig.getInputTime());  //原来的考勤录入时间
            if (StringUtils.isNotEmpty(inputTime)) {
                personnelAttendanceConfig.setInputTime(NewDateUtils.dateFromString(inputTime, "HH:mm"));//每日考勤录入时间(如未按时录入,则为旷工)
            }
            personnelAttendanceConfig.setUpdateDate(new Date());
            personnelAttendanceConfig.setUpdateName(user.getUserName());
            personnelAttendanceConfig.setUpdator(user.getUserID());
//            personnelAttendanceConfigDao.update(personnelAttendanceConfig);
            updatePersonnelAttendanceConfig(personnelAttendanceConfig);
        }
    }

    @Override
    public List<PersonnelAttendanceDepartmentConfigHistory> getPerAttendanceDeptConfigHistory(Integer attendanceConfigHistoryId) {
        Map<String, Object> map = new HashMap<>();
        String hql = "from PersonnelAttendanceDepartmentConfigHistory where ruleId=:attendanceConfigHistoryId order by dept desc";
        map.put("attendanceConfigHistoryId", attendanceConfigHistoryId);
        return personnelAttendanceDepartmentConfigHistoryDao.getListByHQLWithNamedParams(hql, map);
    }

    @Override
    public List<PersonnelAttendanceDepartmentConfig> getAttendanceDeptConfigByConfigId(Integer attendanceConfigId) {
        Map<String, Object> map = new HashMap<>();
        String hql = "from PersonnelAttendanceDepartmentConfig where ruleId=:attendanceConfigId order by dept desc";
        map.put("attendanceConfigId", attendanceConfigId);
        return personnelAttendanceDepartmentConfigDao.getListByHQLWithNamedParams(hql, map);
    }

    private List<PersonnelAttendanceRecord> getPersonnelAttendanceRecord(Integer oid, Date effectDate) {
        Map<String, Object> map = new HashMap<>();
        String hql = "from PersonnelAttendanceRecord where org=:oid and effectDate=:effectDate";
        map.put("oid", oid);
        map.put("effectDate", effectDate);
        return personnelAttendanceRecordDao.getListByHQLWithNamedParams(hql, map);
    }

    @Override
    public void updateAttendanceSetting(String deleteAttendanceSetIds, List personnelAttendanceConfigList, User user, String effectDate,Integer attendancePattern,Short lateLimit,Short earlyLimit,Boolean leaveWork,Boolean patternType) throws ParseException {
        List<PersonnelAttendanceRecord> personnelAttendanceRecordLasts = getPersonnelAttendanceRecord(user.getOid(), NewDateUtils.today(new SimpleDateFormat("yyyy-MM-dd").parse(effectDate)));

        if (patternType==null){
            patternType = false;
        }
        PersonnelAttendanceRecord personnelAttendanceRecord = addPersonnelAttendanceRecord(user, effectDate, "4",patternType);  //考勤设置修改记录表
        if (!MyStrings.nulltoempty(deleteAttendanceSetIds).isEmpty()) {
            String[] deleteSetIds = deleteAttendanceSetIds.split(",");
            Byte operation = AttendanceConfigOperation.delete.getIndex();  //2-删除的
            for (String attendanceId : deleteSetIds) {
                if (!MyStrings.nulltoempty(attendanceId).isEmpty()) {
                    PersonnelAttendanceConfig personnelAttendanceConfig = getPersonnelAttendanceConfigById(Integer.parseInt(attendanceId));
                    //将要删除的考勤设置保存到历史表中
                    PersonnelAttendanceConfigHistory personnelAttendanceConfigHistory = workAttendanceOldService.pcToPH(personnelAttendanceConfig, personnelAttendanceConfig, user, personnelAttendanceRecord, operation);
//                    this.updatePersonnelAttendanceConfig(user,2,personnelAttendanceConfig,personnelAttendanceConfig, personnelAttendanceRecord.getId(), operation,null,null);
                    //添加的部门为修改或者添加考勤设置的公共部门
                    if (personnelAttendanceConfig.getPersonnelAttendanceDepartmentConfigHashSet() != null) {
                        for (PersonnelAttendanceDepartmentConfig pdc : personnelAttendanceConfig.getPersonnelAttendanceDepartmentConfigHashSet()) {
                            addPersonnelAttendanceDepartmentConfigHistory(user, personnelAttendanceConfigHistory, personnelAttendanceRecord, pdc.getDept());
                        }
                    }
                }
            }
        }

        Byte operation = AttendanceConfigOperation.create.getIndex();  //1-添加
        int id = 0;   //接收下面循环的id
        for (int i = 0; i < personnelAttendanceConfigList.size(); i++) {
            JSONObject jo = JSONObject.fromObject(personnelAttendanceConfigList.get(i));
            id = jo.getInt("id");
            Date inputTime = jo.get("inputTime")!=null?NewDateUtils.dateFromString(jo.getString("inputTime"), "HH:mm"):null;
//            Object inputTimeOb = jo.get("inputTime");
//            if (jo.get("inputTime")!=null) {
//                String inputTimeString = (String) inputTimeOb;
//                inputTime = NewDateUtils.dateFromString(inputTimeString, "HH:mm");
//            }
            Date beginTime = NewDateUtils.dateFromString(jo.getString("beginTime"), "HH:mm");
            Date endTime = NewDateUtils.dateFromString(jo.getString("endTime"), "HH:mm");
            Date breakBegin = NewDateUtils.dateFromString(jo.getString("breakBegin"), "HH:mm");
            Date breakEnd = NewDateUtils.dateFromString(jo.getString("breakEnd"), "HH:mm");
            String type = jo.getString("type");
            Boolean isBreak = jo.getBoolean("isBreak");
            Date openDate = NewDateUtils.dateFromString(effectDate, "yyyy-MM-dd");
            Byte attendancePatternByte = ConfigAttendancePattern.manualEntry.getIndex();
            if (attendancePattern!=null && attendancePattern==1){
                attendancePatternByte = ConfigAttendancePattern.attendanceTreasure.getIndex();
            }
            PersonnelAttendanceConfig personnelAttendanceConfig = new PersonnelAttendanceConfig();

            if (id != 0) {  //需要修改的考勤设置
                operation = AttendanceConfigOperation.modify.getIndex();  //3-修改
                PersonnelAttendanceConfig personnelAttendanceConfig1 = getPersonnelAttendanceConfigById(id);  //旧的考勤设置
                if (1==attendancePattern){  //1-考勤宝的
                    if (lateLimit==null){
                        lateLimit = personnelAttendanceConfig1.getLateLimit();
                    }
                    if (earlyLimit==null){
                        earlyLimit = personnelAttendanceConfig1.getEarlyLimit();
                    }
                    if (leaveWork==null){
                        leaveWork = personnelAttendanceConfig1.getLeaveWork();
                    }
                }
                personnelAttendanceConfig = this.addPersonnelAttendanceConfigByDetail(user, personnelAttendanceRecord, personnelAttendanceConfig, jo, effectDate,attendancePattern,lateLimit,earlyLimit,leaveWork); //将未到日期的考勤设置在本表中新增

//                PersonnelAttendanceConfigHistory personnelAttendanceConfigHistory = pcToPH(personnelAttendanceConfig, personnelAttendanceConfig1, user, personnelAttendanceRecord, "3");//将原来的修改记录保存到历史表中
                PersonnelAttendanceConfigHistory personnelAttendanceConfigHistory = workAttendanceOldService.pcToPH(personnelAttendanceConfig, personnelAttendanceConfig1, user, personnelAttendanceRecord, operation);//将原来的修改记录保存到历史表中
                pdcToPdch(user, personnelAttendanceConfig1, personnelAttendanceConfigHistory);//将原来的考勤部门添加到考勤部门历史表中
//                this.updatePersonnelAttendanceConfig(user,2,personnelAttendanceConfig1,personnelAttendanceConfig,personnelAttendanceRecord.getId(),operation,null,null);  //添加对应的历史记录
            } else {   //需要新增的考勤设置
                if (leaveWork==null){
                    leaveWork = Boolean.FALSE;
                }
                //添加的部门为修改或者添加考勤设置的公共部门
//                if (!"".equals(jo.getString("departmentIds")) && jo.getString("departmentIds") != null) {
//                    String[] deptIds = jo.getString("departmentIds").split(",");
//                    for (String deptId : deptIds) {
//                        workAttendanceOldService.addPersonnelAttendanceDepartmentConfig(deptId, user, personnelAttendanceConfig, null, 1);
//                    }
////                personnelAttendanceConfig.getPersonnelAttendanceDepartmentConfigHashSet();
////                System.out.println("personnelAttendanceConfig changed id="+personnelAttendanceConfig.getId()+" depts size="+personnelAttendanceConfig.getPersonnelAttendanceDepartmentConfigHashSet().size());
//                }
                String departmentIds = jo.getString("departmentIds");
//                personnelAttendanceConfig = this.addPersonnelAttendanceConfigByDetail(user, personnelAttendanceRecord, personnelAttendanceConfig, jo, effectDate,attendancePattern,lateLimit,earlyLimit,leaveWork); //将未到日期的考勤设置在本表中新增
                personnelAttendanceConfig = workAttendanceService.addPersonnelAttendanceConfig(user,inputTime,beginTime,endTime,openDate,type,isBreak,breakBegin,breakEnd,operation,attendancePatternByte,lateLimit,earlyLimit,leaveWork,personnelAttendanceRecord.getId(),departmentIds); //将未到日期的考勤设置在本表中新增

                //将新添加的考勤设置在历史表中新增一遍
                PersonnelAttendanceConfigHistory personnelAttendanceConfigHistory = workAttendanceOldService.pcToPH(personnelAttendanceConfig, personnelAttendanceConfig, user, personnelAttendanceRecord, operation);
//                this.updatePersonnelAttendanceConfig(user,2,personnelAttendanceConfig,personnelAttendanceConfig,personnelAttendanceRecord.getId(), operation,null,null);

                //添加的部门为修改或者添加考勤设置的公共部门
                if (!"".equals(jo.getString("departmentIds")) && jo.getString("departmentIds") != null) {
                    String[] deptIds = jo.getString("departmentIds").split(",");
                    for (String deptId : deptIds) {
                        addPersonnelAttendanceDepartmentConfigHistory(user, personnelAttendanceConfigHistory, personnelAttendanceRecord, Integer.parseInt(deptId));
                    }
                }
            }
        }
        //取消上一次未生效的考勤修改
//        PersonnelAttendanceRecord personnelAttendanceRecordLast = getPersonnelAttendanceRecord(user.getOid(),"4",new SimpleDateFormat());
        for (PersonnelAttendanceRecord personnelAttendanceRecordLast : personnelAttendanceRecordLasts) {
            //将上一次修改的考勤设置更新为失效
            personnelAttendanceRecordLast.setOperation("5");
            updatePersonnelAttendanceRecord(personnelAttendanceRecordLast);
            updatePersonnelAttendanceConfigByRecordId(personnelAttendanceRecordLast.getId());
            updatePersonnelAttendanceConfigHistoryByRecordId(personnelAttendanceRecordLast.getId());
        }
    }

    // source 来源 1-审批 2-录入
    @Override
    public Map<String, Object> getOverTimeDetail(Integer overTimeId, String source,Integer historyType) {
        Map<String, Object> map = new HashMap<>();
        if ("1".equals(source)) {
            PersonnelOvertime personnelOvertime = overtimeService.getPersonnelOvertimeById(overTimeId);
            User user = userService.getUserByID(personnelOvertime.getUser_());
            personnelOvertime.setUserName(user.getUserName());

            List<ApprovalProcess> approvalProcess1 = approvalProcessService.getApprovalProcessByBusiness(overTimeId, 2, null); //计划加班的审批流程
            List<ApprovalProcess> approvalProcess2 = approvalProcessService.getApprovalProcessByBusiness(overTimeId, 6, null);  //申报加班审批流程
            map.put("personnelOvertime", personnelOvertime);
            map.put("approvalProcess1", approvalProcess1);
            map.put("approvalProcess2", approvalProcess2);
        }else if (historyType!=null&&1==historyType && "2".equals(source) ){  //考勤修改的查看详情历史的请假
            PersonnelAttendanceUserDetailHistory personnelAttendanceUserDetailHistory = personnelAttendanceUserDetailHistoryDao.get(overTimeId);
            User user = userService.getUserByID(personnelAttendanceUserDetailHistory.getUser());
            personnelAttendanceUserDetailHistory.setUserName(user.getUserName());
            map.put("personnelAttendanceUserDetail", personnelAttendanceUserDetailHistory);
        } else if ("2".equals(source)) {
            PersonnelAttendanceUserDetail personnelAttendanceUserDetail = personnelAttendanceUserDetailDao.get(overTimeId);
            User user = userService.getUserByID(personnelAttendanceUserDetail.getUser());
            personnelAttendanceUserDetail.setUserName(user.getUserName());
            map.put("personnelAttendanceUserDetail", personnelAttendanceUserDetail);
        }
        return map;
    }

    @Override
    public Map<String, Object> getLeaveDetail(Integer leaveId, String source,Integer historyType) {
        Map<String, Object> map = new HashMap<>();
        if ("1".equals(source)) {   //审批
            PersonnelLeave personnelLeave = personnelLeaveDao.get(leaveId);
            User user = userService.getUserByID(personnelLeave.getUser().getUserID());
            personnelLeave.setUserName(user.getUserName());
            personnelLeave.setLeaveTypeName(leaveService.getLeaveTypeName(personnelLeave.getLeaveType(),personnelLeave.getType()));  //处理请假类型名称
            List<ApprovalProcess> processList1 = approvalProcessService.getApprovalProcessByBusiness(personnelLeave.getId(), 3, null);
            if (!MyStrings.nulltoempty(personnelLeave.getActualState()).isEmpty() && "1".equals(personnelLeave.getActualState())) {
                List<PersonnelLeaveItem> personnelLeaveItems = leaveService.getPersonnelLeaveItemByleaveId(leaveId, "4");
                for (PersonnelLeaveItem pp : personnelLeaveItems) {
                    List<ApprovalProcess> processList2 = approvalProcessService.getApprovalProcessByLeaveItem(pp.getId(), 8);  //不取待处理的审批流程
                    pp.setProcessList(processList2);
                }
                map.put("personnelLeaveItems", personnelLeaveItems);  //提前请假申请详情
            }
            map.put("personnelLeave", personnelLeave); //计划请假申请详情
            map.put("processList1", processList1);  //计划请假审批流程
        }else if (historyType!=null&&1==historyType && "2".equals(source) ){  //考勤修改的查看详情历史的请假
            PersonnelAttendanceUserDetailHistory personnelAttendanceUserDetailHistory = personnelAttendanceUserDetailHistoryDao.get(leaveId);
            User user = userService.getUserByID(personnelAttendanceUserDetailHistory.getUser());
            personnelAttendanceUserDetailHistory.setUserName(user.getUserName());
            personnelAttendanceUserDetailHistory.setLeaveTypeName(leaveService.getLeaveTypeName(personnelAttendanceUserDetailHistory.getLeaveType(),personnelAttendanceUserDetailHistory.getBusinessType()));  //处理请假类型名称
            map.put("personnelAttendanceUserDetail", personnelAttendanceUserDetailHistory);
        }else if ("2".equals(source) ) {  //录入
            PersonnelAttendanceUserDetail personnelAttendanceUserDetail = personnelAttendanceUserDetailDao.get(leaveId);
            personnelAttendanceUserDetail.setLeaveTypeName(leaveService.getLeaveTypeName(personnelAttendanceUserDetail.getLeaveType(),personnelAttendanceUserDetail.getBusinessType()));  //处理请假类型名称
            User user = userService.getUserByID(personnelAttendanceUserDetail.getUser());
            personnelAttendanceUserDetail.setUserName(user.getUserName());
            map.put("personnelAttendanceUserDetail", personnelAttendanceUserDetail);
        }
        return map;
    }

    /**
     * 查某职工某天的加班详细情况【月查询中，点击加班总时长时，使用的接口】
     *
     * @param userId         职工id
     * @param attendanceDate 考勤日期
     * @param type           类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班，9-其他
     * @return
     * @throws ParseException
     */
    @Override
    public Map<String, Object> getAttendanceDayDetail(Integer userId, String attendanceDate, String type) throws ParseException {
        Map<String, Object> map = new HashMap<>();
        List<Map<String, Object>> approvalOverTime = new ArrayList<>();  //审批进来的加班
        List<PersonnelAttendanceUserDetail> entryOverTime = new ArrayList<>();  //手动录入进来的加班
//        SimpleDateFormat sdff = new SimpleDateFormat("yyyy-MM-dd");
        if (userId != null) {
//            List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetails = this.getPersonnelAttendanceUserDetailByUserId(userId,null,sdff.parse(attendanceDate),type,null);
            List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetails = this.getPersonnelAttendanceUserDetailByUserId(userId, null, NewDateUtils.dateFromString(attendanceDate, "yyyy-MM-dd"), type, null);
            for (PersonnelAttendanceUserDetail p : personnelAttendanceUserDetails) {
                Map<String, Object> map1 = new HashMap<>();
                if (p.getBusiness() != null && p.getBusiness() != 0) {
                    PersonnelOvertime personnelOvertime = overtimeService.getPersonnelOvertimeById(p.getBusiness());
                    List<ApprovalProcess> approvalProcess1 = approvalProcessService.getApprovalProcessByBusiness(p.getBusiness(), 2, null); //计划加班的审批流程
                    List<ApprovalProcess> approvalProcess2 = approvalProcessService.getApprovalProcessByBusiness(p.getBusiness(), 6, null);  //申报加班审批流程
                    map1.put("personnelOvertime", personnelOvertime);
                    map1.put("approvalProcess1", approvalProcess1);
                    map1.put("approvalProcess2", approvalProcess2);
                    approvalOverTime.add(map1);
                } else {
                    entryOverTime.add(p);
                }
            }
        }
        map.put("approvalOverTime", approvalOverTime);
        map.put("entryOverTime", entryOverTime);
        return map;
    }

//    /**
//     * <AUTHOR>
//     * @date 2018/5/10 16:27
//     * 查询当前的规则使用的所有部门
//     * oid(机构id) 、 type(1-正常班 2-倒班)
//     */
//    @Override
//    public List<PersonnelAttendanceConfig> attendanceDept(Date openDate, Integer oid, String type) {
//        List<PersonnelAttendanceConfig> personnelAttendanceConfigs = workAttendanceService.getPersonnelAttendanceConfigList(oid, type, openDate);
//        for (PersonnelAttendanceConfig ppp : personnelAttendanceConfigs) {
//            ppp.setBeginTimeString(NewDateUtils.dateToString(ppp.getBeginTime(), "HH:mm"));
//            ppp.setEndTimeString(NewDateUtils.dateToString(ppp.getEndTime(), "HH:mm"));
//            ppp.setBreakBeginString(NewDateUtils.dateToString(ppp.getBreakBegin(), "HH:mm"));
//            ppp.setBreakEndString(NewDateUtils.dateToString(ppp.getBreakEnd(), "HH:mm"));
//            if (ppp.getInputTime()!=null) {
//                ppp.setInputTimeString(NewDateUtils.dateToString(ppp.getInputTime(), "HH:mm"));
//            }
//            //1.342字段已改名
//            //ppp.setMiddleBreak(ppp.isBreak());
//            String departmentIds = "";
//            Set<PersonnelAttendanceDepartmentConfig> personnelAttendanceDepartmentConfigs = ppp.getPersonnelAttendanceDepartmentConfigHashSet();
//            for (PersonnelAttendanceDepartmentConfig p : personnelAttendanceDepartmentConfigs) {
//                if (p.getDept() != null && p.getDept() == 0) {
//                    p.setDeptName("其他");
//                } else if (p.getDept() != null) {
//                    Organization org = orgService.getOrgByOid(p.getDept(), OrgService.OrgType.department);
//                    if (org != null) {
//                        p.setDeptName(org.getName());
//                    }
//                }
//                departmentIds = StringUtils.isNotEmpty(departmentIds)?departmentIds+","+p.getDept():p.getDept().toString();
//            }
//            ppp.setDepartmentIds(departmentIds);
//
//        }
//        return personnelAttendanceConfigs;
//    }

//    @Override
//    public Map<String, Object> getTimeTable(Integer oid,String timeTable) {
//        Map<String, Object> map = new HashMap<>();
//        Date today = NewDateUtils.today();
//        Date tableDate;
//        if (StringUtils.isNotEmpty(timeTable)){
//            tableDate = NewDateUtils.dateFromString(timeTable,"yyyy-MM-dd");
//        }else {
//            tableDate = today;
//        }
//        Date beginTime = NewDateUtils.changeMonth(tableDate, 0);  //获取某月第一天
//        Date endTime = NewDateUtils.getLastTimeOfMonth(beginTime);//获取某月最后一天
////        Date nextBeginTime = NewDateUtils.changeMonth(today, 1);  //获取下个月第一天
////        Date nextEndTime = NewDateUtils.getLastTimeOfMonth(nextBeginTime);//获取下个月最后一天
//        List<PersonnelAttendanceException> personnelAttendanceExceptions = this.getPersonnelAttendanceExceptionByMonth(oid, beginTime, endTime);
////        List<PersonnelAttendanceException> nextPersonnelAttendanceExceptions = this.getPersonnelAttendanceExceptionByMonth(oid, nextBeginTime, nextEndTime);
//        map.put("thisMonth", getYearAndMonth(beginTime));  //获取某月的年和月
//        map.put("personnelAttendanceExceptions", personnelAttendanceExceptions);
////        map.put("nextMonth", DateUtils.getYearAndMonth(nextBeginTime));  //获取下月的年和月
////        map.put("nextPersonnelAttendanceExceptions", nextPersonnelAttendanceExceptions);
//        map.put("today", NewDateUtils.dateToString(today, "yyyy-MM-dd"));  //今天日期
//        return map;
//    }

    /**
     *<AUTHOR>
     *@date 2018/5/8 10:04
     *获取年和月（返回 xxxx年x月）
     */
    public static String getYearAndMonth(Date d){
        long yearMonth = NewDateUtils.getYearMonth(d);
        return (yearMonth/100)+"年"+(yearMonth%100)+"月";
    }

//    /**
//     * @param ttType   1-某天的考勤情况  2-某月的考勤情况
//     * @param type     1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他（请假类型）
//     * @param beginDay 考勤日
//     * @param userId   职工id
//     * @return
//     */
//    @Override
//    public Map<String, Object> getAttendanceMonthOrDay(Integer ttType, String type, Date beginDay, Integer userId) {
//        Map<String, Object> map = new HashMap<>();
//        if (ttType != null && userId != null && beginDay != null) {
//            User user = userService.getUserByID(userId);
//            if (ttType == 1) {  //1-某天的考勤情况
//                map = this.getAttendanceDay(beginDay, user);
//            } else {  //2-某月的考勤情况
//                map = this.getAttendanceMonth(beginDay, userId, type);
//            }
//            map.put("userName", user.getUserName());  //职工名称
//        }
//        return map;
//    }

//    private Map<String, Object> getAttendanceDay(Date beginDay, User user) {
//        Map<String, Object> map = new HashMap<>();
//        PersonnelAttendanceUser personnelAttendanceUser = workAttendanceService.getPersonnelAttendanceUserByUserIdAndId(null, user.getUserID(), beginDay, null);
//        if (personnelAttendanceUser != null) {
//            List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetailsNew = new ArrayList<>();
//            List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetails = getPersonnelAttendanceUserDetailByType(personnelAttendanceUser.getId(), beginDay, "4");
//            for (PersonnelAttendanceUserDetail pd:personnelAttendanceUserDetails) {
//                if ("5".equals(pd.getType())){
//                    pd.setLeaveTypeName(leaveService.getLeaveTypeName(pd.getLeaveType(),pd.getBusinessType()));
//                    personnelAttendanceUserDetailsNew.add(pd);
//                }else if ("8".equals(pd.getType())){
//                    if (pd.getBusiness()!=null){
//                        PersonnelOvertime personnelOvertime = personnelOvertimeDao.get(pd.getBusiness());
//                        if (personnelOvertime!=null&&"4".equals(personnelOvertime.getApproveStatus())){ //申报加班的才显示
//                            personnelAttendanceUserDetailsNew.add(pd);
//                        }
//                    }else {
//                        personnelAttendanceUserDetailsNew.add(pd);
//                    }
//                }else {
//                    personnelAttendanceUserDetailsNew.add(pd);
//                }
//            }
//            map.put("personnelAttendanceUserDetail", personnelAttendanceUserDetailsNew);  //职工考勤明细列表
//        }
//        map.put("personnelAttendanceUser1", personnelAttendanceUser);  //职工考勤类型信息列表
//
//        Integer deptId = 0;
//        if (!"".equals(user.getDepartment()) && user.getDepartment() != null) {
//            deptId = Integer.parseInt(user.getDepartment());
//        }
//        Date systemTime = workAttendanceService.getStartUsingSystemTime(user.getOid());  //获取系统时间
//        if (systemTime != null) {   //返回的每日考勤的开始时间、结束时间、每日考勤录入时间
//            if (NewDateUtils.today(beginDay).getTime() >= systemTime.getTime()) {
//                map = returnTime(user.getOid(), deptId, NewDateUtils.today(beginDay), map, null);
//                String beginDate = (String) map.get("beginTime");
//                if (MyStrings.nulltoempty(beginDate).isEmpty()) {
//                    returnTime(user.getOid(), null, systemTime, map, null);
//                }
//            } else {
//                returnTime(user.getOid(), deptId, systemTime, map, null);
//            }
//        }
//        return map;
//    }

//    private Map<String, Object> getAttendanceMonth(Date beginDay, Integer userId, String type) {
//        Map<String, Object> map = new HashMap<>();
//        Date begin = NewDateUtils.changeMonth(beginDay, 0);  //获取某月第一天
//        Date end = NewDateUtils.today();  //若当月则取昨天，当天的有统计不显示的话有问题
//        Date nowMonth = NewDateUtils.changeMonth(new Date(), 0);   //当前月的第一天
//        Date beginMonth = NewDateUtils.changeMonth(beginDay, 0);   //传值来的月的第一天
//        if (nowMonth.compareTo(beginMonth) != 0) {
//            end = NewDateUtils.getLastTimeOfMonth(beginDay);   //获取某月最后一天
//        }
//        if (!MyStrings.nulltoempty(type).isEmpty()) {
//            if (!"1".equals(type)) {   //5-请假,7-旷工,8-加班
//                List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetailsNew = new ArrayList<>();
//                List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetails = getPersonnelAttendanceUserDetailByMonth(userId, null, begin, end, type);
//                for (PersonnelAttendanceUserDetail pd:personnelAttendanceUserDetails) {
//                    if ("5".equals(pd.getType())){
//                        pd.setLeaveTypeName(leaveService.getLeaveTypeName(pd.getLeaveType(),pd.getBusinessType()));
//                        personnelAttendanceUserDetailsNew.add(pd);
//                    }else if ("8".equals(pd.getType())){
//                        if (pd.getBusiness()!=null){
//                            PersonnelOvertime personnelOvertime = personnelOvertimeDao.get(pd.getBusiness());
//                            if (personnelOvertime!=null&&"4".equals(personnelOvertime.getApproveStatus())){ //申报加班的才显示
//                                personnelAttendanceUserDetailsNew.add(pd);
//                            }
//                        }else {
//                            personnelAttendanceUserDetailsNew.add(pd);
//                        }
//                    }else {
//                        personnelAttendanceUserDetailsNew.add(pd);
//                    }
//                }
//                map.put("listMap", personnelAttendanceUserDetailsNew);
//            }
//        }
//        return map;
//    }

    @Override
    public void updateDeptName(Integer deptId, String departmentName) {
        Map<String, Object> map = new HashMap<>();
        String hql = "from PersonnelAttendanceMonthly where department=:department";
        map.put("department", deptId);
        List<PersonnelAttendanceMonthly> personnelAttendanceMonthlies = personnelAttendanceMonthlyDao.getListByHQLWithNamedParams(hql, map);
        for (PersonnelAttendanceMonthly p : personnelAttendanceMonthlies) {
            p.setDepartmentName(departmentName);
            personnelAttendanceMonthlyDao.update(p);
        }
    }

    @Override   //获取系统考勤时间，以及某天在考勤系统中是休息日还是上班日(刘洪涛用)
    public Map<String, Object> getDateAndWork(Integer oid, Date attendanceDate) {
        Map<String, Object> map = new HashMap<>();
        Date systemTime = workAttendanceService.getStartUsingSystemTime(oid);  //获取系统考勤开始时间
        map.put("systemTime", systemTime);
        if (attendanceDate != null) {
            PersonnelAttendanceException personnelAttendanceException = workAttendanceService.getPersonnelAttendanceExceptionByExceptionDate(oid, NewDateUtils.today(attendanceDate));
            if (personnelAttendanceException != null) {
                map.put("type", personnelAttendanceException.getType());
            } else {
                map.put("type", 0);
            }
        } else {
            map.put("type", 0);
        }
        return map;
    }

    /**
     * 获取考勤系统里的加班或请假   刘洪涛-1.145我的工作记录项目使用
     *
     * @param userId         职工id
     * @param attendanceDate 考勤日期
     * @param type           类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班，9-其他
     * @return
     */
    @Override
    public Map<String, Object> getLeaveOrOverTime(Integer userId, Date attendanceDate, String type) {
        Map<String, Object> map = new HashMap<>();
        List<Map<String, Object>> approvalData = new ArrayList<>();  //审批进来的加班
        List<PersonnelAttendanceUserDetail> entryData = new ArrayList<>();  //手动录入进来的加班
        Integer status = 0;  //0-没有加班也没有请假  1-有加班或有请假或者两者均有
        List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetails = this.getPersonnelAttendanceUserDetailByUserId(userId, null, attendanceDate, type, null);
        for (PersonnelAttendanceUserDetail p : personnelAttendanceUserDetails) {
            Map<String, Object> map1 = new HashMap<>();
            if (p.getBusiness() != null && p.getBusiness() != 0) {
                if ("8".equals(type)) {
                    PersonnelOvertime personnelOvertime = overtimeService.getPersonnelOvertimeById(p.getBusiness());
                    List<ApprovalProcess> approvalProcess1 = approvalProcessService.getApprovalProcessByBusiness(p.getBusiness(), 2, null); //计划加班的审批流程
                    List<ApprovalProcess> approvalProcess2 = approvalProcessService.getApprovalProcessByBusiness(p.getBusiness(), 6, null);  //申报加班审批流程
                    map1.put("personnelOvertime", personnelOvertime);
                    map1.put("approvalProcess1", approvalProcess1);
                    map1.put("approvalProcess2", approvalProcess2);
                } else if ("5".equals(type)) {
                    PersonnelLeave personnelLeave = personnelLeaveDao.get(p.getBusiness());
                    User user = userService.getUserByID(personnelLeave.getUser().getUserID());
                    personnelLeave.setUserName(user.getUserName());
                    personnelLeave.setLeaveTypeName(leaveService.getLeaveTypeName(personnelLeave.getLeaveType(),personnelLeave.getType()));  //处理请假类型名称
                    List<ApprovalProcess> processList1 = approvalProcessService.getApprovalProcessByBusiness(personnelLeave.getId(), 3, null);
                    if (!MyStrings.nulltoempty(personnelLeave.getActualState()).isEmpty() && "1".equals(personnelLeave.getActualState())) {
                        List<PersonnelLeaveItem> personnelLeaveItems = leaveService.getPersonnelLeaveItemByleaveId(p.getBusiness(), "4");
                        for (PersonnelLeaveItem pp : personnelLeaveItems) {
                            List<ApprovalProcess> processList2 = approvalProcessService.getApprovalProcessByLeaveItem(pp.getId(), 8);  //不取待处理的审批流程
                            pp.setProcessList(processList2);
                        }
                        map1.put("personnelLeaveItems", personnelLeaveItems);  //提前请假申请详情
                    }
                    map1.put("personnelLeave", personnelLeave); //计划请假申请详情
                    map1.put("processList1", processList1);  //计划请假审批流程
                }
                approvalData.add(map1);
            } else {
                entryData.add(p);
            }
            status = 1;
        }
        if (personnelAttendanceUserDetails.size() <= 0) {  //在此人参加考勤的基础上，可能是某天的考勤还未生成，直接查审批数据
            List<PersonnelLeave> personnelLeaves = leaveService.getPersonnelLeaveListByTime(userId, attendanceDate, NewDateUtils.getLastTimeOfDay(attendanceDate));
            List<PersonnelOvertime> personnelOvertimes = overtimeService.getPersonnelOvertimeListByTime(userId, 4, NewDateUtils.today(attendanceDate), NewDateUtils.getLastTimeOfDay(attendanceDate));
            if (personnelLeaves.size() > 0 || personnelOvertimes.size() > 0) {
                if ("5".equals(type)) {
                    for (PersonnelLeave personnelLeave : personnelLeaves) {  //请假的
                        Map<String, Object> map2 = new HashMap<>();
                        User user = userService.getUserByID(personnelLeave.getUser().getUserID());
                        personnelLeave.setUserName(user.getUserName());
                        personnelLeave.setLeaveTypeName(leaveService.getLeaveTypeName(personnelLeave.getLeaveType(),personnelLeave.getType()));  //处理请假类型名称
                        List<ApprovalProcess> processList1 = approvalProcessService.getApprovalProcessByBusiness(personnelLeave.getId(), 3, null);
                        if (!MyStrings.nulltoempty(personnelLeave.getActualState()).isEmpty() && "1".equals(personnelLeave.getActualState())) {
                            List<PersonnelLeaveItem> personnelLeaveItems = leaveService.getPersonnelLeaveItemByleaveId(personnelLeave.getId(), "4");
                            for (PersonnelLeaveItem pp : personnelLeaveItems) {
                                List<ApprovalProcess> processList2 = approvalProcessService.getApprovalProcessByLeaveItem(pp.getId(), 8);  //不取待处理的审批流程
                                pp.setProcessList(processList2);
                            }
                            map2.put("personnelLeaveItems", personnelLeaveItems);  //提前请假申请详情
                        }
                        map2.put("personnelLeave", personnelLeave); //计划请假申请详情
                        map2.put("processList1", processList1);  //计划请假审批流程
                        approvalData.add(map2);
                    }
                }

                if ("8".equals(type)) {
                    for (PersonnelOvertime personnelOvertime : personnelOvertimes) {  //加班
                        Map<String, Object> map3 = new HashMap<>();
                        List<ApprovalProcess> approvalProcess1 = approvalProcessService.getApprovalProcessByBusiness(personnelOvertime.getId(), 2, null); //计划加班的审批流程
                        List<ApprovalProcess> approvalProcess2 = approvalProcessService.getApprovalProcessByBusiness(personnelOvertime.getId(), 6, null);  //申报加班审批流程
                        map3.put("personnelOvertime", personnelOvertime);
                        map3.put("approvalProcess1", approvalProcess1);
                        map3.put("approvalProcess2", approvalProcess2);
                        approvalData.add(map3);
                    }
                }
                status = 1;
            }
        }
        map.put("approvalData", approvalData);  //系统审批进来的请假或加班
        map.put("entryData", entryData);   //考勤系统录入进来的请假或加班
        map.put("status", status);   //0-没有加班也没有请假  1-有加班或有请假或者两者均有
        return map;
    }

    @Override
    public Integer getBadgeNumber(User user, String code) {
        Integer number = null;
        switch (code) {
            case "attendanceOfficerSubmitEdit"://我的审批-考勤员提交的考勤修改
                number = approvalProcessService.getApprovalProcessCounts(user.getUserID(),54);
                break;
            case "attendanceEditApply"://我的申请-职工考勤修改
                break;
        }
        return number;
    }

    @Override
    public Map<String, Object> applyAttendance(User user,Integer noNeedType,String attendanceAll,String absenteeismAll,String leaveListAll,String overListAll) {
        Map<String, Object> map = new HashMap<>();
        if (!MyStrings.nulltoempty(attendanceAll).isEmpty()) {
            JSONObject attendanceJson = JSONObject.fromObject(attendanceAll);
            Integer attendanceId = attendanceJson.getInt("attendanceId");//考勤id【无需考勤的修改：若是无需考勤的第一次修改，那么此值传0；若是无需考勤第二次或者是大于2次修改，那么此值传对应的考勤id】
            String attendanceDate = attendanceJson.getString("attendanceDate");  //考勤日期
            Integer attendanceUserId = attendanceJson.getInt("attendanceUserId");//考勤人员
            String upIsNormal = attendanceJson.getString("upIsNormal");// 上班考勤 1-正常，0-迟到
            String isAbsenteeism = attendanceJson.getString("isAbsenteeism"); //1-旷工， 0-不旷工
            String isLeave = attendanceJson.getString("isLeave");// 1- 请假 0-没请假
            String downIsNormal = attendanceJson.getString("downIsNormal");//下班考勤 1-正常，0-早退
            String isOverTime = attendanceJson.getString("isOverTime");// 1-加班 0-没加班
            String updateDesc = attendanceJson.getString("updateDesc");// 修改理由
            Date now = new Date(System.currentTimeMillis());

            List<PersonnelAttendanceUserHistory> phPrevious = new ArrayList<>();
            if (0==noNeedType || (1==noNeedType&&attendanceId!=0)) {
                phPrevious = this.getPersonnelAttendanceUserHistoryList(user.getOid(), attendanceUserId,attendanceId, "1",null,null,36);//上一次修改未审批完的
            }else if (1==noNeedType){
                phPrevious = this.getPersonnelAttendanceUserHistoryList(user.getOid(), attendanceUserId,null, "1",NewDateUtils.today(NewDateUtils.dateFromString(attendanceDate,"yyyy-MM-dd")),user.getUserID(),36);//上一次修改未审批完的
            }

            if (phPrevious.size() > 0) {
                map.put("status", 2);
                map.put("content", "本条数据的修改还未审批");
            } else {
                Integer previousId = 0;  //上一次修改的id
                Integer versionNo = 1;
                PersonnelAttendanceUser oldAttendanceUser = new PersonnelAttendanceUser();
                if (attendanceId != 0) {
                    oldAttendanceUser = personnelAttendanceUserDao.get(attendanceId);//旧的考勤
                }
                List<PersonnelAttendanceUserHistory> phPrevious1 = this.getPersonnelAttendanceUserHistoryList(null,attendanceUserId, attendanceId, "2",NewDateUtils.today(NewDateUtils.dateFromString(attendanceDate,"yyyy-MM-dd")),user.getCreator(),36);//是否有上一次修改的记录（如果没有就把现在的考勤user添加上）
                if (phPrevious1.size() == 0 && oldAttendanceUser.getId() != null) {
                    //将PersonnelAttendanceUser中的原值添加到PersonnelAttendanceUser历史表（PersonnelAttendanceUserHistory）中
                    PersonnelAttendanceUserHistory ph1 = new PersonnelAttendanceUserHistory();//存储改前考勤信息
                    BeanUtils.copyPropertiesIgnoreNull(oldAttendanceUser, ph1);   //将p与ph中一样的值从p复制到ph中，不一样的值，另外set
                    ph1.setId(null);
                    ph1.setPersonnelAttendanceUser(oldAttendanceUser);
                    ph1.setCreateDate(now);
                    ph1.setApproveStatus("2");
                    ph1.setPreviousId(0);
                    ph1.setVersionNo(0); //初始的
                    ph1.setAttendanceId(attendanceId);
                    personnelAttendanceUserHistoryDao.save(ph1);

                    List<PersonnelAttendanceUserDetail> attendanceUserDetails = this.getPersonnelAttendanceUserDetailById(oldAttendanceUser.getId());
                    for (PersonnelAttendanceUserDetail pd : attendanceUserDetails) {
                        PersonnelAttendanceUserDetailHistory pdh = new PersonnelAttendanceUserDetailHistory();
                        BeanUtils.copyPropertiesIgnoreNull(pd, pdh);  //将pd与pdh中一样的值从p复制到ph中，不一样的值，另外set
                        pdh.setId(null);
                        pdh.setAttendance(ph1.getId());
                        personnelAttendanceUserDetailHistoryDao.save(pdh);
                    }
                    phPrevious1.add(ph1);
                }
                if (phPrevious1.size() > 0) {
                    previousId = phPrevious1.get(phPrevious1.size() - 1).getId();
                    versionNo = phPrevious1.get(phPrevious1.size() - 1).getVersionNo() + 1;
                }

                //将修改的值添加到历史数据中
                PersonnelAttendanceUserHistory ph = new PersonnelAttendanceUserHistory();//存储新的考勤信息

                if (oldAttendanceUser.getId()!=null){   //如果是无需考勤第一次修改，那么此值是空的
                    ph.setAttendanceId(attendanceId);
                    ph.setPersonnelAttendanceUser(oldAttendanceUser);
                }
                ph.setAttendanceDate(NewDateUtils.dateFromString(attendanceDate, "yyyy-MM-dd"));
                if (noNeedType == 1) {   //无需考勤的
                    ph.setType("10");//类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班，9-其他 10-由无需考勤改到考勤【若是10，则不展示改前信息，找本表查具体的类型】
                } else if (upIsNormal.equals("1") && downIsNormal.equals("1")) {
                    ph.setType("1");  //类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
                    if (isAbsenteeism.equals("1") && isLeave.equals("1")) {
                        ph.setType("9");
                    } else {
                        if (isAbsenteeism.equals("1")) {
                            ph.setType("7");
                        }
                        if (isLeave.equals("1")) {
                            if (!"1".equals(ph.getType())) {
                                ph.setType("9");
                            } else {
                                ph.setType("5");
                            }
                        }
                    }
                } else {
                    String type = this.addTotalType(upIsNormal, downIsNormal, isAbsenteeism, isLeave, isOverTime);  //判断考勤人员表的总类型
                    ph.setType(type);
                }
                ph.setCreator(user.getUserID());
                ph.setCreateName(user.getUserName());
                ph.setCreateDate(new Date());
                ph.setUser(attendanceUserId);  //考勤人员id
                if (!MyStrings.nulltoempty(upIsNormal).isEmpty()) {
                    if ("0".equals(upIsNormal)) {
                        ph.setBeginState("2"); //上班状态:0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
                    } else {
                        ph.setBeginState("1"); //上班状态:0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
                    }
                }
                if (!MyStrings.nulltoempty(downIsNormal).isEmpty()) {
                    if ("0".equals(downIsNormal)) {
                        ph.setEndState("3"); //上班状态:0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
                    } else {
                        ph.setEndState("1"); //上班状态:0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
                    }
                }
                ph.setAmAttendance(new Date());
                ph.setPmAttendance(new Date());
                ph.setOrg(user.getOid());

                int dept = 0;
                try {
                    dept = Integer.parseInt(user.getDepartment());
                } catch (NumberFormatException e) {//==null ==""
                    //Do Nothing
                }
                ph.setDept(dept);
                ph.setUpdateDesc(updateDesc);
                ph.setUpdator(user.getUserID());
                ph.setUpdateName(user.getUserName());
                ph.setUpdateDate(new Date());
                ph.setApproveStatus("1");
                ph.setPreviousId(previousId);
                ph.setVersionNo(versionNo);
                personnelAttendanceUserHistoryDao.save(ph);

                if (isAbsenteeism.equals("1")) {  //旷工
                    JSONArray jsonArray = JSONArray.fromObject(absenteeismAll);
                    List absenteeismList = JSONArray.toList(jsonArray);
                    if (absenteeismList != null && absenteeismList.size() > 0) {
                        for (int i = 0; i < absenteeismList.size(); i++) {
                            JSONObject absenteeismJson = JSONObject.fromObject(absenteeismList.get(i));
                            String aBeginDate = absenteeismJson.getString("aBeginDate");//开始时间
                            String aEndDate = absenteeismJson.getString("aEndDate");//结束时间
                            Date beginDate = NewDateUtils.dateFromString(aBeginDate, sdf);
                            Date endDate = NewDateUtils.dateFromString(aEndDate, sdf);
                            this.addPersonnelAttendanceUserDetailHistory(beginDate, endDate, "7", "2", null, null, ph, user, null, null, null);
                        }
                    }
                }
                if (isLeave.equals("1")) {  //有请假
                    JSONArray jsonArray = JSONArray.fromObject(leaveListAll);
                    List leaveList = JSONArray.toList(jsonArray);
                    if (leaveList != null && leaveList.size() > 0) {
                        for (int i = 0; i < leaveList.size(); i++) {
                            JSONObject jo = JSONObject.fromObject(leaveList.get(i));
                            String leaveBeginDate = jo.getString("leaveBeginDate"); //开始时间
                            String leaveEndDate = jo.getString("leaveEndDate");//结束时间
                            String leaveType = jo.getString("leaveType");//请假类型 1-事假,2-病假,3-年假,4-调休,5-婚假,6-产假,7-陪产假,8-路途假,9-其他'
                            String leaveReason = jo.getString("leaveReason");//请假原因
                            Date beginDate = NewDateUtils.dateFromString(leaveBeginDate, sdf);
                            Date endDate = NewDateUtils.dateFromString(leaveEndDate, sdf);
                            this.addPersonnelAttendanceUserDetailHistory(beginDate, endDate, "5", "2", leaveType, leaveReason, ph, user, null, null, null);
                        }
                    }
                }
                if (isOverTime.equals("1")) {   //有加班
                    JSONArray overArray = JSONArray.fromObject(overListAll);
                    List overList = JSONArray.toList(overArray);
                    if (overList != null && overList.size() > 0) {
                        for (int i = 0; i < overList.size(); i++) {
                            JSONObject jo = JSONObject.fromObject(overList.get(i));
                            String overDuration = jo.getString("overDuration");//时长
                            String overReason = jo.getString("overReason");//加班事由
                            String overMemo = jo.getString("overMemo");//备注
                            this.addPersonnelAttendanceUserDetailHistory(null, null, "8", "2", null, overReason, ph, user, overDuration, overMemo, null);
                        }
                    }
                }

                if (!"".equals(upIsNormal) && "0".equals(upIsNormal)) {  // 上班考勤 1-正常，0-迟到
                    this.addPersonnelAttendanceUserDetailHistory(null, null, "2", "2", null, null, ph, user, null, null, "1"); //2-迟到
                }
                if (!"".equals(downIsNormal) && "0".equals(downIsNormal)) {   //下班考勤 1-正常，0-早退
                    this.addPersonnelAttendanceUserDetailHistory(null, null, "3", "2", null, null, ph, user, null, null, "2");  //3-早退
                }

                User general = userService.getUserByRoleCode(user.getOid(), "general");  //默认审批人为总务
                ApprovalProcess approvalProcess = new ApprovalProcess();
                approvalProcess.setOrg(user.getOid());
                approvalProcess.setFromUser(user.getUserID());
                approvalProcess.setBusinessType(36);
                approvalProcess.setBusiness(ph.getId());
                approvalProcess.setApproveStatus("1");
                approvalProcess.setDescription(user.getUserName() + attendanceDate + "考勤的修改");
                approvalProcess.setLevel(1);
                approvalProcess.setToUser(general.getUserID());
                approvalProcess.setToUserName("指定审批人");  //审批人总称
                approvalProcess.setUserName(general.getUserName());
                approvalProcess.setAskName(user.getUserName());
                approvalProcess.setCreateDate(now);
                approvalProcessDao.save(approvalProcess);

                //给审批人的待处理发送
                this.attendanceRejectSend(1, 1, approvalProcess, general.getUserID(), "/myAttendanceApproval", null, null, "personalSubmitAttendanceApproval");
                //给申请人的待处理发送
                this.attendanceRejectSend(0, 1, approvalProcess, user.getUserID(), "/myAttendanceApply", null, null, "myAttendanceEditApply");
                map.put("status", 1);
                map.put("content", "操作成功");
            }
        }else {
            map.put("status", 0);
            map.put("content", "操作失败");
        }
        return map;
    }

    //考勤 长连接推送   pass 通道  superscript 角标
//    @Override
    public void attendanceRejectSend(int loginNum,int operate,ApprovalProcess approvalProcess,Integer toUserId,String pass,String title, String content,String code){
        System.out.println("个人考勤修改推送开始:"+new Date());
        System.out.println("考勤修改历史id："+approvalProcess.getBusiness()+" userId: "+toUserId);
        HashMap<String,Object> hashMap=new HashMap<>();
        hashMap.put("approvalProcess", approvalProcess);
        User approvalUser= userService.getUserByID(toUserId);
        swMessageService.rejectSend(loginNum,operate,hashMap,toUserId.toString(),pass,title,content,approvalUser,code);
        System.out.println("个人考勤修改推送结束:"+new Date());

    }

    @Override     //updateType 1-总务修改的考勤修改  2-个人修改的考勤修改
    public Map<String,Object> approvalAttendance(User user,Integer approvalProcessId,String approveMemo,String approveStatus,Integer updateType){
        Map<String,Object> map = new HashMap<>();  //最后的返回值
        Date now = new Date(System.currentTimeMillis());
        ApprovalProcess approvalProcess = approvalProcessDao.get(approvalProcessId);
        if (approvalProcess!=null) {
            if ("2".equals(approvalProcess.getApproveStatus())||"3".equals(approvalProcess.getApproveStatus())) {
                map.put("status", 2);
                map.put("content", "已审批，不可重复操作");
            } else {
                String messageCont = "";  //消息的描述
                String memo = "";   //操作时间
                PersonnelAttendanceUserHistory personnelAttendanceUserHistory = personnelAttendanceUserHistoryDao.get(approvalProcess.getBusiness());
                User attendanceUser = userService.getUserByID(personnelAttendanceUserHistory.getUser());   //要修改考勤的人员

                if ("3".equals(approveStatus)) {   //驳回
                    approvalProcess.setApproveStatus("3");
                    approvalProcess.setReason(approveMemo);
                    approvalProcess.setHandleTime(now);
                    approvalProcess.setUserName(user.getUserName());
                    approvalProcessDao.update(approvalProcess);

                    personnelAttendanceUserHistory.setApproveStatus("3");
                    personnelAttendanceUserHistoryDao.update(personnelAttendanceUserHistory);

                    if (1==updateType) {  //总务修改的考勤修改
                        //消息推送，给申请人
                        messageCont = approvalProcess.getDescription() + "申请被驳回了！";
                    }else {
                        messageCont =  "您的" + NewDateUtils.dateToString(personnelAttendanceUserHistory.getAttendanceDate(), "yyyy-MM-dd") + "考勤的修改申请被驳回了！";
                    }
                    memo = "操作时间  " + user.getUserName() + " " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                } else {
                    //先处理旧数据
                    personnelAttendanceMonthlyService.ClearDay(attendanceUser, personnelAttendanceUserHistory.getAttendanceDate());//清除某人某天的考勤月表

                    personnelAttendanceUserHistory.setApproveStatus("2");
                    personnelAttendanceUserHistoryDao.update(personnelAttendanceUserHistory);

                    approvalProcess.setApproveStatus("2");
                    approvalProcess.setHandleTime(now);
                    approvalProcess.setUserName(user.getUserName());
                    approvalProcessDao.update(approvalProcess);
                    PersonnelAttendanceUser personnelAttendanceUser = new PersonnelAttendanceUser();
                    if (null != personnelAttendanceUserHistory.getAttendanceId() && personnelAttendanceUserHistory.getAttendanceId() != 0) {
                        personnelAttendanceUser = personnelAttendanceUserDao.get(personnelAttendanceUserHistory.getAttendanceId());
//                        personnelAttendanceMonthlyService.ClearDay(attendanceUser, personnelAttendanceUserHistory.getAttendanceDate());//清除某人某天的考勤月表

                        //把明细的清除
                        List<PersonnelAttendanceUserDetail> attendanceUserDetailsList = this.getPersonnelAttendanceUserDetailById(personnelAttendanceUserHistory.getAttendanceId());
                        for (PersonnelAttendanceUserDetail pd : attendanceUserDetailsList) {
                            personnelAttendanceUserDetailDao.delete(pd);  //清除考勤明细

                            List<PersonnelAttendanceUserDetailPunch> personnelAttendanceUserDetailPunches = getUserDetailPunchList(user.getOid(),pd.getId(),null,null,null);
                            for (PersonnelAttendanceUserDetailPunch pdp:personnelAttendanceUserDetailPunches) {
                                pdp.setDetail(null);
                                personnelAttendanceUserDetailPunchDao.update(pdp);
                            }
                        }
                        personnelAttendanceUser.setPmAttendance(personnelAttendanceUserHistory.getPmAttendance());
                        personnelAttendanceUser.setAmAttendance(personnelAttendanceUserHistory.getAmAttendance());
                        personnelAttendanceUser.setEndState(personnelAttendanceUserHistory.getEndState());
                        personnelAttendanceUser.setBeginState(personnelAttendanceUserHistory.getBeginState());
                        if ("10".equals(personnelAttendanceUserHistory.getType())) {
                            personnelAttendanceUser.setType((byte)1);
                        }else {
                            personnelAttendanceUser.setType(Byte.valueOf(personnelAttendanceUserHistory.getType()));
                        }
                        personnelAttendanceUser.setUpdateDate(now);
                        personnelAttendanceUser.setUpdateName(attendanceUser.getUserName());
                        personnelAttendanceUser.setUpdator(attendanceUser.getUserID());
                        personnelAttendanceUserDao.update(personnelAttendanceUser);
                    } else {
                        BeanUtils.copyPropertiesIgnoreNull(personnelAttendanceUserHistory, personnelAttendanceUser);
                        personnelAttendanceUser.setId(null);
                        String type = "1";  //1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
                        if (!MyStrings.nulltoempty(personnelAttendanceUserHistory.getBeginState()).isEmpty() &&
                                !MyStrings.nulltoempty(personnelAttendanceUserHistory.getEndState()).isEmpty()){
                            if ("8".equals(personnelAttendanceUserHistory.getBeginState())||"8".equals(personnelAttendanceUserHistory.getEndState())
                             &&!personnelAttendanceUserHistory.getBeginState().equals(personnelAttendanceUserHistory.getEndState())){   //去除全是8的，都是8则为1
                                if ("8".equals(personnelAttendanceUserHistory.getBeginState())){
                                    type = personnelAttendanceUserHistory.getEndState();
                                }else {
                                    type = personnelAttendanceUserHistory.getBeginState();
                                }
                            }else if (personnelAttendanceUserHistory.getBeginState().equals(personnelAttendanceUserHistory.getEndState())){
                                type = personnelAttendanceUserHistory.getBeginState();
                            }else if ("1".equals(personnelAttendanceUserHistory.getBeginState())) {
                                type = personnelAttendanceUserHistory.getEndState();
                            }else if ("1".equals(personnelAttendanceUserHistory.getEndState())){
                                type = personnelAttendanceUserHistory.getBeginState();
                            }else {
                                type = "9";
                            }
                        }
                        personnelAttendanceUser.setType(Byte.valueOf(type));

                        Integer dept = 0;
                        if (StringUtils.isNotEmpty(attendanceUser.getDepartment())){
                            dept = Integer.getInteger(attendanceUser.getDepartment());
                        }
                        map = this.returnTime(user.getOid(), dept, personnelAttendanceUserHistory.getAttendanceDate(), map);  //获取此时此部门所在的考勤规则的三种时间
                        String workBegin = (String) map.get("beginTime");   //开始上班时间  时分
                        String workEnd = (String) map.get("endTime");  //下班时间   时分
                        personnelAttendanceUser.setBeginTime(NewDateUtils.dateFromString(NewDateUtils.dateToString(personnelAttendanceUserHistory.getAttendanceDate(),"yyyy-MM-dd")+" "+workBegin, "yyyy-MM-dd HH:mm"));
                        personnelAttendanceUser.setEndTime(NewDateUtils.dateFromString(NewDateUtils.dateToString(personnelAttendanceUserHistory.getAttendanceDate(),"yyyy-MM-dd")+" "+workEnd, "yyyy-MM-dd HH:mm"));
                        personnelAttendanceUser.setCreateDate(now);
                        personnelAttendanceUser.setCreator(attendanceUser.getUserID());
                        personnelAttendanceUser.setCreateName(attendanceUser.getUserName());
                        personnelAttendanceUser.setUpdateDate(now);
                        personnelAttendanceUser.setUpdateName(attendanceUser.getUserName());
                        personnelAttendanceUser.setUpdator(attendanceUser.getUserID());
                        personnelAttendanceUserDao.save(personnelAttendanceUser);
                    }

                    List<PersonnelAttendanceUserDetailHistory> newAttendanceUserDetailHistoryList = getPersonnelAttendanceUserDetailHistoryByPHId(personnelAttendanceUserHistory.getId(), null, null);
                    Double overDurationTotal = 0.0D;//总时长
                    Double leaveDurationTotal = 0.0D;//总时长
                    int overSize = 0;
                    int leaveSize = 0;
                    int absenteeismSize = 0;

                    for (PersonnelAttendanceUserDetailHistory pdh : newAttendanceUserDetailHistoryList) {
                        if ("8".equals(pdh.getType())) {//加班
                            overDurationTotal = overDurationTotal + pdh.getDuration();
                            overSize++;
                        } else if ("5".equals(pdh.getType())) {
                            leaveDurationTotal = leaveDurationTotal + pdh.getDuration();
                            leaveSize++;
                        } else if ("7".equals(pdh.getType())) {
                            absenteeismSize++;
                        }

                        PersonnelAttendanceUserDetail pd = new PersonnelAttendanceUserDetail();
                        BeanUtils.copyPropertiesIgnoreNull(pdh, pd);  //将pd与pdh中一样的值从p复制到ph中，不一样的值，另外set
                        pd.setAttendanceId(personnelAttendanceUser.getId());
//                        pd.setPersonnelAttendanceUser(personnelAttendanceUser);
                        pd.setId(null);
                        personnelAttendanceUserDetailDao.save(pd);
                    }

                    if (overSize > 0)
                        personnelAttendanceMonthlyService.SetOvertime(attendanceUser, personnelAttendanceUser.getAttendanceDate(), (byte) overSize, overDurationTotal.floatValue());
                    //请假
                    if (leaveSize > 0)
                        personnelAttendanceMonthlyService.SetLeave(attendanceUser, personnelAttendanceUser.getAttendanceDate(), (byte) leaveSize, leaveDurationTotal.floatValue());
                    //矿工
                    if (absenteeismSize > 0)
                        personnelAttendanceMonthlyService.SetAbsenteeisme(attendanceUser, personnelAttendanceUser.getAttendanceDate(), (byte) absenteeismSize);
                    if ("2".equals(personnelAttendanceUser.getBeginState())) {  // 上班考勤 1-正常，0-迟到
                        personnelAttendanceMonthlyService.SetLate(attendanceUser, personnelAttendanceUser.getAttendanceDate(), true);
                    }
                    if ("3".equals(personnelAttendanceUser.getEndState())) {   //下班考勤 1-正常，0-早退
                        personnelAttendanceMonthlyService.SetLeaveEarly(attendanceUser, personnelAttendanceUser.getAttendanceDate(), true);
                    }

                    this.commonOverTime(attendanceUser, personnelAttendanceUserHistory.getAttendanceDate(),attendanceUser.getUserID()); //将机构中有正在处理的计划和申报加班进行最终处理

                    if (1==updateType) {  //总务修改的考勤修改
                        messageCont = approvalProcess.getDescription() + "申请被批准了！";
                    }else {
                        messageCont =  "您的" + NewDateUtils.dateToString(personnelAttendanceUserHistory.getAttendanceDate(), "yyyy-MM-dd") + "考勤的修改申请被批准了！";
                    }
                    memo = "操作时间  " + user.getUserName() + " " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());

                }
                if (1==updateType){  //总务修改的考勤修改

                    //否决的推送【审批人的推送】
                    this.attendanceRejectSend(-1, -1, approvalProcess, user.getUserID(), "/attendanceApprovalInstance", approvalProcess.getDescription(), approvalProcess.getDescription(), "attendanceOfficerSubmitEdit");
                    //消息推送，给总务
//                    String messageCont = approvalInstance.getTitle() + "申请被驳回了！";
//                    String memo = "操作时间  " + user.getUserName() + " " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
                    userSuspendMsgService.saveUserSuspendMsg(1, messageCont, messageCont, memo, approvalProcess.getFromUser(), "attendanceChangeDetail", approvalProcess.getBusiness());//推送我的消息

                    //推给总务，申请角标不变，列表加1【申请人的】
                    this.attendanceRejectSend(0, -1, approvalProcess, approvalProcess.getFromUser(), "/attendanceApplyInstance", approvalProcess.getDescription(), approvalProcess.getDescription(), "attendanceEditApply");


                    if ("2".equals(approveStatus)) {   //批准的
                        //消息推送，给被修改得员工
                        String messageCont1 = "您" + new SimpleDateFormat("yyyy-MM-dd").format(personnelAttendanceUserHistory.getAttendanceDate()) + " 的考勤被修改了，具体可到“我的考勤”中查看";
                        userSuspendMsgService.saveUserSuspendMsg(1, messageCont1, messageCont1, memo
                                , attendanceUser.getUserID(), "attendanceChangeDetail", approvalProcess.getBusiness());//推送我的消息
                    }

                }else if (2==updateType) {   //个人修改的考勤修改

                    //推给总务，审批角标-1，列表-1
                    this.attendanceRejectSend(-1, -1, approvalProcess, user.getUserID(), "/myAttendanceApproval", null, null, "personalSubmitAttendanceApproval");
                    //推给申请人，申请角标不变，列表-1
                    this.attendanceRejectSend(0, -1, approvalProcess, approvalProcess.getFromUser(), "/myAttendanceApply", null, null, "myAttendanceEditApply");
                    //消息推送，给申请人
                    userSuspendMsgService.saveUserSuspendMsg(1, messageCont, memo, approvalProcess.getFromUser(), "myAttendanceChangeDetail", approvalProcess.getBusiness());//推送我的消息
                }
                map.put("status", 1);
                map.put("content", "操作成功");
            }
        }else {
            map.put("status", 0);
            map.put("content", "操作失败");
        }
        return map;
    }

    //将考勤修改中涉及到的待处理加班进行无需申报处理  user修改考勤提交的人 attendanceDate-考勤日期  overTimeUserId-获取加班的人(被修改考勤的考勤人员)
    private void commonOverTime(User user,Date attendanceDate,Integer overTimeUserId){

        List<PersonnelOvertime> personnelOvertimes = overtimeService.getPersonnelOvertimeHandleByTime(overTimeUserId,NewDateUtils.today(attendanceDate),NewDateUtils.getLastTimeOfDay(attendanceDate));
        for (PersonnelOvertime personnelOvertime:personnelOvertimes) {
            personnelOvertime.setApproveStatus("3");
            personnelOvertime.setFinalResult("4");//1-已完结的加班,2-未被认可的加班,3-提交了加班申请,但实际未加班,4-无需再填报时长的加班,5-加班申请被驳回'
            personnelOvertimeDao.update(personnelOvertime);

            List<ApprovalProcess> approvalProcess1 = approvalProcessService.getApprovalProcessByBusiness(personnelOvertime.getId(), 2, null); //计划加班的审批流程
            List<ApprovalProcess> approvalProcess2 = approvalProcessService.getApprovalProcessByBusiness(personnelOvertime.getId(), 6, null);  //申报加班审批流程

            if (approvalProcess2.size()>0 && approvalProcess1.size()>0){
                for (ApprovalProcess a:approvalProcess2) {
                    if ("1".equals(a.getApproveStatus())){
                        a.setApproveStatus("3");
                        approvalProcessDao.update(a);
                        overtimeService.outTimeRejectSend(-1,-1,personnelOvertime,a.getToUser(),"/approvalOutTimeHandle",null,null,"approvalOutTime");
                    }else if ("2".equals(a.getApproveStatus())){
                        overtimeService.outTimeRejectSend(0,-1,personnelOvertime,a.getToUser(),"/approvalOutTimeApproval",null,null,"approvalOutTime");
                    }
                }
                if (approvalProcess2.size()<approvalProcess1.size()){
                    for (ApprovalProcess a:approvalProcess1) {
                        if (a.getLevel()>approvalProcess2.size()){
                            overtimeService.outTimeRejectSend(0,-1,personnelOvertime,a.getToUser(),"/approvalOutTimeApproval",null,null,"approvalOutTime");
                        }
                    }
                }
                //给申请人 待处理推(有申报加班，那就是在申请人的待处理中)
                overtimeService.outTimeRejectSend(0, -1, personnelOvertime, personnelOvertime.getUser_(), "/applyOutTimeHandle", null, null, "applyOutTime");
            }else {
                for (ApprovalProcess a:approvalProcess1) {  //计划加班的审批流程
                    if ("1".equals(a.getApproveStatus())){
                        a.setApproveStatus("3");
                        approvalProcessDao.update(a);
                        overtimeService.outTimeRejectSend(-1,-1,personnelOvertime,a.getToUser(),"/approvalOutTimeHandle",null,null,"approvalOutTime");
                    }else if ("2".equals(a.getApproveStatus())){
                        overtimeService.outTimeRejectSend(0,-1,personnelOvertime,a.getToUser(),"/approvalOutTimeApproval",null,null,"approvalOutTime");
                    }
                }
                //给申请人 已批准推（只有计划加班的审批流程，那么就是计划加班审批完成，在申请人已批准中）
                overtimeService.outTimeRejectSend(-1, -1, personnelOvertime, personnelOvertime.getUser_(), "/applyOutTimeApproval", null, null, "applyOutTime");
            }

            //消息推送，给加班的申请人  (此处的user获取的为考勤修改提交的申请人)
            String memo = "操作时间  " + user.getUserName() + " " + NewDateUtils.dateToString(new Date(),"yyyy-MM-dd HH:mm:ss");
            String messageCont2 = "您" + NewDateUtils.dateToString(attendanceDate,"yyyy-MM-dd") + " 的考勤已被"+user.getUserName()+"修改，这天加班的时长无需再申报";
            userSuspendMsgService.saveUserSuspendMsg(1,messageCont2,memo,overTimeUserId,"applyOutTimeDetail",personnelOvertime.getId());//推送我的消息
        }
    }

    @Override   //beginTime1:开始时间   endTime1 ：结束时间  type 1-加班 2-请假 3-申报加班 4-提前结束请假 5-补报请假
    public Integer getStateTime(Integer oid, Integer deptId, Date openDate, Integer type, String beginTime1, String endTime1) {
        Integer stateTime = 0;  //不可以申请

        //获取某天是休还是班
        PersonnelAttendanceException personnelAttendanceException= workAttendanceService.getPersonnelAttendanceExceptionByExceptionDate(oid,openDate);
        if (personnelAttendanceException!=null&&"1".equals(personnelAttendanceException.getType())){   //类型：1-上班改休息(休)，2-休息改上班(班)
            if (1==type||3==type) {    //加班
                stateTime = 1; //在时间之外，可以申请加班
            }
        }else {
            PersonnelAttendanceConfig personnelAttendanceConfigs = this.getPersonnelAttendanceConfigByDept(oid, deptId, openDate);
            if (personnelAttendanceConfigs != null) {
                Date begin = NewDateUtils.dateFromString(beginTime1, "yyyy-MM-dd HH:mm:ss");
                Date end = NewDateUtils.dateFromString(endTime1, "yyyy-MM-dd HH:mm:ss");
                Date date1 = NewDateUtils.joinDateTimeString(openDate, NewDateUtils.dateToString(personnelAttendanceConfigs.getBeginTime(), "HH:mm"));   //上班时间
                Date date2 = NewDateUtils.joinDateTimeString(openDate, NewDateUtils.dateToString(personnelAttendanceConfigs.getEndTime(), "HH:mm"));   //下班时间
                Date date3 = NewDateUtils.joinDateTimeString(openDate, NewDateUtils.dateToString(personnelAttendanceConfigs.getBreakBegin(), "HH:mm"));  //午休开始时间
                Date date4 = NewDateUtils.joinDateTimeString(openDate, NewDateUtils.dateToString(personnelAttendanceConfigs.getBreakEnd(), "HH:mm"));  //午休结束时间
                if (new Date().getTime()<begin.getTime()&&new Date().getTime()<end.getTime() || 3==type || 5==type) {
                    if (1 == type || 3==type) {    //加班
                        long dateHalfLong = date1.getTime() - 30 * 60 * 1000;
                        Date dateHalf = new Date(dateHalfLong);  //上班时间的前半个小时【如果是早晨上班之前的加班，则加班开始时间必须在上班时间半小时之前】
                        if (begin.getTime() < date1.getTime()) {
                            if (begin.getTime() <= dateHalf.getTime() && end.getTime() <= date1.getTime() && begin.getTime() < end.getTime()) {
                                stateTime = 1; //在时间之外，可以申请加班
                            }
                        } else if (begin.getTime() >= date2.getTime() || (date3.getTime() <= begin.getTime() && end.getTime() <= date4.getTime())) {    //申请的结束时间<=上班时间 ||申请的开始时间>=下班时间 || (申请的开始时间和结束时间在午休时间之间)
                            stateTime = 1; //在时间之外，可以申请加班
                        }
                    } else if (2==type || 5==type) {  //请假或补报请假
                        Integer equalDay = 0; //请假是否为同一天  0-否  1-是
                        if (NewDateUtils.today(begin).getTime() < NewDateUtils.today(end).getTime()) {   //请假申请的时间不是同一天
                            equalDay = 1;
                            date2 = NewDateUtils.joinDateTimeString(end, NewDateUtils.dateToString(personnelAttendanceConfigs.getEndTime(), "HH:mm"));   //下班时间
                            date4 = NewDateUtils.joinDateTimeString(end, NewDateUtils.dateToString(personnelAttendanceConfigs.getBreakEnd(), "HH:mm"));  //午休结束时间
                        }
                        if (begin.getTime() >= date1.getTime() && end.getTime() <= date2.getTime() && begin.getTime() < end.getTime()) {
                            //请假开始时间<=午休开始时间 && 请假结束时间<=午休开始时间  || 请假结束时间>午休结束时间 【请假的时间是上午，或者上午加下午】
                            if (begin.getTime() < date3.getTime() && (end.getTime() <= date3.getTime() || end.getTime() > date4.getTime())) {
                                stateTime = 1; //在允许申请时间之内，可以申请请假
                            }else if (begin.getTime()>=date4.getTime()&&equalDay==0) {
                                stateTime = 1; //在允许申请时间之内，可以申请请假
                                }else if (1==equalDay){    //请假的不是同一天，而且请假开始时间是下午，结束时间是上午或下午

                                //请假开始时间的结束时间
                                date2 = NewDateUtils.joinDateTimeString(openDate, NewDateUtils.dateToString(personnelAttendanceConfigs.getEndTime(), "HH:mm"));   //下班时间
                                //请假开始时间的午休结束时间
                                date4 = NewDateUtils.joinDateTimeString(openDate, NewDateUtils.dateToString(personnelAttendanceConfigs.getBreakEnd(), "HH:mm"));  //午休结束时间
                                //请假结束时间的午休开始时间
                                Date date5 = NewDateUtils.joinDateTimeString(end, NewDateUtils.dateToString(personnelAttendanceConfigs.getBreakBegin(), "HH:mm"));  //午休开始时间
                                //请假结束时间的午休结束时间
                                Date date6 = NewDateUtils.joinDateTimeString(end, NewDateUtils.dateToString(personnelAttendanceConfigs.getBreakEnd(), "HH:mm"));  //午休结束时间
                                //请假结束时间的开始上班时间
                                Date date7 = NewDateUtils.joinDateTimeString(end, NewDateUtils.dateToString(personnelAttendanceConfigs.getBeginTime(), "HH:mm"));  //午休结束时间

                                //请假的是下午
                                if(begin.getTime()>=date4.getTime()&&begin.getTime()<date2.getTime() && end.getTime()>date7.getTime()&&(end.getTime() <= date5.getTime() || end.getTime() > date6.getTime())){
                                    stateTime = 1; //在允许申请时间之内，可以申请请假
                                }else if (begin.getTime()<date3.getTime()&&end.getTime()>date7.getTime()&&(end.getTime()<=date5.getTime()|| end.getTime() > date6.getTime())){
                                    stateTime = 1; //在允许申请时间之内，可以申请请假
                                }
                            }
                        }
                    }else if (4==type){  //提前结束请假
                        if (end.getTime()<=date3.getTime()||(end.getTime()<=date2.getTime()&&end.getTime()>=date4.getTime())){
                            stateTime = 1; //在允许申请时间之内，可以申请请假
                        }
                    }
                }
            } else {
                stateTime = 1;  //查不出来考勤时间则按照正常的情况均可以申请
            }
        }
        return stateTime;
    }

    //获取某月的上班/周末天数
    private Integer getWorkingDays(Integer org,Date beginDate,Date endDate,String type){
        Map<String,Object> map = new HashMap<>();
        String hql = "select count(id) from PersonnelAttendanceException where org=:org";
        map.put("org",org);
        if (beginDate!=null){
            hql+=" and exceptionDate between :beginDate and :endDate";
            map.put("beginDate",beginDate);
            map.put("endDate",endDate);
        }
        if (StringUtils.isNotEmpty(type)){   // 1-假 2-班
            hql+=" and type=:type";
            map.put("type",type);
        }
        Long workingDays = (Long) personnelAttendanceExceptionDao.getByHQLWithNamedParams(hql,map);
        return workingDays.intValue();
    }
    //获取某月的上班/周末天数
    private Pair<Set<Date>, Set<Date>> newGetWorkingDates(Integer org,Date beginDate,Date endDate){
        Map<String,Object> params = new HashMap<>();
        List<String> where = new ArrayList<>();
        StringBuffer hql = new StringBuffer("from PersonnelAttendanceException");
        where.add("org=:org");
        params.put("org",org);
        if (beginDate!=null){
            where.add("exceptionDate between :beginDate and :endDate");
            params.put("beginDate",beginDate);
            params.put("endDate",endDate);
        }
        if(!where.isEmpty()) {
            hql.append(" where ").append(StringUtils.join(where, " and "));
        }
        hql.append(" order by exceptionDate");
        List<PersonnelAttendanceException> list = personnelAttendanceExceptionDao.getListByHQLWithNamedParams(hql.toString(), params);
        Map<String, List<PersonnelAttendanceException>> collect = list.stream().collect(Collectors.groupingBy(PersonnelAttendanceException::getType));
        // 1-假 2-班
        return Pair.of(collect.getOrDefault("2",new ArrayList<>(0)).stream().map(PersonnelAttendanceException::getExceptionDate).collect(Collectors.toSet()), collect.getOrDefault("1",new ArrayList<>(0)).stream().map(PersonnelAttendanceException::getExceptionDate).collect(Collectors.toSet()));
    }

    private List<Map<String,Object>> getStateDays(Integer oid,Integer loginUserId,Integer userId,Date beginDate,Date endDate,String beginState,String endState,String type,List<Integer> userDaysIds){
        Map<String,Object> map = new HashMap<>();
        String hql = "select user,count(id) from PersonnelAttendanceUser where org=:org";
        map.put("org",oid);
        if (loginUserId!=null){
            Map<String,Object> map1 = userService.getUserIds(loginUserId,1);
            List<Integer> userIDs = (List<Integer>) map1.get("userIdsList");
            hql+=" and user in (:userIDs)";
            map.put("userIDs",userIDs);
        }
        if (userId!=null){ //就查这个人的
            hql+=" and user=:userId";
            map.put("userId",userId);
        }
        if (StringUtils.isNotEmpty(beginState)){  //上班状态
            hql+=" and beginState=:beginState";
            map.put("beginState",beginState);
        }
        if (StringUtils.isNotEmpty(endState)){   //下班状态
            hql+=" and endState=:endState";
            map.put("endState",endState);
        }
        if (StringUtils.isNotEmpty(type)){  //整个考勤状态
            hql+=" and type=:type";
            map.put("type",type);
        }
        if (beginDate!=null&&endDate!=null){
            hql+=" and attendanceDate between :beginDate and :endDate";
            map.put("beginDate",beginDate);
            map.put("endDate",endDate);
        }
        if (userDaysIds!=null&&userDaysIds.size()>0){
            hql+=" and user in (:userDaysIds)";
            map.put("userDaysIds",userDaysIds);
        }
        hql+=" group by user";
        List<Object[]> userDays = personnelAttendanceUserDao.getListByHQLWithNamedParams(hql,map);
        List<Map<String,Object>> listMap = new ArrayList<>();
        for (Object[] userDay:userDays) {
            Map<String,Object> map1 = new HashMap<>();
            map1.put("userId",userDay[0]);   //userId
            Long typeDay = (Long) userDay[1];
            map1.put("typeDay",typeDay!=null?typeDay.intValue():0);  //天数
            listMap.add(map1);
        }
        return listMap;
    }
    private Pair<Set<Date>, Set<Date>> getDeforeWorkingDates(List<Date> everyDates, Date beginDate, Date endDate){
        Map<Boolean, List<Date>> collect = everyDates.stream().filter(date -> beginDate.getTime()>=date.getTime() && date.getTime()<=endDate.getTime()).collect(Collectors.groupingBy(date -> NewDateUtils.isWeekend(date)));
        return Pair.of(collect.getOrDefault(Boolean.FALSE,new ArrayList<>(0)).stream().collect(Collectors.toSet()), collect.getOrDefault(Boolean.TRUE,new ArrayList<>(0)).stream().collect(Collectors.toSet()));
    }

    private Integer noAttendanceWorkingDays(Date beginDate,Date endDate){
        Integer workingDays = 0;
        while (beginDate.getTime()<=endDate.getTime()){
            int workDay = NewDateUtils.getDay(beginDate); //获取某个日期是周几，周日为0，周六为6
            if (workDay>=1&&workDay<=5){   //周一到周五
                workingDays++;
            }
            beginDate = NewDateUtils.changeDay(beginDate,1);
        }
        return workingDays;
    }

    @Override    //sourceType:来源类型 1-我的团队 2-总览-职工下的 3-工作记录点评   dateType:1-月报 2-年报
    public Map<String, Object> getAttendanceDays(User user,Integer deptId,Date beginDate,Integer sourceType,Integer dateType,Integer onlyUser,PageInfo pageInfo) {
        Date now = new Date(System.currentTimeMillis());
        logger.warn("WorkAttendanceService.getAttendanceDays:"+user.getOid()+" begin time :" + NewDateUtils.dateToString(now,"yyyy-MM-dd HH:mm:ss.SSS"));
        Map<String,Object> map = new HashMap<>();
        Date endDate = null; //结束时间
        if (dateType!=null&&2==dateType){  //年报
            beginDate = NewDateUtils.getNewYearsDay(beginDate);  //某年的第一天(指定日期那年元旦的0时0分0秒0毫秒)
            endDate = NewDateUtils.today(NewDateUtils.getLastTimeOfYear(beginDate)); //指定日期那年的最后1毫秒
            if (endDate.getTime()>NewDateUtils.today().getTime()){
                endDate = NewDateUtils.today();
            }
        }else {  //默认月报吧
            beginDate = NewDateUtils.changeMonth(beginDate,0);  //某月的第一天
            endDate = NewDateUtils.today(NewDateUtils.getLastTimeOfMonth(beginDate));
            if (endDate.getTime()>NewDateUtils.today().getTime()){
                endDate = NewDateUtils.today();
            }
        }

        Integer loginUserId = null;
        if (sourceType!=null){
            if (1==sourceType) {
                loginUserId = user.getUserID();
            }else if (3==sourceType){  //3-工作记录点评  是董事长自己和直接下属以及间接下属
                User superUser = userService.getUserByRoleCode(user.getOid(), "super");  //董事长
                loginUserId = superUser.getUserID();
            }
        }
        List<AttendanceUserDaysDto> userDaysDtos = userService.getUserDays(user.getOid(),loginUserId,deptId,onlyUser, beginDate, endDate,pageInfo);  //职工(有考勤和未考勤的)分页
        Integer attendanceStatus = 0;  //1-有考勤 0-没有考勤
        Integer workingDays = 0;  //某月总上班天数(截止到当前时间)
//        Integer workingDaysCurrent = 0;  //某月/某年当前的上班天数
        Date systemTime = workAttendanceService.getStartUsingSystemTime(user.getOid());  //是否有考勤
        if (systemTime == null) {  //系统没有设置考勤的，加班和请假就不算了，就只有总的上班天数,统计到当前时间
            workingDays = noAttendanceWorkingDays(beginDate, NewDateUtils.today(endDate));  //获取整个月/年的上班天数(页面上的总和的上班天数)
//            workingDaysCurrent = noAttendanceWorkingDays(beginDate, endDate);  //获取当前的上班天数(改了，没有考勤的这个上班天数不统计了)
//            for (AttendanceUserDaysDto a : userDaysDtos) {
//                a.setWorkingDays(workingDaysCurrent);
//                a.setSubmissionDays(workingDaysCurrent);
//            }
        }else {
            if(beginDate.getTime()<systemTime.getTime()){  //从考勤生效日开始计算
                beginDate = systemTime;
            }
             attendanceStatus = 1;  //1-有考勤 0-没有考勤
//            workingDays = getWorkingDays(user.getOid(),beginDate,NewDateUtils.today(NewDateUtils.getLastTimeOfMonth(beginDate)),"2"); //获取整个月的上班天数
            workingDays = getWorkingDays(user.getOid(),beginDate,NewDateUtils.today(endDate),"2"); //获取整个月/年的上班天数
//            workingDaysCurrent = getWorkingDays(user.getOid(),beginDate,endDate,"2"); //获取当前的上班天数
//            List<Integer> userDaysIds = new ArrayList<>();  //下面的内容取此内容里的
//            for (AttendanceUserDaysDto a:userDaysDtos) {
//                userDaysIds.add(a.getUserID());
//            }
//            while (beginDate.getTime()<endDate.getTime()) {
                //查询对应user的考勤加班等(月)
            logger.warn("WorkAttendanceService.getAttendanceDays:"+user.getOid()+" before loop running :" +TimeUtils.toTimeString2sf(System.currentTimeMillis()-now.getTime())+" at :" + NewDateUtils.dateToString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss.SSS"));

                for (AttendanceUserDaysDto attendanceUserDaysDto : userDaysDtos) {
                    logger.warn("WorkAttendanceService.getAttendanceDays:"+user.getOid()+" loop running :" +TimeUtils.toTimeString2sf(System.currentTimeMillis()-now.getTime())+" at uid:" + attendanceUserDaysDto.getUserID() + "begin" + NewDateUtils.dateToString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss.SSS"));
//                    attendanceUserDaysDto.setWorkingDays(workingDaysCurrent); //某月当前的上班天数
                    attendanceUserDaysDto.setWorkingDays(workingDays); //某月当前的上班天数
                    logger.warn("WorkAttendanceService.getAttendanceDays.getStateDays:"+user.getOid()+" loop running :" +TimeUtils.toTimeString2sf(System.currentTimeMillis()-now.getTime())+" at uid:" + attendanceUserDaysDto.getUserID() + "begin" + NewDateUtils.dateToString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss.SSS"));
                    List<Map<String,Object>> leaveDaysList = getStateDays(user.getOid(),null,attendanceUserDaysDto.getUserID(),beginDate,endDate,"5","5","5",null); //查询全天请假的，整个状态是请假，上班与下班状态都是请假的。
                    logger.warn("WorkAttendanceService.getAttendanceDays.getAttendanceMonthByUser:"+user.getOid()+" loop running :" +TimeUtils.toTimeString2sf(System.currentTimeMillis()-now.getTime())+" at uid:" + attendanceUserDaysDto.getUserID() + "begin" + NewDateUtils.dateToString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss.SSS"));
                    List<PersonnelAttendanceMonthly> attendanceMonthlyList = personnelAttendanceMonthlyService.getAttendanceMonthliesByPeriod(user.getOid(), NewDateUtils.getYearMonth(beginDate),NewDateUtils.getYearMonth(endDate), deptId, null, attendanceUserDaysDto.getUserID(), null, null);
                    logger.warn("WorkAttendanceService.getAttendanceDays.after call:"+user.getOid()+" loop running :" +TimeUtils.toTimeString2sf(System.currentTimeMillis()-now.getTime())+" at uid:" + attendanceUserDaysDto.getUserID() + "begin" + NewDateUtils.dateToString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss.SSS"));

                    for (Map<String, Object> leaveDay : leaveDaysList) {  //取对应职工的请假天数
                        Integer leaveUserId = (Integer) leaveDay.get("userId");
                        if (attendanceUserDaysDto.getUserID().equals(leaveUserId)) {
                            Integer leaveDay1 = (Integer) leaveDay.get("typeDay");
                            attendanceUserDaysDto.setLeaveDays(attendanceUserDaysDto.getLeaveDays()+leaveDay1);
                            break;
                        }
                    }

                    Integer outTimeDays = 0;  //加班天数
                    for (PersonnelAttendanceMonthly personnelAttendanceMonthly : attendanceMonthlyList) {
                        Integer day1 = NewDateUtils.getDate(beginDate);  //开始的天
                        Integer day2 = NewDateUtils.getDate(NewDateUtils.getLastTimeOfMonth(beginDate));  //结束的天
                        if (NewDateUtils.getLastTimeOfMonth(beginDate).getTime()>new Date().getTime()){
                            day2 = NewDateUtils.getDate(new Date());
                        }
                        if (attendanceUserDaysDto.getUserID().equals(personnelAttendanceMonthly.getUser())) {
                            attendanceUserDaysDto.setState("1");  //考勤状态
                            while (day1 <= day2) {
                                try {
                                    Boolean noNeed = personnelAttendanceMonthly.GetNoNeed(day1);  //某天是否需要考勤 1-无需考勤 0-有考勤
                                    Pair<Byte, Float> overtimes = personnelAttendanceMonthly.GetOvertime(day1);
                                    Byte overtime = overtimes.getLeft();  //加班次数
                                    if (noNeed.equals(true) && overtime > 0) {
                                        outTimeDays++;
                                    }
                                    day1++;
                                } catch (InvocationTargetException | IllegalAccessException e) {
                                    Logger.getLogger(getClass()).error("获取考勤月表数据错误!", e);
                                }
                            }
                            break;
                        }
                    }
                    attendanceUserDaysDto.setOutTimeDays(outTimeDays);  //加班天数
                    Integer submissionDays = workingDays + attendanceUserDaysDto.getOutTimeDays() - attendanceUserDaysDto.getLeaveDays();
                    attendanceUserDaysDto.setSubmissionDays(submissionDays);  //截至目前应交天数
                    Integer notSubmissionDays = 0;
                    logger.warn("WorkAttendanceService.getAttendanceDays after loop set:"+user.getOid()+" loop running :" +TimeUtils.toTimeString2sf(System.currentTimeMillis()-now.getTime())+" at :" + attendanceUserDaysDto.getUserID() + "getMyWork bigin " + NewDateUtils.dateToString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss.SSS"));
                    if (dateType!=null&&2==dateType){    //dateType:1-月报 2-年报      queryDate参数支持 yyyy 和 yyyy-MM
                        notSubmissionDays = myWorkService.getNotSubmitDays(user.getOid(), attendanceUserDaysDto.getUserID(), NewDateUtils.getYear(beginDate).toString(),endDate);
                    }else {
                        notSubmissionDays = myWorkService.getNotSubmitDays(user.getOid(), attendanceUserDaysDto.getUserID(), NewDateUtils.dateToString(beginDate, "yyyy-MM-dd").substring(0, 7),endDate);
                    }
                    attendanceUserDaysDto.setNotSubmissionDays(notSubmissionDays); //截至目前未交天数
//                    beginDate = NewDateUtils.changeMonth(beginDate,1);  //下个月的一号
                    logger.warn("WorkAttendanceService.getAttendanceDays.getNotSubmitDays:"+user.getOid()+" loop running :" +TimeUtils.toTimeString2sf(System.currentTimeMillis()-now.getTime())+" at :" + attendanceUserDaysDto.getUserID() + " getMyWork end " + NewDateUtils.dateToString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss.SSS"));
                }
//            }
        }
        logger.warn("WorkAttendanceService.getAttendanceDays:"+user.getOid()+" finish loop running :" +TimeUtils.toTimeString2sf(System.currentTimeMillis()-now.getTime())+" at :" + NewDateUtils.dateToString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss.SSS"));
        map.put("workingDays",workingDays);  //总的上班天数
        map.put("userDaysDtos",userDaysDtos);
        map.put("attendanceStatus",attendanceStatus); //1-有考勤 0-没有考勤
        logger.warn("WorkAttendanceService.getAttendanceDays:"+user.getOid()+" duration :" +TimeUtils.toTimeString2sf(System.currentTimeMillis()-now.getTime())+" finish :" + NewDateUtils.dateToString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss.SSS"));
        return map;
    }

    @Override    //sourceType:来源类型 1-我的团队 2-总览-职工下的 3-工作记录点评   dateType:1-月报 2-年报
    public Map<String, Object> newGetAttendanceDays(User user,Integer deptId,Date beginDate,Integer sourceType,Integer dateType,Integer onlyUser,PageInfo pageInfo) {
        Date now = new Date(System.currentTimeMillis());
        logger.warn("WorkAttendanceService.getAttendanceDays:" + user.getOid() + " begin time :" + NewDateUtils.dateToString(now, sdf));
        Map<String, Object> map = new HashMap<>();
        Date attendanceStart = beginDate, endDate; //结束时间
        if (Integer.valueOf(2).equals(dateType)) {  //年报
            beginDate = NewDateUtils.getNewYearsDay(beginDate);  //某年的第一天(指定日期那年元旦的0时0分0秒0毫秒)
            endDate = NewDateUtils.getLastTimeOfYear(beginDate); //指定日期那年的最后1毫秒
        } else {  //月报
            beginDate = NewDateUtils.changeMonth(beginDate, 0);  //某月的第一天
            endDate = NewDateUtils.getLastTimeOfMonth(beginDate);
        }
        if (endDate.getTime() > System.currentTimeMillis()) {
            endDate = NewDateUtils.getLastTimeOfDay(NewDateUtils.today());
        }

        Integer loginUserId;
        switch (sourceType==null ? Integer.MIN_VALUE : sourceType) {//有可能传null
            case 1:
                loginUserId = user.getUserID();
                break;
            case 3://3-工作记录点评  是董事长自己和直接下属以及间接下属
                User superUser = userService.getUserByRoleCode(user.getOid(), "super");  //董事长
                loginUserId = superUser.getUserID();
                break;
            default://sourceType orthers or null
                loginUserId = null;
        }
        //获取全体的正常班休日期
        map.put("attendanceStatus", workAttendanceService.getStartUsingSystemTime(user.getOid())==null? 0 : 1); //1-有考勤 0-没有考勤
        Triple<List<Date>, Set<Date>, Set<Date>> workingDates = getWorkingDates(beginDate, endDate, user.getOid());//left 总, middle 班, right假,
        List<AttendanceUserDaysDto> userDaysDtos = userService.getUserDays(user.getOid(), loginUserId, deptId, onlyUser, beginDate, endDate, pageInfo);  //职工(有考勤和未考勤的)分页
        Map<Integer, AttendanceUserDaysDto> userDaysDtoMap = userDaysDtos.stream().collect(Collectors.toMap(AttendanceUserDaysDto::getUserID, Function.identity(), (key1, key2) -> key1));
        //查询对应user的考勤加班等(月)
        logger.warn("WorkAttendanceService.getAttendanceDays:" + user.getOid() + " before loop running :" + TimeUtils.toTimeString2sf(System.currentTimeMillis() - now.getTime()) + " at :" + NewDateUtils.dateToString(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss.SSS"));
        List<PersonnelAttendanceMonthly> monthlies = personnelAttendanceMonthlyService.getAttendanceMonthliesByPeriod(user.getOid(), NewDateUtils.getYearMonth(beginDate), NewDateUtils.getYearMonth(endDate), userDaysDtoMap.keySet());
        monthlies.stream().forEach(item -> {
            AttendanceUserDaysDto userDaysDto = userDaysDtoMap.get(item.getUser());
            userDaysDto.setState("1");
            item.MergeWholeDays(userDaysDto.getWholeDays());
        });
        for(AttendanceUserDaysDto dto : userDaysDtoMap.values()) {
            Date startDate, finishDate;//开始结束时间需要结合个人入离职
            startDate = dto.getOnDutyDate()!=null && dto.getOnDutyDate().getTime()>beginDate.getTime() ? dto.getOnDutyDate() : beginDate;
            finishDate = dto.getOffDutyDate()!=null && dto.getOffDutyDate().getTime()<endDate.getTime() ? dto.getOffDutyDate() : endDate;
            dto.setSubmitedDates(myWorkService.getSubmitDays(user.getOid(), dto.getUserID(), startDate, finishDate).stream().collect(Collectors.toSet()));
            dto.fillNoAttendanced(beginDate, endDate, workingDates);
        }
        logger.warn("WorkAttendanceService.getAttendanceDays:"+user.getOid()+" finish loop running :" +TimeUtils.toTimeString2sf(System.currentTimeMillis()-now.getTime())+" at :" + NewDateUtils.dateToString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss.SSS"));
        map.put("workingDays",workingDates.getMiddle().size());  //总的上班天数
        map.put("userDaysDtos",userDaysDtos);
        logger.warn("WorkAttendanceService.getAttendanceDays:"+user.getOid()+" duration :" +TimeUtils.toTimeString2sf(System.currentTimeMillis()-now.getTime())+" finish :" + NewDateUtils.dateToString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss.SSS"));
        return map;
    }

    @Override
    public Map<String, Object> directSuperior(Integer loginUserId, Integer userId) {
        Map<String, Object> map = new HashMap<>();
        Integer status = 3;  //1-loginUserId是userId的直接上级  2-loginUserId是userId的直接下级  3-loginUserId和userId没关系
        User loginUser = userService.getUserByID(loginUserId);  //登录人
        User workUser = userService.getUserByID(userId);  //职工id
        Integer loginLeader = Integer.parseInt(loginUser.getLeader());  //登录人直接上级
        Integer workLeader = Integer.parseInt(workUser.getLeader());  //职工直接上级
        if (loginLeader!=null&&workLeader!=null&&loginUserId.equals(workLeader)){
            status = 1;
        }else if (loginLeader!=null&&workLeader!=null&&userId.equals(loginLeader)){
            status = 2;
        }
        map.put("status",status);
        return map;
    }

    @Override
    public PersonnelAttendanceMonthly getOrFalseMonthly(Integer yearmonth, User user) {
        PersonnelAttendanceMonthly monthly = personnelAttendanceMonthlyService.getAttendanceMonthly(yearmonth, user.getUserID());
        if(monthly!=null) {
            return monthly;
        } else {
            Date beginDate = NewDateUtils.today(NewDateUtils.dateFromString(yearmonth+"01", "yyyyMMdd"));
            Triple<List<Date>, Set<Date>, Set<Date>> workingDates = getWorkingDates(NewDateUtils.today(beginDate), NewDateUtils.getLastTimeOfMonth(beginDate), user.getOid());//left 总, middle 班, right假,
            PersonnelAttendanceMonthly finalMonthly = personnelAttendanceMonthlyService.NewEntity(user,yearmonth);
            workingDates.getMiddle().stream().forEach(date -> {//left 总, middle 班, right假,
                try {
                    int d = NewDateUtils.getDate(date);
                    finalMonthly.SetWorkingHours(d, Float.valueOf(8.0F));//班
                    finalMonthly.SetAttendanced(d, Boolean.FALSE);
                } catch (InvocationTargetException | IllegalAccessException e) {
                    Logger.getLogger(getClass()).error("getOrFalseMonthly error!", e);
                }
            });
            workingDates.getRight().stream().forEach(date -> {//left 总, middle 班, right假,
                try {
                    finalMonthly.SetNoNeed(NewDateUtils.getDate(date), Boolean.TRUE, Boolean.FALSE);//假
                } catch (InvocationTargetException | IllegalAccessException e) {
                    Logger.getLogger(getClass()).error("getOrFalseMonthly error!", e);
                }
            });
//            Long end=NewDateUtils.getLastTimeOfMonth(date).getTime();
//            for(;date.getTime()<=end;date=NewDateUtils.tomorrow(date)) {
//                try {
//                    monthly.SetAttendanced(NewDateUtils.getDate(date), Boolean.FALSE);
//                } catch (InvocationTargetException | IllegalAccessException e) {
//                    Logger.getLogger(getClass()).error("getOrFalseMonthly error!", e);
//                }
//            }
            return finalMonthly;
        }
    }

    @Override
    public Triple<List<Date>, Set<Date>, Set<Date>> getWorkingDates(Date beginDate, Date endDate, Integer oid) {//left 总, middle 班, right假,
        Triple<List<Date>, Set<Date>, Set<Date>> workingDates = Triple.of(NewDateUtils.getEveryDate(beginDate, endDate), new HashSet<>(), new HashSet<>());  //left 所有, middle 班, right假,
        Date systemTime = workAttendanceService.getStartUsingSystemTime(oid);  //是否有考勤
        if (systemTime == null) {  //系统没有设置考勤的，加班和请假就不算了，就只有总的上班天数,统计到当前时间
            Pair<Set<Date>, Set<Date>> pair = getDeforeWorkingDates(workingDates.getLeft(), beginDate, NewDateUtils.today(endDate));  //获取整个月/年的上班天数(页面上的总和的上班天数)
            workingDates.getMiddle().addAll(pair.getLeft());
            workingDates.getRight().addAll(pair.getRight());
        } else {
            if (beginDate.getTime() < systemTime.getTime()) {  //考勤生效日前
                Pair<Set<Date>, Set<Date>> pair = getDeforeWorkingDates(workingDates.getLeft(), beginDate, NewDateUtils.yesterday(systemTime));  //获取整个月/年的上班天数(页面上的总和的上班天数)
                workingDates.getMiddle().addAll(pair.getLeft());
                workingDates.getRight().addAll(pair.getRight());
                beginDate = systemTime;
            }
            Pair<Set<Date>, Set<Date>> pair = newGetWorkingDates(oid, beginDate, endDate);//获取整个月/年的上班天数
            workingDates.getMiddle().addAll(pair.getLeft());
            workingDates.getRight().addAll(pair.getRight());
        }
        return workingDates;
    }

    @Override
    public Map<String, Object> setTimeTable(User user, List timeTable) {
        Map<String,Object> map = new HashMap<>();
        for (int j = 0; j < timeTable.size(); j++) {
            Date dateAttendance = null;
            Integer oldState = 1; //1-有老数据 0-没有老数据了

            List timeTable1 = JSONArray.fromObject(timeTable.get(j));
            for(int n = 0; n < timeTable1.size(); n++) {
                JSONObject jo1 = JSONObject.fromObject(timeTable1.get(n));
                Date dateAttendance1 = NewDateUtils.today(NewDateUtils.dateFromString(jo1.getString("date"), "yyyy-MM-dd"));
                if (dateAttendance == null) {
                    dateAttendance = dateAttendance1;
                }
                if (1 == oldState) {
                    Date beginTime = NewDateUtils.changeMonth(dateAttendance, 0);  //获取本月第一天
                    Date endTime = NewDateUtils.getLastTimeOfMonth(beginTime);//获取本月最后一天
                    List<PersonnelAttendanceException> personnelAttendanceExceptionOlds = this.getPersonnelAttendanceExceptionByMonth(user.getOid(), beginTime, endTime);
                    for (PersonnelAttendanceException pOld : personnelAttendanceExceptionOlds) {
                        personnelAttendanceExceptionDao.delete(pOld);
                    }
                    workAttendanceService.clearWorkDateCaches(user.getOid());//清除工作日缓存
                    oldState = 0;
                }

                this.addPersonnelAttendanceException(user.getOid(), dateAttendance1, jo1.getString("typeSet"), user); //添加作息日期
            }
        }
        map.put("status",1);  //设置成功

        return map;
    }

    @Override
    public Date getPersonnelAttendanceConfigOpenDate(Integer oid, String type, Date openDate) {
        Map<String, Object> params = new HashMap<>();
        String hql = "select openDate from PersonnelAttendanceConfig o where o.enabled = 1";
        if (oid != null) {
            hql += " and o.org=:oid ";
            params.put("oid", oid);
        }
        if (type != null) {
            hql += " and o.type=:type ";
            params.put("type", type);
        }
        if (openDate != null) {
            hql += " and o.openDate<=:openDate ";
            params.put("openDate", openDate);
        }
        hql += " order by id desc";

        return (Date)personnelAttendanceConfigDao.getByHQLWithNamedParams(hql, params);
    }

    /**
     * 打卡
     * @param user
     * @param leaveId 请假id
     * @param overTimeId 加班id
     * @param iotTerminalId 打卡设备id
     * @param punchType 打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
     * @param image 快照图片内容
     * @param terminalUuid (人事员工打卡的)唯一标识/webchat union_id
     * @param brand 品牌/昵称
     * @param model 型号
     * @return
     */
    @Override
    public Map<String, Object> clock(User user, Date punchTime,Integer leaveId, Integer overTimeId, Integer iotTerminalId, Byte punchType, String image,String terminalUuid,String brand,String model) {
        Map<String, Object> map = new HashMap<>();
        IotTerminal iotTerminal = iotTerminalDao.get(iotTerminalId);
        if (iotTerminal!=null&&iotTerminal.getOrgId().equals(user.getOid())) {
            PersonnelAttendanceException personnelAttendanceException = workAttendanceService.getPersonnelAttendanceExceptionByExceptionDate(user.getOid(), NewDateUtils.today()); //查询当天是否上班
            if ((personnelAttendanceException != null && "2".equals(personnelAttendanceException.getType())) || 7 == punchType || 8 == punchType) { //1-假(假得时候只允许加班打卡，上下班得可以打不上) 2-班
                Integer dept = 0;
                if (StringUtils.isNotEmpty(user.getDepartment())) {
                    dept = Integer.getInteger(user.getDepartment());
                }
                List<PersonnelAttendanceConfig> personnelAttendanceConfigs = getPersonnelAttendanceConfigByDeptId(user.getOid(), dept, new Date()); //当前部门的只能有一条
                PersonnelAttendanceConfig personnelAttendanceConfig = personnelAttendanceConfigs.get(0);

                if (personnelAttendanceConfig.getAttendancePattern().equals(ConfigAttendancePattern.attendanceTreasure.getIndex())) {

                    PersonnelAttendanceTerminal personnelAttendanceTerminal = this.getPersonnelAttendanceTerminal(terminalUuid,null,null,AttendanceTerminaType.device.getIndex());  //员工打卡的设备(现在只有手机的)
                    if (personnelAttendanceTerminal==null){
                        personnelAttendanceTerminal = this.addPersonnelAttendanceTerminal(user,terminalUuid,brand,model, AttendanceTerminaType.device.getIndex());
                    }else {
                        //手机设备的时候，要是品牌或型号对不上，可能是手机端的问题
                        if (!brand.equals(personnelAttendanceTerminal.getBrand()) || !model.equals(personnelAttendanceTerminal.getModel())) {
                            logger.warn("WorkAttendanceService.clock: 品牌:"+brand+" 型号："+model);
                            map.put("status", "0");
                            map.put("content", "品牌或型号与原有的不符");
                            return map;
                        }
                    }

                    PersonnelAttendanceUser py = workAttendanceService.getPersonnelAttendanceUserByUserIdAndId(null, user.getUserID(), NewDateUtils.today(), null);
                    Date beginTime = NewDateUtils.joinDateTimeString(NewDateUtils.today(), NewDateUtils.dateToString(personnelAttendanceConfig.getBeginTime(), "HH:mm:ss")); //上班时间，拼接上年月日
                    Date endTime = NewDateUtils.joinDateTimeString(NewDateUtils.today(), NewDateUtils.dateToString(personnelAttendanceConfig.getEndTime(), "HH:mm:ss")); //下班时间，拼接上年月日
                    Date breakBegin = NewDateUtils.joinDateTimeString(NewDateUtils.today(), NewDateUtils.dateToString(personnelAttendanceConfig.getBreakBegin(), "HH:mm:ss")); //午休开始时间，拼接上年月日
                    Date breakEnd = NewDateUtils.joinDateTimeString(NewDateUtils.today(), NewDateUtils.dateToString(personnelAttendanceConfig.getBreakEnd(), "HH:mm:ss")); //午休结束时间，拼接上年月日
                    if (py==null&&"1".equals(personnelAttendanceException.getType())&&(7 == punchType || 8 == punchType)){  //只有考勤是假的时候，才有可能没有这个，加班打卡需要加上
                        py = this.addPersonnelAttendanceUser1(user.getOid(), dept, null, "8", "8", "8", user.getUserID(), NewDateUtils.today(), beginTime, endTime,breakBegin,breakEnd,2);
                    }
                    if (py != null && py.getId() != null) {
                        String type = "1"; //1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班,0-未打卡;
                        PersonnelAttendanceUserDetail personnelAttendanceUserDetail = new PersonnelAttendanceUserDetail();

                        Byte detailPunchType = DetailPunchType.begin.getIndex(); //员工打卡明细对照信息类型 1-开始 2-结束
                        if (punchType.compareTo(PunchType.goToWork.getIndex()) == 0) { //打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
                            long timeInterval = punchTime.getTime() - beginTime.getTime();  //打卡时间-上班时间
                            if(timeInterval > 0 && repeatedClock(user, punchType, NewDateUtils.today())>0) {
                                // 上班重复打卡，超过上班时间，不再更新考勤数据
                                //添加员工打卡信息
                                PersonnelAttendancePunch personnelAttendancePunch = new PersonnelAttendancePunch();
                                personnelAttendancePunch.setOrg(user.getOid());
                                personnelAttendancePunch.setUser(user.getUserID());
                                personnelAttendancePunch.setDept(StringUtils.isNotEmpty(user.getDepartment()) ? Integer.getInteger(user.getDepartment()) : null);
                                //                    personnelAttendancePunch.setCardNo(cardNo);//打卡卡号
//                                personnelAttendancePunch.setAttendanceUser(user.getUserID()); //人员考勤ID
                                personnelAttendancePunch.setSysDevice(iotTerminalId); //打卡设备ID
                                personnelAttendancePunch.setPunchTerminal(personnelAttendanceTerminal.getId()); //人事打卡设备ID
                                personnelAttendancePunch.setPunchTime(punchTime);  //打卡时间
                                personnelAttendancePunch.setPunchType(punchType);  //打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
                                personnelAttendancePunch.setType(AttendanceType.getByName(type).getIndex()); //考勤类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班,0-其它
                                personnelAttendancePunch.setImage(image); //快照图片内容
                                personnelAttendancePunchDao.save(personnelAttendancePunch);
                                map.put("punchId", personnelAttendancePunch.getId());
                                map.put("status", "1");
                                map.put("content", "打卡已重复");
                                return map;
                            }
                            Short lateLimit = personnelAttendanceConfig.getLateLimit();  //迟到时限(分钟)
                            long lateLimitMills = lateLimit * 60 * 1000; //将分钟转换成毫秒
                            if (timeInterval > lateLimitMills) {  //打卡时间-上班时间>迟到时限  ,说明是矿工
                                type = "7";//旷工
                                personnelAttendanceUserDetail = workAttendanceService.getPersonnelAttendanceUserDetail(user.getUserID(), NewDateUtils.today(), "7", null, null);
                                if (personnelAttendanceUserDetail==null) {
                                    personnelAttendanceUserDetail = workAttendanceService.addPersonnelAttendanceUserDetail(beginTime, punchTime, "7", "2", null, null, null, null, py, user, null, null, "1", punchTime); //正常上班
                                    personnelAttendanceMonthlyService.SetAbsenteeisme(user, NewDateUtils.today(), (byte) 1);  //添加一次旷工
                                }else {  //如果是系统默认产生的旷工，那么将修改下旷工时间和对应的信息
                                    if (personnelAttendanceUserDetail.getBeginTime()==null&&personnelAttendanceUserDetail.getEndTime()==null){
                                        personnelAttendanceUserDetail.setBeginTime(beginTime);
                                        personnelAttendanceUserDetail.setEndTime(punchTime);
                                        personnelAttendanceUserDetail.setState("1"); //状态:1-上班,2-下班
                                        personnelAttendanceUserDetail.setSource("2");  //来源:1-审批,2-录入，3-系统默认旷工
                                        personnelAttendanceUserDetail.setReason(null);
                                        personnelAttendanceUserDetail.setCreator(user.getUserID());
                                        personnelAttendanceUserDetail.setCreateName(user.getUserName());
                                        personnelAttendanceUserDetailDao.update(personnelAttendanceUserDetail);
                                    }
                                }
                            } else if (timeInterval > 0 && timeInterval <= lateLimitMills) {  //打卡时间-上班时间<=迟到时限  ,说明是迟到了
                                type = "2";//迟到
                                personnelAttendanceUserDetail = workAttendanceService.getPersonnelAttendanceUserDetail(user.getUserID(), NewDateUtils.today(), "2", null, "1");
                                if (personnelAttendanceUserDetail==null) {
                                    personnelAttendanceUserDetail = workAttendanceService.addPersonnelAttendanceUserDetail(beginTime, punchTime, "2", "2", null, null, null, null, py, user, null, null, "1", punchTime); //上班 2-迟到
                                    personnelAttendanceMonthlyService.SetLate(user, NewDateUtils.today(), true);
                                }
                            } else {
                                personnelAttendanceUserDetail = workAttendanceService.getPersonnelAttendanceUserDetail(user.getUserID(), NewDateUtils.today(), "1", null, "1");
                                if (personnelAttendanceUserDetail == null) {
                                    personnelAttendanceUserDetail = workAttendanceService.addPersonnelAttendanceUserDetail(punchTime, endTime, "1", "2", null, null, null, null, py, user, null, null, "1", punchTime); //正常上班
                                }
                            }
                            py.setAmAttendance(punchTime);//上午考勤时间
                            py.setBeginState(type);  //上班状态:0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
                        } else if (punchType.compareTo(PunchType.offWork.getIndex()) == 0) { //2--下班
                            String endStateOld = py.getEndState();  //原本的下班状态

                            Short earlyLimit = personnelAttendanceConfig.getEarlyLimit();  //早退时限(分钟)
                            long earlyLimitMills = earlyLimit * 60 * 1000; //将分钟转换成毫秒
                            long timeInterval = endTime.getTime() - punchTime.getTime();  //下班时间-打卡时间
                            if (timeInterval > earlyLimitMills) {  //下班时间-打卡时间>早退时限  ,说明是矿工
                                type = "7";//旷工
                                personnelAttendanceUserDetail = workAttendanceService.getPersonnelAttendanceUserDetail(user.getUserID(), NewDateUtils.today(), "7", null, "2");
                                if (personnelAttendanceUserDetail==null&&"0".equals(endStateOld)) {
                                    personnelAttendanceUserDetail = workAttendanceService.addPersonnelAttendanceUserDetail(punchTime, endTime, "7", "2", null, null, null, null, py, user, null, null, "2", punchTime); //正常上班
                                    personnelAttendanceMonthlyService.SetAbsenteeisme(user, NewDateUtils.today(), (byte) 1);  //添加一次旷工
                                }
                            } else if (timeInterval > 0 && timeInterval <= earlyLimitMills) {  //下班时间-打卡时间<=早退时限  ,说明是早退了
                                type = "3"; //早退
                                personnelAttendanceUserDetail = workAttendanceService.getPersonnelAttendanceUserDetail(user.getUserID(), NewDateUtils.today(), "3", null, null);
                                if (personnelAttendanceUserDetail==null&&"0".equals(endStateOld)) {
                                    personnelAttendanceUserDetail = workAttendanceService.addPersonnelAttendanceUserDetail(punchTime, endTime, "3", "2", null, null, null, null, py, user, null, null, "2", punchTime); //下班 2-早退
                                    personnelAttendanceMonthlyService.SetLeaveEarly(user, NewDateUtils.today(), true);
                                }
                            } else {  //正常下班
                                personnelAttendanceUserDetail = workAttendanceService.getPersonnelAttendanceUserDetail(user.getUserID(), NewDateUtils.today(), "1", null, "1");
                                if (personnelAttendanceUserDetail == null&&"0".equals(endStateOld)) {
                                    personnelAttendanceUserDetail = workAttendanceService.addPersonnelAttendanceUserDetail(beginTime, punchTime, "1", "2", null, null, null, null, py, user, null, null, "1",punchTime); //正常上班
                                }
                            }
                            if (!"0".equals(endStateOld)&&!endStateOld.equals(type)){  //下班已打卡了，并且此次打卡与上次打卡状态不一样
                                String state = null;  //状态:1-上班,2-下班
                                if ("7".equals(endStateOld)){
                                    state = "2";
                                    beginTime = punchTime; //将打卡时间赋给开始时间
                                    Byte absenteeismeNum = personnelAttendanceMonthlyService.GetAbsenteeisme(user, NewDateUtils.today());  //旷工次数
                                    personnelAttendanceMonthlyService.SetAbsenteeisme(user, NewDateUtils.today(), (byte)(absenteeismeNum-1));  //原本添加一次的旷工，减去
                                }else if ("3".equals(endStateOld)){
                                    beginTime = punchTime; //将打卡时间赋给开始时间
                                    personnelAttendanceMonthlyService.SetLeaveEarly(user, NewDateUtils.today(), false);
                                } else if ("1".equals(endStateOld)){
                                    endTime = punchTime;  //下班打卡正常的，将打卡时间赋给结束时间
                                }
                                //已经存在的,直接在原本的考勤详情里上改，这里可以改的
                                personnelAttendanceUserDetail = workAttendanceService.getPersonnelAttendanceUserDetail(user.getUserID(), NewDateUtils.today(), endStateOld, null, state);
                                personnelAttendanceUserDetail.setType(type);
                                personnelAttendanceUserDetail.setBeginTime(beginTime);
                                personnelAttendanceUserDetail.setEndTime(endTime);
                                personnelAttendanceUserDetailDao.update(personnelAttendanceUserDetail);
                            }

                            detailPunchType = DetailPunchType.end.getIndex(); //员工打卡明细对照信息类型 1-开始 2-结束
                            py.setEndState(type);  //此次下班打卡状态  下班状态:0-未考勤,1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班 9-其他
                            py.setPmAttendance(punchTime);//下午考勤时间

                            String pyType = py.getType().toString();  //原本的考勤总状态
                            if ("0".equals(endStateOld)||"0".equals(py.getBeginState())){  //原本下班未考勤打卡的,或者上班也未考勤打卡的
                                if ("0".equals(py.getType()) || "1".equals(py.getType()) || "8".equals(py.getType())) {
                                    py.setType(Byte.valueOf(type));
                                } else if (!"1".equals(type)&&!type.equals(py.getType())) {
                                    py.setType((byte)9);
                                }else if ("1".equals(type)&&!type.equals(py.getType())){
                                    py.setType(Byte.valueOf(pyType));
                                }else {
                                    py.setType(Byte.valueOf(type));
                                }
                            }else {  //下班已经打卡的，如果状态与上次打卡状态不对的，那么需要改考勤总状态
                                if ("1".equals(py.getType()) || "8".equals(py.getType())) {
                                    py.setType(Byte.valueOf(type));
                                }else if (!pyType.equals(type) && !endStateOld.equals(type)){  //此次打卡状态与考勤总状态和下班状态不同，才需要更改考勤总状态
                                    if ("1".equals(type)){  //此次打卡是正常的
                                        if ("9".equals(pyType)&&("3".equals(endStateOld)||"7".equals(endStateOld))){
                                            //是否有请假的
                                            PersonnelAttendanceUserDetail userDetailLeave = workAttendanceService.getPersonnelAttendanceUserDetail(user.getUserID(), NewDateUtils.today(), "5", leaveId, null);
                                            //是否有迟到的
                                            PersonnelAttendanceUserDetail userDetailLate= workAttendanceService.getPersonnelAttendanceUserDetail(user.getUserID(), NewDateUtils.today(), "2", null, "1");
                                            if (userDetailLeave!=null&&userDetailLate==null){
                                                py.setType((byte)5);
                                            }else if(userDetailLeave==null&&userDetailLate!=null){
                                                py.setType((byte)2);
                                            }else if (!endStateOld.equals(py.getBeginState()) && !"0".equals(py.getBeginState())){  //如果上次的下班状态和上班状态不同，这次下班状态正常，则直接取上班状态
                                                py.setType(Byte.valueOf(py.getBeginState()));
                                            }
                                        }else if ("3".equals(pyType)||"7".equals(pyType)){  //不等于9的
                                            py.setType(Byte.valueOf(py.getBeginState()));
                                        }
                                    }else {   //此次状态不是正常的
                                        if (!"9".equals(pyType) || type.equals(py.getBeginState())){  //不是第一次打卡的，且状态还不是复杂，就直接赋type
                                            py.setType(Byte.valueOf(type));
                                        }
                                    }
                                }
                            }
                        } else if (punchType.compareTo(PunchType.forenoon.getIndex()) == 0) { //3-午前
                            //午前、午后均按照正常上班查询，与需求沟通下
                            personnelAttendanceUserDetail = workAttendanceService.getPersonnelAttendanceUserDetail(user.getUserID(), NewDateUtils.today(), "1", null, null);
                            if (personnelAttendanceUserDetail == null) {
                                personnelAttendanceUserDetail = workAttendanceService.addPersonnelAttendanceUserDetail(beginTime, endTime, "1", "2", null, null, null, null, py, user, null, null, "1", new Date()); //正常上班
                            }
                        } else if (punchType.compareTo(PunchType.afternoon.getIndex()) == 0) { //4-午后
                            detailPunchType = DetailPunchType.end.getIndex(); //员工打卡明细对照信息类型 1-开始 2-结束
                            personnelAttendanceUserDetail = workAttendanceService.getPersonnelAttendanceUserDetail(user.getUserID(), NewDateUtils.today(), "1", null, null);
                            if (personnelAttendanceUserDetail == null) {
                                personnelAttendanceUserDetail = workAttendanceService.addPersonnelAttendanceUserDetail(beginTime, endTime, "1", "2", null, null, null, null, py, user, null, null, "2", new Date()); //正常上班
                            }
                        } else if (punchType.compareTo(PunchType.leavePost.getIndex()) == 0) { //5-离岗
                            PersonnelLeave personnelLeave = personnelLeaveDao.get(leaveId);
                            //离岗相当于下班，所以是早退或旷工(请假这里产生的早退、旷工、迟到，暂时不进入考勤那边的状态)
                            Short earlyLimit = personnelAttendanceConfig.getEarlyLimit();  //早退时限(分钟)
                            long earlyLimitMills = earlyLimit * 60 * 1000; //将分钟转换成毫秒
                            Date begin = personnelLeave.getActualBeginTime();
                            if (personnelLeave.getActualBeginTime().getTime()<=breakEnd.getTime()&&personnelLeave.getActualBeginTime().getTime()<breakBegin.getTime()) {
                                begin = breakBegin;  //请假开始时间是午休结束，那么午休时间段的打卡均属正常
                            }
//                            long timeInterval = personnelLeave.getActualBeginTime().getTime() - punchTime.getTime();  //请假开始时间-打卡时间
                            long timeInterval = begin.getTime() - punchTime.getTime();  //请假开始时间-打卡时间
                            if (timeInterval > earlyLimitMills) {  //请假开始时间-打卡时间>早退时限  ,说明是矿工
                                type = "7";//旷工
                            } else if (timeInterval > 0 && timeInterval <= earlyLimitMills) {  //请假开始时间-打卡时间<=早退时限  ,说明是早退了
                                type = "3"; //早退
                            }
                            //请假的那条明细
                            personnelAttendanceUserDetail = workAttendanceService.getPersonnelAttendanceUserDetail(user.getUserID(), NewDateUtils.today(), "5", leaveId, null);

                        } else if (punchType.compareTo(PunchType.returningToWork.getIndex()) == 0) { //6-返岗
                            detailPunchType = DetailPunchType.end.getIndex(); //员工打卡明细对照信息类型 1-开始 2-结束
                            PersonnelLeave personnelLeave = personnelLeaveDao.get(leaveId);
                            //返岗相当于上班，所以是迟到或者旷工(请假这里产生的早退、旷工、迟到，暂时不进入考勤那边的状态)
                            Short lateLimit = personnelAttendanceConfig.getLateLimit();  //迟到时限(分钟)
                            long lateLimitMills = lateLimit * 60 * 1000; //将分钟转换成毫秒
                            Date end = personnelLeave.getActualEndTime();
                            if (personnelLeave.getActualEndTime().getTime()>=breakBegin.getTime()&&personnelLeave.getActualEndTime().getTime()<breakEnd.getTime()) {
                                end = breakEnd; //请假结束时间是午休开始，那么午休时间段的打卡均属正常
                            }
//                            long timeInterval = punchTime.getTime() - personnelLeave.getActualEndTime().getTime();  //打卡时间-请假结束时间
                            long timeInterval = punchTime.getTime() - end.getTime();  //打卡时间-请假结束时间
                            if (timeInterval > lateLimitMills) {  //打卡时间-请假结束时间>迟到时限  ,说明是矿工
                                type = "7";//旷工
                            } else if (timeInterval > 0 && timeInterval <= lateLimitMills) {  //打卡时间-上班时间<=迟到时限  ,说明是迟到了
                                type = "2";//迟到
                            }
                            personnelAttendanceUserDetail = workAttendanceService.getPersonnelAttendanceUserDetail(user.getUserID(), NewDateUtils.today(), "5", leaveId, null);

                        } else if (punchType.compareTo(PunchType.beforeOvertime.getIndex()) == 0) { //7-加班前
                            PersonnelOvertime personnelOvertime = personnelOvertimeDao.get(overTimeId);
                            personnelAttendanceUserDetail = workAttendanceService.getPersonnelAttendanceUserDetail(user.getUserID(), NewDateUtils.today(), "8", overTimeId, null);
                            if (personnelAttendanceUserDetail == null) {
                                personnelAttendanceUserDetail = workAttendanceService.addPersonnelAttendanceUserDetail(personnelOvertime.getBeginTime(), personnelOvertime.getEndTime(), "8", "1", personnelOvertime.getType(), null, overTimeId, personnelOvertime.getReason(), py, user, String.valueOf(personnelOvertime.getDuration()), null, null, new Date()); //加班
                            }
                        } else if (punchType.compareTo(PunchType.afterOvertime.getIndex()) == 0) { //8-加班后
                            detailPunchType = DetailPunchType.end.getIndex(); //员工打卡明细对照信息类型 1-开始 2-结束
                            PersonnelOvertime personnelOvertime = personnelOvertimeDao.get(overTimeId);
                            personnelAttendanceUserDetail = workAttendanceService.getPersonnelAttendanceUserDetail(user.getUserID(), NewDateUtils.today(), "8", overTimeId, null);
                            if (personnelAttendanceUserDetail == null) {
                                personnelAttendanceUserDetail = workAttendanceService.addPersonnelAttendanceUserDetail(personnelOvertime.getBeginTime(), personnelOvertime.getEndTime(), "8", "1", personnelOvertime.getType(), null, overTimeId, personnelOvertime.getReason(), py, user, String.valueOf(personnelOvertime.getDuration()), null, null, new Date()); //加班
                            }
                        } else if (punchType.compareTo(PunchType.other.getIndex()) == 0) { //0-其它
                            personnelAttendanceUserDetail = workAttendanceService.getPersonnelAttendanceUserDetail(user.getUserID(), NewDateUtils.today(), "9", null, null);
                        }

                        py.setCreateName(user.getUserName());
                        py.setCreator(user.getUserID());
                        py.setUpdator(user.getUserID());
                        py.setUpdateName(user.getUserName());
                        py.setUpdateDate(new Date());
    //                    if ("0".equals(py.getType()) || "1".equals(py.getType()) || "8".equals(py.getType())) {
    //                        py.setType(type); //总类型：0-未考勤，1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,9-其他，8-加班(周六日加班的)，-1 无需考勤
    //                    }
                        personnelAttendanceUserDao.update(py);

                        //添加员工打卡信息
                        PersonnelAttendancePunch personnelAttendancePunch = new PersonnelAttendancePunch();
                        personnelAttendancePunch.setOrg(user.getOid());
                        personnelAttendancePunch.setUser(user.getUserID());
                        personnelAttendancePunch.setDept(StringUtils.isNotEmpty(user.getDepartment()) ? Integer.getInteger(user.getDepartment()) : null);
    //                    personnelAttendancePunch.setCardNo(cardNo);//打卡卡号
//                        personnelAttendancePunch.setAttendanceUser(user.getUserID()); //人员考勤ID
                        personnelAttendancePunch.setSysDevice(iotTerminalId); //打卡设备ID
                        personnelAttendancePunch.setPunchTerminal(personnelAttendanceTerminal.getId()); //人事打卡设备ID
                        personnelAttendancePunch.setPunchTime(punchTime);  //打卡时间
                        personnelAttendancePunch.setPunchType(punchType);  //打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
                        personnelAttendancePunch.setType(AttendanceType.getByName(type).getIndex()); //考勤类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班,0-其它
                        personnelAttendancePunch.setImage(image); //快照图片内容
                        personnelAttendancePunchDao.save(personnelAttendancePunch);

                        //员工考勤明细打卡对照信息
                        PersonnelAttendanceUserDetailPunch personnelAttendanceUserDetailPunch = new PersonnelAttendanceUserDetailPunch();
                        personnelAttendanceUserDetailPunch.setOrg(user.getOid());
                        personnelAttendanceUserDetailPunch.setUser(user.getUserID());
                        personnelAttendanceUserDetailPunch.setDetail(personnelAttendanceUserDetail.getId()); //明细id
                        personnelAttendanceUserDetailPunch.setPunch(personnelAttendancePunch.getId()); //打卡ID
                        personnelAttendanceUserDetailPunch.setType(detailPunchType);//类型:1-开始,2-结束
                        personnelAttendanceUserDetailPunch.setCreator(user.getUserID());
                        personnelAttendanceUserDetailPunch.setCreateName(user.getUserName());
                        personnelAttendanceUserDetailPunch.setCreateTime(punchTime);
                        personnelAttendanceUserDetailPunchDao.save(personnelAttendanceUserDetailPunch);

                        map.put("punchId", personnelAttendancePunch.getId());
                        map.put("status", "1");
                        map.put("content", "操作成功");
                    } else {
                        map.put("status", "0");
                        map.put("content", "操作失败，此职工或不参加考勤打卡");
                    }
                }else {
                    map.put("status", "0");
                    map.put("content", "操作失败，不是考勤宝模式");
                }
            } else {
                map.put("status", "2");
                map.put("content", "操作失败，因为今天休息！");
            }
        }else {
            map.put("status", "3");
            map.put("content", "不是本机构的打卡");
        }
        return map;
    }

    /**
     * 是否重复打卡
     * @param user
     * @param punchType :打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
     * @param attendanceDate 考勤日期
     * @return
     */
    public int repeatedClock(User user,Byte punchType,Date attendanceDate){
        int num = 0;  // 0-还未打卡  大于0的是已打卡
        if (punchType.compareTo(PunchType.goToWork.getIndex()) == 0) { //打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
            //type //类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班，9-其他，
            num = getAttendanceUserDetailAndDetailPunch(user.getUserID(), attendanceDate, "1", "1", punchType);
            if (0==num){
                num = getAttendanceUserDetailAndDetailPunch(user.getUserID(), attendanceDate, "2", "1", punchType); //迟到
            }
            if (0==num){
                num = getAttendanceUserDetailAndDetailPunch(user.getUserID(), attendanceDate, "7", "1", punchType); //旷工
            }
        } else if (punchType.compareTo(PunchType.offWork.getIndex()) == 0) { //2--下班
            num = getAttendanceUserDetailAndDetailPunch(user.getUserID(), attendanceDate, "1", "1", punchType);
            if (0==num){
                num = getAttendanceUserDetailAndDetailPunch(user.getUserID(), attendanceDate, "3", "1", punchType); //早退
            }
        } else if (punchType.compareTo(PunchType.forenoon.getIndex()) == 0 || punchType.compareTo(PunchType.afternoon.getIndex()) == 0) { //3-午前 4-午后
            //午前、午后均按照正常上班查询，与需求沟通下
            num = getAttendanceUserDetailAndDetailPunch(user.getUserID(), attendanceDate, "1", "1", punchType);
        } else if (punchType.compareTo(PunchType.leavePost.getIndex()) == 0 || punchType.compareTo(PunchType.returningToWork.getIndex()) == 0) { //5-离岗  6-返岗
            //请假的那条明细
            num = getAttendanceUserDetailAndDetailPunch(user.getUserID(), attendanceDate, "5", "1", punchType);

        } else if (punchType.compareTo(PunchType.beforeOvertime.getIndex()) == 0 || punchType.compareTo(PunchType.afterOvertime.getIndex()) == 0) { //7-加班前 8-加班后
            num = getAttendanceUserDetailAndDetailPunch(user.getUserID(), attendanceDate, "8", "1", punchType);

        } else if (punchType.compareTo(PunchType.other.getIndex()) == 0) { //0-其它
            num = getAttendanceUserDetailAndDetailPunch(user.getUserID(), attendanceDate, "9", "1", punchType);
        }
        return num;
    }

//    @Override
//    public PersonnelAttendanceUserDetail getPersonnelAttendanceUserDetail(Integer user,Date attendanceDate,String type,Integer business,String state){
//        Map<String,Object> map = new HashMap<>();
//        String hql = "from PersonnelAttendanceUserDetail where user=:user";
//        map.put("user",user);
//        if (attendanceDate!=null){
//            hql+=" and attendanceDate=:attendanceDate";
//            map.put("attendanceDate",attendanceDate);
//        }
//        if (StringUtils.isNotEmpty(type)){
//            hql+=" and type=:type";
//            map.put("type",type);
//        }
//        if (business!=null){
//            hql+=" and business=:business";
//            map.put("business",business);
//        }
//        if (StringUtils.isNotEmpty(state)){
//            hql+=" and state=:state";
//            map.put("state",state);
//        }
//        PersonnelAttendanceUserDetail personnelAttendanceUserDetail = (PersonnelAttendanceUserDetail) personnelAttendanceUserDetailDao.getByHQLWithNamedParams(hql,map);
//        return personnelAttendanceUserDetail;
//    }

    @Override
    public int getAttendanceUserDetailAndDetailPunch(Integer user,Date attendanceDate,String type,String state,Byte punchType){
        Map<String,Object> map = new HashMap<>();
        String hql = "select count(pap.id) from PersonnelAttendancePunch pap where pap.id in (select dp.punch from PersonnelAttendanceUserDetailPunch dp,PersonnelAttendanceUserDetail d where d.user=:user and d.id=dp.detail";
        map.put("user",user);
        if (attendanceDate!=null){
            hql+=" and d.attendanceDate=:attendanceDate";
            map.put("attendanceDate",attendanceDate);
        }
        if (StringUtils.isNotEmpty(type)){
            hql+=" and d.type=:type";
            map.put("type",type);
        }
        if (StringUtils.isNotEmpty(state)){
            hql+=" and d.state=:state";
            map.put("state",state);
        }
        hql+=")";
        if (StringUtils.isNotEmpty(state)){
            hql+=" and pap.punchType=:punchType"; //打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
            map.put("punchType",punchType);
        }
        Long num = (Long) personnelAttendanceUserDetailDao.getByHQLWithNamedParams(hql,map);
        return num!=null?num.intValue():0;
    }

    @Override
    public Map<String, Object> clockRecord(User user, String attendanceDate) {
        Map<String,Object> map = new HashMap<>();
        List<Map<String, Object>> goToWorkList = new ArrayList<>(); //打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
        List<Map<String, Object>> offWorkList = new ArrayList<>();  //2--下班
        List<Map<String, Object>> forenoonList = new ArrayList<>(); //3-午前
        List<Map<String, Object>> afternoonList = new ArrayList<>();
        List<Map<String, Object>> leaveList = new ArrayList<>();
        List<Map<String, Object>> overtimeList = new ArrayList<>();
        List<Map<String, Object>> otherList = new ArrayList<>();

        Date queryDate = NewDateUtils.today(); //默认
        if (StringUtils.isNotEmpty(attendanceDate)){
            queryDate = NewDateUtils.dateFromString(attendanceDate,"yyyy-MM-dd");
        }
        List<Map<String, Object>> punchList = getClockRecordList(null,user.getUserID(),queryDate,null,null,null,new Date());
        for (Map<String, Object> map1:punchList) {
            Byte punchType = (Byte) map1.get("punchType"); //打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
            if (punchType.equals(PunchType.goToWork.getIndex())){
                goToWorkList.add(map1);
            }else if (punchType.equals(PunchType.offWork.getIndex())){
                offWorkList.add(map1);
            }if (punchType.equals(PunchType.forenoon.getIndex())){
                forenoonList.add(map1);
            }if (punchType.equals(PunchType.afternoon.getIndex())){
                afternoonList.add(map1);
            }if (punchType.equals(PunchType.other.getIndex())){
                otherList.add(map1);
            }
        }

        //请假的
        List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetailList = this.getPersonnelAttendanceUserDetailByUserId(user.getUserID(), null, queryDate, "5", null);
        for (PersonnelAttendanceUserDetail pd:personnelAttendanceUserDetailList) {
            if (pd.getBusiness()!=null){  //审批的可以有打卡
                Map<String, Object> map2 = new HashMap<>();
                map2.put("leaveId",pd.getBusiness());
                List<Map<String, Object>> punchList1 = getClockRecordList(null,user.getUserID(),queryDate,null,pd.getId(),"5",new Date());
                List<Map<String, Object>> leavePostList = new ArrayList<>();
                List<Map<String, Object>> returningToWorkList = new ArrayList<>();
                for (Map<String, Object> map3:punchList1) {
                    Byte punchType = (Byte) map3.get("punchType"); //打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
                    if (punchType.equals(PunchType.leavePost.getIndex())){
                        leavePostList.add(map3);
                    }if (punchType.equals(PunchType.returningToWork.getIndex())){
                        returningToWorkList.add(map3);
                    }
                }
                if (leavePostList.size()>0||returningToWorkList.size()>0) {
                    map2.put("leavePostList", leavePostList);
                    map2.put("returningToWorkList", returningToWorkList);
                    leaveList.add(map2);
                }
            }
        }

        //加班的
        List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetailList1 = this.getPersonnelAttendanceUserDetailByUserId(user.getUserID(), null, queryDate, "8", null);
        for (PersonnelAttendanceUserDetail pd:personnelAttendanceUserDetailList1) {
            if (pd.getBusiness()!=null){  //审批的可以有打卡
                Map<String, Object> map2 = new HashMap<>();
                map2.put("overtimeId",pd.getBusiness());
                List<Map<String, Object>> punchList1 = getClockRecordList(null,user.getUserID(),queryDate,null,pd.getId(),"8",new Date());
                List<Map<String, Object>> beforeOvertimeList = new ArrayList<>();
                List<Map<String, Object>> afterOvertimeList = new ArrayList<>();
                for (Map<String, Object> map3:punchList1) {
                    Byte punchType = (Byte) map3.get("punchType"); //打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
                    if (punchType.equals(PunchType.beforeOvertime.getIndex())){
                        beforeOvertimeList.add(map3);
                    }if (punchType.equals(PunchType.afterOvertime.getIndex())){
                        afterOvertimeList.add(map3);
                    }
                }
                if (beforeOvertimeList.size()>0||afterOvertimeList.size()>0) {
                    map2.put("beforeOvertimeList", beforeOvertimeList);
                    map2.put("afterOvertimeList", afterOvertimeList);
                    overtimeList.add(map2);
                }
            }
        }
        map.put("goToWorkList",goToWorkList);
        map.put("offWorkList",offWorkList);
        map.put("forenoonList",forenoonList);
        map.put("afternoonList",afternoonList);
        map.put("leaveList",leaveList);
        map.put("overtimeList",overtimeList);
        map.put("otherList",otherList);
        return map;
    }

    @Override
    public List<Map<String, Object>> getClockRecordList(Integer org,Integer userId,Date attendanceDate,Byte punchType,Integer personnelAttendanceUserDetailId,String type,Date punchTime) {
        Map<String,Object> map = new HashMap<>();
        String hql = "select paup.punch from PersonnelAttendanceUserDetailPunch paup,PersonnelAttendanceUserDetail pau where pau.attendanceDate=:attendanceDate and pau.id=paup.detail";
        map.put("attendanceDate",attendanceDate);
        if (org!=null) {
            hql+=" and pau.org=:org";
            map.put("org",org);
        }
        if (userId!=null) {
            hql+=" and pau.user=:user";
            map.put("user",userId);
        }
        if (personnelAttendanceUserDetailId!=null){
            hql+=" and pau.id=:personnelAttendanceUserDetailId";
            map.put("personnelAttendanceUserDetailId",personnelAttendanceUserDetailId);
        }
        if (StringUtils.isNotEmpty(type)){
            hql+=" and pau.type=:type";
            map.put("type",type);
        }
        List<Integer> punchIds = personnelAttendanceUserDetailPunchDao.getListByHQLWithNamedParams(hql,map);
        map.clear();

        List<Map<String, Object>> punchList = new ArrayList<>();
        if (punchIds.size()>0) {
            String hql1 = "select pp.id,pp.punchTime,pp.punchType,pp.type,it.sn,md.brand,md.model,pp.punchTerminal from PersonnelAttendancePunch pp,IotTerminal it,PersonnelAttendanceTerminal md where pp.id in (:punchIds) and ((pp.sysDevice=it.id and pp.punchTerminal=md.id) or pp.sysDevice is null)";
            map.put("punchIds", punchIds);
            if (punchType!=null){
                hql1+=" and pp.punchType=:punchType"; //打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
                map.put("punchType",punchType);
            }
            if (punchTime!=null){
                hql1+=" and pp.punchTime<=:punchTime"; //打卡时间
                map.put("punchTime",punchTime);
            }
            hql1+=" group by pp.id";
            List<Object[]> punchs = personnelAttendancePunchDao.getListByHQLWithNamedParams(hql1, map);

            for (Object[] ob:punchs) {
                Map<String, Object> map2 = new HashMap<>();
                map2.put("id", ob[0]); //打卡id
                map2.put("punchTime", ob[1]); //打卡时间
                map2.put("punchType", ob[2]); //打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
                map2.put("type", ob[3]); //考勤类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班,0-其它
                map2.put("sn", ob[4]); //编号
                map2.put("brand", ob[5]); //品牌
                map2.put("model", ob[6]); //型号
                Integer punchTerminal = (Integer) ob[7];
                if (punchTerminal==null){
                    map2.put("brand", ""); //品牌
                    map2.put("model", ""); //型号
                }
                punchList.add(map2);
            }
        }
        return punchList;
    }

    @Override
    public Map<String, Object> clockRecordDetail(User user, Integer punchId) {
        Map<String,Object> map = new HashMap<>();
        PersonnelAttendancePunch personnelAttendancePunch = personnelAttendancePunchDao.get(punchId); //打卡信息
        IotTerminal iotTerminal = new IotTerminal();
        if (personnelAttendancePunch.getSysDevice()!=null) {  //默认打卡的没有打卡设备
            iotTerminal = iotTerminalDao.get(personnelAttendancePunch.getSysDevice()); //考勤设备信息
        }
        PersonnelAttendanceTerminal mobileDevice = new PersonnelAttendanceTerminal();
        if (personnelAttendancePunch.getPunchTerminal()!=null) {
            mobileDevice = personnelAttendanceTerminalDao.get(personnelAttendancePunch.getPunchTerminal()); //人事打卡设备
        }
        User clockUser = userDao.get(personnelAttendancePunch.getUser());
        map.put("personnelAttendancePunch",personnelAttendancePunch);
        map.put("iotTerminal",iotTerminal);
        map.put("mobileDevice",mobileDevice);
        map.put("userName",clockUser.getUserName());
        return map;
    }

    @Override
    public Map<String, Object> modelRecord(User user) {
        Map<String,Object> map = new HashMap<>();
//        List<PersonnelAttendanceRecord> personnelAttendanceRecords = this.getPersonnelAttendanceRecordByOid(user.getOid(),null,null,null); //查询考勤设置的修改记录
        String hql = "select pr.id,ph.attendancePattern,pr.effectDate,pr.createName,pr.createDate,pr.updateName,pr.updateDate from PersonnelAttendanceRecord pr,PersonnelAttendanceConfigHistory ph where pr.patternType=true and pr.org=:org and pr.id=ph.record group by pr.id order by pr.effectDate";
        map.put("org",user.getOid());
        List<Object[]> objects = personnelAttendanceRecordDao.getListByHQLWithNamedParams(hql,map);
        List<Map<String,Object>> modelRecord = new ArrayList<>();
        for (Object[] ob:objects) {
            Map<String,Object> map1 = new HashMap<>();
            map1.put("id",ob[0]); //历史规则id
            Byte attendancePattern = (Byte) ob[1];
            if (ConfigAttendancePattern.attendanceTreasure.getIndex().equals(attendancePattern)){
                map1.put("patternName","考勤宝"); //系统模式
            }else {
                map1.put("patternName","手动录入"); //系统模式
            }
            map1.put("effectDate",ob[2]); //启用日期
            map1.put("createName",ob[3]); //创建人
            map1.put("createDate",ob[4]); //创建日期
            map1.put("updateName",ob[5]); //修改人
            map1.put("updateDate",ob[6]); //修改日期
            modelRecord.add(map1);
        }
        map.clear();
        map.put("modelRecord",modelRecord);
        return map;
    }

    @Override
    public Map<String, Object> modelRecordDetail(User user,Integer historyId) {
        Map<String,Object> map = new HashMap<>();
//        PersonnelAttendanceRecord personnelAttendanceRecord = personnelAttendanceRecordDao.get(historyId);
        String hql = "from PersonnelAttendanceConfigHistory where record=:record";
        map.put("record",historyId);
        List<PersonnelAttendanceConfigHistory> personnelAttendanceConfigHistories = personnelAttendanceConfigHistoryDao.getListByHQLWithNamedParams(hql,map);

        Byte attendancePattern = null;
        List<Map<String,Object>> mapList = new ArrayList<>();
        for (PersonnelAttendanceConfigHistory ph:personnelAttendanceConfigHistories) {
            Map<String,Object> map1 = new HashMap<>();
//            map1.put("attendancePattern",ph.getAttendancePattern()); //考勤模式:1-考勤宝,2-手工录入
            map1.put("beginTime",NewDateUtils.dateToString(ph.getBeginTime(), "HH:mm")); //上班时间
            map1.put("endTime",NewDateUtils.dateToString(ph.getEndTime(), "HH:mm")); //下班时间
            map1.put("breakBegin",NewDateUtils.dateToString(ph.getBreakBegin(), "HH:mm")); //午休开始时间
            map1.put("breakEnd",NewDateUtils.dateToString(ph.getBreakEnd(), "HH:mm")); //午休结束时间
            map1.put("break",ph.isBreak()); //午休是否考勤
            map1.put("lateLimit",ph.getLateLimit()); //迟到时限(分钟)
            map1.put("earlyLimit",ph.getEarlyLimit()); //早退时限(分钟)
            map1.put("leaveWork",ph.getLeaveWork()); //请假到岗是否使用考勤宝:0-不使用(默认),1-使用

            String deptNames = ""; //部门名称
            Set<PersonnelAttendanceDepartmentConfigHistory> dhs = ph.getPersonnelAttendanceDepartmentConfigHistoryHashSet();
            for (PersonnelAttendanceDepartmentConfigHistory dh:dhs) {
                if (dh.getDept()!=null && dh.getDept()==0){
//                    dh.setDeptName("其他");
                    deptNames = StringUtils.isNotEmpty(deptNames)?deptNames+","+"其他":"其他";
                }else {
                    Organization org = orgService.getOrgByOid(dh.getDept(), OrgService.OrgType.department);
                    if (org!=null){
                        if (!MyStrings.nulltoempty(org.getName()).isEmpty()){
//                            dh.setDeptName(org.getName());
                            deptNames = StringUtils.isNotEmpty(deptNames)?deptNames+","+org.getName():org.getName();
                        }
                    }
                }
            }
            map1.put("deptNames",deptNames); //部门名称
            mapList.add(map1);

            if (attendancePattern==null){
                attendancePattern = ph.getAttendancePattern();  //考勤模式:1-考勤宝,2-手工录入
            }
        }
        map.clear();
        map.put("modelRecordDetail",mapList);  //考勤规则信息

        if (ConfigAttendancePattern.attendanceTreasure.getIndex().equals(attendancePattern)){  //考勤宝的
            List<IotTerminal> iotTerminals = iotService.attendanceDevices(user,1);
            map.put("iotTerminals",iotTerminals);  //设备信息
        }
        return map;
    }

    @Override
    public Map<String, Object> updateLimit(User user, Short lateLimit, Short earlyLimit, Boolean leaveWork, Integer configId) {
        Map<String,Object> map = new HashMap<>();
        PersonnelAttendanceConfig personnelAttendanceConfig = personnelAttendanceConfigDao.get(configId);
        List<PersonnelAttendanceConfig> personnelAttendanceConfigs = workAttendanceService.getPersonnelAttendanceConfigList(user.getOid(),"1",personnelAttendanceConfig.getOpenDate());

        //明天有修改要生成的考勤设置
        List<PersonnelAttendanceRecord> personnelAttendanceRecordLasts = getPersonnelAttendanceRecord(user.getOid(), NewDateUtils.tomorrow());
        if (personnelAttendanceRecordLasts.size()>0){
            map.put("content", "明天有新的考勤设置");
        }else {
            PersonnelAttendanceRecord record = new PersonnelAttendanceRecord();
            try {
                record = addPersonnelAttendanceRecord(user, NewDateUtils.dateToString(NewDateUtils.tomorrow(), "yyyy-MM-dd"), "4",false);  //考勤设置修改记录表
            } catch (ParseException e) {
                e.printStackTrace();
            }

            Byte operation = AttendanceConfigOperation.modifyLateLeave.getIndex();  //5-修改迟到早退和请假期设置
            for (int i = 0; i < personnelAttendanceConfigs.size(); i++) {
                PersonnelAttendanceConfig configOld = personnelAttendanceConfigs.get(i);

                PersonnelAttendanceConfig personnelAttendanceConfig1 = new PersonnelAttendanceConfig();
                if (leaveWork==null) {
                    leaveWork = Boolean.FALSE;
                }

                String departmentIds = "";
                for (PersonnelAttendanceDepartmentConfig pdc : configOld.getPersonnelAttendanceDepartmentConfigHashSet()) {
//                    workAttendanceOldService.addPersonnelAttendanceDepartmentConfig(pdc.getDept().toString(), user, personnelAttendanceConfig1, null, 1);
                    departmentIds=departmentIds+pdc.getDept().toString()+",";
                }

                personnelAttendanceConfig1 = workAttendanceService.addPersonnelAttendanceConfig(user,configOld.getInputTime(),configOld.getBeginTime(),configOld.getEndTime(),NewDateUtils.tomorrow(),configOld.getType(),
                        Boolean.TRUE.equals(configOld.getMiddleBreak()),configOld.getBreakBegin(),configOld.getBreakEnd(),operation,configOld.getAttendancePattern(),lateLimit,earlyLimit,leaveWork,record.getId(),departmentIds);

                PersonnelAttendanceConfigHistory personnelAttendanceConfigHistory = workAttendanceOldService.pcToPH(personnelAttendanceConfig1, configOld, user, record, operation);//将原来的修改记录保存到历史表中
                pdcToPdch(user, configOld, personnelAttendanceConfigHistory);//将原来的考勤部门添加到考勤部门历史表中
//                this.updatePersonnelAttendanceConfig(user,2,configOld,personnelAttendanceConfig1,record.getId(),operation,null,null);
            }
            map.put("content", "操作成功");
        }
        return map;
    }

//    @Override
//    public Map<String, Object> initWorkHoursAndLeaveDuration(PrintWriter writer) {
//        responseWriteln(writer, "任务开始：" + NewDateUtils.dateToString(System.currentTimeMillis(), sdf));
//        long start = System.currentTimeMillis();
//        int lastSaveCount = 0, saveCount = 0, monthlyIndex = 0;
//        List<String> errorInfo = new ArrayList<>();
////        List<Organization> organizations = orgPopedomService.getOrgPopedomByMid("kj");//有考勤权限的机构
////        List<Integer> oids = organizations.stream().map(Organization::getId).collect(Collectors.toList());
////        responseWriteln(writer, "orgCount="+oids.size());
//        List<PersonnelAttendanceMonthly> monthlies = personnelAttendanceMonthlyService.getAllAttendanceMonthliesForInit();//按机构，人，时间排序
//        int beginYearmonth = monthlies.stream().max(Comparator.comparingInt(PersonnelAttendanceMonthly::getYearmonth)).get().getYearmonth();
//        Date beginDate = NewDateUtils.dateFromString(beginYearmonth+"01","yyyyMMdd");
//        Date endDate = NewDateUtils.today();
//        responseWriteln(writer, "monthlySize=" + monthlies.size());
//        int oid = Integer.MIN_VALUE;
//        int uid = Integer.MIN_VALUE;
//        User user = null;
//        Map<Date, PersonnelAttendanceUser> attendanceUserMap = null;
//        String methodName = null, lockKey = null;
//        Triple<List<Date>, Set<Date>, Set<Date>> workingDates = Triple.of(new ArrayList<>(0), new HashSet<>(0), new HashSet<>(0));  //left 所有, middle 班, right假,
//
//        PersonnelAttendanceUser attendanceUser;
//        Transaction transaction = sessionFactory.getCurrentSession().getTransaction();
//        boolean commitBegin;
//        if (transaction.getStatus().equals(TransactionStatus.NOT_ACTIVE)) {
//            commitBegin = false;
//            transaction.begin();
//        } else {
//            commitBegin = true;
//        }
//        for (PersonnelAttendanceMonthly monthly : monthlies) {
//            monthlyIndex++;
//            if (monthly.getWorkingHours().equals(0.0F) && monthly.getLeaveDuration().equals(0.0F)) {
//                if (monthly.getOrg() != null) {
//                    if(uid!=monthly.getUser().intValue()) {//user改了
//                        attendanceUserMap = null;
//                        if(saveCount!=lastSaveCount) {
//                            transaction.commit();
//                            transaction.begin();
//                            lastSaveCount = saveCount;
//                        }
//                        responseWriteln(writer, NewDateUtils.dateToString(System.currentTimeMillis(), sdf) + " saveCount / monthlyIndex / monthlyCount: " + saveCount + " / " + monthlyIndex + " / " + monthlies.size());
//                        user = userService.getUserByID(monthly.getUser());
//                        if (user == null) {
//                            String message = "PersonnelAttendanceMonthly 数据错误，找不到User, id=" + monthly.getId() + " UserId= " + uid;
//                            errorInfo.add(message);
//                            responseWriteln(writer, "errorMessage：" + message);
//                            logger.error(message, new RuntimeException());
//                            continue;
//                        }
//                        if(oid != user.getOid()) {//org改了
////                            if(lockKey!=null && methodName!=null) {//如果有机构锁，先释放；
////                                dlmService.releaseLock(methodName, lockKey);
////                                responseWriteln(writer, NewDateUtils.dateToString(System.currentTimeMillis(), sdf) + "释放机构锁 Oid = " + methodName);
////                                oid = Integer.MIN_VALUE;
////                            }
//                            methodName = Thread.currentThread().getStackTrace()[1].getMethodName() + ":" +user.getOid();
//                            if((lockKey = dlmService.getLock(methodName, TimeUnit.HOURS.toMillis(5)))==null) {
//                                responseWriteln(writer, NewDateUtils.dateToString(System.currentTimeMillis(), sdf) + "没取到新机构锁 Oid = " + methodName);
//                                user = null;//没取到新机构锁
//                                continue;
//                            }
//                            oid = user.getOid();
//                            responseWriteln(writer, NewDateUtils.dateToString(System.currentTimeMillis(), sdf) + " Oid=" + oid);
//                            workingDates = getWorkingDates(beginDate, endDate, oid);//left 总, middle 班, right假,
//                        }
//                        uid = monthly.getUser();
//                        responseWriteln(writer, NewDateUtils.dateToString(System.currentTimeMillis(), sdf) + " UserId=" + uid);
//                        List<PersonnelAttendanceUser> attendanceUsers = getPersonnelAttendanceUser(null, null, Arrays.asList(uid), null, null, Arrays.asList("0","1","2","3","4","5","6","7","9"));
//                        attendanceUserMap = attendanceUsers.stream().collect(Collectors.toMap(PersonnelAttendanceUser::getAttendanceDate, Function.identity(), (key1, key2) -> key2));
//                        responseWriteln(writer, NewDateUtils.dateToString(System.currentTimeMillis(), sdf) + " attendanceUserMap finished, size: " + attendanceUsers.size() +" Map size "+ attendanceUserMap.size());
//                    }
//                    if (user != null) {
//                        Date date = NewDateUtils.dateFromString(monthly.getYearmonth() + "01", "yyyyMMdd");
//                        Long end = NewDateUtils.getLastTimeOfMonth(date).getTime();
//                        for (; date.getTime() < end; date = NewDateUtils.changeDay(date, 1)) {   //遍历两个时间之间的时间
//                            if ((attendanceUser=attendanceUserMap.get(date)) != null) {//考勤人员上班
//                                personnelAttendanceMonthlyService.SetWorkingHours(user, date, attendanceUser.GetWorkHours().floatValue());
//                                saveCount++;
//                                List<PersonnelAttendanceUserDetail> details = getPersonnelAttendanceUserDetailByUserId(user.getUserID(), null, NewDateUtils.today(date), "5", "1");
////                                List<PersonnelLeave> personnelLeaves = leaveService.getPersonnelLeaveListByTime(user.getUserID(), NewDateUtils.today(date), NewDateUtils.getLastTimeOfDay(date));
//                                if (details.size() > 0) {
//                                    //wyu：大于0的duration求和
//                                    Double duration = getDurationFromDetails(attendanceUser, details);
//                                    personnelAttendanceMonthlyService.SetLeave(user, date, (byte) details.size(), duration.floatValue());
//                                    saveCount++;
//                                }
//                                //考勤人员假或者无需考勤人员班
//                            } else if(workingDates.getMiddle().contains(date)) {//班
//                                personnelAttendanceMonthlyService.SetWorkingHours(user, date, Float.valueOf(8.0F));
//                            }
//                        }
//                    }
//                    responseWriteln(writer, "saveCount=" + saveCount + "<br />");
//                } else {//数据错误，没有机构id；
//                    String message = "PersonnelAttendanceMonthly 数据错误，机构id为空, id=" + monthly.getId();
//                    errorInfo.add(message);
//                    responseWriteln(writer, "errorMessage：" + message);
//                    logger.error(message, new RuntimeException());
//                }
//            } else {
//                responseWriteln(writer, NewDateUtils.dateToString(System.currentTimeMillis(), sdf) + " saveCount / monthlyIndex / monthlyCount: " + saveCount + " / " + monthlyIndex + " / " + monthlies.size());
//                responseWriteln(writer,Boolean.valueOf(monthly.getWorkingHours().equals(0.0F) && monthly.getLeaveDuration().equals(0.0F)).toString() + ", PersonnelAttendanceMonthly 改过了，monthly id=" + monthly.getId() + " userId=" + monthly.getUser() + "monthly.getWorkingHours()=" +monthly.getWorkingHours().toString() + "monthly.getLeaveDuration()=" + monthly.getLeaveDuration().toString());
//            }
//        }
//        if(lockKey!=null && methodName!=null) {//如果有机构锁，先释放；
//            dlmService.releaseLock(methodName, lockKey);
//        }
//        if (!commitBegin && transaction.getStatus().equals(TransactionStatus.ACTIVE)) {
//            transaction.commit();
//        }
//        responseWriteln(writer, "saveCount / monthlyIndex / monthlyCount: " + saveCount + " / " + monthlyIndex + " / " + monthlies.size());
//        HashMap<String, Object> result = new HashMap<>(4);
//        result.put("monthlySize", monthlies.size());
//        result.put("saveCount", saveCount);
//        result.put("duration", TimeUtils.toTimeString2sf(System.currentTimeMillis() - start));
//        result.put("errorInfo", errorInfo);
//        responseWriteln(writer, "任务完成：" + JSON.toJSONString(result));
//        responseWriteln(writer, "完成时间：" + NewDateUtils.dateToString(System.currentTimeMillis(), sdf));
//        return result;
//    }

//    private void responseWriteln(PrintWriter writer, String message) {
//        if(writer!=null) {
//            writer.println(message + "<br />");
//            writer.flush();
//        } else {
//            logger.warn(message);
//        }
//    }

//    @Autowired
//    SessionFactory sessionFactory;
//    @Autowired
//    DlmService dlmService;
//    @Override
//    public Map<String, Object> initWorkHoursAndLeaveDuration(PrintWriter writer) {
//        responseWriteln(writer, "任务开始：" + NewDateUtils.dateToString(System.currentTimeMillis(), sdf));
//        long start = System.currentTimeMillis();
//        int lastSaveCount = 0, saveCount = 0, monthlyIndex = 0;
//        List<String> errorInfo = new ArrayList<>();
////        List<Organization> organizations = orgPopedomService.getOrgPopedomByMid("kj");//有考勤权限的机构
////        List<Integer> oids = organizations.stream().map(Organization::getId).collect(Collectors.toList());
////        responseWriteln(writer, "orgCount="+oids.size());
//        List<PersonnelAttendanceMonthly> monthlies = personnelAttendanceMonthlyService.getAllAttendanceMonthliesForInit();//按机构，人，时间排序
//        int beginYearmonth = monthlies.stream().max(Comparator.comparingInt(PersonnelAttendanceMonthly::getYearmonth)).get().getYearmonth();
//        Date beginDate = NewDateUtils.dateFromString(beginYearmonth+"01","yyyyMMdd");
//        Date endDate = NewDateUtils.today();
//        responseWriteln(writer, "monthlySize=" + monthlies.size());
//        int oid = Integer.MIN_VALUE;
//        int uid = Integer.MIN_VALUE;
//        User user = null;
//        Map<Date, PersonnelAttendanceUser> attendanceUserMap = null;
//        String methodName = null, lockKey = null;
//        Triple<List<Date>, Set<Date>, Set<Date>> workingDates = Triple.of(new ArrayList<>(0), new HashSet<>(0), new HashSet<>(0));  //left 所有, middle 班, right假,
//
//        PersonnelAttendanceUser attendanceUser;
//        Transaction transaction = sessionFactory.getCurrentSession().getTransaction();
//        boolean commitBegin;
//        if (transaction.getStatus().equals(TransactionStatus.NOT_ACTIVE)) {
//            commitBegin = false;
//            transaction.begin();
//        } else {
//            commitBegin = true;
//        }
//        for (PersonnelAttendanceMonthly monthly : monthlies) {
//            monthlyIndex++;
//            if (monthly.getWorkingHours().equals(0.0F) && monthly.getLeaveDuration().equals(0.0F)) {
//                if (monthly.getOrg() != null) {
//                    if(uid!=monthly.getUser().intValue()) {//user改了
//                        attendanceUserMap = null;
//                        if(saveCount!=lastSaveCount) {
//                            transaction.commit();
//                            transaction.begin();
//                            lastSaveCount = saveCount;
//                        }
//                        responseWriteln(writer, NewDateUtils.dateToString(System.currentTimeMillis(), sdf) + " saveCount / monthlyIndex / monthlyCount: " + saveCount + " / " + monthlyIndex + " / " + monthlies.size());
//                        user = userService.getUserByID(monthly.getUser());
//                        if (user == null) {
//                            String message = "PersonnelAttendanceMonthly 数据错误，找不到User, id=" + monthly.getId() + " UserId= " + uid;
//                            errorInfo.add(message);
//                            responseWriteln(writer, "errorMessage：" + message);
//                            logger.error(message, new RuntimeException());
//                            continue;
//                        }
//                        if(oid != user.getOid()) {//org改了
////                            if(lockKey!=null && methodName!=null) {//如果有机构锁，先释放；
////                                dlmService.releaseLock(methodName, lockKey);
////                                responseWriteln(writer, NewDateUtils.dateToString(System.currentTimeMillis(), sdf) + "释放机构锁 Oid = " + methodName);
////                                oid = Integer.MIN_VALUE;
////                            }
//                            methodName = Thread.currentThread().getStackTrace()[1].getMethodName() + ":" +user.getOid();
//                            if((lockKey = dlmService.getLock(methodName, TimeUnit.HOURS.toMillis(5)))==null) {
//                                responseWriteln(writer, NewDateUtils.dateToString(System.currentTimeMillis(), sdf) + "没取到新机构锁 Oid = " + methodName);
//                                user = null;//没取到新机构锁
//                                continue;
//                            }
//                            oid = user.getOid();
//                            responseWriteln(writer, NewDateUtils.dateToString(System.currentTimeMillis(), sdf) + " Oid=" + oid);
//                            workingDates = getWorkingDates(beginDate, endDate, oid);//left 总, middle 班, right假,
//                        }
//                        uid = monthly.getUser();
//                        responseWriteln(writer, NewDateUtils.dateToString(System.currentTimeMillis(), sdf) + " UserId=" + uid);
//                        List<PersonnelAttendanceUser> attendanceUsers = getPersonnelAttendanceUser(null, null, Arrays.asList(uid), null, null, Arrays.asList("0","1","2","3","4","5","6","7","9"));
//                        attendanceUserMap = attendanceUsers.stream().collect(Collectors.toMap(PersonnelAttendanceUser::getAttendanceDate, Function.identity(), (key1, key2) -> key2));
//                        responseWriteln(writer, NewDateUtils.dateToString(System.currentTimeMillis(), sdf) + " attendanceUserMap finished, size: " + attendanceUsers.size() +" Map size "+ attendanceUserMap.size());
//                    }
//                    if (user != null) {
//                        Date date = NewDateUtils.dateFromString(monthly.getYearmonth() + "01", "yyyyMMdd");
//                        Long end = NewDateUtils.getLastTimeOfMonth(date).getTime();
//                        for (; date.getTime() < end; date = NewDateUtils.changeDay(date, 1)) {   //遍历两个时间之间的时间
//                            if ((attendanceUser=attendanceUserMap.get(date)) != null) {//考勤人员上班
//                                personnelAttendanceMonthlyService.SetWorkingHours(user, date, attendanceUser.GetWorkHours().floatValue());
//                                saveCount++;
//                                List<PersonnelAttendanceUserDetail> details = getPersonnelAttendanceUserDetailByUserId(user.getUserID(), null, NewDateUtils.today(date), "5", "1");
////                                List<PersonnelLeave> personnelLeaves = leaveService.getPersonnelLeaveListByTime(user.getUserID(), NewDateUtils.today(date), NewDateUtils.getLastTimeOfDay(date));
//                                if (details.size() > 0) {
//                                    //wyu：大于0的duration求和
//                                    Double duration = getDurationFromDetails(attendanceUser, details);
//                                    personnelAttendanceMonthlyService.SetLeave(user, date, (byte) details.size(), duration.floatValue());
//                                    saveCount++;
//                                }
//                                //考勤人员假或者无需考勤人员班
//                            } else if(workingDates.getMiddle().contains(date)) {//班
//                                personnelAttendanceMonthlyService.SetWorkingHours(user, date, Float.valueOf(8.0F));
//                            }
//                        }
//                    }
//                    responseWriteln(writer, "saveCount=" + saveCount + "<br />");
//                } else {//数据错误，没有机构id；
//                    String message = "PersonnelAttendanceMonthly 数据错误，机构id为空, id=" + monthly.getId();
//                    errorInfo.add(message);
//                    responseWriteln(writer, "errorMessage：" + message);
//                    logger.error(message, new RuntimeException());
//                }
//            } else {
//                responseWriteln(writer, NewDateUtils.dateToString(System.currentTimeMillis(), sdf) + " saveCount / monthlyIndex / monthlyCount: " + saveCount + " / " + monthlyIndex + " / " + monthlies.size());
//                responseWriteln(writer,Boolean.valueOf(monthly.getWorkingHours().equals(0.0F) && monthly.getLeaveDuration().equals(0.0F)).toString() + ", PersonnelAttendanceMonthly 改过了，monthly id=" + monthly.getId() + " userId=" + monthly.getUser() + "monthly.getWorkingHours()=" +monthly.getWorkingHours().toString() + "monthly.getLeaveDuration()=" + monthly.getLeaveDuration().toString());
//            }
//        }
//        if(lockKey!=null && methodName!=null) {//如果有机构锁，先释放；
//            dlmService.releaseLock(methodName, lockKey);
//        }
//        if (!commitBegin && transaction.getStatus().equals(TransactionStatus.ACTIVE)) {
//            transaction.commit();
//        }
//        responseWriteln(writer, "saveCount / monthlyIndex / monthlyCount: " + saveCount + " / " + monthlyIndex + " / " + monthlies.size());
//        HashMap<String, Object> result = new HashMap<>(4);
//        result.put("monthlySize", monthlies.size());
//        result.put("saveCount", saveCount);
//        result.put("duration", TimeUtils.toTimeString2sf(System.currentTimeMillis() - start));
//        result.put("errorInfo", errorInfo);
//        responseWriteln(writer, "任务完成：" + JSON.toJSONString(result));
//        responseWriteln(writer, "完成时间：" + NewDateUtils.dateToString(System.currentTimeMillis(), sdf));
//        return result;
//    }
//    private void responseWriteln(PrintWriter writer, String message) {
//        if(writer!=null) {
//            writer.println(message + "<br />");
//            writer.flush();
//        } else {
//            logger.warn(message);
//        }
//    }
//    private Pair<List<Date>, List<Date>> getWorkDates(List<PersonnelAttendanceException> list) {
//        List<Date> workDates = new ArrayList<>();
//        List<Date> weekendDates = new ArrayList<>();
//        list.stream().forEach(item -> {
//            switch(item.getType()) {//类型：1-假，2-班
//                case "1":
//                    weekendDates.add(item.getExceptionDate());
//                    break;
//                case "2":
//                    workDates.add(item.getExceptionDate());
//                    break;
//                default:
//                    logger.error("PersonnelAttendanceException 数据错误，Type 不合法, id="+item.getId(),new RuntimeException());
//            }
//        });
//        return Pair.of(weekendDates, workDates);
//    }
//
//    private List<PersonnelAttendanceConfig> getPersonnelAttendanceConfig(Integer oid) {
//        String hql = "from PersonnelAttendanceConfig where org=:oid and enabled=1 order by openDate";
//        Map<String, Object> params = new HashMap<String, Object>();
//        List<PersonnelAttendanceConfig> configs = personnelAttendanceConfigDao.getListByHQLWithNamedParams(hql.toString(), null);
//        return configs;
//    }
   private Double getDurationFromLeaves(PersonnelAttendanceUser personnelAttendanceUser, List<PersonnelLeave> personnelLeaves) {
//        //简单求和
//        List<Pair<Date, Date>> workHoursList = personnelAttendanceUser.GetWorkHoursList();
//        return personnelLeaves.stream().mapToDouble(item -> item.calcDuration(workHoursList)).sum() / TimeUnit.HOURS.toMillis(1);
       //wyu：去重求和
       Map<Long, Long>  workHoursSlices = personnelAttendanceUser.GetSliceWorkHoursList();
       Pair<Map<Long,Long>, Map<Long, Long>> durationSlices = Pair.of(new HashMap<>(), workHoursSlices);
       personnelLeaves.stream().forEach(item ->  item.sliceDuration(durationSlices));
       return Double.valueOf(durationSlices.getLeft().entrySet().stream().mapToLong(item -> item.getValue()-item.getKey()).sum())/TimeUnit.HOURS.toMillis(1);
   }
   private Double getDurationFromDetails(PersonnelAttendanceUser personnelAttendanceUser, List<PersonnelAttendanceUserDetail> details) {
//        //简单求和
//        List<Pair<Date, Date>> workHoursList = personnelAttendanceUser.GetWorkHoursList();
//        return personnelLeaves.stream().mapToDouble(item -> item.calcDuration(workHoursList)).sum() / TimeUnit.HOURS.toMillis(1);
       //wyu：去重求和
       Map<Long, Long>  workHoursSlices = personnelAttendanceUser.GetSliceWorkHoursList();
       Pair<Map<Long,Long>, Map<Long, Long>> durationSlices = Pair.of(new HashMap<>(), workHoursSlices);
       details.stream().forEach(item ->  item.sliceDuration(durationSlices));
       return Double.valueOf(durationSlices.getLeft().entrySet().stream().mapToLong(item -> item.getValue()-item.getKey()).sum())/TimeUnit.HOURS.toMillis(1);
   }

    @Override
    public Map<String, Object> checkAttendanceUpdate(Integer oid) {
        Map<String,Object> map=new HashMap<>();
        Date systemTime = workAttendanceService.getStartUsingSystemTime(oid);  //获取系统考勤开始时间
        if (systemTime!=null) {
            String systemLastMonth = NewDateUtils.dateToString(systemTime,"yyyyMM");  //系统考勤开始时间的上个月时间
            String currentLastMonth = NewDateUtils.dateToString(new Date(),"yyyyMM");  //当前时间的上个月时间
            if (currentLastMonth.compareTo(systemLastMonth)<=0){
                map.put("status", 0);   //不可查询上月考勤(两个时间相等，或者当前时间的上个月在考勤上个月时间之前，都不可修改考勤)
            }else {
                TAccountantSettle settled = subjectSelectService.judgeSettled(2, oid);  //返回值 false 表示上月已结账，true 表示上月未结账，null 表示上月未结账
                if (settled == null || settled.isSettled()) {  //表示上月未结账，可以查询上月考勤
                    map.put("status", 1);
                } else if (!settled.isSettled()) {  //false 表示上月已结账
                    map.put("status", 0);
                }
            }
        }else {
            map.put("status", 0);   //不可查询上月考勤
        }
        return map;
    }

    @Override
    public Map<String, Object> updateTimeTable(User user, String time) {
        Map<String,Object> map=new HashMap<>();
        String newTime = StringEscapeUtils.unescapeHtml(time);  //进行转码
        JSONArray timeTable = JSONArray.fromObject(newTime);
        String content = "";
        boolean updated = false;
        for (int i=0;i<timeTable.size();i++){
            JSONObject timeJo = timeTable.getJSONObject(i);
            String exceptionDate = timeJo.getString("exceptionDate"); //作息日期
            Date exceptionDate1 = NewDateUtils.dateFromString(exceptionDate,"yyyy-MM-dd");
            String typeString = timeJo.getString("type");  //类型：1-假，2-班
            WorkAttendanceService.WorkdayType type = WorkAttendanceService.WorkdayType.Workday;
            if ("1".equals(typeString)) {
                type = WorkAttendanceService.WorkdayType.DayOff;
            }
            PersonnelAttendanceException personnelAttendanceException = workAttendanceService.getPersonnelAttendanceExceptionByExceptionDate(user.getOid(),exceptionDate1);
            if (personnelAttendanceException!=null&&!type.equals(personnelAttendanceException.getType())){  //类型不一样的可以改
                if (exceptionDate1.before(NewDateUtils.today())) { //修改日期是过去的，才需要修改一下数据
                    if ("2".equals(personnelAttendanceException.getType())) { //过去日期的，只有是班的能改为假
                        List<PersonnelAttendanceUser> personnelAttendanceUsers = getPersonnelAttendanceUserByOpenDate(user.getOid(), null, null, exceptionDate1);
                        for (PersonnelAttendanceUser pu : personnelAttendanceUsers) {
                            User user1 = userService.getUserByID(pu.getUser());
                            PersonnelAttendanceUserDetail overtimePud = null;
                            List<Integer> deleteIds = new ArrayList<>();
                            List<PersonnelAttendanceUserDetail>  deletePuds= new ArrayList<>();

                            List<PersonnelAttendanceUserDetail> personnelAttendanceUserDetails = workAttendanceService.getPersonnelAttendanceUserDetailByBusiness(pu.getId(), null, null, null, null);
                            for (PersonnelAttendanceUserDetail pud : personnelAttendanceUserDetails) {
                                System.out.println(JSON.toJSONString(pud));
                                if("8".equals(pud.getType())) {
                                    overtimePud = pud;
                                } else {
                                    deleteIds.add(pud.getId());
                                    deletePuds.add(pud);
                                }
                            }
                            //是否已申报加班
                            boolean hasOvertime = overtimePud!=null && personnelAttendanceMonthlyService.GetOvertime(user1, pu.getAttendanceDate()).getLeft()!=0;
                            personnelAttendanceMonthlyService.SetNoNeed(user1, exceptionDate1, true);
                            personnelAttendanceMonthlyService.ClearDay(user1, exceptionDate1);
                            if (overtimePud==null) {//删除没有加班的数据
                                if(!deletePuds.isEmpty()) {
                                    personnelAttendanceUserDetailDao.deleteAll(deletePuds);
                                }
                                personnelAttendanceUserDao.delete(pu);
                            } else {//处理加班数据
                                List<PersonnelAttendanceUserDetailPunch> detailPunchs = personnelAttendanceUserDetailPunchDao.getListByHQLWithNamedParams("from PersonnelAttendanceUserDetailPunch where detail in (:ids)", new HashMap<String, Object>(1) {{
                                    put("ids", deleteIds);
                                }});
                                Long beginTime = overtimePud.getBeginTime().getTime();
                                Long endTime = overtimePud.getEndTime().getTime();
                                for (PersonnelAttendanceUserDetailPunch dp: detailPunchs) {
                                    dp.setDetail(overtimePud.getId());
                                    if(beginTime>dp.getCreateTime().getTime()) {
                                        beginTime = dp.getCreateTime().getTime();
                                    }
                                    if(endTime<dp.getCreateTime().getTime()) {
                                        endTime = dp.getCreateTime().getTime();
                                    }
                                    personnelAttendanceUserDetailPunchDao.update(dp);
                                }
                                overtimePud.setBeginTime(new Date(beginTime));
                                overtimePud.setEndTime(new Date(endTime));
                                overtimePud.setDuration((double) TimeUnit.MILLISECONDS.toHours(endTime - beginTime));
                                pu.setType((byte)8);
                                pu.setBeginState("8");
                                pu.setEndState("8");
                                if(hasOvertime) {//加班已申报
                                    personnelAttendanceMonthlyService.SetOvertime(user1, pu.getAttendanceDate(), (byte) 1, overtimePud.getDuration().floatValue());
                                }
                                if(!deletePuds.isEmpty()) {
                                    personnelAttendanceUserDetailDao.deleteAll(deletePuds);
                                }
                            }
                        }
                        personnelAttendanceException.setType(type);
                        personnelAttendanceException.setUpdateDate(new Date());
                        personnelAttendanceException.setUpdateName(user.getUserName());
                        personnelAttendanceException.setUpdator(user.getUserID());
                        personnelAttendanceExceptionDao.update(personnelAttendanceException);
                        updated = true;

                        if (StringUtils.isNotEmpty(content)) {
                            if (!"修改成功".equals(content)) {
                                content = content + "，其他日期修改成功";
                            }
                        } else {
                            content = "修改成功";
                        }
                    }else {
                        if (StringUtils.isNotEmpty(content)){
                            content = content+","+exceptionDate+"有误";
                        }else {
                            content = exceptionDate+"有误";
                        }
                    }
                }else {
                    personnelAttendanceException.setType(type);
                    personnelAttendanceException.setUpdateDate(new Date());
                    personnelAttendanceException.setUpdateName(user.getUserName());
                    personnelAttendanceException.setUpdator(user.getUserID());
                    personnelAttendanceExceptionDao.update(personnelAttendanceException);
                    updated = true;

                    if (StringUtils.isNotEmpty(content)) {
                        if (!"修改成功".equals(content)) {
                            content = content + "，其他日期修改成功";
                        }
                    } else {
                        content = "修改成功";
                    }
                }
            }else {
                if (StringUtils.isNotEmpty(content)){
                    content = content+","+exceptionDate+"有误";
                }else {
                    content = exceptionDate+"有误";
                }
            }
        }
        if(updated) {//如果作息时间表被修改，清除工作日缓存
            workAttendanceService.clearWorkDateCaches(user.getOid());
        }
        map.put("status",1);
        map.put("content",content);
        return map;
    }

    @Override
    public List<PersonnelAttendanceUser> getPersonnelAttendanceUserByOpenDate(Integer org, Integer dept, Integer userId, Date openDate) {
        HashMap<String, Object> params = new HashMap<>();
        StringBuffer hql = new StringBuffer(" from PersonnelAttendanceUser where org=:org");
        params.put("org", org);
        if (openDate != null) {
            hql.append(" and attendanceDate=:attendanceDate");
            params.put("attendanceDate", openDate);
        }
        if (dept != null) {
            hql.append(" and p.dept=:dept");
            params.put("dept", dept);
        }
//        if (!MyStrings.nulltoempty(userName).isEmpty()) {
//            hql.append(" and u.userName like :userName");
//            params.put("userName", "%" + userName + "%");
//        }
        if (userId != null) {  //取此人员的下属以及间接下属
            hql.append(" and (u.userID=:userId or u.rankUrl like :userID)");
            params.put("userId", userId); //取职工自己
            params.put("userID", "%/" + userId + "/%");  //取本职工的直接以及间接下级
        }
        List<PersonnelAttendanceUser> personnelAttendanceUsers = (List<PersonnelAttendanceUser>) personnelAttendanceUserDao.getListByHQLWithNamedParams(hql.toString(), params);
        return personnelAttendanceUsers;
    }

//    @Override  //默认添加加班的打卡 overUserId:加班人员id   type：1-考勤定时任务运行时的 2-加班申请审批时的
//    public void addDefaultClockRecord(PersonnelOvertime outTime,Integer type,PersonnelAttendanceUser personnelAttendanceUser,Integer detailId) {
//        User overUser = userDao.get(outTime.getUser_());
//        Integer dept = 0;  //部门其他的
//        if (StringUtils.isNotEmpty(overUser.getDepartment())) {
//            dept = Integer.parseInt(overUser.getDepartment());
//        }
//        Date newDate = new Date();
//        Byte detailPunchType = DetailPunchType.begin.getIndex();  //加班的   类型:1-开始,2-结束
//        Byte punchType = PunchType.beforeOvertime.getIndex(); //加班打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
//        Byte punchType1 = PunchType.offWork.getIndex(); //上下班打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
//        Byte detailPunchType1 = DetailPunchType.end.getIndex();  //上下班的  类型:1-开始,2-结束
//        PersonnelAttendanceConfig personnelAttendanceConfig = getPersonnelAttendanceConfigByDept(overUser.getOid(),dept,outTime.getBeginTime());  //获取加班人员那天的考勤规则
//        if (personnelAttendanceConfig!=null&&personnelAttendanceConfig.getAttendancePattern().equals(WorkAttendanceOldService.ConfigAttendancePattern.attendanceTreasure.getIndex())) {  //考勤宝的
//            PersonnelAttendanceException personnelAttendanceException = getPersonnelAttendanceExceptionByExceptionDate(overUser.getOid(),NewDateUtils.today(outTime.getBeginTime()));
//            if (personnelAttendanceException!=null&&"2".equals(personnelAttendanceException.getType())){  //上班日需要添加默认加班，休息日不需要添加
//                if (personnelAttendanceUser==null){
//                    personnelAttendanceUser = workAttendanceService.getPersonnelAttendanceUserByUserIdAndId(null, overUser.getUserID(), NewDateUtils.today(outTime.getBeginTime()), null); //查询某人某天的考勤记录
//                }
//                if (personnelAttendanceUser!=null&&personnelAttendanceUser.getId()!=null){
//                    Integer colckState = 1 ; //1-不用默认打卡  2-默认打卡
//                    if (personnelAttendanceUser.getBeginTime().getTime()==outTime.getEndTime().getTime()){ //上班时间和加班结束时间一样得
//                        newDate = outTime.getEndTime();
//                        colckState = 2;
//                        detailPunchType = DetailPunchType.end.getIndex();
//                        punchType = PunchType.afterOvertime.getIndex();
//                        punchType1 = PunchType.goToWork.getIndex();
//                        detailPunchType1 = DetailPunchType.begin.getIndex();
//                    }else if (personnelAttendanceUser.getEndTime().getTime()==outTime.getBeginTime().getTime()){  //下班时间和加班开始时间一样得
//                        newDate = outTime.getBeginTime();
//                        colckState =2;
//                    }
//                    if (2==colckState) {
//                        if (2==type) {
//                            //默认加班的打卡
//                            PersonnelAttendanceUserDetail personnelAttendanceUserDetail = getPersonnelAttendanceUserDetail(outTime.getUser_(), NewDateUtils.today(outTime.getBeginTime()), "8", outTime.getId(), null);
//                            if (personnelAttendanceUserDetail == null) {
//                                personnelAttendanceUserDetail = workAttendanceService.addPersonnelAttendanceUserDetail(outTime.getBeginTime(), outTime.getEndTime(), "8", "1", outTime.getType(), null, outTime.getId(), outTime.getReason(), personnelAttendanceUser, overUser, String.valueOf(outTime.getDuration()), null, null, newDate); //加班
//                            }
//                            OverPunchRecord overPunchRecord = new OverPunchRecord(overUser.getUserID(), newDate, punchType, detailPunchType, personnelAttendanceUserDetail.getId(), outTime.getId(),AttendanceType.overtime.getIndex());
//                            clusterMessageSendingOperations.delayCall(newDate, overPunchRecord);
//
//                            //默认上下班的打卡
//                            PersonnelAttendanceUserDetail personnelAttendanceUserDetail1 = getPersonnelAttendanceUserDetail(outTime.getUser_(), NewDateUtils.today(outTime.getBeginTime()), "1", null, null);
//                            if (personnelAttendanceUserDetail1 == null) {
//                                personnelAttendanceUserDetail1 = workAttendanceService.addPersonnelAttendanceUserDetail(personnelAttendanceUser.getBeginTime(), personnelAttendanceUser.getEndTime(), "1", "1", null, null, null, null, personnelAttendanceUser, overUser, null, null, "1", new Date()); //正常上班
//                            }
//                            OverPunchRecord overPunchRecord1 = new OverPunchRecord(overUser.getUserID(), newDate, punchType1, detailPunchType1, personnelAttendanceUserDetail1.getId(), null, AttendanceType.normal.getIndex());
//                            clusterMessageSendingOperations.delayCall(newDate, overPunchRecord1);
//
//                        }else if (1==type){
//                            //默认加班打卡的
//                            if (detailId==null) {
//                                PersonnelAttendanceUserDetail personnelAttendanceUserDetail = getPersonnelAttendanceUserDetail(outTime.getUser_(), NewDateUtils.today(outTime.getBeginTime()), "8", outTime.getId(), null);
//                                if (personnelAttendanceUserDetail == null) {
//                                    personnelAttendanceUserDetail = workAttendanceService.addPersonnelAttendanceUserDetail(outTime.getBeginTime(), outTime.getEndTime(), "8", "1", outTime.getType(), null, outTime.getId(), outTime.getReason(), personnelAttendanceUser, overUser, String.valueOf(outTime.getDuration()), null, null, newDate); //加班
//                                }
//                                detailId = personnelAttendanceUserDetail.getId();
//                            }
//                            this.overPunchTurn(overUser.getUserID(), newDate, punchType, detailPunchType, detailId, outTime.getId(),AttendanceType.overtime.getIndex());
//
//                            //默认山下班打卡的
//                            PersonnelAttendanceUserDetail personnelAttendanceUserDetail1 = getPersonnelAttendanceUserDetail(outTime.getUser_(), NewDateUtils.today(outTime.getBeginTime()), "1", null, null);
//                            if (personnelAttendanceUserDetail1 == null) {
//                                personnelAttendanceUserDetail1 = workAttendanceService.addPersonnelAttendanceUserDetail(personnelAttendanceUser.getBeginTime(), personnelAttendanceUser.getEndTime(), "1", "1", null, null, null, null, personnelAttendanceUser, overUser, null, null, "1", new Date()); //正常上班
//                            }
//                            this.overPunchTurn(overUser.getUserID(), newDate, punchType1, detailPunchType1, personnelAttendanceUserDetail1.getId(), null,AttendanceType.normal.getIndex());
//                        }
//
//                        if ("0".equals(personnelAttendanceUser.getType())){  //考勤的总状态是未考勤的，则变为正常，其他的状态的则不用改。
//                            personnelAttendanceUser.setType((byte)1);  //总状态
//                            if (punchType1.equals(PunchType.goToWork.getIndex())){    //上班的状态
//                                personnelAttendanceUser.setBeginState("1");
//                                personnelAttendanceUser.setAmAttendance(newDate);
//                            }else if (punchType1.equals(PunchType.offWork.getIndex())){  //下班的
//                                if ("0".equals(personnelAttendanceUser.getEndState())){  //下班的状态，如果是未考勤的那么默认的是正常1，如果已经有其他状态的，则不用更改
//                                    personnelAttendanceUser.setEndState("1");
//                                    personnelAttendanceUser.setPmAttendance(newDate);
//                                }
//                            }
////                            personnelAttendanceUser.setUpdator(user.getUserID());
////                            personnelAttendanceUser.setUpdateName(user.getUserName());
//                            personnelAttendanceUser.setUpdateDate(newDate);
//                            personnelAttendanceUserDao.update(personnelAttendanceUser);
//                        }else {   // 考勤总状态是其他，但是上班或下班还有未考勤的
//                            if (punchType1.equals(PunchType.goToWork.getIndex())&&"0".equals(personnelAttendanceUser.getBeginState())){  //上班的状态，如果是未考勤的那么默认的是正常1，如果已经有其他状态的，则不用更改
//                                personnelAttendanceUser.setBeginState("1");
//                                personnelAttendanceUser.setAmAttendance(newDate);
//                                personnelAttendanceUser.setUpdateDate(newDate);
//                                personnelAttendanceUserDao.update(personnelAttendanceUser);
//                            }else if (punchType1.equals(PunchType.offWork.getIndex())&&"0".equals(personnelAttendanceUser.getEndState())){  //下班的状态，如果是未考勤的那么默认的是正常1，如果已经有其他状态的，则不用更改
//                                personnelAttendanceUser.setEndState("1");
//                                personnelAttendanceUser.setPmAttendance(newDate);
//                                personnelAttendanceUser.setUpdateDate(newDate);
//                                personnelAttendanceUserDao.update(personnelAttendanceUser);
//                            }
//                        }
//                    }
//                }
//            }
//        }
//    }

//    @Override    //punchType 打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
//    public void overPunchTurn(Integer overUserId, Date punchTime, Byte punchType, Byte detailPunchType, Integer detailId, Integer overtimeId,Byte type) {
//        User user = userDao.get(overUserId);
//        PersonnelAttendanceUserDetailPunch personnelAttendanceUserDetailPunch1 = getUserDetailPunch(user.getOid(),detailId,detailPunchType,punchTime,"系统");  //查默认添加记录的是否已添加
//        if (personnelAttendanceUserDetailPunch1==null) {  //没添加的再加上
//            //添加员工打卡信息
//            PersonnelAttendancePunch personnelAttendancePunch = new PersonnelAttendancePunch();
//            personnelAttendancePunch.setOrg(user.getOid());
//            personnelAttendancePunch.setUser(user.getUserID());
//            personnelAttendancePunch.setDept(StringUtils.isNotEmpty(user.getDepartment()) ? Integer.getInteger(user.getDepartment()) : null);
////                    personnelAttendancePunch.setCardNo(cardNo);//打卡卡号
////            personnelAttendancePunch.setAttendanceUser(user.getUserID()); //人员考勤ID
////                    personnelAttendancePunch.setSysDevice(iotTerminal.getId()); //打卡设备ID
////                    if (iotTerminal != null && iotTerminal.getMobile() != null) {
////                        personnelAttendancePunch.setMobileDevice(authInfo.getDeviceId().intValue()); //打卡手机ID,此处关联mq_用户手机关联表ID
////                    }
//            personnelAttendancePunch.setPunchTime(punchTime);  //打卡时间
//            personnelAttendancePunch.setPunchType(punchType);  //打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
//            personnelAttendancePunch.setType(type); //考勤类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班,0-其它
////            personnelAttendancePunch.setImage(null); //快照图片内容
//            personnelAttendancePunchDao.save(personnelAttendancePunch);
//
//            //员工考勤明细打卡对照信息
//            PersonnelAttendanceUserDetailPunch personnelAttendanceUserDetailPunch = new PersonnelAttendanceUserDetailPunch();
//            personnelAttendanceUserDetailPunch.setOrg(user.getOid());
//            personnelAttendanceUserDetailPunch.setUser(user.getUserID());
//            personnelAttendanceUserDetailPunch.setDetail(detailId); //明细id
//            personnelAttendanceUserDetailPunch.setPunch(personnelAttendancePunch.getId()); //打卡ID
//            personnelAttendanceUserDetailPunch.setType(detailPunchType);//类型:1-开始,2-结束
////                    personnelAttendanceUserDetailPunch.setCreator(overUser.getUserID());
//            personnelAttendanceUserDetailPunch.setCreateName("系统");  //overUser.getUserName()
//            personnelAttendanceUserDetailPunch.setCreateTime(punchTime);
//            personnelAttendanceUserDetailPunchDao.save(personnelAttendanceUserDetailPunch);
//        }
//    }

//    @Override
//    public PersonnelAttendanceUserDetailPunch getUserDetailPunch(Integer org,Integer detailId, Byte detailPunchType, Date punchTime, String createName) {
//        HashMap<String, Object> params = new HashMap<>();
//        StringBuffer hql = new StringBuffer(" from PersonnelAttendanceUserDetailPunch where org=:org");
//        params.put("org", org);
//        if (detailId != null) {
//            hql.append(" and detail=:detail");
//            params.put("detail", detailId);
//        }
////        if (punchType != null) {
////            hql.append(" and punchType=:punchType");
////            params.put("punchType", punchType);
////        }
//        if (detailPunchType != null) {
//            hql.append(" and type=:type");
//            params.put("type", detailPunchType);
//        }
//        if (detailId != null) {
//            hql.append(" and createTime=:createTime");
//            params.put("createTime", punchTime);
//        }
//        if (StringUtils.isNotEmpty(createName)) {
//            hql.append(" and createName=:createName");
//            params.put("createName", createName);
//        }
//        PersonnelAttendanceUserDetailPunch personnelAttendanceUserDetailPunch = (PersonnelAttendanceUserDetailPunch) personnelAttendanceUserDetailPunchDao.getByHQLWithNamedParams(hql.toString(),params);
//        return personnelAttendanceUserDetailPunch;
//    }

    @Override
    public List<PersonnelAttendanceUserDetailPunch> getUserDetailPunchList(Integer org,Integer detailId, Byte detailPunchType, Date punchTime, String createName) {
        HashMap<String, Object> params = new HashMap<>();
        StringBuffer hql = new StringBuffer(" from PersonnelAttendanceUserDetailPunch where org=:org");
        params.put("org", org);
        if (detailId != null) {
            hql.append(" and detail=:detail");
            params.put("detail", detailId);
        }
        if (detailPunchType != null) {
            hql.append(" and type=:type");
            params.put("type", detailPunchType);
        }
        if (punchTime != null) {
            hql.append(" and createTime=:createTime");
            params.put("createTime", punchTime);
        }
        if (StringUtils.isNotEmpty(createName)) {
            hql.append(" and createName=:createName");
            params.put("createName", createName);
        }
        List<PersonnelAttendanceUserDetailPunch> personnelAttendanceUserDetailPunchs = personnelAttendanceUserDetailPunchDao.getListByHQLWithNamedParams(hql.toString(),params);
        return personnelAttendanceUserDetailPunchs;
    }

    @Override
    public Map<String, Object> getClockRecord(Integer userId,Byte punchType,Integer personnelAttendanceUserDetailId,Integer order) {
        Map<String,Object> map = new HashMap<>();
        String hql1 = "select pp.id,pp.punchTime,pp.punchType,pp.type from PersonnelAttendancePunch pp, PersonnelAttendanceUserDetailPunch paup where pp.id=paup.punch and paup.user=:user and paup.detail=:personnelAttendanceUserDetailId";
        map.put("user",userId);
        map.put("personnelAttendanceUserDetailId",personnelAttendanceUserDetailId);
        if (punchType!=null){
            hql1+=" and pp.punchType=:punchType"; //打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
            map.put("punchType",punchType);
        }
        if (order!=null) {  //order: 1-按打卡时间倒叙
            hql1 += " order by pp.punchTime desc";
        }
        Object[] punch = (Object[]) personnelAttendancePunchDao.getByHQLWithNamedParams(hql1.toString(), map);

        Map<String, Object> map2 = new HashMap<>();
        if (punch!=null) {
            map2.put("id", punch[0]); //打卡id
            map2.put("punchTime", punch[1]); //打卡时间
            map2.put("punchType", punch[2]); //打卡类型:1-上班,2--下班,3-午前,4-午后,5-离岗,6-返岗,7-加班前,8-加班后,0-其它
            map2.put("type", punch[3]); //考勤类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班,0-其它
        }
        return map2;
    }

    @Override
    public PersonnelAttendanceTerminal getPersonnelAttendanceTerminal(String terminalUuid,String brand,String model,Byte type){
        Map<String,Object> map = new HashMap<>();
        StringBuffer hql = new StringBuffer("from PersonnelAttendanceTerminal");
        StringBuffer where = new StringBuffer();
        if (StringUtils.isNotEmpty(terminalUuid)){
            where.append(" and terminalUuid=:terminalUuid");   //唯一标识/webchat union_id
            map.put("terminalUuid",terminalUuid);
        }
        if (StringUtils.isNotEmpty(brand)){
            where.append(" and brand=:brand");  //品牌/昵称
            map.put("brand",brand);
        }
        if (StringUtils.isNotEmpty(model)){
            where.append(" and model=:model");  //型号
            map.put("model",model);
        }
        if (type!=null){
            where.append(" and type=:type");   //类型:1-设备,2-webchat
            map.put("type",type);
        }
        if (where.length() > 0) {
            hql.append(" where ").append(where.substring(4));
        }
        PersonnelAttendanceTerminal personnelAttendanceTerminal = (PersonnelAttendanceTerminal) personnelAttendanceTerminalDao.getByHQLWithNamedParams(hql.toString(),map);
        return personnelAttendanceTerminal;

    }

    public PersonnelAttendanceTerminal addPersonnelAttendanceTerminal(User user,String terminalUuid,String brand,String model,Byte type) {
        PersonnelAttendanceTerminal personnelAttendanceTerminal = new PersonnelAttendanceTerminal();
        personnelAttendanceTerminal.setType(type);   //类型:1-设备,2-webchat
        personnelAttendanceTerminal.setTerminalUuid(terminalUuid);  //唯一标识/webchat union_id
        personnelAttendanceTerminal.setBrand(brand);   //品牌/昵称
        personnelAttendanceTerminal.setModel(model);  //型号
        personnelAttendanceTerminal.setCreateTime(new Date());
        personnelAttendanceTerminalDao.save(personnelAttendanceTerminal);
        return personnelAttendanceTerminal;
    }

    @Override
    public List<PunchDto> getPersonnelAttendancePunches(Date start, Period period, Date end, PunchDto search, User user, PageInfo pageInfo) {
        List<PunchDto> result = new ArrayList<>();
        StringBuilder hql = new StringBuilder("select new cn.sphd.miners.modules.generalAffairs.dto.PunchDto(user, id, cardNo, punchTime, punchType, image, punchTerminal, sysDevice) from PersonnelAttendancePunch where org=:org");
        List<String> where = new ArrayList<>();
        Map<String, Object> params = new HashMap<>(){{
            put("org", user.getOid());
        }};
        List<Integer> searchParamIds;
        Triple<Map<Integer, User>, Map<Integer, PersonnelAttendanceTerminal>, Map<Integer, IotTerminal>> attributesMap = Triple.of(new HashMap<>(), new HashMap<>(), new HashMap<>());
        if ((searchParamIds = searchUsers(search, user, attributesMap))!=null) {//查询用户、部门
            switch (searchParamIds.size()) {
                case 0:
                    return result;//new ArrayList<>(0);
                case 1:
                    where.add("user=:user");
                    params.put("user", searchParamIds.get(0));
                    break;
                default:
                    where.add("user in (:users)");
                    params.put("users", searchParamIds);
            }
        }
        if ((searchParamIds = searchPunchTerminals(search, attributesMap))!=null) {//查询打卡手机
            switch (searchParamIds.size()) {
                case 0:
                    return result;//new ArrayList<>(0);
                case 1:
                    where.add("punchTerminal=:punchTerminal");
                    params.put("punchTerminal", searchParamIds.get(0));
                    break;
                default:
                    where.add("punchTerminal in (:punchTerminals)");
                    params.put("punchTerminal", searchParamIds);
            }
        }
        if ((searchParamIds = searchSysDevice(search, user, attributesMap))!=null) {//查询考勤机
            switch (searchParamIds.size()) {
                case 0:
                    return result;//new ArrayList<>(0);
                case 1:
                    where.add("sysDevice=:sysDevice");
                    params.put("sysDevice", searchParamIds.get(0));
                    break;
                default:
                    where.add("sysDevice in (:sysDevices)");
                    params.put("sysDevice", searchParamIds);
            }
        }
        //查询打卡表数据
        if(StringUtils.isNotBlank(search.getCardNo())) {
            where.add("cardNo like :cardNo");
            params.put("cardNo", "%" + search.getCardNo() + "%");
        }
        if(search.getPunchType() != null) {
            where.add("punchType=:punchType");
            params.put("punchType", search.getPunchType());
        }
        if (start!=null || end!=null) {
            fillDateWhere(start, period, end, where, params);
        }
        if (!where.isEmpty()) {
            hql.append(" and ").append(StringUtils.join(where, " and "));
        }
        hql.append(" order by punchTime desc");
        result = personnelAttendancePunchDao.getListByHQLWithNamedParams(hql.toString(), params, pageInfo);
        fillAttributes(result, attributesMap);
        return result;
    }
    private void fillDateWhere(Date start,Period period,Date end,List<String> where,Map<String, Object> params) {
        //start,period不为空，end失效
        if(start!=null && period!=null && !Period.unlimit.equals(period)) {
            end = Period.changePeriodEnd(start, 1, period);
        }
        //start,end均为整日,period为空，end取当天结束
        if(period==null && NewDateUtils.isTimeZero(end) && (start==null || NewDateUtils.isTimeZero(start))) {
            end = NewDateUtils.getLastTimeOfDay(end);
            if (start.equals(NewDateUtils.today())){
                end = new Date();
            }
        }
        if(start!=null && end==null) {
            where.add("punchTime>=:start");
            params.put("start",start);
        } else if (start==null && end!=null) {
            where.add("punchTime<=:end");
            params.put("end",end);
        } else if (start!=null && end!=null) {
            where.add("punchTime between :start and :end");
            params.put("start",start.getTime()<=end.getTime()?start:end);
            params.put("end",start.getTime()<=end.getTime()?end:start);
        }
    }
    private void fillAttributes(List<PunchDto> result, Triple<Map<Integer, User>, Map<Integer, PersonnelAttendanceTerminal>, Map<Integer, IotTerminal>> attributesMap) {
        User user;
        PersonnelAttendanceTerminal terminal;
        IotTerminal iotTerminal;
        for(PunchDto item : result) {
            if (item.getUserID()!=null) {
                if((attributesMap.getLeft().get(item.getUserID()))==null
                        && (user = userService.getUserByID(item.getUserID()))!=null) {
                    if (StringUtils.isNumeric(user.getDepartment())) {
                        Organization org = orgService.getOrgByOid(Integer.valueOf(user.getDepartment()), OrgService.OrgType.department);
                        user.setDepartName(org.getName());
                    }
                    attributesMap.getLeft().put(user.getUserID(), user);
                }
                if((user = attributesMap.getLeft().get(item.getUserID()))!=null) {
                    item.setUserName(user.getUserName());
                    item.setDepartment(user.getDepartment());
                    item.setName(user.getDepartName());
                }
            }
            if (item.getPunchTerminal()!=null) {
                if((attributesMap.getMiddle().get(item.getPunchTerminal()))==null
                        && (terminal = personnelAttendanceTerminalDao.get(item.getPunchTerminal()))!=null) {
                    attributesMap.getMiddle().put(terminal.getId(), terminal);
                }
                if((terminal = attributesMap.getMiddle().get(item.getPunchTerminal()))!=null) {
                    item.setType(terminal.getType());
                    item.setTerminalUuid(terminal.getTerminalUuid());
                    item.setBrand(terminal.getBrand());
                    item.setModel(terminal.getModel());
                }
            }
            if (item.getSysDevice()!=null) {
                if((attributesMap.getRight().get(item.getSysDevice()))==null
                        && (iotTerminal = iotService.getById(item.getSysDevice()))!=null) {
                    attributesMap.getRight().put(iotTerminal.getId(), iotTerminal);
                }
                if(( iotTerminal = attributesMap.getRight().get(item.getSysDevice()))!=null) {
                    item.setDeviceUuid(iotTerminal.getDeviceUuid());
                    item.setSn(iotTerminal.getSn());
                }
            }
        }
    }
    private List<Integer> searchUsers(PunchDto search, User user, Triple<Map<Integer, User>, Map<Integer, PersonnelAttendanceTerminal>, Map<Integer, IotTerminal>> attributesMap) {
        if (search.getUserID()!=null) {
            return new ArrayList<Integer>(1) {{
                add(search.getUserID());
            }};
        } else {
            if(StringUtils.isNotEmpty(search.getUserName()) || StringUtils.isNotBlank(search.getDepartment()) || StringUtils.isNotBlank(search.getName())) {
                List<Integer> result = new ArrayList<>();
                User userSearch = new User();
                userSearch.setUserName(search.getUserName());
                userSearch.setDepartment(search.getDepartment());
                List<User> users = userService.getAllUsersByOrgDepart(user.getOid(), userSearch, search.getName());
                attributesMap.getLeft().putAll(users.stream().collect(Collectors.toMap(User::getUserID,item->item)));
                result.addAll(users.stream().map(User::getUserID).collect(Collectors.toList()));
                return result;
            }
            return null;
        }
    }
    private List<Integer> searchPunchTerminals(PunchDto search, Triple<Map<Integer, User>, Map<Integer, PersonnelAttendanceTerminal>, Map<Integer, IotTerminal>> attributesMap) {
        if (search.getPunchTerminal()!=null) {
            return new ArrayList<Integer>(1) {{
                add(search.getPunchTerminal());
            }};
        } else if (StringUtils.isNotBlank(search.getTerminalUuid()) || StringUtils.isNotBlank(search.getTerminalUuid()) || StringUtils.isNotBlank(search.getBrand()) || StringUtils.isNotBlank(search.getBrand()) || StringUtils.isNotBlank(search.getModel())){
            List<Integer> result = new ArrayList<>();
            StringBuffer hql = new StringBuffer("from PersonnelAttendanceTerminal");
            List<String> where = new ArrayList<>();
            Map<String, Object> params = new HashMap<>();
            if(StringUtils.isNotBlank(search.getTerminalUuid())) {
                where.add("type=:type");
                params.put("type", search.getType());
            } else if (StringUtils.isNotBlank(search.getTerminalUuid())) {
                where.add("terminalUuid like :terminalUuid");
                params.put("terminalUuid", "%" + search.getTerminalUuid() + "%");
            } else if (StringUtils.isNotBlank(search.getBrand())) {
                where.add("brand like :brand");
                params.put("brand", "%" + search.getBrand() + "%");
            } else if (StringUtils.isNotBlank(search.getModel())) {
                where.add("terminalUuid like :model");
                params.put("model", "%" + search.getModel() + "%");
            }
            if(!where.isEmpty()) {
                hql.append(" where ").append(StringUtils.join(where, " and "));
            }
            List<PersonnelAttendanceTerminal> terminals = personnelAttendanceTerminalDao.getListByHQLWithNamedParams(hql.toString(), params);
            attributesMap.getMiddle().putAll(terminals.stream().collect(Collectors.toMap(PersonnelAttendanceTerminal::getId,item->item)));
            result.addAll(terminals.stream().map(PersonnelAttendanceTerminal::getId).collect(Collectors.toList()));
            return result;
        } else {
            return null;
        }
    }
    private List<Integer> searchSysDevice(PunchDto search, User user, Triple<Map<Integer, User>, Map<Integer, PersonnelAttendanceTerminal>, Map<Integer, IotTerminal>> attributesMap) {
        if (search.getSysDevice()!=null) {
            return new ArrayList<Integer>(1) {{
                add(search.getSysDevice());
            }};
        } else if (StringUtils.isNotBlank(search.getTerminalUuid()) || StringUtils.isNotBlank(search.getTerminalUuid()) || StringUtils.isNotBlank(search.getBrand()) || StringUtils.isNotBlank(search.getBrand()) || StringUtils.isNotBlank(search.getModel())){
            List<Integer> result = new ArrayList<>();
            IotTerminal iotSearch = new IotTerminal();
            iotSearch.setDeviceUuid(search.getDeviceUuid());
            iotSearch.setSn(search.getSn());
            List<IotTerminal> iotTerminals= iotService.searchIotTerminals(iotSearch, user.getOid());
            attributesMap.getRight().putAll(iotTerminals.stream().collect(Collectors.toMap(IotTerminal::getId,item->item)));
            result.addAll(iotTerminals.stream().map(IotTerminal::getId).collect(Collectors.toList()));
            return result;
        } else {
            return null;
        }
    }
}