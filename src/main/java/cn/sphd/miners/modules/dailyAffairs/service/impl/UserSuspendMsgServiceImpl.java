package cn.sphd.miners.modules.dailyAffairs.service.impl;

import cn.sphd.miners.common.utils.TimeUtils;
import cn.sphd.miners.modules.dailyAffairs.dao.SuspendUrlDao;
import cn.sphd.miners.modules.dailyAffairs.dao.UserSuspendMsgDao;
import cn.sphd.miners.modules.dailyAffairs.entity.SuspendUrl;
import cn.sphd.miners.modules.dailyAffairs.entity.UserSuspendMsg;
import cn.sphd.miners.modules.dailyAffairs.service.SWMessageService;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.socket.service.ClusterMessageSendingOperations;
import cn.sphd.miners.modules.system.dao.UserDao;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import io.netty.util.internal.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * Created by Administrator on 2018/10/30.
 */
@Service("userSuspendMsgService")
@Transactional(isolation= Isolation.DEFAULT,propagation= Propagation.REQUIRED,readOnly=false)
public class UserSuspendMsgServiceImpl implements UserSuspendMsgService{

    @Autowired
    UserSuspendMsgDao userSuspendMsgDao;
    @Autowired
    ClusterMessageSendingOperations clusterMessageSendingOperations;
    @Autowired
    UserDao userDao;
    @Autowired
    UserService userService;
    @Autowired
    SuspendUrlDao suspendUrlDao;
    @Autowired
    SWMessageService swMessageService;

    @Override
    public void saveUserSuspendMsg(UserSuspendMsg userSuspendMsg) {
        userSuspendMsgDao.save(userSuspendMsg);
    }

    @Override
    public void updateUserSuspendMsg(String state,Integer toUserId ,Integer... messageIds) {
//        System.out.println("查看消息变已读开始"+new Date());
        ArrayList<Map<String,Integer>> mapArrayList=new ArrayList<>();
        User user=userService.getUserByID(toUserId);
        for (Integer messageId:messageIds) {
            UserSuspendMsg userSuspendMsg=userSuspendMsgDao.get(messageId);

            if (userSuspendMsg.getState().equals("1")) {
                userSuspendMsg.setState(state);//已读
                userSuspendMsg.setUpdateDate(new Date());
                userSuspendMsgDao.update(userSuspendMsg);

                Map<String,Integer> result=new HashMap<>();
                result.put("id",userSuspendMsg.getId());
                mapArrayList.add(result);
            }
        }
        this.userSuspendMsgRejectSend(-mapArrayList.size(),user.getUserID(),null,mapArrayList,"/message",null,null,user);// 长连接推送

    }

    @Override
    public UserSuspendMsg saveUserSuspendMsg(Integer superscript, String content, String memo, Integer toUserId, String type, Integer business) {
        return this.saveUserSuspendMsg(superscript,content,content,memo,toUserId,type,business,null,null);
    }

    @Override
    public UserSuspendMsg saveUserSuspendMsg(Integer superscript, String title, String content, String memo, Integer toUserId, String type, Integer business) {
        return this.saveUserSuspendMsg(superscript,title,content,memo,toUserId,type,business,null,null);
    }




    @Override
    public UserSuspendMsg saveUserSuspendMsg(Integer superscript,String title,String content, String memo,Integer toUserId,String type,Integer business,String callbackClass,String callbackParams) {
        System.out.println("发消息开始"+new Date());

        String hql=" from SuspendUrl where suspendType=:suspendType";
        HashMap<String,Object> map=new HashMap<>();
        map.put("suspendType",type);
        SuspendUrl suspendUrl= (SuspendUrl) suspendUrlDao.getByHQLWithNamedParams(hql,map);

        UserSuspendMsg userSuspendMsg=new UserSuspendMsg();
        userSuspendMsg.setCreateDate(new Date());
        userSuspendMsg.setContent(content);
        userSuspendMsg.setMemo(memo);
        userSuspendMsg.setState("1");
        userSuspendMsg.setUserId(toUserId);
        userSuspendMsg.setSendTime(new Date());
        if (!StringUtil.isNullOrEmpty(callbackClass)){
            userSuspendMsg.setCallbackClass(callbackClass);
            userSuspendMsg.setCallbackParams(callbackParams);
        }
        if (suspendUrl!=null) {
            userSuspendMsg.setUrlAndroid(suspendUrl.getUrlAndroid());
            userSuspendMsg.setUrlIos(suspendUrl.getUrlIos());
            userSuspendMsg.setUri(suspendUrl.getUrlPc()+business);
        }

        userSuspendMsg.setBusinessType(business);
        userSuspendMsgDao.save(userSuspendMsg);//新增

        User user=userDao.get(toUserId);
        System.out.println("人员机构消息数量变更前: "+user.getMsgCount()+" userId: "+toUserId);

//        user.setMsgCount(user.getMsgCount()==null?1:user.getMsgCount()+1);
//        userDao.update(user);
        ArrayList<UserSuspendMsg> result=new ArrayList<>();
        result.add(userSuspendMsg);

        this.userSuspendMsgRejectSend(superscript,toUserId,result,null,"/message",title,userSuspendMsg.getContent(),user);// 长连接推送
        System.out.println("人员机构消息数量变更后: "+user.getMsgCount()+" userId: "+toUserId);
        System.out.println("发消息结束"+new Date());

        return userSuspendMsg;

    }

    @Override
    public void userSuspendMsgRejectSend(Integer superscript, Integer toUserId, ArrayList<UserSuspendMsg> userSuspendMsgList,ArrayList<Map<String,Integer>> mapArrayList, String pass, String title, String content, User user) {
        HashMap<String,Object> hashMap=new HashMap<>();
        if (superscript<0){
            hashMap.put("messageInfo", mapArrayList);  // 小于0是只有要刪除id的列表
        }else {
            hashMap.put("messageInfo", userSuspendMsgList); // 大于0是詳情
        }
        hashMap.put("colHeadNum",superscript); // 大于 0 数据加 角标加， 小于0 数据减 角标减
        swMessageService.rejectSend(superscript,superscript,hashMap,toUserId.toString(),pass,title,content,user,"message");

//        clusterMessageSendingOperations.convertAndSendToUser(toUserId.toString(),pass,title,content,user.getOid(),user.getOrganization().getName(),JSON.toJSONString(hashMap));

    }

    @Override
    public List<UserSuspendMsg> getUserSuspendMsgListByUserId(Integer userId) {
        String hql=" from UserSuspendMsg where userId=:userId and state='1'";
        Map<String,Object> map = new HashMap<>();
        map.put("userId",userId);
        List<UserSuspendMsg> userSuspendMsgList= userSuspendMsgDao.getListByHQLWithNamedParams(hql,map);
        return userSuspendMsgList;
    }

    @Override
    public UserSuspendMsg getMsgById(Integer messageId) {
        return userSuspendMsgDao.get(messageId);
    }

    @Override
    public Integer getUserSuspendMsgCounts(Integer userId) {
        String hql="select count(id) from UserSuspendMsg where userId=:userId and state='1'";
        Map<String,Object> map = new HashMap<>();
        map.put("userId",userId);
        Long userSuspendMsgCount= (Long) userSuspendMsgDao.getByHQLWithNamedParams(hql,map);
        return userSuspendMsgCount.intValue();
    }

    @Override
    public Integer getBadgeNumber(User user, String code) {
        Integer number=null;
        switch (code){
            case "message":  //我的消息
                number=this.getUserSuspendMsgCounts(user.getUserID());
                break;
        }
        return number;
    }

    @Override
    public void deleteUserSuspendMsg(User user, String callbackClass, List<String> callbackParams) {
        String hql=" from UserSuspendMsg where state='1' and userId=:userId and callbackClass=:callbackClass and callbackParams in(:callbackParams)";
        Map<String,Object> map=new HashMap<>();
        map.put("userId",user.getUserID());
        map.put("callbackClass",callbackClass);
        map.put("callbackParams",callbackParams);
        List<UserSuspendMsg> userSuspendMsgList=userSuspendMsgDao.getListByHQLWithNamedParams(hql,map);
        ArrayList<Map<String,Integer>> mapArrayList=new ArrayList<>();

        for (UserSuspendMsg userSuspendMsg:userSuspendMsgList){
            userSuspendMsg.setState("4");//删除
            userSuspendMsgDao.update(userSuspendMsg);

            Map<String,Integer> result=new HashMap<>();
            result.put("id",userSuspendMsg.getId());
            mapArrayList.add(result);
        }
        this.userSuspendMsgRejectSend(-1,user.getUserID(),null,mapArrayList,"/message",null,null,user);// 长连接推送
    }

    @Override
    public List<UserSuspendMsg> getWillDisappearMessages(Integer userId) {
        String hql=" from UserSuspendMsg where state='2' and userId=:userId and :beginDate<=updateDate and updateDate<=:endDate";
        Map<String,Object> map=new HashMap<>();
        Date beginDate=new Date(new Date().getTime() - 3 * 24 * 60 * 60 * 1000);// 当前时间 72小时前
        map.put("userId",userId);
        map.put("beginDate",beginDate);
        map.put("endDate",new Date());
        List<UserSuspendMsg> userSuspendMsgList=userSuspendMsgDao.getListByHQLWithNamedParams(hql+" order by updateDate ASC",map);
        for (UserSuspendMsg userSuspendMsg:userSuspendMsgList){
            String a= TimeUtils.toTimeString2sf(userSuspendMsg.getUpdateDate().getTime()-beginDate.getTime());
            userSuspendMsg.setDisappear("将于"+a+"后消失");
        }
        return userSuspendMsgList;
    }

    @Override
    public void updateUserSuspendMsg(UserSuspendMsg userSuspendMsg) {
        userSuspendMsgDao.update(userSuspendMsg);
    }


    @Override
    public List<UserSuspendMsg> getByContentAndToday(String content,Integer userId) {
        String hql="from UserSuspendMsg where user=?0 and content=?1 and date_format(createDate,'%Y-%m-%d')  =  date_format(?2,'%Y-%m-%d') ";


        return userSuspendMsgDao.getListByHQL(hql,userId,content,new Date());
    }
}
