package cn.sphd.miners.modules.project.entity;

import javax.persistence.*;
import java.util.Date;

/**
 * Created by Administrator on 2018/5/31.
 * 项目 附件表
 */
@Entity
@Table(name = "t_project_attachment")
public class ProjectAttachment {
    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="type"  , length=1 , nullable=true , unique=false)
    private String type;//类型:1-照片,2-其他

    @Column(name="title"  , length=255 , nullable=true , unique=false)
    private String title;//标题

    @Column(name="description"  , length=255 , nullable=true , unique=false)
    private String description;//描述

    @Column(name="path"  , length=255 , nullable=true , unique=false)
    private String path;//存放路径

    @Column(name="orders"   , nullable=true , unique=false)
    private Integer orders;//排序

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;//备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;//创建人id

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    //与项目表
    @ManyToOne(fetch= FetchType.LAZY )
    @JoinColumn(name="project", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private ProjectBase projectBase;

    @Column(name="project"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer projectBaseId;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Integer getOrders() {
        return orders;
    }

    public void setOrders(Integer orders) {
        this.orders = orders;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public ProjectBase getProjectBase() {
        return projectBase;
    }

    public void setProjectBase(ProjectBase projectBase) {
        this.projectBase = projectBase;
    }

    public Integer getProjectBaseId() {
        return projectBaseId;
    }

    public void setProjectBaseId(Integer projectBaseId) {
        this.projectBaseId = projectBaseId;
    }
}
