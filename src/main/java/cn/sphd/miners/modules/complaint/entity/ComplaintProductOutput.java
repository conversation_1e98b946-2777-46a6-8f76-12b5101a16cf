package cn.sphd.miners.modules.complaint.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Entity (name="ComplaintProductOutput")
@Table(name = "t_complaint_product_output")
public class ComplaintProductOutput implements Serializable {
  @Id
  @Column
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Integer id;
  @Column(name = "complaint_detail")
  private Integer complaintDetail;
  @Column(name = "complaint")
  private Integer complaint;
  @Column(name = "output_time")
  private Date outputTime;
  @Column(name = "product_orders")
  private Integer productOrders;
  @Column(name = "output_stock_sn")
  private Integer outputStockSn;
  @Column(name = "orders")
  private Integer orders;
  @Column(name = "memo")
  private String memo;
  @Column(name = "creator")
  private Integer creator;
  @Column(name = "create_name")
  private String createName;
  @Column(name = "create_time")
  private Date createTime;
  @Column(name = "updator")
  private Integer updator;
  @Column(name = "update_name")
  private String updateName;
  @Column(name = "update_time")
  private Date updateTime;

  @ManyToOne(fetch= FetchType.LAZY)
  @JoinColumn(name="complaint_detail", referencedColumnName = "id" , nullable=false , unique=false , insertable=false, updatable=false)
  private ComplaintDetail detailComplaintProductOutput;

  //wiajian


  public ComplaintDetail getDetailComplaintProductOutput() {
    return detailComplaintProductOutput;
  }

  public void setDetailComplaintProductOutput(ComplaintDetail detailComplaintProductOutput) {
    this.detailComplaintProductOutput = detailComplaintProductOutput;
  }

  //shujuku
  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Integer getComplaintDetail() {
    return complaintDetail;
  }

  public void setComplaintDetail(Integer complaintDetail) {
    this.complaintDetail = complaintDetail;
  }

  public Integer getComplaint() {
    return complaint;
  }

  public void setComplaint(Integer complaint) {
    this.complaint = complaint;
  }

  public Date getOutputTime() {
    return outputTime;
  }

  public void setOutputTime(Date outputTime) {
    this.outputTime = outputTime;
  }

  public Integer getProductOrders() {
    return productOrders;
  }

  public void setProductOrders(Integer productOrders) {
    this.productOrders = productOrders;
  }

  public Integer getOutputStockSn() {
    return outputStockSn;
  }

  public void setOutputStockSn(Integer outputStockSn) {
    this.outputStockSn = outputStockSn;
  }

  public Integer getOrders() {
    return orders;
  }

  public void setOrders(Integer orders) {
    this.orders = orders;
  }

  public String getMemo() {
    return memo;
  }

  public void setMemo(String memo) {
    this.memo = memo;
  }

  public Integer getCreator() {
    return creator;
  }

  public void setCreator(Integer creator) {
    this.creator = creator;
  }

  public String getCreateName() {
    return createName;
  }

  public void setCreateName(String createName) {
    this.createName = createName;
  }

  public Date getCreateTime() {
    return createTime;
  }

  public void setCreateTime(Date createTime) {
    this.createTime = createTime;
  }

  public Integer getUpdator() {
    return updator;
  }

  public void setUpdator(Integer updator) {
    this.updator = updator;
  }

  public String getUpdateName() {
    return updateName;
  }

  public void setUpdateName(String updateName) {
    this.updateName = updateName;
  }

  public Date getUpdateTime() {
    return updateTime;
  }

  public void setUpdateTime(Date updateTime) {
    this.updateTime = updateTime;
  }
}
