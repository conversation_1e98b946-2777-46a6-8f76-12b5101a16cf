package cn.sphd.miners.modules.material.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.modules.material.dao.SrmSupplierHistoryDao;
import cn.sphd.miners.modules.material.entity.SrmSupplier;
import cn.sphd.miners.modules.material.entity.SrmSupplierHistory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
@Repository
public class SrmSupplierHistoryDaoImpl extends BaseDao<SrmSupplierHistory, Serializable> implements SrmSupplierHistoryDao {
    @Override
    public Integer saveHis(SrmSupplier srmSupplier) {
        SrmSupplierHistory srmSupplierHistory=new SrmSupplierHistory();
        BeanUtils.copyProperties(srmSupplier,srmSupplierHistory);
        srmSupplierHistory.setSupplier(srmSupplier.getId());

        save(srmSupplierHistory);
        return srmSupplierHistory.getId();
    }
}
