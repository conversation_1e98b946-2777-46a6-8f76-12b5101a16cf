package cn.sphd.miners.modules.forumArea.dto;

/**
 * Created by 朱思旭 on 2018/2/3.
 */
public class PostUserMussage {

    private Integer id;
    private String name;
    private Integer userID;
    private String userName;
    private String mobile;
    private String imgPath;
    private String postName;   //岗位
    private String departName;	//所属部门
    private String isDuty;
    private String gender;		//性别  1-男

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getUserID() {
        return userID;
    }

    public void setUserID(Integer userID) {
        this.userID = userID;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getImgPath() {
        return imgPath;
    }

    public void setImgPath(String imgPath) {
        this.imgPath = imgPath;
    }

    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }

    public String getDepartName() {
        return departName;
    }

    public void setDepartName(String departName) {
        this.departName = departName;
    }

    public String getIsDuty() {
        return isDuty;
    }

    public void setIsDuty(String isDuty) {
        this.isDuty = isDuty;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public PostUserMussage(){

    }

    public PostUserMussage(Integer id, String name){
        this.id = id;
        this.name = name;
    }

    public PostUserMussage(Integer id, String name, Integer userID, String userName, String mobile, String imgPath, String postName, String departName) {
        this.id = id;
        this.name = name;
        this.userID = userID;
        this.userName = userName;
        this.mobile = mobile;
        this.imgPath = imgPath;
        this.postName = postName;
        this.departName = departName;
    }

    public PostUserMussage(Integer id, String name, Integer userID, String userName, String mobile, String imgPath, String postName, String departName, String isDuty, String gender) {
        this.id = id;
        this.name = name;
        this.userID = userID;
        this.userName = userName;
        this.mobile = mobile;
        this.imgPath = imgPath;
        this.postName = postName;
        this.departName = departName;
        this.isDuty = isDuty;
        this.gender = gender;
    }
}
