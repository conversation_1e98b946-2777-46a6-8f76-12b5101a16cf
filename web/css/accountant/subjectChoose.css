.ty-firstTab li.hd{ display: none;  }
.bonceCon input,.bonceCon select,.bonceCon textarea{
    border:1px solid #dee8f0;
    background-color: #fff;
    line-height: 36px;
    text-align: left;
    padding: 0 8px;
    color: #666;
}
.bonceCon input:focus,.bonceCon select:focus,.bonceCon textarea:focus{
    border:1px solid #5d9cec;
    color: #555;
}
.bonceCon input:disabled,.bonceCon select:disabled,.bonceCon textarea:disabled{
    background-color: #eee;
    cursor: not-allowed;
    color: #888;
}
table.ty-table td{ padding:0 5px;  }
.chooseBtn,.borrowMoreLoans,.special{
    position: relative;
    margin-top: 10px;
}
.headInfo{
    display: inline-block;
}
.headInfo .normalItem,.cashFlowGroup .normalItem{
    position: relative;
    padding:0 20px;
}
.borrowMoreLoans>.borrowItem,.borrowMoreLoans>.loanItem{
    margin-top: -1px;
    position: relative;
    padding:10px;
}
.borrowMoreLoans>.borrowItem{
    border-left: 10px solid #7fdd9e;
    background: #e1f3e9;
}
.borrowMoreLoans>.borrowItem:nth-child(odd){
    border-left: 10px solid #a1e0b1;
    background: #eaf5f2;
}
.borrowMoreLoans>.loanItem{
    border-left: 10px solid #97c2f2;
    background: #ebf0f3;
}

.borrowMoreLoans>.loanItem:nth-child(odd){
    border-left: 10px solid #7fb2ec;
    background: #dee8ed;
}
.borrowMoreLoans .mPrice{
    display: inline
}
.borrowMoreLoans .mPrice span{
    font-weight: bold;
}
.borrowMoreLoans .mNumber span,.borrowMoreLoans .mPrice span,.normalItem span{
    margin: 0 5px 0 10px;
    display: inline-block;
}
.borrowMoreLoans .mNumber span:nth-child(1),.normalItem span:nth-child(1){
    width: 78px;
}
.borrowMoreLoans .handle{
    float: right;
    height: 30px;
    line-height: 30px;
}
.borrowMoreLoans span.subjectTitle{
    font-weight: bold;
    margin: 0 5px 0 10px;
    display: inline-block;
    width: 78px;
}
.borrowMoreLoans .borrowItems{
    border-left: 10px solid #a1e0b1;
    background: #eaf5f2;
    padding: 10px;
}
.ty-table thead td {
    border-color: #d7d7d7;
    background: #e8eeeb;
    text-align: center;
}
.borrowMoreLoans .m5{
    margin-top: 5px;
}
.borrowItem .subjectBorrow,.loanItem .subjectLoan,.special .cash,.normalItem .voucher{
    width: 324px;
}
.borrowMoreLoans .price,.special .amount{
    width: 65px;
}
.borrowItem.fixed{
    margin-top: 0;
}
.loanItem.fixed{
    margin-top:10px;
}
.bookIcon:before{
    content: "\f02d";
    font-family: 'FontAwesome';
    font-weight: normal;
    font-style: normal;
    font-size: 18px;
    color: #bed3de;
    position: absolute;
    top: 12px;
    left: 400px;
    z-index: 10
}
.special{
    height: 110px;
    overflow-y: auto;
    overflow-x: hidden;
}
.special .cashFlowGroup{
    margin-bottom: 5px;
}
.subjectNo{
    position: absolute;
    width: 30px;
    height: 54px;
    line-height:54px;
    top: 0;
    left: 0;
    color: #fff;
    text-align: center;
    font-weight:bold;
}
.borrowItem .subjectNo{
    background-color: #a1e0cf;
}
.loanItem .subjectNo{
    background-color: #9bcbe6;
}
input,select{
    padding:0 10px;
}
td>div{ border-bottom:1px solid #e9e9e9 ;overflow: hidden;line-height: 1  }
td>div:after {
    display:inline-block;
    width:0;
    height:100%;
    vertical-align:middle;
    content:"";
}
td>div:last-child{ border-bottom:none;       }
.w65{
    width: 65px;
}
.cp_img_box{
    margin: 5px auto;
    padding: 0;
    height: auto;
    max-width: 250px;
}
.cp_img_box img{
    max-width: 120px;
    height: 60px;
}
.item {
    overflow: hidden;
    clear: both;
    padding:5px 0;
    margin-bottom: 8px;
}
.item input{
    color: #7e7e7e;
}

.item-title{
    display: inline-block;
    width:130px;
    text-align: right;
    margin-right: 5px;
    vertical-align: top;
    line-height:30px;
    color: #5a5e6c;
}
.item-title-big{
    display: inline-block;
    width:300px;
    text-align: right;
    margin-right: 5px;
    vertical-align: top;
    line-height:30px;
    color: #5a5e6c;
}

.item-content{
    display: inline-block;
    width:160px;
    height: 30px;
    border-radius: 2px;
    color: #7e7e7e;
    line-height:20px;
    padding: 6px 8px;
}
.item-content-big{
    display: inline-block;
    width:700px;
    border-radius: 2px;
    color: #7e7e7e;
    line-height:20px;
    padding: 6px 8px;
}