<template>
  <div id="backDetail" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="回款处置" :isBackHome="isBackHome"></TY_NavTop>
    <div class="container">
      <div class="place">
        <div class="placeContainer">
          <div class="lHead">
            <p class="line-s">{{customerName}}</p>
            <div class="listCon">
              <div class="line-p">
                <div class="tt-cell">本次回款 {{ baseInfo.method | incomeType}} {{baseInfo.amount | formatMoney}}元</div>
                <div class="con-cell blueFont" v-show="!detailState" @click="changeState">查看详情<i class="el-icon-caret-bottom"></i></div>
                <div class="con-cell blueFont" v-show="detailState" @click="changeState">收起<i class="el-icon-caret-top"></i></div>
              </div>
            </div>
          </div>
          <div v-show="detailState"> <!--1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐-->
            <div class="checkWay" v-if="baseInfo.method === '1'">
              <div class="line-s">
                <span class="tt-cl">收到日期</span>
                <span class="sub-cl">{{baseInfo.receiveDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
            </div>
            <div class="checkWay" v-if="baseInfo.method === '3'">
              <div class="line-s">
                <span class="tt-cl">收到日期</span>
                <span class="sub-cl">{{baseInfo.receiveDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
              <div class="line-s">
                <span class="tt-cl">支票号</span>
                <span class="sub-cl">{{baseInfo.returnNo}}</span>
              </div>
              <div class="line-s">
                <span class="tt-cl">到期日</span>
                <span class="sub-cl">{{baseInfo.expireDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
              <div class="line-s">
                <span class="tt-cl">出具的单位</span>
                <span class="sub-cl ellipsisSet">{{baseInfo.originalCorp}}</span>
              </div>
              <div class="line-s">
                <span class="tt-cl">出具的银行</span>
                <span class="sub-cl">{{baseInfo.bankName}}</span>
              </div>
            </div>
            <div class="checkWay" v-if="baseInfo.method === '4'">
              <div class="line-s">
                <span class="tt-cl">收到日期</span>
                <span class="sub-cl">{{baseInfo.receiveDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
              <div class="line-s">
                <span class="tt-cl">汇票号</span>
                <span class="sub-cl">{{baseInfo.returnNo}}</span>
              </div>
              <div class="line-s">
                <span class="tt-cl">到期日</span>
                <span class="sub-cl">{{baseInfo.expireDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
              <div class="line-s">
                <span class="tt-cl">最初出具的单位</span>
                <span class="sub-cl">{{baseInfo.originalCorp}}</span>
              </div>
              <div class="line-s">
                <span class="tt-cl">出具的银行</span>
                <span class="sub-cl">{{baseInfo.bankName}}</span>
              </div>
            </div>
            <div class="checkWay" v-if="baseInfo.method === '5'">
              <div class="line-s">
                <span class="tt-cl">到期日期</span>
                <span class="sub-cl">{{baseInfo.expireDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
              <div class="line-s">
                <span class="tt-cl">收款银行</span>
                <span class="sub-cl">{{baseInfo.bankName}}</span>
              </div>
            </div>
            <p class="inputerInfo">录入者：<span>{{baseInfo.createName}} {{baseInfo.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</span></p>
            <p class="inputerInfo">财&nbsp;&nbsp;&nbsp;务：<span>{{baseInfo.financerName}} {{baseInfo.approvalDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</span></p>
            <p class="inputerInfo">销&nbsp;&nbsp;&nbsp;售：<span>{{baseInfo.salerName}} {{baseInfo.salerTime | formatDate('yyyy/MM/dd hh:mm:ss')}}</span></p>
          </div>
        </div> <!--ordersState=1:已全分配完 2：需补发 3：补发完毕-->
        <p class="listTip" v-if="baseInfo.ordersState === '1' || baseInfo.ordersState === '3'">本笔回款属于以下订单：</p>
        <p class="listTip" v-else-if="orderList.length === 0">本笔回款需稍后补发新的订单。</p>
        <p class="listTip" v-else>本笔回款中{{collected}}元属于以下订单，剩余的{{noneCollected}}元需稍后补发新的订单。</p>
        <div class="order" v-for="(item, index) in orderList" :key="index">
          <div class="line-p">
            <div class="tt-cell">
              <p class="line-s">订单号 {{item.sn}}</p>
              <p class="line-s">订单金额 {{item.contractAmount | formatMoney}}元</p>
              <span class="ui-cell_lt"></span>
            </div>
            <div class="sscon-cell">本次回款  {{item.itemAmount | formatMoney}}元</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
  .tt-cell{
    position: relative;
    min-width: 50%;
  }
  .tt-cell p{
    padding:5px 0;
  }
  .ui-cell_lt{
    position:absolute;
    right:0;
    top:6px;
  }
  .ui-cell_lt:after{
    content: " ";
    display: inline-block;
    height: .6rem;
    width: .6rem;
    border-width: .1rem .1rem 0 0;
    border-color: #777;
    border-style: solid;
    transform: matrix(.71,.71,-.71,.71,0,0);
  }
  .sscon-cell{
    float: right;
    margin-top: 22px;
    margin-right: 20px;
  }
  .blueFont{
    color: #0b94ea;
  }
  .inputerInfo span{
    display: inline-block;
    min-width: 190px;
  }
  .order{
    padding: 0 10px;
  }
  .order:nth-of-type(odd){
    background:#ddd;
  }
  .order:nth-of-type(even){
    background: #fff;
  }
</style>
<script>
import { incomeType, formatDate, formatMoney } from '../../../js/common'
export default {
  name: 'collectShDetail',
  filters: {
    incomeType,
    formatDate,
    formatMoney
  },
  data () {
    return {
      loading: true,
      baseInfo: {},
      collected: 0,
      noneCollected: 0,
      orderList: [],
      customerName: '',
      detailState: false
    }
  },
  created () {
    let itemId = this.$route.params.id
    this.$http.post('../../../salesBack/collectQueriesDetail.do', {
      'id': itemId
    }, {
      emulateJSON: true
    }).then((response) => {
      let baseDate = response.body.list
      this.customerName = baseDate.customerName
      this.baseInfo = baseDate.slCollectApplication
      let orders = baseDate.collectIteam
      this.orderList = orders
      if (orders.length > 0) {
        for (var t in orders) {
          this.collected += orders[t].itemAmount
        }
        this.noneCollected = this.baseInfo['amount'] - this.collected
      }
      this.loading = false
    }).catch(function () {
      this.$message({
        type: 'error',
        message: '系统错误，请重试！'
      })
    })
  },
  methods: {
    changeState: function () {
      if (this.detailState === true) {
        this.detailState = false
      } else {
        this.detailState = true
      }
    }
  }
}
</script>
