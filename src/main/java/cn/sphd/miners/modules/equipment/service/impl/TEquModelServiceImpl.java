package cn.sphd.miners.modules.equipment.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.equipment.entity.*;
import cn.sphd.miners.modules.equipment.mapper.*;
import cn.sphd.miners.modules.equipment.service.ITEquCategoryService;
import cn.sphd.miners.modules.equipment.service.ITEquFixedAssetsService;
import cn.sphd.miners.modules.equipment.service.ITEquModelService;
import cn.sphd.miners.modules.material.dao.SrmSupplierDao;
import cn.sphd.miners.modules.material.entity.SrmSupplier;
import cn.sphd.miners.modules.material.service.SrmSupplierService;
import cn.sphd.miners.modules.material.service.UnitService;
import cn.sphd.miners.modules.system.entity.User;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
@Service
@Transactional(isolation = Isolation.DEFAULT, propagation = Propagation.REQUIRED, readOnly = false)
public class TEquModelServiceImpl implements ITEquModelService {
    @Autowired
    private TEquModelMapper tEquModelMapper;

    @Autowired
    private TEquEquipmentMapper equEquipmentMapper;

    @Autowired
    private TEquModelHistoryMapper tEquModelHistoryMapper;

    @Autowired
    private UnitService unitService;

    @Autowired
    private SrmSupplierDao supplierDao;

    @Autowired
    TEquFixedAssetsMapper fixedAssetsDao;

    @Autowired
    TEquFixedAssetsMapper fixedAssetsMapper;

    @Autowired
    TEquFixedAssetsHistoryMapper fixedAssetsHistoryMapper;

    @Autowired
    ITEquModelService equModelService;

    @Autowired
    ITEquFixedAssetsService fixedAssetsService;

    @Autowired
    ITEquCategoryService itEquCategoryService;

    @Autowired
    SrmSupplierService supplierService;
    /**
     * 查询
     *
     * @param id 主键
     * @return
     */
    @Override
    public TEquModel selectTEquModelById(Long id) {
        return tEquModelMapper.selectTEquModelById(id);
    }

    /**
     * 查询列表
     *
     * @param tEquModel
     * @return
     */
    @Override
    public List<TEquModel> selectTEquModelList(TEquModel tEquModel) {
        return tEquModelMapper.selectTEquModelList(tEquModel);
    }

    /**
     * 新增
     *
     * @param tEquModel
     * @param fixedAssets
     * @param user
     * @return 结果
     */
    @Override
    public int insertTEquModel(TEquModel tEquModel, TEquFixedAssets fixedAssets, User user) {
        tEquModel.setOrg(Long.valueOf(user.getOid()));
        tEquModel.setEnabled(1L);
        tEquModel.setCreator(Long.valueOf(user.getUserID()));
        tEquModel.setCreateName(user.getUserName());
        tEquModel.setCreateDate(new Date());

        if (tEquModel.getUnitId() != null && tEquModel.getUnitId() != 0) {
            tEquModel.setUnit(unitService.selectMtUnit(tEquModel.getUnitId().intValue()).getName());
        } else {
            tEquModel.setUnit("");
        }
        if (StringUtils.isNotBlank(tEquModel.getEquipmentName())) {
            //当自己填的与已有的一样时 直接设置id
            TEquEquipment ec = new TEquEquipment();
            ec.setOrg(Long.valueOf(user.getOid()));
            ec.setName(tEquModel.getEquipmentName());
            TEquEquipment equipment = equEquipmentMapper.selectTEquEquipmentByName(ec);
            if (equipment != null) {
                tEquModel.setEquipment(equipment.getId());
            } else {
                TEquEquipment e = new TEquEquipment();
                e.setName(tEquModel.getEquipmentName());
                e.setOrg(Long.valueOf(user.getOid()));
                e.setFullName(tEquModel.getEquipmentName());
                e.setCreator(Long.valueOf(user.getUserID()));
                e.setCreateName(user.getUserName());
                e.setCreateDate(new Date());
                equEquipmentMapper.insertTEquEquipment(e);
                tEquModel.setEquipment(e.getId());
            }

        }
        if (tEquModel.getSupplier() != null) {
            SrmSupplier supplier = supplierDao.get(tEquModel.getSupplier().intValue());
            if (supplier != null) {
                supplier.setSupplyCount(supplier.getSupplyCount() == null ? 1 : supplier.getSupplyCount() + 1);
                supplierDao.update(supplier);
            }
        }


        TEquEquipment eq = equEquipmentMapper.selectTEquEquipmentById(tEquModel.getEquipment());
        if (eq != null) {
            //数量加1
            eq.setModelCount(eq.getModelCount() == null ? 1L : eq.getModelCount() + 1L);
            eq.setQuantity(eq.getQuantity() == null ? 1L : eq.getQuantity() + 1L);
            //来源看情况加1
            if (eq.getSupplierCount() == null) {
                eq.setSupplierCount(1L);
            } else {
                TEquModel m = new TEquModel();
                m.setOrg(Long.valueOf(user.getOid()));
                m.setSupplier(tEquModel.getSupplier());
                m.setEquipment(tEquModel.getEquipment());

                List<TEquModel> nows = tEquModelMapper.selectTEquModelList(m);
                if (nows == null || nows.size() == 0) {
                    eq.setSupplierCount(eq.getSupplierCount() + 1L);
                }
            }

            StringBuilder b = new StringBuilder(eq.getUnits() == null ? "" : eq.getUnits());
            //更新units
            if (StringUtils.isBlank(eq.getUnits())) {
                b.append(tEquModel.getUnit());
            } else {
                b.insert(0, tEquModel.getUnit() + "/");
            }
            eq.setUnits(b.toString().contains("/") ? "--" : b.toString());
            equEquipmentMapper.updateTEquEquipment(eq);
        }

        tEquModelMapper.insertTEquModel(tEquModel);


        return 1;
    }

    /**
     * 修改
     *
     * @param tEquModel
     * @param user
     * @return 结果
     */
    @Override
    public int updateTEquModel(TEquModel tEquModel, User user) {
        //尽量让前端少改代码，此处tEquModel现在传的时assets表的id  后面等前端闲下来再改回来

        TEquFixedAssets fixedAssets = fixedAssetsMapper.selectTEquFixedAssetsById(tEquModel.getId());


        TEquModel now = tEquModelMapper.selectTEquModelById(fixedAssets.getModel());

        tEquModel.setId(fixedAssets.getModel());

        if (tEquModel.getEmCount() == null)
            tEquModel.setQuantity(now.getQuantity());
        if (now.getSupplier() != null) {
            SrmSupplier supplier = supplierDao.get(now.getSupplier().intValue());
            if (supplier != null) {
                supplier.setSupplyCount(supplier.getSupplyCount() == null ? 0 : supplier.getSupplyCount() - 1);
                supplierDao.update(supplier);
            }
        }
        Long modelVersionNo = 0L;
        TEquModelHistory h = new TEquModelHistory();
        h.setModel(now.getId());
        h.setOperation(tEquModel.getOperation());
        List<TEquModelHistory> modelHistories = tEquModelHistoryMapper.selectTEquModelHistoryList(h);
        if (modelHistories == null || modelHistories.isEmpty()) {
            TEquModelHistory history = new TEquModelHistory();
            BeanUtils.copyProperties(now, history);
            history.setModel(now.getId());
            history.setCreateDate(now.getCreateDate());
            history.setCreateName(user.getUserName());
            history.setCreator(Long.valueOf(user.getUserID()));
            history.setVersionNo(modelVersionNo);
            history.setOperation(tEquModel.getOperation());
            history.setQuantity(tEquModel.getQuantity());
            history.setId(null);
            tEquModelHistoryMapper.insertTEquModelHistory(history);
        }
        modelHistories = tEquModelHistoryMapper.selectTEquModelHistoryList(h);

        modelVersionNo = modelHistories.stream().max(Comparator.comparing(TEquModelHistory::getVersionNo)).get().getVersionNo() + 1;



//        if (now.getCreator() == null)
//            tEquModel.setCreator(Long.valueOf(user.getUserID()));
//        if (StringUtils.isBlank(now.getCreateName()))
//            tEquModel.setCreateName(user.getUserName());
//        if (now.getCreateDate() == null)
//            tEquModel.setCreateDate(new Date());
        tEquModel.setEnabled(1L);
        tEquModel.setOrg(Long.valueOf(user.getOid()));


        //保存历史
        TEquModelHistory history = new TEquModelHistory();
        BeanUtils.copyProperties(tEquModel, history);
        history.setModel(now.getId());
        history.setCreateDate(new Date());
        history.setCreateName(user.getUserName());
        history.setCreator(Long.valueOf(user.getUserID()));
        history.setVersionNo(modelVersionNo);
        history.setOperation(tEquModel.getOperation());
        history.setQuantity(tEquModel.getQuantity());
        tEquModelHistoryMapper.insertTEquModelHistory(history);


        //新的+1
        if (tEquModel.getSupplier() != null) {
            SrmSupplier supplier = supplierDao.get(tEquModel.getSupplier().intValue());
            if (supplier != null) {
                supplier.setSupplyCount(supplier.getSupplyCount() == null ? 1 : supplier.getSupplyCount() + 1);
                supplierDao.update(supplier);
            }
        }


        //修改公共信息时  不产生固定资产的修改记录
        if(7==tEquModel.getOperation()){
            Integer assetsVersionNo = 0;
            TEquFixedAssetsHistory f =new TEquFixedAssetsHistory();
            f.setFixedAssets(fixedAssets.getId());
            List<TEquFixedAssetsHistory> fixedAssetsHistories = fixedAssetsHistoryMapper.selectTEquFixedAssetsHistoryList(f);
            if (fixedAssetsHistories == null || fixedAssetsHistories.isEmpty()) {
                TEquFixedAssetsHistory ah = new TEquFixedAssetsHistory();
                BeanUtils.copyProperties(fixedAssets, ah);
                ah.setModel(now.getId());
                ah.setFixedAssets(tEquModel.getId());
                ah.setCreateDate(fixedAssets.getCreateDate());
                ah.setCreateName(user.getUserName());
                ah.setCreator(user.getUserID());
                ah.setVersionNo(assetsVersionNo);
                fixedAssetsHistoryMapper.insertTEquFixedAssetsHistory(ah);
            }
            fixedAssetsHistories = fixedAssetsHistoryMapper.selectTEquFixedAssetsHistoryList(f);
            Optional<TEquFixedAssetsHistory> maxVersionOptional = fixedAssetsHistories.stream()
                    .max(Comparator.comparing(TEquFixedAssetsHistory::getVersionNo));

            // 设置默认值
            assetsVersionNo = maxVersionOptional.map(tEquFixedAssetsHistory -> tEquFixedAssetsHistory.getVersionNo() + 1).orElse(1);

            if (StringUtils.isNotBlank(tEquModel.getrDate())) {
                SimpleDateFormat sd = new SimpleDateFormat("yyyyMM");
                Date d = null;
                try {
                    d = sd.parse(tEquModel.getrDate());
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
                fixedAssets.setReceiveDate(d);
            }
            fixedAssets.setCode(tEquModel.getModelCode());
            fixedAssets.setEquipment(tEquModel.getEquipment());
            fixedAssets.setModel(now.getId());
            fixedAssets.setOriginalValue(tEquModel.getOriginalValue());
            fixedAssets.setLifeSpan(tEquModel.getLifeSpan());
            fixedAssets.setConditions(tEquModel.getConditions());
            fixedAssets.setCategory(Math.toIntExact(tEquModel.getCategory()));
            fixedAssets.setMemo(tEquModel.getMemo());


            TEquFixedAssetsHistory fixedAssetsHistory = new TEquFixedAssetsHistory();
            BeanUtils.copyProperties(fixedAssets, fixedAssetsHistory);
            fixedAssetsHistory.setFixedAssets(fixedAssets.getId());
            fixedAssetsHistory.setCreator(user.getUserID());
            fixedAssetsHistory.setCreateDate(new Date());
            fixedAssetsHistory.setVersionNo(assetsVersionNo);
            fixedAssetsHistory.setCreateName(user.getUserName());
            fixedAssetsHistoryMapper.insertTEquFixedAssetsHistory(fixedAssetsHistory);


            fixedAssetsMapper.updateTEquFixedAssets(fixedAssets);
        }

        return tEquModelMapper.updateTEquModel(tEquModel);
    }

    /**
     * 批量删除
     *
     * @param ids 需要删除的主键
     * @return 结果
     */
    @Override
    public int deleteTEquModelByIds(String... ids) {
        return tEquModelMapper.deleteTEquModelByIds(ids);
    }

    /**
     * 删除信息
     *
     * @param id 主键
     * @return 结果
     */
    @Override
    public int deleteTEquModelById(Long id) {
        return tEquModelMapper.deleteTEquModelById(id);
    }

    /**
     * @param tEquModel
     * @return
     */
    @Override
    public List<Map<String, Object>> selectTEquModelGroupList(TEquModel tEquModel) {
        return tEquModelMapper.selectTEquModelGroupList(tEquModel);
    }

    @Override
    public List<Map<String, Object>> selectEquModelDetailList(TEquModel tEquModel) {
        return tEquModelMapper.selectEquModelDetailList(tEquModel);
    }

    @Override
    public Map selectEquModelDetailList(TEquModel tEquModel, PageInfo pageInfo) {
        Map map = new HashMap();
        List<Map<String, Object>> data = tEquModelMapper.selectEquModelDetailList(tEquModel);
        if (pageInfo != null) {
            pageInfo.setTotalResult(data == null ? 0 : data.size());
            pageInfo.setTotalPage((pageInfo.getTotalResult() - 1) / pageInfo.getPageSize() + 1);
            //        从第几条数据开始
            int firstIndex = (pageInfo.getCurrentPageNo() - 1) * pageInfo.getPageSize();
            //        到第几条数据结束
            int lastIndex = pageInfo.getCurrentPageNo() * pageInfo.getPageSize();

            if (data != null)
                if (lastIndex > data.size()) {
                    lastIndex = data.size();
                }
            map.put("data", data == null ? null : data.subList(firstIndex, lastIndex));
        } else {
            map.put("data", data == null ? null : data);
        }

        map.put("pageInfo", pageInfo);
        //查询全部数量
        TEquModel all = new TEquModel();
        all.setOrg(tEquModel.getOrg());
        all.setCategory(null);
        data = tEquModelMapper.selectEquModelDetailList(all);
        map.put("all", data.size());

        //查询未分类数量
        all.setCategory(-1L);
        data = tEquModelMapper.selectEquModelDetailList(all);
        map.put("noCategory", data.size());
        //查询未分类数量
        all.setCategory(-2L);
        data = tEquModelMapper.selectEquModelDetailList(all);
        map.put("noc", data.size());

        //查询已分类数量
        all.setCategory(0L);
        data = tEquModelMapper.selectEquModelDetailList(all);
        map.put("yetCategory", data.size());



        //查询一共多少一级类别
        TEquCategory c = new TEquCategory();
        c.setOrg(Long.valueOf(tEquModel.getOrg()));
        c.setParent(0L);
        List<TEquCategory> initList = itEquCategoryService.selectTEquCategoryList(c);
        map.put("rootCategoryCount", initList==null?0:initList.size());

        //查询一共多少供应商
        List<SrmSupplier> list = supplierService.getSupplierByEnabled(Math.toIntExact(tEquModel.getOrg()),1);

        map.put("supplierCount", list==null?0:list.size());
        List<SrmSupplier> list1 = supplierService.getSupplierByEnabled(Math.toIntExact(tEquModel.getOrg()),0);
        map.put("suspendSupplierCount", list1==null?0:list1.size());
        return map;
    }

    public int batchClassification(String[] ids) {
        if (ids != null) {
            for (String id : ids) {
                String[] s = id.split("-");
                Long categoryId = Long.valueOf(s[0]);
                for (int i = 1; i < s.length; i++) {
                    Long mid = Long.valueOf(s[i]);

                    TEquFixedAssets fixedAssets = fixedAssetsDao.selectTEquFixedAssetsById(Long.valueOf(mid.intValue()));
                    if (fixedAssets != null) {
                        fixedAssets.setCategory(Math.toIntExact(categoryId));
                        fixedAssetsDao.updateTEquFixedAssets(fixedAssets);
                    }
//                    TEquModel model = tEquModelMapper.selectTEquModelById(mid);
//                    model.setCategory(categoryId);
//
//                    tEquModelMapper.updateTEquModel(model);
                }
            }
        } else {
            return -1;
        }
//
//        if (StringUtils.isNotBlank(ids)) {
//            for (String id : ids.split(",")) {
//                Long mid = Long.valueOf(id);
//
//                TEquModel model = tEquModelMapper.selectTEquModelById(mid);
//                model.setCategory(categoryId);
//
//                tEquModelMapper.updateTEquModel(model);
//            }
//
//        } else return -1;
        return 1;
    }

    @Override
    public Map geModelDetail(Long id) {
        return tEquModelMapper.getModelDetail(id);
    }

    @Override
    public List<Map<String, Object>> selectEquModelDetailListNoGroup(TEquModel tEquModel) {
        return tEquModelMapper.selectEquModelDetailListNoGroup(tEquModel);
    }

    @Override
    public void updateTEquModelByModel(TEquModel model, User user) {
        model.setUpdateDate(new Date());
        model.setUpdateName(user.getUserName());
        tEquModelMapper.updateTEquModel(model);
    }

    @Override
    public void batchModelAdd(List<TEquModel> tEquModels, User user) {
        //改表之后，model只加一次，拆分出的字段增加多次
        TEquModel m = tEquModels.get(0);
        if (m.getSupplier() == null) {
            m.setSupplier(0L);
        }
        if (m.getUnitId() == null) {
            m.setUnitId(0L);
        }
        if (StringUtils.isBlank(m.getModelName())) {
            m.setModelName("--");
        }
        equModelService.insertTEquModel(m, null, user);

        for (TEquModel tEquModel : tEquModels) {
            TEquFixedAssets fixedAssets = new TEquFixedAssets();
            //1.292将字段移到了新表
            if (StringUtils.isNotBlank(tEquModel.getrDate())) {
                SimpleDateFormat sd = new SimpleDateFormat("yyyyMM");
                try {
                    fixedAssets.setReceiveDate(sd.parse(tEquModel.getrDate()));
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }
            }
            fixedAssets.setLifeSpan(tEquModel.getLifeSpan());
            fixedAssets.setConditions(tEquModel.getConditions());
            fixedAssets.setOriginalValue(tEquModel.getOriginalValue());
            fixedAssets.setCode(tEquModel.getModelCode());
            fixedAssets.setOrg(user.getOid());
            fixedAssets.setOperation(1);
            fixedAssets.setMemo(tEquModel.getMemo());
            if (tEquModel.getCategory() != null)
                fixedAssets.setCategory(tEquModel.getCategory().intValue());
            fixedAssets.setModel(m.getId());
            fixedAssets.setCreateDate(new Date());
            fixedAssets.setCreateName(user.getUserName());
            fixedAssets.setEquipment(m.getEquipment());
            fixedAssets.setEnabled(1);
            fixedAssets.setEnabledTime(new Date());
            fixedAssetsService.insertTEquFixedAssets(fixedAssets);
        }

    }
}
