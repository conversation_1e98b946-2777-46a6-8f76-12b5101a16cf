package cn.sphd.miners.modules.publicInformation.entity;


import cn.sphd.miners.common.persistence.BaseEntity;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table ( name ="t_pi_info_visit")
public class PiInfoVisit extends BaseEntity {

    @Id
    @Column
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;//`id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
    @Column(name = "org")
    private Integer org;
    @Column(name = "file")
    private Integer file;
    @Column(name = "type")
    private Byte type;
    @Column(name = "memo")
    private String memo;
    @Column(name = "creator")
    private Integer creator;
    @Column(name = "create_name")
    private String createName;
    @Column(name = "create_date")
    private Date createDate;
    @Column(name = "updator")
    private Integer updator;
    @Column(name = "update_name")
    private String updateName;
    @Column(name = "update_date")
    private Date updateDate;


    public Integer getId() {
      return id;
    }

    public void setId(Integer id) {
      this.id = id;
    }

    public Integer getOrg() {
      return org;
    }

    public void setOrg(Integer org) {
      this.org = org;
    }

    public Integer getFile() {
      return file;
    }

    public void setFile(Integer file) {
      this.file = file;
    }

    public Byte getType() {
      return type;
    }

    public void setType(Byte type) {
      this.type = type;
    }

    public String getMemo() {
      return memo;
    }

    public void setMemo(String memo) {
      this.memo = memo;
    }

    public Integer getCreator() {
      return creator;
    }

    public void setCreator(Integer creator) {
      this.creator = creator;
    }

    public String getCreateName() {
      return createName;
    }

    public void setCreateName(String createName) {
      this.createName = createName;
    }

    public Date getCreateDate() {
      return createDate;
    }

    public void setCreateDate(Date createDate) {
      this.createDate = createDate;
    }

    public Integer getUpdator() {
      return updator;
    }

    public void setUpdator(Integer updator) {
      this.updator = updator;
    }

    public String getUpdateName() {
      return updateName;
    }

    public void setUpdateName(String updateName) {
      this.updateName = updateName;
    }

    public Date getUpdateDate() {
      return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
      this.updateDate = updateDate;
    }
}
