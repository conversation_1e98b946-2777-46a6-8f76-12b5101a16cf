package cn.sphd.miners.modules.generalAffairs.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * Created by Administrator on 2018/4/11.
 * 员工考勤明细历史表
 */
@Entity
@Table(name = "t_personnel_attendance_user_detail_history")
public class PersonnelAttendanceUserDetailHistory implements Serializable {

    @Id
    @Column(name="id" )
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name="user"   , nullable=true , unique=false)
    private Integer user;//用户id ，考虑到以前好多接口，暂时未加集联关系

    @Column(name="org"   , nullable=true , unique=false)
    private Integer org;//机构id ，考虑到以前好多接口，暂时未加集联关系

    @Column(name="attendance"   , nullable=true , unique=false)
    private Integer attendance;//考勤历史表id

    @Column(name="attendance_date"   , nullable=true , unique=false)
    private Date attendanceDate;//考勤日期

    @Column(name="type"  , length=1 , nullable=true , unique=false)
    private String type;//类型：1-正常,2--迟到,3-早退,4-外出,5-请假,6-出差,7-旷工,8-加班

    @Column(name="state"  , length=1 , nullable=true , unique=false)
    private String state;//状态:1-上班,2-下班

    @Column(name="source"  , length=1 , nullable=true , unique=false)
    private String source;//来源:1-审批,2-录入

    @Column(name="begin_time"   , nullable=true , unique=false)
    private Date beginTime;//上班时间

    @Column(name="end_time"   , nullable=true , unique=false)
    private Date endTime;//下班时间

    @Column(name="business"   , nullable=true , unique=false)
    private Integer business;//业务ID 加班和请假分别对应于t_personnel_overtime、t_personnel_leave的主键ID',

    @Column(name="duration"   , nullable=true , unique=false)
    private Double duration;//时长

    @Column(name="reason"  , length=255 , nullable=true , unique=false)
    private String reason;//事由

    @Column(name="business_type"  , length=2 , nullable=true , unique=false)
    private String businessType;//业务类型:其中 请假类型:1-事假,2-病假,3-年假,4-调休,5-婚假,6-产假,7-陪产假,8-路途假,9-其他

    @Column(name="memo"  , length=255 , nullable=true , unique=false)
    private String memo;//备注

    @Column(name="creator"   , nullable=true , unique=false)
    private Integer creator;

    @Column(name="create_name"  , length=100 , nullable=true , unique=false)
    private String createName;

    @Column(name="create_date"   , nullable=true , unique=false)
    private Date createDate;

    @Column(name="updator"   , nullable=true , unique=false)
    private Integer updator;

    @Column(name="update_name"  , length=100 , nullable=true , unique=false)
    private String updateName;

    @Column(name="update_date"   , nullable=true , unique=false)
    private Date updateDate;

    @Column(name="leave_type"   , nullable=true , unique=false)
    private Integer leaveType;  //请假类型id

    //与员工考勤明细表
    @JsonIgnore @JSONField(serialize = false)
    @ManyToOne(fetch= FetchType.EAGER )
    @JoinColumn(name="detail", referencedColumnName = "id" , nullable=true , unique=false , insertable=true, updatable=true)
    private PersonnelAttendanceUserDetail detail;

    @Column(name="detail"  , nullable=true , unique=false, insertable=false, updatable=false)
    private Integer detailId;//考勤明细id

    @Transient
    private String userName;  //职工姓名

    @Transient
    private String leaveTypeName;  //请假类型名称

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUser() {
        return user;
    }

    public void setUser(Integer user) {
        this.user = user;
    }

    public Integer getOrg() {
        return org;
    }

    public void setOrg(Integer org) {
        this.org = org;
    }

    public Integer getAttendance() {
        return attendance;
    }

    public void setAttendance(Integer attendance) {
        this.attendance = attendance;
    }

    public Date getAttendanceDate() {
        return attendanceDate;
    }

    public void setAttendanceDate(Date attendanceDate) {
        this.attendanceDate = attendanceDate;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getBusiness() {
        return business;
    }

    public void setBusiness(Integer business) {
        this.business = business;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public PersonnelAttendanceUserDetail getDetail() {
        return detail;
    }

    public void setDetail(PersonnelAttendanceUserDetail detail) {
        this.detail = detail;
    }

    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer detailId) {
        this.detailId = detailId;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Double getDuration() {
        return duration;
    }

    public void setDuration(Double duration) {
        this.duration = duration;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public Integer getLeaveType() {
        return leaveType;
    }

    public void setLeaveType(Integer leaveType) {
        this.leaveType = leaveType;
    }

    public String getLeaveTypeName() {
        return leaveTypeName;
    }

    public void setLeaveTypeName(String leaveTypeName) {
        this.leaveTypeName = leaveTypeName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
}
