/*//装备器具 - 装备清单 js*/
.mainCon{
    display: none;
    padding: 30px;
}
.maintip{
    width: 500px;
    margin:100px 0 0 100px;
}
.maintip>p:nth-child(3){
    margin-top: 40px;
}
.maintip>p:nth-child(3)>span:nth-child(2){
    margin-left: 100px;
}
#litAdd table{
    width: 100%;
}
#litAdd td{
    padding:6px 10px;
    position: relative;
    border: 1px solid #ccc;
}
#litAdd td input, #litAdd td select{
    max-width: 150px;
}
#litAdd tr.noBr td{
    border: none;
}
.linkBtn{
    color: #0b94ea;
    display: inline-block;
    text-decoration: none;
    cursor: pointer;
}
.linkBtn:hover{
    text-decoration: underline;
}
.litInput{
    padding-right: 50px!important;
}
.litUnit{
    position: absolute;
    top: 10px;
    right: 20px;
}
.textarea{
    width: 100% !important;
}
.limtRole{
    float: right;
}
.color-red{
    color: #e04f3c!important;
}
.exportStep {
    padding-left: 40px;
}
.stepItem {
    margin-bottom: 20px;
}
.flexRow {
    margin-left: 50px;
    display: flex;
    justify-content: space-between;
    width: 356px;
}
#uploadAdd{
    width: 550px;
}
#tab2{
    margin-top: 30px;
}
.inputLit{
    width: 150px;
}
#catContainer{
    max-height: 300px;
    overflow: auto;
    border: 1px solid #eee;
}
#catContainer li{
    line-height: 25px;
}
#catContainer .catName{
    cursor: pointer;
    padding:0 10px;
}
#catContainer .catName:hover{
    background: #b7d4e7;
}
#catContainer .catName.selectedCat{
    background:#0b94ea ;
    color:#fff;
}
#catContainer ul ul{
    margin-left: 30px;
    width: 300px;
    border-left: 1px solid #0b94ea;
}
.preTab{
    width: 100%;
    margin-bottom: 30px;
}
.preTab td{
    vertical-align: sub;
}
/*批量导入*/
.exportStep {  padding-left: 40px;  }
.flexRow{  margin-left: 50px;  display: flex;  justify-content:space-between;  width: 356px;  }
.fileFullName{  width: 300px; line-height:24px;background: #fff;text-align: center;}
.importSect{padding-top: 20px;margin-right: 60px;}
.narrowBody{margin: 0 auto;width: 80%;}
.changeDot{margin-bottom: 12px;}
.changeDot span{margin-right: 12px;}
.stepItem {  margin-bottom: 20px;  }
.viewBtn .uploadify-button{ padding: 0 12px;  margin: 0;display: inline-block; border-radius: 0; height: 24px;  line-height: 26px;background-color: #5d9cec;  color: #fff;border: none;}
.viewBtn .uploadify-button:hover{ background: #5d9cec;}
.mainCon3{display: none;}
.mainCon3 .importCon2 select{ border: 1px solid #d7d7d7;}
.mainCon3 .importCon2 input{width: 120px; border: 1px solid #d7d7d7;text-align: center;}
.bg-yellow thead .tdSpecial{border-bottom: 1px solid #d7d7d7;}
.gap-lt{margin-left: 50px;}
.ty-table td .darkGray{color: #aaa;font-style: normal;}
.mTtl{display: inline-block;width: 200px;}
.md-out{margin-bottom: 20px;}
/*补录*/
#msgCon{margin: 30px auto 0; width: 80%;}
.eqOtherItems{margin-top: 50px;}
