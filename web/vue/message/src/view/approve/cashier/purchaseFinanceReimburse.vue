<template>
  <div id="purchaseFinanceReimburse" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="付款复核" isSearch="true" @toggleList="search"></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待复核" name="1" closable='false' :msgCount="financeReview.length">
        <div class="tip">
          <p>以下待付款项有待您复核。</p>
        </div>
        <div>
          <a class="ui-cell" v-on:click="jump('51' + item.poPaymentApplication.id,0,0, item)" v-for="(item, index) in financeReview" v-bind:key="index" v-if="item.poPaymentApplication !== null">
            <div class="item_fn">
              <div class="supName">{{item.fullName}} <span class="right">{{ item.codeName }}</span></div>
              <div class="oInfo">
                <p> {{ item.poPaymentApplication.type | formatType(item.poPaymentApplication.amount) }}</p>
                <p v-if="Number(item.poPaymentApplication.billCount) > 0">票据 {{item.poPaymentApplication.billCount }} 张，共 {{item.poPaymentApplication.billAmount.toFixed(2) }} 元</p>
                <p v-else>本次付款申请没有附带的票据</p>
              </div>
            </div>
            <div class="txt_right">申请人：{{ item.poPaymentApplication.createName  }}
              {{item.poPaymentApplication.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
          <a class="ui-cell" v-on:click="jump('71' + item.poOrderPrepayment.id,0,1,item)" v-for="(item,index) in financeReview" v-bind:key="index" v-if="item.poOrderPrepayment !== null">
            <div class="item_fn">
              <div class="supName">{{item.fullName}} <span class="right">{{ item.codeName }}</span></div>
              <div class="oInfo">
                <p>计划付款金额&nbsp;{{item.poOrderPrepayment.planAmount}}元</p>
                <p>计划付款时间&nbsp;{{item.poOrderPrepayment.planDate |formatDay('YYYY-MM-DD')}}</p>
              </div>
            </div>
            <div class="txt_right">申请人：{{ item.poOrders.createName  }}
              {{item.poOrders.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="待付款" name="2" closable='false'>
        <div class="tip">
          <p>下列待付款项复核通过，有待付款。</p>
        </div>
        <div>
          <a class="ui-cell" v-on:click="jump('52' + item.poPaymentApplication.id,1,0,item)" v-for="(item, index) in cashierTwoSettled" v-bind:key="index" v-if="item.poPaymentApplication !== null">
            <div class="item_fn">
              <div class="supName">{{item.fullName}} <span class="right">{{ item.codeName }}</span></div>
              <div class="oInfo">
                <p> {{ item.poPaymentApplication.type | formatType(item.poPaymentApplication.amount) }}</p>
                <p v-if="Number(item.poPaymentApplication.billCount) > 0">票据 {{item.poPaymentApplication.billCount }} 张，共 {{item.poPaymentApplication.billAmount.toFixed(2) }} 元</p>
                <p v-else>本次付款申请没有附带的票据</p>
              </div>
            </div>
            <div class="txt_right">申请人：{{ item.poPaymentApplication.createName  }}
              {{item.poPaymentApplication.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
          <a class="ui-cell" v-on:click="jump('72' + item.poOrderPrepayment.id,1,1,item)" v-for="(item,index) in cashierTwoSettled" v-bind:key="index" v-if="item.poOrderPrepayment !== null">
            <div class="item_fn">
              <div class="supName">{{item.fullName}} <span class="right">{{ item.codeName }}</span></div>
              <div class="oInfo">
                <p>计划付款金额&nbsp;{{item.poOrderPrepayment.planAmount}}元</p>
                <p>计划付款时间&nbsp;{{item.poOrderPrepayment.planDate |formatDay('YYYY-MM-DD')}}</p>
              </div>
            </div>
            <div class="txt_right">申请人：{{ item.poOrders.createName  }}
              {{item.poOrders.createDate |formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
            <div class="ui-cell__ft"></div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>
<style lang="less">
#purchaseFinanceReimburse{
  .tabs-tab{ width:50%;  }
  .right{ float: right;  }
  .ui-cell{ display: block;  }
  .oInfo{ padding:5px 20px; }
  .txt_right{ text-align: right;  }
  .tabCount{
    top:0;
    right:0 ;
    left:auto;
  }
  .tip{
    padding:5px 12px;
    background-color: #E2EFDA;
    display: block;
  }
}
</style>

<script>
var that
export default {
  name: 'reviewFinanceReimburse',
  data () {
    return {
      cashierTwoSettled: [],
      financeReview: [],
      listenersUid: [],
      tabValue: '1'
    }
  },
  filters: {
    formatType: function (num, amount) {
      let str = ''
      switch (Number(num)) {
        case 1:
          str = '本次仅提交票据，不付款'
          break
        case 2:
          str = `申请付款${amount.toFixed(2)}元`
          break
        case 3:
          str = `申请付款${amount.toFixed(2)}元`
          break
      }
      return str
    },
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  created () {
    this.tabValue = localStorage.navNum || '1'
    that = this
    this.listenersUid = [
      // 待复核
      this.sphdSocket.subscribe('paymentReviewHandle', function (data) {
        let getData = JSON.parse(data)
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.financeReview.unshift(getData)
        } else if (operate < 0) {
          let id = getData.poPaymentApplication.id
          let _index = -1
          that.financeReview.forEach(function (item, index) {
            if (item.poPaymentApplication.id === id) {
              _index = index
            }
          })
          if (_index > -1) {
            that.financeReview.splice(_index, 1)
          }
        }
      }, null, 'user'),
      // 待付款
      this.sphdSocket.subscribe('paymentReviewApproval', function (data) {
        let getData = JSON.parse(data)
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.cashierTwoSettled.unshift(getData)
        } else if (operate < 0) {
          let id = getData.poPaymentApplication.id
          let _index = -1
          that.cashierTwoSettled.forEach(function (item, index) {
            if (item.poPaymentApplication.id === id) {
              _index = index
            }
          })
          if (_index > -1) {
            that.cashierTwoSettled.splice(_index, 1)
          }
        }
      }, null, 'user'),
      // 付款复核 - 待处理
      this.sphdSocket.subscribe('reviewedApprovalHandle', function (data) {
        let getData = JSON.parse(data)
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.financeReview.unshift(getData)
        } else if (operate < 0) {
          let id = getData.poPaymentApplication.id
          let _index = -1
          that.financeReview.forEach(function (item, index) {
            if (item.poPaymentApplication.id === id) {
              _index = index
            }
          })
          if (_index > -1) {
            that.financeReview.splice(_index, 1)
          }
        }
      }, null, 'user'),
      // 付款复核 - 已批准
      this.sphdSocket.subscribe('reviewedApprovalApproved', function (data) {
        let getData = JSON.parse(data)
        let operate = Number(getData.operate)
        if (operate > 0) {
          that.cashierTwoSettled.unshift(getData)
        } else if (operate < 0) {
          let id = getData.poPaymentApplication.id
          let _index = -1
          that.cashierTwoSettled.forEach(function (item, index) {
            if (item.poPaymentApplication.id === id) {
              _index = index
            }
          })
          if (_index > -1) {
            that.cashierTwoSettled.splice(_index, 1)
          }
        }
      }, null, 'user')
    ]
    that.getList()
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    getList: function () {
      let that = this
      this.axios.get('../../../purchaseInvoice/reviewedApprovalList.do')
        .then(function (response) {
          let res = response.data.data
          that.loading = false
          that.financeReview = res.reviewedApprovalHandle || []
          that.cashierTwoSettled = res.reviewedApprovalApproved || []
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    jump: function (id, num, pun, item) { // num用于判断是待复核还是待付款，pun则用于判断是票据数据还是预付款数据
      let businessGroup = Number(item.businessGroup || 0)
      if (num === 0) { // 待复核
        this.$router.push({
          path: `/purchasePaymentFinanceApprovalDetail/${id}/${pun}/${businessGroup}`
        })
      } else if (num === 1) { // 待付款
        this.$router.push({
          path: `/purchasePaymentFinanceApprovalDetail2/${id}/${pun}/${businessGroup}`
        })
      }
    },
    search: function () {
      this.$router.push({
        path: `/reviewApproveFinanceSearch`
      })
    }
  }
}
</script>
