<template xmlns:v-bind="http://www.w3.org/1999/xhtml">
  <div id="approvalSetting" v-loading.fullscreen.lock="loading">
    <TY_NavTop :title="ttl" :isBackHome="isBackHome"></TY_NavTop>
    <div class="container">
      <div class="ttl1">您可更换查询条件，以得到其他查询结果。</div>
      <div class="cc1">
        <p class="cc2">提交时间</p>
        <div>
          <span class="btn" v-bind:style="{ 'color': color11, 'border-color':color11 }" @click='toggle(1,1)'>近七日</span>
          <span class="btn" v-bind:style="{ 'color': color21, 'border-color':color21 }" @click='toggle(2,1)'>本月</span>
          <span class="btn" v-bind:style="{ 'color': color31, 'border-color':color31 }" @click='toggle(3,1)'>自定义</span>
        </div>
        <div v-if="showDialog == 1" class="cc3">
          <el-date-picker type="date" placeholder="请选择" value-format="yyyy-MM-dd" v-model="beginTime" size="small" style="width:40%"></el-date-picker>
          <el-date-picker type="date" placeholder="请选择" value-format="yyyy-MM-dd" v-model="endTime" size="small" style="width:40%"></el-date-picker>
        </div>
      </div>
      <div>
        <p class="cc2">审批结果</p>
        <div>
          <span class="btn" v-bind:style="{ 'color': color22, 'border-color':color22 }" @click='toggle(2,2)'>已批准</span>
          <span class="btn" v-bind:style="{ 'color': color32, 'border-color':color32 }"  @click='toggle(3,2)'>已驳回</span>
        </div>
      </div>
      <div class="handle_button">
        <span @click='goDetails()' class="ui-btn ui-btn_info">确定</span>
      </div>
    </div>
  </div>
</template>
<style lang="less">
  #approvalSetting{
    .ttl1{ background:#DEEBF6; padding:10px;     }
    .clr2{clear: both;  }
    .cc1{ border-bottom:1px solid #ccc; padding-bottom:10px;   }
    .btn{ border:1px solid #ccc;padding:5px 15px; border-radius:4px;margin:10px; width: 5rem; display: inline-block; text-align: center;   }
    .active{ border-color:#0da394; color: #0da394;  }
    .cc2{ padding:10px 0 0 15px; font-size:1.2rem;  }
    .cc3{ padding:0 0 0 15px;  }
  }
</style>
<script>
export default {
  name: 'approvalSettingsSearch',
  data () {
    return {
      searchInfo: this.$store.getters.getApprovalSettingSearchInfo,
      showDialog: 0,
      applyTime: 1,
      ttl: '审批设置的修改申请',
      chargeResult: 2,
      beginTime: '',
      endTime: '',
      color11: '',
      color21: '',
      color31: '',
      color22: '',
      color32: '',
      searchType: Number(this.$route.params.id) // 1-申请人 2-审批人
    }
  },
  created: function () {
    let _this = this
    console.log('searchInfo', _this.searchInfo)
    console.log('searchType', _this.searchType)
    if (_this.searchType === 2) {
      _this.ttl = '审批设置的修改审批'
    }
    if (_this.searchInfo.applyTime) {
      _this.applyTime = _this.searchInfo.applyTime
      _this['color' + _this.applyTime + '1'] = '#0da394'
      if (_this.applyTime === 3) {
        _this.showDialog = 1
        _this.beginTime = _this.searchInfo.beginTime
        _this.endTime = _this.searchInfo.endTime
      }
      _this.chargeResult = _this.searchInfo.chargeResult
      _this['color' + _this.chargeResult + '2'] = '#0da394'
    } else {
      _this.color11 = '#0da394'
      _this.color22 = '#0da394'
    }
  },
  methods: {
    toggle: function (num, type) {
      if (type === 1) {
        this.color11 = ''
        this.color21 = ''
        this.color31 = ''
        this.applyTime = num
        if (num === 3) {
          this.showDialog = 1
        } else {
          this.showDialog = 0
        }
      } else {
        this.color22 = ''
        this.color32 = ''
        this.chargeResult = num
      }
      this['color' + num + type] = '#0da394'
    },
    goDetails: function () {
      let _this = this
      this.$store.dispatch('setApprovalSettingSearchInfo', {
        'applyTime': _this.applyTime,
        'chargeResult': _this.chargeResult,
        'beginTime': _this.beginTime,
        'endTime': _this.endTime,
        'searchType': _this.searchType
      })
      this.$router.push({
        name: `approvalSettingsSearchDetail`
      })
    }
  }
}
</script>
