<template><!-- 增票认证-->
  <div id="ticketAuthentication"  v-loading.fullscreen.lock="loading">
    <TY_NavTop title="增票认证" isSearch="true" @toggleList="search"></TY_NavTop>
    <div class="container">
      <div class="p1">
        <p>增值税专用发票认证后，认证结果需在此记录.</p>
        <p>记录认证结果时，请先选择认证结果，之后按页面提示继续。</p>
      </div>
      <div class="p1">
        <p>发票的认证结果 <span class="ri" @click="nextStep()">下一步</span></p>
        <el-radio-group v-model="approvalStatus" size="small">
          <el-radio label="1">认证已通过的发票</el-radio><br/>
          <el-radio label="2">认证未通过、不用来抵扣，只用作报销的发票</el-radio><br/>
          <el-radio label="3">认证未通过、换了新发票的发票</el-radio><br/>
        </el-radio-group>
      </div>
      <div class="p1">
        <p>如下{{list.length}}张增值税专用发票尚未记录认证结果。</p>
        <table>
          <tr>
            <td>发票号码</td>
            <td>价税合计</td>
            <td>查看</td>
          </tr>
          <tr v-for="(item, index) in list" v-bind:key="index">
            <td>{{item.billNo}}</td>
            <td>{{item.itemAmount | toFix2}}</td>
            <td><span class="linkBtn" @click="getAuthBillItemDetail(item)">票据信息</span></td>
          </tr>
        </table>
      </div>
    </div>
  </div>
</template>
<style lang="less">
#ticketAuthentication{
  .p1{ background:#fff;margin-bottom:10px; padding:15px;  }
  .linkBtn{ color: #0b9df9; cursor:default;  }
  .linkBtn:hover{ text-decoration:underline;   }
  .ri{ float: right; color:#fff; padding: 2px 10px; background:#0b9df9; cursor: default;  }
  .ri:hover{ background: #30a8f9;   }
  table{
    width:100%;
    td{text-align:center; height:30px; }
    tr:nth-child(even)>td{ background:#eaeaea;  }
  }
}
</style>
<script>
var that
export default {
  name: 'ticketAuthentication',
  data () {
    return {
      loading: true,
      approvalStatus: 0,
      list: [],
      listenersUid: []
    }
  },
  created () {
    that = this
    this.listenersUid = [
      this.sphdSocket.subscribe('cashierAuthenticationHandle', function (data) {
        that.loading = false
        let res = JSON.parse(data)
        console.log('cashierAuthentication receive OK : ' + res)
        console.log(res)
        that.list = res['cashierAuthentication'] || []
      }),
      // 财务增票认证审批人
      this.sphdSocket.subscribe('cashierAuthentication', function (data) {
        that.loading = false
        let res = JSON.parse(data)
        console.log('cashierAuthentication 财务增票认证审批人 receive OK : ' + res)
        console.log(res)
        let status = res.status
        if (status === 1) {
          this.$kiko_message('已有票据完成认证,即将刷新')
          this.getList()
        }
      }, 'user')
    ]
    this.getList()
  },
  filter: {
    toFix2: (val) => Number(val).toFixed(2)
  },
  destroyed: function () {
    let that = this
    this.listenersUid.forEach(function (item) {
      that.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    getList: function () {
      let sessionid = that.sphdSocket.sessionid
      let oid = that.sphdSocket.org.id
      this.sphdSocket.send('cashierAuthentication', { 'session': sessionid, 'oid': oid })
    },
    search: function () {
      this.$router.push({
        path: `/ticketAuthSearch`
      })
    },
    nextStep: function () {
      if (this.approvalStatus === 0) {
        this.$kiko_message('请选择发票的认证结果！')
      } else {
        let sta = this.approvalStatus
        that.$router.push({
          path: `/ticketAuthList/` + sta
        })
      }
    },
    getAuthBillItemDetail: function (item) {
      let params = {
        'name': 'auth',
        'id': item.id,
        'createName': '',
        'reimburseId': '',
        'billCat': item.billCat,
        'feeCat': '',
        'secondFeeCat': '',
        'billCatName': '增值税专用发票',
        'feeCatName': '',
        'secondFeeCatName': '',
        'totalAmount': item.itemAmount,
        'num': '',
        'session': this.sphdSocket.sessionid
      }
      this.$store.dispatch('setBillParam', params)
      this.$router.push({
        path: 'bill',
        name: 'bill'
      })
    }
  }
}
</script>
