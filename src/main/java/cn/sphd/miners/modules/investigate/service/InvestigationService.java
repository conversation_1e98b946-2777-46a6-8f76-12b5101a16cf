package cn.sphd.miners.modules.investigate.service;


import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.investigate.dto.ReqInvestigateSubject;
import cn.sphd.miners.modules.investigate.dto.RespInvestigateStopByMonthList;
import cn.sphd.miners.modules.investigate.dto.RespInvestigateSubject;
import cn.sphd.miners.modules.investigate.entity.InvestigateSubject;
import cn.sphd.miners.modules.system.entity.User;

import java.util.List;

/**
 * @ClassName TrainService
 * @Description TODO
 * <AUTHOR>
 * @Date 2021/2/3 8:13
 * @Version 1.0
 */

public interface InvestigationService {
    List<InvestigateSubject> selectInvestigateSubjectList(User user, PageInfo pageInfo, Integer status,String month);
    int countInvestigateSubject(User user,Integer status,String month);
    List<InvestigateSubject> selectSubjectByNameList(User user, PageInfo pageInfo,String keyword);
    int countSubjectByKeyword(User user, String keyword);
    RespInvestigateStopByMonthList selectSubjectStopByMonthList(User user, String year);

    int addSubject(User user, ReqInvestigateSubject reqInvestigateSubject);
    RespInvestigateSubject selectSubject(int id);

    int updateEnabledSubject(int id,int type,User user);
    int updateSubjectOrTag(User user,ReqInvestigateSubject reqInvestigateSubject);
    int updateSubject(User user,ReqInvestigateSubject reqInvestigateSubject);

    int deleteSubjectJudge(int id);
    int deleteSubject(int id);
}
