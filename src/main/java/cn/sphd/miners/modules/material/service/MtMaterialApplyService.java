package cn.sphd.miners.modules.material.service;

import cn.sphd.miners.modules.material.entity.MtMaterialApply;

import java.util.List;

/**
 *
 * Created by Administrator on 2017/8/2.
 */
public interface MtMaterialApplyService {

    void addMtMaterialApply(MtMaterialApply mtMaterialApply);

    MtMaterialApply getById(Integer ID,Integer oid);

    List<MtMaterialApply> getMtMaterialApplyLists(Integer oid,Integer state);

    List<MtMaterialApply> getMtMaterialApplyLists1(Integer oid);

    void updateMtMaterialApply(MtMaterialApply mtMaterialApply);

}
