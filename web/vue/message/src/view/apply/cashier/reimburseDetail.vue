<template>
  <div id="reimburseApply">
    <TY_NavTop title="报销申请" ></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading">
      <div class="rejectPng" v-if="isReject">
        <img :src="rejectPng">
      </div>
      <div class="panel-title">
        {{personnelReimburse.createName}} 申请报销 {{personnelReimburse.amount}} 元  <div class="right">{{personnelReimburse.transactionType | isSaler}}</div>
      </div>
      <div class="panel-content">
        <div class="item-header">报销事由</div>
        <div>事件的发生日期：{{personnelReimburse.beginDate | formatDay('YYYY年MM月DD日')}} - {{personnelReimburse.endDate | formatDay('DD日')}}</div>
        <div>
          {{personnelReimburse.purpose}}
        </div>
        <div class="panel-content">
          <div class="text-right" v-on:click="toggle = -toggle"><el-link>审批记录<i :class="toggle === -1?'el-icon-arrow-down':'el-icon-arrow-up'"></i> </el-link></div>
          <div v-show="toggle > 0">
            <div class="item-content">
              <div class="processItem">
                <div>提交报销申请</div>
                <div>申请人</div>
                <div>{{personnelReimburse.createName | stringSplit(4)}} {{personnelReimburse.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
              <div v-for="it in approvalProcessList" v-bind:key="it.id">
                <div class="processItem" v-if="it.approveStatus === '2' && !it.businessType">
                  <div>在线审批通过</div>
                  <div>审批人</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 32" class="processItem">
                  <div>在线审核通过</div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 33" class="processItem">
                  <div>线下审核通过</div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '5'" class="processItem">
                  <div>
                    <span v-if="financeAccount.accountType == '2'">报销款已转账</span>
                    <span v-if="financeAccount.accountType == '1'">现金付讫</span>
                  </div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
            <!-- 最后一条审批记录展示 -->
              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType===32">
                <div style="margin-top:15px;"> 票据在线审核未通过！</div>
                <div><span style="float: left">理由：</span> <div style="margin-left: 50px;white-space: pre-wrap;">{{ nowApproveItem.reasonStr }}</div></div>
                <br>
                <div class="processItem">
                  <div></div>
                  <div>审批人</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && !nowApproveItem.businessType">
                <div style="margin-top:15px;">报销申请被驳回！</div>
                <div>驳回理由： {{nowApproveItem.reason || ''}}</div>
                <div class="processItem">
                  <div></div>
                  <div>审批人</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType===33">
                <div style="margin-top:15px;">  线下审核未通过！</div>
                <div>驳回理由: 实际票据与在线审核通过的不一致。</div>
                <div class="processItem">
                  <div></div>
                  <div>出纳员</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-red" v-if="nowApproveItem.approveStatus === '8'">
                <div style="margin-top:15px;">被驳回！</div>
                <div>驳回理由： {{nowApproveItem.reason}}</div>
                <div class="processItem">
                  <div></div>
                  <div>出纳员</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
            <div class="color-blue">
              <span v-if="nowApproveItem.approveStatus === '1' && !nowApproveItem.businessType"><b>{{nowApproveItem.userName}}</b>为下一个审批人</span>
              <span v-if="nowApproveItem.approveStatus === '1' && nowApproveItem.businessType===32">等待出纳在线审核。</span>
              <span v-if="nowApproveItem.approveStatus === '1' && nowApproveItem.businessType===33">
                请将实际票据交出纳，由出纳进行线下审核。<br>
                注意：实际票据需与在线审核通过的票据一致！</span>
              <!--<span v-if="nowApproveItem.approveStatus === '6'" style="display: inline-block; margin-top: 10px;">报销申请在线审批已通过，请将实际票据交财务审核。</span>-->
              <span v-if="(
                        (nowApproveItem.businessType
                        && nowApproveItem.businessType!==33
                        && nowApproveItem.businessType!==32)
                        && (nowApproveItem.approveStatus !== '5'
                        &&nowApproveItem.approveStatus !== '3'))
                   || (!nowApproveItem.businessType && nowApproveItem.approveStatus === '4')

                        " style="display: inline-block; margin-top: 10px;">请等待收款!</span>
              <span v-if="nowApproveItem.approveStatus === '5'">本次报销已完结！</span>
          </div>
          </div>
          </div>
        </div>
      </div>
      <div class="panel-title">
        本次报销共有票据 {{personnelReimburse.billQuantity}} 张，合计 {{personnelReimburse.billAmount}} 元
      </div>
      <div class="panel-content">
        <el-tabs v-model="activeName" @tab-click="handleClick" stretch="true">
          <el-tab-pane label="按票据查看" name="bill">
            <div class="ui-cells_none">
              <a class="ui-cell" @click="goBillInfo(item)" v-for="item in listMapBillCat" v-bind:key="item.billCat">
                <div class="ui-cell__bd">
                  <div class="ui-cell_con">
                    <el-row>
                      <el-col :span="12">{{item.billCatName}}</el-col>
                      <el-col :span="6">{{item.num}}张</el-col>
                      <el-col :span="6">{{item.totalAmount}} 元</el-col>
                    </el-row>
                  </div>
                  <div class="ui-cell__ft"></div>
                </div>
              </a>
            </div>
          </el-tab-pane>
          <el-tab-pane label="按费用查看" name="feeCat">
            <a class="ui-cell" @click="goFeeInfo(item)"  v-for="item in listMapFeeCat" v-bind:key="item.feeCat">
              <div class="ui-cell__bd">
                <div class="ui-cell_con">
                  <el-row>
                    <el-col :span="12">{{item.feeCatName}}</el-col>
                    <el-col :span="6">{{item.secondFeeCatName | chargeNull}}</el-col>
                    <el-col :span="6">{{item.totalAmount}} 元</el-col>
                  </el-row>
                </div>
              </div>
              <div class="ui-cell__ft"></div>
            </a>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div>
        <div class="ui-cells_none">
          <a class="ui-cell" v-if="isRevoke">
            <div class="ui-cell__bd">
              <div class="ui-cell_con">
                <div class="handle_button" style="text-align: center">
                  <button class="ui-btn ui-btn_info" @click="revokeBtn">撤销</button>
                </div>
              </div>
            </div>
          </a>
        </div>
      </div>
    </div>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="98%"
      :before-close="handleClose">
      <div class="text-center">
        <div>撤销后，该条报销将从系统中消失。</div>
        <div>您确定撤销吗？</div>
      </div>
      <div slot="footer" class="dialog-footer">
        <div class="handle_button">
          <input type="submit" class="ui-btn" value=" 取消" @click="dialogVisible = false">
          <input type="submit" class="ui-btn ui-btn_info" value="确定" @click="revoke">
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<style lang="less">
  .processItem{
    display: flex;
    font-size: 11px;
    margin-left:-5px;
    div:nth-child(1){ text-align: left; flex: 1 }
    div:nth-child(2){ text-align: center; flex: 1 }
    div:nth-child(3) { text-align: right; flex: 2 }
  }
  .rejectPng{
    position: absolute;
    top: calc( 50% - 60px);
    left: calc( 50% - 60px);
    z-index: 50;
    filter:alpha(opacity=60);
    opacity:0.6;
    -moz-opacity:0.6;
    -khtml-opacity: 0.6
  }
</style>

<script>
import { isSaler, chargeNull } from '../../../js/common'
import rejectPng from '../../../images/reject.png'
export default {
  name: 'applyReimburse',
  data () {
    return {
      isReject: false, // 判断是否驳回来显示驳回图片
      isRevoke: false, // 判断是否显示撤销
      rejectPng: rejectPng, // 驳回图片
      dialogVisible: false,
      loading: true,
      toggle: -1, // 审批记录控制显隐
      listenersUid: [],
      personnelReimburse: {},
      personnelReimbursePayment: {},
      financeAccount: {},
      approvalProcessList: [],
      nowApproveItem: {},
      listMapBillCat: {},
      listMapFeeCat: {},
      activeName: 'bill'
    }
  },
  filters: {
    isSaler,
    chargeNull,
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    },
    setReasonList: function (approveSelect) {
      let list = approveSelect.split(',')
      let str = ``
      list.each(function (item) {
        if (item === 1) {
          str += `照片不清楚，或信息被遮挡，以至于无法审核。<br>`
        } else if (item === 2) {
          str += `无法入会计帐的票据较多。<br>`
        } else if (item === 3) {
          str += `包含公司不允许报销的票据。<br>`
        } else if (item === 4) {
          str += `票据内容与所录入的信息不一致。<br>`
        } else if (item === 5) {
          str += `其他原因：${this.nowApproveItem.reason}`
        }
      })
      return str
    }
  },
  created () {
    let that = this
    let reimburseId = this.$route.params.id
    this.listenersUid = [
      this.sphdSocket.subscribe('cancel', function (data) {
        that.loading = false
        let getData = JSON.parse(data)
        console.log(getData)
        console.log(getData.state)
        let state = Number(getData.state)
        if (state === 1) {
          that.$kiko_message('撤销成功')
          that.$router.push({
            path: '/applyReimburse',
            name: 'applyReimburse'
          })
        } else if (state === 2) {
          that.$kiko_message('已有审批人进行审批，不可再进行撤销')
        } else if (state === 0) {
          that.$kiko_message('撤销失败')
        } else {
          that.$kiko_message('错误的返回值')
        }
      }),
      this.sphdSocket.subscribe('getReimburseDetail', function (data) {
        console.log('getReimburseDetail session Socket received OK:' + data)
        that.loading = false
        let ReimburseDetail = JSON.parse(data)
        console.log(ReimburseDetail)
        // 报销申请信息
        that.personnelReimburse = ReimburseDetail.personnelReimburse
        that.personnelReimbursePayment = ReimburseDetail.personnelReimbursePayment
        // 审批列表
        that.approvalProcessList = ReimburseDetail.approvalProcessList
        if (that.approvalProcessList.length > 0) {
          that.nowApproveItem = that.approvalProcessList[that.approvalProcessList.length - 1]
          if (that.nowApproveItem.approveStatus === '3' || that.nowApproveItem.approveStatus === '8') {
            that.isReject = true
            that.showReason()
          }
          if (that.approvalProcessList.length === 1 && that.approvalProcessList[0].approveStatus === '1') {
            that.isRevoke = true
          }
        }
        // 按照票据种类列表
        that.listMapBillCat = ReimburseDetail.listMapBillCat
        that.financeAccount = ReimburseDetail.financeAccount
        // 按费用类别列表
        that.listMapFeeCat = ReimburseDetail.listMapFeeCat
        console.log(that.nowApproveItem)
      })
    ]
    let session = this.sphdSocket.sessionid
    this.sphdSocket.send('getReimburseDetail', { reimburseId: reimburseId, session: session })
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    showReason: function () {
      let str = ''
      let reasonStr = this.nowApproveItem.reason
      if (String(this.nowApproveItem.approveSelect) === '5') {
        str += '其他原因:' + reasonStr
      } else {
        let approveSelectArr = []
        let selectStr = this.nowApproveItem.approveSelect
        if (selectStr && selectStr !== 'null') {
          if (selectStr.length > 2) {
            approveSelectArr = selectStr.split(',')
          } else {
            approveSelectArr = [selectStr]
          }
          for (let i = 0; i < approveSelectArr.length; i++) {
            switch (String(approveSelectArr[i])) {
              case '1':
                str += '照片不清楚，或信息被遮挡，以至于无法审核\n'
                break
              case '2':
                str += '无法入会计帐的票据较多\n'
                break
              case '3':
                str += '包含公司不允许报销的票据\n'
                break
              case '4':
                str += '票据内容与所录入的信息不一致\n'
                break
              default:
            }
          }
        }
      }
      this.nowApproveItem['reasonStr'] = str
    },
    search: function () {
      this.$router.push({
        path: `/applyReimburseQuery`
      })
    },
    goBillInfo: function (item) {
      let params = {
        'name': 'apply',
        'createName': this.personnelReimburse.createName,
        'reimburseId': this.personnelReimburse.id,
        'billCat': item.billCat,
        'feeCat': '',
        'secondFeeCat': '',
        'billCatName': item.billCatName,
        'feeCatName': '',
        'secondFeeCatName': '',
        'totalAmount': item.totalAmount,
        'num': item.num,
        'session': this.sphdSocket.sessionid
      }
      this.$store.dispatch('setBillParam', params)
      this.$router.push({
        path: 'bill',
        name: 'bill'
      })
    },
    goFeeInfo: function (item) {
      let params = {
        'name': 'apply',
        'createName': this.personnelReimburse.createName,
        'reimburseId': this.personnelReimburse.id,
        'billCat': '',
        'feeCat': item.feeCat,
        'secondFeeCat': item.secondFeeCat,
        'billCatName': '',
        'feeCatName': item.feeCatName,
        'secondFeeCatName': item.secondFeeCatName,
        'totalAmount': item.totalAmount,
        'session': this.sphdSocket.sessionid
      }
      this.$store.dispatch('setBillParam', params)
      this.$router.push({
        path: 'bill',
        name: 'bill'
      })
    },
    revokeBtn: function () {
      this.dialogVisible = true
    },
    revoke: function () {
      this.loading = true
      this.sphdSocket.send('cancel', { 'reimburseId': this.personnelReimburse.id, 'session': this.sphdSocket.sessionid })
      this.dialogVisible = false
    }
  }
}
</script>
