<template>
  <div class="mine">
    <div class="my-flex-panel my-flex-item">
      <div class="img">
        <img v-if="userImg" alt="11" class="image" :src="userImg || userImgErr" :onerror="imgOnError"/>
        <img v-else alt="22"  class="image" :src="userImgErr" :onerror="userImgErr"/>
      </div>
      <div class="user" v-if="user.mobile">
        <div class="officeName">{{  user.name || '私人领地名称' }}</div>
        <div class="userInfo">
          <div @click="goPage('userMsg')">
            <span class="txt">账号：{{user.mobile || '私人领地账号' }}</span>
            <el-icon class="live-right relaRight">
              <arrow-right-bold color="#53B5A8" class="houseSty8" />
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <div>
      <div class="live-panel live-pad">
        <div class="live-tr live-tr-line">
          <span class="icon icon-changeAcc"></span>修改账号
          <el-icon class="live-right">
            <arrow-right-bold color="#53B5A8" class="houseSty8" />
          </el-icon>
        </div>
        <div class="live-tr">
          <span class="icon icon-about"></span>关于
          <el-icon class="live-right ">
            <arrow-right-bold color="#53B5A8" class="houseSty8" />
          </el-icon>
        </div>
      </div>

      <div class="live-panel live-tr">
        <div @click="goPage('register1')">
          <span class="icon icon-register"></span>注册工作室
          <el-icon class="live-right ">
            <arrow-right-bold color="#53B5A8" class="houseSty8" />
          </el-icon>
        </div>
      </div>
      <div class="live-panel live-tr">
        <div>
          <span class="icon icon-myIncome"></span>我的收益
          <el-icon class="live-right ">
            <arrow-right-bold color="#53B5A8" class="houseSty8" />
          </el-icon>
        </div>
      </div>
      <div class="live-panel live-pad">
        <div class="live-tr live-tr-line" @click="backOffice">
          <span class="icon icon-return"></span>返回机构
          <el-icon class="live-right">
            <arrow-right-bold color="#53B5A8" class="houseSty8" />
          </el-icon>
        </div>
        <div class="live-tr" @click="closeAccFun">
          <span class="icon icon-cancellation"></span>注销账号
          <el-icon class="live-right ">
            <arrow-right-bold color="#53B5A8" class="houseSty8" />
          </el-icon>
        </div>
      </div>


    </div>

  </div>
</template>

<script setup>
import userImgErr from "@/assets/img/tabbar/1.png"
import { updateTokenRemoveUser, getTerritoryAuthInfo, sureLogin,getRootPathFile,  closeAcc } from "@/api/api.js"
import { onMounted, reactive, ref } from 'vue'
import { useRouter } from "vue-router";
import JSONBig from 'json-bigint'
import { ElMessage } from 'element-plus'
import auth from '@/sys/auth'

const router = useRouter();
let userImg = ref('')

let user = ref('')
const fileUrl = ""

onMounted(()=>{
  let isChangeToken = auth.isLogInOrg()
  // let isChangeToken = localStorage.getItem('isChangeToken')
  console.log('isChangeToken=', isChangeToken)
  if(isChangeToken){
    localStorage.setItem('curOrg', JSON.stringify(auth.getOrg()))
    console.log('保存了哦！')
  }
  changeToken()

})

const closeAccFun = ()=> {
  let data = {}
  closeAcc(data).then(res => {
    console.log('closeAcc res', res)
    		if(res.success === 2){
    			let txt = res.data
    			console.log('txt', txt)
          ElMessage.error(txt)
    		} else if(res.success === 1) {
          ElMessage.success('账号已注销！')
          console.log('要跳转 开始页 了 ')
          tt.miniProgram.navigateTo({
            url: `/pages/start/start`,
            success(res) {
              console.log('跳转到 小程序的开始页');
              console.log(res);
            },
            fail(res) {
              console.log('跳转到 小程序的开始页 失败');
            },
          });
        }
  }).catch( err => {
    console.log('closeAcc err = ', err)
  })
}

const imgOnError = (e) => {
  let img = e.srcElement;
  img.src = userImgErr
  img.onerror = null; //防止闪图
}
const goPage = (url) =>{
  console.log('my to url=', url)
  router.push({name: url});
}
const changeToken = () => {
  updateTokenRemoveUser().then((res)=>{
    console.log(' changeToken res = ', res)
    if(res.length > 0){
      localStorage.setItem('isChangeToken','0')
      getUserDetails();
    }
  })
}
const backOffice = () => {
  let org = JSON.parse(localStorage.getItem('curOrg')) ;
  console.log('org =', org )
  if(org && org.id){
    sureLogin({"oid": org.id}).then((res)=>{
      goPage('homeMy')
    })
  }else{
    ElMessage.error('没有可返回的机构哦！')
  }
}
const getUserDetails = () => {
  getTerritoryAuthInfo().then((res) => {
    console.log('重新获取之后', res.data)
    user.value = res.data
    localStorage
    console.log('换token之后 auth.getUser() 返回 null，不能再用了 ', auth.getUser())
    console.log(user.value , '222')
    getFileRoot()
  }).catch((res)=>{
    console.log('bug了，还是换一token下吧')
    console.log(res)

  })
}
const getFileRoot = () => {
	getRootPathFile().then((res) =>{
    // fileUrl:"https://hxz-t.frp.btransmission.commonDatanData/upload/"
    // ow365url:"https://ow365.cn/?i=13867&ssl=1&furl=https://hxz-t.frp.btransmission.com/upload/"
    // uploadUrl:"https://hxz-t.frp.btransmission.com/upload/"
    // webRoot:"https://hxz-t.frp.btransmission.com"

		console.log('getRootPathFile =' , res)
    let user2= auth.getAcc()
    let user1= auth.getUser()
    console.log('user1=', user1)
    console.log('user2=', user2)
    let path = user2.avatar ? user2.avatar : (user1 && user1.imgPath)
    userImg.value = (path && (res.fileUrl + path)) || ''
    console.log(userImg.value)
	})
}
</script>

<style lang="scss" scoped>
$imgWid: 80px;
$imgWid2: 40px;
.mine{
  .houseSty8{
    position: relative;
    top:5px;
  }
  .my-flex-panel{
    display: flex; padding:20px; background-color: #fff; margin-bottom: 20px;
    .img{
      flex: 1; display: inline-block; overflow: hidden; background-color: #fff;
      img{ background-color: #eee; border-radius: $imgWid2; width:$imgWid; height:$imgWid; position: relative; top: 20px; }
    }
    .user{
      flex: 3;
      padding-left: 15px;
      .officeName{ font-size: 1.1em; line-height: 50px; color: $livePageHeadBg; font-weight:bold;  }
      .userInfo{ line-height:30px; color: #555; font-size: 0.9em; }
    }
  }
  .icon-changeAcc{ background-image: url("../../../../../liveMiniApp/src/assets/img/changeAcc.png"); }
  .icon-about{ background-image: url("../../../../../liveMiniApp/src/assets/img/about.png"); }
  .icon-register{ background-image: url("../../../../../liveMiniApp/src/assets/img/register.png"); }
  .icon-myIncome{ background-image: url("../../../../../liveMiniApp/src/assets/img/myIncome.png"); }
  .icon-return{ background-image: url("../../../../../liveMiniApp/src/assets/img/return.png"); background-size: 16px!important; }
  .icon-cancellation{ background-image: url("../../../../../liveMiniApp/src/assets/img/cancellation.png"); }
  .icon{ width: 20px; height:20px; background-color: #fff; background-repeat: no-repeat; background-size: 100%; display: inline-block; position: relative; top:6px; margin-right:14px; }

}


	
</style>
