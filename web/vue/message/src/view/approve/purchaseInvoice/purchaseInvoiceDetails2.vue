<template>
  <div id="purchaseInvoiceDetails3">
    <TY_NavTop :title="ttl" ></TY_NavTop>
    <div class="container" v-loading.fullscreen.lock="loading" v-if="details.poPaymentApplication">
      <div class="rejectPng" v-if="isReject">
        <img :src="rejectPng">
      </div>
      <div class="con1">
        {{ details.poPaymentApplication.type | formatType(details.poPaymentApplication.amount) }}
        <div v-if="details.poPaymentApplication.type === '3'">本次付款申请没有附带的票据</div>
        <p>申请人：{{ details.poPaymentApplication.createName }}{{ details.poPaymentApplication.createDate | formatDay('YYYY-MM-DD HH:mm:ss') }}</p>
      </div>
      <div class="panel-content">
        <div>
          <table style="width:100%;">
            <tr>
              <td>供应商：{{ details.fullName  }}</td>
              <td>{{ details.codeName }}</td>
            </tr>
            <tr>
              <td>订单总额：{{ details.poOrders.amount && details.poOrders.amount.toFixed(2) }}元</td>
              <td>订单号：{{ details.poOrders.sn }} </td>
            </tr>
            <tr>
              <td>订单日期：{{ details.poOrders.createDate | formatDay('YYYY-MM-DD') }}</td>
              <td></td>
            </tr>
          </table>
        </div>
        <div class="panel-content">
          <div class="text-right">
            <el-link style="color: #409eff; margin-right: 20px;" @click="orderProgressRade = !orderProgressRade">查看订单进度
              <i :class="!orderProgressRade?'el-icon-arrow-down':'el-icon-arrow-up'"></i></el-link>
            <el-link v-on:click="toggle = -toggle">审批记录<i :class="toggle === -1?'el-icon-arrow-down':'el-icon-arrow-up'"></i> </el-link>
          </div>
          <div v-show="toggle > 0">
            <div class="item-content">
              <div class="processItem">
                <div>提交报销申请</div>
                <div>申请人</div>
                <div>{{details.poPaymentApplication.createName | stringSplit(4)}} {{ details.poPaymentApplication.createDate | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
              </div>
              <div v-for="it in details.approvalProcessList" v-bind:key="it.id">

                <div v-if="it.approveStatus === '2' && it.businessType == 45" class="processItem">
                  <div>在线审核通过</div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 46" class="processItem">
                  <div>在线审批通过</div>
                  <div>审批人</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 47" class="processItem">
                  <div>线下审核通过</div>
                  <div>出纳员</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
                <div v-if="it.approveStatus === '2' && it.businessType == 48" class="processItem">
                  <div>采购审批通过</div>
                  <div>审批人</div>
                  <div>{{it.userName | stringSplit(4)}} {{it.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <!-- 最后一条审批记录展示 -->
              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType===46">
                <div style="margin-top:15px;"> 票据在线审核未通过！</div>
                <div><span style="float: left">理由：</span> <div style="margin-left: 50px;white-space: pre-wrap;">{{ nowApproveItem.reason }}</div></div>
                <br>
                <div class="processItem">
                  <div></div>
                  <div>审批人</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType=== 45">
                <div style="margin-top:15px;">报销申请被驳回！</div>
                <div>驳回理由： {{nowApproveItem.reason || ''}}</div>
                <div class="processItem">
                  <div></div>
                  <div>审批人</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-red" v-if="nowApproveItem.approveStatus === '3' && nowApproveItem.businessType=== 47">
                <div style="margin-top:15px;">  线下审核未通过！</div>
                <div>驳回理由: 实际票据与在线审核通过的不一致。</div>
                <div class="processItem">
                  <div></div>
                  <div>出纳员</div>
                  <div>{{nowApproveItem.userName | stringSplit(4)}} {{nowApproveItem.handleTime | formatDay('YYYY-MM-DD HH:mm:ss')}}</div>
                </div>
              </div>
              <div class="color-blue">
                <!--<span v-if="nowApproveItem.approveStatus === '1'&& nowApproveItem.businessType===45">等待出纳在线审核。</span>-->
                <span v-if="nowApproveItem.approveStatus === '1' && !isNowApprover && nowApproveItem.businessType===46">
                请将实际票据交出纳，由出纳进行线下审核。<br>
                注意：实际票据需与在线审核通过的票据一致！</span>
                <span v-else-if="nowApproveItem.approveStatus === '1' && isNowApprover && nowApproveItem.businessType===46">
                请等待报销者提交实际票据。<br>
                注意：实际票据需与在线审核通过的票据一致！</span>
                <span v-else-if="nowApproveItem.approveStatus === '5'">本次报销已完结！</span>
                <!--<span v-else-if="nowApproveItem.approveStatus === '1'&& !isNowApprover ">{{ nowApproveItem.userName }}为下一个审批人</span>-->
              </div>
            </div>
          </div>
        </div>
        <div v-if="orderProgressRade">
          <el-table :data="details.orderProgress" stripe style="width: 100%">
            <el-table-column prop="name" label="阶段"></el-table-column>
            <el-table-column prop="amount" label="金额"></el-table-column>
            <el-table-column prop="rate" label="比例"></el-table-column>
          </el-table>
        </div>
        <div v-if="[41,42].indexOf(code) > -1 ">
          <div class="panel-title">
            本次报销共有票据 {{ details.poPaymentApplication.billCount }} 张，合计 {{ details.poPaymentApplication.billAmount && details.poPaymentApplication.billAmount.toFixed(2) }} 元
          </div>
          <div class="panel-content">
            <el-tabs v-model="activeName" @tab-click="handleClick" stretch="true">
              <el-tab-pane label="按票据查看" name="bill">
                <div class="ui-cells_none">
                  <a class="ui-cell" @click="goBillInfo(item)" v-for="item in details.listMapBillCat" v-bind:key="item.billCat">
                    <div class="ui-cell__bd">
                      <div class="ui-cell_con">
                        <el-row>
                          <el-col :span="12">{{item.invoiceCategory | formatCat}}</el-col>
                          <el-col :span="6">{{item.num}}张</el-col>
                          <el-col :span="6">{{item.totalBillAmount && item.totalBillAmount.toFixed(2) }} 元</el-col>
                        </el-row>
                      </div>
                      <div class="ui-cell__ft"></div>
                    </div>
                  </a>
                </div>
              </el-tab-pane>
              <el-tab-pane label="按费用查看" name="feeCat">
                <a class="ui-cell" @click="goFeeInfo(item)"  v-for="item in details.listMapFeeCat" v-bind:key="item.feeCat">
                  <div class="ui-cell__bd">
                    <div class="ui-cell_con">
                      <el-row>
                        <el-col :span="12">{{item.feeCategory}}</el-col>
                        <el-col :span="6">{{item.totalBillAmount && item.totalBillAmount.toFixed(2)}} 元</el-col>
                      </el-row>
                    </div>
                  </div>
                  <div class="ui-cell__ft"></div>
                </a>
              </el-tab-pane>
            </el-tabs>
          </div>
          <div>
            <a class="ui-cell" v-if="code == 41 ">
              <div class="ui-cell__bd">
                <div class="ui-cell_con">
                  <div class="handle_button">
                    <button class="ui-btn" @click="approve(3)">驳回</button>
                    <button class="ui-btn ui-btn_info" @click="approve(2)">批准</button>
                  </div>
                </div>
              </div>
            </a>
          </div>
        </div>
        <div v-if="[43,44,51,52].indexOf(code) > -1">
          <div style="background: #fff; padding:10px 15px;">
            <span>付款事由：</span><span>{{ details.poPaymentApplication.applicationReason | formReason(details.poPaymentApplication.applicationDesc)}}</span>
            <hr>
            <!-- 采购审批者的审批项目 -->
            <div class="ui-cells_none" v-if="code===43">
              <div>
                <el-radio-group v-model="approveSta" size="small">
                  <el-radio label="2">同意付款！</el-radio><br/>
                  <el-radio label="3">不同意付款！</el-radio><br/>
                </el-radio-group>
              </div>
              <div v-if="approveSta === '3'">
                <p>请确定不同意付款的原因（可多选）</p>
                <el-checkbox v-model="approveSelect" label="1">该供应商提供的产品或服务有问题</el-checkbox>
                <el-checkbox v-model="approveSelect" label="2">公司资金紧张，过些日子才能付款</el-checkbox>
                <el-checkbox v-model="approveSelect" label="3">没必要提前付款</el-checkbox>
                <br>
                <span v-show="approveSelect.indexOf('5')>-1" style="float: right;margin-right:100px; " >{{ reason.length }}/40</span>
                <el-checkbox v-model="approveSelect" label="5">其他原因</el-checkbox>
                <el-input style="width:95%; " v-show="approveSelect.indexOf('5')>-1" type="textarea" :rows="2" v-model="reason"
                          placeholder="请在此录入在线审批无法通过的原因，最多可录入40个字。" v-on:keyup.native="strSub" @change="strSub"></el-input>
              </div>
              <div class="handle_button">
                <input type="submit" style="background: #0a9bee; color:#fff; width: 100px; text-align: center;" class="ui-btn" value="确定" @click="approvePay()">
              </div>
            </div>
            <!-- 付款审批者的审批项目 -->
            <div class="ui-cells_none" v-if="code == 51">
              <div style="margin-top: 50px;">
                <el-radio-group v-model="approveStaPayment" size="small">
                  <el-radio label="2">同意付款！</el-radio><br/>
                </el-radio-group>
                <div style="color:#0a9bee; font-size: 0.8em; line-height: 30px; ">注：对付款有异议，需进行调查时可暂不操作。</div>
              </div>
              <div class="handle_button" style="text-align: center;">
                <input type="submit" style="background: #0a9bee; color:#fff; width: 100px; text-align: center;" class="ui-btn" value="确定" @click="approvePayment">
              </div>
            </div>
          </div>
        </div>
      </div>
      <el-dialog title="提示" :visible.sync="dialogVisible" width="70%" >
        <span>确定批准吗？</span>
        <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="approveOk(2,'')">确 定</el-button>
      </span>
      </el-dialog>
    </div>
  </div>
</template>
<style lang="less">
.linkBtn{ color:#5d9cec; cursor:default; margin-right:20px; }
.linkBtn:hover{ text-decoration:underline;  }
.processItem{
  display: flex;
  font-size: 11px;
  margin-left:-5px;
  div:nth-child(1){ text-align: left; flex: 1 }
  div:nth-child(2){ text-align: center; flex: 1 }
  div:nth-child(3) { text-align: right; flex: 2 }
}
.rejectPng{
  position: absolute;
  top: calc( 50% - 60px);
  left: calc( 50% - 60px);
  z-index: 50;
  filter:alpha(opacity=60);
  opacity:0.6;
  -moz-opacity:0.6;
  -khtml-opacity: 0.6
}
.con1{ background:#fff; margin-bottom:10px; padding:5px 15px; font-size:14px;  }
.con1>p{text-align: right; font-size:0.8em;   }
</style>

<script>
import { isSaler, chargeNull } from '../../../js/common'
import rejectPng from '../../../images/reject.png'
export default {
  name: 'purchaseInvoiceDetails3',
  data () {
    return {
      ttl:"",
      isReject: false, // 判断是否驳回来显示驳回图片
      isRevoke: false, // 判断是否显示撤销
      isNowApprover: false,
      rejectPng: rejectPng, // 驳回图片
      dialogVisible: false,
      loading: true,
      toggle: -1, // 审批记录控制显隐
      listenersUid: [],
      approveSelect: [],
      approveSta: '',
      approveStaPayment: '',
      approveStatus: '0',
      nowApproveItem: {},
      reason: "",
      invoiceID: "",
      activeName: "bill",
      orderProgressRade: false,
      code: 0,
      details: {}

    }
  },
  filters: {
    formReason: function (num, applicationDesc) {
      switch (Number(num)){
        case 1:
          return "符合合同约定";
          break;
        case 2:
          return applicationDesc;
          break;
      }
    },
    formatCat: function (num) {
      switch (Number(num)){
        case 1:
          return "增值税专用发票";
          break;
        case 2:
          return "增值税普通发票";
          break;
        case 3:
          return "收据";
        case 4:
          return "其他发票";
          break;
      }
    },
    formatType: function (num, amount) {
      switch (Number(num)){
        case 1:
          return "本次仅提交票据，不付款";
          break;
        case 2:
          return `申请付款：${ amount.toFixed(2) }元`;
          break;
        case 3:
          return `申请付款：${ amount.toFixed(2) }元`;
          break;
      }
    },
    stringSplit: function (str, num) {
      if (str) {
        return str.substring(0, num)
      } else {
        return ''
      }
    }
  },
  created () {
    let that = this
    let paramCode = this.$route.params.id
    that.code = Number(paramCode.substring(0, 2)) // 41-待处理; 42-已批准
    console.log('code:', that.code )
    if(that.code === 44 || that.code === 43){
      that.ttl = "采购部门的付款"
    }
    that.invoiceID = paramCode.substring(2, paramCode.length)
    this.listenersUid = [
    ]
    that.getDetail()
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  watch: {
    approveSelect () {
      console.log(this.approveSelect, this.approveSelect.length)
      let index5 = this.approveSelect.indexOf('5')
      if (this.approveSelect.length > 1 && index5 > -1) {
        this.approveSelect = ['5']
        this.$kiko_message('选择“其他原因”后不可选择其他的原因')
      }
    }
  },
  methods: {
    strSub: function () {
      let reasonLen = this.reason.length
      if (reasonLen > 40) {
        this.reason = this.reason.substr(0, 40)
      }
    },
    getDetail:function(){
      let that = this
      console.log("invoiceID", that.invoiceID)
      this.axios.post('../../../purchaseInvoice/getPurchaseInvoiceDetail.do', { "applicationId": that.invoiceID } )
        .then(function (response) {
          let res = response.data.data
          console.log('详情：', res)
          that.details = res
          let poOrders = res.poOrders
          let rate1 = '- -'
          let rate2 = '- -'
          poOrders.invoicedAmount = poOrders.invoicedAmount || 0
          poOrders.payedAmount = poOrders.payedAmount || 0
          let rate3 = (poOrders.invoicedAmount / poOrders.amount * 100).toFixed(2)
          let rate4 = (poOrders.payedAmount / poOrders.amount * 100).toFixed(2)
          that.details.orderProgress = [
            { 'name': '检验已合格', 'amount': `${  poOrders.checkedAmount ? poOrders.checkedAmount.toFixed(2) :"" }` , 'rate': rate1 },
            { 'name': '已入库', 'amount': `${  poOrders.storedAmount ? poOrders.storedAmount.toFixed(2) :"" }` , 'rate': rate2 },
            { 'name': '票据已提交', 'amount': poOrders.invoicedAmount.toFixed(2) , 'rate': rate3 + '%'},
            { 'name': '已付款', 'amount': poOrders.payedAmount.toFixed(2) , 'rate': rate4 + '%'}
          ]
          that.loading = false
          let ap = res.approvalProcessList || []
          that.nowApproveItem = ap[ap.length - 1] ;
          let lastToUser = that.nowApproveItem.toUser
          if (!lastToUser || lastToUser === that.sphdSocket.uuid()) {
            that.isNowApprover = true
          }
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    search: function () {
      this.$router.push({
        path: `/applyReimburseQuery`
      })
    },
    goBillInfo: function (item) {
      let params = {
        'applicationId': this.$route.params.id,
        'name': 'purchaseInvoice',
        'type': '1',
        'num': item.num,
        'totalAmount': item.totalBillAmount.toFixed(2),
        'billCat': item.invoiceCategory
      }
      this.$store.dispatch('setBillParam', params)
      this.$router.push({
        path: 'bill',
        name: 'bill'
      })
    },
    goFeeInfo: function (item) {
      let params = {
        'applicationId': this.$route.params.id,
        'type': '2',
        'createName': this.details.poOrders.createName,
        'name': 'purchaseInvoice',
        'totalAmount': item.totalBillAmount && item.totalBillAmount.toFixed(2),
        'feeCategory': '采购材料',
        'feeCatName': '采购材料'
      }
      this.$store.dispatch('setBillParam', params)
      this.$router.push({
        path: 'bill',
        name: 'bill'
      })
    },
    approve: function (state) {
      let that = this
      if (state === 3) {
        this.$prompt('驳回理由', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(({ value }) => {
          let reason = value || ''
          if(reason.length === 0){
            that.$kiko_message('请录入驳回理由')
            return false
          }
          that.loading = true
          that.approveOk(3, reason)
        })
      } else {
        this. dialogVisible = true
      }
    },
    approveOk: function (state, reason) {
      let approveInfo = {
        approvalProcessId: this.nowApproveItem.id,
        approveStatus: state,
        reason: reason
      }
      console.log('传参：', approveInfo)
      let that = this
      that.dialogVisible = false
      this.axios.post('../../../purchaseInvoice/purchaseOnlineApproval.do', approveInfo )
        .then(function (response) {
          let res = response.data.data
          let content = res.content
          console.log('审批返回值：', res)
          that.$kiko_message(content)
          if(content === '操作成功'){
            if(approveInfo.approvalStatus == 1){ // 通过
              localStorage.setItem("navNum", "2");
            }else{ // 驳回
              localStorage.setItem("navNum", "1");
            }
            that.$router.push({
              path: `/purchaseBillApproval`
            })
          }
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    approvePay:function () {
      let approveInfo = {
        approvalProcessId: this.nowApproveItem.id,
        approveStatus: this.approveSta,
      }
      let that = this
      if (Number(approveInfo.approveStatus) > 0) {
      } else {
        that.$kiko_message('请先选择审批结果')
        return false
      }
      approveInfo['reason'] = this.reason
      approveInfo['approveSelect'] = this.approveSelect.toString()
      if (approveInfo.approveSelect.length === 0 && (approveInfo.approveStatus) === '3') {
        that.$kiko_message('请先选择在线审批未通过的原因')
        return false
      }
      if ((approveInfo.approveStatus) === '3'&& approveInfo.approveSelect === '5'
        && approveInfo.reason.length ===0 ) {
        that.$kiko_message('请输入未通过的原因')
        return false
      }
      console.log(approveInfo)
      that.loading = true
      this.axios.post('../../../purchaseInvoice/purchasePaymentApproval.do', approveInfo )
        .then(function (response) {
          console.log('详情：', response)
          let res = response.data.data
          that.$kiko_message(res.content)
          that.loading =false
          if(res.content === '操作成功'){
            if(that.approveSta == 2){ // 批准
              localStorage.setItem("navNum", "2");
            }else{ // 驳回
              localStorage.setItem("navNum", "1");
            }
            that.$router.push({
              path: `/purchasePaymentApproval`
            })
          }
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    approvePayment:function () {
      let approveInfo = {
        approvalProcessId: this.nowApproveItem.id
      }
      let that = this
      if (Number(this.approveStaPayment) > 0) {
      } else {
        that.$kiko_message('请先选择审批结果')
        return false
      }
      console.log(approveInfo)
      that.loading = true
      this.axios.post('../../../purchaseInvoice/paymentApprovalApproval.do', approveInfo )
        .then(function (response) {
          let res = response.data.data
          console.log('付款审批者返回值：', res)
          that.$kiko_message(res.content)
          that.loading =false
          if(res.content === '操作成功'){
            localStorage.setItem("navNum", "2");
            that.$router.push({
              path: `/purchasePaymentApplyApproval`
            })
          }
        })
        .catch(function (error) {
          console.log(error)
        })
    },
  }
}
</script>
