/**
 * @param {string} url
 * @param {string} key
 * @returns {string}
 */
export function getKeyFromURL(url, key) {
  if (url.indexOf("?") > -1) {
    var query = decodeURI(url).split("?")[1];
    query = query.split("#/")[0];
    var vars = query.split("&");
    for (var i = 0; i < vars.length; i++) {
      var pair = vars[i].split("=");
      if (pair[0] == key) {
        return pair[1];
      }
    }
  }

  return "";
}
