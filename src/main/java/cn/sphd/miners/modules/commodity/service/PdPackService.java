package cn.sphd.miners.modules.commodity.service;

import cn.sphd.miners.modules.commodity.entity.PdMerchandise;
import cn.sphd.miners.modules.commodity.entity.PdPack;
import cn.sphd.miners.modules.material.entity.MtBase;

import java.util.List;

/**
 * Created by Administrator on 2017/7/24.
 */
public interface PdPackService {

    List<PdPack> getPdPackListByPdBaseId(Integer pdCustomerId);//根据商品外部图号id获取 商品包装信息列表

    PdPack getPdPackById(Integer id);

    void  savePdPack(PdPack pdPack);

    void  updatePdPack(PdPack pdPack);

    PdMerchandise getPdMerchandiseById(Integer id);

    List<PdMerchandise> getPdMerchandiseByOid(Integer oid);

    void deletePdPack(PdPack pdPack);

    List<MtBase> getPackListByCIdCode(Integer categoryId,String code);
}
