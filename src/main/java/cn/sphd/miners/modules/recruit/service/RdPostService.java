package cn.sphd.miners.modules.recruit.service;

import cn.sphd.miners.modules.recruit.entity.RdPost;
import cn.sphd.miners.modules.recruit.entity.RdPostHistory;
import cn.sphd.miners.modules.system.entity.User;

import java.util.Date;
import java.util.List;

public interface RdPostService {

    void saveRdPost(RdPost rdPost);

    RdPost getRdPostById(Long id);

    List<RdPost> getRdPostsByOid(Integer oid,Boolean enabled);

    void updateRdPost(RdPost rdPost, User user,Integer operation);

    List<RdPostHistory> getRdPostHistoriesByPostId(Long postId,Integer[] operations);

    void deleteRdPost(RdPost rdPost);

    public void  saveRdPostHistory(RdPostHistory rdPostHistory,User user);

    void rdPostExecuteChanges(Integer oid, Date beginDate);

    void updateRdPost(RdPost rdPost);
}
