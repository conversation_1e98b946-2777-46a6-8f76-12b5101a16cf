package cn.sphd.miners.modules.dailyIndex.service;

import cn.sphd.miners.modules.dailyIndex.entity.DailyOrg;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by HIAPAD on 2022/6/9.
 */
public interface DailyOrgService {

    void saveDailyOrg(DailyOrg dailyOrg);

    void deleteDailyOrg(DailyOrg dailyOrg);

    void updateDailyOrg(DailyOrg dailyOrg);

    DailyOrg getDailyOrgById(Integer id);

    DailyOrg dailyOrgIndexDay(Integer oid);

    void  dailyAllOrgIndexDay();

    List<Map<String,Object>> getDailyOrgCounts(List<Integer> oids,Integer yearMonthDay,Integer type);

    List<DailyOrg> getDailyOrgList(Integer... oids);

    List<DailyOrg> getDailyOrgInfo(Integer oid,Integer yearMonth);

    DailyOrg getDailyOrg(Integer oid,Integer yearMonthDay);

}
