.page{width: 1000px; padding: 0 20px}
.part{display: none}
.bonceContainer .mainCon{
    width: 90%;
    margin: 8px auto;
}
.ty-page-header{
    display: flex;
    line-height: 24px;
    padding: 0 0 0 70px;
    color: #5d9cec;
    margin-top: 16px;
}
.page-header__left{
    display: flex;
    cursor: pointer;
    margin-right: 40px;
    position: relative;
}
.page-header__left::after {
    content: "";
    position: absolute;
    width: 1px;
    height: 16px;
    right: -20px;
    top: 50%;
    transform: translateY(-50%);
    background-color: #dcdfe6;
}
.page-header__left .icon-back {
    font-size: 18px;
    margin-right: 6px;
    align-self: center;
    position: relative;
    top: 1px;
}
.item-flex{
    display: flex;
    line-height: 30px;
    margin-bottom: 8px;
}
.item-title{
    width: 120px;
    flex: none;
}
.item-content{
    flex: auto;
}
