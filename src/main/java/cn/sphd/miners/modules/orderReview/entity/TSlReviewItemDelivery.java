package cn.sphd.miners.modules.orderReview.entity;

import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 对象 t_sl_review_item_delivery
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
public class TSlReviewItemDelivery
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** $column.columnComment */
    private Long org;

    /** $column.columnComment */
    private Long ordersItem;

    /** $column.columnComment */
    private Long reviewItem;

    /** $column.columnComment */
    private Long ordersReview;

    /** $column.columnComment */
    private Date deliveryNo;

    /** $column.columnComment */
    private Date deliveryDate;

    /** $column.columnComment */
    private Long deliveryAddressId;

    /** $column.columnComment */
    private String deliveryAddress;

    /** $column.columnComment */
    private String memo;

    /** $column.columnComment */
    private Long creator;

    /** $column.columnComment */
    private String createName;

    /** $column.columnComment */
    private Date createDate;

    /** $column.columnComment */
    private Long updator;

    /** $column.columnComment */
    private String updateName;

    /** $column.columnComment */
    private Date updateDate;

    /** $column.columnComment */
    private Long operation;

    /** $column.columnComment */
    private Long previousId;

    /** $column.columnComment */
    private Long versionNo;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOrg(Long org) 
    {
        this.org = org;
    }

    public Long getOrg() 
    {
        return org;
    }
    public void setOrdersItem(Long ordersItem) 
    {
        this.ordersItem = ordersItem;
    }

    public Long getOrdersItem() 
    {
        return ordersItem;
    }
    public void setReviewItem(Long reviewItem) 
    {
        this.reviewItem = reviewItem;
    }

    public Long getReviewItem() 
    {
        return reviewItem;
    }
    public void setOrdersReview(Long ordersReview) 
    {
        this.ordersReview = ordersReview;
    }

    public Long getOrdersReview() 
    {
        return ordersReview;
    }
    public void setDeliveryNo(Date deliveryNo) 
    {
        this.deliveryNo = deliveryNo;
    }

    public Date getDeliveryNo() 
    {
        return deliveryNo;
    }
    public void setDeliveryDate(Date deliveryDate) 
    {
        this.deliveryDate = deliveryDate;
    }

    public Date getDeliveryDate() 
    {
        return deliveryDate;
    }
    public void setDeliveryAddressId(Long deliveryAddressId) 
    {
        this.deliveryAddressId = deliveryAddressId;
    }

    public Long getDeliveryAddressId() 
    {
        return deliveryAddressId;
    }
    public void setDeliveryAddress(String deliveryAddress) 
    {
        this.deliveryAddress = deliveryAddress;
    }

    public String getDeliveryAddress() 
    {
        return deliveryAddress;
    }
    public void setMemo(String memo) 
    {
        this.memo = memo;
    }

    public String getMemo() 
    {
        return memo;
    }
    public void setCreator(Long creator) 
    {
        this.creator = creator;
    }

    public Long getCreator() 
    {
        return creator;
    }
    public void setCreateName(String createName) 
    {
        this.createName = createName;
    }

    public String getCreateName() 
    {
        return createName;
    }
    public void setCreateDate(Date createDate) 
    {
        this.createDate = createDate;
    }

    public Date getCreateDate() 
    {
        return createDate;
    }
    public void setUpdator(Long updator) 
    {
        this.updator = updator;
    }

    public Long getUpdator() 
    {
        return updator;
    }
    public void setUpdateName(String updateName) 
    {
        this.updateName = updateName;
    }

    public String getUpdateName() 
    {
        return updateName;
    }
    public void setUpdateDate(Date updateDate) 
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() 
    {
        return updateDate;
    }
    public void setOperation(Long operation) 
    {
        this.operation = operation;
    }

    public Long getOperation() 
    {
        return operation;
    }
    public void setPreviousId(Long previousId) 
    {
        this.previousId = previousId;
    }

    public Long getPreviousId() 
    {
        return previousId;
    }
    public void setVersionNo(Long versionNo) 
    {
        this.versionNo = versionNo;
    }

    public Long getVersionNo() 
    {
        return versionNo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("org", getOrg())
            .append("ordersItem", getOrdersItem())
            .append("reviewItem", getReviewItem())
            .append("ordersReview", getOrdersReview())
            .append("deliveryNo", getDeliveryNo())
            .append("deliveryDate", getDeliveryDate())
            .append("deliveryAddressId", getDeliveryAddressId())
            .append("deliveryAddress", getDeliveryAddress())
            .append("memo", getMemo())
            .append("creator", getCreator())
            .append("createName", getCreateName())
            .append("createDate", getCreateDate())
            .append("updator", getUpdator())
            .append("updateName", getUpdateName())
            .append("updateDate", getUpdateDate())
            .append("operation", getOperation())
            .append("previousId", getPreviousId())
            .append("versionNo", getVersionNo())
            .toString();
    }
}
