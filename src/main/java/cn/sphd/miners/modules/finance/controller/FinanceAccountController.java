package cn.sphd.miners.modules.finance.controller;

import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.utils.MyStrings;
import cn.sphd.miners.common.utils.ObjectToJson;
import cn.sphd.miners.modules.accountant.service.BuildAccountService;
import cn.sphd.miners.modules.accountant.service.FinanceRelevanceService;
import cn.sphd.miners.modules.finance.dto.FinanceAccountHistoryDto;
import cn.sphd.miners.modules.finance.entity.*;
import cn.sphd.miners.modules.finance.service.AccountService;
import cn.sphd.miners.modules.finance.service.DataService;
import cn.sphd.miners.modules.finance.service.FinanceAccountService;
import cn.sphd.miners.modules.finance.service.FinanceChequeService;
import cn.sphd.miners.modules.personal.service.ApprovalProcessService;
import cn.sphd.miners.modules.system.entity.Organization;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.OrgService;
import cn.sphd.miners.modules.system.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

/**
 * Created by Administrator on 2016/12/12.
 * 会计1.7会计之关联2银行存款
 */
@Controller
@RequestMapping("/account")
public class FinanceAccountController {
    @Autowired
    FinanceAccountService financeAccountService;
    @Autowired
    OrgService orgService;
    @Autowired
    DataService dataService;
    @Autowired
    FinanceChequeService financeChequeService;
    @Autowired
    AccountService accountService;
    @Autowired
    FinanceRelevanceService financeRelevanceService;   //会计科目
    @Autowired
    UserService userService;
    @Autowired
    ApprovalProcessService approvalProcessService;
    @Autowired
    BuildAccountService buildAccountService;

    /**
     *<AUTHOR>
     *@date 2018/11/21 10:01
     *添加新账户
    */
    @ResponseBody
    @RequestMapping("/addAccount.do")
    public void addAccount(User user,FinanceAccount financeAccount, HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        Integer oid = user.getOid();

        FinanceAccount financeAccountBasic = financeAccountService.getBasicAccount(oid);
        if("基本户".equals(financeAccount.getOperation()) && financeAccountBasic!=null) {
             map.put("status",2);   //基本户已经存在
        }else {
            Organization organization = orgService.getByOid(oid);
            user = userService.getUserByID(user.getUserID());

            financeAccount = financeAccountService.addAccount(financeAccount, user, organization);  //添加账户
            map.put("status",1);   //添加成功
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    /**
     *<AUTHOR>
     *@date 2016/11/21 10:18
     *获取所有账户信息（以后用新的接口，在下面）
     */
    @ResponseBody
    @RequestMapping("/getAllAccounts.do")
    public void getAllAccounts(User user,Integer oid,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        List<Integer> orgIntegerList = new ArrayList<>();
        if (oid==null){   //null时表示是查询所有的，不为null时是进行了筛选(查询某机构的数据)
            Boolean fatherOrg = orgService.isFatherOrg(user.getOid());
            if (fatherOrg){
                orgIntegerList = orgService.getOrgSonOrgIds(user.getOid());
            }else {
                oid = user.getOid();
            }
        }else {  //有筛选的时候判断是否同属于一个总机构
            orgService.getOidByOrgSonOrg(user.getOid(),oid);
        }
        List<FinanceAccount> financeAccountList = new ArrayList<>();
        List<FinanceAccount> financeAccounts = financeAccountService.getAllAccounts(oid,orgIntegerList,1,null);   //正常启用的账户
        for (FinanceAccount financeAccount:financeAccounts) {
            financeAccount.setOrgName(financeAccount.getOrg().getName());
        }
        List<FinanceAccount> financeAccounts1 = financeAccountService.getAllAccounts(oid,orgIntegerList,0,null);  //关闭的账户
        for (FinanceAccount financeAccount1:financeAccounts1) {
            financeAccount1.setOrgName(financeAccount1.getOrg().getName());
        }
        financeAccountList.addAll(financeAccounts);
        financeAccountList.addAll(financeAccounts1);
        map.put("financeAccountList",financeAccountList);
        String res = buildAccountService.getBuildAccountState(user.getOid());   //是否建账完成
        map.put("res",res);
        ObjectToJson.objectToJson1(map,new String[]{"org","financeAccountHistories","accountPeriodHashSet","accountDetailHashSet","financeChequeDetailHashSet","financeAccountRecordHashSet"},response);
    }

    /**
     *<AUTHOR>
     *@date 2018/11/29 17:32
     *判断账户可否进行关闭(accountStatus 账户状态 0-关闭 1-启用)
    */
    @ResponseBody
    @RequestMapping("/closingAccountJudge.do")
    public void closingAccountJudge(Integer accountId,Integer accountStatus,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        if (accountId!=null && accountStatus!=null){
            FinanceAccount financeAccount = financeAccountService.getAccountById(accountId);
            if (financeAccount != null){
                if (accountStatus==0) {
                    //账户由开启到关闭时，需要进行判断有没有审批、账户有没有余额
                    Integer approvalCompleted = approvalProcessService.approvalCompleted(financeAccount.getOrg().getId(), financeAccount.getId());  //是否有未完成的审批申请
                    if (financeAccount.getBalance().doubleValue() > 0) {  //账户是否有余额
                        map.put("status",2);  //账户中尚有余额
                    } else if (approvalCompleted == 1) {   //是否有为完成的审批
                        map.put("status",3);;  //账户中有未完成审批的申请
                    }else {
                        map.put("status",1);  //可关闭账户
                    }
                }
            }
        }else {
            map.put("status",0);//失败
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }


    /**
     *<AUTHOR>
     *@date 2018/11/21 13:31
     *账户关闭/启动(accountStatus 账户状态 0-关闭 1-启用)
    */
    @ResponseBody
    @RequestMapping("/updateAccountStatus.do")
     public void updateAccountStatus(User user,Integer accountId,Integer accountStatus,HttpServletResponse response) throws IOException {
         Map<String,Object> map = new HashMap<>();
         user = userService.getUserByID(user.getUserID());
         Integer status = 1;
         if (accountId!=null && accountStatus!=null){
             FinanceAccount financeAccount = financeAccountService.getAccountById(accountId);
             if (financeAccount != null){
                 if (accountStatus==0){   //账户由开启到关闭时，需要进行判断有没有审批、账户有没有余额
                     Integer approvalCompleted = approvalProcessService.approvalCompleted(financeAccount.getOrg().getId(),financeAccount.getId());  //是否有未完成的审批申请
                     if (financeAccount.getBalance().doubleValue()>0){  //账户是否有余额
                         status=2;  //账户中尚有余额
                     } else if (approvalCompleted==1){   //是否有为完成的审批
                         status=3;  //账户中有未完成审批的申请
                     }
                 }
                 if (status==1){
                     financeAccount.setAccountStatus(accountStatus);
                     financeAccount.setEndDate(new Date());  //修改账户状态的开启/关闭时间
                     financeAccountService.updateFinanceAccount(financeAccount);
                     financeAccountService.addAccountRecord(financeAccount,accountStatus,user);  //添加账户启用记录
                     financeAccountService.updateAccountDetails(accountId,accountStatus);   //将数据查看中的数据修改为不可修改，或者修改为可修改
                     //修改成功
                 }
             }
         }else {
            status=0;//修改失败
         }
         map.put("status",status);
         ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    /**
     *<AUTHOR>
     *@date 2018/11/22 10:19
     *查询账户详情
    */
    @ResponseBody
    @RequestMapping("/getAccountDetail.do")
    public void getAccountDetail(HttpServletResponse response, Integer accountId) throws IOException {
        Map<String,Object> map = new HashMap<>();
        if (accountId!=null){
            FinanceAccount account = financeAccountService.getAccountById(accountId);
            List<FinanceAccountRecord> financeAccountRecords = financeAccountService.getAccountRecordsByAccountId(accountId);
            map.put("account",account);
            map.put("financeAccountRecords",financeAccountRecords);
            FinanceAccount financeAccountBasic = financeAccountService.getBasicAccount(account.getOrg_());
            if (financeAccountBasic!=null){
                map.put("status",1);   //有基本户
            }else {
                map.put("status",0);   //没有基本户
            }
            String res = buildAccountService.getBuildAccountState(account.getOrg_());   //是否建账完成 0-建账提示 1-建账提示中选择了是,进入的科目设置 2-建账提示中选择了否,进入的科目设置 3-科目已设置完,进入金额录入页面 4-建账整体流程完成
            map.put("res",res);
        }
        ObjectToJson.objectToJson1(map,new String[]{"org","financeAccountHistories","accountPeriodHashSet","accountDetailHashSet",
                "financeChequeDetailHashSet","financeAccountRecordHashSet","financeAccount"},response);
    }

    /**
     *<AUTHOR>
     *@date 2018/11/22 14:15
     *查询启用的账户列表（以后用新的接口，在下面）
    */
    @ResponseBody
    @RequestMapping("/getAvailableAccounts.do")
    public void getAvailableAccounts(User user,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        Integer oid = user.getOid();
        List<FinanceAccount> financeAccounts = financeAccountService.getAllAccounts(oid,null,1,2);   //正常启用的账户
        map.put("financeAccounts",financeAccounts);
        ObjectToJson.objectToJson1(map,new String[]{"org","financeAccountHistories","accountPeriodHashSet","accountDetailHashSet",
                "financeChequeDetailHashSet","financeAccountRecordHashSet"},response);
    }

    /**
     *<AUTHOR>
     *@date 2018/11/26 9:49
     *账户修改
    */
    @ResponseBody
    @RequestMapping("/updateAccount.do")
     public void updateAccount(User user,Integer accountId,Integer isBasic,String name,String bankName,String account,Double initialAmount,String memo,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        user = userService.getUserByID(user.getUserID());
        if (accountId!=null){
            FinanceAccount financeAccount = financeAccountService.getAccountById(accountId);
            Organization org = orgService.getByOid(financeAccount.getOrg_());

//            BigDecimal balanceFinal = financeAccount.getBalance().subtract(financeAccount.getInitialAmount()).add(BigDecimal.valueOf(initialAmount));
            //使用BigDecimal类构造方法传入double类型时，计算的结果也是不精确的。
            //因为不是所有的浮点数都能够被精确的表示成一个double 类型值，因此它会被表示成与它最接近的 double 类型的值。必须改用传入String的构造方法。这一点在BigDecimal类的构造方法注释中有说明。
            BigDecimal me = new BigDecimal(financeAccount.getInitialAmount().toString());
            BigDecimal ba = new BigDecimal(financeAccount.getBalance().toString());
            BigDecimal balanceFinal = ba.subtract(me).add(BigDecimal.valueOf(initialAmount));
            if (financeAccount.getInitialAmount().compareTo(BigDecimal.valueOf(initialAmount))!=0 && balanceFinal.doubleValue()<0){
                map.put("status",2);  //账户中的余额比初始金额少，不够冲账
            }else {
                financeAccountService.updateAccount(financeAccount,isBasic,name,bankName,account,initialAmount,memo,user,org);
                financeRelevanceService.financeRelevanceModify(org.getId(),financeAccount.getId(),bankName,account,user);   //更新会计的信息
                map.put("status",1);  //修改成功
            }
        }else {
            map.put("status",0);
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
     }

     /**
      *<AUTHOR>
      *@date 2018/11/26 9:52
      *查询账户修改记录列表
     */
     @ResponseBody
     @RequestMapping("/getAccountHistories.do")
    public void getAccountHistories(Integer accountId,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        if(accountId!=null){
            FinanceAccount financeAccount = financeAccountService.getAccountById(accountId);
            List<FinanceAccountHistoryDto> financeAccountHistoryDtos = financeAccountService.getAccountHistoryDtosByAccountId(accountId);  //查找这个账户耳朵所有修改记录
            if (financeAccountHistoryDtos.size()>1) {   //第一条是新添加的账户最开始的信息
                if (1 == financeAccount.getAccountType()) {  //现金/备用金账户
                    for (int i = 1; i < financeAccountHistoryDtos.size(); i++) {
                        financeAccountHistoryDtos.get(i).setRevisedBeforeAmount(financeAccountHistoryDtos.get(i - 1).getInitialAmount());  //修改前金额
                    }
                }
                financeAccountHistoryDtos.remove(0);
                map.put("financeAccountHistoryDtos", financeAccountHistoryDtos);
            }
        }
        ObjectToJson.objectToJson1(map,new String[]{},response);
    }

    /**
     *<AUTHOR>
     *@date 2018/11/27 11:07
     *账户修改记录查看详情
    */
    @ResponseBody
    @RequestMapping("/getAccountHistoryDetail.do")
    public void getAccountHistoryDetail(Integer accountHistoryDetailId,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        if (accountHistoryDetailId!=null){
            FinanceAccountHistory revisedAccountHistory = financeAccountService.getAccountHistoryById(accountHistoryDetailId,1);  //修改后详情
            FinanceAccountHistory beforeAccountHistory = new FinanceAccountHistory();   //修改前的详情
            if (revisedAccountHistory!=null){
                beforeAccountHistory = financeAccountService.getAccountHistoryById(revisedAccountHistory.getPreviousId(),1);  //修改前详情
            }
            map.put("revisedAccountHistory",revisedAccountHistory);  //修改后详情
            map.put("beforeAccountHistory",beforeAccountHistory);    //修改前的详情
        }
        ObjectToJson.objectToJson1(map,new String[]{"accountId"},response);
    }
    /**
     *<AUTHOR>
     *@date 2018/12/7 7:54
     *某机构所有对公户列表
     */
    @ResponseBody
    @RequestMapping("/getPublicAccount.do")
    public void getPublicAccount(User user,HttpServletResponse response) throws IOException {
        Map<String,Object> map = new HashMap<>();
        List<FinanceAccount> financeAccounts = financeAccountService.getPublicAccount(user.getOid());
        map.put("financeAccount",financeAccounts);
        ObjectToJson.objectToJson1(map,new String[]{"org","financeAccountHistories","accountPeriodHashSet","accountDetailHashSet",
                "financeChequeDetailHashSet"},response);
    }

    /**
    *<AUTHOR>
    *@date 2018/11/30 9:39
    *查询是否有基本户
    */
    @ResponseBody
    @RequestMapping("/getBaseAccount.do")
    public void getBaseAccount(HttpServletResponse response,User user) throws IOException {
        Map<String,Object> map = new HashMap<>();
        Integer oid = user.getOid();
        FinanceAccount financeAccountBasic = financeAccountService.getBasicAccount(oid);
        if (financeAccountBasic != null){
            map.put("financeAccountBasic",financeAccountBasic);
            map.put("status",1); //已有基本户
        }else {
            map.put("status",0);  //没有基本户
        }
        String res = buildAccountService.getBuildAccountState(oid);   //是否建账完成 0-建账提示 1-建账提示中选择了是,进入的科目设置 2-建账提示中选择了否,进入的科目设置 3-科目已设置完,进入金额录入页面 4-建账整体流程完成
        map.put("res",res);
        ObjectToJson.objectToJson1(map,new String[]{"org","financeAccountHistories","accountPeriodHashSet","accountDetailHashSet","financeChequeDetailHashSet","financeAccountRecordHashSet"},response);
    }

    /**
     *<AUTHOR>
     *@date 2018/11/30 9:39
     *获取不同条件的账户
     * accountType  1-现金，2-银行,3-其他
     * isPublic 是否为对公帐号:1-是 0-否
     * Integer isBasic 是否是 基本户 1-是，0-否
     * Integer accountStatus 账户状态 0:关闭 1：正常
     */
    @ResponseBody
    @RequestMapping("/getAccountKinds.do")
    public JsonResult getAccountKinds(User user,Integer accountType,String isPublic,Integer isBasic,Integer accountStatus) {
        Map<String,Object> map = new HashMap<>();
        List<FinanceAccount> financeAccounts = financeAccountService.getAccountKinds(user.getOid(), accountType,isPublic,isBasic,accountStatus);
        map.put("financeAccounts",financeAccounts);
        return new JsonResult(1,map);
    }

    //---------------------------以下是1.155I/A首页之财会2的“银行的账户信息”部分（有与上面接口重合的，使用新的接口，可共用）--------------//
    /**
     *<AUTHOR>
     *@date 2021/3/8 10:18
     *获取所有账户信息(与上面的getAllAccounts.do接口相同，PC端若需要新接口的话就使用此接口)
     */
    @ResponseBody
    @RequestMapping("/getAccounts.do")
    public JsonResult getAccounts(User user,Integer oid) throws IOException {
        Map<String,Object> map = new HashMap<>();
        List<FinanceAccount> financeAccountList = new ArrayList<>();
        List<Integer> orgIntegerList = new ArrayList<>();
        if (oid==null){   //null时表示是查询所有的，不为null时是进行了筛选(查询某机构的数据)
            Boolean fatherOrg = orgService.isFatherOrg(user.getOid());
            if (fatherOrg){
                orgIntegerList = orgService.getOrgSonOrgIds(user.getOid());
            }else {
                oid = user.getOid();
            }
        }else {  //有筛选的时候判断是否同属于一个总机构
            orgService.getOidByOrgSonOrg(user.getOid(),oid);
        }
        List<FinanceAccount> financeAccounts = financeAccountService.getAllAccounts(oid,orgIntegerList, 1, 2);   //正常启用的银行账户
        for (FinanceAccount financeAccount:financeAccounts) {
            financeAccount.setOrgName(financeAccount.getOrg().getName());
        }
        List<FinanceAccount> financeAccounts1 = financeAccountService.getAllAccounts(oid, orgIntegerList,0, 2);  //关闭的银行账户
        for (FinanceAccount financeAccount1:financeAccounts1) {
            financeAccount1.setOrgName(financeAccount1.getOrg().getName());
        }
        financeAccountList.addAll(financeAccounts);
        financeAccountList.addAll(financeAccounts1);
        map.put("financeAccountList",financeAccountList);
        String res = buildAccountService.getBuildAccountState(user.getOid());   //是否建账完成 0-建账提示 1-建账提示中选择了是,进入的科目设置 2-建账提示中选择了否,进入的科目设置 3-科目已设置完,进入金额录入页面 4-建账整体流程完成
        map.put("res",res);
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2021/3/8 9:52
     *修改记录列表（与getAccountHistories.do对应）
     */
    @ResponseBody
    @RequestMapping("/getAllAccountHistories.do")
    public JsonResult getAllAccountHistories(Integer accountId) {
        Map<String,Object> map = new HashMap<>();
        if(accountId!=null){
            List<FinanceAccountHistoryDto> financeAccountHistoryDtos = financeAccountService.getAccountHistoryDtosByAccountId(accountId);  //查找这个账户的所有修改记录
            if (financeAccountHistoryDtos!=null) {
                if (financeAccountHistoryDtos.size() == 1 || financeAccountHistoryDtos.size() == 0) {   //第一条是新添加的账户最开始的信息
                    map.put("content", "当前资料尚未经修改。");  //未修改
                    map.put("financeAccountHistoryDtos", new ArrayList<FinanceAccountHistoryDto>());
                } else {
                    for (int i = 0; i < financeAccountHistoryDtos.size(); i++) {
                        if (i == 0) {
                            financeAccountHistoryDtos.get(0).setRecordState("原始信息");
                        } else {
                            financeAccountHistoryDtos.get(i).setRecordState("第" + i + "次修改后");
                        }
                    }
                    map.put("content", "当前资料为第" + (financeAccountHistoryDtos.size() - 1) + "次修改后的结果。");
                    map.put("financeAccountHistoryDtos", financeAccountHistoryDtos);
                }
            }else {
                map.put("content", "当前资料尚未经修改。");  //未修改
                map.put("financeAccountHistoryDtos", new ArrayList<FinanceAccountHistoryDto>());
            }
        }
        return new JsonResult(1,map);
    }

    /**
     *<AUTHOR>
     *@date 2021/3/8 11:07
     *账户修改记录查看详情(与上面的getAccountHistoryDetail.do接口对应)
     */
    @ResponseBody
    @RequestMapping("/getHistoryDetail.do")
    public JsonResult getHistoryDetail(Integer accountHistoryDetailId) {
        Map<String,Object> map = new HashMap<>();
        if (accountHistoryDetailId!=null){
            FinanceAccountHistory revisedAccountHistory = financeAccountService.getAccountHistoryById(accountHistoryDetailId,1);  //修改后详情
            FinanceAccountHistory beforeAccountHistory = new FinanceAccountHistory();   //修改前的详情
            if (revisedAccountHistory!=null){
                beforeAccountHistory = financeAccountService.getAccountHistoryById(revisedAccountHistory.getPreviousId(),1);  //修改前详情
            }
            map.put("revisedAccountHistory",revisedAccountHistory);  //修改后详情
            map.put("beforeAccountHistory",beforeAccountHistory);    //修改前的详情
        }
        return new JsonResult(1,map);
    }

    /**
     *@Description 1.307初始化--新增/修改银行账户(初始化-财务的最后一步点击确定时用，删除只在前端进行吧)
     *@auther 李娅星(此接口参考上面的添加和修改账户的接口)
     *@date 2024/9/9
     *@param updateAddAccounts 修改或添加的内容{
     * Integer accountId：账户id(修改的传，新增的不传) ,String account：帐户 ,String summary：摘要 ,String memo：备注 ,String isPublic：是否为对公帐号:1-是 0-否
     * 	String bankName：开户行(银行名称) ,String name：账户名称 ,Integer cashable：是否可取现 1：可取现 2：不可取现 ,Integer isBasic：是否是 基本户 1-是，0-否}
    */
    @ResponseBody
    @RequestMapping("/updateAndAddAccount.do")
    public JsonResult updateAndAddAccount(User user,String updateAddAccounts){
        Map<String,Object> map = new HashMap<>();
        if (StringUtils.isNotEmpty(updateAddAccounts)){
            map = financeAccountService.updateAndAddAccount(user,updateAddAccounts);
            return new JsonResult(1,map);
        }else {
            return new JsonResult(new MyException("400","传参有误"));
        }
    }


    /**
     *@Description 1.307初始化--（初始化-  删除账号)
     *@auther lixu(接李亚星项目 缺少的接口)
     *@date 2024/10/8
     */
    @ResponseBody
    @RequestMapping("/deleteAccount.do")
    public JsonResult deleteAccount(Integer id){
        if (id!=null){
            FinanceAccount financeAccount=financeAccountService.getAccountById(id);
            if (financeAccount!=null) {
                financeAccountService.deleteFinanceAccount(financeAccount);
                return new JsonResult(1,"操作成功");
            }else {
                return new JsonResult(new MyException("-1","传参有误"));
            }
        }else {
            return new JsonResult(new MyException("-1","传参有误"));
        }
    }
}
