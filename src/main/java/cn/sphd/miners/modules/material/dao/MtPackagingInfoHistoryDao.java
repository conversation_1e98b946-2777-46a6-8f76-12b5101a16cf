package cn.sphd.miners.modules.material.dao;

import cn.sphd.miners.common.persistence.IBaseDao;
import cn.sphd.miners.modules.material.entity.MtPackagingInfo;
import cn.sphd.miners.modules.material.entity.MtPackagingInfoHistory;

import java.io.Serializable;

public interface MtPackagingInfoHistoryDao  extends IBaseDao<MtPackagingInfoHistory, Serializable> {

    //添加历史记录
    MtPackagingInfoHistory insert(MtPackagingInfo pdPackagingInfo);
}
