package cn.sphd.miners.modules.thirdPlatform.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2022-09-29 
 */

@Entity ( name ="TpAppRegister" )
@Table ( name ="t_tp_app_register" )
public class TpAppRegister  implements Serializable {

	private static final long serialVersionUID =  2694565950599328558L;

	/**
	 * ID
	 */
	@Id
	@Column
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * 机构ID
	 */
   	@Column(name = "org" )
	private Integer org;

	/**
	 * 平台ID
	 */
   	@Column(name = "platform" )
	private Integer platform;

	/**
	 * 类型:1-申请小程序,2-绑定小程序,3-发布申请
	 */
   	@Column(name = "type" )
	private Integer type;

	/**
	 * 访问令牌
	 */
   	@Column(name = "token" )
	private String token;

	/**
	 * 标题
	 */
   	@Column(name = "title" )
	private String title;

	/**
	 * 内容
	 */
   	@Column(name = "content" )
	private String content;

	/**
	 * 状态:0-草稿,1-提交申请,2-审批通过,3-审批否决
	 */
   	@Column(name = "approve_status" )
	private Integer approveStatus;

	/**
	 * 批准时间
	 */
   	@Column(name = "approve_time" )
	private Date approveTime;

	/**
	 * 返回状态
	 */
   	@Column(name = "response_status" )
	private Integer responseStatus;

	/**
	 * 请求url
	 */
   	@Column(name = "request_url" )
	private String requestUrl;

	/**
	 * 请求json串
	 */
   	@Column(name = "request_json" )
	private String requestJson;

	/**
	 * 响应json串
	 */
   	@Column(name = "response_json" )
	private String responseJson;

	/**
	 * 回调类
	 */
   	@Column(name = "callback_class" )
	private String callbackClass;

	/**
	 * 回调对象
	 */
   	@Column(name = "callback_json" )
	private String callbackJson;

	/**
	 * 回调表id
	 */
   	@Column(name = "callback_key" )
	private String callbackKey;

	/**
	 * 备注
	 */
   	@Column(name = "memo" )
	private String memo;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_time" )
	private Date createTime;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_time" )
	private Date updateTime;

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOrg() {
		return this.org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getPlatform() {
		return this.platform;
	}

	public void setPlatform(Integer platform) {
		this.platform = platform;
	}

	public Integer getType() {
		return this.type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getToken() {
		return this.token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public String getTitle() {
		return this.title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getContent() {
		return this.content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public Integer getApproveStatus() {
		return this.approveStatus;
	}

	public void setApproveStatus(Integer approveStatus) {
		this.approveStatus = approveStatus;
	}

	public Date getApproveTime() {
		return this.approveTime;
	}

	public void setApproveTime(Date approveTime) {
		this.approveTime = approveTime;
	}

	public Integer getResponseStatus() {
		return this.responseStatus;
	}

	public void setResponseStatus(Integer responseStatus) {
		this.responseStatus = responseStatus;
	}

	public String getRequestUrl() {
		return this.requestUrl;
	}

	public void setRequestUrl(String requestUrl) {
		this.requestUrl = requestUrl;
	}

	public String getRequestJson() {
		return this.requestJson;
	}

	public void setRequestJson(String requestJson) {
		this.requestJson = requestJson;
	}

	public String getResponseJson() {
		return this.responseJson;
	}

	public void setResponseJson(String responseJson) {
		this.responseJson = responseJson;
	}

	public String getCallbackClass() {
		return this.callbackClass;
	}

	public void setCallbackClass(String callbackClass) {
		this.callbackClass = callbackClass;
	}

	public String getCallbackJson() {
		return this.callbackJson;
	}

	public void setCallbackJson(String callbackJson) {
		this.callbackJson = callbackJson;
	}

	public String getCallbackKey() {
		return this.callbackKey;
	}

	public void setCallbackKey(String callbackKey) {
		this.callbackKey = callbackKey;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getCreator() {
		return this.creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return this.createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Integer getUpdator() {
		return this.updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return this.updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateTime() {
		return this.updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

}
