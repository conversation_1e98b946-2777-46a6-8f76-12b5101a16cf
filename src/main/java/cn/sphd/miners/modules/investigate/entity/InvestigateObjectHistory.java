package cn.sphd.miners.modules.investigate.entity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * @Description  
 * <AUTHOR>
 * @Date 2021-08-01 
 */

@Entity ( name ="InvestigateObjectHistory" )
@Table ( name ="t_investigate_object_history" )
public class InvestigateObjectHistory  implements Serializable {

	private static final long serialVersionUID =  6499542773825197402L;

	/**
	 * ID
	 */
	@Id
	@Column(name = "id")
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	private Integer id;

	/**
	 * 机构ID
	 */
   	@Column(name = "org" )
	private Integer org;

	/**
	 * 目标ID
	 */
   	@Column(name = "objective" )
	private Integer objective;

	/**
	 * 发布ID
	 */
   	@Column(name = "publish" )
	private Integer publish;

	/**
	 * 问卷ID
	 */
   	@Column(name = "subject" )
	private Integer subject;

	/**
	 * 联系人姓名
	 */
   	@Column(name = "contact_name" )
	private String contactName;

	/**
	 * 联系方式
	 */
   	@Column(name = "contact_way" )
	private String contactWay;

	/**
	 * 企业或联系人邮箱
	 */
   	@Column(name = "contact_email" )
	private String contactEmail;

	/**
	 * 哈希值
	 */
   	@Column(name = "hash_key" )
	private String hashKey;

	/**
	 * 备注
	 */
   	@Column(name = "memo" )
	private String memo;

	/**
	 * 创建人id
	 */
   	@Column(name = "creator" )
	private Integer creator;

	/**
	 * 创建人
	 */
   	@Column(name = "create_name" )
	private String createName;

	/**
	 * 创建时间
	 */
   	@Column(name = "create_date" )
	private Date createDate;

	/**
	 * 修改人id
	 */
   	@Column(name = "updator" )
	private Integer updator;

	/**
	 * 修改人
	 */
   	@Column(name = "update_name" )
	private String updateName;

	/**
	 * 修改时间
	 */
   	@Column(name = "update_date" )
	private Date updateDate;

	/**
	 * 操作:1-增,2-删,3-改
	 */
   	@Column(name = "operation" )
	private String operation;

	/**
	 * 修改前记录ID
	 */
   	@Column(name = "previous_id" )
	private Integer previousId;

	/**
	 * 版本号,每次修改+1
	 */
   	@Column(name = "version_no" )
	private Integer versionNo;

	public Integer getId() {
		return this.id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOrg() {
		return this.org;
	}

	public void setOrg(Integer org) {
		this.org = org;
	}

	public Integer getObjective() {
		return this.objective;
	}

	public void setObjective(Integer objective) {
		this.objective = objective;
	}

	public Integer getPublish() {
		return this.publish;
	}

	public void setPublish(Integer publish) {
		this.publish = publish;
	}

	public Integer getSubject() {
		return this.subject;
	}

	public void setSubject(Integer subject) {
		this.subject = subject;
	}

	public String getContactName() {
		return this.contactName;
	}

	public void setContactName(String contactName) {
		this.contactName = contactName;
	}

	public String getContactWay() {
		return this.contactWay;
	}

	public void setContactWay(String contactWay) {
		this.contactWay = contactWay;
	}

	public String getContactEmail() {
		return this.contactEmail;
	}

	public void setContactEmail(String contactEmail) {
		this.contactEmail = contactEmail;
	}

	public String getHashKey() {
		return this.hashKey;
	}

	public void setHashKey(String hashKey) {
		this.hashKey = hashKey;
	}

	public String getMemo() {
		return this.memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public Integer getCreator() {
		return this.creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public String getCreateName() {
		return this.createName;
	}

	public void setCreateName(String createName) {
		this.createName = createName;
	}

	public Date getCreateDate() {
		return this.createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public Integer getUpdator() {
		return this.updator;
	}

	public void setUpdator(Integer updator) {
		this.updator = updator;
	}

	public String getUpdateName() {
		return this.updateName;
	}

	public void setUpdateName(String updateName) {
		this.updateName = updateName;
	}

	public Date getUpdateDate() {
		return this.updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public String getOperation() {
		return this.operation;
	}

	public void setOperation(String operation) {
		this.operation = operation;
	}

	public Integer getPreviousId() {
		return this.previousId;
	}

	public void setPreviousId(Integer previousId) {
		this.previousId = previousId;
	}

	public Integer getVersionNo() {
		return this.versionNo;
	}

	public void setVersionNo(Integer versionNo) {
		this.versionNo = versionNo;
	}

}
