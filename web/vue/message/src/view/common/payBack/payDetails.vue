<template>
  <div id="backDetail" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="回款入账" :isBackHome="isBackHome"></TY_NavTop>
    <div class="container">
      <div class="place">
        <div class="placeContainer">
          <div class="lHead">
            <p class="line-s">{{customerName}}</p>
            <div class="listCon">
              <div class="line-p">
                <div class="tt-cell">本次回款 {{ baseInfo.method | incomeType}} {{baseInfo.amount | formatMoney}}元</div>
              </div>
            </div>
          </div>
          <div> <!--1-现金,2-现金支票,3-转帐支票,4-承兑汇票,5-银行转帐-->
            <div class="checkWay" v-if="baseInfo.method === '1'">
              <div class="line-s">
                <span class="tt-cl">收到日期</span>
                <span class="sub-cl">{{baseInfo.receiveDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
            </div>
            <div class="checkWay" v-if="baseInfo.method === '3'">
              <div class="line-s">
                <span class="tt-cl">收到日期</span>
                <span class="sub-cl">{{baseInfo.receiveDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
              <div class="line-s">
                <span class="tt-cl">支票号</span>
                <span class="sub-cl">{{baseInfo.returnNo}}</span>
              </div>
              <div class="line-s">
                <span class="tt-cl">到期日</span>
                <span class="sub-cl">{{baseInfo.expireDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
              <div class="line-s">
                <span class="tt-cl">出具的单位</span>
                <span class="sub-cl ellipsisSet">{{baseInfo.originalCorp}}</span>
              </div>
              <div class="line-s">
                <span class="tt-cl">出具的银行</span>
                <span class="sub-cl">{{baseInfo.bankName}}</span>
              </div>
            </div>
            <div class="checkWay" v-if="baseInfo.method === '4'">
              <div class="line-s">
                <span class="tt-cl">收到日期</span>
                <span class="sub-cl">{{baseInfo.receiveDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
              <div class="line-s">
                <span class="tt-cl">汇票号</span>
                <span class="sub-cl">{{baseInfo.returnNo}}</span>
              </div>
              <div class="line-s">
                <span class="tt-cl">到期日</span>
                <span class="sub-cl">{{baseInfo.expireDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
              <div class="line-s">
                <span class="tt-cl">最初出具的单位</span>
                <span class="sub-cl">{{baseInfo.originalCorp}}</span>
              </div>
              <div class="line-s">
                <span class="tt-cl">出具的银行</span>
                <span class="sub-cl">{{baseInfo.bankName}}</span>
              </div>
            </div>
            <div class="checkWay" v-if="baseInfo.method === '5'">
              <div class="line-s">
                <span class="tt-cl">到期日期</span>
                <span class="sub-cl">{{baseInfo.expireDate | formatDate('yyyy年MM月dd日')}}</span>
              </div>
              <div class="line-s">
                <span class="tt-cl">收款银行</span>
                <span class="sub-cl">{{baseInfo.bankName}}</span>
              </div>
            </div>
            <p class="inputerInfo">录入者：<span>{{baseInfo.createName}} {{baseInfo.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</span></p>
            <p class="inputerInfo">财&nbsp;&nbsp;&nbsp;务：<span>{{baseInfo.financerName}} {{baseInfo.approvalDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</span></p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { incomeType, formatDate, formatMoney } from '../../../js/common'
export default {
  name: 'collectDetail',
  filters: {
    incomeType,
    formatDate,
    formatMoney
  },
  data () {
    return {
      loading: false,
      baseInfo: {},
      customerName: '',
      detailState: false
    }
  },
  created () {
    let _this = this
    this.axios.post('../../../collectWindow/getSlCollectDetailPC.do', {
      collectId: _this.$route.params.id
    }).then(function (response) {
      _this.baseInfo = response.data.slCollectApplication
      _this.customerName = response.data.customerName
    }).catch(function (error) {
      console.log(error)
    })
  }
}
</script>
