package cn.sphd.miners.modules.commodity.service.impl;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.commodity.dao.*;
import cn.sphd.miners.modules.commodity.entity.*;
import cn.sphd.miners.modules.commodity.service.FeatureService;
import cn.sphd.miners.modules.equipment.entity.TEquEquipment;
import cn.sphd.miners.modules.equipment.entity.TEquModel;
import cn.sphd.miners.modules.equipment.mapper.TEquEquipmentMapper;
import cn.sphd.miners.modules.equipment.mapper.TEquModelMapper;
import cn.sphd.miners.modules.material.dao.MtUnitDao;
import cn.sphd.miners.modules.material.entity.MtUnit;
import cn.sphd.miners.modules.system.entity.User;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Transactional
public class FeatureServiceImpl implements FeatureService {

    @Autowired
    private PdBaseDao pdBaseDao;
    @Autowired
    private PdRankDao pdRankDao;
    @Autowired
    private PdRankHistoryDao pdRankHistoryDao;
    @Autowired
    private PdCheckControlDao pdCheckControlDao;
    @Autowired
    private PdCheckControlHistoryDao pdCheckControlHistoryDao;
    @Autowired
    private PdFeatureOptionDao pdFeatureOptionDao;
    @Autowired
    private PdItemOptionDao pdItemOptionDao;
    @Autowired
    private PdItemCheckDao pdItemCheckDao;
    @Autowired
    private PdFeatureItemDao pdFeatureItemDao;
    @Autowired
    private TEquEquipmentMapper tEquEquipmentMapper;
    @Autowired
    private TEquModelMapper tEquModelMapper;
    @Autowired
    private MtUnitDao mtUnitDao;
    @Override
    public List<Map<String, Object>> getProductFeatureList(Integer oid, int type, PageInfo pageInfo) {
        String hql = "SELECT\n" +
                "new Map( p.id AS id,\n" +
                "p.innerSn as innerSn,\n" +
                "p.name AS name,\n" +
                "p.model AS model,\n" +
                "p.specifications AS specifications,\n" +
                "p.unit AS unit,\n" +
                "p.unitId AS unitId,\n" +
                "p.source AS source,\n" +
                "p.composition AS composition,\n" +
                "p.netWeight AS netWeight,\n" +
                "p.weightUnit as weightUnit,\n" +
                "p.createName AS createName," +
                "p.createDate AS createDate," +
                "i.uplaodPath as xhPath," +
                "p.initTime as initTime," +
                "p.initiator as initiator," +
                "p.initiatorName as initiatorName) \n" +
                " from \n" +
                "PdBase p left join PdImage i on i.product = p.id and i.category = 2 " +
                "where p.oid=:oid and p.enabled=1 ";
        if (type == 1) {
            hql += " and  p.initState = 2";
        } else {
            hql += " and ( p.initState != 2 or p.initState is null )";
        }

        Map param = new HashMap();
        param.put("oid", oid);
        return pdBaseDao.getListByHQLWithNamedParams(hql, param, pageInfo);
    }

    @Override
    public Object getYszNum(Integer oid) {
        String hql = "SELECT COUNT(0) as num from PdBase p where p.oid=:oid and p.initState = 2";
        Map param = new HashMap();
        param.put("oid", oid);
        return pdBaseDao.getByHQLWithNamedParams(hql, param);
    }

    @Override
    public Object getMaxCode(Integer oid) {
        String hql = "SELECT max(code) as number from PdCheckControl where org=:oid ";
        Map param = new HashMap();
        param.put("oid", oid);
        return pdBaseDao.getByHQLWithNamedParams(hql, param);
    }

    @Override
    public PdCheckControl getCheckControlById(Long id) {
        return pdCheckControlDao.get(id);
    }

    @Override
    public List<PdRank> getRankList(Integer oid) {
        String hql = "from PdRank where org=:org and enabled=1";
        Map param = new HashMap();
        param.put("org", oid);
        return pdRankDao.getListByHQLWithNamedParams(hql, param);
    }

    @Override
    public String getRankSetting(List<PdRank> list, User user) {

        Date date = new Date();

        //先获取所有的等级设置
        List<PdRank> l=getRankList(user.getOid());
        //获取需要删除的
        List<PdRank> delList=new ArrayList<>();

        List<PdRank> haveList=new ArrayList<>();
        for (PdRank r1:l) {
            int i=0;
            for (PdRank r2:list) {
                if(r2.getId()!=null && r2.getId()==r1.getId()){
                    i++;
                    break;
                }
            }
         
            //需要删除
            if(i==0){
                delList.add(r1);
            }else{
                haveList.add(r1);
            }
        }
        for (PdRank r2 :list
             ) {
            if(r2.getId()==null){
                haveList.add(r2);
            }
        }


        pdRankDao.deleteAll(delList);
        pdRankDao.getSession().flush();
        //修改
        for (PdRank rank:haveList) {
            if(rank.getId()!=null){

                for (PdRank pdRank:list) {
                    if(pdRank.getId()!=null&&rank.getId().longValue()==pdRank.getId()){
                        rank.setName(pdRank.getName());
                        rank.setSymbol(pdRank.getSymbol());
                        break;
                    }
                }
                rank.setCreator(user.getCreator());
                rank.setCreateName(user.getUserName());
                rank.setCreateDate(date);
                rank.setOrg(user.getOid());
                rank.setEnabled(1);
                pdRankDao.update(rank);
                pdRankHistoryDao.insert(rank);
            }
        }


        //新增
        for (PdRank rank : haveList) {
            if(rank.getId()==null){
                rank.setCreator(user.getCreator());
                rank.setCreateName(user.getUserName());
                rank.setCreateDate(date);
                rank.setOrg(user.getOid());
                rank.setEnabled(1);
                pdRankDao.save(rank);
                pdRankHistoryDao.insert(rank);
            }
        }



        return "success";
    }

    @Override
    public String addRankSetting(PdRank rank, User user) {
        Date date = new Date();

        List<PdRank> list=getRankList(user.getOid());
        for (PdRank pdRank:list
             ) {
            pdRank.setCreator(user.getCreator());
            pdRank.setCreateName(user.getUserName());
            pdRank.setCreateDate(date);
            pdRank.setOrg(user.getOid());
            pdRank.setEnabled(1);
            pdRankDao.save(pdRank);

            pdRankHistoryDao.insert(pdRank);
        }
        rank.setCreator(user.getCreator());
        rank.setCreateName(user.getUserName());
        rank.setCreateDate(date);
        rank.setOrg(user.getOid());
        rank.setEnabled(1);
        pdRankDao.save(rank);

        pdRankHistoryDao.insert(rank);
        return "success";
    }

    @Override
    public String updateRankSetting(PdRank rank, User user) {
        Date date = new Date();

        List<PdRank> list=getRankList(user.getOid());
        for (PdRank pdRank:list
        ) {
            if(pdRank.getId().longValue()==rank.getId().longValue()){
                pdRank.setMemo(rank.getMemo());
                pdRank.setName(rank.getName());
                pdRank.setSymbol(rank.getSymbol());
            }
            pdRank.setCreator(user.getCreator());
            pdRank.setCreateName(user.getUserName());
            pdRank.setCreateDate(date);
            pdRank.setOrg(user.getOid());
            pdRank.setEnabled(1);
            pdRankDao.save(pdRank);

            pdRankHistoryDao.insert(pdRank);
        }

        return "success";
    }

    @Override
    public String deleteRankSetting(Long id, User user) {
        Date date = new Date();

        List<PdRank> list=getRankList(user.getOid());
        for (PdRank pdRank:list ) {
            if(pdRank.getId().longValue()==id.longValue()){
                pdRankDao.delete(pdRank);
                continue;
            }
            pdRank.setCreator(user.getCreator());
            pdRank.setCreateName(user.getUserName());
            pdRank.setCreateDate(date);
            pdRank.setOrg(user.getOid());
            pdRank.setEnabled(1);
            pdRankDao.save(pdRank);

            pdRankHistoryDao.insert(pdRank);
        }

        return "success";
    }

    @Override
    public List<PdRankHistory> getRankRecord(Integer oid) {
        String hql = "from PdRankHistory where  org=:org group by createDate";
        Map param = new HashMap();
        param.put("org", oid);
        return pdRankHistoryDao.getListByHQLWithNamedParams(hql, param);
    }

    @Override
    public List<PdRankHistory> getRankRecordDetail(Long id) {
        PdRankHistory pdRankHistory = pdRankHistoryDao.get(id);

        String hql = "from PdRankHistory where  createDate=:createDate";
        Map param = new HashMap();
        param.put("createDate", pdRankHistory.getCreateDate());
        return pdRankHistoryDao.getListByHQLWithNamedParams(hql, param);
    }

    @Override
    public List<PdCheckControl> getPdCheckControlList(Integer oid, Integer enabled) {
        String hql = "from PdCheckControl where  org=:org and enabled=:enabled";
        Map param = new HashMap();
        param.put("org", oid);
        param.put("enabled", enabled);
        return pdCheckControlDao.getListByHQLWithNamedParams(hql, param);
    }

    @Override
    public String addCheckControl(PdCheckControl checkControl) {
        pdCheckControlDao.save(checkControl);
        pdCheckControlHistoryDao.insertHistory(checkControl);
        return "success";
    }

    @Override
    public String updateCheckControl(PdCheckControl checkControl) {
        pdCheckControlDao.update(checkControl);
        pdCheckControlHistoryDao.insertHistory(checkControl);
        return "success";
    }

    @Override
    public String startOrStopCheckControl(Long id, Integer enabled, User user) {
        PdCheckControl checkControl = pdCheckControlDao.get(id);
        checkControl.setEnabled(enabled);
        if (enabled == 1) {
            checkControl.setOperation(5);
        } else {
            checkControl.setOperation(4);
        }
        checkControl.setUpdator(user.getUserID());
        checkControl.setUpdateName(user.getUserName());
        checkControl.setUpdateDate(new Date());
        pdCheckControlDao.update(checkControl);
        pdCheckControlHistoryDao.insertHistory(checkControl);
        return "success";
    }

    @Override
    public String deleteCheckControl(Long id) {
        pdCheckControlDao.deleteById(id);
        return "success";
    }

    @Override
    public List<Map<String, Object>> getCheckControlRecord(Long id) {
        String hql = "select new map(id as id,createName as createName,createDate as createDate,updateName as updateName,updateDate as updateDate ,operation as operation) from PdCheckControlHistory where checkControl=:checkControl ";
        Map param = new HashMap();
        param.put("checkControl", id);
        return pdCheckControlHistoryDao.getListByHQLWithNamedParams(hql, param);
    }

    @Override
    public PdCheckControlHistory getCheckControlRecordDetail(Long id) {
        return pdCheckControlHistoryDao.get(id);
    }

    @Override
    public String addFeatureItem(PdFeatureItem pdFeatureItem, User user) {

        Date date = new Date();

        pdFeatureItem.setCreator(user.getCreator());
        pdFeatureItem.setCreateName(user.getUserName());
        pdFeatureItem.setCreateDate(date);
        pdFeatureItem.setUpdator(user.getCreator());
        pdFeatureItem.setUpdateName(user.getUserName());
        pdFeatureItem.setUpdateDate(date);
        pdFeatureItem.setEnabled(1);
        pdFeatureItem.setOrg(user.getOid());
        pdFeatureItem.setOperation(1);
        pdFeatureItemDao.save(pdFeatureItem);

        if(StringUtils.isNotEmpty(pdFeatureItem.getFeatureOptionJson())){
            List<PdFeatureOption> featureOptionList = JSON.parseArray(pdFeatureItem.getFeatureOptionJson(), PdFeatureOption.class);
            for (PdFeatureOption featureOption : featureOptionList) {
                featureOption.setCategory(0);
                featureOption.setCreator(user.getCreator());
                featureOption.setCreateName(user.getUserName());
                featureOption.setCreateDate(date);
                featureOption.setOperation(1);
                featureOption.setEnabled(1);
                pdFeatureOptionDao.save(featureOption);

                //新增关联
                PdItemOption itemOption = new PdItemOption();
                itemOption.setItem(pdFeatureItem.getId());
                itemOption.setOption(featureOption.getId());
                itemOption.setCreator(user.getCreator());
                itemOption.setCreateName(user.getUserName());
                itemOption.setCreateDate(date);
                itemOption.setOperation(1);
                itemOption.setEnabled(1);
                pdItemOptionDao.save(itemOption);

            }
        }


        if(StringUtils.isNotEmpty(pdFeatureItem.getItemCheckJson1())){
            //1类
            List<PdItemCheck> itemCheckList1 = JSON.parseArray(pdFeatureItem.getItemCheckJson1(), PdItemCheck.class);
            for (PdItemCheck pdItemCheck : itemCheckList1) {
                pdItemCheck.setItem(pdFeatureItem.getId());
                pdItemCheck.setCreator(user.getCreator());
                pdItemCheck.setCreateName(user.getUserName());
                pdItemCheck.setCreateDate(date);
                pdItemCheck.setOperation(1);
                pdItemCheck.setEnabled(1);
                pdItemCheck.setMethod(1);
                pdItemCheck.setProduct(pdFeatureItem.getProduct());
                pdItemCheck.setOrg(pdFeatureItem.getOrg());
                pdItemCheckDao.save(pdItemCheck);
            }
        }
        if(StringUtils.isNotEmpty(pdFeatureItem.getItemCheckJson2())){
            //2类
            List<PdItemCheck> itemCheckList2 = JSON.parseArray(pdFeatureItem.getItemCheckJson2(), PdItemCheck.class);
            for (PdItemCheck pdItemCheck : itemCheckList2) {
                pdItemCheck.setItem(pdFeatureItem.getId());
                pdItemCheck.setCreator(user.getCreator());
                pdItemCheck.setCreateName(user.getUserName());
                pdItemCheck.setCreateDate(date);
                pdItemCheck.setOperation(1);
                pdItemCheck.setEnabled(1);
                pdItemCheck.setMethod(2);
                pdItemCheck.setProduct(pdFeatureItem.getProduct());
                pdItemCheck.setOrg(pdFeatureItem.getOrg());
                pdItemCheckDao.save(pdItemCheck);
            }
        }
        if(StringUtils.isNotEmpty(pdFeatureItem.getItemCheckJson3())){
            //3类
            List<PdItemCheck> itemCheckList3 = JSON.parseArray(pdFeatureItem.getItemCheckJson3(), PdItemCheck.class);
            for (PdItemCheck pdItemCheck : itemCheckList3) {
                pdItemCheck.setItem(pdFeatureItem.getId());
                pdItemCheck.setCreator(user.getCreator());
                pdItemCheck.setCreateName(user.getUserName());
                pdItemCheck.setCreateDate(date);
                pdItemCheck.setOperation(1);
                pdItemCheck.setEnabled(1);
                pdItemCheck.setMethod(3);
                pdItemCheck.setProduct(pdFeatureItem.getProduct());
                pdItemCheck.setOrg(pdFeatureItem.getOrg());
                pdItemCheckDao.save(pdItemCheck);
            }
        }

        return "success";
    }

    @Override
    public String updateFeatureItem(PdFeatureItem pdFeatureItem, User user) {
        Date date = new Date();
        pdFeatureItem.setCreator(user.getCreator());
        pdFeatureItem.setCreateName(user.getUserName());
        pdFeatureItem.setCreateDate(date);
        pdFeatureItem.setUpdator(user.getCreator());
        pdFeatureItem.setUpdateName(user.getUserName());
        pdFeatureItem.setUpdateDate(date);
        pdFeatureItem.setEnabled(1);
        pdFeatureItem.setOrg(user.getOid());
        pdFeatureItem.setOperation(2);
        pdFeatureItemDao.update(pdFeatureItem);

        //删除特性选项列表
        pdItemOptionDao.queryHQLWithNamedParams("delete from PdItemOption where item =" + pdFeatureItem.getId(), new HashMap<>());

        if(StringUtils.isNotEmpty(pdFeatureItem.getFeatureOptionJson())){
            List<PdFeatureOption> featureOptionList = JSON.parseArray(pdFeatureItem.getFeatureOptionJson(), PdFeatureOption.class);
            for (PdFeatureOption featureOption : featureOptionList) {
                featureOption.setCategory(0);
                featureOption.setCreator(user.getCreator());
                featureOption.setCreateName(user.getUserName());
                featureOption.setCreateDate(date);
                featureOption.setOperation(1);
                featureOption.setEnabled(1);
                pdFeatureOptionDao.save(featureOption);

                //新增关联
                PdItemOption itemOption = new PdItemOption();
                itemOption.setItem(pdFeatureItem.getId());
                itemOption.setOption(featureOption.getId());
                itemOption.setCreator(user.getCreator());
                itemOption.setCreateName(user.getUserName());
                itemOption.setCreateDate(date);
                itemOption.setOperation(1);
                itemOption.setEnabled(1);
                pdItemOptionDao.save(itemOption);

            }
        }

        //删除所有类
        pdFeatureOptionDao.queryHQLWithNamedParams("delete from PdItemCheck where item =" + pdFeatureItem.getId(), new HashMap<>());

        if(StringUtils.isNotEmpty(pdFeatureItem.getItemCheckJson1())){
            //1类
            List<PdItemCheck> itemCheckList1 = JSON.parseArray(pdFeatureItem.getItemCheckJson1(), PdItemCheck.class);
            for (PdItemCheck pdItemCheck : itemCheckList1) {
                pdItemCheck.setItem(pdFeatureItem.getId());
                pdItemCheck.setCreator(user.getCreator());
                pdItemCheck.setCreateName(user.getUserName());
                pdItemCheck.setCreateDate(date);
                pdItemCheck.setOperation(1);
                pdItemCheck.setEnabled(1);
                pdItemCheck.setMethod(1);
                pdItemCheck.setProduct(pdFeatureItem.getProduct());
                pdItemCheck.setOrg(pdFeatureItem.getOrg());
                pdItemCheckDao.save(pdItemCheck);
            }
        }

        if(StringUtils.isNotEmpty(pdFeatureItem.getItemCheckJson2())){
            //2类
            List<PdItemCheck> itemCheckList2 = JSON.parseArray(pdFeatureItem.getItemCheckJson2(), PdItemCheck.class);
            for (PdItemCheck pdItemCheck : itemCheckList2) {
                pdItemCheck.setItem(pdFeatureItem.getId());
                pdItemCheck.setCreator(user.getCreator());
                pdItemCheck.setCreateName(user.getUserName());
                pdItemCheck.setCreateDate(date);
                pdItemCheck.setOperation(1);
                pdItemCheck.setEnabled(1);
                pdItemCheck.setMethod(2);
                pdItemCheck.setProduct(pdFeatureItem.getProduct());
                pdItemCheck.setOrg(pdFeatureItem.getOrg());
                pdItemCheckDao.save(pdItemCheck);
            }
        }
        if(StringUtils.isNotEmpty(pdFeatureItem.getItemCheckJson3())){
            //3类
            List<PdItemCheck> itemCheckList3 = JSON.parseArray(pdFeatureItem.getItemCheckJson3(), PdItemCheck.class);
            for (PdItemCheck pdItemCheck : itemCheckList3) {
                pdItemCheck.setItem(pdFeatureItem.getId());
                pdItemCheck.setCreator(user.getCreator());
                pdItemCheck.setCreateName(user.getUserName());
                pdItemCheck.setCreateDate(date);
                pdItemCheck.setOperation(1);
                pdItemCheck.setEnabled(1);
                pdItemCheck.setMethod(3);
                pdItemCheck.setProduct(pdFeatureItem.getProduct());
                pdItemCheck.setOrg(pdFeatureItem.getOrg());
                pdItemCheckDao.save(pdItemCheck);
            }
        }

        return "success";
    }

    @Override
    public List<PdFeatureItem> getFeatureItemList(Integer product,Integer oid) {
        String hql = "from PdFeatureItem where product=" + product;
        List<PdFeatureItem> list = pdFeatureItemDao.getListByHQLWithNamedParams(hql, new HashMap<>());

        List<MtUnit> units=mtUnitDao.getListByHQLWithNamedParams("from MtUnit where org="+oid,new HashMap<>());
        for (PdFeatureItem item:list ) {
            if(item.getUnitId()!=null && item.getUnitId()==900001){
                item.setUnit("mm");
            }else  if(item.getUnitId()!=null && item.getUnitId()==900002){
                item.setUnit("cm");
            }else  if(item.getUnitId()!=null && item.getUnitId()==900003){
                item.setUnit("dm");
            }else  if(item.getUnitId()!=null && item.getUnitId()==900004){
                item.setUnit("m");
            }else{
                for (MtUnit unit:units) {
                    if(item.getUnitId()!=null&&item.getUnitId().intValue()==unit.getId()){
                        item.setUnit(unit.getName());
                        break;
                    }
                }
            }

        }
        //获取所有检测方法列表
        List<PdItemCheck> pdItemCheckList=pdItemCheckDao.getListByHQLWithNamedParams("from PdItemCheck where product="+product,new HashMap<>());
        //或有所有3类控制放啊
        List<PdCheckControl> checkControlList=pdCheckControlDao.getListByHQLWithNamedParams("from PdCheckControl where org="+oid,new HashMap<>());
        //获取所有等级
        List<PdRank> rankList=pdRankDao.getListByHQLWithNamedParams("from PdRank where org="+oid,new HashMap<>());

        //获取设备列表
        TEquEquipment m = new TEquEquipment();
        m.setOrg(Long.valueOf(oid));
        List<TEquEquipment> equEquipments=tEquEquipmentMapper.selectTEquEquipmentList(m);

        //获取型号列表
        TEquModel emp = new TEquModel();
        emp.setOrg(Long.valueOf(oid));
       // List<TEquModel> tEquEquipmentList=tEquModelMapper.selectTEquModelList(emp);
        List<Map<String, Object>> tEquEquipmentList= tEquModelMapper.selectEquModelDetailList(emp);

        for (PdFeatureItem pdFeatureItem : list) {
            //获取选项
            List<PdFeatureOption> pdFeatureOptionList = pdFeatureOptionDao.getListByHQLWithNamedParams("from PdFeatureOption where id in (select option from PdItemOption where item=" + pdFeatureItem.getId() + ")", new HashMap<>());
            pdFeatureItem.setFeatureOptionList(pdFeatureOptionList);


            List<PdItemCheck> l1 = new ArrayList<>();
            List<PdItemCheck> l2 = new ArrayList<>();
            List<PdItemCheck> l3 = new ArrayList<>();
            for (PdItemCheck pdItemCheck:pdItemCheckList) {
                if(pdFeatureItem.getId().longValue()!=pdItemCheck.getItem().longValue()){
                    continue;
                }
                //1类
                if(pdItemCheck.getMethod()==1){
                    for (TEquEquipment ee:equEquipments) {
                        if(ee.getId().longValue()==pdItemCheck.getEquEquipment().longValue()){
                            pdItemCheck.setEquEquipmentName(ee.getName());
                            break;
                        }
                    }

                    for (Map<String, Object> em:tEquEquipmentList) {
                        Long mid= (Long) em.get("mid");
                        if(mid.longValue()==pdItemCheck.getEquModel().longValue()){
                            pdItemCheck.setEquModelName((String) em.get("modelName"));
                            String emCount= (String) em.get("emCount");
                            if(emCount!=null){
                                pdItemCheck.setNum(emCount.split(",").length);
                            }
                            break;
                        }
                    }
                    l1.add(pdItemCheck);
                }
                //2类
                if(pdItemCheck.getMethod()==2){
                    for (TEquEquipment ee:equEquipments) {
                        if(ee.getId().longValue()==pdItemCheck.getEquEquipment().longValue()){
                            pdItemCheck.setEquEquipmentName(ee.getName());
                            break;
                        }
                    }

                    for (Map<String, Object> em:tEquEquipmentList) {
                        Long mid= (Long) em.get("mid");
                        if(mid.longValue()==pdItemCheck.getEquModel().longValue()){
                            pdItemCheck.setEquModelName((String) em.get("modelName"));
                            String emCount= (String) em.get("emCount");
                            if(emCount!=null){
                                pdItemCheck.setNum(emCount.split(",").length);
                            }

                            break;
                        }
                    }
                    l2.add(pdItemCheck);
                }
                //3类
                if(pdItemCheck.getMethod()==3){
                    if(pdItemCheck.getCheckControl()!=null){
                        for (PdCheckControl pdCheckControl:checkControlList) {
                            if(pdCheckControl.getId().longValue()==pdItemCheck.getCheckControl().longValue()){
                                pdItemCheck.setCheckControlObj(pdCheckControl);
                                break;
                            }
                        }
                    }

                    l3.add(pdItemCheck);
                }
            }
            pdFeatureItem.setItemCheckList1(l1);
            pdFeatureItem.setItemCheckList2(l2);
            pdFeatureItem.setItemCheckList3(l3);

            //匹配等级信息
            for (PdRank rank:rankList) {
                if(pdFeatureItem.getRank()!=null){
                    if(rank.getId().longValue() == pdFeatureItem.getRank()){
                        pdFeatureItem.setRankName(rank.getName());
                        pdFeatureItem.setRankSymbol(rank.getSymbol());
                        break;
                    }
                }

            }

        }


        return list;
    }

    @Override
    public List<PdFeatureItem> getFeatureItemListByRank(Integer rankId) {
        String hql = "from PdFeatureItem where rank=" + rankId;
        List<PdFeatureItem> list = pdFeatureItemDao.getListByHQLWithNamedParams(hql, new HashMap<>());
        return list;
    }

    @Override
    public String deleteFeatureItem(Long id) {


        //删除特性选项列表
        pdFeatureOptionDao.queryHQLWithNamedParams("delete from PdItemOption where item =" + id, new HashMap<>());

        //删除所有类
        pdFeatureOptionDao.queryHQLWithNamedParams("delete from PdItemCheck where item =" + id, new HashMap<>());
        pdFeatureItemDao.deleteById(id);
        return "success";
    }

    @Override
    public String productFeatureSetting(Integer product,User user) {
        PdBase pdBase=pdBaseDao.get(product);
        if(pdBase!=null){
            pdBase.setInitState(2);
            pdBase.setInitiator(user.getUserID());
            pdBase.setInitiatorName(user.getUserName());
            pdBase.setInitTime(new Date());
            pdBaseDao.update(pdBase);
        }
        return "success";
    }
}
