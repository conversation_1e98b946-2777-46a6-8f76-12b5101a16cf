<template>
  <div class="ui-cells" id="myApply">
    <a class="ui-cell" v-on:click="applyTo(item)" v-if="item.pid == 2" v-for="(item, index) in $store.state.menu" v-bind:key="index">
      <div class="ui-cell__bd">
        <div class="ui-cell_title">
          <span class="approve_name" v-html="item.name"></span>
          <span :class="!item.corner? 'hd': 'message_count'" v-html="item.corner"></span>
        </div>
      </div>
      <div class="ui-cell__ft"></div>
    </a>
  </div>
</template>
<script>
export default {
  name: 'homeApply',
  data () {
    return {}
  },
  created () {
    this.$store.dispatch('setNewHomeNav', 1)
    window.localStorage.setItem('floating', '')
  },
  methods: {
    applyTo: function (item) {
      let str = 'releaseApply' + 'versionApply' + 'invoiceApply' + 'returnInput' + 'complaintInput' + 'potentialCustomerApply' +
        'approvalSettingsApply' + 'tryApply' + 'editOrgPopedomApply' + 'formulaEditApply' + 'fileBorrowApply' + 'myAttendanceEditApply' +
        'incrementApply' + 'incrementEditApply' + 'contentReleaseApply' + 'contentVersionApply'
      if (str.indexOf(item.code) > -1) {
        this.$router.push({
          path: '/' + item.code
        })
      } else {
        this.$router.push({
          path: `/${item.code}/${item.id}`
        })
      }
    }
  }
}
</script>
