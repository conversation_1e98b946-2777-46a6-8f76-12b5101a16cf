<!-- 吴老师audio3 改的 改的很晕 先放着， 主要是 生成vtt 字幕有问题， 目前不是流的方式 没跑通 放着放着  -->

<template>
  <div class="audio">
    <div id='player'>
      <video id='video' ref="video" controls preload crossorigin='anonymous' playsinline='' webkit-playsinline='' x5-video-player-type='h5' >
        <source :src="src">
        <track id='track' ref="track" src='EasonChan-LonelyWarrior/EasonChan-LonelyWarrior.vtt' srclang='zh-cn' label='简体中文' kind='subtitles' default >
      </video>
      <div id='display' v-html="lyTxt"></div>
    </div>

  </div>
</template>

<script type='text/javascript'>
import { sendToServer, setLyricLive, lyricLive } from '@/api/api.js'
import auth from '@/sys/auth.js'
import { getDeviceType } from "@/utils/browerUtils";

export default {
  name: "Audio",
  data() {
    return {
      videoInfoEvent:'loadedmetadata',
      timer:'-1',
      lyTxt:'',
      tpMemberId:'',
      hls: null,
      src: '',
      url:''
    };
  },
  components: {
  },
  props: {
    lycTxt: {
      type: String,
      default: ''
    },
    playUrl: {
      type: String,
      default: ''
    }
  },
  mounted() {
    let _this = this
    let videoInfoEvent = this.videoInfoEvent
    console.log('tpMemberId = ', this.tpMemberId)
    console.log('playUrl = ', this.playUrl)
    console.log('lycTxt = ', this.lycTxt)
    let root = auth.webRoot + '/upload/'
    _this.src = root + this.playUrl
    _this.url = root + this.playUrl
    let user = auth.getUser()
    console.log('auth=', user)
    _this.tpMemberId = auth.getTpMemberId()
    console.log('_this.tpMemberId=',  _this.tpMemberId )

    let kind = ''
    if (getDeviceType() == 'IOS') {
      kind = 'metadata'
      this.$refs.video.controlsList = 'nofullscreen nodownload noremote footbar'
    } else {
      kind = 'subtitles'
      this.$refs.video.controlsList = 'nodownload noremote footbar'
    }



    if(true){
      this.$refs.video.addEventListener('loadedmetadata', () => {
        this.$refs.video.play();
      });
      this.$refs.video.addEventListener(videoInfoEvent, function () {
        console.log(videoInfoEvent, _this.$refs.video.currentTime)
        let timeMillis = Math.round(_this.$refs.video.currentTime * 1000)
        _this.createLyricLive(timeMillis)
      })
      this.$refs.video.addEventListener('play', function () {
        console.log('play', _this.$refs.video.currentTime)
        let timeMillis = Math.round(_this.$refs.video.currentTime * 1000)
        _this.playLiveTime(timeMillis)
      })
      this.$refs.video.addEventListener('playing', function () {
        console.log('playing', _this.$refs.video.currentTime)
        let timeMillis = Math.round(_this.$refs.video.currentTime * 1000)
        _this.playLiveTime(timeMillis)
      })
      this.$refs.video.addEventListener('pause', function () {
        console.log('pause')
        _this.stopLiveTime()
      })
      this.$refs.video.addEventListener('waiting', function () {
        console.log('waiting')
        _this.stopLiveTime()
      })
      this.$refs.video.addEventListener('seeked', function () {
        console.log('seeked', _this.$refs.video.currentTime)
        let timeMillis = Math.round(_this.$refs.video.currentTime * 1000)
        _this.seekLiveTime(timeMillis)
      })
      this.$refs.video.addEventListener('cuechange', function () {
        let myTrack = this.$refs.track             // track element is 'this'
        let myCues = myTrack.activeCues      // activeCues is an array of current cues.
        let line = ''
        if (myCues.length > 0) {
          line = myCues[0].text   // write the text
        }
        let data = {
          tpMemberId : this.tpMemberId,
          line : line
        }
        setLyricLive(data)

      })

    }

    if (Hls.isSupported()) {
      this.hls = new Hls();
      this.hls.loadSource(this.url);
      this.hls.attachMedia(this.$refs.video);
      this.hls.on(Hls.Events.MANIFEST_PARSED, () => { this.$refs.video.play(); });
      let track = this.$refs.track
      track.src = this.url
      track.srclang = 'zh-cn'
      track.label = '简体中文'
      track.kind = kind
      track.default = 'default'
    }
    else if (this.$refs.video.canPlayType('application/vnd.apple.mpegurl')){
      this.$refs.video.src = this.url;
    }


  },
  destroyed() {
    if (this.hls) {
      this.hls.destroy();
    }
  },
  methods: {
    stopPlay(){
      // 停止播放

    },
    getLyTxt(timeMillis){
      let _this = this
      // this.timer =  setInterval(function() {
      // },250)

      let data = {
        tpMemberId : _this.tpMemberId
      }
      data.timeMillis = timeMillis
      lyricLive(data).then(res => {
        // console.log('lyricLive res=', res)
        _this.lyTxt =  res.data
      })

    },
    createLyricLive(timeMillis) {
      sendToServer('/lyric/createLyricLive.do',{
        tpMemberId : this.tpMemberId,
        timeMillis : timeMillis
      })
    },
    playLiveTime(timeMillis) {
      if(this.lycTxt){
        // this.getLyTxt()
      }
      sendToServer('/lyric/playLiveTime.do',{
        tpMemberId : this.tpMemberId,
        timeMillis : timeMillis
      })
    },
    stopLiveTime() {
      sendToServer('/lyric/stopLiveTime.do',{
        tpMemberId : this.tpMemberId
      })
    },
    seekLiveTime(timeMillis) {
      sendToServer('/lyric/seekLiveTime.do',{
        tpMemberId : this.tpMemberId,
        timeMillis : timeMillis
      })
    }


  },
};

</script>

<style type='text/css' scoped>
#player{
  width:100%;height:100%;text-align:center;
  position: relative;
}
#video{ width:80%;height:300px; display: block; margin:0 auto;  }
#display{
  position:absolute;
  display:block;
  width: 100%;
  writing-mode: vertical-rl;
  text-align :center;
  color:yellow;
  font-size:24px;
  font-family:Comic Sans MS;
  text-shadow: 0.1em 0.1em 0.15em #333;
  z-index:100; /* set z-index to be sure div is on top */
}
video {
  border: 1px solid #aaa;
  object-fit: initial;
}

::cue {
  color:blue;
  font-size:24px;
  line-height:100px;
  background-color:rgba(0,0,0,0) !important;
}
</style>

