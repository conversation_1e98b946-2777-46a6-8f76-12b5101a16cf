export default [
  // ----------培训管理----------
  {
    path: '/testPaperApproval',
    name: 'testPaperApproval',
    component: () => import('@/view/approve/trainManage/testPaperApproval'),
    meta: {
      title: '试卷'
    }
  },
  {
    path: '/testDetails',
    name: 'testDetails',
    component: () => import('@/view/approve/trainManage/testDetails'),
    meta: {
      title: '试卷'
    }
  },
  {
    path: '/questionList/:id',
    name: 'questionList',
    component: () => import('@/view/approve/trainManage/questionList'),
    meta: {
      title: '试卷'
    }
  },
  {
    path: '/question',
    name: 'question',
    component: () => import('@/view/approve/trainManage/question'),
    meta: {
      title: '试卷'
    }
  },
  {
    path: '/examResult/:id',
    name: 'examResult',
    component: () => import('@/view/approve/trainManage/examResult'),
    meta: {
      title: '试卷'
    }
  },
  {
    path: '/answerDetail',
    name: 'answerDetail',
    component: () => import('@/view/approve/trainManage/answerDetail'),
    meta: {
      title: '试卷'
    }
  },
  // ----------模板管理、产品管理----------
  {
    path: '/productManageApproval/:pid',
    name: 'productManageApproval',
    component: () => import('@/view/approve/wonderssProduct/productManageApproval'),
    meta: {
      title: '产品管理'
    }
  },
  {
    path: '/templateManageApproval/:pid',
    name: 'templateManageApproval',
    component: () => import('@/view/approve/wonderssProduct/templateManageApproval'),
    meta: {
      title: '模板管理'
    }
  },
  {
    path: '/productManageApply/:pid',
    name: 'productManageApply',
    component: () => import('@/view/apply/wonderssProduct/productManageApply'),
    meta: {
      title: '产品管理'
    }
  },
  {
    path: '/templateManageApply/:pid',
    name: 'templateManageApply',
    component: () => import('@/view/apply/wonderssProduct/templateManageApply'),
    meta: {
      title: '模板管理'
    }
  },
  {
    path: '/templateOrProjectList/:code',
    name: 'templateOrProjectList',
    component: () => import('@/view/apply/wonderssProduct/templateOrProjectList'),
    meta: {
      title: '模板/产品 创建/修改 产品恢复使用'
    }
  },
  {
    path: '/templateOrProjectDetails/:code/:id/:isSearch',
    name: 'templateOrProjectDetails',
    component: () => import('@/view/apply/wonderssProduct/templateOrProjectDetails'),
    meta: {
      title: '模板（产品）创建、修改 产品恢复使用--详情'
    }
  },
  {
    path: '/templateOrProjectSearch/:code',
    name: 'templateOrProjectSearch',
    component: () => import('@/view/apply/wonderssProduct/templateOrProjectSearch'),
    meta: {
      title: '模板（产品）创建、修改 产品恢复使用--查询'
    }
  },
  {
    path: '/templateOrProjectSearchList/:code',
    name: 'templateOrProjectSearchList',
    component: () => import('@/view/apply/wonderssProduct/templateOrProjectSearchList'),
    meta: {
      title: '模板（产品）创建、修改 产品恢复使用--查询结果'
    }
  },
  {
    path: '/moduleDetail',
    name: 'moduleDetail',
    component: () => import('@/view/apply/wonderssProduct/moduleDetail'),
    meta: {
      title: '模块详情'
    }
  },
  {
    path: '/menuDetail',
    name: 'menuDetail',
    component: () => import('@/view/apply/wonderssProduct/menuDetail'),
    meta: {
      title: '套餐详情'
    }
  }
]
