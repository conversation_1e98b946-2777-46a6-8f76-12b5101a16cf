package cn.sphd.miners.modules.dmmSchedule.controller;

import cn.sphd.miners.common.initializer.AuthPassport;
import cn.sphd.miners.common.persistence.JsonResult;
import cn.sphd.miners.common.persistence.MyException;
import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.auth.entity.AuthAcc;
import cn.sphd.miners.modules.dailyAffairs.service.UserSuspendMsgService;
import cn.sphd.miners.modules.dmmSchedule.entity.*;
import cn.sphd.miners.modules.dmmSchedule.service.*;
import cn.sphd.miners.modules.schedule.entity.*;
import cn.sphd.miners.modules.schedule.service.*;
import cn.sphd.miners.modules.system.dto.UserLoginDto;
import cn.sphd.miners.modules.system.entity.User;
import cn.sphd.miners.modules.system.service.UserService;
import cn.sphd.miners.modules.uploads.entity.UploadFile;
import cn.sphd.miners.modules.uploads.service.UploadService;
import io.netty.util.internal.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by Administrator on 2023/2/28.
 * 领地备忘
 */
@Controller
@RequestMapping("/dmmSchedule")
public class DmmScheduleController {
    @Autowired
    DmmScheduleService dmmScheduleService;
    @Autowired
    DmmJobService dmmJobService;
    @Autowired
    UserService userService;
    @Autowired
    DmmScheduleHistoryService dmmScheduleHistoryService;
    @Autowired
    DmmScheduleImageService dmmScheduleImageService;
    @Autowired
    DmmScheduleSupplementService dmmScheduleSupplementService;
    @Autowired
    UserSuspendMsgService userSuspendMsgService;
    @Autowired
    DmmJobMsgService dmmJobMsgService;
    @Autowired
    UploadService uploadService;
    @Autowired
    MemoScheduleService memoScheduleService;
    @Autowired
    MemoScheduleShareService memoScheduleShareService;
    @Autowired
    MemoJobService memoJobService;
    @Autowired
    MemoScheduleHistoryService memoScheduleHistoryService;
    @Autowired
    MemoScheduleImageService memoScheduleImageService;
    @Autowired
    MemoScheduleSupplementService memoScheduleSupplementService;
    @Autowired
    MemoJobMsgService memoJobMsgService;

    /**
    * <AUTHOR>
    * @Date 2023/3/9
    * 事件管理主页数据
    */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/dmmScheduleManageList.do")
    public JsonResult dmmScheduleManageList(AuthAcc acc,Integer passiveUserId, PageInfo pageInfo,String keyword){

        Map<String,Object> map=new HashMap<>();
        Integer number=0;
        if (passiveUserId!=null){ //选择了 机构内查询
            List<MemoSchedule> memoScheduleList=memoScheduleService.getMemoScheduleListByUserId(passiveUserId,pageInfo,keyword);
            number=memoScheduleService.getMemoScheduleCounts(passiveUserId);
            map.put("memoScheduleList",memoScheduleList);
        }else { //否则查领地
            List<DmmSchedule> dmmScheduleList=dmmScheduleService.getDmmScheduleListByUserId(acc.getId(),pageInfo,keyword);
            number=dmmScheduleService.getDmmScheduleCounts(acc.getId());
            map.put("memoScheduleList",dmmScheduleList);
        }
        map.put("pageInfo",pageInfo);
        map.put("number",number);
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Date 2023/3/9
    * 新增日程
    */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/addDmmSchedule.do")
    public JsonResult addDmmSchedule(AuthAcc acc, Integer passiveUserId, DmmSchedule dmmSchedule, String[] imagePaths, String startDate, Integer[] shareableUserIds,HttpServletRequest request) throws Exception {
        Integer status=0;//失败
        if (acc==null) {
            throw new Exception("登陆账号不能为空！");
        }
        if (passiveUserId!=null) { // 领地选择的机构userId
            User scheduleUser = userService.getUserByID(passiveUserId);

            MemoSchedule memoSchedule=new MemoSchedule();
            BeanUtils.copyProperties(dmmSchedule, memoSchedule);

            if (!StringUtil.isNullOrEmpty(startDate)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

                memoSchedule.setActiveStartDate(sdf.parse(startDate));//首次 执行时间
                if (memoSchedule.getActiveStartDate().getTime() <= new Date().getTime()) {
                    status = 2;//时间已过时，日程不能新增
                } else {
                    imagePaths=copyFileToOrg(imagePaths,scheduleUser,request); //把上传的文件 复制到机构中，返回机构文件路径列表。
                    memoScheduleService.saveMemoSchedule(memoSchedule, scheduleUser, "1", imagePaths);
                    status = 1;//成功
                }
            } else {
                imagePaths=copyFileToOrg(imagePaths,scheduleUser,request); //把上传的文件 复制到机构中，返回机构文件路径列表。
                memoScheduleService.saveMemoSchedule(memoSchedule, scheduleUser, "1", imagePaths);
                status = 1;//成功
            }
            if (status==1) {
                if (shareableUserIds!=null&&shareableUserIds.length > 0){
                    memoScheduleShareService.addMemoScheduleShareByMemoSchedule(memoSchedule, scheduleUser, shareableUserIds); //新建日常直接分享   2021/1/20 1.150备忘收藏版更改
                }
            }
        }else {
            if (!StringUtil.isNullOrEmpty(startDate)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                dmmSchedule.setActiveStartDate(sdf.parse(startDate));//首次 执行时间
                if (dmmSchedule.getActiveStartDate().getTime() <= new Date().getTime()) {
                    status = 2;//时间已过时，日程不能新增
                } else {
                    dmmScheduleService.saveDmmSchedule(dmmSchedule, acc, "1", imagePaths);
                    status = 1;//成功
                }
            } else {
                dmmScheduleService.saveDmmSchedule(dmmSchedule, acc, "1", imagePaths);
                status = 1;//成功
            }
        }
        return new JsonResult(1,status);
    }


    String[] copyFileToOrg(String[] imagePaths,User scheduleUser,HttpServletRequest request) throws IOException {
        List<String> images=new ArrayList<>();
        for(String imagePath:imagePaths){
            UploadFile uploadFile= uploadService.copyFile(imagePath, scheduleUser, "备忘与日程", request);
            images.add(uploadFile.getFilename());
        }
        return images.toArray(new String[0]);
    }

    /**
    * <AUTHOR>
    * @Date 2023/3/9
    * 日程不再提醒
     * passiveUserId  领地中硬要操作机构内日程，要传对应用户id
    */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/dmmScheduleNoRemind.do")
    public JsonResult dmmScheduleNoRemind(Integer id, Integer passiveUserId){
        if (id==null) {
            return new JsonResult(new MyException("403", "没有传值！"));
        }
        if (passiveUserId!=null) {
            memoScheduleService.scheduleNoRemind(id);
        }else {
            dmmScheduleService.scheduleNoRemind(id);
        }
        return new JsonResult(1,1);
    }


    /**
    * <AUTHOR>
    * @Date 2023/3/9
    * 删除单个日程
     * passiveUserId  领地中硬要操作机构内日程，要传对应用户id
    */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/deleteDmmSchedule.do")
    public JsonResult deleteDmmSchedule(AuthAcc acc,Integer id,Integer passiveUserId) {
        if (id==null) {
            return new JsonResult(new MyException("403", "没有传值！"));
        }
        if (passiveUserId!=null){
            User passiveUser=userService.getUserByID(passiveUserId);
            memoScheduleService.deleteMemoSchedule(id, passiveUser);
        }else {
            dmmScheduleService.deleteDmmSchedule(id, acc);
        }
        return new JsonResult(1,1);
    }

    /**
    * <AUTHOR>
    * @Date 2023/3/9
    * 删除日程全部提醒历史记录
     * passiveUserId  领地中硬要操作机构内日程，要传对应用户id
    */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/deleteDmmJob.do")
    public JsonResult deleteDmmJob(AuthAcc acc,Integer id,Integer passiveUserId) {
        if (id==null) {
            return new JsonResult(new MyException("403", "没有传值！"));
        }
        if (passiveUserId!=null){  //操作机构内日程
            User passiveUser=userService.getUserByID(passiveUserId);
            MemoSchedule m = memoScheduleService.getMemoScheduleById(id);

            List<MemoJobMsg> memoJobMsgList=memoJobMsgService.getMemoJobMsgByScheduleId(id,null);
            for (MemoJobMsg memoJobMsg:memoJobMsgList){
                memoJobMsgService.updateMemoJobMsg(memoJobMsg,"4",passiveUser.getUserID());//目前未处理的 变成删除 并推送减去角标和数据
            }
            memoJobMsgService.deleteAllMemoJobMsg(memoJobMsgList);//删除所有该日程的具体提醒记录

            for (MemoJob mj : m.getMemoJobHashSet()) {
                mj.setEnabled(0);
                mj.setValid(false);
                memoJobService.deleteMemoJob(mj);
            }
            List<MemoScheduleSupplement> memoScheduleSupplementList=memoScheduleSupplementService.getMemoScheduleSupplementListBySid(id);
            for (MemoScheduleSupplement scheduleSupplement:memoScheduleSupplementList){
                memoScheduleService.delMemoScheduleSupplementImagFileUsing(scheduleSupplement.getMemoScheduleSupplementImageHashSet(),passiveUser);  //删除日程 补充记录 的 文件引用  2021/3/20 lixu 1.52空间与流量
            }

        }else {
            DmmSchedule m = dmmScheduleService.getDmmScheduleById(id);
            List<DmmJobMsg> dmmJobMsgList = dmmJobMsgService.getDmmJobMsgByScheduleId(id, null);
            for (DmmJobMsg dmmJobMsg : dmmJobMsgList) {
                dmmJobMsgService.updateDmmJobMsg(dmmJobMsg, "4", acc.getId());//目前未处理的 变成删除 并推送减去角标和数据
            }
            dmmJobMsgService.deleteAllDmmJobMsg(dmmJobMsgList);//删除所有该日程的具体提醒记录

            for (DmmJob mj : m.getDmmJobHashSet()) {
                mj.setEnabled(0);
                mj.setValid(false);
                dmmJobService.deleteDmmJob(mj);
            }
            List<DmmScheduleSupplement> dmmScheduleSupplementList = dmmScheduleSupplementService.getDmmScheduleSupplementListBySid(id);
            for (DmmScheduleSupplement scheduleSupplement : dmmScheduleSupplementList) {
                dmmScheduleService.delDmmScheduleSupplementImagFileUsing(scheduleSupplement.getDmmScheduleSupplementImageHashSet(), acc);  //删除日程 补充记录 的 文件引用  2021/3/20 lixu 1.52空间与流量
            }
        }
        return  new JsonResult(1,1);
    }

    /**
    * <AUTHOR>
    * @Date 2023/3/9
    * 日程结案确定接口
     * type 1-本次活动已完成 ，2-不再提醒 ，3-再次提醒
     * remindDate 提醒时间
     * passiveUserId  领地中硬要操作机构内日程，要传对应用户id
    */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/handleDmmSchedule.do")
    public JsonResult handleDmmSchedule(Integer id,Integer msgId,String type,String remindDate,Integer passiveUserId) {
        if (id!=null&&!StringUtil.isNullOrEmpty(type)) {
            Date date = new Date();
            long ss = 0L;
            switch (remindDate) {
                case "fiveMinutes":
                    ss = 300000;
                    break;
                case "tenMinutes":
                    ss = 600000;
                    break;
                case "fifteenMinutes":
                    ss = 900000;
                    break;
                case "halfHour":
                    ss = 30 * 60 * 1000;
                    break;
                case "oneHour":
                    ss = 60 * 60 * 1000;
                    break;
                case "twoHours":
                    ss = 2 * 60 * 60 * 1000;
                    break;
                case "fourHours":
                    ss = 4 * 60 * 60 * 1000;
                    break;
                case "eightHours":
                    ss = 8 * 60 * 60 * 1000;
                    break;
            }
            date = new Date(date.getTime() + ss);// 延时后的时间

            if (passiveUserId!=null){
                MemoSchedule memoSchedule=memoScheduleService.getMemoScheduleById(id);
                if (memoSchedule.getState()==2) {
                    if (!type.equals("3")) {
                        memoScheduleService.handleSchedule(memoSchedule, type, null);
                    } else {
                        memoScheduleService.handleSchedule(memoSchedule, type, date);
                    }

                    MemoJobMsg memoJobMsg = memoJobMsgService.getMemoJobMsgById(msgId);
                    memoJobMsgService.updateMemoJobMsg(memoJobMsg, "2", memoSchedule.getUser());
                    return new JsonResult(1,1); //成功
                }else {
                    // 状态已变更
                    return new JsonResult(new MyException("402", "状态已变更！"));
                }
            }else {
                DmmSchedule dmmSchedule = dmmScheduleService.getDmmScheduleById(id);
                if (dmmSchedule.getState() == 2) {
                    if (!type.equals("3")) {
                        dmmScheduleService.handleSchedule(dmmSchedule, type, null);
                    } else {
                        dmmScheduleService.handleSchedule(dmmSchedule, type, date);
                    }

                    DmmJobMsg dmmJobMsg = dmmJobMsgService.getDmmJobMsgById(msgId);
                    dmmJobMsgService.updateDmmJobMsg(dmmJobMsg, "2", dmmSchedule.getAccId());
                    //成功
                    return new JsonResult(1, 1);
                } else {
                    // 状态已变更
                    return new JsonResult(new MyException("402", "状态已变更！"));
                }
            }
        }else {
            return new JsonResult(new MyException("403", "没有传值！"));
        }
    }

    /**
    * <AUTHOR>
    * @Date 2023/3/9
    * 自定义提醒时间接口
     * passiveUserId  领地中硬要操作机构内日程，要传对应用户id
    */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/copyDmmSchedule.do")
    public JsonResult copyDmmSchedule(Integer msgId, DmmSchedule dmmSchedule, AuthAcc acc, String startDate, String[] imagePaths, Integer passiveUserId) throws IOException, ParseException {
        if (msgId==null) {
            return new JsonResult(new MyException("403", "没有传值！"));
        }
        if (passiveUserId!=null){

            User passiveUser=userService.getUserByID(passiveUserId);
            MemoSchedule memoSchedule=new MemoSchedule();
            BeanUtils.copyProperties(dmmSchedule,memoSchedule);
            if (!StringUtil.isNullOrEmpty(startDate)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
                memoSchedule.setActiveStartDate(sdf.parse(startDate));//首次 执行时间
            }
            memoSchedule.setFreqType(1);
            if (memoSchedule.getActiveStartDate().getTime() <= new Date().getTime()){
                return new JsonResult(new MyException("402", "时间已过时，日程不能新增！"));
            }else {
//                memoScheduleService.scheduleNoRemind(id);//日程不再提醒
                memoScheduleService.saveMemoSchedule(memoSchedule, passiveUser, "1", imagePaths);

                MemoJobMsg memoJobMsg=memoJobMsgService.getMemoJobMsgById(msgId);//具体的提醒
                MemoJob memoJob=memoJobService.getMemoJobById(memoJobMsg.getJob());
                memoJob.setState(3); //状态:1-已完成、2-提醒中、3-不再提醒、4-提醒已过期、5-尚未开始提醒
                memoJobService.updateMemoJob(memoJob);//历史日程 变为不再提醒。

                if (memoJob.getSchedule().getFreqType()>1) {
                    memoJob.getSchedule().setState(1);//原日程 是重复性日程  暂时从 提醒中 变为 "已完成"状态 主页详情展示为 下次提醒时间
                }else {
                    memoJob.getSchedule().setState(3);// 原日程是一次性日程， 变为 不再提醒。
                }
                memoJobMsgService.updateMemoJobMsg(memoJobMsg,"2",passiveUser.getUserID());//具体提醒变为已读
                return new JsonResult(1, 1);
            }

        }else {

            if (!StringUtil.isNullOrEmpty(startDate)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
                dmmSchedule.setActiveStartDate(sdf.parse(startDate));//首次 执行时间
            }
            dmmSchedule.setFreqType(1);
            if (dmmSchedule.getActiveStartDate().getTime() <= new Date().getTime()) {
                //时间已过时，日程不能新增
                return new JsonResult(new MyException("402", "时间已过时，日程不能新增！"));
            } else {
//                DmmScheduleService.scheduleNoRemind(id);//日程不再提醒
                dmmScheduleService.saveDmmSchedule(dmmSchedule, acc, "1", imagePaths);

                DmmJobMsg dmmJobMsg = dmmJobMsgService.getDmmJobMsgById(msgId);//具体的提醒
                DmmJob dmmJob = dmmJobService.getDmmJobById(dmmJobMsg.getJob());
                dmmJob.setState(3); //状态:1-已完成、2-提醒中、3-不再提醒、4-提醒已过期、5-尚未开始提醒
                dmmJobService.updateDmmJob(dmmJob);//历史日程 变为不再提醒。

                if (dmmJob.getSchedule().getFreqType() > 1) {
                    dmmJob.getSchedule().setState(1);//原日程 是重复性日程  暂时从 提醒中 变为 "已完成"状态 主页详情展示为 下次提醒时间
                } else {
                    dmmJob.getSchedule().setState(3);// 原日程是一次性日程， 变为 不再提醒。
                }
                dmmJobMsgService.updateDmmJobMsg(dmmJobMsg, "2", acc.getId());//具体提醒变为已读

                return new JsonResult(1, 1);
            }
        }
    }

    /**
    * <AUTHOR>
    * @Date 2023/3/10
    * 根据日程id，获取提醒历史记录
     *  passiveUserId  领地中硬要操作机构内日程，要传对应用户id
    */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/getDmmJobList.do")
    public JsonResult getDmmJobList(Integer sid,Integer passiveUserId){
        if (sid==null){
            return new JsonResult(new MyException("403", "没有传值！"));
        }
        Map<String, Object> map = new HashMap<>();

        if (passiveUserId!=null){
            MemoSchedule ms = memoScheduleService.getMemoScheduleById(sid);
            List<MemoJob> dmmJobList = memoJobService.getMemoJobListBySid(sid);

            map.put("dmmSchedule", ms);
            map.put("dmmJobList", dmmJobList);
        }else {
            DmmSchedule ms = dmmScheduleService.getDmmScheduleById(sid);
            List<DmmJob> dmmJobList = dmmJobService.getDmmJobListBySid(sid);

            map.put("dmmSchedule", ms);
            map.put("dmmJobList", dmmJobList);
        }
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Date 2023/3/10
    * 删除单条提醒记录
     *  passiveUserId  领地中硬要操作机构内日程，要传对应用户id
    */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/deleteOneDmmJob.do")
    public JsonResult deleteOneDmmJob(AuthAcc acc,Integer id,Integer passiveUserId) {
        if (id==null) {
            return new JsonResult(new MyException("403", "没有传值！"));
        }

        if (passiveUserId!=null){
            User passiveUser=userService.getUserByID(passiveUserId);
            MemoJob mj = memoJobService.getMemoJobById(id);
            mj.setValid(false);//改无效

            List<MemoJobMsg> memoJobMsgList=memoJobMsgService.getMemoJobMsgByJobId(id,null);
            for (MemoJobMsg memoJobMsg:memoJobMsgList){
                memoJobMsgService.updateMemoJobMsg(memoJobMsg,"4",passiveUserId);
            }
            memoJobMsgService.deleteAllMemoJobMsg(memoJobMsgList);//删除 提醒记录 的 每条具体记录

            if(1==mj.getSchedule().getFreqType()){
                mj.getSchedule().setValid(false);//改无效
//                memoScheduleService.updateMemoSchedule(mj.getSchedule());
                memoScheduleService.deleteMemoSchedule(mj.getSchedule());
            }else {
                memoJobService.deleteMemoJob(mj);
            }

            List<MemoScheduleSupplement> memoScheduleSupplementList=memoScheduleSupplementService.getMemoScheduleSupplementListByJobId(id);
            for (MemoScheduleSupplement scheduleSupplement:memoScheduleSupplementList){
                memoScheduleService.delMemoScheduleSupplementImagFileUsing(scheduleSupplement.getMemoScheduleSupplementImageHashSet(),passiveUser);  //删除提醒 补充记录 的 文件引用  2021/3/20 lixu 1.52空间与流量
            }

        }else {

            DmmJob mj = dmmJobService.getDmmJobById(id);
            mj.setValid(false);//改无效

            List<DmmJobMsg> dmmJobMsgList = dmmJobMsgService.getDmmJobMsgByJobId(id, null);
            for (DmmJobMsg dmmJobMsg : dmmJobMsgList) {
                dmmJobMsgService.updateDmmJobMsg(dmmJobMsg, "4", acc.getId());
            }
            dmmJobMsgService.deleteAllDmmJobMsg(dmmJobMsgList);//删除 提醒记录 的 每条具体记录

            if (1 == mj.getSchedule().getFreqType()) {
                mj.getSchedule().setValid(false);//改无效
//                DmmScheduleService.updateDmmSchedule(mj.getSchedule());
                dmmScheduleService.deleteDmmSchedule(mj.getSchedule());
            } else {
                dmmJobService.deleteDmmJob(mj);
            }

            List<DmmScheduleSupplement> dmmScheduleSupplementList = dmmScheduleSupplementService.getDmmScheduleSupplementListByJobId(id);
            for (DmmScheduleSupplement scheduleSupplement : dmmScheduleSupplementList) {
                dmmScheduleService.delDmmScheduleSupplementImagFileUsing(scheduleSupplement.getDmmScheduleSupplementImageHashSet(), acc);  //删除提醒 补充记录 的 文件引用  2021/3/20 lixu 1.52空间与流量
            }
        }

        return new JsonResult(1,1);
    }


    /**
    * <AUTHOR>
    * @Date 2023/3/10
    * 日程历史记录 中提醒改为不再提醒
     * passiveUserId  领地中硬要操作机构内日程，要传对应用户id
    */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/dmmJobNoRemind.do")
    public JsonResult dmmJobNoRemind(Integer id,Integer passiveUserId) {
        if (id==null) {
            return new JsonResult(new MyException("403", "没有传值！"));
        }
        if (passiveUserId!=null){
            MemoJob memoJob=memoJobService.getMemoJobById(id);
            memoJob.setEnabled(0);
            memoJob.setState(3);
            memoJob.setUpdateDate(new Date());
            memoJob.getSchedule().setModifiable(false);//设置成不可修改

            memoJobService.updateMemoJob(memoJob);
            if(memoJob.getSchedule().getFreqType()==1) {
                memoJob.getSchedule().setState(3);
            }
//            删除消息记录
//            List<String> memoJobList=new ArrayList<>();
//            memoJobList.add(memoJob.getId().toString());
//            userSuspendMsgService.deleteUserSuspendMsg(userService.getUserByID(memoJob.getUser()),"memoScheduleService",memoJobList);
            List<MemoJobMsg> memoJobMsgList=memoJobMsgService.getMemoJobMsgByJobId(memoJob.getId(),"1");
            for (MemoJobMsg memoJobMsg:memoJobMsgList){
                memoJobMsgService.updateMemoJobMsg(memoJobMsg,"2",memoJob.getUser());
            }
        }else {

            DmmJob DmmJob = dmmJobService.getDmmJobById(id);
            DmmJob.setEnabled(0);
            DmmJob.setState(3);
            DmmJob.setUpdateDate(new Date());
            DmmJob.getSchedule().setModifiable(false);//设置成不可修改

            dmmJobService.updateDmmJob(DmmJob);
            if (DmmJob.getSchedule().getFreqType() == 1) {
                DmmJob.getSchedule().setState(3);
            }
//            删除消息记录
//            List<String> DmmJobList=new ArrayList<>();
//            DmmJobList.add(DmmJob.getId().toString());
//            userSuspendMsgService.deleteUserSuspendMsg(userService.getUserByID(DmmJob.getUser()),"DmmScheduleService",DmmJobList);
            List<DmmJobMsg> dmmJobMsgList = dmmJobMsgService.getDmmJobMsgByJobId(DmmJob.getId(), "1");
            for (DmmJobMsg dmmJobMsg : dmmJobMsgList) {
                dmmJobMsgService.updateDmmJobMsg(dmmJobMsg, "2", dmmJobMsg.getAccId());
            }
        }
        return new JsonResult(1,1);
    }

    /**
    * <AUTHOR>
    * @Date 2023/3/10
    * 修改日程接口
     * passiveUserId  领地中硬要操作机构内日程，要传对应用户id
    */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/updateDmmSchedule.do")
    public JsonResult updateDmmSchedule(AuthAcc acc, DmmSchedule dmmSchedule, String startDate, String[] imagePaths,Integer passiveUserId) throws Exception {
        Map<String,Object> map=new HashMap<>();
        if(dmmSchedule.getId()==null) {
            return new JsonResult(new MyException("403", "没有传值！"));
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (passiveUserId!=null) {
            User passiveUser=userService.getUserByID(passiveUserId);
            MemoSchedule oldMemoSchedule = memoScheduleService.getMemoScheduleById(dmmSchedule.getId());

            if (!StringUtil.isNullOrEmpty(startDate) && sdf.parse(startDate).getTime() <= new Date().getTime()) {
                //时间已过时，日程不能修改至此时间
                return new JsonResult(new MyException("402", "时间已过时，日程不能修改至此时间！"));
            } else {
                dmmSchedule.setActiveStartDate(StringUtil.isNullOrEmpty(startDate) ? null : sdf.parse(startDate));//首次 执行时间
                if (oldMemoSchedule.getMemoScheduleHistoryHashSet().size() == 0) {
                    memoScheduleHistoryService.saveMemoScheduleHistory(oldMemoSchedule, passiveUser, true,null);//修改前的内容 生成历史记录
                }
                memoScheduleImageService.deleteAllMemoScheduleImage(oldMemoSchedule.getId());//删除原附件
                oldMemoSchedule.getMemoScheduleImageHashSet().clear();
                oldMemoSchedule.setTitle(dmmSchedule.getTitle());
                oldMemoSchedule.setDescription(dmmSchedule.getDescription());
                oldMemoSchedule.setFreqType(dmmSchedule.getFreqType());
                oldMemoSchedule.setActiveStartDate(dmmSchedule.getActiveStartDate());
                oldMemoSchedule.setSpecialInterval(dmmSchedule.getSpecialInterval());
                memoScheduleService.updateMemoSchedule(oldMemoSchedule, passiveUser, imagePaths);//对日程进行修改
                memoScheduleHistoryService.saveMemoScheduleHistory(oldMemoSchedule, passiveUser, false,null);//把修改后的 内容生成历史记录

                map.put("status", 1);//成功
                map.put("dmmSchedule", dmmSchedule);
            }
        }else {
            DmmSchedule oldDmmSchedule = dmmScheduleService.getDmmScheduleById(dmmSchedule.getId());

            if (!StringUtil.isNullOrEmpty(startDate) && sdf.parse(startDate).getTime() <= new Date().getTime()) {
                //时间已过时，日程不能修改至此时间
                return new JsonResult(new MyException("402", "时间已过时，日程不能修改至此时间！"));
            } else {
                dmmSchedule.setActiveStartDate(StringUtil.isNullOrEmpty(startDate) ? null : sdf.parse(startDate));//首次 执行时间
                if (oldDmmSchedule.getDmmScheduleHistoryHashSet().size() == 0) {
                    dmmScheduleHistoryService.saveDmmScheduleHistory(oldDmmSchedule, acc, true);//修改前的内容 生成历史记录
                }
                dmmScheduleImageService.deleteAllDmmScheduleImage(oldDmmSchedule.getId());//删除原附件
                oldDmmSchedule.getDmmScheduleImageHashSet().clear();
                oldDmmSchedule.setTitle(dmmSchedule.getTitle());
                oldDmmSchedule.setDescription(dmmSchedule.getDescription());
                oldDmmSchedule.setFreqType(dmmSchedule.getFreqType());
                oldDmmSchedule.setActiveStartDate(dmmSchedule.getActiveStartDate());
                oldDmmSchedule.setSpecialInterval(dmmSchedule.getSpecialInterval());
                dmmScheduleService.updateDmmSchedule(oldDmmSchedule, acc, imagePaths);//对日程进行修改
                dmmScheduleHistoryService.saveDmmScheduleHistory(oldDmmSchedule, acc, false);//把修改后的 内容生成历史记录

                map.put("status", 1);//成功
                map.put("dmmSchedule", dmmSchedule);
            }
        }
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Date 2023/3/10
    * 获取日程修改记录接口
     * passiveUserId  领地中硬要操作机构内日程，要传对应用户id
    */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/getDmmScheduleHistories.do")
    public JsonResult getDmmScheduleHistories(Integer id,PageInfo pageInfo,Integer passiveUserId){
        if (id==null){
            return new JsonResult(new MyException("403", "没有传值！"));
        }

        Map<String,Object> map=new HashMap<>();

        if (passiveUserId!=null){
            map=memoScheduleHistoryService.getMemoScheduleHistories(id,pageInfo);
        }else {
            List<DmmScheduleHistory> dmmScheduleHistoryList=dmmScheduleHistoryService.getDmmScheduleHistories(id,pageInfo);
            DmmSchedule dmmSchedule=dmmScheduleService.getDmmScheduleById(id);
            List<Map<String,Object>> mapList=new ArrayList<>();
            int number=0;
            if (pageInfo.getCurrentPageNo()>1){
                number=(pageInfo.getCurrentPageNo()-1)*pageInfo.getPageSize();
            }
            for (DmmScheduleHistory dmmScheduleHistory:dmmScheduleHistoryList){
                Map<String,Object> sonMap=new HashMap<>();
                if (pageInfo.getCurrentPageNo()==1&&number==0){
                    sonMap.put("dataState","原始信息");
                }else {
                    sonMap.put("dataState","第"+number+"次修改后");
                }
                sonMap.put("id",dmmScheduleHistory.getId());
                sonMap.put("updateName",dmmScheduleHistory.getCreateName());//人名
                sonMap.put("updateDate",dmmScheduleHistory.getUpdateDate());//修改时间
                mapList.add(sonMap);
                number+=1;
            }
            map.put("number",dmmSchedule.getDmmScheduleHistoryHashSet().size()==0?0:dmmSchedule.getDmmScheduleHistoryHashSet().size()-1);//最新的修改次数
            map.put("updateName",dmmSchedule.getUpdateName());//人名
            map.put("updateDate",dmmSchedule.getUpdateDate());//修改时间
            map.put("dmmScheduleHistoryList",mapList);
            map.put("pageInfo",pageInfo);
        }
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Date  2023/3/10
    * 获取不需要提醒的日程列表
     * passiveUserId  领地中硬要操作机构内日程，要传对应用户id
    */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/getDmmMemoList.do")
    public JsonResult getDmmMemoList(AuthAcc acc,Integer passiveUserId,PageInfo pageInfo){
        Map<String,Object> map=new  HashMap<>();
        if (passiveUserId!=null){
            List<MemoSchedule> memoScheduleList=memoScheduleService.getMemoListByUserId(passiveUserId,pageInfo);
            map.put("memoScheduleList",memoScheduleList);
        }else {
            List<DmmSchedule> dmmScheduleList=dmmScheduleService.getDmmListByUserId(acc.getId(),pageInfo);
            map.put("memoScheduleList",dmmScheduleList);
        }
        map.put("pageInfo",pageInfo);
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Date 2023/3/10
    *  查看日程单一修改记录详情
     *  passiveUserId  领地中硬要操作机构内日程，要传对应用户id
    */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/getDmmScheduleHistoryById.do")
    public JsonResult getDmmScheduleHistoryById(Integer id,Integer passiveUserId){
        if (passiveUserId!=null){
            MemoScheduleHistory m= memoScheduleHistoryService.getMemoScheduleHistoryById(id);
            return new JsonResult(1,m);
        }else {
            DmmScheduleHistory m= dmmScheduleHistoryService.getDmmScheduleHistoryById(id);
            return new JsonResult(1,m);
        }

    }

    /**
    * <AUTHOR>
    * @Date 2023/3/10
    * 根据id，查看日程详情
     * passiveUserId  领地中硬要操作机构内日程，要传对应用户id
    */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/dmmScheduleInfo.do")
    public JsonResult dmmScheduleInfo(Integer id,Integer jobId,Integer msgId,Integer passiveUserId) throws IOException {

        if (passiveUserId!=null){
            MemoSchedule m=memoScheduleService.getMemoScheduleById(id);
            List<MemoScheduleImage>  scheduleImageList=memoScheduleImageService.getMemoScheduleImageListBySid(id);
//        List<MemoScheduleSupplement> scheduleSupplementList=memoScheduleSupplementService.getMemoScheduleSupplementListBySid(id);
            if (msgId!=null&&jobId!=null){
                MemoJob memoJob=memoJobService.getMemoJobById(jobId);
                MemoJobMsg memoJobMsg=memoJobMsgService.getMemoJobMsgById(msgId);
                if (memoJob.getState()==4)
                    memoJobMsgService.updateMemoJobMsg(memoJobMsg,"2",memoJobMsg.getUserId());
            }
            Map<String,Object> map=new HashMap<>();
            map.put("memoSchedule",m);//日程详情
            map.put("scheduleImageList",scheduleImageList);// 附件
//        map.put("scheduleSupplementList",scheduleSupplementList);// 补充记录  及 补充记录附件
            return new JsonResult(1,map);
        }else {
            DmmSchedule m = dmmScheduleService.getDmmScheduleById(id);
            List<DmmScheduleImage> scheduleImageList = dmmScheduleImageService.getDmmScheduleImageListBySid(id);
//        List<DmmScheduleSupplement> scheduleSupplementList=DmmScheduleSupplementService.getDmmScheduleSupplementListBySid(id);
            if (msgId != null && jobId != null) {
                DmmJob dmmJob = dmmJobService.getDmmJobById(jobId);
                DmmJobMsg dmmJobMsg = dmmJobMsgService.getDmmJobMsgById(msgId);
                if (dmmJob.getState() == 4)
                    dmmJobMsgService.updateDmmJobMsg(dmmJobMsg, "2", dmmJobMsg.getAccId());
            }
            Map<String, Object> map = new HashMap<>();
            map.put("dmmSchedule", m);//日程详情
            map.put("scheduleImageList", scheduleImageList);// 附件
//        map.put("scheduleSupplementList",scheduleSupplementList);// 补充记录  及 补充记录附件
            return new JsonResult(1, map);
        }
    }
    
    /**
    * <AUTHOR>
    * @Date 2023/3/10
    * 获取本人 所有提醒记录  历史日程
     * passiveUserId  领地中硬要操作机构内日程，要传对应用户id
    */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/getDmmJobListByUserId.do")
    public JsonResult getDmmJobListByUserId(AuthAcc acc,String keyword,Integer passiveUserId,PageInfo pageInfo){
        Map<String,Object> map=new  HashMap<>();
        if (passiveUserId!=null){
            List<MemoJob> memoJobList=memoJobService.getMemoJobListByUserId(passiveUserId,keyword,pageInfo);
            for (MemoJob memoJob:memoJobList){
                memoJob.setFreqType(memoJob.getSchedule().getFreqType());
            }
            map.put("memoJobList",memoJobList);

        }else {
            List<DmmJob> dmmJobList = dmmJobService.getDmmJobListByUserId(acc.getId(), keyword, pageInfo);
            for (DmmJob dmmJob : dmmJobList) {
                dmmJob.setFreqType(dmmJob.getSchedule().getFreqType());
            }
            map.put("memoJobList", dmmJobList);

        }
        map.put("pageInfo",pageInfo);
        return new JsonResult(1, map);

    }

    /**
    * <AUTHOR>
    * @Date 2023/3/10
    * 无需提醒的日程关键字检索功能
     * passiveUserId  领地中硬要操作机构内日程，要传对应用户id
    */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/searchDmmSchedule.do")
    public JsonResult searchDmmSchedule(AuthAcc acc,String keyword,Integer passiveUserId,PageInfo pageInfo){
        Map<String, Object> map = new HashMap<>();
        if (passiveUserId!=null){
            List<MemoSchedule> mList=memoScheduleService.getMemoScheduleListByKeyword(passiveUserId,keyword,pageInfo);
            map.put("memoScheduleList",mList);
        }else {
            List<DmmSchedule> mList = dmmScheduleService.getDmmScheduleListByKeyword(acc.getId(), keyword, pageInfo);
            map.put("memoScheduleList", mList);
        }
        map.put("pageInfo", pageInfo);
        return new JsonResult(1, map);

    }

    /**
    * <AUTHOR>
    * @Date 2023/3/10
    * 对提醒新增补充记录
     * passiveUserId  领地中硬要操作机构内日程，要传对应用户id
    */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/addDmmJobSupplement.do")
    public JsonResult addDmmJobSupplement(AuthAcc acc,Integer dmmJobId,String description,String[] imagePaths,Integer passiveUserId){
        Boolean b=false;
        if (passiveUserId!=null){
            User passiveUser=userService.getUserByID(passiveUserId);
            b=memoScheduleSupplementService.addMemoScheduleSupplement(passiveUser,dmmJobId,description,imagePaths);
        }else {
            b=dmmScheduleSupplementService.addDmmScheduleSupplement(acc,dmmJobId,description,imagePaths);
        }
        if (b){
            return new JsonResult(1,1);//成功
        }else {
            return new JsonResult(new MyException("403", "没有传值！"));
        }
    }

    /**
    * <AUTHOR>
    * @Date 2023/3/10
    * 删除补充记录
     * passiveUserId  领地中硬要操作机构内日程，要传对应用户id
    */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/deleteDmmJobSupplement.do")
    public JsonResult deleteDmmJobSupplement(AuthAcc acc,Integer supplementId,Integer passiveUserId){
        if (supplementId==null){
            return new JsonResult(new MyException("403", "没有传值！"));
        }
        if (passiveUserId!=null){
            MemoScheduleSupplement memoScheduleSupplement=memoScheduleSupplementService.getMemoScheduleSupplementById(supplementId);
            User user=userService.getUserByID(passiveUserId);
            memoScheduleService.delMemoScheduleSupplementImagFileUsing(memoScheduleSupplement.getMemoScheduleSupplementImageHashSet(),user); // 删除补充记录的附件 引用
            memoScheduleSupplementService.deleteMemoScheduleSupplement(supplementId);// 删除补充记录（对附件有集连删除）
        }else {
            DmmScheduleSupplement dmmScheduleSupplement=dmmScheduleSupplementService.getDmmScheduleSupplementById(supplementId);
            dmmScheduleService.delDmmScheduleSupplementImagFileUsing(dmmScheduleSupplement.getDmmScheduleSupplementImageHashSet(),acc); // 删除补充记录的附件 引用
            dmmScheduleSupplementService.deleteDmmScheduleSupplement(supplementId);// 删除补充记录（对附件有集连删除）
        }

        return new JsonResult(1,1);//成功
    }



    /**
    * <AUTHOR>
    * @Date 2023/3/10
    * 提醒记录（历史记录）筛选 检索
     * passiveUserId  领地中硬要操作机构内日程，要传对应用户id
    */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/searchDmmJob.do")
    public  JsonResult searchDmmJob(AuthAcc acc,Integer passiveUserId,String keyword,PageInfo pageInfo){
        Map<String,Object> map=new  HashMap<>();
        map.put("pageInfo",pageInfo);
        if (passiveUserId!=null){
            List<MemoJob> memoJobList=memoJobService.getMemoJobListByUserId(passiveUserId,keyword,pageInfo);
            map.put("memoJobList",memoJobList);
        }else {
            List<DmmJob> dmmJobList=dmmJobService.getDmmJobListByUserId(acc.getId(),keyword,pageInfo);
            map.put("memoJobList",dmmJobList);
        }
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Date 2023/3/10
    * 提醒记录详情
     * passiveUserId  领地中硬要操作机构内日程，要传对应用户id
    */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/getDmmJobById.do")
    public JsonResult getDmmJobById(Integer dmmJobId,Integer passiveUserId){
        Map<String,Object> map=new HashMap<>();
        if (passiveUserId!=null){
            MemoJob memoJob=memoJobService.getMemoJobById(dmmJobId);
            MemoSchedule m=memoJob.getSchedule();
            memoJob.setMemoScheduleImageHashSet(m.getMemoScheduleImageHashSet());
            memoJob.setFreqType(m.getFreqType());
            memoJob.setActiveStartDate(m.getActiveStartDate());
            List<MemoScheduleImage>  scheduleImageList=memoScheduleImageService.getMemoScheduleImageListBySid(m.getId());//日程附件
            List<MemoScheduleSupplement> scheduleSupplementList=memoScheduleSupplementService.getMemoScheduleSupplementListByJobId(dmmJobId);
            map.put("memoSchedule",memoJob);//日程详情
            map.put("scheduleImageList",scheduleImageList);// 附件
            map.put("scheduleSupplementList",scheduleSupplementList);// 补充记录  及 补充记录附件

        }else {
            DmmJob dmmJob=dmmJobService.getDmmJobById(dmmJobId);
            DmmSchedule m=dmmJob.getSchedule();
            dmmJob.setDmmScheduleImageHashSet(m.getDmmScheduleImageHashSet());
            dmmJob.setFreqType(m.getFreqType());
            dmmJob.setActiveStartDate(m.getActiveStartDate());
            List<DmmScheduleImage>  scheduleImageList=dmmScheduleImageService.getDmmScheduleImageListBySid(m.getId());//日程附件
            List<DmmScheduleSupplement> scheduleSupplementList=dmmScheduleSupplementService.getDmmScheduleSupplementListByJobId(dmmJobId);
            map.put("memoSchedule",dmmJob);//日程详情
            map.put("scheduleImageList",scheduleImageList);// 附件
            map.put("scheduleSupplementList",scheduleSupplementList);// 补充记录  及 补充记录附件
        }
        return new JsonResult(1,map);
    }

    /**
    * <AUTHOR>
    * @Date 2023/3/10
    * 待处理的提醒，初始数据列表
     * passiveUserId  领地中硬要操作机构内日程，要传对应用户id
    */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/getDmmJobMsgList.do")
    public JsonResult  getDmmJobMsgList(AuthAcc acc,Integer passiveUserId){
        if (passiveUserId!=null) {
            User passiveUser=userService.getUserByID(passiveUserId);
            List<MemoJobMsg> memoJobMsgList=memoJobMsgService.getMemoJobMsgBuUserId(passiveUser,"1");
            return new JsonResult(1,memoJobMsgList);
        }else {
            List<DmmJobMsg> dmmJobMsgList = dmmJobMsgService.getDmmJobMsgBuUserId(acc.getId(), "1");
            return new JsonResult(1, dmmJobMsgList);
        }
    }


    /**
     * <AUTHOR>
     * @Date 2023/1/13 10:32
     * 领地 日程/备忘可选保存位置列表
     */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/getDmmSaveLocations.do")
    public JsonResult getDmmSaveLocations(AuthAcc acc){
        List<UserLoginDto> list = userService.getUserList(acc.getId(), null);
        return new JsonResult(1,list);
    }


    /**
     * <AUTHOR>
     * @Date 2023/1/13 11:06
     * 领地选择保存位置 获取日程可分享的人员
     */
    @AuthPassport(manor = true)
    @ResponseBody
    @RequestMapping("/getShareableOrgUsers.do")
    public JsonResult  getShareableUsers(AuthAcc acc,Integer scheduleId,Integer oid) throws Exception {
        if (oid==null){
            throw new Exception("参数oid不能为空！");
        }
//        if (acc==null){
//            throw new Exception("请先登录！");
//        }
//        DmmSchedule DmmSchedule=DmmScheduleService.getDmmScheduleById(scheduleId);
        List<User> userList=userService.getOrgUsersAndSee(oid); //去除身份,全部人员,加跨机构可见人员
        User orgUser= userService.getMasterUserByOidAndAcc(oid,acc);// 日程的创建人
        if (scheduleId!=null) {
            List<Integer> remUserIds = memoScheduleShareService.getRecipientIdsByScheduleId(scheduleId);
            List<User> remUsers = new ArrayList<>();
            remUsers.add(orgUser);//把操作人自己 去掉
            for (User user1 : userList) {
                if (remUserIds.contains(user1.getUserID())) {
                    remUsers.add(user1);
                }
            }
            userList.removeAll(remUsers); // 去除不用分享的人员
        }else {
            userList.remove(orgUser);
        }
        return new JsonResult(1,userList);
    }



    @ResponseBody
    @RequestMapping("/ceshi.do")
    public void  ceshi(){
        dmmScheduleService.addDmmScheduleRemind(7);
    }
}
