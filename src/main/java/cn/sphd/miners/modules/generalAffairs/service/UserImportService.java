package cn.sphd.miners.modules.generalAffairs.service;


import cn.sphd.miners.modules.auth.dto.AuthInfoDto;
import cn.sphd.miners.modules.generalAffairs.entity.ReqUserObject;
import cn.sphd.miners.modules.generalAffairs.entity.SysUserLock;
import cn.sphd.miners.modules.generalAffairs.entity.UserImport;
import cn.sphd.miners.modules.generalAffairs.entity.UserLock;
import cn.sphd.miners.modules.material.entity.MtBase;
import cn.sphd.miners.modules.material.entity.MtCategory;
import cn.sphd.miners.modules.material.entity.ReqMtObject;
import cn.sphd.miners.modules.material.entity.RespStatus;
import cn.sphd.miners.modules.system.entity.User;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @ClassName UserImportService
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/9 11:20
 * @Version 1.0
 */
public interface UserImportService {
    ReqUserObject saveImportUser(ReqUserObject reqUserObject,User user, List<User> userList,Integer oid );
    ReqUserObject unfinishedImportUser(Integer oid);
    List<User> getNotDirectLowerGrade(Integer oid);//获取去除某人直系下级的用户列表
    List<UserImport> getOptionalImportUser(Integer oid,Integer id);
    int deleteImportUser(int id);
    int updateImportUser(UserImport userImport);
    int updateImportUserMobile(UserImport userImport);
    int giveUpImportUser(Integer oid);
    List<User> completeImportUser(HttpServletRequest request, AuthInfoDto authInfo,User loginUser,int org);
    List<User> addSalaryUserForImportUser(int org);
    List<User> getAllUsersByOrganization(Integer orgId, User user);
    List<User> getAllLockUsersByOrganization(Integer org);
    List<SysUserLock> getUserLockListByOidMobile(Integer org, String mobile);
    UserImport addUserImport(UserImport user,int importId);
    int getImportId(int org);
    Integer getNoLeaderUserCountByOid(Integer org);
    void updateImportLeader(Integer id);
    Integer getNoManageUserCountByOid(Integer org);
    UserImport getUserImportByID(int importId);
    void  updateUserForE();

    void deleteUserImportsByOid(Integer oid); //根据oid 删除 导入的人员   1.139中枢管控2  20201120  lixu 加

    Integer getImportNumberByOid(Integer oid);//  查询导入 未完成的人数  1.139中枢管控2 202012/17  lixu加
}
