<template>
  <div id="potentialApplyList" class="potential" v-loading.fullscreen.lock="loading">
    <TY_NavTop title="潜在客户受理" :isBackHome="isBackHome" isSearch="true" @toggleList="screen"></TY_NavTop>
    <TY_Tabs :value="tabValue">
      <TY_TabPane label="待处理" name="1" closable='false' :msgCount="toHandleCount">
        <div class="panel-title">如下潜在客户有待您确定销售负责人</div>
        <div class="ui-cells">
          <a class="ui-col" v-for="(item,index) in untreatList" :key="index" @click="jumpDetail(item.id, '1')">
            <div class="ui-col__bd col-flex">
              <div class="ui-col_con">
                <div>{{item.fullName}}</div>
              </div>
              <div class="ui-col__flag">
              </div>
            </div>
            <div class="ui-col__ft">
              <div class="createDetail">录入者 {{item.createName}} {{item.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</div>
            </div>
          </a>
        </div>
      </TY_TabPane>
      <TY_TabPane label="待正式合作" name="2" closable='false'>
        <div class="panel-title">以下潜在客户的正式合作事宜有待确认推进！</div>
        <div class="ui-cells">
          <a class="ui-col" v-for="(item,index) in finishList" :key="index" @click="jumpDetail(item.id, '2')">
            <div class="ui-col__bd col-flex">
              <div class="ui-col_con">
                <div>{{item.fullName}}</div>
              </div>
              <div class="ui-col__flag">
              </div>
            </div>
            <div class="ui-col__ft">
              <div class="createDetail">录入者 {{item.createName}} {{item.createDate | formatDate('yyyy/MM/dd hh:mm:ss')}}</div>
            </div>
          </a>
        </div>
      </TY_TabPane>
    </TY_Tabs>
  </div>
</template>
<style>

</style>
<script>
import { formatDate } from '@/js/common'
export default {
  name: 'potentialCustomerApproval',
  filters: {
    formatDate
  },
  data () {
    return {
      loading: true,
      tabValue: '1',
      untreatList: [],
      finishList: [],
      listenersUid: []
    }
  },
  created () {
    let _this = this
    let oid = this.sphdSocket.user.oid
    let userId = this.sphdSocket.user.userID
    let session = this.sphdSocket.sessionid
    _this.tabValue = this.$store.getters.getSaleManageNav
    this.listenersUid = [
      this.sphdSocket.subscribe('potentialAcceptList', function (data) {
        let resData = JSON.parse(data)
        _this.untreatList = resData.list
        _this.loading = false
      }),
      this.sphdSocket.subscribe('waitCooperationList', function (data) {
        let resData = JSON.parse(data)
        _this.finishList = resData.list
      }),
      this.sphdSocket.subscribe('potentialAcceptList', function (data) {
        let reqData = JSON.parse(data)
        let update = reqData.operate
        let newDate = {}
        if (update > 0) { // 增加一条
          newDate = reqData.customer
          _this.untreatList.push(newDate)
        } else if (update < 0) { // 减少一条
          let deletId = reqData.customer.id
          let index = _this.untreatList.findIndex(item => Number(deletId) === Number(item.id))
          if (index !== undefined) {
            _this.untreatList.splice(index, 1)
          }
        }
      }, null, 'user'),
      this.sphdSocket.subscribe('waitCooperationList', function (data) {
        let reqData = JSON.parse(data)
        let update = reqData.operate
        let newDate = {}
        if (update > 0) { // 增加一条
          newDate = reqData.customer
          _this.finishList.push(newDate)
        } else if (update < 0) { // 减少一条
          let deletId = reqData.customer.id
          let indx = _this.finishList.findIndex(item => Number(deletId) === Number(item.id))
          if (indx !== undefined) {
            _this.finishList.splice(indx, 1)
          }
        }
      }, null, 'user')
    ]
    this.sphdSocket.send('potentialAcceptList', { 'oid': oid, userId: userId, session: session })
    this.sphdSocket.send('waitCooperationList', { 'oid': oid, userId: userId, session: session })
  },
  destroyed: function () {
    let _this = this
    this.listenersUid.forEach(function (item) {
      _this.sphdSocket.unsubscribe(item)
    })
  },
  methods: {
    jumpDetail: function (id, tab) {
      this.$store.dispatch('setSaleManageNav', tab)
      this.$router.push({
        path: `/potentialApproveDetail/${id}/${tab}`
      })
    },
    screen: function () {
      this.$router.push({
        path: `/applyPotentialQuery/${2}`
      })
    }
  }
}
</script>
