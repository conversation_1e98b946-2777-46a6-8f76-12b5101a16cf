package cn.sphd.miners.modules.equipment.entity;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 装备管理_分类
 * 20230209 1.247装备器具新增对象 t_equ_category
 *
 * <AUTHOR>
 * @date 2023-02-10
 */
public class TEquCategory {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    private Long id;

    /**
     * 机构ID
     */

    private Long org;

    /**
     * 名称
     */

    private String name;

    /**
     * 内容/场合
     */

    private String content;

    /**
     * 父结点ID
     */

    private Long parent;

    /**
     * 是否系统自带:1-系统类别,0-自定义类别
     */

    private Integer isSystem;

    /**
     * 是否可修改 1-可修改 0 -不可修改
     */

    private Integer revisable;

    /**
     * 状态:0-不启用,1-启用
     */

    private Long enabled;

    /**
     * 启停用时间
     */

    private Date enabledTime;

    /**
     * 儿子的个数
     */

    private Long childrens;

    /**
     * 后代个数
     */

    private String descendants;

    /**
     * 路径,存各级祖先节点,以逗号分隔
     */

    private String path;

    /**
     * 同级排序
     */

    private Long orders;

    /**
     * 装备器具个数
     */

    private Long equipmentCount;

    /**
     * 描述
     */

    private String memo;

    /**
     * 创建人id
     */

    private Long creator;

    /**
     * 创建人
     */

    private String createName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createDate;

    /**
     * 修改人id
     */

    private Long updator;

    /**
     * 修改人
     */

    private String updateName;

    /**
     * 修改时间
     */

    private Date updateDate;

    /**
     * 操作:1-增,2-删,3-改,4-启用,5-停用
     */

    private Long operation;

    /**
     * 修改前记录ID
     */

    private Long previousId;

    /**
     * 版本号,每次修改+1
     */

    private Long versionNo;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setOrg(Long org) {
        this.org = org;
    }

    public Long getOrg() {
        return org;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setParent(Long parent) {
        this.parent = parent;
    }

    public Long getParent() {
        return parent;
    }

    public void setIsSystem(Integer isSystem) {
        this.isSystem = isSystem;
    }

    public Integer getIsSystem() {
        return isSystem;
    }

    public void setRevisable(Integer revisable) {
        this.revisable = revisable;
    }

    public Integer getRevisable() {
        return revisable;
    }

    public void setEnabled(Long enabled) {
        this.enabled = enabled;
    }

    public Long getEnabled() {
        return enabled;
    }

    public void setEnabledTime(Date enabledTime) {
        this.enabledTime = enabledTime;
    }

    public Date getEnabledTime() {
        return enabledTime;
    }

    public void setChildrens(Long childrens) {
        this.childrens = childrens;
    }

    public Long getChildrens() {
        return childrens;
    }

    public String getDescendants() {
        return descendants;
    }

    public void setDescendants(String descendants) {
        this.descendants = descendants;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getPath() {
        return path;
    }

    public void setOrders(Long orders) {
        this.orders = orders;
    }

    public Long getOrders() {
        return orders;
    }

    public void setEquipmentCount(Long equipmentCount) {
        this.equipmentCount = equipmentCount;
    }

    public Long getEquipmentCount() {
        return equipmentCount;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getMemo() {
        return memo;
    }

    public void setCreator(Long creator) {
        this.creator = creator;
    }

    public Long getCreator() {
        return creator;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

    public String getCreateName() {
        return createName;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setUpdator(Long updator) {
        this.updator = updator;
    }

    public Long getUpdator() {
        return updator;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public String getUpdateName() {
        return updateName;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setOperation(Long operation) {
        this.operation = operation;
    }

    public Long getOperation() {
        return operation;
    }

    public void setPreviousId(Long previousId) {
        this.previousId = previousId;
    }

    public Long getPreviousId() {
        return previousId;
    }

    public void setVersionNo(Long versionNo) {
        this.versionNo = versionNo;
    }

    public Long getVersionNo() {
        return versionNo;
    }
}
