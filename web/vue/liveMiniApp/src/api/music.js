
import { Request } from "@/utils/request";

var request = Request

/*
// 获取歌曲时长
返回值：
    1 state 0-媒体文件读取错误，1-成功
    2 durationTime 显示给用户看的时长 格式是“xx分xx秒” 直接显示到时长那里就行
    3 duration 用于后台存储的真正时长 是一个毫秒值
    4 filePath 文件路径
*/
export function getAudioFileDuratuin() {
    return request({
        url: '/lyric/getAudioFileDuratuin.do',
        method: 'post',
    })
}



/*
// 主播新增是否需要加字幕歌曲/主播发布任务歌曲任务
传参：
    1 tgCode  第三方大厂(1-weChat,2-byteDance) 这里歌词固定传2
    2 appId 第三方平台appid 你看看好获取吗要是不好弄我在改成后台获取
    3 name 歌曲名称
    4 type 类型:1-歌曲,2-戏曲 戏曲的先不做
    5 captionRequired 是否需要加字幕 0-否 1-是 传0的时候5以下就都不用传了
    6 trackDuration 歌曲时长 上个接口返回的duration字段
    7 bgmDuration 伴奏时长 上个接口返回的duration字段
    8 publishType 发布方式:1-自行上传,2-发布任务,团队成员上传
    9 rhythmOption 节奏选项:1-原唱,2-伴奏  这个值可能有点问题我换了新东西 看看能否接收到
    10 inllustration 说明 publishType=2时非空就传
    11 firstSentence 第一句 publishType=2时非空就传
    12 lastSentence 最后一句 publishType=2时非空就传
    13 approveStatus 审批状态 1-申请提交 2-审合通过 3-申请撤销 captionRequired=0时传2；captionRequired=2且publishType=1时传2；captionRequired=2且publishType=2时传1
    14 versionNo 版本号 传0
    15 rhythm 节奏歌词文本(含时间标签)
    16 lyrics 歌词文本 从剪贴板获取的歌词文本
    17 user string 一个user的接送字符串 应该是这样 [{"user":"1"}，{"user":"2"}] captionRequired=2且publishType=2才传这个值
    18 isSelected 是否被选择 1-被选择 0-未选择 这里固定传1

返回值：
1 song 歌曲信息
    1.1 id
    1.2 name String 歌曲名称
    1.3 captionRequired Byte 是否需要加字幕 0-否 1-是 传0的时候5以下就都不用传了
    1.4 durationTime 显示给用户看的时长 格式是“xx分xx秒” 直接显示就行
*/
export function insertSongs(data) {
    return request({
        url: '/lyric/insertSongs.do',
        method: 'post',
        data
    })
}


/*
获取锁
用途：
为了防止角标出现混录，所以在成员上传节奏或主播上传节奏和撤销任务时要先获取一个锁，当锁不同时可能操作已经完成了，此时就不需要再去有减角标等操作。
传参：
  1 id Long 歌曲的id
返回值：
   lock 锁的字符串，确定时回传给后台
*/
export function getSongLock(data) {
    return request({
        url: '/lyric/getSongLock.do',
        method: 'post',
        data
    })
}

/*
主播或成员上传节奏
传参：
    1 type 类型 1-主播直接上传节奏 2-成员上传节奏
    2 id 歌曲的id
    3 rhythmOption 节奏选项:1-原唱,2-伴奏
    4 rhythm 节奏歌词文本(含时间标签)
    5 lyrics 歌词文本 从剪贴板获取的歌词文本
    6 approveStatus 2-通过审核
    7 isSelected 是否被选择 1-被选择 0-未选择 type=1时传1，type=2时传0
返回值：
    1 state Integer 0-节奏已选完 1-成功 2-不要重复处理
*/
export function insertRhythm(data) {
    return request({
        url: '/lyric/insertRhythm.do',
        method: 'post',
        data
    })
}

/* 主播选择节奏
传参： rhythmId 节奏id
返回值：1 state 1-成功 2-歌词任务已经撤销
* */
export function selectionRhythm(data) {
    return request({
        url: '/lyric/selectionRhythm.do',
        method: 'post',
        data
    })
}

/* 歌词任务/歌词任务处理/歌词任务审批
传参：
    1 type 1-主播 2-成员
    2 approveStatus 状态 1是待上传/待确认 2是已上传/未选用
 // 1 主播 任务/申请（X）-歌词任务 type=1 approveStatus=1
    // 2 主播 处理/审批（X）-歌词任务处理-待上传（X） type=1 approveStatus=1
    // 3 成员 处理/审批（X）-歌词任务处理-待上传（X） type=2 approveStatus=1
    // 4 主播 处理/审批（X）-歌词任务审批-待确认（X）type=1 approveStatus=1
    // 5 主播 处理/审批（X）-歌词任务审批-未选用（X） type=1 approveStatus=2
返回值：
    1 listSongs
      1.1 id
      1.2 name String 歌曲名称
      1.3 durationTime 显示给用户看的时长 格式是“xx分xx秒” 直接显示就行
* */
export function getLyricTask(data) {
    return request({
        url: '/lyric/getLyricTask.do',
        method: 'post',
        data
    })
}


/* 歌曲管理
返回值：
  listSongs
     id
     name String 歌曲名称
     captionRequired Byte 是否需要加字幕 0-否 1-是 传0的时候5以下就都不用传了
     durationTime 显示给用户看的时长 格式是“xx分xx秒” 直接显示就行
* */
export function songManagement(data) {
    return request({
        url: '/lyric/songManagement.do',
        method: 'post',
        data
    })
}


/* 歌词任务处理详情/歌曲详情(用于歌曲管理中获取详情)
传参：
    1 id 歌曲id
    2 type 1-主播 2- 成员
返回值：
1 song
  1.1 id
  1.2 name 歌曲名字
  1.3 durationTime 时长 "3分53秒"
  1.4 inllustration  说明
  1.5 firstSentence 第一句
  1.6 lastSentence 最后一句
  1.7 createName 任务发布人
  1.8 createTime 任务发布时间
  1.9 originalPath 原唱存储路径
  1.10 bgmPath 伴唱存储路径
2 cmwRhythm 节奏 处理中就是此人上传节奏 歌曲管理中就是被选择的节奏
  2.1 createTime 节奏上传时间
  2.2 rhythmOption 节奏选项:1-原唱,2-伴奏
  2.3 rhythm 节奏歌词文本(含时间标签)
* */
export function songMessage(data) {
    return request({
        url: '/lyric/songMessage.do',
        method: 'post',
        data
    })
}

export function getLyriTaskApplyMes(data) {
    return request({
        url: '/lyric/getLyriTaskApplyMes.do',
        method: 'post',
        data
    })
}


/* 歌词任务审批详情
* 入参说明：
    id 歌曲id
*返回值：
1 song
  1.1 id
  1.2 name 歌曲名字
  1.3 durationTime 时长 "3分53秒"
  1.4 inllustration  说明
  1.5 firstSentence 第一句
  1.6 lastSentence 最后一句
  1.7 createName 任务发布人
  1.8 createTime 任务发布时间
  1.9 originalPath 原唱存储路径
  1.10 bgmPath 伴唱存储路径
2 listRhythm 节奏 节奏个数 自己获取一下吧 我就没有返回 这里有几条就是几份节奏
  2.1 createTime 节奏上传时间
  2.2 rhythmOption 节奏选项:1-原唱,2-伴奏
  2.3 rhythm 节奏歌词文本(含时间标签)
  2.4 createName 节奏上传人
*
* */

export function lyriTaskApproveMes(data) {
    return request({
        url: '/lyric/getLyriTaskApproveMes.do',
        method: 'post',
        data
    })
}


export function getAllRhythm(data) {
    return request({
        url: '/lyric/getAllRhythm.do',
        method: 'post',
        data
    })
}

/* 歌词任务详情
入参说明：
    id 歌曲id
返回值：
1 song
    1.1 id
    1.2 name 歌曲名字
    1.3 durationTime 时长 "3分53秒"
    1.4 inllustration  说明
    1.5 firstSentence 第一句
    1.6 lastSentence 最后一句
    1.7 createName 任务发布人
    1.8 createTime 任务发布时间
    1.9 taskListAllUser 接收对象 “共3人,员工1,员工2,员工3”
*/
export function getLyriTaskMes(data) {
    return request({
        url: '/lyric/getLyriTaskMes.do',
        method: 'post',
        data
    })
}

/*

12.主播撤销任务
入参说明：
    id 歌曲id
返回值：
    state 1-成功 2-已上传节奏不能撤回
*/
export function antorCancelTask(data) {
    return request({
        url: '/lyric/antorCancelTask.do',
        method: 'post',
        data
    })
}


/* 歌词任务处理-以上传
入参说明：
year 年 例“year=2022” 不传就默认是当前年
返回值：
1 timeRecord
    1.1 time 有两种样式 一种是“2023年5月” 一种是“2023年”
    1.2 num 次数
*/
export function getUploadedLyricByYear(data) {
    return request({
        url: '/lyric/getUploadedLyricByYear.do',
        method: 'post',
        data
    })
}

/* 歌词任务处理-以上传-点击月获取具体上传记录
入参说明：
month 年月 例“month=2022-05” 必须得传点击哪个月就要传对应的年月
返回值：
1 listRhythm
    1.1 id
    1.2 songName 歌名
    1.3 createTime 节奏上传时间
*/

export function getUploadedLyricByMonth(data) {
    return request({
        url: '/lyric/getUploadedLyricByMonth.do',
        method: 'post',
        data
    })
}

/*

*/

export function getHandleMsgNumber(data) {
    return request({
        url: '/sys/getAllMenu.do',
        method: 'post',
        data
    })
}

/* 修改歌曲基本信息
入参说明：
    1 id Long 歌曲id
    2 name String 歌曲名字
    3 inllustration String 说明
    4 firstSentence String 第一句
    5 lastSentence String 最后一句
    6 selections String 选段名
    7 genres String 流派名
返回值：
    1 state 1-修改成功
    2 song 歌曲信息 （里面所有值同接口7的同名关键字一样，不详细写了，具体看接口1的同名关键字）
*/
export function updateSongMessage(data) {
    return request({
        url: '/lyric/updateSongMessage.do',
        method: 'post',
        data
    })
}

/* 9.修改原唱或者伴奏
入参说明：
    1 id Long 歌曲id
    2 originalPath String 原唱存储路径
    3 trackDuration String 歌曲时长 上个接口返回的duration字段 这里存时间的毫秒值
    4 bgmPath String 伴奏存储路径
    5 bgmDuration String 伴奏时长 上个接口返回的duration字段 这里存时间的毫秒值
    6 module String 上传使用的关键字 这里固定传“字幕精灵”
返回值：
    1 state 1-修改成功
    2 song 歌曲信息 （里面所有值同接口7的同名关键字一样，不详细写了，具体看接口1的同名关键字）

*/
export function updateSongBgmOrOriginal(data) {
    return request({
        url: '/lyric/updateSongBgmOrOriginal.do',
        method: 'post',
        data
    })
}




