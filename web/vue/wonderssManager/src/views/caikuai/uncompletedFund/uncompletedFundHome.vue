<template>
  <!-- 未完结款项主页-->
  <div class="box">
    <div class="head">
      <PageHeader :title="tit" :showClose=true @mycloseFun="handleCustomClose" :showBack=true>
      </PageHeader>
    </div>
    <div class="margin_top_16">
      <div class="ty-fff spaceBetween">
        <div>
          <p>尚未核销的采购预付款</p>
          <p style="color:#337ab7;">注：该款项需收回或报销</p>
        </div>
        <div class="ic" @click="emi()">
          <el-icon>
            <arrow-right/>
          </el-icon>
        </div>
      </div>
      <div class="ty-fff spaceBetween margin_top_16">
        <div>
          <p>采购报销时，多付出去的款项</p>
          <p style="color:#337ab7;">注：该款项需从供应商处收回</p>
        </div>
        <div class="ic" @click="emi()">
          <el-icon>
            <arrow-right/>
          </el-icon>
        </div>
      </div>
      <div class="ty-fff spaceBetween margin_top_16">
        <div>
          <p>客户回款中，多收的款项</p>
          <p style="color:#337ab7;">注：该款项需退回给客户</p>
        </div>
        <div class="ic" @click="emi()">
          <el-icon>
            <arrow-right/>
          </el-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {defineComponent} from "vue";

export default defineComponent({
  name: "uncompletedFundHome",
  data() {
    return {
      tit: "未完结的款项"
    }
  },
  methods: {
    emi() {
      this.$message({
        message: '开发中，敬请期待！',
        type: 'warning',
        offset: 200 // 设置偏移量为200px
      });
    },
    handleCustomClose() {
      this.$router.push("/caikuai")
    }
  }
})
</script>

<style scoped lang="scss">
@import "src/style/loans.scss";

.ty-fff {
  padding: 15px;
  line-height: 22px;
}

p {
  font-size: 14px;
}
</style>
