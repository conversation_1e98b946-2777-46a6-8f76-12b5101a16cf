package cn.sphd.miners.modules.material.dao.impl;

import cn.sphd.miners.common.persistence.BaseDao;
import cn.sphd.miners.common.utils.BeanUtils;
import cn.sphd.miners.modules.material.dao.SrmSupplierPostHistoryDao;
import cn.sphd.miners.modules.material.entity.SrmSupplierPost;
import cn.sphd.miners.modules.material.entity.SrmSupplierPostHistory;
import org.springframework.stereotype.Repository;
import java.io.Serializable;

@Repository
public class SrmSupplierPostHistoryDaoImpl extends BaseDao<SrmSupplierPostHistory, Serializable> implements SrmSupplierPostHistoryDao {
    @Override
    public SrmSupplierPostHistory insert(SrmSupplierPost srmSupplierPost) {
        SrmSupplierPostHistory history=new SrmSupplierPostHistory();
        BeanUtils.copyProperties(srmSupplierPost,history);
        history.setId(null);
        history.setSupplierPost(srmSupplierPost.getId());
        save(history);
        return history;
    }
}
