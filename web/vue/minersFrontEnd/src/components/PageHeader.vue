<template>
  <div class="pageHead">
    <div v-if="showBack" class="back" @click="backBtn()">
      <span class="fa fa-chevron-left"></span>
    </div>
    <div v-if="showClose" class="close" @click="closeBtn()">
      <span class="fa fa-close"></span>

    </div>
    <div class="title">{{ title }}</div>
    <div class="btns"><slot> </slot></div>
  </div>
</template>
<script>
import { defineComponent } from "vue";
import { useRouter } from "vue-router";

export default defineComponent({
  name: "PageHeader",
  props: {
    title: {
      type: String,
      default: "提示",
    },
    showBack: {
      type: Boolean,
      default: false,
    },
    showClose: {
      type: Boolean,
      default: false,
    },
    runMyBackFun: {
      type: Boolean,
      default: false,
    },
  },
  emits: {
    myBackFun: null,
  },
  setup(props, context) {
    // console.log("打印 router");
    // console.log(context.$router);
    const router = useRouter();

    const backBtn = () => {
      console.log("runMyBackFun", props.runMyBackFun);
      if (!props.runMyBackFun) {
        router.back();
        console.log("normal back");
      } else {
        console.log("reback");
        context.emit("myBackFun");
      }
    };
    const closeBtn = () => {
      console.log("closeBtn", props.runMyBackFun);
      context.emit("mycloseFun");
    };


    return {
      backBtn,
      closeBtn,
    };
  },
});
</script>
<style lang="scss" scoped>

.pageHead {
  height: 44px;
  background-color: #53b5a8;
  line-height: 44px;
  color: #fff;
  font-size: 16px;
  position: relative;
  text-align: center;
  
}
.btns {
  width: 50px;
  position: absolute;
  right: 0;
  top:0;
}
.back, .close{
  width: 40px;
  position: absolute;
}
.back {
  left: 0;
}
.close {
  left:40px;
}
.title {
  text-align: center;
}
</style>
