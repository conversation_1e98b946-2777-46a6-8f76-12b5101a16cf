<template>
  <!-- 小程序》我的 账号信息 -->
  <div class="userMsg">
    <Pageheader title="用户信息" :showBack="true"/>
    <div class="live-panel live-tr martop30" @click="goPage('accEditLog')">
      <span>账号</span>
      <span class="live-right" >{{ user.mobile }}
      <el-icon>
          <arrow-right-bold color="#53B5A8" class="houseSty8" />
        </el-icon>
      </span>
    </div>
    <div class="live-panel live-tr" @click="goPage('userOfficeList')">
      <span>可登录的工作室</span>
      <span class="live-right" >{{ officeList.length }}个
      <el-icon>
          <arrow-right-bold color="#53B5A8" class="houseSty8" />
        </el-icon>
      </span>
    </div>
    <div class="live-panel live-tr">
      <span>创建</span>
      <span class="live-right" >{{ userTime }}</span>
    </div>

  </div>
</template>
<style lang="scss" scoped>
.houseSty8{
  position: relative;
  top:3px;
}
.martop30{ margin-top:20px; }
</style>
<script setup>
import { useRouter  } from 'vue-router';
import JSONBig from 'json-bigint'
import auth from "@/sys/auth.js";

const router = useRouter();

let jsonData = JSONBig.parse(localStorage.getItem('jsonData'))
console.log(' let jsonData = ', jsonData)
const org = auth.getOrg()
const user = auth.getAcc()
console.log('====user =====', user)
const officeList = jsonData.officeList
const userTime = (new Date(user.createTime)).format("yyyy年M月d日 hh:mm:ss")

const goPage = (url) => {
  router.push({ name: url })
}
</script>