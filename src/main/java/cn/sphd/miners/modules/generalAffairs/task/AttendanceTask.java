package cn.sphd.miners.modules.generalAffairs.task;

import cn.sphd.miners.common.utils.GetLocalIPUtils;
import cn.sphd.miners.modules.generalAffairs.service.WorkAttendanceService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.sql.DataSource;

/**
 * 考勤设置定时任务逻辑
 * Created by Administrator on 2018/5/30.
 */
public class AttendanceTask {
    @Autowired
    WorkAttendanceService workAttendanceService;
    @Autowired
    DataSource dataSource;

    public void runAttendanceTask() {
        //wyu:获取分布式锁
        if (GetLocalIPUtils.isServer(dataSource)) {//只在服务器端执行
            String lockKey;
            if ((lockKey = workAttendanceService.getDlmtLock(10)) != null) {//加锁10分钟，避免进程死亡导致无法执行
                System.out.println("考勤定时任务开始");
                workAttendanceService.runAttendanceTask(); //账号：13800004400, oid 1075 手工录入考勤，下班无法录入旷工？
                System.out.println("考勤扫描任务结束");
                //wyu:释放分布式锁
                workAttendanceService.releaseDlmLock(lockKey);
            }
        }
    }
/*
http://localhost:8080/workAttendance/testRun.do
http://localhost:8080/workAttendance/testRun.do?now=2025-06-23%2012:12:00
http://localhost:8080/workAttendance/testRun.do?now=2025-06-23%2013:12:00&oid=787
http://localhost:8080/workAttendance/testEvent.do?now=2025-06-23%2016:00:00&uid=558
        setAttendanceUserEvent(user, now, AttendanceType.workdayLieu);
        if(pau.getUser()==558) {
            System.out.println("checkpoint");
        }
SELECT * FROM t_sys_user WHERE oid=787 AND mobile IN ('15202255153', '18622372948','18630854021');
SELECT * FROM t_sys_user WHERE userID=558;
SELECT * FROM t_sys_user WHERE oid=787 AND role_code='general';

SET @STARTDATE=DATE_SUB(CURDATE(), INTERVAL ((WEEKDAY(CURDATE()))%7+7) DAY);
SET @ENDDATE=ADDDATE(CURDATE(),1);
SELECT * FROM t_personnel_attendance_user t WHERE t.org in(787) ORDER BY t.attendance_date DESC, t.org, t.id DESC;
SELECT * FROM t_personnel_attendance_user_detail t WHERE t.org in(787) ORDER BY t.attendance_date DESC, t.org, t.`user`, t.begin_time, t.id DESC;
SELECT t.id,t.`user`,t.org,t.punch_time,t.punch_type,t.workday_type,t.`type`,t.create_time FROM t_personnel_attendance_punch t WHERE t.org=787 AND t.punch_time BETWEEN @STARTDATE AND @ENDDATE ORDER BY t.`user`, t.punch_time, t.id;
SELECT * FROM t_personnel_attendance_punch t WHERE t.org=787 AND t.punch_time BETWEEN @STARTDATE AND @ENDDATE ORDER BY t.`user`, t.punch_time, t.id;


ALTER TABLE `t_personnel_attendance_user` MODIFY COLUMN `late_limit` INTEGER DEFAULT NULL COMMENT '迟到时限(毫秒)' AFTER `omit_after`,
 MODIFY COLUMN `early_limit` INTEGER DEFAULT NULL COMMENT '早退时限(毫秒)' AFTER `late_limit`;
DROP TABLE IF EXISTS `temp_personnel_attendance_config`;
CREATE TABLE IF NOT EXISTS `temp_personnel_attendance_config`(
 `org` INTEGER DEFAULT NULL COMMENT '机构id',
 `open_date` DATETIME(3) DEFAULT NULL COMMENT '启用时间',
 `middle_break` BOOLEAN DEFAULT NULL COMMENT '是否中间休息(是否中午考勤)',
 `attendance_pattern` TINYINT DEFAULT 2 COMMENT '考勤模式:1-考勤宝,2-手工录入'
)ENGINE=MEMORY;
INSERT INTO `temp_personnel_attendance_config`(`org`,`open_date`,`middle_break`,`attendance_pattern`) SELECT t1.`org`,t1.`open_date`,t1.`middle_break`, t1.`attendance_pattern` FROM `t_personnel_attendance_config` t1 JOIN (SELECT `org`,MAX(`open_date`) `max_open_date` FROM `t_personnel_attendance_config` GROUP BY `org`) t2 ON t1.`org`=t2.`org` WHERE t1.`id` = (SELECT MAX(`id`) FROM `t_personnel_attendance_config` WHERE `open_date`=t2.`max_open_date` AND `enabled`=TRUE AND `org`=t1.`org`);
UPDATE `t_personnel_attendance_user` u JOIN `temp_personnel_attendance_config` t ON u.`org`=t.`org` SET u.`late_limit`=t.`late_limit`, u.`early_limit`=t.`early_limit` WHERE u.`attendance_date` >= t.`open_date`;
DROP TABLE IF EXISTS `temp_personnel_attendance_config`;

BEGIN;
SET FOREIGN_KEY_CHECKS=0;
UPDATE `t_personnel_attendance_org_monitor` SET scan_times=3 WHERE org!=1075;
TRUNCATE TABLE `t_personnel_attendance_user_detail`;
TRUNCATE TABLE `t_personnel_attendance_user`;
SET FOREIGN_KEY_CHECKS=1;
COMMIT;
*/
//    public void attendanceSettleDay() {
//        //wyu:获取分布式锁
//        String lockKey, methodName = Thread.currentThread().getStackTrace()[1].getMethodName();
//        if ((lockKey=dlmService.getLock(methodName)) != null) {
//            System.out.println("考勤日任务开始");
//            List<Organization> organizations = orgPopedomService.getOrgPopedomByMid("kj");//有考勤的机构,接口没问题后放开
//            for (Organization organization : organizations) {   //接口没问题后放开
//                Integer oid = organization.getId();
//                System.out.println("考勤日任务 start oid=" + oid);
//                workAttendanceOldService.setDefaultWorkAttendance(oid);
//                System.out.println("考勤日任务 finish oid：" + oid);
//            }
//            System.out.println("考勤日任务结束");
//            //wyu:释放分布式锁
//            dlmService.releaseLock(methodName, lockKey);
//        }
//    }
}
