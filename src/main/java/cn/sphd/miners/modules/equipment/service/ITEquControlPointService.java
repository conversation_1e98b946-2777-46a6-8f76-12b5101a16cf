package cn.sphd.miners.modules.equipment.service;

import cn.sphd.miners.modules.equipment.entity.TEquControlPoint;

import java.util.List;
import java.util.Map;


/**
 * 装备管理_控制点
 * 20240407 1.292机器设备之初始设置 新增Service接口
 *
 * <AUTHOR>
 * @date 2024-04-13
 */
public interface ITEquControlPointService {
    /**
     * 查询装备管理_控制点
     * 20240407 1.292机器设备之初始设置 新增
     *
     * @param id 装备管理_控制点
     *           20240407 1.292机器设备之初始设置 新增主键
     * @return 装备管理_控制点
     * 20240407 1.292机器设备之初始设置 新增
     */
    public TEquControlPoint selectTEquControlPointById(Long id);

    /**
     * 查询装备管理_控制点
     * 20240407 1.292机器设备之初始设置 新增列表
     *
     * @param tEquControlPoint 装备管理_控制点
     *                         20240407 1.292机器设备之初始设置 新增
     * @return 装备管理_控制点
     * 20240407 1.292机器设备之初始设置 新增集合
     */
    public List<TEquControlPoint> selectTEquControlPointList(TEquControlPoint tEquControlPoint);

    /**
     * 新增装备管理_控制点
     * 20240407 1.292机器设备之初始设置 新增
     *
     * @param tEquControlPoint 装备管理_控制点
     *                         20240407 1.292机器设备之初始设置 新增
     * @return 结果
     */
    public int insertTEquControlPoint(TEquControlPoint tEquControlPoint);

    /**
     * 修改装备管理_控制点
     * 20240407 1.292机器设备之初始设置 新增
     *
     * @param tEquControlPoint 装备管理_控制点
     *                         20240407 1.292机器设备之初始设置 新增
     * @return 结果
     */
    public int updateTEquControlPoint(TEquControlPoint tEquControlPoint);

    /**
     * 批量删除装备管理_控制点
     * 20240407 1.292机器设备之初始设置 新增
     *
     * @param ids 需要删除的装备管理_控制点
     *            20240407 1.292机器设备之初始设置 新增主键集合
     * @return 结果
     */
    public int deleteTEquControlPointByIds(String ids);

    /**
     * 删除装备管理_控制点
     * 20240407 1.292机器设备之初始设置 新增信息
     *
     * @param id 装备管理_控制点
     *           20240407 1.292机器设备之初始设置 新增主键
     * @return 结果
     */
    public int deleteTEquControlPointById(Long id);

    List<Map<String, Object>> selectTEquControlPointListWithPics(TEquControlPoint tEquControlPoint);

    int enable(String ids);
}
