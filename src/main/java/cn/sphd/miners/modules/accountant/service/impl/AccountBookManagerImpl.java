package cn.sphd.miners.modules.accountant.service.impl;

import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.accountant.entity.*;
import cn.sphd.miners.modules.accountant.mapper.BuildAccountMapper;
import cn.sphd.miners.modules.accountant.mapper.SubjectPeroidMapper;
import cn.sphd.miners.modules.accountant.mapper.TAccountSubjectMapper;
import cn.sphd.miners.modules.accountant.mapper.TAccountantSubjectHistoryMapper;
import cn.sphd.miners.modules.accountant.service.AccountBookManagerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * Created by root on 17-5-8.
 */
@Service
@Transactional(readOnly=false)
public class AccountBookManagerImpl implements AccountBookManagerService {

    @Autowired
    TAccountSubjectMapper tAccountSubjectMapper;
    @Autowired
    SubjectPeroidMapper subjectPeroidMapper;
    @Autowired
    TAccountantSubjectHistoryMapper subjectHistoryMapper;
    @Autowired
    BuildAccountMapper buildAccountMapper;
    @Autowired
    VoucherUpdate voucherUpdate;

    @Override
    public List<TAccountantSubjectHistory> getAllSubjects(int oid) {
        TAccountantSubjectHistory param = new TAccountantSubjectHistory();
        param.setOrg(oid);
        List<TAccountantSubjectHistory> list = subjectHistoryMapper.listAllSubject(param);
        return list;
    }

    /*得到某科目的总帐数据,也能返回明细数据，用参数flag做区分
    * flag 0 总帐
    * flag 1 明细帐
    * 帐簿管理的数据有可能来自小结帐也来自总结帐，小结帐的话从暂存表中取数据，总结帐的话从原表中取
    * */
    @Override
    public List<TAccountantSubjectPeroid> getSubjectLedger(TAccountantSubjectHistory subjectEntity, int flag, String queryTime) throws ParseException {

        boolean buildFlag = false;//false表示该科目没有建账数据，true表示有建账数据
        //存放所有科目，如果界面上点击的是一级科目，改集合需要存放他下面的二级和三级科目
        List<TAccountantSubjectHistory> subjectList = new ArrayList<TAccountantSubjectHistory>();
        //存放该科目所涉及到的所有记录，按月统计的数据和年报数据，如果页面上选择的是一级科目的话，统计的时候要包括他下面的二级和三级科目
        List<TAccountantSubjectPeroid> peroidList = new ArrayList<TAccountantSubjectPeroid>();
        //存放所有涉及到的科目id
        List<Integer> subjectids = new ArrayList<Integer>();
//        VoucherUpdate vu = new VoucherUpdate();
        subjectEntity = subjectHistoryMapper.getOneSubjecthistory(subjectEntity);
        //把和该科目相关的其他所有科目都找出来，目的是找出科目周期表的数据显示在页面上
        subjectList.add(subjectEntity);
        subjectids.add(subjectEntity.getSubjectId());
        List<TAccountantSubjectHistory> listChild = subjectHistoryMapper.childSubject(subjectEntity);
        int size = 0;
        if(listChild != null)
        {
            size = listChild.size();
            for (TAccountantSubjectHistory sub : listChild)
            {
                subjectList.add(sub);
                subjectids.add(sub.getSubjectId());
                List<TAccountantSubjectHistory> listChildThird = subjectHistoryMapper.childSubject(sub);
                if(listChildThird != null)
                {
                    for (TAccountantSubjectHistory childSub : listChildThird)
                    {
                        subjectList.add(childSub);
                        subjectids.add(childSub.getSubjectId());
                    }
                }
            }
        }

        //该科目的方向
        String direction = subjectEntity.getBalanceDirection();

        Calendar c = Calendar.getInstance();
        int year = c.get(Calendar.YEAR);
        //该科目的年报数据，如果该科目是一级科目，年报需要统计他下面的二级科目和三级科目
        TAccountantSubjectPeroid annualPeroid = new TAccountantSubjectPeroid();
        if(queryTime == null)
        {
            annualPeroid.setPeriod(year + "");
        }
        else
        {
            annualPeroid.setPeriod(queryTime.substring(0,4));
        }
        annualPeroid.setSubjectId(subjectEntity.getSubjectId());
        annualPeroid.setSubject(subjectEntity.getSubject());
        annualPeroid.setBalanceDirection("3");
        annualPeroid.setBalance(BigDecimal.valueOf(0.00));
        annualPeroid.setDebitQuantity(0.00);
        annualPeroid.setCreditQuantity(0.00);
        annualPeroid.setQuantity(subjectEntity.getQuantity());

        TAccountantSubjectPeroid annualBeginPeroid = new TAccountantSubjectPeroid();
        annualBeginPeroid.setSubjectId(subjectEntity.getSubjectId());
        if(queryTime == null)
        {
            annualBeginPeroid.setPeriod(year + "-01-01");
        }
        else
        {
            annualBeginPeroid.setPeriod(queryTime);
        }

        SimpleDateFormat format =  new SimpleDateFormat("yyyy-MM-dd");
//        Calendar c = Calendar.getInstance();
        String now = format.format(new Date());

        annualBeginPeroid = subjectPeroidMapper.getBeginningData(annualBeginPeroid);
        if(annualBeginPeroid != null)
        {
            annualBeginPeroid.setSummary("期初余额");
            BigDecimal beginBalance = annualBeginPeroid.getBeginningBalance() == null ? BigDecimal.valueOf(0) : annualBeginPeroid.getBeginningBalance();
            String beginDir = annualBeginPeroid.getBeginningDirection();
            annualBeginPeroid.setCredit(BigDecimal.valueOf(0.00));
            annualBeginPeroid.setDebit(BigDecimal.valueOf(0.00));
            if(beginDir == null || "0".equals(beginDir))
            {
                annualBeginPeroid.setBeginningDirection("3");
                annualBeginPeroid.setQuantity(0.00);
            }
            String beginDirection = annualBeginPeroid.getBalanceDirection();
            if (beginDirection == null) {
                annualBeginPeroid.setBalanceDirection("3");
            }
            String period = annualBeginPeroid.getPeriod();

            //得到该机构的初始建账月份
            TAccountantSetting link = new TAccountantSetting();
            link.setOrg(subjectEntity.getOrg());
            link.setKey_("inital_month");
            link = buildAccountMapper.getSettingByKey(link);
            String buildMonth = link.getValue_();
            buildMonth = getPreMonthDate(buildMonth);
            if (buildMonth.equals(period)) {buildFlag = true;}
            //如果查询到的期初余额的结账月份不是1月，也就是说还没用满一年的话，期初余额的值就是期末余额，如果该系统已经用了多年的话期初余额就是去年的期末余额也就是今年的年初余额
            if (!period.endsWith("-01")) {
//                BigDecimal balance = annualBeginPeroid.getBalance() == null ? BigDecimal.valueOf(0) : annualBeginPeroid.getBalance();
                if (!buildFlag) {
                    //说明该科目没有建账数据,期末余额需要清零
                    annualBeginPeroid.setBalance(BigDecimal.ZERO);
                    annualBeginPeroid.setBalanceDirection("3");
                }

            }
            else
            {
                if (!buildMonth.endsWith("-01")) {
                    annualBeginPeroid.setBalance(beginBalance);
                    annualBeginPeroid.setBalanceDirection(beginDir);
                }
            }
            //得到期初的数量

            Date date = format.parse(annualBeginPeroid.getPeriod() + "-01");
            c.setTime(date);
            c.add(Calendar.MONTH, -1);

            String time = format.format(c.getTime());
            TAccountantSubjectPeroid annualBeginPeroidParam = new TAccountantSubjectPeroid();
            annualBeginPeroidParam.setPeriod(time);
            annualBeginPeroidParam.setSubjectId(subjectEntity.getSubjectId());
            annualBeginPeroidParam = subjectPeroidMapper.getBeginningQuantity(annualBeginPeroidParam);
            if (annualBeginPeroidParam != null)
            {
                annualBeginPeroid.setQuantity(annualBeginPeroidParam.getQuantity());
                annualBeginPeroid.setCreditQuantity(annualBeginPeroidParam.getCreditQuantity());
                annualBeginPeroid.setDebitQuantity(annualBeginPeroidParam.getDebitQuantity());
            }
        }
        else
        {
            annualBeginPeroid = new TAccountantSubjectPeroid();
            annualBeginPeroid.setSummary("期初余额");
            annualBeginPeroid.setBalanceDirection("3");
            annualBeginPeroid.setBalance(BigDecimal.valueOf(0.00));
            if(queryTime == null)
            {
                annualBeginPeroid.setPeriod(year + "");
            }
            else
            {
                annualBeginPeroid.setPeriod(queryTime.substring(0,4));
            }
        }
        String beginDirection = annualBeginPeroid.getBalanceDirection();
        BigDecimal beginBalance = annualBeginPeroid.getBalance() == null ? BigDecimal.valueOf(0.00) : annualBeginPeroid.getBalance();



        //得到周期表的该科目下的列表，不包含年报
        QueryData qd = new QueryData();
        qd.put("period",queryTime);
        qd.put("peroidlist",subjectids);
        qd.put("curdate",now);
        List<TAccountantSubjectPeroid> listPeroid = null;
        if(flag == 0)
        {
            //总帐的话得到的只是按月统计的数据
            TAccountantSubjectPeroid param = new TAccountantSubjectPeroid();
            param.setPeriod(queryTime);
            param.setSubjectId(subjectEntity.getSubjectId());
            param.setCreateDate(now);
            listPeroid = subjectPeroidMapper.getPeroidList(param);
            //如果总账中只有一个元素的话则要看明细账有没有数据，如果明细账没有数据的话则需要把总账中的那一条数据清掉，
            //总账只有一条数据且明细账没有数据的则没有必要显示总账的那条数据
//            if (listPeroid != null && listPeroid.size() == 1) {
//                List<TAccountantSubjectPeroid> detailPeroidList = subjectPeroidMapper.getDetailPeroidList(qd);
//                if (detailPeroidList.size() == 0) {
//                    listPeroid.clear();
//                }
//            }
            //如果该科目有建账数据的话需要把总账中建账产生的那条数据删除掉
            if (buildFlag && listPeroid.size() > 0) {
                listPeroid.remove(0);
            }
        }
        else
        {
            //明细张的话得到的既有按月统计的数据也有按天统计的数据
            listPeroid = subjectPeroidMapper.getDetailPeroidList(qd);
        }
        if(listPeroid != null && listPeroid.size() > 0)
        {
            String tempPeroid = listPeroid.get(0).getPeriod();
            TAccountantSubjectPeroid monthParam = new TAccountantSubjectPeroid();
            //年累计数据是每次算出来的不是存在数据库中的
            for(int i=0;i<listPeroid.size();i++)
            {
                TAccountantSubjectPeroid tsp = listPeroid.get(i);
                String peroid = tsp.getPeriod();

                BigDecimal annualCredit = annualPeroid.getCredit() == null ? BigDecimal.valueOf(0.00) : annualPeroid.getCredit();
                BigDecimal annualDebit = annualPeroid.getDebit() == null ? BigDecimal.valueOf(0.00) : annualPeroid.getDebit();
//                BigDecimal annualBalance = annualPeroid.getBalance() == null ? BigDecimal.valueOf(0) : annualPeroid.getBalance();
//                String balanceDir = annualPeroid.getBalanceDirection();

                BigDecimal thisCredit = tsp.getCredit() == null ? BigDecimal.valueOf(0.00) : tsp.getCredit();
                BigDecimal thisDebit = tsp.getDebit() == null ? BigDecimal.valueOf(0.00) : tsp.getDebit();
//                BigDecimal thisBalance = tsp.getBalance() == null ? BigDecimal.valueOf(0) : tsp.getBalance();
//                String thisDir = tsp.getBalanceDirection();

                annualPeroid.setCredit(annualCredit.add(thisCredit));
                annualPeroid.setDebit(annualDebit.add(thisDebit));
//                DirAndBalance dab = vu.getBalanceAndDirection(balanceDir,annualBalance,thisDir,thisBalance);
//                annualPeroid.setBalance(dab.getBalance());
//                annualPeroid.setBalanceDirection(dab.getDir());

                if(flag == 1)
                {
                    //统计借数量
                    double thisCrebitQuantity = tsp.getCreditQuantity();
                    double thisDebitQuantity = tsp.getDebitQuantity();
                    //统计贷数量
                    double annualCreditQuantity = annualPeroid.getCreditQuantity();
                    double annualDebitQuantity = annualPeroid.getDebitQuantity();

                    annualPeroid.setCreditQuantity(thisCrebitQuantity + annualCreditQuantity);
                    annualPeroid.setDebitQuantity(thisDebitQuantity + annualDebitQuantity);
                    //统计余额中的数量
                    double thisQuantity = tsp.getQuantity();
//                    double annualQuantity = annualPeroid.getQuantity();
//                    annualPeroid.setQuantity(annualQuantity + thisQuantity);
//                    //统计单价
//                    double thisUnitPrice = tsp.getUnitPrice();
//                    double annualUnitPrice = annualPeroid.getUnitPrice();
//                    annualPeroid.setUnitPrice(annualUnitPrice + thisUnitPrice);
//                    if(size > 1)
//                    {
                    BigDecimal thisBalance = BigDecimal.valueOf(0.00);
                    String thisBalanceDir = "3";
                    if("1".equals(direction))
                    {
                        if(thisCredit.compareTo(thisDebit) > 0)
                        {
                            thisBalance = thisCredit.subtract(thisDebit);
                            thisBalanceDir = "1";
                        }
                        if(thisCredit.compareTo(thisDebit) < 0)
                        {
                            thisBalance = thisDebit.subtract(thisCredit);
                            thisBalanceDir = "2";
                        }
                    }
                    if("2".equals(direction))
                    {
                        if(thisDebit.compareTo(thisCredit) > 0)
                        {
                            thisBalance = thisDebit.subtract(thisCredit);
                            thisBalanceDir = "2";
                        }
                        if(thisDebit.compareTo(thisCredit) < 0)
                        {
                            thisBalance = thisCredit.subtract(thisDebit);
                            thisBalanceDir = "1";
                        }
                    }
                    DirAndBalance dab = voucherUpdate.getBalanceAndDirection(beginDirection,beginBalance,thisBalanceDir,thisBalance);
                    tsp.setBalance(dab.getBalance());
                    tsp.setBalanceDirection(dab.getDir() == null ? "3" : dab.getDir());
                    //设置单价
                    if (thisQuantity > 0)
                    {
//                        tsp.setUnitPrice(dab.getBalance().divide(thisQuantity,2,BigDecimal.ROUND_UP));
                        tsp.setUnitPrice(dab.getBalance().divide(BigDecimal.valueOf(thisQuantity),4,BigDecimal.ROUND_UP).doubleValue());
                    }
                    else
                    {
                        tsp.setUnitPrice(0);
                    }
                    beginDirection = dab.getDir();
                    beginBalance = dab.getBalance();
//                    }

                    //1.如果集合里只有一个明细帐
                    if (listPeroid.size() == 1)
                    {
                        peroidList.add(tsp);
                        //查询一级科目的月统计
                        monthParam.setSubjectId(subjectEntity.getSubjectId());
                        monthParam.setPeriod(peroid.substring(0,peroid.length() - 3));
                        TAccountantSubjectPeroid monthPeroid = subjectPeroidMapper.judgeAnnualReport(monthParam);
                        setMonthUnitPrice(monthPeroid);

                        peroidList.add(monthPeroid);
                        break;
                    }

                    //2.如果当前期数和上一个不同
                    if (!peroid.equals(tempPeroid))
                    {
                        monthParam.setSubjectId(subjectEntity.getSubjectId());
                        monthParam.setPeriod(tempPeroid.substring(0,tempPeroid.length() - 3));
                        TAccountantSubjectPeroid monthPeroid = subjectPeroidMapper.judgeAnnualReport(monthParam);
                        setMonthUnitPrice(monthPeroid);
                        peroidList.add(monthPeroid);
                        peroidList.add(tsp);
                        tempPeroid = peroid;

                    }
                    else
                    {
                        peroidList.add(tsp);
                    }

                    //3.如果当前是最后一个且集合数大于1
                    if (i == (listPeroid.size() - 1))
                    {
                        monthParam.setSubjectId(subjectEntity.getSubjectId());
                        monthParam.setPeriod(peroid.substring(0,peroid.length() - 3));
                        TAccountantSubjectPeroid monthPeroid = subjectPeroidMapper.judgeAnnualReport(monthParam);
                        setMonthUnitPrice(monthPeroid);
                        peroidList.add(monthPeroid);
                    }

                }
                else
                {
                    String bd = tsp.getBalanceDirection();
                    if (bd == null) {
                        tsp.setBalanceDirection("3");
                    }
                    peroidList.add(tsp);
                }

            }
            TAccountantSubjectPeroid lastPeroid = peroidList.get(peroidList.size() - 1);
            if(lastPeroid != null)
            {
                annualPeroid.setBalance(lastPeroid.getBalance());
                annualPeroid.setBalanceDirection(lastPeroid.getBalanceDirection());
            }

        }
        else {
            //如果没有总账或明细账数据的话，本年累计就是建账时的数据，就是科目表中用户录入的该科目的本年借方累计、本年贷方累计、余额及余额方向
            BigDecimal creditAccumulative = subjectEntity.getCreditAccumulative() == null ? BigDecimal.ZERO : subjectEntity.getCreditAccumulative();
            BigDecimal debitAccumulative = subjectEntity.getDebitAccumulative() == null ? BigDecimal.ZERO : subjectEntity.getDebitAccumulative();
            BigDecimal balance = subjectEntity.getBalance();
            String endDirection = subjectEntity.getEndDirection() == null ? "3" : subjectEntity.getEndDirection();
            annualPeroid.setBalance(balance);
            annualPeroid.setBalanceDirection(endDirection);
            annualPeroid.setCredit(creditAccumulative);
            annualPeroid.setDebit(debitAccumulative);
            annualBeginPeroid.setBalance(balance);
            annualBeginPeroid.setBalanceDirection(endDirection);
            annualBeginPeroid.setBeginningBalance(balance);
            annualBeginPeroid.setBeginningDirection(endDirection);
            annualBeginPeroid.setCreditAccumulative(creditAccumulative);
            annualBeginPeroid.setDebitAccumulative(debitAccumulative);
        }

        if (annualPeroid.getCredit().compareTo(BigDecimal.ZERO) == 0 && annualPeroid.getDebit().compareTo(BigDecimal.ZERO) == 0) {
            annualPeroid.setCredit(subjectEntity.getCreditAccumulative());
            annualPeroid.setDebit(subjectEntity.getDebitAccumulative());
        }
        annualPeroid.setSummary("本年累计");
        setMonthUnitPrice(annualPeroid);
        //添加期末余额
        peroidList.add(annualPeroid);

        setMonthUnitPrice(annualBeginPeroid);
        //添加期初余额
        peroidList.add(0,annualBeginPeroid);

        return peroidList;
    }

    private String getPreMonthDate(String buildMonth) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        String initMonth = "2018-08-17 15:09";
        Date date = sdf.parse(initMonth);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.DATE, -1);
        return sdf.format(calendar.getTime());
    }

    private void setMonthUnitPrice(TAccountantSubjectPeroid monthPeroid)
    {
        if (monthPeroid != null)
        {
            double monthQuantity = monthPeroid.getQuantity();
            if (monthQuantity > 0)
            {
                BigDecimal monthBalance = monthPeroid.getBalance() == null ? BigDecimal.valueOf(0) : monthPeroid.getBalance();
//                monthPeroid.setUnitPrice(monthBalance.divide(monthQuantity,2,BigDecimal.ROUND_UP));
                monthPeroid.setUnitPrice(monthBalance.divide(BigDecimal.valueOf(monthQuantity),4,BigDecimal.ROUND_UP).doubleValue());
            }
            else
            {
                monthPeroid.setUnitPrice(0);
            }
        }
    }

    private TAccountantSubjectPeroid newMonthPeroid(String summary, String dir, String peroid, BigDecimal annualBalance, BigDecimal annualCredit, BigDecimal annualDebit) {
        TAccountantSubjectPeroid monthPeroid = new TAccountantSubjectPeroid();
        monthPeroid.setPeriod(peroid.substring(0,peroid.length() - 3));
        monthPeroid.setBalance(annualBalance);
        monthPeroid.setCredit(annualCredit);
        monthPeroid.setDebit(annualDebit);
        monthPeroid.setBalanceDirection(dir);
        monthPeroid.setSummary(summary);
        return monthPeroid;
    }

}
