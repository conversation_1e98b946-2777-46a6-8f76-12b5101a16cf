/*creator: lyt date:2018/4/11 重新登录弹窗样式 */
.pass-main{width:260px;margin:auto;}
.pass-w{padding:12px 6px 12px 12px;width: 260px;margin: auto;}
.pass-w .pass-label{display:inline-block;width:80px;}
.pass-con{padding-left:10px;display:inline-block;width:148px;}
.errorTip{font-size: 12px;color: #ff0000;}
/* 用户操作 */
.userControl{ width:225px; background:#eaeaea;  color:#333; padding-bottom:24px; }
.userControl .fa{ margin-right:10px;   }
.userControl .user_info{ display: flex; flex-direction: row }
.userControl .user_info .logo-avatar{  position: relative; }
.userControl .user_info .u1{ padding: 8px 0 0 15px; line-height: 24px }
.userControl .user_info .u1 #loginUserNAME2{ font-size: 16px ; font-weight: bold; color: #333}
.userControl .user_info .u1 #loginUserPhone{ margin-right: 2px; font-size: 14px ;  color: #555}
.control1>li:first-child{padding:15px; }
#loginUserIMG2, .control1 .changeImgBtn{ width:64px; height:64px; }
.litLi>p{ height:36px; line-height:36px; background:#fff; padding:0 15px; margin: 1px 0 }
.litLi>p:hover{ color:#36C6D3;   }
.litLi>p>.fa.ty-right,  .ttlLi>.fa.ty-left{ font-size:20px; color:#999; margin-top:5px; }
.bigLi>div{ padding:0 15px; background:#fff;   }
.bigLi>div>a{ display:block; height:36px; color: #333; line-height:36px; border-bottom:1px solid #eaeaea;    }
.bigLi>div>a:hover{ color: #36C6D3;     }
.bigLi>div>a:last-child{ border-bottom:none;   }
.control2{ display: none; }
.control2 .fa{ width:16px;  }
.ttlLi{ padding:0 15px; text-align:center; height:35px; line-height:30px;    }
.ttlLi>.ty-left:hover{ color: #36C6D3;  }
.dialog_content{
    width: 310px;
    margin: 0 auto;
}
#pEtip{ color:red; text-align: center;   }
#dialog2 div.pE, #dialog div.pE{ margin: 16px 0}
#dialog2 .pE, #dialog .pE{ border-bottom:1px solid #aaa; width: 100%; margin:0 auto; position:relative;  }
#dialog .pE>span:first-child{ display:inline-block; width:80px; text-align: right;   }
#dialog .pE>input{ display:inline-block; border:none; height:55px; width:205px; padding-left:15px;    }
#dialog .pE .ri{ position: absolute; right:0; top:10px;   }
#dialog .pE input.ri{ width: 100px; height: 32px;   }
#dialog .pE input.ri[disabled]{ background: #ccc;   }
#dialog .pE .ri.fa{ top:13px;font-size:20px; color:#666;     }
#dialog .pE .ri.fa:hover{  color:#333;     }
#dialog .pE>input:active, #dialog .pE>input:focus{ border:none; box-shadow: none;  }
#dialog .pe2,#dialog .pe3{ display: none ; }
#dialog .pe2Tip{ text-align: center; margin-top:15px; font-size:11px;   }
.k-dialog.k-alert .k-dialog-titlebar .k-dialog-title { font-size:14px;  }
.control1 .changeImgBtn{ position: absolute;left:0; top: 0;  text-align:center; display:none; z-index:1; background:rgba(0,0,0,0.3); border-radius:50%;     }
span.changeImgBtn{ color:#fff; text-decoration:underline; padding:5px; line-height:64px; font-size: 12px; z-index: 2 }
#bdWx{
    font-size: 0.8em;
    margin-right: 5px;
}
/* 浮窗 */
.bounceFloating {
    display: none;
    position: absolute;
    top: 48px;
    z-index: 999;
    /*box-shadow: 0 0 10px #666;*/
    /*border: none;*/
    box-shadow: 0 1px 4px #bdbdbd;
    border: 1px solid #c8c8c8;
    background-color: #36C6D3;
    border-top: none;
    width: 395px;
    height: 550px;
    right: 0;
}
.bounceFloating:after{
    content: '';
    position: absolute;
    display: block;
    border: 8px solid transparent;
    border-bottom-color: transparent;
    border-bottom-color: #36c6d3;
    top: -16px;
    right: 16px;
}

div.floatingContainer {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

div.floatingContainer > div.bonceHead {
    border-bottom: none;
    background: none;
    text-align: right;
    cursor: all-scroll;
    height: 8px;
    position: absolute;
    top: 0;
    width: 100%;
    right: 0;
    padding-right: 32px;
}

.navigation_min{
    position: absolute;
    top: 0;
    display: block;
    width: 32px;
    height: 48px;
    cursor: pointer;
    line-height:48px;
    right: 32px;
    text-align: center;
}
.navigation_min .min {
    display: inline-block;
    width: 12px;
    height: 1px;
    background: #fff;
    line-height: 0;
    font-size: 0;
    vertical-align: middle;
}
.navigation_min:hover {
    background-color: #2bb8c4;
    color: #fff;
}

div.bounceFloating iframe {
    width: 100%;
    height: 100%;
    border: none;
}

/* 浮窗复杂的详情页 */
#somethingItemDetailsPage{
    width: 100%;
    position: fixed;
    top: 0;
    z-index: 9996;
    background: rgba(100,100,100,0.5);
    height: 100%;
    display: none;
}
#somethingItemDetailsPage>iframe{
    width: 1200px;
    border: 1px solid #ccc;
    margin: 0 auto;
    height: inherit;
    justify-content: center;
    overflow-x: hidden;
}
#receiveState{
    margin-left: 130px;
}
#receiveState label{
    font-weight: normal;
}
#receiveState input {
    margin-right: 60px;
}
.bounce_flex {
    display: flex;
    justify-content: space-around;
}
.common_pop{position:fixed;display:none; background: rgba(100,100,100,0.7);width: 100%; z-index:9999; min-height:100%; }
.common_bounce{position:fixed;display:none; background: rgba(100,100,100,0.7);width: 100%; z-index:10010; min-height:100%; }
.common_bounce1{position:fixed;display:none; background: rgba(100,100,100,0.7);width: 100%; z-index:10011; min-height:100%; }
.common_bounce2{position:fixed;display:none; background: rgba(100,100,100,0.7);width: 100%; z-index:10012; min-height:100%; }
.btnSet { margin-top:10px;display:flex;justify-content : space-between;}
.btnSet .k-button {margin: 16px 0;letter-spacing:6px;}
.btnSet .k-close {background: #ffff00;}
.btn-rt{ text-align: right;}
.btnSet .k-succes,.btn-rt .up-succes {background: #4f81bd;color:#fff;}
.SMSVerification{
    margin: 8px 24px;
}
.SMSVerification .item{
    margin-top: 16px;
}
.SMSVerification .input_avatar {
    display: -webkit-box;
    display: -moz-box;
    display: -webkit-flex;
    display: -moz-flex;
    display: -ms-flexbox;
    display: flex;
    justify-content: space-between;
    position: relative;
    align-items: center;
    flex: auto;
}
.SMS_phone{
    width: 100%;
    color: #666;
}
.SMSVerification .input_avatar .input_title {
    width: 70px;
    flex: none;
}
.SMSVerification .input_avatar input {
    border: none;
    background-color: inherit;
    font-size: 16px;
    color: #666;
    width: 200px;
}
.getCodeBtn{
    cursor: pointer;
    background-color: #5d9cec;
    color: #fff;
    padding: 0 12px;
    height: 30px;
    line-height: 30px;
    flex: none;
    border: none;
}