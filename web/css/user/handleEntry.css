.info1, .info2{  overflow:hidden; overflow:hidden;  margin-bottom:50px;         }
.info1{ min-width:550px; width:65%;         }
.info2{ min-width:300px; margin-left:10px; width:30%;            }

.mar{ padding-left:40px;  }
article{ color:#666;  }

.userImg{ display: inline-block; border:none; width:88px; height:88px; border-radius:44px;             }
.userName{ padding:40px 0 0;  }
.userTxt{ padding:30px 0;   }
.userTxt p{ margin: 0;   }
.uerinfo{ margin-top:40px;padding:0 10px; position: relative;  color:#454545;     }

.xing:after {  content: "*";  width: 5px;  height: 5px;  display: inline-block;  color: red;  font-weight: bolder;  position: relative;  }






