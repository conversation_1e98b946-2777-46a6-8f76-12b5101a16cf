package cn.sphd.miners.modules.resourceAuthority.service.impl;

import cn.sphd.miners.common.utils.QueryData;
import cn.sphd.miners.modules.resourceAuthority.mapper.ResVisMapper;
import cn.sphd.miners.modules.resourceAuthority.service.ResByAppService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * Created by 朱思旭 on 2021/4/14.
 */
@Service("resByAppService")
@Transactional(readOnly=false)
public class ResByAppServiceImpl implements ResByAppService {

    @Autowired
    ResVisMapper resVisMapper;

    @Override
    public List<String> getResVisitMonth(String type, Integer fileId, Integer userId, Date dateBegin, Date dateEnd) {
        QueryData query = new QueryData();
        query.put("type", type);
        if (fileId != null) {
            query.put("fileId", fileId);
        }
        if (userId != null) {
            query.put("userId", userId);
        }
        query.put("startTime", dateBegin);
        query.put("endTime", dateEnd);
        List<String> list = resVisMapper.getMonthForVisit(query);
        return list;
    }
}
