package cn.sphd.miners.modules.finance.service;

import cn.sphd.miners.common.persistence.PageInfo;
import cn.sphd.miners.modules.finance.entity.FinanceCheque;
import cn.sphd.miners.modules.finance.entity.FinanceChequeDetail;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by Administrator on 2016/12/7.
 */
public interface FinanceChequeService {
    void addCashCheque(FinanceCheque financeCheque);

    List<FinanceCheque> getCheque(Integer oid, List<Integer> orgIntegerList,String type, PageInfo pageInfo);

    void addCashChequeDetail(FinanceChequeDetail financeChequeDetail);

    List<FinanceChequeDetail> getChequeDetail(Integer chequeReg);

    FinanceChequeDetail getChequeByDetailId(Integer detailId);

    void updateCashChequeState(FinanceChequeDetail financeChequeDetail);

    List<FinanceChequeDetail> getChequeByAccountID(Integer accountId);

    FinanceCheque getFinanceChequeById(Integer id);

    FinanceChequeDetail getChequeDetailById(Integer chequeId);

    List<FinanceChequeDetail> getChequeListByFid(Integer fid,String state,String type);

    FinanceChequeDetail getChequeDetailByMyselfId(Integer myselfId);

    Map<String,Object> getFinanceChequeTime(Integer oid);

    Map<String,Object> getAccountCheques(Integer oid,String type,Integer accountId,Map<String,Object> map);

    Map<String,Object> getAccountChequeList(Integer accountId,String type);

    List<FinanceCheque> getYearCheques(Integer oid, Date beginDate,Date endDate);

}
